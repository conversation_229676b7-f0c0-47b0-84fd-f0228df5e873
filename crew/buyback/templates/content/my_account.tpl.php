<?
$boxHeading = HEADER_TITLE_MY_ACCOUNT;
$boxContent = '			<table border="0" width="100%" cellspacing="0" cellpadding="0">';
foreach ($_my_account_files as $filename => $title) {
	$boxContent .= '		<tr>
								<td width="25">&nbsp;</td>';
	if (tep_not_null($date_diff) && (!$customers_security_obj->check_secret_question_isset() || $customers_security_obj->check_secret_question_is_reset()) && $filename == FILENAME_MY_ACCOUNT_MGMT) {
		$boxContent .= ' 		<td align="left"><a href="'.tep_href_link($filename, tep_get_all_get_params(array('action')) . 'current_step=3').'" class="systemNav">'.$title.'</a></td>';
	} else {
		$boxContent .= ' 		<td align="left"><a href="'.tep_href_link($filename, tep_get_all_get_params(array('action')) . '').'" class="systemNav">'.$title.'</a></td>';
	}
	$boxContent .= '  		</tr>
	                        <tr>
	                          <td colspan="2" background="'.DIR_WS_IMAGES.'space.gif" height="1">'.tep_image(DIR_WS_IMAGES.'space.gif', '', '1', '8').'</td>
	                        </tr>';
	}
		    $boxContent .= '	</table>';
require(DIR_WS_TEMPLATES . TEMPLATENAME_INFOMIDDLE);

if ($vip){
	$boxHeading = HEADER_TITLE_MY_VIP_ACCOUNT;
	$boxContent = '	<table border="0" width="100%" cellspacing="0" cellpadding="0">';
		
	foreach ($_my_vip_account_files as $vip_filename => $vip_title) {
		$boxContent .= '<tr>
							<td width="25">&nbsp;</td>
                            <td align="left"><a href="'.tep_href_link($vip_filename, tep_get_all_get_params(array('action')) . '').'" class="menuBoxVIPLink">'.$vip_title.'</a></td>
        		  		</tr>
                        <tr>
                          <td colspan="2" background="'.DIR_WS_IMAGES.'space.gif" height="1">'.tep_image(DIR_WS_IMAGES.'space.gif', '', '1', '8').'</td>
                        </tr>';
	}

	    $boxContent .= '	</table>';
	
	require(DIR_WS_TEMPLATES . TEMPLATENAME_INFOMIDDLE);
}

if ($vip){
	$boxHeading = HEADER_TITLE_VIP_INFO;
	$boxContent = '	<table border="0" width="70%" cellspacing="2" cellpadding="3">
						<tr>
							<td width="40%"><p>'.TEXT_SUPPLIER_GROUP_STATUS.'</p></td>
							<td width="60%"><p>'.$group_rank_array[$select_customer_group_row['vip_supplier_groups_id']]['name'].'</p></td>
						</tr>
						<tr>
							<td width="40%"><p>'.TEXT_SUPPLIER_RANK.'</p></td>
							<td width="60%"><p>'.$group_rank_array[$select_customer_group_row['vip_supplier_groups_id']]['rank'][$select_customer_group_row['vip_rank_id']]['name'].'</p></td>
						</tr>
						<tr>
							<td width="40%"><p>'.TEXT_SUPPLIER_PERCENTAGE.'</p></td>
							<td width="60%"><p>'.$group_rank_array[$select_customer_group_row['vip_supplier_groups_id']]['rank'][$select_customer_group_row['vip_rank_id']]['percentage'].'%</p></td>
						</tr>
						<tr>
							<td width="40%"><p>'.TEXT_SUPPLIER_CUMMULATIVE_POINT.'</p></td>
							<td width="60%"><p>'.(int)$select_customer_group_row['vip_buyback_cummulative_point'].'</p></td>
						</tr>
						<tr>
							<td width="40%"><p>'.TEXT_SUPPLIER_POINT_TO_NEXT_LEVEL.'</p></td>
							<td width="60%"><p>'.(int)($group_rank_array[$select_customer_group_row['vip_supplier_groups_id']]['rank'][$select_customer_group_row['vip_rank_id']]['upgrade_point'] - $select_customer_group_row['vip_buyback_cummulative_point']).'</p></td>
						</tr>
					';
	
	$boxContent .= '</table>';

	require(DIR_WS_TEMPLATES . TEMPLATENAME_INFOMIDDLE);
}
?>
