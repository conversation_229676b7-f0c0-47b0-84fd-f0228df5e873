<? if (file_exists(DIR_WS_JAVASCRIPT . '/payment_xmlhttp.js.php')) { include_once (DIR_WS_JAVASCRIPT . '/payment_xmlhttp.js.php'); } ?>
<div id="dhtmlTooltip"></div>
<script type="text/javascript" language="javascript" src="includes/javascript/dhtml_tooltip.js"></script>
<script type="text/javascript" src="includes/javascript/supplier_xmlhttp.js"></script>
<script type="text/javascript" src="includes/javascript/hints.js"></script>

<link rel="stylesheet" type="text/css" href="<?=DIR_WS_JAVASCRIPT . 'subModal/subModal.css'?>" />
<script type="text/javascript" src="<?=DIR_WS_JAVASCRIPT . 'subModal/common.js'?>"></script>
<script type="text/javascript" src="<?=DIR_WS_JAVASCRIPT . 'subModal/subModal.js'?>"></script>
<? 
if (tep_not_null($day_diff)) {
?>
	<script type="text/javascript">
		//define the notice box.	
		var noticebox_msg = '<?=sprintf(POPUP_TEXT_CHANGE_TO_SECRET_QUESTION_AND_ANSWER_NOTICE, tep_href_link(FILENAME_MY_ACCOUNT_MGMT, tep_get_all_get_params($get_params_always_exclude_array).'current_step=3'), 'onclick="window.top.hidePopWin()"', $day_diff)?>';
		var noticebox_title = '<?=POPUP_TEXT_CHANGE_TO_SECRET_QUESTION_AND_ANSWER_TITLE?>';
		addEvent(window, "load", initPopUp);
	</script>
<?
}
?>
<table width="100%" border="0" cellspacing="0" cellpadding="0">
	<tr>
      <td align="left"><?=tep_image(DIR_WS_IMAGES . 'space.gif', '', '1', '3')?></td>
    </tr>
    <tr>
      	<td align="left">
			<table border="0" width="100%" cellspacing="1" cellpadding="0">
		        <tr>
		          	<td>
		          		<table border="0" width="100%" height="26" cellpadding="0" cellspacing="0" background="<?=DIR_WS_IMAGES?>tab_01.gif">
		            		<tr>
<?
foreach($tabs_array as $key=>$tabname) {
	if ($key == $current_step) {
?>
					  			<!-- Start Active tab-->
		              			<td width="3"><?=tep_image(DIR_WS_IMAGES . 'tab_active01.gif', '', '3', '26')?></td>
		              			<td background="<?=DIR_WS_IMAGES?>tab_active02.gif"><div align="center">
			                		<table cellspacing="2" cellpadding="0">
			                  			<tr>
			                    			<td><p><?=tep_image(DIR_WS_ICONS . 'icon_03.gif', '', '10', '10', 'hspace="4"') . '&nbsp;<span class="title-text">' . $tabname . '</span>'?></p></td>
			                  			</tr>
			                		</table>
			              			</div>
			              		</td>
			              		<td width="3"><?=tep_image(DIR_WS_IMAGES . 'tab_active03.gif', '', '3', '26')?></td>
			              		<!-- End Active tab-->
<?	} else { ?>
								<!-- Start Inactive tab-->
		              			<td width="3"><?=tep_image(DIR_WS_IMAGES . 'tab_inactive01.gif', '', '3', '26')?></td>
		              			<td background="<?=DIR_WS_IMAGES?>tab_inactive02.gif"><div align="center">
		                			<table cellspacing="2" cellpadding="0">
		                  				<tr>
<?		$tab_content = tep_image(DIR_WS_ICONS . 'icon_03.gif', '', '10', '10', 'hspace="4"') . '&nbsp;<span class="title-text">' . $tabname . '</span>';
		$date_diff = $customers_security_obj->get_convert_pin_expired_date();
		if ((tep_not_null($date_diff) && $date_diff > 0 && !$customers_security_obj->check_secret_question_isset()) && $tabname != TEXT_SETTING_SECRET_QUESTION) {
			$tab_link_start = '<a class="SystemNav" onclick="showPopWin(noticebox_msg, noticebox_title, 500, 300, null);" href="javascript:;">';
		} else {
			$tab_link_start = '<a class="SystemNav" href="' . tep_href_link(FILENAME_MY_ACCOUNT_MGMT, tep_get_all_get_params($get_params_always_exclude_array).'current_step='.$key) . '">';
		}
		$tab_link_end = '</a>';
		$tab_content = $tab_link_start . $tab_content . $tab_link_end;
?>
		                    				<td><p><?=$tab_content?></td>
		                  				</tr>
		                			</table>
		              				</div>
		              			</td>
		              			<td width="3"><?=tep_image(DIR_WS_IMAGES . 'tab_inactive03.gif', '', '3', '26')?></td>
					  <!-- End Inactive tab-->
<?	}
}
?>
		              			<td align="right"><?=tep_image(DIR_WS_IMAGES . 'tab_02.gif', '', '3', '26')?></td>
		            		</tr>
		          		</table>
		          	</td>
				</tr>
		        <tr>
		          	<td>
		          		<table width="100%" cellspacing="0" cellpadding="0">
		            		<tr>
		              			<td width="3" background="<?=DIR_WS_IMAGES?>box_infomiddle08.gif"><?=tep_image(DIR_WS_IMAGES . 'space.gif', '', '1', '3')?></td>
		              			<td>
								<!-- Start Tab content-->
			            			<table border="0" cellpadding="0" cellspacing="2" width="100%">
			                			<tr>
			                    			<td colspan="2" background="<?=DIR_WS_IMAGES . 'space.gif'?>" height="8"></td>
			                			</tr>
						    			<tr>
						        			<td colspan="2" valign="top" align="right"><p><?=TEXT_IS_MANDATORY?></p></td>
						    			</tr>
			                			<tr>
			                    			<td colspan="2" background="<?=DIR_WS_IMAGES . 'space.gif'?>" height="8"></td>
			                			</tr>
									</table>
<?
if ($current_step == 0) {
?>
									<!--FIRST TAB -->
									<?=tep_draw_form('process0', FILENAME_MY_ACCOUNT_MGMT, tep_get_all_get_params($get_params_always_exclude_array) . "current_step=$current_step", 'post', 'onSubmit="return check_form1(process0);" autocomplete="off"') . tep_draw_hidden_field('action', 'process')?>
			            			<table border="0" cellpadding="0" cellspacing="2" width="100%">
			                			<tr>
			                    			<td><p><?=TEXT_EMAIL_ADDRESS?></p></td>
			                    			<td>
			                    				<?=tep_draw_input_field('email_address', (isset($form_values_arr['email_address']) && $form_values_arr['email_address'] ? $form_values_arr['email_address'] : ''), 'size="31" tabindex=1', true);?>
			                    				<div class="hint"><p><?=TEXT_HELP_EMAIL?></p></div>
			                    			</td>
			                			</tr>
			                			<tr>
			                    			<td colspan="2" background="<?=DIR_WS_IMAGES . 'space.gif'?>" height="8"></td>
			                			</tr>
			                			<tr>
				                    		<td><p><?=ENTRY_SIGNUP_NAME_NAME?></p></td>
				                    		<td>
				                    				<?=tep_draw_input_field('lastname', (isset($form_values_arr['lastname']) && $form_values_arr['lastname'] ? $form_values_arr['lastname'] : ''), 'size="3" tabindex=2') . '<span class="instruction">('.TEXT_LAST_NAME.')</span>'?>
				                    				<?=tep_draw_input_field('firstname', (isset($form_values_arr['firstname']) && $form_values_arr['firstname'] ? $form_values_arr['firstname'] : ''), 'size="6" tabindex=3') . '<span class="instruction">('.TEXT_FIRST_NAME.')</span>' . '&nbsp;<span class="fieldRequired">*</span>';?>
				                    				<div class="hint"><p><?=TEXT_HELP_LASTNAME?></p></div>
				                    		</td>
				                		</tr>
			                			<tr>
			                    			<td colspan="2" background="<?=DIR_WS_IMAGES . 'space.gif'?>" height="8"></td>
			                			</tr>
			                			<tr>
			                    			<td><p><?=TEXT_DATE_OF_BIRTH?></p></td>
			                    			<td><?=tep_draw_pull_down_menu('dob_month', $month_array, (isset($form_values_arr['dob_month']) && $form_values_arr['dob_month'] ? $form_values_arr['dob_month'] : ''), 'tabindex=4').'&nbsp;'.
			                              		tep_draw_pull_down_menu('dob_day', $day_array, (isset($form_values_arr['dob_day']) && $form_values_arr['dob_day'] ? $form_values_arr['dob_day'] : ''), 'tabindex=5').'&nbsp;'.
			                              		tep_draw_pull_down_menu('dob_year', $year_array, (isset($form_values_arr['dob_year']) && $form_values_arr['dob_year'] ? $form_values_arr['dob_year'] : ''), 'tabindex=6'). '&nbsp;' . TEXT_FIELD_REQUIRED;?>
			                              		<div class="hint"><p><?=TEXT_HELP_DOB?></p></div>
			                              	</td>
			                			</tr>
			                			<tr>
			                    			<td colspan="2" background="<?=DIR_WS_IMAGES . 'space.gif'?>" height="8"></td>
			                			</tr>
			                			<tr>
			                    			<td><p><?=TEXT_GENDER?></p></td>
			                    			<td><?=tep_draw_radio_field('gender', 'm', (isset($form_values_arr['gender']) ? ($form_values_arr['gender'] == 'm' ? true : false) : true), '', 'tabindex=7') . '&nbsp;<span class="instruction">' . TEXT_MALE . '<span>'
			                    				. '&nbsp;' . tep_draw_radio_field('gender', 'f', (isset($form_values_arr['gender']) && $form_values_arr['gender'] == 'f' ? true : false), '', 'tabindex=8') . '&nbsp;<span class="instruction">' . TEXT_FEMALE . '<span>';?>
			                    				<div class="hint"><p><?=TEXT_HELP_GENDER?></p></div>
			                    			</td>
			                			</tr>
			                			<tr>
			                    			<td colspan="2" background="<?=DIR_WS_IMAGES . 'space.gif'?>" height="8"></td>
			                			</tr>
			                			<tr>
			                    			<td colspan="2" background="<?=DIR_WS_IMAGES . 'space_line2.gif'?>" height="1"></td>
			                			</tr>
			                			<tr>
			                    			<td colspan="2" background="<?=DIR_WS_IMAGES . 'space.gif'?>" height="8"></td>
			                			</tr>
			                			<tr>
			                    			<td colspan="2"><p><?=tep_image(DIR_WS_ICONS . 'icon_03.gif') . '&nbsp;' . TEXT_CONTACT_INFO?></p></td>
			                			</tr>
			                			<tr>
			                    			<td colspan="2" background="<?=DIR_WS_IMAGES . 'space.gif'?>" height="8"></td>
			                			</tr>

			                			<tr>
			                    			<td><p><?=TEXT_ADDRESS?></p></td>
			                    			<td>
			                    				<?=tep_draw_input_field('address', (isset($form_values_arr['address']) && $form_values_arr['address'] ? $form_values_arr['address'] : ''), 'size="31" tabindex=9', true);?>
			                    				<div class="hint"><p><?=TEXT_HELP_ADDRESS?></p></div>
			                    			</td>
			                			</tr>
			                			<tr>
			                    			<td colspan="2" background="<?=DIR_WS_IMAGES . 'space.gif'?>" height="8"></td>
			                			</tr>
<?
/*

			                			<!--tr>
						                    <td><p><?=TEXT_SUBURB?></p></td>
						                    <td>
						                    	<p><?=tep_draw_input_field('suburb', (isset($form_values_arr['suburb']) && $form_values_arr['suburb'] ? $form_values_arr['suburb'] : ''), 'size="31"') . '&nbsp;' . tep_image(DIR_WS_ICONS . 'icon_05.gif', '', '14', '15', 'onMouseover="ddrivetip(\''.TEXT_HELP_SUBURB.'\', \'\', 200);" onMouseout="hideddrivetip();"')?>
						                    	<noscript><?='<br>' . TEXT_HELP_SUBURB?></noscript></p>
						                    </td>
			                			</tr>
			                			<tr>
			                    			<td colspan="2" background="<?=DIR_WS_IMAGES . 'space.gif'?>" height="8"></td>
			                			</tr>
			                			<tr>
			                    			<td><p><?=TEXT_POSTCODE?></p></td>
			                    			<td>
			                    				<p><?=tep_draw_input_field('postcode', (isset($form_values_arr['postcode']) && $form_values_arr['postcode'] ? $form_values_arr['postcode'] : ''), 'size="31"') . '&nbsp;' . tep_image(DIR_WS_ICONS . 'icon_05.gif', '', '14', '15', 'onMouseover="ddrivetip(\''.TEXT_HELP_POSTCODE.'\', \'\', 200);" onMouseout="hideddrivetip();"')?>
			                    				<noscript><?='<br>' . TEXT_HELP_POSTCODE?></noscript></p>
			                    			</td>
			                			</tr>
			                			<tr>
			                    			<td colspan="2" background="<?=DIR_WS_IMAGES . 'space.gif'?>" height="8"></td>
			                			</tr-->
*/
?>
			                			<tr>
			                    			<td><p><?=TEXT_CITY?></p></td>
			                    			<td>
			                    				<?=tep_draw_input_field('city', (isset($form_values_arr['city']) && $form_values_arr['city'] ? $form_values_arr['city'] : ''), 'size="31" tabindex=10', true);?>
			                    				<div class="hint"><p><?=TEXT_HELP_CITY?></p></div>
			                    			</td>
			                			</tr>
			                			<tr>
			                    			<td colspan="2" background="<?=DIR_WS_IMAGES . 'space.gif'?>" height="8"></td>
			                			</tr>
			                			<tr>
				                    		<td><p><?=TEXT_COUNTRY?></p></td>
				                    		<td>
				                    			<p>
				                    				<?=((isset($COUNTRY_CHINESE_ARRAY)) ? $COUNTRY_CHINESE_ARRAY[SIGNUP_DEFAULT_COUNTRY_ID] : tep_get_country_name(SIGNUP_DEFAULT_COUNTRY_ID))?>
				                    			</p>
				                    		</td>
				                		</tr>
			                			<tr>
			                    			<td colspan="2" background="<?=DIR_WS_IMAGES . 'space.gif'?>" height="8"></td>
			                			</tr>
			                			<tr>
			                    			<td><p><?=TEXT_STATE?></p></td>
			                    			<td>
<?	if (count($states_array) > 1) {
	   	echo tep_draw_pull_down_menu('state', $states_array, (int)$form_values_arr['state'], 'id="state" tabindex=11', true);
	} else {
		echo tep_draw_input_field('state', (isset($form_values_arr['state']) && $form_values_arr['state'] ? $form_values_arr['state'] : ''), 'size="31" id="state" tabindex=11', true);
	}
?>
												<div class="hint"><p><?=TEXT_HELP_STATE?></p></div>
			                    			</td>
			                			</tr>
			                			<tr>
			                    			<td colspan="2" background="<?=DIR_WS_IMAGES . 'space.gif'?>" height="8"></td>
			                			</tr>
			                			<tr>
			                    			<td colspan="2" background="<?=DIR_WS_IMAGES . 'space.gif'?>" height="8"></td>
			                			</tr>
			                			<tr>
			                    			<td><p><?=TEXT_CONTACT_NO?></p></td>
			                    			<td>
			                    				<?=tep_draw_input_field('contact_no', (isset($form_values_arr['contact_no']) && $form_values_arr['contact_no'] ? ($ori_contact_no['customers_telephone'] != $form_values_arr['contact_no'] ? $form_values_arr['contact_no'] : (substr($form_values_arr['contact_no'], 0, -4)).'****') : ''), 'size="31" tabindex=12', true);?>
			                    				<div class="hint"><p><?=TEXT_HELP_CONTACT_NO?></p></div>
			                    			</td>
			                			</tr>
			                			<tr>
			                    			<td colspan="2" background="<?=DIR_WS_IMAGES . 'space.gif'?>" height="8"></td>
			                			</tr>
			                			<tr>
			                    			<td><p><?=TEXT_MOBILE_NO?></p></td>
			                    			<td>
			                    				<?=tep_draw_input_field('mobile_no', (isset($form_values_arr['mobile_no']) ? ($ori_contact_no['customers_mobile'] != $form_values_arr['mobile_no'] ? $form_values_arr['mobile_no'] : (substr($form_values_arr['mobile_no'], 0, -4) . '****')) : ''), 'size="31" maxlength="25" tabindex=13', true);?>
			                    				<div class="hint"><p><?=TEXT_HELP_MOBILE_NO?></p></div>
			                    			</td>
			                			</tr>
			                			
			                			<tr>
			                    			<td colspan="2" background="<?=DIR_WS_IMAGES . 'space.gif'?>" height="8"></td>
			                			</tr>
			                			<tr>
			                    			<td><p><?=TEXT_QQ_NO?></p></td>
			                    			<td>
			                    				<?=tep_draw_input_field('qq_no', (isset($form_values_arr['qq_no']) ? $form_values_arr['qq_no'] : ''), 'size="31" maxlength="25" tabindex=14', true);?>
			                    				<div class="hint"><p><?=TEXT_HELP_QQ?></p></div>
			                    			</td>
			                			</tr>
			                			<tr>
			                    			<td colspan="2" background="<?=DIR_WS_IMAGES . 'space.gif'?>" height="8"></td>
			                			</tr>
			                			<tr>
			                    			<td><p><?=TEXT_MSN_ADDRESS?></p></td>
			                    			<td>
			                    				<?=tep_draw_input_field('msn_address', (isset($form_values_arr['msn_address']) ? $form_values_arr['msn_address'] : ''), 'size="31" maxlength="25" tabindex=15').'&nbsp;<span class="instruction">'.TEXT_OPTIONAL.'</span>';?>
			                    				<div class="hint"><p><?=TEXT_HELP_MSN?></p></div>
			                    			</td>
			                			</tr>
			                			<tr>
			                    			<td colspan="2" background="<?=DIR_WS_IMAGES . 'space.gif'?>" height="8"></td>
			                			</tr>
			                			<tr>
			                    			<td><p><?=TEXT_YAHOO_ADDRESS?></p></td>
			                    			<td>
			                    				<?=tep_draw_input_field('yahoo_address', (isset($form_values_arr['yahoo_address']) ? $form_values_arr['yahoo_address'] : ''), 'size="31" maxlength="25" tabindex=16').'&nbsp;<span class="instruction">'.TEXT_OPTIONAL.'</span>';?>
			                    				<div class="hint"><p><?=TEXT_HELP_YAHOO?></p></div>
			                    			</td>
			                			</tr>
			                			<tr>
			                    			<td colspan="2" background="<?=DIR_WS_IMAGES . 'space.gif'?>" height="8"></td>
			                			</tr>
			                			<tr>
			                    			<td><p><?=TEXT_ICQ_NO?></p></td>
			                    			<td>
			                    				<?=tep_draw_input_field('icq_no', (isset($form_values_arr['icq_no']) ? $form_values_arr['icq_no'] : ''), 'size="31" maxlength="25" tabindex=17').'&nbsp;<span class="instruction">'.TEXT_OPTIONAL.'</span>';?>
			                    				<div class="hint"><p><?=TEXT_HELP_ICQ?></p></div>
			                    			</td>
			                			</tr>
			                			<!--tr>
			                    			<td colspan="2" background="<?=DIR_WS_IMAGES . 'space.gif'?>" height="8"></td>
			                			</tr>
			                			<tr>
			                    			<td><p><?=TEXT_FAX_NO?></p></td>
			                    			<td>
			                    				<p><?=tep_draw_input_field('fax_no', (isset($form_values_arr['fax_no']) && $form_values_arr['fax_no'] ? $form_values_arr['fax_no'] : ''), 'size="31"') . '&nbsp;' . tep_image(DIR_WS_ICONS . 'icon_05.gif', '', '14', '15', 'onMouseover="ddrivetip(\''.TEXT_HELP_FAX_NO.'\', \'\', 200);" onMouseout="hideddrivetip();"')?>
			                    				<noscript><?='<br>' . TEXT_HELP_FAX_NO?></noscript></p>
			                    			</td>
			                			</tr>
			                			<tr>
			                    			<td colspan="2" background="<?=DIR_WS_IMAGES . 'space.gif'?>" height="8"></td>
			                			</tr>
			                			<tr>
			                    			<td colspan="2" background="<?=DIR_WS_IMAGES . 'space_line2.gif'?>" height="1"></td>
			                			</tr>
			                			<tr>
			                    			<td colspan="2" background="<?=DIR_WS_IMAGES . 'space.gif'?>" height="8"></td>
						                </tr>
						                <tr>
						                    <td colspan="2"><p><?=tep_image(DIR_WS_ICONS . 'icon_03.gif') . '&nbsp;' . TEXT_PREFERENCES?></p></td>
						                </tr>
						                <tr>
						                    <td colspan="2" background="<?=DIR_WS_IMAGES . 'space.gif'?>" height="8"></td>
						                </tr>
						                <tr>
						                    <td><p><?=TEXT_TIME_ZONE?></p></td>
						                    <td>
						                    	<p><?=tep_draw_pull_down_menu(KEY_SP_TIME_ZONE, $time_zone_array, (isset($form_values_arr[KEY_SP_TIME_ZONE]) ? $form_values_arr[KEY_SP_TIME_ZONE] : '')). '&nbsp;' . TEXT_FIELD_REQUIRED .'&nbsp;' . tep_image(DIR_WS_ICONS . 'icon_05.gif', '', '14', '15', 'onMouseover="ddrivetip(\''.TEXT_HELP_TIME_ZONE.'\', \'\', 200);" onMouseout="hideddrivetip();"')?>
						                    	<noscript><?='<br>' . TEXT_HELP_TIME_ZONE?></noscript></p>
						                    </td>
						                </tr>
			                			<tr>
			                    			<td colspan="2" background="<?=DIR_WS_IMAGES . 'space.gif'?>" height="8"></td>
			                			</tr>
			                			<tr>
			                    			<td><p><?=TEXT_LANGUAGE?></p></td>
											<td>
												<p><?=tep_draw_pull_down_menu(KEY_SP_LANG, $pref_language_array, (isset($form_values_arr[KEY_SP_LANG]) ? $form_values_arr[KEY_SP_LANG] : ''), '') . '&nbsp;' . TEXT_FIELD_REQUIRED .'&nbsp;' . tep_image(DIR_WS_ICONS . 'icon_05.gif', '', '14', '15', 'onMouseover="ddrivetip(\''.TEXT_HELP_LANGUAGE.'\', \'\', 200);" onMouseout="hideddrivetip();"')?>
							                    <noscript><?='<br>' . TEXT_HELP_LANGUAGE?></noscript></p>
											</td>
			                			</tr-->
			                			<tr>
			                    			<td colspan="2" background="<?=DIR_WS_IMAGES . 'space.gif'?>" height="8"></td>
			                			</tr>
			                			<tr>
			                    			<td colspan="2" background="<?=DIR_WS_IMAGES . 'space_line2.gif'?>" height="1"></td>
			                			</tr>
			                			<tr>
			                    			<td colspan="2" background="<?=DIR_WS_IMAGES . 'space.gif'?>" height="8"></td>
			                			</tr>
											<?=$customers_security_obj->draw_question_answer_form();?>
			                			<tr>
			                    			<td colspan="2" align="right"><p><?=tep_image_submit(DIR_WS_IMAGES.'button_next.gif', TEXT_SAVE, 'SSL tabindex=50 '. ($customers_security_obj->check_disable_form() ? ' disabled ' : ''))?></p></td>
			                			</tr>
									</table>
									</form>
									<!--END FIRST TAB -->
<?
} else if ($current_step == 1) {
?>
				        			<!--SECOND TAB -->
									<?=tep_draw_form('process1', FILENAME_MY_ACCOUNT_MGMT, tep_get_all_get_params($get_params_always_exclude_array) . "current_step=$next_step", 'post', 'onSubmit="return check_form2(process1);" autocomplete="off"') . tep_draw_hidden_field('action', 'process')?>
									<table border="0" cellpadding="0" cellspacing="2" width="100%">
						                <tr>
						                    <td><p><?=TEXT_CURRENT_PASSWORD?></p></td>
						                    <td><?=tep_draw_password_field('current_password', '', true, 'autocomplete="off" size="31" tabindex=1');?>
						                    <div class="hint"><p><?=TEXT_HELP_CURRENT_PASSWORD?></p></div>
						                </tr>
						                <tr>
						                    <td colspan="2" background="<?=DIR_WS_IMAGES . 'space.gif'?>" height="8"></td>
						                </tr>
						                <tr>
						                    <td><p><?=TEXT_NEW_PASSWORD?></p></td>
						                    <td><?=tep_draw_password_field('password', (isset($form_values_arr['password']) && $form_values_arr['password'] ? $form_values_arr['password'] : ''), true, 'autocomplete="off" size="31" tabindex=2');?>
						                    <div class="hint"><p><?=TEXT_HELP_PASSWORD?></p></div>
						                </tr>
						                <tr>
						                    <td colspan="2" background="<?=DIR_WS_IMAGES . 'space.gif'?>" height="8"></td>
						                </tr>
						                <tr>
						                    <td><p><?=TEXT_CONFIRM_PASSWORD?></p></td>
						                    <td><?=tep_draw_password_field('confirm_password', (isset($form_values_arr['confirm_password']) && $form_values_arr['confirm_password'] ? $form_values_arr['confirm_password'] : ''), true, 'autocomplete="off" size="31" tabindex=3');?>
						                    <div class="hint"><p><?=TEXT_HELP_RETYPE_PASSWORD?></p></div>
						                </tr>
						                <tr>
						                    <td colspan="2" background="<?=DIR_WS_IMAGES . 'space.gif'?>" height="8"></td>
						                </tr>
						               <tr>
			                    			<td colspan="2" background="<?=DIR_WS_IMAGES . 'space_line2.gif'?>" height="1"></td>
			                			</tr>
			                			<tr>
			                    			<td colspan="2" background="<?=DIR_WS_IMAGES . 'space.gif'?>" height="8"></td>
			                			</tr>
											<?=$customers_security_obj->draw_question_answer_form();?>
						                <tr>
						                    <td colspan="2" background="<?=DIR_WS_IMAGES . 'space.gif'?>" height="8"></td>
						                </tr>
						                <tr>
						                    <td colspan="2" align="right"><p><?=tep_image_submit(DIR_WS_IMAGES.'button_next.gif', TEXT_SAVE, 'SSL tabindex=50 '. ($customers_security_obj->check_disable_form() ?  ' disabled ' : ''))?></p></td>
						                </tr>
									</table>
									</form>
								<!--END SECOND TAB -->
<?
} else if ($current_step == 2) {
	echo $form_content;
} else if ($current_step == 3) {
	$custumers_security_obj = new customers_security($_SESSION['sup_languages_id']);
	$options_array = $custumers_security_obj->get_customer_security_questions_list();
?>
	<script type="text/javascript">
		var question_array = new Array();
		var question_id_array = new Array();
<?	
	echo 'question_array[0] = new Array();'."\n";
	echo 'question_array[0]["id"]= "0";'."\n";
	echo 'question_array[0]["text"]= "'.TEXT_SELECT_SECURITY_QUESTION.'";'."\n";
	for ($question_cnt = 0; $question_cnt < count($options_array); $question_cnt++) {
		echo 'question_array['.($question_cnt+1).'] = new Array();'."\n";
		echo 'question_array['.($question_cnt+1).']["id"]= "'.$options_array[$question_cnt]['id'].'";'."\n";
		echo 'question_array['.($question_cnt+1).']["text"]= "'. unicode_to_utf8(entities_to_unicode($options_array[$question_cnt]['text'])).'";'."\n";
	}
?>
		function check_avilable_question() {
			selected_question_array = new Array();
			array_counter = 1;
			for (var quest = 1; quest <= 3; quest++) {
				if (typeof(document.getElementById('question_'+quest)) != "undefined") {
					selected_question_array[array_counter++] = document.getElementById('question_'+quest).value;
				}
			}

			for (var selectbox = 1; selectbox <= 3; selectbox++) {
				var select_name = 'question_'+selectbox;
				var select_box = document.getElementById(select_name);
			
				//empty all drop down box.
				for (var q=(select_box.options.length-1); q>=0; q--) {
					select_box.remove(q);
				}
			
				//rebuild drop down
				for (var option_cnt=0; option_cnt < question_array.length; option_cnt++) {
					select_box.options[option_cnt] = new Option(question_array[option_cnt]["text"], question_array[option_cnt]["id"]);
					//set default value
					if (selected_question_array[selectbox] == 0) {
						select_box.options[0].selected=true;
					} else if (selected_question_array[selectbox] == question_array[option_cnt]["id"]) {
						select_box.options[option_cnt].selected=true;
					}
				}
				
				//remove selected question
				for (var s_ques=1; s_ques<=3; s_ques++) {
					if (selectbox != s_ques && selected_question_array[s_ques] != 0) {
						//alert('select_box:'+selectbox+' selected Q:'+s_ques+ 'Question'+ select_box.options[selected_question_array[s_ques]].text);
						//find the position of the value at drop down box and remove it.
						for (var new_quest=0; new_quest<select_box.options.length; new_quest++) {
							if (select_box.options[new_quest].value == selected_question_array[s_ques]) {
								select_box.remove(new_quest);
							}
						}
					}
				}
			}		
		}
	</script>
<?
	echo $form_content;
}
?>
			              		<!-- End Tab content-->
		              			</td>
		              			<td width="3" background="<?=DIR_WS_IMAGES?>box_infomiddle04.gif"><?=tep_image(DIR_WS_IMAGES . 'space.gif', '', '1', '3')?></td>
		            		</tr>
		            		<tr>
				              	<td width="3" height="3"><?=tep_image(DIR_WS_IMAGES . 'box_infomiddle07.gif', '', '3', '3')?></td>
				              	<td height="3" background="<?=DIR_WS_IMAGES?>box_infomiddle06.gif"><?=tep_image(DIR_WS_IMAGES . 'space.gif', '', '1', '3')?></td>
				              	<td width="3" height="3"><?=tep_image(DIR_WS_IMAGES . 'box_infomiddle05.gif', '', '3', '3')?></td>
		            		</tr>
		          		</table>
		          	</td>
				</tr>
			</table>
      	</td>
	</tr>
    <tr>
		<td align="left"><?=tep_image(DIR_WS_IMAGES . 'space.gif', '', '1', '3')?></td>
    </tr>
</table>