<script type="text/javascript">
<!--
	var global_loading_message = '<?=TEXT_LOADING_MESSAGE?>';
	
    function showError(msg_id) {
    	var errorMsg = '';
		if (msg_id == 1) {
    		errorMsg = '<?=TEXT_QUANTITY_INVALID?>';
    	} else if (msg_id == 2) {
    		errorMsg = '<?=TEXT_FORM_INCOMPLETE?>';
    	} else if (msg_id == 3) {
    		errorMsg = '<?=TEXT_NOT_ACCEPT_BUYBACK?>';
    	} else if (msg_id != null) {
    		errorMsg = msg_id;
    	}

        document.getElementById('fvl_tbody_error').className = 'hide';
        document.getElementById('fvl_span_error').innerHTML = errorMsg;
        document.getElementById('fvl_tbody_error').className = 'show';
    }

	function set_selected_game(game_cat_id, server_cat_id) {
		//Simulate changing game list dropdown.
		if (server_cat_id == 'undefined') {
			server_cat_id = '0';
		}
		var gameListObj = document.getElementById('fvl_input_game_select');
		var languages_id = '<?=$languages_id?>';
		gameListObj.value = game_cat_id;
		onFavLinksGameSelection(gameListObj, languages_id, server_cat_id);
	}
//-->
</script>
<table border="0" width="100%" cellspacing="0" cellpadding="0">
	<tbody>
		<tr>
			<td width="3" height="40"><?=tep_image(DIR_WS_IMAGES . 'box_side01.gif', '', '3', '40')?></td>
            <td background="<?=DIR_WS_IMAGES . 'box_side02.gif'?>">
				<table width="100%" border="0" cellpadding="0" cellspacing="2">
					<tbody>
					<tr>
                      	<td><?=tep_image(DIR_WS_ICONS . 'icon_03.gif', '', '10', '10', ' hspace="4"') . '&nbsp;<span class="title-text">' . HEADER_TITLE_MY_FAVOURITE_LINKS . '</span>' ?></td>
                    </tr>
                    </tbody>
				</table>
			</td>
            <td width="3" height="40"><?=tep_image(DIR_WS_IMAGES . 'box_side03.gif', '', '3', '40')?></td>
		</tr>
		<tr>
	      	<td width="3" background="<?=DIR_WS_IMAGES . 'box_side08.gif'?>"><?=tep_image(DIR_WS_IMAGES . 'space.gif', '', '3', '1')?></td>
	        <td>
	            <!-- start repeat -->
	            <table cellpadding="2" border="0" cellspacing="0" width="100%">
	                <tbody>
					<tr>
						<td>
							<table border="0" width="100%" cellspacing="0" cellpadding="0" >
			                    <!-- Error Display -->
			            		<tbody class="<?=($fvl_error_found) ? 'show' : 'hide'?>" id="fvl_tbody_error" name="fvl_tbody_error">
		            			<tr>
			            			<td align="left" valign="top" class="errorText">
										<div id="fvl_icon_error" name="fvl_icon_error" style="float: left;">
										<?=tep_image(DIR_WS_ICONS . 'icon_error.gif', ICON_WARNING)?></div>
										<div id="fvl_span_error" name="fvl_span_error"><?=$fvl_error_message?></div>
			            			</td>
			            		</tr>
			            		</tbody>
			                 	<tr>
			            			<td width="1"><?=tep_draw_separator('pixel_trans.gif', '1', '1')?></td>
			            		</tr>
		                    	<!-- Notice Display -->
		            			<tbody class="<?=($fvl_notice_found) ? 'show' : 'hide'?>" id="fvl_tbody_notice" name="fvl_tbody_notice">
		            			<tr>
			            			<td align="left" valign="top" class="noticeText">
										<div id="fvl_icon_notice" name="fvl_icon_notice" style="float: left;"></div>
										<div id="fvl_span_notice" name="fvl_span_notice"><?=$fvl_notice_message?></div>
			            			</td>
		            			</tr>
		            			</tbody>
			                	<tr>
			            			<td width="1"><?=tep_draw_separator('pixel_trans.gif', '1', '1')?></td>
			            		</tr>
							</table>
							<!-- Start inner table -->
							<?=tep_draw_form('add_favourite_link', FILENAME_MY_FAVOURITE_LINKS, tep_get_all_get_params(array('action')) . 'action=add' , "post")?>
					    	<table border="0" cellspacing="5" cellpadding="0" class="buttonBox">
			                	<tr>
			            			<td width="1" colspan="10"><?=tep_draw_separator('pixel_trans.gif', '1', '1')?></td>
			            		</tr>
			            		<tbody class="show" id="fvl_tbody_step1" name="fvl_tbody_step1">
		                		<tr>
		                			<td rowspan="8" width="1" ><?=tep_draw_separator('pixel_trans.gif', '1', '1')?></td>
			                		<td align="left" valign="top"><p><?=TEXT_GAME?></p></td>
									<td rowspan="8" width="1" ><?=tep_draw_separator('pixel_trans.gif', '1', '1')?></td>
		                			<td align="left" valign="top">
										<?='<p>'. tep_draw_pull_down_menu('fvl_input_game_select', $fvl_game_list_arr, (isset($form_values_arr['fvl_input_game_select']) && $form_values_arr['fvl_input_game_select'] ? $form_values_arr['fvl_input_game_select'] : ''), ' id="fvl_input_game_select" onChange="onFavLinksGameSelection(this, \''.$languages_id.'\', \'0\');" ')?>
		                                </p><div name="fvl_div_msgField1" id="fvl_div_msgField1" class="generalText"></div></td>
									<td rowspan="8" width="1" ><?=tep_draw_separator('pixel_trans.gif', '1', '1')?></td>
		                			<td align="left" valign="top"><p><?=TEXT_SERVER?></p></td>
		                			<td rowspan="8" width="1" ><?=tep_draw_separator('pixel_trans.gif', '1', '1')?></td>
			                		<td align="left" valign="top">
										<?='<p>'. tep_draw_pull_down_menu('fvl_input_product_select', $fvl_server_list_arr, (isset($form_values_arr['fvl_input_product_select']) && $form_values_arr['fvl_input_product_select'] ? $form_values_arr['fvl_input_product_select'] : ''), ' id="fvl_input_product_select" disabled ')?>
		                                </p><br><div name="fvl_div_msgField2" id="fvl_div_msgField2" class="generalText"></div></td>
		                			<td rowspan="8" width="1" ><?=tep_draw_separator('pixel_trans.gif', '1', '1')?></td>
		                			<td colspan="9" align="right" valign="top">
		                				<div class="hide" id="fvl_tbody_step2" name="fvl_tbody_step2">
		                				<?=tep_image_submit(THEMA.'button_confirm.gif', TEXT_ADD_NEW, 'id="fvl_button_add" name="fvl_button_add"', 'generalBtn')?>
		                				<br/><div name="fvl_div_msgField3" id="fvl_div_msgField3" class="generalText"></div>
		                				</div>
		                			</td>
			                	</tr>
			            		</tbody>
			            	</table>
			            	<?=tep_draw_hidden_field('fvl_div_game_cat_id', '', ' id="fvl_div_game_cat_id" ')?>
					        </form>
					    	<table border="0" width="100%" cellspacing="5" cellpadding="0" class="buttonBox">
			            		<tr>
									<td background="<?=DIR_WS_IMAGES . 'space_line2.gif'?>" height="1"><?=tep_image(DIR_WS_IMAGES . 'space.gif', '', '1', '1')?></td>
			            		</tr>
			                	<tr>
			            			<td colspan="9"><?=tep_draw_separator('pixel_trans.gif', '1', '1')?></td>
			            		</tr>
			                	<tr>
			            			<td colspan="9" align="right">
			            				<?=tep_draw_form('filter_by_game', FILENAME_MY_FAVOURITE_LINKS, tep_get_all_get_params(array('action')) . 'action=select_filter')?>
			            				<p><?=TEXT_FILTER_BY . ' : ' . tep_draw_pull_down_menu('fvl_input_filter_game_select', $filter_by_game_arr, (isset($form_values_arr['fvl_input_filter_game_select']) ? $form_values_arr['fvl_input_filter_game_select'] : 0), 'onchange="this.form.submit();")')?></p>
			            				</form>
			            			</td>
			            		</tr>
			                	<tr>
			            			<td width="1" colspan="9"><?=tep_draw_separator('pixel_trans.gif', '1', '1')?></td>
			            		</tr>
			                    <tr>
			                    	<td>
									<!-- Results -->
										<table border="0" cellpadding="0" cellspacing="0" width="100%">
								   			<tbody valign="top">
								          	<tr>
								            	<td align="center">
								            		<?=tep_draw_form('fav_form', FILENAME_MY_FAVOURITE_LINKS, tep_get_all_get_params(array('action'))."action=batch_action")?>
													<table border="0" cellpadding="0" cellspacing="0" width="100%" bgcolor="#f5f5f5">
														<tbody valign="top">
														<tr>
															<td height="40" width="5"><?=tep_image(DIR_WS_IMAGES . 'box_grey02a.gif', '', '5', '40')?></td>
															<td height="40" background="<?=DIR_WS_IMAGES?>box_grey02b.gif">
																<table border="0" cellpadding="0" cellspacing="0" height="40" width="100%">
																    <tbody>
																	<tr>
																		<td height="3" colspan="<?=$num_titles?>"></td>
																	</tr>
																	<!-- Start Detail Results-->
																    <tr>
																    	<td width="1%"><?=tep_draw_checkbox_field('select_all', '', false, '', 'id="select_all" onclick="javascript:void(setCheckboxes(\'fav_form\',\'select_all\',\'server_batch\'));"')?></td>
																	<?php
																		echo "<td width={$col_titles[1]['width']} align='left' class='title-text'><p>{$col_titles[1]['title']}</p></td>";
																		echo "<td width={$col_titles[7]['width']} align='center' class='title-text'><p>{$col_titles[7]['title']}</p></td>";
																		echo "<td width={$col_titles[2]['width']} align='center' class='title-text'><p>{$col_titles[2]['title']}</p></td>";
																	//	echo "<td width={$col_titles[3]['width']} align='right' class='title-text'><p>{$col_titles[3]['title']}</p></td>";
																		echo "<td width={$col_titles[3]['width']} align='center' class='title-text'><p>{$col_titles[3]['title']}</p></td>";
																		echo "<td width={$col_titles[4]['width']} align='center' class='title-text'><p>{$col_titles[4]['title']}</p></td>";
																		echo "<td width={$col_titles[5]['width']} align='center' class='title-text'><p>&nbsp;{$col_titles[5]['title']}</p></td>";
																		echo "<td width={$col_titles[6]['width']} align='center' class='title-text'><p>{$col_titles[6]['title']}</p></td>";
																	?>
																    </tr>
																	<tr>
																		<td height="3" colspan="<?=$num_titles?>"></td>
																	</tr>
																    </tbody>
																</table>
															</td>
															<td height="40" width="5"><?=tep_image(DIR_WS_IMAGES . 'box_grey02c.gif', '', '5', '40')?></td>
														</tr>
														<tr>
															<td height="40" width="5"></td>
															<td height="40">
																<table border="0" cellpadding="0" cellspacing="1" height="205" width="100%">
																    <tbody>
																    <tr>
																    	<td colspan="<?=$num_titles?>">
																	    	<div id="fvl_div_fav_links" name="fvl_div_fav_links" style="position:relative; height:<?=($fav_links_num_rows>0 ? '205' : '0')?>; width:100%; overflow:auto;"><?=$favLinksHTML?></div>
																		</td>
																    </tr>
												                    <tr>
												                      <td colspan="<?=$num_titles?>" background="<?=DIR_WS_IMAGES . 'space_line2.gif'?>" height="1"><?=tep_image(DIR_WS_IMAGES . 'space.gif', '', '1', '1')?></td>
												                    </tr>
																	<tr>
																		<td height="5" colspan="<?=$num_titles?>"></td>
																	</tr>
																    </tbody>
																</table>
															</td>
															<td height="40" width="5"></td>
														</tr>
														<tr>
															<td align="center" colspan="<?=$num_titles?>">
																<table border="0" width="100%" cellpadding='2' cellspacing='2'>
																	<tr>
																		<td width="45%" align="left">&nbsp;
																		<?
																			$batch_action_array = array(array('id' => '', 'text' => PULL_DOWN_DEFAULT),
																										array('id' => 'AddPresale', 'text' => BUTTON_PRESALE_NOTICE),
																										array('id' => 'RemovePresale', 'text' => BUTTON_PRESALE_REMOVE),
																										array('id' => 'RemoveFav', 'text' => SELECT_TEXT_DELETE_FAV)
																										);
																			echo tep_draw_pull_down_menu('batch_action', $batch_action_array, '', 'onChange="return confirmBatchAction(this)"');
																		?>
																		</td>
																		<td align="left">
																			<div class='title-text'><span id="num_results"><?=$fav_links_num_rows?></span> <?=TEXT_LINKS_FOUND?></div>
																		</td>
																	</tr>
																</table>
															</td>
														</tr>
														</tbody>
													</table>
													</form>
												</td>
											</tr>
										</tbody>
									</table>
								</td>
							</tr>
						</table>
						<!-- end inner table -->
					</td>
				</tr>
				</tbody>
			</table>
			<!-- end repeat -->
		</td>
		<td width="3" background="<?=DIR_WS_IMAGES . 'box_side04.gif'?>"><?=tep_image(DIR_WS_IMAGES . 'space.gif', '', '3', '1')?></td>
	</tr>
	<tr>
		<td width="3" height="16"><?=tep_image(DIR_WS_IMAGES . 'box_side07.gif', '', '3', '16')?></td>
		<td background="<?=DIR_WS_IMAGES . 'box_side06.gif'?>"><?=tep_image(DIR_WS_IMAGES . 'space.gif', '', '', '16')?></td>
		<td width="3" height="16"><?=tep_image(DIR_WS_IMAGES . 'box_side05.gif', '', '3', '16')?></td>
	</tr>
	</tbody>
</table>

<script type="text/javascript">
<!--
<?
	$pre_select_game = isset($form_values_arr['fvl_input_game_select']) ? $form_values_arr['fvl_input_game_select'] : '0';
	if ($pre_select_game) {
		echo "set_selected_game('".$pre_select_game."', '0');\n";
	}
?>
	function confirmBatchAction(selObj) {
		if (trim_str(selObj.value) != '') {
			if (trim_str(selObj.value) == 'RemoveFav') {
				var agree = confirm('<?=JS_CONFIRM_DELETE_FAV?>');
				if (agree) {
					selObj.form.submit();
					return true;
				}
			} else {
				selObj.form.submit();
				return true;
			}
		}
		
		selObj.selectedIndex = 0;
		return false;
	}
//-->
</script>