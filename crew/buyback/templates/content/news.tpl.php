<?
if(!function_exists('eval_buffer')) {
	function eval_buffer($string) {
		ob_start();
		eval("$string[2];");
		$return = ob_get_contents();
		ob_end_clean();
		return $return;
	}
}

if(!function_exists('eval_print_buffer')) {
	function eval_print_buffer($string) {
		ob_start();
		eval("print $string[2];");
		$return = ob_get_contents();
		ob_end_clean();
		return $return;
	}
}

if(!function_exists('eval_html')) {
	function eval_html($string) {
		$string = preg_replace_callback("/(<\?=)(.*?)\?>/si", "eval_print_buffer",$string);
		return preg_replace_callback("/(<\?php|<\?)(.*?)\?>/si", "eval_buffer",$string);
	}
}

$cat_id = (isset($HTTP_GET_VARS["cPath"]) && $HTTP_GET_VARS["cPath"] ? $HTTP_GET_VARS["cPath"] : '');
$news_id = (isset($HTTP_GET_VARS["news_id"]) && $HTTP_GET_VARS["news_id"] ? (int)$HTTP_GET_VARS["news_id"] : 0);

$this_site_id = 0;
if (defined('SITE_ID')) {
	$this_site_id = SITE_ID;
}
$news_display_sites_where_str = "FIND_IN_SET( '" . $this_site_id . "', news_display_sites)";

if ($news_id > 0) {
/*--------------------------------------------------------------- BEFORE ---------------------------------------------------------------------	
	$listing_sql = 'select news_id, news_groups_id, headline, content, date_added, url from ' . TABLE_LATEST_NEWS . " news where status = '1' and $news_display_sites_where_str and language = '". $languages_id. "' and news_id ='".$news_id."'";
--------------------------------------------------------------- BEFORE --------------------------------------------------------------------- */	
//--------------------------------------------------------------- AFTER ---------------------------------------------------------------------
	$listing_sql = 'select lnd.news_id, ln.news_groups_id, lnd.headline, lnd.content, ln.date_added, ln.url from ' . TABLE_LATEST_NEWS . " as ln inner join ".TABLE_LATEST_NEWS_DESCRIPTION." as lnd on ln.news_id = lnd.news_id news where ln.status = '1' and $news_display_sites_where_str and lnd.language_id = '". $languages_id. "' and lnd.news_id ='".$news_id."'";
//--------------------------------------------------------------- AFTER ---------------------------------------------------------------------
} else if ($cat_id) {
	$cat_path_array = explode('_', $cat_id);
	$last_cat_id = end($cat_path_array);

	$news_groups_cat = array(" news_groups_cat_id LIKE '%,0,%' ");
	foreach ($cat_path_array as $cur_cat) {
		$news_groups_cat[] = " news_groups_cat_id LIKE '%,$cur_cat,%' ";
	}

	$news_groups_cat_where_str = implode(" OR ", $news_groups_cat);
	$news_cat_where_str = " IF(latest_news_include_subcat<>0, (".$news_groups_cat_where_str."), news_groups_cat_id LIKE '%,".$last_cat_id.",%' OR news_groups_cat_id LIKE '%,0,%')" ;
/*--------------------------------------------------------------- BEFORE ---------------------------------------------------------------------
	$listing_sql = "select news_id, news_groups_id, headline, content, date_added, url from " . TABLE_LATEST_NEWS . " news where status = '1' and $news_display_sites_where_str and language = '". $languages_id. "' and news_groups_id='".$_REQUEST["news_type"]."' and ".$news_cat_where_str." order by date_added DESC";
--------------------------------------------------------------- BEFORE --------------------------------------------------------------------- */
//--------------------------------------------------------------- AFTER ---------------------------------------------------------------------
	$listing_sql = "select lnd.news_id, ln.news_groups_id, lnd.headline, lnd.content, ln.date_added, ln.url from " . TABLE_LATEST_NEWS . " as ln inner join ".TABLE_LATEST_NEWS_DESCRIPTION." as lnd on ln.news_id = lnd.news_id where ln.status = '1' and $news_display_sites_where_str and lnd.language_id = '". $languages_id. "' and ln.news_groups_id='".$_REQUEST["news_type"]."' and ".$news_cat_where_str." order by ln.date_added DESC";
//--------------------------------------------------------------- AFTER ---------------------------------------------------------------------
} else {
/*--------------------------------------------------------------- BEFORE ---------------------------------------------------------------------
	$listing_sql = 'select news_id, news_groups_id, headline, content, date_added, url from ' . TABLE_LATEST_NEWS . " news where status = '1' and $news_display_sites_where_str and language = '". $languages_id. "' and news_groups_id='".$_REQUEST["news_type"]."' order by date_added DESC";
--------------------------------------------------------------- BEFORE --------------------------------------------------------------------- */
//--------------------------------------------------------------- AFTER ---------------------------------------------------------------------
	$listing_sql = 'select lnd.news_id, ln.news_groups_id, lnd.headline, lnd.content, ln.date_added, ln.url from ' . TABLE_LATEST_NEWS . " as ln inner join ".TABLE_LATEST_NEWS_DESCRIPTION." as lnd on ln.news_id = lnd.news_id where ln.status = '1' and $news_display_sites_where_str and lnd.language_id = '". $languages_id. "' and ln.news_groups_id='".$_REQUEST["news_type"]."' order by ln.date_added DESC";
//--------------------------------------------------------------- AFTER ---------------------------------------------------------------------
}

$listing_split = new splitPageResults($HTTP_GET_VARS['page'], MAX_DISPLAY_LATEST_NEWS_PAGE, $listing_sql, $listing_numrows);
$listing_query = tep_db_query($listing_sql);

$info_box_contents = array();
$news_group_name_select_sql = "SELECT news_groups_name FROM " . TABLE_LATEST_NEWS_GROUPS . " WHERE "
							  . (isset($_REQUEST["news_type"]) ? "news_groups_id='{$_REQUEST["news_type"]}'" : "1");
$news_group_name_result_sql = tep_db_query($news_group_name_select_sql);
if ($news_group_name_row = tep_db_fetch_array($news_group_name_result_sql)) {
	$local_constant_news_title = preg_replace('/[^a-z]/i', '_', $news_group_name_row["news_groups_name"]);
	
	$info_box_contents[] = array(	'align' => 'left',
									'params' => 'class="latestNewsBoxHeading"',
       	    	                  	'text'  => (defined(strtoupper("LOCAL_".$local_constant_news_title)) ? constant(strtoupper("LOCAL_".$local_constant_news_title)) : $news_group_name_row["news_groups_name"]));
	new contentBoxHeading($info_box_contents);
}

$count=0;
$display_these = array();
while ($listing = tep_db_fetch_array($listing_query)) {
	$info_box_contents = array();
  	if (isset($HTTP_GET_VARS["news_id"]) AND $HTTP_GET_VARS["news_id"] == $listing["news_id"]) {
  		$info_box_contents[] = array(	'params' => 'class="latestNewsBoxHeading"',
  										'text' => 	'<table border="0" width="100%" cellspacing="0" cellpadding="0"><tr>'
                                        			.'<td align="left">'
                                        			.'<div class="latestNewsTitle">'.eval_html($listing["headline"]).'</div>'
                                        			.'</td>'
                                        			.'<td align="right">'
                                        			.'<div class="latestNewsDate">'.tep_date_long($listing["date_added"].'</div>')
                                        			.'</td>'
                                        			."</tr></table>\n"
									);
	} else {
		$info_box_contents[] = array(	'params' => 'class="latestNewsBoxHeading"',
										'text' => 	'<table border="0" width="100%" cellspacing="0" cellpadding="0"><tr>'
                              			.'</td>'
                              			."</tr></table>\n"
									);
	}
    $info_box_contents = array();
  	if ($listing['url']) {
		$info_box_contents[] = array(	'params' => 'class="latestNewsBoxHeading"',
										'text' => 	'<div class="latestNewsTitle">'.eval_html($listing["headline"]).'</div>'.
                   									'<div class="latestNewsDate">'.tep_date_long($listing["date_added"]).'</div>'
                   									.'<table border="0"><tr><td>&nbsp;&nbsp;</td>' .'<td class="latestNewsBoxContents">'. nl2br(eval_html($listing['content'])) .'<br><br><a  class="SystemNav" href=http://'.$listing["url"].' class="latestNewsLink">[URL]</a><br>'. '</td></tr></table><br>');
	} else {
	    if ($news_id == 0) {
    	     $text = "<table border=\"0\" cellspacing=\"0\" cellpadding=\"0\" width=\"100%\"><tr><td colspan=\"2\" align=\"left\">" .
    	             "<table border=\"0\" cellspacing=\"0\" cellpadding=\"0\" width=\"100%\"><tr onmouseover=\"this.className='latestNewsTableRowOver';\" onclick=\"showHideContent('". $listing['news_id'] ."', '". $listing["date_added"] ."');\"><td align=\"left\"><div class=\"latestNewsTitle\">". eval_html($listing["headline"]) ."</div></td><td width=\"20%\"><div id=\"datetime_". $listing['news_id']."\" class=\"latestNewsDate\">". $listing["date_added"] ."</div></td><td align=\"right\" width=\"5%\" class=\"latestNewsTitle\"><div id=\"clip_". $listing['news_id']."\" class=\"latestNewsTitle\">". tep_image(DIR_WS_ICONS . 'icon_expand.gif') ."</div></td></tr></table>" .
    	             "</td></tr><tbody id=\"content_". $listing['news_id'] ."\" class=\"hide\">" .
    	             "<tr><td colspan=\"2\"><div class=\"latestNewsDate\">". tep_date_long($listing["date_added"]) ."</div></td></tr>" .
    	             "<tr><td>&nbsp;&nbsp;</td><td class=\"latestNewsBoxContents\">". nl2br(eval_html($listing['content'])) . "</td></tr>" .
    	             "</tbody><tr><td colspan=\"2\">&nbsp;&nbsp;</td></tr></table>";
        } else {
            $text = '<div class="latestNewsTitle">'. eval_html($listing["headline"]) .'</div>' .
    	            '<div class="latestNewsDate">'. tep_date_long($listing["date_added"]) .'</div>' .
                    '<table border="0"><tr><td>&nbsp;&nbsp;</td><td class="latestNewsBoxContents">'. nl2br(eval_html($listing['content'])) . '</td></tr></table><br>';
        }
		$info_box_contents[] = array(	'params' => 'class="latestNewsBoxHeading"',
     									'text' => $text);
	}

	$display_these[$count]= $info_box_contents[0]['text'];
    $count++;
}
?>
<script language="javascript">
<!--
	function showHideContent(itemID, dateTime) {
	    var content = 'content_' + itemID;
	    var clip = 'clip_' + itemID;
	    var date_time = 'datetime_' + itemID;
	    
	    if (DOMCall(content).className == 'hide') {
	        DOMCall(content).className = 'show';
	        DOMCall(clip).innerHTML = '<?=tep_image(DIR_WS_ICONS . 'icon_collapse.gif')?>';
	        DOMCall(date_time).innerHTML = "&nbsp;";
	        
	    } else {
	        DOMCall(content).className = 'hide';
	        DOMCall(date_time).innerHTML = dateTime;
	        DOMCall(clip).innerHTML = '<?=tep_image(DIR_WS_ICONS . 'icon_expand.gif')?>';
	    }
	}
//-->
</script>
<table border="0" width="100%" cellspacing="0" cellpadding="0">
	<tbody>
	<tr>
		<td width="3" height="20"><?=tep_image(DIR_WS_IMAGES . 'box_side01.gif', '', '3', '20')?></td>
		<td background="<?=DIR_WS_IMAGES . 'box_side02.gif'?>">
			<table width="100%" border="0" cellpadding="0" cellspacing="2">
				<tbody>
				<tr>
					<td align="right" class="smallText">
<?	if ( ($listing_numrows > 0) && ( (PREV_NEXT_BAR_LOCATION == '2') || (PREV_NEXT_BAR_LOCATION == '3') )) {
		echo $listing_split->display_links($listing_numrows, MAX_DISPLAY_LATEST_NEWS_PAGE, MAX_DISPLAY_PAGE_LINKS, $HTTP_GET_VARS['page'], tep_get_all_get_params(array('page', 'info', 'x', 'y')));
	} ?>
					</td>
				</tr>
				</tbody>
			</table>
		</td>
		<td width="3" height="20"><?=tep_image(DIR_WS_IMAGES . 'box_side03.gif', '', '3', '20')?></td>
	</tr>
	<tr>
		<td width="3" background="<?=DIR_WS_IMAGES . 'box_side08.gif'?>"><?=tep_image(DIR_WS_IMAGES . 'space.gif', '', '3', '1')?></td>
		<td>
		<!-- start repeat -->
			<table cellpadding="2" border="0" cellspacing="0" width="100%">
				<tbody>
            	<tr>
					<td>
<? 	for($i=0; $i< sizeof($display_these) ; $i++) {
		echo $display_these[$i];
	} ?>
					</td>
				</tr>
				</tbody>
			</table>
            <!-- end repeat -->
        </td>
        <td width="3" background="<?=DIR_WS_IMAGES . 'box_side04.gif'?>"><?=tep_image(DIR_WS_IMAGES . 'space.gif', '', '3', '1')?></td>
	</tr>
	<tr>
		<td width="3" height="16"><?=tep_image(DIR_WS_IMAGES . 'box_side07.gif', '', '3', '16')?></td>
		<td background="<?=DIR_WS_IMAGES . 'box_side06.gif'?>"><?=tep_image(DIR_WS_IMAGES . 'space.gif', '', '', '16')?></td>
		<td width="3" height="16"><?=tep_image(DIR_WS_IMAGES . 'box_side05.gif', '', '3', '16')?></td>
	</tr>
	</tbody>
</table>