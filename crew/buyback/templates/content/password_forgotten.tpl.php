<script type="text/javascript" src="includes/javascript/hints.js"></script>
<?php
$boxContent = '';
$boxHeading = '';

if ($action == 'secret_question') {
	$disabled_submit = false;
	
	$boxContent = tep_draw_form('password_forgotten', FILENAME_PASSWORD_FORGOTTEN, '', 'post', 'autocomplete="off" '. ($customers_security_obj->check_secret_question_isset() == true ? ' onSubmit="return check_form3(password_forgotten);"': ' onSubmit="return check_form_pin(password_forgotten);"' ) ) . tep_draw_hidden_field('action', 'reset_password');
	$boxHeading = HEADER_TITLE_FORGOT_PASSWORD;
	$boxContent .= '
					<table width="100%" border="0" cellspacing="2" cellpadding="0">
						<tr>
						  <td align="left">
							<table width="100%" border="0" cellspacing="2" cellpadding="0">
								<tr>
								  <td colspan="2" align="left"><p>' . tep_image(DIR_WS_IMAGES . 'space.gif', '', '10', '3') . '</p></td>
								</tr>
							  	<tr>
									<td colspan="2"><p>' . TEXT_INSTRUCTIONS . '</p></td>
							  	</tr>
								<tr>
								  <td colspan="2" align="left"><p>' . tep_image(DIR_WS_IMAGES . 'space.gif', '', '10', '8') . '</p></td>
								</tr>
							  	<tr>
									<td><p>' . TEXT_EMAIL_ADDRESS . '</p></td>
									<td><p>' . $customer_email_address . '</p></td>
									' . tep_draw_hidden_field('email_address', $customer_email_address) . '
							  	</tr>
								<tr>
				                    <td colspan="2" background="' . DIR_WS_IMAGES . 'space.gif" height="8"></td>
				                </tr>
								<tr>
	                    			<td colspan="2" background="' . DIR_WS_IMAGES . 'space_line2.gif" height="1"></td>
	                			</tr>
	                			<tr>
	                    			<td colspan="2" background="' . DIR_WS_IMAGES . 'space.gif" height="8"></td>
	                			</tr>
	                			';
	if ($customers_security_obj->check_secret_question_isset() && !$customers_security_obj->check_secret_question_is_reset()) {
		$boxContent .= $customers_security_obj->draw_question_answer_form();
		
		if ($customers_security_obj->check_disable_form())	$disabled_submit = true;
	} else {
		$boxContent .= '<tr>
								<td><p>' . TEXT_PIN_NUMBER . '</p></td>
								<td>' . tep_draw_input_field('pin_number', '', 'size="7"', true) . '
									<div class="hint"><p>' . TEXT_HELP_PIN_NUMBER . '</p></div>
								</td>
						  	</tr>';
	}
	
	$boxContent .= '   			<tr>
				                    <td colspan="2" background="' . DIR_WS_IMAGES . 'space.gif" height="8"></td>
				                </tr>
								<tr>
	                    			<td colspan="2" background="' . DIR_WS_IMAGES . 'space_line2.gif" height="1"></td>
	                			</tr>
	                			<tr>
	                    			<td colspan="2" background="' . DIR_WS_IMAGES . 'space.gif" height="8"></td>
	                			</tr>
								<tr>
								  <td colspan="2" align="left"><p>' . tep_image(DIR_WS_IMAGES . 'space.gif', '', '10', '8') . '</p></td>
								</tr>
				                <tr>
				                    <td colspan="2" align="right"><p>' . tep_image_submit(DIR_WS_IMAGES.'button_next.gif', TEXT_RESET_PASSWORD, 'SSL ') . '</p></td>
				                </tr>
								<tr>
								  <td colspan="2" align="left"><p>' . tep_image(DIR_WS_IMAGES . 'space.gif', '', '10', '8') . '</p></td>
								</tr>
						  	</table>
						  </td>
						</tr>
					</table>
					</form>';

} else {
	$boxContent = tep_draw_form('password_forgotten', FILENAME_PASSWORD_FORGOTTEN, '', 'post', 'autocomplete="off" onSubmit="return check_form_email_address(password_forgotten);"') . tep_draw_hidden_field('action', 'check_email');
	$boxHeading = HEADER_TITLE_FORGOT_PASSWORD;
	$boxContent .= '
					<table width="100%" border="0" cellspacing="2" cellpadding="0">
						<tr>
						  <td align="left">
							<table width="100%" border="0" cellspacing="2" cellpadding="0">
								<tr>
								  <td colspan="2" align="left"><p>' . tep_image(DIR_WS_IMAGES . 'space.gif', '', '10', '3') . '</p></td>
								</tr>
							  	<tr>
									<td colspan="2"><p>' . TEXT_INSTRUCTIONS . '</p></td>
							  	</tr>
								<tr>
								  <td colspan="2" align="left"><p>' . tep_image(DIR_WS_IMAGES . 'space.gif', '', '10', '8') . '</p></td>
								</tr>
							  	<tr>
									<td><p>' . TEXT_EMAIL_ADDRESS . '</p></td>
									<td>' . tep_draw_input_field('email_address', '', 'size="30"', true) .'
									<div class="hint"><p>' . TEXT_HELP_EMAIL_ADDRESS . '</p></div>
									</td>
							  	</tr>
								<tr>
				                    <td colspan="2" background="' . DIR_WS_IMAGES . 'space.gif" height="8"></td>
				                </tr>
								<tr>
	                    			<td colspan="2" background="' . DIR_WS_IMAGES . 'space_line2.gif" height="1"></td>
	                			</tr>
	                			<tr>
	                    			<td colspan="2" background="' . DIR_WS_IMAGES . 'space.gif" height="8"></td>
	                			</tr>
								<tr>
								  <td colspan="2" align="left"><p>' . tep_image(DIR_WS_IMAGES . 'space.gif', '', '10', '8') . '</p></td>
								</tr>
				                <tr>
				                    <td colspan="2" align="right"><p>' . tep_image_submit(DIR_WS_IMAGES.'button_next.gif', TEXT_NEXT, 'SSL') . '</p></td>
				                </tr>
								<tr>
								  <td colspan="2" align="left"><p>' . tep_image(DIR_WS_IMAGES . 'space.gif', '', '10', '8') . '</p></td>
								</tr>
						  	</table>
						  </td>
						</tr>
					</table>
					</form>';
}

require(DIR_WS_TEMPLATES . TEMPLATENAME_INFOMIDDLE);
?>