<?
if ($current_step == 0) {
	if (isset($session_signup_agreement) && $session_signup_agreement == '1') {
		$show_tabs = true;
	} else {
		$show_tabs = false;
	}
} else {
	$show_tabs = true;
}
?>

<div id="dhtmlTooltip"></div>
<script type="text/javascript" language="javascript" src="includes/javascript/dhtml_tooltip.js"></script>
<script type="text/javascript" src="includes/javascript/supplier_xmlhttp.js"></script>
<script type="text/javascript" src="includes/javascript/hints.js"></script>

<table width="100%" border="0" cellspacing="0" cellpadding="0">
	<tr>
		<td align="left"><?=tep_image(DIR_WS_IMAGES . 'space.gif', '', '1', '3')?></td>
    </tr>
    <tr>
		<td align="left">
			<table border="0" width="100%" cellspacing="1" cellpadding="0">
				<tr>
					<td>
						<table border="0" width="100%" height="26" cellpadding="0" cellspacing="0" background="<?=DIR_WS_IMAGES?>tab_01.gif">
							<tr>
<?
if ($show_tabs) {
	foreach($tabs_array as $key => $tabname) {
		if ($key == $current_step) {
?>
								<!-- Start Active tab-->
		              			<td width="3"><?=tep_image(DIR_WS_IMAGES . 'tab_active01.gif', '', '3', '26')?></td>
		              			<td background="<?=DIR_WS_IMAGES?>tab_active02.gif">
		              				<div align="center">
		                				<table cellspacing="2" cellpadding="0">
		                  					<tr>
		                    					<td><p><?=tep_image(DIR_WS_ICONS . 'icon_03.gif', '', '10', '10', 'hspace="4"') . '&nbsp;<span class="title-text">' . $tabname . '</span>'?></p></td>
		                  					</tr>
		                				</table>
		              				</div>
		              			</td>
		              			<td width="3"><?=tep_image(DIR_WS_IMAGES . 'tab_active03.gif', '', '3', '26')?></td>
		              			<!-- End Active tab-->
<?		} else { ?>
					  			<!-- Start Inactive tab-->
		              			<td width="3"><?=tep_image(DIR_WS_IMAGES . 'tab_inactive01.gif', '', '3', '26')?></td>
		              			<td background="<?=DIR_WS_IMAGES?>tab_inactive02.gif"><div align="center">
		                			<table cellspacing="2" cellpadding="0">
		                  				<tr>
<?
			$tab_content = tep_image(DIR_WS_ICONS . 'icon_03.gif', '', '10', '10', 'hspace="4"') . '&nbsp;<span class="title-text">' . $tabname . '</span>';
			$tab_link_start = '';
			$tab_link_end = '';
			
			//Lets think about showing a link to this tab.
			//So, we only link the tab if the tab has been submitted before. Otherwise, user has to use the Next button to submit.
			switch ($key) {
				case 0:
					//Always allow
					$tab_content = $tab_link_start . $tab_content . $tab_link_end;
					break;
				case 1:
					if (isset($form_values_arr['password'])) {
						$tab_content = $tab_link_start . $tab_content . $tab_link_end;
					}
					
					break;
				case 2:
					if (isset($form_values_arr['payment_method'])) {
						$tab_content = $tab_link_start . $tab_content . $tab_link_end;
					}
					
					break;
			}
?>
											<td><p><?=$tab_content?></td>
		                  				</tr>
		                			</table>
		              				</div>
			              		</td>
			              		<td width="3"><?=tep_image(DIR_WS_IMAGES . 'tab_inactive03.gif', '', '3', '26')?></td>
						  		<!-- End Inactive tab-->
<?		}
	}
}
?>
			              		<td align="right"><?=tep_image(DIR_WS_IMAGES . 'tab_02.gif', '', '3', '26')?></td>
			            	</tr>
						</table>
					</td>
				</tr>
				<tr>
					<td>
						<table width="100%" cellspacing="0" cellpadding="0">
			            	<tr>
			              		<td width="3" background="<?=DIR_WS_IMAGES?>box_infomiddle08.gif"><?=tep_image(DIR_WS_IMAGES . 'space.gif', '', '1', '3')?></td>
			              		<td>
								<!-- Start Tab content-->
<?
if ($show_tabs) {
?>
				            		<table border="0" cellpadding="0" cellspacing="2" width="100%">
							    		<tr>
							        		<td valign="top" align="right"><p style="padding:7px;"><?=TEXT_IS_MANDATORY?></p></td>
							    		</tr>
									</table>
<?
}

if ($current_step == 0) {
	if (isset($session_signup_agreement) && $session_signup_agreement == '1') {
?>
									<!--FIRST TAB -->
<?
		echo tep_draw_form('create_account', FILENAME_SUPPLIER_SIGNUP, tep_get_all_get_params($get_params_always_exclude_array) . "current_step=$next_step", 'post', 'onSubmit="return check_form(create_account);" autocomplete="off"');
		echo tep_draw_hidden_field('action', 'process');
?>
				            		<table border="0" cellpadding="0" cellspacing="2" width="100%">
				                		<tr>
				                    		<td><p><?=TEXT_EMAIL_ADDRESS?></p></td>
				                    		<td>
				                    			<?=tep_draw_input_field('email_address', (isset($form_values_arr['email_address']) && $form_values_arr['email_address'] ? $form_values_arr['email_address'] : ''), 'size="31" tabindex=1', true);?>
				                    			<div class="hint"><p><?=TEXT_HELP_EMAIL?></p></div>
				                    		</td>
				                		</tr>
				                		<tr>
				                    		<td colspan="2" background="<?=DIR_WS_IMAGES . 'space.gif'?>" height="8"></td>
				                		</tr>
				                		<tr>
				                    		<td><p><?=TEXT_NEW_PASSWORD?></p></td>
				                    		<td>
				                    			<?=tep_draw_password_field('password', (isset($form_values_arr['password']) && $form_values_arr['password'] ? $form_values_arr['password'] : ''), true, 'size="31" tabindex=2');?>
				                    			<div class="hint"><p><?=TEXT_HELP_PASSWORD?></p></div>
				                    		</td>
				                		</tr>
				                		<tr>
				                    		<td colspan="2" background="<?=DIR_WS_IMAGES . 'space.gif'?>" height="8"></td>
				                		</tr>
				                		<tr>
				                    		<td><p><?=TEXT_CONFIRM_PASSWORD?></p></td>
				                    		<td>
				                    			<?=tep_draw_password_field('confirm_password', (isset($form_values_arr['confirm_password']) && $form_values_arr['confirm_password'] ? $form_values_arr['confirm_password'] : ''), true, 'size="31" tabindex=3');?>
				                    			<div class="hint"><p><?=TEXT_HELP_RETYPE_PASSWORD?></p></div>
				                    		</td>
				                		</tr>
				                		<tr>
				                    		<td colspan="2" background="<?=DIR_WS_IMAGES . 'space.gif'?>" height="8"></td>
				                		</tr>
				                		<tr>
				                    		<td><p><?=ENTRY_SIGNUP_NAME_NAME?></p></td>
				                    		<td>
				                    				<?=tep_draw_input_field('lastname', (isset($form_values_arr['lastname']) && $form_values_arr['lastname'] ? $form_values_arr['lastname'] : ''), 'size="3" tabindex=4') . '<span class="instruction">('.TEXT_LAST_NAME.')</span>';?>
				                    				<?=tep_draw_input_field('firstname', (isset($form_values_arr['firstname']) && $form_values_arr['firstname'] ? $form_values_arr['firstname'] : ''), 'size="6" tabindex=5') . '<span class="instruction">('.TEXT_FIRST_NAME.')</span>';?>
				                    				<div class="hint"><p><?=TEXT_HELP_LASTNAME?></p></div>
				                    		</td>
				                		</tr>
				                		<tr>
				                    		<td colspan="2" background="<?=DIR_WS_IMAGES . 'space.gif'?>" height="8"></td>
				                		</tr>
				                		<tr>
				                    		<td><p><?=TEXT_DATE_OF_BIRTH?></p></td>
				                    		<td>
				                    			<?=tep_draw_pull_down_menu('dob_month', $month_array, (isset($form_values_arr['dob_month']) && $form_values_arr['dob_month'] ? $form_values_arr['dob_month'] : ''), 'tabindex=6').'&nbsp;'.
				                              	tep_draw_pull_down_menu('dob_day', $day_array, (isset($form_values_arr['dob_day']) && $form_values_arr['dob_day'] ? $form_values_arr['dob_day'] : ''), 'tabindex=7').'&nbsp;'.
				                              	tep_draw_pull_down_menu('dob_year', $year_array, (isset($form_values_arr['dob_year']) && $form_values_arr['dob_year'] ? $form_values_arr['dob_year'] : ''), 'tabindex=8').'&nbsp;' . TEXT_FIELD_REQUIRED;?>
				                              	<div class="hint"><p><?=TEXT_HELP_DOB?></p></div>
				                          	</td>
				                		</tr>
				                		<tr>
				                    		<td colspan="2" background="<?=DIR_WS_IMAGES . 'space.gif'?>" height="8"></td>
				                		</tr>
				                		<tr>
				                    		<td><p><?=TEXT_GENDER?></p></td>
				                    		<td>
				                    			<?=tep_draw_radio_field('gender', 'M', (isset($form_values_arr['gender']) ? ($form_values_arr['gender'] == 'M' ? true : false) : true), '', 'tabindex=9') . '&nbsp;' . '<span class="instruction">' . TEXT_MALE . '</span>'
				                    			. '&nbsp;' . tep_draw_radio_field('gender', 'F', (isset($form_values_arr['gender']) && $form_values_arr['gender'] == 'F' ? true : false), '', 'tabindex=9') . '&nbsp;' . '<span class="instruction">' . TEXT_FEMALE . '</span>';?>
				                    			<div class="hint"><p><?=TEXT_HELP_GENDER?></p></div>
				                    		</td>
				                		</tr>
				                		<tr>
				                    		<td colspan="2" background="<?=DIR_WS_IMAGES . 'space.gif'?>" height="8"></td>
				                		</tr>
				                		<tr>
				                    		<td colspan="2" background="<?=DIR_WS_IMAGES . 'space_line2.gif'?>" height="1"></td>
				                		</tr>
				                		<tr>
				                    		<td colspan="2" background="<?=DIR_WS_IMAGES . 'space.gif'?>" height="8"></td>
				                		</tr>
				                		<tr>
				                    		<td colspan="2"><p><?=tep_image(DIR_WS_ICONS . 'icon_03.gif') . '&nbsp;' . TEXT_CONTACT_INFO?></p></td>
				                		</tr>
				                		<tr>
				                    		<td colspan="2" background="<?=DIR_WS_IMAGES . 'space.gif'?>" height="8"></td>
				                		</tr>
				                		
				                		<tr>
				                    		<td><p><?=TEXT_ADDRESS?></p></td>
				                    		<td>
				                    			<?=tep_draw_input_field('address', (isset($form_values_arr['address']) && $form_values_arr['address'] ? $form_values_arr['address'] : ''), 'size="31" tabindex=10', true);?>
				                    			<div class="hint"><p><?=TEXT_HELP_ADDRESS?></p></div>
				                    		</td>
				                		</tr>
				                		
				                		<tr>
				                    		<td colspan="2" background="<?=DIR_WS_IMAGES . 'space.gif'?>" height="8"></td>
				                		</tr>	
				                		<?
				                		/*
				                		<!--tr>
				                    		<td><p><?=TEXT_SUBURB?></p></td>
				                    		<td>
				                    			<p><?=tep_draw_input_field('suburb', (isset($form_values_arr['suburb']) && $form_values_arr['suburb'] ? $form_values_arr['suburb'] : ''), 'size="31"') . '&nbsp;' . tep_image(DIR_WS_ICONS . 'icon_05.gif', '', '14', '15', 'onMouseover="ddrivetip(\''.TEXT_HELP_SUBURB.'\', \'\', 200);" onMouseout="hideddrivetip();"')?>
				                    			<noscript><?='<br>' . TEXT_HELP_SUBURB?></noscript></p>
				                    		</td>
				                		</tr>
				                		<tr>
				                    		<td colspan="2" background="<?=DIR_WS_IMAGES . 'space.gif'?>" height="8"></td>
				                		</tr>
				                		<tr>
				                    		<td><p><?=TEXT_POSTCODE?></p></td>
				                    		<td>
				                    			<p><?=tep_draw_input_field('postcode', (isset($form_values_arr['postcode']) && $form_values_arr['postcode'] ? $form_values_arr['postcode'] : ''), 'size="31"') . '&nbsp;' . tep_image(DIR_WS_ICONS . 'icon_05.gif', '', '14', '15', 'onMouseover="ddrivetip(\''.TEXT_HELP_POSTCODE.'\', \'\', 200);" onMouseout="hideddrivetip();"')?>
				                    			<noscript><?='<br>' . TEXT_HELP_POSTCODE?></noscript></p>
				                    		</td>
				                		</tr>
				                		<tr>
				                    		<td colspan="2" background="<?=DIR_WS_IMAGES . 'space.gif'?>" height="8"></td>
				                		</tr-->*/
				                		?>
				                		<tr>
				                    		<td><p><?=TEXT_CITY?></p></td>
				                    		<td>
				                    			<?=tep_draw_input_field('city', (isset($form_values_arr['city']) && $form_values_arr['city'] ? $form_values_arr['city'] : ''), 'size="31" tabindex=11', true);?>
				                    			<div class="hint"><p><?=TEXT_HELP_CITY?></p></div>
				                    		</td>
				                		</tr>
				                		<tr>
				                    		<td colspan="2" background="<?=DIR_WS_IMAGES . 'space.gif'?>" height="8"></td>
				                		</tr>
				                		<tr>
				                    		<td><p><?=TEXT_COUNTRY?></p></td>
				                    		<td>
				                    			<p>
				                    			    <?=((isset($COUNTRY_CHINESE_ARRAY)) ? $COUNTRY_CHINESE_ARRAY[SIGNUP_DEFAULT_COUNTRY_ID] : tep_get_country_name(SIGNUP_DEFAULT_COUNTRY_ID))?>
				                    			</p>
				                    		</td>
				                		</tr>
				                		<tr>
				                    		<td colspan="2" background="<?=DIR_WS_IMAGES . 'space.gif'?>" height="8"></td>
				                		</tr>
				                		<tr>
				                    		<td><p><?=TEXT_STATE?></p></td>
				                    		<td>
				                    			
<?		if (count($states_array) > 1) {
		   	echo tep_draw_pull_down_menu('state', $states_array, (isset($form_values_arr['state']) ? (int)$form_values_arr['state'] : ''), 'id="state" tabindex=12');
		} else {
			echo tep_draw_input_field('state', (isset($form_values_arr['state']) && $form_values_arr['state'] ? $form_values_arr['state'] : ''), 'size="31" id="state" tabindex=12');
		}
?>
												
												<?='&nbsp;' . TEXT_FIELD_REQUIRED;?>
												<div class="hint"><p><?=TEXT_HELP_STATE?></p></div>
				                    		</td>
				                		</tr>
						                <tr>
						                    <td colspan="2" background="<?=DIR_WS_IMAGES . 'space.gif'?>" height="8"></td>
						                </tr>
						                <tr>
						                    <td colspan="2" background="<?=DIR_WS_IMAGES . 'space.gif'?>" height="8"></td>
						                </tr>
						                <tr>
						                    <td><p><?=TEXT_CONTACT_NO?></p></td>
						                    <td>
						                    	<?=tep_draw_input_field('contact_area_no', (isset($form_values_arr['contact_area_no']) && $form_values_arr['contact_area_no'] ? $form_values_arr['contact_area_no'] : ''), 'size="4" maxlength="4" id="area_code" tabindex=13 onchange="add_area_code(this.id)"', false) . '&nbsp;&nbsp;' . tep_draw_input_field('contact_phone_no', (isset($form_values_arr['contact_phone_no']) && $form_values_arr['contact_phone_no'] ? $form_values_arr['contact_phone_no'] : ''), 'size="8" maxlength="8" tabindex=14', true);?>
						                    	<div class="hint"><p><?=TEXT_HELP_CONTACT_NO?></p></div>
						                    </td>
						                </tr>
				                		<tr>
				                    		<td colspan="2" background="<?=DIR_WS_IMAGES . 'space.gif'?>" height="8"></td>
				                		</tr>
				                		<tr>
				                    		<td><p><?=TEXT_MOBILE_NO?></p></td>
				                    		<td>
				                    			<?=tep_draw_input_field('mobile_no', (isset($form_values_arr['mobile_no']) ? $form_values_arr['mobile_no'] : ''), 'size="31" maxlength="11" tabindex=15', true);?>
			                    				<div class="hint"><p><?=TEXT_HELP_MOBILE_NO?></p></div>
				                    		</td>
				                		</tr>
				                		<tr>
				                    		<td colspan="2" background="<?=DIR_WS_IMAGES . 'space.gif'?>" height="8"></td>
				                		</tr>
				                		<tr>
						                    <td><p><?=TEXT_QQ_NO?></p></td>
						                    <td>
						                    	<?=tep_draw_input_field('qq_no', (isset($form_values_arr['qq_no']) && $form_values_arr['qq_no'] ? $form_values_arr['qq_no'] : ''), 'size="31" tabindex=16', true);?>
					                    		<div class="hint"><p><?=TEXT_HELP_QQ?></p></div>
						                    </td>
						                </tr>
				                		<tr>
				                    		<td colspan="2" background="<?=DIR_WS_IMAGES . 'space.gif'?>" height="8"></td>
				                		</tr>
				                		<tr>
						                    <td><p><?=TEXT_MSN_ADDRESS?></p></td>
						                    <td>
						                    	<?=tep_draw_input_field('msn_address', (isset($form_values_arr['msn_address']) && $form_values_arr['msn_address'] ? $form_values_arr['msn_address'] : ''), 'size="31" tabindex=17') . '&nbsp;<span class="instruction">' . TEXT_OPTIONAL . '</span>';?>
						                    	<div class="hint"><p><?=TEXT_HELP_MSN?></p></div>
						                    </td>
						                </tr>
						                <tr>
				                    		<td colspan="2" background="<?=DIR_WS_IMAGES . 'space.gif'?>" height="8"></td>
				                		</tr>
				                		<tr>
						                    <td><p><?=TEXT_YAHOO_ADDRESS?></p></td>
						                    <td>
						                    	<?=tep_draw_input_field('yahoo_address', (isset($form_values_arr['yahoo_address']) && $form_values_arr['yahoo_address'] ? $form_values_arr['yahoo_address'] : ''), 'size="31" tabindex=18') . '&nbsp;<span class="instruction">' . TEXT_OPTIONAL . '</span>';?>
						                    	<div class="hint"><p><?=TEXT_HELP_YAHOO?></p></div>
						                    </td>
						                </tr>
						                <tr>
				                    		<td colspan="2" background="<?=DIR_WS_IMAGES . 'space.gif'?>" height="8"></td>
				                		</tr>
				                		<tr>
						                    <td><p><?=TEXT_ICQ_NO?></p></td>
						                    <td>
						                    	<?=tep_draw_input_field('icq_no', (isset($form_values_arr['icq_no']) && $form_values_arr['icq_no'] ? $form_values_arr['icq_no'] : ''), 'size="31" tabindex=19') . '&nbsp;<span class="instruction">' . TEXT_OPTIONAL . '</span>';?>
						                    	<div class="hint"><p><?=TEXT_HELP_ICQ?></p></div>
						                    </td>
						                </tr>
						               	<tr>
				                    		<td colspan="2" background="<?=DIR_WS_IMAGES . 'space.gif'?>" height="8"></td>
				                		</tr>
				                		<?
				                		/*
				                		<!--tr>
						                    <td><p><?=TEXT_APPLY_VIP_STATUS?></p></td>
						                    <td>
						                    	<p><?=tep_draw_checkbox_field('apply_vip', 1, (isset($form_values_arr['apply_vip']) && $form_values_arr['apply_vip']== '1' ? true : false )).tep_image(DIR_WS_ICONS . 'icon_05.gif', '', '14', '15', 'onMouseover="ddrivetip(\''.TEXT_HELP_VIP_STATUS.'\', \'\', 200);" onMouseout="hideddrivetip();"')?>
						                    	<noscript><?='<br>' . TEXT_HELP_VIP_STATUS?></noscript></p>
						                    </td>
						                </tr-->
				                		<!--tr>
				                    		<td colspan="2" background="<?=DIR_WS_IMAGES . 'space.gif'?>" height="8"></td>
				                		</tr>
						                <tr>
						                    <td><p><?=TEXT_FAX_NO?></p></td>
						                    <td>
						                    	<p><?=tep_draw_input_field('fax_no', (isset($form_values_arr['fax_no']) && $form_values_arr['fax_no'] ? $form_values_arr['fax_no'] : ''), 'size="31"') . '&nbsp;' . tep_image(DIR_WS_ICONS . 'icon_05.gif', '', '14', '15', 'onMouseover="ddrivetip(\''.TEXT_HELP_FAX_NO.'\', \'\', 200);" onMouseout="hideddrivetip();"')?>
						                    	<noscript><?='<br>' . TEXT_HELP_FAX_NO?></noscript></p>
						                    </td>
						                </tr>
				                		<tr>
				                    		<td colspan="2" background="<?=DIR_WS_IMAGES . 'space.gif'?>" height="8"></td>
				                		</tr>
				                		<tr>
				                    		<td colspan="2" background="<?=DIR_WS_IMAGES . 'space_line2.gif'?>" height="1"></td>
				                		</tr>
				                		<tr>
				                    		<td colspan="2" background="<?=DIR_WS_IMAGES . 'space.gif'?>" height="8"></td>
				                		</tr>
				                		<tr>
				                    		<td colspan="2"><p><?=tep_image(DIR_WS_ICONS . 'icon_03.gif') . '&nbsp;' . TEXT_PREFERENCES?></p></td>
				                		</tr>
				                		<tr>
				                    		<td colspan="2" background="<?=DIR_WS_IMAGES . 'space.gif'?>" height="8"></td>
				                		</tr>
				                		<tr>
				                    		<td colspan="2">
				                    			
				                    		</td>
				                		</tr>
				                		<tr>
				                    		<td><p><?=TEXT_TIME_ZONE?></p></td>
				                    		<td>
				                    			<p><?=tep_draw_pull_down_menu(KEY_SP_TIME_ZONE, $time_zone_array, (isset($form_values_arr[KEY_SP_TIME_ZONE]) ? $form_values_arr[KEY_SP_TIME_ZONE] : '')). '&nbsp;' . TEXT_FIELD_REQUIRED .'&nbsp;' . tep_image(DIR_WS_ICONS . 'icon_05.gif', '', '14', '15', 'onMouseover="ddrivetip(\''.TEXT_HELP_TIME_ZONE.'\', \'\', 200);" onMouseout="hideddrivetip();"')?>
				                    			<noscript><?='<br>' . TEXT_HELP_TIME_ZONE?></noscript></p>
				                    		</td>
				                		</tr>
				                		<tr>
				                    		<td colspan="2" background="<?=DIR_WS_IMAGES . 'space.gif'?>" height="8"></td>
				                		</tr>
				                		<tr>
				                    		<td><p><?=TEXT_LANGUAGE?></p></td>
				                    		<td><p><?=tep_draw_pull_down_menu(KEY_SP_LANG, $pref_language_array, (isset($form_values_arr[KEY_SP_LANG]) ? $form_values_arr[KEY_SP_LANG] : ''), ''). '&nbsp;' . TEXT_FIELD_REQUIRED .'&nbsp;' . tep_image(DIR_WS_ICONS . 'icon_05.gif', '', '14', '15', 'onMouseover="ddrivetip(\''.TEXT_HELP_LANGUAGE.'\', \'\', 200);" onMouseout="hideddrivetip();"')?>
				                    		<noscript><?='<br>' . TEXT_HELP_LANGUAGE?></noscript></p></td>
				                		</tr-->
				                		*/
				                		?>
				                		<tr>
				                    		<td colspan="2" align="right"><p><?=tep_image_submit(DIR_WS_IMAGES.'button_next.gif', TEXT_NEXT, 'tabindex=20')?></p></td>
				                		</tr>
									</table>
									</form>
									<!--END FIRST TAB -->
<?
	} else {
		echo tep_draw_form('agreement_form', FILENAME_SUPPLIER_SIGNUP, '', 'post', '');
		
		echo '	<table border="0" cellpadding="0" cellspacing="2" width="100%">
					<tr>
						<td class="font12Info">'.TEXT_SIGNUP_AGREEMENT.'</td>
					</tr>
					<tr>
						<td align="center"><br>'.
							tep_image_submit(BUTTON_AGREE, ALT_BUTTON_AGREE, 'name="btn_signup_agreement"') . '&nbsp;&nbsp;' .
							tep_button(BUTTON_DISAGREE, ALT_BUTTON_DISAGREE, tep_href_link(FILENAME_DEFAULT), '', 'inputButton') . '
						</td>
					</tr>
				</table>
			</form>';
	}
} else if ($current_step == 1) {
	$custumers_security_obj = new customers_security($_SESSION['sup_languages_id']);
	$security_question_html = $custumers_security_obj->display_customer_security_form('new', $form_values_arr);
	$options_array = $custumers_security_obj->get_customer_security_questions_list();	
?>
	<script type="text/javascript">
		var question_array = new Array();
		var question_id_array = new Array();
<?	
		echo 'question_array[0] = new Array();'."\n";
		echo 'question_array[0]["id"]= "0";'."\n";
		echo 'question_array[0]["text"]= "'.TEXT_SELECT_SECURITY_QUESTION.'";'."\n";
		for ($question_cnt = 0; $question_cnt < count($options_array); $question_cnt++) {
			echo 'question_array['.($question_cnt+1).'] = new Array();'."\n";
			echo 'question_array['.($question_cnt+1).']["id"]= "'.$options_array[$question_cnt]['id'].'";'."\n";
			echo 'question_array['.($question_cnt+1).']["text"]= "'. unicode_to_utf8(entities_to_unicode($options_array[$question_cnt]['text'])).'";'."\n";
		}
?>	

	function check_avilable_question() {
		
		selected_question_array = new Array();
		array_counter = 1;
		for (var quest = 1; quest <= 3; quest++) {
			if (typeof(document.getElementById('question_'+quest)) != "undefined") {
				selected_question_array[array_counter++] = document.getElementById('question_'+quest).value;
			}
		}

		for (var selectbox = 1; selectbox <= 3; selectbox++) {
			var select_name = 'question_'+selectbox;
			var select_box = document.getElementById(select_name);
			
			//empty all drop down box.
			for (var q=(select_box.options.length-1); q>=0; q--) {
				select_box.remove(q);
			}
			
			//rebuild drop down
			for (var option_cnt=0; option_cnt < question_array.length; option_cnt++) {
				select_box.options[option_cnt] = new Option(question_array[option_cnt]["text"], question_array[option_cnt]["id"]);
				//set default value
				if (selected_question_array[selectbox] == 0) {
					select_box.options[0].selected=true;
				} else if (selected_question_array[selectbox] == question_array[option_cnt]["id"]) {
					select_box.options[option_cnt].selected=true;
				}
			}
			
			//remove selected question
			for (var s_ques=1; s_ques<=3; s_ques++) {
				if (selectbox != s_ques && selected_question_array[s_ques] != 0) {
					//alert('select_box:'+selectbox+' selected Q:'+s_ques+ 'Question'+ select_box.options[selected_question_array[s_ques]].text);
					//find the position of the value at drop down box and remove it.
					for (var new_quest=0; new_quest<select_box.options.length; new_quest++) {
						if (select_box.options[new_quest].value == selected_question_array[s_ques]) {
							select_box.remove(new_quest);
						}
					}
				}
			}
		}		
	}
	</script>
<?
	echo $security_question_html;
} else if ($current_step == 2) {
?>
									<!--START SECOND TAB -->
									<!--START ADD NEW PAYMENT METHOD -->
									<?=tep_draw_form('add_payment_method', FILENAME_SUPPLIER_SIGNUP, tep_get_all_get_params($get_params_always_exclude_array) . "current_step=$next_step", 'post', '') . tep_draw_hidden_field('action', 'add_payment_method')?>
									<table border="0" cellpadding="0" cellspacing="2" width="100%">
				                		<tr>
				                    		<td><p><?=TEXT_ADD_PAYMENT_METHOD?></p></td>
				                    		<td>
				                    			<?=tep_draw_pull_down_menu('payment_method', $payment_method_array, '', 'id="payment_method" onchange="if(this.value != \'\') { this.form.submit(); }"', true) . '&nbsp;<NOSCRIPT>&nbsp;' . tep_image_submit('', TEXT_GO) . '</NOSCRIPT>'?>
				                    			<div class="hint"><p><?=TEXT_HELP_PAYMENT_METHOD?></p></div>
				                    		</td>
				                		</tr>
				                		<tr>
				                    		<td colspan="2" background="<?=DIR_WS_IMAGES . 'space.gif'?>" height="8"></td>
				                		</tr>
									</table>
									</form>
									<!--END ADD NEW PAYMENT METHOD -->
									
									<?=tep_draw_form('process2', FILENAME_SUPPLIER_SIGNUP, tep_get_all_get_params($get_params_always_exclude_array) . "current_step=$next_step", 'post', 'autocomplete="off"') . tep_draw_hidden_field('action', 'process')?>
									<table border="0" cellpadding="0" cellspacing="2" width="100%">
<?
	if (isset($form_values_arr['payment_method'])) {
		$cnt = 0;
		foreach ($form_values_arr['payment_method'] as $rownum => $row_payment_method_array) {
			$cnt++;
			foreach ($row_payment_method_array as $row_payment_method_id => $row_payment_method_field_array) {
				echo '         			<tr>
				                    		<td colspan="2" background="' . DIR_WS_IMAGES . 'space.gif' . '" height="8"></td>
				                		</tr>
				                		<tr>
				                    		<td colspan="2" background="' . DIR_WS_IMAGES . 'space_line2.gif' . '" height="1"></td>
				                		</tr>
				                		<tr>
				                    		<td colspan="2" background="' . DIR_WS_IMAGES . 'space.gif' . '" height="8"></td>
				                		</tr>
				                		<tr>
				                    		<td><p>' . $cnt . '.&nbsp; ' . $payment_method_master_array[$row_payment_method_id] . '</p></td>
				                    	<!--	<td align="left"><p><a class="SystemNav" href="' . tep_href_link(FILENAME_SUPPLIER_SIGNUP, tep_get_all_get_params($get_params_always_exclude_array) . "remove_row_num=$rownum&current_step=$next_step&action=remove_payment_method").'">'.TEXT_REMOVE . '</a>&nbsp;' . tep_image(DIR_WS_ICONS . 'icon_05.gif', '', '14', '15', 'onMouseover="ddrivetip(\''.TEXT_HELP_REMOVE_PAYMENT_METHOD.'\', \'\', 200);" onMouseout="hideddrivetip();"') . '&nbsp;<NOSCRIPT><br>' . TEXT_HELP_REMOVE_PAYMENT_METHOD . '</NOSCRIPT></p></td> //-->
				                    		<td align="right"><p><a class="SystemNav" href="' . tep_href_link(FILENAME_SUPPLIER_SIGNUP, tep_get_all_get_params($get_params_always_exclude_array) . "remove_row_num=$rownum&current_step=$next_step&action=remove_payment_method").'"><span class="redIndicator"><b>[ X ]'.TEXT_REMOVE . '<b></span></a>&nbsp;' . tep_image(DIR_WS_ICONS . 'icon_05.gif', '', '14', '15', 'onMouseover="ddrivetip(\''.TEXT_HELP_REMOVE_PAYMENT_METHOD.'\', \'\', 200);" onMouseout="hideddrivetip();"') . '&nbsp;<NOSCRIPT><br>' . TEXT_HELP_REMOVE_PAYMENT_METHOD . '</NOSCRIPT></p></td>
				                		</tr>';
				
				//Loop through all the fields configured for this payment method in db. Then grab values from session.
				$payment_methods_fields_array = tep_get_payment_methods_fields($row_payment_method_id);
				foreach ($payment_methods_fields_array as $payment_methods_fields_id => $payment_methods_fields_array) {
					$fields_options_array = array();
				    $field_name = "payment_method[$rownum][$row_payment_method_id][{$payment_methods_fields_array['payment_methods_fields_id']}]";
				    $field_value = isset($row_payment_method_field_array[$payment_methods_fields_array['payment_methods_fields_id']]) ? $row_payment_method_field_array[$payment_methods_fields_array['payment_methods_fields_id']] : '';
					
					$field_size_arr = explode(',', $payment_methods_fields_array["payment_methods_fields_size"]);
					$field_html = '';
					
					switch ((int)$payment_methods_fields_array['payment_methods_fields_type']) {
						case 1: // Text Box
							list($size, $max_size) = explode(',', $payment_methods_fields_array["payment_methods_fields_size"]);
							$field_html = tep_draw_input_field($field_name, $field_value, "size='$size' maxlength='$max_size'", (int)$payment_methods_fields_array['payment_methods_fields_required']);
							
							break;
						case 2: // Text Area
							$field_html = tep_draw_textarea_field($field_name, true, 25, 3, $field_value) . ((int)$payment_methods_fields_array['payment_methods_fields_required'] ? TEXT_FIELD_REQUIRED : '');
							
							break;
						case 3: // Dropdown Menu
							$selection_array = array();
							if (tep_not_null($payment_methods_fields_array["payment_methods_fields_option"])) {
								$selection_list = explode(':~:', $payment_methods_fields_array["payment_methods_fields_option"]);
								foreach ($selection_list as $val) {
									$selection_array[] = array('id' => tep_db_input($val), 'text' => $val);
								}
							}
							
							$param = ' id="'.$field_id.'" ';
							
							$field_html = tep_draw_pull_down_menu($field_name, $selection_array, '', $param) . ($payment_methods_fields_array['payment_methods_fields_required'] ? TEXT_FIELD_REQUIRED : '');
							
							break;
						case 4: // Radio Button
							$field_html = '';
							
							$selection_list = explode(':~:', $payment_methods_fields_array["payment_methods_fields_option"]);
							foreach ($selection_list as $val) {
								$field_html .= tep_draw_radio_field($field_name, tep_db_input($val), ($val == $default ? true : false), '') . '&nbsp;' . $val . '<br>';
							}
							
							if ($payment_methods_fields_array['payment_methods_fields_required'])	$field_html .= TEXT_FIELD_REQUIRED;
							
							break;
						case "5":	// Date Selection
							$field_html = '';
							if (tep_not_null($payment_methods_fields_array["payment_methods_fields_size"])) {
								list($from_date, $period) = explode(',', $payment_methods_fields_array["payment_methods_fields_size"]);
								if ($from_date == 'TODAY') {
									$from_date = date('Y-m-d');
								}
								$field_html = tep_draw_date_box($field_name, $from_date, $period, $default) . ($payment_methods_fields_array['payment_methods_fields_required'] ? TEXT_FIELD_REQUIRED : '');
							}
							
							break;
						case "7":
							$payment_methods_fields_array["payment_methods_fields_option"] = str_replace(':~:', "\r\n", $payment_methods_fields_array["payment_methods_fields_option"]);
							
							$hidden_value = tep_draw_hidden_field($field_name, $payment_methods_fields_array["payment_methods_fields_option"]);	// Use hidden field to pass info of this type. need it to be saved in database for shown in "Edit Order" page as well.
							
							$field_html = $payment_methods_fields_array["payment_methods_fields_option"] . $hidden_value;
							
							break;
					}
					
					echo '      		<tr valign="top">
											<td width="200" align="left"><p>'.$payment_methods_fields_array['payment_methods_fields_title'].'</p></td>
											<td><p>' . $payment_methods_fields_array['payment_methods_fields_pre_info'] . $field_html . '&nbsp;' . $payment_methods_fields_array['payment_methods_fields_post_info'] . '</p></td>
										</tr>';
				}
			}
		}
	}
?>
				                		<tr>
				                    		<td colspan="2" background="<?=DIR_WS_IMAGES . 'space.gif'?>" height="8"></td>
				                		</tr>
				                		<tr>
				                    		<td colspan="2" background="<?=DIR_WS_IMAGES . 'space_line2.gif'?>" height="1"></td>
				                		</tr>
				                		<tr>
				                    		<td colspan="2" background="<?=DIR_WS_IMAGES . 'space.gif'?>" height="8"></td>
				                		</tr>
				                		<tr>
				                    		<td colspan="2" align="right">
				                    			<p><?=tep_image_submit(DIR_WS_IMAGES.'button_next.gif', TEXT_FINISHED, 'SSL')?></p>
				                    		</td>
				                		</tr>
									</table>
									</form>
									<p>&nbsp;</p>
									<!--END SECOND TAB -->
<?
}
?>
			              			<!-- End Tab content-->
			              		</td>
			              		<td width="3" background="<?=DIR_WS_IMAGES?>box_infomiddle04.gif"><?=tep_image(DIR_WS_IMAGES . 'space.gif', '', '1', '3')?></td>
			            	</tr>
			            	<tr>
			              		<td width="3" height="3"><?=tep_image(DIR_WS_IMAGES . 'box_infomiddle07.gif', '', '3', '3')?></td>
			              		<td height="3" background="<?=DIR_WS_IMAGES?>box_infomiddle06.gif"><?=tep_image(DIR_WS_IMAGES . 'space.gif', '', '1', '3')?></td>
			              		<td width="3" height="3"><?=tep_image(DIR_WS_IMAGES . 'box_infomiddle05.gif', '', '3', '3')?></td>
			            	</tr>
			          	</table>
					</td>
				</tr>
			</table>
		</td>
	</tr>
    <tr>
      <td align="left"><?=tep_image(DIR_WS_IMAGES . 'space.gif', '', '1', '3')?></td>
    </tr>
</table>