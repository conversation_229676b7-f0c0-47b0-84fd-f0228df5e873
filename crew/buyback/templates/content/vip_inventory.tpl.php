<table border="0" width="100%" cellspacing="0" cellpadding="0">
	<tr>
		<td width="3" height="40"><?=tep_image(DIR_WS_IMAGES . 'box_side01.gif', '', '3', '40')?></td>
		<td background="<?=DIR_WS_IMAGES . 'box_side02.gif'?>">
			<table width="100%" border="0" cellpadding="0" cellspacing="2">
				<tbody>
				<tr>
					<td><?=tep_image(DIR_WS_ICONS . 'icon_03.gif', '', '10', '10', ' hspace="4"') . '&nbsp;<span class="title-text">' . HEADER_TITLE_MY_VIP_INVENTORY_UPDATE . '</span>' ?></td>
				</tr>
				</tbody>
			</table>
		</td>
		<td width="3" height="40"><?=tep_image(DIR_WS_IMAGES . 'box_side03.gif', '', '3', '40')?></td>
	</tr>
	<tr>
		<td width="3" background="<?=DIR_WS_IMAGES . 'box_side08.gif'?>"><?=tep_image(DIR_WS_IMAGES . 'space.gif', '', '3', '1')?></td>
		<td>
			<table border="0" cellpadding="2" cellspacing="2" width="100%">
				<tbody>
				<tr>
					<?=tep_draw_form('load_server', FILENAME_MY_VIP_INVENTORY_UPDATE)?>
					<td width="10%"><p><?=TEXT_SELECT_GAME?></p></td>
					<td width="90%"><?=tep_draw_pull_down_menu('cID', $active_game_array, $cID, 'onChange="this.form.submit();"')?></td>
					</form>
				</tr>
<?	if ($cID) { ?>
				<tr>
					<td width="10%"><p><?=TEXT_WORKING_DAY?></p></td>
					<td width="90%"><p>
					<?
						$workday_text_array = array();
						for ($work_cnt=0; $work_cnt < count($workingdays_array); $work_cnt++) {
							$work_day = $workingdays_array[$work_cnt];
							
							$workday_text_array[] = (defined('TEXT_DAY_'.$work_day) ? constant('TEXT_DAY_'.$work_day) : $work_day);
						}
						
						echo implode(', ', $workday_text_array);
					?>
					</p></td>
					
				</tr>
				<tr>
					<td width="10%"><p><?=TEXT_WORKING_TIME?></p></td>
					<td width="90%"><p>
					<?					
						echo implode(' - ', $workingtime_array);
					?>
					</p></td>
					
				</tr>
<?	} ?>
				</tbody>
			</table>
		</td>
		<td width="3" background="<?=DIR_WS_IMAGES . 'box_side04.gif'?>"><?=tep_image(DIR_WS_IMAGES . 'space.gif', '', '3', '1')?></td>
	</tr>
	<tr>
    	<td colspan="3" background="<?=DIR_WS_IMAGES . 'space_line2.gif'?>" height="1"><?=tep_image(DIR_WS_IMAGES . 'space.gif', '', '1', '1')?></td>
    </tr>
<?	if ($cID) { ?>    
    <tr>
    	<td width="3" background="<?=DIR_WS_IMAGES . 'box_side08.gif'?>">&nbsp;</td>
    	<td>
    		<?=tep_draw_form('batch_update_form', '', '', 'post')?>
    		<div class="batchActionBox">
    			<div style="float: right;"><?='<a href="javascript:;" onClick="showHideBox(\'batch_update_div\', \'batch_action_img\')">'.tep_image(DIR_WS_ICONS.'icon_collapse.gif', '', '', '', 'id="batch_action_img"').'<a>'?></div>
    			<div><?=tep_image(DIR_WS_ICONS . 'icon_03.gif', '', '10', '10', 'hspace="4"').SECTION_HEADING_BATCH_UPDATE?></div>
    			<div id="batch_update_div" class="show">
    				<table width="100%" border="0" cellpadding="0" cellspacing="2">
    					<tr>
    						<td width="50%" class="title-text"><p><?=TABLE_HEADER_SERVER_NAME?></p></td>
    						<td width="20%" class="title-text"><p><?=TABLE_HEADER_SERVER_QUANTITY?></p></td>
							<td width="20%" class="title-text"><p><?=TABLE_HEADER_SERVER_MIN_QTY_TO_SELL?></p></td>
    						<td width="10%" class="title-text"><p><?=TABLE_HEADER_SERVER_DELETE?></p></td>
    					</tr>
    					<tr>
    						<td width="50%"><p><?=TEXT_SELECTED_SERVERS?></p></td>
    						<td width="20%"><?=tep_draw_input_field("txtBatchInvQty", '', 'size="5" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) ) { this.value = \'\'; }" onKeyPress="return noEnterKey(event)"')?></td>
    						<td width="20%"><?=tep_draw_input_field("txtBatchMinQty", '', 'size="5" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) ) { this.value = \'\'; }" onKeyPress="return noEnterKey(event)"')?></td>
    						<td width="10%"><?=tep_draw_checkbox_field("chkBatchDelete")?></td>
    					</tr>
    					<tr>
    						<td align="right" colspan="4"><?=tep_button(BUTTON_BATCH_UPDATE, ALT_BUTTON_BATCH_UPDATE, '', 'onClick="apply_batch(\'vip_inventory_form\', \'server_batch\', this);"')?></td>
    					</tr>
    				</table>
    			</div>
    		</div>
    		</form>
    	</td>
    	<td width="3" background="<?=DIR_WS_IMAGES . 'box_side04.gif'?>">&nbsp;</td>
    </tr>
<?	} ?>
	<tr>
		<td width="3" background="<?=DIR_WS_IMAGES . 'box_side08.gif'?>"><?=tep_image(DIR_WS_IMAGES . 'space.gif', '', '3', '1')?></td>
		<td>
<?	if ($cID) {
		echo tep_draw_form('vip_inventory_form', FILENAME_MY_VIP_INVENTORY_UPDATE, tep_get_all_get_params(array('action'))."action=batch_action", 'post', 'enctype="multipart/form-data"');
		echo tep_draw_hidden_field('cID', $cID);
		echo tep_draw_hidden_field("serialized_export_csv_array", tep_array_serialize($export_csv_array));
?>
    		<table border="0" width="100%" height="40" cellspacing="0" cellpadding="0">
    			<tr>
    				<td colspan="7"><p><span class="redIndicator"><?=NOTE_SELECT_SERVER?></span></p></td>
    			</tr>
    			<tr>
    				<td height="40" align="right" width="5"><?=tep_image(DIR_WS_IMAGES . 'box_grey02a.gif', '', '5', '40')?></td>
    				<td width="1%" background="<?=DIR_WS_IMAGES?>box_grey02b.gif"><?=tep_draw_checkbox_field('select_all', '', false, '', 'id="select_all" onclick="javascript:void(setCheckboxes(\'vip_inventory_form\',\'select_all\',\'server_batch\'));"')?></td>
    				<td class="title-text" width="50%" background="<?=DIR_WS_IMAGES?>box_grey02b.gif"><p><?=TABLE_HEADER_SERVER_NAME?></p></td>
    				<td align="center" class="title-text" width="20%" background="<?=DIR_WS_IMAGES?>box_grey02b.gif"><p><?=TABLE_HEADER_SERVER_QUANTITY?></p></td>
    				<td align="center" class="title-text" width="20%" background="<?=DIR_WS_IMAGES?>box_grey02b.gif"><p><?=TABLE_HEADER_SERVER_MIN_QTY_TO_SELL?></p></td>
    				<td class="title-text" width="10%" background="<?=DIR_WS_IMAGES?>box_grey02b.gif"><p><?=TABLE_HEADER_SERVER_DELETE . '<br>' . tep_draw_checkbox_field('delete_all', '', false, '', 'id="delete_all" onclick="javascript:void(setCheckboxes(\'vip_inventory_form\',\'delete_all\',\'delete_batch\'));"')?></p></td>
    				<td height="40" width="5"><?=tep_image(DIR_WS_IMAGES . 'box_grey02c.gif', '', '5', '40')?></td>
    			</tr>
    			<tr>
    				<td colspan="7" class="listingBody">
    					<div style=" width:100%; height:400px; overflow:auto;">
	    					<table border="0" width="100%" height="50px" cellspacing="0" cellpadding="0">
	    						<tbody><?=$server_html?></tbody>
	    					</table>
    					</div>
    				</td>
				</tr>
				<tr>
					<td colspan="7" class="ordersBoxHeading">
						<table border="0" width="100%" cellspacing="0" cellpadding="5">
							<tr>
								<td colspan="2" align="right"><?=tep_submit_button(BUTTON_SUBMIT, ALT_BUTTON_SUBMIT, 'name="btn_inv_update"', 'inputButton')?></td>
							</tr>
							<tr>
								<td align="left">
									<?=tep_draw_file_field('csv_import', 'size="50"').tep_submit_button(BUTTON_IMPORT, ALT_BUTTON_IMPORT, 'name="btn_csv_import"', 'inputButton')?>
								</td>
			    				<td align="right">
			    					<?=isset($HTTP_POST_VARS['btn_csv_import']) ? '' : tep_submit_button(BUTTON_EXPORT, ALT_BUTTON_EXPORT, 'name="btn_csv_export"', 'inputButton')?>
			    				</td>
			    			</tr>
			    		</table>
			    	</td>
    			</tr>
    		</table>
    	</form>
<?	} ?>
    	</td>
    	<td width="3" background="<?=DIR_WS_IMAGES . 'box_side04.gif'?>"><?=tep_image(DIR_WS_IMAGES . 'space.gif', '', '3', '1')?></td>
    </tr>
    <tr>
		<td width="3" height="16"><?=tep_image(DIR_WS_IMAGES . 'box_side07.gif', '', '3', '16')?></td>
		<td background="<?=DIR_WS_IMAGES . 'box_side06.gif'?>"><?=tep_image(DIR_WS_IMAGES . 'space.gif', '', '', '16')?></td>
		<td width="3" height="16"><?=tep_image(DIR_WS_IMAGES . 'box_side05.gif', '', '3', '16')?></td>
	</tr>
</table>
<script language="javascript">
<!--
	function showHideBox(box_id, toggle_id) {
	    var box_obj = DOMCall(box_id);
	    var toggle_obj = DOMCall(toggle_id);
	    
	    if (box_obj != null) {
	    	if (box_obj.className == 'hide') {
		        box_obj.className = 'show';
		        toggle_obj.src = '<?=DIR_WS_ICONS . 'icon_collapse.gif'?>';
		    } else {
		        box_obj.className = 'hide';
		        toggle_obj.src = '<?=DIR_WS_ICONS . 'icon_expand.gif'?>';
		    }
	    }
	}
	
	function apply_batch(the_form, check_item, btnBatchObj) {
		var frm = btnBatchObj.form;
		var inv_qty = frm.txtBatchInvQty.value;
		var min_qty = frm.txtBatchMinQty.value;
		if (trim_str(inv_qty)=='' || (trim_str(inv_qty) != '' && !validateInteger(trim_str(inv_qty)))) { inv_qty = ''; }
		
		if (trim_str(min_qty)=='' || (trim_str(min_qty) != '' && !validateInteger(trim_str(min_qty)))) { min_qty = ''; }
		
		var is_delete = frm.chkBatchDelete.checked;
		
		var elts      = (typeof(document.forms[the_form].elements[check_item+'[]']) != 'undefined')
						? document.forms[the_form].elements[check_item+'[]']
						: "";
		var elts_cnt  = (typeof(elts.length) != 'undefined')
						? elts.length
						: 0;
		
		if (elts_cnt) {
	    	for (var i = 0; i < elts_cnt; i++) {
	    		if (elts[i].checked) {
	    			if (document.forms[the_form].elements['inv_qty['+elts[i].value+']'] != null) {
						document.forms[the_form].elements['inv_qty['+elts[i].value+']'].value = inv_qty;
					}
					
					if (document.forms[the_form].elements['min_qty['+elts[i].value+']'] != null) {
						document.forms[the_form].elements['min_qty['+elts[i].value+']'].value = min_qty;
					}
					
					if (document.forms[the_form].elements['delete_batch[]'][i] != null) {
						document.forms[the_form].elements['delete_batch[]'][i].checked = is_delete;
					}
	    		}
	    	} // end for
		} else if (elts!='') {
	       	elts.checked = do_check;
		} // end if... else
		
		return;
	}
//-->
</script>