<link type="text/css" rel="styleSheet" HREF="<?=DIR_WS_INCLUDES?>stylesheet.css">
<script language="javascript" src="<?=DIR_WS_JAVASCRIPT?>general.js"></script>
<script language="javascript" src="<?=DIR_WS_JAVASCRIPT?>jquery.js"></script>
<script language="javascript" src="<?=DIR_WS_JAVASCRIPT?>ogm_jquery.js"></script>
<script language="javascript" src="<?=DIR_WS_JAVASCRIPT?>ajaxfileupload.js"></script>

<script type="text/javascript">
<!--
	var divhtml = '';
	var countdown_secs_default = 60;
	var countdown_secs = 0;
	var coundown_active = false;
	var lockdown_form_secs = 2;
	var SID = '<?=SID?>';
	var languages_id = '<?=$languages_id?>';
	var history_timerID = 0;
	var global_loading_message = '<?=TEXT_LOADING_MESSAGE?>';
	var global_no_result_found = '<?=TEXT_NO_RECORD_FOUND?>';
	
	function upload_ss (req_grp_id) {
		var display_html = '';

		display_html += '<?=tep_upload_draw_form("upload_ss_form", FILENAME_UPLOAD, tep_get_all_get_params(array("action")), "POST", "enctype=\'multipart/form-data\'")?>';
		display_html += '<?=tep_draw_hidden_field("bb_req_grp_id", "'+req_grp_id+'")?>';
		display_html += '<table border="0" width="100%" style="font-size:12px;">';
		display_html += '<tr valign="top"><td colspan="2"><?=TEXT_USER_REQUIREMENT?></td></tr>';
		display_html += '<tr valign="top"><td nowrap><?=TEXT_FIRST_SS_TITLE?></td><td><?=TEXT_FIRST_SS_DESC?></td></tr>';
		display_html += '<tr valign="top"><td nowrap><?=TEXT_SECOND_SS_TITLE?></td><td><?=TEXT_SECOND_SS_DESC?></td></tr>';
		display_html += '<tr valign="top"><td nowrap><?=TEXT_SS_REF_TITLE?></td><td><?=TEXT_SS_REF_DESC?></td></tr>';
		display_html += '<tr valign="top"><td nowrap><?=TEXT_REQUIREMENT_TITLE?></td><td><?=TEXT_REQUIREMENT_DESC?></td></tr>';
		display_html += '<tr valign="top"><td colspan="2"><?=TEXT_MORE_THAN_2_PHOTO?></td></tr>';
		display_html += '<tr valign="top"><td nowrap>&nbsp;</td></tr>';
		display_html += '<tr><td align="right"><?=TEXT_SCREENSHOT_BEFORE_SEND_GOLD?>: </td><td><?=tep_draw_file_field("ss_image_1", "class=\'upload_class\' id=\'ss_image_1\' size=\'30\'", true)?></td></tr>';
		display_html += '<tr><td align="right"><?=TEXT_SCREENSHOT_AFTER_SENT_GOLD?>: </td><td><?=tep_draw_file_field("ss_image_2", "class=\'upload_class\' id=\'ss_image_2\' size=\'30\'", true)?></td></tr>';
		display_html += "</table>";
		display_html += "</form>";
		
		jquery_confirm_box(display_html, 2, 0, '<?=TEXT_UPLOAD_SS?>');
		
		jQuery('#jconfirm_submit').click(function() { 
			ajaxFileUpload(req_grp_id, 'ss_image_1', 'ss_image_2', 'bb_loading', 'processing', display_html);
			jquery_confirm_box(display_html+'<table><tr><td><?=tep_image(DIR_WS_IMAGES."loading.gif", "", "", "", "id=\'bb_loading\' style=\'display:block;\'")?></td></tr></table>', 2, 0, '<?=TEXT_UPLOAD_SS?>');
		});
	}
	
	function ajaxFileUpload (req_grp_id, file_id_1, file_id_2, loading_id, bb_status, display_html)
	{
		
		jQuery.ajaxFileUpload
		(
			{
				url:'vip_xmlhttp.php?action=redirect_upload_to_curl&req_grp_id='+req_grp_id+'&file_name_1='+file_id_1+'&file_name_2='+file_id_2+'&sup_language=<?=$sup_language?>',
				secureuri:false,
				fileClass:'upload_class',
				dataType: 'xml',
				success: function(xml){
			     	jQuery(xml).find('response').each(function(){
						var validation_result = jQuery("validation_result", this).text();
						var error_msg_3 = jQuery("error_msg_3", this).text();
						if (validation_result != 'done' && error_msg_3 == '') {
							var error_msg_1 = jQuery("error_msg_1", this).text();
							var error_msg_2 = jQuery("error_msg_2", this).text();
							
							var error_html = '';
							var display_with_error_html = '';
							
							error_html += "<table border='0' width='100%' style='display:block;color:red;font-size:12px;'>";
							error_html += '<tr><td style="font-weight:bold;"><?=TEXT_ERROR_MSG?>:</td></tr>';
							
							if (error_msg_1 != '') {
								error_msg_1 = get_error_msg(error_msg_1);
								error_html += '<tr><td>[<?=TEXT_SCREENSHOT_BEFORE_SEND_GOLD?>]</td><td>'+error_msg_1+'</td></tr>';	
							}
							if (error_msg_2 != '') {
								error_msg_2 = get_error_msg(error_msg_2);
								error_html += '<tr><td>[<?=TEXT_SCREENSHOT_AFTER_SENT_GOLD?>]</td><td>'+error_msg_2+'</td></tr>';	
							}
							
							if (error_msg_1 == '' && error_msg_2 == '') {
								var error_msg = '<?=ERROR_UPLOAD_FAILED?>';
								error_html += '<tr><td>&nbsp;</td><td>'+error_msg+'</td></tr>';	
							}
							
							error_html += "</table>";
							
							display_with_error_html = error_html + display_html; 
							jquery_confirm_box(display_with_error_html, 2, 0, '<?=TEXT_UPLOAD_SS?>');
							
							// OK button
							jQuery('#jconfirm_submit').click(function() { 
								ajaxFileUpload(req_grp_id, 'ss_image_1', 'ss_image_2', 'bb_loading', bb_status, display_html);
								jquery_confirm_box(display_html+'<table><tr><td><?=tep_image(DIR_WS_IMAGES."loading.gif", "", "", "", "id=\'bb_loading\' style=\'display:block;\'")?></td></tr></table>', 2, 0, '<?=TEXT_UPLOAD_SS?>');
							});
							
							// Cancel button
							jQuery('#jconfirm_cancel').click(function() { 
								jQuery.unblockUI();	
								
								if (bb_status == 'verifying') {
									var order_status_id = document.getElementById('vipodh_input_order_status_select').value;
									var product_type = document.getElementById('vipodh_input_product_type_select').value;
									var order_no = document.getElementById('vipodh_input_order_no').value;
									var game_cat_id = document.getElementById('vipodh_input_game_select').value;
									var start_date = document.getElementById('vipodh_input_start_date').value;
									var end_date = document.getElementById('vipodh_input_end_date').value;
									search_vip_order_history(order_status_id, product_type, order_no, game_cat_id, start_date, end_date, languages_id, SID);
								}
								
							});
						} else {
							jQuery.unblockUI();
							var order_status_id = document.getElementById('vipodh_input_order_status_select').value;
							var product_type = document.getElementById('vipodh_input_product_type_select').value;
							var order_no = document.getElementById('vipodh_input_order_no').value;
							var game_cat_id = document.getElementById('vipodh_input_game_select').value;
							var start_date = document.getElementById('vipodh_input_start_date').value;
							var end_date = document.getElementById('vipodh_input_end_date').value;
							search_vip_order_history(order_status_id, product_type, order_no, game_cat_id, start_date, end_date, languages_id, SID);
						}
			      });
			   }
			}
		)
		return false;
	}
	
	function get_error_msg (msg) {
		var result = '';
		switch (msg) {
			case '1':
			case '2':
			case '10':
				result = '<?=ERROR_FILESIZE_EXCEED?>';
			break;
			case '3':
				result = '<?=ERROR_UPLOAD_PARTIAL?>';
			break;
			case '4':
				result = '<?=WARNING_NO_FILE_UPLOADED?>';
			break;
			case '6':
				result = '<?=ERROR_NO_TMP_DIR?>';
			break;
			case '8':
				result = '<?=ERROR_NO_UPLOAD_FILE?>';
			break;
			case '9':
				result = '<?=ERROR_FILETYPE_NOT_ALLOWED?>';
			break;
			default:
				result = '<?=ERROR_DESTINATION_NOT_WRITEABLE?>';
			break;
		}
		return result;
	}
	
	function check_file_ext(path){
		var ext = path.split(".");
		var file_ext = ext[ext.length-1].toLowerCase();
		if(file_ext == "jpg" || file_ext == "jpeg"){
			return true;
		} else {
			return false;
		}
	}
	
	function checking_file(file_id){
		if (typeof(file_id) == 'undefined' || file_id == null) {	
	  		return;
	  	} else {
			if (!check_file_ext(file_id.value)){
				alert ("<?=JS_INVALID_SS_FILE_EXT?>\n");
				file_id.value = '';
			}
		}
	}
	
	function validate_confirmed_qty(req_id, req_grp_id) {
		document.getElementById('odh_button_update_'+req_grp_id).attributes['onclick'].value = '';
		
		stop_coundown_activity();
		var customer_id = '<?=$customer_id?>';

		var sent_qty = document.getElementById('sent_qty_'+req_grp_id).value;
		var req_qty = document.getElementById('request_qty_'+req_grp_id).value;
		var req_id = req_id;
		
		jQuery.post("vip_xmlhttp.php?action=update_buyback_request_group",{
	    req_grp_id: req_grp_id, sent_qty: sent_qty, req_id: req_id, customer_id: customer_id
	 	},function(xml){
		     jQuery(xml).find('response').each(function(){
		        var error_msg = jQuery("error_msg", this).text();
		        var result = jQuery("result", this).text();
		        
		        if (result == 'updated') {
		        	
<?
		        	if (BUYBACK_ENABLE_UPLOAD_SCREENSHOT == 'true') {
?>
			        	var display_html = '';
						display_html += '<?=tep_upload_draw_form("upload_ss_form", FILENAME_UPLOAD, tep_get_all_get_params(array("action")), "POST", "enctype=\'multipart/form-data\'")?>';
						display_html += '<?=tep_draw_hidden_field("bb_req_grp_id", "'+req_grp_id+'")?>';
						display_html += '<table border="0" width="100%" style="font-size:12px;">';
						display_html += '<tr valign="top"><td colspan="2"><?=TEXT_USER_REQUIREMENT?></td></tr>';
						display_html += '<tr valign="top"><td nowrap><?=TEXT_FIRST_SS_TITLE?></td><td><?=TEXT_FIRST_SS_DESC?></td></tr>';
						display_html += '<tr valign="top"><td nowrap><?=TEXT_SECOND_SS_TITLE?></td><td><?=TEXT_SECOND_SS_DESC?></td></tr>';
						display_html += '<tr valign="top"><td nowrap><?=TEXT_SS_REF_TITLE?></td><td><?=TEXT_SS_REF_DESC?></td></tr>';
						display_html += '<tr valign="top"><td nowrap><?=TEXT_REQUIREMENT_TITLE?></td><td><?=TEXT_REQUIREMENT_DESC?></td></tr>';
						display_html += '<tr valign="top"><td colspan="2"><?=TEXT_MORE_THAN_2_PHOTO?></td></tr>';
						display_html += '<tr valign="top"><td nowrap>&nbsp;</td></tr>';
						display_html += '<tr><td align="right"><?=TEXT_SCREENSHOT_BEFORE_SEND_GOLD?>: </td><td><?=tep_draw_file_field("ss_image_1", "class=\'upload_class\' id=\'ss_image_1\' size=\'30\'", true)?></td></tr>';
						display_html += '<tr><td align="right"><?=TEXT_SCREENSHOT_AFTER_SENT_GOLD?>: </td><td><?=tep_draw_file_field("ss_image_2", "class=\'upload_class\' id=\'ss_image_2\' size=\'30\'", true)?></td></tr>';
						display_html += "</table>";
						display_html += "</form>";
						
						jquery_confirm_box(display_html, 2, 0, '<?=TEXT_UPLOAD_SS?>');
						
						jQuery('#jconfirm_submit').click(function() { 
							ajaxFileUpload(req_grp_id, 'ss_image_1', 'ss_image_2', 'bb_loading', 'verifying', display_html);
							jquery_confirm_box(display_html+'<table><tr><td><?=tep_image(DIR_WS_IMAGES."loading.gif", "", "", "", "id=\'bb_loading\' style=\'display:block;\'")?></td></tr></table>', 2, 0, '<?=TEXT_UPLOAD_SS?>');
						});
						
						jQuery('#jconfirm_cancel').click(function() { 
							var order_status_id = document.getElementById('vipodh_input_order_status_select').value;
							var product_type = document.getElementById('vipodh_input_product_type_select').value;
							var order_no = document.getElementById('vipodh_input_order_no').value;
							var game_cat_id = document.getElementById('vipodh_input_game_select').value;
							var start_date = document.getElementById('vipodh_input_start_date').value;
							var end_date = document.getElementById('vipodh_input_end_date').value;
							search_vip_order_history(order_status_id, product_type, order_no, game_cat_id, start_date, end_date, languages_id, SID);
						});
<?
		        	} else {
?>
		        		var order_status_id = document.getElementById('vipodh_input_order_status_select').value;
						var product_type = document.getElementById('vipodh_input_product_type_select').value;
						var order_no = document.getElementById('vipodh_input_order_no').value;
						var game_cat_id = document.getElementById('vipodh_input_game_select').value;
						var start_date = document.getElementById('vipodh_input_start_date').value;
						var end_date = document.getElementById('vipodh_input_end_date').value;
						search_vip_order_history(order_status_id, product_type, order_no, game_cat_id, start_date, end_date, languages_id, SID);
<?
		        	}
?>
					
		        } else {
		        	document.getElementById('odh_button_update_'+req_grp_id).attributes['onclick'].value = 'return validate_confirmed_qty(\''+req_id+'\', \''+req_grp_id+'\')';
		        	alert(error_msg);
		        }
		       
		   	 });
	  	});
	}
	
	function validate_request_qty(opId, trade_type) {
		stop_coundown_activity();
		
		if (trade_type == 'trade_cancel') {
			var status	= confirm('<?=JS_CONFIRM_CANCEL_ORDER?>');
		
			if(status == true) {
				document.getElementById('trade_type_'+opId).value = trade_type;
				document.getElementById('accept_order_'+opId).submit();
			}
		} else {
			var error = false;
			var errormsg = '';
			
			var submit_qty = parseInt(document.getElementById('submit_quantity_'+opId).value);
			var char_id = trim_str(document.getElementById('vip_character_name_'+opId).value);
			var captcha_code = trim_str(document.getElementById('captcha_code_'+opId).value);
			
			captcha_code
			
			if(char_id == ''){
				error = true;
				errormsg += "<?=ERROR_NO_TRADE_CHARACTER?>\n";
			}
			
			if (submit_qty < 0 || isNaN(submit_qty)){
				error = true;
				errormsg += "<?=ERROR_INVALID_QTY?>";
			}
			
			if (captcha_code == ''){
				error = true;
				errormsg += "<?=ERROR_INVALID_CODE?>";
			}
			
			if(error){
				alert(errormsg);
				return false;
			} else {
				document.getElementById('trade_type_'+opId).value = trade_type;
				document.getElementById('accept_order_'+opId).submit();
			}
		}
	}

	function add_search_results_row(res_xml_obj) {
		if (typeof(res_xml_obj) != 'object') return;
		
		var req_grp_id = res_xml_obj.getElementsByTagName('req_grp_id')[0];
	  	if (typeof(req_grp_id) == 'undefined' || req_grp_id == null) {	
	  		return;
	  	} else {
	  		req_grp_id = req_grp_id.firstChild.nodeValue;
	  	}
	  	
	  	var req_id = res_xml_obj.getElementsByTagName('req_id')[0];
	  	if (typeof(req_id) == 'undefined' || req_id == null) {
			return;	  	
	  	} else {
	  		req_id = req_id.firstChild.nodeValue;
	  	}
	  	
	  	var game_name = res_xml_obj.getElementsByTagName('game_name')[0];
	  	if (typeof(game_name) == 'undefined' || game_name == null) {
			game_name = '';
		} else {
			game_name = game_name.firstChild.nodeValue;
		}
		
		var server_name = res_xml_obj.getElementsByTagName('server_name')[0];
		if (typeof(server_name) == 'undefined' || server_name == null) {
			server_name = '';
		} else {
			server_name = server_name.firstChild.nodeValue;
		}
		
		var req_qty = res_xml_obj.getElementsByTagName('req_qty')[0];
		if (typeof(req_qty) == 'undefined' || req_qty == null) {
			req_qty = '';
		} else {
			req_qty = req_qty.firstChild.nodeValue;
		}
		
		var confirmed_qty = res_xml_obj.getElementsByTagName('confirmed_qty')[0];
		if (typeof(confirmed_qty) == 'undefined' || confirmed_qty == null) {
			confirmed_qty = '';
		} else {
			confirmed_qty = trim_str(confirmed_qty.firstChild.nodeValue);
		}
		
		var amount = res_xml_obj.getElementsByTagName('amount')[0];
		if (typeof(amount) == 'undefined' || amount == null) {
			amount = '';
		} else {
			amount = amount.firstChild.nodeValue;
		}
		
		var show_restock = res_xml_obj.getElementsByTagName('show_restock')[0];
		if (typeof(show_restock) == 'undefined' || show_restock == null) {
			show_restock = '';
		} else {
			show_restock = trim_str(show_restock.firstChild.nodeValue);
		}
		
		var restock_character = res_xml_obj.getElementsByTagName('restock_character')[0];
		if (typeof(restock_character) == 'undefined' || restock_character == null) {
			restock_character = '';
		} else {
			restock_character = trim_str(restock_character.firstChild.nodeValue);
		}
		
		var status_name = res_xml_obj.getElementsByTagName('status_name')[0];
		if (typeof(status_name) == 'undefined' || status_name == null) {
			status_name = '';
		} else {
			status_name = status_name.firstChild.nodeValue;
		}
		
		var current_status_id = res_xml_obj.getElementsByTagName('current_status_id')[0];
		if (typeof(current_status_id) == 'undefined' || current_status_id == null) {
			current_status_id = '';
		} else {
			current_status_id = current_status_id.firstChild.nodeValue;
		}
		
		var show_expiry = res_xml_obj.getElementsByTagName('show_expiry')[0];
		if (typeof(show_expiry) == 'undefined' || show_expiry == null) {
			show_expiry = '';
		} else {
			show_expiry = show_expiry.firstChild.nodeValue;
		}
		
		var expiry_time = res_xml_obj.getElementsByTagName('expiry_time')[0];
		if (typeof(expiry_time) == 'undefined' || expiry_time == null) {
			expiry_time = '';
		} else {
			expiry_time = expiry_time.firstChild.nodeValue;
		}
		
		var buyback_remarks = res_xml_obj.getElementsByTagName('buyback_remarks')[0];
		if (typeof(buyback_remarks) == 'undefined' || buyback_remarks == null) {
			buyback_remarks = '';
		} else {
			buyback_remarks = buyback_remarks.firstChild.nodeValue;
		}
		
		var trade_mode = res_xml_obj.getElementsByTagName('trade_type')[0];
		if (typeof(trade_mode) == 'undefined' || trade_mode == null) {
			trade_mode = '';
		} else {
			trade_mode = trade_mode.firstChild.nodeValue;
		}
		
		// allow_to_upload: for the user who havent upload the ss yet.
		var allow_to_upload = res_xml_obj.getElementsByTagName('allow_to_upload')[0];
		if (typeof(allow_to_upload) == 'undefined' || allow_to_upload == null) {
			allow_to_upload = '';
		} else {
			allow_to_upload = allow_to_upload.firstChild.nodeValue;
		}
		
		divhtml += '<form name="confirm_sent_'+req_grp_id+'" action="<?=tep_href_link(FILENAME_MY_VIP_ORDERS_HISTORY, tep_get_all_get_params(array('action')) . 'action=confirm_sent')?>" method="post" enctype="multipart/form-data">'
				+ '<table width="100%" border="0" cellpadding="0" cellspacing="0">'
				+ '<tr>'
				+ '<td width="<?=$col_titles[0]['width']?>" class="ordersRecords"><p>'+req_grp_id+'</p></td>'
				+ '<td width="<?=$col_titles[1]['width']?>" class="ordersRecords"><p>'+game_name+'<br><small>'+server_name+'</small></p></td>'
				+ '<td width="<?=$col_titles[2]['width']?>" class="ordersRecords"><p>'+restock_character+'</p></td>';
		divhtml += '<td width="<?=$col_titles[4]['width']?>" class="ordersRecords"><p>'+req_qty+'</p></td>';
		
		//if restock character available, let supplier fill in 2nd list qty
		divhtml += '<td width="<?=$col_titles[5]['width']?>" align="center" class="ordersRecords">';
		if (current_status_id == '1' && show_restock == '1') {
			divhtml += '<input type="text" name="sent_qty['+req_grp_id+']" size="7" maxlength="9" id="sent_qty_'+req_grp_id+'">';
		} else {
			divhtml += '<p>'+confirmed_qty+'</p>';
		}
		
		divhtml += '</td>';

		divhtml += '<td width="<?=$col_titles[6]['width']?>" class="ordersRecords"><p>'+amount+'</p></td>'		
				+ '<td width="<?=$col_titles[7]['width']?>" class="ordersRecords"><p>'+status_name+'</p></td>';
		
		//Action Column
		divhtml += '<td width="<?=$col_titles[8]['width']?>" valign="top">';
		if (current_status_id == '1') {
			//Processing
			divhtml += '<input name="request_group_id" value="'+req_grp_id+'" type="hidden">' 
					+  '<input name="request_id" value="'+req_id+'" type="hidden">';

			if (show_restock == '1') {
				divhtml += 	'<input id="request_qty_'+req_grp_id+'" name="request_qty['+req_grp_id+']" value="'+req_qty+'" type="hidden">' + 
							'<input value="<?=BUTTON_CONFIRM?>" class="generalBtn" title="<?=ALT_BUTTON_CONFIRM?>" name="odh_button_update_'+req_grp_id+'" onmouseover="this.className=\'generalBtnOver\'" onmouseout="this.className=\'generalBtn\'" id="odh_button_update_'+req_grp_id+'" type="button" onclick="return validate_confirmed_qty(\''+req_id+'\', \''+req_grp_id+'\')"><br/>' + 
							'<input value="<?=BUTTON_CANCEL?>" class="generalBtn" title="<?=ALT_BUTTON_CANCEL?>" name="btn_cancel_'+req_grp_id+'" onmouseover="this.className=\'generalBtnOver\'" onmouseout="this.className=\'generalBtn\'" id="minBtnWidth" type="submit" onClick="return confirm(\'<?=JS_CONFIRM_CANCEL_ORDER?>\')">';
			} else {
				divhtml += '<input value="<?=BUTTON_CANCEL?>" class="generalBtn" title="<?=ALT_BUTTON_CANCEL?>" name="btn_cancel_'+req_grp_id+'" onmouseover="this.className=\'generalBtnOver\'" onmouseout="this.className=\'generalBtn\'" id="minBtnWidth" type="submit" onClick="return confirm(\'<?=JS_CONFIRM_CANCEL_ORDER?>\')">';
			}
		} else {
			//Button for popup report
			divhtml += '<input value="<?=TEXT_VIEW?>" class="generalBtn" title="<?=TEXT_VIEW?>" onmouseover="this.className=\'generalBtnOver\'" onmouseout="this.className=\'generalBtn\'" id="minBtnWidth" onclick="hiddenFloatingDiv(\'order_report\');show_order_report(\''+req_grp_id+'\', \'<?=$languages_id?>\', \'<?=SID?>\');" name="vip_btn_view" type="button"><br><span class="title-text" id="wbb_div_msgField_'+req_grp_id+'"></span>';
			if (allow_to_upload == 'yes' && current_status_id != 4) {
				divhtml += '<input type="button" onclick="upload_ss(\''+req_grp_id+'\'); return false;" onmouseout="this.className=\'gray_submit_button\'" title="<?=TEXT_UPLOAD_SS?>" class="gray_submit_button" value="<?=TEXT_UPLOAD_SS?>">';
			}
		}
		
		divhtml += "</td>"
				+ "</tr>";

		// show expiry and screenshot
		if(show_expiry > 0){
			divhtml += '<tr>'
					+  '	<td></td>'
					+  '	<td colspan="<?=(count($col_titles)-2)?>">'
					+  '		<table bgcolor="#e0ecf4" border="0" cellpadding="2" cellspacing="2" width="100%">'
					+  '			<tr>'
					+  '				<td>'
					+  '					<table border="0" cellpadding="2" cellspacing="1" width="100%">'
					+  '						<tr>'
					+  '							<td class="title-text" width="30%"><p style="color:red;"><?=TEXT_EXPIRES?></p></td>'
					+  '							<td class="title-text" colspan="2" width="70%"><p style="color:red;">'+ expiry_time +' +10 <?=TEXT_MINUTES?></p></td>'
					+  '						</tr>'
					+  '					</table>'
					+  '				</td>'
					+  '			</tr>'
					+  '		</table>'
					+  '	</td>'
					+  '	<td></td>'
					+  '</tr>';
		}
		
		
		if (buyback_remarks.length) {
			divhtml += '<tr>'
					+ '		<td></td>'
					+ '		<td class="title-text" bgcolor="#ffffcc" colspan="<?=(count($col_titles)-2)?>">' + buyback_remarks + '</td>'
					+ '		<td></td>'
					+ '</tr>';
		}
		
		divhtml +='		<tr>'
				+ '			<td colspan="<?=count($col_titles)?>"><img src="<?=DIR_WS_IMAGES?>space.gif" alt="" border="0" height="8" width="1"></td>'
				+ '		</tr>'
				+ '		<tr>'
				+ '			<td background="<?=DIR_WS_IMAGES?>space_line2.gif" height="1" colspan="<?=count($col_titles)?>"><img src="<?=DIR_WS_IMAGES . "space.gif"?>" width="1" height="1"></td>'
				+ '		</tr>'
				+ '</table>'
				+ '</form>';
	}
	
	function add_vip_result_row(res_xml_obj) {
		if (typeof(res_xml_obj) != 'object') return;
		
		var orders_products_id = res_xml_obj.getElementsByTagName('orders_products_id')[0];
		if (typeof(orders_products_id) == 'undefined' || orders_products_id == null) {
			return;
		} else {
			orders_products_id = orders_products_id.firstChild.nodeValue;
		}
		
		var game_name = res_xml_obj.getElementsByTagName('game_name')[0];
		if (typeof(game_name) == 'undefined' || game_name == null) {
			game_name = '';
		} else {
			game_name = game_name.firstChild.nodeValue;
		}
		
		var path_name = res_xml_obj.getElementsByTagName('path_name')[0];
		if (typeof(path_name) == 'undefined' || path_name == null) {
			path_name = '';
		} else {
			path_name = path_name.firstChild.nodeValue;
		}
		
		var quantity = res_xml_obj.getElementsByTagName('quantity')[0];
		if (typeof(quantity) == 'undefined' || quantity == null) {
			quantity = '0';
		} else {
			quantity = quantity.firstChild.nodeValue;
		}
		
		var allocate_time = res_xml_obj.getElementsByTagName('allocate_time')[0];
		if (typeof(allocate_time) == 'undefined' || allocate_time == null) {
			allocate_time = '0';
		} else {
			allocate_time = allocate_time.firstChild.nodeValue;
		}
		
		var trade_customers_price = res_xml_obj.getElementsByTagName('trade_customers_price')[0];
		if (typeof(trade_customers_price) == 'undefined' || trade_customers_price == null) {
			trade_customers_price = '0';
		} else {
			trade_customers_price = trade_customers_price.firstChild.nodeValue;
		}
		
		var trade_customers_price_display = res_xml_obj.getElementsByTagName('trade_customers_price_display')[0];
		if (typeof(trade_customers_price_display) == 'undefined' || trade_customers_price_display == null) {
			trade_customers_price_display = '0';
		} else {
			trade_customers_price_display = trade_customers_price_display.firstChild.nodeValue;
		}
		
		var trade_us_price = res_xml_obj.getElementsByTagName('trade_us_price')[0];
		if (typeof(trade_us_price) == 'undefined' || trade_us_price == null) {
			trade_us_price = '0';
		} else {
			trade_us_price = trade_us_price.firstChild.nodeValue;
		}
		
		var trade_us_price_display = res_xml_obj.getElementsByTagName('trade_us_price_display')[0];
		if (typeof(trade_us_price_display) == 'undefined' || trade_us_price_display == null) {
			trade_us_price_display = '0';
		} else {
			trade_us_price_display = trade_us_price_display.firstChild.nodeValue;
		}
		
		var bo_exact_qty_msg = res_xml_obj.getElementsByTagName('bo_exact_qty_msg')[0];
		if (typeof(bo_exact_qty_msg) == 'undefined' && bo_exact_qty_msg == null) {
			bo_exact_qty_msg = '';
		} else {
			bo_exact_qty_msg = bo_exact_qty_msg.firstChild.nodeValue;
		}
		

		vipdivhtml += '<form name="accept_order_'+orders_products_id+'" id="accept_order_'+orders_products_id+'" action="<?=tep_href_link(FILENAME_MY_VIP_ORDERS_HISTORY, tep_get_all_get_params(array('action')) . 'action=accept_order')?>" method="post">'
					+ '<input type="hidden" name="trade_type_'+orders_products_id+'" id="trade_type_'+orders_products_id+'">'
					+ '<table width="100%" border="0" cellpadding="0" cellspacing="0">'
					+ '<tr valign="top">'
					+ '	<td width="<?=$vip_col_titles[0]['width']?>" align="<?=$vip_col_titles[0]['align']?>" class="ordersRecords"><p>'+game_name+'<br/><small>'+path_name+'</small></p></td>'
					+ '	<td width="<?=$vip_col_titles[1]['width']?>" align="<?=$vip_col_titles[1]['align']?>"  class="ordersRecords"><p>'+quantity+'</p></td>'
					+ '	<td width="<?=$vip_col_titles[2]['width']?>" align="<?=$vip_col_titles[2]['align']?>"  class="ordersRecords"><p><input name="submit_quantity['+orders_products_id+']" size="10" type="text" id="submit_quantity_'+orders_products_id+'"></p></td>'
					+ '	<td width="<?=$vip_col_titles[3]['width']?>" align="<?=$vip_col_titles[3]['align']?>"  class="ordersRecords"><p><input name="vip_character_name['+orders_products_id+']" size="10" type="text" id="vip_character_name_'+orders_products_id+'"></p></td>'
					+ '	<td width="<?=($vip_col_titles[4]['width'] + $vip_col_titles[5]['width'])?>" align="<?=$vip_col_titles[4]['align']?>" class="ordersRecords" colspan="2"><p><input type="hidden" name="opId" value="'+orders_products_id+'">'
					+ '		<table border="0" cellpadding="0" cellspacing="0">';

		if(trade_customers_price != 0){
			vipdivhtml += '		<tr>'
						+ '			<td width="<?=$vip_col_titles[4]['width']?>" align="<?=$vip_col_titles[4]['align']?>" class="ordersRecords"><p><?=TEXT_TRADE_CUSTOMERS_PRICE?>'+trade_customers_price_display+'</p></td>'
						+ '			<td width="<?=$vip_col_titles[5]['width']?>" align="<?=$vip_col_titles[5]['align']?>" class="ordersRecords"><input value="<?=BUTTON_TRADE_CUSTOMERS?>" class="generalBtn" title="<?=BUTTON_TRADE_CUSTOMERS?>" name="btn_trade_customer_'+orders_products_id+'" onclick="return validate_request_qty('+orders_products_id+', \'trade_customer\');" onmouseover="this.className=\'generalBtnOver\'" onmouseout="this.className=\'generalBtn\'" type="button"></td>'
						+ '		</tr>';
		}
		
		if(trade_us_price != 0){		
			vipdivhtml += '		<tr>'
						+ '			<td width="<?=$vip_col_titles[4]['width']?>" align="<?=$vip_col_titles[4]['align']?>" class="ordersRecords"><p><?=TEXT_TRADE_WITH_US_PRICE?>'+trade_us_price_display+'</p></td>'
						+ '			<td width="<?=$vip_col_titles[5]['width']?>" align="<?=$vip_col_titles[5]['align']?>" class="ordersRecords"><input value="<?=BUTTON_TRADE_WITH_US?>" class="generalBtn" title=" <?=BUTTON_TRADE_WITH_US?> " name="btn_trade_us_'+orders_products_id+'" onclick="return validate_request_qty('+orders_products_id+', \'trade_us\');" onmouseover="this.className=\'generalBtnOver\'" onmouseout="this.className=\'generalBtn\'" type="button"></td>'
						+ '		</tr>';
		}
		
		vipdivhtml += '			<tr>'
					+ '				<td width="<?=$vip_col_titles[4]['width']?>" align="<?=$vip_col_titles[4]['align']?>" class="ordersRecords"><p>&nbsp;</p></td>'
					+ '				<td width="<?=$vip_col_titles[5]['width']?>" align="<?=$vip_col_titles[5]['align']?>" class="ordersRecords"><input value="<?=BUTTON_TRADE_CANCEL?>" class="generalBtn" title="<?=BUTTON_TRADE_CANCEL?>" name="btn_trade_cancel_'+orders_products_id+'" onclick="return validate_request_qty('+orders_products_id+', \'trade_cancel\');" onmouseover="this.className=\'generalBtnOver\'" onmouseout="this.className=\'generalBtn\'" type="button"></td>'
					+ ' 		</tr>'
					+ '		</table>'
					+ ' </p></td>'
					+ '</tr>';
		
		vipdivhtml += '		<tr>'
					+ '			<td colspan="<?=(count($col_titles))?>" class="ordersRecords">'
					+ '			<?=tep_image('securimage_show.php?sid='.md5(uniqid(time())))?>'
					+ '			<input type="text" name="captcha_code_'+orders_products_id+'" id="captcha_code_'+orders_products_id+'" size="6" maxlength="6" />'
					+ '			</td>'
					+ '		</tr>';
								
		vipdivhtml += '		<tr>'
					+ '			<td class="ordersRecords" colspan="2"><p><b><?=TEXT_EXPIRES?></b>'+allocate_time+'</p></td>';
		if (trade_us_price != 0 || trade_customers_price != 0) {
			vipdivhtml	+= '		<td colspan="<?=(count($col_titles)-1)?>" class="ordersRecords"><p><span class="redIndicator">'+bo_exact_qty_msg+'</span></p></td>';
		} else {
			vipdivhtml	+= '		<td colspan="<?=(count($col_titles)-1)?>" class="ordersRecords"><p><span class="redIndicator">&nbsp;</span></p></td>';
		}
		
		vipdivhtml	+= '	</tr>'
					+ '		<tr>'
					+ '			<td background="<?=DIR_WS_IMAGES?>space_line2.gif" height="1" colspan="<?=count($vip_col_titles)?>"><?=tep_image(DIR_WS_IMAGES . 'space.gif', '', '1', '1')?></td>'
					+ '		</tr>'
					+ '	</table>'
					+ '</form>';
						
	}
	
	function show_search_results_div(orderType) {
		if(orderType == 'vip'){
			document.getElementById('vip_awaiting_order_list').innerHTML = vipdivhtml;
		} else {
			document.getElementById('vipodh_div_search_results').innerHTML = divhtml;
		}
	}

	function toggleCountdown(cbChecked) {
		if (cbChecked == true) {
			start_coundown_activity();
		} else {
			stop_coundown_activity();
		}
	}
	function start_coundown_activity() {
		coundown_active = true;
		countdown_secs = countdown_secs_default;
		document.getElementById('timer_main').innerHTML = '<?='' . '<span id="timer_display" name="timer_display"></span>&nbsp;'.TEXT_SECONDS?>';
		document.getElementById('timer_vip_main').innerHTML = '<?='' . '<span id="timer_vip_display" name="timer_vip_display"></span>&nbsp;'.TEXT_SECONDS?>';
		doCountDown();
	}
	function stop_coundown_activity() {
		coundown_active = false;
		disableDivInputs(false);
		document.getElementById('timer_main').innerHTML = '<span id="timer_display" name="timer_display"></span>';
		document.getElementById('timer_vip_main').innerHTML = '<span id="timer_vip_display" name="timer_vip_display"></span>';
	}
	
	function doCountDown(manual) {
		if (manual == null || !manual) {
			countdown_secs -= 1;
			document.getElementById('timer_display').innerHTML = countdown_secs;
			document.getElementById('timer_vip_display').innerHTML = countdown_secs;
			if (coundown_active == false) {
				document.getElementById('timer_main').innerHTML = '';
				document.getElementById('timer_vip_main').innerHTML = '';
				return;
			}
		}
		
		if (countdown_secs > 0) {
			if (countdown_secs == lockdown_form_secs) {
				disableDivInputs(true);
			}
			history_timerID = setTimeout("doCountDown()",1000);
		} else {
			if (history_timerID) {
				clearTimeout(history_timerID);
				history_timerID = 0;
			}
			
			disableDivInputs(false);
			var order_status_id = document.getElementById('vipodh_input_order_status_select').value;
			var product_type = document.getElementById('vipodh_input_product_type_select').value;
			var order_no = document.getElementById('vipodh_input_order_no').value;
			var game_cat_id = document.getElementById('vipodh_input_game_select').value;
			var start_date = document.getElementById('vipodh_input_start_date').value;
			var end_date = document.getElementById('vipodh_input_end_date').value;
			search_vip_order_history(order_status_id, product_type, order_no, game_cat_id, start_date, end_date, languages_id, SID);
		}
	}

	function disableDivInputs(bool) {
		//Enter here during form lockdown
		document.getElementById('vipodh_input_order_status_select').disabled=bool;
		document.getElementById('vipodh_input_product_type_select').disabled=bool;
		document.getElementById('vipodh_input_order_no').disabled=bool;
		document.getElementById('vipodh_input_game_select').disabled=bool;
		document.getElementById('vipodh_input_start_date').disabled=bool;
		document.getElementById('vipodh_input_end_date').disabled=bool;
		document.getElementById('vipodh_button_search').disabled=bool;
		if (bool == true) {
			document.getElementById('vipodh_div_search_results').innerHTML = '<p class="title-text" valign="middle" align="center"><?=TEXT_WAIT_FOR_REFRESH?></p>';
		}
	}

	function search_vip_order_history(order_status_id, product_type, order_no, game_cat_id, start_date, end_date, languages_id, SID) {
		stop_coundown_activity();
		onVipOrderHistoryRefresh(order_status_id, product_type, order_no, game_cat_id, start_date, end_date, languages_id, SID);
	}
	
	function showHideBox() {
	    var item = 'contentTextBoxAll';
    	if (DOMCall(item).className == 'hide') {
    		DOMCall(item).className = 'show';
    		DOMCall(item +'_rotate').className = 'hide';
    		DOMCall(item +'_image').innerHTML = '<?=tep_image(DIR_WS_ICONS . 'icon_collapse.gif')?>';
    	} else {
    		DOMCall(item).className = 'hide';
    		DOMCall(item +'_rotate').className = 'show';
    		DOMCall(item +'_image').innerHTML = '<?=tep_image(DIR_WS_ICONS . 'icon_expand.gif')?>';
    	}
    }
    
    function popUp(URL) {
        eval("page = window.open(URL, '', 'width=640,height=480,top=100,left=100,scrollbars=yes');");
    }
//-->
</script>


<!--Begin VIP awaiting order-->
<table border="0" width="100%" cellspacing="0" cellpadding="0">
	<tbody>
	<tr>
		<td width="3" height="40"><?=tep_image(DIR_WS_IMAGES . 'box_side01.gif', '', '3', '40')?></td>
		<td background="<?=DIR_WS_IMAGES . 'box_side02.gif'?>">
			<table width="100%" border="0" cellpadding="0" cellspacing="2">
				<tbody>
				<tr>
					<td><?=tep_image(DIR_WS_ICONS . 'icon_03.gif', '', '10', '10', ' hspace="4"') . '&nbsp;<span class="title-text">' . TEXT_VIP_AWAITING_ORDERS_LIST . '</span>' ?></td>
				</tr>
				</tbody>
			</table>
		</td>
		<td width="3" height="40"><?=tep_image(DIR_WS_IMAGES . 'box_side03.gif', '', '3', '40')?></td>
	</tr>
	<tr>
		<td width="3" background="<?=DIR_WS_IMAGES . 'box_side08.gif'?>"><?=tep_image(DIR_WS_IMAGES . 'space.gif', '', '3', '1')?></td>
		<td>
			<table border="0" cellpadding="0" cellspacing="0" width="100%">
				<tr>
					<td align="center" valign="top">
						<table border="0" cellpadding="0" cellspacing="0" width="100%">
							<tr>
								<td height="40" width="5"><?=tep_image(DIR_WS_IMAGES . 'box_grey02a.gif', '', '5', '40')?></td>
								<td height="40" background="<?=DIR_WS_IMAGES?>box_grey02b.gif">
									<table border="0" cellpadding="0" cellspacing="0" height="40" width="750">
										<tr>
											<td height="3" colspan="<?=(count($vip_col_titles)-1)?>"></td>
										</tr>
										<!--start vip orders -->
										<tr valign="center">
										<?
											foreach ($vip_col_titles as $vip_col_array) {
												echo "<td class='title-text' width='".$vip_col_array['width']."' align='".$vip_col_array['align']."'>{$vip_col_array['title']}</td>";
											}
										?>
										</tr>
										<tr>
											<td height="3" colspan="<?=(count($vip_col_titles)-1)?>"></td>
										</tr>
									</table>
								</td>
								<td height="40" width="5"><?=tep_image(DIR_WS_IMAGES . 'box_grey02c.gif', '', '5', '40')?></td>
							</tr>
							<tr>
						    	<td colspan="3" background="<?=DIR_WS_IMAGES . 'space_line2.gif'?>" height="1"><?=tep_image(DIR_WS_IMAGES . 'space.gif', '', '1', '1')?></td>
						    </tr>
							<tr>
								<td height="40" width="5"></td>
								<td height="40">
									<table border="0" cellpadding="0" cellspacing="1" width="750" height="130"> 
									    <tr>
									    	<td height="90%" colspan="<?=count($vip_col_titles)?>">
										    	<div id="vip_awaiting_order_list" name="vip_awaiting_order_list" style="position:relative; height:130; overflow:auto;"><?=$vip_order_html?></div>
											</td>
									    </tr>
					                    <tr>
					                      <td colspan="<?=count($vip_col_titles)?>" background="<?=DIR_WS_IMAGES . 'space_line2.gif'?>" height="1"><?=tep_image(DIR_WS_IMAGES . 'space.gif', '', '1', '1')?></td>
					                    </tr>
										<tr>
											<td align="center" colspan="<?=count($vip_col_titles)?>">
												<table border="0" cellpadding='0' cellspacing='0'>
													<tr>
														<td align='center' colspan='<?=count($vip_col_titles)?>'>
															<div id='auto_refresh_bar' class='title-text'>
																<span id="total_order"><?=$vip_order_awaiting_num_row?></span> <?=TEXT_ORDERS_FOUND?>&nbsp;
																<div class="title-text" align="center">
																	<span id="timer_vip_main" name="timer_vip_main"><span id="timer_vip_display" name="timer_vip_display"></span></span>
																	<?=tep_button(BUTTON_REFRESH, ALT_BUTTON_REFRESH, '', 'onClick="countdown_secs=0; doCountDown(true);"', 'inputButton')?>
																</div>
															</div>
														</td>
													</tr>
												</table>
											</td>
										</tr>
										<tr>
											<td height="5" colspan="<?=count($vip_col_titles)?>"></td>
										</tr>
									</table>
								</td>
								<td height="40" width="5"></td>
							</tr>
						</table>
					</td>
				</tr>
			</table>
		</td>
		<td width="3" background="<?=DIR_WS_IMAGES . 'box_side04.gif'?>"><?=tep_image(DIR_WS_IMAGES . 'space.gif', '', '3', '1')?></td>
	</tr>
	<tr>
		<td width="3" background="<?=DIR_WS_IMAGES . 'box_side08.gif'?>"><?=tep_image(DIR_WS_IMAGES . 'space.gif', '', '3', '1')?></td>
		<td>
    	</td>
    	<td width="3" background="<?=DIR_WS_IMAGES . 'box_side04.gif'?>"><?=tep_image(DIR_WS_IMAGES . 'space.gif', '', '3', '1')?></td>
    </tr>
	<tr>
		<td width="3" height="16"><?=tep_image(DIR_WS_IMAGES . 'box_side07.gif', '', '3', '16')?></td>
		<td background="<?=DIR_WS_IMAGES . 'box_side06.gif'?>"><?=tep_image(DIR_WS_IMAGES . 'space.gif', '', '', '16')?></td>
		<td width="3" height="16"><?=tep_image(DIR_WS_IMAGES . 'box_side05.gif', '', '3', '16')?></td>
	</tr>
	</tbody>
</table>

<!--End VIP awaiting order-->
<br />
<!--Begin VIP order history-->
<table border="0" width="100%" cellspacing="0" cellpadding="0">
  <tbody>
      <tr>
		<td width="3" height="4" colspan="3"></td>
      </tr>
      <tr>
		<td width="3" height="40"><?=tep_image(DIR_WS_IMAGES . 'box_side01.gif', '', '3', '40')?></td>
        <td background="<?=DIR_WS_IMAGES . 'box_side02.gif'?>">
            <table width="100%" border="0" cellpadding="0" cellspacing="2">
                <tbody>
            	<tr>
              		<td><?=tep_image(DIR_WS_ICONS . 'icon_03.gif', '', '10', '10', ' hspace="4"') . '&nbsp;<span class="title-text">' . HEADER_TITLE_MY_VIP_ORDERS_HISTORY . '</span>' ?></td>
            	</tr>
                </tbody>
            </table>
        </td>
        <td width="3" height="40"><?=tep_image(DIR_WS_IMAGES . 'box_side03.gif', '', '3', '40')?></td>
      </tr>
      <tr>
      	<td width="3" background="<?=DIR_WS_IMAGES . 'box_side08.gif'?>"><?=tep_image(DIR_WS_IMAGES . 'space.gif', '', '3', '1')?></td>
        <td>
            <!-- start repeat -->
            <table cellpadding="2" border="0" cellspacing="0" width="100%">
                <tbody>
                    <tr>
                      <td>
						<!-- Start inner table -->
						<?=tep_draw_form('order_history_search', FILENAME_MY_VIP_ORDERS_HISTORY, tep_get_all_get_params(array('action')) . 'action=search' , "post")?>
				    	<table border="0" width="100%" cellspacing="5" cellpadding="0" class="buttonBox">
		                	<tr>
		            			<td width="1" colspan="9"><?=tep_draw_separator('pixel_trans.gif', '1', '1')?></td>
		            		</tr>
		            		<tbody class="show" id="odh_tbody_step1" name="odh_tbody_step1">
		                		<tr>
		                			<td rowspan="6" width="1" ><?=tep_draw_separator('pixel_trans.gif', '1', '1')?></td>
			                		<td align="left" valign="top"><p><?=TEXT_ORDER_STATUS?></p></td>
									<td rowspan="6" width="1" ><?=tep_draw_separator('pixel_trans.gif', '1', '1')?></td>
		                			<td align="left" valign="top" width="30%"><?=tep_draw_pull_down_menu('vipodh_input_order_status_select', $order_status_arr, (isset($form_values_arr['vipodh_input_order_status_select']) && $form_values_arr['vipodh_input_order_status_select'] ? $form_values_arr['vipodh_input_order_status_select'] : ''), ' id="vipodh_input_order_status_select" ')?>
		                			    <?='&nbsp;' . tep_image(DIR_WS_ICONS . 'icon_05.gif', '', '14', '15', 'onMouseover="ddrivetip(\''.TEXT_HELP_ORDER_STATUS_SELECT.'\', \'\', 200);" onMouseout="hideddrivetip();"')?>
										<p><noscript><?='<br>' . TEXT_HELP_ORDER_STATUS_SELECT?></noscript></p>
									</td>
									<td rowspan="6" width="1" ><?=tep_draw_separator('pixel_trans.gif', '1', '1')?></td>
		                			<td align="left" valign="top"><p><?=TEXT_PRODUCT_TYPE?></p></td>
		                			<td rowspan="6" width="1" ><?=tep_draw_separator('pixel_trans.gif', '1', '1')?></td>
			                		<td align="left" valign="top"><?=tep_draw_pull_down_menu('vipodh_input_product_type_select', $product_type_arr, (isset($form_values_arr['vipodh_input_product_type_select']) && $form_values_arr['vipodh_input_product_type_select'] ? $form_values_arr['vipodh_input_product_type_select'] : ''), ' id="vipodh_input_product_type_select" ')?></td>
		                			<td rowspan="6" width="1" ><?=tep_draw_separator('pixel_trans.gif', '1', '1')?></td>
		                		</tr>
			                	<tr>
			            			<td colspan="4"></td>
			            		</tr>
		                		<tr>
			                		<td align="left" valign="top"><p><?=TEXT_ORDER_NO?></p></td>
		                			<td align="left" valign="top"><?=tep_draw_input_field('vipodh_input_order_no', (isset($form_values_arr['vipodh_input_order_no']) && $form_values_arr['vipodh_input_order_no'] ? $form_values_arr['vipodh_input_order_no'] : ''), 'id="vipodh_input_order_no" size="16" maxlength="16"')?></td>
		                			<td align="left" valign="top"><p><?=TEXT_GAME?></p></td>
		                			<td align="left" valign="top"><?=tep_draw_pull_down_menu('vipodh_input_game_select', $wbb_game_list_arr, (isset($form_values_arr['vipodh_input_game_select']) && $form_values_arr['vipodh_input_game_select'] ? $form_values_arr['vipodh_input_game_select'] : ''), ' id="vipodh_input_game_select" ')?></td>
		                		</tr>
			                	<tr>
			            			<td colspan="4"></td>
			            		</tr>
		                		<tr>
			                		<td align="left" valign="top"><p><?=TEXT_START_DATE?></p></td>
		                			<td align="left" valign="top"><?=tep_draw_input_field('vipodh_input_start_date', (isset($_POST['vipodh_input_start_date']) && $_POST['vipodh_input_start_date'] ? $_POST['vipodh_input_start_date'] : ''), ' id="vipodh_input_start_date" size="16" maxlength="16" onblur="if (self.gfPop) { gfPop.validateUserInput(document.order_history_search.vipodh_input_start_date); }" onKeyPress="return noEnterKey(event)" ')?>
			                			<a href="javascript:void(0)" onclick="if(self.gfPop)gfPop.fPopCalendar(document.order_history_search.vipodh_input_start_date);return false;" HIDEFOCUS><img name="popcal1" align="absmiddle" src="includes/javascript/PopCalendarXp/calbtn.gif" width="34" height="22" border="0" alt=""></a>
			                		</td>
			                		<td align="left" valign="top"><p><?=TEXT_END_DATE?></p></td>
		                			<td align="left" valign="top"><?=tep_draw_input_field('vipodh_input_end_date', (isset($_POST['vipodh_input_end_date']) && $_POST['vipodh_input_end_date'] ? $_POST['vipodh_input_end_date'] : ''), ' id="vipodh_input_end_date" size="16" maxlength="16" onblur="if (self.gfPop) { gfPop.validateUserInput(document.order_history_search.vipodh_input_end_date); }" onKeyPress="return noEnterKey(event)" ')?>
		                				<a href="javascript:void(0)" onclick="if(self.gfPop)gfPop.fPopCalendar(document.order_history_search.vipodh_input_end_date);return false;" HIDEFOCUS><img name="popcal2" align="absmiddle" src="includes/javascript/PopCalendarXp/calbtn.gif" width="34" height="22" border="0" alt=""></a>
		                			</td>
		                		</tr>
			                	<tr>
			            			<td colspan="4"></td>
			            		</tr>
		            		</tbody>
		                	<tr>
		            			<td width="1" colspan="9"><?=tep_draw_separator('pixel_trans.gif', '1', '1')?></td>
		            		</tr>
		                	<tr>
		                		<td colspan="9" align="left" valign="top">
		                		    <table border="0" width="100%" cellspacing="2" cellpadding="0">
		                		        <tr>
		                		            <td valign="top" width="90%">
                                            </td>
                                            <td align="right" valign="top" width="15%"><?=tep_image_submit(THEMA.'button_confirm.gif', TEXT_SEARCH, 'id="vipodh_button_search" name="vipodh_button_search" onclick="stop_coundown_activity()"', 'generalBtn') . '&nbsp;'?></td>
                                            <td align="right" valign="top" width="15%"><?=tep_button(BUTTON_RESET, ALT_BUTTON_RESET, tep_href_link(FILENAME_MY_VIP_ORDERS_HISTORY, 'action=reset_session'), '', 'inputButton')?></td>
                                        </tr>
                                    </table>
		                		</td>
		            		</tr>
		            	</table>
				        </form>
				    	<table border="0" width="100%" cellspacing="5" cellpadding="0" class="buttonBox">
		            		<tr>
								<td background="<?=DIR_WS_IMAGES . 'space_line2.gif'?>" height="1"><?=tep_image(DIR_WS_IMAGES . 'space.gif', '', '1', '1')?></td>
		            		</tr>
		                	<tr>
		            			<td width="1" colspan="9"><?=tep_draw_separator('pixel_trans.gif', '1', '1')?></td>
		            		</tr>
		                    <tr>
		                    	<td>	
								<!-- Results -->
									<table border="0" cellpadding="0" cellspacing="0" width="100%">
										<tbody valign="top">
							          	<tr>
							            	<td align="center">
												<table border="0" cellpadding="0" cellspacing="0" width="100%">
													<tbody valign="top">
													<tr>
														<td height="40" width="5"><?=tep_image(DIR_WS_IMAGES . 'box_grey02a.gif', '', '5', '40')?></td>
														<td height="40" background="<?=DIR_WS_IMAGES?>box_grey02b.gif">
															<table border="0" cellpadding="0" cellspacing="0" height="40" width="760">
														    	<tbody>
														    	<tr valign="center">
																<?
																	foreach ($col_titles as $col_array) {
																		echo "<td class='title-text' width='".$col_array['width']."' align='".$col_array['align']."'>{$col_array['title']}</td>";
																	}
																?>
														    	</tr>
														    	</tbody>
															</table>
														</td>
														<td height="40" width="5"><?=tep_image(DIR_WS_IMAGES . 'box_grey02c.gif', '', '5', '40')?></td>
													</tr>
													<tr>
														<td height="40" width="5"></td>
														<td height="40">
															<table border="0" cellpadding="0" cellspacing="1" height="205" width="760">
														    	<tbody>
														    	<tr>
														    		<td height="90%" colspan="<?=$num_titles?>">
															    		<div id="vipodh_div_search_results" name="vipodh_div_search_results" style="position:relative; height:205; overflow:auto;"><?=$searchResultsHTML?></div>
																	</td>
														    	</tr>
										                    	<tr>
										                      		<td colspan="<?=$num_titles?>" background="<?=DIR_WS_IMAGES . 'space_line2.gif'?>" height="1"><?=tep_image(DIR_WS_IMAGES . 'space.gif', '', '1', '1')?></td>
										                    	</tr>
																<tr>
																	<td align="center" colspan="<?=$num_titles?>">
																		<table border="0" cellpadding='0' cellspacing='0'>
																			<tr>
																				<td align='center' colspan='<?=$num_titles?>'>
																					<div id='auto_refresh_bar' class='title-text'>
																						<span id="num_results"><?=$buyback_request_num_rows?></span> <?=TEXT_ORDERS_FOUND?>&nbsp;
																						<div class="title-text" align="center">
																							<span id="timer_main" name="timer_main"><span id="timer_display" name="timer_display"></span></span>
																							<?=tep_button(BUTTON_REFRESH, ALT_BUTTON_REFRESH, '', 'onClick="countdown_secs=0; doCountDown(true, \' \');"', 'inputButton')?>
																						</div>
																					</div>
																				</td>
																			</tr>
																		</table>
																	</td>
																</tr>
																<tr>
																	<td height="5" colspan="<?=$num_titles?>"></td>
																</tr>
															    </tbody>
															</table>
														</td>
														<td height="40" width="5"></td>
													</tr>
													</tbody>
												</table>
											</td>
							          	</tr>
										</tbody>
									</table>
								</td>
		                    </tr>
				        </table>
						<!-- end inner table -->
					</td>
				</tr>
                </tbody>
			</table>
            <!-- end repeat -->
        </td>
        <td width="3" background="<?=DIR_WS_IMAGES . 'box_side04.gif'?>"><?=tep_image(DIR_WS_IMAGES . 'space.gif', '', '3', '1')?></td>
	</tr>
	<tr>
		<td width="3" height="16"><?=tep_image(DIR_WS_IMAGES . 'box_side07.gif', '', '3', '16')?></td>
		<td background="<?=DIR_WS_IMAGES . 'box_side06.gif'?>"><?=tep_image(DIR_WS_IMAGES . 'space.gif', '', '', '16')?></td>
		<td width="3" height="16"><?=tep_image(DIR_WS_IMAGES . 'box_side05.gif', '', '3', '16')?></td>
	</tr>
	</tbody>
</table>
<div><iframe width=188 height=166 name="gToday:datetime:agenda.js:gfPop:plugins_24.js" id="gToday:datetime:agenda.js:gfPop:plugins_24.js" src="<?=DIR_WS_INCLUDES?>javascript/PopCalendarXp/ipopeng.htm" scrolling="no" frameborder="0" style="visibility:visible; z-index:999; position:absolute; top:-500px; left:-500px;"></iframe></div>
<script type="text/javascript">
<!--
<?
	if ($vipodh_auto_refresh_results_bool) {
		echo 'toggleCountdown(true);';
	}
?>
    //initial
   // showHideBox();
//-->
</script>

<!--  the following hidden div will be used by the script -->
<!--<div style="height: 470px; width: 750px; visibility:hidden" id="order_report">-->
<div style="visibility:hidden;" id="order_report">
	<div id="order_report_content" name="order_report_content" style="overflow:auto; height:1px; width:1px;" class="orderReportDivHide">
<?
	$boxContent = '	<table align="center" border="0" cellpadding="0" cellspacing="5">
						<tbody>
						<tr>
						    <td colspan="2" background="' . DIR_WS_IMAGES . 'space.gif'. '" height="4"></td>
						</tr>
						<tr>
							<td align="right"><span class="title-text">' . TEXT_GAME . '</span></td>
							<td align="left"><p><b><span name="wbb_show_game_name" id="wbb_show_game_name"></span></b></p></td>
						</tr>
						<tr>
						    <td colspan="2" background="' . DIR_WS_IMAGES . 'space.gif'. '" height="4"></td>
						</tr>
						<tr>
						    <td colspan="2" background="' . DIR_WS_IMAGES . 'space_line2.gif' . '" height="1"></td>
						</tr>
						<tr>
						    <td colspan="2" background="' . DIR_WS_IMAGES . 'space.gif'. '" height="4"></td>
						</tr>
						<tr>
							<td align="right"><span class="title-text">' . TEXT_SERVER . '</span></td>
							<td align="left"><p><b><span name="wbb_show_server_name" id="wbb_show_server_name"></span></b></p></td>
						</tr>
						<tr>
						    <td colspan="2" background="' . DIR_WS_IMAGES . 'space.gif'. '" height="4"></td>
						</tr>
						<tr>
						    <td colspan="2" background="' . DIR_WS_IMAGES . 'space_line2.gif' . '" height="1"></td>
						</tr>
						<tr>
						    <td colspan="2" background="' . DIR_WS_IMAGES . 'space.gif'. '" height="4"></td>
						</tr>
						<tr>
							<td align="right"><span class="title-text">' . TEXT_QUANTITY . '</span></td>
							<td align="left"><p><b><span name="wbb_show_request_quantity" id="wbb_show_request_quantity"></span> <span name="wbb_show_uom" id="wbb_show_uom"></span> ' . TEXT_OF.' <span name="wbb_show_product_name" id="wbb_show_product_name"></span></b></p></td>
						</tr>
						<tr>
						    <td colspan="2" background="' . DIR_WS_IMAGES . 'space.gif'. '" height="4"></td>
						</tr>
						<tr>
						    <td colspan="2" background="' . DIR_WS_IMAGES . 'space_line2.gif' . '" height="1"></td>
						</tr>
						<tr>
						    <td colspan="2" background="' . DIR_WS_IMAGES . 'space.gif'. '" height="4"></td>
						</tr>
						<tr>
							<td align="right"><span class="title-text">' . TEXT_TOTAL_PRICE . '</span></td>
							<td align="left"><p><b><span name="wbb_show_total_price" id="wbb_show_total_price"></span></b></p></td>
						</tr>
						<tr>
						    <td colspan="2" background="' . DIR_WS_IMAGES . 'space.gif'. '" height="4"></td>
						</tr>
						<tr>
						    <td colspan="2" background="' . DIR_WS_IMAGES . 'space_line2.gif' . '" height="1"></td>
						</tr>
						<tr>
						    <td colspan="2" background="' . DIR_WS_IMAGES . 'space.gif'. '" height="4"></td>
						</tr>
						<tr>
							<td align="right"><span class="title-text">' . TEXT_CHARACTER_NAME . '</span></td>
							<td align="left"><p><b><span name="wbb_show_sender_character" id="wbb_show_sender_character"></span></b></p></td>
						</tr>
						<tr>
						    <td colspan="2" background="' . DIR_WS_IMAGES . 'space.gif'. '" height="4"></td>
						</tr>
						<tr>
						    <td colspan="2" background="' . DIR_WS_IMAGES . 'space_line2.gif' . '" height="1"></td>
						</tr>
						<tr>
						    <td colspan="2" background="' . DIR_WS_IMAGES . 'space.gif'. '" height="4"></td>
						</tr>
						<tr>
							<td align="right"><span class="title-text">' . ENTRY_RECEIVER_CHAR_NAME . '</span></td>
							<td align="left"><p><b><span name="wbb_show_restk_character" id="wbb_show_restk_character"></span></b></p></td>
						</tr>
						<tr>
						    <td colspan="2" background="' . DIR_WS_IMAGES . 'space.gif'. '" height="4"></td>
						</tr>
						<tr>
						    <td colspan="2" background="' . DIR_WS_IMAGES . 'space_line2.gif' . '" height="1"></td>
						</tr>
						<tr>
						    <td colspan="2" background="' . DIR_WS_IMAGES . 'space.gif'. '" height="4"></td>
						</tr>
						<tr>
							<td align="right"><span class="title-text">' . TEXT_DELIVERY_TIME . '</span></td>
							<td align="left"><p><b><span name="wbb_show_delivery_time" id="wbb_show_delivery_time"></span></b></p></td>
						</tr>
						<tr>
						    <td colspan="2" background="' . DIR_WS_IMAGES . 'space.gif'. '" height="4"></td>
						</tr>
						<tr>
						    <td colspan="2" background="' . DIR_WS_IMAGES . 'space_line2.gif' . '" height="1"></td>
						</tr>
						<tr>
						    <td colspan="2" background="' . DIR_WS_IMAGES . 'space.gif'. '" height="4"></td>
						</tr>
						<tr>
							<td align="right"><span class="title-text">' . TEXT_CONTACT_NAME . '</span></td>
							<td align="left"><p><b><span name="wbb_show_contact_name" id="wbb_show_contact_name"></span></b></p></td>
						</tr>
						<tr>
						    <td colspan="2" background="' . DIR_WS_IMAGES . 'space.gif'. '" height="4"></td>
						</tr>
						<tr>
						    <td colspan="2" background="' . DIR_WS_IMAGES . 'space_line2.gif' . '" height="1"></td>
						</tr>
						<tr>
						    <td colspan="2" background="' . DIR_WS_IMAGES . 'space.gif'. '" height="4"></td>
						</tr>
						<tr>
							<td align="right"><span class="title-text">' . TEXT_CONTACT_NO . '</span></td>
							<td align="left"><p><b><span name="wbb_show_contact_no" id="wbb_show_contact_no"></span></b></p></td>
						</tr>
						<tr>
						    <td colspan="2" background="' . DIR_WS_IMAGES . 'space.gif'. '" height="4"></td>
						</tr>
						<tr>
						    <td colspan="2" background="' . DIR_WS_IMAGES . 'space_line2.gif' . '" height="1"></td>
						</tr>
						<tr>
						    <td colspan="2" background="' . DIR_WS_IMAGES . 'space.gif'. '" height="4"></td>
						</tr>
						<tr>
							<td align="right"><span class="title-text">' . TEXT_ADDITIONAL_COMMENTS . '</span></td>
							<td align="left"><p><b><span name="wbb_show_comments" id="wbb_show_comments"></span></b></p></td>
						</tr>
						<tr>
						    <td colspan="2" background="' . DIR_WS_IMAGES . 'space.gif'. '" height="4"></td>
						</tr>
						<tr>
						    <td colspan="2" background="' . DIR_WS_IMAGES . 'space_line2.gif' . '" height="1"></td>
						</tr>
						<tr>
						    <td colspan="2" background="' . DIR_WS_IMAGES . 'space.gif'. '" height="4"></td>
						</tr>
						<tr>
							<td align="right"><span class="title-text">' . TEXT_ORDER_REFERENCE . '</span></td>
							<td align="left"><p><b><span name="wbb_show_order_reference" id="wbb_show_order_reference"></span></b></p></td>
						</tr>
						<tr>
							<td colspan="2" height="5">'.tep_image(DIR_WS_IMAGES . 'space.gif', '', '5').'</td>
						</tr>
						</tbody>
					</table>';
	require(DIR_WS_TEMPLATES . TEMPLATENAME_INFOMIDDLE_DARK);
?>
	</div>
</div>
<!-- Floating div start -->
<link type="text/css" rel="styleSheet" HREF="<?=DIR_WS_JAVASCRIPT?>DimmingDiv/dimming.css">
<script type="text/javascript" SRC="<?=DIR_WS_JAVASCRIPT?>DimmingDiv/dimmingdiv.js"></script>
<script type="text/javascript">
	backupFloatingDivHTML('order_report');
</script>
<!-- Floating div end -->
<!--End VIP order history-->
