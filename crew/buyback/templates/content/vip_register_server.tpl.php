<table border="0" width="100%" cellspacing="0" cellpadding="0">
	<tbody>
	<tr>
		<td width="3" height="40"><?=tep_image(DIR_WS_IMAGES . 'box_side01.gif', '', '3', '40')?></td>
		<td background="<?=DIR_WS_IMAGES . 'box_side02.gif'?>">
			<table width="100%" border="0" cellpadding="0" cellspacing="2">
				<tbody>
				<tr>
					<td><?=tep_image(DIR_WS_ICONS . 'icon_03.gif', '', '10', '10', ' hspace="4"') . '&nbsp;<span class="title-text">' . HEADER_TITLE_MY_VIP_REGISTER_SERVER . '</span>' ?></td>
				</tr>
				</tbody>
			</table>
		</td>
		<td width="3" height="40"><?=tep_image(DIR_WS_IMAGES . 'box_side03.gif', '', '3', '40')?></td>
	</tr>
	<tr>
		<td width="3" background="<?=DIR_WS_IMAGES . 'box_side08.gif'?>"><?=tep_image(DIR_WS_IMAGES . 'space.gif', '', '3', '1')?></td>
		<td>
			<table border="0" cellpadding="2" cellspacing="2" width="100%">
				<tr>
					<td>
						<?=tep_draw_form('load_server', FILENAME_MY_VIP_REGISTER_SERVER)?>
						<table border="0" cellpadding="2" cellspacing="0" width="100%">
							<tr>
								<td width="10%" nowrap><p><?=TEXT_SELECT_SERVER?></p></td>
								<td width="90%"><?=tep_draw_pull_down_menu('cID', $active_game_array, $cID, 'id="cID" onChange="this.form.submit();"')?><div id="select_server"></div></td>
							</tr>
						</table>
						</form>
					</td>
				</tr>
<?	if ($cID) { ?>
				<tr>
					<td>
						<?=tep_draw_form('save_setting', FILENAME_MY_VIP_REGISTER_SERVER, 'action=save_setting&cID='.$cID, 'post', 'onSubmit="return check_working_time();"')?>
						<table border="0" cellpadding="2" cellspacing="0" width="100%">
							<tr>
								<td width="10%"><p><?=TEXT_WORKING_DAY?></p></td>
								<td width="90%">
									<p>
									<?
										for ($day_cnt = 1; $day_cnt <= 7; $day_cnt++){
											$day_checkbox = $day_cnt;
											if ($day_cnt == 7){
												$day_checkbox = 0;
											}
											echo tep_draw_checkbox_field('working_days[]', (string)$day_checkbox, (isset($working_days_array) && in_array($day_checkbox, $working_days_array) ? true : false)) . (defined('TEXT_DAY_'.$day_checkbox) ? constant('TEXT_DAY_'.$day_checkbox) : $day_checkbox).'&nbsp;';
										}
									?>
									</p>
								</td>
							</tr>
							<tr>
								<td width="10%"><p><?=TEXT_WORKING_TIME?></p></td>
								<td width="90%"><p>
								<?									
									$hour_options = array();
									for ($hours_cnt = 0; $hours_cnt < 24; $hours_cnt++) {
										$hour_options[] = array('id' => date("H:i", mktime($hours_cnt, 0, 0, date("m"), date("d"), date("Y"))),
																'text' => date("H:i", mktime($hours_cnt, 0, 0, date("m"), date("d"), date("Y")))
																);
									}
									echo tep_draw_pull_down_menu('start_working_time', $hour_options, (isset($working_time_array) && is_array($working_time_array) ? $working_time_array[0] : ''), 'id=start_working_time') . ' - ' . tep_draw_pull_down_menu('end_working_time', $hour_options, (isset($working_time_array) && is_array($working_time_array) ? $working_time_array[1] : ''), 'id=end_working_time') . '&nbsp;' . TEXT_TIME_ZONE;
								?>	
								</p></td>
							</tr>
							<tr>
								<td colspan="2" align="right"><p><?=tep_image_submit(DIR_WS_IMAGES.'button_next.gif', BUTTON_SAVE, 'SSL')?></p></td>
							</tr>
						</table>
						</form>
						<script language="JavaScript">
						function check_working_time(){
							start_time = document.getElementById('start_working_time').value;
							start_hour = start_time.substring(0,2);
							end_time = document.getElementById('end_working_time').value;
							end_hour = end_time.substring(0,2);
							if (start_hour > end_hour) {
								alert('<?=JS_ERROR_INVALID_WORKING_TIME?>');
								return false;
							}
							return true;
						}
						</script>
					</td>
				</tr>
<?	} ?>
			</table>
		</td>
		<td width="3" background="<?=DIR_WS_IMAGES . 'box_side04.gif'?>"><?=tep_image(DIR_WS_IMAGES . 'space.gif', '', '3', '1')?></td>
	</tr>
	<tr>
    	<td colspan="3" background="<?=DIR_WS_IMAGES . 'space_line2.gif'?>" height="1"><?=tep_image(DIR_WS_IMAGES . 'space.gif', '', '1', '1')?></td>
    </tr>
	<tr>
		<td width="3" background="<?=DIR_WS_IMAGES . 'box_side08.gif'?>"><?=tep_image(DIR_WS_IMAGES . 'space.gif', '', '3', '1')?></td>
		<td>
<?	if ($cID) { ?>
    		<table border="0" width="100%" height="40" cellspacing="0" cellpadding="0">
    			<tr>
    				<td colspan="5"><p><span class="redIndicator"><?=NOTE_SELECT_SERVER?></span></p></td>
    			</tr>
    			<tr>
    				<td height="40" align="right" width="5"><?=tep_image(DIR_WS_IMAGES . 'box_grey02a.gif', '', '5', '40')?></td>
    				<td class="title-text" width="60%" background="<?=DIR_WS_IMAGES?>box_grey02b.gif"><p><?=TABLE_HEADER_SERVER_NAME?></p></td>
    				<td align="center" class="title-text" width="20%" background="<?=DIR_WS_IMAGES?>box_grey02b.gif"><p><?=TABLE_HEADER_SERVER_QUANTITY?></p></td>
    				<td class="title-text" width="20%" background="<?=DIR_WS_IMAGES?>box_grey02b.gif"><p><?=TABLE_HEADER_SERVER_SELECTION?></p></td>
    				<td height="40" width="5"><?=tep_image(DIR_WS_IMAGES . 'box_grey02c.gif', '', '5', '40')?></td>
    			</tr>
    			<tr>
    				<td colspan="5">
    					<div style=" width:100%; height:400px; overflow:auto;">
	    					<table border="0" width="100%" height="50px" cellspacing="0" cellpadding="0" id="serverTable" style="overflow: hidden">
	    						<tbody>
	    						</tbody>
	    					</table>
    					</div>
    				</td>
				</tr>
    		</table>
<?	} ?>
    	</td>
    	<td width="3" background="<?=DIR_WS_IMAGES . 'box_side04.gif'?>"><?=tep_image(DIR_WS_IMAGES . 'space.gif', '', '3', '1')?></td>
    </tr>
	<tr>
		<td width="3" height="16"><?=tep_image(DIR_WS_IMAGES . 'box_side07.gif', '', '3', '16')?></td>
		<td background="<?=DIR_WS_IMAGES . 'box_side06.gif'?>"><?=tep_image(DIR_WS_IMAGES . 'space.gif', '', '', '16')?></td>
		<td width="3" height="16"><?=tep_image(DIR_WS_IMAGES . 'box_side05.gif', '', '3', '16')?></td>
	</tr>
	</tbody>
</table>
<script language="JavaScript">
	get_full_server_list('<?=$cID?>', 'A', 'serverTable');
</script>