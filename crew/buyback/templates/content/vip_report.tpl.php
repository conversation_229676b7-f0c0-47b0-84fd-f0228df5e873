<? if ($report_sql == '') {?>
<script type="text/javascript">
<!--
	function validStartAndEndDate(start_date, end_date) {
		var s = start_date.split('-');
		var e = end_date.split('-');
		
		var startDateObj = new Date(s[0],s[1],s[2]);
		var endDateObj = new Date(e[0],e[1],e[2]);
		
		if(endDateObj.getTime() < startDateObj.getTime()) {
			return false;
		} else {
			return true;
		}
	}
		
	function validate_report_form(){
		var game_id = document.getElementById('cID').value;
		
		var start_date = document.getElementById('report_input_start_date').value;
		if(start_date.length > 0) {
			if (!validateDate(start_date)) {
				alert('Start date is not a valid date format as requested!');
				document.getElementById('report_input_start_date').focus();
				document.getElementById('report_input_start_date').select();
				return false;
			}
		}
		
		var end_date = document.getElementById('report_input_end_date').value;
		if(end_date.length > 0) {
			if (!validateDate(end_date)) {
				alert('End date is not a valid date format as requested!');
				document.getElementById('report_input_end_date').focus();
				document.getElementById('report_input_end_date').select();
				return false;
			}
		}
		
		if (start_date.length > 0 && end_date.length > 0) {
			if (!validStartAndEndDate(start_date, end_date)) {
				alert('Start Date is greater than End Date!');
				document.getElementById('report_input_start_date').focus();
				document.getElementById('report_input_start_date').select();
				return false;
			}
		}
		
		if (game_id == 0){
			alert('<?=JS_ERROR_INVALIDE_GAME_ID?>');
			document.getElementById('cID').focus();
			return false;
		}
		
		return true;
	}
	
//-->
</script>
<? }?>
<table border="0" width="100%" cellspacing="0" cellpadding="0">
	<tr>
		<td width="3" height="40"><?=tep_image(DIR_WS_IMAGES . 'box_side01.gif', '', '3', '40')?></td>
		<td background="<?=DIR_WS_IMAGES . 'box_side02.gif'?>">
			<table width="100%" border="0" cellpadding="0" cellspacing="2">
				<tbody>
				<tr>
					<td><?=tep_image(DIR_WS_ICONS . 'icon_03.gif', '', '10', '10', ' hspace="4"') . '&nbsp;<span class="title-text">' . HEADER_TITLE_MY_VIP_REPORT . '</span>' ?></td>
				</tr>
				</tbody>
			</table>
		</td>
		<td width="3" height="40"><?=tep_image(DIR_WS_IMAGES . 'box_side03.gif', '', '3', '40')?></td>
	</tr>
	<tr>
		<td width="3" background="<?=DIR_WS_IMAGES . 'box_side08.gif'?>"><?=tep_image(DIR_WS_IMAGES . 'space.gif', '', '3', '1')?></td>
		<td>
			<table border="0" cellpadding="2" cellspacing="2" width="100%">
				<tbody>
<? if ($action == '' || $cID == 0) {?>
				
				<?=tep_draw_form('vip_report', FILENAME_MY_VIP_REPORT, tep_get_all_get_params(array('action')) . 'action=show_report')?>
				<tr>	
					<td width="10%"><p><?=TEXT_SELECT_GAME?></p></td>
					<td width="90%" colspan="3"><?=tep_draw_pull_down_menu('cID', $active_game_array, (isset($form_values_arr['cID']) ? $form_values_arr['cID'] : '0') , ' id="cID"')?></td>
				</tr>
				<tr>	
					<td width="10%"><p><?=TEXT_REPORT_TYPE?></p></td>
					<td width="90%" colspan="3"><?=tep_draw_pull_down_menu('report_type', $report_type_array, (isset($form_values_arr['report_type']) ? $form_values_arr['report_type'] : '1') )?></td>
				</tr>
				<tr>	
					<td width="10%"><p><?=TEXT_START_DATE?></p></td>
					<td width="30%"><?=tep_draw_input_field('report_input_start_date', (isset($form_values_arr['report_input_start_date']) && $form_values_arr['report_input_start_date'] ? $form_values_arr['report_input_start_date'] : ''), ' id="report_input_start_date" size="10" maxlength="10" onblur="if (self.gfPop) { gfPop.validateUserInput(document.vip_report.report_input_start_date); }" onKeyPress="return noEnterKey(event)" ')?>
						<a href="javascript:void(0)" onclick="if(self.gfPop)gfPop.fPopCalendar(document.vip_report.report_input_start_date);return false;" HIDEFOCUS><img name="popcal1" align="absmiddle" src="includes/javascript/PopCalendarXp/calbtn.gif" width="34" height="22" border="0" alt=""></a>
					</td>
					<td width="10%"><p><?=TEXT_END_DATE?></p></td>
					<td width="30%"><?=tep_draw_input_field('report_input_end_date', (isset($form_values_arr['report_input_end_date']) && $form_values_arr['report_input_end_date'] ? $form_values_arr['report_input_end_date'] : ''), ' id="report_input_end_date" size="10" maxlength="10" onblur="if (self.gfPop) { gfPop.validateUserInput(document.vip_report.report_input_end_date); }" onKeyPress="return noEnterKey(event)" ')?>
						<a href="javascript:void(0)" onclick="if(self.gfPop)gfPop.fPopCalendar(document.vip_report.report_input_end_date);return false;" HIDEFOCUS><img name="popcal2" align="absmiddle" src="includes/javascript/PopCalendarXp/calbtn.gif" width="34" height="22" border="0" alt=""></a>
					</td>
				</tr>
				<tr>
					<td colspan="4" align="right">
					<?=tep_image_submit(THEMA.'button_confirm.gif', BUTTON_REPORT, ' id="btn_report" name="btn_report" onClick="return validate_report_form()"', 'generalBtn') . '&nbsp;'?>
                    <?=tep_button(BUTTON_RESET, ALT_BUTTON_RESET, tep_href_link(FILENAME_MY_VIP_REPORT, 'action=reset_session'), '', 'inputButton')?>
                    </td>
				</tr>
				</form>
<? } else {
		$row_count = 0;
?>
			<tr>
				<td>
					<table border="0" cellpadding="2" cellspacing="2" width="400">
						<tr>
							<td width="10%"><p><?=TEXT_GAME?></p></td>
							<td width="30%"><p><?=(defined("DISPLAY_NAME_CAT_ID_".$cID) ? constant ("DISPLAY_NAME_CAT_ID_".$cID) : tep_get_category_name($cID, $languages_id))?></p></td>
						</tr>
						<tr>
							<td width="10%"><p><?=TEXT_REPORT_TYPE?></p></td>
							<td width="30%"><p><?=$report_type_array[$report_type-1]['text']?></p></td>
						</tr>
						<tr>
							<td width="10%"><p><?=TEXT_DATE?></p></td>
							<td width="30%"><p><?=date("Y-m-d", $vipReportObj->start_date) . ' ' .TEXT_UNTIL . ' ' .date("Y-m-d", $vipReportObj->end_date)?></p></td>
						</tr>
					</table>
				</td>
			</tr>
			</tbody>
		</table>
		<table border="0" cellpadding="2" cellspacing="2" width="100%">
			<tbody>
<?
		global $currencies;
		$_DECIMAL_PLACES_DISPLAY = 4; //Display
		$currencies->set_decimal_places($_DECIMAL_PLACES_DISPLAY);
		$total_summary = array();
		echo '<tr>
				<td class="reportBoxHeading" rowspan="1">' . TEXT_SERVER_NAME . '</td>';
		
		for($day_cnt= count($vipReportObj->start_date_display_array)-1; $day_cnt >= 0; $day_cnt--){
			echo '<td class="reportBoxHeading" align="center" nowrap="nowrap" valign="bottom" width="10%">' . date("Y-m-d", $vipReportObj->start_date_display_array[$day_cnt]) . '</td>';
		}
		echo '</tr>';
		foreach($buybackSupplierObj->products_arr as $pid => $pinfo) {
			if(isset($vipSupplierObj->registered_servers[$pid])){
				$row_style = ($row_count%2) ? 'ordersListingEven' : 'ordersListingOdd' ;
				echo '<tr class="' . $row_style . '" onMouseOver="rowOverEffect(this, \'invoiceListingRowOver\')" onMouseOut="rowOutEffect(this, \''.$row_style.'\')" onClick="rowClicked(this, \''.$row_style.'\')">
							<td class="reportRecords" valign="top" nowrap="nowrap">' . strip_tags($pinfo['categories_name']) . '</td>';
				for($day_count = count($vipReportObj->start_date_display_array)-1; $day_count >= 0; $day_count--){
					$date_now = $vipReportObj->start_date_display_array[$day_count];
					
					if (!isset($total_summary[$date_now]) || !is_array($total_summary[$date_now])) {
						$total_summary[$date_now] = array(	'quantity' => 0,
															'price' => 0
															);
					}					
					echo '<td class="reportRecords" align="center" nowrap="nowrap" valign="top" width="7%">';	
					if(isset($vipReportObj->report_info[$pid][$date_now])){
						
						$total_summary[$date_now]['quantity'] += $vipReportObj->report_info[$pid][$date_now]['quantity'];
						if($vipReportObj->report_type == 3){
							$total_summary[$date_now]['price'] += ($vipReportObj->report_info[$pid][$date_now]['quantity'] * $vipReportObj->report_info[$pid][$date_now]['price']);
						} else {
							$total_summary[$date_now]['price'] += $vipReportObj->report_info[$pid][$date_now]['price'];
						}						
						echo $vipReportObj->report_info[$pid][$date_now]['quantity'];
						if($vipReportObj->report_info[$pid][$date_now]['price'] > 0){
							$p_price = $vipReportObj->report_info[$pid][$date_now]['price'];
							$p_quantity = $vipReportObj->report_info[$pid][$date_now]['quantity'];
							if($vipReportObj->report_type == 3){
								echo '<hr noshade size="1">'. $currencies->format($p_quantity * $p_price, true, DISPLAY_CURRENCY);
							} else if ($vipReportObj->report_type == 4){
								echo '<hr noshade size="1">'. $currencies->format($p_price, false, DISPLAY_CURRENCY);
							} else {
								echo '<hr noshade size="1">'. $currencies->format($p_price, true, DISPLAY_CURRENCY);
							}
						}
					} else {
						echo '&nbsp;';
					}
					echo '</td>';
				}
				$row_count++;
			}
		}
				
		$row_style = ($row_count%2) ? 'ordersListingEven' : 'ordersListingOdd';
?>
				</tr>
				<tr class="<?=$row_style?>">
					<td class="reportRecords"><b><? switch($vipReportObj->report_type){
														case '1':
															echo TEXT_TOTAL_PRODUCTION_PER_DAYS;
															break;
														case '2':
															echo TEXT_TOTAL_STOCK_LEFT_PER_DAYS;
															break;
														case '3':
															echo TEXT_TOTAL_PRODUCTION_PER_DAYS . '<hr noshade size="1">' . TEXT_TOTAL_REVENUE_PRODUCTION_PER_DAYS;
															break;
														case '4':
															echo TEXT_TOTAL_COMPLETED_ORDERS . '<hr noshade size="1">' . TEXT_TOTAL_REVENUE_COMPLETED_ORDERS;
															break;
													}?></b></td>
<?
		foreach($total_summary as $summary_date => $total_arr){					
			
			echo '<td class="reportRecords" align="center" nowrap="nowrap" valign="top" width="7%">'.($total_arr['quantity'] > 0 ? $total_arr['quantity'] : '&nbsp;');
			
			if($total_arr['price']){
				if($vipReportObj->report_type < 4){
					echo '<hr noshade size="1">' . $currencies->format($total_arr['price'], true, DISPLAY_CURRENCY);
				} else {
					echo '<hr noshade size="1">' . $currencies->format($total_arr['price'], false, DISPLAY_CURRENCY);
				}
			}			
			echo '</td>';
		}
		echo '</tr>';
   }
?>
								
				</tbody>
			</table>
		</td>
		<td width="3" background="<?=DIR_WS_IMAGES . 'box_side04.gif'?>"><?=tep_image(DIR_WS_IMAGES . 'space.gif', '', '3', '1')?></td>
	</tr>
    <tr>
		<td width="3" height="16"><?=tep_image(DIR_WS_IMAGES . 'box_side07.gif', '', '3', '16')?></td>
		<td background="<?=DIR_WS_IMAGES . 'box_side06.gif'?>"><?=tep_image(DIR_WS_IMAGES . 'space.gif', '', '', '16')?></td>
		<td width="3" height="16"><?=tep_image(DIR_WS_IMAGES . 'box_side05.gif', '', '3', '16')?></td>
	</tr>
</table>
<div><iframe width=188 height=166 name="gToday:datetime:agenda.js:gfPop:plugins_24.js" id="gToday:datetime:agenda.js:gfPop:plugins_24.js" src="<?=DIR_WS_INCLUDES?>javascript/PopCalendarXp/ipopeng.htm" scrolling="no" frameborder="0" style="visibility:visible; z-index:999; position:absolute; top:-500px; left:-500px;"></iframe></div>