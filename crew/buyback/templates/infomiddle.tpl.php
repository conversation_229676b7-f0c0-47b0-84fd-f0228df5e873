<table border="0" width="100%" cellspacing="0" cellpadding="0">
  <tbody>
	<tr>
		<td>
			<table border="0" width="100%" cellspacing="0" cellpadding="0">
			  <tbody>
		          <tr>
					<td width="3" height="40"><?=tep_image(DIR_WS_IMAGES . 'box_side01.gif', '', '3', '40')?></td>
		            <td background="<?=DIR_WS_IMAGES . 'box_side02.gif'?>">
		            <?
		            	if ($boxHeading) {
		            ?>
		                <table width="100%" border="0" cellpadding="0" cellspacing="2">
		                    <tbody><tr>
		                      <td><?=tep_image(DIR_WS_ICONS . 'icon_03.gif', '', '10', '10', ' hspace="4"') . '&nbsp;<span class="title-text">' . $boxHeading . '</span>' ?></td>
		                    </tr>
		                    </tbody>
		                </table>
		            <?
		            	}
		            ?>
		            </td>
		            <td width="3" height="40"><?=tep_image(DIR_WS_IMAGES . 'box_side03.gif', '', '3', '40')?></td>
		          </tr>
			      <tr>
			      	<td width="3" background="<?=DIR_WS_IMAGES . 'box_side08.gif'?>"><?=tep_image(DIR_WS_IMAGES . 'space.gif', '', '3', '1')?></td>
			        <td>
			            <!-- start repeat -->
			            <table cellpadding="2" border="0" cellspacing="0" width="100%">
			                <tbody>
			                    <tr>
			                      <td>
			                      <?=$boxContent?>
			                      </td>
			                    </tr>
			                </tbody>
			            </table>
			            <!-- end repeat -->
			        </td>
			        <td width="3" background="<?=DIR_WS_IMAGES . 'box_side04.gif'?>"><?=tep_image(DIR_WS_IMAGES . 'space.gif', '', '3', '1')?></td>
			      </tr>
			      
			      <tr>
			          <td width="3" height="16"><?=tep_image(DIR_WS_IMAGES . 'box_side07.gif', '', '3', '16')?></td>
			          <td background="<?=DIR_WS_IMAGES . 'box_side06.gif'?>"><?=tep_image(DIR_WS_IMAGES . 'space.gif', '', '', '16')?></td>
			          <td width="3" height="16"><?=tep_image(DIR_WS_IMAGES . 'box_side05.gif', '', '3', '16')?></td>
			      </tr>
			  </tbody>
			</table>
		</td>
	</tr>
	<tr>
		<td class="storeBoxTransImg"><?=tep_image(DIR_WS_IMAGES . 'space.gif', '', '5','4')?><td>
	</tr>
  </tbody>
</table>	
<?
$boxContent = '';
$boxHeading = '';
?>