<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<?
define('CN_CENTER_SHOP_WIDTH', 1000);
define('CN_RIGHT_BOX_WIDTH', 200);
//Get current time
    $mtime = microtime();
//Split seconds and microseconds
    $mtime = explode(" ",$mtime);
//Create one value for start time
    $mtime = $mtime[1] + $mtime[0];
//Write start time into a variable
    $tstart = $mtime;

// give the visitors a message that the website will be down at ... time
    if ( (WARN_BEFORE_DOWN_FOR_MAINTENANCE == 'true') && (DOWN_FOR_MAINTENANCE == 'false') ) {
       	$messageStack->add('maintenance', sprintf(TEXT_BEFORE_DOWN_FOR_MAINTENANCE, PERIOD_BEFORE_DOWN_FOR_MAINTENANCE, TEXT_MAINTENANCE_PERIOD_TIME), 'warning');
    }

//--Derive theme settings Start
    $top_left_image_url = THEMA_RESOURCE . T_HEADER_LEFT;
    $top_center_background_image_url = THEMA_RESOURCE . T_HEADER_REPEAT;
    $footer_left_image_url = THEMA_RESOURCE . T_FOOTER_LEFT;
    $child_header_image_url = THEMA_RESOURCE . 'layout/childHeaderRepeat.gif';

    if (file_exists($footer_left_image_url)) {
    	$footer_left_image_dimension = getimagesize($footer_left_image_url);
    } else {
    	$footer_left_image_dimension = array(0, 0, '', '');
    }
    $footer_right_image_url = THEMA_RESOURCE . T_FOOTER_RIGHT;
    if (file_exists($footer_right_image_url)) {
    	$footer_right_image_dimension = getimagesize($footer_right_image_url);
    } else {
    	$footer_right_image_dimension = array(0, 0, '', '');
    }
    $footer_background_image_url = THEMA_RESOURCE . T_FOOTER_REPEAT;
    $footer_center_background_image_url = THEMA_RESOURCE . T_FOOTER_CENTER;
    $table_cell_height = $footer_left_image_dimension[1] > $footer_right_image_dimension[1] ? $footer_left_image_dimension[1] : $footer_right_image_dimension[1];

//--Derive theme settings End

//--Derive Alignment Start
    if ( CENTER_SHOP_ON == 'on' ) {
        $masthead_table_param = ' border="'.CENTER_SHOP_BORDER.'" align="'.CENTER_SHOP_ALIGN.'" width="'.CN_CENTER_SHOP_WIDTH.'" cellpadding="0" ';
        $outer_table_param = ' width="'.CN_CENTER_SHOP_WIDTH.'" border="'.CENTER_SHOP_BORDER.'" align="'.CENTER_SHOP_ALIGN.'" cellpadding="'.CENTER_SHOP_PADDING.'" ';
        $outer_column_left_param = '';
        $inner_table_left_param = ' width="'.LEFT_BOX_WIDTH.'" border="'.CENTER_SHOP_BORDER.'" ';
        $inner_table_center_param = ' width="'.CENTER_BOX_WIDTH.'" border="'.CENTER_SHOP_BORDER.'" align="'.CENTER_BOX_ALIGN.'" cellpadding="'.CENTER_SHOP_PADDING.'" ';
        $inner_table_right_param = ' width="'.CN_RIGHT_BOX_WIDTH.'" border="'.CENTER_SHOP_BORDER.'" align="'.RIGHT_BOX_ALIGN.'" cellpadding="'.CENTER_SHOP_PADDING.'" ';

    } else {
        $masthead_table_param = ' border="0" align="center" width="100%" cellpadding="0" ';
        $outer_table_param = ' border="0" width="100%" cellpadding="2" ';
        $outer_column_left_param = ' width="'.BOX_WIDTH.'"';
        $inner_table_left_param = ' border="0" width="'.BOX_WIDTH.'" ';
        $inner_table_center_param = ' border="0" align="center" cellpadding="0" ';
        $inner_table_right_param = ' border="0" align="center" cellpadding="0" ';
    }

//--Derive Alignment End
?>
<html <?=HTML_PARAMS?> >
	<head>
	<META HTTP-EQUIV="Expires" CONTENT="Tue, 04 Dec 2005 21:29:02 GMT">
	<meta http-equiv="Content-Type" content="text/html; charset=<?=CHARSET?>">
	<? require(DIR_WS_INCLUDES . 'meta_tags.php'); ?>
	<title><?=META_TAG_TITLE?></title>
	<base href="<?=(($request_type == 'SSL') ? HTTPS_SERVER : HTTP_SERVER) . DIR_WS_BUYBACK?>">
	<meta name="description" content="<?=META_TAG_DESCRIPTION?>">
	<meta name="keywords" content="<?=META_TAG_KEYWORDS?>">
	<link rel="shortcut icon" type="image/x-icon" href="/favicon.ico">

	<link rel="stylesheet" type="text/css" href="<?=THEMA_STYLE?>?sup_languages_id=<?=$sup_languages_id?>">
	<!--link rel="stylesheet" type="text/css" href="includes/stylesheet.css"-->
	
	<script type="text/javascript" src="<?=DIR_WS_JAVASCRIPT . 'general.js'?>"></script>
	<script type="text/javascript" src="<?=DIR_WS_JAVASCRIPT . 'xmlhttp.js'?>"></script>
	<script type="text/javascript">
	<!--
		function MM_swapImgRestore() { //v3.0
		  var i,x,a=document.MM_sr; for(i=0;a&&i<a.length&&(x=a[i])&&x.oSrc;i++) x.src=x.oSrc;
		}

		function MM_preloadImages() { //v3.0
		  var d=document; if(d.images){ if(!d.MM_p) d.MM_p=new Array();
		    var i,j=d.MM_p.length,a=MM_preloadImages.arguments; for(i=0; i<a.length; i++)
		    if (a[i].indexOf("#")!=0){ d.MM_p[j]=new Image; d.MM_p[j++].src=a[i];}}
		}

		function MM_findObj(n, d) { //v4.01
		  var p,i,x;  if(!d) d=document; if((p=n.indexOf("?"))>0&&parent.frames.length) {
		    d=parent.frames[n.substring(p+1)].document; n=n.substring(0,p);}
		  if(!(x=d[n])&&d.all) x=d.all[n]; for (i=0;!x&&i<d.forms.length;i++) x=d.forms[i][n];
		  for(i=0;!x&&d.layers&&i<d.layers.length;i++) x=MM_findObj(n,d.layers[i].document);
		  if(!x && d.getElementById) x=d.getElementById(n); return x;
		}

		function MM_swapImage() { //v3.0
		  var i,j=0,x,a=MM_swapImage.arguments; document.MM_sr=new Array; for(i=0;i<(a.length-2);i+=3)
		   if ((x=MM_findObj(a[i]))!=null){document.MM_sr[j++]=x; if(!x.oSrc) x.oSrc=x.src; x.src=a[i+2];}
		}
	//-->
	</script>
<?
 	if (isset($javascript) && trim($javascript) && file_exists(DIR_WS_JAVASCRIPT . basename($javascript))) {
 		if(pathinfo($javascript, PATHINFO_EXTENSION) == 'js') {
 			echo '<script type="text/javascript" src="'.DIR_WS_JAVASCRIPT . basename($javascript).'"></script>';
 		} else if(pathinfo($javascript, PATHINFO_EXTENSION) == 'php') {
 			require(DIR_WS_JAVASCRIPT . basename($javascript));
 		}
 	}
	
	//if (tep_not_null(GOOGLE_ANALYTICS_ACCOUNT_ID)) { // Google Analytics Tracking
?>
	
<?	//} ?>

</head>
<body marginwidth="0" onLoad="">

<? require(DIR_WS_INCLUDES . 'header.php'); ?>

	<!-- Start Masthead -->
    <table width="100%" border="0" cellpadding="0" cellspacing="0">
    	<tr style="background: url('<?=$top_center_background_image_url?>'); background-repeat: repeat-x; background-position: left top;">
			<td valign="top">
                <table cellspacing="0" <?=$masthead_table_param?>>
	            	<tr height="100%">
	            		<td align="left">
	            			<span class="systemNav"><?=tep_image($top_left_image_url, '', '137', '100')?></span>
	            		</td>
	            		<td width="90%" align="right">
	            			<table border="0" width="100%" height="100%" cellspacing="0" cellpadding="0">
	            				<tr align="center" valign="middle">
		            			    <td class="topNavCell">
		            			     	<a id="topNavLink" href="<?=tep_href_link(FILENAME_DEFAULT)?>" class="systemNav"><br><br><?=tep_image(DIR_WS_ICONS . 'icon_home.gif')?><br><?=tep_image(DIR_WS_ICONS . 'icon_02.gif', '', '10', '10', ' hspace="4"')?><?=HEADER_TITLE_TOP?></a>
		            			    </td>
									<td width="1"><?=tep_image(DIR_WS_IMAGES . 'top_nav_sep.gif', '', '3', '100')?></td>
									<td class="topNavCell">
										<a id="topNavLink" href="<?=tep_href_link(FILENAME_MY_ACCOUNT)?>" class="systemNav"><br><br><?=tep_image(DIR_WS_ICONS . 'icon_myaccount.gif')?><br><?=tep_image(DIR_WS_ICONS . 'icon_02.gif', '', '10', '10', ' hspace="4"')?><?=HEADER_TITLE_MY_ACCOUNT?></a>
									</td>
		            			    <td width="1"><?=tep_image(DIR_WS_IMAGES . 'top_nav_sep.gif', '', '3', '100')?></td>
		            			    <td class="topNavCell">
										<a id="topNavLink" href="<?=tep_href_link(FILENAME_BUYBACK)?>" class="systemNav"><br><br><?=tep_image(DIR_WS_ICONS . 'icon_sell.gif')?><br><?=tep_image(DIR_WS_ICONS . 'icon_02.gif', '', '10', '10', ' hspace="4"')?><?=HEADER_TITLE_BUYBACK?></a>
									</td>
									<td width="1"><?=tep_image(DIR_WS_IMAGES . 'top_nav_sep.gif', '', '3', '100')?></td>
									<td class="topNavCell">
										<a id="topNavLinkInactive" href="javascript:void(0);" class="inactiveLink"><br><br><?=tep_image(DIR_WS_ICONS . 'icon_buy.gif')?><br><?=tep_image(DIR_WS_ICONS . 'icon_arrow_inactive.gif', '', '10', '10', ' hspace="4"')?><?=HEADER_TITLE_CATALOG?></a>
									</td>
									<td width="1"><?=tep_image(DIR_WS_IMAGES . 'top_nav_sep.gif', '', '3', '100')?></td>
									<td class="topNavCell">
										<a id="topNavLinkInactive" href="javascript:void(0);" class="inactiveLink"><br><br><?=tep_image(DIR_WS_ICONS . 'icon_trade.gif')?><br><?=tep_image(DIR_WS_ICONS . 'icon_arrow_inactive.gif', '', '10', '10', ' hspace="4"')?><?=HEADER_TITLE_TRADE?></a>
									</td>
								</tr>
	            			</table>
	            		</td>
	            	</tr>
                </table>
      		</td>
    	</tr>
    	<tr style="background: url('<?=$child_header_image_url?>'); background-repeat: repeat-x; background-position: left top;">
			<td valign="top">
                <table cellspacing="0" <?=$masthead_table_param?>>
	            	<tr>
	            		<td align="left" height="25">
	            			<p>&nbsp;<span class="systemNav"><?=$breadcrumb->trail(' &rsaquo; ')?></span></p>
	            		</td>
	            		<td align="right" class="systemBoxText">
	            		<?
	            			$official_datetime = gmdate('D, d M Y H:i:s (\G\M\T\+\8)', time()+ (8*60*60));
	            			echo $official_datetime;
	            		?>
	            		</td>
	            		<td align="right" height="25">
	            			<table border="0" cellspacing="2" cellpadding="0">
	            				<tr>
	            			    	<td><?=tep_image(DIR_WS_ICONS . 'icon_arrow.gif')?><a href="<?=tep_href_link(FILENAME_INFOLINKS, 'id=245&content_id=286')?>" class="systemNav"> <?=HEADER_TITLE_ABOUT_US?> </a>&nbsp;&nbsp;&nbsp;</td>
	            			     	<td><?=tep_image(DIR_WS_ICONS . 'icon_arrow.gif')?><a href="<?=FILENAME_FAQ?>" class="systemNav"> <?=HEADER_TITLE_FAQ?> </a>&nbsp;&nbsp;&nbsp;</td>
	            			     	<td><?=tep_image(DIR_WS_ICONS . 'icon_arrow.gif')?><a href="<?=tep_href_link(FILENAME_INFOLINKS, 'id=247&content_id=289')?>" class="systemNav"> <?=HEADER_TITLE_CONTACT_US?></a>&nbsp;</td>
	            			 	</tr>
							</table>
	            		</td>
	            	</tr>
                </table>
      		</td>
    	</tr>
    </table>
    <!--end masthead-->

    <table cellspacing="0" <?=$outer_table_param?>>
    	<tr>
			<?
				$column = 'left';
				require(DIR_WS_INCLUDES . 'column_manager.php');
				if (count($this_file_column_blocks_arr)) {
					echo '<td class="storeBoxTransImg">'.tep_image(DIR_WS_IMAGES . 'space.gif', '', '5', '1').'</td>';
					echo '<td valign="top" '.$outer_column_left_param.'>
			      			<table cellspacing="0" cellpadding="0" '.$inner_table_left_param.'>
							<!-- left_navigation //-->';
					foreach ($this_file_column_blocks_arr as $block_filename => $block_order) {
						include_once(DIR_WS_BOXES . $block_filename);
					}
					echo '
							<!-- left_navigation_eof //-->
		    				</table>
			    		</td>
			    		';
				}
			?>
			<!-- content //-->
			<td width="100%" valign="top" align="center">
    			<table <?=$inner_table_center_param?> cellspacing="0">
    				<tr>
			    		<td colspan="3" class="storeBoxTransImg"><?=tep_image(DIR_WS_IMAGES . 'space.gif', '', '5','4')?></td>
    				</tr>
    				<tr>
			    		<td class="storeBoxTransImg"><?=tep_image(DIR_WS_IMAGES . 'space.gif', '', '5','4')?></td>
    					<td width="100%" valign="top">
						<?
							if (isset($GLOBAL_STORE_BREAK_TIME) && tep_not_null($GLOBAL_STORE_BREAK_TIME) && $GLOBAL_STORE_BREAK_TIME > 0) {
								echo '<div id="dhtmlAdhocMsg" class="messageStackError" style="background-color: #FFFF00; padding: 2px;">';
								echo tep_image(DIR_WS_ICONS . 'icon_error.gif', ICON_ERROR) . '&nbsp;' . sprintf(TEXT_MESSAGE_BREAK_TIME, ($GLOBAL_STORE_BREAK_TIME/60), ($GLOBAL_STORE_BREAK_TIME%60));
								echo '</div>';
							}
							
							//show msg when account freeze
							if (tep_session_is_registered('customer_id')) {
								if ($customers_security_obj->check_account_freeze()) {
									echo '<div id="dhtmlAccFreezeMsg" class="messageStackError" style="background-color: #FFFF00; padding: 2px;">';
									echo tep_image(DIR_WS_ICONS . 'icon_error.gif', ICON_ERROR) . '&nbsp;' . TEXT_ACCOUNT_FREEZE;
									echo '</div>';
								}
							}
						?>
							<div id="dhtmlTooltip"></div>
							<script type="text/javascript" language="javascript" src="includes/javascript/dhtml_tooltip.js"></script>
<?
						  	if ($messageStack->size($content) > 0) {
								$boxContent = '	<table cellspacing="2" cellpadding="0">
											  	<tr><td>' . $messageStack->output($content) . '</td></tr>
												</table>';
								require(DIR_WS_TEMPLATES . TEMPLATENAME_MSGMIDDLE);
						  	} // end error box
							
	                      	if ($messageStack->size('maintenance') > 0) {
	                       	 	echo $messageStack->output('maintenance');
	                      	}
							
	                      	if (isset($content_template) && file_exists(DIR_WS_CONTENT . basename($content_template))) {
	                    		require(DIR_WS_CONTENT . basename($content_template));
	                    	} elseif (isset($content) && trim($content)) {
	                    	  	require(DIR_WS_CONTENT . $content . '.tpl.php');
	                    	}
?>
						</td>
			    		<td class="storeBoxTransImg"><?=tep_image(DIR_WS_IMAGES . 'space.gif', '', '5','4')?></td>
					</tr>
    				<tr>
			    		<td colspan="3" class="storeBoxTransImg"><?=tep_image(DIR_WS_IMAGES . 'space.gif', '', '5','4')?></td>
    				</tr>
				</table>
			</td>
			<!-- content_eof //-->
			<?
				$column = 'right';
				require(DIR_WS_INCLUDES . 'column_manager.php');
				if (count($this_file_column_blocks_arr)) {
					echo '
						<td valign="top" '.$outer_column_left_param.'>
			      		<table cellspacing="0" cellpadding="0" '.$inner_table_right_param.'>
						<!-- left_navigation //-->';
					foreach ($this_file_column_blocks_arr as $block_filename => $block_order) {
						include_once(DIR_WS_BOXES . $block_filename);
					}
					echo '
						<!-- left_navigation_eof //-->
		    			</table>
			    		</td>
			    		';
			    	
			    	echo '<td class="storeBoxTransImg">'.tep_image(DIR_WS_IMAGES . 'space.gif', '', '5', '1').'</td>';
				}
			?>
  		</tr>
	</table>

	<!-- footer //-->
	<center>
	<? require(DIR_WS_INCLUDES . 'footer.php'); ?>
	</center>
	<!-- footer_eof //-->
	<script src="http://www.google-analytics.com/urchin.js" type="text/javascript"></script>
	<script type="text/javascript">
	_uacct = "UA-318255-8";
	urchinTracker();
	</script>
</body>
</html>
