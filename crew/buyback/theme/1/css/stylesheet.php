<?
	require_once ('../../../includes/configure.php');
	
	/*DEF_SMALLEST_FONT*/
	$SMALLEST_FONT = 9;
	/*DEF_SMALLEST_FONT*/

	/*DEF_SMALL_FONT*/
	$SMALL_FONT = 11;
	/*DEF_SMALL_FONT*/

	/*DEF_MEDIUM_FONT*/
	$MEDIUM_FONT = 12;
	/*DEF_MEDIUM_FONT*/

	/*DEF_LARGE_FONT*/
	$LARGE_FONT = 15;
	/*DEF_LARGE_FONT*/

	/*DEF_LARGEST_FONT*/
	$LARGEST_FONT = 16;
	/*DEF_LARGEST_FONT*/

	/*DEF_BTN_FONT*/
	$BTN_FONT = 11;
	/*DEF_BTN_FONT*/

	/*DEF_CAT_NAV_FONT*/
  	$CAT_NAV_FONT = 13;
  	/*DEF_CAT_NAV_FONT*/

	/*DEF_BTN_BORDER_TOP_WIDTH*/
	$BTN_BORDER_TOP_WIDTH = 1;
	/*DEF_BTN_BORDER_TOP_WIDTH*/

	/*DEF_BTN_BORDER_BOTTOM_WIDTH*/
	$BTN_BORDER_BOTTOM_WIDTH = 1;
	/*DEF_BTN_BORDER_BOTTOM_WIDTH*/

	/*DEF_BTN_BORDER_LEFT_WIDTH*/
	$BTN_BORDER_LEFT_WIDTH = 1;
	/*DEF_BTN_BORDER_LEFT_WIDTH*/

	/*DEF_BTN_BORDER_RIGHT_WIDTH*/
	$BTN_BORDER_RIGHT_WIDTH = 1;
	/*DEF_BTN_BORDER_RIGHT_WIDTH*/


	/*DEF_BTN_BORDER_TOP_WIDTH_OVR*/
	$BTN_BORDER_TOP_WIDTH_OVR = 1;
	/*DEF_BTN_BORDER_TOP_WIDTH_OVR*/

	/*DEF_BTN_BORDER_BOTTOM_WIDTH_OVR*/
	$BTN_BORDER_BOTTOM_WIDTH_OVR = 1;
	/*DEF_BTN_BORDER_BOTTOM_WIDTH_OVR*/

	/*DEF_BTN_BORDER_LEFT_WIDTH_OVR*/
	$BTN_BORDER_LEFT_WIDTH_OVR = 1;
	/*DEF_BTN_BORDER_LEFT_WIDTH_OVR*/

	/*DEF_BTN_BORDER_RIGHT_WIDTH_OVR*/
	$BTN_BORDER_RIGHT_WIDTH_OVR = 1;
	/*DEF_BTN_BORDER_RIGHT_WIDTH_OVR*/


	/*DEF_BTN_ADD_CART_BORDER_TOP_WIDTH*/
	$BTN_ADD_CART_BORDER_TOP_WIDTH = 1;
	/*DEF_BTN_ADD_CART_BORDER_TOP_WIDTH*/

	/*DEF_BTN_ADD_CART_BORDER_BOTTOM_WIDTH*/
	$BTN_ADD_CART_BORDER_BOTTOM_WIDTH = 1;
	/*DEF_BTN_ADD_CART_BORDER_BOTTOM_WIDTH*/

	/*DEF_BTN_ADD_CART_BORDER_LEFT_WIDTH*/
	$BTN_ADD_CART_BORDER_LEFT_WIDTH = 1;
	/*DEF_BTN_ADD_CART_BORDER_LEFT_WIDTH*/

	/*DEF_BTN_ADD_CART_BORDER_RIGHT_WIDTH*/
	$BTN_ADD_CART_BORDER_RIGHT_WIDTH = 1;
	/*DEF_BTN_ADD_CART_BORDER_RIGHT_WIDTH*/


	/*DEF_BTN_ADD_CART_BORDER_TOP_WIDTH_OVR*/
	$BTN_ADD_CART_BORDER_TOP_WIDTH_OVR = 1;
	/*DEF_BTN_ADD_CART_BORDER_TOP_WIDTH_OVR*/

	/*DEF_BTN_ADD_CART_BORDER_BOTTOM_WIDTH_OVR*/
	$BTN_ADD_CART_BORDER_BOTTOM_WIDTH_OVR = 1;
	/*DEF_BTN_ADD_CART_BORDER_BOTTOM_WIDTH_OVR*/

	/*DEF_BTN_ADD_CART_BORDER_LEFT_WIDTH_OVR*/
	$BTN_ADD_CART_BORDER_LEFT_WIDTH_OVR = 1;
	/*DEF_BTN_ADD_CART_BORDER_LEFT_WIDTH_OVR*/

	/*DEF_BTN_ADD_CART_BORDER_RIGHT_WIDTH_OVR*/
	$BTN_ADD_CART_BORDER_RIGHT_WIDTH_OVR = 1;
	/*DEF_BTN_ADD_CART_BORDER_RIGHT_WIDTH_OVR*/


	/*DEF_BTN_PRE_ORDER_BORDER_TOP_WIDTH*/
	$BTN_PRE_ORDER_BORDER_TOP_WIDTH = 1;
	/*DEF_BTN_PRE_ORDER_BORDER_TOP_WIDTH*/

	/*DEF_BTN_PRE_ORDER_BORDER_BOTTOM_WIDTH*/
	$BTN_PRE_ORDER_BORDER_BOTTOM_WIDTH = 1;
	/*DEF_BTN_PRE_ORDER_BORDER_BOTTOM_WIDTH*/

	/*DEF_BTN_PRE_ORDER_BORDER_LEFT_WIDTH*/
	$BTN_PRE_ORDER_BORDER_LEFT_WIDTH = 1;
	/*DEF_BTN_PRE_ORDER_BORDER_LEFT_WIDTH*/

	/*DEF_BTN_PRE_ORDER_BORDER_RIGHT_WIDTH*/
	$BTN_PRE_ORDER_BORDER_RIGHT_WIDTH = 1;
	/*DEF_BTN_PRE_ORDER_BORDER_RIGHT_WIDTH*/


	/*DEF_BTN_PRE_ORDER_BORDER_TOP_WIDTH_OVR*/
	$BTN_PRE_ORDER_BORDER_TOP_WIDTH_OVR = 1;
	/*DEF_BTN_PRE_ORDER_BORDER_TOP_WIDTH_OVR*/

	/*DEF_BTN_PRE_ORDER_BORDER_BOTTOM_WIDTH_OVR*/
	$BTN_PRE_ORDER_BORDER_BOTTOM_WIDTH_OVR = 1;
	/*DEF_BTN_PRE_ORDER_BORDER_BOTTOM_WIDTH_OVR*/

	/*DEF_BTN_PRE_ORDER_BORDER_LEFT_WIDTH_OVR*/
	$BTN_PRE_ORDER_BORDER_LEFT_WIDTH_OVR = 1;
	/*DEF_BTN_PRE_ORDER_BORDER_LEFT_WIDTH_OVR*/

	/*DEF_BTN_PRE_ORDER_BORDER_RIGHT_WIDTH_OVR*/
	$BTN_PRE_ORDER_BORDER_RIGHT_WIDTH_OVR = 1;
	/*DEF_BTN_PRE_ORDER_BORDER_RIGHT_WIDTH_OVR*/


	/*DEF_BTN_NO_STOCK_BORDER_TOP_WIDTH*/
	$BTN_NO_STOCK_BORDER_TOP_WIDTH = 1;
	/*DEF_BTN_NO_STOCK_BORDER_TOP_WIDTH*/

	/*DEF_BTN_NO_STOCK_BORDER_BOTTOM_WIDTH*/
	$BTN_NO_STOCK_BORDER_BOTTOM_WIDTH = 1;
	/*DEF_BTN_NO_STOCK_BORDER_BOTTOM_WIDTH*/

	/*DEF_BTN_NO_STOCK_BORDER_LEFT_WIDTH*/
	$BTN_NO_STOCK_BORDER_LEFT_WIDTH = 1;
	/*DEF_BTN_NO_STOCK_BORDER_LEFT_WIDTH*/

	/*DEF_BTN_NO_STOCK_BORDER_RIGHT_WIDTH*/
	$BTN_NO_STOCK_BORDER_RIGHT_WIDTH = 1;
	/*DEF_BTN_NO_STOCK_BORDER_RIGHT_WIDTH*/

if ($sup_languages_id == 2) {
	$SMALLEST_FONT += 1;
	$SMALL_FONT += 1;
	$MEDIUM_FONT += 1;
	$LARGE_FONT += 1;
	$LARGEST_FONT += 1;
	$BTN_FONT += 1;
}

$top_nav_bg_image_path = '/' . DIR_WS_BUYBACK . DIR_WS_IMAGES . 'top_nav_bg.gif';
$image_button_bg_image_path = '/' . DIR_WS_BUYBACK . DIR_WS_BUTTONS . 'button_bg.gif';
$loading_image_path = '/' . DIR_WS_BUYBACK . DIR_WS_IMAGES . 'loading.gif';
$hint_pointer = '/' . DIR_WS_BUYBACK . DIR_WS_IMAGES . 'pointer.gif';

$style_string = "

/*****************************************************************
	NEW IMPLEMENTATION
*****************************************************************/
div.loadingImg {
	background: url(".$loading_image_path.") no-repeat;
}

.messageStackError, .messageStackWarning {
	font-family: Verdana, Arial, sans-serif;
	font-size: {$MEDIUM_FONT}px;
	color: #CC0000;
}

.messageStackSuccess {
	font-family: Verdana, Arial, sans-serif;
	font-size: {$MEDIUM_FONT}px;
	color: #008000;
}

.fieldRequired { font-family: Verdana, Arial, sans-serif; font-size: 10px; color: #ff0000; }

#dhtmlTooltip {
	font-size: {$SMALL_FONT}px;
	position: absolute;
	width: 150px;
	border: 1px solid black;
	padding: 2px;
	background-color: lightyellow;
	visibility: hidden;
	z-index: 100;
	/*Remove below line to remove shadow. Below line should always appear last within this CSS*/
	/*filter: progid:DXImageTransform.Microsoft.Shadow(color=gray,direction=135);*/
}

/* RIGHT PANEL */
.giantSystemBox {
}

.systemBoxHeading {
    font-family: 'Arial', 'Verdana', 'Helvetica', 'sans-serif';
  	font-size: {$MEDIUM_FONT}px;
	font-weight: bold;
	color: #0082C8;
  	letter-spacing: 1px;
}

a.systemNav {
	font-family: 'Arial', 'Verdana', 'Helvetica', 'sans-serif';
	/*DEF_SYSTEM_NAV1*/
	color: #000000;
	/*DEF_SYSTEM_NAV1*/
	text-decoration: none;
  	font-size: {$SMALL_FONT}px;
}

a.systemNav:link {
	font-family: 'Arial', 'Verdana', 'Helvetica', 'sans-serif';
	/*DEF_SYSTEM_NAV1*/
	color: #000000;
	/*DEF_SYSTEM_NAV1*/
	text-decoration: none;
  	font-size: {$SMALL_FONT}px;
}

a.systemNav:visited {
	font-family: 'Arial', 'Verdana', 'Helvetica', 'sans-serif';
	/*DEF_SYSTEM_NAV1*/
	color: #000000;
	/*DEF_SYSTEM_NAV1*/
	text-decoration: none;
  	font-size: {$SMALL_FONT}px;
}

a.systemNav:hover {
	font-family: 'Arial', 'Verdana', 'Helvetica', 'sans-serif';
	/*DEF_SYSTEM_NAV1*/
	color: #000000;
	/*DEF_SYSTEM_NAV1*/
	text-decoration: none;
  	font-size: {$SMALL_FONT}px;
}

.systemBoxLabel {
	font-family: Verdana, Arial, sans-serif;
  	font-size: {$SMALL_FONT}px;
  	/*DEF_SYSTEM_LABEL*/
  	color: #000;
  	/*DEF_SYSTEM_LABEL*/
}

.systemBoxText {
	font-family: Verdana, Arial, sans-serif;
  	font-size: {$SMALL_FONT}px;
  	color: #000000; 
}

.systemBoxField {
	font-family: Verdana, Arial, sans-serif;
  	font-size: {$SMALL_FONT}px;
  	line-height: 1.5;
}

td.systemBoxTransImg, td.storeBoxTransImg {
}

/* Navigation */
span.headerNavigation {
	font-family: Verdana, Arial, sans-serif;
  	font-size: {$LARGE_FONT}px;
  	color: #999999;
}

a {
	font-family: Verdana, Arial, sans-serif;
	font-size : {$SMALL_FONT}px;
	text-decoration: none
}

a:link {
	font-family: Verdana, Arial, sans-serif;
	color: #000000;
	text-decoration: none;
}

a:visited {
	font-family: Verdana, Arial, sans-serif;
	color: #000000;
	text-decoration: none;
}

a:hover {
	font-family: Verdana, Arial, sans-serif;
	color: #000000;
	text-decoration: none;
}

a.inactiveLink,  {
	font-family: Verdana, Arial, sans-serif;
	font-size : {$SMALL_FONT}px;
	text-decoration: none
}

a.inactiveLink:link {
	font-family: Verdana, Arial, sans-serif;
	color: #CCCCCC;
	text-decoration: none;
}

a.inactiveLink:visited {
	font-family: Verdana, Arial, sans-serif;
	color: #CCCCCC;
	text-decoration: none;
}

a.inactiveLink:hover {
	font-family: Verdana, Arial, sans-serif;
	color: #CCCCCC;
	text-decoration: none;
	cursor: default;
    pointer: default;
}

a.headerNavigation {
  	font-family: Verdana, Arial, sans-serif;
  	font-size: {$LARGE_FONT}px;
  	text-decoration: none;
}

a.headerNavigation:link {
  	font-family: Verdana, Arial, sans-serif;
  	/*DEF_STORE_NAV1*/
  	color: #FFD200;
  	/*DEF_STORE_NAV1*/
  	text-decoration: none;
}

a.headerNavigation:visited {
	font-family: Verdana, Arial, sans-serif;
  	color: #FFD200;
  	text-decoration: none;
}

a.headerNavigation:hover {
  	font-family: Verdana, Arial, sans-serif;
  	/*DEF_STORE_NAV2*/
  	color: #FF8A00;
  	/*DEF_STORE_NAV2*/
  	text-decoration: none;
}

a.actionLink {
	font-family: Verdana, Arial, sans-serif;
	font-size : {$SMALL_FONT}px;
	text-decoration: none;
}

a.actionLink:link {
	font-family: Verdana, Arial, sans-serif;
	color: #000000;
	text-decoration: none;
}

a.actionLink:visited {
	font-family: Verdana, Arial, sans-serif;
	color: #000000;
	text-decoration: none;
}

a.actionLink:hover {
	font-family: Verdana, Arial, sans-serif;
	color: #0082C8;
	text-decoration: none;
}

a.highlightLink {
	font-family: Verdana, Arial, sans-serif;
	font-size : {$MEDIUM_FONT}px;
	font-weight: bold;
	text-decoration: none;
}

a.highlightLink:link {
	font-family: Verdana, Arial, sans-serif;
	font-weight: bold;
	color: #FF6000;
	text-decoration: none;
}

a.highlightLink:visited {
	font-family: Verdana, Arial, sans-serif;
	font-weight: bold;
	color: #FF6000;
	text-decoration: none;
}

a.highlightLink:hover {
	font-family: Verdana, Arial, sans-serif;
	font-weight: bold;
	color: #0082C8;
	text-decoration: none;
}

a.categoryNavigation {
  	font-family: Arial, Verdana, sans-serif;
  	font-size: {$CAT_NAV_FONT}px;
  	text-decoration: none;
}

a.categoryNavigation:link { 
  	font-family: Arial, Verdana, sans-serif;
  	/*DEF_STORE_NAV1*/
  	color: #000;
  	/*DEF_STORE_NAV1*/
  	text-decoration: none;
}

a.categoryNavigation:visited {
	font-family: Arial, Verdana, sans-serif;
  	color: #000; 
  	text-decoration: none;
}

a.categoryNavigation:hover {
  	font-family: Arial, Verdana, sans-serif;
  	/*DEF_STORE_NAV2*/
  	color: #0082C8; 
  	/*DEF_STORE_NAV2*/
  	text-decoration: none;
}

a.box, a.box:link, a.box:visited {
  	font-family: Arial, Verdana, sans-serif;
  	font-size: {$CAT_NAV_FONT}px;
  	text-decoration: none;
    padding: 1px 8px 1px 8px;
    margin: 1px;
    border: 1px solid #EDB329;
    background: #EDB329;
}

a.box:hover {
  	font-family: Arial, Verdana, sans-serif;
  	color: #000000;
  	text-decoration: none;
  	border: 1px solid #0082C8;
  	background: #0082C8;
}

a.boxSelected {
  	font-family: Arial, Verdana, sans-serif;
  	font-size: {$CAT_NAV_FONT}px;
  	text-decoration: none;
    padding: 1px 8px 1px 8px;
    margin: 1px;
    border: 1px solid #0082C8;
    background: #0082C8;
}

a.showHandOver {
	cursor: pointer;
	cursor: hand;
}

a.menuBoxVIPLink:link { 
	font-family: 'Arial', 'Verdana', 'Helvetica', 'sans-serif'; 
	font-size: {$SMALL_FONT}px; 
	color: #ff8000; 
	text-decoration: none; 
}

a.menuBoxVIPLink:visited { 
	font-family: 'Arial', 'Verdana', 'Helvetica', 'sans-serif'; 
	font-size: {$SMALL_FONT}px; 
	color: #ff8000; 
	text-decoration: none; 
}

a.menuBoxVIPLink:active { 
	font-family: 'Arial', 'Verdana', 'Helvetica', 'sans-serif'; 
	font-size: {$SMALL_FONT}px; 
	color: #ff8000; 
	text-decoration: none; 
}

a.menuBoxVIPLink:hover { 
	font-family: 'Arial', 'Verdana', 'Helvetica', 'sans-serif'; 
	font-size: {$SMALL_FONT}px; 
	color: #ff8000; 
	text-decoration: none; 
}

a.subModelLink:link { 
	font-family: 'Arial', 'Verdana', 'Helvetica', 'sans-serif'; 
	font-size: {$SMALL_FONT}px; 
	color: #0000CC; 
	text-decoration: none; 
}

a.subModelLink:visited { 
	font-family: 'Arial', 'Verdana', 'Helvetica', 'sans-serif'; 
	font-size: {$SMALL_FONT}px; 
	color: #0000CC; 
	text-decoration: none; 
}

a.subModelLink:active { 
	font-family: 'Arial', 'Verdana', 'Helvetica', 'sans-serif'; 
	font-size: {$SMALL_FONT}px; 
	color: #0000CC; 
	text-decoration: none; 
}

a.subModelLink:hover { 
	font-family: 'Arial', 'Verdana', 'Helvetica', 'sans-serif'; 
	font-size: {$SMALL_FONT}px; 
	color: #0000CC; 
	text-decoration: none; 
}

.showHandOverBuyback {
	cursor: pointer;
	cursor: hand;
}

input.generalBtn {
	cursor: pointer;
	cursor: hand;
}

input.generalBtnOver {
	cursor: pointer;
	cursor: hand;
}

input.imageBtn {
	font-family: Verdana, Helvetica, Arial, sans-serif;
	font-size: {$BTN_FONT}px;
	font-weight: bold;
	
	width: 91px;
  	height: 22px;
	background-image: url(".$image_button_bg_image_path.");
	
	/*DEF_BTN_FONT_COLOR*/
	color: #666666;
	/*DEF_BTN_FONT_COLOR*/
	/*DEF_BTN_BACKGROUND*/
	background-color: #CCCCCC;
	/*DEF_BTN_BACKGROUND*/
	cursor: pointer;
	cursor: hand;
	align: center;
	text-align:center;
}

input.imageBtnOver {
	font-family: Verdana, Helvetica, Arial, sans-serif;
	font-size: {$BTN_FONT}px;
	font-weight:bold;
	
	width: 91px;
  	height: 22px;
	background-image: url(".$image_button_bg_image_path.");
	
	color: #666666;
	cursor: pointer;
	cursor: hand;
	align: center;
	text-align:center;
}

/* Button Effect */
.inputButton, .inputButtonOver {
	cursor: pointer;
	cursor: hand;
}

/* Footer */
div.loggingFromMsg {
	font-family: Verdana, Arial, Helvetica, sans-serif;
	font-size: {$SMALL_FONT}px;
	font-weight: bold;
	text-align: center;
	color: #CC0000;
}

div.copyrightMsg { 
	font-family: Arial, Verdana, sans-serif;
	font-size: {$SMALL_FONT}px;
	text-align: center;
	color : #132B38;
}

td.pageLoadingMsg {
	font-family: Verdana, Arial, sans-serif;
  	font-size: {$SMALLEST_FONT}px;
  	text-align: center;
  	vertical-align: middle;
  	color: #999999;
}
/* info box */
.infoBoxHeading {
    font-family: 'Arial', 'Verdana', 'Helvetica', 'sans-serif';
	font-size: 9pt;
	font-weight: bold;
	color: #0082C8;
}

.infoBoxContents {
  	font-family: Verdana, Arial, sans-serif;
  	font-size: {$SMALL_FONT}px;
  	color: #000000; 
}

.boxText {
	font-family: Verdana, Arial, sans-serif;
	font-size: {$SMALL_FONT}px;
	color: #999999; 
}

/* News */
.newsOutline {
	/*DEF_LATEST_NEWS_BORDER*/
	border-right: 1px solid #331703;
	/*DEF_LATEST_NEWS_BORDER*/
	padding-right: 10px;
	border-top: 1px solid #331703;
	border-left: 1px solid #331703;
	border-bottom: 1px solid #331703;
	padding-left: 10px;
	font-family: Verdana, Arial, Helvetica, Sans-Serif;
	font-size: {$MEDIUM_FONT}px;
	padding-bottom: 5px;
	margin: 0px 0px 15px;
	color: #CCCCCC;
	padding-top: 10px;
	/*DEF_LATEST_NEWS_BG*/
	background-color: #999999;
	/*DEF_LATEST_NEWS_BG*/
}

.promotionBoxContents, .annBoxContents, .noticeBoxContents, .latestNewsBoxContents {
  	font-family: Verdana, Arial, sans-serif;
  	font-size: {$SMALL_FONT}px;
  	color: #000000;
}

td.promotionBoxHeading, td.annBoxHeading, td.noticeBoxHeading, td.latestNewsBoxHeading {
  	font-family: \'Trebuchet MS\', Verdana, sans-serif;
  	font-size: {$LARGEST_FONT}px;
  	font-style: oblique;
  	font-weight: bolder;
  	letter-spacing: 5px;
  	text-align: right;
  	/*DEF_MAIN_HEADING_NEWS_LABEL*/
  	color: #0082C8;
  	/*DEF_MAIN_HEADING_NEWS_LABEL*/
  	margin: 10 20 5 20;
}

div.promotionTitle, div.annTitle, div.noticeTitle, div.latestNewsTitle {
	font-family: \'Trebuchet MS\', Verdana, sans-serif;
	font-weight: 700;
	font-size: {$LARGE_FONT}px;
	/*DEF_NEWS_TITLE_LABEL*/
	color: #0082C8;
	/*DEF_NEWS_TITLE_LABEL*/
	/*border-bottom: 1px dashed #996600;*/
	text-align: left
}

div.promotionDate, div.annDate, div.noticeDate, div.latestNewsDate {
	font-family: \'Trebuchet MS\', Verdana, sans-serif;
	font-size: {$SMALLEST_FONT}px;
	color: #996600;
	letter-spacing: 2px
}

/* Elements */
BODY {
	/*--only works in IE anyway
	scrollbar-3dlight-color: #0A0A0A;
	scrollbar-face-color: #0A0A0A;
	scrollbar-highlight-color: #444444;
	scrollbar-shadow-color: #444444;
	scrollbar-arrow-color: #999999;
	scrollbar-track-color: #666666;
	scrollbar-darkshadow-color: #333333;
	*/
	/*background: #110700;*/
	color: #999999;
    margin: 0px;
	border: 0px;
	margin-bottom: 0;
	margin-left: 0;
	margin-right: 0;
	margin-top: 0;
}

p {
	font-size: {$SMALL_FONT}px;
	margin-bottom: 0;
	margin-top: 0;
	color: #000000;
}

td.instruction, span.instruction, p.instruction, td.font12Info, span.font12Info, p.font12Info {
  	font-family: Arial, Verdana, sans-serif;
  	font-size: {$SMALL_FONT}px;
  	color: #000000; 
}

.generalText {
	font-size: {$SMALL_FONT}px;
	margin-bottom: 0;
	margin-top: 0;
	color: #999999;
}

.generalTextShow {
	font-size: {$SMALL_FONT}px;
	margin-bottom: 0;
	margin-top: 0;
	color: #999999;
	display: block;
	display: table-row-group;
}
.generalTextHide {
	font-size: {$SMALL_FONT}px;
	margin-bottom: 0;
	margin-top: 0;
	color: #999999;
	display: none;
}

.errorText {
	font-size: {$SMALL_FONT}px;
	margin-bottom: 0;
	margin-top: 0;
	color: #999999;
	margin: 0;
	padding: 10px;
/*	border: 2px dashed red;
	color: red;*/
}
.noticeText {
	font-size: {$SMALL_FONT}px;
	margin-bottom: 0;
	margin-top: 0;
	color: #999999;
	margin: 0;
	padding: 10px;
/*	border: 2px dashed green;
	color: green;*/
}

span.redIndicator {
	color: red;
}

span.navyBlueIndicator {
	color: #0082C8;
}

FORM {
	display: inline;
}

TR.header {
  	background: #FFFFFF;
}

TD {
	font-family: Verdana, Arial, sans-serif;
}

TD.headerNavigation {
  	font-family: Verdana, Arial, sans-serif;
  	font-size: {$SMALLEST_FONT}px;
  	background: #110700;
  	color: #FFFFFF;
  	padding-bottom : 2px;
  	padding-top : 2px;
}

TD.orderHistoryTitle {
  	font-family: Verdana, Arial, sans-serif;
  	font-size: {$LARGEST_FONT}px;
  	font-weight: bold;
  	background: #F5F5F5;
  	color: #0082C8;
  	padding-bottom : 2px;
  	padding-top : 2px;
}

.orderHistoryBox {
    border: 2px solid #F5F5F5;
}

.form-captcha-textfield {
  font-family: 'Arial', 'Verdana', 'Helvetica', 'sans-serif';
  font-size: {$SMALL_FONT}px;
  color: #333333;
  background: transparent;
  height: 18px;
  /*border: 1px;*/
  border: 1px solid #666666;
}

.form-login-textfield {
  padding-right: 2px;
  padding-left: 2px;
  padding-bottom: 1px;
  padding-top: 1px;

  font-family: 'Arial', 'Verdana', 'Helvetica', 'sans-serif';
  font-size: {$SMALL_FONT}px;
  color: #333333;
  background: transparent;
  height: 18px;
  /*border: 1px;*/
  border: 1px solid #666666;
}
/* Table Properties*/
tbody.show, span.show {
	display: block;
	display: table-row-group;
}

div.show {
	display: inline;
	display: table-cell-group;
}

tbody.hide, div.hide, span.hide {
	display: none;
}

a#topNavLinkInactive {
	font-family: Arial, Verdana, Helvetica, sans-serif;
	display: block;
    width: 100%;
    height: 100%;
    text-align: center;
    padding-top: 5px;
    text-decoration: none;
}

a#topNavLinkInactive:hover {
    font-family: Arial, Verdana, Helvetica, sans-serif;
	display: block;
    width: 100%;
    height: 100%;
    text-align: center;
    padding-top: 5px;
    text-decoration: none;
}

a#topNavLink {
	font-family: Arial, Verdana, Helvetica, sans-serif;
	display: block;
    width: 100%;
    height: 100%;
    text-align: center;
    padding-top: 5px;
    text-decoration: none;
}

a#topNavLink:hover {
    BACKGROUND: url($top_nav_bg_image_path) repeat-x 0px 0px;
    font-family: Arial, Verdana, Helvetica, sans-serif;
	display: block;
    width: 100%;
    height: 100%;
    text-align: center;
    padding-top: 5px;
    text-decoration: none;
}

.topNavCell {
	width: 20%;
}

.infoLabel, td.inputLabel, p.inputLabel, div.inputLabel, span.inputLabel {
  	font-family: Arial, Verdana, sans-serif;
  	font-size: {$SMALL_FONT}px;
  	line-height: 1.5;
  	color: #000000;
}

td.inputField, p.inputField, div.inputField, span.inputField {
  	font-family: Arial, Verdana, sans-serif;
  	font-size: {$SMALL_FONT}px;
  	line-height: 1.5;
  	color: #000000;
}

.title-text {
    font-family: 'Arial', 'Verdana', 'Helvetica', 'sans-serif';
	font-size: 9pt;
	font-weight: bold;
	letter-spacing: 2;
	color: #0082C8;
}

td.invoiceRecords, td.ordersRecords, td.reportRecords, td.cfgRecords {
	font-family: Verdana, Arial, sans-serif;
  	font-size: {$SMALL_FONT}px;
  	color : #000; 
}

tr.latestNewsTableRowOver, tr.orderHistoryTableRowOver { 
    cursor: pointer; cursor: hand; 
}

tr.invoiceListingEven, tr.ordersListingEven, tr.reportListingEven {
	background: #E5E5E5; 
}

tr.invoiceListingOdd, tr.ordersListingOdd, tr.reportListingOdd {
	background: #D5D5D5; 
}

tr.invoiceListingRowOver, tr.ordersListingRowOver, tr.reportListingRowOver {
	background: #CCFFCC;
}

tr.rowSelected {
	background: #FFCC99;
}
.invoiceBoxHeading, .ordersBoxHeading, .reportBoxHeading, .commonBoxHeading, .cfgBoxHeading {
 	font-family: Verdana, Arial, sans-serif;
  	font-size: {$SMALL_FONT}px;
  	font-weight: bold;
  	color: #000; 
  	background: #D3DCE3;
}
.smallText { font-family: Verdana, Arial, sans-serif; font-size: 10px; color: #000000;}	
.main { font-family: Verdana, Arial, sans-serif; font-size: 12px;  color: #000000; }
.formAreaTitle { font-family: Tahoma, Verdana, Arial, sans-serif; font-size: 12px; font-weight: bold; color: #000000; }

.boldText {
	font-family: Verdana, Arial, sans-serif;
  	font-size: 28px;
  	font-weight: bold;
  	color : #000;
}

.listingBody {
	background: #DFDFDF;
}

.batchActionBox {
	width: 99%;
	border: 1px solid red;
	padding: 2px;
}

/* The hint to Hide and Show */
.hint {
   	display: none;
   	position: absolute;
	overflow: auto;
    width: 220px;
    border: 1px solid #c93;
    padding: 5px 5px;
    margin-left: 10px;
    background: #ffc;
}


/*****************************************************************
	END OF NEW IMPLEMENTATION
*****************************************************************/
";
echo $style_string;
?>