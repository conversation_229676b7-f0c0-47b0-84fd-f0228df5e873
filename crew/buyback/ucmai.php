<?
require('includes/application_top.php');

tep_set_time_limit(0);
require_once(DIR_WS_FUNCTIONS . 'supplier.php');
require_once(DIR_WS_CLASSES . 'buyback_supplier.php');
require_once(DIR_WS_CLASSES . 'currencies.php');
include_once(DIR_WS_CLASSES . 'vip_order.php');

$currencies = new currencies();

$buyback_game_array = tep_get_game_list_arr();

for ($game_cnt=0; $game_cnt < count($buyback_game_array); $game_cnt++) {
	$result_array = array();
	
	$game_id = $buyback_game_array[$game_cnt]['id'];
	$game_title = $buyback_game_array[$game_cnt]['text'];
	
	if ($game_id) {
		$buybackSupplierObj = new buyback_supplier($game_id);
		$currencies->set_decimal_places($buybackSupplierObj->_DECIMAL_PLACES_DISPLAY);
		$buybackSupplierObj->calculate_offer_price();
		$buybackSupplierObj->get_buyback_product_server_full_info();
		
		foreach ($buybackSupplierObj->products_arr as $products_id => $products_arr) {
			$product_display_name = tep_display_category_path($products_arr['cat_path']." > ".$products_arr['product_name'], $products_arr['cat_id'], 'catalog', false);
			
			if ((float)$products_arr['avg_offer_price'] > 0) {
				if ((int)$products_arr['is_buyback'] && $products_arr['max_qty'] > 0) {
					if ($game_id == '1496') {
						$products_arr['avg_offer_price'] = $products_arr['avg_offer_price'] * 100;
						$products_arr['max_qty'] = $products_arr['max_qty'] / 100;
					}
					$unit_price = number_format($currencies->do_raw_conversion($products_arr['avg_offer_price'], true, DISPLAY_CURRENCY), $currencies->decimal_places, $currencies->currencies[DISPLAY_CURRENCY]['decimal_point'], $currencies->currencies[DISPLAY_CURRENCY]['thousands_point']);
					$result_array[] = $product_display_name.','.$unit_price.','.$products_arr['max_qty'];
				}
			}
		}
	}
	
	//if (count($result_array)) {
		if (!$handle = fopen("download/offgamers_cn_".$game_id.".txt", 'w')) {
	         exit;
	    }
		
	    // Write to our opened file.
	    if (fwrite($handle, implode('|', $result_array)) === FALSE) {
	    	fclose($handle);
	        exit;
	    }
	    
		fclose($handle);
//	}
}
?>