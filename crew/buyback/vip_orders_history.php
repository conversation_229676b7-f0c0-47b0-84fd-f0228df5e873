<?php

require('includes/application_top.php');
require_once(DIR_WS_FUNCTIONS . 'supplier.php');
require_once(DIR_WS_CLASSES . 'buyback_supplier.php');
require_once(DIR_WS_CLASSES . 'vip_order.php');
require_once(DIR_WS_CLASSES . 'vip_groups.php');
require_once(DIR_WS_CLASSES . 'log.php');
require_once(DIR_WS_CLASSES . 'order.php');
require_once(DIR_WS_CLASSES . 'edit_order.php');
require_once(DIR_WS_CLASSES . 'payment_methods.php');
require_once(DIR_WS_CLASSES . 'affiliate.php');
require_once(DIR_WS_CLASSES . 'concurrent_process.php');

if (!tep_session_is_registered('customer_id')) {
	$messageStack->add_session(CONTENT_LOGIN, TEXT_MUST_LOGIN);
	$navigation->set_snapshot();
    tep_redirect(tep_href_link(FILENAME_LOGIN, '', 'SSL'));
}

if (!tep_session_is_registered('vip_supplier_groups_id') || $_SESSION['vip_supplier_groups_id'] < 2) {
	$messageStack->add_session(CONTENT_INDEX_DEFAULT, WARNING_MUST_BE_VIP_MEMBER);
	$navigation->set_snapshot();
    tep_redirect(tep_href_link(FILENAME_DEFAULT, '', 'SSL'));
}

tep_cancel_expirable_buyback_request();

//Define content that gets included in the center column.
//Must match $content . ".tpl.php" in templates/content/
$content = CONTENT_MY_VIP_ORDERS_HISTORY;
//Define the javascript file
$javascript = 'vip_xmlhttp.js';

define('CNBB_REMARK_MAX_QUEUE_NO', 3);
define('DISPLAY_CURRENCY_DECIMAL', 2);

//Creating session key based on filename so we know where it got its values.
$form_session_name = FILENAME_MY_VIP_ORDERS_HISTORY;
$new_session = false;
if (!tep_session_is_registered($form_session_name)) {
	$$form_session_name = array();
	tep_session_register($form_session_name);
	$new_session = true;
}
$form_values_arr = $_SESSION[$form_session_name];

if (isset($_REQUEST['action'])) {
	$action = $_REQUEST['action'];
} else {
	$action = '';
}

//default setting for search vip order history
$allow_search_num_days_ago = 100;
$vipodh_input_order_status_select = '0';
$vipodh_input_product_type_select = 1;
$vipodh_input_order_no = '';
$vipodh_input_game_select = 0;

//overwrite start date with the later date.
$start_date = $vipodh_input_start_date = date('Y-m-d') .' 00:00:00';
$end_date = $vipodh_input_end_date = date('Y-m-d') .' 23:59:59';

/**
 * Validate the post
**/
$errorCount = 0;
switch ($action) {
	case 'reset_session':
	    unset($_SESSION[$form_session_name]);
	    tep_redirect(tep_href_link(FILENAME_MY_VIP_ORDERS_HISTORY, tep_get_all_get_params(array('action')) . 'action=search'));
	    break;
	case 'confirm_sent':
		//1 = pending, 2 = processing, 3 = completed
		//trade with us (update buyback_quantity_confirmed) - buyback status = 1 to 2
		//trade with customer(update buyback_quantity_confirmed, buyback_quantity_received) - buyback status = 1 to 3 if code match, 1 to 2 if code mismatch
		$buyback_request_group_id = isset($_POST['request_group_id']) ? (int)$_POST['request_group_id'] : 0;
		$buyback_request_id = isset($_POST['request_id']) ? (int)$_POST['request_id'] : 0;
		$buyback_quantity_confirmed = isset($_POST['sent_qty'][$buyback_request_group_id]) ? (int)$_POST['sent_qty'][$buyback_request_group_id] : 0;
		//$buyback_request_customers_code = isset($_POST['trade_code'][$buyback_request_group_id]) ? (string)$_POST['trade_code'][$buyback_request_group_id] : 0;
		$buyback_request_customers_code = 0;
		$vip_error = false;
		
		if (isset($_POST['btn_cancel_'.$buyback_request_group_id])) {	// Cancel this order
			$dealing_type_select_sql = "SELECT br.buyback_dealing_type, orders_id, orders_products_id
										FROM ".TABLE_BUYBACK_REQUEST_GROUP." AS brg
										INNER JOIN ".TABLE_BUYBACK_REQUEST." AS br 
											ON (brg.buyback_request_group_id = br.buyback_request_group_id)
										WHERE brg.buyback_request_group_id = '" . tep_db_input($buyback_request_group_id) . "' 
											AND br.orders_id <> 0
											AND br.orders_products_id <> 0
											AND brg.customers_id = '" . $_SESSION['customer_id'] . "' 
											AND br.buyback_request_id='" . tep_db_input($buyback_request_id) . "'";
			$dealing_type_select_result = tep_db_query($dealing_type_select_sql);
			if ($dealing_type_select_row = tep_db_fetch_array($dealing_type_select_result)) {
				$vipOrderCancelObj = new vip_order($dealing_type_select_row['orders_products_id']);
				if ($vipOrderCancelObj->order_accepted_cancellation($_SESSION['customer_id'], $buyback_request_group_id, 'VIP_DEDUCT_ORDER_CANCEL_AFTER_ACCEPTED')) {
					$messageStack->add_session($content, SUCCESS_ORDER_HISTORY_ORDER_CANCEL_SUCCESS, 'success');
				}
				
				//unlock order for dealing type = ofp_deal_with_customers
				if ($dealing_type_select_row['buyback_dealing_type'] == 'ofp_deal_with_customers') {
					//check any others order are assigned for the same order with deal with customers
					$pending_order_select_sql = "SELECT br.buyback_dealing_type, orders_id
												FROM ".TABLE_BUYBACK_REQUEST_GROUP." AS brg
												INNER JOIN ".TABLE_BUYBACK_REQUEST." AS br 
													ON (brg.buyback_request_group_id = br.buyback_request_group_id)
												WHERE brg.buyback_status_id = '1'
													AND br.orders_id = '" . tep_db_input($dealing_type_select_row['orders_id']) . "'
													AND br.orders_products_id <> 0
													AND buyback_dealing_type = 'ofp_deal_with_customers'";
					$pending_order_select_result = tep_db_query($pending_order_select_sql);
					if (tep_db_num_rows($pending_order_select_result) == 0) {
						//check is system lock the order
						$check_order_lock_sql = "	SELECT orders_locked_by 
				    								FROM " . TABLE_ORDERS . " 
				    								WHERE orders_id = '" . tep_db_input($dealing_type_select_row['orders_id']) . "'";
						$check_order_lock_result = tep_db_query($check_order_lock_sql);
						if ($check_order_lock_row = tep_db_fetch_array($check_order_lock_result)) {
							if ($check_order_lock_row['orders_locked_by'] == 0) {
								//unlock order
								tep_customer_order_locking($dealing_type_select_row['orders_id'], '0', 'unlock');
							}
						}
					}
				}
			} else {

				$messageStack->add_session($content, ERROR_ORDER_HISTORY_ORDER_CANCEL_FAILED, 'error');
			}
		}
		
		tep_redirect(tep_href_link(FILENAME_MY_VIP_ORDERS_HISTORY, tep_get_all_get_params(array('action')) . 'action=search'));
		break;
	case 'accept_order':
		//trade with us - buyback status = 1
		//trade with customer - buyback status = 1
		//update buyback_request_quantity
		$error = false;
		$orders_products_id = isset($_POST['opId']) ? (int)$_POST['opId'] : 0;
		$character_name = isset($_POST['vip_character_name'][$orders_products_id]) ? $_POST['vip_character_name'][$orders_products_id] : '';
		$submit_qty = isset($_POST['submit_quantity'][$orders_products_id]) ? (int)$_POST['submit_quantity'][$orders_products_id] : 0; 
		
		$trade_type = isset($_POST['trade_type_'.$orders_products_id]) ? $_POST['trade_type_'.$orders_products_id] : '';
		$captcha_code = isset($_POST['captcha_code_'.$orders_products_id]) ? $_POST['captcha_code_'.$orders_products_id] : '';
		
		//$trade_customer = 'btn_trade_customer_'.$orders_products_id;
		//$trade_us = 'btn_trade_us_'.$orders_products_id;
		//$trade_cancel = 'btn_trade_cancel_'.$orders_products_id;
		$buyback_status = '1';
		$show_character = '0';
		
		$select_vip_awaiting_orders = 	"SELECT orders_products_id, products_id, vip_order_allocation_quantity, vip_order_allocation_time
										FROM " . TABLE_VIP_ORDER_ALLOCATION . "
										WHERE orders_products_id='".tep_db_input($orders_products_id)."' AND customers_id='".tep_db_input($_SESSION['customer_id'])."'";
		
		$select_vip_awaiting_orders_result = tep_db_query($select_vip_awaiting_orders);
		if ($select_vip_awaiting_orders_row = tep_db_fetch_array($select_vip_awaiting_orders_result)) {	
			$vipOrderObj = new vip_order($select_vip_awaiting_orders_row['orders_products_id']);
			$vipOrderObj->get_orders_details();
			$selected_cat_id = $vipOrderObj->order_detail['buyback_categories_id'];
			$selected_product_id = $select_vip_awaiting_orders_row['products_id'];
			
			//check order request is expired
			if (!$vipOrderObj->check_vip_mode($selected_cat_id) || $vipOrderObj->order_request_expired() || !$vipOrderObj->check_purchase_eta($select_vip_awaiting_orders_row['orders_products_id'], $selected_product_id)) {
				tep_redirect(tep_href_link(FILENAME_MY_VIP_ORDERS_HISTORY, tep_get_all_get_params(array('action'))));
			} else {
				//if (isset($_POST[$trade_cancel])) { // Cancel button is clicked
				if ($trade_type == 'trade_cancel') { // Cancel button is clicked
					//delete order request
					$vipOrderObj->order_request_cancellation($_SESSION['customer_id'], $orders_products_id, $select_vip_awaiting_orders_row['products_id'], 'VIP_DEDUCT_SUPPLIER_REJECT');
					$messageStack->add_session($content, SUCCESS_ORDER_HISTORY_ORDER_CANCEL_SUCCESS, 'success');
					tep_redirect(tep_href_link(FILENAME_MY_VIP_ORDERS_HISTORY, tep_get_all_get_params(array('action'))));
				}
				
				include("includes/addon/captcha/securimage.php");
				$img = new Securimage();
				$valid = $img->check($captcha_code);
				
				if($valid == false) {
				    $error = true;
			  	 	$messageStack->add_session($content, ERROR_INVALID_CODE);
				}
				
				if ($character_name == '') {
					$error = true;
					$messageStack->add_session($content, ERROR_NO_TRADE_CHARACTER);
				}
				
				if ($submit_qty == '' || !is_numeric($submit_qty)) {
					$error = true;
					$messageStack->add_session($content, TEXT_QUANTITY_INVALID);
				}
				
				if($submit_qty > 0){
					$buybackSupplierObj = new buyback_supplier($selected_cat_id, $selected_product_id);
					$buybackSupplierObj->vip = true;
					$buybackSupplierObj->calculate_offer_price();
					// check available qty
					$max_qty = (int)$vipOrderObj->tep_get_max_qty($selected_product_id, $select_vip_awaiting_orders_row['orders_products_id'], $_SESSION['customer_id']);
					$min_qty = (int)$buybackSupplierObj->vip_min_qty;
					$min_purchase_qty = (int)$buybackSupplierObj->vip_min_purchse_qty;
					
					if($min_purchase_qty > 0 && $min_qty > 0 && $min_purchase_qty < $max_qty){
						$upper_min_qty = (int)$max_qty - $min_purchase_qty;
						if (($submit_qty >= $min_qty && $submit_qty <= $upper_min_qty)
							|| $submit_qty == $max_qty) {
							;
						} else {
							$error = true;
							$messageStack->add_session($content, TEXT_QUANTITY_INVALID);
						}
					} else {
						if ($submit_qty < $min_qty || $submit_qty > $max_qty) {
							$messageStack->add_session($content, TEXT_QUANTITY_INVALID);
							$error = true;
						}
					}
					// Can allow <= Qty
					/*
					if ($submit_qty != $max_qty) {
						$error = true;
						$messageStack->add_session($content, TEXT_QUANTITY_MAX_NOT_MATCH);
					}
					*/
				} else {
					$error = true;
					$messageStack->add_session($content, TEXT_QUANTITY_INVALID);
				}	
				
				// Temporary Processing to prevent insert unwanted record;
				$matchcase = md5($vipOrderObj->order_detail['orders_id'].':~:'.$max_qty);
				$concurrentProcessObj = new concurrent_process(FILENAME_MY_VIP_ORDERS_HISTORY, $matchcase, $submit_qty);
				
				//usleep(rand(500000, 2000000)); // Delay execution in microseconds
				usleep(1500000);
				if($concurrentProcessObj->concurrent_process_matching()) {
					//if (array_sum($concurrentProcessObj->extra_info_arr) > $max_qty) {
						if (!$concurrentProcessObj->concurrent_process_match_insert_id()) {
							$error = true;
							$messageStack->add_session($content, ERROR_SUBMITTED_BY_OTHER_USER);
						}
					//}
				}
				
				// Trade to Offgamer or Trade to Customer
				switch ($trade_type) {
					case 'trade_us':
						$vip_mode = '1';
						$dealing_type = 'vip_deal_on_game';
						$buyback_status = '1';
						$show_character = '0';
						$vipOrderObj->calculate_trade_us_price();
						$buyback_amount =$vipOrderObj->order_detail['trade_us_price'] * $submit_qty;
						$order_unit_price = $vipOrderObj->order_detail['trade_us_price'];
						$buyback_request_customer_org_code = 0;
						$buyback_request_supplier_code = 0;
						
						break;
					case 'trade_customer':
						$vip_mode = '1';
						$dealing_type = 'ofp_deal_with_customers';
						$buyback_status = '1';
						$show_character = '0';
						$vipOrderObj->calculate_trade_customers_price($_SESSION['customer_id']);
						$buyback_amount = $vipOrderObj->order_detail['trade_customers_price'] * $submit_qty;
						$order_unit_price = $vipOrderObj->order_detail['trade_customers_price'];
						$buyback_request_customer_org_code = tep_rand(1, 9, 7);
						$buyback_request_supplier_code = tep_rand(1, 9, 7);
						
						tep_customer_order_locking($vipOrderObj->order_detail['orders_id'], '0', 'unlock');
						tep_customer_order_locking($vipOrderObj->order_detail['orders_id'], '0', 'lock');
						
						break;
				}
				
				if ($order_unit_price <= 0) {
					$error = true;
					$messageStack->add_session($content, ERROR_PLS_TRY_AGAIN);
				}
				
				if(!$error){
					$select_supplier_info = "SELECT c.customers_firstname, c.customers_lastname, c.customers_telephone, c.customers_email_address, c.customers_dob, 
											c.customers_gender, ab.entry_street_address, ab.entry_city, ab.entry_zone_id, c.customers_mobile, c.customers_msn, 
											c.customers_yahoo, c.customers_qq, c.customers_icq 		
											FROM " . TABLE_CUSTOMERS . " AS c 
											INNER JOIN " . TABLE_ADDRESS_BOOK . " AS ab
												ON(c.customers_id=ab.customers_id) 
											WHERE c.customers_id=".(int)$_SESSION['customer_id'];
								
					$select_supplier_info_result = tep_db_query($select_supplier_info);
					$select_supplier_info_row = tep_db_fetch_array($select_supplier_info_result);
					
					$expired_time_str = date("Y-m-d H:i:s", $vipOrderObj->tep_get_vip_expired_time('vip_order'));
					
					// Create buyback order
			    	$buyback_request_group_arr = array('customers_id' => (int)$_SESSION['customer_id'],
													   'buyback_request_group_date' => 'now()',
													   'buyback_request_group_expiry_date' => $expired_time_str,
													   'remote_addr' => $_SERVER['REMOTE_ADDR'],
													   'currency' => DISPLAY_CURRENCY,
													   'currency_value' => $currencies->get_value(DISPLAY_CURRENCY, 'buy'),
													   'buyback_request_group_comment' => '',
													   'buyback_request_group_served' => '1',
													   'buyback_request_group_user_type' => '1',
													   'buyback_request_contact_name' => $select_supplier_info_row['customers_firstname'].' '.$select_supplier_info_row['customers_lastname'],
													   'buyback_request_contact_telephone' => $select_supplier_info_row['customers_telephone'],
													   'buyback_request_group_site_id' => SITE_ID
														);
														
			    	tep_db_perform(TABLE_BUYBACK_REQUEST_GROUP, $buyback_request_group_arr);
			    	$buyback_request_group_id = tep_db_insert_id();
					
					
					
					$buyback_request_array = array(	'buyback_request_group_id' => $buyback_request_group_id,
												   	'products_id' => $vipOrderObj->order_detail['products_id'],
												   	'buyback_request_quantity' => $submit_qty,
												   	'buyback_amount' => $buyback_amount,
												   	'buyback_sender_character' => $character_name,
												   	'buyback_dealing_type' => $dealing_type,
												   	'buyback_unit_price' => $order_unit_price,
												   	'buyback_request_customer_org_code' => $buyback_request_customer_org_code,
													'buyback_request_supplier_code' => $buyback_request_supplier_code,
													'orders_id' => $vipOrderObj->order_detail['orders_id'],
													'orders_products_id' => $vipOrderObj->order_detail['orders_products_id']
												   );
					
					tep_db_perform(TABLE_BUYBACK_REQUEST, $buyback_request_array);
					
					// Insert buyback history comment
					$buyback_history_data_array = array('buyback_status_id' => $buyback_status,
			    			   							'buyback_request_group_id' => $buyback_request_group_id,
			    			   							'date_added' => 'now()',
			    			   							'customer_notified' => '1',
			    			   							'set_as_buyback_remarks' => '0'
			    			   							);
					tep_db_perform(TABLE_BUYBACK_STATUS_HISTORY, $buyback_history_data_array);
					
					$buyback_request_grp_array = array(	'buyback_status_id' => $buyback_status,
	    												'show_restock' => $show_character,
	    												'buyback_request_order_type' => $vip_mode
	    												);
					tep_db_perform(TABLE_BUYBACK_REQUEST_GROUP, $buyback_request_grp_array, 'update', " buyback_request_group_id='" . $buyback_request_group_id . "'");
	    			/*
					if (isset($_POST[$trade_customer])) { // trade customers
	    			   	//notify customer
	    			   	include_once(DIR_WS_LANGUAGES . 'email_customers.php');
						$trading_code_str = sprintf(EN_EMAIL_TEXT_IN_GAME_TRADE_CODE, $buyback_request_customer_org_code);
						$editOrderObj = new edit_order($vipOrderObj->order_detail['orders_id']);
						$editOrderObj->set_customers_comment($vipOrderObj->order_detail['orders_id'], '0', $trading_code_str, '1');
					}
					*/
					$supplier_info_array = array (	'buyback_request_group_id' => $buyback_request_group_id,
													'buyback_order_info_customer_firstname' => $select_supplier_info_row['customers_firstname'],
													'buyback_order_info_customer_lastname' => $select_supplier_info_row['customers_lastname'],
													'buyback_order_info_customer_telephone' => $select_supplier_info_row['customers_telephone'],
													'buyback_order_info_customer_email' => $select_supplier_info_row['customers_email_address'],
													'buyback_order_info_customer_dob' => $select_supplier_info_row['customers_dob'],
													'buyback_order_info_customer_gender' => $select_supplier_info_row['customers_gender'],
													'buyback_order_info_customer_address' => $select_supplier_info_row['entry_street_address'],
													'buyback_order_info_customer_city' => $select_supplier_info_row['entry_city'],
													'buyback_order_info_customer_state' => $select_supplier_info_row['entry_zone_id'],
													'buyback_order_info_customer_mobile' => $select_supplier_info_row['customers_mobile'],
													'buyback_order_info_customer_msn' => $select_supplier_info_row['customers_msn'],
													'buyback_order_info_customer_yahoo' => $select_supplier_info_row['customers_yahoo'],
													'buyback_order_info_customer_qq' => $select_supplier_info_row['customers_qq'],
													'buyback_order_info_customer_icq' => $select_supplier_info_row['customers_icq']
												);
					tep_db_perform(TABLE_BUYBACK_ORDER_INFO, $supplier_info_array);
					
					$select_order_allocated = "DELETE FROM " . TABLE_VIP_ORDER_ALLOCATION . " WHERE orders_products_id='" . $orders_products_id . "'";
					tep_db_query($select_order_allocated);
										
					//email to notify supplier.
					//add email template to email content chinese
					$email_subject = sprintf(EMAIL_NEW_VIP_ORDER_SUBJECT, $buyback_request_group_id, array_search($buyback_status, $PAYMENT_STATUS_ARRAY));
					$cat_path = tep_output_generated_category_path($vipOrderObj->order_detail['products_id'], 'product');
					$currencies->set_decimal_places(DISPLAY_CURRENCY_DECIMAL);
					$buyback_amount_display = (defined('EMAIL_DISPLAY_CURRENCY') ? constant('EMAIL_DISPLAY_CURRENCY') : constant('DISPLAY_CURRENCY') ). $currencies->apply_currency_exchange((double)$buyback_amount, DISPLAY_CURRENCY);
					$email_greeting = tep_mb_convert_encoding(
											tep_get_email_greeting(	tep_mb_convert_encoding($select_supplier_info_row['customers_firstname'], EMAIL_CHARSET, CHARSET), 
																	tep_mb_convert_encoding($select_supplier_info_row['customers_lastname'], EMAIL_CHARSET, CHARSET),
																	$select_supplier_info_row['customers_gender']), CHARSET, EMAIL_CHARSET);
					
					$email_text = "\n".sprintf(EMAIL_NEW_VIP_ORDERS_SUMMARY, $buyback_request_group_id, tep_date_long(date("Y-m-d")." 00:00:00"), 
									$select_supplier_info_row['customers_email_address'], $VIP_TRADE_MODE[$dealing_type], $submit_qty, $cat_path, 
									$buyback_amount_display, $buyback_amount_display, array_search($buyback_status, $BUYBACK_ORDER_STATUS_ARRAY))
											."\n\n" . EMAIL_VIP_ORDER_GUIDE . "\n" .EMAIL_LOCAL_STORE_EMAIL_SIGNATURE;
											
					tep_mail(tep_mb_convert_encoding($select_supplier_info_row['customers_firstname'] . ' ' . $select_supplier_info_row['customers_lastname'], EMAIL_CHARSET, CHARSET), $select_supplier_info_row['customers_email_address'], $email_subject, $email_greeting . $email_text, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
					
					$jr_email_to_array = tep_parse_email_string(JUNIOR_PURCHASE_TEAM_EMAIL);
					for ($i=0; $i < count($jr_email_to_array); $i++) {
						tep_mail($jr_email_to_array[$i]['name'], $jr_email_to_array[$i]['email'], $email_subject, $email_greeting . $email_text, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
					}
					
					// ADMIN EMAIL
					$unit_selling_price = (double)$buybackSupplierObj->products_arr[$vipOrderObj->order_detail['products_id']]['customer_price'];
					$profit_margin = $unit_selling_price > 0 ? ( ($unit_selling_price - $order_unit_price) / $unit_selling_price ) * 100 : '--';
					$profit_margin = sprintf('%.3f', $profit_margin);
					
					$margin_cfg_array = tep_get_cfg_setting($vipOrderObj->order_detail['products_id'], 'product', 'BUYBACK_PROFIT_MARGIN');
					if ($profit_margin < (double)$margin_cfg_array['BUYBACK_PROFIT_MARGIN']) {
						$cell_span_style = ' style="color:#ff0000;" ';
					} else {
						$cell_span_style = ' style="color:#000000;" ';
					}
					
					$profit_margin = $profit_margin . '%';
					$admin_buyback_product_list = 	'<table border="0" cellspacing="2" cellpadding="2">'.
										  			'	<tr>'.
										  			'		<td style="background-color:#FFD073; color:#000099; font-size:11px">Product</td>'.
										  			'		<td style="background-color:#FFD073; color:#000099; font-size:11px" align="center">Maximum Qty</td>'.
										 			'		<td style="background-color:#FFD073; color:#000099; font-size:11px" align="center">Forecast Available Qty</td>'.
													'		<td style="background-color:#FFD073; color:#000099; font-size:11px" align="center">Forecast Actual Qty</td>'.
										  			'		<td style="background-color:#FFD073; color:#000099; font-size:11px" align="center">Unit Price (USD)</td>' .
										  			'		<td style="background-color:#FFD073; color:#000099; font-size:11px" align="center">Unit Selling Price (USD)</td>'.
										  			'		<td style="background-color:#FFD073; color:#000099; font-size:11px" align="center">Profit Margin</td>'.
										  			'		<td style="background-color:#FFD073; color:#000099; font-size:11px">First List Selling Quantity</td>'.
										  			'		<td style="background-color:#FFD073; color:#000099; font-size:11px" align="center">Amount (USD)</td>'.
										  			'	</tr>'.
													'	<tr>'.
										  			'		<td style="background-color:#DFDFDF; color:#000; font-size:11px"><span '.$cell_span_style.'>'.$cat_path.'</span></td>'.
										  			'		<td style="background-color:#DFDFDF; color:#000; font-size:11px" align="center"><span '.$cell_span_style.'>'.$max_qty.'</span></td>'.
													'		<td style="background-color:#DFDFDF; color:#000; font-size:11px" align="center"><span '.$cell_span_style.'>'.$buybackSupplierObj->products_arr[$vipOrderObj->order_detail['products_id']]['forecast_available_qty'].'</span></td>'.
													'		<td style="background-color:#DFDFDF; color:#000; font-size:11px" align="center"><span '.$cell_span_style.'>'.$buybackSupplierObj->products_arr[$vipOrderObj->order_detail['products_id']]['forecast_actual_qty'].'</span></td>'.
											  		'		<td style="background-color:#DFDFDF; color:#000; font-size:11px" align="center"><span '.$cell_span_style.'>'.$order_unit_price.'</span></td>' .
											  		'		<td style="background-color:#DFDFDF; color:#000; font-size:11px" align="center"><span '.$cell_span_style.'>'.$unit_selling_price.'</span></td>' .
											  		'		<td style="background-color:#DFDFDF; color:#000; font-size:11px" align="center"><span '.$cell_span_style.'>'.$profit_margin.'</span></td>'.
													'		<td style="background-color:#DFDFDF; color:#000; font-size:11px" align="center"><span '.$cell_span_style.'>'.$submit_qty.'</span></td>'.
													'		<td style="background-color:#DFDFDF; color:#000; font-size:11px" align="right"><span '.$cell_span_style.'>'.$buyback_amount.'</span></td>'.
													'	</tr>'.
													'</table>';
					
					$admin_email_text = "\n".sprintf(EMAIL_ADMIN_NEW_VIP_ORDERS_SUMMARY, $buyback_request_group_id, tep_date_long(date("Y-m-d")." 00:00:00"), 
										$select_supplier_info_row['customers_email_address'], $VIP_TRADE_MODE[$dealing_type], $admin_buyback_product_list, 
										$buyback_amount_display, array_search($buyback_status, $BUYBACK_ORDER_STATUS_ARRAY))
										."\n\n" . EMAIL_VIP_ORDER_GUIDE . "\n" .EMAIL_LOCAL_STORE_EMAIL_SIGNATURE;
											
					$admin_email_to_array = tep_parse_email_string(PURCHASE_TEAM_EMAIL);			
					for ($i=0; $i < count($admin_email_to_array); $i++) {
						tep_mail($admin_email_to_array[$i]['name'], $admin_email_to_array[$i]['email'], $email_subject, $email_greeting . $admin_email_text, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
					}					
				}
				
				//$concurrentProcessObj->concurrent_process_cleanup();
				usleep(1500000);
				$update_temp_process_sql = "UPDATE LOW_PRIORITY " . TABLE_TEMP_PROCESS . " SET 
											page_name = '".FILENAME_MY_VIP_ORDERS_HISTORY.":DONE', 
											match_case = '".$vipOrderObj->order_detail['orders_id'].':~:'.$max_qty."' 
											WHERE temp_id = '".(int)$concurrentProcessObj->concurrent_process_insert_id."'";
				tep_db_query($update_temp_process_sql);
			}
		}
	    tep_redirect(tep_href_link(FILENAME_MY_VIP_ORDERS_HISTORY, tep_get_all_get_params(array('action'))));
	    
		break;
	case 'search':
	default:
		//Display pending orders on initial load (no post)
		//Assigned here so we know what to render in the switch() below.
		if (!tep_not_null($action))	$action = 'search';
		
		if (isset($_POST['vipodh_input_order_status_select']) && trim($_POST['vipodh_input_order_status_select'])) {
			$vipodh_input_order_status_select = tep_db_prepare_input($_POST['vipodh_input_order_status_select']);
		}
		if (isset($_POST['vipodh_input_product_type_select'])) $vipodh_input_product_type_select = tep_db_prepare_input($_POST['vipodh_input_product_type_select']);
		if (isset($_POST['vipodh_input_order_no'])) $vipodh_input_order_no = tep_db_prepare_input($_POST['vipodh_input_order_no']);
		if (isset($_POST['vipodh_input_game_select'])) $vipodh_input_game_select = tep_db_prepare_input($_POST['vipodh_input_game_select']);		
		$vipodh_input_start_date = isset($_POST['vipodh_input_start_date']) ? tep_db_prepare_input($_POST['vipodh_input_start_date']) : date('Y-m-d');
		
		if (strstr($vipodh_input_start_date, ':')) {
			//user selected time as well.
			if (!strstr($vipodh_input_start_date, '00:00:00')) {
				$vipodh_input_start_date = $vipodh_input_start_date.':00';
			}
		} else {
			//user did not select time
			$vipodh_input_start_date = $vipodh_input_start_date.' 00:00:00';
		}
		
		$vipodh_input_end_date = isset($_POST['vipodh_input_end_date']) ? tep_db_prepare_input($_POST['vipodh_input_end_date']) : date('Y-m-d');
		
		if (strstr($vipodh_input_end_date, ':')) {
			//user selected time as well.
			if (!strstr($vipodh_input_end_date, '23:59:59')) {
				$vipodh_input_end_date = $vipodh_input_end_date.':59';
			}
		} else {
			//user did not select time
			$vipodh_input_end_date = $vipodh_input_end_date.' 23:59:59';
		}
		
		$date_range = 0;
		$start_date_where_sql = $end_date_where_sql = ' 1 ';
		
		if ((isset($_POST['vipodh_input_start_date']) && tep_not_null($_POST['vipodh_input_start_date'])) && (isset($_POST['vipodh_input_end_date']) && tep_not_null($_POST['vipodh_input_end_date']))) {
			$date_range = tep_day_diff($vipodh_input_start_date, $vipodh_input_end_date);
			
			$start_date_where_sql = " brg.buyback_request_group_date >= '" . tep_db_input($vipodh_input_start_date) . "'";
			
			if ($date_range > $allow_search_num_days_ago) {
				$end_date_where_sql =  " brg.buyback_request_group_date <= DATE_ADD('".$vipodh_input_start_date."', INTERVAL ".$allow_search_num_days_ago." DAY)";
				$end_date = $vipodh_input_end_date = $vipodh_input_end_date = date("Y-m-d H:i:s", strtotime($vipodh_input_start_date." +".$allow_search_num_days_ago." day"));
			} else {
				$end_date_where_sql =  " brg.buyback_request_group_date <= '" . tep_db_input($vipodh_input_end_date) . "'";
			}
		} else if ((isset($_POST['vipodh_input_start_date']) && tep_not_null($_POST['vipodh_input_start_date'])) || (isset($_POST['vipodh_input_end_date']) && tep_not_null($_POST['vipodh_input_end_date']))) {
			if (tep_not_null($_POST['vipodh_input_start_date'])) {
				$date_range = tep_day_diff($vipodh_input_start_date, date("Y-m-d H:i:s"));
				
				$start_date_where_sql = " brg.buyback_request_group_date >= '" . tep_db_input($vipodh_input_start_date) . "'";
				
				if ($date_range > $allow_search_num_days_ago) {
					$end_date_where_sql =  " brg.buyback_request_group_date <= DATE_ADD('".$vipodh_input_start_date."', INTERVAL ".$allow_search_num_days_ago." DAY)";
					$end_date = $vipodh_input_end_date = $_POST['vipodh_input_end_date'] = date("Y-m-d H:i:s", strtotime($vipodh_input_start_date." +".$allow_search_num_days_ago." day"));
				} else {
					$end_date_where_sql =  " brg.buyback_request_group_date <= '" . date("Y-m-d 23:59:59") . "'";
				}
			}
			
			if (tep_not_null($_POST['vipodh_input_end_date'])) {
				$start_date_where_sql = " brg.buyback_request_group_date >= DATE_ADD('".$vipodh_input_end_date."', INTERVAL -".$allow_search_num_days_ago." DAY)";
				$end_date_where_sql =  " brg.buyback_request_group_date <= '" . tep_db_input($vipodh_input_end_date) . "'";
				
				$start_date = $vipodh_input_start_date = $_POST['vipodh_input_start_date'] = date("Y-m-d H:i:s", strtotime($vipodh_input_end_date." -".$allow_search_num_days_ago." day"));
			}
		} else {
			if (tep_not_null($vipodh_input_start_date) && tep_not_null($vipodh_input_end_date)) {
				$start_date_where_sql = " brg.buyback_request_group_date >= '" . tep_db_input($vipodh_input_start_date) . "'";
				$end_date_where_sql =  " brg.buyback_request_group_date <= '" . tep_db_input($vipodh_input_end_date) . "'";
			}
		}
		break;
}


/**
 * Save form vars to session
 */
if($new_session || $action == 'search'){
	$_SESSION[$form_session_name]['vipodh_input_order_status_select'] = $vipodh_input_order_status_select;
	$_SESSION[$form_session_name]['vipodh_input_product_type_select'] = $vipodh_input_product_type_select;
	$_SESSION[$form_session_name]['vipodh_input_order_no'] = $vipodh_input_order_no;
	$_SESSION[$form_session_name]['vipodh_input_game_select'] = $vipodh_input_game_select;
	$_SESSION[$form_session_name]['vipodh_input_start_date'] = $vipodh_input_start_date;
	$_SESSION[$form_session_name]['vipodh_input_end_date'] = $vipodh_input_end_date;
} else {
	$vipodh_input_order_status_select = $_SESSION[$form_session_name]['vipodh_input_order_status_select'];
	$vipodh_input_product_type_select = $_SESSION[$form_session_name]['vipodh_input_product_type_select'];
	$vipodh_input_order_no = $_SESSION[$form_session_name]['vipodh_input_order_no'];
	$vipodh_input_game_select = $_SESSION[$form_session_name]['vipodh_input_game_select'];
	$vipodh_input_start_date = $_SESSION[$form_session_name]['vipodh_input_start_date'];
	$vipodh_input_end_date = $_SESSION[$form_session_name]['vipodh_input_end_date'];
	$start_date = $vipodh_input_start_date;
	$end_date = $vipodh_input_end_date;
}
if ($errorCount > 0) {
	$messageStack->add_session($content, TEXT_ERROR_TRYAGAIN);
	tep_redirect(tep_href_link(FILENAME_MY_VIP_ORDERS_HISTORY) );
}

/**
 * Start preparing search form. Save doing this if redirecting on error.
 */

$vipodh_store_account_balance = tep_get_store_account_balance($_SESSION['customer_id']);

$product_type_arr[] = array('id' => 'game_currency', 'text' => TEXT_GAME_CURRENCY);

//Order statuses. Reflects the processing cycle.
$order_status_arr = tep_get_buyback_status(true);

//All order status
$default_order_status_array = array(array('id' => '0', 'text' => TEXT_ALL_ORDER_STATUS));
$order_status_arr = array_merge($default_order_status_array, $order_status_arr);

//Game listing
$wbb_game_list_arr = tep_get_game_list_arr(array(array('id' => '0', 'text' => TEXT_ALL_GAMES)));

/**
 * Prepare for display results in div
 */
//Default
$vipodh_auto_refresh_results_bool = 1;

$buyback_request_select_sql = "	SELECT brg.buyback_request_group_id, brg.buyback_request_group_date, brg.buyback_request_group_expiry_date, brg.currency, brg.currency_value, brg.show_restock, brg.buyback_status_id,
									br.buyback_request_id, br.products_id, br.buyback_amount, br.buyback_request_quantity, bs.buyback_status_name, br.buyback_quantity_confirmed, br.restock_character AS saved_restock_character, 
									br.buyback_dealing_type, br.buyback_request_supplier_code, br.orders_products_id, pc.categories_id, brg.buyback_request_order_type, brg.buyback_request_group_served, br.buyback_request_screenshot_before_name, br.buyback_request_screenshot_after_name  
								FROM ". TABLE_BUYBACK_REQUEST_GROUP . " AS brg
								LEFT JOIN " . TABLE_BUYBACK_REQUEST . " AS br 
									ON brg.buyback_request_group_id=br.buyback_request_group_id
								INNER JOIN " . TABLE_PRODUCTS_TO_CATEGORIES . " AS pc 
									ON br.products_id=pc.products_id
								INNER JOIN " . TABLE_BUYBACK_STATUS . " AS bs 
									ON ( brg.buyback_status_id=bs.buyback_status_id AND bs.language_id = '".$sup_languages_id."')
								WHERE brg.customers_id = '{$_SESSION['customer_id']}'											
									AND " . $start_date_where_sql . " 
									AND " . $end_date_where_sql . " 
									AND buyback_request_group_user_type = '1'
									AND br.orders_id <> 0 
									AND brg.buyback_request_order_type='1'
									AND br.orders_products_id <> 0";

if ((int)$vipodh_input_order_status_select != 0) {
    $buyback_request_select_sql .= " AND brg.buyback_status_id = '".$vipodh_input_order_status_select."' ";
}
if ($vipodh_input_order_no) {
	$buyback_request_select_sql .= " AND brg.buyback_request_group_id = '".$vipodh_input_order_no."' ";
}

tep_get_subcategories($category_array, $vipodh_input_game_select);
if ($category_array) {
	$category_str = implode("', '", $category_array);
} else {
	$category_str = $vipodh_input_game_select;
}

$buyback_request_select_sql .= " AND pc.categories_id IN ('" . $category_str . "')";
$buyback_request_select_sql .= "  GROUP BY brg.buyback_request_group_id
								  ORDER BY brg.buyback_status_id asc, brg.buyback_request_group_date desc";
$buyback_request_result_sql = tep_db_query($buyback_request_select_sql);
$buyback_request_num_rows = tep_db_num_rows($buyback_request_result_sql);

$searchResultsHTML = '';

$col_titles = array();
$col_titles[0] = array('width' => 70, 'align' => 'left', 'title' => '<p>'.TEXT_ORDER_NO.'</p>');
$col_titles[1] = array('width' => 110, 'align' => 'left', 'title' => '<p>'.TEXT_SERVER.'</p>');
$col_titles[2] = array('width' => 90, 'align' => 'left', 'title' => '<p>'.TABLE_HEADING_RECEIVER_CHAR_NAME.'</p>');
$col_titles[4] = array('width' => 60, 'align' => 'left', 'title' => '<p>'.TABLE_HEADING_ORDER_QUANTITY.'</p>');
$col_titles[5] = array('width' => 80, 'align' => 'left', 'title' => '<p>'.TABLE_HEADING_CONFIRM_QUANTITY.'</p>');
$col_titles[6] = array('width' => 75, 'align' => 'left', 'title' => '<p>'.TEXT_AMOUNT_WITH_CURRENCY.'</p>');
$col_titles[7] = array('width' => 35, 'align' => 'left', 'title' => '<p>'.TEXT_STATUS.'</p>');
$col_titles[8] = array('width' => 55, 'align' => 'center', 'title' => '<p>'.TEXT_ACTION.'</p>');

while($buyback_request_row = tep_db_fetch_array($buyback_request_result_sql)) {
	
	$buyback_main_cat_name = tep_get_buyback_main_cat_info($buyback_request_row['categories_id']);
	$product_display_name = tep_display_category_path(tep_get_product_path($buyback_request_row['products_id']), $buyback_request_row['categories_id'], 'catalog', false);
	
	$order_latest_remark = $restock_character = '';
	//Get restock character.
	$show_restock = 0;

	//display Restock Characters and queing message
    if ((int)$buyback_request_row['buyback_status_id'] != 1) {
        $receiver_char_name_message = '';
  	} else {
  		$show_restock = (int)$buyback_request_row['show_restock'];
//    	if ($total_queue == 0) {
    		if ($show_restock) {
    			if ((int)$buyback_request_row['buyback_status_id'] == 1) {
	        		
	        		$receiver_char_name_message = vip_order::tep_get_customer_trading_char($buyback_request_row['orders_products_id']);
	        		
	        	} 
//	        	else if ($buyback_request_row['buyback_dealing_type'] == 'vip_deal_on_game' && (int)$buyback_request_row['buyback_status_id'] == 1) {
//		        	if(tep_not_null($buyback_request_row['saved_restock_character'])){
//		        		$receiver_char_name_message = $buyback_request_row['saved_restock_character'];
//		        	} else {
//		        		$receiver_char_name_message = tep_get_buyback_restock_char($buyback_request_row['products_id']);
//		        	}
//	        	}	
    		} else {
	        	$receiver_char_name_message = TEXT_CHARACTER_NAME_IN_PREPARATION;
	        }
//	    } else if ($total_queue > 0) {
//	        $receiver_char_name_message = sprintf(TEXT_CHARACTER_NAME_IN_PREPARATION_QUEUE, $total_queue);
//	    }
	}
		
	$searchResultsHTML .= tep_draw_form('confirm_sent_' . $buyback_request_row['buyback_request_group_id'], FILENAME_MY_VIP_ORDERS_HISTORY, tep_get_all_get_params(array('action')) . 'action=confirm_sent', 'post', ' enctype="multipart/form-data"');
	$searchResultsHTML .= '<table width="100%" border="0" cellpadding="0" cellspacing="0">';
	$searchResultsHTML .= "<tr>";
	$searchResultsHTML .= "<td width='{$col_titles[0]['width']}' class='ordersRecords'><p>{$buyback_request_row['buyback_request_group_id']}</p></td>";
	$searchResultsHTML .= "<td width='{$col_titles[1]['width']}' class='ordersRecords'><p>".(defined('DISPLAY_NAME_CAT_ID_'.$buyback_main_cat_name['id']) ? constant ('DISPLAY_NAME_CAT_ID_'.$buyback_main_cat_name['id']) : trim($buyback_main_cat_name['text']))."<br/><small>" . trim($product_display_name) . "</small></p></td>";
	$searchResultsHTML .= "<td width='{$col_titles[2]['width']}' class='ordersRecords'><p>" . $receiver_char_name_message . "</p></td>";
	$searchResultsHTML .= "<td width='{$col_titles[4]['width']}' class='ordersRecords'><p>{$buyback_request_row['buyback_request_quantity']}</p></td>";
				
	//if restock character available, let supplier fill in 2nd list qty
	$searchResultsHTML .= "<td width='{$col_titles[5]['width']}' align='center'>";
	if ($show_restock && (int)$buyback_request_row['buyback_status_id'] == 1) {
		$searchResultsHTML .= tep_draw_input_field('sent_qty['.$buyback_request_row['buyback_request_group_id'].']', '', 'size="7" maxlength="9" id="sent_qty_'.$buyback_request_row['buyback_request_group_id'].'"');
	} else {
		$searchResultsHTML .= "<p>" . (int)$buyback_request_row['buyback_quantity_confirmed'] . "</p>";
	}
	$searchResultsHTML .= "</td>";
	
	$searchResultsHTML .= "<td width='{$col_titles[6]['width']}' class='ordersRecords'><p>".$currencies->format($buyback_request_row['buyback_amount'], true, $buyback_request_row['currency'], $buyback_request_row['currency_value'])."</p></td>";
	$searchResultsHTML .= "<td width='{$col_titles[7]['width']}' class='ordersRecords'><p>".$buyback_request_row['buyback_status_name']."</p></td>";
	
	//Action Column
	$searchResultsHTML .= "<td width='{$col_titles[8]['width']}'>";
	if ((int)$buyback_request_row['buyback_status_id'] == 1) {
		//Processing or pending
		$searchResultsHTML .=	tep_draw_hidden_field('request_group_id', $buyback_request_row['buyback_request_group_id'], 'size="8"') . 
								tep_draw_hidden_field('request_id', $buyback_request_row['buyback_request_id']);
									
		if ($show_restock && (int)$buyback_request_row['buyback_status_id'] == 1) {
			$searchResultsHTML .=	tep_draw_hidden_field('request_qty['.$buyback_request_row['buyback_request_group_id'].']', $buyback_request_row['buyback_request_quantity'], 'id="request_qty_'.$buyback_request_row['buyback_request_group_id'].'"') . 
									//tep_image_submit(BUTTON_CONFIRM, BUTTON_CONFIRM, ' name="odh_button_update_'. $buyback_request_row['buyback_request_group_id'].'" onClick="return validate_confirmed_qty(\''.$buyback_request_row['buyback_request_group_id'].'\');"', 'generalBtn') . '<br/>'. 
									tep_image_button('', BUTTON_CONFIRM, '', 'name="odh_button_update_'. $buyback_request_row['buyback_request_group_id'].'" id="odh_button_update_'. $buyback_request_row['buyback_request_group_id'].'" onclick="return validate_confirmed_qty(\''.(int)$buyback_request_row['buyback_request_id'].'\', \''.$buyback_request_row['buyback_request_group_id'].'\');"') . '&nbsp;' .
									tep_image_submit(BUTTON_CANCEL, BUTTON_CANCEL, ' name="btn_cancel_'. $buyback_request_row['buyback_request_group_id'].'" onClick="return confirm(\''.JS_CONFIRM_CANCEL_ORDER.'\')"', 'generalBtn');
		} else {
			if ((int)$buyback_request_row['buyback_status_id'] == 1) {
				$searchResultsHTML .= tep_image_submit(BUTTON_CANCEL, BUTTON_CANCEL, ' name="btn_cancel_'. $buyback_request_row['buyback_request_group_id'].'" onClick="return confirm(\''.JS_CONFIRM_CANCEL_ORDER.'\')"', 'generalBtn');
			} else {
				//Button for popup report
				$searchResultsHTML .= tep_image_button('', TEXT_VIEW, ' name="vip_btn_view"', 'onclick="hiddenFloatingDiv(\'order_report\');show_order_report(\''.$buyback_request_row['buyback_request_group_id'].'\', \''.$languages_id.'\', \''.SID.'\');"')."<br><span class='title-text' id='wbb_div_msgField_{$buyback_request_row['buyback_request_group_id']}'></span>";
				if (!tep_not_null($buyback_request_row['buyback_request_screenshot_after_name']) && !tep_not_null($buyback_request_row['buyback_request_screenshot_before_name']) && BUYBACK_ENABLE_UPLOAD_SCREENSHOT == 'true') {
					$searchResultsHTML .= tep_image_button('', TEXT_UPLOAD_SS, ' name="upload_ss"', 'onclick="return upload_ss(\''.$buyback_request_row['buyback_request_group_id'].'\');"');
				}
			}
		}
		
	} else {
		//Button for popup report
		$searchResultsHTML .= tep_image_button('', TEXT_VIEW, ' name="vip_btn_view"', 'onclick="hiddenFloatingDiv(\'order_report\');show_order_report(\''.$buyback_request_row['buyback_request_group_id'].'\', \''.$languages_id.'\', \''.SID.'\');"')."<br><span class='title-text' id='wbb_div_msgField_{$buyback_request_row['buyback_request_group_id']}'></span>";
		if (!tep_not_null($buyback_request_row['buyback_request_screenshot_after_name']) && !tep_not_null($buyback_request_row['buyback_request_screenshot_before_name']) && BUYBACK_ENABLE_UPLOAD_SCREENSHOT == 'true' && (int)$buyback_request_row['buyback_status_id'] != 4) {
			$searchResultsHTML .= tep_image_button('', TEXT_UPLOAD_SS, ' name="upload_ss"', 'onclick="return upload_ss(\''.$buyback_request_row['buyback_request_group_id'].'\');"');
		}
	}
	$searchResultsHTML .= "</td>";
	
	$searchResultsHTML .= "</tr>";
	if ((int)$buyback_request_row['buyback_status_id'] == 1 || (int)$buyback_request_row['buyback_status_id'] == 2) {
		$show_expiry = 1;
		if ($buyback_request_row['buyback_status_id'] == 2) {
			$total_received_select_sql = "	SELECT sum(br.buyback_quantity_received) as total_received 
											FROM ".TABLE_BUYBACK_REQUEST." AS br
											WHERE br.buyback_request_group_id = '{$buyback_request_row['buyback_request_group_id']}' 
											GROUP BY buyback_request_group_id";
			$total_received_result_sql = tep_db_query($total_received_select_sql);
			if ($total_received_row = tep_db_fetch_array($total_received_result_sql)) {
				$total_received = (int)$total_received_row['total_received'];
			}
			
			if ($total_received)	$show_expiry = 0;
		}
		
		if ($show_expiry && $buyback_request_row['buyback_request_group_expiry_date'] != '0000-00-00 00:00:00') {
			$get_time_sec = tep_day_diff(date("Y-m-d H:i:s"), $buyback_request_row['buyback_request_group_expiry_date'], 'sec');
			$expiry_mins = floor($get_time_sec/60);
			
			$searchResultsHTML .= '	<tr>
										<td></td>
										<td colspan="'.(count($col_titles)-2).'">
											<table width="100%" border="0" cellpadding="2" cellspacing="2" bgcolor="#E0ECF4">
												<tr>
													<td>
														<table width="100%" border="0" cellpadding="2" cellspacing="1">
															<tr>
																<td class="title-text" width="30%"><p style="color:red;">'.TEXT_EXPIRES.'</p></td>
																<td class="title-text" width="70%" colspan="2"><p style="color:red;">'. $expiry_mins . ' +10 ' . TEXT_MINUTES . '</p></td>
															</tr>
														</table>
													</td>
												</tr>			
											</table>
										</td>
										<td></td>
									</tr>';
		}
		
		if ((int)$buyback_request_row['buyback_status_id'] == 1) {
//			if ($total_queue > 0) {
//				if ($total_queue < (int)CNBB_REMARK_MAX_QUEUE_NO) {
//					$predefined_order_comment_select_sql = "SELECT orders_comments_text 
//															FROM " . TABLE_ORDERS_COMMENTS . "
//															WHERE orders_comments_id = '".CNBB_LOGIN_GAME_QUEUE_REMARK_ID."'";
//					$predefined_order_comment_result_sql = tep_db_query($predefined_order_comment_select_sql);
//					$predefined_order_comment_row = tep_db_fetch_array($predefined_order_comment_result_sql);
//					$order_latest_remark = $predefined_order_comment_row['orders_comments_text'];
//				}
//			} else {
				$buyback_remark_select_sql = "	SELECT comments 
												FROM " . TABLE_BUYBACK_STATUS_HISTORY . "
												WHERE buyback_request_group_id = '".tep_db_input($buyback_request_row['buyback_request_group_id'])."' 
													AND set_as_buyback_remarks = 1 
													AND customer_notified = 1 
													AND (comments IS NOT NULL AND comments <> '')";
				$buyback_remark_result_sql = tep_db_query($buyback_remark_select_sql);
				$buyback_remark_row = tep_db_fetch_array($buyback_remark_result_sql);
				$order_latest_remark = $buyback_remark_row['comments'];
//			}
		} else {
			$buyback_remark_select_sql = "	SELECT comments 
											FROM " . TABLE_BUYBACK_STATUS_HISTORY . "
											WHERE buyback_request_group_id = '".tep_db_input($buyback_request_row['buyback_request_group_id'])."' 
												AND set_as_buyback_remarks = 1 
												AND customer_notified = 1 
												AND (comments IS NOT NULL AND comments <> '')";
			$buyback_remark_result_sql = tep_db_query($buyback_remark_select_sql);
			$buyback_remark_row = tep_db_fetch_array($buyback_remark_result_sql);
			$order_latest_remark = $buyback_remark_row['comments'];
		}
	} else {
		$buyback_remark_select_sql = "	SELECT comments 
										FROM " . TABLE_BUYBACK_STATUS_HISTORY . "
										WHERE buyback_request_group_id = '".tep_db_input($buyback_request_row['buyback_request_group_id'])."' 
											AND set_as_buyback_remarks = 1 
											AND customer_notified = 1 
											AND (comments IS NOT NULL AND comments <> '')";
		$buyback_remark_result_sql = tep_db_query($buyback_remark_select_sql);
		$buyback_remark_row = tep_db_fetch_array($buyback_remark_result_sql);
		$order_latest_remark = $buyback_remark_row['comments'];
	}

	if (tep_not_null($order_latest_remark)) {
		$searchResultsHTML .= '	    <tr>
									    <td></td>
									    <td class="title-text" bgcolor="#ffffcc" colspan="'.(count($col_titles)-2).'">'.$order_latest_remark.'</td>
									    <td></td>
								    </tr>';
	}
	
	$searchResultsHTML .= '	<tr>
								<td colspan="'.(count($col_titles)).'">' . tep_image(DIR_WS_IMAGES . 'space.gif', '', '1', '8') . '</td>
							</tr>
							<tr>
								<td background="' . DIR_WS_IMAGES . 'space_line2.gif" height="1" colspan="'.count($col_titles).'">'.tep_image(DIR_WS_IMAGES . 'space.gif', '', '1', '1').'</td>
							</tr>
						  </table>
						</form>';
}

// Begin VIP awaiting order
$vip_col_titles = array();
$vip_col_titles[0] = array('width' => 180, 'align' => 'left', 'title' => '<p>'.TABLE_HEADING_SERVER_NAME.'</p>');
$vip_col_titles[1] = array('width' => 90, 'align' => 'left', 'title' => '<p>'.TABLE_HEADING_ORDER_AWAITING_ACCEPT_QUANTITY.'</p>');
$vip_col_titles[2] = array('width' => 90, 'align' => 'left', 'title' => '<p>'.TABLE_HEADING_SUBMIT_QUANTITY.'</p>');
$vip_col_titles[3] = array('width' => 90, 'align' => 'left', 'title' => '<p>'.TABLE_HEADING_SENDERS_CHARACTER_ID.'</p>');
$vip_col_titles[4] = array('width' => 160, 'align' => 'left', 'title' => '<p>'.TABLE_HEADING_PRICE_PER_UNIT.'</p>');
$vip_col_titles[5] = array('width' => 100, 'align' => 'left', 'title' => '<p>'.TABLE_HEADING_ACTION.'</p>');

$vip_order_html = '';
$awaiting_vip_order_sql = 	"SELECT voa.orders_products_id, voa.customers_id, voa.products_id, voa.vip_order_allocation_quantity, voa.vip_order_allocation_time, op.orders_id 
							FROM " . TABLE_VIP_ORDER_ALLOCATION . " AS voa 
							LEFT JOIN " . TABLE_ORDERS_PRODUCTS . " AS op 
								ON(voa.orders_products_id=op.orders_products_id) 
							WHERE voa.customers_id='" . $_SESSION['customer_id'] . "'";
$awaiting_vip_order_result = tep_db_query($awaiting_vip_order_sql);
$vip_row_count = 0;
$vip_order_awaiting_num_row = tep_db_num_rows($awaiting_vip_order_result);

while($awaiting_vip_order_row = tep_db_fetch_array($awaiting_vip_order_result)){
	$vipOrderObj = new vip_order($awaiting_vip_order_row['orders_products_id']);
	$vipOrderObj->get_orders_details();
	$bo_exact_qty_msg = '';
	
	$selected_product_id = $vipOrderObj->order_detail['products_id'];
	$selected_cat_id = $vipOrderObj->order_detail['buyback_categories_id'];
	
	//begin get qty range for this product
	$backorder_qty_msg = '';
	$buybackSupplierObj = new buyback_supplier($selected_cat_id, $selected_product_id);
	$buybackSupplierObj->vip = true;
	$buybackSupplierObj->calculate_offer_price();
	// check available qty
	$max_qty = (int)$vipOrderObj->tep_get_max_qty($selected_product_id, $awaiting_vip_order_row['orders_products_id'], $_SESSION['customer_id']);
	$min_qty = (int)$buybackSupplierObj->vip_min_qty;
	$min_purchase_qty = (int)$buybackSupplierObj->vip_min_purchse_qty;
	
	if($min_purchase_qty > 0 && $min_qty > 0 && $min_purchase_qty < $max_qty){
		$upper_min_qty = $max_qty - $min_purchase_qty;
		$bo_exact_qty_msg .= sprintf(TEXT_MATCH_BACKORDER_EXACT_AMOUNT, $upper_min_qty, $max_qty, $upper_min_qty+1, $max_qty-1);
	}
	//$bo_exact_qty_msg .= sprintf(TEXT_MATCH_BACKORDER_AMOUNT_ONLY, $max_qty);
	//end get qty range for this product
	
	if($vipOrderObj->check_vip_mode($selected_cat_id) && !$vipOrderObj->order_request_expired() && $vipOrderObj->check_purchase_eta($awaiting_vip_order_row['orders_products_id'], $selected_product_id)){
		$trade_us_html = '';
		$trade_customer_html = '';
		
		//get vip request cancellation duration
		$request_cancellation_duration = ($vipOrderObj->vip_request_cancellation_duration * 60);
		
		if(in_array('ofp_deal_with_customers', $vipOrderObj->trade_mode)){
			$vipOrderObj->calculate_trade_customers_price($_SESSION['customer_id']);
			if (isset($vipOrderObj->order_detail['trade_customers_price_display']) && $vipOrderObj->order_detail['trade_customers_price'] > 0) {
				$trade_customer_html = '<tr>'."\n".'
											<td width="'.$vip_col_titles[4]['width'].'" align="'.$vip_col_titles[4]['align'].'"><p>'.TEXT_TRADE_CUSTOMERS_PRICE. $vipOrderObj->order_detail['trade_customers_price_display'] .'&nbsp;</p></td>
											<td width="'.$vip_col_titles[5]['width'].'" align="'.$vip_col_titles[5]['align'].'"><p>'. tep_submit_button(BUTTON_TRADE_CUSTOMERS, BUTTON_TRADE_CUSTOMERS, ' name="btn_trade_customer_'.$awaiting_vip_order_row['orders_products_id'].'" onclick="return validate_request_qty(\''. $awaiting_vip_order_row['orders_products_id'] .'\', \'trade_customer\')"').'</p></td>'."\n".'
										</tr>'."\n";
			}
		}
		if(in_array('vip_deal_on_game', $vipOrderObj->trade_mode)){
			$vipOrderObj->calculate_trade_us_price();
			if (isset($vipOrderObj->order_detail['trade_us_price_display']) && $vipOrderObj->order_detail['trade_us_price'] > 0) {
				$trade_us_html = '	<tr>'."\n".'
										<td width="'.$vip_col_titles[4]['width'].'" align="'.$vip_col_titles[4]['align'].'"><p>'.TEXT_TRADE_WITH_US_PRICE. $vipOrderObj->order_detail['trade_us_price_display'] .'&nbsp;</p></td>
										<td width="'.$vip_col_titles[5]['width'].'" align="'.$vip_col_titles[5]['align'].'"><p>'. tep_submit_button(BUTTON_TRADE_WITH_US, BUTTON_TRADE_WITH_US, ' name="btn_trade_us_'.$awaiting_vip_order_row['orders_products_id'].'" onclick="return validate_request_qty(\'' . $awaiting_vip_order_row['orders_products_id'] . '\', \'trade_us\')"').'</p></td>'."\n".'
									</tr>'."\n";
			}
		}
		
		if (tep_not_null($trade_us_html) || tep_not_null($trade_customer_html)) {
			$row_style = ($vip_row_count%2)? 'orderListingEven' : 'orderListingOdd';
			//begin display record
			$vip_order_html .= tep_draw_form('accept_order_' . $awaiting_vip_order_row['orders_products_id'], FILENAME_MY_VIP_ORDERS_HISTORY, tep_get_all_get_params(array('action')) . 'action=accept_order', 'post', 'id ="accept_order_' . $awaiting_vip_order_row['orders_products_id'] . '"');
			$vip_order_html .= tep_draw_hidden_field('trade_type_'.$awaiting_vip_order_row['orders_products_id'], '', 'id="trade_type_' . $awaiting_vip_order_row['orders_products_id'] . '"');
			$vip_order_html .= '<table width="100%" border="0" cellpadding="0" cellspacing="0">'."\n";
			$vip_order_html .= '<tr valign="top" class="'.$row_style.'" height="30">'."\n".'
									<td width="'.$vip_col_titles[0]['width'].'" align="'.$vip_col_titles[0]['align'].'" class="ordersRecords"><p>' . (defined("DISPLAY_NAME_CAT_ID_".$selected_cat_id) ? constant("DISPLAY_NAME_CAT_ID_".$selected_cat_id): $vipOrderObj->order_detail['buyback_categories_name']) . '<br/><small>'. $vipOrderObj->order_detail['category_cat_path'] .'</small></p></td>'."\n".'
									<td width="'.$vip_col_titles[1]['width'].'" align="'.$vip_col_titles[1]['align'].'"  class="ordersRecords"><p>' . $vipOrderObj->tep_get_max_qty($selected_product_id, $awaiting_vip_order_row['orders_products_id'], $_SESSION['customer_id']) . '</p></td>'."\n".'
									<td width="'.$vip_col_titles[2]['width'].'" align="'.$vip_col_titles[2]['align'].'"  class="ordersRecords"><p>' . tep_draw_input_field('submit_quantity['.$awaiting_vip_order_row['orders_products_id'].']', '', ' id="submit_quantity_'. $awaiting_vip_order_row['orders_products_id'] .'" size="10"') . '</p></td>'."\n".'
									<td width="'.$vip_col_titles[3]['width'].'" align="'.$vip_col_titles[3]['align'].'"  class="ordersRecords"><p>' . tep_draw_input_field('vip_character_name['.$awaiting_vip_order_row['orders_products_id'].']', '', ' id="vip_character_name_'. $awaiting_vip_order_row['orders_products_id'] .'" size="10"') . '</p></td>'."\n".'
									<td width="'.($vip_col_titles[4]['width'] + $vip_col_titles[5]['width']).'" align="'.$vip_col_titles[4]['align'].'"  class="ordersRecords" colspan="2"><p>'.tep_draw_hidden_field('opId', $awaiting_vip_order_row['orders_products_id']).'
										<table border="0" cellpadding="0" cellspacing="0" width="100%">'."\n".
										$trade_customer_html.$trade_us_html.
										'	<tr>
												<td width="'.$vip_col_titles[4]['width'].'" align="'.$vip_col_titles[4]['align'].'">&nbsp;</td>
												<td width="'.$vip_col_titles[5]['width'].'" align="'.$vip_col_titles[5]['align'].'">'. tep_button(BUTTON_TRADE_CANCEL, BUTTON_TRADE_CANCEL, '', ' name="btn_trade_cancel_'.$awaiting_vip_order_row['orders_products_id'].'" onClick="return validate_request_qty(\''. $awaiting_vip_order_row['orders_products_id'] .'\', \'trade_cancel\');"', 'generalBtn') .'</td>
											</tr>
										</table></p></td>'."\n".'
								</tr>'."\n";
			// Captcha
			$vip_order_html .= '<tr>'."\n".'
									<td colspan="'.(count($vip_col_titles)).'" class="ordersRecords">
									'.tep_image('securimage_show.php?sid='.md5(uniqid(time()))).'
									'.tep_draw_input_field('captcha_code_'.$awaiting_vip_order_row['orders_products_id'].'', '', ' size="6" maxlength="6" id="captcha_code_'.$awaiting_vip_order_row['orders_products_id'].'"').'
									</td>
								</tr>'."\n";
			// display countdown
			$vip_order_html .= '<tr>'."\n".'
									<td class="ordersRecords" colspan="2"><p><b>' . TEXT_EXPIRES .'</b>'. gmdate('H:i:s', strtotime($awaiting_vip_order_row['vip_order_allocation_time'])+ (8*60*60) + $request_cancellation_duration) . '</p></td>'."\n".'
									<td colspan="'.(count($vip_col_titles)-1).'" class="ordersRecords"><p><span class="redIndicator">'.$bo_exact_qty_msg.'</span></p></td>
								</tr>'."\n";
			$vip_order_html .= '<tr>'."\n".'
									<td background="' . DIR_WS_IMAGES . 'space_line2.gif" height="1" colspan="'. count($vip_col_titles). '">'.tep_image(DIR_WS_IMAGES . 'space.gif', '', '1', '1').'</td>'."\n".'
								</tr>'."\n";
			
			$vip_order_html .= '</table>'."\n";
			$vip_order_html .= '</form>'."\n";
			
			//end display record
			$vip_row_count++;
		}
	}
}
//End VIP awaiting order

$num_titles = count($col_titles);
$form_values_arr = $_SESSION[$form_session_name];
$breadcrumb->add(NAVBAR_TITLE, tep_href_link(FILENAME_MY_VIP_ORDERS_HISTORY, '', 'SSL'));
require(DIR_WS_TEMPLATES . TEMPLATENAME_MAIN_PAGE);
require(DIR_WS_INCLUDES . 'application_bottom.php');
?>