<?php

require('includes/application_top.php');
require_once(DIR_WS_FUNCTIONS . 'supplier.php');
require_once(DIR_WS_CLASSES . 'buyback_supplier.php');
require_once(DIR_WS_CLASSES . 'vip_report.php');
require_once(DIR_WS_CLASSES . 'vip_supplier.php');
require_once(DIR_WS_CLASSES . 'vip_order.php');


if (!tep_session_is_registered('customer_id')) {
	$messageStack->add_session(CONTENT_LOGIN, TEXT_MUST_LOGIN);
	$navigation->set_snapshot();
    tep_redirect(tep_href_link(FILENAME_LOGIN, '', 'SSL'));
}

if (!tep_session_is_registered('vip_supplier_groups_id') || $_SESSION['vip_supplier_groups_id'] < 2) {
	$messageStack->add_session(CONTENT_INDEX_DEFAULT, WARNING_MUST_BE_VIP_MEMBER);
	$navigation->set_snapshot();
    tep_redirect(tep_href_link(FILENAME_DEFAULT, '', 'SSL'));
}

//Define content that gets included in the center column.
//Must match $content . ".tpl.php" in templates/content/
$content = CONTENT_MY_VIP_REPORT;
//Define the javascript file
$javascript = '';

$action = isset($_REQUEST['action']) ? $_REQUEST['action'] : '';
$cID = isset($_POST['cID']) ? $_POST['cID'] : '';
$report_type = isset($_POST['report_type']) ? $_POST['report_type'] : 1;
$report_start_date = isset($_POST['report_input_start_date']) ? $_POST['report_input_start_date'] : date("Y-m-d"); 
$report_end_date = isset($_POST['report_input_end_date']) ? $_POST['report_input_end_date'] : date("Y-m-d");

//Creating session key based on filename so we know where it got its values.
$form_session_name = FILENAME_MY_VIP_REPORT;
if (!tep_session_is_registered($form_session_name)) {
	$$form_session_name = array();
	tep_session_register($form_session_name);
}
$report_sql = '';

$active_game_array = array(array('id' => 0, 'text' => PULL_DOWN_DEFAULT));
tep_get_vip_categories($active_game_array);

$report_type_array = array(	array('id' => '1', 'text' => TEXT_DAILY_PRODUCTION),
							array('id' => '2', 'text' => TEXT_DAILY_STOCK_LEFT),
							array('id' => '3', 'text' => TEXT_DAILY_REVENUE_ON_OFFGAMERS_SELLING_PRICE_FOR_DAILY_PRODUCTION),
							array('id' => '4', 'text' => TEXT_DAILY_REVENUE_ON_ORDER_COMPLETED_ON_OFFGAMERS )
						);
						
switch ($action) {
	case 'show_report':	
		
		if($cID == '' || $cID == 0){
			$messageStack->add_session($content, JS_ERROR_INVALIDE_GAME_ID, 'error');
			tep_redirect(tep_href_link(FILENAME_MY_VIP_REPORT, tep_get_all_get_params(array('action'))));	
		}
		$_SESSION[$form_session_name]['cID'] = $cID;
		$_SESSION[$form_session_name]['report_type'] = $report_type;
		$_SESSION[$form_session_name]['report_input_start_date'] = $report_start_date;
		$_SESSION[$form_session_name]['report_input_end_date'] = $report_end_date;
		
		if ($cID > 0){
			$vipReportObj = new vip_report($_SESSION['customer_id'], $cID, $report_type, $report_start_date, $report_end_date);
			$buybackSupplierObj = new buyback_supplier($cID);
			$vipReportObj->vip_report_query();
			$vipSupplierObj =new vip_supplier($_SESSION['customer_id']);
			$vipSupplierObj->_get_registered_server();
		}
		break;
	case 'reset_session':
		unset($_SESSION[$form_session_name]);
		tep_redirect(tep_href_link(FILENAME_MY_VIP_REPORT, tep_get_all_get_params(array('action'))));
		break;
/*	default : 
		$action = 'default';
		break;*/
}

$form_values_arr = $_SESSION[$form_session_name];

$javascript = 'vip_xmlhttp.js';
$breadcrumb->add(NAVBAR_TITLE, tep_href_link(FILENAME_MY_VIP_REPORT, '', 'SSL'));
require(DIR_WS_TEMPLATES . TEMPLATENAME_MAIN_PAGE);
require(DIR_WS_INCLUDES . 'application_bottom.php');
?>