<?
/*
  	$Id: buyback_order_form.php,v 1.15 2011/04/11 08:23:40 weesiong Exp $
	
	Developer: <PERSON>
  	Copyright (c) 2007 SKC Ventrue
	
  	Released under the GNU General Public License
*/

require('includes/application_top.php');

if (!tep_session_is_registered('customer_id')) {
    tep_redirect(tep_href_link(FILENAME_LOGIN, '', 'SSL'));
}

require(DIR_WS_LANGUAGES . $language . '/' . FILENAME_MY_ORDER_HISTORY);
require(DIR_WS_LANGUAGES . $language . '/' . FILENAME_BUYBACK_ORDER_FORM);

require(DIR_WS_CLASSES . 'log.php');
include_once(DIR_WS_CLASSES . 'edit_order.php');
include_once(DIR_WS_FUNCTIONS . 'supplier.php');

//Define content that gets included in the center column.
//Must match $content . ".tpl.php" in templates/content/
$content = CONTENT_BUYBACK_ORDER_FORM;

//Creating session key based on filename so we know where it got its values.
$form_session_name = FILENAME_BUYBACK_ORDER_FORM;

$form_values_arr = $_SESSION[$form_session_name];

if (isset($_REQUEST['action'])) {
	$action = $_REQUEST['action'];
} else {
	$action = '';
}

$orders_id = $orders_products_id = $buyback_amount = $products_id = '';

$section_mapping = array('0' => 'TITLE_GENERAL_ACCOUNT_INFORMATION_BATTLE', 
						 '1' => 'TITLE_GENERAL_ACCOUNT_INFORMATION_WOW', 
						 '2' => 'TITLE_REGISTRATION_INFO', 
						 '3' => 'TITLE_SECRET_QUESTION', 
						 '4' => 'TITLE_SECONDARY_SECRET_QUESTION',
						 '5' => 'TITLE_WOW_INFO');

$buyback_request_group_id = isset($_GET['bo_id']) ? (int)$_GET['bo_id'] : '';

$get_buyback_info_sql = "	SELECT br.orders_id, br.orders_products_id, br.buyback_amount, br.products_id 
							FROM " . TABLE_BUYBACK_REQUEST . " AS br 
							INNER JOIN " . TABLE_BUYBACK_REQUEST_GROUP . " AS brg 
								ON (br.buyback_request_group_id = brg.buyback_request_group_id) 
							WHERE brg.buyback_request_group_id = '".tep_db_input($buyback_request_group_id)."' 
								AND brg.customers_id = '".$_SESSION['customer_id']."'";
$get_buyback_info_result = tep_db_query($get_buyback_info_sql);

if ($get_buyback_info_row = tep_db_fetch_array($get_buyback_info_result)) {
	$orders_id = $get_buyback_info_row['orders_id'];
	$orders_products_id = $get_buyback_info_row['orders_products_id'];
	$buyback_amount = $get_buyback_info_row['buyback_amount'];
	$products_id = $get_buyback_info_row['products_id'];
	
	$get_template_id_sql = "SELECT ht.hla_template_data 
							FROM " . TABLE_PRODUCTS_TEMPLATES . " AS pt
							LEFT JOIN " . TABLE_HLA_TEMPLATE . " AS ht 
								ON (pt.products_templates_id = ht.hla_template_id)
							WHERE pt.products_id  = '".tep_db_input($products_id)."'";
	$get_template_id_result = tep_db_query($get_template_id_sql);
	if ($get_template_id_row = tep_db_fetch_array($get_template_id_result)) {
		$hla_template_data = $get_template_id_row['hla_template_data'];
		$temp_arr = tep_array_unserialize($hla_template_data);
	}
}

/**
 * Validate the post
**/

switch ($action) {
	case 'submit_form':
		$error_msg = array();
		$new_data_info = array();
		$email_supplier_hla_info = '';
		
		foreach ($temp_arr as $mapping_id => $field_array) {
			$section_name = defined($section_mapping[$mapping_id]) ? constant($section_mapping[$mapping_id]) : $section_mapping[$mapping_id];
			switch ($mapping_id) {
				case '0':
				case '1':
				case '2':
				case '5':
					$hla_product_info['hla_info_stage_1'][$mapping_id]['title'] = $section_name; 
					break;
				case '3':
				case '4':
					$hla_product_info['hla_info_stage_2'][$mapping_id]['title'] = $section_name; 
					break;
			}
			$email_supplier_hla_info .= "\n".'<b>'.$section_name.'</b>'."\n";
			
			foreach ($field_array as $field_name => $field_value) {
				$$field_name = tep_db_prepare_input(htmlspecialchars($_POST[$field_name]));
				$entry_name = defined($field_value['entry']) ? constant($field_value['entry']) : $field_value['entry'];
				$email_supplier_hla_info .= $entry_name.' : ' . $$field_name . "\n";
				
				$new_data_info[$field_name] = $$field_name;
				switch ($mapping_id) {
					case '0':
					case '1':
					case '2':
					case '5':
						$hla_product_info['hla_info_stage_1'][$mapping_id]['info'][$field_name] = $$field_name; 
						break;
					case '3':
					case '4':
						$hla_product_info['hla_info_stage_2'][$mapping_id]['info'][$field_name] = $$field_name; 
						break;
				}
				
				if (isset($field_value['validation'])) {
					$validate_type_array = explode(':~:', $field_value['validation']);
					foreach($validate_type_array as $validate_type) {
						
						if (strpos($validate_type, 'mandatory') !== FALSE) {
							if ( ! tep_not_null($$field_name)) {
								$error_msg[] = $entry_name.' cannot be left blank.';
							}
						} 
						
						if (strpos($validate_type, 'max_char') !== FALSE) {
							$max_value = explode(':', $validate_type);
							
							if (isset($max_value[1]) && tep_not_null($max_value[1])) {
								if (strlen($$field_name) > $max_value[1]) {
									$error_msg[] = $entry_name.' cannot be exceed '.$max_value[1].' characters.';	
								}
							}
						}
					}
				}
			}
		}
		
		if (count($error_msg) > 0) {
			foreach ($error_msg as $error_value) {
				$messageStack->add_session($content, $error_value, 'error');
			}
			$_SESSION['submitted_info'] = $_POST;
			tep_redirect(tep_href_link(FILENAME_BUYBACK_ORDER_FORM, tep_get_all_get_params(array('action'))));
		} else {
			$get_op_id_sql = "	SELECT bs.buyback_status_name 
								FROM " . TABLE_BUYBACK_REQUEST_GROUP . " AS brg 
								LEFT JOIN " . TABLE_BUYBACK_STATUS . " AS bs 
									ON (brg.buyback_status_id = bs.buyback_status_id)
								WHERE brg.buyback_request_group_id = '".tep_db_input($buyback_request_group_id)."' 
									AND brg.customers_id = '".$_SESSION['customer_id']."'
									AND bs.language_id = '1'";
			$get_op_id_result = tep_db_query($get_op_id_sql);
			
			if ($get_op_id_row = tep_db_fetch_array($get_op_id_result)) {
				$buyback_status_name = $get_op_id_row['buyback_status_name'];
				$history_content = '';
				$email_product_path = '';
				$customer_email =  tep_get_customers_email($_SESSION['customer_id']);
				$old_data_info = array();
				$first_time_submit = false;

				$edit_order_obj = new edit_order($orders_id);
				
				$products_cat_path_sql = "	SELECT p.products_cat_path, pd.products_name 
											FROM " . TABLE_PRODUCTS . " AS p 
											INNER JOIN " . TABLE_PRODUCTS_DESCRIPTION . " AS pd 
											ON (p.products_id = pd.products_id) 
											WHERE pd.products_id = '" . tep_db_input($products_id) . "' 
											AND language_id = '1'";
				$products_cat_path_result = tep_db_query($products_cat_path_sql);
				if($products_cat_path_row = tep_db_fetch_array($products_cat_path_result)) {
					$order_products_item_select_sql = "	SELECT orders_products_item_info 
														FROM " . TABLE_ORDERS_PRODUCTS_ITEM . " 
														WHERE orders_products_id = '" . $orders_products_id . "' 
														ORDER BY products_hla_characters_id LIMIT 1";
					$order_products_item_result_sql = tep_db_query($order_products_item_select_sql);
					if ($order_products_item_row = tep_db_fetch_array($order_products_item_result_sql)) {
						$orders_products_item_info = tep_array_unserialize($order_products_item_row['orders_products_item_info']);
						$ref_id = $orders_products_item_info['products_ref_id'];
						
						// Insert log in edit order page
						$history_content = TEXT_BUYBACK_ORDER_ITEMS_DELIVERED_REMARK . "\n";
						$history_content .= "&raquo; " . $products_cat_path_row["products_name"] . ' > REF#' . $ref_id . "\tx 1";
						$email_product_path = $products_cat_path_row["products_name"] . ' > REF#' . $ref_id;
					}
				}
				
				$hla_info_stage_1_arr = tep_draw_products_extra_info($orders_products_id, 'hla_info_stage_1');
							
				if ( ! tep_not_null($hla_info_stage_1_arr)) {
					$first_time_submit = true;
				}
				
				if (!$first_time_submit) {
					$hla_info_stage_1 = tep_array_unserialize($hla_info_stage_1_arr['hla_info_stage_1']);
					foreach ($hla_info_stage_1 as $stage_1_info) {
						foreach ($stage_1_info['info'] as $stage_1_key => $stage_1_value) {
							$old_data_info[$stage_1_key] = $stage_1_value;
						}
					}
					
					$hla_info_stage_2_arr = tep_draw_products_extra_info($orders_products_id, 'hla_info_stage_2');
					
					$hla_info_stage_2 = tep_array_unserialize($hla_info_stage_2_arr['hla_info_stage_2']);
					foreach ($hla_info_stage_2 as $stage_2_key => $stage_2_value) {
						$old_data_info[$stage_2_key] = $stage_2_value;
					}
					
					$diff_data_info = array_diff($new_data_info, $old_data_info);
					if (count($diff_data_info) > 0) {
						$changes_str = 'Changes made on type info:<br />';
						foreach ($diff_data_info as $diff_key => $diff_value) {
							$changes_str .= $diff_key.'<br />';
							//$changes_str .= '<b>'.$diff_key.'</b>: ' . $old_data_info[$diff_key] . ' --> ' . $diff_value . '<br />';
						}
					}
					
					if (tep_not_null($changes_str)) {
						// Insert log to CO
						tep_db_query("INSERT INTO " . TABLE_ORDERS_STATUS_HISTORY . " (orders_id, date_added, customer_notified, comments, comments_type, changed_by) VALUES ('" . (int)$orders_id . "', now(), '0', '" . tep_db_input('##BO##'.$buyback_request_group_id.'## - Supplier Update\'s Info:<br />'.$changes_str)  . "', 2, '".$customer_email."')");
						
						// Insert log to BO
						$buyback_history_data_array = array('buyback_request_group_id' => $buyback_request_group_id,
														   	'buyback_status_id' => 0,
														   	'date_added' => 'now()',
														   	'customer_notified' => 0,
														   	'comments' => $changes_str,
														   	'set_as_buyback_remarks' => 0,
														   	'changed_by' => $customer_email
															);
						tep_db_perform(TABLE_BUYBACK_STATUS_HISTORY, $buyback_history_data_array);
					}
				}

				foreach ($hla_product_info as $hla_product_info_key => $hla_product_info_value) {
					$serialize_hla_info = tep_array_serialize($hla_product_info[$hla_product_info_key]);
					$hla_product_info_sql = "	INSERT INTO " . TABLE_ORDERS_PRODUCTS_EXTRA_INFO . " 
												SET orders_products_id = '".tep_db_input($orders_products_id)."', 
												orders_products_extra_info_key = '".tep_db_input($hla_product_info_key)."', 
												orders_products_extra_info_value = '".tep_db_input($serialize_hla_info)."' 
													ON DUPLICATE KEY 
												UPDATE orders_products_extra_info_value = '".tep_db_input($serialize_hla_info)."'";
					tep_db_query($hla_product_info_sql);
				}
				
				$insert_orders_products_history = array(	'buyback_request_group_id' => $buyback_request_group_id,
									                        'orders_id' => $orders_id,
															'orders_products_id' => $orders_products_id,
															'date_added' => 'now()',
															'last_updated' => 'now()',
															'date_confirm_delivered' => date("Y-m-d H:i:s", mktime(date("H")+72, date("i"), date("s"), date("m"), date("d"), date("Y"))),
															'delivered_amount' => '1',
															'changed_by' => $customer_email
															);
				
				$check_receive_sql = "	SELECT received FROM " . TABLE_ORDERS_PRODUCTS_HISTORY . " 
										WHERE buyback_request_group_id = '".tep_db_input($buyback_request_group_id)."' 
										ORDER BY date_added DESC LIMIT 1";
				$check_receive_result = tep_db_query($check_receive_sql);
				
				if ($check_receive_row = tep_db_fetch_array($check_receive_result)) {
					if ($check_receive_row['received'] == '0') {
						tep_db_perform(TABLE_ORDERS_PRODUCTS_HISTORY, $insert_orders_products_history);
					}
				} 
				
				if ($first_time_submit) {
					tep_db_perform(TABLE_ORDERS_PRODUCTS_HISTORY, $insert_orders_products_history);
					
					// If first time submit
					$products_hla_info = tep_draw_products_extra_info($orders_products_id, 'products_hla');
					if (count($products_hla_info) > 0) {
						$products_hla_info = tep_array_unserialize($products_hla_info['products_hla']);
						if (tep_not_null($products_hla_info['products_hla_id'])) {
							// Qty Received
							$update_confirmed_qty_array = array('buyback_quantity_confirmed' => '1', 
																'buyback_quantity_received' => '1');
							tep_db_perform(TABLE_BUYBACK_REQUEST, $update_confirmed_qty_array, 'update', "buyback_request_group_id='$buyback_request_group_id'");
							
							// Update Buyback Order to PROCESSING
							$update_request_group_status_array = array( 'buyback_status_id' => '2', 
																		'show_restock' => '0', 
																		'buyback_request_group_last_modified' => 'now()');
							tep_db_perform(TABLE_BUYBACK_REQUEST_GROUP, $update_request_group_status_array, 'update', "buyback_request_group_id='$buyback_request_group_id'");
							
							// Insert buyback history comment
							$buyback_history_data_array = array('buyback_request_group_id' => $buyback_request_group_id,
										                        'buyback_status_id' => '2',
																'date_added' => 'now()',
																'customer_notified' => '0',
																'comments' => 'Supplier submit info'
																);
							tep_db_perform(TABLE_BUYBACK_STATUS_HISTORY, $buyback_history_data_array);
							
							// Insert order history comment
							if (tep_not_null($history_content)) {
								tep_db_query("INSERT INTO " . TABLE_ORDERS_STATUS_HISTORY . " (orders_id, date_added, customer_notified, comments, comments_type, changed_by) VALUES ('" . (int)$orders_id . "', now(), '1', '" . tep_db_input($history_content)  . "', 2, '".$customer_email."')");
							}
							
							// Update delivered qty
							$delived_qty_update_sql = "	UPDATE " . TABLE_ORDERS_PRODUCTS . " 
														SET products_delivered_quantity = products_delivered_quantity + 1,
															products_good_delivered_quantity = products_good_delivered_quantity + 1,
															products_good_delivered_price = '".tep_db_input($buyback_amount)."' 
														WHERE orders_id='" . tep_db_input($orders_id) . "' 
															AND products_id = '" . tep_db_input($products_id) . "' 
															AND orders_products_id = '" . tep_db_input($orders_products_id) . "'";
							tep_db_query($delived_qty_update_sql);
							// Insert sales activity
							$edit_order_obj->update_delivered_price($orders_products_id);
							
							$final_price_select_sql = '	SELECT final_price
														FROM ' . TABLE_ORDERS_PRODUCTS . '
														WHERE orders_products_id = "'.$orders_products_id.'"';
							$final_price_result_sql = tep_db_query($final_price_select_sql);
							$final_price_row = tep_db_fetch_array($final_price_result_sql);
							
							$edit_order_obj->insert_sales_activity($orders_products_id, $products_id, 'D', $final_price_row['final_price'], 1, 1, $customer_email);
							
							// Update HLA product status to SOLD
							$update_hla_status_sql = "	UPDATE " . TABLE_PRODUCTS_HLA . " 
														SET products_status = 0 
														WHERE products_hla_id 	='" . tep_db_input($products_hla_info['products_hla_id']) . "'";
							tep_db_query($update_hla_status_sql);
							
							// Email to notify customer
							$get_order_status_sql = "	SELECT o.customers_id, o.date_purchased, os.orders_status_name 
														FROM " . TABLE_ORDERS . " AS o 
														INNER JOIN " . TABLE_ORDERS_STATUS . " AS os 
														ON (o.orders_status = os.orders_status_id) 
		 												WHERE o.orders_id = '" . tep_db_input($orders_id) . "' 
		 												AND os.language_id = '1'";
							$get_order_status_result = tep_db_query($get_order_status_sql);
							if($get_order_status_row = tep_db_fetch_array($get_order_status_result)) {
								$orders_status_name = $get_order_status_row['orders_status_name'];
								$customers_id = $get_order_status_row['customers_id'];
								$date_purchased = $get_order_status_row['date_purchased'];
								
								$customer_sql = "	SELECT c.customers_firstname, c.customers_lastname, c.customers_email_address, c.customers_gender, ci.customer_info_selected_language_id 
													FROM " . TABLE_CUSTOMERS . " AS c 
													LEFT JOIN " . TABLE_CUSTOMERS_INFO . " ci 
														ON (c.customers_id = ci.customers_info_id)
													WHERE c.customers_id = '".tep_db_input($customers_id)."'";
								$customer_result = tep_db_query($customer_sql);
								if($customer_row = tep_db_fetch_array($customer_result)) {
									$customer_name = $customer_row['customers_firstname']." ".$customer_row['customers_lastname'];
									if (eregi_dep('^[a-zA-Z0-9._-]+@[a-zA-Z0-9-]+\.[a-zA-Z.]{2,5}$',  $customer_row['customers_email_address'])) {
										include_once(DIR_WS_LANGUAGES . 'email_customers.php');
										
										if ($customer_row['customer_info_selected_language_id'] == '2') {
											include_once(DIR_WS_LANGUAGES . 'buyback_cn_simplified.php');
											$email_text_order_number = CN_EMAIL_TEXT_ORDER_NUMBER;
											$email_text_date_ordered = CN_EMAIL_TEXT_DATE_ORDERED;
											$email_text_invoice_url = CN_EMAIL_TEXT_INVOICE_URL;
											$email_text_updated_status = CN_EMAIL_TEXT_UPDATED_STATUS;
											$email_text_subject = CN_EMAIL_TEXT_SUBJECT;
										} else if ($customer_row['customer_info_selected_language_id'] == '3') {
											include_once(DIR_WS_LANGUAGES . 'buyback_cn_traditional.php');
											$email_text_order_number = CN_EMAIL_TEXT_ORDER_NUMBER;
											$email_text_date_ordered = CN_EMAIL_TEXT_DATE_ORDERED;
											$email_text_invoice_url = CN_EMAIL_TEXT_INVOICE_URL;
											$email_text_updated_status = CN_EMAIL_TEXT_UPDATED_STATUS;
											$email_text_subject = CN_EMAIL_TEXT_SUBJECT;
										} else {
											include_once(DIR_WS_LANGUAGES . 'buyback_english.php');
											$email_text_order_number = EN_EMAIL_TEXT_ORDER_NUMBER;
											$email_text_date_ordered = EN_EMAIL_TEXT_DATE_ORDERED;
											$email_text_invoice_url = EN_EMAIL_TEXT_INVOICE_URL;
											$email_text_updated_status = EN_EMAIL_TEXT_UPDATED_STATUS;
											$email_text_subject = EN_EMAIL_TEXT_SUBJECT;
										}
										
										$email_greeting = tep_get_email_greeting_english($customer_row['customers_firstname'], $customer_row['customers_lastname'], $customer_row['customers_gender']);
										
										$email = $email_greeting . $email_text_order_number . ' ' . (int)$orders_id . "\n"
												. $email_text_date_ordered . ' ' . tep_date_long($date_purchased) . "\n" . $email_text_invoice_url . ' ' . tep_href_link(FILENAME_ACCOUNT_HISTORY_INFO, 'order_id=' . $orders_id, 'SSL') . "\n\n" ;
										
										$email .= sprintf($email_text_updated_status, $orders_status_name) .
											 	  str_replace("\t", "&nbsp;&nbsp;&nbsp;&nbsp;", $history_content) . "\n\n";
											 	  
										$email .= EMAIL_HLA_CUSTOMER_GUIDE . "\n\n" . EMAIL_HLA_NEW_BUYBACK_ORDER_FOOTER;
										
						            	tep_mail($customer_name, $customer_row['customers_email_address'], implode(' ', array(EMAIL_SUBJECT_PREFIX, sprintf($email_text_subject, (int)$orders_id))), $email, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
									}
								}
							}
						}
					}
				}
				
				$customer_sql = "	SELECT customers_firstname, customers_lastname, customers_email_address, customers_gender 
									FROM " . TABLE_CUSTOMERS . " 
									WHERE customers_id = '".tep_db_input($_SESSION['customer_id'])."'";
				$customer_result = tep_db_query($customer_sql);
				if($customer_row = tep_db_fetch_array($customer_result)) {
					$customer_name = $customer_row['customers_firstname']." ".$customer_row['customers_lastname'];
					$customer_greeting_name = tep_get_email_greeting($customer_row['customers_firstname'], $customer_row['customers_lastname'], $customer_row['customers_gender']);
					
					if (eregi_dep('^[a-zA-Z0-9._-]+@[a-zA-Z0-9-]+\.[a-zA-Z.]{2,5}$',  $customer_row['customers_email_address'])) {
						$buyback_product_list = $email_product_path . ' = ' . $currencies->format((double)$buyback_amount, true, DEFAULT_CURRENCY, '', 'buy')."\n";
						
						$email_content = 	$customer_greeting_name . 
											EMAIL_HLA_NEW_BUYBACK_BODY . 
											sprintf(EMAIL_HLA_NEW_BUYBACK_ORDER_NUMBER, $buyback_request_group_id) . "\n" .
											sprintf(EMAIL_HLA_NEW_BUYBACK_DATE_ORDERED, tep_date_long(date("Y-m-d")." 00:00:00")) . "\n" .
											sprintf(EMAIL_HLA_NEW_BUYBACK_CUSTOMER_EMAIL, $customer_row['customers_email_address']) . "\n\n" .
											EMAIL_HLA_NEW_BUYBACK_PRODUCTS . "\n" . EMAIL_SEPARATOR . "\n" . $buyback_product_list .
											EMAIL_SEPARATOR . "\n" . 
					 						sprintf(EMAIL_HLA_NEW_BUYBACK_ORDER_TOTAL, $currencies->format($buyback_amount, true, DEFAULT_CURRENCY, '', 'buy')) . "\n\n" .
					 						EMAIL_HLA_NEW_BUYBACK_COMMENTS . "\n\n" . 
					 						sprintf(EMAIL_HLA_NEW_BUYBACK_STATUS, $buyback_status_name) . "\n" .
					 						EMAIL_HLA_SUPPLIER_SUBMITTED_INFO . "\n\n" .
					 						$email_supplier_hla_info . "\n\n" .
											EMAIL_HLA_NEW_BUYBACK_ORDER_FOOTER;
						tep_mail($customer_name, $customer_row['customers_email_address'], sprintf(EMAIL_HLA_NEW_BUYBACK_SUBJECT, $buyback_request_group_id), $email_content, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
					}
				}
				
			    tep_redirect(tep_href_link(FILENAME_MY_ORDER_HISTORY, 'action=search&odh_input_product_type_select=hla_product'));
			}
		}
		break;
	case 'edit_info':
		$get_op_id_sql = "	SELECT br.orders_products_id 
							FROM " . TABLE_BUYBACK_REQUEST . " AS br 
							INNER JOIN " . TABLE_BUYBACK_REQUEST_GROUP . " AS brg 
								ON (br.buyback_request_group_id = brg.buyback_request_group_id) 
							WHERE brg.buyback_request_group_id = '".tep_db_input($buyback_request_group_id)."' 
								AND brg.customers_id = '".$_SESSION['customer_id']."'";
		$get_op_id_result = tep_db_query($get_op_id_sql);
		
		if ($get_op_id_row = tep_db_fetch_array($get_op_id_result)) {
			$orders_products_id = $get_op_id_row['orders_products_id'];
			$hla_info_stage_1_arr = tep_draw_products_extra_info($orders_products_id, 'hla_info_stage_1');
			$hla_info_stage_1 = tep_array_unserialize($hla_info_stage_1_arr['hla_info_stage_1']);
			foreach ($hla_info_stage_1 as $stage_1_info) {
				foreach ($stage_1_info['info'] as $stage_1_key => $stage_1_value) {
					$$stage_1_key = $stage_1_value;	
				}
			}
			
			$hla_info_stage_2_arr = tep_draw_products_extra_info($orders_products_id, 'hla_info_stage_2');
			
			$hla_info_stage_2 = tep_array_unserialize($hla_info_stage_2_arr['hla_info_stage_2']);
			
			if (isset($hla_info_stage_2['question'])) {
				// for remain OLD structure still working fine
				foreach ($hla_info_stage_2 as $stage_2_key => $stage_2_value) {
					$$stage_2_key = $stage_2_value;
				}
			} else {
				foreach ($hla_info_stage_2 as $stage_2_info) {
					foreach ($stage_2_info['info'] as $stage_2_key => $stage_2_value) {
						$$stage_2_key = $stage_2_value;	
					}
				}
			}
		}
		break;
}

if ( ! tep_not_null($buyback_request_group_id)) {
	tep_redirect(tep_href_link(FILENAME_MY_ORDER_HISTORY, 'action=search&odh_input_product_type_select=hla_product'));
} else {
	$get_bo_details_sql = "	SELECT br.orders_id, br.products_id, br.orders_products_id, o.date_purchased, os.orders_status_name 
							FROM " . TABLE_BUYBACK_REQUEST . " AS br 
							INNER JOIN " . TABLE_BUYBACK_REQUEST_GROUP . " AS brg 
								ON (br.buyback_request_group_id = brg.buyback_request_group_id) 
							INNER JOIN " . TABLE_ORDERS . " AS o 
								ON (br.orders_id = o.orders_id) 
							INNER JOIN " . TABLE_ORDERS_STATUS . " AS os 
								ON (o.orders_status = os.orders_status_id AND os.language_id = '1') 
							WHERE brg.buyback_request_group_id = '".tep_db_input($buyback_request_group_id)."' 
								AND brg.customers_id = '" . tep_db_input($_SESSION['customer_id']) . "'";
	$get_bo_details_result = tep_db_query($get_bo_details_sql);
	
	if ($get_bo_details_row = tep_db_fetch_array($get_bo_details_result)) {
		$character_cnt = 1;
		$orders_products_item_info = $ref_id_arr = $main_char_info = array();
		$template_html = '';
		
		$orders_id = $get_bo_details_row['orders_id'];
		$orders_products_id = $get_bo_details_row['orders_products_id'];
		$date_ordered = $get_bo_details_row['date_purchased'];
		$orders_status = $get_bo_details_row['orders_status_name'];
		
		$get_main_character_info_sql = "SELECT orders_products_item_info FROM " . TABLE_ORDERS_PRODUCTS_ITEM . " WHERE orders_products_id = '".tep_db_input($orders_products_id)."'";
		$get_main_character_info_result = tep_db_query($get_main_character_info_sql);
		
		while ($get_main_character_info_row = tep_db_fetch_array($get_main_character_info_result)) {
			$orders_products_item_info = tep_array_unserialize($get_main_character_info_row['orders_products_item_info']);
			$ref_id_arr[] = $orders_products_item_info['products_ref_id'];
			if ($character_cnt == 1) {
				$main_char_info = $orders_products_item_info;
			}
			
			$character_cnt ++;
		}

		$template_html = '<tr>
							<td style="width:100%;padding-top:0;padding-left:12px;padding-right:0px;">
							<div style="width:720px;">
								<ul style="list-style: none outside none;padding-left:0px;">';
		
		if (count($temp_arr) > 0) {
			foreach ($temp_arr as $mapping_id => $field_array) {
				$template_html .= '<li style="float:left;width:720px;padding:10px;"><b>'.(defined($section_mapping[$mapping_id]) ? constant($section_mapping[$mapping_id]) : $section_mapping[$mapping_id]).'</b></li>';
				foreach ($field_array as $field_name => $field_value) {
					$li_width = isset($field_value['li_width']) ? $field_value['li_width'] : '300';
					$template_html .= '<li style="float:left;width:'.$li_width.'px;padding:10px;">
										<table border="0" width="100%" cellspacing="0" cellpadding="0">
										<tr>
											<td class="hla_form_entry" nowrap>'.(defined($field_value['entry']) ? constant($field_value['entry']) : $field_value['entry']).(tep_not_null($field_value['entry']) ? ':' : '').'</td>
											<td>';
					if (tep_not_null($field_name)) {
						switch ($field_value['type']) {
							case 'text':
							case 'password':
								$param = isset($field_value['size']) ? ' size='.$field_value['size'] : '';
								$param .= isset($field_value['maxlength']) ? ' maxlength='.$field_value['maxlength'] : '';
								
								$template_html .= tep_draw_input_field($field_name, isset($_SESSION['submitted_info'][$field_name]) ? $_SESSION['submitted_info'][$field_name] : $$field_name, $param, $field_value['type'], false);		
								break;
						}
					}
					$template_html .= '		</td>
										</tr>
										</table>
										</li>';
				}
				
			}
			unset($_SESSION['submitted_info']);
		}
		
		
		$template_html .= '		</ul>
							</div>
							</td>
						</tr>';								
	} else {
		tep_redirect(tep_href_link(FILENAME_MY_ORDER_HISTORY, 'action=search&odh_input_product_type_select=hla_product'));
	}
}	

$form_values_arr = $_SESSION[$form_session_name];

$breadcrumb->add(NAVBAR_TITLE, tep_href_link(FILENAME_BUYBACK_ORDER_FORM, '', 'SSL'));

require(DIR_WS_TEMPLATES . TEMPLATENAME_MAIN_PAGE);

require(DIR_WS_INCLUDES . 'application_bottom.php');
?>