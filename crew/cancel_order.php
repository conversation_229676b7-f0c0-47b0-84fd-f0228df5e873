<?php 

require('includes/application_top.php');

if (!tep_session_is_registered('customer_id')) {
	$navigation->set_snapshot();
    tep_redirect(tep_href_link(FILENAME_LOGIN, '', 'SSL'));
}

$order_id = isset($_REQUEST['order_id']) && is_numeric($_REQUEST['order_id']) ? tep_db_prepare_input($_REQUEST['order_id']) : '';
if (!tep_not_null($order_id)) {
	tep_redirect(tep_href_link(FILENAME_ACCOUNT_HISTORY, 'orders_type=current', 'SSL'));	
}

require(DIR_WS_LANGUAGES . $language . '/' . FILENAME_CANCEL_ORDER);
require(DIR_WS_CLASSES . 'order.php');
$order = new order($order_id);

// check if this order is belong to the customer
if ($order->customer['id'] != $customer_id) {
	$messageStack->add_session('account_history', MESSAGE_UNAUTHORISED_CANCEL_ORDER);
	tep_redirect(tep_href_link(FILENAME_ACCOUNT_HISTORY, 'orders_type=current', 'SSL'));
}

$payment_methods_array = Array();
if ($order->info['payment_methods_id'] != '0') {
	require_once(DIR_WS_CLASSES . 'payment_methods.php');
	$pm_obj = new payment_methods($order->info['payment_methods_id']);
	$payment_method = $pm_obj->payment_methods_description_title;
}

// check from orders_total table if there is any store credit or voucher is used in this order
if (sizeof($order->totals) && is_array($order->totals)) {
	for ($r=0, $t=count($order->totals); $r < $t; $r++) {
		$totals_array[$order->totals[$r]['class']] = array('title'	=> $order->totals[$r]['title'],
														   'text'	=> $order->totals[$r]['text'],
														   'value'	=> $order->totals[$r]['value']
													 );
	}
	
	$order_total_value = $totals_array['ot_subtotal']['text'];	// Payment Amount
	
	// Payment Methods
	if (array_key_exists('ot_coupon', $totals_array)) {
		$search = array('Discount Coupons', '(if there is any)', ':');
		$payment_methods_array[] = str_replace($search, '', TEXT_DISCOUNT_COUPON);
	}
	
	if (array_key_exists('ot_gv', $totals_array)) {
		$payment_methods_array[] = str_replace(':', '', TEXT_STORE_CREDIT);
	}
	
	if (array_key_exists('ot_total', $totals_array) && $totals_array['ot_total']['value'] > 0 ) {
		$payment_methods_array[] = $payment_method; //'CashU';
	}
}


$action = isset($_REQUEST['action']) ? tep_db_prepare_input($_REQUEST['action']) : '';
if ($action == 'process') {
	// record Cancel Customer Order request, prevent repeating cancellation
	tep_db_perform(TABLE_ORDERS_CANCEL_REQUEST, array ( 'orders_id' => $order_id ));
	
		$reason_subject	= tep_not_null($_REQUEST['cancel_reason']) ? tep_db_prepare_input($_REQUEST['cancel_reason']) : '';
	$reason_feedback = tep_not_null($_REQUEST['cancel_feedback']) ? tep_db_prepare_input($_REQUEST['cancel_feedback']) : '';
	// get cancel reason
	$i=0;
	while ($i < sizeof($cancel_reason_array)) {
		$cancel_reason_text[$cancel_reason_array[$i]['id']] = $cancel_reason_array[$i]['text'];
		$i++;
	}
	
	// email templates
	if (sizeof($order->products) > 0 && is_array($order->products)) {
		foreach($order->products as $index => $order_products) {
			$products_name[] = $order_products['name'];
		}
	}
	
	$customer_name	= $order->customer['name'];
	$products		= implode(', ', $products_name);
	$email_subject	= tep_mb_convert_encoding(tep_mb_convert_encoding(sprintf(EMAIL_SUBJECT, $order_id), EMAIL_CHARSET, CHARSET), CHARSET, EMAIL_CHARSET);
		
	$email_text_ogm  = sprintf(EMAIL_CANCEL_ORDER_NUMBER, $order_id)."\n";
	$email_text_ogm .= sprintf(EMAIL_CANCEL_PAYMENT_METHOD, implode(', ', $payment_methods_array))."\n";
	$email_text_ogm .= sprintf(EMAIL_CANCEL_AMOUNT, $order_total_value)."\n";
	$email_text_ogm .= sprintf(EMAIL_CANCEL_REASON, $cancel_reason_text[$reason_subject])."\n";
	$email_text_ogm .= sprintf(EMAIL_CANCEL_FEEDBACK, $reason_feedback)."\n";

	$email_to_customer	= sprintf(EMAIL_TEMPLATE_OCR_TO_CUSTOMER, $customer_name, $order_id, $products);
	$email_to_ogm = sprintf(EMAIL_TEMPLATE_OCR_TO_OGM, $email_text_ogm);

	
	// send email to OGM & customer
	tep_mail(STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS, $email_subject, $email_to_ogm, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
	tep_mail($order->customer['name'], $order->customer['email_address'], $email_subject, $email_to_customer, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
	$messageStack->add_session('mya_order_details', MESSAGE_REQUEST_SENT_SUCCESS, 'success');
	tep_redirect(tep_href_link(FILENAME_ACCOUNT_HISTORY_INFO, 'order_id='.$order_id, 'SSL'));
}


$live_support_link = '<a href="'.tep_href_link(FILENAME_CUSTOMER_SUPPORT, '', 'SSL').'">'.TEXT_LIVE_SUPPORT.'</a>';

$breadcrumb->add(NAVBAR_TITLE_1, tep_href_link(FILENAME_ACCOUNT, '', 'SSL'));
$breadcrumb->add(NAVBAR_TITLE_2, tep_href_link(FILENAME_ACCOUNT_HISTORY, '', 'SSL'));
$breadcrumb->add(NAVBAR_TITLE_3, tep_href_link(FILENAME_ACCOUNT_HISTORY_INFO, 'order_id='.$order_id, 'SSL'));


$content = CONTENT_CANCEL_ORDER;

require(DIR_WS_TEMPLATES . TEMPLATENAME_MAIN_PAGE);
require(DIR_WS_INCLUDES . 'application_bottom.php');
?>