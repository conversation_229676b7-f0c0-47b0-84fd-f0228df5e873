<?php

require_once('includes/application_top.php');

require_once(DIR_WS_MODULES . 'payment/cashU/classes/cashU_ipn_class.php');
include(DIR_WS_LANGUAGES . $language . '/' . FILENAME_CHECKOUT_PROCESS);
//Process payment

if (isset($_POST) && count($_POST)) {
    include_once(DIR_WS_FUNCTIONS . 'custom_product_info.php');
    include_once(DIR_WS_CLASSES . 'order.php');
    include_once(DIR_WS_CLASSES . 'anti_fraud.php');
    include_once(DIR_WS_CLASSES . 'log.php');

    /*     * ********************************************************
      (1) Check this order is pay to us
      (2) This is Pending order
     * ******************************************************** */
    $payment = 'cashU';
    $ipn_data = stripslashes($_POST['sRequest']);
    $xml_array = simplexml_load_string($ipn_data);

    // load selected payment module
    include_once(DIR_WS_CLASSES . 'payment.php');

    $cashU_ipn = new cashU_ipn($xml_array);

    $orders_id = $cashU_ipn->get_order_id();
    $order = new order($orders_id);

    $payment_modules = new payment($payment);
    $$payment = new $payment($order->info['payment_methods_id']);
    $$payment->get_merchant_account($cashU_ipn->key['currency']);
    $$payment->feedback_ipn($cashU_ipn);

    $log_object = new log_files('system');
    if ((int) $orders_id > 0) {
        $cashu_data_array = array('orders_id' => (int) $cashU_ipn->key['session_id'],
            'transaction_id' => tep_db_prepare_input($cashU_ipn->key['cashU_trnID']),
            'transaction_amount' => (double) tep_db_prepare_input($cashU_ipn->key['amount']),
            'transaction_currency' => tep_db_prepare_input($cashU_ipn->key['currency']),
            'transaction_net_amount' => tep_db_prepare_input($cashU_ipn->key['netAmount']),
            'authorisation_result' => json_encode($cashU_ipn->key)
        );

        $cashU_payment_select_sql = "	SELECT orders_id
                                        FROM " . TABLE_PAYMENT_EXTRA_INFO . "
                                        WHERE orders_id = '" . (int) $cashU_ipn->key['session_id'] . "'";
        $cashU_payment_result_sql = tep_db_query($cashU_payment_select_sql);
        if (tep_db_num_rows($cashU_payment_result_sql)) {
            tep_db_perform(TABLE_PAYMENT_EXTRA_INFO, $cashu_data_array, 'update', " orders_id = '" . (int) $cashU_ipn->key['session_id'] . "' ");
        } else {
            $cashu_data_array['orders_id'] = $cashU_ipn->key['session_id'];
            tep_db_perform(TABLE_PAYMENT_EXTRA_INFO, $cashu_data_array);
        }
    }
    
    if ($cashU_ipn->key['responseCode'] == 'OK') {
        if ($cashU_ipn->validate_receiver_account($$payment->merchant_id)) {
            if ($cashU_ipn->validate_transaction_data($$payment)) { // To ensure the integrity of the data posted back to merchant's server
                if (tep_not_null($orders_id)) {
                    if (!is_object($order))
                        $order = new order($orders_id);
                    if ($order->info['orders_status_id'] == $$payment->order_status) { // If the order still in initial status
                        $cashU_ipn->authenticate($order, $orders_id, $$payment);
                        exit;
                    }
                }
            }
        }
    }
}
?>