<?
/*
  $Id: cashUcallback.php,v 1.8 2013/02/19 09:42:07 sionghuat.chng Exp $
  Author : 	<PERSON><PERSON> (<EMAIL>)

  Copyright (c) 2005
  Released under the GNU General Public License
 */

require_once('includes/configure.php');

$form_data = array();
$data = '';
if (isset($_POST) && count($_POST)) {
    foreach ($_POST as $key => $value) {
        $form_data[$key] = $value;
    }
}

$url = HTTP_SERVER . 'checkout_process.php';
?>
<html>
    <head></head>
    <body>
        <form name="cashu_payment_success" action="<?php echo $url?>" method="post">
            <?php
                reset($form_data);
                while (list($key, $value) = each($form_data)) {
                    if (!is_array($_REQUEST[$key])) {
                        echo "<input type='hidden' name='".$key."' value='".htmlspecialchars(stripslashes($value))."'>";
                    }
                }
            ?>
        </form>
        <script type="text/javascript" language="JavaScript">
            document.cashu_payment_success.submit();
        </script>
    </body>
</html>