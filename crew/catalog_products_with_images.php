<?php
/*
  $Id: catalog_products_with_images.php V 3.0
  by <PERSON> <<EMAIL>> V 3.0

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2002 osCommerce

  Released under the GNU General Public License

  notes: added to the catalog/includes/languages/english.php 
  define('IMAGE_BUTTON_UPSORT', 'Sort Asending');
  define('IMAGE_BUTTON_DOWNSORT', 'Sort Desending');
*/
  require('includes/application_top.php');

  require(DIR_WS_LANGUAGES . $language . '/' . FILENAME_CATALOG_PRODUCTS_WITH_IMAGES);

  // Use $location if you have a pre breadcrumb release of OSC then comment out $breadcrumb line
  //$location = ' &raquo; <a href="' . tep_href_link(FILENAME_CATALOG_PRODUCTS_WITH_IMAGES, '', 'NONSSL') . '" class="headerNavigation">' . NAVBAR_TITLE . '</a>';

  // Use $breadcrumb if you have a breadcrumb release of OSC then comment out $location line
  $breadcrumb->add(NAVBAR_TITLE, tep_href_link(FILENAME_CATALOG_PRODUCTS_WITH_IMAGES, '', 'NONSSL'));

  $content = CONTENT_PRINTABLE_CATALOG;

  require(DIR_WS_TEMPLATES . TEMPLATENAME_MAIN_PAGE);



   require(DIR_WS_INCLUDES . 'application_bottom.php');
   
?>