<?
require('includes/application_top.php');
include_once(DIR_WS_CLASSES . 'wow/bag.php');
include_once(DIR_WS_CLASSES . 'wow/char.php');
include_once(DIR_WS_CLASSES . 'wow/item.php');
include_once(DIR_WS_CLASSES . 'wow/skill.php');
include_once(DIR_WS_CLASSES . 'wow/quest.php');
include_once(DIR_WS_CLASSES . 'wow/bank.php');

$action = (isset($_REQUEST['action']) ? $_REQUEST['action'] : '');

if (!tep_session_is_registered('customer_id')) {
	$navigation->set_snapshot();
    tep_redirect(tep_href_link(FILENAME_LOGIN, '', 'SSL'));
}

require(DIR_WS_LANGUAGES . $language . '/' . FILENAME_CHARACTER_PROFILE);

list($order_id, $orders_custom_products_number) = explode('_', $_REQUEST['id']);

if (!tep_not_null($order_id) || !tep_not_null($orders_custom_products_number)) {
	tep_redirect(tep_href_link(FILENAME_ACCOUNT_HISTORY, '', 'SSL'));
}

$customer_info_query = tep_db_query("select customers_id from " . TABLE_ORDERS . " where orders_id = '". (int)$order_id . "'");
$customer_info = tep_db_fetch_array($customer_info_query);

if ($customer_info['customers_id'] != $customer_id) {
	tep_redirect(tep_href_link(FILENAME_ACCOUNT_HISTORY, '', 'SSL'));
}

$orders_products_id_select_sql = "	SELECT ocp.orders_products_id 
									FROM " . TABLE_ORDERS_CUSTOM_PRODUCTS . " AS ocp 
									INNER JOIN " . TABLE_ORDERS_PRODUCTS . " AS op 
										ON (ocp.orders_products_id = op.orders_products_id) 
									WHERE op.orders_id ='" . (int)$order_id . "' 
										AND ocp. orders_custom_products_number ='" . (int)$orders_custom_products_number . "' LIMIT 1";
$orders_products_id_result_sql = tep_db_query($orders_products_id_select_sql);
$orders_products_id_row = tep_db_fetch_array($orders_products_id_result_sql);

$breadcrumb->add(NAVBAR_TITLE_1, tep_href_link(FILENAME_ACCOUNT, '', 'SSL'));
$breadcrumb->add(NAVBAR_TITLE_2, tep_href_link(FILENAME_ACCOUNT_HISTORY, '', 'SSL'));
$breadcrumb->add(' Order #' . $order_id, tep_href_link(FILENAME_ACCOUNT_HISTORY_INFO, 'order_id=' . $order_id, 'SSL'));
$breadcrumb->add(HEADING_TITLE, '');

$wow_addon_path = DIR_WS_INTERFACE;

$content = CONTENT_CHARACTER_PROFILE;

require(DIR_WS_TEMPLATES . TEMPLATENAME_MAIN_PAGE);

require(DIR_WS_INCLUDES . 'application_bottom.php');
?>