<?
/*
  	$Id: checkout_confirmation.php,v 1.28 2011/01/28 08:34:54 boonhock Exp $

  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com

  	Copyright (c) 2003 osCommerce

  	Released under the GNU General Public License
*/
require('includes/application_top.php');
require(DIR_WS_LANGUAGES . $language . '/' . FILENAME_CHECKOUT_CONFIRMATION);

// if the customer is not logged on, redirect them to the login page
if (!tep_session_is_registered('customer_id')) {
	$navigation->set_snapshot(array('mode' => 'SSL', 'page' => FILENAME_CHECKOUT_PAYMENT));
    tep_redirect(tep_href_link(FILENAME_LOGIN, '', 'SSL'));
}

// if there is nothing in the customers cart, redirect them to the shopping cart page
if ($cart->count_contents() < 1) {
	tep_redirect(tep_href_link(FILENAME_SHOPPING_CART));
}

// avoid hack attempts during the checkout procedure by checking the internal cartID
if (isset($cart->cartID) && tep_session_is_registered('cartID')) {
	if ($cart->cartID != $cartID) {
    	tep_redirect(tep_href_link(FILENAME_CHECKOUT_SHIPPING, '', 'SSL'));
    }
}

// if no shipping method has been selected, redirect the customer to the shipping method selection page
if (!tep_session_is_registered('shipping')) {
	tep_redirect(tep_href_link(FILENAME_CHECKOUT_SHIPPING, '', 'SSL'));
}

if (!tep_session_is_registered('payment')) tep_session_register('payment');
if (isset($HTTP_POST_VARS['payment'])) $payment = $HTTP_POST_VARS['payment'];

// storing order id for 2CO payment
if (tep_session_is_registered('order_logged')) tep_session_unregister('order_logged');

/*if (!tep_session_is_registered('comments')) tep_session_register('comments');
if (!tep_session_is_registered('custom_comments')) tep_session_register('custom_comments');

require(DIR_WS_CLASSES . 'cart_comments.php');
//$cart_comments_obj = new cart_comments($custom_comments);
$cart_comments_obj = new cart_comments('');

$active_comments = $cart_comments_obj->count_active_comments(1);

if (isset($HTTP_POST_VARS['continue_checkout'])) {
	$cart_comment_proceed = $cart_comments_obj->construct_comments(1);
	$custom_comments = $cart_comments_obj->comments;
	$comments = $cart_comments_obj->get_plain_comments($custom_comments, 1);

	if (!$cart_comment_proceed) {
		tep_redirect(tep_href_link(FILENAME_CHECKOUT_PAYMENT, 'error_message=' . urlencode(ERROR_CART_COMMENTS_REQUIRED), 'SSL', true, false));
	}
}*/

/*
$active_comments = $cart_comments_obj->count_active_comments(1);
$custom_comments = $cart_comments_obj->comments;
$comments = $cart_comments_obj->get_plain_comments($custom_comments, 1);

if (!$cart_comments_obj->verify_mandatory_info_provided($custom_comments, 1)) {
	tep_redirect(tep_href_link(FILENAME_CHECKOUT_INFO, 'error_message=' . urlencode(ERROR_CART_COMMENTS_REQUIRED), 'SSL', true, false));
}
*/

// load the selected payment module
require(DIR_WS_CLASSES . 'payment.php');
//if ($credit_covers) $payment=''; //ICW added for CREDIT CLASS. Removed in v2.7 cause variable not yet set at this point
$payment_modules = new payment($payment);

//ICW ADDED FOR CREDIT CLASS SYSTEM
require(DIR_WS_CLASSES . 'order_total.php');
require(DIR_WS_CLASSES . 'order.php');
$order = new order;

if ($order->info['subtotal'] > 0) {
	;	// These ordered products has price value.
} else {
	/*	Allow 0.00 Checkout (we have some free gift keys)
	$messageStack->add_session('add_to_cart', WARNING_ORDER_AMOUNT_ZERO, 'error');
	tep_redirect(tep_href_link(FILENAME_SHOPPING_CART));
	break;
	*/
}

$payment_modules->update_status();

//ICW ADDED FOR CREDIT CLASS SYSTEM
$order_total_modules = new order_total;

//ICW ADDED FOR CREDIT CLASS SYSTEM
$order_total_modules->collect_posts();

if (!isset($HTTP_POST_VARS['continue_checkout'])) {
	tep_redirect(tep_href_link(FILENAME_CHECKOUT_PAYMENT));
}

//ICW ADDED FOR CREDIT CLASS SYSTEM
$order_total_modules->pre_confirmation_check();

$full_store_credit = $payment_modules->check_credit_covers();

// ICW CREDIT CLASS Amended Line
//  if ( ( is_array($payment_modules->modules) && (sizeof($payment_modules->modules) > 1) && !is_object($$payment) ) || (is_object($$payment) && ($$payment->enabled == false)) ) {
if ( (is_array($payment_modules->modules)) && (sizeof($payment_modules->modules) > 1) && (!is_object($$payment)) && (!$payment_modules->check_credit_covers()) ) {
	tep_redirect(tep_href_link(FILENAME_CHECKOUT_PAYMENT, 'payment_error=ccerr&error_message=' . urlencode(ERROR_NO_PAYMENT_MODULE_SELECTED), 'SSL'));
}

if (is_array($payment_modules->modules)) {
	$payment_modules->pre_confirmation_check();
}

// This line must be after pre_confirmation_check() for full store credit usage
$payment_method_str = HEADING_PAYMENT_METHOD.'<b>'.(is_object($payment_modules) && tep_not_null($payment_modules->selected_module) ? ($order->info['display_method'] ? $order->info['display_method'] : $order->info['payment_method']) : TEXT_NOT_AVAILABLE).'</b>';

// load the selected shipping module
require(DIR_WS_CLASSES . 'shipping.php');
$shipping_modules = new shipping($shipping);
//ICW Credit class amendment Lines below repositioned

// Stock Check
$any_out_of_stock = false;
if (STOCK_CHECK == 'true') {
	for ($i=0, $n=sizeof($order->products); $i<$n; $i++) {
    	$prod_query = tep_db_query("select products_bundle, products_bundle_dynamic from " . TABLE_PRODUCTS . " where products_id = '" . (int)$order->products[$i]['id'] . "'");
      	$prod_type = tep_db_fetch_array($prod_query);
      	if ($prod_type['products_bundle']=='yes' || $prod_type['products_bundle_dynamic']=='yes') {
	    	//no update
      	} else {
      		if (isset($order->products[$i]['custom_content']['delivery_mode']) && $order->products[$i]['custom_content']['delivery_mode'] == '6') {
      			//
      		} else {
	  			$product_instance_id = tep_not_null($order->products[$i]['custom_content']['hla_account_id']) ? $order->products[$i]['custom_content']['hla_account_id'] : '';
	      		$stock_status = tep_check_stock_status($order->products[$i]['id'], $order->products[$i]['qty'], $product_instance_id);
	        	if (tep_not_null($stock_status) && $stock_status != "pre-order") {
		        	$any_out_of_stock = true;
		      	}
		    }
      	}
	}
    // Out of Stock
    if ( (STOCK_ALLOW_CHECKOUT != 'true') && ($any_out_of_stock == true) ) {
    	tep_redirect(tep_href_link(FILENAME_SHOPPING_CART));
    }
}

/************************************************
	Prevent empty selections for dynamic package
************************************************/
if (!tep_check_dynamic_selections($order->products)) {
	tep_redirect(tep_href_link(FILENAME_SHOPPING_CART));
}

if ($shipping != false)	$breadcrumb->add(NAVBAR_TITLE_1, tep_href_link(FILENAME_CHECKOUT_SHIPPING, '', 'SSL'));
$breadcrumb->add(NAVBAR_TITLE_3, tep_href_link(FILENAME_CHECKOUT_PAYMENT, '', 'SSL'));
$breadcrumb->add(NAVBAR_TITLE_4);

$content = CONTENT_CHECKOUT_CONFIRMATION;

require(DIR_WS_TEMPLATES . TEMPLATENAME_MAIN_PAGE);

require(DIR_WS_INCLUDES . 'application_bottom.php');
?>