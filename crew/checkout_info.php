<?
/*
	$Id: checkout_info.php,v 1.3 2011/01/28 08:35:20 boonhock Exp $
	
	Developer: <PERSON>
  	Copyright (c) 2004 SKC Ventrue
	
  	Released under the GNU General Public License
*/

require('includes/application_top.php');

// if the customer is not logged on, redirect them to the login page
if (!tep_session_is_registered('customer_id')) {
	$navigation->set_snapshot();
    tep_redirect(tep_href_link(FILENAME_LOGIN, '', 'SSL'));
}

// if there is nothing in the customers cart, redirect them to the shopping cart page
if ($cart->count_contents() < 1) {
	tep_redirect(tep_href_link(FILENAME_SHOPPING_CART));
}

// if no shipping method has been selected, redirect the customer to the shipping method selection page
if (!tep_session_is_registered('shipping')) {
    tep_redirect(tep_href_link(FILENAME_CHECKOUT_SHIPPING, '', 'SSL'));
}

// avoid hack attempts during the checkout procedure by checking the internal cartID
if (isset($cart->cartID) && tep_session_is_registered('cartID')) {
	if ($cart->cartID != $cartID) {
    	tep_redirect(tep_href_link(FILENAME_CHECKOUT_SHIPPING, '', 'SSL'));
    }
}

// Stock Check
if ( (STOCK_CHECK == 'true') && (STOCK_ALLOW_CHECKOUT != 'true') ) {
	$products = $cart->get_products();
    for ($i=0, $n=sizeof($products); $i<$n; $i++) {
	  	$prod_query = tep_db_query("select products_bundle, products_bundle_dynamic from " . TABLE_PRODUCTS . " where products_id = '" . (int)$products[$i]['id'] . "'");
	  	$prod_type = tep_db_fetch_array($prod_query);
	  	if($prod_type['products_bundle']=='yes' || $prod_type['products_bundle_dynamic']=='yes'){
	    	//no update
	  	} else {
	  		if (isset($products[$i]['custom_content']['delivery_mode']) && $products[$i]['custom_content']['delivery_mode'] == '6') {
	  			//
	  		} else {
				$product_instance_id = tep_not_null($products[$i]['custom_content']['hla_account_id']) ? $products[$i]['custom_content']['hla_account_id'] : '';
		  		$stock_status = tep_check_stock_status($products[$i]['id'], $products[$i]['quantity'], $product_instance_id);
		    	if (tep_not_null($stock_status) && $stock_status != "pre-order") {
		        	tep_redirect(tep_href_link(FILENAME_SHOPPING_CART));
		        	break;
		      	}
		    }
	  	}
	}
}

if (!tep_session_is_registered('comments')) tep_session_register('comments');
if (!tep_session_is_registered('custom_comments')) tep_session_register('custom_comments');

require(DIR_WS_CLASSES . 'cart_comments.php');
$cart_comments_obj = new cart_comments('');

require(DIR_WS_LANGUAGES . $language . '/' . FILENAME_CHECKOUT_INFO);

if ($shipping != false)	$breadcrumb->add(NAVBAR_TITLE_1, tep_href_link(FILENAME_CHECKOUT_SHIPPING, '', 'SSL'));
$breadcrumb->add(NAVBAR_TITLE_2, tep_href_link(FILENAME_CHECKOUT_INFO, '', 'SSL'));

$content = CONTENT_CHECKOUT_INFO;
//$javascript = $content . '.js.php';

require(DIR_WS_TEMPLATES . TEMPLATENAME_MAIN_PAGE);

require(DIR_WS_INCLUDES . 'application_bottom.php');
?>