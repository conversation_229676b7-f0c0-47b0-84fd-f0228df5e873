<?php
header("Cache-Control: no-cache");
header("Cache-Control: post-check=0,pre-check=0");
header("Cache-Control: max-age=0");
header("Pragma: no-cache");

include_once('includes/application_top.php');
require_once(DIR_WS_CLASSES . 'order_total.php');
require_once(DIR_WS_CLASSES . 'order.php');
require_once(DIR_WS_CLASSES . 'log.php');
require_once(DIR_WS_CLASSES . 'payment.php');
require_once(DIR_WS_CLASSES . 'product.php');

$error = FALSE;
$error_redirect = '';
$error_dm = '';
$error_qty = '';
$error_coupon = '';
$response_array = array();

$languages_id = isset($_SESSION['languages_id']) ? (int)$_SESSION['languages_id'] : '';
$action = isset($_REQUEST['action']) ? $_REQUEST['action'] : '';
$selection_id = isset($HTTP_GET_VARS['selection_id']) ? (int)$HTTP_GET_VARS['selection_id'] : '';
$custom_products_type_id = isset($_POST['cpt_id']) ? (int)$_POST['cpt_id'] : 0;
$show_hop = isset($HTTP_GET_VARS['show_hop']) ? (int)$HTTP_GET_VARS['show_hop'] : '';
$product_id = isset($_REQUEST['products_id']) ? (int)$_REQUEST['products_id'] : '';
$sel_id = isset($_POST['sel_id']) ? (int)$_POST['sel_id'] : 0;
$payment_info = isset($HTTP_GET_VARS['payment_info']) ? $HTTP_GET_VARS['payment_info'] : (isset($_SESSION['selected_payment_info']) ? $_SESSION['selected_payment_info'] : '');
$currency_code = isset($HTTP_GET_VARS['curr_code']) ? $HTTP_GET_VARS['curr_code'] : '';
$sel = isset($HTTP_GET_VARS['sel']) ? $HTTP_GET_VARS['sel'] : '';
$delivery_mode = isset($_REQUEST['delivery_mode']) ? (int)$_REQUEST['delivery_mode'] : 0;
$products_bundle = isset($_POST['products_bundle']) ? $_POST['products_bundle'] : '';
$products_bundle_dynamic = isset($_POST['products_bundle_dynamic']) ? $_POST['products_bundle_dynamic'] : '';
$buyqty = isset($_REQUEST['buyqty']) ? ((int)$_REQUEST['buyqty'] > 100 ? 100 : (int)$_REQUEST['buyqty']) : 0;
$user_confirmed = isset($_POST['confirmed']) ? (int)$_POST['confirmed'] : 0;
$user_cfm_password = isset($_POST['cfm_password']) ? tep_db_prepare_input(strip_tags($_POST['cfm_password'])) : '';
$user_extra_info_array = isset($_REQUEST['extra_info']) ? filter_extra_info($_REQUEST['extra_info']) : array();
$order_id = isset($_POST['oid']) ? (int)$_POST['oid'] : 0;
list($hla_id, $hla_game_id) = (isset($_POST['hla']) && tep_not_empty($_POST['hla'])) ? get_hla_info($_POST['hla']) : array('','');
list($index, $display_section) = get_index_and_type($_POST['index']);

$buyqty = $buyqty > 0 ? $buyqty : 1;
$_SESSION['selected_payment_info'] = $payment_info;

if (isset($_SESSION['language']) && tep_not_null($_SESSION['language'])) {
	if (file_exists(DIR_WS_LANGUAGES . $_SESSION['language'] . '.php')) {
		include_once(DIR_WS_LANGUAGES . $_SESSION['language'] . '.php');
	}
	
	if (file_exists(DIR_WS_LANGUAGES . $_SESSION['language'] . '/' . FILENAME_CUSTOM_PRODUCT_INFO)) {
		include_once(DIR_WS_LANGUAGES . $_SESSION['language'] . '/' . FILENAME_CUSTOM_PRODUCT_INFO);
	}
	
	if (file_exists(DIR_WS_LANGUAGES . $_SESSION['language'] . '/' . FILENAME_CHECKOUT_PAYMENT)) {
		include_once(DIR_WS_LANGUAGES . $_SESSION['language'] . '/' . FILENAME_CHECKOUT_PAYMENT);
	}
}

if (tep_not_null($action)) {
	if (!isset($_SERVER['HTTP_REFERER']) || (strstr($_SERVER['HTTP_REFERER'], HTTP_SERVER) === FALSE && strstr($_SERVER['HTTP_REFERER'], HTTPS_SERVER) === FALSE)) {
//        if ($action != 'products_info') {
            echo "You are not allowed to access from outside";
            exit;
//        }
	}
	
	switch($action) {
        case 'chk_order_status':
            $return_bool = FALSE;
            
            if (isset($_SESSION['customer_id'])) {
                $orders_check_query = tep_db_query("select orders_status from " . TABLE_ORDERS . " where customers_id = '" . (int)$_SESSION['customer_id'] . "' and orders_id = '" . $order_id . "'");
                if ($orders_check = tep_db_fetch_array($orders_check_query)) {
                    if ((int)$orders_check['orders_status'] == 3) {
                        $return_bool = TRUE;
                    }
                }
            }
            
            $response_array['err'] = FALSE;
            $response_array['status'] = $return_bool;
            break;
        case 'buy_code' :
            if (pfv_next_access($user_confirmed) === 'confirm_order') {
                $cart->reset(true);
                
                if (tep_not_empty($product_id)) {
                    if ($custom_products_type_id === 2) {   // only for cd-key to get default delivery mode
                        if ($delivery_mode === 0 || $delivery_mode === 5) {
                            $product_delivery_mode_array = product::get_product_delivery_mode($product_id);
                            $delivery_mode = in_array($delivery_mode, $product_delivery_mode_array) ? $delivery_mode : 0;

                            if ($delivery_mode === 0) {
                                foreach ($product_delivery_mode_array as $dm) {
                                    if ((int)$dm !== 6) {
                                        $delivery_mode = (int)$dm;
                                        break;
                                    }
                                }
                            }
                        }
                    }
                    
                    $custom_product_type = $cart->get_custom_prd_type($product_id);
                    $bypass_DTU_check = FALSE;

                    // DTU product does not need to check account for pop up confirm order box.
                    if ($user_confirmed['skip_cfm_order'] === 1 || $user_confirmed['skip_cfm_order'] === 2) {
                        $bypass_DTU_check = TRUE;
                        
                        if ($user_confirmed['skip_cfm_order'] === 1) {
                            unset($_SESSION['cc_id']);  // required to reset coupon usage.
                        }
                    }

                    // ---------------------------------------------------------------------------------------------------------------------------- Start Pwl
                    if ($custom_product_type == 'power_leveling') {
                        $buyqty = 1;    // Pwl only allow 1 qty.
                        
                        if (!$const_i_am_allowed_here) {
                            $error = 2;
                            $error_redirect = tep_href_link(FILENAME_DEFAULT, '', 'SSL');
                        } else if (!tep_check_product_region_permission($product_id, false)) {
                            $error = 2;
                            $error_redirect = tep_href_link(FILENAME_CUSTOM_PRODUCT_INFO, 'products_id='.$product_id.'&index=1', 'SSL');
                        } else {
                            $active_product = true;
                            
                            if ((int)$_GET['index'] > 0) {
                                $pwl_index = (int)$_GET['index'] - 1;

                                if (isset($_POST["NonJSUpdate"])) {
                                    $custom_cart = array( 	'info' => array('current_level' => $_POST["cmbCurrentLevel"],
                                                                            'desired_level' => $_POST["cmbDesiredLevel"],
                                                                            'current_class' => $_POST["cmbCurrentClass"],
                                                                            'desired_class' => $_POST["cmbDesiredClass"]
                                                                            )
                                                        );
                                    $custom_cart['option'] = array();
                                    if (count($_POST["cp_option"])) {
                                        foreach ($_POST["cp_option"] as $key => $val) {
                                            $custom_cart['option'][$key] = tep_db_prepare_input($val);
                                        }
                                    }
                                } else {
                                    $custom_cart = $cart->contents[$product_id]['custom'][$custom_product_type][$pwl_index]['content'];
                                }

                                if (sizeof($custom_cart) <= 0) {
                                    $pwl_index = null;
                                }
                            } else {
                                $pwl_index = null;
                                $custom_cart["option"] = $_POST["cp_option"];
                            }
                            
                            if (is_array($cPath_array) && count($cPath_array)) {
                                $inactive_cat_select_sql = "SELECT COUNT(categories_id) as inactive_cat 
                                                            FROM " . TABLE_CATEGORIES . "
                                                            WHERE categories_id IN ('" . implode("', '", $cPath_array) . "') 
                                                                AND categories_status = 0";
                                $inactive_cat_result_sql = tep_db_query($inactive_cat_select_sql);
                                $inactive_cat_row = tep_db_fetch_array($inactive_cat_result_sql);

                                if ($inactive_cat_row['inactive_cat'] > 0)	$active_product = false;
                            }

                            if ($active_product) {
                                $product_info_select_sql = "SELECT p.products_id, p.products_model, p.products_quantity, 
                                                                    p.products_bundle, p.products_bundle_dynamic, p.products_bundle_dynamic_qty, 
                                                                    p.products_price, p.products_tax_class_id, p.products_date_added, 
                                                                    p.products_date_available, p.manufacturers_id, p.custom_products_type_id, 
                                                                    p.products_display  
                                                            FROM " . TABLE_PRODUCTS . " p  
                                                            WHERE p.products_status = '1' 
                                                                AND p.products_id = '" . $product_id . "'";
                                $product_info_result_sql = tep_db_query($product_info_select_sql);

                                if ($product_info = tep_db_fetch_array($product_info_result_sql)) {
                                    if (!tep_not_null($product_info['products_bundle_dynamic']) && !tep_not_null($product_info['products_bundle']) && $product_info['products_display'] != '1') {
                                        $active_product = false;
                                    } else {
                                        $prod_desc_array = tep_get_products_complete_description($product_info['products_id']);
                                        $product_info = array_merge($product_info, $prod_desc_array);
                                    }
                                } else {
                                    $active_product = false;
                                }
                            }
                            
                            if ($active_product) {
                                include_once(DIR_WS_CLASSES . 'direct_topup.php');
                                $direct_topup_obj = new direct_topup();

                                if ((int)$product_info['custom_products_type_id'] == 1) {
                                    //--Start Power Levelling
                                    $level_dependent_package = true;

                                    $range_resource_array = tep_get_bracket_range((int)$_POST['datapool_parent_id']);

                                    if (count($range_resource_array["range"]) < 1) {
                                        $current_level = $desired_level = 0;
                                        $level_dependent_package = false;
                                    } else {
                                        $current_level = (int)$_POST["cmbCurrentLevel"];
                                        $desired_level = (int)$_POST["cmbDesiredLevel"];

                                        if ($current_level < 1) {
                                            $error = 2;
                                        }

                                        if ($desired_level < 1) {
                                            $error = 2;
                                        }

                                        if ($current_level >= $desired_level) {
                                            $error = 2;
                                        }
                                    }

                                    if ($error === FALSE) {
                                        $option_html_array = tep_get_options_html($product_id, $desired_level);
                                        $system_option_array = array();

                                        if (count($option_html_array)) {
                                            foreach ($option_html_array as $option_key => $option_res) {
                                                if ($option_res["data_pool_options_required"] == '1' && $option_res["data_pool_options_input_type"] != 999 && $option_res["data_pool_options_input_type"] != 6) {
                                                    if (!isset($_POST["cp_option"][$option_key]) || trim($_POST["cp_option"][$option_key]) == '') {
                                                        $error = 2;
                                                        break;
                                                    }
                                                }

                                                if ($option_res['data_pool_options_input_type'] == 1 || $option_res['data_pool_options_input_type'] == 2) {
                                                    if (!isset($_POST['cp_option'][$option_key]) || tep_not_null(trim($_POST['cp_option'][$option_key]))) {
                                                        $_POST['cp_option'][$option_key] = htmlentities(trim(tep_db_prepare_input($_POST['cp_option'][$option_key])));
                                                    }
                                                } else if ($option_res["data_pool_options_input_type"] == 999) {
                                                    $system_option_array[$option_res["data_pool_options_input_type"]] = $option_res["data_pool_options_show_supplier"];
                                                }
                                            }
                                        }
                                    }

                                    if ($error === FALSE) {
                                        $level_label = tep_get_bracket_value((int)$_POST['datapool_parent_id'], KEY_PL_LEVEL_LABEL);
                                        $class_label = tep_get_bracket_value((int)$_POST['datapool_parent_id'], KEY_PL_CLASS_LABEL);

                                        tep_calculate_bracket_price((int)$_POST['datapool_parent_id'], $current_level, $desired_level, $price, $eta, $msg, $custom_tags);

                                        $base_price_value = tep_get_bracket_value((int)$_POST['datapool_parent_id'], KEY_PL_BASE_PRICE);
                                        $base_time_value = tep_get_bracket_value((int)$_POST['datapool_parent_id'], KEY_PL_BASE_TIME);
                                        $min_time_value = tep_get_bracket_value((int)$_POST['datapool_parent_id'], KEY_PL_MIN_TIME);

                                        $total_price_info = tep_calculate_grand_total_price($base_price_value, $base_time_value, $price, $eta, $min_time_value, $option_html_array, $_POST["cp_option"]);

                                        $final_array['info'] = array('current_level' => $current_level,
                                                                     'desired_level' => $desired_level,
                                                                     'current_class' => (int)$_POST['cmbCurrentClass'],
                                                                     'desired_class' => (int)$_POST['cmbDesiredClass']
                                                                    ); 

                                        $final_array['db_info'] = array();
                                        if ($level_dependent_package) {
                                            $final_array['db_info']['current_level'] = array('label' => sprintf(TEXT_CURRENT_LABEL, $level_label), 'value' => tep_get_level_alias((int)$_POST['datapool_parent_id'], $current_level) . "##1##"); // Will get setting from admin page, in future
                                            $final_array['db_info']['desired_level'] = array('label' => sprintf(TEXT_DESIRED_LABEL, $level_label), 'value' => tep_get_level_alias((int)$_POST['datapool_parent_id'], $desired_level) . "##1##"); // Will get setting from admin page, in future
                                        }

                                        if ((int)$_POST['cmbCurrentClass'] > 0) {
                                            $final_array['db_info']['current_class'] = array('label' => sprintf(TEXT_CURRENT_LABEL, $class_label), 'value' => tep_get_level_name_path((int)$_POST['cmbCurrentClass'], ' > '));
                                        }

                                        if ((int)$_POST['cmbDesiredClass'] > 0) {
                                            $final_array['db_info']['desired_class'] = array('label' => sprintf(TEXT_DESIRED_LABEL, $class_label), 'value' => tep_get_level_name_path((int)$_POST['cmbDesiredClass'], ' > '));
                                        }

                                        if (count($custom_tags) > 0) {
                                            foreach ($custom_tags as $custom_bracket_tags_res) {
                                                $final_array['db_info'][$custom_bracket_tags_res['key']] = array('label' => $custom_bracket_tags_res["display_label"], 'value' => $custom_bracket_tags_res["value"]);
                                            }
                                        }

                                        if (count($range_resource_array) > 0) {
                                            $final_array['db_bracket_info']['cp_bracket'] = array('label' => 'cp_bracket', 'value' => serialize($range_resource_array));
                                        }
                                    }
                                } else {
                                    //--Start CDKey
                                    $total_price_info = array();
                                    $total_price_info['price'] = $product_info['products_price'];
                                    $total_price_info['price_text'] = null;
                                    $total_price_info['eta'] = null;
                                    $total_price_info['eta_text'] = null;
                                    $msg = '';
                                    if ($error) {} 
                                } //--End CDKey

                                // Storing label for all custom types.
                                $final_array['option'] = array();
                                if (count($_POST["cp_option"])) {
                                    foreach ($_POST["cp_option"] as $key => $val) {
                                        $final_array['option'][$key] = tep_db_prepare_input($val);
                                        $sel_option_info_array = tep_grab_option_title_and_value($option_html_array, $key, $val);

                                        if ($sel_option_info_array != false && is_array($sel_option_info_array)) {
                                            $final_array['db_info']['opt_'.$key] = array('label' => $sel_option_info_array['label'], 'value' => $sel_option_info_array['value'], 'show_supplier' => $option_html_array[$key]["data_pool_options_show_supplier"], 'class' => $option_html_array[$key]["data_pool_options_class"]);
                                        }
                                    }
                                }

                                $final_array['calculated'] = array(	'price' => $total_price_info["price"],
                                                                    'price_text' => $total_price_info["price_text"],
                                                                    'eta' => $total_price_info["eta"],
                                                                    'eta_text' => $total_price_info["eta_text"],
                                                                    'show_supplier' => $system_option_array[999],
                                                                    'msg' => $msg
                                                                  );

                                if (!is_null($pwl_index)) {
                                    $cart->remove($product_id, $pwl_index);
                                    $cart->set_custom_product_index($pwl_index);
                                } else {
                                    $cart->set_custom_product_index(-1);
                                }

                                $cart->set_custom_product_content($final_array);

                                if ($publishers_id = $direct_topup_obj->check_is_supported_by_direct_top_up($product_id)) {
                                    $games_mapping_select_sql = "	SELECT pg.publishers_game
                                                                    FROM " . TABLE_PUBLISHERS_GAMES . " AS pg 
                                                                    INNER JOIN " . TABLE_PUBLISHERS_PRODUCTS . " AS pp
                                                                        ON pg.publishers_games_id = pp.publishers_games_id
                                                                    WHERE pg.publishers_id = '".(int)$publishers_id."'
                                                                        AND pp.products_id = '".(int)$product_id."'";
                                    $games_mapping_result_sql = tep_db_query($games_mapping_select_sql);
                                    $games_mapping_row = tep_db_fetch_array($games_mapping_result_sql);
                                    if ($direct_topup_obj->validate_game_acc($publishers_id, array_merge($_REQUEST['game_info'],array('game'=> $games_mapping_row['publishers_game'])))) {
                                        $cart->add_cart($product_id, $buyqty, '', true, '','', true, $_REQUEST['game_info']);
                                    } else {
                                        $error = 2;
                                        $messageStack->add_session('cart_failure', ERROR_INVALID_TOP_UP_ACCOUNT, 'error');
                                        $error_redirect = tep_href_link(FILENAME_CUSTOM_PRODUCT_INFO, 'products_id='.$product_id.'&index=1', 'SSL');
                                    }
                                } else {
                                    $cart->add_cart($product_id, $buyqty, '', true, '', '', true);
                                }

                                //General error handling.
                                if ($error !== FALSE && !tep_not_empty($error_redirect)) {
                                    $error = 2;
                                    $messageStack->add_session('cart_failure', TEXT_MSG_ADD_TO_CART_FAILURE, 'error');
                                    $error_redirect = tep_href_link(FILENAME_CUSTOM_PRODUCT_INFO, 'products_id='.$product_id.'&index=1', 'SSL');
                                }
                            } else {   // is active_product
                                $error = 2;
                                // inactive product
                            }
                        }   // product permission check
                    // ---------------------------------------------------------------------------------------------------------------------------- End Pwl
                    } else {
                        if ($bypass_DTU_check!== TRUE && $delivery_mode === 6) {
                            if (!isset($_REQUEST['game_info']) || !isset($_REQUEST['game_info']['account']) || !tep_not_null($_REQUEST['game_info']['account'])) {
                                $error = TRUE;
                                $error_dm = ERROR_INVALID_TOP_UP_ACCOUNT;
                            } else {
                                if (isset($_REQUEST['game_info']['account_2']) && isset($_REQUEST['game_info']['account'])) {
                                    if (trim($_REQUEST['game_info']['account']) != trim($_REQUEST['game_info']['account_2'])) {
                                        $error = TRUE;
                                        $error_dm = ERROR_INVALID_TOP_UP_ACCOUNT;
                                    }
                                }

                                if ($error !== TRUE) {
                                    $products_id = $product_id;
                                    $qty = $buyqty;
                                    
                                    $_REQUEST['game_info']['account'] = trim($_REQUEST['game_info']['account']);
                                    include_once(DIR_WS_CLASSES . 'direct_topup.php');
                                    $direct_topup_obj = new direct_topup();

                                    $publishers_games_products_id = $products_id;
                                    if ($publishers_id = $direct_topup_obj->check_is_supported_by_direct_top_up($products_id)) {
                                        $products_select_sql = "SELECT products_bundle, products_bundle_dynamic
                                                                FROM " . TABLE_PRODUCTS . "
                                                                WHERE products_id = '".$publishers_games_products_id."'";
                                        $products_result_sql = tep_db_query($products_select_sql);
                                        if ( $products_row = tep_db_fetch_array($products_result_sql)) {
                                            if ($products_row['products_bundle'] == 'yes' || $products_row['products_bundle_dynamic'] == 'yes') {
                                                $bundle_select_sql = "	SELECT pp.products_id
                                                                        FROM " . TABLE_PUBLISHERS_PRODUCTS . " AS pp 
                                                                        INNER JOIN " . TABLE_PRODUCTS_DELIVERY_INFO . " AS pdi
                                                                            ON pdi.products_id = pp.products_id 
                                                                        INNER JOIN " . TABLE_PRODUCTS_BUNDLES . " AS pb
                                                                            ON pp.products_id=pb.subproduct_id
                                                                        WHERE pb.bundle_id = '".tep_get_prid($publishers_games_products_id)."'
                                                                            AND pdi.products_delivery_mode_id = '6'
                                                                        LIMIT 1";
                                                $bundle_result_sql = tep_db_query($bundle_select_sql);
                                                $bundle_row = tep_db_fetch_array($bundle_result_sql);
                                                $publishers_games_products_id = $bundle_row['products_id'];
                                            }
                                        }

                                        $games_mapping_select_sql = "	SELECT pg.publishers_game, pg.publishers_games_id 
                                                                        FROM " . TABLE_PUBLISHERS_GAMES . " AS pg 
                                                                        INNER JOIN " . TABLE_PUBLISHERS_PRODUCTS . " AS pp
                                                                            ON pg.publishers_games_id = pp.publishers_games_id
                                                                        WHERE pg.publishers_id = '".(int)$publishers_id."'
                                                                            AND pp.products_id = '".(int)$publishers_games_products_id."'";
                                        $games_mapping_result_sql = tep_db_query($games_mapping_select_sql);
                                        $games_mapping_row = tep_db_fetch_array($games_mapping_result_sql);

                                        $get_top_up_info_array = $direct_topup_obj->get_top_up_info($publishers_games_products_id);

                                        $validate_game_acc_array = array(	'game' => $games_mapping_row['publishers_game'], 
                                                                            'publishers_games_id' => $games_mapping_row['publishers_games_id'],
                                                                            'product_id' => $products_id,
                                                                            'amount_type' => $get_top_up_info_array['amount_type']['top_up_info_value'],
                                                                            'amount' => $get_top_up_info_array['amount']['top_up_info_value'],
                                                                            'quantity' => $qty
                                                                        );
                                        if ($direct_topup_obj->validate_game_acc($publishers_id, array_merge($_REQUEST['game_info'], $validate_game_acc_array), $curl_response_array)) {
                                            $prod_cat_path = tep_get_product_path($products_id, true);
                                            if (tep_not_null($prod_cat_path)) {
                                                $prod_cat_path_array = explode('_', $prod_cat_path);
                                                $game_id = $prod_cat_path_array[0];
                                            } else {
                                                $game_id = 0;
                                            }
                                            
                                            $extra_info_array = array();

                                            if (isset($_REQUEST['game_info'])) {
                                                $extra_info_array['top_up_info'] = $_REQUEST['game_info'];
                                                update_dtu_game_info('', $_REQUEST['game_info'], $products_id);
                                                $customer_id_conversion = $direct_topup_obj->get_publishers_games_conf($games_mapping_row['publishers_games_id'], 'CONVERT_CUSTOMER_ID_TO_EMAIL_FLAG');
                                                if ($customer_id_conversion == '1' && !empty($extra_info_array['top_up_info']['account']) && !is_numeric($extra_info_array['top_up_info']['account'])) {
                                                    $extra_info_array['top_up_info']['account'] = $extra_info_array['top_up_info']['account'] . ':~:' . $direct_topup_obj->convert_customer_email_to_id($extra_info_array['top_up_info']['account']);
                                                }
                                            }
                                            if (isset($_REQUEST['delivery_mode']) && (int)$_REQUEST['delivery_mode'] > 0 ) {
                                                $extra_info_array['delivery_mode'] = (int)$_REQUEST['delivery_mode'];
                                            }
                                            
                                            $cart->add_cart($products_id, $qty, '', true, '','', true, $extra_info_array);
                                        } else {
                                            if (isset($curl_response_array['result_code']) && $curl_response_array['result_code'] == '1508') {
                                                $error = TRUE;
                                                $error_qty = ERROR_DTU_EXCEED_TOP_UP_LIMIT;
                                            } else if (isset($curl_response_array['game_acc_status']) && $curl_response_array['game_acc_status'] == 'NOT_RELOADABLE') {
                                                $error = TRUE;
                                                $error_dm = ERROR_INVALID_TOP_UP_ACCOUNT;
                                            } else {
                                                $error = TRUE;
                                                $error_dm = ERROR_UNABLE_VALIDATE_TOP_UP_ACCOUNT;
                                            }
                                        }
                                    }
                                }
                            }
                        } else {
                            // Add product with product type != 6
                            $success = '';
                            $err1 = '';
                            
                            $pd_strlen_show_array = array();
                            $pd_strlen_show_array['ASCII'] = 35;
                            $pd_strlen_show_array['UTF-8'] = 28;

                            if (tep_check_product_region_permission($product_id)) {
                                if ($products_bundle == "yes") {
                                    // static bundle product
                                    $bundle_select_sql = "	SELECT pd.products_name, pb.*, p.products_bundle, p.products_id, p.products_price 
                                                            FROM " . TABLE_PRODUCTS . " AS p 
                                                            INNER JOIN " . TABLE_PRODUCTS_DESCRIPTION . " AS pd 
                                                                ON p.products_id=pd.products_id
                                                            INNER JOIN " . TABLE_PRODUCTS_BUNDLES . " AS pb 
                                                                ON pb.subproduct_id=pd.products_id 
                                                            WHERE pb.bundle_id = '" . tep_db_input($product_id) . "' AND language_id = '1'";
                                    $bundle_result_sql = tep_db_query($bundle_select_sql);

                                    $customers_basket_select_sql = "SELECT customers_basket_quantity FROM " . TABLE_CUSTOMERS_BASKET . " WHERE customers_id = '" . (int)$customer_id . "' AND products_id = '" . tep_db_input($product_id) . "'";
                                    $customers_basket_result_sql = tep_db_query($customers_basket_select_sql);
                                    $customers_basket_info = tep_db_fetch_array($customers_basket_result_sql);

                                    $reduce_to_qty = '';
                                    while ($bundle_data = tep_db_fetch_array($bundle_result_sql)) {
                                        $product_select_sql = "	SELECT p.products_id, p.products_quantity, p.products_status, p.products_purchase_mode, p.products_out_of_stock_level 
                                                                FROM " . TABLE_PRODUCTS . " AS p 
                                                                WHERE p.products_id = '" . $bundle_data["products_id"] . "'";
                                        $product_result_sql = tep_db_query($product_select_sql);

                                        if ($product_info = tep_db_fetch_array($product_result_sql)) {
                                            if (!$product_info["products_status"] || $product_info["products_purchase_mode"] == '3') {	// Inactive or Out of Stock
                                                $err1 = "SL";
                                                break;
                                            }

                                            switch ($product_info["products_purchase_mode"]) {
                                                case '1':	// Always Add to Cart
                                                case '2':	// Always Pre-Order
                                                    break;
                                                case '4':	// Auto Mode
                                                    if (tep_not_null($product_info["products_out_of_stock_level"])) {	// If there is setting for out of stock level 
                                                        $current_qty_in_cart = tep_get_total_product_in_cart($customer_id, $product_info["products_id"]);

                                                        $suggested_amt = (int) ((tep_get_products_stock($bundle_data["subproduct_id"], true)-$current_qty_in_cart) / $bundle_data["subproduct_qty"]);
                                                        if ($reduce_to_qty != '') {
                                                            if ($reduce_to_qty > $suggested_amt)
                                                                $reduce_to_qty = $suggested_amt;
                                                        } else {
                                                            $reduce_to_qty = $suggested_amt;
                                                        }

                                                        if ($product_info['products_quantity'] - ($bundle_data['subproduct_qty']*$buyqty + $current_qty_in_cart) < (int)$product_info["products_out_of_stock_level"]) {
                                                            $err1 = "SL";
                                                            break 2;
                                                        }
                                                    }
                                                    break;
                                            }
                                        }
                                    }

                                    if ($err1 !== '') {
                                        $error = TRUE;
                                        $error_qty = ERROR_CHECKOUT_QUANTITY_CONTROL_EXCEED;
                                    } else {
                                        if ($cart->get_custom_prd_type($product_id)=='cdkey') {
                                            $extra_info_array = array();
                                            if (count($user_extra_info_array)) {
                                                $extra_info_array['top_up_info'] = $user_extra_info_array;
                                            }
                                            if ($delivery_mode > 0) {
                                                $extra_info_array['delivery_mode'] = $delivery_mode;
                                            } else {
                                                $extra_info_array['delivery_mode'] = 5;
                                            }
                                        } else {
                                            $extra_info_array = $user_extra_info_array;
                                        }

                                        $parameters = array('action', 'pid', 'success');
                                        $cart->add_cart($product_id, $buyqty, '', true, '', '', false, $extra_info_array);
                                        $success = 'CA';
                                    }
                                } else if ($products_bundle_dynamic == "yes") {
                                    $err1 = '';
                                    $extra_info_array = array();
                                    
                                    if ($cart->get_custom_prd_type($product_id)=='cdkey') {
                                        if (count($user_extra_info_array)) {
                                            $extra_info_array['top_up_info'] = $user_extra_info_array;
                                        }
                                        if ($delivery_mode > 0) {
                                            $extra_info_array['delivery_mode'] = $delivery_mode;
                                        } else {
                                            $extra_info_array['delivery_mode'] = 5;
                                        }
                                    }

                                    // dynamic bundle product
                                    $bd_products_array = array();
                                    $total_bundle_weight = 0;

                                    $bundle_weight_select_sql = "SELECT products_bundle_dynamic_qty FROM " . TABLE_PRODUCTS . " WHERE products_id='".tep_db_input($product_id)."'";
                                    $bundle_weight_result_sql = tep_db_query($bundle_weight_select_sql);
                                    $bundle_weight_row = tep_db_fetch_array($bundle_weight_result_sql);

                                    if ($bundle_weight_row["products_bundle_dynamic_qty"]) {
                                        //$allowed_weight = ($bdn == 'y') ? (int)$_REQUEST['buyqty']*$bundle_weight_row["products_bundle_dynamic_qty"] : ($cart->get_quantity($product_id) + (int)$_REQUEST['buyqty'])*$bundle_weight_row["products_bundle_dynamic_qty"] ;
                                        // only consider present add to cart, no need to bother the one already in cart
                                        $allowed_weight = $buyqty*$bundle_weight_row["products_bundle_dynamic_qty"] ;

                                        $bundle_select_sql = "	SELECT pd.products_name, pb.*, p.products_bundle, p.products_bundle_dynamic, p.products_id, p.products_price 
                                                                FROM " . TABLE_PRODUCTS . " AS p 
                                                                INNER JOIN " . TABLE_PRODUCTS_DESCRIPTION . " AS pd
                                                                    ON p.products_id=pd.products_id
                                                                INNER JOIN " . TABLE_PRODUCTS_BUNDLES . " AS pb 
                                                                    ON pb.subproduct_id=pd.products_id 
                                                                WHERE pb.bundle_id = '" . tep_db_input($product_id) . "' AND language_id = '1'";
                                        $bundle_result_sql = tep_db_query($bundle_select_sql);

                                        while ($bundle_data = tep_db_fetch_array($bundle_result_sql)) {
                                            // Omit those inactive or has the out of stock purchase mode
                                            $product_select_sql = "	SELECT p.products_id, p.products_quantity, p.products_purchase_mode, p.products_out_of_stock_level 
                                                                    FROM " . TABLE_PRODUCTS . " AS p 
                                                                    WHERE p.products_id = '" . $bundle_data['products_id'] . "' AND p.products_status = '1' AND p.products_purchase_mode<>3 ";
                                            $product_result_sql = tep_db_query($product_select_sql);
                                            if ($product_info = tep_db_fetch_array($product_result_sql)) {
                                                $temp_val = ${"q_".$product_info['products_id']};  // Missing qty box for each subproduct
                                                $bd_products_array[] = array('id' => $product_info['products_id'], 
                                                                             'bd_products_qty' => $temp_val);

                                                $total_bundle_weight += (int)$temp_val * $bundle_data["subproduct_weight"];

                                                switch ($product_info["products_purchase_mode"]) {
                                                    case '1':	// Always Add to Cart
                                                    case '2':	// Always Pre-Order
                                                        break;
                                                    case '4':	// Auto Mode
                                                        if (tep_not_null($product_info["products_out_of_stock_level"])) {	// If there is setting for out of stock level 
                                                            $total_stock = $product_info['products_quantity'] ;
                                                            if ($bdn == 'y') {
                                                                $current_qty_in_cart = tep_get_total_product_in_cart($customer_id, $product_info["products_id"], $product_id);		// exclude current product since it is in $temp_val
                                                            } else {
                                                                $current_qty_in_cart = tep_get_total_product_in_cart($customer_id, $product_info["products_id"]);

                                                            }
                                                            $total_order = ($bundle_data["subproduct_qty"] * (int)$temp_val) + $current_qty_in_cart;

                                                            if ($total_order && ($total_stock - $total_order < (int)$product_info["products_out_of_stock_level"])) {
                                                                $err1 = "SL";
                                                                break 2;
                                                            }
                                                        }
                                                        break;
                                                }

                                                if ($total_bundle_weight && $total_bundle_weight > $allowed_weight) {
                                                    $err1 = "OW";	// overweight message
                                                    break;
                                                }
                                            }
                                        }
                                        if (!$total_bundle_weight) {
                                            $err1 = "ES";	// no selections had been made message
                                        } else if ($total_bundle_weight > $allowed_weight) {
                                            $err1 = "OW";	// overweight message
                                        } else if ($total_bundle_weight < $allowed_weight) {
                                            $err1 = "UW";	// underweight message
                                        }
                                    } else {
                                        $err1 = "SL";
                                    }

                                    if ($err1 !== '') {
                                        $error = TRUE;
                                        $error_qty = ERROR_CHECKOUT_QUANTITY_CONTROL_EXCEED;
                                    } else {
                                        if ($bdn == 'y') {
                                            $cart->add_cart($product_id, $buyqty,'','',$bd_products_array, 'y', '', $extra_info_array);
                                        } else {
                                            $cart->add_cart($product_id, $cart->get_quantity($product_id)+$buyqty,'','',$bd_products_array, '', '', $extra_info_array);
                                        }
                                        $success = 'CA';
                                    }
                                } else {
                                    // single product
                                    $suggested_amt = 0;
                                    $prod_inventory_select_sql = "	SELECT products_quantity, products_purchase_mode, products_out_of_stock_level 
                                                                    FROM " . TABLE_PRODUCTS . " 
                                                                    WHERE products_id = '" . tep_db_input($product_id) . "' AND products_status=1";
                                    $prod_inventory_result_sql = tep_db_query($prod_inventory_select_sql);
                                    if ($prod_inventory_row = tep_db_fetch_array($prod_inventory_result_sql)) {
                                        switch($prod_inventory_row['products_purchase_mode']) {
                                            case '1':	// Always Add to Cart
                                            case '2':	// Always Pre-Order
                                                $show_it = 1;
                                                break;
                                            case '3':	// Always Out of Stock
                                                $show_it = 0;
                                                break;
                                            case '4':	// Auto Mode
                                                if (tep_not_null($prod_inventory_row['products_out_of_stock_level'])) {	// If there is setting for out of stock level 
                                                    $current_qty_in_cart = tep_get_total_product_in_cart($customer_id, $product_id);
                                                    $suggested_amt = (int) (tep_get_products_stock($product_id, true)-$current_qty_in_cart);

                                                    if ( (int)$prod_inventory_row['products_quantity'] - ($buyqty+(int)$current_qty_in_cart) < (int)$prod_inventory_row["products_out_of_stock_level"]) {
                                                        $show_it = 0;
                                                        $error = TRUE;
                                                        $error_qty = ERROR_CHECKOUT_QUANTITY_CONTROL_EXCEED;
                                                    } else {
                                                        $show_it = 1;
                                                    }
                                                } else {
                                                    $show_it = 1;
                                                }
                                                break;
                                        }

                                        $extra_info_array = array();
                                        if ($cart->get_custom_prd_type($product_id)=='cdkey') {
                                            $isCustom = true;

                                            //$product_info_query = tep_db_query("select p.products_price from " . TABLE_PRODUCTS . " AS p where p.products_id = '" . tep_db_input($product_id) . "'");
                                            //$product_info = tep_db_fetch_array($product_info_query);

                                            $final_array = array();
                                            //$final_array['calculated'] = array(	'price' => $product_info["products_price"] );
                                            $cart->set_custom_product_index(-1);
                                            $cart->set_custom_product_content($final_array);

                                            $extra_info_array['top_up_info'] = $user_extra_info_array;
                                            if ($delivery_mode > 0) {
                                                $extra_info_array['delivery_mode'] = $delivery_mode;
                                            }
                                        } else {
                                            $isCustom = false;

                                            if ($cart->get_custom_prd_type($product_id) == 'hla') {
                                                $buyqty = 1; // HLA only allow 1 qty.
                                                $extra_info_array = array ( 'hla_account_id' => $hla_id );
                                            }
                                        }

                                        $cart->add_cart($product_id, $buyqty, '', true, '', '', $isCustom, $extra_info_array);
                                        $success = 'CA';
                                    }
                                }
                            } else {
                                $error = 2; // product does not exist in your region.
                            }
                        }   // delivery method
                    }   // Product type
                } else {
                    $error = 2;  // empty product id.
                }
            } else {
                $error = 2;  // cannot access directly to this stage
            }
            
            if ($error !== FALSE) {   // TRUE : return error and continue; 2: return error & end here.
                $response_array['err'] = $error;
                
                if ($error_redirect) {
                    $response_array['redirect'] = $error_redirect;
                }
                
                if ($error === 2) {
                    break;
                }
            }
        case 'get_confirm_order_content':
            $content = '';
            $header_label = '';
            $extra_param = '';
            $pname = '';
            
            if (pfv_next_access($user_confirmed) === 'confirm_order') {
                $custom_product_type = $cart->get_custom_prd_type($product_id);
                $delivery_section_content = '';
                $qty_content = '';
                
                // if we have been here before and are coming back get rid of the credit covers variable
                if(tep_session_is_registered('credit_covers')) tep_session_unregister('credit_covers');  //ICW ADDED FOR CREDIT CLASS SYSTEM
                
                // if no billing destination address was selected, use the customers own address as default
                if (!tep_session_is_registered('billto')) {
                    tep_session_register('billto');
                    $billto = $_SESSION['customer_default_address_id'];
                } else {
                    // verify the selected billing address
                    $check_address_query = tep_db_query("select count(*) as total from " . TABLE_ADDRESS_BOOK . " where customers_id = '" . (int)$customer_id . "' and address_book_id = '" . (int)$billto . "'");
                    $check_address = tep_db_fetch_array($check_address_query);

                    if ($check_address['total'] != '1') {
                        $billto = $_SESSION['customer_default_address_id'];
                        if (tep_session_is_registered('payment')) tep_session_unregister('payment');
                    }
                }

                // a session array storing firstname and lastname for 2CO credit card payment
                if (!tep_session_is_registered('pm_2CO_cc_owner')) tep_session_register('pm_2CO_cc_owner');
                if (!tep_session_is_registered('credit_card_owner')) tep_session_register('credit_card_owner');
                if (!tep_session_is_registered('payment_inputs_array')) tep_session_register('payment_inputs_array');

                $_SESSION['cot_gv'] = 'on';
                
                $order = new order;
                $order_total_modules = new order_total;//ICW ADDED FOR CREDIT CLASS SYSTEM
                
                if (isset($_POST['gv_redeem_code'])) {
                    unset($_SESSION['cc_id']);
                    
                    if (tep_not_empty($_POST['gv_redeem_code'])) {
                        $error_coupon = FALSE;
                        $order_total_modules->collect_posts();

                        if ($error_coupon !== FALSE) {
                            $error = TRUE;
                        }
                    }
                }
                
                $order_total_modules->pre_confirmation_check();	// Need it here to decide show/hide payment method options
                $payment_modules = new payment;
                
                if ($user_confirmed['skip_cfm_order'] === 1) {
                    $fixed_qty = false;
                    $extra_info_str = '';
                    $qty_onchange = 'pfv_confirm_order_qty(this);';
                    
                    if ($custom_product_type == 'game_currency') {
                        $product_info_arr = get_product_info($product_id, $customers_groups_id, $languages_id);
                        $header_label = $product_info_arr['products_cat'];
                        $extra_param = $product_info_arr['categories_id'];
                        unset($product_info_arr);
                    } else if ($custom_product_type == 'hla') {
                        $fixed_qty = true;
                        $product_info_arr = get_hla_product_info($hla_id, $hla_game_id);
                        $pname = $product_info_arr['products_name'];
                        $header_label = $product_info_arr['category_name'];
                        $extra_param = $product_info_arr['products_description'];
                    } else if ($custom_product_type == 'power_leveling') {
                        $fixed_qty = true;
                        $extra_param = TEXT_PWL_IMPORTANT_NOTICE;
                    }
                    
                    if ($delivery_mode === 6) {
                        $dtu_calc_array = array(
                            1 => tep_get_products_info($product_id, 'products_dtu_extra_info_1', $languages_id, $default_languages_id),
                            2 => tep_get_products_info($product_id, 'products_dtu_extra_info_2', $languages_id, $default_languages_id),
                            3 => tep_get_products_info($product_id, 'products_dtu_extra_info_3', $languages_id, $default_languages_id)
                        );
                        
                        foreach ($dtu_calc_array as $idx => $data) {
                            if (tep_not_empty($data)) {
                                if ($idx == 1) {
                                    $extra_info_str .= '<span class="hd3 ext'.$idx.'" data-default="' . $data . '">' . $data . '</span>';
                                } else if ($idx == 2) {
                                    $extra_info_str .= '<span class="hd3 ext'.$idx.'" data-default="' . $data . '">' . $data*$buyqty . '</span>';
                                } else {
                                    $extra_info_str .= '<span class="hd5 ext'.$idx.'">' . $data . '</span>';
                                }
                            }
                        }
                        
                        if (tep_not_empty($extra_info_str)) {
                            $qty_onchange = 'updateDTUextra(this);' . $qty_onchange;
                            $extra_info_str = '<div class="dtu_extra_info">=&nbsp;' . $extra_info_str . '</div>';
                        }
                        
                        unset($dtu_calc_array);
                    }
                    
                    if ($fixed_qty === TRUE) {
                        $qty_selection_range_array = array('1');
                    } else {
                        $qty_selection_range_array = range(1, 100);
                    }
                    
                    foreach ($qty_selection_range_array as $idx) {$qty_array[] = array('id' => $idx, 'text' => $idx);}
                    $qty_content = tep_draw_pull_down_menu("game_info[qty]", $qty_array, $buyqty, ' id="pfv_qty" style="width: 100px;" onchange="'.$qty_onchange.'"');
                    $dm_arr = get_delivery_section_content($custom_product_type, $product_id, $delivery_mode, $extra_param);
                    if (count($dm_arr)) {
                        $delivery_section_content = '<div class="tooltips">' . $dm_arr['tooltips'] . '</div>';
                        $delivery_section_content .= tep_draw_form('dm_form', '', 'post', ' id="dm_form" onsubmit="return false;"') . $dm_arr['html'] . '</form>';
                        $delivery_section_content .= '<div class="error_msg icon_err_msg"></div>';
                        $delivery_section_content .= '<div class="dmnote">' . $dm_arr['note'] . '</div>';
                    }
                    
                    $content = '<table id="tbl_'.$index.'" class="purchase_confirm_box" width="100%" cellpadding="0" cellspacing="0"><tr>' . 
                                '<td class="dm">' .
                                    $delivery_section_content .
                                '</td>' .
                                '<td class="qty">' . 
                                    '<span class="hds3">' . ENTRY_QTY . ':</span>' . 
                                    '<div class="shd1" style="padding-top: 15px; width: 100px;">' . $qty_content . '</div>' . 
                                    $extra_info_str .
                                    '<div class="error_msg icon_err_msg"></div>' . 
                                '</td>' . 
                                '<td class="ot">' . $cart->get_cart2() . '</td>' .
                                '</tr></table>';
                    
                    $cart->reset(true);
                } else if ($user_confirmed['skip_cfm_order'] === 2) {
                    $content = $cart->get_cart2();
                    $cart->reset(true);
                } else {
                    $content = '';
                }
            }
            
            if ($error) {
                if ($error_message) {
                    $response_array['msg'] = $error_message;
                }
                
                if (tep_not_empty($error_qty)) {
                    $response_array['msg4qty'] = $error_qty;
                }
                
                if (tep_not_empty($error_dm)) {
                    $response_array['msg4dm'] = $error_dm;
                }
            }
            
            $response_array['idx'] = $index;
            $response_array['err'] = $error;
            $response_array['content'] = $content;
            $response_array['header_ext'] = $header_label;
            $response_array['pname'] = $pname;
            $response_array['cfm'] = implode('', $user_confirmed);
            
            break;
        case 'cfm_password':
            $error = FALSE;
            
            if (tep_not_empty($user_cfm_password) && isset($_SESSION['customer_id'])) {
                $customer_sql = "   SELECT customers_password 
                                    FROM " . TABLE_CUSTOMERS . " 
                                    WHERE customers_status = '1' 
                                        AND customers_id = '" . $_SESSION['customer_id'] . "' 
                                        AND FIND_IN_SET( '0', customers_login_sites)";
                $check_customer_query = tep_db_query($customer_sql);
                if ($check_customer = tep_db_fetch_array($check_customer_query)) {
                    if (tep_validate_password($user_cfm_password, $check_customer['customers_password'])) {
                        update_lifetime_cookies();
                        $response_array['redirect'] = tep_href_link(FILENAME_CHECKOUT_SHIPPING, 'cfm_proceed=1', 'SSL');
                    } else {
                        $response_array['msg'] = ERROR_WRONG_PASSWORD;
                        $error = TRUE;
                    }
                }
            }
            
            $response_array['err'] = $error;
            break;
        case 'get_qna_content':
            $return_content = '';
            $return_header = '';
            
            if (isset($_SESSION['need_sc_usage_qna']) && $_SESSION['need_sc_usage_qna'] == 'true') {
                $return_content = get_qna_box();
                $return_header = TEXT_SECRET_QNA;
            }
            
            $response_array['header'] = $return_header;
            $response_array['content'] = $return_content;
            $response_array['width'] = '530px';
            break;
        case 'cfm_qna':
            $error = TRUE;
            
            if (isset($_SESSION['customer_id']) && isset($_SESSION['need_sc_usage_qna']) && $_SESSION['need_sc_usage_qna'] == 'true') {
                if ($customers_security_obj->validate_customer_security_answer($_POST['answer'], 'qna', ENTRY_MISMATCH_ANSWER_ERROR) === TRUE) {
                    $response_array['msg'] = ENTRY_MISMATCH_ANSWER_ERROR;
                } else {
                    $sql_data_array = array('customer_info_account_dormant' => 0);
                    tep_db_perform(TABLE_CUSTOMERS_INFO, $sql_data_array, 'update', "customers_info_id = '" . $_SESSION['customer_id'] . "'");
                    update_lifetime_cookies();
                    
                    unset($_SESSION['need_sc_usage_qna']);
                    $error = FALSE;
                }
            }
            
            $response_array['err'] = $error;
            if (!$error && $index !== 0) {
                $response_array['redirect'] = tep_href_link(FILENAME_CHECKOUT_SHIPPING, 'cfm_proceed=1', 'SSL');
            }
            break;
        case 'get_flow':
            if (tep_not_empty($index)) {
                tep_setcookie('pfv_idx', $index, time()+60*60*24, $cookie_path, $cookie_domain);
            } else if (isset($_COOKIE['pfv_idx'])) {
                $index = $_COOKIE['pfv_idx'];
            }
            
            $next_is = pfv_next_access($user_confirmed);
            
            if ($error) {
                $response_array['msg'] = $error_message;
            } else {
                if ($next_is === 'ask_password') {
                    $response_array['header'] = TEXT_LOGIN_PASSWORD; //'LOGIN PASSWORD';
                    $response_array['content'] = get_confirm_password_box($index);
                } else if ($next_is === 'ask_qna') {
                    $response_array['header'] = TEXT_SECRET_QNA;
                    $response_array['content'] = get_qna_box($index);
                    $response_array['width'] = '530px';
                } else if ($next_is === 'go_chkout') {
                    $response_array['redirect'] = tep_href_link(FILENAME_CHECKOUT_SHIPPING, 'cfm_proceed=1', 'SSL');
                } else if ($next_is === 'sc_choice') {
                    $response_array['header'] = HEADER_STORE_CREDIT_USE;
                    $response_array['content'] = get_confirm_store_credit_box($index, $_SESSION['customer_id'], $currency);
                    $response_array['css_padding'] = "15px";
                }
            }
            
            $response_array['idx'] = $index;    // default : 0
            $response_array['nxt'] = $next_is;
            
            break;
    }
}

echo json_encode($response_array);

function tep_xmlhttp_get_latest_news($cat_id, $languages_id, $prod_tpl) {
	$tpl = $prod_tpl;
	$news_html = '';
	$LATEST_NEWS_TYPE = '5';
	define('LATEST_NEWS_BOX', 'classic');
	
	$cPath_array = array($cat_id);
	tep_get_parent_categories($cPath_array, $cat_id);
	$cPath_array = array_reverse($cPath_array);
	ob_start();
	
    include(DIR_WS_MODULES . FILENAME_LATEST_NEWS);
    
    $news_html = ob_get_contents();
	ob_end_clean();
	
	return $news_html;
}

function get_index_and_type ($counter) {
	$return_index = 0;
	$return_type = '';
	
	if (tep_not_empty($counter)) {
		if (stripos($counter, '_') > 0) {
			list($return_type, $index) = explode("_", $counter);
			$return_index = $return_type . '_' . filter_var($index, FILTER_SANITIZE_NUMBER_INT);
		} else {
			$return_index = filter_var($index, FILTER_SANITIZE_NUMBER_INT);
		}
	}
	
	return array($return_index, $return_type);
}

function get_hla_info ($input_data) {
    $return_array = array('','');
    if (tep_not_empty($input_data)) {
		if (stripos($input_data, '-') > 0) {
            list($id, $game_id) = explode("-", $input_data);
            $return_array = array(
                (int)$id,
                (int)$game_id
            );
        }
    }
    return $return_array;
}

function get_delivery_section_content ($custom_product_type, $pID, $delivery_mode, $extra_param = '') {
    global $cart;
    $return_array = array();
    
    switch ($custom_product_type) {
        case 'cdkey':
            $return_array = get_cdkey_delivery_section_content($pID, $delivery_mode);
            break;
        case 'game_currency':
            $return_array = get_currency_delivery_section_content($pID, $delivery_mode, $extra_param);
            break;
        default:
            $return_array = get_general_delivery_section_content($pID, $extra_param);
            break;
    }
    
    
    return $return_array;
}

function get_cdkey_delivery_section_content ($pID, $delivery_mode) {
    $default_dm = '';
    $delivery_methods_array = array();
    $delivery_methods_note_array = array();
    $delivery_mode_icons_array = array();
    $product_delivery_mode_array = array();
    
    $side_dtu_tooltips_html = "<b>" . TEXT_INFO_DELIVERY_SEND_TO_ACCOUNT . "</b><br>" . TEXT_INFO_DELIVERY_SEND_TO_ACCOUNT_DESCRIPTION . "<br><br>";
	$side_dtu_tooltips_html .= "<b>" . TEXT_INFO_DELIVERY_DIRECT_TOP_UP . "</b><br>" . TEXT_INFO_DELIVERY_DIRECT_TOP_UP_DESCRIPTION . "<br><br>";
    $delivery_methods_tooltips = '<div style="float:left;padding-top:2px;"><span class="hds3">'. ENTRY_DELIVERY_METHOD . ': </span></div>'; //<div style="float: left; margin-left: 2px; cursor:pointer;">'.tep_image(DIR_WS_ICONS . 'help-small.png', '', '', '', ' class="ogm_dtu_tooltips" style="vertical-align:middle;cursor:pointer;" ogm_content="'.$side_dtu_tooltips_html.'" ').'</div><div style="clear:both;"></div>';
    $delivery_methods_tooltips .= '<div style="float: left; margin-left: 2px; cursor:pointer;">' . tep_image(DIR_WS_ICONS . 'help-small.png', $side_dtu_tooltips_html, '16', '16', ' class="dmTT"') . '</div><div style="clear:both;"></div>';
    $delivery_methods_tooltips .= '<script>jQuery(function(){jQuery(".dmTT").tipTip({maxWidth: "270px", edgeOffset: 0, defaultPosition: "right", keepAlive: true});});</script>';
    
    $product_delivery_mode = product::get_product_delivery_mode($pID);
    foreach ($product_delivery_mode as $dm_id) {
        $product_delivery_mode_array[$dm_id] = array();
    }
    
    $products_delivery_mode_select_sql = "	SELECT pdm.products_delivery_mode_id, pdm.products_delivery_mode_title
                                            FROM " . TABLE_PRODUCTS_DELIVERY_MODE . " AS pdm
                                            WHERE pdm.products_delivery_mode_id IN ('".implode("','", array_keys($product_delivery_mode_array))."')";
    $products_delivery_mode_result_sql = tep_db_query($products_delivery_mode_select_sql);
    while ($products_delivery_mode_row = tep_db_fetch_array($products_delivery_mode_result_sql)) {
        $product_delivery_mode_array[$products_delivery_mode_row['products_delivery_mode_id']] = $products_delivery_mode_row['products_delivery_mode_title'];
    }
    
    if (tep_not_null($delivery_mode) && count($product_delivery_mode_array)) {
        switch ($delivery_mode) {
            case 6 : // DTU
                if (isset($product_delivery_mode_array['6'])) {
                    $product_delivery_mode_array = array('6' => $product_delivery_mode_array['6']);
                }
                break;
            default : // General
                unset($product_delivery_mode_array['6']);
                break;
        }
    }
    
    if (count($product_delivery_mode_array)) {
        foreach ($product_delivery_mode_array as $dm_id => $product_delivery_mode_data_loop) {
            $default_dm = $default_dm=='' ? $dm_id : $default_dm;
            $display_delivery_mode_label_array = array();
            
            switch ($dm_id) {
                case '5':
                    $display_delivery_mode_label_array[] = TEXT_INFO_DELIVERY_SEND_TO_ACCOUNT;
                    $delivery_mode_icons_array[] = tep_image(DIR_WS_ICONS . 'icon_delivery_mode_ogm_account.gif', '','','',' class="ogm_tooltips" style="vertical-align:middle;cursor:pointer;" ogm_title="'.TEXT_INFO_DELIVERY_SEND_TO_ACCOUNT.'" ogm_content="'.TEXT_INFO_DELIVERY_SEND_TO_ACCOUNT_DESCRIPTION.'"');
                    break;
                case '7':
                    $display_delivery_mode_label_array[] = TEXT_INFO_DELIVERY_IN_STORE_PICKUP;
                    $delivery_mode_icons_array[] = tep_image(DIR_WS_ICONS . 'icon_delivery_mode_pickup.gif', '','','',' class="ogm_tooltips" style="vertical-align:middle;cursor:pointer;" ogm_title="'.TEXT_INFO_DELIVERY_IN_STORE_PICKUP.'" ogm_content="'.TEXT_INFO_DELIVERY_IN_STORE_PICKUP_DESCRIPTION .'"');
                    break;
                case '6':
                    $display_delivery_mode_label_array[] = TEXT_INFO_DELIVERY_DIRECT_TOP_UP . tep_draw_hidden_field('err_dtu_msg', ERROR_INVALID_TOP_UP_ACCOUNT);
                    $delivery_mode_icons_array[] = tep_image(DIR_WS_ICONS . 'icon_delivery_mode_topup.gif', '','','',' class="ogm_tooltips" style="vertical-align:middle;cursor:pointer;" ogm_title="'.TEXT_INFO_DELIVERY_DIRECT_TOP_UP.'" ogm_content="'.TEXT_INFO_DELIVERY_DIRECT_TOP_UP_DESCRIPTION .'"');
                    
                    include_once(DIR_WS_CLASSES . 'direct_topup.php');
                    $direct_topup_obj = new direct_topup();
                    $main_product_id = $pID;
                    
                    $products_select_sql = "SELECT products_id, products_bundle, products_bundle_dynamic, products_price, products_main_cat_id, custom_products_type_id 
                                            FROM " . TABLE_PRODUCTS . "
                                            WHERE products_id = '".$pID."'";
                    $products_result_sql = tep_db_query($products_select_sql);
                    $product_info_row = tep_db_fetch_array($products_result_sql);
                    if ($product_info_row['products_bundle'] == 'yes' || $product_info_row['products_bundle_dynamic'] == 'yes') {
                        $bundle_select_sql = "	SELECT pp.products_id
                                                FROM " . TABLE_PUBLISHERS_PRODUCTS . " AS pp 
                                                INNER JOIN " . TABLE_PRODUCTS_DELIVERY_INFO . " AS pdi
                                                    ON pdi.products_id = pp.products_id 
                                                INNER JOIN " . TABLE_PRODUCTS_BUNDLES . " AS pb
                                                    ON pp.products_id=pb.subproduct_id
                                                WHERE pb.bundle_id = '".tep_get_prid($pID)."'
                                                    AND pdi.products_delivery_mode_id = '6'
                                                LIMIT 1";
                        $bundle_result_sql = tep_db_query($bundle_select_sql);
                        $bundle_row = tep_db_fetch_array($bundle_result_sql);
                        $game_input_array = $direct_topup_obj->get_game_input($bundle_row['products_id']);
                        $main_product_id = $bundle_row['products_id'];
                    } else {
                        $game_input_array = $direct_topup_obj->get_game_input($main_product_id);
                    }

                    if (count($game_input_array)) {
                        // Get Prefill DTU info
                        $prefill_dtu_info_array = get_dtu_game_info('', $pID);
                        // Get Prefill DTU info
                        
                        foreach ($game_input_array as $game_input_key_loop => $game_input_data_loop) {
                            $top_up_info_key = $game_input_data_loop['top_up_info_key'];
                            $default_info = isset($prefill_dtu_info_array[$top_up_info_key]) && tep_not_empty($prefill_dtu_info_array[$top_up_info_key]) ? $prefill_dtu_info_array[$top_up_info_key] : '';
//                            $default_info = tep_not_empty($default_info) ? $default_info : (isset($game_input_data_loop['top_up_info_display'])?$game_input_data_loop['top_up_info_display']:'');
                            $dtu_field_title = ($game_input_data_loop['top_up_info_display'] != '' ? $game_input_data_loop['top_up_info_display'] . ': <br />' : '');
                            
                            switch ($top_up_info_key) {
                                case 'server':
                                    // server list
                                    $servers_select_sql = "	SELECT pg.publishers_server
                                                            FROM " . TABLE_PUBLISHERS_GAMES . " AS pg
                                                            INNER JOIN " . TABLE_PUBLISHERS_PRODUCTS . " AS pp
                                                                ON pg.publishers_games_id = pp.publishers_games_id
                                                            WHERE pp.products_id = '".(int)$main_product_id."'";
                                    $servers_result_sql = tep_db_query($servers_select_sql);
                                    $servers_row = tep_db_fetch_array($servers_result_sql);
                                    $servers_tmp_array = json_decode($servers_row['publishers_server'], 1);
                                    $servers_array = array();
                                    $servers_array[] = array(	
                                        'id' => '',
                                        'text' => PULL_DOWN_DEFAULT
                                    );
                                    
                                    if (isset($servers_tmp_array)) {
                                        foreach ($servers_tmp_array as $servers_id_loop => $server_name_loop) {
                                            $servers_array[] = array(	
                                                'id' => $servers_id_loop,
                                                'text' => $server_name_loop
                                            );
                                        }
                                    }
                                    
                                    $display_delivery_mode_label_array[] = $dtu_field_title . tep_draw_pull_down_menu("game_info[".$game_input_key_loop."]", $servers_array, $default_info, ' default_text="' . PULL_DOWN_DEFAULT . '" onfocus="jQuery(this).removeClass(\'productListingDTUPreInput\').addClass(\'productListingDTUAftInput\')" onblur="if(this.value==\'\') {jQuery(this).removeClass(\'productListingDTUAftInput\').addClass(\'productListingDTUPreInput\')}else{jQuery(this).removeClass(\'productListingDTUPreInput\').addClass(\'productListingDTUAftInput\')}" class="productListingDTUPreInput dtu_customer_input_game_info" ');
                                    break;
                                case 'account_platform':
                                    // account platform
                                    $account_platform_tmp_array = $direct_topup_obj->get_account_platform($main_product_id);
                                    $account_platform_array = array();
                                    $account_platform_array[] = array(	
                                        'id' => '',
                                        'text' => PULL_DOWN_DEFAULT
                                    );
                                    if (isset($account_platform_tmp_array)) {
                                        foreach ($account_platform_tmp_array as $account_platform_id_loop => $account_platform_name_loop) {
                                            $account_platform_array[] = array(	
                                                'id' => $account_platform_id_loop,
                                                'text' => $account_platform_name_loop
                                            );
                                        }
                                    }
                                    $display_delivery_mode_label_array[] = $dtu_field_title . tep_draw_pull_down_menu("game_info[".$game_input_key_loop."]", $account_platform_array, $default_info, ' default_text="' . PULL_DOWN_DEFAULT . '" onfocus="jQuery(this).removeClass(\'productListingDTUPreInput\').addClass(\'productListingDTUAftInput\')" onblur="if(this.value==\'\') {jQuery(this).removeClass(\'productListingDTUAftInput\').addClass(\'productListingDTUPreInput\')}else{jQuery(this).removeClass(\'productListingDTUPreInput\').addClass(\'productListingDTUAftInput\')}" class="productListingDTUPreInput dtu_customer_input_game_info" ');
                                    break;
                                case 'character':
                                    if ($direct_topup_obj->character_is_sync($main_product_id)) {
                                        $extend_description = in_array($main_product_id, array('159290', '156279', '158728')) ? '<div class="specialnote" style="padding-top:2px"><a href="http://r3k.gamernizer.com/game/server" target="_blank">' . TEXT_ORDER_CHANGE_DEFAULT_CHARACTER . '</a></div>' : '';
                                        $character_list_array = array();
                                        $character_list_array[] = array(
                                            'id' => '',
                                            'text' => PULL_DOWN_DEFAULT
                                        );
                                        $character_list_array = get_character_list($character_list_array, $main_product_id, $prefill_dtu_info_array);
                                        $css_class = count($character_list_array) > 1 ? ' character_list_loaded' : '';
                                        
                                        $display_delivery_mode_label_array[] = $dtu_field_title . tep_draw_pull_down_menu("game_info[".$game_input_key_loop."]", $character_list_array, $default_info, ' id="dtu_character_sel" default_text="' . PULL_DOWN_DEFAULT . '" onfocus="load_character_list(this, \''.$pID.'\', \''.$dm_id.'\');jQuery(this).removeClass(\'productListingDTUPreInput\').addClass(\'productListingDTUAftInput\')" onblur="if(this.value==\'\') {jQuery(this).removeClass(\'productListingDTUAftInput\').addClass(\'productListingDTUPreInput\')}else{jQuery(this).removeClass(\'productListingDTUPreInput\').addClass(\'productListingDTUAftInput\')}" class="productListingDTUPreInput' . $css_class . '" ') . $extend_description;
                                    } else {
                                        $display_delivery_mode_label_array[] = $dtu_field_title . tep_draw_input_field("game_info[".$game_input_key_loop."]", $default_info, ' maxlength="64" size="20" onfocus="this.value=\'\';jQuery(this).removeClass(\'productListingDTUPreInput\').addClass(\'productListingDTUAftInput\')" onblur="if(this.value==\'\') {this.value=jQuery(this).attr(\'default_text\');jQuery(this).removeClass(\'productListingDTUAftInput\').addClass(\'productListingDTUPreInput\')}else{jQuery(this).removeClass(\'productListingDTUPreInput\').addClass(\'productListingDTUAftInput\')}" default_text="" class="productListingDTUPreInput dtu_customer_input_game_info" ' );
                                    }
                                    break;
                                default: // account
                                    $display_delivery_mode_label_array[] = $dtu_field_title . tep_draw_input_field("game_info[".$game_input_key_loop."]", $default_info, ' maxlength="64" size="18" onfocus="this.value=\'\';jQuery(this).removeClass(\'productListingDTUPreInput\').addClass(\'productListingDTUAftInput\')" onblur="if(this.value==\'\') {this.value=jQuery(this).attr(\'default_text\');jQuery(this).removeClass(\'productListingDTUAftInput\').addClass(\'productListingDTUPreInput\')}else{jQuery(this).removeClass(\'productListingDTUPreInput\').addClass(\'productListingDTUAftInput\')}" default_text="" class="productListingDTUPreInput dtu_customer_input_game_info" ' );
                                    if ($direct_topup_obj->retype_account($main_product_id)) {
                                        $default_info = ($default_info == $game_input_data_loop['top_up_info_display']) ? TEXT_RETYPE . $default_info : $default_info;
                                        $display_delivery_mode_label_array[] = TEXT_RETYPE . $dtu_field_title . tep_draw_input_field("game_info[".$game_input_key_loop."_2]", $default_info, ' maxlength="64" size="18" onfocus="this.value=\'\';jQuery(this).removeClass(\'productListingDTUPreInput\').addClass(\'productListingDTUAftInput\')" onblur="if(this.value==\'\') {this.value=jQuery(this).attr(\'default_text\');jQuery(this).removeClass(\'productListingDTUAftInput\').addClass(\'productListingDTUPreInput\')}else{jQuery(this).removeClass(\'productListingDTUPreInput\').addClass(\'productListingDTUAftInput\')}" default_text="'.TEXT_RETYPE.$game_input_data_loop['top_up_info_display'].'" class="productListingDTUPreInput dtu_customer_input_game_info" ' );
                                    }
                                    break;
                            }
                        }
                    }
                    
                    break;
                default:
                    $display_delivery_mode_label_array[] = $product_delivery_mode_data_loop;
                    break;
            }

            $delivery_methods_array[$dm_id] = '<table width="100%">
                                                    <tr>
                                                        <td>
                                                            <div onclick="jQuery(\'#dm_' . $dm_id . '\').prop(\'checked\', true);">
                                                                <div style="padding-left:3px;">
                                                                    <input id="dm_' . $dm_id . '" type="radio" name="rd_delivery_mode" value="'.$dm_id.'" ' . ($default_dm==$dm_id?'checked':'') . '>' .
                                                                    implode('</div><div style="padding-left:25px;padding-top:10px;">', $display_delivery_mode_label_array) .
                                                                '</div>' .
                                                            '</div>
                                                        </td>
                                                    </tr>
                                                </table>
                                                <script>
                                                    if(jQuery("#dtu_character_sel").length>0){
                                                        jQuery("#dm_form .dtu_customer_input_game_info").unbind("change");
                                                        jQuery("#dm_form .dtu_customer_input_game_info").change(function(){
                                                            if (jQuery("#dtu_character_sel").hasClass("character_list_loaded")){
                                                                jQuery("#dtu_character_sel").removeClass("character_list_loaded");
                                                                jQuery("#dtu_character_sel>option:gt(0)").remove();
                                                            }
                                                        });
                                                    }
                                                </script>';
        }
    } else { //if ($product_info_row['products_bundle'] != 'yes' && $product_info_row['products_bundle_dynamic'] != 'yes') {
        $delivery_methods_array[5] = TEXT_INFO_DELIVERY_SEND_TO_ACCOUNT;
//        $delivery_methods_array[5]['title'] = TEXT_INFO_DELIVERY_SEND_TO_ACCOUNT;
    }
    
    return array('html' => implode("", $delivery_methods_array), 'tooltips' => $delivery_methods_tooltips, 'note' => implode('', $delivery_methods_note_array));
}

function get_currency_delivery_section_content ($pID, $delivery_mode, $category_id) {
    $default_dm = '';
    $delivery_methods_array = array();
    $delivery_methods_note_array = array();
    $delivery_methods_tooltips_array = array('<div><span class="hds3">'. ENTRY_DELIVERY_METHOD . ': </span></div>');
    $cat_setting_array = tep_get_cat_setting($category_id, 'catalog', 'cs_delivery_option');
    $cat_setting_array = isset($cat_setting_array['cs_delivery_option']) ? explode(',', $cat_setting_array['cs_delivery_option']) : array();
    
    if (count($cat_setting_array)) {
        foreach ($cat_setting_array as $dm) {
            $default_dm = $default_dm=='' ? $dm : $default_dm;
            $enabled = false;
            
            switch ($dm) {
                case 1:
                    $enabled = $default_dm==1;
                    $disabled = $enabled ? '' : ' disabled="disabled"';
                    $delivery_methods_array[] = '<table width="100%">
                                                    <tr>
                                                        <td>
                                                            <div class="group '.($enabled?'gshow':'').'">
                                                                <div style="padding-left:3px;">' . 
                                                                    tep_draw_radio_field("extra_info[delivery_mode]", '1', $enabled, 'id="dm_1" onclick="pfv_show_dm_info(this)" style="background:#ffffff; color:#000000;"') . '
                                                                    <label for="dm_1">' . OPTION_FACE_TO_FACE . '</label>
                                                                </div>
                                                                <div class="entry">' .
                                                                    tep_draw_input_field("extra_info[char_name]", ENTRY_CHARACTER_NAME, 'size="20" onfocus="this.value=\'\';" onblur="if(this.value==\'\') {this.value=jQuery(this).attr(\'default_text\');}else{}" default_text="'.ENTRY_CHARACTER_NAME.'" class="productListingDTUPreInput"' . $disabled) . '
                                                                </div>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                 </table>';
                    $delivery_methods_note_array[] = '<div id="note_dm_1" class="'.($enabled?'noteshow':'').'">'.TEXT_ORDER_FACE_TO_FACE_NOTE.'</div>';
                    break;
                case 2:
                    $enabled = $default_dm==2;
                    $disabled = $enabled ? '' : ' disabled="disabled"';
                    $delivery_methods_array[] = '<table width="100%">
                                                    <tr>
                                                        <td>
                                                            <div class="group '.($enabled?'gshow':'').'">
                                                                <div style="padding-left:3px;">' . 
                                                                    tep_draw_radio_field("extra_info[delivery_mode]", '2', $enabled, 'id="dm_2" onclick="pfv_show_dm_info(this)" style="background:#ffffff; color:#000000;"') . '
                                                                    <label for="dm_2">' . OPTION_PUT_IN_MY_ACCOUNT . '</label>
                                                                </div>
                                                                <div class="entry">' .
                                                                    tep_draw_input_field("extra_info[char_name]", ENTRY_CHARACTER_NAME, 'size="20" onfocus="this.value=\'\';" onblur="if(this.value==\'\') {this.value=jQuery(this).attr(\'default_text\');}else{}" default_text="'.ENTRY_CHARACTER_NAME.'" class="productListingDTUPreInput"' . $disabled) . '
                                                                </div>
                                                                <div class="entry">' .
                                                                    tep_draw_input_field('extra_info[char_account_name]', ENTRY_ACCOUNT_NAME, 'size="20" onfocus="this.value=\'\';" onblur="if(this.value==\'\') {this.value=jQuery(this).attr(\'default_text\');}else{}" default_text="'.ENTRY_ACCOUNT_NAME.'" class="productListingDTUPreInput"' . $disabled) . '
                                                                </div>
                                                                <div class="entry">' .
                                                                    ENTRY_ORDER_GUYA_WOW_ACCOUNT_INFO_TEXT . '
                                                                </div>
                                                                <div class="entry">' .
                                                                    tep_draw_input_field('extra_info[char_account_pwd]', LOGIN_PASSWORD, 'size="20" onfocus="this.value=\'\';" onblur="if(this.value==\'\') {this.value=jQuery(this).attr(\'default_text\');}else{}" default_text="'.LOGIN_PASSWORD.'" class="productListingDTUPreInput"' . $disabled) . '
                                                                </div>
                                                                <div class="entry">' .
                                                                    tep_draw_input_field('extra_info[char_wow_account]', ENTRY_WOW_ACCOUNT_NAME, 'size="20" onfocus="this.value=\'\';" onblur="if(this.value==\'\') {this.value=jQuery(this).attr(\'default_text\');}else{}" default_text="'.ENTRY_WOW_ACCOUNT_NAME.'" class="productListingDTUPreInput"' . $disabled) . '
                                                                </div>
                                                                <div class="entry">' .
                                                                    ENTRY_ORDER_GUYA_WOW_ACCOUNT_INFO2_TEXT . '
                                                                </div>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                 </table>';
                    $delivery_methods_note_array[] = '<div id="note_dm_2" class="'.($enabled?'noteshow':'').'">'.TEXT_ORDER_GUYA_NOTE.'</div>';
                    break;
                case 3:
                    $enabled = $default_dm==3;
                    $disabled = $enabled ? '' : ' disabled="disabled"';
                    $delivery_methods_array[] = '<table width="100%">
                                                    <tr>
                                                        <td>
                                                            <div class="group '.($enabled?'gshow':'').'">
                                                                <div style="padding-left:3px;">' . 
                                                                    tep_draw_radio_field("extra_info[delivery_mode]", '3', $enabled, 'id="dm_3" onClick="pfv_show_dm_info(this)" style="background:#ffffff; color:#000000;"') . '
                                                                    <label for="dm_3">' . OPTION_BY_MAIL . '</label>
                                                                </div>
                                                                <div class="entry">' .
                                                                    tep_draw_input_field("extra_info[char_name]", ENTRY_CHARACTER_NAME, 'size="20" onfocus="this.value=\'\';" onblur="if(this.value==\'\') {this.value=jQuery(this).attr(\'default_text\');}else{}" default_text="'.ENTRY_CHARACTER_NAME.'" class="productListingDTUPreInput"' . $disabled) . '
                                                                </div>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                 </table>';
                    $delivery_methods_note_array[] = '<div id="note_dm_3" class="'.($enabled?'noteshow':'').'">'.TEXT_ORDER_MAIL_NOTE.'</div>';
                    break;
                case 4:
                    $enabled = $default_dm==4;
                    $disabled = $enabled ? '' : ' disabled="disabled"';
                    $delivery_methods_array[] = '<table width="100%">
                                                    <tr>
                                                        <td>
                                                            <div class="group '.($enabled?'gshow':'').'">
                                                                <div style="padding-left:3px;">' . 
                                                                    tep_draw_radio_field("extra_info[delivery_mode]", '4', $enabled, 'id="dm_4" onClick="pfv_show_dm_info(this)" style="background:#ffffff; color:#000000;"') . '
                                                                    <label for="dm_4">' . OPTION_OPEN_STORE . '</label>
                                                                </div>
                                                                <div class="entry">' .
                                                                    tep_draw_input_field("extra_info[char_name]", ENTRY_CHARACTER_NAME, 'size="20" onfocus="this.value=\'\';" onblur="if(this.value==\'\') {this.value=jQuery(this).attr(\'default_text\');}else{}" default_text="'.ENTRY_CHARACTER_NAME.'" class="productListingDTUPreInput"' . $disabled) . '
                                                                </div>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                 </table>';
                    $delivery_methods_note_array[] = '<div id="note_dm_4" class="'.($enabled?'noteshow':'').'">'.TEXT_ORDER_OPEN_STORE_NOTE.'</div>';
            }
        }   
    }
    
    $html = tep_draw_hidden_field('err_char_name', ERROR_PLS_ENTER_CHAR_NAME) .
            tep_draw_hidden_field('err_char_account_name', ERROR_PLS_ENTER_ACCOUNT_NAME) . 
            tep_draw_hidden_field('err_char_account_pwd', ERROR_PLS_ENTER_PASSWORD);
    
    return array('html' => $html . implode("", $delivery_methods_array), 'tooltips' => implode('', $delivery_methods_tooltips_array), 'note' => implode('', $delivery_methods_note_array));
}

function get_general_delivery_section_content ($pID, $important_msg) {
    return array('html' => '', 'tooltips' => '<div><span class="hds3">'. BOX_HEADING_INFORMATION . ': </span></div>', 'note' => $important_msg);
}

function get_confirm_password_box ($index) {
    ob_start();
?>
    <div style="border-bottom: 1px dotted #CCCCCC;padding: 10px 15px;">
<?
    echo    tep_draw_form('cfm_password', '', 'post', ' id="cfm_password_form" onsubmit="jQuery(\'#cfm_password\').click();return false;"') .
            tep_draw_hidden_field('index', $index);
?>
        <table border="0" cellspacing="0" cellpadding="1" height="100%" width="100%">
            <tr>
                <td colspan="3" style="padding:0px;">
                    <div style="background-color: #FFFDDB">
                        <div class="error_msg" style="line-height: 25px;padding-left:5pt;"></div>
                    </div>
                </td>
            </tr>
            <tr><td colspan="3"><?=CONFIRM_LOGIN_PASSWORD_DESC?></td></tr>
            <tr><td>&nbsp;</td></tr>
            <tr>
                <td style="width: 20%;padding:5px 0px;" nowrap><?=LOGIN_PASSWORD?></td>
                <td width="3" align="center">:</td>
                <td><div class="ihd1"><?=tep_draw_input_field('cfm_password', '', '  onfocus="this.value=\'\'" size="50" style="width:250px;"', 'password')?></div></td>
            </tr>
        </table>
        </form>
    </div>
    <div style="text-align: center; padding-top: 10px;"><?=tep_image_button2('gray_short',"javascript:void(0);", BUTTON_CONFIRM, 80,' id="cfm_password" onclick="pfv_cfm_password(this.id)"')?></div>
<?
    $cfm_pw_content = ob_get_contents();
    ob_end_clean();
    
    return $cfm_pw_content;
}

function get_qna_box ($index = '') {
    global $customers_security_obj;
    ob_start();
    
    echo    tep_draw_form('ask_qna', '', 'post', ' id="ask_qna_form" onsubmit="cfm_qna();return false;"') . 
            tep_draw_hidden_field('index', $index);
?>
    <table id="qna_table" width="100%" cellspacing="0" cellpadding="0" style="display: block;padding: 10px 20px 0;">
    <tr>
        <td class="smallText" colspan="2" style="padding-bottom: 10px;"><?=TEXT_DORMANT_ACCOUNT_QNA_REQUEST?><td>
    </tr>
    <tr>
        <td colspan="2"><?=$customers_security_obj->show_customer_security_question_html()?></td>
    </tr>
    <tr><td></td><td class="smallText"><div class="error_msg"><font color=red></font></div><td></tr>
    <tr>
        <td colspan="2" align="center">
            <table border="0" cellspacing="0" cellpadding="10" align="center">
                <tr>
                    <td nowrap><?=tep_image_button2("gray_tall", "javascript: void(0)", BUTTON_CONFIRM,"72","onclick='cfm_qna();'");?></td>
                </tr>
            </table>
        </td>
    </tr>
    <tr><td colspan="2"><div style=""><?=TEXT_FORGOT_QNA?></div></tr>
    </table>
    </form>
<?
    $qna_content = ob_get_contents();
    ob_end_clean();
    
    return $qna_content;
}

function get_confirm_store_credit_box ($index, $customer_id, $currency) {
    global $currencies, $cPath, $shasso_obj;
    
    $sc_currency_code = '';
    $sc_convert_in_my_account = (isset($_SESSION['RegionGST']['tax_title']) && tep_not_null($_SESSION['RegionGST']['tax_title']) ? 1 : 0);
    $sc_array = store_credit::get_current_credits_balance($customer_id);

    if (count($sc_array) > 0) {
        $sc_currency_code = $currencies->get_code_by_id($sc_array['sc_currency_id']);
    }

    ob_start();
?>
    <table id="sc_table" border="0" cellspacing="0" cellpadding="0" height="100%" width="100%">
<?
    if ($sc_convert_in_my_account == 1) {
?>
    <tr>
        <td><label for="act_login"><?=TEXT_STORE_CREDIT_CONVERT_IN_MY_ACCOUNT?></label></td>
    </tr>
<?
    } else {
?>
    <tr>
        <td class="scbox dottedbox">
            <p><?=TEXT_STORE_CREDIT_POPUP_DESCRIPTION?></p>
            <div>
                <div class="sc_btn"><?=tep_image_button2("green", "javascript:set_localization_value('currency' , '" . $sc_currency_code . "' , '');", sprintf(IMAGE_BUTTON_PAY_WITH_SC_CURRENCY, $sc_currency_code), '200px')?></div>
                <div class="desc"><?=sprintf(TEXT_STORE_CREDIT_POPUP_CHANGE_WEBSITE_CURRENCY, $sc_currency_code)?></div>
            </div>
            <div class="extend">
                <div class="sc_btn"><?=tep_image_button2("green", "javascript:void(0);", sprintf(IMAGE_BUTTON_PAY_WITH_WEBSITE_CURRENCY, $currency), '200px', ' onclick="attach_cpage(\'' . $shasso_obj->getPageURL('/storeCredit/index') . '\')"')?></div>
                <div class="desc"><?=sprintf(TEXT_STORE_CREDIT_POPUP_CHANGE_STORE_CREDIT_CURRENCY, $currency)?></div>
            </div>
            <div class="extend">
                <div class="sc_btn"><?=tep_image_button2("green", "javascript:void(0);", IMAGE_BUTTON_PAY_WITHOUT_SC, '200px', ' onclick="pfv(\'' . $index . '\', \'10\')"')?></div>
                <div class="desc"><?=TEXT_STORE_CREDIT_POPUP_CONTINUE_WITH_NO_CHANGE?></div>
            </div>
        </td>
    </tr>
<?
    }
?>
</table>
<?
    $cfm_sc_content = ob_get_contents();
    ob_end_clean();
    
    return $cfm_sc_content;
}

function get_product_info ($product_id, $customers_groups_id, $languages_id) {
    $info_display = '';
    $show = '';
    $products_id = '';
    $categories_id = '';
    $product_name = '';
    $custom_product = '';
    $products_bundle = '';
    $products_quantity = '';
        
    $product_info_select_sql = "	SELECT DISTINCT(p.products_id), p.products_bundle, p.products_bundle_dynamic, p.custom_products_type_id, p.products_price, p.products_tax_class_id, c.categories_id 
                                    FROM " . TABLE_PRODUCTS . " AS p 
                                    INNER JOIN " . TABLE_PRODUCTS_TO_CATEGORIES . " AS ptc 
                                        ON (p.products_id = ptc.products_id) 
                                    INNER JOIN " . TABLE_CATEGORIES . " AS c 
                                        ON (c.categories_id = ptc.categories_id AND c.categories_status = 1) 
                                    INNER JOIN " . TABLE_CATEGORIES_GROUPS . " AS cg 
                                        ON (cg.categories_id = c.categories_id) 
                                    WHERE p.custom_products_type_id = 0 
                                        AND p.products_status = 1 
                                        AND p.products_id = '" . tep_db_input($product_id) . "' 
                                        AND ((cg.groups_id = '".$customers_groups_id. "') OR (cg.groups_id = 0))";
    $product_info_result_sql = tep_db_query($product_info_select_sql);
    if ($product_info_row = tep_db_fetch_array($product_info_result_sql)) {
        if ($product_info_row['products_bundle'] == 'yes') {
            $status_info = tep_product_add_to_cart_permission($product_info_row['products_id'], 'products_bundle');
        } else if ($product_info_row['products_bundle_dynamic'] == 'yes') {
            $status_info = tep_product_add_to_cart_permission($product_info_row['products_id'], 'products_bundle_dynamic');
        } else {
            $status_info = tep_product_add_to_cart_permission($product_info_row['products_id'], '');
        }

        $product_name = tep_get_products_name($product_info_row['products_id'], $languages_id);

        $parent_categories_array = array();
        $parent_categories_array[] = $product_info_row['categories_id'];
        tep_get_parent_categories($parent_categories_array, $product_info_row['categories_id']);

        foreach ($parent_categories_array as $key => $categories_id) {
            $categories_name_select_sql = "	SELECT cd.categories_name 
                                            FROM " . TABLE_CATEGORIES . " AS c 
                                            INNER JOIN " . TABLE_CATEGORIES_DESCRIPTION . " AS cd 
                                                ON (c.categories_id = cd.categories_id AND cd.language_id = '" . (int)$languages_id . "') 
                                            WHERE c.categories_id = '" . (int)$categories_id . "'  
                                                AND c.custom_products_type_id = '999' 
                                                AND c.categories_status = 1";
            $categories_name_result_sql = tep_db_query($categories_name_select_sql);
            if ($categories_name_row = tep_db_fetch_array($categories_name_result_sql)) {
                if (tep_not_null($info_display)) {
                    $info_display = $categories_name_row['categories_name'] . ' - ' . $info_display;
                } else {
                    $info_display = $categories_name_row['categories_name'];
                }
            }
        }
        
        $show = $status_info['show'];
        $products_id = $product_info_row['products_id'];
        $categories_id = $product_info_row['categories_id'];
        $custom_product = $product_info_row['custom_products_type_id'];
        $products_bundle = $product_info_row['products_bundle'];
        $products_quantity = $product_info_row['products_quantity'];
    }
    
    return array(
        'products_status' => $show,
        'products_id' => $products_id,
        'categories_id' => $categories_id,
        'products_name' => $product_name,
        'custom_product' => $custom_product,
        'products_bundle' => $products_bundle,
        'products_quantity' => $products_quantity,
        'products_cat' => $info_display
    );
}

function get_hla_product_info ($hla_account_id, $hla_game_id) {
    global $languages_id, $default_languages_id;
    $default_hla_language_id = '1';
    $categories_name = '';
    $products_description = '';
    $hla_acc_id = '';
    $product_name = '';
    
    if (tep_not_empty($hla_account_id)) {
        $product_info_select_sql = "SELECT hla.products_id, cha.products_hla_id, des.products_hla_characters_name 
                                    FROM " . TABLE_PRODUCTS_HLA . " AS hla 
                                    INNER JOIN " . TABLE_PRODUCTS_HLA_CHARACTERS . " AS cha 
                                        ON (hla.products_hla_id = cha.products_hla_id) 
                                    LEFT JOIN " . TABLE_PRODUCTS_HLA_DESCRIPTION . " AS des 
                                        ON (cha.products_hla_characters_id = des.products_hla_characters_id) 
                                    WHERE cha.products_hla_id = '".tep_db_input($hla_account_id)."' 
                                        AND des.language_id = '".$default_hla_language_id."'
                                    LIMIT 1";
        $product_info_result_sql = tep_db_query($product_info_select_sql);
        if ($product_info_row = tep_db_fetch_array($product_info_result_sql)) {
            $categories_name = tep_get_categories_name($hla_game_id);
            $products_description = tep_get_products_info($product_info_row['products_id'], 'products_description', $languages_id, $default_languages_id);
            $hla_acc_id = $product_info_row['products_hla_id'];
            $product_name = $product_info_row['products_hla_characters_name'];
        }
    }
    
    return array(
        'products_description' => $products_description,
        'category_name' => $categories_name,
        'hla_acc_id' => $hla_acc_id,
        'products_name' => $product_name
    );
}

function filter_extra_info ($xtra_info_array) {
    $return_array = array();
    
    foreach ($xtra_info_array as $key => $value) {
        $compare_value = '';
        
        switch ($key) {
            case 'char_name':
                $compare_value = ENTRY_CHARACTER_NAME;
                break;
            case 'char_account_name':
                $compare_value = ENTRY_ACCOUNT_NAME;
                break;
            case 'char_account_pwd':
                $compare_value = LOGIN_PASSWORD;
                break;
            case 'char_wow_account':
                $compare_value = ENTRY_WOW_ACCOUNT_NAME;
                break;
            default:
        }
        
        $value = ($compare_value == $value) ? '' : $value;
        $return_array[$key] = $value;
    }
    
    return $return_array;
}

function need_popup_sc_choice($cust_id, $region_currency_code) {
    global $currencies;

    $return_bool = TRUE;

    $sc_obj = new store_credit($cust_id);

    if (isset($sc_obj->credit_accounts['sc_currency_id'])) {
        $sc_currency_code = $currencies->get_code_by_id($sc_obj->credit_accounts['sc_currency_id']);
        
        if ($sc_currency_code === $region_currency_code) {
            return FALSE;
        } else {
            $sc_balance = $sc_obj->credit_accounts['sc_reverse'] + $sc_obj->credit_accounts['sc_irreverse'];

            if ($sc_balance <= 0) {
                return FALSE;
            }
        }
    } else {
        $return_bool = FALSE;   // it's possible user does not have sc account
    }

    return $return_bool;
}

function pfv_next_access (&$user_confirmed) {
    global $currency, $credit_covers;
    $next = '';
    
    // {x}{x} : {0: check sc ,1: skip sc}{1: open confirm box; 2: change qty; 3: confirmed order; 4: proceed checkout}
    if (!is_array($user_confirmed)) {
        if ($user_confirmed > 0) {
            $user_confirmed = array(
                'skip_sc' => floor($user_confirmed/10),
                'skip_cfm_order' => ($user_confirmed%10)
            );
        } else {
            $user_confirmed = array(
                'skip_sc' => 0,
                'skip_cfm_order' => 0
            );
        }
    }
    
    if (!isset($_SESSION['customer_id'])) {
        $next = 'login';
    } else if ($user_confirmed['skip_sc'] === 0 && need_popup_sc_choice($_SESSION['customer_id'], $currency)) {
        $next = 'sc_choice';
    } else {
        $next = 'confirm_order';
        
        if ($user_confirmed['skip_cfm_order'] === 4) {
            if (isset($_SESSION['need_sc_usage_qna'])) {
                $next = 'ask_qna';
            } else if ($credit_covers) {
                if (check_is_lifetime_cookies_existed(-5*60) !== TRUE) {
                    $next = 'ask_password';
                } else {
                    $next = 'go_chkout';
                }
            } else {
                $next = 'go_chkout';
            }
        }
    }
    
    return $next;
}

function get_character_list($character_list_array, $pid, $prefill_dtu_info_array) {
    $account = isset($prefill_dtu_info_array['account']) ? $prefill_dtu_info_array['account'] : '';
    $server = isset($prefill_dtu_info_array['server']) ? $prefill_dtu_info_array['server'] : '';
    
    if (tep_not_empty($account) && tep_not_empty($server)) {
        $publishers_select_sql = "	SELECT pg.publishers_id, pg.publishers_games_id, pg.publishers_game 
                                    FROM " . TABLE_PUBLISHERS_GAMES . " AS pg
                                    INNER JOIN " . TABLE_PUBLISHERS_PRODUCTS . " AS pp
                                        ON pp.publishers_games_id = pg.publishers_games_id
                                    WHERE pp.products_id = '" . $pid . "'";
        $publishers_result_sql = tep_db_query($publishers_select_sql);
        if ($publishers_row = tep_db_fetch_array($publishers_result_sql)) {
            include_once(DIR_WS_CLASSES . 'direct_topup.php');
            $direct_topup_obj = new direct_topup();

            $games_acc_array = array();
            $games_acc_array['account'] = $account;
            $games_acc_array['server'] = $server;
            $games_acc_array['game'] = $publishers_row['publishers_game'];
            $games_acc_array['publishers_games_id'] = $publishers_row['publishers_games_id'];

            $characters_array = $direct_topup_obj->get_character_list($publishers_row['publishers_id'], $games_acc_array, $curl_response_array);

            if (isset($curl_response_array['result_code'])) {
                if ($curl_response_array['result_code'] == '2000') {
                    if (count($characters_array) > 0) {
                        foreach ($characters_array as $characters_id_loop => $characters_data_loop) {
                            $character_list_array[] = array(
                                'id' => $characters_id_loop,
                                'text' => $characters_data_loop
                            );
                        }
                    }
                }
            }
            
            unset($direct_topup_obj);
        }
    }
    
    return $character_list_array;
}
?>