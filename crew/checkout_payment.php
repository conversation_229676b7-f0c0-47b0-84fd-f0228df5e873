<?
/*
	$Id: checkout_payment.php,v 1.37 2015/05/28 10:51:32 darren.ng Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com

  	Copyright (c) 2003 osCommerce

  	Released under the GNU General Public License
*/

require('includes/application_top.php');

$delete_temp_process_sql = "DELETE FROM " . TABLE_TEMP_PROCESS . " 
							WHERE page_name = 'checkout_process.php' 
								AND (created_date < DATE_SUB(now(), INTERVAL 180 SECOND))";
tep_db_query($delete_temp_process_sql);

// if the customer is not logged on, redirect them to the login page
if (!tep_session_is_registered('customer_id')) {
	$navigation->set_snapshot();
    tep_redirect(tep_href_link(FILENAME_LOGIN, '', 'SSL'));
}

// if there is nothing in the customers cart, redirect them to the shopping cart page
if ($cart->count_contents() < 1) {
	tep_redirect(tep_href_link(FILENAME_SHOPPING_CART));
}

// if no shipping method has been selected, redirect the customer to the shipping method selection page
if (!tep_session_is_registered('shipping')) {
    tep_redirect(tep_href_link(FILENAME_CHECKOUT_SHIPPING, '', 'SSL'));
}

// avoid hack attempts during the checkout procedure by checking the internal cartID
if (isset($cart->cartID) && tep_session_is_registered('cartID')) {
	if ($cart->cartID != $cartID) {
    	tep_redirect(tep_href_link(FILENAME_CHECKOUT_SHIPPING, '', 'SSL'));
    }
}

/*if (!tep_session_is_registered('comments')) tep_session_register('comments');
if (!tep_session_is_registered('custom_comments')) tep_session_register('custom_comments');

require(DIR_WS_CLASSES . 'cart_comments.php');
$cart_comments_obj = new cart_comments('');*/

/*
$active_comments = $cart_comments_obj->count_active_comments(1);

if ($_REQUEST['action'] == 'info') {
	$cart_comment_proceed = $cart_comments_obj->construct_comments(1);
	$custom_comments = $cart_comments_obj->comments;
	$comments = $cart_comments_obj->get_plain_comments($custom_comments, 1);

	if (!$cart_comment_proceed) {
		tep_redirect(tep_href_link(FILENAME_CHECKOUT_INFO, 'error_message=' . urlencode(ERROR_CART_COMMENTS_REQUIRED), 'SSL', true, false));
	}
}
*/
// if we have been here before and are coming back get rid of the credit covers variable
if(tep_session_is_registered('credit_covers')) tep_session_unregister('credit_covers');  //ICW ADDED FOR CREDIT CLASS SYSTEM

// Stock Check
if ( (STOCK_CHECK == 'true') && (STOCK_ALLOW_CHECKOUT != 'true') ) {
	$products = $cart->get_products();

    for ($i=0, $n=sizeof($products); $i<$n; $i++) {
      	$prod_query = tep_db_query("select products_bundle, products_bundle_dynamic from " . TABLE_PRODUCTS . " where products_id = '" . (int)$products[$i]['id'] . "'");
      	$prod_type = tep_db_fetch_array($prod_query);
      	if($prod_type['products_bundle']=='yes' || $prod_type['products_bundle_dynamic']=='yes'){
	    	//no update
      	} else {
      		if (isset($products[$i]['custom_content']['delivery_mode']) && $products[$i]['custom_content']['delivery_mode'] == '6') {
      			;
      		} else {
	      		$product_instance_id = tep_not_null($products[$i]['custom_content']['hla_account_id']) ? $products[$i]['custom_content']['hla_account_id'] : '';
	      		$stock_status = tep_check_stock_status($products[$i]['id'], $products[$i]['quantity'], $product_instance_id);
	      		
	        	if (tep_not_null($stock_status) && $stock_status != "pre-order") {
		        	tep_redirect(tep_href_link(FILENAME_SHOPPING_CART));
		        	break;
		      	}
		    }
      	}
	}
}

// if no billing destination address was selected, use the customers own address as default
if (!tep_session_is_registered('billto')) {
	tep_session_register('billto');
    $billto = $_SESSION['customer_default_address_id'];
} else {
	// verify the selected billing address
    $check_address_query = tep_db_query("select count(*) as total from " . TABLE_ADDRESS_BOOK . " where customers_id = '" . (int)$customer_id . "' and address_book_id = '" . (int)$billto . "'");
    $check_address = tep_db_fetch_array($check_address_query);

    if ($check_address['total'] != '1') {
    	$billto = $_SESSION['customer_default_address_id'];
      	if (tep_session_is_registered('payment')) tep_session_unregister('payment');
	}
}

// a session array storing firstname and lastname for 2CO credit card payment
if (!tep_session_is_registered('pm_2CO_cc_owner')) tep_session_register('pm_2CO_cc_owner');
if (!tep_session_is_registered('credit_card_owner')) tep_session_register('credit_card_owner');
if (!tep_session_is_registered('payment_inputs_array')) tep_session_register('payment_inputs_array');

$_SESSION['cot_gv'] = 'on';

require(DIR_WS_CLASSES . 'order.php');
$order = new order;

if ($order->info['subtotal'] > 0) {
	;	// These ordered products has price value.
} else {
	$payment = '';
	/*	Allow 0.00 Checkout (we have some free gift keys)
	$messageStack->add_session('add_to_cart', WARNING_ORDER_AMOUNT_ZERO, 'error');
	tep_redirect(tep_href_link(FILENAME_SHOPPING_CART));
	break;
	*/
}

require(DIR_WS_CLASSES . 'order_total.php');//ICW ADDED FOR CREDIT CLASS SYSTEM
$order_total_modules = new order_total;//ICW ADDED FOR CREDIT CLASS SYSTEM
$order_total_modules->pre_confirmation_check();	// Need it here to decide show/hide payment method options

/*-- GST :: check status --*/
localization::verify_gst_condition();

if (tep_not_null($_SESSION['RegionGST']) && (($_SESSION['RegionGST']['loc_country'] != $country) || ($_SESSION['RegionGST']['currency'] != $currency))) {
	$country = $_SESSION['RegionGST']['loc_country'];
	$currency = $_SESSION['RegionGST']['currency'];
	tep_setcookie('RegionalSet[loc_country]', $country, $cookies_expired, $cookie_path, $cookie_domain);
	tep_setcookie('RegionalSet[currency]', $currency, $cookies_expired, $cookie_path, $cookie_domain);
}

$total_weight = $cart->show_weight();
$total_count = $cart->count_contents();
$total_count = $cart->count_contents_virtual(); //ICW ADDED FOR CREDIT CLASS SYSTEM

if (isset($HTTP_GET_VARS['payment'])) {
	if (!tep_session_is_registered('payment')) tep_session_register('payment');
	$payment = $HTTP_GET_VARS['payment'];
}

// load all enabled payment modules
require(DIR_WS_CLASSES . 'payment.php');
$payment_modules = new payment;

if (($order->info['subtotal'] == 0 || $payment_modules->check_credit_covers()) && isset($_GET['cfm_proceed']) && (int)$_GET['cfm_proceed'] === 1) {
    tep_redirect(tep_href_link(FILENAME_CHECKOUT_PROCESS, '', 'SSL'));
}

require(DIR_WS_LANGUAGES . $language . '/' . FILENAME_CHECKOUT_PAYMENT);

if ($shipping != false)	$breadcrumb->add(NAVBAR_TITLE_1, tep_href_link(FILENAME_CHECKOUT_SHIPPING, '', 'SSL'));
$breadcrumb->add(NAVBAR_TITLE_3, tep_href_link(FILENAME_CHECKOUT_PAYMENT, '', 'SSL'));

$content = CONTENT_CHECKOUT_PAYMENT;
$javascript = $content . '.js.php';

require(DIR_WS_TEMPLATES . TEMPLATENAME_MAIN_PAGE);
require(DIR_WS_INCLUDES . 'application_bottom.php');
?>