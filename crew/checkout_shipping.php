<?
/*
  	$Id: checkout_shipping.php,v 1.20 2012/07/13 05:32:49 weesiong Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2003 osCommerce
	
  	Released under the GNU General Public License
*/

require('includes/application_top.php');
require('includes/classes/http_client.php');

// BOF: Individual Shipping 
if (tep_get_configuration_key_value('MODULE_SHIPPING_INDVSHIP_STATUS') and $shiptotal) {
	tep_session_unregister('shipping');
}
// EOF: Individual Shipping 

// if the customer is not logged on, redirect them to the login page
if (!tep_session_is_registered('customer_id')) {
    $navigation->set_snapshot();
    
    if ($cart->count_contents() < 1) {
		tep_redirect(tep_href_link(FILENAME_SHOPPING_CART));
	} else {
		tep_redirect(tep_href_link(FILENAME_LOGIN, 'trace=chkout', 'SSL'));
	}
}

// if there is nothing in the customers cart, redirect them to the shopping cart page
if ($cart->count_contents() < 1) {
	tep_redirect(tep_href_link(FILENAME_SHOPPING_CART));
}

// if no shipping destination address was selected, use the customers own address as default
if (!tep_session_is_registered('sendto')) {
	tep_session_register('sendto');
    $sendto = $_SESSION['customer_default_address_id'];
} else {
	// verify the selected shipping address
    $check_address_query = tep_db_query("select count(*) as total from " . TABLE_ADDRESS_BOOK . " where customers_id = '" . (int)$customer_id . "' and address_book_id = '" . (int)$sendto . "'");
    $check_address = tep_db_fetch_array($check_address_query);
	
    if ($check_address['total'] != '1') {
    	$sendto = $_SESSION['customer_default_address_id'];
      	if (tep_session_is_registered('shipping')) tep_session_unregister('shipping');
    }
}

require(DIR_WS_CLASSES . 'order.php');
$order = new order;

require(DIR_WS_CLASSES . 'cart_comments.php');
$cart_comments_obj = new cart_comments('');

// register a random ID in the session to check throughout the checkout procedure
// against alterations in the shopping cart contents
if (!tep_session_is_registered('cartID')) tep_session_register('cartID');
$cartID = $cart->cartID;

// if the quantity of any products in cart exceed the current stock amount, redirect customer 
// back to view cart page to modify the quantity
if (!$cart->checkout_permission()) {
	$_SESSION['cart_update_error'] = TEXT_STOCK_NOT_AVAILABLE;
	tep_redirect(tep_href_link(FILENAME_SHOPPING_CART));
}

// if the order contains only virtual products, forward the customer to the billing page as
// a shipping address is not needed
if (($order->content_type == 'virtual') || ($order->content_type == 'virtual_weight')) {
	if (!tep_session_is_registered('shipping')) tep_session_register('shipping');
	$shipping = false;
    $sendto = false;
    $parameters = isset($_GET['cfm_proceed']) ? 'cfm_proceed='.(int)$_GET['cfm_proceed'] : '';
   	/*****************************************************************
   		Commented out to show Order Info page instead of Billing page
   	*****************************************************************/
    tep_redirect(tep_href_link(FILENAME_CHECKOUT_PAYMENT, $parameters, 'SSL'));
}

$total_weight = $cart->show_weight();
$total_count = $cart->count_contents();

// load all enabled shipping modules
require(DIR_WS_CLASSES . 'shipping.php');
$shipping_modules = new shipping;

if ( defined('MODULE_ORDER_TOTAL_SHIPPING_FREE_SHIPPING') && (MODULE_ORDER_TOTAL_SHIPPING_FREE_SHIPPING == 'true') ) {
	$pass = false;
	
    switch (MODULE_ORDER_TOTAL_SHIPPING_DESTINATION) {
    	case 'national':
        	if ($order->delivery['country_id'] == STORE_COUNTRY) {
          		$pass = true;
        	}
        	break;
      	case 'international':
        	if ($order->delivery['country_id'] != STORE_COUNTRY) {
          		$pass = true;
        	}
        	break;
      	case 'both':
        	$pass = true;
        	break;
	}
	
    $free_shipping = false;
    if ( ($pass == true) && ($order->info['total'] >= MODULE_ORDER_TOTAL_SHIPPING_FREE_SHIPPING_OVER) ) {
    	$free_shipping = true;
      	include(DIR_WS_LANGUAGES . $language . '/modules/order_total/ot_shipping.php');
    }
} else {
	$free_shipping = false;
}

// BOF: WebMakers.com Added: Downloads Controller - Free Shipping
// Reset $shipping if free shipping is on and weight is not 0
if (tep_get_configuration_key_value('MODULE_SHIPPING_FREESHIPPER_STATUS') and $cart->show_weight()!=0 && $free_shipping) {
	tep_session_unregister('shipping');
}
// EOF: WebMakers.com Added: Downloads Controller - Free Shipping

// process the selected shipping method
if ( isset($HTTP_POST_VARS['action']) && ($HTTP_POST_VARS['action'] == 'process') ) {
	if (!tep_session_is_registered('comments')) tep_session_register('comments');
	if (!tep_session_is_registered('custom_comments')) tep_session_register('custom_comments');
	
	$cart_comment_proceed = $cart_comments_obj->construct_comments(1);
	$custom_comments = $cart_comments_obj->comments;
	$comments = $cart_comments_obj->get_plain_comments($custom_comments, 1);
	
	if (!$cart_comment_proceed) {
		tep_redirect(tep_href_link(FILENAME_CHECKOUT_SHIPPING, 'error_message=' . urlencode(ERROR_CART_COMMENTS_REQUIRED), 'SSL', true, false));
	}
	/*
    if (tep_not_null($HTTP_POST_VARS['comments'])) {
    	$comments = tep_db_prepare_input($HTTP_POST_VARS['comments']);
    }
	*/
    if (!tep_session_is_registered('shipping')) tep_session_register('shipping');
	
    if ( (tep_count_shipping_modules() > 0) || ($free_shipping == true) ) {
    	if ( (isset($HTTP_POST_VARS['shipping'])) && (strpos($HTTP_POST_VARS['shipping'], '_')) ) {
        	$shipping = $HTTP_POST_VARS['shipping'];
			
        	list($module, $method) = explode('_', $shipping);
        	if ( is_object($$module) || ($shipping == 'free_free') ) {
          		if ($shipping == 'free_free') {
            		$quote[0]['methods'][0]['title'] = FREE_SHIPPING_TITLE;
            		$quote[0]['methods'][0]['cost'] = '0';
          		} else {
            		$quote = $shipping_modules->quote($method, $module);
          		}
          		if (isset($quote['error'])) {
            		tep_session_unregister('shipping');
          		} else {
            		if ( (isset($quote[0]['methods'][0]['title'])) && (isset($quote[0]['methods'][0]['cost'])) ) {
              			$shipping = array(	'id' => $shipping,
                                			'title' => (($free_shipping == true) ?  $quote[0]['methods'][0]['title'] : $quote[0]['module'] . ' : ' . $quote[0]['methods'][0]['title'] ),
                                			'cost' => $quote[0]['methods'][0]['cost']);
		              	tep_redirect(tep_href_link(FILENAME_CHECKOUT_PAYMENT, '', 'SSL'));
            		}
          		}
        	} else {
          		tep_session_unregister('shipping');
        	}
      	}
	} else {
    	$shipping = false;
      	tep_redirect(tep_href_link(FILENAME_CHECKOUT_PAYMENT, '', 'SSL'));
    }    
}

// get all available shipping quotes
$quotes = $shipping_modules->quote();

// if no shipping method has been selected, automatically select the cheapest method.
// if the modules status was changed when none were available, to save on implementing
// a javascript force-selection method, also automatically select the cheapest shipping
// method if more than one module is now enabled
if ( !tep_session_is_registered('shipping') || ( tep_session_is_registered('shipping') && ($shipping == false) && (tep_count_shipping_modules() > 1) ) ) $shipping = $shipping_modules->cheapest();

require(DIR_WS_LANGUAGES . $language . '/' . FILENAME_CHECKOUT_SHIPPING);

$breadcrumb->add(NAVBAR_TITLE_1, tep_href_link(FILENAME_CHECKOUT_SHIPPING, '', 'SSL'));
$breadcrumb->add(NAVBAR_TITLE_2, tep_href_link(FILENAME_CHECKOUT_SHIPPING, '', 'SSL'));

$content = CONTENT_CHECKOUT_SHIPPING;
$javascript = $content . '.js';

require(DIR_WS_TEMPLATES . TEMPLATENAME_MAIN_PAGE);
require(DIR_WS_INCLUDES . 'application_bottom.php');
?>