<?php
/*
  	$Id: checkout_success.php,v 1.64 2014/12/12 10:14:18 chingyen Exp $

  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com

  	Copyright (c) 2003 osCommerce

  	Released under the GNU General Public License
*/

require('includes/application_top.php');
require(DIR_WS_LANGUAGES . $language . '/' . FILENAME_CHECKOUT_SUCCESS);

require(DIR_WS_CLASSES . 'order.php');
require_once(DIR_WS_CLASSES . 'payment_methods.php');
require_once(DIR_WS_CLASSES . 'phone_verification.php');
require_once(DIR_WS_MODULES . 'anti_fraud/maxmind/classes/ogm_maxmind.php');

// if the customer is not logged on, redirect them to the shopping cart page
if (!tep_session_is_registered('customer_id')) {
	tep_redirect(tep_href_link(FILENAME_SHOPPING_CART));
}

if (!tep_session_is_registered('order_completed')) {
	tep_redirect(tep_href_link(FILENAME_SHOPPING_CART, 'error_message='.TEXT_NO_ORDER_MADE_INFO_MSG));
}

require_once(DIR_WS_FUNCTIONS . 'user_agent.php');
require_once(DIR_WS_CLASSES . 'log.php');

$user_os = tep_get_os($HTTP_USER_AGENT);
$phone_verify_mode = ($user_os == "Windows" || $user_os == "Linux") ? 'xmlhttp_mode' : 'refresh_mode';
$verify_mode = 0;
$error = false;
$affiliate_id_limit = 301057; // REMOVE THIS FOR TESTING

$customer_id = $_SESSION['customer_id'];

$log_object = new log_files($_SESSION['customer_id']);

$reseller_group_id_array = array(6, 8, 9, 10, 11);

$order_info_select_sql = "	SELECT o.orders_id, o.currency, o.payment_method, o.payment_methods_id, o.payment_methods_parent_id, o.customers_country_international_dialing_code, o.billing_country, ot.value
							FROM " . TABLE_ORDERS . " AS o
							INNER JOIN " . TABLE_ORDERS_TOTAL . " AS ot
								ON o.orders_id=ot.orders_id
							WHERE o.customers_id = '" . (int)$customer_id . "'
								AND ot.class='ot_total'
							ORDER BY o.date_purchased desc LIMIT 1";
$order_info_result_sql = tep_db_query($order_info_select_sql);
$orders = tep_db_fetch_array($order_info_result_sql);

$prod_sel_sql = "SELECT orders_products_id FROM " . TABLE_ORDERS_PRODUCTS . " WHERE orders_id = " . $orders["orders_id"] . " AND custom_products_type_id = 3";
$prod_res_sql = tep_db_query($prod_sel_sql);
if (tep_db_num_rows($prod_res_sql)) {
    tep_redirect(HTTP_SHASSO_PORTAL . '/storeCredit/index?tid=' . $orders["orders_id"]);
}

$order = new order($orders['orders_id']);
$phone_verification_obj = new phone_verification();

$custom_products_type_msg_array = array();

$custom_prd_type_msg_array = array();
$custom_prd_type_msg_array[0] = sprintf(TEXT_CURRENCY_DELIVERY_INFO, tep_href_link(FILENAME_ACCOUNT_HISTORY_INFO, 'order_id='.$orders['orders_id'])) . '&nbsp;<a href="http://kb.offgamers.com/en/category/buy-purchase/currency/">' . tep_image(DIR_WS_ICONS.'help_info.gif', '', '12', '12', '') . '</a>';
$custom_prd_type_msg_array[1] = TEXT_PWL_DELIVERY_INFO . '&nbsp;<a href="http://kb.offgamers.com/en/category/buy-purchase/powerleveling/">' . tep_image(DIR_WS_ICONS.'help_info.gif', '', '12', '12', '') . '</a>';
$custom_prd_type_msg_array[2] = TEXT_CDK_DELIVERY_INFO . '&nbsp;<a href="http://kb.offgamers.com/en/2012/03/26/how-do-i-view-my-softpin/">' . tep_image(DIR_WS_ICONS.'help_info.gif', '', '12', '12', '') . '</a>';

/******************************************************
	This info are used in
	DIR_WS_JAVASCRIPT . 'yahoo_search_marketing.js.php'
******************************************************/
$yahoo_transId = $orders['orders_id'];
$yahoo_currency = $orders['currency'];
$yahoo_amount = $orders['value'];

$customer_log_select_sql = "SELECT c.customers_email_address, c.customers_country_dialing_code_id, c.customers_telephone
							FROM " . TABLE_CUSTOMERS . " AS c
							WHERE customers_id='" . (int)$_SESSION['customer_id'] . "'";
$customer_result_sql = tep_db_query($customer_log_select_sql);
$customer_row = tep_db_fetch_array($customer_result_sql);

$customer_email = $customer_row['customers_email_address'];

$module = '';
$module = new payment_methods($orders['payment_methods_id']);		// can be payment_methods also...
$module = $module->payment_method_array;

$action = $HTTP_GET_VARS['action'];

if (isset($action)) {
	if ($action == 'report') {
		tep_error_report($_SESSION['customer_id'], $orders['orders_id']);
	} else if ($action == 'resend') {
		if (strlen($customer_email) < ENTRY_EMAIL_ADDRESS_MIN_LENGTH) {//if length shorter than the min length
			$messageStack->add_session('checkout_success', ENTRY_EMAIL_ADDRESS_ERROR, 'error_box');
			tep_redirect(tep_href_link(FILENAME_CHECKOUT_SUCCESS)); //removing the "action=resend" to avoid repetitive loop
		} else if (tep_validate_email($customer_email) == false) {//validate the email format
			$messageStack->add_session('checkout_success', ENTRY_EMAIL_ADDRESS_CHECK_ERROR, 'error_box');
			tep_redirect(tep_href_link(FILENAME_CHECKOUT_SUCCESS)); //removing the "action=resend" to avoid repetitive loop
		} else {
			$customer_resend_select_sql = "	SELECT c.customers_id, c.customers_lastname, c.customers_gender, c.customers_firstname, civ.info_verified, civ.serial_number
											FROM " . TABLE_CUSTOMERS . " AS c
											INNER JOIN " . TABLE_CUSTOMERS_INFO_VERIFICATION . " AS civ
												ON (c.customers_email_address = civ.customers_info_value AND civ.info_verification_type = 'email' AND c.customers_id = civ.customers_id)
											WHERE c.customers_email_address = '" . tep_db_input($customer_email) ."'";
			$customer_resend_result_sql = tep_db_query($customer_resend_select_sql);

			if ($customer_resend_row = tep_db_fetch_array($customer_resend_result_sql)) {
				if ($customer_resend_row['info_verified'] == 1) {
					$messageStack->add('checkout_success', MESSAGE_EMAIL_VERIFIED);
				} else {
					tep_send_info_verification($customer_email, $customer_resend_row['customers_firstname'], $customer_resend_row['customers_lastname'], $customer_resend_row['customers_gender'], $customer_resend_row['serial_number'], $customer_resend_row['customers_id']);
					$messageStack->add_session('checkout_success', MESSAGE_SUCCESS_MAIL_2, 'success');
					tep_redirect(tep_href_link(FILENAME_CHECKOUT_SUCCESS));
				}
			} else {
				$messageStack->add_session('checkout_success', $customer_email . MESSAGE_ERROR_MAIL, 'error_box');
				tep_redirect(tep_href_link(FILENAME_CHECKOUT_SUCCESS)); //removing the "action=resend" to avoid repetitive loop
			}
		}
	}
}

$breadcrumb->add(NAVBAR_TITLE_SHOPPING_CART, tep_href_link(FILENAME_SHOPPING_CART, '', 'SSL'));
$breadcrumb->add(NAVBAR_TITLE_ORDER_PAYMENT, tep_href_link(FILENAME_CHECKOUT_PAYMENT, '', 'SSL'));
$breadcrumb->add(NAVBAR_TITLE_1);

$do_phone_verification = false;
$do_email_verification = false;

//if ($phone_verification_obj->get_service_provider_status() && (is_object($module) && $module->confirm_complete_days > 0)) {	// e.g. PayPal and WorldPay
//	$customer_flag_checking_select_sql = "	SELECT customers_id
//											FROM " . TABLE_CUSTOMERS . "
//											WHERE customers_id = '" . tep_db_input($_SESSION['customer_id']) . "'
//												AND (customers_flag='' OR customers_flag='1') ";
//	$customer_flag_checking_result_sql = tep_db_query($customer_flag_checking_select_sql);
//
//	if (tep_db_num_rows($customer_flag_checking_result_sql)) {
//        $maxmind_obj = new ogm_maxmind();
//		
//		if ($maxmind_obj->get_score_from_history($orders['orders_id']) < 4) {
//			$customer_complete_phone_info_array = tep_format_telephone($_SESSION['customer_id']);
//			$complete_telephone_number = $customer_complete_phone_info_array['country_international_dialing_code'] . $customer_complete_phone_info_array['telephone_number'];
//			$complete_telephone_number = preg_replace('/[^\d]/', '', $complete_telephone_number);
//			$country_name = $customer_complete_phone_info_array['country_name'];
//
//			if (tep_info_verified_check($_SESSION['customer_id'], $complete_telephone_number, 'telephone') != 1) {
//				$do_phone_verification = true;
//			}
//		}
//        
//        unset($maxmind_obj);
//	}
//}

//$info_verified_select_sql = "	SELECT c.customers_email_address, civ.info_verified, customers_groups_id
//								FROM " . TABLE_CUSTOMERS . " AS c
//								INNER JOIN " . TABLE_CUSTOMERS_INFO_VERIFICATION . " AS civ
//									ON (c.customers_email_address = civ.customers_info_value AND c.customers_id = civ.customers_id AND civ.info_verification_type = 'email')
//								WHERE c.customers_id = '" . tep_db_input($_SESSION['customer_id']) . "'";
//$info_verified_result_sql = tep_db_query($info_verified_select_sql);
//$info_verified_row = tep_db_fetch_array($info_verified_result_sql);
//
//if ($info_verified_row['info_verified'] == 0) {
//	$do_email_verification = true;
//}

//$banner_img_url = "http://image.offgamers.com/my_account_banner_".$_SESSION['languages_id'].".jpg";
$banner_img_url = '';

if ($languages_id == '1'){
	$banner_url = "http://space.offgamers.com/".$language_code."/blogs/sales-a-promotions/348-september-2010/3951-store-credits-extra-purchase";
} else {
	$banner_url = "http://space.offgamers.com/".$language_code."/blogs/sales-a-promotions-cn/353-2010-9/3919-5-extra-with-every-purchase-of-offgamers-store-credit";
}

$content = CONTENT_CHECKOUT_SUCCESS;
$javascript = 'popup_window.js';

require(DIR_WS_TEMPLATES . TEMPLATENAME_MAIN_PAGE);
require(DIR_WS_INCLUDES . 'application_bottom.php');
?>