<script language="javascript"><!--
var selectionTitle = Array();
selectionTitle['id'] = '';
selectionTitle['text'] = 'Please Select';

var loadingIcon = '<?=tep_image(DIR_WS_IMAGES . "loading.gif", "", 20, 20)?>';
var delivery_option_array = new Array('delivery_mode_f2f', 'delivery_mode_guya', 'delivery_mode_mail', 'delivery_mode_open_store');

isIE=document.all;
isNN=!document.all&&document.getElementById;
isN4=document.layers;
isHot=false;

function DOMCall(name) {
	if (document.getElementById)
    	return document.getElementById(name);
	else if (document.all)
		return document.all[name];
	else if (document.layers)
		return document.layers[name];
}

function appendToSelect(select, value, content) {
	var opt = document.createElement('option');
	opt.value = value;
	opt.text = content;
	select.options.add(opt);
}

function clearOptionList(obj) {
	while (obj.options.length > 0) {
		obj.remove(0);
	}
}

function initInfoCaptions() {
   	// create a dynamic layer and attach it to the HTML document.
	objBody = document.getElementsByTagName('body').item(0);
    objContainer = document.createElement('div');
    objContainer.setAttribute('id', 'infocaption');
    
    objBody.appendChild(objContainer);
}

function add_to_cart_form_checking () {
	var error_message = '';
	
	var mode = 1;
	
	var char_name_obj = DOMCall('extra_info_char_name');
	var online_hr_obj = DOMCall('extra_info_char_online_hr');
	var online_dur_obj = DOMCall('extra_info_char_online_dur');
	var char_account_name_obj = DOMCall('extra_info_char_account_name');
	var char_account_pwd_obj = DOMCall('extra_info_char_account_pwd');
	
	for (var opt_cnt=0; opt_cnt < delivery_option_array.length; opt_cnt++) {
		var tmp_opt_obj = DOMCall(delivery_option_array[opt_cnt]);
		if (tmp_opt_obj != null && tmp_opt_obj.checked) {
			mode = opt_cnt + 1;
		}
	}
	
	if (char_name_obj != null) {
		if (trim_str(char_name_obj.value) == '') {
			error_message = error_message + "* " + '<?=ERROR_PLS_ENTER_CHAR_NAME?>' + "<br>";
		}
	}
	
	if (mode == 1) {
		;
	} else if (mode == 2) {
		if (char_account_name_obj != null) {
			if (trim_str(char_account_name_obj.value) == '') {
				error_message = error_message + "* " + '<?=ERROR_PLS_ENTER_ACCOUNT_NAME?>' + "<br>";
			}
		}
		
		if (char_account_pwd_obj != null) {
			if (trim_str(char_account_pwd_obj.value) == '') {
				error_message = error_message + "* " + '<?=ERROR_PLS_ENTER_PASSWORD?>' + "<br>";
			}
		}
	}
	
	if (trim_str(error_message) != '') {
		var action_msn_obj = DOMCall('div_action_msg');
		if (action_msn_obj != null)	action_msn_obj.innerHTML = '<table border="0" width="100%" bordercolor="#A7A7A7" bgcolor="#ffffff" cellspacing="2" cellpadding="2"><tr><td class="main"><font color="red">'+error_message+'</font></td></tr></table>';
		
		return false;
	} else {
		return true;
	}
}

function showDeliveryInfo(mode) {
	var delivery_message_array = new Array('<?=TEXT_ORDER_FACE_TO_FACE_NOTE?>', '<?=TEXT_ORDER_GUYA_NOTE?>', '<?=TEXT_ORDER_MAIL_NOTE?>', '<?=TEXT_ORDER_OPEN_STORE_NOTE?>');
	var delivery_mode_note_obj = DOMCall('delivery_mode_note');
	
	if (delivery_mode_note_obj != null) {
		DOMCall('delivery_mode_note').innerHTML = '';
	}
	
	for (var opt_cnt=0; opt_cnt < delivery_option_array.length; opt_cnt++) {
		var opt_pos = opt_cnt+1;
		
		if (mode == opt_pos) {
			DOMCall('delivery_sec_'+opt_pos).className = 'show';
			
			if (delivery_mode_note_obj != null) {
				DOMCall('delivery_mode_note').innerHTML = delivery_message_array[opt_cnt];
			}
		} else {
			DOMCall('delivery_sec_'+opt_pos).className = 'hide';
		}
	}
	
	var action_msn_obj = DOMCall('div_action_msg');
	if (action_msn_obj != null)	action_msn_obj.innerHTML = '';
	
	realign_fancybox("popup_add_to_cart");
}

function f_filterResults(n_win, n_docel, n_body) {
	var n_result = n_win ? n_win : 0;
	if (n_docel && (!n_result || (n_result > n_docel)))
		n_result = n_docel;
	return n_body && (!n_result || (n_result > n_body)) ? n_body : n_result;
}

function hideMe() {
	if (typeof(document.getElementById('theLayer')) != 'undefined' && document.getElementById('theLayer') != null) {
		document.getElementById('theLayer').style.visibility = "hidden";
		document.getElementById('theLayerBg').style.visibility = "hidden";
		jQuery('body').css('overflow','');
	}
	
	if (typeof(document.getElementById('theLayerIframe')) != 'undefined' && document.getElementById('theLayerIframe') != null) {
		document.getElementById('theLayerIframe').style.visibility = "hidden";
	}
}

function add_send_to_my_account(pid, custom_products_type, qty, products_bundle, mID) {
	mID = mID || '';
	
	jQuery("#div_error_msg_send_to_my_account").html("");
	
	var default_html = '';
	if (mID != '' && jQuery("tr#delivery_mode_content_" + mID).length > 0) {
		default_html = jQuery("tr#delivery_mode_content_" + mID).html();
		jQuery("tr#delivery_mode_content_" + mID).html('<td colspan="3" style="text-align:center;">' + loadingIcon + '</td>');
	}
	
	if (validateInteger(qty) && qty > 0) {
		add_to_shopping_cart('<?=tep_href_link(basename($PHP_SELF), tep_get_all_get_params(array("action", "products_id", "delivery_mode", "custom_product")) . "action=buy_now&products_id='+pid+'&delivery_mode=5&custom_product='+custom_products_type")?> , qty, products_bundle ,"");
	} else {
		if (default_html!='') {
			jQuery("tr#delivery_mode_content_" + mID).html(default_html);
		}
		jQuery("#div_error_msg_send_to_my_account").html("<?=ERROR_INVALID_QUANTITY?>");
	}
}

function submit_topup(pid, pass_index, qty) {
	var submit_flag = true;
	var qty = qty || '1';
	
	jQuery(".tr_error_msg_"+pass_index).hide();
	jQuery(".tr_error_msg_"+pass_index+" #div_error_msg").html("");
	var submit_data = 'action=direct_topup_insert_cart&delivery_mode=6&pid='+pid+'&';
	jQuery('#td_cart_info_'+pass_index+ ' input, #td_cart_info_'+pass_index+ ' select, #td_cart_info_'+pass_index+ ' textarea').each(function() { 
		if (jQuery(this).val()=='' || jQuery(this).val() == jQuery(this).attr('default_text')) {
			jQuery(this).click();
			jQuery(this).focus();
			submit_flag = false;
		}
		if (jQuery(this).hasClass('character_list_loaded')) {
			submit_data += jQuery(this).attr('name') + '=' + jQuery(this).val() + '##' + jQuery(this).find('option[value='+jQuery(this).val()+']').text() + '&';
		} else {
			submit_data += jQuery(this).attr('name') + '=' + jQuery(this).val() + '&';
		}
    });
    submit_data += 'txt_qty=' + qty;
    
    if (submit_flag) {
    	jquery_notice_box('<?=TEXT_INFO_VERIFYING?>');
		jQuery.ajax({
			url:'<?=tep_href_link("checkout_xmlhttp.php")?>',
			data:submit_data,
			type: 'POST',
			dataType: 'xml',
			timeout: 60000,
			error: function(){
				jQuery.unblockUI();
			},
			success: function(xml) {
				jQuery.unblockUI();
				if (jQuery(xml).find('error_message').length > 0 && jQuery(xml).find('error_message').text() != '' ) {
					jQuery('#tr_top_up_error_msg_'+pass_index+ ' div#div_error_msg').text(jQuery(xml).find('error_message').text());
					jQuery("tr#tr_top_up_error_msg_"+pass_index).show();
				} else {
					
					jQuery("div.delivery_mode_bg_content input").val('');
					
					var total_item = jQuery(xml).find('total_item').text();
					var product_name = jQuery(xml).find('product_name').text();
					var subtotal = jQuery(xml).find('subtotal').text();
					
					jQuery('#mini_cart_added_item').html(product_name);
					jQuery('#mini_cart_total b').html(subtotal);
					jQuery('.itemQty').html(total_item);
					
					PopUpAddToCart();
					
					clearInterval(minicart_interval);
					
					minicart_interval = setInterval("hide_footer_popup('footer_cart')", 8000);
				}
			}
		});
	}
}

//to be removed
function direct_topup_insert_cart(pid, mID) {
	mID = mID || '';
	
	jQuery("#div_top_up_error_msg").html('');
	
	if (jQuery("tr#delivery_mode_content_6 input#txt_qty").length ==0 || !validateInteger(jQuery("tr#delivery_mode_content_6 input#txt_qty").val()) || jQuery("tr#delivery_mode_content_6 input#txt_qty").val() == 0) {
		jQuery("#div_top_up_error_msg").html("<?=ERROR_INVALID_QUANTITY?>");
		return ;
	}
	
	var submit_data = 'action=direct_topup_insert_cart&';
	var submit_flag = true;
	
	jQuery('#frm_top_up_'+pid+ ' input, #frm_top_up_'+pid+ ' select, #frm_top_up_'+pid+ ' textarea').each(function() { 
		if (jQuery(this).val()=='') {
			jQuery(this).focus();
			submit_flag = false;
		}
		submit_data += jQuery(this).attr('name') + '=' + jQuery(this).val() + '&';
    });
    
    if (submit_flag) {
    	if ( mID != '' ) {
    		current_input_html = jQuery("tr#delivery_mode_content_"+mID).html()
    	} else {
    		current_input_html = jQuery("#fancy_content").html();
    	}
    	
    	var pop_out_loading;
	    pop_out_loading = '<table id="popup_loading" cellspacing="0" cellpadding="0" width="100%"><tr><td>';
		pop_out_loading += '<table id="regional_table" width="100%" bgcolor="#ffffff" cellspacing="2" cellpadding="10" style="border: 5px solid white">';
		pop_out_loading += '<tr><td align="center" style="text-align:center;">'+loadingIcon+'</td></tr>';
		pop_out_loading += '</table>';
		pop_out_loading += '</td></tr></table>';
		
		if ( mID != '' ) {
        	jQuery("tr#delivery_mode_content_"+mID).html('<td colspan="2" style="text-align:center;">'+loadingIcon+'</td>');
        } else {
        	jQuery("#fancy_content").html(pop_out_loading);
        }
        
        submit_data += 'delivery_mode=6&pid='+pid;
        jQuery.ajax({
        	url:'<?=tep_href_link("checkout_xmlhttp.php")?>',
        	data:submit_data,
			type: 'POST',
			dataType: 'xml',
			timeout: 60000,
			error: function(){
				hideFancyBox();
			},
        	success: function(xml) {
        		var type = jQuery(xml).find('type').text();
		        if (type == 'redirect') {
			        var url = jQuery(xml).find('url').text();
					document.location.href = url;
				} else {
					if (jQuery(xml).find('error_message').length > 0) {
						if ( mID != '' ) {
							jQuery("tr#delivery_mode_content_"+mID).html(current_input_html);
						} else {
							jQuery("#fancy_content").html(current_input_html);
						}
						jQuery("#div_top_up_error_msg").html(jQuery(xml).find('error_message').text());
						
						jQuery(".fancy_close_footer").css('display', 'block');
						set_fancybox_position();
						realign_fancybox("table_delivery_tab");
						
						jQuery("#btn_direct_topup_insert_cart_"+pid).click(function(){
							direct_topup_insert_cart(pid, mID);
						});
					} else {
						hideFancyBox();
						var total_item = jQuery(xml).find('total_item').text();
						var product_name = jQuery(xml).find('product_name').text();
						var subtotal = jQuery(xml).find('subtotal').text();
						
						//scroll(0,0);
						
						jQuery('#mini_cart_added_item').html(product_name);
						jQuery('#mini_cart_total b').html(subtotal);
						jQuery('.itemQty').html(total_item);
						
						PopUpAddToCart();
						
						clearInterval(minicart_interval);
						
						minicart_interval = setInterval("hide_footer_popup('footer_cart')", 8000);
					}
				}
        	}
        });
    } else {
    	return false;
    }
}
	        
function showMe(pid) {
	var server_action = 'get_product_info';
	var pop_out;
	
	if (pid != '') {
	    pop_out_loading = '<table id="popup_loading" cellspacing="0" cellpadding="0" width="100%"><tr><td>';
		pop_out_loading += '<table id="regional_table" width="100%" bgcolor="#ffffff" cellspacing="2" cellpadding="10" style="border: 5px solid white">';
		pop_out_loading += '<tr><td align="center" style="text-align:center;">'+loadingIcon+'</td></tr>';
		pop_out_loading += '</table>';
		pop_out_loading += '</td></tr></table>';
		
		jQuery('#general_content').html(pop_out_loading);
		show_fancybox('general_popup_box');

		var ref_url = "checkout_xmlhttp.php?action="+server_action+"&pid="+pid;
		
		jQuery.ajax({
			url: ref_url,
			type: 'GET',
			dataType: 'xml',
			timeout: 60000,
			
			success: function(xml) {
				var products_id = jQuery(xml).find('products_id').text();
	      		var custom_product = jQuery(xml).find('custom_product').text();
	      		var products_quantity = jQuery(xml).find('products_quantity').text();
	      		var products_bundle = jQuery(xml).find('products_bundle').text();
	      		var products_cat = jQuery(xml).find('products_cat').text();
	      		var products_name = jQuery(xml).find('products_name').text();
	      		var delivery_option = jQuery(xml).find('delivery_option').text();
	      		
	      		var default_delivery_option = '';
				
				pop_out = '<?=tep_draw_form("buy_now", tep_href_link(basename($PHP_SELF), tep_get_all_get_params(array("action"))."action=buy_now&products_id='+products_id+'&custom_product='+custom_product+'"), "post", "onSubmit=\"return add_to_cart_form_checking();\"").tep_draw_hidden_field("buyqty", 1).tep_draw_hidden_field("buy_now_qty", "'+products_quantity+'").tep_draw_hidden_field("products_bundle", "'+products_bundle+'")?>';
	      		pop_out += '<table id="popup_add_to_cart" width="100%" cellspacing="0" cellpadding="0" style="background-color:#ffffff">';
				pop_out += '<tr><td colspan="2"><div style="padding:10px 20px;float:left"><b class="largeFont"><?=HEADER_DELIVERY_INFORMATION ?></b></div><div style="float:right;padding:10px 15px"></div></td></tr>';
				pop_out += '<tr><td class="smallText" colspan="2" height="10"><div class="dottedLine"><!-- --></div></td></tr>';
				pop_out += '<tr><td class="smallText" colspan="2" style="padding: 10px 20px 10px 20px;"><font color="red"><?=NOTE_DO_NOT_RETURN_GOODS?></font><td></tr>';
				pop_out += '<tr><td class="smallText" colspan="2"><div style="padding:10px 20px;float:left"><font class="largeText"><b>'+products_name+'</b></font><br>'+products_cat+'</div></td></tr>';
				pop_out += '<tr><td align="right" colspan="2"><div style="padding:0 20px;float:left"><table border="0" width="100%" cellspacing="0" cellpadding="0"><tr>'+(delivery_option.indexOf(1)>= 0 ? '<td class="smallText"><?=tep_draw_radio_field("extra_info[delivery_mode]", '1', false, 'id="delivery_mode_f2f" onClick="showDeliveryInfo(1)" style="background:#ffffff; color:#000000;"')?><label for="delivery_mode_f2f"><?=OPTION_FACE_TO_FACE ?></label></td>' : '')+(delivery_option.indexOf(2)>= 0 ? '<td class="smallText"><?=tep_draw_radio_field("extra_info[delivery_mode]", '2', false, 'id="delivery_mode_guya" onClick="showDeliveryInfo(2)" style="background:#ffffff; color:#000000;"')?><label for="delivery_mode_guya"><?=OPTION_PUT_IN_MY_ACCOUNT ?></label></td>' : '')+(delivery_option.indexOf(3)>= 0 ? '<td class="smallText"><?=tep_draw_radio_field("extra_info[delivery_mode]", '3', false, 'id="delivery_mode_mail" onClick="showDeliveryInfo(3)" style="background:#ffffff; color:#000000;"')?><label for="delivery_mode_mail"><?=OPTION_BY_MAIL ?></label></td>' : '')+(delivery_option.indexOf(4)>= 0 ? '<td class="smallText"><?=tep_draw_radio_field("extra_info[delivery_mode]", '4', false, 'id="delivery_mode_open_store" onClick="showDeliveryInfo(4)" style="background:#ffffff; color:#000000;"')?><label for="delivery_mode_open_store"><?=OPTION_OPEN_STORE ?></label></td>' : '')+'</tr></table></div></td></tr>';
				pop_out += '<tr height="40"><td class="smallText" colspan="2"><div style="padding:20px;float:left"><div id="delivery_mode_note" style="border: 1px solid #FF0000; background-color: #FFFFED; padding: 1px;"></div></div></td></tr>';
				pop_out += '<tr><td align="right" class="smallText" width="35%"><div style="padding:5px 10px 5px 20px;float:left"><?=ENTRY_CHARACTER_NAME?>:</div></td><td class="smallText"><?=tep_draw_input_field("extra_info[char_name]", "", 'size="33" id="extra_info_char_name"')?> <span class="redIndicator">*</span></td></tr>';
				pop_out += '<tbody id="delivery_sec_1" class="hide"></tbody>';
				pop_out += '<tbody id="delivery_sec_2" class="hide"><tr><?=sprintf(ENTRY_ORDER_GUYA_ACCOUNT, tep_draw_input_field('extra_info[char_account_name]', '', 'size="33" id="extra_info_char_account_name"'))?></tr>';
				pop_out += '<tr><?=ENTRY_ORDER_GUYA_WOW_ACCOUNT_INFO?></tr>';
				pop_out += '<tr><?=sprintf(ENTRY_ORDER_GUYA_PWD, tep_draw_input_field('extra_info[char_account_pwd]', '', 'size="33" id="extra_info_char_account_pwd"'))?></tr>';
				pop_out += '<tr><?=sprintf(ENTRY_ORDER_GUYA_WOW_ACCOUNT, tep_draw_input_field('extra_info[char_wow_account]', '', 'size="22" id="extra_info_char_wow_account"'))?></tr>';
				pop_out += '<tr><?=ENTRY_ORDER_GUYA_WOW_ACCOUNT_INFO2?></tr></tbody>';
				pop_out += '<tbody id="delivery_sec_3" class="hide"><tr><td></td></tr></tbody>';
				pop_out += '<tbody id="delivery_sec_4" class="hide"><tr><td></td></tr></tbody>';
				pop_out += '<tr><td></td><td class="smallText"><div id="div_action_msg"></div><td></tr>';
				pop_out += '<tr><td class="smallText" colspan="2"><div class="dottedLine"><!-- --></div></td></tr>';
				pop_out += '<tr><td></td><td><table border="0" cellspacing="0" cellpadding="10"><tr><td nowrap><?=tep_image_button2("green", "javascript:add_to_shopping_cart_cur(\'" . tep_href_link(basename($PHP_SELF), tep_get_all_get_params(array("action"))."&1") . "&action=buy_now&products_id='+products_id+'&custom_product='+custom_product+'\',\'1\',\''+products_bundle+'\',\''+ products_quantity +'\')", IMAGE_BUTTON_IN_CART);?></td></tr></table></td></tr>';
				pop_out += '</table></form>';

				jQuery('#general_content').html(pop_out);
				jQuery('#general_content').css('padding', '0px');
				show_fancybox('general_popup_box');
				for (var opt_cnt=0; opt_cnt < delivery_option_array.length; opt_cnt++) {
					var tmp_opt_obj = DOMCall(delivery_option_array[opt_cnt]);
					if (delivery_option.indexOf((opt_cnt+1)) >= 0) {
						//tmp_opt_obj.disabled = false;
						
						if (default_delivery_option == '')	{
							default_delivery_option = (opt_cnt + 1);
							tmp_opt_obj.checked = true;
						}
					}
				}
				
				showDeliveryInfo(default_delivery_option);
			}
		});
	}
}

function load_delivery_content(pass_id) {
	if (jQuery("tr#delivery_mode_content_"+pass_id).css('display')=='none') {
		jQuery("tr.delivery_mode_content").hide();
		jQuery("div.delivery_mode_selection").addClass('delivery_mode_bg');
		jQuery("div.delivery_mode_selection").css('height', '47px');
		
		jQuery("div.delivery_mode_bg_"+pass_id).removeClass('delivery_mode_bg');
		jQuery("div.delivery_mode_bg_"+pass_id).css('height', 'auto');
		
		jQuery("tr#delivery_mode_content_"+pass_id).fadeIn('slow');
		set_fancybox_position();
		realign_fancybox("table_delivery_tab");
	}
}

function showMe_hla(pid, hla_account_id) {
	var server_action = 'get_hla_product_info';
	var pop_out;
	
	var products_id = '';
	var custom_product = '';
	var products_bundle = '';
	var products_quantity = '';
	
	if (pid != '') {
		pop_out_loading = '<table id="popup_loading" cellspacing="0" cellpadding="0" width="100%"><tr><td>';
		pop_out_loading += '<table id="regional_table" width="100%" bgcolor="#ffffff" cellspacing="2" cellpadding="10" style="border: 5px solid white">';
		pop_out_loading += '<tr><td align="center" style="text-align:center;">'+loadingIcon+'</td></tr>';
		pop_out_loading += '</table>';
		pop_out_loading += '</td></tr></table>';
		jQuery('#general_content').html(pop_out_loading);
		show_fancybox('general_popup_box');
		
		var ref_url = "checkout_xmlhttp.php?action="+server_action+"&products_id="+pid+"&hla_acc_id="+hla_account_id;
		
		jQuery.get(ref_url, function(xml) {
			jQuery(xml).find('response').each(function(){
				var category_name = jQuery("category_name", this).text();
				var hla_acc_id = jQuery("hla_acc_id", this).text();
				var products_name = jQuery("products_name", this).text();
				var products_description = jQuery("products_description", this).text();
				
				if (products_description == '') {
	      			add_to_shopping_cart('<?=tep_href_link(basename($PHP_SELF), tep_get_all_get_params(array("action"))."&1")?>&action=buy_now&products_id='+pid+'&hla_account_id='+hla_acc_id+'&custom_product=4' , '1');
	      			hide_fancybox('general_popup_box');
	      		} else {
	      			pop_out = '<form method="POST" name="region_confirmation_form" action="">';
			  		pop_out += '<table id="popup_region_confirmation" width="100%" cellspacing="0" cellpadding="0">';
					pop_out += '<tr><td colspan="2"><div style="padding:10px 20px;float:left"><b class="largeFont"><?=BOX_HEADING_INFORMATION?></b></div><div style="float:right;padding:10px 15px"></div></td></tr>';
					pop_out += '<tr><td class="mediumFont" colspan="2" height="10"><div class="dottedLine"><!-- --></div></td></tr>';
					pop_out += '<tr><td class="mediumFont" colspan="2" style="font-size:12px;padding: 10px 20px 10px 20px;"><b>'+products_name+'<br />'+category_name+'</b><td></tr>';
					pop_out += '<tr><td colspan="2"><div style="padding:5px 10px 5px 20px;float:left" class="mediumFont">'+products_description+'</div></td></tr>';
					pop_out += '<tr><td colspan="2"><div class="dottedLine"><!-- --></div></td></tr>';
					pop_out += '<tr><td colspan="2"><table border="0" cellspacing="0" cellpadding="10" align="center"><tr><td nowrap><?=tep_image_button2("green", "javascript:region_confirmation(\''+pid+'\', \''+hla_acc_id+'\');", IMAGE_BUTTON_IN_CART);?></td></tr></table></td></tr>';
					pop_out += '</table></form>';
					
					jQuery('#general_content').html(pop_out);
					jQuery('#general_content').css('padding', '0px');
					show_fancybox('general_popup_box');
	      		}
			});
		});
	}
}

function region_confirmation(pid, hla_acc_id) {
	add_to_shopping_cart('<?=tep_href_link(basename($PHP_SELF), tep_get_all_get_params(array("action"))."&1")?>&action=buy_now&products_id='+pid+'&hla_account_id='+hla_acc_id+'&custom_product=4' , '1');
	hideFancyBox();
	hide_fancybox('general_popup_box');
}

var selected_pm_sel = '0';
var selected_pm_info = '';
var selected_currency = '';

function showSCConfirm() {
	var server_action = 'get_store_credit_currency';
	var pop_out;

	var ref_url = "checkout_xmlhttp.php?action="+server_action+'&selected_currency='+selected_currency+'&selected_pm_info='+selected_pm_info;
	
	jQuery.ajax({
		url: 'checkout_xmlhttp.php?action='+server_action+'&selected_currency='+selected_currency+'&selected_pm_info='+selected_pm_info,
		type: 'GET',
		dataType: 'xml',
		timeout: 60000,
		success: function(xml) {
			var sc_currency_code = jQuery(xml).find('sc_currency_code').text();
			var no_sc_account = jQuery(xml).find('no_sc_account').text();
			var no_supported_currency = jQuery(xml).find('no_supported_currency').text();
			var sc_convert_in_my_account = jQuery(xml).find('sc_convert_in_my_account').text();
			var sc_convert_text = jQuery(xml).find('sc_convert_text').text();
      		
			pop_out  = '<table id="change_sc_currency_content" border="0" cellspacing="0" cellpadding="0" width="100%"><tr><td>';
			pop_out += '<table id="sc_table" border="0" cellspacing="0" cellpadding="3" height="100%" width="100%">';
			pop_out += '<tr><td width="3" style="padding:15px 0px;"></td><td class="footerPopupTitle" colspan="4"><?=HEADER_STORE_CREDIT_USE?></td></tr>';
			pop_out += '<tr><td colspan="5"><div style="padding-top: 3px; margin-top: 0px;" class="solidGreyLine"><!-- --></div></td></tr>';
			
			if (sc_convert_in_my_account == 1) {
				pop_out += '<tr><td width="3" style="padding:5px 10px;"></td><td colspan="3"><label for="act_login">' + sc_convert_text + '</label></td><td width="3" style="padding:5px 10px;"></td></tr>';
			} else {
				pop_out += '<tr><td width="3" style="padding:5px 10px;"></td><td colspan="3"><label for="act_login"><?=TEXT_STORE_CREDIT_USE ?></label></td><td width="3" style="padding:5px 10px;"></td></tr>';
			}
			
			pop_out += '<tr><td colspan="5"><div style="padding-top: 5px; margin-top: 0px;" class="dottedLine"><!-- --></div></td></tr>';
			pop_out += '<tr><td width="3" style="padding:5px 10px;"></td><td colspan="3">';
			pop_out += '<table border="0" width="100%" cellspacing="0" cellpadding="0" style="padding:7px 0px 10px 0px;">';
			pop_out += '<tr><td><?=tep_image_button2("green", "javascript:hide_fancybox(\'general_popup_box\')", BUTTON_NO, 100)?></td><td>&nbsp;</td>';
			
			if (sc_convert_in_my_account == 1) {
				pop_out += '<td align="right"><?=tep_image_button2("green", tep_href_link(FILENAME_ACCOUNT_STORE_CREDIT), IMAGE_BUTTON_YES, 100)?></td></tr>';
			} else {
				pop_out += '<td align="right"><?=tep_image_button2("green", "javascript:set_localization_value(\'currency\' , \''+sc_currency_code+'\' , \'\');", IMAGE_BUTTON_YES, 100)?></td></tr>';
			}
			
			pop_out += '</table>';
			pop_out += '</td><td width="3" style="padding:5px 10px;"></td></tr>';
			pop_out += '</table>';
			pop_out += '</td></tr></table>';
			jQuery('#general_content').html(pop_out);
			jQuery('#general_content').css('padding', '0px');
			show_fancybox('general_popup_box');
		}
	});
}

function submit_sc_conversion(curr_code) {
	var server_action = 'convert_store_credit';
	
	blockUI_disable();
	jQuery.ajax({
		url: 'checkout_xmlhttp.php?action='+server_action+'&currency_selection='+curr_code,
		type: 'GET',
		dataType: 'xml',
		timeout: 60000,
		error: function(){
			jQuery.unblockUI();
		},
		success: function(xml) {
			var error_message = jQuery(xml).find('error_message').text();
   			var success_message = jQuery(xml).find('success_message').text();
      		
      		if (error_message == '') {
      			//setTimeout("Hot.set($('sum_payment'), 188, 80)", 180);
      			set_localization_value('currency' , curr_code , '');
	      	} else {
	      		alert(error_message);
	      		location.reload(true);
	      	}
	      	
	      	jQuery.unblockUI();
		}
	});
}

function _resetSelection(objID) {
	var loop_cnt = 1;
	var cat_tree_info = objID.id.split('_');
	var next_child_tree = DOMCall(cat_tree_info[0] + '_' + (parseInt(cat_tree_info[1], 10) + loop_cnt));
	
	while (next_child_tree != null && typeof(next_child_tree) != 'undefined' && next_child_tree.type == 'select-one') {
		loop_cnt++;
		
		clearOptionList(next_child_tree);
		appendToSelect(next_child_tree, selectionTitle['id'], selectionTitle['text']);
	    next_child_tree.disabled = true;
	    
	    next_child_tree = DOMCall(cat_tree_info[0] + '_' + (parseInt(cat_tree_info[1], 10) + loop_cnt));
	}
}

function cl_img(img_src) {
	ddrivetip('<img src="'+img_src+'">', '0', '0');
}

function updateCurrency(sel, user_action, selected_currency) {
	var objRef = DOMCall('pm_'+sel);
	
	if (typeof(objRef) == 'undefined' || objRef == null) {
		return false;
	}
	
	if ((objRef.checked != true && user_action == '') || user_action != '') {
	
		objRef.checked = true;
		jQuery("#pm_"+sel).attr('checked', true);
	
		var server_action = 'set_currency';
		var display_html = '';
		var ori_cur = '';
		
		selected_currency = selected_currency || '';
		if (selected_currency == '' ) {
			selected_currency = '<?=$currency?>';
		}
		
		var ref_url = 'checkout_xmlhttp.php?action='+server_action+'&currency='+selected_currency+'&curr_code='+selected_currency+'&payment_info='+objRef.value+'&sel='+sel;
		
		selected_pm_sel = sel;
		selected_pm_info = objRef.value;
		
		hide_payment_customer_info();
		
		//disabledFormInputs('payment_methods_form', 'disabled');
		
		var loading_button = '<?=tep_image_button2('red', 'javascript:this.void(0)', TEXT_IS_LOADING, 200)?>';
		
		jQuery('#checkout_button_div').html(loading_button);
		
		if (typeof(curr_code) != 'undefined' && curr_code != null) {
			curr_code.disabled = false;
		}
		blockUI_disable();
		
		if (jQuery("#sel_checkout_currency").val() != selected_currency) {
			jQuery.ajax({
				url: '<?=tep_href_link(FILENAME_CHECKOUT_PAYMENT, 'currency=\'+selected_currency', 'SSL')?>,
				type: 'GET',
				timeout: 30000,
				error: function(){
					jQuery.unblockUI();
				},
				success: function() {
					jQuery.ajax({
						url: ref_url,
						type: 'GET',
						dataType: 'xml',
						timeout: 30000,
						error: function(){
						jQuery.unblockUI();

						},
						success: function(xml) {
							jQuery("#sel_checkout_currency").val(selected_currency);
							
							//jQuery.unblockUI();
							var error = jQuery(xml).find('error').text();
							var shopping_cart = jQuery(xml).find('cart_display').text();
							var surcharge_display = jQuery(xml).find('surcharge_display').text();
							
							if (error == '') {
								jQuery('#cartbox_div').html(shopping_cart);
								jQuery('#surcharge_'+sel).html(surcharge_display);
			//      			setTimeout("Hot.set($('sum_payment'), 220, 130)", 180);
							} else {
								alert('Error! Please Try again.');
								location.reload(true);
							}
							
							objRef.checked = true;
							jQuery.unblockUI();
							jQuery('#pm_cust_info_'+sel).fadeIn('fast');
							jQuery('#pm_cust_info_'+sel+' input').attr('disabled', false);
						}
					});
				}
			});
		} else {
			jQuery.ajax({
				url: ref_url,
				type: 'GET',
				dataType: 'xml',
				timeout: 30000,
				error: function(){
				jQuery.unblockUI();

				},
				success: function(xml) {
					//jQuery.unblockUI();
					var error = jQuery(xml).find('error').text();
					var shopping_cart = jQuery(xml).find('cart_display').text();
					var surcharge_display = jQuery(xml).find('surcharge_display').text();
					
					if (error == '') {
						jQuery('#cartbox_div').html(shopping_cart);
						jQuery('#surcharge_'+sel).html(surcharge_display);
	//      			setTimeout("Hot.set($('sum_payment'), 220, 130)", 180);
					} else {
						alert('Error! Please Try again.');
						location.reload(true);
					}
					
					objRef.checked = true;
					jQuery.unblockUI();
					jQuery('#pm_cust_info_'+sel).fadeIn('fast');
					jQuery('#pm_cust_info_'+sel+' input').attr('disabled', false);
				}
			});
		}
	}
}

function changeCategory(cat_sel) {
	var path = "<?=tep_href_link('index.php')?>";
	var param = "cPath=" + cat_sel.value;
	
	path = (path.indexOf('?') > 0 ? (path + '&' + param) : (path + '?' + param));
	
	gotoPage(path);
}

function hide_payment_customer_info() {
	jQuery('.system_cust_pm_info').fadeOut('fast');
}

function update_pm_cust_info(pass_div) {
	var submit_str = '';
	jQuery('#' + pass_div + ' input').each(function(){
	  submit_str += jQuery(this).attr('name') + '=' + jQuery(this).val() + '&';
	});
	blockUI_disable();
	jQuery.ajax({
		url: 'checkout_xmlhttp.php?action=update_pm_cust_info&'+submit_str,
		type: 'GET',
		dataType: 'xml',
		timeout: 60000,
		error: function(){
			jQuery.unblockUI();
		},
		
		success: function(xml) {
			if (jQuery(xml).find('result').text()=='1') {
				jQuery.unblockUI();
			} else {
				jquery_confirm_box(jQuery(xml).find('message').text(), 1, 1, '<?=JS_ERROR_TITLE_PAYMENT_INFORMATION?>');
			}
			if (jQuery(xml).find('identify_number').length > 0) {
				jQuery('#' + pass_div + ' input[@id=identify_number]').val(jQuery(xml).find('identify_number').text())
				
			}
			if (jQuery(xml).find('customers_telephone').length > 0) {
				jQuery('#' + pass_div + ' input[@id=telephone_number]').val(jQuery(xml).find('customers_telephone').text())
			}
			if (jQuery(xml).find('checkout_city').length > 0) {
				jQuery('#' + pass_div + ' input[@id=checkout_city]').val(jQuery(xml).find('checkout_city').text())
			}
			if (jQuery(xml).find('checkout_zip').length > 0) {
				jQuery('#' + pass_div + ' input[@id=checkout_zip]').val(jQuery(xml).find('checkout_zip').text())
			}
			if (jQuery(xml).find('checkout_surname').length > 0) {
				jQuery('#' + pass_div + ' input[@id=checkout_surname]').val(jQuery(xml).find('checkout_surname').text())
			}
			if (jQuery(xml).find('checkout_housenumber').length > 0) {
				jQuery('#' + pass_div + ' input[@id=checkout_housenumber]').val(jQuery(xml).find('checkout_housenumber').text())
			}
			if (jQuery(xml).find('checkout_street').length > 0) {
				jQuery('#' + pass_div + ' input[@id=checkout_street]').val(jQuery(xml).find('checkout_street').text())
			}

			if (jQuery(xml).find('checkout_accountname').length > 0) {
				jQuery('#' + pass_div + ' input[@id=checkout_accountname]').val(jQuery(xml).find('checkout_accountname').text())
			}
			if (jQuery(xml).find('checkout_account_number').length > 0) {
				jQuery('#' + pass_div + ' input[@id=checkout_account_number]').val(jQuery(xml).find('checkout_account_number').text())
			}
			if (jQuery(xml).find('checkout_bankcode').length > 0) {
				jQuery('#' + pass_div + ' input[@id=checkout_bankcode]').val(jQuery(xml).find('checkout_bankcode').text())
			}	
			if (jQuery(xml).find('checkout_branchcode').length > 0) {
				jQuery('#' + pass_div + ' input[@id=checkout_branchcode]').val(jQuery(xml).find('checkout_branchcode').text())
			}
			if (jQuery(xml).find('checkout_directdebittext').length > 0) {
				jQuery('#' + pass_div + ' input[@id=checkout_directdebittext]').val(jQuery(xml).find('checkout_directdebittext').text())
			}
			if (jQuery(xml).find('checkout_voucher_number').length > 0) {
				jQuery('#' + pass_div + ' input[@id=checkout_voucher_number]').val(jQuery(xml).find('checkout_voucher_number').text())
			}
			if (jQuery(xml).find('checkout_voucher_value').length > 0) {
				jQuery('#' + pass_div + ' input[@id=checkout_voucher_value]').val(jQuery(xml).find('checkout_voucher_value').text())
			}
		}
	});
}

function checkoutQnA () {
    ask_qna();
    jQuery('#general_popup_box>div.popup_close_button').removeClass('popup_close_button');
}
//--></script>