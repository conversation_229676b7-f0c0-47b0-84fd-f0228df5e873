<?php
header("Cache-Control: no-cache");
header("Cache-Control: post-check=0,pre-check=0");
header("Cache-Control: max-age=0");
header("Pragma: no-cache");
header('Content-Type: text/xml');

include_once('includes/application_top.php');
include_once('includes/functions/custom_product_info.php');
include_once('includes/functions/customers_info_verification.php');
require_once(DIR_WS_CLASSES . 'order_total.php');
require_once(DIR_WS_CLASSES . 'order.php');
require_once(DIR_WS_CLASSES . 'log.php');
require_once(DIR_WS_CLASSES . 'payment.php');
require_once(DIR_WS_CLASSES . 'product.php');
require_once(DIR_WS_CLASSES . 'sc_shopping_cart.php');

$languages_id = isset($_SESSION['languages_id']) ? (int) $_SESSION['languages_id'] : '';

$action = isset($_REQUEST['action']) ? $_REQUEST['action'] : '';
$selection_id = isset($HTTP_GET_VARS['selection_id']) ? (int) $HTTP_GET_VARS['selection_id'] : '';
$custom_products_type_id = isset($HTTP_GET_VARS['custom_products_type_id']) ? (int) $HTTP_GET_VARS['custom_products_type_id'] : '';
$show_hop = isset($HTTP_GET_VARS['show_hop']) ? (int) $HTTP_GET_VARS['show_hop'] : '';
$product_id = isset($HTTP_GET_VARS['pid']) ? (int) $HTTP_GET_VARS['pid'] : '';
$sel_id = isset($HTTP_GET_VARS['sel_id']) ? (int) $HTTP_GET_VARS['sel_id'] : '';
$payment_info = isset($HTTP_GET_VARS['payment_info']) ? $HTTP_GET_VARS['payment_info'] : (isset($_SESSION['selected_payment_info']) ? $_SESSION['selected_payment_info'] : '');
$currency_code = isset($HTTP_GET_VARS['curr_code']) ? $HTTP_GET_VARS['curr_code'] : '';
$sel = isset($HTTP_GET_VARS['sel']) ? $HTTP_GET_VARS['sel'] : '';
list($index, $display_section) = tep_xmlhttp_get_index_and_type($_REQUEST['index']);

$_SESSION['selected_payment_info'] = $payment_info;

if (isset($_SESSION['language']) && tep_not_null($_SESSION['language'])) {
	if (file_exists(DIR_WS_LANGUAGES . $_SESSION['language'] . '.php')) {
		include_once(DIR_WS_LANGUAGES . $_SESSION['language'] . '.php');
	}

	if (file_exists(DIR_WS_LANGUAGES . $_SESSION['language'] . '/' . FILENAME_CUSTOM_PRODUCT_INFO)) {
		include_once(DIR_WS_LANGUAGES . $_SESSION['language'] . '/' . FILENAME_CUSTOM_PRODUCT_INFO);
	}

	if (file_exists(DIR_WS_LANGUAGES . $_SESSION['language'] . '/' . FILENAME_CHECKOUT_PAYMENT)) {
		include_once(DIR_WS_LANGUAGES . $_SESSION['language'] . '/' . FILENAME_CHECKOUT_PAYMENT);
	}
}

echo '<response>';
if (tep_not_null($action)) {
	if (!isset($_SERVER['HTTP_REFERER']) ||
		(strstr($_SERVER['HTTP_REFERER'], HTTP_SERVER) === FALSE && strstr($_SERVER['HTTP_REFERER'], HTTPS_SERVER) === FALSE)
	) {
		echo "You are not allowed to access from outside";
		echo '</response>';
		exit;
	}

	switch ($action) {
		case 'load_dtu_character_list':
			$error_flag = 1;
			$error_msg = ERROR_UNABLE_VALIDATE_TOP_UP_ACCOUNT;
			if (isset($_REQUEST['pid']) && (int) $_REQUEST['pid'] > 0 && (isset($_REQUEST['account']) && tep_not_null($_REQUEST['account'])) || (isset($_REQUEST['server']) && tep_not_null($_REQUEST['server']))) {
				$pid = (int) $_REQUEST['pid'];
				$account = $_REQUEST['account'];
				$server = $_REQUEST['server'];

				$products_select_sql = "SELECT products_bundle, products_bundle_dynamic 
										FROM " . TABLE_PRODUCTS . "
										WHERE products_id = '" . $pid . "'";
				$products_result_sql = tep_db_query($products_select_sql);
				$product_info_row = tep_db_fetch_array($products_result_sql);

				if ($product_info_row['products_bundle'] == 'yes' || $product_info_row['products_bundle_dynamic'] == 'yes') {
					$bundle_select_sql = "	SELECT pp.products_id
                                            FROM " . TABLE_PUBLISHERS_PRODUCTS . " AS pp 
                                            INNER JOIN " . TABLE_PRODUCTS_DELIVERY_INFO . " AS pdi
                                                ON pdi.products_id = pp.products_id 
                                            INNER JOIN " . TABLE_PRODUCTS_BUNDLES . " AS pb
                                                ON pp.products_id=pb.subproduct_id
                                            WHERE pb.bundle_id = '" . tep_get_prid($pid) . "'
                                                AND pdi.products_delivery_mode_id = '6'
                                            LIMIT 1";
					$bundle_result_sql = tep_db_query($bundle_select_sql);
					$bundle_row = tep_db_fetch_array($bundle_result_sql);
					$pid = $bundle_row['products_id'];
				}

				$publishers_select_sql = "	SELECT pg.publishers_id, pg.publishers_games_id, pg.publishers_game 
											FROM " . TABLE_PUBLISHERS_GAMES . " AS pg
											INNER JOIN " . TABLE_PUBLISHERS_PRODUCTS . " AS pp
												ON pp.publishers_games_id = pg.publishers_games_id
											WHERE pp.products_id = '" . $pid . "'";
				$publishers_result_sql = tep_db_query($publishers_select_sql);
				if ($publishers_row = tep_db_fetch_array($publishers_result_sql)) {
					include_once(DIR_WS_CLASSES . 'direct_topup.php');
					$direct_topup_obj = new direct_topup();

					$games_acc_array = array();
					$games_acc_array['account'] = $account;
					$games_acc_array['server'] = $server;
					$games_acc_array['game'] = $publishers_row['publishers_game'];
					$games_acc_array['publishers_games_id'] = $publishers_row['publishers_games_id'];

					$characters_array = $direct_topup_obj->get_character_list($publishers_row['publishers_id'], $games_acc_array, $curl_response_array);

					if (isset($curl_response_array['result_code'])) {
						if ($curl_response_array['result_code'] == '2000') {
							if (count($characters_array) > 0) {
								$error_flag = 0;
								echo "<characters>";
								foreach ($characters_array as $characters_id_loop => $characters_data_loop) {
									echo "<character id='" . $characters_id_loop . "'><![CDATA[" . $characters_data_loop . "]]></character>";
								}
								echo "</characters>";
							} else {
								$error_flag = 1;
								$error_msg = ERROR_DTU_NO_CHARACTERS_IN_LIST;
							}
						} else if ($curl_response_array['result_code'] == '1006') {
							$error_flag = 1;
							$error_msg = ERROR_DTU_NO_CHARACTERS_IN_LIST;
						} else if ($curl_response_array['result_code'] == '1007') {
							$error_flag = 1;
							$error_msg = ERROR_DTU_NO_CHARACTERS_IN_LIST;
						}
					}
				}
			}

			if ($error_flag) {
				echo "<error_message><![CDATA[" . $error_msg . "]]></error_message>";
				echo "<error><![CDATA[1]]></error>";
			} else {
				echo "<error><![CDATA[0]]></error>";
			}
			break;
		case 'products_info':

			$op_popup = 'http://kb.offgamers.com/zhcn/category/my-account/wor-token/';
			if ($languages_id == 1) {
				$op_popup = 'http://kb.offgamers.com/en/category/my-account/wor-token/';
			}

			$error_flag = true;
			$message = '';
			$delivery_time = "-";
			$products_description = "";

			$delivery_methods_array = array();

			$products_add_cart_qty_array = array();
			for ($pre_set_qty_count = 1; $pre_set_qty_count <= 20; $pre_set_qty_count++) {
				$products_add_cart_qty_array[] = array("id" => $pre_set_qty_count, "text" => $pre_set_qty_count);
			}

			$display_html = '	<table border="0" cellspacing="0" cellpadding="0" width="100%">';

			if (isset($_REQUEST['pID']) && (int) $_REQUEST['pID'] > 0) {
				$display_html .= '	<tr>
										<td colspan="2" style="padding-left:20px;">
											<div id="ecQtyText" class="ecQtyText_' . $index . '" style="height: 28px;width:250px;">
												<div style="float: left; position: relative;">
													<div style="float:left;padding-top:5px;">
														' . ENTRY_QTY . '&nbsp;:&nbsp;
													</div>
													<div style="float:left;">
														<input type="text" name="buyqtytext" class="productListingDTUAftInput" id="buyqtytext_' . $index . '" maxlength="4" style="width: 40px; position: relative;" value="1" onFocus="this.value=\'\'" onKeyDown="submit_listing_qty(event, \'' . $pID . '\', \'' . $index . '\', jQuery(\'td#td_cart_info_' . $index . ' input#buyqtytext_' . $index . '\').val())" onblur="javascript:update_listing_qty(\'' . $pID . '\', \'' . $index . '\');" onKeyPress="return numbersOnly(this, event)"/>
													</div>
												</div>
											</div>
										</td>
									</tr>
									<tr id="tr_qty_error_msg_' . $index . '" style="display:none;" class="tr_error_msg_' . $index . '">
										<td valign="bottom" height="25px" colspan="2" style="padding-left:20px;">
											<div>
												<div style="float:left;padding-top:3px;">' . tep_image(DIR_WS_ICONS . 'warning.png') . '</div>
												<div style="float:left;padding-top:3px;padding-left:5px;width:225px;" id="div_error_msg" class="redIndicator"></div>
											</div>
										</td>
									</tr>
									<tr>
										<td colspan="2">' . tep_draw_separator('pixel_trans.gif', '100%', '4') . '</td>
									</tr>
									<tr>
										<td colspan="2" style="border-bottom: 1px dotted rgb(204, 204, 204);">' . tep_draw_separator('pixel_trans.gif', '100%', '4') . '</td>
									</tr>
									<tr>
										<td colspan="2">' . tep_draw_separator('pixel_trans.gif', '100%', '4') . '</td>
									</tr>';

				$pID = (int) $_REQUEST['pID'];

				$cust_select_sql = "	SELECT customers_discount 
			   							FROM " . TABLE_CUSTOMERS . " 
			   							WHERE customers_id = '" . (int) $customer_id . "'";
				$cust_result_sql = tep_db_query($cust_select_sql);
				$cust_row_sql = tep_db_fetch_array($cust_result_sql);

				$cust_group_select_sql = "	SELECT customers_groups_name 
											FROM " . TABLE_CUSTOMERS_GROUPS . " 
											WHERE customers_groups_id = '" . tep_db_input($customers_groups_id) . "'";
				$cust_group_result_sql = tep_db_query($cust_group_select_sql);
				$cust_group_row_sql = tep_db_fetch_array($cust_group_result_sql);

				$products_select_sql = "SELECT products_id, products_bundle, products_bundle_dynamic, products_price, products_main_cat_id, custom_products_type_id 
										FROM " . TABLE_PRODUCTS . "
										WHERE products_id = '" . $pID . "'";
				$products_result_sql = tep_db_query($products_select_sql);
				$product_info_row = tep_db_fetch_array($products_result_sql);

				$custom_product_type_id = tep_get_custom_product_type($pID);
				$product_obj = new product($product_info_row['products_main_cat_id'], $custom_product_type_id);

				$status_info = $product_obj->get_plain_products_status($product_info_row['products_id'], 1);

				$show_it = $status_info['show'];
				$special_price = tep_get_products_special_price($pID);
				$normal_price = $product_info_row['products_price'];

				$customers_groups_info_array = tep_get_customer_group_discount($customer_id, $pID, 'product');
				$customers_groups_discount = $customers_groups_info_array['cust_group_discount'];

				$total_customer_rebate = $customers_groups_info_array['cust_group_rebate'];
				$total_customer_discount = $cust_row_sql['customers_discount'] + $customers_groups_discount;

				$delivery_time = (tep_not_null(tep_get_eta_string($status_info['pre_order_time'])) ? tep_get_eta_string($status_info['pre_order_time']) : '-');
				$products_description = product::get_products_info($pID, 'products_description', $languages_id, $default_languages_id);

				$error_flag = false;

				$gst_popup = '/popup/gst.html';
				if ($languages_id == 2 || $languages_id == 3) {
					$gst_popup = '/popup/gst_cn.html';
				}

				$display_price_column_html = '';
				$display_rebate_column_html = (tep_not_null($_SESSION['RegionGST']['tax_title']) ? '<a href="javascript:;" onclick="window.open(\'' . tep_href_link($gst_popup) . '\',\'mywindow\',\'width=400,height=180,scrollbars=yes\');"><div id="cart_gst_' . $index . '" style="font-weight: normal;">' . sprintf(WARNING_PRODUCT_PRICE_WITHOUT_GST, $_SESSION['RegionGST']['tax_title']) . '</div></a>' : '');

				if ($show_it == 2 && abs(PRE_ORDER_DISCOUNT)) {
					$display_price_column_html = '<div id="cart_price_' . $index . '" style="font-size:14px;font-weight:bold;">' . $currencies->display_price($pID, $product_info_row['products_price'], tep_get_tax_rate($product_info_row['products_tax_class_id']), 1, true) . '</div>' . $display_rebate_column_html;
					if ($cust_group_row_sql['customers_groups_name']) {
						if (abs($total_customer_rebate)) {
							$display_price_column_html .= '<a href="' . $op_popup . '" target="_blank" ><div id="cart_op_' . $index . '">' . $cust_group_row_sql['customers_groups_name'] . ': ' . $currencies->rebate_point . ' ' . TEXT_OP . ' ' . TEXT_REBATE . '</div></a>';
						} else {
							$display_price_column_html .= '<a href="' . $op_popup . '" target="_blank" ><div id="cart_op_' . $index . '"></div></a>';
						}
					}
					$display_price_column_html .= '<div id="cart_normal_price_' . $index . '"></div>';
				} else {
					if ($cust_group_row_sql['customers_groups_name']) {
						if (abs($total_customer_discount)) {
							$display_price_column_html .= '<div id="cart_price_' . $index . '" style="font-size:14px;font-weight:bold;">' . $currencies->display_price($pID, $product_info_row['products_price'], tep_get_tax_rate($product_info_row['products_tax_class_id']), 1, false) . '</div>';
							if (in_array($customers_groups_id, $product_obj->display_original_price_group_array)) {
								$display_price_column_html .= '<div id="cart_normal_price_' . $index . '">(<strike>' . $currencies->display_price_original($pID, $normal_price, tep_get_tax_rate($product_info_row['products_tax_class_id']), 1) . '</strike> ' . TEXT_GENERAL . ')</div>';
							} else {
								$display_price_column_html .= '<div id="cart_normal_price_' . $index . '"></div>';
							}
						} else {
							$display_price_column_html .= '<div id="cart_price_' . $index . '" style="font-size:14px;font-weight:bold;">' . $currencies->display_price($pID, $normal_price, tep_get_tax_rate($product_info_row['products_tax_class_id'])) . '</div>';
							$display_price_column_html .= '<div id="cart_normal_price_' . $index . '"></div>';
						}
						$display_price_column_html .= $display_rebate_column_html;

						if (abs($total_customer_rebate)) {
							$display_price_column_html .= '<a href="' . $op_popup . '" target="_blank" ><div id="cart_op_' . $index . '">' . $cust_group_row_sql['customers_groups_name'] . ': ' . $currencies->rebate_point . ' ' . TEXT_OP . ' ' . TEXT_REBATE . '</div></a>';
						} else {
							$display_price_column_html .= '<a href="' . $op_popup . '" target="_blank" ><div id="cart_op_' . $index . '"></div></a>';
						}
					} else {
						$display_price_column_html .= '<div id="cart_price_' . $index . '" style="font-size:14px;font-weight:bold;">' . $currencies->display_price($pID, $normal_price, tep_get_tax_rate($product_info_row['products_tax_class_id'])) . '</div>';
						$display_price_column_html .= '<div id="cart_normal_price_' . $index . '"></div>';
						$display_price_column_html .= $display_rebate_column_html;
						$display_price_column_html .= '<a href="' . $op_popup . '" target="_blank" ><div id="cart_op_' . $index . '"></div></a>';
					}
				}

				// cause $display_html used '', so this start with ""
				$side_dtu_tooltips_html = "<b>" . TEXT_INFO_DELIVERY_SEND_TO_ACCOUNT . "</b><br>" . TEXT_INFO_DELIVERY_SEND_TO_ACCOUNT_DESCRIPTION . "<br><br>";
				$side_dtu_tooltips_html .= "<b>" . TEXT_INFO_DELIVERY_DIRECT_TOP_UP . "</b><br>" . TEXT_INFO_DELIVERY_DIRECT_TOP_UP_DESCRIPTION . "<br><br><br>";

				$display_html .= '		<tr>
											<td colspan="2" style="padding-left:20px;"><div style="float:left;padding-top:2px;">' . ENTRY_DELIVERY_METHOD . ': </div><div style="float: left; margin-left: 2px; cursor:pointer;">' . tep_image(DIR_WS_ICONS . 'help-small.png', '', '', '', ' class="ogm_dtu_tooltips" style="vertical-align:middle;cursor:pointer;" ogm_content="' . $side_dtu_tooltips_html . '" ') . '</div></td>
										</tr>
										<tr id="tr_top_up_error_msg_' . $index . '" style="display:none;" class="tr_error_msg_' . $index . '">
											<td valign="bottom" height="25px" colspan="2" style="padding-left:20px;">
												<div>
													<div style="float:left;padding-top:3px;">' . tep_image(DIR_WS_ICONS . 'error-small.png') . '</div>
													<div style="float:left;padding-top:3px;padding-left:5px;width:200px;" id="div_error_msg" class="redIndicator"></div>
												</div>
											</td>
										</tr>
										<tr>
											<td colspan="2">' . tep_draw_separator('pixel_trans.gif', '100%', '4') . '</td>
										</tr>';

				$product_delivery_mode = product::get_product_delivery_mode($pID);
				foreach ($product_delivery_mode as $product_delivery_mode_id_loop) {
					$product_delivery_mode_array[$product_delivery_mode_id_loop] = array();
				}

				$products_delivery_mode_select_sql = "	SELECT pdm.products_delivery_mode_id, pdm.products_delivery_mode_title
														FROM " . TABLE_PRODUCTS_DELIVERY_MODE . " AS pdm
														WHERE pdm.products_delivery_mode_id IN ('" . implode("','", array_keys($product_delivery_mode_array)) . "')";
				$products_delivery_mode_result_sql = tep_db_query($products_delivery_mode_select_sql);
				while ($products_delivery_mode_row = tep_db_fetch_array($products_delivery_mode_result_sql)) {
					$product_delivery_mode_array[$products_delivery_mode_row['products_delivery_mode_id']] = $products_delivery_mode_row['products_delivery_mode_title'];
				}

				if (tep_not_null($display_section) && count($product_delivery_mode_array)) {
					switch ($display_section) {
						case 'd' : // DTU
							if (isset($product_delivery_mode_array['6'])) {
								$product_delivery_mode_array = array('6' => $product_delivery_mode_array['6']);
							}
							break;
						case 'p' : // Promotion
							break;
						case 'g' : // General
							unset($product_delivery_mode_array['6']);
							break;
					}
				}

				if (count($product_delivery_mode_array)) {
					foreach ($product_delivery_mode_array as $product_delivery_mode_id_loop => $product_delivery_mode_data_loop) {
						$display_delivery_mode_label = '';
						switch ($product_delivery_mode_id_loop) {
							case '5':
								$display_delivery_mode_label = TEXT_INFO_DELIVERY_SEND_TO_ACCOUNT;
								break;
							case '6':
								$display_delivery_mode_label = TEXT_INFO_DELIVERY_DIRECT_TOP_UP;
								break;
							case '7':
								$display_delivery_mode_label = TEXT_INFO_DELIVERY_IN_STORE_PICKUP;
								break;
							default:
								$display_delivery_mode_label = $product_delivery_mode_data_loop;
								break;
						}

						$display_html .= '		<tr>
													<td colspan="2"  style="padding-left:20px;height: 28px;width:250px;">';
						switch ($product_delivery_mode_id_loop) {
							case '5':
								$display_html .= '	<table width="100%">
														<tr>
															<td colspan="2" nowrap class="delivery_mode_bg delivery_mode_bg_' . $pID . ' delivery_mode_bg_selected" pid="' . $pID . '" mid="5" id="td_delivery_mode_' . $pID . '_5">
																<div style="clear:left;height:15px;" onclick="jQuery(\'#rd_delivery_mode_' . $index . '_' . $product_delivery_mode_id_loop . '\').attr(\'checked\', true);select_delivery_mode(\'' . $pID . '\', \'5\', \'' . $index . '\')">
																	<div style="padding-left:3px;float:left;"><input type="radio" name="rd_delivery_mode_' . $index . '" id="rd_delivery_mode_' . $index . '_5" value="5" checked></div>
																	<div style="padding-left:3px;float:left;padding-top:2px;">' . TEXT_INFO_DELIVERY_SEND_TO_ACCOUNT . '</div>
																</div>
															</td>
														</tr>
													</table>';
								break;
							case '6':
								include_once(DIR_WS_CLASSES . 'direct_topup.php');
								$direct_topup_obj = new direct_topup();
								$main_product_id = $pID;
								if ($product_info_row['products_bundle'] == 'yes' || $product_info_row['products_bundle_dynamic'] == 'yes') {
									$bundle_select_sql = "	SELECT pp.products_id
															FROM " . TABLE_PUBLISHERS_PRODUCTS . " AS pp 
															INNER JOIN " . TABLE_PRODUCTS_DELIVERY_INFO . " AS pdi
																ON pdi.products_id = pp.products_id 
															INNER JOIN " . TABLE_PRODUCTS_BUNDLES . " AS pb
																ON pp.products_id=pb.subproduct_id
															WHERE pb.bundle_id = '" . tep_get_prid($pID) . "'
																AND pdi.products_delivery_mode_id = '6'
															LIMIT 1";
									$bundle_result_sql = tep_db_query($bundle_select_sql);
									$bundle_row = tep_db_fetch_array($bundle_result_sql);
									$game_input_array = $direct_topup_obj->get_game_input($bundle_row['products_id']);
									$main_product_id = $bundle_row['products_id'];
								} else {
									$game_input_array = $direct_topup_obj->get_game_input($main_product_id);
								}

								$display_html .= '	<table width="100%">
														<tr>
															<td colspan="2" nowrap class="delivery_mode_bg delivery_mode_bg_' . $pID . (count($delivery_methods_array) ? '' : ' delivery_mode_bg_selected') . '" pid="' . $pID . '" mid="' . $product_delivery_mode_id_loop . '" id="td_delivery_mode_' . $pID . '_' . $product_delivery_mode_id_loop . '">
																<div style="clear:left;height:15px;"onclick="jQuery(\'#rd_delivery_mode_' . $index . '_' . $product_delivery_mode_id_loop . '\').attr(\'checked\', true);select_delivery_mode(\'' . $pID . '\', \'' . $product_delivery_mode_id_loop . '\', \'' . $index . '\')" >
																	<div style="padding-left:3px;float:left;"><input type="radio" name="rd_delivery_mode_' . $index . '" id="rd_delivery_mode_' . $index . '_' . $product_delivery_mode_id_loop . '" value="' . $product_delivery_mode_id_loop . '" ' . (count($delivery_methods_array) ? '' : ' checked ') . '></div>
																	<div style="padding-left:3px;float:left;padding-top:2px;">' . $display_delivery_mode_label . '</div>
																</div>
																<div class="delivery_mode_bg_content" style="clear:left;padding:5px 5px 5px 7px;' . (count($delivery_methods_array) ? 'display:none;' : '') . '">
																	<table id="top_up_input" border="0" cellspacing="0" cellpadding="0" width="100%">';
								if (count($game_input_array)) {
									foreach ($game_input_array as $game_input_key_loop => $game_input_data_loop) {
										$display_html .= '				<tr>
																			<td width="*%">';
										switch ($game_input_data_loop['top_up_info_key']) {
											case 'server':
												// server list
												$servers_select_sql = "	SELECT pg.publishers_server
																		FROM " . TABLE_PUBLISHERS_GAMES . " AS pg
																		INNER JOIN " . TABLE_PUBLISHERS_PRODUCTS . " AS pp
																			ON pg.publishers_games_id = pp.publishers_games_id
																		WHERE pp.products_id = '" . (int) $main_product_id . "'";
												$servers_result_sql = tep_db_query($servers_select_sql);
												$servers_row = tep_db_fetch_array($servers_result_sql);
												$servers_tmp_array = json_decode($servers_row['publishers_server'], 1);
												$servers_array = array();
												$servers_array[] = array('id' => '',
													'text' => $game_input_data_loop['top_up_info_display']);
												if (isset($servers_tmp_array)) {
													foreach ($servers_tmp_array as $servers_id_loop => $server_name_loop) {
														$servers_array[] = array('id' => $servers_id_loop,
															'text' => $server_name_loop);
													}
												}
												$display_html .= tep_draw_pull_down_menu("game_info[" . $game_input_key_loop . "]", $servers_array, '', ' default_text="' . $game_input_data_loop['top_up_info_display'] . '" onfocus="jQuery(this).removeClass(\'productListingDTUPreInput\').addClass(\'productListingDTUAftInput\')" onblur="if(this.value==\'\') {jQuery(this).removeClass(\'productListingDTUAftInput\').addClass(\'productListingDTUPreInput\')}else{jQuery(this).removeClass(\'productListingDTUPreInput\').addClass(\'productListingDTUAftInput\')}" class="productListingDTUPreInput dtu_customer_input_game_info" ');
												break;
											case 'account_platform':
												// account platform
												$account_platform_tmp_array = $direct_topup_obj->get_account_platform($main_product_id);

												$account_platform_array = array();
												$account_platform_array[] = array('id' => '',
													'text' => $game_input_data_loop['top_up_info_display']);
												if (isset($account_platform_tmp_array)) {
													foreach ($account_platform_tmp_array as $account_platform_id_loop => $account_platform_name_loop) {
														$account_platform_array[] = array('id' => $account_platform_id_loop,
															'text' => $account_platform_name_loop);
													}
												}
												$display_html .= tep_draw_pull_down_menu("game_info[" . $game_input_key_loop . "]", $account_platform_array, '', ' default_text="' . $game_input_data_loop['top_up_info_display'] . '" onfocus="jQuery(this).removeClass(\'productListingDTUPreInput\').addClass(\'productListingDTUAftInput\')" onblur="if(this.value==\'\') {jQuery(this).removeClass(\'productListingDTUAftInput\').addClass(\'productListingDTUPreInput\')}else{jQuery(this).removeClass(\'productListingDTUPreInput\').addClass(\'productListingDTUAftInput\')}" class="productListingDTUPreInput dtu_customer_input_game_info" ');
												break;
											case 'character':
												if ($direct_topup_obj->character_is_sync($main_product_id)) {
													$character_list_array = array();
													$character_list_array[] = array('id' => '',
														'text' => $game_input_data_loop['top_up_info_display']);
													$display_html .= tep_draw_pull_down_menu("game_info[" . $game_input_key_loop . "]", $character_list_array, '', ' default_text="' . $game_input_data_loop['top_up_info_display'] . '" onfocus="load_character_list(this, \'' . $pID . '\', \'' . $product_delivery_mode_id_loop . '\', \'' . $index . '\');jQuery(this).removeClass(\'productListingDTUPreInput\').addClass(\'productListingDTUAftInput\')" onblur="if(this.value==\'\') {jQuery(this).removeClass(\'productListingDTUAftInput\').addClass(\'productListingDTUPreInput\')}else{jQuery(this).removeClass(\'productListingDTUPreInput\').addClass(\'productListingDTUAftInput\')}" class="productListingDTUPreInput" ');
												} else {
													$display_html .= tep_draw_input_field("game_info[" . $game_input_key_loop . "]", $game_input_data_loop['top_up_info_display'], ' maxlength="64" size="20" onfocus="this.value=\'\';jQuery(this).removeClass(\'productListingDTUPreInput\').addClass(\'productListingDTUAftInput\')" onblur="if(this.value==\'\') {this.value=jQuery(this).attr(\'default_text\');jQuery(this).removeClass(\'productListingDTUAftInput\').addClass(\'productListingDTUPreInput\')}else{jQuery(this).removeClass(\'productListingDTUPreInput\').addClass(\'productListingDTUAftInput\')}" default_text="' . $game_input_data_loop['top_up_info_display'] . '" class="productListingDTUPreInput dtu_customer_input_game_info" ');
												}
												break;
											default: // account
												$display_html .= tep_draw_input_field("game_info[" . $game_input_key_loop . "]", $game_input_data_loop['top_up_info_display'], ' maxlength="64" size="18" onfocus="this.value=\'\';jQuery(this).removeClass(\'productListingDTUPreInput\').addClass(\'productListingDTUAftInput\')" onblur="if(this.value==\'\') {this.value=jQuery(this).attr(\'default_text\');jQuery(this).removeClass(\'productListingDTUAftInput\').addClass(\'productListingDTUPreInput\')}else{jQuery(this).removeClass(\'productListingDTUPreInput\').addClass(\'productListingDTUAftInput\')}" default_text="' . $game_input_data_loop['top_up_info_display'] . '" class="productListingDTUPreInput dtu_customer_input_game_info" ');
												if ($direct_topup_obj->retype_account($main_product_id)) {
													$display_html .= ' ' . tep_draw_input_field("game_info[" . $game_input_key_loop . "_2]", TEXT_RETYPE . $game_input_data_loop['top_up_info_display'], ' maxlength="64" size="18" onfocus="this.value=\'\';jQuery(this).removeClass(\'productListingDTUPreInput\').addClass(\'productListingDTUAftInput\')" onblur="if(this.value==\'\') {this.value=jQuery(this).attr(\'default_text\');jQuery(this).removeClass(\'productListingDTUAftInput\').addClass(\'productListingDTUPreInput\')}else{jQuery(this).removeClass(\'productListingDTUPreInput\').addClass(\'productListingDTUAftInput\')}" default_text="' . TEXT_RETYPE . $game_input_data_loop['top_up_info_display'] . '" class="productListingDTUPreInput dtu_customer_input_game_info" ');
												}
												break;
										}
										$display_html .='					</td>
																		</tr>
																		<tr>
																			<td>' . tep_draw_separator('pixel_trans.gif', '100%', '5') . '</td>
																		</tr>';
									}
								}

								$display_html .='					</table>
																</div>
															</td>
														</tr>
													</table>';
								break;
							case '7':
								$display_html .= '	<table width="100%">
														<tr>
															<td colspan="2" nowrap class="delivery_mode_bg delivery_mode_bg_' . $pID . (count($delivery_methods_array) ? '' : ' delivery_mode_bg_selected') . '" pid="' . $pID . '" mid="' . $product_delivery_mode_id_loop . '" id="td_delivery_mode_' . $pID . '_' . $product_delivery_mode_id_loop . '">
																<div style="clear:left;height:15px;" onclick="jQuery(\'#rd_delivery_mode_' . $index . '_' . $product_delivery_mode_id_loop . '\').attr(\'checked\', true);select_delivery_mode(\'' . $pID . '\', \'' . $product_delivery_mode_id_loop . '\', \'' . $index . '\')">
																	<div style="padding-left:3px;float:left;"><input type="radio" name="rd_delivery_mode_' . $index . '" id="rd_delivery_mode_' . $index . '_' . $product_delivery_mode_id_loop . '" value="' . $product_delivery_mode_id_loop . '"  ' . (count($delivery_methods_array) ? '' : ' checked ') . '></div>
																	<div style="padding-left:3px;float:left;padding-top:2px;">' . $display_delivery_mode_label . '</div>
																</div>
															</td>
														</tr>
													</table>';
								break;
						}
						$display_html .= '			</td>
												</tr>
												<tr>
													<td colspan="2">' . tep_draw_separator('pixel_trans.gif', '100%', '8') . '</td>
												</tr>';

						$delivery_methods_array[$product_delivery_mode_id_loop] = $display_delivery_mode_label;
					}
				} else if ($product_info_row['products_bundle'] != 'yes' && $product_info_row['products_bundle_dynamic'] != 'yes') {
					$delivery_methods_array[5] = TEXT_INFO_DELIVERY_SEND_TO_ACCOUNT; // default if not set
					$display_html .= '	<tr>
											<td colspan="2" style="padding-left:20px;">
												<table width="100%">
													<tr>
														<td colspan="2" nowrap class="delivery_mode_bg delivery_mode_bg_' . $pID . ' delivery_mode_bg_selected" pid="' . $pID . '" mid="5" id="td_delivery_mode_' . $pID . '_5">
															<div style="clear:left;height:15px;"  onclick="jQuery(\'#rd_delivery_mode_' . $index . '_5\').click();">
																<div style="padding-left:3px;float:left;"><input type="radio" name="rd_delivery_mode_' . $index . '" id="rd_delivery_mode_' . $index . '_5" value="5" onclick="select_delivery_mode(\'' . $pID . '\', \'5\', \'' . $index . '\')" checked></div>
																<div style="padding-left:3px;float:left;padding-top:2px;">' . TEXT_INFO_DELIVERY_SEND_TO_ACCOUNT . '</div>
															</div>
															<div class="delivery_mode_bg_content" class="wrapword" style="clear:left;padding:5px 5px 5px 7px;">' . TEXT_INFO_VIEW_CODE_IN_ACCOUNT_HISTORY . '</div>
														</td>
													</tr>
												</table>
											</td>
										</tr>';
				}

				$display_button_html = '';
				$first_button_show = 0;

				$cpt_id = tep_get_custom_product_type($pID);

				foreach ($delivery_methods_array as $delivery_methods_id_loop => $delivery_methods_data_loop) {
					$display_button_html .= '<div id="div_add_cart_button_' . $delivery_methods_id_loop . '_' . $index . '" class="div_add_cart_button_' . $index . '" style="' . ($first_button_show ? 'display:none;' : '' ) . '">';
					switch ($delivery_methods_id_loop) {
						case '5':
						case '7':
							if ($show_it == 0) {
								$display_button_html .= tep_image_button2('gray', 'javascript:void(0);', IMAGE_BUTTON_OUT_OF_STOCK, 130, ' id="btn_direct_topup_insert_cart_' . $index . '" style="width:123px;" ');
							} else if ($show_it == 1 || $status_info['is_future_product'] != '1') {
								$display_button_html .= tep_image_button2('green', 'javascript:void(0);', IMAGE_BUTTON_IN_CART, 130, ' id="btn_direct_topup_insert_cart_' . $index . '" onclick="javascript:submit_cart(\'' . $pID . '\', \'' . $index . '\', \'' . $delivery_methods_id_loop . '\', \'\', \'' . $cpt_id . '\', \'' . $product_info_row['products_bundle'] . '\');" style="width:123px;" ');
							} else {
								$display_button_html .= tep_image_button2('green', 'javascript:void(0);', IMAGE_BUTTON_PRE_ORDER, 130, ' onclick="submit_cart(\'' . $pID . '\', \'' . $index . '\', \'' . $delivery_methods_id_loop . '\', \'\', \'' . $cpt_id . '\', \'' . $product_info_row['products_bundle'] . '\')" style="width:123px;" ');
							}
							break;
						case '6':
							$display_button_html .= tep_image_button2('green', 'javascript:void(0);', IMAGE_BUTTON_IN_CART, 130, ' id="btn_direct_topup_insert_cart_' . $index . '" onclick="submit_cart(\'' . (int) $pID . '\', \'' . $index . '\', \'' . $delivery_methods_id_loop . '\');" style="width:123px;" ');
							break;
					}
					$display_button_html .= '</div>';
					$first_button_show = 1;
				}

				$display_button_html .= tep_image_button2('gray', 'javascript:void(0)', IMAGE_BUTTON_IN_CART, 130, ' id="btn_gray_' . $index . '" style="display: none;width:123px;" ');
			} else {
				$error_flag = false;
				$display_button_html = tep_image_button2('green', 'javascript:void(0)', IMAGE_BUTTON_IN_CART, 130, ' onclick="add_empty_product(\'' . $index . '\');" ');
			}

			if (isset($_REQUEST['pID']) && (int) $_REQUEST['pID'] > 0) {
				$display_html .= '	<tr>
										<td colspan="2" style="border-top: 1px dotted rgb(204, 204, 204);">' . tep_draw_separator('pixel_trans.gif', '100%', '8') . '</td>
									</tr>
									<tr>
										<td colspan="2" style="padding-left:20px;">' . $display_price_column_html . '</td>
									</tr>';
			}

			$display_html .= '		<tr><td colspan="2">' . tep_draw_separator('pixel_trans.gif', '100%', '12') . '</td></tr>
									<tr>
										<td colspan="2"  style="padding-left:20px;text-align:left;">' . $display_button_html . '</td>
									</tr>
								</table>';

			$delivery_mode_icons_array = array();
			if (count($delivery_methods_array)) {
				foreach ($delivery_methods_array as $delivery_methods_id_loop => $delivery_methods_data_loop) {
					switch ($delivery_methods_id_loop) {
						case '5':
							$delivery_mode_icons_array[] = tep_image(DIR_WS_ICONS . 'icon_delivery_mode_ogm_account.gif', '', '', '', ' class="ogm_tooltips" style="vertical-align:middle;cursor:pointer;" ogm_title="' . TEXT_INFO_DELIVERY_SEND_TO_ACCOUNT . '" ogm_content="' . TEXT_INFO_DELIVERY_SEND_TO_ACCOUNT_DESCRIPTION . '"');
							break;
						case '6':
							$delivery_mode_icons_array[] = tep_image(DIR_WS_ICONS . 'icon_delivery_mode_topup.gif', '', '', '', ' class="ogm_tooltips" style="vertical-align:middle;cursor:pointer;" ogm_title="' . TEXT_INFO_DELIVERY_DIRECT_TOP_UP . '" ogm_content="' . TEXT_INFO_DELIVERY_DIRECT_TOP_UP_DESCRIPTION . '"');
							break;
						case '7':
							$delivery_mode_icons_array[] = tep_image(DIR_WS_ICONS . 'icon_delivery_mode_pickup.gif', '', '', '', ' class="ogm_tooltips" style="vertical-align:middle;cursor:pointer;" ogm_title="' . TEXT_INFO_DELIVERY_IN_STORE_PICKUP . '" ogm_content="' . TEXT_INFO_DELIVERY_IN_STORE_PICKUP_DESCRIPTION . '"');
							break;
						default:
							break;
					}
				}
			}
			echo "<delivery_methods><![CDATA[" . (count($delivery_mode_icons_array) ? implode("&nbsp;&nbsp;", $delivery_mode_icons_array) : '-') . "]]></delivery_methods>";
			echo "<delivery_time><![CDATA[" . $delivery_time . "]]></delivery_time>";
			echo "<description><![CDATA[" . (tep_not_empty($products_description) ? 1 : 0) . "]]></description>";
			echo "<html><![CDATA[" . $display_html . "]]></html>";
			echo "<error><![CDATA[" . ($error_flag ? 1 : 0) . "]]></error>";
			echo "<message><![CDATA[" . $message . "]]></message>";
			break;
		case 'update_pm_cust_info':
			$error = false;
			$error_message = '';
			$updated_data_array = array();
			if (isset($_REQUEST['identify_number'])) {
				if (!tep_session_is_registered('identify_number')) {
					tep_session_register('identify_number');
				}
				$_SESSION['identify_number'] = trim($_REQUEST['identify_number']);
				$updated_data_array['identify_number'] = $_SESSION['identify_number'];
			}

			if (isset($_REQUEST['telephone_number'])) {
				if (!tep_session_is_registered('customers_telephone')) {
					tep_session_register('customers_telephone');
				}
				$telephone = $_REQUEST['telephone_number'];
				if (substr($telephone, strlen($telephone) - 4, 4) == '****') {
					//
				} else {
					$customer_log_select_sql = "SELECT c.customers_telephone, c.customers_country_dialing_code_id, ci.customers_info_changes_made, c.customers_email_address 
												FROM " . TABLE_CUSTOMERS . " AS c 
												INNER JOIN " . TABLE_CUSTOMERS_INFO . " AS ci 
													ON (c.customers_id = ci.customers_info_id) 
												WHERE customers_id='" . (int) $customer_id . "'";
					$customer_log_result_sql = tep_db_query($customer_log_select_sql);
					$customer_old_log_row = tep_db_fetch_array($customer_log_result_sql);
					$telephone = tep_db_prepare_input(preg_replace('/[^\d]/', '', $telephone));
					$telephone = tep_parse_telephone($telephone, $customer_old_log_row['customers_country_dialing_code_id'], 'id');
					if (strlen($telephone) < ENTRY_TELEPHONE_MIN_LENGTH) {
						$error = true;
						$error_message = ENTRY_TELEPHONE_NUMBER_ERROR;
					} else {
						$_SESSION['customers_telephone'] = trim($telephone);
					}
				}
				$updated_data_array['customers_telephone'] = tep_mask_telephone($_SESSION['customers_telephone']);
			}
			if (isset($_REQUEST['checkout_surname'])) {
				if (!tep_session_is_registered('checkout_surname')) {
					tep_session_register('checkout_surname');
				}
				$_SESSION['checkout_surname'] = trim($_REQUEST['checkout_surname']);
				$updated_data_array['checkout_surname'] = $_SESSION['checkout_surname'];
			}
			if (isset($_REQUEST['checkout_city'])) {
				if (!tep_session_is_registered('checkout_city')) {
					tep_session_register('checkout_city');
				}
				$_SESSION['checkout_city'] = trim($_REQUEST['checkout_city']);
				$updated_data_array['checkout_city'] = $_SESSION['checkout_city'];
			}
			if (isset($_REQUEST['checkout_zip'])) {
				if (!tep_session_is_registered('checkout_zip')) {
					tep_session_register('checkout_zip');
				}
				$_SESSION['checkout_zip'] = trim($_REQUEST['checkout_zip']);
				$updated_data_array['checkout_zip'] = $_SESSION['checkout_zip'];
			}

			if (isset($_REQUEST['checkout_street'])) {
				if (!tep_session_is_registered('checkout_street')) {
					tep_session_register('checkout_street');
				}
				$_SESSION['checkout_street'] = trim($_REQUEST['checkout_street']);
				$updated_data_array['checkout_street'] = $_SESSION['checkout_street'];
			}
			if (isset($_REQUEST['checkout_housenumber'])) {
				if (!tep_session_is_registered('checkout_housenumber')) {
					tep_session_register('checkout_housenumber');
				}
				$_SESSION['checkout_housenumber'] = trim($_REQUEST['checkout_housenumber']);
				$updated_data_array['checkout_housenumber'] = $_SESSION['checkout_housenumber'];
			}
			if (isset($_REQUEST['checkout_city'])) {
				if (!tep_session_is_registered('checkout_city')) {
					tep_session_register('checkout_city');
				}
				$_SESSION['checkout_city'] = trim($_REQUEST['checkout_city']);
				$updated_data_array['checkout_city'] = $_SESSION['checkout_city'];
			}

			if (isset($_REQUEST['checkout_accountname'])) {
				if (!tep_session_is_registered('checkout_accountname')) {
					tep_session_register('checkout_accountname');
				}
				$_SESSION['checkout_accountname'] = trim($_REQUEST['checkout_accountname']);
				$updated_data_array['checkout_accountname'] = $_SESSION['checkout_accountname'];
			}
			if (isset($_REQUEST['checkout_accountnumber'])) {
				if (!tep_session_is_registered('checkout_accountnumber')) {
					tep_session_register('checkout_accountnumber');
				}
				$_SESSION['checkout_accountnumber'] = trim($_REQUEST['checkout_accountnumber']);
				$updated_data_array['checkout_accountnumber'] = $_SESSION['checkout_accountnumber'];
			}
			if (isset($_REQUEST['checkout_bankcode'])) {
				if (!tep_session_is_registered('checkout_bankcode')) {
					tep_session_register('checkout_bankcode');
				}
				$_SESSION['checkout_bankcode'] = trim($_REQUEST['checkout_bankcode']);
				$updated_data_array['checkout_bankcode'] = $_SESSION['checkout_bankcode'];
			}
			if (isset($_REQUEST['checkout_branchcode'])) {
				if (!tep_session_is_registered('checkout_branchcode')) {
					tep_session_register('checkout_branchcode');
				}
				$_SESSION['checkout_branchcode'] = trim($_REQUEST['checkout_branchcode']);
				$updated_data_array['checkout_branchcode'] = $_SESSION['checkout_branchcode'];
			}
			if (isset($_REQUEST['checkout_directdebittext'])) {
				if (!tep_session_is_registered('checkout_directdebittext')) {
					tep_session_register('checkout_directdebittext');
				}
				$_SESSION['checkout_directdebittext'] = trim($_REQUEST['checkout_directdebittext']);
				$updated_data_array['checkout_directdebittext'] = $_SESSION['checkout_directdebittext'];
			}
			if (isset($_REQUEST['checkout_voucher_number'])) {
				if (!tep_session_is_registered('checkout_voucher_number')) {
					tep_session_register('checkout_voucher_number');
				}
				$_SESSION['checkout_voucher_number'] = trim($_REQUEST['checkout_voucher_number']);
				$updated_data_array['checkout_voucher_number'] = $_SESSION['checkout_voucher_number'];
			}
			if (isset($_REQUEST['checkout_voucher_value'])) {
				if (!tep_session_is_registered('checkout_voucher_value')) {
					tep_session_register('checkout_voucher_value');
				}
				$_SESSION['checkout_voucher_value'] = trim($_REQUEST['checkout_voucher_value']);
				$updated_data_array['checkout_voucher_value'] = $_SESSION['checkout_voucher_value'];
			}

			echo '<result><![CDATA[' . ($error ? 0 : 1) . ']]></result>';
			echo '<message><![CDATA[' . $error_message . ']]></message>';
			if (count($updated_data_array)) {
				echo '<updated>';
				foreach ($updated_data_array as $updated_data_key => $updated_data_value) {
					echo '<' . $updated_data_key . '>' . $updated_data_value . '</' . $updated_data_key . '>';
				}
				echo '</updated>';
			}
			break;
		case 'top_up_input':
			$error = 1;
			$return_html = '';
			if (isset($_REQUEST['pid']) && (int) $_REQUEST['pid'] > 0) {
				$pid = (int) $_REQUEST['pid'];
				include_once(DIR_WS_CLASSES . 'direct_topup.php');
				$direct_topup_obj = new direct_topup();
				$game_input_array = $direct_topup_obj->get_game_input($pid);

				$products_name = tep_get_products_name($pid);

				$return_html .= '	<div style="padding: 10px 20px; float: left; width:400px;">
										<form id="frm_top_up_' . (int) $_REQUEST['pid'] . '">
										<table id="top_up_input" width="100%">
											<tr>
												<td colspan="2"><b class="largeFont">' . HEADER_DELIVERY_INFORMATION . '</b></div></td>
											</tr>
											<tr>
												<td colspan="2"><font style="font-size: 12px; font-weight: bold; vertical-align: top;">' . $products_name . '</font></td>
											</tr>
											<tr>
												<td colspan="2">' . ENTRY_DELIVERY_INFORMATION . '</td>
											</tr>';
				foreach ($game_input_array as $game_input_key_loop => $game_input_data_loop) {
					$return_html .= '		<tr>
												<td>' . $game_input_data_loop['top_up_info_display'] . ':</td>
												<td>';
					switch ($game_input_data_loop['top_up_info_key']) {
						case 'server':
							// server list
							$servers_select_sql = "	SELECT pg.publishers_server
													FROM " . TABLE_PUBLISHERS_GAMES . " AS pg
													INNER JOIN " . TABLE_PUBLISHERS_PRODUCTS . " AS pp
														ON pg.publishers_games_id = pp.publishers_games_id
													WHERE pp.products_id = '" . (int) $pid . "'";
							$servers_result_sql = tep_db_query($servers_select_sql);
							$servers_row = tep_db_fetch_array($servers_result_sql);
							$servers_tmp_array = json_decode($servers_row['publishers_server'], 1);
							$servers_array = array();
							$servers_array[] = array('id' => '',
								'text' => PULL_DOWN_DEFAULT);
							if (isset($servers_tmp_array)) {
								foreach ($servers_tmp_array as $servers_id_loop => $server_name_loop) {
									$servers_array[] = array('id' => $servers_id_loop,
										'text' => $server_name_loop);
								}
							}
							$return_html .= tep_draw_pull_down_menu("game_info[" . $game_input_key_loop . "]", $servers_array);
							break;
						case 'account_platform':
							// account platform
							$account_platform_tmp_array = $direct_topup_obj->get_account_platform($pid);

							$account_platform_array = array();
							$account_platform_array[] = array('id' => '',
								'text' => $game_input_data_loop['top_up_info_display']);
							if (isset($account_platform_tmp_array)) {
								foreach ($account_platform_tmp_array as $account_platform_id_loop => $account_platform_name_loop) {
									$account_platform_array[] = array('id' => $account_platform_id_loop,
										'text' => $account_platform_name_loop);
								}
							}
							$return_html .= tep_draw_pull_down_menu("game_info[" . $game_input_key_loop . "]", $account_platform_array, '', ' default_text="' . $game_input_data_loop['top_up_info_display'] . '" onfocus="jQuery(this).removeClass(\'productListingDTUPreInput\').addClass(\'productListingDTUAftInput\')" onblur="if(this.value==\'\') {jQuery(this).removeClass(\'productListingDTUAftInput\').addClass(\'productListingDTUPreInput\')}else{jQuery(this).removeClass(\'productListingDTUPreInput\').addClass(\'productListingDTUAftInput\')}" class="productListingDTUPreInput dtu_customer_input_game_info" ');
							break;
						case 'character':
						default: // account
							$return_html .= tep_draw_input_field("game_info[" . $game_input_key_loop . "]", '', ' maxlength="64" ');
							break;
					}

					$return_html .='			</td>
											</tr>';
				}

				$return_html .= '			<tr>
												<td>' . TEXT_QUANTITY . ':</td>
												<td><input type="text" name="txt_qty" size="5" value="1" style="text-align:right;" onKeyPress="return numbersOnly(this, event)"></td>
											</tr>
											<tr>
												<td></td>
												<td><div id="div_top_up_error_msg" style="color:red;"></div></td>
											</tr>
											<tr>
												<td colspan="2">' . tep_draw_separator('pixel_trans.gif', '100%', '3') . '</td>
											</tr>
											<tr><td colspan="2" class="smallText"><div class="dottedLine"><!-- --></div></td></tr>
											<tr>
												<td colspan="2">' . tep_draw_separator('pixel_trans.gif', '100%', '3') . '</td>
											</tr>
											<tr>
												<td></td>
												<td align="center">' . tep_div_button(2, IMAGE_BUTTON_IN_CART, 'javascript:void(0);', ' id="btn_form_submit_' . (int) $_REQUEST['pid'] . '" ') . '</td>
											</tr>
										</table>
										</form>
									</div>';
				$error = 1;
			}
			echo '<html><![CDATA[' . $return_html . ']]></html>';
			echo '<result><![CDATA[' . ($error ? 0 : 1) . ']]></result>';
			break;
		case 'direct_topup_insert_cart':
			echo "<result>";

			if (isset($_REQUEST['pid']) && (int) $_REQUEST['pid'] > 0) {

				if (!isset($_REQUEST['game_info']) || !isset($_REQUEST['game_info']['account']) || !tep_not_null($_REQUEST['game_info']['account'])) {
					echo "	<error_message>" . ERROR_INVALID_TOP_UP_ACCOUNT . "</error_message>";
				} else {
					if (isset($_REQUEST['game_info']['account_2']) && isset($_REQUEST['game_info']['account'])) {
						if (trim($_REQUEST['game_info']['account']) != trim($_REQUEST['game_info']['account_2'])) {
							echo "	<error_message>" . ERROR_INVALID_TOP_UP_ACCOUNT . "</error_message>";
							echo "</result>";
							break;
						}
					}
					$products_id = (int) $_REQUEST['pid'];
					$qty = (isset($_REQUEST['txt_qty']) && (int) $_REQUEST['txt_qty'] > 0 ? (int) $_REQUEST['txt_qty'] : 1);
					$_REQUEST['game_info']['account'] = trim($_REQUEST['game_info']['account']);
					include_once(DIR_WS_CLASSES . 'direct_topup.php');
					$direct_topup_obj = new direct_topup();

					$publishers_games_products_id = $products_id;
					if ($publishers_id = $direct_topup_obj->check_is_supported_by_direct_top_up($products_id)) {

						$products_select_sql = "SELECT products_bundle, products_bundle_dynamic
												FROM " . TABLE_PRODUCTS . "
												WHERE products_id = '" . $publishers_games_products_id . "'";
						$products_result_sql = tep_db_query($products_select_sql);
						if ($products_row = tep_db_fetch_array($products_result_sql)) {
							if ($products_row['products_bundle'] == 'yes' || $products_row['products_bundle_dynamic'] == 'yes') {
								$bundle_select_sql = "	SELECT pp.products_id
														FROM " . TABLE_PUBLISHERS_PRODUCTS . " AS pp 
														INNER JOIN " . TABLE_PRODUCTS_DELIVERY_INFO . " AS pdi
															ON pdi.products_id = pp.products_id 
														INNER JOIN " . TABLE_PRODUCTS_BUNDLES . " AS pb
															ON pp.products_id=pb.subproduct_id
														WHERE pb.bundle_id = '" . tep_get_prid($publishers_games_products_id) . "'
															AND pdi.products_delivery_mode_id = '6'
														LIMIT 1";
								$bundle_result_sql = tep_db_query($bundle_select_sql);
								$bundle_row = tep_db_fetch_array($bundle_result_sql);
								$publishers_games_products_id = $bundle_row['products_id'];
							}
						}

						$games_mapping_select_sql = "	SELECT pg.publishers_game, pg.publishers_games_id 
														FROM " . TABLE_PUBLISHERS_GAMES . " AS pg 
														INNER JOIN " . TABLE_PUBLISHERS_PRODUCTS . " AS pp
															ON pg.publishers_games_id = pp.publishers_games_id
														WHERE pg.publishers_id = '" . (int) $publishers_id . "'
															AND pp.products_id = '" . (int) $publishers_games_products_id . "'";
						$games_mapping_result_sql = tep_db_query($games_mapping_select_sql);
						$games_mapping_row = tep_db_fetch_array($games_mapping_result_sql);

						$get_top_up_info_array = $direct_topup_obj->get_top_up_info($publishers_games_products_id);

						$validate_game_acc_array = array('game' => $games_mapping_row['publishers_game'],
							'publishers_games_id' => $games_mapping_row['publishers_games_id'],
							'product_id' => $products_id,
							'amount_type' => $get_top_up_info_array['amount_type']['top_up_info_value'],
							'amount' => $get_top_up_info_array['amount']['top_up_info_value'],
							'quantity' => $qty
						);
						if ($direct_topup_obj->validate_game_acc($publishers_id, array_merge($_REQUEST['game_info'], $validate_game_acc_array), $curl_response_array)) {
							$prod_cat_path = tep_get_product_path($products_id, true);
							if (tep_not_null($prod_cat_path)) {
								$prod_cat_path_array = explode('_', $prod_cat_path);
								$game_id = $prod_cat_path_array[0];
							} else {
								$game_id = 0;
							}

							$extra_info_array = array();

							if (isset($_REQUEST['game_info'])) {
								$extra_info_array['top_up_info'] = $_REQUEST['game_info'];
								update_dtu_game_info('', $_REQUEST['game_info'], $products_id);
								$customer_id_conversion = $direct_topup_obj->get_publishers_games_conf($games_mapping_row['publishers_games_id'], 'CONVERT_CUSTOMER_ID_TO_EMAIL_FLAG');
								if ($customer_id_conversion == '1' && !empty($extra_info_array['top_up_info']['account']) && !is_numeric($extra_info_array['top_up_info']['account'])) {
									$extra_info_array['top_up_info']['account'] = $extra_info_array['top_up_info']['account'] . ':~:' . $direct_topup_obj->convert_customer_email_to_id($extra_info_array['top_up_info']['account']);
								}
							}
							if (isset($_REQUEST['delivery_mode']) && (int) $_REQUEST['delivery_mode'] > 0) {
								$extra_info_array['delivery_mode'] = (int) $_REQUEST['delivery_mode'];
							}
							$cart->add_cart($products_id, $qty, '', true, '', '', true, $extra_info_array);

							$temp_products = $cart->get_products();
							$temp_total_cart_item = 0;

							foreach ($temp_products as $temp_products_row) {
								$temp_total_cart_item += $temp_products_row['quantity'];
							}

							foreach ($temp_products as $temp_products_row) {
								if ($temp_products_row['id'] == $products_id) {
									$temp_added_product_name = $temp_products_row['name'];
								}
							}

							$temp_added_product_name = strip_tags($temp_added_product_name);
							$pd_encoding = mb_detect_encoding($temp_added_product_name);

							$pd_strlen_show_array = array();
							$pd_strlen_show_array['ASCII'] = 35;
							$pd_strlen_show_array['UTF-8'] = 28;
							$word_len_show = ((isset($pd_strlen_show_array[$pd_encoding])) ? $pd_strlen_show_array[$pd_encoding] : $default_pd_strlen_show);

							if (mb_strlen($temp_added_product_name, $pd_encoding) >= $word_len_show) {
								$temp_added_product_name = mb_substr($temp_added_product_name, 0, $word_len_show, $pd_encoding) . '...';
							}
						} else {
							if (isset($curl_response_array['result_code']) && $curl_response_array['result_code'] == '1508') {
								echo "<error_message>" . ERROR_DTU_EXCEED_TOP_UP_LIMIT . "</error_message>";
							} else if (isset($curl_response_array['game_acc_status']) && $curl_response_array['game_acc_status'] == 'NOT_RELOADABLE') {
								echo "<error_message>" . ERROR_INVALID_TOP_UP_ACCOUNT . "</error_message>";
							} else {
								echo "<error_message>" . ERROR_UNABLE_VALIDATE_TOP_UP_ACCOUNT . "</error_message>";
							}
						}
						echo "	<type>cart</type>";
						echo '	<total_item><![CDATA[' . $temp_total_cart_item . ']]></total_item>';
						echo '	<product_name><![CDATA[' . $temp_added_product_name . ']]></product_name>';
						echo '	<subtotal><![CDATA[' . $currencies->format($cart->show_total(), false) . ']]></subtotal>';
					}
				}
			}
			echo "</result>";
			break;
		case 'get_hla_product_info':
			$info_display = '';
			$default_hla_language_id = '1';

			if (isset($_GET['hla_acc_id'])) {
				$hla_account_id = (int) $_GET['hla_acc_id'];

				$product_info_select_sql = "SELECT hla.products_id, cha.products_hla_id, des.products_hla_characters_name 
											FROM " . TABLE_PRODUCTS_HLA . " AS hla 
											INNER JOIN " . TABLE_PRODUCTS_HLA_CHARACTERS . " AS cha 
												ON (hla.products_hla_id = cha.products_hla_id) 
											LEFT JOIN " . TABLE_PRODUCTS_HLA_DESCRIPTION . " AS des 
												ON (cha.products_hla_characters_id = des.products_hla_characters_id) 
											WHERE cha.products_hla_id = '" . tep_db_input($hla_account_id) . "' 
												AND des.language_id = '" . $default_hla_language_id . "'
											LIMIT 1";
				$product_info_result_sql = tep_db_query($product_info_select_sql);

				if ($product_info_row = tep_db_fetch_array($product_info_result_sql)) {
					$categories_name = tep_get_categories_name($page_info->main_game_id);
					$products_description = tep_get_products_info($product_info_row['products_id'], 'products_description', $languages_id, $default_languages_id);

					echo "<product_result>";
					echo "	<products_description><![CDATA[" . $products_description . "]]></products_description>";
					echo "	<category_name><![CDATA[" . $categories_name . "]]></category_name>";
					echo "	<hla_acc_id><![CDATA[" . $product_info_row['products_hla_id'] . "]]></hla_acc_id>";
					echo "	<products_name><![CDATA[" . $product_info_row['products_hla_characters_name'] . "]]></products_name>";
					echo "</product_result>";
				}
			}

			break;
		case 'get_product_info':
			$info_display = '';

			$product_info_select_sql = "	SELECT DISTINCT(p.products_id), p.products_bundle, p.products_bundle_dynamic, p.custom_products_type_id, p.products_price, p.products_tax_class_id, c.categories_id 
											FROM " . TABLE_PRODUCTS . " AS p 
											INNER JOIN " . TABLE_PRODUCTS_TO_CATEGORIES . " AS ptc 
												ON (p.products_id = ptc.products_id) 
											INNER JOIN " . TABLE_CATEGORIES . " AS c 
												ON (c.categories_id = ptc.categories_id AND c.categories_status = 1) 
											INNER JOIN " . TABLE_CATEGORIES_GROUPS . " AS cg 
												ON (cg.categories_id = c.categories_id) 
											WHERE p.custom_products_type_id = 0 
												AND p.products_status = 1 
												AND p.products_id = '" . tep_db_input($product_id) . "' 
												AND ((cg.groups_id = '" . $customers_groups_id . "') OR (cg.groups_id = 0))";
			$product_info_result_sql = tep_db_query($product_info_select_sql);

			if ($product_info_row = tep_db_fetch_array($product_info_result_sql)) {
				if ($product_info_row['products_bundle'] == 'yes') {
					$status_info = tep_product_add_to_cart_permission($product_info_row['products_id'], 'products_bundle');
				} else if ($product_info_row['products_bundle_dynamic'] == 'yes') {
					$status_info = tep_product_add_to_cart_permission($product_info_row['products_id'], 'products_bundle_dynamic');
				} else {
					$status_info = tep_product_add_to_cart_permission($product_info_row['products_id'], '');
				}

				$product_name = tep_get_products_name($product_info_row['products_id'], $languages_id);

				$parent_categories_array = array();
				$parent_categories_array[] = $product_info_row['categories_id'];
				tep_get_parent_categories($parent_categories_array, $product_info_row['categories_id']);

				foreach ($parent_categories_array as $key => $categories_id) {
					$categories_name_select_sql = "	SELECT cd.categories_name 
													FROM " . TABLE_CATEGORIES . " AS c 
													INNER JOIN " . TABLE_CATEGORIES_DESCRIPTION . " AS cd 
														ON (c.categories_id = cd.categories_id AND cd.language_id = '" . (int) $languages_id . "') 
													WHERE c.categories_id = '" . (int) $categories_id . "'  
														AND c.custom_products_type_id = '999' 
														AND c.categories_status = 1";
					$categories_name_result_sql = tep_db_query($categories_name_select_sql);
					if ($categories_name_row = tep_db_fetch_array($categories_name_result_sql)) {
						if (tep_not_null($info_display)) {
							$info_display = $categories_name_row['categories_name'] . ' - ' . $info_display;
						} else {
							$info_display = $categories_name_row['categories_name'];
						}
					}
				}

				$cat_setting_array = tep_get_cat_setting($product_info_row['categories_id'], 'catalog', 'cs_delivery_option');
				$show_it = $status_info['show'];

				echo "<product_result>";
				echo "	<products_status><![CDATA[" . $show_it . "]]></products_status>";
				echo "	<products_id><![CDATA[" . $product_info_row['products_id'] . "]]></products_id>";
				echo "	<products_name><![CDATA[" . $product_name . "]]></products_name>";
				echo "	<custom_product><![CDATA[" . $product_info_row['custom_products_type_id'] . "]]></custom_product>";
				echo "	<products_bundle><![CDATA[" . $product_info_row['products_bundle'] . "]]></products_bundle>";
				echo "	<products_quantity><![CDATA[" . $product_info_row['products_quantity'] . "]]></products_quantity>";
				echo "	<products_cat><![CDATA[" . $info_display . "]]></products_cat>";
				echo "	<delivery_option><![CDATA[" . $cat_setting_array['cs_delivery_option'] . "]]></delivery_option>";
				echo "</product_result>";
			}

			break;
		case 'get_store_credit_currency':
//			$selected_currency = isset($HTTP_GET_VARS['selected_currency']) ? $HTTP_GET_VARS['selected_currency'] : '';
//			$selected_pm_info = isset($HTTP_GET_VARS['selected_pm_info']) ? $HTTP_GET_VARS['selected_pm_info'] : '';
//
//			$sc_currency_code = '';
//			$sc_convert_text = '';
//			$no_sc_account = '0';
//			$no_supported_currency = '0';
//			$sc_convert_in_my_account = (tep_not_null($_SESSION['RegionGST']['tax_title']) ? 1 : 0);
//
//			$sc_array = store_credit::get_current_credits_balance($customer_id);
//
//			if (count($sc_array) > 0) {
//				$sc_currency_code = $currencies->get_code_by_id($sc_array['sc_currency_id']);
//				if ($sc_currency_code != $selected_currency) {
//					if (tep_not_null($selected_pm_info)) {
//						$selected_payment_array = explode(':~:', $selected_pm_info);
//						if (sizeof($selected_payment_array) == 3) {
//							$pm_obj = new payment($selected_payment_array[1]);
//
//							$supported_currencies = $GLOBALS[$selected_payment_array[1]]->get_support_currencies($selected_payment_array[2]);
//
//							if (is_array($supported_currencies)) {
//								if (array_search($sc_currency_code, $supported_currencies) === false) {
//									$total_store_credit = ($sc_array['sc_reverse'] + $sc_array['sc_irreverse']) - ($sc_array['sc_reverse_reserve_amt'] + $sc_array['sc_irreverse_reserve_amt']);
//									$from_sc_amt_str = $currencies->format($total_store_credit, false, $sc_currency_code); // 'GBP 45.87';
//									$to_sc_amt = store_credit::get_store_credit_conversion($total_store_credit, $sc_currency_code, $currency); //'MYR 284.21';
//									$to_sc_amt_str = $currencies->format($to_sc_amt, false, $currency);
//									$exch_rate = tep_round(($to_sc_amt / $total_store_credit), 3);
//									$exch_rate_str = '1 ' . $sc_currency_code . ' = ' . $exch_rate . ' ' . $currency;
//									$sc_convert_text = sprintf(TEXT_STORE_CREDIT_CONVERT, $from_sc_amt_str, $to_sc_amt_str, $exch_rate_str);
//									$no_supported_currency = '1';
//								}
//							}
//						}
//					}
//				}
//			} else {
//				$no_sc_account = '1';
//			}
//
//			$sc_convert_text = ($sc_convert_in_my_account ? TEXT_STORE_CREDIT_CONVERT_IN_MY_ACCOUNT : $sc_convert_text);
//
//			$GLOBALS['payment'] = $selected_pm_info;
//			echo "<store_credit_currency>";
//			echo "  <no_sc_account><![CDATA[" . $no_sc_account . "]]></no_sc_account>";
//			echo "  <no_supported_currency><![CDATA[" . $no_supported_currency . "]]></no_supported_currency>";
//			echo "  <sc_currency_code><![CDATA[" . $sc_currency_code . "]]></sc_currency_code>";
//			echo "  <sc_convert_text><![CDATA[" . $sc_convert_text . "]]></sc_convert_text>";
//			echo "  <sc_convert_to_currency><![CDATA[" . $currency . "]]></sc_convert_to_currency>";
//			echo "  <sc_convert_in_my_account><![CDATA[" . $sc_convert_in_my_account . "]]></sc_convert_in_my_account>";
//			echo "</store_credit_currency>";
			break;
		case 'convert_store_credit':
			$error = false;
			$success = false;
			$sc_object = new store_credit($customer_id);
			$to_currency = tep_not_null($_REQUEST['currency_selection']) ? tep_db_prepare_input($_REQUEST['currency_selection']) : "";

			if (tep_not_null($to_currency) == false) {
				$error = true;
			} else {
				$currency_id = array_search($to_currency, $currencies->internal_currencies);
				if (is_null($currency_id) || $currency_id == false) {
					$error = true;
				}
			}

			if ($error == false) {
				if ($sc_object->store_credit_conversion($HTTP_GET_VARS, $messageStack)) {
					echo "<store_credit_conversion>";
					echo "  <success_message><![CDATA[" . SUCCESS_STORE_CREDIT_CONVERSION . "]]></success_message>";
					echo "</store_credit_conversion>";
				} else {
					echo "<store_credit_conversion>";
					echo "  <error_message><![CDATA[conversion failed.]]></error_message>";
					echo "</store_credit_conversion>";
				}
			} else {
				echo "<store_credit_conversion>";
				echo "  <error_message><![CDATA[" . ERROR_INVALID_STORE_CREDIT_CURRENCY_SELECTION . "]]></error_message>";
				echo "</store_credit_conversion>";
			}
			break;
		case 'get_latest_news':
			echo '<section_news><![CDATA[' . tep_xmlhttp_get_latest_news($selection_id, $languages_id, $custom_products_type_id) . ']]></section_news>';
			break;
		case 'set_currency':
			global $cart, $messageStack;

			$error = false;

			if (tep_session_is_registered('customer_id')) {
				if (tep_session_is_registered('order_logged'))
					tep_session_unregister('order_logged');

				if (!tep_session_is_registered('currency') || isset($currency_code)) {
					if (!tep_session_is_registered('currency'))
						tep_session_register('currency');

					if (isset($currency_code)) {
						if (($currency = tep_currency_exists($currency_code)) === FALSE) {
							$currency = (USE_DEFAULT_LANGUAGE_CURRENCY == 'true') ? LANGUAGE_CURRENCY : DEFAULT_CURRENCY;
						}
					}
				}

				if (tep_not_null($payment_info) && preg_match('/^(pm_)(\d+)(:~:)(\S+)(:~:)(\d+)$/', $payment_info)) {
					$order = new order;
					$payment_modules = new payment($payment_info);
					$order_total_modules = new order_total; //ICW ADDED FOR CREDIT CLASS SYSTEM

					$order_total_modules->collect_posts();
					$order_total_modules->pre_confirmation_check();

					$currency_supported = $payment_modules->is_supported_currency($currency_code);

					if ($currency_supported == false) {
						$error = true;
					}
				} else {
					$error = true;
				}
			} else {
				$error = true;
			}

			if ($error == false) {
				if (!tep_session_is_registered('payment'))
					tep_session_register('payment');

				$customer_info_require = false;
				$payment = $payment_info;

				$missing_required_account_array = array();
				$missing_required_address_array = array();

				if ($payment_modules->check_credit_covers() || $order->info['subtotal'] <= 0) {
					//
					$button_html = tep_image_button2('red', 'javascript:pfv("", 14)', IMAGE_BUTTON_SECURE_CHECKOUT, 200);
				} else {
					if ($payment_modules->get_confirm_complete_days() > 0) {
						$button_html = tep_image_button2('red', 'javascript:popup_cfm_billing_info()', IMAGE_BUTTON_SECURE_CHECKOUT, 200);
					} else {
						$button_html = tep_image_button2('red', 'javascript:document.checkout_confirmation.submit()', IMAGE_BUTTON_SECURE_CHECKOUT, 200);
					}

					$button_html .= "<br>" . $payment_modules->get_confirm_button_caption();
				}

//				if (tep_customer_info_require($customer_id, $missing_required_account_array)) {
//					$customer_info_require = true;
//				}
//				if ($payment_modules->get_confirm_complete_days() > 0 && tep_customer_address_require($customer_id, $missing_required_address_array)) {
//					$customer_info_require = true;
//				}

				if ($customer_info_require) {
					$_SESSION['trace'] = 'chkout';

					$missing_required_info_array = array_merge($missing_required_account_array, $missing_required_address_array);

					$messageStack->add_session('account_edit', sprintf(TEXT_PERSONAL_INFO_REQUIRED, implode(', ', $missing_required_info_array)), 'error_box');

					$cart_html = $cart->get_cart(tep_draw_form('checkout_confirmation', tep_href_link(FILENAME_ACCOUNT_EDIT, '', 'SSL'), 'post') . '<table border="0" cellspacing="0" cellpadding="0" width="100%"><tr><td style="padding-right:10px;">' . $button_html . '</td></tr></table></form>');
				} else {
					$cart_html = $cart->get_cart(tep_draw_form('checkout_confirmation', tep_href_link(FILENAME_CHECKOUT_PROCESS, '', 'SSL'), 'post') . '<table border="0" cellspacing="0" cellpadding="0" width="100%"><tr><td>' . $button_html . '</td></tr></table></form>');
				}

				if (strstr(MODULE_ORDER_TOTAL_INSTALLED, 'ot_surcharge')) {
					$surcharge_amt = $ot_surcharge->calculate_surcharge_by($payment, $currency_code, 4);
					$surcharge_html = " <div id='surcharge_" . $sel . "'><table><tr><td>" . ($surcharge_amt > 0 ? "(+ " . $currencies->format($surcharge_amt, false, $currency_code) . " " . ENTRY_PAYMENT_SURCHARGE . ")" : "") . "</td></tr></table></div>";
				} else {
					$surcharge_html = " <div id='surcharge_" . $sel . "'><table><tr><td></td></tr></table></div>";
				}

				echo '	<cart_display_result>
							<cart_display><![CDATA[' . $cart_html . ']]></cart_display>
							<surcharge_display id="' . $sel . '"><![CDATA[' . $surcharge_html . ']]></surcharge_display>
						</cart_display_result>';
			} else {
				echo '	<cart_display_result>
							<error><![CDATA[' . 1 . ']]></error>
						</cart_display_result>';
			}

			break;
		case 'set_sc_currency':
			global $cart, $messageStack;

			$error = false;

			if (tep_session_is_registered('customer_id')) {
				if (tep_session_is_registered('order_logged'))
					tep_session_unregister('order_logged');

				if (!tep_session_is_registered('currency') || isset($currency_code)) {
					if (!tep_session_is_registered('currency'))
						tep_session_register('currency');

					if (isset($currency_code)) {
						if (($currency = tep_currency_exists($currency_code)) === FALSE) {
							$currency = (USE_DEFAULT_LANGUAGE_CURRENCY == 'true') ? LANGUAGE_CURRENCY : DEFAULT_CURRENCY;
						}
					}
				}

				if (tep_not_null($payment_info) && preg_match('/^(pm_)(\d+)(:~:)(\S+)(:~:)(\d+)$/', $payment_info)) {
					$order = new order;
					$payment_modules = new payment($payment_info);
					$order_total_modules = new order_total; //ICW ADDED FOR CREDIT CLASS SYSTEM

					$ot_surcharge->fees_type = 'sc_topup';

					$order_total_modules->collect_posts();
					$order_total_modules->pre_confirmation_check();

					$currency_supported = $payment_modules->is_supported_currency($currency_code);

					if ($currency_supported == false) {
						$error = true;
					}
				} else {
					$error = true;
				}
			} else {
				$error = true;
			}

			$surcharge = 0;
			$store_credit_ordered = 0;
			$total_amount = 0;
			$button_html = '<table border="0" cellspacing="0" cellpadding="0" width="100%"><tr><td align="right" nowrap><div class="lightgray_button" style="float:right"><a href="javascript:;"><span><font><?=TEXT_IS_LOADING?></font></span></a></div></td></tr><tr><td><?=tep_draw_separator("pixel_trans.gif", "100%", "3")?></td></tr><tr><td><div class="dottedLine"><table border="0" width="100%" cellspacing="0" cellpadding="0"><tr><td></td></tr></table></div></td></tr></table>';

			if ($error == false) {
				if (!tep_session_is_registered('payment'))
					tep_session_register('payment');

				$customer_info_require = false;
				$payment = $payment_info;

				$missing_required_account_array = array();
				$missing_required_address_array = array();

				if ($payment_modules->get_confirm_complete_days() > 0) {
					$button_html = tep_image_button2('red', 'javascript:popup_cfm_billing_info()', IMAGE_BUTTON_SECURE_CHECKOUT, 200);
				} else {
					$button_html = tep_image_button2('red', 'javascript:document.checkout_confirmation.submit()', IMAGE_BUTTON_SECURE_CHECKOUT, 200);
				}

				if ($payment_modules->check_credit_covers() || $order->info['subtotal'] <= 0) {
					//
				} else {
					$button_html .= "<br>" . $payment_modules->get_confirm_button_caption();
				}

//				if (tep_customer_info_require($customer_id, $missing_required_account_array)) {
//					$customer_info_require = true;
//				}
//				
//				if ($payment_modules->get_confirm_complete_days() > 0 && tep_customer_address_require($customer_id, $missing_required_address_array)) {
//					$customer_info_require = true;
//				}

				$customers_ordered_sc = 0;
				$customers_sc_select_sql = "SELECT cscq.customers_sc_cart_quantity, p.custom_products_type_id, p.products_price 
											FROM " . TABLE_CUSTOMERS_SC_CART . " AS cscq 
											INNER JOIN " . TABLE_PRODUCTS . " AS p 
												ON p.products_id = cscq.products_id 
											WHERE cscq.customers_id = '" . (int) $customer_id . "'";
				$customers_sc_result_sql = tep_db_query($customers_sc_select_sql);
				if ($customers_sc_row = tep_db_fetch_array($customers_sc_result_sql)) {
					if ($customers_sc_row['custom_products_type_id'] == 3) {
						$customers_sc_currency = tep_get_customer_store_credit_currency($customer_id, false); // SC Product currency always follow Customer SC Balance Currency
						if ($customers_sc_currency == $currency) {
							$customers_ordered_sc = $customers_sc_row['customers_sc_cart_quantity'];
						} else {
							$customers_ordered_sc = $currencies->advance_currency_conversion($customers_sc_row['customers_sc_cart_quantity'], $customers_sc_currency, $currency);
						}
					} else {
						$customers_ordered_sc = $customers_sc_row['products_price'];
					}
				}

				# add GST into Subtotal
				if (strstr(MODULE_ORDER_TOTAL_INSTALLED, 'ot_gst')) {
					$ot_gst->sc_process();
					$ot_gst_val = tep_not_null($ot_gst->output[0]['value']) ? $currencies->advance_currency_conversion($ot_gst->output[0]['value'], DEFAULT_CURRENCY, $currency) : 0;

					$customers_ordered_sc = $customers_ordered_sc + $ot_gst_val;
				}

				$surcharge_amt = '';
				if (strstr(MODULE_ORDER_TOTAL_INSTALLED, 'ot_surcharge')) {
					$surcharge_amt = $ot_surcharge->calculate_surcharge_by($payment, $currency_code, 4, $customers_ordered_sc);
					$surcharge_html = ' <div id="surcharge_' . $sel . '"><table><tr><td>' . ($surcharge_amt > 0 ? '(+ ' . $currencies->format($surcharge_amt, false, $currency_code) . ' ' . ENTRY_PAYMENT_SURCHARGE . ')' : '') . '</td></tr></table></div>';
				} else {
					$surcharge_html = " <div id='surcharge_" . $sel . "'><table><tr><td></td></tr></table></div>";
				}
				$sc_promotion_percentage = store_credit::get_sc_promotion_percentage();
				if ($sc_promotion_percentage > 0) {
					require(DIR_WS_LANGUAGES . $language . '/' . FILENAME_SC_CHECKOUT_PAYMENT);
					$new_sc_to_deliver = $customers_sc_row['customers_sc_cart_quantity'] * (1 + ($sc_promotion_percentage / 100));
					$extraScDiv = page::get_html_simple_rc_box('', '<table><tr><td style="width:87px;">' . sprintf(TEXT_INFO_SC_PROMOTION_PERCENTAGE, $sc_promotion_percentage) . '</td><td valign="top">:</td><td valign="top"><span class="price"><b><font class="largeFont">' . $currencies->format($new_sc_to_deliver, false, $customers_sc_currency) . '</font></b></span></td></tr></table>', 43);
				}
				$customers_ordered_sc_subtotal = $currencies->format($customers_ordered_sc, false, $currency);
				$customers_ordered_sc = $currencies->format($customers_ordered_sc + ($surcharge_amt > 0 ? $surcharge_amt : 0), false, $currency);
				$orig_cart = $cart;
				$cart = new sc_shopping_cart();
				$cart->restore_contents();
				$order = new order('', true);
				$opContent = $cart->calculateRebatePoint();
				$opDiv = '';
				$opExtraDiv = '';
				$cart = $orig_cart;
				$order = new order();
				if (isset($opContent['op'])) {
					$opDiv = page::get_html_simple_rc_box('', '<table><tr class="rebate"><td style="width:87px;">' . $opContent['op']['name'] . '</td><td valign="top">:</td><td class="paymentPriceValue"><span class="op"><b><font class="largeFont">' . $opContent['op']['value'] . '</font></b></span></td></tr></table>', 43);
				}
				if (isset($opContent['extra_op'])) {
					$opExtraDiv = page::get_html_simple_rc_box('', '<table><tr class="rebate"><td style="width:87px;">' . $opContent['extra_op']['name'] . '</td><td valign="top">:</td><td class="paymentPriceValue"><span class="extraOp"><b><font class="largeFont">' . $opContent['extra_op']['value'] . '</font></b></span></td></tr></table>', 43);
				}

				echo '	<cart_display_result>
							<total_surcharge><![CDATA[+' . ($surcharge_amt > 0 ? $currencies->format($surcharge_amt, false, $currency_code) : "") . ']]></total_surcharge>
							<subtotal_amount><![CDATA[' . $customers_ordered_sc_subtotal . ']]></subtotal_amount>
							<total_amount><![CDATA[' . $customers_ordered_sc . ']]></total_amount>
							<surcharge_display id="' . $sel . '"><![CDATA[' . $surcharge_html . ']]></surcharge_display>
                            <op><![CDATA[' . $opDiv . ']]></op>
                            <extra_op><![CDATA[' . $opExtraDiv . ']]></extra_op>
                            <extra_sc><![CDATA[' . $extraScDiv . ']]></extra_sc>
                        ';

				if ($customer_info_require) {

					$_SESSION['trace'] = 'scchkout';

					$missing_required_info_array = array_merge($missing_required_account_array, $missing_required_address_array);

					$messageStack->add_session('account_edit', sprintf(TEXT_PERSONAL_INFO_REQUIRED, implode(', ', $missing_required_info_array)), 'warning');

					echo '<button_html><![CDATA[' . tep_draw_form('checkout_confirmation', tep_href_link(FILENAME_ACCOUNT_EDIT, '', 'SSL'), 'post') . $button_html . '</form>]]></button_html>';
				} else {
					echo '<button_html><![CDATA[' . tep_draw_form('checkout_confirmation', tep_href_link(FILENAME_SC_CHECKOUT_PROCESS, '', 'SSL'), 'post') . $button_html . '</form>]]></button_html>';
				}
				echo '</cart_display_result>';
			} else {
				echo '	<cart_display_result>
							<error><![CDATA[' . 1 . ']]></error>
						</cart_display_result>';
			}

			break;

		case 'products_description':
			$prod_desc = tep_get_products_info($product_id, 'products_description', $languages_id, $default_languages_id);

			echo '<description><![CDATA[' . $prod_desc . ']]></description>';
			break;
		//for paypal ec
		case 'pg_confirm_payment':
			header('Content-Type: text/html');
			$order = new order($_SESSION['confirmation_order_id']);
			$payment_methods_select_sql = "	SELECT payment_methods_id
											FROM " . TABLE_ORDERS ."
											WHERE orders_id = '".$_SESSION['confirmation_order_id']."'";
			$payment_methods_result_sql = tep_db_query($payment_methods_select_sql);
			if ($payment_methods_row = tep_db_fetch_array($payment_methods_result_sql)) {
				$payment_modules = new payment('pm_'.$payment_methods_row['payment_methods_id'].':~:'.$payment.':~:'.$payment_methods_row['payment_methods_id']);
				list($$payment->display_title, $noUse) = explode('<br>', $$payment->display_title);
			}
			if ((isset($_SESSION['checkout_otp']) && $_SESSION['checkout_otp'] == '0') || !isset($_COOKIE['ogm']['cp'])) {
				if (file_exists(DIR_WS_LANGUAGES . $_SESSION['language'] . '/' . FILENAME_PAYMENT_CONFIRMATION)) {
					include_once(DIR_WS_LANGUAGES . $_SESSION['language'] . '/' . FILENAME_PAYMENT_CONFIRMATION);
				}
				if(isset($_GET['devicePin']) && !empty($_GET['devicePin'])) {
					$proceedPaymentConfirmation = false;
					tep_db_query("DELETE FROM " . TABLE_CUSTOMERS_OTP . " WHERE customers_otp_type = 'checkout_device_pin' AND customers_otp_request_date < NOW()");
					$checkout_trial_select_sql = "	SELECT customers_setting_value, created_datetime
													FROM " . TABLE_CUSTOMERS_SETTING . "
													WHERE customers_id = " . (int)$_SESSION['customer_id'] . "
														AND customers_setting_key = 'verify_checkout_security_pin'";
					$checkout_trial_result_sql = tep_db_query($checkout_trial_select_sql);
					if ($checkout_trial_row = tep_db_fetch_array($checkout_trial_result_sql)) {
						$maxTrial = 3;
						$dateDiff = strtotime(date("Y-m-d H:i:s")) - strtotime($checkout_trial_row['created_datetime']);
						$hourDiff = $dateDiff / ( 60 * 60 );
						if ($hourDiff <= 1) { //within 1 hr
							if ((int)$checkout_trial_row['customers_setting_value'] < $maxTrial) {
								$checkout_trial_data_array = array(	
																	'customers_setting_value' => (int)$checkout_trial_row['customers_setting_value'] + 1,
																	'updated_datetime' => 'now()'
									);
								tep_db_perform(TABLE_CUSTOMERS_SETTING, $checkout_trial_data_array, 'update', "  customers_id = " . (int)$_SESSION['customer_id'] . " AND customers_setting_key = 'verify_checkout_security_pin'");
							} else {
								$email_subject = 'Order ID: ' . $_SESSION['confirmation_order_id'] . ' - Fail to verify security pin';
								$email_text = '	Hi all,<br>
												To notify, this particular customer: ' .(int)$_SESSION['customer_id']. ' order number: ' . $_SESSION['confirmation_order_id'] . ' has failed to verify his security pin in ' . $$payment->display_title . '.<br>
												Please contact customer ASAP to find out if there are any problem if the payment is not authorised or they did not receive their codes in their email.';
								tep_mail(STORE_OWNER, EMAIL_TO, implode(' ', array(EMAIL_SUBJECT_PREFIX, $email_subject)), $email_text, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
								tep_redirect(tep_href_link(FILENAME_PAYMENT_CONFIRMATION, 'error_message=' . urlencode(sprintf(ERROR_MAXIMUN_SECURITY_PIN_TRIAL, $maxTrial)), 'SSL', true, false));
								exit;
							}
						} else {
							//reset counter
							$checkout_trial_data_array = array(	
																'customers_setting_value' => 1,
																'updated_datetime' => 'now()',
																'created_datetime' => 'now()'
								);
							tep_db_perform(TABLE_CUSTOMERS_SETTING, $checkout_trial_data_array, 'update', "  customers_id = " . (int)$_SESSION['customer_id'] . " AND customers_setting_key = 'verify_checkout_security_pin'");
						}
					} else {
						$checkout_trial_data_array = array(	'customers_id ' => (int)$_SESSION['customer_id'],
															'customers_setting_key' => 'verify_checkout_security_pin',
															'customers_setting_value' => 1,
															'updated_datetime' => 'now()',
															'created_datetime' => 'now()'
							);
						tep_db_perform(TABLE_CUSTOMERS_SETTING, $checkout_trial_data_array);
					}
					$checkout_otp_select_sql = "SELECT customers_otp_digit
												FROM " . TABLE_CUSTOMERS_OTP . "
												WHERE customers_id = " . (int)$_SESSION['customer_id'] . "
													AND customers_otp_type = 'checkout_device_pin'";
					$checkout_otp_result_sql = tep_db_query($checkout_otp_select_sql);
					if ($checkout_otp_row = tep_db_fetch_array($checkout_otp_result_sql)) {
						if (isset($checkout_otp_row['customers_otp_digit']) && $checkout_otp_row['customers_otp_digit'] == $_GET['devicePin']) {
							$time = time();
							$cookid = $time . "." . $_SESSION['customer_id'] . "." . md5($time . $_SESSION['customer_id'] . CHECKOUT_DEVICEPIN_SECRET);
							$cookie_domain = (($request_type == 'NONSSL') ? HTTP_COOKIE_DOMAIN : HTTPS_COOKIE_DOMAIN);
							$cookie_path = (($request_type == 'NONSSL') ? HTTP_COOKIE_PATH : HTTPS_COOKIE_PATH);
							$cookieLifeTime = time() + 60 * 60 * 24 * 90;
							tep_setcookie('ogm[cp]', $cookid, $cookieLifeTime, $cookie_path, $cookie_domain);

							tep_db_query("DELETE FROM " . TABLE_CUSTOMERS_OTP . " WHERE customers_id = " . (int)$_SESSION['customer_id'] . " AND customers_otp_type = 'checkout_device_pin'");
							tep_db_query("DELETE FROM " . TABLE_CUSTOMERS_SETTING . " WHERE customers_id = " . (int)$_SESSION['customer_id'] . " AND customers_setting_key = 'verify_checkout_security_pin'");
							$proceedPaymentConfirmation = true;
						} else {
							tep_redirect(tep_href_link(FILENAME_PAYMENT_CONFIRMATION, 'error_message=' . urlencode(ERROR_DEVICEPIN_INCORRECT), 'SSL', true, false));
						}
					} else {
						tep_redirect(tep_href_link(FILENAME_PAYMENT_CONFIRMATION, 'error_message=' . urlencode(ERROR_DEVICEPIN_INCORRECT), 'SSL', true, false));
					}
				} else {
					tep_redirect(tep_href_link(FILENAME_PAYMENT_CONFIRMATION, 'error_message=' . urlencode(ERROR_DEVICEPIN_MISSING), 'SSL', true, false));
				}
			} else if ($_SESSION['checkout_otp'] == '1') {
				$proceedPaymentConfirmation = true;
			} else {
				tep_redirect(tep_href_link(FILENAME_PAYMENT_CONFIRMATION, 'error_message=' . urlencode(ERROR_DEVICEPIN_MISSING), 'SSL', true, false));
			}
			if ($proceedPaymentConfirmation) {
				if (method_exists($$payment, 'paymentConfirmation')) {
					$$payment->paymentConfirmation();
				} else {
					tep_redirect(tep_href_link(FILENAME_CHECKOUT_PAYMENT));
				}
			}
			break;
	}
}
echo '</response>';

function tep_xmlhttp_get_latest_news($cat_id, $languages_id, $prod_tpl) {
	$tpl = $prod_tpl;
	$news_html = '';
	$LATEST_NEWS_TYPE = '5';
	define('LATEST_NEWS_BOX', 'classic');

	$cPath_array = array($cat_id);
	tep_get_parent_categories($cPath_array, $cat_id);
	$cPath_array = array_reverse($cPath_array);
	ob_start();

	include(DIR_WS_MODULES . FILENAME_LATEST_NEWS);

	$news_html = ob_get_contents();
	ob_end_clean();

	return $news_html;
}

function tep_xmlhttp_get_index_and_type($counter) {
	$return_index = 0;
	$return_type = '';

	if (tep_not_null($counter)) {
		if (stripos($counter, '_') == 1) {
			list($return_type, $index) = explode("_", $counter);

			switch ($return_type) {
				case 'p' :
				case 'd' :
				case 'g' :
					$return_index = $return_type . '_' . (int) $index;
					break;
				default :
					$return_type = '';
			}
		} else {
			$return_index = (int) $index;
		}
	}

	return array($return_index, $return_type);
}

?>