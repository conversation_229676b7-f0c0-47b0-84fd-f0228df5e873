<?
header("status: 200");
require_once('includes/application_top.php');
include_once(DIR_WS_CLASSES . 'pgs.php');
if (isset($_GET) && count($_GET)) {
    foreach ($_GET as $key => $value) {
        $form_data[$key] = $value;
    }
	if (pgs::getOrderSiteId($_GET['billReferenceNo']) == '5') {
		$url = HTTP_PGS_SERVER . '/IPN/HandleIPN?dp_payment=cimb';
	} else {
		$url = tep_href_link(FILENAME_CIMB_IPN_PROCESS);
	}

?>
<html>
    <head></head>
    <body>
        <form name="cimb_payment_success" action="<?php echo $url?>" method="post">
            <?php
                reset($form_data);
                while (list($key, $value) = each($form_data)) {
					echo "<input type='hidden' name='".$key."' value='".htmlspecialchars(stripslashes($value))."'>";
                }
            ?>
        </form>
        <script type="text/javascript" language="JavaScript">
            document.cimb_payment_success.submit();
        </script>
    </body>
</html>
<?
}
?>