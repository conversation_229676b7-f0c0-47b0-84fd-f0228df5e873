<script type="text/javascript">
parent.window.close();
</script>
<?

header("status: 200");
require_once('includes/application_top.php');

if (isset($_POST) && count($_POST)) {
	include_once(DIR_WS_MODULES . 'payment/cimb/classes/cimb_ipn_class.php');
	include_once(DIR_WS_LANGUAGES . $language . '/' . FILENAME_CHECKOUT_PROCESS);
	include_once(DIR_WS_FUNCTIONS . 'custom_product_info.php');
	include_once(DIR_WS_CLASSES . 'payment.php');
    include_once(DIR_WS_CLASSES . 'order.php');
    include_once(DIR_WS_CLASSES . 'anti_fraud.php');
    include_once(DIR_WS_CLASSES . 'log.php');
	    
	/**********************************************************
		(1) Check this order is pay to us
	**********************************************************/
	$payment = 'cimb';
    $payment_modules = new payment($payment);
    
    $cimb_ipn = new cimb_ipn($_POST);
    $orders_id = $cimb_ipn->get_order_id();
    
	$log_object = new log_files('system');
	
	if (tep_not_null($orders_id)) {
		$order = new order($orders_id);
		$$payment = new $payment($order->info['payment_methods_id']);

		$cimb_get_supported_currencies = $$payment->get_support_currencies($$payment->payment_methods_id, false);
		$cimb_currencies = $order->info['currency'];
		if (!in_array($cimb_currencies, $cimb_get_supported_currencies)) {
			$cimb_currencies = $cimb_get_supported_currencies[0];
		}
		$cimb_data_array = array();
		$cimb_data_array['cimb_status'] = tep_db_prepare_input($_POST['tranStatus']);
		$check_cimb_select_sql = "	SELECT orders_id 
									FROM " . TABLE_CIMB . "
									WHERE orders_id = '".(int)$orders_id."'";
		$check_cimb_result_sql = tep_db_query($check_cimb_select_sql);
		if (tep_db_num_rows($check_cimb_result_sql)) {
			tep_db_perform(TABLE_CIMB, $cimb_data_array, 'update', ' orders_id="'.(int)$orders_id.'"');
		} else {
			$cimb_data_array['cimb_channel_id'] = '';
			$cimb_data_array['cimb_reference_no'] = '';
			$cimb_data_array['cimb_amount'] = (double)$_POST['amount'];
			$cimb_data_array['cimb_currency'] = 'MYR';
			$cimb_data_array['cimb_user_full_name'] = '';

			$cimb_data_array['cimb_transaction_date'] = '';
			$cimb_data_array['cimb_transaction_time'] = '';
			$cimb_data_array['orders_id'] = (int)$orders_id;
			tep_db_perform(TABLE_CIMB, $cimb_data_array);
		}
		$$payment->get_merchant_account($cimb_currencies);
		if ($cimb_ipn->validate_receiver_account($$payment->payeeId)) {
			if ($cimb_ipn->validate_transaction_data($$payment, $orders_id)) {	// To ensure the integrity of the data posted back to merchant's server
				if ($order->info['orders_status_id'] == $$payment->order_status) {	// If the order still in initial status
					$payment_success = $cimb_ipn->authenticate($order, $orders_id, $$payment);
					if ($payment_success) {
						$cimb_ipn->process_this_order($orders_id, $$payment, $$payment->order_processing_status);
					}
				}
			}
		}
	}
}
?>