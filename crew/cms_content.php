<?
require('includes/application_top.php');
require(DIR_WS_LANGUAGES . $language . '/' . FILENAME_CMS_CONTENT);

$menu_id = (isset($_REQUEST['menu_id']) ? (int)$_REQUEST['menu_id'] : '');

$cms_content_array = array(	'menu_title' => ERROR_ARTICLE_REMOVED,
							'menu_content' => ERROR_ARTICLE_REMOVED_DESC);

$content_sql = "SELECT cms_menu_status,cms_menu_right_navigation 
				FROM " . TABLE_CMS_MENU . " 
				WHERE cms_menu_id = '".tep_db_input($menu_id)."' 
					AND cms_menu_content_type = 'content'";
$result_sql = tep_db_query($content_sql);
if ($entry = tep_db_fetch_array($result_sql)) {
	if ($entry['cms_menu_status'] == "1" || $menu_id == 108) {
		$content_select_sql = "	SELECT cms_menu_lang_setting_key, cms_menu_lang_setting_key_value 
								FROM " . TABLE_CMS_MENU_VALUE . " 
								WHERE ( cms_menu_lang_setting_key = 'menu_content' OR cms_menu_lang_setting_key = 'menu_title') 
									AND languages_id = '".tep_db_input($languages_id)."' 
									AND cms_menu_id = '".tep_db_input($menu_id)."'";
		$content_result_sql = tep_db_query($content_select_sql);
		
		while ($content_row = tep_db_fetch_array($content_result_sql)) {
			$cms_content_array[$content_row['cms_menu_lang_setting_key']] = $content_row['cms_menu_lang_setting_key_value']; 
		}
	}
}

define('NAVBAR_TITLE', $cms_content_array['menu_title']);
define('HEADING_TITLE', $cms_content_array['menu_title']);

$content = "cms_content";
$disable_right_navigation_flag = 0;

if (!tep_session_is_registered('customer_id')) 	$navigation->set_snapshot();
$breadcrumb->add(NAVBAR_TITLE);

if ($entry['cms_menu_right_navigation'] == "0") {
	$disable_right_navigation_flag = 1;
}

require(DIR_WS_TEMPLATES . TEMPLATENAME_MAIN_PAGE);
require(DIR_WS_INCLUDES . 'application_bottom.php'); 
?>