<?php
include('includes/application_top.php');
include_once(DIR_WS_CLASSES . 'pgs.php');
$get_array = array();
foreach ($_GET as $get_key_loop => $get_data_loop) $get_array[] = $get_key_loop.'='.$get_data_loop;
if (pgs::getOrderSiteId($_GET['OrderId']) == '5') {
	tep_redirect(HTTP_PGS_SERVER . '/payment/CheckoutSuccess?'.implode("&", $get_array));
} else {
	tep_redirect(tep_href_link(FILENAME_CHECKOUT_PROCESS, implode("&", $get_array), 'SSL'));
}
?>
