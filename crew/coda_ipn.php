<?php

require_once('includes/application_top.php');

require_once(DIR_WS_MODULES . 'payment/coda/classes/codaIpnClass.php');
include(DIR_WS_LANGUAGES . $language . '/' . FILENAME_CHECKOUT_PROCESS);

if (isset($_GET) && count($_GET)) {
    include_once(DIR_WS_CLASSES . 'order.php');
    include_once(DIR_WS_CLASSES . 'anti_fraud.php');
    include_once(DIR_WS_CLASSES . 'log.php');
	include_once(DIR_WS_CLASSES . 'pgs.php');
	
    $payment = 'coda';
	echo 'ResultCode=0'; //reply to coda IPN
    // load selected payment module
	if (pgs::getOrderSiteId($_GET['OrderId']) == '5') {
		pgs::repostToPGS('coda', $_GET);
	} else {
		include_once(DIR_WS_CLASSES . 'payment.php');

		$codaIpn = new codaIpnClass($_GET);

		$orders_id = $codaIpn->get_order_id();
		$order = new order($orders_id);

		$payment_modules = new payment($payment);
		$$payment = new $payment($order->info['payment_methods_id']);
		$$payment->get_merchant_account($order->info['currency']);

		$log_object = new log_files('system');
		if ((int) $orders_id > 0) {
			$coda_data_array = array(
				'coda_order_id' => (int) $orders_id,
				'coda_txn_id' => $codaIpn->key['TxnId'],
				'coda_result_code' => $codaIpn->key['ResultCode'],
				'coda_total_price' => $codaIpn->key['TotalPrice'],
				'coda_checksum' => $codaIpn->key['Checksum']
			);

			$coda_payment_select_sql = "	SELECT coda_order_id
											FROM " . TABLE_CODA . "
											WHERE coda_order_id = '" . (int) $orders_id . "'";
			$coda_payment_result_sql = tep_db_query($coda_payment_select_sql);
			if (tep_db_num_rows($coda_payment_result_sql)) {
				tep_db_perform(TABLE_CODA, $coda_data_array, 'update', " coda_order_id = '" . (int) $orders_id . "' ");
			} else {
				$coda_data_array['coda_order_id'] = $orders_id;
				tep_db_perform(TABLE_CODA, $coda_data_array);
			}
		}
                
		if ($codaIpn->key['ResultCode'] == 0) {
			if ($codaIpn->validate_txnId()) {
				if ($codaIpn->validate_transaction_data($$payment)) { // To ensure the integrity of the data posted back to merchant's server
					if (tep_not_null($orders_id)) {
						if (!is_object($order))
							$order = new order($orders_id);
						if ($order->info['orders_status_id'] == $$payment->order_status) { // If the order still in initial status
							if($codaIpn->authenticate($order)) {
								$paymentResult = $$payment->check_trans_status($codaIpn->key['TxnId']);
								if (isset($paymentResult['resultCode']) && $paymentResult['resultCode'] == 0) {
									$codaIpn->process_this_order($orders_id, $$payment, $$payment->order_processing_status);
								}
							}
						}
					}
				}
			}
		}
	}
}
?>