<?
require('includes/application_top.php');

function populatePages($id, $select, $page=0) {
	if ($select == 0) {
		return '';
	} else {
		$infolinks_contents_select_sql = "	SELECT * 
											FROM " . TABLE_INFOLINKS_CONTENTS . " 
											WHERE infolinks_id = '" . tep_db_input($id) . "' 
											ORDER BY infolinks_contents_page ASC;";
		$infolinks_contents_result_sql = tep_db_query($infolinks_contents_select_sql);
		
	  	$count = tep_db_num_rows($infolinks_contents_result_sql);
	  	
		if ($count == 1) {
			return '';
		} else if ($count>1) {
			$output = 'Pages: ';
		} else {
			$output = '';
		}
		
		$nextpage = $page+1;
		$prevpage = $page-1;	
		
		if ($page > 1) {
			$infolinks_page_select_sql = "	SELECT infolinks_contents_id 
											FROM " . TABLE_INFOLINKS_CONTENTS . " 
											WHERE infolinks_id = '" . tep_db_input($id) . "' 
												AND infolinks_contents_page = '" . tep_db_input($prevpage) . "';";
			$infolinks_page_result_sql = tep_db_query($infolinks_page_select_sql);
			
			if ($infolinks_page_row = tep_db_fetch_array($infolinks_page_result_sql)) {
				$output .= '<a href="' . tep_href_link(FILENAME_INFOLINKS, tep_get_all_get_params(array('id', 'content_id')) . "id=".$id."&content_id=".(int)$infolinks_page_row['infolinks_contents_id'], 'NONSSL') . '" class="pageResults"><u>[< Prev]</u></a>'."&nbsp;";
			}
		}
		
		if ($count) {
			for ($i=0; $i < $count; ++$i) {
				$infolinks_contents_row = tep_db_fetch_array($infolinks_contents_result_sql);
				
				if ($select == (int)$infolinks_contents_row['infolinks_contents_id']) {
					$output .= '<b>'.(int)$infolinks_contents_row['infolinks_contents_page'].'</b>'."&nbsp;";
				} else {
					$output .= '<a href="' . tep_href_link(FILENAME_INFOLINKS, tep_get_all_get_params(array('id', 'content_id')) . "id=".$id."&content_id=".(int)$infolinks_contents_row['infolinks_contents_id'], 'NONSSL') . '" class="pageResults"><u>'.(int)$infolinks_contents_row['infolinks_contents_page'].'</u></a>'."&nbsp;";
				}
			}
			
			$infolinks_next_page_select_sql = "	SELECT infolinks_contents_id 
												FROM " . TABLE_INFOLINKS_CONTENTS . " 
												WHERE infolinks_id = '" . tep_db_input($id) . "' 
													AND infolinks_contents_page = '" . tep_db_input($nextpage) . "';";
			$infolinks_next_page_result_sql = tep_db_query($infolinks_next_page_select_sql);
			
			if ($infolinks_next_page_row = tep_db_fetch_array($infolinks_next_page_result_sql)) {
				$next='<a href="' . tep_href_link(FILENAME_INFOLINKS, tep_get_all_get_params(array('id', 'content_id')) . "id=".$id."&content_id=".(int)$infolinks_next_page_row['infolinks_contents_id'], 'NONSSL') . '" class="pageResults"><u>[Next >]</u></a>'."&nbsp;";
			}
			
			return $output.$next;
		} else {
			return '';
		}
	}
}

function tep_get_infolink_group_path ($infolinks_groups_id,$infolink_breadcrumb_array) {
	$infolinks_groups_select_sql = "	SELECT infolinks_groups_id,infolinks_groups_parent_id,infolinks_groups_title,infolinks_groups_main_page_id 
										FROM " . TABLE_INFOLINKS_GROUPS . " 
										WHERE infolinks_groups_id = '" . tep_db_input($infolinks_groups_id) . "'";
	$infolinks_groups_result_sql = tep_db_query($infolinks_groups_select_sql);
	$infolinks_groups_row = tep_db_fetch_array($infolinks_groups_result_sql);

	$infolink_breadcrumb_array [] = array (	'infolinks_groups_id' => $infolinks_groups_row['infolinks_groups_id'],
											'infolinks_groups_parent_id' => $infolinks_groups_row['infolinks_groups_parent_id'],
											'infolinks_groups_title' => $infolinks_groups_row['infolinks_groups_title'],
											'infolinks_groups_main_page_id' => $infolinks_groups_row['infolinks_groups_main_page_id']);

	if ($infolinks_groups_row['infolinks_groups_parent_id']) {
		$infolink_breadcrumb_array = tep_get_infolink_group_path ($infolinks_groups_row['infolinks_groups_parent_id'],$infolink_breadcrumb_array);
	}
	
	return ($infolink_breadcrumb_array);
}

$id = (int)$_GET['id'];
$content_id = (int)$_GET['content_id'];
$curpageno = 0;
$isempty = true;

$infolinks_select_sql = "SELECT * FROM " . TABLE_INFOLINKS . " WHERE infolinks_id = '" . tep_db_input($id) . "' AND infolinks_active =1;";
$infolinks_result_sql = tep_db_query($infolinks_select_sql);

if ($rowMain = tep_db_fetch_array($infolinks_result_sql)) {
    if ($content_id == 0) {
        $sql = mysql_query("SELECT * FROM infolinks_contents where infolinks_id='".$id."' and infolinks_contents_page='1';");

        if (mysql_num_rows($sql))	$row=mysql_fetch_array($sql);
        $content_id=(int)$row['infolinks_contents_id'];
    }

    $sql = mysql_query("SELECT * FROM infolinks_contents where infolinks_contents_id='".$content_id."';");
    if (mysql_num_rows($sql)) {
        $row = mysql_fetch_array($sql);
        $curpageno = (int)$row['infolinks_contents_page'];
        $isempty = false;
    } else {
        $curpageno = 0;
        $isempty = true;
    }

    if ($rowMain['infolinks_groups_id']) {
        $infolink_breadcrumb_array = array();
        $infolink_breadcrumb_array = tep_get_infolink_group_path ($rowMain['infolinks_groups_id'],$infolink_breadcrumb_array);
    }

    if ($infolink_breadcrumb_array) {
        krsort($infolink_breadcrumb_array);

        foreach ($infolink_breadcrumb_array as $infolink_breadcrumb_row) {
            $breadcrumb->add($infolink_breadcrumb_row['infolinks_groups_title'], tep_href_link(FILENAME_INFOLINKS, 'id='.$infolink_breadcrumb_row['infolinks_groups_main_page_id'] , 'NONSSL'));

        }
    }
} else {
    tep_redirect(tep_href_link(FILENAME_ERROR_PAGE_NOT_FOUND, '', 'SSL'));
}

define("PAGES_LINK", populatePages($id,$content_id,$curpageno));

require(DIR_WS_LANGUAGES . $language . '/' . FILENAME_INFOLINKS);

$breadcrumb->add(NAVBAR_TITLE, tep_href_link(FILENAME_INFOLINKS, 'id='.$id , 'NONSSL'));

if (!tep_session_is_registered('customer_id')) 	$navigation->set_snapshot();


$content = CONTENT_INFOLINKS;
unset($content_template);

if ($rowMain['infolinks_right_navigation'] == "0") {
	$disable_right_navigation_flag = 1;
} else {
	$disable_right_navigation_flag = 0;
}

if ($rowMain['infolinks_imageonly'] == "0") {
	$hide_infolinks_title = false;
} else {
	$hide_infolinks_title = true;
}

require(DIR_WS_TEMPLATES . TEMPLATENAME_MAIN_PAGE);
require(DIR_WS_INCLUDES . 'application_bottom.php'); 
?>