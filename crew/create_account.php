<?php

/*
  $Id: create_account.php,v 1.53 2014/12/29 08:27:37 weesiong Exp $

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2003 osCommerce

  Released under the GNU General Public License
 */

require('includes/application_top.php');
tep_redirect($shasso_obj->getSignupURL('', tep_href_link(FILENAME_DEFAULT, '', 'SSL')));

if (!(isset($_GET['baseURL']) && $_GET['baseURL'] == 'gift_card.php')) {
//    tep_redirect(HTTP_SHASSO_PORTAL . '/auth/register?origin=' . urlencode(tep_href_link(FILENAME_DEFAULT, '', 'SSL')));
}

require(DIR_WS_LANGUAGES . $language . '/' . FILENAME_LOGIN);
include_once(DIR_WS_CLASSES . 'recaptcha.php');

define('GENERAL_NEWSLETTER_ID', 17);

$action = (isset($_REQUEST['action']) ? $_REQUEST['action'] : '');
$baseURL = isset($_REQUEST['baseURL']) ? '&baseURL=' . urlencode($_REQUEST['baseURL']) : '';

$oauth_login = oauth::is_oauth_login();
// redirect the customer to a friendly cookie-must-be-enabled page if cookies are disabled (or the session has not started)
if ($session_started == false) {
    tep_redirect(tep_href_link(FILENAME_COOKIE_USAGE));
}

if (tep_session_is_registered('customer_id')) {
    if (tep_not_null($baseURL)) {
        $path = str_replace(":", "", $_REQUEST['baseURL']);
    } else {
        $path = tep_href_link(FILENAME_DEFAULT, '', 'SSL');
    }
    if ($oauth_login) {
        switch ($oauth_login) {
            case 'oauth':
                oauth::Oauth_login_success(true);
                break;
            case 'redirect_url':
                if (isset($_COOKIE['ogm']) && !empty($_COOKIE['ogm']['si']) && !empty($_COOKIE['ogm']['st'])) {
                    oauth::resetSSOToken($_COOKIE['ogm']['st'], $_COOKIE['ogm']['si']);
                }
                $redirect_url = $_SESSION['login_redirect_url'];
                unset($_SESSION['login_redirect_url']);
                tep_redirect($redirect_url);
                break;
        }
    } else {
        tep_redirect($path);
    }
}

$ask_for_ads_source = false;
$error = false;

$country_id_default_select = tep_get_ip_country_id(true);

if (tep_not_null($action)) {
    switch ($action) {
        case 'create_account':
            $error = false;

            $email_address = tep_not_null($HTTP_POST_VARS['account_email_address']) ? tep_db_prepare_input(strip_tags($HTTP_POST_VARS['account_email_address'])) : "";
            $password = tep_not_null($_POST['password']) ? tep_db_prepare_input(strip_tags($_POST['password'])) : "";
            $password_confirmation = tep_not_null($_POST['password_confirmation']) ? tep_db_prepare_input(strip_tags($_POST['password_confirmation'])) : "";
            $firstname = tep_not_null($HTTP_POST_VARS['firstname']) ? tep_db_prepare_input(strip_tags($HTTP_POST_VARS['firstname'])) : "";
            $lastname = tep_not_null($HTTP_POST_VARS['lastname']) ? tep_db_prepare_input(strip_tags($HTTP_POST_VARS['lastname'])) : "";
//			$phone_country = tep_not_null($HTTP_POST_VARS['country']) ? tep_db_prepare_input(strip_tags($HTTP_POST_VARS['country'])) : "";
//			$contactnumber = tep_not_null($HTTP_POST_VARS['contactnumber']) ? tep_parse_telephone(tep_db_prepare_input(preg_replace('/[^\d]/', '', $HTTP_POST_VARS['contactnumber'])), $phone_country, 'id') : "";
            $gender = tep_not_null($HTTP_POST_VARS['gender']) ? tep_db_prepare_input($HTTP_POST_VARS['gender']) : '';
            $dob = '';
            $refid = tep_not_null($HTTP_POST_VARS['refid']) ? tep_db_prepare_input($HTTP_POST_VARS['refid']) : '';
            $agreed = tep_not_null($HTTP_POST_VARS['agreed']) ? tep_db_prepare_input($HTTP_POST_VARS['agreed']) : '';

            if ($ask_for_ads_source) {
                $survey_answer = (isset($_POST['hidden_survey']) && tep_not_null($_POST['hidden_survey']) ? tep_db_prepare_input(JS_SURVEY_QUESTION . " : " . strip_tags($_POST['hidden_survey'])) : "");
            }

            // Email error message
            if (!tep_not_null($email_address)) {
                $error = true;
                $messageStack->add_session('create_account', ENTRY_EMAIL_ADDRESS_ERROR);
            } else if (strlen($email_address) < ENTRY_EMAIL_ADDRESS_MIN_LENGTH) {
                $error = true;
                $messageStack->add_session('create_account', ENTRY_EMAIL_ADDRESS_ERROR);
            } else if (tep_validate_email($email_address) == false) {
                $error = true;
                $messageStack->add_session('create_account', ENTRY_EMAIL_ADDRESS_CHECK_ERROR);
            } else {
                $check_email_query = tep_db_query("select customers_id from " . TABLE_CUSTOMERS . " where customers_email_address = '" . tep_db_input($email_address) . "'");
                $check_email = tep_db_num_rows($check_email_query);

                if ($check_email > 0) {
                    $error = true;
                    $messageStack->add_session('create_account', ENTRY_EMAIL_ADDRESS_ERROR_EXISTS);
                }
            }

            // Password & Confirm Password error messages
            if (strlen($password) < ENTRY_PASSWORD_MIN_LENGTH) {
                $error = true;
                $messageStack->add_session('create_account', ENTRY_PASSWORD_ERROR);
            }

            // Mantis # 0000024 @ ************ - Confirm Password error message
            if (strlen($password_confirmation) < ENTRY_PASSWORD_MIN_LENGTH) {
                $error = true;
                $messageStack->add_session('create_account', sprintf(ENTRY_PASSWORD_CONFIRMATION_ERROR, ENTRY_PASSWORD_MIN_LENGTH));
            } else if ($password != $password_confirmation) {
                $error = true;
                $messageStack->add_session('create_account', ENTRY_PASSWORD_ERROR_NOT_MATCHING);
            }

            // First Name error message
            if (strlen($firstname) < ENTRY_FIRST_NAME_MIN_LENGTH) {
                $error = true;
                $messageStack->add_session('create_account', ENTRY_FIRST_NAME_ERROR);
            }

            // Last Name error message
            if (strlen($lastname) < ENTRY_LAST_NAME_MIN_LENGTH) {
                $error = true;
                $messageStack->add_session('create_account', ENTRY_LAST_NAME_ERROR);
            }

            // Country error message
//			if (is_numeric($phone_country) == false) {
//		    	$error = true;
//		      	$messageStack->add_session('create_account', ENTRY_COUNTRY_ERROR);
//		    }
            // Mantis # 0000024 @ ************ - Contact Number error message
//			if (strlen($contactnumber) < ENTRY_TELEPHONE_MIN_LENGTH) {
//				$error = true;
//				$messageStack->add_session('create_account', sprintf(ENTRY_CONTACT_NUMBER_ERROR, ENTRY_TELEPHONE_MIN_LENGTH));
//			} else if (tep_is_mobile_num_exist($contactnumber, $phone_country)) {
//                $error = true;
//				$messageStack->add_session('create_account', ENTRY_CONTACT_NUMBER_EXIST_ERROR);
//            }
            // Subscribe to newsletter
            if (isset($HTTP_POST_VARS['newsletter']) && count($HTTP_POST_VARS['newsletter'])) {
                $newsletter = implode(',', $HTTP_POST_VARS['newsletter']);
            } else {
                $newsletter = '0';
            }

            // Check customer if agreed to the terms & policy @ ************
            if (isset($agreed) && !tep_not_null($agreed)) {
                $error = true;
                $messageStack->add_session('create_account', ENTRY_SIGNUP_TERMSANDCONDITION_ERROR);
            }

            if (SIGN_UP_CAPTCHA_FIELD == 1) {
                if (!tep_not_null($ogm_fb_obj->user)) {
                    if (!tep_not_null($_POST['recaptcha_challenge_field']) || !tep_not_null($_POST['recaptcha_response_field'])) {
                        $error = true;
                        $messageStack->add_session('create_account', TEXT_CAPTCHA_MISSING_ERROR);
                    } else if (recaptcha::captcha_validation($_POST['recaptcha_challenge_field'], $_POST['recaptcha_response_field']) === false) {
                        $error = true;
                        $messageStack->add_session('create_account', TEXT_CAPTCHA_ERROR);
                    }
                }
            }

            if (tep_not_null($_POST['dob_year']) && tep_not_null($_POST['dob_month']) && tep_not_null($_POST['dob_day'])) {
                $dob = tep_db_prepare_input($_POST['dob_year'] . '-' . $_POST['dob_month'] . '-' . $_POST['dob_day']);
            }

            // Mantis # 0000024 @ ************ - DOB fields checking
            if ((isset($HTTP_POST_VARS['dob_year']) && tep_not_null($HTTP_POST_VARS['dob_year'])) || (isset($HTTP_POST_VARS['dob_month']) && tep_not_null($HTTP_POST_VARS['dob_month'])) || (isset($HTTP_POST_VARS['dob_day']) && tep_not_null($HTTP_POST_VARS['dob_day']))) {
                $dob_entered = 1;

                $day = $HTTP_POST_VARS['dob_day'];
                $month = $HTTP_POST_VARS['dob_month'];
                $year = $HTTP_POST_VARS['dob_year'];

                $message_1 = ENTRY_DATE_OF_BIRTH_ERROR_1;
                $message_2 = ENTRY_DATE_OF_BIRTH_ERROR_2;
                $message_3 = ENTRY_DATE_OF_BIRTH_ERROR_3;
                $message_4 = ENTRY_DATE_OF_BIRTH_ERROR_4;

                $day_month_message_1 = "";
                $day_month_message_2 = "";
                $count = 0;
                $month_day_message = 0;
                $year_message = "";

                if (!tep_not_null($month)) {
                    $day_month_message_2 = $day_month_message_2 . " month";
                    $message_1 = $message_1 . " month";
                    $count = $count + 1;
                    $month_day_message = 1;
                }

                if (!tep_not_null($day)) {
                    $month_day_message = 1;
                    if ($count == 0) {
                        $count = $count + 1;
                        $day_month_message_2 = $day_month_message_2 . " day";
                        $message_1 = $message_1 . " day";
                    } else if ($count == 1) {
                        $count = $count + 1;
                        $day_month_message_2 = $day_month_message_2 . " and day";
                        $message_1 = $message_1 . " and day";
                    }
                }

                if (!tep_not_null($year)) {
                    $count++;
                    $year_message = $message_4;
                }

                if ($count == 0) {
                    $message_1 = '';
                    $message_2 = '';
                    $message_3 = '';
                }

                if ($month_day_message < 1) {
                    $message_1 = '';
                    $message_2 = '';
                    $message_3 = '';
                }

                if ($count > 0) {
                    $error_message = $error_message . $message_1 . $day_month_message_1 . $message_2 . $day_month_message_2 . $message_3 . $year_message . "\n";
                    $error = true;
                    $dob_entered = 0;
                    $messageStack->add_session('create_account', $error_message);
                }

                if ($dob_entered == 1) {
                    if (!checkdate(substr($dob, 5, 2), substr($dob, 8, 2), substr($dob, 0, 4))) {
                        $error = true;
                        $messageStack->add_session('create_account', ENTRY_DATE_OF_BIRTH_ERROR);
                    }
                }

                if (tep_day_diff(date("Y-m-d H:i:s"), $year . '-' . $month . '-' . $day . ' 00:00:00', 'year') > 0) {
                    $error = true;
                    $messageStack->add_session('create_account', ENTRY_DATE_OF_BIRTH_FUTURE_ERROR);
                }

                if (tep_day_diff($year . '-' . $month . '-' . $day . ' 00:00:00', date("Y-m-d H:i:s"), 'year') > ENTRY_AGE_MAX) {
                    $error = true;
                    $messageStack->add_session('create_account', ENTRY_DATE_OF_BIRTH_OVER_ERROR);
                }
            }

            // Mantis # 0000024 @ ************ - Billing address fields verification
            if (isset($HTTP_POST_VARS['billingaddress1']) && tep_not_null($HTTP_POST_VARS['billingaddress1'])) {
                $address_update = true;

                $billingaddress1 = tep_not_null($HTTP_POST_VARS['billingaddress1']) ? tep_db_prepare_input(strip_tags($HTTP_POST_VARS['billingaddress1'])) : "";
                $billingaddress2 = (tep_not_null($HTTP_POST_VARS['billingaddress2']) && isset($HTTP_POST_VARS['billingaddress2'])) ? tep_db_prepare_input(strip_tags($HTTP_POST_VARS['billingaddress2'])) : "";
                $billingcity = tep_not_null($HTTP_POST_VARS['billingcity']) ? tep_db_prepare_input(strip_tags($HTTP_POST_VARS['billingcity'])) : "";
                $billingpostcode = tep_not_null($HTTP_POST_VARS['billingpostcode']) ? tep_db_prepare_input(strip_tags($HTTP_POST_VARS['billingpostcode'])) : "";
                $state = tep_not_null($HTTP_POST_VARS['state']) ? tep_db_prepare_input(strip_tags($HTTP_POST_VARS['state'])) : "";
                $billingcountry = tep_not_null($HTTP_POST_VARS['billingcountry']) ? tep_db_prepare_input($HTTP_POST_VARS['billingcountry']) : "";

                if (strlen($billingaddress1) < ENTRY_STREET_ADDRESS_MIN_LENGTH) {
                    $error = true;
                    $messageStack->add_session('create_account', ENTRY_STREET_ADDRESS_ERROR);
                }

                if (strlen($billingcity) < ENTRY_CITY_MIN_LENGTH) {
                    $error = true;
                    $messageStack->add_session('create_account', ENTRY_CITY_ERROR);
                }

                if (strlen($billingpostcode) < ENTRY_POSTCODE_MIN_LENGTH) {
                    $error = true;
                    $messageStack->add_session('create_account', ENTRY_POST_CODE_ERROR);
                }

                if (!is_numeric($billingcountry) && tep_not_null($billingaddress1)) {
                    $error = true;
                    $messageStack->add_session('create_account', ENTRY_COUNTRY_ERROR);
                }

                $zone_id = 0;
                $check_query = tep_db_query("select zone_country_id from " . TABLE_ZONES . " where zone_country_id = '" . (int) $billingcountry . "'");
                $entry_state_has_zones = tep_db_num_rows($check_query);

                if ($entry_state_has_zones > 0) {
                    $zone_query = tep_db_query("SELECT zone_id FROM " . TABLE_ZONES . " WHERE zone_country_id = '" . (int) $billingcountry . "' AND zone_id = '" . (int) $state . "'");
                    if (tep_db_num_rows($zone_query) == 1) {
                        $zone = tep_db_fetch_array($zone_query);
                        $zone_id = $zone['zone_id'];
                    } else {
                        $error = true;
                        $messageStack->add_session('create_account', ENTRY_STATE_ERROR_SELECT);
                    }
                } else {
                    if (strlen($state) < ENTRY_STATE_MIN_LENGTH && tep_not_null($billingcountry) && tep_not_null($billingaddress1)) {
                        $error = true;
                        $messageStack->add_session('create_account', ENTRY_STATE_ERROR);
                    }
                }
            }

            if ($error == false) {
                // M#0000075 @ ************ - Viral Inviter join member process
                $recid = tep_not_null($_REQUEST['rec']) ? tep_db_prepare_input($_REQUEST['rec']) : '';
                $random_code = tep_not_null($_REQUEST['code']) ? tep_db_prepare_input($_REQUEST['code']) : '';
                if (tep_not_null($random_code)) {
                    $get_invitee_email_sql = "	SELECT inviter_imports_contact_email
												FROM " . TABLE_INVITER_IMPORTS . "
												WHERE inviter_imports_id='" . (int) $recid . "'
													AND inviter_imports_code='" . tep_db_input($random_code) . "' ";
                    $get_invitee_email_result_sql = tep_db_query($get_invitee_email_sql);

                    if (tep_db_num_rows($get_invitee_email_result_sql) == 1) {
                        $inv_row = tep_db_fetch_array($get_invitee_email_result_sql);
                        if ($email_address == $inv_row['inviter_imports_contact_email']) {
                            $joined_updated = tep_inviter_joined($recid, $random_code, $email_address);
                        }
                    }
                }
                // M#0000075

                $user_password = tep_encrypt_password($password);

                $sql_data_array = array('customers_email_address' => $email_address,
                    'customers_newsletter' => $newsletter,
                    'customers_password' => $user_password,
                    'customers_firstname' => $firstname,
                    'customers_lastname' => $lastname,
                    'account_activated' => 0,
                    // Mantis # 0000024 @ ************ - Optional new entry fields
//										'customers_country_dialing_code_id' => $phone_country,
//										'customers_telephone' => $contactnumber,
                    'customers_gender' => $gender,
                    'customers_dob' => $dob,
                    'customers_security_start_time' => '0000-00-00'
                );
                tep_db_perform(TABLE_CUSTOMERS, $sql_data_array);
                $customer_id = tep_db_insert_id();

                if ($ask_for_ads_source && tep_not_null($survey_answer)) {
                    $sql_data_array = array('customers_id' => $customer_id,
                        'date_remarks_added' => 'now()',
                        'remarks' => $survey_answer,
                        'remarks_added_by' => $email_address
                    );
                    tep_db_perform(TABLE_CUSTOMERS_REMARKS_HISTORY, $sql_data_array);
                }

                // Mantis # 0000024 @ ************ - Instant Message accounts
                $instantmessageaccounttype = isset($HTTP_POST_VARS['instantmessageaccounttype']) && is_array($HTTP_POST_VARS['instantmessageaccounttype']) ? $HTTP_POST_VARS['instantmessageaccounttype'] : array();
                $instantmessageaccount = isset($HTTP_POST_VARS['instantmessageaccount']) && is_array($HTTP_POST_VARS['instantmessageaccount']) ? $HTTP_POST_VARS['instantmessageaccount'] : array();
                $othersinstantmessageaccounttype = isset($HTTP_POST_VARS['othersinstantmessageaccounttype']) && is_array($HTTP_POST_VARS['othersinstantmessageaccounttype']) ? $HTTP_POST_VARS['othersinstantmessageaccounttype'] : array();
                $othersinstantmessageaccountname = isset($HTTP_POST_VARS['othersinstantmessageaccountname']) && is_array($HTTP_POST_VARS['othersinstantmessageaccountname']) ? $HTTP_POST_VARS['othersinstantmessageaccountname'] : array();
                $othersinstantmessageaccount = isset($HTTP_POST_VARS['othersinstantmessageaccount']) && is_array($HTTP_POST_VARS['othersinstantmessageaccount']) ? $HTTP_POST_VARS['othersinstantmessageaccount'] : array();

                if (sizeof($instantmessageaccounttype) > 0) {
                    for ($im_counter = 0; $im_counter < count($instantmessageaccounttype); $im_counter++) {
                        if (isset($instantmessageaccount[$im_counter])) {
                            $sql_data_array = array('customer_id' => $customer_id,
                                'instant_message_type_id' => $instantmessageaccounttype[$im_counter],
                                'instant_message_userid' => $instantmessageaccount[$im_counter]);
                            tep_db_perform(TABLE_INSTANT_MESSAGE_ACCOUNTS, $sql_data_array);
                        }
                    }
                }

                // Mantis # 0000024 @ ************ - Others Instant Message type
                if (sizeof($othersinstantmessageaccounttype) > 0) {
                    for ($others = 0; $others < count($othersinstantmessageaccounttype); $others++) {
                        if (isset($othersinstantmessageaccount[$others]) && isset($othersinstantmessageaccountname[$others])) {
                            $sql_data_array = array('customer_id' => $customer_id,
                                'instant_message_type_id' => $othersinstantmessageaccounttype[$others],
                                'instant_message_userid' => $othersinstantmessageaccount[$others],
                                'instant_message_remarks' => $othersinstantmessageaccountname[$others]);

                            tep_db_perform(TABLE_INSTANT_MESSAGE_ACCOUNTS, $sql_data_array);
                        }
                    }
                }

                // Mantis # 0000024 @ ************ - Billing address
                if ($zone_id > 0) {
                    $entry_state = '';
                } else {
                    $zone_id = 0;
                    $entry_state = $state;
                }

                $sql_data_array = array('customers_id' => $customer_id,
                    'entry_firstname' => $firstname,
                    'entry_lastname' => $lastname,
                    'entry_gender' => $gender,
                    'entry_street_address' => $billingaddress1,
                    'entry_suburb' => $billingaddress2,
                    'entry_city' => $billingcity,
                    'entry_postcode' => $billingpostcode,
                    'entry_country_id' => $billingcountry,
                    'entry_state' => $entry_state,
                    'entry_zone_id' => $zone_id);
                tep_db_perform(TABLE_ADDRESS_BOOK, $sql_data_array);
                $address_id = tep_db_insert_id();

                tep_db_query("update " . TABLE_CUSTOMERS . " set customers_default_address_id = '" . (int) $address_id . "' where customers_id = '" . (int) $customer_id . "'");

                // Capture customer account creation IP
                $customer_info_data_array = array('customers_info_id' => $customer_id,
                    'customers_info_number_of_logons' => 0,
                    'customers_info_date_account_created' => 'now()',
                    'customers_info_account_created_ip' => tep_get_ip_address(),
                    'customers_info_account_created_from' => (int) SITE_ID,
                    'customer_info_selected_country' => $country,
                    'customer_info_selected_language_id' => $languages_id
                );
                tep_db_perform(TABLE_CUSTOMERS_INFO, $customer_info_data_array);

                $customer_select_sql = "	SELECT customers_id, customers_firstname, customers_default_address_id, customers_groups_id, customers_login_sites, customers_dob, customers_gender, customers_telephone
											FROM " . TABLE_CUSTOMERS . "
											WHERE customers_status = '1' AND customers_email_address = '" . tep_db_input($email_address) . "' AND FIND_IN_SET('0', customers_login_sites)";
                $customer_result_sql = tep_db_query($customer_select_sql);
                $customer_row = tep_db_fetch_array($customer_result_sql);

                if (SESSION_RECREATE == 'True') {
                    tep_session_recreate();
                }

                // customer_id must be store in string type
                $customer_id = $customer_row['customers_id'];
                $_SESSION['customer_default_address_id'] = $customer_row['customers_default_address_id'];
                $customer_first_name = $customer_row['customers_firstname'];
                $_SESSION['customers_groups_id'] = $customers_groups_id = $customer_row['customers_groups_id'];

                tep_session_register('customer_id');
                tep_session_register('customer_first_name');

                $sso_token = oauth::addSSOToken($customer_id);
                $cookieLifeTime = 0; //time() + 86400
                tep_setcookie('ogm[si]', $customer_id, $cookieLifeTime, $cookie_path, $cookie_domain);
                tep_setcookie('ogm[st]', $sso_token, $cookieLifeTime, $cookie_path, $cookie_domain);
                $_SESSION['sso_token'] = $sso_token;
                // comment: uncomment below 2 for future use address book
                //tep_session_register('phone_country_id');
                //tep_session_register('customer_zone_id');

                tep_db_query("update " . TABLE_CUSTOMERS_INFO . " set customers_info_date_of_last_logon = now(), customers_info_number_of_logons = customers_info_number_of_logons+1 where customers_info_id = '" . (int) $customer_id . "'");

                $customer_login_sql_data_array = array('customers_id' => $customer_id,
                    'customers_login_date' => 'now()',
                    'customers_login_ua_info' => (isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : getenv('HTTP_USER_AGENT')),
                    'customers_login_ip' => tep_get_ip_address());
                tep_db_perform(TABLE_CUSTOMERS_LOGIN_IP_HISTORY, $customer_login_sql_data_array);

                // This will create the connection to user FB account if user FB ID exist
                $ogm_fb_obj->create_connection($customer_id);

                // restore cart contents
                $cart->restore_contents();

                $stored_customer_query = tep_db_query("select last_page_url, last_cpath from " . TABLE_CUSTOMERS_LAST_CPATH . " where customers_id = '" . (int) $customer_id . "'");
                $stored_customer = tep_db_fetch_array($stored_customer_query);

                if ($oauth_login) {
                    switch ($oauth_login) {
                        case 'oauth':
                            oauth::Oauth_login_success();
                            break;
                        case 'redirect_url':
                            $redirect_url = $_SESSION['login_redirect_url'];
                            unset($_SESSION['login_redirect_url']);
                            tep_redirect($redirect_url);
                            break;
                    }
                } else {
                    if (tep_not_null($_REQUEST['baseURL'])) {
                        if (preg_match('/community/', $_REQUEST['baseURL']) || preg_match('/space.offgamers.com/', $_REQUEST['baseURL'])) {
                            $path = $_REQUEST['baseURL'];
                        } else {
                            $path = str_replace(":", "", $_REQUEST['baseURL']);
                        }
                    } else {
                        $path = tep_href_link(FILENAME_CREATE_ACCOUNT_SUCCESS);
                    }

                    tep_redirect($path);
                }
            } else {
                tep_redirect(tep_href_link(FILENAME_CREATE_ACCOUNT, 'action=create_account_failed'));
            }

            break;
    }
}

$newsletter_group_array = array();

$newsletter_group_select_sql = "SELECT newsletters_groups_id, newsletters_groups_name
								FROM " . TABLE_NEWSLETTERS_GROUPS . "
								WHERE module = 'newsletter'
									AND newsletters_groups_id = '" . tep_db_input(GENERAL_NEWSLETTER_ID) . "'";
$newsletter_group_result_sql = tep_db_query($newsletter_group_select_sql);
while ($newsletter_group_row = tep_db_fetch_array($newsletter_group_result_sql)) {
    //$newsletter_group_array[$newsletter_group_row['newsletters_groups_id']] = $newsletter_group_row['newsletters_groups_name'];
    $newsletter_group_array[$newsletter_group_row['newsletters_groups_id']] = ENTRY_NEWSLETTER_TEXT;
}

$content = CONTENT_CREATE_ACCOUNT;
$javascript = $content . '.js';

require(DIR_WS_TEMPLATES . TEMPLATENAME_MAIN_PAGE);
require(DIR_WS_INCLUDES . 'application_bottom.php');
?>