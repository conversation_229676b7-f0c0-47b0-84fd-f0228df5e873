<?php
header("Cache-Control: no-cache");
header("Cache-Control: post-check=0,pre-check=0");
header("Cache-Control: max-age=0");
header("Pragma: no-cache");
header('Content-Type: text/html');

//// Prevent direct access from Web URL
if (!isset($_SERVER['argv']) || !isset($_SERVER['argv'][1])) {
	exit;
} else {
	$permission_code = $_SERVER['argv'][1];
}
if ($permission_code != 'kj2601ed#%337c7c') exit;

require('includes/application_top.php');

ini_set("memory_limit", "64M");
tep_set_time_limit(0);

if (!defined('DIR_FS_CATALOG_MODULES')) {
	define('DIR_FS_CATALOG_MODULES',DIR_WS_MODULES);
}
require_once(DIR_WS_LANGUAGES . 'english/checkout_process.php');

// supported PG
$supported_pg_array = array('64', '142', '112', '147', '180', '268');

// counter => minutes
$counter_control = array(	'0' => 10,
							'1' => 15,
							'2' => 30,
							'3' => 60,
							'4' => 120);
$MAX_COUNTER_CONTROL = count($counter_control);

$cron_process_checking_select_sql = "	SELECT cron_process_track_in_action, cron_process_track_start_date, cron_process_track_start_date < DATE_SUB(NOW(), INTERVAL 5 MINUTE) AS overdue_process, cron_process_track_start_date < DATE_SUB(NOW(), INTERVAL 30 MINUTE) AS long_process, cron_process_track_failed_attempt 
										FROM " . TABLE_CRON_PROCESS_TRACK . "
										WHERE cron_process_track_filename = 'cron_order_payment.php'";
$cron_process_checking_result_sql = tep_db_query($cron_process_checking_select_sql);

if ($cron_process_checking_row = tep_db_fetch_array($cron_process_checking_result_sql)) {
	if ($cron_process_checking_row['cron_process_track_in_action'] == '1') {
		if ($cron_process_checking_row['overdue_process'] == 1) {
			$cron_process_attempt_update_sql = "UPDATE " . TABLE_CRON_PROCESS_TRACK . " 
												SET cron_process_track_failed_attempt = cron_process_track_failed_attempt + 1 
												WHERE cron_process_track_filename = 'cron_order_payment.php'";
			tep_db_query($cron_process_attempt_update_sql);
		} else {
			mail("<EMAIL>", "[OFFGAMERS] Cronjob Failed", 'Pending order payment status cronjob failed at ' . $cron_process_checking_row['cron_process_track_start_date'],
			     "From: <EMAIL>\r\n" .
			     "X-Mailer: PHP/" . phpversion());
		}
	
        if ($cron_process_checking_row['long_process'] == 1) {  // Prevent cronjob keep awaiting
            $cron_process_update_sql = "UPDATE " . TABLE_CRON_PROCESS_TRACK . " 
                                        SET cron_process_track_in_action=1, 
                                            cron_process_track_start_date=now(),
                                            cron_process_track_failed_attempt=0 
                                        WHERE cron_process_track_filename = 'cron_order_payment.php'";
            tep_db_query($cron_process_update_sql);
        } else {
            exit;
        }
	} else {
		$cron_process_update_sql = "UPDATE " . TABLE_CRON_PROCESS_TRACK . " 
									SET cron_process_track_in_action=1, 
										cron_process_track_start_date=now(),
										cron_process_track_failed_attempt=0 
									WHERE cron_process_track_filename = 'cron_order_payment.php'";
		tep_db_query($cron_process_update_sql);
	}
	
	$remove_non_pending_array = array();
	$remove_non_pending_select_sql = "	SELECT cops.orders_id
										FROM " . TABLE_CRON_ORDERS_PAYMENT_STATUS . " AS cops 
										INNER JOIN " . TABLE_ORDERS . " AS o 
											ON cops.orders_id = o.orders_id
												AND o.orders_status != '1'";
	$remove_non_pending_result_sql = tep_db_query($remove_non_pending_select_sql);
	while ($remove_non_pending_row = tep_db_fetch_array($remove_non_pending_result_sql)) {
		$remove_non_pending_array[] = $remove_non_pending_row['orders_id'];
	}
	if (count($remove_non_pending_array)) {
		$delete_non_pending_sql = "	DELETE FROM " . TABLE_CRON_ORDERS_PAYMENT_STATUS . "
									WHERE orders_id IN ('".implode("','",$remove_non_pending_array)."')";
		tep_db_query($delete_non_pending_sql);
	}
	
	$cron_orders_counter = 0;
	$cron_orders_array = array();
	$cron_orders_select_sql = "	SELECT cops.orders_id, cops.counter, cops.check_date 
								FROM " . TABLE_CRON_ORDERS_PAYMENT_STATUS . " as cops 
								WHERE cops.counter <= '".(int)$MAX_COUNTER_CONTROL."'
								ORDER BY cops.check_date";
	$cron_orders_result_sql = tep_db_query($cron_orders_select_sql);
	while ($cron_orders_row = tep_db_fetch_array($cron_orders_result_sql)) {
		echo "<pre>";print_r($cron_orders_row);echo "<BR><BR>";
		$time_diff_array = get_time_difference($cron_orders_row['check_date'], date("Y-m-d H:i:s"));
		if (isset($counter_control[$cron_orders_row['counter']])) {
			if ((((int)$time_diff_array['days'] * 1440) + ((int)$time_diff_array['hours'] * 60) + (int)$time_diff_array['minutes']) >= $counter_control[$cron_orders_row['counter']]) {
				echo "- Qualified<BR>";
				$cron_orders_array[$cron_orders_row['orders_id']] = $cron_orders_row['counter'];
				$cron_orders_payment_status_data_sql = array(	'counter' => ((int)$cron_orders_row['counter']+1), 
																'check_date' => 'now()');
				tep_db_perform(TABLE_CRON_ORDERS_PAYMENT_STATUS, $cron_orders_payment_status_data_sql, 'update', " orders_id = '".$cron_orders_row['orders_id']."'");
				$cron_orders_counter++;
			}
		}
		if ($cron_orders_counter>=30) break;
	}
	
	if (count($cron_orders_array)) {
		include_once(DIR_WS_CLASSES . 'object_info.php');
		include_once(DIR_WS_CLASSES . 'currencies.php');
		include_once(DIR_WS_CLASSES . 'anti_fraud.php');
		include_once(DIR_WS_CLASSES . 'log.php');
		include_once(DIR_WS_CLASSES . 'order.php');
		include_once(DIR_WS_FUNCTIONS . 'custom_product_info.php');
		
		$log_object = new log_files('system');
		$currencies = new currencies();
		
		require_once(DIR_WS_CLASSES . 'payment_methods.php');
		
		$pm_array = array();
		$payment_info_array = array();
		$pm_sql = "	SELECT payment_methods_id, payment_methods_filename 
					FROM " . TABLE_PAYMENT_METHODS . "
					WHERE payment_methods_parent_id ='0'
						AND payment_methods_receive_status_mode = '1'";
		$pm_result = tep_db_query($pm_sql);
		while ($pm_row = tep_db_fetch_array($pm_result)) {
			$module_class = substr($pm_row['payment_methods_filename'], 0, strrpos($pm_row['payment_methods_filename'], '.'));
			$pm_array[$pm_row['payment_methods_id']] = $module_class;
			if (!tep_class_exists($module_class)) {
				if (file_exists(DIR_WS_MODULES . 'payment/'.$module_class . '.php')) {
					include_once(DIR_WS_MODULES . 'payment/'.$module_class . '.php');
				}
			}
		}
		
		$orders_pm_obj = '';
		$pm_is_newed_class_array = array();
		
		$orders_select_sql = "	SELECT o.orders_id, o.payment_methods_id, o.payment_methods_parent_id
								FROM " . TABLE_ORDERS . " as o 
								WHERE o.orders_id IN ('".implode("','", array_keys($cron_orders_array))."')
									AND o.orders_status = '1'";
		$orders_result_sql = tep_db_query($orders_select_sql);
		while ($orders_row = tep_db_fetch_array($orders_result_sql)) {
			if (in_array($orders_row['payment_methods_parent_id'], $supported_pg_array)) {
				if (!isset($pm_is_newed_class_array[$orders_row['payment_methods_id']])) {
					echo '$payment_info_array['.$orders_row['payment_methods_id'].'] = new '.$pm_array[$orders_row['payment_methods_parent_id']].'('.$orders_row['payment_methods_id'].');<BR>';
					eval('$payment_info_array['.$orders_row['payment_methods_id'].'] = new '.$pm_array[$orders_row['payment_methods_parent_id']].'('.$orders_row['payment_methods_id'].');');
					$pm_is_newed_class_array[$orders_row['payment_methods_id']] = 1;
				}
				
				$orders_pm_obj = clone $payment_info_array[$orders_row['payment_methods_id']];
				
				if (isset($orders_pm_obj) && method_exists($orders_pm_obj, 'check_trans_status')) {
					$orders_pm_obj->payment_methods_id = $orders_row['payment_methods_id'];
					$orders_pm_obj->payment_methods_parent_id = $orders_row['payment_methods_parent_id'];
					$orders_pm_obj->connect_via_proxy = false;
					$orders_pm_obj->check_trans_status_flag = false;
					
					$payment_status = $orders_pm_obj->check_trans_status($orders_row['orders_id']);
					
					if ($orders_pm_obj->check_trans_status_flag) {
						if (file_exists($orders_pm_obj->get_ipn_class_file())) {
							
							require_once($orders_pm_obj->get_ipn_class_file());
							
							if (file_exists(DIR_WS_LANGUAGES . 'english/modules/payment/' . $pm_array[$orders_row['payment_methods_parent_id']] . '.php')) {
								require_once(DIR_WS_LANGUAGES . 'english/modules/payment/' . $pm_array[$orders_row['payment_methods_parent_id']] . '.php');
							}
							
							switch ($pm_array[$orders_row['payment_methods_parent_id']]) {
								case 'moneybookers':
									$pm_class_obj = new mb_ipn(array());
									$pm_class_obj->process_this_order($orders_row['orders_id'], $orders_pm_obj, $orders_pm_obj->order_processing_status);
									break;
								case 'bibit':
									$bibit_proceed = false;
									
									$bibit_payment_method_select_sql = "SELECT bibit_payment_method 
																		FROM " . TABLE_BIBIT . "
																		WHERE orders_id = '".tep_db_input($orders_row['orders_id'])."'";
									$bibit_payment_method_result_sql = tep_db_query($bibit_payment_method_select_sql);
									
									if ($bibit_payment_method_row = tep_db_fetch_array($bibit_payment_method_result_sql)) {
                                        $bibit_mapping_array = array(	'ECMC_CREDIT-SSL' => 'ECMC-SSL',
                                                                        'ECMC_DEBIT-SSL' => 'ECMC-SSL',
                                                                        'ECMC_COMMERCIAL_CREDIT-SSL' => 'ECMC-SSL',
                                                                        'ECMC_COMMERCIAL_DEBIT-SSL' => 'ECMC-SSL',
                                                                        'VISA_CREDIT-SSL' => 'VISA-SSL',
                                                                        'VISA_DEBIT-SSL' => 'VISA-SSL',
                                                                        'VISA_COMMERCIAL_CREDIT-SSL' => 'VISA-SSL',
                                                                        'VISA_COMMERCIAL_DEBIT-SSL' => 'VISA-SSL');
										$bibit_return_system_code = $bibit_payment_method_row['bibit_payment_method'];
										if (isset($bibit_mapping_array[$bibit_return_system_code])) {
											$bibit_return_system_code = $bibit_mapping_array[$bibit_return_system_code];
										}
										
										$bibit_actual_payment_methods_select_sql = "SELECT pm.payment_methods_id 
																					FROM " . TABLE_PAYMENT_METHODS . " AS pm
																					INNER JOIN " . TABLE_PAYMENT_METHODS . " AS pg 
																						ON (pm.payment_methods_parent_id = pg.payment_methods_id 
																							AND pg.payment_methods_filename = 'bibit.php') 
																					WHERE pm.payment_methods_code = '" . tep_db_input($bibit_return_system_code) . "' 
																					LIMIT 1";
										$bibit_actual_payment_methods_result_sql = tep_db_query($bibit_actual_payment_methods_select_sql);
										if ($bibit_actual_payment_methods_row = tep_db_fetch_array($bibit_actual_payment_methods_result_sql)) {
											if ($orders_row['payment_methods_id'] == $bibit_actual_payment_methods_row['payment_methods_id']) {
												$bibit_proceed = true;
											}
										}
									}
									
									if (!$bibit_proceed) {
										continue 2;
									}
									
									$pm_class_obj = new bibit_ipn(array());
									$pm_class_obj->process_this_order($orders_row['orders_id'], $orders_pm_obj, $orders_pm_obj->order_processing_status, $payment_status['lastEvent']);
									break;
								default:
									eval('$pm_class_obj = new ' . $pm_array[$orders_row['payment_methods_parent_id']] . '_ipn(array());');
									$pm_class_obj->process_this_order($orders_row['orders_id'], $orders_pm_obj, $orders_pm_obj->order_processing_status);
									break;
							}
//							echo "DELETE FROM " . TABLE_CRON_ORDERS_PAYMENT_STATUS . " WHERE orders_id = '".$orders_row['orders_id']."'<BR>";
							tep_db_query("DELETE FROM " . TABLE_CRON_ORDERS_PAYMENT_STATUS . " WHERE orders_id = '".$orders_row['orders_id']."'");
//							echo "<br>";
						}
					} else {
//						echo $orders_row['orders_id'] . ' - Fail.<BR>';
					}
				} else {
					tep_db_query("DELETE FROM " . TABLE_CRON_ORDERS_PAYMENT_STATUS . " WHERE orders_id = '".$orders_row['orders_id']."'");
				}
			} else {
				tep_db_query("DELETE FROM " . TABLE_CRON_ORDERS_PAYMENT_STATUS . " WHERE orders_id = '".$orders_row['orders_id']."'");		
			}
		}
	}
	
	$remove_non_pending_select_sql = "	DELETE FROM " . TABLE_CRON_ORDERS_PAYMENT_STATUS . " 
										WHERE counter >= '".(int)$MAX_COUNTER_CONTROL."'";
	tep_db_query($remove_non_pending_select_sql);
	
	$unlock_cron_process_sql = "UPDATE " . TABLE_CRON_PROCESS_TRACK . " 
								SET cron_process_track_in_action=0 
								WHERE cron_process_track_filename = 'cron_order_payment.php'";
	tep_db_query($unlock_cron_process_sql);
}

/**
 * Function to calculate date or time difference.
 * 
 * Function to calculate date or time difference. Returns an array or
 * false on error.
 *
 * <AUTHOR> de Silva                             <<EMAIL>>
 * @copyright    Copyright &copy; 2005, J de Silva
 * @link         http://www.gidnetwork.com/b-16.html    Get the date / time difference with PHP
 * @param        string                                 $start
 * @param        string                                 $end
 * @return       array
 */
function get_time_difference( $start, $end ) {
    $uts['start'] = strtotime( $start );
    $uts['end'] = strtotime( $end );
    if( $uts['start']!==-1 && $uts['end']!==-1 ) {
        if( $uts['end'] >= $uts['start'] ) {
            $diff = $uts['end'] - $uts['start'];
            if( $days=intval((floor($diff/86400))) )
                $diff = $diff % 86400;
            if( $hours=intval((floor($diff/3600))) )
                $diff = $diff % 3600;
            if( $minutes=intval((floor($diff/60))) )
                $diff = $diff % 60;
            $diff = intval( $diff );            
            return( array('days'=>$days, 'hours'=>$hours, 'minutes'=>$minutes, 'seconds'=>$diff) );
        } else {
            trigger_error( "Ending date/time is earlier than the start date/time", E_USER_WARNING );
        }
    } else {
        trigger_error( "Invalid date/time data detected", E_USER_WARNING );
    }
    return( false );
}

// get admin member's group name
function tep_get_admin_group_name($admin_email) {
	$admin_group_select_sql = "	SELECT ag.admin_groups_name
								FROM " . TABLE_ADMIN . " AS a
								INNER JOIN " . TABLE_ADMIN_GROUPS . " AS ag
									ON a.admin_groups_id = ag.admin_groups_id 
								WHERE a.admin_email_address = '" . tep_db_input($admin_email) . "'";
	$admin_group_result_sql = tep_db_query($admin_group_select_sql);
	if ($admin_group_row = tep_db_fetch_array($admin_group_result_sql)) {
		return $admin_group_row['admin_groups_name'];
	}
	return '';
}

?>