<?
/*
  	$Id: custom_product_info.php,v 1.27 2012/07/13 05:35:08 weesiong Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2003 osCommerce
	
  	Released under the GNU General Public License
*/

header("Cache-Control: no-cache");
header("Cache-Control: post-check=0,pre-check=0");
header("Cache-Control: max-age=0");
header("Pragma: no-cache");

require('includes/application_top.php');
require(DIR_WS_LANGUAGES . $language . '/' . FILENAME_CUSTOM_PRODUCT_INFO);

if (!$const_i_am_allowed_here) {
	tep_redirect(tep_href_link(FILENAME_DEFAULT, '', 'SSL'));
}

$products_id = (int)$_GET['products_id'];
$product_tax = tep_get_product_tax_class($products_id);

// verify categories and product permission within region
if (tep_not_null($products_id)) {
	if (!tep_check_product_region_permission($products_id, false)) {
		require(DIR_WS_LANGUAGES . $language . '/' . FILENAME_DEFAULT);
		$const_i_am_allowed_here = false;
		$content = CONTENT_ERROR_REGION;
	}
}

$confirm = (int)$_GET['confirm'];

$custom_cart = array();

if ($const_i_am_allowed_here) {
	if ((int)$_GET['index'] > 0) {
		$index = (int)$_GET['index'] - 1;
		
		if (isset($HTTP_POST_VARS["NonJSUpdate"])) {
			$custom_cart = array( 	'info' => array('current_level' => $HTTP_POST_VARS["cmbCurrentLevel"],
													'desired_level' => $HTTP_POST_VARS["cmbDesiredLevel"],
													'current_class' => $HTTP_POST_VARS["cmbCurrentClass"],
													'desired_class' => $HTTP_POST_VARS["cmbDesiredClass"]
													)
								);
			$custom_cart['option'] = array();
			if (count($HTTP_POST_VARS["cp_option"])) {
				foreach ($HTTP_POST_VARS["cp_option"] as $key => $val) {
					$custom_cart['option'][$key] = tep_db_prepare_input($val);
				}
			}
		} else {
			$custom_cart = $cart->contents[$products_id]['custom'][$cart->get_custom_prd_type($products_id)][$index]['content'];
		}
		
		if (sizeof($custom_cart) <= 0) {
			$index = null;
		}
	} else {
		$index = null;
		$custom_cart["option"] = $HTTP_POST_VARS["cp_option"];
	}
	
	if (DISPLAY_CART == 'true') {
		$goto =  FILENAME_SHOPPING_CART;
		$parameters = array('action', 'cPath', 'products_id', 'index');
	} else {
	  	$goto = basename($PHP_SELF);
	  	$parameters = array('action', 'index');
	}
	
	$active_product = true;
	if (is_array($cPath_array) && count($cPath_array)) {
	  	$inactive_cat_select_sql = "SELECT COUNT(categories_id) as inactive_cat 
	  								FROM " . TABLE_CATEGORIES . "
	  								WHERE categories_id IN ('" . implode("', '", $cPath_array) . "') 
	  									AND categories_status = 0";
	  	$inactive_cat_result_sql = tep_db_query($inactive_cat_select_sql);
		$inactive_cat_row = tep_db_fetch_array($inactive_cat_result_sql);
	  	
	  	if ($inactive_cat_row['inactive_cat'] > 0)	$active_product = false;
	}
	
	if ($active_product) {
		$product_info_select_sql = "SELECT p.products_id, p.products_model, p.products_quantity, 
											p.products_bundle, p.products_bundle_dynamic, p.products_bundle_dynamic_qty, 
											p.products_price, p.products_tax_class_id, p.products_date_added, 
											p.products_date_available, p.manufacturers_id, p.custom_products_type_id, 
											p.products_display  
									FROM " . TABLE_PRODUCTS . " p  
									WHERE p.products_status = '1' 
										AND p.products_id = '" . $products_id . "'";
		$product_info_result_sql = tep_db_query($product_info_select_sql);
		
		if ($product_info = tep_db_fetch_array($product_info_result_sql)) {
			if (!tep_not_null($product_info['products_bundle_dynamic']) && !tep_not_null($product_info['products_bundle']) && $product_info['products_display'] != '1') {
				$active_product = false;
			} else {
				$prod_desc_array = tep_get_products_complete_description($product_info['products_id']);
				$product_info = array_merge($product_info, $prod_desc_array);
			}
		} else {
			$active_product = false;
		}
	}
	
	if ($active_product) {
		if (strtolower($_SERVER['REQUEST_METHOD']) == 'post' && ($_REQUEST['action']=='buy_pwl' || $_REQUEST['action']=='buy_direct_topup') && !isset($HTTP_POST_VARS["NonJSUpdate"])) {
//			$error = false;
//
//            include_once(DIR_WS_CLASSES . 'direct_topup.php');
//			$direct_topup_obj = new direct_topup();
//			
//			if ((int)$product_info['custom_products_type_id']==1) {
//				//--Start Power Levelling
//				$level_dependent_package = true;
//				
//				$range_resource_array = tep_get_bracket_range((int)$HTTP_POST_VARS['datapool_parent_id']);
//
//				if (count($range_resource_array["range"]) < 1) {
//					$current_level = $desired_level = 0;
//					$level_dependent_package = false;
//				} else {
//					$current_level = (int)$HTTP_POST_VARS["cmbCurrentLevel"];
//					$desired_level = (int)$HTTP_POST_VARS["cmbDesiredLevel"];
//                    
//				    if ($current_level < 1) {
//				    	$error = true;
//				    }
//				    
//				    if ($desired_level < 1) {
//				    	$error = true;
//				    }
//				    
//				    if ($current_level >= $desired_level) {
//						$error = true;
//					}
//				}
//               
//			    if (!$error) {
//				    $option_html_array = tep_get_options_html($products_id, $desired_level);
//				    $system_option_array = array();
//
//				    if (count($option_html_array)) {
//					    foreach ($option_html_array as $option_key => $option_res) {
//					    	if ($option_res["data_pool_options_required"] == '1' && $option_res["data_pool_options_input_type"] != 999 && $option_res["data_pool_options_input_type"] != 6) {
//					    		if (!isset($HTTP_POST_VARS["cp_option"][$option_key]) || trim($HTTP_POST_VARS["cp_option"][$option_key]) == '') {
//					    			$error = true;     
//					    			break;
//					    		}
//					    	}
//
//                            if ($option_res['data_pool_options_input_type'] == 1 || $option_res['data_pool_options_input_type'] == 2) {
//					    		if (!isset($HTTP_POST_VARS['cp_option'][$option_key]) || tep_not_null(trim($HTTP_POST_VARS['cp_option'][$option_key]))) {
//					    			$HTTP_POST_VARS['cp_option'][$option_key] = htmlentities(trim(tep_db_prepare_input($HTTP_POST_VARS['cp_option'][$option_key])));
//					    		}
//					    	} else if ($option_res["data_pool_options_input_type"] == 999) {
//					    		$system_option_array[$option_res["data_pool_options_input_type"]] = $option_res["data_pool_options_show_supplier"];
//					    	}
//					    }
//					}
//				}
//
//				if (!$error) {
//					$level_label = tep_get_bracket_value((int)$HTTP_POST_VARS['datapool_parent_id'], KEY_PL_LEVEL_LABEL);
//					$class_label = tep_get_bracket_value((int)$HTTP_POST_VARS['datapool_parent_id'], KEY_PL_CLASS_LABEL);
//					
//					tep_calculate_bracket_price((int)$HTTP_POST_VARS['datapool_parent_id'], $current_level, $desired_level, $price, $eta, $msg, $custom_tags);
//					
//					$base_price_value = tep_get_bracket_value((int)$HTTP_POST_VARS['datapool_parent_id'], KEY_PL_BASE_PRICE);
//					$base_time_value = tep_get_bracket_value((int)$HTTP_POST_VARS['datapool_parent_id'], KEY_PL_BASE_TIME);
//					$min_time_value = tep_get_bracket_value((int)$HTTP_POST_VARS['datapool_parent_id'], KEY_PL_MIN_TIME);
//					
//					$total_price_info = tep_calculate_grand_total_price($base_price_value, $base_time_value, $price, $eta, $min_time_value, $option_html_array, $HTTP_POST_VARS["cp_option"]);
//
//					$final_array['info'] = array('current_level' => $current_level,
//												 'desired_level' => $desired_level,
//												 'current_class' => (int)$HTTP_POST_VARS['cmbCurrentClass'],
//												 'desired_class' => (int)$HTTP_POST_VARS['cmbDesiredClass']
//												); 
//					
//					$final_array['db_info'] = array();
//					if ($level_dependent_package) {
//						$final_array['db_info']['current_level'] = array('label' => sprintf(TEXT_CURRENT_LABEL, $level_label), 'value' => tep_get_level_alias((int)$HTTP_POST_VARS['datapool_parent_id'], $current_level) . "##1##"); // Will get setting from admin page, in future
//						$final_array['db_info']['desired_level'] = array('label' => sprintf(TEXT_DESIRED_LABEL, $level_label), 'value' => tep_get_level_alias((int)$HTTP_POST_VARS['datapool_parent_id'], $desired_level) . "##1##"); // Will get setting from admin page, in future
//					}
//					
//					if ((int)$HTTP_POST_VARS['cmbCurrentClass'] > 0) {
//						$final_array['db_info']['current_class'] = array('label' => sprintf(TEXT_CURRENT_LABEL, $class_label), 'value' => tep_get_level_name_path((int)$HTTP_POST_VARS['cmbCurrentClass'], ' > '));
//					}
//					
//					if ((int)$HTTP_POST_VARS['cmbDesiredClass'] > 0) {
//						$final_array['db_info']['desired_class'] = array('label' => sprintf(TEXT_DESIRED_LABEL, $class_label), 'value' => tep_get_level_name_path((int)$HTTP_POST_VARS['cmbDesiredClass'], ' > '));
//					}
//					
//					if (count($custom_tags) > 0) {
//						foreach ($custom_tags as $custom_bracket_tags_res) {
//							$final_array['db_info'][$custom_bracket_tags_res['key']] = array('label' => $custom_bracket_tags_res["display_label"], 'value' => $custom_bracket_tags_res["value"]);
//						}
//					}
//					
//					if (count($range_resource_array) > 0) {
//						$final_array['db_bracket_info']['cp_bracket'] = array('label' => 'cp_bracket', 'value' => serialize($range_resource_array));
//					}
//				}
//			} else {
//				//--Start CDKey
//				$total_price_info = array();
//				$total_price_info['price'] = $product_info['products_price'];
//				$total_price_info['price_text'] = null;
//				$total_price_info['eta'] = null;
//				$total_price_info['eta_text'] = null;
//				$msg = '';
//				if ($error) {} 
//			} //--End CDKey
//			
//			// Storing label for all custom types.
//			$final_array['option'] = array();
//			if (count($HTTP_POST_VARS["cp_option"])) {
//				foreach ($HTTP_POST_VARS["cp_option"] as $key => $val) {
//					$final_array['option'][$key] = tep_db_prepare_input($val);
//					$sel_option_info_array = tep_grab_option_title_and_value($option_html_array, $key, $val);
//					
//					if ($sel_option_info_array != false && is_array($sel_option_info_array)) {
//						$final_array['db_info']['opt_'.$key] = array('label' => $sel_option_info_array['label'], 'value' => $sel_option_info_array['value'], 'show_supplier' => $option_html_array[$key]["data_pool_options_show_supplier"], 'class' => $option_html_array[$key]["data_pool_options_class"]);
//					}
//				}
//			}
//			
//			$final_array['calculated'] = array(	'price' => $total_price_info["price"],
//												'price_text' => $total_price_info["price_text"],
//											   	'eta' => $total_price_info["eta"],
//											   	'eta_text' => $total_price_info["eta_text"],
//											   	'show_supplier' => $system_option_array[999],
//											   	'msg' => $msg
//											  );
//                                            
//			if (!is_null($index)) {
//				$cart->remove($products_id, $index);
//				$cart->set_custom_product_index($index);
//			} else {
//				$cart->set_custom_product_index(-1);
//			}
//			
//			$cart->set_custom_product_content($final_array);
//			
//			$buyqty = ($_REQUEST['buyqty']? $_REQUEST['buyqty'] : '1');
			
//			if ($publishers_id = $direct_topup_obj->check_is_supported_by_direct_top_up($products_id)) {
//				$games_mapping_select_sql = "	SELECT pg.publishers_game
//												FROM " . TABLE_PUBLISHERS_GAMES . " AS pg 
//												INNER JOIN " . TABLE_PUBLISHERS_PRODUCTS . " AS pp
//													ON pg.publishers_games_id = pp.publishers_games_id
//												WHERE pg.publishers_id = '".(int)$publishers_id."'
//													AND pp.products_id = '".(int)$products_id."'";
//				$games_mapping_result_sql = tep_db_query($games_mapping_select_sql);
//				$games_mapping_row = tep_db_fetch_array($games_mapping_result_sql);
//				if ($direct_topup_obj->validate_game_acc($publishers_id, array_merge($_REQUEST['game_info'],array('game'=> $games_mapping_row['publishers_game'])))) {
//					$cart->add_cart($products_id, $buyqty, '', true, '','', true, $_REQUEST['game_info']);
//				} else {
					$messageStack->add_session('cart_failure', ERROR_INVALID_TOP_UP_ACCOUNT, 'error');
					tep_redirect(tep_href_link(basename($PHP_SELF), tep_get_all_get_params(array('action'))));
//				}
//			} else {
//				$cart->add_cart($products_id, $buyqty, '', true, '', '', true);
//			}
			
//			if (!is_null($index)) {
//				$messageStack->add_session('cart_success', sprintf(SUCCESS_DYNAMIC_CART_UPDATED, tep_href_link(FILENAME_SHOPPING_CART)), 'success');
//			} else {
//				$messageStack->add_session('cart_success', sprintf(SUCCESS_DYNAMIC_CART_ADDED, tep_href_link(FILENAME_SHOPPING_CART)), 'success');
//			}
//			
//			//General error handling.
//			if ($error) {
//				$messageStack->add_session('cart_failure', TEXT_MSG_ADD_TO_CART_FAILURE, 'error');
//				tep_redirect(tep_href_link(basename($PHP_SELF), tep_get_all_get_params(array('action', 'products_id'))));
//			} else {
//				if (!is_null($index)) {
//					tep_redirect(tep_href_link($goto, tep_get_all_get_params($parameters) . "index=".($index+1)));
//				} else {
//					tep_redirect(tep_href_link($goto, tep_get_all_get_params($parameters)));
//				}	
//			}
			exit;
			
		} else {
			if ($products_id > 0) {
				$parent_id = $products_id;
				/*
				$shopping_mode = tep_get_custom_product_add_to_cart_mode($products_id);
				if ($shopping_mode['mode'] == '1') {	// Add directly to cart
					$cart->set_custom_product_content($final_array);
					$cart->add_cart($products_id, '1', '', true, '', '', true);
					if (!is_null($index))
						tep_redirect(tep_href_link($goto, tep_get_all_get_params($parameters)) . "&index=".($index+1));
					else
						tep_redirect(tep_href_link($goto, tep_get_all_get_params($parameters)));
				} else {
				*/
					$data_pool_level_root_select_sql = "SELECT data_pool_level_id 
														FROM " . TABLE_DATA_POOL_LEVEL . " 
														WHERE data_pool_level_parent_id=0 AND products_id ='" . $products_id . "'";
					$data_pool_level_root_result_sql = tep_db_query($data_pool_level_root_select_sql);
					if ($data_pool_level_root_row = tep_db_fetch_array($data_pool_level_root_result_sql)) {
						$datapool_parent_id = (int)$data_pool_level_root_row['data_pool_level_id'];
					}
				//}
			}
		}
	}
	
	if (!tep_session_is_registered('customer_id')) 	$navigation->set_snapshot();
	
	//echo "<script language=\"javascript\" src=\"includes/general.js\"></script>";
	
	$content = CONTENT_CUSTOM_PRODUCT_INFO;
}

require(DIR_WS_TEMPLATES . TEMPLATENAME_MAIN_PAGE);

require(DIR_WS_INCLUDES . 'application_bottom.php');
?>