<?
header("Cache-Control: no-cache");
header("Cache-Control: post-check=0,pre-check=0");
header("Cache-Control: max-age=0");
header("Pragma: no-cache");

header('Content-Type: text/xml');

include_once('includes/application_top.php');
include_once(DIR_WS_FUNCTIONS . 'custom_product_info.php');

$action = $HTTP_GET_VARS['action'];

$language_id = (int)$HTTP_GET_VARS['lang'];

if (isset($language_id) && tep_not_null($language_id)) {
	$language_dir_select_sql = "SELECT directory FROM " . TABLE_LANGUAGES . " WHERE languages_id = '" . $language_id . "'";
	$language_dir_result_sql = tep_db_query($language_dir_select_sql);
	$language_dir_row = tep_db_fetch_array($language_dir_result_sql);
	
	if (file_exists(DIR_WS_LANGUAGES . $language_dir_row["directory"] . '.php')) {
		include_once(DIR_WS_LANGUAGES . $language_dir_row["directory"] . '.php');
	}
	
	if (file_exists(DIR_WS_LANGUAGES . $language_dir_row["directory"] . '/custom_product_info.php')) {
		include_once(DIR_WS_LANGUAGES . $language_dir_row["directory"] . '/custom_product_info.php');
	}
	
	if (file_exists(DIR_WS_LANGUAGES . $language_dir_row["directory"] . '/account_history_info.php')) {
		include_once(DIR_WS_LANGUAGES . $language_dir_row["directory"] . '/account_history_info.php');
	}
}

echo '<response>';
if (tep_not_null($action)) {
	
	if ( !isset($_SERVER['HTTP_REFERER']) ||
		(strstr($_SERVER['HTTP_REFERER'], HTTP_SERVER) === FALSE && strstr($_SERVER['HTTP_REFERER'], HTTPS_SERVER) === FALSE)
		) {
		echo "You are not allowed to access from outside";
		echo '</response>';
		exit;
	}

	switch($action) {
		case "start_level_task":
			$data_pool_level_id = (int)$HTTP_GET_VARS['level_id'];
			$sel_start_level = (int)$HTTP_GET_VARS['s_level'];
			
			$cat_id_select_sql = "SELECT products_id FROM " . TABLE_DATA_POOL_LEVEL . " WHERE data_pool_level_id ='" . tep_db_input($data_pool_level_id) . "'";
			$cat_id_result_sql = tep_db_query($cat_id_select_sql);
		    $cat_id_row = tep_db_fetch_array($cat_id_result_sql);
		    $cat_id = $cat_id_row["products_id"];
		    
			$complete_range_array = tep_get_bracket_range($data_pool_level_id);
			$range_mode = $complete_range_array["mode"];
			
			$level_select_sql = "	SELECT CAST(b1.brackets_value as SIGNED) AS level_val, b2.brackets_value AS alias
									FROM " . TABLE_BRACKETS . " AS b1, " . TABLE_BRACKETS . " AS b2 
									WHERE b1.brackets_dependent=b2.brackets_dependent 
										AND b1.data_pool_level_id ='" . tep_db_input($data_pool_level_id) . "' 
										AND b1.brackets_value > " . (int)$sel_start_level . "
										AND b1.brackets_key='pl_end_level' 
										AND b2.brackets_key='pl_level_alias' 
									ORDER BY level_val";
			$level_result_sql = tep_db_query($level_select_sql);
			echo "<desired_selection>";
			$i = 0;
			$previous_val = $sel_start_level;
			while ($level_row = tep_db_fetch_array($level_result_sql)) {
				if ($range_mode=='continuos') {
					if ($i == 0) {	// First loop, need to check this bracket is the 'first bracket' of the whole bracket level. If yes, do not auto generate the rage between the selected start level and this first bracket level.
						if ($sel_start_level >= $complete_range_array["range"][0]["level"]) {
							for ($linking=$previous_val+1; $linking < $level_row['level_val']; $linking++) {
								echo "<option index='".$linking."'><![CDATA[".$linking."]]></option>";
							}
						}
					} else {
						for ($linking=$previous_val+1; $linking < $level_row['level_val']; $linking++) {
							echo "<option index='".$linking."'><![CDATA[".$linking."]]></option>";
						}
					}
				}
				$previous_val = $level_row['level_val'];
				echo "<option index='".$level_row['level_val']."'><![CDATA[".(isset($level_row['alias']) && trim($level_row['alias']) != '' ? $level_row['alias'] : $level_row['level_val'])."]]></option>";
				$i++;
			}
			echo "</desired_selection>";
			
			$starting_path = tep_get_level_name_path($data_pool_level_id, ' > ');
			$level_tree_array = array();
			tep_get_datapool_subtree_array($data_pool_level_id, $level_tree_array, 0, $starting_path);
			
			echo "<class_selection>";
			tep_display_selection_option($level_tree_array, $sel_start_level);
			echo "</class_selection>";
			
			echo "<option_selection><![CDATA[";
			$option_html_array = tep_get_options_html($cat_id, 0);
			if (count($option_html_array)) {
				foreach ($option_html_array as $option_key => $option_res) {
					if ($option_res["data_pool_options_input_type"] == 999) {
						echo '	<table width="100%" cellspacing="2" cellpadding="0">
								<tr><td colspan="2"></td></tr>
								<tr>
									<td width="1" class="storeBoxLine" colspan="2"><img src="images/pixel_trans.gif" border="0" alt="" width="1" height="1"></td>
	      						</tr>
	      						<tr>
      								<td colspan="2">
      									<div id="custom_bracket_tags_div"></div>
      								</td>
      							</tr>
								<tr>
									<td width="40%" valign="top" class="shoppingCartTotal">'.TEXT_OPTION_PRICE.'</td>
									<td valign="top" class="shoppingCartTotal"><div id="quote_price_div"></div></td>
								</tr>
								<tr>
									<td width="40%" valign="top" class="shoppingCartTotal">'.TEXT_OPTION_ETA.'</td>
									<td valign="top" class="shoppingCartTotal"><div id="quote_eta_div"></div></td>
								</tr>
								<tr>
	      							<td width="1" class="storeBoxLine" colspan="2"><img src="images/pixel_trans.gif" border="0" alt="" width="1" height="1"></td>
	      						</tr>
	      						<tr><td colspan="2"></td></tr>
							</table>';
					} else {
                        $products_id = $cat_id;
						$field_resource = tep_draw_option_field($option_key, $option_res);
						$title = $option_res["data_pool_options_title"];
                        if ($option_res["data_pool_options_required"] == 1)	$title .= ' <font color="red" style="padding-left:1px">*</font>';
						echo '	<table width="100%" cellspacing="0" cellpadding="2">
									<tr>
										<td width="40%" valign="top" class="inputLabel">'.$title.'</td>
										<td valign="top" class="inputField">'.$field_resource["field"].'</td>
									</tr>
								</table>';
					}
				}
			}
			echo "]]></option_selection>";
			
			break;
		case "end_level_task":
			$data_pool_level_id = (int)$HTTP_GET_VARS['level_id'];
			$sel_start_level = (int)$HTTP_GET_VARS['s_level'];
			$sel_end_level = (int)$HTTP_GET_VARS['e_level'];
			$sel_current_class = (int)$HTTP_GET_VARS['class_root_id'];
			
			$cat_id_select_sql = "SELECT products_id FROM " . TABLE_DATA_POOL_LEVEL . " WHERE data_pool_level_id ='" . $data_pool_level_id . "'";
			$cat_id_result_sql = tep_db_query($cat_id_select_sql);
		    $cat_id_row = tep_db_fetch_array($cat_id_result_sql);
		    $cat_id = $cat_id_row["products_id"];
			
			$starting_path = tep_get_level_name_path($sel_current_class, ' > ');
			$level_tree_array = array();
			tep_get_datapool_subtree_array($sel_current_class, $level_tree_array, 0, $starting_path);
			
			if ($sel_current_class > 0 && $sel_current_class != $data_pool_level_id) {
				$data_pool_level_select_sql = "SELECT * FROM " . TABLE_DATA_POOL_LEVEL . " WHERE data_pool_level_id = '" . $sel_current_class . "'";
				$data_pool_level_result_sql = tep_db_query($data_pool_level_select_sql);
				if ($data_pool_level_row = tep_db_fetch_array($data_pool_level_result_sql)) {
					$level_tree_array = array (	array(	'id' => (int)$data_pool_level_row['data_pool_level_id'], 
												   		'parent_id' => (int)$data_pool_level_row['data_pool_level_parent_id'],
														'ref_id' => (int)$data_pool_level_row['data_pool_ref_id'],
														'name' => $data_pool_level_row['data_pool_level_name'], 
														'ident' => 0, 
														'path' => $starting_path,
														'data_pool_max_level' => $data_pool_level_row['data_pool_max_level'],
														'data_pool_min_level' => $data_pool_level_row['data_pool_min_level'],
														'data_pool_level_value' => $data_pool_level_row['data_pool_level_value'],
														'data_pool_input_field' => $data_pool_level_row['data_pool_input_field'],
														'data_pool_level_class' => $data_pool_level_row['data_pool_level_class'],
														'data_pool_level_class_name' => $data_pool_level_row['data_pool_level_class_name'],
														'child' => $level_tree_array)
											);
				}
			}
			
			echo "<class_selection>";
			tep_display_selection_option($level_tree_array, $sel_end_level, $sel_current_class);
			echo "</class_selection>";
			
			tep_calculate_bracket_price($data_pool_level_id, $sel_start_level, $sel_end_level, $price, $eta, $msg, $custom_tags);
			
			echo "<option_selection><![CDATA[";
			$option_html_array = tep_get_options_html($cat_id, $sel_end_level);
			if (count($option_html_array)) {
				foreach ($option_html_array as $option_key => $option_res) {
					if ($option_res["data_pool_options_input_type"] == 999) {
						echo '	<table width="100%" cellspacing="2" cellpadding="0">
								<tr><td colspan="2"></td></tr>
								<tr>
									<td width="1" class="storeBoxLine" colspan="2"><img src="images/pixel_trans.gif" border="0" alt="" width="1" height="1"></td>
	      						</tr>
	      						<tr>
      								<td colspan="2">
      									<div id="custom_bracket_tags_div">';
      					
		      			if (count($custom_tags) > 0) {
							$custom_bracket_tags_html = '<table width="100%" cellspacing="0" cellpadding="0">';
			  				foreach ($custom_tags as $custom_bracket_tags_res) {
			  					$custom_bracket_tags_html .= 
			  									'<tr>
													<td width="40%" valign="top" class="inputLabel">'.$custom_bracket_tags_res["display_label"].'</td>
													<td valign="top" class="inputField">'.$custom_bracket_tags_res["value"].'</td>
												</tr>';
			  				}
			  				$custom_bracket_tags_html .= '</table>';
			  				echo $custom_bracket_tags_html;
						}
						
						echo '			</div>
									</td>
								</tr>
								<tr>
									<td width="40%" valign="top" class="shoppingCartTotal">'.TEXT_OPTION_PRICE.'</td>
									<td valign="top" class="shoppingCartTotal"><div id="quote_price_div"></div></td>
								</tr>
								<tr>
									<td width="40%" valign="top" class="shoppingCartTotal">'.TEXT_OPTION_ETA.'</td>
									<td valign="top" class="shoppingCartTotal"><div id="quote_eta_div"></div></td>
								</tr>
								<tr>
	      							<td width="1" class="storeBoxLine" colspan="2"><img src="images/pixel_trans.gif" border="0" alt="" width="1" height="1"></td>
	      						</tr>
	      						<tr><td colspan="2"></td></tr>
							</table>';
					} else {
                        $products_id = $cat_id;
						$field_resource = tep_draw_option_field($option_key, $option_res);
						$title = $option_res["data_pool_options_title"];
                        if ($option_res["data_pool_options_required"] == 1)	$title .= ' <font color="red" style="padding-left:1px">*</font>';
						echo '	<table width="100%" cellspacing="0" cellpadding="2">
									<tr>
										<td width="40%" valign="top" class="inputLabel">'.$title.'</td>
										<td valign="top" class="inputField">'.$field_resource["field"].'</td>
									</tr>
								</table>';
					}
				}
			}
			echo "]]></option_selection>";
			
			echo "<bracket_info>";
			if (trim($price) != '') {
                $product_currency = tep_get_products_field($cat_id, 'products_base_currency');
                $price = $currencies->advance_currency_conversion($price, $product_currency, $_SESSION['currency'], false, 'buy');
                echo "<price><![CDATA[$price]]></price>";
            }
			if (trim($eta) != '')		echo "<eta><![CDATA[$eta]]></eta>";
			if (trim($msg) != '')		echo "<msg><![CDATA[$msg]]></msg>";
			echo "</bracket_info>";
			break;
		case "class_task":
			$data_pool_level_id = (int)$HTTP_GET_VARS['level_id'];
			$sel_end_level = (int)$HTTP_GET_VARS['e_level'];
			$sel_current_class = (int)$HTTP_GET_VARS['class_root_id'];
			
			$starting_path = tep_get_level_name_path($sel_current_class, ' > ');
			$level_tree_array = array();
			tep_get_datapool_subtree_array($sel_current_class, $level_tree_array, 0, $starting_path);
			
			if ($sel_current_class > 0 && $sel_current_class != $data_pool_level_id) {
				$data_pool_level_select_sql = "SELECT * FROM " . TABLE_DATA_POOL_LEVEL . " WHERE data_pool_level_id = '" . $sel_current_class . "'";
				$data_pool_level_result_sql = tep_db_query($data_pool_level_select_sql);
				if ($data_pool_level_row = tep_db_fetch_array($data_pool_level_result_sql)) {
					$level_tree_array = array( array(	'id' => (int)$data_pool_level_row['data_pool_level_id'], 
												   		'parent_id' => (int)$data_pool_level_row['data_pool_level_parent_id'],
														'ref_id' => (int)$data_pool_level_row['data_pool_ref_id'],
														'name' => $data_pool_level_row['data_pool_level_name'], 
														'ident' => 0, 
														'path' => $starting_path,
														'data_pool_max_level' => $data_pool_level_row['data_pool_max_level'],
														'data_pool_min_level' => $data_pool_level_row['data_pool_min_level'],
														'data_pool_level_value' => $data_pool_level_row['data_pool_level_value'],
														'data_pool_input_field' => $data_pool_level_row['data_pool_input_field'],
														'data_pool_level_class' => $data_pool_level_row['data_pool_level_class'],
														'data_pool_level_class_name' => $data_pool_level_row['data_pool_level_class_name'],
														'child' => $level_tree_array)
											);
				}
			}
			
			echo "<class_selection>";
			tep_display_selection_option($level_tree_array, $sel_end_level, $sel_current_class);
			echo "</class_selection>";
			
			break;
		case "list_global_options":
			$data_pool_level_id = (int)$HTTP_GET_VARS['level_id'];
			
			$cat_id_select_sql = "SELECT products_id FROM " . TABLE_DATA_POOL_LEVEL . " WHERE data_pool_level_id ='" . $data_pool_level_id . "'";
			$cat_id_result_sql = tep_db_query($cat_id_select_sql);
		    $cat_id_row = tep_db_fetch_array($cat_id_result_sql);
		    $cat_id = $cat_id_row["products_id"];
		    
			echo "<option_selection><![CDATA[";
			$option_html_array = tep_get_options_html($cat_id, 0);
			if (count($option_html_array)) {
				$preload_price_eta_js = "\nvar cp_option_price = new Array();\n" . 
										"var cp_option_eta = new Array();\n";
				foreach ($option_html_array as $option_key => $option_res) {
					if ($option_res["data_pool_options_input_type"] == 999) {
						echo '	<table width="100%" cellspacing="2" cellpadding="0">
								<tr><td colspan="2"></td></tr>
								<tr>
									<td width="1" class="storeBoxLine" colspan="2"><img src="images/pixel_trans.gif" border="0" alt="" width="1" height="1"></td>
	      						</tr>
								<tr>
									<td width="40%" valign="top" class="shoppingCartTotal">'.TEXT_OPTION_PRICE.'</td>
									<td valign="top" class="shoppingCartTotal"><div id="quote_price_div"></div></td>
								</tr>
								<tr>
									<td width="40%" valign="top" class="shoppingCartTotal">'.TEXT_OPTION_ETA.'</td>
									<td valign="top" class="shoppingCartTotal"><div id="quote_eta_div"></div></td>
								</tr>
								<tr>
	      							<td width="1" class="storeBoxLine" colspan="2"><img src="images/pixel_trans.gif" border="0" alt="" width="1" height="1"></td>
	      						</tr>
	      						<tr><td colspan="2"></td></tr>
							</table>';
					} else {
						$field_resource = tep_draw_option_field($option_key, $option_res);
						
						if (tep_not_null($field_resource["js"])) {
							$preload_price_eta_js .= $field_resource["js"];
						}
						echo '	<table width="100%" cellspacing="0" cellpadding="2">
									<tr>
										<td width="40%" valign="top" class="inputLabel">'.$option_res["data_pool_options_title"].'</td>
										<td valign="top" class="inputLabel">'.$field_resource["field"].'</td>
									</tr>
								</table>';
					}
				}
				echo 	"\n<script>" . $preload_price_eta_js . "</script>";
			}
			echo "]]></option_selection>";
			
			break;

		case "get_cdkey_image":
			$custom_product_id = (int)$HTTP_GET_VARS['cp_id'];
			$img_source = '';
			if (isset($_SESSION['customer_id'])) {
				if (tep_not_null($custom_product_id)) {
					if (tep_is_cb_customer($_SESSION['customer_id'])) {
 						$img_source = TEXT_CDKEY_SUSPENDED_FOR_VIEWING;
 					} else {
						$cdkey_info_select_sql = "	SELECT file_type, custom_products_code_id, file_name, orders_products_id 
													FROM " . TABLE_CUSTOM_PRODUCTS_CODE . " 
													WHERE custom_products_code_id = '".$custom_product_id."'";
						$cdkey_info_result_sql = tep_db_query($cdkey_info_select_sql);
						if ($order_product_id_row = tep_db_fetch_array($cdkey_info_result_sql)) {
							$customer_order_verify_select_sql = "	SELECT o.customers_id 
																	FROM " . TABLE_ORDERS . " AS o 
																	INNER JOIN " . TABLE_ORDERS_PRODUCTS . " AS op 
																		ON o.orders_id=op.orders_id
																	WHERE o.customers_id = '".tep_db_input($_SESSION['customer_id'])."' 
																		AND o.orders_status IN (2, 3) 
																		AND op.orders_products_id = '".tep_db_input($order_product_id_row['orders_products_id'])."'";
							$customer_order_verify_result_sql = tep_db_query($customer_order_verify_select_sql);
							if (tep_db_num_rows($customer_order_verify_result_sql)) {	// This is the cd key for this customer
								if ($order_product_id_row['file_type'] == 'soft') {
									$img_source = tep_get_cdkey_img($custom_product_id);
								} else {
									$img_source = tep_image(tep_href_link('show_image.php', 'keyident='.$custom_product_id));
								}
							}
						}
					}
				}
			}
            echo '<cdkey_image><![CDATA[';
			echo $img_source;
			echo ']]></cdkey_image>';
			
			break;			
			
		case "get_cdkey_image_multiple":
			$custom_product_id = (string)$HTTP_GET_VARS['cp_id'];
			$all_img_are_valid = true;
			$tmp_xml_res_str = '';
			
			$is_cb_customer = tep_is_cb_customer($_SESSION['customer_id']);

			//if (tep_not_null($custom_product_id)) {
			if (tep_not_null($custom_product_id)) {
				$tmp_xml_res_str = '<cdkey_image>';
				
				$cdkey_info_select_sql = "	SELECT file_type, custom_products_code_id, file_name, orders_products_id 
											FROM " . TABLE_CUSTOM_PRODUCTS_CODE . " 
											WHERE custom_products_code_id in (".$custom_product_id.")";
				$cdkey_info_result_sql = tep_db_query($cdkey_info_select_sql);
				while ($cdkey_info_row = tep_db_fetch_array($cdkey_info_result_sql)) {
					$img_source = '';
					
					if ($is_cb_customer) {
						$img_source = TEXT_CDKEY_SUSPENDED_FOR_VIEWING;
					} else {
						if ((int)$cdkey_info_row['orders_products_id']>0) {
							$customer_order_verify_select_sql = "	SELECT o.customers_id 
																	FROM " . TABLE_ORDERS . " AS o 
																	INNER JOIN " . TABLE_ORDERS_PRODUCTS . " AS op 
																		ON o.orders_id=op.orders_id
																	WHERE o.customers_id = '".tep_db_input($_SESSION['customer_id'])."' 
																		AND o.orders_status IN (2, 3) 
																		AND op.orders_products_id = '".(int)$cdkey_info_row['orders_products_id']."'";
							$customer_order_verify_result_sql = tep_db_query($customer_order_verify_select_sql);
							if (tep_db_num_rows($customer_order_verify_result_sql)) {	// This is the cd key for this customer
								if ($cdkey_info_row['file_type'] == 'soft') {
									$img_source = tep_get_cdkey_img($cdkey_info_row['custom_products_code_id']);
								} else {
									$img_source = tep_image(tep_href_link('show_image.php', 'keyident='.$cdkey_info_row['custom_products_code_id']), '');
								}
							} else {
								$all_img_are_valid = false;
								break;
							}
						} else {
							$all_img_are_valid = false;
							break;
						}
					}
					
					$tmp_xml_res_str .= "<image_".$cdkey_info_row['custom_products_code_id'].">";
					$tmp_xml_res_str .= '<![CDATA[';
					$tmp_xml_res_str .= $img_source;
					$tmp_xml_res_str .= ']]>';
					$tmp_xml_res_str .= "</image_".$cdkey_info_row['custom_products_code_id'].">";
				}
				
				$tmp_xml_res_str .= '</cdkey_image>';
				
				if ($all_img_are_valid)	echo $tmp_xml_res_str;
			}
			break;			
		case 'get_hla_detail': // HLA "Able to Login (Yes/No)"
			$first_row = true;
			$orders_products_id = (int)$_GET['product_no'];
			$extra_info_array = array();
			$hla_info_stage_1 = '';
			$hla_info_stage_2 = '';
			$history_html = '';
			
			if (tep_not_null($orders_products_id) && tep_not_null($_SESSION['customer_id'])) {
				$get_history_select_sql = "	SELECT NOW() as nowtime, op.orders_products_history_id, 
												op.delivered_character, DATE_FORMAT(op.date_added, '%d-%m-%y') AS dates, 
												DATE_FORMAT(op.date_added, '%h:%i %p') AS times, 
												op.date_confirm_delivered, op.dispute_comment, op.received, op.rolled_back 
											FROM " . TABLE_ORDERS_PRODUCTS_HISTORY . " AS op
											INNER JOIN " . TABLE_ORDERS . " AS o 
												ON o.orders_id = op.orders_id 
											WHERE op.orders_products_id = '" . $orders_products_id . "' 
												AND o.customers_id = '" . (int)$_SESSION['customer_id'] . "'
											ORDER BY op.date_added DESC";
				$get_history_result_sql = tep_db_query($get_history_select_sql);
				if (tep_db_num_rows($get_history_result_sql)) {
					// update HLA account status, retrieve hla_info_stage_1 and hla_info_stage_2
					$get_hla_info_sql = "	SELECT orders_products_extra_info_key, orders_products_extra_info_value 
											FROM " . TABLE_ORDERS_PRODUCTS_EXTRA_INFO . " 
											WHERE orders_products_id = '" . $orders_products_id . "'";
					$get_hla_info_result = tep_db_query($get_hla_info_sql);
					while ($get_hla_info_row = tep_db_fetch_array($get_hla_info_result)) {
						if (($get_hla_info_row['orders_products_extra_info_key'] == 'products_hla') || 
							($get_hla_info_row['orders_products_extra_info_key'] == 'hla_info_stage_1') || 
							($get_hla_info_row['orders_products_extra_info_key'] == 'hla_info_stage_2')) {
							$extra_info_array[$get_hla_info_row['orders_products_extra_info_key']] = tep_array_unserialize($get_hla_info_row['orders_products_extra_info_value']);
						}
					}
					
					if (tep_not_null($extra_info_array['products_hla']) && tep_not_null($extra_info_array['hla_info_stage_1'])) {
						$viewed_update_sql = "	UPDATE " . TABLE_PRODUCTS_HLA . " 
												SET products_hla_info_viewed = '1' 
												WHERE products_hla_id = '" . tep_db_input($extra_info_array['products_hla']['products_hla_id']) . "'";
						tep_db_query($viewed_update_sql);
						
						foreach ($extra_info_array['hla_info_stage_1'] as $stage_1_key => $stage_1_info) {
							foreach ($stage_1_info as $key => $val) {
								
								if (is_array($val)) {
									foreach ($val as $child_key => $child_val) {
										$hla_info_stage_1 .= ucwords(str_replace('_', ' ', $child_key)) . ': ' . ($child_key == 'extra_comment' ? '<br />' : '') . nl2br($child_val) . '<br />';
									}
								} else {
									$hla_info_stage_1 .= '<b>' . $val . '</b><br />';
								}
							}
							$hla_info_stage_1 .= '<br />';
						}
					}
					
					while ($get_history_row = tep_db_fetch_array($get_history_result_sql)) {
						$rolled_back = $get_history_row['rolled_back'];
						$dates = $get_history_row['dates'];
						$times = $get_history_row['times'];
						$date_confirm_delivered = $get_history_row['date_confirm_delivered'];
						$buyback_request_group_id = $get_history_row['buyback_request_group_id'];
						$op_history_id = $get_history_row['orders_products_history_id'];
						$record = $get_history_row['orders_products_history_record'];
						
						$row_style = 'padding: 0px 10px;';
						if (tep_not_null($record)) {
							$received = false;
						} else if (!tep_not_null($get_history_row['received'])) {
							if ($rolled_back) {
								$received = false;
								$row_style = 'padding: 0px 10px;';
							} else {
								$confirmation_eta = strtotime($date_confirm_delivered);
								$current_datetime = strtotime($get_history_row['nowtime']);
								
								if ($confirmation_eta > $current_datetime) {
									$received = false;
								} else {
									$received = true;
									$dispute = false;
									
									$row_style = 'padding: 0px 10px;color:#999999;';
								}
							}
						} else {
							$received = true;
							if ($get_history_row['received'] == 0) {
								$dispute = true;
							} else if ($get_history_row['received'] == 1) {
								$dispute = false;
								$row_style = 'padding: 0px 10px;color:#999999;';
							}
						}
						
						
						if ($received && !$dispute) {	// After click Yes
							if (tep_not_null($extra_info_array['hla_info_stage_2'])) {
								if (isset($extra_info_array['hla_info_stage_2']['question'])) {
									// for remain OLD structure still working fine
									foreach ($extra_info_array['hla_info_stage_2'] as $key => $value) {
										$hla_info_stage_2 .= ucwords(str_replace('_', ' ', $key)) . ': ' . $value . '<br />';
									}
								} else {
									foreach ($extra_info_array['hla_info_stage_2'] as $stage_2_key => $stage_2_info) {
										foreach ($stage_2_info as $key => $val) {
											
											if (is_array($val)) {
												foreach ($val as $child_key => $child_val) {
													$hla_info_stage_2 .= ucwords(str_replace('_', ' ', $child_key)) . ': ' . $child_val . '<br />';
												}
											} else {
												$hla_info_stage_2 .= '<b>' . $val . '</b><br />';
											}
										}
										$hla_info_stage_2 .= '<br />';
									}
								}
							}
							break;
						} else {
							if ($first_row) {
								$first_row = false;
								$history_html .= '<tr><td colspan="5"><div style="padding-top: 1px; width:100%;"><!-- --></div></td></tr>';
							} else {
								$history_html .= '<tr><td colspan="5"><div style="padding-top: 1px;border-bottom:1px solid #CFCFCF;width:100%;"><!-- --></div></td></tr>';
							}
							
							$history_html .= '<tr style="'.$row_style.'" valign="top" id="row_'.$op_history_id.'">									
													<td width="13%" style="text-align:center;">'.$dates.'</td>
													<td width="14%" style="text-align:center;">'.$times.'</td>
													<td width="30%" style="text-align:center;">'.$get_history_row['delivered_character'].'</td>';
													
							if ($received) {
								if ($dispute) {
									$history_html .= '<td width="24%" style="text-align:left;">&nbsp;</td>
													  <td width="19%" style="text-align:center;">
														<div style="color:red;padding: 0px 10px;text-align:center;">
															<font color="red" style="text-align:center;">'.TEXT_DID_NOT_RECEIVE.'</font>
														</div>
													  </td>';
								} else {
									$history_html .= '<td width="24%" style="text-align:left;">&nbsp;</td>
													  <td width="19%" style="text-align:center;">
														<div style="padding: 0px 10px;text-align:center;">'.TEXT_DELIVERY_CONFIRMED.'</div>
													  </td>';
								}
							} else {
								if ($rolled_back) {
									$history_html .= '<td width="43%" nowrap style="color:black;text-align:center;" colspan="2">
															'.TEXT_ROLLED_BACK_DELIVERED_AMT.'
														</td>';
								} else {
									$history_html .= '	<td width="24%" align="left" style="color:black;text-align:left;" id="notes_'.$op_history_id.'" nowrap>' . 
																TEXT_AUTO_CONFIRM_DELIVERY_NOTE . '<br/>
																<div style="float: left;">' . TEXT_REMAINING_TIME.': </div><div style="float: left;" id="Countdown_'.$op_history_id.'" value="' . $op_history_id . '_' . $orders_products_id . '"></div>
															</td>
															<td width="19%" id="receive_'.$op_history_id.'" style="text-align:center;">
																'.TEXT_DID_YOU_ABLE_TO_LOGIN.'
																'.tep_button(BUTTON_YES, BUTTON_YES, '', 'style="padding: 0px 2px;" id="receive_yes" onClick="hla_confirm_delivered(\'' . $orders_products_id . '\', \''.$op_history_id.'\');"', '').'
																&nbsp;
																'.tep_button(BUTTON_NO, BUTTON_NO, '', 'style="padding: 0px 2px;" id="receive_no" onClick="delivery_dispute(\''.$op_history_id.'\');"', '').'
															</td>';
									
									if ($date_confirm_delivered != '0000-00-00 00:00:00') {
										$date_confirm_delivered_sec = tep_day_diff(date("Y-m-d H:i:s"), $date_confirm_delivered, 'sec');
										
										// For the Confirm delivered Count Down
										$history_html .= '<script>';
										$history_html .= 'jQuery(function () {
																var currentTime_'.$op_history_id.' = new Date();
										
																var cfm_hour_'.$op_history_id.' = currentTime_'.$op_history_id.'.getHours();
																var cfm_min_'.$op_history_id.' = currentTime_'.$op_history_id.'.getMinutes();
																var cfm_sec_'.$op_history_id.' = currentTime_'.$op_history_id.'.getSeconds();
																var cfm_mon_'.$op_history_id.' = currentTime_'.$op_history_id.'.getMonth() + 1;
																var cfm_date_'.$op_history_id.' = currentTime_'.$op_history_id.'.getDate();
																var cfm_year_'.$op_history_id.' = currentTime_'.$op_history_id.'.getFullYear();
																
																var cfm_s_yrs_'.$op_history_id.' = 0;
																var cfm_s_mths_'.$op_history_id.' = 0;
																var cfm_s_days_'.$op_history_id.' = 0;
																var cfm_s_hrs_'.$op_history_id.' = 0;
																var cfm_s_mins_'.$op_history_id.' = 0;
																var cfm_s_secs_'.$op_history_id.' = 0;
												
																var cfm_date_confirm_delivered_'.$op_history_id.' = '.$date_confirm_delivered_sec.';
																
																cfm_s_yrs_'.$op_history_id.'  = date("Y", mktime(cfm_hour_'.$op_history_id.', cfm_min_'.$op_history_id.', eval(cfm_sec_'.$op_history_id.' + "+" + cfm_date_confirm_delivered_'.$op_history_id.'), cfm_mon_'.$op_history_id.', cfm_date_'.$op_history_id.', cfm_year_'.$op_history_id.'));
																cfm_s_mths_'.$op_history_id.' = date("m", mktime(cfm_hour_'.$op_history_id.', cfm_min_'.$op_history_id.', eval(cfm_sec_'.$op_history_id.' + "+" + cfm_date_confirm_delivered_'.$op_history_id.'), cfm_mon_'.$op_history_id.', cfm_date_'.$op_history_id.', cfm_year_'.$op_history_id.'));
																cfm_s_days_'.$op_history_id.' = date("d", mktime(cfm_hour_'.$op_history_id.', cfm_min_'.$op_history_id.', eval(cfm_sec_'.$op_history_id.' + "+" + cfm_date_confirm_delivered_'.$op_history_id.'), cfm_mon_'.$op_history_id.', cfm_date_'.$op_history_id.', cfm_year_'.$op_history_id.'));
																cfm_s_hrs_'.$op_history_id.'  = date("H", mktime(cfm_hour_'.$op_history_id.', cfm_min_'.$op_history_id.', eval(cfm_sec_'.$op_history_id.' + "+" + cfm_date_confirm_delivered_'.$op_history_id.'), cfm_mon_'.$op_history_id.', cfm_date_'.$op_history_id.', cfm_year_'.$op_history_id.'));
																cfm_s_mins_'.$op_history_id.' = date("i", mktime(cfm_hour_'.$op_history_id.', cfm_min_'.$op_history_id.', eval(cfm_sec_'.$op_history_id.' + "+" + cfm_date_confirm_delivered_'.$op_history_id.'), cfm_mon_'.$op_history_id.', cfm_date_'.$op_history_id.', cfm_year_'.$op_history_id.'));
																cfm_s_secs_'.$op_history_id.' = date("s", mktime(cfm_hour_'.$op_history_id.', cfm_min_'.$op_history_id.', eval(cfm_sec_'.$op_history_id.' + "+" + cfm_date_confirm_delivered_'.$op_history_id.'), cfm_mon_'.$op_history_id.', cfm_date_'.$op_history_id.', cfm_year_'.$op_history_id.'));
																
														       countdown_timer("Countdown_'.$op_history_id.'", "", hla_auto_delivery_confirmation, cfm_s_yrs_'.$op_history_id.', cfm_s_mths_'.$op_history_id.', cfm_s_days_'.$op_history_id.', cfm_s_hrs_'.$op_history_id.', cfm_s_mins_'.$op_history_id.', cfm_s_secs_'.$op_history_id.');
														  });';
										$history_html .= '</script>';
									}
								}
							}
							
							$history_html .= '	</tr>';
							
							if ($received && tep_not_null($get_history_row['dispute_comment'])) {
								$history_html .= '	<tr id="details_'.$op_history_id.'">
														<td colspan="6" style="padding:0px 20px 10px 20px;">
														<span style="padding-right:5px">'.tep_image(DIR_WS_ICONS . "icon_details.gif", "", "9", "9").'</span>
														'.TEXT_DETAILS.' <span id="details_msg_'.$op_history_id.'">'.$get_history_row['dispute_comment'].'</span>
														</td>
													</tr>';
							} else {
								$history_html .= '	<tr id="details_'.$op_history_id.'" style="display:none;">
														<td colspan="6" style="padding:0px 20px 10px 20px;">
														<span style="padding-right:5px">'.tep_image(DIR_WS_ICONS . "icon_details.gif", "", "9", "9").'</span>
														'.TEXT_DETAILS.' <span id="details_msg_'.$op_history_id.'"></span>
														</td>
													</tr>';
							}
						}
					}
				}
				
				
				echo "<detail><![CDATA[";
				
				if (tep_not_null($hla_info_stage_1)) {
					echo '<div>' . $hla_info_stage_1 . '</div>';
					echo '<div class="breakLine"><!-- --></div>';
					echo '<div id="secret_qna_' . $orders_products_id . '" ' . (tep_not_null($hla_info_stage_2) ? '' : 'style="display: none;"') . '>' . $hla_info_stage_2 . '</div>';
				}
				
				if (tep_not_null($history_html)) {
					echo '<div><b>'.TEXT_HLA_SECRET_ANSWER_NOTE.'</b></div>';
					echo '	<div id="orderHistory" style="background-color: rgb(255, 253, 219);width:100%;height:100%;" class="loginColumnBorder">
								<div>
									<table id="orderHistoryTable" width="100%" cellspacing="0" cellpadding="0" border="0" class="boxText">
										<tbody>';
					echo $history_html;
					echo '				</tbody>
									</table>
								</div>
							</div>
							
							<div id="hlaAutoConfirm"><div class="breakLine"><!-- --></div>' . TEXT_HLA_AUTO_CONFIRM_DELIVERY_NOTE . '</div>';
				}
				echo "]]></detail>";
			}
			break;
			
		default:
			echo "<result>Unknown request!</result>";
			break;
	}
}

echo '</response>';

function tep_display_selection_option($level_tree_array, $selected_level) {
	for($i=0; $i < count($level_tree_array); $i++) {
		if ($level_tree_array[$i]["data_pool_min_level"] <= $selected_level && $level_tree_array[$i]["data_pool_max_level"] >= $selected_level || 
			$level_tree_array[$i]["data_pool_min_level"] == 0 && $level_tree_array[$i]["data_pool_max_level"] == 0) {
				echo "<option index='".$level_tree_array[$i]["id"]."'><![CDATA[".$level_tree_array[$i]["path"]."]]></option>";
		}
		if (isset($level_tree_array[$i]["child"]) && count($level_tree_array[$i]["child"])) {
			tep_display_selection_option($level_tree_array[$i]["child"], $selected_level);
		}
	}
}
?>