<?

/*
  $Id: customer_support.php,v 1.10 2012/08/16 06:37:14 weichen Exp $

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2003 osCommerce

  Released under the GNU General Public License
 */

require('includes/application_top.php');
require(DIR_WS_CLASSES . 'mail/htmlMimeMail5/htmlMimeMail.php');

require(DIR_WS_LANGUAGES . $language . '/' . FILENAME_CUSTOMER_SUPPORT);

$breadcrumb->add(NAVBAR_TITLE, tep_href_link(FILENAME_CUSTOMER_SUPPORT, '', 'NONSSL'));

if ($_SESSION['languages_id'] == 1) {
    $send_message_flag = true;
    $chat_live_flag = false;
    $notice_box_flag = false;
} else {
    $send_message_flag = false;
    $chat_live_flag = false;
    $notice_box_flag = false;
}

$issue_send_message_array = array(array('id' => '', 'text' => PULL_DOWN_DEFAULT),
    array('id' => OPTION_ISSUE_5, 'text' => OPTION_ISSUE_5),
    array('id' => OPTION_ISSUE_6, 'text' => OPTION_ISSUE_6),
    array('id' => OPTION_ISSUE_7, 'text' => OPTION_ISSUE_7),
    array('id' => OPTION_ISSUE_8, 'text' => OPTION_ISSUE_8),
    array('id' => OPTION_ISSUE_9, 'text' => OPTION_ISSUE_9),
    array('id' => OPTION_ISSUE_10, 'text' => OPTION_ISSUE_10),
    array('id' => OPTION_ISSUE_11, 'text' => OPTION_ISSUE_11),
    array('id' => OPTION_ISSUE_12, 'text' => OPTION_ISSUE_12)
);

$chat_issue_array = array(array('id' => '', 'text' => PULL_DOWN_DEFAULT),
    array('id' => OPTION_CHAT_ISSUE_1, 'text' => OPTION_CHAT_ISSUE_1),
    array('id' => OPTION_CHAT_ISSUE_2, 'text' => OPTION_CHAT_ISSUE_2),
    array('id' => OPTION_CHAT_ISSUE_3, 'text' => OPTION_CHAT_ISSUE_3),
    array('id' => OPTION_CHAT_ISSUE_4, 'text' => OPTION_CHAT_ISSUE_4),
    array('id' => OPTION_CHAT_ISSUE_5, 'text' => OPTION_CHAT_ISSUE_5),
    array('id' => OPTION_CHAT_ISSUE_6, 'text' => OPTION_CHAT_ISSUE_6),
    array('id' => OPTION_CHAT_ISSUE_7, 'text' => OPTION_CHAT_ISSUE_7),
    array('id' => OPTION_CHAT_ISSUE_8, 'text' => OPTION_CHAT_ISSUE_8),
    array('id' => OPTION_CHAT_ISSUE_9, 'text' => OPTION_CHAT_ISSUE_9),
    array('id' => OPTION_CHAT_ISSUE_10, 'text' => OPTION_CHAT_ISSUE_10),
    array('id' => OPTION_CHAT_ISSUE_11, 'text' => OPTION_CHAT_ISSUE_11),
    array('id' => OPTION_CHAT_ISSUE_12, 'text' => OPTION_CHAT_ISSUE_12)
);

$action = (isset($_REQUEST['action']) ? $_REQUEST['action'] : '');

switch ($action) {
    case 'send':
        $error = false;

        $customers_email_address = tep_db_prepare_input($_POST['customers_email_address']);
        $issue = tep_db_prepare_input($_POST['issue']);
        $phone_number = tep_db_prepare_input($_POST['phone_number']);
        $order_number = tep_db_prepare_input($_POST['order_number']);
        $sell_order_number = tep_db_prepare_input($_POST['sell_order_number']);
        $game_title = tep_db_prepare_input($_POST['game_title']);
        $account_name = tep_db_prepare_input($_POST['account_name']);
        $account_password = tep_db_prepare_input($_POST['account_password']);
        $character_name = tep_db_prepare_input($_POST['character_name']);
        $realm = tep_db_prepare_input($_POST['realm']);
        $region = tep_db_prepare_input($_POST['region']);
        $faction = tep_db_prepare_input($_POST['faction']);
        $class = tep_db_prepare_input($_POST['class1']);
        $server = tep_db_prepare_input($_POST['server']);
        $payment_method = tep_db_prepare_input($_POST['payment_method']);
        $error_found = tep_db_prepare_input($_POST['error_found']);
        $message = nl2br(tep_db_prepare_input($_POST['message']));
        $will_be_in_game = tep_db_prepare_input($_POST['will_be_in_game']);
        $in_game_duration = tep_db_prepare_input($_POST['in_game_duration']);
        $product_type = tep_db_prepare_input($_POST['product_type']);

        $email_text = '';

        if (!tep_not_null($customers_email_address)) {
            $error = true;
            $messageStack->add('customer_support', ENTRY_EMAIL_ADDRESS_ERROR);
        } else if (strlen($customers_email_address) < ENTRY_EMAIL_ADDRESS_MIN_LENGTH) {
            $error = true;
            $messageStack->add('customer_support', ENTRY_EMAIL_ADDRESS_ERROR);
        } else if (tep_validate_email($customers_email_address) == false) {
            $error = true;
            $messageStack->add('customer_support', ENTRY_EMAIL_ADDRESS_CHECK_ERROR);
        }

        if (!$error) {
            $email_subject = $issue;
            //$email_subject	= tep_mb_convert_encoding($issue, EMAIL_CHARSET, CHARSET);

            if ($issue == OPTION_ISSUE_1) {
                $email_text .= "<b>" . HEADING_ORDER_NUMBER . "</b> " . $order_number . "<br>";
                $email_text .= "<b>" . HEADING_PHONE_NUMBER . "</b> " . $phone_number . "<br>";
                $email_text .= "<b>" . HEADING_GAME_TITLE . "</b> " . $game_title . "<br>";
                $email_text .= "<b>" . HEADING_WILL_BE_IN_GAME . "</b> " . $will_be_in_game . "<br>";
                $email_text .= "<b>" . HEADING_IN_GAME_DURATION . "</b> " . $in_game_duration . "<br>";
                $email_text .= "<b>" . HEADING_MESSAGE . "</b> " . $message . "<br>";
            } else if ($issue == OPTION_ISSUE_2) {
                $email_text .= "<b>" . HEADING_ORDER_NUMBER . "</b> " . $order_number . "<br>";
                $email_text .= "<b>" . HEADING_PHONE_NUMBER . "</b> " . $phone_number . "<br>";
                $email_text .= "<b>" . HEADING_ACCOUNT_NAME . "</b> " . $account_name . "<br>";
                $email_text .= "<b>" . HEADING_ACCOUNT_PASSWORD . "</b> " . $account_password . "<br>";
                $email_text .= "<b>" . HEADING_CHARACTER_NAME . "</b> " . $character_name . "<br>";
                $email_text .= "<b>" . HEADING_REGION . "</b> " . $region . "<br>";
                $email_text .= "<b>" . HEADING_FACTION . "</b> " . $faction . "<br>";
                $email_text .= "<b>" . HEADING_SERVER . "</b> " . $server . "<br>";
                $email_text .= "<b>" . HEADING_MESSAGE . "</b> " . $message . "<br>";
            } else if ($issue == OPTION_ISSUE_3) {
                $email_text .= "<b>" . HEADING_ORDER_NUMBER . "</b> " . $order_number . "<br>";
                $email_text .= "<b>" . HEADING_ERROR_FOUND . "</b> " . $error_found . "<br>";
                $email_text .= "<b>" . HEADING_MESSAGE . "</b> " . $message . "<br>";


                $files_tmp_name = array();
                $attachment = array();

                if ($_FILES['screenshot_1']['tmp_name']) {
                    $files_tmp_name[] = $_FILES['screenshot_1']['tmp_name'];
                }

                if ($_FILES['screenshot_2']['tmp_name']) {
                    $files_tmp_name[] = $_FILES['screenshot_2']['tmp_name'];
                }

                if ($_FILES['screenshot_3']['tmp_name']) {
                    $files_tmp_name[] = $_FILES['screenshot_3']['tmp_name'];
                }

                if ($files_tmp_name) {
                    foreach ($files_tmp_name as $files_value) {
                        $file = fopen($files_value, 'rb');
                        $attachment[] = fread($file, filesize($files_value));
                        fclose($file);
                    }
                }
            } else if ($issue == OPTION_ISSUE_4) {
                $email_text .= "<b>" . HEADING_ORDER_NUMBER . "</b> " . $order_number . "<br>";
                $email_text .= "<b>" . HEADING_GAME_TITLE . "</b> " . $game_title . "<br>";
                $email_text .= "<b>" . HEADING_ACCOUNT_NAME . "</b> " . $account_name . "<br>";
                $email_text .= "<b>" . HEADING_ACCOUNT_PASSWORD . "</b> " . $account_password . "<br>";
                $email_text .= "<b>" . HEADING_CHARACTER_NAME . "</b> " . $character_name . "<br>";
                $email_text .= "<b>" . HEADING_REALM . "</b> " . $realm . "<br>";
                $email_text .= "<b>" . HEADING_REGION . "</b> " . $region . "<br>";
                $email_text .= "<b>" . HEADING_FACTION . "</b> " . $faction . "<br>";
                $email_text .= "<b>" . HEADING_CLASS . "</b> " . $class . "<br>";
                $email_text .= "<b>" . HEADING_MESSAGE . "</b> " . $message . "<br>";
            } else if ($issue == OPTION_ISSUE_5) {
                $email_text .= "<b>" . HEADING_ORDER_NUMBER . "</b> " . $order_number . "<br>";
                $email_text .= "<b>" . HEADING_PRODUCT_TYPE . "</b> " . $product_type . "<br>";
                $email_text .= "<b>" . HEADING_MESSAGE . "</b> " . $message . "<br>";
            } else if ($issue == OPTION_ISSUE_6 || $issue == OPTION_ISSUE_7) {
                $email_text .= "<b>" . HEADING_ORDER_NUMBER . "</b> " . $order_number . "<br>";
                $email_text .= "<b>" . HEADING_MESSAGE . "</b> " . $message . "<br>";
            } else if ($issue == OPTION_ISSUE_8) {
                $email_text .= "<b>" . HEADING_PAYMENT_METHOD . "</b> " . $payment_method . "<br>";
                $email_text .= "<b>" . HEADING_MESSAGE . "</b> " . $message . "<br>";
            } else if ($issue == OPTION_ISSUE_9) {
                $email_text .= "<b>" . HEADING_SELL_ORDER_NUMBER . "</b> " . $sell_order_number . "<br>";
                $email_text .= "<b>" . HEADING_MESSAGE . "</b> " . $message . "<br>";
            } else {
                $email_text .= "<b>" . HEADING_MESSAGE . "</b> " . $message . "<br>";
            }

            $email[] = STORE_OWNER_EMAIL_ADDRESS;

            $email_str = implode(", ", $email);

            $headers = 'To: ' . $email_str . "\r\n" .
                    'From: ' . STORE_NAME . '<<EMAIL>>' . "\r\n" .
                    'Reply-To: <EMAIL>' . "\r\n" .
                    'X-Mailer: PHP/' . phpversion();

            $result = mail($email_str, $email_subject, $email_text, $headers);

            $messageStack->add_session('customer_support', SUCCESS_CONTACT_CS_FORM_SENT, 'success');
            tep_redirect(FILENAME_CUSTOMER_SUPPORT);
        }

        break;
    default:
        if (tep_session_is_registered('customer_id')) {
            $customer_info_sql = "	SELECT customers_email_address, customers_telephone
									FROM " . TABLE_CUSTOMERS . "  
									WHERE customers_id ='" . (int) $customer_id . "'";
            $customer_info_result_sql = tep_db_query($customer_info_sql);
            $customer_info_row = tep_db_fetch_array($customer_info_result_sql);
            $customer_info_row['customers_name'] = $_SESSION["customer_first_name"] . " " . $_SESSION["customer_last_name"];
            $customer_info_row['customers_group_name'] = tep_get_customers_groups_name($_SESSION["customers_groups_id"]);
        }

        switch ($languages_id) {
            case '1':
                $live_support_code = 'EN';

                break;
            case '2':
            case '3':
                $live_support_code = 'CN';

                break;
            default:
                $live_support_code = 'EN';

                break;
        }

        break;
}

$content = CONTENT_CUSTOMER_SUPPORT;
unset($content_template);

$javascript = 'popup_window.js';

require(DIR_WS_TEMPLATES . TEMPLATENAME_MAIN_PAGE);

require(DIR_WS_INCLUDES . 'application_bottom.php');
?>