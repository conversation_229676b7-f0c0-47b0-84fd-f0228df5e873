<?
header("Cache-Control: no-cache");
header("Cache-Control: post-check=0,pre-check=0");
header("Cache-Control: max-age=0");
header("Pragma: no-cache");

header('Content-Type: text/xml');

include_once('includes/application_top.php');

require_once(DIR_WS_CLASSES . 'buyback.php');
require_once(DIR_WS_CLASSES . 'vip_groups.php');
require_once(DIR_WS_CLASSES . 'vip_order.php');
require_once(DIR_WS_CLASSES . 'log.php');
require_once(DIR_WS_CLASSES . 'phone_verification.php');
require_once(DIR_WS_CLASSES . 'user.php');

$action = isset($_GET['action']) ? $_GET['action'] : '';
$country_id = isset($HTTP_GET_VARS['country_id']) ? (int) $HTTP_GET_VARS['country_id'] : '';
$language_id = isset($HTTP_GET_VARS['lang']) ? (int) $HTTP_GET_VARS['lang'] : '';
$telephone = isset($HTTP_GET_VARS['telephone']) ? $HTTP_GET_VARS['telephone'] : '';

$customer_id = isset($_SESSION['customer_id']) ? (int) $_SESSION['customer_id'] : '';
$languages_id = isset($_SESSION['languages_id']) ? (int) $_SESSION['languages_id'] : '';

$code_received = isset($HTTP_GET_VARS['code']) ? $HTTP_GET_VARS['code'] : '';
$order_id = isset($HTTP_GET_VARS['order_id']) ? (int) $HTTP_GET_VARS['order_id'] : '';
$buyback_cat_id = isset($HTTP_GET_VARS['buyback_cat_id']) ? (int) $HTTP_GET_VARS['buyback_cat_id'] : '';
$buyback_parent_cat_id = isset($HTTP_GET_VARS['buyback_parent_cat_id']) ? (int) $HTTP_GET_VARS['buyback_parent_cat_id'] : '';
$buyback_qty = isset($HTTP_GET_VARS['buyback_qty']) ? (int) $HTTP_GET_VARS['buyback_qty'] : '';
$buyback_products_id = isset($HTTP_GET_VARS['buyback_products_id']) ? (int) $HTTP_GET_VARS['buyback_products_id'] : '';
$order_products_id = isset($_GET['opid']) ? (int) $_GET['opid'] : '';

$customer_order_id = isset($HTTP_GET_VARS['oID']) ? (int) $HTTP_GET_VARS['oID'] : '';
$customer_rate = isset($HTTP_GET_VARS['point']) ? (int) $HTTP_GET_VARS['point'] : '0';
$curr_code = isset($HTTP_GET_VARS['curr_code']) ? $HTTP_GET_VARS['curr_code'] : DEFAULT_CURRENCY;

$page = isset($_GET['page']) ? $_GET['page'] : '';

$order_id = isset($_GET['order_id']) && is_numeric($_GET['order_id']) ? tep_db_prepare_input($_GET['order_id']) : '';
$call_language = isset($_GET['call_language']) ? $_GET['call_language'] : 'english'; //make default call language = english

$rsp_password = isset($_POST['rsp_password']) ? tep_db_prepare_input($_POST['rsp_password']) : '';
$rsp_new_password = isset($_POST['rsp_new_password']) ? tep_db_prepare_input($_POST['rsp_new_password']) : '';
$rsp_cfm_password = isset($_POST['rsp_cfm_password']) ? tep_db_prepare_input($_POST['rsp_cfm_password']) : '';

echo '<response>';
if (tep_not_null($action)) {

    if (!isset($_SERVER['HTTP_REFERER']) ||
            (strstr($_SERVER['HTTP_REFERER'], HTTP_SERVER) === FALSE && strstr($_SERVER['HTTP_REFERER'], HTTPS_SERVER) === FALSE)
    ) {
        echo "You are not allowed to access from outside";
        echo '</response>';
        exit;
    }

    switch ($action) {
        case 'get_captcha_img':
            echo '<captcha_img><![CDATA[' . tep_image('securimage_show.php?sid=' . md5(uniqid(time()))) . ']]></captcha_img>';

            break;
        case 'delivery_confirmed':
            if (file_exists(DIR_WS_LANGUAGES . $_SESSION['language'] . '/buyback_system.php')) {
                include_once(DIR_WS_LANGUAGES . $_SESSION['language'] . '/buyback_system.php');
            }

            $op_history_id = isset($_GET['op_history_id']) ? $_GET['op_history_id'] : '';
            $selected_trade_method = isset($_GET['selected_trade_method']) ? $_GET['selected_trade_method'] : '';
            $customer_email_address = tep_get_customers_email($_SESSION['customer_id']);
            $delivery_mode = '';
            $hold_buyback = false;

            if (tep_not_null($op_history_id)) {
                $checking_user_select_sql = "	SELECT o.orders_id, o.customers_id, oph.orders_products_history_id, oph.buyback_request_group_id, oph.orders_products_id 
												FROM " . TABLE_ORDERS_PRODUCTS_HISTORY . " AS oph 
												INNER JOIN " . TABLE_ORDERS . " AS o 
													ON (oph.orders_id = o.orders_id) 
												WHERE oph.orders_products_history_id = '" . (int) $op_history_id . "'
													AND received IS NULL";
                $checking_user_result_sql = tep_db_query($checking_user_select_sql);
                if ($checking_user_row = tep_db_fetch_array($checking_user_result_sql)) {
                    if ($checking_user_row['customers_id'] == $_SESSION['customer_id']) {
                        $child_order_product_id = $checking_user_row['orders_products_id'];

                        $parent_order_product_id_select_sql = "	SELECT parent_orders_products_id, custom_products_type_id 
																FROM " . TABLE_ORDERS_PRODUCTS . "
																WHERE orders_products_id  = '" . (int) $child_order_product_id . "'";
                        $parent_order_product_id_result_sql = tep_db_query($parent_order_product_id_select_sql);
                        if ($parent_order_product_id_row = tep_db_fetch_array($parent_order_product_id_result_sql)) {
                            $parent_orders_products_id = $parent_order_product_id_row['parent_orders_products_id'];
                            $custom_products_type_id = $parent_order_product_id_row['custom_products_type_id'];
                        }
                        $extra_info_array = tep_draw_products_extra_info($parent_orders_products_id);

                        if (tep_not_null($extra_info_array['delivery_mode'])) {
                            $delivery_mode = $extra_info_array['delivery_mode'];
                        }

                        if (tep_not_null($selected_trade_method) && $selected_trade_method != 'undefined') {
                            switch ($selected_trade_method) {
                                case 'f2f':
                                    if (tep_not_null($delivery_mode) && $delivery_mode != '1') {
                                        $hold_buyback = true;

                                        $update_buyback_history_data_sql = array('buyback_request_group_id' => $checking_user_row['buyback_request_group_id'],
                                            'buyback_status_id' => 0,
                                            'date_added' => 'now()',
                                            'customer_notified' => 0,
                                            'comments' => "Delivery method not match. Supplier delivered by <b>Face to Face</b> method",
                                            'set_as_buyback_remarks' => 1,
                                            'changed_by' => $customer_email_address
                                        );

                                        tep_db_perform(TABLE_BUYBACK_STATUS_HISTORY, $update_buyback_history_data_sql, 'insert');
                                    }
                                    break;
                                case 'mail':
                                    if (tep_not_null($delivery_mode) && $delivery_mode != '3') {
                                        $hold_buyback = true;

                                        $update_buyback_history_data_sql = array('buyback_request_group_id' => $checking_user_row['buyback_request_group_id'],
                                            'buyback_status_id' => 0,
                                            'date_added' => 'now()',
                                            'customer_notified' => 0,
                                            'comments' => "Delivery method not match. Supplier delivered by <b>Mail</b> method",
                                            'set_as_buyback_remarks' => 1,
                                            'changed_by' => $customer_email_address
                                        );

                                        tep_db_perform(TABLE_BUYBACK_STATUS_HISTORY, $update_buyback_history_data_sql, 'insert');
                                    }
                                    break;
                                case 'open_store':
                                    if (tep_not_null($delivery_mode) && $delivery_mode != '4') {
                                        $hold_buyback = true;

                                        $update_buyback_history_data_sql = array('buyback_request_group_id' => $checking_user_row['buyback_request_group_id'],
                                            'buyback_status_id' => 0,
                                            'date_added' => 'now()',
                                            'customer_notified' => 0,
                                            'comments' => "Delivery method not match. Supplier delivered by <b>Open Store</b> method",
                                            'set_as_buyback_remarks' => 1,
                                            'changed_by' => $customer_email_address
                                        );

                                        tep_db_perform(TABLE_BUYBACK_STATUS_HISTORY, $update_buyback_history_data_sql, 'insert');
                                    }
                                    break;
                                default:
                                    $hold_buyback = true;
                                    $comments = 'Delivery method not match. Supplier delivered by <b>' . $selected_trade_method . '</b> method';

                                    $update_buyback_history_data_sql = array('buyback_request_group_id' => $checking_user_row['buyback_request_group_id'],
                                        'buyback_status_id' => 0,
                                        'date_added' => 'now()',
                                        'customer_notified' => 0,
                                        'comments' => tep_db_input($comments),
                                        'set_as_buyback_remarks' => 1,
                                        'changed_by' => $customer_email_address
                                    );

                                    tep_db_perform(TABLE_BUYBACK_STATUS_HISTORY, $update_buyback_history_data_sql, 'insert');
                                    break;
                            }
                        }

                        $update_history_data_sql['changed_by'] = $customer_email_address;
                        $update_history_data_sql['received'] = '1';
                        $update_history_data_sql['last_updated'] = 'now()';
                        tep_db_perform(TABLE_ORDERS_PRODUCTS_HISTORY, $update_history_data_sql, 'update', ' orders_products_history_id = "' . (int) $op_history_id . '" ');


                        // HLA products update status and return Stage-2 info
                        if ($custom_products_type_id == '4') {
                            $hla_info_stage_2 = '';

                            // retrieve secret Q&A
                            $get_hla_info_sql = "	SELECT orders_products_extra_info_key, orders_products_extra_info_value 
													FROM " . TABLE_ORDERS_PRODUCTS_EXTRA_INFO . " 
													WHERE orders_products_id = '" . $child_order_product_id . "'
														AND orders_products_extra_info_key = 'hla_info_stage_2'
													LIMIT 1";
                            $get_hla_info_result = tep_db_query($get_hla_info_sql);
                            if ($get_hla_info_row = tep_db_fetch_array($get_hla_info_result)) {
                                $extra_info_array = tep_array_unserialize($get_hla_info_row['orders_products_extra_info_value']);

                                if (isset($extra_info_array['question'])) {
                                    // for remain OLD structure still working fine
                                    foreach ($extra_info_array as $key => $value) {
                                        $hla_info_stage_2 .= ucwords(str_replace('_', ' ', $key)) . ': ' . $value . '<br />';
                                    }
                                } else {
                                    foreach ($extra_info_array as $stage_2_key => $stage_2_info) {
                                        foreach ($stage_2_info as $key => $val) {
                                            if (is_array($val)) {
                                                foreach ($val as $child_key => $child_val) {
                                                    $hla_info_stage_2 .= ucwords(str_replace('_', ' ', $child_key)) . ': ' . nl2br($child_val) . '<br />';
                                                }
                                            } else {
                                                $hla_info_stage_2 .= '<b>' . $val . '</b><br />';
                                            }
                                        }
                                        $hla_info_stage_2 .= '<br />';
                                    }
                                }
                            }

                            echo '<result><![CDATA[' . $hla_info_stage_2 . ']]></result>';
                        }


                        if (tep_not_null($checking_user_row['buyback_request_group_id']) && $hold_buyback == false) {
                            // Updating the product average buyback price
                            $cron_pending_credit_mature_period = 0;

                            $buyback_product_select_sql = "	SELECT br.products_id, brg.customers_id, br.buyback_quantity_received, br.buyback_request_quantity, br.buyback_unit_price, br.buyback_quantity_confirmed, brg.buyback_request_group_date, brg.buyback_status_id 
															FROM " . TABLE_BUYBACK_REQUEST_GROUP . " AS brg
																INNER JOIN " . TABLE_BUYBACK_REQUEST . " AS br 
															ON (brg.buyback_request_group_id = br.buyback_request_group_id)  
															WHERE brg.buyback_request_group_id = '" . tep_db_input($checking_user_row['buyback_request_group_id']) . "'";
                            $buyback_product_result_sql = tep_db_query($buyback_product_select_sql);

                            if ($buyback_product_row = tep_db_fetch_array($buyback_product_result_sql)) {
                                if ($buyback_product_row['buyback_status_id'] != 3) {
                                    $buyback_status_history_update_arr = array('set_as_buyback_remarks' => '0');
                                    tep_db_perform(TABLE_BUYBACK_STATUS_HISTORY, $buyback_status_history_update_arr, 'update', "buyback_request_group_id= '" . (int) $checking_user_row['buyback_request_group_id'] . "'");

                                    $buyback_request_group_update_arr = array('buyback_status_id' => '3');
                                    tep_db_perform(TABLE_BUYBACK_REQUEST_GROUP, $buyback_request_group_update_arr, 'update', "buyback_request_group_id= '" . (int) $checking_user_row['buyback_request_group_id'] . "'");

                                    $buyback_history_data_array = array('buyback_request_group_id' => $checking_user_row['buyback_request_group_id'],
                                        'buyback_status_id' => 3,
                                        'date_added' => 'now()',
                                        'customer_notified' => 0,
                                        'comments' => '',
                                        'set_as_buyback_remarks' => 0,
                                        'changed_by' => $customer_email_address
                                    );
                                    tep_db_perform(TABLE_BUYBACK_STATUS_HISTORY, $buyback_history_data_array);

                                    $current_buyback_info_select_sql = "SELECT products_buyback_quantity, products_buyback_price FROM " . TABLE_PRODUCTS . " WHERE products_id = '" . tep_db_input($buyback_product_row['products_id']) . "'";
                                    $current_buyback_info_result_sql = tep_db_query($current_buyback_info_select_sql);
                                    if ($current_buyback_info_row = tep_db_fetch_array($current_buyback_info_result_sql)) {
                                        $prev_buyback_qty = (int) $current_buyback_info_row["products_buyback_quantity"];
                                        $prev_buyback_price = (double) $current_buyback_info_row["products_buyback_price"];

                                        $order_buyback_qty = (int) $buyback_product_row['buyback_quantity_received'];
                                        // Already take into account the $order_buyback_qty

                                        $unit_price = $buyback_product_row['buyback_unit_price'];

                                        $received_quantity = (int) $buyback_product_row['buyback_quantity_received'];
                                        $total_buyback_price = $unit_price * ($received_quantity > $buyback_product_row['buyback_quantity_confirmed'] ? $buyback_product_row['buyback_quantity_confirmed'] : $received_quantity);

                                        $new_buyback_qty = $prev_buyback_qty + $order_buyback_qty;

                                        if ($new_buyback_qty > 0) {
                                            $new_buyback_price = ( ($prev_buyback_qty * $prev_buyback_price) + ($total_buyback_price) ) / $new_buyback_qty;
                                        } else {
                                            $new_buyback_price = 0;
                                        }

                                        $buyback_info_update_sql = "UPDATE " . TABLE_PRODUCTS . " 
																	SET products_buyback_quantity = '" . tep_db_input($new_buyback_qty) . "', products_buyback_price = '" . tep_db_input(round($new_buyback_price, 4)) . "' 
																	WHERE products_id = '" . tep_db_input($buyback_product_row['products_id']) . "'";
                                        tep_db_query($buyback_info_update_sql);

                                        //Always get the max mature period among all the products
                                        $this_prod_mature_period = tep_get_products_payment_mature_period($buyback_product_row['products_id'], $buyback_product_row['customers_id']);
                                        $cron_pending_credit_mature_period = max($this_prod_mature_period, $cron_pending_credit_mature_period);
                                    }

                                    if ($custom_products_type_id == 4) {
                                        $mature_period_day = (int) (($cron_pending_credit_mature_period / 60) / 24);
                                        $comments_processing_to_completed = sprintf(HLA_COMMENTS_PROCESSING_TO_COMPLETED, $mature_period_day);
                                    } else {
                                        $comments_processing_to_completed = COMMENTS_PROCESSING_TO_COMPLETED;
                                    }

                                    $buyback_history_data_array = array('buyback_request_group_id' => $checking_user_row['buyback_request_group_id'],
                                        'buyback_status_id' => 0,
                                        'date_added' => 'now()',
                                        'customer_notified' => 1,
                                        'comments' => $comments_processing_to_completed,
                                        'set_as_buyback_remarks' => 1,
                                        'changed_by' => 'system'
                                    );
                                    tep_db_perform(TABLE_BUYBACK_STATUS_HISTORY, $buyback_history_data_array);
                                    /*                                     * ***********************************************************************************
                                      Preparing data for scheduled cron job
                                      This step applies to supplier buyback (user type=1) AND customer buyback(user type=0)
                                     * *********************************************************************************** */

                                    $cron_job_verify_select_sql = "	SELECT cron_pending_credit_trans_completed_date 
																	FROM " . TABLE_CRON_PENDING_CREDIT . " 
																	WHERE cron_pending_credit_trans_type = 'B' 
																		AND cron_pending_credit_trans_id = '" . tep_db_input($checking_user_row['buyback_request_group_id']) . "'";
                                    $cron_job_verify_result_sql = tep_db_query($cron_job_verify_select_sql);

                                    if (!tep_db_num_rows($cron_job_verify_result_sql)) {
                                        $cron_pending_credit_data_array = array('cron_pending_credit_trans_type' => 'B',
                                            'cron_pending_credit_trans_id' => tep_db_input($checking_user_row['buyback_request_group_id']),
                                            'cron_pending_credit_trans_created_date' => $buyback_product_row['buyback_request_group_date'],
                                            'cron_pending_credit_trans_completed_date' => 'now()',
                                            'cron_pending_credit_mature_period' => $cron_pending_credit_mature_period,
                                            'cron_pending_credit_trans_status' => 3
                                        );
                                        tep_db_perform(TABLE_CRON_PENDING_CREDIT, $cron_pending_credit_data_array);
                                    }
                                    // End of preparing data for scheduled cron job
                                }
                            }
                        } else if ($hold_buyback == true) {
                            if (defined('BUYBACK_ITEM_DELIVERED_OTHER_METHOD_TAG_ID')) {
                                list($co_tag_id, $bo_tag_id) = split_dep(',', BUYBACK_ITEM_DELIVERED_OTHER_METHOD_TAG_ID);
                                // Add CO tag
                                if (tep_not_null($co_tag_id)) {
                                    $assign_orders_tag_update_sql = "UPDATE " . TABLE_ORDERS . " SET orders_tag_ids = IF (orders_tag_ids='', '" . (int) $co_tag_id . "', CONCAT_WS(',', orders_tag_ids, '" . (int) $co_tag_id . "')) WHERE orders_id = '" . (int) $checking_user_row['orders_id'] . "' AND NOT FIND_IN_SET('" . (int) $co_tag_id . "', orders_tag_ids)";
                                    tep_db_query($assign_orders_tag_update_sql);
                                }

                                // Add BO tag
                                if (tep_not_null($bo_tag_id)) {
                                    $assign_orders_tag_update_sql = "UPDATE " . TABLE_BUYBACK_REQUEST_GROUP . " SET orders_tag_ids = IF (orders_tag_ids='', '" . (int) $bo_tag_id . "', CONCAT_WS(',', orders_tag_ids, '" . (int) $bo_tag_id . "')) WHERE buyback_request_group_id = '" . (int) $checking_user_row['buyback_request_group_id'] . "' AND NOT FIND_IN_SET('" . (int) $bo_tag_id . "', orders_tag_ids)";
                                    tep_db_query($assign_orders_tag_update_sql);
                                }
                            }
                        }
                    } else {
                        echo "<error>Invalid Member</error>";
                    }
                } else {
                    echo "<error>Invalid ID</error>";
                }
            } else {
                echo "<error>Invalid ID</error>";
            }
            break;
        case 'delivery_dispute':
            $op_history_id = isset($_GET['op_history_id']) ? $_GET['op_history_id'] : '';
            $customer_email_address = tep_get_customers_email($_SESSION['customer_id']);

            if (tep_not_null($op_history_id)) {
                $dispute_content = isset($_POST['dispute_content']) ? $_POST['dispute_content'] : '';

                $checking_user_select_sql = "	SELECT oph.orders_id, o.customers_id, oph.delivered_amount, oph.orders_products_history_id, oph.buyback_request_group_id, oph.received 
												FROM " . TABLE_ORDERS_PRODUCTS_HISTORY . " AS oph 
												INNER JOIN " . TABLE_ORDERS . " AS o 
													ON (oph.orders_id = o.orders_id) 
												WHERE oph.orders_products_history_id = '" . (int) $op_history_id . "'";
                $checking_user_result_sql = tep_db_query($checking_user_select_sql);
                if ($checking_user_row = tep_db_fetch_array($checking_user_result_sql)) {
                    if ($checking_user_row['customers_id'] == $_SESSION['customer_id']) {
                        if (!tep_not_null($checking_user_row['received'])) {
                            $comment = '';

                            if (tep_not_null($dispute_content)) {
                                $update_history_data_sql['dispute_comment'] = $dispute_content;
                            }

                            $update_history_data_sql['changed_by'] = $customer_email_address;
                            $update_history_data_sql['received'] = '0';
                            $update_history_data_sql['last_updated'] = 'now()';
                            tep_db_perform(TABLE_ORDERS_PRODUCTS_HISTORY, $update_history_data_sql, 'update', ' orders_products_history_id = "' . (int) $op_history_id . '" ');

                            if (tep_not_null($checking_user_row['buyback_request_group_id'])) {
                                $comment = '##BO##' . (int) $checking_user_row['buyback_request_group_id'] . '## - ';
                                $qty = $checking_user_row['delivered_amount'];

                                $update_buyback_history_data_sql = array('buyback_request_group_id' => $checking_user_row['buyback_request_group_id'],
                                    'buyback_status_id' => 0,
                                    'date_added' => 'now()',
                                    'customer_notified' => 0,
                                    'comments' => "Customer complain didn't receive " . $qty . " qty in this transactions",
                                    'set_as_buyback_remarks' => 1,
                                    'changed_by' => 'system'
                                );

                                tep_db_perform(TABLE_BUYBACK_STATUS_HISTORY, $update_buyback_history_data_sql, 'insert');
                            }

                            $comment .= 'Customer claimed did not receive';
                            $comment .= '<br />* ';
                            $comment .= tep_not_null($dispute_content) ? $dispute_content : "I didn't received it. Where is my gold?";

                            $comment_array = array('orders_id' => $checking_user_row['orders_id'],
                                'orders_status_id' => '0',
                                'date_added' => 'now()',
                                'customer_notified' => '0',
                                'comments' => tep_db_prepare_input($comment),
                                'comments_type' => '0',
                                'set_as_order_remarks' => '0',
                                'changed_by' => tep_get_customers_email($_SESSION['customer_id'])
                            );
                            tep_db_perform(TABLE_ORDERS_STATUS_HISTORY, $comment_array);

                            if (defined('BUYBACK_ITEM_NOT_RECEIVED_TAG_ID')) {
                                list($co_tag_id, $bo_tag_id) = split_dep(',', BUYBACK_ITEM_NOT_RECEIVED_TAG_ID);
                                // Add CO tag
                                if (tep_not_null($co_tag_id)) {
                                    $assign_orders_tag_update_sql = "UPDATE " . TABLE_ORDERS . " SET orders_tag_ids = IF (orders_tag_ids='', '" . (int) $co_tag_id . "', CONCAT_WS(',', orders_tag_ids, '" . (int) $co_tag_id . "')) WHERE orders_id = '" . (int) $checking_user_row['orders_id'] . "' AND NOT FIND_IN_SET('" . (int) $co_tag_id . "', orders_tag_ids)";
                                    tep_db_query($assign_orders_tag_update_sql);
                                }

                                // Add BO tag
                                if (tep_not_null($bo_tag_id)) {
                                    $assign_orders_tag_update_sql = "UPDATE " . TABLE_BUYBACK_REQUEST_GROUP . " SET orders_tag_ids = IF (orders_tag_ids='', '" . (int) $bo_tag_id . "', CONCAT_WS(',', orders_tag_ids, '" . (int) $bo_tag_id . "')) WHERE buyback_request_group_id = '" . (int) $checking_user_row['buyback_request_group_id'] . "' AND NOT FIND_IN_SET('" . (int) $bo_tag_id . "', orders_tag_ids)";
                                    tep_db_query($assign_orders_tag_update_sql);
                                }
                            }
                        } else {
                            echo "<error>counting_expired</error>";
                        }
                    } else {
                        echo "<error>Invalid Member</error>";
                    }
                } else {
                    echo "<error>Invalid ID</error>";
                }
            } else {
                echo "<error>Invalid ID</error>";
            }

            break;
        case 'stop_eta':
            if (tep_not_null($order_products_id)) {
                $checking_user_select_sql = "	SELECT o.orders_id, o.customers_id, op.orders_products_is_compensate 
												FROM " . TABLE_ORDERS . " AS o 
												INNER JOIN " . TABLE_ORDERS_PRODUCTS . " AS op 
													ON (o.orders_id = op.orders_id) 
												WHERE op.orders_products_id = '" . (int) $order_products_id . "'";
                $checking_user_result_sql = tep_db_query($checking_user_select_sql);
                if ($checking_user_row = tep_db_fetch_array($checking_user_result_sql)) {
                    if ($_SESSION['customer_id'] == $checking_user_row['customers_id']) {
                        $clear_orders_products_eta = false;
                        $total_buyback_time = 0;
                        $now_time = date("Y-m-d H:i:s");

                        if ($checking_user_row['orders_products_is_compensate'] != 1) {
                            $get_opid_select_sql = "	SELECT orders_products_id, parent_orders_products_id 
														FROM " . TABLE_ORDERS_PRODUCTS . " 
														WHERE parent_orders_products_id = '" . (int) $order_products_id . "'";
                            $get_opid_result_sql = tep_db_query($get_opid_select_sql);
                            if ($get_opid_row = tep_db_fetch_array($get_opid_result_sql)) {
                                $child_order_product_id = $get_opid_row['orders_products_id'];

                                $update_purchase_eta_data_sql = array('orders_products_purchase_eta' => '-999');
                                tep_db_perform(TABLE_ORDERS_PRODUCTS, $update_purchase_eta_data_sql, 'update', ' orders_products_id = "' . (int) $child_order_product_id . '"');
                                tep_db_perform(TABLE_ORDERS_PRODUCTS, $update_purchase_eta_data_sql, 'update', ' orders_products_id = "' . (int) $order_products_id . '"');

                                $clear_orders_products_eta = true;
                            }
                        } else {
                            $child_order_product_id = $order_products_id;

                            $update_purchase_eta_data_sql = array('orders_products_purchase_eta' => '-999');
                            tep_db_perform(TABLE_ORDERS_PRODUCTS, $update_purchase_eta_data_sql, 'update', ' orders_products_id = "' . (int) $order_products_id . '"');
                            $clear_orders_products_eta = true;
                        }

                        $get_expiry_hour_select_sql = "	SELECT expiry_hour, start_time, total_buyback_time 
														FROM " . TABLE_ORDERS_PRODUCTS_ETA . " 
														WHERE orders_products_id = '" . (int) $child_order_product_id . "'";
                        $get_expiry_hour_result_sql = tep_db_query($get_expiry_hour_select_sql);
                        if ($get_expiry_hour_row = tep_db_fetch_array($get_expiry_hour_result_sql)) {
                            $total_buyback_time = tep_day_diff($get_expiry_hour_row['start_time'], $now_time, 'sec');
                            $total_buyback_time += $get_expiry_hour_row['total_buyback_time'];

                            $accumulated_buyback_time = tep_sec_to_daytime($total_buyback_time);

                            $customer_email_address = tep_get_customers_email($_SESSION['customer_id']);

                            $insert_record_data_sql['changed_by'] = $customer_email_address;
                            $insert_record_data_sql['date_added'] = 'now()';
                            $insert_record_data_sql['last_updated'] = 'now()';
                            $insert_record_data_sql['orders_id'] = $checking_user_row['orders_id'];
                            $insert_record_data_sql['orders_products_id'] = $child_order_product_id;
                            $insert_record_data_sql['orders_products_history_record'] = 'TEXT_BUYBACK_CLOSED:~:TEXT_BUYBACK_CLOSED_MANUALLY:~:' . (int) $get_expiry_hour_row['expiry_hour'];

                            tep_db_perform(TABLE_ORDERS_PRODUCTS_HISTORY, $insert_record_data_sql, 'insert');
                        }

                        if ($clear_orders_products_eta) {
                            $closed_buyback_data_sql['expiry_hour'] = '0';
                            $closed_buyback_data_sql['total_buyback_time'] = $total_buyback_time;

                            tep_db_perform(TABLE_ORDERS_PRODUCTS_ETA, $closed_buyback_data_sql, 'update', ' orders_products_id = "' . (int) $child_order_product_id . '"');
                        }
                    }
                }

                echo "<accumulated_buyback_time>" . $accumulated_buyback_time . "</accumulated_buyback_time>";
            }

            break;
        case 'eta_counter':
            $eta = isset($_GET['eta']) ? (int) $_GET['eta'] : 1;
            $type = isset($_GET['type']) ? $_GET['type'] : 'reset';

            $customer_email_address = tep_get_customers_email($_SESSION['customer_id']);

            $eta = $eta > 10 ? '10' : $eta; // forced eta cannot bigger than 10

            $checking_user_select_sql = "	SELECT o.orders_id, o.customers_id, op.orders_products_is_compensate 
											FROM " . TABLE_ORDERS . " AS o 
											INNER JOIN " . TABLE_ORDERS_PRODUCTS . " AS op 
												ON (o.orders_id = op.orders_id) 
											WHERE op.orders_products_id = '" . (int) $order_products_id . "'";
            $checking_user_result_sql = tep_db_query($checking_user_select_sql);
            if ($checking_user_row = tep_db_fetch_array($checking_user_result_sql)) {
                if ($_SESSION['customer_id'] == $checking_user_row['customers_id']) {
                    $child_order_product_id = '';
                    $parent_orders_products_id = '';

                    $get_opid_select_sql = "	SELECT orders_products_id, parent_orders_products_id 
												FROM " . TABLE_ORDERS_PRODUCTS . " 
												WHERE parent_orders_products_id = '" . (int) $order_products_id . "'";
                    $get_opid_result_sql = tep_db_query($get_opid_select_sql);
                    $get_opid_num = tep_db_num_rows($get_opid_result_sql);
                    if ($get_opid_row = tep_db_fetch_array($get_opid_result_sql)) {
                        $child_order_product_id = $get_opid_row['orders_products_id'];
                        $parent_orders_products_id = $get_opid_row['parent_orders_products_id'];
                    } else {
                        $child_order_product_id = $order_products_id;
                    }

                    if (($get_opid_num > 0) || ($checking_user_row['orders_products_is_compensate'] == 1)) {
                        $expiry_hour = $start_time = $total_buyback_time = 0;

                        $now_time = date("Y-m-d H:i:s");

                        $update_purchase_eta_data_sql = array('orders_products_purchase_eta' => 'NULL');
                        tep_db_perform(TABLE_ORDERS_PRODUCTS, $update_purchase_eta_data_sql, 'update', ' orders_products_id = "' . (int) $child_order_product_id . '" AND orders_products_purchase_eta = "-999"');

                        if (tep_not_null($parent_orders_products_id)) {
                            tep_db_perform(TABLE_ORDERS_PRODUCTS, $update_purchase_eta_data_sql, 'update', ' orders_products_id = "' . (int) $parent_orders_products_id . '" AND orders_products_purchase_eta = "-999"');
                        }

                        $op_eta_select_sql = "	SELECT expiry_hour, start_time, total_buyback_time 
												FROM " . TABLE_ORDERS_PRODUCTS_ETA . " 
												WHERE orders_products_id = '" . (int) $child_order_product_id . "'";
                        $op_eta_result_sql = tep_db_query($op_eta_select_sql);
                        if ($op_eta_row = tep_db_fetch_array($op_eta_result_sql)) {
                            switch ($type) {
                                case 'reset':
                                    $update_eta_data_sql['expiry_hour'] = (int) $eta;
                                    $update_eta_data_sql['start_time'] = date("Y-m-d H:i:s");

                                    tep_db_perform(TABLE_ORDERS_PRODUCTS_ETA, $update_eta_data_sql, 'update', ' orders_products_id = "' . (int) $child_order_product_id . '"');

                                    $insert_record_data_sql['changed_by'] = $customer_email_address;
                                    $insert_record_data_sql['date_added'] = 'now()';
                                    $insert_record_data_sql['last_updated'] = 'now()';
                                    $insert_record_data_sql['orders_id'] = $checking_user_row['orders_id'];
                                    $insert_record_data_sql['orders_products_id'] = $child_order_product_id;
                                    $insert_record_data_sql['orders_products_history_record'] = 'TEXT_BUYBACK_OPENED:~:TEXT_BUYBACK_OPENED_DURATION:~:' . (int) $eta;

                                    tep_db_perform(TABLE_ORDERS_PRODUCTS_HISTORY, $insert_record_data_sql, 'insert');

                                    // Assign to VIP is a priority
                                    $vipOrderObj = new vip_order($child_order_product_id);
                                    $vipOrderObj->get_orders_details();
                                    if ($vipOrderObj->check_vip_mode($vipOrderObj->order_detail['buyback_categories_id'])) {
                                        $vipOrderObj->get_available_supplier();
                                        $assign_order_item = array();
                                        $assign_order_item[] = "&raquo; " . $vipOrderObj->order_detail['products_name'];

                                        if (count($vipOrderObj->assigned_supplier)) {
                                            $assign_order_str = "The following items have been assign to supplier:" . "\n" . implode("\n", $assign_order_item) . "\n" . implode("\n", $vipOrderObj->assigned_supplier);
                                            tep_db_query("INSERT INTO " . TABLE_ORDERS_STATUS_HISTORY . " (orders_id, orders_status_id, date_added, customer_notified, comments, comments_type, changed_by) VALUES ('" . (int) $vipOrderObj->order_detail['orders_id'] . "', 0, now(), '0', '" . tep_db_input($assign_order_str) . "', 1, '" . $customer_email_address . "')");
                                        }
                                    }

                                    // Count Up with the accumulated open buyback time
                                    $total_buyback_time = tep_day_diff($op_eta_row['start_time'], $now_time, 'sec');
                                    if (($op_eta_row['total_buyback_time'] > 0) || ($total_buyback_time > 0)) {
                                        $total_buyback_time = $op_eta_row['total_buyback_time'];
                                    }
                                    break;
                                case 'extend':
                                    $expiry_time = strtotime($op_eta_row['start_time'] . " +" . $op_eta_row['expiry_hour'] . " hour");

                                    if (strtotime($now_time) > $expiry_time) {
                                        $update_eta_data_sql['start_time'] = $now_time;
                                        $update_eta_data_sql['expiry_hour'] = $eta;
                                    } else {
                                        $expiry_hour = $op_eta_row['expiry_hour'];
                                        $start_time = tep_day_diff($op_eta_row['start_time'], $now_time, 'sec');

                                        $total_up_eta = $eta + $op_eta_row['expiry_hour'];
                                        $update_eta_data_sql['expiry_hour'] = $total_up_eta;
                                    }

                                    tep_db_perform(TABLE_ORDERS_PRODUCTS_ETA, $update_eta_data_sql, 'update', ' orders_products_id = "' . (int) $child_order_product_id . '"');

                                    $insert_record_data_sql['changed_by'] = $customer_email_address;
                                    $insert_record_data_sql['date_added'] = 'now()';
                                    $insert_record_data_sql['last_updated'] = 'now()';
                                    $insert_record_data_sql['orders_id'] = $checking_user_row['orders_id'];
                                    $insert_record_data_sql['orders_products_id'] = $child_order_product_id;
                                    $insert_record_data_sql['orders_products_history_record'] = 'TEXT_BUYBACK_OPENED:~:TEXT_BUYBACK_OPENED_DURATION_EXTEND:~:' . (int) $eta;

                                    tep_db_perform(TABLE_ORDERS_PRODUCTS_HISTORY, $insert_record_data_sql, 'insert');
                                    break;
                            }
                        } else {
                            // Insert new record;
                            $insert_eta_data_sql['orders_products_id'] = (int) $child_order_product_id;
                            $insert_eta_data_sql['expiry_hour'] = $eta;
                            $insert_eta_data_sql['start_time '] = $now_time;

                            tep_db_perform(TABLE_ORDERS_PRODUCTS_ETA, $insert_eta_data_sql, 'insert');

                            $insert_record_data_sql['changed_by'] = $customer_email_address;
                            $insert_record_data_sql['date_added'] = 'now()';
                            $insert_record_data_sql['last_updated'] = 'now()';
                            $insert_record_data_sql['orders_id'] = $checking_user_row['orders_id'];
                            $insert_record_data_sql['orders_products_id'] = $child_order_product_id;
                            $insert_record_data_sql['orders_products_history_record'] = 'TEXT_BUYBACK_OPENED:~:TEXT_BUYBACK_OPENED_DURATION:~:' . (int) $eta;

                            tep_db_perform(TABLE_ORDERS_PRODUCTS_HISTORY, $insert_record_data_sql, 'insert');

                            // Assign to VIP is a priority
                            $vipOrderObj = new vip_order($child_order_product_id);
                            $vipOrderObj->get_orders_details();
                            if ($vipOrderObj->check_vip_mode($vipOrderObj->order_detail['buyback_categories_id'])) {
                                $vipOrderObj->get_available_supplier();
                                $assign_order_item = array();
                                $assign_order_item[] = "&raquo; " . $vipOrderObj->order_detail['products_name'];

                                if (count($vipOrderObj->assigned_supplier)) {
                                    $customer_email_address = tep_get_customers_email($_SESSION['customer_id']);
                                    $assign_order_str = "The following items have been assign to supplier:" . "\n" . implode("\n", $assign_order_item) . "\n" . implode("\n", $vipOrderObj->assigned_supplier);
                                    tep_db_query("INSERT INTO " . TABLE_ORDERS_STATUS_HISTORY . " (orders_id, orders_status_id, date_added, customer_notified, comments, comments_type, changed_by) VALUES ('" . (int) $vipOrderObj->order_detail['orders_id'] . "', 0, now(), '0', '" . tep_db_input($assign_order_str) . "', 1, '" . $customer_email_address . "')");
                                }
                            }
                        }

                        echo "<expiry_hour>" . $expiry_hour . "</expiry_hour>";
                        echo "<start_time>" . $start_time . "</start_time>";
                        echo "<total_buyback_time>" . $total_buyback_time . "</total_buyback_time>";
                    } else {
                        echo "<error>Order product id couldn't be found!</error>";
                    }
                } else {
                    echo "<error>You have no authorize to access this.</error>";
                }
            } else {
                echo "<error>Order product id couldn't be found!</error>";
            }

            break;
        case 'get_billing_content':
            if (isset($_SESSION['customer_id'])) {
                $header = '';
                $content = '';

                $user_obj = new user();
                $is_billing_info_complete = $user_obj->is_billing_info_completed();

                if ($is_billing_info_complete !== TRUE) {
                    $header = HEADER_CONFIRM_BILLING_INFO;
                    $content = get_confirm_billing_info_content($_SESSION['customer_id']);
                }

                echo '<billing_info_status>' . ($is_billing_info_complete !== TRUE ? '0' : '1') . '</billing_info_status>' .
                '<code>' . $is_billing_info_complete . '</code>' .
                '<content><![CDATA[' . $content . ']]></content>' .
                '<header>' . $header . '</header>';

                unset($user_obj);
            } else {
                echo '<reload>1</reload>';
            }
            break;
        case 'cfm_billing':
            $error = FALSE;
            $error_array = array();
            $country_dial_code = '';

            $user_obj = new user();
            $resp_result = $user_obj->editAccount($_POST);

            if (isset($resp_result['error'])) {
                if ($resp_result['error'] !== TRUE) {
                    $country_id_select_sql = "  SELECT countries_international_dialing_code 
                                                FROM " . TABLE_COUNTRIES . " 
                                                WHERE countries_id ='" . tep_db_input($resp_result['customers_country_dialing_code_id']) . "'";
                    $country_id_result_sql = tep_db_query($country_id_select_sql);
                    if ($country_id_row = tep_db_fetch_array($country_id_result_sql)) {
                        $country_dial_code = $country_id_row['countries_international_dialing_code'];
                    }

                    if (tep_is_mobile_num_exist($resp_result['customers_telephone'], $resp_result['customers_country_dialing_code_id'], $customer_id, $country_dial_code)) {
                        echo '<error_message><![CDATA[<div class="icon_err_msg">' . ENTRY_CONTACT_NUMBER_EXIST_ERROR . '</div>]]></error_message>';
                    } else if (tep_info_verified_check($_SESSION['customer_id'], $country_dial_code . $resp_result['telephone_number'], 'telephone') !== 1) {
                        $customers_security_obj->deactive_token($_SESSION['customer_id'], 'verify_phone_request');
                        $request_result = $customers_security_obj->request_security_token($_SESSION['customer_id'], 'verify_phone_request');

                        if ((int) $request_result['res_code'] > 0) {
                            $content = '<div style="padding:20px 30px">' .
                                    $customers_security_obj->show_customer_security_question_html("inputLabelNew", "inputBoxContentsNew", array('withHeader' => FALSE, 'requestIcon' => tep_image(DIR_WS_ICONS . 'success.gif'), 'requestMessage' => $request_result['res_text'])) .
                                    '</div>' .
                                    '<div style="padding:0 30px 30px">' . TEXT_CHANGE_PHONE_NUMBER . '</div>' .
                                    '<div class="dotborder"></div>' .
                                    '<div style="padding: 15px 28px 8px;">
                                            <div style="float:right;">' . tep_image_button2('gray_short', 'javascript:void(0);', BUTTON_CONFIRM, '', 'onclick="cfm_security_token();"') . '</div>
                                            <div class="clrFx"></div>
                                        </div>';
                        } else {
                            // invalid request
                            $content = '<div style="padding:20px 30px"><div class="icon_err_msg">' . $request_result['res_text'] . '</div></div>' .
                                    '<div style="padding:0 30px 30px">' . TEXT_CHANGE_PHONE_NUMBER . '</div>';
                        }

                        echo '<header><![CDATA[<div><div class="lfloat"><span class="hd1">' . TEXT_SECRET_QNA . '</span></div><div class="lfloat" style="padding-left:3px">' . tep_image(DIR_WS_ICONS . 'help-small.png', sprintf(TEXT_REQUEST_TOKEN_HELP_MSG, tep_href_link(FILENAME_CUSTOMER_SUPPORT)), '16', '16', ' class="rightToolTip"') . '</div><div class="clrFx"></div></div>]]></header>';
                        echo '<content><![CDATA[' . $content . ']]></content>';
                    } else {
                        // valid
                        echo '<form_submit>1</form_submit>';
                    }
                } else {
                    echo '<error_message><![CDATA[<div class="icon_err_msg">' . implode('</div><div class="icon_err_msg">', $resp_result['error_array']) . '</div>]]></error_message>';
                }
            }

            unset($user_obj);
            break;
        case 'verify_last4digit':
            $last4digit = isset($_POST['last4digit']) ? tep_db_prepare_input($_POST['last4digit']) : '';

            $user_obj = new user();
            $response_result = $user_obj->verify_mobile_number_last4digit($_SESSION['customer_id'], $last4digit);

            if ($response_result['error'] === TRUE) {
                echo '<message><![CDATA[<div class="icon_err_msg">' . $response_result['error_message'] . '</div>]]></message>';
            } else {
                $request_result = $customers_security_obj->request_security_token_again_via_email($_SESSION['customer_id']);

                if ($request_result['res_code'] !== FALSE) {
                    echo '<message><![CDATA[' . BOX_SUCCESS_VALIDATE_LAST4DIGIT . ']]></message>';
                } else {
                    echo '<message><![CDATA[<div class="icon_err_msg">' . $request_result['res_text'] . '</div>]]></message>';
                }
            }

            echo '<error>' . ($response_result['error'] === TRUE ? '1' : '0') . '</error>';

            break;
        case 'resend_token':
            ob_start();
            ?>
            <div style="border: 1px solid #cecece; padding: 10px; width: 450px;margin-top:10px;">
                <table id="verify_mobile_num" width="100%" cellspacing="0" cellpadding="0" style="background-color:#ffffff;">
                    <tr><td colspan="3"><?php echo BOX_CHANGE_CONTACT_FILL_LAST4DIGIT_DESC ?></td></tr>
                    <tr><td colspan="3" style="padding:10px 0"><?php echo BOX_CHANGE_CONTACT_FILL_LAST4DIGIT ?></td></tr>
                    <tr>
                        <td style="width: 33%;">&nbsp;</td>
                        <td style="width: 33%; text-align: right; padding-right: 4px;">
                            <div class="ihd1"><?php echo tep_draw_input_field('last4digit', '', 'id="last4digit" style="width:125px" maxlength="4"') ?></div>
                        </td>
                        <td rowspan="2" style="width: 34%; vertical-align:top"><div id="last4digit_error_message"></div></td>
                    </tr>
                    <tr>
                        <td></td>
                        <td style="text-align:right;padding-top:10px"><?php echo tep_image_button2('gray_short', "javascript:void(0);", BUTTON_RESEND_SECURITY_TOKEN, '', ' onClick="verfy_last4digit()"'); ?></td>
                    </tr>
                </table>
            </div>
            <?php
            $verify_mobile_number_box_content = ob_get_contents();
            ob_end_clean();

            echo '<content><![CDATA[' . $verify_mobile_number_box_content . ']]></content>';

            break;
        case 'match_security_answer':
            if ($customers_security_obj->validate_customer_security_answer(urldecode($_GET['answer']), 'account_edit', ENTRY_MISMATCH_ANSWER_ERROR) === true) {
                echo '<error>' . ENTRY_MISMATCH_ANSWER_ERROR . '</error>';
            } else {
                echo '<error>no</error>';
            }
            break;
        case 'match_security_tac':
            if ($customer_id) {
                $customer_complete_phone_info_array = tep_format_telephone($customer_id);

                if (tep_is_mobile_num_exist($customer_complete_phone_info_array['telephone_number'], $customer_complete_phone_info_array['country_id'], $customer_id, $customer_complete_phone_info_array['country_international_dialing_code'])) {
                    echo '<error><![CDATA[' . ENTRY_CONTACT_NUMBER_EXIST_ERROR . ']]></error>' .
                    '<error_icon><![CDATA[' . tep_image(DIR_WS_ICONS . 'error.gif') . ']]></error_icon>';
                } else {
                    if ($customers_security_obj->validate_customer_security_answer(urldecode($_GET['answer']), 'account_edit', ENTRY_MISMATCH_ANSWER_ERROR, 'verify_phone_request') === FALSE) {
                        tep_set_info_verified($_SESSION['customer_id']);
                        echo '<error>-</error>';
                    } else {
                        echo '<error>' . ENTRY_MISMATCH_ANSWER_ERROR . '</error>' .
                        '<error_icon><![CDATA[' . tep_image(DIR_WS_ICONS . 'error.gif') . ']]></error_icon>';
                    }
                }
            }

            break;
        case 'request_sms_token':   // used in edit profile
            $response_code = -1;
            $response_message = '';
            $request_result = $customers_security_obj->request_security_token($_SESSION['customer_id']);

            if ((int) $request_result['res_code'] == -2) {   // invalid mobile number, bring user to edit my account page.
                $response_message = $request_result['res_text'] . '&nbsp;' . sprintf(ENTRY_MISMATCH_ANSWER_RESEND_LINK, tep_href_link(FILENAME_ACCOUNT_EDIT, '', 'SSL'));
            } else if ((int) $request_result['res_code'] < 0) {
                $response_message = $request_result['res_text'] . '&nbsp;' . sprintf(ENTRY_MISMATCH_ANSWER_RESEND_LINK, 'javascript:resend_token();');
            } else {
                $response_message = $request_result['res_text'];

                if ($request_result['res_code'] > 0) {
                    $response_code = (int) $request_result['res_code'];
                } else {
                    $response_code = 0;
                }
            }

            echo '<res_code>' . $response_code . '</res_code>
                  <message><![CDATA[' . $response_message . ']]></message>';
            break;
        case 'request_sms_tac':
            $request_result = $customers_security_obj->request_security_token($_SESSION['customer_id'], 'verify_phone_request');

            echo '<res_code>' . ((int) $request_result['res_code'] > 0 ? $request_result['res_code'] : -1) . '</res_code>
                  <message><![CDATA[' . ((int) $request_result['res_code'] < 0 ? $request_result['res_text'] . '&nbsp;' . sprintf(ENTRY_MISMATCH_ANSWER_RESEND_LINK, tep_href_link(FILENAME_ACCOUNT_EDIT, '', 'SSL')) : $request_result['res_text']) . ']]></message>';
            break;
        case 'edit_phone':  // used in edit profile
            $complete_telephone_number = '';
//            $customers_country_dialing_code_id = '';
            $customer_complete_phone_info_array = tep_format_telephone($_SESSION['customer_id']);

            if (sizeof($customer_complete_phone_info_array) > 0) {
                $complete_telephone_number = '+' . $customer_complete_phone_info_array['country_international_dialing_code'] . "&nbsp;" . substr($customer_complete_phone_info_array['telephone_number'], 0, -4) . '****';
//                $customers_country_dialing_code_id = $customer_complete_phone_info_array['country_id'];
            }

            echo '<current_telephone><![CDATA[' . $complete_telephone_number . ']]></current_telephone>
                  <country_dialing_code_id><![CDATA[' . tep_get_ezsignup_country_list('edit_profile_country_code', 0, 'style="width:250px;"') . ']]></country_dialing_code_id>
                  <telephone><![CDATA[' . tep_draw_input_field('edit_profile_contact_number', '', 'autocomplete="off" id="contact_number"') . ']]></telephone>
                  <security><![CDATA[' . $customers_security_obj->show_customer_security_question_html("inputLabelNew", "inputBoxContentsNew") . ']]></security>';

            break;
        case 'save_phone':  // used in edit profile
            if (file_exists(DIR_WS_LANGUAGES . $_SESSION['language'] . '/' . FILENAME_ACCOUNT_EDIT)) {
                include_once(DIR_WS_LANGUAGES . $_SESSION['language'] . '/' . FILENAME_ACCOUNT_EDIT);
            }

            $error = FALSE;
            $error_message = array();

            if ($customers_security_obj->validate_customer_security_answer($_POST['answer'], 'account_edit', ENTRY_MISMATCH_ANSWER_ERROR) === true) {
                $error = 2;
                $error_message[] = ENTRY_MISMATCH_ANSWER_ERROR . '&nbsp;' . sprintf(ENTRY_MISMATCH_ANSWER_RESEND_LINK, 'javascript:resend_token();');
            }

            if ($error === FALSE) {
                $customer_select_sql = "	SELECT customers_country_dialing_code_id, customers_telephone, customers_email_address
                                            FROM " . TABLE_CUSTOMERS . " 
                                            WHERE customers_id = '" . $_SESSION['customer_id'] . "'";
                $customer_result_sql = tep_db_query($customer_select_sql);
                $customer_row = tep_db_fetch_array($customer_result_sql);

                $edit_profile_contact_number = tep_not_null($_POST['edit_profile_contact_number']) ? strip_tags(tep_db_prepare_input($_POST['edit_profile_contact_number'])) : "";
                $edit_profile_country_code = tep_not_null($_POST['edit_profile_country_code']) ? tep_db_prepare_input($_POST['edit_profile_country_code']) : "";

                if (is_numeric($edit_profile_country_code) == false) {
                    $error = 1;
                    $error_message[] = ENTRY_LOCATION_ERROR;
                }

                if (substr($edit_profile_contact_number, -4) == '****') {
                    $edit_profile_contact_number = $customer_row['customers_telephone'];

                    if ($edit_profile_country_code != $customer_row['customers_country_dialing_code_id']) {
                        if (tep_is_mobile_num_exist($edit_profile_contact_number, $edit_profile_country_code, $customer_id)) {
                            $error = 1;
                            $error_message[] = ENTRY_CONTACT_NUMBER_EXIST_ERROR;
                        }
                    }
                } else {
                    $edit_profile_contact_number = tep_db_prepare_input(preg_replace('/[^\d]/', '', $edit_profile_contact_number));
                    $edit_profile_contact_number = tep_parse_telephone($edit_profile_contact_number, $edit_profile_country_code, 'id');

                    if (strlen($edit_profile_contact_number) < ENTRY_TELEPHONE_MIN_LENGTH) {
                        $error = 1;
                        $error_message[] = ENTRY_TELEPHONE_NUMBER_ERROR;
                    } else if (tep_is_mobile_num_exist($edit_profile_contact_number, $edit_profile_country_code, $customer_id)) {
                        $error = 1;
                        $error_message[] = ENTRY_CONTACT_NUMBER_EXIST_ERROR;
                    }
                }
            }

            if ($error === FALSE) {
                $log_object = new log_files($_SESSION['customer_id']);

                $sql_data_array = array('customers_country_dialing_code_id' => $edit_profile_country_code,
                    'customers_telephone' => $edit_profile_contact_number
                );
                tep_db_perform(TABLE_CUSTOMERS, $sql_data_array, 'update', "customers_id = '" . $_SESSION['customer_id'] . "'");

                tep_db_query("update " . TABLE_CUSTOMERS_INFO . " set customers_info_date_account_last_modified = now() where customers_info_id = '" . $_SESSION['customer_id'] . "'");

                if (tep_not_null($order_id)) {
                    $country_dialing_codes_array = tep_get_coutries_dialing_codes($edit_profile_country_code);
                    $country_name_array = tep_get_countries($edit_profile_country_code);

                    $update_order_telephone_data_sql = array(
                        'customers_telephone' => tep_db_prepare_input($edit_profile_contact_number),
                        'customers_telephone_country' => isset($country_name_array['countries_name']) ? $country_name_array['countries_name'] : '',
                        'customers_country_international_dialing_code' => isset($country_dialing_codes_array['countries_international_dialing_code']) ? $country_dialing_codes_array['countries_international_dialing_code'] : '',
                        'last_modified' => 'now()'
                    );

                    tep_db_perform(TABLE_ORDERS, $update_order_telephone_data_sql, 'update', " orders_id = '" . (int) $order_id . "' AND customers_id = '" . $_SESSION['customer_id'] . "'");
                    unset($update_order_telephone_data_sql);
                }

                $customer_log_result_sql = tep_db_query($customer_select_sql);
                $customer_new_log_row = tep_db_fetch_array($customer_log_result_sql);

                $customer_changes_array = $log_object->detect_changes($customer_row, $customer_new_log_row);
                $customer_changes_formatted_array = $log_object->construct_log_message($customer_changes_array);

                if (count($customer_changes_formatted_array)) {
                    $changes_str = 'Changes made:' . "\n";

                    for ($i = 0; $i < count($customer_changes_formatted_array); $i++) {
                        if (count($customer_changes_formatted_array[$i])) {
                            foreach ($customer_changes_formatted_array[$i] as $field => $res) {
                                if (isset($res['plain_result']) && $res['plain_result'] == '1') {
                                    $changes_str .= $res['text'] . "\n";
                                } else {
                                    $changes_str .= '<b>' . $res['text'] . '</b>: ' . $res['from'] . ' --> ' . $res['to'] . "\n";
                                }
                            }
                        }
                    }
                    $log_object->insert_customer_history_log($customer_new_log_row['customers_email_address'], $changes_str);
                }
            }

            if ($error) {
                if ($error == 2) {
                    echo '<message><![CDATA[' . implode('<br>', $error_message) . ']]></message>';
                } else {
                    echo '<message><![CDATA[<div class="icon_err_msg">' . implode('</div><div class="icon_err_msg">', $error_message) . '</div>]]></message>';
                }
            } else {
                echo '<message><![CDATA[' . tep_image(DIR_WS_ICONS . 'success.gif') . '&nbsp; ' . SUCCESS_ACCOUNT_MOBILE_UPDATED . ']]></message>';
            }

            echo '<error>' . ($error !== FALSE ? $error : '0') . '</error>';

            break;
        case 'edit_delivery_info':
            if (isset($_SESSION['customer_id'])) {
                require_once(DIR_WS_LANGUAGES . $language . '/' . FILENAME_ACCOUNT_HISTORY_INFO);
                $log_object = new log_files($_SESSION['customer_id']);

                $char_name = isset($_POST['char_name']) ? tep_db_prepare_input($_POST['char_name']) : '';
                $account_name = isset($_POST['account_name']) ? tep_db_prepare_input($_POST['account_name']) : '';
                $account_pwd = isset($_POST['account_pwd']) ? $_POST['account_pwd'] : '';
                $char_wow_account = isset($_POST['char_wow_account']) ? tep_db_prepare_input($_POST['char_wow_account']) : '';
                $delivery_mode = isset($_POST['delivery_mode']) ? (int) $_POST['delivery_mode'] : '';
                $order_products_id = isset($_POST['order_products_id']) ? (int) $_POST['order_products_id'] : '';
                $orders_status = '';
                $orders_id = '';
                $previous_delivery_mode = '';
                $delivery_mode_array = array("", OPTION_FACE_TO_FACE, OPTION_PUT_IN_MY_ACCOUNT, OPTION_BY_MAIL, OPTION_OPEN_STORE, "Contract");

                $get_order_status_select_sql = "SELECT o.orders_status, o.orders_id, o.customers_id, op.orders_products_is_compensate  
												FROM " . TABLE_ORDERS . " AS o 
												INNER JOIN " . TABLE_ORDERS_PRODUCTS . " AS op 
													ON (o.orders_id = op.orders_id)
												WHERE op.orders_products_id = '" . (int) $order_products_id . "' 
													AND o.customers_id = '" . tep_db_input($_SESSION['customer_id']) . "'";
                $get_order_status_result_sql = tep_db_query($get_order_status_select_sql);
                if ($get_order_status_row = tep_db_fetch_array($get_order_status_result_sql)) {
                    $order_status = $get_order_status_row['orders_status'];
                    $orders_id = $get_order_status_row['orders_id'];
                    $order_customers_id = $get_order_status_row['customers_id'];
                    $is_compensate = $get_order_status_row['orders_products_is_compensate'];
                }

                $allow_order_status = array(1, 2, 7); // Only allow pending, verifying and processing to edit delivery info
                if (in_array($order_status, $allow_order_status) && $order_customers_id == $_SESSION['customer_id']) {
                    $matching_sql = $is_compensate == 1 ? 'op.orders_products_id' : 'op.parent_orders_products_id';
                    $buyback_order_process_select_sql = "	SELECT br.orders_products_id 
															FROM " . TABLE_BUYBACK_REQUEST . " as br 
															INNER JOIN " . TABLE_BUYBACK_REQUEST_GROUP . " as brg 
																ON (br.buyback_request_group_id = brg.buyback_request_group_id) 
															INNER JOIN  " . TABLE_ORDERS_PRODUCTS . " AS op 
																ON (br.orders_products_id = op.orders_products_id)
															WHERE " . $matching_sql . " = '" . (int) $order_products_id . "' 
																AND (brg.buyback_status_id = 1 OR brg.buyback_status_id = 2)";
                    $buyback_order_process_result_sql = tep_db_query($buyback_order_process_select_sql);
                    $buyback_order_process_num = tep_db_num_rows($buyback_order_process_result_sql);

                    if ($buyback_order_process_num == 0) {
                        $delivery_old_log_row = array();
                        $delivery_new_log_row = array();
                        $char_account_name = '';
                        $char_account_pwd = '';
                        $char_pass_sql = '';
                        $char_wow_account_name = '';
                        $child_order_product_id = '';

                        $delivery_key[] = 'char_name';
                        $delivery_key[] = 'char_account_name';
                        $delivery_key[] = 'char_account_pwd';
                        $delivery_key[] = 'char_wow_account';
                        $delivery_key[] = 'delivery_mode';

                        $extra_info_array = tep_draw_products_extra_info($order_products_id);
                        $previous_delivery_mode = $extra_info_array['delivery_mode'];

                        // get old record
                        $delivery_info_select_sql = "	SELECT orders_products_extra_info_key, orders_products_extra_info_value 
														FROM " . TABLE_ORDERS_PRODUCTS_EXTRA_INFO . " 
														WHERE orders_products_id = '" . (int) $order_products_id . "'";
                        $delivery_info_result_sql = tep_db_query($delivery_info_select_sql);
                        while ($delivery_info_row = tep_db_fetch_array($delivery_info_result_sql)) {
                            // Only match with $delivery_key
                            if (in_array($delivery_info_row['orders_products_extra_info_key'], $delivery_key)) {
                                if ($delivery_info_row['orders_products_extra_info_key'] != 'delivery_mode') {
                                    $delivery_old_log_row[$delivery_info_row['orders_products_extra_info_key']] = $delivery_info_row['orders_products_extra_info_value'];
                                } else {
                                    $delivery_old_log_row[$delivery_info_row['orders_products_extra_info_key']] = $delivery_mode_array[$delivery_info_row['orders_products_extra_info_value']];
                                }
                            }
                        }

                        $get_opid_select_sql = "	SELECT orders_products_id
													FROM " . TABLE_ORDERS_PRODUCTS . " 
													WHERE parent_orders_products_id = '" . (int) $order_products_id . "'";
                        $get_opid_result_sql = tep_db_query($get_opid_select_sql);
                        if ($get_opid_row = tep_db_fetch_array($get_opid_result_sql)) {
                            $child_order_product_id = $get_opid_row['orders_products_id'];
                        } else {
                            $child_order_product_id = $order_products_id;
                        }

                        // get new record
                        $delivery_new_log_row['char_name'] = $char_name;
                        $delivery_new_log_row['char_account_name'] = $account_name;
                        $delivery_new_log_row['char_account_pwd'] = $account_pwd;
                        $delivery_new_log_row['char_wow_account'] = $char_wow_account;
                        $delivery_new_log_row['delivery_mode'] = $delivery_mode_array[$delivery_mode];

                        switch ($delivery_mode) {
                            case '1':
                            case '4':
                                unset($delivery_old_log_row['char_account_name']);
                                unset($delivery_old_log_row['char_account_pwd']);
                                unset($delivery_old_log_row['char_wow_account']);

                                unset($delivery_new_log_row['char_account_name']);
                                unset($delivery_new_log_row['char_account_pwd']);
                                unset($delivery_new_log_row['char_wow_account']);
                                break;
                            case '2':
                                if (!tep_not_null($delivery_old_log_row['char_account_name'])) {
                                    unset($delivery_old_log_row['char_account_name']);
                                    unset($delivery_new_log_row['char_account_name']);
                                    $char_account_name = 'New Account Name has been created.';
                                }

                                if (!tep_not_null($delivery_old_log_row['char_account_pwd'])) {
                                    $char_account_pwd = 'New Password has been created.';
                                } elseif ($delivery_old_log_row['char_account_pwd'] != $account_pwd) {
                                    if (tep_not_null($account_pwd)) {
                                        $char_account_pwd = 'Password Changed';
                                    } else {
                                        $char_pass_sql = " AND orders_products_extra_info_key <> 'char_account_pwd'";
                                    }
                                }

                                if (!tep_not_null($delivery_old_log_row['char_wow_account'])) {
                                    $char_wow_account_name = '<b>WOW Account Name</b>: ' . $char_wow_account;
                                }
                                unset($delivery_old_log_row['char_account_pwd']);
                                unset($delivery_new_log_row['char_account_pwd']);

                                break;
                            case '3':
                                unset($delivery_old_log_row['char_account_name']);
                                unset($delivery_old_log_row['char_account_pwd']);
                                unset($delivery_old_log_row['char_wow_account']);

                                unset($delivery_new_log_row['char_account_name']);
                                unset($delivery_new_log_row['char_account_pwd']);
                                unset($delivery_new_log_row['char_wow_account']);
                                break;
                            case '4':
                                break;
                        }

                        $delivery_changes_array = $log_object->detect_changes($delivery_old_log_row, $delivery_new_log_row);
                        $delivery_changes_formatted_array = $log_object->construct_log_message($delivery_changes_array);

                        if (count($delivery_changes_formatted_array) || tep_not_null($char_account_name) || tep_not_null($char_account_pwd) || tep_not_null($char_wow_account)) {
                            $changes_str = 'Changes made:' . "\n";

                            for ($i = 0; $i < count($delivery_changes_formatted_array); $i++) {
                                if (count($delivery_changes_formatted_array[$i])) {
                                    foreach ($delivery_changes_formatted_array[$i] as $field => $res) {
                                        if (isset($res['plain_result']) && $res['plain_result'] == '1') {
                                            $changes_str .= $res['text'] . "\n";
                                        } else {
                                            $changes_str .= '<b>' . $res['text'] . '</b>: ' . $res['from'] . ' --> ' . $res['to'] . "\n";
                                        }
                                    }
                                }
                            }

                            if (tep_not_null($char_account_name)) {
                                $changes_str .= '<b>' . $char_account_name . '</b>' . "\n";
                            }
                            if (tep_not_null($char_account_pwd)) {
                                $changes_str .= '<b>' . $char_account_pwd . '</b>' . "\n";
                            }
                            if (tep_not_null($char_wow_account_name)) {
                                $changes_str .= $char_wow_account_name . "\n";
                            }

                            $customer_email = tep_get_customers_email($_SESSION['customer_id']);
                            tep_db_query("INSERT INTO " . TABLE_ORDERS_STATUS_HISTORY . " (orders_id, orders_status_id, date_added, customer_notified, comments, comments_type, changed_by) VALUES ('" . (int) $orders_id . "', '0', now(), '1', '" . tep_db_input($changes_str) . "', 1, '" . $customer_email . "')");
                            //$log_object->insert_customer_history_log($customer_email, $changes_str);
                        }

                        foreach ($delivery_key as $delivery_key_value) {
                            tep_db_query("DELETE FROM " . TABLE_ORDERS_PRODUCTS_EXTRA_INFO . " WHERE orders_products_id = '" . (int) $order_products_id . "' AND orders_products_extra_info_key = '" . $delivery_key_value . "' " . $char_pass_sql);
                        }

                        $insert_delivery_array = array('orders_products_id' => (int) $order_products_id,
                            'orders_products_extra_info_key' => 'delivery_mode',
                            'orders_products_extra_info_value' => $delivery_mode);
                        tep_db_perform(TABLE_ORDERS_PRODUCTS_EXTRA_INFO, $insert_delivery_array);

                        switch ($delivery_mode) {
                            case '1':
                            case '4':
                                if ($previous_delivery_mode == '2') {
                                    //if ($previous_delivery_mode == '2' || $previous_delivery_mode == '3') {
                                    $update_purchase_eta_data_sql = array('orders_products_purchase_eta' => '-999');
                                    tep_db_perform(TABLE_ORDERS_PRODUCTS, $update_purchase_eta_data_sql, 'update', ' orders_products_id = "' . (int) $child_order_product_id . '"');
                                    tep_db_perform(TABLE_ORDERS_PRODUCTS, $update_purchase_eta_data_sql, 'update', ' orders_products_id = "' . (int) $order_products_id . '"');

                                    $closed_buyback_data_sql = array('expiry_hour' => '0');
                                    tep_db_perform(TABLE_ORDERS_PRODUCTS_ETA, $closed_buyback_data_sql, 'update', ' orders_products_id = "' . (int) $child_order_product_id . '"');
                                }

                                $sql_data_array = array('orders_products_id' => (int) $order_products_id,
                                    'orders_products_extra_info_key' => 'char_name',
                                    'orders_products_extra_info_value' => $char_name);

                                tep_db_perform(TABLE_ORDERS_PRODUCTS_EXTRA_INFO, $sql_data_array);

                                break;
                            case '2':
                                if ($order_status == '2') {
                                    // Open Buyback
                                    $update_purchase_eta_data_sql = array('orders_products_purchase_eta' => 'NULL');
                                    tep_db_perform(TABLE_ORDERS_PRODUCTS, $update_purchase_eta_data_sql, 'update', ' orders_products_id = "' . (int) $child_order_product_id . '" AND orders_products_purchase_eta = "-999"');
                                    tep_db_perform(TABLE_ORDERS_PRODUCTS, $update_purchase_eta_data_sql, 'update', ' orders_products_id = "' . (int) $order_products_id . '" AND orders_products_purchase_eta = "-999"');

                                    $vipOrderObj = new vip_order($child_order_product_id);
                                    $vipOrderObj->get_orders_details();
                                    if ($vipOrderObj->check_vip_mode($vipOrderObj->order_detail['buyback_categories_id'])) {
                                        $vipOrderObj->get_available_supplier();
                                        $assign_order_item = array();
                                        $assign_order_item[] = "&raquo; " . $vipOrderObj->order_detail['products_name'];

                                        if (count($vipOrderObj->assigned_supplier)) {
                                            $customer_email_address = tep_get_customers_email($_SESSION['customer_id']);
                                            $assign_order_str = "The following items have been assign to supplier:" . "\n" . implode("\n", $assign_order_item) . "\n" . implode("\n", $vipOrderObj->assigned_supplier);
                                            tep_db_query("INSERT INTO " . TABLE_ORDERS_STATUS_HISTORY . " (orders_id, orders_status_id, date_added, customer_notified, comments, comments_type, changed_by) VALUES ('" . (int) $vipOrderObj->order_detail['orders_id'] . "', 0, now(), '0', '" . tep_db_input($assign_order_str) . "', 1, '" . $customer_email_address . "')");
                                        }
                                    }
                                }

                                if (tep_not_null($char_name)) {
                                    $sql_data_array1 = array('orders_products_id' => (int) $order_products_id,
                                        'orders_products_extra_info_key' => 'char_name',
                                        'orders_products_extra_info_value' => $char_name);
                                    tep_db_perform(TABLE_ORDERS_PRODUCTS_EXTRA_INFO, $sql_data_array1);
                                }

                                if (tep_not_null($account_name)) {
                                    $sql_data_array2 = array('orders_products_id' => (int) $order_products_id,
                                        'orders_products_extra_info_key' => 'char_account_name',
                                        'orders_products_extra_info_value' => $account_name);
                                    tep_db_perform(TABLE_ORDERS_PRODUCTS_EXTRA_INFO, $sql_data_array2);
                                }

                                if (tep_not_null($account_pwd)) {
                                    $sql_data_array3 = array('orders_products_id' => (int) $order_products_id,
                                        'orders_products_extra_info_key' => 'char_account_pwd',
                                        'orders_products_extra_info_value' => $account_pwd);
                                    tep_db_perform(TABLE_ORDERS_PRODUCTS_EXTRA_INFO, $sql_data_array3);
                                }

                                if (tep_not_null($char_wow_account)) {
                                    $sql_data_array4 = array('orders_products_id' => (int) $order_products_id,
                                        'orders_products_extra_info_key' => 'char_wow_account',
                                        'orders_products_extra_info_value' => $char_wow_account);
                                    tep_db_perform(TABLE_ORDERS_PRODUCTS_EXTRA_INFO, $sql_data_array4);
                                }

                                break;
                            case '3':
                                if ($order_status == '2') {
                                    // Open Buyback
                                    $update_purchase_eta_data_sql = array('orders_products_purchase_eta' => 'NULL');
                                    tep_db_perform(TABLE_ORDERS_PRODUCTS, $update_purchase_eta_data_sql, 'update', ' orders_products_id = "' . (int) $child_order_product_id . '" AND orders_products_purchase_eta = "-999"');
                                    tep_db_perform(TABLE_ORDERS_PRODUCTS, $update_purchase_eta_data_sql, 'update', ' orders_products_id = "' . (int) $order_products_id . '" AND orders_products_purchase_eta = "-999"');

                                    $vipOrderObj = new vip_order($child_order_product_id);
                                    $vipOrderObj->get_orders_details();
                                    if ($vipOrderObj->check_vip_mode($vipOrderObj->order_detail['buyback_categories_id'])) {
                                        $vipOrderObj->get_available_supplier();
                                        $assign_order_item = array();
                                        $assign_order_item[] = "&raquo; " . $vipOrderObj->order_detail['products_name'];

                                        if (count($vipOrderObj->assigned_supplier)) {
                                            $customer_email_address = tep_get_customers_email($_SESSION['customer_id']);
                                            $assign_order_str = "The following items have been assign to supplier:" . "\n" . implode("\n", $assign_order_item) . "\n" . implode("\n", $vipOrderObj->assigned_supplier);
                                            tep_db_query("INSERT INTO " . TABLE_ORDERS_STATUS_HISTORY . " (orders_id, orders_status_id, date_added, customer_notified, comments, comments_type, changed_by) VALUES ('" . (int) $vipOrderObj->order_detail['orders_id'] . "', 0, now(), '0', '" . tep_db_input($assign_order_str) . "', 1, '" . $customer_email_address . "')");
                                        }
                                    }
                                }

                                $sql_data_array = array('orders_products_id' => (int) $order_products_id,
                                    'orders_products_extra_info_key' => 'char_name',
                                    'orders_products_extra_info_value' => $char_name);

                                tep_db_perform(TABLE_ORDERS_PRODUCTS_EXTRA_INFO, $sql_data_array);
                                break;
                            case '4':
                                break;
                            default:
                                break;
                        }
                    } else if ($buyback_order_process_num > 0) {
                        echo '<error_msg><![CDATA[' . ERROR_ORDER_IN_TRADING . ']]></error_msg>';
                    }
                }

                echo "<url><![CDATA[" . tep_href_link(FILENAME_ACCOUNT_HISTORY_INFO, 'orders_type=current&order_id=' . $orders_id) . "]]></url>";
            }
            break;
        case 'get_product_info':
            $info_display = '';

            $product_info_select_sql = "SELECT o.customers_id, op.products_id, op.products_name, op.products_quantity, op.custom_products_type_id, op.products_categories_id, p2c.categories_id 
										FROM " . TABLE_ORDERS . " AS o 
										INNER JOIN " . TABLE_ORDERS_PRODUCTS . " AS op 
											ON (o.orders_id = op.orders_id) 
										INNER JOIN " . TABLE_PRODUCTS_TO_CATEGORIES . " AS p2c 
											ON (op.products_id = p2c.products_id)
										WHERE op.orders_products_id = '" . (int) $order_products_id . "'";
            $product_info_result_sql = tep_db_query($product_info_select_sql);

            if ($product_info_row = tep_db_fetch_array($product_info_result_sql)) {
                if ($product_info_row['customers_id'] == $_SESSION['customer_id']) {
                    $product_name = tep_get_products_name($product_info_row['products_id'], $languages_id);

                    $parent_categories_array = array();
                    $parent_categories_array[] = $product_info_row['products_categories_id'];
                    tep_get_parent_categories($parent_categories_array, $product_info_row['products_categories_id']);

                    foreach ($parent_categories_array as $key => $categories_id) {
                        $categories_name_select_sql = "	SELECT cd.categories_name 
														FROM " . TABLE_CATEGORIES . " AS c 
														INNER JOIN " . TABLE_CATEGORIES_DESCRIPTION . " AS cd 
															ON (c.categories_id = cd.categories_id AND cd.language_id = '" . (int) $languages_id . "') 
														WHERE c.categories_id = '" . (int) $categories_id . "'  
															AND c.custom_products_type_id = '999' 
															AND c.categories_status = 1";
                        $categories_name_result_sql = tep_db_query($categories_name_select_sql);
                        if ($categories_name_row = tep_db_fetch_array($categories_name_result_sql)) {
                            if (tep_not_null($info_display)) {
                                $info_display = $categories_name_row['categories_name'] . ' - ' . $info_display;
                            } else {
                                $info_display = $categories_name_row['categories_name'];
                            }
                        }
                    }

                    $cat_setting_array = tep_get_cat_setting($product_info_row['categories_id'], 'catalog', 'cs_delivery_option');
                    $extra_info_array = tep_draw_products_extra_info($order_products_id);

                    $delivery_array = explode(",", $cat_setting_array['cs_delivery_option']);

                    $delivery_html .= '<table border="0" cellspacing="0" cellpadding="0"><tr>';

                    $delivery_cnt = 0;
                    $delivery_method = '';
                    $last_delivery = '';

                    if (count($delivery_array) > 0) {
                        $last_delivery = end($delivery_array);
                        foreach ($delivery_array as $delivery_mode) {
                            switch ($delivery_mode) {
                                case '1':
                                    if (tep_not_null($extra_info_array['delivery_mode'])) {
                                        if ($delivery_mode == $extra_info_array['delivery_mode']) {
                                            $delivery_method = $delivery_mode;
                                            $delivery_html .= '<td class="smallText">' . tep_draw_radio_field("extra_info[delivery_mode]", '1', true, 'id ="delivery_mode_1" class="deliveryInfo" onClick="showDeliveryInfo(1)" style="background:#ffffff; color:#000000;"') . '<label for="delivery_mode_f2f">' . OPTION_FACE_TO_FACE . '</label></td>';
                                        } else {
                                            $delivery_html .= '<td class="smallText">' . tep_draw_radio_field("extra_info[delivery_mode]", '1', false, 'id ="delivery_mode_1" class="deliveryInfo" onClick="showDeliveryInfo(1)" style="background:#ffffff; color:#000000;"') . '<label for="delivery_mode_f2f">' . OPTION_FACE_TO_FACE . '</label></td>';
                                        }
                                    } else {
                                        if ($delivery_cnt == 0) {
                                            $delivery_method = $delivery_mode;
                                            $delivery_html .= '<td class="smallText">' . tep_draw_radio_field("extra_info[delivery_mode]", '1', true, 'id ="delivery_mode_1" class="deliveryInfo" onClick="showDeliveryInfo(1)" style="background:#ffffff; color:#000000;"') . '<label for="delivery_mode_f2f">' . OPTION_FACE_TO_FACE . '</label></td>';
                                            $delivery_cnt++;
                                        } else {
                                            $delivery_html .= '<td class="smallText">' . tep_draw_radio_field("extra_info[delivery_mode]", '1', false, 'id ="delivery_mode_1" class="deliveryInfo" onClick="showDeliveryInfo(1)" style="background:#ffffff; color:#000000;"') . '<label for="delivery_mode_f2f">' . OPTION_FACE_TO_FACE . '</label></td>';
                                        }
                                    }

                                    break;
                                case '2':
                                    if (tep_not_null($extra_info_array['delivery_mode'])) {
                                        if ($delivery_mode == $extra_info_array['delivery_mode']) {
                                            $delivery_method = $delivery_mode;
                                            $delivery_html .= '<td class="smallText">' . tep_draw_radio_field("extra_info[delivery_mode]", '2', true, 'id ="delivery_mode_2" class="deliveryInfo" onClick="showDeliveryInfo(2)" style="background:#ffffff; color:#000000;"') . '<label for="delivery_mode_guya">' . OPTION_PUT_IN_MY_ACCOUNT . '</label></td>';
                                        } else {
                                            $delivery_html .= '<td class="smallText">' . tep_draw_radio_field("extra_info[delivery_mode]", '2', false, 'id ="delivery_mode_2" class="deliveryInfo" onClick="showDeliveryInfo(2)" style="background:#ffffff; color:#000000;"') . '<label for="delivery_mode_guya">' . OPTION_PUT_IN_MY_ACCOUNT . '</label></td>';
                                        }
                                    } else {
                                        if ($delivery_cnt == 0) {
                                            $delivery_method = $delivery_mode;
                                            $delivery_html .= '<td class="smallText">' . tep_draw_radio_field("extra_info[delivery_mode]", '2', true, 'id ="delivery_mode_2" class="deliveryInfo" onClick="showDeliveryInfo(2)" style="background:#ffffff; color:#000000;"') . '<label for="delivery_mode_guya">' . OPTION_PUT_IN_MY_ACCOUNT . '</label></td>';
                                            $delivery_cnt++;
                                        } else {
                                            $delivery_html .= '<td class="smallText">' . tep_draw_radio_field("extra_info[delivery_mode]", '2', false, 'id ="delivery_mode_2" class="deliveryInfo" onClick="showDeliveryInfo(2)" style="background:#ffffff; color:#000000;"') . '<label for="delivery_mode_guya">' . OPTION_PUT_IN_MY_ACCOUNT . '</label></td>';
                                        }
                                    }

                                    break;
                                case '3':
                                    if (tep_not_null($extra_info_array['delivery_mode'])) {
                                        if ($delivery_mode == $extra_info_array['delivery_mode']) {
                                            $delivery_method = $delivery_mode;
                                            $delivery_html .= '<td class="smallText">' . tep_draw_radio_field("extra_info[delivery_mode]", '3', true, 'id ="delivery_mode_3" class="deliveryInfo" onClick="showDeliveryInfo(3)" style="background:#ffffff; color:#000000;"') . '<label for="delivery_mode_mail">' . OPTION_BY_MAIL . '</label></td>';
                                        } else {
                                            $delivery_html .= '<td class="smallText">' . tep_draw_radio_field("extra_info[delivery_mode]", '3', false, 'id ="delivery_mode_3" class="deliveryInfo" onClick="showDeliveryInfo(3)" style="background:#ffffff; color:#000000;"') . '<label for="delivery_mode_mail">' . OPTION_BY_MAIL . '</label></td>';
                                        }
                                    } else {
                                        if ($delivery_cnt == 0) {
                                            $delivery_method = $delivery_mode;
                                            $delivery_html .= '<td class="smallText">' . tep_draw_radio_field("extra_info[delivery_mode]", '3', true, 'id ="delivery_mode_3" class="deliveryInfo" onClick="showDeliveryInfo(3)" style="background:#ffffff; color:#000000;"') . '<label for="delivery_mode_mail">' . OPTION_BY_MAIL . '</label></td>';
                                            $delivery_cnt++;
                                        } else {
                                            $delivery_html .= '<td class="smallText">' . tep_draw_radio_field("extra_info[delivery_mode]", '3', false, 'id ="delivery_mode_3" class="deliveryInfo" onClick="showDeliveryInfo(3)" style="background:#ffffff; color:#000000;"') . '<label for="delivery_mode_mail">' . OPTION_BY_MAIL . '</label></td>';
                                        }
                                    }

                                    break;
                                case '4':
                                    if (tep_not_null($extra_info_array['delivery_mode'])) {
                                        if ($delivery_mode == $extra_info_array['delivery_mode']) {
                                            $delivery_method = $delivery_mode;
                                            $delivery_html .= '<td class="smallText">' . tep_draw_radio_field("extra_info[delivery_mode]", '4', true, 'id ="delivery_mode_4" class="deliveryInfo" onClick="showDeliveryInfo(4)" style="background:#ffffff; color:#000000;"') . '<label for="delivery_mode_open_store">' . OPTION_OPEN_STORE . '</label></td>';
                                        } else {
                                            $delivery_html .= '<td class="smallText">' . tep_draw_radio_field("extra_info[delivery_mode]", '4', false, 'id ="delivery_mode_4" class="deliveryInfo" onClick="showDeliveryInfo(4)" style="background:#ffffff; color:#000000;"') . '<label for="delivery_mode_open_store">' . OPTION_OPEN_STORE . '</label></td>';
                                        }
                                    } else {
                                        if ($delivery_cnt == 0) {
                                            $delivery_method = $delivery_mode;
                                            $delivery_html .= '<td class="smallText">' . tep_draw_radio_field("extra_info[delivery_mode]", '4', true, 'id ="delivery_mode_4" class="deliveryInfo" onClick="showDeliveryInfo(4)" style="background:#ffffff; color:#000000;"') . '<label for="delivery_mode_open_store">' . OPTION_OPEN_STORE . '</label></td>';
                                            $delivery_cnt++;
                                        } else {
                                            $delivery_html .= '<td class="smallText">' . tep_draw_radio_field("extra_info[delivery_mode]", '4', false, 'id ="delivery_mode_4" class="deliveryInfo" onClick="showDeliveryInfo(4)" style="background:#ffffff; color:#000000;"') . '<label for="delivery_mode_open_store">' . OPTION_OPEN_STORE . '</label></td>';
                                        }
                                    }
                                    break;
                            }

                            if ($last_delivery == $delivery_mode) {
                                if (!tep_not_null($delivery_method)) {
                                    $delivery_method = $delivery_mode;
                                }
                            }
                        }
                    }

                    $delivery_html .= '</tr></table>';

//					$customer_security_question = $customers_security_obj->show_customer_security_question_html('inputLabelNew','inputBoxContentsNew');

                    $count_char = strlen($extra_info_array['char_account_pwd']);
                    $mask_password = str_repeat("*", $count_char);

                    echo "<products_id><![CDATA[" . $product_info_row['products_id'] . "]]></products_id>";
                    echo "<products_name><![CDATA[" . $product_name . "]]></products_name>";
                    echo "<custom_product><![CDATA[" . $product_info_row['custom_products_type_id'] . "]]></custom_product>";
                    echo "<products_quantity><![CDATA[" . $product_info_row['products_quantity'] . "]]></products_quantity>";
                    echo "<char_name><![CDATA[" . htmlspecialchars($extra_info_array['char_name']) . "]]></char_name>";
                    echo "<account_name><![CDATA[" . htmlspecialchars($extra_info_array['char_account_name']) . "]]></account_name>";
                    echo "<account_pwd><![CDATA[" . $mask_password . "]]></account_pwd>";
                    echo "<char_wow_account><![CDATA[" . htmlspecialchars($extra_info_array['char_wow_account']) . "]]></char_wow_account>";
                    echo "<products_cat><![CDATA[" . $info_display . "]]></products_cat>";
//					echo "<customer_security_question><![CDATA[".$customer_security_question."]]></customer_security_question>";
                    echo "<delivery_html><![CDATA[" . $delivery_html . "]]></delivery_html>";
                    echo "<delivery_mode><![CDATA[" . $delivery_method . "]]></delivery_mode>";
                }
            }

            break;

        case "myaccount_verify_phone":
            require(DIR_WS_LANGUAGES . $language . '/' . FILENAME_ACCOUNT_ACTIVATE);

            if ((int) $order_id > 0) {
                require_once(DIR_WS_CLASSES . 'order.php');
                $order = new order($order_id);
                $customer_complete_phone_info_array['country_id'] = '';
                $customer_complete_phone_info_array['country_international_dialing_code'] = $order->customer['order_country_code'];
                $customer_complete_phone_info_array['telephone_number'] = tep_parse_telephone($order->customer['telephone'], $order->customer['order_country_code'], 'code');
                $complete_telephone_number = $order->customer['order_country_code'] . $customer_complete_phone_info_array['telephone_number'];
            } else {

                $customer_complete_phone_info_array = tep_format_telephone($customer_id);
                $complete_telephone_number = $customer_complete_phone_info_array['country_international_dialing_code'] . $customer_complete_phone_info_array['telephone_number'];
            }

            if (tep_is_mobile_num_exist($customer_complete_phone_info_array['telephone_number'], $customer_complete_phone_info_array['country_id'], $customer_id, $customer_complete_phone_info_array['country_international_dialing_code'])) {
                echo '<error><![CDATA[' . ENTRY_CONTACT_NUMBER_EXIST_ERROR . ']]></error>';
            } else {
                $is_verified = tep_info_verified_check($customer_id, $complete_telephone_number, 'telephone');

                if ($is_verified != 1) { // If not yet verified
                    $request_status = FALSE;
                    $phone_verification_obj = new phone_verification();

                    if (tep_is_x_sharing_mobile_n_verified($customer_id, $customer_complete_phone_info_array['telephone_number'], $customer_complete_phone_info_array['country_international_dialing_code'], 'code', FALSE) !== 0) {
                        $code_generated = tep_four_digit_code_generate();
                        $is_verified = tep_info_verified_check($customer_id, $complete_telephone_number, 'telephone', TRUE);

                        if ($is_verified === FALSE) {
                            $sql_data_array = array('customers_id' => $customer_id,
                                'customers_info_value' => $complete_telephone_number,
                                'serial_number' => $code_generated,
                                'verify_try_turns' => 1,
                                'info_verified' => 0,
                                'info_verification_type' => 'telephone');
                            tep_db_perform(TABLE_CUSTOMERS_INFO_VERIFICATION, $sql_data_array);
                        } else {
                            $update_verify_try_turns = "UPDATE " . TABLE_CUSTOMERS_INFO_VERIFICATION . " 
                                                        SET verify_try_turns = 1,
                                                            serial_number = '" . $code_generated . "'
                                                        WHERE customers_id = '" . $customer_id . "' 
                                                            AND customers_info_value = '" . $complete_telephone_number . "' 
                                                            AND info_verification_type ='telephone'";
                            tep_db_query($update_verify_try_turns);
                        }

                        //$request_status = $phone_verification_obj->request_sms($customer_id, $customer_complete_phone_info_array['telephone_number'], $customer_complete_phone_info_array['country_international_dialing_code'], $code_generated, $call_language);
                        $request_status = $phone_verification_obj->request_sms_v2($customer_complete_phone_info_array, $code_generated);
                    } else {
                        $request_status = -1;
                    }

                    if ($request_status === TRUE) {
                        echo '<verify_code_table><![CDATA[
                                <table border="0" cellpadding="0" cellspacing="0" width="100%">
                                    <tr>
                                        <td class="inputLabel" width="20%" align="left">' . ENTRY_VERIFICATION_CODE . '</td>
                                        <td class="inputField" width="20%" align="left">' .
                        tep_draw_input_field('code', '', 'size="15" maxlength="4"') . '&nbsp;<span class="requiredInfo">*</span>
                                        </td>
                                        <td class="inputButton" width="15%" align="left">';
                        if ((int) $order_id > 0) {
                            echo tep_image_button2('gray_short', 'javascript:void(0)', BUTTON_CONFIRM, '', ' id="button_confirm_code" name="NonJSUpdate" style="float:left;" onclick="verifyTelephone(document.phone_verify_form.code.value);"');
                        } else {
                            echo tep_div_button(1, '&nbsp; ' . BUTTON_CONFIRM . ' &nbsp;', 'phone_verify_form', 'id="button_confirm_code" name="NonJSUpdate" onclick="javascript:verifyTelephone(document.phone_verify_form.code.value);"', 'green_button', false, 'void(0)');
                        }
                        echo '</td>
                                        <td>&nbsp;</td><!-- to absorb all the empty space -->
                                    </tr>
                                </table>
                            ]]></verify_code_table>';
                    } else if ($request_status === -1) {
                        echo '<error><![CDATA[' . ENTRY_CONTACT_NUMBER_EXIST_ERROR . ']]></error>';
                    } else {
                        echo '<error><![CDATA[' . REQUEST_REACHED_MAX_ATTEMPT_ERROR . ']]></error>';
                    }

                    unset($phone_verification_obj);
                }
            }
            break;

        case "myaccount_confirm_telephone":
            require_once(DIR_WS_LANGUAGES . $language . '/' . FILENAME_ACCOUNT_ACTIVATE);

            if ((int) $order_id > 0) {
                require_once(DIR_WS_CLASSES . 'order.php');
                $order = new order($order_id);
                $customer_complete_phone_info_array['country_id'] = '';
                $customer_complete_phone_info_array['country_international_dialing_code'] = $order->customer['order_country_code'];
                $customer_complete_phone_info_array['telephone_number'] = $order->customer['telephone'];
                $complete_telephone_number = $order->customer['order_country_code'] . $order->customer['telephone'];
            } else {
                $customer_complete_phone_info_array = tep_format_telephone($customer_id);
                $complete_telephone_number = $customer_complete_phone_info_array['country_international_dialing_code'] . $customer_complete_phone_info_array['telephone_number'];
            }

            if (tep_is_mobile_num_exist($customer_complete_phone_info_array['telephone_number'], $customer_complete_phone_info_array['country_id'], $customer_id, $customer_complete_phone_info_array['country_international_dialing_code'])) {
                echo '<result>' . ENTRY_CONTACT_NUMBER_EXIST_ERROR . '</result>' .
                '<goto_url><![CDATA[' . tep_href_link(FILENAME_ACCOUNT_ACTIVATE, 'verify=phone', SSL) . ']]></goto_url>';
            } else if (tep_confirming_code($customer_id, $code_received, $complete_telephone_number) == true) {
                tep_set_info_verified($customer_id, $complete_telephone_number);

                echo '<result>' . CODE_MATCH_MESSAGE . '</result>';
                if ($page == 'account') {
                    echo '<goto_url><![CDATA[' . tep_href_link(FILENAME_ACCOUNT, '', SSL) . ']]></goto_url>';
                } else {
                    echo '<goto_url><![CDATA[' . tep_href_link(FILENAME_CHECKOUT_SUCCESS, '', SSL) . ']]></goto_url>';
                }
            } else {
                $try_turns = tep_try_turns($customer_id, $complete_telephone_number);
                echo '<result>' . CODE_NOT_MATCH_RETRY_MESSAGE . '</result>';
                if ($try_turns < MAX_CODE_VERIFICATION) {
                    $try_turns++;
                    tep_set_try_turns($customer_id, $try_turns, $complete_telephone_number);
                } else {
                    if ($page == 'account') {
                        echo '<goto_url><![CDATA[' . tep_href_link(FILENAME_ACCOUNT_ACTIVATE, 'verify=phone', SSL) . ']]></goto_url>';
                    } else {
                        echo '<goto_url><![CDATA[' . tep_href_link(FILENAME_CHECKOUT_SUCCESS, '', SSL) . ']]></goto_url>';
                    }
                }
            }
            break;

        case "state_list":
            $zones_select_sql = "SELECT zone_id, zone_name FROM " . TABLE_ZONES . " WHERE zone_country_id = '" . (int) $country_id . "' ORDER BY zone_name";
            $zones_result_sql = tep_db_query($zones_select_sql);
            echo "<selection>";
            while ($zones_row = tep_db_fetch_array($zones_result_sql)) {
                echo "<option index='" . $zones_row['zone_id'] . "'><![CDATA[" . $zones_row['zone_name'] . "]]></option>";
            }
            echo "</selection>";

            break;
        case "refresh_country_code":
            $country_dialing_select_sql = "select countries_international_dialing_code from " . TABLE_COUNTRIES . " where countries_id ='" . (int) $country_id . "'";
            $country_dialing_result_sql = tep_db_query($country_dialing_select_sql);
            $country_dialing_row = tep_db_fetch_array($country_dialing_result_sql);
            echo "<country_dialing_code>";
            echo $country_dialing_row['countries_international_dialing_code'];
            echo "</country_dialing_code>";

            break;

        case "confirm_telephone":
//	    	require_once(DIR_WS_LANGUAGES . $language . '/' . FILENAME_CHECKOUT_SUCCESS);
//	    	
//    		$customer_complete_phone_info_array = tep_format_telephone($customer_id);
//    		$complete_telephone_number = $customer_complete_phone_info_array['country_international_dialing_code'] . $customer_complete_phone_info_array['telephone_number'];
//			$complete_telephone_number = preg_replace('/[^\d]/', '', $complete_telephone_number);
//			
//			if (tep_confirming_code($customer_id, $code_received) == true) {
//				tep_set_info_verified($customer_id);
//				echo '<result>'.CODE_MATCH_MESSAGE.'</result>';
//				echo '<goto_url><![CDATA[';
//				echo tep_href_link(FILENAME_DEFAULT);
//				echo ']]></goto_url>';
//			} else {
//				$try_turns = tep_try_turns($customer_id);
//				if ($try_turns < MAX_CODE_VERIFICATION) {
//					$try_turns++;
//					tep_set_try_turns($customer_id, $try_turns);
//					echo '<result>'.CODE_NOT_MATCH_RETRY_MESSAGE.'</result>';
//				} else {
//					echo '<result>'.CODE_NOT_MATCH_MESSAGE.'</result>';
//					echo '<goto_url><![CDATA[';
//					echo tep_href_link(FILENAME_DEFAULT);
//					echo ']]></goto_url>';
//				}
//			}

            break;
        case 'verify_email':
            $verify_result = 0;

            $info_verified_select_sql = "	SELECT civ.customers_id, civ.info_verified, civ.serial_number, civ.customers_info_value 
											FROM " . TABLE_CUSTOMERS . " AS c 
											INNER JOIN " . TABLE_CUSTOMERS_INFO_VERIFICATION . " AS civ 
												ON (c.customers_email_address = civ.customers_info_value AND c.customers_id = civ.customers_id AND civ.info_verification_type = 'email') 
											WHERE c.customers_id = '" . tep_db_input($customer_id) . "'";
            $info_verified_result_sql = tep_db_query($info_verified_select_sql);
            $info_verified_row = tep_db_fetch_array($info_verified_result_sql);

            if ($info_verified_row['info_verified'] == 1) {
                $verify_result = 1;
            } else {
                if ($code_received == $info_verified_row['serial_number']) {
                    $verify_result = 1;

                    $sql_data_array = array('info_verified' => 1,
                        'serial_number' => '');

                    tep_db_perform(TABLE_CUSTOMERS_INFO_VERIFICATION, $sql_data_array, 'update', "customers_id = '" . (int) $customer_id . "' AND customers_info_value = '" . tep_db_input($info_verified_row['customers_info_value']) . "'");

                    tep_order_notify(7, $customer_id, $info_verified_row['customers_info_value']);
                }
            }

            echo '<verify>' . $verify_result . '</verify>';
            echo '<email_address><![CDATA[' . $info_verified_row['customers_info_value'] . ']]></email_address>';

            break;
        case 'get_buyback_server_list':
            $has_server = false;

            $xml_str = "<servers>\n";
            $xml_str .= "<server><id>0</id><name>-- Select Your Server--</name></server>\n";
            if ($buyback_cat_id > 0) {
                /*                 * ****************************************************************************
                  Cannot only get direct child instead look for ALL subcategories(all levels)
                  of this main category which has PRODUCTS
                 * **************************************************************************** */
                $subcat_array = array($buyback_cat_id);
                tep_get_subcategories($subcat_array, $buyback_cat_id);

                $categories_select_sql = "	SELECT p2c.categories_id, cd.categories_name, p.products_cat_path, pd.products_name 
		                					FROM " . TABLE_PRODUCTS_TO_CATEGORIES . " AS p2c 
		                					INNER JOIN " . TABLE_CATEGORIES . " AS c 
		                						ON (p2c.categories_id = c.categories_id AND c.categories_status=1) 
		                					INNER JOIN " . TABLE_PRODUCTS . " AS p 
		                						ON (p2c.products_id = p.products_id) 
		                					INNER JOIN " . TABLE_PRODUCTS_DESCRIPTION . " AS pd 
		                						ON (p.products_id = pd.products_id AND pd.language_id = '" . (int) $language_id . "') 
		                					INNER JOIN " . TABLE_BUYBACK_PRODUCTS . " AS bp 
    											ON p2c.products_id=bp.products_id 
		                					INNER JOIN " . TABLE_CATEGORIES_DESCRIPTION . " AS cd 
		                						ON (p2c.categories_id = cd.categories_id AND cd.language_id = '" . (int) $language_id . "') 
		                					WHERE p2c.categories_id IN ('" . implode("', '", $subcat_array) . "') 
		                						AND p.custom_products_type_id=0 
		                						AND p.products_bundle='' 
		  										AND p.products_bundle_dynamic='' 
		                					ORDER BY p.products_cat_path, p.products_sort_order";
                $categories_result_sql = tep_db_query($categories_select_sql);

                while ($categories_row = tep_db_fetch_array($categories_result_sql)) {
                    $has_server = true;
                    $product_display_name = tep_display_category_path($categories_row['products_cat_path'] . " > " . $categories_row['products_name'], $categories_row['categories_id'], 'catalog', false);

                    $xml_str .= "<server><id><![CDATA[" . $categories_row['categories_id'] . "]]></id><name><![CDATA[" . strip_tags($product_display_name) . "]]></name></server>\n";
                }
            }
            $xml_str .= "</servers>\n";
            if ($has_server) {
                $xml_str .= "<error>0</error>";
            } else {
                $xml_str .= "<error>3</error>";
            }
            echo $xml_str;
            break;
        case 'get_buyback_product_info':
            $xml_str = '';
            $showError = false;
            $min_value = 0;
            $max_value = 0;
            $product_name = '&nbsp;';
            $product_id = '&nbsp;';

            $cat_cfg_array = tep_get_cfg_setting($buyback_cat_id, 'category');
            //Get Unit of measurement (ie. Million)
            $buyback_qty_unit = $cat_cfg_array['BUYBACK_QUANTITY_UNIT'];

            $product_select_sql = "	SELECT p.products_id, pd.products_name, p.products_quantity, p.products_actual_quantity 
    								FROM " . TABLE_PRODUCTS_TO_CATEGORIES . " AS p2c
    								INNER JOIN " . TABLE_PRODUCTS . " AS p
    									ON p2c.products_id=p.products_id 
    								INNER JOIN " . TABLE_BUYBACK_PRODUCTS . " AS bp 
    									ON p2c.products_id=bp.products_id 
    								INNER JOIN " . TABLE_PRODUCTS_DESCRIPTION . " AS pd 
    									ON p2c.products_id=pd.products_id 
    								WHERE p2c.categories_id='" . $buyback_cat_id . "' 
    									AND p.products_bundle='' 
    									AND p.products_bundle_dynamic='' 
    									AND pd.language_id = '" . (int) $language_id . "' 
    								ORDER BY p.products_sort_order, pd.products_name";
            $product_result_sql = tep_db_query($product_select_sql);
            //We assume there is only one single product in this cat.
            if ($product_row = tep_db_fetch_array($product_result_sql)) {
                $product_id = $product_row['products_id'];
                $product_name = $product_row['products_name'];

                $buybackProductObj = new buyback_product($product_id);
                $buybackProductObj->calculate();
                if (!$buybackProductObj->is_buyback) {
                    $showError = true;
                }
            } else {
                $showError = true;
            }

            if ($showError) {
                $xml_str .= "<error>3</error>";
                $xml_str .= "<notice>0</notice>";
            } else {
                $xml_str = "<error>0</error>";
                $xml_str .= "<notice>1</notice>";
                $xml_str .= "<product_id><![CDATA[" . $product_id . "]]></product_id>";
                $xml_str .= "<product_name><![CDATA[" . $product_name . "]]></product_name>";
                $xml_str .= "<min><![CDATA[" . $buybackProductObj->min_buyback_qty . "]]></min>";
                $xml_str .= "<max><![CDATA[" . $buybackProductObj->max_buyback_qty . "]]></max>";
                $xml_str .= "<uom><![CDATA[" . $buyback_qty_unit . "]]></uom>";
            }
            echo $xml_str;
            unset($buybackProductObj);
            break;
        case 'get_buyback_price':
            $xml_str = '';
            if ($buyback_products_id) {
                $unit_price = tep_get_buyback_price($buyback_products_id);
                $total_price = number_format(($buyback_qty * $unit_price), 6, '.', '');
                $xml_str .= "<price><![CDATA[" . $total_price . "]]></price>";
                $xml_str .= "<unit_price><![CDATA[" . $unit_price . "]]></unit_price>";
                unset($buybackProductObj);
            }
            echo $xml_str;
            break;
        case 'rate_services':
            $success_update = false;
            $select_order_id_sql = "SELECT br.buyback_request_id, brg.customers_id 
									FROM " . TABLE_BUYBACK_REQUEST . " AS br 
									INNER JOIN " . TABLE_BUYBACK_REQUEST_GROUP . " AS brg
										ON(br.buyback_request_group_id = brg.buyback_request_group_id)
									WHERE orders_id='" . tep_db_input($customer_order_id) . "' 
										AND br.customers_rating IS NULL
										AND brg.buyback_status_id='3'";
            $select_order_id_result = tep_db_query($select_order_id_sql);
            while ($select_order_id_row = tep_db_fetch_array($select_order_id_result)) {
                $update_array = array('customers_rating' => $customer_rate);
                $success_update = tep_db_perform(TABLE_BUYBACK_REQUEST, $update_array, 'update', " buyback_request_id='" . $select_order_id_row['buyback_request_id'] . "'");
                //update supplier point.
                $vipGroupsObj = new vip_groups($select_order_id_row['customers_id']);
                $vipGroupsObj->calculate_customers_rating($customer_rate);
            }
            if ($success_update) {
                echo "<result><success>" . $customer_rate . "</success></result>";
            } else {
                echo "<result><success>0</success></result>";
            }

            break;
        case 'get_zone_country':
            require_once(DIR_WS_CLASSES . 'ogm_xml_to_ary.php');

            $xml_array_obj = new ogm_xml_to_ary(realpath('cache/' . FILENAME_COUNTRY_LIST_XML));
            $xml_array = $xml_array_obj->get_ogm_xml_to_ary();
            $zone_country_xml = '';
            if (count($xml_array)) {
                foreach ($xml_array['countries']['_c']['country'] as $xml_countries_num => $xml_countries_data) {
                    $temp_name_escaped = tep_escape_filtering_id($xml_countries_data['_c']['countries_name']['_v']);
                    $zone_country_xml .= '<div id="zone_country_' . $temp_name_escaped . '" onclick="javascript:set_localization_value(\'loc_country\' , \'' . $xml_countries_data['_c']['countries_id']['_v'] . '\' , \'hide_zones()\')" class="select_zones flag ' . $xml_countries_data['_c']['countries_iso_code_2']['_v'] . '">';
                    $zone_country_xml .= '<div><!-- --></div>';
                    $zone_country_xml .= '' . tep_db_input($xml_countries_data['_c']['countries_name']['_v']);
                    $zone_country_xml .= '</div>';
                }
            }
            if (tep_not_null($zone_country_xml)) {
                echo '<zone_country_info><![CDATA[';
                echo htmlentities($zone_country_xml);
                echo ']]></zone_country_info>';
            } else {
                echo '<zone_country_info><![CDATA[]]></zone_country_info>';
            }

            unset($xml_array_obj);
            unset($xml_array);
            unset($zone_country_xml);

            break;
        case 'customers_testimonial':
            echo "	<testimonial><![CDATA[" . HTML_CUSTOMER_TESTIMONIAL_BOX . "]]></testimonial>";
            break;
        case 'refresh_exchange_rate':
            require(DIR_WS_LANGUAGES . $language . '/' . FILENAME_ACCOUNT_STORE_CREDIT);
            $error = false;

            if (!isset($currencies->currencies[$curr_code])) {
                $error = true;
            }

            $sc_object = new store_credit($customer_id);

            $current_store_credit_arr = $sc_object->get_current_credits_balance($customer_id);

            if (count($current_store_credit_arr) > 0) {
                $current_sc_currency = $currencies->get_code_by_id($current_store_credit_arr['sc_currency_id']);
                if (isset($currencies->currencies[$currencies->get_code_by_id($current_store_credit_arr['sc_currency_id'])])) {
                    $current_sc_amt = ($current_store_credit_arr['sc_reverse'] + $current_store_credit_arr['sc_irreverse']) - ($current_store_credit_arr['sc_reverse_reserve_amt'] + $current_store_credit_arr['sc_irreverse_reserve_amt']);
                    $current_sc_str = $currencies->currencies[$current_sc_currency]['symbol_left'] . number_format($current_sc_amt, $currencies->currencies[$current_sc_currency]['decimal_places'], $currencies->currencies[$current_sc_currency]['decimal_point'], '') . $currencies->currencies[$current_sc_currency]['symbol_right'];

                    $curr_code_title = $currencies->currencies[$curr_code]['title'];

                    $new_sc_amt = $sc_object->get_total_store_credit_in_new_currency($curr_code);
                    $new_sc_str = $currencies->currencies[$curr_code]['symbol_left'] . number_format($new_sc_amt, $currencies->currencies[$curr_code]['decimal_places'], $currencies->currencies[$curr_code]['decimal_point'], '') . $currencies->currencies[$curr_code]['symbol_right'];
                    $convert_rate = ($current_sc_amt > 0) ? tep_round(($new_sc_amt / $current_sc_amt), 5) : 0;
                } else {
                    $error = true;
                }
            } else {
                $current_sc_str = $curr_code_title = $new_sc_str = $convert_rate = $curr_code = $current_sc_currency = $new_sc_amt = '';
                $error = true;
            }

            if (!$error) {
                echo '<exchange_rate><![CDATA[';
                echo '<table border="0" cellpadding="0" cellspacing="0" width="100%">
						<tr>
							<td>' . sprintf(TEXT_CONVERT_MESSAGE, $current_sc_str, $curr_code_title, $new_sc_str, $convert_rate, $curr_code, $current_sc_currency) . '</td>
						</tr>
					  </table>';
                echo ']]></exchange_rate>';
            } else {
                echo '<error><![CDATA[' . ERROR_INVALID_STORE_CREDIT_CURRENCY . ']]></error>';
            }
            break;

        case 'cancel_order':
            include_once(DIR_WS_LANGUAGES . $language . '/' . FILENAME_ACCOUNT_HISTORY_INFO);
            include_once(DIR_WS_CLASSES . 'order.php');

            $order = new order($order_id);

            if ($order->info['orders_status_id'] == '1') { // allow "Pending" status to do direct cancel
                $content = '<table id="cancel_order_confirmation" border="0" width="465px" cellspacing="10" cellpadding="4">
						  		<tr>
						  			<td>
					    				<div style="font-size:16px; font-weight:bold;">' . HEADING_TITLE_CANCEL_ORDER . '</div>
					    				<div class="breakLine" style="height:10px;"><!-- --></div>
					    				<div class="dottedLine"><!-- --></div>
					    				<div class="breakLine" style="height:10px;"><!-- --></div>';

                // check if this order is belong to the customer
                if ($order->customer['id'] == $customer_id) {
                    $payment_methods_array = array();
                    if ($order->info['payment_methods_id'] != '0') {
                        include_once(DIR_WS_CLASSES . 'payment_methods.php');
                        $pm_obj = new payment_methods($order->info['payment_methods_id']);
                        $payment_method = $pm_obj->payment_methods_description_title;
                    }

                    // check from orders_total table if there is any store credit or voucher is used in this order
                    if (sizeof($order->totals) && is_array($order->totals)) {
                        for ($r = 0, $t = count($order->totals); $r < $t; $r++) {
                            $totals_array[$order->totals[$r]['class']] = array('title' => $order->totals[$r]['title'],
                                'text' => $order->totals[$r]['text'],
                                'value' => $order->totals[$r]['value']
                            );
                        }

                        $order_total_value = $totals_array['ot_subtotal']['text']; // Payment Amount
                        // Payment Methods
                        if (array_key_exists('ot_coupon', $totals_array)) {
                            $payment_methods_array[] = TEXT_DISCOUNT_COUPON;
                        }

                        if (array_key_exists('ot_gv', $totals_array)) {
                            $payment_methods_array[] = str_replace(':', '', TEXT_STORE_CREDIT);
                        }

                        if (array_key_exists('ot_total', $totals_array) && $totals_array['ot_total']['value'] > 0) {
                            $payment_methods_array[] = $payment_method; //'CashU';
                        }
                    }

                    $content .= '<table border="0" width="100%" cellspacing="0" cellpadding="2">
				          			<tr>
				          				<td width="50%">' . TITLE_CANCEL_ORDER_NUMBER . '</td>
				          				<td>' . $order_id . '</td>
				          			</tr>
				          			<tr>
				          				<td width="50%">' . TITLE_CANCEL_ORDER_PAYMENT_METHOD . '</td>
				          				<td>' . (sizeof($payment_methods_array) == 1 ? $payment_methods_array[0] : (sizeof($payment_methods_array) > 1 ? implode('</td></tr><tr><td>&nbsp;</td><td>', $payment_methods_array) : '')) . '</td>
				          			</tr>
				          			<tr>
				          				<td width="50%">' . TITLE_CANCEL_ORDER_AMOUNT . '</td>
				          				<td>' . $order_total_value . '</td>
				          			</tr>
				          			
					          		<tr><td colspan="2" height="40px">&nbsp;</td></tr>
					          		<tr><td colspan="2">' . MESSAGE_CANCEL_ORDER_CONFIRM . '</td></tr>
					          		
					          		<tr><td colspan="2" height="10px">&nbsp;</td></tr>
					          		<tr><td colspan="2" height="10px;"><div class="dottedLine"><!-- --></div></td></tr>
					          		
					          		<tr>
					          			<td colspan="2">
					          				<div style="height: 10px;"><!-- --></div>
					          				<table align="center" border="0" cellpadding="0" cellspacing="0">
					          					<tr>
					          						<td>
					          							<input type="button" name="cancel_order_no" class="cancelOrderBtn" style="font-weight: normal;" value="' . BUTTON_CANCEL_ORDER_NO . '" onClick="javascript: hideFancyBox();" />
								    				</td>
								    				<td width="15px">&nbsp;</td>
								    				<td>
								    					<input type="button" class="cancelOrderBtn" name="cancel_order_yes" value="' . BUTTON_CANCEL_ORDER_YES . '" onClick="javascript: cancel_order_confirm(' . $order_id . ');" />
								    				</td>
								    			</tr>
								    		</table>
					    				</td>
					    			</tr>
			  					</table>';
                } else {
                    $content .= '<table border="0" width="100%" cellspacing="0" cellpadding="2">
									<tr><td>' . MESSAGE_UNAUTHORISED_CANCEL_ORDER . '</td></tr>
									<tr><td style="height:10px;">&nbsp;</td></tr>
								</table>';
                }

                $content .= '		</td>
								</tr>
							</table>';

                echo "<detail><![CDATA[" . $content . "]]></detail>";
            } else {
                echo "<redirect>" . tep_href_link(FILENAME_ACCOUNT_HISTORY_INFO, 'order_id=' . $order_id) . "</redirect>";
            }

            break;

        case 'cancel_order_confirm':
            include_once(DIR_WS_LANGUAGES . $language . '/' . FILENAME_CANCEL_ORDER);
            include_once(DIR_WS_LANGUAGES . $language . '/' . FILENAME_ACCOUNT_HISTORY_INFO);
            include_once(DIR_WS_CLASSES . 'order.php');

            $content = '';
            $order = new order($order_id);
            $store_credit = false;
            $order_cancelled = false;

            if ($order->info['orders_status_id'] == '1') { // allow "Pending" status to do direct cancel
                // check if this order is belong to the customer
                if ($order->customer['id'] == $customer_id) {
                    // verify Order Cancellation Request
                    $order_cancelled_select_sql = "SELECT orders_id FROM " . TABLE_ORDERS_CANCEL_REQUEST . " WHERE orders_id = '" . (int) $order_id . "'";
                    $order_cancelled_result_sql = tep_db_query($order_cancelled_select_sql);
                    if (tep_db_num_rows($order_cancelled_result_sql) > 0) {
                        $order_cancelled = true;
                    } else {
                        tep_db_perform(TABLE_ORDERS_CANCEL_REQUEST, array('orders_id' => $order_id));
                    }

                    if (($order->info['payment_methods_parent_id'] > 0)) {
                        // verify Store Credit payment
                        for ($i = 0, $total = count($order->totals); $total > $i; $i++) {
                            if ($order->totals[$i]['class'] == 'ot_gv') {
                                $store_credit = true;
                                break;
                            }
                        }

                        if (($order_cancelled == false) && ($store_credit == false)) {
                            $orders_status_history_perform_array = array('action' => 'insert',
                                'data' => array('orders_id' => (int) $order_id,
                                    'orders_status_id' => 5,
                                    'date_added' => 'now()',
                                    'changed_by' => tep_get_customers_email($_SESSION['customer_id']),
                                    'customer_notified' => 0
                                )
                            );
                            $order->update_order_status(5, $orders_status_history_perform_array, false);
                        }
                    } else {
                        $store_credit = true;
                    }

                    $content = '<table id="cancel_order_confirm" border="0" width="465px" cellspacing="10" cellpadding="4">
							  		<tr>
							  			<td>
						    				<div style="font-size:16px;font-weight:bold;">' . HEADING_TITLE_CANCEL_ORDER . '</div>
						    				<div class="breakLine" style="height: 10px;"><!-- --></div>
						    				<div class="dottedLine"><!-- --></div>
						    				<div class="breakLine" style="height: 20px;"><!-- --></div>
						    				<div>' . ($store_credit == false ? MESSAGE_CANCEL_ORDER_CANCELLED_1 : MESSAGE_CANCEL_ORDER_CANCELLED_2) . '</div>
						    				
						  					<div class="breakLine" style="height:20px;"><!-- --></div>
						    				<div class="dottedLine"><!-- --></div>
						    				<div class="breakLine" style="height:10px;"><!-- --></div>
						    				
						    				<div style="width: 100px; margin: auto;">
						    					<table align="center" border="0" cellpadding="0" cellspacing="0">
						          					<tr>
						          						<td>
						          							<input type="button" class="cancelOrderBtn" name="cancel_order_ok" value="' . BUTTON_CANCEL_ORDER_OK . '" onClick="javascript: location.href = \'' . tep_href_link(FILENAME_ACCOUNT_HISTORY, '', 'SSL') . '\'" />
									    				</td>
									    			</tr>
									    		</table>
						    				</div>
										</td>
									</tr>
								</table>';
                } else {
                    $content .= '<table border="0" width="100%" cellspacing="0" cellpadding="2">
									<tr><td>' . MESSAGE_UNAUTHORISED_CANCEL_ORDER . '</td></tr>
									<tr><td style="height:10px;">&nbsp;</td></tr>
								</table>';
                }
                echo "<detail><![CDATA[" . $content . "]]></detail>";
            } else {
                echo "<redirect>" . tep_href_link(FILENAME_ACCOUNT_HISTORY_INFO, 'order_id=' . $order_id) . "</redirect>";
            }

            break;
        case 'login_pre_validate':
            require_once(DIR_WS_CLASSES . 'recaptcha.php');
            if (tep_not_null($_REQUEST['email'])) {
                $email_address = tep_db_prepare_input($_REQUEST['email']);
                $check_customer_select_sql = "	SELECT customers_id
												FROM " . TABLE_CUSTOMERS . " 
												WHERE customers_email_address = '" . tep_db_input($email_address) . "'";
                $check_customer_result_sql = tep_db_query($check_customer_select_sql);
                if (tep_db_num_rows($check_customer_result_sql)) {
                    if (recaptcha :: is_captcha_require($email_address, tep_get_ip_address())) {
                        echo '<status>Captcha_Needed</status>';
                    } else {
                        echo '<status>Proceed</status>';
                    }
                } else {
                    echo '<status>Wrong Email</status>';
                }
            } else {
                echo '<status>No_Email</status>';
            }
            break;
        case 'clear_mobile_cookie':
            if (isset($_COOKIE['is_mobile'])) {
                $expire = time() - 86400;
                tep_setcookie('is_mobile', '', $expire, $cookie_path, $cookie_domain);
            }
            echo 'success';
            break;
        case 'clear_splash':
            if (!isset($_COOKIE['ogm_se'])) {
                $expire = time() + 30 * 24 * 60 * 60;
                tep_setcookie('ogm_se', '1', $expire, $cookie_path, $cookie_domain);
            }
            echo 'success';
            break;
        case 'reset_password':
            if ((int) $customer_id) {
                require_once(DIR_WS_LANGUAGES . $language . '/' . FILENAME_ACCOUNT_EDIT);
                $error = FALSE;
                $reset_flag = (int) $customers_upkeep_obj->getUpkeepValue('reset_password');
                $status = 'Unknown request!';

                if ($reset_flag !== 2 && strlen($rsp_password) < ENTRY_PASSWORD_MIN_LENGTH) {
                    $error = TRUE;
                    $status = ENTRY_PASSWORD_CURRENT_ERROR;
                }

                // Password & Confirm Password error messages
                if (strlen($rsp_new_password) < ENTRY_PASSWORD_MIN_LENGTH) {
                    $error = TRUE;
                    $status = ENTRY_PASSWORD_NEW_ERROR;
                }

                if ($rsp_new_password != $rsp_cfm_password) {
                    $error = TRUE;
                    $status = ENTRY_PASSWORD_NEW_NOT_MATCH_ERROR;
                }

                if (!$error) {
                    if ($reset_flag) {
                        if ($reset_flag === 1) {
                            $check_customer_select_sql = "	SELECT customers_password
                                                            FROM " . TABLE_CUSTOMERS . " 
                                                            WHERE customers_id = '" . (int) $customer_id . "'";
                            $check_customer_result_sql = tep_db_query($check_customer_select_sql);
                            $check_customer_row = tep_db_fetch_array($check_customer_result_sql);
                            $error = !tep_validate_password($rsp_password, $check_customer_row['customers_password']);
                        }

                        if ($error !== TRUE) {
                            tep_db_perform(TABLE_CUSTOMERS, array('customers_password' => tep_encrypt_password($rsp_new_password)), 'update', "customers_id = '" . (int) $customer_id . "'");
                            $customers_upkeep_obj->unsetUpkeep('reset_password');
                            $customers_upkeep_obj->updateUpkeep();
                            $messageStack->add_session('account', SUCCESS_ACCOUNT_UPDATED, 'success');
                            $messageStack->add_session('set_secret_qna', SUCCESS_ACCOUNT_UPDATED, 'success');

                            $status = 'success';
                        } else {
                            $status = ENTRY_PASSWORD_CURRENT_NOT_MATCH_ERROR;
                        }
                    }
                }

                echo '<status>' . $status . '</status>';
            }

            break;
        case 'clear_reset_phone_flag':
            unset($_SESSION['RESET_PHONE_NO']);
            break;
        case 'update_reset_phone':
            require_once(DIR_WS_LANGUAGES . $language . '/' . FILENAME_LOGIN);
            require_once(DIR_WS_CLASSES . 'log.php');

            $error_arr = array();
            $email_address = '';
            $customers_country_dialing_code_id = '';
            $customers_telephone = '';
            $country = isset($_POST['country']) ? (int) $_POST['country'] : 0;
            $contactnumber = isset($_POST['contactnumber']) ? tep_db_prepare_input(strip_tags($_POST['contactnumber'])) : '';
            $answer = isset($_POST['answer']) ? tep_db_prepare_input($_POST['answer']) : '';
            $last4digit = isset($_POST['last4digit']) ? tep_db_prepare_input($_POST['last4digit']) : '';


            $customer_select_sql = "	SELECT customers_country_dialing_code_id, customers_telephone, customers_email_address 
                                        FROM " . TABLE_CUSTOMERS . " 
                                        WHERE customers_id = '" . $customer_id . "'";
            $customer_result_sql = tep_db_query($customer_select_sql);
            if ($customer_row = tep_db_fetch_array($customer_result_sql)) {
                $email_address = $customer_row['customers_email_address'];
                $customers_country_dialing_code_id = $customer_row['customers_country_dialing_code_id'];
                $customers_telephone = $customer_row['customers_telephone'];
            }

            if (!$country) {
                $error_arr['country'] = ENTRY_LOCATION_ERROR;
            }

            if (substr($contactnumber, -4) == '****') {
                $error_arr['contactnumber'] = ENTRY_CONTACT_NUMBER_EXIST_ERROR;
            } else {
                $contactnumber = tep_db_prepare_input(preg_replace('/[^\d]/', '', $contactnumber));
                $contactnumber = tep_parse_telephone($contactnumber, $country, 'id');

                if (strlen($contactnumber) < ENTRY_TELEPHONE_MIN_LENGTH) {
                    $error_arr['contactnumber'] = ENTRY_TELEPHONE_NUMBER_ERROR;
                } else if (tep_is_x_sharing_mobile_n_verified($customer_id, $contactnumber, $country, 'id') !== TRUE) {
                    $error_arr['contactnumber'] = ENTRY_CONTACT_NUMBER_EXIST_ERROR;
                }
            }

            if (!tep_not_empty($answer) && !tep_not_empty($last4digit)) {
                $error_arr['answer'] = sprintf(ENTRY_ANSWER_ERROR, ENTRY_SECRET_ANSWER_MIN_LENGTH);
            } else if (tep_not_empty($answer) && tep_not_empty($last4digit)) {
                if (strlen($answer) < ENTRY_SECRET_ANSWER_MIN_LENGTH) {
                    $answer = '';
                    $error_arr['answer'] = sprintf(ENTRY_ANSWER_ERROR, ENTRY_SECRET_ANSWER_MIN_LENGTH);
                }

                if (strlen($last4digit) == 4) {
                    unset($error_arr['answer']);
                } else if (isset($error_arr['answer'])) {
                    $last4digit = '';
                    $error_arr['last4digit'] = BOX_ERROR_INVALID_LAST4DIGIT;
                }
            } else if (tep_not_empty($answer) && strlen($answer) < ENTRY_SECRET_ANSWER_MIN_LENGTH) {
                $answer = '';
                $error_arr['answer'] = sprintf(ENTRY_ANSWER_ERROR, ENTRY_SECRET_ANSWER_MIN_LENGTH);
            } else if (tep_not_empty($last4digit) && strlen($last4digit) != 4) {
                $last4digit = '';
                $error_arr['last4digit'] = BOX_ERROR_INVALID_LAST4DIGIT;
            }

            if (!count($error_arr)) {
                if (tep_not_empty($answer) && $customers_security_obj->validate_customer_security_answer($answer) === true) {
                    $error_arr['answer'] = ENTRY_MISMATCH_ANSWER_ERROR;
                }

                if (tep_not_empty($last4digit)) {
                    if ($last4digit != substr($customers_telephone, -4)) {
                        $error_arr['last4digit'] = BOX_ERROR_INVALID_LAST4DIGIT;
                    } else {
                        unset($error_arr['answer']);
                    }
                }
            }

            if (count($error_arr)) {
                foreach ($error_arr as $key => $msg) {
                    echo "<error_key index='" . $key . "'>" . $msg . "</error_key>";
                }
            } else {
                $sql_data_array = array(
                    'customers_country_dialing_code_id' => $country,
                    'customers_telephone' => $contactnumber,
                );

                tep_db_perform(TABLE_CUSTOMERS, $sql_data_array, 'update', "customers_id = '" . $customer_id . "'");

                $customer_old_log_row = array(
                    'customers_country_dialing_code_id' => $customers_country_dialing_code_id,
                    'customers_telephone' => $customers_telephone
                );

                $customer_log_result_sql = tep_db_query($customer_select_sql);
                $customer_new_log_row = tep_db_fetch_array($customer_log_result_sql);

                $log_obj = new log_files($customer_id);
                $customer_changes_array = $log_obj->detect_changes($customer_old_log_row, $customer_new_log_row);
                $customer_changes_formatted_array = $log_obj->construct_log_message($customer_changes_array);
                if (count($customer_changes_formatted_array)) {
                    $changes_str = 'Changes made:' . "\n";
                    for ($i = 0; $i < count($customer_changes_formatted_array); $i++) {
                        if (count($customer_changes_formatted_array[$i])) {
                            foreach ($customer_changes_formatted_array[$i] as $field => $res) {
                                if (isset($res['plain_result']) && $res['plain_result'] == '1') {
                                    $changes_str .= $res['text'] . "\n";
                                } else {
                                    $changes_str .= '<b>' . $res['text'] . '</b>: ' . $res['from'] . (trim($res['to']) != '' ? ' --> ' . $res['to'] : '') . "\n";
                                }
                            }
                        }
                    }
                    $log_obj->insert_customer_history_log($email_address, $changes_str);
                }
            }

            break;
        default:
            echo "<result>Unknown request!</result>";
            break;
    }
}

echo '</response>';

function tep_order_notify($orders_status_id, $customer_id, $customer_email) {
    $orders_id_select_sql = "	SELECT orders_id 
								FROM " . TABLE_ORDERS . " 
								WHERE customers_id = '" . (int) $customer_id . "' 
									AND orders_status = '" . (int) $orders_status_id . "' 
									AND FIND_IN_SET('170', orders_tag_ids)";
    $orders_id_result_sql = tep_db_query($orders_id_select_sql);

    if (tep_db_num_rows($orders_id_result_sql) > 0) {
        $order_cnt = 1;
        $email_subject = 'Customer ID: ' . $customer_id . ' - Email Verified';
        $email_text = 'E-mail: ' . $customer_email . "\n" . 'ID: ' . $customer_id . "\n\n" . 'Order/s awaiting email verification:' . "\n";

        while ($orders_id_row = tep_db_fetch_array($orders_id_result_sql)) {
            $email_text .= $order_cnt . '. ' . $orders_id_row['orders_id'] . "\n";
            $order_cnt++;
        }

        tep_mail(STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS, implode(' ', array(EMAIL_SUBJECT_PREFIX, $email_subject)), $email_text, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
    }
}

function get_confirm_billing_info_content($customer_id) {
    global $country, $languages_id;

    $first_name = '';
    $last_name = '';
    $billing_address1 = '';
    $billing_address2 = '';
    $billing_city = '';
    $billing_state = '';
    $billing_zone_id = 0;
    $billing_zip = '';
    $billing_country = 0;
    $country_code = 0;
    $mobile_phone_number = '';
    $customers_default_address_id = 0;

    $customer_select_sql = "	SELECT customers_country_dialing_code_id, customers_telephone,
                                       customers_firstname, customers_lastname,
                                       customers_default_address_id
								FROM " . TABLE_CUSTOMERS . " 
								WHERE customers_id = '" . (int) $customer_id . "'";
    $customer_result_sql = tep_db_query($customer_select_sql);
    if ($customer_row = tep_db_fetch_array($customer_result_sql)) {
        $country_code = $customer_row['customers_country_dialing_code_id'];
        $mobile_phone_number = $customer_row['customers_telephone'];
        if (tep_not_empty($customer_row['customers_firstname']) && tep_not_empty($customer_row['customers_lastname'])) {
            $first_name = $customer_row['customers_firstname'];
            $last_name = $customer_row['customers_lastname'];
        } else {
            $first_name = '';
            $last_name = '';
        }
        $customers_default_address_id = $customer_row['customers_default_address_id'];
    }

    if (tep_not_empty($mobile_phone_number)) {
        $mobile_section_parameters = ' disabled="disabled"';
        $mobile_phone_number = substr($customer_row['customers_telephone'], 0, -4) . '****';
    }

    $address_select_sql = "	SELECT entry_firstname, entry_lastname, entry_street_address, entry_suburb, entry_postcode, 
                                    entry_city, entry_state, entry_country_id, entry_zone_id 
                            FROM " . TABLE_ADDRESS_BOOK . " 
                            WHERE address_book_id = '" . $customers_default_address_id . "'";
    $address_result_sql = tep_db_query($address_select_sql);
    if ($address_row = tep_db_fetch_array($address_result_sql)) {
        $billing_address1 = $address_row['entry_street_address'];
        $billing_address2 = $address_row['entry_suburb'];
        $billing_city = $address_row['entry_city'];
        $billing_zip = $address_row['entry_postcode'];
        $billing_country = tep_not_null($address_row['entry_country_id']) ? $address_row['entry_country_id'] : $country;
        $billing_state = $address_row['entry_state'];
        $billing_zone_id = $address_row['entry_zone_id'];
    }

    $zones_select_sql = "   SELECT zone_id, zone_name 
                            FROM " . TABLE_ZONES . " 
                            WHERE zone_country_id = '" . $billing_country . "' 
                            ORDER BY zone_name";
    $zones_result_sql = tep_db_query($zones_select_sql);
    if (tep_db_num_rows($zones_result_sql) > 0) {
        $zones_array = array(array('id' => '', 'text' => PULL_DOWN_DEFAULT));

        while ($zones_row = tep_db_fetch_array($zones_result_sql)) {
            $zones_array[] = array('id' => $zones_row['zone_id'], 'text' => $zones_row['zone_name']);
        }

        $state_element = tep_draw_pull_down_menu('billing_state', $zones_array, $billing_zone_id, 'id="billing_state" class="ezInputField" style="width:165px" onblur=""');
    } else {
        $state_element = tep_draw_input_field('billing_state', $billing_state, 'id="billing_state" onblur=""');
    }

    ob_start();
    ?>
    <style>
        .cbibox {padding: 0 30px 0 15px;}
        .cbibox .row {padding: 8px 0px;}
        .cbibox .row1 {padding-top: 20px;}
        .cbibox .lbl {width:165px}
        .cbibox .lbl, .cbibox .ihd1, .cbibox .shd1 {float: left;}
        .cbibox .c3 {float: right}
        .cbibox .c2 input, .cbibox .c3 input {width: 150px;}
        .cbibox .c1 input {width: 325px;}
        .cbibox .c1 select {width: 340px;}
        .cbibox .row {margin-left: 0px;}
    </style>
    <?= tep_draw_form('billing_form', '', 'post', 'id="billing_form" onSubmit="cfm_billing();return false;"') . tep_draw_hidden_field('action', 'process') ?>
    <div class="cbibox">
        <div class="row">
            <div style="background-color: #FFFDDB">
                <div class="error_msg" style="line-height: 25px;padding-left:5pt;"></div>
            </div>
        </div>
        <div class="row row1">
            <div class="lbl"><?= TEXT_NAME ?>:</div>
            <div class="c2 ihd1"><?= tep_draw_input_field('first_name', $first_name, 'id="firstname"') ?></div>
            <div class="c3 ihd1"><?= tep_draw_input_field('last_name', $last_name, 'id="lastname"') ?></div>
            <div class="clrFx"></div>
        </div>
        <div class="row">
            <div class="lbl"><?= TEXT_BILLING_ADDRESS ?></div>
            <div class="c1 ihd1"><?= tep_draw_input_field('billing_address1', $billing_address1, 'id="billing_address1"') ?></div>
            <div class="clrFx"></div>
        </div>
        <div class="row">
            <div class="lbl"><?= TEXT_BILLING_ADDRESS ?></div>
            <div class="c1 ihd1"><?= tep_draw_input_field('billing_address2', $billing_address2, 'id="billing_address2"') ?></div>
            <div class="clrFx"></div>
        </div>
        <div class="row">
            <div class="lbl"><?= TEXT_CITY ?></div>
            <div class="c1 ihd1"><?= tep_draw_input_field('billing_city', $billing_city, 'id="billing_city"') ?></div>
            <div class="clrFx"></div>
        </div>
        <div class="row">
            <div class="lbl"><?= TEXT_COUNTRY ?></div>
            <div class="c1 shd1"><?= tep_get_country_list('billing_country', (tep_not_null($billing_country) ? $billing_country : $country), 'id="billing_country" onChange="refreshStateList(this, \'billing_state_div\', \'billing_state\', \'' . (int) $languages_id . '\', true, \'billing_country_message\', \'' . ENTRY_COUNTRY_ERROR . '\', \'billing_state_message\', \'\', \'\')"') ?></div>
            <div class="clrFx"></div>
        </div>
        <div class="row">
            <div class="lbl"><?= TEXT_STATE_OR_ZIP ?></div>
            <div id="billing_state_div" class="c2 ihd1 shd1" style="width: 165px;"><?= $state_element ?></div>
            <div class="c3 ihd1"><?= tep_draw_input_field('billing_zip', $billing_zip, 'id="billing_zip"') ?></div>
            <div class="clrFx"></div>
        </div>
        <div class="row">
            <div class="lbl"><?= TEXT_COUNTRY_CODE ?></div>
            <div class="c1 shd1"><?= tep_get_ezsignup_country_list('country_code', $country_code, 'id="country_code"' . $mobile_section_parameters) ?></div>
            <div class="clrFx"></div>
        </div>
        <div class="row">
            <div class="lbl"><?= ENTRY_TELEPHONE_NUMBER ?></div>
            <div class="c1 ihd1"><?= tep_draw_input_field('mobile_phone_number', $mobile_phone_number, 'id="mobile_phone_number"' . $mobile_section_parameters) ?></div>
            <div class="clrFx"></div>
        </div>
        <div class="row" style="padding: 0pt 0pt 20px;">
            <div class="lbl" style="height: 30px;">&nbsp;</div>
    <?= TEXT_INFO_MOBILE_PHONE_NUMBER_IS_VALID ?>
        </div>
    </div>
    <input type="submit" name="submit" value="submit" style="display:none;">
    </form>
    <div class="dotborder"></div>
    <div style="padding: 15px 28px 8px;">
        <div style="float:right;"><?= tep_image_button2('gray_short', 'javascript:void(0);', BUTTON_CONFIRM, '', 'onclick="cfm_billing();"') ?></div>
        <div class="clrFx"></div>
    </div>
    <script language="javascript">
        function refreshStateList(sel1, sel2, sel3, _lang, show_title, mesgid_self, errmesg_self, mesgid, errmesg_sel, errmesg_text) {
            var span_objRef = DOMCall(sel2);
            var objRef = DOMCall(sel3);
            var selection_available = false;
    //            var span_mesg = document.getElementById(mesgid);
            var stateSelectionTitle = Array();
            stateSelectionTitle['id'] = '';
            stateSelectionTitle['text'] = 'Please Select';

            if (objRef.type == 'text') {
                objRef = document.createElement("SELECT");
                objRef.name = sel3;
                objRef.id = sel3;
                objRef.className = "ezInputField";
                objRef.style = "width:165px";
                objRef.onchange = function() {
                }
                objRef.onblur = function() {
                }
            } else {
                clearOptionList(objRef);
            }

            span_objRef.innerHTML = 'Loading ...';
    //            span_mesg.innerHTML = '';
            sel1.disabled = true;

            jQuery.ajax({
                url: "customer_xmlhttp.php?action=state_list&country_id=" + sel1.value + "&lang=" + _lang,
                dataType: 'xml',
                timeout: 60000,
                error: function() {
                },
                success: function(xml) {
                    clearOptionList(objRef);
    //                    var selection = jQuery(xml).find("selection");

                    if (jQuery(xml).find("selection").length > 0) {
                        if (show_title == true) {
                            appendToSelect(objRef, stateSelectionTitle['id'], stateSelectionTitle['text']);
                        }

                        jQuery(xml).find("selection > option").each(function() {
                            appendToSelect(objRef, jQuery(this).attr("index"), jQuery(this).text());
                        });
                    }

                    if ((show_title == true && objRef.options.length > 1) || (show_title == false && objRef.options.length > 0)) {
                        selection_available = true;
                    } else {
                        selection_available = false;
                    }

                    if (!selection_available) {
                        objRef = document.createElement("INPUT");
                        objRef.name = sel3;
                        objRef.id = sel3;
                        objRef.width = '120';
                        objRef.className = 'ezInputField';
                        objRef.onfocus = function() {
                        }
                        objRef.onblur = function() {
                        }
                    }

                    span_objRef.innerHTML = '';
                    span_objRef.appendChild(objRef);
                    sel1.disabled = false;

    //                    var msgObj = document.getElementById(mesgid_self);
    //                    var msgObj_state = document.getElementById(mesgid);
    //                    var img_cross = '<img src="images/icons/error.gif" width="13px" height="12px" style="margin: 2px 5px 2px 2px; float: left;">';

    //                    if (trim_str(document.getElementById('billing_address1').value).length > 0 && (trim_str(sel1.value) == '' || trim_str(sel1.value).length == 0))
    //                    {
    //                        msgObj.style.color="#000000";
    //                        msgObj.style.backgroundColor = "#FFD4D4";
    //                        msgObj.style.width = "100%";
    //                        msgObj.style.padding = "0px 0px 2px 0px";
    //                        msgObj.innerHTML = '<div>' + img_cross + '</div><div style="display: inline;"><span style="display: block;">' + errmesg_self + '</span></div>';
    //                        msgObj_state.innerHTML = '';
    //                    }
    //                    else {
    //                        msgObj.innerHTML = '';
    //                        msgObj_state.innerHTML = '';
    //                        msgObj_state.style.backgroundColor = '';
    //                    }
                }
            });
        }
    </script>
    <?
    $return_string = ob_get_contents();
    ob_end_clean();

    return $return_string;
}
?>