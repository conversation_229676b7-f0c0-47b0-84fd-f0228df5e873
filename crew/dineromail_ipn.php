<?
/*
  	$Id: dineromail_ipn.php, v1.0 2005/11/29 18:08:05
  	Author : <PERSON> (<EMAIL>)
  	Title: kuaiqian.com Payment Module V1.0
	
	Revisions:
		Version 1.0 Initial Payment Module
	
  	Copyright (c) 2007 SKC Venture
	
  	Released under the GNU General Public License
*/

require_once('includes/application_top.php');

$payment = 'dineromail';

if (isset($_REQUEST) && count($_REQUEST)) {
	
	if (isset($_REQUEST['Notificacion'])) {
		
	    include_once(DIR_WS_CLASSES . 'anti_fraud.php');
		include_once(DIR_WS_CLASSES . 'log.php');
	    include_once(DIR_WS_CLASSES . 'order.php');
	    include_once(DIR_WS_CLASSES . 'payment.php');
		
		$log_object = new log_files('system');
		
		$matched_order_ids = array();
		preg_match_all("/<[idID]+>([0-9]+)<\/[idID]+>/U",$_REQUEST['Notificacion'], $matched_order_ids);
		
		if (count($matched_order_ids[1])) {
			foreach ($matched_order_ids[1] as $orders_id) {
				require_once(DIR_WS_MODULES . 'payment/dineromail/classes/dineromail_ipn_class.php');
			    $dineromail_ipn = new dineromail_ipn(array('orderId'=>$orders_id));
			    
				$payment_modules = new payment($payment);
				$order = new order($orders_id);
				
				$$payment = new $payment($order->info['payment_methods_id']);
			    $dineromail_currencies = $$payment->get_support_currencies($$payment->payment_methods_id, false);
			    $dineromail_currency = $order->info['currency'];
			    if (!in_array($dineromail_currency, $dineromail_currencies)) {
			    	$dineromail_currency = $dineromail_currencies[0];
			    }
				
				$$payment->get_merchant_account($dineromail_currency);
				
				$tipo = 1;
				$poststring='data=%3CREPORTE%3E%3CNROCTA%3E'.$$payment->dineromail_acc_id.'%3C%2FNROCTA%3E%3CDETALLE%3E%3CCONSULTA%3E%3CCLAVE%3E'.$$payment->dineromail_password.'%3C%2FCLAVE%3E%3CTIPO%3E'.$tipo.'%3C%2FTIPO%3E%3COPERACIONES%3E%3CID%3E'.$orders_id.'%3C%2FID%3E%3C%2FOPERACIONES%3E%3C%2FCONSULTA%3E%3C%2FDETALLE%3E%3C%2FREPORTE%3E';
				$dineromail_xml_response = $$payment->curl_connect($$payment->form_status_url, $poststring);
				
				if ($dineromail_ipn->validate_transaction_data($dineromail_xml_response, $order, $$payment)) {
					if ($order->info['orders_status_id'] == $$payment->order_status) {	// If the order still in initial status
						// Possible values of the Estado element : 1 PAYMENT PENDING, 2 CREDIT, 3 CANCELLED
						if ($dineromail_xml_response['status'] == '2') {
							$dineromail_payment_history_data_array = array(	'orders_id' => (int)$orders_id,
																	  		'dineromail_status_date' => 'now()',
																	  		'dineromail_status' => tep_db_prepare_input(($dineromail_xml_response['status'] ? $dineromail_xml_response['status'] : '')),
																	  		'dineromail_status_description' => "Status: " . $$payment->get_dineromail_status($dineromail_xml_response['status'])
												                      		);
							tep_db_perform(TABLE_DINEROMAIL_STATUS_HISTORY, $dineromail_payment_history_data_array);
							$dineromail_ipn->process_this_order($orders_id, $$payment, $$payment->order_processing_status);
						} else {
							$dineromail_payment_history_data_array = array(	'orders_id' => (int)$orders_id,
																	  		'dineromail_status_date' => 'now()',
																	  		'dineromail_status' => tep_db_prepare_input(($dineromail_xml_response['status'] ? $dineromail_xml_response['status'] : '')),
																	  		'dineromail_status_description' => "Status: " . $$payment->get_dineromail_status($dineromail_xml_response['status'])
												                      		);
							tep_db_perform(TABLE_DINEROMAIL_STATUS_HISTORY, $dineromail_payment_history_data_array);
						}
					}
				}
				
				/*
				Possible values of the MetodoPago element
				1 DINEROMAIL FUNDS
				2 CASH THROUGH PAGOFACIL, RAPIPAGO, COBROEXPRESS O BAPROPAGO
				3 CREDIT CARD
				4 BANK TRANSFER
				*/
				$dineromail_payment_data_array = array(	'dineromail_customer_email' => tep_db_prepare_input(($dineromail_xml_response['email'] ? $dineromail_xml_response['email'] : '')),
												  		'dineromail_amount' => tep_db_prepare_input(($dineromail_xml_response['total_amount'] ? $dineromail_xml_response['total_amount'] : '')),
												  		'dineromail_currency' => tep_db_prepare_input( $dineromail_currency),
												  		'dineromail_currency_flag' => tep_db_prepare_input(($dineromail_xml_response['currency_flag'] ? $dineromail_xml_response['currency_flag'] : '')),
												  		'dineromail_net_amount' => (double)($dineromail_xml_response['net_amount'] ? $dineromail_xml_response['net_amount'] : ''),
												  		'dineromail_payment_method' => (int)($dineromail_xml_response['payment_method'] ? $dineromail_xml_response['payment_method'] : ''),
												  		'dineromail_status' => tep_db_prepare_input(($dineromail_xml_response['status'] ? $dineromail_xml_response['status'] : ''))
						                      		);
				// check order exist
				$dineromail_check_exist_select_sql = "	SELECT orders_id 
														FROM " . TABLE_DINEROMAIL . " 
														WHERE orders_id = '".(int)$orders_id."'";
				$dineromail_check_exist_result_sql = tep_db_query($dineromail_check_exist_select_sql);
				if (tep_db_num_rows($dineromail_check_exist_result_sql)) {
					tep_db_perform(TABLE_DINEROMAIL, $dineromail_payment_data_array, 'update', ' orders_id = "'.(int)$orders_id.'" ');
				} else {
					$dineromail_payment_data_array['orders_id'] = (int)$orders_id;
					tep_db_perform(TABLE_DINEROMAIL, $dineromail_payment_data_array);
				}
			}
		}
	}
}
?>
