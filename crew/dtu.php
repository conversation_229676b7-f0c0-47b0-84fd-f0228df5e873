<?php
require('includes/application_top.php');
require_once(DIR_WS_CLASSES . FILENAME_MAIN_PAGE);
require_once(DIR_WS_CLASSES . 'direct_topup.php');

$error = FALSE;
$cookie_array = array();
$gameID = (isset($_GET['gameID']) && is_numeric($_GET['gameID'])) ? $_GET['gameID'] : '';
$dtu_server = isset($_GET['server']) ? tep_db_prepare_input(strip_tags($_GET['server'])) : '';
$dtu_server = htmlentities($dtu_server, ENT_QUOTES, "utf-8");
$dtu_platform = isset($_GET['platform']) ? tep_db_prepare_input(strip_tags($_GET['platform'])) : '';
$dtu_platform = htmlentities($dtu_platform, ENT_QUOTES, "utf-8");
$dtu_character = isset($_GET['character']) ? tep_db_prepare_input(strip_tags($_GET['character'])) : '';
$dtu_character = htmlentities($dtu_character, ENT_QUOTES, "utf-8");
$dtu_account = isset($_GET['account']) ? tep_db_prepare_input(strip_tags($_GET['account'])) : '';
$dtu_account = htmlentities($dtu_account, ENT_QUOTES, "utf-8");
$dtu_object = new direct_topup();

if (tep_not_empty($gameID)) {
    if ($dtu_object->is_customer_id_to_email_conversion_needed($gameID)) {
        $email_address = tep_get_customers_email($dtu_account);
        if (isset($email_address) && !empty($email_address)) $dtu_account = $email_address;
    }
    $cat_path = main_page::game_cat_path_by_tpl($gameID);
 
    if (tep_not_empty($cat_path)) {
        if (tep_not_empty($dtu_server)) {
            $cookie_array['server'] = $dtu_server;
        }

        if (tep_not_empty($dtu_platform)) {
            $cookie_array['account_platform'] = $dtu_platform;
        }

        if (tep_not_empty($dtu_character)) {
            $cookie_array['character'] = $dtu_character;
        }

        if (tep_not_empty($dtu_account)) {
            $cookie_array['account'] = $dtu_account;
        }
        
        update_dtu_game_info($gameID, $cookie_array);
        tep_redirect(tep_href_link(FILENAME_DEFAULT, 'cPath=' . $cat_path . (isset($_GET['is_mobile']) ? '&is_mobile=' . $_GET['is_mobile'] : ''), 'SSL'));
    } else {
        tep_redirect(tep_href_link(FILENAME_DEFAULT, '', 'SSL'));
    }
}
?>