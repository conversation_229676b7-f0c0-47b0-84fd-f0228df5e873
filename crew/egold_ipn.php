<?
/*
  	$Id: egold_ipn.php, v1.0 2005/11/29 18:08:05
  	Author : <PERSON> (<EMAIL>)
  	Title: e-gold Payment Module V1.0
	
	Revisions:
		Version 1.0 Initial Payment Module
	
  	Copyright (c) 2006 SKC Venture
	
  	Released under the GNU General Public License
*/

require_once('includes/modules/payment/egold/application_top.inc.php');
require_once(DIR_WS_MODULES . 'payment/egold/classes/egold_ipn_class.php');
include(DIR_WS_LANGUAGES . $language . '/' . FILENAME_CHECKOUT_PROCESS);

//Process payment
if (isset($_POST) && count($_POST)) {
	/**********************************************************
		(1) Check this order is pay to us
		(2) This is Pending order
	**********************************************************/
	$payment = 'egold';
    
    // load selected payment module
    include_once(DIR_WS_CLASSES . 'payment.php');
    $payment_modules = new payment($payment);
    
    $egold_ipn = new egold_ipn($_POST);
    
	//transaction_id
	include_once(DIR_WS_FUNCTIONS . 'custom_product_info.php');
	include_once(DIR_WS_CLASSES . 'anti_fraud.php');
    include_once(DIR_WS_CLASSES . 'order.php');
    include_once(DIR_WS_CLASSES . 'log.php');
    
	$log_object = new log_files('system');
	
	// Valid e-gold notification will come from this IP ranges
	if (!isset($$payment->valid_ip_template) || 
		strpos(tep_get_ip_address(), $$payment->valid_ip_template) === 0) {
		if ($egold_ipn->validate_receiver_account($$payment->eg_account_no)) {
			$orders_id = $egold_ipn->get_order_id();
			
			if (tep_not_null($orders_id)) {
				$order = new order($orders_id);
				
				if ($order->info['orders_status_id'] == $$payment->order_status) {	// If the order still in initial status
					$egold_ipn->authenticate($order, $orders_id, $$payment);
				}
			}
		}
	}
}
?>