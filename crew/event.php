<?php
/*
  	$Id: event.php,v 1.16 2011/05/16 03:22:16 weichen Exp $
*/
require('includes/application_top.php');
require(DIR_WS_CLASSES . 'event.php');
require(DIR_WS_CLASSES . 'mail/htmlMimeMail5/htmlMimeMail.php');
require(DIR_WS_LANGUAGES . $language . '/' . FILENAME_EVENT);

if (tep_not_null($_GET['news_id']) && is_numeric($_GET['news_id'])) {
	$news_id = tep_db_prepare_input($_GET['news_id']);
} else {
	tep_redirect(tep_href_link(FILENAME_DEFAULT));
}

$html = new event($news_id);

switch($action) {
	case "submit_event_form":
		if (!tep_session_is_registered('customer_id')) {
	    	return false;
		}
		
		if($html->submit_event_form($_POST, $_FILES, $messageStack)) {
			tep_redirect(tep_href_link(FILENAME_DEFAULT));
		} else {
			tep_redirect(tep_href_link(FILENAME_EVENT, tep_get_all_get_params(array('action'))));
		}
		break;
	default:
		if (!tep_session_is_registered('customer_id')) {
			if($navigation){
				$navigation->set_snapshot();
			}
		}
		
		$action_res_array = $html->addForm($messageStack);
		
		if ($action_res_array['code'] == '-1') {
			tep_redirect(tep_href_link(FILENAME_DEFAULT));
		} else {
			$form_content = $action_res_array['html'] ;
		}
		break;
}

$breadcrumb->add(NAVBAR_TITLE, tep_href_link(FILENAME_EVENT, '', 'NONSSL'));

$content_select_sql = "	SELECT lnd.content, e.events_id
						FROM " . TABLE_LATEST_NEWS_DESCRIPTION . " AS lnd 
						INNER JOIN " . TABLE_EVENTS . " AS e 
							ON lnd.news_id = e.news_id 
						WHERE lnd.news_id = '" . tep_db_input($news_id) . "' 
							AND lnd.headline <> '' 
							AND ( if(lnd.language_id = '". $languages_id. "', 1, 
	 								  if ((select count(lnd_inner.news_id) > 0 from " . TABLE_LATEST_NEWS_DESCRIPTION . " as lnd_inner
	 										where lnd_inner.news_id ='".tep_db_input($news_id)."' 
	 										and lnd_inner.language_id ='". $languages_id. "'), 
											0,
 					 						lnd.language_id = '".$default_languages_id."')
 											)
 										 )";
$content_result_sql = tep_db_query($content_select_sql);

if ($content_row = tep_db_fetch_array($content_result_sql)) {
	$events_id = $content_row['events_id'];
	
	$event_select_sql = "	SELECT ed.events_name
							FROM " . TABLE_EVENTS_DESCRIPTION . " AS ed
							WHERE ed.events_id = '" . tep_db_input($events_id) . "'
								AND ( IF(ed.language_id = '". $languages_id. "', 1,
	 								  IF ((SELECT COUNT(ed_inner.events_id) > 0
	 								  		FROM " . TABLE_EVENTS_DESCRIPTION . " AS ed_inner
	 										WHERE ed_inner.events_id = '" . $events_id . "'
	 											AND ed_inner.events_name <> ''
	 											AND ed_inner.language_id ='". $languages_id. "'), 0,
 					 								ed.language_id = '".$default_languages_id."')
 											)
 										 )";
 	$event_result_sql = tep_db_query($event_select_sql);
 	$event_row = tep_db_fetch_array($event_result_sql);
 	
 	$content_row['events_name'] = $event_row['events_name'];
} else {
	tep_redirect(tep_href_link(FILENAME_DEFAULT));
}

$content = CONTENT_EVENT;

require(DIR_WS_TEMPLATES . TEMPLATENAME_MAIN_PAGE);

require(DIR_WS_INCLUDES . 'application_bottom.php');
?>