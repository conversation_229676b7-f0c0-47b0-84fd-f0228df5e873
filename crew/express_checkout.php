<?php
/*
  	$Id: express_checkout.php,v 1.10 2011/07/11 07:23:46 chingyen Exp $
	
	Developer: Ching Yen
  	Copyright (c) 2005 SKC Ventrue
	
  	Released under the GNU General Public License
*/

ob_start();
?>
	<!-- NEW EXPRESS CHECKOUT -->
	<div id="ecBox" width="300px" style="text-align: center; padding: 13px;">
		<div id="ecBoxInitLoading" style="display: block;">
			<div class="vspacing"></div>
			<div style="text-align: center;" width="100%"><?=tep_image(DIR_WS_IMAGES . 'loading.gif', '', 20, 20);?></div>
			<input type="hidden" id="ecInitLoad" />
		</div>
		
		<div id="ecBoxInfo" style="display: none;">
			<span><?=EXPRESS_CHECKOUT_INFORMATION;?></span>
			<div class="vspacing"></div>
		</div>
		
		
		<!-- Select game -->
		<div id="ecBoxGame" style="display: none;">
			<select name="cPath" id="cPath" onChange="javascript: exp_prod_type_list();"></select>
			<div class="vspacing"></div>
		</div>
		
		
		<!-- Select product type -->
		<div id="ecBoxProdType" style="display: none;">
			<div id="ecProdType">
				<select name="product_type" id="product_type" onChange="javascript: exp_prod_list();"></select>
			</div>
			
			<div class="vspacing"></div>
		</div>
		
		
		<!-- Select product / categories -->
		<div id="ecBoxProdQty" style="display: none;">
			<!-- Select product -->
			<div id="ecProdCmb" style="display: none; float: left;">
				<select name="products_id" id="products_id" onChange="javascript: exp_prod_status();"></select>
			</div>
			
			<!-- Qty -->
			<div id="ecQty" class="dhx_combo_box" style="display: none; float: left; margin-left: 5px;">
				<input type="text" name="buyqty<?=time()?>" class="dhx_combo_box" id="buyqty" maxlength="4" style="width: 59px; position: relative;" value="1" onBlur="javascript:exp_price_op();" onFocus="this.value=''" onKeyPress="javascript: return expQtySubmit(event);" />
			</div>
			
			<div class="vspacing"></div>
		</div>
		
		<!-- Select product type -->
		<div id="ecBoxMode" style="display: none;position:relative;">
			<div id="ecModeListing">
				<select name="ecDeliveryMode" id="ecDeliveryMode" onChange="javascript: exp_prod_delivery_mode();"></select>
			</div>
			<div id="tr_top_up_error_msg" style="display: none;margin-top:5px;">
				<div style="width:200px;position:relative">
					<div style="float:left;"><?=tep_image(DIR_WS_ICONS . 'warning_blue.gif')?></div>
					<div style="float:left;padding-left:6px;" id="div_error_msg" class="redIndicator"></div>
				</div>
			</div>
			
			<!-- Select delivery mode -->
			<div id="ecBoxDTUInput" style="display: none;">
				<div id="tr_top_up_error_msg" style="display: none;">
					<div style="width:200px;left:27px;position:relative">
						<div style="float:left;"><?=tep_image(DIR_WS_ICONS . 'warning_blue.gif')?></div>
						<div style="float:left;" id="div_error_msg" class="redIndicator"></div>
					</div>
				</div>
				<div style="clear:both;" id="ecBoxDTUInputHtml"></div>
			</div>
			
			<div class="vspacing"></div>
		</div>
		
		<!-- Button -->
		<div style="min-height:100px; position: relative;">
			<div id="ecBoxPriceOPLine" style="border-top-color: #CCCCCC;border-top-style: solid;border-top-width: 1px;height: 10px; width: 100%; display:none;"></div>
			
			<!-- Price and OP -->
			<div id="ecBoxPriceLoading" style="display: none;">
				<div class="vspacing"></div>
				<div style="text-align: center;" width="100%"><?=tep_image(DIR_WS_IMAGES . 'loading.gif', '', 20, 20);?></div>
				<div class="vspacing"></div>
			</div>
			
			<div id="ecBoxPriceOP" style="display: none; ">
				<div id="ecBoxPriceOPLine" style="border-top-color: #CCCCCC;border-top-style: solid;border-top-width: 1px;height: 10px; width: 100%;"></div>
				
				<div id="ecPrice" style="display: none; ">
					<?=tep_image_button2('gray_box', 'javascript:void(0);', '<table border="0" cellpadding="0" cellspacing="0" style="border-style: collapse; width: 235px;"><tr><td style="width: 90px;" valign="top"><span class="left" style="width:80px;">' . TEXT_EXP_SUB_TOTAL . '</span></td><td><span id="ecPriceTxt" class="largeFont" style="font-weight: bold;"></span></td></tr></table>', 235, ' style="width: 100%;" ');?>
				</div>
				
				<div id="ecOP" style="display: none; ">
					<?=tep_image_button2('gray_box', 'javascript:void(0);', '<table border="0" cellpadding="0" cellspacing="0" style="border-style: collapse; width: 235px;"><tr><td style="width: 90px;" valign="top"><span class="left" style="width:80px;">' . TEXT_EXP_TOTAL_REBATE . '</span></td><td><span id="ecOPTxt" style="font-weight: normal;"></span></td></tr></table>', 235, ' style="width: 100%;" ');?>
				</div>
				<div class="vspacing"></div>
			</div>

			
			<div id="ecBoxBtn" style="display: none; text-align: center; width: 100%;">
				<table border="0" cellpadding="0" cellspacing="0" width="100%">
					<tr>
						<td>
							<div id="ecYellowBtn" style="display: none; text-align: center;">
								<?=tep_image_button2('yellow', 'javascript: expressCheckout();', '<span id="ecYellowBtnText" style="text-align: center;">' . BUTTON_EXP_CHECKOUT_CHECKOUT . '</span>', 210, ' id="ecYellowCheckOut" style="text-align: center; width: 100%;" ');?>
							</div>
							
							<div id="ecRedBtn" style="display: none;">
								<?=tep_image_button2('red', 'javascript: expressCheckout();', '<span id="ecRedBtnText" style="text-align: center;">' . BUTTON_EXP_CHECKOUT_CHECKOUT . '</span>', 210, ' id="ecRedCheckOut" style="text-align: center; width: 100%;" ');?>
							</div>
							
							<div id="ecGreenBtn" style="display: none; text-align: center;">
								<?=tep_image_button2('green', 'javascript: expressCheckout();', '<span id="ecGreenBtnText" style="text-align: center;">' . BUTTON_EXP_CHECKOUT_CHECKOUT . '</span>', 240, ' id="ecGreenCheckOut" style="text-align: center; width: 100%;" ');?>
							</div>
							
							<div id="ecGrayBtn" style="display: block; text-align: center;">
								<?=tep_image_button2('gray', 'javascript: void(0);', '<span id="ecGrayBtnText" style="text-align: center;">' . BUTTON_EXP_CHECKOUT_CHECKOUT . '</span>', 240, ' id="ecGrayCheckOut" style="text-align: center; width: 100%;" ');?>
							</div>
						</td>
					</tr>
				</table>
				
				<div class="vspacing"></div>
			</div>
		</div>
		
		<!-- Delivery messenge -->
		<div id="ecBoxMessage" style="display: none; text-align: center; position: relative;">
			<span id="ecMessage"></span>
			
			<div class="vspacing"></div>
		</div>
		
		<input type="hidden" id="bundle" />
	</div>
	<!-- End of NEW EXPRESS CHECKOUT -->
	
	
	
	<script language="javascript">
		window.dhx_globalImgPath = "<?=DIR_WS_JAVASCRIPT;?>dhtmlxCombo/imgs/"
	</script>
	
	<script src="<?=DIR_WS_JAVASCRIPT;?>dhtmlxCombo/dhtmlxcommon.js"></script>
	<script src="<?=DIR_WS_JAVASCRIPT;?>dhtmlxCombo/dhtmlxcombo.js"></script>
	<script src="<?=DIR_WS_JAVASCRIPT;?>dhtmlxCombo/ext/dhtmlxcombo_whp.js" type="text/javascript"></script>
	
	<script language="javascript" src="<?=DIR_WS_JAVASCRIPT?>xmlhttp.js"></script>
	<script language="javascript">
		<!--
			var exp_maxQty = 20;
			
			var GA_CUSTOMERS_GROUPS_NAME = "<?=tep_not_null($_SESSION['GA_CUSTOMERS_GROUPS_NAME']) ? $_SESSION['GA_CUSTOMERS_GROUPS_NAME'] : '';?>";
			
			var TABLE_HEADING_DELIVERY_TIME = '<?=TABLE_HEADING_DELIVERY_TIME;?>';
			
			var COMBO_SELECT_CATEGORIES = '<?=COMBO_SELECT_CATEGORIES;?>';
			var COMBO_SELECT_GAME = '<?=COMBO_SELECT_GAME;?>';
			var COMBO_SELECT_PRODUCT = '<?=COMBO_SELECT_PRODUCT;?>';
			var COMBO_SELECT_PRODUCT_TYPE = '<?=COMBO_SELECT_PRODUCT_TYPE;?>';
			var COMBO_SELECT_QTY = '<?=COMBO_SELECT_QTY;?>';
			var COMBO_SELECT_SERVERS = '<?=COMBO_SELECT_SERVERS;?>';
			var COMBO_SELECT_DELIVERY_MODE = '<?=COMBO_SELECT_DELIVERY_MODE;?>';
			
			var BUTTON_EXP_CHECKOUT_BUY_NOW = '<?=BUTTON_EXP_CHECKOUT_BUY_NOW;?>';
			var BUTTON_EXP_CHECKOUT_CHECKOUT = '<?=BUTTON_EXP_CHECKOUT_CHECKOUT;?>';
			var BUTTON_EXP_CHECKOUT_PRE_ORDER = '<?=BUTTON_EXP_CHECKOUT_PRE_ORDER;?>';
			var BUTTON_EXP_CHECKOUT_OUT_OF_STOCK = '<?=BUTTON_EXP_CHECKOUT_OUT_OF_STOCK;?>';
			
			var BUY_NOW_LINK = '<?=tep_href_link(FILENAME_DEFAULT, "action=buy_now");?>';
			var CHECKOUT_PAYMENT_LINK = '<?=tep_href_link(FILENAME_CHECKOUT_PAYMENT);?>';
			var CHECKOUT_XMLHTTP_LINK = '<?=tep_href_link(FILENAME_CHECKOUT_XMLHTTP);?>';
			
			var TEXT_EXP_CHECKOUT_BULK_NOT_AVAILABLE = '<?=TEXT_EXP_CHECKOUT_BULK_NOT_AVAILABLE;?>';
			var TEXT_GENERAL = '<?=TEXT_GENERAL;?>';
			
		//-->
	</script>
	<script language="javascript" src="<?=DIR_WS_JAVASCRIPT?>express_checkout.js"></script>
	
<?php
$ec_content = ob_get_contents();
ob_end_clean();
?>