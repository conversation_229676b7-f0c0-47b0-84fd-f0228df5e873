<?php

include_once('includes/application_top.php');
include_once(DIR_WS_CLASSES . 'product.php');

$action = $_GET['action'];
$tpl = $_GET['tpl'];
$cPath = $_GET['cPath'];
$pid = $_GET['pid'];
$buyqty = $_GET['buyqty'];
$prod = $_GET['prod'];

if (tep_not_null($action)) {
	
	if ( !isset($_SERVER['HTTP_REFERER']) ||
		(strstr($_SERVER['HTTP_REFERER'], HTTP_SERVER) === FALSE && strstr($_SERVER['HTTP_REFERER'], HTTPS_SERVER) === FALSE)
		) {
		echo "You are not allowed to access from outside";
		exit;
	}
	
	if ($action != 'fastlink') {
		$ec_product = new product($cPath, (tep_not_null($tpl) ? $tpl : 0));
		$json = new Services_JSON();
	}
	
	switch ($action) {
		case 'game_available':
			if (is_array($zone_info_array[1]->zone_categories_id) && count($zone_info_array[1]->zone_categories_id)) {
				$this_cat_obj = new category(0);
				$game_available = $this_cat_obj->get_game_product_type_cat_info(array(0, 1, 2, 4));
			}
			echo $json->encode($game_available);
			
			break;
			
			
		case 'product_type':
			$product_type = $ec_product->get_game_product_type($cPath, array(0, 1, 2, 4));
			echo $json->encode($product_type);

			break;
			
			
		case 'products':
		    $cPath_array = tep_parse_category_path($cPath);
    		$ec_product->cpath_check();
    		$ec_product->plain = true;
    		
    		$products = $ec_product->get_product_listing();
    		if (tep_not_null($products['pre_order_time'])) {
    			$products['pre_order_time'] = tep_get_eta_string($products['pre_order_time']);
    		}
    		echo $json->encode($products);
    		
			break;
			
			
		case 'status':
			$status = '';
		    $status = $ec_product->get_plain_products_status($pid, $buyqty);
    		if (tep_not_null($status['pre_order_time'])) {
    			$status['pre_order_time'] = tep_get_eta_string($status['pre_order_time']);
    		}
		    echo $json->encode($status);
		    
			break;
			
			
		case 'fastlink':
			$fastlink = '';
			
			if (tep_not_null($tpl) && ($tpl == 4)) {
				$fastlink = tep_href_link(FILENAME_DEFAULT, 'cPath=' . $cPath);
			} else {
				if (tep_not_null($prod) && ($prod == 1)) {
					$parent_path = tep_get_product_path($pid);
					
					if (tep_not_null($parent_path)) {
						$fastlink = tep_href_link(FILENAME_DEFAULT, 'cPath=' . $parent_path);
					}
				} else {
					$parent_path = trim(tep_get_categories_parent_path($pid), '_');
					
					if (tep_not_null($parent_path)) {
						$fastlink = tep_href_link(FILENAME_DEFAULT, 'cPath=' . $parent_path . '_' . $pid);
					}
				}
			}
			
			echo $fastlink;
			
			break;
	}
}

?>