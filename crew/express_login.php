<?php
/*
  $Id: express_login.php,v 1.9 2013/01/09 04:48:26 chingyen Exp $

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2003 osCommerce

  Released under the GNU General Public License
*/

require('includes/application_top.php');
require(DIR_WS_LANGUAGES . $language . '/' . FILENAME_LOGIN); //FILENAME_EXPRESS_LOGIN
include_once(DIR_WS_CLASSES . 'recaptcha.php');

define('GENERAL_NEWSLETTER_ID', 17);

$fb_user_email = '';
$fb_user_firstname = '';
$fb_user_lastname = '';
$newsletter_group_array = array();
$last_page = (isset($_REQUEST['trace']) ? $_REQUEST['trace'] : '');
$action = (isset($_REQUEST['fb_action']) ? $_REQUEST['fb_action'] : '');

// redirect the customer to a friendly cookie-must-be-enabled page if cookies are disabled (or the session has not started)
if ($session_started == false) {
	tep_redirect(tep_href_link(FILENAME_COOKIE_USAGE));
} else if (tep_session_is_registered('customer_id')) {
	tep_redirect(tep_href_link(FILENAME_FACEBOOK_CONNECT, '', 'SSL'));
} else if (!$ogm_fb_obj->user || !tep_not_null($action)) { // when $ogm_fb_obj->user exist or user has logged in FB and $action exist. 
	tep_redirect(tep_href_link(FILENAME_DEFAULT, '', 'SSL'));
} else if ($ogm_fb_obj->get_FB_prelogin_status() != 'STEP_2') { // when user had across STEP_1 ()
	tep_redirect(tep_href_link(FILENAME_DEFAULT, '', 'SSL'));
}

if (tep_not_null($last_page)) {
	$_SESSION['trace'] = $last_page;
}

if ($action == 'show_create_new') {
	$fb_return_user_info_array = $ogm_fb_obj->get_users_info();
	
	if (count($fb_return_user_info_array)) {
		$fb_user_email = $fb_return_user_info_array['email'];
		$fb_user_firstname = $fb_return_user_info_array['first_name'];
		$fb_user_lastname = $fb_return_user_info_array['last_name'];
	}
	
	$newsletter_group_select_sql = "SELECT newsletters_groups_id, newsletters_groups_name 
									FROM " . TABLE_NEWSLETTERS_GROUPS . " 
									WHERE module = 'newsletter' 
										AND newsletters_groups_id = '" . tep_db_input(GENERAL_NEWSLETTER_ID) . "'";
	$newsletter_group_result_sql = tep_db_query($newsletter_group_select_sql);
	while ($newsletter_group_row = tep_db_fetch_array($newsletter_group_result_sql)) {
		$newsletter_group_array[$newsletter_group_row['newsletters_groups_id']] = ENTRY_NEWSLETTER_TEXT;
	}
    
    $oauth_login = oauth::is_oauth_login();

    if (tep_session_is_registered('customer_id')) {
        if (tep_not_null($oauth_login)) {
            switch ($oauth_login) {
                case 'oauth':
                    oauth::Oauth_login_success();
                break;
            }
        } 
    }
}

$breadcrumb->add(NAVBAR_TITLE, tep_href_link(FILENAME_EXPRESS_LOGIN, '', 'SSL'));
$content = CONTENT_EXPRESS_LOGIN;

require(DIR_WS_TEMPLATES . TEMPLATENAME_MAIN_PAGE);
require(DIR_WS_INCLUDES . 'application_bottom.php');
?>