<?php

require('includes/application_top.php');
require_once(DIR_WS_LANGUAGES . $language . '/' . FILENAME_FACEBOOK_CONNECT);

$action = isset($_REQUEST['action']) ? $_REQUEST['action'] : '';
switch ($action) {
    case 'unbind_facebook_connection':
        $customer_id = (isset($_GET['customer_id']) && is_numeric($_GET['customer_id'])) ? $_GET['customer_id'] : '';
        $serial = isset($_GET['serial']) ? $_GET['serial'] : '';
        $game_url = isset($_GET['game_url']) ? $_GET['game_url'] : '';
        $customers_upkeep = new customers_upkeep($customer_id);
        $fb_detach_code = $customers_upkeep->getUpkeepValue('facebook_detach_code');
        if ($fb_detach_code == $serial) {
            if (!empty($customer_id)) {
                $query = " 	DELETE FROM " . TABLE_CUSTOMERS_CONNECTION . "
                            WHERE customers_id = '" . tep_db_input($customer_id) . "' AND provider='Facebook'";
                tep_db_query($query);
                $sql_data_array = array(
                    'customers_setting_value' => 0,
                    'updated_datetime' => 'now()',
                );

                tep_db_perform(TABLE_CUSTOMERS_SETTING, $sql_data_array, 'update', "customers_id = '" . $customer_id . "' AND customers_setting_key = 'request_fb_unbind'");
                $customers_upkeep->unsetUpkeep('facebook_detach_code');
                $customers_upkeep->updateUpkeep();
                if (!empty($game_url)) {
                    tep_redirect($game_url);
                } else {
                    echo 'Please go back to the parent window.';
                }
            } else {
                echo 'ERROR';
            }
        } else {
            echo 'ERROR';
        }
        break;
}
?>
