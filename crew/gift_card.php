<?php
require('includes/application_top.php');
require_once(DIR_WS_CLASSES . 'gift_card.php');
require_once(DIR_WS_CLASSES . 'user.php');
require_once(DIR_WS_CLASSES . 'access_token.php');
require_once(DIR_WS_CLASSES . 'order.php');
require_once(DIR_WS_CLASSES . 'order_total.php');
require_once(DIR_WS_LANGUAGES . $language . '/' . FILENAME_LOGIN);
require_once(DIR_WS_LANGUAGES . $language . '/' . FILENAME_GIFT_CARD);

$action = isset($_GET['action']) ? $_GET['action'] : '';
//$email_address = isset($_POST['email']) ? tep_db_prepare_input(strip_tags($_POST['email'])) : '';
//$password = tep_not_null($_POST['password']) ? tep_db_prepare_input(strip_tags($_POST['password'])) : "";
//$fname = isset($_POST['fname']) ? tep_db_prepare_input($_POST['fname']) : '';
//$lname = isset($_POST['lname']) ? tep_db_prepare_input($_POST['lname']) : '';
$email_acc_type = isset($_POST['etype']) ? (int)$_POST['etype'] : 0;  // 0 : existing customer, 1 : new acc.
$cid = isset($_SESSION['customer_id']) ? $_SESSION['customer_id'] : 0;

//$tab = isset($_GET['tab']) ? (int)$_GET['tab'] : 1;
//$qty = isset($_POST['qty']) ? $_POST['qty'] : null;
//$game_id = isset($_REQUEST['game_id']) ? tep_db_prepare_input($_REQUEST['game_id']) : '';
//$product_id = isset($_REQUEST['product_id']) ? tep_db_prepare_input($_REQUEST['product_id']) : '';
//$game_qty = isset($_REQUEST['game_qty']) ? (int)$_REQUEST['game_qty'] : 1;
//$game_dm = isset($_REQUEST['game_dm']) ? (int)$_REQUEST['game_dm'] : 0;
//$game_info = isset($_REQUEST['game_info']) ? $_REQUEST['game_info'] : null;

$error = FALSE;
$total_amount = 0;
$pwgc_amount = '0.00';
$temp_cart_holder = array();
$filtered_arr = array();
$process_arr = array();
//$step3_error_array = array();
$qty_array = array(array('id' => 0, 'text' => '--- ' . TEXT_SELECT . ' ---'));
$game_list_array = $qty_array;
$product_list_array = $qty_array;
$dm_list_array = $qty_array;
$dm_info_array = array();

$gift_card_obj = new gift_card($_REQUEST);
$gift_card_obj->restore_pincode_by_session();
$gift_card_obj->remove_pincode_session();
$gift_card_obj->filter_redeemed_pincode();

$sno_arr = $gift_card_obj->getPostData('sno', array());
$pin_arr = $gift_card_obj->getPostData('pcode', array());
$tab = $gift_card_obj->getPostData('tab', 1);        // isset($_GET['tab']) ? (int)$_GET['tab'] : 1;
$qty = $gift_card_obj->getPostData('qty', null);     //isset($_POST['qty']) ? $_POST['qty'] : null;
$game_id = $gift_card_obj->getPostData('game_id');  //isset($_REQUEST['game_id']) ? tep_db_prepare_input($_REQUEST['game_id']) : '';
$product_id = $gift_card_obj->getPostData('product_id');    //isset($_REQUEST['product_id']) ? tep_db_prepare_input($_REQUEST['product_id']) : '';
$game_qty = $gift_card_obj->getPostData('game_qty', 1);      //isset($_REQUEST['game_qty']) ? (int)$_REQUEST['game_qty'] : 1;
$game_dm = $gift_card_obj->getPostData('game_dm', 0);      //isset($_REQUEST['game_dm']) ? (int)$_REQUEST['game_dm'] : 0;
$game_info = $gift_card_obj->getPostData('game_info', null);     //isset($_REQUEST['game_info']) ? $_REQUEST['game_info'] : null;

function gift_card_allowed_customer_groups ($group_id) {
    $return_bool = FALSE;
    
    if (in_array($group_id, array(2,12,3,4,5,15))) {
        $return_bool = TRUE;
    }
    
    return $return_bool;
}

//function get_customer_id_process () {
//    global  $email_acc_type, $email_address, $password, $fname, $lname, $tab, $product_id, $game_qty, $game_dm, 
//            $currency, $currencies, $messageStack, $cart, $order, $credit_covers, $gift_card_obj;
//    $return_int = 0;
//    $error_msg_arr = array();
//    
//    if (tep_not_null($email_address)) {
//        $check_customer_select_sql = "	SELECT customers_id, customers_groups_id
//                                        FROM " . TABLE_CUSTOMERS . " 
//                                        WHERE customers_email_address = '" . tep_db_input($email_address) . "'";
//        $check_customer_query = tep_db_query($check_customer_select_sql);
//        // When email existed
//        if ($email_acc_type === 0 && $customer_info_row = tep_db_fetch_array($check_customer_query)) {
//            // If belongs to allowed user groups
//            if (gift_card_allowed_customer_groups($customer_info_row['customers_groups_id'])) {
//                $login_acc_arr = array(
//                    'email_address' => $email_address,
//                    'password' => $password
//                );
//                
//                $user_obj = new user();
//                $login_response = $user_obj->loginProcess($login_acc_arr);
//                if ($login_response['error'] !== TRUE) {
//                    $return_int = $login_response['customer_id'];
//                } else {
//                    foreach ($login_response['error_info'] as $msg) {
//                        $error_msg_arr[] = $msg;
//                    }
//                }
//                
//                unset($user_obj, $login_response, $login_acc_arr);
//            } else {
//                $error_msg_arr[] = USER_MEMBER_STATUS_NOT_ALLOW;
//            }
//        // When email does not exist
//        } else if ($email_acc_type === 1 && tep_not_null($fname) && tep_not_null($lname)) {
//            $sufficient_credit = FALSE;
//            $auto_login = TRUE;
//            
//            if ($tab == 1) {
//                // Ensure gift card amount sufficient to checkout purchase order before create new OffGamers account.
//                $add_to_cart_resp = gc_add_to_cart($product_id, $game_qty, $game_dm);
//                
//                if ($add_to_cart_resp['error'] === TRUE) {
//                    $cart->reset_mm();
//                    
//                    if (isset($add_to_cart_resp['result']['error']) && defined($add_to_cart_resp['result']['error'])) {
//                        $messageStack->add('gift_card', constant($add_to_cart_resp['result']['error']));
//                    }
//                } else {
//                    $sufficient_credit = TRUE;
//                }
//                
//                unset($add_to_cart_resp);
//            } else {
//                // Does not involve purchasing, redeem gift card only.
//                $sufficient_credit = TRUE;
//                // Redeem GC for new account will not login customer account.
//                $auto_login = FALSE;
//            }
//            
//            if ($sufficient_credit === TRUE) {
//                $new_password = tep_create_random_value(ENTRY_PASSWORD_MIN_LENGTH);
//                $create_acc_arr = array(
//                    'account_email_address' => $email_address,
//                    'password' => $new_password,
//                    'password_confirmation' => $new_password,
//                    'request_reset_password' => true,
//                    'firstname' => $fname,
//                    'lastname' => $lname
//                );
//                
//                $user_obj = new user();
//                $created_response = $user_obj->createAccountProcess($create_acc_arr, $auto_login);
//                $return_int = $created_response['customer_id'];
//                
//                if ($created_response['error'] !== TRUE) {
//                    // Create SC account for user the first time.
//                    $currency_id = $currencies->get_id_by_code($currency);
//                    $store_credit_obj = new store_credit($return_int);
//                    $store_credit_obj->_check_credits_account_exists($currency_id);
//
//                    // Generate token
//                    $access_token_obj = new access_token();
//                    $token = $access_token_obj->getAccessToken($return_int, $email_address.time());
//
//                    // Email user to request reset password.
//                    $email_greeting = tep_get_email_greeting($created_response["firstname"], $created_response["lastname"], $created_response["gender"]);
//                    $email_text = $email_greeting . sprintf(EMAIL_CHANGE_YOUR_PASSWORD_BODY, $email_address, $new_password, tep_href_link(FILENAME_LOGIN, 'action=process&otp=' . $token, 'SSL')) . "\n\n" . EMAIL_FOOTER;
//                    @tep_mail($created_response["firstname"] . ' ' . $created_response["lastname"], $email_address, implode(' ', array(EMAIL_SUBJECT_PREFIX, EMAIL_CHANGE_YOUR_PASSWORD_SUBJECT)), sprintf($email_text, $email_address, $new_password), STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
//
//                    if ($auto_login === TRUE) {
//                        $login_acc_arr = array(
//                            'email_address' => $email_address,
//                            'password' => $new_password
//                        );
//
//                        $login_response = $user_obj->loginProcess($login_acc_arr);
//                        if ($login_response['error'] !== TRUE) {
//                            $return_int = $login_response['customer_id'];
//                        } else {
//                            // Pls check script error.
//                            $return_int = 0;
//
//                            foreach ($login_response['error_info'] as $msg) {
//                                $error_msg_arr[] = $msg;
//                            }
//                        }
//
//                        unset($login_response, $login_acc_arr);
//                    }
//
//                    unset($store_credit_obj, $access_token_obj);
//                } else {
//                    foreach ($created_response['error_info'] as $msg) {
//                        $error_msg_arr[] = $msg;
//                    }
//                }
//
//                unset($user_obj, $created_response);
//            }
//        } else {
//            if ($email_acc_type === 0) {
//                $error_msg_arr[] = sprintf(TEXT_LOGIN_ERROR, '<a href="' . tep_href_link(FILENAME_LOGIN, 'action=forget_password', 'SSL') .  '">' . TEXT_RESET_PASSWORD . '</a>');
//            } else {
//                $error_msg_arr[] = ENTRY_EMAIL_ADDRESS_ERROR_EXISTS;
//            }
//        }
//    } else {
//        $error_msg_arr[] = ENTRY_EMAIL_ADDRESS_ERROR;
//    }
//    
//    return array('cid' => $return_int, 'error' => $error_msg_arr);
//}

function check_b4_add_cart($products_id, $opt = array()) {
    global $cart;
    $return_arr = array();

    $bdn = '';  // unknown
    $buyqty = isset($opt['buyqty']) ? (int)$opt['buyqty'] : 0;
    $extra_info = isset($opt['extra_info']) ? $opt['extra_info'] : array();
    $delivery_mode = isset($opt['delivery_mode']) ? (int)$opt['delivery_mode'] : 0;
    $hla_account_id = isset($opt['hla_account_id']) ? $opt['hla_account_id'] : '';
    
    
    $custom_product_type = $cart->get_custom_prd_type($products_id);

    if ($custom_product_type == 'cdkey' && tep_check_product_region_permission($products_id)) {
        $products_select_sql = "SELECT products_id, products_bundle, products_bundle_dynamic, products_price, products_main_cat_id, custom_products_type_id 
                                FROM " . TABLE_PRODUCTS . "
                                WHERE products_id = '" . $products_id . "'";
        $products_result_sql = tep_db_query($products_select_sql);
        if ($product_info_row = tep_db_fetch_array($products_result_sql)) {
            if ($product_info_row['products_bundle'] == "yes") {
                // static bundle product
                $err1 = '';

                $bundle_select_sql = "	SELECT pd.products_name, pb.*, p.products_bundle, p.products_id, p.products_price 
                                        FROM " . TABLE_PRODUCTS . " AS p 
                                        INNER JOIN " . TABLE_PRODUCTS_DESCRIPTION . " AS pd 
                                            ON p.products_id=pd.products_id
                                        INNER JOIN " . TABLE_PRODUCTS_BUNDLES . " AS pb 
                                            ON pb.subproduct_id=pd.products_id 
                                        WHERE pb.bundle_id = '" . tep_db_input($products_id) . "' AND language_id = '1'";
                $bundle_result_sql = tep_db_query($bundle_select_sql);
                while ($bundle_data = tep_db_fetch_array($bundle_result_sql)) {
                    $product_select_sql = "	SELECT p.products_id, p.products_quantity, p.products_status, p.products_purchase_mode, 
                                                p.products_out_of_stock_level 
                                            FROM " . TABLE_PRODUCTS . " AS p 
                                            WHERE p.products_id = '" . $bundle_data["products_id"] . "'";
                    $product_result_sql = tep_db_query($product_select_sql);
                    if ($product_info = tep_db_fetch_array($product_result_sql)) {
                        if (!$product_info["products_status"] || $product_info["products_purchase_mode"] == '3') {	// Inactive or Out of Stock
                            $err1 = "SL";
                            break;
                        }

                        switch ($product_info["products_purchase_mode"]) {
                            case '1':	// Always Add to Cart
                            case '2':	// Always Pre-Order
                                break;
                            case '4':	// Auto Mode
                                if (tep_not_null($product_info["products_out_of_stock_level"])) {	// If there is setting for out of stock level 
                                    $current_qty_in_cart = $cart->get_mm_total_product_in_cart($product_info["products_id"]);

                                    if ($product_info['products_quantity'] - ($bundle_data['subproduct_qty']*((int)$buyqty) + $current_qty_in_cart) < (int)$product_info["products_out_of_stock_level"]) {
                                        $err1 = "SL";
                                        break 2;
                                    }
                                }
                                break;
                        }
                    }
                }

                if (tep_not_empty($err1)) {
                    $return_arr['error'] = 'TEXT_STOCK_NOT_AVAILABLE';
                } else {
                    if ($custom_product_type == 'cdkey') {
                        $extra_info_array = array();

                        if (count($extra_info)) {
                            $extra_info_array['top_up_info'] = $extra_info;
                        }

                        if ($delivery_mode > 0 ) {
                            $extra_info_array['delivery_mode'] = $delivery_mode;
                        } else {
                            $extra_info_array['delivery_mode'] = 5;
                        }
                    } else {
                        $extra_info_array = $extra_info;
                    }

                    $return_arr = array(
                        'products_id' => $products_id,
                        'qty' => $buyqty,
                        'opt' => array(
                            'attributes' => '',
                            'notify' => true,
                            'array' => '',
                            'bdn' => '',
                            'iscustom' => false,
                            'extra_info_array' => $extra_info_array
                        )
                    );
                }
            } else if ($product_info_row['products_bundle_dynamic'] == "yes") {
                // dynamic bundle product
                $err1 = '';
                $bd_products_array = array();
                $total_bundle_weight = 0;
                $extra_info_array = array();

                if ($custom_product_type == 'cdkey') {
                    if (count($extra_info)) {
                        $extra_info_array['top_up_info'] = $extra_info;
                    }
                    if ($delivery_mode > 0) {
                        $extra_info_array['delivery_mode'] = $delivery_mode;
                    } else {
                        $extra_info_array['delivery_mode'] = 5;
                    }
                }

                $bundle_weight_select_sql = "   SELECT products_bundle_dynamic_qty 
                                                FROM " . TABLE_PRODUCTS . " 
                                                WHERE products_id='".tep_db_input($products_id)."'";
                $bundle_weight_result_sql = tep_db_query($bundle_weight_select_sql);
                $bundle_weight_row = tep_db_fetch_array($bundle_weight_result_sql);
                if ($bundle_weight_row["products_bundle_dynamic_qty"]) {
                    $allowed_weight = $buyqty*$bundle_weight_row["products_bundle_dynamic_qty"] ;

                    $bundle_select_sql = "	SELECT pd.products_name, pb.*, p.products_bundle, p.products_bundle_dynamic, p.products_id, p.products_price 
                                            FROM " . TABLE_PRODUCTS . " AS p 
                                            INNER JOIN " . TABLE_PRODUCTS_DESCRIPTION . " AS pd
                                                ON p.products_id=pd.products_id
                                            INNER JOIN " . TABLE_PRODUCTS_BUNDLES . " AS pb 
                                                ON pb.subproduct_id=pd.products_id 
                                            WHERE pb.bundle_id = '" . tep_db_input($products_id) . "' AND language_id = '1'";
                    $bundle_result_sql = tep_db_query($bundle_select_sql);
                    while ($bundle_data = tep_db_fetch_array($bundle_result_sql)) {
                        // Omit those inactive or has the out of stock purchase mode
                        $product_select_sql = "	SELECT p.products_id, p.products_quantity, p.products_purchase_mode, p.products_out_of_stock_level 
                                                FROM " . TABLE_PRODUCTS . " AS p 
                                                WHERE p.products_id = '" . $bundle_data['products_id'] . "' AND p.products_status = '1' AND p.products_purchase_mode<>3 ";
                        $product_result_sql = tep_db_query($product_select_sql);
                        if ($product_info = tep_db_fetch_array($product_result_sql)) {
                            $temp_val = ${"q_".$product_info['products_id']};  // Missing qty box for each subproduct
                            $bd_products_array[] = array(
                                'id' => $product_info['products_id'], 
                                'bd_products_qty' => $temp_val
                            );

                            $total_bundle_weight += (int)$temp_val * $bundle_data["subproduct_weight"];

                            switch ($product_info["products_purchase_mode"]) {
                                case '1':	// Always Add to Cart
                                case '2':	// Always Pre-Order
                                    break;
                                case '4':	// Auto Mode
                                    if (tep_not_null($product_info["products_out_of_stock_level"])) {	
                                        // If there is setting for out of stock level 
                                        $total_stock = $product_info['products_quantity'] ;

                                        if ($bdn == 'y') {
                                            // exclude current product since it is in $temp_val
                                            $current_qty_in_cart = $cart->get_mm_total_product_in_cart($product_info["products_id"], $products_id);
                                        } else {
                                            $current_qty_in_cart = $cart->get_mm_total_product_in_cart($product_info["products_id"]);
                                        }

                                        $total_order = ($bundle_data["subproduct_qty"] * (int)$temp_val) + $current_qty_in_cart;

                                        if ($total_order && ($total_stock - $total_order < (int)$product_info["products_out_of_stock_level"])) {
                                            $err1 = "SL";
                                            break 2;
                                        }
                                    }
                                    break;
                            }

                            if ($total_bundle_weight && $total_bundle_weight > $allowed_weight) {
                                $err1 = "OW";	// overweight message
                                break;
                            }
                        }
                    }
                    if (!$total_bundle_weight) {
                        $err1 = "ES";	// no selections had been made message
                    } else if ($total_bundle_weight > $allowed_weight) {
                        $err1 = "OW";	// overweight message
                    } else if ($total_bundle_weight < $allowed_weight) {
                        $err1 = "UW";	// underweight message
                    }
                } else {
                    $err1 = "SL";
                }

                if (tep_not_empty($err1)) {
                    $return_arr['error'] = 'TEXT_STOCK_NOT_AVAILABLE';
                } else {
                    if ($bdn == 'y') {
                        $return_arr = array(
                            'products_id' => $products_id,
                            'qty' => $buyqty,
                            'opt' => array(
                                'attributes' => '',
                                'notify' => false,
                                'array' => $bd_products_array,
                                'bdn' => 'y',
                                'iscustom' => false,
                                'extra_info_array' => $extra_info_array
                            )
                        );
                    } else {
                        $return_arr = array(
                            'products_id' => $products_id,
                            'qty' => $buyqty,
                            'opt' => array(
                                'attributes' => '',
                                'notify' => false,
                                'array' => $bd_products_array,
                                'bdn' => '',
                                'iscustom' => false,
                                'extra_info_array' => $extra_info_array
                            )
                        );
                    }
                }
            } else {	
                // single product
                $suggested_amt = 0;
                $prod_inventory_select_sql = "	SELECT products_quantity, products_purchase_mode, products_out_of_stock_level 
                                                FROM " . TABLE_PRODUCTS . " 
                                                WHERE products_id = '" . tep_db_input($products_id) . "' 
                                                    AND products_status=1";
                $prod_inventory_result_sql = tep_db_query($prod_inventory_select_sql);
                if ($prod_inventory_row = tep_db_fetch_array($prod_inventory_result_sql)) {
                    switch($prod_inventory_row['products_purchase_mode']) {
                        case '1':	// Always Add to Cart
                        case '2':	// Always Pre-Order
                            $show_it = 1;
                            break;
                        case '3':	// Always Out of Stock
                            $show_it = 0;
                            break;
                        case '4':	// Auto Mode
                            if (tep_not_null($prod_inventory_row['products_out_of_stock_level'])) {	// If there is setting for out of stock level 
                                $current_qty_in_cart = $cart->get_mm_total_product_in_cart($products_id);

                                if ((int)$prod_inventory_row['products_quantity'] - ($buyqty+$current_qty_in_cart) < (int)$prod_inventory_row["products_out_of_stock_level"]) {
                                    $show_it = 0;
                                } else {
                                    $show_it = 1;
                                }
                            } else {
                                $show_it = 1;
                            }
                            break;
                    }

                    if ($show_it) {
                        $extra_info_array = array();
                        if ($custom_product_type == 'cdkey') {
                            $isCustom = true;
                            $extra_info_array['top_up_info'] = $extra_info;

                            if ($delivery_mode > 0) {
                                $extra_info_array['delivery_mode'] = $delivery_mode;
                            }
                        } else {
                            $isCustom = false;

                            if ($custom_product_type == 'hla') {
                                $extra_info_array = array ('hla_account_id' => $hla_account_id);
                            }
                        }

                        $return_arr = array(
                            'products_id' => $products_id,
                            'qty' => $buyqty,
                            'opt' => array(
                                'attributes' => '',
                                'notify' => true,
                                'array' => '',
                                'bdn' => '',
                                'iscustom' => $isCustom,
                                'extra_info_array' => $extra_info_array
                            )
                        );
                    } else {
                        $return_arr['error'] = 'TEXT_STOCK_NOT_AVAILABLE';
                    }
                }
            }
        } else {
            $return_arr['error'] = 'TEXT_PRODUCT_NOT_FOUND';
        }
    } else {
        $return_arr['error'] = 'TEXT_PRODUCT_NOT_FOUND';
    }

    return $return_arr;
}

function gc_add_to_cart ($product_id, $game_qty, $game_dm, $game_info) {
    global $cart, $temp_cart_holder, $credit_covers, $order;

    if (!count($temp_cart_holder)) {
        $error = FALSE;
        $result = '';
        $cart->reset_mm();
        
        if ($game_dm === 6) {
            if (is_null($game_info) || !isset($game_info['account']) || !tep_not_null($game_info['account'])) {
                $error = TRUE;
                $result['error'] = ERROR_INVALID_TOP_UP_ACCOUNT;
            } else {
                if (isset($game_info['account_2']) && isset($game_info['account'])) {
                    if (trim($game_info['account']) != trim($game_info['account_2'])) {
                        $error = TRUE;
                        $result['error'] = 'ERROR_INVALID_TOP_UP_ACCOUNT';
                    }
                }

                if ($error !== TRUE) {
                    $products_id = $product_id;
                    $qty = $game_qty;

                    $game_info['account'] = trim($game_info['account']);
                    include_once(DIR_WS_CLASSES . 'direct_topup.php');
                    $direct_topup_obj = new direct_topup();

                    $publishers_games_products_id = $products_id;
                    if ($publishers_id = $direct_topup_obj->check_is_supported_by_direct_top_up($products_id)) {
                        $products_select_sql = "SELECT products_bundle, products_bundle_dynamic
                                                FROM " . TABLE_PRODUCTS . "
                                                WHERE products_id = '".$publishers_games_products_id."'";
                        $products_result_sql = tep_db_query($products_select_sql);
                        if ( $products_row = tep_db_fetch_array($products_result_sql)) {
                            if ($products_row['products_bundle'] == 'yes' || $products_row['products_bundle_dynamic'] == 'yes') {
                                $bundle_select_sql = "	SELECT pp.products_id
                                                        FROM " . TABLE_PUBLISHERS_PRODUCTS . " AS pp 
                                                        INNER JOIN " . TABLE_PRODUCTS_DELIVERY_INFO . " AS pdi
                                                            ON pdi.products_id = pp.products_id 
                                                        INNER JOIN " . TABLE_PRODUCTS_BUNDLES . " AS pb
                                                            ON pp.products_id=pb.subproduct_id
                                                        WHERE pb.bundle_id = '".tep_get_prid($publishers_games_products_id)."'
                                                            AND pdi.products_delivery_mode_id = '6'
                                                        LIMIT 1";
                                $bundle_result_sql = tep_db_query($bundle_select_sql);
                                $bundle_row = tep_db_fetch_array($bundle_result_sql);
                                $publishers_games_products_id = $bundle_row['products_id'];
                            }
                        }

                        $games_mapping_select_sql = "	SELECT pg.publishers_game, pg.publishers_games_id 
                                                        FROM " . TABLE_PUBLISHERS_GAMES . " AS pg 
                                                        INNER JOIN " . TABLE_PUBLISHERS_PRODUCTS . " AS pp
                                                            ON pg.publishers_games_id = pp.publishers_games_id
                                                        WHERE pg.publishers_id = '".(int)$publishers_id."'
                                                            AND pp.products_id = '".(int)$publishers_games_products_id."'";
                        $games_mapping_result_sql = tep_db_query($games_mapping_select_sql);
                        $games_mapping_row = tep_db_fetch_array($games_mapping_result_sql);

                        $get_top_up_info_array = $direct_topup_obj->get_top_up_info($publishers_games_products_id);

                        $validate_game_acc_array = array(	'game' => $games_mapping_row['publishers_game'], 
                                                            'publishers_games_id' => $games_mapping_row['publishers_games_id'],
                                                            'product_id' => $products_id,
                                                            'amount_type' => $get_top_up_info_array['amount_type']['top_up_info_value'],
                                                            'amount' => $get_top_up_info_array['amount']['top_up_info_value'],
                                                            'quantity' => $qty
                                                        );
                        if ($direct_topup_obj->validate_game_acc($publishers_id, array_merge($game_info, $validate_game_acc_array), $curl_response_array)) {
                            $prod_cat_path = tep_get_product_path($products_id, true);
                            if (tep_not_null($prod_cat_path)) {
                                $prod_cat_path_array = explode('_', $prod_cat_path);
                                $game_id = $prod_cat_path_array[0];
                            } else {
                                $game_id = 0;
                            }

                            $extra_info_array = array(
                                'delivery_mode' => $game_dm
                            );

                            if (!is_null($game_info)) {
                                $extra_info_array['top_up_info'] = $game_info;
                                update_dtu_game_info('', $game_info, $products_id);
                                $customer_id_conversion = $direct_topup_obj->get_publishers_games_conf($games_mapping_row['publishers_games_id'], 'CONVERT_CUSTOMER_ID_TO_EMAIL_FLAG');
                                if ($customer_id_conversion == '1' && !empty($extra_info_array['top_up_info']['account']) && !is_numeric($extra_info_array['top_up_info']['account'])) {
                                    $extra_info_array['top_up_info']['account'] = $extra_info_array['top_up_info']['account'] . ':~:' . $direct_topup_obj->convert_customer_email_to_id($extra_info_array['top_up_info']['account']);
                                }
                            }
                            
                            $result = $cart->add_mm_cart(
                                $products_id, 
                                $qty, 
                                array('extra_info_array' => $extra_info_array)
                            );
                            
                            if (isset($result['error'])) {
                                // Failed to add cart
                                $error = TRUE;
                            }
//                            $cart->add_cart($products_id, $qty, '', true, '','', true, $extra_info_array);
                        } else {
                            if (isset($curl_response_array['result_code']) && $curl_response_array['result_code'] == '1508') {
                                $error = TRUE;
                                $result['error'] = 'ERROR_DTU_EXCEED_TOP_UP_LIMIT';
                            } else if (isset($curl_response_array['game_acc_status']) && $curl_response_array['game_acc_status'] == 'NOT_RELOADABLE') {
                                $error = TRUE;
                                $result['error'] = 'ERROR_INVALID_TOP_UP_ACCOUNT';
                            } else {
                                $error = TRUE;
                                $result['error'] = 'ERROR_UNABLE_VALIDATE_TOP_UP_ACCOUNT';
                            }
                        }
                    }
                }
            }
        } else {
            $result = check_b4_add_cart($product_id, array('buyqty' => $game_qty, 'delivery_mode' => $game_dm));

            if (isset($result['products_id'])) {
                $result = $cart->add_mm_cart(
                    $result['products_id'], 
                    $result['qty'], 
                    $result['opt']
                );
                
                if (isset($result['error'])) {
                    // Failed to add cart
                    $error = TRUE;
                }
            } else {
                // Failed to validate product
                $error = TRUE;
            }
        }
        
        if ($error !== TRUE) {
            $cart_content = $cart->get_mm_contents();
            $products_arr = $cart->get_products_info($cart_content, false);

            if (!$cart->mm_checkout_permission($products_arr)) {
                $error = TRUE;
                $result['error'] = 'TEXT_STOCK_NOT_AVAILABLE';
            } else {
                if (tep_session_is_registered('credit_covers')) tep_session_unregister('credit_covers');  //ICW ADDED FOR CREDIT CLASS SYSTEM
                if (isset($_SESSION['need_sc_usage_qna']))  unset($_SESSION['need_sc_usage_qna']);
                if (isset($_SESSION['customer_id'])) {
                    $_SESSION['cot_gv'] = 'on'; 
                } else {
                    unset($_SESSION['cot_gv']);
                }
                
                $order = new order('', false, 'gift_card');
                $order->load_order_products($products_arr, 0);
                
                // order total process required gift card object
                $order_total_modules = new order_total;
                $order_total_modules->pre_confirmation_check();
                
                if ($order->info['subtotal'] > 0) {
                    if (isset($credit_covers) && $credit_covers) {
                    } else {
                        $error = TRUE;
                        if (!isset($_SESSION['need_sc_usage_qna'])) {
                            $result['error'] = 'ERROR_NOT_ENOUGH_CREDIT';
                        } else {
                            $result['error'] = 'TEXT_DORMANT_ACCOUNT_QNA_REQUEST';
                        }
                    }
                }
                
                unset($order);
            }
        }
        
        $temp_cart_holder = array('error' => $error, 'result' => $result);
    }
    
    return $temp_cart_holder;
}

if (tep_not_empty($action)) {
    if ($action == 'purchase_gc') {
        $gc_categories_id = 16365; // default
        $pgc_id_arr = array(16347, 16349, 16350, 16351, 16361, 16352, 16354, 16356, 16357, 16353, 16358, 16359, 16360, 16362, 16363, 16364, 16348, 16355, 16365, 19199, 19200);
        
        $_cur_code = '';
        $_sel_sql = "SELECT cur.code 
            FROM " . TABLE_COUNTRIES . " AS c
            INNER JOIN " . TABLE_CURRENCIES . " AS cur 
                ON cur.currencies_id = c.countries_currencies_id 
            WHERE c.countries_id = '" . $_SESSION['country'] . "'";
        $_res_sql = tep_db_query($_sel_sql);
        if ($_row_sql = tep_db_fetch_array($_res_sql)) {
            $_cur_code = $_row_sql['code'];
        }
        
        $pgc_cur['ARS'] = 16347;
        $pgc_cur['AUD'] = 16349;
        $pgc_cur['BRL'] = 16350;
        $pgc_cur['CAD'] = 16351;
        $pgc_cur['CNY'] = 16361;
        $pgc_cur['DKK'] = 16352;
        $pgc_cur['EUR'] = 16354;
        $pgc_cur['HKD'] = 16356;
        $pgc_cur['IDR'] = 16357;
        $pgc_cur['JPY'] = 16353;
        $pgc_cur['MYR'] = 16358;
        $pgc_cur['NOK'] = 16359;
        $pgc_cur['PHP'] = 16360;
        $pgc_cur['SEK'] = 16362;
        $pgc_cur['SGD'] = 16363;
        $pgc_cur['THB'] = 16364;
        $pgc_cur['AED'] = 16348;
        $pgc_cur['GBP'] = 16355;
        $pgc_cur['USD'] = 16365;
        $pgc_cur['KWD'] = 19199;
        $pgc_cur['SAR'] = 19200;
        
        if (!empty($_cur_code) && isset($pgc_cur[$_cur_code]) && (in_array($pgc_cur[$_cur_code], $zone_info_array[1]->zone_categories_id))) {
            $gc_categories_id = $pgc_cur[$_cur_code];
        } else {
            foreach ($pgc_id_arr as $g_id) {
                if (in_array($g_id, $zone_info_array[1]->zone_categories_id)) {
                    $gc_categories_id = $g_id;
                    break;
                }
            }
        }
        
        include_once(DIR_WS_CLASSES . FILENAME_MAIN_PAGE);
        $main_page = new main_page();
        $gc_cPath = $main_page->game_cat_path_by_tpl($gc_categories_id);
        tep_redirect(tep_href_link(FILENAME_DEFAULT, 'cPath=' . $gc_cPath, 'SSL'));
    }
} else if (!is_null($qty) || $gift_card_obj->is_pincode_exist()) {
    $ttl = '0.00';
    $ttl_val = '';
    $ttl_cur = '';
    
    //  -------------------------------------------------------------------------------------------------------------------------------------------- Form Submit
    if (!is_null($qty)) { // Only proceed to checking when submit from form
        // validate input data
        if ($tab == 1 && empty($product_id)) {
            $error = TRUE;
            $messageStack->add('gift_card', TEXT_PRODUCT_NOT_FOUND);
        }
        
        // update pin code info
        $error = !$gift_card_obj->import_pincode($sno_arr, $pin_arr) || $error;
        
        if ($error !== TRUE) {
            // when user is not yet login
            if (!$cid) {
                $gift_card_obj->capture_pincode_by_session();
                $gift_card_obj->capture_purchase_info_by_session();
                
                if ($email_acc_type === 1) {
                    // create new account
                    tep_redirect($shasso_obj->getSignupURL('', array('next_url_ext' => array('proceed_gc_checkout' => 1))));
                } else {
                    // login account
                    tep_redirect($shasso_obj->getLoginURL('', '', array('next_url_ext' => array('proceed_gc_checkout' => 1))));
                }
                
//                $customer_info_arr = get_customer_id_process();
//                $cid = $customer_info_arr['cid'];
//                
//                if ($cid === 0) {
//                    $error = TRUE;
//                    $step3_error_array = $customer_info_arr['error'];
//                }
            // user session existed
            } else {
                if (!gift_card_allowed_customer_groups($_SESSION['customers_groups_id'])) {
                    $error = TRUE;
                    $messageStack->add('gift_card', USER_MEMBER_STATUS_NOT_ALLOW);
                }
            }
        }
    } else {
        //  return error cause checkout process failed to proceed.
        $error = TRUE;  
    }
    //  -------------------------------------------------------------------------------------------------------------------------------------------- Form Submit
    
    if ($gift_card_obj->is_pincode_exist()) {
        $error = !$gift_card_obj->validate_all_gift_cards($cid, $currency) || $error || !$cid;
        
        if ($error !== TRUE) {
            if (tep_not_empty($product_id)) {
                $add_to_cart_resp = gc_add_to_cart($product_id, $game_qty, $game_dm, $game_info);
                
                if ($add_to_cart_resp['error'] === TRUE) {
                    $cart->reset_mm();
                    $error = TRUE;
                    
                    if (isset($add_to_cart_resp['result']['error']) && defined($add_to_cart_resp['result']['error'])) {
                        $messageStack->add('gift_card', constant($add_to_cart_resp['result']['error']));
                    } else {
                        $messageStack->add('gift_card', $add_to_cart_resp['result']['error']);
                    }
                } else {
                    $gift_card_obj->capture_pincode_by_session();
                    // recipient customer id
                    $_SESSION['order_customer_id'] = $cid;
                    tep_redirect(tep_href_link('gc_checkout_process.php', '', 'SSL'));
                }
                
                unset($add_to_cart_resp);
            // Perform redeem when user submit from tab 2
            } else if ($tab === 2) {
                // Redeem GC
                if ($gift_card_obj->redeem_all_gift_cards($cid, $currency)) {
                    $pin_code_info_arr = $gift_card_obj->get_last_pin_code_info();
                    $total_amount = $currencies->format($pin_code_info_arr['total_redeem_gc_deno'], true, $pin_code_info_arr['redeem_gc_currency_code'], 1);
                } else {
                    $error = TRUE;
                }
            }
        }
    }
    
    if ($error !== TRUE) {
        $_SESSION['gift_card_rgc_success']['total_amount'] = $total_amount;
        tep_redirect(tep_href_link(FILENAME_GIFT_CARD, tep_get_all_get_params(array('tab')) . 'tab=' . $tab, 'SSL'));
    } else {
        $ttl_deno = 0;
        $currency_code = '';
        $temp_error_holder = array();
        
        if ($cid) {
            $current_store_credit_arr = store_credit::get_current_credits_balance($cid);
            $redeem_gc_currency_id = isset($gift_card_obj->pin_code_array[0]['result']['redeem_gc_currency_id']) ? $gift_card_obj->pin_code_array[0]['result']['redeem_gc_currency_id'] : '';
            
            if (isset($current_store_credit_arr['sc_currency_id']) && $redeem_gc_currency_id != $current_store_credit_arr['sc_currency_id']) {
                $gift_card_obj->validate_all_gift_cards($cid, $currency);
            }
        }
        
        foreach ($gift_card_obj->pin_code_array as $idx => $data) {
            $result_array = $data['result'];
            $sno = $data['sno'];
            $pin = $data['pin'];
            
            if (isset($result_array['error_key'])) {
                // Invalid GC
                $process_arr['gc'][$idx]['sno'] = $sno;
                $process_arr['gc'][$idx]['pin'] = $pin;
                $process_arr['gc'][$idx]['tbody_class'] = 'highlight';
                $process_arr['gc'][$idx]['msg_class'] = ' gc_fail';
                $process_arr['gc'][$idx]['msg'] = constant($result_array['error_key']);
            } else if (isset($result_array['status']) && $result_array['status'] === 'success_topup') { // Successfully redeemed GC
//                $temp_error_holder[] = '['.$sno.']' . sprintf(SUCCESS_REDEEMED_PARTIAL, $result_array['original_gc_deno_formatted'], $result_array['redeem_gc_deno_formatted']);
            } else {
                // Valid GC but has not been redeemed yet.
                $ttl_deno += $result_array['redeem_gc_deno'];
                $currency_code = $result_array['redeem_gc_currency_code'];
                
                $process_arr['gc'][$idx]['sno'] = $sno;
                $process_arr['gc'][$idx]['pin'] = $pin;
                $process_arr['gc'][$idx]['data-val'] = $result_array['redeem_gc_deno'];
                $process_arr['gc'][$idx]['data-cur'] = $currency_code;
                $process_arr['gc'][$idx]['tbody_class'] = '';
                $process_arr['gc'][$idx]['msg_class'] = ' gc_succ';
                $process_arr['gc'][$idx]['msg'] = sprintf(DF_TEXT_REDEEM_GIFT_CARD_CONFIRMATION, $result_array['original_gc_deno_formatted'], $result_array['redeem_gc_deno_formatted']);
            }
        }
        
        if (count($temp_error_holder)) {
            foreach ($temp_error_holder as $msg) {
                $messageStack->add_session('gift_card', $msg, 'success');
            }
        }
        
        if ($ttl_deno) {
            $ttl = $currencies->format($ttl_deno, true, $currency_code, 1);
            $ttl_val = $ttl_deno;
            $ttl_cur = $currency_code;
        }
        
        if ($process_arr) {
            $process_arr['ttl'] = $ttl;
            $process_arr['ttl_val'] = $ttl_val;
            $process_arr['ttl_cur'] = $ttl_cur;
            $filtered_arr['t'.$tab] = $process_arr;
        }
        
        $cid = isset($_SESSION['customer_id']) ? $_SESSION['customer_id'] : 0;
        // reload customer upkeep if user has just logged in.
        $customers_upkeep_obj->reloadUpkeep($cid);
    }
}

$default_filtered_arr = array(
    'gc' => array('' => array('sno' => '', 'pin' => '', 'tbody_class' => '', 'msg_class' => '', 'msg' => '', 'data-val' => '')),
    'ttl' => '0.00',
    'ttl_val' => '',
    'ttl_cur' => ''
);

if (!isset($filtered_arr['t1'])) {
    $filtered_arr['t1'] = $default_filtered_arr;
}

if (!isset($filtered_arr['t2'])) {
    $filtered_arr['t2'] = $default_filtered_arr;
}

for ($i=1; $i<=20; $i++) {
    $qty_array[] = array('id' => $i, 'text' => $i);
}

if (tep_not_empty($game_id)) {
    $output_arr = $gift_card_obj->get_game_list();
    
    if (isset($output_arr['sorting'])) {
        foreach ($output_arr['sorting'] as $idx) {
            $arr = $output_arr[$idx];
            $game_list_array[] = array('id' => $arr['cPath'], 'text' => $arr['name']);
        }
    }
}

if (tep_not_empty($game_id) && tep_not_empty($product_id)) {
    $output_arr = $gift_card_obj->get_product_list($game_id);
    
    foreach ($output_arr as $arr) {
        $product_list_array[] = array('id' => $arr['id'], 'text' => $arr['text']);
    }
    
    $resp = $gift_card_obj->get_product_details($customer_id, $product_id, $game_qty, $filtered_arr['t1']['ttl_cur'], $filtered_arr['t1']['ttl_val']);
    $pwgc_amount = $resp['formatted_price'];
    
    foreach ($resp['delivery_mode'] as $val => $text) {
        $dm_list_array[] = array('id' => $val, 'text' => $text);
    }
    
    if ($game_dm == 6) {
        $dm_info_array = $gift_card_obj->get_dtu_info($product_id);
    }
}

$content = CONTENT_GIFT_CARD;

require(DIR_WS_TEMPLATES . TEMPLATENAME_MAIN_PAGE);

require(DIR_WS_INCLUDES . 'application_bottom.php');
?>