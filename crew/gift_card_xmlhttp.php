<?php
header("Cache-Control: no-cache");
header("Cache-Control: post-check=0,pre-check=0");
header("Cache-Control: max-age=0");
header("Pragma: no-cache");

require('includes/application_top.php');
require_once(DIR_WS_CLASSES . 'gift_card.php');
include_once(DIR_WS_CLASSES . 'product.php');
require_once(DIR_WS_LANGUAGES . $language . '/' . FILENAME_LOGIN);
include_once(DIR_WS_LANGUAGES . $language . '/' . FILENAME_GIFT_CARD);

if ( !isset($_SERVER['HTTP_REFERER']) ||
	(strstr($_SERVER['HTTP_REFERER'], HTTP_SERVER) === FALSE && strstr($_SERVER['HTTP_REFERER'], HTTPS_SERVER) === FALSE)
	) {
	echo "You are not allowed to access from outside";
	exit;
}

$output_arr = array();
$action = isset($_GET['act']) ? tep_db_prepare_input($_GET['act']) : '';
$serial_no = isset($_REQUEST['sno']) ? tep_db_prepare_input($_REQUEST['sno']) : '';
$pin_code = isset($_REQUEST['pcode']) ? tep_db_prepare_input($_REQUEST['pcode']) : '';
$format_amount = isset($_POST['amt']) ? tep_db_prepare_input($_POST['amt']) : '';
$currency_code = isset($_POST['ccurr']) ? tep_db_prepare_input($_POST['ccurr']) : '';
$cid = isset($_SESSION['customer_id']) ? $_SESSION['customer_id'] : 0;
$email_address = isset($_POST['email']) ? tep_db_prepare_input($_POST['email']) : '';
$cPath = isset($_POST['cPath']) ? tep_db_prepare_input($_POST['cPath']) : '';
$pid = isset($_POST['pid']) ? tep_db_prepare_input($_POST['pid']) : '';
$buyqty = isset($_POST['buyqty']) ? tep_db_prepare_input($_POST['buyqty']) : 0;
$gc_amt = isset($_POST['gc_amt']) ? tep_db_prepare_input($_POST['gc_amt']) : 0;
$gc_curr = isset($_POST['gc_curr']) ? tep_db_prepare_input($_POST['gc_curr']) : '';
$tab_id = isset($_POST['ttype']) ? tep_db_prepare_input(strip_tags($_POST['ttype'])) : '';

switch($action) {
    case 'chk':
        if (tep_not_null($serial_no) && tep_not_null($pin_code)) {
            $gc_obj = new gift_card();
            $return_array = $gc_obj->check_gift_card_info($_REQUEST['sno'], $_REQUEST['pcode'], $cid, $currency);
            
            if ($return_array['status'] !== FALSE) {
                $return_msg = $cid ? DF_TEXT_REDEEM_GIFT_CARD_CONFIRMATION : DF_TEXT_GIFT_CARD_VALUE;
                $output_arr['status'] = 1;
                $output_arr['currency'] = $return_array['redeem_gc_currency_code'];
                $output_arr['amt'] = $return_array['redeem_gc_deno'];
                $output_arr['msg'] = sprintf($return_msg, $return_array['original_gc_deno_formatted'], $return_array['redeem_gc_deno_formatted']);
            } else {
                $output_arr['status'] = 0;
                $output_arr['currency'] = '';
                $output_arr['amt'] = '';
                $output_arr['msg'] = constant($return_array['error_key']);
            }
        }
        
        break;
    case 'format':
        $output_arr = array('famt' => $currencies->format($format_amount, true, $currency_code, 1));
        
        break;
    case 'get_game_list':
        $gc_obj = new gift_card();
        $output_arr = $gc_obj->get_game_list();
        
        break;
    case 'get_product_list':
        $gc_obj = new gift_card();
        $output_arr = $gc_obj->get_product_list($cPath);
        
        break;
    case 'get_product_details':
        $gc_obj = new gift_card();
        $output_arr = $gc_obj->get_product_details($customer_id, $pid, $buyqty, $gc_curr, $gc_amt);
        
        break;
    case 'get_dtu_details':
        $gc_obj = new gift_card();
        $output_arr = $gc_obj->get_dtu_info($pid);
        break;
    case 'get_cfm':
        $response_arr = array(
            'content' => ''
        );
        break;
}

echo json_encode($output_arr);
?>