<?
/*
  	$Id: global_collect_ipn.php, v1.0 2005/11/29 18:08:05
  	Author : <PERSON><PERSON> (<EMAIL>)
  	Title: global_collect_ipn.com Payment Module V1.0
	
	Revisions:
		Version 1.0 Initial Payment Module
	
  	Copyright (c) 2007 SKC Venture
	
  	Released under the GNU General Public License
*/
echo "OK";

require_once('includes/application_top.php');
include_once(DIR_WS_LANGUAGES . $language . '/' . FILENAME_CHECKOUT_PROCESS);

if (isset($_REQUEST) && count($_REQUEST)) {

	$payment = 'global_collect';
    
    require_once(DIR_WS_MODULES . 'payment/global_collect/classes/global_collect_ipn_class.php');
    $global_collect_ipn = new global_collect_ipn($_REQUEST);
    $orders_id = $global_collect_ipn->get_order_id();
    
    include_once(DIR_WS_CLASSES . 'anti_fraud.php');
	include_once(DIR_WS_CLASSES . 'log.php');
    include_once(DIR_WS_CLASSES . 'order.php');
    include_once(DIR_WS_CLASSES . 'payment.php');
    include_once(DIR_WS_CLASSES . 'pgs.php');
	
	if (pgs::getOrderSiteId($orders_id) == '5') {
		pgs::repostToPGS('global_collect', $_POST);
	} else {
		$log_object = new log_files('system');
		$order = new order($orders_id);
		if (isset($order->info['currency'])) $currency = $order->info['currency'];

		$payment_modules = new payment($payment);
		$$payment = new $payment($order->info['payment_methods_id']);

		$global_collect_get_supported_currencies = $$payment->get_support_currencies($$payment->payment_methods_id, false);
		$global_collect_currency = $order->info['currency'];
		if (!in_array($global_collect_currency, $global_collect_get_supported_currencies)) {
			$global_collect_currency = $global_collect_get_supported_currencies[0];
		}

		if (isset($global_collect_ipn->key['EFFORTID']) && (int)$global_collect_ipn->key['EFFORTID'] < 0 && $global_collect_ipn->key['ADDITIONALREFERENCE'] != $orders_id) { // refund

			$store_refund_select_sql = "	SELECT sr.store_refund_amount, o.currency, o.currency_value
											FROM " . TABLE_STORE_REFUND . " AS sr 
											INNER JOIN " . TABLE_ORDERS ." AS o 
												ON o.orders_id = sr.store_refund_trans_id
											WHERE sr.store_refund_id = '".(int)$global_collect_ipn->key['ADDITIONALREFERENCE']."'
												AND o.orders_id = '".(int)$orders_id."'
												AND sr.store_refund_is_processed = '1'";
			$store_refund_result_sql = tep_db_query($store_refund_select_sql);
			if ($store_refund_row = tep_db_fetch_array($store_refund_result_sql)) {
				if ($global_collect_ipn->key['CURRENCYCODE'] == $store_refund_row['currency'] && $global_collect_ipn->key['AMOUNT']== preg_replace('/[^\d]/', '', number_format($currencies->apply_currency_exchange($store_refund_row['store_refund_amount'], $store_refund_row['currency'], $store_refund_row['currency_value']),2))) {
					switch ($global_collect_ipn->key['STATUSID']) {
						case '800':
							$store_refund_data_sql = array(	'store_refund_is_processed' => 0, 
															'store_refund_status' => 3);
							tep_db_perform(TABLE_STORE_REFUND, $store_refund_data_sql, 'update', " store_refund_id = '".(int)$global_collect_ipn->key['ADDITIONALREFERENCE']."' ");

							$store_refund_history_data_sql = array(	'store_refund_id' => (int)$global_collect_ipn->key['ADDITIONALREFERENCE'],
																	'date_added' => 'now()',
																	'store_refund_status' => 3,
																	'payee_notified' => 0,
																	'comments' => tep_db_prepare_input("Refund completed"),
																	'changed_by' => 'system',
																	'changed_by_role' => 'system');
							tep_db_perform(TABLE_STORE_REFUND_HISTORY, $store_refund_history_data_sql);
							break;
						default:
							$store_refund_history_data_sql = array(	'store_refund_id' => (int)$global_collect_ipn->key['ADDITIONALREFERENCE'],
																	'date_added' => 'now()',
																	'payee_notified' => 0,
																	'comments' => tep_db_prepare_input("Unknown refund status (".$global_collect_ipn->key['STATUSID'].")"),
																	'changed_by' => 'system',
																	'changed_by_role' => 'system');
							tep_db_perform(TABLE_STORE_REFUND_HISTORY, $store_refund_history_data_sql);
							break;
					}
				} else {
					$store_refund_data_sql = array(	'store_refund_is_processed' => 0);
					tep_db_perform(TABLE_STORE_REFUND, $store_refund_data_sql, 'update', " store_refund_id = '".(int)$global_collect_ipn->key['ADDITIONALREFERENCE']."' ");

					$store_refund_history_data_sql = array(	'store_refund_id' => (int)$global_collect_ipn->key['ADDITIONALREFERENCE'],
															'date_added' => 'now()',
															'payee_notified' => 0,
															'comments' => "Refund amount not matched, (".$global_collect_ipn->key['CURRENCYCODE'] . " " . ($global_collect_ipn->key['AMOUNT'] / pow(10, 2)) .")",
															'changed_by' => 'system',
															'changed_by_role' => 'system');
					tep_db_perform(TABLE_STORE_REFUND_HISTORY, $store_refund_history_data_sql);
				}
			}
		} else {
			if (in_array($global_collect_ipn->key['STATUSID'], array(600, 800))) {
				if ($order->info['orders_status_id'] != 1) {
					exit;
				} else {
					$cache_key = TABLE_GLOBAL_COLLECT . '/ipn/order_id/' . $orders_id . '/gc_status/' . $global_collect_ipn->key['STATUSID'];
					$cache_result = $memcache_obj->fetch($cache_key);
					if ($cache_result !== FALSE) {	// Not first attempt
						exit;
					} else {
						$memcache_obj->store($cache_key, '1', 300);
					}
				}
			}

			$$payment->get_merchant_account($global_collect_currency);
			if ($global_collect_ipn->validate_receiver_account($$payment->merchantid)) {
				if ($global_collect_ipn->validate_transaction_data($$payment, $orders_id)) {
					if ($global_collect_ipn->authenticate($order, $orders_id, $$payment)) {
						// Order updated to Verifying status
					}
				}
			}

			$gcCurrencies = $global_collect_ipn->key['CURRENCYCODE'];
			$$payment->get_merchant_account($gcCurrencies);

			$gc_return_amt = $global_collect_ipn->key['AMOUNT'];

			$exponent = (int)(tep_not_null($$payment->exponent) ? $$payment->exponent : 2);
			if ($exponent > 0) {
				$gc_return_amt = $gc_return_amt / pow(10, $exponent);
			}

			$global_collect_payment_data_array = array(	'global_collect_payment_reference' => tep_db_prepare_input($global_collect_ipn->key['PAYMENTREFERENCE']),
														'global_collect_additional_reference' => tep_db_prepare_input($global_collect_ipn->key['ADDITIONALREFERENCE']),
														'global_collect_status_id' => tep_db_prepare_input($global_collect_ipn->key['STATUSID']),
														'global_collect_status_date' => tep_db_prepare_input($global_collect_ipn->key['STATUSDATE']),
														'global_collect_currency_code' => tep_db_prepare_input($global_collect_ipn->key['CURRENCYCODE']),
														'global_collect_amount' => number_format($gc_return_amt, $currencies->currencies[$gcCurrencies]['decimal_places'], $currencies->currencies[$gcCurrencies]['decimal_point'], ''),

														'global_collect_effortid' => tep_db_prepare_input($global_collect_ipn->key['EFFORTID']),
														'global_collect_attemptid' => tep_db_prepare_input($global_collect_ipn->key['ATTEMPTID']),
														'global_collect_paymentproductid' => tep_db_prepare_input($global_collect_ipn->key['PAYMENTPRODUCTID']),
														'global_collect_paymentmethodid' => tep_db_prepare_input($global_collect_ipn->key['PAYMENTMETHODID']),
														'global_collect_receiveddate' => tep_db_prepare_input($global_collect_ipn->key['RECEIVEDDATE']),

														'global_collect_cvv_result' => tep_db_prepare_input((isset($global_collect_ipn->key['CVVRESULT'])?$global_collect_ipn->key['CVVRESULT']:'')),
														'global_collect_cc_last_4_digit' => tep_db_prepare_input((isset($global_collect_ipn->key['CCLASTFOURDIGITS'])?$global_collect_ipn->key['CCLASTFOURDIGITS']:'')),
														'global_collect_cc_expiry_date' => tep_db_prepare_input((isset($global_collect_ipn->key['EXPIRYDATE'])?$global_collect_ipn->key['EXPIRYDATE']:''))
														);
			// check order exist
			$global_collect_check_exist_select_sql = "	SELECT global_collect_orders_id 
														FROM " . TABLE_GLOBAL_COLLECT . " 
														WHERE global_collect_orders_id = '".(int)$orders_id."'";
			$global_collect_check_exist_result_sql = tep_db_query($global_collect_check_exist_select_sql);
			if (tep_db_num_rows($global_collect_check_exist_result_sql)) {
				tep_db_perform(TABLE_GLOBAL_COLLECT, $global_collect_payment_data_array, 'update', ' global_collect_orders_id = "'.(int)$orders_id.'" ');
			} else {
				$global_collect_payment_data_array['global_collect_orders_id'] = (int)$orders_id;
				tep_db_perform(TABLE_GLOBAL_COLLECT, $global_collect_payment_data_array);
			}

			//unlock is status 99999 and complete
			if (isset($global_collect_ipn->key['STATUSID']) && (int)$orders_id > 0) {
				switch ($global_collect_ipn->key['STATUSID']) {
					case "99999":
						$refund_update_sql_data_array = array(	'store_refund_is_processed' => 0,
																'store_refund_status' => 3);
						tep_db_perform(TABLE_STORE_REFUND, $refund_update_sql_data_array, 'update', "store_refund_trans_id = '" . (int)$orders_id . "'");

						$store_refund_select_sql = "SELECT store_refund_id
													FROM " . TABLE_STORE_REFUND . " 
													WHERE store_refund_trans_id = '" . (int)$orders_id . "'";
						$store_refund_result_sql = tep_db_query($store_refund_select_sql);
						while ($store_refund_row = tep_db_fetch_array($store_refund_result_sql)) {
							$store_refund_history_data_sql = array(	'store_refund_id' => (int)$store_refund_row['store_refund_id'],
																	'date_added' => 'now()',
																	'store_refund_status' => 3, 
																	'payee_notified' => 0,
																	'comments' => tep_db_prepare_input("Cancel completed"),
																	'changed_by' => 'system',
																	'changed_by_role' => 'system');
							tep_db_perform(TABLE_STORE_REFUND_HISTORY, $store_refund_history_data_sql);						
						}
						break;			
				}
			}
		}
	}
}
?>