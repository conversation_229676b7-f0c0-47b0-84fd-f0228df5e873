<?php
require_once('includes/application_top.php');
require_once(DIR_WS_MODULES . 'payment/googlewallet/classes/googlewallet_ipn_class.php');
require_once(DIR_WS_MODULES . 'payment/googlewallet.php');
require_once(DIR_WS_MODULES . 'payment/googlewallet/classes/JWT.php');
if (isset($_POST) && count($_POST)) {
	if (isset($_POST['jwt']) && !empty($_POST['jwt'])) {
		$paymentObj = new Googlewallet();
		$paymentObj->get_merchant_account(DEFAULT_CURRENCY);
		$decodedInputData = JWT::decode($_POST['jwt'], $paymentObj->secret_word);
		header($_SERVER["SERVER_PROTOCOL"]." 200 OK");
		echo $decodedInputData->response->orderId; //response to google wallet IPN to make the transaction valid, must done within 10 secs
		
		$googlewalletIpnObj = new GooglewalletIpn($decodedInputData);
		$orders_id = $googlewalletIpnObj->get_order_id();
                
		include_once(DIR_WS_CLASSES . 'order.php');
		$order = new order($orders_id);
		$paymentObj = new Googlewallet($order->info['payment_methods_id']);
		$paymentObj->get_merchant_account($googlewalletIpnObj->ipnResponse->request->currencyCode);

		if ((int) $orders_id > 0 && isset($googlewalletIpnObj->ipnResponse->request->name) && !empty($googlewalletIpnObj->ipnResponse->request->name)) {
			if ($googlewalletIpnObj->validate_receiver_account($paymentObj->merchant_id)) {
				$sellerData = json_decode($googlewalletIpnObj->ipnResponse->request->sellerData, 1);
				if ($googlewalletIpnObj->validate_transaction_data($order, $paymentObj, $sellerData['signature'])) { // To ensure the integrity of the data posted back to merchant's server
					if ($googlewalletIpnObj->authenticate($order)) {
						$googlewallet_data_array = array(   'google_wallet_order_id' => tep_db_prepare_input($googlewalletIpnObj->ipnResponse->request->name),
															'google_wallet_merchant_id' => tep_db_prepare_input($googlewalletIpnObj->ipnResponse->aud),
															'google_wallet_create_date' => date('Y-m-d H:i:s', $googlewalletIpnObj->ipnResponse->iat),
															'google_wallet_expired_date' => date('Y-m-d H:i:s', $googlewalletIpnObj->ipnResponse->exp),
															'google_wallet_transaction_id' => tep_db_prepare_input($googlewalletIpnObj->ipnResponse->response->orderId),
															'google_wallet_amount' => tep_db_prepare_input($googlewalletIpnObj->ipnResponse->request->price),
															'google_wallet_currency' => tep_db_prepare_input($googlewalletIpnObj->ipnResponse->request->currencyCode),
															'google_wallet_return_jwt' => $_POST['jwt'],
															'google_wallet_custom_signature' => tep_db_prepare_input($sellerData['signature'])
														);
						$googlewallet_order_select_sql = "	SELECT google_wallet_order_id
															FROM " . TABLE_GOOGLE_WALLET . "
															WHERE google_wallet_order_id = '" . (int) tep_db_input($orders_id) . "'";
						$googlewallet_order_result_sql = tep_db_query($googlewallet_order_select_sql);
						if (tep_db_num_rows($googlewallet_order_result_sql)) {
							tep_db_perform(TABLE_GOOGLE_WALLET, $googlewallet_data_array, 'update', " google_wallet_order_id = '" . (int) $orders_id . "' ");
						} else {
							$googlewallet_data_array['google_wallet_order_id'] = $orders_id;
							tep_db_perform(TABLE_GOOGLE_WALLET, $googlewallet_data_array);
						}
						exit;
					}
				}
			}
		}
	}
}

?>