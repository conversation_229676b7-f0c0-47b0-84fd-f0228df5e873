<?php

/*
  $Id: gv_ipn.php, v1.0 2005/11/29 18:08:05
  Author : <PERSON> (<EMAIL>)
  Title: gudang_voucher.com Payment Module V1.0

  Revisions:
  Version 1.0 Initial Payment Module

  Copyright (c) 2007 SKC Venture

  Released under the GNU General Public License
 */
require_once('includes/application_top.php');

require_once(DIR_WS_MODULES . 'payment/gudang_voucher/classes/gudang_voucher_ipn_class.php');
include(DIR_WS_LANGUAGES . $language . '/' . FILENAME_CHECKOUT_PROCESS);
//Process payment
if (isset($_POST) && count($_POST)) {
    include_once(DIR_WS_FUNCTIONS . 'custom_product_info.php');
    include_once(DIR_WS_CLASSES . 'order.php');
    include_once(DIR_WS_CLASSES . 'anti_fraud.php');
    include_once(DIR_WS_CLASSES . 'log.php');

    /*     * ********************************************************
      (1) Check this order is pay to us
      (2) This is Pending order
     * ******************************************************** */
    $payment = 'gudang_voucher';
    $ipn_data = stripslashes($_POST['data']);
    $post_array = array();
    $xml_array = simplexml_load_string($ipn_data);

    if (!empty($xml_array)) {
        foreach ($xml_array as $key => $value) {
            $post_array[$key] = $value;
        }
        if (isset($xml_array->amount)) {
            foreach ($xml_array->amount as $key => $value) {
                foreach ($value->attributes() as $key2 => $value2)
                    $post_array[$key2] = $value2;
            }
        }
        // No longer need 'amount'. <amount currency="IDR" nominal="150000"/>
        unset($post_array['amount']);
        echo 'OK';
    }

    $custom_data = explode('_', $post_array['custom']);
    $post_array['signature'] = str_replace(" ", "+", urldecode($custom_data[0]));
    $post_array['order_id'] = $custom_data[1];
    unset($custom_data);
    unset($post_array['custom']);

    // load selected payment module
    include_once(DIR_WS_CLASSES . 'payment.php');

    $gudang_voucher_ipn = new gudang_voucher_ipn($post_array);
    $orders_id = $gudang_voucher_ipn->get_order_id();
    $order = new order($orders_id);
    $payment_modules = new payment($payment);
    $$payment = new $payment($order->info['payment_methods_id']);
    $$payment->get_merchant_account($post_array['currency']);

    $log_object = new log_files('system');
    if (isset($gudang_voucher_ipn->key['order_id']) && (int) $gudang_voucher_ipn->key['order_id'] > 0) {
        $gudang_voucher_payment_data_array = array('gudang_voucher_payment_method' => $$payment->payment_methods_id,
            'gudang_voucher_reference_id' => $gudang_voucher_ipn->key['reference'],
            'gudang_voucher_amount' => preg_replace('/[^\d.]/', '', preg_quote($gudang_voucher_ipn->key['nominal'])),
            'gudang_voucher_currency' => $gudang_voucher_ipn->key['currency'],
            'gudang_voucher_remark' => $gudang_voucher_ipn->key['purpose'],
            'gudang_voucher_voucher_code' => $gudang_voucher_ipn->key['voucher_code'],
            'gudang_voucher_tran_status' => $gudang_voucher_ipn->key['status'],
            'gudang_voucher_signature' => $gudang_voucher_ipn->key['signature'],
            'gudang_voucher_tran_datetime' => 'now()'
        );

        $gudang_voucher_payment_select_sql = "	SELECT gudang_voucher_orders_id
                                                FROM " . TABLE_GUDANG_VOUCHER . "
                                                WHERE gudang_voucher_orders_id = '" . (int) $gudang_voucher_ipn->key['order_id'] . "'";
        $gudang_voucher_payment_result_sql = tep_db_query($gudang_voucher_payment_select_sql);
        if (tep_db_num_rows($gudang_voucher_payment_result_sql)) {
            tep_db_perform(TABLE_GUDANG_VOUCHER, $gudang_voucher_payment_data_array, 'update', " gudang_voucher_orders_id = '" . (int) $gudang_voucher_ipn->key['order_id'] . "' ");
        } else {
            $gudang_voucher_payment_data_array['gudang_voucher_orders_id'] = $gudang_voucher_ipn->key['order_id'];
            tep_db_perform(TABLE_GUDANG_VOUCHER, $gudang_voucher_payment_data_array);
        }
    }
    
    //prevent passing demo=1 to checkout live product with demo user
    if ($$payment->test_mode != 'On' && isset($gudang_voucher_ipn->key['development']) && $gudang_voucher_ipn->key['development'] == 'YES') {
        exit;
    }

    if ($gudang_voucher_ipn->key['status'] == 'SUCCESS') {
        if ($gudang_voucher_ipn->validate_receiver_account($$payment->gudang_voucher_merchant_code)) {
            if ($gudang_voucher_ipn->validate_transaction_data($$payment)) { // To ensure the integrity of the data posted back to merchant's server
                $orders_id = $gudang_voucher_ipn->get_order_id();
                if (tep_not_null($orders_id)) {
                    $order = new order($orders_id);
                    if ($order->info['orders_status_id'] == $$payment->order_status) { // If the order still in initial status
                        $gudang_voucher_ipn->authenticate($order, $orders_id, $$payment);
                    }
                }
            }
        }
    }
}
?>