<?
require_once(DIR_WS_CLASSES . 'latest_news.php');

if (isset($_GET['a_aid']) && tep_not_null($_GET['a_aid'])) {
    if (is_numeric($_GET['a_aid'])) {
        $customers_id_select_sql = "	SELECT customers_id
										FROM " . TABLE_CUSTOMERS . "
										WHERE customers_id = '" . (int) $_GET['a_aid'] . "'";
        $customers_id_result_sql = tep_db_query($customers_id_select_sql);
        if (tep_db_num_rows($customers_id_result_sql) > 0) {
            $_SESSION['a_aid'] = $_GET['a_aid'];
        }
    }
}

if (!is_object($lng)) {
    include_once(DIR_WS_CLASSES . 'language.php');
    $lng = new language();
}

define('MENU_COLUMN', 4);

if (ENABLE_SSL == 'true') {
    $path = DIR_WS_IMAGES;
    $lang_btn_path = HTTPS_SERVER . DIR_WS_HTTPS_CATALOG . DIR_WS_LANGUAGES . $language . '/images/buttons/';
} else {
    $path = DIR_WS_IMAGES;
    $lang_btn_path = HTTP_SERVER . DIR_WS_HTTPS_CATALOG . DIR_WS_LANGUAGES . $language . '/images/buttons/';
}

$filename = basename($_SERVER['SCRIPT_FILENAME']);

// Tab Navigation Data Object
if (isset($_GET['menu_id'])) {
    $select_menu_id = $_GET['menu_id'];
} else {
    $selected_menu_filename = ($_SERVER['REQUEST_URI'] != '/' ? $filename : '/');
    $tab_page_select_sql = "	SELECT cms_menu_id
								FROM " . TABLE_CMS_MENU_TAB_PAGE . "
								WHERE cms_linked_filename = '" . tep_db_input($selected_menu_filename) . "'";
    $tab_page_result_sql = tep_db_query($tab_page_select_sql);
    if ($tab_page_row = tep_db_fetch_array($tab_page_result_sql)) {
        $select_menu_id = $tab_page_row['cms_menu_id'];
    }
}

$default_language_info = $lng->get_language_info(DEFAULT_LANGUAGE);
$navigationtab_obj = array();

if (tep_not_null($filename)) {
    switch ($filename) {
        case FILENAME_LOGIN:
        case FILENAME_CREATE_ACCOUNT:
            $sub_menu_1_array = array();
            $sub_menu_2_array = array();
            $navigationtab_obj[] = array('title' => MENU_TITLE_LOGIN_ACCOUNT,
                'url' => FILENAME_LOGIN,
                'menuid' => '',
                'sub_menu_1' => $sub_menu_1_array,
                'sub_menu_size_1' => 130,
                'sub_menu_2' => $sub_menu_2_array,
                'sub_menu_size_2' => 180,
            );
            $navigationtab_obj[] = array('title' => MENU_TITLE_REGISTER_ACCOUNT,
                'url' => FILENAME_CREATE_ACCOUNT,
                'menuid' => '',
                'sub_menu_1' => $sub_menu_1_array,
                'sub_menu_size_1' => 130,
                'sub_menu_2' => $sub_menu_2_array,
                'sub_menu_size_2' => 180,
            );
            break;
        case FILENAME_CHECKOUT_PAYMENT:
        case FILENAME_PAYMENT_CONFIRMATION:
            $sub_menu_1_array = array();
            $sub_menu_2_array = array();
            $navigationtab_obj[] = array('title' => MENU_TITLE_CHECKOUT,
                'url' => FILENAME_CHECKOUT_SHIPPING,
                'menuid' => '',
                'sub_menu_1' => $sub_menu_1_array,
                'sub_menu_size_1' => 130,
                'sub_menu_2' => $sub_menu_2_array,
                'sub_menu_size_2' => 180,
            );
            $navigationtab_obj[] = array('title' => MENU_TITLE_BACK_TO_STORE,
                'url' => '/',
                'menuid' => '',
                'sub_menu_1' => $sub_menu_1_array,
                'sub_menu_size_1' => 130,
                'sub_menu_2' => $sub_menu_2_array,
                'sub_menu_size_2' => 180,
            );
            break;
        default:
            $tab_select_sql = "	SELECT cms_menu_id, cms_menu_content_type, cms_menu_url, cms_menu_seo_alias
								FROM " . TABLE_CMS_MENU . "
								WHERE cms_menu_type = '1'
									AND cms_menu_status = '1'
								ORDER BY cms_menu_sort_order ASC";
            $tab_result_sql = tep_db_query($tab_select_sql);
            while ($tab_row = tep_db_fetch_array($tab_result_sql)) {
                $cms_tab_content_array = array();
                $cms_menu_select_sql = "SELECT cms_menu_lang_setting_key, cms_menu_lang_setting_key_value
										FROM " . TABLE_CMS_MENU_VALUE . "
										WHERE cms_menu_id = '" . tep_db_input($tab_row['cms_menu_id']) . "'
											AND cms_menu_lang_setting_key_value <> ''
											AND (IF (languages_id = '" . (int) $languages_id . "', 1, IF((	SELECT COUNT(cms_menu_id) > 0
																											FROM " . TABLE_CMS_MENU_VALUE . "
																											WHERE cms_menu_id = '" . (int) $tab_row['cms_menu_id'] . "'
																												AND cms_menu_lang_setting_key_value <> ''
																												AND languages_id = '" . (int) $languages_id . "'), 0, languages_id = '" . (int) $default_languages_id . "')))";
                $cms_menu_result_sql = tep_db_query($cms_menu_select_sql);
                while ($cms_menu_row = tep_db_fetch_array($cms_menu_result_sql)) {
                    $cms_tab_content_array[$cms_menu_row['cms_menu_lang_setting_key']] = $cms_menu_row['cms_menu_lang_setting_key_value'];
                }

                if ($tab_row['cms_menu_content_type'] == 'url') {
                    $tempurl = ($tab_row['cms_menu_url'] != 'NOURL' ? tep_href_link($tab_row['cms_menu_url']) : $tab_row['cms_menu_url']);
                } else {
                    if ($tab_row['cms_menu_seo_alias']) {
                        $tempurl = tep_href_link($tab_row['cms_menu_seo_alias'] . '-m-' . $tab_row['cms_menu_id'] . '.ogm', '');
                    } else {
                        $tempurl = tep_href_link(FILENAME_CMS_CONTENT, 'menu_id=' . $tab_row['cms_menu_id']);
                    }
                }

                $sub_menu_1_array = array();
                $sub_menu_2_array = array();
                $sub_menu_data = '';
                $tag_matches = array();

                preg_match('/menuid=["\']?([^"\' ]*)["\' ]/is', $cms_tab_content_array['menu_title'], $tag_matches);

                switch ($tag_matches[1]) {
                    case 'token-store':
                        include_once(DIR_WS_CLASSES . 'search_all_games.php');

                        $platform_array = search_all_games::get_game_all_platform();

                        $sub_menu_1_array['mm_sub1_1'] = array('url' => tep_href_link(FILENAME_SEARCH_ALL_GAMES, 'platform=-999'),
                            'description' => LINK_ALL_PLATFORM);
                        $count_sub_menu = 2;
                        foreach ($platform_array as $platform_id_loop => $platform_data_loop) {
                            $sub_menu_1_array['mm_sub1_' . $count_sub_menu] = array('url' => tep_href_link(FILENAME_SEARCH_ALL_GAMES, 'platform=' . $platform_id_loop),
                                'description' => $platform_data_loop);
                            $count_sub_menu++;
                        }

                        $product_type_array = search_all_games::get_all_product_child_type();

                        $sub_menu_2_array['mm_sub1_1'] = array('url' => tep_href_link(FILENAME_SEARCH_ALL_GAMES, 'product_type=-999'),
                            'description' => LINK_ALL_PRODUCT_TYPE);

                        $count_sub_menu = 2;
                        foreach ($product_type_array as $product_type_id_loop => $product_type_data_loop) {
                            $sub_menu_2_array['mm_sub1_' . $count_sub_menu] = array('url' => tep_href_link(FILENAME_SEARCH_ALL_GAMES, 'product_type=' . $product_type_id_loop),
                                'description' => $product_type_data_loop);
                            $count_sub_menu++;
                        }

                        $navigationtab_obj[] = array('title' => $cms_tab_content_array['menu_title'],
                            'url' => $tempurl,
                            'menuid' => $tab_row['cms_menu_id'],
                            'sub_menu_1' => $sub_menu_2_array,
                            'sub_menu_size_1' => 177,
                            'sub_menu_2' => $sub_menu_1_array,
                            'sub_menu_size_2' => 177,
                            'sub_menu_data' => $sub_menu_data
                        );

                        break;
                    case 'token-express-checkout':
                        $ec_content = ''; // Use in FILENAME_EXPRESS_CHECKOUT
                        include_once(DIR_WS_LANGUAGES . $language . '/' . FILENAME_EXPRESS_CHECKOUT);
                        include_once(FILENAME_EXPRESS_CHECKOUT);

                        $sub_menu_data = $ec_content;

                        $navigationtab_obj[] = array('title' => $cms_tab_content_array['menu_title'],
                            'url' => $tempurl,
                            'menuid' => $tab_row['cms_menu_id'],
                            'sub_menu_data' => $sub_menu_data,
                            'sub_menu_size_1' => 265
                        );

                        break;
                    case 'token-news':
                        $latest_news_obj = new latest_news();
                        $news_category_array = $latest_news_obj->get_latest_news_categories();

                        if (tep_not_empty($news_category_array)) {
                            foreach ($news_category_array as $news_category) {
                                $sub_menu_data .= '	<div id="sub-menu-3" style="padding:0px">
														<div style="padding:7px">
															<a href="' . $news_category['news_url'] . '" class="linkHolder ahd2">
															<span style="" class="linkIcon"></span>' . $news_category['news_groups_name'] . '</a>
													   	</div>
													</div>';
                            }
                        }

                        unset($latest_news_obj);

                        $navigationtab_obj[] = array('title' => $cms_tab_content_array['menu_title'],
                            'url' => $tempurl,
                            'menuid' => $tab_row['cms_menu_id'],
                            'sub_menu_size_1' => 180,
                            'sub_menu_data' => $sub_menu_data
                        );

                        break;
                    case 'token-own-url':
                        $navigationtab_obj[] = array('title' => $cms_tab_content_array['menu_title'],
                            'url' => 'javascript:void(0);',
                            'menuid' => $tab_row['cms_menu_id'],
                            'sub_menu_1' => $sub_menu_1_array,
                            'sub_menu_size_1' => 130,
                            'sub_menu_2' => $sub_menu_2_array,
                            'sub_menu_size_2' => 180,
                            'sub_menu_data' => $sub_menu_data
                        );
                        break;
                    default:
                        $navigationtab_obj[] = array('title' => $cms_tab_content_array['menu_title'],
                            'url' => $tempurl,
//														'class' => $li_class,
                            'menuid' => $tab_row['cms_menu_id'],
                            'sub_menu_1' => $sub_menu_1_array,
                            'sub_menu_size_1' => 130,
                            'sub_menu_2' => $sub_menu_2_array,
                            'sub_menu_size_2' => 180,
                            'sub_menu_data' => $sub_menu_data
                        );
                        break;
                }
            }
    }
}

// Get Shopping Cart Value
$total_cart_item = 0;

if ($cart->count_contents() > 0) {
    $products = $cart->get_products();

    foreach ($products as $products_row) {
        if ($products_row['custom_products_type_id'] == '3') {
            if (isset($products_row['products_bundle']) && $products_row['products_bundle'] == 'yes') {
                $total_cart_item += $products_row['quantity'];
            } else {
                $total_cart_item += 1;
            }
        } else {
            $total_cart_item += $products_row['quantity'];
        }
    }
}

// Geo Zones Object
$country_select_sql = "	SELECT countries_id, countries_name, countries_iso_code_2
						FROM " . TABLE_COUNTRIES . "
						WHERE countries_id = '" . $country . "'";
$country_result_sql = tep_db_query($country_select_sql);
if ($country_row = tep_db_fetch_array($country_result_sql)) {
    $selected_country = $country_row['countries_name'];
    $selected_country_code = $country_row['countries_iso_code_2'];
}

$language_selection_html = '';

if (count($zone_info_array[2]->zone_languages_id)) {
    $language_select_sql = "	SELECT code, name, directory
								FROM " . TABLE_LANGUAGES . "
								WHERE code IN ('" . implode("', '", $zone_info_array[2]->zone_languages_id) . "')
								ORDER BY sort_order ASC";
    $language_result_sql = tep_db_query($language_select_sql);
    for ($lang_cnt = 0; $language_row = tep_db_fetch_array($language_result_sql); $lang_cnt++) {
        if ($lang_cnt > 0)
            $language_selection_html .= ' | ';

        if ($language_row['directory'] == $language) {
            $language_selection_html .= $language_row['name'];
        } else {
            // return false to solve IE 6 browser issue.
            $language_selection_html .= '<a href="javascript:;" onclick="javascript:set_localization_value(\'language\' , \'' . $language_row['code'] . '\' , \'\'); return false;" class="whiteText">' . $language_row['name'] . '</a>';
        }
    }
}

// Currencies Object
$currencies_array = array();
foreach ($zone_info_array[3]->zone_currency_id as $cur_code_in_loop) {
    if (isset($currencies->currencies[$cur_code_in_loop])) {
        $currencies_array[] = array('id' => $cur_code_in_loop, 'text' => $currencies->currencies[$cur_code_in_loop]['title'] . ' (' . $currencies->currencies[$cur_code_in_loop]['symbol_left'] . $currencies->currencies[$cur_code_in_loop]['symbol_right'] . ')');
    }
}

if (tep_session_is_registered('customer_id')) {
    $customer_sc_array = store_credit::get_current_credits_balance($customer_id);

    if (count($customer_sc_array) > 0) {
        $total_store_credit = ($customer_sc_array['sc_reverse'] + $customer_sc_array['sc_irreverse']) - ($customer_sc_array['sc_reverse_reserve_amt'] + $customer_sc_array['sc_irreverse_reserve_amt']);
        $sc_currency_code = $currencies->get_code_by_id($customer_sc_array['sc_currency_id']);
    }

    $email_verified_sql = "	SELECT civ.serial_number, civ.info_verified
							FROM " . TABLE_CUSTOMERS . " AS c
							LEFT JOIN " . TABLE_CUSTOMERS_INFO_VERIFICATION . " AS civ
								ON (c.customers_id = civ.customers_id AND c.customers_email_address = civ.customers_info_value)
							WHERE c.customers_id ='" . (int) $_SESSION['customer_id'] . "'";
    $email_verified_result_sql = tep_db_query($email_verified_sql);
    $email_verified_row = tep_db_fetch_array($email_verified_result_sql);
    $user_credit_balance = tep_user_balance($_SESSION['customer_id'], 'customers');

    $member_status = tep_get_customers_groups_name();
    $current_balance = $currencies->currencies['USD']['symbol_left'] . ' ' . number_format($user_credit_balance['USD'], 2, '.', ',');
    $printStoreCredit = (count($customer_sc_array) > 0) ? $currencies->format($total_store_credit, true, $sc_currency_code, 1) : '0.00';
    $printStorePoint = (int) store_point::get_current_points_balance($_SESSION['customer_id']);

    if (count($customer_sc_array) > 0) {
        $printStoreCreditLink = tep_href_link(FILENAME_ACCOUNT_STORE_CREDIT, '', 'NONSSL');
        $user_has_sc_acct = true;
    } else {
        $printStoreCreditLink = tep_href_link(FILENAME_ACCOUNT_STORE_CREDIT_TOPUP, '', 'NONSSL');
        $user_has_sc_acct = false;
    }
}
?>
<div id="headTop">
    <div class="bShodow"></div>
    <div id="topLogo" class="lfloat">
        <a class="useBlk <?php echo $enable_event_effect; ?>" title="Home" href="<?= tep_href_link('/') ?>"></a>
    </div>
    <div id="topRg">
        <div id="lfHolder" class="lfloat"><?= $ogm_fb_obj->get_FB_button('iframe_like') ?></div>
        <div id="rgHolder" class="rfloat lyr565">
            <div id="sBox" class="sBox">
                <input id="sText" class="sText" type="text" value="<?= ALL_PRODUCTS_LINK ?>" title="<?= ALL_PRODUCTS_LINK ?>" onfocus="if (this.value == this.title)
                            this.value = ''" onblur="if (this.value == '')
                            this.value = this.title">
                <ul id="sResult"></ul>
                <div id="sClear"></div>
            </div>
        </div>
    </div>
</div>
<div id="headNav"><?= $page_obj->get_html_menu($navigationtab_obj) ?></div>
<script type="text/javascript">
                    jQuery(function() {
                        jQuery(".ogmMenu").megamenu();
                    });

                    function sUpdateResult(pass_keyword) {
                        var keyword = jQuery('#sText').val();
                        var cancel_logo = 'url("images/icons/cancel-round.png")';

                        if (keyword.length >= 1 && pass_keyword == keyword) {
                            jQuery("#sClear").css('background-image', 'url("images/icons/loading_16X16.gif")');
                            var sResultAjax = jQuery.ajax({
                                type: "GET",
                                url: "advance_search_xmlhttp.php",
                                data: "keyword=" + keyword + "&languages_id=<?= (int) $languages_id ?>",
                                dataType: "json",
                                success: function(data) {
                                    jQuery("#sClear").css('background-image', cancel_logo);

                                    if (keyword == jQuery('#sText').val()) {
                                        jQuery("#sResult").html('');
                                        if (data.length == 0) {
                                            jQuery("#sResult").append('<li class="sResultLiNone"><div class="sGames"><?= TEXT_INFO_SEARCH_NO_PRODUCT_FOUND ?></div></li>');
                                            jQuery("#sResult").append('<li><div id="sBottom"></div></li>');
                                        } else {
                                            jQuery.each(data, function(key, val) {
                                                jQuery("#sResult").append('<li class="sResultLi"><div class="sGames" onclick="window.location.href=\'<?= tep_href_link(FILENAME_SEARCH_ALL_GAMES) ?>?gid=' + val.id + '\'"><font>' + val.name + '</font></div></li>')
                                            });
                                            jQuery("#sResult").append('<li><div id="sBottom"><?= tep_image_button2("gray_short", "javascript:gotoSearchAllGames(jQuery(\'#sText\').val());", TEXT_INFO_BROWSE_ALL_RESULTS, 150) ?></div></li>');
                                        }
                                        showSearchResult();
                                    }
                                }
                            });
                        } else if (keyword.length >= 1) {
                            jQuery("#sClear").show();
                        } else {
                            jQuery("#sClear").css('background-image', cancel_logo);
                            hideSearchResultNow();
                        }
                    }

                    jQuery('#sText').keyup(function() {
                        jQuery("#sClear").css('background-image', 'url("images/icons/loading_16X16.gif")');
                        current_keyword = jQuery('#sText').val().replace(/([\\'"])/g, "\\$1");
                        setTimeout("sUpdateResult('" + current_keyword + "')", 1000);
                    });

                    function hideSearchResult() {
                        setTimeout("hideSearchResultNow()", 1000);
                    }

                    function hideSearchResultNow() {
                        jQuery("#sBox").removeClass('sBoxSelected');
                        jQuery("#sResult").hide();
                    }

                    function showSearchResult() {
                        jQuery("#sBox").addClass('sBoxSelected');
                        jQuery("#sResult").show();

                        var keyword = jQuery('#sText').val();
                        if (keyword.length >= 1) {
                            jQuery("#sClear").show();
                        }
                    }

                    function gotoSearchAllGames(pass_keyword) {
                        document.location.href = "<?= tep_href_link(FILENAME_SEARCH_ALL_GAMES) ?>?gKeyword=" + pass_keyword;
                    }

                    jQuery("#sClear").click(function() {
                        jQuery("#sText").val('');
                        jQuery("#sText").focus();
                        jQuery("#sClear").hide();
                        hideSearchResultNow();
                    });

                    jQuery("#sText").focus(function() {
                        if (jQuery(this).val().length >= 1) {
                            showSearchResult();
                        } else {
                            hideSearchResult();
                        }
                    });

                    jQuery("#sText").blur(function() {
                        hideSearchResult();
                        if (jQuery(this).val().length < 1) {
                            jQuery("#sClear").hide();
                        }
                    });
</script>