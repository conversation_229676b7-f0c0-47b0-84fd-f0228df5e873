<?php
require('includes/application_top.php');

require_once(DIR_WS_CLASSES . 'page.php');
$page_obj = new page();

$curl_action = isset($_REQUEST['curl_action']) ? $_REQUEST['curl_action'] : '';
$ext_param = isset($_REQUEST['ext_param']) ? $_REQUEST['ext_param'] : '';
$ext_param2 = isset($_REQUEST['ext_param2']) ? $_REQUEST['ext_param2'] : '';
$content_array = array();

if (tep_not_null($curl_action)) {
	switch ($curl_action) {
		case 'get_ogm_layout' :
			ob_start();
				include_once('header.php');
?>
				<link rel="stylesheet" href="<?=DIR_WS_IMAGES?>css/mainpage.css" type="text/css" media="screen" />
				<script type="text/javascript" src="<?=DIR_WS_JAVASCRIPT . 'layout_ogm2011.js'?>"></script>
				<script src="<?=DIR_WS_JAVASCRIPT?>jquery.megamenu.js" type="text/javascript"></script>
				<style>
					#sBox {display: none;}
					#footerBar>table {width:1012px;}
				</style>
				<div id="fancy_box" class="fancy_box" style="display:none;">
					<div class="fancy_inner" style="display: block;">
						<div id="fancy_close" class="fancy_close" style="display: none;"></div>
						<div class="fancy_frame_bg">
							<div class="fancy_bg fancy_bg_n"></div>
							<div class="fancy_bg fancy_bg_ne"></div>
							<div class="fancy_bg fancy_bg_e"></div>
							<div class="fancy_bg fancy_bg_se"></div>
							<div class="fancy_bg fancy_bg_s"></div>
							<div class="fancy_bg fancy_bg_sw"></div>
							<div class="fancy_bg fancy_bg_w"></div>
							<div class="fancy_bg fancy_bg_nw"></div>
						</div>
						<div id="fancy_content" class="fancy_content"></div>
					</div>
				</div>
				
<?php
				$content_array['header'] = ob_get_contents();
			ob_end_clean();
		
			ob_start();
				echo TEXT_COPYRIGHT_INFORMATION;
				$content_array['footer'] = ob_get_contents();
			ob_end_clean();
			
			ob_start();
				require('includes/footer_bar.php');
				echo '<div id="OGM_hiddenContainer">';
				include_once(DIR_WS_INCLUDES . 'hidden_content.php');
				echo '<div id="fancy_box_Bg" class="theLayerBg" style="display:none;"></div></div>';
?>
				<!-- FB API required codes - I have to be here before loading the FB JS SDK -->
				<div id="fb-root"></div>
				<!-- FB API required codes -->
				<script language="javascript">
					<!--
					config = {	cookies_enabled: "<?php echo $cookies_started ? '1' : '1'; ?>",
								defaultCountry: "<?=$country?>",
								special_notice_cookies_enabled: "1", //<?php echo $special_notice_cookies_started ? '1' : '0'; ?>
								language_code: "<?php echo $language_code; ?>",
								generateRegionalBox_ajaj_url: "<?php echo '/proxy.php?'. tep_get_all_get_params().'type=json&file=prestart_json'; ?>",
								generateRegionalBox_ajax_url: "<?php echo '/proxy.php?'. tep_get_all_get_params().'type=xml&file=prestart_xmlhttp'; ?>",
								regionalFormAction: "", //will refresh on the kb index.php
								loginFormAction: "<?php echo tep_href_link(FILENAME_LOGIN); ?>",
								actionSave: "<?php echo TEXT_SAVE; ?>",
								actionForgotPassword: "<b><?php echo LOGIN_BOX_PASSWORD_FORGOTTEN; ?></b>",
								actionContinue: "<?php echo IMAGE_BUTTON_CONTINUE; ?>",
								loadingText: "<?php echo TEXT_IS_LOADING; ?>",
								regionalTitle: "<?php echo BOX_HEADING_REGIONAL_SETTING; ?>",
								regionalNote: "<?php echo TEXT_REGIONAL_NOTE; ?>",
								loginTitle: "<?php echo LOGIN_BOX_HEADING; ?>",
								entryCountry: "<?php echo TEXT_REGIONAL_COUNTRY; ?>",
								entryCurrency: "<?php echo TEXT_CURRENCY.':'; ?>",
								entryLanguage: "<?php echo TEXT_LANGUAGE.':'; ?>",
								entryEmail: "<?php echo ENTRY_EMAIL_ADDRESS; ?>",
								entryPassword: "<?php echo ENTRY_PASSWORD; ?>",
								loginSelection: "<?php echo LOGIN_BOX_LOGIN_SELECTION; ?>",
								newCustomerSelection: "<?php echo LOGIN_BOX_CREATE_ACCOUNT_SELECTION; ?>",
								forgotPasswordSelection: "<?php echo LOGIN_BOX_SEND_PASSWORD_SELECTION; ?>"
							};
					<?=$ogm_fb_obj->get_FB_js_controller()?>
					//-->
				</script>
				<script src="//translate.google.com/translate_a/element.js?cb=googleTranslateElementInit"></script>
<?
				$content_array['footer_bar'] = ob_get_contents();
			ob_end_clean();
			
			$content_array['language'] = $language;
			$content_array['languages_id'] = $languages_id;
			$content_array['language_code'] = $language_code;
			$content_array['currency'] = $currency;
			$content_array['loc_country'] = $country;
			$content_array['cookie_domain'] = $cookie_domain;
			$content_array['cookie_path'] = $cookie_path;
			$content_array['js_file'] = DIR_WS_JAVASCRIPT.FILENAME_JS_JQUERY_IEPNGHACK;            
            $content_array['css_file'] = '<link rel="stylesheet" type="text/css" href="'.(DIR_WS_IMAGES . 'css/'.(isset($language_code) && tep_not_null($language_code) ? 'stylesheet_'.$language_code.'.css' : 'stylesheet_en.css')).'">';
		break;
	case 'get_ogm_fb_cookie_name' : // This will be used for FB Graph API
		if (tep_not_null($ext_param)) {
			$content_array[$ext_param] = 'fbs_' . $ogm_fb_obj->getAppId();
		}
		break;
	case 'insert_search_key' :
		if (tep_not_null($ext_param) && tep_not_null($ext_param2)) {
			tep_record_search_key('KB:'.$ext_param, '', '', $ext_param2);
		}
		break;
	case 'get_ogm_cfg_by_key' :	// Current version not using this
		if (tep_not_null($ext_param)) {
			if (defined($ext_param)) {
				$content_array[$ext_param] = constant($ext_param);
			}
		}
		break;
	}
}
	echo serialize($content_array);
?>