<?
header("Cache-Control: no-cache");
header("Cache-Control: post-check=0,pre-check=0");
header("Cache-Control: max-age=0");
header("Pragma: no-cache");

header('Content-Type: text/xml');

require_once('includes/configure.php');
require(DIR_WS_INCLUDES . 'filenames.php');
require(DIR_WS_INCLUDES . 'database_tables.php');
require(DIR_WS_FUNCTIONS . 'database.php');
tep_db_connect() or die('Unable to connect to database server!');

$configuration_query = tep_db_query('select configuration_key as cfgKey, configuration_value as cfgValue from ' . TABLE_CONFIGURATION);
while ($configuration = tep_db_fetch_array($configuration_query)) {
	define($configuration['cfgKey'], $configuration['cfgValue']);
}

require(DIR_WS_FUNCTIONS . 'general.php');
require(DIR_WS_FUNCTIONS . 'html_output.php');

// include caching class
require(DIR_WS_CLASSES . 'cache_abstract.php');
require(DIR_WS_CLASSES . 'memcache.php');
$memcache_obj = new OGM_Cache_MemCache();

// include amazon web service class
require_once(DIR_WS_CLASSES . 'ogm_amazon_ws.php');
$aws_obj = new ogm_amazon_ws();

echo '<response>';
if (tep_not_null($action)) {
	
	if ( !isset($_SERVER['HTTP_REFERER']) ||
		(strstr($_SERVER['HTTP_REFERER'], HTTP_SERVER) === FALSE && strstr($_SERVER['HTTP_REFERER'], HTTPS_SERVER) === FALSE)
		) {
		echo "You are not allowed to access from outside";
		echo '</response>';
		exit;
	}

	switch($action) {
		case 'hla_alternate_char':
			if (isset($_GET['hla_account_id']) && isset($_GET['ref_id'])) {
				$products_hla_id = $_GET['hla_account_id'];
				$ref_id = $_GET['ref_id'];
				
				$default_hla_language_id = '1';
				$hla_product_html = '';
				
				$get_product_select_sql = "	SELECT acc.seller_id, acc.products_id, acc.products_hla_id, acc.products_price, cha.products_ref_id, cha.products_hla_characters_id, des.products_hla_characters_name, des.products_hla_characters_description 
											FROM " . TABLE_PRODUCTS_HLA . " AS acc 
											INNER JOIN " . TABLE_PRODUCTS_HLA_CHARACTERS . " AS cha 
												ON (acc.products_hla_id = cha.products_hla_id) 
											LEFT JOIN " . TABLE_PRODUCTS_HLA_DESCRIPTION . " AS des 
												ON (cha.products_hla_characters_id = des.products_hla_characters_id) 
											WHERE acc.products_hla_id = '".tep_db_input($products_hla_id)."' 
											AND des.language_id = '".tep_db_input($default_hla_language_id)."' 
											AND acc.products_status = '1' 
											AND acc.products_display = '1' 
											AND cha.products_ref_id <> '".tep_db_input($ref_id)."'
											ORDER BY acc.products_hla_id";
				$get_product_result_sql = tep_db_query($get_product_select_sql);
				$get_product_num = tep_db_num_rows($get_product_result_sql);
			    while ($get_product_row = tep_db_fetch_array($get_product_result_sql)) {
			    	$attr_info_arr = array();
			    	$race_icon = $class_icon = $faction_icon = '';
			    	
			    	$game_name = tep_hla_game_name($get_product_row['seller_id'], $get_product_row['products_id']);
			    	
			    	$get_attr_select_sql = "SELECT products_hla_attributes_type, products_hla_value 
											FROM " . TABLE_PRODUCTS_HLA_ATTRIBUTES . " 
											WHERE products_hla_characters_id = '".tep_db_input($get_product_row['products_hla_characters_id'])."' 
											AND language_id = '".tep_db_input($default_hla_language_id)."'";
					$get_attr_result_sql = tep_db_query($get_attr_select_sql);
					while ($get_attr_row = tep_db_fetch_array($get_attr_result_sql)) {
						$attr_info_arr[$get_attr_row['products_hla_attributes_type']] = $get_attr_row['products_hla_value'];
					}
					
					if (isset($attr_info_arr['race']) && tep_not_null($attr_info_arr['race'])) {
						$race = str_replace(" ", "", $attr_info_arr['race']);
						$race_icon_name = strtolower($race .'_'.$attr_info_arr['gender'].'.gif');
						
						if ($aws_obj->is_aws_s3_enabled()) {
							if ($aws_obj->is_image_exists($race_icon_name, 'hla/'.$game_name.'/race/', 'BUCKET_STATIC')) {
								$race_icon = tep_image($aws_obj->get_image_url_by_instance(), $attr_info_arr['race'], '18', '18');
							} else {
								$race_icon = $attr_info_arr['race'];
							}
						} else {
							$icon_path = file_exists(DIR_FS_HLA.'hla/'.$game_name.'/race/'.$race_icon_name) ? DIR_WS_HLA.'hla/'.$game_name.'/race/'.$race_icon_name : $attr_info_arr['race'];
							$race_icon = tep_image($icon_path, $attr_info_arr['race'], '18', '18');
						}
						
						$race_id = $race.'_'.$get_product_row['products_hla_characters_id'];
						$race_icon = '<span id="'.$race_id.'" onMouseOver="blackbox_tooltip(\''.$attr_info_arr['race'].'\', \''.$race_id.'\');" onMouseOut="hide_blackbox_tooltip();">'.$race_icon.'</span>';
					}
					
					if (isset($attr_info_arr['class']) && tep_not_null($attr_info_arr['class'])) {
						$class = str_replace(" ", "", $attr_info_arr['class']);
						$class_icon_name = strtolower($class.'.gif');
						
						if ($aws_obj->is_aws_s3_enabled()) {
							if ($aws_obj->is_image_exists($class_icon_name, 'hla/'.$game_name.'/class/', 'BUCKET_STATIC')) {
								$class_icon = tep_image($aws_obj->get_image_url_by_instance(), $attr_info_arr['class'], '18', '18');
							} else {
								$class_icon = $attr_info_arr['class'];
							}
						} else {
							$icon_path = file_exists(DIR_FS_HLA.'hla/'.$game_name.'/class/'.$class_icon_name) ? DIR_WS_HLA.'hla/'.$game_name.'/class/'.$class_icon_name : $attr_info_arr['class'];
							$class_icon = tep_image($icon_path, $attr_info_arr['class'], '18', '18');
						}
						
						$class_id = $class.'_'.$get_product_row['products_hla_characters_id'];
						$class_icon = '<span id="'.$class_id.'" onMouseOver="blackbox_tooltip(\''.$attr_info_arr['class'].'\', \''.$class_id.'\');" onMouseOut="hide_blackbox_tooltip();">'.$class_icon.'</span>';
					}
					
					if (isset($attr_info_arr['side']) && tep_not_null($attr_info_arr['side'])) {
						$faction = str_replace(" ", "", $attr_info_arr['side']);
						$faction_icon_name = strtolower($faction.'.gif');
						
						if ($aws_obj->is_aws_s3_enabled()) {
							if ($aws_obj->is_image_exists($faction_icon_name, 'hla/'.$game_name.'/faction/', 'BUCKET_STATIC')) {
								$faction_icon = tep_image($aws_obj->get_image_url_by_instance(), $attr_info_arr['side'], '18', '18');
							} else {
								$faction_icon = $attr_info_arr['side'];
							}
						} else {
							$icon_path = file_exists(DIR_FS_HLA.'hla/'.$game_name.'/faction/'.$faction_icon_name) ? DIR_WS_HLA.'hla/'.$game_name.'/faction/'.$faction_icon_name : $attr_info_arr['side'];
							$faction_icon = tep_image($icon_path, $attr_info_arr['side'], '18', '18');
						}
						
						$faction_id = $faction.'_'.$get_product_row['products_hla_characters_id'];
						$faction_icon = '<span id="'.$faction_id.'" onMouseOver="blackbox_tooltip(\''.$attr_info_arr['side'].'\', \''.$faction_id.'\');" onMouseOut="hide_blackbox_tooltip();">'.$faction_icon.'</span>';
					}
					
					$country = isset($attr_info_arr['country']) && tep_not_null($attr_info_arr['country']) ? $attr_info_arr['country'] : 'N/A';
					
					echo '<alternate_char>';
					echo '<level><![CDATA['.$attr_info_arr['level'].']]></level>';
					echo '<race_icon><![CDATA['.$race_icon.']]></race_icon>';
					echo '<class_icon><![CDATA['.$class_icon.']]></class_icon>';
					echo '<faction_icon><![CDATA['.$faction_icon.']]></faction_icon>';
					echo '<country><![CDATA['.$country.']]></country>';
					echo '<products_ref_id><![CDATA['.$get_product_row['products_ref_id'].']]></products_ref_id>';
					echo '<products_char_id><![CDATA['.$get_product_row['products_hla_characters_id'].']]></products_char_id>';
					echo '<characters_name><![CDATA['.str_replace(' ', '_', $get_product_row['products_hla_characters_name']).']]></characters_name>';
					echo '<characters_description><![CDATA['.$get_product_row['products_hla_characters_description'].']]></characters_description>';
					echo '</alternate_char>';
			    }
			    
			    if ($get_product_num > 0) {
			    	echo '<result><![CDATA['.$get_product_num.']]></result>';
			    } else {
			    	echo '<result><![CDATA[0]]></result>';
			    }
			}
			break;
		
		default:
			echo "<result>Unknown request!</result>";
			break;
	}
}

echo '</response>';
?>