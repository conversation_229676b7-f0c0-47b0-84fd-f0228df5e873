#other_pm_nav, #other_pm_nav ul { /* all lists */
	padding: 0;
	margin: 0;
	list-style: none;
	line-height: 2;
}

#other_pm_nav li { /* 1st lists */
	/* border: 1px solid #336699;*/
}

#other_pm_nav a {
	display: block;
	width: 10em;
	color: #000000;
}

#other_pm_nav li:hover {
	background-color: #e9eff0;
}

#other_pm_nav a:hover { /* for IE hover effect */
	background-color: #e9eff0;
	width: 100%;
}

#other_pm_nav li { /* all list items */
	float: left;
	width: 10em; /* width needed or else Opera goes nuts */
}

#other_pm_nav li ul { /* second-level lists */
	position: absolute;
	background: #FFFFFF;
	width: 10em;
	left: -999em; /* using left instead of display to hide menus because display: none isn't read by screen readers */
	border: 1px solid #336699;
}

#other_pm_nav li ul ul { /* third-and-above-level lists */
	margin: -22 0 0 9.8em;
	width: 5em;
}

#other_pm_nav li ul ul li { /* third-and-above-level lists */
	width: 100%;
}

#other_pm_nav li:hover ul ul, #other_pm_nav li:hover ul ul ul, #other_pm_nav li.sfhover ul ul ul {
	left: -999em;
}

#other_pm_nav li.sfhover ul ul, #other_pm_nav li.sfhover ul li {
	left: -999em;
}

#other_pm_nav li:hover ul, 
#other_pm_nav li li:hover ul, 
#other_pm_nav li li li:hover ul, 
#other_pm_nav li.sfhover ul, 
#other_pm_nav li li.sfhover ul, 
#other_pm_nav li li li.sfhover ul { /* lists nested under hovered list items */
	left: auto;
}

#other_pm_nav li li.sfhover ul { /* lists nested under hovered list items */
	background: #F3F3F3;
}
