.v2LatestNewsBox {
	background: url('../infobox/announcement_center.gif') repeat-y top center;
}

SELECT.gameSelection {
	font-family: Verdana, Arial, Helvetica, sans-serif;
	font-size: 15px;
}

td.gameSelectionLabel {
	font-family: Verdana, Arial, Helvetica, sans-serif;
	font-size: 18px;
	font-weight: bold;
}

td.relatedLinkHeading {
	font-family: Verdana, Arial, Helvetica, sans-serif;
	font-size: 14px;
	font-weight: bold;
}

td.message {
	font-family: Verdana, Arial, Helvetica, sans-serif;
	font-size: 11px;
	color: #777777;
}

td.entryTitle, td.infoTitle {
	font-family: Verdana, Arial, Helvetica, sans-serif;
	font-size: 11px;
	font-weight: bold;
}

td.inputLabelNew {
	font-weight: bold;
	font-family: Arial;
	font-size: 11px;
	color: #666666;
}

td.infoText, td.infoListing, span.infoText {
	font-family: Verdana, Arial, Helvetica, sans-serif;
	font-size: 11px;
}

td.tableTitle {
	font-family: Verdana, Arial, Helvetica, sans-serif;
	font-size: 14px;
	font-weight: bold;
}

ul#tab{
	position: relative;
	width: 525px;
	margin: 0 20px;
	height: 23.5px;
	list-style-type: none;
	overflow: hidden;
}

table.newInputBox {
	background-color: #ebebeb;
}

p a { color: #666; text-decoration: none; font: 11px/1.5 tahoma, arial, verdana, sans-serif; }
p#newsContent a { text-decoration: none; font: 12px/1.5 tahoma, arial, verdana, sans-serif; }
p a em { font-style: normal; border-bottom: 1px solid #999; }
p a:hover em { color: #333; border-bottom: 0; }

#tabbox_top {
	margin: margin: -1px 0px -1px;
	padding: 0;
}

#tabbox_top, {
	margin: -9px 0px -5px;
	padding: 0;
}

.tabbox_middle {
	margin: margin: 1px 0px -1px;
	background: url(../roundbox-main-mid.gif) repeat-y center top;
}

.tabbox_middle, {
	margin: -1px 0px -13px;
	background: url(../roundbox-main-mid.gif) repeat-y center top;
}

div.separator {
	border-right: 1px dotted;
	color: #cccccc;
}

#navBar {
	margin-top: 0px;
	margin-left: 8px;
	display:block; 
	width: 851px;
	height: 28px;
	position: relative;
}

.menu_link_bold,.menu_link_bold span{
	font-family: Arial, Verdana, sans-serif;
  	font-size: 12px;
  	line-height: 1.5;
	color: #000000;
	font-weight: bold;
}

.menu_link_bold:hover, .menu_link_bold:hover span {
	font-family: Arial, Verdana, sans-serif;
  	font-size: 12px;
  	line-height: 1.5;
	color: #cccccc;
	font-weight: bold;
}

span.navyBlueIndicator {
	color: #0082C8;
}

span.redIndicator {
	color: red;
}

a.inactiveLink,  {
	font-family: Verdana, Arial, sans-serif;
	font-size : {$SMALL_FONT}px;
	text-decoration: none
}

div.solidrowblue_separator {
	border-bottom: 1px solid;
	border-color: #cfe1e4;
}

div.solidgrey_row_separator {
	border-bottom: 1px solid;
	border-color: #e6e6e6;
}

a.systemNavNew {color: #ffffff; text-decoration: none;}
a.systemNavNew:link {color: #ffffff; text-decoration: underline;}
a.systemNavNew:visited {color: #ffffff; text-decoration: underline;}
a.systemNavNew:hover {color: #b2bac0; text-decoration: none;}

.inputBoxContentsNew {
  	font-family: Arial, Verdana, sans-serif;
  	font-size: 12px;
  	color: black;
  	border-color: #999999;
}

td.pageHeadingTitleNew {
	font-family: Arial;
	font-size: 20px;
	color:#333333;
}

.inputBoxContentsEmplyNew {
  	font-family: Arial, Verdana, sans-serif;
  	font-size: 12px;
  	color: black;
  	border-color: #999999;
  	background-color: #FFFFCC;
}

.inputBoxContentsLarge {
  	font-family: Arial, Verdana, sans-serif;
  	font-size: 18px;
  	color: black;
  	border-color: #999999;
}

#bottom_menu {
	float:left;
}

#bottom_menu, {
	top: 4px;
	position:relative;
	float: none;
}

#hotrect{
	position:absolute;
	border:1px solid #27a;
	z-index:100;
	display:none;
	filter:Alpha(Opacity=0);
	MozOpacity:0;left:0px;
	top:0px
}

div#ogm_space_beta_icon {
	position: relative;
	z-index: 888;
	top: 0px;
	left: 0px;
}