div.lnBodyTop, div.lnBodyBottom {
/*	width:950px;*/
	height:1px;
	background-color:#bdbdbd;
	clear:both;
}

div.lnBody2Column {
	background: transparent url(../latest_news_layout.gif) repeat-y;
	width:950px;
	clear:both;
	float:left;
}

div.lnBodyColumn1 {
	width:244px;
	float:left;
	overflow:hidden;
}

div.lnBodyColumn2 {
	width:705px;
	float:left;
	overflow:hidden;
}

div.lnBodyColumn1 div.lnBodyColumn1Gray {
	margin:1px 2px;
	width:240px;
	background-color:#f4f4f4;
	float:left;
}

a.tag1  {
	font-size:100%;
	font-weight:normal;
}

a.tag2 {
	font-size:110%;
	font-weight:normal;
}

a.tag3  {
	font-size:120%;
	font-weight:normal;
}

a.tag4  {
	font-size:140%;
	font-weight:normal;
}

a.tag5  {
	font-size:150%;
	font-weight:normal;
}

a.tag6  {
	font-size:160%;
	font-weight:normal;
}

a.tag7  {
	font-size:170%;
	font-weight:normal;
}

a.tag8  {
	font-size:180%;
	font-weight:normal;
}

a.tag9  {
	font-size:190%;
	font-weight:normal;
}

a.tag10  {
	font-size:200%;
	font-weight:normal;
}

a.tag1, a.tag2, a.tag3, a.tag4, a.tag5, a.tag6, a.tag7, a.tag8, a.tag9 {
	background:none repeat scroll 0 0 transparent;
	display:inline-table;
	line-height:100%;
	list-style:none outside none;
	padding-left:4px;
	padding-right:4px;
	text-align:center;
}

div.lnBodyColumn1 ul {
	list-style-image:none;
	list-style-position:outside;
	list-style-type:none;
	margin:0;
	padding:0;
}

div.lnBodyColumn1 ul.listing li {
	margin:0 0 15px 0;
}

div.lnBodyColumn1 ul.checkbox li {
	clear:both;
}

div.lnBodyColumn1 ul.checkbox li div.lbl {
	float:left;
	height:20px;
	padding: 2px 0 0 3px;
}

#released_date {
    width: 100px;
}

div.lnBodyColumn1 ul.listing li a {
	color: #636363;
	text-decoration:underline;
}

div.lnBodyColumn1 h2 {
	font-size:16px;
	color:#006bad;
	display:block;
	margin:0px;
}

div.lnBodyColumn2 h1 {
	font-size:18px;
	color:#006bad;
	display:block;
	margin:0px;
	font-weight:normal;
}

div.lnBodyColumn1 h3 {
	font-size:14px;
	display:block;
	margin:0px;
}

div.lnBodyColumn1 div.line, div.lnBodyColumn2 div.line {
	border-bottom: solid 1px #bdbdbd;
	display:block;
	margin: 10px 0;	
	clear:both;
}

div.lnBodyColumn1 div.dottedLine, div.lnBodyColumn2 div.dottedLine {
	border-bottom: dotted 1px #bdbdbd;
	display:block;
	margin: 10px 0;	
}

div.lnNextIcon {
	background: transparent url(../latest_news_icons.gif) no-repeat -26px 0;
	width:14px;
	height:14px;
	float:left;
}

div.lnPreviousIcon {
	background: transparent url(../latest_news_icons.gif) no-repeat -11px 0;
	width:14px;
	height:14px;
	float:left;
}

div.lnHouseIcon {
	background: transparent url(../latest_news_icons.gif) no-repeat 0 0;
	width:10px;
	height:10px;
	float:left;
}

div.lnBodyColumn2 ul.listing {
	list-style-image:none;
	list-style-position:outside;
	list-style-type:none;
	margin:0;
	padding:0;
}

div.lnBodyColumn2 ul.listing li {
	padding: 20px 0;
	border-bottom: dotted 1px #bdbdbd;
	float:left;
	clear:both;
	width:100%;
}

div.ln_tags {
	background: transparent url(../latest_news_icons.gif) no-repeat -41px 0;
	width:47px;
	height:20px;
	float:left;
}

a.ln_stbutton_background {
	background: transparent url(../latest_news_icons.gif) no-repeat -50px -21px;
	width:50px;
	height:20px;
	float:left;
	color: #777777;
}

a.ln_stbutton_background:hover {
	text-decoration: none;
}

a.ln_stbutton_background span {
	display:block;
	font-size:10px;
	padding:3px 0 0 20px;
}

a.ln_rssIcon {
	background: transparent url(../latest_news_icons.gif) no-repeat 0px -15px;
	width:16px;
	height:16px;
	float:left;
}
