﻿body {
    height: auto;
    top: 0px !important;
}
html, body {
    background-color: #EDEDED;
    color: #545454;
}
.goog-te-banner-frame.skiptranslate {display: none !important;}
li.mm-item a.mm-item-link span.arrow,
li.vm_header>a>span.arrow, li.vm_header_selected>a>span.arrow,
li.vm_header>a>span.arrow2, li.vm_header_selected>a>span.arrow2,
li.vm_header, li.vm_header_selected,
.ibrc_btm_lf, .ibrc_btm_rg, .ibrc_btm_ct,
.rc_top_lf, .rc_top_rg, .rc_btm_lf, .rc_btm_rg, .tabItem>.ct_selected,
.nrc_top_lf, .nrc_top_rg, .nrc_btm_lf, .nrc_btm_rg,
.brc_top_lf, .brc_top_rg, .brc_btm_lf, .brc_btm_rg,
.lgrc_top_lf, .lgrc_top_rg, .lgrc_btm_lf, .lgrc_btm_rg,
.ylrc_top_lf, .ylrc_top_rg, .ylrc_btm_lf, .ylrc_btm_rg,
.grc_top_lf, .grc_top_rg, .grc_btm_lf, .grc_btm_rg,
.sdrc_top_lf, .sdrc_top_rg, .sdrc_btm_lf, .sdrc_btm_rg, .sdrc_top_ct,
.mm-item-content-table td.btm_lf, .mm-item-content-table td.btm_rg, .mm-item-content-table td.btm_ct,
.tabItem>.lf_selected, .tabItem>.rg_selected,
li.mm-item:hover a.mm-item-link, li.mm-item-hover a.mm-item-link,
li.mm-item:hover div.lf, li.mm-item-hover div.lf, li.mm-item:hover div.rg, li.mm-item-hover div.rg,
.tabTop, a.linkHolder>span.linkIcon, span.fbIcon, a.snsLink>span.tweetIcon, a.snsLink>span.youtubeIcon, a.snsLink>span.nlIcon, a.snsLink>span.forumIcon, span.cartIcon,
ul.ogmMenu li.partition,
#blueBar,
#footerBar, .footer_column,
#sags_box>span.search_img,
a.fav_star,
.blue_triangle {
    background-image: url("../box_and_tab.png");
}

.bShodow, .gShodow, .lgShadow {
    background-image: url("../shadow_wide.gif");
    width: 646px;
    background-repeat: no-repeat;
    height: 13px;
}

.sdrc_mdl, .sdrc_mdl2,
.mm-item-content-table td.lf, .mm-item-content-table td.rg {
    background-image: url("../drop-side.png");
}

.personalIcon {
    background: url("../sprite_footer.gif") repeat-x scroll 0px -35px transparent;
    height: 16px;
    left: 0;
    position: absolute;
    width: 16px;
}

.container, #globalContainer, #footerTopContainer, #footerBtmContainer {
    width: 972px;
    margin: 0 auto;
    position: relative;
}
#footerBtmContainer div.description {
    position:absolute;
    z-index: 2;
}
#blueBar {
    background-position: 0 -410px;
    background-repeat: repeat-x;
    width: 100%;
    position: relative;
    min-width: 972px;
}
#yellowBar {
    background-color: #ffffed;
    width: 100%;
}
#blackBar {
    background-color:#000;
    width: 100%;
    height: 260px;
    left: 0;
    position: absolute;
}
#blackbar2 {
    background: #6e6e6e;
    background: -moz-linear-gradient(top,  #6e6e6e 0%, #000000 30%, #000000 30%, #000000 100%);
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#6e6e6e), color-stop(30%,#000000), color-stop(30%,#000000), color-stop(100%,#000000));
    background: -webkit-linear-gradient(top,  #6e6e6e 0%,#000000 30%,#000000 30%,#000000 100%);
    background: -o-linear-gradient(top,  #6e6e6e 0%,#000000 30%,#000000 30%,#000000 100%);
    background: -ms-linear-gradient(top,  #6e6e6e 0%,#000000 30%,#000000 30%,#000000 100%);
    background: linear-gradient(top,  #6e6e6e 0%,#000000 30%,#000000 30%,#000000 100%);
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#6e6e6e', endColorstr='#000000',GradientType=0 );
    margin-bottom: -1px;
    width: 100%;
}
#noticeBar {
    background-color:#AF0000;
    color: #FFF;
    text-decoration: none;
}
#noticeBar>.container {
    min-height: 18px;
    padding: 15px 0;
}
#noticeBar .notice {
    line-height: 15px;
    padding: 0 28px 0 15px;
    font-size: 12px;
}
#noticeBar .hide-icon {
    background: url("../icons/icon_close.png") no-repeat scroll left top rgba(0, 0, 0, 0);
    cursor: pointer;
    height: 18px;
    position: absolute;
    right: 5px;
    top: 10px;
    width: 18px;
}
#pageHeader {}
#headTop {width: 100%;}
#headNav {height: 43px;}
.bShodow {
    box-sizing: unset;
    background-position: center 1px;
    padding: 1px 163px 0;
    position: absolute;
    left: 0;
}
#topLogo {
    padding-top: 15px;
}
#topLogo a {
    background-image: url("../logo-transparent.png");
    background-position: 0 0;
    background-repeat: no-repeat;
    height: 57px;
    width: 253px;
}
#topLogo a.cny {
    background-image: url("../logo-transparent-cny.png");
}
#topLogo a.xmas {
    background-image: url("../logo-transparent-xmas.png");
}
#topRg {
    box-sizing: unset;
    height: 60px;
    margin-left: 251px;
    padding-top: 15px;
}
#lfHolder {
    padding-left: 10px;
}
#rgHolder {
    position: relative;
}

.sBox {
    background-image: url("../searchbox.png");
    background-repeat: no-repeat;
    display: block;
    height: 40px;
    width: 260px;
}

.sBoxSelected {
    background-image: url("../searchbox.png");
    background-position: 0 -39px;
    background-repeat: no-repeat;
    display: block;
    width: 260px;
    height: 50px;
}

#sText {
    background: none repeat scroll 0 0 transparent;
    border: 0 none;
    box-shadow: none;
    color: #f2f2f2;
    font: 11px/16px "Lucida Grande",Arial,Verdana,sans-serif;
    height: 20px;
    left: 0;
    margin: 0 0 0 42px;
    outline: medium none;
    padding: 0;
    position: absolute;
    top: 13px;
    width: 192px;
}

#sClear {
    background-image: url("../icons/cancel-round.png");
    cursor: pointer;
    display: none;
    height: 15px;
    left: 230px;
    position: absolute;
    top: 16px;
    width: 16px;
}

#sResult {
    background-position: -5px -81px;
    background-repeat: repeat-y;
    list-style-type: none;
    position: absolute;
    top: 50px;
    width: 217px;
    padding:0px;
    margin:0px;
}

#sBottom {
    background-image: url("../searchbox.png");
    background-position: 0 -90px;
    height: 60px;
    padding-left: 53px;
    padding-top: 20px;
    position: relative;
    width: 210px;
}

#footerBar, .footer_column {
    background-position: 0 -529px;
    background-repeat: repeat-x;
}
.footerSeperator {
    background: url("../sprite_footer.gif") no-repeat scroll 0 -130px transparent;
}

.sResultLi {
    background-image: url("../searchbox.png");
    background-position: 0 -81px;
    background-repeat: repeat-y;
    padding-bottom: 8px; /*5px;*/
    padding-right: 0;
    padding-top: 10px; /*5px;*/
    position: relative;
    width: 262px;
}

.sResultLiNone{
    background-image: url("../searchbox.png");
    background-position: 0 -81px;
    background-repeat: repeat-y;
    padding-bottom: 8px; /*5px;*/
    padding-right: 0;
    padding-top: 10px; /*5px;*/
    position: relative;
    width: 262px;
}

.sResultLiNone div.sGames{
    left: 8px;
    margin-bottom: -10px;
    margin-top: -11px;
    min-height: 30px;
    padding-left: 17px;
    padding-top: 9px;
    position: relative;
    width: 230px;
}

.sResultLi div.sGames{
    border-top-color: #CCCCCC;
    border-top-style: dotted;
    border-top-width: 1px;
    left: 8px;
    margin-bottom: -10px;
    margin-top: -11px;
    min-height: 30px;
    padding-left: 17px;
    padding-top: 12px;
    position: relative;
    width: 230px;
}

.sResultLi div.sGames:hover{
    background-color: #C9C9C9;
    cursor:pointer;
}

.sResultLi div.sGames font {
    font-family: Arial;
    font-size: 11px;
    color: #545454;
}

#content {
    background-color: #EDEDED;
    clear: both;
    min-height: 700px;
    padding: 0 0 50px;
}
div.bodyContentCenter {
    float: right;
    width: 765px;
}
#mainTopBannerBox, #mainMdlBannerBox, #mainContentBox, #mainContentFooterBox {
    width:100%;
    position:relative;
}
#mainTopBannerBox {
    height: 260px;
}
#mainContentBox {
    height:auto;
    min-height:500px;
}
#mainContentFooterBox {
    min-height:300px;
}
#pageFooter {
    width: 100%;
    position: relative;
    bottom: 0px;
    min-width: 972px;
}
#footerTop {
    background-color: #CCC;
    border-top: 1px solid #B3B3B3;
    height: 370px;
    width: 100%;
}
.gShodow {
    background-position: center -26px;
    padding: 0px 163px;
    position: absolute;
}
#footerBtm {
    background-color: #B1B1B1;
    border-top: 1px solid #9F9F9F;
    height: 100px;
    padding-top: 20px 0 15px;
    width: 100%;
}
.useBlk {
    display:block;
}

.lyr000 {z-index: 0;}
#footerTop, #footerBtm {z-index: 1;}
.lyr100 {z-index: 100;}
.lyr115 {z-index: 115;}
.lyr145 {z-index: 145;}
.lyr150 {z-index: 150;}
.lyr200 {z-index: 200;}		/* slider 200 - 300 */
.lyr300 {z-index: 300;}
.lyr400 {z-index: 400;}

/* Header 500-600 */
.lyr545 {z-index: 545;}		/* main menu 500-550 */
.lyr550 {z-index: 550;}
.lyr565 {z-index: 565;}		/* search box */

.lyr700 {z-index: 700;}		/* footer bar 700 - 800 */
.lyr800 {z-index: 800;}		/* pop up 800 - 900 */
.lyr850 {z-index: 850;}

/* Main Menu */
ul.ogmMenu div.mm-item-content {z-index: 509;}
.mm-content-base {z-index: 511;}
/* Main Menu */

.lfloat {float: left;}
.rfloat {float: right;}

.hspacing {display: inline-block;width:20px}
.vspacing {clear:both;height:20px;}

.dotborder {border-top: 1px dotted #cecece;}
.dotborderBtm {border-bottom: 1px dotted #cecece;}

.dotborderLG {border-color:#CECECE;border-style:dotted;}

.solborder, .solborderb, .solborderc, .solborderd,
.solborder2s, .solborder2bs,
.solborder3s, .solborder3bs {border-color:#CECECE;border-style:solid;}
.solborder {border-width: 1px 0px 0px;}
.solborderb {border-width: 0px 1px 0px;}
.solborderc {border-width: 0px 0px 1px;}
.solborderd {border-width: 0px 0px 0px 1px;}
.solborder2s {border-width: 0px 1px;}
.solborder2bs {border-width: 1px 0px;}
.solborder3s {border-width: 0px 1px 1px;}
.solborder3bs {border-width: 1px 1px 0px;}

.solborderG, .solborderGb {border-color:#9D9D9D;border-style:solid;}
.solborderGb {border-right-width: 1px;}

.solborderLG, .solborderLGd {border-color:#E7E7E7;border-style:solid;}
.solborderLGd {border-left-width: 1px;}

.shw {display: block;}
.hdn {display: none;}
.clrFx {clear: both;}

/* Layout Structure */
.ogm_partition {display: inline-block;width: 100%;}
.halfBox {width:476px;}

div.theLayerBg {
    background-color: #000000;
    border: medium none;
    height: 100%;
    left: 0;
    margin: 0;
    opacity: 0.9;
    padding: 0;
    position: fixed;
    top: 0;
    visibility: hidden;
    width: 100%;
    z-index: 800;
    filter: alpha(opacity=90);
    -moz-opacity: 0.9;
}

/* 	font setting
        {X} : text-transform
        {N} : N-style
        hd{X}{N} : size 12px
        hd{s}{X}{N} : size 11px
*/
span.hd1, span.hdC1, span.hdU1, span.hdL1,
span.hd2,
span.hd3,
span.hd4, span.hdsU4,
span.hd5, span.hds5,
span.hd6 {
    font-weight: bold;
    font-size: 12px;
    line-height: 21px;
}

span.hds1, span.hdsC1, span.hdsU1, span.hdsL1,
span.hds2,
span.hds3,
span.hds4, span.hdsU4,
span.hds5, span.hds5,
span.hds6 {
    font-weight: bold;
    font-size: 11px;
    line-height: 14px;
}

/* black */
span.hd1, span.hdC1, span.hdU1, span.hdL1,
span.hds1, span.hdsC1, span.hdsU1, span.hdsL1 {color:#000;}
span.hdC1, span.hdsC1 {text-transform:capitalize;}
span.hdU1, span.hdsU1 {text-transform:uppercase;}
span.hdL1, span.hdsL1 {text-transform:lowercase;}

/* White */
span.hd2,
span.hds2 {color:#FFF;}

/* #545454 */
span.hd3,
span.hds3 {color:#545454;}

span.hd5, span.hdC5, span.hdU5, span.hdL5,
span.hds5, span.hdsC5, span.hdsU5, span.hdsL5 {font-weight: normal;color:#545454;}
span.hdC5, span.hdsC5 {text-transform:capitalize;}
span.hdU5, span.hdsU5 {text-transform:uppercase;}
span.hdL5, span.hdsL5 {text-transform:lowercase;}

/* #999 */
span.hd4,
span.hds4, span.hdsU4 {color: #999;font-weight: normal;}
span.hdsU4 {text-transform:uppercase;}

/* content description */
span.cd1 {
    color: #545454;
    font-size: 12px;
}
span.op {
}
span.date {
}
span.cdd_price { /*custom dropdown*/
    font-size: 11px;
    font-weight: bold;
    line-height: 16px;
}


/* link
        1 : [b]-[#545454-(H)#0075A7]-[TD][u-(H)u]
        2 : [b]-[#545454-(H)#0075A7]-[TD][u-(H)n]
        3 : [n]-[#FFF-(H)#FFF]-[TD][n-(H)u]
        4 : [b]-[#FFF-(H)#FFF]-[TD][n-(H)u]
        5 : [n]-[#545454-(H)#0075A7]-[TD][u-(H)u]
*/
a.hd1, a.ahd1, a.ahd2, a.ahd3, a.ahd3>span.ihr, a.ahd4, a.ahd4>span.ihr,
a.ahd5, a.ahd5>span.ihr {
    cursor: pointer;
    font-size: 12px;
    line-height: 21px;
}
a.ahds1, a.ahds2, a.ahds3, a.ahds3>span.ihr, a.ahds4, a.ahds4>span.ihr,
a.ahds5, a.ahds5>span.ihr {
    cursor: pointer;
    font-size: 11px;
    line-height: 14px;
}
a.hd1, a.ahd1, a.ahd2, a.ahd4,
a.hds1, a.ahds1, a.ahds2, a.ahds4 {
    font-weight: bold;
}

a.hd1, a.ahd1, a.hds1, a.ahds1,
a.ahd5, a.ahd5>span.ihr, a.ahds5, a.ahds5>span.ihr {color: #545454;text-decoration: underline;}
a.hd1:hover, a.ahd1:hover,
a.hds1:hover, a.ahds1:hover,
a.ahd5:hover, a.ahds5:hover {color: #0075A7;}
a.ahd2, a.ahds2 {color: #545454;text-decoration: underline;}
a.ahd2:hover, a.ahds2:hover {color: #0075A7;text-decoration: none;}
a.ahd3, a.ahd3>span.ihr, a.ahds3, a.ahds3>span.ihr,
a.ahd4, a.ahd4>span.ihr, a.ahds4, a.ahds4>span.ihr {color: #FFF;text-decoration: none;}
a.ahd3:hover, a.ahds3:hover,
a.ahd4:hover, a.ahds4:hover {text-decoration: underline;}

a.linkHolder {
    padding-left: 18px;
    position: relative;
}
a.linkHolder>span.linkIcon {
    background-position: -24px -8px;
    background-repeat: no-repeat;
    height: 12px;
    left: 2px;
    margin-top: 5px;
    position: absolute;
    width: 12px;
}
a.linkHolder:hover>span.linkIcon {
    background-position: -35px -8px;
}

a.snsLink {
    padding-left: 21px;
    position: relative;
}

span.fbIcon {
    background-position: -24px -55px;
}

a.snsLink > span.tweetIcon {
    background-position: -41px -55px;
}

a.snsLink > span.youtubeIcon {
    background-position: -58px -55px;
}

a.snsLink > span.nlIcon {
    background-position: -92px -55px;
}

a.snsLink > span.forumIcon {
    background-position: -41px -73px;
}

span.cartIcon {
    background-position: -24px -72px;
}

span.fbIcon, a.snsLink > span.tweetIcon, a.snsLink > span.youtubeIcon,
a.snsLink > span.nlIcon, a.snsLink > span.forumIcon, span.cartIcon {
    background-repeat: no-repeat;
    height: 17px;
    left: 0;
    margin-top: -1px;
    position: absolute;
    width: 18px;
}

.blue_triangle {
    background-position: -52px -9px;
    background-repeat: no-repeat;
    display: block;
    height: 5px;
    width: 10px;
}

/* Input style
        ihd1 : fw bold - td underline
*/
div.ihd1 {
    display:inline;
}
div.ihd1>input {
    background: none repeat scroll 0 0 transparent;
    border: 1px solid #CDCDCD;
    color: #999999;
    font-size: 11px;
    height: 26px;
    line-height: 26px;
    padding: 0 3px 0 10px;
}
div.ihd1>input.inputfocus,
div.shd1>select.focus {
    color: #545454;
}

div.shd1 {
    height: 28px;
    width: 318px;
    /*
    border: 1px solid #CDCDCD;
display: inline;
width: 300px;
    */
}
div.shd1>select {
    border: 1px solid #CDCDCD;
    color: #999999;
    height: 30px;
    padding: 5px;
    /*
padding: 5px 0 5px 10px;
    */
}
div.shd1>select>option {
    padding: 5px;
}
/* font setting */


/* Shadow */
.lgShadow {
    background-position: center -13px;
    padding: 0px 163px;
    position: absolute;
}
/* Shadow */


/* menu */
/* font setting */
li.mm-item a.mm-item-link, li.mm-item a.mm-item-link span {
    color: #FFF;
    font-size: 12px;
    font-weight: bold;
    line-height: 18px;
    text-decoration: none;
}
li.mm-item:hover a.mm-item-link, li.mm-item-hover a.mm-item-link,
li.mm-item:hover a.mm-item-link span, li.mm-item-hover a.mm-item-link span {
    color: #444;
}
/* font setting */

ul.ogmMenu {margin: 0px auto;padding: 1px 10px 0;border: 0px;list-style: none;display: none;}
ul.ogmMenu li {margin: 0 2px;padding: 0px;float: left;}
ul.ogmMenu li.partition {background-image: url("../head_line.gif");height: 10px;width: 2px;margin-top: 15px;}
ul.ogmMenu li.clear-fix {float: none;clear: both;margin: 0px;padding: 0px;height: 0px;font-size: 0px;line-height: 0px;border: 0px;}
ul.ogmMenu li.mm-item>a.dropdown {padding-right: 30px;}

li.mm-item div.lf, li.mm-item div.rg,
li.mm-item:hover div.lf, li.mm-item:hover div.rg,
li.mm-item-hover div.lf, li.mm-item-hover div.rg {
    background-repeat: no-repeat;
    float: left;
    position: relative;
    height: 42px;
    width: 6px;
}
li.mm-item:hover div.lf, li.mm-item-hover div.lf {background-position: 0 -326px;}
li.mm-item:hover div.rg, li.mm-item-hover div.rg {background-position: -12px -326px;}

li.mm-item a.mm-item-link {float: left;padding: 12px 6px;}
li.mm-item:hover a.mm-item-link, li.mm-item-hover a.mm-item-link {
    background-position: 0 -368px;
    background-repeat: repeat-x;
    cursor: pointer;
    display: block;
    float: left;
    margin: 0;
    position: relative;
}

li.mm-item a.mm-item-link span.arrow {
    background-position: -25px -20px;
    background-repeat: no-repeat;
    height: 18px;
    margin: 0 6px 0 3px;
    position: absolute;
    width: 18px;
}
li.mm-item:hover a.mm-item-link span.arrow,
li.mm-item-hover a.mm-item-link span.arrow {
    background-position: -45px -20px;
}

ul.ogmMenu div.mm-item-content {
    padding: 0px;
    position: absolute;
}

.mm-content-base {
    padding-top: 5px;
    position: relative;
}
#sub-menu-1 {
    border-right: 1px solid #FFF;
    float: left;
    position: relative;
    right: 1px;
    width: 100%;
}
#sub-menu-2 {
    border-right: 1px solid #C7C7C7;
    clear: left;
    float: left;
    position: relative;
    right: 197px;
    width: 100%;
}
#sub-menu-3 {
    clear: left;
    float: left;
    overflow: hidden;
    width: 100%;
}
.mm-sub-menu-1, .mm-sub-menu-2 {
    float: left;
    left: 198px;
    overflow: hidden;
    position: relative;
    padding: 0 10px;
}
.mm-sub-menu-2 {
    left: 199px;
}
.mm-sub-menu-1>div, .mm-sub-menu-2>div {
    padding: 5px;
}

/* Shadow Box */
.mm-item-content-table {width: 100%;}
.mm-item-content-table td {padding: 0px;}
.mm-item-content-table td.ctn {background-color: #EDEDED;}
.mm-item-content-table td.lf, .mm-item-content-table td.rg {width: 12px;}
.mm-item-content-table td.lf {background-position: -25px 0;background-repeat: repeat-y;}
.mm-item-content-table td.rg {background-position: -40px 0;background-repeat: repeat-y;}
.mm-item-content-table td.btm_lf {background-position: -80px -15px;background-repeat: no-repeat;}
.mm-item-content-table td.btm_rg {background-position: -95px -15px;background-repeat: no-repeat;}
.mm-item-content-table td.btm_ct {background-position: 0 -611px;background-repeat: repeat-x;height: 10px;}
/* menu */


/* Tab */
.tabTop {background-position: 0 -202px;background-repeat: repeat-x;height: 41px;position: relative;}
.tabTop>.header {padding-left: 30px;position: absolute;}
.tabTop>.footer {background-color: #CECECE;height: 1px;position: absolute;top: 40px;width: 100%;}
.tabMdl {position: relative;}
.tabItem {float: left;visibility: visible;opacity: 100;padding-right: 5px;}
.tabItem>.lf, .tabItem>.rg {float: left;height: 41px;width: 6px;}
.tabItem>.rg {width: 12px;}
.tabItem>.ct {float: left;cursor: pointer;height: 41px;}
.tabItem>.ct>span {display: block;padding: 13px 10px;white-space: nowrap;}
.tabItem>.lf_selected {background-position: 0 -243px;background-repeat: no-repeat;}
.tabItem>.rg_selected {background-position: -12px -243px;background-repeat: no-repeat;}
.tabItem>.ct_selected {background-position: 0 -285px;background-repeat: repeat-x;}
.tabBtm {position: relative;height: 5px;}
.tabBtm>.footer {position: absolute;width: 100%;}
.tab_row, .tab_row_last {padding: 15px;background-color: #FFF;}
.tab_row_last {padding-bottom: 0px;}
.tabMdl>div>div.algcntr {text-align: center;}
.tabMdl>div>div.t1>div.lfloat {width: 335px;}
.tabMdl>div>div.t1>div.rfloat {margin-top: 5px;}
.tabMdl>div>div.t2>div.lfloat {height:60px;width: 90px;}
.tabMdl>div>div.t2>div.rfloat {width: 335px;word-wrap: break-word;}
.tabMdl>div>div.t3>div.lfloat {width: 444px;word-wrap: break-word;}
/* Tab */


/* Simple/Expandable VMenu Round Corner Box */
.vmContent {background-color: #FFF;clear: both;}
.vmContent>.header {padding: 6px 15px 10px;}
.vmContent>.content {position: relative;}
ul.vmenu, ul.vmenu2 {background-color: #888;border: 0px;margin: 0px auto;padding: 0px 0px;list-style: none;}
li.vm_header {background-repeat: repeat-x;background-position: 0 -624px;cursor: pointer;position: relative;}
li.vm_header:hover, li.vm_header_selected {background-repeat: repeat-x;background-position: 0 -741px;}
li.vm_header>a {
    color: #545454;font-weight: bold;text-decoration: none;display: block;position: relative;}
li.vm_header>a>span.lbl {padding:9px 15px;display: inline-block;}
li.vm_header:hover>a, li.vm_header_selected>a {color: #FFF;}
li.vm_content{width: 100%;float: left;clear: both;}
li.vm_header>a>span.arrow, li.vm_header>a>span.arrow2 {background-repeat: no-repeat;position: absolute;right: 0px;bottom: 0;}
li.vm_header>a>span.arrow {background-position: -24px -19px;height: 20px;margin: 5px 15px;width: 20px;}
li.vm_header_selected>a>span.arrow {background-position: -44px -19px;}
li.vm_header>a>span.arrow2 {background-position: -24px 0;height: 8px;margin: 12px 15px;width: 14px;}
li.vm_header>a:hover>span.arrow2 {background-position: -37px 0;}
li.vm_header_selected>a>span.arrow2, li.vm_header_selected>a:hover>span.arrow2 {background-position: -50px 0;}
li.vm_content{width: 100%;float: left;clear: both;}
/* Simple/Expandable VMenu Round Corner Box */


/* Simple Inner White Round Corner Box */
.srcContent{background-color: #FFF;}
.srcContent>.header{border-bottom: 1px solid #CECECE;padding: 6px 15px 10px;}
.fancy_box .srcContent>.header{padding: 14px 15px 16px;}
/* Simple Inner White Round Corner Box */


/* Drop Down Listing */
ul.dd_listing {
    background-color: #FFF;
    border: 0px;
    margin: 0px auto;
    padding: 0px 0px;
    list-style: none;
}
ul.dd_listing>li {
    padding: 9px 5px 9px 25px;
    display: block;
    cursor: pointer;
}
ul.dd_listing>li:hover {
    background-color: #E9E9E9;
}
ul.dd_listing>li>a {
    display: block;
    text-decoration: none;
    color: #545454;
}
ul.dd_listing2>li {
    background-image: url("../pointer_blue_light.gif");
    background-repeat: no-repeat;
    background-position: 15px 8px;}
/* Drop Down Listing */


/* Search All Game Listing */
div.gameBox:hover, div.gameBox_selected {background-color: #E5E5E5;}

#sag_game_filter_bar .sagames-name,
#sag_game_filter_bar .sagames-letter,
#sag_game_filter_bar .sagames-lang,
#sag_game_filter_bar .sagames-producttype,
#sag_game_filter_bar .sagames-platform,
#sag_game_filter_bar .sagames-genre,
#sag_game_filter_bar .sagames-fav {vertical-align: bottom;}
#sag_game_filter_bar .sagames-name {border-right: 0px;}
#sag_game_filter_bar .sagames-name>span {padding: 0;}
#sag_game_filter_bar .sagames-fav {padding: 0 0 7px 11px; width: 70px;}
.sagames-name, .sagames-lang, .sagames-producttype, .sagames-platform, .sagames-genre {vertical-align: top;}
.sagames-fav {vertical-align: middle;}
.sagames-lang {width:100px;}
.sagames-platform, .sagames-genre {width:135px;}
.sagames-producttype {width:205px;}
.sagames-fav {padding-left: 32px;width: 48px;}
.sagames-name, .sagames-letter, .sagames-lang, .sagames-producttype, .sagames-platform, .sagames-genre {border-right:1px solid #CECECE;}
.gameBoxExtend {padding:5px;}
.sagames-name span, .sagames-lang span, .sagames-platform span, .sagames-genre span {display:block;padding:3px 10px;}
.span_f_search_box, .span_f_letter, .span_f_language, .span_f_product_type, .span_f_platform, .span_f_genre {margin:5px 0 0;}
#sags_box {
    background-color: #FFFFFF;
    border-top: 1px solid #CCCCCC;
    height: 29px;
    overflow: hidden;
    position: relative;
    text-align: left;
}
#sags_box>input {
    border: 0 none;
    color: #999999;
    font-size: 11px;
    height: 22px;
    line-height: 20px;
    padding: 3px 24px 3px 25px;
}
#sags_box>input.inputfocus {
    color: #545454;
}
#sags_box>span.search_img, #sags_box>span.right_img {
    background-repeat: no-repeat;
    position: absolute;
    padding: 0;
    top: 7px;
}
#sags_box>span.search_img {
    background-position: -58px -38px;
    height: 15px;
    left: 4px;
    width: 15px;
}
#sags_box>span.right_img {
    height: 16px;
    right: 4px;
    width: 16px;
}
span.clear_img {
    background-image: url("../icons/cancel-round.png");
    cursor: pointer;
}
span.load_img {
    background-image: url("../icons/loading_16X16.gif");
}
.note_img {
    background-image: url("../icons/note.gif");
    height: 18px;
    position: relative;
    width: 18px;
}
.note_img span.lbl {
    left: 23px;
    position: absolute;
    width: 200px;
    line-height: 18px;
    top: 5%;
}
div.icon_err_msg {
    background: url("../icons/cancel-round.png") no-repeat scroll 0 4px transparent;
    display: block;
    line-height: 15px;
    min-height: 22px;
    padding: 3px 0 0 25px;
}
div.load_img {
    width: 32px;
    height: 32px;
    background-image: url("../lightbox-ico-loading.gif");
}
div.splash_cny_img {
    width: 970px;
    height: 628px;
    background-image: url("../splash_cny.jpg");
}
div.splash_xmas_img {
    width: 970px;
    height: 628px;
    background-image: url("../splash_xmas.jpg");
}
#preload-splash_cny_img {
    background: url("../splash_cny.jpg") no-repeat -9999px -9999px
}
#preload-splash_xmas_img {
    background: url("../splash_xmas.jpg") no-repeat -9999px -9999px
}
div.footer_cny_img {
    width: 972px;
    height: 130px;
    background-image: url("../footer-cny.png");
}
div.footer_xmas_img {
    width: 970px;
    height: 269px;
    background-image: url("../footer-xmas.jpg");
}
a.fav_star {
    background-position: -24px -38px;
    background-repeat: no-repeat;
    display: block;
    height: 18px;
    width: 18px;
    cursor: pointer;
}
a.fav_star:hover, a.fav_star_selected {
    background-position: -41px -38px;
}
/* Search All Game Listing */


/* Custom Drop Down Box */
.customdropdown dd, .customdropdown dt, .customdropdown ul,
.customdropdown2 dd, .customdropdown2 dt, .customdropdown2 ul {margin:0px;padding:0px;}
.customdropdown dd,
.customdropdown2 dd {position:relative;}
.customdropdown a, .customdropdown2 a {color:#545454;font-weight: normal;text-decoration:none;outline:none;vertical-align:top;}
.customdropdown dt a,
.customdropdown2 dt a {background-color:#FFFFFF;display:block;padding-right:20px;border:1px solid #CCCCCC;}
.customdropdown dt a span.arrow {}
.customdropdown dd ul,
.customdropdown2 dd ul {background-color:#FFFFFF;border:1px solid #CCCCCC;color:#C5C0B0;display:none;left:0px;padding:0px;position:absolute;top:-1px;list-style:none;}
.customdropdown span.value, .customdropdown span.ext,
.customdropdown2 span.value, .customdropdown2 span.ext {display:none;}
.customdropdown dd ul li a,
.customdropdown2 dd ul li a {display: block;line-height: 16px;padding: 6px 5px;}
.customdropdown dd ul li a:hover,
.customdropdown2 dd ul li a:hover {background-color: #e9e9e9;}
.customdropdown>dt>a>span.sel,
.customdropdown2>dt>a>span.sel {cursor:pointer;color:#999;display:inline-block;line-height: 16px;padding:6px 5px;} /* IE need cursor */
.customdropdown>dt>a.selected>span.sel,
.customdropdown2>dt>a.selected>span.sel {color: #545454;}

/* Custom Drop Down Box [A] - only for width:270px */
.customdropdown {margin:5px 0px 0px;}
.customdropdown dd, .customdropdown dt, .customdropdown ul {width:270px;}
.customdropdown dt a {background: url("../icons/dropdown.gif") no-repeat scroll 97% center #FFFFFF;width:100%;}	/*257px center;width:250px;*/
.customdropdown dd ul {width:270px;}
.customdropdown>dt>a.selected {color: #545454}
/* Custom Drop Down Box [A] */

/* Custom Drop Down Box [B] */
.customdropdown2 {margin: 0;}
.customdropdown2 dt a {position:relative;}
.customdropdown2 dt a span {width:100%}
.customdropdown2>dt>a.selected {background-color:#e9e9e9;}
.customdropdown2>dt>a>span.arrow {
    background-image: url("../icons/dropdown.gif");
    background-position: 8px 12px;
    background-repeat: no-repeat;
    cursor: pointer;
    height: 4px;
    padding: 12px 8px;
    position: absolute;
    right: 0;
    top: 0;
    width: 7px;
}
.customdropdown2>dt>a.selected>span.arrow {
    background-image: url("../icons/cancel-round.png");
    background-position: 4px 6px;
    height: 16px;
    padding: 6px 4px;
    width: 16px;
}
/* Custom Drop Down Box [B] */


/* [Round corner][Inner White][Code 1] */
.rc_top_lf, .rc_top_rg, .rc_btm_lf, .rc_btm_rg {background-repeat: no-repeat;height: 8px;width: 8px;}
.rc_top_lf {background-position: -1px -1px;float: left;}
.rc_top_rg {background-position: -9px -1px;float: right;}
.rc_btm_lf {background-position: -1px -9px;float: left;}
.rc_btm_rg {background-position: -9px -9px;float: right;}
.rc_mdl {border-color: #CECECE;border-style: solid;border-width: 0 1px;}
.rc_top_ct,.rc_btm_ct {background-color: #FFF;height: 7px;margin:0 8px;}
.rc_top_ct {border-top: 1px solid #CECECE;}
.rc_btm_ct {border-bottom: 1px solid #CECECE;}
/* [Round corner][Inner White][Code 1] */

/* [Round corner][Inner White][Code 6] */
.nrc_top_lf, .nrc_top_rg, .nrc_btm_lf, .nrc_btm_rg {background-repeat: no-repeat;height: 2px;width: 2px;}
.nrc_top_lf {background-position: -87px -30px;float: left;}
.nrc_top_rg {background-position: -89px -30px;float: right;}
.nrc_btm_lf {background-position: -87px -32px;float: left;}
.nrc_btm_rg {background-position: -89px -32px;float: right;}
.nrc_mdl {border: 0 none;}
.nrc_top_ct,.nrc_btm_ct {background-color: #FFF;height: 2px;margin:0 2px;}
.nrc_top_ct {border-top: 0px solid #CECECE;}
.nrc_btm_ct {border-bottom: 0px solid #CECECE;}
/* [Round corner][Inner White][Code 6] */

/* [Round corner][Inner Black][Code 0] */
.brc_top_lf, .brc_top_rg, .brc_btm_lf, .brc_btm_rg {background-repeat: no-repeat;height: 8px;width: 8px;}
.brc_top_lf {background-position: -1px -18px;float: left;}
.brc_top_rg {background-position: -9px -18px;float: right;}
.brc_btm_lf {background-position: -1px -26px;float: left;}
.brc_btm_rg {background-position: -9px -26px;float: right;}
.brc_mdl {border-color: #000;border-style: solid;border-width: 0 1px;background-color: #000;}
.brc_top_ct,.brc_btm_ct {background-color: #000;height: 8px;margin-left: 8px;margin-right: 8px;}
/* [Round corner][Inner Black][Code 0] */

/* [Round corner][Inner Light Gray][Code 2] */
.lgrc_top_lf, .lgrc_top_rg, .lgrc_btm_lf, .lgrc_btm_rg {background-repeat: no-repeat;height: 8px;width: 8px;}
.lgrc_top_lf {background-position: -1px -35px;float: left;}
.lgrc_top_rg {background-position: -9px -35px;float: right;}
.lgrc_btm_lf {background-position: -1px -44px;float: left;}
.lgrc_btm_rg {background-position: -9px -44px;float: right;}
.lgrc_mdl {border-color: #e3e3e3;border-style: solid;border-width: 0 1px;background-color: #e3e3e3;}
.lgrc_top_ct,.lgrc_btm_ct {background-color: #e3e3e3;height: 8px;margin-left: 8px;margin-right: 8px;}
/* [Round corner][Inner Light Gray][Code 2] */

/* [Round corner][Inner Yellow][Code 3] */
.ylrc_top_lf, .ylrc_top_rg, .ylrc_btm_lf, .ylrc_btm_rg {background-repeat: no-repeat;height: 8px;width: 8px;}
.ylrc_top_lf {background-position: -64px -1px;float: left;}
.ylrc_top_rg {background-position: -72px -1px;float: right;}
.ylrc_btm_lf {background-position: -64px -9px;float: left;}
.ylrc_btm_rg {background-position: -72px -9px;float: right;}
.ylrc_mdl {border-color: #ffffcc;border-style: solid;border-width: 0 1px;background-color: #ffffcc;}
.ylrc_top_ct,.ylrc_btm_ct {background-color: #ffffcc;height: 8px;margin-left: 8px;margin-right: 8px;}
/* [Round corner][Inner Yellow][Code 3] */

/* [Round corner][Inner Gray][Code 4] */
.grc_top_lf, .grc_top_rg, .grc_btm_lf, .grc_btm_rg {background-repeat: no-repeat;height: 4px;width: 4px;}
.grc_top_lf {background-position: -64px -18px;float: left;}
.grc_top_rg {background-position: -76px -18px;float: right;}
.grc_btm_lf {background-position: -64px -30px;float: left;}
.grc_btm_rg {background-position: -76px -30px;float: right;}
.grc_mdl {border-color: #dedede;border-style: solid;border-width: 0 1px;background-color: #dedede;}
.grc_top_ct,.grc_btm_ct {background-color: #dedede;height: 4px;margin: 0 4px;}
/* [Round corner][Inner Gray][Code 4] */


/* [Round corner][Inner Gray][Code 5] - Default width: 450px */
.sdrc_top_lf, .sdrc_top_rg, .sdrc_btm_lf, .sdrc_btm_rg {
    background-repeat: no-repeat;
    height: 10px;
    width: 10px;
}
.sdrc_top_lf {background-position: -82px -4px;float: left;}
.sdrc_top_rg {background-position: -96px -4px;float: right;}
.sdrc_btm_lf {background-position: -64px -30px;float: left;}
.sdrc_btm_rg {background-position: -76px -30px;float: right;}
.sdrc_mdl {
    background-position: 425px 0;

    background-repeat: repeat-y;
    float: right;
}
.sdrc_mdl2 {
    background-position: -1px 0;
    background-repeat: repeat-y;
    clear:both;
}
.sdrc_top_ct {
    background-position: 0 -600px;
    background-repeat: repeat-x;
    height: 10px;
    margin: 0px 10px;
}
/* [Round corner][Inner Gray][Code 5] */


/* [Round corner][Image background] */
.ibrc_btm_lf, .ibrc_btm_rg {
    background-repeat: no-repeat;
    height: 20px;
    width: 6px;
}
.ibrc_btm_lf {background-position: -1px -79px;float: left;}
.ibrc_btm_rg {background-position: -12px -79px;float: right;}
.ibrc_btm_ct {
    background-repeat: repeat-x;
    background-position: 0 -101px;
    height: 20px;
    margin: 0 6px;
}
/* [Round corner][Image background] */


/* All Button Style Section */
.main_btn>a>span, .main_btn a,
.connect_with_fb_btn a {
    background: url("../buttons/button_main.png") no-repeat scroll 0 0 transparent;
    text-decoration: none;
    float: left;
}

.main_btn, .main_btn>a>span, .main_btn a {
    cursor: pointer;
    display: inline-block;
    margin: 0;
}
.main_btn a {
    position: relative;
    margin-right: 6px;
}
.main_btn > a > font {
    display: inline-block;
    font-size: 11px;
    font-weight: bold;
    line-height: normal;
    padding: 6px 10px 0 30px;
    text-align: center;
}
.main_btn>a>span {
    position: absolute;
    right: -6px;
    top: 0;
    width: 6px;
}
.gray_btn a {cursor: default;}

/* button height */
.green_btn a, .green_btn>a>span,
.yellow_btn a, .yellow_btn>a>span,
.gray_btn a, .gray_btn>a>span,
.gray_short_btn a, .gray_short_btn>a>span,
.gray_box a, .gray_box>a>span {
    height: 30px;
}
.red_btn a, .red_btn span {
    height: 34px;
}
.gray_tall_btn a, .gray_tall_btn span {
    height: 38px;
}
.gray_big_tall_btn a, .gray_big_tall_btn span {
    height: 66px;
}

/* button label padding & button label color */
.gray_btn>a>font,
.gray_short_btn>a>font,
.gray_tall_btn>a>font,
.gray_big_tall_btn>a>font,
.gray_box>a>font {
    color: #000;
    padding: 6px 4px 6px 10px;
}

.gray_btn>a>font {
    color: #999;
}

.green_btn>a>font,
.yellow_btn>a>font,
.red_btn>a>font {
    color: #FFF !important;
}

.gray_btn>a,
.gray_short_btn>a,
.gray_tall_btn>a,
.gray_big_tall_btn>a {
    text-align: center;
}

.green_btn>a>span {background-position: -300px -28px;}
.green_btn a {background-position: 0 0;}
.green_btn a:hover {background-position: 0 -56px;}
.green_btn a:hover span {background-position: -300px -84px;}

.yellow_btn>a>span {background-position: -300px -140px;}
.yellow_btn a {background-position: 0 -112px;}
.yellow_btn a:hover {background-position: 0 -168px;}
.yellow_btn a:hover span {background-position: -300px -196px;}

.red_btn>a>font {padding: 9px 10px;width: 100%;}
.red_btn>a>span {background-position: -275px -310px;width: 29px !important; right: -29px !important;}
.red_btn a {background-position: 0 -278px;margin-right: 29px;}
.red_btn a:hover {background-position: 0 -342px;}
.red_btn a:hover span {background-position: -275px -374px;}

.gray_btn>a>span, .gray_box>a>span {background-position: -300px -252px;}
.gray_btn a, .gray_box>a {background-position: 0 -224px;}

.gray_short_btn>a>span {background-position: -300px -436px;}
.gray_short_btn a {background-position: 0 -408px;}
.gray_short_btn a:hover {background-position: 0 -464px;}
.gray_short_btn a:hover span {background-position: -300px -492px;}

.gray_big_tall_btn>a>font {font-size: 18px; line-height: 20px; padding-top: 22px;}
.gray_big_tall_btn>a>span {background-position: -300px -765px;}
.gray_big_tall_btn a {background-position: 0 -699px;}
.gray_big_tall_btn a:hover {background-position: 0 -831px}
.gray_big_tall_btn a:hover span {background-position: -300px -897px;}

.gray_tall_btn>a>font {padding-top: 10px;}
.gray_tall_btn>a>span {background-position: -300px -556px;}
.gray_tall_btn a {background-position: 0 -520px;}
.gray_tall_btn a:hover {background-position: 0 -592px;}
.gray_tall_btn a:hover span {background-position: -300px -628px;}

.gray_box a {cursor: default;}
.gray_box a span {display: inline-block;line-height: 15px;vertical-align: top;}
.gray_box a span.left {font-weight: normal;width: 60px;}

.connect_with_fb_btn a {background-position: 0 -663px;height: 23px;margin: 0;width: 170px;}
/* All Button Style Section */


/* All Table Header Section */
div.h1_tbl {
    background-color: #F4F4F4;
    min-height: 15px;
    padding: 5px 15px;
}
div.hc1_tbl{
    border-top: 1px solid #CECECE;
    clear: both;
    float: left;
    padding: 15px 0;
    width: 100%;
}
div.hc1_tbl>div.first {
    padding-left: 15px;
}
div.hc1_tbl>div.last {
    float: right;
    padding-right: 15px;
}
div.h1_tbl div, div.hc1_tbl div{
    float: left;
}
/* All Table Header Section */


/* POP UP */
.popup_close_button {
    background: url("../icons/cancel-square.png") no-repeat scroll left top transparent;
    cursor: pointer;
    height: 16px;
    position: absolute;
    right: 16px;
    top: 16px;
    width: 16px;
    z-index: 181;
}
div.fancy_content_footer {
    border: 0 none;
    bottom:1px;
    display:block;
    height:auto;
    left:1px;
    right:1px;
    top:1px;
    width:auto;
    margin:0;
    position:absolute;
    z-index:100;
}
div.fancy_content_footer td {
    vertical-align: top;
}
div.fancy_box {
    display: none;
    margin: 0;
    padding: 0;
    position: absolute;
    visibility: hidden;
    width: 450px;
    z-index: 850;
}

div.fancy_box_footer {bottom: 33px;display: none;margin: 0;padding: 0;position: absolute;width: 450px;z-index: 850;}
div.fancy_box_footer div.sdrc_mdl2>div.content {background-color: #FFF;margin: 0 5px;}
/* POP UP */

/* Footer Bar */
#footerBar {
    bottom: 0;
    display: block;
    font-size: 12px;
    height: 33px;
    left: 0;
    position: fixed !important;
    width: 100%;
}
.footer_column>a {display: block;margin: 9px 10px;position: relative;}
.footer_column:hover, .footer_column_selected {background-position: 0 -563px;}

#footer_fblogin_td>a, #footer_checkout_td>a#btn_my_cart, #footer_login_td>a#username_trigger {padding-left: 20px;}

#footer_local_td>a {height: 12px;margin: 10px;padding-right: 11px;}
#footer_local_td>a>div.ArrowTopIcon {position: absolute;right: 0;top: 5px;}

#footer_checkout_td>a#btn_my_cart {margin-right: 5px;}
#footer_checkout_td>a#btn_my_cart:hover>span {text-decoration: underline;}
#footer_checkout_td>a#btn_chkout {margin-left: 5px;}
#footer_checkout_td>span, #footer_login_td>span {line-height: 16px;margin: 8px 4px;}

#footer_cart_popup_box {right: -6px;}
#footer_cart_popup_box div.sdrc_mdl2>div.content {width: 440px;}
#footer_cart_popup_content>.row {padding: 10px 15px;}

#whitebox_tooltips {width: 250px;right: -6px;}
#whitebox_tooltips div.sdrc_mdl {background-position: 199px 0;}
#whitebox_tooltips div.sdrc_mdl2>div.content {width: 240px;}
#whitebox_tooltips_content {padding: 10px 20px;}
/* Footer Bar */

/* Form */
div.formRow {
    clear: both;
}
div.formRow>div.lbl{
    float: left;
    line-height: 30px;
    padding-left: 15px;
    width: 120px;
}
div.formRow>div.lf{
    float: left;
}

/* Product Listing Page */
table.gListingMain, table.pListingMain {width:100%;}
table.gListingMain td.img {border-bottom: 1px solid #CECECE; border-right: 1px dotted #CCCCCC; padding: 20px 10px; vertical-align: top; width: 180px;}
table.gListingMain td.img div.top {text-align: center;}
table.gListingMain td.img div.delivery {line-height: 15px;}
table.gListingMain td.img div.delivery>div {width: 100px; margin: 0 auto;}
table.gListingMain td.img div.delivery>div>div {padding-top: 5px;}
.promotionContentBox table.gListingMain td.img {padding-left: 4px;}
table.gListingMain td.prod {border-bottom: 1px solid #CECECE; vertical-align: top;}
table.pListingMain {background-color: #FFF;}
table.pListingMain tr.dtu td {background-color: #f0f0f0;}
table.pListingMain tbody td {padding: 12px 0; background-color: #FFF;}
table.pListingMain tbody td>div {padding-left: 10px;}
table.pListingMain .head {padding: 10px; background-color: #EDEDED;}
table.pListingMain .name {width: 255px; border-top: 1px dotted #CCCCCC;}
table.pListingMain .price {width: 160px; border-top: 1px dotted #CCCCCC;}
table.pListingMain .pl_btn {width: 147px; border-top: 1px dotted #CCCCCC; padding: 7px 0 3px;}
table.pListingMain .foot {width: 542px; padding: 10px; border-top: 1px solid #CECECE;}
table.pListingMain div.ihd1>input {background-color: #FFF; height: 20px;line-height: 20px;}
table.pListingMain td.name > div > div.pl_row {display: block; padding-top: 5px; position: relative;}
table.pListingMain td.name>div>div.dtu_extra_info>span {padding-right:5px;}

/* POP UP Purchase Confirmation Fancy Box */
table.purchase_confirm_box {}
table.purchase_confirm_box td.dm {vertical-align: top; padding: 0 15px 14px; width: 160px}
table.purchase_confirm_box td.dm div.group div.entry {padding-left:25px;padding-top:3px;display:none;}
table.purchase_confirm_box td.dm div.gshow div.entry {display:block}
table.purchase_confirm_box td.dm div.tooltips,
table.purchase_confirm_box td.dm div.dmnote {padding-top: 10px}
table.purchase_confirm_box td.dm div.dmnote div {display:none}
table.purchase_confirm_box td.dm div.dmnote div.noteshow {display:block}
table.purchase_confirm_box td.qty {border-left: 1px dotted #CCCCCC; width: 100px; padding: 10px 15px; vertical-align: top;}
table.purchase_confirm_box td.qty>div.dtu_extra_info>span {padding-right:5px;}
table.purchase_confirm_box td.qty div.shd1 > select {color: #545454;font-size: 11px;height: 26px;}
table.purchase_confirm_box td.ot {border-left: 1px dotted #CCCCCC; min-width: 300px; vertical-align: top;}
table.purchase_confirm_box td.ot td.lbl {border-bottom: 1px dotted #CCCCCC; padding: 10px 15px; line-height: 11px;}
table.purchase_confirm_box td.ot tr.footer td.button {border-bottom: 1px dotted #CCCCCC; padding: 10px; text-align: center;}
table.purchase_confirm_box td.ot tr.footer td.button div.main_btn span.dots {display: inline-block; height: 11px; width: 10px;}
table.purchase_confirm_box td.ot td.paymentPriceValue {border-bottom: 1px dotted #CCCCCC; padding: 10px 15px 10px 0; text-align: right; }
table.purchase_confirm_box td.ot tr.total td,
table.purchase_confirm_box td.ot tr.rebate td {border-bottom: 0 none; padding-bottom: 0;}
table.purchase_confirm_box td.ot tr.total td.paymentPriceValue {font-size: 12px;}
table.purchase_confirm_box td.ot tr.rebate td {padding-top: 0px;}
table.purchase_confirm_box td.ot tr.sc_remain td {border-bottom:0 none; padding-bottom: 14px;}
table.purchase_confirm_box td div.error_msg {background-color: #FFFDDB; margin-top: 10px; display: none;}
table.purchase_confirm_box .lbl {background-color: transparent;border-radius: unset;color: unset;display: table-cell;font-weight: unset;text-shadow: unset;}

.dottedbox>div {border: 1px dotted; padding: 10px;}
.dottedbox>div.extend {border-top: 0 none;}
.scbox div.desc {padding-top: 5px;}
.pfv_header div.ext {}
.pfv_header span {line-height: 16px;}

.non-en body, .non-en td, .non-en div, .non-en span, .non-en p, .non-en font {
    font-family: Georgia,"Times New Roman","宋体",serif;
    font-size: 12px;
}

.non-en span.hd1, .non-en span.hdC1, .non-en span.hdU1, .non-en span.hdL1,
.non-en span.hd2,
.non-en span.hd3,
.non-en span.hd4, .non-en span.hdsU4,
.non-en span.hd5, .non-en span.hds5,
.non-en span.hd6,
.non-en span.cd1,
.non-en a.hd1, .non-en a.ahd1, .non-en a.ahd2, .non-en a.ahd3, .non-en a.ahd3>span.ihr, .non-en a.ahd4, .non-en a.ahd4>span.ihr,
.non-en a.ahd5, .non-en a.ahd5>span.ihr,
.non-en li.mm-item a.mm-item-link, .non-en li.mm-item a.mm-item-link span,
#footerBar {
    font-size: 13px;
}

.non-en span.hds1, .non-en span.hdsC1, .non-en span.hdsU1, .non-en span.hdsL1,
.non-en span.hds2,
.non-en span.hds3,
.non-en span.hds4, .non-en span.hdsU4,
.non-en span.hds5, .non-en span.hds5,
.non-en span.hds6,
.non-en span.cdd_price,
.non-en a.ahds1, .non-en a.ahds2, .non-en a.ahds3, .non-en a.ahds3>span.ihr, .non-en a.ahds4, .non-en a.ahds4>span.ihr,
.non-en a.ahds5, .non-en a.ahds5>span.ihr,
.non-en div.ihd1>input,
.non-en div.shd1>select,
.non-en .sResultLi div.sGames font,
.non-en #sags_box>input,
.non-en .main_btn>a>font {
    font-size: 12px;
}

.wor-small {
    background-image: url("../icons/icon_wor.png");
    display: inline-block;
    vertical-align: middle;
    height: 18px;
    width: 18px;
}

.wor-medium {
    background-image: url("../icons/token-big.png");
    display: inline-block;
    vertical-align: middle;
    height: 59px;
    width: 58px;
}

.tokenIcon{
    background-image: url("../icons/shasso.png");
    background-repeat: no-repeat;
    background-position: -51px -26px;
    width: 18px;
    height: 18px;
}

body.patrick #topLogo a {
    background-image: url("../logo-transparent-patrick.png");
}
body.patrick .sBox {
    background-image: url("../searchbox-patrick.png");
}
body.patrick .bShodow {
    background-image: url("../shadow_wide-patrick.gif");
}
body.patrick #blueBar {
    background-image: url("../box_and_tab-patrick.png");
}
body.patrick #footerBtmContainer div.img_container {
    display: block;
    position: absolute; 
    top: -100px;
}
body.patrick #footerBtmContainer div.img_container > div.img {
    background-image: url("../footer_image-patrick.png");
    display: inline-block;
    height: 293px;
    width: 972px;
}
body.patrick #footerBtm {
    background-color: #A9CD37;
    height: 193px;
}
div.footer_patrick_img {
    clear:both;
    height:20px;
}
.icon_try_beta {
    position: absolute;
    right: 0;
    top: 0;
    z-index: 300;
}
/* User Bar */
#userbar-container {
    height: 36px;
}
#g2g-userbar {
    margin-bottom: 0;
    min-width: 972px;
}

/* overwrite bs 2.3.2 */