.tabHeaderLeft {
	float:left;
	background-image: url(/images/tab_blk_left.gif);
	background-repeat: no-repeat;
	width: 5px;
	height: 30px;
	background-position: bottom;
}

.tabHeaderRight {
	float:left;
	background-image: url(/images/tab_blk_right.gif);
	background-repeat: no-repeat;
	width: 5px;
	height: 30px;
	background-position: bottom;
	
}

.tabHeaderCenter {
	overflow: hidden;
	float:left;
	background: black;
	color:#111;
	padding:0px 0px;
	width: 98%;
	height: 30px;
	background-position: bottom;
	
}
.tabHeaderCenter div{
	cursor: pointer;
}


.tabHeaderCenter div span{
	text-decoration:none!important;
	white-space: nowrap;
	font:12px;
	color: white;
	cursor: pointer;
}

.tabHeaderLeftSelected {
	float:left;
	background-image: url(/images/tab_selected_left.gif);
	background-repeat: no-repeat;
	width: 6px;
	height: 39px;
}

.tabHeaderRightSelected {
	float:left;
	background-image: url(/images/tab_selected_right.gif);
	background-repeat: no-repeat;
	width: 6px;
	height: 39px;

}

.tabHeaderCenterSelected {
	overflow: hidden;
	float:left;
	background-image: url(/images/tab_selected_center.gif);
	background-repeat: repeat-x;
	width: 88%;
	height: 39px;
	padding-top:15px;
	text-align: center;
	color: black;
	cursor: pointer;
}

.tabHeaderCenterSelected span {
	text-decoration:none!important;
	white-space: nowrap;
	font:12px;
	color: black;
}

.tabHeaderCenter div {
	float: left;
	text-align: center;
	height: 30px;
}

.tabHeaderCenter div a {
  text-decoration:none!important;
  font:12px;
  color:white;
  background:#181818;
}

.dottedImage {
	background-image: url(/images/dottedline.gif);
	background-repeat: repeat-x;
	height: 1px;
}

.tabContent {
	clear:both;
	position:relative;
	height:100%;
	width:100%;
	color:black;
	font-size: 11px;
}

.tabContentTable {
	/*border-bottom: solid 1px #CECECE;*/
    background-color: #FFFFFF;
	border-left: solid 1px #CECECE;
	border-right: solid 1px #CECECE;
}

.tabImageContainer {
	padding:5px 10px 5px 10px;
}

.tabInfoContainer {
	padding-left:10px;
}

.rowTabContent {
	background-color:white;
}

.rowTabContentOut {
	background-color:white;
}

.rowTabContentOver {
	background-color:#F4F4F4;

}

.iconGold {
	background-image: url(/images/icon_gold.gif);
	width:25px; 
	height:26px;
	background-repeat: no-repeat;
	background-position: top;
	text-align: center;
	padding-top: 5px;
	padding-left: 2px;
	font-weight: bold;
	margin-left: auto;
    margin-right: auto;
}

.iconGrey {
	background-image: url(/images/icon_grey.gif);
	width:25px; 
	height:26px;
	background-repeat: no-repeat;
	background-position: top;
	text-align: center;
	padding-top: 5px;
	padding-left: 2px;
	font-weight: bold;
	margin-left: auto;
    margin-right: auto;
}

.tabFooterLeft {
	float:left;
	background-position:top;
	background-image: url(/images/box_btm_left.gif);
	background-repeat: no-repeat;
	width:5px;
	height:5px;
}
.tabFooterRight {
	float:right;
	background-image: url(/images/box_btm_right.gif);
	background-repeat: no-repeat;
	width:5px;
	height:5px;
}
.tabFooterCenter {
	float:left;
	width: 100%;
	margin-left:-5px;
	margin-right:-5px;
}
.tabFooterCenterLine {
	height: 5px;
	background-image: url(/images/box_btm_center.gif);
	background-repeat: repeat-x;
	background-position: top;
	margin-left: 5px;
	margin-right: 5px;
}