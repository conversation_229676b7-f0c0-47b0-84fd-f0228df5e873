/*** adding sf-vertical in addition to sf-menu creates a vertical menu ***/
.sf-vertical, .sf-vertical li {
	width:	168px;
	/*border: solid 1px #336699;*/
}
/* this lacks ul at the start of the selector, so the styles from the main CSS file override it where needed */
.sf-vertical li:hover ul,
.sf-vertical li.sfHover ul {
	left:	178px; /* match ul width */
	top:	0;
}

/*** alter arrow directions ***/
.sf-vertical .sfHover a .sf-sub-indicator { 
	top: 0em;
	right: -0.8em;
	background-image: url('../arw_selected.gif');
} /* IE6 gets solid image only */


.sf-vertical > .sfHover > a > .sf-sub-indicator {
	top: 0em;
	right: -0.8em;
	background-image: url('../arw_selected.gif');
} /* use translucent arrow for modern browsers*/


/* hover arrow direction for modern browsers*/
.sf-vertical a:focus > .sf-sub-indicator,
.sf-vertical a:hover > .sf-sub-indicator,
.sf-vertical a:active > .sf-sub-indicator,
.sf-vertical li:hover > a > .sf-sub-indicator,
.sf-vertical li.sfHover > a > .sf-sub-indicator {
	background-position: -10px 0px; /* arrow hovers for modern browsers*/
}