
/*** ESSENTIAL STYLES ***/
.sf-menu, .sf-menu * {margin: 0; padding: 0; list-style: none;}
.sf-menu {line-height: 1.0;}
.sf-menu ul {margin: 0 0 0 2px; position: absolute; top: -999em; width: 120px; border: 1px solid #CCC; /* left offset of submenus need to match (see below) */}
.sf-menu ul li {width: 100%; /*margin-top: 5px;*/}
.sf-menu li:hover {visibility: inherit; /* fixes IE7 'sticky bug' */}
.sf-menu li {float: left; position: relative;}
.sf-menu a {display: block; position: relative;}
.sf-menu li:hover ul,
.sf-menu li.sfHover ul {/*left: 0em; top: 2.5em;*/ border: 1px solid #CCC; background: #fff; /* match top ul list item height */ z-index: 99;}
ul.sf-menu li:hover li ul, 
ul.sf-menu li.sfHover li ul {padding: 1px; top: 1em; /*-999em;*/}
ul.sf-menu li li:hover ul,
ul.sf-menu li li.sfHover ul {/*left: 10em; /* match ul width */ top: 0;}
ul.sf-menu li li:hover li ul,
ul.sf-menu li li.sfHover li ul {top: -999em;}
ul.sf-menu li li li:hover ul,
ul.sf-menu li li li.sfHover ul {/*left: 10em; /* match ul width */ top: 0;}


/*** DEMO SKIN ***/
.sf-menu {float: left; }
.sf-menu a {text-decoration: none;}
.sf-menu a, .sf-menu a:visited  {/* visited pseudo selector so IE6 applies text colour*/color: #000;}
.sf-menu li {background: transparent;}
.sf-menu li li {padding: .8em 0em .0em 1em; background: transparent;}
.sf-menu li li li {background: transparent;}
.sf-menu li:hover, .sf-menu li.sfHover
.sf-menu a:focus, .sf-menu a:hover, .sf-menu a:active {color: #999;	/*outline: 0;*/}


/*** arrows **/
.sf-menu a.sf-with-ul {
	padding-right: 	0em;
	min-width:		1px; /* trigger IE7 hasLayout so spans position accurately */
}

.sf-sub-indicator { 
	position:		absolute;
	display:		block;
	width:			10px;
	height:			19px;
	text-indent: 	-999em;
	overflow:		hidden;
	/*background:		url('../images/arw_selected.gif') no-repeat -10px -100px; /* 8-bit indexed alpha png. IE6 gets solid image only */
}

a > .sf-sub-indicator {  /* give all except IE6 the correct values */
	top:			.8em;
	background-position: 0 -10px; /* use translucent arrow for modern browsers*/
}

/* apply hovers to modern browsers */
a:focus > .sf-sub-indicator,
a:hover > .sf-sub-indicator,
a:active > .sf-sub-indicator,
li:hover > a > .sf-sub-indicator,
li.sfHover > a > .sf-sub-indicator {
	text-decoration: underline;
	background-position: -10px 0px; /* arrow hovers for modern browsers*/
	/*background-image: url('/images/arw_selected.gif');
	right:		0em;	
	top: 		0em;
	z-index: 99999;*/
}

/* point right for anchors in subs */
.sf-menu ul .sf-sub-indicator {background-position:  0 0;}
.sf-menu ul a > .sf-sub-indicator {background-position:  0 0;}

/* apply hovers to modern browsers */
.sf-menu ul a:focus > .sf-sub-indicator,
.sf-menu ul a:hover > .sf-sub-indicator,
.sf-menu ul a:active > .sf-sub-indicator,
.sf-menu ul li:hover > a > .sf-sub-indicator,
.sf-menu ul li.sfHover > a > .sf-sub-indicator {
	background-position: -10px 0; /* arrow hovers for modern browsers*/
}


/*** shadows for all but IE6 ***/
.sf-shadow ul {padding: 0 10px 10px 0;}
.sf-shadow ul.sf-shadow-off {background: transparent;}