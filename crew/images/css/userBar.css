/* Regional Flags */
div.flag div {
    display: block;
    float: left;
    width: 16px;
    height: 11px;
    line-height: 11px;
    font: 1px monospace;
    background-image: url("../flags_matrix.gif");
    margin: 1px; /*2px 4px 2px 0;*/
}
div.flag.AD div { background-position:-16px -44px; }
div.flag.AE div { background-position:-16px -55px; }
div.flag.AF div { background-position:-16px -66px; }
div.flag.AG div { background-position:-16px -77px; }
div.flag.AI div { background-position:-16px -99px; }
div.flag.AL div { background-position:-16px -132px; }
div.flag.AM div { background-position:-16px -143px; }
div.flag.AN div { background-position:-16px -154px; }
div.flag.AO div { background-position:-16px -165px; }
div.flag.AQ div { background-position:-16px -187px; }
div.flag.AR div { background-position:-16px -198px; }
div.flag.AS div { background-position:-16px -209px; }
div.flag.AT div { background-position:-16px -220px; }
div.flag.AU div { background-position:-16px -231px; }
div.flag.AW div { background-position:-16px -253px; }
div.flag.AX div { background-position:-16px -264px; }
div.flag.AZ div { background-position:-16px -286px; }
div.flag.BA div { background-position:-32px -11px; }
div.flag.BB div { background-position:-32px -22px; }
div.flag.BD div { background-position:-32px -44px; }
div.flag.BE div { background-position:-32px -55px; }
div.flag.BF div { background-position:-32px -66px; }
div.flag.BG div { background-position:-32px -77px; }
div.flag.BH div { background-position:-32px -88px; }
div.flag.BI div { background-position:-32px -99px; }
div.flag.BJ div { background-position:-32px -110px; }
div.flag.BM div { background-position:-32px -143px; }
div.flag.BN div { background-position:-32px -154px; }
div.flag.BO div { background-position:-32px -165px; }
div.flag.BR div { background-position:-32px -198px; }
div.flag.BS div { background-position:-32px -209px; }
div.flag.BT div { background-position:-32px -220px; }
div.flag.BV div { background-position:-32px -242px; }
div.flag.BW div { background-position:-32px -253px; }
div.flag.BY div { background-position:-32px -275px; }
div.flag.BZ div { background-position:-32px -286px; }
div.flag.CA div { background-position:-48px -11px; }
div.flag.CC div { background-position:-48px -33px; }
div.flag.CD div { background-position:-48px -44px; }
div.flag.CF div { background-position:-48px -66px; }
div.flag.CG div { background-position:-48px -77px; }
div.flag.CH div { background-position:-48px -88px; }
div.flag.CI div { background-position:-48px -99px; }
div.flag.CK div { background-position:-48px -121px; }
div.flag.CL div { background-position:-48px -132px; }
div.flag.CM div { background-position:-48px -143px; }
div.flag.CN div { background-position:-48px -154px; }
div.flag.CO div { background-position:-48px -165px; }
div.flag.CR div { background-position:-48px -198px; }
div.flag.CS div { background-position:-48px -209px; }
div.flag.CU div { background-position:-48px -231px; }
div.flag.CV div { background-position:-48px -242px; }
div.flag.CX div { background-position:-48px -264px; }
div.flag.CY div { background-position:-48px -275px; }
div.flag.CZ div { background-position:-48px -286px; }
div.flag.DE div { background-position:-64px -55px; }
div.flag.DJ div { background-position:-64px -110px; }
div.flag.DK div { background-position:-64px -121px; }
div.flag.DM div { background-position:-64px -143px; }
div.flag.DO div { background-position:-64px -165px; }
div.flag.DZ div { background-position:-64px -286px; }
div.flag.EC div { background-position:-80px -33px; }
div.flag.EE div { background-position:-80px -55px; }
div.flag.EG div { background-position:-80px -77px; }
div.flag.EH div { background-position:-80px -88px; }
div.flag.ER div { background-position:-80px -198px; }
div.flag.ES div { background-position:-80px -209px; }
div.flag.ET div { background-position:-80px -220px; }
div.flag.FI div { background-position:-96px -99px; }
div.flag.FJ div { background-position:-96px -110px; }
div.flag.FK div { background-position:-96px -121px; }
div.flag.FM div { background-position:-96px -143px; }
div.flag.FO div { background-position:-96px -165px; }
div.flag.FR div { background-position:-96px -198px; }
div.flag.FX div { background-position:-96px -264px; }
div.flag.GA div { background-position:-112px -11px; }
div.flag.GB div { background-position:-112px -22px; }
div.flag.GD div { background-position:-112px -44px; }
div.flag.GE div { background-position:-112px -55px; }
div.flag.GF div { background-position:-112px -66px; }
div.flag.GG div { background-position:-112px -77px; }
div.flag.GH div { background-position:-112px -88px; }
div.flag.GI div { background-position:-112px -99px; }
div.flag.GL div { background-position:-112px -132px; }
div.flag.GM div { background-position:-112px -143px; }
div.flag.GN div { background-position:-112px -154px; }
div.flag.GP div { background-position:-112px -176px; }
div.flag.GQ div { background-position:-112px -187px; }
div.flag.GR div { background-position:-112px -198px; }
div.flag.GS div { background-position:-112px -209px; }
div.flag.GT div { background-position:-112px -220px; }
div.flag.GU div { background-position:-112px -231px; }
div.flag.GW div { background-position:-112px -253px; }
div.flag.GY div { background-position:-112px -275px; }
div.flag.HK div { background-position:-128px -121px; }
div.flag.HM div { background-position:-128px -143px; }
div.flag.HN div { background-position:-128px -154px; }
div.flag.HR div { background-position:-128px -198px; }
div.flag.HT div { background-position:-128px -220px; }
div.flag.HU div { background-position:-128px -231px; }
div.flag.ID div { background-position:-144px -44px; }
div.flag.IE div { background-position:-144px -55px; }
div.flag.IL div { background-position:-144px -132px; }
div.flag.IN div { background-position:-144px -154px; }
div.flag.IO div { background-position:-144px -165px; }
div.flag.IQ div { background-position:-144px -187px; }
div.flag.IR div { background-position:-144px -198px; }
div.flag.IS div { background-position:-144px -209px; }
div.flag.IT div { background-position:-144px -220px; }
div.flag.JM div { background-position:-160px -143px; }
div.flag.JO div { background-position:-160px -165px; }
div.flag.JP div { background-position:-160px -176px; }
div.flag.KE div { background-position:-176px -55px; }
div.flag.KG div { background-position:-176px -77px; }
div.flag.KH div { background-position:-176px -88px; }
div.flag.KI div { background-position:-176px -99px; }
div.flag.KM div { background-position:-176px -143px; }
div.flag.KN div { background-position:-176px -154px; }
div.flag.KP div { background-position:-176px -176px; }
div.flag.KR div { background-position:-176px -198px; }
div.flag.KW div { background-position:-176px -253px; }
div.flag.KY div { background-position:-176px -275px; }
div.flag.KZ div { background-position:-176px -286px; }
div.flag.LA div { background-position:-192px -11px; }
div.flag.LB div { background-position:-192px -22px; }
div.flag.LC div { background-position:-192px -33px; }
div.flag.LI div { background-position:-192px -99px; }
div.flag.LK div { background-position:-192px -121px; }
div.flag.LR div { background-position:-192px -198px; }
div.flag.LS div { background-position:-192px -209px; }
div.flag.LT div { background-position:-192px -220px; }
div.flag.LU div { background-position:-192px -231px; }
div.flag.LV div { background-position:-192px -242px; }
div.flag.LY div { background-position:-192px -275px; }
div.flag.MA div { background-position:-208px -11px; }
div.flag.MC div { background-position:-208px -33px; }
div.flag.MD div { background-position:-208px -44px; }
div.flag.ME div { background-position:-208px -55px; }
div.flag.MG div { background-position:-208px -77px; }
div.flag.MH div { background-position:-208px -88px; }
div.flag.MK div { background-position:-208px -121px; }
div.flag.ML div { background-position:-208px -132px; }
div.flag.MM div { background-position:-208px -143px; }
div.flag.MN div { background-position:-208px -154px; }
div.flag.MO div { background-position:-208px -165px; }
div.flag.MP div { background-position:-208px -176px; }
div.flag.MQ div { background-position:-208px -187px; }
div.flag.MR div { background-position:-208px -198px; }
div.flag.MS div { background-position:-208px -209px; }
div.flag.MT div { background-position:-208px -220px; }
div.flag.MU div { background-position:-208px -231px; }
div.flag.MV div { background-position:-208px -242px; }
div.flag.MW div { background-position:-208px -253px; }
div.flag.MX div { background-position:-208px -264px; }
div.flag.MY div { background-position:-208px -275px; }
div.flag.MZ div { background-position:-208px -286px; }
div.flag.NA div { background-position:-224px -11px; }
div.flag.NC div { background-position:-224px -33px; }
div.flag.NE div { background-position:-224px -55px; }
div.flag.NF div { background-position:-224px -66px; }
div.flag.NG div { background-position:-224px -77px; }
div.flag.NI div { background-position:-224px -99px; }
div.flag.NL div { background-position:-224px -132px; }
div.flag.NO div { background-position:-224px -165px; }
div.flag.NP div { background-position:-224px -176px; }
div.flag.NR div { background-position:-224px -198px; }
div.flag.NU div { background-position:-224px -231px; }
div.flag.NZ div { background-position:-224px -286px; }
div.flag.OM div { background-position:-240px -143px; }
div.flag.PA div { background-position:-256px -11px; }
div.flag.PE div { background-position:-256px -55px; }
div.flag.PF div { background-position:-256px -66px; }
div.flag.PG div { background-position:-256px -77px; }
div.flag.PH div { background-position:-256px -88px; }
div.flag.PK div { background-position:-256px -121px; }
div.flag.PL div { background-position:-256px -132px; }
div.flag.PM div { background-position:-256px -143px; }
div.flag.PN div { background-position:-256px -154px; }
div.flag.PR div { background-position:-256px -198px; }
div.flag.PS div { background-position:-256px -209px; }
div.flag.PT div { background-position:-256px -220px; }
div.flag.PW div { background-position:-256px -253px; }
div.flag.PY div { background-position:-256px -275px; }
div.flag.QA div { background-position:-272px -11px; }
div.flag.RE div { background-position:-288px -55px; }
div.flag.RO div { background-position:-288px -165px; }
div.flag.RS div { background-position:-288px -209px; }
div.flag.RU div { background-position:-288px -231px; }
div.flag.RW div { background-position:-288px -253px; }
div.flag.SA div { background-position:-304px -11px; }
div.flag.SB div { background-position:-304px -22px; }
div.flag.SC div { background-position:-304px -33px; }
div.flag.SD div { background-position:-304px -44px; }
div.flag.SE div { background-position:-304px -55px; }
div.flag.SG div { background-position:-304px -77px; }
div.flag.SH div { background-position:-304px -88px; }
div.flag.SI div { background-position:-304px -99px; }
div.flag.SJ div { background-position:-304px -110px; }
div.flag.SK div { background-position:-304px -121px; }
div.flag.SL div { background-position:-304px -132px; }
div.flag.SM div { background-position:-304px -143px; }
div.flag.SN div { background-position:-304px -154px; }
div.flag.SO div { background-position:-304px -165px; }
div.flag.SR div { background-position:-304px -198px; }
div.flag.SS div { background-position:-304px -209px; }
div.flag.ST div { background-position:-304px -220px; }
div.flag.SV div { background-position:-304px -242px; }
div.flag.SY div { background-position:-304px -275px; }
div.flag.SZ div { background-position:-304px -286px; }
div.flag.TC div { background-position:-320px -33px; }
div.flag.TD div { background-position:-320px -44px; }
div.flag.TF div { background-position:-320px -66px; }
div.flag.TG div { background-position:-320px -77px; }
div.flag.TH div { background-position:-320px -88px; }
div.flag.TJ div { background-position:-320px -110px; }
div.flag.TK div { background-position:-320px -121px; }
div.flag.TL div { background-position:-320px -132px; }
div.flag.TM div { background-position:-320px -143px; }
div.flag.TN div { background-position:-320px -154px; }
div.flag.TO div { background-position:-320px -165px; }
div.flag.TP div { background-position:-320px -176px; }
div.flag.TR div { background-position:-320px -198px; }
div.flag.TT div { background-position:-320px -220px; }
div.flag.TV div { background-position:-320px -242px; }
div.flag.TW div { background-position:-320px -253px; }
div.flag.TZ div { background-position:-320px -286px; }
div.flag.UA div { background-position:-336px -11px; }
div.flag.UG div { background-position:-336px -77px; }
div.flag.UM div { background-position:-336px -143px; }
div.flag.US div { background-position:-336px -209px; }
div.flag.UY div { background-position:-336px -275px; }
div.flag.UZ div { background-position:-336px -286px; }
div.flag.VA div { background-position:-352px -11px; }
div.flag.VC div { background-position:-352px -33px; }
div.flag.VE div { background-position:-352px -55px; }
div.flag.VG div { background-position:-352px -77px; }
div.flag.VI div { background-position:-352px -99px; }
div.flag.VN div { background-position:-352px -154px; }
div.flag.VU div { background-position:-352px -231px; }
div.flag.WF div { background-position:-368px -66px; }
div.flag.WS div { background-position:-368px -209px; }
div.flag.YE div { background-position:-400px -55px; }
div.flag.YT div { background-position:-400px -220px; }
div.flag.YU div { background-position:-400px -231px; }
div.flag.ZA div { background-position:-416px -11px; }
div.flag.ZM div { background-position:-416px -143px; }
div.flag.ZW div { background-position:-416px -253px; }
div.flag.ZR div { background-position:-416px -198px; }

#g2g-userbar.navbar-fixed-top .navbar-inner {
    border-width: 0;
}
#g2g-userbar.navbar .nav > li > a {
    background-color: #2f2f2f;
    font-size: 12px;
    text-shadow: none;
    color: #ffffff;
    padding: 8px 10px;
}
#g2g-userbar.navbar .nav-pills > li > a {
    margin-top: 3px;
    margin-bottom: 3px;
    font-size: 11px;
}
#g2g-userbar.navbar .nav li.dropdown.open > .dropdown-toggle, 
#g2g-userbar.navbar .nav li.dropdown.active > .dropdown-toggle, 
#g2g-userbar.navbar .nav li.dropdown.open.active > .dropdown-toggle {
    background-color: #474747;
}
#g2g-userbar-inner.navbar-inner {
    background-color: #222222;
    background-image: none;
    min-height: 37px;
    filter: none;
}
#g2g-userbar-inner > .container {
    width: 962px !important
}
#g2g-userbar-collapsed.btn {
    background-color: transparent;
    background-image: none;
    border: none;
    box-shadow: none;
}
form.regional-form li {
    padding: 10px 20px;
}
.dropdown.open > a > i {
    color: #faa732;
}
#reg-setting-dd .g2g-select2 {
    margin-bottom: 20px;
}

/* Connect with */
#g2g-userbar.navbar .nav > li > a.connect-with {
    display: table;
    border-collapse: separate;
    padding: 7px 10px;
}
.connect-with > div#login-link {border-left: none;}
.connect-with > div {
    display: table-cell;
    vertical-align: top;
    font-size: 11px;
    border-left: 5px solid #2f2f2f;
}
.connect-with > div.sns {border-left: 3px solid #222222;}
.connect-with > div#login-link:hover, .connect-with > div.sns:hover {
    cursor: pointer;
}

/* Network */
.network {
    padding-right: 20px;
}
.network, .network-link {
    display: table-cell;
    vertical-align: middle;
}
.network-logo {
    background-image: url("../logo_portal_small.png");
    width: 60px;
    height: 60px;
}
.network-logo.network-ogm { background-position: 0 0; }
.network-logo.network-g2g { background-position: -60px 0; }
.network-logo.network-gmz { background-position: -120px 0; }

@media (min-width: 768px) and (max-width: 980px) {
    form.regional-form li {
        color: #FFFFFF;
    }
    #reg-setting-dd .g2g-select2.span3 {
        width: 100%;
    }
}

@media (max-width: 767px) {
    form.regional-form li {
        color: #FFFFFF;
    }
}

/* ============================================= */
.nav-pills > li > a {
    border-radius: 5px;
    margin-bottom: 2px;
    margin-top: 2px;
    padding-bottom: 8px;
    padding-top: 8px;
}
.nav-tabs > li > a, .nav-pills > li > a {
    line-height: 14px;
    margin-right: 2px;
    padding-left: 12px;
    padding-right: 12px;
}
.nav > li > a {
    display: block;
}


.default-avatar {
  background-image: url("../icons/sns.png");
  background-position: -51px -33px;
  background-repeat: repeat;
}
.avatar {
  float: left;
  height: 16px;
  margin-right: 3px;
  width: 16px;
}
.sns {
    background-image: url("../icons/sns.png");
    background-repeat: repeat;
    height: 17px;
    width: 17px;
}
.sns.fb {
    background-position: 0 -33px;
}
.sns.tw {
    background-position: -16px -33px;
}
.sns.gp {
    background-position: -33px -33px;
}

.navbar-fixed-top, .navbar-fixed-bottom, .navbar-static-top {
    margin-left: 0;
    margin-right: 0;
}