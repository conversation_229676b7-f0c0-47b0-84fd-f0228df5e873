<?php

class eclub_api {

    const API_PROVIDER = 'ECLUB';
    const FLAG_INIT = 'I';
    const FLAG_NEW = 'N';
    const FLAG_UPLOADING = 'U';
    const FLAG_UPLOADED = 'S';
    const FLAG_ERROR = 'E';
    const GET_METHOD = 'order_submission';
    const CHECK_METHOD = 'order_check';
    const DEFAULT_ADMIN_EMAIL = 'system';
    const SUCCESS_STATUSFLAG = 1;

    private static $SKU_SUPPORT = array(
        'ESW05_P' => array('amt' => 5.40, 'settle_amt' => 4.5),
        'ESW10_P' => array('amt' => 10.80, 'settle_amt' => 9),
        'ESW15_P' => array('amt' => 16.20, 'settle_amt' => 13.5),
        'ESW20_P' => array('amt' => 21.60, 'settle_amt' => 18),
        'ESW30_P' => array('amt' => 32.40, 'settle_amt' => 27),
        'ESW50_P' => array('amt' => 54, 'settle_amt' => 45),
        'ESW60_P' => array('amt' => 64.80, 'settle_amt' => 54),
        'ESW05MYR_P' => array('amt' => 5, 'settle_amt' => 1.53),
        'ESW20MYR_P' => array('amt' => 20, 'settle_amt' => 6.11),
        'ESW50MYR_P' => array('amt' => 50, 'settle_amt' => 15.28),
        'ESW100MYR_P' => array('amt' => 100, 'settle_amt' => 30.56),
        'ESW200MYR_P' => array('amt' => 200, 'settle_amt' => 61.11),
        'ESW05SGD_P' => array('amt' => 5, 'settle_amt' => 3.88),
        'ESW10SGD_P' => array('amt' => 10, 'settle_amt' => 7.77),
        'ESW20SGD_P' => array('amt' => 20, 'settle_amt' => 15.53),
        'ESW30SGD_P' => array('amt' => 30, 'settle_amt' => 23.30),
        'ESW50SGD_P' => array('amt' => 50, 'settle_amt' => 38.84),
        'ESW75SGD_P' => array('amt' => 75, 'settle_amt' => 58.25),
        'ESW50THB_P' => array('amt' => 50, 'settle_amt' => 1.52),
        'ESW200THB_P' => array('amt' => 200, 'settle_amt' => 6.07),
        'ESW350THB_P' => array('amt' => 350, 'settle_amt' => 10.62),
        'ESW1000THB_P' => array('amt' => 1000, 'settle_amt' => 30.35),
        'ESW2000THB_P' => array('amt' => 2000, 'settle_amt' => 60.69),
        'ESW50PHP_P' => array('amt' => 50, 'settle_amt' => 1.12),
        'ESW250PHP_P' => array('amt' => 250, 'settle_amt' => 5.57),
        'ESW500PHP_P' => array('amt' => 500, 'settle_amt' => 11.14),
        'ESW1000PHP_P' => array('amt' => 1000, 'settle_amt' => 22.28),
        'ESW2200PHP_P' => array('amt' => 2200, 'settle_amt' => 49.03),
        'ESW12000IDR_P' => array('amt' => 12000, 'settle_amt' => 0.99),
        'ESW45000IDR_P' => array('amt' => 45000, 'settle_amt' => 3.72),
        'ESW60000IDR_P' => array('amt' => 60000, 'settle_amt' => 4.96),
        'ESW90000IDR_P' => array('amt' => 90000, 'settle_amt' => 7.43),
        'ESW120000IDR_P' => array('amt' => 120000, 'settle_amt' => 9.92),
        'ESW250000IDR_P' => array('amt' => 250000, 'settle_amt' => 20.66),
        'ESW400000IDR_P' => array('amt' => 400000, 'settle_amt' => 33.05),
        'ESW600000IDR_P' => array('amt' => 600000, 'settle_amt' => 49.57),
    );
    private $api_url,
            $api_signature,
            $api_email,
            $api_password,
            $request_method, $request_transactioncode, $request_quantity,
            $resp_transactioncode, $resp_items, $resp_status, $resp_error, $resp_ordernumber,
            $curl_obj, $cpc_obj;

    public function __construct() {
        include_once(DIR_WS_CLASSES . 'curl.php');
        include_once(DIR_WS_CLASSES . 'custom_product_code.php');
        include_once(DIR_WS_LANGUAGES . 'english.php');

        $this->api_url = ECSTORE_API_URL;
        $this->api_signature = ECSTORE_API_KEY;
        $this->api_email = ECSTORE_API_EMAIL;
        $this->api_password = ECSTORE_API_PASSWORD;
        $this->api_key = ECSTORE_API_KEY;

        $this->curl_obj = new curl();
        $this->curl_obj->ssl_verification = ECSTORE_API_SSL_ENABLED;

        $this->cpc_obj = new custom_product_code();
    }

    public static function isSupportedSKU($sku) {
        return isset(self::$SKU_SUPPORT[$sku]);
    }

    private function generateSignature() {
        return merchant_signature($this->api_key . md5($this->api_password) . $this->request_transactioncode);
    }

    private function generateRequestXML($sku) {
        $return_xml = '';

        switch ($this->request_method) {
            case self::CHECK_METHOD:
                $return_xml = '<?xml version="1.0" encoding="UTF8" ?>' .
                        '<request>' .
                        '<Type>' . $this->request_method . '</Type>' .
                        '<MerchantEmail>' . $this->api_email . '</MerchantEmail>' .
                        '<Signature>' . $this->generateSignature() . '</Signature>' .
                        '<TransactionCode>' . $this->request_transactioncode . '</TransactionCode>' .
                        '</request>';
                break;
            case self::GET_METHOD:
                $return_xml = '<?xml version="1.0" encoding="UTF8" ?>' .
                        '<request>' .
                        '<Type>' . self::GET_METHOD . '</Type>' .
                        '<MerchantEmail>' . $this->api_email . '</MerchantEmail>' .
                        '<Signature>' . $this->generateSignature() . '</Signature>' .
                        '<TransactionCode>' . $this->request_transactioncode . '</TransactionCode>' .
                        '<Items>' .
                        '<Item>' .
                        '<SKU>' . $sku . '</SKU>' .
                        '<Qty>' . $this->request_quantity . '</Qty>' .
                        '</Item>' .
                        '</Items>' .
                        '</request>';
                break;
        }

        return $return_xml;
    }

    private function setMethod($method) {
        if (!is_null($method)) {
            $this->request_method = $method;
        }
    }

    private function setTransCode($trans_code) {
        $this->request_transactioncode = $trans_code;
    }

    private function setQuantity($qty) {
        $this->request_quantity = (int) $qty;
    }

    public function getCode($sku, $qty, $trans_code) {
        $return_bool = FALSE;

        if (self::isSupportedSKU($sku)) {
            $this->setMethod(self::GET_METHOD);
            $this->setQuantity($qty);

            if ($trans_code = $this->logAPIData($sku, self::FLAG_INIT)) {
                $this->setTransCode($trans_code);
                $return_bool = $this->preRequest($sku);
            }
        }

        return $return_bool;
    }

    private function getAmount($sku) {
        $return_amt = 0;

        if (isset(self::$SKU_SUPPORT[$sku])) {
            $return_amt = self::$SKU_SUPPORT[$sku]['amt'];
        }

        return $return_amt;
    }

    private function getSettleAmount($sku) {
        $return_amt = 0;

        if (isset(self::$SKU_SUPPORT[$sku])) {
            $return_amt = self::$SKU_SUPPORT[$sku]['settle_amt'];
        }

        return $return_amt;
    }

    public function checkOrder($sku, $trans_code) {
        $return_bool = FALSE;

        if (self::isSupportedSKU($sku)) {
            $this->setMethod(self::CHECK_METHOD);
            $this->setTransCode($trans_code);

            $return_bool = $this->preRequest($sku);
        }

        return $return_bool;
    }

    private function loadAPIData($raw_response = NULL, $data_array = array()) {
        if (!is_null($raw_response)) {
            $data_array = $this->parseXML($raw_response);
        }

        if (isset($data_array['transactioncode']) && !is_null($data_array['transactioncode'])) {
            $this->resp_transactioncode = $data_array['transactioncode'];
        }

        if (isset($data_array['items']) && is_array($data_array['items'])) {
            $items_array = isset($data_array['items']['item'][0]) ? $data_array['items']['item'] : array($data_array['items']['item']);

            foreach ($items_array as $idx => $item_array) {
                if (isset($item_array['codes']['code'])) {
                    $code_array = is_array($item_array['codes']['code']) ? $item_array['codes']['code'] : array($item_array['codes']['code']);

                    foreach ($code_array as $code) {
                        $this->resp_items[] = array(
                            'log_id' => '',
                            'token' => $code,
                            'sku' => $item_array['sku']
                        );
                    }
                }
            }
        }

        if (isset($data_array['status']) && !is_null($data_array['status'])) {
            $this->resp_status = $data_array['status'];
        }

        if (isset($data_array['ordernumber']) && !is_null($data_array['ordernumber'])) {
            $this->resp_ordernumber = is_string($data_array['ordernumber']) ? $data_array['ordernumber'] : '';
        }

        if (isset($data_array['sysmessage']) && !is_null($data_array['sysmessage'])) {
            $this->resp_error = $data_array['sysmessage'] != 'successful' ? $data_array['sysmessage'] : '';
        }
    }

    public function preRequest($sku, $api_method = NULL, $total_try = 0) {
        $return_bool = FALSE;

        if ($total_try < 3) {
            $this->resetAPIData();
            $this->loadAPIData($this->curl_obj->curl_post($this->api_url, $this->generateRequestXML($sku)));

            if ($this->logAPIData($sku)) {
                $return_bool = TRUE;
            } else {
                $return_bool = $this->preRequest($sku, $api_method, ++$total_try);
                $this->reportError(array('curlError' => $this->curl_obj->get_error(), 'sku' => $sku, 'method' => $this->request_method, 'try_count' => $total_try), 'preRequest');
            }
        }

        return $return_bool;
    }

    public function processBatchRestock($product_id, $order_product_store_price, $sku, $qty, $extra_params = array()) {
        $custom_products_code_array = array();
        $code_status = isset($extra_params['code_status']) ? $extra_params['code_status'] : '1';
        $orders_products_id = isset($extra_params['orders_products_id']) ? $extra_params['orders_products_id'] : '';

        try {
            for ($count = 0; $count < $qty; $count++) {
                if ($this->getCode($sku, 1, $orders_products_id) !== FALSE) {
                    foreach ($this->resp_items as $code_array) {
                        $custom_products_code_id = $this->uploadCode($code_array['log_id'], $code_array['token'], $product_id, $code_status);

                        // create cd key file
                        if ($custom_products_code_id !== FALSE) {
                            $custom_products_code_array[] = $custom_products_code_id;
                        }
                    }
                }
            }
        } catch (Exception $e) {
            $this->reportError(array('product_id' => $product_id, 'sku' => $sku, 'qty' => $qty, 'e' => $e->getMessage()), 'processBatchRestock()');
        }

        $this->addProductAvailableQty($product_id, $custom_products_code_array);

        return $custom_products_code_array !== array();
    }

    private function resetAPIData($default = '') {
        foreach (get_class_vars(get_class($this)) as $name => $value) {
            if (substr($name, 0, 5) == 'resp_') {
                $this->$name = $default;
            }
        }
    }

    private function parseXML($raw_data, $return_array = array()) {
        if (is_string($raw_data)) {
            $xml = simplexml_load_string($raw_data);
            $raw_data = (array) $xml;
        }

        foreach ($raw_data as $key => $value) {
            $key = strtolower($key);

            if (is_object($value) || is_array($value)) {
                $return_array[$key] = $this->parseXML((array) $value);
            } else {
                $return_array[$key] = $value;
            }
        }

        return $return_array;
    }

    private function addProductAvailableQty($product_id, $cdkey_id_arr) {
        global $log_object;

        if (is_object($log_object)) {
            $adj_qty = count($cdkey_id_arr);

            if ($adj_qty) {
                $products_quantity_select_sql = "	SELECT products_quantity, products_actual_quantity 
                                                    FROM " . TABLE_PRODUCTS . "
                                                    WHERE products_id=" . $product_id;
                $products_quantity_result_sql = tep_db_query($products_quantity_select_sql);
                if ($products_quantity_row = tep_db_fetch_array($products_quantity_result_sql)) {
                    $old_prod_available_qty = $products_quantity_row['products_quantity'];
                    $old_prod_actual_qty = $products_quantity_row['products_actual_quantity'];
                    $new_prod_available_qty = $old_prod_available_qty + $adj_qty;
                    $new_prod_actual_qty = $old_prod_actual_qty + $adj_qty;

                    $product_sql = "UPDATE " . TABLE_PRODUCTS . " 
                                        SET products_quantity = IF(products_quantity IS NULL, 0, products_quantity) + " . $adj_qty . ", 
                                            products_actual_quantity = IF(products_actual_quantity IS NULL, 0, products_actual_quantity) + " . $adj_qty . " 
                                    WHERE products_id = " . $product_id;
                    if (tep_db_query($product_sql)) {
                        $log_object->insert_log($product_id, 'products_quantity', $old_prod_available_qty . ':~:' . $old_prod_actual_qty, $new_prod_available_qty . ':~:' . $new_prod_actual_qty, sprintf(LOG_QTY_ADJUST, ''), sprintf(LOG_CDKEY_ID_STR, implode(',', $cdkey_id_arr)));
                    }

                    return TRUE;
                }
            }
        } else {
            $this->reportError('Error: Missing object log_object', 'addProductAvailableQty()');
        }

        return FALSE;
    }

    /*
     * status : 0 - sold
     * status : 1 - available
     * status : -2 - on hold
     */

    private function uploadCode($log_id, $token, $product_id, $status = '0') {
        $return_bool = FALSE;
        $timestamp = date('Y-m-d H:i:s');

        if ($log_id) {
            $insert_array = array(
                'products_id' => $product_id,
                'status_id' => $status,
                'file_name' => $log_id,
                'file_type' => 'soft',
                'code_date_added' => $timestamp,
                'code_date_modified' => $timestamp,
                'code_uploaded_by' => self::DEFAULT_ADMIN_EMAIL,
                'remarks' => self::API_PROVIDER . '_API'
            );

            if (tep_db_perform(TABLE_CUSTOM_PRODUCTS_CODE, $insert_array)) {
                $API_flag = self::FLAG_UPLOADING;
                $custom_products_code_id = tep_db_insert_id();
                $key_string = tep_encrypt_data(base64_encode($token));

                if ($this->cpc_obj->uploadCode($key_string, $custom_products_code_id, $product_id, $timestamp) === TRUE) {
                    $API_flag = self::FLAG_UPLOADED;
                    $return_bool = $custom_products_code_id;
                }

                $this->logAPIData('', $API_flag, $log_id, $return_bool);
            }
        }

        return $return_bool;
    }

    private function logAPIData($sku, $flag_state = '', $log_id = 0, $custom_products_code_id = '') {
        $return_init = 0;
        $flag_state = $flag_state != '' ? $flag_state : ($this->resp_status != self::SUCCESS_STATUSFLAG ? self::FLAG_ERROR : self::FLAG_NEW);

        switch ($flag_state) {
            case self::FLAG_NEW:
                if (is_array($this->resp_items) && count($this->resp_items) == 1 && tep_not_empty($this->request_transactioncode)) {
                    if ($this->request_transactioncode == $this->resp_transactioncode) {
                        $query_array = array(
                            'method' => $this->request_method,
                            'sku' => tep_db_prepare_input($this->resp_items[0]['sku']),
                            'amount' => $this->getAmount($this->resp_items[0]['sku']),
                            'settle_amount' => $this->getSettleAmount($this->resp_items[0]['sku']),
                            'token' => tep_db_prepare_input($this->resp_items[0]['token']),
                            'ack' => tep_db_prepare_input($this->resp_status),
                            'serialnumber' => tep_db_prepare_input($this->resp_ordernumber),
                            'flag_state' => $flag_state
                        );

                        if (tep_db_perform(TABLE_LOG_API_RESTOCK, $query_array, 'update', " id = '" . $this->request_transactioncode . "'")) {
                            $this->resp_items[0]['log_id'] = $this->request_transactioncode;
                        } else {
                            $this->resp_items[0]['log_id'] = FALSE;
                            $this->reportError($query_array, 'Failed to update logAPIData()');
                        }
                    } else {
                        $this->resp_items[0]['log_id'] = FALSE;
                        $this->reportError($query_array, 'Failed to update logAPIData() due to ' . $this->request_transactioncode . ' !== ' . $this->resp_transactioncode);
                    }

                    $return_init = $this->request_transactioncode;
                } else {
                    $query_array = array(
                        'method' => $this->request_method,
                        'sku' => tep_db_prepare_input($sku),
                        'ack' => tep_db_prepare_input($this->resp_status),
                        'serialnumber' => tep_db_prepare_input($this->resp_ordernumber),
                        'flag_state' => $flag_state,
                        'api_provider' => self::API_PROVIDER,
                        'created_datetime' => 'now()'
                    );

                    foreach ($this->resp_items as $idx => $item_array) {
                        $query_array['sku'] = tep_db_prepare_input($item_array['sku']);
                        $query_array['amount'] = $this->getAmount($item_array['sku']);
                        $query_array['settle_amount'] = $this->getSettleAmount($item_array['sku']);
                        $query_array['token'] = tep_db_prepare_input($item_array['token']);

                        if (tep_db_perform(TABLE_LOG_API_RESTOCK, $query_array)) {
                            $this->resp_items[$idx]['log_id'] = tep_db_insert_id();
                        } else {
                            $this->resp_items[$idx]['log_id'] = FALSE;
                            $this->reportError($query_array, 'Failed to insert logAPIData()');
                        }
                    }

                    $return_init = 1;
                }
                break;
            case self::FLAG_INIT:
                $query_array = array(
                    'method' => $this->request_method,
                    'sku' => tep_db_prepare_input($sku),
                    'flag_state' => $flag_state,
                    'api_provider' => self::API_PROVIDER,
                    'created_datetime' => 'now()'
                );

                if (tep_db_perform(TABLE_LOG_API_RESTOCK, $query_array)) {
                    $return_init = tep_db_insert_id();
                }
                break;
            case self::FLAG_UPLOADING:
                $sql_data_array = array(
                    'flag_state' => $flag_state
                );
                tep_db_perform(TABLE_LOG_API_RESTOCK, $sql_data_array, 'update', " id = '" . $log_id . "'");
                break;
            case self::FLAG_UPLOADED:
                $sql_data_array = array(
                    'flag_state' => $flag_state,
                    'custom_products_code_id' => $custom_products_code_id,
                    'token' => ''
                );
                tep_db_perform(TABLE_LOG_API_RESTOCK, $sql_data_array, 'update', " id = '" . $log_id . "'");
                break;
            default:
                $query_array = array(
                    'method' => $this->request_method,
                    'sku' => tep_db_prepare_input($sku),
                    'ack' => tep_db_prepare_input($this->resp_status),
                    'serialnumber' => tep_db_prepare_input($this->resp_ordernumber),
                    'flag_state' => $flag_state,
                    'api_provider' => self::API_PROVIDER,
                    'error_msg' => tep_db_prepare_input($this->resp_error),
                    'created_datetime' => 'now()'
                );

                if (!tep_db_perform(TABLE_LOG_API_RESTOCK, $query_array)) {
                    $this->reportError($query_array, 'Failed to insert error in logAPIData()');
                }
                break;
        }

        return $return_init;
    }

    private function reportError($response_data, $ext_subject = '') {
        include_once(DIR_WS_CLASSES . 'slack_notification.php');
        $slack = new slack_notification();
        $data = json_encode(array(
            'text' => '[OG Crew] Cron RSS Feed Error - ' . date("F j, Y H:i"),
            'attachments' => array(
                array(
                    'color' => 'warning',
                    'text' => $ext_subject . "\n\n" . json_encode($response_data)
                )
            )
        ));
        $slack->send(SLACK_WEBHOOK_DEV_DEBUG, $data);
    }

}

include_once dirname(__FILE__) . DIRECTORY_SEPARATOR . 'config.inc.php';
include_once dirname(__FILE__) . DIRECTORY_SEPARATOR . 'merchant_api_signature.php';
?>