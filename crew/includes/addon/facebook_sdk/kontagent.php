<?php
/*
 * @copyright 2010 Kontagent
 * @link http://www.kontagent.com
 */
class KontagentException extends Exception{}

class Kontagent
{
    private $m_kt_api_key;
    private $m_send_msg_from_js;
    private $m_kt_host;
    private $m_kt_comm_layer;
    
    public function __construct($kt_host, $kt_api_key, $send_msg_from_js=false) {
        $this->m_send_msg_from_js = $send_msg_from_js;
        $this->m_kt_api_key = $kt_api_key;
        $host_port_arry = split_dep(':', $kt_host);
        $this->m_kt_host = $host_port_arry[0];
        
        if(sizeof($host_port_arry) == 2) {
            $this->m_kt_port = $host_port_arry[1];
        } else {
            $this->m_kt_port = '80';
        }
        
        $this->m_kt_comm_layer = new KtCommLayer($this->m_kt_host, $this->m_kt_port, $this->m_kt_api_key);
    }

//	-----------------------------------------------   FB CONNECTED   -----------------------------------------------
    // User Get Connected with FB Connect
    public function track_install($uid) {
        $tracking_url = $this->gen_tracking_install_url($uid);
        $this->m_kt_comm_layer->api_call_method($tracking_url);
    }
    
    public function gen_tracking_install_url($uid) {
        $params = array('s' => $uid);
//        $curr_url = $this->get_current_url();
//        $parsed_items_arry = parse_url($curr_url);
//        parse_str($parsed_items_arry['query'], $parsed_qs_arry);
//		
//		// install through an invite, notification, email or post.
//        if( isset($parsed_qs_arry['kt_ut']) ) {
//            $params['u'] = $parsed_qs_arry['kt_ut'];
//        
//        // install through a ucc event.
//        } else if( isset($parsed_qs_arry['kt_sut']) ) {
//            $params['su'] = $parsed_qs_arry['kt_sut'];
//        }
        
        if( isset($_SESSION['fb_kt_ut']) ) {
        	$params['u'] = $_SESSION['fb_kt_ut'];
        	unset($_SESSION['fb_kt_ut']);
        }
        
        return $this->m_kt_comm_layer->gen_tracking_url('v1', 'apa', $params);
    }
//	-----------------------------------------------   FB CONNECTED   -----------------------------------------------

//	-----------------------------------------------   FB DISCONNECTED   -----------------------------------------------
    // User Disconnect FB Connect
    public function track_uninstall($uid) {
        $tracking_url = $this->gen_tracking_uninstall_url($uid);
        $this->m_kt_comm_layer->api_call_method($tracking_url);
    }
    
    public function gen_tracking_uninstall_url($uid) {
        $params = array('s' => $uid);
        $curr_url = $this->get_current_url();
        
        return $this->m_kt_comm_layer->gen_tracking_url('v1', 'apr', $params);
    }
//	-----------------------------------------------   FB DISCONNECTED   -----------------------------------------------
    
//	-----------------------------------------------   FB INVITE   -----------------------------------------------
    public function gen_invite_post_link($post_link, $long_tracking_code,
                                         $sender_uid,
                                         $st1=null, $st2=null, $st3=null)
    {
        $params = array();
        $params['kt_type'] = 'ins';
        $params['kt_ut'] = $long_tracking_code;
        $params['kt_uid'] = $sender_uid;
        $this->add_subtype123($params, $st1, $st2, $st3);
        
        if(isset($_REQUEST['session'])){
            $params['session'] = json_decode(get_magic_quotes_gpc()
                                            ? stripslashes($_REQUEST['session'])
                                            : $_REQUEST['session'],
                                            true);
        }
        
        if(isset($_REQUEST['fb_sig_session_key'])){
            $params['fb_sig_session_key'] = $_REQUEST['fb_sig_session_key'];
        }

        $mod_url = $this->append_kt_query_str($post_link,
                                              http_build_query($params, '', '&'));
        return $mod_url;
    }
    
    public function gen_invite_content_link($content_link, $long_tracking_code, 
                                            $st1=null, $st2=null, $st3=null)
    {
        $params = array();
        $params['kt_type'] = 'inr';
        $params['kt_ut'] = $long_tracking_code;
        $this->add_subtype123($params, $st1, $st2, $st3);
        $mod_url = $this->append_kt_query_str($content_link,
                                              http_build_query($params, '', '&'));
        return $mod_url;
    }
    
    public function track_invite_sent() {
        $tracking_url = $this->gen_tracking_invite_sent_url();
        $this->m_kt_comm_layer->api_call_method($tracking_url);
    }
    
    //
    // When fb forwards back the control back to the callback url after
    // invite sent, fb_sig_user is no where to be found. That's why we need
    // to have kt_uid in the invite post's query_string.
    //
    public function gen_tracking_invite_sent_url() {
        $params = array( 'u' => $_GET['kt_ut'] );
        if(isset($_GET['kt_uid'])) $params['s'] = $_GET['kt_uid'];
        if(isset($_GET['kt_st1'])) $params['st1'] = $_GET['kt_st1'];
        if(isset($_GET['kt_st2'])) $params['st2'] = $_GET['kt_st2'];
        if(isset($_GET['kt_st3'])) $params['st3'] = $_GET['kt_st3'];

        if(isset($_REQUEST['ids'])){
            $params['r'] = join(',' , $_REQUEST['ids']);
        }

        return $this->m_kt_comm_layer->gen_tracking_url('v1', 'ins', $params);
    }
    
    public function track_invite_received($recipient_uid) {
        $tracking_url = $this->gen_tracking_invite_click_url($recipient_uid);
        $this->m_kt_comm_layer->api_call_method($tracking_url);
    }
    
    // if recipient_uid is not available, pass in null.
    public function gen_tracking_invite_click_url($recipient_uid) {
        $installed = ogm_facebook::customer_has_connection($recipient_uid) ? 1 : 0;
        $params = array( 'u' => $_GET['kt_ut'],
                         'i' => $installed );
		
        if(isset($_GET['kt_st1'])) $params['st1'] = $_GET['kt_st1'];
        if(isset($_GET['kt_st2'])) $params['st2'] = $_GET['kt_st2'];
        if(isset($_GET['kt_st3'])) $params['st3'] = $_GET['kt_st3'];
        if(isset($recipient_uid))  $params['r']   = $recipient_uid;
		
        return $this->m_kt_comm_layer->gen_tracking_url('v1', 'inr', $params);
    }
//	-----------------------------------------------   FB INVITE   -----------------------------------------------
	
//	-----------------------------------------------   Capture User FB Information   -----------------------------------------------
	public function track_user_info($uid, $user_info_json, $friends_info_json)
    {
        $tracking_url = $this->gen_tracking_user_info_link($uid, $user_info_json, $friends_info_json);
        $this->m_kt_comm_layer->api_call_method($tracking_url);
    }
    
	public function gen_tracking_user_info_link($uid, $user_info_json, $friends_info_json)
    {
        $params = array('s'=> $uid);
        if(isset($user_info_json['gender'])){
            $params['g'] = urlencode(strtoupper($user_info_json['gender'][0]));
        }
        if(isset($user_info_json['birthday'])){
            $birthday_components=split_dep("/", $user_info_json['birthday']);
            if(sizeof($birthday_components) == 3)
                $params['b'] = urlencode(trim($birthday_components[2]));
            else
                $params['b'] = urlencode('');
        }
        if(isset($friends_info_json)){
            if(isset($friends_info_json['data'])){
                $params['f'] = sizeof($friends_info_json['data']);
            }
        }
        if(isset($_SESSION['country'])) {
        	$country_array = tep_get_countries_with_iso_codes($_SESSION['country']);
        	if(isset($country_array['countries_iso_code_2'])) {
        		$params['lc'] = $country_array['countries_iso_code_2'];	
        	}
        }
        return $this->m_kt_comm_layer->gen_tracking_url('v1', 'cpu', $params);
    }
//	-----------------------------------------------   Capture User FB Information   -----------------------------------------------
	
	public function get_current_url() {
        $pageURL = 'http';
        if (isset($_SERVER["HTTPS"]) && $_SERVER["HTTPS"] == "on") {
            $pageURL .= "s";
        }
        
        $pageURL .= "://";
        if ($_SERVER["SERVER_PORT"] != "80") {
            $pageURL .= $_SERVER["SERVER_NAME"].":".$_SERVER["SERVER_PORT"].$_SERVER["REQUEST_URI"];
        } else {
            $pageURL .= $_SERVER["SERVER_NAME"].$_SERVER["REQUEST_URI"];
        }
        
        return $pageURL;
    }
	
    public function append_kt_query_str($original_url, $query_str)
    {
        $position = strpos($original_url, '?');
        
        /* There are no query params, just append the new one */
        if ($position === false) {
            return $original_url.'?'.$query_str;
        }
        
        /* Prefix the params with the reference parameter */
        $noParams                   = substr($original_url, 0, $position + 1);
        $params                     = substr($original_url, $position + 1);
        return $noParams.$query_str.'&'.$params;
    }
    
    private function add_subtype123(&$params, $st1=null, $st2=null, $st3=null) {
        if(isset($st1)) {
            $params['kt_st1'] = $st1;
        }
        
        if(isset($st2)) {
            if(!isset($st1)) {
                throw new KontagentException('In order to supply a st2 string , you must also supply a st1 string');
            }    
            $params['kt_st2'] = $st2;
        }
        
        if(isset($st3)) {
            if(!isset($st1) || !isset($st2)) {
                throw new KontagentException('In order to supply a st3 string , you must also supply a st1 string and a st2 string');
            }
            $params['kt_st3'] = $st3;
        }
    }
    
    // User Information Cookie's name
    public function gen_kt_capture_user_info_key($fb_id, $uid) {
        return 'kt_capture_user_info_'.$fb_id."_".$uid;
    }
    
    // Used in invitation
    public function gen_long_tracking_code() {
        return substr(uniqid(rand()), -16);
    }
    
    // Page Request (also used to determine the user location by GeoIP)
    public function gen_tracking_pageview_link($user=null) {
    	//ip, s, u (Page address)
        $params = array('s'	=> $user,
        				'ip'=> tep_get_ip_address());
        return $this->m_kt_comm_layer->gen_tracking_url('v1', 'pgr', $params);
    }
}
?>