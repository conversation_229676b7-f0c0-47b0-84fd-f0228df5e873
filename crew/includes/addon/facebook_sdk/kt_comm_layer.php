<?php

/*
 * @copyright 2010 Kontagent
 * @link http://www.kontagent.com
 */

class KtCommLayer {

    private $m_host;
    private $m_port;
    private $m_api_key;
    private $m_server = "";
    private $m_socket = false;

    public function __construct($host, $port, $api_key) {
        $this->m_host = $host;
        $this->m_port = $port;
        $this->m_api_key = $api_key;
    }

    /**
     * Destructor.
     *
     * Ensures that socket is closed.
     */
    public function __destruct() {
        if ($this->m_socket) {
            fclose($this->m_socket);
        }
    }

    /**
     * Decides on a server to use for subsequent requests.
     */
    private function select_server() {
        // If we are using Kontagent's primary servers then utilize server selection protocol.
        if ($this->m_host == "api.geo.kontagent.net") {
            $this->m_server = $this->select_ip_address($this->m_host, $this->m_port);
        } else {
            $this->m_server = $this->m_host . ":" . $this->m_port;
        }
    }

    private function select_ip_address($host, $port) {
        // First try all servers in geographically-closest datacenter
        $ip_list = gethostbynamel($host);
        $selected_ip = "";

        if ($ip_list != false) {
            shuffle($ip_list);

            foreach ($ip_list as $ip) {
                $socket = @stream_socket_client($ip . ":" . $port, $errno, $errstr, 0.5, STREAM_CLIENT_CONNECT);
                if ($socket) {
                    $this->m_socket = $socket;
                    $selected_ip = $ip;
                    break;
                }
            }
        }

        // Looks like entire datacenter is down, so try our luck with one of global IPs
        if ($selected_ip == "") {
            $global_ip_list = gethostbynamel("api.global.kontagent.net");
            shuffle($global_ip_list);

            foreach ($global_ip_list as $global_ip) {
                $socket = @stream_socket_client($global_ip . ":" . $port, $errno, $errstr, 0.5, STREAM_CLIENT_CONNECT);

                if ($socket) {
                    $this->m_socket = $socket;
                    $selected_ip = $global_ip;
                    break;
                }
            }
        }
        return $selected_ip . ":" . $port;
    }

    public function gen_tracking_url($version, $msg_type, $params) {
        $params['ts'] = gmdate("Ymd.His");
        $url = 'http://' . $this->m_host . ":" . $this->m_port . "/api/" . $version . "/" . $this->m_api_key . "/" . $msg_type . "/?" . http_build_query($params, '', '&');
        return $url;
    }

    public function api_call_method($tracking_url) {
        try {
            $ch = curl_init();
            $opts = array();
            $opts[CURLOPT_CONNECTTIMEOUT] = 10;
            $opts[CURLOPT_RETURNTRANSFER] = true;
            $opts[CURLOPT_TIMEOUT] = 60;
            $opts[CURLOPT_USERAGENT] = 'Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.0)';
            $opts[CURLOPT_URL] = $tracking_url;
            // disable the 'Expect: 100-continue' behaviour. This causes CURL to wait
            // for 2 seconds if the server does not support this header.
            if (isset($opts[CURLOPT_HTTPHEADER])) {
                $existing_headers = $opts[CURLOPT_HTTPHEADER];
                $existing_headers[] = 'Expect:';
                $opts[CURLOPT_HTTPHEADER] = $existing_headers;
            } else {
                $opts[CURLOPT_HTTPHEADER] = array('Expect:');
            }

            curl_setopt_array($ch, $opts);
            $response = curl_exec($ch);

            if ($response === false) {
                $headers = 'To: <EMAIL>' . "\r\n" .
                        'From: <EMAIL>' . "\r\n" .
                        'Reply-To: <EMAIL>' . "\r\n" .
                        'X-Mailer: PHP/' . phpversion();

                mail('<EMAIL>', '[OFFGAMERS] CURL no response.', 'URL: ' . $tracking_url, $headers);
            }
        } catch (Exception $e) {
            $headers = 'To: <EMAIL>' . "\r\n" .
                    'From: <EMAIL>' . "\r\n" .
                    'Reply-To: <EMAIL>' . "\r\n" .
                    'X-Mailer: PHP/' . phpversion();

            mail('<EMAIL>', '[OFFGAMERS] CURL Error Found: ' . $e, 'URL: ' . $tracking_url, $headers);
        }
    }

}
