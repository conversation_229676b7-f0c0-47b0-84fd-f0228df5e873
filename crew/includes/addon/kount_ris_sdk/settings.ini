; Kount RIS PHP SDK configuration

[LOGGING]
; Specify the logger to use. The default loggers supplied with the Kount RIS
; SDK are NOP (a logger that silently discards all logging), and SIMPLE (a
; simple logger that writes messages to a specified file).
; See the README for more advanced logging configuration information.
LOGGER=NOP

; Logging level for SimpleLogger if it is enabled.
; Acceptable logging levels in order of decreasing severity are FATAL, ERROR,
; WARN, INFO, and DEBUG.
SIMPLE_LOG_LEVEL=DEBUG

; Specify the file name where the SimpleLogger will log messages to.
SIMPLE_LOG_FILE=Kount-SDK-Ris-PHP.log

; SimpleLogger log path. This is the directory where the log file will be
; located. This directory must have read and write permissions enabled for the
; PHP user. This directory must already exist.
SIMPLE_LOG_PATH=/tmp

