Open source OpenInviter<sup>TM</sup> (Open Inviter<sup>TM</sup>) is an free import contacts (addressbook) script from email providers like Virgilio, Freemail, Grafitti, Evite, Lycos, Mynet.com, Gawab, Atlas, Mail.com, Libero, Popstarmail, Techemail, Apropo, Azet, Care2, Abv, Interia, Netaddress, KataMail, Walla, 5Fm, Terra, OperaMail, GMX.net, Pochta, Mail2World, Aussiemail, Yandex, Inet, Hushmail, O2, Web.de, Inbox.com, IndiaTimes, Mail.in, FastMail, Clevergo, LinkedIn, Meta, Sapo.pt, Bigstring, Bordermail, Rambler, Nz11, Doramail, Wp.pt, GMail, India, Uk2, Rediff, Canoe, Live/Hotmail, YouTube, Mail.ru, Yahoo!, AOL, Kids, Zapakmail or social portals like Kincafe, Mydogspace, Xing, Multiply, Flickr, Skyrock, Facebook, Friendster, Flingr, Bookcrossing, Badoo, Friendfeed, Fdcareer, Mycatspace, Xanga, Konnects, Hyves, Orkut, NetLog, Livejournal, Eons, Xuqa, Perfspot, Ning, Koolro, Lovento, Last.fm, Bebo, Famiva, Cyworld, Vkontakte, MySpace, Tagged, Plaxo, Hi5, Vimeo, Plurk, Mevio, Motortopia, Meinvz, Plazes, Brazencareerist, Twitter, Flixster, Faces. This contacts importer script is integrating with content management systems (aka CMS) like phpizabi, myBB, RoundCube, Vwebmail, Social Engine, jamit job, nowFire, Joomla1.0, PHPMELODY, JamRoom, Wordpress, SimpleMachines Forum (SMF), vBulletin, Dating Pro, joovili, Drupal, PunBB, Buddy Zone, PhpBB, Joomla, symfony, phpFoX, Boonex Dolphin, Atmail5. Open Inviter is written in PHP 5 (no database required but cURL or wget required) and running on any webserver (tested on Apache) offering advanced tell a friend features. OpenInviter<sup>TM</sup> is a free self hosted solution that does not use a third party gateway (or API) to import contacts.Download it at: <a href='http://openinviter.com'>OpenInviter</a>