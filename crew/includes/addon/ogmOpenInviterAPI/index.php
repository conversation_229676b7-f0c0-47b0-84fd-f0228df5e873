<?php

echo"Open source OpenInviter<sup>TM</sup> (Open Inviter<sup>TM</sup>) is a free import contacts (addressbook) script from email providers like Canoe, Apropo, Abv, Doramail, Inet, LinkedIn, Rediff, Interia, O2, Bordermail, Zapakmail, Mail2World, India, KataMail, Wp.pt, Inbox.com, Gawab, MSN, Hushmail, Libero, Rambler, Netaddress, Care2, Terra, Kids, Mail.com, Walla, IndiaTimes, Uk2, FastMail, Atlas, Yahoo!, Virgilio, Live/Hotmail, Clevergo, Meta, GMX.net, Bigstring, Web.de, Popstarmail, Techemail, Freemail, Lycos, GMail, Evite, 5Fm, Pochta, Nz11, YouTube, Aussiemail, Mynet.com, OperaMail, Mail.ru, AOL, Azet, Mail.in, Grafitti, Yandex, Sapo.pt or social portals like Konnects, NetLog, Mevio, Multiply, Famiva, Plurk, Plaxo, Xuqa, Cyworld, Facebook, MySpace, Skyrock, Livejournal, Lovento, Friendster, Plazes, Badoo, Friendfeed, Vkontakte, Xing, Koolro, Eons, Vimeo, Motortopia, Orkut, Bookcrossing, Flixster, Perfspot, Ning, Flickr, Kincafe, Mycatspace, Fdcareer, Xanga, Faces, Bebo, Tagged, Mydogspace, Flingr, Brazencareerist, Hi5, Hyves, Meinvz, Last.fm, Twitter. This contacts importer script is integrating with content management systems (aka CMS) like Atmail5, RoundCube, Social Engine, phpFoX, PhpBB, JamRoom, PHPMELODY, vBulletin, Buddy Zone, SimpleMachines Forum (SMF), Dating Pro, Boonex Dolphin, myBB, joovili, PunBB, Wordpress, symfony, nowFire, Vwebmail, Drupal, Joomla, phpizabi, jamit job, Joomla1.0. Open Inviter is written in PHP 5 (no database required but cURL or wget required) and running on any webserver (tested on Apache) offering advanced tell a friend features. OpenInviter<sup>TM</sup> is a free self hosted solution that does not use a third party gateway (or API) to import contacts.Download it at: <a href='http://openinviter.com'>OpenInviter</a>";
?>