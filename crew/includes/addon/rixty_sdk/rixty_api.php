<?php

class rixty_api {

    const API_PROVIDER = 'RIXTY';
    const MIN_MARGIN = 0.065;
    const FLAG_NEW = 'N';
    const FLAG_UPLOADING = 'U';
    const FLAG_UPLOADED = 'S';
    const FLAG_ERROR = 'E';
    const GET_METHOD = 'GetSKU';
    const CHECK_METHOD = 'CheckSKU';
    const DEFAULT_ADMIN_EMAIL = 'system';
    const SUCCESS_ACK_FLAG = 'Success';

    private static $SKU_SUPPORT = array(
        'RXT-00500-P', 'RXT-01000-P',
        'RXT-02000-P', 'RXT-02500-P',
        'RXT-05000-P', 'RXT-10000-P'
    );
    private $request_method,
            $api_url,
            $api_signature,
            $resp_rixty_id, $resp_desc, $resp_amt, $resp_settle_amt, $resp_token, $resp_ack, $resp_error, $resp_serialnumber,
            $curl_obj, $cpc_obj;

    public function __construct() {
        include_once(DIR_WS_CLASSES . 'curl.php');
        include_once(DIR_WS_CLASSES . 'custom_product_code.php');
        include_once(DIR_WS_LANGUAGES . 'english.php');

        $this->api_url = RIXTY_API_URL;
        $this->api_signature = RIXTY_API_SIGNATURE;

        $this->curl_obj = new curl();
        $this->cpc_obj = new custom_product_code();
    }

    public static function isSupportedSKU($sku) {
        return in_array($sku, self::$SKU_SUPPORT);
    }

    private function generateRequestArray($sku) {
        return array(
            'method' => $this->request_method,
            'signature' => $this->api_signature,
            'sku' => $sku,
        );
    }

    public function getCode($sku) {
        $return_bool = FALSE;

        if (self::isSupportedSKU($sku)) {
            $this->setMethod(self::GET_METHOD);
            $return_bool = $this->preRequest($sku);
        }

        return $return_bool;
    }

    private function setMethod($method) {
        if (!is_null($method)) {
            $this->request_method = $method;
        }
    }

    public function checkCode($sku) {
        $return_bool = FALSE;

        if (self::isSupportedSKU($sku)) {
            $this->setMethod(self::CHECK_METHOD);
            $return_bool = $this->preRequest($sku);
        }

        return $return_bool;
    }

    private function loadAPIData($raw_response = NULL, $data_array = array()) {
        if (!is_null($raw_response)) {
            $data_array = $this->splitQueryToArray(urldecode($raw_response));
        }

        if (isset($data_array['DESC']) && !is_null($data_array['DESC'])) {
            $this->resp_desc = $data_array['DESC'];
        }

        if (isset($data_array['AMT']) && !is_null($data_array['AMT'])) {
            $this->resp_amt = $data_array['AMT'];
        }

        if (isset($data_array['SETTLEAMT']) && !is_null($data_array['SETTLEAMT'])) {
            $this->resp_settle_amt = $data_array['SETTLEAMT'];
        }

        if (isset($data_array['TOKEN']) && !is_null($data_array['TOKEN'])) {
            $this->resp_token = $data_array['TOKEN'];
        }

        if (isset($data_array['ACK']) && !is_null($data_array['ACK'])) {
            $this->resp_ack = $data_array['ACK'];
        }

        if (isset($data_array['SERIALNUMBER']) && !is_null($data_array['SERIALNUMBER'])) {
            $this->resp_serialnumber = $data_array['SERIALNUMBER'];
        }

        if (isset($data_array['ERRORMESSAGE']) && !is_null($data_array['ERRORMESSAGE'])) {
            $this->resp_error = $data_array['ERRORMESSAGE'];
        }

        if (isset($data_array['rixty_id']) && $data_array['rixty_id'] > 0) {
            $this->resp_rixty_id = $data_array['rixty_id'];
        }
    }

    public function preRequest($sku, $api_method = NULL, $total_try = 0) {
        $return_bool = FALSE;

        if ($total_try < 3) {
            $this->resetAPIData();
            $response = $this->curl_obj->curl_get($this->api_url, $this->generateRequestArray($sku));

            $this->loadAPIData($response);
            $this->logAPIData($sku);

            if ($this->resp_ack == self::SUCCESS_ACK_FLAG) {
                $return_bool = TRUE;
            } else {
                $return_bool = $this->preRequest($sku, $api_method, ++$total_try);
                $this->reportError(array('curlError' => $this->curl_obj->get_error(), 'sku' => $sku, 'method' => $this->request_method, 'try_count' => $total_try), 'preRequest');
            }
        }

        return $return_bool;
    }

    public function processBatchRestock($product_id, $order_product_store_price, $sku, $qty, $extra_params = array()) {
        $custom_products_code_array = array();
        $code_status = isset($extra_params['code_status']) ? $extra_params['code_status'] : '1';

        if ($qty > 0) {    // if partial delivered
            try {
                if (self::isSupportedSKU($sku) && $order_product_store_price > 0) {
                    if ($this->checkCode($sku) !== FALSE) {
                        $margin = ($order_product_store_price - $this->resp_settle_amt) / $order_product_store_price;

                        if ($margin > self::MIN_MARGIN) {
                            for ($count = 0; $count < $qty; $count++) {
                                if ($this->getCode($sku) !== FALSE) {
                                    $custom_products_code_id = $this->uploadCode($product_id, $code_status);

                                    // create cd key file
                                    if ($custom_products_code_id !== FALSE) {
                                        $custom_products_code_array[] = $custom_products_code_id;
                                    }
                                }
                            }

                            $this->addProductAvailableQty($product_id, $custom_products_code_array);
                        } else {
                            // report settle amt > expected
                            $this->reportError(array('product_id' => $product_id, 'resp_settle_amt' => $this->resp_settle_amt, 'order_product_store_price' => $order_product_store_price), 'processBatchRestock()');
                        }
                    }
                }
            } catch (Exception $e) {
                $return_bool = FALSE;
                $this->reportError(array('product_id' => $product_id, 'sku' => $sku, 'qty' => $qty, 'e' => $e->getMessage()), 'processBatchRestock()');
            }
        }

        return $custom_products_code_array !== array();
    }

    public function processRestock($product_id, $order_product_store_price, $sku, $code_status = '0') {
        $return_bool = FALSE;

        if (self::isSupportedSKU($sku) && $order_product_store_price > 0) {
            if ($this->checkCode($sku) !== FALSE) {
                $margin = ($order_product_store_price - $this->resp_settle_amt) / $order_product_store_price;

                if ($margin > self::MIN_MARGIN) {
                    if ($this->getCode($sku) !== FALSE) {
                        $custom_products_code_id = $this->uploadCode($product_id, $code_status);

                        // create cd key file
                        if ($custom_products_code_id !== FALSE) {
                            $this->addProductAvailableQty($product_id, array($custom_products_code_id));
                            $return_bool = TRUE;
                        }
                    }
                } else {
                    // report settle amt > expected
                    $this->reportError(array('product_id' => $product_id, 'resp_settle_amt' => $this->resp_settle_amt, 'order_product_store_price' => $order_product_store_price), 'processRestock()');
                }
            }
        }

        return $return_bool;
    }

    private function resetAPIData($default = '') {
        foreach (get_class_vars(get_class($this)) as $name => $value) {
            if (substr($name, 0, 5) == 'resp_') {
                $this->$name = $default;
            }
        }
    }

    private function splitQueryToArray($data_str) {
        $return_array = array();

        if (!empty($data_str)) {
            $pairs = explode("&", $data_str);

            foreach ($pairs as $pair) {
                list($key, $val) = explode("=", $pair);
                $return_array[$key] = $val;
            }
        }

        return $return_array;
    }

    private function addProductAvailableQty($product_id, $cdkey_id_arr) {
        global $log_object;

        if (is_object($log_object)) {
            $adj_qty = count($cdkey_id_arr);

            if ($adj_qty) {
                $products_quantity_select_sql = "	SELECT products_quantity, products_actual_quantity 
                                                    FROM " . TABLE_PRODUCTS . "
                                                    WHERE products_id=" . $product_id;
                $products_quantity_result_sql = tep_db_query($products_quantity_select_sql);
                if ($products_quantity_row = tep_db_fetch_array($products_quantity_result_sql)) {
                    $old_prod_available_qty = $products_quantity_row['products_quantity'];
                    $old_prod_actual_qty = $products_quantity_row['products_actual_quantity'];
                    $new_prod_available_qty = $old_prod_available_qty + $adj_qty;
                    $new_prod_actual_qty = $old_prod_actual_qty + $adj_qty;

                    $product_sql = "UPDATE " . TABLE_PRODUCTS . " 
                                        SET products_quantity = IF(products_quantity IS NULL, 0, products_quantity) + " . $adj_qty . ", 
                                            products_actual_quantity = IF(products_actual_quantity IS NULL, 0, products_actual_quantity) + " . $adj_qty . " 
                                    WHERE products_id = " . $product_id;
                    if (tep_db_query($product_sql)) {
                        $log_object->insert_log($product_id, 'products_quantity', $old_prod_available_qty . ':~:' . $old_prod_actual_qty, $new_prod_available_qty . ':~:' . $new_prod_actual_qty, sprintf(LOG_QTY_ADJUST, ''), sprintf(LOG_CDKEY_ID_STR, implode(',', $cdkey_id_arr)));
                    }

                    return TRUE;
                }
            }
        } else {
            $this->reportError('Error: Missing object log_object', 'addProductAvailableQty()');
        }

        return FALSE;
    }

    /*
     * status : 0 - sold
     * status : 1 - available
     * status : -2 - on hold
     */

    private function uploadCode($product_id, $status = '0', $code = NULL, $purchase_orders_id = NULL) {
        $this->loadAPIData(NULL, array('TOKEN' => $code, 'rixty_id' => $purchase_orders_id));
        $timestamp = date('Y-m-d H:i:s');
        $return_bool = FALSE;

        $insert_array = array(
            'products_id' => $product_id,
            'status_id' => $status,
            'file_name' => $this->resp_rixty_id,
            'file_type' => 'soft',
            'code_date_added' => $timestamp,
            'code_date_modified' => $timestamp,
            'code_uploaded_by' => self::DEFAULT_ADMIN_EMAIL,
            'remarks' => self::API_PROVIDER . '_API'
        );

        if (tep_db_perform(TABLE_CUSTOM_PRODUCTS_CODE, $insert_array)) {
            $API_flag = self::FLAG_UPLOADING;
            $custom_products_code_id = tep_db_insert_id();
            $key_string = tep_encrypt_data(base64_encode($this->resp_token));

            if ($this->cpc_obj->uploadCode($key_string, $custom_products_code_id, $product_id, $timestamp) === TRUE) {
                $API_flag = self::FLAG_UPLOADED;
                $return_bool = $custom_products_code_id;
            }

            $this->logAPIData('', $API_flag, $this->resp_rixty_id, $return_bool);
        }

        return $return_bool;
    }

    private function logAPIData($sku, $flag_state = '', $log_id = 0, $custom_products_code_id = '') {
        $return_init = 0;
        $flag_state = $flag_state != '' ? $flag_state : ($this->resp_ack !== 'Success' ? self::FLAG_ERROR : self::FLAG_NEW);

        switch ($flag_state) {
            case self::FLAG_UPLOADING:
                $sql_data_array = array(
                    'flag_state' => $flag_state
                );

                $return_init = tep_db_perform(TABLE_LOG_API_RESTOCK, $sql_data_array, 'update', " id = '" . $log_id . "'");
                break;
            case self::FLAG_UPLOADED:
                $sql_data_array = array(
                    'flag_state' => $flag_state,
                    'custom_products_code_id' => $custom_products_code_id,
                    'token' => ''
                );

                $return_init = tep_db_perform(TABLE_LOG_API_RESTOCK, $sql_data_array, 'update', " id = '" . $log_id . "'");
                break;
            case self::FLAG_ERROR:
            case self::FLAG_NEW:
            default:
                $query_array = array(
                    'description' => tep_db_prepare_input($this->resp_desc),
                    'amount' => tep_db_prepare_input($this->resp_amt),
                    'settle_amount' => tep_db_prepare_input($this->resp_settle_amt),
                    'token' => tep_db_prepare_input($this->resp_token),
                    'ack' => tep_db_prepare_input($this->resp_ack),
                    'serialnumber' => tep_db_prepare_input($this->resp_serialnumber),
                    'error_msg' => tep_db_prepare_input($this->resp_error),
                    'flag_state' => $flag_state,
                    'method' => $this->request_method,
                    'sku' => tep_db_prepare_input($sku),
                    'api_provider' => self::API_PROVIDER,
                    'created_datetime' => 'now()'
                );

                if (tep_db_perform(TABLE_LOG_API_RESTOCK, $query_array)) {
                    $this->loadAPIData(NULL, array('rixty_id' => tep_db_insert_id()));
                    $return_init = TRUE;
                } else {
                    $this->reportError($query_array, 'logAPIData()');
                    $return_init = FALSE;
                }
                break;
        }

        return $return_init;
    }

    private function reportError($response_data, $ext_subject = '') {
        include_once(DIR_WS_CLASSES . 'slack_notification.php');
        $slack = new slack_notification();
        $data = json_encode(array(
            'text' => '[OFFGAMERS] Rixty API Error - ' . date("F j, Y H:i"),
            'attachments' => array(
                array(
                    'color' => 'warning',
                    'text' => $ext_subject . "\n\n" . json_encode($response_data)
                )
            )
        ));
        $slack->send(SLACK_WEBHOOK_DEV_DEBUG, $data);
    }

}

include_once dirname(__FILE__) . DIRECTORY_SEPARATOR . 'config.inc.php';
?>