<?
/*
  	$Id: affiliate_application_top.php,v 1.6 2009/07/24 07:28:07 boonhock Exp $
	
  	OSC-Affiliate
  	Contribution based on:
	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2002 - 2003 osCommerce
	
  	Released under the GNU General Public License
*/

// Set the local configuration parameters - mainly for developers
if (file_exists(DIR_WS_INCLUDES . 'local/affiliate_configure.php')) include(DIR_WS_INCLUDES . 'local/affiliate_configure.php');

require(DIR_WS_INCLUDES . 'affiliate_configure.php');
require(DIR_WS_FUNCTIONS . 'affiliate_functions.php');

define('theme_path','cPath=16&theme=16');
// define the database table names used in the contribution
define('TABLE_AFFILIATE', 'affiliate_affiliate');
define('TABLE_AFFILIATE_ACCOUNT', 'affiliate_account');
// if you change this -> affiliate_show_banner must be changed too
define('TABLE_AFFILIATE_BANNERS', 'affiliate_banners');
define('TABLE_AFFILIATE_BANNERS_HISTORY', 'affiliate_banners_history');
define('TABLE_AFFILIATE_CLICKTHROUGHS', 'affiliate_clickthroughs');
define('TABLE_AFFILIATE_SALES', 'affiliate_sales');
define('TABLE_AFFILIATE_PAYMENT', 'affiliate_payment');
define('TABLE_AFFILIATE_PAYMENT_STATUS', 'affiliate_payment_status');
define('TABLE_AFFILIATE_PAYMENT_STATUS_HISTORY', 'affiliate_payment_status_history');

// define the filenames used in the project
define('FILENAME_AFFILIATE_SUMMARY', 'affiliate_summary.php');
define('FILENAME_AFFILIATE_LOGOUT', 'affiliate_logout.php');
define('FILENAME_AFFILIATE', 'affiliate_affiliate.php'); 
define('FILENAME_AFFILIATE_CONTACT', 'affiliate_contact.php');
define('FILENAME_AFFILIATE_FAQ', 'affiliate_faq.php');
define('FILENAME_AFFILIATE_ACCOUNT', 'affiliate_details.php');
define('FILENAME_AFFILIATE_DETAILS', 'affiliate_details.php');
define('FILENAME_AFFILIATE_DETAILS_OK', 'affiliate_details_ok.php');
define('FILENAME_AFFILIATE_TERMS','affiliate_terms.php');

define('FILENAME_AFFILIATE_HELP_1', 'affiliate_help1.php');
define('FILENAME_AFFILIATE_HELP_2', 'affiliate_help2.php');
define('FILENAME_AFFILIATE_HELP_3', 'affiliate_help3.php');
define('FILENAME_AFFILIATE_HELP_4', 'affiliate_help4.php');
define('FILENAME_AFFILIATE_HELP_5', 'affiliate_help5.php');
define('FILENAME_AFFILIATE_HELP_6', 'affiliate_help6.php');
define('FILENAME_AFFILIATE_HELP_7', 'affiliate_help7.php');
define('FILENAME_AFFILIATE_HELP_8', 'affiliate_help8.php');
define('FILENAME_AFFILIATE_INFO', 'affiliate_info.php');

define('FILENAME_AFFILIATE_BANNERS', 'affiliate_banners.php');
define('FILENAME_AFFILIATE_SHOW_BANNER', 'affiliate_show_banner.php');
define('FILENAME_AFFILIATE_SHOW_BANNER1', 'affiliate_show_banner1.php');
define('FILENAME_AFFILIATE_CLICKS', 'affiliate_clicks.php');

define('FILENAME_AFFILIATE_PASSWORD_FORGOTTEN', 'affiliate_password_forgotten.php');

define('FILENAME_AFFILIATE_SALES', 'affiliate_sales.php');
define('FILENAME_AFFILIATE_SIGNUP', 'affiliate_signup.php');

define('FILENAME_AFFILIATE_SIGNUP_OK', 'affiliate_signup_ok.php');
define('FILENAME_AFFILIATE_PAYMENT', 'affiliate_payment.php');              

// include the language translations
require(DIR_WS_LANGUAGES . 'affiliate_' . $language . '.php');

$affiliate_clientdate = (date ("Y-m-d H:i:s"));
$affiliate_clientbrowser = (isset($HTTP_SERVER_VARS["HTTP_USER_AGENT"])?$HTTP_SERVER_VARS["HTTP_USER_AGENT"]:'');
$affiliate_clientip = tep_get_ip_address();
$affiliate_clientreferer = (isset($HTTP_SERVER_VARS["HTTP_REFERER"])?$HTTP_SERVER_VARS["HTTP_REFERER"]:'');

if (!isset($HTTP_SESSION_VARS['affiliate_ref']) || !$HTTP_SESSION_VARS['affiliate_ref']) {
	$affiliate_ref = 0;   // 24
    tep_session_register('affiliate_ref');
    tep_session_register('affiliate_clickthroughs_id');
    if ((isset($HTTP_GET_VARS['ref']) || isset($HTTP_POST_VARS['ref'])) && ($HTTP_GET_VARS['ref'] || $HTTP_POST_VARS['ref'])) {
      	if ($HTTP_GET_VARS['ref']) $affiliate_ref = $HTTP_GET_VARS['ref'];
      	if ($HTTP_POST_VARS['ref']) $affiliate_ref = $HTTP_POST_VARS['ref'];
      	if ($HTTP_GET_VARS['products_id']) $affiliate_products_id = $HTTP_GET_VARS['products_id'];
      	if ($HTTP_POST_VARS['products_id']) $affiliate_products_id = $HTTP_POST_VARS['products_id'];
      	if ($HTTP_GET_VARS['affiliate_banner_id']) $affiliate_banner_id = $HTTP_GET_VARS['affiliate_banner_id'];
      	if ($HTTP_POST_VARS['affiliate_banner_id']) $affiliate_banner_id = $HTTP_POST_VARS['affiliate_banner_id'];
    	
      	if (!$link_to) $link_to = "0";
      	$sql_data_array = array('affiliate_id' => $affiliate_ref,
                              	'affiliate_clientdate' => $affiliate_clientdate,
                              	'affiliate_clientbrowser' => $affiliate_clientbrowser,
                              	'affiliate_clientip' => $affiliate_clientip,
                              	'affiliate_clientreferer' => $affiliate_clientreferer,
                              	'affiliate_products_id' => $affiliate_products_id,
                              	'affiliate_banner_id' => $affiliate_banner_id);
      	tep_db_perform(TABLE_AFFILIATE_CLICKTHROUGHS, $sql_data_array);
      	
      	$affiliate_clickthroughs_id = tep_db_insert_id();
		
   		// Banner has been clicked, update stats:
      	if ($affiliate_banner_id && $affiliate_ref) {
        	$today = date('Y-m-d');
        	$sql = "select * from " . TABLE_AFFILIATE_BANNERS_HISTORY . " where affiliate_banners_id = '" . $affiliate_banner_id  . "' and  affiliate_banners_affiliate_id = '" . $affiliate_ref . "' and affiliate_banners_history_date = '" . $today . "'";
        	$banner_stats_query = tep_db_query($sql);
			
     		// Banner has been shown today
    		if (tep_db_fetch_array($banner_stats_query)) {
        		tep_db_query("update " . TABLE_AFFILIATE_BANNERS_HISTORY . " set affiliate_banners_clicks = affiliate_banners_clicks + 1 where affiliate_banners_id = '" . $affiliate_banner_id . "' and affiliate_banners_affiliate_id = '" . $affiliate_ref. "' and affiliate_banners_history_date = '" . $today . "'");
   				// Initial entry if banner has not been shown
      		} else {
        		$sql_data_array = array('affiliate_banners_id' => $affiliate_banner_id,
                                		'affiliate_banners_products_id' => $affiliate_products_id,
                                		'affiliate_banners_affiliate_id' => $affiliate_ref,
                                		'affiliate_banners_clicks' => '1',
                                		'affiliate_banners_history_date' => $today);
        		tep_db_perform(TABLE_AFFILIATE_BANNERS_HISTORY, $sql_data_array);
      		}
		}
		
 		// Set Cookie if the customer comes back and orders it counts
    	setcookie('affiliate_ref', $affiliate_ref, time() + AFFILIATE_COOKIE_LIFETIME);
	}
	
    if (isset($HTTP_COOKIE_VARS['affiliate_ref']) && $HTTP_COOKIE_VARS['affiliate_ref'] && !$customer_id) { // Customer comes back and is registered in cookie
      	$affiliate_ref = $HTTP_COOKIE_VARS['affiliate_ref'];
    } else if (isset($customer_id) && $customer_id) {
		// get the correct affiliate reference id
		$affiliate_ref_select_sql = "SELECT affiliate_ref_id FROM " . TABLE_CUSTOMERS . " where customers_id='".$customer_id."' and customers_status='1'" ;
		$affiliate_ref_result_sql = tep_db_query($affiliate_ref_select_sql);
	    if ($affiliate_ref_row_sql = tep_db_fetch_array($affiliate_ref_result_sql)) {
	    	if ($affiliate_ref_row_sql["affiliate_ref_id"] != $HTTP_COOKIE_VARS['affiliate_ref']) {
	    		$affiliate_ref = $affiliate_ref_row_sql["affiliate_ref_id"];
	    	}
	    }
	}
} else if ($customer_id) {
	// get the correct affiliate reference id
	$affiliate_ref_select_sql = "SELECT affiliate_ref_id FROM " . TABLE_CUSTOMERS . " where customers_id='".$customer_id."' and customers_status='1'" ;
	$affiliate_ref_result_sql = tep_db_query($affiliate_ref_select_sql);
    if ($affiliate_ref_row_sql = tep_db_fetch_array($affiliate_ref_result_sql)) {
    	if ($affiliate_ref_row_sql["affiliate_ref_id"] != $HTTP_COOKIE_VARS['affiliate_ref']) {
    		$affiliate_ref = $affiliate_ref_row_sql["affiliate_ref_id"];
    	}
    }
}

// Inserted by Bob Vincent 4/6/2003
if ($affiliate_ref > 0) {
  	$sql = "SELECT * FROM " . TABLE_AFFILIATE . " WHERE affiliate_id = '" . $affiliate_ref . "'";
  	$affiliate_query = tep_db_query($sql);
  	if (!tep_db_num_rows($affiliate_query)) {
    	$affiliate_ref = 0;
  	} else {
    	foreach ( tep_db_fetch_array($affiliate_query)
	    	as $affiliate_field => $affiliate_value) {
      		define(strtoupper($affiliate_field),$affiliate_value);
    	}
  	}
  	tep_db_free_result($affiliate_query);
} else {
	define('AFFILIATE_ID',0);
  	define('AFFILIATE_GENDER',0);
  	define('AFFILIATE_FIRSTNAME','');
  	define('AFFILIATE_LASTNAME','');
  	define('AFFILIATE_DOB','');
  	define('AFFILIATE_EMAIL_ADDRESS','');
  	define('AFFILIATE_TELEPHONE','');
  	define('AFFILIATE_FAX','');
  	define('AFFILIATE_PASSWORD','');
  	define('AFFILIATE_HOMEPAGE',FILENAME_DEFAULT);
  	define('AFFILIATE_HEADER_IMAGE',DIR_WS_IMAGES . 'oscommerce.gif');
  	define('AFFILIATE_STREET_ADDRESS','');
  	define('AFFILIATE_SUBURB','');
  	define('AFFILIATE_CITY','');
  	define('AFFILIATE_POSTCODE','');
  	define('AFFILIATE_STATE','');
  	define('AFFILIATE_COUNTRY_ID','');
  	define('AFFILIATE_ZONE_ID','');
  	define('AFFILIATE_AGB',0);
  	define('AFFILIATE_COMPANY','VDS_NAME');
  	define('AFFILIATE_COMPANY_TAXID','');
  	define('AFFILIATE_COMMISSION_PERCENT',0);
  	define('AFFILIATE_PAYMENT_CHECK','');
  	define('AFFILIATE_PAYMENT_PAYPAL','');
  	define('AFFILIATE_PAYMENT_BANK_NAME','');
	define('AFFILIATE_PAYMENT_BANK_BRANCH_NUMBER','');
	define('AFFILIATE_PAYMENT_BANK_SWIFT_CODE','');
	define('AFFILIATE_PAYMENT_BANK_ACCOUNT_NAME','');
	define('AFFILIATE_PAYMENT_BANK_ACCOUNT_NUMBER','');
	define('AFFILIATE_DATE_OF_LAST_LOGON','');
	define('AFFILIATE_NUMBER_OF_LOGONS',0);
	define('AFFILIATE_DATE_ACCOUNT_CREATED','');
	define('AFFILIATE_DATE_ACCOUNT_LAST_MODIFIED','');
}
// End Bob Vincent's contribution

// Compatibility to older Snapshots
// set the type of request (secure or not)
if (!isset($request_type)) $request_type = (getenv('HTTPS') == 'on') ? 'SSL' : 'NONSSL';

// Emulate the breadcrumb class
if (!class_exists('breadcrumb')) {
	class breadcrumb {
	    function add($title, $link = '') {
	    	global $location;
	        $location='&raquo; <a href="' . $link . '" class="headerNavigation">' . $title . '</a>';
		}
	}
    $breadcrumb = new breadcrumb;
}
?>