<?php

/*
  $Id: application_top.php,v 1.170 2015/03/09 11:22:05 weesiong Exp $

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2003 osCommerce

  Released under the GNU General Public License
 */
// start the timer for the page parse time log
define('PAGE_PARSE_START_TIME', microtime());

//sign up captcha
define('SIGN_UP_CAPTCHA_FIELD', '0'); //1 = on;
// set the level of error reporting
error_reporting(E_ALL & ~E_NOTICE);

$HTTP_SERVER_VARS = $_SERVER;
$HTTP_POST_VARS = $_POST;
$HTTP_GET_VARS = $_GET;
$HTTP_COOKIE_VARS = $_COOKIE;

// check if register_globals is enabled.
// since this is a temporary measure this message is hardcoded. The requirement will be removed before 2.2 is finalized.
if (function_exists('ini_get')) {
    ini_get('register_globals') or exit('FATAL ERROR: register_globals is disabled in php.ini, please enable it!');
}

// Set the local configuration parameters - mainly for developers
if (file_exists('includes/local/configure.php'))
    include('includes/local/configure.php');

// include server parameters
require_once('includes/configure.php');

if (strlen(DB_SERVER) < 1) {
    if (is_dir('install')) {
        header('Location: install/index.php');
    }
}

// define the project version
define('PROJECT_VERSION', 'osCommerce 2.2-MS2');

// set the type of request (secure or not)
$request_type = (getenv('HTTPS') == 'on') ? 'SSL' : 'NONSSL';

// set php_self in the local scope
if (!isset($PHP_SELF))
    $PHP_SELF = $HTTP_SERVER_VARS['PHP_SELF'];

if ($request_type == 'NONSSL') {
    define('DIR_WS_CATALOG', DIR_WS_HTTP_CATALOG);
} else {
    define('DIR_WS_CATALOG', DIR_WS_HTTPS_CATALOG);
}

// include the list of project filenames
require(DIR_WS_INCLUDES . 'filenames.php');

// include the list of project database tables
require(DIR_WS_INCLUDES . 'database_tables.php');

// customization for the design layout
define('BOX_WIDTH', 125); // how wide the boxes should be in pixels (default: 125)
// include the database functions
require(DIR_WS_FUNCTIONS . 'database.php');

// make a connection to the database... now
tep_db_connect() or die('Unable to connect to database server!');

// include caching class
require(DIR_WS_CLASSES . 'cache_abstract.php');
require(DIR_WS_CLASSES . 'memcache.php');
$memcache_obj = new OGM_Cache_MemCache();

// storing the total time taken by all the mysql queries
$mysql_query_time = 0;

// set the application parameters
$configuration_query = tep_db_query('select configuration_key as cfgKey, configuration_value as cfgValue from ' . TABLE_CONFIGURATION);
while ($configuration = tep_db_fetch_array($configuration_query)) {
    define($configuration['cfgKey'], $configuration['cfgValue']);
}

$cat_conf_array = array();
$categories_configuration_query = tep_db_query('select categories_configuration_key as cfgKey, categories_configuration_value as cfgValue from ' . TABLE_CATEGORIES_CONFIGURATION . " WHERE categories_id=0; ");
while ($categories_configuration = tep_db_fetch_array($categories_configuration_query)) {
    $cat_conf_array[$categories_configuration['cfgKey']] = $categories_configuration['cfgValue'];
}

if (isset($cPath) && $cPath != '') {
    $cat_path_array = explode('_', $cPath);
    for ($i = 0; $i < count($cat_path_array); $i++) {
        $categories_configuration_query = tep_db_query('select categories_configuration_key as cfgKey, categories_configuration_value as cfgValue from ' . TABLE_CATEGORIES_CONFIGURATION . " WHERE categories_id='" . $cat_path_array[$i] . "'; ");
        while ($categories_configuration = tep_db_fetch_array($categories_configuration_query)) {
            $cat_conf_array[$categories_configuration['cfgKey']] = $categories_configuration['cfgValue'];
        }
    }
} else {
    if (isset($_REQUEST['products_id']) && $_REQUEST['products_id'] != '') {
        $prod_cat_select_sql = "SELECT categories_id FROM " . TABLE_PRODUCTS_TO_CATEGORIES . " WHERE products_id = '" . (int) $_REQUEST['products_id'] . "' AND products_is_link=0 ";
        $prod_cat_result_sql = tep_db_query($prod_cat_select_sql);

        if ($prod_cat_row = tep_db_fetch_array($prod_cat_result_sql)) {
            $cPath_categories_array = array($prod_cat_row['categories_id']);

            $parent_cat_select_sql = "SELECT parent_id, categories_id FROM " . TABLE_CATEGORIES . " WHERE categories_id = '" . (int) $prod_cat_row['categories_id'] . "'";
            $parent_cat_result_sql = tep_db_query($parent_cat_select_sql);

            while ($parent_cat_row = tep_db_fetch_array($parent_cat_result_sql)) {
                if ($parent_cat_row['parent_id'] == 0)
                    break;

                $cPath_categories_array[] = $parent_cat_row['parent_id'];

                if ($parent_cat_row['parent_id'] != $parent_cat_row['parent_cat_row']) {
                    $parent_cat_select_sql = "SELECT parent_id, categories_id FROM " . TABLE_CATEGORIES . " WHERE categories_id = '" . (int) $parent_cat_row['parent_id'] . "'";
                    $parent_cat_result_sql = tep_db_query($parent_cat_select_sql);
                } else {
                    break;
                }
            }

            for ($cat_cnt = count($cPath_categories_array) - 1; $cat_cnt >= 0; $cat_cnt--) {
                $categories_configuration_query = tep_db_query('select categories_configuration_key as cfgKey, categories_configuration_value as cfgValue from ' . TABLE_CATEGORIES_CONFIGURATION . " WHERE categories_id='" . $cPath_categories_array[$cat_cnt] . "'; ");
                while ($categories_configuration = tep_db_fetch_array($categories_configuration_query)) {
                    $cat_conf_array[$categories_configuration['cfgKey']] = $categories_configuration['cfgValue'];
                }
            }
        }
    }
}

foreach ($cat_conf_array as $cfgKey => $cfgValue) {
    define($cfgKey, $cfgValue);
}

// if gzip_compression is enabled, start to buffer the output
if ((GZIP_COMPRESSION == 'true') && ($ext_zlib_loaded = extension_loaded('zlib')) && (PHP_VERSION >= '4')) {
    if (($ini_zlib_output_compression = (int) ini_get('zlib.output_compression')) < 1) {
        if (PHP_VERSION >= '4.0.4') {
            ob_start('ob_gzhandler');
        } else {
            include(DIR_WS_FUNCTIONS . 'gzip_compression.php');
            ob_start();
            ob_implicit_flush();
        }
    } else {
        ini_set('zlib.output_compression_level', GZIP_LEVEL);
    }
}

// set the HTTP GET parameters manually if search_engine_friendly_urls is enabled
if (SEARCH_ENGINE_FRIENDLY_URLS == 'true') {
    if (strlen(getenv('PATH_INFO')) > 1) {
        $GET_array = array();
        $PHP_SELF = str_replace(getenv('PATH_INFO'), '', $PHP_SELF);
        $vars = explode('/', substr(getenv('PATH_INFO'), 1));
        for ($i = 0, $n = sizeof($vars); $i < $n; $i++) {
            if (strpos($vars[$i], '[]')) {
                $GET_array[substr($vars[$i], 0, -2)][] = $vars[$i + 1];
            } else {
                $HTTP_GET_VARS[$vars[$i]] = $vars[$i + 1];
            }
            $i++;
        }

        if (sizeof($GET_array) > 0) {
            while (list($key, $value) = each($GET_array)) {
                $HTTP_GET_VARS[$key] = $value;
            }
        }
    }
}

// define general functions used application-wide
require(DIR_WS_FUNCTIONS . 'general.php');
require(DIR_WS_FUNCTIONS . 'html_output.php');

// Get particular categories configuration setting(s)
require(DIR_WS_FUNCTIONS . 'configuration.php');

// Customer info verifications functions
require_once(DIR_WS_FUNCTIONS . 'customers_info_verification.php');

// set the cookie domain
$cookie_domain = (($request_type == 'NONSSL') ? HTTP_COOKIE_DOMAIN : HTTPS_COOKIE_DOMAIN);
$cookie_path = (($request_type == 'NONSSL') ? HTTP_COOKIE_PATH : HTTPS_COOKIE_PATH);
$filename = basename($_SERVER['SCRIPT_FILENAME']);
$REMOTE_ADDR = tep_get_ip_address();

// include cache functions if enabled
if (USE_CACHE == 'true')
    include(DIR_WS_FUNCTIONS . 'cache.php');

// include amazon web service class
require_once(DIR_WS_CLASSES . 'ogm_amazon_ws.php');

// initialize the category class
require(DIR_WS_CLASSES . 'category.php');

// include shopping cart class
require(DIR_WS_CLASSES . 'shopping_cart.php');

// include store credit class
require(DIR_WS_CLASSES . 'store_credit.php');

// include store point class
require(DIR_WS_CLASSES . 'store_point.php');

// include products low stock class
require(DIR_WS_CLASSES . 'products_low_stock.php');

// begin PayPal_Shopping_Cart_IPN
require(DIR_WS_MODULES . 'payment/paypal/classes/paypal_order.class.php');
// end PayPal_Shopping_Cart_IPN
// include navigation history class
require(DIR_WS_CLASSES . 'navigation_history.php');

// some code to solve compatibility issues
require(DIR_WS_FUNCTIONS . 'compatibility.php');

// include localization class
require(DIR_WS_CLASSES . 'localization.php');
require_once(DIR_WS_CLASSES . 'json.php');

// define our localization functions
require_once(DIR_WS_FUNCTIONS . 'localization.php');

require_once(DIR_WS_CLASSES . 'language.php');

// include currencies class and create an instance
require(DIR_WS_CLASSES . 'currencies.php');

// Pre-define Variables
$country = '';
$currency = '';
$language_code = '';

$session_started = false;
$cookies_started = false; //true: region box auto popup disabled; false: region box auto popup enabled.
$special_notice_cookies_started = true; // Temporarily turn off Special Notice Black Box
$language_code_changed = false;
$update_cookies = 0; //0: No update required; 1: Update cookies + redirect to mainpage; 2: Update cookies only
// --------------------------------------------------- SESSION MANAGER
// check if sessions are supported, otherwise use the php3 compatible session class
if (!function_exists('session_start')) {
    define('PHP_SESSION_NAME', 'OFFGAMERS'); //osCsid
    define('PHP_SESSION_PATH', $cookie_path);
    define('PHP_SESSION_DOMAIN', $cookie_domain);
    define('PHP_SESSION_SAVE_PATH', SESSION_WRITE_DIRECTORY);

    include(DIR_WS_CLASSES . 'sessions.php');
}

// define how the session functions will be used
require(DIR_WS_FUNCTIONS . 'sessions.php');

// set the session name and save path
tep_session_name('OFFGAMERS'); //osCsid
tep_session_save_path(SESSION_WRITE_DIRECTORY);

// set the session cookie parameters
if (function_exists('session_set_cookie_params')) {
    session_set_cookie_params(0, $cookie_path, $cookie_domain);
} elseif (function_exists('ini_set')) {
    ini_set('session.cookie_lifetime', '0');
    ini_set('session.cookie_path', $cookie_path);
    ini_set('session.cookie_domain', $cookie_domain);
}

// set the session ID if it exists
if (isset($HTTP_POST_VARS[tep_session_name()])) {
    tep_session_id($HTTP_POST_VARS[tep_session_name()]);
} elseif (($request_type == 'SSL') && isset($HTTP_GET_VARS[tep_session_name()])) {
    tep_session_id($HTTP_GET_VARS[tep_session_name()]);
}

// start the session
if (SESSION_FORCE_COOKIE_USE == 'True') {
    tep_setcookie('cookie_test', 'please_accept_for_session', time() + 60 * 60 * 24 * 30, $cookie_path, $cookie_domain);

    if (isset($HTTP_COOKIE_VARS['cookie_test'])) {
        tep_session_start();
    }
} elseif (SESSION_BLOCK_SPIDERS == 'True') {
    $user_agent = strtolower(getenv('HTTP_USER_AGENT'));
    $spider_flag = false;

    if (tep_not_null($user_agent)) {
        $spiders = file(DIR_WS_INCLUDES . 'spiders.txt');

        for ($i = 0, $n = sizeof($spiders); $i < $n; $i++) {
            if (tep_not_null($spiders[$i])) {
                if (is_integer(strpos($user_agent, trim($spiders[$i])))) {
                    $spider_flag = true;
                    break;
                }
            }
        }
    }

    if ($spider_flag == false) {
        tep_session_start();
        $session_started = true;
    }
} else {
    tep_session_start();
    $session_started = true;
}

# Country
if (!tep_session_is_registered('country')) {
    tep_session_register('country');
}

# Currency
if (!tep_session_is_registered('currency')) {
    tep_session_register('currency');
}

# Language
if (!tep_session_is_registered('language_code_ses')) {
    tep_session_register('language_code_ses');
    tep_session_register('language');
    tep_session_register('languages_id');
}

if (!tep_session_is_registered('default_language')) {
    tep_session_register('default_language');
    tep_session_register('default_languages_id');
}

if (!tep_session_is_registered('current_language_code'))
    tep_session_register('current_language_code');
if (!tep_session_is_registered('prev_language_code'))
    tep_session_register('prev_language_code');
// --------------------------------------------------- SESSION MANAGER
//if (isset($_COOKIE['ogm_special_notice']) && $_COOKIE['ogm_special_notice'] == 1) {
//	$special_notice_cookies_started = true;
//}
// ---------------------------------------- Regional Parameter MANAGER
if (isset($_POST['RegionalSet']) && count($_POST['RegionalSet']) == 3) {
    $cookies_started = true;
    $update_cookies = 2;

    $currency = tep_db_prepare_input($_POST['RegionalSet']['currency']);
    $language_code = tep_db_prepare_input($_POST['RegionalSet']['language']);
    $countries_iso_code_2 = tep_db_prepare_input($_POST['RegionalSet']['loc_country']);
    
    $countries = tep_db_query("select countries_id from " . TABLE_COUNTRIES . " where countries_iso_code_2 = '" . tep_db_input($countries_iso_code_2) . "'");
    if ($countries_values = tep_db_fetch_array($countries)) {
        $country = $countries_values['countries_id'];
    }

    # No refresh when country is not changed.
    if ((isset($_COOKIE['RegionalSet']['loc_country']) && $_COOKIE['RegionalSet']['loc_country'] != $country)) {
        $update_cookies = 1;
    }
} else if (isset($_COOKIE['ogm_regional']) && $data = stripslashes($_COOKIE['ogm_regional'])) {
    $cookies_started = true;

    $cookieValueArray = json_decode($data, true);
    $countries_iso_code_2 = $cookieValueArray['country'];
    $currency = $cookieValueArray['currency'];
    $language_code = $cookieValueArray['language'];
    
//    if (isset($_COOKIE['RegionalSet']['loc_country'])) {
//        $country = $_COOKIE['RegionalSet']['loc_country'];
//    } else {
        $update_cookies = 2;
        
        $countries = tep_db_query("select countries_id from " . TABLE_COUNTRIES . " where countries_iso_code_2 = '" . tep_db_input($countries_iso_code_2) . "'");
        if ($countries_values = tep_db_fetch_array($countries)) {
            $country = $countries_values['countries_id'];
        }
//    }
    
    if (isset($_REQUEST['language'])) {
        $update_cookies = 2;
        $language_code = $_REQUEST['language'];
    }

    if (isset($_REQUEST['currency'])) {
        $update_cookies = 2;
        $currency = $_REQUEST['currency'];
    }
} else if (isset($_REQUEST['client_IP_thirdparty']) || isset($_REQUEST['loc_country']) || isset($_REQUEST['currency']) || isset($_REQUEST['language'])) {
    $update_cookies = 2;
    $countries_iso_code_2 = '';
    
    if (isset($_REQUEST['from_thirdparty']) && $_REQUEST['from_thirdparty'] > 0) {
        $cookies_started = true;    
    }

    if (isset($_REQUEST['loc_country']) && tep_not_null($_REQUEST['loc_country'])) {
        $country = $_REQUEST['loc_country'];
        
        $countries = tep_db_query("select countries_iso_code_2 from " . TABLE_COUNTRIES . " where countries_id = '" . tep_db_input($country) . "'");
        if ($countries_values = tep_db_fetch_array($countries)) {
            $countries_iso_code_2 = $countries_values['countries_iso_code_2'];
        }
    } else if (isset($_REQUEST['client_IP_thirdparty'])) { //thirdparty client IP (KB)
        $country_info_array = tep_get_ip_country_info($_REQUEST['client_IP_thirdparty']);
        $country = tep_not_null($country_info_array) ? $country_info_array['id'] : '';
        unset($country_info_array);
    } else {
        $country = '';
    }

    if (isset($_REQUEST['currency']))
        $currency = $_REQUEST['currency'];
    if (isset($_REQUEST['language']))
        $language_code = $_REQUEST['language'];
} else {
    # Get default setting from variables validation section
    $update_cookies = 2;
    $country = '';
    $countries_iso_code_2 = '';
    $currency = '';
    $language_code = '';
}
// ---------------------------------------- Regional Parameter MANAGER
// ------------------------------------------------ Validate Variables
# Country
if (!tep_not_null($country) || !tep_country_exists($country)) {
    $cookies_started = false;
    # 1st: base on IP, 2nd : db config
    $country = tep_get_ip_country_id();
    if (empty($country)) {
        $country = STORE_COUNTRY;
    }
    
    $countries = tep_db_query("select countries_iso_code_2 from " . TABLE_COUNTRIES . " where countries_id = '" . tep_db_input($country) . "'");
    if ($countries_values = tep_db_fetch_array($countries)) {
        $countries_iso_code_2 = $countries_values['countries_iso_code_2'];
    }
}

# Used Only In Currency & Language
$localization_obj = new localization($country);

# Currency
$zone_id = $localization_obj->get_zone_id(3);
$zone_info_array = $localization_obj->get_zone_info($zone_id);

if (!tep_not_null($currency) || !in_array($currency, $zone_info_array->zone_currency_id)) {
    $cookies_started = false;
    $currency = $zone_info_array->zone_default_currency_id;
}

# Language
$zone_id = $localization_obj->get_zone_id(2);
$zone_info_array = $localization_obj->get_zone_info($zone_id);

if (!tep_not_null($language_code) || !in_array($language_code, $zone_info_array->zone_languages_id)) {
    $cookies_started = false;
    $language_code = $zone_info_array->zone_default_languages_id;
}

# GST status
if (strstr(MODULE_ORDER_TOTAL_INSTALLED, 'ot_gst')) {
    if (!tep_not_null($_SESSION['is_ip_checked'])) {
        localization::verify_gst_condition();
        $_SESSION['is_ip_checked'] = true;
    }

    if (tep_not_null($_SESSION['RegionGST']) && (($_SESSION['RegionGST']['loc_country'] != $country) || ($_SESSION['RegionGST']['currency'] != $currency))) {
        $country = $_SESSION['RegionGST']['loc_country'];
        $currency = $_SESSION['RegionGST']['currency'];
        $update_cookies = 2;
    }
} else {
    if (tep_not_null($_SESSION['RegionGST'])) {
        unset($_SESSION['RegionGST']);
        unset($_SESSION['is_ip_checked']);
    }
}

unset($localization_obj, $zone_id, $zone_info_array);
// --------- Validated Variables [$country, $currency, $language_code]
// ----------------------------------------------- Variables Expansion
# Language
$default_languages_id = SYSTEM_DEFAULT_LANGUAGE_ID;

if (!isset($_SESSION['language_name']) || !tep_not_null($_SESSION['language']) || !tep_not_null($_SESSION['languages_id']) || ($_SESSION['language_code_ses'] != $language_code)) {
    $language_code_changed = true;

    $language_obj = new language();
    $language_obj->set_language($language_code);
    $language = $language_obj->language['directory'];
    $languages_id = $language_obj->language['id'];
    $language_name = $language_obj->language['name'];
    
    $FB_Locales = ($language_code == 'en') ? 'en_US' : str_replace('-', '_', $language_code);

    unset($language_obj);
} else {
    # Use Session Variable
    $language = $_SESSION['language'];
    $languages_id = $_SESSION['languages_id'];
    $language_name = $_SESSION['language_name'];
    $FB_Locales = ($language_code == 'en') ? 'en_US' : str_replace('-', '_', $language_code);
}
// ----------------------------------------------- Variables Expansion


// ----------------------------------------------------- SESSION RENEW
//$_SESSION['country'] = $country;
$_SESSION['currency'] = $currency;
$_SESSION['countries_iso_code_2'] = $countries_iso_code_2;
$_SESSION['language_code_ses'] = $language_code;
$_SESSION['language'] = $language;
$_SESSION['languages_id'] = $languages_id;
$_SESSION['language_name'] = $language_name;
// ----------------------------------------------------- SESSION RENEW 


// ----------------------------------------------- Single Sign-On and Remember Me
include_once(DIR_WS_CLASSES . 'authenticate.php');
include_once(DIR_WS_CLASSES . 'oauth.php');
include_once(DIR_WS_CLASSES . 'shasso.php');
require_once(DIR_WS_CLASSES . 'regional_setting.php');
require_once(DIR_WS_FUNCTIONS . FILENAME_CUSTOM_PRODUCT_INFO);

$currencies = new currencies();
$localization = new localization($country);
$shasso_obj = new shasso($_GET);

//if ((DOWN_FOR_MAINTENANCE == 'false') && (basename($_SERVER['PHP_SELF']) != FILENAME_LOGIN)) {
if ((DOWN_FOR_MAINTENANCE == 'false') && !(isset($_GET['login_flag']) && $_GET['login_flag'] == "1")) {
    if (basename($PHP_SELF) !== FILENAME_SHASSO) {
        if ($shasso_response = $shasso_obj->init()) {
            if (isset($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
                // ajax call
                if (isset($shasso_response['redirect_url'])) {
                    if (isset($_GET['v']) && $_GET['v'] == 2) {
                        $page = $_SERVER['HTTP_REFERER'] ? $_SERVER['HTTP_REFERER'] : '';
                        echo json_encode(array('redirect' => $shasso_obj->getLoginURL('', $page)));
                        // reason why cannot ask v2 to do login itself is due to v2 will check the user session earlier than directing page to shasso
//                        echo json_encode(array('nxt' => 'login'));
                        exit;
                    }
                }
            } else if (isset($shasso_response['redirect_url'])) {
                tep_redirect($shasso_response['redirect_url']);
            }
        }
    }
}
// ----------------------------------------------- Single Sign-On and Remember Me
// --------------------------------------------- Manipulate DB MANAGER & UPDATE COOKIES
if (tep_session_is_registered('customer_id') && $update_cookies > 0) {
    $customers_info_sql = "	UPDATE " . TABLE_CUSTOMERS_INFO . "
							SET customer_info_selected_country = " . $country . ",
								customer_info_selected_language_id = " . $languages_id . "
							WHERE customers_info_id = '" . tep_db_input($_SESSION['customer_id']) . "'
								AND (customer_info_selected_country != '" . $country . "'
								OR customer_info_selected_language_id != " . $languages_id . ")";
    tep_db_query($customers_info_sql);

    unset($customers_info_sql);
    
    regional_setting::saveCustomerPreference($customer_id);
}

if ($update_cookies > 0) {
    $cookies_started = true;
    $cookies_expired = time() + 60 * 60 * 24 * 365; // 1 year expiry date;

    // store country id - used in adv search
    tep_setcookie('RegionalSet[loc_country]', $country, $cookies_expired, $cookie_path, $cookie_domain);
    
    $cookieValue = array(
        'country' => $countries_iso_code_2,
        'currency' => $currency,
        'language' => $language_code
    );
    
    tep_setcookie('ogm_regional', json_encode($cookieValue), $cookies_expired, $cookie_path, $cookie_domain);
    
//    $tmp_loc_country_info = tep_get_countries_info($country, 'countries_id');
    tep_setcookie('Loc[country_code]', $countries_iso_code_2, $cookies_expired, $cookie_path, $cookie_domain);
//    unset($tmp_loc_country_info);

    if ($update_cookies == 1) {
        tep_redirect(tep_href_link('/'));
    }
}

//if (!isset($_COOKIE['ogm']['sb'])) {
//    $batchId = oauth::getBatchId();
//    $expire = time() + 60 * 60 * 24 * 365; // 90 days expiry date;
//    tep_setcookie('ogm[sb]', $batchId, $expire, $cookie_path, $cookie_domain);
//}
// --------------------------------------------- Manipulate DB MANAGER & UPDATE COOKIES
// ------------------------------------------------------- SEO CONTROLLER
if ($language_code_changed) {
    # append language code and redirect if POST method ONLY {start}
    if (SEARCH_ENGINE_FRIENDLY_URLS == 'true') {
        if (!eregi_dep('callback.php', $_SERVER[REQUEST_URI]) && !eregi_dep('ipn.php', $_SERVER[REQUEST_URI]) && !eregi_dep('checkout_', $_SERVER[REQUEST_URI])) {

            # new: 20090720 language code store into array {start}
            $language_array = array();
            $language_select_sql = "SELECT languages_id, code FROM " . TABLE_LANGUAGES;
            $language_result_sql = tep_db_query($language_select_sql);
            while ($language_row = tep_db_fetch_array($language_result_sql)) {
                $language_array[$language_row['languages_id']] = ( $language_row['languages_id'] > 1 ? $language_row['code'] : '');
            }
            # new: 20090720 language code store into array {end}

            $current_language_code = (!eregi_dep('en', $language_code) ? $language_code : '');

            $server_uri = explode('/', $_SERVER[REQUEST_URI]);
            if (!tep_not_null($server_uri[0]))
                array_shift($server_uri);
            $clean_uri = $_SERVER[REQUEST_URI];

            if (tep_not_null($server_uri[0]) && tep_not_null($language_array)) {
                if (array_search($server_uri[0], $language_array) !== FALSE) {
                    $prev_language_code = $server_uri[0];
                    $clean_uri = str_replace('/' . $server_uri[0], '', $clean_uri);
                } else {
                    $prev_language_code = '';
                }
            }

            if ($current_language_code != $prev_language_code) {
                if (tep_not_null($current_language_code)) {
                    $clean_uri = '/' . $current_language_code . $clean_uri;
                    $prev_language_code = $current_language_code;
                    tep_redirect($clean_uri);
                } else {
                    $prev_language_code = $current_language_code;
                    tep_redirect($clean_uri);
                }
            }
            unset($language_array);
        }
        # append language code and redirect if POST method ONLY {end}
    }
    # redirect if POST method ONLY {end}
}
// ------------------------------------------------------- SEO CONTROLLER

// ---------------------------------------------------------------- Variables Available [$country, $currency, $language_code, $language, $languages_id]

$zone_info_array = $localization->get_zone_configuration();


if (isset($_SESSION['customers_groups_id'])) {
    $customers_groups_id = $_SESSION['customers_groups_id'];
} else {
    $customers_groups_id = $_SESSION['customers_groups_id'] = 1;
}

// set SID once, even if empty
$SID = (defined('SID') ? SID : '');

// verify the ssl_session_id if the feature is enabled
if (($request_type == 'SSL') && (SESSION_CHECK_SSL_SESSION_ID == 'True') && (ENABLE_SSL == true) && ($session_started == true)) {
    $ssl_session_id = getenv('SSL_SESSION_ID');
    if (!tep_session_is_registered('SSL_SESSION_ID')) {
        $SESSION_SSL_ID = $ssl_session_id;
        tep_session_register('SESSION_SSL_ID');
    }

    if ($SESSION_SSL_ID != $ssl_session_id) {
        tep_session_destroy();
        tep_redirect(tep_href_link(FILENAME_SSL_CHECK));
    }
}

// verify the browser user agent if the feature is enabled
if (SESSION_CHECK_USER_AGENT == 'True') {
    $http_user_agent = getenv('HTTP_USER_AGENT');
    if (!tep_session_is_registered('SESSION_USER_AGENT')) {
        $SESSION_USER_AGENT = $http_user_agent;
        tep_session_register('SESSION_USER_AGENT');
    }

    if ($SESSION_USER_AGENT != $http_user_agent) {
        tep_session_destroy();
        tep_redirect(tep_href_link(FILENAME_LOGIN));
    }
}

// verify the IP address if the feature is enabled
if (SESSION_CHECK_IP_ADDRESS == 'True') {
    $ip_address = tep_get_ip_address();
    if (!tep_session_is_registered('SESSION_IP_ADDRESS')) {
        $SESSION_IP_ADDRESS = $ip_address;
        tep_session_register('SESSION_IP_ADDRESS');
    }

    if ($SESSION_IP_ADDRESS != $ip_address) {
        tep_session_destroy();
        tep_redirect(tep_href_link(FILENAME_LOGIN));
    }
}

// create the shopping cart & fix the cart if necesary
if (tep_session_is_registered('cart') && is_object($cart)) {
    if (PHP_VERSION < 4) {
        $broken_cart = $cart;
        $cart = new shoppingCart;
        $cart->unserialize($broken_cart);
    }
} else {
    tep_session_register('cart');
    $cart = new shoppingCart;
}

/* * ***************************************************************
  Calculate category path
 * *************************************************************** */
if (isset($HTTP_GET_VARS['cPath'])) {
    $cPath = $HTTP_GET_VARS['cPath'];
} elseif (isset($HTTP_GET_VARS['products_id']) && !isset($HTTP_GET_VARS['manufacturers_id'])) {
    $cPath = tep_get_product_path($HTTP_GET_VARS['products_id']);
} else {
    $cPath = '';
}

include_once(DIR_WS_CLASSES . 'product.php');

if (tep_not_null($cPath)) {
    $tpl = 0;
    $cPath_array = tep_parse_category_path($cPath);
    $cPath = implode('_', $cPath_array);
    $current_category_id = $cPath_array[(sizeof($cPath_array) - 1)];

    $page_info = new product($cPath, $tpl);
    $page_info->cpath_check();
    $page_info->get_product_child_type();
    $ctype = $page_info->ctype;
} else if (isset($_GET['products_id'])) {
    $tpl = 0;
    $current_category_id = tep_get_actual_product_cat_id($_GET['products_id']);
    $cPath = tep_get_particular_cat_path($current_category_id);
    $cPath_array = tep_parse_category_path($cPath);

    $page_info = new product($cPath, $tpl);
    $page_info->cpath_check();
    $page_info->get_product_child_type();
    $ctype = $page_info->ctype;
} else {
    $cPath = '';
    $tpl = 0;
    $ctype = 0;
    $cPath_array = array();
    $current_category_id = 0;

    $page_info = new product($cPath, $tpl);
    $page_info->get_product_child_type();

    if (isset($_REQUEST['ctype'])) {
        $ctype = $_REQUEST['ctype'];

        if (in_array((int) $ctype, $page_info->custom_product_type_child_id_array)) {
            $tpl = $page_info->custom_product_type_id_array[array_search($ctype, $page_info->custom_product_type_child_id_array)];
        } else {
            tep_redirect(tep_href_link(FILENAME_DEFAULT));
        }
    }
}

$current_game_category_id = 0;  // Current game category id
$games_categories_path = '';    // Current game categories path
$customer_game_array = array(); //

if ($page_info->main_game_id) { // Return data if game id allow in this customer group
    $customer_game_array = tep_get_categories_by_customer_group(array($page_info->main_game_id), $customers_groups_id); // Return current game info base on customer group.

    if (isset($customer_game_array[0]) && count($customer_game_array[0])) {
        $game_cPath_array = tep_game_cpath($customer_game_array[0]['parent_id'], $customer_game_array[0]['categories_parent_path'], $customer_game_array[0]['categories_id']);

        if (count($game_cPath_array)) {
            $current_game_category_id = $game_cPath_array['current_game_category_id'];
            $games_categories_path = $game_cPath_array['games_categories_path'];
        }
    }
}

if (!tep_session_is_registered('SESSION_CURRENT_BROWSER_CAT_ID')) {
    tep_session_register('SESSION_CURRENT_BROWSER_CAT_ID');
}

$SESSION_CURRENT_BROWSER_CAT_ID = $current_category_id;

// begin PayPal_Shopping_Cart_IPN
paypal_order::check_order_status(true);
// end PayPal_Shopping_Cart_IPN

// include the mail classes
require(DIR_WS_CLASSES . 'mime.php');
require(DIR_WS_CLASSES . 'email.php');

# register session for language code {start}
//if (!tep_session_is_registered('current_language_code')) tep_session_register('current_language_code');
//if (!tep_session_is_registered('prev_language_code')) tep_session_register('prev_language_code');
//$language_code = $language_array[$languages_id];
$prev_language_code = $current_language_code;
$current_language_code = ($language_code == 'en') ? '' : $language_code;
# register session for language code {end}
// include the language translations
require(DIR_WS_LANGUAGES . $language . '.php');

// Mantis # 0000024 @ 200810221847 - english version of notice message
require(DIR_WS_LANGUAGES . $language . '/' . 'set_secret_qna.php');

//SEO URL Redirection
if (SEARCH_ENGINE_FRIENDLY_URLS == 'true') {
    include_once(DIR_WS_CLASSES . 'seo.php');

    $page = str_replace("/", "", $_SERVER[SCRIPT_NAME]); //remove '/'
    $seo = new seo_url($page, $_SERVER[QUERY_STRING]);
    $parameters = $seo->parameters; //url parameter
    $if_seo_url = $seo->if_seo_url; //true or false
    $get_data_array = $seo->get_data_array; //GET data
    $redirect_to_index = $seo->redirect_to_index; //redirect to index page
    $server_type = $seo->server_type;

    if ($if_seo_url) {
        $server_url = $_SERVER[SCRIPT_NAME] . (($_SERVER[QUERY_STRING]) ? '?' . $_SERVER[QUERY_STRING] : ''); //join script name and query string as dynamic url
        $seo_url = "/" . $parameters . (($redirect_to_index == 1) ? FILENAME_DEFAULT : FILENAME_URL_EXTENSION) . (($get_data_array) ? "?" . $get_data_array : ''); //seo url
        # append language code into SEO URL {start}
        if (!ereg_dep(HTTP_SERVER, $seo_url) || !ereg_dep(HTTPS_SERVER, $seo_url))
            $seo_url = str_replace('//', '/', $seo_url);
        if (tep_not_null($current_language_code) && !ereg_dep('/' . $current_language_code, $seo_url)) {
            $prev_language_code = $current_language_code;
            $seo_url = '/' . $current_language_code . $seo_url;
        } else if (tep_not_null($current_language_code) && tep_not_null($prev_language_code) && !ereg_dep('/' . $current_language_code, $seo_url)) {
            $prev_language_code = $current_language_code;
            $seo_url = str_replace($prev_language_code . '/', $current_language_code . '/', $seo_url);
        }
        # append language code into SEO URL {end}
        
        if ($server_url == $_SERVER[REQUEST_URI]) { //if static url same as static url
            header("HTTP/1.0 301 Moved Permanently"); //this page moved permanently
            header("Location: " . $http_server . $seo_url); //redirect page with seo url
        } else if ($seo_url != $_SERVER[REQUEST_URI]) { // if dynamic url not same seo url
            $http_server = ((substr($server_type, -1) == "/") ? substr(HTTP_SERVER, 0, -1) : '');
            header("Location: " . $http_server . $seo_url); //redirect page
        }
    }
}

// Page View Module - Moved here to avoid duplicate counter increment due to system redirection
include_once(DIR_WS_CLASSES . "page_view_module.php");
$page_view_module_obj = new page_view_module();
if (!$page_view_module_obj->trace()) {
    die('	<!DOCTYPE HTML PUBLIC "-//IETF//DTD HTML 2.0//EN">
			<html><head>
			<title>403 Forbidden</title>
			</head><body>
			<h1>Forbidden</h1>
			<p>Your IP ' . $page_view_module_obj->get_ip() . ' has been forbidden to access this site further due to excessive page requests.  We would like to apologize if this is a mistake and we will greatly appreciate your help if you <NAME_EMAIL> regarding this matter.  If you are representing a price listing site or if you would like to acquire a full list of the prices of our products/services, kindly e-mail your request and <NAME_EMAIL>.  Thank you!</p>
			<hr>
			</body></html>');
}

// navigation history
if (tep_session_is_registered('navigation')) {
    if (PHP_VERSION < 4) {
        $broken_navigation = $navigation;
        $navigation = new navigationHistory;
        $navigation->unserialize($broken_navigation);
    } else if (!is_object($navigation)) {
        $navigation = new navigationHistory;
    }
} else {
    tep_session_register('navigation');
    $navigation = new navigationHistory;
}

if (is_object($navigation)) {
    $navigation->add_current_page();
}

// BOF: Down for Maintenance except for admin ip
if (EXCLUDE_ADMIN_IP_FOR_MAINTENANCE != tep_get_ip_address()) {
    if (DOWN_FOR_MAINTENANCE == 'true') {
        if (!strstr($PHP_SELF, DOWN_FOR_MAINTENANCE_FILENAME) && !strstr($PHP_SELF, FILENAME_LOGIN_POPUP)) {
            tep_redirect(tep_href_link(DOWN_FOR_MAINTENANCE_FILENAME));
        } else {
            $cookies_started = true;
        }
    } else {
        // do not let people get to down for maintenance page if not turned on
        if (strstr($PHP_SELF, DOWN_FOR_MAINTENANCE_FILENAME)) {
            tep_redirect(tep_href_link(FILENAME_DEFAULT));
        }
    }
}
// EOF: WebMakers.com Added: Down for Maintenance

// Shopping cart actions

if (isset($HTTP_GET_VARS['action'])) {
    // redirect the customer to a friendly cookie-must-be-enabled page if cookies are disabled
    if ($session_started == false) {
        tep_redirect(tep_href_link(FILENAME_COOKIE_USAGE));
    }

    if (DISPLAY_CART == 'true') {
        $goto = FILENAME_SHOPPING_CART;
        $parameters = array('action', 'cPath', 'products_id', 'pid');
    } else {
        $goto = basename($PHP_SELF);
        if ($HTTP_GET_VARS['action'] == 'buy_now') {
            $parameters = array('action', 'pid', 'products_id');
        } else {
            $parameters = array('action', 'pid');
        }
    }



    switch ($HTTP_GET_VARS['action']) {
        case 'set_special_notice_cookie':
            $cookies_expired = time() + 60 * 60 * 24 * 365; // 1 year expiry date;
            tep_setcookie('ogm_special_notice', '1', $cookies_expired, $cookie_path, $cookie_domain);
            die();
            break;
        // customer wants to update the product quantity in their shopping cart
        case 'update_product':
            for ($i = 0, $n = sizeof($HTTP_POST_VARS['products_id']); $i < $n; $i++) {
                list($main_pid, $prod_index) = explode('_', $HTTP_POST_VARS['products_id'][$i]); // PWL products has sub index

                if (isset($HTTP_POST_VARS["DeleteBtn_" . $HTTP_POST_VARS['products_id'][$i] . '_x'])) {
                    if (isset($prod_index) && $prod_index > 0) {
                        // Custom product which has quantity all the time = 1 or Normal product has sub add-to-cart requirement (e.g. different char name)
                        //$remove_from_cart = true;	// needed as flag used in shopping_cart.php class file

                        if ($cart->get_custom_prd_type($main_pid) == 'hla') {
                            $cart->remove($main_pid, $prod_index);
                        } else {
                            $cart->remove($main_pid, (int) ($prod_index - 1)); //remove custom product
                        }

                        //$cart->arrange_custom_product($custom_products_array);
                    } else {
                        $cart->remove($main_pid);
                    }
                } else if (isset($HTTP_POST_VARS["QtyUpdateBtn"])) {
                    list($main_pid, $prod_index) = explode('_', $HTTP_POST_VARS['products_id'][$i]);
                    // Must check enteredt qty equal to integer cast value to prevent very large qty value attack

                    if (!$cart->in_cart($main_pid))
                        continue;

                    if (is_numeric($HTTP_POST_VARS['cart_quantity'][$HTTP_POST_VARS['products_id'][$i]]) && (int) $HTTP_POST_VARS['cart_quantity'][$HTTP_POST_VARS['products_id'][$i]] >= 0 && (int) $HTTP_POST_VARS['cart_quantity'][$HTTP_POST_VARS['products_id'][$i]] <= 2147483647 && $HTTP_POST_VARS['cart_quantity'][$HTTP_POST_VARS['products_id'][$i]] == (int) $HTTP_POST_VARS['cart_quantity'][$HTTP_POST_VARS['products_id'][$i]]) {
                        if (PHP_VERSION < 4) {
                            // if PHP3, make correction for lack of multidimensional array.
                            reset($HTTP_POST_VARS);
                            while (list($key, $value) = each($HTTP_POST_VARS)) {
                                if (is_array($value)) {
                                    while (list($key2, $value2) = each($value)) {
                                        if (ereg_dep("(.*)\]\[(.*)", $key2, $var)) {
                                            $id2[$var[1]][$var[2]] = $value2;
                                        }
                                    }
                                }
                            }
                            $attributes = ($id2[$main_pid]) ? $id2[$main_pid] : '';
                        } else {
                            $attributes = ($HTTP_POST_VARS['id'][$main_pid]) ? $HTTP_POST_VARS['id'][$main_pid] : '';
                        }

                        $custom_array_key = '';

                        if (isset($cart->contents[$main_pid]['custom']['game_currency'])) {
                            $custom_array_key = "game_currency";
                        } else if (isset($cart->contents[$main_pid]['custom']['cdkey'])) {
                            $custom_array_key = "cdkey";
                        } else if (isset($cart->contents[$main_pid]['custom']['store_credit'])) {
                            $custom_array_key = "store_credit";
                        }

                        if (isset($cart->contents[$main_pid]['custom'][$custom_array_key])) {
//							if (isset($cart->contents[$main_pid]['custom'][$custom_array_key][$prod_index-1]['delivery_mode']) && $cart->contents[$main_pid]['custom'][$custom_array_key][$prod_index-1]['delivery_mode'] == 6) {
//								$cart->update_cart($main_pid, $HTTP_POST_VARS['cart_quantity'][$HTTP_POST_VARS['products_id'][$i]], $attributes, false, '', '', true, $cart->contents[$main_pid]['custom'][$custom_array_key][$prod_index-1]);
//							} else {
                            $cart->update_cart($main_pid, $HTTP_POST_VARS['cart_quantity'][$HTTP_POST_VARS['products_id'][$i]], $attributes, false, '', '', false, $cart->contents[$main_pid]['custom'][$custom_array_key][$prod_index - 1]);
//							}
                        } else {
                            $cart->update_cart($main_pid, $HTTP_POST_VARS['cart_quantity'][$HTTP_POST_VARS['products_id'][$i]], $attributes, false);
                        }
                    }

                    if (count($cart->contents) > 1) {
                        $cart->reset(true);
                    }
                }
            }

            tep_redirect(tep_href_link($goto, tep_get_all_get_params($parameters)));
            break;
        // customer adds a product from the products page
        case 'add_product':
            if (isset($HTTP_POST_VARS['products_id']) && is_numeric($HTTP_POST_VARS['products_id'])) {
                $cart->reset(true);
                //$cart->add_cart($HTTP_POST_VARS['products_id'], $cart->get_quantity(tep_get_uprid($HTTP_POST_VARS['products_id'], $HTTP_POST_VARS['id']))+1, $HTTP_POST_VARS['id']);
                $cart->add_cart($HTTP_POST_VARS['products_id'], $cart->get_quantity(tep_get_uprid($HTTP_POST_VARS['products_id'], $HTTP_POST_VARS['id'])) + (int) $HTTP_POST_VARS['buyqty'], $HTTP_POST_VARS['id']);
            }
            //tep_redirect(tep_href_link($goto, tep_get_all_get_params($parameters)));
            break;
        // performed by the 'buy now' button in product listings and review page
        case 'buy_now' :
            $success = '';

            $pd_strlen_show_array = array();
            $pd_strlen_show_array['ASCII'] = 35;
            $pd_strlen_show_array['UTF-8'] = 28;

            if (isset($_REQUEST['products_id']) && tep_check_product_region_permission($_REQUEST['products_id'])) {
                if ((int) ($_REQUEST['custom_product']) == 1) {
                    header('Content-Type: text/xml');
                    echo "<result>";
                    echo "<type>redirect</type>";
                    echo "<url><![CDATA[" . tep_href_link(FILENAME_CUSTOM_PRODUCT_INFO, ($cPath ? 'cPath=' . $cPath . '&' : '') . 'products_id=' . (int) $_REQUEST['products_id']) . "]]></url>";
                    echo "</result>";
                    exit;
                } else {
                    $cart->reset(true);

                    if ($_REQUEST['products_bundle'] == "yes") {
                        // static bundle product
                        $bundle_select_sql = "	SELECT pd.products_name, pb.*, p.products_bundle, p.products_id, p.products_price
												FROM " . TABLE_PRODUCTS . " AS p
												INNER JOIN " . TABLE_PRODUCTS_DESCRIPTION . " AS pd
											  		ON p.products_id=pd.products_id
												INNER JOIN " . TABLE_PRODUCTS_BUNDLES . " AS pb
											  		ON pb.subproduct_id=pd.products_id
												WHERE pb.bundle_id = '" . tep_db_input($_REQUEST["products_id"]) . "' AND language_id = '1'";
                        $bundle_result_sql = tep_db_query($bundle_select_sql);

                        $customers_basket_select_sql = "SELECT customers_basket_quantity FROM " . TABLE_CUSTOMERS_BASKET . " WHERE customers_id = '" . (int) $customer_id . "' AND products_id = '" . tep_db_input($_REQUEST["products_id"]) . "'";
                        $customers_basket_result_sql = tep_db_query($customers_basket_select_sql);
                        $customers_basket_info = tep_db_fetch_array($customers_basket_result_sql);

                        $reduce_to_qty = '';
                        while ($bundle_data = tep_db_fetch_array($bundle_result_sql)) {
                            $product_select_sql = "	SELECT p.products_id, p.products_quantity, p.products_status, p.products_purchase_mode, p.products_out_of_stock_level
													FROM " . TABLE_PRODUCTS . " AS p
													WHERE p.products_id = '" . $bundle_data["products_id"] . "'";
                            $product_result_sql = tep_db_query($product_select_sql);

                            if ($product_info = tep_db_fetch_array($product_result_sql)) {
                                if (!$product_info["products_status"] || $product_info["products_purchase_mode"] == '3') { // Inactive or Out of Stock
                                    $err1 = "SL";
                                    break;
                                }

                                switch ($product_info["products_purchase_mode"]) {
                                    case '1': // Always Add to Cart
                                    case '2': // Always Pre-Order
                                        break;
                                    case '4': // Auto Mode
                                        if (tep_not_null($product_info["products_out_of_stock_level"])) { // If there is setting for out of stock level
                                            $current_qty_in_cart = tep_get_total_product_in_cart($customer_id, $product_info["products_id"]);

                                            $suggested_amt = (int) ((tep_get_products_stock($bundle_data["subproduct_id"], true) - $current_qty_in_cart) / $bundle_data["subproduct_qty"]);
                                            if ($reduce_to_qty != '') {
                                                if ($reduce_to_qty > $suggested_amt)
                                                    $reduce_to_qty = $suggested_amt;
                                            } else {
                                                $reduce_to_qty = $suggested_amt;
                                            }

                                            if ($product_info['products_quantity'] - ($bundle_data['subproduct_qty'] * ((int) $_REQUEST['buyqty']) + $current_qty_in_cart) < (int) $product_info["products_out_of_stock_level"]) {
                                                $err1 = "SL";
                                                break 2;
                                            }
                                        }
                                        break;
                                }
                            }
                        }

                        if ($err1) {
                            $parameters = array('action', 'pid', 'err', 'success');
                            $err = "SL";

                            header('Content-Type: text/xml');
                            echo "<result>";
                            echo "<type>redirect</type>";
                            echo "<url><![CDATA[" . tep_href_link(basename($PHP_SELF), tep_get_all_get_params($parameters)) . '&err=' . $err . '&sq=' . (int) $reduce_to_qty . "]]></url>";
                            echo "</result>";
                            exit;
                        } else {
                            if ($cart->get_custom_prd_type($_REQUEST['products_id']) == 'cdkey') {
                                $extra_info_array = array();
                                if (isset($_REQUEST['extra_info'])) {
                                    $extra_info_array['top_up_info'] = $_REQUEST['extra_info'];
                                }
                                if (isset($_REQUEST['delivery_mode']) && (int) $_REQUEST['delivery_mode'] > 0) {
                                    $extra_info_array['delivery_mode'] = (int) $_REQUEST['delivery_mode'];
                                } else {
                                    $extra_info_array['delivery_mode'] = 5;
                                }
                            } else {
                                $extra_info_array = $HTTP_POST_VARS['extra_info'];
                            }

                            $parameters = array('action', 'pid', 'success');
                            $cart->add_cart($_REQUEST['products_id'], (int) $_REQUEST['buyqty'], '', true, '', '', false, $extra_info_array);
                            $success = 'CA';
                        }
                    } else if ($_REQUEST['products_bundle_dynamic'] == "yes") {

                        $extra_info_array = array();
                        if ($cart->get_custom_prd_type($_REQUEST['products_id']) == 'cdkey') {
                            if (isset($_REQUEST['extra_info'])) {
                                $extra_info_array['top_up_info'] = $_REQUEST['extra_info'];
                            }
                            if (isset($_REQUEST['delivery_mode']) && (int) $_REQUEST['delivery_mode'] > 0) {
                                $extra_info_array['delivery_mode'] = (int) $_REQUEST['delivery_mode'];
                            } else {
                                $extra_info_array['delivery_mode'] = 5;
                            }
                        }

                        // dynamic bundle product
                        $bd_products_array = array();
                        $total_bundle_weight = 0;

                        $bundle_weight_select_sql = "SELECT products_bundle_dynamic_qty FROM " . TABLE_PRODUCTS . " WHERE products_id='" . tep_db_input($_REQUEST['products_id']) . "'";
                        $bundle_weight_result_sql = tep_db_query($bundle_weight_select_sql);
                        $bundle_weight_row = tep_db_fetch_array($bundle_weight_result_sql);

                        if ($bundle_weight_row["products_bundle_dynamic_qty"]) {
                            //$allowed_weight = ($bdn == 'y') ? (int)$_REQUEST['buyqty']*$bundle_weight_row["products_bundle_dynamic_qty"] : ($cart->get_quantity($_REQUEST['products_id']) + (int)$_REQUEST['buyqty'])*$bundle_weight_row["products_bundle_dynamic_qty"] ;
                            // only consider present add to cart, no need to bother the one already in cart
                            $allowed_weight = (int) $_REQUEST['buyqty'] * $bundle_weight_row["products_bundle_dynamic_qty"];

                            $bundle_select_sql = "	SELECT pd.products_name, pb.*, p.products_bundle, p.products_bundle_dynamic, p.products_id, p.products_price
													FROM " . TABLE_PRODUCTS . " AS p
													INNER JOIN " . TABLE_PRODUCTS_DESCRIPTION . " AS pd
												  		ON p.products_id=pd.products_id
													INNER JOIN " . TABLE_PRODUCTS_BUNDLES . " AS pb
												  		ON pb.subproduct_id=pd.products_id
													WHERE pb.bundle_id = '" . tep_db_input($_REQUEST['products_id']) . "' AND language_id = '1'";
                            $bundle_result_sql = tep_db_query($bundle_select_sql);

                            while ($bundle_data = tep_db_fetch_array($bundle_result_sql)) {
                                // Omit those inactive or has the out of stock purchase mode
                                $product_select_sql = "	SELECT p.products_id, p.products_quantity, p.products_purchase_mode, p.products_out_of_stock_level
														FROM " . TABLE_PRODUCTS . " AS p
														WHERE p.products_id = '" . $bundle_data['products_id'] . "' AND p.products_status = '1' AND p.products_purchase_mode<>3 ";
                                $product_result_sql = tep_db_query($product_select_sql);
                                if ($product_info = tep_db_fetch_array($product_result_sql)) {
                                    $temp_val = ${"q_" . $product_info['products_id']};  // Missing qty box for each subproduct
                                    $bd_products_array[] = array('id' => $product_info['products_id'],
                                        'bd_products_qty' => $temp_val);

                                    $total_bundle_weight += (int) $temp_val * $bundle_data["subproduct_weight"];

                                    switch ($product_info["products_purchase_mode"]) {
                                        case '1': // Always Add to Cart
                                        case '2': // Always Pre-Order
                                            break;
                                        case '4': // Auto Mode
                                            if (tep_not_null($product_info["products_out_of_stock_level"])) { // If there is setting for out of stock level
                                                $total_stock = $product_info['products_quantity'];
                                                if ($bdn == 'y') {
                                                    $current_qty_in_cart = tep_get_total_product_in_cart($customer_id, $product_info["products_id"], $_REQUEST['products_id']);  // exclude current product since it is in $temp_val
                                                } else {
                                                    $current_qty_in_cart = tep_get_total_product_in_cart($customer_id, $product_info["products_id"]);
                                                }
                                                $total_order = ($bundle_data["subproduct_qty"] * (int) $temp_val) + $current_qty_in_cart;

                                                if ($total_order && ($total_stock - $total_order < (int) $product_info["products_out_of_stock_level"])) {
                                                    $err1 = "SL";
                                                    break 2;
                                                }
                                            }
                                            break;
                                    }

                                    if ($total_bundle_weight && $total_bundle_weight > $allowed_weight) {
                                        $err1 = "OW"; // overweight message
                                        break;
                                    }
                                }
                            }
                            if (!$total_bundle_weight) {
                                $err1 = "ES"; // no selections had been made message
                            } else if ($total_bundle_weight > $allowed_weight) {
                                $err1 = "OW"; // overweight message
                            } else if ($total_bundle_weight < $allowed_weight) {
                                $err1 = "UW"; // underweight message
                            }
                        } else {
                            $err1 = "SL";
                        }

                        if ($err1) {
                            $parameters = array('action', 'pid', 'err', 'success');
                            $err = $err1;

                            header('Content-Type: text/xml');
                            echo "<result>";
                            echo "<type>redirect</type>";
                            echo "<url><![CDATA[" . tep_href_link(basename($PHP_SELF), tep_get_all_get_params($parameters) . ((substr(tep_get_all_get_params($parameters), -1, 1) == '&') ? '' : '&') . 'err=' . $err . '') . "]]></url>";
                            echo "</result>";
                            exit;
                        } else {
                            if ($bdn == 'y') {
                                $cart->add_cart($_REQUEST['products_id'], (int) $_REQUEST['buyqty'], '', '', $bd_products_array, 'y', '', $extra_info_array);
                            } else {
                                $cart->add_cart($_REQUEST['products_id'], $cart->get_quantity($_REQUEST['products_id']) + (int) $_REQUEST['buyqty'], '', '', $bd_products_array, '', '', $extra_info_array);
                            }
                            $success = 'CA';
                        }
                    } else { // single product
                        $suggested_amt = 0;
                        $prod_inventory_select_sql = "	SELECT products_quantity, products_purchase_mode, products_out_of_stock_level
														FROM " . TABLE_PRODUCTS . "
														WHERE products_id = '" . tep_db_input($_REQUEST['products_id']) . "' AND products_status=1";
                        $prod_inventory_result_sql = tep_db_query($prod_inventory_select_sql);
                        if ($prod_inventory_row = tep_db_fetch_array($prod_inventory_result_sql)) {
                            switch ($prod_inventory_row['products_purchase_mode']) {
                                case '1': // Always Add to Cart
                                case '2': // Always Pre-Order
                                    $show_it = 1;
                                    break;
                                case '3': // Always Out of Stock
                                    $show_it = 0;
                                    break;
                                case '4': // Auto Mode
                                    if (tep_not_null($prod_inventory_row['products_out_of_stock_level'])) { // If there is setting for out of stock level
                                        $current_qty_in_cart = tep_get_total_product_in_cart($customer_id, $_REQUEST['products_id']);
                                        $suggested_amt = (int) (tep_get_products_stock($_REQUEST['products_id'], true) - $current_qty_in_cart);

                                        if ((int) $prod_inventory_row['products_quantity'] - ((int) $_REQUEST['buyqty'] + (int) $current_qty_in_cart) < (int) $prod_inventory_row["products_out_of_stock_level"]) {
                                            $show_it = 0;
                                        } else {
                                            $show_it = 1;
                                        }
                                    } else {
                                        $show_it = 1;
                                    }
                                    break;
                            }

                            if ($show_it) {
                                $qty = (int) $_REQUEST['buyqty'];

                                $extra_info_array = array();
                                if ($cart->get_custom_prd_type($_REQUEST['products_id']) == 'cdkey') {
                                    $isCustom = true;

                                    //$product_info_query = tep_db_query("select p.products_price from " . TABLE_PRODUCTS . " AS p where p.products_id = '" . tep_db_input($_REQUEST['products_id']) . "'");
                                    //$product_info = tep_db_fetch_array($product_info_query);

                                    $final_array = array();
                                    //$final_array['calculated'] = array(	'price' => $product_info["products_price"] );
                                    $cart->set_custom_product_index(-1);
                                    $cart->set_custom_product_content($final_array);

                                    $extra_info_array['top_up_info'] = $_REQUEST['extra_info'];
                                    if (isset($_REQUEST['delivery_mode']) && (int) $_REQUEST['delivery_mode'] > 0) {
                                        $extra_info_array['delivery_mode'] = (int) $_REQUEST['delivery_mode'];
                                    }
                                } else {
                                    $isCustom = false;

                                    if ($cart->get_custom_prd_type($_REQUEST['products_id']) == 'hla') {
                                        $extra_info_array = array('hla_account_id' => $_REQUEST['hla_account_id']);
                                    }
                                }

                                $cart->add_cart($_REQUEST['products_id'], $qty, '', true, '', '', $isCustom, $extra_info_array);
                                $success = 'CA';
                            } else {
                                $temp_products = $cart->get_products();
                                $temp_total_cart_item = 0;

                                foreach ($temp_products as $temp_products_row) {
                                    $temp_total_cart_item += $temp_products_row['quantity'];
                                }

                                foreach ($temp_products as $temp_products_row) {
                                    if ($temp_products_row['id'] == $_REQUEST['products_id']) {
                                        $temp_added_product_name = $temp_products_row['name'];
                                    }
                                }

                                $custom_type = $cart->get_custom_prd_type($_REQUEST['products_id']);
                                if ($custom_type == 'hla') {
                                    $temp_added_product_name = strip_tags($temp_added_product_name, '<br>');
                                } else {
                                    $temp_added_product_name = strip_tags($temp_added_product_name);
                                }
                                $pd_encoding = mb_detect_encoding($temp_added_product_name);

                                $word_len_show = ((isset($pd_strlen_show_array[$pd_encoding])) ? $pd_strlen_show_array[$pd_encoding] : $default_pd_strlen_show);

                                if ($custom_type != 'hla' && mb_strlen($temp_added_product_name, $pd_encoding) >= $word_len_show) {
                                    $temp_added_product_name = mb_substr($temp_added_product_name, 0, $word_len_show, $pd_encoding) . '...';
                                }

                                header('Content-Type: text/xml');
                                echo '	<result>
					        				<type>cart</type>
		                    				<total_item><![CDATA[' . $temp_total_cart_item . ']]></total_item>
											<product_name><![CDATA[' . $temp_added_product_name . ']]></product_name>
											<subtotal><![CDATA[' . $currencies->format($cart->show_total(), false) . ']]></subtotal>
		                    			</result>';
                                exit;
                            }
                        }
                    }
                }


                $temp_products = $cart->get_products();
                $temp_total_cart_item = 0;

                foreach ($temp_products as $temp_products_row) {
                    if ($temp_products_row['custom_products_type_id'] == '3') {
                        if (isset($temp_products_row['products_bundle']) && $temp_products_row['products_bundle'] == 'yes') {
                            $temp_total_cart_item += $temp_products_row['quantity'];
                        } else {
                            $temp_total_cart_item += 1;
                        }
                    } else {
                        $temp_total_cart_item += $temp_products_row['quantity'];
                    }
                }

                $custom_product_type = $cart->get_custom_prd_type($_REQUEST['products_id']);

                foreach ($temp_products as $temp_products_row) {
                    if ($temp_products_row['id'] == $_REQUEST['products_id']) {
                        $temp_added_product_name = $temp_products_row['name'];

                        if (($custom_product_type == 'hla') &&
                                (tep_not_null($temp_products_row['custom_content']['hla_account_id']) && ($temp_products_row['custom_content']['hla_account_id'] == $_REQUEST['hla_account_id']))) {
                            $hla_char_sql = "	SELECT phd.products_hla_characters_name
												FROM " . TABLE_PRODUCTS_HLA . " ph
												LEFT JOIN " . TABLE_PRODUCTS_HLA_CHARACTERS . " AS phc
													ON phc.products_hla_id = ph.products_hla_id
												LEFT JOIN " . TABLE_PRODUCTS_HLA_DESCRIPTION . " AS phd
													ON phd.products_hla_characters_id = phc.products_hla_characters_id
														AND (IF(phd.language_id = '" . tep_db_input($languages_id) . "', 1, phd.language_id = '" . tep_db_input($default_languages_id) . "'))
												WHERE ph.products_hla_id = '" . (int) $_REQUEST['hla_account_id'] . "'
													AND ph.products_id = '" . (int) $_REQUEST['products_id'] . "'
													AND phd.products_hla_characters_name <> ''";
                            $hla_char_result = tep_db_query($hla_char_sql);
                            while ($hla_char_row = tep_db_fetch_array($hla_char_result)) {
                                $temp_char_name .= '<br />&raquo;&nbsp;' . strip_tags($hla_char_row['products_hla_characters_name']);
                            }
                        }
                    }
                }

                $temp_added_product_name = strip_tags($temp_added_product_name);
                $pd_encoding = mb_detect_encoding($temp_added_product_name);

                if (tep_not_null($temp_char_name)) {
                    $temp_added_product_name .= $temp_char_name;
                }

                $word_len_show = ((isset($pd_strlen_show_array[$pd_encoding])) ? $pd_strlen_show_array[$pd_encoding] : $default_pd_strlen_show);

                if ($custom_product_type != 'hla' && mb_strlen($temp_added_product_name, $pd_encoding) >= $word_len_show) {
                    $temp_added_product_name = mb_substr($temp_added_product_name, 0, $word_len_show, $pd_encoding) . '...';
                }

                header('Content-Type: text/xml');
                echo '	<result>
	        				<type>cart</type>
	        				<total_item><![CDATA[' . $temp_total_cart_item . ']]></total_item>
							<product_name><![CDATA[' . $temp_added_product_name . ']]></product_name>
							<subtotal><![CDATA[' . $currencies->format($cart->show_total(), false) . ']]></subtotal>
	        			</result>';
            }
            exit;

            break;
        case 'notify' :
            if (tep_session_is_registered('customer_id')) {
                if (isset($HTTP_GET_VARS['products_id'])) {
                    $notify = $HTTP_GET_VARS['products_id'];
                } else if (isset($HTTP_GET_VARS['notify'])) {
                    $notify = $HTTP_GET_VARS['notify'];
                } else if (isset($HTTP_POST_VARS['notify'])) {
                    $notify = $HTTP_POST_VARS['notify'];
                } else {
                    tep_redirect(tep_href_link(basename($PHP_SELF), tep_get_all_get_params(array('action', 'notify'))));
                }

                if (!is_array($notify))
                    $notify = array($notify);

                for ($i = 0, $n = sizeof($notify); $i < $n; $i++) {
                    $check_query = tep_db_query("select count(*) as count from " . TABLE_PRODUCTS_NOTIFICATIONS . " where products_id = '" . $notify[$i] . "' and customers_id = '" . $customer_id . "'");
                    $check = tep_db_fetch_array($check_query);
                    if ($check['count'] < 1) {
                        tep_db_query("insert into " . TABLE_PRODUCTS_NOTIFICATIONS . " (products_id, customers_id, date_added) values ('" . $notify[$i] . "', '" . $customer_id . "', now())");
                    }
                }
                tep_redirect(tep_href_link(basename($PHP_SELF), tep_get_all_get_params(array('action', 'notify'))));
            } else {
                $navigation->set_snapshot();
                tep_redirect(tep_href_link(FILENAME_LOGIN, '', 'SSL'));
            }
            break;
        case 'notify_remove' :
            if (tep_session_is_registered('customer_id') && isset($HTTP_GET_VARS['products_id'])) {
                $check_query = tep_db_query("select count(*) as count from " . TABLE_PRODUCTS_NOTIFICATIONS . " where products_id = '" . $HTTP_GET_VARS['products_id'] . "' and customers_id = '" . $customer_id . "'");
                $check = tep_db_fetch_array($check_query);
                if ($check['count'] > 0) {
                    tep_db_query("delete from " . TABLE_PRODUCTS_NOTIFICATIONS . " where products_id = '" . $HTTP_GET_VARS['products_id'] . "' and customers_id = '" . $customer_id . "'");
                }
                tep_redirect(tep_href_link(basename($PHP_SELF), tep_get_all_get_params(array('action'))));
            } else {
                $navigation->set_snapshot();
                tep_redirect(tep_href_link(FILENAME_LOGIN, '', 'SSL'));
            }
            break;
        case 'cust_order' :
            if (tep_session_is_registered('customer_id') && isset($HTTP_GET_VARS['pid'])) {
                if (tep_has_product_attributes($HTTP_GET_VARS['pid'])) {
                    tep_redirect(tep_href_link(FILENAME_PRODUCT_INFO, 'products_id=' . $HTTP_GET_VARS['pid']));
                } else {
                    //$cart->add_cart($HTTP_GET_VARS['pid'], $cart->get_quantity($HTTP_GET_VARS['pid'])+1);
                    $cart->add_cart($HTTP_GET_VARS['pid'], $cart->get_quantity($HTTP_GET_VARS['pid']) + (int) $HTTP_POST_VARS['buyqty']);
                }
            }
            tep_redirect(tep_href_link($goto, tep_get_all_get_params($parameters)));
            break;
    }
}

// include the who's online functions
require(DIR_WS_FUNCTIONS . 'whos_online.php');
tep_update_whos_online();

// include the password crypto functions
require(DIR_WS_FUNCTIONS . 'password_funcs.php');

// include validation functions (right now only email address)
require(DIR_WS_FUNCTIONS . 'validations.php');

// split-page-results
require(DIR_WS_CLASSES . 'split_page_results.php');

// infobox
require(DIR_WS_CLASSES . 'boxes.php');

define(THEMA, DIR_WS_THEME . "1/");
define(THEMA_IMAGES, DIR_WS_IMAGES);
//end multiple theme
// auto activate and expire banners
require(DIR_WS_FUNCTIONS . 'banner.php');
tep_activate_banners();
tep_expire_banners();

// auto expire special products
require(DIR_WS_FUNCTIONS . 'specials.php');
tep_expire_specials();

// include the breadcrumb class and start the breadcrumb trail
require(DIR_WS_CLASSES . 'breadcrumb.php');
$breadcrumb = new breadcrumb;

$breadcrumb->add(HEADER_TITLE_TOP, tep_href_link(HTTP_SERVER));

// add category names or the manufacturer name to the breadcrumb trail
if (isset($cPath_array) && count($cPath_array)) {
    $found_breadcrumb_game_id = false;

    $breadcrumb->add(BREADCRUMB_BUY, tep_href_link(FILENAME_SEARCH_ALL_GAMES));

    for ($cpath_cnt = 0, $n = sizeof($cPath_array); $cpath_cnt < $n; $cpath_cnt++) {
        if ($cPath_array[$cpath_cnt] == $page_info->main_game_id) {
            $found_breadcrumb_game_id = true;
        }

        if ($found_breadcrumb_game_id) {
            $breadcrumb->add(tep_get_categories_name($cPath_array[$cpath_cnt]), tep_href_link(FILENAME_SEARCH_ALL_GAMES, 'gid=' . $page_info->main_game_id));

            if ($cPath_array[$cpath_cnt] == $page_info->main_game_id && in_array($ctype, $page_info->custom_product_type_child_id_array)) {
                if ($page_info->custom_products_type_id_cat > 0) {
                    $tpl_store_name = tep_get_store_keyword($ctype);
                    $breadcrumb->add($tpl_store_name, tep_href_link(FILENAME_DEFAULT, 'pagetype=q&ctype=' . $ctype));
                }
            }
        }
    }
} else if ($HTTP_GET_VARS['pagetype'] == "q") {
    $breadcrumb->add(BREADCRUMB_BUY, tep_href_link(FILENAME_SEARCH_ALL_GAMES));
    $breadcrumb->add(tep_get_store_keyword($ctype), tep_href_link('index.php', 'pagetype=q&ctype=' . $ctype));
} elseif (isset($HTTP_GET_VARS['manufacturers_id'])) {
    $manufacturers_query = tep_db_query("select manufacturers_name from " . TABLE_MANUFACTURERS . " where manufacturers_id = '" . (int) $HTTP_GET_VARS['manufacturers_id'] . "'");
    if (tep_db_num_rows($manufacturers_query)) {
        $manufacturers = tep_db_fetch_array($manufacturers_query);
        $breadcrumb->add($manufacturers['manufacturers_name'], tep_href_link(FILENAME_DEFAULT, 'manufacturers_id=' . $HTTP_GET_VARS['manufacturers_id']));
    }
}

// initialize the message stack for output messages
require(DIR_WS_CLASSES . 'message_stack.php');
$messageStack = new messageStack;

// set which precautions should be checked
define('WARN_INSTALL_EXISTENCE', 'true');
define('WARN_CONFIG_WRITEABLE', 'true');
define('WARN_SESSION_DIRECTORY_NOT_WRITEABLE', 'true');
define('WARN_SESSION_AUTO_START', 'true');
define('WARN_DOWNLOAD_DIRECTORY_NOT_READABLE', 'true');
// Include OSC-AFFILIATE

require(DIR_WS_INCLUDES . 'affiliate_application_top.php');
require(DIR_WS_INCLUDES . 'add_ccgvdc_application_top.php');

$const_alluseraccess = "0";

if ((int) $_REQUEST['products_id'] == 0) {
    $temp_cPath = $cPath;
} else {
    $temp_cPath = (string) tep_get_product_parent_id((int) $_REQUEST['products_id']);
}

if (tep_check_cat_permission($temp_cPath, $customers_groups_id)) {
    $const_i_am_allowed_here = true;
} else {
    $const_i_am_allowed_here = false;
}

//include archive class
require_once(DIR_WS_CLASSES . 'archive.php');

/* * ******************************************************************
  Know when google crawls your site (For WOW US and WOW EU)
 * ****************************************************************** */
if (strpos($_SERVER['HTTP_USER_AGENT'], 'Googlebot') !== false && ($current_category_id == 16 || $current_category_id == 1694) && tep_not_null(GOOGLEBOT_CRAWL_NOTIFICATION_EMAIL)) {
    $google_email_to_array = tep_parse_email_string(GOOGLEBOT_CRAWL_NOTIFICATION_EMAIL);

    for ($google_email_cnt = 0; $google_email_cnt < count($google_email_to_array); $google_email_cnt++) {
        tep_mail($google_email_to_array[$google_email_cnt]['name'], $google_email_to_array[$google_email_cnt]['email'], 'Googlebot Visit', 'Googlebot has visited your page: ' . $_SERVER['REQUEST_URI'] . ' at ' . date('Y-m-d H:i:s'), STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
    }
}

// Mantis # 0000024 @ 200810211812 - intialize the customers security class
require(DIR_WS_CLASSES . 'customers_upkeep.php');
require(DIR_WS_CLASSES . 'customers_security.php');

// Mantis # 0000024 @ 200810211813 - Set Secret Q&A
$customers_upkeep_obj = new customers_upkeep();
$customers_security_obj = new customers_security($_SESSION['languages_id']);

if (tep_session_is_registered('customer_id')) {
    // Load customer upkeep setting
    $customers_upkeep_obj->reloadUpkeep($_SESSION['customer_id']);
    $customers_security_obj->set_customers_id($_SESSION['customer_id']);

//    if (!(int)$customers_upkeep_obj->getUpkeepValue('reset_password')) {
//        $date_diff = $customers_security_obj->get_convert_pin_expired_date();
//
//        if (!is_null($date_diff) && $date_diff <= 0 && (basename($_SERVER['PHP_SELF'])!= FILENAME_SET_SECRET_QNA && basename($_SERVER['PHP_SELF'])!= FILENAME_LOGOFF)) {
//            tep_redirect(tep_href_link(FILENAME_SET_SECRET_QNA));
//        }
//    }
}

// Facebook Connect
require_once(DIR_WS_CLASSES . 'ogm_facebook.php');
$ogm_fb_obj = new ogm_facebook(FB_API_KEY, FB_SECRET);
$ogm_fb_obj->set_page_info_obj($page_info);
$ogm_fb_obj->set_FB_Locales($FB_Locales);
$ogm_fb_obj->OGFB_process_flow_state_controller();
$ogm_fb_obj->OGFB_execute_state_controller();
?>