<?
    $boxHeading = '';
    $background_color = 'class="systemBox"';
    $heading_title_style = 'class="systemBoxHeading"';
    $boxText = "systemBoxText";
    $corner_left = 'square';
    $corner_right = 'square';
	$boxContent_attributes = ' align="center"';
	
	$box_style = 2;
	
    $boxContent = '<table border="0" cellspacing="0" cellpadding="1" width="90%">
    					<tr>
    						<td align="center">' . tep_image(DIR_WS_IMAGES . 'icon_ogmguarantee.gif', '', 164, 101) . '</td>
    					</tr>
    					<tr>
							<td>' . tep_draw_separator('pixel_trans.gif', '1', '5') . '</td>
						</tr>
    					<tr>
    						<td align="center">
    							<a target="_blank" href="https://www.mcafeesecure.com/RatingVerify?ref=www.offgamers.com"><img width="94" height="54" border="0" src="//images.scanalert.com/meter/www.offgamers.com/23.gif" alt="McAfee Secure sites help keep you safe from identity theft, credit card fraud, spyware, spam, viruses and online scams" oncontextmenu="alert(\'Copying Prohibited by Law - McAfee Secure is a Trademark of McAfee, Inc.\'); return false;"></a>
    						</td>
    					<tr>
    					<tr>
							<td>' . tep_draw_separator('pixel_trans.gif', '1', '5') . '</td>
						</tr>
						<tr>
    						<td align="center"> 
    							<a href="http://myworld.ebay.com/offgamers" target="_blank">' . tep_image(DIR_WS_IMAGES . 'icon_bottom_ebay.gif', '', 118, 69) . '</a>
    						</td>
    					</tr>
    				</table>';
	
    require(DIR_WS_TEMPLATES . TEMPLATENAME_BOX);
	
  	$boxContent_attributes = '';
?>