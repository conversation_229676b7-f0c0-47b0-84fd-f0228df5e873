<?
/*
  	$Id: best_sellers.php,v 1.9 2007/07/27 13:50:25 chan Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2003 osCommerce
	
  	Released under the GNU General Public License
*/

if (SHOW_BESTSELLERS == "true") {
	if (isset($current_category_id) && ($current_category_id > 0)) {
		$best_sellers_select_sql = "select distinct p.products_id, pd.products_name 
									from " . TABLE_PRODUCTS_TO_CATEGORIES . " p2c, " . TABLE_CATEGORIES . " c, " . TABLE_PRODUCTS . " p, " . TABLE_PRODUCTS_DESCRIPTION . " pd 
									where p2c.products_id=p.products_id 
										and p2c.categories_id = c.categories_id 
										and p2c.products_id = pd.products_id 
										and '" . (int)$current_category_id . "' in (c.categories_id, c.parent_id) 
										and c.categories_status = '1' 
										and p.products_status = '1' 
										and p.products_ordered > 0 
										and p.custom_products_type_id=0 
										and pd.language_id = '" . (int)$languages_id . "' 
										order by p.products_ordered desc, pd.products_name limit " . MAX_DISPLAY_BESTSELLERS;
	    $best_sellers_query = tep_db_query($best_sellers_select_sql);
	} else {
	    //$best_sellers_query = tep_db_query("select distinct p.products_id, pd.products_name from " . TABLE_PRODUCTS . " p, " . TABLE_PRODUCTS_DESCRIPTION . " pd where p.products_status = '1' and p.products_ordered > 0 and p.products_id = pd.products_id and pd.language_id = '" . (int)$languages_id . "' order by p.products_ordered desc, pd.products_name limit " . MAX_DISPLAY_BESTSELLERS);
	    $best_sellers_select_sql = "select distinct p.products_id, pd.products_name 
									from " . TABLE_PRODUCTS_TO_CATEGORIES . " p2c, " . TABLE_CATEGORIES . " c, " . TABLE_PRODUCTS . " p, " . TABLE_PRODUCTS_DESCRIPTION . " pd 
									where p2c.products_id=p.products_id 
										and p2c.categories_id = c.categories_id 
										and p2c.products_id = pd.products_id 
										and c.categories_status = '1' 
										and p.products_status = '1' 
										and p.products_ordered > 0 
										and p.custom_products_type_id=0 
										and pd.language_id = '" . (int)$languages_id . "' 
										order by p.products_ordered desc, pd.products_name limit " . MAX_DISPLAY_BESTSELLERS;
	    $best_sellers_query = tep_db_query($best_sellers_select_sql);
	}
	
	if (tep_db_num_rows($best_sellers_query) >= MIN_DISPLAY_BESTSELLERS) {
?>
		<!-- best_sellers //-->
<?
		$boxHeading = tep_image('images/bullet-title.gif', '', 7, 7) . ' ' . BOX_HEADING_BESTSELLERS;
		$background_color = 'class="systemBox"';
		$heading_title_style = 'class="systemBoxHeading"';
		$boxText = "systemBoxText";
		$corner_left = 'square';
		$corner_right = 'square';
		
	  	$rows = 0;
	  	$boxContent = '	<table border="0" width="100%" cellspacing="0" cellpadding="0" style="background-image: url(images/side-box-mid.gif); background-repeat: repeat-y;">
	  						<tr>
	  							<td align="center">
			  						<table border="0" width="90%" cellspacing="0" cellpadding="0">
				  						<tr>
				  							<td height="4"></td>
				  						</tr>';
	  	while ($best_sellers = tep_db_fetch_array($best_sellers_query)) {
		   	$rows++ ;
			$boxContent .= '			<tr>
				    						<td class="systemBoxText" valign="top">' . tep_row_number_format($rows).'&nbsp' .'<a href="' . tep_href_link(FILENAME_PRODUCT_INFO, 'products_id=' . $best_sellers['products_id']) . '" class="systemBoxNavText">' . $best_sellers['products_name'] . "</td>
				    					</tr>" ;
	  	}
	  	if ($rows)
	  		$boxContent .= '		</table>
	  							</td>
	  						</tr>
	  					</table>';
	  	else
	  		$boxContent .= '<tr><td>&nbsp;</td></tr></table>';
	  	
	  	require(DIR_WS_TEMPLATES . TEMPLATENAME_BOX);
?>
	<!-- best_sellers_eof //-->
<?
	}
}
?>