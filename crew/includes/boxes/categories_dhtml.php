<!-- categories //-->

          <tr>
            <td>
<?php

  
/*
  Dynamic DHTL MenuBar for category listing v 0.2.1
  This is a combination of modified categories.php and
  HV Menu javacript written by <PERSON><PERSON> (c) 2000 version 5.411 24 December 2001 (updated Jan 31st, 2003 by Dynamic Drive for Opera7)
*	HV Menu found on Dynamic Drive ONLY may be used on both commercial and non commerical sites	*
*	For info about HV Menu <NAME_EMAIL>			

  Modifications for osCommerce menuBar_0_2 made by <PERSON> 2/9/2003
  <EMAIL>
*/
  define('LEFT_LINK_HEIGHT',20);
  define('LEFT_LINK_WIDTH',BOX_WIDTH);
 
  $info_box_contents = array(array('align' => 'center',
                               'text'  => BOX_HEADING_CATEGORIES
                              ));
  //new infoBoxHeading($info_box_contents, true, false);
?>
<script type='text/javascript'>
/*********************************************************************************************************************************************
*	(c) Ger Versluis 2000 version 5.411 24 December 2001 (updated Jan 31st, 2003 by Dynamic Drive for Opera7)
*	HV Menu found on Dynamic Drive ONLY may be used on both commercial and non commerical sites	*
*	For info <NAME_EMAIL>							        *
*       This script featured on Dynamic Drive DHTML code library: http://www.dynamicdrive.com
**********************************************************************************************************************************************/

	var LowBgColor='transparent';			// Background color when mouse is not over
	var LowSubBgColor='black';			// Background color when mouse is not over on subs
	var HighBgColor='transparent';			// Background color when mouse is over
	var HighSubBgColor='#83856E';			// Background color when mouse is over on subs
	var FontLowColor='#83856E';			// Font color when mouse is not over
	var FontSubLowColor='#83856E';			// Font color subs when mouse is not over
	var FontHighColor='#B5BC91';			// Font color when mouse is over
	var FontSubHighColor='#B5BC91';			// Font color subs when mouse is over
	var BorderColor='transparent';			// Border color
	var BorderSubColor='#FFFFFF';			// Border color for subs
	var BorderWidth=1;				// Border width
	var BorderBtwnElmnts=0;			// Border between elements 1 or 0
	var FontFamily="Verdana, Arial, sans-serif";	// Font family menu items
	var FontSize=7;				// Font size menu items
	var FontBold=0;				// Bold menu items 1 or 0
	var FontItalic=0;				// Italic menu items 1 or 0
	var MenuTextCentered='left';			// Item text position 'left', 'center' or 'right'
	var MenuCentered='left';			// Menu horizontal position 'left', 'center' or 'right'
	var MenuVerticalCentered='top';		// Menu vertical position 'top', 'middle','bottom' or static
	var ChildOverlap=.0;				// horizontal overlap child/ parent
	var ChildVerticalOverlap=.0;			// vertical overlap child/ parent
	var StartTop=1;				// Menu offset x coordinate
	var StartLeft=0;				// Menu offset y coordinate
	var VerCorrect=0;				// Multiple frames y correction
	var HorCorrect=0;				// Multiple frames x correction
	var LeftPaddng=2;				// Left padding
	var TopPaddng=1;				// Top padding
	var DissapearDelay=500;			// delay before menu folds in

	var FirstLineHorizontal=0;			// SET TO 1 FOR HORIZONTAL MENU, 0 FOR VERTICAL
	var MenuFramesVertical=1;			// Frames in cols or rows 1 or 0
	var TakeOverBgColor=1;			// Menu frame takes over background color subitem frame
	var FirstLineFrame='navig';			// Frame where first level appears
	var SecLineFrame='space';			// Frame where sub levels appear
	var DocTargetFrame='space';			// Frame where target documents appear
	var TargetLoc='MenuBar';				// span id for relative positioning
	var HideTop=0;				// Hide first level when loading new document 1 or 0
	var MenuWrap=1;				// enables/ disables menu wrap 1 or 0
	var RightToLeft=0;				// enables/ disables right to left unfold 1 or 0
	var UnfoldsOnClick=0;			// Level 1 unfolds onclick/ onmouseover
	var WebMasterCheck=0;			// menu tree checking on or off 1 or 0
	var ShowArrow=1;				// Uses arrow gifs when 1
	var KeepHilite=1;				// Keep selected path highligthed
	var Arrws=['<?php echo DIR_WS_IMAGES; ?>tri.gif',10,10,'<?php echo DIR_WS_IMAGES; ?>tridown.gif',10,10,'<?php echo DIR_WS_IMAGES; ?>trileft.gif',10,10];	// Arrow source, width and height

function BeforeStart(){return;}
function AfterBuild(){return;}
function BeforeFirstOpen(){return;}
function AfterCloseAll(){return;}
function Go(){return;}

<?php  

 $categories_string='';

  $number_top_levels = build_menus(0,'','');
  echo 'var NoOffFirstLineMenus= ' . $number_top_levels. ';'	;		// Number of first level items
  echo $categories_string;

function build_menus($currentParID,$menustr,$catstr) {
    global $categories_string, $id, $languages_id;
 // echo $list;
  
   
   $tmpCount = 0;
   $haschildren = 0; //default

// BOF Enable - Disable Categories Contribution-------------------------------------- 
// BOF Original line

   $categories_query_catmenu = tep_db_query("select c.categories_id, cd.categories_name, c.parent_id from " . TABLE_CATEGORIES . " c, " . TABLE_CATEGORIES_DESCRIPTION . " cd where c.parent_id = '" . $currentParID . "' and c.categories_id = cd.categories_id and cd.categories_groups='0' and cd.language_id='" . $languages_id ."' order by sort_order, cd.categories_name");
// EOF Original line

// BOF Enable - Disable Categories
//   $categories_query_catmenu = tep_db_query("select c.categories_id, cd.categories_name, c.parent_id from " . TABLE_CATEGORIES . " c, " . TABLE_CATEGORIES_DESCRIPTION . " cd where c.categories_status = '1' and c.parent_id = '" . $currentParID . "' and c.categories_id = cd.categories_id and cd.language_id='" . $languages_id ."' order by sort_order, cd.categories_name");
// EOF Enable - Disable Categories
// EOF Enable - Disable Categories Contribution-------------------------------------- 

   
   while ($categories = tep_db_fetch_array($categories_query_catmenu))  {
     $tmpString = '';
     $tmpCount += 1;
     $haschildren=tep_has_category_subcategories($categories['categories_id']);

     if ($haschildren) {
         if($menustr != ''){
           $menu_tmp = $menustr . '_' . $tmpCount;
         } else {
           $menu_tmp = $tmpCount;
         }
         if($catstr != ''){
           $cat_tmp = $catstr . '_' . $categories['categories_id'];
         } else {
           $cat_tmp = $categories['categories_id'];
         }
       $NumChildren = build_menus($categories['categories_id'],$menu_tmp,$cat_tmp);     } else {
       $NumChildren = 0; 
      }

    if ($id==$categories['categories_id']) {
      $tmpString .= '<b>';
    }
    // display category name
    $tmpString .= $categories['categories_name'];

    if ( $id==$categories['categories_id'] ) {
      $tmpString .= '</b>';
    }
    if (SHOW_COUNTS == 'true') {
      $products_in_category = tep_count_products_in_category($categories['categories_id']);
      if ($products_in_category > 0) {
        $tmpString .= ' (' . $products_in_category . ')';
      }
    }

     if($catstr != ''){
        $cPath_new = 'cPath=' . $catstr . '_' . $categories['categories_id'];
     } else {
        $cPath_new = 'cPath=' . $categories['categories_id'];
     }

  // Menu tree
//	Menu1_1_1=new Array(Text to show, Link, background image (optional), number of sub elements, height, width);
         if($menustr != ''){
           $menu_tmp = $menustr . '_' . $tmpCount;
         } else {
           $menu_tmp = $tmpCount;
         }
$categories_string .=  'Menu' . ($menustr!=''?$menustr.'_':'') . $tmpCount;
$categories_string .=  '= new Array("' . $tmpString . '","';
$categories_string .=  tep_href_link(FILENAME_DEFAULT, $cPath_new);
$categories_string .= '","",' ;
$categories_string .= $NumChildren;
$categories_string .= ',' . LEFT_LINK_HEIGHT . ',' . LEFT_LINK_WIDTH ;
$categories_string .= '); ';
   
    }// end while
    return $tmpCount;
  }  //end build menus

echo '</script>';

$tabletext ="<table><tr><td><div id='MenuBar' style='position:relative; width: " . LEFT_LINK_WIDTH . "; height: " . ($number_top_levels*LEFT_LINK_HEIGHT) . ";'>&nbsp;</div></td></tr></table>";

  $info_box_contents = array();
  $info_box_contents[] = array('align' => 'left',
                               'text'  => $tabletext
                              );
 new infoBox($info_box_contents);


?>
<SCRIPT LANGUAGE="JavaScript1.2" SRC="includes/menu_animation.js"></SCRIPT>

           </td>
          </tr>
<? if (tep_session_is_registered('customer_id')) { 
	global $customer_id;
    $customer_group_query = tep_db_query("select customers_groups_id from " . TABLE_CUSTOMERS . " where customers_id =  '" . $customer_id . "'");
    $customer_group = tep_db_fetch_array($customer_group_query);
	
	$customers_name_query = tep_db_query("select customers_groups_name from " . TABLE_CUSTOMERS_GROUPS . " where customers_groups_id = " . $customer_group['customers_groups_id']);
    $customers_name = tep_db_fetch_array($customers_name_query);
    
    if($customers_name['customers_groups_name']!="MEMBER"){
    	$grp_name_query = tep_db_query("select categories_id from categories_groups where groups_id = " . $customer_group['customers_groups_id'] . " order by categories_id");
    	$grp_name = tep_db_fetch_array($grp_name_query);
    	if($grp_name['categories_id']){
	?>
<tr>
<td>
<?

 include(DIR_WS_BOXES . 'categories.php');

?>
</td>
</tr>
<? } 
}
}
?>
<!-- categories_eof //-->
