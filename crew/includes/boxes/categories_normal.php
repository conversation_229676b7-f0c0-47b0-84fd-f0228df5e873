<?
/*
  $Id: categories_normal.php,v 1.19 2011/08/04 09:36:24 weichen Exp $

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2003 osCommerce

  Released under the GNU General Public License
*/

$boxHeading = BOX_HEADING_CATEGORIES;
$background_color = 'class="storeBox"';
$heading_title_style = 'class="storeBoxHeading"';
$boxText = 'storeText';
$corner_left = 'rounded';
$corner_right = 'square';

unset($parent_id);
unset($first_id);

function tep_show_list_items($ListItems, $Level=0, $nested_loop) {
	global $languages_id, $cust_grp_id, $const_alluseraccess, $cPath_array, $boxContent, $loop_level;
	$SubTotal=0;
	
	foreach ($ListItems as $ListItem)
	{
		$boxContent.= "<table class='storeBoxText' border='0'>
						<tr><td valign='top'>";
		$boxContent .= str_repeat("&nbsp;", $Level*4);
		
		$NewListItems = array() ;
		
		if ($nested_loop && in_array($ListItem["categories_id"], $cPath_array) && ($Level < $loop_level || $ListItem["categories_id"]==end($cPath_array))) {
			$cat_select_sql = "	SELECT c.categories_id, c.products_count, c.parent_id, cd.categories_name 
								FROM " . TABLE_CATEGORIES . " AS c 
								INNER JOIN " . TABLE_CATEGORIES_DESCRIPTION . " AS cd 
									ON c.categories_id = cd.categories_id 
								INNER JOIN " . TABLE_CATEGORIES_GROUPS . " AS cg 
									ON c.categories_id = cg.categories_id 
								WHERE c.parent_id='" . $ListItem["categories_id"] . "' 
									AND c.categories_status = '1' AND ((cg.groups_id = '".$cust_grp_id. "') OR (cg.groups_id = '".$const_alluseraccess. "')) 
									AND cd.language_id='" . (int)$languages_id ."' 
								ORDER BY sort_order, cd.categories_name " ;
			$cat_result_sql = tep_db_query($cat_select_sql);
			while ($cat_row = tep_db_fetch_array($cat_result_sql)) {
				$NewListItems[] = $cat_row ;
			}
		}
		
		$cPath_new = tep_get_path($ListItem["categories_id"]);
		
		$boxContent .= '<a href="' . tep_href_link(FILENAME_DEFAULT, $cPath_new) . '" class="categoryNavigation">+</a></td>';
	    
		$SubTotal += 1 ;
		$DisplayName = $ListItem["categories_name"];
		if (!$DisplayName) $DisplayName = "" ;
		
    	$cat_url = '<a href="' . tep_href_link(FILENAME_DEFAULT, $cPath_new) . '" class="categoryNavigation">'.$DisplayName.'</a>';
    	
    	if (isset($cPath_array) && in_array($ListItem["categories_id"], $cPath_array)) {
		   	$cat_url = '<b>'.$cat_url."</b>";
		}
		
		if (SHOW_COUNTS == 'true') {
		   	//$products_in_category = tep_count_products_in_category($ListItem["categories_id"]);
		   	$products_in_category = $ListItem["products_count"];
		   	if ($products_in_category > 0) {
		   		$cat_url .= '&nbsp;<span class="storeBoxText">(' . $products_in_category . ')</span>';
		   	}
	    }
	    
		$boxContent .= '<td>'.$cat_url;
		$boxContent .= '</td></tr>'; 
		$boxContent .="</table>";
		
		$SubTotal += tep_show_list_items($NewListItems, $Level+1, $nested_loop) ;
	}
	return $SubTotal ;
}
?>
<!-- categories //-->
<?

$customer_group_query = tep_db_query("select customers_groups_id from " . TABLE_CUSTOMERS . " where customers_id =  '" . $customer_id . "'");
$customer_group = tep_db_fetch_array($customer_group_query);
$cust_grp_id = ($customer_group['customers_groups_id']) ? ($customer_group['customers_groups_id']) : '1';	// 1 = Guest

$boxContent = '';
$tree = array();

$new_path = is_array($cPath_array) ? implode("_", $cPath_array) . '_' : '';

$pinned_cat_id = 0;

$ListItems = array() ;

// get root record
if ($pinned_cat_id) {
	$root_cat_select_sql = "	SELECT c.categories_id, c.products_count, cd.categories_name 
								FROM " . TABLE_CATEGORIES . " AS c 
								INNER JOIN " . TABLE_CATEGORIES_DESCRIPTION . " AS cd 
									ON c.categories_id = cd.categories_id 
								INNER JOIN " . TABLE_CATEGORIES_GROUPS . " AS cg 
									ON c.categories_id = cg.categories_id 
								WHERE c.categories_id='" . $pinned_cat_id . "' 
									AND c.categories_status = '1' AND ((cg.groups_id = '".$cust_grp_id. "') OR (cg.groups_id = '".$const_alluseraccess. "')) 
									AND cd.language_id='" . (int)$languages_id ."' 
								ORDER BY sort_order, cd.categories_name " ;
	$nested_loop = true;
	$loop_level = count($cPath_array) - 1 - array_search($pinned_cat_id, $cPath_array);
} else {
	$root_cat_select_sql = "SELECT c.categories_id, c.products_count, cd.categories_name, c.parent_id, cde.groups_id 
							FROM categories AS c, categories_description AS cd, categories_groups AS cde 
							WHERE c.parent_id = $current_category_id 
								AND c.categories_status = '1' AND ((cde.groups_id = '".$cust_grp_id. "') OR (cde.groups_id = '".$const_alluseraccess. "'))
		                        AND c.categories_id = cd.categories_id AND c.categories_id = cde.categories_id 
		                        AND cd.language_id='" . (int)$languages_id ."' order by sort_order, cd.categories_name";
	$nested_loop = false;
}

$root_cat_result_sql = tep_db_query($root_cat_select_sql);
while ($root_cat_row = tep_db_fetch_array($root_cat_result_sql)) {
	$ListItems[] = $root_cat_row ;
}

/****************************************************************
	If no more subcategories then display it 
	and its siblings if any.
****************************************************************/
if (!count($ListItems) && count($cPath_array) >= 2) {
	$cur_parent_id = $cPath_array[count($cPath_array)-2];
	
	$root_cat_select_sql = "SELECT c.categories_id, c.products_count, cd.categories_name, c.parent_id, cde.groups_id 
							FROM categories AS c, categories_description AS cd, categories_groups AS cde 
							WHERE c.parent_id = $cur_parent_id 
								AND c.categories_status = '1' AND ((cde.groups_id = '".$cust_grp_id. "') OR (cde.groups_id = '".$const_alluseraccess. "'))
		                        AND c.categories_id = cd.categories_id AND c.categories_id = cde.categories_id 
		                        AND cd.language_id='" . (int)$languages_id ."' order by sort_order, cd.categories_name";
	
	$root_cat_result_sql = tep_db_query($root_cat_select_sql);
	
	while ($root_cat_row = tep_db_fetch_array($root_cat_result_sql)) {
		$ListItems[] = $root_cat_row ;
	}
}

tep_show_list_items($ListItems, 0, $nested_loop);

require(DIR_WS_TEMPLATES . TEMPLATENAME_BOX);
?>
<!-- categories_eof //-->