<!-- categories //-->
<? if (tep_session_is_registered('customer_id')) { 
	global $customer_id;
    $customer_group_query = tep_db_query("select customers_groups_id from " . TABLE_CUSTOMERS . " where customers_id =  '" . $customer_id . "'");
    $customer_group = tep_db_fetch_array($customer_group_query);

    $customers_name_query = tep_db_query("select customers_groups_name from " . TABLE_CUSTOMERS_GROUPS . " where customers_groups_id = " . $customer_group['customers_groups_id']); 
    $customers_name = tep_db_fetch_array($customers_name_query);
    
    if($customers_name['customers_groups_name']!="MEMBER"){
    	$grp_name_query = tep_db_query("select categories_id from categories_groups where groups_id = " . $customer_group['customers_groups_id'] . " order by categories_id");
    	$grp_name = tep_db_fetch_array($grp_name_query);
    	if($grp_name['categories_id']){
	?>
<tr>
<td>
<?

 include(DIR_WS_BOXES . 'categories.php');

?>
</td>
</tr>
<? } 
}
}
?>
<!-- categories_eof //-->
