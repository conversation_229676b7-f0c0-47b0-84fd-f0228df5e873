<?php
/*
  $Id: currencies.php,v 1.8 2008/12/18 06:55:50 boonhock Exp $

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2003 osCommerce

  Released under the GNU General Public License
*/

if (isset($currencies) && is_object($currencies)) {
?>
<!-- currencies //-->
<?php
	$box_style = 'Gray_Box_R';

    $boxHeading = BOX_HEADING_CURRENCIES;
    $background_color = 'class="systemBox"';
    $heading_title_style = 'class="systemBoxHeading"';
    $boxText = "systemBoxText";
    $corner_left = 'square';
    $corner_right = 'square';
	$boxContent_attributes = ' align="center"';
	
    reset($currencies->currencies);
    $currencies_array = array();
    while (list($key, $value) = each($currencies->currencies)) {
      	$currencies_array[] = array('id' => $key, 'text' => $value['title']);
    }
	
    $hidden_get_variables = '';
    reset($HTTP_GET_VARS);
    while (list($key, $value) = each($HTTP_GET_VARS)) {
      	if ( ($key != 'currency') && ($key != tep_session_name()) && ($key != 'x') && ($key != 'y') ) {
        	$hidden_get_variables .= tep_draw_hidden_field($key, $value);
      	}
    }
	
    $boxContent = tep_draw_form('currencies', tep_href_link(basename($PHP_SELF), '', $request_type, false), 'get');
    $boxContent .= '		<table cellpadding=5 cellspacing=0 border=0 width=100%><tr><td>';
    $boxContent .= tep_draw_pull_down_menu('currency', $currencies_array, $currency, 'onChange="this.form.submit();" id="select_currency"');
    $boxContent .= $hidden_get_variables;
    $boxContent .= tep_hide_session_id();
    $boxContent .= '
		<div class="payment_gateway_box_bg"><div class="payment_gateway_box_t"><div class="payment_gateway_box_b"><div class="payment_gateway_box_l"><div class="payment_gateway_box_r"><div class="payment_gateway_box_tr"><div class="payment_gateway_box_tl"><div class="payment_gateway_box_bl"><div class="payment_gateway_box_br">
			<table width=100% border=0 cellpadding=2 cellspacing=0>
				<tr class="payment_gateway_header">
					<td nowrap><b>'.TEXT_PAYMENT_GATEWAY.'</b></td>
					<td nowrap><b>'.TABLE_HEADING_STATUS.'</b></td>
				</tr>';

	require_once(DIR_WS_CLASSES . 'payment.php');
	$payment_modules = new payment;
	
	$box_pm_selection = $payment_modules->selection($currency);

	foreach ($box_pm_selection as $payment_row) {
		if ($payment_row['display_module'] && $payment_row['logo'] && ($payment_row['display_zone'] == "1" || $payment_row['display_zone'] == "2")) {
			$boxContent .= '
				<tr><td colspan=2><img src="images/OGM2008/payment_gateway_dotted_line.gif"/></td></tr>
				<tr class="payment_gateway_row">
					<td><font>'.$payment_row['display_module'].'</font></td>
					<td align=center>';

			if ($payment_row['online_status'] == "Online")
				$boxContent .= '						<img src="images/OGM2008/icon/icon-on.gif" width="13" height="13">';
			else {
				$boxContent .= '						<img src="images/OGM2008/icon/icon-off.gif" width="13" height="13">';
			}

			$boxContent .= '
					</td>
				</tr>';

			if ($payment_row['online_status'] == "Offline" && $payment_row['offline_message']){
				$boxContent .= '
				<tr class="payment_gateway_down" align=center>
					<td colspan=2><font>'.$payment_row['offline_message'].'</font></td>
				</tr>';

			}


		}
	}

	$boxContent .= '
			</table>
		</div></div></div></div></div></div></div></div></div>
		<a href="http://www.offgamers.com/payment-methods-i-666.ogm" style="margin: 0 0 5px 5px; display: block; clear:left;">'.LINK_VIEW_ALL_PAYMENT_GATEWAY.'</a>
		</td></tr></table>';

    $boxContent .= '</form>';
	
    require(DIR_WS_TEMPLATES . TEMPLATENAME_BOX);
?>
<!-- currencies_eof //-->
<?
  	$boxContent_attributes = '';
}
?>