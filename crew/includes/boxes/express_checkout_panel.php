<?php
	# retrieve game list
	$game_available = array();
	
	if (is_array($zone_info_array[1]->zone_categories_id) && count($zone_info_array[1]->zone_categories_id)) {
		$this_cat_obj = new category(0);
		$game_available = $this_cat_obj->get_game_product_type_cat_info(array(0, 1, 2));
	}
?>


<!-- Express Checkout Panel -->
<div class="ecBoxShow" id="ecBoxShow" style="display: none;">
	
	<!-- Express Checkout Header -->
	<div class="ecBoxHeader" id="ecBoxHeader">
		<span class="ecTxtMed" style="padding: 14px 0px 0px 14px;"><?=EXPRESS_CHECKOUT_TITLE;?></span>
		<span class="ecTxtSml" style="padding: 0px 0px 0px 14px;"><?=EXPRESS_CHECKOUT_INFORMATION;?></span>
		
		<div class="ecBrkLine" style="height: 20px;"><!-- --></div>
		
		<!-- Select game -->
		<div style="left: 14px; position: absolute;">
			<select name="cPath" id="cPath" onChange="javascript: exp_prod_type_list();">
<?php 			if (isset($game_available['sorting']) && is_array($game_available['sorting'])) {
				for ($game_cnt=0, $game_total_cnt=count($game_available['sorting']); $game_cnt < $game_total_cnt; $game_cnt++) { ?>
					<option value="<?=$game_available[$game_available['sorting'][$game_cnt]]['cPath'];?>"><?=$game_available[$game_available['sorting'][$game_cnt]]['name'];?></option>
<?php 			}
			} ?>
			</select>
		</div>
	</div>
	
	
	<!-- Express Checkout Content -->
	<div class="ecBoxCont">
		
		<!-- Select product type -->
		<div id="ecProdType" style="left: 14px; position: absolute;">
			<select name="product_type" id="product_type" onChange="javascript: exp_prod_list();"></select>
		</div>
		<div class="ecBrkLine" style="height: 35px;"><!-- --></div>
		
		
		<!-- Select product -->
		<div id="ecProd" style="display: none;">
			<div id="ecProdCmb" style="left: 14px; width: 159px;">
				<select name="products_id" id="products_id" onChange="javascript: exp_prod_status();"></select>
			</div>
		</div>
		
		
		<!-- CD Key : Qty, Price and OP -->
		<div id="ecCDKBox" style="display: none;">
			<div class="ecBrkLine" style="height: 13px;"><!-- --></div>
			
			<!-- Qty. -->
			<div id="ecQtyCmb" style="height: 28px;">
				<div style="float: left; left: 14px; position: absolute;">
					<div id="ecQty">
						<select name="buyqtycombo" id="buyqtycombo" onChange="javascript: exp_price_op();"></select>
					</div>
				</div>
				<div style="float: right; width: 80px; padding: 0px 10px 0px 0px;">
					<a id="ecQtyCmbLink"><span class="ecTxtSml"><?=TEXT_EXP_CHECKOUT_QTY_BULK;?></span></a>
				</div>
			</div>
			
			<div id="ecQtyText" style="height: 28px;">
				<div style="float: left; left: 14px; position: absolute;">
					<div class="dhx_combo_box">
						<input type="text" name="buyqtytext" id="buyqtytext" maxlength="4" style="width: 63px; position: relative;" value="<?=COMBO_SELECT_QTY;?>" onBlur="javascript:exp_price_op();" onFocus="this.value=''" onKeyPress="javascript: return expQtySubmit(event);" />
					</div>
					<a id="ecQtyTxtLink" href="javascript: hideQtyText();" style="float: right; left: 80px; position: absolute; top: 0; width: 80px;">
						<span class="ecTxtSml"><?=TEXT_EXP_CHECKOUT_QTY_NORMAL;?></span>
					</a>
				</div>
				<div class="ecBrkLine" style="height: 26px;"><!-- --></div>
			</div>
			
			<!-- Select delivery mode -->
			<div id="ecMode" style="display: none;">
				<div class="ecBrkLine" style="height: 40px;"><!-- --></div>
				<div class="ecTxtSml" style="left: 14px; position: relative;">
					<?=ENTRY_DELIVERY_METHOD?>
				</div>
				<div id="tr_top_up_error_msg"<!-- style="display:none;"-->
					<div style="width:200px;left:27px;position:relative">
						<div style="float:left;padding-top:6px;"><?=tep_image(DIR_WS_ICONS . 'warning_blue.gif')?></div>
						<div style="float:left;padding-top:5px;padding-left:5px;width:150px;" id="div_error_msg" class="redIndicator">ascacasc</div>
					</div>
				</div>
				<div style="left:14px;position:relative;clear:both;" id="ecModeListing" class="ecTxtSml"></div>
				<div class="ecBrkLine" style="height: 26px;"><!-- --></div>
			</div>
			
			<!-- Price and OP -->
			<div id="ecPriceOP" style="display: none; ">
				<div class="ecBrkLine"><!-- --></div>
				<div id="ecLoading" style="text-align: center; display: none;">
					<?=tep_image(DIR_WS_IMAGES . 'loading.gif', '', '20', '20');?>
				</div>
				
				<span id="price" class="ecTxtLrg" style="text-align: center"><!-- --></span>
				<span id="op" class="ecTxtSml" style="text-align: center"><!-- --></span>
			</div>
		</div>
		
		<div class="ecBrkLine"><!-- --></div>
		
		<!-- Express Checkout Green / Gray Button -->
		<div style="text-align: center; width: 100%;">
			<table border="0" cellpadding="0" cellspacing="0" align="center">
				<tr>
					<td>
						<div class="ecRedBtn" id="ecRedBtn" style="display: none;">
							<a id="ecRedCheckOut" href="javascript: expressCheckout();">
								<span id="ecRedBtnText" class="ecTxtMedPlain" style="text-align: center;"><?=BUTTON_EXP_CHECKOUT_CHECKOUT;?></span>
							</a>
						</div>
						
						<div id="ecGreenBtn" class="ecGreenBtn" style="display: none;">
							<a id="ecGreenCheckOut" href="javascript: expressCheckout();">
								<span id="ecGreenBtnText" class="ecTxtMedPlain" style="text-align: center;"><?=BUTTON_EXP_CHECKOUT_CHECKOUT;?></span>
							</a>
						</div>
						
						<div class="ecGrayBtn" id="ecGrayBtn">
							<span id="ecGrayBtnText" class="ecTxtMedPlain" style="text-align: center;"><?=BUTTON_EXP_CHECKOUT_CHECKOUT;?></span>
						</div>
					</td>
				</tr>
			</table>
		</div>
		
						
		<!-- CD Key : Delivery Message -->
		<div id="ecCDKMsgBox" style="display: none; text-align: center; width: 100%">
			<div class="ecBrkLine" style="height: 13px;"><!-- --></div>
			<table border="0" cellpadding="0" cellspacing="0" align="center" width="159px">
				<tr>
					<td>
						<span id="ecCDKMsg" class="ecTxtSml" style="text-align: center"><!-- --></span>
					</td>
				</tr>
			</table>
		</div>
		
		<div class="ecBrkLine"><!-- --></div>
	</div>
	
	
	<!-- Express Checkout Close -->
	<div class="ecClose" id="ecClose">
		<a href="javascript: hideExpBox();"><?=BUTTON_EXP_CHECKOUT_CLOSE;?></a><div class="ecCloseBtn" onClick="javascript: hideExpBox();"><!-- --></div>
	</div>
	
	<div class="ecBoxFooter" id="ecBoxFooter"><!-- --></div>
	<input type="hidden" id="bundle" />
</div>


<script language="javascript">
	window.dhx_globalImgPath = "<?=DIR_WS_JAVASCRIPT;?>dhtmlxCombo/imgs/"
</script>

<script src="<?=DIR_WS_JAVASCRIPT;?>dhtmlxCombo/dhtmlxcommon.js"></script>
<script src="<?=DIR_WS_JAVASCRIPT;?>dhtmlxCombo/dhtmlxcombo.js"></script>
<script src="<?=DIR_WS_JAVASCRIPT;?>dhtmlxCombo/ext/dhtmlxcombo_whp.js" type="text/javascript"></script>

<script language="javascript" src="<?=DIR_WS_JAVASCRIPT?>xmlhttp.js"></script>
<script language="javascript">
	<!--
	// Please append new entry at the back or define new array for new functionality
	var exp_checkout_lang = ['<?=COMBO_SELECT_GAME;?>','<?=COMBO_SELECT_PRODUCT_TYPE;?>', 
							'<?=COMBO_SELECT_QTY;?>', '<?=BUTTON_EXP_CHECKOUT_CHECKOUT;?>',
							'<?=COMBO_SELECT_PRODUCT;?>', '<?=BUTTON_EXP_CHECKOUT_BUY_NOW;?>',
							'<?=COMBO_SELECT_SERVERS;?>', '<?=COMBO_SELECT_CATEGORIES;?>',
							'<?=tep_href_link(FILENAME_CHECKOUT_PAYMENT);?>', '<?=BUTTON_EXP_CHECKOUT_PRE_ORDER;?>', 
							'<?=tep_href_link(basename($PHP_SELF), "action=buy_now");?>', '<?=BUTTON_EXP_CHECKOUT_OUT_OF_STOCK;?>', 
							'<?=TEXT_EXP_CHECKOUT_BULK_NOT_AVAILABLE;?>', '<?=tep_href_link("checkout_xmlhttp.php")?>'];
	var exp_maxQty = 20;
	
	var GA_CUSTOMERS_GROUPS_NAME = "<?=tep_not_null($_SESSION['GA_CUSTOMERS_GROUPS_NAME']) ? $_SESSION['GA_CUSTOMERS_GROUPS_NAME'] : '';?>";
	var TEXT_GENERAL = "<?=TEXT_GENERAL;?>";
	var TABLE_HEADING_DELIVERY_TIME = "<?=TABLE_HEADING_DELIVERY_TIME;?>";
	//-->
</script>
<script language="javascript" src="<?=DIR_WS_JAVASCRIPT?>express_checkout.js?20110324"></script>