<?php
/*
  $Id: games.php,v 1.15 2011/10/27 09:15:06 weesiong Exp $

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2003 osCommerce

  Released under the GNU General Public License
*/

?>
<!-- games_box //-->
<?
	$filter_field_input_value = '';
	
	if ($page_info->main_game_id) {
        $filter_field_input_value = tep_get_categories_name($page_info->main_game_id, $languages_id);
	}
    
	$boxHeading = HEADER_GAMES_SELECTION;
	$box_style = 'Heading_Box_L';
	
    $boxContent .= '<div class="boxContent">
    				<ul>
    					<li>
    						<center>
    							<input type=text name="filter_field_input" value="'.$filter_field_input_value.'" id="filter_field_input" class="filter_field_input blur" title="'.TEXT_TYPE_TO_FILTER_PRODUCTS.'" style="width:164px;" onKeyUp="javascript:filter_out_games()">
    						</center>
    						<div class="halfBreakLine"><!-- --></div>
    					</li>';

	$count_item = 0;
	$games_array = array();
	$boxContent_array = array();
	
	if (substr(basename($PHP_SELF), 0, 7) == 'buyback') { 
		if (count($wbb_game_list_arr) > 0) {
			foreach ($wbb_game_list_arr as $games_row) {
				if ($games_row['id'] != 0) {
					$print_class = 'boxLink';
					$count_item++;
					$temp_name_escaped = tep_escape_filtering_id($games_row['text']);
					
					if ($count_item == 1) {
						$print_class = 'boxLinkFirst';
					}
					
					$boxContent_array[strtolower($games_row['text']).$count_item] = '<li id="'.$temp_name_escaped.'_'.$count_item.'"><a href="'.tep_href_link(FILENAME_BUYBACK.'#buyback_server_listing').'" class="boxLink" onclick="callEvent_onBuybackGameSelection(\''.$games_row['id'].'\', \'0\');">'.$games_row['text'].'</a></li>';
					
					$temp_name = str_replace('&', '', $games_row['text']);
					$games_array[] = '"'.$temp_name.' '.$count_item.'"';
				}
			}
		}
	} else {
        // column left only allow buyback, so impossible to reach here.
	}
	
	$boxContent .= implode("", $boxContent_array);

	$games_javascript_array = implode(',', $games_array);
	
	$boxContent .= '	<li>
							<div style="display:none" id="box_show_all_link">
		    					<a href="javascript:filter_out_games(\'clear\')" class="boxLink">
		    						<img src="/images/icon-expand.gif" align="absmiddle" style="margin: 0px 0px 3px 0px">
		    						&nbsp;<b>'.HEADER_TITLE_SHOW_ALL.'</b>
		    					</a>
	    					</div>
	    				</li>
	    				</ul>
	    			</div>';
	    			
$boxContent .= '
<script type="text/javascript">
var filter_array = new Array();
filter_array = ['.$games_javascript_array.'];

function filter_out_games (action)
{
	if (action == "clear") {
		jQuery("#filter_field_input").val("");
	}

	if (jQuery("#filter_field_input").val() == "")
		jQuery("#box_show_all_link").hide();
	else
		jQuery("#box_show_all_link").show();

	var filter = jQuery("#filter_field_input").val();

	for(i=0;i<filter_array.length;i++) {
		var idname = filter_array[i];
		idname = idname.replace(/ /g,\'_\');
		idname = idname.replace(/\//g,\'\');
		idname = idname.replace(/\\\'/g,\'\');
		idname = idname.replace(/\\//g,\'\\\\\\/\');
		idname = idname.replace(/\'/g,\'\\\'\');
		idname = idname.replace(/\\(/g,\'\');
		idname = idname.replace(/\\)/g,\'\');
		idname = idname.replace(/\\!/g,\'\');
		idname = idname.replace(/\\./g,\'\');
		idname = idname.replace(/\\:/g,\'\');
		idname = idname.replace(/\\,/g,\'\');
		idname = idname.replace(/\\·/g,\'\');

		if (filter.toLowerCase() == filter_array[i].substring(filter.length,0).toLowerCase()){
			jQuery("#"+idname).show();
		} else {
			jQuery("#"+idname).hide();
		}
	}

	if (action == "clear") {
		jQuery(\'#filter_field_input\').hint();
	}

}';

	if ($filter_field_input_value)
		$boxContent .= 'filter_out_games();';
	
	$boxContent .= '</script>';

    require(DIR_WS_TEMPLATES . TEMPLATENAME_BOX);
    
    $boxContent_attributes = '';
	//games_eof
