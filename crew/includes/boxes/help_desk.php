<?
require(DIR_WS_FUNCTIONS . 'connection_function.php');

$selling_array = array();
$selling_array[] = FILENAME_BUYBACK;
$selling_array[]= FILENAME_ACCOUNT_BUYBACK;
$selling_array[]= FILENAME_MY_ORDER_HISTORY;

$filename = basename($_SERVER['SCRIPT_FILENAME']);
if (in_array($filename, $selling_array)) {
	$live_chat = LIVE_HELP_OGM_BUYBACK_IMPLEMENTATION;
} else {
	$live_chat = LIVE_HELP_IMAGE_IMPLEMENTATION;
}

if (trim($live_chat) != '') {
	$formatted_help_desk_html = tep_parse_live_chat_string($live_chat, LIVE_HELP_PRIVILEGES_CUSTOMERS_IMPLEMENTATION);
	
	echo "<!-- information //-->";
	$box_style = "Empty_Box_R";
	$boxContent = '';

	$boxContent .= $formatted_help_desk_html;

	$boxContent .= '
	<div class="break_line" style="clear: left"></div>
	<div id="livechat-on"><a href="aaaaaaa"><img src="images/OGM2008/icon/icon-fastnsafe.gif" width="195" height="59" border="0"></a></div>';

 	require(DIR_WS_TEMPLATES . TEMPLATENAME_BOX);
	echo "<!-- information_eof //-->";
}

?>