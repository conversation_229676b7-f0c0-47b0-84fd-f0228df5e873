<?php
/*
  $Id: index_main_box.php,v 1.22 2012/09/10 08:44:37 weichen Exp $
  
  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2003 osCommerce

  Released under the GNU General Public License
*/

// games_box
$box_style = 'Heading_Box_L';
$boxHeading = HEADER_PRODUCT_SELECTION;

$currency_str = $pwl_str = $cdkey_str = '';

$boxContent .= '<div class="boxContent">';
$boxContent .= '<ul>';

if (tep_not_null($page_info->custom_product_type_child_id_array)) {
	$PT_box_content = '';
	
	foreach ($page_info->custom_product_type_child_id_array as $arr_counter => $ctype) {
		if (tep_not_null($PT_box_content)) {
			$PT_box_content .= '<li><a href="' . tep_href_link('index.php', 'pagetype=q&ctype=' . $ctype) . '" class="boxLink">'. tep_get_store_keyword($ctype) .'</a></li>';
		} else {
			$PT_box_content .= '<li><a href="' . tep_href_link('index.php', 'pagetype=q&ctype=' . $ctype) . '" class="boxLinkFirst">'. tep_get_store_keyword($ctype) .'</a></li>';
		}
	}
	$boxContent .= $PT_box_content;
	unset($PT_box_content);
}

$boxContent .= '	<li><div class="breakLine"><!-- --></div></li>
				</ul>
				</div>';

require(DIR_WS_TEMPLATES . TEMPLATENAME_BOX);
//games_eof

$boxContent_attributes = '';