<?php
/*
  $Id: information.php,v 1.2 2004/09/23 05:36:09 stanley Exp $

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2003 osCommerce

  Released under the GNU General Public License
*/
?>
<!-- information //-->
<?php
  $boxHeading = BOX_HEADING_INFORMATION;
  $background_color = 'class="infoBoxLeft"';
  $corner_left = 'square';
  $corner_right = 'square';
  
  
  $boxContent = //'<a href="' . tep_href_link(FILENAME_SHIPPING) . '"> ' . BOX_INFORMATION_SHIPPING . '</a><br>' .
                
//<tr><td></td></tr><!--tr><td><img src="images/space_line1.gif" width="130" height="1"></td></tr-->
'<table align="center"><tr><td class="smalltext"> <a href="' . tep_href_link(FILENAME_INFO_CONTACT) . '"> ' . BOX_INFORMATION_CONTACT . '</a><br></tr></td></table>'.
 '<table align="center"><tr><td class="smalltext"><a href="' . tep_href_link(FILENAME_INFO_FAQ) . '"> ' . BOX_INFORMATION_FAQ . '</a><br></tr></td></table>' .
 '<table align="center"><tr><td class="smalltext"><a href="' . tep_href_link(FILENAME_PRIVACY) . '"> ' . BOX_INFORMATION_PRIVACY . '</a><br></tr></td></table>' .
  '<table align="center"><tr><td class="smalltext"><a href="' . tep_href_link(FILENAME_INFO_TOS) . '"> ' . BOX_INFORMATION_TOS . '</a><br></tr></td></table>' . //line 27
     
  				
                //'<a href="' . tep_href_link(FILENAME_CONDITIONS) . '"> ' . BOX_INFORMATION_CONDITIONS . '</a><br>' .
                //'<a href="' . tep_href_link(FILENAME_CONTACT_US) . '"> ' . BOX_INFORMATION_CONTACT_WEB . '</a><br>'.
              
   '<table align="center"><tr><td class="smalltext"><a href="' . tep_href_link(FILENAME_INFO_DISCLAIMER) . '"> ' . BOX_INFORMATION_DISCLAIMER . '</a><br></tr></td></table>';
         
 '<table><tr><td class="smalltext"><a href="' . tep_href_link(FILENAME_GV_FAQ, '', 'NONSSL') . '"> ' . BOX_INFORMATION_GV . '</a></tr></td></table>';//ICW ORDER TOTAL CREDIT CLASS/GV
             
  //'<table><tr><td class="smalltext"><a href="' . tep_href_link(FILENAME_CATALOG_PRODUCTS_WITH_IMAGES, '', 'NONSSL') . '">' . BOX_CATALOG_PRODUCTS_WITH_IMAGES . '</a><br>' .
  //              '</table>'.
  
  require(DIR_WS_TEMPLATES . TEMPLATENAME_BOX);
?>
<!-- information_eof //-->
                

