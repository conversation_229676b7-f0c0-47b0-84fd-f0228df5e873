<?php
/*
  	$Id: languages.php,v 1.4 2006/12/07 09:51:38 weichen Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2003 osCommerce
	
  	Released under the GNU General Public License
*/
?>
<!-- languages //-->
<?
$boxHeading = BOX_HEADING_LANGUAGES;
$background_color = 'class="systemBox"';
$heading_title_style = 'class="systemBoxHeading"';
$boxText = "systemBoxText";
$corner_left = 'square';
$corner_right = 'square';
$boxContent_attributes = ' align="center"';

if (!isset($lng) || (isset($lng) && !is_object($lng))) {
	include(DIR_WS_CLASSES . 'language.php');
    $lng = new language;
}

$hidden_get_variables = '';
reset($HTTP_GET_VARS);
while (list($key, $value) = each($HTTP_GET_VARS)) {
  	if ( ($key != 'language') && ($key != tep_session_name()) && ($key != 'x') && ($key != 'y') ) {
    	$hidden_get_variables .= tep_draw_hidden_field($key, $value);
  	}
}

$boxContent = '';
reset($lng->catalog_languages);
if (count($lng->catalog_languages) > 1) {
	$languages_selection_array = array();
	$selected_language = '';
	while (list($key, $value) = each($lng->catalog_languages)) {
		$languages_selection_array[] = array( 'id' => $key, 'text' => $value['name']);
		if (!tep_not_null($selected_language) && $value['directory'] == $language)
			$selected_language = $key;
		//$boxContent .= ' <a href="' . tep_href_link(basename($PHP_SELF), tep_get_all_get_params(array('language', 'currency')) . 'language=' . $key, $request_type) . '">' . tep_image(DIR_WS_LANGUAGES .  $value['directory'] . '/images/' . $value['image'], $value['name']) . '</a> ';
	}
	
	$boxContent = '	<table border="0" width="100%" cellspacing="0" cellpadding="0">';
	$boxContent .= tep_draw_form('language_form', tep_href_link(basename($PHP_SELF), '', $request_type, false), 'get');
	$boxContent .= $hidden_get_variables . tep_hide_session_id();
	$boxContent .= '	<tr>
	            			<td height="4"></td>
	            		</tr>
						<tr>
							<td align="left">';
	$boxContent .= tep_draw_pull_down_menu('language', $languages_selection_array, $selected_language, 'onChange="this.form.submit();"');
	$boxContent .= '		</td>
						</tr>
					</table>
					</form>';
	require(DIR_WS_TEMPLATES . TEMPLATENAME_BOX);
}
?>
<!-- languages_eof //-->
<?
$boxContent_attributes = '';
?>