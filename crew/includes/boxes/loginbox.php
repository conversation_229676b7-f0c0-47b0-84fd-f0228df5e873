<?
// WebMakers.com Added: Do not show if on login or create account
//if ( (!strstr($_SERVER['PHP_SELF'],'login.php')) and (!strstr($_SERVER['PHP_SELF'],'create_account.php')) and !tep_session_is_registered('customer_id') )  {
if ( (!strstr($_SERVER['PHP_SELF'],'login.php')) and !tep_session_is_registered('customer_id') )  {
?>
<!-- loginbox //-->
<?
    if (!tep_session_is_registered('customer_id')) {
  		$boxHeading = HEADER_TITLE_LOGIN;
  		$background_color = 'class="systemBox"';
  		$heading_title_style = 'class="systemBoxHeading"';
  		$boxText = "systemBoxText";
  		$corner_left = 'square';
  		$corner_right = 'square';
		
    	$boxContent = '<table border="0" width="100%" cellspacing="0" cellpadding="0" style="background-image: url(images/side-box-mid.gif); background-repeat: repeat-y;">
            			<tr>
            				<td align="center">
            					<table border="0" width="90%" cellspacing="0" cellpadding="0">
            						<tr>
            							<td>
            								<form name="login" method="post" action="' . tep_href_link(FILENAME_LOGIN, 'action=process', 'SSL') . '">
					            				<tr>
					            					<td height="4" colspan="2"></td>
					            				</tr>
					              				<tr>
					                				<td align="left" class="systemBoxLabel">' . ENTRY_EMAIL_ADDRESS . '</td>
					                  				<td align="left" class="systemBoxField"><input type="text" name="email_address" maxlength="96" size="22" value=""></td>
					              				</tr>
					              				<tr>
					    		  					<td height="4" colspan="2"></td>
					    		  				</tr>
					              				<tr>
					                				<td align="left" class="systemBoxLabel">' . ENTRY_PASSWORD . '</td>
					                				<td align="left" class="systemBoxField"><input type="password" name="password" maxlength="40" size="22" value=""></td>
					              				</tr>
					    		  				<tr>
					    		  					<td height="4" colspan="2"></td>
					    		  				</tr>
					              				<tr>
					              					<td></td>
					                				<td align="left">'.tep_submit_button(IMAGE_BUTTON_LOGIN, IMAGE_BUTTON_LOGIN, '', 'gray_submit_button', false).'</td>
					              				</tr>
					              				<tr>
					    		  					<td height="4" colspan="2"></td>
					    		  				</tr>
					              				<tr>
					                				<td align="left" colspan="2">
					                					<a href="' . tep_href_link(FILENAME_LOGIN, '','SSL') . '" class=systemNav>Sign Up</a>
					                				</td>
					                			</tr>
					                			<tr>
					                				<td align="left" height="5" colspan="2" width="100%" style="background: url(\''.DIR_WS_IMAGES.'row_seperator.gif'.'\'); background-repeat: repeat-x; background-position: left center;"></td>
					                			</tr>					              				<tr>
					                				<td align="left" colspan="2">
					                					<a href="' . tep_href_link(FILENAME_ACCOUNT_ACTIVATE, '','SSL') . '" class=systemNav>'.LOGIN_BOX_ACCOUNT_ACTIVATION.'</a>
					                				</td>
					                			</tr>

					                			<tr>
					                				<td align="left" height="5" colspan="2" width="100%" style="background: url(\''.DIR_WS_IMAGES.'row_seperator.gif'.'\'); background-repeat: repeat-x; background-position: left center;"></td>
					                			</tr>
					                			<tr>
					                				<td align="left" colspan="2">
					                					<a href="' . tep_href_link(FILENAME_PASSWORD_FORGOTTEN, '', 'SSL') . '" class=systemNav>'.LOGIN_BOX_PASSWORD_FORGOTTEN.'</a>
					                				</td>
					              				</tr>
		            						</form>
		            					</td>
		            				</tr>
		            			</table>
		            		</td>
	            		</tr>
            		</table>';
		require(DIR_WS_TEMPLATES . TEMPLATENAME_BOX);
	} else {
  		// If you want to display anything when the user IS logged in, put it
  		// in here...  Possibly a "You are logged in as :" text or something.
	}
?>
<!-- loginbox_eof //-->
<?
// WebMakers.com Added: My Account Info Box
} else {
  	if (tep_session_is_registered('customer_id')) {
  		$customer_sc_array = store_credit::get_current_credits_balance($customer_id);
?>
<!-- my_account_info //-->
<?
		$boxHeading = BOX_HEADING_LOGIN_BOX_MY_ACCOUNT;
	  	$background_color = 'class="systemBox"';
	  	$heading_title_style = 'class="systemBoxHeading"';
	  	$boxText = "systemBoxText";
	  	$corner_left = 'square';
	  	$corner_right = 'square';
	  	
		$email_verified_sql = "	SELECT civ.serial_number, civ.info_verified 
								FROM " . TABLE_CUSTOMERS . " AS c 
								LEFT JOIN " . TABLE_CUSTOMERS_INFO_VERIFICATION . " AS civ 
									ON (c.customers_id = civ.customers_id AND c.customers_email_address = civ.customers_info_value) 
								WHERE c.customers_id ='" . (int)$customer_id . "'";
		$email_verified_result_sql = tep_db_query($email_verified_sql);
		$email_verified_row = tep_db_fetch_array($email_verified_result_sql);
		$user_credit_balance = tep_user_balance($_SESSION['customer_id'], 'customers');
		
		
		$boxContent = '	<table border="0" width="100%" cellspacing="0" cellpadding="0" style="background-image: url(images/side-box-mid.gif); background-repeat: repeat-y;">
							<tr>
								<td align="center">
									<table border="0" cellspacing="0" cellpadding="1" width="90%">';
		
		if (count($user_credit_balance)) {
			$i = 0;
			foreach ($user_credit_balance as $bal_currency => $bal_amt) {
				$boxContent .= '
		<tr>
											<td class="smalltext">' . 
												TEXT_WITHDRAWABLE_CREDIT . '&nbsp;&nbsp;' . $currencies->currencies[$bal_currency]['symbol_left'].number_format($bal_amt, 2, '.', ',').$currencies->currencies[$bal_currency]['symbol_right'] . '<br><a href="'.tep_href_link(FILENAME_MY_PAYMENT_HISTORY, tep_get_all_get_params(array('action', 'cur')) . 'action=show_request&cur='.$bal_currency).'">' . TEXT_WITHDRAW . '</a> |
												<a href="' . tep_href_link(FILENAME_MY_PAYMENT_HISTORY, 'action=show_report&pyh_input_start_date='.(date('Y-m-d', mktime(0, 0, 0, date("m")  , date("d")-7, date("Y")))), 'SSL').'">' . TEXT_STATEMENT . '</a>
											</td>
										</tr>
										<tr>
			                				<td align="left" height="5" width="100%" style="background: url(\''.DIR_WS_IMAGES.'row_seperator.gif'.'\'); background-repeat: repeat-x; background-position: left center;"></td>
			                			</tr>';
				$i++;
			}
		}
		
		$boxContent .='					<tr>
											<td class="smalltext">' . VOUCHER_BALANCE . '&nbsp;&nbsp;' . $currencies->format( ($customer_sc_array['sc_reverse']+$customer_sc_array['sc_irreverse']) - ($customer_sc_array['sc_reverse_reserve_amt']+$customer_sc_array['sc_irreverse_reserve_amt'])) . '</td>
										</tr>
										<tr>
			                				<td align="left" height="5" width="100%" style="background: url(\''.DIR_WS_IMAGES.'row_seperator.gif'.'\'); background-repeat: repeat-x; background-position: left center;"></td>
			                			</tr>
										<tr align="left">
											<td class="smalltext">
				           		   				<a href="' . tep_href_link(FILENAME_ACCOUNT, '', 'SSL') . '" class=systemNav>' . LOGIN_BOX_MY_ACCOUNT . '</a>
				           		   			</td>
				           		   		</tr>
				           		   		<tr>
			                				<td align="left" height="5" width="100%" style="background: url(\''.DIR_WS_IMAGES.'row_seperator.gif'.'\'); background-repeat: repeat-x; background-position: left center;"></td>
			                			</tr>
				            	  		<tr align="left">
				            	  			<td class="smalltext">
				            	  				<a href="' . tep_href_link(FILENAME_ACCOUNT_EDIT, '', 'SSL') . '" class=systemNav>' . LOGIN_BOX_ACCOUNT_EDIT . '</a>
				            	  			</td>
				            	  		</tr>
				            	  		<tr>
			                				<td align="left" height="5" width="100%" style="background: url(\''.DIR_WS_IMAGES.'row_seperator.gif'.'\'); background-repeat: repeat-x; background-position: left center;"></td>
			                			</tr>
				             	 		<tr align="left">
				             	 			<td class="smalltext">
				             	 				<a href="' . tep_href_link(FILENAME_ADDRESS_BOOK, '', 'SSL') . '" class=systemNav>' . LOGIN_BOX_ADDRESS_BOOK . '</a>
				             	 			</td>
				             	 		</tr>
				             	 		<tr>
			                				<td align="left" height="5" width="100%" style="background: url(\''.DIR_WS_IMAGES.'row_seperator.gif'.'\'); background-repeat: repeat-x; background-position: left center;"></td>
			                			</tr>
				             	 		<tr align="left">
				               				<td class="smalltext">
				               					<a href="' . tep_href_link(FILENAME_ACCOUNT_PAYMENT_EDIT, '', 'SSL') . '" class=systemNav>' . LOGIN_BOX_ACCOUNT_PAYMENT_EDIT . '</a>
				               				</td>
				               			</tr>
				             	 		<tr>
			                				<td align="left" height="5" width="100%" style="background: url(\''.DIR_WS_IMAGES.'row_seperator.gif'.'\'); background-repeat: repeat-x; background-position: left center;"></td>
			                			</tr>';
		
		if ($email_verified_row['info_verified'] == 0) {
			$boxContent .= '			<tr align="left"><td class="smalltext"><a href="' . tep_href_link(FILENAME_ACCOUNT_ACTIVATE, 'aov=verify'.(tep_not_null($email_verified_row['serial_number']) ? '' : '&auto_mail=yes'), 'SSL') . '" class=systemNav>' . LOGIN_BOX_VERIFY_EMAIL . '</a><td></tr>
										<tr>
                							<td align="left" height="5" width="100%" style="background: url(\''.DIR_WS_IMAGES.'row_seperator.gif'.'\'); background-repeat: repeat-x; background-position: left center;"></td>
                						</tr>';
		}
		
	   	$boxContent .= 	'				<tr align="left">
				              				<td class="smalltext">
				              					<a href="' . tep_href_link(FILENAME_ACCOUNT_HISTORY, '', 'SSL') . '" class=systemNav>' . LOGIN_BOX_ACCOUNT_HISTORY . '</a>
				              				</td>
				              			</tr>
				              			<tr>
			                				<td align="left" height="5" width="100%" style="background: url(\''.DIR_WS_IMAGES.'row_seperator.gif'.'\'); background-repeat: repeat-x; background-position: left center;"></td>
			                			</tr>
				               			<tr align="left">
				               				<td class="smalltext">
				               					<a href="' . tep_href_link(FILENAME_MY_ORDER_HISTORY, 'history_type=buyback', 'SSL') . '" class=systemNav>' . LOGIN_BOX_BUYBACK_ORDER_HISTORY . '</a>
				               				</td>
				               			</tr>
				               			<tr>
			                				<td align="left" height="5" width="100%" style="background: url(\''.DIR_WS_IMAGES.'row_seperator.gif'.'\'); background-repeat: repeat-x; background-position: left center;"></td>
			                			</tr>
				               			<tr align="left">
				               				<td class="smalltext">
				               					<a href="' . tep_href_link(FILENAME_MY_FAVOURITE_LINKS, '', 'SSL') . '" class=systemNav>' . LOGIN_BOX_BUYBACK_FAVOURITE . '</a>
				               				</td>
				               			</tr>
				               			<tr>
			                				<td align="left" height="5" width="100%" style="background: url(\''.DIR_WS_IMAGES.'row_seperator.gif'.'\'); background-repeat: repeat-x; background-position: left center;"></td>
			                			</tr>
				               			<tr align="left">
				               				<td class="smalltext">
				               					<a href="' . tep_href_link(FILENAME_LOGOFF, '', 'NONSSL') . '" class=systemNav>' . HEADER_TITLE_LOGOFF . '</a>
				               				</td>
				               			</tr>
				               		</table>
								</td>
							<tr>
							</table>';
	   	
		require(DIR_WS_TEMPLATES . TEMPLATENAME_BOX);
?>
<!-- my_account_info_eof //-->
<?
  	}
}
?>