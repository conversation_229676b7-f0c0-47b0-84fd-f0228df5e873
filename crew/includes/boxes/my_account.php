<?php
/*
  $Id: my_account.php,v 1.36 2013/12/04 06:44:43 chingyen Exp $

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2003 osCommerce

  Released under the GNU General Public License
*/
?>

<!-- Login_box //-->
<?php

// Mantis # 0000024 @ ********1129 - Add new boxes
if (tep_session_is_registered('customer_id')) {
	$box_style = 'No_Break_Line_L';

	// Box Heading
	$boxHeadingAccount = BOX_HEADING_ACCOUNT_LEFT_BOX_MY_ACCOUNT;
	$boxHeadingProfile = BOX_HEADING_ACCOUNT_LEFT_BOX_MY_PROFILE;
	$boxHeadingStoreCredits = BOX_HEADING_ACCOUNT_LEFT_BOX_STORE_CREDITS;
	$boxHeadingBuyer	= BOX_HEADING_ACCOUNT_LEFT_BOX_BUYER;
	$boxHeadingSeller	= BOX_HEADING_ACCOUNT_LEFT_BOX_SELLER;
	$boxHeadingVipSeller= BOX_HEADING_ACCOUNT_LEFT_BOX_VIP_SELLER;

	// My Account
	// ----------------------------------------------------  My Profile  ----------------------------------------------------
	$contents_slist = array();
	$contents_slist[] = array('desc' => MY_ACCOUNT_EDIT_PROFILE_LINK, 'href' => tep_href_link(FILENAME_ACCOUNT_EDIT, '', 'SSL'));
	$contents_slist[] = array('desc' => MY_ACCOUNT_MANAGE_NEWSLETTERS_LINK, 'href' => tep_href_link(FILENAME_ACCOUNT_NEWSLETTERS, '', 'SSL'));
	$contents_slist[] = array('desc' => MY_ACCOUNT_OFFGAMERS_POINTS, 'href' => tep_href_link(FILENAME_REDEEM_POINT, '', 'SSL'));

	// verify email address
	if (tep_info_verified_check($customer_id, tep_get_customers_email($customer_id), 'email') != 1) {
		$contents_slist[] = array('desc' => MY_ACCOUNT_VERIFY_EMAIL_LINK, 'href' => tep_href_link(FILENAME_ACCOUNT_ACTIVATE, 'verify=email&auto_mail=yes', 'SSL'));
	}

	// verify phone number
	$customer_complete_phone_info_array = tep_format_telephone($customer_id);
	$complete_telephone_number = sizeof($customer_complete_phone_info_array) > 0 ? $customer_complete_phone_info_array['country_international_dialing_code'] . $customer_complete_phone_info_array['telephone_number'] : '';
	if (tep_info_verified_check($customer_id, $complete_telephone_number, 'telephone') != 1) {
		$contents_slist[] = array('desc' => MY_ACCOUNT_VERIFY_PHONE_NUMBER_LINK, 'href' => tep_href_link(FILENAME_ACCOUNT_ACTIVATE, 'verify=phone', 'SSL'));
	}

	if (tep_customer_info_verification($customer_id, 'files_001_locked') *
		tep_customer_info_verification($customer_id, 'files_002_locked') *
		tep_customer_info_verification($customer_id, 'files_003_locked') *
		tep_customer_info_verification($customer_id, 'files_004_locked') *
		tep_customer_info_verification($customer_id, 'files_005_locked') == 0) {

		$contents_slist[] = array('desc' => MY_ACCOUNT_VERIFICATION_SUBMISSION_FORM, 'href' => tep_href_link(FILENAME_VERIFICATION_SUBMISSION_FORM, '', 'SSL'));
	}

	if ($ogm_fb_obj->FB_connect_switch) {
		$contents_slist[] = array('desc' => MY_ACCOUNT_FACEBOOK_CONNECT, 'href' => tep_href_link(FILENAME_FACEBOOK_CONNECT, '', 'SSL'));
	}

	$contents_array[] = array(	'desc' => $boxHeadingProfile, 'href' => 'javascript:void(0);', 'slist' => $contents_slist, 'default_open' => true);
	unset($contents_slist);
	// ----------------------------------------------------  My Profile  ----------------------------------------------------


	// ----------------------------------------------------  Store Credits  ----------------------------------------------------
	$contents_slist = array();
	$contents_slist[] = array('desc' => MY_ACCOUNT_STORE_CREDITS_TOPUP_LINK, 'href' => tep_href_link(FILENAME_ACCOUNT_STORE_CREDIT_TOPUP, '', 'SSL'));
	$contents_slist[] = array('desc' => MY_ACCOUNT_STORE_CREDITS_HISTORY_LINK, 'href' => tep_href_link(FILENAME_ACCOUNT_STORE_CREDIT_HISTORY, '', 'SSL'));

	$boxContentStoreCredits .= '<div id="box_sc_long" class="gr_content" style="display: block;">';
	$boxContentStoreCredits .= '<ul>';
	$boxContentStoreCredits .= '	<li><a href="' . tep_href_link(FILENAME_ACCOUNT_STORE_CREDIT_TOPUP, '', 'SSL') . '" class="boxLinkFirst">'.MY_ACCOUNT_STORE_CREDITS_TOPUP_LINK . '</a></li>';
	$boxContentStoreCredits .= '	<li><a href="' . tep_href_link(FILENAME_ACCOUNT_STORE_CREDIT_HISTORY, '', 'SSL') . '" class="boxLink">'.MY_ACCOUNT_STORE_CREDITS_HISTORY_LINK.'</a></li>';
	if ($user_has_sc_acct) {
		$contents_slist[] = array('desc' => MY_ACCOUNT_STORE_CREDITS_CONVERSION_LINK, 'href' => $printStoreCreditLink);
	}

	$contents_array[] = array(	'desc' => $boxHeadingStoreCredits, 'href' => 'javascript:void(0);', 'slist' => $contents_slist, 'default_open' => true);
	unset($contents_slist);
	// ----------------------------------------------------  Store Credits  ----------------------------------------------------


	// ----------------------------------------------------  Buyer  ----------------------------------------------------
	$contents_slist = array();
	$contents_slist[] = array('desc' => MY_ACCOUNT_CURRENT_ORDERS, 'href' => tep_href_link(FILENAME_ACCOUNT_HISTORY, 'orders_type=current', 'SSL'));
	$contents_slist[] = array('desc' => MY_ACCOUNT_COMPLETED_ORDERS, 'href' => tep_href_link(FILENAME_ACCOUNT_HISTORY, 'orders_type=completed', 'SSL'));
	$contents_slist[] = array('desc' => MY_ACCOUNT_SPENDING_LIMIT_LINK, 'href' => 'javascript:void(0);');

	$contents_array[] = array(	'desc' => $boxHeadingBuyer, 'href' => 'javascript:void(0);', 'slist' => $contents_slist, 'default_open' => true);
	unset($contents_slist);
	// ----------------------------------------------------  Buyer  ----------------------------------------------------


	// ----------------------------------------------------  Seller  ----------------------------------------------------
	$user_credit_balance = tep_user_balance($_SESSION['customer_id'], 'customers');
	$contents_slist = array();
	$contents_slist[] = array('desc' => MY_ACCOUNT_SELL_ORDER_HISTORY_LINK, 'href' => tep_href_link(FILENAME_MY_ORDER_HISTORY, 'history_type=buyback&startdate='.date('Y-m-d').'', 'SSL'));
	$contents_slist[] = array('desc' => MY_ACCOUNT_WITHDRAW_MONEY_LINK, 'href' => tep_href_link(FILENAME_MY_PAYMENT_HISTORY, tep_get_all_get_params(array('action', 'cur')) . 'action=show_balance'));
	$contents_slist[] = array('desc' => LOGIN_BOX_ACCOUNT_PAYMENT_EDIT, 'href' => tep_href_link(FILENAME_ACCOUNT_PAYMENT_EDIT, '', 'SSL'));
//	$contents_slist[] = array('desc' => MY_ACCOUNT_FAVORITE_SERVERS_LINK, 'href' => tep_href_link(FILENAME_MY_FAVOURITE_LINKS, '', 'SSL'));

	$contents_array[] = array(	'desc' => $boxHeadingSeller, 'href' => 'javascript:void(0);', 'slist' => $contents_slist, 'default_open' => true);
	unset($contents_slist);
	// ----------------------------------------------------  Seller  ----------------------------------------------------


	// ----------------------------------------------------  VIP Seller  ----------------------------------------------------
	if (tep_session_is_registered('vip_supplier_groups_id') && $_SESSION['vip_supplier_groups_id'] > 1) {
		$contents_slist = array();
		$contents_slist[] = array('desc' => MY_ACCOUNT_VIP_REGISTER_SERVER, 'href' => tep_href_link(FILENAME_MY_VIP_REGISTER_SERVER, '', 'SSL'));
		$contents_slist[] = array('desc' => MY_ACCOUNT_VIP_INVENTORY_UPDATE, 'href' => tep_href_link(FILENAME_MY_VIP_INVENTORY_UPDATE, '', 'SSL'));
		$contents_slist[] = array('desc' => MY_ACCOUNT_VIP_ORDERS_HISTORY, 'href' => tep_href_link(FILENAME_MY_VIP_ORDERS_HISTORY, 'startdate='.date('Y-m-d').'', 'SSL'));
		$contents_slist[] = array('desc' => MY_ACCOUNT_VIP_REPORT, 'href' => tep_href_link(FILENAME_MY_VIP_REPORT, '', 'SSL'));

		$contents_array[] = array(	'desc' => $boxHeadingVipSeller, 'href' => 'javascript:void(0);', 'slist' => $contents_slist, 'default_open' => true);
		unset($contents_slist);
	}
	// ----------------------------------------------------  VIP Seller  ----------------------------------------------------


	// Combine Heading with Contents
	$boxContent .= $page_obj->get_html_expandable_vmenu_box($boxHeadingAccount, $contents_array, 0, '', 11);
	// @ ********

    require(DIR_WS_TEMPLATES . TEMPLATENAME_BOX);
}

?>

<!-- login_box_eof //-->
<script language="javascript" src="<?=DIR_WS_JAVASCRIPT?>hoverIntent.js"></script>
<script language="javascript" src="<?=DIR_WS_JAVASCRIPT?>superfish.js"></script>
<link rel="stylesheet" type="text/css" href="<?=DIR_WS_IMAGES?>css/superfish.css" media="screen">
<link rel="stylesheet" type="text/css" href="<?=DIR_WS_IMAGES?>css/superfish-vertical.css" media="screen">

<script language="javascript">
// initialise Superfish
jQuery("ul.sf-menu").superfish({
    delay:     100              	// 1.2 second delay on mouseout
});
</script>