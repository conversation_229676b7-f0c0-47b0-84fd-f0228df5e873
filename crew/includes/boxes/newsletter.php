<?php
/*
  $Id: newsletter.php,v 1.19 2010/11/15 08:06:56 sionghuat.chng Exp $

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2003 osCommerce

  Released under the GNU General Public License
*/

?>
<!-- Newsletter_box //-->
<?

$box_style = "No_Break_Line_L";

$boxContent = '	<div class="bodyContentLeft">
					<a href="'.tep_href_link(FILENAME_ACCOUNT_STORE_CREDIT_TOPUP).'">'.tep_image(DIR_WS_IMAGES . "topup_sc_".$_SESSION['languages_id'].".gif").'</a>
				</div>
				<div class="breakLine"></div>';

if ($ogm_fb_obj->FB_connect_switch) {
	$boxContent .= '<div class="bodyContentLeft">' . $ogm_fb_obj->get_FB_like_box() . '</div><div class="breakLine"><!-- --></div>';
}
require(DIR_WS_TEMPLATES . TEMPLATENAME_BOX);
?>


<!-- Newsletter_box_eof //-->