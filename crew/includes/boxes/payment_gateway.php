<?
	$box_style = "Empty_Box_L";
	$boxHeading = HEADER_RECOMMENDED_PAYMENT_GATEWAY;
	
	$box_pg_image_name = 'pgicon_' . (isset($language_code) && tep_not_null($language_code) ? $language_code : 'en' ) . '.gif';
	$boxContent .= '<div class="boxContent" style="overflow:hidden;">
					<ul>
						<li><b>'. $boxHeading .'</b></li>
					</ul>
					<ul>
  						<li><img src="'. DIR_WS_IMAGES . $box_pg_image_name . '" height="206" width="187"></li>
					</ul>
					<ul>
						<li>
  							<div style="display:inline-block; float:left;">
  								<div class="triangleRightIconBlue" style="display:inline; margin:2px 6px 0 0"><!-- --></div><div style="display:inline"><b><a href="http://kb.offgamers.com/en/category/payment-option/">' . TEXT_VIEW_PAYMENT_GATEWAY . '</a></b></div>
  							</div>
  						</li>
  						<li><div class="halfBreakLine">&nbsp;</div></li>
  					</ul>
					</div>
					<div class="dottedLine"><!-- --></div>';				
	
	require(DIR_WS_TEMPLATES . TEMPLATENAME_BOX);
?>