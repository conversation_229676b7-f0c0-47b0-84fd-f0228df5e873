<!-- polling //-->
<?
require_once(DIR_WS_CLASSES . 'polling.php');
if (basename($PHP_SELF) == FILENAME_DEFAULT) {
	$boxHeading = BOX_HEADING_POLLING;
	$box_style = 'Heading_Box_L';

	if (tep_session_is_registered('customer_id')) {
 	 	$poll_object = new polling($customer_id, $tpl);
	} else {
 	 	$poll_object = new polling(0, $tpl);
	}
  	if ($poll_object->get_active_poll_count() > 0) {
  		if (file_exists(DIR_WS_LANGUAGES . $_SESSION['language'] . '/'. FILENAME_POLLING)) {
			include_once(DIR_WS_LANGUAGES . $_SESSION['language'] . '/' . FILENAME_POLLING);
		}
  		$boxContent = $poll_object->show_polls();
  		
	  	require(DIR_WS_TEMPLATES . TEMPLATENAME_BOX);
	}
}
?>
<!-- polling_eof //-->