<?
/*
  $Id: product_notifications.php,v 1.5 2007/07/27 13:52:00 chan Exp $

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2003 osCommerce

  Released under the GNU General Public License
*/

if (isset($HTTP_GET_VARS['products_id']))
{
?>
<!-- notifications //-->
<?
	$boxHeading = BOX_HEADING_NOTIFICATIONS;
    $background_color = 'class="systemBox"';
    $heading_title_style = 'class="systemBoxHeading"';
    $boxText = "systemBoxText";
    $corner_left = 'square';
    $corner_right = 'square';
    $boxLink = '<a href="' . tep_href_link(FILENAME_ACCOUNT_NOTIFICATIONS, '', 'SSL') . '" class="systemNav"><img src="images/infobox/arrow_right.gif" border="0" alt="more" title=" more " width="12" height="10"></a>';
	
    if (tep_session_is_registered('customer_id')) {
    	$check_query = tep_db_query("select count(*) as count from " . TABLE_PRODUCTS_NOTIFICATIONS . " where products_id = '" . (int)$HTTP_GET_VARS['products_id'] . "' and customers_id = '" . (int)$customer_id . "'");
      	$check = tep_db_fetch_array($check_query);
		$notification_exists = (($check['count'] > 0) ? true : false);
    } else {
      	$notification_exists = false;
    }
	
    if ($notification_exists == true) {
		$boxContent = '<table border="0" cellspacing="0" cellpadding="0"><tr><td align="center"><table border="0" cellspacing="0" cellpadding="2" width="90%"><tr><td class="infoBoxContents"><a href="' . tep_href_link(basename($PHP_SELF), tep_get_all_get_params(array('action')) . 'action=notify_remove', $request_type) . '" class=systemNav>' . tep_image(DIR_WS_IMAGES . 'box_products_notifications_remove.gif', IMAGE_BUTTON_REMOVE_NOTIFICATIONS) . '</a></td><td class="infoBoxContents"><a href="' . tep_href_link(basename($PHP_SELF), tep_get_all_get_params(array('action')) . 'action=notify_remove', $request_type) . '">' . sprintf(BOX_NOTIFICATIONS_NOTIFY_REMOVE, tep_get_products_name($HTTP_GET_VARS['products_id'])) .'</a></td></tr></table></td></tr></table>';
    } else {
      	$boxContent = '<table border="0" cellspacing="0" cellpadding="0"><tr><td align="center"><table border="0" cellspacing="0" cellpadding="2" width="90%"><tr><td class="infoBoxContents"><a href="' . tep_href_link(basename($PHP_SELF), tep_get_all_get_params(array('action')) . 'action=notify', $request_type) . '"  class=systemNav>' . tep_image(DIR_WS_IMAGES . 'box_products_notifications.gif', IMAGE_BUTTON_NOTIFICATIONS) . '</a></td><td class="infoBoxContents"><a href="' . tep_href_link(basename($PHP_SELF), tep_get_all_get_params(array('action')) . 'action=notify', $request_type) . '"  class=systemNav>' . sprintf(BOX_NOTIFICATIONS_NOTIFY, tep_get_products_name($HTTP_GET_VARS['products_id'])) .'</a></td></tr></table></td></tr></table>';
    }
	
    require(DIR_WS_TEMPLATES . TEMPLATENAME_BOX);
?>
<!-- notifications_eof //-->
<?
    $boxLink = '';
}
?>