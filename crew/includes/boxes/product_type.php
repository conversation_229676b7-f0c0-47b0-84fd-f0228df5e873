<?php
/*
  $Id: product_type.php,v 1.39 2013/11/26 09:41:49 weesiong Exp $
  
  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2003 osCommerce

  Released under the GNU General Public License
*/

?>
<!-- product type box //-->
<?
	require_once(DIR_WS_CLASSES . 'latest_news.php');
	
	$box_style = 'Heading_Simple_Box_L';
	$boxHeading = $page_info->get_categories_info($page_info->main_game_id, 'categories_name', $languages_id, $default_languages_id );
	$PT_box_content_array = array();
	$product_type_info_array = array();
    
    if ($current_game_category_id && $games_categories_path) {
        $category_obj = new category($current_game_category_id);
        $available_product_child_types_array = $category_obj->get_available_product_child_type();
        $product_type_child_category_array = $category_obj->get_product_child_type_category($games_categories_path, $available_product_child_types_array);
        unset($available_product_child_types_array, $category_obj);
        
        if (count($product_type_child_category_array)) {
            //select needed categories_product_types
            foreach ($product_type_child_category_array as $category_product_types) {
                $product_type_name = tep_get_categories_name($category_product_types['categories_id'], $languages_id);
                $categories_parent_path = substr($category_product_types['categories_parent_path'], 1).$category_product_types['categories_id'];
                $key = array_search($category_product_types['custom_products_type_child_id'], $page_info->custom_product_type_child_id_array);
                
                $product_type_info_array['product_type'][$key] = array(	
                    'ctype' => $category_product_types['custom_products_type_child_id'],
                    'tpl' => $category_product_types['custom_products_type_id'], 
                    'product_type_cPath' => $categories_parent_path, 
                    'product_type_name' => $product_type_name
                );
            }
            
            // to be continue
            if ($page_info->custom_products_type_id_cat > 0 && tep_not_null($page_info->tpl)) {
                foreach ($product_type_info_array['product_type'] as $game_product_type_info_tmp) {
                    if ($game_product_type_info_tmp['tpl'] == $page_info->tpl) {
                        $product_type_info_array['product_type_cPath'] = $game_product_type_info_tmp['product_type_cPath'];
                        $product_type_info_array['product_type_name'] = $game_product_type_info_tmp['product_type_name'];
                    }
                }
            }
            
            ksort($product_type_info_array['product_type']);
        }
        
        unset($product_type_child_category_array);
    }
    
    if (isset($product_type_info_array['product_type'])) {
        foreach ($product_type_info_array['product_type'] as $product_type_array) {
            if (isset($product_type_array['product_type_name'])) {
                $css_class = false; // '';
                
                if ($page_info->ctype == $product_type_array['ctype'] && in_array($page_info->custom_products_type_id_cat, explode("_", $product_type_array['product_type_cPath']))) {
                    $css_class = true; //' boxMenuSelected';
                }
                
                $PT_box_content_array[] = array ( 'href' => tep_href_link(FILENAME_DEFAULT, 'cPath='.$product_type_array['product_type_cPath']),
                                                  'desc' => $product_type_array['product_type_name'],
                                                  'default' => $css_class
                                                );
            }
        }
    }
    
	$boxContent = $page_obj->get_html_simple_vmenu_box($boxHeading,$PT_box_content_array);
	
	unset($PT_box_content_array);
	
	require(DIR_WS_TEMPLATES . TEMPLATENAME_BOX);
	if ($page_info->tpl == '2') {
		if ($_SESSION['languages_id'] == 2) {
			echo '<div style="margin-bottom: 20px; margin-top: 20px;">';
			echo "<a href=" . tep_href_link('why-us-cn-i-822.ogm') . ">" . tep_image('http://image.offgamers.com/infolink/instant_delivery_banner_2.jpg') . '</a>';
			echo '</div>';
		} else if ($_SESSION['languages_id'] == 4) {
			echo '<div style="margin-bottom: 20px; margin-top: 20px;">';
			echo "<a href=" . tep_href_link('why-us-i-823.ogm') . ">" . tep_image('http://image.offgamers.com/infolink/instant_delivery_banner_4.jpg') . '</a>';
			echo '</div>';
		} else {
            echo '<div style="margin-bottom: 20px; margin-top: 20px;">';
			echo "<a href=" . tep_href_link('why-us-i-823.ogm') . ">" . tep_image('http://image.offgamers.com/infolink/instant_delivery_banner_1.jpg') . '</a>';
			echo '</div>';
        }
	}
    
    echo '<div style="margin-bottom: 20px; margin-top: 20px;">';
    echo "<a href='http://www.offgamers.com/offgamers-store-credits-swift-and-instantaneous-ln-5101.ogm'>" . tep_image('http://image.offgamers.com/infolink/instant_checkout_banner_'.$_SESSION['languages_id'].'.jpg') . '</a>';
    echo '</div>';
    
    echo '<div style="margin-bottom: 20px; margin-top: 20px;">';
    echo "<a href=" . tep_href_link(FILENAME_GIFT_CARD) . ">" . tep_image('http://image.offgamers.com/infolink/gc_banner_'.$_SESSION['languages_id'].'.jpg') . '</a>';
    echo '</div>';
    
	$boxContent_attributes = '';
	
	$latest_news_obj = new latest_news();
	
	$cat_array = array ($page_info->custom_products_type_id_cat, $page_info->main_game_id);
	$latest_news_obj->product_page_side_box($cat_array,$page_info->ctype);
	
	unset($latest_news_obj);
?>