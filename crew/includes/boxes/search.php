<!-- search //-->
<?
$boxHeading = tep_image('images/bullet-title.gif', '', 7, 7) . ' ' . BOX_HEADING_SEARCH;
$background_color = 'class="systemBox"';
$heading_title_style = 'class="systemBoxHeading"';
$boxText = "systemBoxText";
$corner_left = 'square';
$corner_right = 'square';
$boxContent_attributes = ' align="center"';

$boxContent = '	<table border="0" width="100%" cellspacing="0" cellpadding="0">';
$boxContent .= tep_draw_form('quick_find', tep_href_link(FILENAME_ADVANCED_SEARCH_RESULT, '', 'NONSSL', false), 'get');
$boxContent .= tep_draw_hidden_field("search_in_description","1") . tep_hide_session_id();
//$boxContent .= tep_draw_pull_down_menu('categories_id', tep_get_categories(array(array('id' => '', 'text' => TEXT_ALL_CATEGORIES))))."<br>";
//$boxContent .= tep_draw_input_field('keywords', '', 'size="10" maxlength="30" style="width: ' . (BOX_WIDTH-30) . 'px"') . '&nbsp;' . tep_hide_session_id() . tep_image_submit('button_quick_find.gif', BOX_HEADING_SEARCH) . '<br>' . BOX_SEARCH_TEXT . '<br><a href="' . tep_href_link(FILENAME_ADVANCED_SEARCH) . '"><b>' . BOX_SEARCH_ADVANCED_SEARCH . '</b></a>';
$boxContent .= '	<tr>
            			<td height=\"4\"></td>
            		</tr>
					<tr>
               			<td align="center" class=\"systemBoxField\">';
$boxContent .= tep_draw_input_field('keywords', '', 'size="30" maxlength="30"');
$boxContent .= '		</td>
					</tr>
					<tr>
    		  			<td height="4"></td>
    		  		</tr>
					<tr>
						<td align="center">'.tep_image_submit(THEMA.'button_search.gif', IMAGE_BUTTON_QUICK_FIND, '').'</td>
					</tr>
					<tr>
    		  			<td height="8"></td>
    		  		</tr>
					<tr>
    		  			<td align="center"><a href="' . tep_href_link(FILENAME_ADVANCED_SEARCH) . '" class="systemNav">' . BOX_SEARCH_ADVANCED_SEARCH . '</a></td>
    		  		</tr>';
$boxContent .= '</form>
				<script>
					function submit(){
  						document.forms["quick_find"].submit();
  					}
				</script>
						</td>
					</tr>
				</table>
				';
//'<br><a href="' . tep_href_link(FILENAME_ALLPRODS, '', 'NONSSL') . '">' . BOX_INFORMATION_ALLPRODS . '</a><br>';

require(DIR_WS_TEMPLATES . TEMPLATENAME_BOX);

$boxContent_attributes = '';
?>
<!-- search_eof //-->