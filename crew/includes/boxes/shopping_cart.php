<?
/*
 	$Id: shopping_cart.php,v 1.31 2011/04/06 04:15:39 weesiong Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2003 osCommerce
	
  	Released under the GNU General Public License
*/
?>
<!-- shopping_cart //-->
<?
$boxHeading = BOX_HEADING_SHOPPING_CART;
$background_color = 'class="systemBox"';
$heading_title_style = 'class="systemBoxHeading_spc"';
$boxText = "systemBoxText";
$corner_left = 'square';
$corner_right = 'rounded';
$box_top_style = 1;

$boxLink = '<a href="' . tep_href_link(FILENAME_SHOPPING_CART) . '"><img src="images/infobox/arrow_right.gif" border="0" alt="more" title=" more " width="12" height="10"></a>';
$boxContent = '<table border="0" width="100%" cellspacing="0" cellpadding="0" style="background-image: url(images/side-box-mid.gif); background-repeat: repeat-y;">
				<tr>
					<td align="center">
						<table border="0" width="90%" cellspacing="0" cellpadding="2">
							<tr>
			    		  		<td height="4"></td>
			    		  	</tr>';

$custom_prod_index = array();
if ($cart->count_contents() > 0) {
	if (tep_not_null($new_products_id_in_cart)) {
		if (PRODUCT_LIST_IMAGE > 0 ) {
			$aws_obj = new ogm_amazon_ws();
			$aws_obj->set_bucket_key('BUCKET_STATIC');
			$aws_obj->set_filepath('images/products/');
			
			$product_image_select_sql = "	SELECT pd.products_image, pd.products_name 
											FROM " . TABLE_PRODUCTS . " as p 
											INNER JOIN " . TABLE_PRODUCTS_DESCRIPTION . " as pd 
												ON (p.products_id = pd.products_id) 
											WHERE p.products_id = '" . $new_products_id_in_cart . "' 
												AND pd.language_id='" . $languages_id . "' ";

			$product_image_result_sql = tep_db_query($product_image_select_sql);
			$product_image_row = tep_db_fetch_array($product_image_result_sql);
			$new_added_products_image = $product_image_row['products_image'];
			$new_added_products_name = $product_image_row['products_name'];
			
			$pro_img_w = $pro_img_h = '';
			$products_image_info_array = $aws_obj->get_image_info($new_added_products_image);
			unset($aws_obj);
			
			if (tep_not_null($products_image_info_array)) {
				$pro_img = $products_image_info_array['src'];
				$pro_name = $new_added_products_name;
			} else if (tep_not_null($new_added_products_image) && file_exists(DIR_FS_IMAGES . "products/" . $new_added_products_image)) {
				$pro_img = THEMA_IMAGES . "products/" . $new_added_products_image;
				$pro_name = $new_added_products_name;
			} else {
				$pro_img = THEMA_IMAGES . "products/label_nopic.gif";
				$pro_name = '';
			}
			
			list($pro_img_w, $pro_img_h) = getimagesize($pro_img);
			if ($pro_img_w > PRODUCT_IMAGE_WIDTH) {
				$pro_img_w = PRODUCT_IMAGE_WIDTH;
			}
			if ($pro_img_h > PRODUCT_IMAGE_HEIGHT) {
				$pro_img_h = PRODUCT_IMAGE_HEIGHT;
			}
			
			
			if (tep_not_null($pro_img_w)) {
				$boxContent .= '
							<tr>
								<td class="systemBoxText" align="left">' . TEXT_LATEST_PRODUCT_ADDED . '</td>
							</tr>
							<tr>
								<td align="left">' .tep_image($pro_img, $pro_name, $pro_img_w, $pro_img_h, 'center') . '</td>
							</tr>';
			}
		}
	}
	
	$boxContent .= '		<tr>
								<td>
									<table border="0" width="100%" cellspacing="0" cellpadding="2">';
	
	$products = $cart->get_products();
	
	for ($i=0, $n=sizeof($products); $i<$n; $i++) {
    	$boxContent .= '	<tr><td valign="top">'.tep_image('images/icon-arrow-blue.gif', '', 4, 7).'</td><td align="left" valign="top" nowrap>';
		
      	if ((tep_session_is_registered('new_products_id_in_cart')) && ($new_products_id_in_cart == $products[$i]['id'])) {
        	$boxContent .= '<span class="newItemInCart">';
      	} else {
        	$boxContent .= '<span class="systemBoxText">';
      	}
	  	$prod_bd_query = tep_db_query("select products_bundle, products_bundle_dynamic from " . TABLE_PRODUCTS . " where products_id = " . $products[$i]['id'] . " order by products_id");
	  	$prod_bd = tep_db_fetch_array($prod_bd_query);
	  	$prod_bundle = $prod_bd['products_bundle'];
  	  	$prod_bundle_dynamic = $prod_bd['products_bundle_dynamic'];
  	  	
  	  	$bd = ($prod_bundle_dynamic == 'yes') ? 'y' : 'x';
		
		if ((int)$products[$i]['custom_products_type_id'] > 0) {
      		$custom_prod_index[$products[$i]['id']] = (int)$custom_prod_index[$products[$i]['id']] + 1;
  	  		//$boxContent .= $products[$i]['quantity'] . '&nbsp;x&nbsp;</span></td><td valign="top" class="systemBoxText"><a href="' . tep_href_link(FILENAME_CUSTOM_PRODUCT_INFO, 'products_id=' . $products[$i]['id'] . '&index='.(int)$custom_prod_index[$products[$i]['id']]) . '" class="systemBoxNavText">';
  	  		$boxContent .= $products[$i]['quantity'] . '&nbsp;x&nbsp;</span></td><td valign="top" class="systemBoxText">';
  	  	} else {
      		//$boxContent .= $products[$i]['quantity'] . '&nbsp;x&nbsp;</span></td><td valign="top" class="systemBoxText"><a href="' . tep_href_link(FILENAME_PRODUCT_INFO, 'products_id=' . $products[$i]['id'] . '&bdn='. $bd) . '" class="systemBoxNavText">';
      		$boxContent .= $products[$i]['quantity'] . '&nbsp;x&nbsp;</span></td><td valign="top" class="systemBoxText">';
      	}
      	
      	if ((tep_session_is_registered('new_products_id_in_cart')) && ($new_products_id_in_cart == $products[$i]['id'])) {
        	$boxContent .= '<span class="newItemInCart">';
      	} else {
        	$boxContent .= '<span>';
      	}
		
      	//$boxContent .= $products[$i]['name'] . '</span></a></td></tr>';
      	$boxContent .= $products[$i]['name'] . '</span></td></tr>';
		
      	if ((tep_session_is_registered('new_products_id_in_cart')) && ($new_products_id_in_cart == $products[$i]['id'])) {
        	tep_session_unregister('new_products_id_in_cart');
      	}
    }
		$boxContent .= '			</table>
				    			</td>
				    		</tr>';
} else {
		$boxContent .= '	<tr>
		               			<td align="left" class="systemBoxText">'. BOX_SHOPPING_CART_EMPTY.'</td>
		               		</tr>';
}

if ($cart->count_contents() > 0) {
		$boxContent .= '	<tr>
		               			<td class="systemBoxText"><div class="row_separator"></div></td>
		               		</tr>';
		$boxContent .= '	<tr>
		               			<td><div align="left" class="systemPrice">' . $currencies->format($cart->show_total(), false) . '</div></td>
		               		</tr>';
}
// ICW ADDED FOR CREDIT CLASS GV

if (tep_session_is_registered('gv_id')) {
    $gv_query = tep_db_query("select coupon_amount from " . TABLE_COUPONS . " where coupon_id = '" . $gv_id . "'");
    $coupon = tep_db_fetch_array($gv_query);
    $boxContent .= '		<tr>
               					<td align="left">'.tep_draw_separator();
    $boxContent .= tep_draw_separator('pixel_trans.gif', '100%', '10').'<table cellpadding="0" width="100%" cellspacing="0" border="0"><tr><td class="systemBoxText">' . VOUCHER_REDEEMED . '</td><td class="systemBoxText" align="right" valign="bottom">' . $currencies->format($coupon['coupon_amount'], true, '', '', 'buy') . '</td></tr></table>
   								</td>
   							</tr>';
}

if (tep_session_is_registered('cc_id') && $cc_id) {
    $boxContent .= '		<tr>
               					<td align="left">'.tep_draw_separator();
    $boxContent .= '				<table cellpadding="0" width="100%" cellspacing="0" border="0"><tr><td align="left" class="systemBoxText">' . CART_COUPON . '</td><td class="systemBoxText" align="right" valign="bottom">' . '<a href="javascript:popupWindow(\'' . tep_href_link(FILENAME_POPUP_COUPON_HELP, 'cID=' . $cc_id) . '\')">' . CART_COUPON_INFO . '</a>' . '</td></tr></table>
    							</td>
    						</tr>';
}
$boxContent .= '			<tr><td height="8"><div class="row_separator"></div></td></tr>
							<tr>
			               		<td align="left">
			               			<table cellpadding="0" width="100%" cellspacing="0" border="0">
			               				<tr>
			               					<td><b>
			               						<a href="' . tep_href_link(FILENAME_SHOPPING_CART) . '" class="systemNav">'.HEADER_TITLE_CART_CONTENTS.' | </a>
			               					</b></td><td>
			               					'.tep_div_button(2, IMAGE_BUTTON_CHECKOUT,tep_href_link(FILENAME_CHECKOUT_SHIPPING, '', 'SSL'), '', 'red_button').'
			               					</td>
			               				</tr>
			               			</table>
			               		</td>
			               	</tr>';
// ADDED FOR CREDIT CLASS GV END ADDITTION
$boxContent .= "	</table>
				</td>
			</tr>
			</table>";

require(DIR_WS_TEMPLATES . TEMPLATENAME_BOX);

$boxLink = '';
?>
<script language="javascript">
<!--
	function popupWindow(url, windowName, width, height) {
		windowName = (windowName == undefined) ? 'popupWindow' : windowName;
		width = (width == undefined) ? 100 : width;
		height = (height == undefined) ? 100 : height;
		
  		winRef = window.open(url,windowName,'toolbar=no,location=no,directories=no,status=no,menubar=no,scrollbars=no,resizable=yes,copyhistory=no,width='+width+',height='+height+',screenX=150,screenY=150,top=150,left=150')
  		winRef.focus();
	}
//-->
</script>
<!-- shopping_cart_eof //-->