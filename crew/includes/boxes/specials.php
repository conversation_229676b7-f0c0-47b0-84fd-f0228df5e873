<?php
/*
  $Id: specials.php,v 1.4 2010/02/05 10:49:55 henry.chow Exp $

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2003 osCommerce

  Released under the GNU General Public License
*/

// ***** Begin Separate Price per Customer Mod ***
  $customer_group_query = tep_db_query("select customers_group_id from " . TABLE_CUSTOMERS . "        where customers_id =  '" . $customer_id . "'");
  $customer_group = tep_db_fetch_array($customer_group_query);

  if ($random_product = tep_random_select("	SELECT p.products_id, pd.products_name, p.products_price, p.products_tax_class_id, pd.products_image, s.specials_new_products_price 
  											FROM " . TABLE_PRODUCTS . " p, " . TABLE_PRODUCTS_DESCRIPTION . " pd, " . TABLE_SPECIALS . " s 
  											WHERE p.products_status = '1' 
  												AND p.products_id = s.products_id 
  												AND pd.products_id = s.products_id 
  												AND pd.language_id = '" . (int)$languages_id . "' 
  												AND s.status = '1' 
  												AND s.customers_group_id=".(int)$customer_group['customers_group_id']." 
  											ORDER BY s.specials_date_added 
  											DESC LIMIT " . MAX_RANDOM_SELECT_SPECIALS)) {

  $scustomer_group_price_query = tep_db_query("select customers_group_price from " . TABLE_PRODUCTS_GROUPS . " where products_id = '" . $random_product['products_id']. "' and customers_group_id =  '" . $customer_group['customers_group_id'] . "'");
    if ($scustomer_group_price = tep_db_fetch_array($scustomer_group_price_query))
        $random_product['products_price']=$specials['products_price']= $scustomer_group_price['customers_group_price'];
// ***** End Separate Price per Customer Mod ***
?>
<!-- specials //-->
<?php
  $boxHeading = BOX_HEADING_SPECIALS;
  $background_color = '#0A0A0A';
  $corner_left = 'square';
  $corner_right = 'square';
  $boxContent_attributes = ' align="center"';
  $boxLink = '<a href="' . tep_href_link(FILENAME_SPECIALS) . '"><img src="images/infobox/arrow_right.gif" border="0" alt="more" title=" more " width="12" height="10"></a>';

  $boxContent = '<a href="' . tep_href_link(FILENAME_PRODUCT_INFO, 'products_id=' . $random_product["products_id"]) . '">' . tep_image(DIR_WS_IMAGES . $random_product['products_image'], $random_product['products_name'], SMALL_IMAGE_WIDTH, SMALL_IMAGE_HEIGHT) . '</a><br><a href="' . tep_href_link(FILENAME_PRODUCT_INFO, 'products_id=' . $random_product['products_id']) . '">' . $random_product['products_name'] . '</a><br><s>' . $currencies->display_price_original($random_product["products_id"], $random_product['products_price'], tep_get_tax_rate($random_product['products_tax_class_id'])) . '</s><br><span class="productSpecialPrice">' . $currencies->display_price_nodiscount($random_product['products_id'], $random_product['specials_new_products_price'], tep_get_tax_rate($random_product['products_tax_class_id'])) . '</span>';
                 
  require(DIR_WS_TEMPLATES . TEMPLATENAME_BOX);

  $boxLink = '';
  $boxContent_attributes = '';
?>
<!-- specials_eof //-->
<?php
  }
?>