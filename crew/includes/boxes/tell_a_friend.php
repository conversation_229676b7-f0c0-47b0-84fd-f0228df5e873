<?
/*
  $Id: tell_a_friend.php,v 1.7 2008/06/11 10:27:30 edwin.wang Exp $

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2003 osCommerce

  Released under the GNU General Public License
*/
?>
<!-- tell_a_friend //-->
<?
  	$boxHeading = BOX_HEADING_TELL_A_FRIEND;
	$background_color = 'class="systemBox"';
	$heading_title_style = 'class="systemBoxHeading"';
  	$boxText = "systemBoxText";
  	$corner_left = 'square';
  	$corner_right = 'square';
  	$boxContent_attributes = ' align="center"';
  	
	$boxContent = '<table border="0" width="100%" cellspacing="0" cellpadding="0">';
  	$boxContent .= tep_draw_form('tell_a_friend', tep_href_link(FILENAME_TELL_A_FRIEND, '', 'NONSSL', false), 'get');
  	$boxContent .= tep_draw_hidden_field('products_id', $HTTP_GET_VARS['products_id']) . tep_hide_session_id();
  	$boxContent .= '<tr>
            			<td height="4"></td>
            		</tr>
            		<tr><td align="center" class="systemBoxText">' . BOX_TELL_A_FRIEND_TEXT . '</td></tr>';
  	$boxContent .= '<tr><td align="center" class="systemBoxField">' . tep_draw_input_field('to_email_address', '', 'size="30"') . '</td></tr>';
  	$boxContent .= '<tr>
            			<td height="4"></td>
            		</tr>
            		<tr><td align="center" ><table cellspacing=0><tr><td>' . tep_div_button(1, BOX_HEADING_TELL_A_FRIEND,'tell_a_friend', '', 'gray_button') . '</td></tr></table></td></tr>';
	$boxContent .= '</form>
					</table>';
	
  	require(DIR_WS_TEMPLATES . TEMPLATENAME_BOX);
	
  	$boxContent_attributes = '';
?>
<!-- tell_a_friend_eof //-->
