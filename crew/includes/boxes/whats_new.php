<?php
/*
  $Id: whats_new.php,v 1.6 2010/02/23 10:48:41 henry.chow Exp $

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2003 osCommerce

  Released under the GNU General Public License
*/

  if ($random_product = tep_random_select("	SELECT p.products_id, pd.products_image, p.products_tax_class_id, p.products_price 
  											FROM " . TABLE_PRODUCTS . " as p
  											INNER JOIN " . TABLE_PRODUCTS_DESCRIPTION . " as pd
  												ON p.products_id = pd.products_id
  											WHERE products_status = '1' 
  											ORDER BY products_date_added 
  											DESC LIMIT " . MAX_RANDOM_SELECT_NEW)) {

?>
<!-- whats_new //-->
<?php
    $boxHeading = BOX_HEADING_WHATS_NEW;
    $background_color = '';
    $corner_left = 'square';
    $corner_right = 'square';
    $boxContent_attributes = ' align="center"';
    $boxLink = '<a href="' . tep_href_link(FILENAME_PRODUCTS_NEW) . '"><img src="images/infobox/arrow_right.gif" border="0" alt="more" title=" more " width="12" height="10"></a>';
	
    $random_product['products_name'] = tep_get_products_name($random_product['products_id']);
    $random_product['specials_new_products_price'] = tep_get_products_special_price($random_product['products_id']);

    if (tep_not_null($random_product['specials_new_products_price'])) {
      //CGDiscountSpecials start
	  $random_product['specials_new_products_price'] = tep_get_products_special_price($random_product['products_id']);
	  $whats_new_price = '<s>' . $currencies->display_price_original($random_product['products_id'], $random_product['products_price'], tep_get_tax_rate($random_product['products_tax_class_id'])) . '</s><br>';
      $whats_new_price .= '<span class="productSpecialPrice">' . $currencies->display_price_nodiscount($random_product['products_id'], $random_product['specials_new_products_price'], tep_get_tax_rate($random_product['products_tax_class_id'])) . '</span>';
      //CGDiscountSpecials end
    } else {
      $whats_new_price = $currencies->display_price($random_product['products_id'], $random_product['products_price'], tep_get_tax_rate($random_product['products_tax_class_id']));
    }
    
    $boxContent = '<a href="' . tep_href_link(FILENAME_PRODUCT_INFO, 'products_id=' . $random_product['products_id']) . '">' . tep_image(DIR_WS_IMAGES . $random_product['products_image'], $random_product['products_name'], SMALL_IMAGE_WIDTH, SMALL_IMAGE_HEIGHT) . '</a><br><a href="' . tep_href_link(FILENAME_PRODUCT_INFO, 'products_id=' . $random_product['products_id']) . '">' . $random_product['products_name'] . '</a><br>' . $whats_new_price;

    require(DIR_WS_TEMPLATES . TEMPLATENAME_BOX);

    $boxLink = '';
    $boxContent_attributes = '';
?>
<!-- whats_new_eof //-->
<?php
  }
?>