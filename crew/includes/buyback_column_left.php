<?php
/*
  $Id: buyback_column_left.php,v 1.5 2009/10/06 10:40:20 pooifong.chin Exp $

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2003 osCommerce

  Released under the GNU General Public License
*/
if (isset($_SESSION['customer_id']) && tep_not_null($_SESSION['customer_id'])) {
	$buyback_request_select_sql = "	SELECT brg.buyback_request_order_type, brg.buyback_status_id, brg.buyback_request_group_date, brg.buyback_request_group_expiry_date, brg.currency, brg.currency_value, brg.show_restock,
										br.buyback_request_id, br.products_id, br.buyback_amount, br.buyback_request_quantity, bs.buyback_status_name, br.buyback_quantity_confirmed, br.restock_character AS saved_restock_character, 
										br.buyback_dealing_type, pc.categories_id, brg.buyback_request_group_served, br.buyback_request_screenshot_before_name, br.buyback_request_screenshot_after_name 
									FROM ". TABLE_BUYBACK_REQUEST_GROUP . " AS brg
									LEFT JOIN " . TABLE_BUYBACK_REQUEST . " AS br 
										ON (brg.buyback_request_group_id = br.buyback_request_group_id)
									INNER JOIN " . TABLE_PRODUCTS_TO_CATEGORIES . " AS pc 
										ON (br.products_id = pc.products_id)
									INNER JOIN " . TABLE_BUYBACK_STATUS . " AS bs 
										ON ( brg.buyback_status_id = bs.buyback_status_id AND bs.language_id = '" . tep_db_input($languages_id) . "')
									WHERE brg.customers_id = '" . tep_db_input($_SESSION['customer_id']) . "'
										AND brg.buyback_request_order_type IN (0,1) AND brg.buyback_status_id IN (1,2)";
	$buyback_request_select_result = tep_db_query($buyback_request_select_sql);
	$buyback_request_select_num = tep_db_num_rows($buyback_request_select_result);
	
	$my_order_pending = 0;
	$my_order_processing = 0;
	$vip_order_pending = 0;
	$vip_order_processing = 0;
	
	if ($buyback_request_select_num > 0) {
		while ($buyback_request_row = tep_db_fetch_array($buyback_request_select_result)) {
			if ($buyback_request_row['buyback_request_order_type'] == 0) {
				// Normal buyback
				switch ($buyback_request_row['buyback_status_id']) {
					case 1: // Status Pending
							$my_order_pending ++;
						break;
					
					case 2: // Status Processing
							$my_order_processing ++;
						break;
				}
			} elseif ($buyback_request_row['buyback_request_order_type'] == 1) {
				// VIP buyback
				switch ($buyback_request_row['buyback_status_id']) {
					case 1: // Status Pending
						$vip_order_pending ++;
						break;
					
					case 2: // Status Processing
						$vip_order_processing ++;
						break;
				}
			}
		}		
	}
?>
<!-- Login_box //-->
<?php

	$box_style = 'Heading_Box_L';

	// Box Heading
	$boxHeadingSellingCart = HEADER_MY_SELLING_CART;
	$boxHeadingVipSellingCart = HEADER_MY_VIP_SELLING_CART;

	// Selling cart
	$boxContentSellingCart .= '<div class="boxContent">';
	$boxContentSellingCart .= '<ul>';
		
	$boxContentSellingCart .= '<li>
									'.tep_draw_form('order_history_search', tep_href_link(FILENAME_MY_ORDER_HISTORY, tep_get_all_get_params(array('action')) . 'action=search'), 'POST', 'id="my_order_history_pending"')
									 .tep_draw_hidden_field('odh_input_order_status_select', '1', 'id="odh_input_order_status_select"')
									 .tep_draw_hidden_field('odh_input_product_type_select', 'game_currency', 'id="odh_input_product_type_select"')
									 .tep_draw_hidden_field('odh_input_order_no', '', 'id="odh_input_order_no"')
									 .tep_draw_hidden_field('odh_input_game_select', '0', 'id="odh_input_game_select"')
									 .tep_draw_hidden_field('odh_input_start_date', '', 'id="odh_input_start_date"')
									 .tep_draw_hidden_field('odh_input_end_date', '', 'id="odh_input_end_date"')
									.'<a href="javascript:submitForm(\'my_order_history_pending\');" style="color:#000000;">'.$my_order_pending.' '.TEXT_ORDERS.' <span style="font-weight:bold;font-size:11px;">'.TEXT_PENDING.'</span></a>
									</form>
								</li>';
									
	$boxContentSellingCart .= '<li>
									'.tep_draw_form('order_history_search', tep_href_link(FILENAME_MY_ORDER_HISTORY, tep_get_all_get_params(array('action')) . 'action=search'), 'POST', 'id="my_order_history_processing"')
									 .tep_draw_hidden_field('odh_input_order_status_select', '2', 'id="odh_input_order_status_select"')
									 .tep_draw_hidden_field('odh_input_product_type_select', 'game_currency', 'id="odh_input_product_type_select"')
									 .tep_draw_hidden_field('odh_input_order_no', '', 'id="odh_input_order_no"')
									 .tep_draw_hidden_field('odh_input_game_select', '0', 'id="odh_input_game_select"')
									 .tep_draw_hidden_field('odh_input_start_date', '', 'id="odh_input_start_date"')
									 .tep_draw_hidden_field('odh_input_end_date', '', 'id="odh_input_end_date"')
									.'<a href="javascript:submitForm(\'my_order_history_processing\');" style="color:#000000;">'.$my_order_processing.' '.TEXT_ORDERS.' <span style="font-weight:bold;font-size:11px;">'.TEXT_PROCESSING.'</span></a>
									</form>
								</li>';
	
	$boxContentSellingCart .= '</ul>';
	$boxContentSellingCart .= '</div>';
	
	
	// VIP Selling cart
	$boxContentVipSellingCart .= '<div class="boxContent" id="box_profile_long">';
	$boxContentVipSellingCart .= '<ul>';
	
	$boxContentVipSellingCart .= '<li>
									'.tep_draw_form('order_history_search', tep_href_link(FILENAME_MY_VIP_ORDERS_HISTORY, tep_get_all_get_params(array('action')) . 'action=search'), 'POST', 'id="vip_order_history_pending"')
									 .tep_draw_hidden_field('vipodh_input_order_status_select', '1', 'id="odh_input_order_status_select"')
									 .tep_draw_hidden_field('vipodh_input_product_type_select', 'game_currency', 'id="odh_input_product_type_select"')
									 .tep_draw_hidden_field('vipodh_input_order_no', '', 'id="odh_input_order_no"')
									 .tep_draw_hidden_field('vipodh_input_game_select', '0', 'id="odh_input_game_select"')
									 .tep_draw_hidden_field('vipodh_input_start_date', '', 'id="odh_input_start_date"')
									 .tep_draw_hidden_field('vipodh_input_end_date', '', 'id="odh_input_end_date"')
									.'<a href="javascript:submitForm(\'vip_order_history_pending\');" style="color:#000000;">'.$vip_order_pending.' '.TEXT_ORDERS.' <span style="font-weight:bold;font-size:11px;">'.TEXT_PENDING.'</span></a>
									</form>
								  </li>';
								  
	$boxContentVipSellingCart .= '<li>
									'.tep_draw_form('order_history_search', tep_href_link(FILENAME_MY_VIP_ORDERS_HISTORY, tep_get_all_get_params(array('action')) . 'action=search'), 'POST', 'id="vip_order_history_processing"')
									 .tep_draw_hidden_field('vipodh_input_order_status_select', '2', 'id="odh_input_order_status_select"')
									 .tep_draw_hidden_field('vipodh_input_product_type_select', 'game_currency', 'id="odh_input_product_type_select"')
									 .tep_draw_hidden_field('vipodh_input_order_no', '', 'id="odh_input_order_no"')
									 .tep_draw_hidden_field('vipodh_input_game_select', '0', 'id="odh_input_game_select"')
									 .tep_draw_hidden_field('vipodh_input_start_date', '', 'id="odh_input_start_date"')
									 .tep_draw_hidden_field('vipodh_input_end_date', '', 'id="odh_input_end_date"')
									.'<a href="javascript:submitForm(\'vip_order_history_processing\');" style="color:#000000;">'.$vip_order_processing.' '.TEXT_ORDERS.' <span style="font-weight:bold;font-size:11px;">'.TEXT_PROCESSING.'</span></a>
									</form>
								  </li>';
								  
	$boxContentVipSellingCart .= '</ul>';
	$boxContentVipSellingCart .= '</div><br>';
	

	// Combine Heading with Contents
	$boxHeading = $boxHeadingSellingCart;	
	$boxContent .= $boxContentSellingCart;

	if ($_SESSION['vip_supplier_groups_id'] == '2' || $_SESSION['vip_supplier_groups_id'] == '3') {
		$boxContent .= '<div class="breakLine" style="padding:2px;"></div>';
		$boxContent .= '
			<div class="boxHeader" onClick="tongle_description(\'box_profile\', \'boxHeader\');" style="cursor:pointer;">
				<div class="boxHeaderLeft"><!-- --></div>
				<div class="boxHeaderCenter">
					<font>'.$boxHeadingVipSellingCart.'</font>
				</div>
				<div class="boxHeaderRight"><!-- --></div>
			</div>
			';
		$boxContent .= $boxContentVipSellingCart;
	}
	
	$boxContent .= $boxContentProfile;
} else {
	$boxHeading = '';
	$boxContent ='';
}
    require(DIR_WS_TEMPLATES . TEMPLATENAME_BOX);
?>