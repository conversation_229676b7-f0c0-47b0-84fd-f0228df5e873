<?
/*
  $Id: buyback_column_right.php,v 1.8 2012/11/06 04:26:24 chunhoong.leong Exp $

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2003 osCommerce

  Released under the GNU General Public License
*/

$ogm_buyback_info_array = array ('1' => array( 
												array('title' => TITLE_WHY_SELL_TO_US, 'description' => TEXT_WHY_SELL_TO_US)
											), 
											
									'2' => array( 
												array('title' => TITLE_WHY_SELL_TO_US, 'description' => TEXT_WHY_SELL_TO_US)
											),
											
									'3' => array( 
												array('title' => TITLE_WHY_SELL_TO_US, 'description' => TEXT_WHY_SELL_TO_US)
											),
											
									'4' => array( 
												array('title' => TITLE_WHY_SELL_TO_US, 'description' => TEXT_WHY_SELL_TO_US)
											)
							  );

$ogm_seller_info_array = array ('1' => array( 
												array('link' => 'http://kb.offgamers.com/?p=269', 'src' => 'http://image.offgamers.com/buybackcn/seller_guide.gif')
											), 
											
									'2' => array( 
												array('link' => 'http://kb.offgamers.com/zhcn/?p=407', 'src' => 'http://image.offgamers.com/buybackcn/seller_guide_cn.gif')
											),
											
									'3' => array( 
												array('link' => 'http://kb.offgamers.com/zhcn/?p=407', 'src' => 'http://image.offgamers.com/buybackcn/seller_guide_cn.gif')
											),
											
									'4' => array( 
												array('link' => 'http://kb.offgamers.com/?p=269', 'src' => 'http://image.offgamers.com/buybackcn/seller_guide.gif')
											)
							  );

if (tep_not_null(LIVE_SUPPORT)) {
    $boxContentSection2 .= '<div class="boxContentShort" style="width:139px;padding:0px;text-align:center">';
    $boxContentSection2 .= LIVE_SUPPORT;
    $boxContentSection2 .= '</div>';
    $boxContentSection2 .= '<div class="dottedLine"><!-- --></div>';
}

foreach ($ogm_buyback_info_array[$languages_id] as $buyback_info) {
	$boxContentSection2 .= '<div class="boxContentShort" style="width:139px;padding:0px;text-align:left">';
	$boxContentSection2 .= '<div style="padding:8px;font-size:13px;font-weight:bold;">'.$buyback_info['title'].'</div>';
	$boxContentSection2 .= '<div style="padding:0px 8px; 8px 8px;">'.$buyback_info['description'].'</div>';
	$boxContentSection2 .= '</div>';
	$boxContentSection2 .= '<div class="dottedLine"><!-- --></div>';
}

foreach ($ogm_seller_info_array[$languages_id] as $seller_info) {
	$boxContentSection2 .= '<div class="boxContentShort" style="width:139px;padding:0px;text-align:center">';
	$boxContentSection2 .= '<a href="'.$seller_info["link"].'"><img src="'.$seller_info["src"].'" /></a>';
	$boxContentSection2 .= '</div>';
	$boxContentSection2 .= '<div class="dottedLine"><!-- --></div>';
}
?>