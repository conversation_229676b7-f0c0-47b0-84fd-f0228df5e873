<?php

class access_token {
    private $raw_str;
    private $algo = 'md5';
    
	public function __construct() {
        $this->cleanupAccessToken();
    }
    
    public function getAccessToken($customer_id, $raw_str = '', $unique = TRUE) {
        $return_str = '';
        
        if ($customer_id) {
            $this->generateUniqueId($raw_str, $unique);
            $token = $this->generateAccessToken();
            
            $return_str = $this->addAccessToken($token, $customer_id);
        }
        
        return $return_str;
    }
    
    public function set_hash_algo($algo) {
        if (tep_not_null($algo)) {
            $this->algo = $algo;
        }
    }
    
    public function generateUniqueId($raw_str = NULL, $unique = TRUE) {
        if (!is_null($raw_str)) {
            if ($raw_str) {
                if ($unique) {
                    $this->raw_str .= $raw_str;
                } else {
                    $this->raw_str = $raw_str;
                }
            } else {
                $this->raw_str = uniqid(mt_rand(), TRUE);
            }
        }
        
        return $this->raw_str; 
    }
    
    public function generateAccessToken($data_str = NULL, $algo = '') {
        $this->generateUniqueId($data_str);
        $this->set_hash_algo($algo);
        
        return hash($this->algo, $this->raw_str);
    }
    
    public function getCustomerIdFromAccessToken($token) {
        $return_int = 0;
        
        $verify_select_sql = "	SELECT customers_id 
                                FROM " . TABLE_AUTHORIZED_TOKEN . " 
                                WHERE auth_token = '" . $token . "'";
        $verify_result_sql = tep_db_query($verify_select_sql);
        if ($verify_row = tep_db_fetch_array($verify_result_sql)) {
            $return_int = $verify_row['customers_id'];
        }
        
        return $return_int;
    }
    
    public function addAccessToken($token, $customer_id) {
        $return_str = '';
        
        if (tep_not_null($token) && tep_not_null($customer_id)) {
            $create_token_array = array(
                'auth_token' => $token,
                'customers_id' => $customer_id,
                'created_datetime' => 'now()',
            );
            tep_db_perform(TABLE_AUTHORIZED_TOKEN, $create_token_array);
            
            $return_str = $token;
        }
        
        return $return_str;
    }
    
    public function removeAccessToken($token) {
        $token_delete_sql = "   DELETE FROM " . TABLE_AUTHORIZED_TOKEN . " 
                                WHERE auth_token = '" . $token . "';";
        tep_db_query($token_delete_sql);
    }
    
    public function cleanupAccessToken() {
        $token_delete_sql = "   DELETE FROM " . TABLE_AUTHORIZED_TOKEN . " 
                                WHERE now() > (created_datetime + INTERVAL 10 DAY);";
        tep_db_query($token_delete_sql);
    }
}
?>