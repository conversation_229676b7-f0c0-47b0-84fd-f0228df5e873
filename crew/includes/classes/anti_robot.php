<?php

class anti_robot {
    const MAX_GUEST_ENTRY = 20;
    const MAX_HIT_GUEST_COUNT = 15;
    const MAX_HIT_COUNT = 88;
    const MAX_HIT_DURATION = 5; // in minutes
    const SUSPEND_DURATION = 30; // in minutes
    const ACTIVE_DURATION = 30; // in minutes
    const PHONE_VERIFIED_CACHE_DURATION = 900; // in seconds (900s = 15min)
    
    private $user_id;
    private $user_data;
    private $traffic_switch = TRUE;
    private $is_phone_verified;
    
	function __construct() {
        if ($this->traffic_switch) {
            if (isset($_SESSION['customer_id'])) {
                $this->user_id = (int)$_SESSION['customer_id'];

                // remove expired buyback_online_user record
                $this->deleteExpiredData();
            } else {
                $this->traffic_switch = FALSE;
                $this->user_id = 0;
            }
        }
	}
	
    public function traffic_handling() {
        $return_bool = TRUE;
        
        if ($this->traffic_switch) {
            switch (TRUE) {
                case $this->isCaptchaRequiredRequest():
                    // display waiting page & ask for captcha
                    $this->prepareWaitingPage();
                    break;
                case $this->isSuspendedRequest():
                    // display waiting page & ask for captcha
                    $this->prepareWaitingPage();
                    break;
                case $this->isPhoneVerifiedRequest():
                    $this->updateEntry($entry_type = '1');
                    $this->deleteCaptchaFlag();
                    $return_bool = FALSE;
                    break;
                default:
                    // guess request
                    if ($this->getTotalGuest() < self::MAX_GUEST_ENTRY) {
                        $this->updateEntry($entry_type = '0');
                        $this->deleteCaptchaFlag();
                        $return_bool = FALSE;
                    } else {
                        // display waiting page & ask for captcha
                        $this->prepareWaitingPage();
                    }

                    break;
            }
        } else {
            $return_bool = FALSE;
        }
        
        return $return_bool;
    }
    
    public function waiting_handling() {
        $return_bool = TRUE;
        
        if ($this->traffic_switch) {
            switch (TRUE) {
                case $this->isCaptchaRequiredRequest():
                    // display waiting page & ask for captcha
                    break;
                case $this->isFlagSuspended():
                    // display waiting page & ask for captcha
                    break;
                case $this->isPhoneVerifiedRequest():
                    $return_bool = FALSE;
                    break;
                default:
                    // guess request                
                    if ($this->getTotalGuest() < self::MAX_GUEST_ENTRY) {
                        $return_bool = FALSE;
                    }

                    break;
            }
        } else {
            $return_bool = FALSE;
        }
        
        return $return_bool;
    }
    
    public function isCaptchaRequiredRequest() {
        return isset($_SESSION['anti_robot_captcha']) && $_SESSION['anti_robot_captcha'] == 0;
    }
    
    private function isExpiredRequest() {
        $return_bool = FALSE;
        $online_user_expiry_date = $this->getOnlineUser('expiry_date');
        
        if (tep_not_empty($online_user_expiry_date)) {
            if (strtotime($online_user_expiry_date) < time()) {
                $return_bool = TRUE;
            }
        }
        
        return $return_bool;
    }
    
    private function isFlagSuspended() {
        $return_bool = FALSE;
        
        $select_sql = " SELECT *
                        FROM " . TABLE_BUYBACK_SUSPEND_USER . "
                        WHERE customer_id = " . $this->user_id;
        $result_sql = tep_db_query($select_sql);
        if (tep_db_num_rows($result_sql)) {
            $return_bool = TRUE;
        }
        
        return $return_bool;
    }
    
    public function isPhoneVerifiedRequest() {
        global $memcache_obj;
        
        if (is_null($this->is_phone_verified)) {
            $this->is_phone_verified = 0;
            $cache_key = 'buyback/entry/cid/' . $this->user_id;
            $cache_result = $memcache_obj->fetch($cache_key);

            if ($cache_result !== FALSE) {
                $this->is_phone_verified = $cache_result;
            } else {
                $customer_complete_phone_info_array = tep_format_telephone($this->user_id);
                $complete_telephone_number = sizeof($customer_complete_phone_info_array) > 0 ? $customer_complete_phone_info_array['country_international_dialing_code'] . $customer_complete_phone_info_array['telephone_number'] : '';
                if (tep_info_verified_check($this->user_id, $complete_telephone_number, 'telephone') === 1) {
                    $this->is_phone_verified = 1;
                } else {
                    $this->is_phone_verified = 0;
                }

                $memcache_obj->store($cache_key, $this->is_phone_verified, self::PHONE_VERIFIED_CACHE_DURATION);
            }
        }
        
        return $this->is_phone_verified == 1;
    }
    
    private function isSuspendedRequest() {
        $return_bool = FALSE;
        
        if ($this->isFlagSuspended()) {
            $return_bool = TRUE;
        } else {
            if ($this->getOnlineUser() === FALSE) {
                // new user
                $return_bool = FALSE;
            } else if ($this->isExpiredRequest() !== TRUE) {    // Only Giest will get into expired request case to prevent them hold the spot forever
                $max_hit_count = $this->isPhoneVerifiedRequest() ? self::MAX_HIT_COUNT : self::MAX_HIT_GUEST_COUNT;
                
                if ($this->getCurrentHitCount() <= $max_hit_count) {
                    // update hit count
                    $this->updateHitCount();
                    $return_bool = FALSE;
                } else if ($this->getCurrentHitStartTimeDuration() > self::MAX_HIT_DURATION) {    // if current time - start time > max hit duration
                    // reset hit start time and hit count
                    $this->resetCount();
                    $return_bool = FALSE;
                } else {
                    // suspend user from requst
                    $this->registerSuspend();
                    $this->clearCount();
                    $return_bool = TRUE;
                }
            } else {
                $this->clearCount();
                $return_bool = FALSE;
            }
        }
        
        return $return_bool;
    }
    
    private function getCurrentHitCount() {
        return (int)$this->getOnlineUser('hit_count') + 1;
    }
    
    private function getCurrentHitStartTimeDuration() {
        $return_int = 0;
        $hit_start = $this->getOnlineUser('hit_start_time');
        
        if (tep_not_empty($hit_start)) {
            $return_int = (int) ceil((time() - strtotime($hit_start)) / 60);
        }
        
        return $return_int;
    }
    
    private function getOnlineUser($field = NULL) {
        if (is_null($this->user_data)) {
            $select_sql = " SELECT *
                            FROM " . TABLE_BUYBACK_ONLINE_USER . "
                            WHERE customer_id = " . $this->user_id;
            $result_sql = tep_db_query($select_sql);
            if ($result_row = tep_db_fetch_array($result_sql)) {
                $this->user_data = array(
                    'entry_type' => $result_row['entry_type'],
                    'expiry_date' => $result_row['expiry_date'],
                    'hit_count' => $result_row['hit_count'],
                    'hit_start_time' => $result_row['hit_start_time']
                );
            } else {
                $this->user_data = FALSE;
            }
        }
        
        return is_null($field) ? $this->user_data : ($this->user_data === FALSE ? '' : $this->user_data[$field]);
    }
    
    public function getReturnURL($default = '') {
        $return_str = '';
        
        if (isset($_SESSION['anti_robot_returnURL'])) {
            $return_str = $_SESSION['anti_robot_returnURL'];
        }
        
        return tep_not_empty($return_str) ? $return_str : $default;
    }
    
    private function getTotalGuest() {
        $return_int = 0;
        
        $select_sql = " SELECT count(customer_id) as total 
                        FROM " . TABLE_BUYBACK_ONLINE_USER . " 
                        WHERE entry_type = '0'
                            AND customer_id != " . $this->user_id;
        $result_sql = tep_db_query($select_sql);
        if ($result_row = tep_db_fetch_array($result_sql)) {
            $return_int = (int)$result_row['total'];
        }
        
        return $return_int;
    }
    
    private function setReturnURL($url) {
        $_SESSION['anti_robot_returnURL'] = $url;
    }
    
    private function updateEntry($entry_type = '0') {
        if ($this->getOnlineUser() === FALSE) {
            $sql_insert_array = array(
                'customer_id' => $this->user_id,
                'entry_type' => $entry_type,
                'hit_count' => '1',
                'hit_start_time' => 'now()',
                'expiry_date' => date('Y-m-d H:i:s', strtotime("+" . self::ACTIVE_DURATION . " minute")), 
            );
            
            tep_db_perform(TABLE_BUYBACK_ONLINE_USER, $sql_insert_array);
        } else {
            $query = " 	UPDATE " . TABLE_BUYBACK_ONLINE_USER . " 
                            SET entry_type = '" . $entry_type . "'
                        WHERE customer_id = " . $this->user_id;
            tep_db_query($query);
        }
        
        unset($this->user_data);
    }
    
    public function updateCaptchaFlag($flag = 0) {
        $_SESSION['anti_robot_captcha'] = $flag;
    }
    
    private function updateHitCount() {
        $query = " 	UPDATE " . TABLE_BUYBACK_ONLINE_USER . " 
                        SET hit_count = hit_count + 1
                    WHERE customer_id = " . $this->user_id;
        tep_db_query($query);
        
        unset($this->user_data);
    }
    
    private function deleteCaptchaFlag() {
        unset($_SESSION['anti_robot_captcha']);
    }
    
    private function deleteExpiredData() {
        $query = " 	DELETE FROM " . TABLE_BUYBACK_ONLINE_USER . " 
                    WHERE expiry_date < now()";
        tep_db_query($query);
        
        $query = " 	DELETE FROM " . TABLE_BUYBACK_SUSPEND_USER . " 
                    WHERE expiry_date < now()";
        tep_db_query($query);
    }
    
    private function registerSuspend() {
        $sql_insert_array = array(	
            'customer_id' => $this->user_id,
            'expiry_date' => date('Y-m-d H:i:s', strtotime("+" . self::SUSPEND_DURATION . " minute")), 
        );
        tep_db_perform(TABLE_BUYBACK_SUSPEND_USER, $sql_insert_array);
    }
    
    private function prepareWaitingPage() {
        $return_url = $_SERVER['HTTP_REFERER'];
        
        if (!tep_not_empty($return_url) || strstr($return_url, HTTP_SERVER) === FALSE) {
            //use main page
            $return_url = '/';
        }
        
        $this->setReturnURL($return_url);
        $this->updateCaptchaFlag();
    }
    
    private function resetCount() {
        $query = " 	UPDATE " . TABLE_BUYBACK_ONLINE_USER . " 
                        SET hit_count = 1,
                            hit_start_time = now()
                    WHERE customer_id = " . $this->user_id;
        tep_db_query($query);
        
        unset($this->user_data);
    }
    
    private function clearCount() {
        $query = " 	DELETE FROM " . TABLE_BUYBACK_ONLINE_USER . " 
                    WHERE customer_id = " . $this->user_id;  // prevent duplicate entry
        tep_db_query($query);
        
        unset($this->user_data);
    }
    
    public function __destruct() {
        
    }
}
?>