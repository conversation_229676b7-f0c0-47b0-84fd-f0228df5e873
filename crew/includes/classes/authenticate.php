<?php

/*
  $Id: authenticate.php,v 1.3 2014/12/29 12:37:30 weesiong Exp $
 */

class authenticate {
    public static function Login($cid) {
        global $cart, $cookie_path, $cookie_domain, $customer_id, $customer_first_name, $customer_last_name, $customer_country_id,
                $vip_supplier_groups_id, $navigation;

        if (SESSION_RECREATE == 'True') {
            tep_session_recreate();
        }
        
        $customer_sql = '   select customers_id, customers_firstname, customers_lastname, account_activated, customers_password, customers_email_address, 
                                customers_default_address_id, customers_status, customers_groups_id, customers_login_sites, customers_dob, 
                                customers_country_dialing_code_id, customers_telephone
                            from ' . TABLE_CUSTOMERS . ' 
                            where customers_id = "' . $cid . '"';
        $check_customer_query = tep_db_query($customer_sql);
        if ($check_customer = tep_db_fetch_array($check_customer_query)) {
            $check_country_query = tep_db_query("select entry_country_id, entry_zone_id from " . TABLE_ADDRESS_BOOK . " where customers_id = '" . $cid . "' and address_book_id = '" . (int) $check_customer['customers_default_address_id'] . "'");
            $check_country = tep_db_fetch_array($check_country_query);

            $customer_id = $check_customer['customers_id'];
            $customer_first_name = $check_customer['customers_firstname'];
            $customer_last_name = $check_customer['customers_lastname'];
            $customer_country_id = $check_country['entry_country_id'];
            
            tep_session_register('customer_id');
            tep_session_register('customer_country_id');
            tep_session_register('customer_first_name');
            tep_session_register('customer_last_name');
            
            $_SESSION['customers_groups_id'] = $customers_groups_id = $check_customer['customers_groups_id'];
            $_SESSION['customer_default_address_id'] = $check_customer['customers_default_address_id'];
            $_SESSION['customers_login_ip'] = tep_get_ip_address();
            $_SESSION['customers_login_timestamp'] = time();
            $_SESSION['login_trial'] = 0;
            
            // new added, need to check on the class user to make sure all newly added session are there as well
            $_SESSION['customers_groups_name'] = tep_get_customers_groups_name($customers_groups_id);
            
            $buyback_vip_select_sql = "	SELECT vip_supplier_groups_id
                                        FROM " . TABLE_CUSTOMERS_VIP . "
                                        WHERE customers_id='" . $customer_id . "'";
            $buyback_vip_result_sql = tep_db_query($buyback_vip_select_sql);
            if ($buyback_vip_row = tep_db_fetch_array($buyback_vip_result_sql)) {
                $vip_supplier_groups_id = $buyback_vip_row['vip_supplier_groups_id'];
            } else {
                $vip_supplier_groups_id = 1; //default set to member if success login
            }
            tep_session_register('vip_supplier_groups_id');

            $diff_day = 0;
            $customer_last_logon_select_sql = " SELECT customers_info_date_of_last_logon, customers_info_number_of_logons
                                                FROM " . TABLE_CUSTOMERS_INFO . "
                                                WHERE customers_info_id='" . $customer_id . "'";
            $customer_last_logon_result_sql = tep_db_query($customer_last_logon_select_sql);
            if ($customer_last_logon_row = tep_db_fetch_array($customer_last_logon_result_sql)) {
                $diff_seconds = time() - strtotime($customer_last_logon_row['customers_info_date_of_last_logon']);
                $diff_day = $diff_seconds / 86400;
            }

            $customer_info_data_array = array(
                'customers_info_date_of_last_logon' => 'now()',
                'customers_info_number_of_logons' => $customer_last_logon_row['customers_info_number_of_logons'] + 1,
                'customer_info_selected_country' => $_SESSION['country']
            );

            if ($diff_day > 90) { //No login for 90days will be set as dormant account
                $customer_info_data_array['customer_info_account_dormant'] = 1;
            }

            tep_db_perform(TABLE_CUSTOMERS_INFO, $customer_info_data_array, 'update', 'customers_info_id = "' . $_SESSION['customer_id'] . '"');

            // Prevent multiple insert for same Primary Key data
//            $customer_login_insert_sql = "INSERT IGNORE INTO " . TABLE_CUSTOMERS_LOGIN_IP_HISTORY . " (customers_id, customers_login_date, customers_login_ua_info, customers_login_ip) VALUES (" . tep_db_input($_SESSION['customer_id']) . ", now(), '" . tep_db_input(isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : getenv('HTTP_USER_AGENT')) . "', '" . tep_db_input($_SESSION['customers_login_ip']) . "');";
//            tep_db_query($customer_login_insert_sql);

            $loginData = array(
                "customers_id" => $customer_id,
                "customers_login_date" => 'now()',
                "customers_login_ip" => $_SESSION['customers_login_ip'],
                "customers_login_ua_info" => (isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : getenv('HTTP_USER_AGENT')),
                "login_method" => (isset($_SESSION['login_method']) ? $_SESSION['login_method'] : "-"),
            );
            tep_db_perform(TABLE_CUSTOMERS_LOGIN_HISTORY, $loginData);
            
            regional_setting::saveCustomerPreference($customer_id);
            
            // Remember Me feature
//            if (isset($_REQUEST['remember_me']) || $cookie_remember_me) {
//                $expire = time() + 60 * 60 * 24 * 90; // 90 days expiry date;
//                tep_setcookie('ogm[un]', $email_address, $expire, $cookie_path, $cookie_domain);
//                tep_setcookie('ogm[uc]', md5($check_customer['customers_email_address'] . ':' . $check_customer['customers_password']), $expire, $cookie_path, $cookie_domain);
//            }

            // Check SSO token table before creating new one
//            $sso_token = '';
//            $sso_row = oauth::getValidSSOToken($customer_id);
//            if (!empty($sso_row)) {
//                $sso_token = $sso_row['sso_token'];
//                $sso_status = oauth::getSSOTokenStatus($sso_token, $customer_id);
//            }
//            if (!$sso_status) {
//                $sso_token = oauth::addSSOToken($customer_id);
//            }
//            if (isset($sso_token) && $sso_token != "") {
//                $cookieLifeTime = 0; //time() + 86400
//                tep_setcookie('ogm[si]', $customer_id, $cookieLifeTime, $cookie_path, $cookie_domain);
//                tep_setcookie('ogm[st]', $sso_token, $cookieLifeTime, $cookie_path, $cookie_domain);
//                $_SESSION['sso_token'] = $sso_token;
//            } else {
//                $_SESSION['sso_token'] = $_COOKIE['ogm']['st'];
//            }

            
            // restore cart contents
            if (is_object($cart)) {
                $cart->restore_contents();
            }
            
            update_lifetime_cookies();

            if (isset($_SESSION['trace'])) {
            } else {
                if (tep_not_null($_REQUEST['baseURL'])) {
                } else if (sizeof($navigation->snapshot) > 0) {
                    $navigation->clear_snapshot();
                } else {
                }
            }
            
            return true;
        }
        
        return false;
    }
    
    public static function logoff($delCookie = true) {
        $request_type = (getenv('HTTPS') == 'on') ? 'SSL' : 'NONSSL';
        $cookie_domain = (($request_type == 'NONSSL') ? HTTP_COOKIE_DOMAIN : HTTPS_COOKIE_DOMAIN);
        $cookie_path = (($request_type == 'NONSSL') ? HTTP_COOKIE_PATH : HTTPS_COOKIE_PATH);

        # remove Single Sign-On
        if ($delCookie) {
//            $cookie_domain = (($request_type == 'NONSSL') ? HTTP_COOKIE_DOMAIN : HTTPS_COOKIE_DOMAIN);
//            $cookie_path = (($request_type == 'NONSSL') ? HTTP_COOKIE_PATH : HTTPS_COOKIE_PATH);
//            oauth::delSSOToken($cookie_path, $cookie_domain);
//
//            # remove Remember Me
//            if (isset($_COOKIE['ogm'])) {
//                $expire = time() - 3600;
//
//                tep_setcookie('ogm[un]', '', $expire, $cookie_path, $cookie_domain);
//                tep_setcookie('ogm[uc]', '', $expire, $cookie_path, $cookie_domain);
//            }
        } else {
            global $memcache_obj;

            $memcache_obj->delete(TABLE_SSO_TOKEN . '/cid/' . $_SESSION['customer_id'] . '/token/' . $_SESSION['sso_token'], 0);
            tep_db_query("DELETE FROM " . TABLE_SSO_TOKEN . " WHERE sso_token = '" . $_SESSION['sso_token'] . "' AND customers_id = '" . $_SESSION['customer_id'] . "'");
        }

        # remove session
        unset($_SESSION['customer_id']);
        unset($_SESSION['sso_token']);
        unset($_SESSION['customers_groups_id']);
        unset($_SESSION['customer_default_address_id']);
        unset($_SESSION['customer_first_name']);
        unset($_SESSION['customer_country_id']);
        unset($_SESSION['customer_zone_id']);
        unset($_SESSION['comments']);
        unset($_SESSION['custom_comments']);
        unset($_SESSION['buyback_comment']);
        unset($_SESSION['buyback_agree']);
        unset($_SESSION['gv_id']);
        unset($_SESSION['cc_id']);
        unset($_SESSION['cart']);

        # remove facebook
        unset($_SESSION['fb_uid']);
        unset($_SESSION['FB_prelogin_switch']);
        unset($_SESSION['need_sc_usage_qna']);
        unset($_SESSION['lifetime_secret']);
        
        # shasso 
        unset($_SESSION['user_avatar']);
        unset($_SESSION['login_method']);
    }

}

