<?php

class auto_restock {

    private $service;
    public static $service_api = array();

    public function __construct() {
        self::include_lib();
    }

    private static function include_lib() {
        $module_directory = dirname(__FILE__) . DIRECTORY_SEPARATOR . '..' . DIRECTORY_SEPARATOR . 'modules' . DIRECTORY_SEPARATOR . 'auto_restock' . DIRECTORY_SEPARATOR;
        $dir = scandir($module_directory);

        foreach ($dir as $dir_name) {
            if (substr($dir_name, -4) === '_sdk') {
                list($sdk_name, ) = explode('_', $dir_name);
                $api_name = strtolower($sdk_name) . '_api';
                // Ensure php file and config exists before including
                if (file_exists($module_directory . DIRECTORY_SEPARATOR . $dir_name . DIRECTORY_SEPARATOR . $api_name . '.php') && file_exists($module_directory . DIRECTORY_SEPARATOR . $dir_name . DIRECTORY_SEPARATOR . 'config.inc.php')) {
                    include_once($module_directory . DIRECTORY_SEPARATOR . $dir_name . DIRECTORY_SEPARATOR . $api_name . '.php');
                    self::$service_api[$api_name] = new $api_name;
                }
            }
        }
    }

    private function isSupportedService($service) {
        return isset(self::$service_api[$service]);
    }

    /*
     * Use in the aft script during the delivery of cdkey
     */

    public static function isSupportedSKU($sku) {
        $return_string = '';

        if ($sku) {
            self::include_lib();

            foreach (self::$service_api as $service_api => $service_api_obj) {
                if (call_user_func_array($service_api . '::isSupportedSKU', array($sku))) {
                    $return_string = $service_api;
                    break;
                }
            }
        }

        return $return_string;
    }

    public function isSupportedSKUPattern($sku) {
        $return_string = '';

        foreach (self::$service_api as $service_api => $service_api_obj) {
            $pattern_array = call_user_func_array($service_api . '::service_api_pattern');
            foreach ($pattern_array as $pattern) {
                if (substr($sku, 0, $pattern['prefix_len']) === $pattern['prefix_text']) {
                    $return_string = $service_api;
                    break;
                }
            }
        }

        return $return_string;
    }

    private function getServiceObj($sku, $service) {
        if (tep_not_empty($sku)) {
            // first layer if service provide invalid
            if (!$this->isSupportedService($service)) {
                // second layer find service if pattern match
                $service = $this->isSupportedSKUPattern($sku);
            }

            if (!isset($this->service[$service])) {
                if (!tep_not_empty($service)) {
                    $service = self::isSupportedSKU($sku);
                }

                if (tep_not_empty($service)) {
                    $this->service[$service] = self::$service_api[$service];
                }
            }
        }

        return $service != '' ? $this->service[$service] : FALSE;
    }

    /*
     * Used in restock by each order product sku
     */

    public function processBatchRestock($orders_products_id, $process_products_id, $process_store_price, $sku, $qty, $extra_params) {
        $return_bool = FALSE;
        $service_provider = isset($extra_params['api_provider']) ? $extra_params['api_provider'] : '';

        if ($service_obj = $this->getServiceObj($sku, $service_provider)) {
            $extra_info_params = array(
                'orders_products_id' => $orders_products_id
            );

            $return_bool = $service_obj->processBatchRestock($process_products_id, $process_store_price, $sku, $qty, $extra_info_params);

            unset($service_obj);
        }

        return $return_bool;
    }

    private function reportError($response_data, $ext_subject = '') {
        ob_start();
        echo "<pre>" . $ext_subject;
        echo "<br>================================================RESPONSE================================================<br>";
        print_r($response_data);
        echo "<br>========================================================================================================";
        $response_data = ob_get_contents();
        ob_end_clean();

        $serverName = !empty($_SERVER['SERVER_NAME']) ? $_SERVER['SERVER_NAME'] : '-';
        $subject = '[OFFGAMERS] Auto Restock Error - ' . date("F j, Y H:i") . ' from ' . $serverName;
        @tep_mail('OffGamers', '<EMAIL>', $subject, $response_data, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
    }

}

?>