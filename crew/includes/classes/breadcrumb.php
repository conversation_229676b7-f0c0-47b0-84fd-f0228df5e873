<?
/*
  	$Id: breadcrumb.php,v 1.11 2009/01/09 03:50:59 weichen Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2003 osCommerce
	
  	Released under the GNU General Public License
*/

class breadcrumb
{
	var $_trail;
	
    function breadcrumb() {
      	$this->reset();
    }
	
    function reset() {
      	$this->_trail = array();
    }
	
    function add($title, $link = '') {
      	$this->_trail[] = array('title' => strip_tags($title), 'link' => $link);
    }
	
    function trail($separator = ' - ') {
      	$trail_string = TEXT_YOU_ARE_HERE . '&nbsp;';
		
      	for ($i=0, $n=sizeof($this->_trail); $i<$n; $i++) {
			if (isset($this->_trail[$i]['link']) && tep_not_null($this->_trail[$i]['link']) && ($i < $n-1)) {
        		if ($this->_trail[$i]['title'] == HEADER_TITLE_CATALOG) {
        	   		//do nothing
        		} else {
        			$trail_string .= '<a href="' . $this->_trail[$i]['link'] . '" class="path">' . $this->_trail[$i]['title'] . '</a>';	
        		}
        	} else {
        		if ($this->_trail[$i]['title'] == HEADER_TITLE_CATALOG) {
        	   		//do nothing
        		} else {
            		$trail_string .= $this->_trail[$i]['title'];
        		}
        	}
	   		if ($this->_trail[$i]['title'] == HEADER_TITLE_CATALOG) {
	   			// || $this->_trail[$i]['title'] == "Home"
				//do nothing
	   		} else {
          		if (($i+1) < $n) $trail_string .= $separator;
			}
		}
		
      	return $trail_string;
	}
}
?>