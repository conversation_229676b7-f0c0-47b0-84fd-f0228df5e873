<?php
/*
  	$Id: buyback.php,v 1.11 2008/09/12 07:54:35 chan Exp $

  	Developer: <PERSON>
  	Copyright (c) 2006 SKC Venture

  	Released under the GNU General Public License
*/
require_once(DIR_WS_FUNCTIONS . 'supplier.php');

class buyback_product {

    var $products_id;
    var $products_status = 0;           //From products table
    var $products_price = 0.00;         //From products table
    var $products_cat_path = '';         //From products table

    var $is_backorder = false;          //Flag. Whether this product is backorder.
    var $is_buyback = true;            //Flag. Whether "server full" (already have sufficient stock) or not.

    var $products_quantity_available = 0;    //Available Qty
    var $products_quantity_actual = 0;       //Actual Qty
    var $forecast_quantity_available = 0;    //FA1 - Forecasted Available Qty
    var $forecast_quantity_actual = 0;       //FA2 - Forecasted Actual Qty

    var $maximum_inventory_space = 0;           //MIS - Absolute maximum inventory we can keep. The ceiling.
    var $maximum_inventory_space_derived = 0;   //MIS - The raw maximum we derive from the range. Ie without subjecting to partial percentage, etc.
    var $remainder_inventory_space = 0;         //MIS - FA2. The absolute maximum we have left over

    var $min_buyback_qty_db = 0;        //MinQ - Minimum we accept per buyback order (buyback_products)
    var $min_buyback_qty = 0;           //MinQ - Minimum we accept per buyback order

    var $max_buyback_qty_db = 0;                //MaxQ - Maximum we accept per buyback order (buyback_products). For historical display only. Never use for logic cos only accurate to the point of last save().
    var $max_buyback_qty = 0;                   //MaxQ - To force recalculate, call calculate() before grabbing this value.
    var $max_buyback_qty_system_defined = 0;    //MaxQ - Maximum we accept per buyback order as 'suggested' by algorithm. Always calculated, never saved. Relevant or not, depends on settings.

    var $buyback_settings = array();        //Settings saved for this product. Either via batch at buyback_groups, or singularly set at buyback products.

    var $buyback_brackets = array();        //All brackets for this product.
    var $buyback_brackets_delete = array(); //bracket ids marked for deletion

    var $buyback_num_brackets = 0;          //count(brackets)
    var $buyback_bracket_active = array();  //The active bracket for this qty

    var $error_msg_arr = array();           //The complain queue for messageStack. Otherwise always assign messages as fatal.


    //Public Constructor
    function buyback_product($products_id) {
        $this->products_id = $products_id;
        $this->set_buyback_product_info();
        $this->set_brackets();
        $this->set_buyback_settings();
        $this->calculate();
    }

    /*****************************************************************************************************
     * Public methods -  Callable
     *****************************************************************************************************/

    //Set db values to class
    function set_buyback_product_info() {
        $min_buyback_qty_db = 0;
        $max_buyback_qty_db = 0;
        $products_status = 0;
        $products_price = 0;
        $products_quantity_available = 0;
        $products_quantity_actual = 0;

	    $get_min_max_sql = "SELECT buyback_products_minimum_value, buyback_products_status, buyback_products_maximum_value 
	    					FROM ".TABLE_BUYBACK_PRODUCTS." 
	    					WHERE products_id = '" . tep_db_input($this->products_id) . "'";
	    $get_min_max_result = tep_db_query($get_min_max_sql);
	    if ($get_min_max_row = tep_db_fetch_array($get_min_max_result)) {
	        $min_buyback_qty_db = $get_min_max_row['buyback_products_minimum_value'];
	        $max_buyback_qty_db = $get_min_max_row['buyback_products_maximum_value'];
	        $products_status = $get_min_max_row['buyback_products_status'];
	    }

        $product_qty_select_sql = "	SELECT products_cat_path, products_price, products_quantity, products_actual_quantity 
        							FROM " . TABLE_PRODUCTS . " 
        							WHERE products_id = '" . tep_db_input($this->products_id) . "'";
        $product_qty_result_sql = tep_db_query($product_qty_select_sql);
        if ($row = tep_db_fetch_array($product_qty_result_sql)) {
            $products_quantity_available = (int)$row['products_quantity'];
            $products_quantity_actual = (int)$row['products_actual_quantity'];
            $products_price = (float)$row['products_price'];
            $products_cat_path = $row['products_cat_path'];

        }

        //By default, we get default from db here, but overwrite when call calculate();
        $this->min_buyback_qty_db = $this->min_buyback_qty = $min_buyback_qty_db;
        $this->max_buyback_qty_db = $this->max_buyback_qty = $max_buyback_qty_db;

        $this->products_status = $products_status;
        $this->products_cat_path = $products_cat_path;
        $this->products_price = $products_price;
        $this->products_quantity_available = $products_quantity_available;
        $this->products_quantity_actual = $products_quantity_actual;
    }

    //Set custom values to class
    function assign_min_qty($min_qty) {
        $this->min_buyback_qty = $min_qty;
    }

    //Set custom values to class
    function assign_buyback_settings($settings_arr) {
        if (count($settings_arr)) {
            foreach ($settings_arr as $key => $value) {
                $this->buyback_settings[$key] = $value;
            }
        } else {
            $this->set_error(sprintf(MESSAGE_INVALID_SETTINGS_COUNT, $this->products_cat_path));
        }
    }

    //Add a bracket to this class. Can be used in calculate() to trigger errors, then saved in save() if no error.
    function set_add_edit_bracket($buyback_bracket_quantity, $buyback_bracket_value, $buyback_bracket_mode, $buyback_bracket_id=0) {
        $success = false;

        //Lets check if the bracket id already exists.
        $bracket_id_exists = false;
        if ((int)$buyback_bracket_id > 0) {
            //Ie. bracket was assigned to group, to be assigned to product. so bracket id is valid integer
            foreach ($this->buyback_brackets as $key => $bracket_arr) {
                if ((int)$bracket_arr['buyback_bracket_id'] == (int)$buyback_bracket_id) {
                    $bracket_id_exists = true;
                    //exists, so edit
                    $this->buyback_brackets[$key]['buyback_bracket_quantity'] = $buyback_bracket_quantity;
                    $this->buyback_brackets[$key]['buyback_bracket_value'] = $buyback_bracket_value;
                    $this->buyback_brackets[$key]['buyback_bracket_mode'] = $buyback_bracket_mode;
                    break;
                }
            }
        }
        if (!$bracket_id_exists) {
            //Not yet assigned to this product, so add it.
            $this->buyback_brackets[] = array('buyback_bracket_id' => $buyback_bracket_id,
                                                'buyback_bracket_quantity' => $buyback_bracket_quantity,
                                                'buyback_bracket_value' => $buyback_bracket_value,
                                                'buyback_bracket_mode' => $buyback_bracket_mode);
            $this->sort_brackets_by_quantity();
            $this->update_bracket_count();
            $success = true;
        }
        return $success;
    }

    //Delete a single/array of bracket(s) from this class. Can be used in calculate() to trigger errors, then saved in save() if no error.
    function set_delete_bracket($bracket_id) {
        $success = false;
        $delete_bracket_array = array();

        if (!$bracket_id) {
            //In case bracket id is zero (no auto increment id yet) or is empty array for whatever reason.
            return false;
        }
        if (!is_array($bracket_id)) {
            $delete_bracket_array[] = $bracket_id;
        } else {
            $delete_bracket_array = $bracket_id;
        }

        foreach ($delete_bracket_array as $del_bracket_id) {
            $this->buyback_brackets_delete[] = $del_bracket_id;
            foreach ($this->buyback_brackets as $key => $bracket_array) {
                //Pop the bracket out so it doesn't interfere with calculations
                if ($bracket_array['buyback_bracket_id'] == $del_bracket_id) {
                    unset($this->buyback_brackets[$key]);
                    $success = true;
                    break 1;
                }
            }
        }
        $this->sort_brackets_by_quantity();
        $this->update_bracket_count();
        return $success;
    }

    //Delete all the brackets loaded in this class
    function set_delete_bracket_all() {
        foreach ($this->buyback_brackets as $key => $bracket_array) {
            //Pop the bracket out so it doesn't interfere with calculations
            $this->buyback_brackets_delete[] = $bracket_array['buyback_bracket_id'];
            unset($this->buyback_brackets[$key]);
        }
        $this->update_bracket_count();
    }

    //Daily production by contracted farmers
    function get_daily_confirmed_production_qty() {
        $daily_confirmed_production_qty = 0;
        //This part to be defined when china supplier system is defined.
        //Is qty that farmers promise to deliver daily.
        return $daily_confirmed_production_qty;
    }

    //Call this after all changes to object vars ($this->assign_*) to recalculate.
    //Updates class with real-time values. Will not change db unless you call save() after calculate().
    function calculate() {
        $remainder_inventory_space = 0;

        //Build up error messages again. because new vars may fix the errors.
        $this->error_msg_arr = array();
        $this->set_maximum_inventory_space();
        $this->set_forecast_available_actual();

       	$remainder_inventory_space = $this->maximum_inventory_space - $this->forecast_quantity_actual;

        if ($this->is_buyback && $remainder_inventory_space <= 0) {
            //$remainder_inventory_space = 0;
            $this->is_buyback = false;
        }
        $this->remainder_inventory_space = $remainder_inventory_space;

        $this->set_max_buyback_qty(); //For each buyback order
    }

    //Save class data to db. Dont save if errors found (rollback) so we can use play with figures with impunity
    //and only save clean data.
    function save() {
        if (count($this->error_msg_arr) == 0) {

            //save buyback settings
            $buyback_products_setting_delete_sql = "DELETE FROM " . TABLE_BUYBACK_SETTING . " WHERE buyback_setting_reference_id = '" . tep_db_input($this->products_id) . "' 
                                                    AND buyback_setting_table_name='".TABLE_BUYBACK_PRODUCTS."' AND buyback_setting_key IN ('".implode("','", array_keys($this->buyback_settings))."')";
            $buyback_products_setting_result_sql = tep_db_query($buyback_products_setting_delete_sql);
            foreach ($this->buyback_settings as $key => $value) {
                tep_db_perform(TABLE_BUYBACK_SETTING, array('buyback_setting_reference_id' => $this->products_id, 'buyback_setting_table_name' => TABLE_BUYBACK_PRODUCTS, 'buyback_setting_key' => $key, 'buyback_setting_value' => tep_db_prepare_input($value)));
            }

            $buyback_info_delete_sql = "DELETE FROM ".TABLE_BUYBACK_PRODUCTS." WHERE products_id = '".(int)$this->products_id."'";
            tep_db_query($buyback_info_delete_sql);

            $buyback_product_data_array = array('products_id' => $this->products_id,
                                                'buyback_products_status' => $this->products_status,
                                                'buyback_products_minimum_value' => $this->min_buyback_qty,
                                                'buyback_products_maximum_value' => $this->max_buyback_qty);
            tep_db_perform(TABLE_BUYBACK_PRODUCTS, $buyback_product_data_array);

            //Brackets stuff starts here
            //First delete those marked for deletion
     		tep_db_query("DELETE FROM " . TABLE_BUYBACK . " where buyback_bracket_id IN ('" . implode("','", $this->buyback_brackets_delete) . "')");
    		tep_db_query("DELETE FROM " . TABLE_BUYBACK_BRACKET . " where buyback_bracket_id IN ('" . implode("','", $this->buyback_brackets_delete) . "')");

            //save buyback brackets
            for ($i=0; $i<$this->buyback_num_brackets; $i++) {
                //Update the bracket
                $bracket_id = (int)$this->buyback_brackets[$i]['buyback_bracket_id'];
                if ($bracket_id == 0) {
                    //Insert New Bracket
        		    tep_db_perform(TABLE_BUYBACK_BRACKET, $this->buyback_brackets[$i]);

    			    $bracket_id = tep_db_insert_id();
                    $this->buyback_brackets[$i]['buyback_bracket_id'] = $bracket_id;
    				$buyback_data_array = array('products_id' => $this->products_id,
    											'buyback_bracket_id' => $bracket_id);
    			    tep_db_perform(TABLE_BUYBACK, $buyback_data_array);
                } else {
                    //Update Bracket. Don't screw up the auto increment id.
        		    tep_db_perform(TABLE_BUYBACK_BRACKET, $this->buyback_brackets[$i], 'update', "buyback_bracket_id = '" . tep_db_input($bracket_id) . "'");
                }
            }

            //To update var - display only
            $this->max_buyback_qty_db = $this->max_buyback_qty;

            if ($this->min_buyback_qty > $this->max_buyback_qty) {
                $this->set_error(sprintf(MESSAGE_MAX_NOT_GREATER_THAN_MIN, $this->max_buyback_qty, $this->min_buyback_qty));
            }
        } else {
            $this->set_error(sprintf(MESSAGE_ERROR_SO_ABORT, $this->products_cat_path));
        }
    }

    function show_error_warning () {
        global $messageStack;
        foreach ($this->error_msg_arr as $msg) {
            $messageStack->add_session($msg);
        }
    }

    /*****************************************************************************************************
     * Private methods -  Not Callable
     *****************************************************************************************************/
    //Set db values to class
    function set_buyback_settings() {
        $buyback_settings = array();

		$buyback_products_setting_select_sql = "select buyback_setting_key, buyback_setting_value FROM ".TABLE_BUYBACK_SETTING." WHERE buyback_setting_reference_id = '" . (int)$this->products_id . "' AND buyback_setting_table_name = '" . TABLE_BUYBACK_PRODUCTS . "'";
		$buyback_products_setting_select_result = tep_db_query($buyback_products_setting_select_sql);
		while ($buyback_products_setting_row = tep_db_fetch_array($buyback_products_setting_select_result)) {
		    $buyback_settings[$buyback_products_setting_row['buyback_setting_key']] = $buyback_products_setting_row['buyback_setting_value'];
		}
		$this->buyback_settings = $buyback_settings;
    }

    //Set db values to class
    function set_brackets() {
        $buyback_brackets = array();
		$buyback_brackets_select_sql = "SELECT bb.*
        								FROM " . TABLE_BUYBACK . " AS b, " . TABLE_BUYBACK_BRACKET . " AS bb
        								WHERE b.products_id = '" . tep_db_input($this->products_id) . "'
        									AND b.buyback_bracket_id=bb.buyback_bracket_id
        								ORDER BY bb.buyback_bracket_quantity ASC;";

		$buyback_brackets_result_sql = tep_db_query($buyback_brackets_select_sql);
		while ($row = tep_db_fetch_array($buyback_brackets_result_sql)) {
		  $buyback_brackets[] = $row;
		}
		$this->buyback_brackets = $buyback_brackets;
        //$this->sort_brackets_by_quantity();//already done in sql
        $this->update_bracket_count();

        if (!$this->buyback_num_brackets) {
            //No brackets, so remove this record cos no point keeping.
            $buyback_info_delete_sql = "DELETE FROM ".TABLE_BUYBACK_PRODUCTS." WHERE products_id = '".(int)$this->products_id."'";
            tep_db_query($buyback_info_delete_sql);
        }
    }
    function update_bracket_count() {
        $this->buyback_num_brackets = count($this->buyback_brackets);
        if ($this->buyback_num_brackets == 0) {
            if ($this->is_buyback) {
                $this->is_buyback = false;
            }
            $this->set_error(MESSAGE_INVALID_BRACKET_COUNT);
        }
    }
    
    function sort_brackets_by_quantity() {
        foreach ($this->buyback_brackets as $key => $bracket_array) {
           $bracket_quantity_arr[$key]  = $bracket_array['buyback_bracket_quantity'];
        }
        array_multisort($bracket_quantity_arr, SORT_ASC, $this->buyback_brackets);
    }
	
    //Caculate MIS
    function set_maximum_inventory_space() {
        $maximum_inventory_space = 0;
        $maximum_inventory_space_derived = 0;
        $maximum_inventory_space_system_defined = 0;

		if ($this->is_backorder) {
			//Ignore buyback products settings if backorder. Fulfill backorder quantity first.
			//Adding Forecast quantity actual because; remainder_inventory space = MIS - Forecast quantity actual.
			//Remainder inventory space used in determining maxQ per transaction.
			$maximum_inventory_space_derived = abs($this->forecast_quantity_available) + $this->forecast_quantity_actual;
		} else {
			$maximum_inventory_space_derived = $this->get_inventory_space_in_range();
		}

		$maximum_inventory_space_derived -= $this->get_daily_confirmed_production_qty();

        if ((float)$this->buyback_settings['bprd_max_inv_space_percentage'] > 0) {
            $maximum_inventory_space_system_defined = (float)$maximum_inventory_space_derived * ((float)$this->buyback_settings['bprd_max_inv_space_percentage']/100);
        } else {
            //Rubbish percentage ? Ignore it cos we don't want zero division error.
            $maximum_inventory_space_system_defined = $maximum_inventory_space_derived;
        }

        //Find the greater between the two
        $maximum_inventory_space = max($this->buyback_settings['bprd_max_inv_space_overwrite'], $maximum_inventory_space_system_defined);

        if ($maximum_inventory_space < 0) {
            $this->set_error(sprintf(MESSAGE_INVALID_MAX_INV_SPACE, $this->products_cat_path));
        }
        $this->maximum_inventory_space = ceil($maximum_inventory_space);
        $this->maximum_inventory_space_derived = ceil($maximum_inventory_space_derived);
    }
    //Check user settings on get method
    function get_inventory_space_in_range() {
        //depending on db, do either of the following
        $inventory_space = 0;
        switch ($this->buyback_settings['bprd_sales_retrieval_method']) {
            case 'by_date_range':
                //If Sales end date is not saved, use today's date. So it calculates realtime everytime we need inventory space.
                //Will of course affect the average sales cos more days involved as time goes by.
                $end_date = (trim($this->buyback_settings['bprd_sales_end_date']) ? $this->buyback_settings['bprd_sales_end_date'] : date('Y-m-d'));
                if (tep_day_diff($this->buyback_settings['bprd_sales_start_date'], $end_date) > 0) {
                    $inventory_space = $this->get_inventory_space_by_date_range($this->buyback_settings['bprd_sales_start_date'], $end_date);
                } else {
                    $this->set_error(sprintf(MESSAGE_INVALID_START_END_DATE, $this->products_cat_path));
                }
                break;
            case 'by_last_n_days';
                $inventory_space = $this->get_inventory_space_by_num_days($this->buyback_settings['bprd_last_n_days_sales']);
                break;
        }
        return $inventory_space;
    }
    //For reuse and standardisation, we eventually call get_inventory_space_by_date_range()
    function get_inventory_space_by_num_days($num_days) {
        $start_date = '';
        $end_date = '';

        //Here end_date will always be midnite last nite (dont want to mess with partials of today).
        //We want to find out the start date based on num of days to count backwards.
        $gm_end_date  = mktime(24, 0, 0, date("m"), date("d")-1, date("Y"));
        $end_date = date('Y-m-d H:i:s', $gm_end_date);
        $gm_start_date = $gm_end_date - ((int)$num_days * 86400);
        $start_date = date('Y-m-d H:i:s', $gm_start_date);
        return $this->get_inventory_space_by_date_range($start_date, $end_date);
    }
    function get_inventory_space_by_date_range($start_date, $end_date) {
        $inventory_space_in_range = 0;
		if ($this->buyback_settings['bprd_inventory_days'] && $start_date && $end_date) {
    	    //Calculate now.
            $buyback_calculate_inventory_space_settings = array(
                                                            'ppls_inventory_days' => $this->buyback_settings['bprd_inventory_days'],
                                                            'ppls_sales_start_date' => $start_date,
                                                            'ppls_sales_end_date' => $end_date);
            $inventory_space_in_range = tep_get_suggested_max_purchase_qty($this->products_id, $buyback_calculate_inventory_space_settings);
		}
        return $inventory_space_in_range;
    }
	
    //Set db values to class
    function set_forecast_available_actual() {
        $forecast_quantity_available = 0;
        $forecast_quantity_actual = 0;

        $forecast_quantity_available = $this->products_quantity_available;
        $forecast_quantity_actual = $this->products_quantity_actual;

        $product_first_list_qty_select_sql = "SELECT sum(solp.products_quantity) as first_list_qty_total
                FROM " . TABLE_SUPPLIER_ORDER_LISTS . " AS sol
                INNER JOIN " . TABLE_SUPPLIER_ORDER_LISTS_PRODUCTS . " AS solp
                    ON sol.supplier_order_lists_id = solp.supplier_order_lists_id
                WHERE solp.products_id = '" . tep_db_input($this->products_id) . "'
                	AND sol.supplier_order_lists_status IN (1, 5)
                	AND IF(sol.supplier_order_lists_status=1, solp.supplier_order_lists_type='2' AND solp.products_received_quantity IS NULL, solp.supplier_order_lists_type='1')";
        $product_first_list_qty_result_sql = tep_db_query($product_first_list_qty_select_sql);
        if (($row2 = tep_db_fetch_array($product_first_list_qty_result_sql)) && ($first_list_qty_total = (int)$row2['first_list_qty_total'])) {
            $forecast_quantity_available += $first_list_qty_total;
            $forecast_quantity_actual += $first_list_qty_total;
        }

        if ($forecast_quantity_available < 0) {
            $this->is_backorder = true;
            $this->is_buyback = true;
        }

        $this->forecast_quantity_available = $forecast_quantity_available;
        $this->forecast_quantity_actual = $forecast_quantity_actual;
    }

    //Validate the max qty, change where necessary
    function set_max_buyback_qty() {
        $active_bracket_arr = array();
        $max_buyback_qty_system_defined = 0;
        $active_bracket_key = 0;
        $max_buyback_qty = 0;

//Commenting this off so we can get and save Max qty even though server is full.
//Otherwise, we get showstopper Max is zero and not greater than Min.
//        if ($this->is_buyback) {
            if ($this->is_backorder) {
                //Demand greater than supply. Fulfill demand first. Don't worry about other rules.
                $max_buyback_qty_system_defined = abs($this->forecast_quantity_available);

                //just grab the smallest bracket
                $active_bracket_arr['quantity'] = (int)$this->buyback_brackets[0]['buyback_bracket_quantity'];
                if ((int)$this->buyback_brackets[0]['buyback_bracket_mode'] == 0 ) {
                    //value == $
                    $active_bracket_arr['price'] = (float)$this->buyback_brackets[0]['buyback_bracket_value'];
                } else {
                    //value == %
                    $active_bracket_arr['price'] = number_format(((float)$this->buyback_brackets[0]['buyback_bracket_value'] * (float)$this->products_price)/100, 6);
                }

            } else {
                //Not backorder

                //Locate the active bracket that is relevant for forecast_actual_quantity.
                //Bracket also needed to determine max quantity per transaction. We don't want a transaction to span more than 1 bracket per
                //  transaction (price complication) so we use bracket qty as a per-transaction limit. Transactions blocked once MIS is filled.

                //Brackets already sorted by bracket qty ascending
                foreach ($this->buyback_brackets as $bracket_key => $bracket_arr) {
                    if ((int)$bracket_arr['buyback_bracket_quantity'] >= (int)$this->forecast_quantity_actual) {
                        $active_bracket_key = $bracket_key;
                        break;
                    }
                }
                if ($active_bracket_key == 0) {
                    //Looks like the FA2 is greater than all brackets. Lets just grab the largest bracket so we have something.
                    $active_bracket_key = (int)$this->buyback_num_brackets - 1;
                }

                //Its possible that only one bracket exist with qty 0. We'll use that bracket anyway. MIS will be the max limit in this case.

                $active_bracket_arr['quantity'] = (int)$this->buyback_brackets[$active_bracket_key]['buyback_bracket_quantity'];
                //Bracket needed to determine price.
                if ((int)$this->buyback_brackets[$active_bracket_key]['buyback_bracket_mode'] == 0 ) {
                    //value == $
                    $active_bracket_arr['price'] = (float)$this->buyback_brackets[$active_bracket_key]['buyback_bracket_value'];
                } else {
                    //value == %
                    $active_bracket_arr['price'] = number_format(((float)$this->buyback_brackets[$active_bracket_key]['buyback_bracket_value'] * (float)$this->products_price)/100, 6);
                }

        		if ($active_bracket_arr['quantity'] <= (int)$this->forecast_quantity_actual) {
        			//Also caters for bracket qty equals zero
        			$max_buyback_qty_system_defined = $this->maximum_inventory_space - (int)$this->forecast_quantity_actual;

        		} elseif ($active_bracket_arr['quantity'] > (int)$this->forecast_quantity_actual) {
                    //If MIS is greater than bracket qty, we use the bracket qty as a limit.
                    //We don't want a transaction to span more than 1 bracket per transaction (price complication) so we use bracket qty as a per-transaction limit.
                    //This means that we fill brackets one by one until reach MIS.
        			$max_buyback_qty_system_defined = min($this->maximum_inventory_space, $active_bracket_arr['quantity']) - (int)$this->forecast_quantity_actual;
        		}

            } //end not backorder

             //Can't be more than remainder inventory space. So just overwrite with the smaller.
            $max_buyback_qty_system_defined = min($max_buyback_qty_system_defined, $this->remainder_inventory_space);

            //Can't be less than the minimum. The minimum imposes the final ceiling. Cos qty may be less than worth the effort.
            //This is more important than making sure MIS not exceeded.
            //So just overwrite with the larger.
            //Its ok if min == max. Ie. Min=500/Max=500
            $max_buyback_qty_system_defined = max($max_buyback_qty_system_defined, (int)$this->min_buyback_qty);

            if ($this->buyback_settings['bprd_max_qty_source']=='overwrite') {
                $max_buyback_qty = (int)$this->buyback_settings['bprd_max_qty_overwrite'];
                //Now we apply the same basic rules to check the custom overwrite value.
                $max_buyback_qty = min($max_buyback_qty, $this->remainder_inventory_space);
                $max_buyback_qty = max($max_buyback_qty, (int)$this->min_buyback_qty);
                $this->buyback_settings['bprd_max_qty_overwrite'] = $max_buyback_qty;

            } else {
                //No overwrite detected.
                $max_buyback_qty = $max_buyback_qty_system_defined;
            }

//        }//end is buyback

        $this->max_buyback_qty = $max_buyback_qty;
        $this->buyback_bracket_active = $active_bracket_arr;
        $this->max_buyback_qty_system_defined = $max_buyback_qty_system_defined;
    }

    function set_error($msg, $fatal=false) {
        $this->error_msg_arr[] = $msg;
        if ($fatal) {
            $this->show_error_warning();
        }
    }
}
?>