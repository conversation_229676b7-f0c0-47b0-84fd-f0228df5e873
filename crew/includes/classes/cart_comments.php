<?
/*
	$Id: cart_comments.php,v 1.14 2007/02/27 08:03:45 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2005 SKC Ventrue
	
  	Released under the GNU General Public License
*/

class cart_comments {
	var $comments = '';
	
	function cart_comments($comments="")
	{
		$this->comments = $comments;
	}
	
	function construct_comments($type) {
		global $HTTP_POST_VARS;
		$a = 0;
		
		$cart_error_message = '';
		
		$active_comments_select_sql = "	SELECT cart_comments_id, cart_comments_title, cart_comments_input_type, cart_comments_required 
										FROM " . TABLE_CART_COMMENTS . "
										WHERE cart_comments_status=1 AND cart_comments_type = $type
										ORDER BY cart_comments_sort_order";
		$active_comments_result_sql = tep_db_query($active_comments_select_sql);
		while ($active_comments_row = tep_db_fetch_array($active_comments_result_sql)) {
			$comment_id = "comment_".$active_comments_row["cart_comments_id"];
			if ($active_comments_row["cart_comments_required"] && !$this->validate_required_info($active_comments_row["cart_comments_id"], $HTTP_POST_VARS[$comment_id], $active_comments_row["cart_comments_input_type"])) {
				if ($type == 2) {
					$cart_error_message = "Please filled in required comments about your order!";
				} else if($type == 1) {
					$cart_error_message = "Please filled in required comments in the registration form!";
				}
			}
			if (isset($HTTP_POST_VARS[$comment_id])) {
				if (tep_not_null($HTTP_POST_VARS[$comment_id])) {
					$this->comments .= "##ID_".$active_comments_row["cart_comments_id"]."##".tep_db_prepare_input($HTTP_POST_VARS[$comment_id])."##COMMENT##";
		 		}
			}
		}
		
		if (tep_not_null($cart_error_message)) {
			return false;
		} else {
			return true;
		}
	}
	
	function individual_construct_comments($type) {
		global $HTTP_POST_VARS;
		$a = 0;
		
		$cart_error_message = '';
		
		$active_comments_select_sql = "	SELECT cart_comments_id, cart_comments_title, cart_comments_input_type, cart_comments_required 
										FROM " . TABLE_CART_COMMENTS . "
										WHERE cart_comments_status=1 AND cart_comments_type = $type
										ORDER BY cart_comments_sort_order";
		$active_comments_result_sql = tep_db_query($active_comments_select_sql);
		while ($active_comments_row = tep_db_fetch_array($active_comments_result_sql)) {
			$comment_id = "comment_".$active_comments_row["cart_comments_id"];
			if ($active_comments_row["cart_comments_required"] && !$this->validate_required_info($active_comments_row["cart_comments_id"], $HTTP_POST_VARS[$comment_id], $active_comments_row["cart_comments_input_type"])) {
				if ($type == 2){
					$cart_error_message = "Please filled in required comments about your order!";
				} else if($type == 1){
					$cart_error_message = "Please filled in required comments in the registration form!";
				}
			}
			
			if (isset($HTTP_POST_VARS[$comment_id])) {
				if (tep_not_null($HTTP_POST_VARS[$comment_id])) {
					$this->comments .= "##ID_".$active_comments_row["cart_comments_id"]."##".tep_db_prepare_input($HTTP_POST_VARS[$comment_id])."##COMMENT##";
				}
			}
		}
		
		if (tep_not_null($cart_error_message))
			return false;
		else
			return true;
	}
	
	function validate_required_info($comment_id, $msg, $type) {
		$msg = tep_db_prepare_input($msg);
		switch($type) {
			case "1":
			case "2":
			case "3":
				$comments_select_sql = "	SELECT cart_comments_option_title, cart_comments_options 
											FROM " . TABLE_CART_COMMENTS . "
											WHERE cart_comments_id='".$comment_id."'";
				$comments_result_sql = tep_db_query($comments_select_sql);
				if ($comments_row = tep_db_fetch_array($comments_result_sql)) {
					if ($comments_row["cart_comments_option_title"] == "1") {
						if (tep_not_null($comments_row["cart_comments_options"])) {
							$option_list = explode(':~:', $comments_row["cart_comments_options"]);
							array_shift($option_list);
							if (in_array($msg, $option_list)) {
								return true;
							} else {
								return false;
							}
						} else {
							return true;	// no selection at the moment
						}
					} else {
						if (tep_not_null($msg)) {
							return true;
						} else {
							return false;
						}
					}
				} else {
					return false;
				}
				break;
		}
	}
	
	function verify_mandatory_info_provided($custom_comment, $type) {
		if (!tep_not_null($custom_comment)) {
			$custom_comment = $this->comments;
		}
		
		$active_comments_select_sql = "	SELECT cart_comments_id, cart_comments_title, cart_comments_input_type 
										FROM " . TABLE_CART_COMMENTS . "
										WHERE cart_comments_required=1 
											AND cart_comments_status=1 
											AND cart_comments_type = $type 
										ORDER BY cart_comments_sort_order";
		$active_comments_result_sql = tep_db_query($active_comments_select_sql);
		while ($active_comments_row = tep_db_fetch_array($active_comments_result_sql)) {
			$comment_id = $active_comments_row["cart_comments_id"];
			$title = $active_comments_row["cart_comments_title"] . (substr(trim($active_comments_row["cart_comments_title"]), -1) == ':' ? ' ' : ': ');
			$Pattern = "/##ID_".$comment_id."##([^#]+)(##COMMENT##)/is";
			if (!preg_match($Pattern, $custom_comment, $regs)) {
				return false;
			}
		}
		
		return true;
	}
	
	function get_individual_plain_comments($custom_comment, $type) {
		if (!tep_not_null($custom_comment)) {
		}
		
		$active_comments_select_sql = "	SELECT cart_comments_id, cart_comments_title, cart_comments_input_type 
										FROM " . TABLE_CART_COMMENTS . "
										WHERE cart_comments_status=1 AND cart_comments_type = $type
										ORDER BY cart_comments_sort_order";
		$active_comments_result_sql = tep_db_query($active_comments_select_sql);
		while ($active_comments_row = tep_db_fetch_array($active_comments_result_sql)) {
			$comment_id = $active_comments_row["cart_comments_id"];
			$title = $active_comments_row["cart_comments_title"] . (substr(trim($active_comments_row["cart_comments_title"]), -1) == ':' ? ' ' : ': ');
		//	$Pattern = "/^##ID_".$comment_id."/";
			$Pattern = "/##ID_".$comment_id."##([^#]+)(##COMMENT##)/is";
			if(preg_match($Pattern, $custom_comment, $regs)) {
				$individual_comments[] = array	("id" => $comment_id, "text" => $regs[1], "title" => $title);
			} else {
				$individual_comments[] = array	("id" => $comment_id, "text" => "", "title" => $title);
			}
		}
		return $individual_comments;
	}
	
	function get_plain_comments($custom_comment, $type) {
		if (!tep_not_null($custom_comment)) {
			$custom_comment = $this->comments;
		}
		
		$active_comments_select_sql = "	SELECT cart_comments_id, cart_comments_title, cart_comments_input_type 
										FROM " . TABLE_CART_COMMENTS . "
										WHERE cart_comments_status=1 AND cart_comments_type = $type
										ORDER BY cart_comments_sort_order";
		$active_comments_result_sql = tep_db_query($active_comments_select_sql);
		while ($active_comments_row = tep_db_fetch_array($active_comments_result_sql)) {
			$comment_id = $active_comments_row["cart_comments_id"];
			$title = preg_replace("/(<!--X_SAVE-->)(.*?)(<!--END-->)/is", '', $active_comments_row["cart_comments_title"]);	// Strip off unwanted text
			$title .= substr(trim($title), -1) == ':' ? ' ' : ': ';
			
			$Pattern = "/##ID_".$comment_id."##([^#]+)(##COMMENT##)/is";
			if (preg_match($Pattern, $custom_comment, $regs)) {
				if ($active_comments_row["cart_comments_input_type"] == "2") {
					$custom_comment = preg_replace($Pattern, $title."\n\$1\n", $custom_comment);
				} else {
					$custom_comment = preg_replace($Pattern, $title."\$1\n", $custom_comment);
				}
			}
		}
		
		while(substr($custom_comment, -1) == "\n") {
			$custom_comment = substr($custom_comment, 0, -1);
		}	
		return $custom_comment;
	}
	
	function display_comments_fields($custom_comment='', $type) {
		global $comments;
		$required_fields = array();
		
		echo '	<table border="0" cellspacing="0" cellpadding="2">';
		
		$active_comments_select_sql = "	SELECT cart_comments_id, cart_comments_title, cart_comments_input_type, cart_comments_input_size, cart_comments_options, cart_comments_option_title, cart_comments_required, cart_comments_type 
										FROM " . TABLE_CART_COMMENTS . "
										WHERE cart_comments_status=1 AND cart_comments_type = $type
										ORDER BY cart_comments_sort_order";
		$active_comments_result_sql = tep_db_query($active_comments_select_sql);
		while ($active_comments_row = tep_db_fetch_array($active_comments_result_sql)) {
			$input_str = "";
			$input_id = "comment_".$active_comments_row["cart_comments_id"];
			$input_title = $active_comments_row["cart_comments_title"];
			$required = $active_comments_row["cart_comments_required"];
			
			$Pattern = "/##ID_".$active_comments_row["cart_comments_id"]."##([^#]+)(##COMMENT##)/is";
			if(preg_match($Pattern, $custom_comment, $regs)) {
				$default_value = $regs[1];
			} else {
				$default_value = "";
			}
			
			switch ($active_comments_row["cart_comments_input_type"]) {
				case "1":	// text box
					if ($required == "1") {
						$required_fields[$input_id] = array("type" => "1", "title" => $input_title);
					}
					
					if (tep_not_null($active_comments_row["cart_comments_input_size"])) {
						list($size, $max_len) = explode(',', $active_comments_row["cart_comments_input_size"]);
					}
					$param = ' id="'.$input_id.'" SIZE=' . ($size > 0 ? $size : '20') . (isset($max_len) && $max_len > 0 ? " MAXLENGTH=".$max_len : '');
					
					$input_str = '	<tr>
										<td >'.tep_draw_separator('pixel_trans.gif', '30', '1').'</td>
										<td class="inputLabel">' . $input_title . '</td>
										<td valign="top">&nbsp;</td>';
									
					$input_str .= '	</tr>';
					
					$input_str .= '	<tr>
										<td >'.tep_draw_separator('pixel_trans.gif', '30', '1').'</td>
										<td class="inputField">' . tep_draw_input_field($input_id, $default_value, $param) . ($required ? TEXT_FIELD_REQUIRED : '&nbsp;') . '</td>
										<td valign="top">&nbsp;</td>';								
					$input_str .= '	</tr><tr><td colspan="3">'.tep_draw_separator('pixel_trans.gif', '1', '15').'</td></tr>';					
					
					break;
				case "2":	// text area
					if ($required == "1") {
						$required_fields[$input_id] = array("type" => "2", "title" => $input_title);
					}
					
					if (tep_not_null($active_comments_row["cart_comments_input_size"])) {
						list($row_val, $col_val) = explode(',', $active_comments_row["cart_comments_input_size"]);
					}
					$input_str = '	<tr>
										<td>'.tep_draw_separator('pixel_trans.gif', '30', '1').'</td>
										<td class="inputLabel" valign="top">' . $input_title . '</td>
										<td>'.tep_draw_separator('pixel_trans.gif', '1', '15').'</td>';
					$input_str .= '	</tr>';
					
					$input_str .= '	<tr>
										<td>'.tep_draw_separator('pixel_trans.gif', '30', '1').'</td>
										<td class="inputField">' . tep_draw_textarea_field($input_id, "soft", $col_val, $row_val, $default_value, 'id="'.$input_id.'"', false) . '</td>
										<td valign="top">'. ($required ? TEXT_FIELD_REQUIRED : '&nbsp;') . '</td>';
					$input_str .= '	</tr><tr><td colspan="3">'.tep_draw_separator('pixel_trans.gif', '1', '15').'</td></tr>';					
					
					break;
				case "3":	// selection box
					if ($required == "1") {
						$required_fields[$input_id] = array("type" => "3", "title" => $input_title, "option_title" => $active_comments_row["cart_comments_option_title"]);
					}
					
					$option_array = array();
					if (tep_not_null($active_comments_row["cart_comments_options"])) {
						$option_list = explode(':~:', $active_comments_row["cart_comments_options"]);
						foreach ($option_list as $val) {
							$option_array[] = array('id' => $val, 'text' => $val);
						}
					}
					
					$input_str = '	<tr>
										<td>'.tep_draw_separator('pixel_trans.gif', '30', '1').'</td>
										<td class="inputLabel" valign="top">' . $input_title . '</td>
										<td valign="top">'.tep_draw_separator('pixel_trans.gif', '1', '15').'</td>';
					$input_str .= '	</tr>';

					$input_str .= '	<tr>
										<td>'.tep_draw_separator('pixel_trans.gif', '30', '1').'</td>
										<td class="inputField">' . tep_draw_pull_down_menu($input_id, $option_array, $default_value, 'id="'.$input_id.'"', $required ? true : false) . '</td>
										<td valign="top">&nbsp;</td>';
					$input_str .= '	</tr><tr><td colspan="3">'.tep_draw_separator('pixel_trans.gif', '1', '15').'</td></tr>';					
					
					break;
			}
			echo $input_str;
		}
		echo '	</table>';
		
		echo '<script type="text/javascript" language="javascript">'."\n";
		echo '	function comments_validation() {'."\n";
		echo '		var error_message = ""'."\n";
		foreach ($required_fields as $id => $info) {
			switch ($info["type"]) {
				case "1":
				case "2":
					echo	'		if (Trim(document.getElementById(\''.$id.'\').value) == "") {'."\n".
							'			error_message += \'* Please provide \"'.$info["title"].'\" infomation!\'+"\n";'."\n".
							'			//document.getElementById(\''.$id.'\').value="";'."\n".
							'			//document.getElementById(\''.$id.'\').focus();'."\n".
							'			//return error_message;'."\n".
							'		}'."\n";
					break;
				case "3":
					if ($info["option_title"]) {
						echo	'		if (document.getElementById(\''.$id.'\').selectedIndex < 1) {'."\n".
								'			error_message += \'* Please provide \"'.$info["title"].'\" infomation!\'+"\n";'."\n".
								'			//document.getElementById(\''.$id.'\').focus();'."\n".
								'			//return false;'."\n".
								'		}'."\n";
					}
					break;
			}
		}
		echo '		return error_message;'."\n".
			 '	}'."\n";
		echo '</script>';
	}
	
	function count_active_comments($type) {
		$active_comments_select_sql = "	SELECT cart_comments_id 
										FROM " . TABLE_CART_COMMENTS . "
										WHERE cart_comments_status=1 AND cart_comments_type = $type";
		$active_comments_result_sql = tep_db_query($active_comments_select_sql);
		if (tep_db_num_rows($active_comments_result_sql)) {
			return true;
		} else {
			return false;
		}
	}
}
?>