<?

class category {
    var $category_id;
	
	// class constructor
    function category($cid) {
      	$this->category_id = $cid;
	}
	
	// reupdate the categories_parent_path
	function set_categories_parent_path() {
		$parent_path = ($this->category_id > 0 ? '_' . tep_get_particular_cat_path($this->category_id) . '_' : '_');
		
		$this->_update_parent_path($this->category_id, $parent_path);
	}
	
	function _update_parent_path($parent_id, $path) {
		$subcategory_select_sql = "	SELECT categories_id
									FROM categories
									WHERE parent_id = '".tep_db_input($parent_id)."'";
		$subcategory_result_sql = tep_db_query($subcategory_select_sql);
	    while($subcategory_row = tep_db_fetch_array($subcategory_result_sql)) {
	    	$new_path = $path . ($parent_id > 0 ? $parent_id . '_' : '');
	    	
	    	$path_update_sql = "UPDATE categories
	    						SET categories_parent_path = '".tep_db_input(($parent_id > 0 ? $new_path : ''))."'
	    						WHERE categories_id='".tep_db_input($subcategory_row['categories_id'])."'";
	    	tep_db_query($path_update_sql);
	    	
	    	$this->_update_parent_path($subcategory_row['categories_id'], $new_path);
	    }
	}
	
	function get_available_product_type() {
		global $memcache_obj;
    	
		$cache_key = TABLE_CATEGORIES_PRODUCT_TYPES . '/custom_products_type_id/array/categories_id/' . (int)$this->category_id;
    	$cache_result = $memcache_obj->fetch($cache_key);
		if ($cache_result !== FALSE) {
			$available_category_product_type_array = $cache_result;
		} else {
			$available_category_product_type_array = array();
			$categories_product_types_select_sql = "SELECT DISTINCT custom_products_type_id 
													FROM ".TABLE_CATEGORIES_PRODUCT_TYPES."
													WHERE categories_id = '". (int)$this->category_id ."'
													ORDER BY custom_products_type_id";
			$categories_product_types_result_sql = tep_db_query($categories_product_types_select_sql);
			while($categories_product_types_row = tep_db_fetch_array($categories_product_types_result_sql)){
				$available_category_product_type_array[] = $categories_product_types_row['custom_products_type_id'];
			}
			$memcache_obj->store($cache_key, $available_category_product_type_array, 86400); // cache 24 hours
		}
		return $available_category_product_type_array;
	}
	
	function get_available_product_child_type() {
		global $memcache_obj;
    	$available_category_product_type_child_array = array();
    	
		$cache_key = TABLE_CATEGORIES_PRODUCT_TYPES . '/custom_products_type_child_id/array/categories_id/' . (int)$this->category_id;
    	$cache_result = $memcache_obj->fetch($cache_key);
    	
		if ($cache_result !== FALSE) {
			$available_category_product_type_child_array = $cache_result;
		} else {
			$categories_product_types_select_sql = "	SELECT custom_products_type_child_id 
														FROM ".TABLE_CATEGORIES_PRODUCT_TYPES."
														WHERE categories_id = '". (int)$this->category_id ."'
														ORDER BY custom_products_type_child_id";
			$categories_product_types_result_sql = tep_db_query($categories_product_types_select_sql);
			while($categories_product_types_row = tep_db_fetch_array($categories_product_types_result_sql)){
				$available_category_product_type_child_array[] = $categories_product_types_row['custom_products_type_child_id'];
			}
			$memcache_obj->store($cache_key, $available_category_product_type_child_array, 600); // cache 10 minutes
		}
		
		return $available_category_product_type_child_array;
	}
	
	function get_child_categories() {
		$categories_select_sql = "	SELECT categories_id 
									FROM " . TABLE_CATEGORIES .  "
									WHERE categories_parent_path REGEXP ('_".$this->category_id."_')";
		$categories_result_sql = tep_db_query($categories_select_sql);
		$categories_selected_array = array();
		while ($categories_row = tep_db_fetch_array($categories_result_sql)) {
			$categories_selected_array[] = $categories_row['categories_id'];
		}
		return $categories_selected_array;
	}
	
	function category_has_product_type($product_type_id, $customer_group_id=0) {
		$categeries_select_sql = "	SELECT c.categories_parent_path, c.parent_id 
									FROM " . TABLE_CATEGORIES . " AS c ";
		if ($customer_group_id) {
			$categeries_select_sql .= "	INNER JOIN " . TABLE_CATEGORIES_GROUPS . " as cg
											ON cg.categories_id = c.categories_id
												AND cg.groups_id IN ('0', '".$customer_group_id."')";
		}
		$categeries_select_sql .= "	WHERE c.categories_id = '".(int)$this->category_id."'
										AND c.categories_status = '1'";
		$categeries_result_sql = tep_db_query($categeries_select_sql);
		
		if ($categeries_row = tep_db_fetch_array($categeries_result_sql)) {
			if ($categeries_row['parent_id'] == 0) {
				$category_id = $this->category_id;
			} else {
				$cat_parent_path = explode('_',$categeries_row['categories_parent_path']);
				$category_id = $cat_parent_path[1];
			}
			
			$category_has_product_type_select_sql = "	SELECT custom_products_type_id 
														FROM ".TABLE_CATEGORIES_PRODUCT_TYPES."
														WHERE categories_id = '".(int)$category_id."'
															AND custom_products_type_id='".(int)$product_type_id."'";
			$category_has_product_type_result_sql = tep_db_query($category_has_product_type_select_sql);
			return (tep_db_num_rows($category_has_product_type_result_sql)>0 ? true : false );
		}
		
		return false;
	}
	
	function get_categories_service($categories_id, $language_id, $type = '') {
		$categories_services_array = array();
		
		$categories_services_select_sql = "	SELECT categories_services_name, categories_services_url, type 
											FROM " . TABLE_CATEGORIES_SERVICES . " 
											WHERE categories_id = '" . (int)$categories_id . "' 
												AND language_id = '" . (int)$language_id . "'";
		
		//echo $categories_services_select_sql;
		
		if (tep_not_null($type)) {
			$categories_services_select_sql .= " AND type = '" . (int)$type . "'";
		}
		
		$categories_services_result_sql = tep_db_query($categories_services_select_sql);
		while($categories_services_row = tep_db_fetch_array($categories_services_result_sql)) {
			$categories_services_array[$categories_services_row['type']] = array(	'name' => $categories_services_row['categories_services_name'],
																					'url' => $categories_services_row['categories_services_url']
																				);
		}
		
		return $categories_services_array;
	}
	
	
	// retrieve all game based on geo_zone_id
	function get_game_product_type_cat_info($support_product_type_array) {
		global $zone_info_array, $customers_groups_id, $languages_id, $memcache_obj;
		
		$game_available = array();
		$game_sorting = array();
/*-- OGM 20100706 :: temporarily turn off caching --*
		$cache_key = TABLE_CATEGORIES . '/games/array/geo_zone_id/' . $zone_info_array[1]->zone_id . '/groups_id/' . $customers_groups_id . '/language/' . $languages_id;
		$cache_result = $memcache_obj->fetch($cache_key);
		if ($cache_result !== FALSE) {
			$game_available = $cache_result;
		} else {
/*-- OGM 20100706 :: temporarily turn off caching --*/
			$category_where_statement = " c.categories_id IN ('" . implode("', '", $zone_info_array[1]->zone_categories_id) . "') ";
			
			$categories_select_sql = " 	SELECT DISTINCT c.categories_id, c.parent_id, c.categories_parent_path 
										FROM " . TABLE_CATEGORIES . " AS c 
										INNER JOIN " . TABLE_CATEGORIES_GROUPS . " AS cg 
											ON (c.categories_id = cg.categories_id) 
										WHERE c.categories_status = 1 
											AND ((cg.groups_id = '" . $customers_groups_id . "') OR (cg.groups_id = 0)) 
											AND " . $category_where_statement . "
										ORDER BY c.sort_order";
			$categories_result_sql = tep_db_query($categories_select_sql);
			while ($categories_row = tep_db_fetch_array($categories_result_sql)) {
				if ($categories_row['parent_id'] > 0) {
					$cur_cat_path_array = explode('_', substr($categories_row['categories_parent_path'], 1, -1));
					$current_game_category_id = $cur_cat_path_array[0];
					$games_categories_path = substr($categories_row['categories_parent_path'], 1) . $categories_row['categories_id'];
				} else {
					$current_game_category_id = $categories_row['categories_id'];
					$games_categories_path = $categories_row['categories_id'];
				}
						
				//select needed categories_product_types
				if (tep_not_null($current_game_category_id)) {
					$this->category_id = tep_db_input($current_game_category_id);
					$categories_product_types_array = $this->get_available_product_type();
					
					if (is_array($categories_product_types_array) && is_array($support_product_type_array)) {
						if (count(array_intersect($categories_product_types_array, $support_product_type_array)) > 0) {
							$def_cat_name = tep_get_categories_name($categories_row['categories_id'], $languages_id);
							$pin_yin_name = tep_get_categories_pin_yin($categories_row['categories_id'], $languages_id);
							
							$game_available[] = array ( 'cPath' => $games_categories_path,
														'name' => $def_cat_name);
							$game_sorting[] = strtolower($pin_yin_name);
						}
					}
				}
//				$memcache_obj->store($cache_key, $game_available, 10800); // cache 3 hours
			}
			
			asort($game_sorting);
			reset($game_sorting);
			
			$game_available['sorting'] = array_keys($game_sorting);
			unset($game_sorting);
//		}
		
		return $game_available;
	}
	
//	function get_game_product_type_cat_info($support_product_child_type_array) {
//		global $zone_info_array, $customers_groups_id, $languages_id, $memcache_obj;
//		
//		$game_available = array();
//		$game_sorting = array();
///*-- OGM 20100706 :: temporarily turn off caching --*
//		$cache_key = TABLE_CATEGORIES . '/games/array/geo_zone_id/' . $zone_info_array[1]->zone_id . '/groups_id/' . $customers_groups_id . '/language/' . $languages_id;
//		$cache_result = $memcache_obj->fetch($cache_key);
//		if ($cache_result !== FALSE) {
//			$game_available = $cache_result;
//		} else {
///*-- OGM 20100706 :: temporarily turn off caching --*/
//			$category_where_statement = " c.categories_id IN ('" . implode("', '", $zone_info_array[1]->zone_categories_id) . "') ";
//			
//			$categories_select_sql = " 	SELECT DISTINCT c.categories_id, c.parent_id, c.categories_parent_path 
//										FROM " . TABLE_CATEGORIES . " AS c 
//										INNER JOIN " . TABLE_CATEGORIES_GROUPS . " AS cg 
//											ON (c.categories_id = cg.categories_id) 
//										WHERE c.categories_status = 1 
//											AND ((cg.groups_id = '" . $customers_groups_id . "') OR (cg.groups_id = 0)) 
//											AND " . $category_where_statement . "
//										ORDER BY c.sort_order";
//			$categories_result_sql = tep_db_query($categories_select_sql);
//			while ($categories_row = tep_db_fetch_array($categories_result_sql)) {
//				if ($categories_row['parent_id'] > 0) {
//					$cur_cat_path_array = explode('_', substr($categories_row['categories_parent_path'], 1, -1));
//					$current_game_category_id = $cur_cat_path_array[0];
//					$games_categories_path = substr($categories_row['categories_parent_path'], 1) . $categories_row['categories_id'];
//				} else {
//					$current_game_category_id = $categories_row['categories_id'];
//					$games_categories_path = $categories_row['categories_id'];
//				}
//						
//				//select needed categories_product_types
//				if (tep_not_null($current_game_category_id)) {
//					$this->category_id = tep_db_input($current_game_category_id);
//					$categories_product_child_types_array = $this->get_available_product_child_type();
//					
//					if (is_array($categories_product_child_types_array) && is_array($support_product_child_type_array)) {
//						if (count(array_intersect($categories_product_child_types_array, $support_product_child_type_array)) > 0) {
//							$def_cat_name = tep_get_categories_name($categories_row['categories_id'], $languages_id);
//							$pin_yin_name = tep_get_categories_pin_yin($categories_row['categories_id'], $languages_id);
//							
//							$game_available[] = array ( 'cPath' => $games_categories_path,
//														'name' => $def_cat_name);
//							$game_sorting[] = strtolower($pin_yin_name);
//						}
//					}
//				}
////				$memcache_obj->store($cache_key, $game_available, 10800); // cache 3 hours
//			}
//			
//			asort($game_sorting);
//			reset($game_sorting);
//			
//			$game_available['sorting'] = array_keys($game_sorting);
//			unset($game_sorting);
////		}
//		
//		return $game_available;
//	}
	
	
	function get_categories_name_list() {
		global $customers_groups_id, $languages_id;
		
		$category_array = array();
		
		$cat_info_select_sql = "SELECT c.categories_id 
								FROM " . TABLE_CATEGORIES . " AS c 
								INNER JOIN " . TABLE_CATEGORIES_GROUPS . " AS cg 
									ON (cg.categories_id = c.categories_id) 
								WHERE c.parent_id = '" . $this->category_id . "' 
									AND c.categories_status = 1 
									AND ((cg.groups_id = '" . (int)$customers_groups_id. "') OR (cg.groups_id = 0)) 
								ORDER BY c.sort_order";
		$cat_info_result_sql = tep_db_query($cat_info_select_sql);
		if (tep_db_num_rows($cat_info_result_sql) > 0) {
			while ($cat_info_row = tep_db_fetch_array($cat_info_result_sql)) {
				$category_array[] = array(	'id' => $cat_info_row['categories_id'], 
											'name' => tep_get_categories_name($cat_info_row['categories_id'], $languages_id));
			}
		}
		
		return $category_array;
	}
	
	function get_product_child_type_category($pass_path, $pass_product_child_types_array) {
		global $memcache_obj;
		
		$categories_parent_path_array = array();
		foreach ($pass_product_child_types_array as $product_child_types_id) {
			$cache_key = TABLE_CATEGORIES . '/product_type_category_info/array/custom_products_type_child_id/' . (int)$product_child_types_id . '/categories_parent_path/' . $pass_path;
			
	    	$cache_result = $memcache_obj->fetch($cache_key);
			if ($cache_result !== FALSE) {
				if (tep_not_null($cache_result)) {
					$categories_parent_path_array = array_merge($categories_parent_path_array, array($cache_result));
				}
			} else {
				$categories_select_sql = "	SELECT c.categories_id, c.categories_parent_path, c.custom_products_type_id, c.custom_products_type_child_id 
			                                FROM " . TABLE_CATEGORIES . " AS c 
			                                WHERE c.categories_status = 1
		                                        AND c.categories_parent_path LIKE '\_".str_replace('_', '\_', $pass_path)."\_%'
		                                        AND c.custom_products_type_child_id = '" . tep_db_input($product_child_types_id) . "'";
				$categories_result_sql = tep_db_query($categories_select_sql);
				if ($categories_result_row = tep_db_fetch_array($categories_result_sql)) {
					$categories_parent_path_array[] = $categories_result_row;
					
					$memcache_obj->store($cache_key, $categories_result_row, 600); // cache 10 minutes
				} else {
					$memcache_obj->store($cache_key, array(), 600);
				}
			}
		}
		
		return $categories_parent_path_array;
	}
	
	public static function get_categories_url_alias ($categories_id) {
		global $memcache_obj;
		$return_string = '';
		
		$cache_key = TABLE_CATEGORIES . '/categories_id/' . (int)$categories_id . '/categories_url_alias';
		$cache_result = $memcache_obj->fetch($cache_key);
		
		if ($cache_result !== FALSE) {
			$return_string = $cache_result;
		} else {
			$pro_cat_name_select_sql = "SELECT categories_url_alias
										FROM ". TABLE_CATEGORIES ."
										WHERE categories_id='". (int)$categories_id ."'";
			$pro_cat_name_result_sql = tep_db_query($pro_cat_name_select_sql);
			if ($pro_cat_name_row = tep_db_fetch_array($pro_cat_name_result_sql)) {
				$return_string = $pro_cat_name_row['categories_url_alias'];
			}
			
			$memcache_obj->store($cache_key, $return_string, 21600); // cache 6 hours
		}
		
		return $return_string;
	}
}
?>