<?

class concurrent_process {
    var $page_name;
    var $match_case;
    var $concurrent_process_insert_id;
    var $extra_info;
    var $concurrent_process_id_arr = array();
	var $extra_info_arr = array();
	
	// class constructor
    function concurrent_process($pagename, $matchcase, $extrainfo) {
      	$this->page_name = tep_db_input($pagename);
      	$this->match_case = tep_db_input($matchcase);
      	$this->extra_info = tep_db_input($extrainfo);
      	$this->concurrent_process_insert();
	}
	
	function concurrent_process_insert() {
		$insert_concurrent_process_sql = array('page_name' => $this->page_name,
											   'match_case' => $this->match_case,
											   'extra_info' => $this->extra_info,
											   'created_date' => 'now()'
											);
		tep_db_perform(TABLE_TEMP_PROCESS, $insert_concurrent_process_sql);	
		$this->concurrent_process_insert_id = tep_db_insert_id();
	}
	
	function concurrent_process_matching() {
		$get_data_sql = "	SELECT temp_id, page_name, match_case, extra_info, created_date 
							FROM " . TABLE_TEMP_PROCESS . " 
							WHERE page_name = '".$this->page_name."' 
								AND match_case = '".$this->match_case."'";
		$get_data_result = tep_db_query($get_data_sql);
		
		if (tep_db_num_rows($get_data_result) > 1) {
			while($get_data_row = tep_db_fetch_array($get_data_result)) {
				$this->concurrent_process_id_arr[] = $get_data_row['temp_id'];
				$this->extra_info_arr[] = $get_data_row['extra_info'];
			}
			return true;
		} else {
			return false;
		}
	}
	
	function concurrent_process_match_insert_id () {
		if (count($this->concurrent_process_id_arr) > 0 && tep_not_null($this->concurrent_process_insert_id)) {
			if ($this->concurrent_process_id_arr[0] == $this->concurrent_process_insert_id) {	// First order
				return true;
			} else {
				return false;
			}
		} else {
			return false;
		}
	}
	
	function concurrent_process_cleanup() {
		$del_data_sql = "DELETE LOW_PRIORITY FROM " . TABLE_TEMP_PROCESS . " WHERE temp_id = '".(int)$this->concurrent_process_insert_id."'";
		tep_db_query($del_data_sql);
	}
}
?>