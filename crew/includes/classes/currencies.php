<?php

/*
  $Id: currencies.php,v 1.38 2014/09/05 09:28:40 akmal.adnan Exp $

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2003 osCommerce

  Released under the GNU General Public License
 */

// Class to handle currencies
// TABLES: currencies
class currencies {

    var $currencies, $internal_currencies, $decimal_places, $rebate_point, $rebate_point_formula, $product_instance_id;

    // class constructor
    function currencies() {
        $this->decimal_places = null;
        $this->currencies = array();
        $this->internal_currencies = array();
        $this->rebate_point = 0;
        $this->rebate_point_formula = '';
        $this->product_instance_id = '';

        $selling_site_currency_select_sql = "	SELECT currencies_id, code, title, symbol_left, symbol_right, decimal_point, thousands_point, decimal_places, value, buy_value, sell_value, currencies_used_for 
    											FROM " . TABLE_CURRENCIES . " 
    											WHERE currencies_used_for <> '' 
    											ORDER BY title, code";
        $selling_site_currency_result_sql = tep_db_query($selling_site_currency_select_sql);
        while ($selling_site_currency_row = tep_db_fetch_array($selling_site_currency_result_sql)) {
            $used_mode_array = explode(',', $selling_site_currency_row['currencies_used_for']);
            if (in_array('SELL', $used_mode_array)) {
                $this->currencies[$selling_site_currency_row['code']] = array('title' => $selling_site_currency_row['title'],
                    'symbol_left' => $selling_site_currency_row['symbol_left'],
                    'symbol_right' => $selling_site_currency_row['symbol_right'],
                    'decimal_point' => $selling_site_currency_row['decimal_point'],
                    'thousands_point' => $selling_site_currency_row['thousands_point'],
                    'decimal_places' => $selling_site_currency_row['decimal_places'],
                    'value' => $selling_site_currency_row['value'],
                    'buy_value' => $selling_site_currency_row['buy_value'],
                    'sell_value' => $selling_site_currency_row['sell_value']
                );
            }

            $this->internal_currencies[$selling_site_currency_row['currencies_id']] = $selling_site_currency_row['code'];
        }
    }

    // class methods
    function format($number, $calculate_currency_value = true, $currency_type = '', $currency_value = '', $type = 'sell') {
        global $currency;

        if (empty($currency_type))
            $currency_type = $currency;

        if ($calculate_currency_value == true) {
            if (tep_not_null($currency_value)) {
                $rate = $currency_value;
            } else {
                $rate = (tep_not_null($type) ? $this->currencies[$currency_type][$type . '_value'] : $this->currencies[$currency_type]['value']);
            }

            // added by subrat
            if (is_null($this->decimal_places)) {
                $format_string = $this->currencies[$currency_type]['symbol_left'] . number_format(tep_round($number * $rate, $this->currencies[$currency_type]['decimal_places']), $this->currencies[$currency_type]['decimal_places'], $this->currencies[$currency_type]['decimal_point'], $this->currencies[$currency_type]['thousands_point']) . $this->currencies[$currency_type]['symbol_right'];
            } else {
                $format_string = $this->currencies[$currency_type]['symbol_left'] . number_format(tep_round($number * $rate, $this->decimal_places), $this->decimal_places, $this->currencies[$currency_type]['decimal_point'], $this->currencies[$currency_type]['thousands_point']) . $this->currencies[$currency_type]['symbol_right'];
            }

            // if the selected currency is in the european euro-conversion and the default currency is euro,
            // the currency will displayed in the national currency and euro currency
            if ((DEFAULT_CURRENCY == 'EUR') && ($currency_type == 'DEM' || $currency_type == 'BEF' || $currency_type == 'LUF' || $currency_type == 'ESP' || $currency_type == 'FRF' || $currency_type == 'IEP' || $currency_type == 'ITL' || $currency_type == 'NLG' || $currency_type == 'ATS' || $currency_type == 'PTE' || $currency_type == 'FIM' || $currency_type == 'GRD')) {
                $format_string .= ' <small>[' . $this->format($number, true, 'EUR') . ']</small>';
            }
        } else {
            if (is_null($this->decimal_places)) {
                $format_string = $this->currencies[$currency_type]['symbol_left'] . number_format(tep_round($number, $this->currencies[$currency_type]['decimal_places']), $this->currencies[$currency_type]['decimal_places'], $this->currencies[$currency_type]['decimal_point'], $this->currencies[$currency_type]['thousands_point']) . $this->currencies[$currency_type]['symbol_right'];
            } else {
                $format_string = $this->currencies[$currency_type]['symbol_left'] . number_format(tep_round($number, $this->decimal_places), $this->decimal_places, $this->currencies[$currency_type]['decimal_point'], $this->currencies[$currency_type]['thousands_point']) . $this->currencies[$currency_type]['symbol_right'];
            }
        }

        // BOF: WebMakers.com Added: Down for Maintenance
        if (DOWN_FOR_MAINTENANCE == 'true' and DOWN_FOR_MAINTENANCE_PRICES_OFF == 'true') {
            $format_string = '';
        }
        // BOF: WebMakers.com Added: Down for Maintenance

        return $format_string;
    }

    //Simulate $currencies->format() but don't append currency symbols.
    function do_raw_conversion($number, $calculate_currency_value = true, $currency_type = DEFAULT_CURRENCY, $currency_value = '', $type = 'sell') {
        $raw_number = $number;
        if ($calculate_currency_value) {
            if (tep_not_null($currency_value)) {
                $rate = $currency_value;
            } else {
                $rate = $this->get_value($currency_type, $type);
            }

            $raw_number = $number * $rate;
        }
        return $raw_number;
    }

    function format_round_down($number, $calculate_currency_value = true, $currency_type = DEFAULT_CURRENCY, $currency_value = '', $type = 'sell') {
        if ($calculate_currency_value) {
            $rate = ($currency_value) ? $currency_value : $this->currencies[$currency_type]['value'];

            // added by subrat
            if (is_null($this->decimal_places))
                $format_string = $this->currencies[$currency_type]['symbol_left'] . number_format($this->_currency_round_down($number * $rate, pow(10, (int) $this->currencies[$currency_type]['decimal_places'])), $this->currencies[$currency_type]['decimal_places'], $this->currencies[$currency_type]['decimal_point'], $this->currencies[$currency_type]['thousands_point']) . $this->currencies[$currency_type]['symbol_right'];
            else
                $format_string = $this->currencies[$currency_type]['symbol_left'] . number_format($this->_currency_round_down($number * $rate, pow(10, (int) $this->currencies[$currency_type]['decimal_places'])), $this->decimal_places, $this->currencies[$currency_type]['decimal_point'], $this->currencies[$currency_type]['thousands_point']) . $this->currencies[$currency_type]['symbol_right'];
            // if the selected currency is in the european euro-conversion and the default currency is euro,
            // the currency will displayed in the national currency and euro currency
            if ((DEFAULT_CURRENCY == 'EUR') && ($currency_type == 'DEM' || $currency_type == 'BEF' || $currency_type == 'LUF' || $currency_type == 'ESP' || $currency_type == 'FRF' || $currency_type == 'IEP' || $currency_type == 'ITL' || $currency_type == 'NLG' || $currency_type == 'ATS' || $currency_type == 'PTE' || $currency_type == 'FIM' || $currency_type == 'GRD')) {
                $format_string .= ' <small>[' . $this->format_round_down($number, true, 'EUR', '', $type) . ']</small>';
            }
        } else {
            if (is_null($this->decimal_places))
                $format_string = $this->currencies[$currency_type]['symbol_left'] . number_format($this->_currency_round_down($number, pow(10, (int) $this->currencies[$currency_type]['decimal_places'])), $this->currencies[$currency_type]['decimal_places'], $this->currencies[$currency_type]['decimal_point'], $this->currencies[$currency_type]['thousands_point']) . $this->currencies[$currency_type]['symbol_right'];
            else
                $format_string = $this->currencies[$currency_type]['symbol_left'] . number_format($this->_currency_round_down($number, pow(10, (int) $this->decimal_places)), $this->decimal_places, $this->currencies[$currency_type]['decimal_point'], $this->currencies[$currency_type]['thousands_point']) . $this->currencies[$currency_type]['symbol_right'];
        }
        return $format_string;
    }

    function _currency_round_down($val, $rounder) {
        if ($rounder != 0) {
            return floor($val * $rounder) / $rounder;
        } else {
            return $val;
        }
    }

    function apply_currency_exchange($number, $currency_type = DEFAULT_CURRENCY, $currency_value = '', $type = 'sell') {
        $rate = ($currency_value) ? $currency_value : $this->get_value($currency_type, $type);

        return tep_round($number * $rate, $this->currencies[$currency_type]['decimal_places']);
    }

    function advance_currency_conversion($number, $from_currency_type = DEFAULT_CURRENCY, $to_currency_type = DEFAULT_CURRENCY, $round = true, $type = 'buy') {
        $rate = $this->advance_currency_conversion_rate($from_currency_type, $to_currency_type, $type);

        if ($round) {
            return tep_round($number * $rate, is_null($this->decimal_places) ? $this->currencies[$to_currency_type]['decimal_places'] : $this->decimal_places);
        } else {
            return $number * $rate;
        }
    }

    function is_set($code) {
        if (isset($this->currencies[$code]) && tep_not_null($this->currencies[$code])) {
            return true;
        } else {
            return false;
        }
    }

    function get_value($code, $type = 'sell') {
        if (tep_not_null($type)) {
            return $this->currencies[$code][$type . '_value'];
        } else {
            return $this->currencies[$code]['value'];
        }
    }

    function get_decimal_places($code) {
        return $this->currencies[$code]['decimal_places'];
    }

    function set_decimal_places($decimal_place) {
        $decimal_place = (int) $decimal_place;

        if ($decimal_place <= 0)
            $this->decimal_places = null;
        else
            $this->decimal_places = $decimal_place;
    }

    function get_code_by_id($cur_id) {
        return $this->internal_currencies[$cur_id];
    }

    function get_id_by_code($cur_code) {
        return (int) array_search($cur_code, $this->internal_currencies);
    }

    function get_currency_set() {
        $currency_set_array = array();

        if (count($this->currencies)) {
            foreach ($this->currencies as $code => $currency_info) {
                $currency_set_array[] = array('id' => $code, 'text' => $currency_info['title'] . ' (' . $currency_info['symbol_left'] . $currency_info['symbol_right'] . ')');
            }
        }

        return $currency_set_array;
    }

    //CGDiscountSpecials start
    function display_price_original($products_id, $products_price, $products_tax, $quantity = 1) {
        global $currency;

        $calculate_currency_value = true;

        if (tep_not_null($products_id)) {
            $product_price_array = $this->get_product_prices_info($products_id);

            $product_type = tep_get_custom_product_type($products_id);

            if ($product_type == 3) { // SC Product Type
                $calculate_currency_value = false;
                $store_credit_currency_code = tep_get_customer_store_credit_currency($customer_id, false);

                $products_price = $product_price_array['price'];

                if ($store_credit_currency_code != $currency) {
                    $products_price = round($this->advance_currency_conversion($products_price, $store_credit_currency_code, $currency, false, 'buy'), $this->currencies[$currency]['decimal_places']);
                }
            } else {
                // Get percentage if price value <>
                if (isset($this->currencies[$product_price_array['base_cur']])) {
                    if ($product_price_array['base_cur'] == $currency) {
                        $calculate_currency_value = false;
                    } else if (isset($product_price_array['defined_price'][$currency])) {
                        $products_price = $product_price_array['defined_price'][$currency];
                        $calculate_currency_value = false;
                    } else {
                        if ($currency == DEFAULT_CURRENCY) {
                            // use spot rate to have better selling price (business decision)
                            $products_price = $products_price / $this->currencies[$product_price_array['base_cur']]['value'];
                        } else {
                            $products_price = $products_price / $this->currencies[$product_price_array['base_cur']]['value'];
                        }
                    }
                }
            }
        }

        return $this->format(tep_add_tax($products_price, $products_tax) * $quantity, $calculate_currency_value, '', '', '');
    }

    function display_price($products_id, $products_price, $products_tax, $quantity = 1, $include_pre_order = false, $pre_order_discount = '', $payment_methods_id = 0) {
        global $customer_id, $currency;

        $price_convert = 0;
        $calculate_currency_value = true;

        if (tep_not_null($products_id)) {
            $product_price_array = $this->get_product_prices_info($products_id);

            $product_type = tep_get_custom_product_type($products_id);

            if ($product_type == 3) { // SC Product Type
                $calculate_currency_value = false;
                $store_credit_currency_code = tep_get_customer_store_credit_currency($customer_id, false);

                $products_price = $product_price_array['price'];

                if ($store_credit_currency_code != $currency) {
                    $products_price = $this->advance_currency_conversion($products_price, $store_credit_currency_code, $currency, false, 'buy');
                }
            } else {
                // Get percentage if price value <>
                if (isset($this->currencies[$product_price_array['base_cur']])) {
                    if ($product_price_array['base_cur'] == $currency) {
                        $calculate_currency_value = false;
                    } else if (isset($product_price_array['defined_price'][$currency])) {
                        $products_price = $product_price_array['defined_price'][$currency];
                        $calculate_currency_value = false;
                    } else {
                        if ($currency == DEFAULT_CURRENCY) {
                            // use spot rate to have better selling price (business decision)
                            $products_price = $products_price / $this->currencies[$product_price_array['base_cur']]['value'];
                        } else {
                            $products_price = $products_price / $this->currencies[$product_price_array['base_cur']]['value'];
                        }
                    }
                }
            }
        }

        $pre_order_discount = tep_not_null($pre_order_discount) ? $pre_order_discount : PRE_ORDER_DISCOUNT;

        if ($include_pre_order && abs($pre_order_discount)) {
            if ($pre_order_discount >= 0) {
                $products_price = $products_price + $products_price * abs($pre_order_discount) / 100;
            } else {
                $products_price = $products_price - $products_price * abs($pre_order_discount) / 100;
            }
        }

        if (tep_session_is_registered('customer_id')) {
            $customers_groups_info_array = tep_get_customer_group_discount($customer_id, $products_id, 'product');
            $customers_groups_discount = $customers_groups_info_array['cust_group_discount'];
            $customers_groups_rebate = abs($customers_groups_info_array['cust_group_rebate']);

            $customers_discount_select_sql = "	SELECT customers_discount 
         										FROM " . TABLE_CUSTOMERS . " 
         										WHERE customers_id =  '" . (int) $customer_id . "'";
            $query = tep_db_query($customers_discount_select_sql);
            $query_result = tep_db_fetch_array($query);
            $customer_discount = $query_result['customers_discount'];

            $customer_discount = $customer_discount + $customers_groups_discount;
        } else {
            $customers_groups_info_array = tep_get_customer_group_discount(0, $products_id, 'product');
            $customer_discount = $customers_groups_info_array['cust_group_discount'];
            $customers_groups_rebate = abs($customers_groups_info_array['cust_group_rebate']);
        }

        if ($customer_discount >= 0) {
            $products_price = $products_price + $products_price * abs($customer_discount) / 100;
        } else {
            $products_price = $products_price - $products_price * abs($customer_discount) / 100;
        }

        $cust_group_extra_rebate = 0;
        if ($payment_methods_id > 0 && isset($customers_groups_info_array['cust_group_discount_id']) && (int) $customers_groups_info_array['cust_group_discount_id'] > 0) {
            $customers_groups_extra_op_select_sql = "	SELECT bonus_op 
														FROM " . TABLE_CUSTOMERS_GROUPS_EXTRA_OP . "
														WHERE customers_groups_discount_id = '" . (int) $customers_groups_info_array['cust_group_discount_id'] . "'
															AND payment_methods_id = '" . (int) $payment_methods_id . "'
															AND (currency = '" . tep_db_input($currency) . "'
																OR currency = '*')";
            $customers_groups_extra_op_result_sql = tep_db_query($customers_groups_extra_op_select_sql);
            if ($customers_groups_extra_op_row = tep_db_fetch_array($customers_groups_extra_op_result_sql)) {
                $cust_group_extra_rebate = abs($customers_groups_extra_op_row['bonus_op']);
            }
        }

        if ($calculate_currency_value) { // This price is in USD (Default Currency)
            $price_convert = number_format(($products_price * $quantity), 2, '.', '');

            $this->rebate_point = floor($price_convert * $customers_groups_rebate);
            $this->rebate_point_extra = floor($price_convert * $cust_group_extra_rebate);
            $this->rebate_point_formula = '(' . $products_price . ' * ' . $quantity . ')' . ' = ' . $price_convert . '<br><br>' . $price_convert . ' * ' . $customers_groups_rebate . ' = ' . $this->rebate_point;
        } else {
            $price_convert = number_format(($products_price * $quantity) / $this->currencies[$currency]['value'], 2, '.', '');

            $this->rebate_point = floor($price_convert * $customers_groups_rebate);
            $this->rebate_point_extra = floor($price_convert * $cust_group_extra_rebate);
            $this->rebate_point_formula = '(' . $products_price . ' * ' . $quantity . ') / ' . $this->currencies[$currency]['value'] . ' = ' . $price_convert . '<br><br>' . $price_convert . ' * ' . $customers_groups_rebate . ' = ' . $this->rebate_point;
        }

        return $this->format($products_price * $quantity, $calculate_currency_value, '', '', '');
        //return $this->format(tep_add_tax($products_price, $products_tax) * $quantity, $calculate_currency_value);
    }

    function display_price_per_unit($products_id, $products_price, $products_tax, $quantity = 1, $include_pre_order = false, $pre_order_discount = '') {
        global $customer_id;

        $pre_order_discount = tep_not_null($pre_order_discount) ? $pre_order_discount : PRE_ORDER_DISCOUNT;

        if ($include_pre_order && abs($pre_order_discount)) {
            if ($pre_order_discount >= 0) {
                $products_price = $products_price + $products_price * abs($pre_order_discount) / 100;
            } else {
                $products_price = $products_price - $products_price * abs($pre_order_discount) / 100;
            }
        }

        if (tep_session_is_registered('customer_id')) {
            $customers_groups_info_array = tep_get_customer_group_discount($customer_id, $products_id, 'product');
            $customers_groups_discount = $customers_groups_info_array['cust_group_discount'];

            $query = tep_db_query("select customers_discount from " . TABLE_CUSTOMERS . " where customers_id =  '" . $customer_id . "'");
            $query_result = tep_db_fetch_array($query);
            $customer_discount = $query_result['customers_discount'];

            $customer_discount = $customer_discount + $customers_groups_discount;

            if ($customer_discount >= 0) {
                $products_price = $products_price + $products_price * abs($customer_discount) / 100;
            } else {
                $products_price = $products_price - $products_price * abs($customer_discount) / 100;
            }

            return $this->format(tep_add_tax($products_price, $products_tax) / $quantity, '', '', '');
        } else {
            //return PRICES_LOGGED_IN_TEXT;
            return $this->format(tep_add_tax($products_price, $products_tax) / $quantity, '', '', '');
        }
    }

    function display_preorder_price($products_id, $products_price, $products_tax, $quantity = 1, $pre_order_discount = '') {
        global $currency;

        $calculate_currency_value = true;

        if (tep_not_null($products_id)) {
            $product_price_array = $this->get_product_prices_info($products_id);

            $product_type = tep_get_custom_product_type($products_id);

            if ($product_type == 3) { // SC Product Type
                $calculate_currency_value = false;
                $store_credit_currency_code = tep_get_customer_store_credit_currency($customer_id, false);

                $products_price = $product_price_array['price'];

                if ($store_credit_currency_code != $currency) {
                    $products_price = $this->advance_currency_conversion($products_price, $store_credit_currency_code, $currency, false, 'buy');
                }
            } else {
                // Get percentage if price value <>
                if (isset($this->currencies[$product_price_array['base_cur']])) {
                    if ($product_price_array['base_cur'] == $currency) {
                        $calculate_currency_value = false;
                    } else if (isset($product_price_array['defined_price'][$currency])) {
                        $products_price = $product_price_array['defined_price'][$currency];
                        $calculate_currency_value = false;
                    } else {
                        if ($currency == DEFAULT_CURRENCY) {
                            // use spot rate to have better selling price (business decision)
                            $products_price = $products_price / $this->currencies[$product_price_array['base_cur']]['value'];
                        } else {
                            $products_price = $products_price / $this->currencies[$product_price_array['base_cur']]['value'];
                        }
                    }
                }
            }
        }

        $pre_order_discount = tep_not_null($pre_order_discount) ? $pre_order_discount : PRE_ORDER_DISCOUNT;

        if (abs($pre_order_discount)) {
            if ($pre_order_discount >= 0) {
                $products_price = $products_price + $products_price * abs($pre_order_discount) / 100;
            } else {
                $products_price = $products_price - $products_price * abs($pre_order_discount) / 100;
            }
        }

        return $this->format(tep_add_tax($products_price, $products_tax) * $quantity, $calculate_currency_value, '', '', '');
    }

    function display_preorder_price_per_unit($products_price, $products_tax, $quantity = 1, $pre_order_discount = '') {
        $pre_order_discount = tep_not_null($pre_order_discount) ? $pre_order_discount : PRE_ORDER_DISCOUNT;

        if (abs($pre_order_discount)) {
            if ($pre_order_discount >= 0) {
                $products_price = $products_price + $products_price * abs($pre_order_discount) / 100;
            } else {
                $products_price = $products_price - $products_price * abs($pre_order_discount) / 100;
            }
        }

        return $this->format(tep_add_tax($products_price, $products_tax) / $quantity, '', '', '');
    }

    /*
      Make sure $products_price is always based currency value
     */

    function display_price_nodiscount($products_id, $products_price, $products_tax = 0, $quantity = 1, $discount = array()) {
        global $customer_id, $currency;

        $calculate_currency_value = false;

        $product_price_array = $this->get_product_prices_info($products_id);

        $product_type = tep_get_custom_product_type($products_id);

        if ($product_type == 3) { // SC Product Type
            $calculate_currency_value = false;
            $store_credit_currency_code = tep_get_customer_store_credit_currency($customer_id, false);

            $products_price = $product_price_array['price'];

            if ($store_credit_currency_code != $currency) {
                $products_price = round($this->advance_currency_conversion($products_price, $store_credit_currency_code, $currency, false, 'buy'), $this->currencies[$currency]['decimal_places']);
            }
        } else {
            if ($product_price_array['base_cur'] == $currency) {
                ;
            } else if (isset($product_price_array['defined_price'][$currency])) {
                $products_price = $product_price_array['defined_price'][$currency];
                for ($dis_cnt = 0; $dis_cnt < count($discount); $dis_cnt++) {
                    $products_price += ( $products_price * $discount[$dis_cnt] / 100);
                }
            } else {
                if ($currency == DEFAULT_CURRENCY) {
                    // use spot rate to have better selling price (business decision)
                    $products_price = $products_price / $this->currencies[$product_price_array['base_cur']]['value'];
                } else {
                    $products_price = $products_price / $this->currencies[$product_price_array['base_cur']]['value'];
                }
                $products_price = tep_round($products_price * $this->currencies[$currency]['value'], $this->currencies[$currency]['decimal_places']);
            }
        }
        return $this->format(tep_add_tax($products_price, $products_tax) * $quantity, $calculate_currency_value, '', '', '');
    }

    //CGDiscountSpecials end

    function display_noformat_price_nodiscount($products_id, $products_price, $products_tax = 0, $quantity = 1, $discount = array()) {
        global $customer_id, $currency;

        $calculate_currency_value = false;

        $product_price_array = $this->get_product_prices_info($products_id);

        $product_type = tep_get_custom_product_type($products_id);

        if ($product_type == 3) { // SC Product Type
            $calculate_currency_value = false;
            $store_credit_currency_code = tep_get_customer_store_credit_currency($customer_id, false);

            $products_price = $product_price_array['price'];

            if ($store_credit_currency_code != $currency) {
                $products_price = round($this->advance_currency_conversion($products_price, $store_credit_currency_code, $currency, false, 'buy'), $this->currencies[$currency]['decimal_places']);
            }
        } else {
            if ($product_price_array['base_cur'] == $currency) {
                ;
            } else if (isset($product_price_array['defined_price'][$currency])) {
                $products_price = $product_price_array['defined_price'][$currency];
                for ($dis_cnt = 0; $dis_cnt < count($discount); $dis_cnt++) {
                    $products_price += ( $products_price * $discount[$dis_cnt] / 100);
                }
            } else {
                if ($currency == DEFAULT_CURRENCY) {
                    // use spot rate to have better selling price (business decision)
                    $products_price = $products_price / $this->currencies[$product_price_array['base_cur']]['value'];
                } else {
                    $products_price = $products_price / $this->currencies[$product_price_array['base_cur']]['value'];
                }

                $products_price = tep_round($products_price * $this->currencies[$currency]['value'], $this->currencies[$currency]['decimal_places']);
            }
        }
        return tep_add_tax($products_price, $products_tax) * $quantity;
    }

    function get_product_price($products_id, $customer_id, $pre_order_discount = '0', $currency_type = '') {
        global $currency;

        $do_conversion = false;
        if (empty($currency_type))
            $currency_type = $currency;

        $product_type = tep_get_custom_product_type($products_id);

        $product_price_array = $this->get_product_prices_info($products_id);

        if ($product_type == 3) { // SC Product Type
            $store_credit_currency_code = tep_get_customer_store_credit_currency($customer_id, false);

            $products_price = $product_price_array['price'];

            if ($store_credit_currency_code != $currency) {
                $products_price = $this->advance_currency_conversion($products_price, $store_credit_currency_code, $currency, false, 'buy');
            }

            return round($products_price, $this->currencies[$currency]['decimal_places']);
        } else {
            if ($special_price = tep_get_products_special_price($products_id)) {
                $products_price = $special_price;
            } else {
                if ($product_price_array['base_cur'] == $currency_type) {
                    $products_price = $product_price_array['price'];
                } else if (isset($product_price_array['defined_price'][$currency_type])) {

                    $products_price = $product_price_array['defined_price'][$currency_type];
                } else {
                    $products_price = $product_price_array['price'];
                    $do_conversion = true;
                }

                // Merging store changes: Get the customer group discount
                $customer_discount_array = tep_get_customer_discount_array($customer_id, $products_id, 'product');
                $customer_discount = $customer_discount_array['individual'] + $customer_discount_array['group'];

                // pre-order discount
                if ($pre_order_discount >= 0) {
                    $products_price = $products_price + $products_price * abs($pre_order_discount) / 100;
                } else {
                    $products_price = $products_price - $products_price * abs($pre_order_discount) / 100;
                }

                if ($customer_discount >= 0) {
                    $products_price = $products_price + $products_price * abs($customer_discount) / 100;
                } else {
                    $products_price = $products_price - $products_price * abs($customer_discount) / 100;
                }

                if ($do_conversion) {
                    $products_price = tep_round($products_price, $this->currencies[$product_price_array['base_cur']]['decimal_places']);

                    if ($currency == DEFAULT_CURRENCY) {
                        // use spot rate to have better selling price (business decision)
                        $products_price = $products_price * $this->currencies[$currency]['value'] / $this->currencies[$product_price_array['base_cur']]['value'];
                    } else {
                        // use spot rate to have better selling price (business decision)
                        $products_price = $products_price * $this->currencies[$currency]['value'] / $this->currencies[$product_price_array['base_cur']]['value'];
                    }
                }
            }
        }

        return number_format(tep_round($products_price, $this->currencies[$currency_type]['decimal_places']), $this->currencies[$currency_type]['decimal_places'], '.', ''); // WARNING: Result value must be consist of digit and '.' only. This value used for calculation
    }

    function get_product_prices_info($products_id) {
        $custom_type = tep_get_custom_product_type($products_id);
        if ($custom_type == 4 && tep_not_null($this->product_instance_id)) { // HLA
            $base_price_info_select_sql = "	SELECT ph.products_price, ph.products_base_currency, 
												p.custom_products_type_id, p.products_bundle 
											FROM " . TABLE_PRODUCTS_HLA . " AS ph 
											INNER JOIN " . TABLE_PRODUCTS . " AS p 
												ON p.products_id = ph.products_id 
											WHERE ph.products_hla_id = '" . tep_db_input($this->product_instance_id) . "' 
												AND ph.products_id = '" . tep_db_input($products_id) . "'";
        } else {
            $base_price_info_select_sql = "	SELECT products_price, products_base_currency, custom_products_type_id, products_bundle 
											FROM " . TABLE_PRODUCTS . "
											WHERE products_id = '" . tep_db_input($products_id) . "'";
        }
        $base_price_info_result_sql = tep_db_query($base_price_info_select_sql);
        $base_price_info_row = tep_db_fetch_array($base_price_info_result_sql);

        $product_price_array = array('price' => $base_price_info_row['products_price'],
            'base_cur' => $base_price_info_row['products_base_currency'],
            'custom_products_type_id' => $base_price_info_row['custom_products_type_id'],
            'products_bundle' => $base_price_info_row['products_bundle'],
            'defined_price' => array()
        );

        $defined_prices_select_sql = "	SELECT products_currency_prices_value, products_currency_prices_code 
										FROM " . TABLE_PRODUCTS_CURRENCY_PRICES . "
										WHERE products_id = '" . tep_db_input($products_id) . "'";
        $defined_prices_result_sql = tep_db_query($defined_prices_select_sql);

        while ($defined_prices_row = tep_db_fetch_array($defined_prices_result_sql)) {
            $product_price_array['defined_price'][$defined_prices_row['products_currency_prices_code']] = $defined_prices_row['products_currency_prices_value'];
        }

        return $product_price_array;
    }

    function advance_currency_conversion_rate($from_currency_type = DEFAULT_CURRENCY, $to_currency_type = DEFAULT_CURRENCY, $type = 'sell') {
        if ($from_currency_type == $to_currency_type) {
            return tep_round(1, 8);
        }

        if ($type == 'sell') {
            if ($to_currency_type == DEFAULT_CURRENCY) {
                $rate = ($this->currencies[$from_currency_type]['sell_value'] > 0) ? bcdiv($this->currencies[$to_currency_type]['buy_value'], $this->currencies[$from_currency_type]['sell_value'], 10) : 0;
            } else {
                $rate = ($this->currencies[$from_currency_type]['sell_value'] > 0) ? bcdiv($this->currencies[$to_currency_type]['buy_value'], $this->currencies[$from_currency_type]['value'], 10) : 0;
            }
        } elseif($type == 'spot') {
            $rate = ($this->currencies[$from_currency_type]['value'] > 0) ? (double) ($this->currencies[$to_currency_type]['value'] / $this->currencies[$from_currency_type]['value']) : 0;
        } else {
            if ($to_currency_type == DEFAULT_CURRENCY) {
                $rate = ($this->currencies[$from_currency_type]['sell_value'] > 0) ? bcdiv($this->currencies[$to_currency_type]['sell_value'], $this->currencies[$from_currency_type]['buy_value'], 10) : 0;
            } else {
                $rate = ($this->currencies[$from_currency_type]['sell_value'] > 0) ? bcdiv($this->currencies[$to_currency_type]['sell_value'], $this->currencies[$from_currency_type]['value'], 10) : 0;
            }
        }

        return tep_round($rate, 8);
    }

}

?>