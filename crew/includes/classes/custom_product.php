<?

class custom_product {
	var $info;
	
	function custom_product($order_product_id = '') {
		$this->info = array();
		
		if (tep_not_null($order_product_id)) {
        	$this->query($order_product_id);
      	}
	}
	
	function query($order_product_id) {
		$orders_products_info_select_sql = "SELECT SEC_TO_TIME( (TO_DAYS(now())*24*3600+TIME_TO_SEC(now())) - (TO_DAYS(sta.supplier_tasks_time_reference)*24*3600+TIME_TO_SEC(sta.supplier_tasks_time_reference)) ) AS hours, sta.supplier_tasks_time_reference, sta.supplier_tasks_time_taken, sta.supplier_firstname, sta.supplier_lastname, ocpB.orders_custom_products_value AS bracket_info, ocp.orders_custom_products_value AS task_info, sta.supplier_tasks_start_time, sta.orders_products_id, sta.supplier_tasks_allocation_info, sta.supplier_tasks_allocation_progress, sta.supplier_tasks_status 
											FROM " . TABLE_ORDERS . " AS o 
											INNER JOIN " . TABLE_ORDERS_STATUS . " AS os 
												ON (o.orders_status=os.orders_status_id) 
											INNER JOIN " . TABLE_ORDERS_PRODUCTS . " AS op 
												ON (o.orders_id=op.orders_id AND op.custom_products_type_id = 1) 
											INNER JOIN " . TABLE_ORDERS_CUSTOM_PRODUCTS . " AS ocp 
												ON (op.orders_products_id=ocp.orders_products_id AND ocp.orders_custom_products_key='power_leveling_info') 
											INNER JOIN " . TABLE_SUPPLIER_TASKS_ALLOCATION . " AS sta 
												ON (op.orders_products_id=sta.orders_products_id) 
											LEFT JOIN " . TABLE_ORDERS_CUSTOM_PRODUCTS . " AS ocpB 
												ON (sta.orders_products_id=ocpB.orders_products_id AND ocpB.orders_custom_products_key='power_leveling_bracket_info') 
											WHERE sta.orders_products_id='" . (int)$order_product_id . "'";

		$orders_products_info_result_sql = tep_db_query($orders_products_info_select_sql);
		$orders_products_info_row = tep_db_fetch_array($orders_products_info_result_sql);
		
		preg_match('/(?:ETA:)(?:.*?)(\d+)( day)(##)?/', $orders_products_info_row['task_info'], $eta_days_array);
		preg_match('/(?:ETA:)(?:.*?)(\d+)( hour)(##)?/', $orders_products_info_row['task_info'], $eta_hours_array);
		
		$time_array = explode(':', $orders_products_info_row['hours']);
		
		if ($orders_products_info_row['supplier_tasks_status'] == 2 || $orders_products_info_row['supplier_tasks_status'] == 3 || $orders_products_info_row['supplier_tasks_status'] == 4) {
			$time_pass = (int)((int)$orders_products_info_row['supplier_tasks_time_taken'] / 60);
		} else if ($orders_products_info_row['supplier_tasks_status'] == 1) {
			$time_pass = (int)((int)$time_array[0] + ((int)$orders_products_info_row['supplier_tasks_time_taken'] / 60));
		}
		
		if (tep_not_null($orders_products_info_row['bracket_info'])) {
			$tot_time = 0;
			$bracket_info_array = unserialize($orders_products_info_row['bracket_info']);
			
			preg_match('/(Current '.$bracket_info_array['bracket_cfg']['pl_level_label'].': )([0-9a-zA-Z]+)(##)?/', $orders_products_info_row['task_info'], $current_level_array);
			preg_match('/(Desired '.$bracket_info_array['bracket_cfg']['pl_level_label'].': )([0-9a-zA-Z]+)(##)?/', $orders_products_info_row['task_info'], $desired_level_array);
			
			if (count($eta_days_array) > 0) {
				$tot_time = $eta_days_array[1] * 24;
			}
			
			$tot_time += $eta_hours_array[1];
			
			if ($orders_products_info_row['supplier_tasks_status'] == 2 || $orders_products_info_row['supplier_tasks_status'] == 3) {
				$time_pass = (int)((int)$orders_products_info_row['supplier_tasks_time_taken'] / 60);
			} else if ($orders_products_info_row['supplier_tasks_status'] == 1) {
				$time_pass = (int)((int)$time_array[0] + ((int)$orders_products_info_row['supplier_tasks_time_taken'] / 60));
			}
			
			if ($bracket_info_array['mode'] == 'continuos') {
				$current_level = $current_level_array[2];
				$desired_level = $desired_level_array[2];
							
				for ($i = 0; $i < sizeof($bracket_info_array['range']) ; $i++) {		
					if ((int)$bracket_info_array['range'][$i]['level'] <= (int)$desired_level) {
						if ((int)$bracket_info_array['range'][$i]['level'] > (int)$current_level) {
							$tot_time_need = $tot_time_need + (double)$bracket_info_array['range'][$i]['interval'];
						}
					}
				}
			} else if ($bracket_info_array['mode'] == 'discrete') {
				$current_alias = $current_level_array[2];
				$desired_alias = $desired_level_array[2];
				
				for ($track = 0; $track < sizeof($bracket_info_array['range']) ; $track++) {
					if (tep_not_null($bracket_info_array['range'][$track]['alias'])) {
						if ($current_alias == $bracket_info_array['range'][$track]['alias']) {
							$start = $track;
						} else if ($desired_alias == $bracket_info_array['range'][$track]['alias']) {
							$end = $track;
						}
					} else {
						if ($current_alias == $bracket_info_array['range'][$track]['level']) {
							$start = $track;
						} else if ($desired_alias == $bracket_info_array['range'][$track]['level']) {
							$end = $track;
						}
					}
				}
				
				$current_level = $start;
				$desired_level = $end;
				
				for ($i = 0; $i < sizeof($bracket_info_array['range']) ; $i++) {
									
					if ((int)$bracket_info_array['range'][$i]['level'] <= (int)$desired_level) {
						if ((int)$bracket_info_array['range'][$i]['level'] > (int)$current_level) {
							$tot_time_need = $tot_time_need + (double)$bracket_info_array['range'][$i]['interval'];
						}
					}
				}
			}
		}
		
		if (tep_not_null($orders_products_info_row['bracket_info']) && $desired_level != 0) {
		$level_bar_unit = 20 / ((int)$desired_level - $current_level);
		
			if ($bracket_info_array['mode'] == 'continuos') {
				$total_bar_unit = ((int)$orders_products_info_row['supplier_tasks_allocation_progress'] - (int)$current_level)*$level_bar_unit;
			} else if ($bracket_info_array['mode'] == 'discrete') {
				for ($track_bar = 0; $track_bar < sizeof($bracket_info_array['range']) ; $track_bar++) {
					if (tep_not_null($bracket_info_array['range'][$track_bar]['alias'])) {
						if ($orders_products_info_row['supplier_tasks_allocation_progress'] == $bracket_info_array['range'][$track_bar]['alias']) {
							$total_bar_unit = ($track_bar - (int)$current_level) * $level_bar_unit;
						}
					} else {
						if ($orders_products_info_row['supplier_tasks_allocation_progress'] == $bracket_info_array['range'][$track_bar]['level']) {
							$total_bar_unit = ($track_bar - (int)$current_level) * $level_bar_unit;
						}
					}
				}
			}
		}
		
		$this->info = array('product_name' => $orders_products_info_row["products_name"],
							'bracket_info_array' => $bracket_info_array,
							'bar_available' => (tep_not_null($bracket_info_array) ? true : false),
							'bar_value' => $total_bar_unit,
				          	'total_time' => $tot_time,
				          	'total_time_need' => $total_time_need,
				          	'time_pass' => $time_pass,
				          	'start_level' => $current_level_array[2],
                          	'desired_level' => $desired_level_array[2],
                          	'current_level' => $orders_products_info_row['supplier_tasks_allocation_progress'],
                          	'label' => $bracket_info_array['bracket_cfg']['pl_level_label']);
	}

}
?>