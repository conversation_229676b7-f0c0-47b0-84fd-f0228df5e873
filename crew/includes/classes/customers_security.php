<?
/*
	$Id: customers_security.php,v 1.21 2014/11/03 04:16:09 weesiong Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2003 osCommerce
	
  	Released under the GNU General Public License
*/

class customers_security
{
	private $transition_period;
	private $convert_start_date;
	private $customers_id;
	private $language_id;
	private $max_request = 3;
    
    function  __construct($language_id) {
		$this->language_id = $language_id;
		$this->transition_period = 0;
	}
	
	public function set_customers_id($customers_id) {
		$this->customers_id = $customers_id;
		$this->get_customers_security_start_time();
	}
	
	//update the customers_security_start_time to 0000-00-00 to recognise it had set the Q&A
	public function remove_customers_security_start_time() {
		$start_time_update_sql = "	UPDATE " . TABLE_CUSTOMERS ." 
									SET customers_security_start_time = '0000-00-00'
									WHERE customers_id='". tep_db_input($this->customers_id) ."'";
		return tep_db_query($start_time_update_sql);
	}
	
	public function set_customer_security_answer($question, $answer, $action = 'insert') {
		if (!is_array($question) || !is_array($answer)) {
			return false;
		}
		
		if (!tep_not_null($question[0]) || !tep_not_null($answer[0])) {
			return false;
		}

		$customer_security_answer_array = array (	'customers_security_question_1' => $question[0],
													'customers_security_answer_1' => $answer[0],
													'customers_security_counter' => '0',
													'customers_security_update_time' => 'now()'
												);
		if ($action == 'insert') {
			$customer_security_answer_array['customers_id'] = tep_db_input($this->customers_id);
			tep_db_perform(TABLE_CUSTOMERS_SECURITY, $customer_security_answer_array);
			$this->remove_customers_security_start_time();
		} 
		else if ($action == 'update') {
			tep_db_perform(TABLE_CUSTOMERS_SECURITY, $customer_security_answer_array, 'update', " customers_id = '" . tep_db_input($this->customers_id) . "'");
			$this->remove_customers_security_start_time();
		}
	}
	
	public function update_security_counter() {
		$customer_security_counter_update_sql = "UPDATE " . TABLE_CUSTOMERS_SECURITY . " 
												SET customers_security_counter = customers_security_counter+1
												WHERE customers_id='". tep_db_input($this->customers_id) ."'";
		tep_db_query($customer_security_counter_update_sql);
	}
	
	public function update_question_ask($question_array) {
		if (!is_array($question_array)) {
			return false;
		}
		
		$question_ask = implode(",", $question_array);
		$customer_security_counter_update_sql = "UPDATE " . TABLE_CUSTOMERS_SECURITY . " 
												SET customers_security_question_ask = '" . tep_db_input($question_ask) . "'
												WHERE customers_id='". tep_db_input($this->customers_id) ."'";
		tep_db_query($customer_security_counter_update_sql);
	}
	
	public function check_secret_question_isset() {
		$exist = false;
		$secret_question_select_sql = "	SELECT customers_id, customers_security_question_1, customers_security_answer_1  
										FROM " . TABLE_CUSTOMERS_SECURITY . "
										WHERE customers_id = '" . tep_db_input($this->customers_id) . "'";
		$secret_question_select_result = tep_db_query($secret_question_select_sql);

		if ($secret_question_select_row = tep_db_fetch_array($secret_question_select_result)) {
			// Mantis # 0000024 @ 200810301612 - check if secret question and answer is set
			if (tep_not_null($secret_question_select_row['customers_id']) || (tep_not_null($secret_question_select_row['customers_security_question_1']) && tep_not_null($secret_question_select_row['customers_security_answer_1']))) {
				$exist = true;
			} // ##
		}
		
		return $exist;
	}
	
	public function check_secret_question_is_reset() {
		$reset_qna = false;
		$secret_question_select_sql = "	SELECT customers_id 
										FROM " . TABLE_CUSTOMERS_SECURITY . "
										WHERE customers_id = '" . tep_db_input($this->customers_id) . "'
											AND customers_security_question_1 = ''
											AND customers_security_answer_1 = ''";
		$secret_question_select_result = tep_db_query($secret_question_select_sql);
		
		if ($secret_question_select_row = tep_db_fetch_array($secret_question_select_result)) {
			$reset_qna = true;
		}
		
		return $reset_qna;
	}
	
	public function check_is_max_attempted() {
		$max_attempted = true;
		
		if ($this->get_security_counter() < MAX_SECURITY_TRY) {
			$max_attempted = false;
		}
		return $max_attempted;
	}
	
	public function check_account_freeze() {
		//Freeze the withdraw with following condition :
		//1. Over the max attempted to answer the question
		$freeze_withdraw = false;
		
		if ($this->check_is_max_attempted()) {
			$freeze_withdraw = true;
		}
		return $freeze_withdraw;
	}
	
	//check need to disable the submit button
	public function check_disable_form() {
		$button = false;
		$date_diff = $this->get_convert_pin_expired_date();
		if (tep_not_null($date_diff)) {
			$button = true;
		} else {
			if ($this->check_secret_question_isset()) {
				if ($this->check_is_max_attempted()) {
					$button = true;
				} else if ($this->check_secret_question_is_reset()) {
					$button = true;
				}
			}
		}
		return $button;
	}
	
	public function get_customers_security_start_time() {
		$security_start_time_select_sql = "SELECT customers_security_start_time 
		 									FROM " . TABLE_CUSTOMERS . "
		 									WHERE  customers_id = '" . tep_db_input($this->customers_id) . "'";
		$security_start_time_select_result = tep_db_query($security_start_time_select_sql);
		if ($security_start_time_select_row = tep_db_fetch_array($security_start_time_select_result)) {
			$this->convert_start_date = $security_start_time_select_row['customers_security_start_time'];
		}
	}

	// Mantis # 0000024 @ 200810151600 - Security questions feature: set customers security start time
	public function set_customers_security_start_time() {
		$customers_transition_security_start_time = date("Y-m-d", mktime(0, 0, 0, date("m")  , date("d") + $this->transition_period, date("Y")));
		
		$start_time_update_sql = "	UPDATE ". TABLE_CUSTOMERS ."
									SET customers_security_start_time = '". $customers_transition_security_start_time ."'
									WHERE customers_id='". tep_db_input($this->customers_id) ."'";
		tep_db_query($start_time_update_sql);
	}
	// ##
	
	public function get_convert_pin_expired_date() {
		//calculate the day left for setting Q&A
		//if NOT NULL mean they not yet set.
		$day_diff = NULL;
		if (tep_not_null($this->convert_start_date) && $this->convert_start_date != '0000-00-00') {
			$today_date = date("Y-m-d");
			
			if (tep_day_diff($this->convert_start_date, $today_date) === FALSE) {	// Start_date is a future date
				$day_diff = $this->transition_period + tep_day_diff($today_date, $this->convert_start_date);
			} else {	// Start_date is a past date
				$day_diff = $this->transition_period - tep_day_diff($this->convert_start_date, $today_date);
			}
		}
		return $day_diff;
	}
	
	public function get_random_question($first_question = 0) {
		$sec_question = 0;
		//no first random number
		if ($first_question != 0) {
			$sec_question = tep_rand(1, 3);
			if ($sec_question == $first_question) {
				$sec_question = $this->get_random_question($first_question);
			}
		} else {
			$sec_question = tep_rand(1, 3);
		}
		return $sec_question;
	}
    
	public function get_customer_security_questions_ask() {
		$question_ask_array = array();
		$security_questions_ask_sql = "	SELECT customers_security_question_ask 
										FROM " . TABLE_CUSTOMERS_SECURITY . "
										WHERE customers_id='". tep_db_input($this->customers_id) ."'";
		$security_questions_ask_result = tep_db_query($security_questions_ask_sql);
		if ($security_questions_ask_row = tep_db_fetch_array($security_questions_ask_result)) {		
			if (tep_not_null($security_questions_ask_row['customers_security_question_ask'])) {
				$question_ask_array = explode(",", $security_questions_ask_row['customers_security_question_ask']);
			} else {
				$question_ask_array[] = $this->get_random_question();
				$question_ask_array[] = $this->get_random_question($question_ask_array[0]);
				$this->update_question_ask($question_ask_array);
			}
		}
		return $question_ask_array;
	}
	
	
	public function get_question_text($question_id){
		$question_text = '';
		$question_array = $this->get_customer_security_questions_list();
		foreach ($question_array as $counter => $q_array) {
			foreach ($q_array as $id => $text) {
				if ($question_array[$counter]['id'] == $question_id) {
					$question_text = $question_array[$counter]['text'];
				}
			}
		}
		return $question_text;
	}
	
	
	// Mantis # 0000024 @ 200811031851 - add 1 var in get_customer_security_questions_list to return the specific question
	public function get_customer_security_questions_list($question_id='0') {
		$customer_security_questions_array = array();
				
		// @ 200811101149
		$query = ($question_id=='0')? "" : " AND customers_security_question_id='" . tep_db_input($question_id) . "'";
		$get_languages_id = tep_db_input($this->language_id);
		
		$customer_record_select_sql = "	SELECT * FROM ". TABLE_CUSTOMERS_SECURITY_QUESTIONS ." WHERE language_id = '$get_languages_id'";
		$customer_record_exists_result = tep_db_query($customer_record_select_sql);

		if (tep_db_num_rows($customer_record_exists_result) == 0) {
			$get_languages_id = tep_db_input($_SESSION['default_languages_id']);
		} // @
		
		$customer_security_select_sql = "	SELECT customers_security_question_id, customers_security_question 
											FROM " . TABLE_CUSTOMERS_SECURITY_QUESTIONS ." 
											WHERE language_id = '$get_languages_id' " . $query;
		$customer_security_select_result = tep_db_query($customer_security_select_sql);
		
		while ($customer_security_select_row = tep_db_fetch_array($customer_security_select_result)) {
			$customer_security_questions_array[] = array(	'id' => $customer_security_select_row['customers_security_question_id'],
															'text' => $customer_security_select_row['customers_security_question']);
		}
		
		return $customer_security_questions_array;
	}
	
	// @ 200811121239 - Show HTML of customer's security question and answer
	public function show_customer_security_question_html($clsLabel='', $clsInput='', $extra_option = array()) {
        $html_entry = '';
        $withHeader = isset($extra_option['withHeader']) ? $extra_option['withHeader'] : TRUE; 
        $request_icon = isset($extra_option['requestIcon']) ? $extra_option['requestIcon'] : ''; 
        $request_message = isset($extra_option['requestMessage']) ? $extra_option['requestMessage'] : TRUE;
        $request_type = isset($extra_option['requestType']) ? $extra_option['requestType'] : 'security_token_request';
        $ajx_request_action = $request_type == 'security_token_request' ? 'request_sms_token' : 'request_sms_tac';
        
        if ($withHeader) {
            $html_entry .= '<div class="breakLine"><!-- --></div>
                            <div class="infoBoxTitle">SECURITY TOKEN '.tep_image(DIR_WS_ICONS . 'help-small.png', sprintf(TEXT_REQUEST_TOKEN_HELP_MSG, tep_href_link(FILENAME_CUSTOMER_SUPPORT)), '16', '16', ' class="rightToolTip"').'</div>';
        }
        
		$html_entry .= '<div style="background-color:#ededed; border: 1px solid #cecece; padding: 10px; width: 450px;">
                            <div id="request_token_div"' . ($request_message===TRUE?'':'style="display:none;"') . '>
                                <div style="height:60px; float:left; width: 25px">'. tep_image(DIR_WS_ICONS . 'note.gif') . '</div>
                                <div style="float:left; width: 400px">'.TEXT_REQUEST_TOKEN_MSG.'</div>
                                <br style="clear:both;" />
                                <div style="text-align:center;">'.tep_image_button2('gray_short', "javascript:void(0);", BUTTON_REQUEST_TOKEN, '', ' onClick="trigger_sms_token();"').'</div>
                            </div>
                            <div id="enter_token_div"' . ($request_message===TRUE?'style="display:none;"':'') . '>
                                <div id="token_request_icon" style="height:60px; float:left; width: 25px">' . $request_icon . '</div>
                                <div id="token_request_msg" style="float:left; width: 400px">' . $request_message . '</div>
                                <br style="clear:both;" />
                                <div id="input" style="text-align:center;">'.tep_draw_input_field('answer', '', "autocomplete=\"off\" style=\"width: 200px\" class=\"placeholder\" rel=\"Enter the 6 digits security token\" ").'</div>
                            </div>
                        </div>
                        <div id="verify_mobile_number"></div>';
		
        $html_entry .= '<script language="javascript">
                            function trigger_sms_token() {
                                jQuery("#request_token_div").hide();
                                
                                jQuery.get("/customer_xmlhttp.php?action=' . $ajx_request_action . '", function(xml) {
                                    jQuery(xml).find(\'response\').each(function() {
                                        jQuery("#token_request_msg").html(jQuery(xml).find(\'message\').text());
                                        jQuery("#enter_token_div input").show();
                                        
                                        var res_code = jQuery(xml).find(\'res_code\').text();
                                        if (res_code > 0) {
                                            jQuery("#token_request_icon").html(\''.tep_image(DIR_WS_ICONS . 'success.gif').'\');
                                        } else {
                                            jQuery("#token_request_icon").html(\''.tep_image(DIR_WS_ICONS . 'error.gif').'\');
                                        }
                                        
                                        if(res_code==-1){
                                            jQuery("#enter_token_div input").hide();
                                        }
                                        
                                        jQuery("#enter_token_div").show();
                                    });
                                });
                            }
                            
                            function resend_token() {
                                jQuery.get("/customer_xmlhttp.php?action=resend_token", function(xml) {
                                    jQuery("#verify_mobile_number").html(jQuery(xml).find("content").text());
                                });
                            }

                            function verfy_last4digit() {
                                jQuery.ajax({
                                    type:"POST",
                                    url:"/customer_xmlhttp.php?action=verify_last4digit",
                                    data:"last4digit="+jQuery(\'#last4digit\').val(),
                                    dataType: "xml",
                                    beforeSend: function(){
                                        jQuery("#last4digit_error_message").html("");
                                    },
                                    success: function(xml) {
                                        var status = jQuery(xml).find("error").text(),
                                            msg = jQuery(xml).find("message").text();
                                        if(status=="1"){
                                            jQuery("#last4digit_error_message").html(msg);
                                        }else{
                                            jQuery("#verify_mobile_number").html("");
                                            jQuery("#token_request_icon").html(\''.tep_image(DIR_WS_ICONS . 'success.gif').'\');
                                            jQuery("#token_request_msg").html(msg);
                                            jQuery("#enter_token_div input").show();
                                        }
                                    }
                                });
                            }
                            
                            jQuery(function(){
                                jQuery(".rightToolTip").tipTip({maxWidth: "270px", edgeOffset: 0, defaultPosition: "right", keepAlive: true});
                            });
                        </script>';
		return $html_entry;
	}
	
    /*
     * $request_type : security_token_request (edit record token) | verify_phone_request (verify phone number token) 
     */
    public function request_security_token($customer_id, $request_type = 'security_token_request') {
		$request_status = $this->verify_token_request($customer_id, FALSE, $request_type);
        $response_result = array();
        
        if ($request_status !== FALSE) {
            if ($request_status === 1) {
                require_once(DIR_WS_CLASSES . 'phone_verification.php');
                $pv_obj = new phone_verification();
                $customer_complete_phone_info_array = tep_format_telephone($customer_id);
                
                if (!tep_not_empty($customer_complete_phone_info_array['telephone_number'])) {
                    $request_status = -2;
//                    $response_result['res_text'] = ENTRY_CONTACT_NUMBER_EXIST_ERROR;
                } else if (in_array($pv_obj->get_phone_type($customer_id, '', $customer_complete_phone_info_array['country_international_dialing_code'], $customer_complete_phone_info_array['telephone_number']), array(2, 3))) {
                    require_once(DIR_WS_CLASSES . 'sms.php');
                    $complete_telephone_number = $customer_complete_phone_info_array['country_international_dialing_code'] . $customer_complete_phone_info_array['telephone_number'];
                    
                    if (strlen($complete_telephone_number) > 8) {
                        // Generate token and insert into OTP table for verification purpose
                        $token_val = $this->generate_token(6);
                        $this->update_customer_token($customer_id, $token_val, $request_type);
                        $request_status = $this->verify_token_request($customer_id, TRUE, $request_type);
                        
                        if ($request_status === 1) {
                            $sms_object = new sms();
                            $sms_object->send($customer_id, $customer_complete_phone_info_array, 'From OffGamers. Your security token: '.$token_val.'. Validity 10 minutes from the time you receive this SMS. TQ', 'security_token');

                            $response_result['res_text'] = sprintf(TEXT_REQUEST_TOKEN_SUCCESS_MSG, substr($complete_telephone_number, 0, -4) . str_repeat('X', 4));

                            if (!$sms_object->send_sms) {
                                $response_result['res_text'] .= '<br>Testing Mode: Security Token: ' . $token_val;
                            }
                        }
                    } else {
                        $request_status = -2;
                    }
                } else {
                    $request_status = -1;
                }
            }
        }
        
        if ($request_status === FALSE) {
            $response_result['res_text'] = TEXT_REQUEST_TOKEN_FAIL_MSG;
        } else {
            switch ($request_status) {
                case 2:
                    $response_result['res_text'] = TEXT_REQUEST_TOKEN_REUSE_MSG;
                    break;
                case -1:
                    // invalid phone number type
                    $response_result['res_text'] = ENTRY_CONTACT_NUMBER_EXIST_ERROR;
                    break;
                case -2:
                    $response_result['res_text'] = 'Invalid mobile phone number';
                    break;
            }
        }
        
        $response_result['res_code'] = $request_status;
        return $response_result;
	}
    
    public function request_security_token_again_via_email ($customer_id) {
        $request_type = 'security_token_request';
        $request_status = $this->verify_token_request($customer_id, FALSE, $request_type);
        $response_result = array();
        
        if ($request_status !== FALSE) {
            if ($request_status == '1') {   // create new security token when user attempt the token for the first time of the day
                $token_val = $this->generate_token(6);
                $this->update_customer_token($customer_id, $token_val);
                $this->verify_token_request($customer_id, TRUE, $request_type);
            }
            
            $token_select_sql = "   SELECT customers_id, customers_otp_digit
                                    FROM " . TABLE_CUSTOMERS_OTP . "
                                    WHERE customers_id = '".(int)$customer_id."'
                                        AND customers_otp_type = '" . tep_db_input($request_type) . "'";
            $token_result_sql = tep_db_query($token_select_sql);
            if ($token_row = tep_db_fetch_array($token_result_sql)) {
                $pin_number_plain = $token_row['customers_otp_digit'];

                $reset_customer_setting_data_array = array(
                    'customers_setting_value' => '1',
                    'updated_datetime' => date('Y-m-d H:i:s', mktime(date('H'), date('i'), date('s'), date('m'), date('d')+1, date('Y'))),
                    'created_datetime' => 'now()'
                );
                tep_db_perform(TABLE_CUSTOMERS_SETTING, $reset_customer_setting_data_array, 'update', "customers_id = '" . (int)$customer_id . "' AND customers_setting_key = 'security_token_request'");

                $token_data_array = array(
                    'customers_otp_request_date' => date('Y-m-d H:i:s', mktime(date('H'), date('i'), date('s'), date('m'), date('d')+1, date('Y')))
                );
                tep_db_perform(TABLE_CUSTOMERS_OTP, $token_data_array, 'update', "customers_id = '" . (int)$customer_id . "' AND customers_otp_type = '" . tep_db_input($request_type) . "'");

                $reset_pin_customers_info_select_sql = "SELECT customers_firstname, customers_lastname, customers_gender, customers_email_address 
                                                        FROM ". TABLE_CUSTOMERS ."
                                                        WHERE customers_id='". (int)$customer_id ."'";
                $reset_pin_customers_info_result_sql = tep_db_query($reset_pin_customers_info_select_sql);
                $reset_pin_customers_info_row = tep_db_fetch_array($reset_pin_customers_info_result_sql);

                $customers_firstname = $reset_pin_customers_info_row['customers_firstname'];
                $customers_lastname = $reset_pin_customers_info_row['customers_lastname'];
                $customers_gender = $reset_pin_customers_info_row['customers_gender'];
                $customers_email_address = $reset_pin_customers_info_row['customers_email_address'];

                $reset_pin_email_content = 	tep_get_email_greeting($customers_firstname, $customers_lastname, $customers_gender) .
                                            sprintf(EMAIL_USER_RESET_PIN_BODY, $pin_number_plain) . "\n\n" .
                                            EMAIL_FOOTER;

                @tep_mail($customers_firstname . ' ' . $customers_lastname, $customers_email_address, EMAIL_USER_RESET_PIN_SUBJECT, $reset_pin_email_content, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
            }
        } else {
            $response_result['res_text'] = TEXT_REQUEST_TOKEN_FAIL_MSG;
        }
        
        $response_result['res_code'] = $request_status;
        return $response_result;
    }
    
    private function verify_token_request($customer_id, $update = FALSE, $token_type = 'security_token_request') {
        $return_int = FALSE;    // 1 = new request, 2 = reuse
        
        if ($customer_id) {
            $counter_select_sql = "	SELECT customers_setting_value, created_datetime > DATE_SUB(NOW(), INTERVAL 24 HOUR) AS same_day_request, 
                                        updated_datetime > DATE_SUB(NOW(), INTERVAL 10 MINUTE) AS active_request
                                    FROM " . TABLE_CUSTOMERS_SETTING . " 
                                    WHERE customers_id = '" . tep_db_input($customer_id) . "'
                                        AND customers_setting_key = '" . tep_db_input($token_type) .  "'";
            $counter_result_sql = tep_db_query($counter_select_sql);
            if ($counter_row = tep_db_fetch_array($counter_result_sql)) {
                if ($counter_row['same_day_request'] == '1') {
                    if ($counter_row['active_request'] == '1') {    // Reuse
                        $return_int = 2;
                    } else {
                        if ((int)$counter_row['customers_setting_value'] >= $this->max_request) {
                            $return_int = FALSE;
                        } else {
                            $return_int = 1;
                            
                            if ($update) {
                                $return_int = FALSE;
                                
                                $query = " 	UPDATE " . TABLE_CUSTOMERS_SETTING . " 
                                            SET customers_setting_value = customers_setting_value + 1,
                                                updated_datetime = now()
                                            WHERE customers_id = '" . tep_db_input($customer_id) . "' 
                                                AND customers_setting_key = '" . tep_db_input($token_type) ."'";
                                if (tep_db_query($query)) {
                                    $counter_select_sql = "	SELECT customers_setting_value
                                                            FROM " . TABLE_CUSTOMERS_SETTING . "
                                                            WHERE customers_id = '" . tep_db_input($customer_id) . "'
                                                                AND customers_setting_key = '" . tep_db_input($token_type) ."'";
                                    $counter_result_sql = tep_db_query($counter_select_sql);
                                    if ($counter_row = tep_db_fetch_array($counter_result_sql)) {
                                        if ((int)$counter_row['customers_setting_value'] <= $this->max_request) {
                                            $return_int = 1;
                                        }
                                    }
                                }
                            }
                        }
                    }
                } else {
                    $return_int = 1;
                    
                    if ($update) {
                        $sql_data_array = array(
                            'customers_setting_value' => '1',
                            'updated_datetime' => 'now()',
                            'created_datetime' => 'now()'
                        );
                        if (!tep_db_perform(TABLE_CUSTOMERS_SETTING, $sql_data_array, 'update', "customers_id = '" . tep_db_input($customer_id) . "' AND customers_setting_key = '" . tep_db_input($token_type) . "'")) {
                            $return_int = FALSE;
                        }
                    }
                }
            } else {
                $return_int = 1;
                
                if ($update) {
                    $sql_data_array = array(
                        'customers_id' => $customer_id,
                        'customers_setting_key' => $token_type,
                        'customers_setting_value' => '1',
                        'updated_datetime' => 'now()',
                        'created_datetime' => 'now()'
                    );
                    if (!tep_db_perform(TABLE_CUSTOMERS_SETTING, $sql_data_array)) {
                        $return_int = FALSE;
                    }
                }
            }
        }
        
        return $return_int;
    }
    
    public function deactive_token($customer_id, $request_type = 'security_token_request') {
        $sql_data_array = array(
            'updated_datetime' => date('Y-m-d H:i:s', mktime(date('H'), date('i'), date('s'), date('m'), date('d')-1, date('Y'))),
        );
        tep_db_perform(TABLE_CUSTOMERS_SETTING, $sql_data_array, 'update', "customers_id = '" . tep_db_input($customer_id) . "' AND customers_setting_key = '" . tep_db_input($request_type) . "'");
        
        $token_data_array = array(
            'customers_otp_request_date' => date('Y-m-d H:i:s', mktime(date('H'), date('i'), date('s'), date('m'), date('d')-1, date('Y')))
        );
        tep_db_perform(TABLE_CUSTOMERS_OTP, $token_data_array, 'update', "customers_id = '" . (int)$customer_id . "' AND customers_otp_type = '" . tep_db_input($request_type) . "'");
        
        unset($sql_data_array, $token_data_array);
    }
    
    private function generate_token($no_of_pin) {
        $code_length = 0;
        $pin = '';
        
        while ($code_length < $no_of_pin) {
            $pin .= tep_rand(0, 9);
            $code_length++;
        }
        
        return $pin;
    }
    
    private function update_customer_token($customer_id, $token, $request_type = 'security_token_request') {
        $token_data_array = array(
                                'customers_id' => $customer_id,
                                'customers_otp_type' => $request_type,
                                'customers_otp_digit' => $token,
                                'customers_otp_request_date' => 'now()'
                                );
        
        $token_select_sql = "   SELECT customers_id
                                FROM " . TABLE_CUSTOMERS_OTP . "
                                WHERE customers_id = '".tep_db_input($customer_id)."'
                                    AND customers_otp_type = '" . tep_db_input($request_type) . "'";
        $token_result_sql = tep_db_query($token_select_sql);
		
		if (tep_db_num_rows($token_result_sql)) {
            tep_db_perform(TABLE_CUSTOMERS_OTP, $token_data_array, 'update', "customers_id = '" . tep_db_input($customer_id) . "' AND customers_otp_type = '" . tep_db_input($request_type) . "'");
        } else {
            tep_db_perform(TABLE_CUSTOMERS_OTP, $token_data_array);
        }
    }
    
	// @ 200812171245 - Check security answer
	public function validate_customer_security_answer($post_answer='',$form_name='',$err_message='', $request_type = 'security_token_request') {
		global $messageStack;
		
        $error = true;
		$post_answer = tep_not_null($post_answer) ? tep_db_prepare_input(strip_tags($post_answer)) : '';
        
        $challenge_token = $this->get_customer_security_token($request_type);

        if ($challenge_token !== FALSE) {
            if ($challenge_token != 0) {
                if ($post_answer == $challenge_token) {
                    $error = false;
                } else {
                    // Answer wrongly
                }
            } else {
                // Expiry token
            }
        }
        
        if ($error && $form_name && $err_message) $messageStack->add($form_name, $err_message);
		
		return $error;
	}
	
	
	// @ 200811101649 - Get customer secret question
	public function get_customer_registered_security_question($question='1') {
		$registered_question = '';
		$field = 'customers_security_question_'.$question;
		
		$customer_security_question_select_sql = "	SELECT " . $field . "
													FROM ". TABLE_CUSTOMERS_SECURITY . "
													WHERE customers_id = '" . tep_db_input($this->customers_id) . "'";														
		$customer_security_question_select_result = tep_db_query($customer_security_question_select_sql);
		
		if ($customer_security_question = tep_db_fetch_array($customer_security_question_select_result)) {
			$registered_question = $customer_security_question[$field];
		}
		
		return $registered_question;
	}
	
	private function get_customer_security_token($request_type = 'security_token_request') {
        $correct_token = FALSE;
        
        $customer_otp_select_sql = "    SELECT customers_otp_digit, customers_otp_request_date > DATE_SUB(NOW(), INTERVAL 10 MINUTE) AS active_token
										FROM ". TABLE_CUSTOMERS_OTP . "
                                        WHERE customers_id = '" . tep_db_input($this->customers_id) . "'
                                            AND customers_otp_type = '" . tep_db_input($request_type) . "'";														
		$customer_otp_result_sql = tep_db_query($customer_otp_select_sql);
		
		if ($customer_otp_row = tep_db_fetch_array($customer_otp_result_sql)) {
            if ($customer_otp_row['active_token'] == '1') {
                $correct_token = $customer_otp_row['customers_otp_digit'];
            } else {
                $correct_token = 0;
            }
		}
		
		return $correct_token;
    }
    
	public function get_customer_registered_security_answer($answer='1') {
		$registered_answer = '';
		$field = 'customers_security_answer_'.$answer;
		
		$customer_security_answer_select_sql = "	SELECT " . $field . "
													FROM ". TABLE_CUSTOMERS_SECURITY . "
													WHERE customers_id = '" . tep_db_input($this->customers_id) . "'";														
		$customer_security_answer_select_result = tep_db_query($customer_security_answer_select_sql);

		if ($customer_security_answer = tep_db_fetch_array($customer_security_answer_select_result)) {
			$registered_answer = $customer_security_answer[$field];
		}
		
		return $registered_answer;
	}
	
	public function get_security_counter() {
		$security_counter_select_sql = "SELECT customers_security_counter 
										FROM " . TABLE_CUSTOMERS_SECURITY ." 
										WHERE customers_id='" . tep_db_input($this->customers_id) . "'";
		$security_counter_select_result = tep_db_query($security_counter_select_sql);
		$security_counter_select_row = tep_db_fetch_array($security_counter_select_result);
		return $security_counter_select_row['customers_security_counter'];
	}
}
?>