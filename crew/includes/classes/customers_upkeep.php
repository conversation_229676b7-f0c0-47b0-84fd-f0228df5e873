<?
/*
	$Id: customers_upkeep.php,v 1.2 2012/07/13 05:56:42 weesiong Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2003 osCommerce
	
  	Released under the GNU General Public License
*/

class customers_upkeep
{
    private $upkeep_array;
	public $customers_id;
	
	public function __construct($customers_id = 0) {
        $this->upkeep_array = array();
        $this->reloadUpkeep($customers_id);        
	}
	
    function reloadUpkeep($customers_id) {
        $this->setCustomersId($customers_id);
        
        if ($this->customers_id) {
            $upkeep_select_sql = "	SELECT storage 
                                    FROM " . TABLE_CUSTOMERS_UPKEEP . " 
                                    WHERE customers_id = '" . $this->customers_id . "'";
            $upkeep_result_sql = tep_db_query($upkeep_select_sql);
            if ($upkeep_row = tep_db_fetch_array($upkeep_result_sql)) {
                $this->upkeep_array = json_decode($upkeep_row['storage'], true);
            }
        }
    }
    
	function mergeUpkeep($merge_array) {
		if (is_array($merge_array)) {
            $this->upkeep_array = array_merge($this->upkeep_array, $merge_array);
        }
	}
	
    function unsetUpkeep($req_field) {
        unset($this->upkeep_array[$req_field]);
    }
    
	function createUpkeep() {
        if ($this->customers_id) {
            tep_db_perform(TABLE_CUSTOMERS_UPKEEP, array('customers_id' => $this->customers_id, 'storage' => json_encode($this->upkeep_array)));   
        }
	}
	
	function updateUpkeep() {
        if ($this->customers_id) {
            $update_sql = "	SELECT customers_id 
                            FROM " . TABLE_CUSTOMERS_UPKEEP . " 
                            WHERE customers_id='" . $this->customers_id . "'";
            $result_sql = tep_db_query($update_sql);
            if (tep_db_num_rows($result_sql)) {
                $customer_upkeep_update_sql = " UPDATE " . TABLE_CUSTOMERS_UPKEEP . " 
                                                SET storage = '" . json_encode($this->upkeep_array) . "'
                                                WHERE customers_id='" . $this->customers_id . "'";
                tep_db_query($customer_upkeep_update_sql);
            } else {
                $this->createUpkeep();
            }
        }
	}
	
	function setCustomersId($customers_id) {
		if ((int)$customers_id) {
            $this->customers_id = (int)$customers_id;
        }
	}
    
    /*
     * reset_password : Redirect and request user to reset password [Allowed values: 0 (off),1 (on)]
     */
    function getUpkeepValue($req_field) {
        if(isset($this->upkeep_array[$req_field])) {
            return $this->upkeep_array[$req_field];
        }
        
        return FALSE;
    }
    
}
?>