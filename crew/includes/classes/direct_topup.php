<?

/*
	function listing:
	- get_supported_direct_top_up_product
	- get_game_input
	- add_customers_top_up_info
	- add_top_up
	- do_top_up
	- validate_game_acc
	- check_top_up_status
	- check_payment_status
	- get_credit_balance
	- get_signature
	
	- get_product_info
	- verify_signature
	- get_server_list
	- cron_update_server_list
	- cron_check_credit_balance
		- DEFINE MIN_BALANCE
*/

include_once(DIR_WS_CLASSES . 'direct_topup_api_log.php');
include_once(DIR_WS_CLASSES . 'publishers.php');
include_once(DIR_WS_CLASSES . 'curl.php');

class direct_topup extends publishers{
	var $connect_via_proxy;
	
	function direct_topup() {
		$this->connect_via_proxy = false;
	}
	
	function get_product_publisher($pass_id) {
		global $memcache_obj;
		
		$publishers_id = 0;
		
		$cache_key = TABLE_TOP_UP_INFO . '/publishers_products/'.(int)$pass_id.'/publisher';
		$cache_result = $memcache_obj->fetch($cache_key);
		
		if ($cache_result !== FALSE) {
			$publishers_id = $cache_result;
		} else {
			$publishers_id_select_sql = "	SELECT pg.publishers_id 
											FROM " . TABLE_PUBLISHERS_PRODUCTS . " AS pp 
											INNER JOIN " . TABLE_PUBLISHERS_GAMES . " AS pg 
												ON pp.publishers_games_id = pg.publishers_games_id
											WHERE pp.products_id = '".(int)$pass_id."'";
			$publishers_id_result_sql = tep_db_query($publishers_id_select_sql);
			if ($publishers_id_row = tep_db_fetch_array($publishers_id_result_sql)) {
				$publishers_id = $publishers_id_row['publishers_id'];
			}
			$memcache_obj->store($cache_key, $publishers_id, 86400);
		}
		return $publishers_id;
	}
	
	function get_product_info($pass_id, $info_key = 'GAME') {
		global $memcache_obj;
		
		$return_info = null;
		
		$cache_key = TABLE_TOP_UP_INFO . '/product/'.(int)$pass_id.'/'.$info_key;
		$cache_result = $memcache_obj->fetch($cache_key);
		
		if ($cache_result !== FALSE) {
			$return_info = $cache_result;
		} else {
			$product_info_select_sql = "	SELECT tui.top_up_info_value 
											FROM " . TABLE_TOP_UP_INFO . " AS tui  
											WHERE tui.products_id = '".(int)$pass_id."'
												AND tui.top_up_info_key = '".$info_key."'
												AND tui.top_up_info_type_id = '1'";
			$product_info_result_sql = tep_db_query($product_info_select_sql);
			if ($product_info_row = tep_db_fetch_array($product_info_result_sql)) {
				$return_info = $product_info_row['top_up_info_value'];
			}
			$memcache_obj->store($cache_key, $return_info, 86400);
		}
		return $return_info;
	}
	
	function get_supported_direct_top_up_product() {
		global $memcache_obj;
		
		$supported_games = array();
		
		$cache_key = TABLE_TOP_UP_INFO . '/supported_direct_top_up_product/products_id';
		$cache_result = $memcache_obj->fetch($cache_key);
		
		if ($cache_result !== FALSE) {
			$supported_games = $cache_result;
		} else {
			$supported_direct_top_up_select_sql = "	SELECT pp.products_id, pg.publishers_id 
													FROM " . TABLE_PUBLISHERS_PRODUCTS . " AS pp 
													INNER JOIN " . TABLE_PUBLISHERS_GAMES . " AS pg 
														ON pg.publishers_games_id = pp.publishers_games_id
													GROUP BY pp.products_id";
			$supported_direct_top_up_result_sql = tep_db_query($supported_direct_top_up_select_sql);
			while ($supported_direct_top_up_row = tep_db_fetch_array($supported_direct_top_up_result_sql)) {
				$supported_games[$supported_direct_top_up_row['products_id']] = $supported_direct_top_up_row['publishers_id'];
			}
			$memcache_obj->store($cache_key, $supported_games, 86400);
		}
		return $supported_games;
	}
	
	function get_servers($products_id) {
		global $memcache_obj;
		
		$servers_array = array();
		
        $cache_key = TABLE_PUBLISHERS_GAMES . '/products_id/'.$products_id.'/publishers_server';
        
		$cache_result = $memcache_obj->fetch($cache_key);
		
		if ($cache_result !== FALSE) {
			$servers_array = $cache_result;
		} else {
			$game_info_select_sql = "	SELECT pg.publishers_server
										FROM " . TABLE_PUBLISHERS_PRODUCTS . " AS pp 
										INNER JOIN " . TABLE_PUBLISHERS_GAMES . " AS pg
											ON pp.publishers_games_id = pg.publishers_games_id
										WHERE pp.products_id = '".$products_id."'";
			$game_info_result_sql = tep_db_query($game_info_select_sql);
			if ($game_info_row = tep_db_fetch_array($game_info_result_sql)) {
				$servers_array = json_decode($game_info_row['publishers_server'],true);
				$memcache_obj->store($cache_key, $servers_array, 86400);
			}
		}
		return $servers_array;
	}
	
	function conf_get_servers($products_id) {
		global $memcache_obj;
		
		$servers_array = array();
		
        $cache_key = TABLE_PUBLISHERS_GAMES . '/products_id/'.$products_id.'/publishers_server';
        
		$cache_result = $memcache_obj->fetch($cache_key);
		
		if ($cache_result !== FALSE) {
			$servers_array = $cache_result;
		} else {
			$game_info_select_sql = "	SELECT pg.publishers_server
										FROM " . TABLE_PUBLISHERS_PRODUCTS . " AS pp 
										INNER JOIN " . TABLE_PUBLISHERS_GAMES . " AS pg
											ON pp.publishers_games_id = pg.publishers_games_id
										WHERE pp.products_id = '".$products_id."'";
			$game_info_result_sql = tep_db_query($game_info_select_sql);
			if ($game_info_row = tep_db_fetch_array($game_info_result_sql)) {
				$servers_array = json_decode($game_info_row['publishers_server'],true);
				$memcache_obj->store($cache_key, $servers_array, 86400);
			}
		}
		
		$sel_servers_array = array();
		if (isset($servers_array['servers']) && count($servers_array['servers'])) {
			foreach ($servers_array['servers'] as $servers_id_loop => $servers_data_loop) {
				$sel_servers_array[] = array('id' => $servers_id_loop, 'text' => $servers_data_loop);
			}
		}
		return $sel_servers_array;
	}
	
	function check_is_supported_by_direct_top_up($products_id) {
		global $memcache_obj;
		
		$publishers_id = 0;
		
		$cache_key = TABLE_TOP_UP_INFO . '/products_id/' . $products_id . '/is_supported_by_direct_top_up/';
		$cache_result = $memcache_obj->fetch($cache_key);
		
		if ($cache_result !== FALSE) {
			$publishers_id = $cache_result;
		} else {
			$product_info_select_sql = "SELECT products_bundle, products_bundle_dynamic, custom_products_type_id 
										FROM " . TABLE_PRODUCTS . " 
										WHERE products_id='" . tep_db_input($products_id) . "'";
			$product_info_result_sql = tep_db_query($product_info_select_sql);
			$product_info_row = tep_db_fetch_array($product_info_result_sql);
			if ($product_info_row["products_bundle"] == 'yes' || $product_info_row["products_bundle_dynamic"] == 'yes') {
				$publishers_select_sql = "	SELECT pg.publishers_id
											FROM " . TABLE_PUBLISHERS_GAMES . " AS pg
											INNER JOIN " . TABLE_PUBLISHERS_PRODUCTS . " AS pp 
												ON pg.publishers_games_id = pp.publishers_games_id
											INNER JOIN " . TABLE_PRODUCTS_DELIVERY_INFO . " AS pdi
												ON pdi.products_id = pp.products_id 
											INNER JOIN " . TABLE_PRODUCTS_BUNDLES . " AS pb
												ON pp.products_id=pb.subproduct_id
											WHERE pb.bundle_id = '".(int)$products_id."'
												AND pdi.products_delivery_mode_id = '6'
											LIMIT 1";
				$publishers_result_sql = tep_db_query($publishers_select_sql);
				if ($publishers_row = tep_db_fetch_array($publishers_result_sql)) {
					$publishers_id = $publishers_row['publishers_id'];
				}
			} else {
				$publishers_select_sql = "	SELECT pg.publishers_id
											FROM " . TABLE_PUBLISHERS_GAMES . " AS pg
											INNER JOIN " . TABLE_PUBLISHERS_PRODUCTS . " AS pp 
												ON pg.publishers_games_id = pp.publishers_games_id
											INNER JOIN " . TABLE_PRODUCTS_DELIVERY_INFO . " AS pdi
												ON pdi.products_id = pp.products_id
											WHERE pdi.products_id = '".(int)$products_id."'
												AND pdi.products_delivery_mode_id = '6'";
				$publishers_result_sql = tep_db_query($publishers_select_sql);
				if ($publishers_row = tep_db_fetch_array($publishers_result_sql)) {
					$publishers_id = $publishers_row['publishers_id'];
				}
			}
			$memcache_obj->store($cache_key, $publishers_id, 86400);
		}
		return $publishers_id;
	}
	
	function get_game_input($products_id) {
		global $memcache_obj, $languages_id, $default_languages_id;
		$game_input_array = array();

		$cache_key = TABLE_TOP_UP_INFO . '/products_id/' . $products_id . '/language/' . $languages_id;
		$cache_result = $memcache_obj->fetch($cache_key);
		
		if ($cache_result !== FALSE) {
			$game_input_array = $cache_result;
		} else {
			$game_input_select_sql = "	SELECT tui.top_up_info_id, tuil.top_up_info_display, tui.top_up_info_key, tui.use_function, tui.set_function 
										FROM " . TABLE_TOP_UP_INFO . " AS tui 
										INNER JOIN " . TABLE_TOP_UP_INFO_LANG . " AS tuil
											ON tui.top_up_info_id = tuil.top_up_info_id
										WHERE tui.products_id  = '".(int)$products_id."'
											AND tui.top_up_info_type_id = 2
											AND IF(tuil.languages_id = '". $languages_id ."' , 1, IF (( SELECT COUNT(tuil2.top_up_info_id) > 0 
												 FROM ". TABLE_TOP_UP_INFO_LANG." AS tuil2
												 WHERE tuil2.top_up_info_id = tui.top_up_info_id
												 	AND tuil2.languages_id = '".$languages_id."'), 0, tuil.languages_id = '".$default_languages_id."'))
										ORDER BY tui.sort_order";
			$game_input_result_sql = tep_db_query($game_input_select_sql);
			while ($game_input_row = tep_db_fetch_array($game_input_result_sql)) {
				$game_input_array[$game_input_row['top_up_info_key']] = $game_input_row;
			}
			$memcache_obj->store($cache_key, $game_input_array, 86400);
		}
		return $game_input_array;
	}
	
	function get_admin_game_input($products_id) {
		global $memcache_obj, $languages_id, $default_languages_id;
		$game_input_array = array();
		
		$cache_key = TABLE_TOP_UP_INFO . '/products_id/' . $products_id . '/language/' . $languages_id;
		$cache_result = $memcache_obj->fetch($cache_key);
		
		if ($cache_result !== FALSE) {
			$game_input_array = $cache_result;
		} else {
			$game_input_select_sql = "	SELECT tui.top_up_info_id, tuil.top_up_info_display, tui.top_up_info_key, tui.use_function, tui.set_function 
										FROM " . TABLE_TOP_UP_INFO . " AS tui 
										INNER JOIN " . TABLE_TOP_UP_INFO_LANG . " AS tuil
											ON tui.top_up_info_id = tuil.top_up_info_id
										WHERE tui.products_id  = '".(int)$products_id."'
											AND tui.top_up_info_type_id = 2
											AND tuil.languages_id = 1 
										ORDER BY tui.sort_order";
			$game_input_result_sql = tep_db_query($game_input_select_sql);
			while ($game_input_row = tep_db_fetch_array($game_input_result_sql)) {
				$game_input_array[$game_input_row['top_up_info_key']] = $game_input_row;
			}
			$memcache_obj->store($cache_key, $game_input_array, 86400);
		}
		return $game_input_array;
	}
	
	function get_top_up_info($products_id) {
		global $memcache_obj, $languages_id, $default_languages_id;
		$game_input_array = array();

		$cache_key = TABLE_TOP_UP_INFO . '/products_id/' . $products_id;
		$cache_result = $memcache_obj->fetch($cache_key);
		
		if ($cache_result !== FALSE) {
			$top_up_info_array = $cache_result;
		} else {
			$top_up_info_select_sql = "	SELECT tui.* 
										FROM " . TABLE_TOP_UP_INFO . " AS tui 
										WHERE tui.products_id  = '".(int)$products_id."'
										ORDER BY tui.sort_order";
			$top_up_info_result_sql = tep_db_query($top_up_info_select_sql);
			while ($top_up_info_row = tep_db_fetch_array($top_up_info_result_sql)) {
				$top_up_info_array[$top_up_info_row['top_up_info_key']] = $top_up_info_row;
			}
			$memcache_obj->store($cache_key, $top_up_info_array, 86400);
		}
		return $top_up_info_array;
	}
	
	function get_customer_input_value($orders_products_id) {
		$customer_input_value = array();
		$customer_input_value_select_sql = "SELECT ctui.top_up_info_id, ctui.top_up_value 
											FROM " . TABLE_CUSTOMERS_TOP_UP_INFO . " AS ctui 
											WHERE ctui.orders_products_id  = '".(int)$orders_products_id."'";
		$customer_input_value_result_sql = tep_db_query($customer_input_value_select_sql);
		while ($customer_input_value_row = tep_db_fetch_array($customer_input_value_result_sql)) {
			$customer_input_value[$customer_input_value_row['top_up_info_id']] = $customer_input_value_row['top_up_value'];
		}
		return $customer_input_value;
	}
	
	function add_customers_top_up_info($orders_products_id, $extra_param) {
		$added_info = 0;
		if (count($extra_param)) {
			$top_up_info_key_array = array();
			$top_up_select_sql = "	SELECT top_up_info_id, top_up_info_key
									FROM " . TABLE_TOP_UP_INFO . "
									WHERE top_up_info_key IN ('".implode("','", array_keys($extra_param))."')";
			$top_up_result_sql = tep_db_query($top_up_result_sql);
			while ($top_up_row = tep_db_fetch_array($top_up_result_sql)) {
				$top_up_info_key_array[$top_up_row['top_up_info_key']] = $top_up_row['top_up_info_id'];
			}
			
			foreach ($extra_param as $extra_key_loop => $extra_data_loop) {
				$customers_top_up_info_data_sql = array('top_up_info_id' => (int)$top_up_info_key_array[$extra_key_loop],
														'orders_products_id' => $orders_products_id,
														'top_up_value' => tep_db_prepare_input($extra_data_loop));
				tep_db_perform(TABLE_CUSTOMERS_TOP_UP_INFO, $customers_top_up_info_data_sql);
				$added_info++;
			}
		}
		return $added_info;
	}
	
	function add_top_up($orders_products_id, $publishers_id) {
		$return_array = array();
		$return_array['top_up_id'] = 0;
		
		$check_orders_products_select_sql = "	SELECT orders_products_id
												FROM " . TABLE_ORDERS_TOP_UP . " 
												WHERE orders_products_id = '".(int)$orders_products_id."'";
		$check_orders_products_result_sql = tep_db_query($check_orders_products_select_sql);
		if ($check_orders_products_row = tep_db_fetch_array($check_orders_products_result_sql)) {
			$return_array['top_up_id'] = $check_orders_products_row['top_up_id'];
		} else {
			$top_up_data_sql = array(	'orders_products_id' => (int)$orders_products_id,
										'top_up_status' => 1,
										'top_up_process_flag' => 0,
										'publishers_id' => (int)$publishers_id,
										'top_up_created_date' => 'now()'
										);
			tep_db_perform(TABLE_ORDERS_TOP_UP, $top_up_data_sql);
			$return_array['top_up_id'] = tep_db_insert_id();
		}
		return $return_array;
	}
	
	/* function to check OffGamers' orders status */
	function get_customer_top_up_status($orders_products_id) {
		$return_status = 0;
		$orders_top_up_status_select_sql = "	SELECT top_up_status  
												FROM " . TABLE_ORDERS_TOP_UP . " AS otu 
												WHERE otu.orders_products_id = '".(int)$orders_products_id."'";
		$orders_top_up_status_result_sql = tep_db_query($orders_top_up_status_select_sql);
		if ($orders_top_up_status_row = tep_db_fetch_array($orders_top_up_status_result_sql)) {
			$return_status = $orders_top_up_status_row['top_up_status'];
		}
		return $return_status;
	}
	
	/* function to check customer top-up info */
	function get_customer_top_up_info($orders_products_id) {
		$customer_top_up_info_array = array();
		$get_customer_top_up_info_select_sql = "SELECT otu.*  
			    								FROM " . TABLE_ORDERS_TOP_UP . " AS otu 
			    								WHERE orders_products_id = '".(int)$orders_products_id."'";
		$get_customer_top_up_info_result_sql = tep_db_query($get_customer_top_up_info_select_sql);
		if ($get_customer_top_up_info_row = tep_db_fetch_array($get_customer_top_up_info_result_sql)) {
			$customer_top_up_info_array = $get_customer_top_up_info_row;
		}
	 	return $customer_top_up_info_array;
	}
	
	/* function return top-up status defination in array */
	function top_up_status($pass_id = '') {
		$top_up_status_array = array();
		$top_up_status_array[0] = 'Unknown';
		$top_up_status_array[1] = 'Pending';
		$top_up_status_array[3] = 'Reloaded';
		$top_up_status_array[10] = 'Failed';
		$top_up_status_array[11] = 'Not Found';
		
		return (isset($top_up_status_array[(int)$pass_id]) ? $top_up_status_array[(int)$pass_id] : $top_up_status_array[0]);
	}
	
	function validate_game_acc($publisher_id, $games_acc_array, &$curl_response_array='') {
		$class = $this->void_include_class($publisher_id);
		if (tep_not_null($class) && tep_class_exists('dtu_'.$class)) {
  			eval('$direct_topup_class_obj = new dtu_'.$class.'();');
  		} else {
  			$direct_topup_class_obj = new dtu_offgamers();
  		}
  		
  		if (method_exists($direct_topup_class_obj, 'validate_game_acc')) {
  			return $direct_topup_class_obj->validate_game_acc($publisher_id, $games_acc_array,$curl_response_array);
  		} else {
  			return false;
  		}
	}
	
	function get_character_list($publisher_id, $games_acc_array, &$curl_response_array='') {
		$class = $this->void_include_class($publisher_id);
		if (tep_not_null($class) && tep_class_exists('dtu_'.$class)) {
  			eval('$direct_topup_class_obj = new dtu_'.$class.'();');
  		} else {
  			$direct_topup_class_obj = new dtu_offgamers();
  		}
  		
  		if (method_exists($direct_topup_class_obj, 'get_character_list')) {
  			return $direct_topup_class_obj->get_character_list($publisher_id, $games_acc_array, $curl_response_array);
  		} else {
  			return array();
  		}
	}
	
	function void_include_all_classes() {
		$module_directory = DIR_WS_MODULES . 'direct_topup/';
		$file_extension = substr($PHP_SELF, strrpos($PHP_SELF, '.'));
		$directory_array = array();
		$file_extension = '.php';
		if ($dir = @dir($module_directory)) {
			while ($file = $dir->read()) {
				if (!is_dir($module_directory . $file)) {
					if (substr($file, strrpos($file, '.')) == $file_extension) {
		        		if (file_exists(DIR_FS_CATALOG_LANGUAGES . $language . '/modules/direct_topup/' . $file)) {
						    include_once(DIR_FS_CATALOG_LANGUAGES . $language . '/modules/direct_topup/' . $file);
						}
					    include_once($module_directory . $file);
		        	}
		      	}
		    }
		    $dir->close();
		}
	}
	
	function void_include_class($publisher_id) {
		global $memcache_obj, $language;
		$cache_key = TABLE_PUBLISHERS_CONFIGURATION . '/publishers_id/' . $publisher_id . '/key/top_up_mode';
		$cache_result = $memcache_obj->fetch($cache_key);
		
		$module_directory = DIR_WS_MODULES . 'direct_topup/';
		
		$class_name = '';
		if ($cache_result !== FALSE) {
			$class_name = $cache_result;
		} else {
			$publishers_configuration_sql = "	SELECT publishers_configuration_value, publishers_configuration_key
												FROM ".TABLE_PUBLISHERS_CONFIGURATION." 
												WHERE publishers_id = '".(int)$publisher_id."'
													AND publishers_configuration_key = 'TOP_UP_MODE'";
			$publishers_configuration_result = tep_db_query($publishers_configuration_sql);
			if ($publishers_configuration_row = tep_db_fetch_array($publishers_configuration_result)) {
				$class_name = $publishers_configuration_row['publishers_configuration_value'];
			} else {
				$class_name = 'offgamers';
			}
			$memcache_obj->store($cache_key, $class_name, 86400);
		}
		
		if (tep_not_null($class_name)) {
			include_once($module_directory . $class_name . '.php');
			if (file_exists(DIR_FS_CATALOG_LANGUAGES . $language . '/modules/direct_topup/' . $class_name . '.php')) {
				include_once(DIR_FS_CATALOG_LANGUAGES . $language . '/modules/direct_topup/' . $class_name . '.php');
			}
			else{
                if (file_exists(DIR_FS_CATALOG_LANGUAGES . $language . '/modules/direct_topup/offgamers.php')) {
                    include_once(DIR_FS_CATALOG_LANGUAGES . $language . '/modules/direct_topup/offgamers.php');
                }
            }
		}
		return $class_name;
	}
	
	function get_result_code_description($pass_code) {
		switch ($pass_code) {
			case '2000':
				return 'Success';

			case '1000':
				return 'Unknown action';
			case '1001':
				return 'Incomplete request';
			case '1002':
				return 'Invalid signature';
			case '1003':
				return 'IP access denied';
			case '1004':
				return 'Publisher does not exists';
			case '1005':
				return 'Publisher inactive';
			case '1006':
				return 'Game character doest not exist';
			case '1007':
				return 'Game account doest not exist';
			case '1008':
				return 'Game does not exists';
			case '1009':
				return 'Inactive Game';
			case '1010':
				return 'Server does not exists';
			case '1011':
				return 'Inactive Server';
			case '1012':
				return 'Merchant does not exist';
			case '1013':
				return 'Inactive merchant';
			case '1014':
				return 'Publisher Reference ID expired';
			case '1015':
				return 'Publisher Reference ID does not exists';
			case '1016':
				return 'Duplicate Publisher Reference ID';
			case '1017':
				return 'Server not available';

            case '1200':
                return 'Pending Publishers to Notify Status';

			case '1300':
				return 'Permission denied (Unable to check reference status)';
				
			case '1500':
				return 'Out of credit';
			case '1501':
				return 'Top-up amount less than minimum amount';
			case '1502':
				return 'Top-up amount exceed than maximum amount';
			case '1503':
				return 'Permission denied (Unable to top-up  other transaction)';
			case '1504':
				return 'Invalid top-up amount';
			case '1505':
				return 'Invalid top-up currency';
			case '1506':
				return 'Game account suspended';
			case '1507':
				return 'Game account closed';
			case '1508':
				return 'Exceed player top-up limit';
			case '1509':
				return 'Invalid payment status at OffGamers Side';
            case '1510':
                return 'Hit Minimum Margin Checking, Order Blocked from delivery';
				
			case '3000':
				return 'Please contact publisher';
				
				break;
			default:
				return $pass_code;
		}
	}
	
	function get_publishers_games_conf($pass_publishers_games_id='', $pass_key='') {
		$publishers_games_configuration_array = array();
		$publishers_games_configuration_select_sql = "	SELECT * 
														FROM " . TABLE_PUBLISHERS_GAMES_CONFIGURATION  . "
														WHERE publishers_games_id = '".$pass_publishers_games_id."'";
		if (tep_not_null($pass_key)) {
			$publishers_games_configuration_select_sql .= " AND publishers_games_configuration_key = '".tep_db_input($pass_key)."'";
			$publishers_games_configuration_result_sql = tep_db_query($publishers_games_configuration_select_sql);
			$publishers_games_configuration_row = tep_db_fetch_array($publishers_games_configuration_result_sql);
			return $publishers_games_configuration_row['publishers_games_configuration_value'];
		} else {
			$publishers_games_configuration_result_sql = tep_db_query($publishers_games_configuration_select_sql);
			while ($publishers_games_configuration_row = tep_db_fetch_array($publishers_games_configuration_result_sql)) {
				$publishers_games_configuration_array[$publishers_games_configuration_row['publishers_games_configuration_key']] = $publishers_games_configuration_row;
			}
			return $publishers_games_configuration_array;
		}
	}
	
	function character_is_sync($products_id) {
		global $memcache_obj;
		
		$cache_key = TABLE_TOP_UP_INFO . '/'.(int)$products_id.'/sync_publisher_character_flag';
		$cache_result = $memcache_obj->fetch($cache_key);
		
		$sync_publisher_character_flag = false;
		
		if ($cache_result !== FALSE) {
			$sync_publisher_character_flag = $cache_result;
		} else {
			$is_sync_sql = "SELECT top_up_info_value
							FROM " . TABLE_TOP_UP_INFO . "
							WHERE top_up_info_key = 'sync_publisher_character_flag'
								AND products_id = '".(int)$products_id."'";
			$is_sync_result = tep_db_query($is_sync_sql);
			$is_sync_row = tep_db_fetch_array($is_sync_result);
			$sync_publisher_character_flag = ($is_sync_row['top_up_info_value']==1 ? true : false);
			
			$memcache_obj->store($cache_key, $sync_publisher_character_flag, 86400);
		}
		return $sync_publisher_character_flag;
	}
    
    function retype_account($products_id) {
		global $memcache_obj;
		
		$cache_key = TABLE_TOP_UP_INFO . '/'.(int)$products_id.'/retype_account_flag';
		$cache_result = $memcache_obj->fetch($cache_key);
		
		$retype_account_flag = false;
		
		if ($cache_result !== FALSE) {
			$retype_account_flag = $cache_result;
		} else {
			$to_retype_sql = "SELECT top_up_info_value
							FROM " . TABLE_TOP_UP_INFO . "
							WHERE top_up_info_key = 'retype_account_flag'
								AND products_id = '".(int)$products_id."'";
			$to_retype_result = tep_db_query($to_retype_sql);
			$to_retype_row = tep_db_fetch_array($to_retype_result);
			$retype_account_flag = ($to_retype_row['top_up_info_value']==1 ? true : false);
			
			$memcache_obj->store($cache_key, $retype_account_flag, 86400);
		}
		return $retype_account_flag;
	}
	
	function get_account_platform($products_id) {
		global $memcache_obj;
		
		$cache_key = TABLE_PUBLISHERS_GAMES_CONFIGURATION . '/products_id/'.(int)$products_id.'/account_platform_list';
		$cache_result = $memcache_obj->fetch($cache_key);
		
		$account_platform_array = array();
		
		if ($cache_result !== FALSE) {
			$account_platform_array = $cache_result;
		} else {
			$account_platform_sql = "	SELECT pgc.publishers_games_configuration_value 
										FROM " . TABLE_PUBLISHERS_GAMES_CONFIGURATION . " AS pgc
										INNER JOIN " . TABLE_PUBLISHERS_PRODUCTS . " AS pp
											ON pgc.publishers_games_id = pp.publishers_games_id
												AND pgc.publishers_games_configuration_key = 'ACCOUNT_PLATFORM_LIST' 
										WHERE pp.products_id = '".(int)$products_id."'";
			$account_platform_result_sql = tep_db_query($account_platform_sql);
			if ($account_platform_row = tep_db_fetch_array($account_platform_result_sql)) {
				$account_platform_array = json_decode($account_platform_row['publishers_games_configuration_value'], 1);
				$memcache_obj->store($cache_key, $account_platform_array, 86400); // 1 day
			}
		}
		return $account_platform_array;
	}
    
    function is_customer_id_to_email_conversion_needed($cat_id) {
        global $memcache_obj;
		
		$cache_key = TABLE_PUBLISHERS_GAMES_CONFIGURATION . '/catagories_id/'.(int)$cat_id.'/convert_customer_id_to_email_flag';
		$cache_result = $memcache_obj->fetch($cache_key);
        $convert_customer_id_to_email_flag = '';
        if ($cache_result !== FALSE) {
			$convert_customer_id_to_email_flag = $cache_result;
		} else {
            $publishers_game_id_select_sql = "  SELECT publishers_games_id 	 
                                                FROM " . TABLE_PUBLISHERS_GAMES . " 
                                                WHERE categories_id  = '".(int)$cat_id."'";
            $publishers_game_id_result_sql = tep_db_query($publishers_game_id_select_sql);
            if ($publishers_game_id_row = tep_db_fetch_array($publishers_game_id_result_sql)) {
                $convert_customer_id_to_email_flag = $this->get_publishers_games_conf($publishers_game_id_row['publishers_games_id'], 'CONVERT_CUSTOMER_ID_TO_EMAIL_FLAG');
				$memcache_obj->store($cache_key, $convert_customer_id_to_email_flag, 86400); // 1 day
            }
        }
      
        return ($convert_customer_id_to_email_flag == '1') ? true : false;
    }
    
    function convert_customer_email_to_id($customer_email) {
        $customer_id = '';
        $customer_id_select_sql = " SELECT customers_id 
                                    FROM " . TABLE_CUSTOMERS . "
                                    WHERE customers_email_address = '" . tep_db_prepare_input($customer_email) . "'";
        $customer_id_result_sql = tep_db_query($customer_id_select_sql);
        if ($customer_id_row = tep_db_fetch_array($customer_id_result_sql)) {
            $customer_id = $customer_id_row['customers_id'];
        }
        
        return $customer_id;
    }
}
?>