<?
class direct_topup_api_log {
	var $api_log_id;
	function direct_topup_api_log($pass_action, $request_log, $extra_param='') {
		$this->api_log_id = 0;
		$this->ip_address = tep_get_ip_address();
		
		$api_log_data_sql = array();
		$api_log_data_sql['ip_address'] = tep_db_prepare_input($this->ip_address);
		$api_log_data_sql['action'] = tep_db_prepare_input($pass_action);
		$api_log_data_sql['request_log'] = tep_db_prepare_input($request_log);
		$api_log_data_sql['request_start'] = 'now()';
		
		if (tep_not_null($extra_param)) $api_log_data_sql = array_merge($api_log_data_sql, $extra_param);
		
		tep_db_perform(TABLE_API_LOG, $api_log_data_sql);
		
		$this->api_log_id = tep_db_insert_id();
	}
	
	function end_log($pass_result_code, $response_log, $extra_param) {
		if ((int)$this->api_log_id==0) return;
		$api_log_data_sql = array();
		$api_log_data_sql['result_code'] = tep_db_prepare_input($pass_result_code);
		$api_log_data_sql['response_log'] = tep_db_prepare_input($response_log);
		$api_log_data_sql['request_end'] = 'now()';
		
		if (tep_not_null($extra_param)) $api_log_data_sql = array_merge($api_log_data_sql, $extra_param);
		
		tep_db_perform(TABLE_API_LOG, $api_log_data_sql, 'update', " api_log_id = '".$this->api_log_id."' ");
	}
}
?>