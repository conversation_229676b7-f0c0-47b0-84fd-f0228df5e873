<?php
/*
  	$Id: event.php,v 1.26 2011/07/13 08:41:01 sionghuat.chng Exp $
*/
class event extends email {
	var $news_id;
	
	// class constructor
	function event($news_id='') {
		$this->news_id = $news_id;
	}
	
	function addForm(&$messageStack) {
		GLOBAL $languages_id, $default_languages_id;
		$events_select_sql = "	SELECT events_id, events_order_period_date_from, events_order_period_date_to, events_order_period_note, events_order_period_empty_err_msg, events_order_period_invalid_err_msg 
								FROM " . TABLE_EVENTS . " 
								WHERE news_id = '" . $this->news_id . "' 
								AND events_status = '1'";
		$event_result_sql = tep_db_query($events_select_sql);
		
		$listing_html = '';
		$printelements = "menu_content";
		$server_selection_array = array();
		$action_res_array = array('code' => '', 'html' => '');
		
		ob_start();
?>

<script>
	function limitChars(textarea2, limit, infodiv, char_left) {
		var text = textarea2.value; 
		var textlength = text.length;
		var info = document.getElementById(infodiv);
		
		if(textlength > limit) {
		//info.innerHTML = 'You cannot write more then '+limit+' characters!';
		textarea2.value = text.substr(0,limit);
		return false;
		} else {
			info.innerHTML = (limit - textlength) + ' ' + char_left;
			return true;
		}
	}
</script>

<?=tep_draw_form('event_form', tep_href_link(FILENAME_EVENT, tep_get_all_get_params() . 'action=submit_event_form', 'SSL'), 'post', 'enctype="multipart/form-data"')?>
<table border="0" width="100%" cellspacing="0" cellpadding="0">
<? if ($messageStack->size('event') > 0) { ?>
	<tr bgcolor="#fbfac6">
    	<td><?=$messageStack->output('event')?></td>
	</tr>
<?	} ?>
	<tr>
		<td><?=tep_draw_separator('pixel_trans.gif', '100%', '13'); ?></td>
	</tr>
	<tr>
		<td class="message" nowrap>
		<?
			if (isset($_SESSION['customer_id'])) {
				echo '&nbsp;' . TEXT_LOGIN_EVENT_MESSAGE;
			} else {
				echo '&nbsp;' . sprintf(TEXT_LOGOFF_EVENT_MESSAGE, FILENAME_LOGIN, FILENAME_LOGIN);
			}
		?>
		</td>
	</tr>
	<tr>
		<td><?=tep_draw_separator('pixel_trans.gif', '100%', '13'); ?></td>
	</tr>
	<tr>
		<td>
			<div class="row_separator"></div>
		</td>
	</tr>
	<tr>
		<td><?=tep_draw_separator('pixel_trans.gif', '100%', '15'); ?></td>
	</tr>
	<tr>
		<td>
			<table border="0" width="100%" cellpadding="2" cellspacing="5">
				<?php
					if ($event_result_row = tep_db_fetch_array($event_result_sql)) {
						$i = 0;
						$field_name = array();
						$field_name_array = array();
						$required = '';
						$note = '';
						
						//if error message is set, perform the task + display * required
						if (tep_not_null($event_result_row['events_order_period_empty_err_msg'])) {
							echo tep_draw_hidden_field('required_field[]', 'order_no');
							$required = '<span class="redIndicator">*</span>';
						} else {
							$required = '';
						}
						
						//if note is set, display note
						if (tep_not_null($event_result_row['events_order_period_note'])) {
							$note = '<br/>( ' . $event_result_row['events_order_period_note'] . ' )';
						} else {
							$note = '';
						}
						//if validation period start + end date is set, display order id input field
						if (($event_result_row['events_order_period_date_from'] != '0000-00-00 00:00:00') && ($event_result_row['events_order_period_date_to'] != '0000-00-00 00:00:00')) {
							echo '<tr>
									<td class="inputLabelNew" align="left" valign="top" colspan="2">'.ENTRY_EVENT_ORDER_NO.'</td>
								  </tr>	
								  <tr>
									<td class="inputField">
										'.tep_draw_input_field('order_no', "", 'size="35" maxlength="20" colspan="2" class="noFocusInput"').' ' . $required . ''.$note.'
									</td>
								</tr>';
						}
						
						$event_options_select_sql = "	SELECT eo.events_options_id, eo.events_options_input_type, eo.events_options_required, eod.events_options_title, eod.events_options_max_size, eod.events_options_row_size, eod.events_options_column_size, eod.events_options_name, eod.events_options_note
														FROM " . TABLE_EVENTS_OPTIONS . " AS eo
														INNER JOIN ".TABLE_EVENTS_OPTIONS_DESCRIPTION ." as eod
															ON (eo.events_options_id = eod.events_options_id)
														WHERE eo.events_id = '" . $event_result_row['events_id'] . "'
															AND eo.events_options_status = '1'
															AND ( IF(eod.language_id = '". $languages_id. "'&& eod.events_options_title <> '', 1,
												 					IF ((	SELECT count(eod_inner.events_options_id) > 0 from " . TABLE_EVENTS_OPTIONS_DESCRIPTION . " as eod_inner
												 							WHERE eod_inner.events_options_id = eo.events_options_id 
												 								AND eod_inner.events_options_title <> ''
												 								AND eod_inner.language_id ='". $languages_id. "'), 0, eod.language_id = '". $default_languages_id ."')))
																	ORDER BY eo.events_options_sort_order";	
						$event_options_result_sql = tep_db_query($event_options_select_sql);
						
						// Built Event Option Display
						while ($event_options_result_row = tep_db_fetch_array($event_options_result_sql)) {
							if ($event_options_result_row['events_options_max_size'] > 0) {//if max size is set, store the max size value
								echo tep_draw_hidden_field('content_'.$i.'[max_size]', $event_options_result_row['events_options_max_size']);
							}
							
							$field_name = 'content_'.$i;
							$field_name_array = 'content_'.$i.'[input_content]'; //create an array of content_0[]
							
							if ($event_options_result_row['events_options_required'] == '1') {//if mandatory option is set, store $field_name (#155)
								echo tep_draw_hidden_field('required_field[]', $field_name);
							}
							
							//store events_options_id + events_options_title to the respective hidden field
							echo tep_draw_hidden_field('content_'.$i.'[events_option_id]', $event_options_result_row['events_options_id']);
							echo tep_draw_hidden_field('content_'.$i.'[events_name]', $event_options_result_row['events_options_title']);
							
							$create_input_field = $this->field_creator($event_options_result_row, $field_name_array, $languages_id, $default_languages_id);
						 	
							echo '<tr>
									<td colspan="2" class="inputLabelNew" align="left" valign="top">'.$event_options_result_row['events_options_title'].'</td>
								  </tr>';
							
							echo '<tr>
									<td colspan="2">'.$create_input_field.'</td>
								  </tr>';
							$i++;
						}
					} else {
						$action_res_array['code'] = '-1';
					}
				?>
				
				<tr>
					<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '100%', '10'); ?></td>
				</tr>
				
				<tr>
					<td colspan="2" align="left">
					<?
						if (isset($_SESSION['customer_id'])) {
                            echo tep_image_button2('gray_short','javascript:void(0);',BUTTON_SUBMIT,'','onClick="document.event_form.submit()" style="float:left"');
							
						} else {
                            echo tep_image_button2('gray_short','javascript:void(0);',BUTTON_SUBMIT,'','onClick="alert(\'Please Login First\')" style="float:left"');
						}
                        echo '&nbsp;&nbsp;&nbsp;';
                        echo tep_image_button2('gray_short','javascript:history.back(-1);',BUTTON_CANCEL,'','style="float:right"');
					?>
					</td>
				</tr>
			</table>
		</td>
	</tr>
	<tr>
		<td><?=tep_draw_separator('pixel_trans.gif', '100%', '20'); ?></td>
	</tr>
</table>
</form>

<?
		$action_res_array['html'] = ob_get_contents();
		
		ob_end_clean();
 		
		return $action_res_array;
	}
	
	function submit_event_form($post_array, $files_array, &$messageStack) {
		global $languages_id, $default_languages_id;
		
		//Definition
		$admin_email_name_n_address_array 		= array();
		$customer_email_name_n_address_array 	= array();
		$show_in_customer_email_array 			= array();
		$error 									= false;
		$share_display 							= false;
		$customer_email 						= '';
		$customer_name 							= '';
		$customer_tel 							= '';
		$note 									= '';
		$order_no_row							= '';
		$files_tmp_name 						= array();
		$data 									= array();
		$full_path_array 						= array();
		$option_id_array 						= array();
		
		$csv_str_array = array();
		
		$get_events_select_sql = "	SELECT e.events_id, e.events_sender_email, e.events_admin_copy_email, e.events_order_period_date_from, e.events_order_period_date_to,
										e.events_order_period_empty_err_msg, e.events_order_period_invalid_err_msg 
									FROM " . TABLE_EVENTS . " AS e 
									WHERE e.news_id = '" . tep_db_prepare_input($this->news_id) . "'
										AND e.events_status = '1'";
		$get_events_result_sql = tep_db_query($get_events_select_sql);
		
		if ($get_events_row = tep_db_fetch_array($get_events_result_sql)) {
			if (tep_not_null($post_array['required_field'])) {
				if($this->validation($post_array['required_field'], $post_array, $files_array, $get_events_row['events_id'], $messageStack, $languages_id, $default_languages_id) == false) {
					$error = true;
				}
			}
			
			if (($get_events_row['events_order_period_date_from'] != '0000-00-00 00:00:00')  && ($get_events_row['events_order_period_date_to'] != '0000-00-00 00:00:00')) {
				if (tep_not_null($get_events_row['events_order_period_empty_err_msg'])) {
					if (tep_not_null($post_array['required_field']) && in_array('order_no', $post_array['required_field'])) {
						if(!tep_not_null($post_array['order_no'])) {
							$error = true;
							$error_msg = $get_events_row['events_order_period_empty_err_msg'];
							$messageStack->add_session('event', $error_msg);
						}
					}
				}
				
				if(tep_not_null($post_array['order_no'])) {
                    $validate_order_customer_select_sql = "	SELECT orders_id 
															FROM " . TABLE_ORDERS . " 
															WHERE orders_id = '" . tep_db_input($post_array['order_no']) . "'
                                                                AND customers_id = '" . tep_db_input($_SESSION['customer_id']) . "'";
                    $validate_order_customer_result_sql = tep_db_query($validate_order_customer_select_sql);
                    if (tep_db_num_rows($validate_order_customer_result_sql) == 0) {
                        $error = true;
                        $error_msg = 'Invalid Order ID';
                        $messageStack->add_session('event', $error_msg);
                    } else if (tep_not_null($get_events_row['events_order_period_invalid_err_msg'])) {
						$validate_order_no_select_sql = "	SELECT orders_id 
															FROM " . TABLE_ORDERS . " 
															WHERE orders_id = '" . tep_db_input($post_array['order_no']) . "'
                                                                AND last_modified BETWEEN '" . $get_events_row['events_order_period_date_from'] . "' AND '" . $get_events_row['events_order_period_date_to'] . "'";
						$validate_order_no_result_sql = tep_db_query($validate_order_no_select_sql);
						if (tep_db_num_rows($validate_order_no_result_sql) > 0) {
							$order_no_row .= '<tr>
												 <td align="right" width="130">Order Number : </td>
												 <td>' . $post_array['order_no'] . '</td>
											  </tr>';
						} else {
							$error = true;
							$error_msg = $get_events_row['events_order_period_invalid_err_msg'];
							$messageStack->add_session('event', $error_msg);
						}
					} else {
						$order_no_row .= '<tr>
											 <td align="right" width="130">Order Number : </td>
											 <td>' . $post_array['order_no'] . '</td>
										  </tr>';
					}
				}
			}
			
			// Show in customer email
			if (tep_not_null($get_events_row['events_sender_email'])) {
				$show_in_customer_email_array = $this->tep_parse_email_string($get_events_row['events_sender_email'],1);
				$sender_name = $show_in_customer_email_array[0]['name'];
				$sender_email = $show_in_customer_email_array[0]['email'];
			} else {
				$sender_name = STORE_OWNER;
				$sender_email = STORE_OWNER_EMAIL_ADDRESS;
			}
			
			// admin's email
			if (tep_not_null($get_events_row['events_admin_copy_email'])) {
				$admin_email_name_n_address_array = $this->tep_parse_email_string($get_events_row['events_admin_copy_email']);
			} else {
				$admin_email_name_n_address_array[] = '"' . STORE_OWNER . '"<' . STORE_OWNER_EMAIL_ADDRESS . '>';
			}
			
		} else {
			$error = true;
		}
		
		if ($error == false) {
			$event_info_select_sql = "	SELECT events_name, events_email_tpl
										FROM " . TABLE_EVENTS_DESCRIPTION . " 
										WHERE events_id = '" . $get_events_row['events_id'] . "' 
											AND events_name <> '' 
											AND (IF (language_id = '" . (int)$languages_id . "', 1, IF(( SELECT COUNT(events_id) > 0 
																										FROM " . TABLE_EVENTS_DESCRIPTION . " 
																										WHERE events_id = '" . $get_events_row['events_id'] . "' 
																											AND language_id = '" . (int)$languages_id . "' 
																											AND events_name <> ''), 0, language_id = '" . (int)$default_languages_id . "')))";
			$event_info_result_sql = tep_db_query($event_info_select_sql);
			$event_info_row = tep_db_fetch_array($event_info_result_sql);
			
			$customers_info_select_sql = "	SELECT c.customers_firstname, c.customers_lastname, c.customers_email_address, c.customers_telephone, c.customers_default_address_id, co.countries_international_dialing_code 
											FROM " . TABLE_CUSTOMERS . " AS c
											LEFT JOIN " . TABLE_COUNTRIES . " AS co
												ON (co.countries_id = c.customers_country_dialing_code_id) 
											WHERE c.customers_id = '" . tep_db_input($_SESSION['customer_id']) . "'";
			$customers_info_result_sql = tep_db_query($customers_info_select_sql);
			if ($customers_info_row = tep_db_fetch_array($customers_info_result_sql)) {
				$customer_email = $customers_info_row['customers_email_address'];
				$customer_name = $customers_info_row['customers_firstname'] . ' ' . $customers_info_row['customers_lastname'];
				$customer_tel = $customers_info_row['countries_international_dialing_code'] . $customers_info_row['customers_telephone'];
			}
			$customer_address = tep_address_label($_SESSION['customer_id'], $customers_info_row['customers_default_address_id']);
			
			$email_text .= '<table border="0" width="100%">';
			
			$email_text .= $order_no_row;
			
			$email_text .= '<tr>
								<td align="right" width="130">ID : </td>
								<td>' . $_SESSION['customer_id'] . '</td>
							</tr>';
			$email_text .= '<tr>
								<td align="right">Name : </td>
								<td>' . $customer_name . '</td>
							</tr>';
			$email_text .= '<tr>
								<td align="right">Email : </td>
								<td>' . $customer_email . '</td>
							</tr>';
			$email_text .= '<tr>
								<td align="right">Telephone No : </td>
								<td>' . $customer_tel . '</td>
							</tr>';
			$email_text .= '<tr>
								<td align="right">Address : </td>
								<td>' . $customer_address . '</td>
							</tr>';
			
			$email_text .= '<tr>
								<td align="right">Language Selection: </td>
								<td>' . $_SESSION['language'] . '</td>
							</tr>';
			
			$email_text .= '<tr height="20">
								<td align="right">&nbsp;</td>
								<td>&nbsp;</td>
							</tr>';
			$email_text .= '</table>';
			
			$email_text .= '<table border="0" width="100%">';
			if (tep_not_null($post_array)) {
				foreach ($post_array as $post_key => $post_value) {
					if (is_array($post_value)) {
						if (array_key_exists($post_key, $files_array)) {
							if ($files_array[$post_key]['error']['input_content'] == 0) {
								$files_tmp_name[] = $files_array[$post_key]['tmp_name']['input_content'];
							}
						} else {
							if (tep_not_null($post_array[$post_key]['input_content'])) {
								if (substr($post_key,0,8) == 'content_') {
									$field_content = tep_db_prepare_input($post_array[$post_key]['input_content']);
									
									$email_text .= '<tr>
														<td align="right" width="130">'.$post_array[$post_key]['events_name'].' : </td>
														<td>'.$field_content.'</td>
													</tr>';
													
									$csv_str_array[] = str_replace(array('"', '<br>', '<BR>'), array('""', ''), $field_content);
								}
							}
						}
					}
				}
			}
			$email_text .= '</table>';
			if (count($csv_str_array))	$email_text .= '<br>"'.implode('", "', $csv_str_array).'"';
			
			$email_subject = tep_db_prepare_input($event_info_row['events_name']);
			$customer_name = tep_db_prepare_input($customer_name);
			$customer_email = tep_db_prepare_input($customer_email);
			$char_name = tep_db_prepare_input($char_name);
			$email_sender = tep_db_prepare_input($email_sender);
			
			$customer_email_name_n_address_array[] = '"' . $customer_name . '"<' . $customer_email . '>';
			preg_match_all("/(##)([0-9]+)(##)/", $event_info_row['events_email_tpl'], $catches, PREG_SET_ORDER);
			foreach ($catches as $val) {
				$full_path_array[] = "/".$val[0]."/";
				$option_id_array[] = $this->get_option_content_by_opt_id($val[2], $post_array);
			}
			$new_email_temp = nl2br(preg_replace($full_path_array, $option_id_array, $event_info_row['events_email_tpl']));
			foreach ($files_tmp_name as $files_value) {
				$file = fopen($files_value, 'rb');
				$data[] = fread($file, filesize($files_value));
				fclose($file);
			}
			$configuration_query = tep_db_query('select configuration_key as cfgKey, configuration_value as cfgValue from ' . TABLE_CONFIGURATION);
			
			// Send to OGM admin ## email subject from db
			$this->send_confirmation_email($admin_email_name_n_address_array, implode(' ', array(EMAIL_SUBJECT_PREFIX, $email_subject)), $email_text, $data, $customer_name, $customer_email);
			
			// Send to Customer ## email subject from define language
			$this->send_confirmation_email($customer_email_name_n_address_array, implode(' ', array(EMAIL_SUBJECT_PREFIX, CUSTOMER_NOTIFICATION_EVENT_SUBJECT)), $new_email_temp, '', $sender_name, $sender_email);
			$messageStack->add_session('index', EVENT_SUBMIT_SUCCESS, 'success');
			
			return true;
		} else {
			return false;
		}
	}
	
	function send_confirmation_email($admin_email_name_n_address_array, $email_subject, $email_text, $attachment, $from_email_name, $from_email_address, $bcc_email_address='') {
		
		if (SEND_EMAILS != 'true') return false;
		$mail = new htmlMimeMail();
		$mail->setHeadCharset('utf-8');
		$mail->setHtmlCharset('utf-8');
		$mail->setSubject($email_subject);
		$mail->setHtml($email_text);
		if(tep_not_null($attachment)) {
			foreach ($attachment as $attachment_value) {
				$mail->addAttachment($attachment_value, 'event', 'image/jpeg');
			}
		}
		$mail->setFrom($from_email_address);
		
		if (EMAIL_TRANSPORT == 'smtp') {
			//$result = $mail->send(array('"' . $to_name . '"<' . $to_email_address . '>'), 'smtp');
			$result = $mail->send($admin_email_name_n_address_array, 'smtp');
		} else {
			$result = $mail->send($admin_email_name_n_address_array);
		}
		
	}

	function field_creator ($event_option_row, $field_name_array, $languages_id, $default_languages_id) {
		$options_value_array = array();
		$options_sub_sort_order_array = array();
		
		$store_data = '<table border="0" width="100%" cellpadding="0" cellspacing="0">';
		
		$note = tep_not_null($event_option_row['events_options_note']) ? '<div>( <i>'.$event_option_row['events_options_note'].'</i> )</div>' : '';
		$required = $event_option_row['events_options_required'] == '1' ? '<span class="redIndicator">*</span>' : '';
		$note_2 = tep_not_null($event_option_row['events_options_note']) ? '<tr><td>(<i>'.$event_option_row['events_options_note'].'</i> )</td></tr>' : '';
		
		switch ($event_option_row['events_options_input_type']) {
			case '1' :
				$display = tep_draw_input_field($field_name_array, '', 'size="'.$event_option_row['events_options_row_size'].'" class="inputBoxContentsNew" maxlength="'.$event_option_row['events_options_max_size'].'"').$required .$note;
				$share_display = true;
				
				break;
			case '2' :
				$display = tep_draw_textarea_field($field_name_array, "", $event_option_row['events_options_column_size'], $event_option_row['events_options_row_size'], "", 'id="'.$field_name_array.'" style="background:white;" onkeyup="limitChars(this, \''.$event_option_row['events_options_max_size'].'\', \'count_'.$field_name_array.'\', \''.CHAR_LEFT.'\')"').$required.$note;
				$display_2 .= 	'<div id="count_'.$field_name_array.'">'.$event_option_row['events_options_max_size'].' '.CHAR_LEFT.'</div>';
				$share_display = true;
				
				break;
			case '3' :
			case '4' :
				$get_options_value_select_sql = "	SELECT events_options_values
													FROM " . TABLE_EVENTS_OPTIONS_VALUES . "
													WHERE events_options_id = '" . tep_db_prepare_input($event_option_row['events_options_id']) . "'
														AND (IF (language_id = '" . (int)$languages_id . "', 1, IF(( SELECT COUNT(inner_eov.events_options_id) > 0 
																															FROM " . TABLE_EVENTS_OPTIONS_VALUES . " as inner_eov
																															WHERE inner_eov.events_options_id = '" . tep_db_prepare_input($event_option_row['events_options_id']) . "' 
																																AND inner_eov.events_options_values <> ''
																																AND inner_eov.language_id = '" . (int)$languages_id . "'), 0, language_id = '" . (int)$default_languages_id . "')))
													ORDER BY events_options_values_sort_order";
				
				$get_options_value_result_sql = tep_db_query($get_options_value_select_sql);
				$radioOptCnt = 0;
				
				while ($get_options_value_row = tep_db_fetch_array($get_options_value_result_sql)) {
					if($event_option_row['events_options_input_type'] == '3'){
						$store_data .= '<tr>
											<td class="inputField">
												'.tep_draw_radio_field($field_name_array, $get_options_value_row['events_options_values'], $radioOptCnt == 0 ? true : false).''.$get_options_value_row['events_options_values'].'
											</td>
										</tr>';
						$radioOptCnt++;
					} elseif ($event_option_row['events_options_input_type'] == '4'){
						$input_type_array[] = array("id" => $get_options_value_row['events_options_values'], "text" => $get_options_value_row['events_options_values'], "param" => '');
					}
				}
				
				if($event_option_row['events_options_input_type'] == '4'){
					$store_data .= '<tr>
										<td>
										'.tep_draw_pull_down_menu($field_name_array, $input_type_array, "").'
										</td>
									</tr>';
				}
				$store_data .= $note_2;
				$share_display = false;
				
				break;
			case '5' :
				$display = $event_option_row['events_options_name'];
				$share_display = true;
				
				break;
			case '6' :
				$max_size_in_binary = $event_option_row['events_options_max_size'] * 1024;
				define('EVENT_SCREENSHOT_MAX_SIZE', $max_size_in_binary);
				$display = tep_draw_input_field($field_name_array, '', '', 'file').$required.$note;	
				$share_display = true;
				
				break;
		}
		
		if ($share_display) {
			$store_data .= '<tr>
						<td class="inputField">
							'.$display.'
							'.(tep_not_null($display_2) ? $display_2 : '').'
						</td>
					</tr>';
		}
		
		$store_data .= '</table>';
		return $store_data;
	}

	function validation ($require_to_be_validate_array, $post_data, $files_data, $events_id, &$messageStack, $lang_id, $default_lang) {
		
		$no_err = true;
		//EVENT_REQUIRED_FIELD_ERROR
		foreach ($post_data as $post_key => $post_value) {
			$ss_tmp_name = $files_data[$post_key]['tmp_name']['input_content'];
			$ss_type 	 = $files_data[$post_key]['type']['input_content'];
			$ss_name 	 = $files_data[$post_key]['name']['input_content'];
			$ss_size 	 = $files_data[$post_key]['size']['input_content'];
			$ss_error	 = $files_data[$post_key]['error']['input_content'];
			
			if ($ss_size > ($post_data[$post_key]['max_size'] * 1024)) {
				$no_err = false;
				$messageStack->add_session('event', EVENT_SS_SIZE_EXCEED_LIMIT_ERROR);
			}
			
			if($ss_error > 0) {
				if ($ss_error != 4) {
					$no_err = false;
					if($ss_error == 1 || $ss_error == 2) {
						$messageStack->add_session('event', EVENT_SS_SIZE_EXCEED_LIMIT_ERROR);
					}	else {
						$messageStack->add_session('event', EVENT_SS_ERROR);
					}
				}
			}
			
			if (file_exists($ss_tmp_name)) {
				if (is_uploaded_file($ss_tmp_name)) {
					$ss_path_part = pathinfo($ss_name);
					if (strtolower($ss_path_part['extension']) != 'jpg' && strtolower($ss_path_part['extension']) != 'jpeg') {
						$messageStack->add_session('event', EVENT_SS_EXTENSION_ERROR);
					}
				}
			}
			
			if (in_array($post_key, $require_to_be_validate_array)) {
				if (array_key_exists($post_key, $files_data)) {
					if ($ss_error == 4) {
						$no_err = false;
						$error_msg = $this->retrieve_err_msg($post_data[$post_key]['events_option_id'],$lang_id, $default_lang);
						$messageStack->add_session('event', $error_msg);
					} elseif($no_err) {
						// If user doesnt upload the file, but system require. (Display Error)
						if (!file_exists($ss_tmp_name)) {
							if (!tep_not_null($post_data[$post_key]['input_content']) && ($post_data[$post_key] != $post_data['order_no'])) {
								$no_err = false;
								if ($error_msg = $this->retrieve_err_msg($post_data[$post_key]['events_option_id'], $lang_id, $default_lang)) {
									$messageStack->add_session('event', $error_msg);
								} else {
									$error_msg =  $post_data[$post_key]['events_name']." ".ERROR_CANNOT_LEFT_BLANK;
									$messageStack->add_session('event', $error_msg);
								}
							}
						}
					}
				} else {
					// If user doesnt input the value, and system require. (Display Error)
					if (!tep_not_null($post_data[$post_key]['input_content']) && ($post_data[$post_key] != $post_data['order_no'])) {
						$no_err = false;
						if ($error_msg = $this->retrieve_err_msg($post_data[$post_key]['events_option_id'], $lang_id, $default_lang)) {
							$messageStack->add_session('event', $error_msg);
						} else {
							$error_msg =  $post_data[$post_key]['events_name']." ".ERROR_CANNOT_LEFT_BLANK;
							$messageStack->add_session('event', $error_msg);
						}
					}
				}
			}
		}
		
		if ($no_err) {
			return true;
		} else {
			return false;
		}
	}

	function retrieve_err_msg ($id, $lang_id, $default_lang) {
		$get_err_msg_select_sql = "	SELECT events_options_err_msg
									FROM " . TABLE_EVENTS_OPTIONS_DESCRIPTION . "
									WHERE events_options_id = '" . tep_db_input($id) . "'
		 								AND ( IF(language_id = '". $lang_id ."', 1,
			 								  IF ((SELECT COUNT(edo_inner.events_options_id) > 0
			 								  		FROM " . TABLE_EVENTS_OPTIONS_DESCRIPTION . " as edo_inner
			 										WHERE edo_inner.events_options_id = '" . tep_db_input($id) . "'
			 											AND edo_inner.events_options_err_msg <> ''
			 											AND edo_inner.language_id ='". $lang_id. "'), 0,language_id = '".$default_lang."')
		 											)
		 										 )";
		
		$get_err_msg_result_sql = tep_db_query($get_err_msg_select_sql);
		if ($get_err_msg_row = tep_db_fetch_array($get_err_msg_result_sql)) {
			return $get_err_msg_row['events_options_err_msg'];
		} else {
			return false;
		}
	}
	
	function get_option_content_by_opt_id ($opt_id, $post_array) {
		foreach ($post_array as $post_key => $post_value) {
			if ($post_array[$post_key]['events_option_id'] == $opt_id) {
				if (tep_not_null($post_array[$post_key]['input_content'])) {
					if (substr($post_key,0,8) == 'content_') {
						return tep_db_prepare_input($post_array[$post_key]['input_content']);
					}
				}
			}
		}
	}
}
?>