<?
/*
  	$Id: file_manager.php,v 1.2 2006/07/25 07:18:26 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2005 SKC Ventrue
	
  	Released under the GNU General Public License
*/

/*******************************************************
	Class to handle file/directory manipulation
*******************************************************/
class file_manager
{
	var $debug_mode = false;
	var $source;
	
	function file_manager() {
		tep_set_time_limit(0);
		//$this->source = $pwd;
	}
	
	function copyFile($source, $dest) {
		if (!@copy($source, $dest))
			return $this->_error("Unable to copy file \"$source\" to \"$dest\".");
	 	
		return true;
	}
	
	function removeDirectory($dirname) {
		if (substr($dirname, -1) != '/') $dirname .= '/';
		
		if (false === ($files = $this->_getDirectory($dirname)))
			return $this->_error("Unable to access directory: $dirname");
	 	
		// Delete files.
		foreach ($files['files'] as $file) {
			if (!@unlink($dirname . $file))
				return	$this->_error("Unable to delete file: $dirname$file");
		}
	 	
		// Recursively delete subdirectories.
		foreach ($files['dirs'] as $dir) {
			$this->removeDirectory($dirname . $dir);
	 	}
	 	
	 	if (!@rmdir("$dirname")) 
            return $this->_error("Unable to remove directory: $dirname");
		return true;
	}
	
	function _getDirectory($directory) {
		if (substr($directory, -1) != '/') $destination .= '/';
	 	
		$result = array('dirs' => array(), 'files' => array());
	 	
		if (false === ($handle = @opendir($directory)))
			return false;
	 	
		while (false !== ($file = readdir($handle))) {
			if ($file == '.' || $file == '..')	continue;
	 		
			$fullPath = $directory . $file;
	 		
			if (is_dir($fullPath)) {
				$result['dirs'][] = $file;
			} else {
				if (!file_exists($fullPath)) 	continue; 
				$result['files'][] = $file;
			}
		}
	 	
		closedir($handle);
	 	
		sort($result['dirs']);
		sort($result['files']);
	 	
		return $result;
	}
	
	function _error($message) {
		global $messageStack;
		$messageStack->add_session($message, 'error');
		return false;
		/*
		echo "\n$message";
		exit(1);
		*/
	}
	
	function createDirectory($dirname) {
		if (!@mkdir($dirname, 0775))
			return false;
		else
			return true;
	}
	
	function touchDirectory($dirname) {
	    clearstatcache ();
		
		if (strstr($dirname, ":/")) {
			// Windows system path name must be using backslash Eg: C:/Htdocs/test/
			$Prefix = substr($dirname,0,2) ;
			$tmpStr = explode("/", substr($dirname,2)) ; // strip first slash
		} else {
			$Prefix = "" ;
	    	$tmpStr = explode("/", substr($dirname,1)) ; // strip first slash
	    }
	    $Str = "$Prefix" ;
	    foreach ($tmpStr as $p) {
	        $Str .= "/$p" ;
	        if ($this->debug_mode)	echo "<span class=error>Checking dir, $Str!</span><br>\n" ;	            
	        if (!@is_dir($Str)) {
	            if ($this->debug_mode)	echo "<span class=error>Making dir, ($Str)!</span><br>\n" ;	            	        
	            if (!@mkdir($Str,0775)) {
	                if ($this->debug_mode) echo "<span class=error>Unable to create directory, $Str!</span><br>\n" ;
		 		    return false;
		 		}		 		
		    }
	    }
	    return true;
	}
	
	function fileUpload($file_obj, $file_id, $destination, $allowed_ext=array(), $overwrite=false) {
		global $messageStack;
		
		if (isset($file_obj)) {
        	$file = array(	'name' => $file_obj['name'][$file_id],
                      		'type' => $file_obj['type'][$file_id],
                      		'size' => $file_obj['size'][$file_id],
                      		'tmp_name' => $file_obj['tmp_name'][$file_id]);
      	}
      	
      	if ( tep_not_null($file['tmp_name']) && ($file['tmp_name'] != 'none') && is_uploaded_file($file['tmp_name']) ) {
      		if ($file["size"] > 0) {
	        	if (count($allowed_ext) > 0) {
	          		if (!in_array(strtolower(substr($file['name'], strrpos($file['name'], '.')+1)), $allowed_ext)) {
	              		$messageStack->add_session(ERROR_FILETYPE_NOT_ALLOWED, 'error');
	            		return false;
	          		}
	        	}
			} else {
          		$messageStack->add_session(ERROR_NO_UPLOAD_FILE, 'error');
        		return false;
			}
      	} else {
          	$messageStack->add_session(WARNING_NO_FILE_UPLOADED, 'warning');
        	return false;
      	}
      	
      	if (substr($destination, -1) != '/') $destination .= '/';
      	if (!@is_dir($destination)) {
			$this->touchDirectory($destination);
		}
		$path_parts = pathinfo($file_obj['name'][$file_id]);
		$ext = strtolower($path_parts['extension']);
		
		$new_filename = $file_id . '.' . $ext;
		if ($overwrite) {
			if (($sourceList = $this->_getDirectory($destination)) !== false) {
				for($dup_cnt=0; $dup_cnt < count($sourceList['files']); $dup_cnt++) {
					$tmp_path_parts = pathinfo($sourceList['files'][$dup_cnt]);
					if (basename($tmp_path_parts['basename'], '.'.$tmp_path_parts['extension']) === $file_id) {
						$oldPermission = @umask(0);
						@chmod($destination . $sourceList['files'][$dup_cnt], 0777);
						@umask($oldPermission);
						@unlink("$destination" . $sourceList['files'][$dup_cnt]);
					}
				}
			}
		}
      	if (@move_uploaded_file($file['tmp_name'], $destination . $new_filename)) {
        	$oldPermission = @umask(0);
			@chmod($destination . $new_filename, 0777);
			@umask($oldPermission);
		} else {
			$messageStack->add_session("Unable to move '".$file['tmp_name']."' to '".$destination . $new_filename."'" , 'warning');
        	return false;
		}
	}
	
	function fileExists($dir, $filname, $ignoreExt = false) {
		$found = false;
		if (($sourceList = $this->_getDirectory($dir)) !== false) {
			for($file_cnt=0; $file_cnt < count($sourceList['files']); $file_cnt++) {
				$tmp_path_parts = pathinfo($sourceList['files'][$file_cnt]);
				$compare_filename = ($ignoreExt == true) ? basename($tmp_path_parts['basename'], '.'.$tmp_path_parts['extension']) : basename($tmp_path_parts['basename']);
				if ($compare_filename === $filname) {
					$found = $tmp_path_parts['basename'];
					break;
				}
			}
		}
		
		return $found;
	}
	
	function sync($source, $destination) {
		global $options;
		
		// Get array of files and directories in source.
		if (false === ($sourceList = $this->getDirectory($source)))
			$this->_error("Invalid source directory: $source");
	 	
		// Attempt to create destination if it doesn't exist.
		if (!is_dir($destination)) {
			echo $this->debug_mode ? "Creating directory $destination\n" : '';
	 		
			if (!$this->createDirectory($destination))
				$this->_error("Unable to create directory: $destination");
		}
	 	
		// Get array of files and directories in destination.
		if (false === ($destList = $this->getDirectory($destination)))
			$this->_error("Invalid destination directory: $destination");
	 	
		// Remove orphaned subdirectories.
		$orphanedDirs = array_diff($destList['dirs'], $sourceList['dirs']);
	 	
		foreach ($orphanedDirs as $key => $dir) {
			unset($destList['dirs'][$key]);
			$this->removeDirectory("$destination/$dir");
		}
	 	
		// Remove orphaned files.
		$orphanedFiles = array_diff($destList['files'], $sourceList['files']);
	 
		foreach ($orphanedFiles as $key => $file) {
			unset($destList['files'][$key]);
	 		
			if (!@unlink("$destination/$file"))
				$this->_error("Unable to remove file: $destination/$file");
		}
	 	
		// Synchronize existing files.
		foreach ($destList['files'] as $file) {
			if (filesize("$source/$file") != filesize("$destination/$file")) {
				echo $this->debug_mode ? "Updating $destination/$file\n" : '';
				$this->copyFile("$source/$file", "$destination/$file");
			}
		}
	 	
		// Create missing files.
		$missingFiles = array_diff($sourceList['files'], $destList['files']);
	 
		foreach ($missingFiles as $file) {
			echo $this->debug_mode ? "Adding $destination/$file\n" : '';
			$this->copyFile("$source/$file", "$destination/$file");
		}
	 	
		// Recursively synchronize subdirectories.
		foreach ($sourceList['dirs'] as $dir)
			$this->sync("$source/$dir", "$destination/$dir");
	}
}
?>