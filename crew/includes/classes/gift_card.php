<?php

require_once(DIR_WS_CLASSES . 'curl.php');

class gift_card {

    private $action;
    protected $merchant_code;
    protected $secret_key;
    private $pin_url;
    private $scope;
    private $topup_diff_range = 20;
    private $pin_code_info_array;
    public $purchase_info;
    public $pin_code_array;

    function __construct($post_data = NULL) {
        $this->merchant_code = PIN_MERCANT_CODE;
        $this->secret_key = PIN_SECRET_KEY;
        $this->pin_url = HTTPS_PIN_SERVER;
        $this->post_array = array();
        $this->pin_code_info_array = array();
        $this->pin_code_array = array();
        $this->setPostData($post_data);
    }

    private function authenticate() {
        $result = array();
        $this->action = 'api_auth.php';
        $this->hash = time();
        $this->auth = md5($this->merchant_code . $this->secret_key . $this->hash);
        $this->post_array = array('merchant_code' => $this->merchant_code,
            'auth' => $this->auth,
            'hash' => $this->hash,
            'scope' => $this->scope
        );
        $result = $this->curl();

        return $result;
    }

    public function getPostData($key, $default = '') {
        return isset($this->purchase_info[$key]) ? $this->purchase_info[$key] : $default;
    }

    private function setPostData($post_data) {
        if (isset($post_data['proceed_gc_checkout'])) {
            if (isset($_SESSION['gc_purchase_info'])) {
                $post_data = $_SESSION['gc_purchase_info'];

                unset($_SESSION['gc_purchase_info']);
            }
        }

        if (isset($post_data['tab'])) {
            $this->purchase_info['tab'] = (int) $post_data['tab'];
        }

        if (isset($post_data['sno'])) {
            $this->purchase_info['sno'] = tep_db_prepare_input($post_data['sno']);
        }

        if (isset($post_data['pcode'])) {
            $this->purchase_info['pcode'] = tep_db_prepare_input($post_data['pcode']);
        }

        if (isset($post_data['qty'])) {
            $this->purchase_info['qty'] = (int) $post_data['qty'];
        }

        if (isset($post_data['game_id'])) {
            $this->purchase_info['game_id'] = tep_db_prepare_input($post_data['game_id']);
        }

        if (isset($post_data['product_id'])) {
            $this->purchase_info['product_id'] = tep_db_prepare_input($post_data['product_id']);
        }

        if (isset($post_data['game_qty'])) {
            $this->purchase_info['game_qty'] = (int) $post_data['game_qty'];
        }

        if (isset($post_data['game_dm'])) {
            $this->purchase_info['game_dm'] = (int) $post_data['game_dm'];
        }

        if (isset($post_data['game_info'])) {
            $this->purchase_info['game_info'] = tep_db_prepare_input($post_data['game_info']);
        }
    }

    public function redeem($pin, $serial) {
        $result = array();
        $this->scope = 'redeem';
        $auth = $this->authenticate();
        if (isset($auth['token']) && !empty($auth['token'])) {
            $this->action = 'api_redeem.php';
            $this->post_array = array('pin' => $pin,
                'serial' => $serial,
                'token' => $auth['token']
            );
            $result = $this->curl();
        }

        return $result;
    }

    public function get_balance($pin, $serial) {
        $result = array();

        if (!empty($serial) && !empty($pin)) {
            if (!isset($this->pin_code_info_array[$pin . $serial])) {
                $this->scope = 'getPinInfo';
                $auth = $this->authenticate();

                if (isset($auth['token']) && !empty($auth['token'])) {
                    $this->action = 'api_get_balance.php';
                    $this->post_array = array('pin' => $pin,
                        'serial' => $serial,
                        'token' => $auth['token']
                    );
                    $result = $this->curl();
                }

                if (empty($result) || !isset($result['status']) || !isset($result['redeem'])) {
                    $result = array('error_key' => 'USER_GIFTCARD_UNKNOWN_ERROR');
                } else {
                    if (isset($result['error']) && !empty($result['error'])) {
                        $result = array('error_key' => 'USER_GIFTCARD_ERROR');
                    } else if ($result['status'] == 0) {
                        $result = array('error_key' => 'USER_GIFTCARD_DEACTIVATED');
                    } else if ($result['redeem'] == 1) {
                        $result = array('error_key' => 'USER_GIFTCARD_REDEEMED');
                    } else {
                        $start_date = strtotime($result['start_date'] . ' 00:00:00');
                        $end_date = strtotime($result['end_date'] . ' 23:59:59');

                        if (time() < $start_date || time() > $end_date) {
                            $result = array('error_key' => 'USER_GIFTCARD_DEACTIVATED');
                        } else if (!$this->getCurrenciesObj()->get_id_by_code($result['currency'])) {
                            // check gift card currency
                            $result = array('error_key' => 'USER_GIFTCARD_CURRENCY_INVALID');
                        }
                    }
                }

                $this->pin_code_info_array[$pin . $serial] = $result;
            }

            $result = $this->pin_code_info_array[$pin . $serial];
        } else {
            $result = array('error_key' => 'USER_GIFTCARD_INFO_MISSING');
        }

        return $result;
    }

    private function getCurrenciesObj() {
        if (!is_object($this->currency)) {
            include_once(DIR_WS_CLASSES . 'currencies.php');
            $this->currency = new currencies();
        }

        return $this->currency;
    }

    public function get_customer_gc_info($original_gc_deno, $original_gc_redeem_deno, $original_gc_currency_code, $default_currency_code, $customer_id = 0) {
        include_once(DIR_WS_CLASSES . 'currencies.php');

        $sc_balance_array = array();
        $currencies = $this->getCurrenciesObj();
        $redeem_gc_deno = $original_gc_redeem_deno;
        $redeem_gc_currency_id = $original_gc_currency_id = $currencies->get_id_by_code($original_gc_currency_code);

        if ($customer_id) {
            $sc_obj = new store_credit($customer_id);
            $sc_balance_array = $sc_obj->get_current_credits_balance($customer_id);
        }

        if (count($sc_balance_array) > 0) { // With SC
            $redeem_gc_currency_code = $currencies->get_code_by_id($sc_balance_array['sc_currency_id']);

            if ($redeem_gc_currency_code != $original_gc_currency_code) {
                $redeem_gc_deno = $currencies->advance_currency_conversion($original_gc_redeem_deno, $original_gc_currency_code, $redeem_gc_currency_code, true, 'sell');
                $redeem_gc_currency_id = $currencies->get_id_by_code($redeem_gc_currency_code);
            }

            $sc_topup_precheck_currency_code = $currencies->get_code_by_id($sc_balance_array['sc_currency_id']);
            $sc_topup_precheck_total_sc = ($sc_balance_array['sc_reverse'] + $sc_balance_array['sc_irreverse']) - ($sc_balance_array['sc_reverse_reserve_amt'] + $sc_balance_array['sc_irreverse_reserve_amt']);

            if ($redeem_gc_currency_code != DEFAULT_CURRENCY) {
                $sc_topup_precheck_value = $currencies->advance_currency_conversion($sc_topup_precheck_total_sc, $sc_topup_precheck_currency_code, DEFAULT_CURRENCY, true, 'sell');
            } else {
                $sc_topup_precheck_value = $sc_topup_precheck_total_sc;
            }
        } else {    // Without SC
            $redeem_gc_currency_code = $default_currency_code != '' ? $default_currency_code : DEFAULT_CURRENCY;

            if ($redeem_gc_currency_code != $original_gc_currency_code) {
                $redeem_gc_deno = $currencies->advance_currency_conversion($original_gc_redeem_deno, $original_gc_currency_code, $redeem_gc_currency_code, true, 'sell');
                $redeem_gc_currency_id = $currencies->get_id_by_code($redeem_gc_currency_code);
            }

            $sc_topup_precheck_value = 0;
        }

        return array(
            'status' => 1,
            'original_gc_deno' => $original_gc_deno,
            'original_gc_currency_id' => $original_gc_currency_id,
            'original_gc_currency_code' => $original_gc_currency_code,
            'original_gc_deno_formatted' => $currencies->format($original_gc_redeem_deno, true, $original_gc_currency_code, 1),
            'original_gc_redeem_deno' => $original_gc_redeem_deno,
            'redeem_gc_deno' => $redeem_gc_deno,
            'redeem_gc_currency_id' => $redeem_gc_currency_id,
            'redeem_gc_currency_code' => $redeem_gc_currency_code,
            'redeem_gc_deno_formatted' => $currencies->format($redeem_gc_deno, true, $redeem_gc_currency_code, 1),
            'sc_topup_precheck_value' => $sc_topup_precheck_value
        );
    }

    private function curl() {
        $response = '';

        $curl_obj = new curl();
        $response = $curl_obj->curl_post($this->pin_url . $this->action, $this->post_array);

        if ($response === false) {
            $response = '';
        }

        return json_decode($response, TRUE);
    }

    function check_gift_card_info($serial, $pin, $customer_id = 0, $currency_code = '') {
        $gift_card_info = array();

        $gift_card_balance = $this->get_balance($pin, $serial);

        if (isset($gift_card_balance['error_key'])) {
            return array('status' => FALSE, 'error_key' => $gift_card_balance['error_key']);
        } else {
            $gift_card_info = $this->get_customer_gc_info($gift_card_balance['deno'], $gift_card_balance['deno'], $gift_card_balance['currency'], $currency_code, $customer_id);

            if (!empty($gift_card_info)) {
                return $gift_card_info;
            } else {
                return array('status' => FALSE, 'error_key' => 'USER_GIFTCARD_ERROR');
            }
        }
    }

    function redeem_gift_card($pin, $serial, $customer_id, $currency_code = '') {
        $gift_card_currency_id = '';
        $sc_top_up_info = array();

        $gift_card_balance = $this->get_balance($pin, $serial);

        if (isset($gift_card_balance['error_key'])) {
            return array('error_key' => $gift_card_balance['error_key']);
        } else {
            if ($gift_card_balance['redeem'] == 0 && $gift_card_balance['deno'] > 0 && $gift_card_balance['status'] == 1) {
                $original_gc_deno = $gift_card_balance['deno'];
                $original_gc_currency_code = $gift_card_balance['currency'];

                $redeem_gift_card = $this->redeem($pin, $serial);

                if (isset($redeem_gift_card['success']) && !empty($redeem_gift_card['success']) && $redeem_gift_card['success'] === 'Redeem Success') {
                    $redeem_gc_info_arr = $this->get_customer_gc_info($original_gc_deno, $original_gc_deno, $original_gc_currency_code, $currency_code, $customer_id);
                    $redeem_gc_info_arr['status'] = 'success_redeemed'; // update status only

                    return $redeem_gc_info_arr;
                } else {
                    return array('error_key' => 'USER_REDEEM_GIFTCARD_FAILURE');
                }
            } else {
                return array('error_key' => 'USER_GIFTCARD_UNKNOWN_ERROR');
            }
        }
    }

    function add_gift_card_value_into_store_credit($gift_card_data, $pin, $serial, $ip, $customer_id, $email_address = '') {
        $original_gc_deno = $gift_card_data['original_gc_deno'];
        $original_gc_currency_id = $gift_card_data['original_gc_currency_id'];
        $original_gc_currency_code = $gift_card_data['original_gc_currency_code'];
        $original_gc_redeem_deno = $gift_card_data['original_gc_redeem_deno'];
        $redeem_gc_deno = $gift_card_data['redeem_gc_deno'];
        $redeem_gc_currency_code = $gift_card_data['redeem_gc_currency_code'];
        $redeem_gc_currency_id = $gift_card_data['redeem_gc_currency_id'];
        $sc_topup_precheck_value = $gift_card_data['sc_topup_precheck_value'];

        $currencies = $this->getCurrenciesObj();
        $sc_obj = new store_credit($customer_id);

        if ($redeem_gc_currency_code != DEFAULT_CURRENCY) {
            $gc_value_to_USD = $currencies->advance_currency_conversion($redeem_gc_deno, $redeem_gc_currency_code, DEFAULT_CURRENCY, true, 'sell');
        } else {
            $gc_value_to_USD = $redeem_gc_deno;
        }

        $trans_array = array(
            'type' => '',
            'id' => '',
            'act_type' => 'GC',
            'user_id' => $customer_id,
            'add_amount' => $redeem_gc_deno,
            'sc_type' => 'NR',
            'show_desc' => '0',
            'added_by' => $email_address,
            'added_by_role' => 'customers',
            'currency_id' => $redeem_gc_currency_id
        );

        $sc_top_up_info = $sc_obj->miscellaneous_add_amount($trans_array, 'Gift Card Redemption');
        $sc_topup_postcheck_value_array = $sc_obj->get_current_credits_balance($customer_id);
        $sc_postcheck_currency_code = $currencies->get_code_by_id($sc_topup_postcheck_value_array['sc_currency_id']);
        $sc_postcheck_total_sc = ($sc_topup_postcheck_value_array['sc_reverse'] + $sc_topup_postcheck_value_array['sc_irreverse']) - ($sc_topup_postcheck_value_array['sc_reverse_reserve_amt'] + $sc_topup_postcheck_value_array['sc_irreverse_reserve_amt']);

        if ($sc_postcheck_currency_code != DEFAULT_CURRENCY) {
            $sc_topup_postcheck_value = $currencies->advance_currency_conversion($sc_postcheck_total_sc, $sc_postcheck_currency_code, DEFAULT_CURRENCY, true, 'sell');
        } else {
            $sc_topup_postcheck_value = $sc_postcheck_total_sc;
        }

        $added_sc_in_USD = $sc_topup_postcheck_value - $sc_topup_precheck_value;
        if (abs($added_sc_in_USD - $gc_value_to_USD) > $this->topup_diff_range) {
            $mail_content = '   Gift Card Pin Number : ' . $pin . ' 
                                Gift Card Serial Number : ' . $serial . ' 
                                Gift Card Value : ' . $redeem_gc_currency_code . ' ' . $redeem_gc_deno . '
                                Customer ID :' . $customer_id . '
                                DateTime : ' . time();

            $headers = 'To: <EMAIL>' . "\r\n" .
                    'From: <EMAIL>' . "\r\n" .
                    'Reply-To: <EMAIL>' . "\r\n" .
                    'X-Mailer: PHP/' . phpversion();
            mail("<EMAIL>", "[OFFGAMERS] GiftCard TopUp Suspicious Value", $mail_content, $headers);
        }

        if (!empty($sc_top_up_info)) {
            $gc_redeem_track_array = array(
                'customers_id' => $customer_id,
                'transaction_id' => $sc_top_up_info['sc_trans_id'],
                'serial_number' => $serial,
                'pin_number' => $pin,
                'gift_card_deno' => $original_gc_deno,
                'gift_card_currency_id' => $original_gc_currency_id,
                'gift_card_currency_code' => $original_gc_currency_code,
                'gift_card_redeem_amount' => $original_gc_redeem_deno,
                'transaction_type' => 'SC',
                'redeem_date' => date('Y-m-d H:i:s'),
                'redeem_ip' => $ip,
                'issued_amount' => $sc_top_up_info['sc_amount'],
                'issued_currency_id' => $sc_top_up_info['sc_currency_id'],
                'issued_currency_code' => $sc_top_up_info['sc_currency']
            );

            tep_db_perform(TABLE_GIFT_CARD_REDEMPTION, $gc_redeem_track_array);

            return array(
                'status' => 'success_topup',
                'original_gc_deno' => $original_gc_deno,
                'original_gc_currency_id' => $original_gc_currency_id,
                'original_gc_currency_code' => $original_gc_currency_code,
                'original_gc_deno_formatted' => $currencies->format($original_gc_deno, true, $original_gc_currency_code, 1),
                'original_gc_redeem_deno' => $original_gc_redeem_deno,
                'redeem_gc_deno' => $sc_top_up_info['sc_amount'],
                'redeem_gc_currency_id' => $sc_top_up_info['sc_currency_id'],
                'redeem_gc_currency_code' => $sc_top_up_info['sc_currency'],
                'redeem_gc_deno_formatted' => $currencies->format($sc_top_up_info['sc_amount'], true, $sc_top_up_info['sc_currency'], 1),
                'sc_topup_precheck_value' => ''
            );
        }
    }

    function add_balance_gift_card_value_into_store_credit($order_id, $order_amount, $order_currency_code, $purchase_currency_code, $customer_id) {
        $return_bool = TRUE;
        $ip = tep_get_ip_address();
        $email_address = '';

        $currencies = $this->getCurrenciesObj();
        $purchase_currency_amount = $currencies->advance_currency_conversion($order_amount, $order_currency_code, $purchase_currency_code, true, 'buy');

        $customer_select_sql = "SELECT customers_email_address
                                FROM " . TABLE_CUSTOMERS . " 
                                WHERE customers_id = '" . tep_db_input($customer_id) . "'";
        $customer_result_sql = tep_db_query($customer_select_sql);
        if ($customer_info_row = tep_db_fetch_array($customer_result_sql)) {
            $email_address = $customer_info_row['customers_email_address'];
        }

        foreach ($this->pin_code_array AS $idx => $pin_set) {
            $redeemed_result = $pin_set['result'];

            if (isset($redeemed_result['status']) && $redeemed_result['status'] == 'success_redeemed') {
                $update_redeem_track = FALSE;
                $original_gc_redeem_deno = 0;
                $purchase_currency_balance_amount = 0;
                $original_gc_deno = $redeemed_result['original_gc_deno'];
                $original_gc_currency_code = $redeemed_result['original_gc_currency_code'];
                $redeem_gc_deno = $redeemed_result['redeem_gc_deno'];

                $purchase_currency_amount = $purchase_currency_amount - $redeem_gc_deno;  // $purchase_currency_amount == redeem_gc_currency_code

                if ($purchase_currency_amount < 0) {
                    $purchase_currency_balance_amount = $purchase_currency_amount * -1;

                    if ($purchase_currency_amount + $redeem_gc_deno > 0) {
                        $update_redeem_track = TRUE;
                    }

                    // only for rounding purpose
                    $purchase_currency_balance_amount = $currencies->advance_currency_conversion($purchase_currency_balance_amount, $purchase_currency_code, $purchase_currency_code, true);
                    $original_gc_redeem_deno = ($purchase_currency_balance_amount / $redeem_gc_deno) * $original_gc_deno; // (Purchase amount balance / total purchase amount) * gift card deno amount
                    // only for rounding purpose
                    $original_gc_redeem_deno = $currencies->advance_currency_conversion($original_gc_redeem_deno, $original_gc_currency_code, $original_gc_currency_code, true);

                    $result = $this->get_customer_gc_info($original_gc_deno, $original_gc_redeem_deno, $original_gc_currency_code, $purchase_currency_code, $customer_id);
                    $result = $this->add_gift_card_value_into_store_credit($result, $pin_set['pin'], $pin_set['sno'], $ip, $customer_id, $email_address);
                    $this->pin_code_array[$idx]['result'] = $result;

                    $purchase_currency_amount = 0;
                } else {
                    $update_redeem_track = TRUE;
                }

                if ($update_redeem_track) {
                    // Gift Card fully used for product purchase.
                    $original_gc_redeem_deno = $original_gc_deno - $original_gc_redeem_deno;
                    $redeem_balance_amount = $redeem_gc_deno - $purchase_currency_balance_amount;

                    $gc_redeem_track_array = array(
                        'customers_id' => $customer_id,
                        'transaction_id' => $order_id,
                        'serial_number' => $pin_set['sno'],
                        'pin_number' => $pin_set['pin'],
                        'gift_card_deno' => $original_gc_deno,
                        'gift_card_currency_id' => $redeemed_result['original_gc_currency_id'],
                        'gift_card_currency_code' => $original_gc_currency_code,
                        'gift_card_redeem_amount' => $original_gc_redeem_deno,
                        'transaction_type' => 'CO',
                        'redeem_date' => date('Y-m-d H:i:s'),
                        'redeem_ip' => $ip,
                        'issued_amount' => $redeem_balance_amount,
                        'issued_currency_id' => $redeemed_result['redeem_gc_currency_id'],
                        'issued_currency_code' => $redeemed_result['redeem_gc_currency_code']
                    );

                    tep_db_perform(TABLE_GIFT_CARD_REDEMPTION, $gc_redeem_track_array);

                    $this->pin_code_array[$idx]['result']['order_amount'] = $currencies->format($redeem_balance_amount, true, $redeemed_result['redeem_gc_currency_code'], 1);
                }
            }
        }
    }

    function validate_all_gift_cards($customer_id = 0, $currency_code = '', $sno = '', $pin = '') {
        $return_bool = TRUE;
        $this->set_pincode($sno, $pin);

        foreach ($this->pin_code_array AS $idx => $pin_set) {
            if (isset($pin_set['result']['error_key']))
                continue;
            $result = $this->check_gift_card_info($pin_set['sno'], $pin_set['pin'], $customer_id, $currency_code);
            $this->pin_code_array[$idx]['result'] = $result;

            if (isset($result['error_key']))
                $return_bool = FALSE;
        }

        return $return_bool;
    }

    /*
     * $add_to_sc : [true] Add to user store credit after redeem, [false] skip add to user store credit 
     */

    function redeem_all_gift_cards($customer_id, $currency_code = '', $add_to_sc = true, $email_address = '') {
        $return_bool = TRUE;
        $ip = tep_get_ip_address();

        if (!tep_not_empty($email_address) && $customer_id) {
            $customer_select_sql = "SELECT customers_email_address 
                                    FROM " . TABLE_CUSTOMERS . " 
                                    WHERE customers_id = '" . tep_db_input($customer_id) . "'";
            $customer_result_sql = tep_db_query($customer_select_sql);
            if ($customer_info_row = tep_db_fetch_array($customer_result_sql)) {
                $email_address = $customer_info_row['customers_email_address'];
            }
        }

        foreach ($this->pin_code_array AS $idx => $pin_set) {
            if (isset($pin_set['result']['error_key']))
                continue;
            $redeemed_result = $this->redeem_gift_card($pin_set['pin'], $pin_set['sno'], $customer_id, $currency_code);
            $this->pin_code_array[$idx]['result'] = $redeemed_result;

            if (isset($redeemed_result['status']) && $redeemed_result['status'] == 'success_redeemed' && $add_to_sc) {
                $result = $this->add_gift_card_value_into_store_credit($redeemed_result, $pin_set['pin'], $pin_set['sno'], $ip, $customer_id, $email_address);
                $this->pin_code_array[$idx]['result'] = $result;
            } else {
                $return_bool = FALSE;
            }
        }

        return $return_bool;
    }

    function get_last_pin_code_info() {
        $return_bool = TRUE;
        $original_gc_currency_id = 0;
        $redeem_gc_currency_id = 0;
        $redeem_gc_currency_code = '';
        $total_redeem_gc_deno = 0;
        $all_sno_arr = array();

        foreach ($this->pin_code_array AS $idx => $data) {
            $result = $data['result'];

            $all_sno_arr[] = $data['sno'];

            if (isset($result['error_key'])) {
                $return_bool = FALSE;
            } else if (isset($result['status'])) {
                $original_gc_currency_id = $result['original_gc_currency_id'];
                $redeem_gc_currency_id = $result['redeem_gc_currency_id'];
                $redeem_gc_currency_code = $result['redeem_gc_currency_code'];

                $total_redeem_gc_deno += $result['redeem_gc_deno'];
            }
        }

        return array(
            'status' => $return_bool,
            'original_gc_currency_id' => $original_gc_currency_id,
            'redeem_gc_currency_id' => $redeem_gc_currency_id,
            'redeem_gc_currency_code' => $redeem_gc_currency_code,
            'total_redeem_gc_deno' => $total_redeem_gc_deno,
            'serial_no_used' => implode(", ", $all_sno_arr)
        );
    }

    function set_pincode($sno, $pin, $reset = FALSE) {
        $return_bool = FALSE;
        if ($reset)
            $this->pin_code_array = array();

        if (tep_not_empty($sno) && tep_not_empty($pin)) {
            if ($this->is_sno_existed($sno)) {
                $this->pin_code_array[] = array(
                    'sno' => $sno,
                    'pin' => $pin,
                    'result' => array(
                        'error_key' => 'USER_GIFTCARD_ERROR'
                    )
                );
            } else {
                $return_bool = TRUE;
                $this->pin_code_array[] = array('sno' => $sno, 'pin' => $pin);
            }
        } else if (tep_not_empty($sno) && !tep_not_empty($pin)) {
            $this->pin_code_array[] = array(
                'sno' => $sno,
                'pin' => $pin,
                'result' => array(
                    'error_key' => 'USER_GIFTCARD_INFO_MISSING'
                )
            );
        }

        return $return_bool;
    }

    function import_pincode($sno_array, $pin_array) {
        // start new gift card data
        $this->clear_pincode();
        $return_success = TRUE;

        foreach ($sno_array as $key => $sno) {
            $sno = trim(tep_db_prepare_input($sno));
            $pin = isset($pin_array[$key]) ? tep_db_prepare_input($pin_array[$key]) : '';
            $return_success = $this->set_pincode($sno, $pin) === TRUE && $return_success;  // TRUE == ERROR
        }

        return $return_success;
    }

    function is_sno_existed($sno) {
        $return_bool = FALSE;

        foreach ($this->pin_code_array as $idx => $data) {
            if ($data['sno'] == $sno) {
                $return_bool = TRUE;
                break;
            }
        }

        return $return_bool;
    }

    function capture_pincode_by_session() {
        $_SESSION['pin_code_array'] = $this->pin_code_array;
    }

    function capture_purchase_info_by_session() {
        $_SESSION['gc_purchase_info'] = $this->purchase_info;
    }

    function restore_pincode_by_session() {
        if (isset($_SESSION['pin_code_array'])) {
            $this->pin_code_array = $_SESSION['pin_code_array'];
        }
    }

    function remove_pincode_session() {
        unset($_SESSION['pin_code_array']);
    }

    function is_pincode_exist() {
        return tep_not_empty($this->pin_code_array);
    }

    function clear_pincode() {
        $this->remove_pincode_session();
        $this->pin_code_array = array();
    }

    function filter_redeemed_pincode() {
        foreach ($this->pin_code_array AS $idx => $data) {
            if (isset($data['result'])) {
                if (isset($data['result']['status']) && $data['result']['status'] !== 1) {
                    unset($this->pin_code_array[$idx]);
                }
            }
        }
    }

    function get_game_list() {
        // required application_top included
        $this_cat_obj = new category(0);
        return $this_cat_obj->get_game_product_type_cat_info(array(2));
    }

    function get_product_list($cPath) {
        global $cPath_array;
        // required application_top included
        $output_arr = array();
        $ec_product = new product($cPath, 2);
        $ec_product->set_product_flag_id('4');
        $product_type = $ec_product->get_game_product_type($cPath, array(2));

        foreach ($product_type as $ptype_info) {
            $ec_product->categories_id = $ptype_info['cPath'];
//            $last_cat_id = $cPath_array[sizeof($cPath_array)-1];
//            $cat_arr[] = $last_cat_id;

            $cPath_array = tep_parse_category_path($ptype_info['cPath']);
            $ec_product->cpath_check();
            $ec_product->plain = true;
            $products_arr = $ec_product->get_product_listing();

            foreach ($products_arr as $product_info) {
//                $product_delivery_mode_array = $ec_product->get_product_delivery_mode($product_info['id']);
//                $is_dtu_flag = in_array('6', $product_delivery_mode_array) ? 1 : 0;	// Delivery mode is DTU ?
//                $only_dtu_box_flag = $is_dtu_flag ? (count($product_delivery_mode_array) == 1 ? 1 : 0) : 0;	// If only DTU
//                if (!$only_dtu_box_flag) {
                $output_arr[] = array('id' => $product_info['id'], 'text' => $product_info['name']);
//                }
            }

            unset($products_arr);
        }

        unset($ec_product, $product_type);

        return $output_arr;
    }

    function get_product_details($customer_id, $pid, $buyqty, $gc_curr, $gc_amt) {
        global $currencies, $currency;
        // required application_top included
        $price = '0.00';
        $dm = array();
        $alert_notice = '';

        if ($pid) {
            $ec_product = new product($cPath, 2);
            $response_arr = $ec_product->get_plain_products_status($pid, $buyqty);
            $product_price = $currencies->get_product_price($pid, $customer_id);
            $total_amount = $currencies->advance_currency_conversion($product_price * $buyqty);

            if ($response_arr) {
//                if (isset($response_arr['delivery_mode']['6'])) {
//                    unset($response_arr['delivery_mode']['6']);
//                }

                $price = $response_arr['price'];
                $dm = $response_arr['delivery_mode'];
            }

            if ($gc_curr == $currency && $gc_amt < $total_amount) {
                $alert_notice = ERROR_NOT_ENOUGH_CREDIT_AFTER_QTY;
            }
        }

        unset($response_arr);

        return array(
            'formatted_price' => $price,
            'price' => $total_amount,
            'alert_notice' => $alert_notice,
            'delivery_mode' => $dm
        );
    }

    function get_dtu_info($pID) {
        $display_delivery_mode_label_array = array();
        include_once(DIR_WS_CLASSES . 'direct_topup.php');
        $direct_topup_obj = new direct_topup();
        $main_product_id = $pID;

        $products_select_sql = "SELECT products_id, products_bundle, products_bundle_dynamic, products_price, products_main_cat_id, custom_products_type_id 
                                FROM " . TABLE_PRODUCTS . "
                                WHERE products_id = '" . $pID . "'";
        $products_result_sql = tep_db_query($products_select_sql);
        $product_info_row = tep_db_fetch_array($products_result_sql);
        if ($product_info_row['products_bundle'] == 'yes' || $product_info_row['products_bundle_dynamic'] == 'yes') {
            $bundle_select_sql = "	SELECT pp.products_id
                                    FROM " . TABLE_PUBLISHERS_PRODUCTS . " AS pp 
                                    INNER JOIN " . TABLE_PRODUCTS_DELIVERY_INFO . " AS pdi
                                        ON pdi.products_id = pp.products_id 
                                    INNER JOIN " . TABLE_PRODUCTS_BUNDLES . " AS pb
                                        ON pp.products_id=pb.subproduct_id
                                    WHERE pb.bundle_id = '" . tep_get_prid($pID) . "'
                                        AND pdi.products_delivery_mode_id = '6'
                                    LIMIT 1";
            $bundle_result_sql = tep_db_query($bundle_select_sql);
            $bundle_row = tep_db_fetch_array($bundle_result_sql);
            $game_input_array = $direct_topup_obj->get_game_input($bundle_row['products_id']);
            $main_product_id = $bundle_row['products_id'];
        } else {
            $game_input_array = $direct_topup_obj->get_game_input($main_product_id);
        }

        if (count($game_input_array)) {
            // Get Prefill DTU info
            $prefill_dtu_info_array = get_dtu_game_info('', $pID);
            // Get Prefill DTU info

            foreach ($game_input_array as $game_input_key_loop => $game_input_data_loop) {
                $top_up_info_key = $game_input_data_loop['top_up_info_key'];
                $default_info = isset($prefill_dtu_info_array[$top_up_info_key]) && tep_not_empty($prefill_dtu_info_array[$top_up_info_key]) ? $prefill_dtu_info_array[$top_up_info_key] : '';
                $default_info = tep_not_empty($default_info) ? $default_info : (isset($_REQUEST['game_info'][$top_up_info_key]) ? $_REQUEST['game_info'][$top_up_info_key] : (isset($game_input_data_loop['top_up_info_display']) ? $game_input_data_loop['top_up_info_display'] : ''));

                switch ($top_up_info_key) {
                    case 'server':
                        // server list
                        $servers_select_sql = "	SELECT pg.publishers_server
                                                FROM " . TABLE_PUBLISHERS_GAMES . " AS pg
                                                INNER JOIN " . TABLE_PUBLISHERS_PRODUCTS . " AS pp
                                                    ON pg.publishers_games_id = pp.publishers_games_id
                                                WHERE pp.products_id = '" . (int) $main_product_id . "'";
                        $servers_result_sql = tep_db_query($servers_select_sql);
                        $servers_row = tep_db_fetch_array($servers_result_sql);
                        $servers_tmp_array = json_decode($servers_row['publishers_server'], 1);
                        $servers_array = array();
                        $servers_array[] = array(
                            'id' => '',
                            'text' => $game_input_data_loop['top_up_info_display']
                        );

                        if (isset($servers_tmp_array)) {
                            foreach ($servers_tmp_array as $servers_id_loop => $server_name_loop) {
                                $servers_array[] = array(
                                    'id' => $servers_id_loop,
                                    'text' => $server_name_loop
                                );
                            }
                        }

                        $display_delivery_mode_label_array[] = tep_draw_pull_down_menu("game_info[" . $game_input_key_loop . "]", $servers_array, $default_info, ' default_text="' . $game_input_data_loop['top_up_info_display'] . '" onfocus="jQuery(this).removeClass(\'productListingDTUPreInput\').addClass(\'productListingDTUAftInput\')" onblur="if(this.value==\'\') {jQuery(this).removeClass(\'productListingDTUAftInput\').addClass(\'productListingDTUPreInput\')}else{jQuery(this).removeClass(\'productListingDTUPreInput\').addClass(\'productListingDTUAftInput\')}" class="productListingDTUPreInput dtu_customer_input_game_info" ');
                        break;
                    case 'account_platform':
                        // account platform
                        $account_platform_tmp_array = $direct_topup_obj->get_account_platform($main_product_id);
                        $account_platform_array = array();
                        $account_platform_array[] = array(
                            'id' => '',
                            'text' => $game_input_data_loop['top_up_info_display']
                        );
                        if (isset($account_platform_tmp_array)) {
                            foreach ($account_platform_tmp_array as $account_platform_id_loop => $account_platform_name_loop) {
                                $account_platform_array[] = array(
                                    'id' => $account_platform_id_loop,
                                    'text' => $account_platform_name_loop
                                );
                            }
                        }
                        $display_delivery_mode_label_array[] = tep_draw_pull_down_menu("game_info[" . $game_input_key_loop . "]", $account_platform_array, $default_info, ' default_text="' . $game_input_data_loop['top_up_info_display'] . '" onfocus="jQuery(this).removeClass(\'productListingDTUPreInput\').addClass(\'productListingDTUAftInput\')" onblur="if(this.value==\'\') {jQuery(this).removeClass(\'productListingDTUAftInput\').addClass(\'productListingDTUPreInput\')}else{jQuery(this).removeClass(\'productListingDTUPreInput\').addClass(\'productListingDTUAftInput\')}" class="productListingDTUPreInput dtu_customer_input_game_info" ');
                        break;
                    case 'character':
                        if ($direct_topup_obj->character_is_sync($main_product_id)) {
                            $character_list_array = array();
                            $character_list_array[] = array(
                                'id' => '',
                                'text' => $game_input_data_loop['top_up_info_display']
                            );
                            $display_delivery_mode_label_array[] = tep_draw_pull_down_menu("game_info[" . $game_input_key_loop . "]", $character_list_array, $default_info, ' id="dtu_character_sel" default_text="' . $game_input_data_loop['top_up_info_display'] . '" onfocus="load_character_list(this, \'' . $pID . '\', \'' . $dm_id . '\');jQuery(this).removeClass(\'productListingDTUPreInput\').addClass(\'productListingDTUAftInput\')" onblur="if(this.value==\'\') {jQuery(this).removeClass(\'productListingDTUAftInput\').addClass(\'productListingDTUPreInput\')}else{jQuery(this).removeClass(\'productListingDTUPreInput\').addClass(\'productListingDTUAftInput\')}" class="productListingDTUPreInput" ');
                        } else {
                            $display_delivery_mode_label_array[] = tep_draw_input_field("game_info[" . $game_input_key_loop . "]", $default_info, ' maxlength="64" size="53" onfocus="this.value=\'\';jQuery(this).removeClass(\'productListingDTUPreInput\').addClass(\'productListingDTUAftInput\')" onblur="if(this.value==\'\') {this.value=jQuery(this).attr(\'default_text\');jQuery(this).removeClass(\'productListingDTUAftInput\').addClass(\'productListingDTUPreInput\')}else{jQuery(this).removeClass(\'productListingDTUPreInput\').addClass(\'productListingDTUAftInput\')}" default_text="' . $game_input_data_loop['top_up_info_display'] . '" class="productListingDTUPreInput dtu_customer_input_game_info" ');
                        }
                        break;
                    default: // account
                        $display_delivery_mode_label_array[] = tep_draw_input_field("game_info[" . $game_input_key_loop . "]", $default_info, ' maxlength="64" size="53" onfocus="this.value=\'\';jQuery(this).removeClass(\'productListingDTUPreInput\').addClass(\'productListingDTUAftInput\')" onblur="if(this.value==\'\') {this.value=jQuery(this).attr(\'default_text\');jQuery(this).removeClass(\'productListingDTUAftInput\').addClass(\'productListingDTUPreInput\')}else{jQuery(this).removeClass(\'productListingDTUPreInput\').addClass(\'productListingDTUAftInput\')}" default_text="' . $game_input_data_loop['top_up_info_display'] . '" class="productListingDTUPreInput dtu_customer_input_game_info" ');
                        if ($direct_topup_obj->retype_account($main_product_id)) {
                            $default_info = ($default_info == $game_input_data_loop['top_up_info_display']) ? TEXT_RETYPE . $default_info : $default_info;
                            $display_delivery_mode_label_array[] = tep_draw_input_field("game_info[" . $game_input_key_loop . "_2]", $default_info, ' maxlength="64" size="53" onfocus="this.value=\'\';jQuery(this).removeClass(\'productListingDTUPreInput\').addClass(\'productListingDTUAftInput\')" onblur="if(this.value==\'\') {this.value=jQuery(this).attr(\'default_text\');jQuery(this).removeClass(\'productListingDTUAftInput\').addClass(\'productListingDTUPreInput\')}else{jQuery(this).removeClass(\'productListingDTUPreInput\').addClass(\'productListingDTUAftInput\')}" default_text="' . TEXT_RETYPE . $game_input_data_loop['top_up_info_display'] . '" class="productListingDTUPreInput dtu_customer_input_game_info" ');
                        }
                        break;
                }
            }
        }

        return $display_delivery_mode_label_array;
    }

}

?>