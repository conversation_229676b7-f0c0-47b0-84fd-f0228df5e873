<?
class latest_news{
	
	public function __construct () {
		$this->aws_obj = new ogm_amazon_ws();
		$this->aws_obj->set_bucket_key('BUCKET_STATIC');
		$this->aws_obj->set_filepath('images/news/');
		$this->page_obj = new page();
		
		if(!function_exists('eval_buffer')) {
			function eval_buffer($string) {
				ob_start();
				eval("$string[2];");
				$return = ob_get_contents();
				ob_end_clean();
				return $return;
			}
		}
	
		if(!function_exists('eval_print_buffer')) {
			function eval_print_buffer($string) {
				ob_start();
				eval("print $string[2];");
				$return = ob_get_contents();
				ob_end_clean();
				return $return;
			}
		}
		
		if(!function_exists('eval_html')) {
			function eval_html($string) {
				$string = preg_replace_callback("/(<\?=)(.*?)\?>/si", "eval_print_buffer",$string);
				return preg_replace_callback("/(<\?php|<\?)(.*?)\?>/si", "eval_buffer",$string);
			}
		}
	}
	
	//List latest news for mainpage latest news section 
	public function list_latest_news($num_news='') {
		if (defined('SITE_ID')) {
			$this_site_id = SITE_ID;
		}
		if (tep_not_null($num_news)) {
			$limit_news = "LIMIT ". $num_news ."";
		} else {
			$limit_news = "LIMIT 7"; //default is 7 news
		}
		$news_display_sites_where_str = "FIND_IN_SET( '" . $this_site_id . "', news_display_sites)";
		$news_select_sql = "SELECT ln.news_id, lnd.headline, lnd.latest_news_summary, ln.latest_news_url_alias, ln.date_added ,lnd.content
							FROM " . TABLE_LATEST_NEWS . " as ln
							INNER JOIN " . TABLE_LATEST_NEWS_GROUPS . " as lng
								ON (ln.news_groups_id = lng.news_groups_id)
							INNER JOIN " . TABLE_LATEST_NEWS_DESCRIPTION . " as lnd
								ON (ln.news_id = lnd.news_id)
							WHERE ln.status = 1
								AND lng.news_groups_display_status = 1
								AND lnd.language_id = '" . $_SESSION['languages_id'] . "'
								AND lnd.headline <> ''
								AND $news_display_sites_where_str 
							ORDER BY ln.date_added DESC
							$limit_news
							";	
		$news_result_sql = tep_db_query($news_select_sql);
		while ($news_row = tep_db_fetch_array($news_result_sql)) {
			if(!tep_not_null($news_row['content'])) {
				$default_news_select_sql = "SELECT content, headline, latest_news_summary
											FROM " . TABLE_LATEST_NEWS_DESCRIPTION . "
											WHERE news_id = '". $news_row['news_id'] ."'
												AND is_default = 1";
				$default_news_result_sql = tep_db_query($default_news_select_sql);
				if ($default_news_row = tep_db_fetch_array($default_news_result_sql)) {
					$news_row['content'] = $default_news_row['content'];
					$news_row['headline'] = $default_news_row['headline'];
					$news_row['latest_news_summary'] = $default_news_row['latest_news_summary'];
				}
			}
			$thumbnail_img_name = $news_row['news_id'] .'_1_'.$_SESSION['languages_id'].'.jpg';
			if ($this->aws_obj->is_aws_s3_enabled()) {
	 			if ($this->aws_obj->is_image_exists($thumbnail_img_name)) {
					$thumbnail_image_url = $this->aws_obj->get_image_url_by_instance();
				} else {
					$thumbnail_image_url = '';
				}
			} else {
				if (file_exists(DIR_FS_CATALOG. 'images/news/' . $thumbnail_img_name)) {
					$thumbnail_image_url = DIR_WS_IMAGES . 'news/' . $thumbnail_img_name; 
				} else {
					$thumbnail_image_url = '';
				}
			}
			$shorten_summary = nl2br(eval_html($news_row['latest_news_summary']));
			if (mb_substr($shorten_summary, 120, 1)) {
				$shorten_summary = mb_substr($shorten_summary, 0, 119);
				$shorten_summary .= "..";
			}
			$date = date('l, d M, Y',strtotime($news_row['date_added']));
			$news_array[] = array (	'news_id' => $news_row['news_id'],
									'headline' => $news_row['headline'],
									'latest_news_summary' => $shorten_summary,
									'latest_news_url_alias' => $news_row['latest_news_url_alias'],
									'date_added' => $date,
									'thumbnail_image_url' => $thumbnail_image_url
									); 
		}
		
		return $news_array;	
	}
	
	//List latest news related to the category
	private function list_latest_news_by_category($category_array,$cust_prod_type,$num_news='',$extra_info=false) {
		if (defined('SITE_ID')) {
			$this_site_id = SITE_ID;
		}
		if (tep_not_null($num_news)) {
			$limit_news = "LIMIT ". $num_news ."";
		} else {
			$limit_news = 'LIMIT 10'; // Set default news as 10 
		}
		if ($extra_info === true) {
			$extra_info_where_str = 'AND lng.news_groups_id = 5';
		} else {
			$extra_info_where_str = '';
		}
		$news_display_sites_where_str = "FIND_IN_SET( '" . $this_site_id . "', ln.news_display_sites)";
		$custom_products_type_where_str = " (ln.custom_products_type = '' OR FIND_IN_SET('" . (int)$cust_prod_type . "', ln.custom_products_type))";
		$news_select_sql = "SELECT DISTINCT lnc.news_id, lnd.headline, lnd.latest_news_summary, lnd.content, ln.latest_news_url_alias, ln.date_added 
							FROM " . TABLE_LATEST_NEWS_CATEGORIES . " as lnc
							INNER JOIN " . TABLE_LATEST_NEWS . " as ln
								ON (lnc.news_id  = ln.news_id)
							INNER JOIN " . TABLE_LATEST_NEWS_GROUPS . " as lng
								ON (ln.news_groups_id = lng.news_groups_id)
							INNER JOIN " . TABLE_LATEST_NEWS_DESCRIPTION . " as lnd
								ON (lnc.news_id = lnd.news_id)
							WHERE lnc.categories_id IN ('".implode("','", $category_array)."')
								AND ln.status = 1
								AND lng.news_groups_display_status = 1
								AND lnd.language_id = '" . $_SESSION['languages_id'] . "'
								AND lnd.headline <> ''
								AND $news_display_sites_where_str
								AND $custom_products_type_where_str
								$extra_info_where_str
							ORDER BY ln.date_added DESC
							$limit_news
							";	
		$news_result_sql = tep_db_query($news_select_sql);
        
		while ($news_row = tep_db_fetch_array($news_result_sql)) {
			if(!tep_not_null($news_row['content'])) {
				$default_news_select_sql = "SELECT content, headline, latest_news_summary
											FROM " . TABLE_LATEST_NEWS_DESCRIPTION . "
											WHERE news_id = '". $news_row['news_id'] ."'
												AND is_default = 1";
				$default_news_result_sql = tep_db_query($default_news_select_sql);
				if ($default_news_row = tep_db_fetch_array($default_news_result_sql)) {
					$news_row['content'] = $default_news_row['content'];
					$news_row['headline'] = $default_news_row['headline'];
					$news_row['latest_news_summary'] = $default_news_row['latest_news_summary'];
				}

			}
			$date = date('l, d M, Y',strtotime($news_row['date_added']));
			$extra_img_name = $news_row['news_id'] .'_3_'.$_SESSION['languages_id'].'.jpg';
			$news_array[] = array (	'news_id' => $news_row['news_id'],
									'headline' => $news_row['headline'],
									'latest_news_summary' => $news_row['latest_news_summary'],
									'latest_news_url_alias' => $news_row['latest_news_url_alias'],
									'date_added' => $date,
									'extra_image' => $extra_img_name
									); 
		}
		
		return $news_array;
	}
	
	// Get all news groups categories to list in top menu
	public function get_latest_news_categories ($num_categories='') {
		if (tep_not_null($num_categories)) {
			$limit_categories = "LIMIT ". $num_categories ."";
		} else {
			$limit_categories = "LIMIT 10"; // set default as 10
		}
		$news_category_select_sql = "	SELECT lng.news_groups_id, lngd.news_groups_name 
										FROM  " . TABLE_LATEST_NEWS_GROUPS . " as lng
										INNER JOIN " . TABLE_LATEST_NEWS_GROUPS_DESCRIPTION . " as lngd
											ON (lng.news_groups_id = lngd.news_groups_id)
										WHERE lng.news_groups_display_status  = 1
											AND lngd.language_id = '" . $_SESSION['languages_id'] . "'
										ORDER BY lng.news_groups_sort_order
										$limit_categories
										";	
		$news_category_result_sql = tep_db_query($news_category_select_sql);						
		while ($news_category_row = tep_db_fetch_array($news_category_result_sql)) {
			$news_category_array[] = array ( 'news_groups_id' => $news_category_row['news_groups_id'],
											 'news_groups_name' => $news_category_row['news_groups_name'],
											 'news_url' => tep_href_link(FILENAME_SEARCH_LATEST_NEWS,'news_type=' . $news_category_row['news_groups_id'] . '')
											);
		}
		
		return $news_category_array;
	}
	
	// Get latest blog news in mainpage blog section
	public function get_recent_blog($num_blog='') {
		if (tep_not_null($num_blog)) {
			$num_blog = "LIMIT ". $num_blog ."";
		} else {
			$num_blog = "LIMIT 4"; // set default blog to display as 4
		}
		
	}
	
	// Get extra info for product under product listing expanded info section
	public function get_product_extra_info($category_array,$cust_prod_type,$num_news='') {
		$extra_info_array = $this->list_latest_news_by_category($category_array,$cust_prod_type,$num_news='',true);
		
		return $extra_info_array;
	}
	
	// Display product page side box news and images according to product categories
	public function product_page_side_box($cat_array,$page_info) {
		$image_size = array ('width' => '100%'); 
		$news_array = $this->list_latest_news_by_category($cat_array,$page_info);
		if (tep_not_null($news_array)) {
			foreach ($news_array as $key) {
				$news_url_parameter="news_id=" . $key['news_id'] . "";
				if ($this->aws_obj->is_aws_s3_enabled()) {
		 			if ($this->aws_obj->is_image_exists($key['extra_image'])) {
						echo '<div style="margin-bottom: 20px; margin-top: 20px;">';
						echo "<a href=" . tep_href_link(FILENAME_SEARCH_LATEST_NEWS,$news_url_parameter) . ">" . tep_image($this->aws_obj->get_image_url_by_instance(),'',$image_size['width']) . "</a>";
						echo '</div>';
					}
				} else {
					if (file_exists(DIR_FS_CATALOG. 'images/news/' . $key['extra_image'])) {
						echo '<div style="margin-bottom: 20px; margin-top: 20px;">';
						echo "<a href=" . tep_href_link(FILENAME_SEARCH_LATEST_NEWS,$news_url_parameter) . ">" . tep_image(DIR_WS_IMAGES . 'news/' . $key['extra_image'],'',$image_size['width']) . "</a>";
						echo '</div>';
					}
				}
				$print_summary = nl2br(eval_html($key['latest_news_summary']));
				if (mb_substr($print_summary, 60, 1)) {
					$print_summary = mb_substr($print_summary, 0, 59);
					$print_summary .= "...";
				}
				
				$news_content_string .= '
					<div style="padding:15px;word-wrap:break-word">
						<a class="hd1" href="' . tep_href_link(FILENAME_SEARCH_LATEST_NEWS,$news_url_parameter) . '">'. strip_tags($key['headline']). '</a>
						<div style="ctn">
							<span class="date">'. $key['date_added'] . '</span><br>
							<span style="">' . $print_summary . '</span>
						</div>
					</div>
					<div class="dotborder"></div>
					<div style="clrFx"></div>';
			}
		}
		$news_content_string .= '<div style="padding:15px 35px 0px">' . tep_image_button2('gray_short',tep_href_link(FILENAME_SEARCH_LATEST_NEWS),BUTTON_READ_MORE_NEWS) . '</div>';
		if(tep_not_null($news_array)) {
			echo $this->page_obj->get_html_simple_rc_box(BOX_INFORMATION_NEWS , $news_content_string, 12);
		}
		
		unset($this->aws_obj);
		unset($this->page_obj);
	}
	
	private function load_memcache() {
		global $memcache_obj;
		
		if (!isset($memcache_obj)) {
			require_once(DIR_WS_CLASSES . 'cache_abstract.php');
			require_once(DIR_WS_CLASSES . 'memcache.php');
			$memcache_obj = new OGM_Cache_MemCache();
		}
		
		return $memcache_obj;
	}
	
	// Cache latest news column for days
	public function cached_latest_news_column($cache_hr,$num_news='') {
		$memcache_obj = $this->load_memcache();
		$cache_key = TABLE_LATEST_NEWS . '/main_page_news_tab/array/language/'.$_SESSION['languages_id'];
		$cache_result = $memcache_obj->fetch($cache_key);
		if ($cache_result !== FALSE) {
			$news_array = $cache_result;
		} else {
			$news_array = $this->list_latest_news($num_news='');
			if ($cache_hr > 0) {
				$memcache_obj->store($cache_key, $news_array, $cache_hr * 3600); //cache for how many days 
			}
		}
		unset($cache_key, $cache_result);
		
		return $news_array;
	}
}
?>