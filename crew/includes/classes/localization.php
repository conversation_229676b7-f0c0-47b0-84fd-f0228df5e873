<?php

/*
  $Id: localization.php,v 1.11 2015/02/12 09:43:23 akmal.adnan Exp $

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2003 osCommerce

  Released under the GNU General Public License
 */

// Class to handle localization
// TABLES: zone_info, zones_to_geo_zones, geo_zones

class localization {

    var $country = '';
    var $default_country = STORE_COUNTRY;
    var $zone_info_array = array();

    // class constructor
    function localization($country_id) {
        // Grab default store country
        if (tep_not_null($country_id)) {
            $this->country = $country_id;
        } else {
            $this->country = $this->default_country;
        }
    }

    // class methods
    function get_zone_configuration() {
        for ($loc_type = 1; $loc_type <= 4; $loc_type++) {
            $zones_id = $this->get_zone_id($loc_type);

            $this->zone_info_array[$loc_type] = $this->get_zone_info($zones_id);
            $this->zone_info_array[$loc_type]->zone_id = $zones_id;
        }

        return $this->zone_info_array;
    }

    function get_zone_id($zone_type) {
        global $memcache_obj;
        $geo_result_array = array();
        $fresh_request = true;

        $cache_key = TABLE_GEO_ZONES . '/geo_zone_type/array/' . $zone_type;

        $cache_result = $memcache_obj->fetch($cache_key);

        if ($cache_result !== FALSE) {
            $geo_result_array = $cache_result;

            if (isset($geo_result_array[$this->country]) && is_array($geo_result_array[$this->country])) {
                $fresh_request = false;
            }
        }

        if ($fresh_request) {
            $countries_zones_id_select_sql = "	SELECT z.geo_zone_id
												FROM " . TABLE_ZONES_TO_GEO_ZONES . " AS gz
												INNER JOIN " . TABLE_GEO_ZONES . " AS z
													ON gz.geo_zone_id = z.geo_zone_id
												WHERE gz.zone_country_id = '" . tep_db_input($this->country) . "'
													AND z.geo_zone_type = '" . tep_db_input($zone_type) . "'";
            $countries_zones_id_result_sql = tep_db_query($countries_zones_id_select_sql);

            if ($countries_zones_id_row = tep_db_fetch_array($countries_zones_id_result_sql)) {
                $geo_result_array[$this->country] = $countries_zones_id_row;
            } else {
                $default_zones_id_select_sql = "	SELECT z.geo_zone_id
													FROM " . TABLE_ZONES_TO_GEO_ZONES . " AS gz
													INNER JOIN " . TABLE_GEO_ZONES . " AS z
														ON gz.geo_zone_id = z.geo_zone_id
													WHERE gz.zone_country_id = '" . tep_db_input($this->default_country) . "'
														AND z.geo_zone_type = '" . tep_db_input($zone_type) . "'";
                $default_zones_id_result_sql = tep_db_query($default_zones_id_select_sql);

                $default_zones_id_row = tep_db_fetch_array($default_zones_id_result_sql);

                $geo_result_array[$this->country] = $default_zones_id_row;
            }

            $memcache_obj->replace($cache_key, $geo_result_array, 86400);
        }

        return $geo_result_array[$this->country]['geo_zone_id'];
    }

    function get_zone_info($zones_id) {
        global $memcache_obj;
        $zone_setting_result = array();

        $cache_key = TABLE_ZONES_INFO . '/geo_zone_id/' . $zones_id;

        $cache_result = $memcache_obj->fetch($cache_key);

        if ($cache_result !== FALSE) {
            $zone_setting_result = $cache_result;
        } else {
            $json = new Services_JSON();

            $zone_info_select_sql = "	SELECT geo_zone_info
										FROM " . TABLE_ZONES_INFO . "
										WHERE geo_zone_id = '" . tep_db_input($zones_id) . "'";
            $zones_info_result = tep_db_query($zone_info_select_sql);
            $zones_info_row = tep_db_fetch_array($zones_info_result);

            $zone_setting_result = $json->decode($zones_info_row['geo_zone_info']);

            if (isset($zone_setting_result->zone_categories_id)) {
                $_all_game = array();
                foreach ($zone_setting_result->zone_categories_id as $categories_id) {
                    $categories_select_sql = "	SELECT c.categories_id
                                    FROM " . TABLE_CATEGORIES . " AS c
                                    WHERE c.categories_id=" . $categories_id . " AND c.categories_status = 1";
                    $categories_result_sql = tep_db_query($categories_select_sql);
                    if (tep_db_num_rows($categories_result_sql) > 0) {
                        $_all_game[] = $categories_id;
                    }
                }
                $zone_setting_result->zone_categories_id = $_all_game;
                unset($_all_game);
            }

            $memcache_obj->store($cache_key, $zone_setting_result, 86400);
        }

        if (isset($zone_setting_result->zone_currency_id)) {
            require_once(DIR_WS_CLASSES . 'currencies.php');
            $currencies = new currencies();

            $zone_setting_result->zone_currency_id = array_intersect($zone_setting_result->zone_currency_id, $currencies->internal_currencies);

            if ($currencies->is_set($zone_setting_result->zone_default_currency_id) !== TRUE) {
                $zone_setting_result->zone_default_currency_id = DEFAULT_CURRENCY;
            }

            unset($currencies);
        }

        return $zone_setting_result;
    }

    function get_zone_type_info($type_id) {
        return $this->zone_info_array[$type_id];
    }

    function is_supported($needle, $type) {
        switch ($type) {
            case 1:
                return is_array($this->zone_info_array[$type]->zone_categories_id) && in_array($needle, $this->zone_info_array[$type]->zone_categories_id) ? true : false;

                break;
            case 2:
                return is_array($this->zone_info_array[$type]->zone_languages_id) && in_array($needle, $this->zone_info_array[$type]->zone_languages_id) ? true : false;

                break;
            case 3:
                return is_array($this->zone_info_array[$type]->zone_currency_id) && in_array($needle, $this->zone_info_array[$type]->zone_currency_id) ? true : false;

                break;
            case 4:
                return is_array($this->zone_info_array[$type]->zone_payment_gateway_id) && in_array($needle, $this->zone_info_array[$type]->zone_payment_gateway_id) ? true : false;

                break;
            default:
                break;
        }
    }

    # GST

    public function verify_gst_condition() {
        if (strstr(MODULE_ORDER_TOTAL_INSTALLED, 'ot_gst')) {
            $country = '';
            $gst_currency = '';
            unset($_SESSION['RegionGST']);

            # Country
//            $country = tep_get_ip_country_id();
//            if (!tep_not_null($country)) {
//                $country = STORE_COUNTRY;
//            }
//
//            if (tep_country_exists($country)) {
//                include_once(DIR_WS_MODULES . 'order_total/ot_gst.php');
//                $ot_gst = new ot_gst;
//                $gst_currency = $ot_gst->get_gst_currency($country);
//
//                if (tep_not_null($gst_currency) && tep_currency_exists($gst_currency)) {
//                    $_SESSION['RegionGST']['loc_country'] = $country;
//                    $_SESSION['RegionGST']['currency'] = $gst_currency;
//                    $_SESSION['RegionGST']['tax_title'] = $ot_gst->get_gst_title();
//                }
//            }
        }
    }

}

?>