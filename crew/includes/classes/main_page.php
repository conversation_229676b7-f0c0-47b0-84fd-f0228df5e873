<?php
/*
  	$Id: main_page.php,v 1.9 2013/03/13 06:16:32 weichen Exp $
	
	Developer: Ching Yen
  	Copyright (c) 2005 SKC Ventrue
	
  	Released under the GNU General Public License
*/

include_once(DIR_WS_CLASSES . FILENAME_SEARCH_ALL_GAMES);

class main_page {
	private $max_best_selling = 10;
	
	public function __construct() {
	}
	
	public function main_page_init() {
		$content = array_merge($this->get_country_content_description(), $this->get_country_content('tab_content'));
		return $content;
	}
	
//	public function get_publisher() {
//		global $zone_info_array;
//		
//		$content = array();
//		
//		if (tep_not_empty($zone_info_array[1]->zone_categories_id)) {
//			$counter = 0;
//			$publishers_game = array();
//			
//			$publishers_sel_sql = "	SELECT p.publishers_id, pg.categories_id 
//									FROM " . TABLE_PUBLISHERS . " AS p 
//									INNER JOIN " . TABLE_PUBLISHERS_GAMES . " AS pg 
//										ON pg.publishers_id = p.publishers_id 
//									INNER JOIN " . TABLE_CATEGORIES . " AS c 
//										ON c.categories_id = pg.categories_id 
//											AND c.categories_status = '1'
//									WHERE p.publishers_status = '1' 
//										AND pg.publishers_games_status = '1' 
//									ORDER BY p.sort_order";
//			$publishers_res_sql = tep_db_query($publishers_sel_sql);
//			while ($publishers_row = tep_db_fetch_array($publishers_res_sql)) {
//				if (in_array($publishers_row['categories_id'], $zone_info_array[1]->zone_categories_id) &&
//					!in_array($publishers_row['categories_id'], $publishers_game)) {
//					$publishers_game[] = $publishers_row['categories_id'];
//				}
//			}
//			
//			if (count($publishers_game)) {
//				$search_all_games = new search_all_games();
//				$all_genre = $search_all_games->get_game_all_genre();
//				$all_platform = $search_all_games->get_game_all_platform();
//				
//				foreach ($publishers_game as $cat_id) {
//					if ($this->max_publisher > $counter) {
//						$publisher[] = array (	'cat_id' => $cat_id, 
//												'cat_path' => $this->game_cat_path_by_tpl($cat_id), 
//												'cat_name' => tep_get_categories_name($cat_id), 
//												'genre' => $search_all_games->get_game_genre($cat_id, $all_genre), 
//												'platform' => $search_all_games->get_game_platform($cat_id, $all_platform) 
//										);
//						$counter++;
//					}
//				}
//			}
//			
//			$content['publisher'] = $publisher;
//		}
//		
//		return $content;
//	}
	
	public function get_country_content($key = '') {
		$content = array();
		$json = new Services_JSON();
		
		if (!tep_not_empty($key)) {
			$key = 'tab_content, footer_all_payment_image';
		}
		
		$content_sel_sql = "SELECT " . $key . " 
							FROM " . TABLE_COUNTRIES_CONTENT . " 
							WHERE countries_id = '" . $_SESSION['country'] . "'
							LIMIT 1";
		$content_res_sql = tep_db_query($content_sel_sql);
		if ($content_row = tep_db_fetch_array($content_res_sql)) {
			foreach ($content_row as $fieldname => $fieldval) {
				switch ($fieldname) {
					case 'tab_content':
						$data = $json->decode($content_row[$fieldname]);
						
						$best_selling = array();
						$dtu_game = array();
                        
                        $search_all_games = new search_all_games();
                        $all_genre = $search_all_games->get_game_all_genre();
                        $all_platform = $search_all_games->get_game_all_platform();

						if ($data->best_selling != '') {
                            $counter = 0;
							foreach ($data->best_selling as $num => $cat_id) {
								if ($this->max_best_selling > $counter) {
									$best_selling[] = array (	'cat_id' => $cat_id, 
																'cat_path' => $this->game_cat_path_by_tpl($cat_id), 
																'cat_name' => tep_get_categories_name($cat_id), 
																'genre' => $search_all_games->get_game_genre($cat_id, $all_genre), 
																'platform' => $search_all_games->get_game_platform($cat_id, $all_platform) 
														);
									$counter++;
								}
							}
						}
						
                        if ($data->dtu_game != '') {
                            $counter = 0;
							foreach ($data->dtu_game as $num => $cat_id) {
								if ($this->max_best_selling > $counter) {
									$dtu_game[] = array (	'cat_id' => $cat_id, 
                                                            'cat_path' => $this->game_cat_path_by_tpl($cat_id), 
                                                            'cat_name' => tep_get_categories_name($cat_id), 
                                                            'genre' => $search_all_games->get_game_genre($cat_id, $all_genre), 
                                                            'platform' => $search_all_games->get_game_platform($cat_id, $all_platform) 
                                                    );
									$counter++;
								}
							}
						}
                        
                        $content['main_tab']['best_selling'] = $best_selling;
                        $content['main_tab']['dtu_game'] = $dtu_game;
                        
						break;
					case 'footer_all_payment_image':
						$content['footer_payment_img'] = unserialize($content_row[$fieldname]);
						break;
				}
			}
		}
		
		return $content;
	}
	
	public function get_country_content_description($key = '') {
		$content = array();
		
		if (!tep_not_empty($key)) {
			$key = 'slider_content, banner_content';
		}
		
		$content_sel_sql = "SELECT " . $key . " 
							FROM " . TABLE_COUNTRIES_CONTENT_DESCRIPTION . " 
							WHERE countries_id = '" . $_SESSION['country'] . "' 
								AND language_id = '" . $_SESSION['languages_id'] . "'";
		$content_res_sql = tep_db_query($content_sel_sql);
		if ($content_row = tep_db_fetch_array($content_res_sql)) {
			foreach ($content_row as $fieldname => $fieldval) {
				$array = array();
				
				switch ($fieldname) {
					case 'slider_content':
						if (tep_not_empty($content_row[$fieldname])) {
							$data = unserialize($content_row[$fieldname]);
							
							if (tep_not_empty($data)) {
								foreach ($data as $num => $vals) {
									foreach ($vals as $key => $val) {
										$array[$num][$key] = $val;
									}
								}
							}
						}
						
						$content['main_slider'] = $array;
						break;
						
					case 'banner_content':
						if (tep_not_empty($content_row[$fieldname])) {
							$data = unserialize($content_row[$fieldname]);
							
							if (tep_not_empty($data)) {
								foreach ($data as $key => $val) {
									$array[] = $val;
								}
							}
						}
						
						$content['main_best_selling'] = $array;
						break;
				}
			}
		}
		
		return $content;
	}
	
	public function game_cat_path_by_tpl($parent_id, $tpl = 2) { // default CDK
		$cat_path = '';
		
		$game_cat_sel_sql = "	SELECT categories_id, categories_parent_path 
								FROM " . TABLE_CATEGORIES . " 
								WHERE parent_id = '" . (int)$parent_id . "' 
									AND custom_products_type_id = '" . (int)$tpl . "' 
									AND categories_status = '1' 
								LIMIT 1";
		$game_cat_res_sql = tep_db_query($game_cat_sel_sql);
		if ($game_cat_row = tep_db_fetch_array($game_cat_res_sql)) {
			$cat_path = trim($game_cat_row['categories_parent_path'] . $game_cat_row['categories_id'], '_');
		}
		
		return $cat_path;
	}
}


?>