<?
/*
  	$Id: message_stack.php,v 1.11 2011/10/14 08:29:10 weichen Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2002 osCommerce
  	Released under the GNU General Public License
	
	Example usage:
  	$messageStack = new messageStack();
  	$messageStack->add('general', 'Error: Error 1', 'error');
  	$messageStack->add('general', 'Error: Error 2', 'warning');
  	if ($messageStack->size('general') > 0) echo $messageStack->output('general');
*/

class messageStack extends tableBox
{
	// class constructor
    function messageStack()
    {
      	global $messageToStack;
		
      	$this->messages = array();
		
      	if (tep_session_is_registered('messageToStack')) {
        	for ($i=0, $n=sizeof($messageToStack); $i<$n; $i++) {
          		$this->add($messageToStack[$i]['class'], $messageToStack[$i]['text'], $messageToStack[$i]['type']);
        	}
        	tep_session_unregister('messageToStack');
      	}
    }
	
	// class methods
    function add($class, $message, $type = 'error')
    {
      	if ($type == 'error') {
        	$this->messages[] = array('params' => 'class="messageStackError"', 'class' => $class, 'text' => tep_image(DIR_WS_ICONS . 'error.gif', ICON_ERROR) . '&nbsp;' . $message);
      	} else if ($type == 'warning') {
        	$this->messages[] = array('params' => 'class="messageStackWarning"', 'class' => $class, 'text' => tep_image(DIR_WS_ICONS . 'warning.png', ICON_WARNING) . '&nbsp;' . $message);
      	} else if ($type == 'success') {
        	$this->messages[] = array('params' => 'class="messageStackSuccess"', 'class' => $class, 'text' => tep_image(DIR_WS_ICONS . 'success.gif', ICON_SUCCESS) . '&nbsp;' . $message);
        } else if ($type == 'success_box') {
        	$this->messages[] = array('params' => 'class="messageStackSuccess"', 'class' => $class, 'text' => '<div><div style="float:left;padding-left:10;padding-right:10;">'.tep_image(DIR_WS_ICONS . 'success.gif', ICON_NOTICE).'</div><div style="float:left;">'.$message.'</div></div>');
		} else if ($type == 'notice_box') {
        	$this->messages[] = array('params' => 'class="messageStackSuccess"', 'class' => $class, 'text' => '	<table width="100%"><tr><td width="35px" style="text-align:left;vertical-align: top;"><div style="padding:0px 10px;text-align:center;">'.tep_image(DIR_WS_ICONS . 'note.gif', ICON_SUCCESS).'</div></td><td width="*%" align="left">'.$message.'</td></tr></table>');
		} else if ($type == 'error_box') {
        	$this->messages[] = array('params' => 'class="messageStackError"', 'class' => $class, 'text' => '	<table width="100%"><tr><td width="22px" style="text-align:left;vertical-align: top;"><div style="padding-left:10;padding-right:10;">'.tep_image(DIR_WS_ICONS . 'error.gif', ICON_ERROR).'</div></td><td width="*%" align="left">'.$message.'</td></tr></table>');
		} else if ($type == 'promo_box') {
        	$this->messages[] = array('params' => 'class="messageStackSuccess"', 'class' => $class, 'text' => '	<table width="100%"><tr><td width="22px" style="text-align:left;vertical-align: top;"><div style="padding-left:10;padding-right:10;">'.tep_image(DIR_WS_ICONS . 'icon_promo_big.gif', ICON_PROMO).'</div></td><td width="*%" align="left"><h2>'.$message.'</h2></td></tr></table>');
      	} else {
        	$this->messages[] = array('params' => 'class="messageStackSuccess"', 'class' => $class, 'text' => '	<table width="100%"><tr><td width="100%" align="left"><h2>'.$message.'</h2></td></tr></table>');
      	}
	}
	
	//Mantis # 0000024 @ ************ - For My Account phase 2 "My Account Home"
	function myaccount_add($class, $message, $type='warning') {
      	if ($type == 'warning') {
        	$this->messages[] = array('params' => 'class="messageStackWarning"', 'class' => $class, 'text' => tep_image(DIR_WS_ICONS . 'warning_large.gif', ICON_WARNING, '', '', 'style="float:left; margin: 2px 5px 5px 5px;"') . $message);
      	}
    }
    
    function add_session($class, $message, $type = 'error')
    {
      	global $messageToStack;
		
      	if (!tep_session_is_registered('messageToStack')) {
        	tep_session_register('messageToStack');
        	$messageToStack = array();
      	}
		
      	$messageToStack[] = array('class' => $class, 'text' => $message, 'type' => $type);
    }
	
    function reset()
    {
      	$this->messages = array();
    }
	
    function output($class)
    {
      	$this->table_data_parameters = 'class="messageBox"';
		
      	$output = array();
      	for ($i=0, $n=sizeof($this->messages); $i<$n; $i++) {
        	if ($this->messages[$i]['class'] == $class) {
          		$output[] = $this->messages[$i];
        	}
      	}
		
      	return $this->tableBox($output);
    }
	
    function size($class)
    {
      	$count = 0;
		
      	for ($i=0, $n=sizeof($this->messages); $i<$n; $i++) {
        	if ($this->messages[$i]['class'] == $class) {
          		$count++;
       		}
		}
		
      	return $count;
	}
}
?>