<?php

class oauth {

    const OAUTH_AUTHORIZE_REQUEST_CODE = 'Authorize/RequestCode/';

    public function __construct() {

    }

    public function is_oauth_login() {
        $oauth_login = '';

        if (tep_not_null($_REQUEST['request_id']) && tep_not_null($_REQUEST['client_id'])) {
            $oauth_login = 'oauth';
            $_SESSION['request_id'] = $_REQUEST['request_id'];
            $_SESSION['client_id'] = $_REQUEST['client_id'];
        } else if ((tep_not_null($_SESSION['request_id']) && tep_not_null($_SESSION['client_id']))) {
            $oauth_login = 'oauth';
        } else if (tep_not_null($_SESSION['login_redirect_url']) || tep_not_null($_REQUEST['redirect_url'])) {
            $_SESSION['login_redirect_url'] = isset($_REQUEST['redirect_url']) ? $_REQUEST['redirect_url'] : $_SESSION['login_redirect_url'];
            $oauth_login = 'redirect_url';
        }

        return $oauth_login;
    }

    public function Oauth_login_success($cookie_check = false) {
        $request_id = isset($_SESSION['request_id']) ? $_SESSION['request_id'] : $_REQUEST['request_id'];
        $client_id = isset($_SESSION['client_id']) ? $_SESSION['client_id'] : $_REQUEST['client_id'];
        $signature = sha1($_SESSION['customer_id'] . $request_id . $client_id . OAUTH_SECRET_KEY);
        $data_array = array('customer_id' => $_SESSION['customer_id'],
            'request_id' => $request_id,
            'client_id' => $client_id,
            'signature' => $signature);
        $get_data = http_build_query($data_array, '', '&');

        if (isset($_COOKIE['ogm']) && !empty($_COOKIE['ogm']['si']) && !empty($_COOKIE['ogm']['st']) && $cookie_check) {
            self::resetSSOToken($_COOKIE['ogm']['st'], $_COOKIE['ogm']['si']);
        }
        unset($_SESSION['request_id']);
        unset($_SESSION['client_id']);
        tep_redirect(HTTPS_OAUTH . self::OAUTH_AUTHORIZE_REQUEST_CODE . '?' . $get_data);
    }

    public static function resetSSOToken($ssoToken, $customerID) {
        $check_sso_sql = "  SELECT customers_id, UNIX_TIMESTAMP(datetime) as timestamp
                            FROM " . TABLE_SSO_TOKEN . "
                            WHERE sso_token  = '" . tep_db_prepare_input($ssoToken) . "'
                                AND customers_id = '" . tep_db_prepare_input($customerID) . "'";
        $check_sso_query = tep_db_query($check_sso_sql);
        $check_sso_result = tep_db_fetch_array($check_sso_query);
        if (!isset($check_sso_result['customers_id']) || $check_sso_result['timestamp'] < time()) {
            $sso_token = self::addSSOToken($customerID);
            $cookieLifeTime = 0; //time() + 86400
            $cookie_domain = (($request_type == 'NONSSL') ? HTTP_COOKIE_DOMAIN : HTTPS_COOKIE_DOMAIN);
            $cookie_path = (($request_type == 'NONSSL') ? HTTP_COOKIE_PATH : HTTPS_COOKIE_PATH);
            tep_setcookie('ogm[si]', $_SESSION['customer_id'], $cookieLifeTime, $cookie_path, $cookie_domain);
            tep_setcookie('ogm[st]', $sso_token, $cookieLifeTime, $cookie_path, $cookie_domain);
            $_SESSION['sso_token'] = $sso_token;
        }
    }

    public static function addSSOToken($cid) {
        $sso_token = oauth::generateSSOToken($cid);
        $batchId = (isset($_COOKIE['ogm']) && !empty($_COOKIE['ogm']['sb']) ? $_COOKIE['ogm']['sb'] : '');
        tep_db_query("INSERT IGNORE INTO " . TABLE_SSO_TOKEN . " (`sso_token`, `customers_id`, `batch_id`, `datetime`) VALUES ('" . $sso_token . "', '" . $cid . "', '" . $batchId . "', '" . date("Y-m-d H:i:s", strtotime("+2 HOUR")) . "')");
        return $sso_token;
    }

    private static function generateSSOToken($cid) {
        $salt = '';
        $sso_token = '';

        # salt
        for ($i = 0; $i < 10; $i++) {
            $salt .= oauth::generateRandom();
        }
        $salt = substr(md5($salt), 0, 2);

        $sso_token = md5($salt . date('YmdHis') . SSO_SECRET_KEY . $cid);

        return $sso_token;
    }

    public static function delSSOToken($cookie_path, $cookie_domain) {
        if (isset($_COOKIE['ogm']['st']) && isset($_COOKIE['ogm']['si'])) {
            $cookieLifeTime = time() - 3600;
            tep_setcookie('ogm[si]', '', $cookieLifeTime, $cookie_path, $cookie_domain);
            tep_setcookie('ogm[st]', '', $cookieLifeTime, $cookie_path, $cookie_domain);
            tep_setcookie('ogm[sb]', '', $cookieLifeTime, $cookie_path, $cookie_domain);

            $c_attr['sso_token'] = $_COOKIE['ogm']['st'];
            $c_attr['customers_id'] = $_COOKIE['ogm']['si'];
            $sso_delete_sql = "DELETE FROM " . TABLE_SSO_TOKEN . " WHERE sso_token='" . $c_attr['sso_token'] . "'";
            $sso_delete_result = tep_db_query($sso_delete_sql);

            global $memcache_obj;
            $memcache_obj->delete(TABLE_SSO_TOKEN . '/cid/' . $_COOKIE['ogm']['si'] . '/token/' . $c_attr['sso_token'], 0);
        }
    }

    private static function generateRandom($min = null, $max = null) {
        if (isset($min) && isset($max)) {
            if ($min >= $max) {
                return $min;
            } else {
                return mt_rand($min, $max);
            }
        } else {
            return mt_rand();
        }
    }

    public static function getSSOTokenStatus($token, $cid) {
        global $memcache_obj;

        $status = false;

        $cache_key = TABLE_SSO_TOKEN . '/cid/' . $cid . '/token/' . $token;
        $cache_result = $memcache_obj->fetch($cache_key);

        if ($cache_result !== FALSE) {
            $status = true;
        } else {
            $sso_sel_sql = "SELECT sso_token, datetime FROM " . TABLE_SSO_TOKEN . "
                            WHERE sso_token = '" . $token . "'
                                AND customers_id = '" . $cid . "'
                                AND datetime > '" . date('Y-m-d H:i:s') . "'";
            $sso_res_sql = tep_db_query($sso_sel_sql);

            if ($sso_row = tep_db_fetch_array($sso_res_sql)) {
                $status = true;
                $c_attr = array();

                $c_attr['datetime'] = date("Y-m-d H:i:s", strtotime("+2 hours"));
                tep_db_perform(TABLE_SSO_TOKEN, $c_attr, 'update', "sso_token = '" . $sso_token . "' AND customers_id = '" . $cid . "'");
                $memcache_obj->store($cache_key, 1, 900); // 15 minutes
            }
        }

        return $status;
    }

    public static function getValidSSOToken($cid) {
        $_data = array();
        if (isset($_COOKIE['ogm']) && !empty($_COOKIE['ogm']['sb'])) {
            $batchId = $_COOKIE['ogm']['sb'];

            $sso_delete_sql = "DELETE FROM " . TABLE_SSO_TOKEN . " WHERE customers_id = '" . $cid . "' AND datetime < NOW()";
            tep_db_query($sso_delete_sql);

            $sso_sel_sql = "SELECT sso_token, datetime FROM " . TABLE_SSO_TOKEN . "
                            WHERE
                                customers_id = '" . $cid . "' AND batch_id = '" . $batchId . "'
                            ORDER BY datetime desc
                            LIMIT 1";
            $sso_res_sql = tep_db_query($sso_sel_sql);
            $_data = tep_db_fetch_array($sso_res_sql);
        }
        return $_data;
    }

    public static function getBatchId() {
        $time = time();
        $browser = $_SERVER['HTTP_USER_AGENT'];
        $sessionId = session_id();
        $batchId = md5($time . ':' . $browser . ':' . $sessionId);
        return $batchId;
    }

}

?>