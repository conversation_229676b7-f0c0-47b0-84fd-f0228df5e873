<?php
require_once DIR_WS_INCLUDES . 'addon/facebook_sdk/facebook.php';
require_once DIR_WS_INCLUDES . 'addon/facebook_sdk/kontagent.php';
require_once DIR_WS_INCLUDES . 'addon/facebook_sdk/kt_comm_layer.php';

class ogm_facebook extends Facebook {

    public $FB_current_state = '';    // The current stage of the fb action.
    public $messageStack_class = '';   // Define message stack class name.
    public $FB_trigger_flag = array();   // The FB process flow array.
    public $user = null;      // Graph API does not has parameter user.
    public $FB_connect_switch = 1;    // Switch to turn FB ON (1) or OFF (0)
    public $FB_kontagent_switch = false;   // Switch to turn Kontagent ON (true) or OFF (false)
    public $current_page_info_obj;    // Required to retrive current page info
    public $FB_locales = null;     // FB Locales
    public $FB_open_graph_tags_existed = false; // FB meta tags created

    public function __construct($api_key, $secret, $generate_session_secret = false) {
        global $PHP_SELF;
        parent::__construct(array('appId' => FB_APPS_ID, 'secret' => $secret, 'cookie' => true));
        $this->kt = null;
        $this->FB_kontagent_switch = (KT_SERVICE_ENABLED == 'true') ? true : false;

        if (!isset($_SESSION['ses_ip_country'])) {
            $_SESSION['ses_ip_country'] = tep_get_ip_country_id();
        }

        if (in_array($_SESSION['ses_ip_country'], array('44'))) {
            $this->FB_connect_switch = 0;
            $this->FB_kontagent_switch = false;
        }

        if (isset($_REQUEST['loginsucc'])) {
            $redirect_uri = tep_href_link(basename($PHP_SELF), tep_get_all_get_params(array('code', 'state', 'loginsucc', 'logoutsucc')));
            $this->OGFB_account_registry();

            if (isset($_REQUEST['request_id'])) {
                echo "<script>document.domain = '" . JS_CROSS_DOMAIN . "'</script>";
            }

            if (isset($_REQUEST['fb_app']) && !empty($_REQUEST['fb_app'])) {
                echo "  <form action='" . $redirect_uri . "' method='post' name='fb_prelog'>
                            <input type='hidden' value='pre_login' name='fb_action'>
                        </form>
                        <script>
                            document.fb_prelog.submit();
                        </script>
                        ";
            } else {
                echo "<script>
                        var form = opener.document.createElement('form');
                        var hiddenField = opener.document.createElement('input');

                        form.method = 'post';
                        form.id = 'FBlink_selection_form';
                        form.action = (location.href.search(/logoff.php/i) > 0 || location.href.search(/header_template.php/i) > 0) ? '/' : '" . $redirect_uri . "';
                        hiddenField.type = 'hidden';
                        hiddenField.name = 'fb_action';
                        hiddenField.value = 'pre_login';
                        form.appendChild(hiddenField);

                        window.opener.document.body.appendChild(form);
                        window.opener.document.getElementById('FBlink_selection_form').submit();
                        window.opener.focus();
                        window.close();
                        </script>";
            }
            exit;
        } else if (isset($_REQUEST['logoutsucc'])) {
            $redirect_uri = tep_href_link(basename($PHP_SELF), tep_get_all_get_params(array('code', 'state', 'loginsucc', 'logoutsucc')));
            $this->clearAllPersistentData();
            tep_redirect($redirect_uri);
        }

        ## For Kontagent Only ##
        if (isset($_GET['kt_type'])) {
            switch ($_GET['kt_type']) {
                case 'ins':
                    // Call when user has sent the invitation.
                    $this->OGFB_call_kt_method(array('kt_action' => 'track_invite_sent'));
                    break;
                case 'inr':
                    if (!isset($_SESSION['fb_kt_ut']) || $_SESSION['fb_kt_ut'] != $_GET['fb_kt_ut']) {
                        $_SESSION['fb_kt_ut'] = tep_db_prepare_input($_GET['kt_ut']);
                        $this->OGFB_call_kt_method(array('kt_action' => 'track_invite_received'));
                    }

                    break;
            }// switch
        }// if
        ## For Kontagent Only ##

        $this->OGFB_account_registry();
    }

    public function OGFB_process_flow_state_controller() {
        $this->FB_current_state = isset($_REQUEST['fb_action']) ? $_REQUEST['fb_action'] : '';

        if ($this->FB_connect_switch == 0) {
            // Facebook Connect was turn Off.
            // --- FB[Logged-IN] & OGM[Logged-IN/OUT] Section
        } else if ($this->user) {

            ## Filter Special Unwanted Cases - Start
            if (isset($_SESSION['fb_uid'])) {
                if ($this->user != $_SESSION['fb_uid']) {
                    // Logout FB connect
                    $this->set_trigger_flag('REDIRECT_TO_CURRENT_PAGE_PLUS_LOGOUT_FB');
                } else if (!isset($_SESSION['customer_id'])) {
                    if (isset($_POST['fb_action']) && $_POST['fb_action'] == 'pre_login') {
                        unset($_SESSION['fb_uid']);
                    }
                }
            }
            ## Filter Special Unwanted Cases - End

            if (isset($_SESSION['customer_id'])) {

                if ($this->OGFB_account_connected()) {
                    // ** New Graph API will disconnect app. and $this->user will no long exist.
                    // ** If using JS SDK, below flow is no long valid.
                    // When Logged In FB and Connected
                    if ($this->FB_current_state == 'disconnect') {
                        $this->set_messageStack_class('facebook_connect');

                        $this->set_trigger_flag('EXISTING_USER_PROCESS_UNLINK');
                        $this->set_trigger_flag('REDIRECT_TO_FACEBOOK_CONNECT_PAGE');

                        // Cases where user click on FB Connect button even logged in ogm - System will redirect to login page again.
                    } else if ($this->FB_current_state == 'pre_login') {
                        $this->set_trigger_flag('REDIRECT_TO_FACEBOOK_CONNECT_PAGE');
                    }
                } else {
                    if ($this->FB_current_state == 'pre_login') {
                        $this->set_messageStack_class('facebook_connect');

                        $this->set_trigger_flag('LOGGED_USER_PROCESS_LINK');
                        $this->set_trigger_flag('REDIRECT_TO_FACEBOOK_CONNECT_PAGE');

                        // Not In use for state 'fb_logout'. It was replaced by FB.logout
                    } else if ($this->FB_current_state == 'fb_logout') {
                        $this->set_trigger_flag('REDIRECT_TO_CURRENT_PAGE_PLUS_LOGOUT_FB');
                    }
                }
            } else if (tep_not_null($this->FB_current_state)) {

                if ($this->OGFB_account_connected()) {
                    $this->set_trigger_flag('PREPARE_CONTENT_FOR_LOGIN_PAGE');
                } else {

                    // Pre FB login fancybox selection - create OGM account selection selected flow
                    if ($this->FB_current_state == 'selected_new') {
                        $this->set_trigger_flag('REGISTER_PRELOGIN_FLAG_S2');
                        $this->set_trigger_flag('REDIRECT_TO_CREATE_NEW_PAGE');

                        // Pre FB login fancybox selection - link with OGM account selection selected flow
                    } else if ($this->FB_current_state == 'selected_link') {
                        $this->set_trigger_flag('REGISTER_PRELOGIN_FLAG_S2');
                        $this->set_trigger_flag('REDIRECT_TO_LOGIN_PAGE');

                        // Internal create OGM account process - create new OGM account form submitted flow
                    } else if ($this->FB_current_state == 'process_create') {
                        $this->set_trigger_flag('NEW_USER_PROCESS_LINK');
                        $this->set_trigger_flag('UNREGISTER_PRELOGIN_FLAG');

                        // Internal link account process - link with OGM account form submitted flow
                    } else if ($this->FB_current_state == 'process_login') {
                        $this->set_trigger_flag('EXISTING_USER_PROCESS_LINK');
                        $this->set_trigger_flag('UNREGISTER_PRELOGIN_FLAG');
                        $this->set_trigger_flag('PREPARE_CONTENT_FOR_LOGIN_PAGE');

                        // FB connect button clicked flow
                    } else if ($this->FB_current_state == 'pre_login') {
                        $this->set_trigger_flag('REGISTER_PRELOGIN_FLAG');

                        // Client failed to complete OGFB connect.
                        // Not In use for state 'fb_logout'. It was replaced by FB.logout
                    } else if ($this->FB_current_state == 'fb_logout') {
                        $this->set_trigger_flag('REDIRECT_TO_CURRENT_PAGE_PLUS_LOGOUT_FB');
                    }
                }
            }

            // --- FB[Logged-OUT] & OGM[Logged-IN] Section
        } else if (isset($_SESSION['customer_id'])) {
            if ($this->FB_current_state == 'disconnect') {
                $this->set_messageStack_class('facebook_connect');

                $this->set_trigger_flag('EXISTING_USER_PROCESS_UNLINK');
                $this->set_trigger_flag('REDIRECT_TO_FACEBOOK_CONNECT_PAGE');
            }

            // --- FB[Logged-OUT] & OGM[Logged-OUT] Section
        } else {
            if (isset($_SESSION['FB_prelogin_switch'])) {
                unset($_SESSION['FB_prelogin_switch']);
            }
        }
    }

    public function OGFB_execute_state_controller($ext_param = '') {
        global $language;

        $process_status = true;
        $email = isset($_REQUEST['email_address']) ? $_REQUEST['email_address'] : '';
        $password = isset($_REQUEST['password']) ? $_REQUEST['password'] : '';

        foreach ($this->FB_trigger_flag AS $flag) {
            switch ($flag) {
                case 'REDIRECT_TO_MAIN_PAGE' :
                    $path = tep_href_link('/');
                    tep_redirect($path);
                    break;
                case 'REDIRECT_TO_CREATE_NEW_PAGE' :
                    $path = tep_href_link(FILENAME_EXPRESS_LOGIN, tep_get_all_get_params(array('fb_action', 'action')) . 'fb_action=show_create_new' . $ext_param);
                    tep_redirect($path);
                    break;
                case 'REDIRECT_TO_LOGIN_PAGE' :
                    $path = tep_href_link(FILENAME_EXPRESS_LOGIN, tep_get_all_get_params(array('fb_action', 'action')) . 'fb_action=show_login' . $ext_param);
                    tep_redirect($path);
                    break;
                case 'REDIRECT_TO_FACEBOOK_CONNECT_PAGE' :
                    $path = $this->baseURL_filtering(tep_href_link(FILENAME_FACEBOOK_CONNECT));
                    tep_redirect($path);
                    break;
                case 'REDIRECT_TO_CURRENT_PAGE_PLUS_LOGOUT_FB' :  // Not In use. It was replaced by FB.logout
                    $logout_page = tep_href_link(FILENAME_FACEBOOK_CONNECT);
                    tep_redirect($this->getLogoutUrl($logout_page));
                    break;
                case 'NEW_USER_PROCESS_LINK' :
                    require_once(DIR_WS_LANGUAGES . $language . '/' . FILENAME_LOGIN);
                    $this->set_messageStack_class('express_login');

                    $email_address = tep_not_null($_POST['account_email_address']) ? tep_db_prepare_input(strip_tags($_POST['account_email_address'])) : "";
                    $firstname = tep_not_null($_POST['firstname']) ? tep_db_prepare_input(strip_tags($_POST['firstname'])) : "";
                    $lastname = tep_not_null($_POST['lastname']) ? tep_db_prepare_input(strip_tags($_POST['lastname'])) : "";
                    $agreed = tep_not_null($_POST['agreed']) ? tep_db_prepare_input($_POST['agreed']) : '';

                    // Email error message
                    if (!tep_not_null($email_address)) {
                        $process_status = false;
                        $this->set_output_messages(ENTRY_EMAIL_ADDRESS_ERROR);
                    } else if (strlen($email_address) < ENTRY_EMAIL_ADDRESS_MIN_LENGTH) {
                        $process_status = false;
                        $this->set_output_messages(ENTRY_EMAIL_ADDRESS_ERROR);
                    } else if (tep_validate_email($email_address) == false) {
                        $process_status = false;
                        $this->set_output_messages(ENTRY_EMAIL_ADDRESS_CHECK_ERROR);
                    } else {
                        $check_email_query = tep_db_query("SELECT customers_id FROM " . TABLE_CUSTOMERS . " WHERE customers_email_address = '" . tep_db_input($email_address) . "'");
                        $check_email = tep_db_num_rows($check_email_query);

                        if ($check_email > 0) {
                            $process_status = false;
                            $this->set_output_messages(ENTRY_EMAIL_ADDRESS_ERROR_EXISTS);
                        }
                    }

                    // First Name error message
                    if (strlen($firstname) < ENTRY_FIRST_NAME_MIN_LENGTH) {
                        $process_status = false;
                        $this->set_output_messages(ENTRY_FIRST_NAME_ERROR);
                    }

                    // Last Name error message
                    if (strlen($lastname) < ENTRY_LAST_NAME_MIN_LENGTH) {
                        $process_status = false;
                        $this->set_output_messages(ENTRY_LAST_NAME_ERROR);
                    }

                    // Check customer if agreed to the terms & policy @ 200811061006
                    if (isset($agreed) && !tep_not_null($agreed)) {
                        $process_status = false;
                        $this->set_output_messages(ENTRY_SIGNUP_TERMSANDCONDITION_ERROR);
                    }

                    if (SIGN_UP_CAPTCHA_FIELD == 1) {
                        include_once(DIR_WS_CLASSES . 'recaptcha.php');
                        if (!tep_not_null($_POST['recaptcha_challenge_field']) || !tep_not_null($_POST['recaptcha_response_field'])) {
                            $process_status = false;
                            $this->set_output_messages(TEXT_CAPTCHA_MISSING_ERROR);
                        } else if (recaptcha::captcha_validation($_POST['recaptcha_challenge_field'], $_POST['recaptcha_response_field']) === false) {
                            $process_status = false;
                            $this->set_output_messages(TEXT_CAPTCHA_ERROR);
                        }
                    }

                    if ($process_status && tep_not_null($this->user)) {
                        $new_password = tep_create_random_value(ENTRY_PASSWORD_MIN_LENGTH);

                        $email_greeting = tep_get_email_greeting($firstname, $lastname, '');
                        $email_text2 = $email_greeting . EMAIL_PASSWORD_REMINDER_BODY . "\n\n" . EMAIL_FOOTER;
                        @tep_mail($firstname . ' ' . $lastname, $email_address, implode(' ', array(EMAIL_SUBJECT_PREFIX, EMAIL_PASSWORD_REMINDER_SUBJECT)), sprintf($email_text2, $email_address, $new_password), STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);

                        $_POST['password'] = $new_password;
                        $_POST['password_confirmation'] = $new_password;
                    } else {
                        $this->set_trigger_flag('REDIRECT_TO_CREATE_NEW_PAGE', true);
                        $this->OGFB_execute_state_controller('&action=failed');
                    }

                    break;
                case 'PREPARE_CONTENT_FOR_LOGIN_PAGE' :
                    $_POST['request_id'] = isset($_REQUEST['request_id']) ? $_REQUEST['request_id'] : '';
                    $_POST['client_id'] = isset($_REQUEST['client_id']) ? $_REQUEST['client_id'] : '';
                    $form_action = 'action=process';

                    if (isset($_REQUEST['baseURL'])) {
                        $form_action = $form_action . '&baseURL=' . urlencode($_REQUEST['baseURL']);
                    } elseif ($this->FB_current_state == 'process_login') {
                        $form_action = $form_action . '&baseURL=/' . FILENAME_EXPRESS_LOGIN;
                    }

                    if (isset($_POST['action']))
                        unset($_POST['action']);
                    if (isset($_POST['fb_action']))
                        unset($_POST['fb_action']);

                    $_SESSION['post_data_holder'] = $_POST;
                    tep_redirect(tep_href_link(FILENAME_LOGIN, $form_action, 'SSL'));

                    exit;
                    break;
                case 'EXISTING_USER_PROCESS_LINK' :
                    require_once(DIR_WS_LANGUAGES . $language . '/' . FILENAME_LOGIN);

                    $this->set_messageStack_class('express_login');

                    if (tep_not_null($email) && tep_not_null($password)) {
                        $query = "	SELECT customers_id, customers_password
		    						FROM " . TABLE_CUSTOMERS . "
		    						WHERE customers_email_address = '" . tep_db_input($email) . "'";
                        $result = tep_db_query($query);
                        if ($result_row = tep_db_fetch_array($result)) {
                            if (tep_validate_password($password, $result_row['customers_password'])) {
                                if ($this->get_FB_user_id($result_row['customers_id']) === FALSE) { // Force 1-1 relationship
                                    $this->create_connection($result_row['customers_id']);
                                } else {
                                    $process_status = false;
                                }
                            } else {
                                $process_status = false;
                            }
                        } else {
                            $process_status = false;
                        }
                    } else {
                        $process_status = false;
                    }

                    if (!$process_status) {
                        $this->set_output_messages(sprintf(TEXT_LOGIN_ERROR, '<a href="' . tep_href_link(FILENAME_LOGIN, 'action=forget_password', 'SSL') . '">' . TEXT_RESET_PASSWORD . '</a>'), "error");
                        $this->set_trigger_flag('REDIRECT_TO_LOGIN_PAGE', true);
                        $this->OGFB_execute_state_controller('&action=failed');
                    }

                    break;
                case 'EXISTING_USER_PROCESS_UNLINK' :
                    if (isset($_SESSION['customer_id'])) {
                        $customers_fb_uid = $this->get_FB_user_id();

                        if ($customers_fb_uid !== FALSE) {
                            try {
                                //If OGM account connected with FB but user want to disconnect the link without login FB.
                                $this->user = $customers_fb_uid;
                                $this->api(array('method' => 'auth.revokeAuthorization',
                                    'uid' => $this->user));

                                $query = " 	DELETE FROM " . TABLE_CUSTOMERS_CONNECTION . "
											WHERE customers_id = " . $_SESSION['customer_id'] . " AND provider='Facebook'";
                                tep_db_query($query);

                                $_SESSION['OGFB_user_disconnected'] = true;

                                $this->set_output_messages(SUCCESS_FB_DISCONNECTED, "success");
                            } catch (Exception $e) {
                                unset($_SESSION['fb_uid']);
                                $this->report_error("Processing [EXISTING_USER_PROCESS_UNLINK][action => auth.revokeAuthorization]: " . $e->getMessage());
                            }
                        } else {
                            $this->report_error("Processing [EXISTING_USER_PROCESS_UNLINK] : customers_fb_uid = null.");
                        }
                    }

                    break;
                case 'LOGGED_USER_PROCESS_LINK' :
                    if (isset($_SESSION['customer_id'])) {
                        if ($this->customer_has_connection($this->user) === FALSE) {
                            $customers_fb_uid = $this->get_FB_user_id();

                            if ($customers_fb_uid === FALSE) {
                                $this->create_connection();
                            } else if ($customers_fb_uid !== FALSE) {
                                $this->set_output_messages(ERROR_INVALID_FB_UID, "error");
                            }
                        } else {
                            $this->set_output_messages(ERROR_INVALID_FB_UID, "error");
                        }
                    }

                    break;
                case 'REGISTER_PRELOGIN_FLAG' :
                    $_SESSION['FB_prelogin_switch'] = 'STEP_1';

                    break;
                case 'REGISTER_PRELOGIN_FLAG_S2' :
                    $_SESSION['FB_prelogin_switch'] = 'STEP_2';

                    break;
                case 'UNREGISTER_PRELOGIN_FLAG' :
                    unset($_SESSION['FB_prelogin_switch']);

                    break;
                default:
                    break;
            }
        }

        return $process_status;
    }

    private function OGFB_call_kt_method($ext_params) {
        if ($this->FB_kontagent_switch) {
            try {
                if (!is_object($this->kt)) {
                    /* If Kontagent is not included, it will still work */
                    $this->kt = new Kontagent(KT_API_SERVER, KT_API_KEY);
                }

                switch ($ext_params['kt_action']) {
                    case 'track_install':
                        $this->kt->track_install($this->user);
                        break;
                    case 'track_uninstall':
                        $this->kt->track_uninstall($_SESSION['fb_uid']);
                        break;
                    case 'track_user_info':
                        $this->kt->track_user_info($this->user, $ext_params['user_info'], $ext_params['friends_info']);
                        break;
                    case 'gen_tracking_pageview_link':
                        return $this->kt->gen_tracking_pageview_link($this->user);
                        break;
                    case 'gen_kt_capture_user_info_key':
                        return $this->kt->gen_kt_capture_user_info_key(FB_APPS_ID, $this->user);
                        break;
                    case 'track_invite_sent':
                        $this->kt->track_invite_sent();
                        break;
                    case 'track_invite_received':
                        $this->kt->track_invite_received($this->user);
                        break;
                    case 'gen_long_tracking_code':
                        return $this->kt->gen_long_tracking_code();
                        break;
                    case 'gen_invite_post_link':
                        $default_link = $this->kt->gen_invite_post_link(tep_href_link(FILENAME_INVITER, 'step=fb_invite', 'SSL'), $ext_params['long_tracking_code'], $this->user);

                        return $default_link;
                        break;
                    case 'gen_invite_content_link':
                        $default_link = $this->kt->gen_invite_content_link(tep_href_link(FILENAME_DEFAULT, 'a_aid=' . $ext_params['customer_id'], 'SSL'), $ext_params['long_tracking_code']);

                        return $default_link;
                        break;
                    case 'capture_user_info':
                        $capture_user_info_key = $this->OGFB_call_kt_method(array('kt_action' => 'gen_kt_capture_user_info_key'));

                        if (!isset($_COOKIE[$capture_user_info_key])) {
                            $user_info = $this->api('/me');
                            $friends_info = $this->api('/me/friends');

                            $this->OGFB_call_kt_method(array('kt_action' => 'track_user_info', 'user_info' => $user_info, 'friends_info' => $friends_info));

                            if (!headers_sent()) {
                                setcookie($capture_user_info_key, 'done', time() + 1209600); // 30days(2592000), 2 weeks(1209600)
                            }
                        }
                        break;
                }
            } catch (Exception $e) {
                $this->FB_kontagent_switch = false;
                $message = isset($_SERVER['HTTP_REFERER']) ? 'Http referer : ' . $_SERVER['HTTP_REFERER'] : '';
                $message .= isset($ext_params['kt_action']) ? '<br>action request : ' . $ext_params['kt_action'] : '';
                $this->report_error("Processing [OGFB_call_kt_method] : " . $message . '<br>error : ' . $e->getMessage());
            }

            // FB_kontagent_switch is turned OFF
        } else {
            switch ($ext_params['kt_action']) {
                case 'gen_invite_post_link':
                    $default_link = tep_href_link(FILENAME_INVITER, 'step=fb_invite', 'SSL');

                    return $default_link;
                    break;
                case 'gen_invite_content_link':
                    $default_link = tep_href_link(FILENAME_DEFAULT, 'a_aid=' . $ext_params['customer_id'], 'SSL');

                    return $default_link;
                    break;
            }
        }

        return '';
    }

    // Just for new connection
    public function create_connection($customer_id = '') {
        if (!tep_not_null($customer_id) && isset($_SESSION['customer_id']))
            $customer_id = $_SESSION['customer_id'];

        if (tep_not_null($customer_id) && tep_not_null($this->user)) {
            $sql_insert_array = array(
                'customers_id' => $customer_id,
                'provider' => 'Facebook',
                'provider_uid' => $this->user
            );
            tep_db_perform(TABLE_CUSTOMERS_CONNECTION, $sql_insert_array);

            $this->OGFB_call_kt_method(array('kt_action' => 'track_install'));
            unset($sql_insert_array);
        }
    }

    private function OGFB_account_connected() {
        $return_flag = false;
        $customer_id = '';

        if (isset($_SESSION['fb_uid']) && isset($_SESSION['customer_id'])) {
            $return_flag = true;
        } else if (tep_not_null($this->user)) {
            if ($this->customer_has_connection($this->user, $customer_id)) {
                if (!isset($_SESSION['customer_id'])) {
                    // Used to register fb_uid session when user confirm connected
                    $_SESSION['OGFB_user_connected'] = true;

                    $customer_info_str = "	SELECT customers_email_address
											FROM " . TABLE_CUSTOMERS . "
											WHERE customers_id = " . $customer_id;
                    $customer_info_query = tep_db_query($customer_info_str);
                    $customer_info_result = tep_db_fetch_array($customer_info_query);

                    $_POST['email_address'] = $customer_info_result['customers_email_address'];

                    $return_flag = true;
                } else if ($_SESSION['customer_id'] == $customer_id) {
                    // Used to register fb_uid session when user confirm connected
                    $_SESSION['OGFB_user_connected'] = true;

                    // Update user registry because user has connected and customer id existed.
                    $this->OGFB_account_registry();

                    $return_flag = true;
                }
            }
        }

        return $return_flag;
    }

    private function OGFB_account_registry() {
        if ($this->FB_connect_switch == 1) {
            // User has/has not logged in FB
            try {
                $fb_user = $this->getUser();
            } catch (FacebookApiException $e) {
                $this->report_error("Method [OGFB_account_registry] : " . $e->getMessage());
            }

            if (tep_not_null($fb_user)) {
                // When user ds from OGM site, cookies required refresh to get updated
                // Unset fb_uid to removed unwanted call.
                if (isset($_SESSION['OGFB_user_disconnected'])) {
                    $this->OGFB_call_kt_method(array('kt_action' => 'track_uninstall'));
                    unset($_SESSION['OGFB_user_disconnected'], $_SESSION['fb_uid']);
                } else {
                    $this->user = $fb_user;

                    // When user has logged In FB and connected with OGM.
                    if (isset($_SESSION['OGFB_user_connected'])) {
                        $_SESSION['fb_uid'] = $fb_user;
                        unset($_SESSION['OGFB_user_connected']);

                        $this->OGFB_call_kt_method(array('kt_action' => 'capture_user_info'));
                    }
                }
            } else {
                // Only Connected User and has Logged In FB User has this session
                if (isset($_SESSION['fb_uid'])) {
                    unset($_SESSION['fb_uid']);
                }

                if (isset($_SESSION['FB_prelogin_switch'])) {
                    unset($_SESSION['FB_prelogin_switch']);
                }
            }
        }
    }

    private function retrieve_product_info() {
        $return_array = array();

        if (isset($_GET['products_id'])) {
            $return_array = tep_get_products_complete_description($_GET['products_id']);

            return $return_array;
        }
    }

    private function baseURL_filtering($url) {
        if (isset($_REQUEST['baseURL'])) {
            $url = str_replace(":", "", $_REQUEST['baseURL']);
        }

        return $url;
    }

    private function set_messageStack_class($class) {
        if ($class != '') {
            $this->messageStack_class = $class;
        }
    }

    private function set_output_messages($message, $type = 'error') {
        global $messageStack;

        if ($this->messageStack_class != '') {
            $messageStack->add_session($this->messageStack_class, $message, $type);
        }
    }

    private function set_trigger_flag($new_flag, $reset = false) {
        if (tep_not_null($new_flag)) {
            if ($reset) {
                unset($this->FB_trigger_flag);
            }

            $this->FB_trigger_flag[] = $new_flag;
        }
    }

    public function set_page_info_obj($page_info_obj) {
        $this->current_page_info_obj = $page_info_obj;
    }

    public function set_FB_locales($FB_locales) {
        if (tep_not_null($FB_locales)) {
            $this->FB_locales = $FB_locales;
        }
    }

    public function get_users_info() {
        $return_array = array();

        if ($this->user) {
            try {
                $my_info = $this->api('/me');

                $return_array['first_name'] = $my_info['first_name'];
                $return_array['last_name'] = $my_info['last_name'];
                $return_array['email'] = (strpos($my_info['email'], '@proxymail.facebook.com') === false && isset($my_info['email'])) ? $my_info['email'] : '';
            } catch (Exception $e) {
                $this->report_error("Method [get_users_info] : " . $e->getMessage());
            }
        }

        return $return_array;
    }

    public function get_logout_onclick($logout_page) {
        $logout_page .= strpos($logout_page, '?') === false ? '?logoutsucc=1' : '&logoutsucc=1';
        if (isset($_SESSION['fb_uid']) && $this->FB_connect_switch == 1) {
            //return "FB.logout(function(response) {window.location.href='".$logout_page."';});";
            return "window.location.href='" . $this->getLogoutUrl(array('next' => $logout_page)) . "'";
        } else {
            return "window.location.href='" . $logout_page . "';";
        }
    }

    public function get_FBML_tag($tag_type) {
        $return_str = '';

        if (!tep_not_null($this->user))
            return '';

        if ($tag_type == 'name') {
            $return_str = '<fb:name uid=loggedinuser useyou=false></fb:name>';
        }

        return $return_str;
    }

    public function get_FB_button($btn_type = '', $ext_param = '') {
        global $PHP_SELF;
        $return_str = '';

        if ($this->FB_connect_switch == 1) {
            if ($btn_type == 'disconnect') {
                $return_str = '<a href="' . tep_href_link(FILENAME_FACEBOOK_CONNECT, 'fb_action=disconnect', 'SSL') . '">' . $ext_param . '</a>';
            } else if ($btn_type == 'like') {
                if ($this->FB_open_graph_tags_existed) {
                    $return_str = '&nbsp;<fb:like layout="button_count" show_faces="false"></fb:like>';
                }
            } else if ($btn_type == 'iframe_like') {
                $return_str = '<iframe src="http://www.facebook.com/plugins/like.php?href=http%3A%2F%2Fwww.facebook.com%2Foffgamers&amp;layout=standard&amp;show_faces=false&amp;width=450&amp;action=like&amp;font=arial&amp;colorscheme=light&amp;height=35" scrolling="no" frameborder="0" style="border:none; overflow:hidden; width:350px; height:35px;" allowTransparency="true"></iframe>';
            } else {
                $params_str = tep_get_all_get_params(array('code', 'state', 'loginsucc', 'logoutsucc'));
                $params_str = $params_str == '' ? 'loginsucc=1' : $params_str . 'loginsucc=1';
                $params_str .= $ext_param ? '&' . $ext_param : '';
                $redirect_uri = tep_href_link(basename($PHP_SELF), $params_str);
                if ($btn_type == 'ogm_connect_facebook')
                    $redirect_uri = tep_href_link(FILENAME_LOGIN, $params_str);
                $loginUrl = $this->getLoginUrl(array('display' => 'popup', 'redirect_uri' => $redirect_uri, 'scope' => FB_REQUIRED_FB_PERMISSION));

                if ($btn_type == 'connect') {
                    $return_str = '<div class="facebookConnect_footerbar"><a href="javascript:void(0);" onclick="trigger_FB_login(\'' . $loginUrl . '\');"></a></div>';
                } else if ($btn_type == 'connect_with_facebook' || $btn_type == 'login_with_facebook') {
                    $return_str = '<div class="connect_with_fb_btn"><a href="javascript:void(0);" onclick="trigger_FB_login(\'' . $loginUrl . '\');" class=""></a></div>';
                } else if ($btn_type == 'ogm_connect_facebook') {
                    $return_str = '<div class="connect_with_fb_btn"><a href="javascript:void(0);" onclick="_gaq.push([\'_trackEvent\', \'Acquisition\', \'FB\', \'R3K\']);trigger_FB_login(\'' . $loginUrl . '\');" class=""></a></div>';
                } else if ($btn_type == 'footer_bar_fb') {
                    $return_str = '<a class="ahds3" href="javascript:void(0);" onclick="trigger_FB_login(\'' . $loginUrl . '\');"><span class="fbIcon"></span>' . MY_ACCOUNT_FACEBOOK_CONNECT . '</a>';
                }
            }
        }

        return $return_str;
    }

    public function get_FB_like_box() {
        return '<iframe src="http://www.facebook.com/plugins/likebox.php?id=***********&amp;width=187&amp;connections=9&amp;stream=false&amp;header=false&amp;height=370" scrolling="no" frameborder="0" style="border:none; overflow:hidden; width:187px; height:370px;" allowTransparency="true"></iframe>';
    }

    public function get_FB_locales() {
        return tep_not_null($this->FB_locales) ? $this->FB_locales : 'en_US';
    }

    public function get_FB_multi_friend_selector() {
        global $customer_id, $language;

        if (!defined('TEXT_FACEBOOK_FRIEND_SELECTOR_CONTENT')) {
            require_once(DIR_WS_LANGUAGES . $language . '/' . FILENAME_INVITER);
        }

        if ($this->FB_connect_switch == 0) {
            $return_str = 'Facebook service not available.';
        } else if (isset($_SESSION['fb_uid'])) {
            $fb_user = $_SESSION['fb_uid'];

            // For Kontagent
            $long_tracking_code = $this->OGFB_call_kt_method(array('kt_action' => 'gen_long_tracking_code'));
            $invite_action_link = $this->OGFB_call_kt_method(array('kt_action' => 'gen_invite_post_link',
                'long_tracking_code' => $long_tracking_code));
            $invite_content_link = $this->OGFB_call_kt_method(array('kt_action' => 'gen_invite_content_link',
                'long_tracking_code' => $long_tracking_code,
                'customer_id' => $customer_id));
            // For Kontagent
            // Retrieve array of friends who've already authorized the app.
            $query = "SELECT uid FROM user WHERE is_app_user = 1 AND uid IN (SELECT uid2 FROM friend WHERE uid1 = $fb_user)";
            $_friends = $this->api(array('method' => 'fql.query', 'query' => $query));

            // Extract the user ID's returned in the FQL request into a new array.
            $friends = array();
            if (is_array($_friends) && count($_friends)) {
                foreach ($_friends as $friend) {
                    $friends[] = $friend['uid'];
                }
            }

            // Convert the array of friends into a comma-delimeted string.
            $friends = implode(',', $friends);

            ob_start();
            ?>
            <div id="invite_form" style="margin: 0px auto; width: 630px;">
                <fb:serverFbml width="630px">
                    <script type="text/fbml">
                        <fb:fbml>
                        <fb:request-form
                        action='<?= $invite_action_link ?>'
                        method='POST'
                        type='OffGamers'
                        content='<?= htmlentities(sprintf(TEXT_FACEBOOK_FRIEND_SELECTOR_CONTENT, $fb_user, $customer_id, $invite_content_link), ENT_COMPAT, "UTF-8") ?>'>
                        <fb:multi-friend-selector
                        cols="4"
                        max="30"
                        showborder="false"
                        email_invite="false"
                        exclude_ids="<?= $friends ?>"
                        actiontext="<?= TEXT_FACEBOOK_FRIEND_SELECTOR_ACTIONTEXT ?>">
                        </fb:request-form>
                        </fb:fbml>
                    </script>
                </fb:serverfbml>
            </div>
            <?
            $return_str = ob_get_contents();
            ob_end_clean();
        } else if ($this->get_FB_user_id() !== FALSE) {
            $return_str = '<center>Your account has connected. Please login using Facebook Connect.</center>';
        } else {
            $return_str = $this->get_FB_button('login_with_facebook');
        }

        return $return_str;
    }

    public function get_FB_open_graph_tags() {
        $return_tags = '';

        if (is_object($this->current_page_info_obj) && $this->FB_connect_switch == 1) {
            switch ($this->current_page_info_obj->tpl) { // Product Type
                case 0 :
                    if ((int) $this->current_page_info_obj->has_product) {
                        if (tep_not_null($this->current_page_info_obj->categories_id)) {
                            $this->FB_open_graph_tags_existed = true;

                            $cPath = explode('_', $this->current_page_info_obj->categories_id);
                            $current_category_id = array_pop($cPath);

                            ob_start();
                            ?>
                            <meta property="og:title" content="<?= htmlspecialchars(strip_tags(tep_get_categories_heading_title($current_category_id))) ?>"/>
                            <meta property="fb:app_id" content="<?= $this->getAppId(); ?>"/>
                            <meta property="og:type" content="game"/>
                            <meta property="og:url" content="<?= $this->curPageURL() ?>"/>
                            <meta property="og:image" content="<?= 'http://image.offgamers.com/img/facebook/fb-ogm-90x90.gif' ?>"/>
                            <meta property="og:site_name" content="<?= STORE_NAME ?>"/>
                            <meta property="og:description" content="<?= htmlspecialchars(META_TAG_DESCRIPTION) ?>"/>
                            <?
                            $return_tags = ob_get_contents();
                            ob_end_clean();
                        }
                    }
                    break;
                case 1 :
                case 2 :
                    if (basename($_SERVER[SCRIPT_NAME]) == FILENAME_CUSTOM_PRODUCT_INFO) {
                        $this->FB_open_graph_tags_existed = true;
                        $product_info_array = $this->retrieve_product_info();

                        ob_start();
                        ?>
                        <meta property="og:title" content="<?= htmlspecialchars(strip_tags($product_info_array['products_name'])) ?>"/>
                        <meta property="fb:app_id" content="<?= $this->getAppId(); ?>"/>
                        <meta property="og:type" content="game"/>
                        <meta property="og:url" content="<?= $this->curPageURL() ?>"/>
                        <meta property="og:image" content="<?= 'http://image.offgamers.com/img/facebook/fb-ogm-90x90.gif' ?>"/>
                        <meta property="og:site_name" content="<?= STORE_NAME ?>"/>
                        <meta property="og:description" content="<?= htmlspecialchars(META_TAG_DESCRIPTION) ?>"/>
                        <?
                        $return_tags = ob_get_contents();
                        ob_end_clean();
                    }
                    break;
                default:
            }
        }

        return $return_tags;
    }

    public function get_FB_picture($img_type = '', $img_width = '', $img_height = '') {
        if (!tep_not_null($this->user))
            return '';

        // FB has 3 sizes: square (50x50), small (50 pixels wide, variable height), and large (about 200 pixels wide, variable height)
        if ($img_type == 'custom_small') {
            return "<img src='https://graph.facebook.com/" . $this->user . "/picture?type=small' width='" . $img_width . "' height='" . $img_height . "'/>";
        } elseif ($img_type == 'square') {
            return "<img src='https://graph.facebook.com/" . $this->user . "/picture?type=square'/>";
        }

        return '';
    }

    public function get_FB_prelogin_status() {
        if (isset($_SESSION['FB_prelogin_switch']) && tep_not_null($_SESSION['FB_prelogin_switch'])) {
            return $_SESSION['FB_prelogin_switch'];
        } else {
            return '';
        }
    }

    public function get_FB_user_id($ogm_cust_id = '') {
        $fb_user_id = false;

        if (!tep_not_null($ogm_cust_id) && isset($_SESSION['customer_id']))
            $ogm_cust_id = $_SESSION['customer_id'];

        $fb_user_select_sql = "	SELECT provider_uid
								FROM " . TABLE_CUSTOMERS_CONNECTION . "
								WHERE customers_id = '" . $ogm_cust_id . "' AND provider='Facebook'";
        $fb_user_result_sql = tep_db_query($fb_user_select_sql);
        if ($fb_user_row = tep_db_fetch_array($fb_user_result_sql)) {
            $fb_user_id = $fb_user_row['customers_fb_uid'];
        }

        return $fb_user_id;
    }

    public function customer_has_connection($uid, &$customer_id = '') {
        $return_flag = false;

        if ($uid) {
            $query_str = "	SELECT customers_id
							FROM " . TABLE_CUSTOMERS_CONNECTION . "
							WHERE provider='Facebook' AND provider_uid = " . $uid;
            $result_sql = tep_db_query($query_str);
            if ($result_row = tep_db_fetch_array($result_sql)) {
                $return_flag = true;
                $customer_id = $result_row['customers_id'];

                if (tep_db_num_rows($result_sql) > 1) {
                    $query = " 	DELETE FROM " . TABLE_CUSTOMERS_CONNECTION . "
                                WHERE customers_id != '" . $customer_id . "'
                                AND provider = 'Facebook'
                                AND provider_uid = '" . $uid . "'";
                    tep_db_query($query);
                }
            }
        }

        return $return_flag;
    }

    public function get_FB_js_controller() {
        global $PHP_SELF;
        $return_str = '';

        $params_str = tep_get_all_get_params(array('code', 'state', 'loginsucc', 'logoutsucc'));
        $params_str = $params_str == '' ? 'logoutsucc=1' : $params_str . 'logoutsucc=1';
        $redirect_uri = tep_href_link(basename($PHP_SELF), $params_str);

        ob_start();
        ?>
        FB_config = {
        FB_UID: "<?= $this->user ?>",
        js_fb_connect_switch: <?= $this->FB_connect_switch ?>,
        js_prelink_selection_flag: <?= ($this->get_FB_prelogin_status() == 'STEP_1' ? 1 : 0) ?>,
        js_fb_logout_link: "<?= $this->getLogoutUrl(array('next' => $redirect_uri)) ?>",
        js_FILENAME_EXPRESS_LOGIN: "<?= tep_href_link(FILENAME_EXPRESS_LOGIN, tep_get_all_get_params()) ?>",
        txt_expressLoginTitle: "<?= HEADER_FB_CONNECT_SELECTION ?>",
        txt_expressLoginUserInfo: ["<?= $this->get_FB_picture('square') ?>",
        "<?= sprintf(TEXT_FB_CONNECT_SELECTION, $this->get_FBML_tag('name')) ?>"],
        lbl_expressLoginFirstSel: "<?= OPTION_FB_CONNECT_SELECTION_FIRST ?>",
        lbl_expressLoginSecondSel: "<?= OPTION_FB_CONNECT_SELECTION_SECOND ?>"
        };
        <?
        if ($this->FB_connect_switch == 1) {
            ?>
            window.fbAsyncInit = function() {
            FB.init({
            appId   : '<?php echo $this->getAppId(); ?>',
            session : <?php echo json_encode($this->session); ?>, // don't refetch the session when PHP already has it
            status  : true, // check login status
            cookie  : true, // enable cookies to allow the server to access the session
            xfbml   : true,  // parse XFBML
            channelUrl: '<?= tep_href_link("channel.html", '', 'SSL') ?>'
            });
            };

            (function() {
            var e = document.createElement('script');
            e.src = document.location.protocol + '//connect.facebook.net/<?php echo $this->get_FB_locales(); ?>/all.js';
            e.async = true;
            document.getElementById('fb-root').appendChild(e);
            }());
            <?
        }

        $return_str = ob_get_contents();
        ob_end_clean();

        return $return_str;
    }

    public function get_KT_tracking_script() {
        if ($this->FB_kontagent_switch) {
            return "<img src='" . $this->OGFB_call_kt_method(array('kt_action' => 'gen_tracking_pageview_link')) . "' width='0px' height='0px' style='display:none;'/>";
        }
        return;
    }

    public function get_login_url($redirect_uri, $scope, $display = 'popup') {
        return $this->getLoginUrl(array('display' => $display, 'redirect_uri' => $redirect_uri, 'scope' => $scope));
    }

    private function curPageURL() {
        $pageURL = 'http';
        if ($_SERVER["HTTPS"] == "on") {
            $pageURL .= "s";
        }
        $pageURL .= "://";

        if ($_SERVER["SERVER_PORT"] != "80") {
            $pageURL .= $_SERVER["SERVER_NAME"] . ":" . $_SERVER["SERVER_PORT"] . $_SERVER["REQUEST_URI"];
        } else {
            $pageURL .= $_SERVER["SERVER_NAME"] . $_SERVER["REQUEST_URI"];
        }

        return $pageURL;
    }

    private function report_error($message) {
        $subject = '[OFFGAMERS] FB Connect Error from ' . HTTP_SERVER . ' - ' . date("F j, Y H:i");
        @tep_mail('OffGamers', '<EMAIL>', $subject, $message, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
    }

}
?>