<?php

class ogm_kount {

    private $merchant_id = KOUNT_MERCHANT_ID;
    private $api_key = KOUNT_RIS_API_KEY;
    private $order_id, $extended_order_ids, $transaction_type;  // OGM Order Type [CO/BO]
    private $device_id;
    private $enabled = KOUNT_ENABLED;
    private $extended_variables_array = array();
    private $missing_fields = array();
    private $query_url = KOUNT_RIS_URL;
    private $trans_id_table_array = null;
    private $max_duration = 144000;  // 100 days
    // Declare the database tables fields used to map with the API response data. MAPPING LIKE [ DB_FIELD => API_FIELD ]
    private $db_tables = array(TABLE_API_TM_BROWSER => array('browser_language' => 'LANGUAGE',
            'browser_string' => 'UAS',
            'enabled_js' => 'JAVASCRIPT',
            'enabled_fl' => 'FLASH',
            'enabled_ck' => 'COOKIES',
            'enabled_im' => '',
            'css_image_loaded' => '',
            'flash_version' => '',
            'flash_lang' => '',
            'flash_os' => '',
            'headers_name_value_hash' => '',
            'headers_order_string_hash' => '',
            'http_os_signature' => '',
            'http_referer' => 'SITE',
            'plugin_adobe_acrobat' => '',
            'plugin_flash' => '',
            'plugin_hash' => '',
            'plugin_silverlight' => ''
        ),
        TABLE_API_TM_DEVICE => array('device_id' => 'FINGERPRINT',
            'device_result' => '',
            'os' => 'OS',
            'screen_res' => 'DSR',
            'local_time_offset' => '',
            'local_time_offset_range' => '',
            'time_zone' => 'TIMEZONE',
            'device_score' => '',
            'device_attributes' => '',
            'device_activities' => '',
            'device_assert_history' => '',
            'device_last_update' => '',
            'device_worst_score' => '',
            'profiling_datetime' => '',
            'device_first_seen' => 'DDFS',
            'device_last_event' => '',
            'device_match_result' => '',
            'offset_measure_time' => '',
            'os_anomaly' => '',
            'os_fonts_hash' => '',
            'os_fonts_number' => ''
        ),
        TABLE_API_TM_PROXY_IP => array('proxy_ip' => 'IP_IPAD',
            'proxy_ip_score' => '',
            'proxy_ip_attributes' => '',
            'proxy_ip_activities' => '',
            'proxy_ip_assert_history' => '',
            'proxy_ip_last_update' => '',
            'proxy_ip_worst_score' => '',
            'proxy_ip_city' => 'IP_CITY',
            'proxy_ip_geo' => 'IP_COUNTRY',
            'proxy_ip_isp' => '',
            'proxy_ip_latitude' => 'IP_LAT',
            'proxy_ip_longitude' => 'IP_LON',
            'proxy_type' => '',
            'proxy_ip_first_seen' => '',
            'proxy_ip_last_event' => '',
            'proxy_ip_organization' => 'IP_ORG',
            'proxy_ip_region' => 'IP_REGION',
            'proxy_ip_result' => ''
        ),
        TABLE_API_TM_RISK_SUMMARY_N_POLICY => array('summary_risk_score' => 'SCOR',
            'policy_score' => 'SCOR',
            'reason_code' => 'REASON_CODE'
        ),
        TABLE_API_TM_TRUE_IP => array('true_ip' => 'PIP_IPAD',
            'true_ip_activities' => '',
            'true_ip_attributes' => '',
            'true_ip_city' => 'PIP_CITY',
            'true_ip_geo' => 'PIP_COUNTRY',
            'true_ip_isp' => '',
            'true_ip_last_update' => '',
            'true_ip_latitude' => 'PIP_LAT',
            'true_ip_longitude' => 'PIP_LON',
            'true_ip_worst_score' => '',
            'true_ip_score' => '',
            'true_ip_first_seen' => '',
            'true_ip_last_event' => '',
            'true_ip_organization' => 'PIP_ORG',
            'true_ip_region' => 'PIP_REGION',
            'true_ip_result' => ''
        ),
        TABLE_API_TM_TRANSACTION_IDENTIFIER => array('transaction_id' => 'ORDR',
            'request_result' => '',
            'customers_id' => 'CUSTOMER_ID',
            'customers_login_ip' => 'CUSTOMERS_LOGIN_IP',
            'customers_login_date' => 'CUSTOMER_LOGIN_DATETIME',
            'request_id' => 'TRAN',
            'device_id' => 'FINGERPRINT',
            'transaction_type' => 'TRANSACTION_TYPE',
        ),
        TABLE_API_TM_FUZZY_DEVICE => array('fuzzy_device_id' => '',
            'fuzzy_device_first_seen' => '',
            'fuzzy_device_id_confidence' => '',
            'fuzzy_device_last_event' => '',
            'fuzzy_device_last_update' => '',
            'fuzzy_device_match_result' => '',
            'fuzzy_device_result' => '',
            'fuzzy_device_score' => '',
            'fuzzy_device_worst_score' => ''
        ),
        'EXTENDED_VARIABLES_ARRAY' => array('local_attrib_3' => ''), //extended_order_ids
    );
    private $feedback_fields = array(
        'CUSTOMER_ID' => 'customers_id',
        'CUSTOMERS_LOGIN_IP' => 'customers_login_ip',
        'CUSTOMER_LOGIN_DATETIME' => 'customers_login_datetime',
        'TRANSACTION_TYPE' => 'transaction_type',
    );
    private $udf_fields = array(
        'CUSTOMER_LOGIN_DATE' => 'customers_login_date',
        'TRANSACTION_TYPE' => 'transaction_type',
    );

    public function __construct($order_id = '', $order_type = 'CO') {
        if ($this->enabled == 'true') {
            $this->enabled = 1; // Turn ON
        } else {
            $this->enabled = 0; // Turn OFF
        }

        $this->set_order_id($order_id);
        $this->set_transaction_type($order_type);
    }

    public function execute_stored_query_call($orders_id, $customer_id = 0) {
        if ($this->enabled) {
            $this->set_order_id($orders_id);
            $pass_params = array();

            $get_query_select_sql = "SELECT transaction_type, extra_info FROM " . TABLE_API_KOUNT_QUERY_QUEUE . " WHERE orders_id = '" . $this->order_id . "'";
            $get_query_result_sql = tep_db_query($get_query_select_sql);
            if ($get_query_row = tep_db_fetch_array($get_query_result_sql)) {
                $extra_info_array = json_decode($get_query_row['extra_info'], true);

                $p_arr = explode(':~:', $extra_info_array['payment']);
                $payment_type = '';
                if (!empty($p_arr) && count($p_arr) == 3) {
                    $payment_type = $p_arr[1];
                }

                foreach ($extra_info_array['products_info'] as $index => $product_info) {
                    $price = round(preg_replace("/[^0-9,.]/", "", $product_info['amount']) * 100);
                    $extra_info_array['products_info'][$index]['amount'] = $price;
                }

                $pass_params = array(
                    'orders_id' => $this->order_id,
                    'transaction_type' => $get_query_row['transaction_type'],
                    'session_id' => $extra_info_array['session_id'],
                    'customers_id' => $extra_info_array['customer_id'],
                    'customers_name' => $extra_info_array['customer_info']['firstname'] . ' ' . $extra_info_array['customer_info']['lastname'],
                    'customers_login_ip' => $extra_info_array['customers_login_ip'],
                    'customers_login_date' => date('Y-m-d H:i:s', $extra_info_array['timestamp']),
                    'customers_login_datetime' => date('Y/m/d H:i:s', $extra_info_array['timestamp']),
                    'session_timestamp' => $extra_info_array['timestamp'],
                    'currency_code' => $extra_info_array['currency_code'],
                    'email' => $extra_info_array['customer_info']['email_address'],
                    'payment_token' => $extra_info_array['payment_token'],
                    'payment_type' => $payment_type,
                    'total' => $extra_info_array['amount'] * 100,
                    'customers_billing_address' => $extra_info_array['customer_info']['street_address'],
                    'customers_billing_city' => $extra_info_array['customer_info']['city'],
                    'customers_billing_state' => $extra_info_array['customer_info']['state'],
                    'customers_billing_country' => $extra_info_array['customer_info']['country_ISO'],
                    'customers_billing_postcode' => $extra_info_array['customer_info']['postcode'],
                    'creditcard_verification' => isset($extra_info_array['creditcard_verification']) ? $extra_info_array['creditcard_verification'] : null,
                    'creditcard_expiry' => isset($extra_info_array['creditcard_expiry']) ? $extra_info_array['creditcard_expiry'] : null,
                    //Array
                    'products_info' => $extra_info_array['products_info'],
                );

                if ($this->execution_rules($customer_id)) {
                    $this->execute_query_call($pass_params);
                }

                $delete_expired_records_sql = "	DELETE FROM " . TABLE_API_KOUNT_QUERY_QUEUE . " 
                                                WHERE created_datetime < DATE_SUB(NOW(),INTERVAL 1 DAY)";
                tep_db_query($delete_expired_records_sql);

                $delete_order_record_sql = "	DELETE FROM " . TABLE_API_KOUNT_QUERY_QUEUE . " 
                                                WHERE orders_id = '" . $this->order_id . "'";
                tep_db_query($delete_order_record_sql);
                unset($extra_info_array, $pass_params);
            }
        }
    }

    private function execute_query_call($ext_params = '') {
        if (!tep_not_null($ext_params)) {
            return;
        }
        $session_timestamp = $ext_params['session_timestamp'];

        $checking_str = '<br>CREATE DT: ' . date("d/m/y : H:i:s", $session_timestamp)
                . '<br>Call DT: ' . date("d/m/y : H:i:s")
                . '<br>Total Time Spend In Seconds: ' . (time() - $session_timestamp)
                . '<br><br>URL: ' . $this->query_url
                . '<br><br>Orders ID: ' . $ext_params['orders_id'];

        require_once DIR_WS_INCLUDES . 'addon/kount_ris_sdk/autoload.php';
        require_once(DIR_WS_INCLUDES . 'classes/ogm_kount_ris.php');

        $kount_settings = new Kount_Ris_ArraySettings(array(
            'MERCHANT_ID' => $this->merchant_id,
            'URL' => $this->query_url,
            'API_KEY' => $this->api_key,
            'CONNECT_TIMEOUT' => 30,
            'IS_PROXY_ENABLED' => false, //WWW_USE_PROXY_SERVER == 'true',
        ));

        $request = new ogm_kount_ris($kount_settings);
        $this->prepareRequest($request, $ext_params);
        try {
            $response = $request->getResponse();

            if ($response->getMode() == 'E') {
                $this->report_error('Kount Response Error: ' . $response->getErrorCode() . ' - ' . $response->getParm('ERROR_0') . '<br>' . $checking_str);
            } else {
                $this->device_id = $response->getFingerPrint();
                $api_array = $response->asArray();
                $api_array = $this->crunch_data($api_array);
                $mapped_array = $this->map_db_fields_with_api_return_fields($api_array, $ext_params);
                $content_extended_array = $this->extend_data_content($mapped_array, $request->asArray(), $ext_params);
                $db_insert_array = $this->duplicate_for_grouped_orders($content_extended_array);
                $this->insert_api_data_into_db($db_insert_array);

                unset($mapped_array, $content_extended_array, $db_insert_array);
            }
        } catch (Exception $e) {
            $this->report_error('CURL Error Found: ' . $e . '<br>' . $checking_str);
        }
    }

    private function prepareRequest(Kount_Ris_Request_Inquiry $request, $ext_params) {
        $cartlist = array();

        foreach ($ext_params['products_info'] as $product_info) {
            $cartlist[] = new Kount_Ris_Data_CartItem('product', $product_info['name'], $product_info['name'], $product_info['qty'], $product_info['amount']);
        }

        $request
                ->setAuth('A')
                ->setIpAddress($ext_params['customers_login_ip'])
                ->setCurrency($ext_params['currency_code'])
                ->setEmail($ext_params['email'])
                ->setMack('Y')
                ->setWebsite('DEFAULT')
                ->setTotal($ext_params['total'])
                ->setSessionId($ext_params['session_id'])
                ->setOrderNumber($ext_params['orders_id'])
                ->setUnique($ext_params['customers_id'])
                ->setName($ext_params['customers_name'])
                ->setBillingAddress(
                        $ext_params['customers_billing_address'], '', //Address 2
                        $ext_params['customers_billing_city'], $ext_params['customers_billing_state'], $ext_params['customers_billing_postcode'], $ext_params['customers_billing_country'])
                ->setCart($cartlist);

        //UDF Fields
        foreach ($this->udf_fields as $key => $val) {
            if (isset($ext_params[$val])) {
                $request->setUserDefinedField($key, $ext_params[$val]);
            }
        }

        //Payment Type
        if ($ext_params['payment_type'] != '' && $ext_params['payment_token'] != '') {
            switch ($ext_params['payment_type']) {
                case 'paypal':
                    $request->setPayPalPayment($ext_params['payment_token']);
                    break;

                case 'googlewallet':
                    $request->setGooglePayment($ext_params['payment_token']);
                    break;

                case 'moneybookers':
                    $request->setPayment('SKRILL', $ext_params['payment_token']);
                    break;

                case 'adyen':
                    $request->setParm('PENC', 'MASK');
                    $request->setCardPayment($ext_params['payment_token']);
                    if (isset($ext_params['creditcard_verification'])) {
                        $request->setCvvr($ext_params['creditcard_verification'] ? 'M' : 'N');
                    }

                    if (isset($ext_params['creditcard_expiry'])) {
                        list($mth, $yr) = explode('/', $ext_params['creditcard_expiry']);
                        $request->setExpirationMonth(str_pad($mth, 2, "0", STR_PAD_LEFT));
                        $request->setExpirationYear($yr);
                    }
                    break;

                default:
                    $request->setNoPayment();
            }
        } else {
            $request->setNoPayment();
        }
    }

    private function execution_rules($customers_id) {
        $fire_list = array(1, 2, 12);
        $return_bool = TRUE;

        if ($customers_id) {
            $customers_groups_id_select_sql = "	SELECT customers_aft_groups_id
                                                FROM " . TABLE_CUSTOMERS . "
                                                WHERE customers_id = '" . $customers_id . "'";
            $customers_groups_id_result_sql = tep_db_query($customers_groups_id_select_sql);
            if ($customers_groups_id_row = tep_db_fetch_array($customers_groups_id_result_sql)) {
                if (!in_array((int) $customers_groups_id_row['customers_aft_groups_id'], $fire_list)) {
                    $return_bool = FALSE;
                }
            }
        }

        return $return_bool;
    }

    private function crunch_data($api_array) {
        if (isset($api_array['PROXY']) && $api_array['PROXY'] == 'N' && empty($api_array['PIP_IPAD']) && !empty($api_array['IP_IPAD'])) {
            $transfer_ip_array = array(
                'IP_IPAD', 'IP_CITY', 'IP_COUNTRY', 'IP_LAT', 'IP_LON', 'IP_ORG', 'IP_REGION',
            );

            foreach ($transfer_ip_array as $key) {
                $api_array['P' . $key] = $api_array[$key];
                $api_array[$key] = '';
            }
        }

        return $api_array;
    }

    // DB table fields Mapping and return as format {MAPPED_ARRAY[DB_TABLES][DB PARAMETER] = API VALUE}
    private function map_db_fields_with_api_return_fields($api_array, $ext_params) {
        $return_array = array();

        if (!empty($api_array)) {
            $this->missing_fields = $api_array;

            //Feedback Param Fields
            foreach ($this->feedback_fields as $api_key => $field_key) {
                if (isset($ext_params[$field_key])) {
                    $api_array[$api_key] = $ext_params[$field_key];
                }
            }

            foreach ($this->db_tables AS $table_name => $table_fields_array) {
                $db_table_not_required_insert = true;

                foreach ($table_fields_array AS $db_field => $api_field) {
                    if ($api_field == '') {
                        $return_array[$table_name][$db_field] = '';
                    } else {
                        if (!empty($api_array[$api_field])) {
                            $return_array[$table_name][$db_field] = $api_array[$api_field];
                            $db_table_not_required_insert = false;
                        }

                        unset($this->missing_fields[$api_field]);
                    }
                }

                if ($db_table_not_required_insert) {
                    unset($return_array[$table_name]);
                }
            }
        }

        return $return_array;
    }

    private function extend_data_content($mapped_array, $request_array, $ext_params = '') {
        if (tep_not_null($mapped_array)) {
            $mapped_array[TABLE_API_TM_TRANSACTION_IDENTIFIER]['create_datetime'] = 'now()';
            $mapped_array[TABLE_API_TM_TRANSACTION_IDENTIFIER]['request_result'] = 'success';

            if (isset($mapped_array['EXTENDED_VARIABLES_ARRAY'])) {
                $this->extended_variables_array = $mapped_array['EXTENDED_VARIABLES_ARRAY'];
                unset($mapped_array['EXTENDED_VARIABLES_ARRAY']);
            }

            $mapped_array[TABLE_API_TM_TRANSACTION_IDENTIFIER]['missing_field_bk'] = http_build_query($this->missing_fields, null, '&');
            $mapped_array[TABLE_API_TM_TRANSACTION_IDENTIFIER]['query_string'] = http_build_query($request_array, null, '&');
        }

        return $mapped_array;
    }

    // Duplicate the TM API return record for BO. Return format as {DB_ARRAY[BO_IDS][DB_TABLES][API PARAMETER] = API VALUE}
    private function duplicate_for_grouped_orders($extended_array) {
        $return_array = array();

        if (tep_not_null($extended_array)) {
            $return_array[$this->order_id] = $extended_array;

            if (isset($this->extended_variables_array['extended_order_ids'])) {
                $order_ids_array = explode(",", $this->extended_variables_array['extended_order_ids']);

                foreach ($order_ids_array as $order_id) {
                    $order_id = trim($order_id);
                    $extended_array[TABLE_API_TM_TRANSACTION_IDENTIFIER]['transaction_id'] = $order_id;
                    $return_array[$order_id] = $extended_array;
                }
            }
        }

        return $return_array;
    }

    // Insert proccessed API_ARRAY and save into db tables.

    private function insert_api_data_into_db($insert_arrays) {
        foreach ($insert_arrays as $order_id => $insert_array) {
            tep_db_perform(TABLE_API_TM_TRANSACTION_IDENTIFIER, $insert_array[TABLE_API_TM_TRANSACTION_IDENTIFIER]);
            $tm_query_ID = tep_db_insert_id();
            unset($insert_array[TABLE_API_TM_TRANSACTION_IDENTIFIER]); // Removed TABLE_API_TM_TRANSACTION_IDENTIFIER records from the array to prevent duplicate records.

            if (tep_not_null($insert_array)) {
                foreach ($insert_array as $table_name => $table_field) {
                    $table_field['api_tm_query_id'] = $tm_query_ID;
                    tep_db_perform($table_name, $table_field);
                }
            }
        }
    }

    public function set_order_id($order_id) {
        if (tep_not_null($order_id)) {
            $this->order_id = $order_id;
        }
    }

    public function set_order_ids($order_ids_array) {
        if (is_array($order_ids_array) && count($order_ids_array)) {
            $this->order_id = array_shift($order_ids_array);
            $this->extended_order_ids = implode(",", $order_ids_array);
        }
    }

    public function set_transaction_type($transaction_type) {
        if (tep_not_null($transaction_type)) {
            $this->transaction_type = strtoupper($transaction_type);
        }
    }

    private function get_unique_customers_by_device($device_id, $last_datetime = '', $minute_interval = '') {
        $device_id_shared_customers = array();
        $sql_extension = '';

        if (tep_not_null($last_datetime) && tep_not_null($minute_interval) && (int) $minute_interval < $this->max_duration) {
            $sql_extension = " 	AND create_datetime <= '" . $last_datetime . "'
                                AND create_datetime >= DATE_SUB('" . $last_datetime . "',INTERVAL " . $minute_interval . " MINUTE)";
        } else {
            $sql_extension = " 	AND create_datetime <= now()
                                AND create_datetime >= DATE_SUB(now(),INTERVAL 1 YEAR)";
        }

        // Customers who are using the SAME device ID.
        $transaction_select_sql = "SELECT DISTINCT customers_id
                                    FROM " . TABLE_API_TM_TRANSACTION_IDENTIFIER . "
                                    WHERE customers_id != ''
                                        AND device_id = '" . $device_id . "'" .
                $sql_extension;
        $transaction_result_sql = tep_db_query($transaction_select_sql);
        while ($transaction_row = tep_db_fetch_array($transaction_result_sql)) {
            $device_id_shared_customers[] = $transaction_row['customers_id'];
        }

        return $device_id_shared_customers;
    }

    public function get_db_tbl_transaction_id_array() {
        $return_array = array();

        if (!tep_not_null($this->order_id) || !tep_not_null($this->transaction_type)) {
            $this->report_error('[get_db_tbl_transaction_id_array] => order_id empty.');
        } else if (is_array($this->trans_id_table_array) && $this->trans_id_table_array['transaction_id'] == $this->order_id) {
            $return_array = $this->trans_id_table_array;
        } else {
            $transaction_select_sql = "	SELECT api_tm_query_id, transaction_id, customers_id, request_result, device_id, create_datetime
                                        FROM " . TABLE_API_TM_TRANSACTION_IDENTIFIER . "
                                        WHERE transaction_id = '" . $this->order_id . "'
                                            AND transaction_type = '" . $this->transaction_type . "'";
            $transaction_result_sql = tep_db_query($transaction_select_sql);
            if ($transaction_row = tep_db_fetch_array($transaction_result_sql)) {
                $return_array = $transaction_row;
            }
        }

        $this->trans_id_table_array = $return_array;

        return $return_array;
    }

    public function get_total_unique_customers_by_device($duration) {
        $return_int = 0;

        if ((int) $duration > $this->max_duration || !tep_not_null($duration)) {
            $this->report_error('[get_total_unique_customers_by_device] => Duration > ' . $this->max_duration . ' or 0 (max minute).');
        } else if (tep_not_null($this->get_db_tbl_transaction_id_array())) {
            if (isset($this->trans_id_table_array['device_id']) && $this->trans_id_table_array['device_id']) {
                $return_int = count($this->get_unique_customers_by_device($this->trans_id_table_array['device_id'], $this->trans_id_table_array['create_datetime'], $duration));
            }
        } else {
            $this->report_error('[get_total_unique_customers_by_device] => data not found.<br>tran obj: ' . http_build_query($this->trans_id_table_array, null, '&'));
        }

        return $return_int;
    }

    public function get_device_verification_result() {
        $return_string = '';

        if (tep_not_null($this->get_db_tbl_transaction_id_array())) {
            if ($this->trans_id_table_array['request_result'] == 'success') {
                $return_string = 'success';
            } else {
                $return_string = 'failed';
            }
        } else {
            $this->report_error('[get_device_verification_result] => Kount records not found using oID.<br>tran obj:<br>' . http_build_query($this->trans_id_table_array, null, '&'));
        }

        return $return_string;
    }

    //Normalized Score : 0 = Good, 99 = Bad
    //Kount : 0 = Good, 99 = Bad

    public function get_summary_score() {
        $return_string = '0';

        if (tep_not_null($this->get_db_tbl_transaction_id_array())) {
            $transaction_select_sql = "	SELECT api_tm_query_id, policy_score FROM " . TABLE_API_TM_RISK_SUMMARY_N_POLICY . "
                                        WHERE api_tm_query_id = '" . $this->trans_id_table_array['api_tm_query_id'] . "' ";
            $transaction_result_sql = tep_db_query($transaction_select_sql);
            if ($transaction_row = tep_db_fetch_array($transaction_result_sql)) {
                $return_string = $transaction_row['policy_score'];
            }
        } else {
            $this->report_error('[get_summary_score] => Kount records not found using oID.<br>tran obj:<br>' . http_build_query($this->trans_id_table_array, null, '&'));
        }

        return $return_string;
    }

    private function report_error($message, $sess = '') {
        $subject = 'Order ID: ' . $this->order_id;

        @tep_mail('OffGamers', '<EMAIL>', $subject, $message . (tep_not_null($sess) ? '<br>Session ID: ' . $sess : ''), STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
    }

}

?>