<?php

/*
  $Id: ogm_threat_metrix.php,v 1.24 2016/02/02 08:45:40 weesiong Exp $

  Developer: <PERSON>
  Copyright (c) 2006 SKC Venture

  Released under the GNU General Public License
 */

class ogm_threat_metrix {

    private $org_id = TM_ORG_ID;
    private $api_key = TM_API_Key;
    private $tm_params;               // TM API Query Params
    private $order_id, $extended_order_ids, $transaction_type;  // OGM Order Type [CO/BO]
    private $device_id;
    private $event_type = 'PAYMENT';
    private $session_type = 'session-policy';
    private $threat_metrix_switch = TM_SERVICE_ENABLED;
    private $extended_variables_array = array();
    private $missing_fields = array();
    private $query_url = "https://h-api.online-metrix.net/api/session-query";
    private $servic_mode = TM_SERVICE_MODE;
    private $trans_id_table_array = null;
    private $orders_table_array = null;
    private static $max_duration = 144000;  // 100 days
    // Declare the database tables fields used to map with the API response data. MAPPING LIKE [ API_FIELD => DB_FIELD ]
    private $db_tables = array(TABLE_API_TM_BROWSER => array('browser_language' => 'browser_language',
            'browser_string' => 'browser_string',
            'enabled_js' => 'enabled_js',
            'enabled_fl' => 'enabled_fl',
            'enabled_ck' => 'enabled_ck',
            'enabled_im' => 'enabled_im',
            'css_image_loaded' => 'css_image_loaded',
            'flash_version' => 'flash_version',
            'flash_lang' => 'flash_lang',
            'flash_os' => 'flash_os',
            'headers_name_value_hash' => 'headers_name_value_hash',
            'headers_order_string_hash' => 'headers_order_string_hash',
            'http_os_signature' => 'http_os_signature',
            'http_referer' => 'http_referer',
            'plugin_adobe_acrobat' => 'plugin_adobe_acrobat',
            'plugin_flash' => 'plugin_flash',
            'plugin_hash' => 'plugin_hash',
            'plugin_silverlight' => 'plugin_silverlight'
        ),
        TABLE_API_TM_DEVICE => array('device_id' => 'device_id',
            'device_result' => 'device_result',
            'os' => 'os',
            'screen_res' => 'screen_res',
            'local_time_offset' => 'local_time_offset',
            'local_time_offset_range' => 'local_time_offset_range',
            'time_zone' => 'time_zone',
            'device_score' => 'device_score',
            'device_attributes' => 'device_attributes',
            'device_activities' => 'device_activities',
            'device_assert_history' => 'device_assert_history',
            'device_last_update' => 'device_last_update',
            'device_worst_score' => 'device_worst_score',
            'profiling_datetime' => 'profiling_datetime',
            'device_first_seen' => 'device_first_seen',
            'device_last_event' => 'device_last_event',
            'device_match_result' => 'device_match_result',
            'offset_measure_time' => 'offset_measure_time',
            'os_anomaly' => 'os_anomaly',
            'os_fonts_hash' => 'os_fonts_hash',
            'os_fonts_number' => 'os_fonts_number'
        ),
        TABLE_API_TM_PROXY_IP => array('proxy_ip' => 'proxy_ip',
            'proxy_ip_score' => 'proxy_ip_score',
            'proxy_ip_attributes' => 'proxy_ip_attributes',
            'proxy_ip_activities' => 'proxy_ip_activities',
            'proxy_ip_assert_history' => 'proxy_ip_assert_history',
            'proxy_ip_last_update' => 'proxy_ip_last_update',
            'proxy_ip_worst_score' => 'proxy_ip_worst_score',
            'proxy_ip_city' => 'proxy_ip_city',
            'proxy_ip_geo' => 'proxy_ip_geo',
            'proxy_ip_isp' => 'proxy_ip_isp',
            'proxy_ip_latitude' => 'proxy_ip_latitude',
            'proxy_ip_longitude' => 'proxy_ip_longitude',
            'proxy_type' => 'proxy_type',
            'proxy_ip_first_seen' => 'proxy_ip_first_seen',
            'proxy_ip_last_event' => 'proxy_ip_last_event',
            'proxy_ip_longitude' => 'proxy_ip_longitude',
            'proxy_ip_organization' => 'proxy_ip_organization',
            'proxy_ip_region' => 'proxy_ip_region',
            'proxy_ip_result' => 'proxy_ip_result'
        ),
        TABLE_API_TM_RISK_SUMMARY_N_POLICY => array('summary_risk_score' => 'summary_risk_score',
            'policy_score' => 'policy_score',
            'reason_code' => 'reason_code'
        ),
        TABLE_API_TM_TRUE_IP => array('true_ip' => 'true_ip',
            'true_ip_activities' => 'true_ip_activities',
            'true_ip_attributes' => 'true_ip_attributes',
            'true_ip_city' => 'true_ip_city',
            'true_ip_geo' => 'true_ip_geo',
            'true_ip_isp' => 'true_ip_isp',
            'true_ip_last_update' => 'true_ip_last_update',
            'true_ip_latitude' => 'true_ip_latitude',
            'true_ip_longitude' => 'true_ip_longitude',
            'true_ip_worst_score' => 'true_ip_worst_score',
            'true_ip_score' => 'true_ip_score',
            'true_ip_first_seen' => 'true_ip_first_seen',
            'true_ip_last_event' => 'true_ip_last_event',
            'true_ip_longitude' => 'true_ip_longitude',
            'true_ip_organization' => 'true_ip_organization',
            'true_ip_region' => 'true_ip_region',
            'true_ip_result' => 'true_ip_result'
        ),
        TABLE_API_TM_TRANSACTION_IDENTIFIER => array('transaction_id' => 'transaction_id',
            'request_result' => 'request_result',
            'request_id' => 'request_id',
            'device_id' => 'device_id',
            'local_attrib_1' => 'customers_id',
            'local_attrib_2' => 'transaction_type'
        ),
        TABLE_API_TM_FUZZY_DEVICE => array('fuzzy_device_id' => 'fuzzy_device_id',
            'fuzzy_device_first_seen' => 'fuzzy_device_first_seen',
            'fuzzy_device_id_confidence' => 'fuzzy_device_id_confidence',
            'fuzzy_device_last_event' => 'fuzzy_device_last_event',
            'fuzzy_device_last_update' => 'fuzzy_device_last_update',
            'fuzzy_device_match_result' => 'fuzzy_device_match_result',
            'fuzzy_device_result' => 'fuzzy_device_result',
            'fuzzy_device_score' => 'fuzzy_device_score',
            'fuzzy_device_worst_score' => 'fuzzy_device_worst_score'
        ),
        'NOT_REQUIRED' => array('org_id' => 'org_id',
            'service_type' => 'service_type',
            'session_id' => 'session_id',
            'event_type' => 'event_type',
        ),
        'EXTENDED_VARIABLES_ARRAY' => array('local_attrib_3' => 'extended_order_ids')
    );

    public function __construct($order_id = '', $order_type = 'CO') {
        if ($this->threat_metrix_switch == 'true') {
            $this->threat_metrix_switch = 1; // Turn ON
        } else {
            $this->threat_metrix_switch = 0; // Turn OFF
        }

        $this->set_order_id($order_id);
        $this->set_transaction_type($order_type);
    }

    public function execute_stored_query_call($orders_id, $customer_id = 0) {
        if ($this->threat_metrix_switch) {
            $this->set_order_id($orders_id);
            $pass_params = array();

            $get_query_select_sql = "SELECT transaction_type, extra_info FROM " . TABLE_API_KOUNT_QUERY_QUEUE . " WHERE orders_id = '" . $this->order_id . "'";
            $get_query_result_sql = tep_db_query($get_query_select_sql);
            if ($get_query_row = tep_db_fetch_array($get_query_result_sql)) {
                $extra_info_array = json_decode($get_query_row['extra_info'], true);

                $pass_params = array(
                    'orders_id' => $this->order_id,
                    'transaction_type' => $get_query_row['transaction_type'],
                    'TM_session_id_created_timestamp' => $extra_info_array['timestamp'],
                    'session_id' => $extra_info_array['session_id'],
                    'customers_id' => $extra_info_array['customer_id'],
                    'customers_login_ip' => $extra_info_array['customers_login_ip'],
                    'customers_login_timestamp' => $extra_info_array['timestamp'],
                    'customers_city' => $extra_info_array['customer_info']['city'],
                    'customers_country' => $extra_info_array['customer_info']['country_ISO'],
                );

                if ($this->execution_rules($customer_id)) {
                    $this->execute_query_call($pass_params);
                }

                $delete_expired_records_sql = "	DELETE FROM " . TABLE_API_KOUNT_QUERY_QUEUE . " 
                                                                                            WHERE created_datetime < DATE_SUB(NOW(),INTERVAL 1 DAY)";
                //tep_db_query($delete_expired_records_sql);

                $delete_order_record_sql = "	DELETE FROM " . TABLE_API_KOUNT_QUERY_QUEUE . " 
                                                                                            WHERE orders_id = '" . $this->order_id . "'";
                //tep_db_query($delete_order_record_sql);

                unset($extra_info_array, $pass_params);
            }
        }
    }

    private function execute_query_call($ext_params = '') {
        if (!tep_not_null($ext_params)) {
            return;
        }
        $session_timestamp = $ext_params['TM_session_id_created_timestamp'];
        $this->tm_params = http_build_query($this->generate_post_params($ext_params), null, '&');

        // Checking purpose
        $checking_str = '<br>CREATE DT: ' . date("d/m/y : H:i:s", $session_timestamp)
                . '<br>Call DT: ' . date("d/m/y : H:i:s")
                . '<br>Total Time Spend In Seconds: ' . (time() - $session_timestamp)
                . '<br><br>URL: ' . $this->query_url
                . '<br><br>Params: ' . $this->tm_params;

        $options = array(CURLOPT_URL => $this->query_url,
            CURLOPT_VERBOSE => TRUE,
            CURLOPT_SSL_VERIFYPEER => FALSE,
            CURLOPT_RETURNTRANSFER => TRUE,
            CURLOPT_FOLLOWLOCATION => TRUE,
            CURLOPT_SSL_VERIFYHOST => 1,
            CURLOPT_TIMEOUT => 5,
            CURLOPT_POST => 1,
            CURLOPT_POSTFIELDS => $this->tm_params,
            CURLOPT_USERAGENT => "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.0)"
        );
        if (WWW_USE_PROXY_SERVER == 'true') {
            $options[CURLOPT_PROXY] = 'http://my-proxy.offgamers.lan:3128';
        }

        try {
            $ch = curl_init();
            curl_setopt_array($ch, $options);
            $response = curl_exec($ch);
            if ($response !== false) {
                $api_array = $this->decode_string_to_array($response);
                $this->device_id = isset($api_array['device_id']) ? $api_array['device_id'] : '';
                $mapped_array = $this->map_db_fields_with_api_return_fields($api_array);
                $content_extended_array = $this->extend_data_content($mapped_array, $ext_params);
                $db_insert_array = $this->duplicate_for_grouped_orders($content_extended_array);
                $this->insert_api_data_into_db($db_insert_array);
                unset($api_array, $mapped_array, $content_extended_array, $db_insert_array);
            } else {
                $this->report_error('CURL no response. ' . $checking_str);
            }
        } catch (Exception $e) {
            $this->report_error('CURL Error Found: ' . $e . '<br>' . $checking_str);
        }

        curl_close($ch);
    }

    private function generate_post_params($ext_params) {
        $return_array['org_id'] = $this->org_id;
        $return_array['api_key'] = $this->api_key;
        $return_array['service_type'] = $this->session_type;
        $return_array['event_type'] = $this->event_type;

        $return_array['transaction_id'] = $ext_params['orders_id'];
        $return_array['session_id'] = $ext_params['session_id'];
        $return_array['account_address_city'] = $ext_params['customers_city'];
        $return_array['account_address_country'] = $ext_params['customers_country'];
        $return_array['local_attrib_1'] = $ext_params['customers_id'];
        $return_array['local_attrib_2'] = $ext_params['transaction_type'];

        if (tep_not_null($this->extended_order_ids)) {
            $return_array['local_attrib_3'] = $this->extended_order_ids;
        }

        if ($this->servic_mode == 'Live') {
            $return_array['local_attrib_5'] = 'live';
        }

        // http_build_query will encode all the params
        return $return_array;
    }

    private function execution_rules($customers_id) {
        $fire_list = array(1, 2, 12);
        $return_bool = TRUE;

        if ($customers_id) {
            $customers_groups_id_select_sql = "	SELECT customers_aft_groups_id
                                                FROM " . TABLE_CUSTOMERS . "
                                                WHERE customers_id = '" . $customers_id . "'";
            $customers_groups_id_result_sql = tep_db_query($customers_groups_id_select_sql);
            if ($customers_groups_id_row = tep_db_fetch_array($customers_groups_id_result_sql)) {
                if (!in_array((int) $customers_groups_id_row['customers_aft_groups_id'], $fire_list)) {
                    $return_bool = FALSE;
                }
            }
        }

        return $return_bool;
    }

    // decode TM's API returns string and convert into API_ARRAY with format {API_ARRAY[API PARAMETER] = API VALUE}
    private function decode_string_to_array($api_string) {
        if (!tep_not_null($api_string))
            return '';

        $return_array = array();

        $encoded_string = urldecode($api_string);
        $encoded_array = explode("&", $encoded_string);

        foreach ($encoded_array AS $pnv) {
            list($p, $v) = explode("=", $pnv, 2);
            $return_array[str_replace(" ", "", $p)] = $v; // remove space in between parameter and save into return_array.
        }

        return $return_array;
    }

    // DB table fields Mapping and return as format {MAPPED_ARRAY[DB_TABLES][DB PARAMETER] = API VALUE}
    private function map_db_fields_with_api_return_fields($api_array) {
        $return_array = array();
        $this->missing_fields = $api_array;

        if (tep_not_null($api_array)) {
            foreach ($this->db_tables AS $table_name => $table_fields_array) {
                $db_table_not_required_insert = true;

                foreach ($table_fields_array AS $api_field => $db_field) {
                    if (isset($api_array[$api_field])) {
                        $return_array[$table_name][$db_field] = $api_array[$api_field];
                        $db_table_not_required_insert = false;
                        unset($this->missing_fields[$api_field]);
                    } else {
                        $return_array[$table_name][$db_field] = '';
                    }
                }

                if ($db_table_not_required_insert) {
                    unset($return_array[$table_name]);
                }
            }

            if (isset($return_array['EXTENDED_VARIABLES_ARRAY'])) {
                $this->extended_variables_array = $return_array['EXTENDED_VARIABLES_ARRAY'];
                unset($return_array['EXTENDED_VARIABLES_ARRAY']);
            }

            unset($return_array['NOT_REQUIRED'], $api_array);
        }

        return $return_array;
    }

    private function extend_data_content($mapped_array, $ext_params = '') {
        $return_array = array();
        if (tep_not_null($mapped_array)) {
            $return_array = $mapped_array;

            $return_array[TABLE_API_TM_TRANSACTION_IDENTIFIER]['create_datetime'] = 'now()';
            $return_array[TABLE_API_TM_TRANSACTION_IDENTIFIER]['query_string'] = $this->query_url . '?' . $this->tm_params;

            if (tep_not_null($ext_params)) {
                $return_array[TABLE_API_TM_TRANSACTION_IDENTIFIER]['customers_login_ip'] = $ext_params['customers_login_ip'];
                $return_array[TABLE_API_TM_TRANSACTION_IDENTIFIER]['customers_login_date'] = date("Y-m-d : H:i:s", $ext_params['customers_login_timestamp']);
            }

            if (tep_not_null($this->missing_fields)) {
                // TM 2.1 has changed unknown Session Handling - We make it remain unchange by overwrite the request result to previous value
                if (isset($this->missing_fields['unknown_session'])) {
                    if (strtolower($this->missing_fields['unknown_session']) == 'yes') {
                        $return_array[TABLE_API_TM_TRANSACTION_IDENTIFIER]['request_result'] = 'fail_unknown_session';
                    }
                }

                $return_array[TABLE_API_TM_TRANSACTION_IDENTIFIER]['missing_field_bk'] = http_build_query($this->missing_fields, null, '&');
            }

            unset($mapped_array);
        }
        return $return_array;
    }

    // Duplicate the TM API return record for BO. Return format as {DB_ARRAY[BO_IDS][DB_TABLES][API PARAMETER] = API VALUE}
    private function duplicate_for_grouped_orders($extended_array) {
        $return_array = array();
        $order_ids_array = array();

        if (tep_not_null($extended_array)) {
            $return_array[$this->order_id] = $extended_array;

            if (isset($this->extended_variables_array['extended_order_ids'])) {
                $order_ids_array = explode(",", $this->extended_variables_array['extended_order_ids']);
                foreach ($order_ids_array as $order_id) {
                    $order_id = trim($order_id);

                    $extended_array[TABLE_API_TM_TRANSACTION_IDENTIFIER]['transaction_id'] = $order_id;
                    $return_array[$order_id] = $extended_array;
                }
            }
        }

        return $return_array;
    }

    // Insert proccessed API_ARRAY and save into db tables.
    private function insert_api_data_into_db($insert_arrays) {
        foreach ($insert_arrays as $order_id => $insert_array) {
            $tm_query_ID = 0;

            tep_db_perform(TABLE_API_TM_TRANSACTION_IDENTIFIER, $insert_array[TABLE_API_TM_TRANSACTION_IDENTIFIER]);
            $tm_query_ID = tep_db_insert_id();

            unset($insert_array[TABLE_API_TM_TRANSACTION_IDENTIFIER]); // Removed TABLE_API_TM_TRANSACTION_IDENTIFIER records from the array to prevent duplicate records.

            if (tep_not_null($insert_array)) {
                foreach ($insert_array as $table_name => $table_field) {
                    $temp_query_array = $table_field;
                    $temp_query_array['api_tm_query_id'] = $tm_query_ID;
                    tep_db_perform($table_name, $temp_query_array);

                    unset($temp_query_array);
                }
            }
        }
    }

    public function set_session_type($session_type) {
        $this->session_type = $session_type;
    }

    public function set_event_type($event_type) {
        $this->event_type = $event_type;
    }

    public function set_order_id($order_id) {
        if (tep_not_null($order_id)) {
            $this->order_id = $order_id;
        }
    }

    public function set_order_ids($order_ids_array) {
        if (is_array($order_ids_array) && count($order_ids_array)) {
            $this->order_id = array_shift($order_ids_array);
            $this->extended_order_ids = implode(",", $order_ids_array);
        }
    }

    public function set_transaction_type($transaction_type) {
        if (tep_not_null($transaction_type)) {
            $this->transaction_type = strtoupper($transaction_type);
        }
    }

    private function get_unique_customers_by_device($device_id, $last_datetime = '', $minute_interval = '') {
        $device_id_shared_customers = array();
        $sql_extension = '';

        if (tep_not_null($last_datetime) && tep_not_null($minute_interval) && (int) $minute_interval < $this->max_duration) {
            $sql_extension = " 	AND create_datetime <= '" . $last_datetime . "'
								AND create_datetime >= DATE_SUB('" . $last_datetime . "',INTERVAL " . $minute_interval . " MINUTE)";
        } else {
            $sql_extension = " 	AND create_datetime <= now()
								AND create_datetime >= DATE_SUB(now(),INTERVAL 1 YEAR)";
        }

        // Customers who are using the SAME device ID.
        $transaction_select_sql = "	SELECT DISTINCT customers_id
									FROM " . TABLE_API_TM_TRANSACTION_IDENTIFIER . "
									WHERE customers_id != ''
										AND device_id = '" . $device_id . "'" .
                $sql_extension;
        $transaction_result_sql = tep_db_query($transaction_select_sql);
        while ($transaction_row = tep_db_fetch_array($transaction_result_sql)) {
            $device_id_shared_customers[] = $transaction_row['customers_id'];
        }

        return $device_id_shared_customers;
    }

    public function get_db_tbl_transaction_id_array() {
        $return_array = array();

        if (!tep_not_null($this->order_id) || !tep_not_null($this->transaction_type)) {
            $this->report_error('[get_db_tbl_transaction_id_array] => order_id empty.');
        } else if (is_array($this->trans_id_table_array) && $this->trans_id_table_array['transaction_id'] == $this->order_id) {
            $return_array = $this->trans_id_table_array;
        } else {
            $transaction_select_sql = "	SELECT api_tm_query_id, transaction_id, customers_id, request_result, device_id, create_datetime
										FROM " . TABLE_API_TM_TRANSACTION_IDENTIFIER . "
										WHERE transaction_id = '" . $this->order_id . "'
											AND transaction_type = '" . $this->transaction_type . "'";
            $transaction_result_sql = tep_db_query($transaction_select_sql);
            if ($transaction_row = tep_db_fetch_array($transaction_result_sql)) {
                $return_array = $transaction_row;
            }
        }

        $this->trans_id_table_array = $return_array;

        return $return_array;
    }

    public function get_total_unique_customers_by_device($duration) {
        $return_int = 0;

        if ((int) $duration > $this->max_duration || !tep_not_null($duration)) {
            $this->report_error('[get_total_unique_customers_by_device] => Duration > ' . $this->max_duration . ' or 0 (max minute).');
        } else if (tep_not_null($this->get_db_tbl_transaction_id_array())) {
            if (isset($this->trans_id_table_array['device_id']) && $this->trans_id_table_array['device_id']) {
                $return_int = count($this->get_unique_customers_by_device($this->trans_id_table_array['device_id'], $this->trans_id_table_array['create_datetime'], $duration));
            }
        } else {
            $this->report_error('[get_total_unique_customers_by_device] => data not found.<br>tran obj: ' . http_build_query($this->trans_id_table_array, null, '&'));
        }

        return $return_int;
    }

    public function get_device_verification_result() {
        $return_string = '';

        if (tep_not_null($this->get_db_tbl_transaction_id_array())) {
            if ($this->trans_id_table_array['request_result'] == 'success') {
                $return_string = 'success';
            } else {
                $return_string = 'failed';
            }
        } else {
            $this->report_error('[get_device_verification_result] => TM records not found using oID.<br>tran obj:<br>' . http_build_query($this->trans_id_table_array, null, '&'));
        }

        return $return_string;
    }

    //Normalized Score : 0 = Good, 99 = Bad
    //TM : -100 = Bad, 100 = Good
    public function get_summary_score() {
        $return_string = '0';

        if (tep_not_null($this->get_db_tbl_transaction_id_array())) {
            $transaction_select_sql = "	SELECT api_tm_query_id, policy_score FROM " . TABLE_API_TM_RISK_SUMMARY_N_POLICY . "
                                            WHERE api_tm_query_id = '" . $this->trans_id_table_array['api_tm_query_id'] . "' ";
            $transaction_result_sql = tep_db_query($transaction_select_sql);
            if ($transaction_row = tep_db_fetch_array($transaction_result_sql)) {
                $return_string = abs(round(($transaction_row['policy_score'] - 100) * -0.5));
            }
        } else {
            $this->report_error('[get_summary_score] => TM records not found using oID.<br>tran obj:<br>' . http_build_query($this->trans_id_table_array, null, '&'));
        }

        return $return_string;
    }

    private function report_error($message, $sess = '') {
        $subject = '[OFFGAMERS] Order ID: ' . $this->order_id;

        @tep_mail('OffGamers', '<EMAIL>', $subject, $message . (tep_not_null($sess) ? '<br>Session ID: ' . $sess : ''), STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
    }

}

?>