<?php
/*
  	$Id: order.php,v 1.86 2015/05/28 10:51:32 darren.ng Exp $

  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com

  	Copyright (c) 2003 osCommerce

  	Released under the GNU General Public License
*/

include_once(DIR_WS_FUNCTIONS . 'customers_info_verification.php');
require_once(DIR_WS_CLASSES . 'anti_fraud.php');

class order
{
	var $info, $totals, $products, $customer, $billing, $delivery, $content_type;
	var $order_id;

    function order($order_id = '', $is_sc = false, $is_type = '')
    {
    	$this->info = array();
      	$this->totals = array();
      	$this->products = array();
      	$this->customer = array();
      	$this->delivery = array();
      	$this->billing = array();

      	if (tep_not_null($order_id)) {
        	$this->query($order_id);
        	$this->order_id = $order_id;
        } else if ($is_type == 'gift_card') {
      	} else {
        	$this->cart($is_sc);
      	}
	}

	function get_cdkey_identifier($products_id, $orders_products_id)
	{
		$cdkey_identifier_arr = array();
		$ids_str = '';
		$sql = "SELECT orders_custom_products_value
				FROM " . TABLE_ORDERS_CUSTOM_PRODUCTS . "
				WHERE products_id = '" . tep_db_input($products_id) . "'
					AND orders_products_id = '" . tep_db_input($orders_products_id) . "'
					AND orders_custom_products_key = 'cd_key_id'";
		$result = tep_db_query($sql);
		$ids_arr = array();

		while ($row = tep_db_fetch_array($result)) {
			$ids_arr = array_merge($ids_arr, explode(",", $row['orders_custom_products_value']));
		}

		if (count($ids_arr)) {
			$assigned_cd_key_str = implode("', '", $ids_arr);

			if (tep_not_null($assigned_cd_key_str)) {
				$sql = "SELECT custom_products_code_id, file_name, file_type
						FROM custom_products_code
						WHERE custom_products_code_id IN ('".$assigned_cd_key_str."')
						ORDER BY code_date_added asc, file_name asc";
				$result = tep_db_query($sql);
				while ($row = tep_db_fetch_array($result)) {
					$cdkey_identifier_arr[$row['custom_products_code_id']] = array(	'key_identifier' => $row['custom_products_code_id'],
																					'file_name' => $products_id.'_'.$row['custom_products_code_id'],
																					'file_type' => tep_not_null($row['file_type']) ? $row['file_type'] : 'jpg'
																					);
				}
			}
		}
		return $cdkey_identifier_arr;
	}

	function get_custom_products_type_name($custom_products_type_id)
	{
		$label = '';
		$sql = "SELECT custom_products_type_name FROM " .TABLE_CUSTOM_PRODUCTS_TYPE. " WHERE custom_products_type_id=$custom_products_type_id";
		$result = tep_db_query($sql);
		while($row = tep_db_fetch_array($result))
		{
			//"Power Leveling" => "power_leveling"
			$name = strtolower(str_replace(' ', '_', $row['custom_products_type_name']));
		}
		return $name;
	}

    function query($order_id)
    {
    	global $languages_id, $default_languages_id;

      	$order_id = tep_db_prepare_input($order_id);

		$order_query = tep_db_query("SELECT o.*, coun.countries_international_dialing_code, coun.countries_name FROM " . TABLE_ORDERS . " AS o LEFT JOIN " . TABLE_COUNTRIES . " AS coun ON (coun.countries_name = o.customers_country) WHERE o.orders_id = '" . (int)$order_id . "'");
      	$order = tep_db_fetch_array($order_query);

      	$totals_query = tep_db_query("select title, text, class, value from " . TABLE_ORDERS_TOTAL . " where orders_id = '" . (int)$order_id . "' order by sort_order");
      	while ($totals = tep_db_fetch_array($totals_query)) {
        	$this->totals[] = array('title' => $totals['title'],
                                	'text' => $totals['text'],
                                	'value' => $totals['value'],
                                	'class' => $totals['class']
                                	);

			if ($totals['class'] == 'ot_total') {
				$order_total = $totals;
			} else if ($totals['class'] == 'ot_shipping') {
				$shipping_method = $totals;
			}
      	}

     	// begin PayPal_Shopping_Cart_IPN
//     	$order_total_query = tep_db_query("select text, value from " . TABLE_ORDERS_TOTAL . " where orders_id = '" . (int)$order_id . "' and class = 'ot_total'");
     	// end PayPal_Shopping_Cart_IPN
//     	$order_total = tep_db_fetch_array($order_total_query);

     	//begin PayPal_Shopping_Cart_IPN
     	//$shipping_method_query = tep_db_query("select title, value from " . TABLE_ORDERS_TOTAL . " where orders_id = '" . (int)$order_id . "' and class = 'ot_shipping'");
	 	//end PayPal_Shopping_Cart_IPN
      	//$shipping_method = tep_db_fetch_array($shipping_method_query);

		$order_status_select_sql = "select orders_status_name
									from " . TABLE_ORDERS_STATUS . "
									where orders_status_id = '" . $order['orders_status'] . "'
										AND (IF (language_id = '" . (int)$languages_id . "',
													1,
													IF((SELECT COUNT(orders_status_id) > 0
														FROM " . TABLE_ORDERS_STATUS . "
														WHERE orders_status_id = '" . $order['orders_status'] . "'
															AND language_id = '" . (int)$languages_id . "'), 0, language_id = '" . (int)$default_languages_id . "')))";
      	$order_status_result_sql = tep_db_query($order_status_select_sql);
      	$order_status_row = tep_db_fetch_array($order_status_result_sql);

       	$this->info = array('currency' => $order['currency'],
				          	'currency_value' => $order['currency_value'],
				          	'payment_method' => $order['payment_method'],
				          	'payment_methods_id' => $order['payment_methods_id'],
				          	'payment_methods_parent_id' => $order['payment_methods_parent_id'],
				          	'cc_type' => $order['cc_type'],
				          	'cc_owner' => $order['cc_owner'],
				          	'cc_number' => $order['cc_number'],
				          	'cc_expires' => $order['cc_expires'],
				          	'date_purchased' => $order['date_purchased'],
				          	'pm_2CO_cc_owner_firstname' => $order['pm_2CO_cc_owner_firstname'],
				          	'pm_2CO_cc_owner_lastname' => $order['pm_2CO_cc_owner_lastname'],
				     		//begin PayPal_Shopping_Cart_IPN
				          	'orders_status_id' => $order['orders_status'],
				          	'shipping_cost' => $shipping_method['value'],
				          	'total_value' => $order_total['value'],
				     		//end PayPal_Shopping_Cart_IPN
				     		'orders_status_id' => $order['orders_status'],
				          	'orders_status' => $order_status_row['orders_status_name'],
				          	'paypal_ipn_id' => $order['paypal_ipn_id'],
				          	'remote_addr' => $order['remote_addr'],
				          	'last_modified' => $order['last_modified'],
                          	'total' => strip_tags($order_total['text']),
                          	'shipping_method' => ((substr($shipping_method['title'], -1) == ':') ? substr(strip_tags($shipping_method['title']), 0, -1) : strip_tags($shipping_method['title'])),
                          	'orders_aft_executed' => $order['orders_aft_executed']
                          	);

      	$this->customer = array('id' => $order['customers_id'],
                              	'name' => $order['customers_name'],
                              	'company' => $order['customers_company'],
                              	'street_address' => $order['customers_street_address'],
                              	'suburb' => $order['customers_suburb'],
                              	'city' => $order['customers_city'],
                              	'postcode' => $order['customers_postcode'],
                              	'state' => $order['customers_state'],
                              	'country' => $order['customers_country'],
                              	'telephone_country' => (tep_not_null($order['customers_telephone_country']) ? $order['customers_telephone_country'] : $order['countries_name']),
                              	'order_country_code' => (tep_not_null($order['customers_country_international_dialing_code']) ? $order['customers_country_international_dialing_code'] : $order['countries_international_dialing_code']),
                              	'format_id' => $order['customers_address_format_id'],
                              	'telephone' => $order['customers_telephone'],
                              	'email_address' => $order['customers_email_address'],
                              	'customers_groups_id' => $order['customers_groups_id']);

      	$this->delivery = array('name' => $order['delivery_name'],
                              	'company' => $order['delivery_company'],
                              	'street_address' => $order['delivery_street_address'],
                              	'suburb' => $order['delivery_suburb'],
                              	'city' => $order['delivery_city'],
                              	'postcode' => $order['delivery_postcode'],
                              	'state' => $order['delivery_state'],
                              	'country' => $order['delivery_country'],
                              	'format_id' => $order['delivery_address_format_id']);

      	if (empty($this->delivery['name']) && empty($this->delivery['street_address'])) {
        	$this->delivery = false;
      	}

      	$this->billing = array(	'name' => $order['billing_name'],
                             	'company' => $order['billing_company'],
                             	'street_address' => $order['billing_street_address'],
                             	'suburb' => $order['billing_suburb'],
                             	'city' => $order['billing_city'],
                             	'postcode' => $order['billing_postcode'],
                             	'state' => $order['billing_state'],
                             	'country' => $order['billing_country'],
                             	'format_id' => $order['billing_address_format_id']);

		// TODO: Split actual purchase and compensated
      	$index = 0;
     	$orders_products_select_sql = "	SELECT orders_products_id, custom_products_type_id, products_id, products_name, products_model, products_price, products_tax, products_quantity, products_delivered_quantity, final_price, products_pre_order, products_categories_id, orders_products_is_compensate
      									FROM " . TABLE_ORDERS_PRODUCTS . "
      									WHERE orders_id = '" . (int)$order_id . "'
      										AND products_bundle_id = 0";
      	$orders_products_query = tep_db_query($orders_products_select_sql);
      	while ($orders_products = tep_db_fetch_array($orders_products_query)) {
      		$this->products[$index] = array('qty' => $orders_products['products_quantity'],
      										'delivered_qty' => $orders_products['products_delivered_quantity'],
								          	'id' => $orders_products['products_id'],
											//begin PayPal_Shopping_Cart_IPN
								          	'orders_products_id' => $orders_products['orders_products_id'],
											//end PayPal_Shopping_Cart_IPN
											'custom_products_type_id' => $orders_products['custom_products_type_id'],
								          	'name' => $orders_products['products_name'],
								          	'model' => $orders_products['products_model'],
								          	'tax' => $orders_products['products_tax'],
								          	'price' => $orders_products['products_price'],
								          	'final_price' => $orders_products['final_price'],
								          	'pre_order' => $orders_products['products_pre_order'],
								          	'products_categories_id' => $orders_products['products_categories_id'],
								          	'is_compensate' => $orders_products['orders_products_is_compensate']
								          	);
			if ($orders_products['custom_products_type_id'] == '2') {
				$delivery_mode_sql = "	SELECT orders_products_extra_info_value
										FROM " . TABLE_ORDERS_PRODUCTS_EXTRA_INFO . "
										WHERE orders_products_id = '".$orders_products['orders_products_id']."'
											AND orders_products_extra_info_key = 'delivery_mode'";
				$delivery_mode_result = tep_db_query($delivery_mode_sql);
				if ($delivery_mode_row = tep_db_fetch_array($delivery_mode_result)) {
					$this->products[$index]['custom_content']['delivery_mode'] = $delivery_mode_row['orders_products_extra_info_value'];
				}
			}

			if ($cdkey_info = $this->get_cdkey_identifier($orders_products['products_id'], $orders_products['orders_products_id']))
				$this->products[$index]['cdkey_info'] = $cdkey_info;

			$subproduct_select_sql = "	SELECT orders_products_id, products_id, products_name, products_model, products_price, products_tax, products_quantity, final_price, products_categories_id, products_delivered_quantity, products_categories_id, orders_products_purchase_eta,
											products_good_delivered_quantity, products_canceled_quantity, products_reversed_quantity,
											IF(	orders_products_purchase_eta IS NULL or orders_products_purchase_eta=-999,
												NULL,
												(TO_DAYS(DATE_ADD('".$order['date_purchased']."', INTERVAL orders_products_purchase_eta HOUR))*24*3600 + TIME_TO_SEC(DATE_ADD('".$order['date_purchased']."', INTERVAL orders_products_purchase_eta HOUR)) - (TO_DAYS(now())*24*3600 + TIME_TO_SEC(now()))) / 3600
											) AS purchase_eta
										FROM " . TABLE_ORDERS_PRODUCTS . "
										WHERE orders_id = '" . (int)$order_id . "'
											AND parent_orders_products_id = '" . (int)$orders_products['orders_products_id'] . "'";
			$subproduct_result_sql = tep_db_query($subproduct_select_sql);
			$sub_index = 0;
			while ($subproduct_info = tep_db_fetch_array($subproduct_result_sql)) {

				$custom_content_array = array();
				$delivery_mode_sql = "	SELECT orders_products_extra_info_value
										FROM " . TABLE_ORDERS_PRODUCTS_EXTRA_INFO . "
										WHERE orders_products_id = '".$subproduct_info['orders_products_id']."'
											AND orders_products_extra_info_key = 'delivery_mode'";
				$delivery_mode_result = tep_db_query($delivery_mode_sql);
				if ($delivery_mode_row = tep_db_fetch_array($delivery_mode_result)) {
	      			$custom_content_array['delivery_mode'] = $delivery_mode_row['orders_products_extra_info_value'];
	      			$this->products[$index]['custom_content']['delivery_mode'] = $delivery_mode_row['orders_products_extra_info_value'];
				}

				$this->products[$index]['package'][$sub_index] = array(	'qty' => $subproduct_info['products_quantity'],
															          	'id' => $subproduct_info['products_id'],
															          	'orders_products_id' => $subproduct_info['orders_products_id'],
															          	'name' => $subproduct_info['products_name'],
															          	'model' => $subproduct_info['products_model'],
															          	'tax' => $subproduct_info['products_tax'],
															          	'price' => $subproduct_info['products_price'],
															          	'final_price' => $subproduct_info['final_price'],
															          	'products_categories_id' => $subproduct_info['products_categories_id'],
															          	'delivered_qty' => $subproduct_info['products_delivered_quantity'],
	                                        							'purchase_eta' => $subproduct_info['purchase_eta'],
	                                        							'org_purchase_eta' => $subproduct_info['orders_products_purchase_eta'],
	                                        							'custom_content' => $custom_content_array,
															          	);
				$this->products[$index]['package'][$sub_index]['qty_info'] = array(	'delivered_quantity' => $subproduct_info['products_good_delivered_quantity'],
																					'canceled_quantity' => $subproduct_info['products_canceled_quantity'],
																					'reversed_quantity' => $subproduct_info['products_reversed_quantity']
																					);
				if ($cdkey_info = $this->get_cdkey_identifier($subproduct_info['products_id'], $subproduct_info['orders_products_id']))
				$this->products[$index]['package'][$sub_index]['cdkey_info'] = $cdkey_info;
				$sub_index++;
			}

			// ********** Begin Separate Price per Customer Mod **************
//			global $customer_id;
//			$customer_group_id_query = tep_db_query("select customers_group_id from " . TABLE_CUSTOMERS . " where customers_id = '". $customer_id . "'");
//			$customer_group_id = tep_db_fetch_array($customer_group_id_query);
//  			if ($customer_group_id['customers_group_id'] != '0'){
//  				$orders_customers_price = tep_db_query("select customers_group_price from " . TABLE_PRODUCTS_GROUPS . " where customers_group_id = '". $customer_group_id['customers_group_id'] . "' and products_id = '" . $products[$index]['id'] . "'");
//  				if ($orders_customers = tep_db_fetch_array($orders_customers_price)){
//    				$this->products[$index] = array('price' => $orders_customers['customers_group_price'],
//                                    				'final_price' => $orders_customers['customers_group_price']);
//  				}
//			}
			// ********** End Separate Price per Customer Mod **************
        	$subindex = 0;
        	//begin PayPal_Shopping_Cart_IPN
			$attributes_query = tep_db_query("select products_options_id, products_options_values_id, products_options, products_options_values, options_values_price, price_prefix from " . TABLE_ORDERS_PRODUCTS_ATTRIBUTES . " where orders_id = '" . (int)$order_id . "' and orders_products_id = '" . (int)$orders_products['orders_products_id'] . "'");
			//end PayPal_Shopping_Cart_IPN

        	if (tep_db_num_rows($attributes_query)) {
          		while ($attributes = tep_db_fetch_array($attributes_query)) {
             		$this->products[$index]['attributes'][$subindex] = array(	//begin PayPal_Shopping_Cart_IPN
														          				'option_id' => $attributes['products_options_id'],
														          				'value_id' => $attributes['products_options_values_id'],
																				//end PayPal_Shopping_Cart_IPN
														          				'option' => $attributes['products_options'],
														          				'value' => $attributes['products_options_values'],
														          				'prefix' => $attributes['price_prefix'],
														          				'price' => $attributes['options_values_price']);
					$subindex++;
          		}
        	}
        	$this->info['tax_groups']["{$this->products[$index]['tax']}"] = '1';

        	$index++;
      	}
	}

	function format_character_name($matches) {
		// as usual: $matches[0] is the complete match
		// $matches[1] the match for the first subpattern
		// enclosed in '(...)' and so on

		$formated_string = preg_replace('/([^A-Z]+)/s', "<span style='color:#0A246A;'>\\1</span>", preg_quote($matches[1], "/"));
		$formated_string = tep_db_prepare_input($formated_string);

		return "<span style='color:#019858;'>".$formated_string."</span>";
	}

	function get_product_order_info() {
		$total_ordered_product = count($this->products);
		$styling_keys_array = array('char_name', 'char_account_name', 'char_account_pwd');

		for ($prod_cnt=0; $prod_cnt < $total_ordered_product; $prod_cnt++) {
			$this->products[$prod_cnt]['extra_info'] = array();

			$extra_info_select_sql = "	SELECT orders_products_extra_info_key, orders_products_extra_info_value
										FROM " . TABLE_ORDERS_PRODUCTS_EXTRA_INFO . "
										WHERE orders_products_id = '" . tep_db_input($this->products[$prod_cnt]['orders_products_id']) . "'";
			$extra_info_result_sql = tep_db_query($extra_info_select_sql);

			while ($extra_info_row = tep_db_fetch_array($extra_info_result_sql)) {
				$value = $extra_info_row['orders_products_extra_info_value'];

				if (in_array($extra_info_row['orders_products_extra_info_key'], $styling_keys_array)) {
					$value = preg_replace_callback(	'/([^a-z]+)/s',
              										array($this, 'format_character_name'),
              										$value);

					if (tep_not_null($value))	$value = '<span style="color: red; font-size: 15px;"><b>'.$value.'</b></span>';
				}

				$this->products[$prod_cnt]['extra_info'][$extra_info_row['orders_products_extra_info_key']] = $value;
			}
		}
	}

	function insert_opders_products_extra_info($orders_products_id, $orders_products_extra_info_key, $orders_products_extra_info_value) {
		$data_sql_array = array('orders_products_id' => $orders_products_id,
								'orders_products_extra_info_key' => $orders_products_extra_info_key,
								'orders_products_extra_info_value' => $orders_products_extra_info_value
								);

		tep_db_perform(TABLE_ORDERS_PRODUCTS_EXTRA_INFO, $data_sql_array);
	}

	function get_compensate_products() {
		$index = 0;
		$this->info['compensation_fully_delivered'] = 1;

		$compensate_product_select_sql = "	SELECT *,
												IF(	orders_products_purchase_eta IS NULL or orders_products_purchase_eta=-999,
  													NULL,
  													(TO_DAYS(DATE_ADD('".$this->info['date_purchased']."', INTERVAL orders_products_purchase_eta HOUR))*24*3600 + TIME_TO_SEC(DATE_ADD('".$this->info['date_purchased']."', INTERVAL orders_products_purchase_eta HOUR)) - (TO_DAYS(now())*24*3600 + TIME_TO_SEC(now()))) / 3600
  												) AS purchase_eta
											FROM " . TABLE_ORDERS_PRODUCTS . "
											WHERE orders_id = '" . (int)$this->order_id . "'
												AND orders_products_is_compensate=1
											ORDER BY orders_products_id";
		$compensate_product_result_sql = tep_db_query($compensate_product_select_sql);
      	while ($compensate_product_row = tep_db_fetch_array($compensate_product_result_sql)) {
        	$this->compensate_products[$index] = array(	'order_products_id' => $compensate_product_row['orders_products_id'],
														'qty' => $compensate_product_row['products_quantity'],
			                                        	'name' => $compensate_product_row['products_name'],
            			                            	'id' => $compensate_product_row['products_id'],
                        			                	'price' => $compensate_product_row['products_price'],
            			                            	'final_price' => $compensate_product_row['final_price'],
                        			                	'pre_order' => $compensate_product_row['products_pre_order'],
                                    			    	'custom_products_type_id' => $compensate_product_row['custom_products_type_id'],
			                                        	'delivered_qty' => $compensate_product_row['products_delivered_quantity'],
            			                            	'purchase_eta' => $compensate_product_row['purchase_eta'],
                        			                	'org_purchase_eta' => $compensate_product_row['orders_products_purchase_eta']
                                    		    	);

			$this->compensate_products[$index]['price_info'] = array(	'delivered_price' => $compensate_product_row['products_good_delivered_price'],
																		'canceled_price' => $compensate_product_row['products_canceled_price'],
																		'reversed_price' => $compensate_product_row['products_reversed_price']);

			if ($compensate_product_row['products_delivered_quantity'] < $compensate_product_row['products_quantity'])	$this->info['compensation_fully_delivered'] = 0;

			if ($cdkey_info = $this->get_cdkey_identifier($compensate_product_row['products_id'], $compensate_product_row['orders_products_id'])) {
				$this->compensate_products[$index]['cdkey_info'] = $cdkey_info;
			}

			$compensation_select_sql = "SELECT compensate_for_orders_products_id, compensate_entered_currency, compensate_entered_currency_value, compensate_order_currency, compensate_order_currency_value, compensate_accident_amount, compensate_non_accident_amount, compensate_supplier_amount, compensate_by_supplier_id,
											orders_compensate_products_added_by, orders_compensate_products_messages
										FROM " . TABLE_ORDERS_COMPENSATE_PRODUCTS . "
										WHERE orders_products_id = '" . tep_db_input($compensate_product_row['orders_products_id']) . "'";
			$compensation_result_sql = tep_db_query($compensation_select_sql);
	      	if ($compensation_row = tep_db_fetch_array($compensation_result_sql)) {
	      		$this->compensate_products[$index]['compensate'] = array(	'for_product' => $compensation_row['compensate_for_orders_products_id'],
																			'input_currency' => $compensation_row['compensate_entered_currency'],
																			'input_currency_value' => $compensation_row['compensate_entered_currency_value'],
																			'output_currency' => $compensation_row['compensate_order_currency'],
																			'output_currency_value' => $compensation_row['compensate_order_currency_value'],
																			'accident_amount' => $compensation_row['compensate_accident_amount'],
																			'non_accident_amount' => $compensation_row['compensate_non_accident_amount'],
																			'supplier_amount' => $compensation_row['compensate_supplier_amount'],
																			'supplier_id' => $compensation_row['compensate_by_supplier_id'],
																			'added_by' => $compensation_row['orders_compensate_products_added_by'],
																			'comments' => $compensation_row['orders_compensate_products_messages']
																			);
		    }

			$index++;
		}
	}

	function get_products_ordered()
	{
		global $languages_id, $currencies;
		$products_ordered = '';
		for ($i=0, $n=sizeof($this->products); $i<$n; $i++) {
	    	$currencies->product_instance_id = tep_not_null($this->products[$i]['custom_content']['hla_account_id']) ? $this->products[$i]['custom_content']['hla_account_id'] : '';

		    $products_ordered_attributes = '';
		    if (isset($this->products[$i]['attributes'])) {
		      	for ($j=0, $n2=sizeof($this->products[$i]['attributes']); $j<$n2; $j++) {
		      		if (DOWNLOAD_ENABLED == 'true') {
		          		$attributes_query = "	select popt.products_options_name, poval.products_options_values_name, pa.options_values_price, pa.price_prefix, pad.products_attributes_maxdays, pad.products_attributes_maxcount , pad.products_attributes_filename
		                               			from " . TABLE_PRODUCTS_OPTIONS . " popt, " . TABLE_PRODUCTS_OPTIONS_VALUES . " poval, " . TABLE_PRODUCTS_ATTRIBUTES . " pa
		                               			left join " . TABLE_PRODUCTS_ATTRIBUTES_DOWNLOAD . " pad
		                                			on pa.products_attributes_id=pad.products_attributes_id
		                               			where pa.products_id = '" . $this->products[$i]['id'] . "'
					                                and pa.options_id = '" . $this->products[$i]['attributes'][$j]['option_id'] . "'
					                                and pa.options_id = popt.products_options_id
					                                and pa.options_values_id = '" . $this->products[$i]['attributes'][$j]['value_id'] . "'
					                                and pa.options_values_id = poval.products_options_values_id
					                                and popt.language_id = '" . $languages_id . "'
					                                and poval.language_id = '" . $languages_id . "'";
		          		$attributes = tep_db_query($attributes_query);
		        	} else {
		          		$attributes = tep_db_query("select popt.products_options_name, poval.products_options_values_name, pa.options_values_price, pa.price_prefix from " . TABLE_PRODUCTS_OPTIONS . " popt, " . TABLE_PRODUCTS_OPTIONS_VALUES . " poval, " . TABLE_PRODUCTS_ATTRIBUTES . " pa where pa.products_id = '" . $this->products[$i]['id'] . "' and pa.options_id = '" . $this->products[$i]['attributes'][$j]['option_id'] . "' and pa.options_id = popt.products_options_id and pa.options_values_id = '" . $this->products[$i]['attributes'][$j]['value_id'] . "' and pa.options_values_id = poval.products_options_values_id and popt.language_id = '" . $languages_id . "' and poval.language_id = '" . $languages_id . "'");
		        	}
		        	$attributes_values = tep_db_fetch_array($attributes);

		        	$products_ordered_attributes .= "\n\t" . $attributes_values['products_options_name'] . ' ' . $attributes_values['products_options_values_name'];
		      	}
			}

			$cat_path = tep_output_generated_category_path($this->products[$i]['id'], 'product');
	      	$product_email_display_str = $cat_path . (tep_not_null($cat_path) ? ' > ' : '') . $this->products[$i]['name'] . (tep_not_null($this->products[$i]['model']) ? ' (' . $this->products[$i]['model'] . ')' : '');

		    $products_ordered .= $this->products[$i]['qty'] . ' x ' . $product_email_display_str . ' = ' . $currencies->display_price($this->products[$i]['final_price'], $this->products[$i]['tax'], $this->products[$i]['qty']) . $products_ordered_attributes . "\n";
		}

		return $products_ordered;
	}

	function get_payments_breakdown()
	{
		$payments_breakdown = '';
		for ($i=0, $n=sizeof($this->totals); $i<$n; $i++) {
			$payments_breakdown .= strip_tags($this->totals[$i]['title']) . ' ' . strip_tags($this->totals[$i]['text']) . "\n";
		}

		return $payments_breakdown;
	}

	function do_payment_action($fn_name, $language)
	{
		$payment_module_obj = new payment_methods($this->info['payment_methods_id']);
		$selected_payment_module_obj = $payment_module_obj->payment_method_array;

		if (is_object($selected_payment_module_obj) && ((int)$selected_payment_module_obj->payment_methods_id > 0)) {
			eval('$selected_payment_module_obj->$fn_name($this->order_id);');	// Use single quote
		}
	}

    function cart($is_sc=false)
    {
    	global $customer_id, $sendto, $billto, $cart, $languages_id, $currency, $currencies, $shipping, $payment, $pm_2CO_cc_owner, $credit_card_owner;
		global $cart_comments_obj, $payment_modules;

      	$this->content_type = $cart->get_content_type();

		$customer_address_sql = "	SELECT 	c.customers_firstname, c.customers_lastname, c.customers_gender, c.customers_country_dialing_code_id, c.customers_telephone, c.customers_email_address, c.customers_groups_id,
											ab.entry_company, ab.entry_street_address, ab.entry_suburb, ab.entry_postcode, ab.entry_city, ab.entry_zone_id,
											z.zone_name,
											co.countries_id, co.countries_name, co.countries_iso_code_2, co.countries_iso_code_3, co.address_format_id, ab.entry_state
									FROM " . TABLE_CUSTOMERS . " AS c
									LEFT JOIN " . TABLE_ADDRESS_BOOK . " AS ab
										ON (c.customers_default_address_id = ab.address_book_id AND ab.customers_id = '" . (int)$customer_id . "')
									LEFT JOIN " . TABLE_ZONES . " AS z
										ON (ab.entry_zone_id = z.zone_id)
									LEFT JOIN " . TABLE_COUNTRIES . " AS co
										ON (ab.entry_country_id = co.countries_id)
									WHERE c.customers_id = '" . (int)$customer_id . "'";
      	$customer_address_query = tep_db_query($customer_address_sql);
      	$customer_address = tep_db_fetch_array($customer_address_query);

      	$telephone = tep_parse_telephone($customer_address['customers_telephone'], $customer_address['customers_country_dialing_code_id'], 'id');

      	$shipping_address_query = tep_db_query("select ab.entry_firstname, ab.entry_lastname, ab.entry_company, ab.entry_street_address, ab.entry_suburb, ab.entry_postcode, ab.entry_city, ab.entry_zone_id, z.zone_name, ab.entry_country_id, c.countries_id, c.countries_name, c.countries_iso_code_2, c.countries_iso_code_3, c.address_format_id, ab.entry_state from " . TABLE_ADDRESS_BOOK . " ab left join " . TABLE_ZONES . " z on (ab.entry_zone_id = z.zone_id) left join " . TABLE_COUNTRIES . " c on (ab.entry_country_id = c.countries_id) where ab.customers_id = '" . (int)$customer_id . "' and ab.address_book_id = '" . (int)$sendto . "'");
      	$shipping_address = tep_db_fetch_array($shipping_address_query);// Billing address always same as Profile since we just allow 1 address.

		$billto = $_SESSION['customer_default_address_id'];

      	$billing_address_query = tep_db_query("select ab.entry_firstname, ab.entry_lastname, ab.entry_company, ab.entry_street_address, ab.entry_suburb, ab.entry_postcode, ab.entry_city, ab.entry_zone_id, z.zone_name, ab.entry_country_id, c.countries_id, c.countries_name, c.countries_iso_code_2, c.countries_iso_code_3, c.address_format_id, ab.entry_state from " . TABLE_ADDRESS_BOOK . " ab left join " . TABLE_ZONES . " z on (ab.entry_zone_id = z.zone_id) left join " . TABLE_COUNTRIES . " c on (ab.entry_country_id = c.countries_id) where ab.customers_id = '" . (int)$customer_id . "' and ab.address_book_id = '" . (int)$billto . "'");
      	$billing_address = tep_db_fetch_array($billing_address_query);

      	$tax_address_query = tep_db_query("select ab.entry_country_id, ab.entry_zone_id from " . TABLE_ADDRESS_BOOK . " ab left join " . TABLE_ZONES . " z on (ab.entry_zone_id = z.zone_id) where ab.customers_id = '" . (int)$customer_id . "' and ab.address_book_id = '" . (int)($this->content_type == 'virtual' ? $billto : $sendto) . "'");
      	$tax_address = tep_db_fetch_array($tax_address_query);

		$customer_international_dialing_code_select_sql = "	SELECT c.countries_international_dialing_code, c.countries_name
															FROM " . TABLE_CUSTOMERS . " AS cust
															INNER JOIN " . TABLE_COUNTRIES . " AS c
																ON (cust.customers_country_dialing_code_id = c.countries_id)
															WHERE cust.customers_id = '" . (int)$customer_id . "'";
		$customer_international_dialing_code_query = tep_db_query($customer_international_dialing_code_select_sql);
		if ($customer_international_dialing_code_row = tep_db_fetch_array($customer_international_dialing_code_query)) {
			$country_dialing_code = $customer_international_dialing_code_row['countries_international_dialing_code'];
		} else {
			$country_dialing_code = 'NULL';
		}

      	$this->info = array('order_status' => DEFAULT_ORDERS_STATUS_ID,
                          	'currency' => $currency,
                          	'currency_value' => $currencies->get_value($currency, 'sell'),
                          	'payment_method' => $payment,
                          	'cc_type' => (isset($GLOBALS['cc_type']) ? $GLOBALS['cc_type'] : ''),
                          	'cc_owner' => (isset($GLOBALS['cc_owner']) ? $GLOBALS['cc_owner'] : ''),
                          	'cc_number' => (isset($GLOBALS['cc_number']) ? $GLOBALS['cc_number'] : ''),
                          	'cc_expires' => (isset($GLOBALS['cc_expires']) ? $GLOBALS['cc_expires'] : ''),
                          	'pm_2CO_cc_owner_firstname' => ($pm_2CO_cc_owner["firstname"] ? $pm_2CO_cc_owner["firstname"] : ''),
                          	'pm_2CO_cc_owner_lastname' => ($pm_2CO_cc_owner["lastname"] ? $pm_2CO_cc_owner["lastname"] : ''),
                          	'wp_owner_firstname' => ($credit_card_owner["wp"]["firstname"] ? $credit_card_owner["wp"]["firstname"] : ''),
                          	'wp_owner_lastname' => ($credit_card_owner["wp"]["lastname"] ? $credit_card_owner["wp"]["lastname"] : ''),
                          	'shipping_method' => $shipping['title'],
                          	'shipping_cost' => $shipping['cost'],
                          	'subtotal' => 0,
                          	'tax' => 0,
                          	'tax_groups' => array(),
                          	'comments' => (isset($GLOBALS['comments']) ? $GLOBALS['comments'] : ''));

		if (is_object($cart_comments_obj)) {
			$this->info['comments_array'] = $cart_comments_obj->get_individual_plain_comments($GLOBALS['custom_comments'], 1);
		}

      	if (isset($GLOBALS[$payment]) && is_object($GLOBALS[$payment])) {
        	$this->info['payment_method'] = $GLOBALS[$payment]->title;

        	// added by wei chen, use this if the display title is different from payment method name
        	if ( isset($GLOBALS[$payment]->display_title) && $GLOBALS[$payment]->display_title != '' ) {
				$this->info['display_method'] = $GLOBALS[$payment]->display_title;
			}

        	if ( isset($GLOBALS[$payment]->order_status) && is_numeric($GLOBALS[$payment]->order_status) && ($GLOBALS[$payment]->order_status > 0) ) {
          		$this->info['order_status'] = $GLOBALS[$payment]->order_status;
        	}
      	} else {
      		if (!tep_not_null($this->info['payment_method'])) {	// If no payment method selected
      			if (is_object($payment_modules)) {
		      		if ($payment_modules->check_credit_covers())	$this->info['order_status'] = 7;	// Fully covered by store credit
		      	}
      		}
      	}

      	$this->customer = array('firstname' => $customer_address['customers_firstname'],
                              	'lastname' => $customer_address['customers_lastname'],
                              	'gender' => $customer_address['customers_gender'],
                              	'company' => $customer_address['entry_company'],
                              	'street_address' => $customer_address['entry_street_address'],
                              	'suburb' => $customer_address['entry_suburb'],
                              	'city' => $customer_address['entry_city'],
                              	'postcode' => $customer_address['entry_postcode'],
                              	'state' => ((tep_not_null($customer_address['entry_state'])) ? $customer_address['entry_state'] : $customer_address['zone_name']),
                              	'zone_id' => $customer_address['entry_zone_id'],
                              	'country' => array('id' => $customer_address['countries_id'], 'title' => $customer_address['countries_name'], 'iso_code_2' => $customer_address['countries_iso_code_2'], 'iso_code_3' => $customer_address['countries_iso_code_3']),
                              	'format_id' => $customer_address['address_format_id'],
                              	'telephone_country_name' => ((tep_not_null($customer_international_dialing_code_row['countries_name'])) ? $customer_international_dialing_code_row['countries_name'] : 'NULL'),
                              	'int_dialing_code' => $country_dialing_code,
                              	'telephone' => $telephone,
                              	'email_address' => $customer_address['customers_email_address'],
                              	'customers_groups_id' => $customer_address['customers_groups_id']);

      	$this->delivery = array('firstname' => $shipping_address['entry_firstname'],
                              	'lastname' => $shipping_address['entry_lastname'],
                              	'company' => $shipping_address['entry_company'],
                              	'street_address' => $shipping_address['entry_street_address'],
                              	'suburb' => $shipping_address['entry_suburb'],
                              	'city' => $shipping_address['entry_city'],
                              	'postcode' => $shipping_address['entry_postcode'],
                              	'state' => ((tep_not_null($shipping_address['entry_state'])) ? $shipping_address['entry_state'] : $shipping_address['zone_name']),
                              	'zone_id' => $shipping_address['entry_zone_id'],
                              	'country' => array('id' => $shipping_address['countries_id'], 'title' => $shipping_address['countries_name'], 'iso_code_2' => $shipping_address['countries_iso_code_2'], 'iso_code_3' => $shipping_address['countries_iso_code_3']),
                              	'country_id' => $shipping_address['entry_country_id'],
                              	'format_id' => $shipping_address['address_format_id']);

		$this->billing = array(	'firstname' => $billing_address['entry_firstname'],
                             	'lastname' => $billing_address['entry_lastname'],
                             	'company' => $billing_address['entry_company'],
                             	'street_address' => $billing_address['entry_street_address'],
                             	'suburb' => $billing_address['entry_suburb'],
                             	'city' => $billing_address['entry_city'],
                             	'postcode' => $billing_address['entry_postcode'],
                             	'state' => ((tep_not_null($billing_address['entry_state'])) ? $billing_address['entry_state'] : $billing_address['zone_name']),
                             	'zone_id' => $billing_address['entry_zone_id'],
                             	'country' => array('id' => $billing_address['countries_id'], 'title' => $billing_address['countries_name'], 'iso_code_2' => $billing_address['countries_iso_code_2'], 'iso_code_3' => $billing_address['countries_iso_code_3']),
                             	'country_id' => $billing_address['entry_country_id'],
                             	'format_id' => $billing_address['address_format_id']);

      	$index = 0;
      	$pm_status = false;
      	if(STORE_PROMOTION == 'true'){
  			$promotions_query = tep_db_query("select promotions_id, promotions_title, promotions_description, promotions_from, promotions_to, promotions_min_value from promotions where promotions_status = '1' order by promotions_id");
  		    $promotions = tep_db_fetch_array($promotions_query);
		    if($promotions['promotions_id']){
		    	$pm_status = true;	//on the promotions
			    //$pm_date_from = $promotions['promotions_from'];
			    //$pm_date_to = $promotions['promotions_to'];
			    $pm_value = $promotions['promotions_min_value'];
		    }
  		}

      	$products = $cart->get_products();

		for ($i=0, $n=sizeof($products); $i<$n; $i++) {
      		if($products[$i]['pm_total'] > $pm_value){
				//do nothing
      		} else {
      			$products[$i]['pm_price'] ="";
      		}

	    	$currencies->product_instance_id = tep_not_null($products[$i]['custom_content']['hla_account_id']) ? $products[$i]['custom_content']['hla_account_id'] : '';

      		if ($products[$i]['pm_price'] == "FREE") {
      			$quantity = $products[$i]['quantity'] - 1;
      			$pm_price = $quantity * $products[$i]['final_price'];
      			//echo $pm_price + $cart->attributes_price($products[$i]['id']);
      			$this->products[$index] = array('qty' => $products[$i]['quantity'],
                                        		'name' => $products[$i]['name']." <font color=orange>(~1 Free Items - worth ".$currencies->format($products[$i]['final_price']).")</font>",
                                        		'model' => $products[$i]['model'],
                                        		'tax' => tep_get_tax_rate($products[$i]['tax_class_id'], $tax_address['entry_country_id'], $tax_address['entry_zone_id']),
                                        		'tax_description' => tep_get_tax_description($products[$i]['tax_class_id'], $tax_address['entry_country_id'], $tax_address['entry_zone_id']),
                                        		'normal_price' => $products[$i]['normal_price'],
                                        		'price' => $products[$i]['price'],
                                        		'final_price' => $products[$i]['price'] + $cart->attributes_price($products[$i]['id']),
                                        		'base_currency' => $products[$i]['base_currency'],
                                        		'discounts' => $products[$i]['discounts'],
                                        		'products_categories_id' => $products[$i]['products_categories_id'],
                                        		'pm_price' => 'FREE',
                                        		'weight' => $products[$i]['weight'],
                                        		'id' => $products[$i]['id'],
                                        		'pre_order' => $products[$i]['pre_order'],
												'custom_products_type_id' => (int)$products[$i]['custom_products_type_id'],
												'custom_products_type_name' => $this->get_custom_products_type_name((int)$products[$i]['custom_products_type_id']),
												'custom_content' => $products[$i]['custom_content']
                                        		);

                if ($is_sc) {
                    $this->products[$index]['storage_price'] = array(	'normal_price' => $this->products[$i]['normal_price'] / $currencies->get_value($currency, 'sell'),
                                                                        'price' => $this->products[$i]['price'] / $currencies->get_value($currency, 'sell'),
                                                                        'final_price' => $this->products[$i]['final_price'] / $currencies->get_value($currency, 'sell'));

                } else {
                    $this->products[$index]['storage_price'] = array(	'normal_price' => $currencies->display_noformat_price_nodiscount($this->products[$i]['id'], $this->products[$i]['normal_price']) / $currencies->get_value($currency, 'sell'),
                                                                        'price' => $currencies->display_noformat_price_nodiscount($this->products[$i]['id'], $this->products[$i]['price'], $this->products[$i]['tax'], 1, $this->products[$i]['discounts']) / $currencies->get_value($currency, 'sell'),
                                                                        'final_price' => $currencies->display_noformat_price_nodiscount($this->products[$i]['id'], $this->products[$i]['final_price'], $this->products[$i]['tax'], 1, $this->products[$i]['discounts']) / $currencies->get_value($currency, 'sell'));
                }

				$product_query = tep_db_query("select products_bundle, products_bundle_dynamic from " . TABLE_PRODUCTS . " where products_id = '" . $products[$i]['id'] . "'");
				$product = tep_db_fetch_array($product_query);
				if ($product['products_bundle_dynamic']=="yes") {
					$b_index = 0;
					$product_bundle_dynamic_select_sql = "	SELECT cb.subproducts_id, cb.subproducts_price, cb.subproducts_quantity, p.custom_products_type_id, p.products_model
															FROM " . TABLE_CUSTOMERS_BASKET_BUNDLE . " AS cb
															LEFT JOIN " . TABLE_PRODUCTS . " AS p
    															ON cb.subproducts_id = p.products_id
															WHERE cb.customers_id = '" . (int)$customer_id . "'
																AND cb.products_bundle_id = '" . $products[$i]["id"] . "'
																AND p.products_status=1
															ORDER BY cb.subproducts_id";
					$product_bundle_dynamic_result_sql = tep_db_query($product_bundle_dynamic_select_sql);
					while ($product_bundle = tep_db_fetch_array($product_bundle_dynamic_result_sql)) {
						$products_name = tep_get_products_name($product_bundle['subproducts_id']);

						$this->products[$index]['bundle'][$b_index] = array('qty' => $product_bundle['subproducts_quantity'],
		                                        							'name' => $products_name,
		                                        							'model' => $product_bundle['products_model'],
		                                        							'tax' => tep_get_tax_rate($products[$i]['tax_class_id'], $tax_address['entry_country_id'], $tax_address['entry_zone_id']),
		                                        							'tax_description' => tep_get_tax_description($products[$i]['tax_class_id'], $tax_address['entry_country_id'], $tax_address['entry_zone_id']),
		                                        							'normal_price' => $product_bundle['subproducts_price'],
		                                        							'price' => $product_bundle['subproducts_price'],
		                                        							'final_price' => $product_bundle['subproducts_price'],
		                                        							'products_categories_id' => $products[$i]['products_categories_id'],
		                                        							'pm_price' => '',
		                                        							'weight' => '',
		                                        							'id' => $product_bundle['subproducts_id']);

                        if ($is_sc) {
                            $this->products[$index]['bundle'][$b_index]['storage_price'] = array(	'normal_price' => $product_bundle['subproducts_price'] / $currencies->get_value($currency, 'sell'),
                                                                                                    'price' => $product_bundle['subproducts_price'] / $currencies->get_value($currency, 'sell'),
                                                                                                    'final_price' => $product_bundle['subproducts_price'] / $currencies->get_value($currency, 'sell'));

                        } else {
                            $this->products[$index]['bundle'][$b_index]['storage_price'] = array(	'normal_price' => $currencies->display_noformat_price_nodiscount($product_bundle['subproducts_id'], $product_bundle['subproducts_price']) / $currencies->get_value($currency, 'sell'),
                                                                                                    'price' => $currencies->display_noformat_price_nodiscount($product_bundle['subproducts_id'], $product_bundle['subproducts_price'], '', 1, '') / $currencies->get_value($currency, 'sell'),
                                                                                                    'final_price' => $currencies->display_noformat_price_nodiscount($product_bundle['subproducts_id'], $product_bundle['subproducts_price'], '', 1, '') / $currencies->get_value($currency, 'sell'));
                        }

						if (!isset($this->products[$index]['custom_products_type_id'])) {
						 	$this->products[$index]['custom_products_type_id'] = (int)$product_bundle['custom_products_type_id'];
							$this->products[$index]['custom_products_type_name'] = $this->get_custom_products_type_name((int)$product_bundle['custom_products_type_id']);
						}

						$b_index++;
					}
				} else if ($product['products_bundle']=="yes") {
					$s_index = 0;
					$static_bundle_select_sql = "	SELECT pb.subproduct_id, pb.subproduct_qty, p.custom_products_type_id, p.products_price, p.products_model
										 			FROM ". TABLE_PRODUCTS_BUNDLES . " AS pb
										 			LEFT JOIN " . TABLE_PRODUCTS . " AS p
										 				ON pb.subproduct_id=p.products_id
													WHERE pb.bundle_id='" . $products[$i]['id'] . "'
														AND p.products_status=1";
					$static_bundle_result_sql = tep_db_query($static_bundle_select_sql);
					while ($static_bundle = tep_db_fetch_array($static_bundle_result_sql)) {
						$products_name = tep_get_products_name($static_bundle['subproduct_id']);

						$this->products[$index]['static'][$s_index] = array('qty' => $static_bundle['subproduct_qty'],
		                                        							'name' => $products_name,
		                                        							'model' => $static_bundle['products_model'],
		                                        							'tax' => tep_get_tax_rate($products[$i]['tax_class_id'], $tax_address['entry_country_id'], $tax_address['entry_zone_id']),
		                                        							'tax_description' => tep_get_tax_description($products[$i]['tax_class_id'], $tax_address['entry_country_id'], $tax_address['entry_zone_id']),
		                                        							'normal_price' => $static_bundle['products_price'],
		                                        							'price' => '0.00',
		                                        							'final_price' => '0.00',
		                                        							'products_categories_id' => $products[$i]['products_categories_id'],
		                                        							'pm_price' => '',
		                                        							'weight' => '',
		                                        							'id' => $static_bundle['subproduct_id']);

                        if ($is_sc) {
                            $this->products[$index]['static'][$s_index]['storage_price'] = array(	'normal_price' => $static_bundle['products_price'] / $currencies->get_value($currency, 'sell'),
                                                                                                    'price' => $static_bundle['products_price'] / $currencies->get_value($currency, 'sell'),
                                                                                                    'final_price' => $static_bundle['products_price'] / $currencies->get_value($currency, 'sell'));

                        } else {
                            $this->products[$index]['static'][$s_index]['storage_price'] = array(	'normal_price' => $currencies->display_noformat_price_nodiscount($static_bundle['subproduct_id'], $static_bundle['products_price']) / $currencies->get_value($currency, 'sell'),
                                                                                                    'price' => $currencies->display_noformat_price_nodiscount($static_bundle['subproduct_id'], $static_bundle['products_price'], '', 1, '') / $currencies->get_value($currency, 'sell'),
                                                                                                    'final_price' => $currencies->display_noformat_price_nodiscount($static_bundle['subproduct_id'], $static_bundle['products_price'], '', 1, '') / $currencies->get_value($currency, 'sell'));
                        }

						if (!isset($this->products[$index]['custom_products_type_id'])) {
						 	$this->products[$index]['custom_products_type_id'] = (int)$static_bundle['custom_products_type_id'];
							$this->products[$index]['custom_products_type_name'] = $this->get_custom_products_type_name((int)$static_bundle['custom_products_type_id']);
						}

						$s_index++;
					}
				} else {
					$this->products[$index]['custom_products_type_id'] = (int)$products[$i]['custom_products_type_id'];
					$this->products[$index]['custom_products_type_name'] = $this->get_custom_products_type_name((int)$products[$i]['custom_products_type_id']);
				}
      		} else {
        		$this->products[$index] = array('qty' => $products[$i]['quantity'],
                                        		'name' => $products[$i]['name'],
                                        		'model' => $products[$i]['model'],
                                        		'tax' => tep_get_tax_rate($products[$i]['tax_class_id'], $tax_address['entry_country_id'], $tax_address['entry_zone_id']),
                                        		'tax_description' => tep_get_tax_description($products[$i]['tax_class_id'], $tax_address['entry_country_id'], $tax_address['entry_zone_id']),
                                        		'normal_price' => $products[$i]['normal_price'],
                                        		'price' => $products[$i]['price'],
                                        		'final_price' => $products[$i]['price'] + $cart->attributes_price($products[$i]['id']),
                                        		'base_currency' => $products[$i]['base_currency'],
                                        		'discounts' => $products[$i]['discounts'],
                                        		'products_categories_id' => $products[$i]['products_categories_id'],
                                        		'pm_price' => '',
                                        		'weight' => $products[$i]['weight'],
                                        		'id' => $products[$i]['id'],
                                        		'pre_order' => $products[$i]['pre_order'],
												'custom_content' => $products[$i]['custom_content']
                                        		);

                if ($is_sc) {
                    $this->products[$index]['storage_price'] = array(	'normal_price' => $this->products[$i]['normal_price'] / $currencies->get_value($currency, 'sell'),
                                                                        'price' => $this->products[$i]['price'] / $currencies->get_value($currency, 'sell'),
                                                                        'final_price' => $this->products[$i]['final_price'] / $currencies->get_value($currency, 'sell'));

                } else {
                    $this->products[$index]['storage_price'] = array(	'normal_price' => $currencies->display_noformat_price_nodiscount($this->products[$i]['id'], $this->products[$i]['normal_price']) / $currencies->get_value($currency, 'sell'),
                                                                        'price' => $currencies->display_noformat_price_nodiscount($this->products[$i]['id'], $this->products[$i]['price'], $this->products[$i]['tax'], 1, $this->products[$i]['discounts']) / $currencies->get_value($currency, 'sell'),
                                                                        'final_price' => $currencies->display_noformat_price_nodiscount($this->products[$i]['id'], $this->products[$i]['final_price'], $this->products[$i]['tax'], 1, $this->products[$i]['discounts']) / $currencies->get_value($currency, 'sell'));
                }

                $product_query = tep_db_query("select products_bundle, products_bundle_dynamic from " . TABLE_PRODUCTS . " where products_id = '" . $products[$i]['id'] . "'");
				$product = tep_db_fetch_array($product_query);
				if ($product['products_bundle_dynamic']=="yes") {
					$b_index = 0;
					$product_bundle_dynamic_select_sql = "	SELECT cb.subproducts_id, cb.subproducts_price, cb.subproducts_quantity, p.custom_products_type_id, p.products_model
															FROM " . TABLE_CUSTOMERS_BASKET_BUNDLE . " AS cb
															LEFT JOIN " . TABLE_PRODUCTS . " AS p
    															ON cb.subproducts_id = p.products_id
															WHERE cb.customers_id = '" . (int)$customer_id . "'
																AND cb.products_bundle_id = '" . $products[$i]["id"] . "'
																AND p.products_status=1
															ORDER BY cb.subproducts_id";
					$product_bundle_dynamic_result_sql = tep_db_query($product_bundle_dynamic_select_sql);
					while ($product_bundle = tep_db_fetch_array($product_bundle_dynamic_result_sql)) {
						$products_name = tep_get_products_name($product_bundle['subproducts_id']);

						$this->products[$index]['bundle'][$b_index] = array('qty' => $product_bundle['subproducts_quantity'],
		                                        							'name' => $products_name,
		                                        							'model' => $product_bundle['products_model'],
		                                        							'tax' => tep_get_tax_rate($products[$i]['tax_class_id'], $tax_address['entry_country_id'], $tax_address['entry_zone_id']),
		                                        							'tax_description' => tep_get_tax_description($products[$i]['tax_class_id'], $tax_address['entry_country_id'], $tax_address['entry_zone_id']),
		                                        							'normal_price' => $product_bundle['subproducts_price'],
		                                        							'price' => $product_bundle['subproducts_price'],
		                                        							'final_price' => $product_bundle['subproducts_price'],
		                                        							'products_categories_id' => $products[$i]['products_categories_id'],
		                                        							'pm_price' => '',
		                                        							'weight' => '',
		                                        							'id' => $product_bundle['subproducts_id']);

                        if ($is_sc) {
                            $this->products[$index]['bundle'][$b_index]['storage_price'] = array(	'normal_price' => $product_bundle['subproducts_price'] / $currencies->get_value($currency, 'sell'),
                                                                                                    'price' => $product_bundle['subproducts_price'] / $currencies->get_value($currency, 'sell'),
                                                                                                    'final_price' => $product_bundle['subproducts_price'] / $currencies->get_value($currency, 'sell'));

                        } else {
                            $this->products[$index]['bundle'][$b_index]['storage_price'] = array(	'normal_price' => $currencies->display_noformat_price_nodiscount($product_bundle['subproducts_id'], $product_bundle['subproducts_price']) / $currencies->get_value($currency, 'sell'),
                                                                                                    'price' => $currencies->display_noformat_price_nodiscount($product_bundle['subproducts_id'], $product_bundle['subproducts_price'], tep_get_tax_rate($products[$i]['tax_class_id'], $tax_address['entry_country_id'], $tax_address['entry_zone_id']), 1) / $currencies->get_value($currency, 'sell'),
                                                                                                    'final_price' => $currencies->display_noformat_price_nodiscount($product_bundle['subproducts_id'], $product_bundle['subproducts_price'], tep_get_tax_rate($products[$i]['tax_class_id'], $tax_address['entry_country_id'], $tax_address['entry_zone_id']), 1) / $currencies->get_value($currency, 'sell'));
                        }

						if (!isset($this->products[$index]['custom_products_type_id'])) {
						 	$this->products[$index]['custom_products_type_id'] = (int)$product_bundle['custom_products_type_id'];
							$this->products[$index]['custom_products_type_name'] = $this->get_custom_products_type_name((int)$product_bundle['custom_products_type_id']);
						}

						$b_index++;
					}
				} else if ($product['products_bundle']=="yes") {
					$s_index = 0;
					$static_bundle_select_sql = "	SELECT pb.subproduct_id, pb.subproduct_qty, p.custom_products_type_id, p.products_price, p.products_model
										 			FROM ". TABLE_PRODUCTS_BUNDLES . " AS pb
										 			LEFT JOIN " . TABLE_PRODUCTS . " AS p
										 				ON pb.subproduct_id=p.products_id
													WHERE pb.bundle_id='" . $products[$i]['id'] . "'
														AND p.products_status=1" ;
					$static_bundle_result_sql = tep_db_query($static_bundle_select_sql);
					while ($static_bundle = tep_db_fetch_array($static_bundle_result_sql)) {
						$products_name = tep_get_products_name($static_bundle['subproduct_id']);

						$this->products[$index]['static'][$s_index] = array('qty' => $static_bundle['subproduct_qty'],
		                                        							'name' => $products_name,
		                                        							'model' => $static_bundle['products_model'],
		                                        							'tax' => tep_get_tax_rate($products[$i]['tax_class_id'], $tax_address['entry_country_id'], $tax_address['entry_zone_id']),
		                                        							'tax_description' => tep_get_tax_description($products[$i]['tax_class_id'], $tax_address['entry_country_id'], $tax_address['entry_zone_id']),
		                                        							'normal_price' => $static_bundle['products_price'],
		                                        							'price' => '0.00',
		                                        							'final_price' => '0.00',
		                                        							'products_categories_id' => $products[$i]['products_categories_id'],
		                                        							'pm_price' => '',
		                                        							'weight' => '',
		                                        							'id' => $static_bundle['subproduct_id']);

                        if ($is_sc) {
                            $this->products[$index]['static'][$s_index]['storage_price'] = array(	'normal_price' => $static_bundle['products_price'] / $currencies->get_value($currency, 'sell'),
                                                                                                    'price' => $static_bundle['products_price'] / $currencies->get_value($currency, 'sell'),
                                                                                                    'final_price' => $static_bundle['products_price'] / $currencies->get_value($currency, 'sell'));

                        } else {
                            $this->products[$index]['static'][$s_index]['storage_price'] = array(	'normal_price' => $currencies->display_noformat_price_nodiscount($static_bundle['subproduct_id'], $static_bundle['products_price']) / $currencies->get_value($currency, 'sell'),
                                                                                                    'price' => $currencies->display_noformat_price_nodiscount($static_bundle['subproduct_id'], $static_bundle['products_price']) / $currencies->get_value($currency, 'sell'),
                                                                                                    'final_price' => $currencies->display_noformat_price_nodiscount($static_bundle['subproduct_id'], $static_bundle['products_price']) / $currencies->get_value($currency, 'sell'));
                        }

						if (!isset($this->products[$index]['custom_products_type_id'])) {
						 	$this->products[$index]['custom_products_type_id'] = (int)$static_bundle['custom_products_type_id'];
							$this->products[$index]['custom_products_type_name'] = $this->get_custom_products_type_name((int)$static_bundle['custom_products_type_id']);
						}

						$s_index++;
					}
				} else {
					$this->products[$index]['custom_products_type_id'] = (int)$products[$i]['custom_products_type_id'];
					$this->products[$index]['custom_products_type_name'] = $this->get_custom_products_type_name((int)$products[$i]['custom_products_type_id']);
				}
			}

			// ********** Begin Separate Price per Customer Mod **************
			global $customer_id;
			$customer_group_id_query = tep_db_query("select customers_group_id from " . TABLE_CUSTOMERS . " where customers_id = '". $customer_id . "'");
			$customer_group_id = tep_db_fetch_array($customer_group_id_query);
  			if ($customer_group_id['customers_group_id'] != '0'){
  				$orders_customers_price = tep_db_query("select customers_group_price from " . TABLE_PRODUCTS_GROUPS . " where customers_group_id = '". $customer_group_id['customers_group_id'] . "' and products_id = '" . $products[$i]['id'] . "'");
  				$orders_customers = tep_db_fetch_array($orders_customers_price);
    			if ($orders_customers = tep_db_fetch_array($orders_customers_price)) {
    				$this->products[$index] = array('price' => $orders_customers['customers_group_price'],
                                        			'final_price' => $orders_customers['customers_group_price'] + $cart->attributes_price($products[$i]['id']));
    			}
  			}
			// ********** End Separate Price per Customer Mod **************
        	if ($products[$i]['attributes']) {
          		$subindex = 0;
          		reset($products[$i]['attributes']);
          		while (list($option, $value) = each($products[$i]['attributes'])) {
            		$attributes_query = tep_db_query("select popt.products_options_name, poval.products_options_values_name, pa.options_values_price, pa.price_prefix from " . TABLE_PRODUCTS_OPTIONS . " popt, " . TABLE_PRODUCTS_OPTIONS_VALUES . " poval, " . TABLE_PRODUCTS_ATTRIBUTES . " pa where pa.products_id = '" . (int)$products[$i]['id'] . "' and pa.options_id = '" . (int)$option . "' and pa.options_id = popt.products_options_id and pa.options_values_id = '" . (int)$value . "' and pa.options_values_id = poval.products_options_values_id and popt.language_id = '" . (int)$languages_id . "' and poval.language_id = '" . (int)$languages_id . "'");
            		$attributes = tep_db_fetch_array($attributes_query);

            		$this->products[$index]['attributes'][$subindex] = array(	'option' => $attributes['products_options_name'],
                                                                     			'value' => $attributes['products_options_values_name'],
                                                                     			'option_id' => $option,
                                                                     			'value_id' => $value,
                                                                     			'prefix' => $attributes['price_prefix'],
                                                                     			'price' => $attributes['options_values_price']);
            		$subindex++;
          		}
        	}
			//promotion
        	if ($this->products[$index]['pm_price'] == "FREE"){
        		$qty = $this->products[$index]['qty'] - 1;
        	} else {
        		$qty = $this->products[$index]['qty'];
        	}

        	if ($is_sc) {
        		$shown_price = $this->products[$index]['price'] * $qty;
        	} else {
        		$shown_price = $currencies->display_noformat_price_nodiscount($this->products[$index]['id'], $this->products[$index]['price'], $this->products[$index]['tax'], $qty, $this->products[$index]['discounts']);
			}
			$shown_price = $shown_price / $currencies->get_value($currency, 'sell');	// MUST convert to DEFAULT_CURRENCY value

        	$this->info['subtotal'] += $shown_price;

        	$products_tax = $this->products[$index]['tax'];
        	$products_tax_description = $this->products[$index]['tax_description'];
        	if (DISPLAY_PRICE_WITH_TAX == 'true') {
          		$this->info['tax'] += $shown_price - ($shown_price / (($products_tax < 10) ? "1.0" . str_replace('.', '', $products_tax) : "1." . str_replace('.', '', $products_tax)));
          		if (isset($this->info['tax_groups'][$products_tax_description])) {
            		$this->info['tax_groups'][$products_tax_description] += $shown_price - ($shown_price / (($products_tax < 10) ? "1.0" . str_replace('.', '', $products_tax) : "1." . str_replace('.', '', $products_tax)));
          		} else {
            		$this->info['tax_groups'][$products_tax_description] = $shown_price - ($shown_price / (($products_tax < 10) ? "1.0" . str_replace('.', '', $products_tax) : "1." . str_replace('.', '', $products_tax)));
          		}
        	} else {
          		$this->info['tax'] += ($products_tax / 100) * $shown_price;
          		if (isset($this->info['tax_groups'][$products_tax_description])) {
            		$this->info['tax_groups'][$products_tax_description] += ($products_tax / 100) * $shown_price;
          		} else {
            		$this->info['tax_groups'][$products_tax_description] = ($products_tax / 100) * $shown_price;
          		}
        	}
			$index++;
		}

      	if (DISPLAY_PRICE_WITH_TAX == 'true') {
        	$this->info['total'] = $this->info['subtotal'] + $this->info['shipping_cost'];
      	} else {
        	$this->info['total'] = $this->info['subtotal'] + $this->info['tax'] + $this->info['shipping_cost'];
      	}

      	if ($this->info['subtotal'] <= 0) { // All product are free
			if ($this->verify_zero_amount_checkout()) {
      			$this->info['order_status'] = 7;
      		} else {
      			$this->info['order_status'] = 1;
      		}
      	}

	}

	function manage_order_sc($from_status, $to_status) {
		global $currencies;

		$result_array = array();
		$error = false;

		switch($to_status) {
			case '5':
				if ($from_status == '1' || $from_status == '7') {	// Re-issue when update from Pending / Verifying to Cancel
					$sc_canceled_select_sql = "	SELECT store_credit_history_id
												FROM " . TABLE_STORE_CREDIT_HISTORY . "
												WHERE store_credit_history_trans_type='C'
													AND store_credit_history_trans_id='" . tep_db_input($this->order_id) . "'
													AND store_credit_activity_type='".LOG_SC_ACTIVITY_TYPE_CANCEL."'";
					$sc_canceled_result_sql = tep_db_query($sc_canceled_select_sql);

					if (!tep_db_num_rows($sc_canceled_result_sql)) {	// Ignore auto credit if previously has SC canceled for this order
						$sc_used_array = $this->get_sc_used();
						if (count($sc_used_array)) {
							$sc_object = new store_credit($this->customer['id']);
							$to_customers_sc_currency_id = tep_get_customer_store_credit_currency($this->customer['id']);

							foreach ($sc_used_array as $sc_type => $credit_amount_array) {
								if ($credit_amount_array['amount'] > 0) {
									$from_sc_currency_id = $credit_amount_array['currency_id'];
									$from_sc_currency_code = $currencies->get_code_by_id($from_sc_currency_id);
									$to_sc_currency_code = $currencies->get_code_by_id($to_customers_sc_currency_id);

									$credit_amount = number_format($currencies->advance_currency_conversion($credit_amount_array['amount'], $from_sc_currency_code, $to_sc_currency_code, false, 'sell'), $currencies->currencies[$to_sc_currency_code]['decimal_places'], $currencies->currencies[$to_sc_currency_code]['decimal_point'], '');

									$trans_array = array('sc_type' => $sc_type, 'added_by' => 'system', 'added_by_role' => 'admin', 'type' => 'C', 'id' => $this->order_id, 'act_type' => LOG_SC_ACTIVITY_TYPE_CANCEL, 'add_amount' => $credit_amount, 'show_desc' => 0, 'currency_id' => $to_customers_sc_currency_id);
									$sc_reissue_result = $sc_object->miscellaneous_add_amount($trans_array);
									if (is_array($sc_reissue_result) && count($sc_reissue_result))	$result_array[] = $sc_reissue_result;
								}
							}
						}
					}
				}
				break;
			default:
				break;
		}

		return $error ? false : $result_array;
	}

	function get_sc_used() {
		$sc_used_info = array();

		$sc_select_sql = "	SELECT store_credit_account_type, store_credit_history_debit_amount, store_credit_history_currency_id
							FROM " . TABLE_STORE_CREDIT_HISTORY . "
							WHERE store_credit_history_trans_type='C'
								AND store_credit_history_trans_id='" . tep_db_input($this->order_id) . "'
								AND store_credit_activity_type='P'";
		$sc_result_sql = tep_db_query($sc_select_sql);
		while ($sc_row = tep_db_fetch_array($sc_result_sql)) {
			$sc_used_info[$sc_row['store_credit_account_type']] = array('currency_id' => $sc_row['store_credit_history_currency_id'], 'amount' => $sc_row['store_credit_history_debit_amount']);
		}

		return $sc_used_info;
	}

	function update_order_status($orders_status, $orders_status_history_data_sql_array, $deduct_stock_for_automated_payment = false) {
    	$orders_status_update_sql_data = array(	'orders_status' => tep_db_input($orders_status),
				 								'last_modified' => 'now()'
				 								);

		if (!isset($orders_status_history_data_sql_array['data']['orders_id']) || (int)$orders_status_history_data_sql_array['data']['orders_id']==0) $orders_status_history_data_sql_array['data']['orders_id'] = $this->order_id;

		if ($this->info['orders_aft_executed'] != '-1') {
			$orders_status_update_sql_data['orders_aft_executed'] = 0;
		}

		tep_db_perform(TABLE_ORDERS, $orders_status_update_sql_data, 'update', "orders_id = '" . (int)$this->order_id . "'");
		tep_update_orders_status_counter($orders_status_history_data_sql_array['data']);

		if ($orders_status_history_data_sql_array['action'] == 'insert') {
			$orders_status_history_data_sql_array['data']['date_added'] = 'now()';
			tep_db_perform(TABLE_ORDERS_STATUS_HISTORY, $orders_status_history_data_sql_array['data']);
		} else if ($orders_status_history_data_sql_array['action'] == 'update') {
			tep_db_perform(TABLE_ORDERS_STATUS_HISTORY, $orders_status_history_data_sql_array['data'], 'update', "orders_id = '" . (int)$this->order_id . "'");
		}

		if ($deduct_stock_for_automated_payment) {
			$this->get_product_order_info();
			tep_deduct_stock_for_automated_payment($this->order_id, $this->products, $this->info['orders_status_id'], $orders_status);
		}

		if ($orders_status == 7) { // order in verifying
			// call AFT Module query.
            $cid = isset($this->customer['id']) ? $this->customer['id'] : 0;

			$aft_obj = new anti_fraud($this->order_id);
			$aft_obj->getAftModule()->execute_stored_query_call($this->order_id, $cid);
			$aft_obj->execute_aft_script();
		}
    }

    function get_recommend_product($product_id_array) {
    	$product_res = array();

    	$other_products_id_select = "	SELECT DISTINCT op.products_id, COUNT(o.customers_id) AS total_purchase
										FROM " . TABLE_ORDERS . " AS o
											INNER JOIN " . TABLE_ORDERS_PRODUCTS . " AS op ON ( o.orders_id = op.orders_id )
										WHERE o.date_purchased > DATE_SUB(now() , INTERVAL 7 DAY)
											AND o.orders_status IN (2, 3)
											AND op.products_id NOT IN ('" . implode("','", $product_id_array) . "')
											AND o.customers_id <> '" . $this->customer['id'] . "'
										GROUP BY op.products_id, o.customers_id
										ORDER BY total_purchase DESC
										LIMIT 10";
		$other_products_id_result = tep_db_query($other_products_id_select);

		while($other_products_id_row = tep_db_fetch_array($other_products_id_result)){
			$product_res[$other_products_id_row['products_id']] = $other_products_id_row['total_purchase'];
		}

		return $product_res;
    }

    function verify_zero_amount_checkout () {
    	$zero_price_valid = false;
		for ($i=0, $n=sizeof($this->products); $i<$n; $i++) {
			$price_check_sql = "SELECT products_price
								FROM " . TABLE_PRODUCTS . "
								WHERE products_id = '". $this->products[$i]['id'] . "'";
			$price_check_result_sql = tep_db_query($price_check_sql);
			if ($price_check_row = tep_db_fetch_array($price_check_result_sql)) {
				if ($price_check_row['products_price'] > 0) {
					$zero_price_valid = false;
					break;
				} else {
					$zero_price_valid = true;
				}
			}
		}
		return $zero_price_valid;
    }

    function verify_zero_amount_checkout_by_products_id ($products_array) {
    	$zero_price_valid = false;
		for ($i=0, $n=sizeof($products_array); $i<$n; $i++) {
			$price_check_sql = "SELECT products_price
								FROM " . TABLE_PRODUCTS . "
								WHERE products_id = '". $products_array[$i]['id'] . "'";
			$price_check_result_sql = tep_db_query($price_check_sql);
			if ($price_check_row = tep_db_fetch_array($price_check_result_sql)) {
				if ($price_check_row['products_price'] > 0) {
					$zero_price_valid = false;
					break;
				} else {
					$zero_price_valid = true;
				}
			}
		}
		return $zero_price_valid;
    }

    function load_order_header ($customer_id) {
        global $sendto, $billto, $languages_id, $currency, $currencies, $shipping, $payment, $pm_2CO_cc_owner, $credit_card_owner;

		$customer_address_sql = "	SELECT 	c.customers_firstname, c.customers_lastname, c.customers_gender, c.customers_country_dialing_code_id, c.customers_telephone, c.customers_email_address, c.customers_groups_id,
											ab.entry_company, ab.entry_street_address, ab.entry_suburb, ab.entry_postcode, ab.entry_city, ab.entry_zone_id,
											z.zone_name,
											co.countries_id, co.countries_name, co.countries_iso_code_2, co.countries_iso_code_3, co.address_format_id, ab.entry_state
									FROM " . TABLE_CUSTOMERS . " AS c
									LEFT JOIN " . TABLE_ADDRESS_BOOK . " AS ab
										ON (c.customers_default_address_id = ab.address_book_id AND ab.customers_id = '" . (int)$customer_id . "')
									LEFT JOIN " . TABLE_ZONES . " AS z
										ON (ab.entry_zone_id = z.zone_id)
									LEFT JOIN " . TABLE_COUNTRIES . " AS co
										ON (ab.entry_country_id = co.countries_id)
									WHERE c.customers_id = '" . (int)$customer_id . "'";
      	$customer_address_query = tep_db_query($customer_address_sql);
      	$customer_address = tep_db_fetch_array($customer_address_query);

      	$telephone = tep_parse_telephone($customer_address['customers_telephone'], $customer_address['customers_country_dialing_code_id'], 'id');

      	$shipping_address_query = tep_db_query("select ab.entry_firstname, ab.entry_lastname, ab.entry_company, ab.entry_street_address, ab.entry_suburb, ab.entry_postcode, ab.entry_city, ab.entry_zone_id, z.zone_name, ab.entry_country_id, c.countries_id, c.countries_name, c.countries_iso_code_2, c.countries_iso_code_3, c.address_format_id, ab.entry_state from " . TABLE_ADDRESS_BOOK . " ab left join " . TABLE_ZONES . " z on (ab.entry_zone_id = z.zone_id) left join " . TABLE_COUNTRIES . " c on (ab.entry_country_id = c.countries_id) where ab.customers_id = '" . (int)$customer_id . "' and ab.address_book_id = '" . (int)$sendto . "'");
      	$shipping_address = tep_db_fetch_array($shipping_address_query);// Billing address always same as Profile since we just allow 1 address.

		$billto = $_SESSION['customer_default_address_id'];

      	$billing_address_query = tep_db_query("select ab.entry_firstname, ab.entry_lastname, ab.entry_company, ab.entry_street_address, ab.entry_suburb, ab.entry_postcode, ab.entry_city, ab.entry_zone_id, z.zone_name, ab.entry_country_id, c.countries_id, c.countries_name, c.countries_iso_code_2, c.countries_iso_code_3, c.address_format_id, ab.entry_state from " . TABLE_ADDRESS_BOOK . " ab left join " . TABLE_ZONES . " z on (ab.entry_zone_id = z.zone_id) left join " . TABLE_COUNTRIES . " c on (ab.entry_country_id = c.countries_id) where ab.customers_id = '" . (int)$customer_id . "' and ab.address_book_id = '" . (int)$billto . "'");
      	$billing_address = tep_db_fetch_array($billing_address_query);

		$customer_international_dialing_code_select_sql = "	SELECT c.countries_international_dialing_code, c.countries_name
															FROM " . TABLE_CUSTOMERS . " AS cust
															INNER JOIN " . TABLE_COUNTRIES . " AS c
																ON (cust.customers_country_dialing_code_id = c.countries_id)
															WHERE cust.customers_id = '" . (int)$customer_id . "'";
		$customer_international_dialing_code_query = tep_db_query($customer_international_dialing_code_select_sql);
		if ($customer_international_dialing_code_row = tep_db_fetch_array($customer_international_dialing_code_query)) {
			$country_dialing_code = $customer_international_dialing_code_row['countries_international_dialing_code'];
		} else {
			$country_dialing_code = 'NULL';
		}

      	$this->info = array('order_status' => DEFAULT_ORDERS_STATUS_ID,
                          	'currency' => $currency,
                          	'currency_value' => $currencies->get_value($currency, 'sell'),
                          	'payment_method' => $payment,
                          	'cc_type' => (isset($GLOBALS['cc_type']) ? $GLOBALS['cc_type'] : ''),
                          	'cc_owner' => (isset($GLOBALS['cc_owner']) ? $GLOBALS['cc_owner'] : ''),
                          	'cc_number' => (isset($GLOBALS['cc_number']) ? $GLOBALS['cc_number'] : ''),
                          	'cc_expires' => (isset($GLOBALS['cc_expires']) ? $GLOBALS['cc_expires'] : ''),
                          	'pm_2CO_cc_owner_firstname' => ($pm_2CO_cc_owner["firstname"] ? $pm_2CO_cc_owner["firstname"] : ''),
                          	'pm_2CO_cc_owner_lastname' => ($pm_2CO_cc_owner["lastname"] ? $pm_2CO_cc_owner["lastname"] : ''),
                          	'wp_owner_firstname' => ($credit_card_owner["wp"]["firstname"] ? $credit_card_owner["wp"]["firstname"] : ''),
                          	'wp_owner_lastname' => ($credit_card_owner["wp"]["lastname"] ? $credit_card_owner["wp"]["lastname"] : ''),
                          	'shipping_method' => $shipping['title'],
                          	'shipping_cost' => $shipping['cost'],
                          	'subtotal' => 0,
                          	'tax' => 0,
                          	'tax_groups' => array(),
                          	'comments' => (isset($GLOBALS['comments']) ? $GLOBALS['comments'] : ''));

      	$this->customer = array('firstname' => $customer_address['customers_firstname'],
                              	'lastname' => $customer_address['customers_lastname'],
                              	'gender' => $customer_address['customers_gender'],
                              	'company' => $customer_address['entry_company'],
                              	'street_address' => $customer_address['entry_street_address'],
                              	'suburb' => $customer_address['entry_suburb'],
                              	'city' => $customer_address['entry_city'],
                              	'postcode' => $customer_address['entry_postcode'],
                              	'state' => ((tep_not_null($customer_address['entry_state'])) ? $customer_address['entry_state'] : $customer_address['zone_name']),
                              	'zone_id' => $customer_address['entry_zone_id'],
                              	'country' => array('id' => $customer_address['countries_id'], 'title' => $customer_address['countries_name'], 'iso_code_2' => $customer_address['countries_iso_code_2'], 'iso_code_3' => $customer_address['countries_iso_code_3']),
                              	'format_id' => $customer_address['address_format_id'],
                              	'telephone_country_name' => ((tep_not_null($customer_international_dialing_code_row['countries_name'])) ? $customer_international_dialing_code_row['countries_name'] : 'NULL'),
                              	'int_dialing_code' => $country_dialing_code,
                              	'telephone' => $telephone,
                              	'email_address' => $customer_address['customers_email_address'],
                              	'customers_groups_id' => $customer_address['customers_groups_id']);

      	$this->delivery = array('firstname' => $shipping_address['entry_firstname'],
                              	'lastname' => $shipping_address['entry_lastname'],
                              	'company' => $shipping_address['entry_company'],
                              	'street_address' => $shipping_address['entry_street_address'],
                              	'suburb' => $shipping_address['entry_suburb'],
                              	'city' => $shipping_address['entry_city'],
                              	'postcode' => $shipping_address['entry_postcode'],
                              	'state' => ((tep_not_null($shipping_address['entry_state'])) ? $shipping_address['entry_state'] : $shipping_address['zone_name']),
                              	'zone_id' => $shipping_address['entry_zone_id'],
                              	'country' => array('id' => $shipping_address['countries_id'], 'title' => $shipping_address['countries_name'], 'iso_code_2' => $shipping_address['countries_iso_code_2'], 'iso_code_3' => $shipping_address['countries_iso_code_3']),
                              	'country_id' => $shipping_address['entry_country_id'],
                              	'format_id' => $shipping_address['address_format_id']);

		$this->billing = array(	'firstname' => $billing_address['entry_firstname'],
                             	'lastname' => $billing_address['entry_lastname'],
                             	'company' => $billing_address['entry_company'],
                             	'street_address' => $billing_address['entry_street_address'],
                             	'suburb' => $billing_address['entry_suburb'],
                             	'city' => $billing_address['entry_city'],
                             	'postcode' => $billing_address['entry_postcode'],
                             	'state' => ((tep_not_null($billing_address['entry_state'])) ? $billing_address['entry_state'] : $billing_address['zone_name']),
                             	'zone_id' => $billing_address['entry_zone_id'],
                             	'country' => array('id' => $billing_address['countries_id'], 'title' => $billing_address['countries_name'], 'iso_code_2' => $billing_address['countries_iso_code_2'], 'iso_code_3' => $billing_address['countries_iso_code_3']),
                             	'country_id' => $billing_address['entry_country_id'],
                             	'format_id' => $billing_address['address_format_id']);
    }

    function update_order_info () {
        global $cart_comments_obj, $payment, $credit_covers;

        if (is_object($cart_comments_obj)) {
			$this->info['comments_array'] = $cart_comments_obj->get_individual_plain_comments($GLOBALS['custom_comments'], 1);
		}

      	if (isset($GLOBALS[$payment]) && is_object($GLOBALS[$payment])) {
        	$this->info['payment_method'] = $GLOBALS[$payment]->title;

        	// added by wei chen, use this if the display title is different from payment method name
        	if ( isset($GLOBALS[$payment]->display_title) && $GLOBALS[$payment]->display_title != '' ) {
				$this->info['display_method'] = $GLOBALS[$payment]->display_title;
			}

        	if ( isset($GLOBALS[$payment]->order_status) && is_numeric($GLOBALS[$payment]->order_status) && ($GLOBALS[$payment]->order_status > 0) ) {
          		$this->info['order_status'] = $GLOBALS[$payment]->order_status;
        	}
      	} else {
      		if (!tep_not_null($this->info['payment_method'])) {	// If no payment method selected
      			if (tep_session_is_registered('credit_covers') && $credit_covers) {
		      		$this->info['order_status'] = 7;	// Fully covered by store credit
		      	}
      		}
      	}
    }

    function load_order_products ($products, $customer_id) {
        global $currencies, $currency;

        // Changes the database table because it involve basket table.

        $index = 0;
      	$pm_status = false;
      	$tax_country_id = '';
        $tax_zone_id = '';

        $cart_obj = new shoppingCart($reset_cart = false);

      	if(STORE_PROMOTION == 'true'){
  			$promotions_query = tep_db_query("select promotions_id, promotions_title, promotions_description, promotions_from, promotions_to, promotions_min_value from promotions where promotions_status = '1' order by promotions_id");
  		    $promotions = tep_db_fetch_array($promotions_query);
		    if($promotions['promotions_id']){
		    	$pm_status = true;	//on the promotions
			    //$pm_date_from = $promotions['promotions_from'];
			    //$pm_date_to = $promotions['promotions_to'];
			    $pm_value = $promotions['promotions_min_value'];
		    }
  		}

        // address_book_id is using billno == $_SESSION['customer_default_address_id'] and fixed as content_type == 'virtual'
        if (isset($_SESSION['customer_default_address_id'])) {
            $tax_address_query = tep_db_query(" SELECT ab.entry_country_id, ab.entry_zone_id
                                                FROM " . TABLE_ADDRESS_BOOK . " ab LEFT JOIN " . TABLE_ZONES . " z ON (ab.entry_zone_id = z.zone_id)
                                                WHERE ab.customers_id = '" . (int)$customer_id . "'
                                                    AND ab.address_book_id = '" . (int)$_SESSION['customer_default_address_id'] . "'"
            );
            if ($tax_address = tep_db_fetch_array($tax_address_query)) {
                $tax_country_id = $tax_address['entry_country_id'];
                $tax_zone_id = $tax_address['entry_zone_id'];
            }
        }

		for ($i=0, $n=sizeof($products); $i<$n; $i++) {
            $product_arr = $products[$i];
            $ori_id = (int)$product_arr['id'];
            $ori_pm_total = $product_arr['pm_total'];
            $ori_custom_content = $product_arr['custom_content'];
            $ori_quantity = $product_arr['quantity'];
            $ori_final_price = $product_arr['final_price'];
            $ori_name = $product_arr['name'];
            $ori_model = $product_arr['model'];
            $ori_tax_class_id = $product_arr['tax_class_id'];
            $ori_normal_price = $product_arr['normal_price'];
            $ori_price = $product_arr['price'];
            $ori_base_currency = $product_arr['base_currency'];
            $ori_discounts = $product_arr['discounts'];
            $ori_products_categories_id = (int)$product_arr['products_categories_id'];
            $ori_weight = $product_arr['weight'];
            $ori_pre_order = $product_arr['pre_order'];
            $ori_custom_products_type_id = (int)$product_arr['custom_products_type_id'];
            $ori_attributes = tep_not_empty($product_arr['attributes']) ? $product_arr['attributes'] : array();

      		if($ori_pm_total > $pm_value){
				//do nothing
      		} else {
      			$ori_pm_total = "";
      		}

	    	$currencies->product_instance_id = (isset($ori_custom_content['hla_account_id']) && tep_not_null($ori_custom_content['hla_account_id'])) ? $ori_custom_content['hla_account_id'] : '';

      		if ($ori_pm_total == "FREE") {
      			$pm_price = ($ori_quantity - 1) * $ori_final_price;

      			$this->products[$index] = array(
                    'qty' => $ori_quantity,
                    'name' => $ori_name." <font color=orange>(~1 Free Items - worth " . $currencies->format($ori_final_price) . ")</font>",
                    'model' => $ori_model,
                    'tax' => tep_get_tax_rate($ori_tax_class_id, $tax_country_id, $tax_zone_id),
                    'tax_description' => tep_get_tax_description($ori_tax_class_id, $tax_country_id, $tax_zone_id),
                    'normal_price' => $ori_normal_price,
                    'price' => $ori_price,
                    'final_price' => $ori_price + $cart_obj->get_attributes_price($ori_id, $ori_attributes, $customer_id),
                    'base_currency' => $ori_base_currency,
                    'discounts' => $ori_discounts,
                    'products_categories_id' => $ori_products_categories_id,
                    'pm_price' => 'FREE',
                    'weight' => $ori_weight,
                    'id' => $ori_id,
                    'pre_order' => $ori_pre_order,
                    'custom_products_type_id' => $ori_custom_products_type_id,
                    'custom_products_type_name' => $this->get_custom_products_type_name($ori_custom_products_type_id),
                    'custom_content' => $ori_custom_content
                );

               	$this->products[$index]['storage_price'] = array(
                    'normal_price' => $currencies->display_noformat_price_nodiscount($this->products[$i]['id'], $this->products[$i]['normal_price']) / $currencies->get_value($currency, 'sell'),
               		'price' => $currencies->display_noformat_price_nodiscount($this->products[$i]['id'], $this->products[$i]['price'], $this->products[$i]['tax'], 1, $this->products[$i]['discounts']) / $currencies->get_value($currency, 'sell'),
               		'final_price' => $currencies->display_noformat_price_nodiscount($this->products[$i]['id'], $this->products[$i]['final_price'], $this->products[$i]['tax'], 1, $this->products[$i]['discounts']) / $currencies->get_value($currency, 'sell')
                );

				$product_query = tep_db_query("select products_bundle, products_bundle_dynamic from " . TABLE_PRODUCTS . " where products_id = '" . $ori_id . "'");
				$product = tep_db_fetch_array($product_query);
				if ($product['products_bundle_dynamic']=="yes") {
					$b_index = 0;
					$product_bundle_dynamic_select_sql = "	SELECT cb.subproducts_id, cb.subproducts_price, cb.subproducts_quantity, p.custom_products_type_id, p.products_model
															FROM " . TABLE_CUSTOMERS_BASKET_BUNDLE . " AS cb
															LEFT JOIN " . TABLE_PRODUCTS . " AS p
    															ON cb.subproducts_id = p.products_id
															WHERE cb.customers_id = '" . (int)$customer_id . "'
																AND cb.products_bundle_id = '" . $ori_id . "'
																AND p.products_status=1
															ORDER BY cb.subproducts_id";
					$product_bundle_dynamic_result_sql = tep_db_query($product_bundle_dynamic_select_sql);
					while ($product_bundle = tep_db_fetch_array($product_bundle_dynamic_result_sql)) {
						$products_name = tep_get_products_name($product_bundle['subproducts_id']);

						$this->products[$index]['bundle'][$b_index] = array(
                            'qty' => $product_bundle['subproducts_quantity'],
                            'name' => $products_name,
                            'model' => $product_bundle['products_model'],
                            'tax' => tep_get_tax_rate($ori_tax_class_id, $tax_country_id, $tax_zone_id),
                            'tax_description' => tep_get_tax_description($ori_tax_class_id, $tax_country_id, $tax_zone_id),
                            'normal_price' => $product_bundle['subproducts_price'],
                            'price' => $product_bundle['subproducts_price'],
                            'final_price' => $product_bundle['subproducts_price'],
                            'products_categories_id' => $ori_products_categories_id,
                            'pm_price' => '',
                            'weight' => '',
                            'id' => $product_bundle['subproducts_id']
                        );

		                $this->products[$index]['bundle'][$b_index]['storage_price'] = array(
                            'normal_price' => $currencies->display_noformat_price_nodiscount($product_bundle['subproducts_id'], $product_bundle['subproducts_price']) / $currencies->get_value($currency, 'sell'),
                            'price' => $currencies->display_noformat_price_nodiscount($product_bundle['subproducts_id'], $product_bundle['subproducts_price'], '', 1, '') / $currencies->get_value($currency, 'sell'),
                            'final_price' => $currencies->display_noformat_price_nodiscount($product_bundle['subproducts_id'], $product_bundle['subproducts_price'], '', 1, '') / $currencies->get_value($currency, 'sell')
                        );

						if (!isset($this->products[$index]['custom_products_type_id'])) {
						 	$this->products[$index]['custom_products_type_id'] = (int)$product_bundle['custom_products_type_id'];
							$this->products[$index]['custom_products_type_name'] = $this->get_custom_products_type_name((int)$product_bundle['custom_products_type_id']);
						}



						$b_index++;
					}
				} else if ($product['products_bundle']=="yes") {
					$s_index = 0;
					$static_bundle_select_sql = "	SELECT pb.subproduct_id, pb.subproduct_qty, p.custom_products_type_id, p.products_price, p.products_model
										 			FROM ". TABLE_PRODUCTS_BUNDLES . " AS pb
										 			LEFT JOIN " . TABLE_PRODUCTS . " AS p
										 				ON pb.subproduct_id=p.products_id
													WHERE pb.bundle_id='" . $ori_id . "'
														AND p.products_status=1";
					$static_bundle_result_sql = tep_db_query($static_bundle_select_sql);
					while ($static_bundle = tep_db_fetch_array($static_bundle_result_sql)) {
						$products_name = tep_get_products_name($static_bundle['subproduct_id']);

						$this->products[$index]['static'][$s_index] = array(
                            'qty' => $static_bundle['subproduct_qty'],
                            'name' => $products_name,
                            'model' => $static_bundle['products_model'],
                            'tax' => tep_get_tax_rate($ori_tax_class_id, $tax_country_id, $tax_zone_id),
                            'tax_description' => tep_get_tax_description($ori_tax_class_id, $tax_country_id, $tax_zone_id),
                            'normal_price' => $static_bundle['products_price'],
                            'price' => '0.00',
                            'final_price' => '0.00',
                            'products_categories_id' => $ori_products_categories_id,
                            'pm_price' => '',
                            'weight' => '',
                            'id' => $static_bundle['subproduct_id']
                        );

		                $this->products[$index]['static'][$s_index]['storage_price'] = array(
                            'normal_price' => $currencies->display_noformat_price_nodiscount($static_bundle['subproduct_id'], $static_bundle['products_price']) / $currencies->get_value($currency, 'sell'),
                            'price' => $currencies->display_noformat_price_nodiscount($static_bundle['subproduct_id'], $static_bundle['products_price'], '', 1, '') / $currencies->get_value($currency, 'sell'),
                            'final_price' => $currencies->display_noformat_price_nodiscount($static_bundle['subproduct_id'], $static_bundle['products_price'], '', 1, '') / $currencies->get_value($currency, 'sell')
                        );

						if (!isset($this->products[$index]['custom_products_type_id'])) {
						 	$this->products[$index]['custom_products_type_id'] = (int)$static_bundle['custom_products_type_id'];
							$this->products[$index]['custom_products_type_name'] = $this->get_custom_products_type_name((int)$static_bundle['custom_products_type_id']);
						}

						$s_index++;
					}
				} else {
					$this->products[$index]['custom_products_type_id'] = $ori_custom_products_type_id;
					$this->products[$index]['custom_products_type_name'] = $this->get_custom_products_type_name($ori_custom_products_type_id);
				}
      		} else {
        		$this->products[$index] = array(
                    'qty' => $ori_quantity,
                    'name' => $ori_name,
                    'model' => $ori_model,
                    'tax' => tep_get_tax_rate($ori_tax_class_id, $tax_country_id, $tax_zone_id),
                    'tax_description' => tep_get_tax_description($ori_tax_class_id, $tax_country_id, $tax_zone_id),
                    'normal_price' => $ori_normal_price,
                    'price' => $ori_price,
                    'final_price' => $ori_price + $cart_obj->get_attributes_price($ori_id, $ori_attributes, $customer_id),
                    'base_currency' => $ori_base_currency,
                    'discounts' => $ori_discounts,
                    'products_categories_id' => $ori_products_categories_id,
                    'pm_price' => '',
                    'weight' => $ori_weight,
                    'id' => $ori_id,
                    'pre_order' => $ori_pre_order,
                    'custom_content' => $ori_custom_content
                );

				$this->products[$index]['storage_price'] = array(
                    'normal_price' => $currencies->display_noformat_price_nodiscount($this->products[$i]['id'], $this->products[$i]['normal_price']) / $currencies->get_value($currency, 'sell'),
                    'price' => $currencies->display_noformat_price_nodiscount($this->products[$i]['id'], $this->products[$i]['price'], $this->products[$i]['tax'], 1, $this->products[$i]['discounts']) / $currencies->get_value($currency, 'sell'),
                    'final_price' => $currencies->display_noformat_price_nodiscount($this->products[$i]['id'], $this->products[$i]['final_price'], $this->products[$i]['tax'], 1, $this->products[$i]['discounts']) / $currencies->get_value($currency, 'sell')
                );

                $product_query = tep_db_query("select products_bundle, products_bundle_dynamic from " . TABLE_PRODUCTS . " where products_id = '" . $ori_id . "'");
				$product = tep_db_fetch_array($product_query);
				if ($product['products_bundle_dynamic']=="yes") {
					$b_index = 0;
					$product_bundle_dynamic_select_sql = "	SELECT cb.subproducts_id, cb.subproducts_price, cb.subproducts_quantity, p.custom_products_type_id, p.products_model
															FROM " . TABLE_CUSTOMERS_BASKET_BUNDLE . " AS cb
															LEFT JOIN " . TABLE_PRODUCTS . " AS p
    															ON cb.subproducts_id = p.products_id
															WHERE cb.customers_id = '" . (int)$customer_id . "'
																AND cb.products_bundle_id = '" . $ori_id . "'
																AND p.products_status=1
															ORDER BY cb.subproducts_id";
					$product_bundle_dynamic_result_sql = tep_db_query($product_bundle_dynamic_select_sql);
					while ($product_bundle = tep_db_fetch_array($product_bundle_dynamic_result_sql)) {
						$products_name = tep_get_products_name($product_bundle['subproducts_id']);

						$this->products[$index]['bundle'][$b_index] = array(
                            'qty' => $product_bundle['subproducts_quantity'],
                            'name' => $products_name,
                            'model' => $product_bundle['products_model'],
                            'tax' => tep_get_tax_rate($ori_tax_class_id, $tax_country_id, $tax_zone_id),
                            'tax_description' => tep_get_tax_description($ori_tax_class_id, $tax_country_id, $tax_zone_id),
                            'normal_price' => $product_bundle['subproducts_price'],
                            'price' => $product_bundle['subproducts_price'],
                            'final_price' => $product_bundle['subproducts_price'],
                            'products_categories_id' => $ori_products_categories_id,
                            'pm_price' => '',
                            'weight' => '',
                            'id' => $product_bundle['subproducts_id']
                        );

		                $this->products[$index]['bundle'][$b_index]['storage_price'] = array(
                            'normal_price' => $currencies->display_noformat_price_nodiscount($product_bundle['subproducts_id'], $product_bundle['subproducts_price']) / $currencies->get_value($currency, 'sell'),
                            'price' => $currencies->display_noformat_price_nodiscount($product_bundle['subproducts_id'], $product_bundle['subproducts_price'], tep_get_tax_rate($ori_tax_class_id, $tax_country_id, $tax_zone_id), 1) / $currencies->get_value($currency, 'sell'),
                            'final_price' => $currencies->display_noformat_price_nodiscount($product_bundle['subproducts_id'], $product_bundle['subproducts_price'], tep_get_tax_rate($ori_tax_class_id, $tax_country_id, $tax_zone_id), 1) / $currencies->get_value($currency, 'sell')
                        );

						if (!isset($this->products[$index]['custom_products_type_id'])) {
						 	$this->products[$index]['custom_products_type_id'] = (int)$product_bundle['custom_products_type_id'];
							$this->products[$index]['custom_products_type_name'] = $this->get_custom_products_type_name((int)$product_bundle['custom_products_type_id']);
						}

						$b_index++;
					}
				} else if ($product['products_bundle']=="yes") {
					$s_index = 0;
					$static_bundle_select_sql = "	SELECT pb.subproduct_id, pb.subproduct_qty, p.custom_products_type_id, p.products_price, p.products_model
										 			FROM ". TABLE_PRODUCTS_BUNDLES . " AS pb
										 			LEFT JOIN " . TABLE_PRODUCTS . " AS p
										 				ON pb.subproduct_id=p.products_id
													WHERE pb.bundle_id='" . $ori_id . "'
														AND p.products_status=1" ;
					$static_bundle_result_sql = tep_db_query($static_bundle_select_sql);
					while ($static_bundle = tep_db_fetch_array($static_bundle_result_sql)) {
						$products_name = tep_get_products_name($static_bundle['subproduct_id']);

						$this->products[$index]['static'][$s_index] = array(
                            'qty' => $static_bundle['subproduct_qty'],
                            'name' => $products_name,
                            'model' => $static_bundle['products_model'],
                            'tax' => tep_get_tax_rate($ori_tax_class_id, $tax_country_id, $tax_zone_id),
                            'tax_description' => tep_get_tax_description($ori_tax_class_id, $tax_country_id, $tax_zone_id),
                            'normal_price' => $static_bundle['products_price'],
                            'price' => '0.00',
                            'final_price' => '0.00',
                            'products_categories_id' => $ori_products_categories_id,
                            'pm_price' => '',
                            'weight' => '',
                            'id' => $static_bundle['subproduct_id']
                        );

						$this->products[$index]['static'][$s_index]['storage_price'] = array(
                            'normal_price' => $currencies->display_noformat_price_nodiscount($static_bundle['subproduct_id'], $static_bundle['products_price']) / $currencies->get_value($currency, 'sell'),
                            'price' => $currencies->display_noformat_price_nodiscount($static_bundle['subproduct_id'], $static_bundle['products_price']) / $currencies->get_value($currency, 'sell'),
                            'final_price' => $currencies->display_noformat_price_nodiscount($static_bundle['subproduct_id'], $static_bundle['products_price']) / $currencies->get_value($currency, 'sell')
                        );

						if (!isset($this->products[$index]['custom_products_type_id'])) {
						 	$this->products[$index]['custom_products_type_id'] = (int)$static_bundle['custom_products_type_id'];
							$this->products[$index]['custom_products_type_name'] = $this->get_custom_products_type_name((int)$static_bundle['custom_products_type_id']);
						}

						$s_index++;
					}
				} else {
					$this->products[$index]['custom_products_type_id'] = $ori_custom_products_type_id;
					$this->products[$index]['custom_products_type_name'] = $this->get_custom_products_type_name($ori_custom_products_type_id);
				}
			}

			// ********** Begin Separate Price per Customer Mod **************
			$customer_group_id_query = tep_db_query("select customers_group_id from " . TABLE_CUSTOMERS . " where customers_id = '". $customer_id . "'");
			$customer_group_id = tep_db_fetch_array($customer_group_id_query);
  			if ($customer_group_id['customers_group_id'] != '0'){
  				$orders_customers_price = tep_db_query("select customers_group_price from " . TABLE_PRODUCTS_GROUPS . " where customers_group_id = '". $customer_group_id['customers_group_id'] . "' and products_id = '" . $ori_id . "'");
  				$orders_customers = tep_db_fetch_array($orders_customers_price);
    			if ($orders_customers = tep_db_fetch_array($orders_customers_price)) {
    				$this->products[$index] = array(
                        'price' => $orders_customers['customers_group_price'],
                        'final_price' => $orders_customers['customers_group_price'] + $cart_obj->get_attributes_price($ori_id, $ori_attributes, $customer_id)
                    );
    			}
  			}
			// ********** End Separate Price per Customer Mod **************
        	if ($ori_attributes) {
          		$subindex = 0;
          		reset($ori_attributes);
          		while (list($option, $value) = each($ori_attributes)) {
            		$attributes_query = tep_db_query("select popt.products_options_name, poval.products_options_values_name, pa.options_values_price, pa.price_prefix from " . TABLE_PRODUCTS_OPTIONS . " popt, " . TABLE_PRODUCTS_OPTIONS_VALUES . " poval, " . TABLE_PRODUCTS_ATTRIBUTES . " pa where pa.products_id = '" . $ori_id . "' and pa.options_id = '" . (int)$option . "' and pa.options_id = popt.products_options_id and pa.options_values_id = '" . (int)$value . "' and pa.options_values_id = poval.products_options_values_id and popt.language_id = '" . (int)$languages_id . "' and poval.language_id = '" . (int)$languages_id . "'");
            		$attributes = tep_db_fetch_array($attributes_query);

            		$this->products[$index]['attributes'][$subindex] = array(
                        'option' => $attributes['products_options_name'],
                        'value' => $attributes['products_options_values_name'],
                        'option_id' => $option,
                        'value_id' => $value,
                        'prefix' => $attributes['price_prefix'],
                        'price' => $attributes['options_values_price']
                    );
            		$subindex++;
          		}
        	}
			//promotion
        	if ($this->products[$index]['pm_price'] == "FREE"){
        		$qty = $this->products[$index]['qty'] - 1;
        	} else {
        		$qty = $this->products[$index]['qty'];
        	}

        	if ($is_sc) {
        		$shown_price = $this->products[$index]['price'] * $qty;
        	} else {
        		$shown_price = $currencies->display_noformat_price_nodiscount($this->products[$index]['id'], $this->products[$index]['price'], $this->products[$index]['tax'], $qty, $this->products[$index]['discounts']);
			}
			$shown_price = $shown_price / $currencies->get_value($currency, 'sell');	// MUST convert to DEFAULT_CURRENCY value

        	$this->info['subtotal'] += $shown_price;

        	$products_tax = $this->products[$index]['tax'];
        	$products_tax_description = $this->products[$index]['tax_description'];
        	if (DISPLAY_PRICE_WITH_TAX == 'true') {
          		$this->info['tax'] += $shown_price - ($shown_price / (($products_tax < 10) ? "1.0" . str_replace('.', '', $products_tax) : "1." . str_replace('.', '', $products_tax)));
          		if (isset($this->info['tax_groups'][$products_tax_description])) {
            		$this->info['tax_groups'][$products_tax_description] += $shown_price - ($shown_price / (($products_tax < 10) ? "1.0" . str_replace('.', '', $products_tax) : "1." . str_replace('.', '', $products_tax)));
          		} else {
            		$this->info['tax_groups'][$products_tax_description] = $shown_price - ($shown_price / (($products_tax < 10) ? "1.0" . str_replace('.', '', $products_tax) : "1." . str_replace('.', '', $products_tax)));
          		}
        	} else {
          		$this->info['tax'] += ($products_tax / 100) * $shown_price;
          		if (isset($this->info['tax_groups'][$products_tax_description])) {
            		$this->info['tax_groups'][$products_tax_description] += ($products_tax / 100) * $shown_price;
          		} else {
            		$this->info['tax_groups'][$products_tax_description] = ($products_tax / 100) * $shown_price;
          		}
        	}
			$index++;
		}

      	if (DISPLAY_PRICE_WITH_TAX == 'true') {
        	$this->info['total'] = $this->info['subtotal'] + $this->info['shipping_cost'];
      	} else {
        	$this->info['total'] = $this->info['subtotal'] + $this->info['tax'] + $this->info['shipping_cost'];
      	}

      	if ($this->info['subtotal'] <= 0) { // All product are free
			if ($this->verify_zero_amount_checkout_by_products_id($products)) {
      			$this->info['order_status'] = 7;
      		} else {
      			$this->info['order_status'] = 1;
      		}
      	}
    }
}
?>