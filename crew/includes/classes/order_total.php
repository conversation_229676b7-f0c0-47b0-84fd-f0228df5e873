<?php

/*
  $Id: order_total.php,v 1.44 2015/01/08 03:02:46 weesiong Exp $
  orig : order_total.php,v 1.4 2003/02/11 00:04:53 hpdl Exp $

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2003 osCommerce

  Released under the GNU General Public License
 */

class order_total {

    var $modules;
    var $store_credit_balance_html;
    var $store_credit_remain_html;

    // class constructor
    function order_total() {
        global $language;

        if (defined('MODULE_ORDER_TOTAL_INSTALLED') && tep_not_null(MODULE_ORDER_TOTAL_INSTALLED)) {
            $this->modules = explode(';', MODULE_ORDER_TOTAL_INSTALLED);

            reset($this->modules);
            while (list(, $value) = each($this->modules)) {
                include_once(DIR_WS_LANGUAGES . $language . '/modules/order_total/' . $value);
                include_once(DIR_WS_MODULES . 'order_total/' . $value);

                $class = substr($value, 0, strrpos($value, '.'));
                $GLOBALS[$class] = new $class;
            }
        }
    }

    function process($is_sc = false) {
        $order_total_array = array();
        if (is_array($this->modules)) {
            reset($this->modules);
            while (list(, $value) = each($this->modules)) {
                $class = substr($value, 0, strrpos($value, '.'));
                if ($GLOBALS[$class]->enabled) {
                    if ($is_sc && method_exists($GLOBALS[$class], 'sc_process')) {
                        $GLOBALS[$class]->sc_process();
                    } else {
                        $GLOBALS[$class]->process();
                    }

                    for ($i = 0, $n = sizeof($GLOBALS[$class]->output); $i < $n; $i++) {
                        if (tep_not_null($GLOBALS[$class]->output[$i]['title']) && tep_not_null($GLOBALS[$class]->output[$i]['text'])) {
                            $order_total_array[] = array('code' => $GLOBALS[$class]->code,
                                'title' => $GLOBALS[$class]->output[$i]['title'],
                                'text' => $GLOBALS[$class]->output[$i]['text'],
                                'value' => $GLOBALS[$class]->output[$i]['value'],
                                'sort_order' => $GLOBALS[$class]->sort_order);
                        }
                    }
                }
            }
        }

        return $order_total_array;
    }

    function output($custom_info = '', $total_op = 0, $total_extra_op = 0, $display_type = 'standard') {

        $output_string_array = array();

        if (is_array($this->modules)) {
            reset($this->modules);

            require_once(DIR_WS_CLASSES . 'page.php');
            $page_obj = new page();

            if ($display_type == 'standard') {
                while (list(, $value) = each($this->modules)) {
                    $class = substr($value, 0, strrpos($value, '.'));
                    if ($GLOBALS[$class]->enabled) {
                        if ($class != 'ot_coupon') { // GST :: hide Discount Coupon redemption amount
                            $size = sizeof($GLOBALS[$class]->output);
                            $subtotal_display = false;

                            for ($i = 0; $i < $size; $i++) {
                                $amt_display = $GLOBALS[$class]->output[$i]['text'];

                                if (isset($GLOBALS[$class]->output[$i]['display_text'])) {
                                    $amt_display = $GLOBALS[$class]->output[$i]['display_text'];
                                }

                                if ($GLOBALS[$class]->output[$i]['title'] == MODULE_ORDER_TOTAL_SUBTOTAL_TITLE . ':') {
                                    $subtotal_display = true;
                                }

                                $output_string = '	<tr>';

                                if ($subtotal_display) {
                                    $output_string .= '	<td valign="top" style="background-color: #FFFFFF;border-top:1px solid #cccccc;border-bottom:1px solid #cccccc;padding:10px 0px 0px 15px;height:35px;" width="242px">';
                                } else {
                                    $output_string .= '	<td valign="top" class="paymentPriceTitle" style="padding:10px 0px 0px 15px;height:35px;margin:5px 0px;" width="240px">';
                                }

                                if ($class == 'ot_total') {
                                    $output_string .= '<a href="' . tep_href_link(FILENAME_SHOPPING_CART) . '" style="text-decoration:underline;">' . LINK_EDIT_SHOPPING_CART . '</a>';
                                    //$output_string .= tep_image_button2('gray_short', FILENAME_SHOPPING_CART, '<span class="left">' .  . '</span>');
                                }
                                if (isset($GLOBALS[$class]->output[$i]['display_title'])) {
                                    $output_string .= $GLOBALS[$class]->output[$i]['display_title'];
                                } else {
                                    $output_string .= $GLOBALS[$class]->output[$i]['title'];
                                }

                                $output_string .= '		</td>';

                                if ($GLOBALS[$class]->output[$i]['display_title'] == '##CUSTOM_OT_PM_NAME##') {
                                    $output_string .= '	<td valign="top" id="sum_payment" class="paymentPriceValue" style="text-align:right;padding:10px 0px 0px 6px;height:35px;width:229px;" nowrap>
                                                            <div style="width:205px;padding-right:15px;">';
                                } else {
                                    if ($subtotal_display) {
                                        $output_string .= '	<td valign="top" class="paymentPriceValue"  style="background-color: #FFFFFF;text-align:right;border-top:1px solid #cccccc;border-bottom:1px solid #cccccc;padding:10px 30px 0px 6px;">';
                                    } else {
                                        $output_string .= '	<td valign="top" class="paymentPriceValue" style="text-align:right;padding:10px 30px 0px 6px;">';
                                    }
                                }
                                $output_string .= '				<table border="0" width="100%"">
                                                                    <tr>
                                                                        <td style="text-align:right;">';
                                if ($class == 'ot_total') {
                                    //$output_string .= tep_image_button2('gray_box', 'javascript:void(0);', $amt_display, 200) . "<br>";
                                    $output_string .= '<div>' . $page_obj->get_html_simple_rc_box('', $amt_display, 43) . '</div>';

                                    if ($GLOBALS[$class]->output[$i]['display_title'] == '##CUSTOM_OT_PM_NAME##') {
                                        $output_string .= tep_draw_separator('pixel_trans.gif', '100%', '3');
                                        $output_string .= '<div>' . $page_obj->get_html_simple_rc_box('', '<table><tr><td style="width:65px;padding-left: 10px;">' . ENTRY_REBATE . '</td><td>:</td><td>' . tep_display_op('<span class="price"><b><font style="color:#005599;">' . $total_op . '</font></b></span>') . '</td></tr></table>', 43) . '</div>';
                                        if ($total_extra_op > 0) {
                                            $output_string .= tep_draw_separator('pixel_trans.gif', '100%', '3');
                                            $output_string .= '<div>' . $page_obj->get_html_simple_rc_box('', '<table><tr><td style="width:65px;padding-left: 10px;">' . ENTRY_ADDED_BONUS . '</td><td valign="top">:</td><td valign="top">' . tep_display_op('<span class="price"><b><font style="color:#005599;">' . $total_extra_op . ' ' . '</font></b></span>') . '</td></tr></table>', 43) . '</div>';
                                        }
                                    }
                                } else {
                                    $output_string .= $amt_display;
                                }

                                $output_string .= '						</td>
                                                                    </tr>
                                                                </table>';
                                if ($GLOBALS[$class]->output[$i]['display_title'] == '##CUSTOM_OT_PM_NAME##') {
                                    $output_string .= '		</div>';
                                }
                                $output_string .= '			</td>
                                                        </tr>';

                                if ($subtotal_display) {
                                    ;
                                } else if ($class != 'ot_total') {
                                    $output_string .= '	<tr>
                                                            <td colspan="2">
                                                                <div class="row_separator" style="margin:5px 0px">
                                                                    <table border="0" width="100%" cellspacing="0" cellpadding="0">
                                                                        <tr><td></td></tr>
                                                                    </table>
                                                                </div>
                                                            </td>
                                                        </tr>';
                                }

                                $output_string_array[] = $output_string;
                            }
                        } else { // GST :: re-locate Discount Coupon redemption form
                            $amt_display = '';

                            if (tep_not_null($GLOBALS[$class]->output)) {
                                $amt_display = tep_not_null($GLOBALS[$class]->output[0]['display_text']) ? $GLOBALS[$class]->output[0]['display_text'] : $GLOBALS[$class]->output[0]['text'];
                            }

                            $output_string = $GLOBALS[$class]->credit_selection($amt_display);
                            if (tep_not_null($output_string)) {
                                $output_string .= '	<tr>
                                                    <td colspan="2">
                                                        <div class="row_separator" style="margin-bottom: 5px;">
                                                            <table border="0" width="100%" cellspacing="0" cellpadding="0">
                                                                <tr><td></td></tr>
                                                            </table>
                                                        </div>
                                                    </td>
                                                </tr>';
                            }
                            $output_string_array[] = $output_string;
                        }
                    }
                }
            } else if ($display_type == 'basic') {
                if (isset($GLOBALS['ot_gv']) && $GLOBALS['ot_gv']->enabled) {
                    $sc_balance = $GLOBALS['ot_gv']->use_credit_amount();
                    $sc_balance = tep_not_empty($sc_balance) ? $sc_balance : '-';
                    $sc_remaining = $GLOBALS['ot_gv']->store_credit_remain_formatted;

                    $this->store_credit_balance_html = '<tr class="sc_balance"><td class="lbl"><span class="hds3">' . MOBULE_ORDER_TOTAL_STORE_CREDIT_BALANCE . '</span></td><td class="paymentPriceValue">' . $sc_balance . '</td></tr>';
                    $this->store_credit_remain_html = '<tr class="sc_remain"><td class="lbl">' . MOBULE_ORDER_TOTAL_STORE_CREDIT_RAMAINING . '</td><td class="paymentPriceValue">' . $sc_remaining . '</td></tr>';
                } else {
                    $this->store_credit_balance_html = '-';
                    $this->store_credit_remain_html = '-';
                }

                while (list(, $value) = each($this->modules)) {
                    $class = substr($value, 0, strrpos($value, '.'));
                    if ($GLOBALS[$class]->enabled) {
                        if ($class != 'ot_coupon') { // GST :: hide Discount Coupon redemption amount
                            $size = sizeof($GLOBALS[$class]->output);

                            for ($i = 0; $i < $size; $i++) {
                                $amt_display = $GLOBALS[$class]->output[$i]['text'];

                                if ($class == 'ot_total') {
                                    $output_string = '<tr class="total"><td class="lbl">' . TEXT_TOTAL_PAY . ':</td><td class="paymentPriceValue">' . $amt_display . '</td></tr>';

                                    if ($GLOBALS[$class]->output[$i]['display_title'] == '##CUSTOM_OT_PM_NAME##') {
                                        $output_string .= '<tr class="rebate"><td class="lbl">' . ENTRY_REBATE . ':</td><td class="paymentPriceValue">' . tep_display_op($total_op) . '</td></tr>';
                                        if ($total_extra_op > 0) {
                                            $output_string .= '<tr class="rebate"><td class="lbl">' . ENTRY_ADDED_BONUS . '</td><td class="paymentPriceValue">' . tep_display_op($total_extra_op) . '</td></tr>';
                                        }
                                    }
                                } else {
                                    if (isset($GLOBALS[$class]->output[$i]['display_text'])) {
                                        $amt_display = $GLOBALS[$class]->output[$i]['display_text'];
                                    }

                                    // Start Label
                                    $output_string = '<tr class="general"><td class="lbl">';
                                    if (isset($GLOBALS[$class]->output[$i]['simple_display_title'])) {
                                        $output_string .= $GLOBALS[$class]->output[$i]['simple_display_title'];
                                    } else if ($GLOBALS[$class]->output[$i]['display_title'] == '##CUSTOM_OT_PM_NAME##') {
                                        
                                    } else if (isset($GLOBALS[$class]->output[$i]['display_title'])) {
                                        $output_string .= $GLOBALS[$class]->output[$i]['display_title'];
                                    } else {
                                        $output_string .= $GLOBALS[$class]->output[$i]['title'];
                                    }
                                    $output_string .= '</td>';
                                    // End Label
                                    // Start Amount
                                    if ($GLOBALS[$class]->output[$i]['display_title'] == '##CUSTOM_OT_PM_NAME##') {
                                        $output_string .= '	<td id="sum_payment" class="paymentPriceValue" nowrap>';
                                    } else {
                                        $output_string .= '	<td class="paymentPriceValue">';
                                    }
                                    $output_string .= $amt_display;
                                    $output_string .= '</td></tr>';
                                }

                                $output_string_array[] = $output_string;
                            }
                        } else { // GST :: re-locate Discount Coupon redemption form
                            $amt_display = '';

                            if (tep_not_null($GLOBALS[$class]->output)) {
                                $amt_display = tep_not_null($GLOBALS[$class]->output[0]['display_text']) ? $GLOBALS[$class]->output[0]['display_text'] : $GLOBALS[$class]->output[0]['text'];
                            }

                            $output_string = $GLOBALS[$class]->credit_selection($amt_display, $display_type);
                            $output_string_array[] = $output_string;
                        }
                    }
                }
            }
            $output_string_array[] = '<script>
            jQuery(function() {
                jQuery(".wTip").tipTip({maxWidth: "270px", edgeOffset: 0, defaultPosition: "left", keepAlive: true});
                jQuery("body").click(function(){
                    jQuery("#tiptip_holder").hide();
                });
            });
            </script>  ';

            if (isset($custom_info['selection']) && $custom_info['selection']) {
                $output_string_array[count($output_string_array)] = end($output_string_array);
                $output_string_array[count($output_string_array) - 2] = $this->credit_selection();
            }
        }

        $output_string = implode('', $output_string_array);

        if (!isset($custom_info['pm_name']) || !$custom_info['pm_name']) {
            $output_string = preg_replace("/(##CUSTOM_)(.*?)(##)/is", tep_draw_separator('blank.gif', '160', '1'), $output_string);
        }

        return $output_string;
    }

    // ICW ORDER TOTAL CREDIT CLASS/GV SYSTEM - START ADDITION
    //
	// This function is called in checkout payment after display of payment methods. It actually calls
    // two credit class functions.
    //
	// use_credit_amount() is normally a checkbox used to decide whether the credit amount should be applied to reduce
    // the order total. Whether this is a Gift Voucher, or discount coupon or reward points etc.
    //
	// The second function called is credit_selection(). This in the credit classes already made is usually a redeem box.
    // for entering a Gift Voucher number. Note credit classes can decide whether this part is displayed depending on
    // E.g. a setting in the admin section.
    //
	function credit_selection() {
        $selection_string = '';
        $close_string = '';
        $credit_class_string = '';

        if (MODULE_ORDER_TOTAL_INSTALLED) {
            $header_string = '	<tr>
        							<td>
        								<table border="0" width="100%" cellspacing="0" cellpadding="2">
	        								<tr>
	        									<td class="creditBoxHeading">' . TABLE_HEADING_CREDIT . '</td>
	        								</tr>
	        							</table>
	        						</td>
	        					</tr>
	        					<tr>
	        						<td>
	        							<table border="0" width="100%" cellspacing="1" cellpadding="2" class="infoBox">
	        								<tr class="infoBoxContents">
	        									<td>
	        										<table border="0" width="100%" cellspacing="0" cellpadding="2">
	        											<tr>
	        												<td colspan="2">
	        													<table border="0" width="100%" cellspacing="0" cellpadding="2">' . "\n";

            $close_string = '                           		</table>
	        												</td>
	        											</tr>
	        										</table>
	        									</td>
	        								</tr>
	        							</table>
	        						</td>
	        					</tr>
	        					<tr>
	        						<td width="100%">' . tep_draw_separator('pixel_trans.gif', '100%', '10') . '</td>
	        					</tr>';

            reset($this->modules);
            $output_string = '';

            while (list(, $value) = each($this->modules)) {
                $class = substr($value, 0, strrpos($value, '.'));
                if ($class != 'ot_coupon') { // GST :: re-locate Discount Coupon redemption form
                    if ($GLOBALS[$class]->enabled && $GLOBALS[$class]->credit_class) {
                        if ($selection_string == '')
                            $selection_string = $GLOBALS[$class]->credit_selection();
                    }
                }
            }

            $output_string .= $selection_string; // Only one selection among thos ot module
        }
        return $output_string;
    }

    // update_credit_account is called in checkout process on a per product basis. It's purpose
    // is to decide whether each product in the cart should add something to a credit account.
    // e.g. for the Gift Voucher it checks whether the product is a Gift voucher and then adds the amount
    // to the Gift Voucher account.
    // Another use would be to check if the product would give reward points and add these to the points/reward account.
    //
    function update_credit_account($i) {
        if (MODULE_ORDER_TOTAL_INSTALLED) {
            reset($this->modules);
            while (list(, $value) = each($this->modules)) {
                $class = substr($value, 0, strrpos($value, '.'));
                if (($GLOBALS[$class]->enabled && $GLOBALS[$class]->credit_class)) {
                    $GLOBALS[$class]->update_credit_account($i);
                }
            }
        }
    }

    // This function is called in checkout confirmation.
    // It's main use is for credit classes that use the credit_selection() method. This is usually for
    // entering redeem codes(Gift Vouchers/Discount Coupons). This function is used to validate these codes.
    // If they are valid then the necessary actions are taken, if not valid we are returned to checkout payment
    // with an error
    //
    function collect_posts() {
        global $HTTP_POST_VARS, $HTTP_SESSION_VARS;
        if (MODULE_ORDER_TOTAL_INSTALLED) {
            reset($this->modules);
            while (list(, $value) = each($this->modules)) {
                $class = substr($value, 0, strrpos($value, '.'));
                if (($GLOBALS[$class]->enabled && $GLOBALS[$class]->credit_class)) {
                    $post_var = 'c' . $GLOBALS[$class]->code;
                    if ($HTTP_POST_VARS[$post_var])
                        $_SESSION[$post_var] = $HTTP_POST_VARS[$post_var];
                    //if (!tep_session_is_registered($post_var)) tep_session_register($post_var);
                    $GLOBALS[$class]->collect_posts();
                }
            }
        }
    }

    // pre_confirmation_check is called on checkout confirmation. It's function is to decide whether the
    // credits available are greater than the order total. If they are then a variable (credit_covers) is set to
    // true. This is used to bypass the payment method. In other words if the Gift Voucher is more than the order
    // total, we don't want to go to paypal etc.
    //
	function pre_confirmation_check() {
        global $payment, $order, $credit_covers;

        if (MODULE_ORDER_TOTAL_INSTALLED) {
            $total_deductions = 0;
            reset($this->modules);
            $order_total = $order->info['total'];
            while (list(, $value) = each($this->modules)) {
                $class = substr($value, 0, strrpos($value, '.'));
                $order_total = $this->get_order_total_main($class, $order_total);
                if (($GLOBALS[$class]->enabled && $GLOBALS[$class]->credit_class)) {
                    $total_deductions = $total_deductions + tep_round($GLOBALS[$class]->pre_confirmation_check($order_total), 6);
                    $order_total = $order_total - tep_round($GLOBALS[$class]->pre_confirmation_check($order_total), 6);
                }
            }
            if ($order->info['subtotal'] > 0 && ((double) trim(tep_round($order->info['total'], 2)) - (double) trim(tep_round($total_deductions, 2)) <= 0)) { // If order gran total is greater than zero, only consider store credit
                if (!isset($_SESSION['need_sc_usage_qna'])) {
                    if (!tep_session_is_registered('credit_covers'))
                        tep_session_register('credit_covers');
                    $credit_covers = true;
                }
            } else {   // belts and suspenders to get rid of credit_covers variable if it gets set once and they put something else in the cart
                //$credit_covers = false;
                if (tep_session_is_registered('credit_covers'))
                    tep_session_unregister('credit_covers');
            }
        }
    }

    // this function is called in checkout process. it tests whether a decision was made at checkout payment to use
    // the credit amount be applied aginst the order. If so some action is taken. E.g. for a Gift voucher the account
    // is reduced the order total amount.
    //
    function apply_credit($new_order_id) {
        if (MODULE_ORDER_TOTAL_INSTALLED) {
            reset($this->modules);
            while (list(, $value) = each($this->modules)) {
                $class = substr($value, 0, strrpos($value, '.'));
                if (($GLOBALS[$class]->enabled && $GLOBALS[$class]->credit_class)) {
                    $GLOBALS[$class]->apply_credit($new_order_id);
                }
            }
        }
        unset($_SESSION['cot_gv']);
    }

    // Called in checkout process to clear session variables created by each credit class module.
    //      
    function clear_posts() {
        global $HTTP_POST_VARS, $HTTP_SESSION_VARS;
        if (MODULE_ORDER_TOTAL_INSTALLED) {
            reset($this->modules);
            while (list(, $value) = each($this->modules)) {
                $class = substr($value, 0, strrpos($value, '.'));
                if (($GLOBALS[$class]->enabled && $GLOBALS[$class]->credit_class)) {
                    $post_var = 'c' . $GLOBALS[$class]->code;
                    unset($_SESSION[$post_var]);
                    // if (tep_session_is_registered($post_var)) tep_session_unregister($post_var);
                }
            }
        }
    }

    // Called at various times. This function calulates the total value of the order that the
    // credit will be appled aginst. This varies depending on whether the credit class applies
    // to shipping & tax
    //
    function get_order_total_main($class, $order_total) {
        global $credit, $order;

        if (is_array($order->products)) {
            foreach ($order->products as $idx => $product_array) {
                if ($product_array['custom_products_type_id'] == '3') {
                    $order_total = $order_total - ($product_array['final_price'] * $product_array['qty']);
                }
            }
        }

        if ($GLOBALS[$class]->include_tax == 'false')
            $order_total = $order_total - $order->info['tax'];
        if ($GLOBALS[$class]->include_shipping == 'false')
            $order_total = $order_total - $order->info['shipping_cost'];
        return tep_round($order_total, 6);
    }

    // This function calculates the total value of the order that the surcharge will be calculated based on.
    function get_order_total_pre_surcharge() {
        $ot_total_pre_surcharge = 0;
        $order_total_array = $this->process();

        foreach ($order_total_array as $ot_class) {
            switch ($ot_class['code']) {
                case 'ot_subtotal':
                    $ot_total_pre_surcharge = $ot_class['value'];
                    break;
                case 'ot_coupon':
                case 'ot_gv':
                    $ot_total_pre_surcharge = $ot_total_pre_surcharge - $ot_class['value'];
                    break;
                case 'ot_gst':
                    $ot_total_pre_surcharge = $ot_total_pre_surcharge + $ot_class['value'];
                    break;
            }
        }

        return $ot_total_pre_surcharge;
    }

    // ICW ORDER TOTAL CREDIT CLASS/GV SYSTEM - END ADDITION
}

?>