<?
/*
  $Id: page.php,v 1.23 2015/01/30 08:39:33 akmal.adnan Exp $

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2003 osCommerce

  Released under the GNU General Public License
 */

class page {

    public $index_count = 0;
    public $shared_error_region_disabled = true;
    public $layout_style = '';
    public $blk_bar_flag = 'hdn';

    public function __construct() {
        
    }

    public function get_layout_style() {
        global $PHP_SELF, $const_i_am_allowed_here, $page_info, $localization, $current_category_id, $disable_right_navigation_flag, $content;

        if (substr(basename($PHP_SELF), 0, 5) == 'index' && !isset($_REQUEST['cPath'])) {
            $this->layout_style = "1column";
            $this->blk_bar_flag = 'shw';

            if ($const_i_am_allowed_here) {
                if ($page_info->main_game_id > 0) {
                    if (!$localization->is_supported($page_info->main_game_id, 1)) {
                        $this->shared_error_region_disabled = false;
                        $this->layout_style = "1column";
                    } else if ($page_info->custom_products_type_id_cat > 0 && $page_info->has_product == false && $page_info->tpl == 0 || $page_info->main_game_id == $current_category_id) {
                        $this->layout_style = "onlyleftcolumn";
                    }
                } else {
                    if (!$localization->is_supported($page_info->categories_id, 1)) { // This checking has to come before product type checking
                        $this->shared_error_region_disabled = false;
                        $this->layout_style = "1column";
                    }
                }
            }
        } else if (substr(basename($PHP_SELF), 0, 7) == 'buyback' && substr(basename($PHP_SELF), 0, 13) != 'buyback_order') {
            $this->layout_style = "3column";

            if ($const_i_am_allowed_here) {
                if ($page_info->main_game_id > 0) {
                    if (!$localization->is_supported($page_info->main_game_id, 1)) {
                        $this->shared_error_region_disabled = false;
                        $this->layout_style = "1column";
                    } else if ($page_info->custom_products_type_id_cat > 0 && $page_info->has_product == false && $page_info->tpl == 0 || $page_info->main_game_id == $current_category_id) {
                        $this->layout_style = "onlyleftcolumn";
                    }
                } else {
                    if (!$localization->is_supported($page_info->categories_id, 1)) { // This checking has to come before product type checking
                        $this->shared_error_region_disabled = false;
                        $this->layout_style = "1column";
                    }
                }
            }
        } else if ($this->is_only_right_column($PHP_SELF, $disable_right_navigation_flag)) {
            $this->layout_style = "onlyrightcolumn";
        } else if ($this->is_only_left_column($PHP_SELF, isset($_REQUEST['cPath']))) {
            $this->layout_style = "onlyleftcolumn";
        } else if ($this->is_1column($PHP_SELF, $disable_right_navigation_flag)) {
            $this->layout_style = "1column";
        } else if ($content == CONTENT_ERROR_PAGE_NOT_FOUND) {
            $this->shared_error_region_disabled = false;
            $this->layout_style = "1column";
        } else {
            $this->layout_style = "onlyrightcolumn";
        }

        return $this->layout_style;
    }

    private function is_only_right_column($currentFile, $disable_right_navigation_flag) {
        $return_bool = FALSE;

        $baseFile = basename($currentFile);
        $onlyrightcolumn_pattern_array = array('password_forgotten', 'advanced_search_result', 'advanced_search');

        foreach ($onlyrightcolumn_pattern_array as $pattern) {
            if (substr($baseFile, 0, strlen($pattern)) == $pattern) {
                $return_bool = TRUE;
                break;
            }
        }

        if (!$return_bool && ((substr($baseFile, 0, 11) == 'cms_content' && $disable_right_navigation_flag != '1') || (substr($baseFile, 0, 7) == 'content' && $disable_right_navigation_flag != '1'))) {
            $return_bool = TRUE;
        }

        unset($onlyrightcolumn_pattern_array);
        return $return_bool;
    }

    private function is_only_left_column($currentFile, $issetcPath) {
        $return_bool = FALSE;

        $baseFile = basename($currentFile);
        $onlyleftcolumn_pattern_array = array('buyback_order', 'sc_checkout_payment', 'account', 'address_book', 'my_', 'vip_', 'set_secret_qna');

        foreach ($onlyleftcolumn_pattern_array as $pattern) {
            if (substr($baseFile, 0, strlen($pattern)) == $pattern) {
                $return_bool = TRUE;
                break;
            }
        }

        if (!$return_bool && (in_array($baseFile, array(FILENAME_CANCEL_ORDER, FILENAME_RECEIVE_PAYMENT_INFO, FILENAME_REDEEM_POINT, FILENAME_INVITER, FILENAME_VERIFICATION_SUBMISSION_FORM, FILENAME_FACEBOOK_CONNECT, FILENAME_CUSTOM_PRODUCT_INFO)) || (substr($baseFile, 0, 5) == 'index' && $issetcPath))) {
            $return_bool = TRUE;
        }

        unset($onlyleftcolumn_pattern_array);
        return $return_bool;
    }

    private function is_1column($currentFile, $disable_right_navigation_flag) {
        $return_bool = FALSE;

        $baseFile = basename($currentFile);
        $onecolumn_pattern_array = array(
            'buyback', 'shopping_cart', 'set_secret_qna', 'login', 'account_edit', 'search_all_games', 'news', 'event', 'checkout_',
            FILENAME_INVITER_X, 'create_account_success', 'express_login', 'search_latest_news', 'create_account', 'logoff'
        );

        foreach ($onecolumn_pattern_array as $pattern) {
            if (substr($baseFile, 0, strlen($pattern)) == $pattern) {
                $return_bool = TRUE;
                break;
            }
        }

        if (!$return_bool && ((substr($baseFile, 0, 7) == 'content' && $disable_right_navigation_flag == '1') || (substr($baseFile, 0, 11) == 'cms_content' && $disable_right_navigation_flag == '1') || $disable_right_navigation_flag == '1' || in_array($baseFile, array(FILENAME_GIFT_CARD)))) {
            $return_bool = TRUE;
        }

        unset($onecolumn_pattern_array);
        return $return_bool;
    }

    public function get_template_file() {
        global $content_template, $content;
        $template_file = DIR_WS_CONTENT;

        if (isset($content_template) && file_exists(DIR_WS_CONTENT . basename($content_template))) {
            $template_file .= basename($content_template);
        } else {
            $template_file .= $content . '.tpl.php';
        }

        return $template_file;
    }

    function browser_detection() {
        // initialize the variables
        $browser = '';

        // set to lower case to avoid errors, check to see if http_user_agent is set
        $navigator_user_agent = ( isset($_SERVER['HTTP_USER_AGENT']) ) ? strtolower($_SERVER['HTTP_USER_AGENT']) : '';

        // run through the main browser possibilities, assign them to the main $browser variable
        if (stristr($navigator_user_agent, "msie")) {
            $browser = 'ie';
//		} elseif (stristr($navigator_user_agent, "opera")) {
//			$browser = 'op';
//		} elseif (stristr($navigator_user_agent, "konqueror") || stristr($navigator_user_agent, "safari")) {
//			$browser = 'sf'; 
//		} elseif (stristr($navigator_user_agent, "gecko") || stristr($navigator_user_agent, "mozilla/4")) {
//			$browser = 'ff';
        } else {
            $browser = 'non-ie';
        }

        if (isset($_SESSION['languages_id'])) {
            if (!in_array($_SESSION['languages_id'], array(1, 4))) {    // Except EN and ID
                $browser .= ' non-en';
            }
        }

        return $browser;
    }

    public function get_body_classes($default = '') {
        return $this->browser_detection() . ($default != '' ? ' ' . $default : '');
    }

    public function get_html_menu($navigationtab_obj) {
        $return_string = '';
        $li_arr = array();

        foreach ($navigationtab_obj as $navigationtab_value) {
            if ((isset($navigationtab_value['sub_menu_1']) && count($navigationtab_value['sub_menu_1']) > 0) ||
                    (isset($navigationtab_value['sub_menu_2']) && count($navigationtab_value['sub_menu_2']) > 0)) {

                $li_arr_html = '<li class="mm-item">
									<div class="lf lyr550"></div>
									<a class="mm-item-link dropdown lyr550" ' . ($navigationtab_value['url'] != 'NOURL' ? 'href="' . $navigationtab_value['url'] . '"' : 'href="javascript:void(0)"') . '>' . $navigationtab_value['title'] . '<span class="arrow"></span></a>
									<div class="rg lyr550"></div>
									<div class="mm-item-content" style="width: ' . ((int) (isset($navigationtab_value['sub_menu_size_1']) ? $navigationtab_value['sub_menu_size_1'] : 0) + (int) (isset($navigationtab_value['sub_menu_size_2']) ? $navigationtab_value['sub_menu_size_2'] : 0) + 65) . 'px;">
									<table class="mm-item-content-table" cellspacing="0">
									<tr>
									<td class="lf"></td>
									<td class="ctn">
									<div class="mm-content-base">
										<div id="sub-menu-3">
											<div id="sub-menu-2">
												<div id="sub-menu-1">';

                if (isset($navigationtab_value['sub_menu_1']) && count($navigationtab_value['sub_menu_1']) > 0) {
                    $li_arr_html .= '	<div class="mm-sub-menu-1" style="';
                    if (isset($navigationtab_value['sub_menu_size_1']) && (int) $navigationtab_value['sub_menu_size_1'] > 0) {
                        $li_arr_html .= 'width: ' . $navigationtab_value['sub_menu_size_1'] . 'px;';
                    }
                    $li_arr_html .= 'float:left;">';
                    $li_arr_html .= '<div style="padding:0px;">' . $this->get_html_simple_rc_box('', '<span class="hspacing"></span><span class="hdU1">' . MENU_HEADER_GROUP_BY_PRODUCT_TYPE . '</span>', 43) . '</div>';

                    foreach ($navigationtab_value['sub_menu_1'] as $navigationtab_id_loop => $navigationtab_data_loop) {
                        $li_arr_html .= '<div id="' . $navigationtab_id_loop . '">';
                        $li_arr_html .= '<a class="linkHolder ahd2" href="' . $navigationtab_data_loop['url'] . '">';
                        $li_arr_html .= '<span class="linkIcon"></span>' . $navigationtab_data_loop['description'];
                        $li_arr_html .= '</a>';
                        $li_arr_html .= '</div>';
                    }
                    $li_arr_html .= '	</div>';
                }

                if (isset($navigationtab_value['sub_menu_2']) && count($navigationtab_value['sub_menu_2']) > 0) {
                    $li_arr_html .= '	<div class="mm-sub-menu-2" style="';
                    if (isset($navigationtab_value['sub_menu_size_2']) && (int) $navigationtab_value['sub_menu_size_2'] > 0) {
                        $li_arr_html .= 'width: ' . $navigationtab_value['sub_menu_size_2'] . 'px;';
                    }
                    $li_arr_html .= 'float:left;' . (isset($navigationtab_value['sub_menu_1']) && (count($navigationtab_value['sub_menu_2']) > count($navigationtab_value['sub_menu_1'])) ? 'border-left:1px solid #cccccc;' : '') . '">';
                    $li_arr_html .= '<div style="padding:0px;">' . $this->get_html_simple_rc_box('', '<span class="hspacing"></span><span class="hdU1">' . MENU_HEADER_GROUP_BY_PLATFORM . '</span>', 43) . '</div>';

                    foreach ($navigationtab_value['sub_menu_2'] as $navigationtab_id_loop => $navigationtab_data_loop) {
                        $li_arr_html .= '<div class="mm-content-base" id="' . $navigationtab_id_loop . '">';
                        $li_arr_html .= '<a class="linkHolder ahd2" href="' . $navigationtab_data_loop['url'] . '">';
                        $li_arr_html .= '<span class="linkIcon"></span>' . $navigationtab_data_loop['description'];
                        $li_arr_html .= '</a>';
                        $li_arr_html .= '</div>';
                    }
                    $li_arr_html .= '	</div>';
                }

                $li_arr_html .= '	</div></div></div>
									<div class="clrFx"></div>
									</div>
									</td>
									<td class="rg"></td>
									</tr>
									<tr><td class="btm_lf"></td><td class="btm_ct"></td><td class="btm_rg"></td></tr>
									</table>
									</div>
								</li>';
                $li_arr[] = $li_arr_html;
            } else if (isset($navigationtab_value['sub_menu_data']) && tep_not_empty($navigationtab_value['sub_menu_data'])) {
                $li_arr_html = '<li class="mm-item">
									<div class="lf lyr550"></div>
									<a class="mm-item-link dropdown lyr550" ' . ($navigationtab_value['url'] != 'NOURL' ? 'href="' . $navigationtab_value['url'] . '"' : 'href="javascript:void(0)"') . '>' . $navigationtab_value['title'] . '<span class="arrow"></span></a>
									<div class="rg lyr550"></div>
									<div class="mm-item-content" style="width: ' . (int) (isset($navigationtab_value['sub_menu_size_1']) ? $navigationtab_value['sub_menu_size_1'] + 25 : 0) . 'px;">
									<table class="mm-item-content-table" cellspacing="0">
									<tr>
									<td class="lf"></td>
									<td class="ctn">
									<div class="mm-content-base">';
                $li_arr_html .= $navigationtab_value['sub_menu_data'];

                $li_arr_html .= '	<div class="clrFx"></div>
									</div>
									<td class="rg"></td>
									</tr>
									<tr>
										<td class="btm_lf"></td><td class="btm_ct"></td><td class="btm_rg"></td>
									</tr>
									</table>
									</div>
								</li>';
                $li_arr[] = $li_arr_html;
            } else {
                $li_arr[] = '<li class="mm-item">
								<div class="lf"></div>
								<a class="mm-item-link" href="' . $navigationtab_value['url'] . '">' . $navigationtab_value['title'] . '</a>
								<div class="rg"></div>
							</li>';
            }
        }

        ob_start();
        ?>
        <ul class="ogmMenu"><?= implode('<li class="mm-item partition"></li>', $li_arr) ?><li class="clear-fix"></li></ul>
        <?
        $return_string = ob_get_contents();
        ob_end_clean();

        return $return_string;
    }

    /*
      Tab Box
      @param string $box_style (Optional)
      0 : Without footer.
      1 : With round corner footer.
      2 : With image background footer.
      3 : Without footer & No Border
      @param string $footer (Optional) Fixed height content to display in the footer of the box.
     */

    public function get_html_tab($tab_content_array, $box_style = 1, $footer = '', $id_suffix = '') {
        $return_string = '';
        $tab_header = '';
        $tab_footer = '';
        $tab_content_string = '';
        $box_border_class = '';
        $id_suffix = tep_not_null($id_suffix) ? '_' . $id_suffix : '';

        if ($box_style == 3) {
            $box_border_class = '';
        } else if ($box_style == 2) {
            $box_border_class = ' solborder2s';
            $tab_footer = '	<div class="ibrc_btm_lf"></div>
							<div class="ibrc_btm_rg"></div>
							<div class="ibrc_btm_ct"></div>';
        } else if ($box_style == 1) {
            $box_border_class = ' solborder2s';
            $tab_footer = '	<div class="rc_btm_lf"></div>
							<div class="rc_btm_rg"></div>
							<div class="rc_btm_ct"></div>';
        }

        foreach ($tab_content_array as $count => $tab_content) {
            $id_prefix = 'tab' . $this->index_count . $id_suffix;
            $selected = isset($tab_content['selected']) ? (int) $tab_content['selected'] : ($count ? 0 : 1);
            $tab_header .= '<div id="' . $id_prefix . '" class="tabItem">
								<div class="' . $this->get_selected_css_class('lf', $selected) . '"></div>
								<div class="' . $this->get_selected_css_class('ct', $selected) . '"><span class="hdC1">' . $tab_content['label'] . '</span></div>
								<div class="' . $this->get_selected_css_class('rg', $selected) . '"></div>
							</div>';

            $tab_content_string .= '<div id="' . $id_prefix . '_content" style="' . ($selected ? '' : 'display:none') . '">' . $tab_content['content'] . '</div>';
            $this->index_count++;
        }

        ob_start();
        ?>
        <div class="tabTop"><div class="header lyr150"><?= $tab_header ?></div><div class="footer lyr145"></div></div>
        <div class="tabMdl cc_mdl<?= $box_border_class ?>"><?= $tab_content_string ?></div>
        <?
        $return_string = ob_get_contents();
        ob_end_clean();

        return $return_string . $tab_footer;
    }

    /*
      Simple Round Corner Box
      @param string $box_style (Optional)
      0 : Black Box & Without round corner footer.
      1 : Black Box & With round corner footer.
      2 : Black Box & With image background footer.
      10: White Box & Without round corner footer.
      11: White Box & With round corner footer.
      12: White Box & With image background footer.
      13: White Box & Without header TITLE & With round corner footer.
      14: White Box & Without header TITLE & Without round corner footer.
      20: Light Gray Box & Without footer.
      21: Light Gray Box & With round corner footer.
      22: Light Gray Box & With image background footer.
      23: Light Gray Box & Without header TITLE & With round corner footer.
      30: Yellow Box & Without round corner footer.
      31: Yellow Box & With round corner footer.
      32: Yellow Box & With image background footer.
      33: Yellow Box & Without header & With round corner footer.
      40: Gray Box & Without round corner footer.
      41: Gray Box & With round corner footer.
      42: Gray Box & With image background footer.
      43: Gray Box & Without header TITLE & With round corner footer.
      63: White Box & Without header TITLE & With round corner footer.
      @param string $footer (Optional) Fixed height content to display in the footer of the box.
     */

    public function get_html_simple_rc_box($header_title, $content_string, $box_style = 1, $footer = '') {
        $box_color_array = array(0 => array('prefix' => 'brc', 'hd' => 'hd2'),
            1 => array('prefix' => 'rc', 'hd' => 'hd1'),
            2 => array('prefix' => 'lgrc', 'hd' => 'hd1'),
            3 => array('prefix' => 'ylrc', 'hd' => 'hd1'),
            4 => array('prefix' => 'grc', 'hd' => 'hd1'),
            5 => array('prefix' => 'sdrc', 'hd' => 'hd1'),
            6 => array('prefix' => 'nrc', 'hd' => 'hd1'));

        $box_color_code = floor($box_style / 10); // Get color code
        $box_layout = ($box_style % 10);   // Get style code

        $bc_prefix = $box_color_array[$box_color_code]['prefix'];
        $header_title_css_class = $box_color_array[$box_color_code]['hd'];

        // Header
        if (in_array($box_layout, array(3, 4))) {
            $header_title = '';
        } else {
            $header_title = '<div class="header"><span class="' . $header_title_css_class . '">' . $header_title . '</span></div>';
        }

        // Footer
        if (in_array($box_layout, array(0, 4))) {
            $box_footer = ''; // Empty footer
        } else if ($box_layout == 2) { // Image Bg
            $box_footer = '	<div class="ibrc_btm_lf"></div><div class="ibrc_btm_rg"></div>
							<div class="ibrc_btm_ct">' . $footer . '</div>';
        } else {
            $box_footer = '	<div class="' . $bc_prefix . '_btm_lf"></div><div class="' . $bc_prefix . '_btm_rg"></div><div class="' . $bc_prefix . '_btm_ct"></div>';
        }

        $box_header = '<div class="' . $bc_prefix . '_top_lf"></div><div class="' . $bc_prefix . '_top_rg"></div><div class="' . $bc_prefix . '_top_ct"></div>';

        if (in_array($box_color_code, array(5))) {
            $return_string = '<div class="' . $bc_prefix . '_mdl"><div class="' . $bc_prefix . '_mdl2">';
            $return_string .= $header_title;
            $return_string .= '<div class="content">' . $content_string . '</div>';
            $return_string .= '</div></div>';
        } else {
            $return_string = '<div class="srcContent ' . $bc_prefix . '_mdl">';
            $return_string .= $header_title;
            $return_string .= '<div class="content">' . $content_string . '</div>';
            $return_string .= '</div>';
        }
        return $box_header . $return_string . $box_footer;
    }

    /*
      Simple Vertical Menu Box

      @param string $header (Required) The title of the box header for display.
      @param string $expand_method (Optional)
      1 : Expand OR Collapse.
      2 : Expand 1 AND Collapse the rest.
      @param array $contents (Required) The list of navigation.
      array[]['href'] : (Required) The destination of a link
      array[]['desc'] : (Required) The description of a link
      array[]['default'] : (Optional) If TRUE, the list will be selected.
     */

    public function get_html_simple_vmenu_box($header, $contents, $expand_method = 2) {
        $return_string = '';
        $ul_class = 'vmenu' . ($expand_method == 1 ? '' : '2');

        foreach ($contents as $content) {
            $default_flag = false;

            if (isset($content['default']) && $content['default']) {
                $default_flag = true;
            }

            $return_string .= '<li class="' . $this->get_selected_css_class('vm_header', $default_flag) . (tep_not_null($return_string) ? ' solborder' : '') . '"><a href="' . $content['href'] . '"><span class="lbl">' . $content['desc'] . '</span></a></li>';
        }

        ob_start();
        ?>
        <div class="brc_top_lf"></div>
        <div class="brc_top_rg"></div>
        <div class="brc_top_ct"></div>
        <div class="vmContent">
            <div class="header brc_mdl"><span class="hd2"><?= $header ?></span></div>
            <div class="content solborder3s">
                <ul class="<?= $ul_class ?>"><?= $return_string ?></ul>
            </div>
        </div>
        <?
        $return_string = ob_get_contents();
        ob_end_clean();

        return $return_string;
    }

    /*
      Expandable Vertical Menu Box

      @param string $header (Required) The title of the box header for display.
      @param decimal $box_style (Optional)
      0 : Black header & No footer.
      1 : Black header & with round corner footer.
      2 : Black header & with image background footer.
      10: White header & No footer.
      11: White header & with round corner footer.
      12: White header & with image background footer.
      @param string $footer (Optional) The content to display in the footer of the box.
      @param string $expand_method (Optional)
      1  : [Arrow1]Expand OR Collapse.
      2  : [Arrow1]Expand 1 AND Collapse the rest.
      11 : [Arrow2]Expand OR Collapse.
      12 : [Arrow2]Expand 1 AND Collapse the rest.
      @param array $contents (Required) The list of navigation.
      array[]['desc'] : (Required) The destination of a link
      array[]['href'] : (Optional) The description of a link
      array[]['id']   : (Optional) The outter list ID.
      array[]['content'] : (Optional) The customized content for the menu
      array[]['default_open'] : (Optional) If TRUE, the list will be expanded.
      array[]['slist'][]['href'] : (Required)
      array[]['slist'][]['desc'] : (Required)
      array[]['slist'][]['ext'] : (Optional)
      array[]['slist'][]['id'] : (Optional)	The inner list ID.
     */

    public function get_html_expandable_vmenu_box($header, $contents, $box_style = 1, $footer = '', $expand_method = 2) {
        $return_string = '';
        $box_footer = '';
        $box_border_class = 'solborder3s';

        $box_color_code = floor($box_style / 10); // Get color code
        $box_layout = ($box_style % 10);   // Get style code
        $arrow_type = floor($expand_method / 10); // Get arrow design code
        $exp_method = ($expand_method % 10);  // Get expanding method code

        $ul_class = 'vmenu' . ($exp_method == 1 ? '' : '2');
        $arrow_code = $arrow_type == 0 ? 'arrow' : 'arrow2';

        if ($box_layout == 2) {
            $box_border_class = 'solborder2s';
            $footer = tep_not_null($footer) ? '<div class="footer solborder2s">' . $footer . '</div>' : '';
            $box_footer = '	<div class="ibrc_btm_lf"></div>
							<div class="ibrc_btm_rg"></div>
							<div class="ibrc_btm_ct"></div>';
        } else if ($box_layout == 1) {
            $box_footer = '	<div class="rc_btm_lf"></div>
							<div class="rc_btm_rg"></div>
							<div class="rc_btm_ct"></div>';
        }

        if ($box_color_code == 0) { // Black Header
            $header_cap = 'brc';
            $header_class = 'hd2';
        } else { // White Header
            $box_border_class = 'solborder3bs';
            $header_cap = 'rc';
            $header_class = 'hd1';
        }

        foreach ($contents as $count_outlist => $outlist_array) {
            $inner_list = '';
            $arror_icon = '';
            $outer_li_class = tep_not_null($return_string) ? ' solborder' : '';

            $default_open_flag = false;
            $default_open_style = ' style="display:none"';
            if (isset($outlist_array['default_open']) && $outlist_array['default_open']) {
                $default_open_flag = true;
                $default_open_style = '';
            }

            if (isset($outlist_array['slist'])) {
                foreach ($outlist_array['slist'] as $count_inlist => $inlist_array) {
                    $li_class = $count_inlist ? 'dotborder' : '';
                    $li_id = isset($inlist_array['id']) ? 'id="' . $inlist_array['id'] . '"' : '';

                    $inner_list .= '<li ' . $li_id . ' class="' . $li_class . '">';
                    if (isset($inlist_array['href']) && tep_not_empty($inlist_array['href'])) {
                        $inner_list .= '<a href="' . $inlist_array['href'] . '">';
                    }

                    $inner_list .= $inlist_array['desc'];

                    if (isset($inlist_array['href']) && tep_not_empty($inlist_array['href'])) {
                        $inner_list .= '</a>';
                    }
                    $inner_list .= $inlist_array['ext'] . '</li>';
                }

                if (tep_not_null($inner_list)) {
                    $arror_icon = '<span class="' . $arrow_code . '"></span>';
                    $inner_list = '<li class="vm_content solborder"' . $default_open_style . '><ul class="dd_listing">' . $inner_list . '</ul></li>';
                }
            } else if (isset($outlist_array['content'])) {
                $arror_icon = '<span class="' . $arrow_code . '"></span>';
                $inner_list = '<li class="vm_content solborder"' . $default_open_style . '>' . $outlist_array['content'] . '</li>';
            }

            $li_id = isset($outlist_array['id']) ? 'id="' . $outlist_array['id'] . '"' : '';
            $return_string .= '<li ' . $li_id . ' class="' . $this->get_selected_css_class('vm_header', $default_open_flag) . $outer_li_class . ' clrFx' . '">';
            if (isset($outlist_array['href']) && tep_not_empty($outlist_array['href'])) {
                $return_string .= '<a href="' . $outlist_array['href'] . '">';
            }

            $return_string .= '<span class="lbl">' . $outlist_array['desc'] . '</span>' . $arror_icon;
            if (isset($outlist_array['href']) && tep_not_empty($outlist_array['href'])) {
                $return_string .= '</a>';
            }

            $return_string .= '</li>' . $inner_list;
        }

        ob_start();
        ?>
        <div class="<?= $header_cap ?>_top_lf"></div>
        <div class="<?= $header_cap ?>_top_rg"></div>
        <div class="<?= $header_cap ?>_top_ct"></div>
        <div class="vmContent">
            <div class="header <?= $header_cap ?>_mdl"><span class="<?= $header_class ?>"><?= $header ?></span></div>
            <div class="content <?= $box_border_class ?>">
                <ul class="<?= $ul_class ?>"><?= $return_string ?></ul>
                <div class="clrFx"></div>
            </div>
            <?= $footer ?>
        </div>
        <?
        $return_string = ob_get_contents();
        ob_end_clean();

        return $return_string . $box_footer;
    }

    private function get_selected_css_class($default, $selected, $delimiter = '_selected', $append = true) {
        if ($selected) {
            $default .= ($append ? ' ' . $default : '') . $delimiter;
        }

        return $default;
    }

    public function generate_product_list_layout($layout_data, $custom_product_type_id) {
        ob_start();
        ?>
        <table class="gListingMain" cellspacing="0" cellpadding="0">
            <?
            foreach ($layout_data as $sorting_order => $game_array) {
                ?>
                <tr>
                    <td class="img">
                        <?
                        echo '<div class="top">' . $game_array['image'] . '</div>';

                        if (count($game_array['delivery_method'])) {
                            echo '<div class="delivery">' .
                            '<div>' .
                            '<div>' . TEXT_INFO_DELIVERY_MODE . ':</div>' .
                            '<div>' . implode(' ', $this->get_delivery_method_icon($game_array['delivery_method'])) . '</div>' .
                            '<div>' . TABLE_HEADING_DELIVERY_TIME . ':<br>' . TEXT_INSTANT . '</div>' .
                            '</div>' .
                            '</div>';
                        }
                        ?>
                    </td>
                    <td class="prod">
                        <table class="pListingMain" cellspacing="0" cellpadding="0" border="0">
                            <tr>
                                <td class="head" colspan="3"><span class="hd1"><?= $game_array['name'] ?></span></td>
                            </tr>
                            <tbody>
                                <?
                                foreach ($game_array['group'] AS $products_array) {
                                    $tr_class = $products_array['is_dtu_flag'] == 1 ? 'dtu' : '';
                                    $default_dm_id = $products_array['is_dtu_flag'] == 0 ? 5 : 6;
                                    $product_name = (!tep_not_empty($products_array['name']) ? $game_array['name'] : $products_array['name']);
                                    $btn_onclick = $products_array['btn_info']['color'] != 'gray' ? ' onclick="pfv(this.id)"' : '';
                                    ?>
                                    <tr id="<?= 'data_' . $products_array['index'] ?>" 
                                        class="<?= $tr_class ?>"
                                        data-pid="<?= $products_array['pid'] ?>" 
                                        data-products-bundle="<?= $products_array['products_bundle'] ?>" 
                                        data-dm="<?= $default_dm_id ?>"
                                        data-cpt-id="<?= $custom_product_type_id ?>"
                                        data-name="<?= htmlspecialchars($product_name) ?>">
                                        <td class="name"><div><?= $this->get_product_label_section($products_array, $product_name) ?></div></td>
                                        <td class="price"><div><?= $this->get_product_price_section($products_array) ?></div></td>
                                        <td class="pl_btn"><?= tep_image_button2($products_array['btn_info']['color'], 'javascript:void(0);', $products_array['btn_info']['label'], 120, ' id="' . $products_array['index'] . '"' . $btn_onclick) ?></td>
                                    </tr>
                                    <?
                                }
                                ?>
                            </tbody>
                            <tfoot>
                                <tr>
                                    <td class="foot" colspan="3"><span><?= ($game_array['more_info'] != '' ? $game_array['more_info'] : '&nbsp;') ?></span></td>
                                </tr>
                            </tfoot>
                        </table>
                    </td>
                </tr>
                <?
            }
            ?>
        </table>
        <script type="text/javascript">
            jQuery(function() {
                jQuery(".wTip").tipTip({maxWidth: "270px", edgeOffset: 0, defaultPosition: "left", keepAlive: true});
                jQuery("body").click(function() {
                    jQuery('#tiptip_holder').hide();
                });
            });
        </script>
        <?
        $return_string = ob_get_contents();
        ob_end_clean();

        return $return_string;
    }

    private function get_delivery_method_icon($delivery_methods_array) {
        $delivery_mode_icons_array = array();
        if (count($delivery_methods_array)) {
            foreach ($delivery_methods_array as $delivery_methods_id_loop) {
                switch ($delivery_methods_id_loop) {
                    case '5':
                        $delivery_mode_icons_array[] = tep_image(DIR_WS_ICONS . 'icon_delivery_mode_ogm_account.gif', '', '', '', ' class="ogm_tooltips" style="vertical-align:middle;cursor:pointer;" ogm_title="' . TEXT_INFO_DELIVERY_SEND_TO_ACCOUNT . '" ogm_content="' . TEXT_INFO_DELIVERY_SEND_TO_ACCOUNT_DESCRIPTION . '"');
                        break;
                    case '6':
                        $delivery_mode_icons_array[] = tep_image(DIR_WS_ICONS . 'icon_delivery_mode_topup.gif', '', '', '', ' class="ogm_tooltips" style="vertical-align:middle;cursor:pointer;" ogm_title="' . TEXT_INFO_DELIVERY_DIRECT_TOP_UP . '" ogm_content="' . TEXT_INFO_DELIVERY_DIRECT_TOP_UP_DESCRIPTION . '"');
                        break;
                    case '7':
                        $delivery_mode_icons_array[] = tep_image(DIR_WS_ICONS . 'icon_delivery_mode_pickup.gif', '', '', '', ' class="ogm_tooltips" style="vertical-align:middle;cursor:pointer;" ogm_title="' . TEXT_INFO_DELIVERY_IN_STORE_PICKUP . '" ogm_content="' . TEXT_INFO_DELIVERY_IN_STORE_PICKUP_DESCRIPTION . '"');
                        break;
                    default:
                        break;
                }
            }
        }

        return $delivery_mode_icons_array;
    }

    private function get_product_label_section($product_info_array, $product_name, $customqty = '') {
        $return_str = $product_name;
        $product_notice = '';

        // Missing GST title



        if (is_array($product_info_array['promotion_info'])) {
            if (tep_not_empty($product_info_array['promotion_info']['notice'])) {
                $product_notice .= '<div class="pl_row">' . $product_info_array['promotion_info']['notice'] . '</div>'; // time counter box
            }

            if (tep_not_empty($product_info_array['promotion_info']['status'])) {
                $product_notice .= '<div class="pl_row"><div style="float:left;">' . TEXT_PROMOTION_STATUS . ':</div><div style="float:left;padding-left:5px;color:#FF0000;">' . $product_info_array['promotion_info']['status'] . '</div><div style="clear:both;"></div></div>';
            }
        }

        // pre-order or out of stock notice
        if (tep_not_empty($product_info_array['product_notice']) && $product_info_array['product_notice'] != '-') {
            $product_notice .= '<div class="pl_row"><div class="note_img"><span class="lbl">' . $product_info_array['product_notice'] . '</span></div>';
        }

        if ($product_info_array['is_dtu_flag'] === 1) {
            $extra_info_str = '';
            $return_str = '<div><b>' . TEXT_DTU_TO_GAME_ACCOUNT . '</b></div>';
            $return_str .= '<div class="pl_row ihd1">' . $product_name . ' <b>x</b> ' . tep_draw_input_field('custom_qty', $customqty, 'class="customqty" size="6" onkeyup="updateDTUextra(this)" maxlength="3" style="width: 43px; margin: 0px;"') . '</div>';

            if (count($product_info_array['dtu_extra_info']) > 0) {
                foreach ($product_info_array['dtu_extra_info'] as $idx => $data) {
                    if (tep_not_empty($data)) {
                        if ($idx < 3) {
                            $extra_info_str .= '<span class="hd3 ext' . $idx . '" data-default="' . $data . '">' . $data . '</span>';
                        } else {
                            $extra_info_str .= '<span class="hd5 ext' . $idx . '">' . $data . '</span>';
                        }
                    }
                }

                $return_str .= tep_not_empty($extra_info_str) ? '<div class="dtu_extra_info">=&nbsp;' . $extra_info_str . '</div>' : '';
                unset($extra_info_str);
            }
        }

        return $return_str . $product_notice;
    }

    private function get_product_price_section($product_info_array) {
        $group_name = ' <span class="cdd_price">(' . $product_info_array['customers_groups_name'] . ')</span>';

        $return_str = '<span class="cdd_price">' . $product_info_array['price'] . '</span>';
        $return_str .= '<div>' . ($product_info_array['op'] != '' ? tep_display_op($product_info_array['op'], $group_name) : tep_display_op(0, ' ' . $group_name)) . '</div>';
        $return_str .= tep_not_empty($product_info_array['normal_price']) ? '<strike>' . $product_info_array['normal_price'] . '</strike>' : '';
        $return_str .= $product_info_array['gst_title'];

        return $return_str;
    }

}
?>