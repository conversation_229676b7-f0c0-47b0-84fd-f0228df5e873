<?

class page_view_module {

    var $xml_array, $files_name_array, $tag_configurations_mode;
    var $tags_names, $tags_configurations;
    var $current_tag, $ip_address;
    var $current_url;

    function page_view_module() {
        require_once('ogm_xml_to_ary.php');

        $this->xml_array_obj = new ogm_xml_to_ary(realpath(DIR_FS_CATALOG . "cache/page_view_module.xml"));
        $this->xml_array = $this->xml_array_obj->get_ogm_xml_to_ary();

        $this->ip_address = tep_get_ip_address();
        $this->set_tags();

        $this->current_url = $this->get_current_url();
        $this->set_settings();
    }

    function trace() {
        $this->current_tag = $this->get_current_tags($this->current_url);
        if (tep_not_null($this->current_tag)) {
            if ($this->tag_configurations_mode[$this->current_tag]['mode'] == 'o') {
                return true;
            }
        } else {
            return true;
        }

        $ip_list_mode_array = array('o' => 'Off', 't' => 'Tracking', 'b' => 'Blocking');

        $page_view_ip_list_id = 0;
        $visited_tag_counter = 0;
        $page_view_ip_list = 'c';
        $page_view_ip_mode = 't';

        $this->ip_in_binary = tep_ip_in_binary_form($this->ip_address);

        /* 		//backup for in case mysql highload
          $page_view_ip_list_select_sql = "	SELECT pvil.page_view_ip_list_id, pvil.page_view_ip_list_mode, pvil.page_view_ip_list_list, its.ip_tags_stats_counter
          FROM " . TABLE_PAGE_VIEW_IP_LIST . " as pvil
          LEFT JOIN " . TABLE_IP_TAGS_STATS . " as its
          ON its.page_view_ip_list_id = pvil.page_view_ip_list_id
          AND its.script_tags_name = '".tep_db_input($this->current_tag)."'
          WHERE pvil.page_view_ip_list_ip = '".tep_db_input($this->ip_address)."'
          AND pvil.page_view_ip_list_ip_subnet = '32'";
          $page_view_ip_list_result_sql = tep_db_query($page_view_ip_list_select_sql);
          $page_view_ip_list_num_row = tep_db_num_rows($page_view_ip_list_result_sql);
          if (!$page_view_ip_list_num_row) {
          $page_view_ip_list_select_sql = "	SELECT pvil.page_view_ip_list_id, pvil.page_view_ip_list_mode, pvil.page_view_ip_list_list, its.ip_tags_stats_counter
          FROM " . TABLE_PAGE_VIEW_IP_LIST . " as pvil
          LEFT JOIN " . TABLE_IP_TAGS_STATS . " as its
          ON its.page_view_ip_list_id = pvil.page_view_ip_list_id
          AND its.script_tags_name = '".tep_db_input($this->current_tag)."'
          WHERE pvil.page_view_ip_list_ip_binary = LEFT( '".tep_db_input($this->ip_in_binary)."', pvil.page_view_ip_list_ip_subnet )
          ORDER BY pvil.page_view_ip_list_ip_subnet DESC";

          // remove below 1 if mysql high load, item(A)
         */
        //item(A) temp new query
        $page_view_ip_list_select_sql = "	SELECT pvil.page_view_ip_list_ip_binary, pvil.page_view_ip_list_id, pvil.page_view_ip_list_mode, pvil.page_view_ip_list_list, its.ip_tags_stats_counter, pvil.page_view_ip_list_ip_subnet
												FROM " . TABLE_PAGE_VIEW_IP_LIST . " as pvil 
												LEFT JOIN " . TABLE_IP_TAGS_STATS . " as its 
													ON its.page_view_ip_list_id = pvil.page_view_ip_list_id 
														AND its.script_tags_name = '" . tep_db_input($this->current_tag) . "'
												WHERE pvil.page_view_ip_list_ip_binary LIKE '" . substr(tep_db_input($this->ip_in_binary), 0, 16) . "%'
												ORDER BY pvil.page_view_ip_list_ip_subnet";
        //end temp new query
        $page_view_ip_list_result_sql = tep_db_query($page_view_ip_list_select_sql);
        $page_view_ip_list_num_row = tep_db_num_rows($page_view_ip_list_result_sql);
//		}		// backup for in case mysql highload
        if (!$page_view_ip_list_num_row) {

            if ((int) $this->tag_configurations_mode[$this->current_tag]['max_counter'] >= 0 && (int) $this->tag_configurations_mode[$this->current_tag]['max_counter'] <= $visited_tag_counter) {
                $page_view_ip_list = 'b';
                $page_view_ip_mode = $this->tag_configurations_mode[$this->current_tag]['mode'];
                $this->send_email_notification("This IP Address," . $this->ip_address . " has been moved to Watch List (" . (isset($ip_list_mode_array[$page_view_ip_mode]) ? $ip_list_mode_array[$page_view_ip_mode] : $ip_list_mode_array[$page_view_ip_mode]) . " mode) when visiting (" . $this->current_url . ")", "PVM - " . $this->ip_address);
            }
            $page_view_ip_list_data_sql = array('page_view_ip_list_ip' => tep_db_prepare_input($this->ip_address),
                'page_view_ip_list_ip_binary' => tep_db_prepare_input($this->ip_in_binary),
                'page_view_ip_list_ip_subnet' => '32',
                'page_view_ip_list_list' => $page_view_ip_list,
                'page_view_ip_list_mode' => $page_view_ip_mode,
                'page_view_ip_list_last_url' => tep_db_prepare_input($_SERVER['REQUEST_URI']),
                'page_view_ip_list_last_update' => 'now()'
            );
            if ($page_view_ip_mode == 'b') {
                $page_view_ip_list_data_sql['page_view_ip_list_last_blocked'] = 'now()';
            }
            tep_db_perform(TABLE_PAGE_VIEW_IP_LIST, $page_view_ip_list_data_sql);
            $page_view_ip_list_id = tep_db_insert_id();
        } else {
            $page_view_ip_list_array = array();
            while ($page_view_ip_list_row = tep_db_fetch_array($page_view_ip_list_result_sql)) {
                if (strcasecmp(substr($this->ip_in_binary, 0, $page_view_ip_list_row['page_view_ip_list_ip_subnet']), substr($page_view_ip_list_row['page_view_ip_list_ip_binary'], 0, $page_view_ip_list_row['page_view_ip_list_ip_subnet'])) === 0) {
                    $page_view_ip_list_array[$page_view_ip_list_row['page_view_ip_list_list']] = $page_view_ip_list_row;
                }
            }

            if (count($page_view_ip_list_array)) {
                if (isset($page_view_ip_list_array['w'])) {
                    $page_view_ip_list_row = $page_view_ip_list_array['w'];
                } else if (isset($page_view_ip_list_array['b'])) {
                    $page_view_ip_list_row = $page_view_ip_list_array['b'];
                } else {
                    $page_view_ip_list_row = $page_view_ip_list_array['c'];
                }
                //$page_view_ip_list_row = tep_db_fetch_array($page_view_ip_list_result_sql);	//backup for in case mysql highload
                switch ($page_view_ip_list_row['page_view_ip_list_list']) {
                    case "b":
                    case "w":
                        $page_view_ip_mode = $page_view_ip_list_row['page_view_ip_list_mode'];
                        $page_view_ip_list = $page_view_ip_list_row['page_view_ip_list_list'];
                        break;
                    case "c":
                        if ((int) $this->tag_configurations_mode[$this->current_tag]['max_counter'] < 0) {
                            $page_view_ip_list = 'c';
                        } else if (!((int) $this->tag_configurations_mode[$this->current_tag]['max_counter'] > ($page_view_ip_list_row['ip_tags_stats_counter'] + 1))) {  // plus 1 for current visit
                            $page_view_ip_list = 'b';
                            $page_view_ip_mode = $this->tag_configurations_mode[$this->current_tag]['mode'];
                            $this->send_email_notification("This IP Address," . $this->ip_address . " has been moved to Watch List (" . (isset($ip_list_mode_array[$page_view_ip_mode]) ? $ip_list_mode_array[$page_view_ip_mode] : $ip_list_mode_array[$page_view_ip_mode]) . " mode) when visiting (" . $this->current_url . ")", "PVM - " . $this->ip_address);
                        }
                        break;
                    default:
                        $page_view_ip_mode = $page_view_ip_list_row['page_view_ip_list_mode'];
                        $page_view_ip_list = $page_view_ip_list_row['page_view_ip_list_list'];
                        break;
                }
                $visited_tag_counter = (int) $page_view_ip_list_row['ip_tags_stats_counter'];
                $page_view_ip_list_id = $page_view_ip_list_row['page_view_ip_list_id'];
            } else { // new ip
                if ((int) $this->tag_configurations_mode[$this->current_tag]['max_counter'] >= 0 && (int) $this->tag_configurations_mode[$this->current_tag]['max_counter'] <= $visited_tag_counter) {
                    $page_view_ip_list = 'b';
                    $page_view_ip_mode = $this->tag_configurations_mode[$this->current_tag]['mode'];
                    $this->send_email_notification("This IP Address," . $this->ip_address . " has been moved to Watch List (" . (isset($ip_list_mode_array[$page_view_ip_mode]) ? $ip_list_mode_array[$page_view_ip_mode] : $ip_list_mode_array[$page_view_ip_mode]) . " mode) when visiting (" . $this->current_url . ")", "PVM - " . $this->ip_address);
                }
                $page_view_ip_list_data_sql = array('page_view_ip_list_ip' => tep_db_prepare_input($this->ip_address),
                    'page_view_ip_list_ip_binary' => tep_db_prepare_input($this->ip_in_binary),
                    'page_view_ip_list_ip_subnet' => '32',
                    'page_view_ip_list_list' => $page_view_ip_list,
                    'page_view_ip_list_mode' => $page_view_ip_mode,
                    'page_view_ip_list_last_url' => tep_db_prepare_input($_SERVER['REQUEST_URI']),
                    'page_view_ip_list_last_update' => 'now()'
                );
                if ($page_view_ip_mode == 'b') {
                    $page_view_ip_list_data_sql['page_view_ip_list_last_blocked'] = 'now()';
                }
                tep_db_perform(TABLE_PAGE_VIEW_IP_LIST, $page_view_ip_list_data_sql);
                $page_view_ip_list_id = tep_db_insert_id();
            }
        }

        // record page counter
        $ip_list_stats_data_sql = array('page_view_ip_list_id' => $page_view_ip_list_id,
            'customers_id' => (isset($_SESSION['customer_id']) && (int) $_SESSION['customer_id'] > 0 ? (int) $_SESSION['customer_id'] : 0),
            'ip_list_history_ip_address' => tep_db_prepare_input($this->ip_address),
            'scripts_name' => tep_db_prepare_input($_SERVER['REQUEST_URI']),
            'ip_list_history_tags' => tep_db_prepare_input($this->current_tag),
            'ip_list_history_datatime' => 'now()',
            'ip_list_history_remark ' => tep_db_prepare_input('IP mode: ' . $ip_list_mode_array[(isset($page_view_ip_list_row['page_view_ip_list_mode']) && tep_not_null($page_view_ip_list_row['page_view_ip_list_mode']) ? $page_view_ip_list_row['page_view_ip_list_mode'] : 't')])
        );
        tep_db_perform(TABLE_IP_LIST_HISTORY, $ip_list_stats_data_sql);

        $page_view_ip_list_data_sql = array('page_view_ip_list_last_url' => tep_db_prepare_input($_SERVER['REQUEST_URI']),
            'page_view_ip_list_last_update' => 'now()',
            'page_view_ip_list_list' => $page_view_ip_list,
            'page_view_ip_list_mode' => $page_view_ip_mode,
            'page_view_ip_list_last_update' => 'now()'
        );
        if ($page_view_ip_list_row['page_view_ip_list_list'] != 'b' && $page_view_ip_mode == 'b') {
            $page_view_ip_list_data_sql['page_view_ip_list_last_blocked'] = 'now()';
        }
        tep_db_perform(TABLE_PAGE_VIEW_IP_LIST, $page_view_ip_list_data_sql, 'update', ' page_view_ip_list_id = "' . (int) $page_view_ip_list_id . '"');

        if (tep_not_null($this->current_tag)) {
            $ip_tags_stats_select_sql = "	SELECT ip_tags_stats_counter
											FROM " . TABLE_IP_TAGS_STATS . "
											WHERE page_view_ip_list_id = '" . $page_view_ip_list_id . "'
												AND script_tags_name = '" . $this->current_tag . "'";
            $ip_tags_stats_result_sql = tep_db_query($ip_tags_stats_select_sql);
            if ($ip_tags_stats_row = tep_db_fetch_array($ip_tags_stats_result_sql)) {
                $update_ip_tags_stats_data_sql = array();
                $update_ip_tags_stats_data_sql['ip_tags_stats_last_visit'] = 'now()';
                if ($ip_tags_stats_row['ip_tags_stats_counter'] <= (int) $this->settings['display_max_counter']) {
                    $update_ip_tags_stats_data_sql['ip_tags_stats_counter'] = (int) $ip_tags_stats_row['ip_tags_stats_counter'] + 1;
                }
                tep_db_perform(TABLE_IP_TAGS_STATS, $update_ip_tags_stats_data_sql, 'update', "page_view_ip_list_id = '" . $page_view_ip_list_id . "' AND script_tags_name = '" . $this->current_tag . "'");
            } else {
                tep_db_query("  INSERT IGNORE INTO " . TABLE_IP_TAGS_STATS . " (`page_view_ip_list_id`, `script_tags_name`, `ip_tags_stats_counter`, `ip_tags_stats_last_visit`)
                                                VALUES ('" . $page_view_ip_list_id . "', '" . $this->current_tag . "', '1', 'NOW()')");
            }
        }
        return ($page_view_ip_mode == 'b' ? false : true);
    }

    function send_email_notification($pass_message = '', $pass_subject = 'Page View Module', $pass_action = 'notify') {
        switch ($pass_action) {
            case 'notify':
                $emails_array = array();
                $full_emails_array = array();
                foreach ($this->xml_array['page_view_module']['_c']['notification']['_c']['ip_banned']['_c']['emails']['_c']['email'] as $emails_loop) {
                    if (isset($emails_loop['_c']['address']['_v'])) {
                        $emails_array[] = $emails_loop['_c']['address']['_v'];
                        $full_emails_array[] = $emails_loop['_c']['name']['_v'] . " <" . $emails_loop['_c']['address']['_v'] . ">";
                    } else if (isset($emails_loop['address']['_v'])) {
                        $emails_array[] = $emails_loop['address']['_v'];
                        $full_emails_array[] = $emails_loop['name']['_v'] . " <" . $emails_loop['address']['_v'] . ">";
                    }
                }

                if (count($emails_array) && tep_not_null($pass_message)) {
                    $emails_str = implode(",", $emails_array);
                    @tep_mail($emails_str, $emails_str, $pass_subject, $pass_message, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
                }
                break;
        }
    }

    function get_current_url() {
        $param_str = '';
        foreach ($_GET as $get_field => $get_data) {
            if ($param_str != '') {
                $param_str .= '&';
            }
            $param_str .= $get_field . '=' . $get_data;
        }
        if ($param_str != '')
            $param_str = '?' . $param_str;
        return $_SERVER["PHP_SELF"] . $param_str;
    }

    function set_tags() {
        $this->tags_names = array();
        $this->tags_configurations = array();
        $this->files_name_array = array();
        foreach ($this->xml_array['page_view_module']['_c']['tags']['_c'] as $tag_configurations_mode_name_loop => $tag_configurations_mode_data_loop) {
            $this->tags_names[] = $tag_configurations_mode_name_loop;
            foreach ($tag_configurations_mode_data_loop['_c']['configurations']['_c'] as $mode_name_loop => $mode_data_loop) {
                $this->tag_configurations_mode[$tag_configurations_mode_name_loop][$mode_name_loop] = $mode_data_loop['_v'];
            }
        }
        return $this->tags_names;
    }

    function get_tags_names() {
        return $this->tags_names;
    }

    function set_settings() {
        $this->settings = array();
        if (isset($this->xml_array['page_view_module']['_c']['setting']['_c']) && count($this->xml_array['page_view_module']['_c']['setting']['_c'])) {
            foreach ($this->xml_array['page_view_module']['_c']['setting']['_c'] as $settings_name_loop => $settings_data_loop) {
                $this->settings[$settings_name_loop] = $settings_data_loop['_v'];
            }
        }
        return $this->settings;
    }

    function set_tags_files_data($pass_tags) {
        $count = 0;
        foreach ($this->xml_array['page_view_module']['_c']['tags']['_c'][$pass_tags]['_c']['files']['_c']['file'] as $files_index => $files_datas) {
            if (isset($files_datas['_c'])) {
                $this->files_name_array[$pass_tags][$count][$files_datas['_c']['name']['_v']] = (isset($files_datas['_c']['param']['_c']) ? $files_datas['_c']['param']['_c'] : array());
            } else if (isset($files_datas['name']) && isset($files_datas['param'])) {
                $this->files_name_array[$pass_tags][$count][$files_datas['name']['_v']] = $files_datas['param']['_c'];
            } else if (isset($files_datas['name'])) {
                $this->files_name_array[$pass_tags][$count][$files_datas['name']['_v']] = array();
            }
            $count++;
        }
        return $this->files_name_array;
    }

    function get_tags_files_data() {
        return $this->files_name_array;
    }

    function get_current_tags($pass_url = '') {
        $param_array = array();
        if ($pass_url == '') {
            foreach ($_GET as $get_field => $get_data) {
                $param_array[] = $get_field;
            }
            $path_parts = pathinfo($_SERVER["PHP_SELF"]);
            $current_script_name = $path_parts["basename"];
        } else {

            $path_parts = pathinfo($pass_url);
            if (strpos($path_parts["basename"], '?')) {
                $current_script_name = substr($path_parts["basename"], 0, strpos($path_parts["basename"], '?'));
            } else {
                $current_script_name = $path_parts["basename"];
            }

            if ($path_parts["dirname"]{0} == '/' && $path_parts["dirname"] != '/') {
                $current_script_name = substr($path_parts["dirname"], 1) . '/' . $current_script_name;
            } else if ($path_parts["dirname"]{0} == '\\' && $path_parts["dirname"] != '\\') {
                $current_script_name = substr($path_parts["dirname"], 1) . '\\' . $current_script_name;
            }

            $current_param_str = substr($path_parts["extension"], strpos($path_parts["extension"], '?') + 1);
            if (tep_not_null($current_param_str) && strpos($current_param_str, '&')) {
                $current_param_array = explode("&", $current_param_str);
                foreach ($current_param_array as $current_param_data) {
                    $param_array[] = (tep_not_null(substr($current_param_data, 0, strpos($current_param_data, '='))) ? substr($current_param_data, 0, strpos($current_param_data, '=')) : '');
                }
            } else {
                if (strpos($current_param_str, '=')) {
                    $param_array[] = substr($current_param_str, 0, strpos($current_param_str, '='));
                }
            }
        }

        foreach ($this->get_tags_names() as $tags) {
            $this->set_tags_files_data($tags);
        }

        foreach ($this->get_tags_files_data() as $tags => $tags_data) {
            foreach ($tags_data as $file_index => $filedatas) {
                foreach ($filedatas as $filenames => $filedatas_data) {
                    //if ($current_script_name == $filenames) {  // cant capture joomla script
                    if (substr($current_script_name, 0, strlen($filenames)) == $filenames) {
                        $matched_flag = true;
                        if (!count($filedatas_data)) {
                            return $tags;
                        } else if (count($param_array)) {
                            foreach ($filedatas_data as $filedatas_index_loop => $filedatas_data_loop) {
                                if (!in_array($filedatas_index_loop, $param_array)) {
                                    $matched_flag = false;
                                }
                            }
                        } else if (!count($param_array) && count($filedatas_data)) {
                            $matched_flag = false;
                        }
                        if ($matched_flag) {
                            return $tags;
                        }
                    }
                }
            }
        }
        return '';
    }

    function get_ip() {
        return $this->ip_address;
    }

}

?>