<?php

class Pagination {
	var $php_self;
	var $rows_per_page = 0; //Number of records to display per page
	var $total_rows = 0; //Total number of rows returned by the query
	var $links_per_page = 5; //Number of links to display per page
	var $append = ""; //Paremeters to append to pagination links
	var $sql = "";
	var $debug = false;
	var $conn = false;
	var $page = 1;
	var $max_pages = 0;
	var $offset = 0;
	var $is_array = false;
	var $array_content = array();
	var $href_js = '';
	var $page_last_records = 0;
	
	/**
	 * Constructor
	 *
	 * @param resource $connection Mysql connection link
	 * @param string $sql SQL query to paginate. Example : SELECT * FROM users
	 * @param integer $rows_per_page Number of records to display per page. Defaults to 10
	 * @param integer $links_per_page Number of links to display per page. Defaults to 5
	 * @param string $append Parameters to be appended to pagination links 
	 */
	
	function Pagination($array_content = array(), $connection = null, $sql = null, $rows_per_page = 50, $links_per_page = 5, $append = "") {
		if (tep_not_null($array_content)) {
			$this->is_array = true;
			$this->array_content = $array_content;
		} else {
			$this->conn = $connection;
			$this->sql = $sql;
		}
		
		$this->rows_per_page = (int)$rows_per_page;
		
		if (intval($links_per_page ) > 0) {
			$this->links_per_page = (int)$links_per_page;
		} else {
			$this->links_per_page = 5;
		}
		$this->append = $append;
		$this->php_self = htmlspecialchars($_SERVER['PHP_SELF'] );
		if (isset($_REQUEST['page'] )) {
			$this->page = intval($_REQUEST['page'] );
		}
	}
	
	/**
	 * Executes the SQL query and initializes internal variables
	 *
	 * @access public
	 * @return resource
	 */
	function paginate() {
		//Check for valid mysql connection
		if ($this->is_array) {
			$this->total_rows = count($this->array_content);
			//Return FALSE if no rows found
			if ($this->total_rows == 0) {
				if ($this->debug)
					echo "Query returned zero rows.";
				return FALSE;
			}
			
			//Max number of pages
			$this->max_pages = ceil($this->total_rows / $this->rows_per_page );
			if ($this->links_per_page > $this->max_pages) {
				$this->links_per_page = $this->max_pages;
			}
			
			//Check the page value just in case someone is trying to input an aribitrary value
			if ($this->page > $this->max_pages || $this->page <= 0) {
				$this->page = 1;
			}
			
			//Calculate Offset
			$this->offset = $this->rows_per_page * ($this->page - 1);
			$this->page_last_records = ($this->page == $this->max_pages) ? $this->total_rows : ($this->rows_per_page*$this->page);
			
			//Fetch the required result set
			$rs = array_chunk($this->array_content, $this->rows_per_page);
			
			if (!isset($rs[($this->page - 1)])) {
				if ($this->debug)
					echo "Pagination for array failed. Check your query.";
				return false;
			} else {
				$rs = $rs[($this->page - 1)];
			}
		} else {
			if (! $this->conn || ! is_resource($this->conn )) {
				if ($this->debug)
					echo "MySQL connection missing<br />";
				return false;
			}
			
			//Find total number of rows
			$all_rs = @mysql_query($this->sql );
			if (! $all_rs) {
				if ($this->debug)
					echo "SQL query failed. Check your query.<br /><br />Error Returned: " . mysql_error();
				return false;
			}
			$this->total_rows = mysql_num_rows($all_rs );
			@mysql_close($all_rs );
			
			//Return FALSE if no rows found
			if ($this->total_rows == 0) {
				if ($this->debug)
					echo "Query returned zero rows.";
				return FALSE;
			}
			
			//Max number of pages
			$this->max_pages = ceil($this->total_rows / $this->rows_per_page );
			if ($this->links_per_page > $this->max_pages) {
				$this->links_per_page = $this->max_pages;
			}
			
			//Check the page value just in case someone is trying to input an aribitrary value
			if ($this->page > $this->max_pages || $this->page <= 0) {
				$this->page = 1;
			}
			
			//Calculate Offset
			$this->offset = $this->rows_per_page * ($this->page - 1);
			
			//Fetch the required result set
			$rs = @mysql_query($this->sql . " LIMIT {$this->offset}, {$this->rows_per_page}" );
			if (! $rs) {
				if ($this->debug)
					echo "Pagination query failed. Check your query.<br /><br />Error Returned: " . mysql_error();
				return false;
			}
		}
		
		return $rs;
	}
	
	/**
	 * Display the link to the first page
	 *
	 * @access public
	 * @param string $tag Text string to be displayed as the link. Defaults to 'First'
	 * @return string
	 */
	function renderFirst($tag = 'First') {
		if ($this->total_rows == 0)
			return FALSE;
		
		if ($this->page == 1) {
			return '';
		} else {
			return '<div class="lfloat">' . tep_image_button2('gray_short', $this->get_href(1), $tag) . '</div>';
		}
	}
	
	/**
	 * Display the link to the last page
	 *
	 * @access public
	 * @param string $tag Text string to be displayed as the link. Defaults to 'Last'
	 * @return string
	 */
	function renderLast($tag = 'Last') {
		if ($this->total_rows == 0)
			return FALSE;
		
		if ($this->page == $this->max_pages) {
			return '';
		} else {
			return '<div class="lfloat">' . tep_image_button2('gray_short', $this->get_href($this->max_pages), $tag) . '</div>';
		}
	}
	
	/**
	 * Display the next link
	 *
	 * @access public
	 * @param string $tag Text string to be displayed as the link. Defaults to '>>'
	 * @return string
	 */
	function renderNext($tag = '&gt;') {
		if ($this->total_rows == 0)
			return FALSE;
		
		if ($this->page < $this->max_pages) {
			return '<div class="lfloat">' . tep_image_button2('gray_short', $this->get_href($this->page + 1), $tag) . '</div>';
		} else {
			return '';
		}
	}
	
	/**
	 * Display the previous link
	 *
	 * @access public
	 * @param string $tag Text string to be displayed as the link. Defaults to '<<'
	 * @return string
	 */
	function renderPrev($tag = '&lt;') {
		if ($this->total_rows == 0)
			return FALSE;
		
		if ($this->page > 1) {
			return '<div class="lfloat">' . tep_image_button2('gray_short', $this->get_href($this->page - 1), $tag) . '</div>';
		} else {
			return '';
		}
	}
	
	/**
	 * Display the page links
	 *
	 * @access public
	 * @return string
	 */
	function renderNav($prefix = '<span class="hds3 lfloat" style="display: inline-block;padding: 6px 10px;vertical-align: top;">', $suffix = '</span>') {
		if ($this->total_rows == 0)
			return FALSE;
		
        //$batch = ceil($this->page / $this->links_per_page );
		//$end = ($batch * $this->links_per_page) - (($this->page % $this->links_per_page) ? ($batch - 1) : 0);
		
        if ($this->page > 1) {
            $val = ($this->page - 1)/($this->links_per_page-1);
            $val_fr = floor($val);
            $batch = ceil($val);
        } else {
            $batch = 1;
        }
        
        $end = ($batch * $this->links_per_page) - (($val_fr == $$batch) ? 0 : ($batch - 1));
        
		if ($end == $this->page) {
			$end = $end + $this->links_per_page - 1;
//			$end = $end + ceil($this->links_per_page/2);
		}
        
		if ($end > $this->max_pages) {
			$end = $this->max_pages;
		}
        
		$start = $end - $this->links_per_page + 1;
		$links = '';
		
		if ($end != 1) {
			for($i = $start; $i <= $end; $i ++) {
				if ($i == $this->page) {
					$links .= $prefix . $i . $suffix;
				} else {
					$links .= '<div class="lfloat">' . tep_image_button2('gray_short', $this->get_href($i), $i) . '</div>';
				}
			}
		}
		
		return $links;
	}
	
	/**
	 * Display full pagination navigation
	 *
	 * @access public
	 * @return string
	 */
	function renderFullNav() {
		return $this->renderFirst(BUTTON_FIRST) . $this->renderPrev() . $this->renderNav() .  $this->renderNext() . $this->renderLast(BUTTON_LAST);
	}
	
	function get_href ($page) {
		$return_string = '';
		
		if (tep_not_empty($this->href_js)) {
			$return_string = sprintf($this->href_js, $page);
		} else {
			$return_string = $this->php_self . '?page=' . $page . '&' . $this->append;
		}
		
		return $return_string;	
	}
	
	function set_href_js ($js_script) {
		$this->href_js = $js_script;
	}
	
	/**
	 * Set debug mode
	 *
	 * @access public
	 * @param bool $debug Set to TRUE to enable debug messages
	 * @return void
	 */
	function setDebug($debug) {
		$this->debug = $debug;
	}
}
?>
