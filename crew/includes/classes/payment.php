<?php

/*
  $Id: payment.php,v 1.33 2014/08/13 07:14:17 ahsan.atiq Exp $

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2003 osCommerce

  Released under the GNU General Public License
 */

class payment {

    var $modules, $selected_module;

    // class constructor
    function payment($module = '') {
        // BOF: WebMakers.com Added: Downloads Controller
        global $payment, $language, $PHP_SELF, $cart, $languages_id;
        // EOF: WebMakers.com Added: Downloads Controller

        if ((tep_not_null($module))) {
            $payment_info_array = explode(':~:', $module);

            if (sizeof($payment_info_array) > 1) {
                $payment_gateway_code = $payment_info_array[1];
                $payment_methods_id = $payment_info_array[2];
            } else {
                $payment_gateway_code = $module;
            }

            $filename_select_sql = "SELECT payment_methods_filename
									FROM " . TABLE_PAYMENT_METHODS . "
									WHERE payment_methods_parent_id = 0
										AND payment_methods_receive_status = 1
										AND payment_methods_filename = '" . tep_db_input($payment_gateway_code . '.php') . "'";
            $filename_result_sql = tep_db_query($filename_select_sql);
            if ($filename_row = tep_db_fetch_array($filename_result_sql)) {
                $this->selected_module = $payment_gateway_code;
                $include_modules[] = array('class' => $payment_gateway_code, 'file' => $filename_row['payment_methods_filename'], 'selected_pm_id' => $payment_methods_id);
                $this->modules[] = $filename_row['payment_methods_filename'];
            }
        } else {
            // BOF: WebMakers.com Added: Downloads Controller - Free Shipping and Payments
            // Show either normal payment modules or free payment module when Free Shipping Module is On
            // Free Payment Only

            if (tep_get_configuration_key_value('MODULE_PAYMENT_FREECHARGER_STATUS') and ($cart->show_total() == 0 and $cart->show_weight() == 0)) {
                $this->selected_module = $module;
                $include_modules[] = array('class' => 'freecharger', 'file' => 'freecharger.php');
            } else {
                $filename_select_sql = "SELECT payment_methods_filename, payment_methods_id
										FROM " . TABLE_PAYMENT_METHODS . "
										WHERE payment_methods_parent_id = 0
											AND payment_methods_receive_status = 1
											AND (payment_methods_filename <> NULL OR payment_methods_filename <> '')";
                $filename_result_sql = tep_db_query($filename_select_sql);
                while ($filename_row = tep_db_fetch_array($filename_result_sql)) {
                    $this->modules[] = $filename_row['payment_methods_filename'];
                }

                while (list(, $value) = each($this->modules)) {
                    $class = substr($value, 0, strrpos($value, '.'));
                    // Don't show Free Payment Module
                    if ($class != 'freecharger') {
                        $include_modules[] = array('class' => $class, 'file' => $value);
                    }
                }
            }
        }

        for ($i = 0, $n = sizeof($include_modules); $i < $n; $i++) {
            if (file_exists(DIR_WS_MODULES . 'payment/' . $include_modules[$i]['file'])) {
                include_once(DIR_WS_LANGUAGES . $language . '/modules/payment/' . $include_modules[$i]['file']);
                include_once(DIR_WS_MODULES . 'payment/' . $include_modules[$i]['file']);

                if (isset($include_modules[$i]['selected_pm_id']) && tep_not_null($include_modules[$i]['selected_pm_id'])) {
                    $GLOBALS[$include_modules[$i]['class']] = new $include_modules[$i]['class']($include_modules[$i]['selected_pm_id']);
                } else {
                    $GLOBALS[$include_modules[$i]['class']] = new $include_modules[$i]['class'];
                }
            }
        }

        if (!isset($GLOBALS[$payment]) || (isset($GLOBALS[$payment]) && !is_object($GLOBALS[$payment]))) {
            if (is_object($GLOBALS[$payment_gateway_code])) {
                if ($GLOBALS[$payment_gateway_code]->enabled) {
                    $payment = $include_modules[0]['class'];
                }
            }
        }

        if ((tep_not_null($module)) && (is_array($this->modules) && in_array($module, $this->modules)) && (isset($GLOBALS[$module]->form_action_url))) {
            $this->form_action_url = $GLOBALS[$module]->form_action_url;
        }
    }

    // class methods
    /* The following method is needed in the checkout_confirmation.php page
      due to a chicken and egg problem with the payment class and order class.
      The payment modules needs the order destination data for the dynamic status
      feature, and the order class needs the payment module title.
      The following method is a work-around to implementing the method in all
      payment modules available which would break the modules in the contributions
      section. This should be looked into again post 2.2.
     */

    function is_supported_currency($selected_currency) {
        if (is_array($this->modules)) {
            if (is_object($GLOBALS[$this->selected_module])) {
                $supported_currency = $GLOBALS[$this->selected_module]->is_supported_currency($selected_currency);
            }
        }

        return $supported_currency;
    }

    function update_status() {
        if (is_array($this->modules)) {
            if (is_object($GLOBALS[$this->selected_module])) {
                if (function_exists('method_exists')) {
                    if (method_exists($GLOBALS[$this->selected_module], 'update_status')) {
                        $GLOBALS[$this->selected_module]->update_status();
                    }
                } else { // PHP3 compatibility
                    @call_user_method('update_status', $GLOBALS[$this->selected_module]);
                }
            }
        }
    }

    function get_confirm_complete_days() {
        if (is_array($this->modules)) {
            if (is_object($GLOBALS[$this->selected_module])) {
                $confirm_complete_days = $GLOBALS[$this->selected_module]->get_confirm_complete_days();
            }
        }

        return $confirm_complete_days;
    }

    function get_require_address_information() {
        if (is_array($this->modules)) {
            if (is_object($GLOBALS[$this->selected_module])) {
                $confirm_complete_days = $GLOBALS[$this->selected_module]->get_require_address_information();
            }
        }

        return $confirm_complete_days;
    }

    function javascript_validation() {
        $js = '';
        if (is_array($this->modules)) {
            $js = '<script language="javascript"><!-- ' . "\n" .
                    'function check_form() {' . "\n" .
                    '  	var error = 0;' . "\n" .
                    '  	var error_message = "' . JS_ERROR . '";' . "\n" .
                    '  	var payment_value = null;' . "\n" .
                    '  	if (document.checkout_payment.payment != null) {' . "\n" .
                    '  		if (document.checkout_payment.payment.length) {' . "\n" .
                    '   		for (var i=0; i<document.checkout_payment.payment.length; i++) {' . "\n" .
                    '      			if (document.checkout_payment.payment[i].checked && document.checkout_payment.payment[i].value !=\'\') {' . "\n" .
                    '        			payment_value = document.checkout_payment.payment[i].value;' . "\n" .
                    '      			}' . "\n" .
                    '    		}' . "\n" .
                    '  		} else if (document.checkout_payment.payment.checked) {' . "\n" .
                    '    		payment_value = document.checkout_payment.payment.value;' . "\n" .
                    '  		} else if (document.checkout_payment.payment.value) {' . "\n" .
                    '    		payment_value = document.checkout_payment.payment.value;' . "\n" .
                    '  		}' . "\n" .
                    '  	} else { payment_value = \'\' } ' . "\n\n";

            reset($this->modules);

            while (list(, $value) = each($this->modules)) {
                $class = substr($value, 0, strrpos($value, '.'));
                if ($GLOBALS[$class]->enabled) {
                    $js .= $GLOBALS[$class]->javascript_validation();
                }
            }

            $js .= "\n" . '  if (payment_value == null && submitter != 1) {' . "\n" . // ICW CREDIT CLASS Gift Voucher System
                    '    error_message = error_message + "' . JS_ERROR_NO_PAYMENT_MODULE_SELECTED . '";' . "\n" .
                    '    error = 1;' . "\n" .
                    '  }' . "\n\n" .
                    // Gift Voucher System Line below amended
                    '  if (error == 1 && submitter != 1) {' . "\n" .
                    '    alert(error_message);' . "\n" .
                    '    return false;' . "\n" .
                    '  } else {' . "\n" .
                    '    return true;' . "\n" .
                    '  }' . "\n" .
                    '}' . "\n" .
                    'function payment_switch_currency(pm, cur, pop_msg) {' . "\n" .
                    '  	var payment_filename = \'' . tep_href_link(FILENAME_CHECKOUT_PAYMENT, 'currency=\'+cur+\'&payment=\'+pm') . ';' . "\n" .
                    '	if (pop_msg == 1) { ' . "\n" .
                    '	    var answer = confirm(\'Are you sure to change the Checkout Currency?\')' . "\n" .
                    '	    if (answer != 0) { ' . "\n" .
                    '   	    location = payment_filename;' . "\n" .
                    '	    }' . "\n" .
                    '	} else {' . "\n" .
                    ' 	    location = payment_filename;' . "\n" .
                    '	}' . "\n" .
                    '}' . "\n" .
                    '//--></script>' . "\n";
        }

        return $js;
    }

    function get_customers_groups_payment_methods($customers_groups_id) {
        $customers_groups_select_sql = "SELECT customers_groups_payment_methods
										FROM " . TABLE_CUSTOMERS_GROUPS . "
										WHERE customers_groups_id = '" . (int) $customers_groups_id . "'";
        $customers_groups_result_sql = tep_db_query($customers_groups_select_sql);
        $customers_groups_row = tep_db_fetch_array($customers_groups_result_sql);
        $selected_payment_methods_array = (isset($customers_groups_row['customers_groups_payment_methods']) && tep_not_null($customers_groups_row['customers_groups_payment_methods']) ? explode(",", $customers_groups_row['customers_groups_payment_methods']) : array());
        return $selected_payment_methods_array;
    }

    function selection($selected_currency, &$payment) {
        global $zone_info_array, $customer_id, $customers_groups_id;

        $payment_methods_array = array();
        $zone_payment_gateway_id_array = array();
        $zone_payment_gateway_nrp_array = array();

        $is_nrp_customer = tep_is_nrp_customer($customer_id);

        if (isset($zone_info_array[4]->zone_payment_gateway_id) && is_array($zone_info_array[4]->zone_payment_gateway_id)) {
            $zone_payment_gateway_id_array = $zone_info_array[4]->zone_payment_gateway_id;
        }

        if (isset($zone_info_array[4]->zone_payment_gateway_nrp) && is_array($zone_info_array[4]->zone_payment_gateway_nrp)) {
            $zone_payment_gateway_nrp_array = $zone_info_array[4]->zone_payment_gateway_nrp;
        }

        if (is_array($this->modules)) {
            reset($this->modules);

            if (tep_not_null($payment)) {
                $selected_payment_array = explode(':~:', $payment);

                if (sizeof($selected_payment_array) < 3) {
                    $payment = '';
                }
            }

            $selected_payment_methods_array = $this->get_customers_groups_payment_methods($customers_groups_id);

            while (list(, $value) = each($this->modules)) {
                $class = substr($value, 0, strrpos($value, '.'));
                if ($GLOBALS[$class]->enabled) {

                    $selection = $GLOBALS[$class]->selection();

                    foreach ($selection as $pm_info_array) {
                        $show_payment = false;

                        if (in_array($pm_info_array['payment_methods_id'], $zone_payment_gateway_id_array) && in_array($pm_info_array['payment_methods_id'], $selected_payment_methods_array)) {
                            if ($is_nrp_customer) {
                                if ($pm_info_array['confirm_complete_days'] <= 0) {
                                    $show_payment = true; // This is for the rules Show NRP PM for NRP Customer Only
                                }
                            } else {
                                if (!in_array($pm_info_array['payment_methods_id'], $zone_payment_gateway_nrp_array)) {
                                    $show_payment = true;
                                }
                            }

                            if ($show_payment) {
                                if (sizeof($pm_info_array['currency']) > 0 && in_array($selected_currency, $pm_info_array['currency'])) {
                                    $pm_info_temp_array['payment_gateway_code'] = $pm_info_array['payment_gateway_code'];
                                    $pm_info_temp_array['payment_methods_receive_status_mode'] = ((int) $GLOBALS[$class]->enabled != 1 ? (int) $GLOBALS[$class]->enabled : $pm_info_array['payment_methods_receive_status_mode']);
                                    $pm_info_temp_array['currency'] = $pm_info_array['currency'];
                                    $pm_info_temp_array['payment_methods_description_title'] = $pm_info_array['payment_methods_description_title'];
                                    $pm_info_temp_array['payment_methods_logo'] = $pm_info_array['payment_methods_logo'];

                                    $pm_info_temp_array['show_billing_address'] = $pm_info_array['show_billing_address'];
                                    $pm_info_temp_array['show_contact_number'] = (isset($pm_info_array['show_contact_number']) ? $pm_info_array['show_contact_number'] : '');  // currently only for mobile money
                                    $pm_info_temp_array['show_ic'] = (isset($pm_info_array['show_ic']) ? $pm_info_array['show_ic'] : '');  // currently only for mobile money

                                    $pm_info_temp_array['show_city'] = (isset($pm_info_array['show_city']) ? $pm_info_array['show_city'] : '');
                                    $pm_info_temp_array['show_zip'] = (isset($pm_info_array['show_zip']) ? $pm_info_array['show_zip'] : '');
                                    $pm_info_temp_array['show_surname'] = (isset($pm_info_array['show_surname']) ? $pm_info_array['show_surname'] : '');
                                    $pm_info_temp_array['show_housenumber'] = (isset($pm_info_array['show_housenumber']) ? $pm_info_array['show_housenumber'] : '');
                                    $pm_info_temp_array['show_street'] = (isset($pm_info_array['show_street']) ? $pm_info_array['show_street'] : '');
                                    $pm_info_temp_array['show_accountname'] = (isset($pm_info_array['show_accountname']) ? $pm_info_array['show_accountname'] : '');
                                    $pm_info_temp_array['show_accountnumber'] = (isset($pm_info_array['show_accountnumber']) ? $pm_info_array['show_accountnumber'] : '');
                                    $pm_info_temp_array['show_directdebittext'] = (isset($pm_info_array['show_directdebittext']) ? $pm_info_array['show_directdebittext'] : '');
                                    $pm_info_temp_array['show_bankcode'] = (isset($pm_info_array['show_bankcode']) ? $pm_info_array['show_bankcode'] : '');
                                    $pm_info_temp_array['show_branchcode'] = (isset($pm_info_array['show_branchcode']) ? $pm_info_array['show_branchcode'] : '');
                                    $pm_info_temp_array['show_vouchernumber'] = (isset($pm_info_array['show_vouchernumber']) ? $pm_info_array['show_vouchernumber'] : '');
                                    $pm_info_temp_array['show_vouchervalue'] = (isset($pm_info_array['show_vouchervalue']) ? $pm_info_array['show_vouchervalue'] : '');

                                    if (isset($pm_info_array['payment_methods_status_message'])) {
                                        $pm_info_temp_array['payment_methods_status_message'] = $pm_info_array['payment_methods_status_message'];
                                    }

                                    $payment_methods_array[$pm_info_array['payment_methods_types_id']][$pm_info_array['payment_methods_parent_id']][$pm_info_array['payment_methods_id']] = $pm_info_temp_array;

                                    if (tep_not_null($payment)) {
                                        if ($selected_payment_array[2] == $pm_info_array['payment_methods_id']) {
                                            $payment_methods_array[$pm_info_array['payment_methods_types_id']]['show'] = 1;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        if (!tep_not_null($payment)) {
            $payment_methods_id_select_sql = "	SELECT statistic_value AS payment_methods_id
                                                FROM " . TABLE_CUSTOMERS_PURCHASE_STATISTIC . "
                                                WHERE customers_id = '" . (int) $customer_id . "'
                                                    AND statistic_key = 'last_payment_method_id'";
            $payment_methods_id_result_sql = tep_db_query($payment_methods_id_select_sql);
            if ($payment_methods_id_row = tep_db_fetch_array($payment_methods_id_result_sql)) {
                $pm_info_select_sql = "	SELECT pm.payment_methods_types_id, parent_pm.payment_methods_code, parent_pm.payment_methods_id AS parent_payment_methods_id
		  								FROM " . TABLE_PAYMENT_METHODS . " AS pm
		  								INNER JOIN " . TABLE_PAYMENT_METHODS . " AS parent_pm
		  									ON (pm.payment_methods_parent_id = parent_pm.payment_methods_id)
		  								WHERE pm.payment_methods_id = '" . (int) $payment_methods_id_row['payment_methods_id'] . "'
		  									AND pm.payment_methods_receive_status_mode = 1";
                $pm_info_result_sql = tep_db_query($pm_info_select_sql);
                if ($pm_info_row = tep_db_fetch_array($pm_info_result_sql)) {
                    if (isset($payment_methods_array[$pm_info_row['payment_methods_types_id']][$pm_info_row['parent_payment_methods_id']][$payment_methods_id_row['payment_methods_id']])) {
                        $payment = 'pm_' . $pm_info_row['payment_methods_types_id'] . ':~:' . $pm_info_row['payment_methods_code'] . ':~:' . $payment_methods_id_row['payment_methods_id'];
                        $payment_methods_array[$pm_info_row['payment_methods_types_id']]['show'] = 1;
                    }
                }
            }
        }
        
        return $payment_methods_array;
    }

    function draw_other_payment_selection($other_payment_array, $current_currency, $available_currency) {
        $menu_output = '';

        if (is_array($other_payment_array) && count($other_payment_array)) {
            $menu_output = '<ul id="other_pm_nav">
								<li><a href="javascript:;" style="display: block; border: 1px solid #336699; width: 160px;">&nbsp;&nbsp;Others</a>
									<ul>';
            foreach ($other_payment_array as $pm_code => $payment_method_info) {
                if (is_array($payment_method_info['currency'])) {
                    $supported_currency = array_intersect($available_currency, $payment_method_info['currency']);

                    if (count($supported_currency)) {
                        $show_this_payment = false;

                        if (in_array($current_currency, $supported_currency)) {
                            if (count($supported_currency) >= 2)
                                $show_this_payment = true;
                        } else if (count($supported_currency) >= 1) {
                            $show_this_payment = true;
                        }

                        if ($show_this_payment) {
                            $menu_output .= '<li><a href="javascript:;">&nbsp;&nbsp;' . $payment_method_info['name'] . '</a>
												<ul>';
                            foreach ($supported_currency as $cur_value) {
                                if ($cur_value != $current_currency) {
                                    $menu_output .= '<li><a href="javascript:payment_switch_currency(\'' . $pm_code . '\', \'' . $cur_value . '\', \'0\')">&nbsp;&nbsp;' . $cur_value . '</a></li>';
                                }
                            }
                            $menu_output .= '	</ul>
											</li>';
                        }
                    }
                }
            }
            $menu_output .= '		</ul>
								</li>
							</ul>';
        }

        return $menu_output;
    }

    //ICW CREDIT CLASS Gift Voucher System
    // Check credit covers was setup to test whether credit covers is set in other parts of the code
    function check_credit_covers() {
        global $credit_covers;

        return tep_session_is_registered('credit_covers') && $credit_covers; // Must be a session variable since in ot_total.php it only been unregister tep_session_unregister('credit_covers') but not unset()
    }

    function pre_confirmation_check() {
        global $credit_covers, $payment_modules; //ICW CREDIT CLASS Gift Voucher System
        if (is_array($this->modules)) {
            if (is_object($GLOBALS[$this->selected_module]) && ($GLOBALS[$this->selected_module]->enabled)) {
                if ($credit_covers) { // Gift Voucher System
                    $GLOBALS[$this->selected_module]->enabled = false; //ICW CREDIT CLASS Gift Voucher System
                    $GLOBALS[$this->selected_module] = NULL; //ICW CREDIT CLASS Gift Voucher System
                    $payment_modules = ''; //ICW CREDIT CLASS Gift Voucher System
                } else { //ICW CREDIT CLASS Gift Voucher System
                    $GLOBALS[$this->selected_module]->pre_confirmation_check();
                }
            }
        }
    }

//ICW CREDIT CLASS Gift Voucher System

    function confirmation() {
        if (is_array($this->modules)) {
            if (is_object($GLOBALS[$this->selected_module]) && ($GLOBALS[$this->selected_module]->enabled)) {
                return $GLOBALS[$this->selected_module]->confirmation();
            }
        }
    }

    function process_button() {
        if (is_array($this->modules)) {
            if (is_object($GLOBALS[$this->selected_module]) && ($GLOBALS[$this->selected_module]->enabled)) {
                return $GLOBALS[$this->selected_module]->process_button();
            }
        }
    }

    function before_process() {
        if (is_array($this->modules)) {
            if (is_object($GLOBALS[$this->selected_module]) && ($GLOBALS[$this->selected_module]->enabled)) {
                return $GLOBALS[$this->selected_module]->before_process();
            }
        }
    }

    function after_process() {
        if (is_array($this->modules)) {
            if (is_object($GLOBALS[$this->selected_module]) && ($GLOBALS[$this->selected_module]->enabled)) {
                return $GLOBALS[$this->selected_module]->after_process();
            }
        }
    }

    function draw_confirm_button() {
        if (is_array($this->modules)) {
            if (is_object($GLOBALS[$this->selected_module]) && ($GLOBALS[$this->selected_module]->enabled)) {
                if (method_exists($GLOBALS[$this->selected_module], 'draw_confirm_button')) {
                    return $GLOBALS[$this->selected_module]->draw_confirm_button();
                } else {
                    return tep_image_button2('red', 'javascript:document.checkout_confirmation.submit()', IMAGE_BUTTON_CONFIRM_ORDER, 200);
                }
            }
        }
    }

    function get_confirm_button_caption() {
        if (is_array($this->modules)) {
            if (is_object($GLOBALS[$this->selected_module]) && ($GLOBALS[$this->selected_module]->enabled)) {
                if (method_exists($GLOBALS[$this->selected_module], 'get_confirm_button_caption')) {
                    $confirm_button_caption = $GLOBALS[$this->selected_module]->get_confirm_button_caption();
                    return sprintf(TEXT_INFO_PAYMENT_CONFIRM_CAPTION, $confirm_button_caption);
                } else {
                    return tep_image_button2('red', 'javascript:document.checkout_confirmation.submit()', IMAGE_BUTTON_CONFIRM_ORDER, 200);
                }
            }
        }
    }

    function get_error() {
        if (is_array($this->modules)) {
            if (is_object($GLOBALS[$this->selected_module]) && ($GLOBALS[$this->selected_module]->enabled)) {
                return $GLOBALS[$this->selected_module]->get_error();
            }
        }
    }

    function get_pm_status($pass_pm) {
        if (isset($GLOBALS[$pass_pm])) {
            return $GLOBALS[$pass_pm]->get_pm_status();
        }
        return '';
    }

    function sort_selection($payment_gateway) {
        $payment_modules = array();

        // convert 3-dimentional array into 1-dimentional array
        foreach ($payment_gateway as $pt_key => $pt_array) {
            foreach ($pt_array as $pg_key => $pg_array) {
                if (isset($pg_array) && is_array($pg_array)) {
                    foreach ($pg_array as $pm_key => $pm_array) {
                        $pm_array['pt_key'] = $pt_key;
                        $pm_array['pg_key'] = $pg_key;
                        $payment_modules[$pm_key] = $pm_array;
                    }
                }
            }
        }

        $payment_gateway = array();
        // get payment modules sorting from db
        $payment_methods_select_sql = "SELECT
                                        pm.payment_methods_id as id,
                                        pm.payment_methods_title as title,
                                        pm.payment_methods_sort_order as sort,
                                        pm.payment_methods_receive_featured_status as featured_status
								FROM " . TABLE_PAYMENT_METHODS . " as pm
								ORDER BY pm.payment_methods_sort_order, pm.payment_methods_title ";
        $payment_methods_result_sql = tep_db_query($payment_methods_select_sql);
        while ($row = tep_db_fetch_array($payment_methods_result_sql)) {
            if (isset($payment_modules[$row['id']])) {
                $payment_module = $payment_modules[$row['id']];
                $pt_key = $payment_module['pt_key'];
                $payment_module['sort'] = $row['sort'];
                $payment_gateway[$pt_key][$row['id']] = $payment_module;
                if($row['featured_status']=='1'){
                    $payment_gateway[0][$row['id']] = $payment_module;
                }
            }
        }

        return $payment_gateway;
    }

}

?>
