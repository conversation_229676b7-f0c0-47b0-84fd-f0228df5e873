<?

class payment_module_info {
    var $payment_accounts, $user_id, $user_role, $max_entry, $user_input_field_type;

	// class constructor
    function payment_module_info($user_id, $user_role) {
      	$this->payment_accounts = array();

		$this->_get_current_payment_accounts($user_id, $user_role);

		$this->user_id = $user_id;
		$this->user_role = $user_role;
		
		$this->user_input_field_type = array(1, 2, 3, 4);
		
		if ($this->user_role == 'supplier') {
			$this->max_entry = MAX_SUPPLIER_PAYMENT_BOOK_ENTRIES;
		} else {
			$this->max_entry = -1;	// Unlimited
		}
	}

	function _get_current_payment_accounts($user_id, $user_role) {
		$payment_acc_select_sql = "	SELECT pab.*, pm.payment_methods_send_mode_name, pm.payment_methods_send_currency, pm.payment_methods_send_available_sites 
									FROM " . TABLE_STORE_PAYMENT_ACCOUNT_BOOK . " AS pab
									INNER JOIN " . TABLE_PAYMENT_METHODS . " AS pm
										ON pab.payment_methods_id=pm.payment_methods_id
									WHERE pab.user_id = '" . tep_db_input($user_id) . "'
										AND pab.user_role = '" . tep_db_input($user_role) . "'
									ORDER BY pab.payment_methods_alias";
		$payment_acc_result_sql = tep_db_query($payment_acc_select_sql);

		while ($payment_acc_row = tep_db_fetch_array($payment_acc_result_sql)) {
			$this->payment_accounts[$payment_acc_row['store_payment_account_book_id']] = array(	'pm_id' => $payment_acc_row['payment_methods_id'],
																								'pm_alias' => $payment_acc_row['payment_methods_alias'],
																								'pm_name' => $payment_acc_row['payment_methods_send_mode_name'],
																								'pm_currency' => $payment_acc_row['payment_methods_send_currency'],
																								'pm_avail_sites' => $payment_acc_row['payment_methods_send_available_sites']);

			if (strstr($payment_acc_row['payment_methods_send_available_sites'], '0') === false) {
				//continue;
			}
			
			$payment_acc_field_select_sql = "	SELECT pabd.payment_methods_fields_id, pabd.payment_methods_fields_value, pmf.payment_methods_fields_title, pmf.payment_methods_fields_type  
												FROM " . TABLE_STORE_PAYMENT_ACCOUNT_BOOK_DETAILS . " AS pabd
												INNER JOIN " . TABLE_PAYMENT_METHODS_FIELDS . " AS pmf
													ON pabd.payment_methods_fields_id=pmf.payment_methods_fields_id
												WHERE pabd.store_payment_account_book_id = '" . tep_db_input($payment_acc_row['store_payment_account_book_id']) . "'
												ORDER BY pmf.payment_methods_fields_sort_order";
			$payment_acc_field_result_sql = tep_db_query($payment_acc_field_select_sql);

			while ($payment_acc_field_row = tep_db_fetch_array($payment_acc_field_result_sql)) {
				$this->payment_accounts[$payment_acc_row['store_payment_account_book_id']]['field'][] = $payment_acc_field_row;
			}
		}
	}

	function _get_all_active_send_payment_methods($include_option_title=false, $currency_id='') {
		$pm_array = array();
		if ($include_option_title)	$pm_array[] = array('id' => '', 'text' => PULL_DOWN_DEFAULT);

		$payment_method_select_sql = "	SELECT payment_methods_id, payment_methods_send_mode_name, payment_methods_send_available_sites
										FROM " . TABLE_PAYMENT_METHODS . "
										WHERE payment_methods_send_status = 1
											AND payment_methods_send_status_mode = 1 
											AND payment_methods_parent_id <> '0' 
											" . (tep_not_null($currency_id) ? "AND payment_methods_send_currency = '" . tep_db_input($currency_id) . "'" : "") . "
										ORDER BY payment_methods_sort_order";
		$payment_method_result_sql = tep_db_query($payment_method_select_sql);
		while ($payment_method_row = tep_db_fetch_array($payment_method_result_sql)) {
			if (strstr($payment_method_row['payment_methods_send_available_sites'], '0') !== false) {
				$pm_array[] = array('id' => $payment_method_row['payment_methods_id'], 'text' => $payment_method_row['payment_methods_send_mode_name']);
			}
		}

		return $pm_array;
	}
	
	function get_system_defined_payment_account_selection($include_option_title=false) {
		$pm_array = array();
		
		if ($include_option_title)	$pm_array[] = array('id' => '', 'text' => PULL_DOWN_DEFAULT);
		
		$payment_method_select_sql = " 	SELECT pm.payment_methods_id, pm.payment_methods_send_mode_name, pm.payment_methods_send_available_sites
										FROM . " . TABLE_PAYMENT_METHODS . " as pm
										INNER JOIN " . TABLE_PAYMENT_METHODS_TYPES . " AS pmt
											ON (pm.payment_methods_types_id=pmt.payment_methods_types_id 
												AND pmt.payment_methods_types_mode = 'SEND' 
												AND pmt.payment_methods_types_system_define = '1')";
		$payment_method_result_sql = tep_db_query($payment_method_select_sql);
		
		while ($payment_method_row = tep_db_fetch_array($payment_method_result_sql)) {
			if (strstr($payment_method_row['payment_methods_send_available_sites'], '0') !== false) {
				$pm_array[] = array('id' => $payment_method_row['payment_methods_id'], 'text' => $payment_method_row['payment_methods_send_mode_name']);
			}
		}
		
		return $pm_array;
	}
	
	function _get_existing_payment_account_selection($include_option_title=false) {
		$pm_array = array();

		if ($include_option_title)	$pm_array[] = array('id' => '', 'text' => PULL_DOWN_DEFAULT);

		if (isset($this->payment_accounts) && count($this->payment_accounts)) {
			foreach ($this->payment_accounts as $pm_book_id => $pm_book_info) {
				$pm_array[] = array('id' => $pm_book_id, 'text' => $pm_book_info['pm_alias']);
			}
		}

		return $pm_array;
	}
	
	function _get_user_particulars() {
		if ($this->user_role == 'supplier') {
			$user_info_select_sql = "	SELECT supplier_firstname AS fname, supplier_lastname AS lname, supplier_email_address AS email, supplier_telephone AS phone, supplier_disable_withdrawal as disable_withdrawal
										FROM " . TABLE_SUPPLIER . "
										WHERE supplier_id = '" . tep_db_input($this->user_id) . "'";
		} else {
			$user_info_select_sql = "	SELECT cust.customers_firstname AS fname, cust.customers_lastname AS lname, cust.customers_email_address AS email, cust.customers_telephone AS phone, cust.customers_mobile AS mobile, c.countries_international_dialing_code AS dialing_code, customers_disable_withdrawal as disable_withdrawal 
										FROM " . TABLE_CUSTOMERS . " AS cust 
										LEFT JOIN " . TABLE_COUNTRIES . " AS c 
											ON (cust.customers_country_dialing_code_id = c.countries_id) 
										WHERE cust.customers_id = '" . tep_db_input($this->user_id) . "'";
		}
		
		$user_info_result_sql = tep_db_query($user_info_select_sql);
		$user_info_row = tep_db_fetch_array($user_info_result_sql);
		
		return $user_info_row;
	}
	
	function list_payment_account_book($filename) {
		$payment_list_content = '';
		
		$payment_list_content = '
								<table id="myTable" border="0" cellspacing="0" cellpadding="0" width="100%">
									<tr class="boxHeader">
										<td style="width:2%;"><div class="boxHeaderLeft" style="width:100%;">&nbsp;</div></td>
										<td style="width:15%;">
											<div class="boxHeaderCenter" style="width:100%;color:#ffffff;">
												<span style="position:absolute;margin-top:5px;text-align:left;">' . TABLE_HEADING_PAYMENT_ACCOUNT . '&nbsp;</span>
											</div>
										</td>
										<td style="width:4%;"><div class="boxHeaderDivider" style="width:100%;">&nbsp;</div></td>
										<td style="width:58%;">
											<div class="boxHeaderCenter" style="width:100%;color:#ffffff;">
												<span style="position:absolute;margin-top:5px;text-align:left;">' . TABLE_HEADING_PAYMENT_INFO . '&nbsp;</span>
											</div>
										</td>
										<td style="width:4%;"><div class="boxHeaderDivider" style="width:100%;">&nbsp;</div></td>
										<td style="width:15%;">
											<div class="boxHeaderCenter" style="width:100%;color:#ffffff;">
												<span style="position:absolute;margin-top:5px;margin-left:-25px;text-align:center;">' . TABLE_HEADING_PAYMENT_ACTION . '&nbsp;</span>
											</div>
										</td>
										<td style="width:2%;"><div class="boxHeaderRight" style="width:100%;">&nbsp;</div></td>
									</tr>
								';
											
		if (!count($this->payment_accounts)) {
			$payment_list_content .= '<tr><td class="inputLabel" colspan="7">'.TEXT_PM_NO_EXISTING_PAYMENT_ACCOUNT.'</td></tr>';
		} 
		else {
			$payment_list_content .= '
									<!--tr>
										<td class="inputLabel" colspan="2">'.$book_info['pm_name'].' ['.$book_info['pm_alias'].']'.'</td>
									</tr>
									<tr>
										<td class="inputLabel">'.TABLE_HEADING_PM_PAYMENT_ACCOUNT.'</td>
										<td class="inputLabel">&nbsp;</td>
									</tr-->';
			
			foreach ($this->payment_accounts as $book_id => $book_info) {
				$pma_details = '';
				
				// listed payment account details
				if (tep_not_null($this->payment_accounts[$book_id]['field'])) {
					foreach ($this->payment_accounts[$book_id]['field'] as $field_num => $pma_fields) {
						if (in_array($pma_fields['payment_methods_fields_type'], $this->user_input_field_type)) {
							$pma_details .= $pma_fields['payment_methods_fields_title'] .': '. $pma_fields['payment_methods_fields_value'] .'<br>';
						}
					}
				}
				if (strstr($book_info['pm_avail_sites'], '0') === false) {
					$pma_details .= '<br><span class="errorText">(This disbursement method no longer supported.)</span>';
				}
				
				$safe_pm_title = htmlspecialchars(addslashes($book_info['pm_alias']), ENT_QUOTES);
				$payment_list_content .= '
										<tr>
											<td style="padding:5px 0;">&nbsp;</td>
											<td style="padding:5px 0;vertical-align:top;" class="inputLabel">'.$book_info['pm_alias'].'</td>
											<td style="padding:5px 0;">&nbsp;</td>
											<td style="padding:5px 0;" class="inputLabel">
												<b>'.$book_info['pm_name'].'</b><br>'. $pma_details .'
											</td>
											<td style="padding:5px 0;">&nbsp;</td>
											<td style="padding:5px 0;vertical-align:top;" class="inputLabel"> 
												<div style="display:block;text-align:center;"> ' .
													((strstr($book_info['pm_avail_sites'], '0') === false) ? BUTTON_EDIT.'&nbsp;&nbsp;|&nbsp;&nbsp;' : '<a href="' . tep_href_link($filename, 'action=edit_pm&book_id='.$book_id) . '">'. BUTTON_EDIT .'</a>&nbsp;&nbsp;|&nbsp;&nbsp;') .
													'<a style="cursor:pointer;cursor:hand;" onclick="confirm_delete(\''.$safe_pm_title.'\', \''.TABLE_HEADING_PM_PAYMENT_ACCOUNT.'\', \''.tep_href_link($filename, 'action=delete_pm&subaction=confirm_delete_pm&book_id='.$book_id).'\')">'. BUTTON_REMOVE . '</a>
												</div> 
											</td>
											<td style="padding:5px 0;">&nbsp;</td>
										</tr>
										<tr><td class="dottedLine" colspan="7" style="line-height:1px;margin:0px;">&nbsp;</td></tr>';
			}
		}
		
		$payment_list_content .= '</table>';
		
		$payment_list_html = '
							<table border="0" width="100%" cellspacing="0" cellpadding="0" class="inputBox" style="padding:5px;margin-bottom:5px;">
								<!--tr class="inputBoxContents">
									<td>
										<table border="0" width="100%" cellspacing="0" cellpadding="2">
											<tr>
												<td class="inputBoxHeading">'.TABLE_HEADING_PM_PAYMENT_ACCOUNT.'</td>
												<td class="requiredInfo" align="right">'.FORM_REQUIRED_INFORMATION.'</td>
											</tr>
										</table>
									</td>
								</tr-->
								<tr>
									<td>
										<table border="0" width="100%" cellspacing="0" cellpadding="0" class="inputNestedBox">
											<tr class="inputNestedBoxContents">
												<td>'.$payment_list_content.'</td>
											</tr>
           								</table>
									</td>
								</tr>';
		
		if ($this->max_entry < 0 || count($this->payment_accounts) < $this->max_entry) {
            $payment_list_html .= '
								<tr>
                  					<td>
	          							<table border="0" width="100%" cellspacing="1" cellpadding="2" class="inputNestedBox">
	                						<tr class="inputNestedBoxContents">
			                                  	<td align="right">'.
				                                  	tep_draw_form('add_pm_form', tep_href_link($filename, tep_get_all_get_params(array('subaction', 'action')) . 'action=add_pm'), 'POST').
														tep_div_button(1, ALT_BUTTON_ADD_PM, 'add_pm_form', ' style="float:right"', 'gray_button').'
				                                  	</form>
			                                  	</td>
	                						</tr>
	          							</table>
	          						</td>
	        					</tr>';
     	}
     	else if (count($this->payment_accounts) == $this->max_entry) {
            $payment_list_html .= '
					<tr>
      					<td style="padding:20px 0 30px 0;">
  							<table border="0" width="100%" cellspacing="1" cellpadding="2" class="inputNestedBox">
        						<tr class="inputNestedBoxContents">
        							<td align="right" width="" style="color:#999999;">'. sprintf(TEXT_PM_REACHED_MAX, $this->max_entry) .'</td>
                                  	<td align="right" width="18%" style="padding-right:5px;">'.
                                  		tep_draw_form('add_pm_form', tep_href_link($filename, tep_get_all_get_params(array('subaction', 'action'))), 'POST').
                                  			tep_div_button(1, ALT_BUTTON_ADD_PM, '', 'id="disable_add_pm" onmouseover="x=this.getElementsByTagName(\'a\'); x[0].style.textDecoration=\'none\';" style="float:right;"', 'lightgray_button', false, 'return false;')
                                  	.'	</form>
                                  	</td>
        						</tr>
  							</table>
  						</td>
					</tr>';
     	}
     	
     	$payment_list_html .= '</table>';
     	
		return $payment_list_html;
	}

	function add_payment_account($filename, $book_id='') {
		global $currencies, $currency;
		$sc_currency = $sc_currency_id = '';
		$this_action = isset($book_id) && tep_not_null($book_id) ? 'edit' : 'add';
		
		$currency_drop_down_array = array(array('id' => '', 'text' => PULL_DOWN_DEFAULT));
		$currency_list = $currencies->get_currency_set();
		for ($i=0, $n=sizeof($currency_list); $i<$n; $i++) {
			$currency_drop_down_array[] = array('id' => $currency_list[$i]['id'], 'text' => $currency_list[$i]['text']);
		}
		
		if ($this_action == 'edit' && $this->payment_accounts[$book_id]['pm_currency'] == 0) {
			// use store credit currency
			$sc_currency_select_sql = "	SELECT sc_currency_id
										FROM " . TABLE_COUPON_GV_CUSTOMER . "
										WHERE customer_id = '" . tep_db_input($this->user_id) . "'";
			$sc_currency_result_sql = tep_db_query($sc_currency_select_sql);
			if ($sc_currency_row = tep_db_fetch_array($sc_currency_result_sql)) {
				$sc_currency = $currencies->get_code_by_id($sc_currency_row['sc_currency_id']);
				$sc_currency_id = $sc_currency_row['sc_currency_id'];
			}
		} else {
			$sc_currency = $currencies->get_code_by_id($this->payment_accounts[$book_id]['pm_currency']);
		}
		
		$payment_list_html = '';
		
		if ($this->max_entry > 0) {
			if ($this_action == 'add' && count($this->payment_accounts) >= $this->max_entry) {
				$payment_list_html = '
								<table border="0" width="100%" cellspacing="1" cellpadding="2" class="inputNestedBox">
									<tr class="inputNestedBoxContents">
										<td>
											'.tep_div_button(2, BUTTON_BACK,tep_href_link($filename, tep_get_all_get_params(array('action', 'subaction'))), ' style="float:right"', 'gray_button').'
										</td>
									</tr>
	           					</table>';
			}
		}
		
		if (!tep_not_null($payment_list_html)) {
			$payment_list_html = '
								<table border="0" width="100%" cellspacing="1" cellpadding="2" class="inputNestedBox">
									<tr class="inputNestedBoxContents">
										<td>'
										. tep_draw_form('add_pm_form', tep_href_link($filename,tep_get_all_get_params(array('subaction', 'action')) . ($this_action == 'edit' ? 'action=edit_pm&subaction=update_pm' : 'action=add_pm&subaction=insert_pm')), 'POST')
										. ($this_action == 'edit' ? tep_draw_hidden_field("book_id", $_REQUEST["book_id"]) : '')
										. '
											<table class="inputBox" border="0" cellpadding="2" cellspacing="1" width="100%">
												<tr class="inputBoxContents">
													<td class="inputLabel">'.ENTRY_FORM_PM_ALIAS.'</td>
													<td class="inputField">'.tep_draw_input_field('pm_alias', $this->payment_accounts[$book_id]['pm_alias'], 'size="30"').'&nbsp;'.TEXT_FIELD_REQUIRED.'</td>
												</tr>
												<tr class="inputBoxContents">
													<td width="35%" class="inputLabel">'.ENTRY_FORM_PM_SELECT_CURRENCY.'</td>
													<td class="inputField">'.tep_draw_pull_down_menu('pm_currency', $currency_drop_down_array, $sc_currency, 'id="pm_currency" onChange="getPMLists(this, \'pm_select_div\', \'pm_field_div\', \''.$this->payment_accounts[$book_id]['pm_id'].'\', \''.$book_id.'\');"').'</td>
												</tr>
												<tr class="inputBoxContents">
													<td width="35%" class="inputLabel">'.ENTRY_FORM_PM_SELECT_PM.'</td>
													<td class="inputField"><div id="pm_select_div">'.tep_draw_pull_down_menu('pm_id', ($book_id ? $this->_get_all_active_send_payment_methods(true, $this->payment_accounts[$book_id]['pm_currency']) : array()), $this->payment_accounts[$book_id]['pm_id'], 'id="pm_id" onChange="getPMOptions(this, \'pm_field_div\', \''.$book_id.'\');"').'</div></td>
												</tr>
												<tr>
													<td class="inputLabel" colspan="2">
														<div id="pm_field_div" style="padding-top:0.2em; display: block;"></div>
													</td>
												</tr>
												<tr>
													<td colspan="2">';
				global $customers_security_obj;		
				$payment_list_html .= $customers_security_obj->show_customer_security_question_html("inputLabelNew","inputBoxContentsNew");
				$payment_list_html .= '
													</td>
												<tr>
												<tr height="20px">
													<td colspan="2">
														<table class="buttonBox2" border="0" cellpadding="2" cellspacing="1" width="100%">
														<tbody>
															<tr class="buttonBoxContents2">
																<td align="left">
																	'.tep_div_button(2, ALT_BUTTON_BACK,tep_href_link($filename, tep_get_all_get_params(array('action', 'subaction'))), '', 'gray_button').'
																</td>
																<td align="right">
																	'.tep_div_button(1, ($this_action == 'add' ? ALT_BUTTON_ADD_PM : ALT_BUTTON_UPDATE),'add_pm_form', 'style="float:right"', 'gray_button').'
																</td>
															</tr>
														</tbody>
														</table>
													</td>
												</tr>
											</table>
										</td>
									</tr>
								</table>
								<script>
									jQuery("#pm_id").ready(function (){';
				if ($book_id) {
					$payment_list_html .= 'getPMOptions(document.getElementById("pm_id"), "pm_field_div", \''.$book_id.'\');';
				} else if ($this_action == 'add') {
					$payment_list_html .= 'getPMLists(document.getElementById(\'pm_currency\'), \'pm_select_div\', \'pm_field_div\', \''.$book_id.'\');';
				}
				$payment_list_html .= '									});
								</script>';
	  	}
	  	
		return $payment_list_html;
	}

	function insert_payment_account($input_array, &$messageStack, $content) {
		global $customers_security_obj, $currencies;
		
		if ($this->max_entry > 0 && count($this->payment_accounts) >= $this->max_entry) {	// Exceed maximum allowed payment account entry
			$messageStack->add_session($content, ERROR_PM_ACCOUNT_BOOK_FULL, 'error');
			return true;
		}

		$error = false;

		$account_book_data_array = array();
		$account_book_details_data_array = array();

		$payment_method_select_sql = "	SELECT payment_methods_id, payment_methods_types_id
										FROM " . TABLE_PAYMENT_METHODS . "
										WHERE payment_methods_id = '".tep_db_input($input_array['pm_id'])."'
											AND payment_methods_send_status = 1 
											AND payment_methods_send_status_mode = 1";
		$payment_method_result_sql = tep_db_query($payment_method_select_sql);

		if ($payment_method_row = tep_db_fetch_array($payment_method_result_sql)) {
			if (!tep_not_null($input_array['pm_alias'])) {
				$error = true;
				$messageStack->add($content, ERROR_EMPTY_PM_ALIAS, 'error');
			}
			
			// check payment method is store credit withdraw method
			$pmt_select_sql = "	SELECT payment_methods_types_system_define
								FROM " . TABLE_PAYMENT_METHODS_TYPES . "
								WHERE payment_methods_types_id = '" . $payment_method_row['payment_methods_types_id'] . "'
								AND payment_methods_types_mode = 'SEND'";
			$pmt_result_sql = tep_db_query($pmt_select_sql);
			if ($pmt_row = tep_db_fetch_array($pmt_result_sql)) {
				if ($pmt_row['payment_methods_types_system_define'] == '1') {
					// check customer existing store credit account
					$sc_currency = '';
					$sc_currency_select_sql = "	SELECT sc_currency_id
												FROM " . TABLE_COUPON_GV_CUSTOMER . "
												WHERE customer_id = '" . tep_db_input($this->user_id) . "'";
					$sc_currency_result_sql = tep_db_query($sc_currency_select_sql);
					if ($sc_currency_row = tep_db_fetch_array($sc_currency_result_sql)) {
						$sc_currency = $currencies->get_code_by_id($sc_currency_row['sc_currency_id']);
					}
					
					// no store credit account exist, create new store credit account for customer
					if ($sc_currency == '' && tep_not_null($input_array['pm_currency'])) {
						$selected_currency_id = array_search($input_array['pm_currency'], $currencies->internal_currencies);
						$sc_account_data_array = array(	'customer_id' => $this->user_id,
														'sc_currency_id' => $selected_currency_id,
														'sc_reversible_amount' => 0,
														'sc_reversible_reserve_amount' => 0,
														'sc_irreversible_amount' => 0,
														'sc_irreversible_reserve_amount' => 0,
														'sc_last_modified' => 'now()'
														);
					}
				}
			}

			$account_book_data_array = array(	'user_id' => $this->user_id,
												'user_role' => $this->user_role,
						                        'payment_methods_id' => $input_array['pm_id'],
						                      	'payment_methods_alias' => tep_db_prepare_input($input_array['pm_alias'])
						                  	);

			$payment_fields_select_sql = "	SELECT payment_methods_fields_id, payment_methods_fields_title, payment_methods_fields_required
											FROM " . TABLE_PAYMENT_METHODS_FIELDS . "
											WHERE payment_methods_id = '" . tep_db_input($input_array['pm_id']) . "'
												AND payment_methods_mode = 'SEND'
												AND payment_methods_fields_status = 1";
			$payment_fields_result_sql = tep_db_query($payment_fields_select_sql);

			while ($payment_fields_row = tep_db_fetch_array($payment_fields_result_sql)) {
				if (!tep_not_null($input_array['pm_field'][$payment_fields_row['payment_methods_fields_id']]) && $payment_fields_row['payment_methods_fields_required'] == '1') {
					$error = true;
					$messageStack->add($content, sprintf(ERROR_PM_FIELD_INFO_REQUIRED, $payment_fields_row['payment_methods_fields_title']), 'error');
				}

				$account_book_details_data_array[] = array(	'payment_methods_fields_id' => $payment_fields_row['payment_methods_fields_id'],
															'payment_methods_fields_value' => tep_db_prepare_input($input_array['pm_field'][$payment_fields_row['payment_methods_fields_id']])
									                  	);
			}
		} else {
			$error = true;
			$messageStack->add($content, ERROR_INVALID_PM_SELECTION, 'error');
		}
		
		// Mantis # 0000024 @ ************ - Customer's Profile Verification (Validate customer's answer)
		if ($customers_security_obj->validate_customer_security_answer($input_array['answer'], $content, ENTRY_MISMATCH_ANSWER_ERROR)) {
			$error = true;
		}
		
		if (!$error) {
			if (count($account_book_data_array)) {
				tep_db_perform(TABLE_STORE_PAYMENT_ACCOUNT_BOOK, $account_book_data_array);
				$payment_account_book_id = tep_db_insert_id();

				for ($acc_book_detail_cnt=0; $acc_book_detail_cnt < count($account_book_details_data_array); $acc_book_detail_cnt++) {
					$account_book_details_data_array[$acc_book_detail_cnt]['store_payment_account_book_id'] = $payment_account_book_id;
					tep_db_perform(TABLE_STORE_PAYMENT_ACCOUNT_BOOK_DETAILS, $account_book_details_data_array[$acc_book_detail_cnt]);
				}
			}
			
			if (isset($sc_account_data_array) && is_array($sc_account_data_array)) {
				tep_db_perform(TABLE_COUPON_GV_CUSTOMER, $sc_account_data_array);
			}
			return true;
		} else {
			return false;
		}
	}
	
	function update_payment_account($input_array, &$messageStack, $content) {
		global $customers_security_obj;
		
		$error = false;
		
		$account_book_data_array = array();
		$account_book_details_data_array = array();
		
		if (isset($input_array['book_id'])) {
			$payment_method_select_sql = "	SELECT payment_methods_id
											FROM " . TABLE_PAYMENT_METHODS . "
											WHERE payment_methods_id = '".tep_db_input($input_array['pm_id'])."'
												AND payment_methods_send_status = 1 
												AND payment_methods_send_status_mode = 1";
			$payment_method_result_sql = tep_db_query($payment_method_select_sql);
			
			if ($payment_method_row = tep_db_fetch_array($payment_method_result_sql)) {
				if (!tep_not_null($input_array['pm_alias'])) {
					$error = true;
					$messageStack->add($content, ERROR_EMPTY_PM_ALIAS, 'error');
				}
				
				$account_book_data_array = array(	'payment_methods_id' => $input_array['pm_id'],
							                      	'payment_methods_alias' => tep_db_prepare_input($input_array['pm_alias'])
							                  	);
				
				$payment_fields_select_sql = "	SELECT payment_methods_fields_id, payment_methods_fields_title, payment_methods_fields_required
												FROM " . TABLE_PAYMENT_METHODS_FIELDS . "
												WHERE payment_methods_id = '" . tep_db_input($payment_method_row['payment_methods_id']) . "'
													AND payment_methods_mode = 'SEND'
													AND payment_methods_fields_status = 1";
				$payment_fields_result_sql = tep_db_query($payment_fields_select_sql);
				
				while ($payment_fields_row = tep_db_fetch_array($payment_fields_result_sql)) {
					if (!tep_not_null($input_array['pm_field'][$payment_fields_row['payment_methods_fields_id']]) && $payment_fields_row['payment_methods_fields_required'] == '1') {
						$error = true;
						$messageStack->add($content, sprintf(ERROR_PM_FIELD_INFO_REQUIRED, $payment_fields_row['payment_methods_fields_title']), 'error');
					}
					
					$account_book_details_data_array[] = array(	'payment_methods_fields_id' => $payment_fields_row['payment_methods_fields_id'],
																'payment_methods_fields_value' => tep_db_prepare_input($input_array['pm_field'][$payment_fields_row['payment_methods_fields_id']])
										                  	);
				}
			} else {
				$error = true;
				$messageStack->add($content, ERROR_INVALID_PM_SELECTION, 'error');
			}
		} else {
			$error = true;
			$messageStack->add($content, ERROR_INVALID_PM_BOOK_SELECTION, 'error');
		}
		
		// Mantis # 0000024 @ ************ - Customer's Profile Verification (Validate customer's answer)
		if ($customers_security_obj->validate_customer_security_answer($input_array['answer'], $content, ENTRY_MISMATCH_ANSWER_ERROR)) {
			$error = true;
		}
		
		if (!$error) {
			if (count($account_book_data_array)) {
				$update_res = tep_db_perform(TABLE_STORE_PAYMENT_ACCOUNT_BOOK, $account_book_data_array, 'update', "store_payment_account_book_id = '" . tep_db_input($input_array['book_id']) . "'");
				
				if ($update_res) {
					$account_book_field_delete_sql = "DELETE FROM " . TABLE_STORE_PAYMENT_ACCOUNT_BOOK_DETAILS . " WHERE store_payment_account_book_id = '" . tep_db_input($input_array['book_id']) . "'";
					tep_db_query($account_book_field_delete_sql);
					
					for ($acc_book_detail_cnt=0; $acc_book_detail_cnt < count($account_book_details_data_array); $acc_book_detail_cnt++) {
						$account_book_details_data_array[$acc_book_detail_cnt]['store_payment_account_book_id'] = $input_array['book_id'];
						tep_db_perform(TABLE_STORE_PAYMENT_ACCOUNT_BOOK_DETAILS, $account_book_details_data_array[$acc_book_detail_cnt]);
					}
				}
			}
			return true;
		} else {
			return false;
		}
	}
	
	function delete_payment_account($book_id, &$messageStack, $content) {
		$payment_account_delete_sql = "	DELETE " . TABLE_STORE_PAYMENT_ACCOUNT_BOOK . ", " . TABLE_STORE_PAYMENT_ACCOUNT_BOOK_DETAILS . "
										FROM " . TABLE_STORE_PAYMENT_ACCOUNT_BOOK . "
										INNER JOIN " . TABLE_STORE_PAYMENT_ACCOUNT_BOOK_DETAILS . "
											ON ( ".TABLE_STORE_PAYMENT_ACCOUNT_BOOK.".store_payment_account_book_id=".TABLE_STORE_PAYMENT_ACCOUNT_BOOK_DETAILS.".store_payment_account_book_id)
										WHERE ".TABLE_STORE_PAYMENT_ACCOUNT_BOOK.".store_payment_account_book_id = '" . tep_db_input($book_id) . "'
											AND ".TABLE_STORE_PAYMENT_ACCOUNT_BOOK.".user_id = '" . tep_db_input($this->user_id) . "'
											AND ".TABLE_STORE_PAYMENT_ACCOUNT_BOOK.".user_role = '" . tep_db_input($this->user_role) . "'";
		tep_db_query($payment_account_delete_sql);

		if (tep_db_affected_rows()) {
			$messageStack->add_session($content, SUCCESS_PM_DELETE_PM_ACCOUNT, 'success');
		}
	}

	function confirm_delete_payment_account($filename, $book_id='') {
		$payment_list_html = '<table width="100%" border="0" align="center" cellpadding="1" cellspacing="0">
           						<tr>
              						<td>'. tep_draw_form('delete_pm_form', tep_href_link($filename, tep_get_all_get_params(array('subaction', 'action', 'book_id')) . 'action=delete_pm&subaction=confirm_delete_pm&book_id='.$book_id), 'POST') .
										'<table border="0" width="100%" cellspacing="0" cellpadding="2">
                  							<tr>
                    							<td align="left" valign="top">
                    								<table class="inputBox" border="0" cellpadding="2" cellspacing="1" width="100%">
                    									<tr class="inputBoxContents">
                    										<td class="inputLabel">'.ENTRY_FORM_PM_ALIAS.'</td>
                    										<td class="inputField"><span class="title-text">'. (isset($this->payment_accounts[$book_id]['pm_alias']) ? $this->payment_accounts[$book_id]['pm_alias'] : '') . '</span></td>
                    									</tr>
                    									<tr class="inputBoxContents">
                    										<td class="inputLabel">'.TEXT_PIN_NUMBER.'</td>
                    										<td class="inputField">'.tep_draw_input_field('pin_number', '', 'size="6"', true) . '&nbsp;' . tep_image(DIR_WS_ICONS . 'help_info.gif', '', '12', '12', 'onMouseover="ddrivetip(\''.TEXT_HELP_PIN_NUMBER.'\', \'\', 200);" onMouseout="hideddrivetip();"') . '&nbsp;&nbsp;' . TEXT_PIN_NUMBER_NOTE.'</td>
                    									</tr>
                    									<tr>
                    										<td class="inputLabel" colspan="2">
                    											<div id="pm_field_div" style="padding-top:0.2em; display: block;"></div>
                    										</td>
                    									</tr>
						            					<tr>
						                  					<td colspan="2">
							          							<table class="buttonBox2" border="0" cellpadding="2" cellspacing="1" width="100%">
							              						<tbody>
							                						<tr class="buttonBoxContents2">
							                							<td align="left">
																			'.tep_div_button(2, ALT_BUTTON_BACK,tep_href_link($filename, tep_get_all_get_params(array('action', 'subaction'))), '', 'gray_button').'
									                                  	</td>
									                                  	<td align="right">
									                                  		'.tep_div_button(1, LINK_PM_DELETE_PM_ACCOUNT,'delete_pm_form', 'style="float:right"', 'gray_button').'
																		  '.tep_draw_hidden_field('pm_id', $this->payment_accounts[$book_id]['pm_id'], 'id="pm_id"') .'
									                                  	</td>
							                						</tr>
							              						</tbody>
							          							</table>
							          						</td>
							        					</tr>
                    								</table>
                    							</td>
                  							</tr>
              							</table>
              							</form>
              						</td>
            					</tr>
            					<script language="javascript">getPMOptions(document.getElementById(\'pm_id\'), \'pm_field_div\', \''.$book_id.'\');</script>
          					</table>';

		return $payment_list_html;

	}

	function show_withdraw_form($filename, $credit_currency) {
		global $currencies;
		
		$user_credit_info_select_sql = "SELECT store_account_balance_amount, store_account_reserve_amount
										FROM " . TABLE_STORE_ACCOUNT_BALANCE . "
										WHERE user_id = '".tep_db_input($this->user_id)."'
											AND user_role = '".tep_db_input($this->user_role)."'
											AND store_account_balance_currency = '".tep_db_input($credit_currency)."'";
		$user_credit_info_result_sql = tep_db_query($user_credit_info_select_sql);

		$user_info_array = $this->_get_user_particulars();
		
		if ($user_info_array['disable_withdrawal'] == '0' && $user_credit_info_row = tep_db_fetch_array($user_credit_info_result_sql)) {
			$general_reserve_amount = $this->_get_general_reserve_amount();
			$converted_general_reserve_amount = $currencies->apply_currency_exchange($general_reserve_amount, $credit_currency, '', 'sell');
			
			$total_reserve_amount = $converted_general_reserve_amount + (double)$user_credit_info_row['store_account_reserve_amount'];
			
			$available_balance = $user_credit_info_row['store_account_balance_amount'] - $total_reserve_amount;
			
			if ($available_balance <  0)	$available_balance = 0;
			
			$withdraw_form_html = '	<table border="0" width="100%" cellspacing="5" cellpadding="0">
	           						<tr>
	              						<td>'.
	              							tep_draw_form('withdraw_form', tep_href_link($filename, tep_get_all_get_params(array('subaction', 'action')) . 'action=confirm_withdraw'), 'POST') . '
	              							<table border="0" width="100%" cellspacing="0" cellpadding="2">
	                  							<tr>
	                    							<td align="left" valign="top">
	                    								<table class="inputBox" border="0" cellpadding="2" cellspacing="1" width="100%">
	                    									<tr class="inputBoxContents">
	                    										<td width="30%" class="inputLabel">'.ENTRY_WITHDRAW_CURRENT_BALANCE.'</td>
	                    										<td class="inputField">'.
	                    											$currencies->format($user_credit_info_row['store_account_balance_amount'], false, $credit_currency).'&nbsp;&nbsp;'.
	                    											sprintf(LINK_WITHDRAW_ACCOUNT_STATEMENT, tep_href_link(FILENAME_MY_PAYMENT_HISTORY, 'action=show_report&pyh_input_start_date='.(date('Y-m-d', mktime(0, 0, 0, date("m")  , date("d")-7, date("Y")))), 'SSL')).'
	                    										</td>
	                    									</tr>
	                    									<tr class="inputBoxContents">
	                    										<td class="inputLabel">'.ENTRY_WITHDRAW_RESERVE_AMOUNT.'</td>
	                    										<td class="inputField">'.$currencies->format($total_reserve_amount, false, $credit_currency).'</td>
	                    									</tr>
	                    									<tr class="inputBoxContents">
	                    										<td class="inputLabel">'.ENTRY_WITHDRAW_AVAILABLE_BALANCE.'</td>
	                    										<td class="inputField">'.$currencies->format_round_down($available_balance, false, $credit_currency).'</td>
	                    									</tr>
	                    									<tr class="inputBoxContents">
	                    										<td class="inputLabel">'.ENTRY_WITHDRAW_AMOUNT.'</td>
	                    										<td class="inputField">'.$currencies->currencies[$credit_currency]['symbol_left'] . tep_draw_input_field('withdraw_amount', '', 'id="withdraw_amount" onkeyup="if (trim_str(this.value) != \'\' && !currencyValidation(trim_str(this.value))) { this.value = \'\'; }" onChange="getPMInfo(document.getElementById(\'pb_id\'), \''.$_REQUEST["cur"].'\', \'withdraw_amount\', \'pm_field_div\');"').$currencies->currencies[$credit_currency]['symbol_right'].'</td>
	                    									</tr>
	                    									<tr class="inputBoxContents">
	                    										<td class="inputLabel">'.ENTRY_WITHDRAW_PAYMENT_ACCOUNT.'</td>
	                    										<td class="inputField">'.
	                    											tep_draw_pull_down_menu('pb_id', $this->_get_existing_payment_account_selection(true), '', 'id="pb_id" onChange="getPMInfo(this, \''.$_REQUEST["cur"].'\', \'withdraw_amount\', \'pm_field_div\');"').'&nbsp;&nbsp;'.
	                    											'<a href="' . tep_href_link(FILENAME_ACCOUNT_PAYMENT_EDIT, '', 'SSL') . '">' . LOGIN_BOX_ACCOUNT_PAYMENT_EDIT . '</a>
	                    										</td>
	                    									</tr>
	                    									<tr>
	                    										<td class="inputLabel">&nbsp;</td>
	                    										<td class="inputLabel">
	                    											<div id="pm_field_div" style="padding-top:0.2em; display: block;"></div>
	                    										</td>
	                    									</tr>
							            					<tr>
							                  					<td colspan="2">
								          							<table class="buttonBox2" border="0" cellpadding="2" cellspacing="1" width="100%">
								              						<tbody>
								                						<tr class="buttonBoxContents2">
								                							<td align="right">
								                								'.tep_div_button(1, ALT_BUTTON_CONTINUE, 'withdraw_form', 'style="float:right"', 'gray_button').'
										                                  	</td>
								                						</tr>
								              						</tbody>
								          							</table>
								          						</td>
								        					</tr>
	                    								</table>
	                    							</td>
	                  							</tr>
	              							</table>
	              							</form>
	              						</td>
	            					</tr>
	            					<script>
                                        $(document).ready(function () {
                                            getPMInfo(document.getElementById(\'pb_id\'), \''.$_REQUEST["cur"].'\', \'withdraw_amount\', \'pm_field_div\');
                                        });
                                    </script>
	          					</table>';
		} else {
			if ($user_info_array['disable_withdrawal'] == '1') {
				$withdraw_form_html = '	<table width="600" border="0" align="center" cellpadding="1" cellspacing="0" class="smallText" valign="middle">
										<tr>
											<td class="messageStackError">'.nl2br(ERROR_WITHDRAW_DISABLE_WITHDRAWAL).'</td>
										</tr>
										</table>';
			} else {
				$withdraw_form_html = '	<table width="600" border="0" align="center" cellpadding="1" cellspacing="0" class="smallText" valign="middle">
										<tr>
											<td class="messageStackError">'.nl2br(ERROR_WITHDRAW_NO_CURRENCY_ACCOUNT_INFO).'</td>
										</tr>
										</table>';
			}
		}

		return $withdraw_form_html;
	}

	function confirm_withdraw_form($filename, $credit_currency, $input_array, $action, &$messageStack) {
		global $currencies;
		
		$withdraw_form_html = $pm_sec_html = '';
		$withdraw_failed = false;
		$display_trans_fee_array = array();
		$total_fees_amount = 0;
		$action_res_array = array('code' => '', 'html' => '');
		
		$user_credit_info_select_sql = "SELECT store_account_balance_amount, store_account_reserve_amount 
										FROM " . TABLE_STORE_ACCOUNT_BALANCE . "
										WHERE user_id = '".tep_db_input($this->user_id)."' 
											AND user_role = '".tep_db_input($this->user_role)."' 
											AND store_account_balance_currency = '".tep_db_input($credit_currency)."'";
		$user_credit_info_result_sql = tep_db_query($user_credit_info_select_sql);
		
		if ($user_credit_info_row = tep_db_fetch_array($user_credit_info_result_sql)) {
			$general_reserve_amount = $this->_get_general_reserve_amount();
			$converted_general_reserve_amount = $currencies->apply_currency_exchange($general_reserve_amount, $credit_currency, '', 'sell');
			
			$total_reserve_amount = $converted_general_reserve_amount + (double)$user_credit_info_row['store_account_reserve_amount'];
			
			$available_balance = $user_credit_info_row['store_account_balance_amount'] - $total_reserve_amount;
			
			if ($available_balance <  0)	$available_balance = 0;
			
			$payment_book_id = isset($input_array['pb_id']) ? trim($input_array['pb_id']) : '';
			$withdraw_amount = isset($input_array['withdraw_amount']) ? (double)$input_array['withdraw_amount'] : 0;
			
			// check if any incomplete mandatory data
			$mandatory_data_completed_flag = true;
			$check_incomplete_field_select_sql = "	SELECT pmf.payment_methods_fields_id, spabd.payment_methods_fields_value 
													FROM " . TABLE_PAYMENT_METHODS_FIELDS . " AS pmf 
													INNER JOIN " . TABLE_STORE_PAYMENT_ACCOUNT_BOOK . " AS spab
														ON pmf.payment_methods_id = spab.payment_methods_id
													LEFT JOIN " . TABLE_STORE_PAYMENT_ACCOUNT_BOOK_DETAILS . " spabd 
														ON spabd.store_payment_account_book_id = spab.store_payment_account_book_id
															AND spabd.payment_methods_fields_id = pmf.payment_methods_fields_id
													WHERE spab.store_payment_account_book_id = '".tep_db_input($payment_book_id)."'
														AND pmf.payment_methods_fields_required = '1'
														AND pmf.payment_methods_fields_status = '1'";
			$check_incomplete_field_result_sql = tep_db_query($check_incomplete_field_select_sql);
			while ($check_incomplete_field_row = tep_db_fetch_array($check_incomplete_field_result_sql)) {
				if (!tep_not_null($check_incomplete_field_row['payment_methods_fields_value'])) {
					$mandatory_data_completed_flag = false;
				}
			}
			
			if (!$mandatory_data_completed_flag) {
				tep_redirect(tep_href_link(FILENAME_ACCOUNT_PAYMENT_EDIT, tep_get_all_get_params(array('action', 'subaction', 'book_id')). 'book_id='.$payment_book_id.'&action=edit_pm', 'SSL'));
			}
			
			if ($available_balance > 0 && $withdraw_amount <= $available_balance) {
				if ($withdraw_amount > 0) {
					$payment_book_select_sql = "	SELECT pm.payment_methods_id, pm.payment_methods_send_mode_name, pm.payment_methods_send_currency, spab.payment_methods_alias, pm.payment_methods_types_id, pm.payment_methods_send_available_sites 
													FROM " . TABLE_STORE_PAYMENT_ACCOUNT_BOOK . " AS spab 
													INNER JOIN " . TABLE_PAYMENT_METHODS . " AS pm 
														ON spab.payment_methods_id=pm.payment_methods_id 
													WHERE spab.store_payment_account_book_id = '".tep_db_input($payment_book_id)."' 
														AND spab.user_id = '".tep_db_input($this->user_id)."' 
														AND spab.user_role = '".tep_db_input($this->user_role)."' 
														AND pm.payment_methods_send_status = 1 
														AND pm.payment_methods_send_status_mode = 1";
					$payment_book_result_sql = tep_db_query($payment_book_select_sql);
					
					if ($payment_book_row = tep_db_fetch_array($payment_book_result_sql)) {
						if (strstr($payment_book_row['payment_methods_send_available_sites'], '0') === false) {
							$withdraw_failed = true;
							$messageStack->add_session(ERROR_INVALID_PM_BOOK_SELECTION, 'error');
						}
						
						if ($payment_book_row['payment_methods_send_currency'] == '0') {
							$pmt_row = tep_db_fetch_array(tep_db_query("SELECT payment_methods_types_system_define FROM " . TABLE_PAYMENT_METHODS_TYPES . " WHERE payment_methods_types_id='".$payment_book_row['payment_methods_types_id']."'"));
							if ($pmt_row['payment_methods_types_system_define'] == '1') {
								$current_store_credit_arr = store_credit::get_current_credits_balance($this->user_id);
								$payment_currency = $currencies->get_code_by_id($current_store_credit_arr['sc_currency_id']);
								$payment_book_row['payment_methods_send_currency'] = $current_store_credit_arr['sc_currency_id'];
							} else {
								$withdraw_failed = true;
								$messageStack->add_session(ERROR_INVALID_PM_SELECTION, 'error');
							}
						} else {
							$payment_currency = $currencies->get_code_by_id($payment_book_row['payment_methods_send_currency']);
						}
						
						$trans_fee_array = $this->_calculate_fees($payment_book_row['payment_methods_id'], $credit_currency, $withdraw_amount);
						
						if ($trans_fee_array['cost'] > 0) {
							$display_trans_fee_array[] = $currencies->format($trans_fee_array['cost'], true, $credit_currency);
							$total_fees_amount += $currencies->apply_currency_exchange($trans_fee_array['cost'], $credit_currency, '', 'sell');
						}
						
						if ($trans_fee_array['percent'] > 0) {
							$display_trans_fee_array[] = $trans_fee_array['percent'] . '%';
							//$total_fees_amount += ($withdraw_amount * $trans_fee_array['percent']) / 100;
							$total_fees_amount += ($withdraw_amount / (1 + ($trans_fee_array['percent'] / 100))) * ($trans_fee_array['percent'] / 100);
						}
						
						$payment_fee_select_sql = "	SELECT * 
													FROM " . TABLE_PAYMENT_FEES . "	
													WHERE payment_methods_id = '".tep_db_input($payment_book_row['payment_methods_id'])."' 
														AND payment_methods_mode = 'SEND'";
						$payment_fee_result_sql = tep_db_query($payment_fee_select_sql);
						$payment_fee_row = tep_db_fetch_array($payment_fee_result_sql);
						
						$converted_min_withdraw  = $currencies->apply_currency_exchange($payment_fee_row['payment_fees_min'], $credit_currency, '', 'sell');
						if ($converted_min_withdraw > 0 && $withdraw_amount < $converted_min_withdraw) {	// Below minimum withdraw amount
							if ($payment_fee_row['payment_fees_below_min'] == 'beneficiary') {
								if (count($display_trans_fee_array)) {
									$withdraw_fees = implode(' + ', $display_trans_fee_array);
								} else {
									$withdraw_fees = TEXT_WITHDRAW_FEES_FREE;
									//$withdraw_fees = 'DO_NOT_SHOW_FEES';
								}
							} else if ($payment_fee_row['payment_fees_below_min'] == 'payer') {
								$withdraw_fees = TEXT_WITHDRAW_FEES_FREE;
								//$withdraw_fees = 'DO_NOT_SHOW_FEES';
							} else {
								$withdraw_failed = true;
								$messageStack->add_session(sprintf(WARNING_WITHDRAW_NOT_ALLOWED_PAYMENT_METHOD, $payment_book_row['payment_methods_alias']), 'error');
							}
						} else {
							if ($payment_fee_row['payment_fees_bear_by'] == 'beneficiary') {
								if (count($display_trans_fee_array)) {
									$withdraw_fees = implode(' + ', $display_trans_fee_array);
								} else {
									$withdraw_fees = TEXT_WITHDRAW_FEES_FREE;
									//$withdraw_fees = 'DO_NOT_SHOW_FEES';
								}
							} else {
								$withdraw_fees = TEXT_WITHDRAW_FEES_FREE;
								//$withdraw_fees = 'DO_NOT_SHOW_FEES';
							}
						}
						
						if ($withdraw_fees == TEXT_WITHDRAW_FEES_FREE) { // Reset total fees to 0 if not applicable
							$total_fees_amount = 0;
						}
						
						$converted_max_withdraw  = $currencies->apply_currency_exchange($payment_fee_row['payment_fees_max'], $credit_currency, '', 'sell');
						
						if ($converted_max_withdraw > 0 && $withdraw_amount > $converted_max_withdraw) {	// Over limit
							$withdraw_failed = true;
							$messageStack->add_session(sprintf(WARNING_WITHDRAW_NOT_ALLOWED_PAYMENT_METHOD, $payment_book_row['payment_methods_alias']), 'error');
						}
						
						// Fees is higher than withdraw amount
						if ($withdraw_amount <= $total_fees_amount) {
							$withdraw_failed = true;
							$messageStack->add_session(WARNING_WITHDRAW_FEES_HIGHER_THAN_WITHDRAW_AMOUNT, 'error');
						}
						
						if ($withdraw_failed) {
							$action_res_array['code'] = '-1';
						} else {
							if ($action == 'submit') {	// Actual insert
								$user_info_array = $this->_get_user_particulars();
								
								$after_fees_amount = $withdraw_amount - $total_fees_amount;
								
								/**********************************************************************
									Capture the withdraw calculation and store it for reference
								**********************************************************************/
								$fees_calculation_text .= 	ENTRY_WITHDRAW_CURRENT_BALANCE . ' ' . $currencies->format($user_credit_info_row['store_account_balance_amount'], false, $credit_currency) . "\n" .
															ENTRY_WITHDRAW_RESERVE_AMOUNT . ' ' . $currencies->format($total_reserve_amount, false, $credit_currency) . "\n" . 
															ENTRY_WITHDRAW_AVAILABLE_BALANCE . ' ' . $currencies->format($available_balance, false, $credit_currency) . "\n" .
						                    				ENTRY_WITHDRAW_AMOUNT . ' ' . $currencies->currencies[$credit_currency]['symbol_left'] . $withdraw_amount .$currencies->currencies[$credit_currency]['symbol_right'] . "\n" .
						                    				ENTRY_WITHDRAW_PAYMENT_ACCOUNT . ' ' . $payment_book_row['payment_methods_alias'] . "\n\n";
								
								$fees_calculation_text .= 	ENTRY_WITHDRAW_MIN_AMT . ' ' . ($payment_fee_row['payment_fees_min'] > 0 ? $currencies->format($payment_fee_row['payment_fees_min'], true, $credit_currency) : TEXT_NO_WITHDRAW_LIMIT) . "\n" .
											    		 	ENTRY_WITHDRAW_MAX_AMT . ' ' . ($payment_fee_row['payment_fees_max'] > 0 ? $currencies->format($payment_fee_row['payment_fees_max'], true, $credit_currency) : TEXT_NO_WITHDRAW_LIMIT) . "\n";
								
								if ($withdraw_fees != 'DO_NOT_SHOW_FEES') {
									$fees_calculation_text .= ENTRY_WITHDRAW_FEES . $withdraw_fees . "\n\n";
								}

								$withdraw_final_amount = $currencies->advance_currency_conversion($after_fees_amount,$credit_currency,$payment_currency,'','sell');
								$fees_calculation_text .= ENTRY_WITHDRAW_FINAL_AMOUNT . $currencies->format_round_down($withdraw_final_amount,false,$payment_currency,'','') . "\n\n";

								//$fees_calculation_text .= ENTRY_WITHDRAW_FINAL_AMOUNT . $currencies->format($after_fees_amount, true, $payment_currency, '', 'buy') . "\n\n"; //$currencies->format($after_fees_amount, false, $credit_currency) . "\n\n";
								/**********************************************************************
									End of capture the withdraw calculation and store it for reference
								**********************************************************************/
								
								
								$payment_fields_select_sql = "	SELECT pmf.payment_methods_id 
																FROM " . TABLE_PAYMENT_METHODS_FIELDS . " AS pmf
																WHERE pmf.payment_methods_id = '" . tep_db_input($payment_book_row['payment_methods_id']) . "'";
								$payment_fields_result_sql = tep_db_query($payment_fields_select_sql);
								if (!tep_db_num_rows($payment_fields_result_sql)) {
									$payment_fields_select_sql = "	SELECT pm.payment_methods_parent_id as payment_methods_id
																	FROM " . TABLE_PAYMENT_METHODS . " AS pm  
																	WHERE pm.payment_methods_id = '" . tep_db_input($payment_book_row['payment_methods_id']) . "'";
									$payment_fields_result_sql = tep_db_query($payment_fields_select_sql);									
								}
								$payment_fields_row = tep_db_fetch_array($payment_fields_result_sql);
								$payment_methods_id = (int)$payment_fields_row['payment_methods_id'];
								
								$payment_fields_select_sql = "	SELECT pmf.payment_methods_fields_id, pmf.payment_methods_fields_title, pmf.payment_methods_fields_sort_order, spabd.payment_methods_fields_value 
																FROM " . TABLE_PAYMENT_METHODS_FIELDS . " AS pmf 
																LEFT JOIN " . TABLE_STORE_PAYMENT_ACCOUNT_BOOK_DETAILS . " spabd 
																	ON (pmf.payment_methods_fields_id=spabd.payment_methods_fields_id AND spabd.store_payment_account_book_id = '".tep_db_input($payment_book_id)."')
																WHERE pmf.payment_methods_id = '" . tep_db_input($payment_methods_id) . "' 
																	AND pmf.payment_methods_mode = 'SEND' 
																	AND pmf.payment_methods_fields_status = 1 
																ORDER BY pmf.payment_methods_fields_sort_order";
								$payment_fields_result_sql = tep_db_query($payment_fields_select_sql);

								$payment_sql_data_array = array(	'user_id' => tep_db_prepare_input($this->user_id),
					    		           							'user_role' => tep_db_prepare_input($this->user_role),
					    	 	          							'user_firstname' => $user_info_array['fname'],
					    	 	          							'user_lastname' => $user_info_array['lname'],
					    	 	          							'user_email_address' => $user_info_array['email'],
					    	 	          							'user_country_international_dialing_code' => $user_info_array['dialing_code'],
					    	 	          							'user_telephone' => $user_info_array['phone'],
					    	 	          							'user_mobile' => $user_info_array['mobile'],
					    	 	          							'store_payments_date' => 'now()',
					    	 	          							'store_payments_status' => '1',
					    	  	         							'store_payments_request_currency' => tep_db_prepare_input($credit_currency),
					    	  	         							'store_payments_request_amount' => (double)$withdraw_amount,
					    	  	         							'store_payments_fees' => tep_db_prepare_input($total_fees_amount),
					    	  	         							'store_payments_after_fees_amount' => $after_fees_amount,
					    	  	         							'store_payments_paid_currency' => tep_db_prepare_input($currencies->get_code_by_id($payment_book_row['payment_methods_send_currency'])),
					    	  	         							'store_payments_methods_id' => $payment_book_row['payment_methods_id'],
					    	  	         							'store_payments_methods_name' => tep_db_prepare_input($payment_book_row['payment_methods_send_mode_name']),
					    	  	         							'store_payment_account_book_id' => $payment_book_id,
					    	  	         							'user_payment_methods_alias' => tep_db_prepare_input($payment_book_row['payment_methods_alias'])
					        	    	       						);
					        	
					        	if ($credit_currency != $currencies->get_code_by_id($payment_book_row['payment_methods_send_currency'])) {
									$payment_sql_data_array['store_payments_paid_currency_value'] = $currencies->advance_currency_conversion_rate($credit_currency, $currencies->get_code_by_id($payment_book_row['payment_methods_send_currency']));
								}
					        	 
					        	tep_db_perform(TABLE_STORE_PAYMENTS, $payment_sql_data_array);
								$insert_payment_id = tep_db_insert_id();
								
								if ($insert_payment_id > 0) {
									// Update live credit balance
									$update_info = array(	array(	'field_name'=> 'store_account_balance_amount',
																	'operator'=> '-', 
																	'value'=> $withdraw_amount)
														);
									
									$new_store_acc_balance = $this->_set_store_acc_balance($credit_currency, $update_info);
									
									// Insert account statement history
									$account_balance_history_data_array = array('user_id' => tep_db_prepare_input($this->user_id),
																				'user_role' => tep_db_prepare_input($this->user_role),
														                        'store_account_history_date' => 'now()',
														                        'store_account_history_currency' => tep_db_prepare_input($credit_currency),
														                        'store_account_history_debit_amount' => (double)$withdraw_amount,
														                        'store_account_history_credit_amount' => 'NULL',
														                        'store_account_history_after_balance' => (double)$new_store_acc_balance,
														                        'store_account_history_trans_type' => 'P',
														                        'store_account_history_trans_id' => $insert_payment_id,
														                        'store_account_history_added_by' => $user_info_array['email'],
														                        'store_account_history_added_by_role' => tep_db_prepare_input($this->user_role)
														                       );
									tep_db_perform(TABLE_STORE_ACCOUNT_HISTORY, $account_balance_history_data_array);
									
									// Insert payment details
									while ($payment_fields_row = tep_db_fetch_array($payment_fields_result_sql)) {
										$payment_details_sql_data_array = array('store_payments_id' => $insert_payment_id,
							    		           								'payment_methods_fields_id' => $payment_fields_row['payment_methods_fields_id'],
								    	 	          							'payment_methods_fields_title' => $payment_fields_row['payment_methods_fields_title'],
								    	 	          							'payment_methods_fields_value' => $payment_fields_row['payment_methods_fields_value'],
								    	 	          							'payment_methods_fields_sort_order' => $payment_fields_row['payment_methods_fields_sort_order']
							        	    	       							);
										tep_db_perform(TABLE_STORE_PAYMENTS_DETAILS, $payment_details_sql_data_array);
										
										$fees_calculation_text .= $payment_fields_row['payment_methods_fields_title'] . ' ' . $payment_fields_row['payment_methods_fields_value'] . "\n";
									}
									
									// Insert payment history
									$comments_contents_str = tep_db_prepare_input(sprintf(COMMENT_WITHDRAW_FUNDS_REQUESTED, $payment_book_row['payment_methods_alias'], $payment_book_row['payment_methods_send_mode_name'])) . "\n\n" . tep_db_prepare_input($fees_calculation_text);
									$payment_history_sql_data_array = array('store_payments_id' => $insert_payment_id,
						    		           								'store_payments_status' => '1',
							    	 	          							'date_added' => 'now()',
							    	 	          							'payee_notified' => '1',
							    	 	          							'comments' => $comments_contents_str,
							    	 	          							'changed_by' => $user_info_array['email'],
							    	 	          							'changed_by_role' => tep_db_prepare_input($this->user_role)
						        	    	       							);
									tep_db_perform(TABLE_STORE_PAYMENTS_HISTORY, $payment_history_sql_data_array);
									 
									// Update withdraw fees calculation history
									$payment_update_array = array('store_payments_fees_calculation' => tep_db_prepare_input($fees_calculation_text));
									tep_db_perform(TABLE_STORE_PAYMENTS, $payment_update_array, 'update', 'store_payments_id="'.tep_db_input($insert_payment_id).'"');
									
									// E-mail the beneficiary
									@tep_mail($user_info_array['fname'].' '.$user_info_array['lname'], $user_info_array['email'], sprintf(EMAIL_PAYMENT_PAYMENT_UPDATE_SUBJECT, $insert_payment_id), $comments_contents_str . "\n\n" . EMAIL_PAYMENT_FOOTER, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
									
									$messageStack->add_session(SUCCESS_WITHDRAW_SUCCESS, 'success');
									$action_res_array['code'] = '1';
								} else {
									// Withdraw failed
									$messageStack->add_session(ERROR_WITHDRAW_NOT_SUCCESS, 'error');
									$action_res_array['code'] = '-1';
								}
							} else {
								$pm_sec_html .= '<tr class="inputBoxContents">
													<td width="35%" class="inputLabel" valign="top">'.ENTRY_WITHDRAW_MIN_AMT.'</td>
													<td class="inputField" valign="top">'.($payment_fee_row['payment_fees_min'] > 0 ? $currencies->format($payment_fee_row['payment_fees_min'], true, $credit_currency) : TEXT_NO_WITHDRAW_LIMIT).'</td>
												 </tr>
												 <tr class="inputBoxContents">
													<td class="inputLabel" valign="top">'.ENTRY_WITHDRAW_MAX_AMT.'</td>
													<td class="inputField" valign="top">'.($payment_fee_row['payment_fees_max'] > 0 ? $currencies->format($payment_fee_row['payment_fees_max'], true, $credit_currency) : TEXT_NO_WITHDRAW_LIMIT).'</td>
												 </tr>';
								
								if ($withdraw_fees != 'DO_NOT_SHOW_FEES') {
									$pm_sec_html .= '<tr class="inputBoxContents">
														<td class="inputLabel" valign="top">'.ENTRY_WITHDRAW_FEES.'</td>
														<td class="inputField" valign="top"><b>'.$withdraw_fees.'</b></td>
													 </tr>';
								}
								$withdraw_final_amount = $currencies->advance_currency_conversion($withdraw_amount-$total_fees_amount,$credit_currency,$payment_currency,'','sell');

								$pm_sec_html .= '<tr class="inputBoxContents">
													<td class="inputLabel" valign="top"><b>'.ENTRY_WITHDRAW_FINAL_AMOUNT.'</b></td>
													<td class="inputField" valign="top"><b>'.$currencies->format_round_down($withdraw_final_amount,false,$payment_currency,'','').'</b></td>
												 </tr>
												 <tr><td colspan="2" height="10px"></td></tr>';
								
								$payment_fields_select_sql = "	SELECT pmf.*, spabd.payment_methods_fields_value 
																FROM " . TABLE_PAYMENT_METHODS_FIELDS . " AS pmf 
																LEFT JOIN " . TABLE_STORE_PAYMENT_ACCOUNT_BOOK_DETAILS . " spabd 
																	ON (pmf.payment_methods_fields_id=spabd.payment_methods_fields_id AND spabd.store_payment_account_book_id = '".tep_db_input($payment_book_id)."')
																WHERE pmf.payment_methods_id = '" . tep_db_input($payment_book_row['payment_methods_id']) . "' 
																	AND pmf.payment_methods_mode = 'SEND' 
																	AND pmf.payment_methods_fields_status = 1 
																ORDER BY pmf.payment_methods_fields_sort_order";
								$payment_fields_result_sql = tep_db_query($payment_fields_select_sql);
								
								while ($payment_fields_row = tep_db_fetch_array($payment_fields_result_sql)) {
									$pm_sec_html .= '<tr class="inputBoxContents">
														<td class="inputLabel" valign="top" nowrap><i>'.$payment_fields_row['payment_methods_fields_title'].'</i></td>
														<td class="inputField" valign="top">'.nl2br($payment_fields_row['payment_methods_fields_value']).'</td>
												 	 </tr>';
								}
								
								$withdraw_form_html = '<table width="100%" border="0" align="center" cellpadding="1" cellspacing="0" class="smallText" valign="middle">
						           						<tr>
						              						<td>'.
						              							tep_draw_form('withdraw_form', tep_href_link($filename, tep_get_all_get_params(array('subaction', 'action')) . 'action=submit_withdraw'), 'POST') . 
						              							tep_draw_hidden_field("pb_id", $payment_book_id) . 
						              							tep_draw_hidden_field("withdraw_amount", $withdraw_amount) . '
						              							<table border="0" width="100%" cellspacing="0" cellpadding="2">
						                  							<tr bgcolor="#ffffff">
						                    							<td align="left" valign="top">
						                    								<table class="inputBox" border="0" cellpadding="2" cellspacing="1" width="100%">
						                    									<tr class="inputBoxContents">
						                    										<td width="30%" class="inputLabel">'.ENTRY_WITHDRAW_CURRENT_BALANCE.'</td>
						                    										<td class="inputField">'.
						                    											$currencies->format($user_credit_info_row['store_account_balance_amount'], false, $credit_currency).'&nbsp;&nbsp;' . '
						                    										</td>
						                    									</tr>
						                    									<tr class="inputBoxContents">
						                    										<td class="inputLabel">'.ENTRY_WITHDRAW_RESERVE_AMOUNT.'</td>
						                    										<td class="inputField">'.$currencies->format($total_reserve_amount, false, $credit_currency).'</td>
						                    									</tr>
						                    									<tr class="inputBoxContents">
						                    										<td class="inputLabel">'.ENTRY_WITHDRAW_AVAILABLE_BALANCE.'</td>
						                    										<td class="inputField">'.$currencies->format($available_balance, false, $credit_currency).'</td>
						                    									</tr>
						                    									<tr class="inputBoxContents">
						                    										<td class="inputLabel">'.ENTRY_WITHDRAW_AMOUNT.'</td>
						                    										<td class="inputField"><b>'.$currencies->currencies[$credit_currency]['symbol_left'] . $withdraw_amount .$currencies->currencies[$credit_currency]['symbol_right'].'</b></td>
						                    									</tr>
						                    									<tr class="inputBoxContents">
						                    										<td class="inputLabel">'.ENTRY_WITHDRAW_PAYMENT_ACCOUNT.'</td>
						                    										<td class="inputField">'.$payment_book_row['payment_methods_alias'].'</td>
						                    									</tr>
						                    									<tr>
						                    										<td class="inputLabel">&nbsp;</td>
						                    										<td class="inputLabel">
						                    											<table border="0" width="100%" cellspacing="0" cellpadding="2">'.$pm_sec_html.'</table>
						                    										</td>
						                    									</tr>
												            					<tr>
												                  					<td colspan="2">
													          							<table class="buttonBox2" border="0" cellpadding="2" cellspacing="1" width="100%">
													              						<tbody>
													                						<tr class="buttonBoxContents2">
													                							<td align="left">
													                								'.tep_div_button(2, ALT_BUTTON_BACK,tep_href_link($filename, tep_get_all_get_params(array('subaction'))), '', 'gray_button').'
															                                  	</td>
															                                  	<td align="right">
															                                  		'.tep_div_button(1, ALT_BUTTON_CONFIRM, 'withdraw_form', 'style="float:right"', 'gray_button', true).'
															                                  	</td>
													                						</tr>
													              						</tbody>
													          							</table>
													          						</td>
													        					</tr>
						                    								</table>
						                    							</td>
						                  							</tr>
						              							</table>
						              							</form>
						              						</td>
						            					</tr>
						          					</table>';
						      	$action_res_array['html'] = $withdraw_form_html;
							}
						}
					} else {
						$messageStack->add_session(ERROR_WITHDRAW_NO_PAYMENT_ACCOUNT_BOOK, 'error');
						$action_res_array['code'] = '-1';
					}
				} else {
					$messageStack->add_session(ERROR_WITHDRAW_NO_WITHDRAW_AMOUNT, 'error');
					$action_res_array['code'] = '-1';
				}
			} else {
				$messageStack->add_session(ERROR_WITHDRAW_NO_AVAILABLE_FUNDS, 'error');
				$action_res_array['code'] = '-1';
			}
		} else {
			$messageStack->add_session(ERROR_WITHDRAW_NO_CURRENCY_ACCOUNT_INFO, 'error');
			$action_res_array['code'] = '-1';
		}
		
		return $action_res_array;
	}
	
	function send_payment_status_email($payment_id, $comment='') {
		global $currencies, $PAYMENT_STATUS_ARRAY;
		
		$this->_get_payment_info($payment_id);
		
		$user_particulars_array = $this->_get_user_particulars();
		
		// Send email to payee
		/*
		$email = 	sprintf(EMAIL_PAYMENT_TEXT_TITLE, $currencies->format($this->payment_info["request_amount"], false, $this->payment_info["request_currency"]), $this->payment_info['payment_methods_name']) . "\n\n" .
					EMAIL_PAYMENT_TEXT_SUMMARY_TITLE . "\n" . EMAIL_SEPARATOR . "\n" .
				 	sprintf(EMAIL_PAYMENT_TEXT_PAYMENT_NUMBER, $payment_id) . "\n" .
				 	sprintf(EMAIL_PAYMENT_TEXT_PAYMENT_DATE, tep_date_long($this->info["payments_date"])) . "\n\n" .
				 	sprintf(EMAIL_PAYMENT_TEXT_STATUS_UPDATE, $this->info['status_name']) . "\n\n" . 
				 	//sprintf(EMAIL_PAYMENT_TEXT_COMMENTS, tep_db_prepare_input($comment)) . "\n\n" .
				 	EMAIL_PAYMENT_WITHDRAW_CLOSING . "\n\n" .
				 	EMAIL_PAYMENT_FOOTER;
		*/
		$email_payment_status = array_search($this->info['status'], $PAYMENT_STATUS_ARRAY);
		
		$email = 	sprintf(EMAIL_PAYMENT_TEXT_TITLE) . "\n\n" .
					EMAIL_PAYMENT_TEXT_SUMMARY_TITLE . "\n" . EMAIL_SEPARATOR . "\n" .
				 	sprintf(EMAIL_PAYMENT_TEXT_PAYMENT_NUMBER, $payment_id) . "\n" .
				 	sprintf(EMAIL_PAYMENT_TEXT_PAID_AMOUNT, $currencies->format($this->payment_info["paid_amount"], false, $this->payment_info["paid_currency"])) . "\n" .
				 	sprintf(EMAIL_PAYMENT_TEXT_PAYMENT_DATE, tep_date_long($this->info["payments_date"])) . "\n\n" .
				 	sprintf(EMAIL_PAYMENT_TEXT_STATUS_UPDATE, $email_payment_status) . "\n" . 
				 	//sprintf(EMAIL_PAYMENT_TEXT_COMMENTS, tep_db_prepare_input($comment)) . "\n\n" .
				 	EMAIL_PAYMENT_WITHDRAW_PROCESS_GUIDE . "\n\n" .
				 	EMAIL_PAYMENT_FOOTER;
		$email_greeting = tep_get_email_greeting($user_particulars_array['fname'], $user_particulars_array['lname'], $user_particulars_array['gender']);
		
		$email = $email_greeting . $email;
		@tep_mail($user_particulars_array['fname'].' '.$user_particulars_array['lname'], $this->beneficiary['email_address'], sprintf(EMAIL_PAYMENT_PAYMENT_UPDATE_SUBJECT, $payment_id), $email, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
	}
	
	function _get_general_reserve_amount() {
		switch($this->user_role) {
			case "supplier":
				$reserve_amt_select_sql = "	SELECT supplier_reserve_amount
											FROM " . TABLE_SUPPLIER . "
											WHERE supplier_id = '".tep_db_input($this->user_id)."' ";
				$reserve_amt_result_sql = tep_db_query($reserve_amt_select_sql);
				$reserve_amt_row = tep_db_fetch_array($reserve_amt_result_sql);

				return $reserve_amt_row['supplier_reserve_amount'];

				break;
			case "customers":
				$reserve_amt_select_sql = "	SELECT customers_reserve_amount 
											FROM " . TABLE_CUSTOMERS . " 
											WHERE customers_id = '".tep_db_input($this->user_id)."' ";
				$reserve_amt_result_sql = tep_db_query($reserve_amt_select_sql);
				$reserve_amt_row = tep_db_fetch_array($reserve_amt_result_sql);
				
				return $reserve_amt_row['customers_reserve_amount'];
				
				break;
			default:
				return 0;

				break;
		}
	}

	function _calculate_fees($payment_method_id, $withdraw_currency, $withdraw_amount) {
		global $currencies;

		$trans_fee_array = array('cost' => 0, 'percent' => 0);

		$payment_fee_select_sql = "	SELECT *
									FROM " . TABLE_PAYMENT_FEES . "
									WHERE payment_methods_id = '".tep_db_input($payment_method_id)."'
										AND payment_methods_mode = 'SEND'";
		$payment_fee_result_sql = tep_db_query($payment_fee_select_sql);
		$payment_fee_row = tep_db_fetch_array($payment_fee_result_sql);

		if ($payment_fee_row['payment_fees_cost_value'] > 0) 	$trans_fee_array['cost'] = $payment_fee_row['payment_fees_cost_value'];

		if ($payment_fee_row['payment_fees_cost_percent'] > 0) {
			$percent_fees_not_in_range = false;

			$percent_fees = ($withdraw_amount * $payment_fee_row['payment_fees_cost_percent']) / 100;

			if ($payment_fee_row['payment_fees_cost_percent_min'] > 0) {
				$w_currency_min_fee = $currencies->apply_currency_exchange($payment_fee_row['payment_fees_cost_percent_min'], $withdraw_currency, '', 'sell');
				if ($percent_fees < $w_currency_min_fee) {
					$percent_fees = $payment_fee_row['payment_fees_cost_percent_min'];
					$percent_fees_not_in_range = true;
				}
			}

			if ($payment_fee_row['payment_fees_cost_percent_max'] > 0)	{
				$w_currency_max_fee = $currencies->apply_currency_exchange($payment_fee_row['payment_fees_cost_percent_max'], $withdraw_currency, '', 'sell');

				if ($percent_fees > $w_currency_max_fee) {
					$percent_fees = $payment_fee_row['payment_fees_cost_percent_max'];
					$percent_fees_not_in_range = true;
				}
			}

			if ($percent_fees_not_in_range) {
				$trans_fee_array['cost'] += $percent_fees;
			} else {
				$trans_fee_array['percent'] += $payment_fee_row['payment_fees_cost_percent'];
			}
		}

		return $trans_fee_array;
	}

	function _set_store_acc_balance($credit_currency, $update_info) {
		/*******************************************************************
			operator = +, -, and =
		*******************************************************************/
		$sql_update_array = array();

		// Generate the update sql
		for ($update_cnt=0; $update_cnt < count($update_info); $update_cnt++) {
			if ( ($update_info[$update_cnt]['field_name'] == 'store_account_balance_amount' || $update_info[$update_cnt]['field_name'] == 'store_account_reserve_amount') ) {
				$update_info[$update_cnt]['operator'] = trim($update_info[$update_cnt]['operator']);
				switch($update_info[$update_cnt]['operator']) {
					case '+':
						$sql_update_array[] = $update_info[$update_cnt]['field_name'] . ' = ' . $update_info[$update_cnt]['field_name'] . ' + ' . tep_db_input($update_info[$update_cnt]['value']);
						break;
					case '-':
						$sql_update_array[] = $update_info[$update_cnt]['field_name'] . ' = ' . $update_info[$update_cnt]['field_name'] . ' - ' . tep_db_input($update_info[$update_cnt]['value']);
						break;
					case '=':
						$sql_update_array[] = $update_info[$update_cnt]['field_name'] . ' = ' . tep_db_input($update_info[$update_cnt]['value']);
						break;
					default:
						break;
				}
			}
		}

		if (count($sql_update_array)) {
			$sql_update_array[] = ' store_account_last_modified = now() ';

			$update_sql_str = " SET " . implode(', ', $sql_update_array);

	    	/*************************************************************************
			 	Lock the TABLE_STORE_ACCOUNT_BALANCE
			 	REMEMBER: Need to lock all the tables involved in this block.
			*************************************************************************/
	    	tep_db_query("LOCK TABLES " . TABLE_STORE_ACCOUNT_BALANCE . " WRITE;");

	    	$store_acc_balance_update_sql = "	UPDATE " . TABLE_STORE_ACCOUNT_BALANCE .
	    										$update_sql_str . "
												WHERE user_id = '" . tep_db_input($this->user_id) . "'
													AND user_role = '" . tep_db_input($this->user_role) . "'
													AND store_account_balance_currency = '" . tep_db_input($credit_currency) . "'";
			tep_db_query($store_acc_balance_update_sql);

			$new_balance_select_sql = "	SELECT store_account_balance_amount
										FROM " . TABLE_STORE_ACCOUNT_BALANCE . "
										WHERE user_id = '" . tep_db_input($this->user_id) . "'
											AND user_role = '" . tep_db_input($this->user_role) . "'
											AND store_account_balance_currency = '" . tep_db_input($credit_currency) . "'";
			$new_balance_result_sql = tep_db_query($new_balance_select_sql);
			$new_balance_row = tep_db_fetch_array($new_balance_result_sql);

			tep_db_query("UNLOCK TABLES;");
			/********************************************************************
			 	End of locking the TABLE_STORE_ACCOUNT_BALANCE table.
			********************************************************************/

			return $new_balance_row['store_account_balance_amount'];
		}

		return false;
	}

	function search_acc_statement($filename, $session_name) {
		global $languages_id;

		$acc_statement_html = '';

	  	$show_options = array 	(	array ('id' => '10', "text" => "10"),
									array ('id' => '20', "text" => "20"),
									array ('id' => '50', "text" => "50"),
									array ('id' => 'ALL', "text" => TEXT_ALL_PAGES)
								);
		//payment status dropdown
		$payment_status_arr = array(array("id" => "", "text" => PULL_DOWN_DEFAULT));
		$result = tep_db_query("SELECT store_payments_status_id, store_payments_status_name
								FROM ".TABLE_STORE_PAYMENTS_STATUS." where language_id='$languages_id'
								ORDER BY store_payments_status_sort_order");
		while ($row=tep_db_fetch_array($result)) {
			$payment_status_arr[] = array('id' => $row['store_payments_status_id'], 'text' => $row['store_payments_status_name']);
		}

		ob_start();
?>
	  	<table width="100%" border="0" align="center" cellpadding="1" cellspacing="0" class="smallText" valign="middle">
			<tr>
				<td>
					<table border="0" width="100%" cellspacing="0" cellpadding="0">
						<tr>
							<td width="20%">&nbsp;</td>
        					<td>
        						<?=tep_draw_form('payments_criteria', tep_href_link($filename, tep_get_all_get_params(array('action')) . 'action=show_report'), 'POST')?>
	        						<table border="0" cellspacing="2" cellpadding="0">
										<tr>
											<td class="main" ><?=ENTRY_ORDER_START_DATE?></td>
											<td class="main" valign="top" nowrap>
	    										<script language="javascript"><!--
	  												var date_start_date = new ctlSpiffyCalendarBox("date_start_date", "payments_criteria", "start_date", "btnDate_start_date", "", scBTNMODE_CUSTOMBLUE);
	  												date_start_date.writeControl(); date_start_date.dateFormat="yyyy-MM-dd"; document.getElementById('start_date').value='<?=$_SESSION[$session_name]["start_date"]?>';
												//--></script>
	    									</td>
	    									<td class="main" width="10%">&nbsp;</td>
	    									<td class="main" valign="top" nowrap><?=ENTRY_ORDER_END_DATE?></td>
	    									<td class="main" >&nbsp;</td>
	    									<td class="main"  valign="top" nowrap>
				    							<script language="javascript"><!--
				  									var date_end_date = new ctlSpiffyCalendarBox("date_end_date", "payments_criteria", "end_date", "btnDate_end_date", "", scBTNMODE_CUSTOMBLUE);
				  									date_end_date.writeControl(); date_end_date.dateFormat="yyyy-MM-dd"; document.getElementById('end_date').value='<?=$_SESSION[$session_name]["end_date"]?>';
												//--></script>
				    						</td>
										</tr>
										<tr>
		            						<td colspan="6"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
		          						</tr>
		          						<tr>
		          							<td class="main"><?=ENTRY_PAYMENT_ID?></td>
		          							<td class="main" ><?=tep_draw_input_field('payment_id', (isset($_SESSION[$session_name]["payment_id"]) ? $_SESSION[$session_name]["payment_id"] : ''), ' id="payment_id" size="15" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) ) { this.value = \'\'; } else { resetControls(this); }" onBlur="if (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value)) ) { this.value = \'\'; } else { resetControls(this); }" onKeyPress="return noEnterKey(event)"')?></td>
				    						<td class="main" >&nbsp;</td>
				    						<td class="main"><?=ENTRY_ORDER_ID?></td>
				    						<td class="main" >&nbsp;</td>
				    						<td class="main" ><?=tep_draw_input_field('order_id', (isset($_SESSION[$session_name]["order_id"]) ? $_SESSION[$session_name]["order_id"] : ''), ' id="order_id" size="15" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) ) { this.value = \'\'; } else { resetControls(this); }" onBlur="if (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value)) ) { this.value = \'\'; } else { resetControls(this); }" onKeyPress="return noEnterKey(event)"')?></td>
										</tr>
										<tr>
		            						<td colspan="6"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
		          						</tr>
	
		          						<tr>
				    						<td class="main"><?=ENTRY_PAYMENT_STATUS?></td>
				    						<td class="main" ><?=tep_draw_pull_down_menu('payment_status_id', $payment_status_arr, (isset($_SESSION[$session_name]['payment_status_id']) ? $_SESSION[$session_name]['payment_status_id'] : '0'), 'id="payment_status_id"')?></td>
				    						<td class="main" >&nbsp;</td>
											<td class="main"><?=ENTRY_RECORDS_PER_PAGE?></td>
				    						<td class="main" >&nbsp;</td>
							    			<td class="main"><?=tep_draw_pull_down_menu("show_records", $show_options, (isset($_SESSION[$session_name]["show_records"]) ? $_SESSION[$session_name]["show_records"] : ''), '') . TEXT_LANG_RECORDS_PER_PAGE?></td>
										</tr>
										<tr>
		            						<td colspan="6"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
		          						</tr>
	
		          						<tr>
											<td class="main">&nbsp;</td>
							    			<td class="main">&nbsp;</td>
							    			<td class="main" >&nbsp;</td>
				    						<td colspan="3">
				    							<?=tep_div_button(2, ALT_BUTTON_RESET,tep_href_link($filename, 'action=reset_session'), 'style="float:right;"', 'gray_button') ?>
				    							<?=tep_div_button(2, IMAGE_BUTTON_SEARCH,'javascript:form_checking(\'do_search\');', 'style="float:right;"', 'gray_button') ?>
			  								</td>
										</tr>
		        					</table>
	        					</form>
	        				</td>
	        			</tr>
	        		</table>
	        	</td>
	        </tr>
	    </table>
			<script type="text/javascript" language="javascript"><!--
				function form_checking(action) {
				    document.payments_criteria.submit();
	    		}

				function resetControls(controlObj) {
					if (trim_str(controlObj.value) != '') {
						if (controlObj.id == 'payment_id') {
							document.payments_criteria.order_id.value = '';
						} else {
							document.payments_criteria.payment_id.value = '';
						}
						document.payments_criteria.start_date.value = '';
						document.payments_criteria.end_date.value = '';
						document.payments_criteria.show_records.selectedIndex = 0;
		    		} else {
		    			controlObj.value = '';
		    		}
				}
	    	//-->
			</script>
<?
		$acc_statement_html = ob_get_contents();
		ob_end_clean() ;

		return $acc_statement_html;
	}

	function show_acc_statement($filename, $session_name, $input_array, &$messageStack) {
		global $currencies, $languages_id, $HTTP_GET_VARS, $languages_id;

		if (!isset($_REQUEST['cont']) || !$_REQUEST['cont']) {
			$_SESSION[$session_name]["start_date"] = $input_array["start_date"];
			$_SESSION[$session_name]["end_date"] = $input_array["end_date"];
			$_SESSION[$session_name]["payment_id"] = $input_array["payment_id"];
			$_SESSION[$session_name]["order_id"] = $input_array["order_id"];
			$_SESSION[$session_name]["payment_status_id"] = $input_array["payment_status_id"];
			$_SESSION[$session_name]["show_records"] = $input_array["show_records"];
	  	}

	  	if (tep_not_null($_SESSION[$session_name]["start_date"])) {
			if (strpos($_SESSION[$session_name]["start_date"], ':') !== false) {
				$startDateObj = explode(' ', trim($_SESSION[$session_name]["start_date"]));
				list($yr, $mth, $day) = explode('-', $startDateObj[0]);
				list($hr, $min) = explode(':', $startDateObj[1]);
				$start_date_str = " ( sah.store_account_history_date >= '".date("Y-m-d H:i:s", mktime((int)$hr, (int)$min, 0, $mth, $day, $yr))."' )";
			} else {
				list($yr, $mth, $day) = explode('-', trim($_SESSION[$session_name]["start_date"]));
				$start_date_str = " ( sah.store_account_history_date >= '".date("Y-m-d H:i:s", mktime(0,0,0,$mth,$day,$yr))."')";
			}
		} else {
			$start_date_str = " 1 ";
		}

		if (tep_not_null($_SESSION[$session_name]["end_date"])) {
			if (strpos($_SESSION[$session_name]["end_date"], ':') !== false) {
				$endDateObj = explode(' ', trim($_SESSION[$session_name]["end_date"]));
				list($yr, $mth, $day) = explode('-', $endDateObj[0]);
				list($hr, $min) = explode(':', $endDateObj[1]);
				$end_date_str = " ( sah.store_account_history_date <= '".date("Y-m-d H:i:s", mktime((int)$hr, (int)$min, 0, $mth, $day, $yr))."' )";
			} else {
				list($yr, $mth, $day) = explode('-', trim($_SESSION[$session_name]["end_date"]));
				$end_date_str = " ( sah.store_account_history_date <= '".date("Y-m-d", mktime(23,59,59,$mth,$day,$yr))."')";
			}
		} else {
			$end_date_str = " 1 ";
		}

		if (tep_not_null($_SESSION[$session_name]["payment_status_id"]) && (int)$_SESSION[$session_name]["payment_status_id"]) {
			//user selected payment status. so for now, only search trans that is type = payment.
			$payment_status_str = "sp.store_payments_status = '".tep_db_prepare_input((int)$_SESSION[$session_name]["payment_status_id"])."'
								    AND sah.store_account_history_trans_type = 'P'";
			$payment_status_join_str = "LEFT JOIN ".TABLE_STORE_PAYMENTS." AS sp ON (sp.store_payments_id = sah.store_account_history_trans_id)";

		} else {
			$payment_status_str = '1';
			$payment_status_join_str = '';
		}

	  	$payment_id_str = (isset($_SESSION[$session_name]["payment_id"]) && tep_not_null($_SESSION[$session_name]["payment_id"])) ? " sah.store_account_history_trans_id='" . $_SESSION[$session_name]["payment_id"] . "' AND sah.store_account_history_trans_type='P'" : "1";
		$order_id_str = (isset($_SESSION[$session_name]["order_id"]) && tep_not_null($_SESSION[$session_name]["order_id"])) ? " sah.store_account_history_trans_id = '".$_SESSION[$session_name]["order_id"]."' AND sah.store_account_history_trans_type<>'P'" : "1";

	  	$statement_user_str = " sah.user_id='" . tep_db_input($this->user_id) . "' and sah.user_role='".tep_db_input($this->user_role)."' ";

	  	$result_display_text = TEXT_DISPLAY_NUMBER_OF_RECORDS;

	  	// Need store_account_history_id in ORDER BY to maintain the ordering for those records on the same date and time
	  	$acc_statement_select_sql = "	SELECT sah.store_account_history_date AS activity_date, sah.store_account_history_trans_type AS trans_type, sah.store_account_history_trans_id AS trans_id, sah.store_account_history_id, 
			  								sah.store_account_history_activity_title AS activity_title, sah.store_account_history_activity_desc AS activity_desc, sah.store_account_transaction_reserved AS trans_reserved, sah.store_account_history_activity_desc_show, 
			  								sah.store_account_history_currency AS currency, sah.store_account_history_debit_amount AS debit_amount, sah.store_account_history_credit_amount AS credit_amount, sah.store_account_history_after_balance AS after_balance
										FROM " . TABLE_STORE_ACCOUNT_HISTORY . " AS sah
										".$payment_status_join_str."
										WHERE " . $order_id_str . "
											AND " . $payment_id_str . "
											AND " . $start_date_str . "
											AND " . $end_date_str . "
											AND " . $statement_user_str . "
											AND " . $payment_status_str . "
										ORDER BY sah.store_account_history_date desc, sah.store_account_history_id DESC";

		$show_records = $_SESSION[$session_name]["show_records"];

		//if ($show_records != "ALL") {
		//	$acc_statement_split_object = new splitPageResults($_REQUEST['page'], ($show_records=='' || $show_records=='DEFAULT' ? MAX_DISPLAY_SEARCH_RESULTS : (int) $show_records), $acc_statement_select_sql, $acc_statement_select_sql_numrows, true);
		//}
		$acc_statement_result_sql = tep_db_query($acc_statement_select_sql);

		ob_start();
?>
			<table width="100%" border="0" cellspacing="0" cellpadding="0">
				<tr>
					<td>&nbsp;</td>
				</tr>
				<tr>
					<td>
						<table width="100%" border="0" align="center" cellpadding="0" cellspacing="0" class="smallText" valign="middle">
							<tr>
								<td align="right" width="1"><?=tep_draw_separator('pixel_trans.gif', '1', '1')?></td>
								<td class="boxHeaderLeftTable"><?=tep_draw_separator('pixel_trans.gif', '1', '1')?></td>
								<td class="boxHeaderCenterTable">
									<table border="0" cellpadding="0" cellspacing="0" width="100%">
									<tr valign="center">
										<th width="12%"><?=TABLE_HEADING_ACC_STAT_DATETIME?></th>
										<th><?=TABLE_HEADING_ACC_STAT_ACTIVITY?></th>
										<th width="15%" align="center" nowrap><?=TABLE_HEADING_ACC_STAT_PAYMENT_STATUS?></th>
									    <th width="12%" align="center"><?=TABLE_HEADING_ACC_STAT_DEBIT?></th>
									    <th width="12%" align="right"><?=TABLE_HEADING_ACC_STAT_CREDIT?></th>
									    <th width="12%" align="right"><?=TABLE_HEADING_ACC_STAT_BALANCE?></th>
									    <th width="12%" align="right"><?=TABLE_HEADING_ACTION?></th>
									</tr>
								</table>
					    		</td>
					    		<td class="boxHeaderRightTable"><?=tep_draw_separator('pixel_trans.gif', '1', '1')?></td>
					    		<td align="right" width="1"><?=tep_draw_separator('pixel_trans.gif', '1', '1')?></td>
							</tr>
<?
		$row_count = 0;
		
		echo '<tr>
				<td>'.tep_draw_separator("pixel_trans.gif", "1", "1").'</td>
				<td colspan="3"><table border="0" cellpadding="0" cellspacing="0" width="100%">';
		
		while ($acc_statement_row = tep_db_fetch_array($acc_statement_result_sql)) {
			//$row_style = ($row_count%2) ? 'searchListingEven' : 'searchListingOdd';
			$row_style = 'ordersListing';
	  		$activity_title = '';
			$trans_details = '';
			
			if ($row_count != 0) {
				echo '<tr><td colspan="7"><div class="row_separator"></div></td></tr>';
			}
			
	  		if (tep_not_null($acc_statement_row['activity_title'])) {
	  			$lowercase_activity_title = strtolower(trim($acc_statement_row['activity_title']));
	  			
	  			if ($lowercase_activity_title == 'manual addition') {
	  				$activity_title = TEXT_MANUAL_ADDITION;
	  			} else if($lowercase_activity_title == 'manual deduction') {
	  				$activity_title = TEXT_MANUAL_DEDUCTION;
	  			} else if($lowercase_activity_title == 'compensate') {
	  				$activity_title = TEXT_COMPENSATE;
	  			} else if (strpos($lowercase_activity_title, 'cancelation of payment') !== false) {
	  				$no_cancelation = end(explode(" ",$lowercase_activity_title));
	  				$activity_title = sprintf(TEXT_PAYMENT_CANCELLATION, $no_cancelation);
	  			}  else if (strpos($lowercase_activity_title, 'cancelation of redemption') !== false) {
	  				$no_cancelation = end(explode(" ",$lowercase_activity_title));
	  				$activity_title = sprintf(TEXT_REDEEM_CANCELLATION, $no_cancelation);
	  			}
	  			
	  			$activity_title .= (tep_not_null($acc_statement_row['activity_desc']) ? (($acc_statement_row['store_account_history_activity_desc_show']) ? '<br><div class="paymentRemarkSelectedRow">'.TEXT_ACC_STAT_ADMIN_COMMENT.'<br>' . nl2br($acc_statement_row['activity_desc']) : '') : '') . '</div>';
	  		} else {
	  			switch($acc_statement_row['trans_type']) {
	  				case 'P':
	  					$activity_title = sprintf(TITLE_TRANS_PAYMENT, $acc_statement_row['trans_id']);
	  					break;
	  				case 'S':
	  					$activity_title = sprintf(TITLE_TRANS_SUPPLIER_ORDER, $acc_statement_row['trans_id']);
	  					break;
	  				case 'PWL':
	  					$activity_title = sprintf(TITLE_TRANS_PWL_ORDER, $acc_statement_row['trans_id']);
	  					break;
	  				case 'B':
	  					$activity_title = sprintf(TITLE_TRANS_BUYBACK_ORDER, $acc_statement_row['trans_id']);
	  					break;
	  				default:
	  					$activity_title = $acc_statement_row['trans_type'] . ' ' . $acc_statement_row['trans_id'];
	  					break;
	  			}
	  		}

            $store_payments_read_mode_css = 1;
	  		if ($acc_statement_row['trans_type'] == 'P') {
	  			$payment_info_select_sql = "SELECT sp.store_payments_status, sps.store_payments_status_name, sp.store_payments_read_mode 
	  										FROM " . TABLE_STORE_PAYMENTS . " AS sp
	  										INNER JOIN " . TABLE_STORE_PAYMENTS_STATUS . " sps
	  											ON (sp.store_payments_status=sps.store_payments_status_id
	  											AND sps.language_id = '".$languages_id."')
	  										WHERE sp.store_payments_id='".tep_db_input($acc_statement_row['trans_id'])."'";
	  			$payment_info_result_sql = tep_db_query($payment_info_select_sql);
	  			$payment_info_row = tep_db_fetch_array($payment_info_result_sql);

                $store_payments_read_mode_css = $payment_info_row['store_payments_read_mode'];
	  			if ($payment_info_row['store_payments_status'] == '3' || $payment_info_row['store_payments_status'] == '4') {
	  				$status_update_date_select_sql = "	SELECT date_added
	  													FROM " . TABLE_STORE_PAYMENTS_HISTORY . "
	  													WHERE store_payments_id = '".tep_db_input($acc_statement_row['trans_id'])."'
	  														AND store_payments_status = '".$payment_info_row['store_payments_status']."'
	  													ORDER BY date_added DESC
	  													LIMIT 1";
	  				$status_update_date_result_sql = tep_db_query($status_update_date_select_sql);
		  			$status_update_date_row = tep_db_fetch_array($status_update_date_result_sql);

	  				$payment_status = sprintf(TEXT_ACC_STAT_PAYMENT_STATUS_UPDATE_DATE, $payment_info_row['store_payments_status_name'], $status_update_date_row['date_added']);
	  			} else {
	  				$payment_status = $payment_info_row['store_payments_status_name'];
	  			}
	  		} else {
	  			$payment_status = TEXT_ACC_STAT_NOT_APPLICABLE;
	  		}
			
			$activity_date = tep_datetime_short($acc_statement_row['activity_date'], '%Y-%m-%d %H:%M');
			
			
	  		echo '			<tbody class="'. ($store_payments_read_mode_css > 0 ? '' : 'boldText') .'">
	  		                <tr height="20" class="'.$row_style.'" onmouseover="this.className =\'ordersListingOver\'" onmouseout="this.className =\'ordersListingOut\'">
								<td class="main" valign="top" width="12%" style="padding-left:5px;" nowrap>'.$activity_date.'</td>
							   	<td align="center" class="main" valign="top">'.$activity_title;

			$comments_select_sql = "SELECT store_account_comments_date_added, store_account_comments, store_account_comments_notified
		                            FROM ". TABLE_STORE_ACCOUNT_COMMENTS ." 
		                            WHERE store_account_history_id='". $acc_statement_row['store_account_history_id'] ."'
		                                AND store_account_comments_notified='1'
		                            ORDER BY store_account_comments_id";
            $comments_result_sql = tep_db_query($comments_select_sql);

            if (tep_db_num_rows($comments_result_sql) > 0) {
                while ($comments_row = tep_db_fetch_array($comments_result_sql)) {
					    echo '<hr>'. $comments_row['store_account_comments_date_added'] . '<br><div class="paymentRemarkSelectedRow">'. TEXT_ACC_STAT_ADMIN_COMMENT .'<br>'. nl2br($comments_row['store_account_comments']) . '</div>';
				}
            }

			echo '              </td>
								<td align="center" valign="top" width="15%" class="main">'.$payment_status.'</td>
								<td align="center" valign="top" width="12%" class="main">'.(tep_not_null($acc_statement_row['debit_amount']) ? $currencies->format($acc_statement_row['debit_amount'], false, $acc_statement_row['currency']) : TEXT_ACC_STAT_NOT_APPLICABLE).'</td>
								<td align="right" valign="top" width="12%" class="main">'.(tep_not_null($acc_statement_row['credit_amount']) ? $currencies->format($acc_statement_row['credit_amount'], false, $acc_statement_row['currency']) : TEXT_ACC_STAT_NOT_APPLICABLE).'</td>
								<td align="right" valign="top" width="12%" class="main">'.$currencies->format($acc_statement_row['after_balance'], false, $acc_statement_row['currency']).'</td>
								<td align="right" valign="top" width="12%" class="main">';
	  		if ($acc_statement_row['trans_type'] == 'P') {
				echo 				tep_div_button(2, TEXT_VIEW,'javascript:show_payment_report(\''.tep_db_input($acc_statement_row['trans_id']).'\');', 'style="float:left"', 'gray_button')
									."<br><span class='title-text' id='wbb_div_msgField_".tep_db_input($acc_statement_row['trans_id'])."'></span>";
			} else {
				echo '				&nbsp;';
			}
			echo '				</td>
							  </tr>
		        			  </tbody>';
			
	  		$row_count++;
	  	}
	  	echo '		</table>
	  			</td>
	  			<td>'.tep_draw_separator("pixel_trans.gif", "1", "1").'</td>
	  		</tr>';
?>
						</table>
					</td>
				</tr>
			</table>
<?
		$report_section_html = ob_get_contents();
		ob_end_clean() ;

		// This should be here to let all the session get updated value
		$search_section_html = $this->search_acc_statement($filename, $session_name);

		return $search_section_html . "\n" . $report_section_html;
	}
	
	function _get_payment_info($payment_id) {
		global $languages_id;
		
		$payment_info_select_sql = "SELECT * 
									FROM " . TABLE_STORE_PAYMENTS . "
									WHERE store_payments_id = '" . tep_db_input($payment_id) . "'";
		$payment_info_result_sql = tep_db_query($payment_info_select_sql);
		
		if ($payment_info_row = tep_db_fetch_array($payment_info_result_sql)) {
			$payment_status_select_sql = "	SELECT store_payments_status_name 
	  										FROM " . TABLE_STORE_PAYMENTS_STATUS . " 
	  										WHERE store_payments_status_id = '" . tep_db_input($payment_info_row['store_payments_status']) . "' 
	  											AND language_id='" . $languages_id  . "'";
	  		$payment_status_result_sql = tep_db_query($payment_status_select_sql);
	  		$payment_status_row = tep_db_fetch_array($payment_status_result_sql);
	  		
			$this->info = array('pay_id' => $payment_id,
								'payments_date' => $payment_info_row['store_payments_date'],
								'status' => $payment_info_row['store_payments_status'],
								'status_name' => $payment_info_row['store_payments_status_name'],
								'last_modified' => $payment_info_row['store_payments_last_modified']
								);
			
			$this->beneficiary = array(	'id' => $payment_info_row['user_id'],
	      								'role' => $payment_info_row['user_role'],
	      								'firstname' => $payment_info_row['user_firstname'],
	      								'lastname' => $payment_info_row['user_lastname'],
	      								'email_address' => $payment_info_row['user_email_address']
	      								);
	      	
	      	$this->payment_info = array('request_currency' => $payment_info_row['store_payments_request_currency'],
	      								'request_amount' => $payment_info_row['store_payments_request_amount'],
	      								'fees' => $payment_info_row['store_payments_fees'],
	      								'after_fees_amount' => $payment_info_row['store_payments_after_fees_amount'],
	      								'paid_currency' => $payment_info_row['store_payments_paid_currency'],
	      								'exchange_rate' => $payment_info_row['store_payments_paid_currency_value'],
	      								'paid_amount' => $payment_info_row['store_payments_paid_amount'],
	      								'reference' => $payment_info_row['store_payments_reference'],
	      								'payment_methods_id' => $payment_info_row['store_payments_methods_id'],
	      								'payment_methods_name' => $payment_info_row['store_payments_methods_name'],
	      								'account_book_id' => $payment_info_row['store_payment_account_book_id'],
	      								'payment_methods_alias' => $payment_info_row['user_payment_methods_alias'],
	      								'fees_calculation' => $payment_info_row['store_payments_fees_calculation']
	      								);
		}
	}
	
	function set_max_entry($max_number_of_accounts) {
		$this->max_entry = $max_number_of_accounts;
	}
}
?>