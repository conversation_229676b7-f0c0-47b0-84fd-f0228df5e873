<?php
include_once(DIR_WS_CLASSES . 'curl.php');

class pgs {
	
	function getOrderSiteId($ordersId) {
		$siteID = '0';
		$order_id_select_sql = "	SELECT orders_extra_info_value 
									FROM " . TABLE_ORDERS_EXTRA_INFO . "
									WHERE orders_id = '" . tep_db_input($ordersId) . "'
										AND orders_extra_info_key = 'site_id'";
		$order_id_result_sql = tep_db_query($order_id_select_sql);
		if ($order_id_row_sql = tep_db_fetch_array($order_id_result_sql)) {
			if (isset($order_id_row_sql['orders_extra_info_value']) && $order_id_row_sql['orders_extra_info_value'] > 0) {
				$siteID = $order_id_row_sql['orders_extra_info_value'];
			}
		} 
		return $siteID;
	}
	
	function repostToPGS($paymentModule, $postData) {
		$repostUrl = HTTP_PGS_SERVER . '/IPN/HandleIPN?dp_payment=' . $paymentModule;
		$curlObj = new curl();
		$curlObj->curl_post($repostUrl, $postData);
	}
}

?>
