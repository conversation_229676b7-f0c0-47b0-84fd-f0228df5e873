<?php

class polling {
	var $identity;
	var $active_polls;
	var $active_polls_storage;
	
	function polling($identity, $type='') {
		$this->identity = $identity;
		$this->type = $type;
		
		$this->active_polls = array();
		$this->active_polls_storage = array();
		
		$this->_get_active_polls($type);
	}
	
	function show_polls() {
		$boxContent = 	'<script src="includes/javascript/polling_xmlhttp.js"></script>';
		$boxContent .= 	'<script language="JavaScript">
						<!--
							function getCheckedValue(radioObj) {
								if(!radioObj)
									return "";
								var radioLength = radioObj.length;
								if(radioLength == undefined)
									if(radioObj.checked)
										return radioObj.value;
									else
										return "";
								for(var i = 0; i < radioLength; i++) {
									if(radioObj[i].checked) {
										return radioObj[i].value;
									}
								}
								return "";
							}
							function setCheckedValue(radioObj, newValue) {
								if(!radioObj)
									return;
								var radioLength = radioObj.length;
								if(radioLength == undefined) {
									radioObj.checked = (radioObj.value == newValue.toString());
									return;
								}
								for(var i = 0; i < radioLength; i++) {
									radioObj[i].checked = false;
									if(radioObj[i].value == newValue.toString()) {
										radioObj[i].checked = true;
									}
								}
							}
							function update_poll(poll_id, aa, div_name, form_name, input_name) {
								poll_submit_form_checking(poll_id, aa, div_name, form_name, input_name);
							}
							function poll_submit_form_checking(poll_id, aa, div_name, form_name, input_name) {
								if (checkForSelection(form_name, input_name)) {
									var option_id = getCheckedValue(document.forms[form_name].elements[input_name])
									var user_id = '.$this->identity.';
									var tpl = \''.$this->type.'\';
									submitPollVote(user_id, poll_id, option_id, aa, div_name, form_name, input_name, tpl);
									return false;
								} else {
									alert(\''.WARNING_POLLING_NO_SELECTION.'\');
									return false;
								}
							}
							function poll_comment_form_checking(user_id, poll_id, div_name, form_name, comment_input_name, email_input_name){
								if (document.forms[form_name].elements[comment_input_name].value.length==0){
									alert(\''.WARNING_POLLING_NO_COMMENT.'\');
								} else if (document.forms[form_name].elements[email_input_name].value.length==0){
									alert(\''.WARNING_POLLING_NO_EMAIL.'\');
								} else if (!validateEmail(document.forms[form_name].elements[email_input_name].value)){
									alert(\''.WARNING_POLLING_INVALID_EMAIL.'\');
								} else {
									var comment = document.forms[form_name].elements[comment_input_name].value;
									var email = document.forms[form_name].elements[email_input_name].value;
									var tpl = \''.$this->type.'\';
									submitPollComment(user_id, poll_id, div_name, form_name, comment, email, tpl);							
								}
								return false;
							}
						//-->
						</script>';
		$boxContent .= 	'<div id="poll_votes"><table border="0" width="100%" cellspacing="0" cellpadding="0">';
		
		$total_poll = count($this->active_polls);
		for ($poll_cnt=0; $poll_cnt < $total_poll; $poll_cnt++) {

			$boxContent .= '<tr>
								<td align="center"><div id="poll_vote_'.$this->active_polls[$poll_cnt]['id'].'_div">'.
									tep_draw_form('poll_vote_'.$this->active_polls[$poll_cnt]['id'].'_form', tep_href_link(FILENAME_POLLING, 'action=vote&pollid='.$this->active_polls[$poll_cnt]['id']), 'post', 'onSubmit="return poll_submit_form_checking(\''.$this->active_polls[$poll_cnt]['id'].'\', \''.$this->active_polls[$poll_cnt]['allow_anonymous'].'\', \'poll_vote_'.$this->active_polls[$poll_cnt]['id'].'_div\', \'poll_vote_'.$this->active_polls[$poll_cnt]['id'].'_form\', \'poll_'.$this->active_polls[$poll_cnt]['id'].'\')"') . '
									<table border="0" width="90%" cellspacing="0" cellpadding="4">';
			if ($poll_cnt > 0) {
				$boxContent .= '		<tr>
		               						<td class="systemBoxText" colspan="2"><div class="row_separator">&nbsp;</div></td>
		               					</tr>';
			}
			
			$boxContent .= '			<tr>
					            			<td align="left" colspan="2" style="color:#000000;font-size:11px;font-weight:bold">'.tep_draw_hidden_field('poll_vote_'.$this->active_polls[$poll_cnt]['id'].'_hidden', $this->active_polls[$poll_cnt]['id']).$this->active_polls[$poll_cnt]['question'].'<br /></td>
				            			</tr>';
			
			$total_option = count($this->active_polls[$poll_cnt]['option']);
			for ($opt_cnt=0; $opt_cnt < $total_option; $opt_cnt++) {
				$boxContent .= '		<tr>
				            				<td width="20" valign="top">'.tep_draw_radio_field("poll_".$this->active_polls[$poll_cnt]['id'], $this->active_polls[$poll_cnt]['option'][$opt_cnt]['id'], false, 'id="poll_'.$this->active_polls[$poll_cnt]['id'].'_'.$opt_cnt.'"').'</td><td width="150" style="color:#000000;font-size:11px;text-align:left;"><label for=\"poll_'.$this->active_polls[$poll_cnt]['id']."_".$opt_cnt."\">".$this->active_polls[$poll_cnt]['option'][$opt_cnt]['value'].'</label></td>
					            		</tr>';
			}
			
			$boxContent .= '		</table>
									<div class="dottedLine"><!-- --></div>
									<div style="text-align:center">
										<div style="float:left;">'. 
											tep_div_button(2, BUTTON_VOTE, 'javascript:update_poll(\''.$this->active_polls[$poll_cnt]['id'].'\', \''.$this->active_polls[$poll_cnt]['allow_anonymous'].'\', \'poll_vote_'.$this->active_polls[$poll_cnt]['id'].'_div\', \'poll_vote_'.$this->active_polls[$poll_cnt]['id'].'_form\', \'poll_'.$this->active_polls[$poll_cnt]['id'].'\')', 'style="float:left;padding:0 0 0 7px;"', 'gray_button') . 
										'</div>
										<div style="float:left">' . 
											tep_div_button(2, BUTTON_VIEW_RESULT, 'javascript:;' , 'onclick="showPollResult('.$this->active_polls[$poll_cnt]['id'].', '.$this->identity.', \'' . $this->type . '\');" style="padding:0 0 0 7px;"', 'gray_button') . 
										'</div>
									</div>
									</form>
								</div></td>
							</tr>';
		}
		
		$boxContent .= '</table></div>';
		
		return $boxContent;
	}
	
	function _get_active_polls($type) {
		$index = 0;
		
		$poll_type_where_str = tep_not_null($type) ? " FIND_IN_SET('".(int)$type."', polls_questions_custom_products_type) " : " 1 ";
		
		$datetime_now = date("Y-m-d H:i:s");
		$active_pool_select_sql = "	SELECT polls_questions_id, polls_questions, polls_questions_allow_anonymous  
									FROM " . TABLE_POLLS_QUESTIONS . "
									WHERE polls_questions_status = '1' 
										AND polls_questions_start_date <= '".$datetime_now."' 
										AND polls_questions_end_date >= '".$datetime_now."' 
										AND " . $poll_type_where_str;
		
		if (!tep_session_is_registered('customer_id')) {
			$active_pool_select_sql .= " AND polls_questions_allow_anonymous = '1'";
		}
										
		$active_pool_result_sql = tep_db_query($active_pool_select_sql);
		
		while ($active_pool_row = tep_db_fetch_array($active_pool_result_sql)) {
			$option_array = array();
			$option_select_sql = "	SELECT polls_questions_options_id, polls_questions_options_value
									FROM " . TABLE_POLLS_QUESTIONS_OPTIONS . "
									WHERE polls_questions_id='".tep_db_input($active_pool_row['polls_questions_id'])."' " . "
									ORDER BY polls_questions_options_id";
			$option_result_sql = tep_db_query($option_select_sql);
			
			while ($option_row = tep_db_fetch_array($option_result_sql)) {
				$option_array[] = array('id' => $option_row['polls_questions_options_id'],
										'value' => $option_row['polls_questions_options_value']
										);
			}
			$this->active_polls[$index] = array('id' => $active_pool_row['polls_questions_id'],
												'question' => $active_pool_row['polls_questions'],
												'option' => $option_array,
												'allow_anonymous' => $active_pool_row['polls_questions_allow_anonymous']
												);
			
			$this->active_polls_storage[$active_pool_row['polls_questions_id']] = array('id' => $active_pool_row['polls_questions_id'],
																						'question' => $active_pool_row['polls_questions'],
																						'option' => $option_array,
																						'allow_anonymous' => $active_pool_row['polls_questions_allow_anonymous']
																						);
			
			$index++;
		}
	}
	
	function is_valid_poll($id, $option) {
		$valid = false;
		if (isset($this->active_polls_storage[$id])) {
			$total_option = count($this->active_polls_storage[$id]['option']);
			for ($opt_cnt=0; $opt_cnt < $total_option; $opt_cnt++) {
				if ($this->active_polls_storage[$id]['option'][$opt_cnt]['id'] == $option) {
					$valid = true;
					break;
				}
			}
		}
		
		return $valid;
	}
	
	function submit_vote($id, $option) {
		if ($this->identity!='0'){
			$vote_checking_sql = "	SELECT customers_id
									FROM " . TABLE_POLLS_QUESTIONS_ANSWERS . "
									WHERE polls_questions_id = '".tep_db_input($id)."'
									AND customers_id = '".tep_db_input($this->identity)."'";
			$vote_result_sql = tep_db_query($vote_checking_sql);
			if (tep_db_num_rows($vote_result_sql)) {
				return false;
			}
		} else {
		}
		
		$vote_data_array = array(	'customers_id' => $this->identity,
									'polls_questions_id' => $id,
									'polls_questions_options_id' => $option,
									'polls_questions_answers_date' => 'now()'
								);
		tep_db_perform(TABLE_POLLS_QUESTIONS_ANSWERS, $vote_data_array);
		return true;
	}
	
	function submit_comment($uid, $pid, $comment, $email) {
		if ($uid==0) {
			$id = $email;
		} else {
			$id = $this->identity;
		}
		
		$comment_data_array = array('customers_id' => $id,
									'polls_questions_id' => $pid,
									'polls_comments' => tep_db_prepare_input($comment),
									'polls_comments_date' => 'now()'
								);
		tep_db_perform(TABLE_POLLS_COMMENTS, $comment_data_array);
		
		return true;
	}
	
	function get_active_poll_count() {
		return count($this->active_polls);
	}
	
	function show_poll_result($id, $uid, $tpl) {
		$total_option = count($this->active_polls_storage[$id]['option']);
		$total_vote = 0;
		$option_vote = array();
		echo '<table border="0" width="100%" align="left" cellspacing="0" cellpadding="0">';
		echo '<tr>';
		echo '<td>';
		echo '<table border="0" width="90%" cellspacing="0" cellpadding="0">';
		echo '<tr>';
		echo '<td style="color:#000000;font-size:11px;font-weight:bold;">' . $this->active_polls_storage[$id]['question'] . '<br /><br /></td>';
		echo '</tr>';
		for ($opt_cnt=0; $opt_cnt < $total_option; $opt_cnt++) {
			$count = $this->_get_answered_count($this->active_polls_storage[$id]['option'][$opt_cnt]['id']);
			$option_vote[] = $count;
			$total_vote += (int)$count;
		}
		for ($opt_cnt=0; $opt_cnt < $total_option; $opt_cnt++) {
			$option_question = $this->active_polls_storage[$id]['option'][$opt_cnt]['value'];
			$option_count = $this->_get_answered_count($this->active_polls_storage[$id]['option'][$opt_cnt]['id']);
			echo '<tr>';
			echo '<td style="color:#000000;font-size:11px;">';
			echo "" . $option_question . "<br />";
			echo '</td>';
			echo '</tr>';
			if($total_vote>0){
				echo '<tr>';
				echo '<td style="color:#000000; font-size:10px; background-color:#BBC664; background-image:url(\'' . DIR_WS_IMAGES . '/poll.gif\'); background-position: ' . (round(149*$option_count/$total_vote)) . 'px 0px; background-repeat: no-repeat;">';
				echo '&nbsp;&nbsp;&nbsp;' . (round(100*$option_count/$total_vote, 2)) . '%';
				echo '</td>';
				echo '</tr>';
			} else {
				echo '<tr>';
				echo '<td style="color:#000000; font-size:10px; background-color:#BBC664; background-image:url(\'' . DIR_WS_IMAGES . '/poll.gif\'); background-position: 0px 0px; background-repeat: no-repeat;">';
				echo '&nbsp;&nbsp;&nbsp;0%';
				echo '</td>';
				echo '</tr>';
			}
			echo '<tr>';
			echo '<td style="color:#999999; font-size:10px; font-weight:normal;">';
			echo "" . $option_count . " " . TEXT_POLLING_VOTES . "";
			echo '</td>';
			echo '</tr>';
			echo '<tr>';
			echo '<td style="color:#999999; font-size:10px; font-weight:normal;">';
			echo '&nbsp;';
			echo '</td>';
			echo '</tr>';
		}
		echo '<tr>';
		echo '<td style="color:#000000;font-size:11px;">';
		echo TEXT_POLLING_TOTAL_VOTES . ': <b>'.$total_vote . '</b>';
		echo '</td>';
		echo '</tr>';
		echo '<tr>';
		echo '<td style="color:#000000;font-size:11px;">';
		echo '<span style="color:#0000BB; cursor: pointer;" onclick="showPollComment('.$id.', '.$uid.', '.$tpl.')">' . LINK_POLLING_COMMENT . '</span>';
		echo '</td>';
		echo '</tr>';
		echo '<tr>';
		echo '<td style="color:#999999; font-size:10px; font-weight:normal;">';
		echo '&nbsp;';
		echo '</td>';
		echo '</tr>';
		echo '<tr>';
		echo '<td align="left"><div class="dottedLine"><!-- --></div>';
		echo tep_div_button(2, IMAGE_BUTTON_BACK,'javascript:;', 'onclick="showPollQuestions('.$uid.', '.$tpl.')"', 'gray_button');
		echo '</td>';
		echo '</tr>';
		echo '</table>';
		echo '</td>';
		echo '</tr>';
		echo '</table>';
	}
		
	function _get_answered_count($option) {
		$vote_count_select_sql = "	SELECT COUNT(customers_id) AS total_answer 
									FROM " . TABLE_POLLS_QUESTIONS_ANSWERS . "
									WHERE polls_questions_options_id = '".tep_db_input($option)."'";
		$vote_count_result_sql = tep_db_query($vote_count_select_sql);
		$vote_count_row = tep_db_fetch_array($vote_count_result_sql);
		
		return (int)$vote_count_row['total_answer'];
	}
}

?>