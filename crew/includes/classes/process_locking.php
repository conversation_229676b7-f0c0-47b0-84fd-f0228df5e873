<?php

class process_locking
{
    const DEFAULT_LOG_IDENTITY = 'system';
    const MOVE_ORDER = 'PROCESS_MOVING_ORDER';
    
    private $request_id,
            $request_process,
            $request_by,
            $locked_by,
            $locked_email,
            $locked_datetime,
            $locked_ip,
            $log_obj;
	
	public function __construct() {
        include_once(DIR_WS_CLASSES . 'log.php');
        $this->log_obj = new log_files(self::DEFAULT_LOG_IDENTITY);
	}
	
	public function isLocked($id = NULL, $action = NULL, $return_data = TRUE) {
        $this->prepareLock($id, $action);
        $return_bool = TRUE;
        
        $temp_process_sql = "   SELECT *
                                FROM " . TABLE_TEMP_PROCESS . "
                                WHERE page_name = '" . $this->request_process . "'
                                    AND match_case = '" . $this->request_id . "'";
        $temp_process_result = tep_db_query($temp_process_sql);
        if (tep_db_num_rows($temp_process_result) === 0) {
            $return_bool = FALSE;
        } else {
            $this->locked_by = NULL;
            $this->locked_email = NULL;
            $this->locked_datetime = NULL;
            $this->locked_ip = NULL;
            
            if ($return_data) {
                $this->prepareLock($id, $action, tep_db_fetch_array($temp_process_result));
            }
        }
        
        return $return_bool;
	}
	
    /*
     * return result
     *  - TRUE  : Has been locked by somebody else
     *  - FALSE : Has been locked by you
     *  - NULL  : Has not been locked by anyone
     */
    public function orderIsLocked($who_id, $id = NULL, $action = NULL, $order_locked_array = array()) {
        $this->prepareLock($id, $action, $order_locked_array);
        $return_bool = TRUE;
        
        if ($this->isLocked() !== TRUE) {
            $order_locked_by = $this->getOrderLockedBy($id);
            
            if (tep_not_null($order_locked_by)) {
                if ($order_locked_by == $who_id) {
                    $return_bool = FALSE;
                }
            } else {
                $return_bool = NULL;
            }
        }
        
        return $return_bool;
	}
    
    public function orderIsLockedBy() {
        return !is_null($this->locked_by) ? $this->locked_by : '';
    }
	
    private function generateExtraInfo() {
        $extra_info = array();
        
        if ($this->request_by) {
            $extra_info['request_by'] = $this->request_by;
            
            if ($this->request_by == self::DEFAULT_LOG_IDENTITY) {
                $extra_info['locked_by'] = self::DEFAULT_LOG_IDENTITY;
                $extra_info['locked_email'] = self::DEFAULT_LOG_IDENTITY;
                $extra_info['locked_ip'] = getenv("REMOTE_ADDR");
            }
        }
        
        if ($this->locked_by) {
            $extra_info['locked_by'] = $this->locked_by;
        }
    
        if ($this->locked_email) {
            $extra_info['locked_email'] = $this->locked_email;
        }
        
        if ($this->locked_ip) {
            $extra_info['locked_ip'] = $this->locked_ip;
        }
        
        return json_encode($extra_info);
    }
    
    private function getOrderLockedBy($id) {
        if (is_null($this->locked_by)) {
            $lock_orders_select_sql = " SELECT o.orders_locked_by, o.orders_locked_from_ip, o.orders_locked_datetime, 
                                                a.admin_email_address
                                        FROM " . TABLE_ORDERS . " AS o
                                        LEFT JOIN " . TABLE_ADMIN . " AS a
                                            ON o.orders_locked_by = a.admin_id
                                        WHERE orders_id = '" . $id . "'";
            $lock_orders_result_sql = tep_db_query($lock_orders_select_sql);
            if ($lock_orders_row = tep_db_fetch_array($lock_orders_result_sql)) {
                $this->prepareLock(NULL, NULL, $lock_orders_row);
            }
        }
        
        return $this->locked_by;
    }
    
    public function getOrderIsLockedByEmail() {
        return !is_null($this->locked_email) ? $this->locked_email : ($this->locked_by == 0 ? self::DEFAULT_LOG_IDENTITY : 'UNKNOWN');
    }
        
    public function getOrderIsLockedDT() {
        return !is_null($this->locked_datetime) ? $this->locked_datetime : '';
    }
    
    public function getOrderIsLockedIP() {
        return !is_null($this->locked_ip) ? $this->locked_ip : '';
    }
    
    private function prepareLock($id, $action, $extra_params = array()) {
        if (!is_null($id)) {
            $this->request_id = !empty($id) ? (int)$id : 0;
        }
        
        if (!is_null($action)) {
            $this->request_process = $action;
        }
        
        if (isset($extra_params['extra_info']) && !empty($extra_params['extra_info'])) {
            $extra_params = array_merge(json_decode($extra_params['extra_info'], true), $extra_params);
        }
        
        if (isset($extra_params['request_by'])) {
            $this->request_by = $extra_params['request_by'];
        }
        
        if (isset($extra_params['orders_locked_by'])) {
            if (!empty($extra_params['orders_locked_by']) || $extra_params['orders_locked_by'] == 0) {
                $this->locked_by = $extra_params['orders_locked_by'];
            }
        } else if (isset($extra_params['locked_by'])) {
            $this->locked_by = $extra_params['locked_by'];
        }
        
        if (isset($extra_params['orders_locked_from_ip']) && !empty($extra_params['orders_locked_from_ip'])) {
            $this->locked_ip = $extra_params['orders_locked_from_ip'];
        } else if (isset($extra_params['locked_ip'])) {
            $this->locked_ip = $extra_params['locked_ip'];
        }
        
        if (isset($extra_params['admin_email_address']) && !empty($extra_params['admin_email_address'])) {
            $this->locked_email = $extra_params['admin_email_address'];
        } else if (isset($extra_params['locked_email'])) {
            $this->locked_email = $extra_params['locked_email'];
        }
        
        if (isset($extra_params['orders_locked_datetime']) && !empty($extra_params['orders_locked_datetime'])) {
            $this->locked_datetime = $extra_params['orders_locked_datetime'];
        } else if (isset($extra_params['created_date'])) {
            $this->locked_datetime = $extra_params['created_date'];
        }
    }
    
    public function processLock($id = NULL, $action = NULL, $by = self::DEFAULT_LOG_IDENTITY) {
        $this->prepareLock($id, $action, array('request_by' => $by));
        
        if ($this->isLocked() === FALSE) {
            return $this->insertLock();
        }
        
        return FALSE;
    }
    
    public function processLockOrder($id = NULL, $action = NULL, $by = self::DEFAULT_LOG_IDENTITY) {
        $this->prepareLock($id, $action, array('request_by' => $by));
        
        if ($this->orderIsLocked($by) === NULL) {
            if ($this->insertLock()) {
                $this->log_obj->identity = $by;
                $this->log_obj->insert_orders_log($this->request_id, ORDERS_LOG_LOCK_ORDER, FILENAME_ORDERS);
                return TRUE;
            }
        }
        
        return FALSE;
    }
    
    public function releaseLocked($id = NULL, $action = NULL, $by = self::DEFAULT_LOG_IDENTITY) {
        $this->prepareLock($id, $action);
        
        $delete_temp_process = "DELETE FROM " . TABLE_TEMP_PROCESS . "
                                WHERE page_name = '" . $this->request_process . "'
                                    AND match_case = '" . $this->request_id ."'";
        
        if (tep_db_query($delete_temp_process)) {
            $this->log_obj->identity = $by;
            $this->log_obj->insert_orders_log($this->request_id, ORDERS_LOG_UNLOCK_OWN_ORDER, FILENAME_ORDERS);
            
            return TRUE;
        }
    
        return FALSE;
    }
    
    public function whoLocked($id = NULL, $action = NULL) {
        $this->prepareLock($id, $action);
        
        $temp_process_sql = "   SELECT *
                                FROM " . TABLE_TEMP_PROCESS . "
                                WHERE page_name = '" . $this->request_process . "'
                                    AND match_case = '" . $this->request_id . "'";
        $temp_process_result = tep_db_query($temp_process_sql);
        if ($temp_process_row = tep_db_query($temp_process_result)) {
            $this->prepareLock($id, $action, $temp_process_row);
        }
        
        return array(
            'by' => $this->orderIsLockedBy(),
            'ip' => $this->getOrderIsLockedIP(),
            'email' => $this->getOrderIsLockedByEmail(),
            'datetime' => $this->getOrderIsLockedDT(),
        );
    }
    
    private function insertLock() {
        $temp_process_data_array = array(
            'page_name' => $this->request_process,
            'match_case' => $this->request_id,
            'extra_info' => $this->generateExtraInfo(),
            'created_date' => 'now()'
        );
        
        return tep_db_perform(TABLE_TEMP_PROCESS, $temp_process_data_array);
    }
    
}
?>