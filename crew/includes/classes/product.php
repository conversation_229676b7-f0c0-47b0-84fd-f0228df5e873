<?php

require_once(DIR_WS_CLASSES . 'page.php');

class product {

    var $tpl;
    var $ctype;
    var $categories_id;
    var $has_product = false;
    var $custom_products_type_id_cat = 0;
    var $main_game_id = 0;
    var $display_original_price_group_array;
    var $plain = false; // EXPRESS CHECKOUT RETURN PLAIN DATA RESULT
    var $custom_product_type_id_array = array();
    var $custom_product_type_child_id_array = array();
    var $aws_obj = null;
    var $products_flag_id = '';

    function product($categories_id, $tpl) {
        $this->categories_id = $categories_id;
        $this->tpl = $tpl;
        $this->ctype = 0;
        $this->display_original_price_group_array = array(1, 2, 12, 3, 4, 5, 15);
        $this->plain = $plain; // EXPRESS CHECKOUT RETURN PLAIN DATA RESULT

        $this->aws_obj = new ogm_amazon_ws();
        $this->aws_obj->set_bucket_key('BUCKET_STATIC');
        $this->aws_obj->set_filepath('images/products/');

        $this->page_obj = new page();
    }

    function cpath_check() {
        global $cPath_array, $zone_info_array;
        $game_array = array();
        $include_subcategories = false;

        if (sizeof($cPath_array) > 1) {
            for ($path_cnt = 1; $path_cnt < sizeof($cPath_array); $path_cnt++) {
                $custom_products_type_id_select_sql = "	SELECT custom_products_type_id, custom_products_type_child_id
														FROM " . TABLE_CATEGORIES . "
														WHERE categories_id = '" . (int) $cPath_array[$path_cnt] . "'
															AND custom_products_type_id <> 999";
                $custom_products_type_id_result_sql = tep_db_query($custom_products_type_id_select_sql);
                if ($custom_products_type_id_row = tep_db_fetch_array($custom_products_type_id_result_sql)) {
                    $this->tpl = $custom_products_type_id_row['custom_products_type_id'];
                    $this->ctype = $custom_products_type_id_row['custom_products_type_child_id'];
                    $this->custom_products_type_id_cat = $cPath_array[$path_cnt];
                    break;
                }
            }
        }

        $last_cat_id = $cPath_array[sizeof($cPath_array) - 1];

        if ($this->tpl == 2) {
            $include_subcategories = true;
        }

        if (tep_count_products_in_category($last_cat_id, false, false, $include_subcategories) > 0) {
            $this->has_product = true;
        }

        if (is_array($zone_info_array[1]->zone_categories_id)) {
            $game_array = array_intersect($zone_info_array[1]->zone_categories_id, $cPath_array);
        }

        if (sizeof($game_array) > 0) {
            array_multisort($game_array);

            $this->main_game_id = $game_array[0];
        }
    }

    function set_product_flag_id($str) {
        $this->products_flag_id = $str;
    }

    function get_product_flag_id_condition($field_name) {
        $return_str = '';

        if (tep_not_empty($this->products_flag_id)) {
            $return_str = ' AND NOT FIND_IN_SET(' . $this->products_flag_id . ', ' . $field_name . ') ';
        }

        return $return_str;
    }

    function get_categories_info($categories_id, $field_name, $languages_id, $default_languages_id) {
        $categories_info_select_sql = "	SELECT " . $field_name . "
										FROM " . TABLE_CATEGORIES_DESCRIPTION . "
										WHERE categories_id = '" . (int) $categories_id . "'
										AND " . $field_name . " <> ''
												and (IF(language_id = '" . $languages_id . "' , 1, IF (( SELECT COUNT(categories_id) > 0
																									 FROM " . TABLE_CATEGORIES_DESCRIPTION . "
																									 WHERE categories_id = '" . (int) $categories_id . "'
																									 	and language_id = '" . $languages_id . "'
																									 AND " . $field_name . " <> ''
																										), 0, language_id = '" . $default_languages_id . "')))";
        $categories_info_result_sql = tep_db_query($categories_info_select_sql);
        $categories_info_row = tep_db_fetch_array($categories_info_result_sql);

        return $categories_info_row[$field_name];
    }

    function get_products_info($products_id, $field_name, $languages_id, $default_languages_id) {
        global $memcache_obj;

        $available_field_name = array('products_image', 'products_image_title', 'products_description_image', 'products_description_image_title');

        $products_info = '';

        $cache_key = TABLE_PRODUCTS_DESCRIPTION . '/get/' . $field_name . '/products_id/' . $products_id . '/language/' . $languages_id;

        $cache_result = $memcache_obj->fetch($cache_key);

        if ($cache_result !== FALSE && in_array($field_name, $available_field_name)) {
            $products_info = $cache_result;
        } else {
            $products_info_select_sql = "	SELECT " . $field_name . "
											FROM " . TABLE_PRODUCTS_DESCRIPTION . "
											WHERE products_id = '" . (int) $products_id . "'
											AND " . $field_name . " <> ''
													and (IF(language_id = '" . $languages_id . "' , 1, IF (( SELECT COUNT(products_id) > 0
																										 FROM " . TABLE_PRODUCTS_DESCRIPTION . "
																										 WHERE products_id = '" . (int) $products_id . "'
																										 	and language_id = '" . $languages_id . "'
																										 AND " . $field_name . " <> ''
																											), 0, language_id = '" . $default_languages_id . "')))";
            $products_info_result_sql = tep_db_query($products_info_select_sql);
            $products_info_row = tep_db_fetch_array($products_info_result_sql);
            $products_info = $products_info_row[$field_name];

            $memcache_obj->store($cache_key, $products_info, 86400);
        }

        return $products_info;
    }

    function get_promotion_products_info($products_id, $field_name, $languages_id, $default_languages_id) {
        $pproducts_info = '';

        $pproducts_info_select_sql = "	SELECT " . $field_name . "
										FROM " . TABLE_PRODUCTS_PROMOTION_DESCRIPTION . "
										WHERE products_id = '" . (int) $products_id . "'
										AND " . $field_name . " <> ''
												AND (IF(language_id = '" . $languages_id . "' , 1, IF (( SELECT COUNT(products_id) > 0
																									 FROM " . TABLE_PRODUCTS_PROMOTION_DESCRIPTION . "
																									 WHERE products_id = '" . (int) $products_id . "'
																									 	and language_id = '" . $languages_id . "'
																									 AND " . $field_name . " <> ''
																										), 0, language_id = '" . $default_languages_id . "')))";
        $pproducts_info_result_sql = tep_db_query($pproducts_info_select_sql);
        if ($pproducts_info_row = tep_db_fetch_array($pproducts_info_result_sql)) {
            $pproducts_info = $pproducts_info_row[$field_name];
        }

        return $pproducts_info;
    }

    function get_listing_page() {
        switch ($this->tpl) {
            case 0:
                $content = CONTENT_INDEX_GAME_CURRENCY_PRODUCTS;
                break;
            case 1:
                $content = CONTENT_INDEX_PWL_PWL_PRODUCTS;
                break;
            case 2:
                $content = CONTENT_INDEX_CDKEY_PRODUCTS;
                break;
            case 3:
                $content = CONTENT_INDEX_PRODUCTS; //CONTENT_INDEX_STORE_CREDIT_PRODUCTS;
                break;
            case 4:
                $content = CONTENT_INDEX_HLA_PRODUCTS;
                break;
            default:
                $content = CONTENT_INDEX_PRODUCTS;
                break;
        }

        return $content;
    }

    function get_product_listing() {
        global $cPath_array;
        $cat_arr = array();

        $last_cat_id = $cPath_array[sizeof($cPath_array) - 1];
        $cat_arr[] = $last_cat_id;

        if ($this->has_product) {
            if ($this->tpl == 2) {
                tep_get_subcategories($cat_arr, $last_cat_id, 1);
            }

            if ($this->tpl == 3) {
                $page_html = $this->get_store_credits_list($cat_arr);
            } else if ($this->tpl == 2) {
                if ($this->plain) {
                    $page_html = $this->get_plain_products_list($cat_arr);
                } else {
                    $page_html = $this->get_products_list($cat_arr);
                }
            } else {
                if ($this->plain) {
                    $page_html = $this->get_plain_products_list($cat_arr);
                } else {
                    $page_html = $this->get_products_list($cat_arr);
                }
            }
        } else {
            if ($this->tpl == 2) {
                tep_get_subcategories($cat_arr, $last_cat_id, 1);
            } else if ($this->tpl == 3) {
                $page_html = $this->get_store_credits_list($cat_arr);
            } else {
                if ($this->plain) {
                    $page_html = $this->get_plain_cat_listing($last_cat_id);
                } else {
                    $page_html = $this->get_cat_listing($last_cat_id);
                }
            }
        }

        return $page_html;
    }

    function get_category_dimension($category_image) {
        $img_w = $img_h = '';

        $this->aws_obj->set_filepath('images/category/');
        $image_info_array = $this->aws_obj->get_image_info($category_image);

        if (tep_not_null($image_info_array)) {
            $img_src = $image_info_array['src'];
        } else if (tep_not_null($category_image) && file_exists(DIR_FS_IMAGES . 'category/' . $category_image)) {
            $img_src = THEMA_IMAGES . 'category/' . $category_image;
        } else {
            $img_src = THEMA_IMAGES . 'no_product_image.gif';
        }

        return array($img_src, $img_w, $img_h);
    }

    function get_product_dimension($products_image) {
        $pro_img_w = $pro_img_h = '';

        $this->aws_obj->set_filepath('images/products/');
        $products_image_info_array = $this->aws_obj->get_image_info($products_image);

        if (tep_not_null($products_image_info_array)) {
            $pro_img = $products_image_info_array['src'];
        } else if (tep_not_null($products_image) && file_exists(DIR_FS_IMAGES . 'products/' . $products_image)) {
            $pro_img = THEMA_IMAGES . 'products/' . $products_image;
        } else {
            $pro_img = THEMA_IMAGES . 'no_product_image.gif';
        }

        return array($pro_img, $pro_img_w, $pro_img_h);
    }

    private function get_image_html($id, $id_type) {
        global $languages_id, $default_languages_id;
        $image_tag_string = $img_w = $img_h = '';

        if ($id_type == 'category') {
            $category_image = $this->get_categories_info($id, 'categories_image', $languages_id, $default_languages_id);
            list($img_src, $img_w, $img_h) = $this->get_category_dimension($category_image);
            $image_tag_string = tep_image($img_src, $this->get_categories_info($id, 'categories_image_title', $languages_id, $default_languages_id), $img_w, $img_h);
        } else { // product || promotion_product
            $product_image = $product_image_title = '';

            if ($id_type == 'promotion_product') {
                $product_image = $this->get_promotion_products_info($id, 'promotion_image', $languages_id, $default_languages_id);
                $product_image_title = $this->get_promotion_products_info($id, 'promotion_image_title', $languages_id, $default_languages_id);
            }

            if (!tep_not_null($product_image)) {
                $product_image = $this->get_products_info($id, 'products_image', $languages_id, $default_languages_id);
            }

            if (!tep_not_null($product_image_title)) {
                $product_image_title = $this->get_products_info($id, 'products_image_title', $languages_id, $default_languages_id);
            }

            list($img_src, $img_w, $img_h) = $this->get_product_dimension($product_image);
            $image_tag_string = tep_image($img_src, $product_image_title, $img_w, $img_h);
        }

        return $image_tag_string;
    }

    function get_categories_list($categories_id) {
        global $customers_groups_id, $languages_id;
        $res_array = array();

        $cat_select_sql = "	SELECT c.categories_id, c.parent_id, cd.categories_name, cd.categories_pin_yin
							FROM " . TABLE_CATEGORIES . " AS c
							INNER JOIN " . TABLE_CATEGORIES_DESCRIPTION . " AS cd
								ON (c.categories_id = cd.categories_id AND cd.language_id = 1)
							INNER JOIN " . TABLE_CATEGORIES_GROUPS . " AS cg
								ON (cg.categories_id = c.categories_id)
							WHERE c.parent_id = '" . (int) $categories_id . "'
								AND c.categories_status = 1
								AND ((cg.groups_id = '" . (int) $customers_groups_id . "') OR (cg.groups_id = 0))
							ORDER BY c.sort_order, cd.categories_name";
        $cat_result_sql = tep_db_query($cat_select_sql);
        while ($cat_row = tep_db_fetch_array($cat_result_sql)) {
            $display_name = $cat_row['categories_name'];
            $cat_pin_yin = $cat_row['categories_pin_yin'];

            if ($languages_id != 1) {
                $display_name = tep_get_categories_name($cat_row['categories_id'], $languages_id);
                $cat_pin_yin = tep_get_categories_pin_yin($cat_row['categories_id'], $languages_id);
                if ($this->plain) {
                    $display_name = strip_tags($display_name);
                }
            }

            $this->get_full_cat_name($cat_row['categories_id'], $cat_row['categories_id'], $cat_pin_yin, $display_name, $res_array);
        }

        return $res_array;
    }

    function get_full_cat_name($parent_id, $categories_id, $cat_internal_name, $display_name, &$res_array) {
        global $customers_groups_id, $languages_id;

        $cat_select_sql = "	SELECT c.categories_id, c.parent_id, cd.categories_name, cd.categories_pin_yin
							FROM " . TABLE_CATEGORIES . " AS c
							INNER JOIN " . TABLE_CATEGORIES_DESCRIPTION . " AS cd
								ON (c.categories_id = cd.categories_id AND cd.language_id = 1)
							INNER JOIN " . TABLE_CATEGORIES_GROUPS . " AS cg
								ON (cg.categories_id = c.categories_id)
							WHERE c.parent_id = '" . (int) $categories_id . "'
								AND c.categories_status = 1
								AND ((cg.groups_id = '" . (int) $customers_groups_id . "') OR (cg.groups_id = 0))";
        $cat_result_sql = tep_db_query($cat_select_sql);
        if (tep_db_num_rows($cat_result_sql) > 0) {
            while ($cat_row = tep_db_fetch_array($cat_result_sql)) {
                $name_append = $cat_row['categories_name'];
                $pin_yin_append = $cat_row['categories_pin_yin'];

                if ($languages_id != 1) {
                    $name_append = tep_get_categories_name($cat_row['categories_id'], $languages_id);
                    $pin_yin_append = tep_get_categories_pin_yin($cat_row['categories_id'], $languages_id);
                }

                $this->get_full_cat_name($parent_id, $cat_row['categories_id'], $cat_internal_name . ' - ' . $pin_yin_append, $display_name . ' - ' . $name_append, $res_array);
            }
        } else {
            if ($this->plain) {
                $res_array[] = array('id' => $categories_id,
                    'name' => strip_tags($display_name));
            } else {
                $res_array[$parent_id][] = array('id' => $categories_id,
                    'cat_internal_name' => $cat_internal_name,
                    'display_name' => strip_tags($display_name)
                );
            }
        }

        return true;
    }

    function get_cat_listing($categories_id) {
        $cat_list_html = '';

        if ($this->tpl == 0) {
            $first_char = '';
            $alphabet_array = array();

            for ($letter = 65; $letter <= 90; $letter++) {
                $alphabet_array[chr($letter)] = array();
            }

            $alphabet_array['numeric'] = array();

            $categories_list_array = $this->get_categories_list($categories_id);

            foreach ($categories_list_array as $cat_id => $cat_info_array) {
                foreach ($cat_info_array as $num => $cat_array) {
                    $first_char = substr($cat_array['cat_internal_name'], 0, 1);

                    if (preg_match("/[A-Z\s_]/i", $first_char)) {
                        $alphabet_array[strtoupper($first_char)][$cat_id][] = array('id' => $cat_array['id'], 'display_name' => $cat_array['display_name']);
                    } else { // Special character & numeric will be here.
                        $alphabet_array['numeric'][$cat_id][] = array('id' => $cat_array['id'], 'display_name' => $cat_array['display_name']);
                    }
                }
            }

            $cat_menu_html = '	<div class="middleBoxHeader">
									<div style="padding:7px 0 0 7px;">';
            $cat_list_html = '	<div class="middleBoxContent">';

            $first_entry_flag1 = false;
            $row_count = 0;
            $col_cnt = 0;

            foreach ($alphabet_array as $alphabet_key => $categories_info_array) {
                $row_count++;

                if (sizeof($alphabet_array[$alphabet_key]) > 0) {
                    $cat_menu_html .= '	<div class="alphabetSeletor" id="alpha_nav_' . $alphabet_key . '_link">
											<a href="javascript:show_alpha_div(\'alpha_nav_' . $alphabet_key . '\')">' . (($alphabet_key == 'numeric') ? '0 - 9' : $alphabet_key) . '</a>
										</div>
										<div class="alphabetSeletor" id="alpha_nav_' . $alphabet_key . '_selected" style="display:none">
											<h3>' . $alphabet_key . '</h3>
										</div>';

                    $cat_list_html .= '	<div id="alpha_div_' . $alphabet_key . '">
											<table border="0" cellpadding="7" cellspacing="0" width="100%">
												<tr>
													<td><h2>' . $alphabet_key . '</h2></td>
												</tr>
												<tr>
													<td>
														<table border="0" cellpadding="0" cellspacing="0" width="100%">';

                    foreach ($categories_info_array as $main_cat_id => $cat_detail_array) {
                        if (($col_cnt % 4) == 0) {
                            $cat_list_html .= '					<tr>';
                        }

                        $cat_list_html .= '							<td width="20%" valign="top">
																		<table border="0" cellpadding="2" cellspacing="0" width="100%">';

                        $col_cnt++;

                        foreach ($cat_detail_array as $number => $cat_info) {
                            $cat_list_html .= '								<tr>
																				<td>';

                            if ($cat_info['id'] == $main_cat_id) {
                                $cat_list_html .= '									<a href="' . tep_href_link(FILENAME_DEFAULT, tep_get_all_get_params(array('cPath')) . 'cPath=' . $_REQUEST['cPath'] . '_' . $cat_info['id']) . '">' . $cat_info['display_name'] . '</a>';
                            } else {
                                $cat_list_html .= '									<a href="' . tep_href_link(FILENAME_DEFAULT, tep_get_all_get_params(array('cPath')) . 'cPath=' . $_REQUEST['cPath'] . '_' . $main_cat_id . '_' . $cat_info['id']) . '">' . $cat_info['display_name'] . '</a>';
                            }

                            $cat_list_html .= '									</td>
																			</tr>';
                        }

                        $cat_list_html .= '								</table>
																	</td>';

                        if (($col_cnt % 4) == 0) {
                            $cat_list_html .= '					</tr>
																<tr>
																	<td>' . tep_draw_separator('pixel_trans.gif', '100%', '10') . '</td>
																</tr>';
                        }
                    }

                    $cat_list_html .= '					</table>
													</td>
												</tr>
											</table>
											<div class="dottedLine" style="border-color: #CECECE;"></div>
											<table border="0" cellpadding="0" cellspacing="0" width="97%" align="center">
												<tr>
													<td>
														<a href="' . tep_href_link(FILENAME_DEFAULT, tep_get_all_get_params()) . '#top' . '">' . TEXT_BACK_TO_TOP . '</a>
													</td>
												</tr>
											</table>';

                    if ($row_count < sizeof($alphabet_array) - 1) {
                        $cat_list_html .= '	<div class="dottedLine" style="border-color: #CECECE;"></div><div style="height: 5px;"></div>';
                    } else {
                        $cat_list_html .= '	<div style="height: 5px;"></div>';
                    }

                    $cat_list_html .= '	</div>';
                } else {
                    $cat_menu_html .= '	<div class="alphabetSeletor">' . (($alphabet_key == 'numeric') ? '0 - 9' : $alphabet_key) . '</div>';
                }

                $col_cnt = 0;
            }

            $cat_menu_html .= '			<div class="alphabetSeletor" id="alpha_nav_all_games_link" style="display:none">
											<a href="javascript:show_alpha_div(\'alpha_nav_all_games\')">' . HEADER_TITLE_SHOW_ALL . '</a>
										</div>
										<div class="alphabetSeletor" id="alpha_nav_all_games_selected">
											<h3>' . HEADER_TITLE_SHOW_ALL . '</h3>
										</div>
									</div>
								</div>';

            $cat_list_html .= '</div>';

            $cat_html .= '	<div class="middleBox" style="border-width:0px">' . $cat_menu_html . $cat_list_html . '</div>';
        } else if ($this->tpl == 1) {
            global $customers_groups_id, $languages_id;
            $product_count = 0;

            $cat_html = '		<table border="0" width="100%" cellspacing="0" cellpadding="1">
									<tr>
										<td width="14px"></td>
										<td>
											<table border="0" width="100%" cellspacing="0" cellpadding="0">';

            $cat_info_select_sql = "	SELECT c.categories_id
										FROM " . TABLE_CATEGORIES . " AS c
										INNER JOIN " . TABLE_CATEGORIES_GROUPS . " AS cg
											ON (cg.categories_id = c.categories_id)
										WHERE c.parent_id = '" . (int) $categories_id . "'
											AND c.categories_status = 1
											AND ((cg.groups_id = '" . (int) $customers_groups_id . "') OR (cg.groups_id = 0))
										ORDER BY c.sort_order";
            $cat_info_result_sql = tep_db_query($cat_info_select_sql);
            if (tep_db_num_rows($cat_info_result_sql) > 0) {
                while ($cat_info_row = tep_db_fetch_array($cat_info_result_sql)) {
                    $pwl_cat_name = tep_get_categories_name($cat_info_row['categories_id'], $languages_id);
                    $pwl_cat_description = tep_get_categories_description($cat_info_row['categories_id'], $languages_id);

                    $product_count++;

                    $cat_html .= '				<tr>
													<td colspan="2">' . tep_draw_separator('pixel_trans.gif', '100%', '5') . '</td>
												</tr>
												<tr>
													<td>
														<table border="0" width="100%" cellspacing="3" cellpadding="0">
															<tr>
																<td style="font-size:12px;font-weight:bold;">' . $pwl_cat_name . '</td>
															</tr>
															<tr>
																<td class="main">' . $pwl_cat_description . '</td>
															</tr>
														</table>
													</td>
													<td width="23%" align="center" valign="top" nowrap>' .
                            tep_image_button2('green', tep_href_link(FILENAME_DEFAULT, tep_get_all_get_params(array('cPath')) . 'cPath=' . $_REQUEST['cPath'] . '_' . $cat_info_row['categories_id']), TEXT_VIEW_PRODUCTS, '', 'style="padding:0px;"') .
                            '</td>
												</tr>
												<tr>
													<td colspan="2">' . tep_draw_separator('pixel_trans.gif', '100%', '5') . '</td>
												</tr>';
                    if ($product_count < tep_db_num_rows($cat_info_result_sql)) {
                        $cat_html .= '			<tr><td colspan="3"><div class="row_separator"><table border="0" width="100%" cellspacing="0" cellpadding="0"><tr><td></td></tr></table></div></td></tr>';
                    }
                }
            } else {
                $cat_html .= '					<tr>
													<td class="main">' . TEXT_NO_PRODUCTS . '</td>
												</tr>';
            }

            $cat_html .= '					</table>
										</td>
										<td width="14px"></td>
									</tr>
								</table>';
        }

        return $cat_html;
    }

    function get_products_list($category_array) {
        global $languages_id, $customer_id, $customers_groups_id, $currencies, $PHP_SELF, $currency, $default_languages_id, $page, $page_info, $current_category_id;

        include_once(DIR_WS_CLASSES . 'direct_topup.php');

        $cat_drop_down_array = array();

        if ($this->custom_products_type_id_cat > 0) {
            $categories_list_array = $this->get_categories_list($this->custom_products_type_id_cat);
        } else {
            $categories_list_array = array();
        }

        foreach ($categories_list_array as $parent_cat_id => $cat_details_array) {
            for ($cat_cnt = 0, $cat_total = sizeof($cat_details_array); $cat_cnt < $cat_total; $cat_cnt++) {
                //tep_get_parent_categories($parent_categories_array, $cat_details_array[$cat_cnt]['id']);
                $this_parent_categories_path = substr(tep_get_categories_parent_path($cat_details_array[$cat_cnt]['id']), 1, -1);

                if (tep_not_null($this_parent_categories_path)) {
                    //$parent_categories_array = array_reverse($parent_categories_array);
                    $cat_drop_down_array[] = array('id' => $this_parent_categories_path . '_' . $cat_details_array[$cat_cnt]['id'], 'text' => $cat_details_array[$cat_cnt]['display_name']);
                }
            }
        }

        $op_popup = 'http://kb.offgamers.com/en/category/my-account/wor-token/';
        $gst_popup = '/popup/gst.html';

        if ($languages_id == 2 || $languages_id == 3) {
            $op_popup = 'http://kb.offgamers.com/zhcn/category/my-account/wor-token/';
            $gst_popup = '/popup/gst_cn.html';
        }

        # display GST description
        $products_gst_msg = (tep_not_null($_SESSION['RegionGST']['tax_title']) ? '<br /><a href="javascript:;" onclick="window.open(\'' . tep_href_link($gst_popup) . '\',\'mywindow\',\'width=400,height=180,scrollbars=yes\');"><span style="font-weight: normal;">' . sprintf(WARNING_PRODUCT_PRICE_WITHOUT_GST, $_SESSION['RegionGST']['tax_title']) . '</span></a>' : '');

        $product_count = 0;
        $products_html = '	<script language="javascript" src="' . DIR_WS_JAVASCRIPT . 'ogm_tooltips/ogm_tooltips.js"></script>
							<link rel="stylesheet" href="' . DIR_WS_JAVASCRIPT . 'ogm_tooltips/ogm_tooltips.css" type="text/css">
							<script src="' . DIR_WS_JAVASCRIPT . 'dhtmlxCombo/dhtmlxcommon.js"></script>
							<script src="' . DIR_WS_JAVASCRIPT . 'dhtmlxCombo/dhtmlxcombo.js"></script>
							<script src="' . DIR_WS_JAVASCRIPT . 'dhtmlxCombo/ext/dhtmlxcombo_whp.js" type="text/javascript"></script>';

        if ($this->tpl != 2) {
            $products_html .= '<table border="0" width="100%" cellspacing="0" cellpadding="0">
								<tr>
									<td width="14px"></td>
									<td>
										<table border="0" width="100%" cellspacing="0" cellpadding="0">';
        }

        if ($this->tpl != 2 && $this->tpl != 4) {
            $products_html .= '				<tr>
												<td colspan="3">' . tep_draw_separator('pixel_trans.gif', '100%', '10') . '</td>
											</tr>';
            if (count($cat_drop_down_array)) {
                $products_html .= '
											<tr>
												<td colspan="3" align="right">
													<table border="0" cellspacing="0" cellpadding="0" align="right">
														<tr>';

                if ($this->tpl == 1) {
                    $products_html .= '						<td style="font-size:12px;font-weight:bold;">' . BOX_HEADING_CATEGORIES . ':&nbsp;</td>';
                }

                $products_html .= '							<td>' . tep_draw_pull_down_menu('categories_selection', $cat_drop_down_array, $_REQUEST['cPath'], 'onchange="changeCategory(this);" class="ezInputField" style="width:auto;height:21;"') . '&nbsp;&nbsp;</td>
														</tr>
													</table>
												</td>
											</tr>';
            }
            $products_html .= '				<tr>
												<td colspan="3">' . tep_draw_separator('pixel_trans.gif', '100%', '10') . '</td>
											</tr>
											<tr>
												<td colspan="3"><div class="row_separator"><!-- --></div></td>
											</tr>';
        }

        $direct_topup_obj = new direct_topup();

        $count_index = 0;
        $load_products_info_array = array();
        $load_counter_array = array();
        $grouping_data_array = array();
        $general_content_array = array();
        $promotion_content_array = array();
        $dtu_btn_info_array = array('color' => 'yellow', 'label' => IMAGE_BUTTON_DIRECT_TOP_UP);    // Start Get Button Label

        $product_info_select_sql = "	SELECT DISTINCT(p.products_id), p.products_bundle, p.products_bundle_dynamic, p.custom_products_type_id,
											p.products_price, p.products_tax_class_id, p.products_quantity, p.products_add_to_cart_msg, p.products_sort_order,
											ptc.categories_id,
											c.sort_order
										FROM " . TABLE_PRODUCTS . " AS p
										INNER JOIN " . TABLE_PRODUCTS_TO_CATEGORIES . " AS ptc
											ON (p.products_id = ptc.products_id)
										INNER JOIN " . TABLE_CATEGORIES_GROUPS . " AS cg
											ON (cg.categories_id = ptc.categories_id)
										INNER JOIN " . TABLE_CATEGORIES . " AS c
											ON (c.categories_id = ptc.categories_id AND c.categories_status = 1)
										WHERE ptc.categories_id IN ('" . implode("', '", $category_array) . "')
											AND ((p.custom_products_type_id = '" . (int) $this->tpl . "') OR (p.custom_products_type_id = 0))
											AND p.products_status = 1
											AND p.products_display = 1
											AND ((cg.groups_id = '" . (int) $customers_groups_id . "') OR (cg.groups_id = 0))
										ORDER BY p.products_sort_order";
        $product_info_result_sql = tep_db_query($product_info_select_sql);
        $total_rows = tep_db_num_rows($product_info_result_sql);
        while ($product_info_row = tep_db_fetch_array($product_info_result_sql)) {
            $product_count++;
            $add_to_cart_msg = $product_info_row['products_add_to_cart_msg'];

            if ($product_info_row['products_bundle'] == 'yes') {
                $status_info = tep_product_add_to_cart_permission($product_info_row['products_id'], 'products_bundle');
            } else if ($product_info_row['products_bundle_dynamic'] == 'yes') {
                $status_info = tep_product_add_to_cart_permission($product_info_row['products_id'], 'products_bundle_dynamic');
            } else {
                $status_info = tep_product_add_to_cart_permission($product_info_row['products_id'], '');
            }

            $pid = $product_info_row['products_id'];
            $product_name = tep_get_products_name($pid, $languages_id);
            $show_it = $status_info['show'];
            $special_price = tep_get_products_special_price($pid);
            $normal_price = $product_info_row['products_price'];

            $cust_select_sql = "	SELECT customers_discount
		   							FROM " . TABLE_CUSTOMERS . "
		   							WHERE customers_id = '" . (int) $customer_id . "'";
            $cust_result_sql = tep_db_query($cust_select_sql);
            $cust_row_sql = tep_db_fetch_array($cust_result_sql);

            $cust_group_select_sql = "	SELECT customers_groups_name
										FROM " . TABLE_CUSTOMERS_GROUPS . "
										WHERE customers_groups_id = '" . tep_db_input($customers_groups_id) . "'";
            $cust_group_result_sql = tep_db_query($cust_group_select_sql);
            $cust_group_row_sql = tep_db_fetch_array($cust_group_result_sql);

            $customers_groups_info_array = tep_get_customer_group_discount($customer_id, $pid, 'product');
            $customers_groups_discount = $customers_groups_info_array['cust_group_discount'];

            $total_customer_rebate = $customers_groups_info_array['cust_group_rebate'];
            $total_customer_discount = $cust_row_sql['customers_discount'] + $customers_groups_discount;

            if ($this->tpl == 0) {
                $currency_count_index = $this->get_count_index_by_type($count_index, 'c');

                if ($show_it == 0) {
                    $btn_onclick = '';
                    $btn_info_array = array('color' => 'gray', 'label' => IMAGE_BUTTON_OUT_OF_STOCK);
                } else if ($show_it == 1) {
                    $btn_info_array = array('color' => 'green', 'label' => IMAGE_BUTTON_BUY_NOW);
                } else {
                    if ($status_info['is_future_product'] == '1') {
                        $btn_info_array = array('color' => 'green', 'label' => IMAGE_BUTTON_PRE_ORDER);
                    } else {
                        $btn_info_array = array('color' => 'green', 'label' => IMAGE_BUTTON_BUY_NOW);
                    }
                }

                $btn_onclick = $btn_info_array['color'] != 'gray' ? ' onclick="pfv(this.id)"' : ''; // ' onclick="showMe(\'' . $pid . '\');"';

                $products_html .= '			<tr>
												<td colspan="3">' . tep_draw_separator('pixel_trans.gif', '100%', '4') . '</td>
											</tr>
											<tr>
												<td class="main" valign="top" width="46%">
													<table border="0" cellspacing="0" cellpadding="0" width="100%">
														<tr>
															<td style="font-size:12px;font-weight:bold;vertical-align:top;">' . $product_name . '</td>
														</tr>
														<tr>
															<td class="main">' . TABLE_HEADING_DELIVERY_TIME . ': ' . (tep_not_null(tep_get_eta_string($status_info['pre_order_time'])) ? tep_get_eta_string($status_info['pre_order_time']) : '-') . '</td>
														</tr>
													</table>
												</td>
												<td class="main" align="center" valign="top" width=30%">
													<table border="0" cellspacing="2" cellpadding="0" width="100%">';

                if ($show_it == 2 && abs(PRE_ORDER_DISCOUNT)) {
                    $products_html .= '					<tr>
															<td style="font-size:14px;font-weight:bold;">' . $currencies->display_price($pid, $product_info_row['products_price'], tep_get_tax_rate($product_info_row['products_tax_class_id']), 1, true) . $products_gst_msg . '</td>
														</tr>';

                    if (abs($total_customer_rebate)) {
                        $products_html .= '				<tr>
															<td><a href="' . $op_popup . '" target="_blank" >' . tep_display_op($currencies->rebate_point, ' (' . $cust_group_row_sql['customers_groups_name'] . ')') . '</a></td>
														</tr>';
                    } else {
                        $products_html .= '				<tr>
															<td>' . ' (' . $cust_group_row_sql['customers_groups_name'] . ')' . '</a></td>
														</tr>';
                    }
                } else {
                    if ($cust_group_row_sql['customers_groups_name']) {
                        if (abs($total_customer_discount)) {
                            $products_html .= '			<tr>
															<td style="font-size:14px;font-weight:bold;">' . $currencies->display_price($pid, $product_info_row['products_price'], tep_get_tax_rate($product_info_row['products_tax_class_id']), 1, false) . (in_array($customers_groups_id, $this->display_original_price_group_array) ? '' : $products_gst_msg) . '</td>
														</tr>';
                            if (in_array($customers_groups_id, $this->display_original_price_group_array)) {
                                $products_html .= '	<tr>
															<td class="main" nowrap>
																(<strike>' . $currencies->display_price_original($pid, $normal_price, tep_get_tax_rate($product_info_row['products_tax_class_id']), 1) . '</strike> ' . TEXT_GENERAL . ')' . $products_gst_msg . '
															</td>
														</tr>';
                            }
                        } else {
                            $products_html .= '			<tr>
															<td style="font-size:14px;font-weight:bold;">' . $currencies->display_price($pid, $normal_price, tep_get_tax_rate($product_info_row['products_tax_class_id'])) . $products_gst_msg . '</td>
														</tr>';
                        }

                        if (abs($total_customer_rebate)) {
                            $products_html .= '			<tr>
															<td><a href="' . $op_popup . '" target="_blank" >' . tep_display_op($currencies->rebate_point, ' (' . $cust_group_row_sql['customers_groups_name'] . ')') . '</a></td>
														</tr>';
                        } else {
                            $products_html .= '			<tr>
															<td>' . ' (' . $cust_group_row_sql['customers_groups_name'] . ')' . '</a></td>
														</tr>';
                        }
                    } else {
                        $products_html .= '				<tr>
															<td style="font-size:14px;font-weight:bold;">' . $currencies->display_price($pid, $normal_price, tep_get_tax_rate($product_info_row['products_tax_class_id'])) . $products_gst_msg . '</td>
														</tr>';
                    }
                }

                $products_html .= '					</table>
												</td>
												<td align="center" valign="top" class="main" nowrap>
                                                    <table border="0" cellspacing="0" cellpadding="0" width="95%" align="right">
                                                    <tr id="data_' . $currency_count_index . '"
                                                        data-pid="' . $pid . '"
                                                        data-products-bundle="' . $product_info_row['products_bundle'] . '"
                                                        data-dm=""
                                                        data-cpt-id="' . $this->tpl . '"
                                                        data-name="' . $product_name . '">
                                                        <td align="center" nowrap>
                                                            ' . tep_image_button2($btn_info_array['color'], 'javascript:void(0);', IMAGE_BUTTON_BUY_NOW, 120, ' id="' . $currency_count_index . '"' . $btn_onclick) . '
                                                        </td>
                                                    </tr>
                                                    </table>
                                                </td>
											</tr>
											<tr>
												<td colspan="3">' . tep_draw_separator('pixel_trans.gif', '100%', '4') . '</td>
											</tr>';

                if ($product_count < $total_rows) {
                    $products_html .= '		<tr><td colspan="3"><div class="row_separator"><!-- --></div></td></tr>';
                }
            } else if ($this->tpl == 1) {
                $pwl_products_description = tep_get_products_description($pid, $languages_id);
                $custom_product_info = tep_get_custom_product_listing_info($pid);
                $delivery_time_str = '-';

                if (!$show_it) {
                    // out of stock
                    $delivery_time_str = (tep_not_null(tep_get_eta_string($status_info['pre_order_time'])) ? tep_get_eta_string($status_info['pre_order_time']) : '-');
                } else {
                    if ($show_it == 2) {
                        // pre-order
                        $delivery_time_str = (tep_not_null(tep_get_eta_string($status_info['pre_order_time'])) ? tep_get_eta_string($status_info['pre_order_time']) : '-');
                    }
                }

                $products_html .= '			<tr>
												<td>' . tep_draw_separator('pixel_trans.gif', '100%', '15') . '</td>
											</tr>
											<tr>
												<td>
													<table border="0" cellspacing="0" cellpadding="0" width="100%">
														<tr>
															<td valign="top">
																<table border="0" cellspacing="0" cellpadding="1" width="100%">
																	<tr>
																		<td style="font-size:12px;font-weight:bold;vertical-align:top;">' .
                        $product_name .
                        ($delivery_time_str != '-' ? '<div class="row"><div class="note_img"><span class="label">' . $delivery_time_str . '</span></div>' : '') .
                        '</td>
																	</tr>
																	<tr>
																		<td>
																			<div id="product_list_' . $pid . '" valign="middle">
																				<a href="javascript:;" onclick="display_product(\'' . $pid . '\');">&#8250;&nbsp;' . LINK_MORE_INFO . '</a>
																			</div>
																		</td>
																	</tr>
																</table>
															</td>
															<td valign="top" width="24%">
																<table border="0" cellspacing="0" cellpadding="0" width="100%">';

                if ($custom_product_info['configured'] == '1') {
                    if ($cust_group_row_sql['customers_groups_name']) {
                        if (abs($total_customer_discount)) {
                            $products_html .= '						<tr>
																		<td style="font-size:14px;font-weight:bold;">' . $currencies->display_price($pid, $custom_product_info['price']['value'], tep_get_tax_rate($product_info_row['products_tax_class_id']), 1, false) . (in_array($customers_groups_id, $this->display_original_price_group_array) ? '' : $products_gst_msg) . '</td>
																	</tr>';

                            if (in_array($customers_groups_id, $this->display_original_price_group_array)) {
                                $products_html .= '				<tr>
																		<td class="main" nowrap>
																			(<strike>' . $currencies->display_price_original($pid, $custom_product_info['price']['value'], tep_get_tax_rate($product_info_row['products_tax_class_id']), 1) . '</strike> ' . TEXT_GENERAL . ')' . $products_gst_msg . '
																		</td>
																	</tr>';
                            }
                        } else {
                            $products_html .= '						<tr>
																		<td style="font-size:14px;font-weight:bold;">' . $currencies->display_price($pid, $custom_product_info['price']['value'], tep_get_tax_rate($product_info_row['products_tax_class_id'])) . $products_gst_msg . '</td>
																	</tr>';
                        }

                        if (abs($total_customer_rebate)) {
                            $products_html .= '						<tr>
																		<td nowrap><a href="' . $op_popup . '" target="_blank" >' . tep_display_op($currencies->rebate_point, ' (' . $cust_group_row_sql['customers_groups_name'] . ')') . '</a></td>
																	</tr>';
                        } else {
                            $products_html .= '						<tr>
																		<td nowrap>' . ' (' . $cust_group_row_sql['customers_groups_name'] . ')' . '</a></td>
																	</tr>';
                        }
                    } else {
                        $products_html .= '							<tr>
																		<td style="font-size:14px;font-weight:bold;">' . $currencies->display_price($pid, $normal_price, tep_get_tax_rate($product_info_row['products_tax_class_id'])) . $products_gst_msg . '</td>
																	</tr>';
                    }
                } else {
                    $products_html .= '								<tr>
																		<td style="font-size:14px;font-weight:bold;">' . $currencies->display_price($pid, $custom_product_info['price']['value'], tep_get_tax_rate($product_info_row['products_tax_class_id'])) . $products_gst_msg . '</td>
																	</tr>';
                }

                $products_html .= '								</table>
															</td>
															<td valign="top" align="left" width="20%" nowrap>
																<table border="0" cellspacing="0" cellpadding="0">
																	<tr>
																		<td>';

                if (!$show_it) {
                    $products_html .= tep_image_button2('gray', 'javascript:void(0);', IMAGE_BUTTON_OUT_OF_STOCK, 120);
                } else {
                    $products_html .= tep_draw_form('buy_now_' . $product_info_row['products_id'], tep_href_link(basename($PHP_SELF), tep_get_all_get_params(array('action', 'cPath')) . 'action=buy_now&products_id=' . $product_info_row['products_id'] . '&custom_product=' . (int) $product_info_row['custom_products_type_id']), 'POST', '') . tep_draw_hidden_field("buyqty", '1');
                    $products_html .= tep_image_button2('green', 'javascript:add_to_shopping_cart(\'' . tep_href_link(basename($PHP_SELF), tep_get_all_get_params(array('action')) . 'action=buy_now&products_id=' . $product_info_row['products_id'] . "&custom_product=" . (int) $product_info_row['custom_products_type_id']) . '\' , \'1\', \'' . $product_info_row['products_bundle'] . '\' ,\'' . ((int) $product_info_row['custom_products_type_id'] != 1 ? $product_info_row['products_quantity'] : 1) . '\')', IMAGE_BUTTON_BUY_NOW, 120);
                    $products_html .= '										<input type="hidden" name="buy_now_qty" value="1">
																			<input type="hidden" name="products_bundle" value="' . $product_info_row['products_bundle'] . '">
                                                                            </form>';
                }

                $products_html .= '										</td>
																	</tr>
																</table>
															</td>
														</tr>
														<tr>
															<td colspan="3">
																<div class="hide" id="collapse_' . $pid . '">
																	<table border="0" cellspacing="0" cellpadding="0" width="100%">
																		<tr>
																			<td>' . tep_draw_separator('pixel_trans.gif', '100%', '10') . '</td>
																		</tr>
																		<tr>
																			<td class="main">' . $pwl_products_description . '</td>
																		</tr>
																		<tr>
																			<td>' . tep_draw_separator('pixel_trans.gif', '100%', '10') . '</td>
																		</tr>
																	</table>
																</div>
															</td>
														</tr>
													</table>
												</td>
											</tr>
											<tr>
												<td>' . tep_draw_separator('pixel_trans.gif', '100%', '15') . '</td>
											</tr>';
                if ($product_count < $total_rows) {
                    $products_html .= '		<tr>
												<td colspan="3">
													<div class="row_separator"><!-- --></div>
												</td>
											</tr>';
                }
            } else if ($this->tpl == 2) {
                $btn_info_array = array();
                $dtu_calc_array = array();
                $customers_groups_name = '';
                $price_strike = '';
                $product_op = '';

                $promo_product_array = $this->get_promotion_products($pid);
                $is_promo_flag = tep_not_null($promo_product_array);
                $only_promo_box_flag = $is_promo_flag ? $promo_product_array['promotion_box_only'] : 0;
                $order_key = str_pad($product_info_row['products_sort_order'], 6, "0", STR_PAD_LEFT) . '_' . str_pad($pid, 8, "0", STR_PAD_LEFT);

                $product_delivery_mode_array = $this->get_product_delivery_mode($pid);
                $is_dtu_flag = in_array('6', $product_delivery_mode_array) ? 1 : 0; // Delivery mode is DTU ?
                $only_dtu_box_flag = $is_dtu_flag ? (count($product_delivery_mode_array) == 1 ? 1 : 0) : 0; // If only DTU
                $delivery_time_str = (tep_not_null(tep_get_eta_string($status_info['pre_order_time'])) ? tep_get_eta_string($status_info['pre_order_time']) : '-');

                $customers_groups_name = $cust_group_row_sql['customers_groups_name'];

                // Start Get Price Info
                if ($show_it == 2 && abs(PRE_ORDER_DISCOUNT)) {
                    $product_price = $currencies->display_price($pid, $product_info_row['products_price'], tep_get_tax_rate($product_info_row['products_tax_class_id']), 1, true);

                    if ($cust_group_row_sql['customers_groups_name']) {
                        if (abs($total_customer_rebate)) {
                            $product_op = $currencies->rebate_point;
                        }
                    }
                } else {
                    if ($cust_group_row_sql['customers_groups_name']) {
                        if (abs($total_customer_discount)) {
                            $product_price = $currencies->display_price($pid, $product_info_row['products_price'], tep_get_tax_rate($product_info_row['products_tax_class_id']), 1, false);

                            if (in_array($customers_groups_id, $this->display_original_price_group_array)) {
                                $price_strike = $currencies->display_price_original($pid, $normal_price, tep_get_tax_rate($product_info_row['products_tax_class_id']), 1);
                            }
                        } else {
                            $product_price = $currencies->display_price($pid, $normal_price, tep_get_tax_rate($product_info_row['products_tax_class_id']));
                        }

                        if (abs($total_customer_rebate)) {
                            $product_op = $currencies->rebate_point;
                        }
                    } else {
                        $product_price = $currencies->display_price($pid, $normal_price, tep_get_tax_rate($product_info_row['products_tax_class_id']));
                    }
                }
                // End Get Price Info

                if ($show_it == 0) {
                    $extra_notice = $delivery_time_str;
                    $btn_info_array = array('color' => 'gray', 'label' => IMAGE_BUTTON_OUT_OF_STOCK); //tep_image_button2('gray', 'javascript:void(0);', IMAGE_BUTTON_OUT_OF_STOCK, 130, ' id="btn_direct_topup_insert_cart_'.$index.'" style="width:123px;" ');
                } else if ($show_it == 1 || $status_info['is_future_product'] != '1') {
                    $extra_notice = '';
                    $btn_info_array = array('color' => 'green', 'label' => IMAGE_BUTTON_BUY_CODE);   //IMAGE_BUTTON_IN_CART
                } else {
                    $extra_notice = $delivery_time_str;
                    $btn_info_array = array('color' => 'green', 'label' => IMAGE_BUTTON_PRE_ORDER); //IMAGE_BUTTON_PRE_ORDER
                }
                // End Get Button Label

                if ($is_dtu_flag) {
                    $dtu_calc_array = array(
                        1 => tep_get_products_info($pid, 'products_dtu_extra_info_1', $languages_id, $default_languages_id),
                        2 => tep_get_products_info($pid, 'products_dtu_extra_info_2', $languages_id, $default_languages_id),
                        3 => tep_get_products_info($pid, 'products_dtu_extra_info_3', $languages_id, $default_languages_id)
                    );
                }

                // Start Group All Promotion products
                if ($is_promo_flag) {
                    $group_array = array();
                    $product_status = $this->get_promotion_product_status($product_info_row, $status_info, $promo_product_array);

                    // Group All Direct Top Up products
                    if ($is_dtu_flag) {
                        $promotion_DTU_count_index = $this->get_count_index_by_type($count_index, 'pd');
                        $promotion_extra_notice = $this->draw_counter_box($promotion_DTU_count_index, $promo_product_array['ttl_seconds'], $load_counter_array);

                        $group_array[] = array(
                            'index' => $promotion_DTU_count_index,
                            'pid' => $pid,
                            'name' => '',
                            'price' => $product_price,
                            'normal_price' => $price_strike,
                            'op' => $product_op,
                            'gst_title' => $products_gst_msg,
                            'customers_groups_name' => $customers_groups_name,
                            'is_dtu_flag' => $is_dtu_flag,
                            'products_bundle' => $product_info_row['products_bundle'],
                            'product_notice' => $extra_notice,
                            'btn_info' => $dtu_btn_info_array,
                            'promotion_info' => array('notice' => $promotion_extra_notice, 'status' => $product_status),
                            'dtu_extra_info' => $dtu_calc_array
                        );
                    }

                    // Group All General products
                    if (!$only_dtu_box_flag) {
                        $promotion_general_count_index = $this->get_count_index_by_type($count_index, 'pg');
                        $promotion_extra_notice = $this->draw_counter_box($promotion_general_count_index, $promo_product_array['ttl_seconds'], $load_counter_array);

                        $group_array[] = array(
                            'index' => $promotion_general_count_index,
                            'pid' => $pid,
                            'name' => '',
                            'price' => $product_price,
                            'normal_price' => $price_strike,
                            'op' => $product_op,
                            'gst_title' => $products_gst_msg,
                            'customers_groups_name' => $customers_groups_name,
                            'is_dtu_flag' => 0,
                            'products_bundle' => $product_info_row['products_bundle'],
                            'product_notice' => $extra_notice,
                            'btn_info' => $btn_info_array,
                            'promotion_info' => array('notice' => $promotion_extra_notice, 'status' => $product_status),
                            'dtu_extra_info' => $dtu_calc_array
                        );
                    }

                    $promotion_content_array[$order_key] = array(
                        'image' => $this->get_image_html($pid, 'promotion_product'),
                        'name' => $product_name,
                        'more_info' => tep_get_products_info($pid, 'products_description', $languages_id, $default_languages_id),
                        'delivery_method' => $product_delivery_mode_array,
                        'group' => $group_array
                    );

                    unset($group_array);
                }
                // End Group All Promotion products

                if (!$only_promo_box_flag) {
                    if (isset($categories_list_array[$product_info_row['categories_id']])) { // If in 4th Layer
                        $grouping_data_array[$product_info_row['categories_id']]['sort_order'] = $product_info_row['sort_order'];
                        $grouping_data_array[$product_info_row['categories_id']]['group'][] = array(
                            'pid' => $pid,
                            'name' => $product_name,
                            'price' => $product_price,
                            'normal_price' => $price_strike,
                            'op' => $product_op,
                            'gst_title' => $products_gst_msg,
                            'customers_groups_name' => $customers_groups_name,
                            'is_dtu_flag' => $is_dtu_flag,
                            'products_bundle' => $product_info_row['products_bundle'],
                            'product_notice' => $extra_notice,
                            'btn_info' => $btn_info_array,
                            'promotion_info' => '',
                            'dtu_extra_info' => $dtu_calc_array,
                            'only_dtu_box_flag' => $only_dtu_box_flag
                        );
                    } else { // At 3rd Layer
                        $group_array = array();

                        // Group All Direct Top Up products
                        if ($is_dtu_flag) {
                            $DTU_count_index = $this->get_count_index_by_type($count_index, 'd');

                            $group_array[] = array(
                                'index' => $DTU_count_index,
                                'pid' => $pid,
                                'name' => '',
                                'price' => $product_price,
                                'normal_price' => $price_strike,
                                'op' => $product_op,
                                'gst_title' => $products_gst_msg,
                                'customers_groups_name' => $customers_groups_name,
                                'is_dtu_flag' => $is_dtu_flag,
                                'products_bundle' => $product_info_row['products_bundle'],
                                'product_notice' => $extra_notice,
                                'btn_info' => $dtu_btn_info_array,
                                'promotion_info' => '',
                                'dtu_extra_info' => $dtu_calc_array
                            );
                        }

                        // Group All General products
                        if (!$only_dtu_box_flag) {
//							$general_count_index = $this->get_count_index_by_type($count_index, 'g');
//							$general_content_array[$order_key] = $this->draw_cdkey_content($pid, $product_name, $general_count_index, 'product');

                            $group_array[] = array(
                                'index' => $this->get_count_index_by_type($count_index, 'g'),
                                'pid' => $pid,
                                'name' => '',
                                'price' => $product_price,
                                'normal_price' => $price_strike,
                                'op' => $product_op,
                                'gst_title' => $products_gst_msg,
                                'customers_groups_name' => $customers_groups_name,
                                'is_dtu_flag' => 0,
                                'products_bundle' => $product_info_row['products_bundle'],
                                'product_notice' => $extra_notice,
                                'btn_info' => $btn_info_array,
                                'promotion_info' => '',
                                'dtu_extra_info' => $dtu_calc_array
                            );
                        }

                        $general_content_array[$order_key] = array(
                            'image' => $this->get_image_html($pid, 'product'),
                            'name' => $product_name,
                            'more_info' => tep_get_products_info($pid, 'products_description', $languages_id, $default_languages_id),
                            'delivery_method' => $product_delivery_mode_array,
                            'group' => $group_array
                        );
                    }
                }

                unset($product_delivery_mode_array);

                // At last row - Print Out the content
                if ($product_count == $total_rows) {
                    foreach ($grouping_data_array AS $parent_cat_id => $category_array) {
                        $dtu_group_array = array();
                        $general_group_array = array();
                        $order_key = str_pad($category_array['sort_order'], 6, "0", STR_PAD_LEFT) . '_' . str_pad($parent_cat_id, 8, "0", STR_PAD_LEFT);
                        $product_delivery_mode_array = array();

                        foreach ($category_array['group'] AS $idx => $product_array) {
                            $product_delivery_mode_array = array_merge($this->get_product_delivery_mode($product_array['pid']), $product_delivery_mode_array);
                            $btn_info_array = $product_array['btn_info'];

                            if ($product_array['is_dtu_flag']) {
                                $product_array['btn_info'] = $dtu_btn_info_array;
                                $product_array['index'] = $this->get_count_index_by_type($count_index, 'd');
                                $dtu_group_array[] = $product_array;
                            }

                            if (!$product_array['only_dtu_box_flag']) {
                                $product_array['btn_info'] = $btn_info_array;
                                $product_array['index'] = $this->get_count_index_by_type($count_index, 'g');
                                $product_array['is_dtu_flag'] = 0;
                                $general_group_array[] = $product_array;
                            }

                            unset($btn_info_array);
                        }

                        $general_content_array[$order_key] = array(
                            'image' => $this->get_image_html($parent_cat_id, 'category'),
                            'name' => tep_get_categories_name($parent_cat_id),
                            'more_info' => tep_get_categories_description($parent_cat_id, $languages_id),
                            'delivery_method' => array_unique($product_delivery_mode_array, SORT_NUMERIC),
                            'group' => array_merge($dtu_group_array, $general_group_array)
                        );

                        unset($dtu_group_array, $general_group_array, $product_delivery_mode_array);
                    }

                    // Promotion Section
                    if (tep_not_null($promotion_content_array)) {
                        $promotion_content = $this->page_obj->generate_product_list_layout($promotion_content_array, $this->tpl);

                        $new_page_content_array[] = '<table border="0" width="100%" cellspacing="0" cellpadding="0" class="promotionContentBox" style="background-color:#FFFFFF;">
                                                        <tr><td class="pheader"><div class="ribbontag"><span>' . TEXT_PROMOTION_PRODUCTS . '</span></div></td></tr>
                                                        <tr><td>' . $promotion_content . '</td></tr>
													 </table>';
                        unset($promotion_content_array, $promotion_content);
                    }

                    if (tep_not_null($general_content_array)) {
                        ksort($general_content_array);
                        $general_content = $this->page_obj->generate_product_list_layout($general_content_array, $this->tpl);

                        ob_start();
                        global $cPath_array, $ctype;

                        $LATEST_NEWS_TYPE = '5';
                        $display_content = 'simple';
                        define('LATEST_NEWS_BOX', 'OGM2008');
                        include(DIR_WS_MODULES . FILENAME_LATEST_NEWS);
                        $latest_news_content = ob_get_contents();
                        ob_end_clean();

                        $latest_news_content = '<div style="padding:10px 30px">' . $latest_news_content . '</div>';

                        $new_page_content_array[] = $this->page_obj->get_html_simple_rc_box(TABLE_HEADING_PRODUCTS_LIST, $general_content . $latest_news_content, 12);
                        unset($page_content_array, $general_content);
                    }

                    $products_html .= implode('<div class="breakLine" style="height:20px;"></div>', $new_page_content_array);
                    unset($new_page_content_array, $category_game_selection_array);
                }

                unset($promo_product_array);
            } else if ($this->tpl == 3) {
                $currency_currency_code = tep_get_customer_store_credit_currency($customer_id, false); // SC Product currency always follow Customer SC Balance Currency

                $pro_img_w = $pro_img_h = '';

                $pro_img = $this->get_products_info($pid, 'products_image', $languages_id, $default_languages_id);
                $products_image_info_array = $this->aws_obj->get_image_info($pro_img);

                if (tep_not_null($products_image_info_array)) {
                    $pro_img_src = $products_image_info_array['src'];
                } else if (tep_not_null($pro_img) && file_exists(DIR_FS_IMAGES . 'products/' . $pro_img)) {
                    $pro_img_src = THEMA_IMAGES . 'products/' . $pro_img;
                } else {
                    $pro_img_src = THEMA_IMAGES . 'no_product_image.gif';
                }

                list($pro_img_w, $pro_img_h) = getimagesize($pro_img_src);

                if ($pro_img_w > PRODUCT_IMAGE_WIDTH) {
                    $pro_img_w = PRODUCT_IMAGE_WIDTH;
                }

                if ($pro_img_h > PRODUCT_IMAGE_HEIGHT) {
                    $pro_img_h = PRODUCT_IMAGE_HEIGHT;
                }

                $products_html .= '			<tr>
												<td colspan="2">' . tep_draw_separator('pixel_trans.gif', '100%', '15') . '</td>
											</tr>
											<tr> ' .
                        tep_draw_form('buy_now_' . $product_count, tep_href_link(basename($PHP_SELF), tep_get_all_get_params(array('action')) . 'action=buy_now&products_id=' . $pid . '&custom_product=' . $product_info_row['custom_products_type_id']), 'POST');

                $products_html .= '			<td width="26%" align="center" valign="middle"><a href="' . tep_href_link(FILENAME_CUSTOM_PRODUCT_INFO, 'products_id=' . $product_info_row['products_id']) . '" class="productNavigation">' . tep_image($pro_img_src, $this->get_products_info($pid, 'products_image_title', $languages_id, $default_languages_id), $pro_img_w, $pro_img_h) . '</a></td>';

                $products_html .= '				<td valign="middle">
													<table border="0" cellspacing="0" cellpadding="0" width="100%">
														<tr>
															<td valign="top">
																<table border="0" cellspacing="0" cellpadding="0" width="100%">
																	<tr>
																		<td style="font-size:12px;font-weight:bold;vertical-align:top;">' . $product_name . '</td>
																	</tr>
																	<tr>
																		<td class="main">' . TABLE_HEADING_DELIVERY_TIME . ': ' . (tep_not_null(tep_get_eta_string($status_info['pre_order_time'])) ? tep_get_eta_string($status_info['pre_order_time']) : '-') . '</td>
																	</tr>
																	<tr>
																		<td>' . tep_draw_separator('pixel_trans.gif', '100%', '4') . '</td>
																	</tr>
																</table>
															</td>
															<td width="8%">&nbsp;</td>
															<td valign="top" width="15%">
																<table border="0" cellspacing="0" cellpadding="0" width="100%">';

                if ($show_it == 2 && abs(PRE_ORDER_DISCOUNT)) {
                    $products_html .= '								<tr>
																		<td style="font-size:14px;font-weight:bold;">&nbsp;&nbsp;&nbsp;' . $currencies->display_price($pid, $product_info_row['products_price'], tep_get_tax_rate($product_info_row['products_tax_class_id']), 1, true) . $products_gst_msg . '</td>
																	</tr>';
                } else {
                    if ($product_info_row['custom_products_type_id'] == '3') {
                        $products_html .= '							<tr>
																		<td style="font-size:14px;font-weight:bold;" nowrap>' . $currencies->currencies[$currency_currency_code]['symbol_left'] . tep_draw_input_field('customqty', $customqty, 'id="customqty" size="6"') . $currencies->currencies[$currency_currency_code]['symbol_right'] . '</td>
																	</tr>';
                    } else {
                        if ($cust_group_row_sql['customers_groups_name'] && abs($total_customer_discount)) {
                            $products_html .= '						<tr>
																		<td style="font-size:14px;font-weight:bold;">&nbsp;&nbsp;&nbsp;' . $currencies->display_price($pid, $product_info_row['products_price'], tep_get_tax_rate($product_info_row['products_tax_class_id']), 1, false) . '</td>
																	</tr>
																	<tr>
																		<td class="main" nowrap>&nbsp;&nbsp;&nbsp;
																			(<strike>' . $currencies->display_price_original($pid, $normal_price, tep_get_tax_rate($product_info_row['products_tax_class_id']), 1) . '</strike> ' . TEXT_GENERAL . ')' . $products_gst_msg . '
																		</td>
																	</tr>';
                        } else {
                            $products_html .= '						<tr>
																		<td style="font-size:14px;font-weight:bold;" nowrap>&nbsp;&nbsp;&nbsp;' . $currencies->format($normal_price, false, $currency_currency_code) . $products_gst_msg . '</td>
																	</tr>';
                        }
                    }
                }

                $products_html .= '									<tr>
																		<td>' . tep_draw_separator('pixel_trans.gif', '100%', '4') . '</td>
																	</tr>
																	<tr>
																		<td nowrap>';

                if ($show_it == 0) {
                    $products_html .= tep_image_button2('gray', 'javascript:;', '<font color=#BBBBBB>' . IMAGE_BUTTON_OUT_OF_STOCK . '</font>', '108px');
                } else {
                    if ($product_info_row['custom_products_type_id'] == '3') {
                        $products_html .= /* tep_draw_form('buy_now_' . $product_count, tep_href_link(basename($PHP_SELF), tep_get_all_get_params(array('action')) . 'action=buy_now&products_id=' . $pid . '&custom_product=' . $product_info_row['custom_products_type_id']), 'POST', '') . */
                                tep_draw_hidden_field('products_bundle', $product_info_row['products_bundle']) .
                                tep_image_button2('green', 'javascript:add_to_shopping_cart(\'' . tep_href_link(basename($PHP_SELF), tep_get_all_get_params(array('action')) . 'action=buy_now&products_id=' . $pid . '&custom_product=' . $product_info_row['custom_products_type_id']) . '\' , \'-1\', \'' . $product_info_row['products_bundle'] . '\' ,\'\')', IMAGE_BUTTON_IN_CART);
                        //'</form>';
                    } else {
                        $products_html .= /* tep_draw_form('buy_now_' . $product_count, tep_href_link(basename($PHP_SELF), tep_get_all_get_params(array('action')) . 'action=buy_now&products_id=' . $pid . '&custom_product=' . $product_info_row['custom_products_type_id']), 'POST', '') . */
                                tep_draw_hidden_field('buyqty', 1) .
                                tep_draw_hidden_field('products_bundle', $product_info_row['products_bundle']) .
                                tep_image_button2('green', 'javascript:add_to_shopping_cart(\'' . tep_href_link(basename($PHP_SELF), tep_get_all_get_params(array('action')) . 'action=buy_now&products_id=' . $pid . '&custom_product=' . $product_info_row['custom_products_type_id']) . '\' , \'1\', \'' . $product_info_row['products_bundle'] . '\' ,\'\')', IMAGE_BUTTON_IN_CART);
                        //'</form>';
                    }
                }

                $products_html .= '										</td>
																	</tr>
																</table>
															</td>
														</tr>
													</table>
												</td>
												</form>
											</tr>
											<tr>
												<td colspan="2">' . tep_draw_separator('pixel_trans.gif', '100%', '15') . '</td>
											</tr>';

                if ($product_count < $total_rows) {
                    $products_html .= '		<tr><td colspan="2"><div class="row_separator"><!-- --></div></td></tr>';
                }
            } else if ($this->tpl == 4) {
                $hla_region_arr = array();
                $products_templates_id = 0;

                $get_template_id_sql = "SELECT products_templates_id FROM " . TABLE_PRODUCTS_TEMPLATES . " WHERE products_id  = '" . tep_db_input($pid) . "'";
                $get_template_id_result = tep_db_query($get_template_id_sql);
                if ($get_template_id_row = tep_db_fetch_array($get_template_id_result)) {
                    $products_templates_id = $get_template_id_row['products_templates_id'];
                }

                switch ($products_templates_id) {
                    case '1': // WoW
                        $hla_country_arr = $this->get_hla_attribute($pid, 'country');
                        $hla_region_arr[] = array('id' => 'Any', 'text' => TEXT_HLA_ANY);

                        if (count($hla_country_arr) > 0) {
                            foreach ($hla_country_arr as $hla_country) {
                                $hla_region_arr[] = array('id' => $hla_country, 'text' => $hla_country);
                            }
                        }

                        $search_criteria_arr[] = array('region_' . TEXT_COUNTRY => $hla_region_arr,
                            'class_' . HLA_CLASS => array(
                                array('id' => 'Any', 'text' => TEXT_HLA_ANY),
                                array('id' => 'Death Knight', 'text' => HLA_CLASS_DEATH_KNIGHT),
                                array('id' => 'Druid', 'text' => HLA_CLASS_DRUID),
                                array('id' => 'Hunter', 'text' => HLA_CLASS_HUNTER),
                                array('id' => 'Mage', 'text' => HLA_CLASS_MAGE),
                                array('id' => 'Monk', 'text' => HLA_CLASS_MONK),
                                array('id' => 'Paladin', 'text' => HLA_CLASS_PALADIN),
                                array('id' => 'Priest', 'text' => HLA_CLASS_PRIEST),
                                array('id' => 'Rogue', 'text' => HLA_CLASS_ROGUE),
                                array('id' => 'Shaman', 'text' => HLA_CLASS_SHAMAN),
                                array('id' => 'Warlock', 'text' => HLA_CLASS_WARLOCK),
                                array('id' => 'Warrior', 'text' => HLA_CLASS_WARRIOR)),
                            'race_' . HLA_RACE => array(
                                array('id' => 'Any', 'text' => TEXT_HLA_ANY),
                                array('id' => 'Blood Elf', 'text' => HLA_RACE_BLOOD_ELF),
                                array('id' => 'Draenei', 'text' => HLA_RACE_DRAENEI),
                                array('id' => 'Dwarf', 'text' => HLA_RACE_DWARF),
                                array('id' => 'Gnome', 'text' => HLA_RACE_GNOME),
                                array('id' => 'Goblin', 'text' => HLA_RACE_GOBLIN),
                                array('id' => 'Human', 'text' => HLA_RACE_HUMAN),
                                array('id' => 'Night Elf', 'text' => HLA_RACE_NIGHT_ELF),
                                array('id' => 'Orc', 'text' => HLA_RACE_ORC),
                                array('id' => 'Pandaren', 'text' => HLA_RACE_PANDAREN),
                                array('id' => 'Tauren', 'text' => HLA_RACE_TAUREN),
                                array('id' => 'Troll', 'text' => HLA_RACE_TROLL),
                                array('id' => 'Undead', 'text' => HLA_RACE_UNDEAD),
                                array('id' => 'Worgen', 'text' => HLA_RACE_WORGEN)),
                            'talent_' . HLA_TALENT => array(
                                array('id' => 'Any', 'text' => TEXT_HLA_ANY),
                                array('id' => 'Affliction', 'text' => HLA_TALENT_AFFLICTION),
                                array('id' => 'Arcane', 'text' => HLA_TALENT_ARCANE),
                                array('id' => 'Arms', 'text' => HLA_TALENT_ARMS),
                                array('id' => 'Assassination', 'text' => HLA_TALENT_ASSASSINATION),
                                array('id' => 'Balance', 'text' => HLA_TALENT_BALANCE),
                                array('id' => 'Beast Mastery', 'text' => HLA_TALENT_BEAST_MASTERY),
                                array('id' => 'Blood', 'text' => HLA_TALENT_BLOOD),
                                array('id' => 'Combat', 'text' => HLA_TALENT_COMBAT),
                                array('id' => 'Demonology', 'text' => HLA_TALENT_DEMONOLOGY),
                                array('id' => 'Destruction', 'text' => HLA_TALENT_DESTRUCTION),
                                array('id' => 'Discipline', 'text' => HLA_TALENT_DISCIPLINE),
                                array('id' => 'Elemental', 'text' => HLA_TALENT_ELEMENTAL),
                                array('id' => 'Enhancement', 'text' => HLA_TALENT_ENHANCEMENT),
                                array('id' => 'Feral Combat', 'text' => HLA_TALENT_FERAL_COMBAT),
                                array('id' => 'Fire', 'text' => HLA_TALENT_FIRE),
                                array('id' => 'Frost', 'text' => HLA_TALENT_FROST),
                                array('id' => 'Fury', 'text' => HLA_TALENT_FURY),
                                array('id' => 'Holy', 'text' => HLA_TALENT_HOLY),
                                array('id' => 'Marksmanship', 'text' => HLA_TALENT_MARKSMANSHIP),
                                array('id' => 'Protection', 'text' => HLA_TALENT_PROTECTION),
                                array('id' => 'Restoration', 'text' => HLA_TALENT_RESTORATION),
                                array('id' => 'Retribution', 'text' => HLA_TALENT_RETRIBUTION),
                                array('id' => 'Shadow', 'text' => HLA_TALENT_SHADOW),
                                array('id' => 'Subtlety', 'text' => HLA_TALENT_SUBTLETY),
                                array('id' => 'Survival', 'text' => HLA_TALENT_SURVIVAL),
                                array('id' => 'Unholy', 'text' => HLA_TALENT_UNHOLY)),
                            'side_' . HLA_SIDE => array(
                                array('id' => 'Any', 'text' => TEXT_HLA_ANY),
                                array('id' => 'Alliance', 'text' => HLA_SIDE_ALLIANCE),
                                array('id' => 'Horde', 'text' => HLA_SIDE_HORDE)),
                            'level_' . HLA_LEVEL => array(
                                array('id' => 'Any', 'text' => TEXT_HLA_ANY),
                                array('id' => '50_69', 'text' => '50-69'),
                                array('id' => '70_70', 'text' => '70'),
                                array('id' => '71_79', 'text' => '71-79'),
                                array('id' => '80_80', 'text' => '80'),
                                array('id' => '81_85', 'text' => '81-85'),
                                array('id' => '86_90', 'text' => '86-90'),
                                array('id' => '91_100', 'text' => '91-100')
                            ),
                            'gender_' . HLA_GENDER => array(
                                array('id' => 'Any', 'text' => TEXT_HLA_ANY),
                                array('id' => 'Male', 'text' => HLA_GENDER_MALE),
                                array('id' => 'Female', 'text' => HLA_GENDER_FEMALE)),
                            'price_' . HLA_PRICE => array_merge(array(array('id' => 'Any', 'text' => TEXT_HLA_ANY)), $this->get_product_price_selection($products_templates_id)),
                            'refid_' . HLA_REFERENCE_ID => ''
                        );
                        break;
                    case '2': // Warhammer
                        $search_criteria_arr[] = array('race_' . HLA_RACE => array(
                                array('id' => 'Any', 'text' => TEXT_HLA_ANY),
                                array('id' => 'Chaos', 'text' => HLA_RACE_CHAOS),
                                array('id' => 'Dark Elf', 'text' => HLA_RACE_DARK_ELF),
                                array('id' => 'Dwarf', 'text' => HLA_RACE_WARHAMMER_DWARF),
                                array('id' => 'Empire', 'text' => HLA_RACE_EMPIRE),
                                array('id' => 'Greenskin', 'text' => HLA_RACE_GREENSKIN),
                                array('id' => 'High Elf', 'text' => HLA_RACE_HIGH_ELF)),
                            'class_' . HLA_CLASS => array(
                                array('id' => 'Any', 'text' => TEXT_HLA_ANY),
                                array('id' => 'Archmage', 'text' => HLA_CLASS_ARCHMAGE),
                                array('id' => 'Black Orc', 'text' => HLA_CLASS_BLACK_ORC),
                                array('id' => 'Blazing Sun Knight', 'text' => HLA_CLASS_BLAZING_SUN_KNIGHT),
                                array('id' => 'Bright Wizard', 'text' => HLA_CLASS_BRIGHT_WIZARD),
                                array('id' => 'Choppa', 'text' => HLA_CLASS_CHOPPA),
                                array('id' => 'Chosen', 'text' => HLA_CLASS_CHOSEN),
                                array('id' => 'Dark Elf Black Guard', 'text' => HLA_CLASS_DARK_ELF_BLACK_GUARD),
                                array('id' => 'Disciple of Khaine', 'text' => HLA_CLASS_DISCIPLE_OF_KHAINE),
                                array('id' => 'Engineer', 'text' => HLA_CLASS_ENGINEER),
                                array('id' => 'Ironbreaker', 'text' => HLA_CLASS_IRONBREAKER),
                                array('id' => 'Magus', 'text' => HLA_CLASS_MAGUS),
                                array('id' => 'Marauder', 'text' => HLA_CLASS_MARAUDER),
                                array('id' => 'Rune Priest', 'text' => HLA_CLASS_RUNE_PRIEST),
                                array('id' => 'Shadow Warrior', 'text' => HLA_CLASS_SHADOW_WARRIOR),
                                array('id' => 'Shaman', 'text' => HLA_CLASS_WARHAMMER_SHAMAN),
                                array('id' => 'Slayer', 'text' => HLA_CLASS_SLAYER),
                                array('id' => 'Sorceress', 'text' => HLA_CLASS_SORCERESS),
                                array('id' => 'Squig Herder', 'text' => HLA_CLASS_SQUIG_HERDER),
                                array('id' => 'Swordmaster', 'text' => HLA_CLASS_SWORDMASTER),
                                array('id' => 'Warrior Priest', 'text' => HLA_CLASS_WARRIOR_PRIEST),
                                array('id' => 'White Lion', 'text' => HLA_CLASS_WHITE_LION),
                                array('id' => 'Witch Elf', 'text' => HLA_CLASS_WITCH_ELF),
                                array('id' => 'Witch Hunter', 'text' => HLA_CLASS_WITCH_HUNTER),
                                array('id' => 'Zealot', 'text' => HLA_CLASS_ZEALOT)),
                            'level_' . HLA_LEVEL => array(
                                array('id' => 'Any', 'text' => TEXT_HLA_ANY),
                                array('id' => '20_30', 'text' => '20-30'),
                                array('id' => '31_39', 'text' => '31-39'),
                                array('id' => '40_40', 'text' => '40')),
                            'price_' . HLA_PRICE => array_merge(array(array('id' => 'Any', 'text' => TEXT_HLA_ANY)), $this->get_product_price_selection($products_templates_id)),
                            'refid_' . HLA_REFERENCE_ID => '',
                            'server_' . HLA_SERVER => array(
                                array('id' => 'Any', 'text' => TEXT_HLA_ANY),
                                array('id' => 'Badlands', 'text' => HLA_SERVER_BADLANDS),
                                array('id' => 'Dark Crag', 'text' => HLA_SERVER_DARK_CRAG),
                                array('id' => 'Darklands', 'text' => HLA_SERVER_DARKLANDS),
                                array('id' => 'Gorfang', 'text' => HLA_SERVER_GORFANG),
                                array('id' => 'Heldenhammer', 'text' => HLA_SERVER_HELDENHAMMER),
                                array('id' => 'Iron Rock', 'text' => HLA_SERVER_IRON_ROCK),
                                array('id' => 'Ironclaw', 'text' => HLA_SERVER_IRONCLAW),
                                array('id' => 'Ironfist', 'text' => HLA_SERVER_IRONFIST),
                                array('id' => 'Magnus', 'text' => HLA_SERVER_MAGNUS),
                                array('id' => 'Monolith', 'text' => HLA_SERVER_MONOLITH),
                                array('id' => 'Ostermark', 'text' => HLA_SERVER_OSTERMARK),
                                array('id' => 'Phoenix Throne', 'text' => HLA_SERVER_PHOENIX_THRONE),
                                array('id' => 'Praag', 'text' => HLA_SERVER_PRAAG),
                                array('id' => 'Skull Throne', 'text' => HLA_SERVER_SKULL_THRONE),
                                array('id' => 'Thorgrim', 'text' => HLA_SERVER_THORGRIM),
                                array('id' => 'Volkmar', 'text' => HLA_SERVER_VOLKMAR),
                                array('id' => 'Vortex', 'text' => HLA_SERVER_VORTEX),
                                array('id' => 'Wasteland', 'text' => HLA_SERVER_WASTELAND))
                        );
                        break;
                    case '3': // Age Of Conan
                        $search_criteria_arr[] = array('race_' . HLA_RACE => array(
                                array('id' => 'Any', 'text' => TEXT_HLA_ANY),
                                array('id' => 'Aquilonian', 'text' => HLA_RACE_AQUILONIAN),
                                array('id' => 'Cimmerian', 'text' => HLA_RACE_CIMMERIAN),
                                array('id' => 'Stygian', 'text' => HLA_RACE_STYGIAN)),
                            'class_' . HLA_CLASS => array(
                                array('id' => 'Any', 'text' => TEXT_HLA_ANY),
                                array('id' => 'Assassin', 'text' => HLA_CLASS_ASSASSIN),
                                array('id' => 'Barbarian', 'text' => HLA_CLASS_BARBARIAN),
                                array('id' => 'Bear Shaman', 'text' => HLA_CLASS_BEAR_SHAMAN),
                                array('id' => 'Conqueror', 'text' => HLA_CLASS_CONQUEROR),
                                array('id' => 'Dark Templar', 'text' => HLA_CLASS_DARK_TEMPLAR),
                                array('id' => 'Demonologist', 'text' => HLA_CLASS_DEMONOLOGIST),
                                array('id' => 'Guardian', 'text' => HLA_CLASS_GUARDIAN),
                                array('id' => 'Herald of Xotli', 'text' => HLA_CLASS_HERALD_OF_XOTLI),
                                array('id' => 'Necromancer', 'text' => HLA_CLASS_NECROMANCER),
                                array('id' => 'Priest of Mitra', 'text' => HLA_CLASS_PRIEST_OF_MITRA),
                                array('id' => 'Ranger', 'text' => HLA_CLASS_RANGER),
                                array('id' => 'Tempest of Set', 'text' => HLA_CLASS_TEMPEST_OF_SET)),
                            'level_' . HLA_LEVEL => array(
                                array('id' => 'Any', 'text' => TEXT_HLA_ANY),
                                array('id' => '50_69', 'text' => '50-69'),
                                array('id' => '70_79', 'text' => '70-79'),
                                array('id' => '80_80', 'text' => '80')),
                            'gender_' . HLA_GENDER => array(
                                array('id' => 'Any', 'text' => TEXT_HLA_ANY),
                                array('id' => 'Male', 'text' => HLA_GENDER_MALE),
                                array('id' => 'Female', 'text' => HLA_GENDER_FEMALE)),
                            'price_' . HLA_PRICE => array_merge(array(array('id' => 'Any', 'text' => TEXT_HLA_ANY)), $this->get_product_price_selection($products_templates_id)),
                            'server_' . HLA_SERVER => array(
                                array('id' => 'Any', 'text' => TEXT_HLA_ANY),
                                array('id' => 'Bloodspire', 'text' => HLA_SERVER_BLOODSPIRE),
                                array('id' => 'Cimmeria', 'text' => HLA_SERVER_CIMMERIA),
                                array('id' => 'gwahlur', 'text' => HLA_SERVER_GWAHLUR),
                                array('id' => 'Set', 'text' => HLA_SERVER_SET),
                                array('id' => 'Tyranny', 'text' => HLA_SERVER_TYRANNY),
                                array('id' => 'Wiccana', 'text' => HLA_SERVER_WICCANA)),
                            'refid_' . HLA_REFERENCE_ID => ''
                        );
                        break;
                    case '4': // AION US
                        $search_criteria_arr[] = array('race_' . HLA_RACE => array(
                                array('id' => 'Any', 'text' => TEXT_HLA_ANY),
                                array('id' => 'Asmodian', 'text' => HLA_RACE_ASMODIAN),
                                array('id' => 'Elyos', 'text' => HLA_RACE_ELYOS)),
                            'class_' . HLA_CLASS => array(
                                array('id' => 'Any', 'text' => TEXT_HLA_ANY),
                                array('id' => 'Assassin', 'text' => HLA_CLASS_AION_ASSASSIN),
                                array('id' => 'Chanter', 'text' => HLA_CLASS_CHANTER),
                                array('id' => 'Cleric', 'text' => HLA_CLASS_CLERIC),
                                array('id' => 'Gladiator', 'text' => HLA_CLASS_GLADIATOR),
                                array('id' => 'Ranger', 'text' => HLA_CLASS_AION_RANGER),
                                array('id' => 'Sorcerer', 'text' => HLA_CLASS_SORCERER),
                                array('id' => 'Spiritmaster', 'text' => HLA_CLASS_SPIRITMASTER),
                                array('id' => 'Templar', 'text' => HLA_CLASS_TEMPLAR)),
                            'level_' . HLA_LEVEL => array(
                                array('id' => 'Any', 'text' => TEXT_HLA_ANY),
                                array('id' => '30_39', 'text' => '30-39'),
                                array('id' => '40_49', 'text' => '40-49'),
                                array('id' => '50_55', 'text' => '50-55')),
                            'gender_' . HLA_GENDER => array(
                                array('id' => 'Any', 'text' => TEXT_HLA_ANY),
                                array('id' => 'Male', 'text' => HLA_GENDER_MALE),
                                array('id' => 'Female', 'text' => HLA_GENDER_FEMALE)),
                            'price_' . HLA_PRICE => array_merge(array(array('id' => 'Any', 'text' => TEXT_HLA_ANY)), $this->get_product_price_selection($products_templates_id)),
                            'server_' . HLA_SERVER => array(
                                array('id' => 'Any', 'text' => TEXT_HLA_ANY),
                                array('id' => 'Israphel', 'text' => HLA_SERVER_ISRAPHEL),
                                array('id' => 'Nezekan', 'text' => HLA_SERVER_NEZEKAN),
                                array('id' => 'Siel', 'text' => HLA_SERVER_SIEL),
                                array('id' => 'Vaizel', 'text' => HLA_SERVER_VAIZEL),
                                array('id' => 'Zikel', 'text' => HLA_SERVER_ZIKEL)),
                            'refid_' . HLA_REFERENCE_ID => ''
                        );
                        break;
                    case '5': // AION EU
                        $search_criteria_arr[] = array('race_' . HLA_RACE => array(
                                array('id' => 'Any', 'text' => TEXT_HLA_ANY),
                                array('id' => 'Asmodian', 'text' => HLA_RACE_ASMODIAN),
                                array('id' => 'Elyos', 'text' => HLA_RACE_ELYOS)),
                            'class_' . HLA_CLASS => array(
                                array('id' => 'Any', 'text' => TEXT_HLA_ANY),
                                array('id' => 'Assassin', 'text' => HLA_CLASS_ASSASSIN),
                                array('id' => 'Chanter', 'text' => HLA_CLASS_CHANTER),
                                array('id' => 'Cleric', 'text' => HLA_CLASS_CLERIC),
                                array('id' => 'Gladiator', 'text' => HLA_CLASS_GLADIATOR),
                                array('id' => 'Ranger', 'text' => HLA_CLASS_RANGER),
                                array('id' => 'Sorcerer', 'text' => HLA_CLASS_SORCERER),
                                array('id' => 'Spiritmaster', 'text' => HLA_CLASS_SPIRITMASTER),
                                array('id' => 'Templar', 'text' => HLA_CLASS_TEMPLAR)),
                            'level_' . HLA_LEVEL => array(
                                array('id' => 'Any', 'text' => TEXT_HLA_ANY),
                                array('id' => '30_39', 'text' => '30-39'),
                                array('id' => '40_49', 'text' => '40-49'),
                                array('id' => '50_55', 'text' => '50-55')),
                            'gender_' . HLA_GENDER => array(
                                array('id' => 'Any', 'text' => TEXT_HLA_ANY),
                                array('id' => 'Male', 'text' => HLA_GENDER_MALE),
                                array('id' => 'Female', 'text' => HLA_GENDER_FEMALE)),
                            'price_' . HLA_PRICE => array_merge(array(array('id' => 'Any', 'text' => TEXT_HLA_ANY)), $this->get_product_price_selection($products_templates_id)),
                            'server_' . HLA_SERVER => array(
                                array('id' => 'Any', 'text' => TEXT_HLA_ANY),
                                array('id' => 'Balder', 'text' => HLA_SERVER_BALDER),
                                array('id' => 'Israphel', 'text' => HLA_SERVER_ISRAPHEL),
                                array('id' => 'Kromede', 'text' => HLA_SERVER_KROMEDE),
                                array('id' => 'Perento', 'text' => HLA_SERVER_PERENTO),
                                array('id' => 'Spatalos', 'text' => HLA_SERVER_SPATALOS),
                                array('id' => 'Suthran', 'text' => HLA_SERVER_SUTHRAN),
                                array('id' => 'Telemachus', 'text' => HLA_SERVER_TELEMACHUS),
                                array('id' => 'Thor', 'text' => HLA_SERVER_THOR),
                                array('id' => 'Urtem', 'text' => HLA_SERVER_URTEM)),
                            'refid_' . HLA_REFERENCE_ID => ''
                        );
                        break;
                    case '6': // RIFT
                        $search_criteria_arr[] = array('race_' . HLA_RACE => array(
                                array('id' => 'Any', 'text' => TEXT_HLA_ANY),
                                array('id' => 'Defiant', 'text' => HLA_RACE_DEFIANT),
                                array('id' => 'Guardian', 'text' => HLA_RACE_GUARDIAN)
                            ),
                            'class_' . HLA_CLASS => array(
                                array('id' => 'Any', 'text' => TEXT_HLA_ANY),
                                array('id' => 'Cleric', 'text' => HLA_CLASS_CLERIC),
                                array('id' => 'Mage', 'text' => HLA_CLASS_MAGE),
                                array('id' => 'Rogue', 'text' => HLA_CLASS_ROGUE),
                                array('id' => 'Warrior', 'text' => HLA_CLASS_WARRIOR)
                            ),
                            'level_' . HLA_LEVEL => array(
                                array('id' => 'Any', 'text' => TEXT_HLA_ANY),
                                array('id' => '40_49', 'text' => '40-49'),
                                array('id' => '50_50', 'text' => '50')),
                            'price_' . HLA_PRICE => array_merge(array(array('id' => 'Any', 'text' => TEXT_HLA_ANY)), $this->get_product_price_selection($products_templates_id)),
                            'refid_' . HLA_REFERENCE_ID => ''
                        );
                        break;
                    default:

                        break;
                }

                $products_html .= '			<tr>
												<td colspan="2">' . tep_draw_separator('pixel_trans.gif', '100%', '15') . '</td>
											</tr>
											';

                $hla_search_html .= '			<table>
			    								<tr>
				    							<td valign="middle" colspan="0">
				    								' . tep_draw_form('hla_advanced_search_form', tep_href_link(FILENAME_DEFAULT, tep_get_all_get_params(array('action', 'region', 'class', 'race', 'talent', 'side', 'level', 'gender', 'price')) . 'action=hla_advanced_search'), 'POST', '  ') . '
													<table class="" border="0" cellspacing="0" cellpadding="0" width="100%">
														<tr>
															<td valign="top">
																<div class="hlaAdvSearch">
																<ul>';

                foreach ($search_criteria_arr as $search_criteria) {
                    foreach ($search_criteria as $search_key => $search_info) {
                        list($search_value, $search_title) = explode('_', $search_key);
                        $hla_search_html .= '<li>
																	<table>
																		<tr>';
                        if ($search_value != 'region') {
                            $hla_search_html .= '			<td style="width:70px;">' . $search_title . '</td>';
                        } else {
                            $hla_search_html .= '			<td class="mediumFont redIndicator" style="width:70px;font-weight:bold;">*' . $search_title . '</td>';
                        }

                        if (is_array($search_info)) {
                            $hla_search_html .= '			<td nowrap>:' . tep_draw_pull_down_menu($search_value, $search_info) . '</td>';
                        } else {
                            $hla_search_html .= '			<td nowrap>:' . tep_draw_input_field($search_value, '', 'id="' . $search_value . '" maxlength="11" size="21"') . '</td>';
                        }
                        $hla_search_html .= '		</tr>
																	</table>
																</li>';
                    }
                }

                $hla_search_html .= '					</ul>
																</div>
															</td>
														</tr>
														<tr>
															<td>
																<div class="dottedLine">&nbsp;<!-- --></div>
															</td>
														</tr>
														<tr>
															<td>
															<table align="right">
																<tr>
																	<td>
																		' . tep_image_button2('green', 'javascript:void(0);', IMAGE_BUTTON_SEARCH, '', 'OnClick="document.hla_advanced_search_form.submit();"') . '
																	</td>
																</tr>
															</table>
															</td>
														</tr>
													</table>
													</form>
												</td>
											</tr>';
                $hla_search_html .= '			<tr>
												<td colspan="2">' . tep_draw_separator('pixel_trans.gif', '100%', '5') . '</td>
											</tr>
											</table>';
                echo $this->page_obj->get_html_simple_rc_box(TEXT_ADVANCED_SEARCH_OPTIONS, $hla_search_html, 11);
            }
        }

        unset($page_content_array, $general_content_array, $promotion_content_array, $grouping_data_array);

        if ($this->tpl != 2) {
            $products_html .= '				</table>
										</td>
										<td width="14px"></td>
									</tr>
								</table>';
        }

        $products_html .= '<div id="ogm_tooltips">
								<div class="ogm_tooltips_close">
									<div class="ogm_tooltips_icon"></div>
									<div class="ogm_tooltips_link"></div>
								</div>
								<div class="ogm_tooltips_title"></div>
								<div class="ogm_tooltips_content"></div>
								<div class="ogm_tooltips_triangle"></div>
							</div>';

        $products_html .= '<script type="text/javascript">
            jQuery(function() {
                jQuery(".wTip").tipTip({maxWidth: "270px", edgeOffset: 0, defaultPosition: "left", keepAlive: true});
                jQuery("body").click(function() {
                    jQuery("#tiptip_holder").hide();
                });
            });
        </script>';

        if ($this->tpl == 4) {
            $default_title_name = tep_get_categories_heading_title($current_category_id);
            $default_hla_language_id = '1';
            $hla_product_html = '';
            $hla_account_id_arr = array();
            $hla_ref_id_arr = array();

            $join_attr_table_sql = $attr_type_sql = $attr_languague_sql = $order_by_sql = $attr_search_sql = $hla_paging = $join_search_attr_table_sql = '';

            if (!tep_not_null($_REQUEST['refid'])) {
                if (tep_not_null($_REQUEST['page'])) {
                    $page = is_int($_REQUEST['page']) ? tep_db_prepare_input($_REQUEST['page']) : 1;
                } else {
                    $page = 1;
                }

                if (isset($_REQUEST['direction']) && tep_not_null($_REQUEST['direction'])) {
                    $direction = tep_db_prepare_input($_REQUEST['direction']);
                } else {
                    $direction = 'desc';
                    $level_arw_img = DIR_WS_ICONS . "icon_arw_down.gif";
                    $price_arw_img = DIR_WS_ICONS . "icon_arw_down.gif";
                }

                if (isset($_REQUEST['col']) && tep_not_null($_REQUEST['col'])) {
                    $col = tep_db_prepare_input($_REQUEST['col']);
                    switch ($col) {
                        case 'img_level':
                            $join_attr_table_sql = "LEFT JOIN " . TABLE_PRODUCTS_HLA_ATTRIBUTES . " AS attr ON (cha.products_hla_characters_id = attr.products_hla_characters_id)";
                            $attr_type_sql = "AND attr.products_hla_attributes_type = 'level'";
                            $attr_languague_sql = "AND attr.language_id = '" . tep_db_input($default_hla_language_id) . "'";
                            $order_by_sql = "attr.products_hla_value";

                            $level_arw_img = $direction == 'desc' ? DIR_WS_ICONS . "icon_arw_down.gif" : DIR_WS_ICONS . "icon_arw_up.gif";
                            $price_arw_img = DIR_WS_ICONS . "icon_arw_down.gif";
                            break;
                        case 'img_price':
                            $order_by_sql = "acc.products_price";

                            $price_arw_img = $direction == 'desc' ? DIR_WS_ICONS . "icon_arw_down.gif" : DIR_WS_ICONS . "icon_arw_up.gif";
                            $level_arw_img = DIR_WS_ICONS . "icon_arw_down.gif";
                            break;
                    }
                } else {
                    $order_by_sql = "acc.products_hla_id";
                }

                if ($_GET['action'] == 'hla_advanced_search') {
                    if ((tep_not_null($_REQUEST['region']) && $_REQUEST['region'] != 'Any') ||
                            (tep_not_null($_REQUEST['race']) && $_REQUEST['race'] != 'Any') ||
                            (tep_not_null($_REQUEST['class']) && $_REQUEST['class'] != 'Any') ||
                            (tep_not_null($_REQUEST['side']) && $_REQUEST['side'] != 'Any') ||
                            (tep_not_null($_REQUEST['level']) && $_REQUEST['level'] != 'Any') ||
                            (tep_not_null($_REQUEST['gender']) && $_REQUEST['gender'] != 'Any') ||
                            (tep_not_null($_REQUEST['talent']) && $_REQUEST['talent'] != 'Any') ||
                            (tep_not_null($_REQUEST['server']) && $_REQUEST['server'] != 'Any')) {

                        if (tep_not_null($_REQUEST['region']) && $_REQUEST['region'] != 'Any') {
                            $hla_paging = '&region=' . $_REQUEST['region'];
                            $join_search_attr_table_sql = " LEFT JOIN " . TABLE_PRODUCTS_HLA_ATTRIBUTES . " AS region_attr ON (cha.products_hla_characters_id = region_attr.products_hla_characters_id)";
                            $attr_search_sql = " AND (region_attr.products_hla_attributes_type = 'country' AND region_attr.products_hla_value = '" . tep_db_input($_REQUEST['region']) . "')";
                        }

                        if (tep_not_null($_REQUEST['race']) && $_REQUEST['race'] != 'Any') {
                            $hla_paging .= '&race=' . $_REQUEST['race'];
                            $join_search_attr_table_sql .= " LEFT JOIN " . TABLE_PRODUCTS_HLA_ATTRIBUTES . " AS race_attr ON (cha.products_hla_characters_id = race_attr.products_hla_characters_id)";
                            $attr_search_sql .= " AND (race_attr.products_hla_attributes_type = 'race' AND race_attr.products_hla_value = '" . tep_db_input($_REQUEST['race']) . "')";
                        }

                        if (tep_not_null($_REQUEST['class']) && $_REQUEST['class'] != 'Any') {
                            $hla_paging .= '&class=' . $_REQUEST['class'];
                            $join_search_attr_table_sql .= " LEFT JOIN " . TABLE_PRODUCTS_HLA_ATTRIBUTES . " AS class_attr ON (cha.products_hla_characters_id = class_attr.products_hla_characters_id)";
                            $attr_search_sql .= " AND (class_attr.products_hla_attributes_type = 'class' AND class_attr.products_hla_value = '" . tep_db_input($_REQUEST['class']) . "')";
                        }

                        if (tep_not_null($_REQUEST['side']) && $_REQUEST['side'] != 'Any') {
                            $hla_paging .= '&side=' . $_REQUEST['side'];
                            $join_search_attr_table_sql .= " LEFT JOIN " . TABLE_PRODUCTS_HLA_ATTRIBUTES . " AS side_attr ON (cha.products_hla_characters_id = side_attr.products_hla_characters_id)";
                            $attr_search_sql .= " AND (side_attr.products_hla_attributes_type = 'side' AND side_attr.products_hla_value = '" . tep_db_input($_REQUEST['side']) . "')";
                        }

                        if (tep_not_null($_REQUEST['gender']) && $_REQUEST['gender'] != 'Any') {
                            $hla_paging .= '&gender=' . $_REQUEST['gender'];
                            $join_search_attr_table_sql .= " LEFT JOIN " . TABLE_PRODUCTS_HLA_ATTRIBUTES . " AS gender_attr ON (cha.products_hla_characters_id = gender_attr.products_hla_characters_id)";
                            $attr_search_sql .= " AND (gender_attr.products_hla_attributes_type = 'gender' AND gender_attr.products_hla_value = '" . tep_db_input($_REQUEST['gender']) . "')";
                        }

                        if (tep_not_null($_REQUEST['talent']) && $_REQUEST['talent'] != 'Any') {
                            $hla_paging .= '&talent=' . $_REQUEST['talent'];
                            $join_search_attr_table_sql .= " LEFT JOIN " . TABLE_PRODUCTS_HLA_ATTRIBUTES . " AS talent_attr ON (cha.products_hla_characters_id = talent_attr.products_hla_characters_id)";
                            $attr_search_sql .= " AND (talent_attr.products_hla_attributes_type = 'talent' AND talent_attr.products_hla_value = '" . tep_db_input($_REQUEST['talent']) . "')";
                        }

                        if (tep_not_null($_REQUEST['level']) && $_REQUEST['level'] != 'Any') {
                            $hla_paging .= '&level=' . $_REQUEST['level'];
                            list($from_level, $to_level) = explode("_", $_REQUEST['level']);
                            $join_search_attr_table_sql .= " LEFT JOIN " . TABLE_PRODUCTS_HLA_ATTRIBUTES . " AS lvl_attr ON (cha.products_hla_characters_id = lvl_attr.products_hla_characters_id)";
                            $attr_search_sql .= " AND (lvl_attr.products_hla_attributes_type = 'level' AND lvl_attr.products_hla_value >= '" . tep_db_input($from_level) . "' AND lvl_attr.products_hla_value <= '" . tep_db_input($to_level) . "')";
                        }

                        if (tep_not_null($_REQUEST['server']) && $_REQUEST['server'] != 'Any') {
                            $hla_paging .= '&server=' . $_REQUEST['server'];
                            $join_search_attr_table_sql .= " LEFT JOIN " . TABLE_PRODUCTS_HLA_ATTRIBUTES . " AS server_attr ON (cha.products_hla_characters_id = server_attr.products_hla_characters_id)";
                            $attr_search_sql .= " AND (server_attr.products_hla_attributes_type = 'server' AND server_attr.products_hla_value = '" . tep_db_input($_REQUEST['server']) . "')";
                        }
                    }

                    if (tep_not_null($_REQUEST['price']) && $_REQUEST['price'] != 'Any') {
                        $hla_paging .= '&price=' . $_REQUEST['price'];
                        list($from_price, $to_price) = explode("_", $_REQUEST['price']);

                        $from_price = $this->price_conversion_non_decimal($from_price, $_SESSION['currency'], 'USD', '1', false, 'sell');

                        if (tep_not_null($to_price)) {
                            $to_price = $this->price_conversion_non_decimal($to_price, $_SESSION['currency'], 'USD', '1', false, 'sell');

                            $attr_search_sql .= " AND (acc.products_price >= '" . tep_db_input($from_price) . "' AND acc.products_price <= '" . tep_db_input($to_price) . "')";
                        } else {
                            $attr_search_sql .= " AND acc.products_price >= '" . tep_db_input($from_price) . "'";
                        }
                    }
                }

                if (tep_not_null($hla_paging)) {
                    $hla_paging = substr($hla_paging, 1);
                }

                $get_product_select_sql = "	SELECT acc.seller_id, acc.products_id, acc.products_hla_id, acc.products_type, acc.products_price, cha.products_ref_id, cha.products_hla_characters_id
											FROM " . TABLE_PRODUCTS_HLA . " AS acc
                                            INNER JOIN " . TABLE_PRODUCTS_SUPPLIER . " AS ps
                                                ON (acc.seller_id=ps.supplier_id AND ps.supplier_status='1')
											INNER JOIN " . TABLE_PRODUCTS_HLA_CHARACTERS . " AS cha
												ON (acc.products_hla_id = cha.products_hla_id)
											" . $join_attr_table_sql . "
											" . $join_search_attr_table_sql . "
											WHERE acc.products_id = '" . tep_db_input($pid) . "'
											" . $attr_languague_sql . "
											" . $attr_type_sql . "
											" . $attr_search_sql . "
											AND acc.available_quantity = '1'
											AND acc.products_status = '1'
											AND acc.products_display = '1'
											GROUP BY acc.products_hla_id ORDER BY " . $order_by_sql . " " . $direction;
            } else {
                $get_product_select_sql = "	SELECT acc.seller_id, acc.products_id, acc.products_hla_id, acc.products_type, acc.products_price, cha.products_ref_id, cha.products_hla_characters_id
											FROM " . TABLE_PRODUCTS_HLA . " AS acc
                                            INNER JOIN " . TABLE_PRODUCTS_SUPPLIER . " AS ps
                                                ON (acc.seller_id=ps.supplier_id AND ps.supplier_status='1')
											INNER JOIN " . TABLE_PRODUCTS_HLA_CHARACTERS . " AS cha
												ON (acc.products_hla_id = cha.products_hla_id)
											WHERE acc.products_id = '" . tep_db_input($pid) . "'
                                                AND cha.products_ref_id = '" . tep_db_input($_REQUEST['refid']) . "'
                                                AND acc.available_quantity = '1'
                                                AND acc.products_status = '1'
                                                AND acc.products_display = '1'
											GROUP BY acc.products_hla_id";
            }

            $hla_product_html = '<tr style="color:white;text-align:center;">
									<td style="width:1px;"><div class="boxHeaderLeft"><!-- --></div></td>
									<td class="boxHeaderCenterTable" style="width:100px;text-align:center;"><font style="cursor:pointer;" onclick="chgImage(\'img_level\')"><span style="white-space: nowrap">' . TABLE_HLA_HEADING_LEVEL . '&nbsp;</span></font>' . tep_image($level_arw_img, '', '', '', ' style="vertical-align:middle; cursor:pointer; cursor:hand;" id="img_level" onclick="chgImage(this.id)"') . '</td>
									<td style="width:1px;"><div class="boxHeaderDivider"><!-- --></div></td>
									<td class="boxHeaderCenterTable" style="width:140px;text-align:center;"><span style="white-space: nowrap">' . TABLE_HLA_HEADING_RACE . ' / ' . TABLE_HLA_HEADING_CLASS . '&nbsp;</span></td>
									<td style="width:1px;"><div class="boxHeaderDivider"><!-- --></div></td>
									<td class="boxHeaderCenterTable" style="width:80px;text-align:center;"><span style="white-space: nowrap">' . strtoupper(HLA_SIDE) . '&nbsp;</span></td>
									<td style="width:1px;"><div class="boxHeaderDivider"><!-- --></div></td>
									<td class="boxHeaderCenterTable" style="width:90px;white-space: nowrap;" nowrap><font style="cursor:pointer;" onclick="chgImage(\'img_price\')"><span style="white-space: nowrap">' . TABLE_HLA_HEADING_PRICE . '&nbsp;</span></font>' . tep_image($price_arw_img, '', '', '', ' style="vertical-align:middle; cursor:pointer; cursor:hand;" id="img_price" onclick="chgImage(this.id)"') . '</td>
									<td style="width:1px;"><div class="boxHeaderDivider"><!-- --></div></td>
									<td class="boxHeaderCenterTable" style="width:170px;text-align:center;white-space: nowrap;" nowrap><span style="white-space: nowrap">' . TABLE_HLA_HEADING_ACTION . '&nbsp;</span></td>
									<td style="width:1px;"><div class="boxHeaderRight"><!-- --></div></td>
								</tr>';

            $get_product_select_sql = strtolower(preg_replace('/\s\s+/', ' ', trim($get_product_select_sql)));

            $hla_product_split = new splitPageResults($get_product_select_sql, MAX_DISPLAY_ORDER_HISTORY, 'acc.products_hla_id');
            $get_product_result_sql = tep_db_query($hla_product_split->sql_query);

            //$get_product_result_sql = tep_db_query($get_product_select_sql);
            $display_faction_arr = array('1'); // 1 = WOW

            $hla_total_product = tep_db_num_rows($get_product_result_sql);

            while ($get_product_row = tep_db_fetch_array($get_product_result_sql)) {
                $currency_count_index = $this->get_count_index_by_type($count_index, 'h');
                $btn_onclick = ' onclick="pfv(this.id)"';   //showMe_hla(\''.$pid.'\', \''.$get_product_row['products_hla_id'].'\');
                $attr_info_arr = array();
                $race_id = '';

                $game_name = tep_hla_game_name($get_product_row['seller_id'], $get_product_row['products_id']);

                $currencies->product_instance_id = $get_product_row['products_hla_id'];
                $race_icon = $class_icon = $faction_icon = $products_hla_characters_name = $products_hla_characters_description = '';
                if ($get_product_row['products_type'] == '2') {
                    $hla_account_id_arr[] = $get_product_row['products_hla_id'];
                    $hla_ref_id_arr[] = $get_product_row['products_ref_id'];
                }

                $get_attr_select_sql = "SELECT products_hla_attributes_type, products_hla_value
										FROM " . TABLE_PRODUCTS_HLA_ATTRIBUTES . "
										WHERE products_hla_characters_id = '" . tep_db_input($get_product_row['products_hla_characters_id']) . "'
										AND language_id = '" . tep_db_input($default_hla_language_id) . "'";
                $get_attr_result_sql = tep_db_query($get_attr_select_sql);
                while ($get_attr_row = tep_db_fetch_array($get_attr_result_sql)) {
                    $attr_info_arr[$get_attr_row['products_hla_attributes_type']] = $get_attr_row['products_hla_value'];
                }

                $get_desc_select_sql = "SELECT products_hla_characters_name, products_hla_characters_description
										FROM " . TABLE_PRODUCTS_HLA_DESCRIPTION . "
										WHERE products_hla_characters_id = '" . tep_db_input($get_product_row['products_hla_characters_id']) . "'
										AND language_id = '" . tep_db_input($default_hla_language_id) . "'";
                $get_desc_result_sql = tep_db_query($get_desc_select_sql);
                if ($get_desc_row = tep_db_fetch_array($get_desc_result_sql)) {
                    $products_hla_characters_name = $get_desc_row['products_hla_characters_name'];
                    $products_hla_characters_description = $get_desc_row['products_hla_characters_description'];
                }

                if (tep_not_null($attr_info_arr['race'])) {
                    $race = str_replace(" ", "", $attr_info_arr['race']);
                    $race_icon_name = strtolower($race . '_' . $attr_info_arr['gender'] . '.gif');

                    if ($this->aws_obj->is_aws_s3_enabled()) {
                        if ($this->aws_obj->is_image_exists($race_icon_name, 'hla/' . $game_name . '/race/', 'BUCKET_STATIC')) {
                            $race_icon = tep_image($this->aws_obj->get_image_url_by_instance(), $attr_info_arr['race'], '18', '18');
                        } else {
                            $race_icon = $attr_info_arr['race'];
                        }
                    } else {
                        $icon_path = file_exists(DIR_FS_HLA . 'hla/' . $game_name . '/race/' . $race_icon_name) ? DIR_WS_HLA . 'hla/' . $game_name . '/race/' . $race_icon_name : DIR_WS_HLA . 'hla/' . $game_name . '/race/' . strtolower($race . '.gif');
                        $race_icon = tep_image($icon_path, $attr_info_arr['race'], '18', '18');
                    }

                    $race_id = $race . '_' . $get_product_row['products_hla_characters_id'];
                }

                if (tep_not_null($attr_info_arr['class'])) {
                    $class = str_replace(" ", "", $attr_info_arr['class']);
                    $class_icon_name = strtolower($class . '.gif');
                    $icon_path = '';

                    if ($this->aws_obj->is_aws_s3_enabled()) {
                        if ($this->aws_obj->is_image_exists($class_icon_name, 'hla/' . $game_name . '/class/', 'BUCKET_STATIC')) {
                            $class_icon = tep_image($this->aws_obj->get_image_url_by_instance(), $attr_info_arr['class'], '18', '18');
                        } else {
                            $class_icon = $attr_info_arr['class'];
                        }
                    } else {
                        $icon_path = file_exists(DIR_FS_HLA . 'hla/' . $game_name . '/class/' . $class_icon_name) ? DIR_WS_HLA . 'hla/' . $game_name . '/class/' . $class_icon_name : $attr_info_arr['class'];
                        $class_icon = tep_image($icon_path, $attr_info_arr['class'], '18', '18');
                    }

                    $class_id = $class . '_' . $get_product_row['products_hla_characters_id'];
                }

                if (tep_not_null($attr_info_arr['side'])) {
                    $faction = str_replace(" ", "", $attr_info_arr['side']);
                    $faction_icon_name = strtolower($faction . '.gif');

                    if ($this->aws_obj->is_aws_s3_enabled()) {
                        if ($this->aws_obj->is_image_exists($faction_icon_name, 'hla/' . $game_name . '/faction/', 'BUCKET_STATIC')) {
                            $faction_icon = tep_image($this->aws_obj->get_image_url_by_instance(), $attr_info_arr['side'], '18', '18');
                        } else {
                            $faction_icon = $attr_info_arr['side'];
                        }
                    } else {
                        $icon_path = file_exists(DIR_FS_HLA . 'hla/' . $game_name . '/faction/' . $faction_icon_name) ? DIR_WS_HLA . 'hla/' . $game_name . '/faction/' . $faction_icon_name : $attr_info_arr['side'];
                        $faction_icon = tep_image($icon_path, $attr_info_arr['side'], '18', '18');
                    }

                    $faction_id = $faction . '_' . $get_product_row['products_hla_characters_id'];
                }

                $hla_product_html .= '<tr><td colspan="11"><table border="0" cellpadding="0" cellspacing="0" width="100%" style="padding: 5px 0px;">';
                $hla_product_html .= '<tr><td><table border="0" cellpadding="0" cellspacing="0" width="100%" style="border:solid 1px #CCCCCC;">';
                //$hla_product_html .= '<tr><td><table border="0" cellpadding="0" cellspacing="0" width="100%" style="border:solid 1px #CCCCCC;">';

                $hla_product_html .= '<tr style="padding-top:10px;padding-bottom:0px;"
                                            id="data_' . $currency_count_index . '"
                                            data-pid="' . $pid . '"
                                            data-products-bundle=""
                                            data-dm=""
                                            data-cpt-id="' . $this->tpl . '"
                                            data-name="' . $default_title_name . '"
                                            data-hla="' . $get_product_row['products_hla_id'] . '-' . $page_info->main_game_id . '">
										<td style="width:7px;padding-top:50px;padding-bottom:0px;">&nbsp;</td>
										<td style="width:70px;text-align:center;"><span style="font-size:20px;font-weight:bold;">' . $attr_info_arr['level'] . '</span><br />#' . $get_product_row['products_ref_id'] . '</td>
										<td style="width:33px;">&nbsp;</td>
										<td style="width:110px;text-align:center;">
											<span id="' . $race_id . '" onMouseOver="blackbox_tooltip(\'' . $attr_info_arr['race'] . '\', \'' . $race_id . '\');" onMouseOut="hide_blackbox_tooltip();">' . $race_icon . '</span>&nbsp;&nbsp;&nbsp;<span id="' . $class_id . '" onMouseOver="blackbox_tooltip(\'' . $attr_info_arr['class'] . '\', \'' . $class_id . '\');" onMouseOut="hide_blackbox_tooltip();">' . $class_icon . '</span>
										</td>
										<td style="width:33px;">&nbsp;</td>
										<td style="width:50px;text-align:center;">' . (in_array($products_templates_id, $display_faction_arr) ? '<span id="' . $faction_id . '" onMouseOver="blackbox_tooltip(\'' . $attr_info_arr['side'] . '\', \'' . $faction_id . '\');" onMouseOut="hide_blackbox_tooltip();">' . $faction_icon . '</span>' : '') . '</td>
										<td style="width:33px;">&nbsp;</td>
										<td style="width:80px;font-size:14px;font-weight:bold;">' . $currencies->display_price($pid, $get_product_row['products_price'], tep_get_tax_rate($product_info_row['products_tax_class_id']), 1, true) . $products_gst_msg . '</td>
										<td style="width:33px;">&nbsp;</td>
										<td style="width:110px;text-align:center;">
											<form method="POST" action="' . tep_href_link(basename($PHP_SELF), tep_get_all_get_params(array("action")) . "&1") . '&action=buy_now&products_id=' . $pid . '&custom_product=' . $this->tpl . '" name="buy_now_1">
												' . tep_image_button2('green', 'javascript:void(0)', IMAGE_BUTTON_BUY_NOW, 120, ' id="' . $currency_count_index . '"' . $btn_onclick) . '
											</form>
										</td>
										<td style="width:7px;">&nbsp;</td>
										</tr>';

                // View Profile and More Info
                $hla_product_html .= '<tr>
										<td colspan="7">
											<span style="padding:0 18px 10px 18px;float:left;">
												<a href="javascript:void(0);" onClick="window.open(\'' . tep_href_link(FILENAME_VIEW_PROFILE, 'char_id=' . $get_product_row['products_hla_characters_id']) . '\', \'' . ereg_replace_dep("[^+A-Za-z0-9]", "_", $products_hla_characters_name) . '\', \'width=\'+screen.width+\', height=\'+screen.height+\', top=0, left=0, fullscreen=yes, scrollbars=yes\'); return false;">' . TEXT_HLA_VIEW_PROFILE . '</a>
											</span>';
                $hla_product_html .= '		<span style="padding:0 18px;display:block;float:left;" id="more_info_link_' . $get_product_row['products_hla_characters_id'] . '">
												<a href="javascript:void(0);" onClick="hla_more_info(\'' . $get_product_row['products_hla_characters_id'] . '\');">&#8250;&nbsp;' . LINK_MORE_INFO . '</a>
										  	</span>
										  	<span style="padding:0 18px;display:none;float:left;" id="hide_info_link_' . $get_product_row['products_hla_characters_id'] . '">
												<a href="javascript:void(0);" onClick="hla_more_info(\'' . $get_product_row['products_hla_characters_id'] . '\');">&#8250;&nbsp;' . LINK_HIDE_INFO . '</a>
										  	</span>';
                $hla_product_html .= '  </td>
										<td colspan="4">' . (tep_not_null($add_to_cart_msg) ? TEXT_SELLING_DELIVERY_TIME . ': ' . $add_to_cart_msg : '&nbsp;') . '</td>
										</tr>';

                // Show More info : Character's Description
                $more_info_server = isset($attr_info_arr['server']) && tep_not_null($attr_info_arr['server']) ? '<div style="font-weight:bold;" class="largeFont">' . HLA_SERVER . ': ' . stripslashes($attr_info_arr['server']) . '</div>' : '';

                $hla_product_html .= '<tr>
										<td colspan="11" width="580">
											<div id="more_info_' . $get_product_row['products_hla_characters_id'] . '" style="padding:20px 32px;display:none;overflow:hidden;">' . $more_info_server . ENTRY_COUNTRY . ' ' . (tep_not_null($attr_info_arr['country']) ? $attr_info_arr['country'] : 'N/A') . '<br />' . nl2br($products_hla_characters_description) . '</div>
										</td>
									</tr>';


                // Show Alternate Characters

                $check_alternate_char_sql = "SELECT products_hla_characters_id FROM " . TABLE_PRODUCTS_HLA_CHARACTERS . " WHERE products_hla_id = '" . tep_db_input($get_product_row['products_hla_id']) . "'";
                $check_alternate_char_num = tep_db_num_rows(tep_db_query($check_alternate_char_sql));
                if ($check_alternate_char_num > 1) {
                    $hla_product_html .= '<tr>
											<td colspan="11" id="alternate_' . $get_product_row['products_hla_id'] . '" style="text-align:center;">
											' . tep_image(DIR_WS_IMAGES . 'lightbox-ico-loading.gif', '', '32', '32') . '
											</td>
										</tr>';
                }

                $hla_product_html .= '</table></td></tr>';
                $hla_product_html .= '</table></td></tr>';
            }

            if ($hla_total_product > 0) {
                $hla_product_html .= '<tr>
										<td colspan="11">
										<div id="pager" class="boxHeader" style="width:99%; text-align:right;">
											<span style="display:inline-block;">' . $hla_product_split->display_links(MAX_DISPLAY_PAGE_LINKS, tep_get_all_get_params(array('page', 'info', 'x', 'y')) . $hla_paging) . '</span>
										</div>
										</td>
									</tr>';
            } else {
                $hla_product_html .= '<tr>
										<td colspan="11">
										<div id="pager" class="boxHeader" style="width:99%; text-align:center;color:red;padding:10px;">
											' . TEXT_HLA_SEARCH_NO_RECORDS . '
										</div>
										</td>
									</tr>';
            }

            $products_html .= '<table border="0" cellspacing="0" cellpadding="0" width="100%">
									<tr>
										<td valign="top">' . tep_draw_separator('pixel_trans.gif', '100%', '15') . '</td>
									</tr>
								</table>';

            $new_hla_product_html .= '<table border="0" cellspacing="0" cellpadding="10" width="100%">
									<tr>
										<td valign="top">
											<table border="0" cellspacing="0" cellpadding="0" width="100%">
												' . $hla_product_html . '
											</table>
										</td>
									</tr>
								</table>';

            $products_html .= $this->page_obj->get_html_simple_rc_box('', $new_hla_product_html, 13);

            if (count($hla_account_id_arr) > 0) {
                $hla_account_id_js = implode(",", $hla_account_id_arr);
                $hla_ref_id_js = implode('","', $hla_ref_id_arr);

                $products_html .= '<script>
										jQuery(document).ready(function() {
											load_alternate_character(\'' . $hla_account_id_js . '\', \'' . $hla_ref_id_js . '\');
										});
									</script>';
            }
        }
        $products_html .= '	<script>
								window.dhx_globalImgPath = "' . DIR_WS_JAVASCRIPT . 'dhtmlxCombo/imgs/";

								jQuery(document).ready(function() {';
        if (count($load_counter_array)) {
            $products_html .= 'setInterval("countdown(array(\'' . implode("','", $load_counter_array) . '\'))", 1000);';
        }

//		if (count($load_products_info_array)) {
//			foreach ($load_products_info_array as $load_products_info_index_loop => $load_products_info_id_loop) {
//				$products_html .= 'load_products_info(\''.$load_products_info_id_loop.'\', \''.$load_products_info_index_loop.'\');';
//			}
//		}

        $products_html .= '});

						        function add_empty_product (pass_index) {
						        	jQuery("#tr_product_error_msg_"+pass_index).show();

						        	jQuery("#btn_green_empty_"+pass_index).removeClass("main_btn green_btn").addClass("main_btn gray_btn");
									jQuery("#tr_product_error_msg_"+pass_index+" #div_error_msg").html("' . JS_ERROR_PRODUCT_EMPTY . '");
						        }

						        function load_character_list(obj, pid, mid, pass_index) {
									var tbl_obj = jQuery("#"+obj.id).parents("table.purchase_confirm_box");

						        	if (!jQuery(obj).hasClass("character_list_loaded")) {
									    var check_flag = true;
                                        
                                        tbl_obj.find("td.dm>div.error_msg").hide().html("");

                                        tbl_obj.find("form#dm_form .dtu_customer_input_game_info").each(function(){
									        if (jQuery(this).val() == "" || jQuery(this).val() == jQuery(this).attr("default_text")) {
									            check_flag = false;
									        }
									    });

									    if (check_flag) {
									    	var customer_input_account = "";
								        	var customer_input_server = "";

								        	check_flag = false;
								        	tbl_obj.find("form#dm_form .dtu_customer_input_game_info").each(function(){
								        		if (jQuery(this).attr("name")=="game_info[account]") {
								        			customer_input_account = jQuery(this).val();
								        			check_flag = true;
								        		} else if (jQuery(this).attr("name")=="game_info[server]") {
								        			customer_input_server = jQuery(this).val();
								        			check_flag = true;
								        		}
								        	});

								        	if (check_flag) {
								        		jQuery(obj).hide();
								        		jquery_notice_box("' . TEXT_IS_LOADING . '");
								        		var display_html = "<option value=\'\'>"+jQuery(obj).attr("default_text")+"</option>";

												jQuery.ajax({
													url: "checkout_xmlhttp.php?action=load_dtu_character_list&pid="+pid+"&account=" + customer_input_account + "&server=" + customer_input_server,
													type: "GET",
													dataType: "xml",
													timeout: 60000,
													error: function(){
														jQuery.unblockUI();
														jQuery(obj).show();
														jQuery(obj).html(display_html);
													},
													success: function(xml) {
														jQuery(obj).show();
														jQuery.unblockUI();

														if (jQuery(xml).find("error").text()=="1") {
                                                            tbl_obj.find("td.dm>div.error_msg").show().html(jQuery(xml).find("error_message").text());
														} else {
															var display_html = "<option value=\'\'>"+jQuery(obj).attr("default_text")+"</option>";
															if (jQuery(xml).find("character").length > 0) {
																jQuery(xml).find("character").each(function(){
																	display_html += "<option value=\'"+jQuery(this).attr("id")+"\'>"+jQuery(this).text()+"</option>";
																});
															} else {
                                                                tbl_obj.find("td.dm>div.error_msg").show().html(jQuery(xml).find("error_message").text());
															}
														}
														jQuery(obj).html(display_html);
													}
												});
									    	}

									        jQuery(obj).addClass("character_list_loaded");
//									        tbl_obj.find("form#dm_form .dtu_customer_input_game_info").change(function(){
//									        	jQuery(obj).removeClass("character_list_loaded");
//									        	tbl_obj.find("form#dm_form .dtu_customer_input_game_info").unbind("change");
//									        	jQuery(obj).val("");
//									        });
										}
									}
						        }

						        function load_products_info(pid, pass_index) {
									jQuery("#div_products_delivery_time_"+pass_index).hide();
									jQuery("#div_products_delivery_mode_"+pass_index).hide();
									jQuery("#tr_product_error_msg_"+pass_index).hide();

									jQuery("#div_short_products_description_"+pass_index).show();
									jQuery("#div_short_products_description_"+pass_index).html(\'<div style="text-align:center;" width="100%">' . tep_image(DIR_WS_IMAGES . "loading.gif", "", 20, 20) . '</div>\');
									jQuery("#td_cart_info_"+pass_index).html(\'<div style="text-align:center;width:270px;">' . tep_image(DIR_WS_IMAGES . 'loading.gif', '', '20', '20') . '</div>\');
									jQuery("#div_products_description_"+pass_index).hide();

									jQuery.ajax({
										url: "checkout_xmlhttp.php?action=products_info&pID="+pid+"&index="+pass_index,
										type: "GET",
										dataType: "xml",
										timeout: 60000,
										error: function(){
											jQuery.unblockUI();
										},
										success: function(xml) {
											jQuery("#div_products_description_" + pass_index).html("");
											jQuery("#div_products_delivery_time_"+pass_index).html(jQuery(xml).find("delivery_time").text());
											jQuery("#div_products_delivery_time_"+pass_index).show();

											if (jQuery(xml).find("description").text()=="1") {
												jQuery("#div_short_products_description_"+pass_index).html("<a href=\"javascript:toggle_product_info(\'"+pass_index+"\',\'"+pid+"\');\" class=\"productNavigation\">' . LINK_MORE_PRODUCT_INFO . '</a>");
											} else {
												jQuery("#div_short_products_description_"+pass_index).html(\'\');
											}

											jQuery("#td_cart_info_"+pass_index).hide();
											jQuery("#td_cart_info_"+pass_index).html(jQuery(xml).find("html").text());';

        if (isset($_REQUEST['pid']) && (int) $_REQUEST['pid'] > 0) {
            $products_html .= '				if (pid == ' . (int) $_REQUEST['pid'] . ') {
												toggle_product_info(\'+pass_index+\',\'"+pid+"\');
											}';
        }

        $products_html .= '					if (jQuery(xml).find("delivery_methods").text() != "") {
												jQuery("#div_products_delivery_mode_"+pass_index).html(jQuery(xml).find("delivery_methods").text());
												jQuery("#div_products_delivery_mode_"+pass_index).show();
											}

											if (jQuery(".ogm_tooltips").length>0) {
												jQuery(".ogm_tooltips").ogm_tooltips( {
													method : \'mouseover\',
													image_up : \'' . DIR_WS_IMAGES . 'triangle_face_up.gif\',
													image_close : \'' . DIR_WS_ICONS . 'cancel-round.png\'
												});
											}

											if (jQuery(".ogm_dtu_tooltips").length>0) {
												jQuery(".ogm_dtu_tooltips").ogm_tooltips( {
													method : \'click\',
													image_up : \'' . DIR_WS_IMAGES . 'triangle_face_up.gif\',
													image_close : \'' . DIR_WS_ICONS . 'cancel-round.png\',
													width : \'160\'
												});
											}

											if ( pid==0 || pid == "" ) {
												//jQuery(\'#buyqtytext_\'+pass_index).block({message: null, overlayCSS: {backgroundColor: \'#A3A3A3\', opacity: \'0.6\', cursor: \'default\'}});
												jQuery(\'#buyqtytext_\'+pass_index).attr(\'disabled\', true);
											}

											jQuery("#td_cart_info_"+pass_index).show();
										}
									});
								}
								';

        $products_html .= '		function select_delivery_mode(pid, mid, pass_index) {
									pass_index = pass_index || 0;

									update_listing_qty(pid, pass_index);

									jQuery("#td_cart_info_"+pass_index+" td.delivery_mode_bg_"+pid+" .delivery_mode_bg_content").hide();
									jQuery("#td_cart_info_"+pass_index+" td#td_delivery_mode_"+pid+"_"+mid+" .delivery_mode_bg_content").show();

									jQuery("#td_cart_info_"+pass_index+" td.delivery_mode_bg_"+pid+"").removeClass("delivery_mode_bg_selected");
									jQuery("#td_cart_info_"+pass_index+" td.delivery_mode_bg_"+pid+" .delivery_mode_bg_content").hide();

									jQuery("#td_cart_info_"+pass_index+" td#td_delivery_mode_"+pid+"_"+mid).addClass("delivery_mode_bg_selected");
									jQuery("#td_cart_info_"+pass_index+" td#td_delivery_mode_"+pid+"_"+mid+" #top_up_input input, td#td_delivery_mode_"+pid+"_"+mid+" #top_up_input select").val("");
									jQuery("#td_cart_info_"+pass_index+" td#td_delivery_mode_"+pid+"_"+mid+" .delivery_mode_bg_content").show();

									jQuery("#td_cart_info_"+pass_index+" td#td_delivery_mode_"+pid+"_"+mid+" .delivery_mode_bg_content .productListingDTUPreInput").each(function(){
										jQuery(this).val(jQuery(this).attr(\'default_text\'));
									});
								}

								function toggle_product_info(pass_index, pid) {
									var html = "";

									if (jQuery("#div_products_description_" + pass_index).html() == "") {
										if (typeof pid != "undefined") {
											jQuery("#div_products_description_" + pass_index).html(\'<div style="text-align:center;" width="100%">' . tep_image(DIR_WS_IMAGES . "loading.gif", "", 20, 20) . '</div>\');

											jQuery.get("checkout_xmlhttp.php?action=products_description&pid=" + pid, function (xml) {
												if (jQuery(xml).find("description").text() != "") {
													html += jQuery(xml).find("description").text();
													html += "<br><br><br><a href=\"javascript:toggle_product_info(\'" + pass_index + "\',\'" + pid + "\');\" class=\"productNavigation\">' . LINK_HIDE_PRODUCT_INFO . '</a>";

													jQuery("#div_products_description_" + pass_index).html(html);
												}
											});
										}
									}

									jQuery("#div_short_products_description_" + pass_index).toggle();
									jQuery("#div_products_description_" + pass_index).toggle();
								}

								function submit_listing_qty(e, pid, pass_index) {
									if (window.event) {
										key = window.event.keyCode; // IE
									} else {
										key = e.which;
									}

									if (key == 13) {
										update_listing_qty(pid, pass_index);
									}
								}

								function update_listing_qty(pid, pass_index) {
									if (pid=="" || pid == 0)  return false;

									jQuery(".tr_error_msg_"+pass_index).hide();
									jQuery(".tr_error_msg_"+pass_index+" #div_error_msg").html("");

									pass_qty = jQuery("#buyqtytext_"+pass_index).val();

									if (pass_qty == 0 || pass_qty != parseInt(pass_qty)) {
										jQuery("#tr_qty_error_msg_"+pass_index).show();
										jQuery("#tr_qty_error_msg_"+pass_index+" #div_error_msg").html("' . ERROR_INVALID_QUANTITY . '");

										jQuery(".div_add_cart_button_"+pass_index).hide();
										jQuery("#btn_gray_"+pass_index).show();
										jQuery("#cart_price_"+pass_index).html(\'\');
										jQuery("#cart_normal_price_"+pass_index).html(\'\');
										jQuery("#cart_op_"+pass_index).html(\'\');

									} else {
										var selected_mode = "";
										if (jQuery("input[type=radio][name=rd_delivery_mode_"+pass_index+"][checked]").length > 0) {
											selected_mode = jQuery("input[type=radio][name=rd_delivery_mode_"+pass_index+"][checked]").val();
										}

										jQuery("#cart_price_"+pass_index).html(\'<div style="text-align:center;" width="100%">' . tep_image(DIR_WS_IMAGES . "loading.gif", "", 20, 20) . '</div>\');
										jQuery("#cart_op_"+pass_index).text("");
										jQuery("#cart_normal_price_"+pass_index).text("");
										jQuery("div.div_add_cart_button_"+pass_index).hide();
										jQuery("#cart_gst_"+pass_index).hide();

										jQuery.getJSON("express_checkout_xmlhttp.php?action=status&tpl=' . $this->tpl . '&cPath=' . $_REQUEST['cPath'] . '&pid="+pid+"&buyqty="+pass_qty , function(data) {
											if (jQuery("input[type=radio][name=rd_delivery_mode_"+pass_index+"][checked]").val() == selected_mode) {
												jQuery("#div_add_cart_button_"+pass_index+" font").html("' . IMAGE_BUTTON_IN_CART . '"); // reset to default
												jQuery("#btn_gray_"+pass_index+" font").html("' . IMAGE_BUTTON_IN_CART . '"); // reset to default
												if (data.show == "1" || selected_mode == 6 || (data.show == "2" && data.is_future_product == "0")) {
													jQuery("#btn_gray_"+pass_index).hide();
													jQuery("#div_add_cart_button_"+selected_mode+"_"+pass_index).show();
													jQuery("#div_add_cart_button_"+selected_mode+"_"+pass_index+" font").html("' . IMAGE_BUTTON_IN_CART . '");
												} else if (data.show == "2") {
													jQuery("#btn_gray_"+pass_index).hide();
													jQuery("#div_add_cart_button_"+selected_mode+"_"+pass_index+" font").html("' . IMAGE_BUTTON_PRE_ORDER . '");
													jQuery("#div_add_cart_button_"+selected_mode+"_"+pass_index).show();
												} else {
													jQuery("#tr_qty_error_msg_"+pass_index).show();
													jQuery("#tr_qty_error_msg_"+pass_index+" #div_error_msg").html(\'' . TEXT_STOCK_NOT_AVAILABLE . '\');
													jQuery("#btn_gray_"+pass_index+" font").html(\'' . IMAGE_BUTTON_OUT_OF_STOCK . '\');
													jQuery("#btn_gray_"+pass_index).show();
													jQuery("#cart_price_"+pass_index).html(\'\');
												}

												var pre_order_time = "-";
												if (selected_mode != 6 && data.pre_order_time!="") {
													pre_order_time = data.pre_order_time;
												}
												jQuery("#div_products_delivery_time_"+pass_index).html(pre_order_time);

												jQuery("#cart_price_"+pass_index).html(data.price);
												jQuery("#cart_op_"+pass_index).text(data.op);

												if (data.normal_price != "" && data.normal_price != data.price) {
													jQuery("#cart_normal_price_"+pass_index).html("(<strike>"+data.normal_price+"</strike> ' . TEXT_GENERAL . ')");
												}

												if (!empty(data.gst_message) && typeof data.gst_message != "undefined") {
													jQuery("#cart_gst_"+pass_index).text(data.gst_message);
													jQuery("#cart_gst_"+pass_index).show();
												}
											} else if (jQuery("input[type=radio][name=rd_delivery_mode_"+pass_index+"][checked]").length == 0) {
												jQuery("#cart_price_"+pass_index).html("");
												jQuery("#cart_op_"+pass_index).text("");
												jQuery("#btn_gray_"+pass_index).show();
												jQuery("#cart_price_"+pass_index).html(\'\');
											}
										});
									}
								}

								function submit_cart(pid, pass_index, mid, pass_qty, custom_type_id, pass_bundle) {
									custom_type_id = custom_type_id || \'\';
									pass_bundle = pass_bundle || \'\';
									pass_qty = pass_qty || \'\';

									if (pass_qty == \'\') {
										pass_qty = jQuery("#buyqtytext_"+pass_index).val();
									}

									if (mid==6) {
										submit_topup(pid, pass_index, pass_qty);
									} else {
										add_to_shopping_cart(\'' . tep_href_link(basename($PHP_SELF), tep_get_all_get_params(array('action')) . 'action=buy_now&products_id=\'+pid+\'&custom_product=\'+custom_type_id+\'&delivery_mode=\'+mid+\'') . '\' , pass_qty, pass_bundle ,\'\');
									}
								}
							</script>';

        # Publisher Game List for CDK Only
        if ($this->tpl == 2) {
            $products_html .= $this->publisher_game_list();
        }

        return $products_html;
    }

    function get_product_price_selection($templates_id) {
        switch ($templates_id) {
            case '1': // Wow
                return array(
                    array('id' => '0_' . $this->price_conversion_non_decimal('200', 'USD', $_SESSION['currency'], '10'), 'text' => $this->price_conversion_non_decimal('0', 'USD', $_SESSION['currency'], '10', true) . ' - ' . $this->price_conversion_non_decimal('200', 'USD', $_SESSION['currency'], '10', true)),
                    array('id' => $this->price_conversion_non_decimal('200', 'USD', $_SESSION['currency'], '10') . '_' . $this->price_conversion_non_decimal('300', 'USD', $_SESSION['currency'], '10'), 'text' => $this->price_conversion_non_decimal('200', 'USD', $_SESSION['currency'], '10', true) . ' - ' . $this->price_conversion_non_decimal('300', 'USD', $_SESSION['currency'], '10', true)),
                    array('id' => $this->price_conversion_non_decimal('300', 'USD', $_SESSION['currency'], '10') . '_' . $this->price_conversion_non_decimal('400', 'USD', $_SESSION['currency'], '10'), 'text' => $this->price_conversion_non_decimal('300', 'USD', $_SESSION['currency'], '10', true) . ' - ' . $this->price_conversion_non_decimal('400', 'USD', $_SESSION['currency'], '10', true)),
                    array('id' => $this->price_conversion_non_decimal('400', 'USD', $_SESSION['currency'], '10') . '_' . $this->price_conversion_non_decimal('600', 'USD', $_SESSION['currency'], '10'), 'text' => $this->price_conversion_non_decimal('400', 'USD', $_SESSION['currency'], '10', true) . ' - ' . $this->price_conversion_non_decimal('600', 'USD', $_SESSION['currency'], '10', true)),
                    array('id' => $this->price_conversion_non_decimal('600', 'USD', $_SESSION['currency'], '10') . '_' . $this->price_conversion_non_decimal('800', 'USD', $_SESSION['currency'], '10'), 'text' => $this->price_conversion_non_decimal('600', 'USD', $_SESSION['currency'], '10', true) . ' - ' . $this->price_conversion_non_decimal('800', 'USD', $_SESSION['currency'], '10', true)),
                    array('id' => $this->price_conversion_non_decimal('800', 'USD', $_SESSION['currency'], '10'), 'text' => $this->price_conversion_non_decimal('800', 'USD', $_SESSION['currency'], '10', true) . '+')
                );
                break;
            case '2': // Warhammer
                return array(
                    array('id' => '0_' . $this->price_conversion_non_decimal('200', 'USD', $_SESSION['currency'], '10'), 'text' => $this->price_conversion_non_decimal('0', 'USD', $_SESSION['currency'], '10', true) . ' - ' . $this->price_conversion_non_decimal('200', 'USD', $_SESSION['currency'], '10', true)),
                    array('id' => $this->price_conversion_non_decimal('200', 'USD', $_SESSION['currency'], '10') . '_' . $this->price_conversion_non_decimal('300', 'USD', $_SESSION['currency'], '10'), 'text' => $this->price_conversion_non_decimal('200', 'USD', $_SESSION['currency'], '10', true) . ' - ' . $this->price_conversion_non_decimal('300', 'USD', $_SESSION['currency'], '10', true)),
                    array('id' => $this->price_conversion_non_decimal('300', 'USD', $_SESSION['currency'], '10') . '_' . $this->price_conversion_non_decimal('400', 'USD', $_SESSION['currency'], '10'), 'text' => $this->price_conversion_non_decimal('300', 'USD', $_SESSION['currency'], '10', true) . ' - ' . $this->price_conversion_non_decimal('400', 'USD', $_SESSION['currency'], '10', true)),
                    array('id' => $this->price_conversion_non_decimal('400', 'USD', $_SESSION['currency'], '10') . '_' . $this->price_conversion_non_decimal('600', 'USD', $_SESSION['currency'], '10'), 'text' => $this->price_conversion_non_decimal('400', 'USD', $_SESSION['currency'], '10', true) . ' - ' . $this->price_conversion_non_decimal('600', 'USD', $_SESSION['currency'], '10', true)),
                    array('id' => $this->price_conversion_non_decimal('600', 'USD', $_SESSION['currency'], '10') . '_' . $this->price_conversion_non_decimal('800', 'USD', $_SESSION['currency'], '10'), 'text' => $this->price_conversion_non_decimal('600', 'USD', $_SESSION['currency'], '10', true) . ' - ' . $this->price_conversion_non_decimal('800', 'USD', $_SESSION['currency'], '10', true)),
                    array('id' => $this->price_conversion_non_decimal('800', 'USD', $_SESSION['currency'], '10'), 'text' => $this->price_conversion_non_decimal('800', 'USD', $_SESSION['currency'], '10', true) . '+')
                );
                break;
            case '3': // Age Of Conan
                return array(
                    array('id' => '0_' . $this->price_conversion_non_decimal('100', 'USD', $_SESSION['currency'], '10'), 'text' => $this->price_conversion_non_decimal('0', 'USD', $_SESSION['currency'], '10', true) . ' - ' . $this->price_conversion_non_decimal('100', 'USD', $_SESSION['currency'], '10', true)),
                    array('id' => $this->price_conversion_non_decimal('100', 'USD', $_SESSION['currency'], '10') . '_' . $this->price_conversion_non_decimal('150', 'USD', $_SESSION['currency'], '10'), 'text' => $this->price_conversion_non_decimal('100', 'USD', $_SESSION['currency'], '10', true) . ' - ' . $this->price_conversion_non_decimal('150', 'USD', $_SESSION['currency'], '10', true)),
                    array('id' => $this->price_conversion_non_decimal('150', 'USD', $_SESSION['currency'], '10') . '_' . $this->price_conversion_non_decimal('200', 'USD', $_SESSION['currency'], '10'), 'text' => $this->price_conversion_non_decimal('150', 'USD', $_SESSION['currency'], '10', true) . ' - ' . $this->price_conversion_non_decimal('200', 'USD', $_SESSION['currency'], '10', true)),
                    array('id' => $this->price_conversion_non_decimal('200', 'USD', $_SESSION['currency'], '10'), 'text' => $this->price_conversion_non_decimal('200', 'USD', $_SESSION['currency'], '10', true) . '+')
                );
                break;
            case '4': // AION US
            case '5': // AION EU
            case '6': // RIFT
                return array(
                    array('id' => '0_' . $this->price_conversion_non_decimal('150', 'USD', $_SESSION['currency'], '10'), 'text' => $this->price_conversion_non_decimal('0', 'USD', $_SESSION['currency'], '10', true) . ' - ' . $this->price_conversion_non_decimal('150', 'USD', $_SESSION['currency'], '10', true)),
                    array('id' => $this->price_conversion_non_decimal('150', 'USD', $_SESSION['currency'], '10') . '_' . $this->price_conversion_non_decimal('300', 'USD', $_SESSION['currency'], '10'), 'text' => $this->price_conversion_non_decimal('150', 'USD', $_SESSION['currency'], '10', true) . ' - ' . $this->price_conversion_non_decimal('300', 'USD', $_SESSION['currency'], '10', true)),
                    array('id' => $this->price_conversion_non_decimal('300', 'USD', $_SESSION['currency'], '10') . '_' . $this->price_conversion_non_decimal('500', 'USD', $_SESSION['currency'], '10'), 'text' => $this->price_conversion_non_decimal('300', 'USD', $_SESSION['currency'], '10', true) . ' - ' . $this->price_conversion_non_decimal('500', 'USD', $_SESSION['currency'], '10', true)),
                    array('id' => $this->price_conversion_non_decimal('500', 'USD', $_SESSION['currency'], '10'), 'text' => $this->price_conversion_non_decimal('500', 'USD', $_SESSION['currency'], '10', true) . '+')
                );
                break;
        }
    }

    function get_store_credits_list($category_array) {
        global $languages_id, $customer_id, $customers_groups_id, $currencies, $PHP_SELF, $currency, $default_languages_id;
        global $sc_promotion_percentage;

        $cat_drop_down_array = array();
        $categories_list_array = array();

        $op_popup = 'http://kb.offgamers.com/zhcn/category/my-account/wor-token/';

        if ($languages_id == 2 || $languages_id == 3) {
            $op_popup = 'http://kb.offgamers.com/en/category/my-account/wor-token/';
        }

        $product_count = 0;

        $customqty = 0;
        $customers_sc_select_sql = "SELECT cscq.customers_sc_cart_quantity, p.custom_products_type_id, p.products_price
									FROM " . TABLE_CUSTOMERS_SC_CART . " AS cscq
									INNER JOIN " . TABLE_PRODUCTS . " AS p
										ON p.products_id = cscq.products_id
									WHERE cscq.customers_id = '" . (int) $_SESSION['customer_id'] . "'";
        $customers_sc_result_sql = tep_db_query($customers_sc_select_sql);
        if ($customers_sc_row = tep_db_fetch_array($customers_sc_result_sql)) {
            $customqty = $customers_sc_row['customers_sc_cart_quantity'];
        }
        $currency_currency_code = tep_get_customer_store_credit_currency($customer_id, false); // SC Product currency always follow Customer SC Balance Currency
        $minimum_sc = store_credit::get_minimum_store_credit($currency_currency_code);

        $products_html = '	<table border="0" width="100%" cellspacing="0" cellpadding="0">
								<tr>
									<td width="14px"></td>
									<td>
										<table border="0" width="100%" cellspacing="0" cellpadding="0">';
        $products_html .= '				<tr>
											<td colspan="3">' . tep_draw_separator('pixel_trans.gif', '100%', '20') . '</td>
										</tr>';
        $products_html .= '
										<tr>
											<td colspan="3" align="right">
												<table border="0" cellspacing="0" cellpadding="0">
													<tr><td><h2>' . TABLE_HEADING_STORE_CREDIT_TOPUP . '</h2></td></tr>';
        $products_html .= '							<tr><td>' . TABLE_HEADING_STORE_CREDIT_TOPUP_NEWS_TEXT . '<br><br></td></tr>
                                                    <tr><td>' . TABLE_HEADING_STORE_CREDIT_TOPUP_TEXT . '</td></tr>
												</table>
											</td>
										</tr>';
        $products_html .= '				<tr>
											<td colspan="3">' . tep_draw_separator('pixel_trans.gif', '100%', '20') . '</td>
										</tr>
										<tr>
											<td colspan="3"><div class="row_separator"><!-- --></div></td>
										</tr>';

        $product_info_select_sql = "	SELECT DISTINCT(p.products_id), p.products_bundle, p.products_bundle_dynamic, p.custom_products_type_id,
											p.products_price, p.products_tax_class_id, p.products_quantity
										FROM " . TABLE_PRODUCTS . " AS p
										INNER JOIN " . TABLE_PRODUCTS_TO_CATEGORIES . " AS ptc
											ON (p.products_id = ptc.products_id)
										INNER JOIN " . TABLE_CATEGORIES_GROUPS . " AS cg
											ON (cg.categories_id = ptc.categories_id)
										INNER JOIN " . TABLE_CATEGORIES . " AS c
											ON (c.categories_id = ptc.categories_id AND c.categories_status = 1)
										WHERE ptc.categories_id IN ('" . implode("', '", $category_array) . "')
											AND p.custom_products_type_id
											AND p.products_status = 1
											AND p.products_display = 1
											AND ((cg.groups_id = '" . (int) $customers_groups_id . "') OR (cg.groups_id = 0))
										ORDER BY p.products_sort_order";
        $product_info_result_sql = tep_db_query($product_info_select_sql);
        while ($product_info_row = tep_db_fetch_array($product_info_result_sql)) {
            $product_count++;

            if ($product_info_row['products_bundle'] == 'yes') {
                $status_info = tep_product_add_to_cart_permission($product_info_row['products_id'], 'products_bundle');
            } else if ($product_info_row['products_bundle_dynamic'] == 'yes') {
                $status_info = tep_product_add_to_cart_permission($product_info_row['products_id'], 'products_bundle_dynamic');
            } else {
                $status_info = tep_product_add_to_cart_permission($product_info_row['products_id'], '');
            }

            $pid = $product_info_row['products_id'];
            $product_name = tep_get_products_name($pid, $languages_id);
            $show_it = $status_info['show'];
            $special_price = tep_get_products_special_price($pid);
            $normal_price = $product_info_row['products_price'];

            $customers_groups_info_array = tep_get_customer_group_discount($customer_id, $pid, 'product');
            $customers_groups_discount = $customers_groups_info_array['cust_group_discount'];

            $total_customer_rebate = $customers_groups_info_array['cust_group_rebate'];

            if ($this->tpl == 3) {
                $pro_img_w = $pro_img_h = '';

                $pro_img = $this->get_products_info($pid, 'products_image', $languages_id, $default_languages_id);
                $products_image_info_array = $this->aws_obj->get_image_info($pro_img);

                if (tep_not_null($products_image_info_array)) {
                    $pro_img_src = $products_image_info_array['src'];
                } else if (tep_not_null($pro_img) && file_exists(DIR_FS_IMAGES . 'products/' . $pro_img)) {
                    $pro_img_src = THEMA_IMAGES . 'products/' . $pro_img;
                } else {
                    $pro_img_src = THEMA_IMAGES . 'no_product_image.gif';
                }

                list($pro_img_w, $pro_img_h) = getimagesize($pro_img_src);

                if ($pro_img_w > PRODUCT_IMAGE_WIDTH) {
                    $pro_img_w = PRODUCT_IMAGE_WIDTH;
                }

                if ($pro_img_h > PRODUCT_IMAGE_HEIGHT) {
                    $pro_img_h = PRODUCT_IMAGE_HEIGHT;
                }

                $products_html .= '			<tr>
												<td colspan="2">' . tep_draw_separator('pixel_trans.gif', '100%', '15') . '</td>
											</tr>
											<tr>
												<td width="26%" align="center" valign="middle">' . tep_image($pro_img_src, $this->get_products_info($pid, 'products_image_title', $languages_id, $default_languages_id), $pro_img_w, $pro_img_h) . '</td>';

                if ($product_info_row['custom_products_type_id'] == '3') {
                    $products_html .= '			<td valign="middle">' .
                            tep_draw_form('buy_now_' . $product_count, tep_href_link(FILENAME_SC_CHECKOUT_PAYMENT, tep_get_all_get_params(array('action', 'cPath'))), 'POST', ' onSubmit="return validate_minimum_sc(\'' . $minimum_sc . '\',document.getElementById(\'customqty_' . $product_count . '\').value)" ') . '
														<input type="hidden" name="action" value="buy_now">
														<input type="hidden" name="products_id" value="' . $pid . '">
														<input type="hidden" name="custom_product" value="' . $product_info_row['custom_products_type_id'] . '">
														<table border="0" cellspacing="0" cellpadding="0" width="100%">
															<tr>
																<td valign="top">
																	<table border="0" cellspacing="0" cellpadding="0" width="100%">
																		<tr>
																			<td><h2>' . sprintf(ENTRY_FORM_BUY_TEXT, $currencies->currencies[$currency_currency_code]['symbol_left']) . tep_draw_input_field('customqty', $customqty, 'id="customqty_' . $product_count . '" size="6" onKeyPress="return numbersOnly(this, event)" onKeyUp="update_promo_amount(' . $product_count . ', this.value, \'' . (1 + ($sc_promotion_percentage / 100)) . '\', \'' . $currencies->currencies[$currency_currency_code]['symbol_left'] . '\',\'' . $currencies->currencies[$currency_currency_code]['symbol_right'] . '\')"') . sprintf(ENTRY_FORM_STORE_CREDITS_TEXT, $currencies->currencies[$currency_currency_code]['symbol_right']) . '</h2></td>
																		</tr>
																		<tr>
																			<td class="main">' . sprintf(TEXT_MINIMUM_STORE_CREDITS_TOPUP, $currencies->currencies[$currency_currency_code]['title'], $currencies->currencies[$currency_currency_code]['symbol_left'] . $minimum_sc . $currencies->currencies[$currency_currency_code]['symbol_right']) . '</td>
																		</tr>';
                    if ($sc_promotion_percentage > 0) {
                        $products_html .= '								<tr>
																			<td>' . tep_draw_separator('pixel_trans.gif', '100%', '20') . '</td>
																		</tr>
																		<tr>
																			<td class="main"><div id="update_promo_amount_' . $product_count . '">' . tep_image(DIR_WS_ICONS . 'icon_promo_small.gif', ICON_PROMO, '', '', 'style="vertical-align:middle;"') . "&nbsp;&nbsp;" . TEXT_INFO_ENTERED_SC_PROMO_AMOUNT1 . '<b>' . $currencies->currencies[$currency_currency_code]['symbol_left'] . number_format((int) $customqty, 2, '.', '') . $currencies->currencies[$currency_currency_code]['symbol_right'] . '</b>' . TEXT_INFO_ENTERED_SC_PROMO_AMOUNT2 . '<b>' . $currencies->currencies[$currency_currency_code]['symbol_left'] . number_format((int) $customqty * (1 + ($sc_promotion_percentage / 100)), 2, '.', '') . $currencies->currencies[$currency_currency_code]['symbol_right'] . '</b>' . TEXT_INFO_ENTERED_SC_PROMO_AMOUNT3 . '</div></td>
																		</tr>';
                    }

                    $products_html .= '									<tr>
																			<td>' . tep_draw_separator('pixel_trans.gif', '100%', '4') . '</td>
																		</tr>
																	</table>
																</td>
																<td width="8%">&nbsp;</td>
																<td valign="top" width="15%">
																	<table border="0" cellspacing="0" cellpadding="0" width="100%">
																		<tr>
																			<td nowrap>';
                    $products_html .= tep_draw_hidden_field('products_bundle', $product_info_row['products_bundle']) .
                            tep_image_button2('green', 'javascript:void(0);', IMAGE_BUTTON_CONTINUE, '', 'onclick="if (validate_minimum_sc(\'' . $minimum_sc . '\',document.getElementById(\'customqty_' . $product_count . '\').value)) {document.buy_now_' . $product_count . '.submit();}" ');
                    $products_html .= '</td>
																		</tr>
																	</table>
																</td>
															</tr>
														</table>
													</form>
												</td>';
                }

                $products_html .= '			</tr>
											<tr>
												<td colspan="2">' . tep_draw_separator('pixel_trans.gif', '100%', '15') . '</td>
											</tr>';

                if ($product_count < tep_db_num_rows($product_info_result_sql)) {
                    $products_html .= '		<tr><td colspan="2"><div class="row_separator"><!-- --></div></td></tr>';
                }
            }
        }

        $products_html .= '				</table>
									</td>
									<td width="14px"></td>
								</tr>
							</table>';

        return $products_html;
    }

    /* -- EXPRESS CHECKOUT -- */

    function get_game_product_type($cPath, $support_product_type_array) {
        global $languages_id, $memcache_obj;

        $product_available = array();

        $cur_cat_path_array = explode('_', $cPath);
        $current_game_category_id = $cur_cat_path_array[0];

        $this_cat_obj = new category($current_game_category_id);
        $cat_support_prod_type_array = $this_cat_obj->get_available_product_type();

        if (is_array($cat_support_prod_type_array) && is_array($support_product_type_array)) {
            foreach ($cat_support_prod_type_array as $products_type_id) {
                if (in_array($products_type_id, $support_product_type_array)) {
                    $categories_select_sql1 = "	SELECT c.categories_id, c.categories_parent_path, c.custom_products_type_id
												FROM " . TABLE_CATEGORIES . " AS c
												WHERE c.categories_status = 1
													AND c.categories_parent_path LIKE '\_" . str_replace('_', '\_', $cPath) . "\_%'
													AND c.custom_products_type_id = '" . tep_db_input($products_type_id) . "'";
                    $categories_result_sql1 = tep_db_query($categories_select_sql1);
                    while ($categories_result_row1 = tep_db_fetch_array($categories_result_sql1)) {
                        $categories_parent_path = substr($categories_result_row1['categories_parent_path'], 1, -1);
                        $categories_parent_path .= "_" . $categories_result_row1['categories_id'];

                        $product_available[] = array('name' => strip_tags(tep_get_categories_name($categories_result_row1['categories_id'], $languages_id)),
                            'cPath' => $categories_parent_path,
                            'tpl' => $categories_result_row1['custom_products_type_id']);
                    }
                }
            }
        }

        return $product_available;
    }

    function get_plain_products_list($cat_array) {
        global $languages_id, $customers_groups_id, $memcache_obj;

        $products_array = array();
        $product_flag_id = tep_not_empty($this->products_flag_id) ? '/products_flag_id/' . $this->products_flag_id : '';

        $cache_key = TABLE_PRODUCTS . '/products/array/cat_path/' . $this->categories_id . '/custom_products_type_id/' . (int) $this->tpl . '/language/' . $languages_id . $product_flag_id;
        $cache_result = $memcache_obj->fetch($cache_key);
        if ($cache_result !== FALSE) {
            $products_array = $cache_result;
        } else {
            $categories_list_array = array();

            if ($this->tpl == 2 && $this->custom_products_type_id_cat > 0) {
                $temp_holder = $this->plain;
                $this->plain = false;
                $categories_list_array = $this->get_categories_list($this->custom_products_type_id_cat);
                $this->plain = $temp_holder;
            }

            $product_info_select_sql = "	SELECT DISTINCT(p.products_id), p.products_sort_order, ptc.categories_id, c.sort_order
											FROM " . TABLE_PRODUCTS . " AS p
											INNER JOIN " . TABLE_PRODUCTS_TO_CATEGORIES . " AS ptc
												ON (p.products_id = ptc.products_id)
											INNER JOIN " . TABLE_CATEGORIES_GROUPS . " AS cg
												ON (cg.categories_id = ptc.categories_id)
											INNER JOIN " . TABLE_CATEGORIES . " AS c
												ON (c.categories_id = ptc.categories_id AND c.categories_status = 1)
											WHERE ptc.categories_id IN ('" . implode("', '", $cat_array) . "')
												AND ((p.custom_products_type_id = '" . (int) $this->tpl . "') OR (p.custom_products_type_id = 0))
												AND p.products_status = 1
												AND p.products_display = 1
												AND ((cg.groups_id = '" . (int) $customers_groups_id . "') OR (cg.groups_id = 0))
                                                " . $this->get_product_flag_id_condition('p.products_flag_id') . "
											ORDER BY p.products_sort_order";
            $product_info_result_sql = tep_db_query($product_info_select_sql);
            while ($product_info_row = tep_db_fetch_array($product_info_result_sql)) {
                if (isset($categories_list_array[$product_info_row['categories_id']])) {
                    $order_key = str_pad($product_info_row['sort_order'], 6, "0", STR_PAD_LEFT) . '_' . str_pad($product_info_row['categories_id'], 8, "0", STR_PAD_LEFT) . '_' . str_pad($product_info_row['products_id'], 8, "0", STR_PAD_LEFT);
                } else {
                    $order_key = str_pad($product_info_row['products_sort_order'], 6, "0", STR_PAD_LEFT) . '_' . str_pad($product_info_row['products_id'], 8, "0", STR_PAD_LEFT);
                }

                $products_array[$order_key] = array(
                    'id' => $product_info_row['products_id'],
                    'name' => strip_tags(tep_get_products_name($product_info_row['products_id'], $languages_id))
                );
            }

            ksort($products_array);
            $memcache_obj->store($cache_key, $products_array, 3600); // cache 1 hours
        }

        return $products_array;
    }

    function get_plain_cat_listing($categories_id) {
        global $languages_id, $memcache_obj;

        $category_array = array();

        $cache_key = TABLE_CATEGORIES . '/product_parent_category/array/categories_id/' . $categories_id . '/custom_products_type_id/' . $this->tpl . '/language/' . $languages_id;
        $cache_result = $memcache_obj->fetch($cache_key);
        if ($cache_result !== FALSE) {
            $category_array = $cache_result;
        } else {
            if ($this->tpl == 0) {
                $category_array = $this->get_categories_list($categories_id);
            } else if ($this->tpl == 1) {
                $this_cat_obj = new category($categories_id);
                $category_array = $this_cat_obj->get_categories_name_list();
            }
            $memcache_obj->store($cache_key, $category_array, 3600); // cache 1 hours
        }

        return $category_array;
    }

    function get_plain_products_status($pid, $buyqty = '', $delivery_methods = '') {
        global $language, $currencies;

        include_once(DIR_WS_LANGUAGES . $language . '/' . FILENAME_EXPRESS_CHECKOUT);
        include_once(DIR_WS_LANGUAGES . $language . '/' . FILENAME_DEFAULT);

        $product_price = '';
        $display_price = '';
        $rebate_point = '';
        $total_customer_discount = 0;

        $products_status = array();
        $products_result = array();
        $delivery_mode_array = array();
        $product_type = '';
        $message = '';

        $product_info_select_sql = "SELECT p.products_price, p.products_tax_class_id, p.products_bundle,
										p.products_bundle_dynamic
									FROM " . TABLE_PRODUCTS . " AS p
									INNER JOIN " . TABLE_PRODUCTS_TO_CATEGORIES . " AS ptc
										ON (p.products_id = ptc.products_id)
									INNER JOIN " . TABLE_CATEGORIES_GROUPS . " AS cg
										ON (cg.categories_id = ptc.categories_id)
									WHERE p.products_id = '" . tep_db_input($pid) . "'
										AND ((p.custom_products_type_id = '" . (int) $this->tpl . "') OR (p.custom_products_type_id = 0))
										AND p.products_status = 1
										AND p.products_display = 1
										AND ((cg.groups_id = '" . (int) $_SESSION['customers_groups_id'] . "') OR (cg.groups_id = 0))
									LIMIT 1";
        $product_info_result_sql = tep_db_query($product_info_select_sql);
        if ($product_info_row = tep_db_fetch_array($product_info_result_sql)) {
            $product_type = (tep_not_empty($product_info_row['products_bundle']) ? 'products_bundle' : (tep_not_empty($product_info_row['products_bundle_dynamic']) ? 'products_bundle_dynamic' : ''));
            $products_status = $this->product_add_to_cart_permission($pid, $product_type);

            // EXPRESS CHECKOUT :: price x buyqty
            if (tep_not_empty($buyqty)) {
                # update available qty and status
                if ($products_status['auto_purchase_mode'] == true) {
                    $hide_delivery_message = false;

                    $remain_stock = $products_status['products_quantity'] - $buyqty;

                    if (tep_not_empty($products_status['products_out_of_stock_level']) && ($products_status['products_out_of_stock_level'] > $remain_stock)) {
                        $show_it = 0;
                        $message = $products_status['products_out_of_stock_msg'];
                    } else if (($products_status['show'] == 2) || (tep_not_empty($products_status['products_pre_order_level']) && ($products_status['products_pre_order_level'] > $remain_stock))) {
                        if ($products_status['products_date_available'] > date('Y-m-d H:i:s')) {
                            $show_it = 2;
                            $is_future_product = 1;
                        } else {
                            $show_it = 1;
                        }

                        $message = $products_status['products_preorder_msg'];
                    } else {
                        $show_it = 1;
                        $hide_delivery_message = true;
                        $message = $products_status['products_add_to_cart_msg'];
                    }

                    // return values
                    $products_status['show'] = $show_it;
                    $products_status['is_future_product'] = $is_future_product ? 1 : 0;

                    if (!$hide_delivery_message) {
                        $products_status['pre_order_time'] = $message;
                    }
                }

                $special_price = tep_get_products_special_price($pid);
                $normal_price = $product_info_row['products_price'];
                $products_tax_class_id = $product_info_row['products_tax_class_id'];

                $cust_select_sql = "	SELECT customers_discount
			   							FROM " . TABLE_CUSTOMERS . "
			   							WHERE customers_id = '" . (int) $_SESSION['customer_id'] . "'";
                $cust_result_sql = tep_db_query($cust_select_sql);
                $cust_row_sql = tep_db_fetch_array($cust_result_sql);

                $customers_groups_name = tep_get_customers_groups_name($_SESSION['customers_groups_id']);

                $customers_groups_info_array = tep_get_customer_group_discount($_SESSION['customer_id'], $pid, 'product');
                $customers_groups_discount = $customers_groups_info_array['cust_group_discount'];

                $total_customer_rebate = $customers_groups_info_array['cust_group_rebate'];
                $total_customer_discount = $cust_row_sql['customers_discount'] + $customers_groups_discount;

                if ($show_it == 2 && abs(PRE_ORDER_DISCOUNT)) {
                    $display_price = $currencies->display_price($pid, $normal_price, tep_get_tax_rate($products_tax_class_id), $buyqty, true);
                } else {
                    if (abs($total_customer_discount)) {
                        $display_price = $currencies->display_price($pid, $normal_price, tep_get_tax_rate($products_tax_class_id), $buyqty, false);
                    } else {
                        $display_price = $currencies->display_price($pid, $normal_price, tep_get_tax_rate($products_tax_class_id), $buyqty);
                    }
                }

                if (abs($total_customer_discount) && in_array($_SESSION['customers_groups_id'], $this->display_original_price_group_array)) {
                    $product_price = $currencies->display_price_original($pid, $normal_price, tep_get_tax_rate($product_info_row['products_tax_class_id']), $buyqty);
                }

                $rebate_point = tep_display_op($currencies->rebate_point) . ' (' . $customers_groups_name . ')';
            }

            // if is bundle + cd key, not allow customer to add cart if all cant intersect any delivery mode
            if ($product_info_row["products_bundle"] == 'yes' || $product_info_row["products_bundle_dynamic"] == 'yes') {
                $delivery_mode_array = $product_delivery_mode_array = $this->get_product_delivery_mode($pid);
                if (count($product_delivery_mode_array) == 0) {
                    $products_status['show'] = 0;
                }
            }

            $products_result = array('show' => $products_status['show'],
                'is_future_product' => $products_status['is_future_product'],
                'pre_order_time' => $products_status['pre_order_time'],
                'bundle' => $products_status['bundle'],
                'quantity' => $products_status['quantity'],
                'normal_price' => $product_price,
                'price' => $display_price,
                'op' => $rebate_point,
                'delivery_mode' => $products_status['delivery_methods'],
                'delivery_mode_html' => $products_status['delivery_mode_html'],
                'gst_message' => tep_not_empty($_SESSION['RegionGST']['tax_title']) ? sprintf(WARNING_PRODUCT_PRICE_WITHOUT_GST, $_SESSION['RegionGST']['tax_title']) : '');
        }

        return $products_result;
    }

    function get_hla_attribute($product_id, $attr_type) {
        global $memcache_obj;

        $attr_result = array();

        $cache_key = TABLE_PRODUCTS_HLA_ATTRIBUTES . '/' . $attr_type . '/array/products_id/' . $product_id . '/language/1';

        $cache_result = $memcache_obj->fetch($cache_key);

        if ($cache_result !== FALSE) {
            $attr_result = $cache_result;
        } else {
            $get_hla_attr_select = "SELECT DISTINCT hla_attr.products_hla_value
									FROM " . TABLE_PRODUCTS_HLA . " AS hla
									INNER JOIN " . TABLE_PRODUCTS_HLA_CHARACTERS . " AS hla_char
										ON (hla.products_hla_id = hla_char.products_hla_id)
									INNER JOIN " . TABLE_PRODUCTS_HLA_ATTRIBUTES . " AS hla_attr
										ON (hla_char.products_hla_characters_id = hla_attr.products_hla_characters_id AND hla_attr.language_id = '1')
									WHERE hla.products_id = '" . tep_db_input($product_id) . "'
										AND hla_attr.products_hla_attributes_type = '" . tep_db_input($attr_type) . "'";
            $get_hla_attr_result_select = tep_db_query($get_hla_attr_select);
            while ($get_hla_attr_row = tep_db_fetch_array($get_hla_attr_result_select)) {
                $attr_result[] = $get_hla_attr_row['products_hla_value'];
            }
            sort($attr_result);

            $memcache_obj->store($cache_key, $attr_result, 86400); // 1 Day
        }

        return $attr_result;
    }

    function get_product_child_type() {
        global $memcache_obj;

        $cache_key = TABLE_CUSTOM_PRODUCTS_TYPE_CHILD . '/product_child_type_info/array/';

        $cache_result = $memcache_obj->fetch($cache_key);
        if ($cache_result !== FALSE) {
            $this->custom_product_type_child_id_array = $cache_result['ctype'];
            $this->custom_product_type_id_array = $cache_result['tpl'];
        } else {
            $custom_product_type_child_select_sql = " 	SELECT custom_products_type_id, custom_products_type_child_id, display_status
														FROM " . TABLE_CUSTOM_PRODUCTS_TYPE_CHILD . " AS c
														ORDER BY sort_order";
            $custom_product_type_child_result_sql = tep_db_query($custom_product_type_child_select_sql);
            while ($custom_product_type_child_row = tep_db_fetch_array($custom_product_type_child_result_sql)) {
                if ($custom_product_type_child_row['display_status'] == 1) {
                    $this->custom_product_type_child_id_array[] = $custom_product_type_child_row['custom_products_type_child_id'];
                    $this->custom_product_type_id_array[] = $custom_product_type_child_row['custom_products_type_id'];
                }
            }

            if (tep_not_null($this->custom_product_type_child_id_array)) {
                $custom_product_type_array['ctype'] = $this->custom_product_type_child_id_array;
                $custom_product_type_array['tpl'] = $this->custom_product_type_id_array;

                $memcache_obj->store($cache_key, $custom_product_type_array, 600); // cache 10 minutes
            }
        }
    }

    function price_conversion_non_decimal($number, $from_currency_type = DEFAULT_CURRENCY, $to_currency_type = DEFAULT_CURRENCY, $power_of = 1, $currency_symbol = false, $type = 'buy') {
        global $currencies;

        $price_number = 0;

        $rate = $currencies->advance_currency_conversion_rate($from_currency_type, $to_currency_type, $type);

        $price_number = ceil(intval($number * $rate) / $power_of) * $power_of;

        if ($currency_symbol) {
            $price_number = $currencies->currencies[$to_currency_type]['symbol_left'] . $price_number . $currencies->currencies[$to_currency_type]['symbol_right'];
        }

        return $price_number;
    }

    function get_product_delivery_mode($products_id) {
        global $memcache_obj;

        $cache_key = TABLE_PRODUCTS_DELIVERY_INFO . '/products_delivery_mode_id/array/products_id/' . $products_id;
        $cache_result = $memcache_obj->fetch($cache_key);

        $products_delivery_mode_array = array();

        if ($cache_result !== FALSE) {
            $products_delivery_mode_array = $cache_result;
        } else {
            $product_info_select_sql = "SELECT products_bundle, products_bundle_dynamic, custom_products_type_id
										FROM " . TABLE_PRODUCTS . "
										WHERE products_id='" . tep_db_input($products_id) . "'";
            $product_info_result_sql = tep_db_query($product_info_select_sql);
            $product_info_row = tep_db_fetch_array($product_info_result_sql);

            if ($product_info_row["products_bundle"] == 'yes' || $product_info_row["products_bundle_dynamic"] == 'yes') {
                // Assume package ONLY consists of ONE product type
                $product_delivery_mode_found = false;
                $custom_products_type_id = $product_info_row["custom_products_type_id"];
                $sub_product_select_sql = "	SELECT p.products_id, p.custom_products_type_id
							    			FROM " . TABLE_PRODUCTS . " AS p
											INNER JOIN " . TABLE_PRODUCTS_BUNDLES . " AS pb
												ON p.products_id=pb.subproduct_id
							    			WHERE pb.bundle_id = '" . (int) $products_id . "'";
                $sub_product_result_sql = tep_db_query($sub_product_select_sql);
                while ($sub_product_row = tep_db_fetch_array($sub_product_result_sql)) {
                    $custom_products_type_id = $sub_product_row['custom_products_type_id'];

                    $sub_product_delivery_mode_array = product::get_product_delivery_mode($sub_product_row['products_id']);

                    if (count($products_delivery_mode_array)) {
                        $products_delivery_mode_array = array_intersect($products_delivery_mode_array, $sub_product_delivery_mode_array);
                    } else {
                        $products_delivery_mode_array = $sub_product_delivery_mode_array;
                    }
                }

                if (!$product_delivery_mode_found && count($products_delivery_mode_array) == 0) {
                    if ($custom_products_type_id == 2) {
                        $products_delivery_mode_array[] = 5; // force all to 5 if empty
                    } else if ($custom_products_type_id == 0) {
                        $products_delivery_mode_array[] = 1; // force all to 1 if empty
                    }
                }
            } else {
                $product_delivery_mode_select_sql = "	SELECT pdi.products_delivery_mode_id
														FROM " . TABLE_PRODUCTS_DELIVERY_INFO . " AS pdi
														WHERE pdi.products_id = '" . (int) $products_id . "'";
                $product_delivery_mode_result_sql = tep_db_query($product_delivery_mode_select_sql);
                while ($product_delivery_mode_row = tep_db_fetch_array($product_delivery_mode_result_sql)) {
                    $products_delivery_mode_array[] = $product_delivery_mode_row['products_delivery_mode_id'];
                }

                if (count($products_delivery_mode_array) == 0) {
                    if ($product_info_row["custom_products_type_id"] == 2) {
                        $products_delivery_mode_array[] = 5; // force all to 5 if empty
                    } else if ($product_info_row["custom_products_type_id"] == 0) {
                        $products_delivery_mode_array[] = 1; // force all to 1 if empty
                    }
                }
            }

            $memcache_obj->store($cache_key, $products_delivery_mode_array, 86400); // 1 day
        }
        return $products_delivery_mode_array;
    }

    /* -- verify product quantity and status -- */

    function product_add_to_cart_permission($products_id, $type, $product_instance_id = '') {
        global $default_languages_id, $languages_id;

        define('EC_COMBO_MAX_QTY', 20);

        $status_info = array();
        $show_it = 1;
        $pre_order_time = '';
        $is_future_product = false;
        $auto_purchase_mode = false;

        $bundle_qty = '';
        $subproduct_qty = '';
        $tmp_available_qty = '';
        $tmp_pre_order_level = '';
        $tmp_out_of_stock_level = '';
        $cur_eta = 0;
        $max_future_date = '';
        $display_html = '';

        $products_quantity = 0;
        $products_message = '';
        $custom_products_type_child_id = '';

        $available_qty = '';
        $pre_order_level = '';
        $out_of_stock_level = '';
        $products_date_available = '';
        $products_out_of_stock_msg = '';
        $products_add_to_cart_msg = '';
        $products_preorder_msg = '';


        if ($type == 'products_bundle') {
            // retrieve BUNDLE PRODUCT's child-product(s) info
            $bundle_select_sql = "	SELECT p.products_id, p.products_quantity, p.products_status,
										p.products_skip_inventory, p.products_eta,
										p.products_pre_order_level, p.products_out_of_stock_level,
										p.products_purchase_mode, p.products_date_available,
										pb.bundle_id, pb.subproduct_id, pb.subproduct_qty
									FROM " . TABLE_PRODUCTS . " AS p
									INNER JOIN " . TABLE_PRODUCTS_BUNDLES . " AS pb
					  					ON pb.subproduct_id = p.products_id
									WHERE pb.bundle_id = '" . tep_get_prid($products_id) . "'
										AND p.products_id <> ''";
            $bundle_result_sql = tep_db_query($bundle_select_sql);
            if (tep_db_num_rows($bundle_result_sql) > 0) {
                while ($bundle_data = tep_db_fetch_array($bundle_result_sql)) {
                    if (!$bundle_data['products_status'] || $bundle_data['products_purchase_mode'] == '3') { // Inactive or Out of Stock
                        $show_it = 0;
                        break;
                    } else {
                        $subproduct_qty = $bundle_data['subproduct_qty'];
                        $prod_available_qty = $bundle_data['products_quantity'];

                        switch ($bundle_data['products_purchase_mode']) {
                            case '1': // Always Add to Cart
                                ;  // do nothing, remain current whatsoever state
                                break;

                            case '2': // Always Pre-Order
                                if ($show_it != 0) {
                                    $show_it = 2;

                                    if ($bundle_data['products_date_available'] > date('Y-m-d H:i:s')) {
                                        $is_future_product = true;

                                        if ($bundle_data['products_date_available'] > $max_future_date) {
                                            $max_future_date = $bundle_data['products_date_available'];
                                        }

                                        if (tep_not_empty($tmp_available_qty) && tep_not_empty($tmp_pre_order_level)) {
                                            $tmp_pre_order_level = $tmp_available_qty;
                                        }
                                    }
                                }
                                break;

                            case '3': // Always Out of Stock
                                $show_it = 0;
                                $auto_purchase_mode = false;
                                break;

                            case '4': // Auto Mode
                                if ($show_it != 0) {
                                    if (!$bundle_data['products_skip_inventory']) {
                                        $auto_purchase_mode = true;

                                        $available_qty = floor($prod_available_qty / $subproduct_qty);
                                        $pre_order_level = floor($bundle_data['products_pre_order_level'] / $subproduct_qty);
                                        $out_of_stock_level = floor($bundle_data['products_out_of_stock_level'] / $subproduct_qty);

                                        if (!tep_not_empty($tmp_available_qty) || ($tmp_available_qty > $available_qty)) {
                                            $tmp_available_qty = $available_qty;
                                        }

                                        if (!tep_not_empty($tmp_pre_order_level) || ($tmp_pre_order_level > $pre_order_level)) {
                                            $tmp_pre_order_level = $pre_order_level;
                                        }

                                        if (!tep_not_empty($tmp_out_of_stock_level) || ($tmp_out_of_stock_level > $out_of_stock_level)) {
                                            $tmp_out_of_stock_level = $out_of_stock_level;
                                        }

                                        if (!tep_not_empty($pre_order_level) && !tep_not_empty($out_of_stock_level)) {
                                            $show_it = 1;
                                        } else {
                                            if (tep_not_empty($out_of_stock_level) && ((int) $out_of_stock_level >= $available_qty)) { // Out-of-Stock Level
                                                $show_it = 0;
                                            } else if (tep_not_empty($pre_order_level) && ((int) $pre_order_level >= $available_qty)) { // Pre-Order Level
                                                $show_it = 2;

                                                if ($bundle_data['products_date_available'] > date('Y-m-d H:i:s')) {
                                                    $is_future_product = true;
                                                } else {
                                                    # History Date, always Add-to-Cart
                                                    $show_it = 1;
                                                }
                                            }
                                        }

                                        if (!tep_not_empty($max_future_date) || ($bundle_data['products_date_available'] > $max_future_date)) {
                                            $max_future_date = $bundle_data['products_date_available'];
                                        }
                                    } else {
                                        if ($show_it != 2) {
                                            $show_it = 1;
                                        }
                                    }
                                }
                                break;
                        }
                    }
                }

                if (!$show_it) {
                    $is_future_product = false;
                } else {
                    $products_date_available = $max_future_date;

                    if (tep_not_empty($tmp_available_qty)) {
                        $available_qty = $tmp_available_qty;
                    }

                    if (tep_not_empty($tmp_pre_order_level)) {
                        $pre_order_level = $tmp_pre_order_level;
                    }

                    if (tep_not_empty($tmp_out_of_stock_level)) {
                        $out_of_stock_level = $tmp_out_of_stock_level;
                    }
                }
            }
        } else if ($type == 'products_bundle_dynamic') { // 20110315 WEICHEN : not support in LIVE, least priority to test
            $dynamic_weight_select_sql = "	SELECT products_bundle_dynamic_qty
											FROM " . TABLE_PRODUCTS . "
											WHERE products_id = '" . tep_db_input($products_id) . "'";
            $dynamic_weight_result_sql = tep_db_query($dynamic_weight_select_sql);
            if ($dynamic_weight = tep_db_fetch_array($dynamic_weight_result_sql)) {
                $available_weight = 0; // store all total possible weight excluding pre-order status item(s)

                $bundle_select_sql = "	SELECT p.products_id, p.products_quantity, p.products_eta,
											p.products_pre_order_level, p.products_out_of_stock_level,
											p.products_purchase_mode, p.products_date_available,
											pb.bundle_id, pb.subproduct_id, pb.subproduct_qty, pb.subproduct_weight
										FROM " . TABLE_PRODUCTS . " AS p
										INNER JOIN " . TABLE_PRODUCTS_BUNDLES . " AS pb
						  					ON pb.subproduct_id = p.products_id
										WHERE pb.bundle_id = " . tep_get_prid($products_id) . "
											AND p.products_status = 1
											AND p.products_purchase_mode <> 3";
                $bundle_result_sql = tep_db_query($bundle_select_sql);
                while ($bundle_data = tep_db_fetch_array($bundle_result_sql)) {
                    switch ($bundle_data['products_purchase_mode']) {
                        case '1': // Always Add to Cart
                            $show_it = 1;
                            break 2;

                        case '2': // Always Pre-Order
                            $show_it = 2;

                            if ($bundle_data['products_date_available'] > date('Y-m-d H:i:s')) {
                                $is_future_product = true;

                                if ($bundle_data['products_date_available'] > $max_future_date) {
                                    $max_future_date = $bundle_data['products_date_available'];
                                }
                            }
                            break;

                        case '3': // Always Out of Stock
                            $show_it = 0;
                            break;

                        case '4': // Auto Mode
                            if (!$bundle_data['products_skip_inventory']) {
                                $auto_purchase_mode = true;
                            }

                            $subproduct_qty = $bundle_data['subproduct_qty'];
                            $prod_available_qty = $bundle_data['products_quantity'];
                            $prod_pre_order_level = $bundle_data['products_pre_order_level'];
                            $prod_out_of_stock_level = $bundle_data['products_out_of_stock_level'];

                            $remain_stock = $prod_available_qty - $subproduct_qty;

                            if (tep_not_empty($prod_out_of_stock_level) && ($prod_out_of_stock_level > $remain_stock)) {  // Out-of-Stock Level
;
                            } else if (tep_not_empty($prod_pre_order_level) && ($prod_pre_order_level > $remain_stock)) { // Pre-Order Level
                                $show_it = 2;

                                if ($bundle_data['products_date_available'] > date('Y-m-d H:i:s')) {
                                    $is_future_product = true;

                                    if ($bundle_data['products_date_available'] > $max_future_date) {
                                        $max_future_date = $bundle_data['products_date_available'];
                                    }
                                }
                            } else {
                                $qty_ratio = 0;

                                if (tep_not_empty($prod_out_of_stock_level)) {
                                    $qty_ratio = ($prod_available_qty - $prod_out_of_stock_level) / $subproduct_qty;
                                } else {
                                    if (!tep_not_empty($prod_out_of_stock_level) && !tep_not_empty($pre_order_level)) { // Always Add-to-Cart
                                        $show_it = 1;
                                        break 2;
                                    } else {
                                        $qty_ratio = $prod_available_qty / $subproduct_qty;
                                    }
                                }

                                if ($qty_ratio >= 1) {
                                    $available_weight += $qty_ratio * $bundle_data['subproduct_weight'];
                                }
                            }
                            break;
                    }
                }

                if (!$show_it || ($show_it == 2)) {
                    $show_it = ($available_weight >= $dynamic_weight['products_bundle_dynamic_qty']) ? 1 : $show_it;
                }

                if (!$show_it) {
                    $is_future_product = false;
                } else {
                    $products_date_available = $max_future_date;
                }
            }
        } else {
            $product_select_sql = "	SELECT products_quantity, products_eta, products_pre_order_level,
										products_out_of_stock_level, products_purchase_mode, products_date_available,
										products_add_to_cart_msg, products_preorder_msg, products_out_of_stock_msg,
										products_skip_inventory
									FROM " . TABLE_PRODUCTS . "
									WHERE products_id = '" . tep_db_input($products_id) . "'";
            $product_result_sql = tep_db_query($product_select_sql);
            if ($product_row = tep_db_fetch_array($product_result_sql)) {
                switch ($product_row['products_purchase_mode']) {
                    case '1': // Always Add to Cart
                        $show_it = 1;
                        break;

                    case '2': // Always Pre-Order
                        $show_it = 2;

                        // PRE-ORDER :: verify future product date and message
                        if ($product_row['products_date_available'] > date('Y-m-d H:i:s')) {
                            $is_future_product = true;
                        }
                        break;

                    case '3': // Always Out of Stock
                        $show_it = 0;
                        break;

                    case '4': // Auto Mode
                        if (!$product_row['products_skip_inventory']) {
                            $auto_purchase_mode = true;

                            $available_qty = $product_row['products_quantity'];
                            $pre_order_level = $product_row['products_pre_order_level'];
                            $out_of_stock_level = $product_row['products_out_of_stock_level'];

                            if (!tep_not_empty($pre_order_level) && !tep_not_empty($out_of_stock_level)) {
                                $show_it = 1;
                            } else {
                                if (tep_not_empty($out_of_stock_level) && (int) $out_of_stock_level >= $available_qty) { // Out-of-Stock level
                                    $show_it = 0;
                                } else if (tep_not_empty($pre_order_level) && (int) $pre_order_level >= $available_qty) { // Pre-Order level
                                    $show_it = 2;

                                    // PRE-ORDER :: verify future product date and message
                                    if ($product_row['products_date_available'] > date('Y-m-d H:i:s')) {
                                        $is_future_product = true;
                                    } else {
                                        # History Date, always Add-to-Cart
                                        $show_it = 1;
                                    }
                                }
                            }

                            $products_date_available = $product_row['products_date_available'];
                        } else {
                            $show_it = 1;
                        }

                        break;
                }
            }
        }


        // HLA Product
        if (tep_not_empty($product_instance_id) && ($show_it == 1 || $show_it == 2)) {
            if (tep_get_custom_product_type($products_id) == 4) {
                $hla_select_sql = "	SELECT products_hla_id
									FROM " . TABLE_PRODUCTS_HLA . "
									WHERE products_id = '" . tep_db_input($products_id) . "'
										AND products_hla_id = '" . tep_db_input($product_instance_id) . "'
										AND available_quantity > 0
										AND actual_quantity > 0
										AND products_status = '1'
										AND products_display = '1'";
                $hla_result_sql = tep_db_query($hla_select_sql);
                if (tep_db_num_rows($hla_result_sql) == 0) {
                    $show_it = 0;
                }
            }
        }


        // Product Delivery Message
        $product_select_sql = "	SELECT products_add_to_cart_msg, products_preorder_msg, products_out_of_stock_msg
								FROM " . TABLE_PRODUCTS . "
								WHERE products_id = '" . tep_db_input($products_id) . "'
								LIMIT 1";
        $product_result_sql = tep_db_query($product_select_sql);
        if ($product_row = tep_db_fetch_array($product_result_sql)) {
            $products_add_to_cart_msg = $product_row['products_add_to_cart_msg'];
            $products_preorder_msg = $product_row['products_preorder_msg'];
            $products_out_of_stock_msg = $product_row['products_out_of_stock_msg'];

            switch ($show_it) {
                case '0':
                    if (tep_not_empty($product_row['products_out_of_stock_msg'])) {
                        $pre_order_time = $products_out_of_stock_msg;
                    }
                    break;

                case '1':
                    if (tep_not_empty($product_row['products_add_to_cart_msg'])) {
                        $pre_order_time = $products_add_to_cart_msg;
                    }
                    break;

                case '2':
                    if (tep_not_empty($product_row['products_preorder_msg'])) {
                        $pre_order_time = $products_preorder_msg;
                    }
                    break;
            }
        }




        /* -- EXPRESS CHECKOUT :: stock check max available qty -- */
        if ($show_it > 0) {
            $products_quantity = EC_COMBO_MAX_QTY;

            $product_select_sql = "	SELECT products_quantity
									FROM " . TABLE_PRODUCTS . "
									WHERE products_id = '" . tep_db_input($products_id) . "'
										AND products_status = 1
										AND products_display = 1
									LIMIT 1";
            $product_result_sql = tep_db_query($product_select_sql);
            if ($product_row = tep_db_fetch_array($product_result_sql)) {
                if ($auto_purchase_mode && tep_not_empty($out_of_stock_level)) { // Purchase Mode : Auto
                    $products_quantity = abs($out_of_stock_level - $product_row['products_quantity']);
                }
            }
        }

        // DIRECT TOP-UP :: delivery mode
        if ((int) $this->tpl == 2) {
            $product_delivery_mode_array = $this->get_product_delivery_mode($products_id);
            $delivery_methods_array = array();
            foreach ($product_delivery_mode_array as $product_delivery_mode_id_loop) {
                $display_delivery_mode_label = '';
                switch ($product_delivery_mode_id_loop) {
                    case '5':
                        $display_delivery_mode_label = TEXT_INFO_DELIVERY_SEND_TO_ACCOUNT;
                        //
                        break;
                    case '6':
                        $display_delivery_mode_label = TEXT_INFO_DELIVERY_DIRECT_TOP_UP;

                        include_once(DIR_WS_CLASSES . 'direct_topup.php');
                        $direct_topup_obj = new direct_topup();

                        if ($type == 'products_bundle_dynamic' || $type == 'products_bundle') {
                            $bundle_select_sql = "	SELECT pp.products_id
													FROM " . TABLE_PUBLISHERS_PRODUCTS . " AS pp
													INNER JOIN " . TABLE_PRODUCTS_DELIVERY_INFO . " AS pdi
														ON pdi.products_id = pp.products_id
													INNER JOIN " . TABLE_PRODUCTS_BUNDLES . " AS pb
														ON pp.products_id=pb.subproduct_id
													WHERE pb.bundle_id = '" . tep_get_prid($products_id) . "'
														AND pdi.products_delivery_mode_id = '6'
													LIMIT 1";
                            $bundle_result_sql = tep_db_query($bundle_select_sql);
                            $bundle_row = tep_db_fetch_array($bundle_result_sql);
                            $game_input_array = $direct_topup_obj->get_game_input($bundle_row['products_id']);
                        } else {
                            $game_input_array = $direct_topup_obj->get_game_input($products_id);
                        }

                        $display_html .= '	<table id="top_up_input" border="0" cellspacing="0" cellpadding="0" width="100%">
												<tr>
													<td>' . tep_draw_separator('pixel_trans.gif', '100%', '3') . '</td>
												</tr>';
                        if (count($game_input_array)) {
                            foreach ($game_input_array as $game_input_key_loop => $game_input_data_loop) {
                                $display_html .= '	<tr>
														<td>
															<div class="' . ($game_input_data_loop['top_up_info_key'] != 'dhx_combo_box' ? '' : '') . '" style="width:240px">';
                                switch ($game_input_data_loop['top_up_info_key']) {
                                    case 'server':
                                        // server list
                                        $servers_select_sql = "	SELECT pg.publishers_server
																FROM " . TABLE_PUBLISHERS_GAMES . " AS pg
																INNER JOIN " . TABLE_PUBLISHERS_PRODUCTS . " AS pp
																	ON pg.publishers_games_id = pp.publishers_games_id
																WHERE pp.products_id = '" . (int) $products_id . "'";
                                        $servers_result_sql = tep_db_query($servers_select_sql);
                                        $servers_row = tep_db_fetch_array($servers_result_sql);
                                        $servers_tmp_array = json_decode($servers_row['publishers_server'], 1);
                                        $servers_array = array();
                                        if (isset($servers_tmp_array)) {
                                            foreach ($servers_tmp_array as $servers_id_loop => $server_name_loop) {
                                                $servers_array[] = array('id' => $servers_id_loop,
                                                    'text' => $server_name_loop);
                                            }
                                        }

                                        $display_html .= '	<div id="ecServerListing" style="float: left;">' .
                                                tep_draw_pull_down_menu("game_info[" . $game_input_key_loop . "]", $servers_array, '', ' id="ec_dtu_server_id" ') .
                                                '	</div>';
                                        break;
                                    case 'info_label':
                                        $display_html .= $game_input_data_loop['top_up_info_display'];
                                        break;
                                    case 'character':
                                        if ($direct_topup_obj->character_is_sync($products_id)) {
                                            $character_list_array = array();
                                            $character_list_array[] = array('id' => '',
                                                'text' => $game_input_data_loop['top_up_info_display']);
                                            //$display_html .= tep_draw_pull_down_menu("game_info[".$game_input_key_loop."]", $character_list_array, '', ' default_text="'.$game_input_data_loop['top_up_info_display'].'" onfocus="load_character_list(this, \''.$pID.'\', \''.$product_delivery_mode_id_loop.'\', \''.$index.'\');jQuery(this).removeClass(\'productListingDTUPreInput\').addClass(\'productListingDTUAftInput\')" onblur="if(this.value==\'\') {jQuery(this).removeClass(\'productListingDTUAftInput\').addClass(\'productListingDTUPreInput\')}else{jQuery(this).removeClass(\'productListingDTUPreInput\').addClass(\'productListingDTUAftInput\')}" class="productListingDTUPreInput" ');
                                            $display_html .= '	<div id="ecCharacterListing" style="float: left;">' .
                                                    tep_draw_pull_down_menu("game_info[" . $game_input_key_loop . "]", $character_list_array, '', ' id="ec_dtu_character_list" ') .
                                                    '	</div>';
                                        } else {
                                            $display_html .= '<div class="dhx_combo_box">' . tep_draw_input_field("game_info[" . $game_input_key_loop . "]", $game_input_data_loop['top_up_info_display'], ' maxlength="64" size="20" onfocus="this.value=\'\'" onblur="if(this.value==\'\') {this.value=jQuery(this).attr(\'default_text\')}" default_text="' . $game_input_data_loop['top_up_info_display'] . '" style="width:240px;" class="dhx_combo_box" id="ec_dtu_' . $game_input_key_loop . '"') . '</div>';
                                        }
                                        break;
                                    default: // account
                                        $display_html .= '<div class="dhx_combo_box">' . tep_draw_input_field("game_info[" . $game_input_key_loop . "]", $game_input_data_loop['top_up_info_display'], ' maxlength="64" size="20" onfocus="this.value=\'\'" onblur="if(this.value==\'\') {this.value=jQuery(this).attr(\'default_text\')}" default_text="' . $game_input_data_loop['top_up_info_display'] . '" style="width:240px;" class="dhx_combo_box" id="ec_dtu_' . $game_input_key_loop . '"') . '</div>';
                                        if ($direct_topup_obj->retype_account($products_id)) {
                                            $display_html .= '<div class="dhx_combo_box">' . tep_draw_input_field("game_info[" . $game_input_key_loop . "_2]", TEXT_RETYPE . $game_input_data_loop['top_up_info_display'], ' maxlength="64" size="20" onfocus="this.value=\'\'" onblur="if(this.value==\'\') {this.value=jQuery(this).attr(\'default_text\')}" default_text="' . TEXT_RETYPE . $game_input_data_loop['top_up_info_display'] . '" style="width:240px;" class="dhx_combo_box" id="ec_dtu_' . $game_input_key_loop . '_2"') . '</div>';
                                        }
                                        break;
                                }
                                $display_html .='			</div>
														</td>
													</tr>
													<tr>
														<td>' . tep_draw_separator('pixel_trans.gif', '100%', '5') . '</td>
													</tr>';
                            }
                        }

                        $display_html .='		</table>';
                        break;
                    case '7':
                        $display_delivery_mode_label = TEXT_INFO_DELIVERY_IN_STORE_PICKUP;
                        //
                        break;
                }

                $delivery_methods_array[$product_delivery_mode_id_loop] = $display_delivery_mode_label;
            }
        }

        // if is bundle + cd key, not allow customer to add cart if all cant intersect any delivery mode
        if (($type == 'products_bundle' || $type == 'products_bundle_dynamic')) {
            if (count($product_delivery_mode_array) == 0) {
                $show_it = 0;
            }
        }

        // return values
        $status_info = array('auto_purchase_mode' => $auto_purchase_mode,
            'show' => $show_it,
            'is_future_product' => $is_future_product ? 1 : 0,
            'quantity' => $products_quantity,
            'bundle' => ($type == 'products_bundle' ? 'yes' : ''),
            'pre_order_time' => (tep_not_empty($pre_order_time) ? '#DATE#' . $pre_order_time . '#DATE#' : ''),
            'delivery_methods' => $delivery_methods_array,
            'delivery_mode_html' => $display_html,
            'products_date_available' => $products_date_available,
            'products_quantity' => $available_qty,
            'products_pre_order_level' => $pre_order_level,
            'products_out_of_stock_level' => $out_of_stock_level,
            'products_out_of_stock_msg' => (tep_not_empty($products_out_of_stock_msg) ? '#DATE#' . $products_out_of_stock_msg . '#DATE#' : ''),
            'products_add_to_cart_msg' => (tep_not_empty($products_add_to_cart_msg) ? '#DATE#' . $products_add_to_cart_msg . '#DATE#' : ''),
            'products_preorder_msg' => (tep_not_empty($products_preorder_msg) ? '#DATE#' . $products_preorder_msg . '#DATE#' : '')
        );

        return $status_info;
    }

    private function get_promotion_products($products_id) {
        global $memcache_obj;
        $cached_duration = 86400; // 1 day
        $return_array = array();
        $ttl_seconds = 0;

        $cache_key = TABLE_PRODUCTS_PROMOTION . '/promotion_product_info/array/products_id/' . $products_id;
        $cache_result = $memcache_obj->fetch($cache_key);
        if ($cache_result !== FALSE) {
            $return_array = $cache_result;
            $ttl_seconds = (int) $return_array['ts'] - time();
        } else {

            $pp_select_sql = "	SELECT products_id, promotion_box_only, promotion_selling_status,
									promotion_limited_stock, promotion_limited_stock_qty, promotion_end_date
								FROM " . TABLE_PRODUCTS_PROMOTION . "
								WHERE (promotion_start_date != '0000-00-00 00:00:00' AND (promotion_end_date = '0000-00-00 00:00:00' AND now() >= promotion_start_date)
									OR (promotion_end_date != '0000-00-00 00:00:00' AND promotion_end_date > now() AND now() >= promotion_start_date))
									AND products_id = " . (int) $products_id;
            $pp_result_sql = tep_db_query($pp_select_sql);
            if ($pp_row = tep_db_fetch_array($pp_result_sql)) {
                if ($pp_row['promotion_end_date'] != '0000-00-00 00:00:00') {
                    $temp_date = str_ireplace(array(' ', ':'), '-', $pp_row['promotion_end_date']);
                    list($yr, $mth, $day, $hr, $mn, $sc) = explode("-", $temp_date);
                    $pp_row['ts'] = mktime($hr, $mn, $sc, $mth, $day, $yr);
                    $ttl_seconds = (int) $pp_row['ts'] - time();

                    if ($ttl_seconds < 86400) {
                        $cached_duration = $ttl_seconds;
                    }
                }

                $return_array = $pp_row;
            }

            $memcache_obj->store($cache_key, $return_array, $cached_duration);
        }

        if ($ttl_seconds > 0) {
            $return_array['ttl_seconds'] = $ttl_seconds;
        }

        return $return_array;
    }

    private function get_promotion_product_status($product_info_array, $status_array, $setting_array) {
        $return_status = '';
        $selling_status_enabled = false;

        // GET qty from 3 bundle, bundle dynamic and single
        if ($product_info_array['products_bundle'] == 'yes') {
            $qty = (int) $status_array['bundle_qty'];
        } else if ($product_info_array['products_bundle_dynamic'] == 'yes') {
            $qty = 0; // unknown
        } else {
            $qty = (int) $product_info_array['products_quantity'];
        }

        /*
          0 = out of stock
          1 = add-to-cart
          2 = pre order
         */
        if ((int) $status_array['show'] > 0) {
            if ((int) $setting_array['promotion_limited_stock'] == 1) {
                if ($qty < (int) $setting_array['promotion_limited_stock_qty']) {
                    $return_status = TEXT_STATUS_LIMITED_STOCK . '!';
                } else {
                    $selling_status_enabled = true;
                }
            } else {
                $selling_status_enabled = true;
            }

            if ($selling_status_enabled) {
                switch ($setting_array['promotion_selling_status']) {
                    case 1:
                        $return_status = TEXT_STATUS_FAST_SELLING . '!';
                        break;
                    case 2:
                        $return_status = TEXT_STATUS_PRICE_SLASH . '!';
                        break;
                    default:
                }
            }
        }

        return $return_status;
    }

    private function draw_counter_box($count_index, $ttl_seconds, &$counter_id_array = array()) {
        $return_string = '';
        $hr = $mn = $sc = 0;

        if ($ttl_seconds > 0) {
            $ttls = $ttl_seconds;
            $counter_id_array[] = $count_index;

            $dy = floor(($ttls / 86400));
            if ($dy > 0) {
                $ttls = $ttls - ($dy * 86400);
                if ($dy > 99) {
                    $dy = 99;
                }
            }

            $hr = floor(($ttls / 3600));
            if ($hr > 0) {
                $ttls = $ttls - ($hr * 3600);
            }

            $mn = floor(($ttls / 60));
            if ($mn > 0) {
                $sc = $ttls - ($mn * 60);
            }

            $dy = str_pad($dy, 2, "0", STR_PAD_LEFT);
            $hr = str_pad($hr, 2, "0", STR_PAD_LEFT);
            $mn = str_pad($mn, 2, "0", STR_PAD_LEFT);
            $sc = str_pad($sc, 2, "0", STR_PAD_LEFT);

            $return_string = '<div id="cb_' . $count_index . '" class="counterBox">
								<input id="cbd_' . $count_index . '" type="hidden" value="' . $ttl_seconds . '">
								<div class="counterBox_left"><!-- --></div>
								<div class="counterBox_middle">
								  <div class="md"><span class="digit">' . $dy . '</span><span class="label">' . strtoupper(DAY2) . '</span></div>
								  <div class="counterBox_partition"><!-- --></div>
								  <div class="mh"><span class="digit">' . $hr . '</span><span class="label">' . strtoupper(HOURS) . '</span></div>
								  <div class="counterBox_partition"><!-- --></div>
								  <div class="mm"><span class="digit">' . $mn . '</span><span class="label">' . strtoupper(MINUTES) . '</span></div>
								  <div class="counterBox_partition"><!-- --></div>
								  <div class="ms"><span class="digit">' . $sc . '</span><span class="label">' . strtoupper(SECONDS) . '</span></div>
							  	</div>
							 	<div class="counterBox_right"><!-- --></div>
							  </div>';
        }

        return $return_string;
    }

    private function get_count_index_by_type(&$count_index, $type = '') {
        switch ($type) {
            case '':
                $return_index = $count_index;
                break;
            case 'p': // Promotion
            case 'pd':
            case 'pg':
            case 'd': // DTU
            case 'g': // General
            default: // Undefined
                $return_index = $type . '_' . $count_index;
                break;
        }

        $count_index += 1;
        return $return_index;
    }

    private function publisher_game_list() {
        global $zone_info_array, $memcache_obj;

        $table_column = 3;
        $publisher_game_list_html = '';

        if (tep_not_empty($this->main_game_id)) {
            $game_list = array();
            $game_html = '';

            $cache_key = TABLE_GAME_TO_PUBLISHER . '/game_id/array/publisher_id/' . $this->main_game_id . '/language/' . $_SESSION['languages_id'];
            $cache_result = $memcache_obj->fetch($cache_key);

            if ($cache_result !== FALSE) {
                $game_list = $cache_result;
            } else {
                include_once(DIR_WS_CLASSES . FILENAME_MAIN_PAGE);

                $game_sel_sql = "	SELECT gtp.game_id
									FROM " . TABLE_GAME_TO_PUBLISHER . " AS gtp
									INNER JOIN " . TABLE_CATEGORIES_DESCRIPTION . " AS cd
										ON cd.categories_id = gtp.game_id
											AND cd.language_id = '" . $_SESSION['languages_id'] . "'
									INNER JOIN " . TABLE_CATEGORIES . " AS c
										ON c.categories_id = gtp.game_id
											AND c.categories_status = '1'
									WHERE gtp.publisher_id = " . $this->main_game_id . "
									ORDER BY cd.categories_pin_yin ASC";
                $game_res_sql = tep_db_query($game_sel_sql);
                while ($game_row = tep_db_fetch_array($game_res_sql)) {
                    $cat_path = '';

                    if (count(array_intersect($zone_info_array[1]->zone_categories_id, array($game_row['game_id']))) > 0) {
                        $cat_path = main_page::game_cat_path_by_tpl($game_row['game_id']);

                        if ($cat_path != '') {
                            $game_list[] = array('cat_id' => $game_row['game_id'],
                                'cat_name' => tep_get_categories_name($game_row['game_id'], $_SESSION['languages_id']),
                                'cat_path' => $cat_path);
                        }
                    }
                }
                $memcache_obj->store($cache_key, $game_list, 900); // cache 15 minutes
            }

            if (tep_not_empty($game_list)) {
                $game_list_cnt = count($game_list);
                $num_col_max_row = $game_list_cnt % 3;
                $max_row_per_col = round($game_list_cnt / 3);

                $game_html .= '<div style="padding: 20px;">';
                $game_html .= '<table width="100%" border="0" cellpadding="0" cellspacing="0">';
                $game_html .= '<tr>';

                for ($i = 0; $table_column > $i; $i++) {
                    $cnt = 0;
                    $content = '';
                    $max_row = (($num_col_max_row == 1) && ($i == 0)) ? ($max_row_per_col + 1) : $max_row_per_col;

                    for ($j = 0, $max = count($game_list); $max > $j; $j++) {
                        if ($max_row > $cnt) {
                            if (tep_not_empty($game_list[$j]['cat_path'])) {
                                $url_link = tep_href_link(FILENAME_DEFAULT, 'cPath=' . $game_list[$j]['cat_path']);
                            } else {
                                $url_link = tep_href_link(FILENAME_SEARCH_ALL_GAMES, 'gid=' . $game_list[$j]['cat_id']);
                            }

                            $content .= '<tr>';
                            $content .= '<td width="10px" valign="top" style="text-align: left;"><div class="triangleRightIconGlay" style="margin: 3px 5px;"></div></td>';
                            $content .= '<td valign="top"><a href="' . $url_link . '">' . $game_list[$j]['cat_name'] . '</a></td>';
                            $content .= '</tr>';

                            $cnt++;
                            unset($game_list[$j]);
                        } else {
                            break;
                        }
                    }
                    $game_list = array_values($game_list);

                    if (($i > 0) && tep_not_empty($content)) {
                        $game_html .= '<td valign="top" style="border-left: 1px dotted #A5A5A5; padding-left: 15px; display: table-cell;">';
                        $game_html .= '<table width="223px" border="0" cellpadding="0" cellspacing="0" style="border-style: collapse;">';
                        $game_html .= $content;
                        $game_html .= '</table>';
                        $game_html .= '</td>';
                    } else {
                        $game_html .= '<td valign="top">';
                        $game_html .= '<table width="238px" border="0" cellpadding="0" cellspacing="0" style="border-style: collapse;">';
                        $game_html .= $content;
                        $game_html .= '</table>';
                        $game_html .= '</td>';
                    }
                }

                $game_html .= '</tr>';
                $game_html .= '</table>';
                $game_html .= '</div>';

                include_once(DIR_WS_CLASSES . FILENAME_PAGE);
                $publisher_game_list_html .= '<div class="vspacing"></div>';
                $publisher_game_list_html .= page::get_html_simple_rc_box(SUPPORTED_GAME_LIST, $game_html, 11);
            }
        }

        return $publisher_game_list_html;
    }

}

?>