<?
/*
  	$Id: products_low_stock.php,v 1.1 2010/07/29 11:16:05 wilson.sun Exp $

  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com

  	Copyright (c) 2003 osCommerce

  	Released under the GNU General Public License
*/

class products_low_stock {
	
	// class constructor
    function products_low_stock() {
	}
	
	function add_low_stock_warning($trans_array) {
		$error = false;
		
		if (isset($trans_array['products_id']) && tep_not_null($trans_array['products_id'])) {
			$check_warning_select_sql = "SELECT products_id FROM " . TABLE_PRODUCTS_LOW_STOCK . " WHERE products_id = '" . tep_db_input($trans_array['products_id']) . "'";
			$check_warning_result_sql = tep_db_query($check_warning_select_sql);
			if (tep_db_num_rows($check_warning_result_sql) <= 0) {
				$data_array = array (	'products_id' => $trans_array['products_id'],
										'custom_products_type_id' => $trans_array['custom_products_type_id'],
										'low_stock_date' => 'now()',
										'low_stock_tag_ids' => '',
										'low_stock_status' => 1
									);
				tep_db_perform(TABLE_PRODUCTS_LOW_STOCK, $data_array);
			}
		} else {
			$error = true;
		}
		
		return $error;
	}
}
?>