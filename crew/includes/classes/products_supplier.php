<?php

class products_supplier {
	var $supplier_id, $products_supplier, $products_rss_link;
	
	function products_supplier($supplier_id) {
		$this->products_supplier = array();
		
		$this->supplier_id = $supplier_id;
		
		$this->query();
	}
	
	function query() {
		$products_supplier_array = array();
		
		$products_supplier_select_sql = "SELECT * FROM " . TABLE_PRODUCTS_SUPPLIER . " WHERE supplier_id = '" . (int)$this->supplier_id . "'";
		$products_supplier_result_sql = tep_db_query($products_supplier_select_sql);
		if ($products_supplier_row = tep_db_fetch_array($products_supplier_result_sql)) {
			foreach ($products_supplier_row as $key => $val) {
				$products_supplier_array[$key] = $val;
			}
		}
		
		$this->products_supplier = $products_supplier_array;
	}
}
?>