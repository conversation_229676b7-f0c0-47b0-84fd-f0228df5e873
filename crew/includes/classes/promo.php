<?php
/*
  	$Id: promo.php,v 1.1 2011/03/25 07:56:51 weesiong Exp $
*/
class promo {
	public $cookie_name;
	public $promo_enabled = 'true';
	public $start_date = '2011-03-28';	// YYYY-MM-DD 00:00:00
	public $expired_date = '2011-04-30';	// YYYY-MM-DD 23:59:59
	public $coming_timestamp = 0;
	public $future_timestamp = 0;
	public $current_timestamp = 0;
	
	// class constructor
	public function __construct () {
		if (tep_not_null($this->expired_date)) {
			list($yr, $mth, $day) = explode("-", $this->expired_date);
			$this->current_timestamp = time();
			$this->future_timestamp = mktime(23, 59, 59, $mth, $day, $yr);
		}
		
		if (tep_not_null($this->start_date)) {
			list($yr, $mth, $day) = explode("-", $this->start_date);
			$this->coming_timestamp = mktime(0, 0, 1, $mth, $day, $yr);
		}
	}
	
	public function is_promo_enabled () {
		return ($this->promo_enabled == 'true' && $this->is_promo_active());
	}
	
	public function is_promo_active () {
		$return_status = true;
		
		if ($this->coming_timestamp > 0) {
			$return_status = ((time() - $this->coming_timestamp) > 0);
		}
		
		if ($return_status) {
			$return_status = (($this->future_timestamp - $this->current_timestamp) > 0);
		}
		
		return $return_status;
	}
	
	public function set_cookie_name ($cookie_name) {
		if (tep_not_null($cookie_name)) {
			$this->cookie_name = $cookie_name;
		}
	}
	
	public function set_cookie_expiry_duration ($cookie_expiry_duration) {
		// In Minute
		if (tep_not_null($cookie_expiry_duration)) {
			$this->future_timestamp = time() + $cookie_expiry_duration;
		}
	}
	
	public function promo_cookies_exist ($cookie_name = null) {
		$this->set_cookie_name($cookie_name);
		
		return isset($_COOKIE[$this->cookie_name]);
	}
	
	public function create_cookie ($cookie_value = 1, $cookie_expiry_date = null, $cookie_name = null) {
		global $cookie_path, $cookie_domain;
		
		$this->set_cookie_expiry_duration($cookie_expiry_date); // 1 year expiry date;
		$this->set_cookie_name($cookie_name);
		
		if ($this->is_promo_active()) {
			tep_setcookie($this->cookie_name, $cookie_value, $this->future_timestamp, $cookie_path, $cookie_domain);
		}
	}
	
	public function update_order_success_log ($orders_id) {
		$check_exist_sql = "	SELECT orders_id 
								FROM " . TABLE_PROMOTIONS_ORDERS_LOG . "
								WHERE orders_id = ". (int)$orders_id;
		$check_exist_result_sql = tep_db_query($check_exist_sql);
		if (!tep_db_num_rows($check_exist_result_sql)) {
			$insert_array = array(	'orders_id' => $orders_id,
									'created_datetime' => 'now()'
								);
			tep_db_perform(TABLE_PROMOTIONS_ORDERS_LOG, $insert_array);	
		}
	}
	
	function printContent($news_id) {
		global $languages_id, $default_languages_id;
		
		$aws_obj = new ogm_amazon_ws();
		$aws_obj->set_bucket_key('BUCKET_STATIC');
		$aws_obj->set_filepath('images/news/');
		
		$this_site_id = 0;
		if (defined('SITE_ID')) {
			$this_site_id = SITE_ID;
		}
		
		if (!tep_not_null($news_id)) {
			$news_id = 1767;
		}
		
		$news_display_sites_where_str = "FIND_IN_SET( '" . $this_site_id . "', news_display_sites)";
	
		$page_sql = "	SELECT lnd.news_id, lnd.headline, lnd.content, ln.date_added, ln.url from ". TABLE_LATEST_NEWS. "  as ln 
						INNER JOIN " . TABLE_LATEST_NEWS_DESCRIPTION . "  as lnd 
							ON (ln.news_id = lnd .news_id) 
							where " . $news_display_sites_where_str . "  
					    		AND ( if(lnd.language_id = '". $languages_id. "' && lnd.headline <> '', 1, 
										if ((	select count(lnd.news_id) > 0 from  " . TABLE_LATEST_NEWS_DESCRIPTION . " as lnd
												where " . $news_display_sites_where_str . " 
													and lnd.news_id ='".$news_id."' 
													and lnd.language_id ='". $languages_id. "'
													and lnd.headline <> ''), 0,
												lnd.language_id = '".$default_languages_id."')
										)
									)
					    		AND lnd.news_id ='".$news_id."'"; 
		$page_query = tep_db_query($page_sql);
		$page = tep_db_fetch_array($page_query);
?>
			<div class="line" style="width:671px"><!-- --></div>
			<div style="float:left">
				<h1><?=$this->eval_html($page["headline"]) ?></h1>
				<small><?=tep_date_long($page["date_added"]) ?></small>
			</div>
			<div style="clear:both"><!-- --></div>
			<div class="line" style="width:671px"><!-- --></div>
			
<?
		if ($aws_obj->is_aws_s3_enabled()) {
			if ($aws_obj->is_image_exists($page['news_id'] .'_2_'.$_SESSION['languages_id'].'.jpg')) {
				echo tep_image($aws_obj->get_image_url_by_instance());
				echo '<div class="breakLine"><!-- --></div>';
			} 
		} else {
			if (file_exists(DIR_FS_CATALOG. 'images/news/'. $page['news_id'] .'_2_'.$_SESSION['languages_id'].'.jpg')) {
				echo tep_image(DIR_WS_IMAGES . 'news/'.$page['news_id']."_2_".$_SESSION['languages_id'].".jpg"); 
				echo '<div class="breakLine"><!-- --></div>';
			} 
		}
?>
			<div style="float:right;width:70px;margin:10px 0 10px 10px;border:solid 1px #bdbdbd">
				<div style="padding:10px">
					<a href="http://twitter.com/share" class="twitter-share-button" data-count="vertical">Tweet</a><script type="text/javascript" src="http://platform.twitter.com/widgets.js"></script>
					<div class="breakLine"><!-- --></div>
					<script src="/includes/javascript/fbShare.js" type="text/javascript"></script>
					<a name="fb_share" type="box_count" share_url="<?='http://'.$_SERVER['HTTP_HOST'].$_SERVER['REQUEST_URI'] ?>"></a>
					<div class="breakLine"><!-- --></div>
					<script type="text/javascript">
					(function() {
					var s = document.createElement('SCRIPT'), s1 = document.getElementsByTagName('SCRIPT')[0];
					s.type = 'text/javascript';
					s.async = true;
					s.src = 'http://widgets.digg.com/buttons.js';
					s1.parentNode.insertBefore(s, s1);
					})();
					</script>
					<a class="DiggThisButton DiggMedium"></a>
					<div class="breakLine"><!-- --></div>
					<script src="http://www.stumbleupon.com/hostedbadge.php?s=5"></script>
					<div class="breakLine"><!-- --></div>
					<script language="javascript" type="text/javascript"> 
						//Use a customized callback routine. For example:
						//Create a sharelet with button element set to false and the custom handler
						var object = SHARETHIS.addEntry({
								title:'<?=$this->eval_html($page["headline"]) ?>',
								url: '<?='http://'.$_SERVER['HTTP_HOST'].$_SERVER['REQUEST_URI'] ?>'
							},
							{button:false, onmouseover: false, offsetLeft: -122, offsetTop: -150, onclick:custom_st_fn}
						);
						//Create customized button and attach it to the share object
						document.write('<span id="share"><a href="javascript:void(0);" class="ln_stbutton_background"><span>Share</span></a></span>'); //class="stbutton"
						var element = document.getElementById("share");
						object.attachButton(element);
						
						function custom_st_fn() {
							jQuery("#stwrapper").mouseout(function(){
								jQuery("div.stclose").trigger('click');
							});
							return true;
						}
					</script>
					<div style="clear:both"><!-- --></div>
				</div>
			</div>
			<p class="mediumFont" id='newsContent'><?=$this->eval_html($page["content"]) ?></p>
			<div class="breakLine"><!-- --></div>
			<div class="dottedLine"><!-- --></div>
<?
	}
	
	function eval_html($string) {
		$string = preg_replace_callback("/(<\?=)(.*?)\?>/si", "promo::eval_print_buffer",$string);
		return preg_replace_callback("/(<\?php|<\?)(.*?)\?>/si", "promo::eval_buffer",$string);
	}

	static function eval_buffer($string) {
		ob_start();
		eval("$string[2];");
		$return = ob_get_contents();
		ob_end_clean();
		return $return;
	}
	
	static function eval_print_buffer($string) {
		ob_start();
		eval("print $string[2];");
		$return = ob_get_contents();
		ob_end_clean();
		return $return;
	}
}
?>