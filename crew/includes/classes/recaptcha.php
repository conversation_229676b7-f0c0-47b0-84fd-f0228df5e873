<?
require_once(DIR_WS_INCLUDES . 'addon/captcha/reCAPTCHA/recaptchalib.php');

class recaptcha {

	function login_captcha_html($theme = 1) {
        if ($theme == 1) {
            echo '	<script type="text/javascript">
                     var RecaptchaOptions = {
                        theme : \'clean\'
                     };
                     </script>';
        } else {
            echo '	<script type="text/javascript">
                     var RecaptchaOptions = {
                        theme : \'custom\',
                        custom_theme_widget: \'divrecaptcha\'
                     };
                    </script>
                    <div id="divrecaptcha" style="display:none;">    
                       <table>
                            <tr>
                                <td><div id="recaptcha_image"></div></td>
                                <td VALIGN="top">
                                    <div id="controls">
                                        <a href="javascript:void(0);" onclick="Recaptcha.reload();" ><img id="recaptcha_reload" width="25" height="18" alt="Get a new challenge" src="http://www.google.com/recaptcha/api/img/clean/refresh.png"></img></a> <br />  
                                        <a href="javascript:void(0);" onclick="javascript:Recaptcha.switch_type(\'audio\');" class="recaptcha_only_if_image" ><img id="recaptcha_switch_audio" width="25" height="15" alt="Get an audio challenge" src="http://www.google.com/recaptcha/api/img/clean/audio.png"></img></a>  
                                        <a href="javascript:void(0);" onclick="Recaptcha.switch_type(\'image\'); " class="recaptcha_only_if_audio" ><img id="recaptcha_switch_img" width="25" height="15" alt="Get a visual challenge" src="http://www.google.com/recaptcha/api/img/clean/text.png"></img></a> <br />  
                                        <a href="javascript:void(0);" onclick="Recaptcha.showhelp();" ><img id="recaptcha_whatsthis" width="25" height="16" src="http://www.google.com/recaptcha/api/img/clean/help.png" alt="Help"></img></a>  
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <input type="text" name="recaptcha_response_field" id="recaptcha_response_field" /> 
                                </td>    
                            </tr>
                        </table>
                    </div>   
                    <style type="text/css" >  
                    #divrecaptcha{  
                        width:365px;  
                        font-size:12px; font-family:Arial, Helvetica, sans-serif;  
                        border:1px solid #DFDFDF !important;
                        padding:5px;
                        background-color:white;
                    }  
                    #controls{ 
                        width:26px; 
                        padding:5px 10px;
                    }  
                    #recaptcha_image{  
                        padding:2px; background:#f9f9f9;  
                        border:1px solid #e0e0e0;  
                        height:57px !important;
                        width:302px !important;
                    }  
                    #recaptcha_response_field {  
                       border: 1px solid #3C3C3C !important; 
                       background-color:#FFF !important; 
                       width:308px;
                       padding:5px;
                       font-size:11pt;
                    }  
                    </style> ';
        }
		//$publickey = "6LeLxMUSAAAAALeZ0FW17GCUPzc5j1hzHhibYxsJ";
        $publickey = CAPTCHA_RECAPTCHA_PUBLIC_KEY;
        
		echo recaptcha_get_html($publickey);
	}
	
	function captcha_validation($challange, $response) {
		$result = false;
		//$privatekey = "6LeLxMUSAAAAAMxkVtw0_Jk2VlPL84EQeqjyxAHo";
        $privatekey = CAPTCHA_RECAPTCHA_PRIVATE_KEY;
        
		$resp = recaptcha_check_answer ($privatekey,
										tep_get_ip_address(),
										$challange,
										$response);

		if ($resp->is_valid) {
			$result = true;
		} else {
//			die ("The reCAPTCHA wasn't entered correctly. Go back and try it again." . "(reCAPTCHA said: " . $resp->error . ")");
			$result = false;
		}
		
		return $result;
	}
	
	function is_captcha_require($customer_email, $customer_current_login_ip) {
		$result = true;
		$customer_ip_info_select_sql = "SELECT clih.customers_login_ip
										FROM " . TABLE_CUSTOMERS_LOGIN_IP_HISTORY . " as clih
										INNER JOIN " . TABLE_CUSTOMERS . " as c
											ON (clih.customers_id = c.customers_id)
										WHERE c.customers_email_address = '" . tep_db_input($customer_email) . "'
										ORDER BY clih.customers_login_date DESC
										LIMIT 1";
		$customer_ip_info_result_sql = tep_db_query($customer_ip_info_select_sql);
		if ($customer_ip_info_row = tep_db_fetch_array($customer_ip_info_result_sql)) {
			$customer_previous_login_ip = $customer_ip_info_row['customers_login_ip'];
			$customer_previous_login_country = tep_get_ip_country_info($customer_previous_login_ip);
			$customer_currenct_login_country = tep_get_ip_country_info($customer_current_login_ip);

			if ($customer_previous_login_ip == $customer_current_login_ip) {
				$result = false;
			} else if (tep_not_null($customer_previous_login_country) && tep_not_null($customer_currenct_login_country)) {
				if($customer_previous_login_country['id'] == $customer_currenct_login_country['id']) {
					$result = false;
				}
			} 
			if ($_SESSION['login_trial'] > 2) {
				$result = true;
			}
		}
		
		return $result;
	}
}
?>