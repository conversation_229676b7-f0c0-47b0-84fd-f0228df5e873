<?php

class regional_setting {
    public static function saveCustomerPreference($customer_id) {
        global $language_code, $currency;
        
        $preferenceKey = array(
            'country' => $_SESSION['countries_iso_code_2'],
            'language' => $language_code,
            'currency' => $currency
        );

        foreach ($preferenceKey as $key => $value) {
            $select_sql = " SELECT customers_id
                            FROM " . TABLE_CUSTOMERS_PREFERENCE . "
                            WHERE customers_id='" . $customer_id . "'
                                AND preference_key = '" . $key . "'";
            $result_sql = tep_db_query($select_sql);
            if ($row = tep_db_fetch_array($result_sql)) {
                $data_array = array(
                    'value' => $value
                );
                tep_db_perform(TABLE_CUSTOMERS_PREFERENCE, $data_array, 'update', 'customers_id = "' . $customer_id . '" AND preference_key = "' . $key . '"');
            } else {
                $data_array = array(
                    'customers_id' => $customer_id,
                    'preference_key' => $key,
                    'value' => $value
                );
                tep_db_perform(TABLE_CUSTOMERS_PREFERENCE, $data_array);
            }
        }
    }

}
?>
