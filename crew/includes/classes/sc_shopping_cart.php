<?
/*
  	$Id: sc_shopping_cart.php,v 1.2 2013/05/31 09:35:11 sionghuat.chng Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2003 osCommerce
	
  	Released under the GNU General Public License
*/

class sc_shopping_cart extends shoppingCart {
	var $contents, $total, $weight, $cartID, $content_type;
	// mod indvship
	var $shiptotal;
	var $custom_product_index;
	// end indvship
	
	function sc_shopping_cart() {
		$this->reset();
	}
	
	function get_custom_product_content($product_id) {
		global $customer_id;
		
		$final = array();
      	$custom_type = $this->get_custom_prd_type($product_id);
      	
      	if (tep_not_null($custom_type)) {
			$final['custom'][$custom_type] = array();
			$temp = array();
			
			//select sc cart
			$sc_cart_select_sql = "	SELECT customers_sc_cart_quantity 
									FROM " . TABLE_CUSTOMERS_SC_CART . " AS csc 
									WHERE csc.customers_id = '" . (int)$customer_id . "' ";
			$sc_cart_result_sql = tep_db_query($sc_cart_select_sql);
			if ($sc_cart_row = tep_db_fetch_array($sc_cart_result_sql)) {
				$final['qty'] = $sc_cart_row['customers_sc_cart_quantity'];
			}
		}
		
		return $final;
	}
	
    function get_custom_prd_type($products_id) {
    	$custom_type = '';
    	if ((int)$products_id > 0) {
	    	$product_type_select_sql = "SELECT custom_products_type_id, products_bundle, products_bundle_dynamic 
						    			FROM " . TABLE_PRODUCTS . " 
						    			WHERE products_id = '" . tep_db_input($products_id) . "'";
	    	$product_type_result_sql = tep_db_query($product_type_select_sql);
	    	$product_type_row = tep_db_fetch_array($product_type_result_sql);
			
			if ($product_type_row['products_bundle'] != 'yes' && $product_type_row['products_bundle_dynamic'] != 'yes') {	// Single Product
				switch ($product_type_row['custom_products_type_id']) {
					case 0:
						$custom_type = 'game_currency';
						break;
					case 1:
						$custom_type = 'power_leveling';
						break;
					case 2:
						$custom_type = 'cdkey';
						break;
					case 3:
						$custom_type = 'store_credit';
						break;
				}
			} else {	// Package (Static or Dynamic)
				// NOTE: Assum package can only contain ONE product type
				$subproduct_type_select_sql = "	SELECT p.custom_products_type_id 
								    			FROM " . TABLE_PRODUCTS . " AS p 
												INNER JOIN " . TABLE_PRODUCTS_BUNDLES . " AS pb 
													ON p.products_id = pb.subproduct_id 
								    			WHERE pb.bundle_id = '" . tep_db_input($products_id) . "'
												LIMIT 1";
				$subproduct_type_result_sql = tep_db_query($subproduct_type_select_sql);
				$subproduct_type_row = tep_db_fetch_array($subproduct_type_result_sql);
				switch ($subproduct_type_row['custom_products_type_id']) {
					case 0:
						$custom_type = 'game_currency';
						break;
					case 1:
						$custom_type = 'power_leveling';
						break;
					case 2:
						$custom_type = 'cdkey';
						break;
					case 3:
						$custom_type = 'store_credit';
						break;
				}
			}
    	}
    	return $custom_type;
    }
	
    function restore_contents() {
		global $customer_id, $gv_id;
		
      	if (!tep_session_is_registered('customer_id')) return false;
		
		if (is_array($this->contents)) {
        	reset($this->contents);
        	while (list($products_id, ) = each($this->contents)) {
				
				$custom_type = $this->get_custom_prd_type($products_id);
				if (sizeof($this->contents[$products_id]['custom'][$custom_type]) > 0) {
					if ($custom_type == 'store_credit') {
						$qty = $this->contents[$products_id]['qty'];
						$product_query = tep_db_query("SELECT products_id FROM " . TABLE_CUSTOMERS_SC_CART . " WHERE customers_id = '" . (int)$customer_id . "' AND products_id = '" . tep_db_input($products_id) . "'");
	          			if ($product_row = tep_db_fetch_array($product_query) && $products_id == $product_row['products_id']) {
	          				$qty = (int)$qty;
	          				tep_db_query("UPDATE " . TABLE_CUSTOMERS_SC_CART . " SET customers_sc_cart_quantity = customers_sc_cart_quantity + $qty WHERE customers_id = '" . (int)$customer_id . "' AND products_id = '" . tep_db_input($products_id) . "'");
	          			} else {
	          				if (isset($product_row['products_id']) && $products_id != $product_row['products_id']) {
	          					tep_db_query("DELETE FROM " . TABLE_CUSTOMERS_SC_CART . " WHERE customers_id = '" . (int)$customer_id . "'");
	          				}
	          				$customers_sc_cart_data_sql = array('customers_id' => (int)$customer_id,
	          													'products_id' => (int)$products_id,
	          													'customers_sc_cart_quantity' => (int)$qty,
	          													'customers_sc_cart_date_added' => 'now()');
	          				tep_db_perform(TABLE_CUSTOMERS_SC_CART, $customers_sc_cart_data_sql);
	          			}
					}
				}
      		}
		}
		
		// reset per-session cart contents, but not the database contents
      	$this->reset(false);
		
		$products_select_sql = "SELECT products_id, customers_sc_cart_quantity 
								FROM " . TABLE_CUSTOMERS_SC_CART . " 
								WHERE customers_id = '" . (int)$customer_id . "'";
      	$products_query = tep_db_query($products_select_sql);
      	while ($products = tep_db_fetch_array($products_query)) {
			$tmp_array = array();
			$tmp_array = $this->get_custom_product_content($products['products_id']);
			
			if (is_array($tmp_array) && sizeof($tmp_array) > 0)	$this->contents[$products['products_id']] = $tmp_array;
      	}
		
      	$this->cleanup();
      	$this->calculate();
	}
	
    function reset($reset_database = false) {
    	global $customer_id;
		
      	$this->contents = array();
      	$this->total = 0;
      	$this->weight = 0;
      	$this->custom_product_index = -1;
		// mod indvship
      	$this->shiptotal = 0;
		// end indvship
      	$this->content_type = false;
		
      	if (tep_session_is_registered('customer_id') && ($reset_database == true)) {
			tep_db_query("DELETE FROM " . TABLE_CUSTOMERS_SC_CART . " WHERE customers_id = '" . (int)$customer_id . ";'");
      	}
		
      	unset($this->cartID);
      	//if (tep_session_is_registered('cartID')) tep_session_unregister('cartID');
	}
	
	function update_cart($products_id, $qty = '1', $attributes = '', $notify = true, $array = '', $bdn = '', $iscustom = false, $extra_info_array = '') {
      	global $new_products_id_in_cart, $customer_id;
      	
      	$products_id = tep_get_uprid($products_id, $attributes);
      	
      	$isincart = $this->in_cart($products_id);
      	
		if ($isincart && $iscustom == false) {
			
        	$this->update_quantity($products_id, $qty, $attributes, $extra_info_array);
        	
			if (tep_session_is_registered('customer_id')) {
				tep_db_query("UPDATE " . TABLE_CUSTOMERS_SC_CART . " SET customers_sc_cart_quantity = '" . $this->contents[$products_id]['qty'] . "' WHERE products_id = '" . $products_id . "' AND customers_id = '" . (int)$customer_id . "'");
        	} else {
        		$_SESSION['cart_update_error'] = TEXT_STOCK_NOT_AVAILABLE;
        	}
      	} else {
			if (!$iscustom) {	//Non-Custom products
				$this->contents[$products_id] = array('qty' => $qty);
				if (!isset($this->contents[$products_id]['custom']['game_currency'])) {
					$this->contents[$products_id]['custom']['game_currency'] = array();
				}
				
				if (tep_session_is_registered('customer_id')) {
					tep_db_query("INSERT INTO " . TABLE_CUSTOMERS_SC_CART . " (customers_id, products_id, customers_sc_cart_quantity, customers_sc_cart_date_added) VALUES ('" . (int)$customer_id . "', '" . tep_db_input($products_id) . "', '" . $qty . "', now())");
					
				}
			} else {	//Custom Products
				$insert_custom_data = false;
				
	      		$custom_type = $this->get_custom_prd_type($products_id);
				if (tep_session_is_registered('customer_id') && $insert_custom_data) {
					tep_db_query("INSERT INTO " . TABLE_CUSTOMERS_SC_CART . " (customers_id, products_id, customers_sc_cart_quantity, customers_sc_cart_date_added) VALUES ('" . (int)$customer_id . "', '" . tep_db_input($products_id) . "', '" . $qty . "', now())");
				}
				
				if (!is_array($this->contents[$products_id]['custom'][$custom_type]))
					$this->contents[$products_id]['custom'][$custom_type] = array();
				
				if ($this->custom_product_index == -1) {
					array_push($this->contents[$products_id]['custom'][$custom_type], array('content' => $this->custom_product_content));
				} else {
					$this->contents[$products_id]['custom'][$custom_type][$this->custom_product_index] = array('content' => $this->custom_product_content);
				}				
			}
			
			unset($this->custom_product_content);
      	}
   		
      	unset($isincart);
      	$this->cleanup();	// IMPORTANT: This Will remove those product has < 1 in cart qty
		
		// assign a temporary unique ID to the order contents to prevent hack attempts during the checkout procedure
		$this->calculate();
	}
	
	function add_cart($products_id, $qty = '1', $attributes = '', $notify = true, $array = '', $bdn = '', $iscustom = false, $extra_info_array = '') {
      	global $new_products_id_in_cart, $customer_id;
      	
      	$products_id = tep_get_uprid($products_id, $attributes);
      	
      	$prod_cat_path = tep_get_product_path($products_id, true);
		if (tep_not_null($prod_cat_path)) {
			$prod_cat_path_array = explode('_', $prod_cat_path);
			$game_id = $prod_cat_path_array[0];
		} else {
			$game_id = 0;
		}
		
   		$custom_type = $this->get_custom_prd_type($products_id);
		
      	if ($notify == true) {
      		//	This newly added item will be highlighted in shopping cart summary box
        	$new_products_id_in_cart = $products_id;
        	tep_session_register('new_products_id_in_cart');
      	}
      	
      	$isincart = $this->in_cart($products_id);
      	
      	if (isset($extra_info_array['char_name']))	$extra_info_array['char_name'] = tep_db_prepare_input($extra_info_array['char_name']);
		if (isset($extra_info_array['char_account_name']))	$extra_info_array['char_account_name'] = tep_db_prepare_input($extra_info_array['char_account_name']);
		if (isset($extra_info_array['char_account_pwd']))	$extra_info_array['char_account_pwd'] = tep_db_prepare_input($extra_info_array['char_account_pwd']);
		if (isset($extra_info_array['char_wow_account']))	$extra_info_array['char_wow_account'] = tep_db_prepare_input($extra_info_array['char_wow_account']);
		
		if ($isincart && $iscustom == false) {
			$new_quantity = $qty;
			
			if (isset($this->contents[$products_id]['custom']['game_currency'])) {
				for ($char_cnt = 0; $char_cnt < sizeof($this->contents[$products_id]['custom']['game_currency']); $char_cnt++) {
					if ($this->contents[$products_id]['custom']['game_currency'][$char_cnt]['char_name'] == trim($extra_info_array['char_name'])) {
						$new_quantity += $this->contents[$products_id]['custom']['game_currency'][$char_cnt]['qty'];
					}
				}
			} else if (isset($this->contents[$products_id]['custom']['cdkey'])) {
				$new_quantity += $this->contents[$products_id]['custom']['cdkey'][0]['qty'];
			} else if (isset($this->contents[$products_id]['custom']['store_credit'])) {
				$new_quantity += $this->contents[$products_id]['custom']['store_credit'][0]['qty'];
			}
			
			$this->update_cart($products_id, $new_quantity, $attributes, $notify, $array, $bdn, $iscustom, $extra_info_array);
      	} else {
			if (!$iscustom) {	//Non-Custom products

				$this->contents[$products_id] = array('qty' => $qty);
				if (!isset($this->contents[$products_id]['custom'][$custom_type])) {
					$this->contents[$products_id]['custom'][$custom_type] = array();
				}
				
				if ($custom_type == 'game_currency') {
					$game_currency_info_array = array(	'qty' => $qty,
														'char_name' => trim($extra_info_array['char_name']),
														'delivery_mode' => $extra_info_array['delivery_mode']
													 );
					
					if ($extra_info_array['delivery_mode'] == '1') {
						//$game_currency_info_array['char_online_time'] = $extra_info_array['char_online_time'];
						//$game_currency_info_array['char_online_dur'] = $extra_info_array['char_online_dur'];
					} else if ($extra_info_array['delivery_mode'] == '2') {
						$game_currency_info_array['char_account_name'] = $extra_info_array['char_account_name'];
						$game_currency_info_array['char_account_pwd'] = $extra_info_array['char_account_pwd'];
						$game_currency_info_array['char_wow_account'] = $extra_info_array['char_wow_account'];
					}
					
					$this->contents[$products_id]['custom'][$custom_type][] = $game_currency_info_array;
				} else if ($custom_type == 'cdkey') {
					$this->contents[$products_id]['custom'][$custom_type][] = array('qty' => $qty);
				} else if ($custom_type == 'store_credit') {
					$this->contents[$products_id]['custom'][$custom_type][] = array('qty' => $qty);
				}

	        	if (tep_session_is_registered('customer_id')) {
					tep_db_query("INSERT INTO " . TABLE_CUSTOMERS_SC_CART . " (customers_id, products_id, customers_sc_cart_quantity, customers_sc_cart_date_added) VALUES ('" . (int)$customer_id . "', '" . tep_db_input($products_id) . "', '" . $qty . "', now())");
				}
			} else {	//Custom Products
				$insert_custom_data = false;
				
	      		// $custom_type = $this->get_custom_prd_type($products_id);	// Commented out as function call before this
				switch ($custom_type) {
					case 'store_credit':
						if ($isincart) {
							$qty += $this->contents[$products_id]['qty'];
							$this->update_quantity($products_id, $qty);
						} else {
							$this->contents[$products_id] = array('qty' => $qty);
							$insert_custom_data = true;
						}
						break;
				}
				
				if (!is_array($this->contents[$products_id]['custom'][$custom_type]))
					$this->contents[$products_id]['custom'][$custom_type] = array();
				
				if ($custom_type == 'power_leveling') {
					if ($this->custom_product_index == -1) {
						array_push($this->contents[$products_id]['custom'][$custom_type], array('content' => $this->custom_product_content));
					} else {
						$this->contents[$products_id]['custom'][$custom_type][$this->custom_product_index] = array('content' => $this->custom_product_content);
					}
				} else if ($custom_type == 'store_credit') {
					$this->contents[$products_id]['custom'][$custom_type][0] = array('qty' => $qty);
				} else {	// CD Key atm
					$this->contents[$products_id]['custom'][$custom_type][0] = array('qty' => $qty);
				}
			}
			
			unset($this->custom_product_content);
			
        	if (is_array($attributes)) {
          		reset($attributes);
          		while (list($option, $value) = each($attributes)) {
            		$this->contents[$products_id]['attributes'][$option] = $value;
          		}
        	}
      	}
   		
      	unset($isincart);
      	$this->cleanup();	// IMPORTANT: This Will remove those product has < 1 in cart qty
		$this->calculate();
		// assign a temporary unique ID to the order contents to prevent hack attempts during the checkout procedure
	}
	
	function set_custom_product_index($index=-1) {
		$this->custom_product_index = $index;	
	}
	
    function update_quantity($products_id, $quantity = '', $attributes = '', $extra_info_array = '') {
    	global $customer_id;
		
		$temp_qty = (int)$quantity;
		$char_exist =  false;
		$char_exist_pos = 0;
		
      	if ($quantity == '') {
      		return true; // nothing needs to be updated if theres no quantity, so we return true..
      	}
		
		$custom_type = $this->get_custom_prd_type($products_id);
		
		if ($custom_type == 'store_credit') {
			$this->contents[$products_id]['custom'][$custom_type][0] = array('qty' => $temp_qty);
		}
		
		if ($temp_qty == 0) {
      		$this->remove($products_id);
      		return true;
      	}
      	
      	$this->contents[$products_id]['qty'] = $temp_qty;
      	
		// update database
      	if (tep_session_is_registered('customer_id')) tep_db_query("UPDATE " . TABLE_CUSTOMERS_SC_CART . " SET customers_sc_cart_quantity = '" . $quantity . "' WHERE customers_id = '" . (int)$customer_id . "' AND products_id = '" . tep_db_input($products_id) . "'");
	}
	
    function cleanup() {
    	global $customer_id, $customers_groups_id;
    	
      	reset($this->contents);
      	
      	while (list($key,) = each($this->contents)) {
      		$remove_this_product = false;
      		
      		if ($this->get_product_type($key) == 'custom') {
      			if (!isset($this->contents[$key]['custom']) || sizeof($this->contents[$key]['custom']) == 0) {
      				$remove_this_product = true;
      			}
      		} else {
      			if (sizeof($this->contents[$key]['custom']) > 0 
					&& !isset($this->contents[$key]['custom']['game_currency'])
					&& !isset($this->contents[$key]['custom']['store_credit']) 
					&& !isset($this->contents[$key]['custom']['cdkey'])) {	// It should not be a custom info, so kill it

      				$remove_this_product = true;
      			}
      		}
      		
			if (isset($this->contents[$key]['qty']) && $this->contents[$key]['qty'] < 1) {
        		$remove_this_product = true;
        	}
        	
        	if (!$remove_this_product) {	// If decided to keep this product in cart, check if this product still active (both product and categories status)
	        	$p_cPath = tep_get_product_path($key, true);
	        	$p_cPath_array = tep_parse_category_path($p_cPath);
	        	
	        	$active_product = true;
	        	$permitted_product = true;
				if (is_array($p_cPath_array) && count($p_cPath_array)) {
					// Check for inactive product
				  	$inactive_cat_select_sql = "SELECT COUNT(categories_id) AS inactive_cat 
				  								FROM " . TABLE_CATEGORIES . "
				  								WHERE categories_id IN ('" . implode("', '", $p_cPath_array) . "') 
				  									AND categories_status = 0";
				  	$inactive_cat_result_sql = tep_db_query($inactive_cat_select_sql);
					$inactive_cat_row = tep_db_fetch_array($inactive_cat_result_sql);
				  	
				  	if ($inactive_cat_row['inactive_cat'] > 0)	{$active_product = false;}
				  	
				  	// Check for product access permission
				  	$permitted_cat_select_sql = "	SELECT COUNT(linkid) AS permitted_cat 
				  									FROM " . TABLE_CATEGORIES_GROUPS . "
				  									WHERE categories_id IN ('" . implode("', '", $p_cPath_array) . "') 
				  										AND ((groups_id = '".(int)$customers_groups_id."') or (groups_id=0)) ";
				  	$permitted_cat_result_sql = tep_db_query($permitted_cat_select_sql);
					$permitted_cat_row = tep_db_fetch_array($permitted_cat_result_sql);
					
				  	if ($permitted_cat_row['permitted_cat'] != count($p_cPath_array))	{$permitted_product = false;}
				}
				
				if ($active_product) {
					$product_status_select_sql = "	SELECT products_status 
				  									FROM " . TABLE_PRODUCTS . " 
				  									WHERE products_id = '" . tep_db_input($key) . "'";
					$product_status_result_sql = tep_db_query($product_status_select_sql);
					$product_status_row = tep_db_fetch_array($product_status_result_sql);
					
					if ($product_status_row['products_status'] != '1') {
						$active_product = false;
					}
				}
				
				if ($active_product) {
					$products_display_select_sql = "	SELECT products_display, products_bundle, products_bundle_dynamic 
					  									FROM " . TABLE_PRODUCTS . " 
					  									WHERE products_id = '" . tep_db_input($key) . "'";
					$products_display_result_sql = tep_db_query($products_display_select_sql);
					$products_display_row = tep_db_fetch_array($products_display_result_sql);
					
					if ($products_display_row['products_display'] != '1' && $products_display_row['products_bundle'] == '' && $products_display_row['products_bundle_dynamic'] == '') {
						$active_product = false;
					}
				}
				
				if (!$active_product || !$permitted_product) {	// Remove from cart if it is not active / not accessible at the moment
					$remove_this_product = true;
				}
			}
			
        	if ($remove_this_product) {
        		unset($this->contents[$key]);
				// remove from database
          		if (tep_session_is_registered('customer_id')) {
					tep_db_query("DELETE FROM " . TABLE_CUSTOMERS_SC_CART . " WHERE customers_id = '" . (int)$customer_id . "' AND products_id = '" . tep_db_input($key) . "'");
          		}
        	}
      	}
    }
	
    function remove($products_id, $custom_basket_id = -1) {
    	global $customer_id;
    	
    	$remove_from_cart = true;
    	$basket_id = 0;
    	$custom_type = $this->get_custom_prd_type($products_id);
    	
		$this->arrange_custom_product($products_id);
      	if (tep_session_is_registered('customer_id') && $remove_from_cart) {
		   	tep_db_query("DELETE FROM " . TABLE_CUSTOMERS_SC_CART . " WHERE customers_id = '" . (int)$customer_id . "' AND products_id = '" . tep_db_input($products_id) . "'");
      	}
      	
		$this->calculate();
	}
	
    function remove_all() {
    	$this->reset();
    }
	
    function get_product_id_list()
    {
    	$product_id_list = '';
      	if (is_array($this->contents)) {
        	reset($this->contents);
        	while (list($products_id, ) = each($this->contents)) {
          		$product_id_list .= ', ' . $products_id;
        	}
      	}
		
      	return substr($product_id_list, 2);
	}
	
	function calculate() {
		global $customer_id, $currency, $currencies;
		
		$this->total_virtual = 0; // ICW Gift Voucher System
	  	$this->total = 0;
	  	$this->weight = 0;
		// mod indvship
	  	$this->shiptotal = 0;
		// end indvship
	  	if (!is_array($this->contents)) return 0;
		
		//CGDiscountSpecials start
		/*************************************************************
			Get the customer total discount! Is based on customer not
			product so is not wise to put it in the while loop!!
		*************************************************************/
		$customer_ind_discount = 0;
		
		$prod_quantity_array = array();
	  	$pre_order_array = array();
	  	$custom_prod_total = 0;
	  	reset($this->contents);
	  	$temp_contents = $this->contents;
	  	
		foreach ($this->contents as $custom_content) {
			$products_id = key($temp_contents);
			$product_exists = false;
			
			$prod_count_sql = "	SELECT count(products_id) as counted 
								FROM " . TABLE_PRODUCTS . " 
								WHERE products_id = '" . $products_id . "'";
			$prod_count_result = tep_db_query($prod_count_sql);
			if ($row = tep_db_fetch_array($prod_count_result)) {
				if ((int)$row['counted'] > 0) {
					$product_exists = true;
				} else {
					$product_exists = false;
				}
			}	
			
			if ($product_exists) {
				//
			} else {
				unset($this->contents[$products_id]);
			}
			
			next($temp_contents);
		}
	  	
		unset($temp_contents);
	  	reset($this->contents);
	  	
	  	while (list($products_id, ) = each($this->contents)) {
			$pre_order = false;
			$qty = $this->contents[$products_id]['qty'];
			
			$products_sql = "	SELECT products_bundle, products_bundle_dynamic, products_weight 
								FROM " . TABLE_PRODUCTS . " 
								WHERE products_id = '" . (int)$products_id . "'";
			$products_query = tep_db_query($products_sql);
			if ($products = tep_db_fetch_array($products_query)) {
				$prod_status_select_sql = "	SELECT products_status 
											FROM " . TABLE_PRODUCTS . " 
											WHERE products_id = '" . (int)$products_id . "'";
				$prod_status_result_sql = tep_db_query($prod_status_select_sql);
				if ($prod_status_row = tep_db_fetch_array($prod_status_result_sql)) {
					if (!$prod_status_row["products_status"]) {
						$pre_order = false;
					} else {
						$prod_quantity_array[$products_id] += $qty;
						
						$stock_status = tep_check_stock_status($products_id, $prod_quantity_array[$products_id]);
						if ($stock_status == "pre-order") {
							$pre_order = true;
						}
					}
				}
				$this->weight += ($qty * $products["products_weight"]);
			}
			if ($pre_order)	 $pre_order_array[] = $products_id;
		}
		
		reset($this->contents);
		$cnt = 1;
		//$cnt_bundle = count($bundleV_array);
		while (list($products_id, ) = each($this->contents)) {
			$qty = $this->contents[$products_id]['qty'];
			
			// products price
			$product_sql = "SELECT products_id, products_price, products_ship_price, products_tax_class_id, products_weight 
							FROM " . TABLE_PRODUCTS . " 
							WHERE products_id = '" . (int)$products_id . "'";
			$product_query = tep_db_query($product_sql);
			if ($product = tep_db_fetch_array($product_query)) {
				// Merging store changes: Get the cat defined configuration settings
				$cat_cfg_array = tep_get_cfg_setting($products_id, 'product', 'PRE_ORDER_DISCOUNT');
				
				// ICW ORDER TOTAL CREDIT CLASS Start Amendment
	  			$no_count = 1;
	  			$gv_sql = "	SELECT products_model 
				  			FROM " . TABLE_PRODUCTS . " 
				  			WHERE products_id = '" . (int)$products_id . "'";
	  			$gv_query = tep_db_query($gv_sql);
	  			$gv_result = tep_db_fetch_array($gv_query);
	  			if (ereg_dep('^GIFT', $gv_result['products_model'])) {
	    			$no_count = 0;
	  			}
				// ICW ORDER TOTAL  CREDIT CLASS End Amendment
	  			$prid = $product['products_id'];
				
	  			$products_tax = tep_get_tax_rate($product['products_tax_class_id']);
	  			$products_price = $product['products_price'];
				$products_weight = $product['products_weight'];
				
				// pre-order discount
	      		if (in_array($products_id, $pre_order_array) && abs($cat_cfg_array['PRE_ORDER_DISCOUNT'])) {
	  				$pre_order_discount = $cat_cfg_array['PRE_ORDER_DISCOUNT'];
	  			} else {
	  				$pre_order_discount = 0;
	  			}
	  			
	  			$product_prices_info_array = $currencies->get_product_prices_info($products_id);
				$products_price = $currencies->get_product_price($products_id, $customer_id, $pre_order_discount, $product_prices_info_array['base_cur']);
				
				//CGDiscountSpecials end
				// mod indvship
				$products_ship_price = $product['products_ship_price'];
				// end indvship
				$num = ($cnt == 1) ? 0 : 1 ;
				
				if ($products_id == $this->pm_after_id) {
					//echo $num ."===". $cnt ."===". $qty;
	  	  			if ($cnt >= 1 && $qty >1) {
						if ($qty > $num) {	
							if ($this->pm_free) {
								$qty = $qty - 1;
							}
						}
					} else if ($cnt > 1 && $qty > 0) {
						if ($qty >= $num) {
							if ($this->pm_free) {
								$qty = $qty - 1;
							}
						}
					} else if ($cnt >= 1 && $qty > 0) {
						if ($qty >= $num) {
							if ($this->pm_free) {
								$qty = $qty - 1;
							}
						}
					}
				}
				
				$this->total += $products_price * $qty;
				
				// mod indvship
				$this->shiptotal += ($products_ship_price * $qty);
				// end indvship
				//$this->weight += ($qty * $products_weight);
			}
			
			$cnt++;
			
			// attributes price
			if (isset($this->contents[$products_id]['attributes'])) {
	  			reset($this->contents[$products_id]['attributes']);
	  			while (list($option, $value) = each($this->contents[$products_id]['attributes'])) {
	  				$attribute_price_sql = "SELECT options_values_price, price_prefix 
	  										FROM " . TABLE_PRODUCTS_ATTRIBUTES . " 
	  										WHERE products_id = '" . (int)$prid . "' 
	  											AND options_id = '" . (int)$option . "' 
	  											AND options_values_id = '" . (int)$value . "'";
	    			$attribute_price_query = tep_db_query($attribute_price_sql);
	    			$attribute_price = tep_db_fetch_array($attribute_price_query);
	            	
	   				$attribute_price['options_values_price'] = $attribute_price['options_values_price'] + $attribute_price['options_values_price'] * abs(0) / 100;
	   				
	    			if ($attribute_price['price_prefix'] == '+') {
	      				$this->total += $qty * tep_add_tax($attribute_price['options_values_price'], $products_tax);
	    			} else {
	      				$this->total -= $qty * tep_add_tax($attribute_price['options_values_price'], $products_tax);
	    			}
	  			}
			}
		}
	}
	
	function checkout_permission() {
		global $customer_id;
		
		$products = $this->get_products();
		
		if (!count($products)) return false;
		$checked_products = array();
		
		for ($i=0; $i<count($products); $i++) {
			$prod_bd_query = tep_db_query("SELECT products_bundle, products_bundle_dynamic FROM " . TABLE_PRODUCTS . " WHERE products_id = " . $products[$i]['id'] . " ORDER BY products_id");
	  		$prod_bd = tep_db_fetch_array($prod_bd_query);
  	  		$prod_bundle = $prod_bd['products_bundle'];
  	  		$prod_bundle_dynamic = $prod_bd['products_bundle_dynamic'];
  	  		
  	  		if (STOCK_CHECK == 'true') {
      			$prod_inventory_select_sql = "SELECT products_quantity, products_status FROM " . TABLE_PRODUCTS . " WHERE products_id = '" . $products[$i]['id'] . "'";
                $prod_inventory_result_sql = tep_db_query($prod_inventory_select_sql);
				
				if ($prod_inventory_row = tep_db_fetch_array($prod_inventory_result_sql)) {
					if (!$prod_inventory_row["products_status"]) {
						return false;
						break;
					}
					
					$skip_stock_checking = tep_single_product_skip_stock_check($products[$i]['id']);
					
					if ($skip_stock_checking['state'] == -1) {
						return false;
						break;
					} else if ($skip_stock_checking['state'] == 0) {
						$current_qty_in_cart = tep_get_total_product_in_cart($customer_id, $products[$i]['id']);
						// check with weichen this checking, (int)$skip_stock_checking['stock_level'] > 0 should be here or not, added by boon hock
						if ((int)$skip_stock_checking['stock_level'] > 0 && ($prod_inventory_row["products_quantity"] - $current_qty_in_cart) < $skip_stock_checking['stock_level']) {
          					return false;
							break;
						} else {
							$checked_products[] = $bundle_data["subproduct_id"];
						}
					}
				}
	      	}
    	}
    	return true;
	}
	
    function get_products() {
    	global $languages_id, $pm_status, $pm_period, $pm_date_from, $pm_date_to, $pm_value, $customer_id, $currencies, $currency;
		
      	if (!is_array($this->contents)) return false;
      	
      	$query = tep_db_query("select customers_discount from " . TABLE_CUSTOMERS . " where customers_id =  '" . $customer_id . "'");
  		$query_result = tep_db_fetch_array($query);
  		$customer_ind_discount = $query_result['customers_discount'];
  		
      	$this->cleanup();	// Get the latest "valid" product since product's type might get changed during customer doing shopping
      	
      	$prod_quantity_array = array();
      	$pre_order_array = array();
      	reset($this->contents);
      	while (list($products_id, ) = each($this->contents)) {
      		$pre_order = false;
        	$qty = $this->contents[$products_id]['qty'];
      		
      		$prod_status_select_sql = "	SELECT products_status 
										FROM " . TABLE_PRODUCTS . " 
										WHERE products_id = '" . (int)$products_id . "'";
			$prod_status_result_sql = tep_db_query($prod_status_select_sql);
			if ($prod_status_row = tep_db_fetch_array($prod_status_result_sql)) {
				if (!$prod_status_row["products_status"]) {
					$pre_order = false;
				} else {
					$prod_quantity_array[$products_id] += $qty;
					
			    	$stock_status = tep_check_stock_status($products_id, $prod_quantity_array[$products_id]);
					if ($stock_status == "pre-order") {
						$pre_order = true;
					}
			    }
			}
			if ($pre_order)	 $pre_order_array[] = $products_id;
		}
		
      	$products_array = array();
      	$pm_free = 0;
     	
     	if (tep_session_is_registered('customer_id')) {
      		if(STORE_PROMOTION == 'true'){
      			//if($pm_status == true){
		    	reset($this->contents);
		    	$cnt_pro = 0;
				while (list($products_id, ) = each($this->contents)) {
					$cnt_pro++;
					$qty = $this->contents[$products_id]['qty'];
					
					$pro_select_sql = " select p.products_id, p.products_model, p.products_price, p.products_weight, p.products_tax_class_id 
										from " . TABLE_PRODUCTS . " p
										where p.products_id = '" . (int)$products_id . "'";
					$pro_query = tep_db_query($pro_select_sql);
					if ($prod = tep_db_fetch_array($pro_query)) {
						$prod_price = $prod['products_price'];
						$prod_id = $prod['products_id'];
						$prod_price_normal =  $prod_price;		// normal price before discount
						
						// Merging store changes: Get the cat defined configuration settings
	        			$cat_cfg_array = tep_get_cfg_setting($prod_id, 'product', 'PRE_ORDER_DISCOUNT');
	        			
	        			// pre-order discount
		          		if (in_array($products_id, $pre_order_array) && abs($cat_cfg_array['PRE_ORDER_DISCOUNT'])) {
		      				$pre_order_discount = $cat_cfg_array['PRE_ORDER_DISCOUNT'];
		      			} else {
		      				$pre_order_discount = 0;
		      			}
		      			
	        			$prod_price = $currencies->get_product_price($prod_id, $customer_id, $pre_order_discount, $products['products_base_currency']);
			          	
						if(!$ntotal) {
							$ntotal = ($prod_price * $qty);		// price after discount
						} else {
							$ntotal = $ntotal + ($prod_price * $qty);
						}
						
						if ($price_cheap) { //2nd onward product go here
							$pm_free = 1;
							$this->pm_free = $pm_free;
							
							if($price_cheap >= $prod_price){
								//get now price
								$pm_after_id = $prod_id;
								$this->pm_after_id = $pm_after_id;
								$pm_after_price =  $prod_price;
								$price_cheap = $prod_price; //pass the cheapest value to compare next item
							} else {
								//get prev price
								$pm_after_price =  $price_cheap;	
							}
						} else { //first product go here
							$price_cheap = $prod_price;
							$pm_after_price =  $prod_price;
							$pm_after_id = $prod_id;
							$this->pm_after_id = $pm_after_id;
							
							if($qty > 1){ 
								$pm_free = 1;
								$this->pm_free = $pm_free;
								$qty = $qty - 1;
							}
						}
					}
				}
      		}
        	
			if($pm_free) {
				$ntotal = $ntotal - $pm_after_price;
				
				if($ntotal >= ($pm_value + 0)){
					//echo "PM is valid!";
					$pm_free = 1;
					$this->pm_free = $pm_free;
				} else {
					$pm_free = 0;
					$this->pm_free = $pm_free;	
				}
			}
      	//}
      	}
		
		$promotions_query = tep_db_query("select promotions_id, promotions_title, promotions_description, promotions_from, promotions_to, promotions_min_value from promotions where promotions_status = '1' order by promotions_id");
  		$promotions = tep_db_fetch_array($promotions_query);
		if ($promotions['promotions_id']) {
	    	$pm_date_from = $promotions['promotions_from'];
			$pm_date_to = $promotions['promotions_to'];
			$pm_value = $promotions['promotions_min_value'];
			
			$pm_todate = split("[/]", date('m/d/Y'));
		  	if($pm_todate[2])
		  		$pmtoday = mktime(0, 0, 0, $pm_todate[0], $pm_todate[1] ,$pm_todate[2] );
			
			$pm_dtfrom = split("[/]", tep_date_short($pm_date_from));
		  	if($pm_dtfrom[2])
		  		$pmfrom = mktime(0, 0, 0, $pm_dtfrom[0], $pm_dtfrom[1] ,$pm_dtfrom[2] );
		  	
		  	$pm_dtto = split("[/]", tep_date_short($pm_date_to));
		  	if($pm_dtto[2]) {
		  	   	$pmto = mktime(0, 0, 0, $pm_dtto[0], $pm_dtto[1] ,$pm_dtto[2] );
		  	    
		  	    if($pm_free) {
		  	    	if($pmfrom <= $pmtoday && $pmtoday <= $pmto){
		  	   			//echo "PM start n end date";
		  	   			$pm_free = 1;
						
						if($ntotal >= ($pm_value + 0)){
							//echo "PM is valid!";
							$pm_free = 1;
							$this->pm_free = $pm_free;
						} else {
							$pm_free = 0;
							$this->pm_free = $pm_free;	
						}
		  	   		} else {
		  	   			if($pmtoday >= $pmto && $pmfrom <= $pmtoday){
		  	   				//echo "PM already end date!";	
							$pm_free = 0;
							$this->pm_free = $pm_free;
		  	   			}
		  	   		}
		  	    } else {
		  	    	$pm_free = 0;
					$this->pm_free = $pm_free;
				}
			}
		} else {
		    $pm_free = 0;
		    $this->pm_free = $pm_free;
		}   
      	
      	reset($this->contents);
      	//$cnt_bundle = count($bundleV_array);
      	$cnt = 0;
      	while (list($products_id, ) = each($this->contents)) {
      		$pre_order = false;
        	$qty = $this->contents[$products_id]['qty'];
        	
        	$prod_cat_path = tep_get_product_path($products_id, true);
			if (tep_not_null($prod_cat_path)) {
				$prod_cat_path_array = explode('_', $prod_cat_path);
				$game_id = $prod_cat_path_array[0];
			} else {
				$game_id = 0;
			}
			
			$products_select_sql = "SELECT p.products_id, p.products_model, pd.products_image, p.products_price, p.products_base_currency, p.products_bundle, p.products_bundle_dynamic, p.products_weight, p.products_tax_class_id,p.custom_products_type_id 
									FROM " . TABLE_PRODUCTS . " p
									INNER JOIN " . TABLE_PRODUCTS_DESCRIPTION . " pd 
										ON p.products_id=pd.products_id
									WHERE p.products_id = '" . (int)$products_id . "'
										AND pd.language_id = '1'";
      		
      		$products_query = tep_db_query($products_select_sql);
        	if ($products = tep_db_fetch_array($products_query)) {
        		$prod_applied_discount = array();	// Must be in correct ordering
          		$prid = $products['products_id'];
          		$products_name = tep_get_products_name($products_id);
          		$products_price = $products['products_price'];
          		$product_price_normal =  $products_price;
	          	
          		// Merging store changes: Get the cat defined configuration settings
    			$cat_cfg_array = tep_get_cfg_setting($products_id, 'product', 'PRE_ORDER_DISCOUNT');
    			
    			// Merging store changes: Get the customer group discount
	    		$customers_groups_info_array = tep_get_customer_group_discount($customer_id, $products_id, 'product');
				$customers_groups_discount = $customers_groups_info_array['cust_group_discount'];
				$customer_discount = $customer_ind_discount + $customers_groups_discount;
				
    			// pre-order discount
          		if (in_array($products_id, $pre_order_array) && abs($cat_cfg_array['PRE_ORDER_DISCOUNT'])) {
      				$pre_order_discount = $cat_cfg_array['PRE_ORDER_DISCOUNT'];
      			} else {
      				$pre_order_discount = 0;
      			}
      			
				$product_price_array = $currencies->get_product_prices_info($products_id);
				$store_credit_currency_code = tep_get_customer_store_credit_currency($customer_id, false);
				
				$products_price = $product_price_array['price'];
				if ($store_credit_currency_code != $currency) {
					$products_price = $currencies->advance_currency_conversion($products_price, $store_credit_currency_code, $currency, false, 'buy');
				}
				
				if (abs($pre_order_discount) > 0)	$prod_applied_discount[] = $pre_order_discount;
	          	if (abs($customer_discount) > 0)	$prod_applied_discount[] = $customer_discount;
	          	
   				if ($pm_free == 1 && (int)$products['custom_products_type_id']==0) { //if promotion
   					if ($pm_after_id == $products_id) {
		          		$products_array[] = array(	'id' => $products_id,
		                                    		'name' => $products_name,
				                                    'model' => $products['products_model'],
				                                    'image' => $products['products_image'],
				                                    'price' => $products_price,
				                                    'base_currency' => $products['products_base_currency'],
				                                    'quantity' => $this->contents[$products_id]['qty'],
				                                    'weight' => $products['products_weight'],
				                                    'final_price' => ($products_price + $this->attributes_price($products_id)),
				                                    'pm_price' => 'FREE',
				                                    'pm_total' => $product_price_normal,
				                                    'tax_class_id' => $products['products_tax_class_id'],
				                                    'normal_price' => $product_price_normal,
				                                    'discounts' => $prod_applied_discount,
				                                    'products_categories_id' => $game_id,
				                                    'attributes' => (isset($this->contents[$products_id]['attributes']) ? $this->contents[$products_id]['attributes'] : ''),
				                                    'pre_order' => (in_array($products_id, $pre_order_array) ? 1 : 0),
													'custom_products_type_id' => (int)$products['custom_products_type_id'],
													'custom_content' => array()
												);
			    	} else {
				  		$products_array[] = array(	'id' => $products_id,
				                                    'name' => $products_name,
				                                    'model' => $products['products_model'],
				                                    'image' => $products['products_image'],
				                                    'price' => $products_price,
				                                    'base_currency' => $products['products_base_currency'],
				                                    'quantity' => $this->contents[$products_id]['qty'],
				                                    'weight' => $products['products_weight'],
				                                    'final_price' => $product_price_normal,
				                                    'pm_price' => '',
				                                    'pm_total' => $ntotal,
				                                    'tax_class_id' => $products['products_tax_class_id'],
				                                    'normal_price' => $product_price_normal,
				                                    'discounts' => $prod_applied_discount,
				                                    'products_categories_id' => $game_id,
				                                    'attributes' => (isset($this->contents[$products_id]['attributes']) ? $this->contents[$products_id]['attributes'] : ''),
				                                    'pre_order' => (in_array($products_id, $pre_order_array) ? 1 : 0),
													'custom_products_type_id' => (int)$products['custom_products_type_id'],
													'custom_content' => array()
												);
					}
		  		} else {
		  			//	This is TOTALLY NOT Promotion
					$final_price = 0;
					if ((int)$products['custom_products_type_id'] == 0) {
						echo "_d_";
						$final_price = $products_price + $this->attributes_price($products_id);
						$final_qty = (int)$this->contents[$products_id]['qty'];
						
						$custom_type = $this->get_custom_prd_type($products_id);
						$session_array = $this->contents[$products_id]['custom'][$custom_type];
						
						if ($products['products_bundle'] == 'yes') {
							$products['custom_products_type_id'] = tep_get_custom_product_type($products_id);
						}
						
						$products_array[] = array(	'id' => $products_id,
				                                    'name' => $products_name,
				                                    'model' => $products['products_model'],
				                                    'image' => $products['products_image'],
				                                    'price' => $products_price,
				                                    'quantity' => $final_qty,
				                                    'weight' => $products['products_weight'],
				                                    'final_price' => $product_price_normal,
				                                    'tax_class_id' => $products['products_tax_class_id'],
				                                    'normal_price' => $product_price_normal,
				                                    'products_categories_id' => $game_id,
				                                    'pm_price' => '',
				                                    'pm_total' => $ntotal,
				                                    'discounts' => $prod_applied_discount,
				                                    'attributes' => (isset($this->contents[$products_id]['attributes']) ? $this->contents[$products_id]['attributes'] : ''),
				                                    'pre_order' => (in_array($products_id, $pre_order_array) ? 1 : 0),
													'custom_products_type_id' => (int)$products['custom_products_type_id'],
													'custom_content' => (isset($this->contents[$products_id]['custom'][$custom_type]) ? $this->contents[$products_id]['custom'][$custom_type] : array()),
													'products_bundle' => $products['products_bundle']
												);
					} else if ((int)$products['custom_products_type_id'] == 2) {
						$final_price = $products_price + $this->attributes_price($products_id);
						$final_qty = (int)$this->contents[$products_id]['qty'];
		  				
						$products_array[] = array(	'id' => $products_id,
				                                    'name' => $products_name,
				                                    'model' => $products['products_model'],
				                                    'image' => $products['products_image'],
				                                    'price' => $products_price,
				                                    'base_currency' => $products['products_base_currency'],
				                                    'quantity' => $final_qty,
				                                    'weight' => $products['products_weight'],
				                                    'final_price' => $product_price_normal,
				                                    'tax_class_id' => $products['products_tax_class_id'],
				                                    'normal_price' => $product_price_normal,
				                                    'products_categories_id' => $game_id,
				                                    'pm_price' => '',
				                                    'pm_total' => $ntotal,
				                                    'discounts' => $prod_applied_discount,
				                                    'attributes' => (isset($this->contents[$products_id]['attributes']) ? $this->contents[$products_id]['attributes'] : ''),
				                                    'pre_order' => (in_array($products_id, $pre_order_array) ? 1 : 0),
													'custom_products_type_id' => (int)$products['custom_products_type_id'],
													'custom_content' => array()
												);
					} else if ((int)$products['custom_products_type_id'] == 1) {
						//Power Leveling
						$final_qty = 1;						
						$custom_type = $this->get_custom_prd_type($products_id);
						$session_array = $this->contents[$products_id]['custom'][$custom_type];
						
						if (sizeof($session_array)>0) {
							foreach ($session_array as $temp_arr) {
								if ($customer_discount >= 0) {
						     		$custom_products_price = (double)($temp_arr['content']['calculated']['price'] + $temp_arr['content']['calculated']['price'] * abs($customer_discount) / 100);
					      		} else {
						     		$custom_products_price = (double)($temp_arr['content']['calculated']['price'] - $temp_arr['content']['calculated']['price'] * abs($customer_discount) / 100);
					      		}
								$products_array[] = array(	'id' => $products_id,
															'name' => $products_name,
															'model' => $products['products_model'],
															'image' => $products['products_image'],
															'price' => $custom_products_price,
															'base_currency' => $products['products_base_currency'],
															'quantity' => $final_qty,
															'weight' => $products['products_weight'],
															'final_price' => $product_price_normal,
															'tax_class_id' => $products['products_tax_class_id'],
															'normal_price' => (double)$temp_arr['content']['calculated']['price'],
															'products_categories_id' => $game_id,
															'pm_price' => '',
															'pm_total' => $ntotal,
															'discounts' => $prod_applied_discount,
															'attributes' => (isset($this->contents[$products_id]['attributes']) ? $this->contents[$products_id]['attributes'] : ''),
															'pre_order' => (in_array($products_id, $pre_order_array) ? 1 : 0),
															'custom_products_type_id' => (int)$products['custom_products_type_id'],
															'custom_content' => $temp_arr['content']
														);
							}
						}
					} else if ((int)$products['custom_products_type_id'] == 3) {
						$final_price = $products_price;
						
						$final_qty = (int)$this->contents[$products_id]['qty'];
		  				
						$products_array[] = array(	'id' => $products_id,
				                                    'name' => $products_name,
				                                    'model' => $products['products_model'],
				                                    'image' => $products['products_image'],
				                                    'price' => $products_price,
				                                    'base_currency' => $products['products_base_currency'],
				                                    'quantity' => $final_qty,
				                                    'weight' => $products['products_weight'],
				                                    'final_price' => $final_price,
				                                    'tax_class_id' => $products['products_tax_class_id'],
				                                    'normal_price' => $products_price,
				                                    'products_categories_id' => $game_id,
				                                    'pm_price' => '',
				                                    'pm_total' => $ntotal,
				                                    'discounts' => $prod_applied_discount,
				                                    'attributes' => (isset($this->contents[$products_id]['attributes']) ? $this->contents[$products_id]['attributes'] : ''),
				                                    'pre_order' => (in_array($products_id, $pre_order_array) ? 1 : 0),
													'custom_products_type_id' => (int)$products['custom_products_type_id'],
													'custom_content' => array()
												);
					}
		  		}
			}
        	$cnt++;
      	}
      	return $products_array;
	}
	
    function show_total() {
    	$this->calculate();
      	return $this->total;
	}
	
	function get_shiptotal() {
    	$this->calculate();
    	return $this->shiptotal;
	}
	
    function show_weight() {
    	return 1;
    	$this->calculate();
      	return $this->weight;
    }
	
    function get_content_type() {
    	$this->content_type = false;
      	
      	if ((DOWNLOAD_ENABLED == 'true') && ($this->count_contents() > 0)) {
        	reset($this->contents);
        	while (list($products_id, ) = each($this->contents)) {
          		if (isset($this->contents[$products_id]['attributes'])) {
            		reset($this->contents[$products_id]['attributes']);
            		while (list(, $value) = each($this->contents[$products_id]['attributes'])) {
              			$virtual_check_query = tep_db_query("select count(*) as total from " . TABLE_PRODUCTS_ATTRIBUTES . " pa, " . TABLE_PRODUCTS_ATTRIBUTES_DOWNLOAD . " pad where pa.products_id = '" . (int)$products_id . "' and pa.options_values_id = '" . (int)$value . "' and pa.products_attributes_id = pad.products_attributes_id");
              			$virtual_check = tep_db_fetch_array($virtual_check_query);
						
              			if ($virtual_check['total'] > 0) {
                			switch ($this->content_type) {
                  				case 'physical':
                    				$this->content_type = 'mixed';
                    				return $this->content_type;
                    				break;
                  				default:
                    				$this->content_type = 'virtual';
                    				break;
                			}
              			} else {
                			switch ($this->content_type) {
                  				case 'virtual':
                    				$this->content_type = 'mixed';
	                    			return $this->content_type;
                    				break;
                  				default:
                    				$this->content_type = 'physical';
                    				break;
                			}
              			}
            		}
					// ICW ADDED CREDIT CLASS - Begin
          		} elseif ($this->show_weight() == 0) {
            		reset($this->contents);
            		while (list($products_id, ) = each($this->contents)) {
              			$virtual_check_query = tep_db_query("select products_weight from " . TABLE_PRODUCTS . " where products_id = '" . $products_id . "'");
              			$virtual_check = tep_db_fetch_array($virtual_check_query);
              			if ($virtual_check['products_weight'] == 0) {
                			switch ($this->content_type) {
                  				case 'physical':
                    				$this->content_type = 'mixed';
                    				return $this->content_type;
                    				break;
                  				default:
                    				$this->content_type = 'virtual_weight';
                    				break;
                			}
              			} else {
                			switch ($this->content_type) {
                  				case 'virtual':
                    				$this->content_type = 'mixed';
                    				return $this->content_type;
                    				break;
                  				default:
                    				$this->content_type = 'physical';
                    				break;
                			}
              			}
            		}
					// ICW ADDED CREDIT CLASS - End
          		} else {
            		switch ($this->content_type) {
              			case 'virtual':
                			$this->content_type = 'mixed';
                			return $this->content_type;
                			break;
              			default:
                			$this->content_type = 'physical';
                			break;
            		}
          		}
        	}
      	} else {
        	$this->content_type = 'physical';
      	}
		
      	return $this->content_type;
	}
    
    function calculateRebatePoint() {
		global $customer_id, $languages_id, $currencies, $order, $order_total_modules, $payment_modules, $payment_modules_info, $order_total_reprocess;
		global $payment;

		$products = $this->get_products();
		$products_amt = sizeof($products);
		$custom_prod_index = array();
		$total_op = 0;
        $opResult = array();
		
		$current_select_pm_id = 0;
		if (tep_not_null($payment) && preg_match('/^(pm_)(\d+)(:~:)(\S+)(:~:)(\d+)$/', $payment, $selected_payment) && count($selected_payment) == 7) {
			$current_select_pm_id = (int)$selected_payment[6];
		}
		
		$extra_op_array = array();
		for ($i = 0; $i < $products_amt; $i++) {
	    	if ((int)$products[$i]['custom_products_type_id'] > 0) {
	      		$custom_prod_index[$products[$i]['id']] = (int)$custom_prod_index[$products[$i]['id']] + 1;
	      		$unique_pid = $products[$i]['id'].'_'.$custom_prod_index[$products[$i]['id']];
	      	} else {
	      		$unique_pid = $products[$i]['id'];
	      	}
            
	    	if ((int)$products[$i]['custom_products_type_id'] < 1) {
                foreach($this->contents[$products[$i]['id']]['custom']['store_credit'] as $count => $extra_info_array) {
                    $product_qty = $this->contents[$products[$i]['id']]['qty'];
                    $currencies->display_price($products[$i]['id'], $products[$i]['normal_price'], tep_get_tax_rate($products[$i]['tax_class_id']), $product_qty, true, '', $current_select_pm_id);
		    		$total_op += $currencies->rebate_point;
		    		$extra_op_array[$unique_pid] += $currencies->rebate_point_extra;
                }
            } else {
                $product_qty = $this->contents[$products[$i]['id']]['qty'];
                //to calculate the op & extra op
                $currencies->display_price($products[$i]['id'], $products[$i]['normal_price'], tep_get_tax_rate($products[$i]['tax_class_id']), $product_qty, true, '', $current_select_pm_id);
                $total_op = $currencies->rebate_point;
                $extra_op_array[$unique_pid] = $currencies->rebate_point_extra;
			}
		}
		$custom_prod_index = array();
		if (MODULE_ORDER_TOTAL_INSTALLED) {
			if (!isset($order_total_reprocess) || $order_total_reprocess==true) $order_total_modules->process();
			$cust_group_extra_rebate = 0;
			$cust_group_extra_op = 0;
			if (tep_not_null($payment) && preg_match('/^(pm_)(\d+)(:~:)(\S+)(:~:)(\d+)$/', $payment, $selected_payment) && count($selected_payment) == 7) {
				for ($count_products = 0; $count_products < $products_amt; $count_products++) {
			    	if ((int)$products[$count_products]['custom_products_type_id'] > 0) {
			      		$custom_prod_index[$products[$count_products]['id']] = (int)$custom_prod_index[$products[$count_products]['id']] + 1;
			      		$unique_pid = $products[$count_products]['id'].'_'.$custom_prod_index[$products[$count_products]['id']];
			      	} else {
			      		$unique_pid = $products[$count_products]['id'];
			      	}
					
			    	if (isset($extra_op_array[$unique_pid]) && $extra_op_array[$unique_pid] > 0) {
						$surcharge_amt = 0;
						if (isset($GLOBALS['ot_surcharge']) && $GLOBALS['ot_surcharge']->enabled) {
		            		for ($ot_surcharge_count=0;$ot_surcharge_count < sizeof($GLOBALS['ot_surcharge']->output);$ot_surcharge_count++) {
		              			if (tep_not_null($GLOBALS['ot_surcharge']->output[$ot_surcharge_count]['title']) && tep_not_null($GLOBALS['ot_surcharge']->output[$ot_surcharge_count]['text'])) {
		                			$surcharge_amt = $GLOBALS['ot_surcharge']->output[$ot_surcharge_count]['value'];
		              			}
		            		}
						}
						
						$gst_amt = 0;
						if (isset($GLOBALS['ot_gst']) && $GLOBALS['ot_gst']->enabled) {
		            		for ($gst_cnt=0; $gst_cnt < sizeof($GLOBALS['ot_gst']->output); $gst_cnt++) {
		              			if (tep_not_null($GLOBALS['ot_gst']->output[$gst_cnt]['title']) && tep_not_null($GLOBALS['ot_gst']->output[$gst_cnt]['text'])) {
		                			$gst_amt = $GLOBALS['ot_gst']->output[$gst_cnt]['value'];
		              			}
		            		}
						}
						
						$cust_group_extra_rebate = floor($extra_op_array[$unique_pid] * ( ($order->info['total'] - $surcharge_amt - $gst_amt)/ $order->info['subtotal']));
						$cust_group_extra_op = $cust_group_extra_op + $cust_group_extra_rebate;
					}
				}
			}
		}

        if ($total_op > 0) {
            $opResult['op'] = array ('name' => ENTRY_REBATE, 'value' => tep_display_op($total_op));
        }
        if ($cust_group_extra_op > 0) {
            $opResult['extra_op'] = array('name' => ENTRY_ADDED_BONUS, 'value' => tep_display_op($cust_group_extra_op));
        }
		
		return $opResult;
	}
}
?>