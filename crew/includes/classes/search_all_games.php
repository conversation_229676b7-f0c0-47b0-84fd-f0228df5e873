<?php

class search_all_games {

    public $alphabet_game_array = array();
    public $alphabet_array = array("0", "a", "b", "c", "d", "e", "f", "g", "h", "i", "j", "k", "l", "m", "n", "o", "p", "q", "r", "s", "t", "u", "v", "w", "x", "y", "z");
    public $selected_game_alphabet_array = array();
    public $selected_game_alphabet_id = ''; // Not in used after change to new layout 2011
    public $selected_game_region_id = 0; // Not in used after change to new layout 2011
    public $selected_grtl_id = null;  // Not in used after change to new layout 2011
    public $selected_letter = null;
    public $selected_game_language = null;
    public $selected_platform = null;
    public $selected_genre = null;
    public $selected_product_type = null;
    public $selected_gid = null;
    public $selected_favorite = 0;
    public $game_all_genre_array = array();
    public $game_all_platform_array = array();
    public $game_all_favorite_array = array();
    public $display_record_per_page = 50;
    public $search_string = null;
    public $max_allowed_favorite = 20;

    public function __construct() {
        $this->predefine_all_selected_values();
    }

    public function get_valid_categories_by_customer_group($categories_id_array, $country, $customers_groups_id) {
        global $memcache_obj;

        $categories_memcache_row = array();
        $categories_memcache_key = TABLE_CATEGORIES . '/valid_game_list/array/country/' . $country . '/customers_groups_id/' . $customers_groups_id;
        $categories_memcache_result = $memcache_obj->fetch($categories_memcache_key);

        if ($categories_memcache_result !== FALSE) {
            $categories_memcache_row = $categories_memcache_result;
        } else {
            $categories_memcache_row = tep_get_categories_by_customer_group($categories_id_array, $customers_groups_id);
            $memcache_obj->store($categories_memcache_key, $categories_memcache_row, 600); // Cache for 10 minutes
        }

        return $categories_memcache_row;
    }

    public function get_games_info($zone_game_array, $country, $customers_groups_id) {
        global $languages_id;

        $game_product_available = array();
        $unique_game_array = array();
        $categories_array = array();
        if (is_array($zone_game_array->zone_categories_id) && !empty($zone_game_array->zone_categories_id)) {
            $categories_array = $this->get_valid_categories_by_customer_group($zone_game_array->zone_categories_id, $country, $customers_groups_id);
        }

        foreach ($categories_array as $categories_row) {
            $available_product_child_types_array = array();
            $product_type_child_category_array = array();

            $cat_name = tep_get_categories_name($categories_row['categories_id'], $languages_id);
            $heading_title = tep_get_categories_heading_title($categories_row['categories_id'], $languages_id);
            $cat_pin_yin = tep_get_categories_pin_yin($categories_row['categories_id'], $languages_id);

            $game_product_type_tmp = array();

            if ($categories_row['parent_id'] > 0) {
                $cur_cat_path_array = explode('_', substr($categories_row['categories_parent_path'], 1, -1));
                $current_game_category_id = $cur_cat_path_array[0];
                $games_categories_path = substr($categories_row['categories_parent_path'], 1) . $categories_row['categories_id'];
            } else {
                $current_game_category_id = $categories_row['categories_id'];
                $games_categories_path = $categories_row['categories_id'];
            }

            //categories selected Available Product Type
            $category_obj = new category($current_game_category_id);
            $available_product_child_types_array = $category_obj->get_available_product_child_type();
            $product_type_child_category_array = $category_obj->get_product_child_type_category($games_categories_path, $available_product_child_types_array);
            unset($category_obj, $available_product_child_types_array);

            //select needed categories_product_types
            foreach ($product_type_child_category_array as $category_product_types) {
                $product_type_name = tep_get_categories_name($category_product_types['categories_id'], $languages_id);
                $categories_parent_path = substr($category_product_types['categories_parent_path'], 1) . $category_product_types['categories_id'];

                $game_product_available[$category_product_types['custom_products_type_child_id']][$categories_row['categories_id']] = '';

                $game_product_type_tmp[] = array(
                    'ctype' => $category_product_types['custom_products_type_child_id'],
                    'tpl' => $category_product_types['custom_products_type_id'],
                    'product_type_cPath' => $categories_parent_path,
                    'product_type_name' => $product_type_name
                );
            }

            if (count($game_product_type_tmp)) {
                if ($zone_game_array->zone_sort_type == 'define') {
                    $temp_sort_order = array_search($categories_row['categories_id'], $zone_game_array->zone_sort_order);
                    $unique_game_array[$temp_sort_order] = array('id' => $categories_row['categories_id'],
                        'cPath' => $games_categories_path,
                        'name' => $cat_name,
                        'title' => $heading_title,
                        'pin_yin' => $cat_pin_yin,
                        'product_type' => $game_product_type_tmp
                    );
                } else {
                    $unique_game_array[] = array('id' => $categories_row['categories_id'],
                        'cPath' => $games_categories_path,
                        'name' => $cat_name,
                        'title' => $heading_title,
                        'pin_yin' => $cat_pin_yin,
                        'product_type' => $game_product_type_tmp
                    );
                }
            }
        }

        if ($zone_game_array->zone_sort_type == "define") {
            ksort($unique_game_array);
        }

        $return_array = array(
            'game_product_available' => $game_product_available,
            'unique_game_array' => $unique_game_array
        );

        return $return_array;
    }

    public function generate_game_array() {
        global $zone_info_array, $country, $customers_groups_id, $page_info, $languages_id;

        $games_array = $this->get_games_info($zone_info_array[1], $country, $customers_groups_id);
        $game_product_available = $games_array['game_product_available'];
        $unique_game_array = $games_array['unique_game_array'];

        $temp_game_map = array();
        $temp_game_array = array();
        $temp_game_array2 = array();
        $temp_game_array3 = array();

        $this->game_all_language_array = $this->get_game_all_language();
        $this->game_all_genre_array = $this->get_game_all_genre();
        $this->game_all_platform_array = $this->get_game_all_platform();
        $this->game_all_favorite_array = $this->get_game_all_favorite();

        foreach ($unique_game_array as $pointer => $game_array) {
            $each_game_array = array();
            $game_id = $game_array['id'];
            $each_game_array['product_type'] = array();
            $letter = strtolower(substr($game_array['pin_yin'], 0, 1));
            $letter = array_search($letter, $this->alphabet_array) > 0 ? $letter : '0';
            $sort_order = strtolower($game_array['name']) . $pointer;

            if (isset($game_array['product_type'])) {
                foreach ($game_array['product_type'] as $alpha_game_type) {
                    // Removed product_type_name is null checking
                    if ((int) $this->selected_product_type > 0) {
                        if ($alpha_game_type['ctype'] == $this->selected_product_type) {
                            $each_game_array['product_type'][] = array('cPath' => $alpha_game_type['product_type_cPath'], 'name' => $alpha_game_type['product_type_name']);
                            break;
                        }
                    } else {
                        $key = array_search($alpha_game_type['ctype'], $page_info->custom_product_type_child_id_array);
                        $each_game_array['product_type'][$key] = array('cPath' => $alpha_game_type['product_type_cPath'], 'name' => $alpha_game_type['product_type_name']);
                    }
                }

                ksort($each_game_array['product_type']);
            }

            $each_game_array['id'] = $game_id;
            $each_game_array['name'] = $game_array['name'];
            $each_game_array['platform'] = $this->get_game_platform($game_id, $this->game_all_platform_array);
            $each_game_array['genre'] = $this->get_game_genre($game_id, $this->game_all_genre_array);
            $each_game_array['language'] = $this->get_game_language($game_id, $this->game_all_language_array);
            $each_game_array['favorite'] = $this->get_game_favorite($game_id, $this->game_all_favorite_array);
            $each_game_array['game_style'] = '';
            $each_game_array['pointer'] = $pointer;
            $each_game_array['letter'] = $letter;
            $each_game_array['sort_order'] = $sort_order;

            $temp_game_array[$letter][$sort_order] = $each_game_array;
            $temp_game_map[$game_id] = array('letter' => $letter,
                'pointer' => $pointer,
                'sort_order' => $sort_order);

            unset($each_game_array);
        }

        // 1 : Selected game array
        if (tep_not_null($this->selected_gid)) {
            if (isset($temp_game_map[$this->selected_gid])) {
                $temp_filtered_game_array = array();
                $selected_letter = $temp_game_map[$this->selected_gid]['letter'];
                $selected_pointer = $temp_game_map[$this->selected_gid]['pointer'];
                $selected_sort_order = $temp_game_map[$this->selected_gid]['sort_order'];

//				unset($temp_game_array[$selected_game_alphabet][$sort_order]);
//
//				if ($selected_game_alphabet == '0') { //array_search($selected_game_alphabet, $this->alphabet_array) == 0
//					$this->selected_game_alphabet_id = 99;
//					$this->selected_game_alphabet_array = array($selected_game_alphabet);
//				} else {
//					$temp_alpha_id = array_search($selected_game_alphabet, $this->alphabet_array);
//					$this->selected_game_alphabet_id = ($temp_alpha_id % 2 == 0) ? $temp_alpha_id : $temp_alpha_id+1;
//					$selected_game_alphabet = $this->alphabet_array[$this->selected_game_alphabet_id-1];
//					$this->selected_game_alphabet_array = array($this->alphabet_array[$this->selected_game_alphabet_id-1], $this->alphabet_array[$this->selected_game_alphabet_id]);
//				}
//
//				// '' == null == ASCII return 0 (1st char)
//				$temp_game_array[''][''] = $temp_game_map[$this->selected_gid];
//				$temp_game_array['']['']['game_style'] = ' gameBox_selected';

                $temp_filtered_game_array = $temp_game_array[$selected_letter][$selected_sort_order];

                unset($temp_game_array, $temp_game_map);

                $temp_game_array = array();
                $temp_game_array[$selected_letter][$selected_sort_order] = $temp_filtered_game_array;
            } else {
                unset($temp_game_array, $temp_game_map);
            }
        } else if ($this->selected_favorite == 1) {
            if (tep_not_null($this->game_all_favorite_array)) {
                foreach (array_keys(array_diff_key($temp_game_map, $this->game_all_favorite_array)) AS $category_id) {
                    $ref_arr = $temp_game_map[$category_id];
                    unset($temp_game_array[$ref_arr['letter']][$ref_arr['sort_order']]);
                }
            } else {
                unset($temp_game_array);
            }

            unset($temp_game_map);
            // 2 : Filter by Region's Language
        } else if (tep_not_null($this->selected_grtl_id)) {
            $temp_filtered_game_array = array();

            $get_categories_select = "	SELECT categories_id
										FROM " . TABLE_GAME_REGION_LANGUAGE_TO_CATEGORIES . "
										WHERE game_region_to_language_id = " . $this->selected_grtl_id;
            $get_categories_query = tep_db_query($get_categories_select);
            while ($get_categories_row = tep_db_fetch_array($get_categories_query)) {
                $temp_filtered_game_array[$get_categories_row['categories_id']] = '';
            }

            if (tep_not_null($temp_filtered_game_array)) {
                foreach (array_keys(array_diff_key($temp_game_map, $temp_filtered_game_array)) AS $category_id) {
                    $ref_arr = $temp_game_map[$category_id];
                    unset($temp_game_array[$ref_arr['letter']][$ref_arr['sort_order']], $temp_game_map[$category_id]);
                }
            } else {
                // When filter found empty game.
                unset($temp_game_array, $temp_game_map);
            }
            unset($temp_filtered_game_array);
        }

        if (tep_not_null($temp_game_map)) {
            // Filter by Letter
            if (tep_not_null($this->selected_letter) && (int) $this->selected_letter > 0) { // if $this->selected_letter == 99 is for all
                $alphabet_key = $this->selected_letter == 99 ? 0 : $this->selected_letter;

                if (isset($this->alphabet_array[$alphabet_key])) {
                    $letter = $this->alphabet_array[$alphabet_key];

                    if (isset($temp_game_array[$letter])) {
                        $holder_array = array();
                        $holder_array = $temp_game_array[$letter];

                        unset($temp_game_array, $temp_game_map);
                        $temp_game_map = array();

                        $temp_game_array[$letter] = $holder_array;

                        foreach ($temp_game_array[$letter] AS $sort_order => $game_array) {
                            $temp_game_map[$game_array['id']] = array('letter' => $game_array['letter'],
                                'pointer' => $game_array['pointer'],
                                'sort_order' => $sort_order);
                        }

                        unset($holder_array);
                    } else {
                        unset($temp_game_array, $temp_game_map);
                    }
                }
            }

            // Filter by Search String
            if (tep_not_empty($this->search_string) && tep_not_null($temp_game_map)) {
                $temp_filtered_game_array = array();

                $categories_search_sql = "	SELECT DISTINCT categories_id
											FROM " . TABLE_CATEGORIES_SEARCH . "
											WHERE search_value LIKE '%" . tep_db_input($this->search_string) . "%'";
                $categories_search_result = tep_db_query($categories_search_sql);
                while ($categories_search_row = tep_db_fetch_array($categories_search_result)) {
                    $temp_filtered_game_array[$categories_search_row['categories_id']] = '';
                }

                if (tep_not_null($temp_filtered_game_array)) {
                    foreach (array_keys(array_diff_key($temp_game_map, $temp_filtered_game_array)) AS $category_id) {
                        $ref_arr = $temp_game_map[$category_id];
                        unset($temp_game_array[$ref_arr['letter']][$ref_arr['sort_order']], $temp_game_map[$category_id]);
                    }
                } else {
                    // When filter found empty game.
                    unset($temp_game_array, $temp_game_map);
                }
                unset($temp_filtered_game_array);
            }

            // Filter by Game's Platform
            if ((int) $this->selected_platform > 0 && tep_not_null($temp_game_map)) {
                $temp_filtered_game_array = array();

                $get_categories_select = "	SELECT categories_id
											FROM " . TABLE_GAME_PLATFORM_TO_CATEGORIES . "
											WHERE game_platform_id = " . $this->selected_platform;
                $get_categories_query = tep_db_query($get_categories_select);
                while ($get_categories_row = tep_db_fetch_array($get_categories_query)) {
                    $temp_filtered_game_array[$get_categories_row['categories_id']] = '';
                }

                if (tep_not_null($temp_filtered_game_array)) {
                    foreach (array_keys(array_diff_key($temp_game_map, $temp_filtered_game_array)) AS $category_id) {
                        $ref_arr = $temp_game_map[$category_id];
                        unset($temp_game_array[$ref_arr['letter']][$ref_arr['sort_order']], $temp_game_map[$category_id]);
                    }
                } else {
                    // When filter found empty game.
                    unset($temp_game_array, $temp_game_map);
                }
                unset($temp_filtered_game_array);
            }

            // Filter by Game's Genre
            if ((int) $this->selected_genre > 0 && tep_not_null($temp_game_map)) {
                $temp_filtered_game_array = array();

                $get_categories_select = "	SELECT categories_id
											FROM " . TABLE_GAME_GENRE_TO_CATEGORIES . "
											WHERE game_genre_id = " . $this->selected_genre;
                $get_categories_query = tep_db_query($get_categories_select);
                while ($get_categories_row = tep_db_fetch_array($get_categories_query)) {
                    $temp_filtered_game_array[$get_categories_row['categories_id']] = '';
                }

                if (tep_not_null($temp_filtered_game_array)) {
                    foreach (array_keys(array_diff_key($temp_game_map, $temp_filtered_game_array)) AS $category_id) {
                        $ref_arr = $temp_game_map[$category_id];
                        unset($temp_game_array[$ref_arr['letter']][$ref_arr['sort_order']], $temp_game_map[$category_id]);
                    }
                } else {
                    // When filter found empty game.
                    unset($temp_game_array, $temp_game_map);
                }
                unset($temp_filtered_game_array);
            }

            // Filter by Game's Product Type
            if ((int) $this->selected_product_type > 0 && tep_not_null($temp_game_map)) {
                $temp_filtered_game_array = array();

                if (isset($game_product_available[$this->selected_product_type])) {
                    $temp_filtered_game_array = $game_product_available[$this->selected_product_type];
                }

                if (tep_not_null($temp_filtered_game_array)) {
                    foreach (array_keys(array_diff_key($temp_game_map, $temp_filtered_game_array)) AS $category_id) {
                        $ref_arr = $temp_game_map[$category_id];
                        unset($temp_game_array[$ref_arr['letter']][$ref_arr['sort_order']], $temp_game_map[$category_id]);
                    }
                } else {
                    // When filter found empty game.
                    unset($temp_game_array, $temp_game_map);
                }
                unset($temp_filtered_game_array);
            }

            // Filter by Game's Language
            if ((int) $this->selected_game_language > 0 && tep_not_null($temp_game_map)) {
                $temp_filtered_game_array = array();

                $get_categories_select = "	SELECT categories_id
											FROM " . TABLE_GAME_LANGUAGE_TO_CATEGORIES . "
											WHERE game_language_id = " . $this->selected_game_language;
                $get_categories_query = tep_db_query($get_categories_select);
                while ($get_categories_row = tep_db_fetch_array($get_categories_query)) {
                    $temp_filtered_game_array[$get_categories_row['categories_id']] = '';
                }

                if (tep_not_null($temp_filtered_game_array)) {
                    foreach (array_keys(array_diff_key($temp_game_map, $temp_filtered_game_array)) AS $category_id) {
                        $ref_arr = $temp_game_map[$category_id];
                        unset($temp_game_array[$ref_arr['letter']][$ref_arr['sort_order']], $temp_game_map[$category_id]);
                    }
                } else {
                    // When filter found empty game.
                    unset($temp_game_array, $temp_game_map);
                }
                unset($temp_filtered_game_array);
            }
        }

        unset($temp_game_map);

        // Re-organize the game array - Start
        if (tep_not_null($temp_game_array)) {
            foreach ($temp_game_array as $letter => $game) {
                if (count($game)) {
                    ksort($game, SORT_STRING);
                    $temp_game_array2[$letter] = array_values($game);
                }
            }

            unset($temp_game_array);
            ksort($temp_game_array2, SORT_STRING);

            foreach ($temp_game_array2 as $alphabet => $games_array) {
                foreach ($games_array as $game_array) {
                    $temp_game_array3[] = $game_array;
                }
            }

            unset($temp_game_array2);

            $this->alphabet_game_array = $temp_game_array3;
            unset($temp_game_array3);
        }
        // Re-organize the game array - End
    }

    private function predefine_all_selected_values() {
        $this->selected_gid = isset($_GET['gid']) ? (int) $_GET['gid'] : null;

        $this->search_string = (isset($_REQUEST['gKeyword']) && $_REQUEST['gKeyword'] != ALL_PRODUCTS_LINK) ? tep_db_prepare_input(strip_tags($_REQUEST['gKeyword'])) : null;
        $this->selected_letter = isset($_REQUEST['letter']) ? (int) $_REQUEST['letter'] : null;
        $this->selected_game_language = isset($_REQUEST['glanguage']) ? (int) $_REQUEST['glanguage'] : null;
        $this->selected_platform = isset($_REQUEST['platform']) ? (int) $_REQUEST['platform'] : null;
        $this->selected_genre = isset($_REQUEST['genre']) ? (int) $_REQUEST['genre'] : null;
        $this->selected_product_type = isset($_REQUEST['product_type']) ? (int) $_REQUEST['product_type'] : null;
        $this->selected_favorite = isset($_REQUEST['favorite']) ? (int) $_REQUEST['favorite'] : 0;

        if (tep_not_null($this->selected_gid)) {
            // when got game id
        } else if (tep_not_empty($this->selected_game_language) || tep_not_empty($this->selected_platform) || tep_not_empty($this->selected_genre) || tep_not_empty($this->selected_product_type)) {
            // when do filter from SAG page/outside of SAG
        } else {
            // when page refresh
            if (isset($_COOKIE['search_all_games']) && tep_not_null($_COOKIE['search_all_games'])) {
                $raw_data_array = explode(",", $_COOKIE['search_all_games']);

                if (count($raw_data_array) == 6) {
                    list($cookie_letter, $cookie_game_language_id, $cookie_product_type, $cookie_platform, $cookie_genre, $cookie_favorite) = $raw_data_array;

                    $this->selected_letter = (int) $cookie_letter;
                    $this->selected_game_language = (int) $cookie_game_language_id;
                    $this->selected_product_type = (int) $cookie_product_type;
                    $this->selected_platform = (int) $cookie_platform;
                    $this->selected_genre = (int) $cookie_genre;
                    $this->selected_favorite = (int) $cookie_favorite;
                }
            }
        }
    }

    function generate_html_body_content() {
        include_once(DIR_WS_CLASSES . 'pagination.php');
        global $page_info;
        $return_string = '';
        $hide_no_record = 0;

        ob_start();

        if (tep_not_null($this->alphabet_game_array)) {
            $pager_obj = new Pagination($this->alphabet_game_array, null, null, $this->display_record_per_page);
            $pager_obj->set_href_js('javascript:reload_sag_data("%d")');
            $this->alphabet_game_array = $pager_obj->paginate();

            foreach ($this->alphabet_game_array as $game_array) {
                $game_product_type = '';

                // Game Type Listing - Start
                foreach ($game_array['product_type'] as $alpha_game_type) {
                    $game_product_type .= '<div style="padding: 3px 0;">
												<div class="triangleRightIconGlay" style="margin:3px 5px"><!-- --></div>
												<a href="' . tep_href_link(FILENAME_DEFAULT, 'cPath=' . $alpha_game_type['cPath']) . '">' . $alpha_game_type['name'] . '</a>
											</div>';
                }
                // Game Type Listing - End
                ?>
                <div class="gameBox gameBoxExtend solborderc<?= $game_array['game_style'] ?>">
                    <table cellpadding="3" cellspacing="0" width="100%">
                        <tr>
                            <td class="sagames-name"><span class="hds3"><?= $game_array['name'] ?></span></td>
                            <td class="sagames-lang"><span class="hds5"><?= implode(" / ", $game_array['language']) ?>&nbsp;</span></td>
                            <td class="sagames-producttype"><span class="hds5"><?= $game_product_type ?></span></td>
                            <td class="sagames-platform"><span class="hds5"><?= implode(" / ", $game_array['platform']) ?>&nbsp;</span></td>
                            <td class="sagames-genre"><span class="hds5"><?= implode(" / ", $game_array['genre']) ?>&nbsp;</span></td>
                            <td class="sagames-fav"><a id="fav-<?= $game_array['id'] ?>" href="javascript:update_fav('fav-<?= $game_array["id"] ?>');" class="<?= $this->get_selected_css_class('fav_star', $game_array['favorite'], '_selected') ?>"></a></td>
                        </tr>
                    </table>
                </div>
                <?
            }
            ?>
            <div class="vspacing"></div>
            <div style="float:left;"><span class="hds5"><?= sprintf(TEXT_DISPLAY_NUMBER_OF_PRODUCTS, ($pager_obj->offset + 1), $pager_obj->page_last_records, $pager_obj->total_rows) ?></span></div>
            <div id="ogm_pg" style="float:right;"><?= $pager_obj->renderFullNav() ?></div>
            <?
            unset($pager_obj);
        }

        $return_string = ob_get_contents();
        ob_end_clean();

        unset($this->alphabet_game_array);

        return $return_string;
    }

    public function get_filtering_bar_object() {
        global $languages_id;

        $return_array = array();
        $filter_by_letter_array = array(array('id' => 0, 'text' => LINK_ALL_NUMERIC),
            array('id' => 99, 'text' => FILTER_BY_NUMERIC));
        $filter_by_game_language_array = array(array('id' => 0, 'text' => FILTER_BY_LANGUAGE), array('id' => -999, 'text' => LINK_ALL_LANGUAGE));
        $filter_by_genre_array = array(array('id' => 0, 'text' => FILTER_BY_GENRE), array('id' => -999, 'text' => LINK_ALL_GENRE));
        $filter_by_platform_array = array(array('id' => 0, 'text' => FILTER_BY_PLATFORM), array('id' => -999, 'text' => LINK_ALL_PLATFORM));
        $filter_by_product_type_array = array(array('id' => 0, 'text' => FILTER_BY_PRODUCT_TYPE), array('id' => -999, 'text' => LINK_ALL_PRODUCT_TYPE));

        $filter_by_game_language_array = $this->convert_to_selection_format($this->game_all_language_array, $filter_by_game_language_array);
        $filter_by_genre_array = $this->convert_to_selection_format($this->game_all_genre_array, $filter_by_genre_array);
        $filter_by_platform_array = $this->convert_to_selection_format($this->game_all_platform_array, $filter_by_platform_array);
        $filter_by_product_type_array = $this->convert_to_selection_format($this->get_all_product_child_type(), $filter_by_product_type_array);

        for ($counter = 1; $counter < count($this->alphabet_array); $counter++) {
            $filter_by_letter_array[] = array('id' => $counter, 'text' => strtoupper($this->alphabet_array[$counter]));
        }

        $return_array['letter'] = '<div class="span_f_letter">' . tep_draw_css_pull_down_menu2('f_letter', $filter_by_letter_array, $this->selected_letter, 'sag', 55, 300) . '</div>';
        $return_array['genre'] = '<div class="span_f_genre">' . tep_draw_css_pull_down_menu2('f_genre', $filter_by_genre_array, $this->selected_genre, 'sag', 135) . '</div>';
        $return_array['platform'] = '<div class="span_f_platform">' . tep_draw_css_pull_down_menu2('f_platform', $filter_by_platform_array, $this->selected_platform, 'sag', 135) . '</div>';
        $return_array['product_type'] = '<div class="span_f_product_type">' . tep_draw_css_pull_down_menu2('f_product_type', $filter_by_product_type_array, $this->selected_product_type, 'sag', 205) . '</div>';
        $return_array['language'] = '<div class="span_f_language">' . tep_draw_css_pull_down_menu2('f_language', $filter_by_game_language_array, $this->selected_game_language, 'sag', 100) . '</div>';
        $return_array['search_box'] = '<div class="span_f_search_box">
												<div id="sags_box">
													<span class="search_img"></span>
													<input type="text" autocomplete="off" title="' . ALL_PRODUCTS_LINK . '" style="width: 146px;" value="' . ($this->search_string == '' ? ALL_PRODUCTS_LINK : $this->search_string) . '" onfocus="if(this.value==this.title) this.value=\'\'" onblur="if(this.value==\'\') this.value=this.title">
													<span class="right_img' . ($this->search_string == '' ? ' empty' : '') . '"></span>
												</div>
											</div>';
        $return_array['favorite'] = '<a id="fav-star" href="javascript: void(0);" class="' . $this->get_selected_css_class('fav_star', $this->selected_favorite, '_selected') . '" style="text-decoration: none;">
												<span><div style="padding-top: 2px; padding-left: 22px; width: 45px;">' . FILTER_BY_FAVORITE . '</div></span>
											</a>' .
                tep_draw_hidden_field('filter_fav', $this->selected_favorite, 'id="f_fav"');

        unset($filter_by_letter_array, $filter_by_game_language_array, $filter_by_genre_array, $filter_by_platform_array, $filter_by_product_type_array);

        return $return_array;
    }

    private function convert_to_selection_format($to_convert_array, $return_array = array()) {
        foreach ($to_convert_array AS $id => $text) {
            $return_array[] = array('id' => $id, 'text' => $text);
        }

        return $return_array;
    }

    public function update_cookie() {
        global $cookies_expired, $cookie_path, $cookie_domain;

        // Update client's search all games setting.
        $cookies_expired = time() + 60 * 60 * 1; // 1 hour expiry date;
        $cookie_content_array = array($this->selected_letter,
            $this->selected_game_language,
            $this->selected_product_type,
            $this->selected_platform,
            $this->selected_genre,
            $this->selected_favorite);

        tep_setcookie('search_all_games', implode(",", $cookie_content_array), $cookies_expired, $cookie_path, $cookie_domain);
    }

    public function update_favorite() {
        $return_array = array();
        $cat_id_array = isset($_REQUEST['pid']) ? explode("-", $_REQUEST['pid']) : '';

        if (isset($cat_id_array[1]) && (int) $cat_id_array[1] > 0) {
            if (isset($_SESSION['customer_id']) && tep_not_empty($_SESSION['customer_id'])) {
                $all_cat_id_array = array();

                $check_fav_select = "	SELECT categories_id
										FROM " . TABLE_CUSTOMERS_FAVORITES . "
										WHERE customers_id = " . $_SESSION['customer_id'];
                $check_fav_query = tep_db_query($check_fav_select);
                while ($check_fav_row = tep_db_fetch_array($check_fav_query)) {
                    $all_cat_id_array[] = $check_fav_row['categories_id'];
                }

                $fav_bal = count($all_cat_id_array);

                if (in_array($cat_id_array[1], $all_cat_id_array)) {
                    $query = " 	DELETE FROM " . TABLE_CUSTOMERS_FAVORITES . "
								WHERE customers_id = " . $_SESSION['customer_id'] . "
									AND categories_id = " . (int) $cat_id_array[1];
                    tep_db_query($query);

                    $return_array['status'] = 2;
                    $return_array['balance'] = $fav_bal - 1;
                } else if ($fav_bal >= $this->max_allowed_favorite) {
                    $return_array['status'] = -2;
                } else {
                    $sql_insert_array = array('customers_id' => $_SESSION['customer_id'],
                        'categories_id' => (int) $cat_id_array[1]
                    );
                    tep_db_perform(TABLE_CUSTOMERS_FAVORITES, $sql_insert_array);

                    unset($sql_insert_array);
                    $return_array['status'] = 1;
                    $return_array['balance'] = $fav_bal + 1;
                }
            } else {
                $return_array['status'] = -1;
            }
        } else {
            $return_array['status'] = -3;
        }

        return $return_array;
    }

    public function get_game_all_favorite() {
        $return_array = array();

        if (isset($_SESSION['customer_id'])) {
            $get_fav_select = "	SELECT categories_id
									FROM " . TABLE_CUSTOMERS_FAVORITES . "
									WHERE customers_id = " . $_SESSION['customer_id'];
            $get_fav_query = tep_db_query($get_fav_select);
            while ($get_fav_row = tep_db_fetch_array($get_fav_query)) {
                $return_array[$get_fav_row['categories_id']] = '';
            }
        }

        return $return_array;
    }

    public function get_game_all_genre() {
        global $languages_id;
        $return_array = array();

        $get_genre_select = "	SELECT gg.game_genre_id, ggd.game_genre_description
								FROM " . TABLE_GAME_GENRE_DESCRIPTION . " AS ggd
								INNER JOIN " . TABLE_GAME_GENRE . " AS gg
									ON gg.game_genre_id=ggd.game_genre_id
								WHERE ggd.language_id = " . $languages_id . "
								ORDER BY gg.sort_order";
        $get_genre_query = tep_db_query($get_genre_select);
        while ($get_genre_row = tep_db_fetch_array($get_genre_query)) {
            $return_array[$get_genre_row['game_genre_id']] = $get_genre_row['game_genre_description'];
        }

        return $return_array;
    }

    public function get_game_all_language() {
        global $languages_id;
        $return_array = array();

        $get_lang_select = "	SELECT gl.game_language_id, gld.game_language_description
								FROM " . TABLE_GAME_LANGUAGE_DESCRIPTION . " AS gld
								INNER JOIN " . TABLE_GAME_LANGUAGE . " AS gl
									ON gl.game_language_id=gld.game_language_id
								WHERE gld.language_id = " . $languages_id . "
								ORDER BY gl.sort_order";
        $get_lang_query = tep_db_query($get_lang_select);
        while ($get_lang_row = tep_db_fetch_array($get_lang_query)) {
            $return_array[$get_lang_row['game_language_id']] = $get_lang_row['game_language_description'];
        }

        return $return_array;
    }

    public function get_game_all_platform() {
        global $languages_id;
        $return_array = array();

        $get_platforms_select = "	SELECT gp.game_platform_id, gpd.game_platform_description
									FROM " . TABLE_GAME_PLATFORM_DESCRIPTION . " AS gpd
									INNER JOIN " . TABLE_GAME_PLATFORM . " AS gp
										ON gp.game_platform_id=gpd.game_platform_id
									WHERE gpd.language_id = " . $languages_id . "
									ORDER BY gp.sort_order";
        $get_platforms_query = tep_db_query($get_platforms_select);
        while ($get_platforms_row = tep_db_fetch_array($get_platforms_query)) {
            $return_array[$get_platforms_row['game_platform_id']] = $get_platforms_row['game_platform_description'];
        }

        return $return_array;
    }

    public function get_all_product_child_type() {
        global $languages_id;

        $product_type_array = array();
        $get_product_type_select = "	SELECT cptc.custom_products_type_child_id, cptcl.custom_products_type_child_name
										FROM " . TABLE_CUSTOM_PRODUCTS_TYPE_CHILD_LANG . " AS cptcl
										INNER JOIN " . TABLE_CUSTOM_PRODUCTS_TYPE_CHILD . " AS cptc
											ON cptc.custom_products_type_child_id=cptcl.custom_products_type_child_id
										WHERE cptc.custom_products_type_child_id NOT IN (1, 2, 5, 12, 15, 16)
                                            AND cptcl.languages_id = " . $languages_id . " 
                                            AND cptc.display_status = 1
										ORDER BY cptc.sort_order";
        $get_product_type_query = tep_db_query($get_product_type_select);
        while ($get_product_type_row = tep_db_fetch_array($get_product_type_query)) {
            $product_type_array[$get_product_type_row['custom_products_type_child_id']] = $get_product_type_row['custom_products_type_child_name'];
        }
        return $product_type_array;
    }

    public function get_game_favorite($categories_id, $all_favorite_array) {
        $return_bool = false;
        if (isset($all_favorite_array[$categories_id])) {
            $return_bool = true;
        }

        return $return_bool;
    }

    public function get_game_genre($categories_id, $all_genre_array) {
        global $memcache_obj;
        $cat_genre_array = array();
        $return_array = array();

        $cache_key = TABLE_GAME_GENRE_TO_CATEGORIES . '/game_genre_id/array/categories_id/' . $categories_id;
        $cache_result = $memcache_obj->fetch($cache_key);

        if ($cache_result !== FALSE) {
            $cat_genre_array = $cache_result;
        } else {
            $get_category_genre_select = "	SELECT game_genre_id
											FROM " . TABLE_GAME_GENRE_TO_CATEGORIES . "
											WHERE categories_id = " . $categories_id;
            $get_category_genre_query = tep_db_query($get_category_genre_select);
            while ($get_category_genre_row = tep_db_fetch_array($get_category_genre_query)) {
                $cat_genre_array[] = $get_category_genre_row['game_genre_id'];
            }

            $memcache_obj->store($cache_key, $cat_genre_array, 86400); // Cache for 1 day
        }

        foreach ($cat_genre_array as $genre_id) {
            $return_array[] = $all_genre_array[$genre_id];
        }

        return $return_array;
    }

    public function get_game_language($categories_id, $all_language_array) {
        global $memcache_obj;
        $cat_language_array = array();
        $return_array = array();

        $cache_key = TABLE_GAME_LANGUAGE_TO_CATEGORIES . '/game_language_id/array/categories_id/' . $categories_id;
        $cache_result = $memcache_obj->fetch($cache_key);

        if ($cache_result !== FALSE) {
            $cat_language_array = $cache_result;
        } else {
            $get_category_language_select = "	SELECT game_language_id
												FROM " . TABLE_GAME_LANGUAGE_TO_CATEGORIES . "
												WHERE categories_id = " . $categories_id;
            $get_category_language_query = tep_db_query($get_category_language_select);
            while ($get_category_language_row = tep_db_fetch_array($get_category_language_query)) {
                $cat_language_array[] = $get_category_language_row['game_language_id'];
            }

            $memcache_obj->store($cache_key, $cat_language_array, 86400); // Cache for 1 day
        }

        foreach ($cat_language_array as $game_language_id) {
            $return_array[] = $all_language_array[$game_language_id];
        }

        unset($cat_language_array);

        return $return_array;
    }

    public function get_game_platform($categories_id, $all_platform_array) {
        global $memcache_obj;
        $cat_platform_array = array();
        $return_array = array();

        $cache_key = TABLE_GAME_PLATFORM_TO_CATEGORIES . '/game_platform_id/array/categories_id/' . $categories_id;
        $cache_result = $memcache_obj->fetch($cache_key);

        if ($cache_result !== FALSE) {
            $cat_platform_array = $cache_result;
        } else {
            $get_category_platform_select = "	SELECT game_platform_id
												FROM " . TABLE_GAME_PLATFORM_TO_CATEGORIES . "
												WHERE categories_id = " . $categories_id;
            $get_category_platform_query = tep_db_query($get_category_platform_select);
            while ($get_category_platform_row = tep_db_fetch_array($get_category_platform_query)) {
                $cat_platform_array[] = $get_category_platform_row['game_platform_id'];
            }

            $memcache_obj->store($cache_key, $cat_platform_array, 86400); // Cache for 1 day
        }

        foreach ($cat_platform_array as $platform_id) {
            $return_array[] = $all_platform_array[$platform_id];
        }

        return $return_array;
    }

    private function get_selected_css_class($default, $selected, $delimiter = '_expand', $append = true) {
        if ($selected) {
            $default .= ($append ? ' ' . $default : '') . $delimiter;
        }

        return $default;
    }

    public function get_left_region_box() { // Not in used after change to new layout 2011
        global $languages_id;
        $region_n_language_array = array();
        $return_string = '<div class="boxHeader" style="margin-bottom:1px;">
								<div class="boxHeaderLeft"><!-- --></div>
								<div class="boxHeaderCenter">
									<font>' . HEADING_TITLE_GAME_REGION . '</font>
								</div>
								<div class="boxHeaderRight"><!-- --></div>
							</div>';

        $game_and_region_select = "	SELECT 	grtl.game_region_to_language_id, grtl.game_region_id, grtl.default_setting,
									gld.game_language_description,
									grd.game_region_description
									FROM " . TABLE_GAME_REGION_TO_LANGUAGE . " AS grtl
									INNER JOIN " . TABLE_GAME_LANGUAGE_DESCRIPTION . " AS gld
										ON grtl.game_language_id = gld.game_language_id
									INNER JOIN " . TABLE_GAME_LANGUAGE . " AS gl
										ON grtl.game_language_id = gl.game_language_id
									INNER JOIN " . TABLE_GAME_REGION_DESCRIPTION . " AS grd
										ON grtl.game_region_id = grd.game_region_id
									INNER JOIN " . TABLE_GAME_REGION . " AS gr
										ON grtl.game_region_id = gr.game_region_id
									WHERE gld.language_id = grd.language_id AND gld.language_id = " . $languages_id . "
									ORDER BY gr.sort_order, gl.sort_order";
        $game_and_region_query = tep_db_query($game_and_region_select);
        while ($game_and_region_row = tep_db_fetch_array($game_and_region_query)) {
            $region_id = $game_and_region_row['game_region_id'];

            $region_n_language_array[$region_id]['game_region_description'] = $game_and_region_row['game_region_description'];
            $region_n_language_array[$region_id]['selected_grtl_id'] = ($game_and_region_row['default_setting'] == 1) ? $game_and_region_row['game_region_to_language_id'] : $region_n_language_array[$region_id]['selected_grtl_id'];
            $region_n_language_array[$region_id]['game_languages_description'][$game_and_region_row['game_region_to_language_id']] = $game_and_region_row['game_language_description'];
        }

        foreach ($region_n_language_array AS $region_id => $region_array) {
            $region_selected = false;
            $region_selected_style = "display: none;";

            if ($region_id == $this->selected_game_region_id) {
                $region_selected = true;
                $region_selected_style = "display: block;";
                $region_array['selected_grtl_id'] = $this->selected_grtl_id;
            }

            $return_string .= '<li class="ie_fix" style="list-style:none outside none;"><div id="gr' . $region_id . '" class="' . $this->get_selected_css_class('gr_container', $region_selected) . '" onclick="game_region_toggle(this, \'' . $region_array['selected_grtl_id'] . '\');">
									<div class="gr_partition">
										<div class="' . $this->get_selected_css_class('gr_tl', $region_selected) . '"></div>
										<div class="' . $this->get_selected_css_class('gr_tm', $region_selected) . '"></div>
										<div class="' . $this->get_selected_css_class('gr_tr', $region_selected) . '"></div>
									</div>
									<!-- Middle -->
									<div class="' . $this->get_selected_css_class('gr_ml', $region_selected) . '">
										<font>' . $region_array['game_region_description'] . '</font>
									</div>
									<div class="' . $this->get_selected_css_class('gr_mr', $region_selected) . '"><div><!-- --></div></div>
									<!-- Middle -->
									<!-- Bottom -->
									<div class="gr_partition">
										<div class="' . $this->get_selected_css_class('gr_bl', $region_selected) . '"></div>
										<div class="' . $this->get_selected_css_class('gr_bm', $region_selected) . '"></div>
										<div class="' . $this->get_selected_css_class('gr_br', $region_selected) . '"></div>
									</div>
									<!-- Bottom -->
								</div></li>';

            $return_string .= '<div id="gr' . $region_id . '_long" class="gr_content" style="' . $region_selected_style . '">';
            $return_string .= '<ul id="gl' . $region_id . '">';

            foreach ($region_array['game_languages_description'] AS $grtl_id => $language_description) {
                $css_class = ($region_array['selected_grtl_id'] == $grtl_id ? 'gr_content_selected' : '');
                $return_string .= '<li><a id="l' . $grtl_id . '" href="javascript:void(0);" class="' . $css_class . '" onclick="game_region_language_toggle(' . $region_id . ',' . $grtl_id . ');">' . $language_description . '</a></li>';
            }

            $return_string .= '</ul>';
            $return_string .= '<div class="gr_partition">
									<div class="gr_bl2_expand"></div>
									<div class="gr_bm gr_bm_expand"></div>
									<div class="gr_br2_expand"></div>
								</div>';
            $return_string .= '</div>';
        }

        return $return_string;
    }

}
?>
