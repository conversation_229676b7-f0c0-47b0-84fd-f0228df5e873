<?php

require_once(DIR_WS_CLASSES . 'curl.php');

class shasso {

    const COUNTRY_STATIC_DOMAIN = 'https://d14lt5r6ptglkw.cloudfront.net/data/localization/country.json';
    const REGION_STATIC_DOMAIN = 'https://d14lt5r6ptglkw.cloudfront.net/data/localization/region.json';
    const SSO_S3ID_COOKIE_DURATION = 86400;     // 60 * 60 * 24;
    const SSO_S3RM_COOKIE_DURATION = 2592000;   // 30 * 60 * 60 * 24

    public $curl_obj,
            $lang,
            $input;
    private $api_url,
            $gui_url,
            $origin, $path,
            $service,
            $secret;

    public function __construct($data = array()) {
        $this->curl_obj = new curl();
        $this->curl_obj->connect_via_proxy = false;

        $this->lang = $_SESSION['language_code_ses'];

        $this->input = array();
        $this->setInputs($data);

        $this->api_url = HTTP_SHASSO_PORTAL . '/api/sso';
        $this->gui_url = HTTP_SHASSO_PORTAL . '/sso/index';
        $this->service = SHASSO_CLIENT_ID;
        $this->secret = SHASSO_CLIENT_SECRET;
        $this->origin = urlencode(rtrim(HTTP_SERVER, '/'));
    }

    public function setInputs($data) {
        $safe_data = $this->purify($data, 'GET');
        $cookie_safe_data = $this->purify($_COOKIE, 'COOKIE');

        if (isset($safe_data['S3ID'])) {
            $this->input['S3ID_method'] = 'GET';
            $this->input['S3ID'] = $safe_data['S3ID'];
        } else if (isset($cookie_safe_data['OLD_S3ID'])) {
            $this->input['S3ID_method'] = 'COOKIE';
            $this->input['S3ID'] = $cookie_safe_data['OLD_S3ID'];
        }

        if (isset($safe_data['S3RM']) && $safe_data['S3RM'] == 1) {
            $this->input['S3RM'] = 'GET';
        } else if (isset($cookie_safe_data['OLD_S3RM']) && $cookie_safe_data['OLD_S3RM'] == 1) {
            $this->input['S3RM'] = 'COOKIE';
        }

        if (isset($safe_data['next_url'])) {
            $this->input['next_url'] = $safe_data['next_url'];
        }
    }

    public function init() {
        $return_array = array();

        if (isset($_SESSION['customer_id']) && $_SESSION['customer_id']) {
            if (isset($this->input['S3ID'])) {
                $this->requestClearID();

                if ($data = $this->requestUID()) {
                    if ($data['user_id'] !== $_SESSION['customer_id']) {
                        authenticate::logoff();

                        if (authenticate::Login($data['user_id'])) {
                            $this->afterLogin($data);
                        }
                    }
                } else {
                    authenticate::logoff();
                    $this->requestClearRME();
                }
            }
        } else if ($this->input) {
            if (isset($this->input['S3ID'])) {
                $this->requestClearID();

                if ($data = $this->requestUID()) {
                    if (authenticate::Login($data['user_id'])) {
                        $this->afterLogin($data);
                    }
                }
            } else if (isset($this->input['S3RM'])) {
                $this->requestClearRME();

                $return_array['redirect_url'] = $this->getLoginURL();
            }
        }

        if (!isset($return_array['redirect_url'])) {
            if ($redirect_url = $this->getNextURL()) {
                $return_array['redirect_url'] = $redirect_url;
            }
        }

        return $return_array;
    }

    public function requestUID($S3ID = NULL) {
        $return_array = array();
        $url = $this->api_url . '/requestUID';

        if ($response = $this->prepareRequest($url, $S3ID)) {
            if ($response["status"] === true && isset($response["result"][0]["user_id"])) {
                $return_array = $response['result'][0];
            }
        }
        
        return $return_array;
    }

    private function afterLogin($data) {
        $safe_data = $this->purify($data);

        if (isset($this->input['S3RM']) && $this->input['S3RM'] === 'GET') {
            $this->requestSetRME();
        }

        if (isset($safe_data['avatar']) && tep_not_empty($safe_data['avatar'])) {
            $_SESSION['user_avatar'] = $safe_data['avatar'];
        }

        if (isset($safe_data['login_method'])) {
            $_SESSION['login_method'] = $safe_data['login_method'];

            if ($_SESSION['login_method'] !== 'Normal') { // is sns login
                $_SESSION['fb_uid'] = 1;
            }
        }
    }

    public function requestClearAllCookie() {
        $this->requestClearID();
        $this->requestClearRME();
    }

    private function requestClearID() {
        global $cookie_path, $cookie_domain;
        $expire = time() - self::SSO_S3ID_COOKIE_DURATION;

        tep_setcookie('OLD_S3ID', '', $expire, $cookie_path, $cookie_domain);
    }

    private function requestClearRME() {
        global $cookie_path, $cookie_domain;
        $expire = time() - self::SSO_S3RM_COOKIE_DURATION;

        tep_setcookie('OLD_S3RM', '', $expire, $cookie_path, $cookie_domain);
    }

    public function requestSetID($value = NULL) {
        global $cookie_path, $cookie_domain;
        $value = $value !== NULL ? $value : $this->input['S3ID'];
        $expire = time() + self::SSO_S3ID_COOKIE_DURATION;

        tep_setcookie('OLD_S3ID', $value, $expire, $cookie_path, $cookie_domain);
    }

    public function requestSetRME($value = 1) {
        global $cookie_path, $cookie_domain;
        $expire = time() + self::SSO_S3RM_COOKIE_DURATION;

        tep_setcookie('OLD_S3RM', $value, $expire, $cookie_path, $cookie_domain);
    }

    private function generateSignature($data) {
        return md5($data['service'] . $data['S3ID'] . $this->secret);
    }

    private function generateRequestString($S3ID = NULL) {
        $data = array(
            'S3ID' => ($S3ID !== NULL ? $S3ID : $this->input['S3ID']),
            'service' => $this->service
        );
        $data['signature'] = $this->generateSignature($data);

        return $data;
    }

    public function getAvatar() {
        return isset($_SESSION['user_avatar']) ? $_SESSION['user_avatar'] : '';
    }

    public function getGUIURL($sns = '', $default_page = '', $lang = '', $extended_param = array()) {
        $path = '';
        $url_ext = '';
        $lang = $lang != '' ? $lang : $this->lang;
        
        if ($default_page) {
            $path = $default_page;
        } else if ($this->path) {
            $path = $this->path;
        } else if (isset($_SERVER['REQUEST_URI']) && tep_not_null($_SERVER['REQUEST_URI']) && strpos($_SERVER['REQUEST_URI'], FILENAME_LOGOFF) !== 1 && strpos($_SERVER['REQUEST_URI'], FILENAME_LOGIN) !== 1) {
            $path = $_SERVER['REQUEST_URI'];

            if ((ENABLE_SSL == true) && (getenv('HTTPS') == 'on')) { // We are loading an SSL page
                $path = substr(HTTPS_SERVER, 0, -1) . $path;
            } else {
                $path = substr(HTTP_SERVER, 0, -1) . $path;
            }
            $this->path = $path;
        } else {
            $this->path = $path = tep_href_link(FILENAME_DEFAULT, '', 'SSL');
        }

        if ($sns) {
            $url_ext .= '&sns=' . $sns;
        }
        
        if (isset($extended_param['next_url_ext'])) {
            $query = parse_url($path, PHP_URL_QUERY);
            
            if($query) {
                $path .= '&';
            } else {
                $path .= '?';
            }
            
            $path .= http_build_query($extended_param['next_url_ext'], null, '&');
        }
        
        return $this->gui_url . "?origin=" . $this->origin . "&service=" . $this->service . "&hl=" . $lang . "&next_url=" . urlencode($path) . $url_ext;
    }
    
    public function getPageURL($action, $next_url = '') {
        if ($next_url) {
            $next_url = "&next_url=" . urlencode($next_url);
        }
        
        return HTTP_SHASSO_PORTAL . $action . "?origin=" . $this->origin . "&service=" . $this->service . "&hl=" . $this->lang . $next_url;
    }

    public function getNextURL() {
        $path = '';

        if (isset($this->input['next_url'])) {
            $path = $this->input['next_url'];
        }

        return $path;
    }

    public function getJsonURL($type) {
        $return_str = '';

        if ($type == 'region') {
            $return_str = self::REGION_STATIC_DOMAIN;
        } else if ($type == 'country') {
            $return_str = self::COUNTRY_STATIC_DOMAIN;
        }

        return $return_str;
    }

    public function getSignupURL($default_page = '', $extended_param = array()) {
        return $this->getGUIURL('', $default_page, $this->lang, $extended_param) . '&action=signup';
    }

    public function getLoginURL($sns = '', $default_page = '', $extended_param = array()) {
        return $this->getGUIURL($sns, $default_page, $this->lang, $extended_param) . '&action=login';
    }

    public function getLogoutURL($default_page = '') {
        $default_page = $default_page != '' ? $default_page : tep_href_link(FILENAME_LOGOFF, '', 'SSL');
        return $this->getGUIURL('', $default_page, $this->lang) . '&action=logout';
    }

    private function prepareRequest($url, $S3ID = NULL) {
        $return_array = array();

        if ($response = $this->curl_obj->curl_post($url, $this->generateRequestString($S3ID))) {
            $return_array = json_decode($response, true);
        }

        return $return_array;
    }

    private function purify($data, $method = '') {
        $return_data = array();

        switch ($method) {
            case 'GET':
                if (isset($data['S3ID'])) {
                    if (($str = filter_var($data['S3ID'], FILTER_SANITIZE_STRING, FILTER_FLAG_NO_ENCODE_QUOTES)) !== FALSE) {
                        $return_data['S3ID'] = $str;
                    }
                }

                if (isset($data['S3RM'])) {
                    if (($int = filter_var($data['S3RM'], FILTER_SANITIZE_NUMBER_INT)) !== FALSE) {
                        $return_data['S3RM'] = $int;
                    }
                }
                break;
            case 'COOKIE':
                if (isset($data['OLD_S3ID'])) {
                    if (($str = filter_var($data['OLD_S3ID'], FILTER_SANITIZE_STRING, FILTER_FLAG_NO_ENCODE_QUOTES)) !== FALSE) {
                        $return_data['OLD_S3ID'] = $str;
                    }
                }

                if (isset($data['OLD_S3RM'])) {
                    if (($int = filter_var($data['OLD_S3RM'], FILTER_SANITIZE_NUMBER_INT)) !== FALSE) {
                        $return_data['OLD_S3RM'] = $int;
                    }
                }
                break;
        }

        if (isset($data['next_url'])) {
            $URI_components = parse_url($data['next_url']);

            if (isset($URI_components['host']) && strpos(HTTP_SERVER, $URI_components['host']) !== false) {
                $return_data['next_url'] = $data['next_url'];
            }
        }

        if (isset($data['user_id'])) {
            if (($int = filter_var($data['user_id'], FILTER_SANITIZE_NUMBER_INT)) !== FALSE) {
                $return_data['user_id'] = $int;
            }
        }

        if (isset($data['avatar'])) {
            if (($str = filter_var($data['avatar'], FILTER_SANITIZE_STRING, FILTER_FLAG_NO_ENCODE_QUOTES)) !== FALSE) {
                $return_data['avatar'] = $str;
            }
        }

        if (isset($data['login_method'])) {
            if (($str = filter_var($data['login_method'], FILTER_SANITIZE_STRING, FILTER_FLAG_NO_ENCODE_QUOTES)) !== FALSE) {
                $return_data['login_method'] = $str;
            }
        }

        return $return_data;
    }

}