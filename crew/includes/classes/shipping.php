<?
/*
  	$Id: shipping.php,v 1.4 2005/05/18 06:36:17 weichen Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2003 osCommerce
	
  	Released under the GNU General Public License
*/

class shipping
{
    var $modules;
	
	// class constructor
    function shipping($module = '')
    {
		// BOF: WebMakers.com Added: Downloads Controller
      	global $language, $PHP_SELF, $cart;
		// EOF: WebMakers.com Added: Downloads Controller
		
		// BOF: Individual Shipping Prices      
      	$shiptotal = $cart->get_shiptotal();
		// EOF: Individual Shipping Prices
		
      	if (defined('MODULE_SHIPPING_INSTALLED') && tep_not_null(MODULE_SHIPPING_INSTALLED)) {
        	$this->modules = explode(';', MODULE_SHIPPING_INSTALLED);
			
        	$include_modules = array();
			
        	if ( (tep_not_null($module)) && (in_array(substr($module['id'], 0, strpos($module['id'], '_')) . '.' . substr($PHP_SELF, (strrpos($PHP_SELF, '.')+1)), $this->modules)) ) {
          		$include_modules[] = array('class' => substr($module['id'], 0, strpos($module['id'], '_')), 'file' => substr($module['id'], 0, strpos($module['id'], '_')) . '.' . substr($PHP_SELF, (strrpos($PHP_SELF, '.')+1)));
        	} else {
          		reset($this->modules);
				
				// BOF: WebMakers.com Added: Downloads Controller - Free Shipping and Payments
				// Show either normal shipping modules or free shipping module when Free Shipping Module is On
          		// Free Shipping Only
          		if (tep_get_configuration_key_value('MODULE_SHIPPING_FREESHIPPER_STATUS') and $cart->show_weight()==0) {
            		$include_modules[] = array('class'=> 'freeshipper', 'file' => 'freeshipper.php');
            	}
          		if (tep_get_configuration_key_value('MODULE_SHIPPING_INDVSHIP_STATUS') and $shiptotal) {
        			$include_modules[] = array('class'=> 'indvship', 'file' => 'indvship.php');
      			} else {
      				// All Other Shipping Modules
        			while (list(, $value) = each($this->modules)) {
          				$class = substr($value, 0, strrpos($value, '.'));
          				// Don't show Free Shipping Module
          				if ($class !='freeshipper') {
							if ($class != 'indvship') {
            					$include_modules[] = array('class' => $class, 'file' => $value);
            				}
            			}
        			}
      			}
				// EOF: WebMakers.com Added: Downloads Controller - Free Shipping and Payments
        	}
			
        	for ($i=0, $n=sizeof($include_modules); $i<$n; $i++) {
          		include(DIR_WS_LANGUAGES . $language . '/modules/shipping/' . $include_modules[$i]['file']);
          		include(DIR_WS_MODULES . 'shipping/' . $include_modules[$i]['file']);
				
          		$GLOBALS[$include_modules[$i]['class']] = new $include_modules[$i]['class'];
        	}
      	}
	}
	
    function quote($method = '', $module = '')
    {
      	global $total_weight, $shipping_weight, $shipping_quoted, $shipping_num_boxes, $padding_weight;
		
      	$quotes_array = array();
		
      	if (is_array($this->modules)) {
        	$shipping_quoted = '';
        	$shipping_num_boxes = 1;
        	$shipping_weight = $total_weight;
			
        	if (SHIPPING_BOX_WEIGHT >= $shipping_weight*SHIPPING_BOX_PADDING/100) {
        		$padding_weight = SHIPPING_BOX_WEIGHT;
          		$shipping_weight = $shipping_weight + SHIPPING_BOX_WEIGHT;
        	} else {
        		$padding_weight = $shipping_weight * SHIPPING_BOX_PADDING/100;
          		$shipping_weight = $shipping_weight + ($shipping_weight*SHIPPING_BOX_PADDING/100);
        	}
			
        	if ($shipping_weight > SHIPPING_MAX_WEIGHT) { // Split into many boxes
          		$shipping_num_boxes = ceil($shipping_weight/SHIPPING_MAX_WEIGHT);
          		$shipping_weight = $shipping_weight/$shipping_num_boxes;
        	}
			
        	$include_quotes = array();
			
        	reset($this->modules);
        	while (list(, $value) = each($this->modules)) {
          		$class = substr($value, 0, strrpos($value, '.'));
          		if (tep_not_null($module)) {
            		if ( ($module == $class) && ($GLOBALS[$class]->enabled) ) {
              			$include_quotes[] = $class;
            		}
          		} else if ($GLOBALS[$class]->enabled) {
            		$include_quotes[] = $class;
          		}
        	}
			
        	$size = sizeof($include_quotes);
        	for ($i=0; $i<$size; $i++) {
          		$quotes = $GLOBALS[$include_quotes[$i]]->quote($method);
          		if (is_array($quotes)) $quotes_array[] = $quotes;
        	}
      	}
		
      	return $quotes_array;
    }
	
    function cheapest()
    {
      	if (is_array($this->modules)) {
        	$rates = array();
			
        	reset($this->modules);
        	while (list(, $value) = each($this->modules)) {
          		$class = substr($value, 0, strrpos($value, '.'));
          		if ($GLOBALS[$class]->enabled) {
            		$quotes = $GLOBALS[$class]->quotes;
            		for ($i=0, $n=sizeof($quotes['methods']); $i<$n; $i++) {
              			if (isset($quotes['methods'][$i]['cost']) && tep_not_null($quotes['methods'][$i]['cost'])) {
                			$rates[] = array(	'id' => $quotes['id'] . '_' . $quotes['methods'][$i]['id'],
                                 				'title' => $quotes['module'] . ' (' . $quotes['methods'][$i]['title'] . ')',
                                 				'cost' => $quotes['methods'][$i]['cost']);
              			}
            		}
          		}
        	}
			
        	$cheapest = false;
        	for ($i=0, $n=sizeof($rates); $i<$n; $i++) {
          		if (is_array($cheapest)) {
            		if ($rates[$i]['cost'] < $cheapest['cost']) {
              			$cheapest = $rates[$i];
            		}
          		} else {
            		$cheapest = $rates[$i];
          		}
        	}
			
        	return $cheapest;
      	}
	}
	
	function javascript_validation()
    {
    	$js = '';
      	if (is_array($this->modules)) {
        	$js = 	'<script language="javascript"><!-- ' . "\n" .
        			'	function check_form() {' . "\n" .
              		'  		var error = 0;' . "\n" .
		            '  		var error_message = "' . JS_ERROR . '";' . "\n" .
              		'  		var shipping_value = null;' . "\n" . 
              		'		if (typeof(document.checkout_address.shipping) != "undefined") {' . "\n" .
              		'  			if (document.checkout_address.shipping.length) {' . "\n" .
              		'    			for (var i=0; i<document.checkout_address.shipping.length; i++) {' . "\n" .
              		'      				if (document.checkout_address.shipping[i].checked) {' . "\n" .
              		'        				shipping_value = document.checkout_address.shipping[i].value;' . "\n" .
              		'      				}' . "\n" .
              		'    			}' . "\n" .
              		'  			} else if (document.checkout_address.shipping.checked) {' . "\n" .
              		'    			shipping_value = document.checkout_address.shipping.value;' . "\n" .
              		'  			} else if (document.checkout_address.shipping.value) {' . "\n" .
              		'    			shipping_value = document.checkout_address.shipping.value;' . "\n" .
              		'  			}' . "\n\n";
			/*
        	reset($this->modules);
        	while (list(, $value) = each($this->modules)) {
          		$class = substr($value, 0, strrpos($value, '.'));
          		if ($GLOBALS[$class]->enabled) {
            		$js .= $GLOBALS[$class]->javascript_validation();
          		}
        	}
			*/
        	$js .=  '  			if (shipping_value == null) {' . "\n" . 
            	   	'    			error_message = error_message + "' . JS_ERROR_NO_SHIPPING_MODULE_SELECTED . '";' . "\n" .
               		'    			error = 1;' . "\n" .
               		'  			}' . "\n\n" .
               		'  			if (error == 1) {' . "\n" .
               		'    			alert(error_message);' . "\n" .
               		'    			return false;' . "\n" .
               		'  			} else {' . "\n" .
               		'    			return true;' . "\n" .
               		'  			}' . "\n" .
               		'  		}' . "\n" .
               		'	}' . "\n" .
               		'//--></script>' . "\n";
		}
      	return $js;
	}
}
?>