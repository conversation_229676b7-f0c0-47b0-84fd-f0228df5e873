<?
/*
  	$Id: shopping_cart.php,v 1.111 2014/12/29 12:16:11 weesiong Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2003 osCommerce
	
  	Released under the GNU General Public License
*/

class shoppingCart
{
	var $contents, $total, $weight, $cartID, $content_type;
	// mod indvship
	var $shiptotal;
	var $custom_product_index;
    var $mm_contents; // this parameter should be in private but session table special character cause search customer online status failed.
	// end indvship
	
	function shoppingCart($reset_cart = true) {
        if ($reset_cart) {
            $this->reset();
        }
	}
	
	function get_custom_product_content($product_id) {
		global $customer_id;
		
		$final = array();
      	$custom_type = $this->get_custom_prd_type($product_id);
      	
      	if (tep_not_null($custom_type)) {
			$final['custom'][$custom_type] = array();
			$temp = array();
			
			//	Powerleveling, CD Key and Game Currency
			// get basket id first
			$result = tep_db_query("SELECT a.customers_basket_quantity, b.customers_basket_custom_value 
									FROM " . TABLE_CUSTOMERS_BASKET . " AS a, ".TABLE_CUSTOMERS_BASKET_CUSTOM." AS b 
									WHERE a.customers_id = '" . (int)$customer_id . "' 
										AND a.products_id = '" . (int)$product_id . "' 
										AND a.customers_basket_id = b.customers_basket_id;");
			
			if (tep_db_num_rows($result)) {
				
				if ($custom_type == 'power_leveling') {
					while($row = tep_db_fetch_array($result)) {
						$temp = $this->_unserialize($row['customers_basket_custom_value']);
						$content = array('content' => $temp);
						$final['custom'][$custom_type][] = $content;
					}
				} else {
					if ($row = tep_db_fetch_array($result)) {
						$final['qty'] = $row['customers_basket_quantity'];
						$temp = $this->_unserialize($row['customers_basket_custom_value']);
						if (/*$custom_type == 'cdkey' || */$custom_type == 'store_credit') {
							$content = array('qty' => $temp);
							$final['custom'][$custom_type][] = $content;
						} else {
							$content = $temp;
							$final['custom'][$custom_type] = $content;
						}
					}
				}
			}
		}
		
		return $final;
	}
	
    function get_custom_prd_type($products_id) {
    	$custom_type = '';
    	if ((int)$products_id > 0) {
	    	$product_type_select_sql = "SELECT custom_products_type_id, products_bundle, products_bundle_dynamic 
						    			FROM " . TABLE_PRODUCTS . " 
						    			WHERE products_id = '" . tep_db_input($products_id) . "'";
	    	$product_type_result_sql = tep_db_query($product_type_select_sql);
	    	$product_type_row = tep_db_fetch_array($product_type_result_sql);
			
			if ($product_type_row['products_bundle'] != 'yes' && $product_type_row['products_bundle_dynamic'] != 'yes') {	// Single Product
				switch ($product_type_row['custom_products_type_id']) {
					case 0:
						$custom_type = 'game_currency';
						break;
					case 1:
						$custom_type = 'power_leveling';
						break;
					case 2:
						$custom_type = 'cdkey';
						break;
					case 3:
						$custom_type = 'store_credit';
						break;
					case 4:
						$custom_type = 'hla';
						break;
				}
			} else {	// Package (Static or Dynamic)
				// NOTE: Assum package can only contain ONE product type
				$subproduct_type_select_sql = "	SELECT p.custom_products_type_id 
								    			FROM " . TABLE_PRODUCTS . " AS p 
												INNER JOIN " . TABLE_PRODUCTS_BUNDLES . " AS pb 
													ON p.products_id = pb.subproduct_id 
								    			WHERE pb.bundle_id = '" . tep_db_input($products_id) . "'
												LIMIT 1";
				$subproduct_type_result_sql = tep_db_query($subproduct_type_select_sql);
				$subproduct_type_row = tep_db_fetch_array($subproduct_type_result_sql);
				switch ($subproduct_type_row['custom_products_type_id']) {
					case 0:
						$custom_type = 'game_currency';
						break;
					case 1:
						$custom_type = 'power_leveling';
						break;
					case 2:
						$custom_type = 'cdkey';
						break;
					case 3:
						$custom_type = 'store_credit';
						break;
					case 4:
						$custom_type = 'hla';
						break;
				}
			}
    	}
    	return $custom_type;
    }
	
    function restore_contents() {
		// ICW replace line
      	global $customer_id, $gv_id;
		
      	if (!tep_session_is_registered('customer_id')) return false;
		
		// insert current cart contents in database
      	if (is_array($this->contents)) {
        	reset($this->contents);
        	while (list($products_id, ) = each($this->contents)) {
        		$prod_cat_path = tep_get_product_path($products_id, true);
				if (tep_not_null($prod_cat_path)) {
					$prod_cat_path_array = explode('_', $prod_cat_path);
					$game_id = $prod_cat_path_array[0];
				} else {
					$game_id = 0;
				}
				
				$custom_type = $this->get_custom_prd_type($products_id);
							
				if (sizeof($this->contents[$products_id]['custom'][$custom_type]) > 0) {
					if ($custom_type == 'cdkey') { 
						$qty = $this->contents[$products_id]['qty'];
						
						$product_sql = "SELECT customers_basket_id, customers_basket_quantity 
										FROM " . TABLE_CUSTOMERS_BASKET . " 
										WHERE customers_id = '" . (int)$customer_id . "' 
											AND products_id = '" . tep_db_input($products_id) . "'";
						$product_query = tep_db_query($product_sql);
						if (!tep_db_num_rows($product_query)) {
	            			tep_db_query("INSERT INTO " . TABLE_CUSTOMERS_BASKET . " (customers_id, products_id, customers_basket_quantity, customers_basket_date_added, products_categories_id) VALUES ('" . (int)$customer_id . "', '" . tep_db_input($products_id) . "', '" . $qty . "', '" . date('Ymd') . "', '" . $game_id . "')");
		            		$last_insert_id = tep_db_insert_id();
							$serialized_game_currency_array = $this->_serialize($this->contents[$products_id]['custom']['cdkey']);
							
							tep_db_query("INSERT INTO " . TABLE_CUSTOMERS_BASKET_CUSTOM . " (customers_basket_id, customers_basket_custom_key,  customers_basket_custom_value) VALUES ('" . (int)$last_insert_id . "', 'cdkey', '" . $serialized_game_currency_array ."')");
						} else {
							$product_row = tep_db_fetch_array($product_query);
							
	            			$latest_qty = $product_row['customers_basket_quantity'] + (int)$qty;
							tep_db_query("UPDATE " . TABLE_CUSTOMERS_BASKET . " SET customers_basket_quantity = '" . (int)$latest_qty . "' WHERE customers_id = '" . (int)$customer_id . "' AND products_id = '" . tep_db_input($products_id) . "'");
							
							$customers_basket_custom_value_select_sql = "SELECT customers_basket_custom_value FROM " . TABLE_CUSTOMERS_BASKET_CUSTOM . " WHERE customers_basket_id = '" . (int)$product_row['customers_basket_id'] . "' AND customers_basket_custom_key = 'cdkey'";
							$customers_basket_custom_value_result_sql = tep_db_query($customers_basket_custom_value_select_sql);
							$customers_basket_custom_value_row = tep_db_fetch_array($customers_basket_custom_value_result_sql);
							
							$custom_value_array = $this->_unserialize($customers_basket_custom_value_row['customers_basket_custom_value']);
							
							if (isset($custom_value_array) && count($custom_value_array)) {
								foreach ($custom_value_array as $custom_value_index_loop => $custom_value_data_loop) {
									ksort($custom_value_data_loop['top_up_info']);
									reset($custom_value_data_loop['top_up_info']);
									
									$count_index = 0;
									foreach ($this->contents[$products_id]['custom']['cdkey'] as $cdkey_index_loop => $cdkey_data_loop) {
										if (isset($cdkey_data_loop['top_up_info'])) {
											ksort($cdkey_data_loop['top_up_info']);
											reset($cdkey_data_loop['top_up_info']);
										} else {
											$cdkey_data_loop['top_up_info'] = array();
										}
										
										if (isset($custom_value_data_loop['top_up_info'])) {
											reset($custom_value_data_loop['top_up_info']);
										} else {
											$custom_value_data_loop['top_up_info'] = array();
										}
										
										if ($custom_value_data_loop['delivery_mode'] == $cdkey_data_loop['delivery_mode'] && 
											count(array_diff_assoc($custom_value_data_loop['top_up_info'], $cdkey_data_loop['top_up_info']))==0 &&
											count(array_diff_assoc($cdkey_data_loop['top_up_info'], $custom_value_data_loop['top_up_info']))==0 ) {
											break;
										}
										$count_index++;
									}
									
									if (isset($this->contents[$products_id]['custom']['cdkey'][$count_index])) {
										$this->contents[$products_id]['custom']['cdkey'][$count_index]['qty'] = $this->contents[$products_id]['custom']['cdkey'][$count_index]['qty'] + $custom_value_data_loop['qty'];
									} else {
										$this->contents[$products_id]['custom']['cdkey'][$count_index] = array(	'qty' => $custom_value_data_loop['qty'], 
																												'delivery_mode' => $custom_value_data_loop['delivery_mode'], 
																												'top_up_info' => $custom_value_data_loop['top_up_info']);
									}
								}
							}
							tep_db_query("UPDATE " . TABLE_CUSTOMERS_BASKET_CUSTOM . " SET customers_basket_custom_value = '" . $this->_serialize($this->contents[$products_id]['custom']['cdkey']) . "' WHERE customers_basket_id = '" . (int)$product_row['customers_basket_id'] . "' AND customers_basket_custom_key = 'cdkey'");
						}
					} else if ($custom_type == 'store_credit') { 
						//store_credit
						$qty = $this->contents[$products_id]['qty'];
						$product_query = tep_db_query("SELECT products_id FROM " . TABLE_CUSTOMERS_BASKET . " WHERE customers_id = '" . (int)$customer_id . "' AND products_id = '" . tep_db_input($products_id) . "'");
	          			if (!tep_db_num_rows($product_query)) {
	          				tep_db_query("INSERT INTO " . TABLE_CUSTOMERS_BASKET . " (customers_id, products_id, customers_basket_quantity, customers_basket_date_added, products_categories_id) VALUES ('" . (int)$customer_id . "', '" . tep_db_input($products_id) . "', '" . $qty . "', '" . date('Ymd') . "', '" . $game_id . "')");
	          				$last_insert_id = tep_db_insert_id();
							
							tep_db_query("DELETE FROM " . TABLE_CUSTOMERS_BASKET_CUSTOM . " WHERE customers_basket_id = '" . (int)$last_insert_id . ";'");
							
							$serialized_array = $this->_serialize($this->contents[$products_id]['custom'][$custom_type][0]['qty']);
							
							tep_db_query("INSERT INTO " . TABLE_CUSTOMERS_BASKET_CUSTOM . " (customers_basket_id,customers_basket_custom_key,customers_basket_custom_value) VALUES ('" . (int)$last_insert_id . "', '" . $custom_type . "', '" . $serialized_array . "')");
	          			} else {
							$qty = (int)$qty;
	            			tep_db_query("UPDATE " . TABLE_CUSTOMERS_BASKET . " SET customers_basket_quantity = customers_basket_quantity + $qty WHERE customers_id = '" . (int)$customer_id . "' AND products_id = '" . tep_db_input($products_id) . "'");
	          			}
					} else if ($custom_type == 'power_leveling') {
						//power leveling
						foreach($this->contents[$products_id]['custom'][$custom_type] as $cust) {
							tep_db_query("INSERT INTO " . TABLE_CUSTOMERS_BASKET . " (customers_id, products_id, customers_basket_quantity, final_price, customers_basket_date_added, products_categories_id) VALUES ('" . (int)$customer_id . "', '" .(int)($products_id) . "', '1', '".$cust['calculated']['price']."' ,'" . date('Ymd') . "', '" . $game_id . "')");					
							$last_insert_id = tep_db_insert_id();
							
							tep_db_query("DELETE FROM " . TABLE_CUSTOMERS_BASKET_CUSTOM . " WHERE customers_basket_id = '" . (int)$last_insert_id . ";'");
								
							$serialized_array = $this->_serialize($cust['content']);
							tep_db_query("INSERT INTO " . TABLE_CUSTOMERS_BASKET_CUSTOM . " (customers_basket_id,customers_basket_custom_key,customers_basket_custom_value) VALUES ('" . (int)$last_insert_id . "', '" . $custom_type . "', '" . $serialized_array . "')");
						}
					} else if ($custom_type == 'hla') {
						//high level account
						$product_query = tep_db_query("SELECT customers_basket_id FROM " . TABLE_CUSTOMERS_BASKET . " WHERE customers_id = '" . (int)$customer_id . "' AND products_id = '" . tep_db_input($products_id) . "'");
						if (!tep_db_num_rows($product_query)) {
							$qty = $this->contents[$products_id]['qty'];
							
							tep_db_query("INSERT INTO " . TABLE_CUSTOMERS_BASKET . " (customers_id, products_id, customers_basket_quantity, customers_basket_date_added, products_categories_id) VALUES ('" . (int)$customer_id . "', '" . tep_db_input($products_id) . "', '" . $qty . "', '" . date('Ymd') . "', '" . $game_id . "')");
							$last_insert_id = tep_db_insert_id();
							
							tep_db_query("DELETE FROM " . TABLE_CUSTOMERS_BASKET_CUSTOM . " WHERE customers_basket_id = '" . (int)$last_insert_id . ";'");
							
							$serialized_array = $this->_serialize($this->contents[$products_id]['custom'][$custom_type]);
							tep_db_query("INSERT INTO " . TABLE_CUSTOMERS_BASKET_CUSTOM . " (customers_basket_id, customers_basket_custom_key, customers_basket_custom_value) VALUES ('" . (int)$last_insert_id . "', '" . $custom_type . "', '" . $serialized_array . "')");
						} else {
							$product_row = tep_db_fetch_array($product_query);
							
							$customers_basket_custom_value_select_sql = "	SELECT customers_basket_custom_value 
																			FROM " . TABLE_CUSTOMERS_BASKET_CUSTOM . " 
																			WHERE customers_basket_id = '" . (int)$product_row['customers_basket_id'] . "' 
																				AND customers_basket_custom_key = '" . $custom_type . "'";
							$customers_basket_custom_value_result_sql = tep_db_query($customers_basket_custom_value_select_sql);
							$customers_basket_custom_value_row = tep_db_fetch_array($customers_basket_custom_value_result_sql);
							$hla_array = $this->_unserialize($customers_basket_custom_value_row['customers_basket_custom_value']);
							
							foreach ($hla_array as $extra_info_cnt => $extra_info_array) {
								if (!$this->hla_product_in_cart($products_id, $extra_info_array['hla_account_id'])) {
									$this->contents[$products_id]['custom'][$custom_type][] = $extra_info_array;
								}
							}
							
							$this->arrange_custom_product($products_id);
							$qty = count($this->contents[$products_id]['custom'][$custom_type]);
							
							tep_db_query("UPDATE " . TABLE_CUSTOMERS_BASKET . " SET customers_basket_quantity = '" . (int)$qty . "' WHERE customers_id = '" . (int)$customer_id . "' AND products_id = '" . tep_db_input($products_id) . "'");
							
							$serialized_array = $this->_serialize($this->contents[$products_id]['custom'][$custom_type]);
							tep_db_query("UPDATE " . TABLE_CUSTOMERS_BASKET_CUSTOM . " SET customers_basket_custom_value ='" . $serialized_array . "' WHERE customers_basket_id = '" . (int)$product_row['customers_basket_id'] . "' AND customers_basket_custom_key = '" . $custom_type . "'");
						}
					} else { // game currency
						$qty = $this->contents[$products_id]['qty'];
						
	          			$product_query = tep_db_query("SELECT customers_basket_id, customers_basket_quantity FROM " . TABLE_CUSTOMERS_BASKET . " WHERE customers_id = '" . (int)$customer_id . "' AND products_id = '" . tep_db_input($products_id) . "'");
	          			if (!tep_db_num_rows($product_query)) {
	            			tep_db_query("INSERT INTO " . TABLE_CUSTOMERS_BASKET . " (customers_id, products_id, customers_basket_quantity, customers_basket_date_added, products_categories_id) VALUES ('" . (int)$customer_id . "', '" . tep_db_input($products_id) . "', '" . $qty . "', '" . date('Ymd') . "', '" . $game_id . "')");
		            		$last_insert_id = tep_db_insert_id();
							
							$serialized_game_currency_array = $this->_serialize($this->contents[$products_id]['custom']['game_currency']);
					
							tep_db_query("INSERT INTO " . TABLE_CUSTOMERS_BASKET_CUSTOM . " (customers_basket_id, customers_basket_custom_key,  customers_basket_custom_value) VALUES ('" . (int)$last_insert_id . "', 'game_currency', '" . $serialized_game_currency_array ."')");
							
							if (isset($this->contents[$products_id]['attributes'])) {
		              			reset($this->contents[$products_id]['attributes']);
		              			while (list($option, $value) = each($this->contents[$products_id]['attributes'])) {
		                			tep_db_query("INSERT INTO " . TABLE_CUSTOMERS_BASKET_ATTRIBUTES . " (customers_id, products_id, products_options_id, products_options_value_id) VALUES ('" . (int)$customer_id . "', '" . tep_db_input($products_id) . "', '" . (int)$option . "', '" . (int)$value . "')");
		              			}
		            		}
		            		if (isset($this->contents[$products_id]["bundle"]) && count($this->contents[$products_id]["bundle"])) {
		            			foreach($this->contents[$products_id]["bundle"] as $subproduct_id => $subproduct_buy_qty) {
		            				$product_price_select_sql = "	SELECT products_price
																	FROM products 
																	WHERE products_status = '1' AND products_id = '" . $subproduct_id . "'";
									$product_price_result_sql = tep_db_query($product_price_select_sql);
									$product_price = tep_db_fetch_array($product_price_result_sql);
		            				tep_db_query("INSERT INTO " . TABLE_CUSTOMERS_BASKET_BUNDLE . " (customers_id, products_bundle_id, subproducts_id, subproducts_quantity, subproducts_price, customers_basket_bundle_date_added, products_categories_id) VALUES ('" . (int)$customer_id . "', '" . tep_db_input($products_id) . "', '" . $subproduct_id . "', '" .$subproduct_buy_qty["sub_qty"] . "', '" . $product_price["products_price"] . "', '". date('Ymd') . "', '" . $game_id . "')");
		            			}
		            		}
	          			} else {
	          				//tep_db_query("UPDATE " . TABLE_CUSTOMERS_BASKET . " SET customers_basket_quantity = customers_basket_quantity + $qty WHERE customers_id = '" . (int)$customer_id . "' AND products_id = '" . tep_db_input($products_id) . "'");
	          				
							$product_row = tep_db_fetch_array($product_query);
							
							$qty = (int)$qty;
	            			
							$customers_basket_custom_value_select_sql = "	SELECT customers_basket_custom_value FROM " . TABLE_CUSTOMERS_BASKET_CUSTOM . " WHERE customers_basket_id = '" . (int)$product_row['customers_basket_id'] . "' AND customers_basket_custom_key = 'game_currency'";
							$customers_basket_custom_value_result_sql = tep_db_query($customers_basket_custom_value_select_sql);
							$customers_basket_custom_value_row = tep_db_fetch_array($customers_basket_custom_value_result_sql);
							
							if (isset($this->contents[$products_id]['custom']['game_currency'])) {
								$game_currency_array = $this->_unserialize($customers_basket_custom_value_row['customers_basket_custom_value']);
								$new_char_array = array();
								
								$latest_qty = $product_row['customers_basket_quantity'] + $qty;
								
								tep_db_query("UPDATE " . TABLE_CUSTOMERS_BASKET . " SET customers_basket_quantity = '" . (int)$latest_qty . "' WHERE customers_id = '" . (int)$customer_id . "' AND products_id = '" . tep_db_input($products_id) . "'");
								
								$db_array_size = sizeof($game_currency_array);
								
								for ($session_array_count = 0; $session_array_count < sizeof($this->contents[$products_id]['custom']['game_currency']); $session_array_count++) {
									for ($db_array_count = 0; $db_array_count < sizeof($game_currency_array); $db_array_count++) {
										$char_exist = false;
										if ($game_currency_array[$db_array_count]['char_name'] == $this->contents[$products_id]['custom']['game_currency'][$session_array_count]['char_name']) {
											$this->contents[$products_id]['custom']['game_currency'][$session_array_count]['qty'] = $this->contents[$products_id]['custom']['game_currency'][$session_array_count]['qty'] + $game_currency_array[$db_array_count]['qty'];
											$char_exist = true;
										} else {
											if ($db_array_count == ($db_array_size-1)) {
												if (!$char_exist) {
													$new_char_array[$game_currency_array[$db_array_count]['char_name']] = $game_currency_array[$db_array_count]['qty'];
												}
											}
										}
									}
								}
								
								if (sizeof($new_char_array) > 0) {
									foreach($new_char_array as $char_name => $quan) {
										$insert_array = true;
										for ($lastest_session_cnt = 0; $lastest_session_cnt < sizeof($this->contents[$products_id]['custom']['game_currency']); $lastest_session_cnt++) {
											if ($this->contents[$products_id]['custom']['game_currency'][$lastest_session_cnt]['char_name'] == $char_name) {
												$insert_array = false;
											}
										}
										
										if ($insert_array) {
											$this->contents[$products_id]['custom']['game_currency'][] = array('char_name' => $char_name, 'qty' => $quan);
											//tep_db_query("UPDATE " . TABLE_CUSTOMERS_BASKET . " SET customers_basket_quantity = customers_basket_quantity + " . (int)$quan . " WHERE customers_id = '" . (int)$customer_id . "' AND products_id = '" . tep_db_input($products_id) . "'");
										}
									}
								}
								
								tep_db_query("UPDATE " . TABLE_CUSTOMERS_BASKET_CUSTOM . " SET customers_basket_custom_value = '" . $this->_serialize($this->contents[$products_id]['custom']['game_currency']) . "' WHERE customers_basket_id = '" . (int)$product_row['customers_basket_id'] . "' AND customers_basket_custom_key = 'game_currency'");
							}
							
							if (isset($this->contents[$products_id]["bundle"]) && count($this->contents[$products_id]["bundle"])) {
	            				foreach($this->contents[$products_id]["bundle"] as $subproduct_id => $subproduct_buy_qty) {
	            					if ($subproduct_buy_qty["sub_qty"]) {
		            					$customers_basket_bundle_select_sql = "SELECT customers_basket_bundle_id FROM " . TABLE_CUSTOMERS_BASKET_BUNDLE . " WHERE customers_id = '" . (int)$customer_id . "' AND products_bundle_id = '" . tep_db_input($products_id) . "' AND subproducts_id = '" . tep_db_input($subproduct_id) . "'";
			            				$customers_basket_bundle_result_sql = tep_db_query($customers_basket_bundle_select_sql);
			            				if (!tep_db_num_rows($customers_basket_bundle_result_sql)) {
			            					$product_price_select_sql = "	SELECT products_price
																			FROM products 
																			WHERE products_status = '1' AND products_id = '" . $subproduct_id . "'";
											$product_price_result_sql = tep_db_query($product_price_select_sql);
											if ($product_price = tep_db_fetch_array($product_price_result_sql))
			            						tep_db_query("INSERT INTO " . TABLE_CUSTOMERS_BASKET_BUNDLE . " (customers_id, products_bundle_id, subproducts_id, subproducts_quantity, subproducts_price, customers_basket_bundle_date_added, products_categories_id) VALUES ('" . (int)$customer_id . "', '" . tep_db_input($products_id) . "', '" . $subproduct_id . "', '" .$subproduct_buy_qty["sub_qty"] . "', '" . $product_price["products_price"] . "', '". date('Ymd') . "', '" . $game_id . "')");
			            				} else {
			            					tep_db_query("UPDATE " . TABLE_CUSTOMERS_BASKET_BUNDLE . " SET subproducts_quantity = subproducts_quantity + " . $subproduct_buy_qty["sub_qty"] . " WHERE customers_id = '" . (int)$customer_id . "' AND products_bundle_id = '" . tep_db_input($products_id) . "'AND subproducts_id = '" . tep_db_input($subproduct_id) . "'");
			            				}
		            				}
	            				}
	            			}
	          			}
					}
				}
      		}
		}
		
		// reset per-session cart contents, but not the database contents
      	$this->reset(false);
		
		$products_sql = "	SELECT customers_basket_id, products_id, customers_basket_quantity 
							FROM " . TABLE_CUSTOMERS_BASKET . " 
							WHERE customers_id = '" . (int)$customer_id . "'";
      	$products_query = tep_db_query($products_sql);
      	while ($products = tep_db_fetch_array($products_query)) {
			$tmp_array = array();
			$tmp_array = $this->get_custom_product_content($products['products_id']);
			
			if (is_array($tmp_array) && sizeof($tmp_array) > 0)	$this->contents[$products['products_id']] = $tmp_array;
			
			// attributes
			$attributes_query = tep_db_query("select products_options_id, products_options_value_id from " . TABLE_CUSTOMERS_BASKET_ATTRIBUTES . " where customers_id = '" . (int)$customer_id . "' and products_id = '" . tep_db_input($products['products_id']) . "'");
			while ($attributes = tep_db_fetch_array($attributes_query)) {
				$this->contents[$products['products_id']]['attributes'][$attributes['products_options_id']] = $attributes['products_options_value_id'];
			}
      	}
		
      	$this->cleanup();
      	$this->calculate();
      	$this->cartID = $this->generate_cart_id();
	}
	
    function reset($reset_database = false) {
    	global $customer_id;
		
      	$this->contents = array();
      	$this->total = 0;
      	$this->weight = 0;
      	$this->custom_product_index = -1;
		// mod indvship
      	$this->shiptotal = 0;
		// end indvship
      	$this->content_type = false;
		
      	if (tep_session_is_registered('customer_id') && ($reset_database == true)) {
			$result = tep_db_query("SELECT customers_basket_id from ".TABLE_CUSTOMERS_BASKET." WHERE customers_id = '" . (int)$customer_id . "';");
			while($row = tep_db_fetch_array($result)) {
				tep_db_query("DELETE FROM " . TABLE_CUSTOMERS_BASKET_CUSTOM . " WHERE customers_basket_id = '" . (int)$row['customers_basket_id'] . ";'");
			}		
        	tep_db_query("DELETE FROM " . TABLE_CUSTOMERS_BASKET . " WHERE customers_id = '" . (int)$customer_id . "'");
        	tep_db_query("DELETE FROM " . TABLE_CUSTOMERS_BASKET_ATTRIBUTES . " WHERE customers_id = '" . (int)$customer_id . "'");
        	tep_db_query("DELETE FROM " . TABLE_CUSTOMERS_BASKET_BUNDLE . " WHERE customers_id = '" . (int)$customer_id . "'");
      	}
		
      	unset($this->cartID);
      	if (tep_session_is_registered('cartID')) tep_session_unregister('cartID');
	}
    
	function update_cart($products_id, $qty = '1', $attributes = '', $notify = true, $array = '', $bdn = '', $iscustom = false, $extra_info_array = '') {
		global $new_products_id_in_cart, $customer_id;
      	
      	$custom_product_type_id = tep_get_custom_product_type($products_id);
      	if ($custom_product_type_id == 2) { // only do when cd key 
	      	$selected_delivery_mode = 5; // put default as 5
	      	if (isset($extra_info_array['delivery_mode']) && (int)$extra_info_array['delivery_mode'] > 0) {
	      		$selected_delivery_mode = $extra_info_array['delivery_mode'];
	    	}
	    	
	    	include_once(DIR_WS_CLASSES . 'product.php');
	      	$product_delivery_mode_array = product::get_product_delivery_mode($products_id);
	      	if (!in_array($selected_delivery_mode, $product_delivery_mode_array)) {
	      		// delivery mode not supported
	      		return ;
	    	}
    	}
      	
      	$products_id = tep_get_uprid($products_id, $attributes);
      	
      	$prod_cat_path = tep_get_product_path($products_id, true);
		if (tep_not_null($prod_cat_path)) {
			$prod_cat_path_array = explode('_', $prod_cat_path);
			$game_id = $prod_cat_path_array[0];
		} else {
			$game_id = 0;
		}
		
      	if ($notify == true) {
      		//	This newly added item will be highlighted in shopping cart summary box
        	$new_products_id_in_cart = $products_id;
        	tep_session_register('new_products_id_in_cart');
      	}
      	
		$custom_type = $this->get_custom_prd_type($products_id);
      	$isincart = $this->in_cart($products_id);
      	
      	$hla_prod_isincart = tep_not_null($extra_info_array['hla_account_id']) ? $this->hla_product_in_cart($products_id, $extra_info_array['hla_account_id']) : '';
      	
      	if ($isincart && $iscustom == false) {
			
			// PLEASE ASK WEICHEN ABOUT THIS, should we put $allow_update = true and check for parameter with delivery mode = 6?
			if (isset($extra_info_array['delivery_mode']) && $extra_info_array['delivery_mode'] == '6') {
				$allow_update = true;
			} else if (STOCK_CHECK == 'true') {
				$allow_update = $this->check_update_cart($products_id, $qty, tep_not_null($extra_info_array['hla_account_id']) ? $extra_info_array['hla_account_id'] : '');
			}
			
			if ($allow_update) {
				
				$this->update_quantity($products_id, $qty, $attributes, $extra_info_array);
				if (tep_session_is_registered('customer_id')) {
					tep_db_query("UPDATE " . TABLE_CUSTOMERS_BASKET . " SET customers_basket_quantity = '" . $this->contents[$products_id]['qty'] . "' WHERE products_id = '" . $products_id . "' AND customers_id = '" . (int)$customer_id . "' AND products_categories_id = '" . $game_id . "'");
					
					$customers_basket_id_select_sql = "	SELECT customers_basket_id 
														FROM " . TABLE_CUSTOMERS_BASKET . " 
														WHERE products_id = '" . (int)$products_id . "' 
															AND customers_id = '" . (int)$customer_id . "' 
															AND products_categories_id = '" . (int)$game_id . "'";
					$customers_basket_id_result_sql = tep_db_query($customers_basket_id_select_sql);
					$customers_basket_id_row = tep_db_fetch_array($customers_basket_id_result_sql);
					
					$serialized_array = $this->_serialize($this->contents[$products_id]['custom'][$custom_type]);
					
					tep_db_query("UPDATE " . TABLE_CUSTOMERS_BASKET_CUSTOM . " SET customers_basket_custom_value ='" . $serialized_array . "' WHERE customers_basket_id = '" . (int)$customers_basket_id_row['customers_basket_id'] . "' AND customers_basket_custom_key = '" . $custom_type . "'");
				}
				
	    		$bundle_select_sql = "	SELECT pd.products_name, pb.*, p.products_bundle, p.products_bundle_dynamic,
		                       				p.products_id, p.products_price
							   			FROM " . TABLE_PRODUCTS . " p 
							   			INNER JOIN " . TABLE_PRODUCTS_DESCRIPTION . " AS pd
							   				ON (p.products_id=pd.products_id)
							   			INNER JOIN " . TABLE_PRODUCTS_BUNDLES . " AS pb
							   				ON (pb.subproduct_id=pd.products_id) 
							   			WHERE pb.bundle_id = '" . (int)$products_id . "' 
											AND language_id = '1'";
	        	$bundle_result_sql = tep_db_query($bundle_select_sql);
	        	
				while ($bundle_data = tep_db_fetch_array($bundle_result_sql)) {
					$product_select_sql = "	SELECT p.products_id, pd.products_name, 
												pd.products_description, p.products_model, p.products_quantity, 
												p.products_bundle, pd.products_image, pd.products_url, p.products_price, 
												p.products_tax_class_id, p.products_date_added, p.products_date_available, 
												p.manufacturers_id 
											FROM products p, products_description pd 
											WHERE p.products_status = '1' AND p.products_id = '" . $bundle_data["products_id"] . "' 
												AND pd.products_id = p.products_id AND pd.language_id = '1'";
					$product_result_sql = tep_db_query($product_select_sql);
					$product_info = tep_db_fetch_array($product_result_sql);
					
					$cnt = count($array);
					for ($a=0; $a < $cnt; $a++) {
						if (isset($array[$a]["id"]) && $array[$a]['id'] == $product_info['products_id']) {
							if (tep_session_is_registered('customer_id')) {
								$product_bundle_select_sql = "SELECT subproducts_quantity FROM " . TABLE_CUSTOMERS_BASKET_BUNDLE . " WHERE products_bundle_id = '".$products_id."' AND customers_id = '" . (int)$customer_id . "' AND subproducts_id = '" . $product_info["products_id"] . "'";
								$product_bundle_result_sql = tep_db_query($product_bundle_select_sql);
								if ($product_bundle = tep_db_fetch_array($product_bundle_result_sql)) {
									$qty_bd = ($bdn == 'y') ? (int)$array[$a]['bd_products_qty'] : (int)$product_bundle['subproducts_quantity'] + (int)$array[$a]['bd_products_qty'];
									tep_db_query("UPDATE " . TABLE_CUSTOMERS_BASKET_BUNDLE . " SET subproducts_quantity = '" . $qty_bd . "' WHERE products_bundle_id = '".$products_id."' AND customers_id = '" . (int)$customer_id . "' AND subproducts_id = '" . $product_info["products_id"] . "'");
								} else {
									tep_db_query("INSERT INTO ". TABLE_CUSTOMERS_BASKET_BUNDLE . " (customers_id, products_bundle_id, subproducts_id, subproducts_quantity, subproducts_price, customers_basket_bundle_date_added, products_categories_id) VALUES 
											('" . (int)$customer_id . "', '" . tep_db_input($products_id) . "', '". $product_info['products_id'] . "', '" . $array[$a]['bd_products_qty'] . "', '". $product_info['products_price'] . "', '" . date('Ymd') . "', '" . $game_id . "')");
								}
							} else {
								if ($bdn == 'y') {
									$this->contents[$products_id]['bundle'][$product_info["products_id"]]['sub_qty'] = (int)$array[$a]['bd_products_qty'];
								} else {
									$this->contents[$products_id]['bundle'][$product_info["products_id"]]['sub_qty'] += (int)$array[$a]['bd_products_qty'];
								}
							}
						}
	        		}
	        	}
        	} else {
        		$_SESSION['cart_update_error'] = TEXT_STOCK_NOT_AVAILABLE;
        	}
      	} else {
      		if (!$iscustom) {	//Non-Custom products
				$this->contents[$products_id] = array('qty' => $qty);
				if (!isset($this->contents[$products_id]['custom']['game_currency'])) {
					$this->contents[$products_id]['custom']['game_currency'] = array();
				}
				
				if ($custom_type == 'game_currency') {
					$game_currency_info_array = array(	'qty' => $qty,
														'char_name' => trim($extra_info_array['char_name']),
														'delivery_mode' => $extra_info_array['delivery_mode']
													 );
					
					if ($extra_info_array['delivery_mode'] == '1') {
						//$game_currency_info_array['char_online_time'] = $extra_info_array['char_online_time'];
						//$game_currency_info_array['char_online_dur'] = $extra_info_array['char_online_dur'];
					} else if ($extra_info_array['delivery_mode'] == '2') {
						$game_currency_info_array['char_account_name'] = $extra_info_array['char_account_name'];
						$game_currency_info_array['char_account_pwd'] = $extra_info_array['char_account_pwd'];						
						$game_currency_info_array['char_wow_account'] = $extra_info_array['char_wow_account'];
					}
					
					$this->contents[$products_id]['custom']['game_currency'][] = $game_currency_info_array;
				} else if ($custom_type == 'cdkey') {
					$extra_info_insert_array = array('qty' => $qty);
					if (isset($extra_info_array['delivery_mode'])) {
						$extra_info_insert_array['delivery_mode'] = $extra_info_array['delivery_mode'];
					}
					if (isset($extra_info_array['top_up_info'])) {
						$extra_info_insert_array['top_up_info'] = $extra_info_array['top_up_info'];
					}
					$this->contents[$products_id]['custom'][$custom_type][] = $extra_info_insert_array;
				} else if ($custom_type == 'hla') {
					$this->contents[$products_id]['custom'][$custom_type][] = array('qty' => 1, 
																					'hla_account_id' => $extra_info_array['hla_account_id'] );
					$qty = count($this->contents[$products_id]['custom'][$custom_type]);
				}
				
	        	if (tep_session_is_registered('customer_id')) {
					if ($custom_type == 'hla') {
						tep_db_query("UPDATE " . TABLE_CUSTOMERS_BASKET . " SET customers_basket_quantity = '" . $qty . "' WHERE products_id = '" . $products_id . "' AND customers_id = '" . (int)$customer_id . "' AND products_categories_id = '" . $game_id . "'");
						
						$customers_basket_id_select_sql = "	SELECT customers_basket_id 
															FROM " . TABLE_CUSTOMERS_BASKET . " 
															WHERE products_id = '" . (int)$products_id . "' 
																AND customers_id = '" . (int)$customer_id . "' 
																AND products_categories_id = '" . (int)$game_id . "'";
						$customers_basket_id_result_sql = tep_db_query($customers_basket_id_select_sql);
						$customers_basket_id_row = tep_db_fetch_array($customers_basket_id_result_sql);
						
						$serialized_array = $this->_serialize($this->contents[$products_id]['custom'][$custom_type]);
						
						tep_db_query("UPDATE " . TABLE_CUSTOMERS_BASKET_CUSTOM . " SET customers_basket_custom_value ='" . $serialized_array . "' WHERE customers_basket_id = '" . (int)$customers_basket_id_row['customers_basket_id'] . "' AND customers_basket_custom_key = '" . $custom_type . "'");
					} else {
						tep_db_query("INSERT INTO " . TABLE_CUSTOMERS_BASKET . " (customers_id, products_id, customers_basket_quantity, customers_basket_date_added, products_categories_id) VALUES ('" . (int)$customer_id . "', '" . tep_db_input($products_id) . "', '" . $qty . "', '" . date('Ymd') . "', '" . $game_id . "')");
						$last_insert_id = tep_db_insert_id();
						$serialized_array = $this->_serialize($this->contents[$products_id]['custom'][$custom_type]);
						tep_db_query("INSERT INTO " . TABLE_CUSTOMERS_BASKET_CUSTOM . " (customers_basket_id, customers_basket_custom_key, customers_basket_custom_value) VALUES ('" . (int)$last_insert_id . "', '" . $custom_type . "', '" . $serialized_array . "')");
					}
				}
			} else {	//Custom Products
				$insert_custom_data = false;
				
				$custom_type = $this->get_custom_prd_type($products_id);
				switch ($custom_type) {
					case 'power_leveling':
						$last_insert_id = 0;
						$insert_custom_data = true;
						break;
					case 'cdkey':
						if ($isincart) {
							$this->update_quantity($products_id, $qty, '', $extra_info_array);
						} else {
							$extra_info_insert_array = array('qty' => $qty);
							if (isset($extra_info_array['delivery_mode'])) {
								$extra_info_insert_array['delivery_mode'] = $extra_info_array['delivery_mode'];
							}
							if (isset($extra_info_array['top_up_info'])) {
								$extra_info_insert_array['top_up_info'] = $extra_info_array['top_up_info'];
							}
							$this->contents[$products_id]['custom'][$custom_type][] = $extra_info_insert_array;
							$insert_custom_data = true;
						}
						break;
					case 'hla': 
						$this->contents[$products_id]['custom'][$custom_type][] = array('qty' => 1, 
																						'hla_account_id' => $extra_info_array['hla_account_id']);
						
						$qty = count($this->contents[$products_id]['custom'][$custom_type]);
						$this->contents[$products_id] = array('qty' => $qty);
						
						if (!$hla_prod_isincart) $insert_custom_data = false;
						break;
				}
				
				if (tep_session_is_registered('customer_id') && $insert_custom_data) {
					// get the last insert id.
					tep_db_query("INSERT INTO " . TABLE_CUSTOMERS_BASKET . " (customers_id, products_id, customers_basket_quantity, final_price, customers_basket_date_added, products_categories_id) VALUES ('" . (int)$customer_id . "', '" . tep_db_input($products_id) . "', '" . $qty . "', '".$this->custom_product_content['calculated']['price']."' ,'" . date('Ymd') . "', '" . $game_id . "')");
					$last_insert_id = tep_db_insert_id();
					
					if ($custom_type == 'power_leveling') {
						$this->custom_product_content['id'] = (int)$last_insert_id;
					}
					
					$serialized_array = $this->_serialize($this->custom_product_content);
					
					tep_db_query("INSERT INTO " . TABLE_CUSTOMERS_BASKET_CUSTOM . " (customers_basket_id, customers_basket_custom_key, customers_basket_custom_value) VALUES ('" . (int)$last_insert_id . "', '" . $custom_type . "', '" . $serialized_array . "')");
				} else if (tep_session_is_registered('customer_id') && !$insert_custom_data && $custom_type == 'hla') {
					tep_db_query("UPDATE " . TABLE_CUSTOMERS_BASKET . " SET customers_basket_quantity = '" . $this->contents[$products_id]['qty'] . "' WHERE products_id = '" . tep_db_input($products_id) . "' AND customers_id = '" . (int)$customer_id . "' AND products_categories_id = '" . $game_id . "'");
					
					$customers_basket_id_select_sql = "	SELECT customers_basket_id 
														FROM " . TABLE_CUSTOMERS_BASKET . " 
														WHERE products_id = '" . (int)$products_id . "' 
															AND customers_id = '" . (int)$customer_id . "' 
															AND products_categories_id = '" . (int)$game_id . "'";
					$customers_basket_id_result_sql = tep_db_query($customers_basket_id_select_sql);
					$customers_basket_id_row = tep_db_fetch_array($customers_basket_id_result_sql);
					
					$serialized_array = $this->_serialize($this->contents[$products_id]['custom'][$custom_type]);
					
					tep_db_query("UPDATE " . TABLE_CUSTOMERS_BASKET_CUSTOM . " SET customers_basket_custom_value ='" . $serialized_array . "' WHERE customers_basket_id = '" . (int)$customers_basket_id_row['customers_basket_id'] . "' AND customers_basket_custom_key = '" . $custom_type . "'");
				}
				
				if (!is_array($this->contents[$products_id]['custom'][$custom_type]))
					$this->contents[$products_id]['custom'][$custom_type] = array();
				
				if ($this->custom_product_index == -1) {
					array_push($this->contents[$products_id]['custom'][$custom_type], array('content' => $this->custom_product_content));
				} else {
					$this->contents[$products_id]['custom'][$custom_type][$this->custom_product_index] = array('content' => $this->custom_product_content);
				}
				
				if (!is_array($this->contents[$products_id]['custom'][$custom_type]))
					$this->contents[$products_id]['custom'][$custom_type] = array();
				
				if (tep_not_null($this->custom_product_content)) {
					if ($this->custom_product_index == -1) {
						array_push($this->contents[$products_id]['custom'][$custom_type], array('content' => $this->custom_product_content));
					} else {
						$this->contents[$products_id]['custom'][$custom_type][$this->custom_product_index] = array('content' => $this->custom_product_content);
					}
				}
			}
			
			unset($this->custom_product_content);
        	
    		$bundle_select_sql = "	SELECT pd.products_name, pb.*, p.products_bundle, p.products_bundle_dynamic,
                       					p.products_id, p.products_price
					   				FROM products p 
					   				INNER JOIN products_description pd
					   					ON p.products_id=pd.products_id
					   				INNER JOIN products_bundles pb
					   					ON pb.subproduct_id=pd.products_id 
					   				WHERE pb.bundle_id = '" . (int)$products_id . "' AND language_id = '1'";
    		$bundle_result_sql = tep_db_query($bundle_select_sql);
    		
			while ($bundle_data = tep_db_fetch_array($bundle_result_sql)) {
				$product_select_sql = "	SELECT p.products_id, pd.products_name, 
											pd.products_description, p.products_model, p.products_quantity, 
											p.products_bundle, pd.products_image, pd.products_url, p.products_price, 
											p.products_tax_class_id, p.products_date_added, p.products_date_available, 
											p.manufacturers_id 
										FROM products p, products_description pd 
										WHERE p.products_status = '1' AND p.products_id = '" . $bundle_data["products_id"] . "' 
											AND pd.products_id = p.products_id AND pd.language_id = '1'";
				$product_result_sql = tep_db_query($product_select_sql);
				$product_info = tep_db_fetch_array($product_result_sql);
				
				$cnt = count($array);
				for ($a=0; $a<$cnt; $a++) {
					if(isset($array[$a]['id']) && $array[$a]['id'] == $product_info['products_id']) {
						//echo $array[$a]['bd_products_qty'];
						if (tep_session_is_registered('customer_id')) {
							tep_db_query("INSERT INTO ". TABLE_CUSTOMERS_BASKET_BUNDLE . " (customers_id, products_bundle_id, subproducts_id, subproducts_quantity, subproducts_price, customers_basket_bundle_date_added, products_categories_id) VALUES 
										('" . (int)$customer_id . "', '" . tep_db_input($products_id) . "', '". $product_info['products_id'] . "', '" . $array[$a]['bd_products_qty'] . "', '". $product_info['products_price'] . "', '" . date('Ymd') . "', '" . $game_id . "')");
						} else {
							$this->contents[$products_id]['bundle'][$product_info['products_id']] = array('sub_qty' => $array[$a]['bd_products_qty'] );
						}
					}
				}
			}
			
        	if (is_array($attributes)) {
          		reset($attributes);
          		while (list($option, $value) = each($attributes)) {
            		$this->contents[$products_id]['attributes'][$option] = $value;
					// insert into database
            		if (tep_session_is_registered('customer_id')) tep_db_query("insert into " . TABLE_CUSTOMERS_BASKET_ATTRIBUTES . " (customers_id, products_id, products_options_id, products_options_value_id, products_categories_id) values ('" . (int)$customer_id . "', '" . tep_db_input($products_id) . "', '" . (int)$option . "', '" . (int)$value . "', '" . $game_id . "')");
          		}
        	}
      	}
   		
      	unset($isincart);
      	$this->cleanup();	// IMPORTANT: This Will remove those product has < 1 in cart qty
		
		// assign a temporary unique ID to the order contents to prevent hack attempts during the checkout procedure
		$this->calculate();
      	$this->cartID = $this->generate_cart_id();
	}
	
	function add_cart($products_id, $qty = '1', $attributes = '', $notify = true, $array = '', $bdn = '', $iscustom = false, $extra_info_array = '') {
		global $new_products_id_in_cart, $customer_id;
       	
      	$custom_product_type_id = tep_get_custom_product_type($products_id);
      	if ($custom_product_type_id == 2) { // only do when cd key 
	      	$selected_delivery_mode = 5; // put default as 5
	      	if (isset($extra_info_array['delivery_mode']) && (int)$extra_info_array['delivery_mode'] > 0) {
	      		$selected_delivery_mode = $extra_info_array['delivery_mode'];
	    	}
	    	
	    	include_once(DIR_WS_CLASSES . 'product.php');
	      	$product_delivery_mode_array = product::get_product_delivery_mode($products_id);
	      	if (!in_array($selected_delivery_mode, $product_delivery_mode_array)) {
	      		// delivery mode not supported
	      		return ;
	    	}
    	}
    	
      	$products_id = tep_get_uprid($products_id, $attributes);
      	
      	$prod_cat_path = tep_get_product_path($products_id, true);
		if (tep_not_null($prod_cat_path)) {
			$prod_cat_path_array = explode('_', $prod_cat_path);
			$game_id = $prod_cat_path_array[0];
		} else {
			$game_id = 0;
		}
		
   		$custom_type = $this->get_custom_prd_type($products_id);
		
      	if ($notify == true) {
      		//	This newly added item will be highlighted in shopping cart summary box
        	$new_products_id_in_cart = $products_id;
        	tep_session_register('new_products_id_in_cart');
      	}
      	
      	$isincart = $this->in_cart($products_id);
      	
		if ($custom_type == 'hla') {
			$hla_prod_isincart = $this->hla_product_in_cart($products_id, $extra_info_array['hla_account_id']);
		}
      	
//      	if (isset($extra_info_array['char_name']))	$extra_info_array['char_name'] = tep_db_prepare_input($extra_info_array['char_name']);
//		if (isset($extra_info_array['char_account_name']))	$extra_info_array['char_account_name'] = tep_db_prepare_input($extra_info_array['char_account_name']);
//		if (isset($extra_info_array['char_account_pwd']))	$extra_info_array['char_account_pwd'] = tep_db_prepare_input($extra_info_array['char_account_pwd']);
//		if (isset($extra_info_array['char_wow_account']))	$extra_info_array['char_wow_account'] = tep_db_prepare_input($extra_info_array['char_wow_account']);
		
		if (isset($extra_info_array) && is_array($extra_info_array) && count($extra_info_array)) {
			foreach ($extra_info_array as $extra_info_key_loop => $extra_info_value_loop) {
				$extra_info_array[$extra_info_key_loop] = tep_db_prepare_input($extra_info_value_loop);
				if (is_array($extra_info_value_loop)) {
					foreach ($extra_info_value_loop as $extra_info_key_sub_loop => $extra_info_value_sub_loop) {
						$extra_info_array[$extra_info_key_loop][$extra_info_key_sub_loop] = tep_db_prepare_input($extra_info_value_sub_loop);
					}
				}
			}
		}
		
		if ($isincart && $iscustom == false) {
			$new_quantity = $qty;
			if (isset($this->contents[$products_id]['custom']['game_currency'])) {
				for ($char_cnt = 0; $char_cnt < sizeof($this->contents[$products_id]['custom']['game_currency']); $char_cnt++) {
					if ($this->contents[$products_id]['custom']['game_currency'][$char_cnt]['char_name'] == trim($extra_info_array['char_name'])) {
						$new_quantity += $this->contents[$products_id]['custom']['game_currency'][$char_cnt]['qty'];
					}
				}
			} else if (isset($this->contents[$products_id]['custom']['cdkey'])) {
				$count_index = 0;
				
				foreach ($this->contents[$products_id]['custom']['cdkey'] as $cdkey_index_loop => $cdkey_data_loop) {
					if (isset($cdkey_data_loop['top_up_info'])) {
						ksort($cdkey_data_loop['top_up_info']);
						reset($cdkey_data_loop['top_up_info']);
					} else {
						$cdkey_data_loop['top_up_info'] = array();
					}
					
					if (isset($extra_info_array['top_up_info'])) {
						reset($extra_info_array);
					} else {
						$extra_info_array['top_up_info'] = array();
					}
					
					if ( $extra_info_array['delivery_mode'] == $cdkey_data_loop['delivery_mode'] && 
						count(array_diff_assoc($extra_info_array['top_up_info'], $cdkey_data_loop['top_up_info']))==0 &&
						count(array_diff_assoc($cdkey_data_loop['top_up_info'], $extra_info_array['top_up_info']))==0  ) {
						$new_quantity += $cdkey_data_loop['qty'];
					}
					$count_index++;
				}
			} else if (isset($this->contents[$products_id]['custom']['store_credit'])) {
				$new_quantity += $this->contents[$products_id]['custom']['store_credit'][0]['qty'];
			} else if (isset($this->contents[$products_id]['custom']['hla'])) {
				$prod_cnt = count($this->contents[$products_id]['custom']['hla']);
				
      			if (!$hla_prod_isincart) {
					$new_quantity += $prod_cnt;
				} else {
					$new_quantity = $prod_cnt;
				}
			}
			
			$this->update_cart($products_id, $new_quantity, $attributes, $notify, $array, $bdn, $iscustom, $extra_info_array);
      	} else {
      		if (!$iscustom) {	//Non-Custom products
				
				$this->contents[$products_id] = array('qty' => $qty);
				if (!isset($this->contents[$products_id]['custom'][$custom_type])) {
					$this->contents[$products_id]['custom'][$custom_type] = array();
				}
				
				if ($custom_type == 'game_currency') {
					$game_currency_info_array = array(	'qty' => $qty,
														'char_name' => trim($extra_info_array['char_name']),
														'delivery_mode' => $extra_info_array['delivery_mode']
													 );
					
					if ($extra_info_array['delivery_mode'] == '1') {
						//$game_currency_info_array['char_online_time'] = $extra_info_array['char_online_time'];
						//$game_currency_info_array['char_online_dur'] = $extra_info_array['char_online_dur'];
					} else if ($extra_info_array['delivery_mode'] == '2') {
						$game_currency_info_array['char_account_name'] = $extra_info_array['char_account_name'];
						$game_currency_info_array['char_account_pwd'] = $extra_info_array['char_account_pwd'];
						$game_currency_info_array['char_wow_account'] = $extra_info_array['char_wow_account'];
					}
					
					$this->contents[$products_id]['custom'][$custom_type][] = $game_currency_info_array;
				} else if ($custom_type == 'cdkey') {
					$extra_info_insert_array = array('qty' => $qty);
					if (isset($extra_info_array['delivery_mode'])) {
						$extra_info_insert_array['delivery_mode'] = $extra_info_array['delivery_mode'];
					} else {
						$extra_info_insert_array['delivery_mode'] = 5;
					}
					if (isset($extra_info_array['top_up_info'])) {
						$extra_info_insert_array['top_up_info'] = $extra_info_array['top_up_info'];
					} else {
						$extra_info_insert_array['top_up_info'] = array();
					}
					$this->contents[$products_id]['custom'][$custom_type][] = $extra_info_insert_array;
				} else if ($custom_type == 'store_credit') {
					$this->contents[$products_id]['custom'][$custom_type][] = array('qty' => $qty);
				} else if (!$isincart && !$hla_prod_isincart && $custom_type == 'hla') {
					$this->contents[$products_id]['custom'][$custom_type][] = array('qty' => $qty, 
																					'hla_account_id' => $extra_info_array['hla_account_id']);
				}
				
	        	if (tep_session_is_registered('customer_id')) {
					tep_db_query("INSERT INTO " . TABLE_CUSTOMERS_BASKET . " (customers_id, products_id, customers_basket_quantity, customers_basket_date_added, products_categories_id) VALUES ('" . (int)$customer_id . "', '" . tep_db_input($products_id) . "', '" . $qty . "', '" . date('Ymd') . "', '" . $game_id . "')");
					$last_insert_id = tep_db_insert_id();
					
					$serialized_array = $this->_serialize($this->contents[$products_id]['custom'][$custom_type]);
					tep_db_query("INSERT INTO " . TABLE_CUSTOMERS_BASKET_CUSTOM . " (customers_basket_id, customers_basket_custom_key, customers_basket_custom_value) VALUES ('" . (int)$last_insert_id . "', '" . $custom_type . "', '" . $serialized_array . "')");
				}
			} else {	//Custom Products
				$insert_custom_data = false;
				
				// $custom_type = $this->get_custom_prd_type($products_id);	// Commented out as function call before this
				switch ($custom_type) {
					case 'power_leveling':
						$last_insert_id = 0;
						$insert_custom_data = true;
						break;
					case 'cdkey':
						if ($isincart) {
							$count_index = 0;

							if (!isset($extra_info_array['delivery_mode'])) {
								$extra_info_array['delivery_mode'] = 5;
							}
							
							$this->arrange_custom_product($products_id);
							ksort($this->contents[$products_id]['custom']['cdkey']);
							if (isset($this->contents[$products_id]['custom']['cdkey'])) {
								foreach ($this->contents[$products_id]['custom']['cdkey'] as $cdkey_index_loop => $cdkey_data_loop) {
									if (isset($cdkey_data_loop['top_up_info'])) {
										ksort($cdkey_data_loop['top_up_info']);
										reset($cdkey_data_loop['top_up_info']);
									} else {
										$cdkey_data_loop['top_up_info'] = array();
									}
									
									if (isset($extra_info_array['top_up_info'])) {
										ksort($extra_info_array['top_up_info']);
										reset($extra_info_array);
									} else {
										$extra_info_array['top_up_info'] = array();
									}
									
									if (count(array_diff_assoc($extra_info_array['top_up_info'], $cdkey_data_loop['top_up_info']))==0 &&
										count(array_diff_assoc($cdkey_data_loop['top_up_info'], $extra_info_array['top_up_info']))==0 &&
										$cdkey_data_loop['delivery_mode'] == $extra_info_array['delivery_mode'] ) {
										break;
									}
									$count_index++;
								}
							}
							
							if (isset($this->contents[$products_id]['custom']['cdkey'][$count_index])) {
								$tmp_qty = $qty + $this->contents[$products_id]['custom']['cdkey'][$count_index]['qty'];
								$this->update_quantity($products_id, $tmp_qty, '', $extra_info_array);
							} else {
								$extra_info_insert_array = array('qty' => $qty);
								if (isset($extra_info_array['delivery_mode'])) {
									$extra_info_insert_array['delivery_mode'] = $extra_info_array['delivery_mode'];
								}
								
								if (isset($extra_info_array['top_up_info'])) {
									$extra_info_insert_array['top_up_info'] = $extra_info_array['top_up_info'];
								} else {
									$extra_info_insert_array['top_up_info'] = array();
								}
								$this->contents[$products_id]['custom'][$custom_type][$count_index] = $extra_info_insert_array;
								$insert_custom_data = true;
								$this->update_quantity($products_id, $qty, '', $extra_info_array);
							}
							
						} else {
							$extra_info_insert_array = array('qty' => $qty);
							if (isset($extra_info_array['delivery_mode'])) {
								$extra_info_insert_array['delivery_mode'] = $extra_info_array['delivery_mode'];
							} else {
								$extra_info_insert_array['delivery_mode'] = 5;
							}
							
							if (isset($extra_info_array['top_up_info'])) {
								$extra_info_insert_array['top_up_info'] = $extra_info_array['top_up_info'];
							} else {
								$extra_info_insert_array['top_up_info'] = array();
							}
							$this->contents[$products_id]['custom'][$custom_type][0] = $extra_info_insert_array;
							$insert_custom_data = true;
						}
						break;
					case 'store_credit':
						if ($isincart) {
							$qty += $this->contents[$products_id]['qty'];
							$this->update_quantity($products_id, $qty);
						} else {
							$this->contents[$products_id] = array('qty' => $qty);
							$insert_custom_data = true;
						}
						break;
					case 'hla':
						if (!$isincart && !$hla_prod_isincart) {
							$this->contents[$products_id] = array('qty' => $qty);
							$insert_custom_data = true;
						}
						break;
				}
				
				if (tep_session_is_registered('customer_id') && $insert_custom_data) {
					// get the last insert id.
					tep_db_query("INSERT INTO " . TABLE_CUSTOMERS_BASKET . " (customers_id, products_id, customers_basket_quantity, final_price, customers_basket_date_added, products_categories_id) VALUES ('" . (int)$customer_id . "', '" . tep_db_input($products_id) . "', '" . $qty . "', '".$this->custom_product_content['calculated']['price']."' ,'" . date('Ymd') . "', '" . $game_id . "')");
					$last_insert_id = tep_db_insert_id();
					
					if ($custom_type == 'power_leveling') {
						$this->custom_product_content['id'] = (int)$last_insert_id;
					}
					
					$serialized_array = $this->_serialize($this->custom_product_content);
					tep_db_query("INSERT INTO " . TABLE_CUSTOMERS_BASKET_CUSTOM . " (customers_basket_id, customers_basket_custom_key, customers_basket_custom_value) VALUES ('" . (int)$last_insert_id . "', '" . $custom_type . "', '" . $serialized_array . "')");
				}
				
				if (!is_array($this->contents[$products_id]['custom'][$custom_type]))
					$this->contents[$products_id]['custom'][$custom_type] = array();
					
				if ($custom_type == 'power_leveling') {
					if ($this->custom_product_index == -1) {
						array_push($this->contents[$products_id]['custom'][$custom_type], array('content' => $this->custom_product_content));
					} else {
						$this->contents[$products_id]['custom'][$custom_type][$this->custom_product_index] = array('content' => $this->custom_product_content);
					}
				} else if ($custom_type == 'store_credit') {
					$this->contents[$products_id]['custom'][$custom_type][0] = array('qty' => $qty);
				} else if ($custom_type == 'hla' && !$hla_prod_isincart) {
					$this->contents[$products_id]['custom'][$custom_type][0] = array(	'qty' => $qty, 
																						'hla_account_id' => $extra_info_array['hla_account_id']);
				} else {	// CD Key atm
/*					$extra_info_insert_array = array('qty' => $qty);
					if (isset($extra_info_array['delivery_mode'])) {
						$extra_info_insert_array['delivery_mode'] = $extra_info_array['delivery_mode'];
					}
					if (isset($extra_info_array['top_up_info'])) {
						$extra_info_insert_array['top_up_info'] = $extra_info_array['top_up_info'];
					}
					$this->contents[$products_id]['custom'][$custom_type][0] = $extra_info_insert_array;*/
				}
			}
			
			unset($this->custom_product_content);
			
    		$bundle_select_sql = "	SELECT pd.products_name, pb.*, p.products_bundle, p.products_bundle_dynamic,
                       					p.products_id, p.products_price
					   				FROM " . TABLE_PRODUCTS . " p 
					   				INNER JOIN " . TABLE_PRODUCTS_DESCRIPTION . " pd
					   					ON p.products_id=pd.products_id
					   				INNER JOIN " . TABLE_PRODUCTS_BUNDLES . " pb
					   					ON pb.subproduct_id=pd.products_id 
					   				WHERE pb.bundle_id = '" . (int)$products_id . "' AND language_id = '1'";
    		$bundle_result_sql = tep_db_query($bundle_select_sql);
    		
			while ($bundle_data = tep_db_fetch_array($bundle_result_sql)) {
				$product_select_sql = "	SELECT p.products_id, pd.products_name, 
											pd.products_description, p.products_model, p.products_quantity, 
											p.products_bundle, pd.products_image, pd.products_url, p.products_price, 
											p.products_tax_class_id, p.products_date_added, p.products_date_available, 
											p.manufacturers_id 
										FROM " . TABLE_PRODUCTS . " p, " . TABLE_PRODUCTS_DESCRIPTION . " pd 
										WHERE p.products_status = '1' AND p.products_id = '" . $bundle_data["products_id"] . "' 
											AND pd.products_id = p.products_id AND pd.language_id = '1'";
				$product_result_sql = tep_db_query($product_select_sql);
				$product_info = tep_db_fetch_array($product_result_sql);
				
				$cnt = count($array);
				for ($a=0; $a < $cnt; $a++) {
					if(isset($array[$a]['id']) && $array[$a]['id'] == $product_info['products_id']) {
						//echo $array[$a]['bd_products_qty'];
						if (tep_session_is_registered('customer_id')) {
							tep_db_query("INSERT INTO ". TABLE_CUSTOMERS_BASKET_BUNDLE . " (customers_id, products_bundle_id, subproducts_id, subproducts_quantity, subproducts_price, customers_basket_bundle_date_added, products_categories_id) VALUES 
										('" . (int)$customer_id . "', '" . tep_db_input($products_id) . "', '". $product_info['products_id'] . "', '" . $array[$a]['bd_products_qty'] . "', '". $product_info['products_price'] . "', '" . date('Ymd') . "', '" . $game_id . "')");
						} else {
							$this->contents[$products_id]['bundle'][$product_info['products_id']] = array('sub_qty' => $array[$a]['bd_products_qty'] );
						}
					}
				}
			}
      		
        	if (is_array($attributes)) {
          		reset($attributes);
          		while (list($option, $value) = each($attributes)) {
            		$this->contents[$products_id]['attributes'][$option] = $value;
					// insert into database
            		if (tep_session_is_registered('customer_id')) tep_db_query("insert into " . TABLE_CUSTOMERS_BASKET_ATTRIBUTES . " (customers_id, products_id, products_options_id, products_options_value_id, products_categories_id) values ('" . (int)$customer_id . "', '" . tep_db_input($products_id) . "', '" . (int)$option . "', '" . (int)$value . "', '" . $game_id . "')");
          		}
        	}
      	}
      	
      	unset($isincart);
      	$this->cleanup();	// IMPORTANT: This Will remove those product has < 1 in cart qty
		$this->calculate();
		
		// assign a temporary unique ID to the order contents to prevent hack attempts during the checkout procedure
      	$this->cartID = $this->generate_cart_id();
	}
	
	function set_custom_product_index($index=-1) {
		$this->custom_product_index = $index;	
		
	}
	
    function update_quantity($products_id, $quantity = '', $attributes = '', $extra_info_array = '') {
    	global $customer_id;
		
		$temp_qty = (int)$quantity;
		
		$char_exist =  false;
		$char_exist_pos = 0;
		
      	if ($quantity == '') {
      		return true; // nothing needs to be updated if theres no quantity, so we return true..
      	}
		
		$custom_type = $this->get_custom_prd_type($products_id);
		
		if ($custom_type == 'game_currency') {
			if (isset($this->contents[$products_id]['custom'][$custom_type])) {
				foreach ($this->contents[$products_id]['custom'][$custom_type] as $info_array_cnt => $extra_array) {
					if ($extra_array['char_name'] == trim($extra_info_array['char_name'])) {
						if ($quantity > 0) {
							$this->contents[$products_id]['custom'][$custom_type][$info_array_cnt]['qty'] = (int)$quantity;
						} else {
							unset($this->contents[$products_id]['custom'][$custom_type][$info_array_cnt]);
						}
						
						$char_exist = true;
						$char_exist_pos = $info_array_cnt;
					} else {
						$temp_qty += $this->contents[$products_id]['custom'][$custom_type][$info_array_cnt]['qty'];
					}
				}
				
				if (!$char_exist) {
					if ($quantity > 0 && tep_not_null(trim($extra_info_array['char_name']))) {
						$game_currency_info_array = array(	'qty' => (int)$quantity,
															'char_name' => trim($extra_info_array['char_name']),
															'delivery_mode' => $extra_info_array['delivery_mode']
														 );
						
						if ($extra_info_array['delivery_mode'] == '1') {
							//$game_currency_info_array['char_online_time'] = $extra_info_array['char_online_time'];
							//$game_currency_info_array['char_online_dur'] = $extra_info_array['char_online_dur'];
						} else if ($extra_info_array['delivery_mode'] == '2') {
							$game_currency_info_array['char_account_name'] = $extra_info_array['char_account_name'];
							$game_currency_info_array['char_account_pwd'] = $extra_info_array['char_account_pwd'];
							$game_currency_info_array['char_wow_account'] = $extra_info_array['char_wow_account'];
						}
						
						$this->contents[$products_id]['custom'][$custom_type][] = $game_currency_info_array;
					}
				} else {
					$this->contents[$products_id]['custom'][$custom_type][$char_exist_pos]['delivery_mode'] = $extra_info_array['delivery_mode'];
					
					unset($this->contents[$products_id]['custom'][$custom_type][$char_exist_pos]['char_account_name']);
					unset($this->contents[$products_id]['custom'][$custom_type][$char_exist_pos]['char_account_pwd']);
					unset($this->contents[$products_id]['custom'][$custom_type][$char_exist_pos]['char_wow_account']);
					//unset($this->contents[$products_id]['custom'][$custom_type][$char_exist_pos]['char_online_time']);
					//unset($this->contents[$products_id]['custom'][$custom_type][$char_exist_pos]['char_online_dur']);
					
					if ($extra_info_array['delivery_mode'] == '1') {
						//$this->contents[$products_id]['custom'][$custom_type][$char_exist_pos]['char_online_time'] = $extra_info_array['char_online_time'];
						//$this->contents[$products_id]['custom'][$custom_type][$char_exist_pos]['char_online_dur'] = $extra_info_array['char_online_dur'];
					} else if ($extra_info_array['delivery_mode'] == '2') {
						$this->contents[$products_id]['custom'][$custom_type][$char_exist_pos]['char_account_name'] = $extra_info_array['char_account_name'];
						$this->contents[$products_id]['custom'][$custom_type][$char_exist_pos]['char_account_pwd'] = $extra_info_array['char_account_pwd'];
						$this->contents[$products_id]['custom'][$custom_type][$char_exist_pos]['char_wow_account'] = $extra_info_array['char_wow_account'];
					}
				}
			}
		} else if ($custom_type == 'cdkey') {
			$extra_info_insert_array = array('qty' => $quantity);
			if (isset($extra_info_array['delivery_mode'])) {
				$extra_info_insert_array['delivery_mode'] = $extra_info_array['delivery_mode'];
			} else {
				$extra_info_array['delivery_mode'] = $extra_info_insert_array['delivery_mode'] = 5; // default as send to my account
			}
			
			if (isset($extra_info_array['top_up_info'])) {
				$extra_info_insert_array['top_up_info'] = $extra_info_array['top_up_info'];
			} else {
				$extra_info_insert_array['top_up_info'] = array();
			}
			
			$count_index = 0;
			
			if (isset($this->contents[$products_id]['custom']['cdkey'])) {
				ksort($this->contents[$products_id]['custom']['cdkey']);
				foreach ($this->contents[$products_id]['custom']['cdkey'] as $cdkey_index_loop => $cdkey_data_loop) {
					if (isset($cdkey_data_loop['top_up_info'])) {
						ksort($cdkey_data_loop['top_up_info']);
						reset($cdkey_data_loop['top_up_info']);
					} else {
						$cdkey_data_loop['top_up_info'] = array();
					}
									
					if (isset($extra_info_array['top_up_info'])) {
						ksort($extra_info_array['top_up_info']);
						reset($extra_info_array['top_up_info']);
					} else {
						$extra_info_array['top_up_info'] = array();
					} 
					
					if (count(array_diff_assoc($extra_info_array['top_up_info'], $cdkey_data_loop['top_up_info']))==0 &&
						count(array_diff_assoc($cdkey_data_loop['top_up_info'], $extra_info_array['top_up_info']))==0 &&
						$cdkey_data_loop['delivery_mode'] == $extra_info_array['delivery_mode'] ) {
						break;
					}
					$count_index++;
				}
			}
			
			$this->contents[$products_id]['custom'][$custom_type][$count_index] = $extra_info_insert_array;
			if ($quantity == 0) {
				unset($this->contents[$products_id]['custom'][$custom_type][$count_index]);
			}
		} else if ($custom_type == 'store_credit') {
			$this->contents[$products_id]['custom'][$custom_type][0] = array('qty' => $temp_qty);
		} else if ($custom_type == 'hla') {
			if ($this->hla_product_in_cart($products_id, $extra_info_array['hla_account_id'])) {
	      		$hla_prod_array = $this->contents[$products_id]['custom'][$custom_type];
	      		
				for ($i=0, $total_hla = count($hla_prod_array); $total_hla > $i; $i++) {
					if ($extra_info_array['hla_account_id'] == $hla_prod_array[$i]['hla_account_id']) {
						unset($this->contents[$products_id]['custom'][$custom_type][$i]);
					}
				}
				$this->arrange_custom_product($products_id);
			}
			
			$this->contents[$products_id]['custom'][$custom_type][] = array('qty' => 1, 
																			'hla_account_id' => $extra_info_array['hla_account_id']);
			
			$quantity = count($this->contents[$products_id]['custom'][$custom_type]);
			$temp_qty = (int)$quantity;
		}
		
		if ($temp_qty == 0) {
      		$this->remove($products_id);
      		return true;
      	}
      	$this->contents[$products_id]['qty'] = $temp_qty;
      	
		// update database
      	if (tep_session_is_registered('customer_id')) tep_db_query("UPDATE " . TABLE_CUSTOMERS_BASKET . " SET customers_basket_quantity = '" . $quantity . "' WHERE customers_id = '" . (int)$customer_id . "' AND products_id = '" . tep_db_input($products_id) . "'");
		
      	if (is_array($attributes)) {
        	reset($attributes);
        	while (list($option, $value) = each($attributes)) {
          		$this->contents[$products_id]['attributes'][$option] = $value;
				// update database
          		if (tep_session_is_registered('customer_id')) tep_db_query("update " . TABLE_CUSTOMERS_BASKET_ATTRIBUTES . " set products_options_value_id = '" . (int)$value . "' where customers_id = '" . (int)$customer_id . "' and products_id = '" . tep_db_input($products_id) . "' and products_options_id = '" . (int)$option . "'");
        	}
      	}
	}
	
    function cleanup() {
    	global $customer_id, $customers_groups_id, $localization;
    	
      	reset($this->contents);
      	
      	while (list($key,) = each($this->contents)) {
      		$remove_this_product = false;
      		
			if (!tep_check_product_region_permission($key)) { // verify Products available to current Region 
				$remove_this_product = true;
			} else {
	      		if ($this->get_product_type($key) == 'custom') {
	      			if (!isset($this->contents[$key]['custom']) || sizeof($this->contents[$key]['custom']) == 0) {
	      				$remove_this_product = true;
	      			}
	      		} else {
	      			if (sizeof($this->contents[$key]['custom']) > 0 && 
						!isset($this->contents[$key]['custom']['game_currency']) && 
						!isset($this->contents[$key]['custom']['store_credit']) && 
						!isset($this->contents[$key]['custom']['cdkey']) && 
						!isset($this->contents[$key]['custom']['hla'])) {	// It should not be a custom info, so kill it
	
	      				$remove_this_product = true;
	      			}
	      		}
	      		
				$custom_name_array = array ( 'game_currency', 'hla', 'cdkey' );
				for ($i=0, $total_custom = count($custom_name_array); $total_custom > $i; $i++) {
					$custom_name = $custom_name_array[$i];
					
		      		if (isset($this->contents[$key]['custom'][$custom_name])) {
		      			
		      			if (is_array($this->contents[$key]['custom'][$custom_name]) && sizeof($this->contents[$key]['custom'][$custom_name]) > 0) {
							$total_qty = 0;
							
							foreach ($this->contents[$key]['custom'][$custom_name] as $extra_info_cnt => $extra_info_array) {
								if (isset($extra_info_array['char_name']) || isset($extra_info_array['hla_account_id']) || isset($extra_info_array['top_up_info'])) {
									$total_qty += $extra_info_array['qty'];
								} else {
									unset($this->contents[$key]['custom'][$custom_name][$extra_info_cnt]);
								}
							}
							
							$this->arrange_custom_product($key);
							
							if (tep_session_is_registered('customer_id')) {
			        			$customers_basket_id_select_sql = "	SELECT customers_basket_id 
			        												FROM " . TABLE_CUSTOMERS_BASKET . " 
			        												WHERE customers_id = '" . (int)$customer_id . "' 
			        												AND products_id = '" . tep_db_input($key) . "'";
			        			$customers_basket_id_result_sql = tep_db_query($customers_basket_id_select_sql);
			        			if ($customers_basket_id_row = tep_db_fetch_array($customers_basket_id_result_sql)) {
			        				$sql_data_array = array('customers_basket_custom_value' => $this->_serialize($this->contents[$key]['custom'][$custom_name]));
			        				tep_db_perform(TABLE_CUSTOMERS_BASKET_CUSTOM, $sql_data_array, 'update', "customers_basket_id = '" . (int)$customers_basket_id_row['customers_basket_id'] . "'");
			        			}
			        		}
			        		
							if ($this->contents[$key]['qty'] != $total_qty) {
								$this->contents[$key]['qty'] = $total_qty;
							}
							
							if (!$total_qty)	unset($this->contents[$key]);
						} else {
							unset($this->contents[$key]);
						}
					} 
				}
				
	      		if (isset($this->contents[$key]['qty']) && $this->contents[$key]['qty'] < 1) {
	        		$remove_this_product = true;
	        	}
	    	}
        	
        	if (!$remove_this_product) {	// If decided to keep this product in cart, check if this product still active (both product and categories status)
	        	$p_cPath = tep_get_product_path($key, true);
	        	$p_cPath_array = tep_parse_category_path($p_cPath);
	        	
	        	$active_product = true;
	        	$permitted_product = true;
				if (is_array($p_cPath_array) && count($p_cPath_array)) {
					// Check for inactive product
				  	$inactive_cat_select_sql = "SELECT COUNT(categories_id) AS inactive_cat 
				  								FROM " . TABLE_CATEGORIES . "
				  								WHERE categories_id IN ('" . implode("', '", $p_cPath_array) . "') 
				  									AND categories_status = 0";
				  	$inactive_cat_result_sql = tep_db_query($inactive_cat_select_sql);
					$inactive_cat_row = tep_db_fetch_array($inactive_cat_result_sql);
				  	
				  	if ($inactive_cat_row['inactive_cat'] > 0)	{$active_product = false;}
				  	
				  	// Check for product access permission
				  	$permitted_cat_select_sql = "	SELECT COUNT(linkid) AS permitted_cat 
				  									FROM " . TABLE_CATEGORIES_GROUPS . "
				  									WHERE categories_id IN ('" . implode("', '", $p_cPath_array) . "') 
				  										AND ((groups_id = '".(int)$customers_groups_id."') or (groups_id=0)) ";
				  	$permitted_cat_result_sql = tep_db_query($permitted_cat_select_sql);
					$permitted_cat_row = tep_db_fetch_array($permitted_cat_result_sql);
					
				  	if ($permitted_cat_row['permitted_cat'] != count($p_cPath_array))	{$permitted_product = false;}
				}
				
				if ($active_product) {
					$product_status_select_sql = "	SELECT products_status 
				  									FROM " . TABLE_PRODUCTS . " 
				  									WHERE products_id = '" . tep_db_input($key) . "'";
					$product_status_result_sql = tep_db_query($product_status_select_sql);
					$product_status_row = tep_db_fetch_array($product_status_result_sql);
					
					if ($product_status_row['products_status'] != '1') {
						$active_product = false;
					}
				}
				
				if ($active_product) {
					$products_display_select_sql = "	SELECT products_display, products_bundle, products_bundle_dynamic 
					  									FROM " . TABLE_PRODUCTS . " 
					  									WHERE products_id = '" . tep_db_input($key) . "'";
					$products_display_result_sql = tep_db_query($products_display_select_sql);
					$products_display_row = tep_db_fetch_array($products_display_result_sql);
					
					if ($products_display_row['products_display'] != '1' && $products_display_row['products_bundle'] == '' && $products_display_row['products_bundle_dynamic'] == '') {
						$active_product = false;
					}
				}
				
				if (!$active_product || !$permitted_product) {	// Remove from cart if it is not active / not accessible at the moment
					$remove_this_product = true;
				}
			}
			
        	if ($remove_this_product) {
        		unset($this->contents[$key]);
				// remove from database
          		if (tep_session_is_registered('customer_id')) {
					$result = tep_db_query("SELECT customers_basket_id FROM ".TABLE_CUSTOMERS_BASKET." WHERE customers_id = '" . (int)$customer_id . "' AND products_id = '" . tep_db_input($key) . "'");
					while ($row = tep_db_fetch_array($result)) {
						tep_db_query("DELETE FROM " . TABLE_CUSTOMERS_BASKET_CUSTOM . " WHERE customers_basket_id = '" . (int)$row['customers_basket_id'] . ";'");
					}
					
            		tep_db_query("DELETE FROM " . TABLE_CUSTOMERS_BASKET . " WHERE customers_id = '" . (int)$customer_id . "' AND products_id = '" . tep_db_input($key) . "'");
            		tep_db_query("DELETE FROM " . TABLE_CUSTOMERS_BASKET_ATTRIBUTES . " WHERE customers_id = '" . (int)$customer_id . "' AND products_id = '" . tep_db_input($key) . "'");
            		tep_db_query("DELETE FROM " . TABLE_CUSTOMERS_BASKET_BUNDLE ." WHERE customers_id = '" . (int)$customer_id . "' AND products_bundle_id = '" . tep_db_input($key) . "'");
          		}
        	}
      	}
    }
	
	function get_product_type($pid) {
		$product_type_select_sql = "SELECT products_bundle, products_bundle_dynamic, custom_products_type_id FROM " . TABLE_PRODUCTS . " WHERE products_id = '" . (int)$pid . "'";
		$product_type_result_sql = tep_db_query($product_type_select_sql);
		$product_type_row = tep_db_fetch_array($product_type_result_sql);
		
		if ($product_type_row["custom_products_type_id"] > 0) {
			return 'custom';
		} else if ($product_type_row["products_bundle"] == 'yes') {
			return 'static';
		} else if ($product_type_row["products_bundle_dynamic"] == 'yes') {
			return 'dynamic';
		} else {
			return 'single';
		}
	}
	
    function count_contents() {	// get total number of items in cart 
      	$total_items = 0;
      	
      	if (is_array($this->contents)) {
	        reset($this->contents);
        	while (list($products_id, ) = each($this->contents)) {
          		$total_items += $this->get_quantity($products_id);
        	}
      	}
      	return $total_items;
	}
	
    function get_quantity($products_id) {
    	if (isset($this->contents[$products_id])) {
			if (sizeof($this->contents[$products_id]['custom']) > 0) {
				if (sizeof($this->contents[$products_id]['custom'][$this->get_custom_prd_type($products_id)]) > 0) { // For now, power leveling, cd key and game currency
					return 1;
				}
			} else {
			   	return $this->contents[$products_id]['qty'];
			}
      	} else {
        	return 0;
      	}
    }
	
    function in_cart($products_id) {
      	if (isset($this->contents[$products_id])) {
        	return true;
      	} else {
        	return false;
      	}
    }
    
    function hla_product_in_cart($products_id, $hla_acc_id) {
    	$custom_type = $this->get_custom_prd_type($products_id);
		$prod_cnt = tep_not_null($this->contents[$products_id]['custom'][$custom_type]) ? count($this->contents[$products_id]['custom'][$custom_type]) : 0;
		
		if ($prod_cnt > 0) {
			foreach ($this->contents[$products_id]['custom'][$custom_type] as $extra_info_cnt => $extra_info_array) {
				if ($extra_info_array['hla_account_id'] == $hla_acc_id) {
					return true;
					break;
				}
			}
		}
		
		return false;
    }
	
    function arrange_custom_product($products_id) {
    	$custom_type = $this->get_custom_prd_type($products_id);
    	$re_index = 0;
    	$temp_array = array();
    	
    	if (count($this->contents[$products_id]['custom'][$custom_type])) {
    		foreach ($this->contents[$products_id]['custom'][$custom_type] as $old_index => $custom_array) {
    			$temp_array[$re_index] = $custom_array;
    			$re_index++;
    		}
    		$this->contents[$products_id]['custom'][$custom_type] = $temp_array;
    	}
    }
	
    function remove($products_id, $custom_basket_id = -1) {
    	global $customer_id;
    	
    	$remove_from_cart = true;
    	$basket_id = 0;
    	
    	$custom_type = $this->get_custom_prd_type($products_id);
		
		if ($custom_type == 'store_credit') {
			//$basket_id = (int)$this->contents[$products_id]['custom'][$custom_type][$custom_basket_id]['content']['id'];
			$customers_basket_id_select_sql = " SELECT customers_basket_id FROM " . TABLE_CUSTOMERS_BASKET . " WHERE customers_id = '" . (int)$customer_id . "' AND products_id = '" . (int)$products_id . "'";
      		$customers_basket_id_result_sql = tep_db_query($customers_basket_id_select_sql);
      		$customers_basket_id_row = tep_db_fetch_array($customers_basket_id_result_sql);
			
			$basket_id = (int)$customers_basket_id_row['customers_basket_id'];
			
      		unset($this->contents[$products_id]);
      	} else if ($custom_type == 'game_currency' || $custom_type == 'cdkey') {
      		$customers_basket_id_select_sql = " SELECT customers_basket_id FROM " . TABLE_CUSTOMERS_BASKET . " WHERE customers_id = '" . (int)$customer_id . "' AND products_id = '" . (int)$products_id . "'";
      		$customers_basket_id_result_sql = tep_db_query($customers_basket_id_select_sql);
      		$customers_basket_id_row = tep_db_fetch_array($customers_basket_id_result_sql);
      		
      		$qty_left = $this->contents[$products_id]['qty'] - $this->contents[$products_id]['custom'][$custom_type][$custom_basket_id]['qty'];
			
      		if ($qty_left < 1) {
      			unset($this->contents[$products_id]);
      			$basket_id = (int)$customers_basket_id_row['customers_basket_id'];
      		} else {
      			$remove_from_cart = false;
      			$this->contents[$products_id]['qty'] = $qty_left;
      			unset($this->contents[$products_id]['custom'][$custom_type][$custom_basket_id]);
      			
      			tep_db_query("UPDATE " . TABLE_CUSTOMERS_BASKET . " SET customers_basket_quantity = '" . (int)$qty_left . "' WHERE customers_id = '" . (int)$customer_id . "' AND products_id = '" . tep_db_input($products_id) . "'");
      		}
		} else if ($custom_type == 'hla') {
      		$hla_acc_id = (int)$custom_basket_id;
      		
      		$customers_basket_id_select_sql = " SELECT customers_basket_id FROM " . TABLE_CUSTOMERS_BASKET . " WHERE customers_id = '" . (int)$customer_id . "' AND products_id = '" . (int)$products_id . "'";
      		$customers_basket_id_result_sql = tep_db_query($customers_basket_id_select_sql);
      		$customers_basket_id_row = tep_db_fetch_array($customers_basket_id_result_sql);
			$basket_id = (int)$customers_basket_id_row['customers_basket_id'];
      		
      		$hla_prod_array = $this->contents[$products_id]['custom'][$custom_type];
			for ($cnt=0, $total_hla = count($hla_prod_array); $total_hla > $cnt; $cnt++) {
				if ($hla_acc_id == $hla_prod_array[$cnt]['hla_account_id']) {
					unset($this->contents[$products_id]['custom'][$custom_type][$cnt]);
				}
			}
			
			$qty_left = count($this->contents[$products_id]['custom'][$custom_type]);
			if ($qty_left > 0) {
				$remove_from_cart = false;
				$this->contents[$products_id]['qty'] = $qty_left;
				
				$serialized_array = $this->_serialize($this->contents[$products_id]['custom'][$custom_type]);
				
				tep_db_query("UPDATE " . TABLE_CUSTOMERS_BASKET . " SET customers_basket_quantity = " . (int)$qty_left . " WHERE customers_id = '" . (int)$customer_id . "' AND products_id = '" . tep_db_input($products_id) . "'");
				tep_db_query("UPDATE " . TABLE_CUSTOMERS_BASKET_CUSTOM . " SET customers_basket_custom_value ='" . $serialized_array . "' WHERE customers_basket_id = '" . $basket_id . "' AND customers_basket_custom_key = '" . $custom_type . "'");
			} else {
				unset($this->contents[$products_id]);
			}
      	} else {
      		$basket_id = (int)$this->contents[$products_id]['custom'][$custom_type][$custom_basket_id]['content']['id'];
			unset($this->contents[$products_id]['custom'][$custom_type][$custom_basket_id]);
		}
		
		$this->arrange_custom_product($products_id);
		
		// remove from database
      	if (tep_session_is_registered('customer_id') && $remove_from_cart) {
			// this was custom product, handle carefully
      		if ($basket_id > 0) {
				tep_db_query("DELETE FROM " . TABLE_CUSTOMERS_BASKET . " WHERE customers_basket_id='".$basket_id."' AND customers_id = '" . (int)$customer_id . "' AND products_id = '" . tep_db_input($products_id) . "'");
				tep_db_query("DELETE FROM " . TABLE_CUSTOMERS_BASKET_CUSTOM . " WHERE customers_basket_id = '" . (int)$basket_id . ";'");
			} else {
			   	tep_db_query("DELETE FROM " . TABLE_CUSTOMERS_BASKET . " WHERE customers_id = '" . (int)$customer_id . "' AND products_id = '" . tep_db_input($products_id) . "'");
			}
        	
        	tep_db_query("DELETE FROM " . TABLE_CUSTOMERS_BASKET_ATTRIBUTES . " WHERE customers_id = '" . (int)$customer_id . "' AND products_id = '" . tep_db_input($products_id) . "'");
        	tep_db_query("DELETE FROM " . TABLE_CUSTOMERS_BASKET_BUNDLE . " WHERE customers_id = '" . (int)$customer_id . "' AND products_bundle_id = '" . tep_db_input($products_id) . "'");
      	}
		
		// assign a temporary unique ID to the order contents to prevent hack attempts during the checkout procedure
		$this->calculate();
      	$this->cartID = $this->generate_cart_id();
	}
	
    function remove_all() {
    	$this->reset();
    }
	
    function get_product_id_list()
    {
    	$product_id_list = '';
      	if (is_array($this->contents)) {
        	reset($this->contents);
        	while (list($products_id, ) = each($this->contents)) {
          		$product_id_list .= ', ' . $products_id;
        	}
      	}
		
      	return substr($product_id_list, 2);
	}
	
    function calculate()
    {
    	global $customer_id, $currency, $currencies;
    	
    	$this->total_virtual = 0; // ICW Gift Voucher System
      	$this->total = 0;
      	$this->weight = 0;
		// mod indvship
      	$this->shiptotal = 0;
		// end indvship
      	if (!is_array($this->contents)) return 0;
		
		//CGDiscountSpecials start
		/*************************************************************
			Get the customer total discount! Is based on customer not
			product so is not wise to put it in the while loop!!
		*************************************************************/
		$customer_ind_discount = 0;
		if (isset($customer_id) && tep_not_null($customer_id)) {
			$customers_discount_select_sql = "	SELECT customers_discount 
												FROM " . TABLE_CUSTOMERS . " 
												WHERE customers_id =  '" . tep_db_input($customer_id) . "'";
			$customers_discount_result_sql = tep_db_query($customers_discount_select_sql);
			$customers_discount_row = tep_db_fetch_array($customers_discount_result_sql);
			$customer_ind_discount = $customers_discount_row['customers_discount'];
		}
		
		$prod_quantity_array = array();
      	$pre_order_array = array();
      	$custom_prod_total = 0;
      	reset($this->contents);
      	
		while (list($products_id, ) = each($this->contents)) {
			$product_exists = false;
			
			$custom_type = $this->get_custom_prd_type($products_id);
			
			$contents_array = $this->contents[$products_id]['custom'][$custom_type];
			$total_prod = ($custom_type == 'hla' || $custom_type == 'power_leveling' || $custom_type == 'cdkey') ? count($this->contents[$products_id]['custom'][$custom_type]) : 1;
			
			for ($i=0; $total_prod > $i; $i++) {
				if ($custom_type == 'hla') {
					$prod_count_sql = "	SELECT count(products_id) as counted 
										FROM " . TABLE_PRODUCTS_HLA . " 
										WHERE products_id = '" . tep_db_input($products_id) . "' 
											AND products_hla_id = '" . tep_db_input($contents_array[$i]['hla_account_id']) . "'";
				} else {
					$prod_count_sql = "SELECT count(products_id) as counted FROM " . TABLE_PRODUCTS . " WHERE products_id = '" . $products_id . "'";
				}
				
				$prod_count_result = tep_db_query($prod_count_sql);
				
				if ($row = tep_db_fetch_array($prod_count_result)) {
					if ((int)$row['counted'] > 0) {
						$product_exists = true;
					} else {
						$product_exists = false;
					}
				}
				
				if ($product_exists) {
					//Nick: Only Applies to power leveling cos the increment here assumes price is 0 in product setup.
					if (sizeof($this->contents[$products_id]['custom']['power_leveling']) > 0) {
						$customers_groups_info_array = tep_get_customer_group_discount($customer_id, $products_id, 'product');
						$customers_groups_discount = $customers_groups_info_array['cust_group_discount'];
						
						$customer_discount = $customer_ind_discount + $customers_groups_discount;
						
						if ($customer_discount >= 0) {
		     				$custom_products_price = (double)($contents_array[$i]['content']['calculated']['price'] + $contents_array[$i]['content']['calculated']['price'] * abs($customer_discount) / 100);
	      				} else {
		     				$custom_products_price = (double)($contents_array[$i]['content']['calculated']['price'] - $contents_array[$i]['content']['calculated']['price'] * abs($customer_discount) / 100);
	      				}
						
	      				$custom_products_price = $currencies->display_noformat_price_nodiscount($products_id, $custom_products_price);
						$this->total += $custom_products_price;
					} else if (count($this->contents[$products_id]['custom']['hla']) > 0) {
						for ($cnt=0, $total_hla = count($this->contents[$products_id]['custom']['hla']); $total_hla > $cnt; $cnt++) {
							$hla_sql = "SELECT products_hla_id FROM " . TABLE_PRODUCTS_HLA . " WHERE products_hla_id = '" . tep_db_input($this->contents[$products_id]['custom']['hla'][$cnt]['hla_account_id']) . "'";
							$hla_result = tep_db_query($hla_sql);
							
							if (!$row = tep_db_fetch_array($hla_result)) {
								unset($this->contents[$products_id]['custom']['hla'][$cnt]);
							}
						}
						
						if (count($this->contents[$products_id]['custom']['hla']) > 0) {
							$this->arrange_custom_product($products_id);
							$this->contents[$products_id]['qty'] = count($this->contents[$products_id]['custom']['hla']);
						} else {
							unset($this->contents[$products_id]);
						}
					}
				} else {
					unset($this->contents[$products_id]);
				}
			}
		}
      	
      	reset($this->contents);
      	
      	while (list($products_id, ) = each($this->contents)) {
			$custom_type = $this->get_custom_prd_type($products_id);
			
			$total_prod = ($custom_type == 'hla' || $custom_type == 'power_leveling' || $custom_type == 'cdkey') ? count($this->contents[$products_id]['custom'][$custom_type]) : 1;
			
			for ($i=0; $total_prod > $i; $i++) {
				$pre_order = false;
				$qty = $this->contents[$products_id]['qty'];
				
				$products_sql = "	SELECT products_bundle, products_bundle_dynamic, products_weight 
									FROM " . TABLE_PRODUCTS . " 
									WHERE products_id = '" . (int)$products_id . "'";
				$products_query = tep_db_query($products_sql);
				if ($products = tep_db_fetch_array($products_query)) {
					if ($products['products_bundle'] == 'yes') {
						$bundle_select_sql = "	SELECT pb.subproduct_id, pb.subproduct_qty, p.products_status, p.products_weight 
												FROM " . TABLE_PRODUCTS_BUNDLES . " AS pb 
												LEFT JOIN " . TABLE_PRODUCTS . " AS p 
													ON pb.subproduct_id = p.products_id  
												WHERE bundle_id='" . $products_id . "'" ;
						$bundle_result_sql = tep_db_query($bundle_select_sql);
						
						while ($bundle_data = tep_db_fetch_array($bundle_result_sql)) {
							if (!$bundle_data["products_status"]) {
								$pre_order = false;
								break;
							}
							
							$prod_quantity_array[$bundle_data["subproduct_id"]] += $qty * $bundle_data["subproduct_qty"];
							
							$stock_status = tep_check_stock_status($bundle_data["subproduct_id"], $prod_quantity_array[$bundle_data["subproduct_id"]]);
							
							if ($stock_status == "pre-order") {
								$pre_order = true;
							}
							
							$this->weight += ($qty * $bundle_data["subproduct_qty"] * $bundle_data["products_weight"]);
						}
					} else if ($products['products_bundle_dynamic'] == 'yes') {
						$bundle_select_sql = "	SELECT cb.subproducts_quantity, pb.subproduct_id, pb.subproduct_qty, p.products_status, p.products_weight 
												FROM " . TABLE_CUSTOMERS_BASKET_BUNDLE . " AS cb
												LEFT JOIN " . TABLE_PRODUCTS_BUNDLES . " AS pb
													ON cb.products_bundle_id = pb.bundle_id 
												LEFT JOIN " . TABLE_PRODUCTS . " AS p
													ON pb.subproduct_id = p.products_id 
												WHERE cb.customers_id = '" . (int)$customer_id . "' AND cb.subproducts_id = pb.subproduct_id AND cb.products_bundle_id = '" . $products_id . "'";
						$bundle_result_sql = tep_db_query($bundle_select_sql);
						
						while ($bundle_data = tep_db_fetch_array($bundle_result_sql)) {
							if ($bundle_data["subproducts_quantity"]) {		// if got value implies customer select this subproduct 
								if (!$bundle_data["products_status"]) {
									$pre_order = false;
									break;
								} else {
									$prod_quantity_array[$bundle_data["subproduct_id"]] += $bundle_data["subproducts_quantity"]*$bundle_data["subproduct_qty"];
									$stock_status = tep_check_stock_status($bundle_data["subproduct_id"], $prod_quantity_array[$bundle_data["subproduct_id"]]);
									
									if ($stock_status == "pre-order") {
										$pre_order = true;
									}
								}
								
								$this->weight += ($bundle_data["subproducts_quantity"] * $bundle_data["subproduct_qty"] * $bundle_data["products_weight"]);
							}
						}
					} else {
						$prod_status_select_sql = "SELECT products_status FROM " . TABLE_PRODUCTS . " WHERE products_id = '" . (int)$products_id . "'";
						$prod_status_result_sql = tep_db_query($prod_status_select_sql);
						if ($prod_status_row = tep_db_fetch_array($prod_status_result_sql)) {
							if (!$prod_status_row["products_status"]) {
								$pre_order = false;
							} else {
								$prod_quantity_array[$products_id] += $qty;
								
								$product_instance_id = tep_not_null($this->contents[$products_id]['custom'][$custom_type][$i]['hla_account_id']) ? $this->contents[$products_id]['custom'][$custom_type][$i]['hla_account_id'] : '';
								$stock_status = tep_check_stock_status($products_id, $prod_quantity_array[$products_id], $product_instance_id);
								if ($stock_status == "pre-order") {
									$pre_order = true;
								}
							}
						}
						
						$this->weight += ($qty * $products["products_weight"]);
					}
				}
				if ($pre_order)	 $pre_order_array[] = $products_id;
			}
		}
		
 		reset($this->contents);
  		$cnt = 1;
  		//$cnt_bundle = count($bundleV_array);
  		while (list($products_id, ) = each($this->contents)) {
			$custom_type = $this->get_custom_prd_type($products_id);
			
			$total_prod = ($custom_type == 'hla' || $custom_type == 'power_leveling' || $custom_type == 'cdkey') ? count($this->contents[$products_id]['custom'][$custom_type]) : 1;
			
			for ($i=0; $total_prod > $i; $i++) {
				$product_instance_id = tep_not_null($this->contents[$products_id]['custom'][$custom_type][$i]['hla_account_id']) ? $this->contents[$products_id]['custom'][$custom_type][$i]['hla_account_id'] : '';
	    		$currencies->product_instance_id = $product_instance_id;
	    		
	    		if ($custom_type=='cdkey') {
	    			$qty = $this->contents[$products_id]['custom']['cdkey'][$i]['qty'];
	    		} else {
	    			$qty = $this->contents[$products_id]['qty'];
	    		}
	    		
	    		// Merging store changes: Get the customer group discount
				$customers_groups_info_array = tep_get_customer_group_discount($customer_id, $products_id, 'product');
				$customers_groups_discount = $customers_groups_info_array['cust_group_discount'];
				
				$customer_discount = $customer_ind_discount + $customers_groups_discount;
				
				// products price
				if ($custom_type == 'hla') {
	    			$qty = $this->contents[$products_id]['custom'][$custom_type][$i]['qty'];
	    			
					$product_query = tep_db_query("	SELECT p.products_id, ph.products_price, p.products_ship_price, 
														p.products_tax_class_id, p.products_weight 
													FROM " . TABLE_PRODUCTS . " AS p 
													LEFT JOIN " . TABLE_PRODUCTS_HLA . " AS ph 
														ON ph.products_id = p.products_id 
													WHERE p.products_id = '" . (int)$products_id . "'
														AND ph.products_hla_id = '" . (int)$product_instance_id . "'");
				} else {
    				$product_query = tep_db_query("SELECT products_id, products_price, products_ship_price, products_tax_class_id, products_weight FROM " . TABLE_PRODUCTS . " WHERE products_id = '" . (int)$products_id . "'");
				}
				
	    		if ($product = tep_db_fetch_array($product_query)) {
	    			// Merging store changes: Get the cat defined configuration settings
	    			$cat_cfg_array = tep_get_cfg_setting($products_id, 'product', 'PRE_ORDER_DISCOUNT');
	    			
					// ICW ORDER TOTAL CREDIT CLASS Start Amendment
	      			$no_count = 1;
	      			$gv_query = tep_db_query("SELECT products_model FROM " . TABLE_PRODUCTS . " WHERE products_id = '" . (int)$products_id . "'");
	      			$gv_result = tep_db_fetch_array($gv_query);
	      			if (ereg('^GIFT', $gv_result['products_model'])) {
	        			$no_count = 0;
	      			}
					// ICW ORDER TOTAL  CREDIT CLASS End Amendment
	      			$prid = $product['products_id'];
					
	      			$products_tax = tep_get_tax_rate($product['products_tax_class_id']);
	      			$products_price = $product['products_price'];
	  				$products_weight = $product['products_weight'];
					
	    			// pre-order discount
	          		if (in_array($products_id, $pre_order_array) && abs($cat_cfg_array['PRE_ORDER_DISCOUNT'])) {
	      				$pre_order_discount = $cat_cfg_array['PRE_ORDER_DISCOUNT'];
	      			} else {
	      				$pre_order_discount = 0;
	      			}
	      			
	    			$products_price = $currencies->get_product_price($products_id, $customer_id, $pre_order_discount, '');
	    			
					//CGDiscountSpecials end
					// mod indvship
					$products_ship_price = $product['products_ship_price'];
					// end indvship
					$num = ($cnt == 1) ? 0 : 1 ;
					
					if ($products_id == $this->pm_after_id) {
						//echo $num ."===". $cnt ."===". $qty;
	  	  	  			if ($cnt >= 1 && $qty >1) {
	    					if ($qty > $num) {	
	    						if ($this->pm_free) {
									$qty = $qty - 1;
	    						}
							}
	  					} else if ($cnt > 1 && $qty > 0) {
	  						if ($qty >= $num) {
	    						if ($this->pm_free) {
									$qty = $qty - 1;
	    						}
							}
	  					} else if ($cnt >= 1 && $qty > 0) {
	  						if ($qty >= $num) {
	    						if ($this->pm_free) {
									$qty = $qty - 1;
	    						}
							}
	  					}
					}
					$this->total += $products_price * $qty;
		      		
					// mod indvship
					$this->shiptotal += ($products_ship_price * $qty);
					// end indvship
					//$this->weight += ($qty * $products_weight);
				}
				
				$cnt++;
				
				// attributes price
				if (isset($this->contents[$products_id]['attributes'])) {
		  			reset($this->contents[$products_id]['attributes']);
		  			while (list($option, $value) = each($this->contents[$products_id]['attributes'])) {
		    			$attribute_price_query = tep_db_query("select options_values_price, price_prefix from " . TABLE_PRODUCTS_ATTRIBUTES . " where products_id = '" . (int)$prid . "' and options_id = '" . (int)$option . "' and options_values_id = '" . (int)$value . "'");
		    			$attribute_price = tep_db_fetch_array($attribute_price_query);
		            	
					  	if ($customer_discount >= 0) {
		     				$attribute_price['options_values_price'] = $attribute_price['options_values_price'] + $attribute_price['options_values_price'] * abs($customer_discount) / 100;
		  				} else {
		     				$attribute_price['options_values_price'] = $attribute_price['options_values_price'] - $attribute_price['options_values_price'] * abs($customer_discount) / 100;
		  				}
		  				
		    			if ($attribute_price['price_prefix'] == '+') {
		      				$this->total += $qty * tep_add_tax($attribute_price['options_values_price'], $products_tax);
		    			} else {
		      				$this->total -= $qty * tep_add_tax($attribute_price['options_values_price'], $products_tax);
		    			}
		  			}
				}
			}
		}
	}
	
    function attributes_price($products_id) {
    	global $customer_id;
    	
    	$attributes_price = 0;
		
      	if (isset($this->contents[$products_id]['attributes'])) {
      		//CGDiscountSpecials start
      		$query = tep_db_query("select customers_discount from " . TABLE_CUSTOMERS . " where customers_id =  '" . $customer_id . "'");
          	$query_result = tep_db_fetch_array($query);
          	$customer_ind_discount = $query_result['customers_discount'];
          	
          	// Merging store changes: Get the customer group discount
    		$customers_groups_info_array = tep_get_customer_group_discount($customer_id, $products_id, 'product');
			$customers_groups_discount = $customers_groups_info_array['cust_group_discount'];
			$customer_discount = $customer_ind_discount + $customers_groups_discount;
			
          	//CGDiscountSpecials end
          	
        	reset($this->contents[$products_id]['attributes']);
        	while (list($option, $value) = each($this->contents[$products_id]['attributes'])) {
          		$attribute_price_query = tep_db_query("select options_values_price, price_prefix from " . TABLE_PRODUCTS_ATTRIBUTES . " where products_id = '" . (int)$products_id . "' and options_id = '" . (int)$option . "' and options_values_id = '" . (int)$value . "'");
          		$attribute_price = tep_db_fetch_array($attribute_price_query);
          		
		  		if ($customer_discount >= 0) {
		     		$attribute_price['options_values_price'] = $attribute_price['options_values_price'] + $attribute_price['options_values_price'] * abs($customer_discount) / 100;
	      		} else {
		     		$attribute_price['options_values_price'] = $attribute_price['options_values_price'] - $attribute_price['options_values_price'] * abs($customer_discount) / 100;
	      		}
				
          		if ($attribute_price['price_prefix'] == '+') {
            		$attributes_price += $attribute_price['options_values_price'];
          		} else {
            		$attributes_price -= $attribute_price['options_values_price'];
          		}
        	}
      	}
		
      	return $attributes_price;
	}
    
	function checkout_permission() {
		global $customer_id;
		
		$products = $this->get_products();
		if (!count($products)) return false;
		$checked_products = array();
		
		for ($i=0; $i < count($products); $i++) {
			// Products Checkout Quantity Control
			if (tep_not_null($products[$i]['checkout_qty_exceed']) && ($products[$i]['checkout_qty_exceed'] == 1)) {
				return false;
			}
			
			$prod_bd_query = tep_db_query("SELECT products_bundle, products_bundle_dynamic FROM " . TABLE_PRODUCTS . " WHERE products_id = " . $products[$i]['id'] . " ORDER BY products_id");
	  		$prod_bd = tep_db_fetch_array($prod_bd_query);
  	  		$prod_bundle = $prod_bd['products_bundle'];
  	  		$prod_bundle_dynamic = $prod_bd['products_bundle_dynamic'];
  	  		
  	  		if (STOCK_CHECK == 'true') {
	      		if ($prod_bundle=="yes") {
	      			$bundle_select_sql = "	SELECT p.products_id, p.products_quantity, p.products_status 
											FROM products AS p 
											INNER JOIN products_bundles AS pb
										  		ON pb.subproduct_id=p.products_id 
											WHERE pb.bundle_id ='" . $products[$i]['id'] . "'"  ;
					$bundle_result_sql = tep_db_query($bundle_select_sql);
					
					while ($bundle_data = tep_db_fetch_array($bundle_result_sql)) {
						if (!$bundle_data["products_status"]) {  // not success if one of the subproduct is inactive
							return false;
							break;
						}
						
			      		if (isset($products[$i]['custom_content']['delivery_mode']) && $products[$i]['custom_content']['delivery_mode'] == '6') {
			      			//
			      		} else {
							$skip_stock_checking = tep_single_product_skip_stock_check($bundle_data["products_id"]);
							
							if ($skip_stock_checking['state'] == -1) {
								return false;
								break;
							} else if ($skip_stock_checking['state'] == 0) {
								$current_qty_in_cart = tep_get_total_product_in_cart($customer_id, $bundle_data["products_id"]);
								if ($bundle_data['products_quantity'] - $current_qty_in_cart < $skip_stock_checking['stock_level']) {
									return false;
									break;
								} else {
									$checked_products[] = $bundle_data["products_id"];
								}
							}
						}
					}
	      		} else if ($prod_bundle_dynamic == 'yes') {
	      			// syn the badket bundle table then check for add more than allowed weight
	      			if (!$this->syn_basket_bundle($customer_id, $products[$i]['id']))	return false;
	      			
	      			$bundle_select_sql = "	SELECT cb.subproducts_quantity, pb.subproduct_id, pb.subproduct_qty, p.products_status, p.products_quantity 
	    									FROM " . TABLE_CUSTOMERS_BASKET_BUNDLE . " AS cb
	    									LEFT JOIN " . TABLE_PRODUCTS_BUNDLES . " AS pb
	    										ON cb.products_bundle_id = pb.bundle_id 
	    									LEFT JOIN " . TABLE_PRODUCTS . " AS p
    											ON pb.subproduct_id = p.products_id 
	    									WHERE cb.customers_id = '" . (int)$customer_id . "' AND cb.subproducts_id = pb.subproduct_id AND cb.products_bundle_id = '" . $products[$i]['id'] . "'";
					$bundle_result_sql = tep_db_query($bundle_select_sql);
					
					while ($bundle_data = tep_db_fetch_array($bundle_result_sql)) {
						if (!$bundle_data["products_status"]) {
							return false;
							break;
						}
						
						if ($bundle_data["subproducts_quantity"]) {
							if (isset($products[$i]['custom_content']['delivery_mode']) && $products[$i]['custom_content']['delivery_mode'] == '6') {
								//
							} else {
								$skip_stock_checking = tep_single_product_skip_stock_check($bundle_data["subproduct_id"]);
								
								if ($skip_stock_checking['state'] == -1) {
									return false;
									break;
								} else if ($skip_stock_checking['state'] == 0) {
									$current_qty_in_cart = tep_get_total_product_in_cart($customer_id, $bundle_data["subproduct_id"]);
									if ($bundle_data["products_quantity"] - $current_qty_in_cart < $skip_stock_checking['stock_level']) {
				          				return false;
										break;
									} else {
										$checked_products[] = $bundle_data["subproduct_id"];
									}
								}
								
								if (!$skip_stock_checking) {
									$current_qty_in_cart = tep_get_total_product_in_cart($customer_id, $bundle_data["subproduct_id"]);
									if ($bundle_data["products_quantity"] < $current_qty_in_cart) {
				          				return false;
										break;
									} else {
										$checked_products[] = $bundle_data["subproduct_id"];
									}
								}
							}
		        		}
					}
	      		} else {
	      			if (isset($products[$i]['custom_content']['delivery_mode']) && $products[$i]['custom_content']['delivery_mode']=='6') {
	      				//
	      			} else {
		      			$custom_type = $this->get_custom_prd_type($products[$i]['id']);
		      			if ($custom_type == 'hla') {
							$prod_inventory_select_sql = "	SELECT available_quantity AS products_quantity, products_status 
															FROM " . TABLE_PRODUCTS_HLA . " 
															WHERE products_id = '" . tep_db_input($products[$i]['id']) . "' 
																AND products_hla_id = '" . tep_db_input($products[$i]['custom_content']['hla_account_id']) . "'";
						} else {
		      				$prod_inventory_select_sql = "SELECT products_quantity, products_status FROM " . TABLE_PRODUCTS . " WHERE products_id = '" . $products[$i]['id'] . "'";
		      			}
	                    $prod_inventory_result_sql = tep_db_query($prod_inventory_select_sql);
						
						if ($prod_inventory_row = tep_db_fetch_array($prod_inventory_result_sql)) {
							if (!$prod_inventory_row["products_status"]) {
								return false;
								break;
							}
							
							if (($custom_type == 'hla') && (0 >= $prod_inventory_row["products_quantity"])) {
								return false;
								break;
							} else {
								if (isset($products[$i]['custom_content']['delivery_mode']) && $products[$i]['custom_content']['delivery_mode'] == '6') {
									//
								} else {
									$skip_stock_checking = tep_single_product_skip_stock_check($products[$i]['id']);
									
									if ($skip_stock_checking['state'] == -1) {
										return false;
										break;
									} else if ($skip_stock_checking['state'] == 0) {
										$current_qty_in_cart = tep_get_total_product_in_cart($customer_id, $products[$i]['id']);
										if ($prod_inventory_row["products_quantity"] - $current_qty_in_cart < $skip_stock_checking['stock_level']) {
				          					return false;
											break;
										} else {
											$checked_products[] = $bundle_data["subproduct_id"];
										}
									}
								}
							}
						}
					}
	        	}
	      	}
    	}
    	return true;
	}
	
	function syn_basket_bundle($customer_id, $products_id) {	// apply for dynamic product only
		$selected_subproduct = array();
		$total_bundle_weight = 0;
		
		$bundle_weight_select_sql = "SELECT products_bundle_dynamic_qty FROM " . TABLE_PRODUCTS . " WHERE products_id='".$products_id."'";
		$bundle_weight_result_sql = tep_db_query($bundle_weight_select_sql);
		$bundle_weight_row = tep_db_fetch_array($bundle_weight_result_sql);
		
		if ($bundle_weight_row["products_bundle_dynamic_qty"]) {
			$allowed_weight = $this->contents[$products_id]['qty'] * $bundle_weight_row["products_bundle_dynamic_qty"];
			
			$dynamic_subproducts_select_sql = "	SELECT cb.customers_basket_bundle_id, cb.subproducts_quantity, pb.subproduct_id, pb.subproduct_weight 
												FROM " . TABLE_CUSTOMERS_BASKET_BUNDLE . " AS cb
												LEFT JOIN " . TABLE_PRODUCTS_BUNDLES . " AS pb
													ON cb.products_bundle_id = pb.bundle_id 
												LEFT JOIN " . TABLE_PRODUCTS . " AS p
													ON pb.subproduct_id = p.products_id 
												WHERE cb.customers_id = '" . (int)$customer_id . "' AND cb.subproducts_id = pb.subproduct_id AND cb.products_bundle_id = '" . $products_id . "'";
			$dynamic_subproducts_result_sql = tep_db_query($dynamic_subproducts_select_sql);
			
			while ($dynamic_subproducts_row = tep_db_fetch_array($dynamic_subproducts_result_sql)) {
				if (in_array($dynamic_subproducts_row["subproduct_id"], $selected_subproduct)) {
					$duplicate_subproduct_delete_sql = "DELETE FROM " . TABLE_CUSTOMERS_BASKET_BUNDLE . " WHERE customers_basket_bundle_id='" . $dynamic_subproducts_row["customers_basket_bundle_id"] . "'";
					tep_db_query($duplicate_subproduct_delete_sql);
				} else {
					$selected_subproduct[] = $dynamic_subproducts_row["subproduct_id"];
					$total_bundle_weight += $dynamic_subproducts_row["subproducts_quantity"] * $dynamic_subproducts_row["subproduct_weight"];
				}
			}
			
			if (!count($selected_subproduct)) {
				return false;	// empty selections
			} else if ($total_bundle_weight && $total_bundle_weight > $allowed_weight) {
				return false;	// overweight
			} else {
				return true;	// valid selections
			}
		} else {
			return false;
		}
	}
	
    function get_products() {
    	global $languages_id, $pm_status, $pm_period, $pm_date_from, $pm_date_to, $pm_value, $customer_id, $currencies;
		
      	if (!is_array($this->contents)) return false;
      	
      	$customers_discount_select_sql = "	SELECT customers_discount 
      										FROM " . TABLE_CUSTOMERS . " 
      										WHERE customers_id =  '" . $customer_id . "'";
      	$customers_discount_result_sql = tep_db_query($customers_discount_select_sql);
  		$customers_discount_row = tep_db_fetch_array($customers_discount_result_sql);
  		$customer_ind_discount = $customers_discount_row['customers_discount'];
  		
      	$this->cleanup();	// Get the latest "valid" product since product's type might get changed during customer doing shopping
      	
      	$prod_quantity_array = array();
      	$pre_order_array = array();
      	reset($this->contents);
		
      	while (list($products_id, ) = each($this->contents)) {
      		$custom_type = $this->get_custom_prd_type($products_id);
			
			$total_prod = ($custom_type == 'hla' || $custom_type == 'power_leveling' || $custom_type == 'cdkey') ? count($this->contents[$products_id]['custom'][$custom_type]) : 1;
			for ($i=0; $total_prod > $i; $i++) {
	      		$pre_order = false;
	      		
	      		if ($custom_type=='cdkey') {
	      			$qty = $this->contents[$products_id]['custom']['cdkey'][$i]['qty'];
	      		} else {
      				$qty = $this->contents[$products_id]['qty'];
      			}
	      		
	      		$products_query = tep_db_query("SELECT products_bundle, products_bundle_dynamic FROM " . TABLE_PRODUCTS . " WHERE products_id = '" . (int)$products_id . "'");
	        	if ($products = tep_db_fetch_array($products_query)) {
			      	if ($products["products_bundle"] == "yes") {
						$bundle_select_sql = "	SELECT pb.subproduct_id, pb.subproduct_qty, p.products_status 
												FROM " . TABLE_PRODUCTS_BUNDLES . " AS pb 
												LEFT JOIN " . TABLE_PRODUCTS . " AS p 
													ON pb.subproduct_id = p.products_id  
												WHERE bundle_id='" . $products_id . "'" ;
						$bundle_result_sql = tep_db_query($bundle_select_sql);
						while ($bundle_data = tep_db_fetch_array($bundle_result_sql)) {
							if (!$bundle_data["products_status"]) {
								$pre_order = false;
								break;
							}
							
							$prod_quantity_array[$bundle_data["subproduct_id"]] += $qty * $bundle_data["subproduct_qty"];
							$stock_status = tep_check_stock_status($bundle_data["subproduct_id"], $prod_quantity_array[$bundle_data["subproduct_id"]], '', $products_id);
			        		if ($stock_status == "pre-order") {
			          			$pre_order = true;
			          		}
						}
					} else if ($products["products_bundle_dynamic"] == "yes") {
						$bundle_select_sql = "	SELECT cb.subproducts_quantity, pb.subproduct_id, pb.subproduct_qty, p.products_status
												FROM " . TABLE_CUSTOMERS_BASKET_BUNDLE . " AS cb
												LEFT JOIN " . TABLE_PRODUCTS_BUNDLES . " AS pb
													ON cb.products_bundle_id = pb.bundle_id 
												LEFT JOIN " . TABLE_PRODUCTS . " AS p
													ON pb.subproduct_id = p.products_id 
												WHERE cb.customers_id = '" . (int)$customer_id . "' AND cb.subproducts_id = pb.subproduct_id AND cb.products_bundle_id = '" . $products_id . "'";
						$bundle_result_sql = tep_db_query($bundle_select_sql);
						
						while ($bundle_data = tep_db_fetch_array($bundle_result_sql)) {
							if ($bundle_data["subproducts_quantity"]) {		// if got value implies customer select this subproduct 
								if (!$bundle_data["products_status"]) {
									$pre_order = false;
									break;
								} else {
									$prod_quantity_array[$bundle_data["subproduct_id"]] += $bundle_data["subproducts_quantity"]*$bundle_data["subproduct_qty"];
				        			$stock_status = tep_check_stock_status($bundle_data["subproduct_id"], $prod_quantity_array[$bundle_data["subproduct_id"]], '', $products_id);
									
				          			if ($stock_status == "pre-order") {
						        		$pre_order = true;
				        			}
				        		}
				        	}
						}
					} else {
						$prod_status_select_sql = "	SELECT products_status 
													FROM " . TABLE_PRODUCTS . " 
													WHERE products_id = '" . (int)$products_id . "'";
			            $prod_status_result_sql = tep_db_query($prod_status_select_sql);
			            if ($prod_status_row = tep_db_fetch_array($prod_status_result_sql)) {
			            	if (!$prod_status_row["products_status"]) {
			            		$pre_order = false;
							} else {
				      			$prod_quantity_array[$products_id] += $qty;
				      			
								$product_instance_id = tep_not_null($this->contents[$products_id]['custom'][$custom_type][$i]['hla_account_id']) ? $this->contents[$products_id]['custom'][$custom_type][$i]['hla_account_id'] : '';
					        	$stock_status = tep_check_stock_status($products_id, $prod_quantity_array[$products_id], $product_instance_id);
				        		if ($stock_status == "pre-order") {
				        			$pre_order = true;
				        		}
					        }
			            }
					}
				}
				
				if ($pre_order)	 $pre_order_array[] = $products_id;
			}
		}
		
		$products_array = array();
      	$pm_free = 0;
     	
     	if (tep_session_is_registered('customer_id')) {
      		if(STORE_PROMOTION == 'true'){
      			//if($pm_status == true){
		    	reset($this->contents);
		    	$cnt_pro = 0;
				while (list($products_id, ) = each($this->contents)) {
					$cnt_pro++;
					
	      			$qty = $this->contents[$products_id]['qty'];
	      			
					$custom_type = $this->get_custom_prd_type($products_id);
					
					$total_prod = ($custom_type == 'hla' || $custom_type == 'power_leveling' || $custom_type == 'cdkey') ? count($this->contents[$products_id]['custom'][$custom_type]) : 1;
					
					for ($i=0; $total_prod > $i; $i++) {
						$product_instance_id = tep_not_null($this->contents[$products_id]['custom'][$custom_type][$i]['hla_account_id']) ? $this->contents[$products_id]['custom'][$custom_type][$i]['hla_account_id'] : '';
						$currencies->product_instance_id = $product_instance_id;
						
						if ($custom_type == 'hla') {
			    			$qty = $this->contents[$products_id]['custom'][$custom_type][$i]['qty'];
			    			
							$pro_select_sql = "	SELECT p.products_id, p.products_model, ph.products_price, ph.products_base_currency, p.products_weight, p.products_tax_class_id 
												FROM " . TABLE_PRODUCTS_HLA . " AS ph 
												LEFT JOIN " . TABLE_PRODUCTS . " AS p 
													ON p.products_id = ph.products_id 
												WHERE ph.products_id = '" . (int)$products_id . "'
													AND ph.products_hla_id = '" . $product_instance_id . "'";
						} else {
							$pro_select_sql = " select p.products_id, p.products_model, p.products_price, p.products_weight, p.products_tax_class_id 
												from " . TABLE_PRODUCTS . " p
												where p.products_id = '" . (int)$products_id . "'";
						}
						
						$pro_query = tep_db_query($pro_select_sql);
						if ($prod = tep_db_fetch_array($pro_query)) {
							$prod_price = $prod['products_price'];
							$prod_id = $prod['products_id'];
							$prod_price_normal =  $prod_price;		// normal price before discount
							
							// Merging store changes: Get the cat defined configuration settings
		        			$cat_cfg_array = tep_get_cfg_setting($prod_id, 'product', 'PRE_ORDER_DISCOUNT');
		        			
		        			// pre-order discount
			          		if (in_array($products_id, $pre_order_array) && abs($cat_cfg_array['PRE_ORDER_DISCOUNT'])) {
			      				$pre_order_discount = $cat_cfg_array['PRE_ORDER_DISCOUNT'];
			      			} else {
			      				$pre_order_discount = 0;
			      			}
			      			
							$prod_price = $currencies->get_product_price($prod_id, $customer_id, $pre_order_discount, (tep_not_null($prod['products_base_currency']) ? $prod['products_base_currency'] : $products['products_base_currency']));
				          	
							if(!$ntotal) {
								$ntotal = ($prod_price * $qty);		// price after discount
							} else {
								$ntotal = $ntotal + ($prod_price * $qty);
							}
							
							if ($price_cheap) { //2nd onward product go here
								$pm_free = 1;
								$this->pm_free = $pm_free;
								
								if($price_cheap >= $prod_price){
									//get now price
									$pm_after_id = $prod_id;
									$this->pm_after_id = $pm_after_id;
									$pm_after_price =  $prod_price;
									$price_cheap = $prod_price; //pass the cheapest value to compare next item
								} else {
									//get prev price
									$pm_after_price =  $price_cheap;	
								}
							} else { //first product go here
								$price_cheap = $prod_price;
								$pm_after_price =  $prod_price;
								$pm_after_id = $prod_id;
								$this->pm_after_id = $pm_after_id;
								
								if($qty > 1){ 
									$pm_free = 1;
									$this->pm_free = $pm_free;
									$qty = $qty - 1;
								}
							}
						}
					}
				}
      		}
        	
			if($pm_free) {
				$ntotal = $ntotal - $pm_after_price;
				
				if($ntotal >= ($pm_value + 0)){
					//echo "PM is valid!";
					$pm_free = 1;
					$this->pm_free = $pm_free;
				} else {
					$pm_free = 0;
					$this->pm_free = $pm_free;	
				}
			}
      	//}
      	}
		
		$promotions_query = tep_db_query("select promotions_id, promotions_title, promotions_description, promotions_from, promotions_to, promotions_min_value from promotions where promotions_status = '1' order by promotions_id");
  		$promotions = tep_db_fetch_array($promotions_query);
		if ($promotions['promotions_id']) {
	    	$pm_date_from = $promotions['promotions_from'];
			$pm_date_to = $promotions['promotions_to'];
			$pm_value = $promotions['promotions_min_value'];
			
			$pm_todate = split("[/]", date('m/d/Y'));
		  	if($pm_todate[2])
		  		$pmtoday = mktime(0, 0, 0, $pm_todate[0], $pm_todate[1] ,$pm_todate[2] );
			
			$pm_dtfrom = split("[/]", tep_date_short($pm_date_from));
		  	if($pm_dtfrom[2])
		  		$pmfrom = mktime(0, 0, 0, $pm_dtfrom[0], $pm_dtfrom[1] ,$pm_dtfrom[2] );
		  	
		  	$pm_dtto = split("[/]", tep_date_short($pm_date_to));
		  	if($pm_dtto[2]) {
		  	   	$pmto = mktime(0, 0, 0, $pm_dtto[0], $pm_dtto[1] ,$pm_dtto[2] );
		  	    
		  	    if($pm_free) {
		  	    	if($pmfrom <= $pmtoday && $pmtoday <= $pmto){
		  	   			//echo "PM start n end date";
		  	   			$pm_free = 1;
						
						if($ntotal >= ($pm_value + 0)){
							//echo "PM is valid!";
							$pm_free = 1;
							$this->pm_free = $pm_free;
						} else {
							$pm_free = 0;
							$this->pm_free = $pm_free;	
						}
		  	   		} else {
		  	   			if($pmtoday >= $pmto && $pmfrom <= $pmtoday){
		  	   				//echo "PM already end date!";	
							$pm_free = 0;
							$this->pm_free = $pm_free;
		  	   			}
		  	   		}
		  	    } else {
		  	    	$pm_free = 0;
					$this->pm_free = $pm_free;
				}
			}
		} else {
		    $pm_free = 0;
		    $this->pm_free = $pm_free;
		}   
      	
      	reset($this->contents);
      	//$cnt_bundle = count($bundleV_array);
      	$cnt = 0;
      	while (list($products_id, ) = each($this->contents)) {
			$custom_type = $this->get_custom_prd_type($products_id);
			$contents_array = $this->contents[$products_id]['custom'][$custom_type];
			$total_prod = ($custom_type == 'hla' || $custom_type == 'power_leveling' || $custom_type == 'cdkey') ? count($contents_array) : 1;
			
			for ($i=0; $total_prod > $i; $i++) {
				$pre_order = false;
				
	      		$qty = $this->contents[$products_id]['qty'];
      			
				$product_instance_id = tep_not_null($contents_array[$i]['hla_account_id']) ? $contents_array[$i]['hla_account_id'] : '';
				$currencies->product_instance_id = $product_instance_id;
				
				$prod_cat_path = tep_get_product_path($products_id, true);
				if (tep_not_null($prod_cat_path)) {
					$prod_cat_path_array = explode('_', $prod_cat_path);
					$game_id = $prod_cat_path_array[0];
				} else {
					$game_id = 0;
				}
/*			
				$products_select_sql = "select p.products_id, p.products_model, p.products_image, p.products_price, p.products_base_currency, p.products_bundle, p.products_bundle_dynamic, p.products_weight, p.products_tax_class_id,p.custom_products_type_id 
										from " . TABLE_PRODUCTS . " p
										where p.products_id = '" . (int)$products_id . "'";
*/
				if ($custom_type == 'hla') {
					$products_select_sql = "SELECT p.products_id, p.products_model, pd.products_image, ph.products_price, ph.products_base_currency, p.products_bundle, p.products_bundle_dynamic, p.products_weight, p.products_tax_class_id, p.custom_products_type_id 
											FROM " . TABLE_PRODUCTS_HLA . " AS ph 
											LEFT JOIN " . TABLE_PRODUCTS . " AS p 
												ON p.products_id = ph.products_id 
											INNER JOIN " . TABLE_PRODUCTS_DESCRIPTION . " pd 
												ON ph.products_id = pd.products_id
											WHERE ph.products_id = '" . (int)$products_id . "'
												AND ph.products_hla_id = '" . (int)$product_instance_id . "'";
				} else {
					$products_select_sql = "SELECT p.products_id, p.products_model, pd.products_image, p.products_price, p.products_base_currency, p.products_bundle, p.products_bundle_dynamic, p.products_weight, p.products_tax_class_id,p.custom_products_type_id 
											FROM " . TABLE_PRODUCTS . " p
											INNER JOIN " . TABLE_PRODUCTS_DESCRIPTION . " pd 
												ON p.products_id=pd.products_id
											WHERE p.products_id = '" . (int)$products_id . "'
												AND pd.language_id = '1'";
				}
	  			
	      		$products_query = tep_db_query($products_select_sql);
	        	if ($products = tep_db_fetch_array($products_query)) {
	        		$prod_applied_discount = array();	// Must be in correct ordering
	          		$prid = $products['products_id'];
	          		$products_name = tep_get_products_name($products_id);
	          		$products_price = $products['products_price'];
	          		$product_price_normal =  $products_price;
		          	
	          		// Merging store changes: Get the cat defined configuration settings
	    			$cat_cfg_array = tep_get_cfg_setting($products_id, 'product', 'PRE_ORDER_DISCOUNT');
	    			
	    			// Merging store changes: Get the customer group discount
		    		$customers_groups_info_array = tep_get_customer_group_discount($customer_id, $products_id, 'product');
					$customers_groups_discount = $customers_groups_info_array['cust_group_discount'];
					$customer_discount = $customer_ind_discount + $customers_groups_discount;
					
	    			// pre-order discount
	          		if (in_array($products_id, $pre_order_array) && abs($cat_cfg_array['PRE_ORDER_DISCOUNT'])) {
	      				$pre_order_discount = $cat_cfg_array['PRE_ORDER_DISCOUNT'];
	      			} else {
	      				$pre_order_discount = 0;
	      			}
	      			
	    			$products_price = $currencies->get_product_price($products_id, $customer_id, $pre_order_discount, $products['products_base_currency']);
	    			
	    			if (abs($pre_order_discount) > 0)	$prod_applied_discount[] = $pre_order_discount;
		          	if (abs($customer_discount) > 0)	$prod_applied_discount[] = $customer_discount;
		          	
		          	// Products Checkout Quantity Control
					$checkout_qty_exceed = tep_not_null($_SESSION['checkout_ctrl'][$products_id]['exceed']) ? $_SESSION['checkout_ctrl'][$products_id]['exceed'] : 0; 
					$checkout_qty_orders = tep_not_null($_SESSION['checkout_ctrl'][$products_id]['orders']) ? $_SESSION['checkout_ctrl'][$products_id]['orders'] : '';
						
	      			$custom_content_array = array();
	      			if (isset($this->contents[$products_id]['custom']['cdkey'][$i]['top_up_info'])) {
	      				$custom_content_array = $this->contents[$products_id]['custom']['cdkey'][$i]['top_up_info'];
	      			}
	      			if (isset($this->contents[$products_id]['custom']['cdkey'][$i]['delivery_mode'])) {
	      				$custom_content_array['delivery_mode'] = $this->contents[$products_id]['custom']['cdkey'][$i]['delivery_mode'];
	      			}
	      			
	      			if (!count($custom_content_array)) {
		      			$custom_content_array = (isset($this->contents[$products_id]['custom'][$custom_type]) ? $this->contents[$products_id]['custom'][$custom_type] : array());
		      		}
					
	   				if ($pm_free == 1 && (int)$products['custom_products_type_id']==0) { //if promotion
			      		if ($pm_after_id == $products_id) {
			          		$products_array[] = array(	'id' => $products_id,
			                                    		'name' => $products_name,
					                                    'model' => $products['products_model'],
					                                    'image' => $products['products_image'],
					                                    'price' => $products_price,
					                                    'base_currency' => $products['products_base_currency'],
					                                    'quantity' => $this->contents[$products_id]['qty'],
					                                    'weight' => $products['products_weight'],
					                                    'final_price' => ($products_price + $this->attributes_price($products_id)),
					                                    'pm_price' => 'FREE',
					                                    'pm_total' => $ntotal,
					                                    'tax_class_id' => $products['products_tax_class_id'],
					                                    'normal_price' => $product_price_normal,
					                                    'discounts' => $prod_applied_discount,
					                                    'products_categories_id' => $game_id,
					                                    'attributes' => (isset($this->contents[$products_id]['attributes']) ? $this->contents[$products_id]['attributes'] : ''),
					                                    'pre_order' => (in_array($products_id, $pre_order_array) ? 1 : 0),
														'custom_products_type_id' => (int)$products['custom_products_type_id'],
														'custom_content' => $custom_content_array,
														'checkout_qty_exceed' => $checkout_qty_exceed, 
														'checkout_qty_orders' => $checkout_qty_orders
													);
				    	} else {
					  		$products_array[] = array(	'id' => $products_id,
					                                    'name' => $products_name,
					                                    'model' => $products['products_model'],
					                                    'image' => $products['products_image'],
					                                    'price' => $products_price,
					                                    'base_currency' => $products['products_base_currency'],
					                                    'quantity' => $this->contents[$products_id]['qty'],
					                                    'weight' => $products['products_weight'],
					                                    'final_price' => $products_price,
					                                    'pm_price' => '',
					                                    'pm_total' => $ntotal,
					                                    'tax_class_id' => $products['products_tax_class_id'],
					                                    'normal_price' => $product_price_normal,
					                                    'discounts' => $prod_applied_discount,
					                                    'products_categories_id' => $game_id,
					                                    'attributes' => (isset($this->contents[$products_id]['attributes']) ? $this->contents[$products_id]['attributes'] : ''),
					                                    'pre_order' => (in_array($products_id, $pre_order_array) ? 1 : 0),
														'custom_products_type_id' => (int)$products['custom_products_type_id'],
														'custom_content' => $custom_content_array,
														'checkout_qty_exceed' => $checkout_qty_exceed, 
														'checkout_qty_orders' => $checkout_qty_orders
													);
						}
			  		} else {
			  			//	This is TOTALLY NOT Promotion
						$final_price = 0;
						if ((int)$products['custom_products_type_id'] == 0) {
							$final_price = $products_price + $this->attributes_price($products_id);
							
							if ($custom_type=='cdkey') {
				      			$final_qty = $this->contents[$products_id]['custom']['cdkey'][$i]['qty'];
				      		} else {
			      				$final_qty = $this->contents[$products_id]['qty'];
			      			}
							
							$custom_type = $this->get_custom_prd_type($products_id);
							$session_array = $this->contents[$products_id]['custom'][$custom_type];
							
							if ($products['products_bundle'] == 'yes') {
								$products['custom_products_type_id'] = tep_get_custom_product_type($products_id);
							}
				      		
							$products_array[] = array(	'id' => $products_id,
					                                    'name' => $products_name,
					                                    'model' => $products['products_model'],
					                                    'image' => $products['products_image'],
					                                    'price' => $products_price,
					                                    'quantity' => $final_qty,
					                                    'weight' => $products['products_weight'],
					                                    'final_price' => $final_price,
					                                    'tax_class_id' => $products['products_tax_class_id'],
					                                    'normal_price' => $product_price_normal,
					                                    'products_categories_id' => $game_id,
					                                    'pm_price' => '',
					                                    'pm_total' => $ntotal,
					                                    'discounts' => $prod_applied_discount,
					                                    'attributes' => (isset($this->contents[$products_id]['attributes']) ? $this->contents[$products_id]['attributes'] : ''),
					                                    'pre_order' => (in_array($products_id, $pre_order_array) ? 1 : 0),
														'custom_products_type_id' => (int)$products['custom_products_type_id'],
														'custom_content' => $custom_content_array,
														'products_bundle' => $products['products_bundle'],
														'checkout_qty_exceed' => $checkout_qty_exceed,
														'checkout_qty_orders' => $checkout_qty_orders
													);
						} else if ((int)$products['custom_products_type_id'] == 2) {
							$final_price = $products_price + $this->attributes_price($products_id);
							
				      		if ($custom_type=='cdkey') {
				      			$final_qty = $this->contents[$products_id]['custom']['cdkey'][$i]['qty'];
				      		} else {
			      				$final_qty = $this->contents[$products_id]['qty'];
			      			}
			      			
							$products_array[] = array(	'id' => $products_id,
					                                    'name' => $products_name,
					                                    'model' => $products['products_model'],
					                                    'image' => $products['products_image'],
					                                    'price' => $products_price,
					                                    'base_currency' => $products['products_base_currency'],
					                                    'quantity' => $final_qty,
					                                    'weight' => $products['products_weight'],
					                                    'final_price' => $final_price,
					                                    'tax_class_id' => $products['products_tax_class_id'],
					                                    'normal_price' => $product_price_normal,
					                                    'products_categories_id' => $game_id,
					                                    'pm_price' => '',
					                                    'pm_total' => $ntotal,
					                                    'discounts' => $prod_applied_discount,
					                                    'attributes' => (isset($this->contents[$products_id]['attributes']) ? $this->contents[$products_id]['attributes'] : ''),
					                                    'pre_order' => (in_array($products_id, $pre_order_array) ? 1 : 0),
														'custom_products_type_id' => (int)$products['custom_products_type_id'],
														'custom_content' => $custom_content_array,
														'checkout_qty_exceed' => $checkout_qty_exceed,
														'checkout_qty_orders' => $checkout_qty_orders
													);
						} else if ((int)$products['custom_products_type_id'] == 1) {
							//Power Leveling
							$final_qty = 1;
							
							if ($customer_discount >= 0) {
								$custom_products_price = (double)($contents_array[$i]['content']['calculated']['price'] + $contents_array[$i]['content']['calculated']['price'] * abs($customer_discount) / 100);
							} else {
								$custom_products_price = (double)($contents_array[$i]['content']['calculated']['price'] - $contents_array[$i]['content']['calculated']['price'] * abs($customer_discount) / 100);
							}
							
							$products_array[] = array(	'id' => $products_id,
														'name' => $products_name,
														'model' => $products['products_model'],
														'image' => $products['products_image'],
														'price' => $custom_products_price,
														'base_currency' => $products['products_base_currency'],
														'quantity' => $final_qty,
														'weight' => $products['products_weight'],
														'final_price' => $custom_products_price,
														'tax_class_id' => $products['products_tax_class_id'],
														'normal_price' => (double)$contents_array[$i]['content']['calculated']['price'],
														'products_categories_id' => $game_id,
														'pm_price' => '',
														'pm_total' => $ntotal,
														'discounts' => $prod_applied_discount,
														'attributes' => (isset($this->contents[$products_id]['attributes']) ? $this->contents[$products_id]['attributes'] : ''),
														'pre_order' => (in_array($products_id, $pre_order_array) ? 1 : 0),
														'custom_products_type_id' => (int)$products['custom_products_type_id'],
														'custom_content' => $contents_array[$i]['content'],
														'checkout_qty_exceed' => $checkout_qty_exceed,
														'checkout_qty_orders' => $checkout_qty_orders
													);
						} else if ((int)$products['custom_products_type_id'] == 3) {
							$final_price = $products_price;
							$final_qty = (int)$this->contents[$products_id]['qty'];
			  				
							$products_array[] = array(	'id' => $products_id,
					                                    'name' => $products_name,
					                                    'model' => $products['products_model'],
					                                    'image' => $products['products_image'],
					                                    'price' => $products_price,
					                                    'base_currency' => $products['products_base_currency'],
					                                    'quantity' => $final_qty,
					                                    'weight' => $products['products_weight'],
					                                    'final_price' => $final_price,
					                                    'tax_class_id' => $products['products_tax_class_id'],
					                                    'normal_price' => $product_price_normal,
					                                    'products_categories_id' => $game_id,
					                                    'pm_price' => '',
					                                    'pm_total' => $ntotal,
					                                    'discounts' => $prod_applied_discount,
					                                    'attributes' => (isset($this->contents[$products_id]['attributes']) ? $this->contents[$products_id]['attributes'] : ''),
					                                    'pre_order' => (in_array($products_id, $pre_order_array) ? 1 : 0),
														'custom_products_type_id' => (int)$products['custom_products_type_id'],
														'custom_content' => array(),
														'checkout_qty_exceed' => $checkout_qty_exceed,
														'checkout_qty_orders' => $checkout_qty_orders
													);
							$final_price = $products_price + $this->attributes_price($products_id);
							$final_qty = (int)$this->contents[$products_id]['qty'];
			  				
							$products_array[] = array(	'id' => $products_id,
					                                    'name' => $products_name,
					                                    'model' => $products['products_model'],
					                                    'image' => $products['products_image'],
					                                    'price' => $products_price,
					                                    'base_currency' => $products['products_base_currency'],
					                                    'quantity' => $final_qty,
					                                    'weight' => $products['products_weight'],
					                                    'final_price' => $final_price,
					                                    'tax_class_id' => $products['products_tax_class_id'],
					                                    'normal_price' => $product_price_normal,
					                                    'products_categories_id' => $game_id,
					                                    'pm_price' => '',
					                                    'pm_total' => $ntotal,
					                                    'discounts' => $prod_applied_discount,
					                                    'attributes' => (isset($this->contents[$products_id]['attributes']) ? $this->contents[$products_id]['attributes'] : ''),
					                                    'pre_order' => (in_array($products_id, $pre_order_array) ? 1 : 0),
														'custom_products_type_id' => (int)$products['custom_products_type_id'],
														'custom_content' => array(),
														'checkout_qty_exceed' => $checkout_qty_exceed,
														'checkout_qty_orders' => $checkout_qty_orders
													);
						} else if ((int)$products['custom_products_type_id'] == 4) {
							// HLA
							$final_price = $products_price + $this->attributes_price($products_id);
							
							$products_array[] = array(	'id' => $products_id,
														'name' => $products_name,
														'model' => $products['products_model'],
														'image' => $products['products_image'],
														'price' => $products_price,
														'base_currency' => $products['products_base_currency'],
														'quantity' => $contents_array[$i]['qty'],
														'weight' => $products['products_weight'],
														'final_price' => $final_price,
														'tax_class_id' => $products['products_tax_class_id'],
														'normal_price' => $product_price_normal,
														'products_categories_id' => $game_id,
														'pm_price' => '',
														'pm_total' => $ntotal,
														'discounts' => $prod_applied_discount,
														'attributes' => (isset($this->contents[$products_id]['attributes']) ? $this->contents[$products_id]['attributes'] : ''),
														'pre_order' => (in_array($products_id, $pre_order_array) ? 1 : 0),
														'custom_products_type_id' => (int)$products['custom_products_type_id'],
														'custom_content' => $contents_array[$i],
														'checkout_qty_exceed' => $checkout_qty_exceed,
														'checkout_qty_orders' => $checkout_qty_orders
													);
						}
			  		}
				}
	        	$cnt++;
	        }
      	}
      	
      	unset($_SESSION['checkout_ctrl']);  // Products Checkout Quantity Control
      	
      	return $products_array;
	}
	
    function show_total() {
    	$this->calculate();
      	return $this->total;
	}
	
	function get_shiptotal() {
    	$this->calculate();
    	return $this->shiptotal;
	}
	
    function show_weight() {
    	$this->calculate();
      	return $this->weight;
    }
	
    function generate_cart_id($length = 5) {
    	return tep_create_random_value($length, 'digits');
    }
	
    function get_content_type() {
    	$this->content_type = false;
      	
      	if ((DOWNLOAD_ENABLED == 'true') && ($this->count_contents() > 0)) {
        	reset($this->contents);
        	while (list($products_id, ) = each($this->contents)) {
          		if (isset($this->contents[$products_id]['attributes'])) {
            		reset($this->contents[$products_id]['attributes']);
            		while (list(, $value) = each($this->contents[$products_id]['attributes'])) {
              			$virtual_check_query = tep_db_query("select count(*) as total from " . TABLE_PRODUCTS_ATTRIBUTES . " pa, " . TABLE_PRODUCTS_ATTRIBUTES_DOWNLOAD . " pad where pa.products_id = '" . (int)$products_id . "' and pa.options_values_id = '" . (int)$value . "' and pa.products_attributes_id = pad.products_attributes_id");
              			$virtual_check = tep_db_fetch_array($virtual_check_query);
						
              			if ($virtual_check['total'] > 0) {
                			switch ($this->content_type) {
                  				case 'physical':
                    				$this->content_type = 'mixed';
                    				return $this->content_type;
                    				break;
                  				default:
                    				$this->content_type = 'virtual';
                    				break;
                			}
              			} else {
                			switch ($this->content_type) {
                  				case 'virtual':
                    				$this->content_type = 'mixed';
	                    			return $this->content_type;
                    				break;
                  				default:
                    				$this->content_type = 'physical';
                    				break;
                			}
              			}
            		}
					// ICW ADDED CREDIT CLASS - Begin
          		} elseif ($this->show_weight() == 0) {
            		reset($this->contents);
            		while (list($products_id, ) = each($this->contents)) {
              			$virtual_check_query = tep_db_query("select products_weight from " . TABLE_PRODUCTS . " where products_id = '" . $products_id . "'");
              			$virtual_check = tep_db_fetch_array($virtual_check_query);
              			if ($virtual_check['products_weight'] == 0) {
                			switch ($this->content_type) {
                  				case 'physical':
                    				$this->content_type = 'mixed';
                    				return $this->content_type;
                    				break;
                  				default:
                    				$this->content_type = 'virtual_weight';
                    				break;
                			}
              			} else {
                			switch ($this->content_type) {
                  				case 'virtual':
                    				$this->content_type = 'mixed';
                    				return $this->content_type;
                    				break;
                  				default:
                    				$this->content_type = 'physical';
                    				break;
                			}
              			}
            		}
					// ICW ADDED CREDIT CLASS - End
          		} else {
            		switch ($this->content_type) {
              			case 'virtual':
                			$this->content_type = 'mixed';
                			return $this->content_type;
                			break;
              			default:
                			$this->content_type = 'physical';
                			break;
            		}
          		}
        	}
      	} else {
        	$this->content_type = 'physical';
      	}
		
      	return $this->content_type;
	}
	
    function unserialize($broken) {
    	for (reset($broken); $kv=each($broken); ) {
        	$key = $kv['key'];
        	if (gettype($this->$key)!="user function")
        		$this->$key=$kv['value'];
      	}
	}
	
	function set_custom_product_content($cont) {
		$this->custom_product_content = $cont;
	}
	
	function _serialize($arr) {
		return urlencode(serialize($arr));
	}
	
	function _unserialize($val) {
		return unserialize(urldecode(stripslashes($val)));
	}	
	
	function get_date_time_string($hours) {
		$day = (int)($hours / 24);
		$hours = $hours % 24;
		
		return ($day >= 1 ? $day . " Day". ($day > 1 ? 's' : '') : '') . ($hours > 0 || (abs($day) < 1 && $hours >= 0) ? ' '.$hours . " Hour" . ($hours > 1 ? 's' : '') : '');
	}
	
	// ------------------------ ICWILSON CREDIT CLASS Gift Voucher Addittion-------------------------------Start
	// amend count_contents to show nil contents for shipping
	// as we don't want to quote for 'virtual' item
	// GLOBAL CONSTANTS if NO_COUNT_ZERO_WEIGHT is true then we don't count any product with a weight
	// which is less than or equal to MINIMUM_WEIGHT
	// otherwise we just don't count gift certificates
	
    function count_contents_virtual() {	// get total number of items in cart disregard gift vouchers
      	$total_items = 0;
      	
      	if (is_array($this->contents)) {
        	reset($this->contents);
        	while (list($products_id, ) = each($this->contents)) {
          		$no_count = false;
          		$gv_query = tep_db_query("select products_model from " . TABLE_PRODUCTS . " where products_id = '" . $products_id . "'");
          		$gv_result = tep_db_fetch_array($gv_query);
          		if (ereg('^GIFT', $gv_result['products_model'])) {
            		$no_count=true;
          		}
          		
          		if (NO_COUNT_ZERO_WEIGHT == 1) {
            		$gv_query = tep_db_query("select products_weight from " . TABLE_PRODUCTS . " where products_id = '" . tep_get_prid($products_id) . "'");
            		$gv_result=tep_db_fetch_array($gv_query);
            		if ($gv_result['products_weight']<=MINIMUM_WEIGHT) {
              			$no_count=true;
            		}
          		}
          		
          		if (!$no_count) $total_items += $this->get_quantity($products_id);
        	}
      	}
      	return $total_items;
	}
	
	function check_bundle($product_id) {
		global $languages_id;
		
		$check_bundle_select_sql = "	SELECT pb.bundle_id 
										FROM " . TABLE_PRODUCTS_BUNDLES . " AS pb
										LEFT JOIN " . TABLE_PRODUCTS_DESCRIPTION . " AS pd
											ON pb.bundle_id = pd.products_id 
										WHERE pd.language_id = '" . (int)$languages_id . "' 
											AND pb.bundle_id = '" . (int)$product_id . "' 
										LIMIT 1";
		$check_bundle_result_sql = tep_db_query($check_bundle_select_sql);
					
		if (tep_db_num_rows($check_bundle_result_sql) > 0) {
			return true;
		} else {
			return false;
		}
	}
	// ------------------------ ICWILSON CREDIT CLASS Gift Voucher Addittion-------------------------------End
	
	function check_update_cart($products_id, $qty, $product_instance_id='') {
		$result = true;
		$prod_quantity_array = array();
		
		$prod_bd_select_sql = "	SELECT products_bundle, products_bundle_dynamic, products_bundle_dynamic_qty 
								FROM " . TABLE_PRODUCTS . " 
								WHERE products_id = '" . tep_db_input($products_id) . "'";
		$prod_bd_query = tep_db_query($prod_bd_select_sql);
	  	$prod_bd = tep_db_fetch_array($prod_bd_query);
  	  	$prod_bundle = $prod_bd['products_bundle'];
  	  	$prod_bundle_dynamic = $prod_bd['products_bundle_dynamic'];
		
		if ($prod_bundle == 'yes') {
			$bundle_select_sql = "	SELECT pb.subproduct_id, pb.subproduct_qty, p.products_status, p.products_purchase_mode 
									FROM " . TABLE_PRODUCTS_BUNDLES . " AS pb 
									LEFT JOIN " . TABLE_PRODUCTS . " AS p 
										ON pb.subproduct_id = p.products_id  
									WHERE bundle_id='" . tep_db_input($products_id) . "'" ;
			$bundle_result_sql = tep_db_query($bundle_select_sql);
			while ($bundle_data = tep_db_fetch_array($bundle_result_sql)) {
				$prod_quantity_array[$bundle_data["subproduct_id"]] += $qty * $bundle_data["subproduct_qty"];
				$stock_status = tep_check_stock_status($bundle_data["subproduct_id"], $prod_quantity_array[$bundle_data["subproduct_id"]]);
				
				if (tep_not_null($stock_status)) {
          			if ($stock_status == 'pre-order') {
          				;
          			} else {
          				$result = false;
          			}
          		}
			}
		} else if ($prod_bundle_dynamic == 'yes') {
			;
		} else {
			$prod_status_select_sql = "	SELECT products_status, products_purchase_mode 
										FROM " . TABLE_PRODUCTS . " 
										WHERE products_id = '" . tep_db_input($products_id) . "'";
            $prod_status_result_sql = tep_db_query($prod_status_select_sql);
            if ($prod_status_row = tep_db_fetch_array($prod_status_result_sql)) {
        		$stock_status = tep_check_stock_status($products_id, $qty, $product_instance_id);
        		
        		if (tep_not_null($stock_status)) {
        			if ($stock_status == 'pre-order') {
        				;
        			} else {
        				$result = false;
        			}
        		}
            }
		}
		
		return $result;
	}
	
	function get_cart($checkout_button = '') {
		global $customer_id, $languages_id, $currencies, $order, $order_total_modules, $payment_modules, $payment_modules_info, $order_total_reprocess;
		global $payment;
		
		$products = $this->get_products();
		$products_amt = sizeof($products);
		$custom_prod_index = array();
		$item_list_html = '';
		$default_pd_strlen_show = 31;
		$total_op = 0;
		
		$pd_strlen_show_array = array();
		$pd_strlen_show_array['ASCII'] = 50;
		$pd_strlen_show_array['UTF-8'] = 31;
		
		$current_select_pm_id = 0;
		if (tep_not_null($payment) && preg_match('/^(pm_)(\d+)(:~:)(\S+)(:~:)(\d+)$/', $payment, $selected_payment) && count($selected_payment) == 7) {
			$current_select_pm_id = (int)$selected_payment[6];
		}
		
		$cart_html = '		<div>
								<div class="h1_tbl" style="width:441px;height:15px;border-bottom:1px solid #CCCCCC">
									<div style="width:280px;float:left;">
										<font style="color:#cccccc;"><span class="hdsU4">' . TABLE_HEADING_PRODUCT . '</span></font>
									</div>
									<div class="" style="width:70px;float:left;text-align:center;">
										<font style="color:#cccccc;"><span class="hdsU4">' . TABLE_HEADING_QUANTITY . '</span></font>
									</div>
									<div class="" style="width:82px;float:left;text-align:center;">
										<font style="color:#cccccc;"><span class="hdsU4">' . TABLE_HEADING_AMOUNT . '</span></font>
									</div>
								</div>';
		
		$cart_html .=			tep_draw_form('checkout_payment', tep_href_link(FILENAME_CHECKOUT_CONFIRMATION, '', 'SSL'), 'post', 'onsubmit="return payment_check_form();"') .
						'		<table border="0" width="100%" cellspacing="0" cellpadding="0">';
		
		$extra_op_array = array();
		for ($i = 0; $i < $products_amt; $i++) {
	    	if ((int)$products[$i]['custom_products_type_id'] > 0) {
	      		$custom_prod_index[$products[$i]['id']] = (int)$custom_prod_index[$products[$i]['id']] + 1;
	      		$unique_pid = $products[$i]['id'].'_'.$custom_prod_index[$products[$i]['id']];
	      	} else {
	      		$unique_pid = $products[$i]['id'];
	      	}
	      	
	      	$prod_bd_query = tep_db_query("select products_bundle, products_bundle_dynamic, products_bundle_dynamic_qty from " . TABLE_PRODUCTS . " where products_id = " . $products[$i]['id'] );
		  	$prod_bd = tep_db_fetch_array($prod_bd_query);
	  	  	$prod_bundle = $prod_bd['products_bundle'];
	  	  	$prod_bundle_dynamic = $prod_bd['products_bundle_dynamic'];
	  	  	
	  	  	if ($products[$i]['pm_price'] == 'FREE') {
	  	  		$display_free = false;
	  	  	}
	  	  	
	    	if ((int)$products[$i]['custom_products_type_id'] < 1) {
				if (isset($this->contents[$products[$i]['id']]['custom']['game_currency'])) {
					$custom_name = 'game_currency';
				} else if (isset($this->contents[$products[$i]['id']]['custom']['cdkey'])) {
					$custom_name = 'cdkey';
				} else if (isset($this->contents[$products[$i]['id']]['custom']['store_credit'])) {
					$custom_name = 'store_credit';
				} else if (isset($this->contents[$products[$i]['id']]['custom']['hla'])) {
					$custom_name = 'hla';
				}
				
		    	foreach($this->contents[$products[$i]['id']]['custom'][$custom_name] as $count => $extra_info_array) {
					$product_instance_id = tep_not_null($extra_info_array['hla_account_id']) ? $extra_info_array['hla_account_id'] : '';
			    	$currencies->product_instance_id = $product_instance_id;
			    	
		    		if (in_array($custom_name, array('cdkey', 'store_credit'))) {
		    			$product_qty = $this->contents[$products[$i]['id']]['qty'];
		    		} else {
		    			$product_qty = $extra_info_array['qty'];
		    		}
		    		
		    		$products_name = strip_tags($products[$i]['name']);
		    		$display_price = $currencies->display_price($products[$i]['id'], $products[$i]['normal_price'], tep_get_tax_rate($products[$i]['tax_class_id']), $product_qty, true, '', $current_select_pm_id);
		    		
		    		$total_op += $currencies->rebate_point;
		    		$extra_op_array[$unique_pid] += $currencies->rebate_point_extra;
		    		
					if ($custom_name == 'store_credit') {
						$products_name .= ' ('. tep_get_customer_store_credit_currency($customer_id, false) . ')';
					}
		    		
		    		if ($products[$i]['pm_price'] == 'FREE') {
		    			if ($display_free) {
			    			$products_name .= '* Get 1 free';
			    		}
			  		}
			  		
			  		$pd_encoding = mb_detect_encoding($products_name);
					$word_len_show = ((isset($pd_strlen_show_array[$pd_encoding])) ? $pd_strlen_show_array[$pd_encoding] : $default_pd_strlen_show);
					
					if (mb_strlen($products_name, $pd_encoding) > $word_len_show) {
						$products_name = trim(mb_substr($products_name, 0, $word_len_show, $pd_encoding)) . '...';
					}
			  		
			  		$item_list_html .= '	<tr class="divTableRowTight">
			  									<td class="divTableCell" valign="top" style="padding:5px 0px 0px 15px;width:290px">' . $products_name . '</td>
			  									<td class="divTableCell" valign="top" style="text-align:center;padding-top:5px;width:46px;">' . (($prod_bundle_dynamic == 'yes') ? $products[$i]['quantity'] : $product_qty) . '</td>
			  									<td class="paymentPriceValue" valign="top" style="text-align:right;padding-top:5px;width:85px;">';
			  		
			  		//CGDiscountSpecials start
					if ($display_cust_price_col) {
			      		if ($products[$i]['pm_price'] == 'FREE' && !$count) {
		      				$prod_qty = ($product_qty > 1) ? $product_qty - 1 : $product_qty;
			      	    	$item_list_html .= $currencies->display_price_nodiscount($products[$i]['id'], $products[$i]['final_price'], tep_get_tax_rate($products[$i]['tax_class_id']), $product_qty, $products[$i]['discounts']);
						} else {
							$item_list_html .= $currencies->display_price_nodiscount($products[$i]['id'], $products[$i]['final_price'], tep_get_tax_rate($products[$i]['tax_class_id']), $product_qty, $products[$i]['discounts']);
			      		}
			    	} else {
			    		if ($products[$i]['pm_price'] == 'FREE' && !$count) {
			            	if ($display_free) {
			            		$final_qty = $product_qty - 1;
			            	} else {
			            		$final_qty = $product_qty;
			            	}
			            	
			            	$item_list_html .= $currencies->display_price_nodiscount($products[$i]['id'], $products[$i]['final_price'], tep_get_tax_rate($products[$i]['tax_class_id']), $final_qty, $products[$i]['discounts']);
			            } else {
			            	$item_list_html .= $currencies->display_price_nodiscount($products[$i]['id'], $products[$i]['final_price'], tep_get_tax_rate($products[$i]['tax_class_id']), $product_qty, $products[$i]['discounts']);
			            }
					}
			  		
			  		$item_list_html .= '	</td>
			  							</tr>';
			 	}
			} else {
				$product_instance_id = tep_not_null($products[$i]['custom_content']['hla_account_id']) ? $products[$i]['custom_content']['hla_account_id'] : '';
		    	$currencies->product_instance_id = $product_instance_id;
		    	
				$display_price = $currencies->display_price($products[$i]['id'], $products[$i]['normal_price'], tep_get_tax_rate($products[$i]['tax_class_id']), $products[$i]['quantity'], true, '', $current_select_pm_id);
				$total_op += $currencies->rebate_point;
				$extra_op_array[$unique_pid] += $currencies->rebate_point_extra;
				
				$products_name = strip_tags($products[$i]['name']);
				
				if ($products[$i]['custom_products_type_id'] == '3') {
					$products_name .= ' ('. tep_get_customer_store_credit_currency($customer_id, false) . ')';
				}
				
				if ($products[$i]['pm_price'] == 'FREE') {
					$products_name .= '* Get 1 free';
				}
				
				$pd_encoding = mb_detect_encoding($products_name);
				$word_len_show = ((isset($pd_strlen_show_array[$pd_encoding])) ? $pd_strlen_show_array[$pd_encoding] : $default_pd_strlen_show);
				
				if (mb_strlen($products_name, $pd_encoding) > $word_len_show) {
					$products_name = trim(mb_substr($products_name, 0, $word_len_show, $pd_encoding)) . '...';
				}
				
				$item_list_html .= '	<tr class="divTableRowTight">
		  									<td class="divTableCell" valign="top" style="padding:5px 0px 0px 15px;width:290px;">' . $products_name . '</td>
		  									<td class="divTableCell" valign="top" style="text-align:center;padding-top:5px;width:46px;">' . $products[$i]['quantity'] . '</td>
		  									<td class="paymentPriceValue" valign="top" style="text-align:right;padding-top:5px;width:85px;">';
			  	
			  	//CGDiscountSpecials start
				if ($display_cust_price_col) {
		      		if ($products[$i]['pm_price'] == 'FREE') {
		      			if ($products[$i]['quantity'] > 1) {
		      				$prod_qty = $products[$i]['quantity'] - 1;	
		      			}
		      	    	$item_list_html .= $currencies->display_price_nodiscount($products[$i]['id'], $products[$i]['final_price'], tep_get_tax_rate($products[$i]['tax_class_id']), $prod_qty, $products[$i]['discounts']);
					} else {
						$item_list_html .= $currencies->display_price_nodiscount($products[$i]['id'], $products[$i]['final_price'], tep_get_tax_rate($products[$i]['tax_class_id']), $products[$i]['quantity'], $products[$i]['discounts']);
		      		}
		    	} else {
		    		if ($products[$i]['pm_price'] == 'FREE') {
						if ($products[$i]['quantity'] > 1) {
		      				$prod_qty = $products[$i]['quantity'] - 1;	
			      		}
			      		$item_list_html .= $currencies->display_price_nodiscount($products[$i]['id'], $products[$i]['final_price'], tep_get_tax_rate($products[$i]['tax_class_id']), $prod_qty, $products[$i]['discounts']);
					} else {
		        		$item_list_html .= $currencies->display_price_nodiscount($products[$i]['id'], $products[$i]['final_price'], tep_get_tax_rate($products[$i]['tax_class_id']), $products[$i]['quantity'], $products[$i]['discounts']);
					}
				}
			  	
			  	$item_list_html .= '		</td>
			  							</tr>';
			}
			
			if ($i != sizeof($products) - 1) {
				$item_list_html .= '	<tr>
											<td colspan="3">' . tep_draw_separator('pixel_trans.gif', '100%', '5') . '</td>
										</tr>';
			} else {
				$item_list_html .= '	<tr>
											<td colspan="3">' . tep_draw_separator('pixel_trans.gif', '100%', '5') . '</td>
										</tr>';
			}
		}
		
		$pd_box_height = $products_amt * 26;
		
		if ($pd_box_height > 80) {
			$pd_box_height = 80;
		}
		
		$cart_html .= '					<tr>
											<td colspan="3">
												<div style="overflow-y:auto;overflow-x:hidden;height:'.$pd_box_height.'px;">
													<table border="0" width="437px" cellspacing="0" cellpadding="0">' .
														$item_list_html .
													'</table>
												</div>
											</td>
										</tr>';
		
		$custom_prod_index = array();
		if (MODULE_ORDER_TOTAL_INSTALLED) {
			if (!isset($order_total_reprocess) || $order_total_reprocess==true) $order_total_modules->process();
			$cust_group_extra_rebate = 0;
			$cust_group_extra_op = 0;
			if (tep_not_null($payment) && preg_match('/^(pm_)(\d+)(:~:)(\S+)(:~:)(\d+)$/', $payment, $selected_payment) && count($selected_payment) == 7) {
				for ($count_products = 0; $count_products < $products_amt; $count_products++) {
			    	if ((int)$products[$count_products]['custom_products_type_id'] > 0) {
			      		$custom_prod_index[$products[$count_products]['id']] = (int)$custom_prod_index[$products[$count_products]['id']] + 1;
			      		$unique_pid = $products[$count_products]['id'].'_'.$custom_prod_index[$products[$count_products]['id']];
			      	} else {
			      		$unique_pid = $products[$count_products]['id'];
			      	}
					
			    	if (isset($extra_op_array[$unique_pid]) && $extra_op_array[$unique_pid] > 0) {
						$surcharge_amt = 0;
						if (isset($GLOBALS['ot_surcharge']) && $GLOBALS['ot_surcharge']->enabled) {
		            		for ($ot_surcharge_count=0;$ot_surcharge_count < sizeof($GLOBALS['ot_surcharge']->output);$ot_surcharge_count++) {
		              			if (tep_not_null($GLOBALS['ot_surcharge']->output[$ot_surcharge_count]['title']) && tep_not_null($GLOBALS['ot_surcharge']->output[$ot_surcharge_count]['text'])) {
		                			$surcharge_amt = $GLOBALS['ot_surcharge']->output[$ot_surcharge_count]['value'];
		              			}
		            		}
						}
						
						$gst_amt = 0;
						if (isset($GLOBALS['ot_gst']) && $GLOBALS['ot_gst']->enabled) {
		            		for ($gst_cnt=0; $gst_cnt < sizeof($GLOBALS['ot_gst']->output); $gst_cnt++) {
		              			if (tep_not_null($GLOBALS['ot_gst']->output[$gst_cnt]['title']) && tep_not_null($GLOBALS['ot_gst']->output[$gst_cnt]['text'])) {
		                			$gst_amt = $GLOBALS['ot_gst']->output[$gst_cnt]['value'];
		              			}
		            		}
						}
						
						$cust_group_extra_rebate = floor($extra_op_array[$unique_pid] * ( ($order->info['total'] - $surcharge_amt - $gst_amt)/ $order->info['subtotal']));
						$cust_group_extra_op = $cust_group_extra_op + $cust_group_extra_rebate;
					}
				}
			}
			
			$cart_html .= $order_total_modules->output(array('selection' => true), $total_op, $cust_group_extra_op);
		}
		
		$cart_html .= '				</table>
								</form>
								<table width="450px"><tr><td width="246px"></td><td align="right" width="*%">
								<div id="checkout_button_div">';
		
		if ($payment_modules->check_credit_covers() || $order->info['subtotal'] <= 0) {
			if (!tep_session_is_registered('payment')) tep_session_register('payment');
			
			$cart_html .= 			tep_draw_form('checkout_confirmation', tep_href_link(FILENAME_CHECKOUT_PROCESS, '', 'SSL'), 'post') .
										'<table border="0" cellspacing="0" cellpadding="0" align="center">
											<tr>
												<td> ' .
										tep_image_button2('red', 'javascript:document.checkout_confirmation.submit()', IMAGE_BUTTON_SECURE_CHECKOUT, 200) . 
										'		</td>
											</tr>
										</table>
									</form>';
		} else if (tep_not_null($checkout_button)) {
			$cart_html .= $checkout_button;
		} else {
			$cart_html .=			'<table border="0" cellspacing="0" cellpadding="0" width="100%">
										<tr>
											<td>' . 
												tep_image_button2('gray', 'javascript:this.void(0)', '<font style="color:#BBBBBB;font-size:11px;">'.IMAGE_BUTTON_SECURE_CHECKOUT.'</font>', 200) . 
									'		</td>
										</tr>
										<tr>
											<td> ' . tep_draw_separator("pixel_trans.gif", "100%", "3") . '</td>
										</tr>
									</table>';
		}
		
		$cart_html .= '			</div></td></tr></table>
							</div>';
		
		return $cart_html;
	}
    
    function mm_checkout_permission($products) {
		if (!count($products)) return false;
		$checked_products = array();
		
		for ($i=0; $i < count($products); $i++) {
			// Products Checkout Quantity Control
			if (tep_not_null($products[$i]['checkout_qty_exceed']) && ($products[$i]['checkout_qty_exceed'] == 1)) {
				return false;
			}
			
			$prod_bd_query = tep_db_query("SELECT products_bundle, products_bundle_dynamic FROM " . TABLE_PRODUCTS . " WHERE products_id = " . $products[$i]['id'] . " ORDER BY products_id");
	  		$prod_bd = tep_db_fetch_array($prod_bd_query);
  	  		$prod_bundle = $prod_bd['products_bundle'];
  	  		$prod_bundle_dynamic = $prod_bd['products_bundle_dynamic'];
  	  		
  	  		if (STOCK_CHECK == 'true') {
	      		if ($prod_bundle=="yes") {
	      			$bundle_select_sql = "	SELECT p.products_id, p.products_quantity, p.products_status 
											FROM products AS p 
											INNER JOIN products_bundles AS pb
										  		ON pb.subproduct_id=p.products_id 
											WHERE pb.bundle_id ='" . $products[$i]['id'] . "'"  ;
					$bundle_result_sql = tep_db_query($bundle_select_sql);
					while ($bundle_data = tep_db_fetch_array($bundle_result_sql)) {
						if (!$bundle_data["products_status"]) {  // not success if one of the subproduct is inactive
							return false;
							break;
						}
						
			      		if (isset($products[$i]['custom_content']['delivery_mode']) && $products[$i]['custom_content']['delivery_mode'] == '6') {
			      			//
			      		} else {
							$skip_stock_checking = tep_single_product_skip_stock_check($bundle_data["products_id"]);
							
							if ($skip_stock_checking['state'] == -1) {
								return false;
								break;
							} else if ($skip_stock_checking['state'] == 0) {
								$current_qty_in_cart = $this->get_mm_total_product_in_cart($bundle_data["products_id"]);
								if ($bundle_data['products_quantity'] - $current_qty_in_cart < $skip_stock_checking['stock_level']) {
									return false;
									break;
								} else {
									$checked_products[] = $bundle_data["products_id"];
								}
							}
						}
					}
	      		} else if ($prod_bundle_dynamic == 'yes') {
	      			// syn the badket bundle table then check for add more than allowed weight
//	      			if (!$this->syn_basket_bundle($customer_id, $products[$i]['id']))	return false;
//	      			
//	      			$bundle_select_sql = "	SELECT cb.subproducts_quantity, pb.subproduct_id, pb.subproduct_qty, p.products_status, p.products_quantity 
//	    									FROM " . TABLE_CUSTOMERS_BASKET_BUNDLE . " AS cb
//	    									LEFT JOIN " . TABLE_PRODUCTS_BUNDLES . " AS pb
//	    										ON cb.products_bundle_id = pb.bundle_id 
//	    									LEFT JOIN " . TABLE_PRODUCTS . " AS p
//    											ON pb.subproduct_id = p.products_id 
//	    									WHERE cb.customers_id = '" . (int)$customer_id . "' AND cb.subproducts_id = pb.subproduct_id AND cb.products_bundle_id = '" . $products[$i]['id'] . "'";
//					$bundle_result_sql = tep_db_query($bundle_select_sql);
//					
//					while ($bundle_data = tep_db_fetch_array($bundle_result_sql)) {
//						if (!$bundle_data["products_status"]) {
//							return false;
//							break;
//						}
//						
//						if ($bundle_data["subproducts_quantity"]) {
//							if (isset($products[$i]['custom_content']['delivery_mode']) && $products[$i]['custom_content']['delivery_mode'] == '6') {
//								//
//							} else {
//								$skip_stock_checking = tep_single_product_skip_stock_check($bundle_data["subproduct_id"]);
//								
//								if ($skip_stock_checking['state'] == -1) {
//									return false;
//									break;
//								} else if ($skip_stock_checking['state'] == 0) {
//									$current_qty_in_cart = $this->get_mm_total_product_in_cart($bundle_data["subproduct_id"]);
//									if ($bundle_data["products_quantity"] - $current_qty_in_cart < $skip_stock_checking['stock_level']) {
//				          				return false;
//										break;
//									} else {
//										$checked_products[] = $bundle_data["subproduct_id"];
//									}
//								}
//								
//								if (!$skip_stock_checking) {
//									$current_qty_in_cart = $this->get_mm_total_product_in_cart($bundle_data["subproduct_id"]);
//									if ($bundle_data["products_quantity"] < $current_qty_in_cart) {
//				          				return false;
//										break;
//									} else {
//										$checked_products[] = $bundle_data["subproduct_id"];
//									}
//								}
//							}
//		        		}
//					}
                    return false;
	      		} else {
	      			if (isset($products[$i]['custom_content']['delivery_mode']) && $products[$i]['custom_content']['delivery_mode']=='6') {
	      				//
	      			} else {
		      			$custom_type = $this->get_custom_prd_type($products[$i]['id']);
//		      			if ($custom_type == 'hla') {
//							$prod_inventory_select_sql = "	SELECT available_quantity AS products_quantity, products_status 
//															FROM " . TABLE_PRODUCTS_HLA . " 
//															WHERE products_id = '" . tep_db_input($products[$i]['id']) . "' 
//																AND products_hla_id = '" . tep_db_input($products[$i]['custom_content']['hla_account_id']) . "'";
//						} else {
		      				$prod_inventory_select_sql = "SELECT products_quantity, products_status FROM " . TABLE_PRODUCTS . " WHERE products_id = '" . $products[$i]['id'] . "'";
//		      			}
	                    $prod_inventory_result_sql = tep_db_query($prod_inventory_select_sql);
						
						if ($prod_inventory_row = tep_db_fetch_array($prod_inventory_result_sql)) {
							if (!$prod_inventory_row["products_status"]) {
								return false;
								break;
							}
							
							if (($custom_type == 'hla') && (0 >= $prod_inventory_row["products_quantity"])) {
								return false;
								break;
							} else {
								if (isset($products[$i]['custom_content']['delivery_mode']) && $products[$i]['custom_content']['delivery_mode'] == '6') {
									//
								} else {
									$skip_stock_checking = tep_single_product_skip_stock_check($products[$i]['id']);
									
									if ($skip_stock_checking['state'] == -1) {
										return false;
										break;
									} else if ($skip_stock_checking['state'] == 0) {
										$current_qty_in_cart = $this->get_mm_total_product_in_cart($products[$i]['id']);
										if ($prod_inventory_row["products_quantity"] - $current_qty_in_cart < $skip_stock_checking['stock_level']) {
				          					return false;
											break;
										} else {
											$checked_products[] = $bundle_data["subproduct_id"];
										}
									}
								}
							}
						}
					}
	        	}
	      	}
    	}
    	return true;
	}
    
    function get_mm_total_product_in_cart($products_id, $exclude_bundle_id = '') {
        $cart_prod_total = 0;
        
        if (isset($this->mm_contents[$products_id]['qty'])) {
            $customers_basket_select_sql = "SELECT p.products_id 
                                            FROM " . TABLE_PRODUCTS . " AS p
                                            WHERE p.products_id = '" . tep_db_input($products_id) . "' 
                                                AND p.products_bundle<>'yes' 
                                                AND p.products_bundle_dynamic<>'yes'";
        $customers_basket_result_sql = tep_db_query($customers_basket_select_sql);
        if ( $customers_basket_info = tep_db_fetch_array($customers_basket_result_sql) )
            $cart_prod_total += $this->mm_contents[$products_id]['qty'];
        }
        
        foreach ($this->mm_contents AS $p_id => $bundle_arr) {
            $customers_basket_select_sql = "SELECT pb.subproduct_qty 
                                            FROM " . TABLE_PRODUCTS . " AS p
                                            LEFT JOIN " . TABLE_PRODUCTS_BUNDLES . " AS pb
                                                ON p.products_id = pb.bundle_id 
                                            WHERE p.products_id = '" . tep_db_input($p_id) . "'
                                                AND pb.subproduct_id = '" . tep_db_input($products_id) . "' 
                                                AND p.products_bundle='yes' ";
            $customers_basket_result_sql = tep_db_query($customers_basket_select_sql);
            while ( $customers_basket_info = tep_db_fetch_array($customers_basket_result_sql) ) {
                $cart_prod_total += $this->mm_contents[$p_id]['qty']*$customers_basket_info["subproduct_qty"];
            }
        }
        
        foreach ($this->mm_contents AS $p_id => $bundle_arr) {
            if (isset($bundle_arr['bundle'])) {
                foreach ($bundle_arr['bundle'] AS $subproducts_id => $bundle_info) {
                    if ($products_id == $subproducts_id) {
                        $customers_basket_select_sql = "SELECT pb.bundle_id, pb.subproduct_qty
                                                        FROM " . TABLE_PRODUCTS_BUNDLES . " AS pb
                                                            ON cb.products_bundle_id = pb.bundle_id 
                                                        WHERE pb.bundle_id = '" . tep_db_input($p_id) . "' 
                                                            AND pb.subproduct_id = '" . tep_db_input($products_id) . "'";
                        $customers_basket_result_sql = tep_db_query($customers_basket_select_sql);
                        while ( $customers_basket_info = tep_db_fetch_array($customers_basket_result_sql) ) {
                            if ($customers_basket_info["bundle_id"] != $exclude_bundle_id)
                                $cart_prod_total += $bundle_info['sub_qty']*$customers_basket_info["subproduct_qty"];
                        }
                    }
                }
            }
        }
        
        return $cart_prod_total;
    }
    
    // Only support 1 product 
    // Only support CDK
    // Checking on delivery method is supported?
    // Product type is supported?
    //
    function add_mm_cart($products_id, $qty = '1', $opt = array()) {
        $attributes = isset($opt['attributes']) ? $opt['attributes'] : '';
        $notify = isset($opt['notify']) ? $opt['notify'] : true;
        $array = isset($opt['array']) ? $opt['array'] : '';
        $bdn = isset($opt['bdn']) ? $opt['bdn'] : '';
        $iscustom = isset($opt['iscustom']) ? $opt['iscustom'] : true;
        $extra_info_array = isset($opt['extra_info_array']) ? $opt['extra_info_array'] : '';
        
        $return_arr = array();
      	$custom_product_type_id = tep_get_custom_product_type($products_id);
      	
        if ($custom_product_type_id == 2) { // only do when cd key 
	      	$selected_delivery_mode = 5; // put default as 5
	      	if (isset($extra_info_array['delivery_mode']) && (int)$extra_info_array['delivery_mode'] > 0) {
	      		$selected_delivery_mode = $extra_info_array['delivery_mode'];
	    	}
	    	
	    	include_once(DIR_WS_CLASSES . 'product.php');
	      	$product_delivery_mode_array = product::get_product_delivery_mode($products_id);
	      	if (!in_array($selected_delivery_mode, $product_delivery_mode_array)) {
	      		// delivery mode not supported
                $return_arr['error'] = 'dm_not_supported';
	      		return $return_arr;
	    	}
    	}
    	
      	$products_id = tep_get_uprid($products_id, $attributes);
      	
      	$prod_cat_path = tep_get_product_path($products_id, true);
		if (tep_not_null($prod_cat_path)) {
			$prod_cat_path_array = explode('_', $prod_cat_path);
			$game_id = $prod_cat_path_array[0];
		} else {
			$game_id = 0;
		}
		
   		$custom_type = $this->get_custom_prd_type($products_id);
		
      	if ($notify == true) {
      		//	This newly added item will be highlighted in shopping cart summary box
//        	$new_products_id_in_cart = $products_id;
//        	tep_session_register('new_products_id_in_cart');
      	}
      	
		if ($custom_type != 'cdkey') {
            $return_arr['error'] = 'product_type_not_supported';
            return $return_arr;
		}
      	
		if (is_array($extra_info_array) && count($extra_info_array)) {
			foreach ($extra_info_array as $extra_info_key_loop => $extra_info_value_loop) {
				$extra_info_array[$extra_info_key_loop] = tep_db_prepare_input($extra_info_value_loop);
				if (is_array($extra_info_value_loop)) {
					foreach ($extra_info_value_loop as $extra_info_key_sub_loop => $extra_info_value_sub_loop) {
						$extra_info_array[$extra_info_key_loop][$extra_info_key_sub_loop] = tep_db_prepare_input($extra_info_value_sub_loop);
					}
				}
			}
		}
        
        $isincart = false;  //$this->in_mm_cart($products_id);
		
		if ($isincart && $iscustom == false) {
		} else {
      		if (!$iscustom) {	//Non-Custom products
				$this->mm_contents[$products_id] = array('qty' => $qty);
				if (!isset($this->mm_contents[$products_id]['custom'][$custom_type])) {
					$this->mm_contents[$products_id]['custom'][$custom_type] = array();
				}
				
				if ($custom_type == 'game_currency') {
				} else if ($custom_type == 'cdkey') {
					$extra_info_insert_array = array('qty' => $qty);
					if (isset($extra_info_array['delivery_mode'])) {
						$extra_info_insert_array['delivery_mode'] = $extra_info_array['delivery_mode'];
					} else {
						$extra_info_insert_array['delivery_mode'] = 5;
					}
					if (isset($extra_info_array['top_up_info'])) {
						$extra_info_insert_array['top_up_info'] = $extra_info_array['top_up_info'];
					} else {
						$extra_info_insert_array['top_up_info'] = array();
					}
					$this->mm_contents[$products_id]['custom'][$custom_type][] = $extra_info_insert_array;
				} else if ($custom_type == 'store_credit') {
				} else if (!$isincart && !$hla_prod_isincart && $custom_type == 'hla') {
				}
			} else {	//Custom Products
				$insert_custom_data = false;
				
				// $custom_type = $this->get_custom_prd_type($products_id);	// Commented out as function call before this
				switch ($custom_type) {
					case 'power_leveling':
						break;
					case 'cdkey':
						if ($isincart) {
						} else {
							$extra_info_insert_array = array('qty' => $qty);
							if (isset($extra_info_array['delivery_mode'])) {
								$extra_info_insert_array['delivery_mode'] = $extra_info_array['delivery_mode'];
							} else {
								$extra_info_insert_array['delivery_mode'] = 5;
							}
							
							if (isset($extra_info_array['top_up_info'])) {
								$extra_info_insert_array['top_up_info'] = $extra_info_array['top_up_info'];
							} else {
								$extra_info_insert_array['top_up_info'] = array();
							}
							$this->mm_contents[$products_id]['custom'][$custom_type][0] = $extra_info_insert_array;
							$insert_custom_data = true;
						}
						break;
					case 'store_credit':
						break;
					case 'hla':
						break;
				}
				
				if (!is_array($this->mm_contents[$products_id]['custom'][$custom_type]))
					$this->mm_contents[$products_id]['custom'][$custom_type] = array();
					
//				if ($custom_type == 'power_leveling') {
//				} else if ($custom_type == 'store_credit') {
//				} else if ($custom_type == 'hla' && !$hla_prod_isincart) {
//				} else {	
//                    //// CD Key atm
//				}
			}
			
    		$bundle_select_sql = "	SELECT pd.products_name, pb.*, p.products_bundle, p.products_bundle_dynamic,
                       					p.products_id, p.products_price
					   				FROM " . TABLE_PRODUCTS . " p 
					   				INNER JOIN " . TABLE_PRODUCTS_DESCRIPTION . " pd
					   					ON p.products_id=pd.products_id
					   				INNER JOIN " . TABLE_PRODUCTS_BUNDLES . " pb
					   					ON pb.subproduct_id=pd.products_id 
					   				WHERE pb.bundle_id = '" . (int)$products_id . "' AND language_id = '1'";
    		$bundle_result_sql = tep_db_query($bundle_select_sql);
    		
			while ($bundle_data = tep_db_fetch_array($bundle_result_sql)) {
				$product_select_sql = "	SELECT p.products_id, pd.products_name, 
											pd.products_description, p.products_model, p.products_quantity, 
											p.products_bundle, pd.products_image, pd.products_url, p.products_price, 
											p.products_tax_class_id, p.products_date_added, p.products_date_available, 
											p.manufacturers_id 
										FROM " . TABLE_PRODUCTS . " p, " . TABLE_PRODUCTS_DESCRIPTION . " pd 
										WHERE p.products_status = '1' AND p.products_id = '" . $bundle_data["products_id"] . "' 
											AND pd.products_id = p.products_id AND pd.language_id = '1'";
				$product_result_sql = tep_db_query($product_select_sql);
				$product_info = tep_db_fetch_array($product_result_sql);
				
				$cnt = count($array);
				for ($a=0; $a < $cnt; $a++) {
					if(isset($array[$a]['id']) && $array[$a]['id'] == $product_info['products_id']) {
						//echo $array[$a]['bd_products_qty'];
//						if (tep_session_is_registered('customer_id')) {
//							tep_db_query("INSERT INTO ". TABLE_CUSTOMERS_BASKET_BUNDLE . " (customers_id, products_bundle_id, subproducts_id, subproducts_quantity, subproducts_price, customers_basket_bundle_date_added, products_categories_id) VALUES 
//										('" . (int)$customer_id . "', '" . tep_db_input($products_id) . "', '". $product_info['products_id'] . "', '" . $array[$a]['bd_products_qty'] . "', '". $product_info['products_price'] . "', '" . date('Ymd') . "', '" . $game_id . "')");
//						} else {
							$this->mm_contents[$products_id]['bundle'][$product_info['products_id']] = array('sub_qty' => $array[$a]['bd_products_qty'] );
//						}
					}
				}
			}
      		
        	if (is_array($attributes)) {
          		reset($attributes);
          		while (list($option, $value) = each($attributes)) {
            		$this->mm_contents[$products_id]['attributes'][$option] = $value;
					// insert into database
//            		if (tep_session_is_registered('customer_id')) tep_db_query("insert into " . TABLE_CUSTOMERS_BASKET_ATTRIBUTES . " (customers_id, products_id, products_options_id, products_options_value_id, products_categories_id) values ('" . (int)$customer_id . "', '" . tep_db_input($products_id) . "', '" . (int)$option . "', '" . (int)$value . "', '" . $game_id . "')");
          		}
        	}
      	}
      	
      	unset($isincart);
//      	$this->cleanup();	// IMPORTANT: This Will remove those product has < 1 in cart qty
//		$this->calculate();
		
		// assign a temporary unique ID to the order contents to prevent hack attempts during the checkout procedure
//      	$this->cartID = $this->generate_cart_id();
        
        return $this->mm_contents;
	}
    
    function get_mm_contents () {
        return $this->mm_contents;
    }
    
    function count_mm_contents() {	// get total number of items in cart 
      	$total_items = 0;
      	
      	if (is_array($this->mm_contents)) {
	        reset($this->mm_contents);
        	while (list($products_id, ) = each($this->mm_contents)) {
          		$total_items += $this->get_mm_quantity($products_id);
        	}
      	}
      	return $total_items;
	}
    
    function in_mm_cart($products_id) {
      	if (isset($this->mm_contents[$products_id])) {
        	return true;
      	} else {
        	return false;
      	}
    }
    
    function reset_mm (){
        $this->mm_contents = array();
    }
	
    function get_attributes_price($products_id, $attribute_arr, $customer_id) { // same with attributes_price() but by passing in data instead of using cart content
    	$attributes_price = 0;
		$customer_ind_discount = 0;
        
      	if (tep_not_empty($attribute_arr) && count($attribute_arr)) {
      		//CGDiscountSpecials start
      		$query = tep_db_query("select customers_discount from " . TABLE_CUSTOMERS . " where customers_id =  '" . $customer_id . "'");
          	if ($query_result = tep_db_fetch_array($query)) {
                $customer_ind_discount = $query_result['customers_discount'];
            }
            
          	// Merging store changes: Get the customer group discount
    		$customers_groups_info_array = tep_get_customer_group_discount($customer_id, $products_id, 'product');
			$customers_groups_discount = $customers_groups_info_array['cust_group_discount'];
			$customer_discount = $customer_ind_discount + $customers_groups_discount;
			
          	//CGDiscountSpecials end
          	reset($attribute_arr);
        	while (list($option, $value) = each($attribute_arr)) {
          		$attribute_price_query = tep_db_query("select options_values_price, price_prefix from " . TABLE_PRODUCTS_ATTRIBUTES . " where products_id = '" . (int)$products_id . "' and options_id = '" . (int)$option . "' and options_values_id = '" . (int)$value . "'");
          		$attribute_price = tep_db_fetch_array($attribute_price_query);
          		
		  		if ($customer_discount >= 0) {
		     		$attribute_price['options_values_price'] = $attribute_price['options_values_price'] + $attribute_price['options_values_price'] * abs($customer_discount) / 100;
	      		} else {
		     		$attribute_price['options_values_price'] = $attribute_price['options_values_price'] - $attribute_price['options_values_price'] * abs($customer_discount) / 100;
	      		}
				
          		if ($attribute_price['price_prefix'] == '+') {
            		$attributes_price += $attribute_price['options_values_price'];
          		} else {
            		$attributes_price -= $attribute_price['options_values_price'];
          		}
        	}
      	}
		
      	return $attributes_price;
	}
	
    function get_products_info($products_array, $involve_cart = true, $customer_id = 0) {
    	global $languages_id, $pm_status, $pm_period, $pm_date_from, $pm_date_to, $pm_value, $currencies;
		
      	if (!is_array($products_array)) return false;
      	
      	$pm_free = 0;
        $customer_ind_discount = 0;
        $return_products_array = array();
      	$prod_quantity_array = array();
      	$pre_order_array = array();
        
        $customers_discount_select_sql = "	SELECT customers_discount 
      										FROM " . TABLE_CUSTOMERS . " 
      										WHERE customers_id =  '" . $customer_id . "'";
      	$customers_discount_result_sql = tep_db_query($customers_discount_select_sql);
  		if ($customers_discount_row = tep_db_fetch_array($customers_discount_result_sql)) {
            $customer_ind_discount = $customers_discount_row['customers_discount'];
        }
  		
        if ($involve_cart) {
            $this->cleanup();	// Get the latest "valid" product since product's type might get changed during customer doing shopping
        }
      	
        reset($products_array);
      	while (list($products_id, ) = each($products_array)) {
      		$custom_type = $this->get_custom_prd_type($products_id);
			
			$total_prod = ($custom_type == 'hla' || $custom_type == 'power_leveling' || $custom_type == 'cdkey') ? count($products_array[$products_id]['custom'][$custom_type]) : 1;
			for ($i=0; $total_prod > $i; $i++) {
	      		$pre_order = false;
	      		
	      		if ($custom_type=='cdkey') {
	      			$qty = $products_array[$products_id]['custom']['cdkey'][$i]['qty'];
	      		} else {
      				$qty = $products_array[$products_id]['qty'];
      			}
	      		
	      		$products_query = tep_db_query("SELECT products_bundle, products_bundle_dynamic 
                                                FROM " . TABLE_PRODUCTS . " 
                                                WHERE products_id = '" . (int)$products_id . "'");
	        	if ($products = tep_db_fetch_array($products_query)) {
			      	if ($products["products_bundle"] == "yes") {
						$bundle_select_sql = "	SELECT pb.subproduct_id, pb.subproduct_qty, p.products_status 
												FROM " . TABLE_PRODUCTS_BUNDLES . " AS pb 
												LEFT JOIN " . TABLE_PRODUCTS . " AS p 
													ON pb.subproduct_id = p.products_id  
												WHERE bundle_id='" . $products_id . "'" ;
						$bundle_result_sql = tep_db_query($bundle_select_sql);
						while ($bundle_data = tep_db_fetch_array($bundle_result_sql)) {
							if (!$bundle_data["products_status"]) {
								$pre_order = false;
								break;
							}
							
							$prod_quantity_array[$bundle_data["subproduct_id"]] += $qty * $bundle_data["subproduct_qty"];
							$stock_status = tep_check_stock_status($bundle_data["subproduct_id"], $prod_quantity_array[$bundle_data["subproduct_id"]], '', $products_id);
			        		if ($stock_status == "pre-order") {
			          			$pre_order = true;
			          		}
						}
					} else if ($products["products_bundle_dynamic"] == "yes") {
						$bundle_select_sql = "	SELECT pb.subproduct_id, pb.subproduct_qty, p.products_status
												FROM " . TABLE_PRODUCTS_BUNDLES . " AS pb		
												LEFT JOIN " . TABLE_PRODUCTS . " AS p
													ON pb.subproduct_id = p.products_id 
												WHERE pb.bundle_id = '" . $products_id . "'";
						$bundle_result_sql = tep_db_query($bundle_select_sql);
						while ($bundle_data = tep_db_fetch_array($bundle_result_sql)) {
							if (isset($products_array[$products_id]["bundle"][$bundle_data["subproduct_id"]]['sub_qty']) && $products_array[$products_id]["bundle"][$bundle_data["subproduct_id"]]['sub_qty']) {		// if got value implies customer select this subproduct 
								if (!$bundle_data["products_status"]) {
									$pre_order = false;
									break;
								} else {
									$prod_quantity_array[$bundle_data["subproduct_id"]] += $products_array[$products_id]["bundle"][$bundle_data["subproduct_id"]]['sub_qty']*$bundle_data["subproduct_qty"];
				        			$stock_status = tep_check_stock_status($bundle_data["subproduct_id"], $prod_quantity_array[$bundle_data["subproduct_id"]], '', $products_id);
				          			if ($stock_status == "pre-order") {
						        		$pre_order = true;
				        			}
				        		}
				        	}
						}
					} else {
						$prod_status_select_sql = "	SELECT products_status 
													FROM " . TABLE_PRODUCTS . " 
													WHERE products_id = '" . (int)$products_id . "'";
			            $prod_status_result_sql = tep_db_query($prod_status_select_sql);
			            if ($prod_status_row = tep_db_fetch_array($prod_status_result_sql)) {
			            	if (!$prod_status_row["products_status"]) {
			            		$pre_order = false;
							} else {
				      			$prod_quantity_array[$products_id] += $qty;
				      			
								$product_instance_id = tep_not_null($products_array[$products_id]['custom'][$custom_type][$i]['hla_account_id']) ? $products_array[$products_id]['custom'][$custom_type][$i]['hla_account_id'] : '';
					        	$stock_status = tep_check_stock_status($products_id, $prod_quantity_array[$products_id], $product_instance_id);
				        		if ($stock_status == "pre-order") {
				        			$pre_order = true;
				        		}
					        }
			            }
					}
				}
				
				if ($pre_order)	 $pre_order_array[] = $products_id;
			}
		}
		
     	if ((int)$customer_id) {
      		if(STORE_PROMOTION == 'true'){
		    	$cnt_pro = 0;
                
                reset($products_array);
				while (list($products_id, ) = each($products_array)) {
					$cnt_pro++;
	      			$qty = $products_array[$products_id]['qty'];
					$custom_type = $this->get_custom_prd_type($products_id);
					$total_prod = ($custom_type == 'hla' || $custom_type == 'power_leveling' || $custom_type == 'cdkey') ? count($products_array[$products_id]['custom'][$custom_type]) : 1;
					
					for ($i=0; $total_prod > $i; $i++) {
//						$product_instance_id = tep_not_null($products_array[$products_id]['custom'][$custom_type][$i]['hla_account_id']) ? $products_array[$products_id]['custom'][$custom_type][$i]['hla_account_id'] : '';
//						$currencies->product_instance_id = $product_instance_id;
						
//						if ($custom_type == 'hla') {
//			    			$qty = $products_array[$products_id]['custom'][$custom_type][$i]['qty'];
//			    			
//							$pro_select_sql = "	SELECT p.products_id, p.products_model, ph.products_price, ph.products_base_currency, p.products_weight, p.products_tax_class_id 
//												FROM " . TABLE_PRODUCTS_HLA . " AS ph 
//												LEFT JOIN " . TABLE_PRODUCTS . " AS p 
//													ON p.products_id = ph.products_id 
//												WHERE ph.products_id = '" . (int)$products_id . "'
//													AND ph.products_hla_id = '" . $product_instance_id . "'";
//						} else {
							$pro_select_sql = " select p.products_id, p.products_model, p.products_price, p.products_weight, p.products_tax_class_id 
												from " . TABLE_PRODUCTS . " p
												where p.products_id = '" . (int)$products_id . "'";
//						}
						
						$pro_query = tep_db_query($pro_select_sql);
						if ($prod = tep_db_fetch_array($pro_query)) {
							$prod_price = $prod['products_price'];
							$prod_id = $prod['products_id'];
							$prod_price_normal =  $prod_price;		// normal price before discount
							
							// Merging store changes: Get the cat defined configuration settings
		        			$cat_cfg_array = tep_get_cfg_setting($prod_id, 'product', 'PRE_ORDER_DISCOUNT');
		        			
		        			// pre-order discount
			          		if (in_array($products_id, $pre_order_array) && abs($cat_cfg_array['PRE_ORDER_DISCOUNT'])) {
			      				$pre_order_discount = $cat_cfg_array['PRE_ORDER_DISCOUNT'];
			      			} else {
			      				$pre_order_discount = 0;
			      			}
			      			
							$prod_price = $currencies->get_product_price($prod_id, $customer_id, $pre_order_discount, (tep_not_null($prod['products_base_currency']) ? $prod['products_base_currency'] : $products['products_base_currency']));
				          	
							if(!$ntotal) {
								$ntotal = ($prod_price * $qty);		// price after discount
							} else {
								$ntotal = $ntotal + ($prod_price * $qty);
							}
							
							if ($price_cheap) { //2nd onward product go here
								$pm_free = 1;
								
								if($price_cheap >= $prod_price){
									//get now price
									$pm_after_id = $prod_id;
//									$this->pm_after_id = $pm_after_id;  // no calculation() involve
									$pm_after_price =  $prod_price;
									$price_cheap = $prod_price; //pass the cheapest value to compare next item
								} else {
									//get prev price
									$pm_after_price =  $price_cheap;	
								}
							} else { //first product go here
								$price_cheap = $prod_price;
								$pm_after_price =  $prod_price;
								$pm_after_id = $prod_id;
//								$this->pm_after_id = $pm_after_id;
								
								if($qty > 1){ 
									$pm_free = 1;
									$qty = $qty - 1;
								}
							}
						}
					}
				}
      		}
        	
			if($pm_free) {
				$ntotal = $ntotal - $pm_after_price;
				
				if($ntotal >= ($pm_value + 0)){
					//echo "PM is valid!";
					$pm_free = 1;
				} else {
					$pm_free = 0;
				}
			}
      	//}
      	}
		
		$promotions_query = tep_db_query("  select promotions_id, promotions_title, promotions_description, promotions_from, promotions_to, promotions_min_value 
                                            from promotions 
                                            where promotions_status = '1' order by promotions_id");
  		$promotions = tep_db_fetch_array($promotions_query);
		if ($promotions['promotions_id']) {
	    	$pm_date_from = $promotions['promotions_from'];
			$pm_date_to = $promotions['promotions_to'];
			$pm_value = $promotions['promotions_min_value'];
			
			$pm_todate = split("[/]", date('m/d/Y'));
		  	if($pm_todate[2])
		  		$pmtoday = mktime(0, 0, 0, $pm_todate[0], $pm_todate[1] ,$pm_todate[2] );
			
			$pm_dtfrom = split("[/]", tep_date_short($pm_date_from));
		  	if($pm_dtfrom[2])
		  		$pmfrom = mktime(0, 0, 0, $pm_dtfrom[0], $pm_dtfrom[1] ,$pm_dtfrom[2] );
		  	
		  	$pm_dtto = split("[/]", tep_date_short($pm_date_to));
		  	if($pm_dtto[2]) {
		  	   	$pmto = mktime(0, 0, 0, $pm_dtto[0], $pm_dtto[1] ,$pm_dtto[2] );
		  	    
		  	    if($pm_free) {
		  	    	if($pmfrom <= $pmtoday && $pmtoday <= $pmto){
		  	   			//echo "PM start n end date";
		  	   			$pm_free = 1;
						
						if($ntotal >= ($pm_value + 0)){
							//echo "PM is valid!";
							$pm_free = 1;
						} else {
							$pm_free = 0;
						}
		  	   		} else {
		  	   			if($pmtoday >= $pmto && $pmfrom <= $pmtoday){
		  	   				//echo "PM already end date!";	
							$pm_free = 0;
		  	   			}
		  	   		}
		  	    } else {
		  	    	$pm_free = 0;
				}
			}
		} else {
		    $pm_free = 0;
		}   
      	
      	$cnt = 0;
        reset($products_array);
      	while (list($products_id, ) = each($products_array)) {
			$custom_type = $this->get_custom_prd_type($products_id);
			$contents_array = $products_array[$products_id]['custom'][$custom_type];
			$total_prod = ($custom_type == 'hla' || $custom_type == 'power_leveling' || $custom_type == 'cdkey') ? count($contents_array) : 1;
			
			for ($i=0; $total_prod > $i; $i++) {
				$pre_order = false;
				
	      		$qty = $products_array[$products_id]['qty'];
      			$attribute_arr = isset($products_array[$products_id]['attributes']) ? $products_array[$products_id]['attributes'] : '';
                
//				$product_instance_id = tep_not_null($contents_array[$i]['hla_account_id']) ? $contents_array[$i]['hla_account_id'] : '';
//				$currencies->product_instance_id = $product_instance_id;
				
				$prod_cat_path = tep_get_product_path($products_id, true);
				if (tep_not_null($prod_cat_path)) {
					$prod_cat_path_array = explode('_', $prod_cat_path);
					$game_id = $prod_cat_path_array[0];
				} else {
					$game_id = 0;
				}
/*			
				$products_select_sql = "select p.products_id, p.products_model, p.products_image, p.products_price, p.products_base_currency, p.products_bundle, p.products_bundle_dynamic, p.products_weight, p.products_tax_class_id,p.custom_products_type_id 
										from " . TABLE_PRODUCTS . " p
										where p.products_id = '" . (int)$products_id . "'";
*/
//				if ($custom_type == 'hla') {
//					$products_select_sql = "SELECT p.products_id, p.products_model, pd.products_image, ph.products_price, ph.products_base_currency, p.products_bundle, p.products_bundle_dynamic, p.products_weight, p.products_tax_class_id, p.custom_products_type_id 
//											FROM " . TABLE_PRODUCTS_HLA . " AS ph 
//											LEFT JOIN " . TABLE_PRODUCTS . " AS p 
//												ON p.products_id = ph.products_id 
//											INNER JOIN " . TABLE_PRODUCTS_DESCRIPTION . " pd 
//												ON ph.products_id = pd.products_id
//											WHERE ph.products_id = '" . (int)$products_id . "'
//												AND ph.products_hla_id = '" . (int)$product_instance_id . "'";
//				} else {
					$products_select_sql = "SELECT p.products_id, p.products_model, pd.products_image, p.products_price, p.products_base_currency, p.products_bundle, p.products_bundle_dynamic, p.products_weight, p.products_tax_class_id,p.custom_products_type_id 
											FROM " . TABLE_PRODUCTS . " p
											INNER JOIN " . TABLE_PRODUCTS_DESCRIPTION . " pd 
												ON p.products_id=pd.products_id
											WHERE p.products_id = '" . (int)$products_id . "'
												AND pd.language_id = '1'";
//				}
	  			
	      		$products_query = tep_db_query($products_select_sql);
	        	if ($products = tep_db_fetch_array($products_query)) {
	        		$prod_applied_discount = array();	// Must be in correct ordering
	          		$prid = $products['products_id'];
	          		$products_name = tep_get_products_name($products_id);
	          		$products_price = $products['products_price'];
	          		$product_price_normal =  $products_price;
		          	
	          		// Merging store changes: Get the cat defined configuration settings
	    			$cat_cfg_array = tep_get_cfg_setting($products_id, 'product', 'PRE_ORDER_DISCOUNT');
	    			
	    			// Merging store changes: Get the customer group discount
		    		$customers_groups_info_array = tep_get_customer_group_discount($customer_id, $products_id, 'product');
					$customers_groups_discount = $customers_groups_info_array['cust_group_discount'];
					$customer_discount = $customer_ind_discount + $customers_groups_discount;
					
	    			// pre-order discount
	          		if (in_array($products_id, $pre_order_array) && abs($cat_cfg_array['PRE_ORDER_DISCOUNT'])) {
	      				$pre_order_discount = $cat_cfg_array['PRE_ORDER_DISCOUNT'];
	      			} else {
	      				$pre_order_discount = 0;
	      			}
	      			
	    			$products_price = $currencies->get_product_price($products_id, $customer_id, $pre_order_discount, $products['products_base_currency']);
	    			
	    			if (abs($pre_order_discount) > 0)	$prod_applied_discount[] = $pre_order_discount;
		          	if (abs($customer_discount) > 0)	$prod_applied_discount[] = $customer_discount;
		          	
		          	// Products Checkout Quantity Control
					$checkout_qty_exceed = tep_not_null($_SESSION['checkout_ctrl'][$products_id]['exceed']) ? $_SESSION['checkout_ctrl'][$products_id]['exceed'] : 0; 
					$checkout_qty_orders = tep_not_null($_SESSION['checkout_ctrl'][$products_id]['orders']) ? $_SESSION['checkout_ctrl'][$products_id]['orders'] : '';
						
	      			$custom_content_array = array();
	      			if (isset($products_array[$products_id]['custom']['cdkey'][$i]['top_up_info'])) {
	      				$custom_content_array = $products_array[$products_id]['custom']['cdkey'][$i]['top_up_info'];
	      			}
	      			if (isset($products_array[$products_id]['custom']['cdkey'][$i]['delivery_mode'])) {
	      				$custom_content_array['delivery_mode'] = $products_array[$products_id]['custom']['cdkey'][$i]['delivery_mode'];
	      			}
	      			
	      			if (!count($custom_content_array)) {
		      			$custom_content_array = (isset($products_array[$products_id]['custom'][$custom_type]) ? $products_array[$products_id]['custom'][$custom_type] : array());
		      		}
					
	   				if ($pm_free == 1 && (int)$products['custom_products_type_id']==0) { //if promotion
			      		if ($pm_after_id == $products_id) {
			          		$return_products_array[] = array(	
                                'id' => $products_id,
                                'name' => $products_name,
                                'model' => $products['products_model'],
                                'image' => $products['products_image'],
                                'price' => $products_price,
                                'base_currency' => $products['products_base_currency'],
                                'quantity' => $products_array[$products_id]['qty'],
                                'weight' => $products['products_weight'],
                                'final_price' => ($products_price + $this->get_attributes_price($products_id, $attribute_arr, $customer_id)),
                                'pm_price' => 'FREE',
                                'pm_total' => $ntotal,
                                'tax_class_id' => $products['products_tax_class_id'],
                                'normal_price' => $product_price_normal,
                                'discounts' => $prod_applied_discount,
                                'products_categories_id' => $game_id,
                                'attributes' => (isset($products_array[$products_id]['attributes']) ? $products_array[$products_id]['attributes'] : ''),
                                'pre_order' => (in_array($products_id, $pre_order_array) ? 1 : 0),
                                'custom_products_type_id' => (int)$products['custom_products_type_id'],
                                'custom_content' => $custom_content_array,
                                'checkout_qty_exceed' => $checkout_qty_exceed, 
                                'checkout_qty_orders' => $checkout_qty_orders
                            );
				    	} else {
					  		$return_products_array[] = array(	
                                'id' => $products_id,
                                'name' => $products_name,
                                'model' => $products['products_model'],
                                'image' => $products['products_image'],
                                'price' => $products_price,
                                'base_currency' => $products['products_base_currency'],
                                'quantity' => $products_array[$products_id]['qty'],
                                'weight' => $products['products_weight'],
                                'final_price' => $products_price,
                                'pm_price' => '',
                                'pm_total' => $ntotal,
                                'tax_class_id' => $products['products_tax_class_id'],
                                'normal_price' => $product_price_normal,
                                'discounts' => $prod_applied_discount,
                                'products_categories_id' => $game_id,
                                'attributes' => (isset($products_array[$products_id]['attributes']) ? $products_array[$products_id]['attributes'] : ''),
                                'pre_order' => (in_array($products_id, $pre_order_array) ? 1 : 0),
                                'custom_products_type_id' => (int)$products['custom_products_type_id'],
                                'custom_content' => $custom_content_array,
                                'checkout_qty_exceed' => $checkout_qty_exceed, 
                                'checkout_qty_orders' => $checkout_qty_orders
                            );
						}
			  		} else {
			  			//	This is TOTALLY NOT Promotion
						$final_price = 0;
						if ((int)$products['custom_products_type_id'] == 0) {
							$final_price = $products_price + $this->get_attributes_price($products_id, $attribute_arr, $customer_id);
							
							if ($custom_type=='cdkey') {
				      			$final_qty = $products_array[$products_id]['custom']['cdkey'][$i]['qty'];
				      		} else {
			      				$final_qty = $products_array[$products_id]['qty'];
			      			}
							
							$custom_type = $this->get_custom_prd_type($products_id);
							$session_array = $products_array[$products_id]['custom'][$custom_type];
							
							if ($products['products_bundle'] == 'yes') {
								$products['custom_products_type_id'] = tep_get_custom_product_type($products_id);
							}
				      		
							$return_products_array[] = array(	
                                'id' => $products_id,
                                'name' => $products_name,
                                'model' => $products['products_model'],
                                'image' => $products['products_image'],
                                'price' => $products_price,
                                'quantity' => $final_qty,
                                'weight' => $products['products_weight'],
                                'final_price' => $final_price,
                                'tax_class_id' => $products['products_tax_class_id'],
                                'normal_price' => $product_price_normal,
                                'products_categories_id' => $game_id,
                                'pm_price' => '',
                                'pm_total' => $ntotal,
                                'discounts' => $prod_applied_discount,
                                'attributes' => (isset($products_array[$products_id]['attributes']) ? $products_array[$products_id]['attributes'] : ''),
                                'pre_order' => (in_array($products_id, $pre_order_array) ? 1 : 0),
                                'custom_products_type_id' => (int)$products['custom_products_type_id'],
                                'custom_content' => $custom_content_array,
                                'products_bundle' => $products['products_bundle'],
                                'checkout_qty_exceed' => $checkout_qty_exceed,
                                'checkout_qty_orders' => $checkout_qty_orders
                            );
						} else if ((int)$products['custom_products_type_id'] == 2) {
							$final_price = $products_price + $this->get_attributes_price($products_id, $attribute_arr, $customer_id);
							
				      		if ($custom_type=='cdkey') {
				      			$final_qty = $products_array[$products_id]['custom']['cdkey'][$i]['qty'];
				      		} else {
			      				$final_qty = $products_array[$products_id]['qty'];
			      			}
			      			
							$return_products_array[] = array(	
                                'id' => $products_id,
                                'name' => $products_name,
                                'model' => $products['products_model'],
                                'image' => $products['products_image'],
                                'price' => $products_price,
                                'base_currency' => $products['products_base_currency'],
                                'quantity' => $final_qty,
                                'weight' => $products['products_weight'],
                                'final_price' => $final_price,
                                'tax_class_id' => $products['products_tax_class_id'],
                                'normal_price' => $product_price_normal,
                                'products_categories_id' => $game_id,
                                'pm_price' => '',
                                'pm_total' => $ntotal,
                                'discounts' => $prod_applied_discount,
                                'attributes' => (isset($products_array[$products_id]['attributes']) ? $products_array[$products_id]['attributes'] : ''),
                                'pre_order' => (in_array($products_id, $pre_order_array) ? 1 : 0),
                                'custom_products_type_id' => (int)$products['custom_products_type_id'],
                                'custom_content' => $custom_content_array,
                                'checkout_qty_exceed' => $checkout_qty_exceed,
                                'checkout_qty_orders' => $checkout_qty_orders
                            );
						} else if ((int)$products['custom_products_type_id'] == 1) {
							//Power Leveling
							$final_qty = 1;
							
							if ($customer_discount >= 0) {
								$custom_products_price = (double)($contents_array[$i]['content']['calculated']['price'] + $contents_array[$i]['content']['calculated']['price'] * abs($customer_discount) / 100);
							} else {
								$custom_products_price = (double)($contents_array[$i]['content']['calculated']['price'] - $contents_array[$i]['content']['calculated']['price'] * abs($customer_discount) / 100);
							}
							
							$return_products_array[] = array(	
                                'id' => $products_id,
                                'name' => $products_name,
                                'model' => $products['products_model'],
                                'image' => $products['products_image'],
                                'price' => $custom_products_price,
                                'base_currency' => $products['products_base_currency'],
                                'quantity' => $final_qty,
                                'weight' => $products['products_weight'],
                                'final_price' => $custom_products_price,
                                'tax_class_id' => $products['products_tax_class_id'],
                                'normal_price' => (double)$contents_array[$i]['content']['calculated']['price'],
                                'products_categories_id' => $game_id,
                                'pm_price' => '',
                                'pm_total' => $ntotal,
                                'discounts' => $prod_applied_discount,
                                'attributes' => (isset($products_array[$products_id]['attributes']) ? $products_array[$products_id]['attributes'] : ''),
                                'pre_order' => (in_array($products_id, $pre_order_array) ? 1 : 0),
                                'custom_products_type_id' => (int)$products['custom_products_type_id'],
                                'custom_content' => $contents_array[$i]['content'],
                                'checkout_qty_exceed' => $checkout_qty_exceed,
                                'checkout_qty_orders' => $checkout_qty_orders
                            );
						} else if ((int)$products['custom_products_type_id'] == 3) {
							$final_price = $products_price;
							$final_qty = (int)$products_array[$products_id]['qty'];
			  				
							$return_products_array[] = array(	
                                'id' => $products_id,
                                'name' => $products_name,
                                'model' => $products['products_model'],
                                'image' => $products['products_image'],
                                'price' => $products_price,
                                'base_currency' => $products['products_base_currency'],
                                'quantity' => $final_qty,
                                'weight' => $products['products_weight'],
                                'final_price' => $final_price,
                                'tax_class_id' => $products['products_tax_class_id'],
                                'normal_price' => $product_price_normal,
                                'products_categories_id' => $game_id,
                                'pm_price' => '',
                                'pm_total' => $ntotal,
                                'discounts' => $prod_applied_discount,
                                'attributes' => (isset($products_array[$products_id]['attributes']) ? $products_array[$products_id]['attributes'] : ''),
                                'pre_order' => (in_array($products_id, $pre_order_array) ? 1 : 0),
                                'custom_products_type_id' => (int)$products['custom_products_type_id'],
                                'custom_content' => array(),
                                'checkout_qty_exceed' => $checkout_qty_exceed,
                                'checkout_qty_orders' => $checkout_qty_orders
                            );
							$final_price = $products_price + $this->get_attributes_price($products_id, $attribute_arr, $customer_id);
							$final_qty = (int)$products_array[$products_id]['qty'];
			  				
							$return_products_array[] = array(	
                                'id' => $products_id,
                                'name' => $products_name,
                                'model' => $products['products_model'],
                                'image' => $products['products_image'],
                                'price' => $products_price,
                                'base_currency' => $products['products_base_currency'],
                                'quantity' => $final_qty,
                                'weight' => $products['products_weight'],
                                'final_price' => $final_price,
                                'tax_class_id' => $products['products_tax_class_id'],
                                'normal_price' => $product_price_normal,
                                'products_categories_id' => $game_id,
                                'pm_price' => '',
                                'pm_total' => $ntotal,
                                'discounts' => $prod_applied_discount,
                                'attributes' => (isset($products_array[$products_id]['attributes']) ? $products_array[$products_id]['attributes'] : ''),
                                'pre_order' => (in_array($products_id, $pre_order_array) ? 1 : 0),
                                'custom_products_type_id' => (int)$products['custom_products_type_id'],
                                'custom_content' => array(),
                                'checkout_qty_exceed' => $checkout_qty_exceed,
                                'checkout_qty_orders' => $checkout_qty_orders
                            );
						} else if ((int)$products['custom_products_type_id'] == 4) {
							// HLA
							$final_price = $products_price + $this->get_attributes_price($products_id, $attribute_arr, $customer_id);
							
							$return_products_array[] = array(	
                                'id' => $products_id,
                                'name' => $products_name,
                                'model' => $products['products_model'],
                                'image' => $products['products_image'],
                                'price' => $products_price,
                                'base_currency' => $products['products_base_currency'],
                                'quantity' => $contents_array[$i]['qty'],
                                'weight' => $products['products_weight'],
                                'final_price' => $final_price,
                                'tax_class_id' => $products['products_tax_class_id'],
                                'normal_price' => $product_price_normal,
                                'products_categories_id' => $game_id,
                                'pm_price' => '',
                                'pm_total' => $ntotal,
                                'discounts' => $prod_applied_discount,
                                'attributes' => (isset($products_array[$products_id]['attributes']) ? $products_array[$products_id]['attributes'] : ''),
                                'pre_order' => (in_array($products_id, $pre_order_array) ? 1 : 0),
                                'custom_products_type_id' => (int)$products['custom_products_type_id'],
                                'custom_content' => $contents_array[$i],
                                'checkout_qty_exceed' => $checkout_qty_exceed,
                                'checkout_qty_orders' => $checkout_qty_orders
                            );
						}
			  		}
				}
	        	$cnt++;
	        }
      	}
      	
//      	unset($_SESSION['checkout_ctrl']);  // Products Checkout Quantity Control
      	
      	return $return_products_array;
	}
    
    function get_mm_quantity($products_id) {
    	if (isset($this->mm_contents[$products_id])) {
			if (sizeof($this->mm_contents[$products_id]['custom']) > 0) {
				if (sizeof($this->mm_contents[$products_id]['custom'][$this->get_custom_prd_type($products_id)]) > 0) { // For now, power leveling, cd key and game currency
					return 1;
				}
			} else {
			   	return $this->mm_contents[$products_id]['qty'];
			}
      	} else {
        	return 0;
      	}
    }
    
    function get_cart2 ($checkout_button = '') {
		global $customer_id, $languages_id, $currencies, $order, $order_total_modules, $payment_modules, $payment_modules_info, $order_total_reprocess;
		global $payment;
		
        $cart_html = '';
        $cart_footer_html = '';
		$products = $this->get_products();
		$products_amt = sizeof($products);
		$custom_prod_index = array();
		$item_list_html = '';
		$default_pd_strlen_show = 31;
		$total_op = 0;
		
		$pd_strlen_show_array = array();
		$pd_strlen_show_array['ASCII'] = 50;
		$pd_strlen_show_array['UTF-8'] = 31;
		
		$current_select_pm_id = 0;
		if (tep_not_null($payment) && preg_match('/^(pm_)(\d+)(:~:)(\S+)(:~:)(\d+)$/', $payment, $selected_payment) && count($selected_payment) == 7) {
			$current_select_pm_id = (int)$selected_payment[6];
		}
        
		$extra_op_array = array();
		for ($i = 0; $i < $products_amt; $i++) {
	    	if ((int)$products[$i]['custom_products_type_id'] > 0) {
	      		$custom_prod_index[$products[$i]['id']] = (int)$custom_prod_index[$products[$i]['id']] + 1;
	      		$unique_pid = $products[$i]['id'].'_'.$custom_prod_index[$products[$i]['id']];
	      	} else {
	      		$unique_pid = $products[$i]['id'];
	      	}
	      	
	      	$prod_bd_query = tep_db_query("select products_bundle, products_bundle_dynamic, products_bundle_dynamic_qty from " . TABLE_PRODUCTS . " where products_id = " . $products[$i]['id'] );
		  	$prod_bd = tep_db_fetch_array($prod_bd_query);
	  	  	$prod_bundle = $prod_bd['products_bundle'];
	  	  	$prod_bundle_dynamic = $prod_bd['products_bundle_dynamic'];
	  	  	
	    	if ((int)$products[$i]['custom_products_type_id'] < 1) {
				if (isset($this->contents[$products[$i]['id']]['custom']['game_currency'])) {
					$custom_name = 'game_currency';
				} else if (isset($this->contents[$products[$i]['id']]['custom']['cdkey'])) {
					$custom_name = 'cdkey';
				} else if (isset($this->contents[$products[$i]['id']]['custom']['store_credit'])) {
					$custom_name = 'store_credit';
				} else if (isset($this->contents[$products[$i]['id']]['custom']['hla'])) {
					$custom_name = 'hla';
				}
				
		    	foreach($this->contents[$products[$i]['id']]['custom'][$custom_name] as $count => $extra_info_array) {
					$product_instance_id = tep_not_null($extra_info_array['hla_account_id']) ? $extra_info_array['hla_account_id'] : '';
			    	$currencies->product_instance_id = $product_instance_id;
			    	
		    		if (in_array($custom_name, array('cdkey', 'store_credit'))) {
		    			$product_qty = $this->contents[$products[$i]['id']]['qty'];
		    		} else {
		    			$product_qty = $extra_info_array['qty'];
		    		}
		    		
		    		$display_price = $currencies->display_price($products[$i]['id'], $products[$i]['normal_price'], tep_get_tax_rate($products[$i]['tax_class_id']), $product_qty, true, '', $current_select_pm_id);
		    		
		    		$total_op += $currencies->rebate_point;
		    		$extra_op_array[$unique_pid] += $currencies->rebate_point_extra;
			 	}
			} else {
				$product_instance_id = tep_not_null($products[$i]['custom_content']['hla_account_id']) ? $products[$i]['custom_content']['hla_account_id'] : '';
		    	$currencies->product_instance_id = $product_instance_id;
		    	
				$display_price = $currencies->display_price($products[$i]['id'], $products[$i]['normal_price'], tep_get_tax_rate($products[$i]['tax_class_id']), $products[$i]['quantity'], true, '', $current_select_pm_id);
				$total_op += $currencies->rebate_point;
				$extra_op_array[$unique_pid] += $currencies->rebate_point_extra;
			}
		}
		
		$custom_prod_index = array();
		if (MODULE_ORDER_TOTAL_INSTALLED) {
			if (!isset($order_total_reprocess) || $order_total_reprocess==true) $order_total_modules->process();
			$cust_group_extra_rebate = 0;
			$cust_group_extra_op = 0;
			if (tep_not_null($payment) && preg_match('/^(pm_)(\d+)(:~:)(\S+)(:~:)(\d+)$/', $payment, $selected_payment) && count($selected_payment) == 7) {
				for ($count_products = 0; $count_products < $products_amt; $count_products++) {
			    	if ((int)$products[$count_products]['custom_products_type_id'] > 0) {
			      		$custom_prod_index[$products[$count_products]['id']] = (int)$custom_prod_index[$products[$count_products]['id']] + 1;
			      		$unique_pid = $products[$count_products]['id'].'_'.$custom_prod_index[$products[$count_products]['id']];
			      	} else {
			      		$unique_pid = $products[$count_products]['id'];
			      	}
					
			    	if (isset($extra_op_array[$unique_pid]) && $extra_op_array[$unique_pid] > 0) {
						$surcharge_amt = 0;
						if (isset($GLOBALS['ot_surcharge']) && $GLOBALS['ot_surcharge']->enabled) {
		            		for ($ot_surcharge_count=0;$ot_surcharge_count < sizeof($GLOBALS['ot_surcharge']->output);$ot_surcharge_count++) {
		              			if (tep_not_null($GLOBALS['ot_surcharge']->output[$ot_surcharge_count]['title']) && tep_not_null($GLOBALS['ot_surcharge']->output[$ot_surcharge_count]['text'])) {
		                			$surcharge_amt = $GLOBALS['ot_surcharge']->output[$ot_surcharge_count]['value'];
		              			}
		            		}
						}
						
						$gst_amt = 0;
						if (isset($GLOBALS['ot_gst']) && $GLOBALS['ot_gst']->enabled) {
		            		for ($gst_cnt=0; $gst_cnt < sizeof($GLOBALS['ot_gst']->output); $gst_cnt++) {
		              			if (tep_not_null($GLOBALS['ot_gst']->output[$gst_cnt]['title']) && tep_not_null($GLOBALS['ot_gst']->output[$gst_cnt]['text'])) {
		                			$gst_amt = $GLOBALS['ot_gst']->output[$gst_cnt]['value'];
		              			}
		            		}
						}
						
						$cust_group_extra_rebate = floor($extra_op_array[$unique_pid] * ( ($order->info['total'] - $surcharge_amt - $gst_amt)/ $order->info['subtotal']));
						$cust_group_extra_op = $cust_group_extra_op + $cust_group_extra_rebate;
					}
				}
			}
			
			$cart_html .= $order_total_modules->output(array('selection' => false), $total_op, $cust_group_extra_op, 'basic');
		}
		
		if ($payment_modules->check_credit_covers() || $order->info['subtotal'] <= 0) {
			if (!tep_session_is_registered('payment')) tep_session_register('payment');
        }
			$cart_footer_html .=    tep_image_button2('red', 'javascript:void(0)', '<span class="lbl" data-cfm="' . IMAGE_BUTTON_CONFIRM_ORDER . '" data-load="'.str_replace(".", "", TEXT_LOADING_MESSAGE).'">' . IMAGE_BUTTON_CONFIRM_ORDER . '</span><span class="dots" data-id=""></span>', 200, ' id="pfv_btn_confirm" onclick="pfv_confirm_order(this)"');
			$cart_footer_html .= $checkout_button;
		
            //onsubmit="return payment_check_form();"
        $cart_html = tep_draw_form('checkout_payment', tep_href_link(FILENAME_CHECKOUT_CONFIRMATION, '', 'SSL'), 'post', ' id="coupon_form"') .
                    '<table border="0" width="100%" cellspacing="0" cellpadding="0">' .
                    $order_total_modules->store_credit_balance_html . 
                    $cart_html .
                    '</table>' .
                    '</form>' .
                    '<table cellspacing="0" cellpadding="0" border="0" width="100%">' .
                    '<tr class="footer"><td class="button" colspan="2">' . $cart_footer_html . '</td></tr>' .
                    $order_total_modules->store_credit_remain_html .
                    '</table>';
		
		return $cart_html;
	}
}
?>