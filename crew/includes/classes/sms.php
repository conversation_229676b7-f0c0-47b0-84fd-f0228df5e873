<?php
require_once(DIR_WS_CLASSES . 'curl.php');

class sms {
    public $send_sms;
    
	function  __construct() {
        $this->send_sms = (SMS_MODULE_MODE == 'Production' ? true : false);
    }
    
    public function send($customer_id, $phone_info, $sms_text, $sms_purpose) {
        $mobile_number = $phone_info['country_international_dialing_code'] . $phone_info['telephone_number'];
        
        if ($this->send_sms) {
            if ($sms_purpose != 'verify_phone' && in_array($phone_info['country_international_dialing_code'], array('62','60'))) {    // Indonesia, Malaysia
                $provider = 'mobile_ace';
            } else {
                $provider = 'clickatell';
            }
            
            include_once(DIR_WS_MODULES . 'sms/'.$provider.'.php');
            
            if (class_exists($provider)) {
                $sms_obj = new $provider();
                if ($provider == 'telesign_us') {
                    $sms_obj->send($phone_info['country_international_dialing_code'], $phone_info['telephone_number'], $sms_text);
                } else {
                    $sms_obj->send($mobile_number, $sms_text);
                }
                
                // Insert sms report records
                $sql_data_array = array(
                    'customers_id' => $customer_id,
                    'sms_request_date' => 'now()',
                    'sms_request_type' => $sms_purpose,
                    'sms_request_phone_number' => $mobile_number,
                    'sms_request_phone_country_id' => $phone_info['country_id'],
                    'sms_request_page' => $_SERVER['HTTP_REFERER'],
                    'sms_provider' => $provider
                );
                tep_db_perform(TABLE_SMS_REPORT, $sql_data_array);
            }
        }
        
        return true;
    }
    
    public function send_without_report($provider, $phone_number, $sms_text, $unicode=false) {
        if ($this->send_sms) {
            include_once(DIR_WS_MODULES . 'sms/'.$provider.'.php');
            
            if (class_exists($provider)) {
                $sms_obj = new $provider();
                
                if ($unicode) {
                    $sms_obj->unicode_msg = true;
                }
                
                if ($provider == 'telesign_us') {
                    $sms_obj->send($phone_number['country_international_dialing_code'], $phone_number['telephone_number'], $sms_text);
                } else {
                    $sms_obj->send($phone_number, $sms_text);
                }
            }
        }
        
        return true;
    }
}
?>