<?
/*
  	$Id: split_page_results.php,v 1.14 2013/08/14 08:11:28 wenbin.ng Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2003 osCommerce
	
  	Released under the GNU General Public License
*/

class splitPageResults {
	var $sql_query, $number_of_rows, $current_page_number, $number_of_pages, $number_of_rows_per_page, $page_name;
	//var $count_total=0;    var $counting_test=0;   echo ' test declare';  echo $counting_test;
	/* class constructor */
    function splitPageResults($query, $max_rows, $count_key = '*', $page_holder = 'page', $link = 'db_link', $full_query = false) {
      	global $HTTP_GET_VARS, $HTTP_POST_VARS;
      	
      	$counting_test= $max_rows + $counting_test;
		
      	$this->sql_query = $query;
      	$this->page_name = $page_holder;
		
      	if (isset($HTTP_GET_VARS[$page_holder])) {
        	$page = $HTTP_GET_VARS[$page_holder];
      	} else if (isset($HTTP_POST_VARS[$page_holder])) {
        	$page = $HTTP_POST_VARS[$page_holder];
      	} else {
        	$page = '';
      	}
		
      	if (empty($page) || !is_numeric($page)) $page = 1;
      	$this->current_page_number = $page;
		
      	$this->number_of_rows_per_page = $max_rows;
      	
        if($full_query === false){
            $pos_to = strlen($this->sql_query);
            $pos_from = strpos($this->sql_query, ' from', 0);

            $pos_group_by = strpos($this->sql_query, ' group by', $pos_from);
            if (($pos_group_by < $pos_to) && ($pos_group_by != false)) $pos_to = $pos_group_by;

            $pos_having = strpos($this->sql_query, ' having', $pos_from);
            if (($pos_having < $pos_to) && ($pos_having != false)) $pos_to = $pos_having;

            $pos_order_by = strpos($this->sql_query, ' order by', $pos_from);
            if (($pos_order_by < $pos_to) && ($pos_order_by != false)) $pos_to = $pos_order_by;

            if (strpos($this->sql_query, 'distinct') || strpos($this->sql_query, 'group by')) {
                    $count_string = 'distinct ' . tep_db_input($count_key);
            } else {
                    $count_string = tep_db_input($count_key);
            }

                    if (strpos($this->sql_query, 'group by') && strpos($this->sql_query, 'sum(')) {
                            $actual_query = tep_db_query($this->sql_query);
                            $this->number_of_rows = tep_db_num_rows($actual_query);
                    } else {
                    $count_query = tep_db_query("select count(" . $count_string . ") as total " . substr($this->sql_query, $pos_from, ($pos_to - $pos_from)), $link);

                    $count = tep_db_fetch_array($count_query);

                    $this->number_of_rows = $count['total'];
                    }
        } else {
            $count_query = tep_db_query($query);
            $this->number_of_rows = tep_db_num_rows($count_query);
        }
      	$this->number_of_pages = ceil($this->number_of_rows / $this->number_of_rows_per_page);
		
      	if ($this->current_page_number > $this->number_of_pages) {
        	$this->current_page_number = $this->number_of_pages;
      	}
		
      	$offset = $this->current_page_number > 0 ? ($this->number_of_rows_per_page * ($this->current_page_number - 1)) : 0;
		
      	$this->sql_query .= " limit " . $offset . ", " . $this->number_of_rows_per_page;
	}
	
	
	/* class functions */
	// display split-page-number-links
    function display_links($max_page_links, $parameters = '') {
    	global $PHP_SELF, $request_type;
    	
    	if ($this->number_of_rows <= 0)	return;
    	
      	$display_links_string = '';
      	$class = 'class="pageResults"';

      	if (tep_not_null($parameters) && (substr($parameters, -1) != '&')) $parameters .= '&';
		
		// previous button - not displayed on first page
      	if ($this->current_page_number > 1) $display_links_string .= '<a href="' . tep_href_link(basename($PHP_SELF), $parameters . $this->page_name . '=' . ($this->current_page_number - 1), $request_type) . '" class="pageResults" title=" ' . PREVNEXT_TITLE_PREVIOUS_PAGE . ' "><u>' . PREVNEXT_BUTTON_PREV . '</u></a> | &nbsp;&nbsp;';
		
		// check if number_of_pages > $max_page_links
      	$cur_window_num = intval($this->current_page_number / $max_page_links);
      	if ($this->current_page_number % $max_page_links) {
      		$cur_window_num++;
      	}
		
      	$max_window_num = intval($this->number_of_pages / $max_page_links);
      	if ($this->number_of_pages % $max_page_links) {
      		$max_window_num++;
      	}
		
		// page nn button
		// @200902201644 - Changed style as "Prev | 01 - 02 - 03 ... "
		//$last_set_first_number = $this->number_of_pages - $max_page_links + 1;
		$last_set_first_number = $this->number_of_pages - $max_page_links >= $max_page_links ? 1 + ($this->number_of_pages - $max_page_links) : $max_page_links + 1;
		if ($last_set_first_number > 1) {
			if ($this->current_page_number <= $last_set_first_number - 1) {
				$jump_index = ($cur_window_num - 1) * $max_page_links + 1;
				$condition1 = $cur_window_num * $max_page_links;			
	
				if ($cur_window_num * $max_page_links == $last_set_first_number || $this->current_page_number >= $last_set_first_number - 1) {
					$jump_index = ($last_set_first_number - $max_page_links <= 0) ? 1 : $last_set_first_number - $max_page_links;
					$condition1 = ($last_set_first_number - $max_page_links <= 0) ? $last_set_first_number : $last_set_first_number - 1;
				}
			} else {
				$jump_index = 1;
				$condition1 = $max_page_links;
			}
			
			$condition2 = $this->number_of_pages;
			for ($jump_to_page = $jump_index; ($jump_to_page <= $condition1) && ($jump_to_page <= $condition2); $jump_to_page++) {
				$dash = (($jump_to_page < $condition1) && ($jump_to_page < $condition2)) ? " - " : " ";
				if ($jump_to_page == $this->current_page_number) {
					$display_links_string .= '<b>' . sprintf("%02d", $jump_to_page) . '</b>'.$dash;
				} else {
					if ($jump_to_page < $last_set_first_number) {
						$display_links_string .= '<a href="' . tep_href_link(basename($PHP_SELF), $parameters . $this->page_name . '=' . $jump_to_page, $request_type) . '" class="pageResults" title=" ' . sprintf(PREVNEXT_TITLE_PAGE_NO, $jump_to_page) . ' "><u>' . sprintf("%02d", $jump_to_page) . '</u></a>'.$dash;
					}
				}
			}
      		
      		$has_prev_next_window_link = false;
      		
			// previous window of pages
	      	if ($cur_window_num > 1 && $last_set_first_number - $max_page_links > 1) {
	      		$has_prev_next_window_link = true;
	      		$display_links_string .= '<a href="' . tep_href_link(basename($PHP_SELF), $parameters . $this->page_name . '=' . (($this->current_page_number >= $last_set_first_number) ? $last_set_first_number - 1 : ($cur_window_num - 1) * $max_page_links), $request_type) . '" class="pageResults" title=" ' . sprintf(PREVNEXT_TITLE_PREV_SET_OF_NO_PAGE, $max_page_links) . ' "> . . . </a>';
	      	}
			
			// next window of pages
	      	if ($cur_window_num < $max_window_num && ($cur_window_num * $max_page_links) < $last_set_first_number - 1) {
	      		$has_prev_next_window_link = true;
	      		$display_links_string .= '<a href="' . tep_href_link(basename($PHP_SELF), $parameters . $this->page_name . '=' . ($cur_window_num * $max_page_links + 1), $request_type) . '" class="pageResults" title=" ' . sprintf(PREVNEXT_TITLE_NEXT_SET_OF_NO_PAGE, $max_page_links) . ' "> . . . </a>&nbsp;';
	      	}
			
			// @200902201715 - Changed style as " ... 10 - 11 - 12 | Next"
	      	for ($jump_to_page2 = $last_set_first_number; ($jump_to_page2 <= $this->number_of_pages); $jump_to_page2++) {
	      		if (!$has_prev_next_window_link && $jump_to_page2 == $jump_to_page) {
	      			$display_links_string .= ' - ';
	      		}
	      		
	        	$dash = (($jump_to_page2 < ($max_window_num * $max_page_links)) && ($jump_to_page2 < $this->number_of_pages)) ? " - " : " ";
	
	        	if ($jump_to_page2 == $this->current_page_number) {
	          		$display_links_string .= '<b>' . sprintf("%02d", $jump_to_page2) . '</b>'.$dash;
	        	} else {
	          		$display_links_string .= '<a href="' . tep_href_link(basename($PHP_SELF), $parameters . $this->page_name . '=' . $jump_to_page2, $request_type) . '" class="pageResults" title=" ' . sprintf(PREVNEXT_TITLE_PAGE_NO, $jump_to_page2) . ' "><u>' . sprintf("%02d", $jump_to_page2) . '</u></a>'.$dash;
	        	}
	      	}
      	
			// next button
			if (($this->current_page_number < $this->number_of_pages) && ($this->number_of_pages != 1)) $display_links_string .= '&nbsp;| <a href="' . tep_href_link(basename($PHP_SELF), $parameters . $this->page_name . '=' . ($this->current_page_number + 1), $request_type) . '" class="pageResults" title=" ' . PREVNEXT_TITLE_NEXT_PAGE . ' "><u>' . PREVNEXT_BUTTON_NEXT . '</u></a>&nbsp;';
			//$display_link_string  is --> [< Prev]   1  2  3  [Next >]
		}
		else {
			$display_links_string = '<b>'.sprintf("%02d", "1").'</b>';	
		}
      	
      	return $display_links_string;
	}
	
	
	// display number of total products found
    function display_count($text_output) {
		$to_num = ($this->number_of_rows_per_page * $this->current_page_number);
      	if ($to_num > $this->number_of_rows) $to_num = $this->number_of_rows;
		
      	$from_num = ($this->number_of_rows_per_page * ($this->current_page_number - 1));
		
      	if ($to_num == 0) {
        	$from_num = 0;
      	} else {
        	$from_num++;
      	}
		
      	return sprintf($text_output, $from_num, $to_num, $this->number_of_rows);
	}
}
?>
