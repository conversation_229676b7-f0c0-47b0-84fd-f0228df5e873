<?

class store_credit {
    var $credit_accounts, $user_id;
	var $credit_accounts_type;
	
	// class constructor
    function store_credit($user_id) {
      	$this->credit_accounts = array();
		$this->user_id = $user_id;
		
		$this->_get_current_credits_balance();
		
		$this->credit_accounts_type = array('R' => 'Reversible',
											'NR' => 'Non-Reversible'
											);
	}
	
	function get_sc_promotion_percentage() {
		$percentage = 0;
		
        if (isset($_SESSION['customer_id']) && is_numeric($_SESSION['customer_id'])) {
            $customers_groups_select_sql = "SELECT customers_groups_id 
                                            FROM " . TABLE_CUSTOMERS . " 
                                            WHERE customers_id =  '" . tep_db_input($_SESSION['customer_id']) . "'";
            $customers_groups_result_sql = tep_db_query($customers_groups_select_sql);
            $customers_groups_row = tep_db_fetch_array($customers_groups_result_sql);
            
            $extra_sc_select_sql = "SELECT customers_groups_extra_sc
                                    FROM " . TABLE_CUSTOMERS_GROUPS . "
                                    WHERE customers_groups_id = '".tep_db_input($customers_groups_row['customers_groups_id'])."'";
            $extra_sc_result_sql = tep_db_query($extra_sc_select_sql);
            $extra_sc_row = tep_db_fetch_array($extra_sc_result_sql);
            
            if ($extra_sc_row['customers_groups_extra_sc'] > 0) {
                $percentage = $extra_sc_row['customers_groups_extra_sc'];
            }
        }
        
		return $percentage;
	}
	
	function _get_current_credits_balance() {
		$credit_acc_select_sql = "	SELECT sc_reversible_amount, sc_irreversible_amount, sc_currency_id 
									FROM " . TABLE_COUPON_GV_CUSTOMER . " AS cgc 
									WHERE customer_id = '" . tep_db_input($this->user_id) . "'";
		$credit_acc_result_sql = tep_db_query($credit_acc_select_sql);
		if ($credit_acc_row = tep_db_fetch_array($credit_acc_result_sql)) {
			$this->credit_accounts = array(	'sc_reverse' => $credit_acc_row['sc_reversible_amount'],
											'sc_irreverse' => $credit_acc_row['sc_irreversible_amount'],
											'sc_currency_id' => $credit_acc_row['sc_currency_id']
											);
		}
	}
	
	function get_current_credits_balance($user_id) {
		$credit_amounts = array();
		
		$credit_acc_select_sql = "	SELECT sc_reversible_amount, sc_reversible_reserve_amount, sc_irreversible_amount, sc_irreversible_reserve_amount, sc_currency_id 
									FROM " . TABLE_COUPON_GV_CUSTOMER . " AS cgc 
									WHERE customer_id = '" . tep_db_input($user_id) . "'";
		$credit_acc_result_sql = tep_db_query($credit_acc_select_sql);
		if ($credit_acc_row = tep_db_fetch_array($credit_acc_result_sql)) {
			$credit_accounts = array(	'sc_reverse' => $credit_acc_row['sc_reversible_amount'],
										'sc_reverse_reserve_amt' => $credit_acc_row['sc_reversible_reserve_amount'],
										'sc_irreverse' => $credit_acc_row['sc_irreversible_amount'],
										'sc_irreverse_reserve_amt' => $credit_acc_row['sc_irreversible_reserve_amount'],
										'sc_currency_id' => $credit_acc_row['sc_currency_id']
									);
		}
		
		return $credit_accounts;
	}
	
	// Sets the available & actual qty of a product
	function set_store_credit_balance($sc_array, $trans_array, $admin_msg='', $user_msg='') {
		/*******************************************************************
			operator = + (add credit), - (deduct credit), = (assign new credit)
		*******************************************************************/
		$sql_update_array = array();
		
		// Generate the update sql
		for ($sc_cnt=0; $sc_cnt < count($sc_array); $sc_cnt++) {
			if ( ($sc_array[$sc_cnt]['field_name'] == 'sc_reversible_amount' || $sc_array[$sc_cnt]['field_name'] == 'sc_irreversible_amount') ) {
				$sc_array[$sc_cnt]['operator'] = trim($sc_array[$sc_cnt]['operator']);
				switch($sc_array[$sc_cnt]['operator']) {
					case '+':
						$sql_update_array[] = $sc_array[$sc_cnt]['field_name'] . ' = ' . $sc_array[$sc_cnt]['field_name'] . ' + ' . tep_db_input($sc_array[$sc_cnt]['value']);
						break;
					case '-':
						$sql_update_array[] = $sc_array[$sc_cnt]['field_name'] . ' = ' . $sc_array[$sc_cnt]['field_name'] . ' - ' . tep_db_input($sc_array[$sc_cnt]['value']);
						break;
					case '=':
						$sql_update_array[] = $sc_array[$sc_cnt]['field_name'] . ' = ' . tep_db_input($sc_array[$sc_cnt]['value']);
						break;
					default:
						break;
				}
			}
		}
		
	    if (count($sql_update_array)) {
	    	$customer_email_select_sql = "	SELECT customers_email_address 
	    									FROM " . TABLE_CUSTOMERS . " 
	    									WHERE customers_id = '" . tep_db_input($this->user_id) . "'";
	    	$customer_email_result_sql = tep_db_query($customer_email_select_sql);
  			
  			if ($customer_email_row = tep_db_fetch_array($customer_email_result_sql)) {
		    	$sql_update_array[] = ' sc_last_modified = now() ';
				
		    	$update_sql_str = " SET " . implode(', ', $sql_update_array);
		    	
		    	$sc_update_sql = "	UPDATE " . TABLE_COUPON_GV_CUSTOMER . 
		    						$update_sql_str . " 
									WHERE customer_id = '" . tep_db_input($this->user_id) . "'";
				tep_db_query($sc_update_sql);
				
				$this->_get_current_credits_balance();
				
				if (count($this->credit_accounts)) {
					for ($sc_cnt=0; $sc_cnt < count($sc_array); $sc_cnt++) {
						if ($sc_array[$sc_cnt]['field_name'] == 'sc_reversible_amount') {
							$sc_balance_history_data_array = array(	'customer_id' => tep_db_prepare_input($this->user_id),
																	'store_credit_account_type' => 'R',
											                        'store_credit_history_date' => 'now()',
											                        'store_credit_history_currency_id' => $this->credit_accounts['sc_currency_id'],
											                        'store_credit_history_debit_amount' => (double)$sc_array[$sc_cnt]['value'],
										    	                    'store_credit_history_credit_amount' => 'NULL',
																	'store_credit_history_r_after_balance' => (double)$this->credit_accounts['sc_reverse'],
																	'store_credit_history_nr_after_balance' => (double)$this->credit_accounts['sc_irreverse'],
																	'store_credit_history_trans_type' => $trans_array['type'],
																	'store_credit_history_trans_id' => $trans_array['id'],
										                    	    'store_credit_activity_type' => LOG_SC_ACTIVITY_TYPE_PURCHASE,
										                        	'store_credit_history_added_by' => $customer_email_row['customers_email_address'],
										                        	'store_credit_history_added_by_role' => 'customers'
											                       );
							tep_db_perform(TABLE_STORE_CREDIT_HISTORY, $sc_balance_history_data_array);
						} else if ($sc_array[$sc_cnt]['field_name'] == 'sc_irreversible_amount') {
							// Insert store credit history
							$sc_balance_history_data_array = array(	'customer_id' => tep_db_prepare_input($this->user_id),
																	'store_credit_account_type' => 'NR',
											                        'store_credit_history_date' => 'now()',
											                        'store_credit_history_currency_id' => $this->credit_accounts['sc_currency_id'],
											                        'store_credit_history_debit_amount' => (double)$sc_array[$sc_cnt]['value'],
										    	                    'store_credit_history_credit_amount' => 'NULL',
										    	                    'store_credit_history_r_after_balance' => (double)$this->credit_accounts['sc_reverse'],
																	'store_credit_history_nr_after_balance' => (double)$this->credit_accounts['sc_irreverse'],
																	'store_credit_history_trans_type' => $trans_array['type'],
																	'store_credit_history_trans_id' => $trans_array['id'],
										                    	    'store_credit_activity_type' => LOG_SC_ACTIVITY_TYPE_PURCHASE,
										                        	'store_credit_history_added_by' => $customer_email_row['customers_email_address'],
										                        	'store_credit_history_added_by_role' => 'customers'
											                       );
							tep_db_perform(TABLE_STORE_CREDIT_HISTORY, $sc_balance_history_data_array);
						}
					}
				}
			}
			
    		return $this->credit_accounts;
    		
	    }
	}
	
	function get_credit_accounts() {
		return $this->credit_accounts;
	}
	
	function miscellaneous_deduct_amount($input_array, $action_message = '', $action_desc = '') {
		global $currencies;
		
		$return_result = array();
		$error = false;
		
		if (!isset($input_array['currency_id'])) {
			$input_array['currency_id'] = array_search(DEFAULT_CURRENCY, $currencies->internal_currencies);
		}
		
		if (isset($this->credit_accounts_type[$input_array['sc_type']])) {
			// Will create store credit account if not exists
			if ($this->_check_credits_account_exists()) {
				$deduct_amount = trim($input_array['deduct_amount']);
				if (is_numeric($deduct_amount) && $deduct_amount >= 0) {
					// Update live credit balance
					$update_info = array(	array(	'field_name'=> $input_array['sc_type'] == 'R' ? 'sc_reversible_amount' : 'sc_irreversible_amount',
													'operator'=> '-', 
													'value'=> $deduct_amount,
													'currency_id' => $input_array['currency_id'])
										);
					
					$new_sc_balance = $this->_set_store_credit_balance($update_info);
					
					// Insert store credit history
					$sc_balance_history_data_array = array(	'customer_id' => tep_db_prepare_input($this->user_id),
															'store_credit_account_type' => tep_db_prepare_input($input_array['sc_type']),
									                        'store_credit_history_date' => 'now()',
															'store_credit_history_currency_id' => (int)$new_sc_balance['sc_currency_id'],
									                        'store_credit_history_debit_amount' => (double)$deduct_amount,
								    	                    'store_credit_history_credit_amount' => 'NULL',
															'store_credit_history_r_after_balance' => (double)$new_sc_balance['sc_reverse'],
															'store_credit_history_nr_after_balance' => (double)$new_sc_balance['sc_irreverse'],
															'store_credit_history_trans_type' => tep_db_prepare_input($input_array['type']),
															'store_credit_history_trans_id' => tep_db_prepare_input($input_array['id']),
								                    	    'store_credit_activity_type' => tep_db_prepare_input($input_array['act_type']),
								                    	    'store_credit_history_activity_title' => $action_message,
															'store_credit_history_activity_desc' => tep_db_prepare_input($action_desc),
															'store_credit_history_activity_desc_show' => (int)$input_array['show_desc'],
								                        	'store_credit_history_added_by' => tep_db_prepare_input($input_array['added_by']),
								                        	'store_credit_history_added_by_role' => tep_db_prepare_input($input_array['added_by_role'])
									                       );
					tep_db_perform(TABLE_STORE_CREDIT_HISTORY, $sc_balance_history_data_array);
					$sc_transaction_id = tep_db_insert_id();
					
					$return_result = array('sc_type' => $input_array['sc_type'], 'sc_trans_id' => $sc_transaction_id, 'sc_amount' => (double)$deduct_amount, 'sc_currency' => $currencies->get_code_by_id($new_sc_balance['sc_currency_id']));
				} else {
					$error = true;
				}
			} else {
				$error = true;
			}
		} else {
			$error = true;
		}
		
		if (!$error) {
			return $return_result;
		} else {
			return false;
		}
	}
	
	function miscellaneous_add_amount($input_array, $action_message = '', $action_desc = '') {
		global $currencies;
		
		$return_result = array();
		$error = false;
		
		if (!isset($input_array['currency_id'])) {
			$input_array['currency_id'] = array_search(DEFAULT_CURRENCY, $currencies->internal_currencies);
		}
		
		if (isset($this->credit_accounts_type[$input_array['sc_type']])) {
			// Will create store credit account if not exists
			if ($this->_check_credits_account_exists($input_array['currency_id'])) {
				$add_amount = trim($input_array['add_amount']);
				if (is_numeric($add_amount) && $add_amount >= 0) {
					// Update live credit balance
					$update_info = array(	array(	'field_name'=> $input_array['sc_type'] == 'R' ? 'sc_reversible_amount' : 'sc_irreversible_amount',
													'operator'=> '+', 
													'value'=> $add_amount,
													'currency_id' => $input_array['currency_id'])
										);
					
					$new_sc_balance = $this->_set_store_credit_balance($update_info);
					
					// Insert store credit history
					$sc_balance_history_data_array = array(	'customer_id' => tep_db_prepare_input($this->user_id),
															'store_credit_account_type' => tep_db_prepare_input($input_array['sc_type']),
									                        'store_credit_history_date' => 'now()',
											                'store_credit_history_currency_id' => $new_sc_balance['sc_currency_id'],
									                        'store_credit_history_debit_amount' => 'NULL',
								    	                    'store_credit_history_credit_amount' => (double)$new_sc_balance['converted_amount'],
															'store_credit_history_r_after_balance' => (double)$new_sc_balance['sc_reverse'],
															'store_credit_history_nr_after_balance' => (double)$new_sc_balance['sc_irreverse'],
															'store_credit_history_trans_type' => tep_db_prepare_input($input_array['type']),
															'store_credit_history_trans_id' => tep_db_prepare_input($input_array['id']),
								                    	    'store_credit_activity_type' => tep_db_prepare_input($input_array['act_type']),
								                    	    'store_credit_history_activity_title' => $action_message,
															'store_credit_history_activity_desc' => tep_db_prepare_input($action_desc),
															'store_credit_history_activity_desc_show' => (int)$input_array['show_desc'],
								                        	'store_credit_history_added_by' => tep_db_prepare_input($input_array['added_by']),
								                        	'store_credit_history_added_by_role' => tep_db_prepare_input($input_array['added_by_role'])
									                       );
					tep_db_perform(TABLE_STORE_CREDIT_HISTORY, $sc_balance_history_data_array);
					$sc_transaction_id = tep_db_insert_id();
					
					$return_result = array('sc_type' => $input_array['sc_type'], 'sc_trans_id' => $sc_transaction_id, 'sc_amount' => (double)$add_amount, 'sc_currency_id' => $new_sc_balance['sc_currency_id'], 'sc_currency' => $currencies->get_code_by_id($new_sc_balance['sc_currency_id']));
					
					//$messageStack->add_session(SUCCESS_SC_STAT_SC_CREDITED, 'success');
				} else {
					$error = true;
					//$messageStack->add_session(ERROR_SC_STAT_INVALID_ADDITION_AMOUNT, 'error');
				}
			} else {
				$error = true;
				//$messageStack->add_session(ERROR_SC_STAT_USER_NOT_EXISTS, 'error');
			}
		} else {
			$error = true;
			//$messageStack->add_session(ERROR_SC_STAT_SC_TYPE_NOT_EXISTS, 'error');
		}
		
		if (!$error) {
			return $return_result;
		} else {
			return false;
		}
	}
	
	function _check_credits_account_exists($new_sc_currency_id = 0) {
		$credit_acc_select_sql = "	SELECT customer_id 
									FROM " . TABLE_COUPON_GV_CUSTOMER . " 
									WHERE customer_id = '" . tep_db_input($this->user_id) . "'";
		$credit_acc_result_sql = tep_db_query($credit_acc_select_sql);
		
		if (tep_db_num_rows($credit_acc_result_sql) > 0) {	// Store credit account exists
			return true;
		} else {	// Check if this is a valid customer
			$customer_select_sql = "SELECT customers_id 
									FROM " . TABLE_CUSTOMERS . " 
									WHERE customers_id = '" . tep_db_input($this->user_id) . "'";
			$customer_result_sql = tep_db_query($customer_select_sql);
			
			if (tep_db_num_rows($customer_result_sql) > 0) {	// Valid customer
				$sc_balance_data_array = array(	'customer_id' => $this->user_id,
												'sc_reversible_amount' => 0,
												'sc_reversible_reserve_amount' => 0,
												'sc_irreversible_amount' => 0,
												'sc_irreversible_reserve_amount' => 0,
												'sc_last_modified' => 'now()'
											);
                
                if ($new_sc_currency_id) {
                    $sc_balance_data_array['sc_currency_id'] = $new_sc_currency_id;
                }
                
				tep_db_perform(TABLE_COUPON_GV_CUSTOMER, $sc_balance_data_array);
				
				return true;
			} else {
				return false;
			}
		}
	}
	
	// Sets the available & actual qty of a product
	function _set_store_credit_balance($sc_array) {
		global $currencies;
		/*******************************************************************
			operator = + (add credit), - (deduct credit), = (assign new credit)
		*******************************************************************/
		$sql_update_array = array();
        $converted_amount = 0;
        
		$credit_acc_select_sql = "	SELECT DISTINCT sc_currency_id 
									FROM " . TABLE_COUPON_GV_CUSTOMER . " 
									WHERE customer_id = '" . tep_db_input($this->user_id) . "'";
		$credit_acc_result_sql = tep_db_query($credit_acc_select_sql);
		$credit_acc_row = tep_db_fetch_array($credit_acc_result_sql);
		
		// Generate the update sql
		for ($sc_cnt=0; $sc_cnt < count($sc_array); $sc_cnt++) {
			if ( ($sc_array[$sc_cnt]['field_name'] == 'sc_reversible_amount' || $sc_array[$sc_cnt]['field_name'] == 'sc_irreversible_amount') ) {
				$sc_array[$sc_cnt]['operator'] = trim($sc_array[$sc_cnt]['operator']);
				
                if ($credit_acc_row['sc_currency_id'] != $sc_array[$sc_cnt]['currency_id']) {
					$sc_array[$sc_cnt]['value'] = $currencies->advance_currency_conversion($sc_array[$sc_cnt]['value'], $currencies->get_code_by_id($sc_array[$sc_cnt]['currency_id']), $currencies->get_code_by_id($credit_acc_row['sc_currency_id']), true, 'sell');
				}
                
                $converted_amount = $sc_array[$sc_cnt]['value'];
                
				switch($sc_array[$sc_cnt]['operator']) {
					case '+':
						$sql_update_array[] = $sc_array[$sc_cnt]['field_name'] . ' = ' . $sc_array[$sc_cnt]['field_name'] . ' + ' . tep_db_input($sc_array[$sc_cnt]['value']);
						break;
					case '-':
						$sql_update_array[] = $sc_array[$sc_cnt]['field_name'] . ' = ' . $sc_array[$sc_cnt]['field_name'] . ' - ' . tep_db_input($sc_array[$sc_cnt]['value']);
						break;
					case '=':
						$sql_update_array[] = $sc_array[$sc_cnt]['field_name'] . ' = ' . tep_db_input($sc_array[$sc_cnt]['value']);
						break;
					default:
						break;
				}
			} else if ($sc_array[$sc_cnt]['field_name'] == 'sc_currency_id') {
				$sc_array[$sc_cnt]['operator'] = trim($sc_array[$sc_cnt]['operator']);
				switch($sc_array[$sc_cnt]['operator']) {
					case '=':
						$sql_update_array[] = $sc_array[$sc_cnt]['field_name'] . ' = ' . tep_db_input($sc_array[$sc_cnt]['value']);
						break;
					default:
						break;
				}
			}
		}
		
	    if (count($sql_update_array)) {
	    	$sql_update_array[] = ' sc_last_modified = now() ';
			
	    	$update_sql_str = " SET " . implode(', ', $sql_update_array);
	    	
	    	/*************************************************************************
			 	Lock the TABLE_COUPON_GV_CUSTOMER 
			 	REMEMBER: Need to lock all the tables involved in this block.
			*************************************************************************/
	    	tep_db_query("LOCK TABLES " . TABLE_COUPON_GV_CUSTOMER . " WRITE;");
	    	
	    	$sc_update_sql = "	UPDATE " . TABLE_COUPON_GV_CUSTOMER . 
	    						$update_sql_str . " 
								WHERE customer_id = '" . tep_db_input($this->user_id) . "'";
			tep_db_query($sc_update_sql);
			
			tep_db_query("UNLOCK TABLES;");
			/********************************************************************
			 	End of locking the TABLE_COUPON_GV_CUSTOMER table.
			********************************************************************/
			
    		$this->_get_current_credits_balance();
            if ($converted_amount > 0) $this->credit_accounts['converted_amount'] = $converted_amount;
    		return $this->credit_accounts;
	    }
	}
	
	function store_credit_conversion($input_array, &$messageStack)
	{
		global $currencies;
		
		// check CONVERT TO currency still active in master currencies
		$curr_code =  isset($input_array['currency_selection']) ? $input_array['currency_selection'] : '';
		if (!tep_not_null($curr_code)) { return false; }
		
		if (!isset($currencies->currencies[$curr_code])) {
			$messageStack->add('account_store_credit', ERROR_INVALID_STORE_CREDIT_CURRENCY_SELECTION);
			return false;
		}
		
		$curr_id = array_search($curr_code, $currencies->internal_currencies);
		if (is_null($curr_id) || $curr_id == false) { return false; }
		
		$store_credit_conversion_history_date = date("Y-m-d H:i:s");
		$sc_reissue_result = array();
		$usd_store_credit_row = $new_store_credit_row = array(
															'sc_reverse' => 0,
															'sc_reverse_reserve_amt' => 0,
															'sc_irreverse' => 0,
															'sc_irreverse_reserve_amt' => 0
															);
		
		// get customers info
		$customer_row = $this->get_customer_info($this->user_id);
		
		// first get all store credit amounts
		$store_credit_row = $this->get_current_credits_balance($this->user_id);
		
		if (count($store_credit_row) > 0) {
			// check CONVERT FROM currency still active in master currencies
			if (!isset($currencies->currencies[$currencies->get_code_by_id($store_credit_row['sc_currency_id'])])) {
				$messageStack->add('account_store_credit', ERROR_INVALID_STORE_CREDIT_CURRENCY);
				return false;
			}
            
            if (($store_credit_row['sc_reverse']+$store_credit_row['sc_reverse_reserve_amt']) < 0 || ($store_credit_row['sc_irreverse']+$store_credit_row['sc_irreverse_reserve_amt']) < 0) {
                $messageStack->add('account_store_credit', ERROR_SC_CONVERSION);
                return false;
            }
			
			$from_currency_code = $currencies->get_code_by_id($store_credit_row['sc_currency_id']);
			
			// Create store credit statement records for RSC and NRSC before conversion to deduct the to-be-converted-amount
			$trans_array = array('sc_type' => 'R', 'added_by' => $customer_row['customers_email_address'], 'added_by_role' => 'customers', 'type' => '', 'id' => '', 'act_type' => LOG_SC_ACTIVITY_TYPE_CONVERT, 'deduct_amount' => ($store_credit_row['sc_reverse']+$store_credit_row['sc_reverse_reserve_amt']), 'show_desc' => 0, 'currency_id' => $store_credit_row['sc_currency_id']);
			$sc_reissue_result[] = $this->miscellaneous_deduct_amount($trans_array, LOG_SC_STAT_SC_CONVERT);
			$trans_array = array('sc_type' => 'NR', 'added_by' => $customer_row['customers_email_address'], 'added_by_role' => 'customers', 'type' => '', 'id' => '', 'act_type' => LOG_SC_ACTIVITY_TYPE_CONVERT, 'deduct_amount' => ($store_credit_row['sc_irreverse']+$store_credit_row['sc_irreverse_reserve_amt']), 'show_desc' => 0, 'currency_id' => $store_credit_row['sc_currency_id']);
			$sc_reissue_result[] = $this->miscellaneous_deduct_amount($trans_array, LOG_SC_STAT_SC_CONVERT);
			
			$new_store_credit_row['sc_reverse'] = $currencies->advance_currency_conversion($store_credit_row['sc_reverse'], $from_currency_code, $curr_code, true, 'sell');
			$new_store_credit_row['sc_reverse_reserve_amt'] = $currencies->advance_currency_conversion($store_credit_row['sc_reverse_reserve_amt'], $from_currency_code, $curr_code, true, 'sell');
			$new_store_credit_row['sc_irreverse'] = $currencies->advance_currency_conversion($store_credit_row['sc_irreverse'], $from_currency_code, $curr_code, true, 'sell');
			$new_store_credit_row['sc_irreverse_reserve_amt'] = $currencies->advance_currency_conversion($store_credit_row['sc_irreverse_reserve_amt'], $from_currency_code, $curr_code, true, 'sell');
			$exch_rate = $currencies->advance_currency_conversion_rate($from_currency_code, $curr_code, 'sell');
			
			$store_credit_sql_data = array(	'sc_currency_id' => $curr_id,
											'sc_conversion_date' => $store_credit_conversion_history_date
											);
			tep_db_perform(TABLE_COUPON_GV_CUSTOMER, $store_credit_sql_data, 'update' , "customer_id='".$this->user_id."'");
			
			// Create store credit statement records for RSC and NRSC after conversion by adding the converted-amount
			$trans_array = array('sc_type' => 'R', 'added_by' => $customer_row['customers_email_address'], 'added_by_role' => 'customers', 'type' => '', 'id' => '', 'act_type' => LOG_SC_ACTIVITY_TYPE_CONVERT, 'add_amount' => ($new_store_credit_row['sc_reverse']+$new_store_credit_row['sc_reverse_reserve_amt']), 'show_desc' => 0, 'currency_id' => $curr_id);
			$sc_reissue_result[] = $this->miscellaneous_add_amount($trans_array, LOG_SC_STAT_SC_CONVERT);
			$trans_array = array('sc_type' => 'NR', 'added_by' => $customer_row['customers_email_address'], 'added_by_role' => 'customers', 'type' => '', 'id' => '', 'act_type' => LOG_SC_ACTIVITY_TYPE_CONVERT, 'add_amount' => ($new_store_credit_row['sc_irreverse']+$new_store_credit_row['sc_irreverse_reserve_amt']), 'show_desc' => 0, 'currency_id' => $curr_id);
			$sc_reissue_result[] = $this->miscellaneous_add_amount($trans_array, LOG_SC_STAT_SC_CONVERT);
			
			// add RSC history records into conversion history table
			$sc_conversion_history_data_array = array(	'customer_id' => $this->user_id,
        	                       						'store_credit_conversion_history_date' => $store_credit_conversion_history_date,
	        	                        				'store_credit_account_type' => 'RSC',
	            	        	           				'store_credit_conversion_from_currency_id' => $customer_row['sc_currency_id'],
	                	                				'store_credit_conversion_from_amount' => number_format($store_credit_row['sc_reverse'], $currencies->currencies[$from_currency_code]['decimal_places'], $currencies->currencies[$from_currency_code]['decimal_point'], ''),
	                    	            				'store_credit_conversion_from_reserve_amount' => number_format($store_credit_row['sc_reverse_reserve_amt'], $currencies->currencies[$from_currency_code]['decimal_places'], $currencies->currencies[$from_currency_code]['decimal_point'], ''),
	                    		           				'store_credit_conversion_to_currency_id' => $curr_id,
	                    	    	       				'store_credit_conversion_to_amount' => number_format($new_store_credit_row['sc_reverse'], $currencies->currencies[$curr_code]['decimal_places'], $currencies->currencies[$curr_code]['decimal_point'], ''),
	                    	        	   				'store_credit_conversion_to_reserve_amount' => number_format($new_store_credit_row['sc_reverse_reserve_amt'], $currencies->currencies[$curr_code]['decimal_places'], $currencies->currencies[$curr_code]['decimal_point'], ''),
	                    	           					'store_credit_conversion_currency_values' => $exch_rate
                    	           						);
			tep_db_perform(TABLE_STORE_CREDIT_CONVERSION_HISTORY, $sc_conversion_history_data_array);
			
			// add NRSC history records into conversion history table
			$sc_conversion_history_data_array = array(	'customer_id' => $this->user_id,
            	                   						'store_credit_conversion_history_date' => $store_credit_conversion_history_date,
	            	                    				'store_credit_account_type' => 'NRSC',
	                	    	           				'store_credit_conversion_from_currency_id' => $customer_row['sc_currency_id'],
	                    	            				'store_credit_conversion_from_amount' => number_format($store_credit_row['sc_irreverse'], $currencies->currencies[$from_currency_code]['decimal_places'], $currencies->currencies[$from_currency_code]['decimal_point'], ''),
	                        	        				'store_credit_conversion_from_reserve_amount' => number_format($store_credit_row['sc_irreverse_reserve_amt'], $currencies->currencies[$from_currency_code]['decimal_places'], $currencies->currencies[$from_currency_code]['decimal_point'], ''),
	                    	    	       				'store_credit_conversion_to_currency_id' => $curr_id,
	                    	        	   				'store_credit_conversion_to_amount' => number_format($new_store_credit_row['sc_irreverse'], $currencies->currencies[$curr_code]['decimal_places'], $currencies->currencies[$curr_code]['decimal_point'], ''),
	                    	           					'store_credit_conversion_to_reserve_amount' => number_format($new_store_credit_row['sc_irreverse_reserve_amt'], $currencies->currencies[$curr_code]['decimal_places'], $currencies->currencies[$curr_code]['decimal_point'], ''),
	                    	           					'store_credit_conversion_currency_values' => $exch_rate
                    	           						);
			tep_db_perform(TABLE_STORE_CREDIT_CONVERSION_HISTORY, $sc_conversion_history_data_array);
			
			return true;
		} else {
			return false;
		}
	}
	
	function get_total_store_credit_in_new_currency($new_currency_code = DEFAULT_CURRENCY, $return_raw = false)
	{
		global $currencies;
		
		$new_curr_check = array_search($new_currency_code, $currencies->internal_currencies);
		if (is_null($new_curr_check) || $new_curr_check == false) { return '0'; }
		
		$store_credit_row = $this->get_current_credits_balance($this->user_id);
		if (count($store_credit_row) > 0) {
			$total_store_credit = ($store_credit_row['sc_reverse'] + $store_credit_row['sc_irreverse']) - ($store_credit_row['sc_reverse_reserve_amt'] + $store_credit_row['sc_irreverse_reserve_amt']);
			$current_store_credit_currency = $store_credit_row['sc_currency_id'];
			$from_currency_code = $currencies->get_code_by_id($current_store_credit_currency);
			
			// if current store credit currency same as profile currency, just return the store credit amount
			if ($from_currency_code == $new_currency_code) {
				if ($return_raw == true) {
					return $total_store_credit;
				} else {
					return number_format($total_store_credit, $currencies->currencies[$new_currency_code]['decimal_places'], $currencies->currencies[$new_currency_code]['decimal_point'], $currencies->currencies[$new_currency_code]['thousands_point']);
				}
			}
		} else { // customer currently does not have store credit account
			return '0.00';
		}
		
		if ($return_raw == true) {
			$exch_rate = $currencies->advance_currency_conversion_rate($from_currency_code, $new_currency_code, 'sell');
			return ($total_store_credit * $exch_rate);
		} else {
			$new_store_credit_value = $currencies->advance_currency_conversion($total_store_credit, $from_currency_code, $new_currency_code, true, 'sell');
			return $new_store_credit_value;
		}
	}
	
	function get_store_credits_in_new_currency($new_currency_code = DEFAULT_CURRENCY)
	{
		global $currencies;
	
		$new_store_credit_row = array(
										'sc_reverse' => 0,
										'sc_reverse_reserve_amt' => 0,
										'sc_irreverse' => 0,
										'sc_irreverse_reserve_amt' => 0
									);
		
		$store_credit_row = $this->get_current_credits_balance($this->user_id);
		if (count($store_credit_row) > 0) {
			$current_store_credit_currency = $store_credit_row['sc_currency_id'];
			$from_currency_code = $currencies->get_code_by_id($current_store_credit_currency);
			
			// if current store credit currency same as profile currency, just return the store credit amount
			if ($from_currency_code == $new_currency_code) {
				return $store_credit_row;
			}
			
			$new_store_credit_row['sc_reverse'] = $currencies->advance_currency_conversion($store_credit_row['sc_reverse'], $from_currency_code, $new_currency_code, true, 'buy');
			$new_store_credit_row['sc_reverse_reserve_amt'] = $currencies->advance_currency_conversion($store_credit_row['sc_reverse_reserve_amt'], $from_currency_code, $new_currency_code, true, 'buy');
			$new_store_credit_row['sc_irreverse'] = $currencies->advance_currency_conversion($store_credit_row['sc_irreverse'], $from_currency_code, $new_currency_code, true, 'buy');
			$new_store_credit_row['sc_irreverse_reserve_amt'] = $currencies->advance_currency_conversion($store_credit_row['sc_irreverse_reserve_amt'], $from_currency_code, $new_currency_code, true, 'buy');
		}
		
		return $new_store_credit_row;
	}
    
    function get_store_credit_conversion($sc_amount = 0, $sc_from_currency = DEFAULT_CURRENCY, $sc_to_currency = DEFAULT_CURRENCY, $type = 'sell') {
    	global $currencies;
    	
    	if (trim($sc_from_currency) == trim($sc_to_currency)) return $sc_amount;
    	
    	$return_value = $currencies->advance_currency_conversion($sc_amount, $sc_from_currency, $sc_to_currency, true, $type);
    	
    	return $return_value;
    }
	
	function get_customer_info($user_id)
	{
		$customer_sc_select_sql = "	SELECT sc_currency_id, sc_conversion_date 
									FROM " . TABLE_COUPON_GV_CUSTOMER . " 
									WHERE customer_id='" . tep_db_input($user_id) . "'";
		$customer_sc_result_sql = tep_db_query($customer_sc_select_sql);
		$customer_row = tep_db_fetch_array($customer_sc_result_sql);
		
		if (count($customer_row) > 0) {
			$customer_select_sql = "SELECT customers_id, customers_email_address 
									FROM " . TABLE_CUSTOMERS . " 
									WHERE customers_id = '" . tep_db_input($user_id) . "'";
			$customer_result_sql = tep_db_query($customer_select_sql);
			if ($customer_info_row = tep_db_fetch_array($customer_result_sql)) {
				$customer_row['customer_id'] = $customer_info_row['customers_id'];
				$customer_row['customers_email_address'] = $customer_info_row['customers_email_address'];
			}
		}
		
		return $customer_row;
	}
	
	function get_cpath_info() {
		global $customers_groups_id, $cPath_array;
		
		$store_credit_cPath = '';
		$store_credit_select_sql = "	SELECT DISTINCT c.categories_id, c.parent_id, c.categories_parent_path 
										FROM " . TABLE_CATEGORIES . " AS c 
										INNER JOIN " . TABLE_CATEGORIES_GROUPS . " AS cg 
											ON (c.categories_id = cg.categories_id) 
										WHERE c.categories_status = 1 
											AND c.custom_products_type_id = '3'
											AND ((cg.groups_id = '".$customers_groups_id. "') OR (cg.groups_id = 0)) 
										ORDER BY c.categories_id";
		$store_credit_result_sql = tep_db_query($store_credit_select_sql);
		if ($store_credit_row = tep_db_fetch_array($store_credit_result_sql)) {
			if ($store_credit_row['parent_id'] > 0) {
				$store_credit_cPath = substr($store_credit_row['categories_parent_path'], 1).$store_credit_row['categories_id'];
			} else {
				$store_credit_cPath = $store_credit_row['categories_id'];
			}
		}
		
		$cPath_array = tep_parse_category_path($store_credit_cPath);
		
		return $store_credit_cPath;
	}
	
	function get_minimum_store_credit($to_currency, $min_value = 10, $min_currency = 'USD', $round_to_nearest = 10) {
		global $currencies;
		
		if ($to_currency == $min_currency) {
			$new_min_value = $min_value;
		} else {
			$new_min_value = store_credit::get_store_credit_conversion($min_value, $min_currency, $to_currency, 'buy');
		}
		
		$new_min_value = ceil($new_min_value / $round_to_nearest) * $round_to_nearest;
		
		return $new_min_value;
	}
}
?>