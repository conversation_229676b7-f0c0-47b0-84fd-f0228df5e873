<?php
class store_point {
	var $user_id, $user_role, $op_amt;
	
	function store_point($user_id, $user_role) {
		$this->user_id = $user_id;
		$this->user_role = $user_role;
		$this->op_amt = (int)$this->_get_current_points_balance($user_id);
		$this->sp_currency = 'USD';
	}
	
	function redeem_store_point() {
		$this->op_amt = (int)$this->_get_current_points_balance($this->user_id);
		$insert_id = 0;
        
		if ($this->op_amt >= tep_get_configuration_key_value('OP_MIN_REDEMPTION')) {
			$currencies = new currencies();
			
			$code_select_sql = "	SELECT c.code 
									FROM " . TABLE_CURRENCIES . " AS c 
									INNER JOIN " . TABLE_COUPON_GV_CUSTOMER . " AS cgc 
										ON (c.currencies_id = cgc.sc_currency_id) 
									WHERE cgc.customer_id = '" . (int)$this->user_id . "'";
			$code_result_sql = tep_db_query($code_select_sql);
			if ($code_row = tep_db_fetch_array($code_result_sql)) {
				$cur_redeem = $code_row['code'];
			} else {
				//$cur_redeem = DEFAULT_CURRENCY;
				$cur_redeem = $this->_get_last_completed_order_currency($this->user_id);
                if (!isset($currencies->currencies[$cur_redeem])) {  // If the currency no longer supported
                    $cur_redeem = DEFAULT_CURRENCY;
                }
			}
			
			$exchange_rate = $currencies->advance_currency_conversion_rate($this->sp_currency, $cur_redeem);
			$remaining_sp = $this->op_amt % 100;
			$redeem_sp = $this->op_amt - $remaining_sp;
			$request_currency_amount = (($redeem_sp / 10000) * $exchange_rate);
			
			$customers_select_sql = "	SELECT c.customers_firstname, c.customers_lastname, c.customers_email_address, c.customers_telephone, c.customers_mobile, coun.countries_international_dialing_code 
										FROM " . TABLE_CUSTOMERS . " AS c 
										LEFT JOIN " . TABLE_COUNTRIES . " AS coun 
											ON (c.customers_country_dialing_code_id = coun.countries_id) 
										WHERE c.customers_id = '" . (int)$this->user_id . "'";
			$customers_result_sql = tep_db_query($customers_select_sql);
			if ($customers_row = tep_db_fetch_array($customers_result_sql)) {
                $success_redeem = false;
				/*************************************************************************
				 	Lock the TABLE_STORE_POINTS 
				 	REMEMBER: Need to lock all the tables involved in this block.
				*************************************************************************/
				tep_db_query("LOCK TABLES " . TABLE_STORE_POINTS . " WRITE;");
                
                $latest_op_amt = $this->_get_current_points_balance($this->user_id);
                // Verify if latest balance still remain unchange during locking status
                if ($this->op_amt == $latest_op_amt) {
                    $success_redeem = true;
                    
                    $sp_sql_data_array = array('sp_amount' => $remaining_sp);
                    tep_db_perform(TABLE_STORE_POINTS, $sp_sql_data_array, 'update', "customers_id = '" . (int)$this->user_id . "'");
                }
                
				tep_db_query("UNLOCK TABLES;");
                
                if ($success_redeem) {
                    $sql_data_array = array('user_id' => $this->user_id,
                                            'user_role' => $this->user_role,
                                            'user_firstname' => $customers_row['customers_firstname'],
                                            'user_lastname' => $customers_row['customers_lastname'],
                                            'user_email_address' => $customers_row['customers_email_address'],
                                            'user_country_international_dialing_code' => $customers_row['countries_international_dialing_code'],
                                            'user_telephone' => $customers_row['customers_telephone'],
                                            'user_mobile' => $customers_row['customers_mobile'],
                                            'store_points_redeem_date' => 'now()',
                                            'store_points_redeem_status' => 1,
                                            'store_points_redeem_amount' => $redeem_sp,
                                            'store_points_request_currency' => $cur_redeem,
                                            'store_points_request_currency_amount' => $request_currency_amount,
                                            'store_points_paid_currency' => $cur_redeem,
                                            'store_points_exchange_rate' => $exchange_rate,
                                            'store_points_redeem_last_modified' => 'now()'
                                            );

                    tep_db_perform(TABLE_STORE_POINTS_REDEEM, $sql_data_array);
                    $insert_id = tep_db_insert_id();
                    
                    $comments = sprintf(TEXT_REDEEMED_MSG, $redeem_sp . ' ' . TEXT_OFFGAMERS_POINTS, $currencies->format($request_currency_amount, false, $cur_redeem), $insert_id);

                    $sprh_sql_data_array = array(	'store_points_redeem_id' => $insert_id,
                                                    'store_points_redeem_status' => 1,
                                                    'date_added' => 'now()',
                                                    'payee_notified' => 1,
                                                    'comments' => $comments,
                                                    'changed_by' => $customers_row['customers_email_address'],
                                                    'changed_by_role' => 'customers'
                                                );
                    tep_db_perform(TABLE_STORE_POINTS_REDEEM_HISTORY, $sprh_sql_data_array);

                    $sph_sql_data_array = array('customer_id' => $this->user_id,
                                                'store_points_history_date' => 'now()',
                                                'store_points_history_debit_amount' => $redeem_sp,
                                                'store_points_history_after_balance' => $remaining_sp,
                                                'store_points_history_trans_type' => 'C',
                                                'store_points_history_trans_id' => $insert_id,
                                                'store_points_history_activity_type' => 'D',
                                                'store_points_history_activity_title' => 'Redemption ' . $insert_id,
                                                'store_points_history_activity_desc' => $comments,
                                                'store_points_history_activity_desc_show' => 1,
                                                'store_points_history_added_by' => $customers_row['customers_email_address'],
                                                'store_points_history_added_by_role' => 'customers'
                                                );

                    tep_db_perform(TABLE_STORE_POINTS_HISTORY, $sph_sql_data_array);

                    $this->op_amt = $this->_get_current_points_balance($this->user_id);
                }
			}
		}
		
		return $insert_id;
	}
	
	function _get_current_points_balance($user_id) {
		$sp_amount_select_sql = "	SELECT sp_amount 
									FROM " . TABLE_STORE_POINTS . " 
									WHERE customers_id = '" . (int)$user_id . "'";
		$sp_amount_result_sql = tep_db_query($sp_amount_select_sql);
		$sp_amount_row = tep_db_fetch_array($sp_amount_result_sql);
		
		return $sp_amount_row['sp_amount'];
	}
	
	function get_current_points_balance($user_id) {
		$sp_amount_select_sql = "	SELECT sp_amount 
									FROM " . TABLE_STORE_POINTS . " 
									WHERE customers_id = '" . (int)$user_id . "'";
		$sp_amount_result_sql = tep_db_query($sp_amount_select_sql);
		$sp_amount_row = tep_db_fetch_array($sp_amount_result_sql);
		
		return $sp_amount_row['sp_amount'];
	}
	
	function _get_last_completed_order_currency($user_id) {
		$sp_currency_to_credit = DEFAULT_CURRENCY;
		
		$order_select_sql = "	SELECT currency
								FROM " . TABLE_ORDERS . "
								WHERE orders_status = '3'
								AND customers_id='" . (int)$user_id . "'
								ORDER BY last_modified DESC
								LIMIT 1";
		$order_result_sql = tep_db_query($order_select_sql);
		if ($order_info = tep_db_fetch_array($order_result_sql)) {
			$sp_currency_to_credit = $order_info['currency'];
		}
		
		return $sp_currency_to_credit;
	}
	
	function show_redeem_button() {
		$currencies = new currencies();
		
		$code_select_sql = "	SELECT c.code 
								FROM " . TABLE_CURRENCIES . " AS c 
								INNER JOIN " . TABLE_COUPON_GV_CUSTOMER . " AS cgc 
									ON (c.currencies_id = cgc.sc_currency_id) 
								WHERE cgc.customer_id = '" . (int)$this->user_id . "'";
		$code_result_sql = tep_db_query($code_select_sql);
		if ($code_row = tep_db_fetch_array($code_result_sql)) {
			$cur_redeem = $code_row['code'];
		} else {
			$cur_redeem = $this->_get_last_completed_order_currency($this->user_id);
            if (!isset($currencies->currencies[$cur_redeem])) {  // If the currency no longer supported
                $cur_redeem = DEFAULT_CURRENCY;
            }
		}
		
		$exchange_rate = $currencies->advance_currency_conversion_rate($this->sp_currency, $cur_redeem, 'sell');
		
		$redeem_button_html = '	<div id="redeemBtn" class="green_button" style="float:right" onClick="redeemPoint()">
									<a href="javascript:void(0)">
										<span>
											<font>' . sprintf(BTN_REDEEM_POINT, $currencies->format((($this->op_amt - ($this->op_amt % 100)) / 10000) * $exchange_rate, false, $cur_redeem)) . '</font>
										</span>
									</a>
								</div>';
		
		return $redeem_button_html;
	}
}
?>