<?
/*
  	$Id: theme_manager.php,v 1.3 2009/07/21 05:31:56 weesiong Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2005 SKC Ventrue
	
  	Released under the GNU General Public License
*/

////
// Class to handle theme management
class theme_manager
{
	var $theme_id;
	var $language_code;
	var $parser; 
	var $node_stack = array(); 
	var $parent;
	
	function theme_manager($theme_id, $language_code) {
		$this->theme_id = $theme_id;
		$this->language_code = $language_code;
	}
	
	/********************************************************************
		PUBLIC 
		Parse a text string containing valid XML into a multidimensional 
		array located at rootnode. 
	********************************************************************/
	function parse($xmlContents='') { 
		if (!tep_not_null($xmlContents)) 	return;
		
		$this->parser = xml_parser_create(); 
		
		xml_set_object($this->parser, $this); // Use XML Parser within an object
		xml_parser_set_option($this->parser, XML_OPTION_CASE_FOLDING, false); // Conform to W3C specs
		xml_set_element_handler($this->parser, "startElement", "endElement"); 
		xml_set_character_data_handler($this->parser, "characterData"); 
		
		// Build a Root node and initialize the node_stack... 
		$this->node_stack = array(); 
		//$this->startElement(null, "root", array()); 
		// parse the data and free the parser... 
		if (!xml_parse($this->parser, $xmlContents, true)) {
			die(sprintf("XML error: %s at line %d",
				xml_error_string(xml_get_error_code($this->parser)),
				xml_get_current_line_number($this->parser)));		//get XML parser error string
		}
		xml_parser_free($this->parser);	// free XML parser
		
		// recover the root node from the node stack 
		$rnode = array_pop($this->node_stack);
		// return the root node... 
		return($rnode);
	}
	
	/********************************************************************
		PROTECTED 
		Start a new Element. This means we push the new element onto the 
		stack and reset it's properties.
	********************************************************************/
	function startElement($parser, $name, $attrs) {
		// create a new node... 
		$node = array(); 
		$node["_NAME"] = $name; 
		$lastnode = count($this->node_stack);
		
		if ($this->node_stack[$lastnode-1][_NAME] != "file") {
			$this->parent = $node["_PARENT"] = ($lastnode > 0 ? $this->node_stack[$lastnode-1][name] : 0);
		} else {
			$node["_PARENT"] = $this->parent;
		}
		
		foreach ($attrs as $key => $value) { 
			$node[$key] = $value; 
		}
		
		$node["_DATA"] = "";
		$node["_ELEMENTS"] = array();
		// add the new node to the end of the node stack 
		array_push($this->node_stack, $node);
	}
	
	/********************************************************************
		PROTECTED 
		End an element. This is done by popping the last element from the 
		stack and adding it to the previous element on the stack.
	********************************************************************/
	function endElement($parser, $name) { 
		// Note: "_ELEMENTS" must be the last element of the node_stack array 
		// since it will be poped at the end
		// pop this element off the node stack
		$node = array_pop($this->node_stack); 
		
		$node["_DATA"] = trim($node["_DATA"]); 
		// and add it an an element of the last node in the stack... 
		$lastnode = count($this->node_stack);
		
		if ($lastnode > 0)
			array_push($this->node_stack[$lastnode-1]["_ELEMENTS"], $node);
		else
			$this->node_stack = $node;
	}
	
	/********************************************************************
		PROTECTED 
		Collect the data onto the end of the current chars.
	********************************************************************/
	function characterData($parser, $data) {
		// add this data to the last node in the stack... 
		$lastnode = count($this->node_stack); 
		$this->node_stack[$lastnode-1]["_DATA"] .= $data; 
	}
	
	function load_actual_filenames() {
		require_once(DIR_WS_CLASSES . 'file_manager.php');
		$file_obj = new file_manager();
		
		$themeXMLFile = DIR_FS_ADMIN . 'theme/standard.xml';
		if ( ($themeXMLContents = @file_get_contents ($themeXMLFile)) != FALSE) {
			$them_structure = $this->parse($themeXMLContents);
			
			for ($i=0; $i < count($them_structure); $i++) {
				if ($them_structure[$i]['_NAME'] == 'folder') {
					if (count($them_structure[$i]['_ELEMENTS'])) {
						while(list($index, $eleArray) = each($them_structure[$i]['_ELEMENTS'])) {
							$fileTypes = explode(',', $eleArray['allowedtypes']);
							
							for ($fileType_cnt = 0; $fileType_cnt < count($fileTypes); $fileType_cnt++) { // added the language code by wee siong
								$found_filename = $file_obj->fileExists(DIR_FS_THEME . $this->theme_id . '/' . $them_structure[$i]['name'] . '/', $eleArray['name']. '_' . $this->language_code . '.' . $fileTypes[$fileType_cnt], false);
								
								if ($found_filename)	break;
							}
							
							if (tep_not_null($found_filename)) {
								define($eleArray['token'], $them_structure[$i]['name'] . '/' . $found_filename);
							}
						}
					}
				}
			}
		}
	}
}