<?php

require_once(DIR_WS_CLASSES . 'recaptcha.php');

class user {

    public function __construct() {

    }

    function loginProcess($data) {
        global $cart,
        $cookie_path, $cookie_domain, $customer_id, $customer_first_name, $customer_last_name, $customer_country_id;

        $customer_id = 0;
        $error = 0;
        $error_msg_array = array();
        $cookie_remember_me = false;
        $sso_status = false;

        if (isset($data['param']) && tep_not_null($data['param'])) {
            $param = tep_array_unserialize($data['param']);
            $catelog_domain = ((ENABLE_SSL_AFFILIATE == 'true') ? HTTPS_AFFILIATE_SERVER : HTTP_AFFILIATE_SERVER);

            if ($param['aff_domain'] == $catelog_domain) { //check correct domain
                $email_address = $param['aff_u'];
                $password = $param['aff_p'];
            }
        } else if (isset($_COOKIE['ogm']['st']) && tep_not_null($_COOKIE['ogm']['st']) && isset($_COOKIE['ogm']['si']) && tep_not_null($_COOKIE['ogm']['si']) && !tep_not_null($email_address) && !tep_not_null($password)) {
            # Single Sign-On
            $sso_status = oauth::getSSOTokenStatus($_COOKIE['ogm']['st'], $_COOKIE['ogm']['si']);

            if ($sso_status) {
                $customer_query = tep_db_query("SELECT customers_email_address
													FROM " . TABLE_CUSTOMERS . "
													WHERE customers_id = '" . tep_db_input($_COOKIE['ogm']['si']) . "'");
                if ($customer_row = tep_db_fetch_array($customer_query)) {
                    $email_address = $customer_row['customers_email_address'];
                    $authorized_access = TRUE;
                } else {
                    authenticate::logoff();
                }
            } else {
                authenticate::logoff();
            }
        } else if (isset($_COOKIE['ogm']) && tep_not_null($_COOKIE['ogm']['un']) && tep_not_null($_COOKIE['ogm']['uc']) && !tep_not_null($data['email_address']) && !tep_not_null($data['password'])) {
            $email_address = $_COOKIE['ogm']['un'];
            $password = $_COOKIE['ogm']['uc'];

            $cookie_remember_me = true;
        } else {
            if (tep_not_null($data['email_address']) && tep_not_null($data['password'])) {
                if (isset($data['enabled_captcha_check']) && $data['enabled_captcha_check']) {
                    $validate_ip_address = isset($data['ext_ip']) ? $data['ext_ip'] : tep_get_ip_address();
                    if (recaptcha::is_captcha_require($data['email_address'], $validate_ip_address)) {
                        if (!tep_not_null($data['recaptcha_challenge_field']) || !tep_not_null($data['recaptcha_response_field'])) {
                            $error = 1;
                            $error_msg_array['captcha'] = TEXT_CAPTCHA_MISSING_ERROR;
                        } else if (recaptcha::captcha_validation($data['recaptcha_challenge_field'], $data['recaptcha_response_field']) === false) {
                            $error = 1;
                            $error_msg_array['captcha'] = TEXT_CAPTCHA_ERROR;
                        }
                    }
                }

                if ($error === 0) {
                    $email_address = tep_db_prepare_input($data['email_address']);
                    $password = tep_db_prepare_input($data['password']);

                    if (tep_not_null($_COOKIE['ogm']['un']) && tep_not_null($_COOKIE['ogm']['uc'])) {
                        $expire = time() - 3600;
                        tep_setcookie('ogm[un]', '', $expire, $cookie_path, $cookie_domain);
                        tep_setcookie('ogm[uc]', '', $expire, $cookie_path, $cookie_domain);
                    }
                }
            } else {
                $error = 1;
            }
        }

        // Check if email exists
        if ($error === 0) {
            $check_customer_query = tep_db_query("select customers_id, customers_firstname, customers_lastname, account_activated, customers_password, customers_email_address, customers_default_address_id, customers_status, customers_groups_id, customers_login_sites, customers_dob,  customers_country_dialing_code_id, customers_telephone
                                                    from " . TABLE_CUSTOMERS . " where customers_status = '1'
                                                        and customers_email_address = '" . tep_db_input($email_address) . "'
                                                        and FIND_IN_SET( '0', customers_login_sites)");
            if (!tep_db_num_rows($check_customer_query)) {
                $error = 2;
            } else {
                $check_customer = tep_db_fetch_array($check_customer_query);

                if (isset($_SESSION['fb_uid'])) {
                    $fb_connect_select_sql = "	SELECT provider_uid
                                                FROM " . TABLE_CUSTOMERS_CONNECTION . "
                                                WHERE customers_id = '" . $check_customer['customers_id'] . "' AND provider='Facebook'";
                    $fb_connect_result_sql = tep_db_query($fb_connect_select_sql);
                    if ($fb_connect_row = tep_db_fetch_array($fb_connect_result_sql)) {
                        if ($fb_connect_row['provider_uid'] != $_SESSION['fb_uid']) {
                            $error = 2;
                        }
                    } else {
                        $error = 2;
                    }
                } else if ($cookie_remember_me == true) {
                    if (md5($check_customer['customers_email_address'] . ':' . $check_customer['customers_password']) != $password) {
                        $error = 2;

                        $expire = time() - 3600;
                        tep_setcookie('ogm[un]', '', $expire, $cookie_path, $cookie_domain);
                        tep_setcookie('ogm[uc]', '', $expire, $cookie_path, $cookie_domain);
                    }
                } else if (!tep_validate_password($password, $check_customer['customers_password'])) {
                    $error = 2;
                }

                if ($error === 0) {
                    if (SESSION_RECREATE == 'True') {
                        tep_session_recreate();
                    }

                    $check_country_query = tep_db_query("select entry_country_id, entry_zone_id from " . TABLE_ADDRESS_BOOK . " where customers_id = '" . (int) $check_customer['customers_id'] . "' and address_book_id = '" . (int) $check_customer['customers_default_address_id'] . "'");
                    $check_country = tep_db_fetch_array($check_country_query);

                    $_SESSION['customers_groups_id'] = $customers_groups_id = $check_customer['customers_groups_id'];

                    $customer_id = $check_customer['customers_id'];
                    $customer_first_name = $check_customer['customers_firstname'];
                    $customer_last_name = $check_customer['customers_lastname'];
                    $customer_country_id = $check_country['entry_country_id'];

                    $_SESSION['customer_default_address_id'] = $check_customer['customers_default_address_id'];
                    $_SESSION['customer_first_name'] = $check_customer['customers_firstname'];
                    $_SESSION['customer_last_name'] = $check_customer['customers_lastname'];
                    $_SESSION['customer_country_id'] = $check_country['entry_country_id'];
                    $_SESSION['customer_id'] = $customer_id;

                    $customer_zone_id = $check_country['entry_zone_id'];
                    $phone_country_id = $check_customer['customers_country_dialing_code_id'];
                    $telephone_number = $check_customer['customers_telephone'];

                    $buyback_vip_select_sql = "	SELECT vip_supplier_groups_id
                                                FROM " . TABLE_CUSTOMERS_VIP . "
                                                WHERE customers_id='" . $customer_id . "'";
                    $buyback_vip_result_sql = tep_db_query($buyback_vip_select_sql);
                    if ($buyback_vip_row = tep_db_fetch_array($buyback_vip_result_sql)) {
                        $vip_supplier_groups_id = $buyback_vip_row['vip_supplier_groups_id'];
                    } else {
                        $vip_supplier_groups_id = 1; //default set to member if success login
                    }

                    $_SESSION['vip_supplier_groups_id'] = $vip_supplier_groups_id;

                    // Check SSO token table before creating new one
                    $sso_token = '';
                    $sso_row = oauth::getValidSSOToken($customer_id);
                    if (!empty($sso_row)) {
                        $sso_token = $sso_row['sso_token'];
                        $sso_status = oauth::getSSOTokenStatus($sso_token, $customer_id);
                    }
                    if (!$sso_status) {
                        $sso_token = oauth::addSSOToken($customer_id);

                        $diff_day = 0;
                        $customer_last_logon_select_sql = " SELECT customers_info_date_of_last_logon, customers_info_number_of_logons
                                                            FROM " . TABLE_CUSTOMERS_INFO . "
                                                            WHERE customers_info_id='" . $customer_id . "'";
                        $customer_last_logon_result_sql = tep_db_query($customer_last_logon_select_sql);
                        if ($customer_last_logon_row = tep_db_fetch_array($customer_last_logon_result_sql)) {
                            $diff_seconds = time() - strtotime($customer_last_logon_row['customers_info_date_of_last_logon']);
                            $diff_day = $diff_seconds / 86400;
                        }

                        $customer_info_data_array = array('customers_info_date_of_last_logon' => 'now()',
                            'customers_info_number_of_logons' => $customer_last_logon_row['customers_info_number_of_logons'] + 1,
                            'customer_info_selected_country' => $_SESSION['country']
                        );

                        if ($diff_day > 90) { //No login for 90days will be set as dormant account
                            $customer_info_data_array['customer_info_account_dormant'] = 1;
                        }

                        tep_db_perform(TABLE_CUSTOMERS_INFO, $customer_info_data_array, 'update', 'customers_info_id = "' . $_SESSION['customer_id'] . '"');

                        $customer_login_sql_data_array = array('customers_id' => $customer_id,
                            'customers_login_date' => 'now()',
                            'customers_login_ua_info' => (isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : getenv('HTTP_USER_AGENT')),
                            'customers_login_ip' => tep_get_ip_address());
                        tep_db_perform(TABLE_CUSTOMERS_LOGIN_IP_HISTORY, $customer_login_sql_data_array);
                    }

                    // Remember Me feature
                    if (isset($data['remember_me']) || $cookie_remember_me) {
                        $expire = time() + 60 * 60 * 24 * 90; // 90 days expiry date;
                        tep_setcookie('ogm[un]', $email_address, $expire, $cookie_path, $cookie_domain);
                        tep_setcookie('ogm[uc]', md5($check_customer['customers_email_address'] . ':' . $check_customer['customers_password']), $expire, $cookie_path, $cookie_domain);
                    }
                    $cookieLifeTime = 0; //time() + 86400
                    tep_setcookie('ogm[si]', $customer_id, $cookieLifeTime, $cookie_path, $cookie_domain);
                    tep_setcookie('ogm[st]', $sso_token, $cookieLifeTime, $cookie_path, $cookie_domain);
                    $_SESSION['sso_token'] = $sso_token;

                    $_SESSION['customers_login_ip'] = $customer_login_sql_data_array['customers_login_ip'];
                    $_SESSION['customers_login_timestamp'] = time();
                    $_SESSION['login_trial'] = 0;
                    // restore cart contents
                    $cart->restore_contents();
//                    $_SESSION['RESET_PHONE_NO'] = 1;

                    update_lifetime_cookies();
                }
            }
        }

        if ($error !== 0) {
            $_SESSION['login_trial']++;

            if ($error === 2) {
                $error_msg_array['login'] = sprintf(TEXT_LOGIN_ERROR, '<a href="' . tep_href_link(FILENAME_LOGIN, 'action=forget_password', 'SSL') . '">' . TEXT_RESET_PASSWORD . '</a>');
            }
        }

        return array('error' => ($error !== 0), 'error_info' => $error_msg_array, 'customer_id' => $customer_id);
    }

    function createAccountProcess($data, $login_status = FALSE) {
        global $ogm_fb_obj, $customers_upkeep_obj, $customers_security_obj, $currencies, $cart,
        $country, $languages_id;

        $customer_id = 0;
        $sql_customers_arr = array();
        $ask_for_ads_source = false;
        $error = false;
        $error_msg_array = array();

        $email_address = isset($data['account_email_address']) ? tep_db_prepare_input(strip_tags($data['account_email_address'])) : NULL;
        $password = isset($data['password']) ? tep_db_prepare_input(strip_tags($data['password'])) : NULL;
        $password_confirmation = isset($data['password_confirmation']) ? tep_db_prepare_input(strip_tags($data['password_confirmation'])) : NULL;
        $firstname = isset($data['firstname']) ? tep_db_prepare_input(strip_tags($data['firstname'])) : '';
        $lastname = isset($data['lastname']) ? tep_db_prepare_input(strip_tags($data['lastname'])) : '';
        $phone_country = isset($data['country']) ? tep_db_prepare_input(strip_tags($data['country'])) : NULL;
        $contactnumber = isset($data['contactnumber']) ? tep_parse_telephone(tep_db_prepare_input(preg_replace('/[^\d]/', '', $data['contactnumber'])), $phone_country, 'id') : NULL;
        $gender = isset($data['gender']) ? tep_db_prepare_input($data['gender']) : '';
        $newsletter_array = isset($data['newsletter']) ? tep_db_prepare_input($data['newsletter']) : NULL;
        $request_to_reset_password = isset($data['request_reset_password']) ? (int) $data['request_reset_password'] : 0;

        $dob = '';
        $dob_year = isset($data['dob_year']) ? tep_db_prepare_input(strip_tags($data['dob_year'])) : NULL;
        $dob_month = isset($data['dob_month']) ? tep_db_prepare_input(strip_tags($data['dob_month'])) : NULL;
        $dob_day = isset($data['dob_day']) ? tep_db_prepare_input(strip_tags($data['dob_day'])) : NULL;

        $agreed = isset($data['agreed']) ? tep_db_prepare_input($data['agreed']) : NULL;

        if ($ask_for_ads_source) {
            $survey_answer = (isset($data['hidden_survey']) && tep_not_null($data['hidden_survey']) ? tep_db_prepare_input(JS_SURVEY_QUESTION . " : " . strip_tags($data['hidden_survey'])) : "");
        }

        // Email error message
        if (!tep_not_null($email_address)) {
            $error = true;
            $error_msg_array[] = ENTRY_EMAIL_ADDRESS_ERROR;
        } else if (strlen($email_address) < ENTRY_EMAIL_ADDRESS_MIN_LENGTH) {
            $error = true;
            $error_msg_array[] = ENTRY_EMAIL_ADDRESS_ERROR;
        } else if (tep_validate_email($email_address) == false) {
            $error = true;
            $error_msg_array[] = ENTRY_EMAIL_ADDRESS_CHECK_ERROR;
        } else {
            $check_email_query = tep_db_query("select customers_id from " . TABLE_CUSTOMERS . " where customers_email_address = '" . tep_db_input($email_address) . "'");
            $check_email = tep_db_num_rows($check_email_query);

            if ($check_email > 0) {
                $error = true;
                $error_msg_array[] = ENTRY_EMAIL_ADDRESS_ERROR_EXISTS;
            } else {
                $sql_customers_arr['customers_email_address'] = $email_address;
            }
        }

        // Password & Confirm Password error messages
        if (strlen($password) < ENTRY_PASSWORD_MIN_LENGTH) {
            $error = true;
            $error_msg_array[] = ENTRY_PASSWORD_ERROR;
        } else {
            $user_password = tep_encrypt_password($password);
            $sql_customers_arr['customers_password'] = $user_password;
        }

        // Mantis # 0000024 @ 200809261152 - Confirm Password error message
        if (!is_null($password_confirmation)) {
            if (strlen($password_confirmation) < ENTRY_PASSWORD_MIN_LENGTH) {
                $error = true;
                $error_msg_array[] = printf(ENTRY_PASSWORD_CONFIRMATION_ERROR, ENTRY_PASSWORD_MIN_LENGTH);
            } else if ($password != $password_confirmation) {
                $error = true;
                $error_msg_array[] = ENTRY_PASSWORD_ERROR_NOT_MATCHING;
            }
        }

        // First Name error message
        if (strlen($firstname) < ENTRY_FIRST_NAME_MIN_LENGTH) {
            $error = true;
            $error_msg_array[] = ENTRY_FIRST_NAME_ERROR;
        } else {
            $sql_customers_arr['customers_firstname'] = $firstname;
        }

        // Last Name error message
        if (strlen($lastname) < ENTRY_LAST_NAME_MIN_LENGTH) {
            $error = true;
            $error_msg_array[] = ENTRY_LAST_NAME_ERROR;
        } else {
            $sql_customers_arr['customers_lastname'] = $lastname;
        }

        // Country error message
        if (!is_null($phone_country)) {
            if (is_numeric($phone_country) == false) {
                $error = true;
                $error_msg_array[] = ENTRY_COUNTRY_ERROR;
            } else {
                $sql_customers_arr['customers_country_dialing_code_id'] = $phone_country;
            }
        }

        // Mantis # 0000024 @ 200809261212 - Contact Number error message
        if (!is_null($contactnumber)) {
            if (strlen($contactnumber) < ENTRY_TELEPHONE_MIN_LENGTH) {
                $error = true;
                $error_msg_array[] = sprintf(ENTRY_CONTACT_NUMBER_ERROR, ENTRY_TELEPHONE_MIN_LENGTH);
            } else if (tep_is_mobile_num_exist($contactnumber, $phone_country)) {
                $error = true;
                $error_msg_array[] = ENTRY_CONTACT_NUMBER_EXIST_ERROR;
            } else {
                $sql_customers_arr['customers_telephone'] = $contactnumber;
            }
        }

        // Subscribe to newsletter
        if (!is_null($newsletter_array) && count($newsletter_array)) {
            $newsletter = implode(',', $newsletter_array);
        } else {
            $newsletter = '0';
        }

        // Check customer if agreed to the terms & policy @ 200811061006
        if (!is_null($agreed) && !tep_not_null($agreed)) {
            $error = true;
            $error_msg_array[] = ENTRY_SIGNUP_TERMSANDCONDITION_ERROR;
        }

        if (tep_not_null($dob_year) && tep_not_null($dob_month) && tep_not_null($dob_day)) {
            $dob = tep_db_prepare_input($dob_year . '-' . $dob_month . '-' . $dob_day);
        }

        // Mantis # 0000024 @ 200810031635 - DOB fields checking
        if (tep_not_null($dob_year) || tep_not_null($dob_month) || tep_not_null($dob_day)) {
            $dob_entered = 1;

            $day = $dob_day;
            $month = $dob_month;
            $year = $dob_year;

            $message_1 = ENTRY_DATE_OF_BIRTH_ERROR_1;
            $message_2 = ENTRY_DATE_OF_BIRTH_ERROR_2;
            $message_3 = ENTRY_DATE_OF_BIRTH_ERROR_3;
            $message_4 = ENTRY_DATE_OF_BIRTH_ERROR_4;

            $day_month_message_1 = "";
            $day_month_message_2 = "";
            $count = 0;
            $month_day_message = 0;
            $year_message = "";

            if (!tep_not_null($month)) {
                $day_month_message_2 = $day_month_message_2 . " month";
                $message_1 = $message_1 . " month";
                $count = $count + 1;
                $month_day_message = 1;
            }

            if (!tep_not_null($day)) {
                $month_day_message = 1;
                if ($count == 0) {
                    $count = $count + 1;
                    $day_month_message_2 = $day_month_message_2 . " day";
                    $message_1 = $message_1 . " day";
                } else if ($count == 1) {
                    $count = $count + 1;
                    $day_month_message_2 = $day_month_message_2 . " and day";
                    $message_1 = $message_1 . " and day";
                }
            }

            if (!tep_not_null($year)) {
                $count++;
                $year_message = $message_4;
            }

            if ($count == 0) {
                $message_1 = '';
                $message_2 = '';
                $message_3 = '';
            }

            if ($month_day_message < 1) {
                $message_1 = '';
                $message_2 = '';
                $message_3 = '';
            }

            if ($count > 0) {
                $error_message = $error_message . $message_1 . $day_month_message_1 . $message_2 . $day_month_message_2 . $message_3 . $year_message . "\n";
                $error = true;
                $dob_entered = 0;
                $error_msg_array[] = $error_message;
            }

            if ($dob_entered == 1) {
                if (!checkdate(substr($dob, 5, 2), substr($dob, 8, 2), substr($dob, 0, 4))) {
                    $error = true;
                    $error_msg_array[] = ENTRY_DATE_OF_BIRTH_ERROR;
                }
            }

            if (tep_day_diff(date("Y-m-d H:i:s"), $year . '-' . $month . '-' . $day . ' 00:00:00', 'year') > 0) {
                $error = true;
                $error_msg_array[] = ENTRY_DATE_OF_BIRTH_FUTURE_ERROR;
            }

            if (tep_day_diff($year . '-' . $month . '-' . $day . ' 00:00:00', date("Y-m-d H:i:s"), 'year') > ENTRY_AGE_MAX) {
                $error = true;
                $error_msg_array[] = ENTRY_DATE_OF_BIRTH_OVER_ERROR;
            }
        }

        $zone_id = 0;
        $billingaddress1 = isset($data['billingaddress1']) ? tep_db_prepare_input(strip_tags($data['billingaddress1'])) : '';
        $billingaddress2 = isset($data['billingaddress2']) ? tep_db_prepare_input(strip_tags($data['billingaddress2'])) : '';
        $billingcity = isset($data['billingcity']) ? tep_db_prepare_input(strip_tags($data['billingcity'])) : '';
        $billingpostcode = isset($data['billingpostcode']) ? tep_db_prepare_input(strip_tags($data['billingpostcode'])) : '';
        $state = isset($data['state']) ? tep_db_prepare_input(strip_tags($data['state'])) : '';
        $billingcountry = isset($data['billingcountry']) ? tep_db_prepare_input($data['billingcountry']) : '';

        // Mantis # 0000024 @ 200810031535 - Billing address fields verification
        if (isset($data['billingaddress1']) && tep_not_null($data['billingaddress1'])) {
//            $address_update = true;

            if (strlen($billingaddress1) < ENTRY_STREET_ADDRESS_MIN_LENGTH) {
                $error = true;
                $error_msg_array[] = ENTRY_STREET_ADDRESS_ERROR;
            }

            if (strlen($billingcity) < ENTRY_CITY_MIN_LENGTH) {
                $error = true;
                $error_msg_array[] = ENTRY_CITY_ERROR;
            }

            if (strlen($billingpostcode) < ENTRY_POSTCODE_MIN_LENGTH) {
                $error = true;
                $error_msg_array[] = ENTRY_POST_CODE_ERROR;
            }

            if (!is_numeric($billingcountry) && tep_not_null($billingaddress1)) {
                $error = true;
                $error_msg_array[] = ENTRY_COUNTRY_ERROR;
            }

            $check_query = tep_db_query("select zone_country_id from " . TABLE_ZONES . " where zone_country_id = '" . (int) $billingcountry . "'");
            $entry_state_has_zones = tep_db_num_rows($check_query);

            if ($entry_state_has_zones > 0) {
                $zone_query = tep_db_query("SELECT zone_id FROM " . TABLE_ZONES . " WHERE zone_country_id = '" . (int) $billingcountry . "' AND zone_id = '" . (int) $state . "'");
                if (tep_db_num_rows($zone_query) == 1) {
                    $zone = tep_db_fetch_array($zone_query);
                    $zone_id = $zone['zone_id'];
                } else {
                    $error = true;
                    $error_msg_array[] = ENTRY_STATE_ERROR_SELECT;
                }
            } else {
                if (strlen($state) < ENTRY_STATE_MIN_LENGTH && tep_not_null($billingcountry) && tep_not_null($billingaddress1)) {
                    $error = true;
                    $error_msg_array[] = ENTRY_STATE_ERROR;
                }
            }
        }

        if ($error == false) {
            // M#0000075 @ 200909041155 - Viral Inviter join member process
            $recid = tep_not_null($data['rec']) ? tep_db_prepare_input($data['rec']) : '';
            $random_code = tep_not_null($data['code']) ? tep_db_prepare_input($data['code']) : '';

            if (tep_not_null($random_code)) {
                $get_invitee_email_sql = "	SELECT inviter_imports_contact_email
                                            FROM " . TABLE_INVITER_IMPORTS . "
                                            WHERE inviter_imports_id='" . (int) $recid . "'
                                                AND inviter_imports_code='" . tep_db_input($random_code) . "' ";
                $get_invitee_email_result_sql = tep_db_query($get_invitee_email_sql);

                if (tep_db_num_rows($get_invitee_email_result_sql) == 1) {
                    $inv_row = tep_db_fetch_array($get_invitee_email_result_sql);
                    if ($email_address == $inv_row['inviter_imports_contact_email']) {
                        $joined_updated = tep_inviter_joined($recid, $random_code, $email_address);
                    }
                }
            }
            // M#0000075

            $sql_customers_arr['customers_newsletter'] = $newsletter;
            $sql_customers_arr['account_activated'] = 0;
            $sql_customers_arr['customers_gender'] = $gender;
            $sql_customers_arr['customers_dob'] = $dob;
            $sql_customers_arr['customers_security_start_time'] = '0000-00-00';
            tep_db_perform(TABLE_CUSTOMERS, $sql_customers_arr);
            $customer_id = tep_db_insert_id();

            if ($ask_for_ads_source && tep_not_null($survey_answer)) {
                $sql_data_array = array(
                    'customers_id' => $customer_id,
                    'date_remarks_added' => 'now()',
                    'remarks' => $survey_answer,
                    'remarks_added_by' => $email_address
                );
                tep_db_perform(TABLE_CUSTOMERS_REMARKS_HISTORY, $sql_data_array);
            }

            // Mantis # 0000024 @ ************ - Instant Message accounts
            $instantmessageaccounttype = isset($data['instantmessageaccounttype']) && is_array($data['instantmessageaccounttype']) ? $data['instantmessageaccounttype'] : array();
            $instantmessageaccount = isset($data['instantmessageaccount']) && is_array($data['instantmessageaccount']) ? $data['instantmessageaccount'] : array();
            $othersinstantmessageaccounttype = isset($data['othersinstantmessageaccounttype']) && is_array($data['othersinstantmessageaccounttype']) ? $data['othersinstantmessageaccounttype'] : array();
            $othersinstantmessageaccountname = isset($data['othersinstantmessageaccountname']) && is_array($data['othersinstantmessageaccountname']) ? $data['othersinstantmessageaccountname'] : array();
            $othersinstantmessageaccount = isset($data['othersinstantmessageaccount']) && is_array($data['othersinstantmessageaccount']) ? $data['othersinstantmessageaccount'] : array();

            if (sizeof($instantmessageaccounttype) > 0) {
                for ($im_counter = 0; $im_counter < count($instantmessageaccounttype); $im_counter++) {
                    if (isset($instantmessageaccount[$im_counter])) {
                        $sql_data_array = array('customer_id' => $customer_id,
                            'instant_message_type_id' => $instantmessageaccounttype[$im_counter],
                            'instant_message_userid' => $instantmessageaccount[$im_counter]);
                        tep_db_perform(TABLE_INSTANT_MESSAGE_ACCOUNTS, $sql_data_array);
                    }
                }
            }

            // Mantis # 0000024 @ ************ - Others Instant Message type
            if (sizeof($othersinstantmessageaccounttype) > 0) {
                for ($others = 0; $others < count($othersinstantmessageaccounttype); $others++) {
                    if (isset($othersinstantmessageaccount[$others]) && isset($othersinstantmessageaccountname[$others])) {
                        $sql_data_array = array('customer_id' => $customer_id,
                            'instant_message_type_id' => $othersinstantmessageaccounttype[$others],
                            'instant_message_userid' => $othersinstantmessageaccount[$others],
                            'instant_message_remarks' => $othersinstantmessageaccountname[$others]);

                        tep_db_perform(TABLE_INSTANT_MESSAGE_ACCOUNTS, $sql_data_array);
                    }
                }
            }

            // Mantis # 0000024 @ ************ - Billing address
            if ($zone_id > 0) {
                $entry_state = '';
            } else {
                $zone_id = 0;
                $entry_state = $state;
            }

            $sql_data_array = array('customers_id' => $customer_id,
                'entry_firstname' => $firstname,
                'entry_lastname' => $lastname,
                'entry_gender' => $gender,
                'entry_street_address' => $billingaddress1,
                'entry_suburb' => $billingaddress2,
                'entry_city' => $billingcity,
                'entry_postcode' => $billingpostcode,
                'entry_country_id' => $billingcountry,
                'entry_state' => $entry_state,
                'entry_zone_id' => $zone_id);
            tep_db_perform(TABLE_ADDRESS_BOOK, $sql_data_array);
            $address_id = tep_db_insert_id();

            // verify email address
            $serial_number_generated = tep_gen_random_serial($email_address, TABLE_CUSTOMERS_INFO_VERIFICATION, 'serial_number', '', 12);
            $sql_data_array = array('customers_id' => $customer_id,
                'customers_info_value' => $email_address,
                'serial_number' => $serial_number_generated,
                'info_verified' => 0,
                'info_verification_type' => "email");
            tep_db_perform(TABLE_CUSTOMERS_INFO_VERIFICATION, $sql_data_array);

            tep_db_query("update " . TABLE_CUSTOMERS . " set customers_default_address_id = '" . (int) $address_id . "' where customers_id = '" . (int) $customer_id . "'");

            // Capture customer account creation IP
            $customer_info_data_array = array('customers_info_id' => $customer_id,
                'customers_info_number_of_logons' => 1,
                'customers_info_date_account_created' => 'now()',
                'customers_info_account_created_ip' => tep_get_ip_address(),
                'customers_info_account_created_from' => (int) SITE_ID,
                'customer_info_selected_country' => $country,
                'customer_info_selected_language_id' => $languages_id,
                'customers_info_date_of_last_logon' => 'now()'
            );
            tep_db_perform(TABLE_CUSTOMERS_INFO, $customer_info_data_array);

            // Set some of the system request
            if ($request_to_reset_password) {
                $reset_flag = $login_status ? 2 : 1;    // 2: reset without need to verify customer password.
                $customers_upkeep_obj->setCustomersId($customer_id);
                $customers_upkeep_obj->mergeUpkeep(array('reset_password' => $reset_flag));
                $customers_upkeep_obj->createUpkeep();
            }

            // build the message content
            $name = $firstname . ' ' . $lastname;

            $email_text .= tep_get_email_greeting($firstname, $lastname, $gender);

            $activate_address = tep_href_link(FILENAME_ACCOUNT_ACTIVATE, 'serial=' . $serial_number_generated . '&email=' . $email_address . '&action=verify_email');
            $link = '<a href="' . $activate_address . '" target="_blank">' . $activate_address . '</a><br>';

            $email_text .= EMAIL_VERIFY_CONTENT . $link . EMAIL_VERIFY_CONTENT_ADDRESS_INFO . EMAIL_MANUAL_ACTIVATE_EMAIL_2 . $email_address . EMAIL_MANUAL_ACTIVATE_CODE_2 . $serial_number_generated . "\n\n" . EMAIL_CONTACT . EMAIL_FOOTER;

            // ICW - CREDIT CLASS CODE BLOCK ADDED  ******************************************************* BEGIN
            if (NEW_SIGNUP_GIFT_VOUCHER_AMOUNT > 0) {
                $coupon_code = create_coupon_code();
                $insert_query = tep_db_query("insert into " . TABLE_COUPONS . " (coupon_code, coupon_type, coupon_amount, date_created) values ('" . $coupon_code . "', 'G', '" . NEW_SIGNUP_GIFT_VOUCHER_AMOUNT . "', now())");
                $insert_id = tep_db_insert_id($insert_query);
                $insert_query = tep_db_query("insert into " . TABLE_COUPON_EMAIL_TRACK . " (coupon_id, customer_id_sent, sent_firstname, emailed_to, date_sent) values ('" . $insert_id . "', '0', 'Admin', '" . $email_address . "', now() )");

                $email_text .= sprintf(EMAIL_GV_INCENTIVE_HEADER, $currencies->format(NEW_SIGNUP_GIFT_VOUCHER_AMOUNT)) . "\n\n" .
                        sprintf(EMAIL_GV_REDEEM, $coupon_code) . "\n\n" .
                        EMAIL_GV_LINK . tep_href_link(FILENAME_GV_REDEEM, 'gv_no=' . $coupon_code) .
                        "\n\n";
            }

            if (NEW_SIGNUP_DISCOUNT_COUPON != '') {
                $coupon_id = NEW_SIGNUP_DISCOUNT_COUPON;
                $coupon_query = tep_db_query("select * from " . TABLE_COUPONS . " where coupon_id = '" . $coupon_id . "'");
                $coupon_desc_query = tep_db_query("select * from " . TABLE_COUPONS_DESCRIPTION . " where coupon_id = '" . $coupon_id . "' and language_id = '" . languages_id . "'");
                $coupon = tep_db_fetch_array($coupon_query);
                $coupon_desc = tep_db_fetch_array($coupon_desc_query);
                $insert_query = tep_db_query("insert into " . TABLE_COUPON_EMAIL_TRACK . " (coupon_id, customer_id_sent, sent_firstname, emailed_to, date_sent) values ('" . $coupon_id . "', '0', 'Admin', '" . $email_address . "', now() )");
                $email_text .= EMAIL_COUPON_INCENTIVE_HEADER . "\n\n" .
                        $coupon_desc['coupon_description'] .
                        sprintf(EMAIL_COUPON_REDEEM, $coupon['coupon_code']) . "\n\n" .
                        "\n\n";
            }

            // ICW - CREDIT CLASS CODE BLOCK ADDED  ******************************************************* END
            @tep_mail($name, $email_address, implode(' ', array(EMAIL_SUBJECT_PREFIX, EMAIL_SUBJECT_2)), $email_text, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
        }

        return array(
            'error' => $error,
            'error_info' => $error_msg_array,
            'customer_id' => $customer_id,
            'firstname' => $firstname,
            'lastname' => $lastname,
            'gender' => $gender
        );
    }

    function editAccount($data) {
        $customer_id = $_SESSION['customer_id'];

        $error = FALSE;
        $error_array = array();
        $first_name = isset($data['first_name']) ? tep_db_prepare_input(strip_tags($data['first_name'])) : '';
        $last_name = isset($data['last_name']) ? tep_db_prepare_input(strip_tags($data['last_name'])) : '';
        $billing_address1 = isset($data['billing_address1']) ? tep_db_prepare_input(strip_tags($data['billing_address1'])) : '';
        $billing_address2 = isset($data['billing_address2']) ? tep_db_prepare_input(strip_tags($data['billing_address2'])) : '';
        $billing_city = isset($data['billing_city']) ? tep_db_prepare_input(strip_tags($data['billing_city'])) : '';
        $billing_state = isset($data['billing_state']) ? tep_db_prepare_input(strip_tags($data['billing_state'])) : '';
        $billing_zip = isset($data['billing_zip']) ? tep_db_prepare_input(strip_tags($data['billing_zip'])) : '';
        $billing_country = isset($data['billing_country']) ? tep_db_prepare_input(strip_tags($data['billing_country'])) : '';
        $country_code = isset($data['country_code']) ? (int) $data['country_code'] : '';
        $mobile_phone_number = isset($data['mobile_phone_number']) ? tep_db_prepare_input(strip_tags($data['mobile_phone_number'])) : '';

        $billing_zone_id = 0;
        $update_mobile_number = FALSE;
        $customers_default_address_id = 0;
        $all_customers_info_changes_made = '';

        $customer_select_sql = "	SELECT c.customers_firstname, c.customers_lastname, c.customers_email_address, c.customers_login_sites,
                                           c.customers_country_dialing_code_id, c.customers_telephone,
                                           c.customers_default_address_id,
                                           ci.customers_info_changes_made
                                    FROM " . TABLE_CUSTOMERS . " AS c
                                    INNER JOIN " . TABLE_CUSTOMERS_INFO . " AS ci
                                        ON (c.customers_id = ci.customers_info_id)
                                    WHERE c.customers_id = '" . (int) $customer_id . "'";
        $customer_result_sql = tep_db_query($customer_select_sql);
        if ($customer_row = tep_db_fetch_array($customer_result_sql)) {
            $customers_default_address_id = $customer_row['customers_default_address_id'];
            $all_customers_info_changes_made = $customer_row['customers_info_changes_made'];

            if (!tep_not_empty($customer_row['customers_telephone'])) {
                $update_mobile_number = TRUE;
            } else {
                $country_code = $customer_row['customers_country_dialing_code_id'];
                $mobile_phone_number = $customer_row['customers_telephone'];
            }
        }

        if (strlen($first_name) < ENTRY_FIRST_NAME_MIN_LENGTH) {
            $error = TRUE;
            $error_array[] = ENTRY_FIRST_NAME_ERROR;
        }

        if (strlen($last_name) < ENTRY_LAST_NAME_MIN_LENGTH) {
            $error = TRUE;
            $error_array[] = ENTRY_LAST_NAME_ERROR;
        }

        if (!tep_not_null($billing_address1) || (tep_not_null($billing_address1) && strlen($billing_address1) < ENTRY_STREET_ADDRESS_MIN_LENGTH)) {
            $error = TRUE;
            $error_array[] = ENTRY_STREET_ADDRESS_ERROR;
        }

        if (!tep_not_null($billing_zip) || (tep_not_null($billing_zip) && strlen($billing_zip) < ENTRY_POSTCODE_MIN_LENGTH)) {
            $error = TRUE;
            $error_array[] = ENTRY_POST_CODE_ERROR;
        }

        if (!tep_not_null($billing_city) || (tep_not_null($billing_city) && strlen($billing_city) < ENTRY_CITY_MIN_LENGTH)) {
            $error = TRUE;
            $error_array[] = ENTRY_CITY_ERROR;
        }

        if (!tep_not_null($billing_country) || (tep_not_null($billing_country) && !is_numeric($billing_country))) {
            $error = TRUE;
            $error_array[] = ENTRY_COUNTRY_ERROR;
        } else {
            $check_query = tep_db_query("SELECT zone_country_id FROM " . TABLE_ZONES . " WHERE zone_country_id = '" . (int) $billing_country . "'");
            $entry_state_has_zones = tep_db_num_rows($check_query);

            if ($entry_state_has_zones > 0) {
                $zone_query = tep_db_query("SELECT zone_id FROM " . TABLE_ZONES . " WHERE zone_country_id = '" . (int) $billing_country . "' AND zone_id = '" . (int) $billing_state . "'");
                if (tep_db_num_rows($zone_query) == 1) {
                    $zone = tep_db_fetch_array($zone_query);
                    $billing_zone_id = $zone['zone_id'];
                    $billing_state = '';
                } else {
                    $error = TRUE;
                    $error_array[] = ENTRY_STATE_ERROR_SELECT;
                }
            } else {
                if (!tep_not_null($billing_state) || (tep_not_null($billing_state) && strlen($billing_state) < ENTRY_STATE_MIN_LENGTH)) {
                    $error = TRUE;
                    $error_array[] = ENTRY_STATE_ERROR;
                }
            }
        }

        // customer contact number is empty
        if ($update_mobile_number === TRUE) {
            if (is_numeric($country_code) == false || $country_code < 1) {
                $error = TRUE;
                $error_array[] = ENTRY_LOCATION_ERROR;
            }

            $mobile_phone_number = tep_parse_telephone(tep_db_prepare_input(preg_replace('/[^\d]/', '', $mobile_phone_number)), $country_code, 'id');

            if (strlen($mobile_phone_number) < ENTRY_TELEPHONE_MIN_LENGTH) {
                $error = TRUE;
                $error_array[] = ENTRY_TELEPHONE_NUMBER_ERROR;
            } else if (tep_is_mobile_num_exist($mobile_phone_number, $country_code, $customer_id)) {
                $error = TRUE;
                $error_array[] = ENTRY_CONTACT_NUMBER_EXIST_ERROR;
            }
        }

        if ($error !== TRUE) {
            require_once(DIR_WS_CLASSES . 'log.php');

            // Update Name
            $sql_data_array = array(
                'customers_firstname' => $first_name,
                'customers_lastname' => $last_name
            );

            if ($update_mobile_number === TRUE) {
                $sql_data_array['customers_country_dialing_code_id'] = $country_code;
                $sql_data_array['customers_telephone'] = $mobile_phone_number;
            }

            tep_db_perform(TABLE_CUSTOMERS, $sql_data_array, 'update', "customers_id = '" . (int) $customer_id . "'");

            tep_db_query("update " . TABLE_CUSTOMERS_INFO . " set customers_info_date_account_last_modified = now() where customers_info_id = '" . (int) $customer_id . "'");

            $customer_log_result_sql = tep_db_query($customer_select_sql);
            $customer_new_log_row = tep_db_fetch_array($customer_log_result_sql);

            $log_object = new log_files($customer_id);
            $customer_changes_array = $log_object->detect_changes($customer_row, $customer_new_log_row);
            $customer_changes_formatted_array = $log_object->construct_log_message($customer_changes_array);

            $sql_address_data_array = array(
                'entry_firstname' => $first_name,
                'entry_lastname' => $last_name,
                'entry_street_address' => $billing_address1,
                'entry_suburb' => $billing_address2,
                'entry_zone_id' => $billing_zone_id,
                'entry_state' => $billing_state,
                'entry_postcode' => $billing_zip,
                'entry_city' => $billing_city,
                'entry_country_id' => $billing_country
            );

            $customer_address_log_select_sql = "	SELECT entry_street_address, entry_suburb, entry_postcode, entry_city, entry_state, entry_country_id, entry_zone_id
                                                    FROM " . TABLE_ADDRESS_BOOK . "
                                                    WHERE address_book_id = '" . $customers_default_address_id . "'";
            $customer_address_old_log_result_sql = tep_db_query($customer_address_log_select_sql);
            if ($customer_address_old_log_row = tep_db_fetch_array($customer_address_old_log_result_sql)) { // If found address
                if ((int) $customer_address_old_log_row["entry_zone_id"] > 0) {
                    $customer_address_old_log_row["entry_state"] = tep_get_zone_name((int) $customer_address_old_log_row["entry_country_id"], (int) $customer_address_old_log_row["entry_zone_id"], '');
                    unset($customer_address_old_log_row["entry_zone_id"]);
                }

                tep_db_perform(TABLE_ADDRESS_BOOK, $sql_address_data_array, 'update', "address_book_id = '" . $customers_default_address_id . "' AND customers_id ='" . $customer_id . "'");
            } else {
                // This customer has no address yet, insert new address
                $sql_address_data_array['customers_id'] = $customer_id;

                tep_db_perform(TABLE_ADDRESS_BOOK, $sql_address_data_array);
                $customer_new_address_id = tep_db_insert_id();

                $customer_address_data_array = array('customers_default_address_id' => $customer_new_address_id);
                tep_db_perform(TABLE_CUSTOMERS, $customer_address_data_array, 'update', "customers_id = '" . (int) $customer_id . "'");

                $_SESSION['customer_default_address_id'] = $customer_new_address_id;
            }

            $customer_address_new_log_result_sql = tep_db_query($customer_address_log_select_sql);
            $customer_address_new_log_row = tep_db_fetch_array($customer_address_new_log_result_sql);

            if ((int) $customer_address_new_log_row["entry_zone_id"] > 0) {
                $customer_address_new_log_row["entry_state"] = tep_get_zone_name((int) $customer_address_new_log_row["entry_country_id"], (int) $customer_address_new_log_row["entry_zone_id"], '');
                unset($customer_address_new_log_row["entry_zone_id"]);
            }


            $customer_address_changes_array = $log_object->detect_changes($customer_address_old_log_row, $customer_address_new_log_row);
            $customer_address_changes_formatted_array = $log_object->construct_log_message($customer_address_changes_array);

            $customer_changes_array = array_merge($customer_changes_array, $customer_address_changes_array);
            $customer_changes_formatted_array = array_merge($customer_changes_formatted_array, $customer_address_changes_formatted_array);
            $all_customers_info_changes_made = $log_object->contruct_changes_string($customer_changes_array, $all_customers_info_changes_made);

            if (count($customer_changes_formatted_array)) {
                $changes_str = 'Changes made:' . "\n";

                for ($i = 0; $i < count($customer_changes_formatted_array); $i++) {
                    if (count($customer_changes_formatted_array[$i])) {
                        foreach ($customer_changes_formatted_array[$i] as $field => $res) {
                            if (isset($res['plain_result']) && $res['plain_result'] == '1') {
                                $changes_str .= $res['text'] . "\n";
                            } else {
                                $changes_str .= '<b>' . $res['text'] . '</b>: ' . $res['from'] . ' --> ' . $res['to'] . "\n";
                            }
                        }
                    }
                }
                $log_object->insert_customer_history_log($customer_new_log_row['customers_email_address'], $changes_str);
            }

            $sql_data_array = array('customers_info_changes_made' => $all_customers_info_changes_made);
            tep_db_perform(TABLE_CUSTOMERS_INFO, $sql_data_array, 'update', "customers_info_id = '" . (int) $customer_id . "'");

            // reset the session variables
            $_SESSION['customer_first_name'] = $first_name;
            $_SESSION['customer_last_name'] = $last_name;
            $_SESSION['customer_country_id'] = $billing_country;
        }

        return array(
            'error' => $error,
            'error_array' => $error_array,
            'customers_country_dialing_code_id' => $country_code,
            'customers_telephone' => $mobile_phone_number
        );
    }

    function is_billing_info_completed() {
        $return_bool = TRUE;
        $dial_country_id = '';
        $dial_country_code = '';
        $mobile_phone_number = '';
        $customers_default_address_id = 0;

        $customer_select_sql = "	SELECT customers_country_dialing_code_id, customers_telephone,
                                           customers_firstname, customers_lastname,
                                           customers_default_address_id
                                    FROM " . TABLE_CUSTOMERS . "
                                    WHERE customers_id = '" . (int) $_SESSION['customer_id'] . "'";
        $customer_result_sql = tep_db_query($customer_select_sql);
        if ($customer_row = tep_db_fetch_array($customer_result_sql)) {
            $dial_country_id = $customer_row['customers_country_dialing_code_id'];
            $mobile_phone_number = $customer_row['customers_telephone'];
            $customers_default_address_id = $customer_row['customers_default_address_id'];

            if (!tep_not_empty($customer_row['customers_telephone']) || !tep_not_empty($customer_row['customers_firstname']) || !tep_not_empty($customer_row['customers_lastname'])) {
                $return_bool = 1;
            }
        } else {
            $return_bool = -1;
        }

        if ($return_bool === TRUE) {
            $address_select_sql = "	SELECT entry_street_address, entry_postcode,
                                        entry_city, entry_state, entry_country_id, entry_zone_id
                                    FROM " . TABLE_ADDRESS_BOOK . "
                                    WHERE address_book_id = '" . $customers_default_address_id . "'";
            $address_result_sql = tep_db_query($address_select_sql);
            if ($address_row = tep_db_fetch_array($address_result_sql)) {
                if (!tep_not_empty($address_row['entry_street_address']) || !tep_not_empty($address_row['entry_country_id']) ||
                        !tep_not_empty($address_row['entry_city']) || !tep_not_empty($address_row['entry_postcode']) ||
                        (!tep_not_empty($address_row['entry_state']) && $address_row['entry_zone_id'] == 0)) {
                    $return_bool = 2;
                }
            } else {
                $return_bool = -2;
            }
        }

        if ($return_bool === TRUE) {
            $country_id_select_sql = "  SELECT countries_international_dialing_code
                                        FROM " . TABLE_COUNTRIES . "
                                        WHERE countries_id ='" . tep_db_input($dial_country_id) . "'";
            $country_id_result_sql = tep_db_query($country_id_select_sql);
            if ($country_id_row = tep_db_fetch_array($country_id_result_sql)) {
                $dial_country_code = $country_id_row['countries_international_dialing_code'];
            }

            if (tep_info_verified_check($_SESSION['customer_id'], $dial_country_code . $mobile_phone_number, 'telephone') !== 1) {
                $return_bool = 3;
            }
        }

        return $return_bool;
    }

    function verify_mobile_number_last4digit($customer_id, $last4digit) {
        $error = TRUE;
        $error_message = '';
        $customers_telephone = '';

        if (tep_not_empty($last4digit) && strlen($last4digit) != 4) {
            $error_message = BOX_ERROR_INVALID_LAST4DIGIT;
        } else {
            $customer_select_sql = "	SELECT customers_telephone
                                        FROM " . TABLE_CUSTOMERS . "
                                        WHERE customers_id = '" . $customer_id . "'";
            $customer_result_sql = tep_db_query($customer_select_sql);
            if ($customer_row = tep_db_fetch_array($customer_result_sql)) {
                $customers_telephone = $customer_row['customers_telephone'];
            }

            if ($last4digit == substr($customers_telephone, -4)) {
                $error = FALSE;
            } else {
                $error_message = BOX_ERROR_INVALID_LAST4DIGIT;
            }
        }

        return array('error' => $error, 'error_message' => $error_message);
    }

}

/* End of file User_agent.php */
/* Location: ./system/libraries/User_agent.php */