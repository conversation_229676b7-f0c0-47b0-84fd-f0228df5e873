<?
class vip_groups {
	var $customer_id;
	var $vip_info;
	var $new_rank;
	var $new_middle_point;
	
	function vip_groups($customer_id) {
		$this->customer_id = $customer_id;
		$this->vip_info = array();		
		$this->get_vip_info(true);
	}
	
	function get_vip_info($flush=false) {
		if ($flush) {
			$customers_vip_select_sql = " 	SELECT vip_supplier_groups_id, vip_rank_id, vip_buyback_cummulative_point 
											FROM " . TABLE_CUSTOMERS_VIP . " 
											WHERE customers_id='" . tep_db_input($this->customer_id) . "'";
			$customers_vip_result_sql = tep_db_query($customers_vip_select_sql);
			
			if ($customers_vip_row = tep_db_fetch_array($customers_vip_result_sql)) {
				$this->vip_info = $customers_vip_row;
			}
		}
		
		return $this->vip_info;
	}
	
	function get_supplier_group_name($status_id){
		$select_status_name_sql = "	SELECT vip_supplier_groups_name 
									FROM " . TABLE_VIP_SUPPLIER_GROUPS ." WHERE language_id='1' AND vip_supplier_groups_id='" . $status_id . "'";
		$select_status_name_result = tep_db_query($select_status_name_sql);
		$select_status_name_row = tep_db_fetch_array($select_status_name_result);
		return strtolower($select_status_name_row['vip_supplier_groups_name']);
	}
	
	function set_vip_group($groups_id, $point, $countries_id){ //admin user manual change status
		$region_group_id = '';
		$status_name = $this->get_supplier_group_name($groups_id);
		
		$region_group_select_sql = "SELECT vip_region_group_id 
									FROM " . TABLE_VIP_REGION_GROUP . " 
									WHERE FIND_IN_SET('".$countries_id."', countries_ids)";
		$region_group_result_sql = tep_db_query($region_group_select_sql);
		if ($region_group_row = tep_db_fetch_array($region_group_result_sql)) {
			$region_group_id = $region_group_row['vip_region_group_id'];
		} else {
			$region_group_select_sql = "SELECT vip_region_group_id 
										FROM " . TABLE_VIP_REGION_GROUP . " 
										WHERE FIND_IN_SET('0', countries_ids)";
			$region_group_result_sql = tep_db_query($region_group_select_sql);
			if ($region_group_row = tep_db_fetch_array($region_group_result_sql)) {
				$region_group_id = $region_group_row['vip_region_group_id'];
			}
		}
		
		if (count($this->vip_info)) {	// has VIP info
			if ($this->vip_info['vip_supplier_groups_id'] != $groups_id) { //status changed
				$this->vip_info['vip_supplier_groups_id'] = $groups_id;
				if (tep_not_null($region_group_id)) {
					$select_basic_rank_sql = "	SELECT * 
												FROM " . TABLE_VIP_RANK . " 
												WHERE vip_region_group_id = '" . tep_db_input($region_group_id) . "' 
													AND vip_rank_action = 'SYSTEM_VIP_GROUPS_STATUS_" . $this->vip_info['vip_supplier_groups_id'] . "' 
												ORDER BY vip_rank_sort_order DESC"; //get the lowest rank
					$select_basic_result = tep_db_query($select_basic_rank_sql);
					
					if ($select_basic_row = tep_db_fetch_array($select_basic_result)) {
						$this->vip_info['vip_rank_id'] = $select_basic_row['vip_rank_id'];
						$this->vip_info['vip_buyback_cummulative_point'] = $this->get_median_point($select_basic_row['vip_rank_upgrade_point'], $select_basic_row['vip_rank_downgrade_point']);
					} else {
						// set zero if vip_supplier_groups_id = 1 (member)
						if ((int)$this->vip_info['vip_supplier_groups_id'] == 1) {
							$this->vip_info['vip_rank_id'] = 0;
							$this->vip_info['vip_buyback_cummulative_point'] = 0;
						}
					}
				}
    		} else {
    			$this->vip_info['vip_buyback_cummulative_point'] += $point;
    			$this->check_vip_rank();
        	}
        	$customers_vip_data_array = array(	'vip_supplier_groups_id' => $this->vip_info['vip_supplier_groups_id'],
												'vip_rank_id' => $this->vip_info['vip_rank_id'],
												'vip_buyback_cummulative_point' => $this->vip_info['vip_buyback_cummulative_point']
											);
        	tep_db_perform(TABLE_CUSTOMERS_VIP, $customers_vip_data_array, 'update', 'customers_id="' . tep_db_input($this->customer_id) . '"');
		} else {
			$this->vip_info['vip_supplier_groups_id'] = $groups_id;
			
			if (tep_not_null($region_group_id)) {
				$select_basic_rank_sql = "	SELECT * 
											FROM " . TABLE_VIP_RANK . " 
											WHERE vip_region_group_id = '" . tep_db_input($region_group_id) . "' 
												AND vip_rank_action = 'SYSTEM_VIP_GROUPS_STATUS_" . $this->vip_info['vip_supplier_groups_id'] . "' 
											ORDER BY vip_rank_sort_order DESC"; //get the lowest rank
				$select_basic_result = tep_db_query($select_basic_rank_sql);
				
				if ($select_basic_row = tep_db_fetch_array($select_basic_result)) {
					$this->vip_info['vip_rank_id'] = $select_basic_row['vip_rank_id'];
					$this->vip_info['vip_buyback_cummulative_point'] = $this->get_median_point($select_basic_row['vip_rank_upgrade_point'], $select_basic_row['vip_rank_downgrade_point']);
				}
			}
			
    		$customers_vip_data_array = array(	'vip_supplier_groups_id' => $this->vip_info['vip_supplier_groups_id'],
												'vip_rank_id' => $this->vip_info['vip_rank_id'],
												'vip_buyback_cummulative_point' => $this->vip_info['vip_buyback_cummulative_point'],
												'customers_id' => $this->customer_id
											);
			tep_db_perform(TABLE_CUSTOMERS_VIP, $customers_vip_data_array);
		}
	}
	
	function check_vip_rank(){	// check need to change rank 
		$select_vip_rank_sql = "SELECT vip_region_group_id, vip_rank_downgrade_point, vip_rank_upgrade_point, vip_rank_sort_order 
								FROM " . TABLE_VIP_RANK . " 
								WHERE vip_rank_id = '" . $this->vip_info['vip_rank_id'] . "'";
		$select_vip_rank_result = tep_db_query($select_vip_rank_sql);
		$select_vip_rank_row = tep_db_fetch_array($select_vip_rank_result);
		if ($this->vip_info['vip_buyback_cummulative_point'] < $select_vip_rank_row['vip_rank_downgrade_point']) { //downgrade the rank
			$this->assign_rank($select_vip_rank_row['vip_region_group_id'], $select_vip_rank_row['vip_rank_sort_order'], 'down');
		} else if ($this->vip_info['vip_buyback_cummulative_point'] > $select_vip_rank_row['vip_rank_upgrade_point']){ //up rank
			$this->assign_rank($select_vip_rank_row['vip_region_group_id'], $select_vip_rank_row['vip_rank_sort_order'], 'up');
		}
	}
	
	function assign_rank($cur_region_group_id, $cur_sort_order, $level){
		$select_rank_sql = "SELECT * 
							FROM " . TABLE_VIP_RANK . " 
							WHERE vip_region_group_id= '" . $cur_region_group_id . "' 
								AND vip_rank_sort_order " . ($level == "up" ? '<' : '>') . " '" . $cur_sort_order . "' 
								AND '" . $this->vip_info['vip_buyback_cummulative_point'] . "' BETWEEN vip_rank_downgrade_point AND vip_rank_upgrade_point
							ORDER BY vip_rank_sort_order ".($level == "up" ? 'DESC' : 'ASC');
		$select_rank_result = tep_db_query($select_rank_sql);
		if ($select_rank_row = tep_db_fetch_array($select_rank_result)){
			$this->vip_info['vip_buyback_cummulative_point'] = $this->get_median_point($select_rank_row['vip_rank_upgrade_point'], $select_rank_row['vip_rank_downgrade_point']);
			$this->vip_info['vip_rank_id'] = $select_rank_row['vip_rank_id'];
			
			$this->check_upgrades();
		} else if($level == 'down'){ //set to default if can't search for any rank below the current rank (member = vip_supplier_groups_id = 1)
			$this->vip_info['vip_buyback_cummulative_point'] = 0;		
			$this->vip_info['vip_rank_id'] = 0;
			$this->vip_info['vip_supplier_groups_id'] = 1;
		}
	}
	
	function check_upgrades() { // check need to change status?
		$rank_token = $this->get_rank_action_token();
		$rank_token_array = explode('_', $rank_token);
		
		if (strcasecmp($this->vip_info['vip_supplier_groups_id'], $rank_token_array[count($rank_token_array) - 1]) != 0){
			$this->vip_info['vip_supplier_groups_id'] = $rank_token_array[count($rank_token_array) - 1];
		}
	}
	
	function get_median_point($upgrade_point, $downgrade_point){ // set new cummulative point when rank changed
		$middle = ceil(($upgrade_point+$downgrade_point)/2);
		return $middle;
	}
	
	function get_rank_action_token() {
		$select_rank_action_sql = "	SELECT vip_rank_action 
									FROM " . TABLE_VIP_RANK . " 
									WHERE vip_rank_id='" . $this->vip_info['vip_rank_id'] . "'";
		$select_rank_action_result = tep_db_query($select_rank_action_sql);
		$select_rank_action_row = tep_db_fetch_array($select_rank_action_result);
		
		return $select_rank_action_row['vip_rank_action'];
	}
	
	function get_new_rank(){
		return $this->vip_info['vip_rank_id'];
	}
	
	function get_new_point(){
		return $this->new_middle_point;
	}
	
	function calculate_cummulative_point($action){
		$select_point_sql = "SELECT vip_rules_operator, vip_rules_value 
							FROM " . TABLE_VIP_RULES ." 
							WHERE vip_rules_key='" . $action . "'";
		$select_point_result = tep_db_query($select_point_sql);
		if($select_point_row = tep_db_fetch_array($select_point_result)){
			$operator = $select_point_row['vip_rules_operator'];
			$point = $select_point_row['vip_rules_value'];
			$this->vip_info['vip_buyback_cummulative_point'] += $operator.$point;
			$this->check_vip_rank();
			$update_point_array = array('vip_supplier_groups_id' => tep_db_input($this->vip_info['vip_supplier_groups_id']),
										'vip_rank_id' => tep_db_input($this->vip_info['vip_rank_id']),
										'vip_buyback_cummulative_point' => tep_db_input($this->vip_info['vip_buyback_cummulative_point'])
										);
			
			tep_db_perform(TABLE_CUSTOMERS_VIP, $update_point_array, 'update', 'customers_id="' . tep_db_input($this->customer_id) . '"');
		}
		
	}
	
	function calculate_customers_rating($point){
		$this->vip_info['vip_buyback_cummulative_point'] += $point;
		$this->check_vip_rank();
		$update_point_array = array('vip_supplier_groups_id' => tep_db_input($this->vip_info['vip_supplier_groups_id']),
									'vip_rank_id' => tep_db_input($this->vip_info['vip_rank_id']),
									'vip_buyback_cummulative_point' => tep_db_input($this->vip_info['vip_buyback_cummulative_point'])
									);
		
		tep_db_perform(TABLE_CUSTOMERS_VIP, $update_point_array, 'update', 'customers_id="' . tep_db_input($this->customer_id) . '"');
	}
	
}
?>