<?php
/*
  	$Id: vip_supplier.php,v 1.2 2012/11/06 03:27:07 weichen Exp $

  	Developer: <PERSON>
  	Copyright (c) 2005 SKC Ventrue

  	Released under the GNU General Public License
*/

////
// Class to handle theme management
require_once(DIR_WS_CLASSES . 'log.php');

class vip_supplier
{
	var $supplier_id;
	var $registered_servers;
	var $game_settings;
	
	function vip_supplier($supplier_id) {
		$this->supplier_id = $supplier_id;
		$this->registered_servers = array();	// VIP Module Supplier registered servers
		$this->game_settings = array();
		
		$this->_get_registered_server();
	}
	
	function get_supplier_setting($main_cat_id) {
		$setting_select_sql = "	SELECT vip_supplier_setting_key, vip_supplier_setting_value 
								FROM " . TABLE_VIP_SUPPLIER_SETTING . "
								WHERE customers_id = '".tep_db_input($this->supplier_id)."' 
									AND categories_id = '".tep_db_input($main_cat_id)."'";
		$setting_result_sql = tep_db_query($setting_select_sql);
		while ($setting_row = tep_db_fetch_array($setting_result_sql)) {
	    	$this->game_settings[$setting_row['vip_supplier_setting_key']] = $setting_row['vip_supplier_setting_value'];
		}
	}
	
	function set_supplier_setting($content, $main_cat_id, $setting_key, $setting_val, $messageStack, $update_info='') {
		if ($main_cat_id) {
			if (tep_not_null($setting_val)) {
				if (is_array($setting_val)) {
					$setting_val = implode(',', $setting_val);
				}
				$setting_data_array = array (	'vip_supplier_setting_value' => $setting_val);
			}
			
			// Check record exist
			$key_select_sql = "	SELECT vip_supplier_setting_value 
								FROM " . TABLE_VIP_SUPPLIER_SETTING . " 
								WHERE customers_id='".tep_db_input($this->supplier_id)."' 
									AND categories_id='".tep_db_input($main_cat_id)."' 
									AND vip_supplier_setting_key = '".tep_db_input($setting_key)."'";
			$key_result_sql = tep_db_query($key_select_sql);
			
			if (tep_db_num_rows($key_result_sql)) { // if exist
				if (tep_not_null($setting_val)) {
					tep_db_perform(TABLE_VIP_SUPPLIER_SETTING, $setting_data_array, 'update', "customers_id='".tep_db_input($this->supplier_id)."' AND categories_id='".tep_db_input($main_cat_id)."' AND vip_supplier_setting_key = '" . $setting_key . "'");
				} else {
					$delete_supplier_setting_sql = "DELETE FROM " . TABLE_VIP_SUPPLIER_SETTING ." 
													WHERE customers_id = '" . tep_db_input($this->supplier_id) . "' 
														AND categories_id = '" . tep_db_input($main_cat_id) . "'
														AND vip_supplier_setting_key = '" . tep_db_input($setting_key) . "'";
					tep_db_query($delete_supplier_setting_sql);
				}
			} else { //insert
				$setting_data_array['vip_supplier_setting_key'] = $setting_key;
				$setting_data_array['customers_id'] = $this->supplier_id;
				$setting_data_array['categories_id'] = $main_cat_id;
				
				tep_db_perform(TABLE_VIP_SUPPLIER_SETTING, $setting_data_array);
			}
			if (tep_not_null($update_info)) {
				$messageStack->add_session($content, $update_info, 'success');
			}
		}
	}
	
	function update_inventory($product_id, $quantity, $action='update') {
		//add to log(TABLE_VIP_SUPPLIER_INVENTORY_LOGS) and calculate supplier production(TABLE_VIP_PRODUCTION_HISTORY) when update inventory qty
		$logObj = new log_files($this->supplier_id);
		$logObj->table = TABLE_VIP_SUPPLIER_INVENTORY_LOGS;
		
		if ($quantity > 0) {
			//get current value before update
			$old_qty = $this->get_old_qty($product_id);
			if ($old_qty !== FALSE) {
                $action = 'update'; // No longer is insert when there is inventory for this Product of this Supplier
            }
			//update latest quantity
			if ($action == 'insert') {
				$server_data_array = array(	'customers_id' => $this->supplier_id,
		        							'products_id' => $product_id,
		        							'vip_supplier_inventory_qty' => $quantity
	                                		);
	            tep_db_perform(TABLE_VIP_SUPPLIER_INVENTORY, $server_data_array);
			} else if($action == 'update') {
				$inventory_update_sql = "	UPDATE " . TABLE_VIP_SUPPLIER_INVENTORY . " 
											SET vip_supplier_inventory_qty = '" . tep_db_input($quantity) . "' 
											WHERE customers_id = '".tep_db_input($this->supplier_id)."' 
												AND products_id = '".tep_db_input($product_id)."'";
				tep_db_query($inventory_update_sql);
			}
						
			// add to log
			if ($quantity != $old_qty){
				$logObj->insert_vip_inventory_log($product_id, $old_qty, $quantity);
			}
			
			//compare the qty and add to production log if new quantity is higher than old qty.
			if($quantity > $old_qty){
				$production_qty = $quantity - $old_qty;
				$logObj->table = TABLE_VIP_PRODUCTION_HISTORY;
				$logObj->update_production_log($product_id, $production_qty);
			}
			
		} else {
			$this->delete_inventory($product_id);
		}
	}
	
	function update_min_quantity($product_id, $min_qty){
		$min_qty_sql = "UPDATE " . TABLE_VIP_SUPPLIER_INVENTORY . " 
						SET vip_supplier_inventory_min_qty = '".tep_db_input($min_qty)."' 
						WHERE customers_id='" . tep_db_input($this->supplier_id) . "' AND products_id='" . tep_db_input($product_id) . "'";
		tep_db_query($min_qty_sql);
	}
	
	function delete_inventory($product_id) {
		$quantity = 0;
		$logObj = new log_files($this->supplier_id);
		$logObj->table = TABLE_VIP_SUPPLIER_INVENTORY_LOGS;
		$old_qty = $this->get_old_qty($product_id);
		$inventory_delete_sql = "	DELETE FROM " . TABLE_VIP_SUPPLIER_INVENTORY . " 
									WHERE customers_id = '".tep_db_input($this->supplier_id)."' 
										AND products_id = '".tep_db_input($product_id)."'";
		tep_db_query($inventory_delete_sql);
		$logObj->insert_vip_inventory_log($product_id, $old_qty, $quantity);
	}
	
	function _get_registered_server() {
		$server_select_sql = "	SELECT products_id, vip_supplier_inventory_qty, vip_supplier_inventory_min_qty
								FROM " . TABLE_VIP_SUPPLIER_INVENTORY . "
								WHERE customers_id = '".tep_db_input($this->supplier_id)."'";
		$server_result_sql = tep_db_query($server_select_sql);
		while ($server_result_row = tep_db_fetch_array($server_result_sql)) {
	    	$this->registered_servers[$server_result_row['products_id']] = array (	'qty' => $server_result_row['vip_supplier_inventory_qty'],
	    																			'min_qty' => $server_result_row['vip_supplier_inventory_min_qty']
	    																			);
		}
	}
	
	function get_old_qty($prod_id){
		$old_qty = FALSE;
		$select_old_qty = "	SELECT vip_supplier_inventory_qty 
							FROM " . TABLE_VIP_SUPPLIER_INVENTORY ." 
							WHERE customers_id='" . tep_db_input($this->supplier_id) . "' 
								AND products_id='" . tep_db_input($prod_id) . "'";
		$select_old_qty_result = tep_db_query($select_old_qty);
		if ($select_old_qty_row = tep_db_fetch_array($select_old_qty_result)){
			$old_qty = $select_old_qty_row['vip_supplier_inventory_qty'];
		}
		return $old_qty;
	}
}