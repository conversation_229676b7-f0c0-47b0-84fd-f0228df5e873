<?php
	class quest {
		var $data;
		
		function quest ($data) {
			$this->data = $data;
		}
		
		function get ( $field ) {
			return $this->data[$field];
		}
		
		function outHeader ( $subOpen ) {
                    if ( $subOpen ) {
				echo '</ul></li>';
			}
			echo '<li class="questlocation">' . $this->data['char_quest_location'] . '<ul>';
		}
		
		function out () {
			list ( $level, $isElite, $isComplete ) = explode ( ':', $this->data['char_quest_level'] );
			
			if ($isElite != 'Elite' ) {
				echo '<li class="quest">[' . $level . ']&nbsp;' . $this->data['char_quest_title'] . '</li>';
			} else {
				echo '<li class="quest">[' . $level . ']&nbsp;' . $this->data['char_quest_title'] . '&nbsp;(Elite)</li>';			
			}
		}
	}
		
	function get_quests ($id, $game_char_history_id) {
		$quests = array ();
		$location = '';
		
		$quest_info_select_sql = "	SELECT char_quest_location,  char_quest_order,  char_quest_level, char_quest_title 
									FROM " . TABLE_CHAR_QUEST_HISTORY . " 
									WHERE game_char_id = '" . tep_db_input($id) . "' 
										AND game_char_history_id = '" . tep_db_input($game_char_history_id) . "' 
									ORDER BY char_quest_order";
		$quest_info_result_sql = tep_db_query($quest_info_select_sql);
		
		while ($quest_info_row = tep_db_fetch_array($quest_info_result_sql)) {
			if ($location != $quest_info_row['char_quest_location']) {
				$location = $quest_info_row['char_quest_location'];
				$quests[$location] = array ();
			}
			$quest = new quest ($quest_info_row);
			$quests[$location][] = $quest;
		}
		return $quests;
	}
?>