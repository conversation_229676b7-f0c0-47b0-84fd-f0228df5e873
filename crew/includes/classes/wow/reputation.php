<?php
	class reputation {
		var $data;
		
		function reputation ($data) {
			$this->data = get_reputation_details($data['char_reputation_history_id']);
		}
		
		function out () {
			$reputation_content = '';
			foreach ($this->data as $reputation_category => $reputation_name_array) {
				$reputation_content .= '<div class="reputation"><span class="yellow">' . $reputation_category . '</span><br>';
				
				foreach ($reputation_name_array as $reputation_name => $reputation_info_array) {
					$reputation_content .= '&nbsp;&nbsp;' . $reputation_name . ' - ' . $reputation_info_array['char_reputation_detail_value'] . ' (' . $reputation_info_array['char_reputation_detail_standing'] . ')<br>';
				}
				$reputation_content .= '<br>';			
				$reputation_content .= '</div>';
			}
			echo $reputation_content;
		}
	}
		
	function get_reputation ($id, $game_char_history_id) {
		$char_reputation_history_id_select_sql = "	SELECT char_reputation_history_id FROM " . TABLE_CHAR_REPUTATION_HISTORY . " WHERE game_char_id ='" . tep_db_input($id) . "' AND game_char_history_id ='" . tep_db_input($game_char_history_id) . "'";
		$char_reputation_history_id_result_sql = tep_db_query($char_reputation_history_id_select_sql);
		$char_reputation_history_id_row = tep_db_fetch_array($char_reputation_history_id_result_sql);
		
		$reputation = new reputation($char_reputation_history_id_row);
		
		return $reputation;
	}
	
	function get_reputation_details ($char_reputation_history_id) {
		$char_reputation_history_info_array = array();
		
		$char_reputation_history_info_select_sql = "	SELECT char_reputation_detail_category, char_reputation_detail_name, char_reputation_detail_standing, char_reputation_detail_value, char_reputation_detail_at_war 
														FROM " . TABLE_CHAR_REPUTATION_DETAIL . " 
														WHERE char_reputation_history_id ='" . tep_db_input($char_reputation_history_id) . "'";
		$char_reputation_history_info_result_sql = tep_db_query($char_reputation_history_info_select_sql);
		while ($char_reputation_history_info_row = tep_db_fetch_array($char_reputation_history_info_result_sql)) {
			$char_reputation_history_info_array[$char_reputation_history_info_row['char_reputation_detail_category']][$char_reputation_history_info_row['char_reputation_detail_name']] = $char_reputation_history_info_row;
		}
		
		return $char_reputation_history_info_array;
	}
?>