<?php

class skill {
  var $data;

  function skill( $data ) {
    $this->data = $data;
  }

  function get( $field ) {
    return $this->data[$field];
  }
  
  function outHeader() {
    echo '<div class="skilltype">'.$this->data['char_skill_type'].' </div>';
  }
  
  function out() {
  	$path = tep_href_link(DIR_WS_CATALOG . DIR_WS_INTERFACE);
  	
    list($level, $max) = explode( ':', $this->data['char_skill_level'] );
    
    if( $max == 1 ) {
      $bgImage = $path . 'img/barGrey.gif';
    } else {
      $bgImage = $path . 'img/barEmpty.gif';
    }

    echo '	<div class="skill">
				<div class="skillbox">
					<img class="bg" alt="" src="'.$bgImage.'" />';
	if( $max > 1 ) {
  		$width = intval(($level/$max) * 354);
 		 echo tep_image($path . 'img/barBit.gif', '', $width, '13', 'class="bit"');
	}
	echo '				<span class="name">'.$this->data['char_skill_name'].'</span>';
	if( $max > 1 ) {
		echo '				<span class="level">'.$level.'/'.$max.'</span>';
	}
	echo '		</div>
			</div>';
	}
}

function skill_get_many_by_type($id, $type, $game_char_history_id) {
	return skill_get_many($id, "`type` = '$type'", $game_char_history_id);
}

function skill_get_many_by_order($id, $order, $game_char_history_id) {
 	return skill_get_many($id, "`char_skill_order` = '$order'", $game_char_history_id);
}

function skill_get_many($id, $search, $game_char_history_id) {
	$skill_info_select_sql = "SELECT * FROM " . TABLE_CHAR_SKILL_HISTORY . " WHERE game_char_id ='" . tep_db_input($id) . "' AND game_char_history_id='" . tep_db_input($game_char_history_id) . "' AND $search";
	$skill_info_result_sql = tep_db_query($skill_info_select_sql);
	
	$skills = array();
	
	while ($skill_info_row = tep_db_fetch_array($skill_info_result_sql)) {
		$skill = new skill($skill_info_row);
    	$skills[] = $skill;
	}
	
	return $skills;
}
?>