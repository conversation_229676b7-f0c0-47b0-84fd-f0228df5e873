<?
/*
  	$Id: column_left.php,v 1.27 2011/10/27 09:31:12 weesiong Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2003 osCommerce
	
  	Released under the GNU General Public License
*/

if (basename($PHP_SELF) == FILENAME_DEFAULT || basename($PHP_SELF) == FILENAME_CUSTOM_PRODUCT_INFO) {
	if (($page_info->main_game_id == 0 && $page_info->custom_products_type_id_cat > 0) || $pagetype == 'q') {
	} else {
		include(DIR_WS_BOXES . 'product_type.php');
	}
	
	//include(DIR_WS_BOXES . 'newsletter.php');			// FB Box
	//include(DIR_WS_BOXES . 'customer_testimonial.php');
	//include(DIR_WS_BOXES . 'payment_gateway.php');
	//include(DIR_WS_BOXES . 'polling.php');
} else if (substr(basename($PHP_SELF), 0, 13) == 'buyback_order' || substr(basename($PHP_SELF), 0, 19) == 'sc_checkout_payment' || substr(basename($PHP_SELF), 0, 7) == 'account' || substr(basename($PHP_SELF), 0, 12) == 'address_book' || substr(basename($PHP_SELF), 0, 3) == 'my_' || substr(basename($PHP_SELF), 0, 14) == 'set_secret_qna' || basename($PHP_SELF) == FILENAME_CANCEL_ORDER || basename($PHP_SELF) == FILENAME_RECEIVE_PAYMENT_INFO || substr(basename($PHP_SELF), 0, 4) == 'vip_' || in_array(basename($PHP_SELF), array(FILENAME_REDEEM_POINT, FILENAME_INVITER, FILENAME_VERIFICATION_SUBMISSION_FORM, FILENAME_FACEBOOK_CONNECT))) {
	include(DIR_WS_BOXES . 'my_account.php');
} else if (substr(basename($PHP_SELF), 0, 7) == 'buyback') {
	include(DIR_WS_INCLUDES . 'buyback_column_left.php');
	include(DIR_WS_BOXES . 'games.php');
} else if (substr(basename($PHP_SELF), 0, 16) == 'search_all_games') {
	include(DIR_WS_BOXES . FILENAME_SEARCH_ALL_GAMES);
}
?>