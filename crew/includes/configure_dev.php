<?php
/*
  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2003 osCommerce

  Released under the GNU General Public License
*/

/*******************************************************************
	Define the webserver and path parameters
	DIR_FS_* = Filesystem directories (local/physical)
	
	DIR_WS_* = Webserver directories (virtual/URL)
		Note: Should not has leading slash and must has ending slash
********************************************************************/
  define('HTTP_SERVER', 'http://www.offgamers.dev/'); // eg, http://localhost - should not be empty for productive servers
  define('HTTPS_SERVER', 'https://www.offgamers.dev/'); // eg, https://localhost - should not be empty for productive servers
  
   if ((getenv('HTTPS') == 'on')) {
      define('HTTP_STATIC_SERVER', 'http://www.offgamers.dev/'); // eg, static server
  } else {
      define('HTTP_STATIC_SERVER', 'http://www.offgamers.dev/'); // eg, static server
  }
  
  define('ENABLE_SSL', false); // secure webserver for checkout procedure?
  define('HTTP_COOKIE_DOMAIN', 'offgamers.dev');
  define('HTTPS_COOKIE_DOMAIN', 'offgamers.dev');
  define('HTTP_COOKIE_PATH', '/');
  define('HTTPS_COOKIE_PATH', '/');
  define('HTTP_AFFILIATE_SERVER', 'http://www.offgamers.dev/');
  define('HTTPS_AFFILIATE_SERVER', 'https://www.offgamers.dev/');
  define('ENABLE_SSL_AFFILIATE', 'false'); // secure webserver for supplier module
  define('ENABLE_SSL_UPLOAD', 'false'); // secure webserver for upload module
  define('HTTP_UPLOAD_SERVER', 'http://www.offgamers.dev/');
  define('HTTPS_UPLOAD_SERVER', 'https://www.offgamers.dev/');
  define('HTTP_PGS_SERVER', 'http://www.offgamers.dev/pgs/index.php'); 
  define('DIR_FS_ADMIN', 'D:/_www/offgamers.dev/www54.offgamers.dev/httpdocs/admin/'); // absolute path required
  define('DIR_FS_CATALOG', 'D:/_www/offgamers.dev/www54.offgamers.dev/httpdocs/');
  define('DIR_FS_STATIC_SERVER', 'D:/_www/offgamers.dev/www54.offgamers.dev/httpdocs/'); // eg, static server
  define('DIR_FS_STATIC_SERVER_DEST', 'D:/_www/offgamers.dev/www54.offgamers.dev/httpdocs/'); // eg, static server
  define('DIR_FS_DOCUMENT_ROOT', 'D:/_www/offgamers.dev/www54.offgamers.dev/httpdocs/'); // where the pages are located on the server
  define('DIR_WS_HTTP_CATALOG', '');
  define('DIR_WS_HTTPS_CATALOG', '');
  define('DIR_WS_AFFILIATE', 'affiliate/'); // absolute path required
  define('DIR_WS_IMAGES', HTTP_STATIC_SERVER . 'images/');
  define('DIR_WS_HLA', HTTP_STATIC_SERVER . 'images/');
  define('DIR_FS_IMAGES', DIR_FS_STATIC_SERVER . 'images/');
  define('DIR_FS_HLA', DIR_FS_STATIC_SERVER_DEST . 'images/');
  define('DIR_WS_ICONS', DIR_WS_IMAGES . 'icons/');
  define('DIR_WS_INTERFACE', DIR_WS_IMAGES . 'Interface/');
  define('DIR_WS_INCLUDES', 'includes/');
  define('DIR_WS_BOXES', DIR_WS_INCLUDES . 'boxes/');
  define('DIR_WS_FUNCTIONS', DIR_WS_INCLUDES . 'functions/');
  define('DIR_WS_CLASSES', DIR_WS_INCLUDES . 'classes/');
  define('DIR_WS_MODULES', DIR_WS_INCLUDES . 'modules/');
  define('DIR_WS_LANGUAGES', DIR_WS_INCLUDES . 'languages/');
  define('DIR_WS_UPLOAD', 'upload/'); // absolute path required
  define('DIR_WS_THEME', 'theme/');
  define('DIR_FS_THEME', DIR_FS_CATALOG . 'theme/');
  
  define('SITE_ID', '0');
  
  /****************************
  	CD Key
  ****************************/
  define('DIR_FS_SECURE', 'D:/_secure/');
  define('DIR_FS_SECURE_TEMP', 'D:/_secure/temp/');
  define('SECURE_KEY', '2QH4E9B1M9L0F37D3HJ4FH6853FL8GD8');	// Must be 32 characters length
  define('SECURE_KEY_IV', '9S?C3B4WMQW6XM8P');	// Must be 16 characters length
  define('SECURE_CIPHER', MCRYPT_RIJNDAEL_128);
  
  /****************************
  	Customer Documents
  ****************************/
  define('DIR_FS_DATA_LL_SUBMISSION', DIR_FS_CATALOG . 'upload/docs/');
  define('DIR_FS_DATA_LL_SUBMISSION_TEMP', DIR_FS_CATALOG . 'upload/docs/temp_editing/');
  
  /****************************
  	Affiliate Module
  ****************************/
  define('DIR_WS_AFFILIATE_AFFILIATES', 'affiliates/'); // absolute path required
  define('DIR_WS_AFFILIATE_MERCHANT', 'merchant/'); // absolute path required
  define('DIR_WS_AFFILIATE_SCRIPTS', 'scripts/'); // absolute path required
  /****************************
  	End of Affiliate Module
  ****************************/
  
  // define affiliate
  define('AFFILIATE_SITE_ID', 2); // for customers login sites
  define('JOOMLA_REMOTE_ACCOUNT_KEY','2C6893B43F19B09ADC2E');
  
  // OAuth Setting
  define('HTTPS_OAUTH', 'http://oauth.offgamers.lan/ogm_connect/index.php/oauth2/');
  define('OAUTH_SECRET_KEY', '123456'); // Corresponding to setting in OAuth main.php config file
  define('JS_CROSS_DOMAIN', 'www.offgamers.lan');   // Define as production.com for www.production.com
  
  // SSO Setting
  define('SSO_SECRET_KEY', '123456');   // used to encrypt the SSO token 
  
//Added for BTS1.0
  define('DIR_WS_TEMPLATES', 'templates/');
  define('DIR_WS_CONTENT', DIR_WS_TEMPLATES . 'content/');
  define('DIR_WS_JAVASCRIPT', HTTP_STATIC_SERVER . 'includes/javascript/');
  define('DIR_FS_JAVASCRIPT', DIR_FS_STATIC_SERVER . 'includes/javascript/');
//End BTS1.0

  define('DIR_WS_DOWNLOAD_PUBLIC', 'pub/');
  define('DIR_FS_DOWNLOAD', DIR_FS_CATALOG . 'download/');
  define('DIR_FS_DOWNLOAD_PUBLIC', DIR_FS_CATALOG . 'pub/');
  
  /****************************
  	Gift Card
  ****************************/
  define('HTTPS_PIN_SERVER', 'https://api.offgamers.biz/ws/pin/');
  define('PIN_MERCANT_CODE', 'OGM');
  define('PIN_SECRET_KEY', 'TESTOGM');
  
  /****************************
  	My Account
  ****************************/
  define("HTTP_SHASSO_PORTAL", "http://www.shasso.dev");

  define('SHASSO_CLIENT_ID', 'ogm');
  define('SHASSO_CLIENT_SECRET','LcRMgYNjQ99UQT3wQeAjVG6T');
  
  /****************************
  	Memcached
  ****************************/
  define('CFG_MEMCACHE_SERVER', 'localhost'); // eg, localhost - should not be empty for productive servers
  
  /****************************
  	Checkout
  ****************************/
  define('CHECKOUT_DEVICEPIN_SECRET', '123456');
  
  /****************************
    Auto Tagging
  ****************************/
  define('PWLING_UNSUBMITTED_ORDER_TAG_ID',101);
  define('HLA_WOW_ACC_ORDER_TAG_ID',472);
  
// define our database connection
  define('DB_SERVER', 'localhost'); // eg, localhost - should not be empty for productive servers
  define('DB_SERVER_USERNAME', 'skc_user');
  define('DB_SERVER_PASSWORD', 'skc_password');
  define('DB_DATABASE', 'offgamers');
  define('USE_PCONNECT', 'false'); // use persistent connections?
  define('STORE_SESSIONS', 'mysql'); // leave empty '' for default handler or set to 'mysql'
  
// define our reporting database connection  
  define('DB_REPORT_SERVER', 'localhost'); // eg, localhost - should not be empty for productive servers
  define('DB_REPORT_SERVER_USERNAME', 'skc_user');
  define('DB_REPORT_SERVER_PASSWORD', 'skc_password');
  define('DB_REPORT_DATABASE', 'offgamers');
?>
