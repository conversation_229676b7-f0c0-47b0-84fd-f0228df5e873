<?

/*
  $Id: database_tables.php,v 1.153 2015/05/28 10:47:33 darren.ng Exp $

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2003 osCommerce

  Released under the GNU General Public License
 */
// BEGIN latest news
define('TABLE_LATEST_NEWS', 'latest_news');
define('TABLE_LATEST_NEWS_DESCRIPTION', 'latest_news_description');
define('TABLE_LATEST_NEWS_GROUPS', 'latest_news_groups');
define('TABLE_LATEST_NEWS_GROUPS_DESCRIPTION', 'latest_news_groups_description');
define('TABLE_LATEST_NEWS_CATEGORIES', 'latest_news_categories');
define('TABLE_LATEST_NEWS_TAG_CONTENT', 'latest_news_tag_content');
define('TABLE_LATEST_NEWS_TAG_CATEGORIES', 'latest_news_tag_categories');

// define the database table names used in the project
define('TABLE_ADDRESS_BOOK', 'address_book');
define('TABLE_ADDRESS_FORMAT', 'address_format');
define('TABLE_ADMIN', 'admin');
define('TABLE_ADYEN', 'adyen');
define('TABLE_ADYEN_STATUS_HISTORY', 'adyen_status_history');
define('TABLE_AFT_AUTOMATION', 'aft_automation');
define('TABLE_AFT_AUTOMATION_CATEGORY', 'aft_automation_category');
define('TABLE_AFT_AUTOMATION_VERSION', 'aft_automation_version');
define('TABLE_AFT_FUNCTIONS', 'aft_functions');
define('TABLE_ALIPAY', 'alipay');
define('TABLE_ALIPAY_STATUS_HISTORY', 'alipay_status_history');
define('TABLE_AUTOMATE_BUYBACK_PRICE', 'automate_buyback_price');
define('TABLE_AUTHORIZED_TOKEN', 'authorized_token');
define('TABLE_AUTOMATE_ALERT_COLOR', 'automate_alert_color');
define('TABLE_BANNERS', 'banners');
define('TABLE_BANNERS_HISTORY', 'banners_history');
define('TABLE_BIBIT', 'bibit');
define('TABLE_BIBIT_CURRENCIES', 'bibit_currencies');
define('TABLE_BIBIT_PAYMENT_STATUS_HISTORY', 'bibit_payment_status_history');
define('TABLE_BUYBACK_COMPETITOR_STATUS_BRACKET', 'buyback_competitor_status_bracket');
define('TABLE_BUYBACK_COMPETITOR_STATUS_BRACKET_SET', 'buyback_competitor_status_bracket_set');
define('TABLE_BUYBACK_SUPPLIER_PRICE_BRACKET_SET', 'buyback_supplier_price_bracket_set');
define('TABLE_BUYBACK_SUPPLIER_PRICE_BRACKET', 'buyback_supplier_price_bracket');
define('TABLE_BUYBACK_PRICE_CONTROL', 'buyback_price_control');
define('TABLE_BUYBACK_ONLINE_USER', 'buyback_online_user');
define('TABLE_BUYBACK_SUSPEND_USER', 'buyback_suspend_user');
define('TABLE_CART_COMMENTS', 'cart_comments');
define('TABLE_CATEGORIES', 'categories');
define('TABLE_CATEGORIES_CONFIGURATION', 'categories_configuration');
define('TABLE_CATEGORIES_DESCRIPTION', 'categories_description');
define('TABLE_CATEGORIES_GAME_DETAILS', 'categories_game_details');
define('TABLE_CATEGORIES_GROUPS', 'categories_groups');
define('TABLE_CATEGORIES_SEARCH', 'categories_search');
define('TABLE_CATEGORIES_SERVICES', 'categories_services');
define('TABLE_CATEGORIES_SETTING', 'categories_setting');
define('TABLE_CATEGORIES_STRUCTURES', 'categories_structures');
define('TABLE_CATEGORIES_PRODUCT_TYPES', 'categories_product_types');
define('TABLE_CATEGORIES_TYPES_GROUPS', 'categories_types_groups');
define('TABLE_CATEGORIES_TYPES_SETS', 'categories_types_sets');
define('TABLE_CATEGORIES_TYPES_TO_SETS', 'categories_types_to_sets');
define('TABLE_CATEGORIES_TYPES', 'categories_types');
define('TABLE_CIMB', 'cimb');
define('TABLE_CIMB_STATUS_HISTORY', 'cimb_status_history');
define('TABLE_CRON_GENESIS_ORDERS', 'cron_genesis_orders');
define('TABLE_CRON_ORDERS_PAYMENT_STATUS', 'cron_orders_payment_status');
define('TABLE_CRON_PENDING_CREDIT', 'cron_pending_credit');
define('TABLE_CRON_PROCESS_TRACK', 'cron_process_track');
define('TABLE_CRON_OPEN_RESTOCK_ID', 'cron_open_restock_id');
define('TABLE_CMS_MENU', 'cms_menu'); // Menu Management
define('TABLE_CMS_MENU_VALUE', 'cms_menu_lang_setting'); // Menu Management
define('TABLE_CMS_MENU_TAB_PAGE', 'cms_menu_tab_pages'); // Menu Management
define('TABLE_SEO_META_TAG', 'seo_meta_tag'); // SEO META TAG
define('TABLE_CODA', 'coda');
define('TABLE_CODA_STATUS_HISTORY', 'coda_status_history');
define('TABLE_COMPETITORS_TO_CATEGORIES', 'competitors_to_categories');
define('TABLE_COMPETITORS', 'competitors');
define('TABLE_CONFIGURATION', 'configuration');
define('TABLE_CONFIGURATION_GROUP', 'configuration_group');
define('TABLE_COUNTER', 'counter');
define('TABLE_COUNTER_HISTORY', 'counter_history');
define('TABLE_COUNTRIES', 'countries');
define('TABLE_COUNTRIES_CONTENT', 'countries_content');
define('TABLE_COUNTRIES_CONTENT_DESCRIPTION', 'countries_content_description');
define('TABLE_COUPON_GV_CUSTOMER', 'coupon_gv_customer');
define('TABLE_COUPON_GV_QUEUE', 'coupon_gv_queue');
define('TABLE_COUPON_REDEEM_TRACK', 'coupon_redeem_track');
define('TABLE_COUPON_EMAIL_TRACK', 'coupon_email_track');
define('TABLE_COUPONS', 'coupons');
define('TABLE_COUPONS_DESCRIPTION', 'coupons_description');
define('TABLE_CURRENCIES', 'currencies');
define('TABLE_CUSTOM_PRODUCTS_CODE', 'custom_products_code');
define('TABLE_CUSTOM_PRODUCTS_CODE_LOG', 'custom_products_code_log');
define('TABLE_CUSTOMERS', 'customers');
define('TABLE_CUSTOMERS_AFT_GROUPS', 'customers_aft_groups');
define('TABLE_CUSTOMERS_BASKET', 'customers_basket');
define('TABLE_CUSTOMERS_BASKET_ATTRIBUTES', 'customers_basket_attributes');
define('TABLE_CUSTOMERS_BASKET_BUNDLE', 'customers_basket_bundle');
define('TABLE_CUSTOMERS_CONNECTION', 'sns_connection');
define('TABLE_CUSTOMERS_FLAG_MESSAGE', 'customers_flag_message');
define('TABLE_CUSTOMERS_FAVORITES', 'customers_favorites');
define('TABLE_CUSTOMERS_GROUPS', 'customers_groups');
define('TABLE_CUSTOMERS_GROUPS_DISCOUNT', 'customers_groups_discount');
define('TABLE_CUSTOMERS_GROUPS_EXTRA_OP', 'customers_groups_extra_op');
define('TABLE_CUSTOMERS_INFO', 'customers_info');
define('TABLE_CUSTOMERS_LAST_CPATH', 'customers_last_cpath');
define('TABLE_CUSTOMERS_LOGIN_IP_HISTORY', 'customers_login_ip_history');
define('TABLE_CUSTOMERS_GROUPS_PURCHASE_LIMIT', 'customers_groups_purchase_limit');
define('TABLE_CUSTOMERS_OTP', 'customers_otp');
define('TABLE_CUSTOMERS_SETTING', 'customers_setting');
define('TABLE_CUSTOMERS_SC_CART', 'customers_sc_cart');
define('TABLE_CUSTOMERS_PURCHASE_STATISTIC', 'customers_purchase_statistic');
define('TABLE_CUSTOMERS_UPKEEP', 'customers_upkeep');
define('TABLE_CUSTOMERS_VERIFICATION_DOCUMENT', 'customers_verification_document');
define('TABLE_CUSTOMERS_LOGIN_HISTORY', 'customers_login_history');
define('TABLE_CUSTOMERS_PREFERENCE', 'customers_preference');
define('TABLE_DEFINE_MAINPAGE', 'define_mainpage');
define('TABLE_DEFINE_PRODUCT_TYPE_PAGE', 'define_product_type_page');
define('TABLE_DINEROMAIL', 'dineromail');
define('TABLE_DINEROMAIL_STATUS_HISTORY', 'dineromail_status_history');
define('TABLE_EMAIL_DOMAIN_GROUPS_DOMAINS', 'email_domain_groups_domains');
define('TABLE_EVENTS', 'events');
define('TABLE_EVENTS_DESCRIPTION', 'events_description');
define('TABLE_EVENTS_OPTIONS', 'events_options');
define('TABLE_EVENTS_OPTIONS_DESCRIPTION', 'events_options_description');
define('TABLE_EVENTS_OPTIONS_VALUES', 'events_options_values');
define('TABLE_GAME_GENRE', 'game_genre');
define('TABLE_GAME_GENRE_DESCRIPTION', 'game_genre_description');
define('TABLE_GAME_GENRE_TO_CATEGORIES', 'game_genre_to_categories');
define('TABLE_GAME_LANGUAGE', 'game_language');
define('TABLE_GAME_LANGUAGE_DESCRIPTION', 'game_language_description');
define('TABLE_GAME_LANGUAGE_TO_CATEGORIES', 'game_language_to_categories');
define('TABLE_GAME_PLATFORM', 'game_platform');
define('TABLE_GAME_PLATFORM_DESCRIPTION', 'game_platform_description');
define('TABLE_GAME_PLATFORM_TO_CATEGORIES', 'game_platform_to_categories');
define('TABLE_GAME_REGION', 'game_region');
define('TABLE_GAME_REGION_DESCRIPTION', 'game_region_description');
define('TABLE_GAME_REGION_LANGUAGE_TO_CATEGORIES', 'game_region_language_to_categories');
define('TABLE_GAME_REGION_TO_LANGUAGE', 'game_region_to_language');
define('TABLE_GAME_TO_PUBLISHER', 'game_to_publisher');
define('TABLE_GIFT_CARD_REDEMPTION', 'gift_card_redemption');
define('TABLE_GEO_ZONES', 'geo_zones');
define('TABLE_GLOBAL_COLLECT', 'global_collect');
define('TABLE_GLOBAL_COLLECT_STATUS_HISTORY', 'global_collect_status_history');
define('TABLE_GOOGLE_WALLET', 'google_wallet');
define('TABLE_GOOGLE_WALLET_STATUS_HISTORY', 'google_wallet_status_history');
define('TABLE_GUDANG_VOUCHER_STATUS_HISTORY', 'gudang_voucher_status_history');
define('TABLE_GUDANG_VOUCHER', 'gudang_voucher');
define('TABLE_INDOMOG', 'indomog');
define('TABLE_INDOMOG_STATUS_HISTORY', 'indomog_status_history');
define('TABLE_INDOMOG_DTU_LOGS', 'indomog_dtu_logs');
define('TABLE_INFOLINKS', 'infolinks');
define('TABLE_INFOLINKS_CONTENTS', 'infolinks_contents');
define('TABLE_INFOLINKS_GROUPS', 'infolinks_groups');
define('TABLE_KUAIQIAN', 'kuaiqian');
define('TABLE_KUAIQIAN_PAYMENT_STATUS_HISTORY', 'kuaiqian_payment_status_history');
define('TABLE_LANGUAGES', 'languages');
define('TABLE_LOCKING', 'locking');
define('TABLE_LOG_TABLE', 'log_table');
define('TABLE_LOG_API_RESTOCK', 'log_api_restock');
define('TABLE_LOG_SES_BOUNCE', 'log_ses_bounce');
define('TABLE_MANUFACTURERS', 'manufacturers');
define('TABLE_MANUFACTURERS_INFO', 'manufacturers_info');
define('TABLE_MAXMIND_HISTORY', 'maxmind_history');  // Maxmind Score
define('TABLE_MAXMIND_TELEPHONE_IDENTIFICATION', 'maxmind_telephone_identification'); // Maxmind
define('TABLE_MAYBANK', 'maybank');
define('TABLE_MAYBANK_PAYMENT_STATUS_HISTORY', 'maybank_payment_status_history');
define('TABLE_MAZOOMA', 'mazooma');
define('TABLE_MAZOOMA_STATUS_HISTORY', 'mazooma_status_history');
define('TABLE_SMART2PAY', 'smart2pay');
define('TABLE_SMART2PAY_STATUS_HISTORY', 'smart2pay_status_history');
define('TABLE_SMART2PAY_GLOBALPAY', 'smart2pay_globalpay');
define('TABLE_SMART2PAY_GLOBALPAY_STATUS_HISTORY', 'smart2pay_globalpay_status_history');
define('TABLE_ONECARDV2_STATUS_HISTORY', 'onecardv2_status_history');
define('TABLE_ONECARDV2', 'onecardv2');
define('TABLE_PAYNEARME', 'paynearme');
define('TABLE_PAYNEARME_ORDER', 'paynearme_order');
define('TABLE_PAYNEARME_CUSTOMER', 'paynearme_customer');
define('TABLE_PAYNEARME_PAYMENT_INFO', 'paynearme_payment_info');
define('TABLE_PAYNEARME_STATUS_HISTORY', 'paynearme_status_history');
define('TABLE_MOBILE_MONEY', 'mobile_money');
define('TABLE_MOBILE_MONEY_HISTORY', 'mobile_money_history');
define('TABLE_MOZCOM', 'mozcom');
define('TABLE_NEWSLETTERS_GROUPS', 'newsletters_groups');
define('TABLE_ONECARD', 'onecard');
define('TABLE_ONECARD_STATUS_HISTORY', 'onecard_status_history');
define('TABLE_ORDERS', 'orders');
define('TABLE_ORDERS_CANCEL_REQUEST', 'orders_cancel_request');
define('TABLE_ORDERS_COMMENTS', 'orders_comments');
define('TABLE_ORDERS_COMPENSATE_PRODUCTS', 'orders_compensate_products');
define('TABLE_ORDERS_DELIVERY_QUEUE', 'orders_delivery_queue');
define('TABLE_ORDERS_EXTRA_INFO', 'orders_extra_info');
define('TABLE_ORDERS_NOTIFICATION', 'orders_notification');
define('TABLE_ORDERS_PRODUCTS', 'orders_products');
define('TABLE_ORDERS_PRODUCTS_EXTRA_INFO', 'orders_products_extra_info');
define('TABLE_ORDERS_PRODUCTS_ATTRIBUTES', 'orders_products_attributes');
define('TABLE_ORDERS_PRODUCTS_DOWNLOAD', 'orders_products_download');
define('TABLE_ORDERS_PRODUCTS_ETA', 'orders_products_eta');
define('TABLE_ORDERS_PRODUCTS_HISTORY', 'orders_products_history');
define('TABLE_ORDERS_STATUS', 'orders_status');
define('TABLE_ORDERS_STATUS_HISTORY', 'orders_status_history');
define('TABLE_ORDERS_STATUS_STAT', 'orders_status_stat');
define('TABLE_ORDERS_LOG_TABLE', 'orders_log_table');
define('TABLE_ORDERS_TAG', 'orders_tag');
define('TABLE_ORDERS_TOTAL', 'orders_total');
define('TABLE_IP_LIST_HISTORY', 'ip_list_history');
define('TABLE_IP_TAGS_STATS', 'ip_tags_stats');
define('TABLE_PAGE_VIEW_IP_LIST', 'page_view_ip_list');
define('TABLE_PAYMENT_EXTRA_INFO', 'payment_extra_info');
define('TABLE_PAYMENT_EGOLD', 'egold');
define('TABLE_PAYMENT_EGOLD_CURRENCIES', 'egold_currencies');
define('TABLE_PAYMENT_GATEWAY_INSTANCE', 'payment_gateway_instance');
define('TABLE_PAYMENT_IPAY88', 'ipay88');
define('TABLE_PAYMENT_IPAY88_PAYMENT_STATUS_HISTORY', 'ipay88_payment_status_history');
define('TABLE_PAYMENT_MONEYBOOKERS', 'moneybookers');
define('TABLE_PAYMENT_MONEYBOOKERS_COUNTRIES', 'moneybookers_countries');
define('TABLE_PAYMENT_MONEYBOOKERS_CURRENCIES', 'moneybookers_currencies');
define('TABLE_PAYMENT_MONEYBOOKERS_STATUS_HISTORY', 'moneybookers_payment_status_history');
define('TABLE_PAYMENT_FEES', 'payment_fees');
define('TABLE_PAYMENT_FEES_SC', 'payment_fees_sc');
define('TABLE_PAYMENT_METHODS', 'payment_methods');
define('TABLE_PAYMENT_METHODS_FIELDS', 'payment_methods_fields');
define('TABLE_PAYMENT_WEBMONEY', 'webmoney');
define('TABLE_PAYMENT_METHODS_TYPES', 'payment_methods_types');
define('TABLE_PAYMENT_METHODS_TYPES_DESCRIPTION', 'payment_methods_types_description');
define('TABLE_PAYMENT_METHODS_DESCRIPTION', 'payment_methods_description');
define('TABLE_PAYMENT_METHODS_STATUS_DESCRIPTION', 'payment_methods_status_description');
define('TABLE_PAYMENT_METHODS_INSTANCE', 'payment_methods_instance');
define('TABLE_PAYMENT_METHODS_INSTANCE_SETTING', 'payment_methods_instance_setting');
define('TABLE_PAYMENT_METHODS_OUTGOING_INSTANCE', 'payment_methods_outgoing_instance');
define('TABLE_PAYMENT_METHODS_OUTGOING_INSTANCE_SETTING', 'payment_methods_outgoing_instance_setting');
define('TABLE_PAYMENT_CONFIGURATION_INFO', 'payment_configuration_info');
define('TABLE_PAYMENT_CONFIGURATION_INFO_DESCRIPTION', 'payment_configuration_info_description');
define('TABLE_PAYPALIPN_TXN', 'paypalipn_txn'); // PAYPALIPN
define('TABLE_PAYPALEC', 'paypalec');
define('TABLE_PAYPALEC_STATUS_HISTORY', 'paypalec_status_history');
define('TABLE_PAYPALEC_REFERENCE_TRANSACTION', 'paypalec_reference_transaction'); 
define('TABLE_PAYPALEC_REFERENCE_TRANSACTION_HISTORY', 'paypalec_reference_transaction_history');
define('TABLE_PHONE_VERIFICATION_LIMIT', 'phone_verification_limit');
define('TABLE_POLLS_QUESTIONS', 'polls_questions');
define('TABLE_POLLS_QUESTIONS_ANSWERS', 'polls_questions_answers');
define('TABLE_POLLS_QUESTIONS_OPTIONS', 'polls_questions_options');
define('TABLE_POLLS_COMMENTS', 'polls_comments');
define('TABLE_PRODUCTS', 'products');
define('TABLE_PRODUCTS_ATTRIBUTES', 'products_attributes');
define('TABLE_PRODUCTS_ATTRIBUTES_DOWNLOAD', 'products_attributes_download');
define('TABLE_PRODUCTS_BUNDLES', 'products_bundles');
define('TABLE_PRODUCTS_CHECKOUT_SETTING', 'products_checkout_setting');
define('TABLE_PRODUCTS_CURRENCY_PRICES', 'products_currency_prices');
define('TABLE_PRODUCTS_DELIVERY_INFO', 'products_delivery_info');
define('TABLE_PRODUCTS_DELIVERY_MODE', 'products_delivery_mode');
define('TABLE_PRODUCTS_DESCRIPTION', 'products_description');
define('TABLE_PRODUCTS_GROUPS', 'products_groups'); //Separate Pricing per Customer Mod
define('TABLE_PRODUCTS_LOW_STOCK', 'products_low_stock');
define('TABLE_PRODUCTS_NOTIFICATIONS', 'products_notifications');
define('TABLE_PRODUCTS_OPTIONS', 'products_options');
define('TABLE_PRODUCTS_OPTIONS_VALUES', 'products_options_values');
define('TABLE_PRODUCTS_OPTIONS_VALUES_TO_PRODUCTS_OPTIONS', 'products_options_values_to_products_options');
define('TABLE_PRODUCTS_PROMOTION', 'products_promotion');
define('TABLE_PRODUCTS_PROMOTION_DESCRIPTION', 'products_promotion_description');
define('TABLE_PRODUCTS_TO_CATEGORIES', 'products_to_categories');
define('TABLE_PRODUCTS_XSELL', 'products_xsell');  // Added for Xsell Products Mod
define('TABLE_PROMOTIONS_ORDERS_LOG', 'promotions_orders_log');
define('TABLE_STORE_POINTS_HISTORY', 'store_points_history');
define('TABLE_STORE_POINTS_REDEEM', 'store_points_redeem');
define('TABLE_STORE_POINTS_REDEEM_HISTORY', 'store_points_redeem_history');
define('TABLE_REVIEWS', 'reviews');
define('TABLE_REVIEWS_DESCRIPTION', 'reviews_description');
define('TABLE_RHB', 'rhb');
define('TABLE_RHB_PAYMENT_STATUS_HISTORY', 'rhb_payment_status_history');
define('TABLE_SALES_ACTIVITIES', 'sales_activities');
define('TABLE_SEARCH_KEYWORDS_LOG', 'search_keywords_log');
define('TABLE_SESSIONS', 'sessions');
define('TABLE_SEARCH_PRODUCTS', 'search_products');
define('TABLE_SHIPPING_CLASSES', 'shipping_classes');
define('TABLE_SHIPPING_CLASSES_VALUES', 'shipping_classes_values');
define('TABLE_SMS_REPORT', 'sms_report');
define('TABLE_SPECIALS', 'specials');
define('TABLE_SSO_TOKEN', 'sso_token');
define('TABLE_STATUS_CONFIGURATION', 'status_configuration');
define('TABLE_STORE_ACCOUNT_BALANCE', 'store_account_balance');
define('TABLE_STORE_ACCOUNT_COMMENTS', 'store_account_comments');
define('TABLE_STORE_ACCOUNT_HISTORY', 'store_account_history');
define('TABLE_STORE_CREDIT_CONVERSION_HISTORY', 'store_credit_conversion_history');
define('TABLE_STORE_CREDIT_HISTORY', 'store_credit_history');
define('TABLE_STORE_PAYMENT_ACCOUNT_BOOK', 'store_payment_account_book');
define('TABLE_STORE_PAYMENT_ACCOUNT_BOOK_DETAILS', 'store_payment_account_book_details');
define('TABLE_STORE_PAYMENTS_STATUS', 'store_payments_status');
define('TABLE_STORE_PAYMENTS', 'store_payments');
define('TABLE_STORE_PAYMENTS_HISTORY', 'store_payments_history');
define('TABLE_STORE_PAYMENTS_DETAILS', 'store_payments_details');
define('TABLE_STORE_POINTS', 'store_points');
define('TABLE_STORE_POINT_HISTORY', 'store_point_history');
define('TABLE_STORE_REFUND', 'store_refund');
define('TABLE_STORE_REFUND_HISTORY', 'store_refund_history');
define('TABLE_TAX_CLASS', 'tax_class');
define('TABLE_TAX_RATES', 'tax_rates');
define('TABLE_TEMP_PROCESS', 'temp_process');
define('TABLE_THEMES', 'themes');
define('TABLE_THEMES_TO_CATEGORIES', 'themes_to_categories');
define('TABLE_USER_FAVOURITE_PRODUCTS', 'user_favourite_products');
define('TABLE_USER_FLAGS', 'user_flags');
define('TABLE_USER_SETTING', 'user_setting');
define('TABLE_VERIFICATION_DOC_LOG', 'customers_verification_document_log');
define('TABLE_WHOS_ONLINE', 'whos_online');
define('TABLE_ZONES', 'zones');
define('TABLE_ZONES_TO_GEO_ZONES', 'zones_to_geo_zones');

// data pool
define('TABLE_DATA_POOL', 'data_pool');
define('TABLE_DATA_POOL_LEVEL', 'data_pool_level');
define('TABLE_DATA_POOL_LEVEL_TAGS', 'data_pool_level_tags');
define('TABLE_DATA_POOL_OPTIONS', 'data_pool_options');
define('TABLE_DATA_POOL_OPTIONS_VALUES', 'data_pool_options_values');
define('TABLE_DATA_POOL_REF', 'data_pool_ref');
define('TABLE_DATA_POOL_TEMPLATE', 'data_pool_template');
define('TABLE_BRACKETS_TAGS', 'brackets_tags');
define('TABLE_BRACKETS', 'brackets');
define('TABLE_CUSTOM_PRODUCTS_TYPE', 'custom_products_type');
define('TABLE_CUSTOM_PRODUCTS_TYPE_CHILD', 'custom_products_type_child');
define('TABLE_CUSTOM_PRODUCTS_TYPE_CHILD_LANG', 'custom_products_type_child_lang');
define('TABLE_POOL_TEMPLATE_T0_CATEGORIES', 'data_pool_template_to_categories');
define('TABLE_CUSTOMERS_BASKET_CUSTOM', 'customers_basket_custom');
define('TABLE_ORDERS_CUSTOM_PRODUCTS', 'orders_custom_products');
define('TABLE_GAME_CHAR_LOG', 'game_char_log');

//buyback
define('TABLE_BUYBACK', 'buyback');
define('TABLE_BUYBACK_CATEGORIES', 'buyback_categories');
define('TABLE_BUYBACK_REQUEST', 'buyback_request');
define('TABLE_BUYBACK_REQUEST_GROUP', 'buyback_request_group');
define('TABLE_BUYBACK_BASKET', 'buyback_basket');
define('TABLE_BUYBACK_BRACKET', 'buyback_bracket');
define('TABLE_BUYBACK_BRACKET_TAGS', 'buyback_bracket_tags');
define('TABLE_BUYBACK_SETTING', 'buyback_setting');
define('TABLE_BUYBACK_GROUPS', 'buyback_groups');
define('TABLE_BUYBACK_GROUPS_TAGS', 'buyback_groups_tags');
define('TABLE_BUYBACK_GROUPS_TAGS_INFO', 'buyback_groups_tags_info');
define('TABLE_BUYBACK_GROUPS_TO_CATEGORIES', 'buyback_groups_to_categories');
define('TABLE_BUYBACK_PRODUCTS', 'buyback_products');
define('TABLE_BUYBACK_STATUS', 'buyback_status');
define('TABLE_BUYBACK_STATUS_HISTORY', 'buyback_status_history');
define('TABLE_SUPPLIER_ORDER_LISTS', 'supplier_order_lists');
define('TABLE_SUPPLIER_ORDER_LISTS_PRODUCTS', 'supplier_order_lists_products');
define('TABLE_RESTOCK_CHARACTER_SETS_TO_CATEGORIES', 'restock_character_sets_to_categories');
define('TABLE_RESTOCK_CHARACTER_INFO', 'restock_character_info');
define('TABLE_RESTOCK_CHARACTER_SETS', 'restock_character_sets');

define('TABLE_API_LOG', 'api_log');
define('TABLE_ORDERS_TOP_UP', 'orders_top_up');
define('TABLE_ORDERS_TOP_UP_REMARK', 'orders_top_up_remark');
define('TABLE_PUBLISHERS', 'publishers');
define('TABLE_PUBLISHERS_GAMES', 'publishers_games');
define('TABLE_PUBLISHERS_GAMES_CONFIGURATION', 'publishers_games_configuration');
define('TABLE_CUSTOMERS_TOP_UP_INFO', 'customers_top_up_info');
define('TABLE_PUBLISHERS_CONFIGURATION', 'publishers_configuration');
define('TABLE_PUBLISHERS_PRODUCTS', 'publishers_products');
define('TABLE_TOP_UP_INFO', 'top_up_info');
define('TABLE_TOP_UP_INFO_LANG', 'top_up_info_lang');
define('TABLE_TOP_UP_INFO_TYPE', 'top_up_info_type');
define('TABLE_TOP_UP_QUEUE', 'top_up_queue');

//start VIP module
define('TABLE_BUYBACK_ORDER_INFO', 'buyback_order_info');
define('TABLE_CUSTOMERS_VIP', 'customers_vip');
define('TABLE_VIP_REGION_GROUP', 'vip_region_group');
define('TABLE_VIP_RANK', 'vip_rank');
define('TABLE_VIP_SUPPLIER_GROUPS', 'vip_supplier_groups');
define('TABLE_VIP_ORDER_ALLOCATION', 'vip_order_allocation');
define('TABLE_VIP_PRODUCTION_HISTORY', 'vip_production_history');
define('TABLE_VIP_STOCK_HISTORY', 'vip_stock_history');
define('TABLE_VIP_SUPPLIER_INVENTORY_LOGS', 'vip_supplier_inventory_logs');
define('TABLE_VIP_SUPPLIER_INVENTORY', 'vip_supplier_inventory');
define('TABLE_VIP_SUPPLIER_SETTING', 'vip_supplier_setting');
define('TABLE_VIP_RULES', 'vip_rules');
//end VIP module


define('TABLE_USER_COMMENTS', 'user_comments');
define('TABLE_CUSTOMERS_REMARKS_HISTORY', 'customers_remarks_history');

// Paypal E-mail Address Verification
define('TABLE_CUSTOMERS_PAYMENT_EMAILS', 'customers_payment_emails');

define('TABLE_CUSTOMERS_INFO_VERIFICATION', 'customers_info_verification');

//custom product
define('TABLE_SUPPLIER_TASKS_SETTING', 'supplier_tasks_setting');
define('TABLE_SUPPLIER_TASKS_ALLOCATION', 'supplier_tasks_allocation');

define('TABLE_GAME_CHAR', 'game_char');
define('TABLE_GAME_CHAR_HISTORY', 'game_char_history');
define('TABLE_CHAR_ITEM_HISTORY', 'char_item_history');
define('TABLE_CHAR_SKILL_HISTORY', 'char_skill_history');
define('TABLE_CHAR_QUEST_HISTORY', 'char_quest_history');
define('TABLE_CHAR_HONOR_HISTORY', 'char_honor_history');
define('TABLE_CHAR_ITEM_STORAGE', 'char_item_storage');
define('TABLE_CHAR_REPUTATION_HISTORY', 'char_reputation_history');
define('TABLE_CHAR_REPUTATION_DETAIL', 'char_reputation_detail');
define('TABLE_CHAR_PET', 'char_pet');
define('TABLE_CHAR_PET_HISTORY', 'char_pet_history');

define('TABLE_ZONES_INFO', 'zones_info');

// # 0000024 - My Account Phase II @ ************: Tables for Easy-signup's Instant Messaging
define('TABLE_INSTANT_MESSAGE_ACCOUNTS', 'instant_message_accounts');
define('TABLE_INSTANT_MESSAGE_TYPE', 'instant_message_type');

// # 0000024 - My Account Phase II @ ************: Tables for Secret Questions
define('TABLE_CUSTOMERS_SECURITY_QUESTIONS', 'customers_security_questions');
define('TABLE_CUSTOMERS_SECURITY', 'customers_security');

// # 0000075 - Viral Inviter @ ************
define('TABLE_INVITER_IMPORTS', 'inviter_imports');
define('TABLE_INVITER_MESSAGES', 'inviter_messages');
define('TABLE_INVITER_CONTACTS', 'inviter_contacts');
define('TABLE_INVITER_IGNORE_LIST', 'inviter_ignore_list');

// Supplier
define('TABLE_SUPPLIER', 'supplier');
define('TABLE_SUPPLIER_GROUPS', 'supplier_groups');
define('TABLE_SUPPLIER_HISTORY', 'supplier_history');
define('TABLE_SUPPLIER_HISTORY_GROUP', 'supplier_history_group');
define('TABLE_SUPPLIER_LANGUAGES', 'supplier_languages');
define('TABLE_SUPPLIER_LIST_TIME_SETTING', 'supplier_list_time_setting');
define('TABLE_SUPPLIER_LIST_STATUS', 'supplier_list_status');
define('TABLE_SUPPLIER_ORDER_LISTS_HISTORY', 'supplier_order_lists_history');
define('TABLE_SUPPLIER_PAYMENTS', 'supplier_payments');
define('TABLE_SUPPLIER_PAYMENTS_HISTORY', 'supplier_payments_history');
define('TABLE_SUPPLIER_PAYMENTS_ORDERS', 'supplier_payments_orders');
define('TABLE_SUPPLIER_PAYMENTS_STATUS', 'supplier_payments_status');
define('TABLE_SUPPLIER_PREFERENCES', 'supplier_preferences');
define('TABLE_SUPPLIER_PRICING', 'supplier_pricing');
define('TABLE_SUPPLIER_PRICING_SETTING', 'supplier_pricing_setting');
define('TABLE_SUPPLIER_PURCHASE_MODES', 'supplier_purchase_modes');
define('TABLE_SUPPLIER_TASKS_ALLOCATION_HISTORY', 'supplier_tasks_allocation_history');

define('TABLE_BANNERS_RESOURCES', 'banners_resources');

// Threat Metrix
define('TABLE_API_TM_BROWSER', 'api_tm_browser');
define('TABLE_API_TM_DEVICE', 'api_tm_device');
define('TABLE_API_TM_PROXY_IP', 'api_tm_proxy_ip');
define('TABLE_API_TM_RISK_SUMMARY_N_POLICY', 'api_tm_risk_summary_n_policy');
define('TABLE_API_TM_TRANSACTION_IDENTIFIER', 'api_tm_transaction_identifier');
define('TABLE_API_TM_TRUE_IP', 'api_tm_true_ip');
define('TABLE_API_TM_QUERY_QUEUE', 'api_tm_query_queue');
define('TABLE_API_TM_FUZZY_DEVICE', 'api_tm_fuzzy_device');

// Kount Query Queue (The rest share with Threat Metrix
define('TABLE_API_KOUNT_QUERY_QUEUE', 'api_kount_query_queue');

// HLA
define('TABLE_ORDERS_PRODUCTS_ITEM', 'orders_products_item');
define('TABLE_PRODUCTS_HLA', 'products_hla');
define('TABLE_PRODUCTS_HLA_ATTRIBUTES', 'products_hla_attributes');
define('TABLE_PRODUCTS_HLA_CHARACTERS', 'products_hla_characters');
define('TABLE_PRODUCTS_HLA_DESCRIPTION', 'products_hla_description');
define('TABLE_PRODUCTS_TEMPLATES', 'products_templates');
define('TABLE_PRODUCTS_SUPPLIER', 'products_supplier');
define('TABLE_CRON_HLA', 'cron_hla');
define('TABLE_PRODUCTS_TO_SUPPLIER', 'products_to_supplier');
define('TABLE_PRODUCTS_RSS_LINK', 'products_rss_link');
define('TABLE_HLA_TEMPLATE', 'products_hla_template');

// Orders Tax
define('TABLE_ORDERS_TAX_CONFIGURATION', 'orders_tax_configuration');
define('TABLE_ORDERS_TAX_CONFIGURATION_DESCRIPTION', 'orders_tax_configuration_description');

// C2C
define('TABLE_C2C_CONFIGURATION', 'c2c_configuration');

define('TABLE_CATEGORY_DISCOUNT_RULES', 'categories_discount_rules');
define('TABLE_CATEGORY_DISCOUNT_LIST', 'categories_discount_list');
define('TABLE_CATEGORY_DISCOUNT_GROUP_RULES', 'categories_discount_group_rules');
?>
