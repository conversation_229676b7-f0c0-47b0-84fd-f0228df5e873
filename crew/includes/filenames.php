<?php
/*
  $Id: filenames.php,v 1.89 2014/12/29 12:19:11 weesiong Exp $
  
  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2003 osCommerce

  Released under the GNU General Public License
*/

  // BEGIN latest news
  define('FILENAME_LATEST_NEWS', 'latest_news.php');
  define('FILENAME_NEWS', 'news.php');
  define('FILENAME_ALL_PRODUCTS', 'all_products.php');
  
// define the content used in the project
  define('CONTENT_ACCOUNT', 'account');
  define('CONTENT_ACCOUNT_BUYBACK', 'account_buyback');
  define('CONTENT_ACCOUNT_EDIT', 'account_edit');
  define('CONTENT_ACCOUNT_HISTORY', 'account_history');
  define('CONTENT_ACCOUNT_HISTORY_INFO', 'account_history_info');
  define('CONTENT_ACCOUNT_NEWSLETTERS', 'account_newsletters');
  define('CONTENT_ACCOUNT_NOTIFICATIONS', 'account_notifications');
  define('CONTENT_ACCOUNT_PASSWORD', 'account_password');
  define('CONTENT_ACCOUNT_ACTIVATE', 'account_activate');
  define('CONTENT_ACCOUNT_PAYMENT_EDIT', 'account_payment_edit');
  define('CONTENT_ACCOUNT_STORE_CREDIT', 'account_store_credit');
  define('CONTENT_ACCOUNT_STORE_CREDIT_HISTORY', 'account_store_credit_history');
  define('CONTENT_ACCOUNT_STORE_CREDIT_TOPUP', 'account_store_credit_topup');
  define('CONTENT_ADDRESS_BOOK', 'address_book');
  define('CONTENT_ADDRESS_BOOK_PROCESS', 'address_book_process');
  define('CONTENT_ADVANCED_SEARCH', 'advanced_search');
  define('CONTENT_ADVANCED_SEARCH_RESULT', 'advanced_search_result');
  define('CONTENT_ALSO_PURCHASED_PRODUCTS', 'also_purchased_products');
  define('CONTENT_BBCALLBACK', 'bbcallback');
  define('CONTENT_BUYBACK', 'buyback');
  define('CONTENT_BUYBACK_ORDER_FORM', 'buyback_order_form');
  define('CONTENT_BUYBACK_REQUESTS_INFO', 'buyback_requests_info');
  define('CONTENT_CANCEL_ORDER', 'cancel_order');
  define('CONTENT_CHARACTER_PROFILE', 'character_profile');
  define('CONTENT_CHECKOUT_CONFIRMATION', 'checkout_confirmation');
  define('CONTENT_CHECKOUT_INFO', 'checkout_info');
  define('CONTENT_CHECKOUT_PAYMENT', 'checkout_payment');
  define('CONTENT_CHECKOUT_PAYMENT_ADDRESS', 'checkout_payment_address');
  define('CONTENT_CHECKOUT_PROCESS', 'checkout_process');
  define('CONTENT_CHECKOUT_SHIPPING', 'checkout_shipping');
  define('CONTENT_CHECKOUT_SHIPPING_ADDRESS', 'checkout_shipping_address');
  define('CONTENT_CHECKOUT_SUCCESS', 'checkout_success');
  define('CONTENT_CONDITIONS', 'conditions');
  define('CONTENT_CUSTOMER_SUPPORT', 'customer_support');
  define('CONTENT_COOKIE_USAGE', 'cookie_usage');
  define('CONTENT_CONTACT_US', 'contact_us');
  define('CONTENT_CREATE_ACCOUNT', 'create_account');
  define('CONTENT_CREATE_ACCOUNT_SUCCESS', 'create_account_success');
  define('CONTENT_CUSTOM_PRODUCT_INFO', 'custom_product_info');
  define('CONTENT_DOWN_FOR_MAINT', 'down_for_maintenance');
  define('CONTENT_EVENT', 'event');
  define('CONTENT_ERROR_PAGE_NOT_FOUND', 'page_not_found');
  define('CONTENT_ERROR_REGION', 'error_region');
  define('CONTENT_EXPRESS_LOGIN', 'express_login');
  define('CONTENT_FACEBOOK_CONNECT', 'facebook_connect');
  define('CONTENT_GIFT_CARD', 'gift_card');
  define('CONTENT_GV_FAQ', 'gv_faq');
  define('CONTENT_GV_REDEEM', 'gv_redeem');
  define('CONTENT_GV_SEND', 'gv_send');  
  define('CONTENT_HELP_CENTER', 'help_center');
  define('CONTENT_ITEM_REQUEST', 'item_request');
  define('CONTENT_INDEX_DEFAULT', 'index_default');
  define('CONTENT_INDEX_NESTED', 'index_nested');
  define('CONTENT_INDEX_PRODUCTS', 'index_products');
  define('CONTENT_SEARCH_ALL_GAMES', 'search_all_games');
  define('CONTENT_INDEX_PRODUCT_TYPE', 'index_product_type');
  define('CONTENT_INDEX_PWL_PWL_PRODUCTS', 'index_pwl_products');
  define('CONTENT_INDEX_GAME_CURRENCY_PRODUCTS', 'index_game_currency_products');
  define('CONTENT_INDEX_CDKEY_PRODUCTS', 'index_cdkey_products');
  define('CONTENT_INDEX_STORE_CREDIT_PRODUCTS', 'index_store_credit_products');
  define('CONTENT_INDEX_HLA_PRODUCTS', 'index_hla_products');
  define('CONTENT_INFO_SHOPPING_CART', 'info_shopping_cart');
  define('CONTENT_INFO_NEWS', 'info_news');
  define('CONTENT_INFO_PROMOTION', 'info_promotions');
  define('CONTENT_INFO_FAQ', 'info_faq');
  define('CONTENT_INFO_TOS', 'info_tos');
  define('CONTENT_INFO_CONTACT', 'info_contact');
  define('CONTENT_INFO_DISCLAIMER', 'info_disclaimer');
  define('CONTENT_INFOLINKS', 'content');
  define('CONTENT_LINKS', 'links');
  define('CONTENT_LOGIN', 'login');
  define('CONTENT_LOGOFF', 'logoff');
  define('CONTENT_MY_FAVOURITE_LINKS', 'my_favourite_links');
  define('CONTENT_MY_PAYMENT_HISTORY', 'my_payment_history');
  define('CONTENT_MY_ORDER_HISTORY', 'my_order_history');
  define('CONTENT_NEW_PRODUCTS', 'new_products');
  define('CONTENT_NEWS', 'news');
  define('CONTENT_SEARCH_LATEST_NEWS', 'search_latest_news');
  define('CONTENT_REDEEM_POINT', 'redeem_point');
  define('CONTENT_RECEIVE_PAYMENT_INFO', 'submit_payment_info');
  define('CONTENT_PASSWORD_FORGOTTEN', 'password_forgotten');
  define('CONTENT_PAYMENT_CONFIRMATION', 'payment_confirmation');
  define('CONTENT_POPUP_IMAGE', 'popup_image');
  define('CONTENT_POPUP_SEARCH_HELP', 'popup_search_help');
  define('CONTENT_PRICE_MATCH', 'price_match');
  define('CONTENT_PRIVACY', 'privacy');
  define('CONTENT_PROMO', 'promo');
  define('CONTENT_POLL_RESULT', 'poll_result');
  define('CONTENT_POLLING', 'polling');
  define('CONTENT_PRODUCT_INFO', 'product_info');
  define('CONTENT_PRODUCT_LISTING', 'product_listing');
  define('CONTENT_PRODUCT_REVIEWS', 'product_reviews');
  define('CONTENT_PRODUCT_REVIEWS_INFO', 'product_reviews_info');
  define('CONTENT_PRODUCT_REVIEWS_WRITE', 'product_reviews_write');
  define('CONTENT_PRODUCTS_NEW', 'products_new');
  define('CONTENT_REVIEWS', 'reviews');
  define('CONTENT_SC_CHECKOUT_PAYMENT', 'sc_checkout_payment');
  define('CONTENT_SET_SECRET_QNA', 'set_secret_qna');
  define('CONTENT_SHIPPING', 'shipping');
  define('CONTENT_SHOPPING_CART', 'shopping_cart');
  define('CONTENT_SPECIALS', 'specials');
  define('CONTENT_SSL_CHECK', 'ssl_check');
  define('CONTENT_TELL_A_FRIEND', 'tell_a_friend');
  define('CONTENT_UPCOMING_PRODUCTS', 'upcoming_products');
  define('CONTENT_PRINTABLE_CATALOG', 'catalog_products_with_images');
  define('CONTENT_ALLPRODS', 'allprods');
  
  define('FILENAME_ABOUT_US','about_us.php');

// MaxiDVD Added Line For WYSIWYG HTML Area: BOF
  define('FILENAME_DEFINE_MAINPAGE', 'mainpage.php');
// MaxiDVD Added Line For WYSIWYG HTML Area: EOF

// define the filenames used in the project

  define('FILENAME_SEARCH_LATEST_NEWS', CONTENT_SEARCH_LATEST_NEWS . '.php');
  define('FILENAME_SEARCH_ALL_GAMES', CONTENT_SEARCH_ALL_GAMES . '.php');
  define('FILENAME_SC_CHECKOUT_PAYMENT', CONTENT_SC_CHECKOUT_PAYMENT . '.php');
  define('FILENAME_ACCOUNT', CONTENT_ACCOUNT . '.php');
  define('FILENAME_ACCOUNT_BUYBACK', CONTENT_ACCOUNT_BUYBACK . '.php');
  define('FILENAME_ACCOUNT_EDIT', CONTENT_ACCOUNT_EDIT . '.php');
  define('FILENAME_ACCOUNT_HISTORY', CONTENT_ACCOUNT_HISTORY . '.php');
  define('FILENAME_ACCOUNT_HISTORY_INFO', CONTENT_ACCOUNT_HISTORY_INFO . '.php');
  define('FILENAME_ACCOUNT_NEWSLETTERS', CONTENT_ACCOUNT_NEWSLETTERS . '.php');
  define('FILENAME_ACCOUNT_NOTIFICATIONS', CONTENT_ACCOUNT_NOTIFICATIONS . '.php');
  define('FILENAME_ACCOUNT_PASSWORD', CONTENT_ACCOUNT_PASSWORD . '.php');
  define('FILENAME_ACCOUNT_ACTIVATE', CONTENT_ACCOUNT_ACTIVATE . '.php');
  define('FILENAME_ACCOUNT_PAYMENT_EDIT', CONTENT_ACCOUNT_PAYMENT_EDIT . '.php');
  define('FILENAME_ACCOUNT_STORE_CREDIT', CONTENT_ACCOUNT_STORE_CREDIT . '.php');
  define('FILENAME_ACCOUNT_STORE_CREDIT_HISTORY', CONTENT_ACCOUNT_STORE_CREDIT_HISTORY . '.php');
  define('FILENAME_ACCOUNT_STORE_CREDIT_TOPUP', CONTENT_ACCOUNT_STORE_CREDIT_TOPUP . '.php');
  define('FILENAME_ADDRESS_BOOK', CONTENT_ADDRESS_BOOK . '.php');
  define('FILENAME_ADDRESS_BOOK_PROCESS', CONTENT_ADDRESS_BOOK_PROCESS . '.php');
  define('FILENAME_ADVANCED_SEARCH', CONTENT_ADVANCED_SEARCH . '.php');
  define('FILENAME_ADVANCED_SEARCH_RESULT', CONTENT_ADVANCED_SEARCH_RESULT . '.php');
  define('FILENAME_ALIPAY_IPN', 'alipay_ipn.php');
  define('FILENAME_ALSO_PURCHASED_PRODUCTS', CONTENT_ALSO_PURCHASED_PRODUCTS . '.php');
  define('FILENAME_BBCALLBACK', CONTENT_BBCALLBACK . '.php');
  define('FILENAME_BUYBACK', CONTENT_BUYBACK . '.php');
  define('FILENAME_BUYBACK_ORDER_FORM', CONTENT_BUYBACK_ORDER_FORM.'.php');
  define('FILENAME_BUYBACK_REQUESTS_INFO', CONTENT_BUYBACK_REQUESTS_INFO . '.php');
  define('FILENAME_CATALOG_PRODUCTS_WITH_IMAGES', 'catalog_products_with_images.php'); // CATALOG_PRODUCTS_WITH_IMAGES_mod
  define('FILENAME_CANCEL_ORDER', CONTENT_CANCEL_ORDER . '.php');
  define('FILENAME_CHECKOUT_CONFIRMATION', CONTENT_CHECKOUT_CONFIRMATION . '.php');
  define('FILENAME_CHECKOUT_INFO', CONTENT_CHECKOUT_INFO . '.php');
  define('FILENAME_CHECKOUT_PAYMENT', CONTENT_CHECKOUT_PAYMENT . '.php');
  define('FILENAME_CHECKOUT_PAYMENT_ADDRESS', CONTENT_CHECKOUT_PAYMENT_ADDRESS . '.php');
  define('FILENAME_CHECKOUT_PROCESS', CONTENT_CHECKOUT_PROCESS . '.php');
  define('FILENAME_CHECKOUT_SHIPPING', CONTENT_CHECKOUT_SHIPPING . '.php');
  define('FILENAME_CHECKOUT_SHIPPING_ADDRESS', CONTENT_CHECKOUT_SHIPPING_ADDRESS . '.php');
  define('FILENAME_CHECKOUT_SUCCESS', CONTENT_CHECKOUT_SUCCESS . '.php');
  define('FILENAME_CUSTOMER_SUPPORT', CONTENT_CUSTOMER_SUPPORT . '.php');
  define('FILENAME_CHECKOUT_XMLHTTP', 'checkout_xmlhttp.php');
  define('FILENAME_CONTACT_US', CONTENT_CONTACT_US . '.php');
  define('FILENAME_COUNTRY_LIST_XML', 'country_list.xml');
  define('FILENAME_EXPRESS_LOGIN', CONTENT_EXPRESS_LOGIN.'.php');
  define('FILENAME_FACEBOOK_CONNECT', CONTENT_FACEBOOK_CONNECT.'.php');
  define('FILENAME_PRICE_MATCH', CONTENT_PRICE_MATCH . '.php');
  define('FILENAME_IPAY_IPN', 'ipay_ipn.php');
  define('FILENAME_ITEM_REQUEST', CONTENT_ITEM_REQUEST . '.php');
  define('FILENAME_CIMB_IPN', 'cimb_ipn.php');
  define('FILENAME_CIMB_IPN_PROCESS', 'cimb_ipn_process.php');
  define('FILENAME_CONDITIONS', CONTENT_CONDITIONS . '.php');
  define('FILENAME_COOKIE_USAGE', CONTENT_COOKIE_USAGE . '.php');
  define('FILENAME_CREATE_ACCOUNT', CONTENT_CREATE_ACCOUNT . '.php');
  define('FILENAME_CREATE_ACCOUNT_SUCCESS', CONTENT_CREATE_ACCOUNT_SUCCESS . '.php');
  define('FILENAME_DEFAULT', 'index.php');
  define('FILENAME_DEFAULT_SPECIALS', 'default_specials.php');
  define('FILENAME_DOWNLOAD', 'download.php');
  define('FILENAME_EGOLD_IPN', 'egold_ipn.php');
  define('FILENAME_EVENT', CONTENT_EVENT . '.php');
  define('FILENAME_ERROR_PAGE_NOT_FOUND', CONTENT_ERROR_PAGE_NOT_FOUND . '.php');
  define('FILENAME_EXPRESS_CHECKOUT', 'express_checkout.php');
  define('FILENAME_GIFT_CARD', CONTENT_GIFT_CARD . '.php');
  define('FILENAME_GLOBAL_COLLECT', 'global_collect.php');
  define('FILENAME_HELP_CENTER', 'help_center.php');
  define('FILENAME_INFO_SHOPPING_CART', CONTENT_INFO_SHOPPING_CART . '.php');
  define('FILENAME_INFO_NEWS', CONTENT_INFO_NEWS . '.php');
  define('FILENAME_INFO_PROMOTION', CONTENT_INFO_PROMOTION . '.php');
  define('FILENAME_INFO_FAQ', CONTENT_INFO_FAQ . '.php');
  define('FILENAME_INFO_TOS', CONTENT_INFO_TOS . '.php');
  define('FILENAME_INFO_CONTACT', CONTENT_INFO_CONTACT . '.php');
  define('FILENAME_INFO_DISCLAIMER', CONTENT_INFO_DISCLAIMER . '.php');
  define('FILENAME_LINK', CONTENT_LINKS . '.php');
  define('FILENAME_LOGIN', CONTENT_LOGIN . '.php');
  define('FILENAME_LOGIN_POPUP', 'login_popup.php');
  define('FILENAME_LOGOFF', CONTENT_LOGOFF . '.php');
  define('FILENAME_MAIN_PAGE', 'main_page.php');
  define('FILENAME_MAZOOMA_IPN', 'mazooma_ipn.php');
  define('FILENAME_PAYNEARME_IPN', 'paynearme_ipn.php');
  define('FILENAME_SMART2PAY', 'smart2pay.php');
  define('FILENAME_SMART2PAY_IPN', 'smart2pay_ipn.php');
  define('FILENAME_MB_IPN', 'mb_ipn.php');
  define('FILENAME_MY_FAVOURITE_LINKS', CONTENT_MY_FAVOURITE_LINKS.'.php');
  define('FILENAME_MY_PAYMENT_HISTORY', CONTENT_MY_PAYMENT_HISTORY.'.php');
  define('FILENAME_MY_ORDER_HISTORY', CONTENT_MY_ORDER_HISTORY.'.php');
  define('FILENAME_NEW_PRODUCTS', CONTENT_NEW_PRODUCTS . '.php');
  define('FILENAME_ORDERS', 'orders.php');
  define('FILENAME_ADMIN_ORDERS', 'orders.php');
  define('FILENAME_ONECARD_CALLBACK', 'onecard_callback.php');
  define('FILENAME_PAGE', 'page.php');
  define('FILENAME_PASSWORD_FORGOTTEN', CONTENT_PASSWORD_FORGOTTEN . '.php');
  define('FILENAME_POPUP_IMAGE', CONTENT_POPUP_IMAGE . '.php');
  define('FILENAME_POPUP_SEARCH_HELP', CONTENT_POPUP_SEARCH_HELP . '.php');
  define('FILENAME_PRIVACY', CONTENT_PRIVACY . '.php');
  define('FILENAME_PROMO', CONTENT_PROMO . '.php');
  define('FILENAME_INFOLINKS', CONTENT_INFOLINKS . '.php');
  define('FILENAME_CUSTOM_PRODUCT_INFO', CONTENT_CUSTOM_PRODUCT_INFO . '.php');
  define('FILENAME_PAYMENT_CONFIRMATION',CONTENT_PAYMENT_CONFIRMATION . '.php');
  define('FILENAME_POLLING', CONTENT_POLLING . '.php');
  define('FILENAME_POLL_RESULT', CONTENT_POLL_RESULT . '.php');
  define('FILENAME_PRESTART_XMLHTTP', 'prestart_xmlhttp.php');
  define('FILENAME_PRESTART_JSON', 'prestart_json.php');
  define('FILENAME_PRODUCT_INFO', CONTENT_PRODUCT_INFO . '.php');
  define('FILENAME_PRODUCT_LISTING', CONTENT_PRODUCT_LISTING . '.php');
  define('FILENAME_PRODUCT_REVIEWS', CONTENT_PRODUCT_REVIEWS . '.php');
  define('FILENAME_PRODUCT_REVIEWS_INFO', CONTENT_PRODUCT_REVIEWS_INFO . '.php');
  define('FILENAME_PRODUCT_REVIEWS_WRITE', CONTENT_PRODUCT_REVIEWS_WRITE . '.php');
  define('FILENAME_PRODUCTS_NEW', CONTENT_PRODUCTS_NEW . '.php');
  define('FILENAME_REDEEM_POINT', CONTENT_REDEEM_POINT . '.php');
  define('FILENAME_REDIRECT', 'redirect.php');
  define('FILENAME_REVIEWS', CONTENT_REVIEWS . '.php');
  define('FILENAME_SC_CHECKOUT_PROCESS', 'sc_checkout_process.php');
  define('FILENAME_SET_SECRET_QNA', CONTENT_SET_SECRET_QNA . '.php');
  define('FILENAME_SHIPPING', CONTENT_SHIPPING . '.php');
  define('FILENAME_SHOPPING_CART', CONTENT_SHOPPING_CART . '.php');
  define('FILENAME_SPECIALS', CONTENT_SPECIALS . '.php');
  define('FILENAME_SSL_CHECK', CONTENT_SSL_CHECK . '.php');
  define('FILENAME_SHASSO', 'shasso.php');
  define('FILENAME_RECEIVE_PAYMENT_INFO', CONTENT_RECEIVE_PAYMENT_INFO . '.php');
  define('FILENAME_TELL_A_FRIEND', CONTENT_TELL_A_FRIEND . '.php');
  define('FILENAME_UPCOMING_PRODUCTS', CONTENT_UPCOMING_PRODUCTS . '.php');
  define('FILENAME_CHECKOUT_PAYPALIPN', 'checkout_paypalipn.php'); // PAYPALIPN
  define('FILENAME_WAITING_ROOM', 'waiting_room.php');
  define('FILENAME_WM_IPN', 'wm_ipn.php');
  define('FILENAME_WPCALLBACK', 'wpcallback.php');
  define('FILENAME_UPLOAD', 'upload.php');
  define('FILENAME_VIEW_PROFILE', 'view_profile.php');
  
  define('FILENAME_MAYBANKCALLBACK', 'maybankcallback.php');
  define('FILENAME_MOBILE_MONEY_CHECKOUT_PROCESS', 'mobile_money_checkout_process.php');
  define('FILENAME_KUAIQIANCALLBACK', 'kuaiqiancallback.php');
  define('FILENAME_KUAIQIAN_MERCHANT_CALLBACK', 'kuaiqian_merchant_callback.php');
  define('FILENAME_PAYNEARME', '/modules/payment/paynearme.php');
  
  define('FILENAME_URL_INDEX', 'c');
  define('FILENAME_URL_PRODUCT_INFO', 'p');
  define('FILENAME_URL_CUSTOM_PRODUCT_INFO', 'cp');
  define('FILENAME_URL_NEWS', 'n');
  define('FILENAME_URL_NEWS_ALL', 'a');
  define('FILENAME_URL_NEWS_PAGE', 'np');
  define('FILENAME_URL_INFO', 'i');
  define('FILENAME_URL_EXTENSION', '.ogm');
  define('FILENAME_USER_AGENT', 'user_agent.php');
  define('FILENAME_USER_AGENTS_CONFIG', 'user_agents_config.php');
  define('FILENAME_UI_SUBMIT', 'ui_submit.php');
  define('FILENAME_UI_READER', 'ui_reader.php');
  define('FILENAME_CHARACTER_PROFILE', 'character_profile.php');
  
// Lango and PopioWeb added for Affiliate Mod: BOF 
  define('CONTENT_AFFILIATE', 'affiliate_affiliate'); 
  define('CONTENT_AFFILIATE_BANNER', 'affiliate_banners'); 
  define('CONTENT_AFFILIATE_CLICKS', 'affiliate_clicks'); 
  define('CONTENT_AFFILIATE_CONTACT', 'affiliate_contact'); 
  define('CONTENT_AFFILIATE_DETAILS', 'affiliate_details'); 
  define('CONTENT_AFFILIATE_DETAILS_OK', 'affiliate_details_ok'); 
  define('CONTENT_AFFILIATE_FAQ', 'affiliate_faq'); 
  define('CONTENT_AFFILIATE_INFO', 'affiliate_info'); 
  define('CONTENT_AFFILIATE_LOGOUT', 'affiliate_logout'); 
  define('CONTENT_AFFILIATE_PASSWORD_FORGOTTEN', 'affiliate_password_forgotten'); 
  define('CONTENT_AFFILIATE_PAYMENT', 'affiliate_payment'); 
  define('CONTENT_AFFILIATE_SALES', 'affiliate_sales'); 
  define('CONTENT_AFFILIATE_SHOW_BANNER', 'affiliate_show_banner'); 
  define('CONTENT_AFFILIATE_SIGNUP', 'affiliate_signup'); 
  define('CONTENT_AFFILIATE_SIGNUP_OK', 'affiliate_signup_ok'); 
  define('CONTENT_AFFILIATE_SUMMARY', 'affiliate_summary'); 
  define('CONTENT_AFFILIATE_TERMS', 'affiliate_terms');

// Added for Xsell Products Mod
  define('FILENAME_XSELL_PRODUCTS', 'xsell_products.php');
  define('FILENAME_PRODUCT_LISTING_COL', 'product_listing_col.php');
//BEGIN allprods modification
  define('FILENAME_ALLPRODS', 'allprods.php');
//END allprods modification
  define('FILENAME_DYNAMIC_MOPICS', 'dynamic_mopics.php');

// Facebook
  define('FB_XD_RECEIVER', 'xd_receiver.htm');

// CMS Module
  define('FILENAME_CMS_CONTENT', 'cms_content.php');
  
// define the templatenames used in the project
  define('TEMPLATENAME_BOX', 'box.tpl.php');
  define('TEMPLATENAME_MAIN_PAGE', 'main_page.tpl.php');
  define('TEMPLATENAME_POPUP', 'popup.tpl.php');
  define('TEMPLATENAME_STATIC', 'static.tpl.php');
  
  define('CHECKOUT_VERIFICATION_INSTRUCTION', 'phone_verification_help.php');
  
//vip buyback start
define('CONTENT_MY_VIP_REGISTER_SERVER', 'vip_register_server');
define('CONTENT_MY_VIP_INVENTORY_UPDATE', 'vip_inventory');
define('CONTENT_MY_VIP_ORDERS_HISTORY', 'vip_orders_history');
define('CONTENT_MY_VIP_REPORT', 'vip_report');
//vip buyback end

//vip buyback start
define('FILENAME_MY_VIP_REGISTER_SERVER', CONTENT_MY_VIP_REGISTER_SERVER.'.php');
define('FILENAME_MY_VIP_INVENTORY_UPDATE', CONTENT_MY_VIP_INVENTORY_UPDATE.'.php');
define('FILENAME_MY_VIP_ORDERS_HISTORY', CONTENT_MY_VIP_ORDERS_HISTORY.'.php');
define('FILENAME_MY_VIP_REPORT', CONTENT_MY_VIP_REPORT.'.php');
//vip buyback end

// M#0000068 - Affiliate @200904211527
define('CONTENT_MY_AFFILIATE_OVERVIEW', 'my_affiliate');
define('CONTENT_MY_AFFILIATE_PANEL', 'panel');

define('FILENAME_MY_AFFILIATE_OVERVIEW', CONTENT_MY_AFFILIATE_OVERVIEW.'.php');
define('FILENAME_MY_AFFILIATE_PANEL', CONTENT_MY_AFFILIATE_PANEL.'.php');


// M#0000075 @ 200909041208 - Viral Inviter
define('CONTENT_INVITER', 'inviter');
define('CONTENT_OPENINVITER', 'openinviter');
define('CONTENT_INVITER_X', 'inviter_x');

define('FILENAME_INVITER', CONTENT_INVITER.'.php');
define('FILENAME_OPENINVITER', CONTENT_OPENINVITER . '.php');
define('FILENAME_INVITER_X', CONTENT_INVITER_X.'.php');

define('CONTENT_STATS_ORDERS_TRACKING', 'order_track');
define('FILENAME_STATS_ORDERS_TRACKING', CONTENT_STATS_ORDERS_TRACKING . '.php');

define('FILENAME_JS_JQUERY_IEPNGHACK', 'jquery.iepnghack.1.6.js');
define('FILENAME_JS_LAYOUT_OGM', 'layout_ogm2008.js?20130205');

define('CONTENT_VERIFICATION_SUBMISSION_FORM', 'verification_submission_form');

define('FILENAME_VERIFICATION_SUBMISSION_FORM', CONTENT_VERIFICATION_SUBMISSION_FORM.'.php');

?>