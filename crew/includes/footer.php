<?php
// SEO Link Data Object
$seolink_obj = array();

$menu_sql = "	SELECT * 
				FROM " . TABLE_CMS_MENU . " 
				WHERE cms_menu_type='2' 
					AND cms_menu_status = 1 
				ORDER BY cms_menu_sort_order ASC";
$menu_result = tep_db_query($menu_sql);

while ($menu_row = tep_db_fetch_array($menu_result)) {
	$cms_footer_content_array = array();
	$cms_footer_select_sql = "	SELECT cms_menu_lang_setting_key, cms_menu_lang_setting_key_value 
								FROM " . TABLE_CMS_MENU_VALUE . " 
								WHERE cms_menu_id = '" . tep_db_input($menu_row['cms_menu_id']) . "' 
									AND cms_menu_lang_setting_key_value <> '' 
									AND (IF (languages_id = '" . (int)$languages_id . "', 1, IF((	SELECT COUNT(cms_menu_id) > 0 
																									FROM " . TABLE_CMS_MENU_VALUE . " 
																									WHERE cms_menu_id = '" . (int)$menu_row['cms_menu_id'] . "' 
																										AND cms_menu_lang_setting_key_value <> '' 
																										AND languages_id = '" . (int)$languages_id . "'), 0, languages_id = '" . (int)$default_languages_id . "')))";
	$cms_footer_result_sql = tep_db_query($cms_footer_select_sql);
	while ($cms_footer_row = tep_db_fetch_array($cms_footer_result_sql)) {
		$cms_footer_content_array[$cms_footer_row['cms_menu_lang_setting_key']] = $cms_footer_row['cms_menu_lang_setting_key_value'];
	}
	
	$seolink_obj[] = array (	'menu_content' => $cms_footer_content_array["menu_content"]	);
}

include_once(DIR_WS_CLASSES . FILENAME_MAIN_PAGE);
$footer_all_payment_image = main_page::get_country_content('footer_all_payment_image');

if (tep_not_empty($_SESSION['country'])) {
	$country_info = tep_get_countries($country, true);
	$country_code = tep_not_empty($country_info['countries_iso_code_2']) ? $country_info['countries_iso_code_2'] : '';
}
?>
					<div class="gShodow"></div>
					<div class="footerTop" style="">
						<div class="vspacing"></div>
						<div id="wrapper" style="">
							<div style="position:absolute;padding:15px 0 0 790px;">
								<?=tep_image_button2('gray_short',tep_href_link("http://kb.offgamers.com/en/category/payment-option/?c=" . (tep_not_null($country_code) ? $country_code : 'All') . ""),BUTTON_ALL_PAYMENT_METHODS,165);?>
							</div>
							<?=$footer_all_payment_image['footer_payment_img'][0];?>
						</div>
						<div class="vspacing"></div>
						<div style="padding: 10px 0px 0px 30px;">
							<?php
								$count = 0;
								
								foreach ($seolink_obj as $seolink_row) {
									echo $seolink_row['menu_content'];
									$count++;
								}
							?>
						</div>
					</div>
					<div class="footerBottomDiv" id="footerBottomDiv"></div>
				<div style="clear: both;"></div>
<?php
unset($main_page);

echo tep_parse_live_chat_string(B2C_SELLING_LIVE_SUPPORT);
?>