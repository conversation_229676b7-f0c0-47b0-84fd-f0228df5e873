<?
// Geo Zones Object
$country_select_sql = "	SELECT countries_id, countries_name, countries_iso_code_2
						FROM " . TABLE_COUNTRIES . "
						WHERE countries_id = '".$country."'";
$country_result_sql = tep_db_query($country_select_sql);
if ($country_row = tep_db_fetch_array($country_result_sql)) {
	$selected_country = $country_row['countries_name'];
	$selected_country_code = $country_row['countries_iso_code_2'];
}

//Pre-load Data into array
$language_select_sql = "	SELECT name, code 
							FROM " . TABLE_LANGUAGES;
$language_result = tep_db_query($language_select_sql);
while($language_row = tep_db_fetch_array($language_result)){
	$languages_array[$language_row['code']] = $language_row['name'];
}

//Get Geo Zone ID
if (!is_object($localization_obj)) {
	$localization_obj = new localization($country);
}

$geo_zone_id = $localization_obj->get_zone_id(3);
$currency_info_obj = $localization_obj->get_zone_info($geo_zone_id);

$geo_zone_id = $localization_obj->get_zone_id(2);
$language_info_obj = $localization_obj->get_zone_info($geo_zone_id);

$selected_currency = ($cookies_started && in_array($currency, $currency_info_obj->zone_currency_id)) ? $currency : $currency_info_obj->zone_default_currency_id;
$selected_language = ($cookies_started && in_array($language_code, $language_info_obj->zone_languages_id)) ? $language_code : $language_info_obj->zone_default_languages_id;

foreach ($currency_info_obj->zone_currency_id AS $cy_code) {
	if (isset($currencies->currencies[$cy_code])) {
		$currency_text = $currencies->currencies[$cy_code]['title']." (".$currencies->currencies[$cy_code]['symbol_left'].$currencies->currencies[$cy_code]['symbol_right'].")";
		$currency_arr[] = array("id" => $cy_code, "text" => $currency_text);
	}
}

foreach ($language_info_obj->zone_languages_id AS $ln_code) {
	$language_arr[] = array("id" => $ln_code, "text" => $languages_array[$ln_code]);
}
?>
<div id="footerBar" class="lyr700" style="text-align:center;">
  <table width="984" height="30" border="0" cellpadding="0" cellspacing="0" align="center" style="">
      <tr>
        <td valign="bottom" align="center" >
        	 <table border="0" width="100%" height="100%" cellpadding="0" cellspacing="0" class="footerTable">
				<tr>
					<td align="left">
						<table border="0" cellpadding="0" cellspacing="0" align="left">
							<tr>
								<td width="5">
						        	&nbsp;
						        </td>
						      	<td>
						        	<div class="footerSeperator"></div>
						        </td>
						        <td id="footer_local_td" class="footer_column">
						        	<a href="javascript:void(0);" onClick="PopUpContent('region_popup_box');">
						        		<div class="flag <?=$selected_country_code ?>" style="cursor:pointer;"><div></div></div>
						        		<div class="ArrowTopIcon"></div>
						        	</a>
						        </td>
<?						        			
	if (!isset($_SESSION['customer_id'])) {
		$fb_btn = $ogm_fb_obj->get_FB_button('footer_bar_fb');
		if (tep_not_empty($fb_btn)) { // FB supported
?>
								<td width="1">
						        	<div class="footerSeperator"></div>
						        </td>
								<td id="footer_fblogin_td" class="footer_column">
									<?=$fb_btn?>
								</td>
<?
		}
?>
								<td width="1">
						        	<div class="footerSeperator"></div>
						        </td>
						        <td id="footer_login_td" class="footer_column">
									<a href="javascript:void(0);" class="ahds3" onClick="show_fancybox('login_popup_box');"><?=TEXT_LOGIN?></a>
								</td>
								<td width="1">
						        	<div class="footerSeperator"></div>
						        </td>
						        <td class="footer_column">
						        	<a href="javascript:void(0);" class="ahds3" Onclick="window.location.href='<?=tep_href_link(FILENAME_CREATE_ACCOUNT, '','SSL');?>'"><?=TEXT_REGISTER?></a>
						        </td>
						        <td width="1">
						        	<div class="footerSeperator"></div>
						        </td>
<?
	} else {
?>
								<td width="1">
						        	<div class="footerSeperator"></div>
						        </td>
								<td id="footer_login_td" class="footer_column">
									<a id="username_trigger" class="ahds4 lfloat" onMouseOver="trigger_whitebox_tooltip('member_info_tooltip', 'username_trigger');" onMouseOut="hide_whitebox_tooltip();" href="javascript:void(0);" onclick="window.location.href='<?=tep_href_link(FILENAME_ACCOUNT, '', 'SSL');?>'">
<?
		if (isset($_SESSION['fb_uid'])) {
?>
									<div style="left: 0;position: absolute;height:16px; width:16px; padding:0px;"><?=$ogm_fb_obj->get_FB_picture('custom_small', '16', '16')?></div>
<?
		} else {
?>
									<span class="personalIcon"></span>
<?
		}
?>
										<?=$_SESSION['customer_first_name'];?>
									</a>
									<span class="hd2 lfloat">|</span>
									<a href="javascript:void(0);" class="ahds3 lfloat" Onclick="window.location.href='<?=tep_href_link(FILENAME_ACCOUNT_HISTORY, '', 'SSL');?>'"><?=TEXT_TRACK_BUYBACK_ORDER?></a>
									<span class="hd2 lfloat">|</span>
									<a href="javascript:void(0);" class="ahds3 lfloat" Onclick="window.location.href='<?=tep_href_link(FILENAME_MY_ORDER_HISTORY, 'history_type=buyback&startdate='.date('Y-m-d').'', 'SSL');?>'"><?=TEXT_TRACK_SELLING_ORDER?></a>
									<span class="hd2 lfloat">|</span>
									<a href="javascript:void(0);" class="ahds3 lfloat" Onclick="<?=$ogm_fb_obj->get_logout_onclick(tep_href_link(FILENAME_LOGOFF, '', 'NONSSL'))?>"><?=TEXT_LOGOUT?></a>
						        	<div class="clrFx"></div>
						        </td>
						        <td width="1">
						        	<div class="footerSeperator"></div>
						        </td>
<?
	}
?>
						     </tr>
						</table>
					</td>
					<td align="right">
						<table border="0" cellpadding="0" cellspacing="0" align="right">
							<tr>
								<td width="1">
						        	<div class="footerSeperator"></div>
						        </td>
						        <td class="footer_column">
						        	<a href="javascript:void(0);" class="ahds3" onclick="window.location.href='<?=tep_href_link(FILENAME_CUSTOMER_SUPPORT, '','SSL');?>'"><?=TEXT_LIVE_CHAT?></a>
						        </td>
						        <td width="1">
						        	<div class="footerSeperator"></div>
						        </td>
						        <td width="5">&nbsp;</td>
							</tr>
						</table>
					</td>
				</tr>        	 
			</table>
        </td>
      </tr>
  </table>
</div>
<?
	if (!isset($_SESSION['customer_id'])) {
?>
<script>
	function change_content(id) {
		if(id == 'footer_forgot_password_content') {
			jQuery("#footer_login_content").css('display', 'none');
		} else {
			jQuery("#footer_forgot_password_content").css('display', 'none');
		}
		jQuery("#"+id).css('display', 'block');
		var table_width = jQuery('#'+id+' table').width() + 45;
		jQuery('#footer_login_popup_box').css('width', table_width);
	}
	
	function resetOthersRadio(passValue) {
		jQuery("#footer_login_table input[name=action]").not('[value="'+passValue+'"]').attr("checked", false);
	}
	
	function submit_login_form(id) {
		if (id == 'LoginForm') {
			if (jQuery("#act_login").attr('checked')) {
				document.getElementById('LoginForm_submit').click();
			} else {
				document.getElementById(id).submit();
			}
		} else {
			document.getElementById(id).submit();
		}
	}
</script>
<?
	} else {
?>

<div id="member_info_tooltip" style="display:none;">
	<table cellspacing="1" cellpadding="1">
        <tr>
            <td><?=TOOLTIPS_MEMBER_ID?></td>
            <td>: <span class="hds3"><?=$_SESSION['customer_id']?></span></td>
        </tr>
        <tr>
            <td><?=TOOLTIPS_MEMBER_STATUS?></td>
            <td>: <span class="hds3"><?=$member_status?></span></td>
        </tr>
        <tr>
            <td><?=TOOLTIPS_STORE_CREDIT_BALANCE?></td>
            <td>: <span class="hds3"><?=$printStoreCredit?></span></td>
        </tr>
        <tr>
            <td><?=TOOLTIPS_OFFGAMERS_POINTS?></td>
            <td>: <span class="hds3"><?=$printStorePoint?></span></td>
	    </tr>
        <tr>
            <td><?=TOOLTIPS_WITHDRAWABLE_BALANCE?></td>
            <td>: <span class="hds3"><?=$current_balance?></span></td>
        </tr>                                           
	</table>
</div>

<? 
	}
	
	if (strpos($_SERVER['HTTP_USER_AGENT'], 'iPhone') !== FALSE || strpos($_SERVER['HTTP_USER_AGENT'], 'iPad') !== FALSE) {
		$iphone_html = '<script>';
	    $iphone_html .= 'jQuery(document).ready(function(){';
	    
	    $iphone_html .= "	var new_css_style = 'width:100%;height:33px;position:absolute!important;left:0px;bottom: 0px; display: block;color: white;font-size: 12px;z-index: 700;' + jQuery('#footerBar').attr('style');";
	    $iphone_html .= "	jQuery('#footerBar').attr('style', new_css_style);";
	    
	    $iphone_html .= '	jQuery(window).scroll(function() {';
	    $iphone_html .= '		bottom_bar();';
	    $iphone_html .= '	});';
	    
	    $iphone_html .= '	jQuery(window).resize(function() {';
	    $iphone_html .= '		bottom_bar();';
	    $iphone_html .= '	});';
	    
	    $iphone_html .= '	bottom_bar();';
			
	    $iphone_html .= '});';
	    $iphone_html .= '</script>';
	    
	    echo $iphone_html;
	}

?>
<!-- // BlackBox Tooltip -->
<div id="blackbox_tooptips" style="display:none;">
	<table cellspacing="0" cellpadding="0" border="0" id="dpop" class="popupBubble">
		<tr><td class="black_tpl"></td><td class="black_tpc"></td><td class="black_tpr"></td></tr>
		<tr>
		   	<td class="black_mdl"></td>
		   	<td class="black_mdc">
		   		<table class="popup-contents" cellspacing="0" cellpadding="1">
	                <tr>
						<td id="blackbox_tooptips_content">&nbsp;</td>
	                </tr>
	        	</table>
		   	</td>
		    <td class="black_mdr"></td>
		</tr>
		<tr><td class="black_bml"></td><td class="black_bmc"></td><td class="black_bmr"></td></tr>
		<tr>
			<td class="black_tp" colspan="3"><?=tep_image(DIR_WS_IMAGES.'bubble_tail_arrow_btm.gif', 'popup tail', '17', '9')?></td>
		</tr>
	</table>
</div>
<div id="ogm_special_notice" style="display:none;position:fixed;width:500px;bottom:23px;left:2px;z-index:999;">
	<table cellspacing="0" cellpadding="0" border="0" width="100%">
		<tr><td class="black_tpl"></td><td class="black_tpc"></td><td class="black_tpr"></td></tr>
		<tr>
		   	<td class="black_mdl"></td>
		   	<td class="black_mdc">
		   		<table class="popup-contents" cellspacing="0" cellpadding="1" width="100%">
		   			<tr>
		   				<td>
		   					<table border="0" cellspacing="0" cellpadding="0" width="100%">
				   			<tr>
				   				<td width="100%">&nbsp;</td>
				   				<td><a href="javascript:void(0);" onClick="hide_special_notice();"><div class="redCloseIcon"></div></a></td>
				   			</tr>
				   			</table>
		   				</td>
		   			</tr>
	                <tr>
                        <td class="whiteText" style="padding:5px 10px 10px 10px;line-height:1.5;">
                        	<?=OGM_SPECIAL_NOTICE?>
                        </td>
	                </tr>
	        	</table>
		   	</td>
		    <td class="black_mdr"></td>
		</tr>
		<tr><td class="black_bml"></td><td class="black_bmc"></td><td class="black_bmr"></td></tr>
		<tr>
			<td class="black_tp" colspan="3"><?=tep_image(DIR_WS_IMAGES.'bubble_tail_arrow_btm.gif', 'popup tail', '17', '9')?></td>
		</tr>
	</table>
</div>