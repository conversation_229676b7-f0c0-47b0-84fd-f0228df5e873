<?
/*
  	$Id: configuration.php,v 1.8 2012/12/07 12:08:56 weichen Exp $
	
  	Developer: <PERSON> (c) 2006 SKC Venture
	
  	Released under the GNU General Public License
*/

function tep_get_cfg_setting($id, $id_type='catalog', $cfg_key='', $key_type='configuration_key') {
    global $memcache_obj;
    
	$cat_cfg_array = array();
	$cid = $id;
	
	if ($id_type == 'product') 	$cid = tep_get_actual_product_cat_id($id);
	
	$cat_path = tep_get_particular_cat_path($cid);
	
	if (tep_not_null($cfg_key)) {
        $cache_key = TABLE_CATEGORIES_CONFIGURATION . '/categories_id/' . $cid . '/cfg_key/' . $cfg_key . '/key_type' . $key_type . '/cfg_setting';
        $cache_result = $memcache_obj->fetch($cache_key);
        
        if ($cache_result !== FALSE) {
            $cat_cfg_array = $cache_result;
        } else {
            $cat_path_array = explode('_', $cat_path);
            $cat_path_array = array_merge(array(0), $cat_path_array);

            if ($key_type == 'configuration_key') {
                for ($i=count($cat_path_array)-1; $i >= 0; $i--) {
                    $cfg_value_select_sql = "	SELECT categories_configuration_value AS cfgValue 
                                                FROM " . TABLE_CATEGORIES_CONFIGURATION . " 
                                                WHERE categories_id ='" . $cat_path_array[$i] . "' 
                                                    AND categories_configuration_key = '" . tep_db_input($cfg_key) . "'; ";
                    $cfg_value_result_sql = tep_db_query($cfg_value_select_sql);
                    if ($cfg_value_row = tep_db_fetch_array($cfg_value_result_sql)) {
                        $cat_cfg_array[$cfg_key] = $cfg_value_row['cfgValue'];
                        break;
                    }
                }
            } else if ($key_type == 'group_id') {
                for ($i=0; $i < count($cat_path_array); $i++) {
                    $cfg_value_select_sql = "	SELECT categories_configuration_key AS cfgKey, categories_configuration_value AS cfgValue 
                                                FROM " . TABLE_CATEGORIES_CONFIGURATION . " 
                                                WHERE categories_id ='" . $cat_path_array[$i] . "' 
                                                    AND categories_configuration_group_id = '" . tep_db_input($cfg_key) . "'";
                    $cfg_value_result_sql = tep_db_query($cfg_value_select_sql);
                    while ($cfg_value_row = tep_db_fetch_array($cfg_value_result_sql)) {
                        $cat_cfg_array[$cfg_value_row['cfgKey']] = $cfg_value_row['cfgValue'];
                    }
                }
            }
            
            $memcache_obj->store($cache_key, $cat_cfg_array, 7200);
        }
	} else {
		$cat_cfg_select_sql = "	SELECT categories_configuration_key AS cfgKey, categories_configuration_value AS cfgValue 
								FROM " . TABLE_CATEGORIES_CONFIGURATION . " 
								WHERE categories_id=0; ";
		$cat_cfg_result_sql = tep_db_query($cat_cfg_select_sql);
		while ($cat_cfg_row = tep_db_fetch_array($cat_cfg_result_sql)) {
			$cat_cfg_array[$cat_cfg_row['cfgKey']] = $cat_cfg_row['cfgValue'];
		}
		
		if (tep_not_null($cat_path)) {
			$cat_path_array = explode('_', $cat_path);
			for ($i=0; $i < count($cat_path_array); $i++) {
				$cfg_value_select_sql = "	SELECT categories_configuration_key AS cfgKey, categories_configuration_value AS cfgValue 
											FROM " . TABLE_CATEGORIES_CONFIGURATION . " 
											WHERE categories_id ='" . $cat_path_array[$i] . "' ";
				$cfg_value_result_sql = tep_db_query($cfg_value_select_sql);
				while ($cfg_value_row = tep_db_fetch_array($cfg_value_result_sql)) {
					$cat_cfg_array[$cfg_value_row['cfgKey']] = $cfg_value_row['cfgValue'];
				}
			}
		}
	}
	
	return $cat_cfg_array;
}

function tep_get_cat_setting($id, $id_type='catalog', $setting_key='') {
	$cat_setting_array = array();
	$cid = $id;
	
	if ($id_type == 'product') 	$cid = tep_get_actual_product_cat_id($id);
	
	$cat_path = tep_get_particular_cat_path($cid);
	
	if (tep_not_null($setting_key)) {
		$cat_path_array = explode('_', $cat_path);
		$cat_path_array = array_merge(array(0), $cat_path_array);
		
		for ($i=count($cat_path_array)-1; $i >= 0; $i--) {
			$setting_value_select_sql = "	SELECT categories_setting_value AS settingValue 
											FROM " . TABLE_CATEGORIES_SETTING . " 
											WHERE categories_id ='" . $cat_path_array[$i] . "' 
												AND categories_setting_key = '" . tep_db_input($setting_key) . "'; ";
			$setting_value_result_sql = tep_db_query($setting_value_select_sql);
			if ($setting_value_row = tep_db_fetch_array($setting_value_result_sql)) {
				$cat_setting_array[$setting_key] = $setting_value_row['settingValue'];
				break;
			}
		}
	}
	
	return $cat_setting_array;
}

// Transaction status configuration related functions
function tep_status_update_notification($trans_type, $trans_id, $user_email_address, $from_status, $to_status, $mode='M', $comments='') {
	global $languages_id, $currencies;
	
	$notification_field = $mode == 'M' ? 'status_configuration_manual_notification' : 'status_configuration_auto_notification';
	
	$notification_select_sql = "SELECT " . $notification_field . "
								FROM " . TABLE_STATUS_CONFIGURATION . " 
								WHERE status_configuration_trans_type = '".tep_db_input($trans_type)."' 
									AND status_configuration_source_status_id = '".tep_db_input($from_status)."' 
									AND status_configuration_destination_status_id = '".tep_db_input($to_status)."'";
	$notification_result_sql = tep_db_query($notification_select_sql);
	
	if ($notification_row = tep_db_fetch_array($notification_result_sql)) {
		$notification_email_subject = '';
		
		$email_to_array = tep_parse_email_string($notification_row[$notification_field]);
		
		if (count($email_to_array)) {
			if ($trans_type == 'C') {
				$notification_email_subject = EMAIL_CUSTOMER_ORDER_UPDATE_NOTIFICATION_SUBJECT;
				
				$orders_status_array = array();
				$orders_status_query = tep_db_query("SELECT orders_status_id, orders_status_name FROM " . TABLE_ORDERS_STATUS . " WHERE language_id = '" . (int)$languages_id . "' ORDER BY orders_status_sort_order");
				while ($orders_status = tep_db_fetch_array($orders_status_query)) {
				    $orders_status_array[$orders_status['orders_status_id']] = $orders_status['orders_status_name'];
				}
				
				$order_info_select_sql = "	SELECT o.date_purchased, o.payment_methods_id, o.payment_methods_parent_id, ot.text AS order_total 
											FROM " . TABLE_ORDERS  . " AS o 
											INNER JOIN " . TABLE_ORDERS_TOTAL . " AS ot 
												ON (o.orders_id=ot.orders_id AND ot.class = 'ot_total')
											WHERE o.orders_id = '" . tep_db_input($trans_id) . "'";
				$order_info_result_sql = tep_db_query($order_info_select_sql);
				$order_info_row = tep_db_fetch_array($order_info_result_sql);
				
				$display_pm_title = payment_methods::get_pm_display_title($order_info_row['payment_methods_id'], $order_info_row['payment_methods_parent_id']);
				
				$trans_status_update_notification_email = sprintf(EMAIL_TRANS_UPDATE_NOTIFICATION_CONTENT, $trans_id, $order_info_row['date_purchased'], strip_tags($order_info_row['order_total']), $display_pm_title, ($mode == 'M' ? EMAIL_TEXT_TRANS_UPDATE_MANUAL_NOTIFICATION : EMAIL_TEXT_TRANS_UPDATE_AUTO_NOTIFICATION), $orders_status_array[$from_status], $orders_status_array[$to_status], date("Y-m-d H:i:s"), tep_get_ip_address(), $user_email_address, $comments);
			}
		}
		
		if (tep_not_null($notification_email_subject)) {
			for ($email_to_cnt=0; $email_to_cnt < count($email_to_array); $email_to_cnt++) {
				tep_mail($email_to_array[$email_to_cnt]['name'], $email_to_array[$email_to_cnt]['email'], implode(' ', array(EMAIL_SUBJECT_PREFIX, sprintf($notification_email_subject, $trans_id))), $trans_status_update_notification_email, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
			}
		}
	}
}
?>