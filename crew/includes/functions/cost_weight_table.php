<?php
/*
  $Id: cost_weight_table.php,v 1.1 2004/12/04 04:11:19 weichen Exp $

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2003 osCommerce

  Released under the GNU General Public License
*/

function display_table() {
	global $currency_symbol,$table_costtable,$table_pointer,$weight_pointer,$weight_matrix;
	
		$num_of_bands = sizeof($table_costtable);
		$num_of_matrices = sizeof($weight_matrix[0]);
		
		$display_table = '<table border="1" width="400" class="smalltable">';
		$display_table .= '<tr align="center">';
		$display_table .= '<th colspan="7">' . MODULE_SHIPPING_PWTABLE_TEXT_TABLE_ROW_HEAD . '</th></tr>';
		$display_table .= '<tr align="center"><th>' . MODULE_SHIPPING_PWTABLE_TEXT_TABLE_COLUMN_HEAD . '</th>';
		for ($i=0; $i<$num_of_matrices; $i+=2) {
			$display_table .= '<th>' . $weight_matrix[0][$i] . '</th>';
		}
		$display_table .= '</tr>';
		


	for ($j=0; $j<$num_of_bands; $j++) {
		$display_table .= '<tr align="center"><th>' . $currency_symbol . $table_costtable[$j] . '</th>';
		for ($i=0; $i<$num_of_matrices; $i+=2) {

			$display_table .= '<td ' . (($table_pointer==$j && $weight_pointer==$i)?"class=\"smalltablebackhighlight\"":"") . '>' . $currency_symbol . $weight_matrix[$j][$i+1] . '</td>';
		}
		$display_table .= '</tr>';
	}
	$display_table .= '</table>';

	return $display_table;
}

?>