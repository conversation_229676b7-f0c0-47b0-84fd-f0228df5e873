<?

/*
  $Id: customers_info_verification.php,v 1.43 2013/06/18 07:31:58 chingyen Exp $

  Developer: <PERSON><PERSON>
  Copyright (c) 2006 SKC Venture

  Released under the GNU General Public License
 */

function tep_format_telephone($customer_id) {
    $customer_info_select_sql = "select customers_telephone, customers_country_dialing_code_id, customers_default_address_id from " . TABLE_CUSTOMERS . " where customers_id ='" . (int) $customer_id . "'";
    $customer_info_result_sql = tep_db_query($customer_info_select_sql);
    $customer_info_row = tep_db_fetch_array($customer_info_result_sql);

    $customer_telephone_not_standard_format = $customer_info_row['customers_telephone'];
    $customer_telephone_not_standard_format = preg_replace('/[^\d]/', '', $customer_telephone_not_standard_format);
    $customer_country_dialing_code_id = $customer_info_row['customers_country_dialing_code_id'];
    $customer_default_address_id = $customer_info_row['customers_default_address_id'];

    if (!tep_not_null($customer_country_dialing_code_id)) {
        $customer_country_dialing_code_id_select_sql = "select entry_country_id from " . TABLE_ADDRESS_BOOK . " where address_book_id = '" . $customer_default_address_id . "' and customers_id ='" . (int) $customer_id . "'";
        $customer_country_dialing_code_id_query = tep_db_query($customer_country_dialing_code_id_select_sql);
        $customer_country_dialing_code_id_row = tep_db_fetch_array($customer_country_dialing_code_id_query);

        $international_dialing_code_id_update_sql = "update " . TABLE_CUSTOMERS . " set customers_country_dialing_code_id = '" . $customer_country_dialing_code_id_row['entry_country_id'] . "' where customers_id ='" . (int) $customer_id . "'";
        tep_db_query($international_dialing_code_id_update_sql);

        $customer_country_dialing_code_id = $customer_country_dialing_code_id_row['entry_country_id'];
    }

    $country_international_dialing_code_sql_select = "select countries_international_dialing_code, countries_name from " . TABLE_COUNTRIES . " where countries_id='" . $customer_country_dialing_code_id . "'";
    $country_international_dialing_code_query = tep_db_query($country_international_dialing_code_sql_select);
    $country_international_dialing_code_row = tep_db_fetch_array($country_international_dialing_code_query);

    $country_international_dialing_code = $country_international_dialing_code_row['countries_international_dialing_code'];
    $country_name = $country_international_dialing_code_row['countries_name'];

    $customer_telephone = tep_parse_telephone($customer_telephone_not_standard_format, $country_international_dialing_code, 'code');

    $customer_telephone = array('country_id' => $customer_country_dialing_code_id, 'country_name' => $country_name, 'country_international_dialing_code' => $country_international_dialing_code, 'telephone_number' => $customer_telephone);
    return $customer_telephone;
}

function tep_verification_allowed_check($customer_id, $pass_phone_number='') {
    if (tep_not_null($pass_phone_number)) {
        $complete_telephone_number = preg_replace('/[^\d]/', '', $pass_phone_number);
    } else {
        $customer_complete_phone_info = tep_format_telephone($customer_id);
        $complete_telephone_number = $customer_complete_phone_info['country_international_dialing_code'] . $customer_complete_phone_info['telephone_number'];
    }

    $customer_turns_select_sql = "  SELECT verify_try_turns
                                    FROM " . TABLE_CUSTOMERS_INFO_VERIFICATION . "
                                    WHERE customers_id = '" . (int) $customer_id . "'
                                        AND customers_info_value = '" . $complete_telephone_number . "'
                                        AND info_verification_type = '" . 'telephone' . "'";
    $customer_turns_result_sql = tep_db_query($customer_turns_select_sql);
    $customer_turns_row = tep_db_fetch_array($customer_turns_result_sql);

    return $customer_turns_row['verify_try_turns'];
}

function tep_parse_telephone($telephone, $country_needle, $type = 'id') {
    $country_code = '';

    $telephone = preg_replace('/[^\d]/', '', $telephone);

    if ($type == 'id') {
        $telephone_country_code_select_sql = "select countries_id, countries_international_dialing_code from " . TABLE_COUNTRIES . " where countries_id ='" . tep_db_input($country_needle) . "'";
        $telephone_country_code_result_sql = tep_db_query($telephone_country_code_select_sql);
        $telephone_country_code_row = tep_db_fetch_array($telephone_country_code_result_sql);

        $country_code = $telephone_country_code_row['countries_international_dialing_code'];
        $country_id = $telephone_country_code_row['countries_id'];
    } else if ($type == 'code') {
        $country_code = $country_needle;

        $country_id_select_sql = "select countries_id from " . TABLE_COUNTRIES . " where countries_international_dialing_code ='" . tep_db_input($country_code) . "'";
        $country_id_result_sql = tep_db_query($country_id_select_sql);
        $country_id_row = tep_db_fetch_array($country_id_result_sql);

        $country_id = $country_id_row['countries_id'];
    }

    switch ($country_id) {
        case 105: // Italy (Fixed line has one elading zero but Mobile does not have)
            $extra_reg_rule = '(?:0)';
            break;
        default:
            $extra_reg_rule = '';
            break;
    }
    $telephone = preg_replace('/^(0+)(' . $extra_reg_rule . '\d+)/', '$2', $telephone);

    if (tep_not_null($country_code)) {
        while (strlen($telephone) > 10) {
            if (preg_match('/^(' . $extra_reg_rule . $country_code . ')(\d+)/', $telephone)) {
                $telephone = preg_replace('/^(' . $extra_reg_rule . $country_code . ')(\d+)/', '$2', $telephone);
                $telephone = preg_replace('/^(0+)(' . $extra_reg_rule . '\d+)/', '$2', $telephone);
            } else {
                break;
            }
        }
    }

    switch ($country_id) {
        case 105: // Italy (Fixed line has one elading zero but Mobile does not have)
            if (substr($telephone, 0, 2) == '03') { // Mobile number
                $telephone = substr($telephone, 1);
            }

            break;
        default:
            ;
            break;
    }

    return $telephone;
}

function tep_four_digit_code_generate() {
    $code = rand(1000, 9999);

    return $code;
}

function tep_confirming_code($customer_id, $code_received, $complete_telephone_number='') {
    $return_bool = false;

    if (tep_not_null($complete_telephone_number)) {
        $complete_telephone_number = preg_replace('/[^\d]/', '', $complete_telephone_number);
    } else {
        $customer_complete_phone_info = tep_format_telephone($customer_id);
        $complete_telephone_number = $customer_complete_phone_info['country_international_dialing_code'] . $customer_complete_phone_info['telephone_number'];
    }

    $customer_info_verify_code_select_sql = "	SELECT serial_number
												FROM " . TABLE_CUSTOMERS_INFO_VERIFICATION . "
												WHERE customers_id = '" . (int) $customer_id . "'
                                                    AND customers_info_value ='" . $complete_telephone_number . "'
													AND info_verification_type = 'telephone'";
    $customer_info_verify_code_result_sql = tep_db_query($customer_info_verify_code_select_sql);
    if ($customer_info_verify_code_row = tep_db_fetch_array($customer_info_verify_code_result_sql)) {
        if ($customer_info_verify_code_row['serial_number'] == $code_received) {
            $return_bool = true;
        }
    }

    return $return_bool;
}

function tep_country_maxmind_support($country_dialing_code) {
    $country_maxmind_support_check_select_sql = "	SELECT " . TELEPHONE_VERIFICATION_SERVICES . "_support
													FROM " . TABLE_COUNTRIES . "
													WHERE countries_international_dialing_code = '" . (int) $country_dialing_code . "'";
    $country_maxmind_support_check_result_sql = tep_db_query($country_maxmind_support_check_select_sql);
    $country_maxmind_support_check_row = tep_db_fetch_array($country_maxmind_support_check_result_sql);

    return $country_maxmind_support_check_row[TELEPHONE_VERIFICATION_SERVICES . '_support'];
}

function tep_set_info_verified($customer_id, $complete_telephone_number='') {
    if (tep_not_empty($complete_telephone_number)) {
        $complete_telephone_number = preg_replace('/[^\d]/', '', $complete_telephone_number);
    } else {
        $customer_complete_phone_info = tep_format_telephone($customer_id);
        $complete_telephone_number = $customer_complete_phone_info['country_international_dialing_code'] . $customer_complete_phone_info['telephone_number'];
    }

    $customer_info_verified_select_sql = "	SELECT customers_id
                                            FROM " . TABLE_CUSTOMERS_INFO_VERIFICATION . "
                                            WHERE customers_id = '" . (int) $customer_id . "'
                                                AND customers_info_value = '" . tep_db_input($complete_telephone_number) . "'
                                                AND info_verification_type = 'telephone'";
    $customer_info_verified_result_sql = tep_db_query($customer_info_verified_select_sql);
    if (tep_db_num_rows($customer_info_verified_result_sql)) {
        $update_set_info_verified = "	UPDATE " . TABLE_CUSTOMERS_INFO_VERIFICATION . "
                                        SET info_verified = '1',
                                            serial_number = '',
                                            customers_info_verification_date = now()
                                        WHERE customers_id = '" . (int) $customer_id . "'
                                            AND customers_info_value ='" . $complete_telephone_number . "'
                                            AND info_verification_type='telephone'";
        tep_db_query($update_set_info_verified);
    } else {
        $sql_data_array = array('customers_id' => $customer_id,
            'customers_info_value' => $complete_telephone_number,
            'serial_number' => '',
            'verify_try_turns' => 1,
            'info_verified' => 1,
            'info_verification_type' => 'telephone',
            'customers_info_verification_date' => 'now()');
        tep_db_perform(TABLE_CUSTOMERS_INFO_VERIFICATION, $sql_data_array);
    }
}

function tep_set_try_turns($customer_id, $turns, $pass_phone_number='') {
    if (tep_not_null($pass_phone_number)) {
        $complete_telephone_number = preg_replace('/[^\d]/', '', $pass_phone_number);
    } else {
        $customer_complete_phone_info = tep_format_telephone($customer_id);
        $complete_telephone_number = $customer_complete_phone_info['country_international_dialing_code'] . $customer_complete_phone_info['telephone_number'];
    }

    $update_verify_try_turns = "UPDATE " . TABLE_CUSTOMERS_INFO_VERIFICATION . "
								SET verify_try_turns = '" . $turns . "'
								WHERE customers_id = '" . (int) $customer_id . "'
                                    AND customers_info_value ='" . $complete_telephone_number . "'
									AND info_verification_type='telephone'";
    tep_db_query($update_verify_try_turns);
}

function tep_reset_try_turns($customer_id, $verify_info, $type) {
    $update_reset_try_turns = "	UPDATE " . TABLE_CUSTOMERS_INFO_VERIFICATION . "
								SET verify_try_turns = 0
								WHERE customers_id = '" . (int) $customer_id . "'
                                    AND customers_info_value ='" . $verify_info . "'
									AND info_verification_type='" . $type . "'";
    tep_db_query($update_reset_try_turns);
}

function tep_try_turns($customer_id, $pass_phone_number='') {
    if (tep_not_null($pass_phone_number)) {
        $complete_telephone_number = preg_replace('/[^\d]/', '', $pass_phone_number);
    } else {
        $customer_complete_phone_info = tep_format_telephone($customer_id);
        $complete_telephone_number = $customer_complete_phone_info['country_international_dialing_code'] . $customer_complete_phone_info['telephone_number'];
    }

    $customer_verify_try_turns_select_sql = "	SELECT verify_try_turns
												FROM " . TABLE_CUSTOMERS_INFO_VERIFICATION . "
												WHERE customers_id = '" . (int) $customer_id . "'
                                                    AND customers_info_value ='" . $complete_telephone_number . "'
													AND info_verification_type ='telephone'";
    $customer_verify_try_turns_result_sql = tep_db_query($customer_verify_try_turns_select_sql);
    $customer_verify_try_turns_row = tep_db_fetch_array($customer_verify_try_turns_result_sql);

    return $customer_verify_try_turns_row['verify_try_turns'];
}

// Only check if there is record in TABLE_CUSTOMERS_INFO_VERIFICATION regardless of verify or not
//function tep_info_been_verify ($verify_info, $customer_id = 0, $type = 'telephone') {
//	$return_bool = FALSE;
//
//    $customer_verify_info_select_sql = "SELECT customers_info_value
//                                        FROM " . TABLE_CUSTOMERS_INFO_VERIFICATION . "
//                                        WHERE customers_info_value ='" . tep_db_input($verify_info) . "'
//                                            AND info_verification_type ='" . tep_db_input($type) . "'
//                                            AND info_verified = 1";
//
//    if ($customer_id) {
//        $customer_verify_info_select_sql .= " AND customers_id ='" . (int)$customer_id . "'";
//    }
//
//	$customer_verify_info_result_sql = tep_db_query($customer_verify_info_select_sql);
//	if (tep_db_num_rows($customer_verify_info_result_sql) > 0) {
//        $return_bool = TRUE;
//    }
//
//    return $return_bool;
//}

function tep_info_verified_check($customer_id, $verify_info, $type, $check_by_customer = TRUE) {  // remove $type usage
    $return_int = FALSE;

    $customer_info_verified_select_sql = "	SELECT info_verified
                                            FROM " . TABLE_CUSTOMERS_INFO_VERIFICATION . "
                                            WHERE customers_info_value = '" . tep_db_input($verify_info) . "'
                                                AND info_verification_type = '" . tep_db_input($type) . "'";

    if ($check_by_customer) {
        $customer_info_verified_select_sql .= " AND customers_id = '" . (int) $customer_id . "'";
    }

    $customer_info_verified_result_sql = tep_db_query($customer_info_verified_select_sql);
    while ($customer_info_verified_row = tep_db_fetch_array($customer_info_verified_result_sql)) {
        $return_int = (int) $customer_info_verified_row['info_verified'];

        if ($return_int == 1) {
            break;
        }
    }

    return $return_int;
}

function tep_customer_info_verification($customer_id, $verify_field) {
    global $memcache_obj;
    $return_string = '';

    if (tep_not_null($verify_field)) {
        $cache_key = TABLE_CUSTOMERS_VERIFICATION_DOCUMENT . '/customers_id/' . (int) $customer_id . '/' . $verify_field;
        $cache_result = $memcache_obj->fetch($cache_key);

        if ($cache_result !== FALSE) {
            $return_string = $cache_result;
        } else {
            $customer_info_select_sql = "	SELECT " . $verify_field . "
											FROM " . TABLE_CUSTOMERS_VERIFICATION_DOCUMENT . "
											WHERE customers_id = '" . (int) $customer_id . "'";
            $customer_info_result_sql = tep_db_query($customer_info_select_sql);
            if ($customer_info_row = tep_db_fetch_array($customer_info_result_sql)) {
                $return_string = $customer_info_row[$verify_field];
            } else {
                $return_string = 1;
            }
            $memcache_obj->store($cache_key, $return_string, 86400);
        }
    }
    return $return_string;
}

function tep_send_info_verification($email_address, $firstname, $lastname, $gender, $serial = '', $customer_id) {
    if (!tep_not_null($serial)) {
        // Generate Serial Number
        $new_serial = tep_gen_random_serial($email_address, TABLE_CUSTOMERS_INFO_VERIFICATION, 'serial_number', '', 12);

        $verification_info_select_sql = "   SELECT customers_id
                                            FROM " . TABLE_CUSTOMERS_INFO_VERIFICATION . "
                                            WHERE customers_id ='" . tep_db_input($customer_id) . "'
                                                AND customers_info_value = '" . tep_db_input($email_address) . "'
                                                AND info_verification_type = 'email'";
        $verification_info_result_sql = tep_db_query($verification_info_select_sql);

        if ($verification_info_row = tep_db_fetch_array($verification_info_result_sql)) {
            tep_db_query("update " . TABLE_CUSTOMERS_INFO_VERIFICATION . " set serial_number = '" . $new_serial . "' where customers_info_value = '" . tep_db_input($email_address) . "' and customers_id ='" . (int) $customer_id . "'");
        } else {
            $sql_data_array = array('customers_id ' => $customer_id,
                'customers_info_value' => $email_address,
                'serial_number' => $new_serial,
                'info_verified' => 0,
                'info_verification_type' => 'email');
            tep_db_perform(TABLE_CUSTOMERS_INFO_VERIFICATION, $sql_data_array);
        }
    } else {
        $new_serial = $serial;
    }

    $name = $firstname . ' ' . $lastname;

    $email_text .= tep_get_email_greeting($firstname, $lastname, $gender);

    $activate_address = tep_href_link(FILENAME_ACCOUNT_ACTIVATE, 'serial=' . $new_serial . '&email=' . $email_address . '&action=verify_email');
    $link = '<a href="' . $activate_address . '" target="_blank">' . $activate_address . '</a><br>';

    $email_text .= EMAIL_VERIFY_CONTENT . $link . EMAIL_VERIFY_CONTENT_ADDRESS_INFO . EMAIL_MANUAL_ACTIVATE_EMAIL_2 . $email_address . EMAIL_MANUAL_ACTIVATE_CODE_2 . $new_serial . "\n\n" . EMAIL_CONTACT . EMAIL_FOOTER;
    tep_mail($name, $email_address, implode(' ', array(EMAIL_SUBJECT_PREFIX, EMAIL_SUBJECT_2)), $email_text, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
}

function tep_error_report($customer_id, $orders_id) {
    $customer_info_select_sql = "select customers_firstname, customers_lastname, customers_gender from " . TABLE_CUSTOMERS . " where customers_id ='" . (int) $customer_id . "'";
    $customer_info_result_sql = tep_db_query($customer_info_select_sql);
    $customer_info_row = tep_db_fetch_array($customer_info_result_sql);

    $order_info_select_sql = "select date_purchased, orders_status from " . TABLE_ORDERS . " where orders_id ='" . (int) $orders_id . "'";
    $order_info_result_sql = tep_db_query($order_info_select_sql);
    $order_info_row = tep_db_fetch_array($order_info_result_sql);

    $customer_complete_phone_info_array = tep_format_telephone($customer_id);
    $complete_telephone_number = $customer_complete_phone_info_array['country_international_dialing_code'] . ' ' . $customer_complete_phone_info_array['telephone_number'];
    $country_name = $customer_complete_phone_info_array['country_name'];

    $firstname = $customer_info_row['customers_firstname'];
    $lastname = $customer_info_row['customers_lastname'];
    $gender = $customer_info_row['customers_gender'];
    $name = $firstname . ' ' . $lastname;

    $report_time = date('Y-m-d H:i:s');

    $email_text .= EMAIL_CONTENT_1 . ' ' . $report_time;
    $email_text .= '<br>' . EMAIL_CONTENT_2 . ' ' . $name;
    $email_text .= '<br>' . EMAIL_CONTENT_3 . ' ' . $customer_id;
    $email_text .= '<br>' . EMAIL_CONTENT_4 . ' ' . $orders_id;
    $email_text .= '<br>' . EMAIL_CONTENT_5 . ' ' . $order_info_row['date_purchased'];
    //$email_text .= '<br>' . EMAIL_CONTENT_6 . ' ' .
    $email_text .= '<br>' . EMAIL_CONTENT_7 . ' ' . $country_name;
    $email_text .= '<br>' . EMAIL_CONTENT_8 . ' ' . '+' . $complete_telephone_number;

    tep_mail(EMAIL_ADMIN_NAME, STORE_OWNER_EMAIL_ADDRESS, EMAIL_TITLE, $email_text, $name, $customer_email);
    $messageStack = new messageStack();
    $messageStack->add_session('index', EMAIL_REPORT_SEND_MESSAGE, 'success');
    tep_redirect(tep_href_link(FILENAME_DEFAULT));
}

function tep_get_user_flags() {
    $user_flags_array = array();

    $user_flags_select_sql = "	SELECT user_flags_id, user_flags_name, user_flags_description, user_flags_alias, user_flags_css_style
								FROM " . TABLE_USER_FLAGS . "
								WHERE user_flags_status = 1
								ORDER BY user_flags_id	";
    $user_flags_result_sql = tep_db_query($user_flags_select_sql);

    while ($user_flags_row = tep_db_fetch_array($user_flags_result_sql)) {
        $user_flags_array[$user_flags_row['user_flags_id']] = $user_flags_row;
    }

    return $user_flags_array;
}

function tep_is_cb_customer($customer_id) {
    $customer_cb_flag_select_sql = "SELECT customers_id
									FROM " . TABLE_CUSTOMERS . "
									WHERE customers_id = '" . tep_db_input($customer_id) . "'
										AND FIND_IN_SET('4', customers_flag)";
    $customer_cb_flag_result_sql = tep_db_query($customer_cb_flag_select_sql);

    if (tep_db_num_rows($customer_cb_flag_result_sql)) {
        return true;
    } else {
        return false;
    }
}

function tep_is_nrp_customer($customer_id) {
    $customer_cb_flag_select_sql = "SELECT customers_id
									FROM " . TABLE_CUSTOMERS . "
									WHERE customers_id = '" . tep_db_input($customer_id) . "'
										AND FIND_IN_SET('2', customers_flag)";
    $customer_cb_flag_result_sql = tep_db_query($customer_cb_flag_select_sql);

    if (tep_db_num_rows($customer_cb_flag_result_sql)) {
        return true;
    } else {
        return false;
    }
}

function tep_get_customer_store_credit_currency($customer_id, $return_id = true) {
    global $currencies, $currency;

    $customer_store_credit_currency_select_sql = "	SELECT sc_currency_id
													FROM " . TABLE_COUPON_GV_CUSTOMER . "
													WHERE customer_id = '" . tep_db_input($customer_id) . "'";
    $customer_store_credit_currency_result_sql = tep_db_query($customer_store_credit_currency_select_sql);

    if ($sc_currency_row = tep_db_fetch_array($customer_store_credit_currency_result_sql)) {
        if ($return_id) {
            return $sc_currency_row['sc_currency_id'];
        } else {
            return $currencies->get_code_by_id($sc_currency_row['sc_currency_id']);
        }
    } else {
        // use selected currency if no record found.
        if ($return_id) {
            return array_search($currency, $currencies->internal_currencies);
        } else {
            return $currency;
        }
    }
}

function tep_mask_telephone($customers_telephone) {
    return substr($customers_telephone, 0, -4) . '****';
}

function tep_customer_info_require($customer_id, &$error_message) {
    $information_required_missing_array = array();

    $customers_info_select_sql = "	SELECT customers_firstname, customers_lastname, customers_default_address_id, customers_country_dialing_code_id, customers_telephone
									FROM " . TABLE_CUSTOMERS . "
									WHERE customers_id = '" . (int) $customer_id . "'";
    $customers_info_result_sql = tep_db_query($customers_info_select_sql);
    if ($customers_info_row = tep_db_fetch_array($customers_info_result_sql)) {
        if (!tep_not_null($customers_info_row['customers_firstname'])) {
            $information_required_missing_array[] = TEXT_FIRST_NAME;
        }

        if (!tep_not_null($customers_info_row['customers_lastname'])) {
            $information_required_missing_array[] = TEXT_LAST_NAME;
        }

        if (!tep_not_null($customers_info_row['customers_country_dialing_code_id'])) {
            $information_required_missing_array[] = TEXT_COUNTRY_CODE;
        }

        if (!tep_not_null($customers_info_row['customers_telephone'])) {
            $information_required_missing_array[] = TEXT_CONTACT_NUMBER;
        }

//		$customers_security_select_sql = "	SELECT customers_security_question_1, customers_security_answer_1
//											FROM " . TABLE_CUSTOMERS_SECURITY . "
//											WHERE customers_id = '" . (int)$customer_id . "'";
//		$customers_security_result_sql = tep_db_query($customers_security_select_sql);
//		if ($customers_security_row = tep_db_fetch_array($customers_security_result_sql)) {
//			if (!tep_not_null($customers_security_row['customers_security_question_1'])) {
//				$information_required_missing_array[] = TEXT_SECRET_QUESTION;
//			}
//
//			if (!tep_not_null($customers_security_row['customers_security_answer_1'])) {
//				$information_required_missing_array[] = TEXT_ANSWER;
//			}
//		} else {
//			$information_required_missing_array[] = TEXT_SECRET_QUESTION;
//			$information_required_missing_array[] = TEXT_ANSWER;
//		}
    }

    $error_message = $information_required_missing_array;

    return count($information_required_missing_array) > 0 ? true : false;
}

function tep_customer_address_require($customer_id, &$error_message) {
    $information_required_missing_array = array();

    $address_book_info_select_sql = "	SELECT ab.entry_street_address, ab.entry_postcode, ab.entry_city, ab.entry_state, ab.entry_country_id, ab.entry_zone_id
										FROM " . TABLE_CUSTOMERS . " AS c
										INNER JOIN " . TABLE_ADDRESS_BOOK . " AS ab
											ON (ab.address_book_id = c.customers_default_address_id)
										WHERE c.customers_id = '" . (int) $customer_id . "'";
    $address_book_info_result_sql = tep_db_query($address_book_info_select_sql);
    if ($address_book_info_row = tep_db_fetch_array($address_book_info_result_sql)) {
        if (!tep_not_null($address_book_info_row['entry_street_address'])) {
            $information_required_missing_array[] = TEXT_BILLING_ADDRESS1;
        }

        if (!tep_not_null($address_book_info_row['entry_postcode'])) {
            $information_required_missing_array[] = TEXT_POST_CODE;
        }

        if (!tep_not_null($address_book_info_row['entry_city'])) {
            $information_required_missing_array[] = TEXT_CITY;
        }

        if (!tep_not_null($address_book_info_row['entry_country_id'])) {
            $information_required_missing_array[] = TEXT_COUNTRY;
        }

        if (!tep_not_null($address_book_info_row['entry_state']) && (int) $address_book_info_row['entry_zone_id'] < 1) {
            $information_required_missing_array[] = TEXT_STATE;
        }
    } else {
        $information_required_missing_array[] = TEXT_BILLING_ADDRESS1;
        $information_required_missing_array[] = TEXT_POST_CODE;
        $information_required_missing_array[] = TEXT_CITY;
        $information_required_missing_array[] = TEXT_COUNTRY;
        $information_required_missing_array[] = TEXT_STATE;
    }

    $error_message = $information_required_missing_array;

    return count($information_required_missing_array) > 0 ? true : false;
}

// M#0000075 @ 200909041150 - Viral Inviter opt out function
function tep_inviter_ignore($rec_id='', $code='') {
    $flag_success = false;

    // verify record exists
    $find_contact_sql = "	SELECT inviter_imports_service, inviter_imports_contact_email
							FROM " . TABLE_INVITER_IMPORTS . "
							WHERE inviter_imports_id='" . (int) $rec_id . "'
								AND inviter_imports_code='" . $code . "' ";
    $find_contact_result_sql = tep_db_query($find_contact_sql);

    if ($inv_row = tep_db_fetch_array($find_contact_result_sql)) {
        $inv_email = $inv_row['inviter_imports_contact_email'];
        $inv_email_provider = $inv_row['inviter_imports_service'];

        // check if record exists in inviter_ignore_list table
        $check_ignore_sql = "	SELECT inviter_ignore_contact
								FROM " . TABLE_INVITER_IGNORE_LIST . "
								WHERE inviter_ignore_contact = '" . $inv_email . "'
									AND inviter_ignore_service = '" . $inv_email_provider . "' ";
        $check_ignore_result_sql = tep_db_query($check_ignore_sql);

        if (tep_db_num_rows($check_ignore_result_sql) == 0) {
            $add_contact_array = array('inviter_ignore_contact' => $inv_email,
                'inviter_ignore_service' => $inv_email_provider);
            if (tep_db_perform(TABLE_INVITER_IGNORE_LIST, $add_contact_array)) {
                $flag_success = true;
            }
        }
    } // fi

    return $flag_success;
}

// M#0000075 @ 200909041150 - Viral Inviter join member function
function tep_inviter_joined($rec_id='', $code='', $input_email='') {
    $joined_successfully = false;

    $find_contacts_sql = "	SELECT inviter_imports_id
							FROM " . TABLE_INVITER_IMPORTS . "
							WHERE inviter_imports_id='" . (int) $rec_id . "'
								AND inviter_imports_contact_email='" . $input_email . "'
								AND inviter_imports_code='" . $code . "'
								AND inviter_imports_contact_joined='0' ";
    $find_contacts_result_sql = tep_db_query($find_contacts_sql);

    if (tep_db_num_rows($find_contacts_result_sql) == 1) {
        $update_imports_joined_array = array('inviter_imports_contact_joined' => '1');
        if (tep_db_perform(TABLE_INVITER_IMPORTS, $update_imports_joined_array, "update", "inviter_imports_id='" . (int) $rec_id . "' ")) {
            $joined_successfully = true;
        }
    } // fi

    return $joined_successfully;
}

function tep_file_uploaded_email_notification($cust_id) {
    $email_subject = 'Verification Form Uploaded From Customer <' . $cust_id . '>';
    $email_content = 'Verification form successfully uploaded from customer, please review.';

    $email_to_array = tep_parse_email_string(STORE_AFT_DOCUMENT_NOTIFICATION_EMAIL);
    for ($email_to_cnt = 0; $email_to_cnt < count($email_to_array); $email_to_cnt++) {
        tep_mail($email_to_array[$email_to_cnt]['name'], $email_to_array[$email_to_cnt]['email'], implode(' ', array(EMAIL_SUBJECT_PREFIX, $email_subject)), $email_content, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
    }
}

function validate_customer_domant_account($cust_id) {
    $customer_is_domant = false;

    $customer_domant_select_sql = " SELECT customer_info_account_dormant
                                    FROM " . TABLE_CUSTOMERS_INFO . "
                                    WHERE customers_info_id= " . (int) $cust_id . "";
    $customer_domant_result_sql = tep_db_query($customer_domant_select_sql);

    if ($customer_domant_row = tep_db_fetch_array($customer_domant_result_sql)) {
        if ($customer_domant_row['customer_info_account_dormant'] == '1') {
            $customer_is_domant = true;
        }
    }

    return $customer_is_domant;
}

function tep_is_mobile_num_exist($phone_no, $country_dial_id, $customer_id = 0, $country_dial_code = '') {
    $return_bool = FALSE;

    if (!empty($country_dial_id) && empty($country_dial_code)) {
        $country_id_select_sql = "  SELECT countries_international_dialing_code
                                    FROM " . TABLE_COUNTRIES . "
                                    WHERE countries_id ='" . tep_db_input($country_dial_id) . "'";
        $country_id_result_sql = tep_db_query($country_id_select_sql);
        if ($country_id_row = tep_db_fetch_array($country_id_result_sql)) {
            $country_dial_code = $country_id_row['countries_international_dialing_code'];
        }
    } else if (empty($country_dial_id) && !empty($country_dial_code)) {
        $country_id_select_sql = "  SELECT countries_id
                                    FROM " . TABLE_COUNTRIES . "
                                    WHERE countries_international_dialing_code ='" . tep_db_input($country_dial_code) . "'";
        $country_id_result_sql = tep_db_query($country_id_select_sql);
        if ($country_id_row = tep_db_fetch_array($country_id_result_sql)) {
            $country_dial_id = $country_id_row['countries_id'];
        }
    }

    $customer_phone_select_sql = "  SELECT customers_id
                                    FROM " . TABLE_CUSTOMERS . "
									WHERE customers_country_dialing_code_id = '" . $country_dial_id . "'
                                        AND customers_telephone = '" . tep_db_input($phone_no) . "'
                                        AND customers_id != '" . (int) $customer_id . "'";
    $customer_phone_result_sql = tep_db_query($customer_phone_select_sql);
    while ($phone_row = tep_db_fetch_array($customer_phone_result_sql)) {
        if (tep_info_verified_check($phone_row['customers_id'], ($country_dial_code . $phone_no), 'telephone') === 1) {
            $return_bool = TRUE;
            break;
        }
    }

    return $return_bool;
}

function tep_is_x_sharing_mobile_n_verified($customer_id, $phone_no, $country_dial_input, $country_dial_input_type = 'code', $bypass_phone_number_type_checking = TRUE) {
    require_once(DIR_WS_CLASSES . 'phone_verification.php');

    $return_bool = 0;   // Invalid mobile type
    $country_dial_id = 0;
    $country_dial_code = '';

    if (tep_not_empty($phone_no)) {
        if ($country_dial_input_type == 'id') {
            $country_dial_id = $country_dial_input;

            $country_id_select_sql = "  SELECT countries_international_dialing_code
                                        FROM " . TABLE_COUNTRIES . "
                                        WHERE countries_id ='" . tep_db_input($country_dial_id) . "'";
            $country_id_result_sql = tep_db_query($country_id_select_sql);
            if ($country_id_row = tep_db_fetch_array($country_id_result_sql)) {
                $country_dial_code = $country_id_row['countries_international_dialing_code'];
            }
        } else if ($country_dial_input_type == 'code') {
            $country_dial_code = $country_dial_input;

            $country_id_select_sql = "  SELECT countries_id
                                        FROM " . TABLE_COUNTRIES . "
                                        WHERE countries_international_dialing_code ='" . tep_db_input($country_dial_input) . "'";
            $country_id_result_sql = tep_db_query($country_id_select_sql);
            if ($country_id_row = tep_db_fetch_array($country_id_result_sql)) {
                $country_dial_id = $country_id_row['countries_id'];
            }
        }

        $pv_obj = new phone_verification();

        if ($bypass_phone_number_type_checking || in_array($pv_obj->get_phone_type($customer_id, '', $country_dial_code, $phone_no), array(2, 3))) {
            $customer_phone_select_sql = "  SELECT customers_id
                                            FROM " . TABLE_CUSTOMERS . "
                                            WHERE customers_country_dialing_code_id = '" . $country_dial_id . "'
                                                AND customers_telephone = '" . tep_db_input($phone_no) . "'
                                                AND customers_id != '" . (int) $customer_id . "'";
            $customer_phone_result_sql = tep_db_query($customer_phone_select_sql);
            if (tep_db_num_rows($customer_phone_result_sql) > 0) {
                $verified_check = tep_info_verified_check($customer_id, $country_dial_code . $phone_no, 'telephone');

                if ($verified_check !== FALSE) {
                    if ($verified_check == 1) {
                        // Sharing but it's verified from the same person.
                        $return_bool = TRUE;
                    } else {
                        // Shared mobile number
                        $return_bool = -1;
                    }
                } else {
                    // Number has not been verified
                    $return_bool = TRUE;
                }
            } else {
                // Not sharing
                $return_bool = TRUE;
            }
        }
    } else {
        // phone number is not yet set by customer consider not in sharing && not invalid phone type.
        $return_bool = TRUE;
    }

    unset($pv_obj);

    return $return_bool;
}

?>