<?

/*
  $Id: general.php,v 1.201 2015/03/04 10:13:39 weesiong Exp $

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2003 osCommerce

  Released under the GNU General Public License
 */

////
// Stop from parsing any further PHP code
function tep_exit() {
    tep_session_close();
    exit();
}

////
// Redirect to another page or site
function tep_redirect($url) {
    // Append baseURL
    $real_filename = '';
    $parse_url_array = parse_url($url);

    if (isset($parse_url_array['path'])) { // url path exist eg. /index.php
        $filename_array = explode('/', $parse_url_array['path']); // prevent more than 1 filename exist
        foreach ($filename_array as $filename) {
            if (strtolower(pathinfo($filename, PATHINFO_EXTENSION)) == 'php') {
                $real_filename = $filename;
                break;
            }
        }
    }

    if ($real_filename == FILENAME_LOGIN) {
        $URI_components = parse_url($_SERVER['REQUEST_URI']);
        $URI_query = $URI_components['query'];
        $url_path = '';

        if (tep_not_null($URI_query) && eregi_dep('baseURL', $URI_query)) {
            $URI_array = split_dep('[&=]', $URI_query);
            $url_path = 'baseURL=' . $URI_array[array_search('baseURL', $URI_array) + 1];
            unset($URI_array);
        } else if (basename($_SERVER['SCRIPT_NAME']) != FILENAME_LOGIN) {
            $url_path = 'baseURL=' . urlencode($_SERVER['SCRIPT_NAME']);
        }

        if (tep_not_null($url_path)) {
            if (eregi_dep('\?', $url)) {
                $url = $url . '&' . $url_path;
            } else {
                $url = $url . '?' . $url_path;
            }
        }
        unset($URI_components, $URI_query, $url_path);
    }

    if ((ENABLE_SSL == true) && (getenv('HTTPS') == 'on')) { // We are loading an SSL page
        if (substr($url, 0, strlen(HTTP_SERVER)) == HTTP_SERVER) { // NONSSL url
            $url = HTTPS_SERVER . substr($url, strlen(HTTP_SERVER)); // Change it to SSL
        }
    }

    # set SESSION for current_language_code, eg: zh-CN, zh-TW, etc
    if (SEARCH_ENGINE_FRIENDLY_URLS == 'true') {
        if (!eregi_dep('callback.php', $url) && !eregi_dep('ipn.php', $url) && !eregi_dep('checkout_', $url)) {
            global $languages_id, $current_language_code;

            $prev_language_code = $current_language_code;

            if ($languages_id > 1) {
                $language_select_sql = "SELECT code
										FROM " . TABLE_LANGUAGES . "
										WHERE languages_id = '" . $languages_id . "' LIMIT 1";
                $language_result_sql = tep_db_query($language_select_sql);
                if ($language_row = tep_db_fetch_array($language_result_sql)) {
                    $current_language_code = $language_row['code'];
                }
            } else {
                $current_language_code = '';
            }

            if (($prev_language_code != $current_language_code) || (tep_not_null($current_language_code) && !eregi_dep('/' . $current_language_code, $url))) {
                $http_host = '';
                if (eregi_dep("^" . HTTP_SERVER, $url) || eregi_dep("^" . HTTP_SERVER, $url))
                    $http_host = eregi_dep("^" . HTTP_SERVER, $url) ? HTTP_SERVER : HTTPS_SERVER;
                if (tep_not_null($current_language_code)) {
                    if (tep_not_null($prev_language_code) && eregi_dep('/' . $prev_language_code, $url)) {
                        $url = str_replace('/' . $prev_language_code, '/' . $current_language_code, $url);
                    } else {
                        $url = str_replace($http_host, $http_host . $current_language_code . '/', $url);
                    }
                } else if (tep_not_null($prev_language_code) && !tep_not_null($current_language_code)) {
                    $url = str_replace($http_host . $prev_language_code . '/', $http_host, $url);
                }
            }
        }
    }

    header('Location: ' . $url);

    tep_exit();
}

// Sets timeout for the current script.
// Cant be used in safe mode.
function tep_set_time_limit($limit) {
    if (!get_cfg_var('safe_mode')) {
        set_time_limit($limit);
    }
}

function tep_generate_category_path($id, $from = 'category', $categories_array = '', $index = 0) {
    global $default_languages_id, $languages_id;

    if (!is_array($categories_array))
        $categories_array = array();

    if ($from == 'product') {
        $categories_query = tep_db_query("select categories_id from " . TABLE_PRODUCTS_TO_CATEGORIES . " where products_id = '" . (int) $id . "'");
        while ($categories = tep_db_fetch_array($categories_query)) {
            if ($categories['categories_id'] == '0') {
                $categories_array[$index][] = array('id' => '0', 'text' => TEXT_TOP);
            } else {
                $category_query = tep_db_query("select cd.categories_name, c.parent_id from " . TABLE_CATEGORIES . " c, " . TABLE_CATEGORIES_DESCRIPTION . " cd
				  								where c.categories_id = '" . (int) $categories['categories_id'] . "'
													and c.categories_id = cd.categories_id
													and cd.categories_name <> ''
													and (IF(cd.language_id='" . tep_db_input($languages_id) . "', 1, cd.language_id='" . tep_db_input($default_languages_id) . "'))
												");
                $category = tep_db_fetch_array($category_query);
                $categories_array[$index][] = array('id' => $categories['categories_id'], 'text' => $category['categories_name']);
                if ((tep_not_null($category['parent_id'])) && ($category['parent_id'] != '0'))
                    $categories_array = tep_generate_category_path($category['parent_id'], 'category', $categories_array, $index);
                $categories_array[$index] = array_reverse($categories_array[$index]);
            }
            $index++;
        }
    } else if ($from == 'category') {
        $category_query = tep_db_query("select cd.categories_name, c.parent_id from " . TABLE_CATEGORIES . " c, " . TABLE_CATEGORIES_DESCRIPTION . " cd
										where c.categories_id = '" . (int) $id . "'
											and c.categories_id = cd.categories_id
											and cd.categories_name <> ''
											and (IF(cd.language_id='" . tep_db_input($languages_id) . "', 1, cd.language_id='" . tep_db_input($default_languages_id) . "'))
										");
        $category = tep_db_fetch_array($category_query);
        $categories_array[$index][] = array('id' => $id, 'text' => $category['categories_name']);
        if ((tep_not_null($category['parent_id'])) && ($category['parent_id'] != '0'))
            $categories_array = tep_generate_category_path($category['parent_id'], 'category', $categories_array, $index);
    }

    return $categories_array;
}

////
// Wrapper for class_exists() function
// This function is not available in all PHP versions so we test it before using it.
function tep_class_exists($class_name) {
    if (function_exists('class_exists')) {
        return class_exists($class_name);
    } else {
        return true;
    }
}

function tep_parse_email_string($email_string) {
    $email_array = tep_not_null($email_string) ? explode(',', $email_string) : array();
    $email_pattern = "/([^<]*?)(?:<)([^>]+)(?:>)/is";
    $email_receivers = array();

    for ($receiver_cnt = 0; $receiver_cnt < count($email_array); $receiver_cnt++) {
        if (preg_match($email_pattern, $email_array[$receiver_cnt], $regs)) {
            $receiver_name = trim($regs[1]);
            $receiver_email = trim($regs[2]);

            $email_receivers[] = array('name' => $receiver_name, 'email' => $receiver_email);
        }
    }

    return $email_receivers;
}

function tep_output_generated_category_path($id, $from = 'category') {
    $calculated_category_path_string = '';
    $calculated_category_path = tep_generate_category_path($id, $from);
    for ($i = 0, $n = sizeof($calculated_category_path); $i < $n; $i++) {
        for ($j = 0, $k = sizeof($calculated_category_path[$i]); $j < $k; $j++) {
            if ($ts) {
                $ts = $calculated_category_path[$i][$j]['text'] . " > " . $ts;
            } else {
                $ts = $calculated_category_path[$i][$j]['text'];
            }
            $calculated_category_path_string .= $calculated_category_path[$i][$j]['text'] . '&nbsp;&gt;&nbsp;';
        }
        $calculated_category_path_string = substr($calculated_category_path_string, 0, -16) . '<br>';
    }
    $calculated_category_path_string = substr($calculated_category_path_string, 0, -4);

    if (strlen($calculated_category_path_string) < 1)
        $calculated_category_path_string = TEXT_TOP;

    return $calculated_category_path_string;
}

function tep_output_generated_category_path_sq($id, $from = 'category', $style = true) {
    $calculated_category_path_string = '';
    $calculated_category_path = tep_generate_category_path($id, $from);
    for ($i = 0, $n = sizeof($calculated_category_path); $i < $n; $i++) {
        for ($j = 0, $k = sizeof($calculated_category_path[$i]); $j < $k; $j++) {
            if ($ts) {
                $ts = $calculated_category_path[$i][$j]['text'] . " > " . $ts;
            } else {
                $ts = $calculated_category_path[$i][$j]['text'];
            }
        }
    }

    if ($style)
        return "<span class='breadCrumb'>$ts</span>";
    else
        return "$ts";
}

function tep_output_generated_category_path_id($id, $from = 'category') {
    $calculated_category_path_string = '';
    $calculated_category_path = tep_generate_category_path($id, $from);
    for ($i = 0, $n = sizeof($calculated_category_path); $i < $n; $i++) {
        for ($j = 0, $k = sizeof($calculated_category_path[$i]); $j < $k; $j++) {

            if ($ts) {
                $ts = $calculated_category_path[$i][$j]['id'] . "_" . $ts;
            } else {
                $ts = $calculated_category_path[$i][$j]['id'];
            }
        }
    }

    return trim($ts);
}

////
// Parse the data used in the html tags to ensure the tags will not break
function tep_parse_input_field_data($data, $parse) {
    return strtr(trim($data), $parse);
}

function tep_output_string($string, $translate = false, $protected = false) {
    if ($protected == true) {
        return htmlspecialchars($string);
    } else {
        if ($translate == false) {
            return tep_parse_input_field_data($string, array('"' => '&quot;'));
        } else {
            return tep_parse_input_field_data($string, $translate);
        }
    }
}

function tep_output_string_protected($string) {
    return tep_output_string($string, false, true);
}

function tep_sanitize_string($string) {
    $string = ereg_replace_dep(' +', ' ', trim($string));

    return preg_replace("/[<>]/", '_', $string);
}

function tep_get_customers_name($customers_id) {
    $customers = tep_db_query("select customers_firstname, customers_lastname from " . TABLE_CUSTOMERS . " where customers_id = '" . (int) $customers_id . "'");
    $customers_values = tep_db_fetch_array($customers);

    return $customers_values['customers_firstname'] . ' ' . $customers_values['customers_lastname'];
}

function tep_get_customers_email($customers_id) {
    $customers = tep_db_query("select customers_email_address from " . TABLE_CUSTOMERS . " where customers_id = '" . (int) $customers_id . "'");
    $customers_values = tep_db_fetch_array($customers);

    return $customers_values['customers_email_address'];
}

////
// Return a random row from a database query
function tep_random_select($query) {
    $random_product = '';
    $random_query = tep_db_query($query);
    $num_rows = tep_db_num_rows($random_query);
    if ($num_rows > 0) {
        $random_row = tep_rand(0, ($num_rows - 1));
        tep_db_data_seek($random_query, $random_row);
        $random_product = tep_db_fetch_array($random_query);
    }

    return $random_product;
}

////
// Return a product's name
// TABLES: products
function tep_get_products_name($product_id, $language_id = 0) {
    global $default_languages_id, $languages_id;

    if ($language_id == 0)
        $language_id = $languages_id;

    $product_sql = "SELECT products_name
					FROM " . TABLE_PRODUCTS_DESCRIPTION . "
					WHERE products_id = '" . (int) $product_id . "'
						AND products_name <> ''
						AND (IF (language_id = '" . (int) $language_id . "', 1, IF(( SELECT COUNT(products_id) > 0
																					FROM " . TABLE_PRODUCTS_DESCRIPTION . "
																					WHERE products_id = '" . (int) $product_id . "'
																						AND products_name <> ''
																						AND language_id = '" . (int) $language_id . "'), 0, language_id = '" . (int) $default_languages_id . "')))";
    $product_query = tep_db_query($product_sql);

    $product = tep_db_fetch_array($product_query);

    return $product['products_name'];
}

function tep_get_products_description($product_id, $language_id = 0) {
    global $default_languages_id, $languages_id;

    if ($language_id == 0)
        $language_id = $languages_id;

    $products_description_select_sql = "SELECT products_description
										FROM " . TABLE_PRODUCTS_DESCRIPTION . "
										WHERE products_id = '" . (int) $product_id . "'
											AND products_description <> ''
											AND (IF (language_id = '" . (int) $language_id . "', 1, IF(( SELECT COUNT(products_id) > 0
																										FROM " . TABLE_PRODUCTS_DESCRIPTION . "
																										WHERE products_id = '" . (int) $product_id . "'
																											AND products_description <> ''
																											AND language_id = '" . (int) $language_id . "'), 0, language_id = '" . (int) $default_languages_id . "')))";
    $products_description_result_sql = tep_db_query($products_description_select_sql);
    $products_description_row = tep_db_fetch_array($products_description_result_sql);

    return $products_description_row['products_description'];
}

// Get products_payment_mature_period
function tep_get_products_payment_mature_period($products_id, $supplier_id = 0) {
    $mature_period = 0;

    $product_mature_period_select_sql = "	SELECT products_payment_mature_period
										 	FROM " . TABLE_PRODUCTS . "
										 	WHERE products_id = '" . tep_db_input($products_id) . "'";
    $product_mature_period_result_sql = tep_db_query($product_mature_period_select_sql);

    if ($product_mature_period_row = tep_db_fetch_array($product_mature_period_result_sql)) {
        $mature_period = $product_mature_period_row['products_payment_mature_period'];
    }

    if ($supplier_id > 0) {
        include_once(DIR_WS_CLASSES . 'products_supplier.php');

        $prod_sup_obj = new products_supplier($supplier_id);
        if (isset($prod_sup_obj->products_supplier['payout_grace_period_offset']) && tep_not_null($prod_sup_obj->products_supplier['payout_grace_period_offset'])) {
            $mature_period += $prod_sup_obj->products_supplier['payout_grace_period_offset'];
        }
    }

    return $mature_period;
}

function tep_get_products_complete_description($product_id, $language_id = 0) {
    global $default_languages_id, $languages_id;

    if ($language_id == 0)
        $language_id = $languages_id;

    $products_description_select_sql = "SELECT products_name, products_description, products_url, products_viewed, products_location,  products_image,  products_image_title,  products_description_image,  products_description_image_title
										FROM " . TABLE_PRODUCTS_DESCRIPTION . "
										WHERE products_id = '" . (int) $product_id . "'
											AND products_name <> ''
											AND (IF (language_id = '" . (int) $language_id . "', 1, IF(( SELECT COUNT(products_id) > 0
																										FROM " . TABLE_PRODUCTS_DESCRIPTION . "
																										WHERE products_id = '" . (int) $product_id . "'
																											AND products_name <> ''
																											AND language_id = '" . (int) $language_id . "'), 0, language_id = '" . (int) $default_languages_id . "')))";
    $products_description_result_sql = tep_db_query($products_description_select_sql);
    $products_description_row = tep_db_fetch_array($products_description_result_sql);

    return $products_description_row;
}

function tep_get_products_field($products_id, $field_name) {
    global $memcache_obj;

    $available_field_name = array('products_image', 'products_image_title', 'products_description');

    $products_field = '';

    $cache_key = TABLE_PRODUCTS . '/get/' . $field_name . '/products_id/' . $products_id . '/language/' . $languages_id;

    $cache_result = $memcache_obj->fetch($cache_key);

    if ($cache_result !== FALSE && in_array($field_name, $available_field_name)) {
        $products_field = $cache_result;
    } else {
        $products_field_select_sql = "	SELECT " . $field_name . "
										FROM " . TABLE_PRODUCTS . "
										WHERE products_id = '" . (int) $products_id . "'
											AND " . $field_name . " <> ''";
        $products_field_result_sql = tep_db_query($products_field_select_sql);
        $products_field_row = tep_db_fetch_array($products_field_result_sql);
        $products_field = $products_field_row[$field_name];

        $memcache_obj->store($cache_key, $products_field, 86400);
    }

    return $products_field;
}

function tep_get_products_info($products_id, $field_name, $languages_id, $default_languages_id) {
    global $memcache_obj;

    $available_field_name = array('products_image', 'products_image_title', 'products_description');

    $products_info = '';

    $cache_key = TABLE_PRODUCTS_DESCRIPTION . '/get/' . $field_name . '/products_id/' . $products_id . '/language/' . $languages_id;

    $cache_result = $memcache_obj->fetch($cache_key);

    if ($cache_result !== FALSE && in_array($field_name, $available_field_name)) {
        $products_info = $cache_result;
    } else {
        $products_info_select_sql = "	SELECT " . $field_name . "
										FROM " . TABLE_PRODUCTS_DESCRIPTION . "
										WHERE products_id = '" . (int) $products_id . "'
											AND " . $field_name . " <> ''
											AND (IF(language_id = '" . $languages_id . "' , 1, IF (( SELECT COUNT(products_id) > 0
																								 FROM " . TABLE_PRODUCTS_DESCRIPTION . "
																								 WHERE products_id = '" . (int) $products_id . "'
																								 	AND language_id = '" . $languages_id . "'
																									AND " . $field_name . " <> ''
																										), 0, language_id = '" . $default_languages_id . "')))";
        $products_info_result_sql = tep_db_query($products_info_select_sql);
        $products_info_row = tep_db_fetch_array($products_info_result_sql);
        $products_info = $products_info_row[$field_name];

        $memcache_obj->store($cache_key, $products_info, 86400);
    }

    return $products_info;
}

//CGDiscountSpecials start
function tep_get_customers_groups_id() {
    global $customer_id;
    $customers_groups_query = tep_db_query("select customers_groups_id from " . TABLE_CUSTOMERS . " where customers_id =  '" . (int) $customer_id . "'");
    $customers_groups_id = tep_db_fetch_array($customers_groups_query);
    return $customers_groups_id['customers_groups_id'];
}

function tep_get_customers_groups_name($pass_groups_id = '') {
    if (!tep_not_null($pass_groups_id))
        $pass_groups_id = tep_get_customers_groups_id();

    $customers_groups_result_sql = tep_db_query("select customers_groups_name from " . TABLE_CUSTOMERS_GROUPS . " where customers_groups_id =  '" . (int) $pass_groups_id . "'");
    $customers_groups_row = tep_db_fetch_array($customers_groups_result_sql);
    return $customers_groups_row['customers_groups_name'];
}

function tep_get_customer_discount_array($customer_id, $id, $id_type = 'catalog') {
    $customers_discount = 0;

    $cust_select_sql = "	SELECT customers_discount
   							FROM " . TABLE_CUSTOMERS . "
   							WHERE customers_id = '" . (int) $customer_id . "'";
    $cust_result_sql = tep_db_query($cust_select_sql);
    if ($cust_row_sql = tep_db_fetch_array($cust_result_sql)) {
        $customers_discount = $cust_row_sql["customers_discount"];
    }

    $customer_group_info_array = tep_get_customer_group_discount($customer_id, $id, $id_type);
    $customer_group_discount = $customer_group_info_array['cust_group_discount'];

    return array('individual' => $customers_discount, 'group' => $customer_group_discount);
}

function tep_get_customer_group_discount($customer_id, $id, $id_type = 'catalog') {
    $cust_group_discount = 0;
    $cust_group_rebate = 0;
    $cust_group_discount_id = 0;
    $cid = $id;
    $cust_group_info_array = array();

    if ($id_type == 'product')
        $cid = tep_get_actual_product_cat_id($id);

    $cat_path = tep_get_particular_cat_path($cid);
    $cat_path_array = explode('_', $cat_path);
    $cat_path_array = array_merge(array(0), $cat_path_array);

    for ($i = count($cat_path_array) - 1; $i >= 0; $i--) {
        $grp_discount_select_str = "SELECT customers_groups_discount, customers_groups_rebate, customers_groups_discount_id
									FROM " . TABLE_CUSTOMERS_GROUPS_DISCOUNT . " AS gd
									INNER JOIN " . TABLE_CUSTOMERS . " AS c
										ON gd.customers_groups_id = c.customers_groups_id";

        $grp_discount_where_str = " WHERE gd.categories_id ='" . tep_db_input($cat_path_array[$i]) . "'";

        if ((int) $customer_id > 0) {
            $grp_discount_where_str .= " AND c.customers_id = '" . tep_db_input($customer_id) . "'";
        } else {
            $grp_discount_where_str .= " AND gd.customers_groups_id = '1'";
        }

        $grp_discount_select_sql = $grp_discount_select_str . $grp_discount_where_str;
        $grp_discount_result_sql = tep_db_query($grp_discount_select_sql);
        if ($grp_discount_row = tep_db_fetch_array($grp_discount_result_sql)) {
            $cust_group_discount = $grp_discount_row['customers_groups_discount'];
            $cust_group_rebate = $grp_discount_row['customers_groups_rebate'];
            $cust_group_discount_id = $grp_discount_row['customers_groups_discount_id'];

            break;
        }
    }

    $cust_group_info_array['cust_group_discount'] = $cust_group_discount;
    $cust_group_info_array['cust_group_rebate'] = $cust_group_rebate;
    $cust_group_info_array['cust_group_discount_id'] = $cust_group_discount_id;

    return $cust_group_info_array;
}

function tep_get_email_greeting($firstname, $lastname, $gender = '') {
    if (ACCOUNT_GENDER == 'true') {
        if ($gender == 'm') {
            $email_greeting = sprintf(EMAIL_GREET_MR, $lastname);
        } else if ($gender == 'f') {
            $email_greeting = sprintf(EMAIL_GREET_MS, $lastname);
        } else {
            $email_greeting = sprintf(EMAIL_GREET_NONE, $firstname);
        }
    } else {
        $email_greeting = sprintf(EMAIL_GREET_NONE, $firstname);
    }

    return $email_greeting;
}

function tep_get_email_greeting_english($firstname, $lastname, $gender = '') {
    if (ACCOUNT_GENDER == 'true') {
        if ($gender == 'm') {
            $email_greeting = sprintf(EN_EMAIL_GREET_MR, $lastname);
        } else if ($gender == 'f') {
            $email_greeting = sprintf(EN_EMAIL_GREET_MS, $lastname);
        } else {
            $email_greeting = sprintf(EN_EMAIL_GREET_NONE, $firstname);
        }
    } else {
        $email_greeting = sprintf(EN_EMAIL_GREET_NONE, $firstname);
    }

    return $email_greeting;
}

function tep_get_products_special_price($product_id) {
    global $customer_id;
    $product_query = tep_db_query("SELECT specials_new_products_price FROM " . TABLE_SPECIALS . " WHERE products_id = '" . (int) $product_id . "' AND status = '1' AND customers_id = '" . tep_db_input($customer_id) . "' AND customers_groups_id = '0'");
    if (!tep_db_num_rows($product_query)) {
        $customer_groups_id = tep_get_customers_groups_id();
        $product_query = tep_db_query("SELECT specials_new_products_price FROM " . TABLE_SPECIALS . " WHERE products_id = '" . (int) $product_id . "' AND status = '1' AND customers_groups_id = '" . tep_db_input($customer_groups_id) . "' AND customers_id = '0'");
        if (!tep_db_num_rows($product_query)) {
            $product_query = tep_db_query("SELECT specials_new_products_price FROM " . TABLE_SPECIALS . " WHERE products_id = '" . (int) $product_id . "' AND status = '1' AND customers_groups_id = '0' AND customers_id = '0'");
        }
    }
    $product = tep_db_fetch_array($product_query);
    return $product['specials_new_products_price'];
}

//CGDiscountSpecials end
////
// Return a product's stock
// TABLES: products
function tep_get_products_stock($products_id, $offset_out_of_stock_level = false) {
    $products_id = tep_get_prid($products_id);
    $stock_query = tep_db_query("SELECT products_quantity, products_out_of_stock_level FROM " . TABLE_PRODUCTS . " WHERE products_id = '" . (int) $products_id . "'");
    $stock_values = tep_db_fetch_array($stock_query);

    if ($offset_out_of_stock_level) {
        return ($stock_values['products_quantity'] - (int) $stock_values['products_out_of_stock_level']);
    } else {
        return $stock_values['products_quantity'];
    }
}

////
// Get total number of product added to cart
function tep_get_total_product_in_cart($customer_id, $products_id, $exclude_bundle_id = '') {
    $cart_prod_total = 0;
    $customers_basket_select_sql = "SELECT c.customers_basket_quantity
    								FROM " . TABLE_CUSTOMERS_BASKET . " AS c
    								LEFT JOIN " . TABLE_PRODUCTS . " AS p
    									ON c.products_id = p.products_id
    								WHERE c.customers_id = '" . (int) $customer_id . "'
    									AND c.products_id = '" . tep_db_input($products_id) . "'
    									AND p.products_bundle<>'yes'
    									AND p.products_bundle_dynamic<>'yes'";
    $customers_basket_result_sql = tep_db_query($customers_basket_select_sql);
    if ($customers_basket_info = tep_db_fetch_array($customers_basket_result_sql))
        $cart_prod_total += $customers_basket_info["customers_basket_quantity"];

    $customers_basket_select_sql = "SELECT c.products_id, c.customers_basket_quantity, pb.subproduct_qty
    								FROM " . TABLE_CUSTOMERS_BASKET . " AS c
    								LEFT JOIN " . TABLE_PRODUCTS . " AS p
    									ON c.products_id = p.products_id
    								LEFT JOIN " . TABLE_PRODUCTS_BUNDLES . " AS pb
    									ON p.products_id = pb.bundle_id
    								WHERE c.customers_id = '" . (int) $customer_id . "'
    									AND pb.subproduct_id = '" . tep_db_input($products_id) . "'
    									AND p.products_bundle='yes' ";
    $customers_basket_result_sql = tep_db_query($customers_basket_select_sql);
    while ($customers_basket_info = tep_db_fetch_array($customers_basket_result_sql)) {
        $cart_prod_total += $customers_basket_info["customers_basket_quantity"] * $customers_basket_info["subproduct_qty"];
    }

    $customers_basket_select_sql = "SELECT cb.subproducts_quantity, pb.bundle_id, pb.subproduct_qty
    								FROM " . TABLE_CUSTOMERS_BASKET_BUNDLE . " AS cb
    								LEFT JOIN " . TABLE_PRODUCTS_BUNDLES . " AS pb
    									ON cb.products_bundle_id = pb.bundle_id
    								WHERE cb.customers_id = '" . (int) $customer_id . "'
    									AND cb.subproducts_id = '" . tep_db_input($products_id) . "'
    									AND pb.subproduct_id = '" . tep_db_input($products_id) . "'";
    $customers_basket_result_sql = tep_db_query($customers_basket_select_sql);
    while ($customers_basket_info = tep_db_fetch_array($customers_basket_result_sql)) {
        if ($customers_basket_info["bundle_id"] != $exclude_bundle_id)
            $cart_prod_total += $customers_basket_info["subproducts_quantity"] * $customers_basket_info["subproduct_qty"];
    }

    return $cart_prod_total;
}

/* -- CY 20110328 :: General function with same usage --*
  ////
  // Check if this product has the right to be checkout even if low qty or negative qty
  // TABLES: products
  function tep_check_product_permission ($products_id) {
  $products_id = tep_get_prid($products_id);
  $prod_permission_query = tep_db_query("SELECT products_skip_inventory FROM " . TABLE_PRODUCTS . " WHERE products_id = '" . (int)$products_id . "'");
  $prod_permission = tep_db_fetch_array($prod_permission_query);

  return ($prod_permission['products_skip_inventory']);
  }

  function tep_check_product_skip_stock ($products_id) {
  $products_id = tep_get_prid($products_id);
  $product_skip_stock_query = tep_db_query("SELECT products_skip_inventory FROM " . TABLE_PRODUCTS . " WHERE products_id = '" . (int)$products_id . "'");
  $product_skip_stock_row = tep_db_fetch_array($product_skip_stock_query);

  return ($product_skip_stock_row['products_skip_inventory']);
  }
  /*-- CY 20110328 :: General function with same usage -- */

// check Product's Main Game categories_id available in Region
function tep_check_product_region_permission($id, $is_categories = false) {
    global $localization;

    $game_array = array();
    $permission = false;
    $id = tep_get_prid($id);

    if ($is_categories) {
        $cat_path = explode('_', tep_get_categories_parent_path($id));
    } else {
        $cat_path = explode('_', tep_get_product_path($id));
    }
    $zone_info_array = $localization->get_zone_configuration();

    if (is_array($zone_info_array[1]->zone_categories_id)) {
        $game_array = array_intersect($zone_info_array[1]->zone_categories_id, $cat_path);

        if (count($game_array) > 0) {
            $permission = true;
        }
    }

    return $permission;
}

function tep_get_eta_string($eta) {
    if (preg_match("/(?:#DATE#)([^#]+)(?:#DATE#)?/is", $eta, $regs)) {
        return $regs[1];
    } else {
        $hours = (int) $eta;
        $minutes = ($eta * 60) - ($hours * 60);

        return ($hours >= 1 ? $hours . " hr" . ($hours > 1 ? 's' : '') : '') . ($minutes > 0 ? ' ' . $minutes . " min" . ($minutes > 1 ? 's' : '') : '');
    }
}

function tep_single_product_skip_stock_check($product_id) {
    $show_it = '';
    $out_of_stock_level = '';

    $product_select_sql = "	SELECT products_purchase_mode, products_out_of_stock_level
							FROM " . TABLE_PRODUCTS . "
							WHERE products_id = '" . tep_db_input($product_id) . "'";
    $product_result_sql = tep_db_query($product_select_sql);

    if ($product_info = tep_db_fetch_array($product_result_sql)) {
        switch ($product_info["products_purchase_mode"]) {
            case '1': // Always Add to Cart
            case '2': // Always Pre-Order
                $show_it = 1;
                break;
            case '3': // Always Out of Stock
                $show_it = -1;
                break;
            case '4':
                if (tep_not_null($product_info["products_out_of_stock_level"])) { // If there is setting for out of stock level
                    $show_it = 0;
                    $out_of_stock_level = (int) $product_info["products_out_of_stock_level"];
                } else {
                    $show_it = 1;
                }
                break;
        }
    }

    return array('state' => $show_it, 'stock_level' => $out_of_stock_level);
}

function tep_product_add_to_cart_permission($product_id, $type, $product_instance_id = '') {
    global $default_languages_id, $languages_id;
    $status_info = array();
    $pre_order_time = 0;
    $cur_eta = 0;
    $subproduct_qty = '';
    $is_future_product = false;
    $max_future_date = '';
    $bundle_qty = '';
    $auto_purchase_mode = false;

    if ($type == "products_bundle") {
        $bundle_select_sql = "	SELECT pb.*, p.products_id, p.products_quantity, p.products_status, p.products_eta, p.products_pre_order_level, p.products_out_of_stock_level, p.products_purchase_mode, p.products_date_available
								FROM " . TABLE_PRODUCTS . " AS p
								INNER JOIN " . TABLE_PRODUCTS_DESCRIPTION . " AS pd
				  					ON p.products_id=pd.products_id
								INNER JOIN " . TABLE_PRODUCTS_BUNDLES . " AS pb
				  					ON pb.subproduct_id=pd.products_id
								WHERE pb.bundle_id = " . tep_get_prid($product_id) . "
									AND p.products_id <> ''
									AND (IF(language_id='" . tep_db_input($languages_id) . "', 1, language_id='" . tep_db_input($default_languages_id) . "'))
							";
        $bundle_result_sql = tep_db_query($bundle_select_sql);

        if (tep_db_num_rows($bundle_result_sql)) {
            $show_it = 1;
            while ($bundle_data = tep_db_fetch_array($bundle_result_sql)) {
                $subproduct_qty = $bundle_data['subproduct_qty'];
                if (!$bundle_data["products_status"] || $bundle_data["products_purchase_mode"] == '3') { // Inactive or Out of Stock
                    $show_it = 0;
                    break;
                }

                $bundle_prd_qty = $bundle_data['products_quantity'] / $subproduct_qty;
                if (!tep_not_null($bundle_qty)) {
                    $bundle_qty = $bundle_prd_qty;
                } else {
                    $bundle_qty = ($bundle_prd_qty > $bundle_qty ? $bundle_qty : $bundle_prd_qty);
                }

                // Get SYSTEM_PRODUCT_ETA category configuration of this single product. Cannot use package id since its subproduct might not from the same category
                $cat_cfg_array = tep_get_cfg_setting($bundle_data["products_id"], 'product', 'SYSTEM_PRODUCT_ETA');

                switch ($bundle_data["products_purchase_mode"]) {
                    case '1': // Always Add to Cart
                        ; // do nothing, remain current whatsoever state

                        break;
                    case '2': // Always Pre-Order
                        $cur_eta = $bundle_data["products_eta"] != '' ? $bundle_data["products_eta"] : $cat_cfg_array['SYSTEM_PRODUCT_ETA'];
                        $pre_order_time = ($cur_eta > $pre_order_time) ? $cur_eta : $pre_order_time;

                        if ($bundle_data['products_date_available'] > date('Y-m-d H:i:s')) {
                            if ($bundle_data['products_date_available'] > $max_future_date)
                                $max_future_date = $bundle_data['products_date_available'];
                            $is_future_product = true;
                        }

                        $show_it = 2; // pre-order

                        break;
                    case '4': // Auto Mode
                        $pre_order_level = ($bundle_data["products_pre_order_level"] != '') ? $bundle_data["products_pre_order_level"] : '';
                        $diff = $bundle_data['products_quantity'] - $bundle_data["subproduct_qty"];
                        $auto_purchase_mode = true;

                        if (tep_not_null($bundle_data["products_out_of_stock_level"]) && $diff < $bundle_data["products_out_of_stock_level"]) { // If there is setting for out of stock level
                            $show_it = 0; // out of stock
                            break 2;
                        } else if (tep_not_null($pre_order_level) && $diff < $pre_order_level) {
                            $show_it = 2;
                            $cur_eta = $bundle_data["products_eta"] != '' ? $bundle_data["products_eta"] : $cat_cfg_array['SYSTEM_PRODUCT_ETA'];
                            $pre_order_time = ($cur_eta > $pre_order_time) ? $cur_eta : $pre_order_time;

                            if ($bundle_data['products_date_available'] > date('Y-m-d H:i:s')) {
                                if ($bundle_data['products_date_available'] > $max_future_date)
                                    $max_future_date = $bundle_data['products_date_available'];
                                $is_future_product = true;
                            }
                        } else {
                            ; // do nothing, remain current whatsoever state
                        }

                        break;
                }
            }

            if (!$show_it) {
                $is_future_product = false;
                $pre_order_time = '';
            }

            // Format pre-order time
            if ($is_future_product) {
                $dateObj = explode(' ', trim($max_future_date));
                list($yr, $mth, $day) = explode('-', $dateObj[0]);
                list($hr, $min, $sec) = explode(':', $dateObj[1]);
                $pre_order_time = '#DATE#' . date("F j, Y", mktime((int) $hr, (int) $min, (int) $sec, $mth, $day, $yr)) . '#DATE#';
            }
        } else {
            $show_it = 0;
        }
    } else if ($type == "products_bundle_dynamic") {
        $dynamic_weight_result_sql = tep_db_query("	SELECT products_bundle_dynamic_qty
    				              					FROM " . TABLE_PRODUCTS . "
        	        			  					WHERE products_id = '" . tep_db_input($product_id) . "'");
        if ($dynamic_weight = tep_db_fetch_array($dynamic_weight_result_sql)) {
            $available_weight = 0; // store all total possible weight excluding pre-order status item(s)

            $bundle_select_sql = "	SELECT pb.*, p.products_id, p.products_quantity, p.products_eta, p.products_pre_order_level, p.products_out_of_stock_level, p.products_purchase_mode, p.products_date_available
									FROM " . TABLE_PRODUCTS . " AS p
									INNER JOIN " . TABLE_PRODUCTS_DESCRIPTION . " AS pd
					  					ON p.products_id=pd.products_id
									INNER JOIN " . TABLE_PRODUCTS_BUNDLES . " AS pb
					  					ON pb.subproduct_id=pd.products_id
									WHERE pb.bundle_id = " . tep_get_prid($product_id) . "
										AND p.products_status=1
										AND p.products_purchase_mode<>3
										AND (IF(language_id='" . tep_db_input($languages_id) . "', 1, language_id='" . tep_db_input($default_languages_id) . "'))
								";
            $bundle_result_sql = tep_db_query($bundle_select_sql);

            while ($bundle_data = tep_db_fetch_array($bundle_result_sql)) {
                // Get SYSTEM_PRODUCT_ETA category configuration of this single product. Cannot use package id since its subproduct might not from the same category
                $cat_cfg_array = tep_get_cfg_setting($bundle_data["products_id"], 'product', 'SYSTEM_PRODUCT_ETA');

                switch ($bundle_data["products_purchase_mode"]) {
                    case '1': // Always Add to Cart
                        $show_it = 1;

                        break 2;
                    case '2': // Always Pre-Order
                        $show_it = 2;
                        $cur_eta = $bundle_data["products_eta"] != '' ? $bundle_data["products_eta"] : $cat_cfg_array['SYSTEM_PRODUCT_ETA'];
                        $pre_order_time = ($cur_eta > $pre_order_time) ? $cur_eta : $pre_order_time;

                        if ($bundle_data['products_date_available'] > date('Y-m-d H:i:s')) {
                            if ($bundle_data['products_date_available'] > $max_future_date)
                                $max_future_date = $bundle_data['products_date_available'];
                            $is_future_product = true;
                        }

                        break;
                    case '4': // Auto Mode
                        $pre_order_level = ($bundle_data["products_pre_order_level"] != '') ? $bundle_data["products_pre_order_level"] : '';
                        $diff = $bundle_data['products_quantity'] - $bundle_data["subproduct_qty"];
                        $auto_purchase_mode = true;

                        if (tep_not_null($bundle_data["products_out_of_stock_level"]) && $diff < $bundle_data["products_out_of_stock_level"]) { // If there is setting for out of stock level
; // Out of stock
                        } else if (tep_not_null($pre_order_level) && $diff < $pre_order_level) {
                            $show_it = 2;
                            $cur_eta = $bundle_data["products_eta"] != '' ? $bundle_data["products_eta"] : $cat_cfg_array['SYSTEM_PRODUCT_ETA'];
                            $pre_order_time = ($cur_eta > $pre_order_time) ? $cur_eta : $pre_order_time;

                            if ($bundle_data['products_date_available'] > date('Y-m-d H:i:s')) {
                                if ($bundle_data['products_date_available'] > $max_future_date)
                                    $max_future_date = $bundle_data['products_date_available'];
                                $is_future_product = true;
                            }
                        } else {
                            if (tep_not_null($bundle_data["products_out_of_stock_level"])) {
                                $qty_ratio = ($bundle_data['products_quantity'] - $bundle_data["products_out_of_stock_level"]) / $bundle_data["subproduct_qty"];
                            } else {
                                if (!tep_not_null($bundle_data["products_out_of_stock_level"]) && !tep_not_null($pre_order_level)) { // Same as Always Add to Cart
                                    $show_it = 1;
                                    break 2;
                                } else {
                                    $qty_ratio = $bundle_data['products_quantity'] / $bundle_data["subproduct_qty"];
                                }
                            }
                            if ((int) $qty_ratio >= 1) {
                                $available_weight += (int) $qty_ratio * $bundle_data["subproduct_weight"];
                            }
                        }

                        break;
                }
            }

            if (!$show_it) {

                $show_it = ($available_weight && $available_weight >= $dynamic_weight["products_bundle_dynamic_qty"]) ? 1 : 0;
            } else if ($show_it == 2) {
                $show_it = ($available_weight && $available_weight >= $dynamic_weight["products_bundle_dynamic_qty"]) ? 1 : 2;
            }

            if (!$show_it) {
                $is_future_product = false;
                $pre_order_time = '';
            }

            // Format pre-order time
            if ($is_future_product) {
                $dateObj = explode(' ', trim($max_future_date));
                list($yr, $mth, $day) = explode('-', $dateObj[0]);
                list($hr, $min, $sec) = explode(':', $dateObj[1]);
                $pre_order_time = '#DATE#' . date("F j, Y", mktime((int) $hr, (int) $min, (int) $sec, $mth, $day, $yr)) . '#DATE#';
            }
        } else {
            $show_it = 0;
        }
    } else {
        $product_cart_state_select_sql = "	SELECT products_quantity, products_eta, products_pre_order_level, products_out_of_stock_level, products_purchase_mode, products_date_available, DATE_FORMAT(products_date_available, '%M %e, %Y') AS date_available_string
											FROM " . TABLE_PRODUCTS . "
											WHERE products_id = '" . tep_db_input($product_id) . "'";
        $product_cart_state_result_sql = tep_db_query($product_cart_state_select_sql);
        $product_cart_state_row = tep_db_fetch_array($product_cart_state_result_sql);
        $qty = $product_cart_state_row['products_quantity'];

        // Get SYSTEM_PRODUCT_ETA category configuration of this single product.
        $cat_cfg_array = tep_get_cfg_setting($product_id, 'product', 'SYSTEM_PRODUCT_ETA');

        switch ($product_cart_state_row["products_purchase_mode"]) {
            case '1': // Always Add to Cart
                $show_it = 1;
                break;
            case '2': // Always Pre-Order
                if ($product_cart_state_row['products_date_available'] > date('Y-m-d H:i:s')) {
                    $is_future_product = true;
                    $pre_order_time = '#DATE#' . $product_cart_state_row['date_available_string'] . '#DATE#';
                } else {
                    $pre_order_time = $product_cart_state_row["products_eta"] != '' ? $product_cart_state_row["products_eta"] : $cat_cfg_array['SYSTEM_PRODUCT_ETA'];
                }
                $show_it = 2; // pre-order

                break;
            case '3': // Always Out of Stock
                $show_it = 0;
                break;
            case '4': // Auto Mode
                $pre_order_level = ($product_cart_state_row["products_pre_order_level"] != '') ? $product_cart_state_row["products_pre_order_level"] : '';
                $auto_purchase_mode = true;

                if (tep_not_null($product_cart_state_row["products_out_of_stock_level"]) && $qty <= (int) $product_cart_state_row["products_out_of_stock_level"]) { // If there is setting for out of stock level
                    $show_it = 0;
                } else if (tep_not_null($pre_order_level) && $qty <= $pre_order_level) {
                    $show_it = 2; // pre-order

                    if ($product_cart_state_row['products_date_available'] > date('Y-m-d H:i:s')) {
                        $is_future_product = true;
                        $pre_order_time = '#DATE#' . $product_cart_state_row['date_available_string'] . '#DATE#';
                    } else {
                        $pre_order_time = $product_cart_state_row["products_eta"] != '' ? $product_cart_state_row["products_eta"] : $cat_cfg_array['SYSTEM_PRODUCT_ETA'];
                    }
                } else {
                    $show_it = 1;
                }

                break;
        }
    }

    $product_delivery_msg_select_sql = "SELECT products_add_to_cart_msg, products_preorder_msg, products_out_of_stock_msg
										FROM " . TABLE_PRODUCTS . "
										WHERE products_id = '" . tep_db_input($product_id) . "'";
    $product_delivery_msg_result_sql = tep_db_query($product_delivery_msg_select_sql);
    $product_delivery_msg_row = tep_db_fetch_array($product_delivery_msg_result_sql);

    switch ($show_it) {
        case '0':
            if (tep_not_null($product_delivery_msg_row['products_out_of_stock_msg']))
                $pre_order_time = '#DATE#' . $product_delivery_msg_row['products_out_of_stock_msg'] . '#DATE#';
            break;
        case '1':
            if (tep_not_null($product_delivery_msg_row['products_add_to_cart_msg']))
                $pre_order_time = '#DATE#' . $product_delivery_msg_row['products_add_to_cart_msg'] . '#DATE#';
            break;
        case '2':
            if (tep_not_null($product_delivery_msg_row['products_preorder_msg']))
                $pre_order_time = '#DATE#' . $product_delivery_msg_row['products_preorder_msg'] . '#DATE#';
            break;
    }


    // HLA
    if (tep_not_null($product_instance_id) && tep_get_custom_product_type($product_id) == 4 && ($show_it == 1 || $show_ite == 2)) {
        $hla_available_qty_select_sql = "	SELECT products_hla_id
											FROM " . TABLE_PRODUCTS_HLA . "
											WHERE products_id = '" . tep_db_input($product_id) . "'
												AND products_hla_id = '" . tep_db_input($product_instance_id) . "'
												AND available_quantity > 0
												AND actual_quantity > 0
												AND products_status = '1'
												AND products_display = '1'";
        $hla_available_qty_result = tep_db_query($hla_available_qty_select_sql);
        if (!$hla_available_qty_row = tep_db_fetch_array($hla_available_qty_result)) {
            $show_it = 0;
        }
    }


    $status_info["show"] = $show_it;
    $status_info["pre_order_time"] = $pre_order_time;
    $status_info["is_future_product"] = $is_future_product ? '1' : '0';
    $status_info["subproduct_qty"] = $subproduct_qty;
    $status_info['bundle_qty'] = $bundle_qty;
    $status_info['auto_purchase_mode'] = $auto_purchase_mode;

    return $status_info;
}

function tep_check_dynamic_selections($products_array) {
    for ($i = 0; $i < count($products_array); $i++) {
        $prod_query = tep_db_query("select products_bundle, products_bundle_dynamic from " . TABLE_PRODUCTS . " where products_id = '" . (int) $products_array[$i]['id'] . "'");
        $prod_type = tep_db_fetch_array($prod_query);
        if ($prod_type['products_bundle_dynamic'] == 'yes') {
            if (!count($products_array[$i]['bundle'])) {
                return false;
            }
        }
    }
    return true;
}

////
// Check if the required stock is available
// If insufficent stock then determine whether it is pre-order or out-of-stock status
function tep_check_stock_status($products_id, $products_quantity, $product_instance_id = '', $bundle_id = '') {
    // Products Checkout Quantity Control
    global $memcache_obj;

    $checkout_ctrl_id = tep_not_null($bundle_id) ? $bundle_id : $products_id;
    if (!isset($_SESSION['checkout_ctrl'][$checkout_ctrl_id])) {
        $_SESSION['checkout_ctrl'][$checkout_ctrl_id]['exceed'] = 0;
    }

    $show_it = '';

    $product_info_select_sql = "SELECT products_pre_order_level, products_out_of_stock_level, products_purchase_mode
								FROM " . TABLE_PRODUCTS . "
		    	        		WHERE products_id = '" . tep_db_input($products_id) . "'";
    $product_info_result_sql = tep_db_query($product_info_select_sql);
    $product_info_row = tep_db_fetch_array($product_info_result_sql);

    switch ($product_info_row["products_purchase_mode"]) {
        case '1': // Always Add to Cart
            $show_it = 1;

            break;
        case '2': // Always Pre-Order
            $show_it = 2; // pre-order

            break;
        case '3': // Always Out of Stock
            $show_it = 0;

            break;
        case '4': // Auto Mode
            $stock_left = tep_get_products_stock($products_id) - $products_quantity;
            $pre_order_level = ($product_info_row["products_pre_order_level"] != '') ? $product_info_row["products_pre_order_level"] : '';

            if (tep_not_null($product_info_row["products_out_of_stock_level"]) && $stock_left < (int) $product_info_row["products_out_of_stock_level"]) { // If there is setting for out of stock level
                $show_it = 0;
            } else if (tep_not_null($pre_order_level) && $stock_left < $pre_order_level) {
                $show_it = 2; // pre-order
            } else {
                $show_it = 1;
            }

            break;
    }


    // HLA
    if (tep_not_null($product_instance_id) && tep_get_custom_product_type($products_id) == 4 && ($show_it == 1 || $show_it == 2)) {
        $hla_available_qty_select_sql = "	SELECT products_hla_id
											FROM " . TABLE_PRODUCTS_HLA . "
											WHERE products_id = '" . tep_db_input($products_id) . "'
												AND products_hla_id = '" . tep_db_input($product_instance_id) . "'
												AND available_quantity > 0
												AND actual_quantity > 0
												AND products_status = '1'
												AND products_display = '1'";
        $hla_available_qty_result = tep_db_query($hla_available_qty_select_sql);
        if (!$hla_available_qty_row = tep_db_fetch_array($hla_available_qty_result)) {
            $show_it = 0;
        }
    }

    // Products Checkout Quantity Control [START]
    if ($show_it != '0') {
        $product_cache_key = TABLE_PRODUCTS_CHECKOUT_SETTING . '/checkout_setting/array/products_id/' . $products_id;

        $product_cache_result = $memcache_obj->fetch($product_cache_key);
        if ($product_cache_result !== FALSE) {
            $product_cache = $product_cache_result;
        } else {
            $sql = "SELECT max_purchase_quantity, max_purchase_period
					FROM " . TABLE_PRODUCTS_CHECKOUT_SETTING . "
					WHERE products_id = '" . tep_db_input($products_id) . "'";
            $result_sql = tep_db_query($sql);
            $row = tep_db_fetch_array($result_sql);

            $product_cache['quantity'] = tep_not_null($row['max_purchase_quantity']) ? $row['max_purchase_quantity'] : '';
            $product_cache['minute'] = tep_not_null($row['max_purchase_period']) ? $row['max_purchase_period'] : '';
            $memcache_obj->store($product_cache_key, $product_cache, 86400); // cache 1 day
        }

        if (tep_not_null($product_cache['quantity']) && tep_not_null($product_cache['minute'])) {
            $orders_id_list = array();
            $total_prod_qty = 0;

            $customer_cache_key = TABLE_PRODUCTS_CHECKOUT_SETTING . '/exceed_limit_info/array/customers_id/' . $_SESSION['customer_id'] . '/products_id/' . $products_id;
            $customer_cache_result = $memcache_obj->fetch($customer_cache_key);

            if (($customer_cache_result !== FALSE) && ($product_cache_result !== FALSE) && ((int) $customer_cache_result['quantity'] > 0)) {
                $customer_cache = $customer_cache_result;
            } else {
                // CO history checking, exclude "Cancel"
                $purchase_history_select_sql = "SELECT o.orders_id, op.products_quantity
												FROM " . TABLE_ORDERS . " AS o
												INNER JOIN " . TABLE_ORDERS_PRODUCTS . " AS op
													ON op.orders_id = o.orders_id
												WHERE op.products_id = '" . tep_db_input($products_id) . "'
													AND o.customers_id = '" . tep_db_input($_SESSION['customer_id']) . "'
													AND o.orders_status <> '5'
													AND o.date_purchased >= (DATE_SUB(NOW(), INTERVAL " . $product_cache['minute'] . " MINUTE))
												ORDER BY orders_id DESC";
                $purchase_history_result_sql = tep_db_query($purchase_history_select_sql);
                while ($purchase_history_row = tep_db_fetch_array($purchase_history_result_sql)) {
                    $total_prod_qty += $purchase_history_row['products_quantity'];
                    $orders_id_list[] = $purchase_history_row['orders_id'];
                }

                $customer_cache['quantity'] = $total_prod_qty;
                $customer_cache['orders_id_list'] = array_values(array_unique($orders_id_list));

                $memcache_obj->store($customer_cache_key, $customer_cache, 3600); // cache 1 hours
            }

            $total_prod_in_cart = tep_get_total_product_in_cart($_SESSION['customer_id'], $products_id, $bundle_id);

            if (($customer_cache['quantity'] + $total_prod_in_cart) > $product_cache['quantity']) {
                $_SESSION['checkout_ctrl'][$checkout_ctrl_id]['exceed'] = 1;

                if (tep_not_null($customer_cache['orders_id_list'])) {
                    if (tep_not_null($bundle_id) && is_array($_SESSION['checkout_ctrl'][$checkout_ctrl_id]['orders'])) {
                        $_SESSION['checkout_ctrl'][$checkout_ctrl_id]['orders'] = array_unique(array_merge($_SESSION['checkout_ctrl'][$checkout_ctrl_id]['orders'], $customer_cache['orders_id_list']));
                    } else {
                        $_SESSION['checkout_ctrl'][$checkout_ctrl_id]['orders'] = $customer_cache['orders_id_list'];
                    }
                }
            } else if (!tep_not_null($bundle_id) && ($_SESSION['checkout_ctrl'][$checkout_ctrl_id]['exceed'] == 1)) {
                $_SESSION['checkout_ctrl'][$checkout_ctrl_id]['exceed'] = 0;
            }
        } else {
            $_SESSION['checkout_ctrl'][$checkout_ctrl_id]['exceed'] = 0;
        }
    }
    // Products Checkout Quantity Control [END]

    if ($show_it == "0")
        $stock_status = '<span class="markProductOutOfStock">' . STOCK_MARK_PRODUCT_OUT_OF_STOCK . '</span>';
    else if ($show_it == "2")
        $stock_status = "pre-order";
    else
        $stock_status = '';

    return $stock_status;
}

function tep_get_add_to_cart_suggestion($products_id, $total_purchase_qty, $purchase_qty, $unit_qty) {
    $suggested_add_to_cart_qty = $purchase_qty;

    $product_info_select_sql = "SELECT products_pre_order_level, products_out_of_stock_level, products_purchase_mode
								FROM " . TABLE_PRODUCTS . "
		    	        		WHERE products_id = '" . tep_db_input($products_id) . "'";
    $product_info_result_sql = tep_db_query($product_info_select_sql);
    $product_info_row = tep_db_fetch_array($product_info_result_sql);

    switch ($product_info_row["products_purchase_mode"]) {
        case '1': // Always Add to Cart
            break;
        case '2': // Always Pre-Order
        case '3': // Always Out of Stock
            $suggested_add_to_cart_qty = 0;
            break;
        case '4': // Auto Mode
            $cur_stock_left = tep_get_products_stock($products_id);
            $purchase_qty_exclude_this_package = $total_purchase_qty - ($purchase_qty * $unit_qty);
            $starting_qty = $cur_stock_left - $purchase_qty_exclude_this_package;

            $pre_order_level = ($product_info_row["products_pre_order_level"] != '') ? $product_info_row["products_pre_order_level"] : '';

            if (tep_not_null($product_info_row["products_out_of_stock_level"]) && ($starting_qty < (int) $product_info_row["products_out_of_stock_level"])) { // If there is setting for out of stock level
                $suggested_add_to_cart_qty = 0;
            } else if (tep_not_null($pre_order_level)) {
                if ($starting_qty < $pre_order_level) {
                    $suggested_add_to_cart_qty = 0;
                } else if ($starting_qty - ($purchase_qty * $unit_qty) >= $pre_order_level) {
                    ;
                } else {
                    $suggested_add_to_cart_qty = 0;
                    for ($i = $purchase_qty; $i >= 0; $i--) {
                        if ($starting_qty - ($i * $unit_qty) >= $pre_order_level) {
                            $suggested_add_to_cart_qty = $i;
                            break 2;
                        }
                    }
                }
            }

            break;
    }

    return $suggested_add_to_cart_qty;
}

// Deducting the stock for the order when its status turn to verifying from pending.
// Created by wei chen
function tep_deduct_stock_for_automated_payment($order_id, $products, $from_status_id, $to_status_id, $cid = 0) {
    global $log_object, $customer_id;

    $cid = tep_not_empty($cid) ? $cid : $customer_id;
    $reset_purchase_eta_products_array = array();
    $reset_purchase_eta = false;

    reset($products);

    for ($i = 0; $i < count($products); $i++) {
        if ($products[$i]['custom_products_type_id'] == 4) { // deduct HLA `products_hla`.`available_quantity`
            // HLA products_hla_id
            if (tep_not_null($products[$i]['extra_info']['products_hla'])) {
                $extra_info = tep_array_unserialize($products[$i]['extra_info']['products_hla']);
            }
            $product_instance_id = tep_not_null($extra_info['products_hla_id']) ? $extra_info['products_hla_id'] : '';

            $hla_available_qty_update_sql = "	UPDATE " . TABLE_PRODUCTS_HLA . "
												SET available_quantity = available_quantity - 1
												WHERE products_hla_id = " . (int) $product_instance_id;
            tep_db_query($hla_available_qty_update_sql);
        } else {
            $stock_query = tep_db_query("SELECT products_quantity, products_actual_quantity, products_bundle, products_bundle_dynamic, products_cat_path, products_quantity_order, products_skip_inventory, custom_products_type_id FROM " . TABLE_PRODUCTS . " WHERE products_id = '" . tep_get_prid($products[$i]["id"]) . "'");

            if (tep_db_num_rows($stock_query) > 0) {
                $stock_values = tep_db_fetch_array($stock_query);
                if ($stock_values["products_bundle_dynamic"] == 'yes' || $stock_values['products_bundle'] == 'yes') { // dynamic bundle
                    for ($pbd_loop = 0; $pbd_loop < count($products[$i]["package"]); $pbd_loop++) {
                        $subproduct_stock_query = tep_db_query("SELECT products_quantity, products_actual_quantity, products_bundle, products_bundle_dynamic, products_cat_path, products_quantity_order, products_skip_inventory, custom_products_type_id FROM " . TABLE_PRODUCTS . " WHERE products_id = '" . tep_get_prid($products[$i]["package"][$pbd_loop]["id"]) . "'");
                        $subproduct_stock_values = tep_db_fetch_array($subproduct_stock_query);

                        $estimate_stock_left = $subproduct_stock_values["products_quantity"] - $products[$i]["package"][$pbd_loop]["qty"];

                        if (isset($products[$i]["package"][$pbd_loop]['custom_content']['delivery_mode']) && $products[$i]["package"][$pbd_loop]['custom_content']['delivery_mode'] == '6') {
                            //
                        } else {
                            // Update product's quantity if do not keep inventory is not set and keep a log for it
                            if (!$subproduct_stock_values["products_skip_inventory"]) {
                                $update_qty_array = array(array('field_name' => 'products_quantity',
                                        'operator' => '=',
                                        'value' => $estimate_stock_left)
                                );
                                // This function will handle the qty adjustment and keep the log if asking so
                                tep_set_product_qty(tep_get_prid($products[$i]["package"][$pbd_loop]["id"]), $update_qty_array, true, sprintf(LOG_SALES_ORDER, $order_id, $from_status_id, $to_status_id), '');

                                $cat_cfg_array = tep_get_cfg_setting($products[$i]["package"][$pbd_loop]["id"], 'product');
                                $email_to_array = tep_parse_email_string($cat_cfg_array['LOW_STOCK_EMAIL']);

                                $stock_reorder = $subproduct_stock_values['products_quantity_order']; // individual product reorder level, if any
                                $warning_stock = ($stock_reorder != "") ? $stock_reorder : $cat_cfg_array['STOCK_REORDER_LEVEL'];

                                $current_stock = $estimate_stock_left;

                                $low_stock_email = '<b>Low stock warning:</b> ' . $products[$i]["package"][$pbd_loop]["name"] . "\n" . '<b>Model No.:</b> ' . $products[$i]["package"][$pbd_loop]["model"] . "\n" . '<b>Quantity:</b> ' . $estimate_stock_left . "\n" . '<b>Product URL:</b> ' . HTTP_SERVER . DIR_WS_HTTP_CATALOG . 'custom_product_info.php?products_id=' . $products[$i]["package"][$pbd_loop]["id"] . "\n\n" . '<b>Product\'s Reorder Level:</b> ' . $warning_stock . " units\n\n<b>Category Path:</b> " . $subproduct_stock_values["products_cat_path"];
                                $low_stock_subject = 'Low Stock Warning: ' . strip_tags($products[$i]["package"][$pbd_loop]["name"]);

                                if ($current_stock <= $warning_stock) {
                                    if ($subproduct_stock_values["custom_products_type_id"] == '2') {
                                        $low_stock_data = array('products_id' => $products[$i]["package"][$pbd_loop]["id"], 'custom_products_type_id' => $subproduct_stock_values["custom_products_type_id"]);
                                        products_low_stock::add_low_stock_warning($low_stock_data);
                                    }
                                    if ((int) $subproduct_stock_values["custom_products_type_id"] > 0) {
                                        //If custom product, check if exists config for low stock email to overwrite global.
                                        $custom_product_low_stock_email_select_sql = "select custom_products_low_stock_email from " . TABLE_CUSTOM_PRODUCTS_TYPE . " where custom_products_type_id = '" . tep_db_prepare_input($subproduct_stock_values["custom_products_type_id"]) . "'";
                                        $custom_product_low_stock_email_result = tep_db_query($custom_product_low_stock_email_select_sql);
                                        if (($custom_product_low_stock_email_row = tep_db_fetch_array($custom_product_low_stock_email_result)) && tep_not_null($custom_product_low_stock_email_row['custom_products_low_stock_email'])) {
                                            if (tep_not_null($cat_cfg_array[$custom_product_low_stock_email_row['custom_products_low_stock_email']])) {
                                                $email_to_array = tep_parse_email_string($cat_cfg_array[$custom_product_low_stock_email_row['custom_products_low_stock_email']]);
                                            }
                                        }
                                    }

                                    for ($email_cnt = 0; $email_cnt < count($email_to_array); $email_cnt++) {
                                        tep_mail($email_to_array[$email_cnt]['name'], $email_to_array[$email_cnt]['email'], $low_stock_subject, $low_stock_email, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
                                    }
                                }
                            }
                        }
                        $reset_purchase_eta_products_array[] = $products[$i]["package"][$pbd_loop]["orders_products_id"];
                    }
                } else { // single product
                    $estimate_stock_left = $stock_values['products_quantity'] - $products[$i]['qty'];

                    if (isset($products[$i]['custom_content']['delivery_mode']) && $products[$i]['custom_content']['delivery_mode'] == 6) {
                        //
                    } else {
                        //Update product's quantity if do not keep inventory is not set and keep a log for it
                        if (!$stock_values["products_skip_inventory"]) {
                            if (isset($products[$i]['custom_content']['delivery_mode']) && $products[$i]['custom_content']['delivery_mode'] == '6') {
                                //
                            } else {
                                $update_qty_array = array(array('field_name' => 'products_quantity',
                                        'operator' => '=',
                                        'value' => $estimate_stock_left)
                                );
                                // This function will handle the qty adjustment and keep the log if asking so
                                tep_set_product_qty(tep_get_prid($products[$i]['id']), $update_qty_array, true, sprintf(LOG_SALES_ORDER, $order_id, $from_status_id, $to_status_id), '');
                            }
                        }
                    }

                    if (isset($products[$i]['custom_content']['delivery_mode']) && $products[$i]['custom_content']['delivery_mode'] == 6) {
                        //
                    } else {
                        if (!$stock_values["products_skip_inventory"]) {
                            $cat_cfg_array = tep_get_cfg_setting($products[$i]['id'], 'product');
                            $email_to_array = tep_parse_email_string($cat_cfg_array['LOW_STOCK_EMAIL']);

                            $stock_reorder = $stock_values['products_quantity_order'];
                            $warning_stock = ($stock_reorder != "") ? $stock_reorder : $cat_cfg_array['STOCK_REORDER_LEVEL'];

                            $current_stock = $estimate_stock_left;

                            $low_stock_email = '<b>Low stock warning:</b> ' . $products[$i]['name'] . "\n" . '<b>Model No.:</b> ' . $products[$i]['model'] . "\n" . '<b>Quantity:</b> ' . $estimate_stock_left . "\n" . '<b>Product URL:</b> ' . HTTP_SERVER . DIR_WS_HTTP_CATALOG . 'custom_product_info.php?products_id=' . $products[$i]['id'] . "\n\n" . '<b>Product\'s Reorder Level:</b> ' . $warning_stock . " units\n\n<b>Category Path:</b> " . $stock_values['products_cat_path'];
                            $low_stock_subject = 'Low Stock Warning: ' . strip_tags($products[$i]['name']);

                            if ($current_stock <= $warning_stock) {
                                if ($stock_values["custom_products_type_id"] == '2') {
                                    $low_stock_data = array('products_id' => $products[$i]['id'], 'custom_products_type_id' => $stock_values["custom_products_type_id"]);
                                    products_low_stock::add_low_stock_warning($low_stock_data);
                                }
                                if ((int) $stock_values["custom_products_type_id"] > 0) {
                                    //If custom product, check if exists config for low stock email to overwrite global.
                                    $custom_product_low_stock_email_select_sql = "select custom_products_low_stock_email from " . TABLE_CUSTOM_PRODUCTS_TYPE . " where custom_products_type_id = '" . tep_db_prepare_input($stock_values["custom_products_type_id"]) . "'";
                                    $custom_product_low_stock_email_result = tep_db_query($custom_product_low_stock_email_select_sql);
                                    if (($custom_product_low_stock_email_row = tep_db_fetch_array($custom_product_low_stock_email_result)) && tep_not_null($custom_product_low_stock_email_row['custom_products_low_stock_email'])) {
                                        if (tep_not_null($cat_cfg_array[$custom_product_low_stock_email_row['custom_products_low_stock_email']])) {
                                            $email_to_array = tep_parse_email_string($cat_cfg_array[$custom_product_low_stock_email_row['custom_products_low_stock_email']]);
                                        }
                                    }
                                }

                                for ($email_cnt = 0; $email_cnt < count($email_to_array); $email_cnt++) {
                                    tep_mail($email_to_array[$email_cnt]['name'], $email_to_array[$email_cnt]['email'], $low_stock_subject, $low_stock_email, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
                                }
                            }
                        }
                    }

                    $reset_purchase_eta_products_array[] = $products[$i]["orders_products_id"];
                    if ($products[$i]['custom_products_type_id'] == 1)
                        $reset_purchase_eta = true;
                }
            }//end if
        }
    }//end for loop

    /*     * ********************************************************
      Reset the Purchase ETA to -999 (Not counted as buyback
      demand, awaiting admin to enter the ETA)
     * ******************************************************** */
    if (!$reset_purchase_eta) {
        if ($to_status_id == 7) {
            $reset_purchase_eta = true;
        } else {
            $pwl_order_select_sql = "	SELECT COUNT(DISTINCT o.orders_id) AS total_count
										FROM " . TABLE_ORDERS . " AS o
										INNER JOIN " . TABLE_ORDERS_PRODUCTS . " AS op
											ON o.orders_id=op.orders_id
										WHERE customers_id='" . tep_db_input($cid) . "'
											AND orders_status='2'
											AND op.custom_products_type_id=1";
            $pwl_order_result_sql = tep_db_query($pwl_order_select_sql);
            $pwl_order_row = tep_db_fetch_array($pwl_order_result_sql);

            if ($pwl_order_row['total_count'] > 0)
                $reset_purchase_eta = true;
        }
    }

    if (count($reset_purchase_eta_products_array)) {
        if ($reset_purchase_eta) {
            $eta_update_sql = "	UPDATE " . TABLE_ORDERS_PRODUCTS . "
								SET orders_products_purchase_eta = -999
								WHERE orders_products_id IN ('" . implode("', '", $reset_purchase_eta_products_array) . "')
									AND custom_products_type_id = 0 ";
            tep_db_query($eta_update_sql);
        } else if ($to_status_id == 2) {
            $customer_profile_email = tep_get_customers_email($cid);
            if ($cid < '127270' || tep_info_verified_check($cid, $customer_profile_email, 'email') == '1') {
                $eta_update_sql = "	UPDATE " . TABLE_ORDERS_PRODUCTS . " AS opMain
									INNER JOIN " . TABLE_ORDERS_PRODUCTS . " AS opSub
										ON opMain.orders_products_id=opSub.parent_orders_products_id
									INNER JOIN " . TABLE_ORDERS_PRODUCTS_EXTRA_INFO . " AS opei
										ON (opMain.orders_products_id=opei.orders_products_id AND opei.orders_products_extra_info_key = 'char_online_time')
									SET opSub.orders_products_purchase_eta=opei.orders_products_extra_info_value
									WHERE opSub.orders_products_id IN ('" . implode("', '", $reset_purchase_eta_products_array) . "')
										AND opSub.custom_products_type_id = 0 ";
                tep_db_query($eta_update_sql);
            }
        }
    }
}

//// By Wei Chen
// Sets the available & actual qty of a product
function tep_set_product_qty($products_id, $qty_array, $keep_log = true, $admin_msg = '', $user_msg = '') {
    global $log_object;
    /*     * *****************************************************************
      operator = + (add stock), - (deduct stock), = (assign new value)
     * ***************************************************************** */
    $sql_update_array = array();

    // Generate the update sql
    for ($qty_cnt = 0; $qty_cnt < count($qty_array); $qty_cnt++) {
        if (($qty_array[$qty_cnt]['field_name'] == 'products_quantity' || $qty_array[$qty_cnt]['field_name'] == 'products_actual_quantity')) {
            $qty_array[$qty_cnt]['operator'] = trim($qty_array[$qty_cnt]['operator']);
            switch ($qty_array[$qty_cnt]['operator']) {
                case '+':
                    $sql_update_array[] = $qty_array[$qty_cnt]['field_name'] . ' = ' . $qty_array[$qty_cnt]['field_name'] . ' + ' . tep_db_input($qty_array[$qty_cnt]['value']);
                    break;
                case '-':
                    $sql_update_array[] = $qty_array[$qty_cnt]['field_name'] . ' = ' . $qty_array[$qty_cnt]['field_name'] . ' - ' . tep_db_input($qty_array[$qty_cnt]['value']);
                    break;
                case '=':
                    $sql_update_array[] = $qty_array[$qty_cnt]['field_name'] . ' = ' . tep_db_input($qty_array[$qty_cnt]['value']);
                    break;
                default:
                    break;
            }
        }
    }

    if (count($sql_update_array)) {
        if ($keep_log) {
            $product_stock_select_sql = "	SELECT products_quantity, products_actual_quantity
											FROM " . TABLE_PRODUCTS . "
											WHERE products_id = '" . tep_db_input($products_id) . "'";
            $product_stock_result_sql = tep_db_query($product_stock_select_sql);

            if ($product_stock_row = tep_db_fetch_array($product_stock_result_sql)) {
                $previous_product_quantity = $product_stock_row['products_quantity'];
                $previous_product_actual_quantity = $product_stock_row['products_actual_quantity'];
            } else {
                $previous_product_quantity = 0;
                $previous_product_actual_quantity = 0;
            }
        }

        $update_sql_str = " SET " . implode(', ', $sql_update_array);

        $product_stock_update_sql = "	UPDATE " . TABLE_PRODUCTS .
                $update_sql_str . "
										WHERE products_id = '" . tep_db_input($products_id) . "'";
        tep_db_query($product_stock_update_sql);

        /*         * ***********************************************************
          This section must be outside the if ($keep_log) section
          since we need it to grab the latest FIFO Average price
         * *********************************************************** */
        $product_stock_select_sql = "	SELECT products_quantity, products_actual_quantity
										FROM " . TABLE_PRODUCTS . "
										WHERE products_id = '" . tep_db_input($products_id) . "'";
        $product_stock_result_sql = tep_db_query($product_stock_select_sql);

        if ($product_stock_row = tep_db_fetch_array($product_stock_result_sql)) {
            $new_product_quantity = $product_stock_row['products_quantity'];
            $new_product_actual_quantity = $product_stock_row['products_actual_quantity'];

            // Calculate the latest FIFO Average price
            tep_set_product_fifo_avg_price($products_id, $new_product_quantity, $new_product_actual_quantity);
        } else {
            $new_product_quantity = 0;
            $new_product_actual_quantity = 0;
        }

        if ($keep_log) {
            $log_object->insert_log($products_id, 'products_quantity', $previous_product_quantity . ':~:' . $previous_product_actual_quantity, $new_product_quantity . ':~:' . $new_product_actual_quantity, $admin_msg, $user_msg);
        }
    }
}

//// By Wei Chen
function tep_set_product_fifo_avg_price($products_id, $available_qty, $actual_qty) {
    $qty_type_array = array('available' => array('qty' => $available_qty, 'fifo_field' => 'products_quantity_fifo_cost'),
        'actual' => array('qty' => $actual_qty, 'fifo_field' => 'products_actual_quantity_fifo_cost')
    );

    foreach ($qty_type_array as $qty_type => $qty_info) {
        $total_cost = 0;
        if ($qty_info['qty'] > 0) {
            $current_stock = $qty_info['qty'];
            $keep_looping = true;
            $loop = 0;
            $rec_count = 10;
            do {
                $offset = $loop * $rec_count;

                $restock_select_sql = "	SELECT brg.buyback_request_group_date AS ref_date, br.buyback_quantity_received AS rstk_qty, (IF(br.buyback_request_quantity > 0, buyback_amount/br.buyback_request_quantity, 0)) AS unit_price
										FROM " . TABLE_BUYBACK_REQUEST_GROUP . " AS brg
										INNER JOIN " . TABLE_BUYBACK_REQUEST . " AS br
											ON (brg.buyback_request_group_id=br.buyback_request_group_id AND br.products_id='" . tep_db_input($products_id) . "')
										WHERE brg.buyback_status_id IN (1, 2, 3)
										ORDER BY ref_date desc
										LIMIT " . $offset . ", " . $rec_count;
                $restock_result_sql = tep_db_query($restock_select_sql);
                if (tep_db_num_rows($restock_result_sql)) {
                    while ($current_stock > 0 && $restock_row = tep_db_fetch_array($restock_result_sql)) {
                        $list_price = (double) $restock_row['unit_price'];
                        $list_rstk_qty = $restock_row['rstk_qty'];

                        if ($list_rstk_qty > 0) {
                            if ($current_stock > $list_rstk_qty) {
                                $total_cost += $list_rstk_qty * $list_price;
                                $current_stock -= $list_rstk_qty;
                            } else {
                                $total_cost += $current_stock * $list_price;
                                $current_stock = 0;
                            }
                        }
                    }
                } else {
                    $keep_looping = false; // End the loop
                }

                $loop++;
            } while ($current_stock > 0 && $keep_looping);

            if ($current_stock > 0) { // Not enough information
                $calculated_unit_price = 'NULL';
            } else {
                $calculated_unit_price = $total_cost / $qty_info['qty'];
            }
            $fifo_cost_data_array = array($qty_info['fifo_field'] => is_numeric($calculated_unit_price) ? round($calculated_unit_price, 6) : $calculated_unit_price);
            tep_db_perform(TABLE_PRODUCTS, $fifo_cost_data_array, 'update', "products_id = '" . tep_db_input($products_id) . "'");
        } else {
            $fifo_cost_data_array = array($qty_info['fifo_field'] => $total_cost);
            tep_db_perform(TABLE_PRODUCTS, $fifo_cost_data_array, 'update', "products_id = '" . tep_db_input($products_id) . "'");
        }
    }
}

function tep_get_first_list_quantity($products_id) {
    $first_list_quantity = 0;

    $buyback_prod_pending_list_qty_select_sql = "	SELECT sum(br.buyback_request_quantity) as first_list_qty_total
								   		         	FROM " . TABLE_BUYBACK_REQUEST_GROUP . " AS brg
													INNER JOIN " . TABLE_BUYBACK_REQUEST . " AS br
								                		ON brg.buyback_request_group_id = br.buyback_request_group_id
								            		WHERE br.products_id = '" . tep_db_input($products_id) . "'
								            			AND brg.buyback_status_id = 1 ";
    $buyback_prod_pending_list_qty_result_sql = tep_db_query($buyback_prod_pending_list_qty_select_sql);
    if ($buyback_prod_pending_list_qty_row = tep_db_fetch_array($buyback_prod_pending_list_qty_result_sql)) {
        $first_list_quantity += (int) $buyback_prod_pending_list_qty_row['first_list_qty_total'];
    }

    $buyback_prod_processing_list_qty_select_sql = "SELECT sum(IF(brg.buyback_request_group_site_id > 0, br.buyback_quantity_confirmed, br.buyback_request_quantity)) as first_list_qty_total
								   		         	FROM " . TABLE_BUYBACK_REQUEST_GROUP . " AS brg
													INNER JOIN " . TABLE_BUYBACK_REQUEST . " AS br
								                		ON brg.buyback_request_group_id = br.buyback_request_group_id
								            		WHERE br.products_id = '" . tep_db_input($products_id) . "'
								            			AND brg.buyback_status_id = 2
								            			AND br.buyback_quantity_received=0";
    $buyback_prod_processing_list_qty_result_sql = tep_db_query($buyback_prod_processing_list_qty_select_sql);
    if ($buyback_prod_processing_list_qty_row = tep_db_fetch_array($buyback_prod_processing_list_qty_result_sql)) {
        $first_list_quantity += (int) $buyback_prod_processing_list_qty_row['first_list_qty_total'];
    }

    return $first_list_quantity;
}

function tep_get_so_first_list_quantity($orders_id, $orders_products_id) {
    $first_list_quantity = 0;
    $buyback_prod_pending_list_qty_select_sql = "	SELECT sum(br.buyback_request_quantity) as first_list_qty_total
								   		         	FROM " . TABLE_BUYBACK_REQUEST_GROUP . " AS brg
													INNER JOIN " . TABLE_BUYBACK_REQUEST . " AS br
								                		ON brg.buyback_request_group_id = br.buyback_request_group_id
								            		WHERE brg.buyback_status_id NOT IN(3,4)
								            			AND br.buyback_quantity_received = 0
								            			AND br.orders_id = '" . (int) $orders_id . "'
														AND br.orders_products_id = '" . (int) $orders_products_id . "'";
    $buyback_prod_pending_list_qty_result_sql = tep_db_query($buyback_prod_pending_list_qty_select_sql);

    if ($buyback_prod_pending_list_qty_row = tep_db_fetch_array($buyback_prod_pending_list_qty_result_sql)) {
        $first_list_quantity += (int) $buyback_prod_pending_list_qty_row['first_list_qty_total'];
    }

    return $first_list_quantity;
}

////
// Break a word in a string if it is longer than a specified length ($len)
function tep_break_string($string, $len, $break_char = '-') {
    $l = 0;
    $output = '';
    for ($i = 0, $n = strlen($string); $i < $n; $i++) {
        $char = substr($string, $i, 1);
        if ($char != ' ') {
            $l++;
        } else {
            $l = 0;
        }
        if ($l > $len) {
            $l = 1;
            $output .= $break_char;
        }
        $output .= $char;
    }

    return $output;
}

////
// Return all HTTP GET variables, except those passed as a parameter
function tep_get_all_get_params($exclude_array = '') {
    global $HTTP_GET_VARS;

    if (!is_array($exclude_array))
        $exclude_array = array();

    $get_url = '';
    if (is_array($HTTP_GET_VARS) && (sizeof($HTTP_GET_VARS) > 0)) {
        reset($HTTP_GET_VARS);
        while (list($key, $value) = each($HTTP_GET_VARS)) {
            if ((strlen($value) > 0) && ($key != tep_session_name()) && ($key != 'error') && (!in_array($key, $exclude_array)) && ($key != 'x') && ($key != 'y')) {
                $get_url .= $key . '=' . rawurlencode(stripslashes($value)) . '&';
            }
        }
    }

    return $get_url;
}

function tep_get_actual_product_cat_id($prod_id) {
    $category_select_sql = "SELECT categories_id FROM " . TABLE_PRODUCTS_TO_CATEGORIES . " WHERE products_id = '" . $prod_id . "' AND products_is_link=0";
    $category_result_sql = tep_db_query($category_select_sql);
    $category_id = tep_db_fetch_array($category_result_sql);

    return $category_id["categories_id"];
}

////
// Returns an array with countries
// TABLES: countries
function tep_get_countries($countries_id = '', $with_iso_codes = false) {
    $countries_array = array();
    if (tep_not_null($countries_id)) {
        if ($with_iso_codes == true) {
            $countries = tep_db_query("select countries_name, countries_iso_code_2, countries_iso_code_3 from " . TABLE_COUNTRIES . " where countries_id = '" . (int) $countries_id . "' order by countries_name");
            $countries_values = tep_db_fetch_array($countries);
            $countries_array = array('countries_name' => $countries_values['countries_name'],
                'countries_iso_code_2' => $countries_values['countries_iso_code_2'],
                'countries_iso_code_3' => $countries_values['countries_iso_code_3']);
        } else {
            $countries = tep_db_query("select countries_name from " . TABLE_COUNTRIES . " where countries_id = '" . (int) $countries_id . "'");
            $countries_values = tep_db_fetch_array($countries);
            $countries_array = array('countries_name' => $countries_values['countries_name']);
        }
    } else {
        $countries = tep_db_query("select countries_id, countries_name from " . TABLE_COUNTRIES . " where countries_display = 1 order by countries_name");
        while ($countries_values = tep_db_fetch_array($countries)) {
            $countries_array[] = array('countries_id' => $countries_values['countries_id'],
                'countries_name' => $countries_values['countries_name']);
        }
    }

    return $countries_array;
}

////
// Alias function to tep_get_countries, which also returns the countries iso codes
function tep_get_countries_with_iso_codes($countries_id) {
    return tep_get_countries($countries_id, true);
}

// Changes made @ 200809161819 refering Mantis # 0000024: Get Countries' International Dialing Codes
function tep_get_coutries_dialing_codes($countries_id) {
    if (tep_not_null($countries_id)) {
        $countries = tep_db_query("select countries_international_dialing_code from " . TABLE_COUNTRIES . " where countries_id = '" . (int) $countries_id . "'");
        $countries_values = tep_db_fetch_array($countries);
        $countries_array = array('countries_international_dialing_code' => $countries_values['countries_international_dialing_code']);
    }

    return $countries_array;
}

// ##
// Generate a path to categories
function tep_get_path($current_category_id = '') {
    global $cPath_array;

    if (tep_not_null($current_category_id)) {
        $cPath_new = $current_category_id;

        $parent_category_select_sql = "SELECT parent_id FROM " . TABLE_CATEGORIES . " WHERE categories_id = '" . (int) $current_category_id . "' AND parent_id<>0";
        $parent_category_result_sql = tep_db_query($parent_category_select_sql);

        while ($parent_category_row = tep_db_fetch_array($parent_category_result_sql)) {
            $cPath_new = $parent_category_row["parent_id"] . '_' . $cPath_new;

            $parent_category_select_sql = "SELECT parent_id FROM " . TABLE_CATEGORIES . " WHERE categories_id = '" . $parent_category_row["parent_id"] . "' AND parent_id<>0";
            $parent_category_result_sql = tep_db_query($parent_category_select_sql);
        }
    } else {
        $cPath_new = implode('_', $cPath_array);
    }

    return 'cPath=' . $cPath_new;
}

function tep_get_particular_cat_path($category_id) {
    global $memcache_obj;

    $this_cat_path = '';
    if (tep_not_null($category_id)) {
        $cache_key = TABLE_CATEGORIES . '/categories_id/' . $category_id . '/particular_cat_path';
        $cache_result = $memcache_obj->fetch($cache_key);

        if ($cache_result !== FALSE) {
            $this_cat_path = $cache_result;
        } else {
            $cat_id_array = array($category_id);
            $category_query = tep_db_query("SELECT parent_id FROM " . TABLE_CATEGORIES . " WHERE categories_id = '" . (int) $category_id . "' AND parent_id<>0");
            while ($category = tep_db_fetch_array($category_query)) {
                $cat_id_array[] = $category["parent_id"];
                $category_query = tep_db_query("SELECT parent_id FROM " . TABLE_CATEGORIES . " WHERE categories_id = '" . $category["parent_id"] . "' AND parent_id<>0");
            }

            $cat_id_array = array_reverse($cat_id_array);
            $this_cat_path = count($cat_id_array) ? implode('_', $cat_id_array) : '';

            $memcache_obj->store($cache_key, $this_cat_path, 7200);
        }
    }

    return $this_cat_path;
}

////
// Returns the clients browser
function tep_browser_detect($component) {
    global $HTTP_USER_AGENT;

    return stristr($HTTP_USER_AGENT, $component);
}

function tep_get_countries_info($seek_value, $fieldname = 'countries_name') {
    $country_info_select_sql = "select * from " . TABLE_COUNTRIES . " where $fieldname = '" . tep_db_input($seek_value) . "'";
    $country_info_result_sql = tep_db_query($country_info_select_sql);
    $country_info_row = tep_db_fetch_array($country_info_result_sql);

    $country_info = array('id' => $country_info_row['countries_id'],
        'countries_name' => $country_info_row['countries_name'],
        'countries_iso_code_2' => $country_info_row['countries_iso_code_2'],
        'countries_iso_code_3' => $country_info_row['countries_iso_code_3'],
        'address_format_id' => $country_info_row['address_format_id'],
        'aft_risk_type' => $country_info_row['aft_risk_type']);

    return $country_info;
}

////
// Alias function to tep_get_countries()
function tep_get_country_name($country_id) {
    $country_array = tep_get_countries($country_id);

    return $country_array['countries_name'];
}

////
// Returns the zone (State/Province) name
// TABLES: zones
function tep_get_zone_name($country_id, $zone_id, $default_zone) {
    $zone_query = tep_db_query("select zone_name from " . TABLE_ZONES . " where zone_country_id = '" . (int) $country_id . "' and zone_id = '" . (int) $zone_id . "'");
    if (tep_db_num_rows($zone_query)) {
        $zone = tep_db_fetch_array($zone_query);
        return $zone['zone_name'];
    } else {
        return $default_zone;
    }
}

////
// Returns the zone (State/Province) code
// TABLES: zones
function tep_get_zone_code($country_id, $zone_id, $default_zone) {
    $zone_query = tep_db_query("select zone_code from " . TABLE_ZONES . " where zone_country_id = '" . (int) $country_id . "' and zone_id = '" . (int) $zone_id . "'");
    if (tep_db_num_rows($zone_query)) {
        $zone = tep_db_fetch_array($zone_query);
        return $zone['zone_code'];
    } else {
        return $default_zone;
    }
}

////
// Wrapper function for round()
function tep_round($number, $precision) {
    $number = number_format($number, $precision, '.', '');
    /*
      if (strpos($number, '.') && (strlen(substr($number, strpos($number, '.')+1)) > $precision)) {
      $number = substr($number, 0, strpos($number, '.') + 1 + $precision + 1);

      if (substr($number, -1) >= 5) {
      if ($precision > 1) {
      $number = substr($number, 0, -1) + ('0.' . str_repeat(0, $precision-1) . '1');
      } elseif ($precision == 1) {
      $number = substr($number, 0, -1) + 0.1;
      } else {
      $number = substr($number, 0, -1) + 1;
      }
      } else {
      $number = substr($number, 0, -1);
      }
      }
     */

    return $number;
}

function tep_get_product_tax_class($product_id) {
    $tax_class_select_sql = "SELECT products_tax_class_id FROM " . TABLE_PRODUCTS . " WHERE products_id ='" . (int) $product_id . "'";
    $tax_class_result_sql = tep_db_query($tax_class_select_sql);
    $tax_class_row = tep_db_fetch_array($tax_class_result_sql);

    return $tax_class_row["products_tax_class_id"];
}

////
// Returns the tax rate for a zone / class
// TABLES: tax_rates, zones_to_geo_zones
function tep_get_tax_rate($class_id, $country_id = -1, $zone_id = -1) {
    global $customer_zone_id, $customer_country_id;

    if (($country_id == -1) && ($zone_id == -1)) {
        if (!tep_session_is_registered('customer_id')) {
            $country_id = STORE_COUNTRY;
            $zone_id = STORE_ZONE;
        } else {
            $country_id = $customer_country_id;
            $zone_id = $customer_zone_id;
        }
    }

    $tax_query = tep_db_query("select sum(tax_rate) as tax_rate from " . TABLE_TAX_RATES . " tr left join " . TABLE_ZONES_TO_GEO_ZONES . " za on (tr.tax_zone_id = za.geo_zone_id) left join " . TABLE_GEO_ZONES . " tz on (tz.geo_zone_id = tr.tax_zone_id) where (za.zone_country_id is null or za.zone_country_id = '0' or za.zone_country_id = '" . (int) $country_id . "') and (za.zone_id is null or za.zone_id = '0' or za.zone_id = '" . (int) $zone_id . "') and tr.tax_class_id = '" . (int) $class_id . "' group by tr.tax_priority");
    if (tep_db_num_rows($tax_query)) {
        $tax_multiplier = 1.0;
        while ($tax = tep_db_fetch_array($tax_query)) {
            $tax_multiplier *= 1.0 + ($tax['tax_rate'] / 100);
        }
        return ($tax_multiplier - 1.0) * 100;
    } else {
        return 0;
    }
}

////
// Return the tax description for a zone / class
// TABLES: tax_rates;
function tep_get_tax_description($class_id, $country_id, $zone_id) {
    $tax_query = tep_db_query("select tax_description from " . TABLE_TAX_RATES . " tr left join " . TABLE_ZONES_TO_GEO_ZONES . " za on (tr.tax_zone_id = za.geo_zone_id) left join " . TABLE_GEO_ZONES . " tz on (tz.geo_zone_id = tr.tax_zone_id) where (za.zone_country_id is null or za.zone_country_id = '0' or za.zone_country_id = '" . (int) $country_id . "') and (za.zone_id is null or za.zone_id = '0' or za.zone_id = '" . (int) $zone_id . "') and tr.tax_class_id = '" . (int) $class_id . "' order by tr.tax_priority");
    if (tep_db_num_rows($tax_query)) {
        $tax_description = '';
        while ($tax = tep_db_fetch_array($tax_query)) {
            $tax_description .= $tax['tax_description'] . ' + ';
        }
        $tax_description = substr($tax_description, 0, -3);

        return $tax_description;
    } else {
        return TEXT_UNKNOWN_TAX_RATE;
    }
}

////
// Add tax to a products price
function tep_add_tax($price, $tax) {
    global $currencies;
    if ((DISPLAY_PRICE_WITH_TAX == 'true') && ($tax > 0)) {
        return tep_round($price, $currencies->currencies[DEFAULT_CURRENCY]['decimal_places']) + tep_calculate_tax($price, $tax);
    } else {
        return tep_round($price, $currencies->currencies[DEFAULT_CURRENCY]['decimal_places']);
    }
}

// Calculates Tax rounding the result
function tep_calculate_tax($price, $tax) {
    global $currencies;

    return tep_round($price * $tax / 100, $currencies->currencies[DEFAULT_CURRENCY]['decimal_places']);
}

////
// Return the number of products in a category
// TABLES: products, products_to_categories, categories
function tep_count_products_in_category($category_id, $include_inactive = false, $include_hidden = false, $include_subcategories = true) {
    global $customers_groups_id, $const_alluseraccess;
    $products_count = 0;

    $StatusWhereStr = ($include_inactive == true) ? " 1" : " p.products_status='1' ";
    $DisplayWhereStr = ($include_hidden == true) ? " 1" : " ( p.products_bundle='yes' OR p.products_bundle_dynamic='yes' OR (p.products_bundle<>'yes' AND p.products_bundle_dynamic<>'yes' AND p.products_display='1')) ";

    $items_count_select_sql = "	SELECT COUNT(p2c.products_id) AS total
    							FROM " . TABLE_PRODUCTS_TO_CATEGORIES . " AS p2c
    							INNER JOIN " . TABLE_CATEGORIES . " AS c
    								ON p2c.categories_id=c.categories_id
    							INNER JOIN " . TABLE_CATEGORIES_GROUPS . " AS cg
									ON c.categories_id = cg.categories_id
								INNER JOIN " . TABLE_PRODUCTS . " AS p
									ON p2c.products_id=p.products_id
    							WHERE c.categories_id='" . (int) $category_id . "' AND c.categories_status=1
    								AND ((cg.groups_id = '" . $customers_groups_id . "') OR (cg.groups_id = '" . $const_alluseraccess . "')) AND " . $StatusWhereStr . " AND " . $DisplayWhereStr;
    $items_count_result_sql = tep_db_query($items_count_select_sql);
    $items_count_row = tep_db_fetch_array($items_count_result_sql);
    $products_count += $items_count_row['total'];

    if ($include_subcategories) {
        $sub_categories_select_sql = "	SELECT c.categories_id
										FROM " . TABLE_CATEGORIES . " AS c
										INNER JOIN " . TABLE_CATEGORIES_GROUPS . " AS cg
											ON c.categories_id = cg.categories_id
										WHERE c.parent_id = '" . (int) $category_id . "' AND c.categories_status=1
											AND ((cg.groups_id = '" . $customers_groups_id . "') OR (cg.groups_id = '" . $const_alluseraccess . "'))";
        $sub_categories_result_sql = tep_db_query($sub_categories_select_sql);
        if (tep_db_num_rows($sub_categories_result_sql)) {
            while ($sub_categories_row = tep_db_fetch_array($sub_categories_result_sql)) {
                $products_count += tep_count_products_in_category($sub_categories_row['categories_id'], $include_inactive, $include_hidden);
            }
        }
    }

    return $products_count;
}

function tep_count_all_products_in_category($category_id, &$cat_count_array, $include_inactive = false, $include_hidden = false) {
    global $customers_groups_id, $const_alluseraccess;
    $products_count = 0;

    $StatusWhereStr = ($include_inactive == true) ? " 1" : " p.products_status='1' ";
    $DisplayWhereStr = ($include_hidden == true) ? " 1" : " ( p.products_bundle='yes' OR p.products_bundle_dynamic='yes' OR (p.products_bundle<>'yes' AND p.products_bundle_dynamic<>'yes' AND p.products_display='1')) ";

    $items_count_select_sql = "	SELECT COUNT(p.products_id) AS total
    							FROM " . TABLE_PRODUCTS . " AS p
    							INNER JOIN " . TABLE_PRODUCTS_TO_CATEGORIES . " AS p2c
    								ON p.products_id=p2c.products_id
    							INNER JOIN " . TABLE_CATEGORIES . " AS c
    								ON p2c.categories_id=c.categories_id
								INNER JOIN " . TABLE_CATEGORIES_GROUPS . " AS cg
									ON c.categories_id = cg.categories_id
    							WHERE p2c.categories_id='" . (int) $category_id . "' AND c.categories_status=1
    								AND ((cg.groups_id = '" . $customers_groups_id . "') OR (cg.groups_id = '" . $const_alluseraccess . "')) AND " . $StatusWhereStr . " AND " . $DisplayWhereStr;
    $items_count_result_sql = tep_db_query($items_count_select_sql);
    $items_count_row = tep_db_fetch_array($items_count_result_sql);
    $products_count += $items_count_row['total'];

    $sub_categories_select_sql = "	SELECT c.categories_id
									FROM " . TABLE_CATEGORIES . " AS c
									INNER JOIN " . TABLE_CATEGORIES_GROUPS . " AS cg
										ON c.categories_id = cg.categories_id
									WHERE c.parent_id = '" . (int) $category_id . "' AND c.categories_status=1
										AND ((cg.groups_id = '" . $customers_groups_id . "') OR (cg.groups_id = '" . $const_alluseraccess . "'))";
    $sub_categories_result_sql = tep_db_query($sub_categories_select_sql);
    if (tep_db_num_rows($sub_categories_result_sql)) {
        while ($sub_categories_row = tep_db_fetch_array($sub_categories_result_sql)) {
            $products_count += tep_count_all_products_in_category($sub_categories_row['categories_id'], $cat_count_array, $include_inactive, $include_hidden);
        }
    }
    $cat_count_array[$category_id] = $products_count;
    return $products_count;
}

////
// Return true if the category has subcategories
// TABLES: categories
function tep_has_category_subcategories($category_id) {
    $child_category_query = tep_db_query("select count(*) as count from " . TABLE_CATEGORIES . " where parent_id = '" . (int) $category_id . "'");
    $child_category = tep_db_fetch_array($child_category_query);

    if ($child_category['count'] > 0) {
        return true;
    } else {
        return false;
    }
}

////
// Returns the address_format_id for the given country
// TABLES: countries;
function tep_get_address_format_id($country_id) {
    $address_format_query = tep_db_query("select address_format_id as format_id from " . TABLE_COUNTRIES . " where countries_id = '" . (int) $country_id . "'");
    if (tep_db_num_rows($address_format_query)) {
        $address_format = tep_db_fetch_array($address_format_query);
        return $address_format['format_id'];
    } else {
        return '1';
    }
}

////
// Return a formatted address
// TABLES: address_format
function tep_address_format($address_format_id, $address, $html, $boln, $eoln) {
    $address_format_query = tep_db_query("select address_format as format from " . TABLE_ADDRESS_FORMAT . " where address_format_id = '" . (int) $address_format_id . "'");
    $address_format = tep_db_fetch_array($address_format_query);

    $company = tep_output_string_protected($address['company']);
    if (isset($address['firstname']) && tep_not_null($address['firstname'])) {
        $firstname = tep_output_string_protected($address['firstname']);
        $lastname = tep_output_string_protected($address['lastname']);
    } elseif (isset($address['name']) && tep_not_null($address['name'])) {
        $firstname = tep_output_string_protected($address['name']);
        $lastname = '';
    } else {
        $firstname = '';
        $lastname = '';
    }
    $street = tep_output_string_protected($address['street_address']);
    $suburb = tep_output_string_protected($address['suburb']);
    $city = tep_output_string_protected($address['city']);
    $state = tep_output_string_protected($address['state']);
    if (isset($address['country_id']) && tep_not_null($address['country_id'])) {
        $country = tep_get_country_name($address['country_id']);

        if (isset($address['zone_id']) && tep_not_null($address['zone_id'])) {
            $state = tep_get_zone_code($address['country_id'], $address['zone_id'], $state);
        }
    } elseif (isset($address['country']) && tep_not_null($address['country'])) {
        $country = tep_output_string_protected($address['country']);
    } else {
        $country = '';
    }
    $postcode = tep_output_string_protected($address['postcode']);
    $zip = $postcode;

    if ($html) {
        // HTML Mode
        $HR = '<hr>';
        $hr = '<hr>';
        if (($boln == '') && ($eoln == "\n")) { // Values not specified, use rational defaults
            $CR = '<br>';
            $cr = '<br>';
            $eoln = $cr;
        } else { // Use values supplied
            $CR = $eoln . $boln;
            $cr = $CR;
        }
    } else {
        // Text Mode
        $CR = $eoln;
        $cr = $CR;
        $HR = '----------------------------------------';
        $hr = '----------------------------------------';
    }

    $statecomma = '';
    $streets = $street;
    if ($suburb != '')
        $streets = $street . $cr . $suburb;
    if ($country == '')
        $country = tep_output_string_protected($address['country']);
    if ($state != '')
        $statecomma = $state . ', ';

    $fmt = $address_format['format'];
    eval("\$address = \"$fmt\";");

    if ((ACCOUNT_COMPANY == 'true') && (tep_not_null($company))) {
        $address = $company . $cr . $address;
    }
    return $address;
}

////
// Return a formatted address
// TABLES: customers, address_book
function tep_address_label($customers_id, $address_id = 1, $html = false, $boln = '', $eoln = "\n") {
    $address_query = tep_db_query("select entry_firstname as firstname, entry_lastname as lastname, entry_company as company, entry_street_address as street_address, entry_suburb as suburb, entry_city as city, entry_postcode as postcode, entry_state as state, entry_zone_id as zone_id, entry_country_id as country_id from " . TABLE_ADDRESS_BOOK . " where customers_id = '" . (int) $customers_id . "' and address_book_id = '" . (int) $address_id . "'");
    $address = tep_db_fetch_array($address_query);

    $format_id = tep_get_address_format_id($address['country_id']);

    return tep_address_format($format_id, $address, $html, $boln, $eoln);
}

function tep_row_number_format($number) {
    if (($number < 10) && (substr($number, 0, 1) != '0'))
        $number = '0' . $number;

    return $number;
}

function tep_get_categories_name($categories_id, $language_id = 0) {
    global $default_languages_id, $languages_id, $memcache_obj;

    if ($language_id == 0)
        $language_id = $languages_id;

    $cache_key = TABLE_CATEGORIES_DESCRIPTION . '/categories_id/' . (int) $categories_id . '/language/' . $language_id . '/categories_name';
    $cache_result = $memcache_obj->fetch($cache_key);

    if ($cache_result !== FALSE) {
        $categories_name = $cache_result;
    } else {
        $categories_name_select_sql = "	SELECT categories_name
										FROM " . TABLE_CATEGORIES_DESCRIPTION . "
										WHERE categories_id = '" . (int) $categories_id . "'
											AND categories_name <> ''
											AND (IF (language_id = '" . (int) $language_id . "', 1, IF(( SELECT COUNT(categories_id) > 0
																										FROM " . TABLE_CATEGORIES_DESCRIPTION . "
																										WHERE categories_id = '" . (int) $categories_id . "'
																											AND language_id = '" . (int) $language_id . "'
																											AND categories_name <> ''), 0, language_id = '" . (int) $default_languages_id . "')))";
        $categories_name_result_sql = tep_db_query($categories_name_select_sql);
        $categories_name_row = tep_db_fetch_array($categories_name_result_sql);
        $categories_name = $categories_name_row['categories_name'];

        $memcache_obj->store($cache_key, $categories_name, 86400); // Cache for 1 day
    }

    return $categories_name;
}

function tep_get_categories_pin_yin($categories_id, $language_id = 0) {
    global $default_languages_id, $languages_id, $memcache_obj;

    if ($language_id == 0)
        $language_id = $languages_id;

    $cache_key = TABLE_CATEGORIES_DESCRIPTION . '/categories_id/' . (int) $categories_id . '/language/' . $language_id . '/categories_pin_yin';
    $cache_result = $memcache_obj->fetch($cache_key);

    if ($cache_result !== FALSE) {
        $categories_pin_yin = $cache_result;
    } else {
        $categories_pin_yin_select_sql = "	SELECT categories_pin_yin
											FROM " . TABLE_CATEGORIES_DESCRIPTION . "
											WHERE categories_id = '" . (int) $categories_id . "'
												AND categories_pin_yin <> ''
												AND (IF (language_id = '" . (int) $language_id . "', 1, IF(( SELECT COUNT(categories_id) > 0
																											FROM " . TABLE_CATEGORIES_DESCRIPTION . "
																											WHERE categories_id = '" . (int) $categories_id . "'
																												AND language_id = '" . (int) $language_id . "'
																												AND categories_pin_yin <> ''), 0, language_id = '" . (int) $default_languages_id . "')))";
        $categories_pin_yin_result_sql = tep_db_query($categories_pin_yin_select_sql);
        $categories_pin_yin_row = tep_db_fetch_array($categories_pin_yin_result_sql);
        $categories_pin_yin = $categories_pin_yin_row['categories_pin_yin'];

        $memcache_obj->store($cache_key, $categories_pin_yin, 86400); // Cache for 1 day
    }

    return $categories_pin_yin;
}

function tep_get_categories_heading_title($categories_id, $language_id = 0) {
    global $default_languages_id, $languages_id, $memcache_obj;

    if ($language_id == 0)
        $language_id = $languages_id;
    $heading_title = '';

    $cache_key = TABLE_CATEGORIES_DESCRIPTION . '/categories_id/' . (int) $categories_id . '/language/' . $language_id . '/categories_heading_title';

    $cache_result = $memcache_obj->fetch($cache_key);

    if ($cache_result !== FALSE) {
        $heading_title = $cache_result;
    } else {
        $categories_heading_title_select_sql = " SELECT categories_heading_title
												 FROM " . TABLE_CATEGORIES_DESCRIPTION . "
												 WHERE categories_id = '" . (int) $categories_id . "'
													 AND categories_heading_title <> ''
													 AND (IF (language_id = '" . (int) $language_id . "', 1, IF(( SELECT COUNT(categories_id) > 0
																											FROM " . TABLE_CATEGORIES_DESCRIPTION . "
																											WHERE categories_id = '" . (int) $categories_id . "'
																												AND language_id = '" . (int) $language_id . "'
																												AND categories_heading_title <> ''), 0, language_id = '" . (int) $default_languages_id . "')))";
        $categories_heading_title_result_sql = tep_db_query($categories_heading_title_select_sql);
        $categories_heading_title_row = tep_db_fetch_array($categories_heading_title_result_sql);
        $heading_title = $categories_heading_title_row['categories_heading_title'];

        $memcache_obj->store($cache_key, $heading_title, 7200);
    }

    return $heading_title;
}

function tep_get_categories_parent_path($categories_id) {
    $categories_path_select_sql = "	SELECT categories_parent_path
									FROM " . TABLE_CATEGORIES . "
									WHERE categories_id = '" . (int) $categories_id . "'";
    $categories_path_result_sql = tep_db_query($categories_path_select_sql);
    $categories_path_row = tep_db_fetch_array($categories_path_result_sql);
    return $categories_path_row['categories_parent_path'];
}

function tep_get_categories_description($categories_id, $language_id = 0) {
    global $default_languages_id, $languages_id;

    if ($language_id == 0)
        $language_id = $languages_id;

    $categories_description_select_sql = "	SELECT categories_description
											FROM " . TABLE_CATEGORIES_DESCRIPTION . "
											WHERE categories_id = '" . (int) $categories_id . "'
												AND categories_description <> ''
												AND (IF (language_id = '" . (int) $language_id . "', 1, IF(( SELECT COUNT(categories_id) > 0
																											FROM " . TABLE_CATEGORIES_DESCRIPTION . "
																											WHERE categories_id = '" . (int) $categories_id . "'
																												AND categories_description <> ''
																												AND language_id = '" . (int) $language_id . "'), 0, language_id = '" . (int) $default_languages_id . "')))";
    $categories_description_result_sql = tep_db_query($categories_description_select_sql);
    $categories_description_row = tep_db_fetch_array($categories_description_result_sql);

    return $categories_description_row['categories_description'];
}

function tep_get_categories($categories_array = '', $parent_id = '0', $indent = '') {
    global $default_languages_id, $languages_id;

    if (!is_array($categories_array))
        $categories_array = array();

    $categories_query = tep_db_query("select c.categories_id, cd.categories_name from " . TABLE_CATEGORIES . " c, " . TABLE_CATEGORIES_DESCRIPTION . " cd
										where parent_id = '" . (int) $parent_id . "'
											and c.categories_id = cd.categories_id
											and cd.categories_name <> ''
											and (IF(cd.language_id='" . tep_db_input($languages_id) . "', 1, cd.language_id='" . tep_db_input($default_languages_id) . "'))
										order by sort_order, cd.categories_name
									");
    while ($categories = tep_db_fetch_array($categories_query)) {
        $categories_array[] = array('id' => $categories['categories_id'],
            'text' => $indent . $categories['categories_name']);

        if ($categories['categories_id'] != $parent_id) {
            $categories_array = tep_get_categories($categories_array, $categories['categories_id'], $indent . '__');
        }
    }

    return $categories_array;
}

function tep_custom_get_categories($categories_array = '', $parent_id = '0', $indent = '') {
    global $deafult_languages_id, $languages_id;
    global $customers_groups_id;

    if (!is_array($categories_array))
        $categories_array = array();

    $initial_query = tep_db_query("select categories_name from " . TABLE_CATEGORIES_DESCRIPTION . " where categories_id=" . (int) $parent_id . ";");
    while ($initial_row = tep_db_fetch_array($initial_query)) {
        $categories_array[] = array('id' => $parent_id,
            'text' => $initial_row['categories_name']);
    }

    $top_categories_select_sql = "	SELECT c.categories_id, cd.categories_name
									FROM " . TABLE_CATEGORIES . " c, " . TABLE_CATEGORIES_DESCRIPTION . " cd, " . TABLE_CATEGORIES_GROUPS . " AS cg
									WHERE parent_id = '" . (int) $parent_id . "'
										AND c.categories_status='1'
										AND c.categories_id = cd.categories_id
										AND cd.categories_name <> ''
										AND (IF(cd.language_id='" . tep_db_input($languages_id) . "', 1, cd.language_id='" . tep_db_input($default_languages_id) . "'))
										AND c.categories_id = cg.categories_id
										AND ((cg.groups_id = '" . $customers_groups_id . "') OR (cg.groups_id = 0))
									ORDER BY sort_order, cd.categories_name";
    $categories_query = tep_db_query($top_categories_select_sql);
    while ($categories = tep_db_fetch_array($categories_query)) {
        $categories_array[] = array('id' => $categories['categories_id'],
            'text' => $indent . strip_tags($categories['categories_name']));
    }

    return $categories_array;
}

function tep_get_categories_no_child($categories_array = '', $parent_id = '0', $indent = '', $cat_id = '') {
    global $default_languages_id, $languages_id;

    if (!is_array($categories_array))
        $categories_array = array();

    if ($cat_id) {
        $categories_query = tep_db_query("	select c.categories_id, cd.categories_name from " . TABLE_CATEGORIES . " c, " . TABLE_CATEGORIES_DESCRIPTION . " cd
											where parent_id = '" . (int) $parent_id . "'
												and c.categories_id = cd.categories_id
												and c.categories_id='" . $cat_id . "'
												and cd.categories_name <> ''
												and (IF(cd.language_id='" . tep_db_input($languages_id) . "', 1, cd.language_id='" . tep_db_input($default_languages_id) . "'))
											order by sort_order, cd.categories_name");
    } else {
        $categories_query = tep_db_query("select c.categories_id, cd.categories_name from " . TABLE_CATEGORIES . " c, " . TABLE_CATEGORIES_DESCRIPTION . " cd
											where parent_id = '" . (int) $parent_id . "'
												and c.categories_id = cd.categories_id
												and cd.categories_name <> ''
												and (IF(cd.language_id='" . tep_db_input($languages_id) . "', 1, cd.language_id='" . tep_db_input($default_languages_id) . "'))
											order by sort_order, cd.categories_name");
    }

    while ($categories = tep_db_fetch_array($categories_query)) {
        $categories_array[] = array('id' => $categories['categories_id'],
            'text' => $indent . $categories['categories_name']);
        if ($categories['categories_id'] != $parent_id) {
            $categories_array = tep_get_categories($categories_array, $categories['categories_id'], $indent . '&nbsp;&nbsp;');
        }
    }

    return $categories_array;
}

function tep_get_manufacturers($manufacturers_array = '') {
    if (!is_array($manufacturers_array))
        $manufacturers_array = array();

    $manufacturers_query = tep_db_query("select manufacturers_id, manufacturers_name from " . TABLE_MANUFACTURERS . " order by manufacturers_name");
    while ($manufacturers = tep_db_fetch_array($manufacturers_query)) {
        $manufacturers_array[] = array('id' => $manufacturers['manufacturers_id'], 'text' => $manufacturers['manufacturers_name']);
    }

    return $manufacturers_array;
}

////
// Return all subcategory IDs
// TABLES: categories
function tep_get_subcategories(&$subcategories_array, $parent_id = 0, $status = '') {

    $parent_path_str = tep_get_categories_parent_path((int) $parent_id);
    if (tep_not_null($parent_path_str)) {
        $parent_path_str = $parent_path_str . (int) $parent_id . '_';
    } else {
        $parent_path_str = '_' . (int) $parent_id . '_';
    }
    $parent_path_str = preg_replace("/_/u", "\_", $parent_path_str);

    $sub_categories_select_sql = "	SELECT categories_id
									FROM " . TABLE_CATEGORIES . "
									WHERE " . ($parent_id > 0 ? " categories_parent_path LIKE '" . $parent_path_str . "%'" : '1');
    if (tep_not_null($status)) {
        $sub_categories_select_sql .= " AND categories_status = '" . (int) $status . "'";
    }

    $sub_categories_result_sql = tep_db_query($sub_categories_select_sql);
    while ($sub_categories_row = tep_db_fetch_array($sub_categories_result_sql)) {
        $subcategories_array[sizeof($subcategories_array)] = $sub_categories_row['categories_id'];
    }
}

function tep_get_vip_categories(&$active_game_array) {
    $game_array = tep_get_game_list_arr();
    for ($game_cnt = 0; $game_cnt < count($game_array); $game_cnt++) {
        $check_vip_mode_sql = "SELECT buyback_setting_key, buyback_setting_value
								FROM " . TABLE_BUYBACK_SETTING . "
								WHERE buyback_setting_key='vip_trade_mode_option'
									AND buyback_setting_table_name='buyback_categories'
									AND buyback_setting_reference_id='" . tep_db_input($game_array[$game_cnt]['id']) . "'";
        $check_vip_mode_result = tep_db_query($check_vip_mode_sql);
        if ($check_vip_mode_row = tep_db_fetch_array($check_vip_mode_result)) {
            $active_game_array[] = array('id' => $game_array[$game_cnt]['id'],
                'text' => $game_array[$game_cnt]['text']
            );
        }
    }
}

// Output a raw date string in the selected locale date format
// $raw_date needs to be in this format: YYYY-MM-DD HH:MM:SS
function tep_date_long($raw_date = '', $date_format = DATE_FORMAT_LONG) {
    if (($raw_date == '0000-00-00 00:00:00') || ($raw_date == ''))
        return false;

    $year = (int) substr($raw_date, 0, 4);
    $month = (int) substr($raw_date, 5, 2);
    $day = (int) substr($raw_date, 8, 2);
    $hour = (int) substr($raw_date, 11, 2);
    $minute = (int) substr($raw_date, 14, 2);
    $second = (int) substr($raw_date, 17, 2);

    return strftime($date_format, mktime($hour, $minute, $second, $month, $day, $year));
}

////
// Output a raw date string in the selected locale date format
// $raw_date needs to be in this format: YYYY-MM-DD HH:MM:SS
// NOTE: Includes a workaround for dates before 01/01/1970 that fail on windows servers
function tep_date_short($raw_date) {
    if (($raw_date == '0000-00-00 00:00:00') || empty($raw_date))
        return false;

    $year = substr($raw_date, 0, 4);
    $month = (int) substr($raw_date, 5, 2);
    $day = (int) substr($raw_date, 8, 2);
    $hour = (int) substr($raw_date, 11, 2);
    $minute = (int) substr($raw_date, 14, 2);
    $second = (int) substr($raw_date, 17, 2);

    if (@date('Y', mktime($hour, $minute, $second, $month, $day, $year)) == $year) {
        return date(DATE_FORMAT, mktime($hour, $minute, $second, $month, $day, $year));
    } else {
        return ereg_replace_dep('2037' . '$', $year, date(DATE_FORMAT, mktime($hour, $minute, $second, $month, $day, 2037)));
    }
}

function tep_datetime_short($raw_datetime, $format = DATE_TIME_FORMAT) {
    if (($raw_datetime == '0000-00-00 00:00:00') || ($raw_datetime == ''))
        return false;

    $year = (int) substr($raw_datetime, 0, 4);
    $month = (int) substr($raw_datetime, 5, 2);
    $day = (int) substr($raw_datetime, 8, 2);
    $hour = (int) substr($raw_datetime, 11, 2);
    $minute = (int) substr($raw_datetime, 14, 2);
    $second = (int) substr($raw_datetime, 17, 2);

    return strftime($format, mktime($hour, $minute, $second, $month, $day, $year));
}

function tep_date_ctrl($ori_date, $operator, $type, $number) {
    $hour_figure = $min_figure = $sec_figure = $month_figure = $day_figure = $year_figure = 0;
    $hour = $min = $sec = $month = $day = $year = 0;

    switch ($type) {
        case 'hour':
            $hour_figure = $number;
            break;
        case 'min':
            $min_figure = $number;
            break;
        case 'sec':
            $sec_figure = $number;
            break;
        case 'month':
            $month_figure = $number;
            break;
        case 'day':
            $day_figure = $number;
            break;
        case 'year':
            $year_figure = $number;
            break;
    }

    list($date, $time) = split_dep(" ", $ori_date);

    $date_arr = split_dep("-", $date);
    $time_arr = split_dep(":", $time);

    $year = $date_arr[0];
    $month = $date_arr[1];
    $day = $date_arr[2];

    $hour = $time_arr[0];
    $min = $time_arr[1];
    $sec = $time_arr[2];

    if ($operator == '+') {
        $hour += $hour_figure;
        $min += $min_figure;
        $sec += $sec_figure;
        $month += $month_figure;
        $day += $day_figure;
        $year += $year_figure;
    } else if ($operator == '-') {
        $hour -= $hour_figure;
        $min -= $min_figure;
        $sec -= $sec_figure;
        $month -= $month_figure;
        $day -= $day_figure;
        $year -= $year_figure;
    } else {
        return 'Error: Operator is not set.';
    }

    $result = date("Y-m-d H:i:s", mktime($hour, $min, $sec, $month, $day, $year));

    return $result;
}

////
// Parse search string into indivual objects
function tep_parse_search_string($search_str = '', &$objects) {
    $search_str = trim(strtolower($search_str));

    // Break up $search_str on whitespace; quoted string will be reconstructed later
    $pieces = split_dep('[[:space:]]+', $search_str);
    $objects = array();
    $tmpstring = '';
    $flag = '';

    for ($k = 0; $k < count($pieces); $k++) {
        /* while (substr($pieces[$k], 0, 1) == '(') {
          $objects[] = '(';
          if (strlen($pieces[$k]) > 1) {
          $pieces[$k] = substr($pieces[$k], 1);
          } else {
          $pieces[$k] = '';
          }
          }

          $post_objects = array();
          while (substr($pieces[$k], -1) == ')')  {
          $post_objects[] = ')';
          if (strlen($pieces[$k]) > 1) {
          $pieces[$k] = substr($pieces[$k], 0, -1);
          } else {
          $pieces[$k] = '';
          }
          } */

        // Check individual words
        if ((substr($pieces[$k], -1) != '"') && (substr($pieces[$k], 0, 1) != '"')) {
            $objects[] = trim($pieces[$k]);
            for ($j = 0; $j < count($post_objects); $j++) {
                $objects[] = $post_objects[$j];
            }
        } else {
            /* 	This means that the $piece is either the beginning or the end of a string.
              So, we'll slurp up the $pieces and stick them together until we get to the
              end of the string or run out of pieces.
             */
            // Add this word to the $tmpstring, starting the $tmpstring
            $tmpstring = trim(ereg_replace_dep('"', ' ', $pieces[$k]));
            // Check for one possible exception to the rule. That there is a single quoted word.
            if (substr($pieces[$k], -1) == '"') {
                // Turn the flag off for future iterations
                $flag = 'off';
                $objects[] = trim($pieces[$k]);

                for ($j = 0; $j < count($post_objects); $j++) {
                    $objects[] = $post_objects[$j];
                }
                unset($tmpstring);
                // Stop looking for the end of the string and move onto the next word.
                continue;
            }
            // Otherwise, turn on the flag to indicate no quotes have been found attached to this word in the string.
            $flag = 'on';
            // Move on to the next word
            $k++;
            // Keep reading until the end of the string as long as the $flag is on
            while (($flag == 'on') && ($k < count($pieces))) {
                while (substr($pieces[$k], -1) == ')') {
                    $post_objects[] = ')';
                    if (strlen($pieces[$k]) > 1) {
                        $pieces[$k] = substr($pieces[$k], 0, -1);
                    } else {
                        $pieces[$k] = '';
                    }
                }

                // If the word doesn't end in double quotes, append it to the $tmpstring.
                if (substr($pieces[$k], -1) != '"') {
                    // Tack this word onto the current string entity
                    $tmpstring .= ' ' . $pieces[$k];
                    // Move on to the next word
                    $k++;
                    continue;
                } else {
                    /* If the $piece ends in double quotes, strip the double quotes, tack the
                      $piece onto the tail of the string, push the $tmpstring onto the $haves,
                      kill the $tmpstring, turn the $flag "off", and return.
                     */
                    $tmpstring .= ' ' . trim(ereg_replace_dep('"', ' ', $pieces[$k]));
                    // Push the $tmpstring onto the array of stuff to search for
                    $objects[] = trim($tmpstring);

                    for ($j = 0; $j < count($post_objects); $j++) {
                        $objects[] = $post_objects[$j];
                    }
                    unset($tmpstring);

                    // Turn off the flag to exit the loop
                    $flag = 'off';
                }
            }
        }
    }
    // add default logical operators if needed
    $temp = array();
    for ($i = 0; $i < (count($objects) - 1); $i++) {
        $temp[] = $objects[$i];
        if (($objects[$i] != 'and') &&
                ($objects[$i] != 'or') &&
                ($objects[$i] != '(') &&
                ($objects[$i + 1] != 'and') &&
                ($objects[$i + 1] != 'or') &&
                ($objects[$i + 1] != ')')) {
            $temp[] = ADVANCED_SEARCH_DEFAULT_OPERATOR;
        }
    }
    $temp[] = $objects[$i];
    $objects = $temp;

    $keyword_count = 0;
    $operator_count = 0;
    $balance = 0;
    for ($i = 0; $i < count($objects); $i++) {
        if ($objects[$i] == '(')
            $balance--;
        if ($objects[$i] == ')')
            $balance++;
        if (($objects[$i] == 'and') || ($objects[$i] == 'or')) {
            $operator_count++;
        } elseif (($objects[$i]) && ($objects[$i] != '(') && ($objects[$i] != ')')) {
            $keyword_count++;
        }
    }

    if (($operator_count < $keyword_count) && ($balance == 0)) {
        return true;
    } else {
        return false;
    }
}

////
// Check date
function tep_checkdate($date_to_check, $format_string, &$date_array) {
    $separator_idx = -1;

    $separators = array('-', ' ', '/', '.');
    $month_abbr = array('jan', 'feb', 'mar', 'apr', 'may', 'jun', 'jul', 'aug', 'sep', 'oct', 'nov', 'dec');
    $no_of_days = array(31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31);

    $format_string = strtolower($format_string);

    if (strlen($date_to_check) != strlen($format_string)) {
        return false;
    }

    $size = sizeof($separators);
    for ($i = 0; $i < $size; $i++) {
        $pos_separator = strpos($date_to_check, $separators[$i]);
        if ($pos_separator != false) {
            $date_separator_idx = $i;
            break;
        }
    }

    for ($i = 0; $i < $size; $i++) {
        $pos_separator = strpos($format_string, $separators[$i]);
        if ($pos_separator != false) {
            $format_separator_idx = $i;
            break;
        }
    }

    if ($date_separator_idx != $format_separator_idx) {
        return false;
    }

    if ($date_separator_idx != -1) {
        $format_string_array = explode($separators[$date_separator_idx], $format_string);
        if (sizeof($format_string_array) != 3) {
            return false;
        }

        $date_to_check_array = explode($separators[$date_separator_idx], $date_to_check);
        if (sizeof($date_to_check_array) != 3) {
            return false;
        }

        $size = sizeof($format_string_array);
        for ($i = 0; $i < $size; $i++) {
            if ($format_string_array[$i] == 'mm' || $format_string_array[$i] == 'mmm')
                $month = $date_to_check_array[$i];
            if ($format_string_array[$i] == 'dd')
                $day = $date_to_check_array[$i];
            if (($format_string_array[$i] == 'yyyy') || ($format_string_array[$i] == 'aaaa'))
                $year = $date_to_check_array[$i];
        }
    } else {
        if (strlen($format_string) == 8 || strlen($format_string) == 9) {
            $pos_month = strpos($format_string, 'mmm');
            if ($pos_month != false) {
                $month = substr($date_to_check, $pos_month, 3);
                $size = sizeof($month_abbr);
                for ($i = 0; $i < $size; $i++) {
                    if ($month == $month_abbr[$i]) {
                        $month = $i;
                        break;
                    }
                }
            } else {
                $month = substr($date_to_check, strpos($format_string, 'mm'), 2);
            }
        } else {
            return false;
        }

        $day = substr($date_to_check, strpos($format_string, 'dd'), 2);
        $year = substr($date_to_check, strpos($format_string, 'yyyy'), 4);
    }

    if (strlen($year) != 4) {
        return false;
    }

    if (!settype($year, 'integer') || !settype($month, 'integer') || !settype($day, 'integer')) {
        return false;
    }

    if ($month > 12 || $month < 1) {
        return false;
    }

    if ($day < 1) {
        return false;
    }

    if (tep_is_leap_year($year)) {
        $no_of_days[1] = 29;
    }

    if ($day > $no_of_days[$month - 1]) {
        return false;
    }

    $date_array = array($year, $month, $day);

    return true;
}

////
// Check if year is a leap year
function tep_is_leap_year($year) {
    if ($year % 100 == 0) {
        if ($year % 400 == 0)
            return true;
    } else {
        if (($year % 4) == 0)
            return true;
    }

    return false;
}

////
// Return table heading with sorting capabilities
function tep_create_sort_heading($sortby, $colnum, $heading, $CustomStyle = '') {
    global $PHP_SELF;

    $sort_prefix = '';
    $sort_suffix = '';

    if ($sortby) {
        $sort_prefix = '<a href="' . tep_href_link(basename($PHP_SELF), tep_get_all_get_params(array('page', 'info', 'sort')) . 'page=1&sort=' . $colnum . ($sortby == $colnum . 'a' ? 'd' : 'a')) . '" title="' . tep_output_string(TEXT_SORT_PRODUCTS . ($sortby == $colnum . 'd' || substr($sortby, 0, 1) != $colnum ? TEXT_ASCENDINGLY : TEXT_DESCENDINGLY) . TEXT_BY . $heading) . ($CustomStyle ? '"' . $CustomStyle . "'" : '" class="productBoxHeading"') . '>';
        //$sort_suffix = (substr($sortby, 0, 1) == $colnum ? (substr($sortby, 1, 1) == 'a' ? '+' : '-') : '') . '</a>';
        $sort_suffix = (substr($sortby, 0, 1) == $colnum ? (substr($sortby, 1, 1) == 'a' ? '' : '') : '') . '</a>';
    }

    return $sort_prefix . $heading . $sort_suffix;
}

function tep_get_game_list_arr($selection_array = '') {
    global $memcache_obj, $languages_id;

    if (!is_object($memcache_obj)) {
        require_once(DIR_WS_CLASSES . 'cache_abstract.php');
        require_once(DIR_WS_CLASSES . 'memcache.php');
        $memcache_obj = new OGM_Cache_MemCache();
    }

    $game_list_array = array();

    $cache_key = TABLE_CATEGORIES . '/categories_buyback_main_cat/1/language/' . $languages_id;

    $cache_result = $memcache_obj->fetch($cache_key);

    if ($cache_result !== FALSE) {
        $game_list_array = $cache_result;
    } else {
        require_once(DIR_WS_CLASSES . 'category.php');

        if (!is_array($selection_array)) {
            $game_list_array = array(array('id' => 0, 'text' => PULL_DOWN_DEFAULT)); //TEXT_SELECT_YOUR_GAME
        } else {
            $game_list_array = $selection_array;
        }
        $main_cat_select_sql = "SELECT c.categories_id
	                            FROM " . TABLE_CATEGORIES . " AS c
	                            WHERE c.categories_buyback_main_cat = 1
	                            ORDER BY c.sort_order";
        $main_cat_result_sql = tep_db_query($main_cat_select_sql);
        while ($main_cat_row = tep_db_fetch_array($main_cat_result_sql)) {
            $cat_obj = new category($main_cat_row['categories_id']);
            if ($cat_obj->category_has_product_type(0, 1)) {
                $game_list_array[] = array('id' => $main_cat_row['categories_id'],
                    'text' => strip_tags(tep_get_categories_name($main_cat_row['categories_id'])));
            }
            unset($cat_obj);
        }

        $memcache_obj->store($cache_key, $game_list_array, 10800);
    }

    return $game_list_array;
}

////
// Recursively go through the categories and retreive all parent categories IDs
// TABLES: categories
function tep_get_parent_categories(&$categories, $categories_id) {
    $parent_categories_query = tep_db_query("select parent_id from " . TABLE_CATEGORIES . " where categories_id = '" . (int) $categories_id . "'");
    while ($parent_categories = tep_db_fetch_array($parent_categories_query)) {
        if ($parent_categories['parent_id'] == 0)
            return true;
        $categories[sizeof($categories)] = $parent_categories['parent_id'];
        if ($parent_categories['parent_id'] != $categories_id) {
            tep_get_parent_categories($categories, $parent_categories['parent_id']);
        }
    }
}

////
// Construct a category path to the product
// TABLES: products_to_categories
function tep_get_product_path($products_id, $show_all = false) {
    $cPath = '';

    if ($show_all)
        $category_query = tep_db_query("select p2c.categories_id from " . TABLE_PRODUCTS . " p, " . TABLE_PRODUCTS_TO_CATEGORIES . " p2c where p.products_id = '" . (int) $products_id . "' and p.products_id = p2c.products_id limit 1");
    else
        $category_query = tep_db_query("select p2c.categories_id from " . TABLE_PRODUCTS . " p, " . TABLE_PRODUCTS_TO_CATEGORIES . " p2c where p.products_id = '" . (int) $products_id . "' and p.products_status = '1' and p.products_id = p2c.products_id limit 1");

    if (tep_db_num_rows($category_query)) {
        $category = tep_db_fetch_array($category_query);

        $categories = array();
        tep_get_parent_categories($categories, $category['categories_id']);

        $categories = array_reverse($categories);

        $cPath = implode('_', $categories);

        if (tep_not_null($cPath))
            $cPath .= '_';
        $cPath .= $category['categories_id'];
    }
    return $cPath;
}

function tep_get_product_path_name($id) {
    $result = tep_db_query("SELECT p.products_cat_path, pd.products_name from " . TABLE_PRODUCTS . " p, " . TABLE_PRODUCTS_DESCRIPTION . " pd where pd.products_id=p.products_id and p.products_id=$id;");

    if ($row = tep_db_fetch_array($result)) {
        return $row['products_cat_path'] . " > " . $row['products_name'];
    } else {
        return "";
    }
}

////
// Return a product ID with attributes
function tep_get_uprid($prid, $params) {
    $uprid = $prid;
    if ((is_array($params)) && (!strstr($prid, '{'))) {
        while (list($option, $value) = each($params)) {
            $uprid = $uprid . '{' . $option . '}' . $value;
        }
    }

    return $uprid;
}

////
// Return a product ID from a product ID with attributes
function tep_get_prid($uprid) {
    $pieces = explode('{', $uprid);

    return $pieces[0];
}

function tep_get_orders_status_name($orders_status_id, $language_id = '') {
    global $default_languages_id;

    if (!$language_id)
        $language_id = $languages_id;
    $orders_status_query = tep_db_query("select orders_status_name from " . TABLE_ORDERS_STATUS . " where orders_status_id = '" . (int) $orders_status_id . "' and language_id = '" . (int) $default_languages_id . "'");
    $orders_status = tep_db_fetch_array($orders_status_query);

    return $orders_status['orders_status_name'];
}

function tep_get_orders_status() {
    global $languages_id;

    $orders_status_array = array();
    $orders_status_query = tep_db_query("select orders_status_id, orders_status_name from " . TABLE_ORDERS_STATUS . " where language_id = '" . (int) $languages_id . "' order by orders_status_sort_order");
    while ($orders_status = tep_db_fetch_array($orders_status_query)) {
        $orders_status_array[] = array('id' => $orders_status['orders_status_id'],
            'text' => $orders_status['orders_status_name']);
    }

    return $orders_status_array;
}

////
// Return a customer greeting
function tep_customer_greeting() {
    global $customer_id, $customer_first_name;

    if (tep_session_is_registered('customer_first_name') && tep_session_is_registered('customer_id')) {
        $greeting_string = sprintf(TEXT_GREETING_PERSONAL, tep_output_string_protected($customer_first_name), tep_href_link(FILENAME_PRODUCTS_NEW));
    } else {
        $greeting_string = sprintf(TEXT_GREETING_GUEST, tep_href_link(FILENAME_LOGIN, '', 'SSL'), tep_href_link(FILENAME_CREATE_ACCOUNT, '', 'SSL'));
    }

    return $greeting_string;
}

////
//! Send email (text/html) using MIME
// This is the central mail function. The SMTP Server should be configured
// correct in php.ini
// Parameters:
// $to_name           The name of the recipient, e.g. "Jan Wildeboer"
// $to_email_address  The eMail address of the recipient,
//                    e.g. <EMAIL>
// $email_subject     The subject of the eMail
// $email_text        The text of the eMail, may contain HTML entities
// $from_email_name   The name of the sender, e.g. Shop Administration
// $from_email_adress The eMail address of the sender,
//                    e.g. <EMAIL>

function tep_mail($to_name, $to_email_address, $email_subject, $email_text, $from_email_name, $from_email_address, $bcc_email_address = '') {
    if (SEND_EMAILS != 'true')
        return false;

    $sent_status = false;
    $extra_header = '';

    $letter = array();
    $letter['message']['subject'] = $email_subject;
    $letter['message']['body'] = $email_text;
    $letter['envelope']['to'] = array('name' => $to_name, 'address' => $to_email_address);
    $letter['envelope']['from'] = array('name' => $from_email_name, 'address' => $from_email_address);
    $letter['envelope']['bcc'] = $bcc_email_address;

    $aws_obj = new ogm_amazon_ws();

    if ($aws_obj->send_mail_by_ses_controller($letter)) {
        $sent_status = $aws_obj->send_mail_by_ses($letter);
    }

    if (!$sent_status) {
        // Instantiate a new mail object
        $message = new email(array('X-Mailer: php',
            'Reply-To: ' . $from_email_address
        ));

        // Build the text version
        $text = strip_tags($email_text);
        if (EMAIL_USE_HTML == 'true') {
            $message->add_html($email_text, $text);
        } else {
            $message->add_text($text);
        }

        if ($bcc_email_address)
            $extra_header .= "Bcc: $bcc_email_address\r\n";
        // Send message
        $message->build_message();
        $message->send($to_name, $to_email_address, $from_email_name, $from_email_address, $email_subject, $extra_header);
    }

    unset($aws_obj, $letter);
}

// Check if product has attributes
function tep_has_product_attributes($products_id) {
    $attributes_query = tep_db_query("select count(*) as count from " . TABLE_PRODUCTS_ATTRIBUTES . " where products_id = '" . (int) $products_id . "'");
    $attributes = tep_db_fetch_array($attributes_query);

    if ($attributes['count'] > 0) {
        return true;
    } else {
        return false;
    }
}

////
// Get the number of times a word/character is present in a string
function tep_word_count($string, $needle) {
    $temp_array = split_dep($needle, $string);
    return sizeof($temp_array);
}

function tep_count_modules($modules = '') {
    $count = 0;

    if (empty($modules))
        return $count;

    $modules_array = split_dep(';', $modules);

    for ($i = 0, $n = sizeof($modules_array); $i < $n; $i++) {
        $class = substr($modules_array[$i], 0, strrpos($modules_array[$i], '.'));

        if (is_object($GLOBALS[$class])) {
            if ($GLOBALS[$class]->enabled) {
                $count++;
            }
        }
    }

    return $count;
}

function tep_count_payment_modules() {
    return tep_count_modules(MODULE_PAYMENT_INSTALLED);
}

function tep_count_shipping_modules() {
    return tep_count_modules(MODULE_SHIPPING_INSTALLED);
}

function tep_create_random_value($length, $type = 'mixed') {
    if (($type != 'mixed') && ($type != 'chars') && ($type != 'digits'))
        return false;

    $rand_value = '';
    while (strlen($rand_value) < $length) {
        if ($type == 'digits') {
            $char = tep_rand(0, 9);
        } else {
            $char = chr(tep_rand(0, 255));
        }
        if ($type == 'mixed') {
            if (eregi_dep('^[a-z0-9]$', $char))
                $rand_value .= $char;
        } elseif ($type == 'chars') {
            if (eregi_dep('^[a-z]$', $char))
                $rand_value .= $char;
        } elseif ($type == 'digits') {
            if (ereg_dep('^[0-9]$', $char))
                $rand_value .= $char;
        }
    }

    return $rand_value;
}

function tep_array_to_string($array, $exclude = '', $equals = '=', $separator = '&') {
    if (!is_array($exclude))
        $exclude = array();

    $get_string = '';
    if (sizeof($array) > 0) {
        while (list($key, $value) = each($array)) {
            if ((!in_array($key, $exclude)) && ($key != 'x') && ($key != 'y')) {
                $get_string .= $key . $equals . $value . $separator;
            }
        }
        $remove_chars = strlen($separator);
        $get_string = substr($get_string, 0, -$remove_chars);
    }

    return $get_string;
}

function tep_not_null($value) {
    if (is_array($value)) {
        if (sizeof($value) > 0) {
            return true;
        } else {
            return false;
        }
    } else {
        if (($value != '') && (strtolower($value) != 'null') && (strlen(trim($value)) > 0)) {
            return true;
        } else {
            return false;
        }
    }
}

function tep_not_empty($value) {
    if (is_array($value)) {
        if (sizeof($value) > 0) {
            return true;
        } else {
            return false;
        }
    } else {
        if (($value !== '') && (strtolower($value) != 'null') && (strlen(trim($value)) > 0)) {
            return true;
        } else {
            return false;
        }
    }
}

function tep_addslashes_input(&$value) {
    $value = tep_db_input($value);
}

function tep_utf8_to_unicode($str) {
    $unicode = array();
    $values = array();
    $lookingFor = 1;

    for ($i = 0; $i < strlen($str); $i++) {
        $thisValue = ord($str[$i]);
        if ($thisValue < 128)
            $unicode[] = $thisValue;
        else {
            if (count($values) == 0)
                $lookingFor = ( $thisValue < 224 ) ? 2 : 3;
            $values[] = $thisValue;
            if (count($values) == $lookingFor) {
                $number = ( $lookingFor == 3 ) ?
                        ( ( $values[0] % 16 ) * 4096 ) + ( ( $values[1] % 64 ) * 64 ) + ( $values[2] % 64 ) :
                        ( ( $values[0] % 32 ) * 64 ) + ( $values[1] % 64 );

                $unicode[] = $number;
                $values = array();
                $lookingFor = 1;
            } // if
        } // if
    } // for

    return $unicode;
}

// utf8_to_unicode

function tep_unicode_to_entities_preserving_ascii($unicode) {
    $entities = '';
    foreach ($unicode as $value) {
        $entities .= ( $value > 127 ) ? '&#' . $value . ';' : chr($value);
    } //foreach
    return $entities;
}

// unicode_to_entities_preserving_ascii
////
// Output the tax percentage with optional padded decimals
function tep_display_tax_value($value, $padding = TAX_DECIMAL_PLACES) {
    if (strpos($value, '.')) {
        $loop = true;
        while ($loop) {
            if (substr($value, -1) == '0') {
                $value = substr($value, 0, -1);
            } else {
                $loop = false;
                if (substr($value, -1) == '.') {
                    $value = substr($value, 0, -1);
                }
            }
        }
    }

    if ($padding > 0) {
        if ($decimal_pos = strpos($value, '.')) {
            $decimals = strlen(substr($value, ($decimal_pos + 1)));
            for ($i = $decimals; $i < $padding; $i++) {
                $value .= '0';
            }
        } else {
            $value .= '.';
            for ($i = 0; $i < $padding; $i++) {
                $value .= '0';
            }
        }
    }

    return $value;
}

////
// Checks to see if the currency code exists as a currency
// TABLES: currencies
function tep_currency_exists($code) {
    $code = tep_db_prepare_input($code);

    $currency_code = tep_db_query("	SELECT currencies_id
    								FROM " . TABLE_CURRENCIES . "
    								WHERE code = '" . tep_db_input($code) . "'");
    if (tep_db_num_rows($currency_code)) {
        return $code;
    } else {
        return false;
    }
}

////
// Checks to see if the country code exists as a country
// TABLES: countries
function tep_country_exists($pass_id) {
    $pass_id = (int) $pass_id;

    $check_country_select_sql = "	SELECT countries_id
									FROM " . TABLE_COUNTRIES . "
									WHERE countries_id = '" . $pass_id . "'";
    $check_country_result_sql = tep_db_query($check_country_select_sql);
    if (tep_db_num_rows($check_country_result_sql)) {
        return $pass_id;
    } else {
        return false;
    }
}

function tep_string_to_int($string) {
    return (int) $string;
}

////
// Parse and secure the cPath parameter values
function tep_parse_category_path($cPath) {
    // make sure the category IDs are integers
    $cPath_array = array_map('tep_string_to_int', explode('_', $cPath));

    // make sure no duplicate category IDs exist which could lock the server in a loop
    $tmp_array = array();
    $n = sizeof($cPath_array);
    for ($i = 0; $i < $n; $i++) {
        if (!in_array($cPath_array[$i], $tmp_array)) {
            $tmp_array[] = $cPath_array[$i];
        }
    }
    return $tmp_array;
}

////
// Return a random value
function tep_rand($min = null, $max = null) {
    static $seeded;

    if (!isset($seeded)) {
        mt_srand((double) microtime() * 1000000);
        $seeded = true;
    }

    if (isset($min) && isset($max)) {
        if ($min >= $max) {
            return $min;
        } else {
            return mt_rand($min, $max);
        }
    } else {
        return mt_rand();
    }
}

function tep_check_cat_permission($cPath = "", $group_id = 0) {

    $group_id = (int) $group_id;

    if (!$group_id)
        $group_id = 1;

    $cPath_arrays = explode("_", $cPath);
    $i = sizeof($cPath_arrays);

    if ($i > 0)
        $i--;

    $cat_group_select_sql = "select groups_id from categories_groups where categories_id='" . $cPath_arrays[$i] . "'";
    $cat_group_result_sql = tep_db_query($cat_group_select_sql);
    while ($cat_group_row_sql = tep_db_fetch_array($cat_group_result_sql)) {
        $original_group_id = $cat_group_row_sql['groups_id'];
        if ($original_group_id > 0) {
            if ($original_group_id == $group_id) {
                return true;
            }
        } else {
            return true;
        }
    }
    return false;
}

function tep_get_product_parent_id($id) {
    $query = tep_db_query("select categories_id from products_to_categories where products_id='" . $id . "';");
    $cat_id = '';

    while ($result = tep_db_fetch_array($query)) {
        $cat_id .= $result['categories_id'] . "_";
    }
    $cat_id = trim($cat_id, "_");
    return $cat_id;
}

function tep_setcookie($name, $value = '', $expire = 0, $path = '/', $domain = '', $secure = 0) {
    setcookie($name, $value, $expire, $path, (tep_not_null($domain) ? $domain : ''), $secure);
}

function tep_get_ip_address() {
    if (defined('WWW_USE_PROXY_SERVER') && WWW_USE_PROXY_SERVER == 'true') {
        if (isset($_SERVER)) {
            if (isset($_SERVER['HTTP_TRUE_CLIENT_IP'])) {
                $ip = $_SERVER['HTTP_TRUE_CLIENT_IP'];
            } else if (isset($_SERVER['HTTP_X_FORWARDED_FOR']) && tep_not_null($_SERVER['HTTP_X_FORWARDED_FOR'])) {
                $ip_array = explode(',', $_SERVER['HTTP_X_FORWARDED_FOR']);
                $ip = trim(end($ip_array));
            } else {
                $ip = $_SERVER['REMOTE_ADDR'];
            }
        } else {
            if (getenv('HTTP_TRUE_CLIENT_IP')) {
                $ip = getenv('HTTP_TRUE_CLIENT_IP');
            } else {
                $ip = getenv('REMOTE_ADDR');
            }
        }
    } else {
        if (getenv('HTTP_TRUE_CLIENT_IP')) {
            $ip = getenv('HTTP_TRUE_CLIENT_IP');
        } else {
            $ip = getenv('REMOTE_ADDR');
        }
    }
    return $ip;
}

function tep_count_customer_orders($id = '', $check_session = true) {
    global $customer_id;

    if (is_numeric($id) == false) {
        if (tep_session_is_registered('customer_id')) {
            $id = $customer_id;
        } else {
            return 0;
        }
    }

    if ($check_session == true) {
        if ((tep_session_is_registered('customer_id') == false) || ($id != $customer_id)) {
            return 0;
        }
    }

    $orders_check_query = tep_db_query("select count(*) as total from " . TABLE_ORDERS . " where customers_id = '" . (int) $id . "'");
    $orders_check = tep_db_fetch_array($orders_check_query);

    return $orders_check['total'];
}

function tep_count_customer_buyback_requests($id = '', $check_session = true) {
    global $customer_id;

    if (is_numeric($id) == false) {
        if (tep_session_is_registered('customer_id')) {
            $id = $customer_id;
        } else {
            return 0;
        }
    }

    if ($check_session == true) {
        if ((tep_session_is_registered('customer_id') == false) || ($id != $customer_id)) {
            return 0;
        }
    }

    $query = tep_db_query("select count(*) as total from " . TABLE_BUYBACK_REQUEST_GROUP . " where customers_id = '" . (int) $id . "'");
    $check = tep_db_fetch_array($query);

    return (int) $check['total'];
}

function tep_count_customer_address_book_entries($id = '', $check_session = true) {
    global $customer_id;

    if (is_numeric($id) == false) {
        if (tep_session_is_registered('customer_id')) {
            $id = $customer_id;
        } else {
            return 0;
        }
    }

    if ($check_session == true) {
        if ((tep_session_is_registered('customer_id') == false) || ($id != $customer_id)) {
            return 0;
        }
    }

    $addresses_query = tep_db_query("select count(*) as total from " . TABLE_ADDRESS_BOOK . " where customers_id = '" . (int) $id . "'");
    $addresses = tep_db_fetch_array($addresses_query);
    return $addresses['total'];
}

// nl2br() prior PHP 4.2.0 did not convert linefeeds on all OSs (it only converted \n)
function tep_convert_linefeeds($from, $to, $string) {
    if ((PHP_VERSION < "4.0.5") && is_array($from)) {
        return ereg_replace_dep('(' . implode('|', $from) . ')', $to, $string);
    } else {
        return str_replace($from, $to, $string);
    }
}

function tep_filtersqldata(&$data) {
    $data = mysql_escape_string($data);
}

function tep_clean_data($var, $filter = false) {
    if (!is_array($var)) {
        if (isset($var)) {
            if ($filter)
                filtersqldata($var);
            return $var;
        } else {
            return "";
        }
    } else {
        for ($i = 0; $i < sizeof($var); ++$i) {
            if (isset($var[$i])) {
                if ($filter)
                    filtersqldata($var[$i]);
            } else {
                $var[$i] = "";
            }
        }
        return $var;
    }
}

// Added by Subrat
function tep_set_buyback_cart($arr) {
    global $customer_id;
    // if the bugger is already logged in, add to database
//	if (((int)$login_id) > 0) {
    if (((int) $customer_id) > 0) {
        // delete all the cart for this bugger
        tep_db_query("DELETE FROM buyback_basket where customers_id ='" . (int) $_SESSION['customer_id'] . "' and buyback_basket_user_type='0';");

        // now insert his cart stuffs
        if (sizeof($arr) > 0) {
            foreach ($arr as $a) {
                tep_db_perform("buyback_basket", $a);
            }
        }
    } else { // otherwise serialize the array and add to session
        if (!tep_session_is_registered('buyback_cart'))
            tep_session_register("buyback_cart");

        if (sizeof($arr) > 0)
            $_SESSION['buyback_cart'] = tep_array_serialize($arr);
        else
            $_SESSION['buyback_cart'] = tep_array_serialize(array());
    }
}

function tep_clear_buyback_cart() {
    tep_db_query("DELETE FROM buyback_basket where customers_id ='" . (int) $_SESSION['customer_id'] . "'  and buyback_basket_user_type='0';");
}

function tep_get_buyback_cart($try_to_get_from_session_also = false) {
    if (tep_session_is_registered('customer_id')) {
        $count = 0;
        $result = tep_db_query("select * from buyback_basket where customers_id = '" . (int) $_SESSION['customer_id'] . "' AND buyback_basket_user_type='0';");

        while ($row = tep_db_fetch_array($result)) {
            $count++;
            $arr[(int) $row['products_id']] = $row;
        }

        if ($count == 0 && $try_to_get_from_session_also) {
            if (tep_session_is_registered('buyback_cart'))
                return tep_array_unserialize($_SESSION['buyback_cart']);
        } else {
            return $arr;
        }
    } else {
        if (tep_session_is_registered('buyback_cart'))
            return tep_array_unserialize($_SESSION['buyback_cart']);
        else
            return array();
    }
}

function tep_get_buyback_price($products_id) {

    $buybackProductObj = new buyback_product($products_id);
    $unit_price = $buybackProductObj->buyback_bracket_active['price'];
    unset($buybackProductObj);
    return $unit_price;
}

function tep_calculate_buyback_amount($products_id, $real_qty) {
    // 0 = quantity
    // 1 = price
    // 2 = percent or not
    $products_id = (int) $products_id;
    $real_qty = (int) $real_qty;

    return tep_get_buyback_price($products_id) * $real_qty;
}

function tep_get_actual_brackets($brackets, $qty) {
    // 0 = quantity
    // 1 = price
    // 2 = cumulative quantity

    $indexFound = false;
    $prev = 0;
    $ret = array();

    foreach ($brackets as $b) {
        if ($indexFound == false) {
            if ($qty <= $b[0]) {
                $indexFound = true;
                // push the cumulative value
                array_push($b, $b[0]);
                array_push($ret, $b);
                $prev = $b[0];
            }
        } else {
            $prev = $prev + $b[0];
            // push the cumulative value
            array_push($b, $prev);
            array_push($ret, $b);
        }
    }

    return $ret;
}

function tep_buyback_round($a) {
    return ceil($a / 100) * 100;
}

function tep_get_datapool_tree_array($categories_id) {
    $retArray = $parentArray = array();

    $main_level_select_sql = "SELECT * FROM " . TABLE_DATA_POOL_LEVEL . " WHERE data_pool_level_parent_id =0 AND products_id ='" . $categories_id . "' ORDER BY data_pool_sort_order, data_pool_level_name";
    $main_level_result_sql = tep_db_query($main_level_select_sql);

    while ($row = tep_db_fetch_array($main_level_result_sql)) {
        $path = $row['data_pool_level_name'];

        $parentArray[] = array('id' => (int) $row['data_pool_level_id'],
            'parent_id' => (int) $row['data_pool_level_parent_id'],
            'ref_id' => (int) $row['data_pool_ref_id'],
            'name' => $row['data_pool_level_name'],
            'ident' => 0,
            'path' => $path,
            'data_pool_level_value' => $row['data_pool_level_value'],
            'data_pool_input_field' => $row['data_pool_input_field'],
            'data_pool_level_class' => $row['data_pool_level_class'],
            'data_pool_level_class_name' => $row['data_pool_level_class_name'],
            'child' => array());

        tep_get_datapool_subtree_array((int) $row['data_pool_level_id'], $retArray, 0, $path);
        $parentArray[sizeof($parentArray) - 1]['child'] = $retArray;
        $retArray = array();
    }

    return array_reverse($parentArray);
}

function tep_get_datapool_subtree_array($id, &$retArray, $identLength = 0, $parentName = '') {
    $identLength = (int) $identLength + 20;
    $sql = "SELECT * from " . TABLE_DATA_POOL_LEVEL . " where data_pool_level_parent_id ='" . $id . "' order by data_pool_sort_order, data_pool_level_name;";
    $result = tep_db_query($sql);
    $parentArray = array();

    while ($row = tep_db_fetch_array($result)) {
        $path = $parentName . (tep_not_null($parentName) && tep_not_null($row['data_pool_level_name']) ? ' > ' : '') . $row['data_pool_level_name'];

        $parentArray[] = array('id' => (int) $row['data_pool_level_id'],
            'parent_id' => (int) $row['data_pool_level_parent_id'],
            'ref_id' => (int) $row['data_pool_ref_id'],
            'name' => $row['data_pool_level_name'],
            'ident' => $identLength,
            'path' => $path,
            'data_pool_max_level' => $row['data_pool_max_level'],
            'data_pool_min_level' => $row['data_pool_min_level'],
            'data_pool_level_value' => $row['data_pool_level_value'],
            'data_pool_input_field' => $row['data_pool_input_field'],
            'data_pool_level_class' => $row['data_pool_level_class'],
            'data_pool_level_class_name' => $row['data_pool_level_class_name'],
            'child' => array());

        tep_get_datapool_subtree_array((int) $row['data_pool_level_id'], $retArray, $identLength, $path);
        $parentArray[sizeof($parentArray) - 1]['child'] = $retArray;

        $retArray = array();
    }

    $retArray = $parentArray;
}

function tep_array_serialize($arr) {
    return urlencode(serialize($arr));
}

function tep_array_unserialize($val) {
    return unserialize(urldecode(stripslashes($val)));
}

function tep_get_email_greeting_chinese($firstname, $lastname, $gender = '') {
    if (ACCOUNT_GENDER == 'true') {
        if ($gender == 'm') {
            $email_greeting = sprintf(EMAIL_GREET_MR_CHINESE, $lastname);
        } else if ($gender == 'f') {
            $email_greeting = sprintf(EMAIL_GREET_MS_CHINESE, $lastname);
        } else {
            $email_greeting = sprintf(EMAIL_GREET_NONE_CHINESE, $firstname);
        }
    } else {
        $email_greeting = sprintf(EMAIL_GREET_NONE_CHINESE, $firstname);
    }

    return $email_greeting;
}

function tep_gen_random_serial($email, $searchTable = "", $searchField = "", $prefix = "", $str_length = "") {
    $again = true;
    while ($again) {
        $time = time(getdate());
        $random_key = rand(1, 99999);

        $value = md5($email . $time . $random_key . $prefix);

        if (tep_not_null($str_length) && is_numeric($str_length)) {
            $value = substr($value, 0, $str_length);
        }

        $sql = "SELECT " . $searchField . " FROM " . $searchTable . " WHERE " . $searchField . "='" . $value . "'";
        $result = tep_db_query($sql);

        if (tep_db_num_rows($result)) {
            $again = true;
        } else {
            $again = false;
        }
    }
    return $value;
}

function tep_unlink_dir($dir) {
    $h1 = opendir($dir);

    while ($file = readdir($h1)) {
        if ($file == '.' || $file == '..')
            continue;
        if (is_dir($dir . $file))
            continue;
        @unlink($dir . $file);
    }
    closedir($h1);
    @rmdir($dir);
    return (bool) is_dir($dir);
}

function tep_get_cdkey_img($keyident) {
    global $customer_id;

    include_once(DIR_WS_CLASSES . 'custom_product_code.php');
    include_once(DIR_WS_FUNCTIONS . 'customers_info_verification.php');

    if (tep_is_cb_customer($customer_id))
        return;

    $cp_code_id = tep_verify_cdkey_owner($keyident);

    if ($cp_code_id !== FALSE) {
        $custom_products_code_select_sql = "SELECT custom_products_code_id, file_type, custom_products_code_viewed,
                                                to_s3, products_id, code_date_added
                                            FROM " . TABLE_CUSTOM_PRODUCTS_CODE . "
                                            WHERE custom_products_code_id = '" . tep_db_input($cp_code_id) . "'";
        $custom_products_code_result_sql = tep_db_query($custom_products_code_select_sql);

        if ($custom_products_code_row = tep_db_fetch_array($custom_products_code_result_sql)) {
            // Flag this cd key as viewed
            if ($custom_products_code_row['custom_products_code_viewed'] == '0') {
                require_once(DIR_WS_CLASSES . 'log.php');
                $cd_key_log_object = new log_files($customer_id);

                $cdkey_log_select_sql = "SELECT custom_products_code_viewed FROM " . TABLE_CUSTOM_PRODUCTS_CODE . " WHERE custom_products_code_id='" . tep_db_input($cp_code_id) . "'";
                $cdkey_old_log_result_sql = tep_db_query($cdkey_log_select_sql);
                $cdkey_old_log_row = tep_db_fetch_array($cdkey_old_log_result_sql);

                $viewed_update_sql = "	UPDATE " . TABLE_CUSTOM_PRODUCTS_CODE . "
										SET custom_products_code_viewed = 1
										WHERE custom_products_code_id = '" . tep_db_input($cp_code_id) . "'";
                tep_db_query($viewed_update_sql);

                $cdkey_new_log_result_sql = tep_db_query($cdkey_log_select_sql);
                $cdkey_new_log_row = tep_db_fetch_array($cdkey_new_log_result_sql);

                $cdkey_changes_array = $cd_key_log_object->detect_changes($cdkey_old_log_row, $cdkey_new_log_row);

                $cdkey_changes_formatted_array = $cd_key_log_object->construct_log_message($cdkey_changes_array);

                if (count($cdkey_changes_formatted_array)) {
                    $changes_str = 'Changes made:' . "\n";
                    for ($i = 0; $i < count($cdkey_changes_formatted_array); $i++) {
                        if (count($cdkey_changes_formatted_array[$i])) {
                            foreach ($cdkey_changes_formatted_array[$i] as $field => $res) {
                                if (isset($res['plain_result']) && $res['plain_result'] == '1') {
                                    $changes_str .= $res['text'] . "\n";
                                } else {
                                    $changes_str .= '<b>' . $res['text'] . '</b>: ' . $res['from'] . ' --> ' . $res['to'] . "\n";
                                }
                            }
                        }
                    }
                    $cd_key_log_object->insert_cdkey_history_log('customers', $cp_code_id, $changes_str);
                }
            }

            $custom_products_code_id = $custom_products_code_row['custom_products_code_id'];
            $to_s3 = $custom_products_code_row['to_s3'];
            $products_id = $custom_products_code_row['products_id'];
            $code_date_added = $custom_products_code_row['code_date_added'];

            $cpc_obj = new custom_product_code();
            $theData = $cpc_obj->getCode($custom_products_code_id, $to_s3, $products_id, $code_date_added);
            unset($cpc_obj);

            return ($theData !== FALSE ? tep_decrypt_data($theData) : '');
        }
    }
}

function tep_verify_cdkey_owner($keyident) {
    $keyident = trim($keyident);

    $cp_code_id_select_sql = "	SELECT custom_products_code_id, orders_products_id
								FROM " . TABLE_CUSTOM_PRODUCTS_CODE . "
								WHERE custom_products_code_id = '" . tep_db_input($keyident) . "'";
    $cp_code_id_result_sql = tep_db_query($cp_code_id_select_sql);
    if ($cp_code_id_row = tep_db_fetch_array($cp_code_id_result_sql)) {
        $customer_order_verify_select_sql = "	SELECT o.customers_id
												FROM " . TABLE_ORDERS . " AS o
												INNER JOIN " . TABLE_ORDERS_PRODUCTS . " AS op
													ON o.orders_id=op.orders_id
												WHERE o.customers_id = '" . tep_db_input($_SESSION['customer_id']) . "'
													AND o.orders_status IN (2, 3)
													AND op.orders_products_id = '" . (int) $cp_code_id_row['orders_products_id'] . "'";
        $customer_order_verify_result_sql = tep_db_query($customer_order_verify_select_sql);
        if (tep_db_num_rows($customer_order_verify_result_sql)) { // This is the cd key for this customer
            return $cp_code_id_row['custom_products_code_id'];
        }
    }
    return false;
}

// Encrypt Data
function tep_encrypt_data($theData) {
    $length = strlen($theData);
    $length = sprintf("%020d", $length);
    $theData = mcrypt_encrypt(SECURE_CIPHER, SECURE_KEY, $theData, MCRYPT_MODE_CBC, SECURE_KEY_IV);

    $theData = base64_encode($theData);
    $theData = $length . $theData;
    return ($theData);
}

// Decrypt Data
function tep_decrypt_data($theData) {
    if ($theData) {
        $length = substr($theData, 0, 20);
        $theData = substr($theData, 20);
        $length = intval($length);

        $theData = base64_decode($theData);
        $theData = mcrypt_decrypt(SECURE_CIPHER, SECURE_KEY, $theData, MCRYPT_MODE_CBC, SECURE_KEY_IV);
        $theData = substr($theData, 0, $length);
        $theData = base64_decode($theData);
    }

    return $theData;
}

function tep_show_base64_img($keyident) {
    $cdkey_info_select_sql = "	SELECT file_type
                                FROM " . TABLE_CUSTOM_PRODUCTS_CODE . "
                                WHERE custom_products_code_id = '" . $keyident . "'";
    $cdkey_info_result_sql = tep_db_query($cdkey_info_select_sql);
    $cdkey_info_row = tep_db_fetch_array($cdkey_info_result_sql);

    if ($cdkey_info_row['file_type'] == 'soft') {
        ; // Text format
    } else {
        header("Content-type: image/jpeg");
        header("Expires: Mon, 02 May 2001 23:00:00 GMT");
        header("Cache-Control: no-store, no-cache, must-revalidate");
        header("Cache-Control: post-check=0, pre-check=0", false);
        header("Pragma: no-cache");
    }

    echo tep_get_cdkey_img($keyident);
    exit;
}

function br2nl($text) {
    /* Remove XHTML linebreak tags. */
    $text = str_replace("<br />", "\r\n", $text);
    /* Remove HTML 4.01 linebreak tags. */
    $text = str_replace("<br>", "\r\n", $text);
    /* Return the result. */
    return $text;
}

function tep_make_base64_img_zip($key_info, $product_id, $order_id) {
    global $languages_id;

    $keyident_arr = $key_info['cdkey_info'];
    $key_check_sum_arr = $key_info['check_sum'];

    if (md5($order_id . '#' . $product_id . '#' . $_SESSION['customer_id']) != $key_check_sum_arr) {
        die("Download failed");
    }

    require_once(DIR_WS_CLASSES . 'zip_file.php');

    $zipfile = new zipfile();

    $batch_name = "{$order_id}_{$product_id}_Qty" . count($keyident_arr);
    $soft_pin = '';

    if (count($keyident_arr) > 0) {
        foreach ($keyident_arr as $cdkey_id => $row) {
            $file_type = (trim($row['file_type'])) ? $row['file_type'] : 'jpg';
            if ($file_type == 'soft') {
                $soft_pin .= strip_tags(br2nl(tep_get_cdkey_img($row['key_identifier']))) . "\r\n";
            } else {
                $bin_img = tep_get_cdkey_img($row['key_identifier']);
                $zipfile->addFile($bin_img, $row['key_identifier'] . '.' . $row['file_type']);
            }
        }

        if (tep_not_null($soft_pin)) {
            $soft_pin = strip_tags(tep_get_products_name($product_id, $languages_id)) . "\r\n" . $soft_pin;
            $filename = "softpin.txt";

            $zipfile->addFile($soft_pin, $filename);
        }
    }
    $zipfile->sendFile($batch_name);
}

/**
 * @return void
 * @param string	$zipfilename	Filename for result file.
 * @param string	$target_path	Path to archive. Relative to $basedir. Add files or folders without trailing slash. input mask is allowed. ie. *.jpg .
 * @param string	$basedir		Basedir that is parent of $target_path. Specify relative path from where the script is called.
 * @param bool		$send_to_user	True to throw headers for download dialog.
 * @param bool		$storepaths		True to create directory hierarchy in the zip file.
 * @desc Public Interface for archive class.
 */
function tep_make_archive_zip($zipfilename, $target_path, $basedir = DIR_FS_DOWNLOAD, $send_to_user = true, $storepaths = false) {
    $zip = new zip_file($zipfilename);
    // All files added will be relative to the directory in which the script is executing
    // inmemory - Set to 1 to create archive in memory instead of to $basedir. (Nick-12Jun06:Has a bug where choosing 'Open' instead of 'Save as' gives u an empty archive.)
    // recurse - Recurse $target_path
    // overwrite - Overwrite existing $zipfilename if found.
    $zip->set_options(array('inmemory' => 0, 'recurse' => 1, 'storepaths' => $storepaths, 'basedir' => $basedir, 'overwrite' => 1));
    $zip->add_files($target_path);
    // Store without compression
    $zip->store_files("*.exe");
    $zip->create_archive();
    // Send archive to user for download
    if ($send_to_user) {
        $zip->download_file();
    }
}

function tep_parse_live_chat_string($live_chat_string, $privileges_live_chat_string = '') {
    if (tep_not_null($live_chat_string)) {
        // Show the Live Chat only for Regular, Gold, Platinum, Platinum Plus and anonymous Customers
        if (isset($_SESSION["customer_id"])) {
            $customer_info_select_sql = "SELECT c.customers_email_address, c.customers_firstname, c.customers_lastname, c.customers_groups_id, cg.customers_groups_name
										 FROM " . TABLE_CUSTOMERS . " AS c
										 INNER JOIN " . TABLE_CUSTOMERS_GROUPS . " AS cg
										 	ON c.customers_groups_id=cg.customers_groups_id
										 WHERE c.customers_id = '" . tep_db_input($_SESSION["customer_id"]) . "'";
            $customer_info_result_sql = tep_db_query($customer_info_select_sql);

            if ($customer_info_row = tep_db_fetch_array($customer_info_result_sql)) {
                if (!in_array($customer_info_row['customers_groups_id'], array(2, 4, 5, 15))) {
                    $live_chat_string = '';
                }
            } else {
                $live_chat_string = '';
            }
        } else {
            $customer_info_row = array('customers_email_address' => '',
                'customers_firstname' => '',
                'customers_lastname' => '',
                'customers_groups_name' => 'Guest'
            );
        }

        if (tep_not_null($privileges_live_chat_string) && isset($customer_info_row['customers_groups_id']) && ($customer_info_row['customers_groups_id'] == '4' || $customer_info_row['customers_groups_id'] == '5')) {
            $live_chat_string = $privileges_live_chat_string;
        }

        $live_chat_string = str_replace("OGM_CUSTOMER_NAME", urlencode($customer_info_row['customers_firstname'] . ' ' . $customer_info_row['customers_lastname']), $live_chat_string);
        $live_chat_string = str_replace("OGM_CUSTOMER_GROUP", urlencode($customer_info_row['customers_groups_name']), $live_chat_string);
        $live_chat_string = str_replace("OGM_CUSTOMER_EMAIL", urlencode($customer_info_row['customers_email_address']), $live_chat_string);

        $live_chat_string = str_replace("vr=&amp;", "vr=" . urlencode($customer_info_row['customers_email_address']) . "&amp;", $live_chat_string);
        $live_chat_string = str_replace("ve=&amp;", "ve=" . urlencode($customer_info_row['customers_email_address']) . "&amp;", $live_chat_string);
        $live_chat_string = str_replace("vn=&amp;", "vn=" . urlencode($customer_info_row['customers_firstname'] . ' ' . $customer_info_row['customers_lastname']) . "&amp;", $live_chat_string);
        $live_chat_string = str_replace("vp=&amp;", "vp=" . urlencode($telephone_number) . "&amp;", $live_chat_string);
        $live_chat_string = str_replace("vi=&amp;", "vi=" . urlencode($customer_info_row['customers_groups_name']) . "&amp;", $live_chat_string);
    }

    return $live_chat_string;
}

function tep_ip_in_binary_form($ip) {
    $ip_bin_str = '';

    $ip_array = explode('.', $ip);

    for ($i = 0; $i < count($ip_array); $i++) {
        $this_ip_value = ((int) $ip_array[$i] < 0 || (int) $ip_array[$i] > 255) ? 0 : (int) $ip_array[$i];

        $ip_bin_str .= sprintf("%08s", decbin($this_ip_value));
    }

    return $ip_bin_str;
}

function tep_send_affiliate_notification($oID, $order_obj) {
    $customer_affiliate_select_sql = "	SELECT affiliate_ref_id
										FROM " . TABLE_CUSTOMERS . "
										WHERE customers_id = '" . tep_db_input($order_obj->customer["id"]) . "'";
    $customer_affiliate_result_sql = tep_db_query($customer_affiliate_select_sql);
    $customer_affiliate_row = tep_db_fetch_array($customer_affiliate_result_sql);

    if (tep_not_null($customer_affiliate_row['affiliate_ref_id'])) {
        // Check if this is valid sales
        $valid_affiliate_sales = false;
        if (tep_not_null(AFFILIATE_ORDERS_ELIGIBLE_PAYMENT_PERIOD)) {
            $affiliate_sales_select_sql = "	SELECT o.orders_id
	        	  							FROM " . TABLE_ORDERS . " AS o, " . TABLE_CUSTOMERS_INFO . " AS ci
	          								WHERE o.orders_id = '" . tep_db_input($oID) . "'
	          									AND o.customers_id = ci.customers_info_id
	          									AND DATE_FORMAT(o.date_purchased, '%Y-%m-%d %H:%i:%s') <= DATE_ADD(ci.customers_info_date_account_created, INTERVAL " . (int) AFFILIATE_ORDERS_ELIGIBLE_PAYMENT_PERIOD . " DAY) ";
            $affiliate_sales_result_sql = tep_db_query($affiliate_sales_select_sql);
            if (tep_db_num_rows($affiliate_sales_result_sql)) {
                $valid_affiliate_sales = true;
            }
        } else {
            $valid_affiliate_sales = true;
        }

        if ($valid_affiliate_sales) {
            for ($i = 0; $i < count($order_obj->totals); $i++) {
                if ($order_obj->totals[$i]['class'] == 'ot_total') {
                    $total_order_amount = strip_tags($order_obj->totals[$i]['text']);
                }
            }

            $affiliate_profile_select_sql = "SELECT affiliate_gender, affiliate_firstname, affiliate_lastname, affiliate_email_address FROM " . TABLE_AFFILIATE . " WHERE affiliate_id = '" . tep_db_input($customer_affiliate_row['affiliate_ref_id']) . "'";
            $affiliate_profile_result_sql = tep_db_query($affiliate_profile_select_sql);
            if ($affiliate_profile_row = tep_db_fetch_array($affiliate_profile_result_sql)) {
                $email_firstname = $affiliate_profile_row["affiliate_firstname"];
                $email_lastname = $affiliate_profile_row["affiliate_lastname"];

                $email_greeting = tep_get_email_greeting($email_firstname, $email_lastname, $affiliate_profile_row["affiliate_gender"]);

                $email = $email_greeting . sprintf(EMAIL_TEXT_AFFILIATE_NOTIFICATION_INFO, $order_obj->info['date_purchased'], $oID, $total_order_amount) . "\n\n" . EMAIL_FOOTER;

                tep_mail($email_firstname . ' ' . $email_lastname, $affiliate_profile_row['affiliate_email_address'], implode(' ', array(EMAIL_SUBJECT_PREFIX, sprintf(EMAIL_TEXT_AFFILIATE_NOTIFICATION_SUBJECT, (int) $oID, $total_order_amount))), $email, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
            }
        }
    }
}

function tep_date_delta($ts_start_date, $ts_end_date) {
    /*     * ****************************************************************
      Both date parameter is an array in the form:
      (hour, minute, second, month, day, year)
     * **************************************************************** */
    $secs_in_day = 86400;

    $i_years = $ts_end_date["Y"] - $ts_start_date["Y"];
    $i_months = $ts_end_date["m"] - $ts_start_date["m"];
    $i_days = $ts_end_date["d"] - $ts_start_date["d"];
    if ($i_days < 0)
        $i_months--;
    if ($i_months < 0) {
        $i_years--;
        $i_months += 12;
    }
    if ($i_days < 0) {
        $i_days = date('t', mktime(0, 0, 0, $ts_start_date["m"], 1, date('Y'))) - $ts_start_date["d"];
        $i_days += $ts_end_date["d"];
    }

    $ts_end_sec = $ts_end_date["H"] * 3600 + $ts_end_date["i"] * 60 + $ts_end_date["s"];
    $ts_start_sec = $ts_start_date["H"] * 3600 + $ts_start_date["i"] * 60 + $ts_start_date["s"];

    $f_delta = $ts_end_sec - $ts_start_sec;
    $f_secs = $f_delta % $secs_in_day;
    $f_secs -= ($i_secs = $f_secs % 60);
    $i_mins = intval($f_secs / 60) % 60;
    $f_secs -= $i_mins * 60;
    $i_hours = intval($f_secs / 3600);

    return array($i_years, $i_months, $i_days, $i_hours, $i_mins, $i_secs);
    //return array($i_years, $i_months, $i_days, 0, 0, 0);
}

function tep_calculate_age($s_start_date, $s_end_date = '', $b_show_days = 0, $b_show_time = false) {
    if ($b_show_time == true) {
        $b_show_time = strlen($s_start_date > 10) ? true : false;
    }

    $ts_start_date = array('H' => substr($s_start_date, 11, 2),
        'i' => substr($s_start_date, 14, 2),
        's' => substr($s_start_date, 17, 2),
        'm' => substr($s_start_date, 5, 2),
        'd' => substr($s_start_date, 8, 2),
        'Y' => substr($s_start_date, 0, 4));
    if ($s_end_date) {
        $ts_end_date = array('H' => substr($s_end_date, 11, 2),
            'i' => substr($s_end_date, 14, 2),
            's' => substr($s_end_date, 17, 2),
            'm' => substr($s_end_date, 5, 2),
            'd' => substr($s_end_date, 8, 2),
            'Y' => substr($s_end_date, 0, 4));
    } else {
        $ts_end_date = array('H' => date('H'),
            'i' => date('i'),
            's' => date('s'),
            'm' => date('m'),
            'd' => date('d'),
            'Y' => date('Y'));
    }

    if (checkdate($ts_start_date['m'], $ts_start_date['d'], $ts_start_date['Y']) == false || checkdate($ts_end_date['m'], $ts_end_date['d'], $ts_end_date['Y']) == false) {
        return 'Invalid Date';
    }

    list ($i_age_years, $i_age_months, $i_age_days, $i_age_hours, $i_age_mins, $i_age_secs) = tep_date_delta($ts_start_date, $ts_end_date);

    // output
    $s_age = '';
    if ($i_age_years)
        $s_age .= "$i_age_years yr";
    if ($i_age_months)
        $s_age .= ($s_age ? ', ' : '') . "$i_age_months mth";
    if ($b_show_days && $i_age_days) {
        $s_age .= ($s_age ? ', ' : '') . "$i_age_days day" . (abs($i_age_days) > 1 ? 's' : '');
    }

    if ($b_show_time && $i_age_hours)
        $s_age .= ($s_age ? ', ' : '') . "$i_age_hours hour" . (abs($i_age_hours) > 1 ? 's' : '');
    if ($b_show_time && $i_age_mins)
        $s_age .= ($s_age ? ', ' : '') . "$i_age_mins minute" . (abs($i_age_mins) > 1 ? 's' : '');
    if ($b_show_time && $i_age_secs)
        $s_age .= ($s_age ? ', ' : '') . "$i_age_secs second" . (abs($i_age_secs) > 1 ? 's' : '');

    if (!tep_not_null($s_age)) {
        $s_age = $b_show_days ? '0 day' : '0 mth';
    }

    return $s_age;
}

function tep_day_diff($start_date_time, $end_date_time, $result_unit = 'day') {
    $s_yr = $s_mth = $s_day = $s_hr = $s_min = $s_sec = 0;
    $e_yr = $e_mth = $e_day = $e_hr = $e_min = $e_sec = 0;

    list($s_date_str, $s_time_str) = explode(' ', $start_date_time);
    if (tep_not_null($s_date_str)) {
        list($s_yr, $s_mth, $s_day) = explode('-', $s_date_str);
    }
    if (tep_not_null($s_time_str)) {
        list($s_hr, $s_min, $s_sec) = explode(':', $s_time_str);
    }

    list($e_date_str, $e_time_str) = explode(' ', $end_date_time);
    if (tep_not_null($e_date_str)) {
        list($e_yr, $e_mth, $e_day) = explode('-', $e_date_str);
    }
    if (tep_not_null($e_time_str)) {
        list($e_hr, $e_min, $e_sec) = explode(':', $e_time_str);
    }

    if (checkdate((int) $s_mth, (int) $s_day, (int) $s_yr) == false || checkdate((int) $e_mth, (int) $e_day, (int) $e_yr) == false) {
        return false;
    }

    $start_time_secs = mktime($s_hr, $s_min, $s_sec, $s_mth, $s_day, $s_yr);
    $end_time_secs = mktime($e_hr, $e_min, $e_sec, $e_mth, $e_day, $e_yr);
    $daydiff = $end_time_secs - $start_time_secs;

    if ($daydiff < 0) {
        return false;
    }
    if ($result_unit == 'day') {
        return $daydiff / 86400;
    } else if ($result_unit == 'sec') {
        return $daydiff;
    } else if ($result_unit == 'year') {
        return $daydiff / 31536000;
    }
}

function tep_sec_to_daytime($secs) {
    if ($days = intval((floor($secs / 86400))))
        $secs = $secs % 86400;
    if ($hours = intval((floor($secs / 3600))))
        $secs = $secs % 3600;
    if ($minutes = intval((floor($secs / 60))))
        $secs = $secs % 60;
    $secs = intval($secs);

    $result = tep_not_null($days) ? $days . 'd ' : '';
    $result .= tep_not_null($hours) ? strlen($hours) == 1 ? '0' . $hours . ':' : $hours . ':' : '00:';
    $result .= tep_not_null($minutes) ? strlen($minutes) == 1 ? '0' . $minutes . ':' : $minutes . ':' : '00:';
    $result .= tep_not_null($secs) ? strlen($secs) == 1 ? '0' . $secs : $secs : '00';
    return $result;
}

function tep_get_buyback_main_cat_info($id, $from = 'category') {
    $buyback_main_cat_info = array();
    $buyback_main_cat_path = '';

    $cat_path_array = tep_generate_category_path($id, $from);
    for ($cat_cnt = count($cat_path_array[0]) - 1; $cat_cnt >= 0; $cat_cnt--) {
        $buyback_main_cat_path .= $cat_path_array[0][$cat_cnt]['text'] . ' >';

        $main_cat_select_sql = "SELECT categories_id
								FROM " . TABLE_CATEGORIES . "
								WHERE categories_buyback_main_cat = 1
									AND categories_id = '" . (int) $cat_path_array[0][$cat_cnt]['id'] . "'";
        $main_cat_result_sql = tep_db_query($main_cat_select_sql);
        if (tep_db_num_rows($main_cat_result_sql)) {
            $buyback_main_cat_info = array('id' => (int) $cat_path_array[0][$cat_cnt]['id'],
                'text' => $cat_path_array[0][$cat_cnt]['text'],
                'path' => substr($buyback_main_cat_path, 0, -1));
            break;
        }
    }

    return $buyback_main_cat_info;
}

function tep_get_buyback_main_cat_id($product_id) {
    $products_cat_id_path_arr = array();
    $main_cat_id = '';

    $products_cat_id_path_select_sql = "SELECT products_cat_id_path
										FROM " . TABLE_PRODUCTS . "
										WHERE products_id = '" . (int) $product_id . "'";
    $products_cat_id_path_result_sql = tep_db_query($products_cat_id_path_select_sql);

    if ($products_cat_id_path_row = tep_db_fetch_array($products_cat_id_path_result_sql)) {
        $products_cat_id_path_arr = explode("_", $products_cat_id_path_row['products_cat_id_path']);
        $products_cat_id_path_arr = array_slice($products_cat_id_path_arr, 1);

        $main_cat_select_sql = "SELECT categories_id
								FROM " . TABLE_CATEGORIES . "
								WHERE categories_buyback_main_cat = '1'
									AND categories_id IN ('" . implode("','", $products_cat_id_path_arr) . "')";
        $main_cat_result_sql = tep_db_query($main_cat_select_sql);

        if ($main_cat_row = tep_db_fetch_array($main_cat_result_sql)) {
            $main_cat_id = $main_cat_row['categories_id'];
        }
    }

    return $main_cat_id;
}

function tep_get_supplier_pref_setting($sup_id) {
    $setting_array = array();

    $pref_setting_select_sql = "SELECT user_setting_key, user_setting_value FROM " . TABLE_USER_SETTING . " WHERE user_setting_user_id = '" . (int) $sup_id . "'";
    $pref_setting_result_sql = tep_db_query($pref_setting_select_sql);

    while ($pref_setting_row = tep_db_fetch_array($pref_setting_result_sql)) {
        $setting_array[$pref_setting_row["user_setting_key"]] = $pref_setting_row["user_setting_value"];
    }

    return $setting_array;
}

function tep_get_buyback_order_status_name($order_status_id, $language_id = '') {
    global $default_languages_id, $languages_id;

    $status_select_sql = "SELECT buyback_status_name
							FROM " . TABLE_BUYBACK_STATUS . "
							WHERE buyback_status_id = '" . (int) $order_status_id . "'
								AND buyback_status_name <> ''
								AND (IF (language_id = '" . (int) $languages_id . "', 1, IF((	SELECT COUNT(buyback_status_id) > 0
																								FROM " . TABLE_BUYBACK_STATUS . "
																								WHERE buyback_status_id = '" . (int) $order_status_id . "'
																									AND buyback_status_name <> ''
																									AND language_id = '" . (int) $languages_id . "'), 0, language_id = '" . (int) $default_languages_id . "')))";
    $status_result_sql = tep_db_query($status_select_sql);
    $status_row = tep_db_fetch_array($status_result_sql);

    return $status_row['buyback_status_name'];
}

function tep_get_buyback_status($isTop = false, $min = 0) {
    global $languages_id;
    $arrayStack = array();

    $temp = array();
    $result = tep_db_query("select buyback_status_id, buyback_status_name from " . TABLE_BUYBACK_STATUS . " where buyback_status_id >= " . $min . " AND language_id='" . $languages_id . "';");

    while ($row = tep_db_fetch_array($result)) {
        $temp['id'] = $row['buyback_status_id'];
        $temp['text'] = $row['buyback_status_name'];
        array_push($arrayStack, $temp);
        $temp = array();
    }

    return $arrayStack;
}

function tep_time_check($start_time, $end_time, $use_time = '', $return_val = false) {
    if (!tep_not_null($start_time) || !tep_not_null($end_time)) {
        return false;
    }

    $start_time_array = explode(':', $start_time);
    $end_time_array = explode(':', $end_time);

    $start_time = ltrim($start_time_array[0], '0') . $start_time_array[1];
    $end_time = ltrim($end_time_array[0], '0') . $end_time_array[1];

    $start_time = (int) $start_time;
    $end_time = (int) $end_time;

    if (tep_not_null($use_time)) {
        $t_t_array = explode(':', $use_time);
        $t_t_check = ltrim($t_t_array[0], '0') . $t_t_array[1];
        $t_t_check = (int) $t_t_check;
    } else {
        $t_t_array = explode(':', date('H') . ':' . date('i'));
        $t_t_check = ltrim($t_t_array[0], '0') . $t_t_array[1];
        $t_t_check = (int) $t_t_check;
    }

    if ($end_time > $start_time) {
        if ($return_val) {
            if (($t_t_check >= $start_time) && ($end_time >= $t_t_check)) {
                $end_timestamp = mktime($end_time_array[0], $end_time_array[1], 0, date('m'), date('d'), date('Y'));
                $t_timestamp = mktime($t_t_array[0], $t_t_array[1], 0, date('m'), date('d'), date('Y'));
                return ($end_timestamp - $t_timestamp);
            } else {
                return false;
            }
        } else {
            return ($t_t_check >= $start_time) && ($end_time >= $t_t_check);
        }
    } else {
        if ($return_val) {
            if (($t_t_check >= $start_time) || ($t_t_check <= $end_time)) {
                $end_timestamp = mktime($end_time_array[0], $end_time_array[1], 0, date('m'), date('d') + 1, date('Y'));
                $t_timestamp = mktime($t_t_array[0], $t_t_array[1], 0, date('m'), date('d'), date('Y'));
                return ($end_timestamp - $t_timestamp);
            } else {
                return false;
            }
        } else {
            return ($t_t_check >= $start_time) || ($t_t_check <= $end_time);
        }
    }
}

function tep_verify_lists_time_status($start_time, $end_time, $date_time) {
    if (!tep_not_null($date_time))
        return false;

    list($date_str, $time_str) = explode(' ', $date_time);
    list($hr, $min, $sec) = explode(':', $time_str);

    $use_time = $hr . ':' . $min;

    if (tep_time_check($start_time, $end_time, $use_time)) {
        if (($days_lapse = tep_day_diff($date_time, date('Y-m-d H:i:s'))) != FALSE) {
            if ($days_lapse < 1) {
                return true;
            }
        }
    }

    return false;
}

function tep_get_supplier_pricing_setting($sup_grp_id, $list_id) {
    $setting_array = array();

    $pricing_setting_select_sql = "SELECT supplier_pricing_setting_key, supplier_pricing_setting_value FROM " . TABLE_SUPPLIER_PRICING_SETTING . " WHERE supplier_groups_id = '" . (int) $sup_grp_id . "' AND products_purchases_lists_id = '" . (int) $list_id . "'";
    $pricing_setting_result_sql = tep_db_query($pricing_setting_select_sql);

    while ($pricing_setting_row = tep_db_fetch_array($pricing_setting_result_sql)) {
        $setting_array[$pricing_setting_row["supplier_pricing_setting_key"]] = $pricing_setting_row["supplier_pricing_setting_value"];
    }

    return $setting_array;
}

function tep_supplier_check_url($url) {
    return eregi_dep("^https?://[a-z0-9]([-_.]?[a-z0-9])+[.][a-z0-9][a-z0-9/=?.&\~_-]+$", $url);
}

function tep_get_previously_restocked_qty($product_id, $sup_grp_id, $list_id) {
    $list_info_select_sql = "SELECT products_purchases_lists_reference_date FROM " . TABLE_PRODUCTS_PURCHASES_LISTS . " WHERE products_purchases_lists_id = '" . (int) $list_id . "'";
    $list_info_result_sql = tep_db_query($list_info_select_sql);
    $list_info_row = tep_db_fetch_array($list_info_result_sql);

    /*     * ****************************************************************************
      List based deduction. Do not take into account those restock quantities
      for the same product in other lists.
     * **************************************************************************** */
    $restock_qty_select_sql = "	SELECT SUM(solp.products_received_quantity) AS restock_qty
								FROM " . TABLE_SUPPLIER_ORDER_LISTS . " AS sol
								INNER JOIN " . TABLE_SUPPLIER . " AS s
									ON (sol.suppliers_id=s.supplier_id AND s.supplier_groups_id='" . (int) $sup_grp_id . "')
								INNER JOIN " . TABLE_SUPPLIER_ORDER_LISTS_PRODUCTS . " AS solp
									ON (sol.supplier_order_lists_id=solp.supplier_order_lists_id AND solp.supplier_order_lists_type=2 AND solp.products_id='" . (int) $product_id . "')
								WHERE sol.supplier_order_lists_date >= '" . (tep_not_null($list_info_row["products_purchases_lists_reference_date"]) ? trim($list_info_row["products_purchases_lists_reference_date"]) : date('Y-m-d H:i:s')) . "'
									AND sol.supplier_order_lists_status IN (2, 3)
									AND sol.products_purchases_lists_id='" . tep_db_input($list_id) . "'";
    $restock_qty_result_sql = tep_db_query($restock_qty_select_sql);
    $restock_qty_row = tep_db_fetch_array($restock_qty_result_sql);

    return (int) $restock_qty_row["restock_qty"];
}

function tep_update_record_tags($filename, $rec_id, $status_id, $extra_tag) {
    if ($filename == FILENAME_STATS_ORDERS_TRACKING) {

        $orders_tag_select_sql = "SELECT orders_tag_ids FROM " . TABLE_ORDERS . " WHERE orders_id = '" . tep_db_input($rec_id) . "'";

        $orders_tag_result_sql = tep_db_query($orders_tag_select_sql);
        $orders_tag_row = tep_db_fetch_array($orders_tag_result_sql);
        if (tep_not_null($orders_tag_row["orders_tag_ids"])) {
            $new_order_tags_array = array();
            $applicable_orders_tag_select_sql = "	SELECT orders_tag_id
													FROM " . TABLE_ORDERS_TAG . "
													WHERE FIND_IN_SET(orders_tag_id, '" . $orders_tag_row["orders_tag_ids"] . "')
														AND FIND_IN_SET('" . (int) $status_id . "', orders_tag_status_ids)
														AND filename='" . tep_db_input(FILENAME_STATS_ORDERS_TRACKING) . "'";
            $applicable_orders_tag_result_sql = tep_db_query($applicable_orders_tag_select_sql);
            while ($applicable_orders_tag_row = tep_db_fetch_array($applicable_orders_tag_result_sql)) {
                $new_order_tags_array[] = $applicable_orders_tag_row["orders_tag_id"];
            }
            if (tep_not_null($extra_tag) && !in_array($extra_tag, $new_order_tags_array))
                $new_order_tags_array[] = $extra_tag;

            $new_tag_string = count($new_order_tags_array) > 0 ? implode(',', $new_order_tags_array) : '';

            tep_db_query("UPDATE " . TABLE_ORDERS . " SET orders_tag_ids='" . $new_tag_string . "' WHERE orders_id='" . tep_db_input($rec_id) . "'");
        } else {
            if (tep_not_null($extra_tag)) {
                tep_db_query("UPDATE " . TABLE_ORDERS . " SET orders_tag_ids='" . $extra_tag . "' WHERE orders_id='" . tep_db_input($rec_id) . "'");
            }
        }
    } else if ($filename == FILENAME_BUYBACK_REQUESTS) {
        $orders_tag_select_sql = "SELECT orders_tag_ids FROM " . TABLE_BUYBACK_REQUEST_GROUP . " WHERE buyback_request_group_id = '" . tep_db_input($rec_id) . "'";
        $orders_tag_result_sql = tep_db_query($orders_tag_select_sql);
        $orders_tag_row = tep_db_fetch_array($orders_tag_result_sql);

        if (tep_not_null($orders_tag_row["orders_tag_ids"])) {
            $new_order_tags_array = array();
            $applicable_orders_tag_select_sql = "	SELECT orders_tag_id
													FROM " . TABLE_ORDERS_TAG . "
													WHERE FIND_IN_SET(orders_tag_id, '" . $orders_tag_row["orders_tag_ids"] . "')
														AND FIND_IN_SET('" . (int) $status_id . "', orders_tag_status_ids)
														AND filename='" . tep_db_input(FILENAME_BUYBACK_REQUESTS) . "'";
            $applicable_orders_tag_result_sql = tep_db_query($applicable_orders_tag_select_sql);
            while ($applicable_orders_tag_row = tep_db_fetch_array($applicable_orders_tag_result_sql)) {
                $new_order_tags_array[] = $applicable_orders_tag_row["orders_tag_id"];
            }
            if (tep_not_null($extra_tag) && !in_array($extra_tag, $new_order_tags_array))
                $new_order_tags_array[] = $extra_tag;

            $new_tag_string = count($new_order_tags_array) > 0 ? implode(',', $new_order_tags_array) : '';
            tep_db_query("UPDATE " . TABLE_BUYBACK_REQUEST_GROUP . " SET orders_tag_ids='" . $new_tag_string . "' WHERE buyback_request_group_id='" . tep_db_input($rec_id) . "'");
        } else {
            if (tep_not_null($extra_tag)) {
                tep_db_query("UPDATE " . TABLE_BUYBACK_REQUEST_GROUP . " SET orders_tag_ids='" . $extra_tag . "' WHERE buyback_request_group_id='" . tep_db_input($rec_id) . "'");
            }
        }
    }
}

function tep_get_product_unit_price($product_id) {
    $select_price = "SELECT products_price FROM " . TABLE_PRODUCTS . "
					WHERE products_id='" . tep_db_input($product_id) . "'";
    $select_price_result = tep_db_query($select_price);
    $select_price_row = tep_db_fetch_array($select_price_result);
    return $select_price_row['products_price'];
}

function tep_get_total_reserve_vip($products_id) {
    $total_reserve = 0;
    //get the orders products id which for same product
    $select_orders_products = " SELECT DISTINCT(orders_products_id)
								FROM " . TABLE_VIP_ORDER_ALLOCATION . "
								WHERE products_id = '" . tep_db_input($products_id) . "'";
    $select_orders_products_result = tep_db_query($select_orders_products);
    while ($select_orders_products_row = tep_db_fetch_array($select_orders_products_result)) {
        $vipOrderObj = new vip_order($select_orders_products_row['orders_products_id']);
        $vipOrderObj->get_orders_details();
        $max_reserve = $vipOrderObj->order_detail['quantity_need'];
        //total allocate for supplier
        $select_total_in_allocate = "SELECT SUM(vip_order_allocation_quantity) AS vip_order_allocation_quantity
									FROM " . TABLE_VIP_ORDER_ALLOCATION . "
									WHERE orders_products_id = '" . tep_db_input($select_orders_products_row['orders_products_id']) . "'";
        $select_total_in_allocate_result = tep_db_query($select_total_in_allocate);
        $select_total_in_allocate_row = tep_db_fetch_array($select_total_in_allocate_result);

        if ($max_reserve >= $select_total_in_allocate_row['vip_order_allocation_quantity']) {
            $total_reserve = $total_reserve + $select_total_in_allocate_row['vip_order_allocation_quantity'];
        } else {
            $total_reserve = $total_reserve + $max_reserve;
        }
    }
    return $total_reserve;
}

function tep_escape_filtering_id($id_text) {
    $id_text = str_replace(" ", "_", $id_text);
    $id_text = str_replace("&", "", $id_text);
    $id_text = str_replace(".", "", $id_text);
    $id_text = str_replace(",", "", $id_text);
    $id_text = str_replace("(", "", $id_text);
    $id_text = str_replace(")", "", $id_text);
    $id_text = str_replace("'", "", $id_text);
    $id_text = str_replace(":", "", $id_text);
    $id_text = str_replace("!", "", $id_text);
    $id_text = str_replace(",", "", $id_text);
    $id_text = str_replace("/", "", $id_text);
    $id_text = str_replace("·", "", $id_text);

    return $id_text;
}

function tep_escape_filtering_text($id_text) {
    $id_text = str_replace("&", "", $id_text);
    $id_text = str_replace(".", "", $id_text);
    $id_text = str_replace(",", "", $id_text);
    $id_text = str_replace("(", "", $id_text);
    $id_text = str_replace(")", "", $id_text);
    $id_text = str_replace("'", "", $id_text);
    $id_text = str_replace(":", "", $id_text);
    $id_text = str_replace("!", "", $id_text);

    return $id_text;
}

function tep_get_account_created_from($id) {
    $account_created_from = 0;
    $get_customer_info_sql = " SELECT customers_info_account_created_from
								FROM " . TABLE_CUSTOMERS_INFO . "
								WHERE customers_info_id = '" . tep_db_input($id) . "'";
    $get_customer_info_result_sql = tep_db_query($get_customer_info_sql);
    if ($get_customer_info_row = tep_db_fetch_array($get_customer_info_result_sql)) {
        $account_created_from = $get_customer_info_row['customers_info_account_created_from'];
    }
    return $account_created_from;
}

function tep_get_customer_info($customer_id, $field_name) {
    $customers_info_select_sql = "	SELECT " . $field_name . "
									FROM " . TABLE_CUSTOMERS_INFO . "
									WHERE customers_info_id = '" . (int) $customer_id . "'";
    $customers_info_result_sql = tep_db_query($customers_info_select_sql);
    $customers_info_row = tep_db_fetch_array($customers_info_result_sql);

    return $customers_info_row[$field_name];
}

function tep_customer_order_locking($orders_id, $lock_by = '0', $action = 'lock', $vip = 1) {
    // check for order lock
    $user_id = $lock_by;
    if ($lock_by == 0) {
        $user_id = 'system';
    }

    $log_obj = new log_files($user_id);

    if ($action == 'unlock') {
        $order_lock_select_sql = "SELECT orders_locked_by
								FROM " . TABLE_ORDERS . "
								WHERE orders_id = '" . tep_db_input($orders_id) . "'";
        $order_lock_select_result = tep_db_query($order_lock_select_sql);
        $order_lock_select_row = tep_db_fetch_array($order_lock_select_result);

        if (tep_not_null($order_lock_select_row['orders_locked_by'])) {
            //unlock the order
            $admin_email_select_sql = "	SELECT admin_email_address
										FROM " . TABLE_ADMIN . "
										WHERE admin_id = '" . $order_lock_select_row['orders_locked_by'] . "'";
            $admin_email_select_result = tep_db_query($admin_email_select_sql);
            $admin_email_select_row = tep_db_fetch_array($admin_email_select_result);

            if ($lock_by == $order_lock_select_row['orders_locked_by']) {
                //keep log for unlock order
                $log_obj->insert_orders_log($orders_id, ORDERS_LOG_UNLOCK_OWN_ORDER, FILENAME_ADMIN_ORDERS);
            } else {
                $log_obj->insert_orders_log($orders_id, sprintf(ORDERS_LOG_UNLOCK_OTHER_PEOPLE_ORDER, $admin_email_select_row['admin_email_address'], ($vip == 1) ? 'Lock for VIP Order' : 'Lock for GENESIS Project'), FILENAME_ADMIN_ORDERS);
            }

            $order_lock_array = array('orders_locked_by' => 'NULL',
                'orders_locked_from_ip' => 'NULL',
                'orders_locked_datetime' => 'NULL');
            tep_db_perform(TABLE_ORDERS, $order_lock_array, 'update', "  orders_id = '" . tep_db_input($orders_id) . "'");
        }
    } else if ($action == 'lock') {
        //lock order by system.
        $order_lock_array = array('orders_locked_by' => $lock_by,
            'orders_locked_from_ip' => tep_get_ip_address(),
            'orders_locked_datetime' => 'now()');
        tep_db_perform(TABLE_ORDERS, $order_lock_array, 'update', "  orders_id = '" . tep_db_input($orders_id) . "'");
        $log_obj->insert_orders_log($orders_id, ORDERS_LOG_LOCK_ORDER, FILENAME_ADMIN_ORDERS);
    }
}

function tep_get_cur_page_url($pass_action = '') {
    $page_url = 'http';
    if ($_SERVER["HTTPS"] == "on") {
        $page_url .= "s";
    }
    $page_url .= "://";
    if ($_SERVER["SERVER_PORT"] != "80") {
        $page_url .= $_SERVER["SERVER_NAME"] . ":" . $_SERVER["SERVER_PORT"] . $_SERVER["REQUEST_URI"];
    } else {
        $page_url .= $_SERVER["SERVER_NAME"] . $_SERVER["REQUEST_URI"];
    }
    if (tep_not_null($pass_action)) {
        switch ($pass_action) {
            case 'affiliate':
                if (isset($_SESSION['customer_id']) && tep_not_null($_SESSION['customer_id'])) {
                    if (strpos($page_url, '?') === true) {
                        if (substr($page_url, -1) == '?') {
                            $page_url .= 'a_aid=' . $_SESSION['customer_id'];
                        } else {
                            $page_url .= '&a_aid=' . $_SESSION['customer_id'];
                        }
                    } else {
                        $page_url .= '?a_aid=' . $_SESSION['customer_id'];
                    }
                }
                break;
            default:
                //
                break;
        }
    }
    return $page_url;
}

function tep_get_cur_script($pass_action = '') {
    $page_url = $_SERVER["REQUEST_URI"];
    if ($page_url{0} == "/")
        $page_url = substr($page_url, 1);
    return $page_url;
}

function tep_get_store_keyword($ctype, $language_id = 0) {
    global $memcache_obj;
    $return_string = '';

    if ($language_id == 0)
        $language_id = $_SESSION['languages_id'];

    $cache_key = TABLE_CUSTOM_PRODUCTS_TYPE_CHILD_LANG . '/get/custom_products_type_child_name/custom_products_type_child_id/' . $ctype . '/language/' . $language_id;
    $cache_result = $memcache_obj->fetch($cache_key);

    if ($cache_result !== FALSE) {
        $return_string = $cache_result;
    } else {
        $store_name_select_sql = "	SELECT custom_products_type_child_name
									FROM " . TABLE_CUSTOM_PRODUCTS_TYPE_CHILD_LANG . "
									WHERE custom_products_type_child_id = " . (int) $ctype . " AND
										languages_id = " . (int) $language_id;
        $store_name_result_sql = tep_db_query($store_name_select_sql);
        if ($store_name_row = tep_db_fetch_array($store_name_result_sql)) {
            $return_string = $store_name_row['custom_products_type_child_name'];
            $memcache_obj->store($cache_key, $return_string, 86400);
        }
    }

    return $return_string;
}

function tep_get_store_url($ctype) {
    global $memcache_obj;
    $return_string = '';

    $cache_key = TABLE_CUSTOM_PRODUCTS_TYPE_CHILD . '/get/custom_products_type_child_url/custom_products_type_child_id/' . $ctype;
    $cache_result = $memcache_obj->fetch($cache_key);

    if ($cache_result !== FALSE) {
        $return_string = $cache_result;
    } else {
        $store_name_select_sql = "	SELECT custom_products_type_child_url
										FROM " . TABLE_CUSTOM_PRODUCTS_TYPE_CHILD . "
										WHERE custom_products_type_child_id = " . (int) $ctype;
        $store_name_result_sql = tep_db_query($store_name_select_sql);
        if ($store_name_row = tep_db_fetch_array($store_name_result_sql)) {
            $return_string = $store_name_row['custom_products_type_child_url'];
            $memcache_obj->store($cache_key, $return_string, 86400);
        }
    }

    return $return_string;
}

function tep_insert_cron_pending_credit($trans_type, $trans_id, $trans_created_date, $mature_period, $trans_status) {

    $cron_job_verify_select_sql = "	SELECT cron_pending_credit_trans_completed_date
									FROM " . TABLE_CRON_PENDING_CREDIT . "
									WHERE cron_pending_credit_trans_type = '" . tep_db_input($trans_type) . "'
										AND cron_pending_credit_trans_id = '" . tep_db_input($trans_id) . "'";
    $cron_job_verify_result_sql = tep_db_query($cron_job_verify_select_sql);

    if (!tep_db_num_rows($cron_job_verify_result_sql)) {
        $cron_pending_credit_data_array = array('cron_pending_credit_trans_type' => tep_db_prepare_input($trans_type),
            'cron_pending_credit_trans_id' => tep_db_prepare_input($trans_id),
            'cron_pending_credit_trans_created_date' => tep_db_prepare_input($trans_created_date),
            'cron_pending_credit_trans_completed_date' => 'now()',
            'cron_pending_credit_mature_period' => $mature_period,
            'cron_pending_credit_trans_status' => $trans_status
        );
        tep_db_perform(TABLE_CRON_PENDING_CREDIT, $cron_pending_credit_data_array);
    }
}

function tep_update_orders_status_counter($record_array, $increment = 1) {
    $o_id = isset($record_array['orders_id']) ? (int) $record_array['orders_id'] : 0;
    $o_status_id = isset($record_array['orders_status_id']) ? (int) $record_array['orders_status_id'] : 0;
    $o_latest_date = (isset($record_array['date_added']) && $record_array['date_added'] != 'now()') ? '\'' . $record_array['date_added'] . '\'' : 'now()';

    if (!isset($record_array['changed_by']))
        $record_array['changed_by'] = 'system';

    if ($o_id && $o_status_id > 0) {
        $record_exist_select_sql = "	SELECT orders_id
                                        FROM " . TABLE_ORDERS_STATUS_STAT . "
                                        WHERE orders_id = " . $o_id . "
                                                AND orders_status_id = " . $o_status_id;
        $record_exist_result_sql = tep_db_query($record_exist_select_sql);

        if ($record_exist_row = tep_db_fetch_array($record_exist_result_sql)) {
            $orders_status_sql = "	UPDATE " . TABLE_ORDERS_STATUS_STAT . "
                                        SET occurrence=occurrence+" . (int) $increment . ",
                                            latest_date = " . $o_latest_date . ",
                                            changed_by = '" . $record_array['changed_by'] . "'
                                        WHERE orders_id = " . $o_id . "
                                            AND orders_status_id = " . $o_status_id;
            tep_db_query($orders_status_sql);
        } else {
            $orders_status_sql = "	INSERT INTO " . TABLE_ORDERS_STATUS_STAT . "
                                        (orders_id, orders_status_id, occurrence, first_date, latest_date, changed_by)
                                        VALUES (" . $o_id . ", " . $o_status_id . ", " . (int) $increment . ", NOW(), " . $o_latest_date . ", '" . $record_array['changed_by'] . "')";
            tep_db_query($orders_status_sql);

            switch ($o_status_id) {
                case 7:
                    $c_sel = "  SELECT customers_id, payment_methods_id, payment_methods_parent_id, date_purchased 
                                FROM " . TABLE_ORDERS . " WHERE orders_id = " . $o_id;
                    $c_res = tep_db_query($c_sel);
                    if ($c_row = tep_db_fetch_array($c_res)) {
                        $pm_title = array();
                        $pm_id = array(
                            'payment_methods_parent_id' => $c_row['payment_methods_parent_id'],
                            'payment_methods_id' => $c_row['payment_methods_id']
                        );

                        foreach ($pm_id as $key => $val) {
                            $_title = '';

                            if (empty($val) && ($key == 'payment_methods_id')) {
                                $_title = 'Full Store Credit';
                            } else {
                                $pm_sel = " SELECT payment_methods_title FROM " . TABLE_PAYMENT_METHODS . " WHERE payment_methods_id = '" . $val . "'";
                                $pm_res = tep_db_query($pm_sel);
                                if ($pm_row = tep_db_fetch_array($pm_res)) {
                                    $_title = $pm_row['payment_methods_title'];
                                }
                            }

                            if ($_title !== '') {
                                $pm_title[$key] = $_title;
                            }
                        }

                        $m_data1 = array(
                            'customers_id' => $c_row['customers_id'],
                            'statistic_key' => 'last_payment_method_id',
                            'statistic_value' => $pm_id['payment_methods_id'],
                            'created_date' => $c_row['date_purchased']
                        );
                        $m_data2 = array(
                            'customers_id' => $c_row['customers_id'],
                            'statistic_key' => 'last_payment_method_title',
                            'statistic_value' => implode(' -> ', $pm_title),
                            'created_date' => $c_row['date_purchased']
                        );

                        $m_sel = "  SELECT created_date FROM " . TABLE_CUSTOMERS_PURCHASE_STATISTIC . " 
                                    WHERE customers_id = " . $c_row['customers_id'] . " 
                                        AND statistic_key = 'last_payment_method_id'";
                        $m_res = tep_db_query($m_sel);
                        if ($m_row = tep_db_fetch_array($m_res)) {
                            if (strtotime($c_row['date_purchased']) > strtotime($m_row['created_date'])) {
                                tep_db_perform(TABLE_CUSTOMERS_PURCHASE_STATISTIC, $m_data1, 'update', " customers_id = '" . $c_row['customers_id'] . "' AND statistic_key = 'last_payment_method_id' ");
                                tep_db_perform(TABLE_CUSTOMERS_PURCHASE_STATISTIC, $m_data2, 'update', " customers_id = '" . $c_row['customers_id'] . "' AND statistic_key = 'last_payment_method_title' ");
                            }
                        } else {
                            tep_db_perform(TABLE_CUSTOMERS_PURCHASE_STATISTIC, $m_data1);
                            tep_db_perform(TABLE_CUSTOMERS_PURCHASE_STATISTIC, $m_data2);
                        }
                        unset($m_data1, $m_data2, $m_data3);
                    }
                    break;
            }
        }

        return true;
    } else {
        return false;
    }
}

// get product extra info details
function tep_draw_products_extra_info($orders_product_id = '', $info_key = '') {
    $qry1 = tep_not_null($info_key) ? "AND orders_products_extra_info_key='" . tep_db_input($info_key) . "'" : '';
    $orders_products_extra_info_select_query = "SELECT * FROM " . TABLE_ORDERS_PRODUCTS_EXTRA_INFO . " WHERE orders_products_id='" . (int) $orders_product_id . "' $qry1 ";
    $orders_products_extra_info_select_res = tep_db_query($orders_products_extra_info_select_query);

    while ($orders_products_extra_info_row = tep_db_fetch_array($orders_products_extra_info_select_res)) {
        $extra_info[$orders_products_extra_info_row['orders_products_extra_info_key']] = $orders_products_extra_info_row['orders_products_extra_info_value'];
    }
    return $extra_info;
}

function tep_customer_delivery_confirmation($method_id) {
    switch ($method_id) {
        case '1': // Face 2 Face
        case '3': // Mail
        case '4': // Open Store
            return true; // Required Customer to Confirm
            break;
        case '2': // Put Into My Account
            return false;
            break;
        default:
            return false;
            break;
    }
}

function tep_direct_open_buyback($method_id) {
    switch ($method_id) {
        case '1': // Face 2 Face
        case '4': // Open Store
            return false;
            break;
        case '2': // Put Into My Account
        case '3': // Mail
            return true;
            break;
        default:
            return false;
            break;
    }
}

function tep_order_product_pending_delivery($order_status, $custom_type, $customer_id) {
    $total_count = 0;

    if (tep_not_null($custom_type)) {
        switch ($custom_type) {
            case 1:
                $get_total_pending_select_sql = "	SELECT o.orders_id
													FROM " . TABLE_ORDERS . " AS o
													INNER JOIN " . TABLE_ORDERS_PRODUCTS . " AS op
														ON o.orders_id = op.orders_id
													WHERE customers_id = '" . (int) $customer_id . "'
														AND orders_status = '" . $order_status . "'
														AND op.custom_products_type_id = '1'
														AND op.products_quantity <> op.products_delivered_quantity";
                $get_total_pending_result_sql = tep_db_query($get_total_pending_select_sql);
                if (tep_db_num_rows($get_total_pending_result_sql)) {
                    $total_count++;
                }

                break;

            case 2:
                $get_total_pending_select_sql = "	SELECT o.orders_id
													FROM " . TABLE_ORDERS . " AS o
													INNER JOIN " . TABLE_ORDERS_PRODUCTS . " AS op
														ON o.orders_id = op.orders_id
													WHERE customers_id = '" . (int) $customer_id . "'
														AND orders_status = '" . $order_status . "'
														AND op.custom_products_type_id = '2'
														AND op.products_quantity <> op.products_delivered_quantity";
                $get_total_pending_result_sql = tep_db_query($get_total_pending_select_sql);
                if (tep_db_num_rows($get_total_pending_result_sql)) {
                    $total_count++;
                }
                break;

            case 3:
                $get_total_pending_select_sql = "	SELECT o.orders_id, op.orders_products_is_compensate, op.parent_orders_products_id
													FROM " . TABLE_ORDERS . " AS o
													INNER JOIN " . TABLE_ORDERS_PRODUCTS . " AS op
														ON o.orders_id = op.orders_id
													WHERE customers_id = '" . (int) $customer_id . "'
														AND orders_status = '" . $order_status . "'
														AND op.custom_products_type_id = '0'
														AND op.products_quantity <> op.products_delivered_quantity";
                $get_total_pending_result_sql = tep_db_query($get_total_pending_select_sql);
                while ($get_total_pending_row = tep_db_fetch_array($get_total_pending_result_sql)) {
                    if ($get_total_pending_row['orders_products_is_compensate'] == 1) {
                        if ($get_total_pending_row['parent_orders_products_id'] == 0) {
                            $total_count++;
                        }
                    } else {
                        if ($get_total_pending_row['parent_orders_products_id'] != 0) {
                            $total_count++;
                        }
                    }
                }
                break;
            default:
                break;
        }
    }
    return $total_count;
}

function tep_get_payment_info_verified_date($check_info, $compare_type, $verified_status = '') {
    $payment_method_array = array(
        'credit_card',
        'paypal_payer_id',
        'moneybookers'
    );
    $verified_date = '';

    if (in_array($compare_type, $payment_method_array)) {
        $s_key = '';
        switch ($compare_type) {
            case 'credit_card':
                if (isset($check_info['card_number']) && tep_not_null($check_info['card_number'])) {
                    $s_key = substr($check_info['card_number'], -4);
                }
                break;

            case 'paypal_payer_id':
                if (isset($check_info['payer_id']) && tep_not_null($check_info['payer_id'])) {
                    $s_key = tep_db_input($check_info['payer_id']);
                }
                break;
            case 'moneybookers':
                if (isset($check_info['email']) && tep_not_null($check_info['email'])) {
                    $s_key = tep_db_input($check_info['email']);
                }
                break;
        }

        if (!empty($s_key)) {
            $m_sel = "  SELECT statistic_value FROM " . TABLE_CUSTOMERS_PURCHASE_STATISTIC . " 
                        WHERE customers_id = " . $check_info['customers_id'] . " 
                            AND statistic_key = '" . $compare_type . "_" . $s_key . "'";
            $m_res = tep_db_query($m_sel);
            if ($m_row = tep_db_fetch_array($m_res)) {
                $verified_date = $m_row['statistic_value'];
            } else {
                $orders_array = array();
                $check_status = array("2", "3");
                if (is_array($verified_status) && count($verified_status)) {
                    $check_status = $verified_status;
                }

                switch ($compare_type) {
                    case 'credit_card':
                        if (isset($check_info['card_number']) && tep_not_null($check_info['card_number'])) {
                            $formatted_credit_card_number = substr($check_info['card_number'], -4);

                            $adyen_credit_card_select_sql = "   SELECT o.orders_id 
                                                                FROM " . TABLE_ORDERS . " AS o 
                                                                INNER JOIN " . TABLE_ADYEN . " AS a
                                                                    ON o.orders_id = a.adyen_order_id
                                                                WHERE a.adyen_cc_card_summary = '" . tep_db_input($formatted_credit_card_number) . "'
                                                                    AND o.customers_id = '" . (int) $check_info['customers_id'] . "' 
                                                                    AND o.date_purchased < '" . $check_info['date_purchased'] . "' 
                                                                    AND o.orders_status IN ('" . implode("','", $check_status) . "')";
                            $adyen_credit_card_result_sql = tep_db_query($adyen_credit_card_select_sql);
                            while ($adyen_credit_card_row = tep_db_fetch_array($adyen_credit_card_result_sql)) {
                                $orders_array[] = $adyen_credit_card_row['orders_id'];
                            }

                            $bibit_credit_card_select_sql = "   SELECT o.orders_id 
                                                                FROM " . TABLE_ORDERS . " AS o 
                                                                INNER JOIN " . TABLE_BIBIT . " AS b 
                                                                    ON o.orders_id = b.orders_id
                                                                WHERE b.bibit_card_number LIKE '%" . tep_db_input($formatted_credit_card_number) . "'
                                                                    AND o.customers_id = '" . (int) $check_info['customers_id'] . "' 
                                                                    AND o.date_purchased < '" . $check_info['date_purchased'] . "' 
                                                                    AND o.orders_status IN ('" . implode("','", $check_status) . "')";
                            $bibit_credit_card_result_sql = tep_db_query($bibit_credit_card_select_sql);
                            while ($bibit_credit_card_row = tep_db_fetch_array($bibit_credit_card_result_sql)) {
                                $orders_array[] = $bibit_credit_card_row['orders_id'];
                            }

                            $gc_credit_card_select_sql = "  SELECT o.orders_id
                                                            FROM " . TABLE_ORDERS . " AS o
                                                            INNER JOIN " . TABLE_GLOBAL_COLLECT . " AS gc
                                                                ON o.orders_id = gc.global_collect_orders_id
                                                            WHERE gc.global_collect_cc_last_4_digit = '" . tep_db_input($formatted_credit_card_number) . "'
                                                                AND o.customers_id = '" . (int) $check_info['customers_id'] . "'
                                                                AND o.date_purchased < '" . $check_info['date_purchased'] . "'
                                                                AND o.orders_status IN ('" . implode("','", $check_status) . "')";
                            $gc_credit_card_result_sql = tep_db_query($gc_credit_card_select_sql);
                            while ($gc_credit_card_row = tep_db_fetch_array($gc_credit_card_result_sql)) {
                                $orders_array[] = $gc_credit_card_row['orders_id'];
                            }
                        }

                        break;
                    case 'paypal_payer_id':
                        if (isset($check_info['payer_id']) && tep_not_null($check_info['payer_id'])) {
                            $check_credit_card_select_sql = "   SELECT o.orders_id
                                                                FROM " . TABLE_ORDERS . " AS o
                                                                INNER JOIN " . TABLE_PAYPAL . " AS p
                                                                    ON o.orders_id = p.invoice
                                                                WHERE p.payer_id = '" . tep_db_input($check_info['payer_id']) . "'
                                                                    AND o.customers_id = '" . (int) $check_info['customers_id'] . "'
                                                                    AND o.date_purchased < '" . $check_info['date_purchased'] . "'
                                                                    AND o.orders_status IN ('" . implode("','", $check_status) . "')";
                            $check_credit_card_result_sql = tep_db_query($check_credit_card_select_sql);
                            while ($check_credit_card_row = tep_db_fetch_array($check_credit_card_result_sql)) {
                                $orders_array[] = $check_credit_card_row['orders_id'];
                            }
                        }
                        break;
                    case 'moneybookers':
                        if (isset($check_info['email']) && tep_not_null($check_info['email'])) {
                            $check_select_sql = "   SELECT o.orders_id
                                                    FROM " . TABLE_ORDERS . " AS o
                                                    INNER JOIN " . TABLE_PAYMENT_MONEYBOOKERS . " AS p
                                                        ON o.orders_id = p.mb_trans_id
                                                    WHERE p.mb_payer_email = '" . tep_db_input($check_info['email']) . "'
                                                        AND o.customers_id = '" . (int) $check_info['customers_id'] . "'
                                                        AND o.date_purchased < '" . $check_info['date_purchased'] . "'
                                                        AND o.orders_status IN ('" . implode("','", $check_status) . "')";
                            $check_result_sql = tep_db_query($check_select_sql);
                            while ($check_row = tep_db_fetch_array($check_result_sql)) {
                                $orders_array[] = $check_row['orders_id'];
                            }
                        }
                        break;
                }

                if (count($orders_array)) {
                    $orders_status_history_select_sql = "   SELECT first_date 
                                                            FROM " . TABLE_ORDERS_STATUS_STAT . "
                                                            WHERE orders_id IN ('" . implode("','", $orders_array) . "')
                                                                AND orders_status_id = '2'
                                                            ORDER BY first_date
                                                            LIMIT 1";
                    $orders_status_history_result_sql = tep_db_query($orders_status_history_select_sql);
                    if ($orders_status_history_row = tep_db_fetch_array($orders_status_history_result_sql)) {
                        if (strtotime($orders_status_history_row['first_date']) < strtotime($check_info['date_purchased'])) {
                            $verified_date = $orders_status_history_row['first_date'];

                            $m_sel = "  INSERT INTO " . TABLE_CUSTOMERS_PURCHASE_STATISTIC . " (customers_id, statistic_key, statistic_value, created_date) 
                                        VALUES (" . $check_info['customers_id'] . ", '" . $compare_type . '_' . $s_key . "', '" . $verified_date . "', NOW()) 
                                        ON DUPLICATE KEY UPDATE customers_id = customers_id";
                            tep_db_query($m_sel);
                        }
                    }
                }
            }

            if ($verified_date !== '') {
                $verified_date = date('Y-m-d', strtotime($verified_date));
            }
        }
    }
    return $verified_date;
}

function tep_record_search_key($keywords, $categories_id = '', $customer_id = '', $ip = '') {
    if (!tep_not_null($ip))
        $ip = tep_get_ip_address();

    if (tep_not_null($keywords) && $keywords != TEXT_SEARCH_WHOLE_STORE) {
        $sql_data_array = array('search_keywords_log_date' => 'now()',
            'search_keywords_log_keywords' => $keywords,
            'search_keywords_log_categories_id' => $categories_id,
            'search_keywords_log_customer_id' => $customer_id,
            'search_keywords_log_ip' => $ip,
            'search_keywords_log_ip_country' => tep_get_ip_country_id()
        );
        tep_db_perform(TABLE_SEARCH_KEYWORDS_LOG, $sql_data_array);
    }
}

function tep_hla_game_name($seller_id, $products_id) {
    global $memcache_obj;
    $folder_name = '';

    $cache_key = TABLE_PRODUCTS_RSS_LINK . '/products_id/' . $products_id . '/seller_id/' . $seller_id . '/folder_name';

    $cache_result = $memcache_obj->fetch($cache_key);

    if ($cache_result !== FALSE) {
        $folder_name = $cache_result;
    } else {
        $get_folder_name_select_sql = "	SELECT pts.folder_name
										FROM " . TABLE_PRODUCTS_TO_SUPPLIER . " AS pts
										WHERE pts.supplier_id = '" . tep_db_input($seller_id) . "'
										AND pts.products_id = '" . tep_db_input($products_id) . "'";
        $get_folder_name_result_sql = tep_db_query($get_folder_name_select_sql);
        if ($get_folder_name_row = tep_db_fetch_array($get_folder_name_result_sql)) {
            $folder_name = $get_folder_name_row['folder_name'];
        }

        $memcache_obj->store($cache_key, $folder_name, 86400);
    }

    return $folder_name;
}

function tep_date_raw($date, $reverse = false) {
    if ($reverse) {
        return substr($date, 3, 2) . substr($date, 0, 2) . substr($date, 6, 4);
    } else {
        return substr($date, 6, 4) . substr($date, 0, 2) . substr($date, 3, 2);
    }
}

function tep_get_categories_by_customer_group($categories_id_array, $customers_groups_id, $fields = 'DISTINCT c.categories_id, c.parent_id, c.categories_parent_path') {
    $categories_memcache_row = array();
    $categories_select_sql = " 	SELECT " . $fields . "
                                FROM " . TABLE_CATEGORIES . " AS c
                                INNER JOIN " . TABLE_CATEGORIES_GROUPS . " AS cg
                                    ON (c.categories_id = cg.categories_id)
                                WHERE c.categories_status = 1
                                    AND ((cg.groups_id = '" . $customers_groups_id . "') OR (cg.groups_id = 0))
                                    AND c.categories_id IN ('" . implode("', '", $categories_id_array) . "')
                                ORDER BY c.sort_order";

    $categories_result_sql = tep_db_query($categories_select_sql);
    while ($categories_row = tep_db_fetch_array($categories_result_sql)) {
        $categories_memcache_row[] = $categories_row;
    }

    return $categories_memcache_row;
}

function tep_game_cpath($parent_id, $categories_parent_path, $categories_id) {
    $return_array = array();

    if ($parent_id > 0) {
        $cur_cat_path_array = explode('_', substr($categories_parent_path, 1, -1));
        $return_array['current_game_category_id'] = $cur_cat_path_array[0];
        $return_array['games_categories_path'] = substr($categories_parent_path, 1) . $categories_id;
    } else {
        $return_array['current_game_category_id'] = $categories_id;
        $return_array['games_categories_path'] = $categories_id;
    }

    return $return_array;
}

function tep_display_op($total_op, $extend_left_content = '') {
    return '<span class="wTip" title="' . TOOLTIPS_OFFGAMERS_POINTS . '" style="background: url(\'' . DIR_WS_ICONS . 'icon_wor.png\') no-repeat scroll 0 0 transparent;padding: 0 0 0 20px;display:inline-block;"><span style="line-height: 18px;">' . $total_op . $extend_left_content . '</span></span>';
}

// ================== For 2012 new purchase flow
function update_lifetime_cookies() {
    global $SESS_LIFE, $cookie_path, $cookie_domain;
    $max_life = 1440;
    $normal_life = 10;
    $lifetime_secret = '';

    if (isset($SESS_LIFE)) {
        $max_life = $SESS_LIFE;
    }

    require_once(DIR_WS_CLASSES . 'access_token.php');
    $access_token_obj = new access_token();
    $_SESSION['lifetime_secret'] = $access_token_obj->generateAccessToken(time() . $_SESSION['customer_id']);
    unset($access_token_obj);

    if (isset($_SESSION['customer_id']) && tep_not_empty($_SESSION['customer_id'])) {
        if (isset($_SESSION['fb_uid'])) {
            $cookies_expired = time() + $max_life;
        } else {
            $cookies_expired = time() + ($normal_life * 60);
        }

        $content = $cookies_expired . '-' . md5($cookies_expired . $_SESSION['lifetime_secret']);
    } else {
        $cookies_expired = time() - ($normal_life * 60);
        $content = '';
    }

    tep_setcookie("u_valid", $content, $cookies_expired, $cookie_path, $cookie_domain); // Validated user life time
}

function check_is_lifetime_cookies_existed($extended_in_sec = 0) {
    $return_bool = FALSE;

    if (isset($_SESSION['customer_id']) && isset($_SESSION['lifetime_secret'])) {
        if (isset($_SESSION['fb_uid'])) {
            // no need to verify password
            $return_bool = TRUE;
        } else {
            if (isset($_COOKIE['u_valid'])) {
                $split_content = explode('-', $_COOKIE['u_valid']);

                if (count($split_content) == 2) {
                    if ((int) $split_content[0] > (time() - $extended_in_sec)) {
                        if ($split_content[1] === md5($split_content[0] . $_SESSION['lifetime_secret'])) {
                            $return_bool = TRUE;
                        }
                    }
                }
            }
        }
    }

    return $return_bool;
}

function update_dtu_game_info($gameID, $data_array, $productID = '') {
    global $cookie_path, $cookie_domain;
    $cookie_array = array();

    if (!tep_not_empty($gameID) && tep_not_empty($productID)) {
        require_once(DIR_WS_CLASSES . 'product.php');
        global $cPath_array;
        $current_category_id = tep_get_actual_product_cat_id($productID);
        $cPath = tep_get_particular_cat_path($current_category_id);
        $cPath_array = tep_parse_category_path($cPath);

        $prod_info = new product($cPath, 0);
        $prod_info->cpath_check();
        $gameID = $prod_info->main_game_id;
        unset($prod_info);
    }

    foreach ($data_array as $field => $value) {
        if (tep_not_empty($value)) {
            $cookie_array[$field] = $value;
        }
    }

    if (count($cookie_array) && tep_not_empty($gameID)) {
        $cookies_expired = time() + 60 * 60 * 24 * 365; // 1 year expiry date;
        tep_setcookie('DTU[' . $gameID . ']', json_encode($cookie_array), $cookies_expired, $cookie_path, $cookie_domain);
    }
}

function get_dtu_game_info($gameID, $productID = '') {
    $cookie_array = array();
    $temp_array = array();

    if (!tep_not_empty($gameID) && tep_not_empty($productID)) {
        require_once(DIR_WS_CLASSES . 'product.php');
        global $cPath_array;
        $current_category_id = tep_get_actual_product_cat_id($productID);
        $cPath = tep_get_particular_cat_path($current_category_id);
        $cPath_array = tep_parse_category_path($cPath);

        $prod_info = new product($cPath, 0);
        $prod_info->cpath_check();
        $gameID = $prod_info->main_game_id;
        unset($prod_info);
    }

    if (isset($_COOKIE['DTU'][$gameID])) {
        $temp_array = json_decode(tep_db_prepare_input(strip_tags($_COOKIE['DTU'][$gameID])), true);

        if (is_array($temp_array) && count($temp_array)) {
            foreach ($temp_array as $field => $value) {
                $value = tep_db_prepare_input(strip_tags($value));
                $field = tep_db_prepare_input(strip_tags($field));

                if (tep_not_empty($value) && tep_not_empty($field)) {
                    $cookie_array[$field] = $value;
                }
            }
        }

        unset($temp_array);
    }

    return $cookie_array;
}

function validate_max_pending_order($customer_id, $checkoutType = '') {
    if (defined('B2C_MAX_NUM_OF_PENDING') && defined('B2C_MAX_PENDING_WITHIN_MIN')) {
        $count_order_select_sql = "SELECT COUNT(orders_id) as total_orders
                                    FROM " . TABLE_ORDERS . "
                                    WHERE customers_id = '" . $customer_id . "'
                                        AND date_purchased > DATE_SUB(NOW(), INTERVAL " . B2C_MAX_PENDING_WITHIN_MIN . " MINUTE) 
                                        AND orders_status = 1";
        $count_order_result_sql = tep_db_query($count_order_select_sql);
        if ($count_order_row = tep_db_fetch_array($count_order_result_sql)) {
            if ($count_order_row['total_orders'] >= B2C_MAX_NUM_OF_PENDING) {
                $admin_email_to_array = tep_parse_email_string(B2C_EXCEED_MAX_PENDING_RECIPIENT);
                for ($admin_email_to_cnt = 0; $admin_email_to_cnt < count($admin_email_to_array); $admin_email_to_cnt++) {
                    @tep_mail($admin_email_to_array[$admin_email_to_cnt]['name'], $admin_email_to_array[$admin_email_to_cnt]['email'], implode(' ', array(EMAIL_SUBJECT_PREFIX, sprintf(EMAIL_MAX_PENDING_ORDER_SUBJECT, $customer_id))), sprintf(EMAIL_MAX_PENDING_ORDER_TEXT, $customer_id), STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
                }
                if ($checkoutType != 'Paypal') {
                    $delete_temp_process_sql = "DELETE FROM " . TABLE_TEMP_PROCESS . "
												WHERE page_name = 'checkout_process.php'
													AND match_case = '" . $customer_id . "'";
                    tep_db_query($delete_temp_process_sql);
                }
                if ($checkoutType == 'SC') {
                    tep_redirect(tep_href_link(FILENAME_SC_CHECKOUT_PAYMENT, 'payment_error=ccerr&error_message=' . sprintf(TEXT_ERROR_MAX_PENDING_ORDER, $count_order_row['total_orders']), 'SSL'));
                } else {
                    tep_redirect(tep_href_link(FILENAME_CHECKOUT_PAYMENT, 'payment_error=ccerr&error_message=' . sprintf(TEXT_ERROR_MAX_PENDING_ORDER, $count_order_row['total_orders']), 'SSL'));
                }
                exit;
            }
        }
    }
}

function getProductsUrlAlias($pid) {
    global $memcache_obj;
    $return_string = '';

    $pid = (int) $pid;

    $cache_key = TABLE_PRODUCTS . 'products_url_alias/array/products_id/' . $pid;
    $cache_result = $memcache_obj->fetch($cache_key);

    if ($cache_result !== FALSE) {
        $return_string = $cache_result;
    } else {
        $select_sql = " SELECT products_url_alias
                        FROM " . TABLE_PRODUCTS . "
                        WHERE products_id = '" . $pid . "'
                            AND custom_products_type_id = 2
                            AND products_status = 1";
        $result_sql = tep_db_query($select_sql);
        if ($row = tep_db_fetch_array($result_sql)) {
            $return_string = $row['products_url_alias'];
        }

        $memcache_obj->store($cache_key, $return_string, 21600); // cache 6 hours
    }

    return $return_string;
}

function getSinglePathTagKeyByGameID($game_id) {
    global $memcache_obj;
    $tag_id = 0;
    $return_array = array();

    $game_id = (int) $game_id;

    $cache_key = 'categories_tagmap' . '/tag_id/integer/game_id/' . $game_id;
    $cache_result = $memcache_obj->fetch($cache_key);

    if ($cache_result !== FALSE) {
        $tag_id = $cache_result;
    } else {
        $select_sql = " SELECT tag_id
                        FROM categories_tagmap
                        WHERE game_id = '" . $game_id . "'";
        $result_sql = tep_db_query($select_sql);
        if ($row = tep_db_fetch_array($result_sql)) {
            $tag_id = $row['tag_id'];
        }

        $memcache_obj->store($cache_key, $tag_id, 21600); // cache 6 hours
    }

    if ($tag_id) {
        $cache_key = 'categories_tag' . '/single_path/array/tag_id/' . $tag_id;
        $cache_result = $memcache_obj->fetch($cache_key);

        if ($cache_result !== FALSE) {
            $return_array = $cache_result;
        } else {
            $select_sql = "	SELECT parent.tag_id, parent.tag_key
                            FROM categories_tag AS node,
                                categories_tag AS parent
                            WHERE node.tag_lft BETWEEN parent.tag_lft AND parent.tag_rgt
                                AND node.tag_id = '" . $tag_id . "'
                                AND node.tag_status = 1
                            ORDER BY node.tag_lft";
            $result_sql = tep_db_query($select_sql);
            while ($row = tep_db_fetch_array($result_sql)) {
                $return_array[] = $row['tag_key'];
            }

            array_shift($return_array);

            $memcache_obj->store($cache_key, $return_array, 21600); // cache 6 hours
        }
    }

    return $return_array;
}

// ================== For 2012 new purchase flow
// BOF: WebMakers.com Added: Downloads Controller
require(DIR_WS_FUNCTIONS . 'downloads_controller.php');
// EOF: WebMakers.com Added: Downloads Controller
// BOF IndvShip
//require(DIR_WS_FUNCTIONS . 'indvship_status.php');
// EOF
?>