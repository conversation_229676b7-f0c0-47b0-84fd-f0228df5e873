<?
/*
  	$Id: html_output.php,v 1.64 2013/09/09 08:42:53 weesiong Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2003 osCommerce
	
  	Released under the GNU General Public License
*/

////
// The HTML href link wrapper function
function tep_href_link($page = '', $parameters = '', $connection = 'NONSSL', $add_session_id = true, $search_engine_safe = true, $use_achor = '') {
	global $request_type, $session_started, $SID;
	
	if (!tep_not_null($page)) {
    	die('</td></tr></table></td></tr></table><br><br><font color="#ff0000"><b>Error!</b></font><br><br><b>Unable to determine the page link!<br><br>');
    }
	
	if ($page == '/') $page = '';	// Assume is home link
	
	if ($request_type == 'SSL') {
		$link = HTTPS_SERVER . DIR_WS_HTTPS_CATALOG;
		$http_host = HTTPS_SERVER;
	} else if ($connection == 'NONSSL') {
      	$link = HTTP_SERVER . DIR_WS_HTTP_CATALOG;
		$http_host = HTTP_SERVER;
    } else if ($connection == 'SSL') {
    	if (ENABLE_SSL == true) {
    		$link = HTTPS_SERVER . DIR_WS_HTTPS_CATALOG;
    		$http_host = HTTPS_SERVER;
       	} else {
       		$link = HTTP_SERVER . DIR_WS_HTTP_CATALOG;
       		$http_host = HTTP_SERVER;
		}
	} else {
      	die('</td></tr></table></td></tr></table><br><br><font color="#ff0000"><b>Error!</b></font><br><br><b>Unable to determine connection method on a link!<br><br>Known methods: NONSSL SSL</b><br><br>');
    }
	
    if ( (SEARCH_ENGINE_FRIENDLY_URLS == 'true') && ($search_engine_safe == true) ) {
    	include_once(DIR_WS_CLASSES . 'seo.php'); //seo url class file
	    $seo = new seo_url($page, $parameters);
	    $parameters = $seo->parameters;
	    $if_seo_url = $seo->if_seo_url;
	    $get_data_array = $seo->get_data_array;
	}
	
	if (tep_not_null($parameters)) {
    	if ($if_seo_url) {
    		$link .= tep_output_string($parameters) . FILENAME_URL_EXTENSION; // *.ogm
    		$separator = '?';
    	} else {
	      	$link .= $page . '?' . tep_output_string($parameters);
	      	$separator = '&';
	   	}
    } else {
      	$link .= $page;
      	$separator = '?';
    }
	
    while ( (substr($link, -1) == '&') || (substr($link, -1) == '?') ) $link = substr($link, 0, -1);
	
	// Add the session ID when moving from different HTTP and HTTPS servers, or when SID is defined
    if ( ($add_session_id == true) && ($session_started == true) && (SESSION_FORCE_COOKIE_USE == 'False') ) {
      	if (tep_not_null($SID)) {
        	$_sid = $SID;
      	} elseif ( ( ($request_type == 'NONSSL') && ($connection == 'SSL') && (ENABLE_SSL == true) ) || ( ($request_type == 'SSL') && ($connection == 'NONSSL') ) ) {
        	if (HTTP_COOKIE_DOMAIN != HTTPS_COOKIE_DOMAIN) {
          		$_sid = tep_session_name() . '=' . tep_session_id();
        	}
      	}
    }
	
	if (tep_not_null($get_data_array)) {
		$link .= $separator . $get_data_array;
		$separator = '&';
	}
	/*
    if (isset($_sid)) {
      	$link .= $separator . $_sid;
    }
	*/
    if (tep_not_null($use_achor)) {
    	$link .= $use_achor;
    }
    
	# append current_language_code (eg: zh-CN, zh-TW, etc) into URL
    if (SEARCH_ENGINE_FRIENDLY_URLS == 'true') {
    	if (!eregi_dep('callback.php', $page) && !eregi_dep('ipn.php', $page) && !eregi_dep('checkout_', $page)) {
			global $current_language_code;
			
			if (($link == $http_host) || ($page == $http_host)) { # home link
				if ($link != $page && tep_not_null($page)) $link = $page;
				$link .= (tep_not_null($current_language_code) ? $current_language_code . '/' : '');
			} else if (eregi_dep("^http", $page) && !eregi_dep("^" . $http_host, $page)) {
				$link = $page;
			} else {
				$link = $http_host . (tep_not_null($current_language_code) ? $current_language_code . '/' : '') . ltrim(str_replace($http_host, '', $link), '/');
			}
		}
	}
	
    return $link;
}

function tep_affiliate_href_link($page = '', $parameters = '', $connection = 'NONSSL') {
	global $request_type;
	
	if (!tep_not_null($page)) {
      	die('</td></tr></table></td></tr></table><br><br><font color="#ff0000"><b>Error!</b></font><br><br><b>Unable to determine the page link!<br><br>Function used:<br><br>tep_href_link(\'' . $page . '\', \'' . $parameters . '\', \'' . $connection . '\')</b>');
    }
    
    if ($request_type == 'SSL') {
		$link = HTTPS_AFFILIATE_SERVER . DIR_WS_AFFILIATE;
	} else if ($connection == 'NONSSL') {
      	$link = HTTP_AFFILIATE_SERVER . DIR_WS_AFFILIATE;
    } else if ($connection == 'SSL') {
      	if (ENABLE_SSL == 'true') {
        	$link = HTTPS_AFFILIATE_SERVER . DIR_WS_AFFILIATE;
      	} else {
        	$link = HTTP_AFFILIATE_SERVER . DIR_WS_AFFILIATE;
      	}
    } else {
      	die('</td></tr></table></td></tr></table><br><br><font color="#ff0000"><b>Error!</b></font><br><br><b>Unable to determine connection method on a link!<br><br>Known methods: NONSSL SSL<br><br>Function used:<br><br>tep_href_link(\'' . $page . '\', \'' . $parameters . '\', \'' . $connection . '\')</b>');
    }
	
    if (tep_not_null($parameters)) {
		$link = $link . $page . '?' . $parameters . (substr($parameters,-1)=='&' ? '' : '&') . (defined('SID') ? SID : '');
    } else {
		$link = $link . $page . '?' . (defined('SID') ? SID : '');
    }
	
    while ( (substr($link, -1) == '&') || (substr($link, -1) == '?') ) $link = substr($link, 0, -1);
	
    return $link;
}

function tep_upload_href_link($page = '', $parameters = '', $connection = 'NONSSL') {
	if ($connection == 'NONSSL') {
		$link = HTTP_UPLOAD_SERVER . DIR_WS_UPLOAD;
    } else if ($connection == 'SSL') {
      	if (ENABLE_SSL_UPLOAD == 'true') {
        	$link = HTTPS_UPLOAD_SERVER . DIR_WS_UPLOAD;
      	} else {
        	$link = HTTP_UPLOAD_SERVER . DIR_WS_UPLOAD;
      	}
	} else {
		die('</td></tr></table></td></tr></table><br><br><font color="#ff0000"><b>Error!</b></font><br><br><b>Unable to determine connection method on a link!<br><br>Known methods: NONSSL SSL<br><br>Function used:<br><br>tep_upload_href_link(\'' . $page . '\', \'' . $parameters . '\', \'' . $connection . '\')</b>');
    }

    if ($parameters == '') {
      	$link .= $page;
    } else {
      	$link .= $page . '?' . $parameters;
    }

    while ( (substr($link, -1) == '&') || (substr($link, -1) == '?') ) $link = substr($link, 0, -1);

    return $link;
}
////
// The HTML image wrapper function
function tep_image($src, $alt = '', $width = '', $height = '', $parameters = '') {
	if ( (empty($src) || ($src == DIR_WS_IMAGES)) && (IMAGE_REQUIRED == 'false') ) {
		return false;
    }
	
	// alt is added to the img tag even if it is null to prevent browsers from outputting
	// the image filename as default
	$image = '<img src="' . tep_output_string($src) . '" border="0" alt="' . tep_output_string($alt) . '"';
	
    if (tep_not_null($alt)) {
		$image .= ' title=" ' . tep_output_string($alt) . ' "';
    }
	
    if ( (CONFIG_CALCULATE_IMAGE_SIZE == 'true') && (empty($width) || empty($height)) && strpos($src, '?') === FALSE) {
      	if ($image_size = @getimagesize($src)) {
        	if (empty($width) && tep_not_null($height)) {
          		$ratio = $height / $image_size[1];
          		$width = $image_size[0] * $ratio;
        	} else if (tep_not_null($width) && empty($height)) {
          		$ratio = $width / $image_size[0];
          		$height = $image_size[1] * $ratio;
        	} else if (empty($width) && empty($height)) {
          		$width = $image_size[0];
          		$height = $image_size[1];
        	}
      	} else if (IMAGE_REQUIRED == 'false') {
        	return false;
      	}
	}
	
    if (tep_not_null($width) && tep_not_null($height)) {
		$image .= ' width="' . tep_output_string($width) . '" height="' . tep_output_string($height) . '"';
    }
	
    if (tep_not_null($parameters)) $image .= ' ' . $parameters;
	
    $image .= '>';
	
    return $image;
}

////
// The HTML form submit button wrapper function
// Outputs a button in the selected language

function tep_button($name, $alt = '', $url='', $parameters = '', $css_class = 'generalBtn', $btn_active = true) {
   	$button_html = '<input type="BUTTON" value="'.$name.'" class="'.$css_class.'" ';

    if (tep_not_null($alt)) $button_html .= ' title=" ' . tep_output_string($alt) . ' "';

    if (tep_not_null($parameters)) $button_html .= ' ' . $parameters;

	$button_html .= ($btn_active == true ? ' onMouseOver="this.className=\''.$css_class.'Over'.'\'"' : '') .' onMouseOut="this.className=\''.$css_class.'\'" ';

    if (tep_not_null($url))	$button_html .= 'onClick="window.location=\''.$url.'\'"';

    $button_html .= '>';

    return $button_html;
}

function tep_image_submit($image='', $alt = '', $parameters = '', $css_class = 'generalBtn', $btn_active = true, $fixed_width = '',$own_path = '') {
	global $language;
	
	if ((tep_not_null($image) && file_exists(DIR_WS_LANGUAGES . $language . '/images/buttons/' . $image)) || (tep_not_null($image) && tep_not_null($own_path))) {
	    $image_submit = '<input type="image" style="border:none;" src="' . tep_output_string(($own_path) ? $own_path.$image : DIR_WS_LANGUAGES . $language . '/images/buttons/' . $image) . '" alt="' . tep_output_string($alt) . '"';
		
	    if (tep_not_null($alt)) $image_submit .= ' title=" ' . tep_output_string($alt) . ' "';
		
		if (tep_not_null($parameters)) {
			$image_submit .= ' ' . $parameters;
		}
		
	    $image_submit .= '>';
	} else {
		if (utf8_strlen($alt) == strlen($alt))	$alt = strtoupper($alt);
   	
	   	$image_submit = '<input type="SUBMIT" value="'.$alt.'" class="'.$css_class.'" ';
		
	    if (tep_not_null($alt)) $image_submit .= ' title=" ' . tep_output_string($alt) . ' "';
		
	    if (tep_not_null($parameters)) $image_submit .= ' ' . $parameters;
	    
		$image_submit .= ($btn_active == true ? ' onMouseOver="this.className=\''.$css_class.'Over'.'\'"' : '') .' onMouseOut="this.className=\''.$css_class.'\'" ';
		
	    if ($fixed_width) {
	    	$fixed_width = (int)$fixed_width;
	    	$image_submit .= ' style="width: '.( ($fixed_width*utf8_strlen($alt)) + (utf8_strlen($alt) != strlen($alt) ? (int)($fixed_width/1.5)*utf8_strlen($alt) : 0) ).'px"';
	    } else {
	    	if (utf8_strlen($alt) < (int)BUTTON_MIN_CHAR_LENGTH)  $image_submit .= ' ';
	    }
	    
	    $image_submit .= '>';
	}
	return $image_submit;
}

////
// Output a function button in the selected language
function tep_image_button($image, $alt = '', $url='', $parameters = '', $css_class = 'generalBtn', $btn_active = true, $fixed_width = '', $img_width = '', $img_height = '') {
	global $language;
	
	if (tep_not_null($image) && file_exists(DIR_WS_LANGUAGES . $language . '/images/buttons/' . $image)) {
	    $normal_image = tep_output_string(DIR_WS_LANGUAGES . $language . '/images/buttons/' . $image);
	   	$idx = strrpos ($normal_image, ".");
	   	if ($idx === false)
	     	exit ("Invalid image: $image");
	   	$highlight_image = substr($normal_image, 0, $idx) . "_over" . substr ($normal_image, $idx);
	   	if (file_exists ($highlight_image)) {
	     	$parameters .= " onMouseOver=\"this.src='$highlight_image'\" onMouseOut=\"this.src='$normal_image'\" class=\"inputButton\"";
		}
		
		if (tep_not_null($url))	$parameters .= 'onClick="window.location=\''.$url.'\'"';
		
	   	return tep_image($normal_image, $alt, $img_width, $img_height, $parameters . ' class="inputButton"');
	} else {
	   	if (utf8_strlen($alt) == strlen($alt))	$alt = strtoupper($alt);
	   	
	   	$image_submit = '<input type="BUTTON" value="'.$alt.'" class="'.$css_class.'" ';
		
	    if (tep_not_null($alt)) $image_submit .= ' title=" ' . tep_output_string($alt) . ' "';
		
	    if (tep_not_null($parameters)) $image_submit .= ' ' . $parameters;
		
		$image_submit .= ($btn_active == true ? ' onMouseOver="this.className=\''.$css_class.'Over'.'\'"' : '') .' onMouseOut="this.className=\''.$css_class.'\'" ';
		
		if (utf8_strlen($alt) < (int)BUTTON_MIN_CHAR_LENGTH)  $image_submit .= ' ';
	    
	    if (tep_not_null($url))	$image_submit .= 'onClick="window.location=\''.$url.'\'"';
	    
	    if ($fixed_width) {
	    	$fixed_width = (int)$fixed_width;
	    	$fixed_width = ($fixed_width*utf8_strlen($alt)) + (utf8_strlen($alt) != strlen($alt) ? (int)($fixed_width/1.5)*utf8_strlen($alt) : 0);
	    	$image_submit .= ' style="width: '.$fixed_width.'px"';
	    }
	    
	    $image_submit .= '>';
		
	    return $image_submit;
	}
}

function tep_image_button2($image_type, $url, $label = '', $btn_width_px = '', $parameters = '') {
	$return_string = '';
	$minus_px = 6;
	
	switch ($image_type) {
		case 'green' :
			$btn_class = 'green_btn';
			break;
		case 'yellow' :
			$btn_class = 'yellow_btn';
			break;
		case 'red' :
			$btn_class = 'red_btn';
			$minus_px = 29;
			break;
		case 'gray' :
			$btn_class = 'gray_btn';
			break;
		case 'gray_box':
			$btn_class = 'gray_box';
			break;
		case 'gray_short' :
			$btn_class = 'gray_short_btn';
			break;
		case 'gray_tall' :
			$btn_class = 'gray_tall_btn';
			break;
        case 'gray_big_tall' :
			$btn_class = 'gray_big_tall_btn';
			break;
	}
	
	$div_style = tep_not_null($btn_width_px) ? ' style="width:' . $btn_width_px . 'px"' : '';
	$parameters .= tep_not_null($btn_width_px) ? ' style="width:' . ($btn_width_px-$minus_px) . 'px"' : '';
	
	$return_string = '<div class="main_btn ' . $btn_class . '"' . $div_style . '><a href="' . tep_output_string($url) . '"' . $parameters . '><font>' . $label . '</font><span></span></a></div>';
	
	return $return_string;
}

////////////////////////////////////////   
// return tep_image(DIR_WS_LANGUAGES . $language . '/images/buttons/' . $image, $alt, '', '', $parameters);
// }

////
// Output a separator either through whitespace, or with an image
function tep_draw_separator($image = 'pixel_black.gif', $width = '100%', $height = '1') {
	return tep_image(DIR_WS_IMAGES . $image, '', $width, $height);
}

////
// Output a form
function tep_draw_form($name, $action, $method = 'post', $parameters = '') {
	$form = '<form name="' . tep_output_string($name) . '" action="' . tep_output_string($action) . '" method="' . tep_output_string($method) . '"';
	
    if (tep_not_null($parameters)) $form .= ' ' . $parameters;
	
    $form .= '>';
    
    return $form;
}

////
// Output a form
function tep_upload_draw_form($name, $action, $parameters = '', $method = 'post', $params = '') {
	$form = '<form name="' . tep_output_string($name) . '" action="';
    if (tep_not_null($parameters)) {
      	$form .= tep_upload_href_link($action, $parameters, 'SSL');
    } else if (tep_not_null($action)) {
      	$form .= tep_upload_href_link($action, '', 'SSL');
    }
    $form .= '" method="' . tep_output_string($method) . '"';
    if (tep_not_null($params)) {
      	$form .= ' ' . $params;
    }
    $form .= '>';

	if (SID && strtolower($method) == 'get') $form .= tep_draw_hidden_field(tep_session_name(), tep_session_id());

    return $form;
}

////
// Output a form input field
function tep_draw_input_field($name, $value = '', $parameters = '', $type = 'text', $reinsert_value = true) {
	$field = '<input type="' . tep_output_string($type) . '" name="' . tep_output_string($name) . '"';
	
    if ( (isset($GLOBALS[$name])) && ($reinsert_value == true) ) {
      	$field .= ' value="' . tep_output_string(stripslashes($GLOBALS[$name])) . '"';
    } else if (tep_not_null($value)) {
      	$field .= ' value="' . tep_output_string($value) . '"';
    }
	
    if (tep_not_null($parameters)) $field .= ' ' . $parameters;
	
    $field .= '>';
	
    return $field;
}

////
// Output a form password field
function tep_draw_password_field($name, $value = '', $parameters = 'maxlength="40"') {
	return tep_draw_input_field($name, $value, $parameters, 'password', false);
}

////
// Output a selection field - alias function for tep_draw_checkbox_field() and tep_draw_radio_field()
function tep_draw_selection_field($name, $type, $value = '', $checked = false, $parameters = '') {
	$selection = '<input type="' . tep_output_string($type) . '" name="' . tep_output_string($name) . '"';
	
    if (tep_not_null($value)) $selection .= ' value="' . tep_output_string($value) . '"';
	
    if ( ($checked == true) || ( isset($GLOBALS[$name]) && is_string($GLOBALS[$name]) && ( ($GLOBALS[$name] == 'on') || (isset($value) && (stripslashes($GLOBALS[$name]) == $value)) ) ) ) {
      	$selection .= ' CHECKED';
    }
	
    if (tep_not_null($parameters)) $selection .= ' ' . $parameters;
	
    $selection .= '>';
	
    return $selection;
}

////
// Output a form checkbox field
function tep_draw_checkbox_field($name, $value = '', $checked = false, $parameters = '') {
	$parameters .= ' class="checkBox" ';
    return tep_draw_selection_field($name, 'checkbox', $value, $checked, $parameters);
}

////
// Output a form radio field
function tep_draw_radio_field($name, $value = '', $checked = false, $parameters = '') {
	$parameters .= ' class="radio" ';
    return tep_draw_selection_field($name, 'radio', $value, $checked, $parameters);
}

////
// Output a form textarea field
function tep_draw_textarea_field($name, $wrap, $width, $height, $text = '', $parameters = '', $reinsert_value = true) {
	$field = '<textarea name="' . tep_output_string($name) . '" wrap="' . tep_output_string($wrap) . '" cols="' . tep_output_string($width) . '" rows="' . tep_output_string($height) . '"';
	
    if (tep_not_null($parameters)) $field .= ' ' . $parameters;
	
    $field .= '>';
	
    if ( (isset($GLOBALS[$name])) && ($reinsert_value == true) ) {
      	$field .= stripslashes($GLOBALS[$name]);
    } else if (tep_not_null($text)) {
      	$field .= $text;
    }
	
    $field .= '</textarea>';
	
    return $field;
}

////
// Output a form hidden field
function tep_draw_hidden_field($name, $value = '', $parameters = '', $force_value = false) {
	$field = '<input type="hidden" name="' . tep_output_string($name) . '"';
	
    if (tep_not_null($value)) {
      	$field .= ' value="' . tep_output_string($value) . '"';
    } else if (isset($GLOBALS[$name])) {
      	$field .= ' value="' . tep_output_string(stripslashes($GLOBALS[$name])) . '"';
    } else if ($force_value == true) {
        $field .= ' value="' . $value . '"';
    }
	
    if (tep_not_null($parameters)) $field .= ' ' . $parameters;
	
    $field .= '>';
	
    return $field;
}

////
// Hide form elements
function tep_hide_session_id() {
	global $session_started, $SID;
	
    if (($session_started == true) && tep_not_null($SID)) {
      	return tep_draw_hidden_field(tep_session_name(), tep_session_id());
    }
}

////
// Output a form pull down menu
function tep_draw_pull_down_menu($name, $values, $default = '', $parameters = '', $required = false) {
	$field = '<select name="' . tep_output_string($name) . '"';
	
    if (tep_not_null($parameters)) $field .= ' ' . $parameters;
	
    $field .= '>';
	
    if (empty($default) && isset($GLOBALS[$name])) $default = stripslashes($GLOBALS[$name]);
	
    for ($i=0, $n=sizeof($values); $i<$n; $i++) {
      	$field .= '<option value="' . tep_output_string($values[$i]['id']) . '"';
      	if ($default == $values[$i]['id']) {
        	$field .= ' SELECTED';
      	}
		
      	$field .= '>' . tep_output_string($values[$i]['text'], array('"' => '&quot;', '\'' => '&#039;', '<' => '&lt;', '>' => '&gt;')) . '</option>';
	}
    $field .= '</select>';
	
    if ($required == true) $field .= TEXT_FIELD_REQUIRED;
	
    return $field;
}

function tep_draw_css_pull_down_menu ($box_id, $box_array, $box_default='', $group_id = '', $box_width = 270) {
	$dd_li = '';
	$dt_style = ' style="width:'.$box_width.'px;"';
	$ul_style = ' style="width:'.($box_width+20).'px;"';
	
	if (isset($box_default['text']) && isset($box_default['id'])) {
		$box_default = $box_default['text'] . '<span class="value notranslate">' . $box_default['id'] . '</span>';
	} else {
		$box_default = $box_array[0]['text'];
	}
	
	if (isset($box_array[0]['ext'])) {
		foreach ($box_array AS $li_array) {
			$li_class = $dd_li!=''?' class="dotborder"':'';
			$dd_li .= '<li' . $li_class . '><a href="javascript:void(0)">' . $li_array['text'] . '<span class="value notranslate">' . $li_array['id'] . '</span></a></li>';	
		}
	} else {
		foreach ($box_array AS $li_array) {
			$li_class = $dd_li!=''?' class="dotborder"':'';
			$dd_li .= '<li' . $li_class . '><a href="javascript:void(0)">' . $li_array['text'] . '<span class="value notranslate">' . $li_array['id'] . '</span></a></li>';	
		}
	}
	
	$dd_drop_down = '<dl id="' . $box_id . '" class="customdropdown ' . $group_id . '">
				        <dt' . $dt_style . '><a href="javascript:void(0)"><span class="sel">' . $box_default . '</span></a></dt>
				        <dd><ul' . $ul_style . '>' . $dd_li . '</ul></dd>
				    </dl>';
	
	return $dd_drop_down;
}

function tep_draw_css_pull_down_menu2 ($box_id, $box_array, $box_default='', $group_id = '', $box_width = 200, $scroll_height = '') {
	$a_selected_class = '';
	$span_selected_class = '';
	$dd_li = '';
	$default_value = $box_array[0]['text'] . '<span class="value notranslate">' . $box_array[0]['id'] . '</span>';
	$ul_scroll = tep_not_empty($scroll_height) ? 'overflow-y:scroll;max-height:' . $scroll_height . 'px;' : '';
	$ul_style = 'width:' . ($box_width-2) . 'px;' . $ul_scroll;
	
	if (tep_not_null($box_default)) {
		foreach ($box_array AS $li_array) {
			$li_class = $dd_li!=''?' class="dotborder"':'';
			
			if ($li_array['id'] == $box_default) {
				$a_selected_class = ' class="selected"';
				$span_selected_class = ' cancel';
				$default_value = $li_array['text'] . '<span class="value">' . $li_array['id'] . '</span>';
			}
			
			$dd_li .= '<li' . $li_class . '><a href="javascript:void(0)"' . $li_array['ext'] . '>' . $li_array['text'] . '<span class="value notranslate">' . $li_array['id'] . '</span></a></li>';	
		}
	} else {
		foreach ($box_array AS $li_array) {
			$li_class = $dd_li!=''?' class="dotborder"':'';
			$dd_li .= '<li' . $li_class . '><a href="javascript:void(0)"' . $li_array['ext'] . '>' . $li_array['text'] . '<span class="value notranslate">' . $li_array['id'] . '</span></a></li>';	
		}
	}
	
	$dd_drop_down = '<dl id="' . $box_id . '" class="customdropdown2 ' . $group_id . '" style="width:'.$box_width.'px;">
				        <dt><a href="javascript:void(0)"' . $a_selected_class . '><span class="sel">' . $default_value . '</span><span class="arrow' . $span_selected_class . '"></span></a></dt>
				        <dd>
				            <ul style="' . $ul_style . '">' . $dd_li . '</ul>
				        </dd>
				    </dl>';
	
	return $dd_drop_down;
}

function tep_draw_pull_down_menu_cat($name, $values, $default = '', $parameters = '', $required = false) {
	$field = '<select name="' . tep_output_string($name) . '"';
	
    if (tep_not_null($parameters)) $field .= ' ' . $parameters;
	
    $field .= '>';
	
    if (empty($default) && isset($GLOBALS[$name])) $default = stripslashes($GLOBALS[$name]);
	
    for ($i=0, $n=sizeof($values); $i<$n; $i++) {
      	$field .= '<option value="' . tep_output_string($values[$i]['id']) . '"';
      	if ($default == $values[$i]['id']) {
        	$field .= ' SELECTED';
      	}
		
      	$field .= '>' . tep_output_string($values[$i]['text'], array('"' => '&quot;', '\'' => '&#039;', '<' => '&lt;', '>' => '&gt;')) . '</option>';
	}
    $field .= '</select>';
	
    if ($required == true) $field .= TEXT_FIELD_REQUIRED;
	
    return $field;
}

////
// Creates a pull-down list of countries
function tep_get_country_list($name, $selected = '', $parameters = '') {
	$countries_array = array(array('id' => '', 'text' => PULL_DOWN_DEFAULT));
    $countries = tep_get_countries();
	
    for ($i=0, $n=sizeof($countries); $i<$n; $i++) {
      	$countries_array[] = array('id' => $countries[$i]['countries_id'], 'text' => $countries[$i]['countries_name']);
    }
	
    return tep_draw_pull_down_menu($name, $countries_array, $selected, $parameters);
}


// Changes made @ 200809161740 refering Mantis # 0000024: Generate Country list
function tep_get_ezsignup_country_list($name, $selected = '', $parameters = '') 
{
    $countries_array = array(array('id' => '', 'text' => PULL_DOWN_DEFAULT));
    $countries = tep_get_countries();
	
    for ($i=0, $n=sizeof($countries); $i<$n; $i++) 
    {
    	$countries_code = tep_get_coutries_dialing_codes($countries[$i]['countries_id']);
    	$countries_text = $countries[$i]['countries_name'].' +'.$countries_code['countries_international_dialing_code'];
    	
      	$countries_array[] = array('id' => $countries[$i]['countries_id'], 'text' => $countries_text);
    }
	
    return tep_draw_pull_down_menu($name, $countries_array, $selected, $parameters);
}
// ##


// Changes made @ 200809181655 refering Mantis # 0000024: Generate Security Questions List
function tep_get_ezsignup_security_questions_list($name, $selected = '', $parameters = '') 
{
	global $languages_id;
	
    $customer_security_questions_array = array(array('id' => '', 'text' => PULL_DOWN_DEFAULT));
    
    $customer_security_select_sql = "	SELECT customers_security_question_id, customers_security_question 
										FROM " . TABLE_CUSTOMERS_SECURITY_QUESTIONS ." 
										WHERE language_id = '" . (int)$languages_id . "'";
	$customer_security_select_result = tep_db_query($customer_security_select_sql);
	while ($customer_security_select_row = tep_db_fetch_array($customer_security_select_result)) 
	{
		$customer_security_questions_array[] = array('id' => $customer_security_select_row['customers_security_question_id'],
													 'text' => $customer_security_select_row['customers_security_question']	);
	}
    
    return tep_draw_pull_down_menu($name, $customer_security_questions_array, $selected, $parameters);
}
// ##


// Changes made @ 200809241608 refering Mantis # 0000024: Generate Instant Message Type List
function tep_get_ezsignup_instant_message_type_list($name, $selected = '', $parameters = '') 
{
	$instant_message_type_array = array( array('id' => '', 'text' => PULL_DOWN_DEFAULT) );
	$instant_message_type_select_sql = "SELECT instant_message_type_id, instant_message_type_name 
										FROM " . TABLE_INSTANT_MESSAGE_TYPE . " ORDER BY instant_message_type_order";
	$instant_message_type_select_result = tep_db_query($instant_message_type_select_sql);
	
	while($instant_message_type_select_row = tep_db_fetch_array($instant_message_type_select_result))
	{
		$instant_message_type_array[] = array('id' => $instant_message_type_select_row['instant_message_type_id'], 
											  'text' => $instant_message_type_select_row['instant_message_type_name'] );
	}
	$instant_message_type_array[] = array('id' => '0', 'text' => 'Others');

	return tep_draw_pull_down_menu($name, $instant_message_type_array, $selected, $parameters);
}
// ##

/*
function tep_draw_buyback_brackets($products_id,&$brackets_array) {
	$skip = false;
	if (is_null($brackets_array))
		$skip=true;	
	else if(!is_array($brackets_array))
		$brackets_array = array();
	
	$buyback_sql = "select * from products,buyback,buyback_products,buyback_bracket where 
					buyback.products_id = '$products_id' and buyback.products_id = products.products_id and buyback.buyback_bracket_id = buyback_bracket.buyback_bracket_id and 
					buyback.products_id = buyback_products.products_id order by buyback_bracket.buyback_bracket_quantity asc;";
	
	// fetch the damn result for use later					
	$buyback_result = tep_db_query($buyback_sql);			
	$prev_bracket = $i=0;
	
	while ($row = mysql_fetch_array($buyback_result)) {							
		$class = (($i % 2)==0) ? "buybackbracketsEven" : "buybackbracketsOdd";
		
		if ($i==0) {
			$row1.= '<td class="'.$class.'"><b>'.TEXT_QTY.'</b></td>';
			$row2.= '<td class="'.$class.'"><b>'.TEXT_PRICE.'</b></td>';
		}
		
		$products_quantity =  (int)$row['products_quantity'];
		
		if ($i==0) {							
			$actual_bracket = (int)$row['buyback_bracket_quantity'] - (int)$row['products_quantity'];
			$actual_bracket = tep_buyback_round($actual_bracket);
			if (!$skip) {					
				$tmp = array($actual_bracket,$row['buyback_bracket_value'],(int)$row['buyback_bracket_mode']);
				array_push($brackets_array,$tmp);
			}
			$row1.= '<td align="center" class="'.$class.'">'.$actual_bracket.'</td>';
			$prev_bracket = (int)$row['buyback_bracket_quantity'];
		} else {
			$actual_bracket = (int)$row['buyback_bracket_quantity'] - $prev_bracket;
			$actual_bracket = tep_buyback_round($actual_bracket);
			if (!$skip) {
				$tmp = array($actual_bracket,$row['buyback_bracket_value'],(int)$row['buyback_bracket_mode']);
				array_push($brackets_array,$tmp);
			}
			$prev_bracket = (int)$row['buyback_bracket_quantity'];
			$row1.= '<td align="center" class="'.$class.'">'.tep_buyback_round($actual_bracket).'</td>';	
		}
		
		if ($row['buyback_bracket_mode'] == 0) {
			$bracket_value = (double)$row['buyback_bracket_value'];		
			$bracket_value = tep_round($bracket_value,3);	
		} else {
			$bracket_value = ((double)$row['buyback_bracket_value'] * (double)$row['products_price'])/100;
			$bracket_value = tep_round($bracket_value,3);
		}
		
		$row2.= '<td align="center" class="'.$class.'">'.$bracket_value.'</td>';
		
		$i++;
	}
	
	return '<table border="0" cellspacing="2" cellpadding="2"><tr>'.$row1.'</tr><tr>'.$row2.'</tr></table>';				
}
*/

if (!function_exists ('utf8_strlen')) {
	function utf8_strlen($str) {
		$count = 0;
		for ($i = 0; $i < strlen($str); $i++) {
	   		$value = ord($str[$i]);
	   		if ($value > 127) {
	       		if ($value >= 192 && $value <= 223)
	           		$i++;
	       		else if($value >= 224 && $value <= 239)
	           		$i = $i + 2;
	       		else if($value >= 240 && $value <= 247)
	           		$i = $i + 3;
	       		else	// Not a UTF-8 compatible string
	           		;
	       	}
	   		
	   		$count++;
	   	}
		return $count;
	}
}

function tep_draw_date_box($name, $from_date, $period, $default='') {
	$date_array = array();
	list($y, $m, $d) = explode('-', $from_date);
	$m = (int)$m;
	$d = (int)$d;
	
	$month_day = date('t', mktime(0, 0, 0, $m, $d, $y));
	
	$days_this_month = ($month_day - $d) + 1;
	$days_next_month = $period - $days_this_month;
	
	$current_month_days_loop = $d + ($period > $days_this_month ? $days_this_month : $period);
	for ($day_cnt=(int)$d; $day_cnt <= $current_month_days_loop; $day_cnt++) {
		$date_array[] = array('id' => $y . '-' . ((int)$m < 10 ? '0'.(int)$m : (int)$m) . '-' . ($day_cnt < 10 ? '0'.$day_cnt : $day_cnt), 'text' => $y . '-' . ((int)$m < 10 ? '0'.(int)$m : (int)$m) . '-' . ($day_cnt < 10 ? '0'.$day_cnt : $day_cnt));
	}
	
	while ($days_next_month > 0) {
		$m++;
		if ($m > 12) {
			$m = 1;
			$y++;
		}
		
		$month_day = date('t', mktime(0, 0, 0, $m, $d, $y));
		
		$month_total_days = ($days_next_month > $month_day) ? $month_day : $days_next_month;
		for ($day_cnt=1; $day_cnt <= $month_total_days; $day_cnt++) {
			$date_array[] = array('id' => $y . '-' . ((int)$m < 10 ? '0'.(int)$m : (int)$m) . '-' . ($day_cnt < 10 ? '0'.$day_cnt : $day_cnt), 'text' => $y . '-' . ((int)$m < 10 ? '0'.(int)$m : (int)$m) . '-' . ($day_cnt < 10 ? '0'.$day_cnt : $day_cnt));
		}
		
		$days_next_month -= $month_day;
	}
	
	return tep_draw_pull_down_menu($name, $date_array, $default, 'onChange=""');
}

function tep_draw_date_selection_boxes($month='', $day='', $year='', $display_lastest_value=false, $parameter = '') {
	$month_array = array( array('id' => '', 'text' => 'Month') );
	$month_array[] = array('id' => '01', 'text' => 'January');
	$month_array[] = array('id' => '02', 'text' => 'February');
	$month_array[] = array('id' => '03', 'text' => 'March');
	$month_array[] = array('id' => '04', 'text' => 'April');
	$month_array[] = array('id' => '05', 'text' => 'May');
	$month_array[] = array('id' => '06', 'text' => 'June');
	$month_array[] = array('id' => '07', 'text' => 'July');
	$month_array[] = array('id' => '08', 'text' => 'August');
	$month_array[] = array('id' => '09', 'text' => 'September');
	$month_array[] = array('id' => '10', 'text' => 'October');
	$month_array[] = array('id' => '11', 'text' => 'November');
	$month_array[] = array('id' => '12', 'text' => 'December');
	
	$day_array = array( array('id' => '', 'text' => 'Day') );
	for ($d_i=1; $d_i <= 31; $d_i++) {

		$day_str = sprintf('%02d', $d_i);
		$day_array[] = array('id' => $day_str, 'text' => $day_str);
	}
	
	$year_array = array( array('id' => '', 'text' => 'Year' ) );
	
	$year_cnt = ((date("Y")-1) - ENTRY_AGE_MAX);
	
	// Mantis # 0000024 @ 2000809231208 - Year of birth selection box
	for ($year_cnt; $year_cnt < date("Y"); $year_cnt++) {
		$year_array[] = array('id' => $year_cnt, 'text' => $year_cnt);
	}
	
	//$dob = tep_draw_pull_down_menu('dob_month', $month_array, $month, 'id="month"' . ' ' . $parameter) . "&nbsp;&nbsp;" . tep_draw_pull_down_menu('dob_day', $day_array, $day, 'id="day"'  . ' ' . $parameter) . "&nbsp; , &nbsp;" . tep_draw_input_field('dob_year', $year, 'size="4" maxlength="4"' . ' ' . $parameter, 'text', $display_lastest_value ? true : false);
	$dob = tep_draw_pull_down_menu('dob_month', $month_array, $month, 'id="month"' . ' ' . $parameter) . "&nbsp;&nbsp;" . tep_draw_pull_down_menu('dob_day', $day_array, $day, 'id="day"'  . ' ' . $parameter) . "&nbsp;&nbsp;" . tep_draw_pull_down_menu('dob_year', $year_array, $year, 'id="year"'  . ' ' . $parameter);
	return $dob;
}


function tep_display_category_path($path, $id, $id_type='catalog', $safe_quote = true) {
	$cat_display_level_setting = '';

	$cat_cfg_array = tep_get_cfg_setting($id, $id_type, 'BUYBACK_SUPPLIER_DISPLAY_LEVELS');
	$cat_display_level_setting = $cat_cfg_array['BUYBACK_SUPPLIER_DISPLAY_LEVELS'];
	
	if (!tep_not_null($cat_display_level_setting))	return $path;

	$level_array = array();

	$level_array = explode(',', $cat_display_level_setting);
	$path_array = explode('>', $path);
	$ret = array();

	for ($i=0; $i < count($level_array); $i++) {
		$level_array[$i] = trim($level_array[$i]);
		$level_array[$i] = (int)$level_array[$i];
		if ($level_array[$i] > 0) {
			$index = $level_array[$i]-1;
			if (trim($path_array[$index]) != '')	$ret[] = trim($path_array[$index]);
		}
	}

	return $safe_quote ? implode(" &gt; ",$ret) : implode(" > ",$ret);
}

function tep_submit_button($name='', $alt = '', $parameters = '', $css_class = 'generalBtn', $btn_active = true) {
   	$submit_html = '<input type="SUBMIT" value="'.$name.'" class="'.$css_class.'" ';

    if (tep_not_null($alt)) $submit_html .= ' title=" ' . tep_output_string($alt) . ' "';

    if (tep_not_null($parameters)) $submit_html .= ' ' . $parameters;

	$submit_html .= ($btn_active == true ? ' onMouseOver="this.className=\''.$css_class.'Over'.'\'"' : '') .' onMouseOut="this.className=\''.$css_class.'\'" ';

    $submit_html .= '>';

    return $submit_html;
}


// $buttontype
// 1. Submit Button
// 2. Hyperlink (Link & Javascript)

function tep_div_button($buttontype="2", $button_text = '',$button_url = '' , $parameters = '', $css_class = 'gray_button', $blockUI = false, $custom_js='') {
	//$extra_js = '';
	
	if ($blockUI == true) {
		$extra_js .= ' blockUI_disable();';
	}
	
    $submit_html = '<div class="'.$css_class.'" '.$parameters.'>';
		
	if ($buttontype == "1") {
		if (tep_not_null($custom_js)) {
			$submit_html .= '<a href="javascript:'.$custom_js.'">';
		} else {
			$submit_html .= '<a href="javascript:'.$extra_js.'document.'.$button_url.'.submit()">';
		}
	} elseif ($buttontype == "2") {
		$href_value = "";
		
		if (strpos($button_url , 'javascript:') !== FALSE) {
			$button_url = preg_replace('/(javascript:)/is', '', $button_url);
			$href_value = 'javascript:'.$extra_js.$button_url;
		} else {
			$href_value	= 'javascript:'.$extra_js.'document.location.href=\''.$button_url.'\'';		
		}
		
		$submit_html .= '<a href="'.$href_value.'">';
	}
	
	$submit_html .= '<span><font>'.$button_text.'</font></span></a></div>';

    return $submit_html;
}

// mbstring module related
function tep_mb_convert_encoding($str, $to_encoding, $from_encoding) {
	$do_encoding = true;
	if (extension_loaded('mbstring')) {
		$to_encoding = strtoupper($to_encoding);
		$from_encoding = strtoupper($from_encoding);
		
		if (tep_not_null($from_encoding)) {
			if (mb_detect_encoding($str) != $from_encoding) {
				$do_encoding = false;
			}
		} else {
			$from_encoding = mb_detect_encoding($str);
		}
		if ($do_encoding)	$str = mb_convert_encoding($str, $to_encoding, $from_encoding);
	}
	
	return $str;
}
?>