<?php
/*
  $Id: localization.php,v 1.4 2009/10/22 10:51:08 chan Exp $

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2003 osCommerce

  Released under the GNU General Public License
*/

  function quote_oanda_currency($code, $base = DEFAULT_CURRENCY) {
    $page = file('http://www.oanda.com/convert/fxdaily?value=1&redirected=1&exch=' . $code .  '&format=CSV&dest=Get+Table&sel_list=' . $base);

    $match = array();

    preg_match('/(.+),(\w{3}),([0-9.]+),([0-9.]+)/i', implode('', $page), $match);

    if (sizeof($match) > 0) {
      return $match[3];
    } else {
      return false;
    }
  }

  function quote_xe_currency($to, $from = DEFAULT_CURRENCY) {
    $page = file('http://www.xe.net/ucc/convert.cgi?Amount=1&From=' . $from . '&To=' . $to);

    $match = array();

    preg_match('/[0-9.]+\s*' . $from . '\s*=\s*([0-9.]+)\s*' . $to . '/', implode('', $page), $match);

    if (sizeof($match) > 0) {
      return $match[1];
    } else {
      return false;
    }
  }

function tep_user_balance($user_id, $user_role) {
	$user_balance_array = array();
	
	$store_account_balance_select_sql = "	SELECT store_account_balance_currency, store_account_balance_amount 
											FROM " . TABLE_STORE_ACCOUNT_BALANCE . " 
											WHERE user_id = '" . tep_db_input($user_id) . "' 
												AND user_role = '" . tep_db_input($user_role) . "' 
											ORDER BY store_account_balance_currency";
	$store_account_balance_result_sql = tep_db_query($store_account_balance_select_sql);
	
    while ($store_account_balance_row = tep_db_fetch_array($store_account_balance_result_sql)) {
    	$user_balance_array[$store_account_balance_row['store_account_balance_currency']] = $store_account_balance_row['store_account_balance_amount'];
    }
    
    return $user_balance_array;
}

function tep_get_ip_country_id($mail_notify = false) {
	require_once(DIR_WS_MODULES . 'anti_fraud/maxmind/db/geoip.inc');
	
	$country_id = '';
	
	if (file_exists(DIR_WS_MODULES . 'anti_fraud/maxmind/db/GeoIP.dat') && is_readable(DIR_WS_MODULES . 'anti_fraud/maxmind/db/GeoIP.dat')) {
		$gi = geoip_open(DIR_WS_MODULES . 'anti_fraud/maxmind/db/GeoIP.dat', GEOIP_STANDARD);
		$countries_iso_code_2 = geoip_country_code_by_addr($gi, tep_get_ip_address());
		
		if (tep_not_null($countries_iso_code_2)) {
			$country_info_array = tep_get_countries_info($countries_iso_code_2, 'countries_iso_code_2');
			
			if (count($country_info_array)) {
				$country_id = $country_info_array['id'];
			}
		}
	} else {
		if ($mail_notify) {
			$maxmind_geoip_email_array = tep_parse_email_string(MAXMIND_GEOIP_EMAIL_ADDRESS);
			
			for ($maxmind_geoip_email_array_cnt = 0; $maxmind_geoip_email_array_cnt < count($maxmind_geoip_email_array); $maxmind_geoip_email_array_cnt++) {
				tep_mail($maxmind_geoip_email_array[$maxmind_geoip_email_array_cnt]['name'], $maxmind_geoip_email_array[$maxmind_geoip_email_array_cnt]['email'], EMAIL_MAXMIND_SUBJECT, EMAIL_MAXMIND_CONTENT, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
			}
		}
	}
	
	return $country_id;
}

function tep_get_ip_country_info($ip_address) {
	require_once(DIR_WS_MODULES . 'anti_fraud/maxmind/db/geoip.inc');
	
	$country_info = array();
	
	if (file_exists(DIR_WS_MODULES . 'anti_fraud/maxmind/db/GeoIP.dat') && is_readable(DIR_WS_MODULES . 'anti_fraud/maxmind/db/GeoIP.dat')) {
		$gi = geoip_open(DIR_WS_MODULES . 'anti_fraud/maxmind/db/GeoIP.dat', GEOIP_STANDARD);
		$countries_iso_code_2 = geoip_country_code_by_addr($gi, $ip_address); //'************'
		
		if (tep_not_null($countries_iso_code_2)) {
			$country_info = tep_get_countries_info($countries_iso_code_2, 'countries_iso_code_2');
		}
	}
	
	return $country_info;
}

function tep_get_main_currency($countries_id) {
	$countries_currencies_id_select_sql = "	SELECT countries_currencies_id 
											FROM " . TABLE_COUNTRIES . " 
											WHERE countries_id = '" . tep_db_input($countries_id) . "'";
	$countries_currencies_id_result_sql = tep_db_query($countries_currencies_id_select_sql);
	$countries_currencies_id_row = tep_db_fetch_array($countries_currencies_id_result_sql);
	
	if (tep_not_null($countries_currencies_id_row['countries_currencies_id'])) {
		$code_select_sql = "	SELECT code 
								FROM " . TABLE_CURRENCIES . " 
								WHERE currencies_id = '" . tep_db_input($countries_currencies_id_row['countries_currencies_id']) . "'";
		$code_result_sql = tep_db_query($code_select_sql);
		$code_row = tep_db_fetch_array($code_result_sql);
		
		return $code_row['code'];
	} else {
		return (USE_DEFAULT_LANGUAGE_CURRENCY == 'true') ? LANGUAGE_CURRENCY : DEFAULT_CURRENCY;
	}
}
?>
