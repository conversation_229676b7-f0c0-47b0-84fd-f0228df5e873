<?
function tep_get_browser($user_agent_var) {
	if((ereg_dep("Nav", $user_agent_var)) || (ereg_dep("Gold", $user_agent_var)) || (ereg_dep("X11", $user_agent_var)) || (ereg_dep("Mozilla", $user_agent_var)) || (ereg_dep("Netscape", $user_agent_var)) AND (!ereg_dep("MSIE", $user_agent_var))) $browser = "Netscape";
	elseif(ereg_dep("MSIE", $user_agent_var)) $browser = "MSIE";
	elseif(ereg_dep("Lynx", $user_agent_var)) $browser = "Lynx";
	elseif(ereg_dep("Opera", $user_agent_var)) $browser = "Opera";
	elseif(ereg_dep("WebTV", $user_agent_var)) $browser = "WebTV";
	elseif(ereg_dep("Konqueror", $user_agent_var)) $browser = "Konqueror";
	elseif((eregi_dep("bot", $user_agent_var)) || (ereg_dep("Google", $user_agent_var)) || (ereg_dep("Slurp", $user_agent_var)) || (ereg_dep("Scooter", $user_agent_var)) || (eregi_dep("Spider", $user_agent_var)) || (eregi_dep("Infoseek", $user_agent_var))) $browser = "Bot";
	else $browser = "Other";
	
	return $browser;
}

function tep_get_os($user_agent_var) {
	if(ereg_dep("Win", $user_agent_var)) $os = "Windows";
	elseif((ereg_dep("Mac", $user_agent_var)) || (ereg_dep("PPC", $user_agent_var))) $os = "Mac";
	elseif(ereg_dep("Linux", $user_agent_var)) $os = "Linux";
	elseif(ereg_dep("FreeBSD", $user_agent_var)) $os = "FreeBSD";
	elseif(ereg_dep("SunOS", $user_agent_var)) $os = "SunOS";
	elseif(ereg_dep("IRIX", $user_agent_var)) $os = "IRIX";
	elseif(ereg_dep("BeOS", $user_agent_var)) $os = "BeOS";
	elseif(ereg_dep("OS/2", $user_agent_var)) $os = "OS/2";
	elseif(ereg_dep("AIX", $user_agent_var)) $os = "AIX";
	else $os = "Other";
	
	return $os;
}
?>