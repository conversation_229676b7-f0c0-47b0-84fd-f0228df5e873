<?php
/*
  $Id: whos_online.php,v 1.6 2010/12/21 07:51:07 weichen Exp $

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2003 osCommerce

  Released under the GNU General Public License
*/

function tep_update_whos_online() {
    global $customer_id;
	
    if (tep_session_is_registered('customer_id')) {
      	$wo_customer_id = $customer_id;
		
		$wo_full_name = $_SESSION["customer_first_name"].' '.$_SESSION["customer_last_name"];
    } else {
      	$wo_customer_id = '';
      	$wo_full_name = 'Guest';
    }
	
    $wo_session_id = tep_session_id();
    $wo_ip_address = tep_get_ip_address();
    $wo_last_page_url = getenv('REQUEST_URI');
	
    $current_time = time();
    $xx_mins_ago = ($current_time - 900);
	
	// remove entries that have expired
    tep_db_query("delete from " . TABLE_WHOS_ONLINE . " where time_last_click < '" . $xx_mins_ago . "'");
	
	$whois_select_sql = "SELECT session_id FROM " . TABLE_WHOS_ONLINE . " WHERE session_id = '" . tep_db_input($wo_session_id) . "'";
    $whois_result_sql = tep_db_query($whois_select_sql);
    
    if (tep_db_num_rows($whois_result_sql)) {
      	tep_db_query("update " . TABLE_WHOS_ONLINE . " set customer_id = '" . (int)$wo_customer_id . "', full_name = '" . tep_db_input($wo_full_name) . "', ip_address = '" . tep_db_input($wo_ip_address) . "', time_last_click = '" . tep_db_input($current_time) . "', last_page_url = '" . tep_db_input($wo_last_page_url) . "' where session_id = '" . tep_db_input($wo_session_id) . "'");
    } else {
      	tep_db_query("insert into " . TABLE_WHOS_ONLINE . " (customer_id, full_name, session_id, ip_address, time_entry, time_last_click, last_page_url, http_referer) values ('" . (int)$wo_customer_id . "', '" . tep_db_input($wo_full_name) . "', '" . tep_db_input($wo_session_id) . "', '" . tep_db_input($wo_ip_address) . "', '" . tep_db_input($current_time) . "', '" . tep_db_input($current_time) . "', '" . tep_db_input($wo_last_page_url) . "', '" . tep_db_input($_SERVER['HTTP_REFERER']) . "')");
    }
}
?>