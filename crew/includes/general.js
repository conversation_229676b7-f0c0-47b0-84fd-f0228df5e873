/*
  	$Id: general.js,v 1.8 2007/10/06 04:58:32 chan Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2003 osCommerce
	
  	Released under the GNU General Public License
*/

function SetFocus(TargetFormName)
{
	var target = 0;
  	if (TargetFormName != "") {
    	for (i=0; i<document.forms.length; i++) {
      		if (document.forms[i].name == TargetFormName) {
     	   		target = i;
        		break;
      		}
    	}
	}
	
  	var TargetForm = document.forms[target];
    
  	for (i=0; i<TargetForm.length; i++) {
    	if ( (TargetForm.elements[i].type != "image") && (TargetForm.elements[i].type != "hidden") && (TargetForm.elements[i].type != "reset") && (TargetForm.elements[i].type != "submit") ) {
      		TargetForm.elements[i].focus();
			
      		if ( (TargetForm.elements[i].type == "text") || (TargetForm.elements[i].type == "password") ) {
        		TargetForm.elements[i].select();
      		}
			
      		break;
    	}
  	}
}

function RemoveFormatString(TargetElement, FormatString)
{
	if (TargetElement.value == FormatString) {
    	TargetElement.value = "";
  	}
	
  	TargetElement.select();
}

function CheckDateRange(from, to)
{
	if (Date.parse(from.value) <= Date.parse(to.value)) {
    	return true;
  	} else {
    	return false;
  	}
}

function IsValidDate(DateToCheck, FormatString)
{
  	var strDateToCheck;
	var strDateToCheckArray;
  	var strFormatArray;
  	var strFormatString;
  	var strDay;
  	var strMonth;
  	var strYear;
  	var intday;
  	var intMonth;
  	var intYear;
  	var intDateSeparatorIdx = -1;
  	var intFormatSeparatorIdx = -1;
  	var strSeparatorArray = new Array("-"," ","/",".");
  	var strMonthArray = new Array("jan","feb","mar","apr","may","jun","jul","aug","sep","oct","nov","dec");
  	var intDaysArray = new Array(31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31);

  	strDateToCheck = DateToCheck.toLowerCase();
  	strFormatString = FormatString.toLowerCase();
	
  	if (strDateToCheck.length != strFormatString.length) {
    	return false;
  	}
	
  	for (i=0; i<strSeparatorArray.length; i++) {
    	if (strFormatString.indexOf(strSeparatorArray[i]) != -1) {
      		intFormatSeparatorIdx = i;
      		break;
    	}
  	}

  	for (i=0; i<strSeparatorArray.length; i++) {
    	if (strDateToCheck.indexOf(strSeparatorArray[i]) != -1) {
      		intDateSeparatorIdx = i;
      		break;
    	}
  	}
	
  	if (intDateSeparatorIdx != intFormatSeparatorIdx) {
    	return false;
  	}
	
  	if (intDateSeparatorIdx != -1) {
	    strFormatArray = strFormatString.split(strSeparatorArray[intFormatSeparatorIdx]);
    	if (strFormatArray.length != 3) {
      		return false;
    	}
		
    	strDateToCheckArray = strDateToCheck.split(strSeparatorArray[intDateSeparatorIdx]);
    	if (strDateToCheckArray.length != 3) {
      		return false;
    	}
		
    	for (i=0; i<strFormatArray.length; i++) {
      		if (strFormatArray[i] == 'mm' || strFormatArray[i] == 'mmm') {
        		strMonth = strDateToCheckArray[i];
      		}
			
      		if (strFormatArray[i] == 'dd') {
        		strDay = strDateToCheckArray[i];
      		}
			
      		if (strFormatArray[i] == 'yyyy') {
        		strYear = strDateToCheckArray[i];
      		}
    	}
  	} else {
    	if (FormatString.length > 7) {
      		if (strFormatString.indexOf('mmm') == -1) {
        		strMonth = strDateToCheck.substring(strFormatString.indexOf('mm'), 2);
      		} else {
        		strMonth = strDateToCheck.substring(strFormatString.indexOf('mmm'), 3);
      		}
			
      		strDay = strDateToCheck.substring(strFormatString.indexOf('dd'), 2);
      		strYear = strDateToCheck.substring(strFormatString.indexOf('yyyy'), 2);
    	} else {
      		return false;
    	}
	}

  	if (strYear.length != 4) {
    	return false;
  	}

  	intday = parseInt(strDay, 10);
  	if (isNaN(intday)) {
    	return false;
  	}
  	if (intday < 1) {
    	return false;
  	}
	
  	intMonth = parseInt(strMonth, 10);
  	if (isNaN(intMonth)) {
    	for (i=0; i<strMonthArray.length; i++) {
      		if (strMonth == strMonthArray[i]) {
        		intMonth = i+1;
        		break;
      		}
    	}
    	if (isNaN(intMonth)) {
      		return false;
    	}
  	}
  	if (intMonth > 12 || intMonth < 1) {
    	return false;
  	}
	
  	intYear = parseInt(strYear, 10);
  	if (isNaN(intYear)) {
    	return false;
  	}
  	if (IsLeapYear(intYear) == true) {
    	intDaysArray[1] = 29;
  	}
	
  	if (intday > intDaysArray[intMonth - 1]) {
    	return false;
  	}
  	
  	return true;
}

function IsLeapYear(intYear)
{
	if (intYear % 100 == 0) {
    	if (intYear % 400 == 0) {
      		return true;
    	}
  	} else {
    	if ((intYear % 4) == 0) {
      		return true;
    	}
  	}
	
  	return false;
}

function setCheckboxes(the_form, control_field, check_item)
{
	var elts      = (typeof(document.forms[the_form].elements[check_item+'[]']) != 'undefined')
    	          ? document.forms[the_form].elements[check_item+'[]']
        	      : "";
	var elts_cnt  = (typeof(elts.length) != 'undefined')
    	          ? elts.length
        	      : 0;

	if (document.getElementById(control_field).value > 0) {	// if currently selected all
		var do_check = 0;
		document.getElementById(control_field).value = 0;
	} else { // if currently not selected all
		var do_check = 1;
		document.getElementById(control_field).value = 1;
	}
	if (elts_cnt) {
    	for (var i = 0; i < elts_cnt; i++) {
        	elts[i].checked = do_check;
    	} // end for
	} else if (elts!='') {
       	elts.checked = do_check;
	} // end if... else
    return true;
} // end of the 'setCheckboxes()' function

function checkForSelection(the_form, check_item) {
	var selectionMade = false;
	
	var elts      = (typeof(document.forms[the_form].elements[check_item]) != 'undefined')
    	          ? document.forms[the_form].elements[check_item]
        	      : "";
	var elts_cnt  = (typeof(elts.length) != 'undefined')
    	          ? elts.length
        	      : 0;
	
	if (elts_cnt) {
    	for (var i = 0; i < elts_cnt; i++) {
        	if (elts[i].checked == true) {
        		selectionMade = true;
        		break;
        	}
    	} // end for
	} else if (elts!='') {
       	if (elts.checked == true) {
       		selectionMade = true;
       	}
	} // end if... else
    return selectionMade;
}

function validateInteger( strValue ) {
	var objRegExp  = /(^\d\d*$)/;
	
	//check for integer characters
	return objRegExp.test(strValue);
}

function trim_str(strValue) {
	strValue += '';
	return ltrim_str(rtrim_str(strValue));
}

function ltrim_str(strValue) {
	var LTRIMrgExp = /^\s */;
	return strValue.replace(LTRIMrgExp, '');
}

function rtrim_str(strValue) {
	var RTRIMrgExp = /\s *$/;
	return strValue.replace(RTRIMrgExp, '');
}

function DOMCall(name) {
	if (document.getElementById)
    	return document.getElementById(name);
	else if (document.all)
		return document.all[name];
	else if (document.layers)
		return document.layers[name];
}

function currency(anynum, sym_left, sym_right, dec) {
	var dec_pt = dec != null ? dec : 3;
	var retval = currency_display(anynum, dec_pt);
	
	if (sym_left != null && sym_left != '') {
		return sym_left + retval;
	} else {
		return retval + sym_right;
	}
}

function currency_display(number, places) {
	var result = Math.round(number*Math.pow(10,places))/Math.pow(10,places);
	var str_res = result.toString();
		
	if (str_res.lastIndexOf('.') < 0) {
		var padding_right_zero = '';
		
		for (var i=0; i < places; i++) {
			padding_right_zero += '0';
		}
		
		if (padding_right_zero.length > 0) {
			str_res = str_res + '.' + padding_right_zero;
		}
	}
	
	dStr = str_res.substr(0, str_res.indexOf("."));
	dNum = dStr - 0;
	pStr = str_res.substr(str_res.indexOf("."));
	while (pStr.length <= places) { pStr += '0'; }
	
	if (dNum >= 1000) {
		dLen = dStr.length;
		dStr = parseInt(""+(dNum/1000))+","+dStr.substring(dLen-3,dLen);
	}
	
	if (dNum >= 1000000) {
		dLen = dStr.length;
		dStr = parseInt(""+(dNum/1000000))+","+dStr.substring(dLen-7,dLen);
	}
	
	result = dStr + pStr;
	
	return result;
}

function URLEncode(plain_text) {
	// The Javascript escape and unescape functions do not correspond
	// with what browsers actually do...
	var SAFECHARS = "0123456789" +					// Numeric
					"ABCDEFGHIJKLMNOPQRSTUVWXYZ" +	// Alphabetic
					"abcdefghijklmnopqrstuvwxyz" +
					"-_.!~*'()";					// RFC2396 Mark characters
	var HEX = "0123456789ABCDEF";

	var plaintext = plain_text;
	var encoded = "";
	for (var i = 0; i < plaintext.length; i++ ) {
		var ch = plaintext.charAt(i);
	    if (ch == " ") {
		    encoded += "+";				// x-www-urlencoded, rather than %20
		} else if (SAFECHARS.indexOf(ch) != -1) {
		    encoded += ch;
		} else {
		    var charCode = ch.charCodeAt(0);
			if (charCode > 255) {
			    alert( "Unicode Character '"
                        + ch
                        + "' cannot be encoded using standard URL encoding.\n" +
				          "(URL encoding only supports 8-bit characters.)\n" +
						  "A space (+) will be substituted." );
				encoded += "+";
			} else {
				encoded += "%";
				encoded += HEX.charAt((charCode >> 4) & 0xF);
				encoded += HEX.charAt(charCode & 0xF);
			}
		}
	} // for

	return encoded;
}