<?php

if (tep_not_null(GOOGLE_TAG_MANAGER_ID)) {
    //https://developers.google.com/analytics/devguides/collection/upgrade/reference/gtm
    $gtm_array = array(
        'customerID' => isset($_SESSION['customer_id']) ? $_SESSION['customer_id'] : 0,
        'customerGroup' => isset($_SESSION['GA_CUSTOMERS_GROUPS_NAME']) ? $_SESSION['GA_CUSTOMERS_GROUPS_NAME'] : '-'
    );

    if ($content == CONTENT_CHECKOUT_SUCCESS) {
        if (is_array($orders)) {
            if (is_object($order)) {
                $payment_methods_parent_title = '';
                $total_product_count = count($order->products);
                
                if ($order->info['payment_methods_parent_id'] > 0) {
                    $pg_sql = "	SELECT payment_methods_title
                                FROM " . TABLE_PAYMENT_METHODS . "
                                WHERE payment_methods_id = '" . $order->info['payment_methods_parent_id'] . "'";
                    $pg_query = tep_db_query($pg_sql);
                    if ($pg_row = tep_db_fetch_array($pg_query)) {
                        $payment_methods_parent_title = $pg_row['payment_methods_title'];
                    }
                }
                
                // https://support.google.com/tagmanager/answer/3002596
                $gtm_array['event'] = 'trackTrans';
                $gtm_array['transactionId'] = $order->order_id;
                $gtm_array['transactionTotal'] = $order->info['total_value'];
                
                // additional info (Optional)
                $gtm_array['paymentGatewayID'] = $order->info['payment_methods_parent_id'];
                $gtm_array['paymentGateway'] = $payment_methods_parent_title;
                $gtm_array['paymentMethodID'] = $order->info['payment_methods_id'];
                $gtm_array['paymentMethod'] = $order->info['payment_method'];
                $gtm_array['transactionCity'] = $order->customer['city'];
                $gtm_array['transactionState'] = $order->customer['state'];
                $gtm_array['transactionCountry'] = $order->customer['country'];
                
                for ($p_cnt=0; $p_cnt < $total_product_count; $p_cnt++) {
                    $gtm_array['transactionProducts'][] = array(
                        'sku' => $order->products[$p_cnt]['id'],
                        'name' => $order->products[$p_cnt]['name'],
                        'category' => $order->products[$p_cnt]['name'],
                        'price' => $order->products[$p_cnt]['final_price'],
                        'quantity' => $order->products[$p_cnt]['qty']
                    );
                }
            }
        }
    }
?>
    <!-- Google Tag Manager -->
    <script>var dataLayer = <?php echo json_encode(array($gtm_array)); ?>;</script>
    <noscript><iframe src="//www.googletagmanager.com/ns.html?id=<?php echo GOOGLE_TAG_MANAGER_ID; ?>" height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
    <script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src='//www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);})(window,document,'script','dataLayer','<?php echo GOOGLE_TAG_MANAGER_ID; ?>');</script>
    <!-- End Google Tag Manager -->
<?php
}

if (tep_not_null(GOOGLE_ANALYTICS_ACCOUNT_ID)) { // Google Analytics Tracking 
?>
    <!-- GA :: Initial Referrer [START] -->
    <script type="text/javascript" src="<?=DIR_WS_JAVASCRIPT?>initial_referrer_ga.js"></script>
    <script type="text/javascript">
    <!--
        var firstTracker = _roit._getTracker("<?=GOOGLE_ANALYTICS_ACCOUNT_ID?>");
        if (document.cookie.match("(^|;\\s)__roia=")) {
            firstTracker._setReferrerOverride();
            firstTracker._setCampNameKey();
            firstTracker._setCampMediumKey();
            firstTracker._setCampSourceKey();
            firstTracker._setCampTermKey();
            firstTracker._setCampContentKey();
            firstTracker._setCampCIdKey();
        }
        firstTracker._trackPageview();
    //-->
    </script>
    <!-- GA :: Initial Referrer [END] -->


    <script type="text/javascript">
    <!--
        var gaJsHost = (("https:" == document.location.protocol) ? "https://ssl." : "http://www.");
        document.write(unescape("%3Cscript src='" + gaJsHost + "google-analytics.com/ga.js' type='text/javascript'%3E%3C/script%3E"));
    //-->
    </script>

    <script type="text/javascript">
    <!--
        var pageTracker = _gat._getTracker("<?=GOOGLE_ANALYTICS_ACCOUNT_ID?>");
    //-->
    </script>

    <!-- GA :: Regional Search Engine [START] -->
    <script src="http://www.advanced-web-metrics.com/scripts/custom_se-ga.js" type="text/javascript"></script>
    <!-- GA :: Regional Search Engine [END] -->

    <script type="text/javascript">
    <!--
        pageTracker._initData();
        pageTracker._setReferrerOverride(document.referrer.toString());
    //-->
    </script>

    <!-- GA :: Classify Your Site Visitor [START] -->
<?		if (isset($_SESSION['GA_CUSTOMERS_GROUPS_NAME'])) { ?>
        <script type="text/javascript">
        <!--
            pageTracker._setCustomVar(
                1,									// This custom var is set to slot #1
                "Customers Group",					// The top-level name for your online content categories
                "<?=$_SESSION['GA_CUSTOMERS_GROUPS_NAME'];?>",	// Sets the value of "Section" to "Life & Style" for this particular aricle
                3									// Sets the scope to page-level
            );
        //-->
        </script>
<?		} ?>
    <!-- GA :: Classify Your Site Visitor [END] -->

    <script type="text/javascript">
    <!--
        pageTracker._trackPageview();
    //-->
    </script>
<?php
    if ($content == CONTENT_CHECKOUT_SUCCESS) {
        if (file_exists(DIR_FS_JAVASCRIPT . 'google_analytics.js.php')) { include_once (DIR_FS_JAVASCRIPT . 'google_analytics.js.php'); }
    }
}
?>