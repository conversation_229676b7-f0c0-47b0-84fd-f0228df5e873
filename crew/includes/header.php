<?
if (!is_object($lng)) {
	include_once(DIR_WS_CLASSES . 'language.php');
	$lng = new language();
}

define('MENU_COLUMN', 4);

if (ENABLE_SSL == 'true') {
	$path = DIR_WS_IMAGES;
	$lang_btn_path = HTTPS_SERVER . DIR_WS_HTTPS_CATALOG . DIR_WS_LANGUAGES . $language . '/images/buttons/';
} else {
	$path = DIR_WS_IMAGES;
	$lang_btn_path = HTTP_SERVER . DIR_WS_HTTPS_CATALOG . DIR_WS_LANGUAGES . $language . '/images/buttons/';
}

$filename = basename($_SERVER['SCRIPT_FILENAME']);

// Category Where statement

$category_wehere_statement = " 0 ";
if (count($zone_configuration_array[1]->zone_categories_id)) {
	$category_where_statement = " ( c.categories_id IN ('" . implode("', '", $zone_configuration_array[1]->zone_categories_id) . "') ) ";
}

// Game Product Object

	$game_product_available = array();

	$categories_select_sql = " 	SELECT c.categories_id, cd.categories_name 
								FROM " . TABLE_CATEGORIES . " AS c 
								INNER JOIN " . TABLE_CATEGORIES_DESCRIPTION . " AS cd 
									ON (c.categories_id = cd.categories_id) 
								INNER JOIN " . TABLE_CATEGORIES_GROUPS . " AS cg 
									ON (c.categories_id=cg.categories_id) 
								WHERE c.parent_id = 0 
									AND c.categories_status = 1 
									AND cd.language_id = '" . (int)$languages_id . "' 
									AND ((cg.groups_id = '".$customers_groups_id. "') OR (cg.groups_id = 0)) 
									AND $category_where_statement
								ORDER BY cd.categories_name";

	$categories_result_sql = tep_db_query($categories_select_sql);
	while ($categories_row = tep_db_fetch_array($categories_result_sql)) {
		$category_array = array($categories_row['categories_id']);
		tep_get_subcategories($category_array, $categories_row['categories_id'], 1);
		
		for ($cpt_id_cnt = 0; $cpt_id_cnt <= 2; $cpt_id_cnt++) {
			$product_type_where_sql_str = ' 1 ';
			$limit_sql_str = '';
			
			if ($cpt_id_cnt == 0) {
				$product_type_where_sql_str =  " p.custom_products_type_id = '0'";
			} else if ($cpt_id_cnt == 1) {
				$product_type_where_sql_str =  " p.custom_products_type_id = '1'";
				$limit_sql_str = ' LIMIT 1 ';
			} else if ($cpt_id_cnt == 2) {
				$product_type_where_sql_str =  " p.custom_products_type_id IN (0, 2)";
			}
			
			$product_id_select_sql = "	SELECT p.products_id, p.products_bundle, p.products_bundle_dynamic, p.custom_products_type_id 
										FROM " . TABLE_PRODUCTS . " AS p
										INNER JOIN " . TABLE_PRODUCTS_TO_CATEGORIES . " AS ptc 
											ON (p.products_id = ptc.products_id) 
										WHERE ptc.categories_id IN ('" . implode("', '", $category_array) . "') 
											AND " . $product_type_where_sql_str . "
											AND p.products_status = 1 
											AND p.products_display = 1 
										" . $limit_sql_str;
			$product_id_result_sql = tep_db_query($product_id_select_sql);
			if (tep_db_num_rows($product_id_result_sql)) {
				if ($cpt_id_cnt == 1) {
					$game_product_available[$cpt_id_cnt][] = array('id' => $categories_row['categories_id'], 'name' => $categories_row['categories_name']);
				} else {
					while ($product_id_row = tep_db_fetch_array($product_id_result_sql)) {
						if ($product_id_row['products_bundle'] == '' && $product_id_row['products_bundle_dynamic'] == '') {	// This is SINGLE Product
							if ($product_id_row['custom_products_type_id'] != $cpt_id_cnt) {
								continue;
							} else {
								$game_product_available[$cpt_id_cnt][] = array('id' => $categories_row['categories_id'], 'name' => $categories_row['categories_name']);
								break;
							}
						} else {	// This is BUNDLE
							if (tep_get_custom_product_type($product_id_row['products_id']) != $cpt_id_cnt) {
								continue;
							} else {
								$game_product_available[$cpt_id_cnt][] = array('id' => $categories_row['categories_id'], 'name' => $categories_row['categories_name']);
								break;
							}
						}
					}
				}
			}
		}
	}

// Get Unique Games

$game_product_type_tmp = array();
$unique_game_array = array();
$game_array_flag = array();
unset($game_array_flag);

foreach ($game_product_available as $game_tpl => $game_product_type) {
	foreach ($game_product_type as $game_product_row) {
		$temp_id = $game_product_row['id'];
		$game_product_type_tmp[$temp_id][] = $game_tpl;

		if (empty($game_array_flag["$temp_id"])){
			$unique_game_array[] = $game_product_row;
			$game_array_flag["$temp_id"] = 1;
		}
	}
}

foreach ($unique_game_array as $unique_game_row_id => $unique_game_row) {
	$temp_id = $unique_game_row['id'];
	$unique_game_array[$unique_game_row_id]['product_type'] = $game_product_type_tmp[$temp_id];
}

// Sort unique_game_array
if ($zone_configuration_array[1]->zone_sort_type == "define") {
	foreach ($zone_configuration_array[1]->zone_sort_order as $zone_sort_order_row) {
		foreach ($unique_game_array as $unique_game_array_row) {
			if ($zone_sort_order_row == $unique_game_array_row['id']) {
				$new_unique_game_array[] = $unique_game_array_row;
			}
		}
	}
	$unique_game_array = $new_unique_game_array;
}

// Tab Navigation Data Object
if (isset($_GET['menu_id'])) {
	$select_menu_id = $_GET['menu_id'];
} else {
	$tab_page_select_sql = "	SELECT cms_menu_id 
								FROM " . TABLE_CMS_MENU_TAB_PAGE . "
								WHERE cms_linked_filename = '" . tep_db_input($filename) . "'";
	$tab_page_result_sql = tep_db_query($tab_page_select_sql);
	if ($tab_page_row = tep_db_fetch_array($tab_page_result_sql)) {
		$select_menu_id = $tab_page_row['cms_menu_id'];
	}
}

$default_language_info = $lng->get_language_info(DEFAULT_LANGUAGE);
$navigationtab_obj = array();

$tab_select_sql = "	SELECT cms_menu_id, cms_menu_content_type, cms_menu_url, cms_menu_seo_alias, cms_menu_type
					FROM " . TABLE_CMS_MENU . " 
					WHERE (cms_menu_type = '3' 
						OR cms_menu_type = '4' 
						OR cms_menu_type = '5')
						AND cms_menu_status = '1' 
					ORDER BY cms_menu_sort_order ASC";
$tab_result_sql = tep_db_query($tab_select_sql);
while ($tab_row = tep_db_fetch_array($tab_result_sql)) {
	$cms_tab_content_array = array();
	$cms_menu_select_sql = "SELECT cms_menu_lang_setting_key, cms_menu_lang_setting_key_value 
							FROM " . TABLE_CMS_MENU_VALUE . " 
							WHERE cms_menu_id = '" . tep_db_input($tab_row['cms_menu_id']) . "' 
								AND cms_menu_lang_setting_key_value <> '' 
								AND (IF(languages_id='".tep_db_input($languages_id)."', 1, languages_id='".tep_db_input($default_language_info['id'])."'))";
	$cms_menu_result_sql = tep_db_query($cms_menu_select_sql);
	while ($cms_menu_row = tep_db_fetch_array($cms_menu_result_sql)) {
		$cms_tab_content_array[$cms_menu_row['cms_menu_lang_setting_key']] = $cms_menu_row['cms_menu_lang_setting_key_value'];
	}

	if ($tab_row['cms_menu_content_type'] == "url") {
		$tempurl = $tab_row['cms_menu_url'];
	} else {
		if ($tab_row['cms_menu_seo_alias']) {
			$tempurl = tep_href_link($tab_row['cms_menu_seo_alias']."-m-".$tab_row['cms_menu_id'].".ogm", '');
		} else {
			$tempurl = tep_href_link( FILENAME_CMS_CONTENT , 'menu_id='.$tab_row['cms_menu_id']);
		}
	}
	
	$menu_obj[] = array (	'title' => $cms_tab_content_array['menu_title'],
							'url' => $tempurl,
							'menuid' => $tab_row['cms_menu_id'],
							'menu_type' => $tab_row['cms_menu_type']
						);
}

// Geo Zones Object

$country_select_sql = "	SELECT countries_id,countries_name
							FROM " . TABLE_COUNTRIES . "
							ORDER BY countries_name ASC";
$country_result_sql = tep_db_query($country_select_sql);
while ($country_row = tep_db_fetch_array($country_result_sql)) {
	$country_obj[] = array (	'id' => $country_row['countries_id'],
							'title' => $country_row['countries_name']
						);

	if ($country == $country_row['countries_id'])
		$selected_country = $country_row['countries_name'];
}

?>
	<div id="pagehead">
		<div id="pagehead-logo"><img src="images/ogm-logo.gif"></div>
		<div id="pagehead_nav">
			<table cellpadding=0 cellspacing=0 border=0 width=0 border=0 width=100%>
			<tr><td align=right>
				<div id="pagehead_nav_text">
<?php
foreach ($menu_obj as $menu_row) {
	if ($menu_row['menu_type'] == "3") {
?>
					<div style="float:left"><a href="<?php echo $menu_row['url']; ?>"><?php echo $menu_row['title']; ?></a>&nbsp;&nbsp;|&nbsp;&nbsp;</div>
<?php
	}
}

?>
					<div style="float:left; position:relative;" id="select_span_zones" onclick="javascript:show_zones()">
						<div class="select_box_zones" id="select_box_zones">
<?php
							$first_entry_flag = true;
							foreach ($country_obj as $country_entry)
							{
								if ($first_entry_flag == true) {
									$first_entry_flag = false;
?>
							<div><a href="/index.php?country=<?=$country_entry['id'] ?>" class="select_zones_first"><?=$country_entry['title'] ?></a></div>
<?php
								}
								else {
?>
							<div><a href="/index.php?country=<?=$country_entry['id'] ?>" class="select_zones"><?=$country_entry['title'] ?></a></div>
<?php
								}
							}
?>
						</div>
						<a href="javascript:show_zones();" id="select_link_zones" onclick="javascript:show_zones()"><?=$selected_country ?></a>&nbsp;&nbsp;<img src="images/OGM2008/icon/icon-expand-b.gif" id="select_icon_zones" onclick="javascript:show_zones()">
					</div>
				</div>
			</td></tr>
			<tr><td align=right>
				<div id="pagehead_nav_tab">
					<ul class="nav_tab">
<?php
$itemcount = 0;

foreach ($menu_obj as $menu_row) {
	if ($menu_row['menu_type'] == "4") {
		$itemcount++;
		
		if (substr(basename($PHP_SELF), 0, 7) == "buyback")
			$li_class = "tab_notselected";
		else
			$li_class = "tab_selected";
		
		if ($itemcount == 1) {
?>
						<li class="<?=$li_class ?>" style="position:relative">
							<iframe class="two" frameborder=0 scrolling=no id="frame2"></iframe>
							<div class="shopping_cart_icon" onClick="javascript:show_mini_cart()"></div>
							<div class="mini_shopping_cart" id="mini_shopping_cart">
							<div class="mini_shopping_cart_border">
							<div class="mini_shopping_cart_border_t">
							<div class="mini_shopping_cart_border_l">
							<div class="mini_shopping_cart_border_r">
							<div class="mini_shopping_cart_border_b">
							<div class="mini_shopping_cart_border_br">
							<div class="mini_shopping_cart_border_bl">
							<div class="mini_shopping_cart_border_tl">
							<div class="mini_shopping_cart_border_tr">
								<table cellpadding=5 cellspacing=0 border=0 width=100%><tr><td align=center>
								<table cellpadding=3 cellspacing=0 border=0>
<?php

$total_cart_item = "0";

$custom_prod_index = array();
if ($cart->count_contents() > 0) {
	$products = $cart->get_products();

	$total_cart_item = count($products);

	for ($i=0, $n=sizeof($products); $i<$n; $i++) {
	  	$prod_bd_query = tep_db_query("select products_bundle, products_bundle_dynamic from " . TABLE_PRODUCTS . " where products_id = " . $products[$i]['id'] . " order by products_id");
	  	$prod_bd = tep_db_fetch_array($prod_bd_query);
	  	$prod_bundle = $prod_bd['products_bundle'];
  	  	$prod_bundle_dynamic = $prod_bd['products_bundle_dynamic'];

  	  	$bd = ($prod_bundle_dynamic == 'yes') ? 'y' : 'x';
		
		if ((int)$products[$i]['custom_products_type_id'] > 0) {
      		$custom_prod_index[$products[$i]['id']] = (int)$custom_prod_index[$products[$i]['id']] + 1;
  	  		$temp_qty = $products[$i]['quantity'];
  	  	} else {
      		$temp_qty = $products[$i]['quantity'];
      	}
      	
      	$temp_productname = $products[$i]['name'];
      	$temp_productprice = $products[$i]['price'];
      	$temp_productsubtotal = (int) $temp_qty * $temp_productprice;

		if ($i == 0)
			$temp_class = "mini_cart_product_row_first";
		else
			$temp_class = "mini_cart_product_row";
		
      	if ((tep_session_is_registered('new_products_id_in_cart')) && ($new_products_id_in_cart == $products[$i]['id'])) {
        	tep_session_unregister('new_products_id_in_cart');
      	}

		if ($i > 0) {
?>
									<tr><td align=center><img src="images/OGM2008/mini_cart_dotted_line.gif"></td></tr>
<?php
		}
?>
									<tr class="<?php echo $temp_class; ?>">
										<td valign=top><?php echo "<b>".$temp_qty." X </b>".$temp_productname; ?></td>
									</tr>
<?php
    }
} else {
?>
									<tr class="mini_cart_product_row_first">
										<td><?php echo BOX_SHOPPING_CART_EMPTY; ?></td>
									</tr>
<?php
}
?>
									<tr><td colspan=2 align=center><img src="images/OGM2008/mini_cart_solid_line.gif"></td></tr>
									<tr><td colspan=2 align=center>
									<table cellpadd=0 cellspacing=0 border=0 width=100%>
										<tr>
											<td><b><?php echo $total_cart_item; ?></b> <?=TEXT_ITEMS_IN_MY_CART ?></td>
											<td align=right><div style="font-size:14;"><?=TEXT_TOTAL ?> <b><?php echo $currencies->format($cart->show_total(), false); ?></b></div></td>
										</tr>
									</table>
									</td></tr>
									<tr><td colspan=2 align=center>
										<table cellpadding=0 cellspacing=0 border=0><tr><td>
											<div class="green_button" style="padding:10px 2px"><a href="<?php echo tep_href_link(FILENAME_SHOPPING_CART, '', 'SSL'); ?>"><span><font style="color:#FFFFFF"><?=HEADER_TITLE_CART_CONTENTS ?></font></span></a></div>
											<div class="red_button" style="padding:10px 2px"><a href="<?php echo tep_href_link(FILENAME_CHECKOUT_SHIPPING, '', 'SSL'); ?>"><span><font style="color:#FFFFFF"><?=IMAGE_BUTTON_CHECKOUT ?></font></span></a></div>
										</td></tr></table>
									</td></tr>
								</table>
								</td></tr></table>
							</div></div></div></div></div></div></div></div></div></div>
							<div id="close_cart_icon" class="close_cart_icon" onclick="javascript:show_mini_cart()"></div>
							<a href="<?php echo $menu_row['url']; ?>"><span><font style="text-align: center"><?php echo $menu_row['title']; ?></font></span></a>
						</li>
<?php
		}
		else if ($itemcount == 2) {

		if (substr(basename($PHP_SELF), 0, 7) == "buyback")
			$li_class = "tab_selected";
		else
			$li_class = "tab_notselected";
?>
						<li class="<?=$li_class ?>"><a href="<?php echo $menu_row['url']; ?>"><span><font style="text-align: center"><?php echo $menu_row['title']; ?></font></span></a></li>
<?php
		}
		else {
		}
	}
}
?>
						<li class="tab_green_l">
							<a href="javascript:show_mini_cart()"><span><font>
							<div id="cart_item_no"><b><?php echo $total_cart_item; ?></b> <?=TEXT_ITEMS_IN_MY_CART ?></div>
							</font></span></a>
						</li>
						<li class="tab_green_m"><a href="javascript:show_mini_cart()"><span><font>|</font></span></a></li>
						<li class="tab_green_r">
							<a id="checkout_link" href="<?php echo tep_href_link(FILENAME_CHECKOUT_SHIPPING, '', 'SSL'); ?>"><span><font id="checkout_word"><?=HEADER_TITLE_CHECKOUT ?></font></span></a>
						</li>
					</ul>
				</div>
			</td></tr>
			</table>
		</div>
	</div>
	<div style="clear:both;">
		<div class="gray_box_bg" style="width:100%"><div class="gray_box_bl" style="width:100%"><div class="gray_box_br" style="width:100%"><div class="gray_box_tl" style="width:100%"><div class="gray_box_tr" style="width:100%">
			<table cellpadding=0 cellspacing=0 border=0 width=100%><tr><td align=center>
			<table cellpadding=0 cellspacing=0 border=0 width=100%><tr><td align=center>
				<ul class="nav_node">
<?php

$itemcount = 0;

foreach ($menu_obj as $menu_row) {
	if ($menu_row['menu_type'] == "5") {
		$itemcount++;
	}
}

$lastitemcount = $itemcount;
$itemcount = 0;

$li_item_width = (int) (99/$lastitemcount);
$li_1st_item_width = 99 - ($lastitemcount - 1) * $li_item_width;

foreach ($menu_obj as $menu_row) {
	if ($menu_row['menu_type'] == "5") {
		$itemcount++;

		if ($itemcount == 1) {
			if (isset($tpl) && $pagetype != "gi")
				$printclass = "notselected_first";
			else
				$printclass = "selected_first";
?>
					<li class="<?=$printclass ?>" style="width:<?php echo $li_1st_item_width; ?>%">
						<iframe class="one" frameborder=0 scrolling=no id="frame1"></iframe>
						<div class="top_50_games_icon" id="top_50_games_icon"></div>
						<div class="top_50_games_window" id="top_50_games_window">
						<div class="top_50_games_border">
						<div class="top_50_games_border_t">
						<div class="top_50_games_border_l">
						<div class="top_50_games_border_r">
						<div class="top_50_games_border_b">
						<div class="top_50_games_border_br">
						<div class="top_50_games_border_bl">
						<div class="top_50_games_border_tl">
						<div class="top_50_games_border_tr">
							<table cellpadding=7 cellspacing=0 border=0 width=100%><tr><td align=center>
							<table cellpadding=3 cellspacing=0 border=0 width=100%>
<?php
			$counter_count = 0;
			foreach ($unique_game_array as $unique_game_row) {
				if ($counter_count%4 == 0)
					echo "								<tr>";

				$counter_count++;
				echo '									<td valign=top width=25%>'.$counter_count.') <div style="display: inline; cursor: pointer" onclick="javascript:document.location.href=\''.tep_href_link(str_replace(' ', '-', $unique_game_row['name']).'-gi-'.$unique_game_row['id'].'.ogm', '', 'NONSSL').'\'">'.$unique_game_row['name'].'</div></td>';

				if ($counter_count%4 == 0)
					echo "								</tr>";
				
				if ($counter_count >= 50) {
					break;
				}
			}
			
			if ($counter_count%4 == 1)
				echo "								<td width=25%><br></td><td width=25%><br></td><td width=25%><br></td></tr>";
			else if ($counter_count%4 == 2)
				echo "								<td width=25%><br></td><td width=25%><br></td></tr>";
			else if ($counter_count%4 == 3)
				echo "								<td width=25%><br></td></tr>";
?>
								<tr>
									<td colspan=4 align=right><div style="display: inline; cursor: pointer" onclick="javascript:document.location.href='<?=FILENAME_SEARCH_ALL_GAMES ?>'"><b>View All Games</b></div></td>
								</tr>
							</table>
							</td></tr></table>
						</div></div></div></div></div></div></div></div></div></div>
						<a id="top_50_games_link" href="javascript:show_top_50()"><span><font><?php echo $menu_row['title']; ?> &nbsp;&nbsp;<img src="/images/OGM2008/icon/icon-expand-w.gif" border=0 id="topgame_icon"></font></span></a>
						<input type=hidden name="top_50_games_flag" value="" id="top_50_games_flag">
					</li>
<?php
		}
		else if ($itemcount == $lastitemcount) {
			if (isset($tpl) && stristr($menu_row['url'], "-pi-".$tpl) && $pagetype != "gi")
				$printclass = "selected_last";
			else
				$printclass = "notselected_last";
?>
					<li class="<?=$printclass ?>" style="width:<?php echo $li_item_width; ?>%"><a href="<?php echo $menu_row['url']; ?>"><span><font><?php echo $menu_row['title']; ?></font></span></a></li>
<?php
		}
		else {
			if (isset($tpl) && stristr($menu_row['url'], "-pi-".$tpl) && $pagetype != "gi")
				$printclass = "selected_middle";
			else
				$printclass = "notselected_middle";
?>
					<li class="<?=$printclass ?>" style="width:<?php echo $li_item_width; ?>%"><a href="<?php echo $menu_row['url']; ?>"><span><font><?php echo $menu_row['title']; ?></font></span></a></li>
<?php
		}
	}
}
?>
				</ul>
			</td></tr></table>
			</td></tr></table>
				<div id="searchbar">
					<?php echo tep_draw_form('quick_find', tep_href_link(FILENAME_ADVANCED_SEARCH_RESULT, '', 'NONSSL', false), 'get').tep_draw_hidden_field("search_in_description","1") . tep_hide_session_id(); ?>
					<div class="search_field1"></div>
					<div class="search_field2"><div class="search_field_t"><div class="search_field_b"><div class="search_field_r">
					<input type=text name="keywords" value="" title="<?=TEXT_SEARCH_WHOLE_STORE ?>" class="search_field_input blur" id="search_field_input">
					</div></div></div></div></form>
					<li class="gray_small_button"><a href="javascript:document.quick_find.submit();"><span><font><?=IMAGE_BUTTON_QUICK_FIND ?></font></span></a></li>
				</div>
				<div id="signupbar">
<?php
if ( (!strstr($_SERVER['PHP_SELF'],'login.php')) and !tep_session_is_registered('customer_id') )  {
    if (!tep_session_is_registered('customer_id')) {
?>
<div style="float:left; height: 22px;">
<?php echo tep_draw_form('login', tep_href_link(FILENAME_LOGIN, 'action=process', 'NONSSL', false), 'post'); ?>
<input type="text" name="email_address" id="login_email_address" maxlength="96" size="20" value="" title="Login Email Address" class="field_input blur" onKeyPress="return submitenter(this,event)">
<input type="password" name="password" id="login_password" maxlength="40" size="20" value="" title="Password" class="field_input blur" onKeyPress="return submitenter(this,event)">
</div></form>
<div class="gray_small_button"><a href="javascript:document.login.submit();"><span><font><?=ALT_BUTTON_SIGN_IN?></font></span></a></div>
<div style="float:left; margin:4px; 3px;">
<a href="<?php echo tep_href_link(FILENAME_LOGIN, '','SSL'); ?>" class=systemNav><?=ALT_BUTTON_SIGN_UP?></a> | 
<a href="<?php echo tep_href_link(FILENAME_PASSWORD_FORGOTTEN, '', 'SSL'); ?>" class=systemNav><?php echo LOGIN_BOX_PASSWORD_FORGOTTEN; ?></a>
</div>
<?php
	}
 	else {

	}
}
else {
  	if (tep_session_is_registered('customer_id')) {
  		$customer_sc_array = store_credit::get_current_credits_balance($customer_id);

		$email_verified_sql = "	SELECT civ.serial_number, civ.info_verified 
								FROM " . TABLE_CUSTOMERS . " AS c 
								LEFT JOIN " . TABLE_CUSTOMERS_INFO_VERIFICATION . " AS civ 
									ON (c.customers_id = civ.customers_id AND c.customers_email_address = civ.customers_info_value) 
								WHERE c.customers_id ='" . (int)$customer_id . "'";
		$email_verified_result_sql = tep_db_query($email_verified_sql);
		$email_verified_row = tep_db_fetch_array($email_verified_result_sql);
		$user_credit_balance = tep_user_balance($_SESSION['customer_id'], 'customers');
?>
						Welcome <a href="<?php echo tep_href_link(FILENAME_ACCOUNT, '', 'SSL'); ?>" class="signup"><b><?php echo $_SESSION['customer_first_name']; ?></b></a>&nbsp;&nbsp;|&nbsp;&nbsp;
						<a href="aaaaaa" class="signup"><b>Platinum</b></a> member&nbsp;&nbsp;|&nbsp;&nbsp;
						Credit Balance <b>
<?php
		if (count($user_credit_balance)) {
			$i = 0;
			foreach ($user_credit_balance as $bal_currency => $bal_amt) {
				echo '<a href="'.tep_href_link(FILENAME_MY_PAYMENT_HISTORY, tep_get_all_get_params(array('action', 'cur')) . 'action=show_request&cur='.$bal_currency).'">' . $currencies->currencies[$bal_currency]['symbol_left'].number_format($bal_amt, 2, '.', ',').$currencies->currencies[$bal_currency]['symbol_right'] . '</a> ,';
			}
		}

		echo $currencies->format( ($customer_sc_array['sc_reverse']+$customer_sc_array['sc_irreverse']) - ($customer_sc_array['sc_reverse_reserve_amt']+$customer_sc_array['sc_irreverse_reserve_amt']));
?>
						</b>&nbsp;&nbsp;|&nbsp;&nbsp;
						<a href="<?php echo tep_href_link(FILENAME_LOGOFF, '', 'NONSSL'); ?>" class="signup"><b>Logout</b></a>
<?php
	}
}
?>
				</div>
		</div></div></div></div></div>
	</div>