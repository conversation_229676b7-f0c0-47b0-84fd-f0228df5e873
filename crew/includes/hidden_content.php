<div id="region_popup_box" class="fancy_box">
	<div class="popup_close_button"></div>
	<div id="region_popup_box_content" class="fancy_content_footer" style="position:static;">
<?
ob_start();
?>
		<table border="0" cellspacing="0" cellpadding="0" width="100%" style="padding:15px 23px 0 20px;">
			<tr>
				<td>
				<form id="regionalForm_footer" name="regionalForm_footer" method="post" action="">
				<table id="regional_setting_table" border="0" cellspacing="0" cellpadding="3" width="100%" style="background-color:#ffffff;">
					<tr>
						<td height="10"></td>
					</tr>
					<tr>
						<td colspan="3" width="100%" style="padding:5px 10px;"><?=TEXT_REGIONAL_NOTE?></td>
					</tr>
					<tr>
						<td height="10"></td>
					</tr>
					<tr>
						<td width="70" style="padding:5px 10px;"><?=TEXT_REGIONAL_COUNTRY?></td>
						<td width="1">:</td>
						<td><div id="footer_local_country"><?=TEXT_IS_LOADING?></div></td>
					</tr>
					
					<tr>
						<td style="padding:5px 10px;"><?=TEXT_CURRENCY?></td>
						<td width="1">:</td>
						<td style="text-align:left;"><div id="div_currency">
							<?php
								if (tep_not_null($_SESSION['RegionGST']['currency']) && tep_not_null($currencies->currencies[$_SESSION['RegionGST']['currency']])) {
									echo '<input type="hidden" name="RegionalSet[currency]" value="' . $_SESSION['RegionGST']['currency'] . '">' . $currencies->currencies[$_SESSION['RegionGST']['currency']]['title'] . ' (' . $currencies->currencies[$_SESSION['RegionGST']['currency']]['symbol_left'] . $currencies->currencies[$_SESSION['RegionGST']['currency']]['symbol_right'] . ")";
								} else {
									echo tep_draw_pull_down_menu('RegionalSet[currency]', $currency_arr, $selected_currency,'style="width:305px"');
								}
							?>
						</div></td>
					</tr>
					
					<tr>
						<td style="padding:5px 10px;"><?=TEXT_LANGUAGE?></td>
						<td width="1">:</td>
						<td><div id="div_language">
								<?=tep_draw_pull_down_menu('RegionalSet[language]', $language_arr, $selected_language,'style="width:305px"'); ?>
							</div>
						</td>
					</tr>
					
					<tr>
						<td height="15"></td>
					</tr>
					<tr>
						<td style="padding:5px 0px;">&nbsp;</td>
						<td width="3" align="center">&nbsp;</td>
						<td align="center" width="50%">
							<div id="home">
								<?=tep_image_button2('gray_tall',"javascript:void(0);", TEXT_SAVE, 80, 'id="footer_local_submit"');?>
							</div>
						</td>
					<tr>
					<tr>
						<td height="50"></td>
					</tr>
				</table>
				</form>
				</td>
			</tr>
		</table>
<?
$region_setting_contents = ob_get_contents();
ob_end_clean();
echo $page_obj->get_html_simple_rc_box(BOX_HEADING_REGIONAL_SETTING, $region_setting_contents, 61);
?>
	</div>
</div>

<div id="login_popup_box" class="fancy_box">
	<div class="popup_close_button"></div>
	<div id="login_popup_box_content" class="fancy_content_footer" style="position:static;">
<?
		ob_start();
?>
			<table id="footer_login_content" border="0" cellspacing="0" cellpadding="0" width="100%" style="padding:15px 23px 0 20px;">
			<tr>
				<td>
				<form id="LoginForm" name="LoginForm" method="post" action="<?=tep_href_link(FILENAME_LOGIN, 'action=process', 'SSL')?>">
				<table id="footer_login_table" border="0" cellspacing="0" cellpadding="3" height="100%" width="100%" style="background-color:#ffffff;">
				
				<tr>
					<td style="padding:5px 0px;" nowrap><?=LOGIN_EMAIL?></td>
					<td width="3" align="center">:</td>
					<td><?=tep_draw_input_field('email_address', '', ' size="50" style="width:250px;"');?></td>
				</tr>
				
				<tr>
					<td style="padding:5px 0px;" nowrap><?=LOGIN_PASSWORD?></td>
					<td width="3" align="center">:</td>
					<td><?=tep_draw_input_field('password', '', ' size="50" style="width:250px;" onkeydown="if(event.keyCode == 13) {submit_login_form(\'LoginForm\');}"', 'password');?></td>
				</tr>
				
				<tr>
					<td style="padding:5px 0px;">&nbsp;</td>
					<td width="3" align="center">&nbsp;</td>
					<td nowrap><?=tep_draw_checkbox_field('remember_me', '1', false, "id='footer_remember_me'");?>&nbsp;<label for="footer_remember_me"><?=ENTRY_REMEMBER_ME?></label></td>
				</tr>
				<tr>
					<td>&nbsp;</td>
				</tr>
				<tr>
					<td style="padding:5px 0px;">&nbsp;</td>
					<td width="3" align="center">&nbsp;</td>
					<td align="center" width="50%">
						<div style="float:left"><?=tep_image_button2('gray_tall',"javascript:void(0);", IMAGE_BUTTON_LOGIN,80,'onClick=submit_login_form(\'LoginForm\')');
							echo tep_submit_button('', '', 'style="display:none" name="btn_login"');?>
						</div>
						<div style="float: left; padding-left: 11px; padding-top: 9px;">
							<a style="padding:5px" id="password_forgotten_link" href="javascript:void(0);"><?=LOGIN_BOX_PASSWORD_FORGOTTEN?></a>
						</div>
					</td>
                </tr>
				<tr>
					<td colspan="4">
						<div style="padding-top: 5px; margin-top: 0px;" class="dottedLine"><!-- --></div>
					</td>
				</tr>
				
				<tr>
					<td colspan="4">
						<table border="0" width="100%" cellspacing="0" cellpadding="0" style="padding:7px 0px 20px 0px;">
							<tr>
								<td>
									<table border="0" width="100%" cellspacing="0" cellpadding="0" align="right" style="text-align:right">
										<tr>
											<td>
												<div style="float:left;width:103px" >
													<?=LOGIN_WITH_FB_TITLE?>
												</div>
											</td>
											<td width="10" align="center">:</td>
											<td>
												<?=$ogm_fb_obj->get_FB_button('connect_with_facebook')?>
											</td>
										</tr>
									</table>
								</td>
								<td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td>
							</tr>
						</table>
					</td>
				</tr>
				</table>
				<input type="submit" id="LoginForm_submit" name="LoginForm_submit" class="transparent_class" style="position:absolute;" />
				</form>
				</td>
			</tr>
			</table>
<?
		$login_form = ob_get_contents();
		ob_end_clean();
		echo $page_obj->get_html_simple_rc_box(HEADER_LOGIN_TO_YOUR_ACCOUNT, $login_form, 61);
?>
	</div>
</div>

<div id="forgot_password_popup_box" class="fancy_box">
	<div class="popup_close_button"></div>
	<div id="forgot_password_popup_box_content" class="fancy_content_footer" style="position:static;">
<?
		ob_start();
?>
			<table id="footer_forgot_password_content" border="0" cellspacing="0" cellpadding="0" width="100%" style="padding:15px 23px 0 20px;">
			<tr>
				<td>
					<?=tep_draw_form('forget_password', tep_href_link(FILENAME_LOGIN, 'action=send_password'.$baseURL, 'SSL'), 'post', ' id="forget_password"') ?>
					<table id="footer_login_table" border="0" cellspacing="0" cellpadding="3" height="100%" width="100%" style="background-color:#ffffff;">
					<tr>
						<td width="3" style="padding:2px 10px 28px;"><?=tep_draw_radio_field('action', 'send_password', true, ' onclick="resetOthersRadio(this.value)"');?></td>
						<td colspan="3" style=""><?=LOGIN_BOX_SEND_PASSWORD_SELECTION?></td>
					</tr>
					
					<tr>
						<td width="3" style="">&nbsp;</td>
						<td style="" nowrap><?=LOGIN_EMAIL?></td>
						<td style="" width="3" align="center">:</td>
						<td style=""><?=tep_draw_input_field('forgotten_email_address', '', ' id="entry_email" size="50" style="width:150px;"  onkeydown="if(event.keyCode == 13) {submit_login_form(\'forget_password\');}"');?></td>
					</tr>
					
					<tr>
						<td width="3">&nbsp;</td>
						<td colspan="3" style="padding-top:10px;"><a id="login_return_link" href="javascript:void(0);"><?=LOGIN_RETURN?></a></td>
					</tr>
					
					<tr>
						<td colspan="4">
							<div style="padding-top: 5px; margin-top: 0px;" class="dottedLine"><!-- --></div>
						</td>
					</tr>
					
					<tr>
						<td colspan="4" align="right">
							<table border="0" cellspacing="0" cellpadding="0" style="padding:7px 0px 20px 0px;" align="right">
								<tr>
									<td>
										<div id="home" class="ashGreybutton">
											<a href="javascript:void(0);" onClick="submit_login_form('forget_password');">
												<span>
													<font><?=IMAGE_BUTTON_CONTINUE?></font>
												</span>
											</a>
										</div>
									</td>
								</tr>
							</table>
						</td>
					</tr>
					</table>
					</form>
				</td>
			</tr>
			</table>
<?
		$forgot_pw_form = ob_get_contents();
		ob_end_clean();
		echo $page_obj->get_html_simple_rc_box(LOGIN_BOX_PASSWORD_FORGOTTEN, $forgot_pw_form, 61);
?>
	</div>
</div>

<?
    if ((int)$customers_upkeep_obj->getUpkeepValue('reset_password')) {      
?>
<div id="reset_password_popup_box" class="fancy_box">
	<div id="reset_password_popup_box_content" class="fancy_content_footer" style="position:static;">
<?
		ob_start();
?>
        <table id="reset_forgot_password_content" border="0" cellspacing="0" cellpadding="0" width="100%" style="padding:15px 23px 0 20px;">
        <tr>
            <td>
                <?=tep_draw_form('reset_password', '', 'post', ' id="reset_password_form"') ?>
                <table border="0" cellspacing="0" cellpadding="3" height="100%" width="100%" style="background-color:#ffffff;">
                    <tr>
                        <td colspan="3" style="padding:0px;">
                            <div style="background-color: #FFFDDB">
                                <div class="error_msg" style="line-height: 25px;padding-left:5pt;"></div>
                            </div>
                        </td>
                    </tr>
<?
        if ((int)$customers_upkeep_obj->getUpkeepValue('reset_password') == 1) {
?>
                    <tr>
                        <td style="padding:5px 0px;" nowrap><?=LOGIN_PASSWORD?></td>
                        <td width="3" align="center">:</td>
                        <td><?=tep_draw_input_field('rsp_password', '', ' size="50" style="width:250px;"', 'password')?></td>
                    </tr>
<?
        }
?>
                    <tr>
                        <td style="padding:5px 0px;" nowrap><?=LOGIN_PASSWORD_NEW?></td>
                        <td width="3" align="center">:</td>
                        <td><?=tep_draw_input_field('rsp_new_password', '', ' size="50" style="width:250px;"', 'password')?></td>
                    </tr>

                    <tr>
                        <td style="padding:5px 0px;"><?=LOGIN_PASSWORD_NEW_CONFIRMATION?></td>
                        <td width="3" align="center">:</td>
                        <td nowrap><?=tep_draw_input_field('rsp_cfm_password', '', ' size="50" style="width:250px;"', 'password')?></td>
                    </tr>
                    <tr>
                        <td>&nbsp;</td>
                    </tr>
                    <tr>
                        <td style="padding:5px 0px;">&nbsp;</td>
                        <td width="3" align="center">&nbsp;</td>
                        <td align="center" width="50%">
                            <div style="float:left"><?=tep_image_button2('gray_tall',"javascript:void(0);", 'Save', 80,'onClick=submit_reset_pw_form()')?></div>
                        </td>
                    </tr>
                    <tr>
                        <td>&nbsp;</td>
                    </tr>
                </table>
                </form>
            </td>
        </tr>
        </table>
<?
		$reset_pw_form = ob_get_contents();
		ob_end_clean();
		echo $page_obj->get_html_simple_rc_box(BOX_CHANGE_PASSWORD, $reset_pw_form, 61);
?>
	</div>
</div>
<?
    }
    
    if (isset($_SESSION['RESET_PHONE_NO']) && $_SESSION['RESET_PHONE_NO']) {
        require_once(DIR_WS_LANGUAGES . $language . '/' . FILENAME_LOGIN);
        
        if (!$customer_id) {
            unset($_SESSION['RESET_PHONE_NO']);
        } else {
            $customer_country_code = '';
            $customer_contact_number = '';
            $account_sql = "SELECT customers_telephone, customers_country_dialing_code_id
                            FROM " . TABLE_CUSTOMERS . " 
                            WHERE customers_id ='" . (int)$customer_id . "'";
            $account_result_sql = tep_db_query($account_sql);
            if ($account = tep_db_fetch_array($account_result_sql)) {
                $customer_country_code = $account['customers_country_dialing_code_id'];
                $customer_contact_number = tep_not_null($account['customers_telephone']) ? substr($account['customers_telephone'], 0, -4) . '****' : '';
            }
?>
<div id="reset_phone_popup_box" class="fancy_box" style="width: 700px">
    <div class="popup_close_button"></div>
	<div id="reset_phone_popup_box_content" class="fancy_content_footer" style="position:static;">
        <script>
            function submit_rpt_form(){
                jQuery('span.err_msg').removeClass('gc_fail').html('').parents('tbody.highlight').removeClass('highlight');
                jQuery.ajax({
                    type: "post",
                    url: 'customer_xmlhttp.php?action=update_reset_phone',
                    data: jQuery('#reset_phone_form').serialize(),
                    dataType: 'xml',
                    beforeSend:  function() {
                        jQuery('span.err_msg').addClass('load_img load_img_fm');
                    },
                    error: function(){jQuery('span.err_msg').removeClass('load_img load_img_fm');alert('Please try again...')},
                    success: function(xml) {
                        jQuery('span.err_msg').removeClass('load_img load_img_fm');
                        if(jQuery(xml).find('error_key').length > 0){
                            jQuery(xml).find("error_key").each(function(){
                                jQuery('#'+jQuery(this).attr("index")+'_msg').addClass('gc_fail').html(jQuery(this).text()).parents('tbody').addClass('highlight');
                            });
                        }else{
                            skip_submit_rpt_form();
                            if (window.location.pathname=='/account.php'){window.location.reload();}
                        }
                    }
                });
            }
            
            function skip_submit_rpt_form(){
                // clear the session
                jQuery.ajax({
                    url: 'customer_xmlhttp.php?action=clear_reset_phone_flag',
                    dataType: 'xml',
                    error: function(){alert('Please try again...')},
                    success: function() {
                        hide_fancybox('reset_phone_popup_box');
                    }
                });
            }
            
            jQuery('#reset_phone_popup_box>div.popup_close_button').bind('click', function(e){
                skip_submit_rpt_form();
            });
        </script>
        <style type="text/css">
            table#rpt td {vertical-align:middle;padding: 8px 2px;}
            table#rpt div.wl {height: 1px; border-top: 1px dotted black; text-align: center; position: relative; width: 315px;}
            table#rpt div.wl span {position: relative; top: -.7em; background: white; display: inline-block;}
            table#rpt span.gc_fail {display: block;line-height: 15px;padding: 3px 0 0 25px;min-height: 22px;background: url("images/icons/cancel-round.png") no-repeat scroll 0 4px transparent;}
            table#rpt span.load_img_fm {width: 16px; height: 16px; display: block;margin-top: 5px;}
            table#rpt tbody.highlight {background-color:#FFFDDB}
        </style>
<?
            ob_start();
            echo tep_draw_form('reset_phone', '', 'post', ' id="reset_phone_form"');
?>
            <div  style="padding:15px 23px 0 20px;">
                <div>
                    <span class="hds5"><?=BOX_CHANGE_CONTACT_NOTICE?></span>
                </div>
                <div class="dotborder" style="height: 1px; margin: 12px 0pt;"></div>
                <div>
                    <span class="hds3"><?=BOX_UPDATE_YOUR_MOBILE_NUMBER?></span>
                    <div style="padding-bottom:10px"><span class="hds5"><?=BOX_CHANGE_CONTACT_FOR_SECURITY_REASONS?></span></div>
                </div>
                <table id="rpt" border="0" cellspacing="0" cellpadding="0" style="width: 100%;">
                    <tbody>
                    <tr>
                        <td width="155px" style="width:155px"><span class="hdsC5"><?=EZ_COUNTRY_CODE?><font color="red">*</font></span></td>
                        <td width="320px" style="width:320px"><div class="shd1"><?=tep_get_ezsignup_country_list('country', $customer_country_code, 'style="width:315px;" id="country"')?></div></td>
                        <td width="180px" style="width:180px"><span id="country_msg" class="err_msg">&nbsp;</span></td>
                    </tr>
                    </tbody>
                    <tbody>
                    <tr>
                        <td><span class="hdsC5"><?=EZ_CONTACT_NUMBER?><font color="red">*</font></span></td>
                        <td><div class="ihd1"><?=tep_draw_input_field('contactnumber', $customer_contact_number, 'style="width: 300px;background-color: #FFF;" id="contactnumber"', 'text', false)?></div></td>
                        <td><span id="contactnumber_msg" class="err_msg"></span></td>
                    </tr>
                    </tbody>
                    <tr><td colspan="3"><div class="dotborder" style="height: 1px;"></div></td></tr>
                    <tr>
                        <td><span class="hdsC5"><?=EZ_SECRET_QUESTION?><font color="red">*</font></span></td>
                        <td><span class="hdC5"><?=$customers_security_obj->get_customer_registered_security_question()?></span></td>
                        <td></td>
                    </tr>
                    <tbody>
                    <tr>
                        <td><span class="hdsC5"><?=EZ_ANSWER?><font color="red">*</font></span></td>
                        <td><div class="ihd1"><?=tep_draw_input_field('answer', '', 'style="width: 300px;background-color: #FFF;" id="answer"')?></div></td>
                        <td><span id="answer_msg" class="err_msg"></span></td>
                    </tr>
                    </tbody>
                    <tr>
                        <td></td>
                        <td><div class="wl"><span>&nbsp;<?=BOX_CHANGE_CONTACT_OR?>&nbsp;</span></div></td>
                        <td></td>
                    </tr>
                    <tbody>
                    <tr>
                        <td><?=BOX_CHANGE_CONTACT_FILL_LAST4DIGIT?>:</td>
                        <td><div class="ihd1"><?=tep_draw_input_field('last4digit', '', 'style="width: 300px;background-color: #FFF;" id="last4digit"')?></div></td>
                        <td><span id="last4digit_msg" class="err_msg"></span></td>
                    </tr>
                    </tbody>
                    <tr>
                        <td colspan="3" style="padding-top:10px"><?=BOX_CHANGE_CONTACT_FOOTER_DESC?></td>
                    </tr>
                </table>
            </div>
        </form>
        <div class="solborder" style="margin: 15px;"></div>
        <div style="text-align:center">
<?
            echo '<div style="display:inline; padding-right: 5px;zoom: 1">' . tep_image_button2('gray_tall',"javascript:void(0);", BUTTON_SAVE_CHANGES, '','onClick="submit_rpt_form()"') . '</div>' . 
                 '<div style="display:inline; padding-left: 5px;zoom: 1">' . tep_image_button2('gray_tall',"javascript:void(0);", BUTTON_SKIP_NOW, '','onClick="skip_submit_rpt_form()"') . '</div>';
?>
        </div>
<?
            $reset_pw_form = ob_get_contents();
            ob_end_clean();
            echo $page_obj->get_html_simple_rc_box(BOX_IMPORTANT_NOTICE, $reset_pw_form, 61);
?>
	</div>
</div>
<?
        }
    }
?>
<div id="general_popup_box" class="fancy_box">
	<div class="popup_close_button"></div>
		<div id="general_popup_box_content" class="general_fancy_content" style="position:static;">
<?
		ob_start();
?>
	
		<div id="general_content" style="padding:40px"></div>
		
<?
		$general_content = ob_get_contents();
		ob_end_clean();
		echo $page_obj->get_html_simple_rc_box('', $general_content, 63);
?>
	</div>
</div>
<div id="whitebox_tooltips" class="fancy_box_footer" style="display:none;">
	<?=$page_obj->get_html_simple_rc_box('', '<div id="whitebox_tooltips_content">&nbsp;</div><div class="lfloat" style="padding:5px 50px 0px;"><span class="blue_triangle"></span></div><div class="clrFx"></div>', 54)?>
</div>