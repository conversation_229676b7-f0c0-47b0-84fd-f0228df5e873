<HTML>
<HEAD><TITLE>Datetime Picker Theme</TITLE>
</HEAD>
<BODY>
<style type="text/css">
.plain {height:20px; vertical-align:middle;}
</style>



<FORM name="demoform">
&nbsp;Date Field: <input class="plain" name="dc" value="" size="19"><a href="javascript:void(0)" onclick="if(self.gfPop)gfPop.fPopCalendar(document.demoform.dc);return false;" HIDEFOCUS><img name="popcal" align="absmiddle" src="calbtn.gif" width="34" height="22" border="0" alt=""></a>
<!--a href="javascript:void(0)" onclick="if(self.gfPop)gfPop.fPopCalendar(document.demoform.dc, new Array('01/05/2001', '10/08/2002'));return false;" HIDEFOCUS><img name="popcal" align="absmiddle" src="calbtn.gif" width="34" height="22" border="0" alt=""-->
</FORM>




<font size=-1>
<BR><BR> 
<BR>NOTE: Set the gAgendaMask options to -1 to enable all agenda effects.
<BR>NOTE: Right now the time incremental interval is set to 5 minutes. To change it, simply modify the "var _inc=5;" statement in this plugins_12.js, where you may find other options like scroll time and separator char.
<BR>NOTE: To use 24 hours time, simply switch to use the plugins_24.js.
</font>

  <hr size="1">
  <div align="right"><font size=-2><em>Copyright&copy; 2003-2005 Idemfactor Solutions, 
    Inc. All rights reserved.</em></font></div>



<!--  PopCalendar(tag name and id must match) Tags should not be enclosed in tags other than the html body tag. -->
<iframe width=188 height=166 name="gToday:datetime:agenda.js:gfPop:plugins_24.js" id="gToday:datetime:agenda.js:gfPop:plugins_24.js" src="ipopeng.htm" scrolling="no" frameborder="0" style="visibility:visible; z-index:999; position:absolute; top:-500px; left:-500px;">
</iframe>
</BODY>
</HTML>
