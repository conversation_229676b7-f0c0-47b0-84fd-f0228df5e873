/*!
 * Bootstrap Form Helpers
 *
 * Copyright 2012 <PERSON>, Inc
 * Licensed under the Apache License v2.0
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Designed and built by @vincent lamanna.
 */

.bfh-selectbox {
  height: 30px;
  position: relative;
  display: inline-block;
}

.bfh-selectbox-toggle {
  *margin-bottom: -3px;
}

.bfh-selectbox-toggle:active,
.open .bfh-selectbox-toggle {
  outline: 0;
}

.bfh-selectbox .caret {
  margin-top: 8px;
  margin-left: 2px;
  height: 8px;
}

.bfh-selectbox > .bfh-selectbox-toggle > .caret {
  float: right;
}

.bfh-selectbox-options {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 1000;
  display: none;
  float: left;
  min-width: 160px;
  padding: 5px 0;
  margin: 2px 0 0;
  background-color: #ffffff;
  border: 1px solid #ccc;
  border: 1px solid rgba(0, 0, 0, 0.2);
  *border-right-width: 2px;
  *border-bottom-width: 2px;
  -webkit-border-radius: 6px;
     -moz-border-radius: 6px;
          border-radius: 6px;
  -webkit-box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
     -moz-box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
          box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
  -webkit-background-clip: padding-box;
     -moz-background-clip: padding;
          background-clip: padding-box;
}

.bfh-selectbox-options ul {
  list-style: none;
}

.bfh-selectbox-options.pull-right {
  right: 0;
  left: auto;
}

.bfh-selectbox-options a {
  display: block;
  padding: 3px 20px;
  clear: both;
  font-weight: normal;
  line-height: 20px;
  height: 20px;
  color: #333333;
  white-space: nowrap;
  text-decoration: none;
}

.bfh-googlefonts .bfh-selectbox-options a {
  height: 30px;
  text-indent: -9999px;
  background-image: url(../img/bootstrap-formhelpers-googlefonts.png);
}

.bfh-selectbox-options li > a:focus {
  color: #ffffff;
  text-decoration: none;
  background-color: #0088cc;
  background-color: #0081c2;
  background-image: -moz-linear-gradient(top, #0088cc, #0077b3);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#0088cc), to(#0077b3));
  background-image: -webkit-linear-gradient(top, #0088cc, #0077b3);
  background-image: -o-linear-gradient(top, #0088cc, #0077b3);
  background-image: linear-gradient(to bottom, #0088cc, #0077b3);
  background-repeat: repeat-x;
  filter: progid:dximagetransform.microsoft.gradient(startColorstr='#ff0088cc', endColorstr='#ff0077b3', GradientType=0);
}

.bfh-googlefonts .bfh-selectbox-options li > a:focus {
  background-image: url(../img/bootstrap-formhelpers-googlefonts.png);
  background-repeat: no-repeat;
  filter: none;
  background-color: transparent;
  filter: none;
  outline: none;
}

.bfh-selectbox-options .active > a,
.bfh-selectbox-options .active > a:hover {
  color: #ffffff;
  text-decoration: none;
  background-color: #0088cc;
  background-color: #0081c2;
  background-image: linear-gradient(to bottom, #0088cc, #0077b3);
  background-image: -moz-linear-gradient(top, #0088cc, #0077b3);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#0088cc), to(#0077b3));
  background-image: -webkit-linear-gradient(top, #0088cc, #0077b3);
  background-image: -o-linear-gradient(top, #0088cc, #0077b3);
  background-repeat: repeat-x;
  outline: 0;
  filter: progid:dximagetransform.microsoft.gradient(startColorstr='#ff0088cc', endColorstr='#ff0077b3', GradientType=0);
}

.bfh-googlefonts .bfh-selectbox-options .active > a,
.bfh-googlefonts .bfh-selectbox-options .active > a:hover {
  background-image: url(../img/bootstrap-formhelpers-googlefonts.png);
  background-repeat: no-repeat;
  filter: none;
  background-color: transparent;
  filter: none;
}

.bfh-selectbox-options .disabled > a,
.bfh-selectbox-options .disabled > a:hover {
  color: #999999;
}

.open {
  *z-index: 1000;
}

.open > .bfh-selectbox-options {
  display: block;
}

.pull-right > .bfh-selectbox-options {
  right: 0;
  left: auto;
}

.bfh-selectbox > .bfh-selectbox-toggle {
  color: #000;
  padding: 4px;
  display: inline-block;
  text-decoration: none;
  background-color: white;
  border: 1px solid #CCC;
  border: 1px solid rgba(0, 0, 0, 0.2);
  -webkit-border-radius: 6px;
  -moz-border-radius: 6px;
  border-radius: 6px;
}

.bfh-selectbox-options ul {
  max-height: 200px;
  overflow-x: hidden;
  overflow-y: auto;
  margin: 5px 0 0 0;
  width: 240px;
}

.bfh-selectbox-filter {
  margin: 0 10px;
}

.bfh-selectbox > .bfh-selectbox-toggle > .bfh-selectbox-option {
  display: inline-block;
  float: left;
  height: 20px;
  overflow: hidden;
}

span.bfh-countries {
  line-height: 18px;
}

.bfh-datepicker-calendar > table.calendar {
  width: 376px;
  background: #fff;
}

.bfh-datepicker-calendar > table.calendar .months-header > th {
  text-align: center;
  font-size: 12px;
}

.bfh-datepicker-calendar > table.calendar .months-header > th.month > span {
  width: 100px;
  display: inline-block;
}

.bfh-datepicker-calendar > table.calendar .months-header > th.year > span {
  width: 50px;
  display: inline-block;
}

.bfh-datepicker-calendar > table.calendar .days-header > th {
  text-align: center;
  font-size: 11px;
  line-height: 12px;
}

.bfh-datepicker-calendar > table.calendar > tbody > tr > td {
  text-align: center;
  font-size: 11px;
  line-height: 12px;
}

.bfh-datepicker-calendar > table.calendar > tbody > tr > td.today {
	background-color: #999;
	color: #fff;
	text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
}

.bfh-datepicker-calendar > table.calendar > tbody > tr > td.off {
	color: #aaa;
}

.bfh-datepicker {
  position: relative;
}

.bfh-datepicker-toggle {
  *margin-bottom: -3px;
}

.bfh-datepicker-calendar {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 1000;
  display: none;
  float: left;
  min-width: 160px;
}

.bfh-datepicker-calendar.pull-right {
  right: 0;
  left: auto;
}

.open > .bfh-datepicker-calendar {
  display: block;
}

.bfh-datepicker-calendar > table > tbody > tr > td:not(.off):hover {
  cursor: pointer;
  color: #ffffff;
  text-decoration: none;
  background-color: #0088cc;
  background-color: #0081c2;
  background-image: linear-gradient(to bottom, #0088cc, #0077b3);
  background-image: -moz-linear-gradient(top, #0088cc, #0077b3);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#0088cc), to(#0077b3));
  background-image: -webkit-linear-gradient(top, #0088cc, #0077b3);
  background-image: -o-linear-gradient(top, #0088cc, #0077b3);
  background-repeat: repeat-x;
  outline: 0;
  filter: progid:dximagetransform.microsoft.gradient(startColorstr='#ff0088cc', endColorstr='#ff0077b3', GradientType=0);
}

.bfh-timepicker-popover > table {
  width: 180px;
  margin: 0;
}

.bfh-timepicker-popover > table > tbody > tr > td {
  text-align: center;
  border: 0;
}

.bfh-timepicker-popover > table > tbody > tr > td.separator {
  line-height: 65px;
  font-weight: bold;
  font-size: 20px;
}

.bfh-timepicker-popover > table > tbody > tr > td > input {
  margin: 0;
  text-align: center;
}

.bfh-timepicker {
  position: relative;
}

.bfh-timepicker-toggle {
  *margin-bottom: -3px;
}

.bfh-timepicker-popover {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 1000;
  display: none;
  float: left;
  min-width: 160px;
  background-color: #ffffff;
  border: 1px solid #ccc;
  border: 1px solid rgba(0, 0, 0, 0.2);
  *border-right-width: 2px;
  *border-bottom-width: 2px;
  -webkit-border-radius: 6px;
     -moz-border-radius: 6px;
          border-radius: 6px;
}

.bfh-timepicker-popover.pull-right {
  right: 0;
  left: auto;
}

.open > .bfh-timepicker-popover {
  display: block;
}