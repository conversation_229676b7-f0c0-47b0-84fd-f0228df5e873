<script language="javascript"><!--
var selected;
var submitter = null;

function submitFunction() {
   submitter = 1;
}

function selectRowEffect(object, buttonSelect) {
	var pm_value = '';

	if (!selected) {
		if (document.getElementById) {
	  		selected = document.getElementById('defaultSelected');
		} else {
	  		selected = document.all['defaultSelected'];
		}
	}
  	if (selected) selected.className = 'moduleRow';
  	object.className = 'moduleRowSelected';
  	selected = object;

	// one button is not an array
	if (typeof(document.payment_methods_form) != 'undefined') {
		if (document.payment_methods_form.payment[0]) {
			jQuery("#pm_"+buttonSelect).attr("checked", "true");
			pm_value = jQuery("#pm_"+buttonSelect).attr("checked", "true");
		} else {
			document.payment_methods_form.payment.checked = true;
			pm_value = document.payment_methods_form.payment.value;
		}
	}
  	//radioDisplayArea(pm_value);

  	if (pm_value != 'iPay88') {
  		reset_pm_selection('ipay88_pm_id', '', false);
  	}
}

function rowOverEffect(object) {
  if (object.className == 'moduleRow') object.className = 'moduleRowOver';
}

function rowOutEffect(object) {
  if (object.className == 'moduleRowOver') object.className = 'moduleRow';
}

function Trim(strValue) {
	return LTrim(RTrim(strValue));
}

function LTrim(strValue) {
	var LTRIMrgExp = /^\s */;
	return strValue.replace(LTRIMrgExp, '');
}

function RTrim(strValue) {
	var RTRIMrgExp = /\s *$/;
	return strValue.replace(RTRIMrgExp, '');
}

function payment_check_form() {
	if (!check_form()) {
		return false;
	}

	if (typeof(comments_validation) == 'function') {
  		var comment_check_result = comments_validation();

		if (comment_check_result != '') {
			alert(comment_check_result);
			return false;
		}
	}
	/*
	if (!comments_validation()) {
		return false;
	}
	*/
	return true;
}

function reset_pm_selection(field_name, match_val, mode) {
	if (!selected) {
		if (document.getElementById) {
	  		selected = document.getElementById('defaultSelected');
		} else {
	  		selected = document.all['defaultSelected'];
		}
	}

	var elts      = (typeof(document.forms['checkout_payment']) != 'undefined' && typeof(document.forms['checkout_payment'].elements[field_name]) != 'undefined')
    	          ? document.forms['checkout_payment'].elements[field_name]
        	      : "";
	var elts_cnt  = (typeof(elts.length) != 'undefined')
    	          ? elts.length
        	      : 0;

	if (elts_cnt) {
    	for (var i = 0; i < elts_cnt; i++) {
    		if (match_val == '') {
    			elts[i].checked = mode;
    		} else {
	        	if (elts[i].value == match_val) {
	        		elts[i].checked = mode;

	        		if (field_name == 'payment') {
		        		var newly_selected_row = document.getElementById('row_pm_'+i) != null ? document.getElementById('row_pm_'+i) : document.getElementById('defaultSelected');
		        		if (selected) selected.className = 'moduleRow';
		        		newly_selected_row.className = "moduleRowSelected";
		        		selected = newly_selected_row;
	        		}

	        		break;
	        	}
	        }
    	} // end for
	} else if (elts!='') {
       	if (elts.value == match_val) {
       		elts.checked = mode;
       	}
	} // end if... else
}

//--></script>

<? echo $payment_modules->javascript_validation(); ?>