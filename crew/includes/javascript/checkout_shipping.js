	var selected;
	
	function selectRowEffect(object, buttonSelect) {
  		if (!selected) {
    		if (document.getElementById) {
      			selected = document.getElementById('defaultSelected');
    		} else {
      			selected = document.all['defaultSelected'];
    		}
  		}
		
  		if (selected) selected.className = 'moduleRow';
  		object.className = 'moduleRowSelected';
  		selected = object;
		
		// one button is not an array
  		if (document.checkout_address.shipping[0]) {
    		document.checkout_address.shipping[buttonSelect].checked=true;
  		} else {
    		document.checkout_address.shipping.checked=true;
  		}
	}
	
	function rowOverEffect(object) {
  		if (object.className == 'moduleRow') object.className = 'moduleRowOver';
	}
	
	function rowOutEffect(object) {
  		if (object.className == 'moduleRowOver') object.className = 'moduleRow';
	}
	
	function Trim(strValue) {
		return LTrim(RTrim(strValue));
	}
	
	function LTrim(strValue) {
		var LTRIMrgExp = /^\s */;
		return strValue.replace(LTRIMrgExp, '');
	}
	
	function RTrim(strValue) {
		var RTRIMrgExp = /\s *$/;
		return strValue.replace(RTRIMrgExp, '');
	}
	
	function shipping_check_form() {
		if (!check_form()) {
			return false;
		}
		
		if (typeof(comments_validation) == 'function') {
	  		var comment_check_result = comments_validation();
	  		
			if (comment_check_result != '') {
				alert(comment_check_result);
				return false;
			}
		}
		
		return true;
	}