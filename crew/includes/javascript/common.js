function Select_Before(){return true};
function Select_After(){};
function Act_Before(){return true};
function Act_After(){};
function Login_Before(){};
function Login_After(){};
String.prototype.trim=function(){return this.replace(/(^\s*)|(\s*$)/g,"")};
Array.prototype.seek = function(v) {
	for(var i=0; i<this.length; i++) if(this[i][0]==v) return i; 
    return -1;
};
Array.prototype.remove = function(pos) {
	if(pos==null||pos<0||pos>=this.length) return;
	for(var i=pos;i<this.length-1;i++) this[i]= this[i+1];
    return this.pop();
};
function $() {
	if(arguments.length <= 1) return document.getElementById(arguments[0]);
	var arr=[];
	for (var i=0;i<arguments.length;i++) arr.push($(arguments[i]));
	return arr;
}
function $$(n,c){
	var t=c==null?n:c,o=c==null?document:(typeof(n)=="string"?$(n):n);
	return o.getElementsByTagName(t.toUpperCase());
}
function $$$(n){return document.getElementsByName(n);}
var Control={},Search={},List={},SET={},K={},
Service={
queue:[],
call:function(a,p,c){
	if(!a)return;
	if(this.busy){return this.queue.push({a:a,p:p,c:c})}
	if(c){if(c.busy)return;if(typeof(c)=="object")c.busy=true;}
	this.busy=true;
	var s=p==null?"":(typeof(p)=="object"?F.toXml(p):p),i=a.lastIndexOf('.'),h=F.xmlHttp(),d;
	if(i>-1){d=a.substring(0,i);a=a.substring(i+1);} else d=F.urlhead();
	if(c)h.onreadystatechange=function(){if(h.readyState<4)return;Service.analyse(h,a,c);};
	h.open("POST",d+".ashx",c?true:false);
	h.setRequestHeader("SOAPAction", a);
	h.setRequestHeader("Content-Type", "text/xml;charset=utf-8");
	if(document.body.lang)h.setRequestHeader("Accept-Language",document.body.lang);
	h.send("<?xml version='1.0'?><"+a+">"+s+"</"+a+">");
	if(!c) return this.analyse(h,a,c);
},
analyse:function(h,a,c){
	try{
	var T={};
	eval(h.responseText);
	if(T.logout)Login_Before(T);else if(T.login)Login_After(T);
	if(T.script)eval(T.script);
	if(T.alert)alert(T.alert);
	if(c){if(typeof(c)=="object")c.adapt(T);else c(T);}
	return T;
}
catch(e){if(typeof(c)=="object")c.abort(e);else alert("Server Busy, Please Try Later!");}
finally{this.busy=false;if(this.queue.length>0){var q=this.queue.pop();this.call(q.a,q.p,q.c);}}
}},
Cookie={
get:function(n){
  n+="=";var c=document.cookie,v="";
  if(c.length==0) return v;
  var a=c.indexOf(n);
  if(a==-1) return v;
  a+=n.length;
  var b=c.indexOf(";",a);
  if (b==-1) b=c.length;
  v=unescape(c.substring(a,b));
  return v;
},
set:function(n,v,h){
  var e=h==null?"":"; expires="+new Date((new Date()).getTime() + h * 3600000).toGMTString();
  document.cookie=n+"="+escape(v)+";path=/"+e;
}},
Check={
number:function(o){
	var v=o.value.trim(),z=o.getAttribute("zero"),a=F.label(o);
	if(!v) return o.value="";
	if(v==z) return o.value=z;
	v=parseFloat(v);
	if(isNaN(v)) throw{message:a+lang.numerr}; 
	if(v==0&&z)return o.value=z;
	if(!o.getAttribute("neg"))v=Math.abs(v);
	var t=parseInt(o.getAttribute("lower")),s=parseInt(o.getAttribute("scale"));
	if(t&&v<t)throw{message:a+lang.numdown+t};
	t=parseInt(o.getAttribute("upper"));
	if(t&&v>t)throw{message:a+lang.numup+t};
	if(!s) return o.value=parseInt(v);
	var r=v.toString(),p=r.indexOf("."),l=0;
	p<0?r+=".":l=r.length-1-p;
	for (var i=0;i<(s-l);i++) r+="0";
	o.value=r;
},
en:function(o){
	var v=o.value.trim();
	if(v.match(/[^\w\s\.,\-\&\/\\]+/i))throw{message:F.label(o)+lang.en};
	o.value=v;
},
email:function(o){
	var v=o.value.trim();
	if(!v.match(/^[\w.-]+@([0-9a-z][\w-]+\.)+[a-z]{2,3}$/i))throw{message:lang.email};
	o.value=v;
},
date:function(o){
	var v=o.value.trim();
	if (!v) return o.value=v;
	if(!v.match(/^(?:([0-9]{4}\/(?:(?:0?[1,3-9]|1[0-2])\/(?:29|30)|((?:0?[13578]|1[02])\/31)))|([0-9]{4}\/(?:0?[1-9]|1[0-2])\/(?:0?[1-9]|1\d|2[0-8]))|(((?:(\d\d(?:0[48]|[2468][048]|[13579][26]))|(?:0[48]00|[2468][048]00|[13579][26]00))\/0?2\/29)))$/i))
	throw{message:F.label(o)+lang.date};
	o.value=v;
},
web:function(o){
	var v=o.value.trim(),a=o.getAttribute("label");
	if(!a)a=o.getAttribute("name");
	if (!v) return o.value=v;
	if(!v.match(/^(http:\/\/)?(\w+(-\w+)*)(\.(\w+(-\w+)*))*(\?\S*)?$/i))
	throw{message:a+lang.web};
	o.value=v;
},
focus:function(o){
	var n=o.getAttribute("name"),d=F.id("warn_"+n);
	if(d){
		d.className="warn_hover";
		var s=d.getAttribute("hover");
		d.innerHTML=s?s:d.old;
	}
},
exam:function(o){
	var k=o.getAttribute("kind"),n=o.getAttribute("name");
	try
	{	var v=o.value,a=o.getAttribute("label"),t=o.getAttribute("need");
		if((t||v)&&this[k])this[k](o);
		v=o.value;
		if(v)v=v.trim();
		if(!a)a=n;
		if (t&&!v)throw{message:t};
		var m=o.getAttribute("min"),n=o.getAttribute("max");
		if (m&&v.length<m)throw{message:a+lang.max[0]+m+lang.max[1]};
		if (n&&v.length>n)throw{message:a+lang.min[0]+n+lang.min[1]};
		if ((t=o.getAttribute("equal"))&&F.id(t)&&F.id(t).value&&(F.id(t).value.trim()!=v))
			throw{message:lang.equal};
		if(o.tagName=="INPUT")o.value=v;
		this.warn(o,void(0));
	}
	catch(e){this.warn(o,e);}
},
warn:function(o,e){
	o.error=e;
	var n=o.getAttribute("name"),d=F.id("warn_"+n);
	if(!d) return;
	if(o.error){d.className="warn_error";d.innerHTML=o.error.message;}
	else{
		d.className="warn_finish";
		var s=d.getAttribute("finish");
		d.innerHTML=s?s:d.old;
	}
}},
Flash={
spacer:"|",
size:function(w,h){this.width=w;this.height=h;},
set:function(n,v){this.para={};this.add(n,v);},
add:function(n,v){
	if(!this.para) this.para={};
	if(!this.para[n]) this.para[n]=[];
	this.para[n].push(v);
},
write:function(n){
	var s=[],t=[];
	s.push("<object classid='clsid:D27CDB6E-AE6D-11cf-96B8-444553540000' ");
	s.push("codebase='http://download.macromedia.com/pub/shockwave/cabs/flash/swflash.cab#version=8,0,0,0' width="+this.width+" height="+this.height+">");
	s.push("<param name='movie' value='"+this.src+"' />");
	if(this.bgcolor) s.push("<param name='bgcolor' value='"+this.bgcolor+"'>");
	s.push("<param name='quality' value='high' />");
	s.push("<param name='menu' value='false' />");
	if(this.para){
		for(var e in this.para){
			var x=[],p=this.para[e];
			for(var i=0;i<p.length;i++) x.push(p[i]);
			t.push(e+"="+x.join(this.spacer));
		}
		s.push("<param name='FlashVars' value='"+t.join("&")+"'>");
	}
	s.push('<embed src="'+this.src+'" quality="high" pluginspage=http://www.macromedia.com/go/getflashplayer type="application/x-shockwave-flash"');
	if(this.bgcolor) s.push(" bgcolor='"+this.bgcolor+"' ");
	s.push(" width="+this.width+" height="+this.height);
	if(this.para) s.push(" FlashVars='" + t.join("&") + "'");
	s.push("></embed></object>");
	if(n) $(n).innerHTML=s.join('');
	else document.write(s.join(''));
}},
F={
urlhead:function(){
	var url=location.pathname,pos=url.lastIndexOf('/');
	if(pos>-1) url=url.substring(pos+1);
	if((pos=url.indexOf('-'))>-1) url=url.substring(0,pos);
	else if((pos=url.indexOf('.'))>-1) url=url.substring(0,pos);
	return url;
},
ie:(window.ActiveXObject!=null?true:false),
ie7:navigator.userAgent.indexOf('MSIE 7.0')>-1,
call:function(a,p,f){return this.callxml(a,p?"<id>"+p+"</id>":"",f);},
callxml:function(a,p,f){try{return Service.call(a,p?p:"",f)}catch(e){alert(e.message)}},
send:function(url){
	var h=F.xmlHttp();
	h.open("GET",url,false);
	h.send(null);
	return h.responseText;
},
xmlIn:function(s){if(s==null)return "";return s.toString().replace(/&/g,"&amp;").replace(/"/g,"&quot;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/\r/g,"\\r").replace(/\n/g,"\\n");},
xmlOut:function(s){if(s==null)return "";return s.replace(/&amp;/g,"&").replace(/&quot;/g,'"').replace(/&#39;/g,"'").replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&nbsp;/g," ")},
id:function(n){return document.getElementById(n);},
name:function(n){return document.getElementsByName(n);},
tag:function(n,o){
	if(!o)o=document;
	if(typeof(o)=="string")o=document.getElementById(o);
	return o.getElementsByTagName(n.toUpperCase());},
label:function(o){var n=o.getAttribute("label");if(!n)n=o.getAttribute("name");return n;},
addCss:function(url){
	var o=F.tag("HEAD")[0],s=F.id("mycss");
	if(!s) return;
	if(F.ie)s.href=url;
	else{
		o.removeChild(s);
		s=o.appendChild(document.createElement("link"));
		s.id="mycss";
		s.type="text/css";
		s.rel="stylesheet";
		s.href=url;
	}
},
addJs:function(u){
	var s=F.tag("HEAD")[0].appendChild(document.createElement("script"));  
	s.type="text/javascript";  
	s.src=u;
},
addEvent:function(o,n,f){
	if(window.attachEvent) o.attachEvent("on"+n,function(){f(o)});
	else o.addEventListener(n,function(){f(o)},false)
},
format:function(v,len){
	if(v==void(0)||v==null) return "";
	v=(Math.round(parseFloat(v)*100)/100).toString();
	var p=v.indexOf(".");
	if(p==-1) v+=".00";
	else if(p==v.length-2) v+="0";
	else v=v.substring(0,p+3);
	if(len){var m=len-v.length;for(var i=0;i<m;i++)v="&nbsp;"+v;}
	return v;
},
tran:function(s,d){if(F.id(s))F.id(s).style.display="none";if(F.id(d))F.id(d).style.display="block";},
xmlHttp:function(){
	try	{
		if(window.XMLHttpRequest)return new XMLHttpRequest();
		if (window.ActiveXObject){
			try{return new ActiveXObject("MSXML2.XMLHTTP")}
			catch(e){ 
				try{return new ActiveXObject("Microsoft.XMLHTTP")}
				catch(e){location="http.html"}
			}
		}
	}catch(e){location="http.html";}
},
empty:function(o){for(var e in o)return false;return true},
top:function(o,t){
	if (!t)t="TR";
	var f=o.parentNode;
	while (f.tagName!=t)f=f.parentNode;
	return f;
},
addEvent:function(o,n,f){
	if(window.attachEvent) o.attachEvent("on"+n,function(){f(o)});
	else o.addEventListener(n,function(){f(o)},false)
},
get:function(o,b){
	var tags={INPUT:1,TEXTAREA:1,SELECT:1};
	if(o.get)return o.get();
	var v;
	if(o.type=="checkbox"||o.type=="radio"){
		if(o.id=="key"||o.checked)v=o.value;
		else v=o.getAttribute("none");
		return v;
	}
	if(tags[o.tagName]==1||o.getAttribute("need"))Check.exam(o);
	if(o.error)throw o.error;
	v=o.value;
	if(b||o.id=="key"){if(!v)v=o.data?o.data:o.getAttribute("data");return v;}
	if(v&&(!o.data||o.data!=v))return v;
},
showSelect:function(b,o){
	if(!F.ie)return;
	var l=document.getElementsByTagName("SELECT");
	for(var i=0;i<l.length;i++)l[i].style.visibility=(b?"visible":"hidden")
	if(!o) return;
	l=o.getElementsByTagName("SELECT");
	for(var i=0;i<l.length;i++)l[i].style.visibility="visible";
},
pop:function(o,n){
	var m=document.getElementById("mask"),p=o?o:document.getElementById("pop");
	if(!p)return;
	var q=p.getElementsByTagName("IMG")[0],w=screen.width,h=document.body.clientHeight;
	F.showSelect(false,p);
	q.onclick=function(){F.unpop(p)};
	if(F.ie){
		var t=q.parentNode;
		t.onmousedown=function(){
			var x=event.x,y=event.y,o=document;
			t.x=x-parseInt(p.style.left.replace("px",""));
			t.y=y-parseInt(p.style.top.replace("px",""));
			t.setCapture();
			o.onmousemove=function(){
				var x=F.ie?event.x:e.clientX,y=F.ie?event.y:e.clientY;
				p.style.left=x-t.x+"px";
				p.style.top=y-t.y+"px";
			}};
		t.onmouseup=function(){
			if(F.ie){t.releaseCapture();document.onmousemove=null}
			else {window.releaseEvents(Event.MOUSEMOVE);window.onmousemove=null}	};
	}
	if(h<screen.height)h=screen.height;
	m.style.height=h+"px";
	m.style.width=w+"px";
	h=screen.height-200;
	if(n)p.getElementsByTagName("SPAN")[0].innerHTML=n;
	m.style.display=p.style.display="block";
	document.documentElement.style.overflow="hidden";
	p.style.left=parseInt((document.documentElement.scrollLeft||document.body.scrollLeft)+(w-p.offsetWidth)/2)+"px";
	if(F.ie&&!F.ie7){
	window.onscroll=function(){p.style.top=parseInt((document.documentElement.scrollTop||document.body.scrollTop)+(h-p.offsetHeight)/2)+"px";};
	window.onscroll();
	}
	else p.style.top=parseInt((h-p.offsetHeight)/2)+"px";
},
unpop:function(p){
	document.documentElement.style.overflow="auto";
	document.documentElement.style.overflowX="hidden";
	var m=document.getElementById("mask");
	if(m)m.style.display="none";
	if(!p)p=F.id("pop");
	if(p)p.style.display="none";
	if(F.ie)window.onscroll=null;
	F.showSelect(true)
},
assign:function(o,c){
	var t=o.getAttribute("inbox");if(t){eval("o.inbox="+t)};
	if(t=o.getAttribute("name")){var w=F.id("warn_"+t);if(w)w.className="warn";}
	switch(o.getAttribute("group"))
	{	case "radio":
			o.set=function(v){this.value=this.data=v;if(this.kid[v])this.kid[v].checked=true};
			o.kid={};
			var l=F.tag("INPUT",o);
			for (var i=0;i<l.length;i++) 
			{	l[i].onclick=function(){o.value=this.value};
				o.kid[l[i].value]=l[i];
				if(l[i].checked)o.value=l[i].value;
			}
			return;
		case "checkbox":
			o.kid={};
			var l=F.tag("INPUT",o);
			o.set=function(v){
				this.value=this.data=v;
				for (var e in this.kid)this.kid[e].checked=false;
				var l=v.split(",");
				for (var i=0;i<l.length;i++)if(this.kid[l[i]])this.kid[l[i]].checked=true;
			};
			for (var i=0;i<l.length;i++) 
			{	l[i].onclick=function(){
				if(this.checked){
					var m=o.getAttribute("max");
					if(this.checked && m && o.value.toString().split(',').length>=m){ 
						alert(lang.maxsel[0]+m+lang.maxsel[1]);
						return this.checked=false;
					}
					o.value=(!o.value?this.value:o.value+","+this.value);
				}
				else o.value=(","+(!o.value?"":o.value)+",").replace(","+this.value+",",",").replace(/^,|,$/g,"");
				};
				o.kid[l[i].value]=l[i];
				o.value=(l[i].checked?(!o.value?l[i].value:o.value+","+l[i].value):("," + (!o.value?"":o.value) + ",").replace("," + l[i].value + ",",",").replace(/^,|,$/g,""));
			}
			return;
		case "order":
			c.all["order"]=o;
			o.root=c.name;
			var l=F.tag("*",o);
			for (var i=0;i<l.length;i++) 
			{	if(l[i].getAttribute("field")){
					l[i].onclick=function(){
						var s=this.getAttribute("by"),n=this.getAttribute("field");
						this.setAttribute("by",s=s=="desc"?"asc":"desc");
						if(o.pre)o.pre.style.backgroundImage="none";
						this.style.backgroundImage="url(/js/pic/"+s+".gif)";
						o.pre=this;
						o.value=n.indexOf(",")>=0?n.replace(","," "+s+",")+" "+s:n+" "+s;
						Search[o.root].select();};
					var s=l[i].getAttribute("by");
					if(s){
						l[i].style.backgroundImage="url(/js/pic/"+s+".gif)";
						var n=l[i].getAttribute("field");
						o.value=n.indexOf(",")>=0?n.replace(","," "+s+",")+" "+s:n+" "+s;
						o.pre=l[i];
					}
				}
			}
			o.get=function(){return this.value;}
			return;
		case "link":
			o.root=c;
			o.child={};
			o.set=function(v){	if(o.child[v])o.child[v].onclick();};
			var l=F.tag("A",o);
			for (var i=0;i<l.length;i++) 
			{	if(l[i].getAttribute("data")==void(0)) continue;
				o.child[l[i].getAttribute("data")]=l[i];
				l[i].onclick=function(){
					if(o.pre)o.pre.className="none";
					this.className="focus";
					o.pre=this;
					o.value=this.getAttribute("data");
					var n=this.getAttribute("list"),h=this.getAttribute("hide");
					if(n&&h)List[n].setHide(h);
					o.root.select();
					this.blur();
					return false;
				};
				if(l[i].className=="focus"){
					o.value=l[i].getAttribute("data");
					o.pre=l[i];
				};
			}
			return true;
		case "page":
			var l=F.tag("IMG",o);
			for(var i=0;i<l.length;i++)
			{	l[i].setAttribute("root",c.name);
				l[i].onmousedown=function(){this.parentNode.bgColor="#FF9900"};
				l[i].onmouseup=function(){this.parentNode.bgColor="#EEEEEE"};
				l[i].onclick=i==0?
				function(){var c=Control[this.getAttribute("root")],p=parseInt(F.get(c.all.page,true));c.page(isNaN(p)?1:p+1)}:
				function(){var c=Control[this.getAttribute("root")],p=parseInt(F.get(c.all.page,true));c.page(isNaN(p)?1:p-1)};
			}
			l=F.tag("INPUT",o)[0];
			if(!l) return;
			l.setAttribute("root",c.name);
			l.get=function(){var p=parseInt(this.value);return isNaN(p)?1:p;}
			l.onkeypress=function(e){e=window.event||e;var code=e.keyCode||e.which;if(code!=13) return;
				Control[this.getAttribute("root")].page(this.get())};
			return true;
		case "pageturn":
			o.setAttribute("root",c.name);
			o.set=function(s){
				this.innerHTML="";
				if(!s)return;
				s=s.split("|");
				var c=Control[this.getAttribute("root")],p=parseInt(s[0]),n=parseInt(s[1]);
				if (!n) return;
				if (n==1) return this.innerHTML="<font color=#ff0000>"+lang.ponly+"</font>";
				var q=parseInt(o.getAttribute("scale"));
				if(!q)q=10;
				var p1=p-q<1?1:p-q,p2=p+q>n?n:p+q;
				for(var i=p1-1;i<=p2+1;i++)
				{	var b=(p==i);
					var a=this.appendChild(document.createElement(b?"span":"a"));
					if(b)
					{	a.style.fontWeight="bold";
						a.style.color="#F00";
					}
					else
					{	a.className="link";
						a.href=".";
						if(i==p1-1)a.onclick=function(){c.page(1);return false};
						else if(i==p2+1)a.onclick=function(){c.page(n);return false};
						else{a.p=i;a.onclick=function(){c.page(this.p);return false}};
					}
					if(i==p1-1)a.innerHTML=lang.pbegin;
					else if(i==p2+1)a.innerHTML=lang.pend;
					else a.innerHTML="["+i+"]";
					this.appendChild(document.createTextNode(" "));
				}
			}
			c.all["pageturn"]=o;
			return;
		case "xml":
			o.kid={};
			o.init=function(k){
				if(k.id.indexOf("warn_")>=0) return k.old=k.innerHTML;
				var n=k.getAttribute("name");
				if(n){this.kid[n]=k;return F.assign(k);}
				for(var i=0;i<k.childNodes.length;i++)if(k.childNodes[i].nodeType==1)o.init(k.childNodes[i]);
			};
			o.get=function(){
				var p={},i=0,t;
				for(var e in this.kid)if(t=F.get(this.kid[e],true)){p[e]=t;i++;}
				if(i==0) return;
				var s=""
				for(var e in p){if(s)s+=","; s+='"'+F.xmlIn(e)+'":"'+F.xmlIn(p[e])+'"';}
				return s;
			};
			o.set=function(v){
				var d=eval("T={"+v+"}");
				for(var e in this.kid){
					e=F.xmlOut(e);
					var w=F.id("warn_"+e);
					if(w){w.className="warn";w.innerHTML=w.old;}
					this.kid[e].set(F.xmlOut(d[e]));
				}
			};
			for(var i=0;i<o.childNodes.length;i++)if(o.childNodes[i].nodeType==1)o.init(o.childNodes[i]);
			return;
		case "code": return init_code(o);
	}
	if(o.tagName=="TEXTAREA"||o.tagName=="SELECT"||o.type=="text"||o.type=="password"){
		o.set=function(v){this.value=this.data=v;if(this.inbox)this.inbox(v)};
		F.addEvent(o,"focus",Check.focus);
		o.onblur=function(){Check.exam(this)};
	}
	else if(o.type=="hidden")o.set=function(v){this.value=v;if(this.inbox)this.inbox(v)};
	else if(o.type=="radio"||o.type=="checkbox")
	{
		if(o.id=="key") o.set=function(v){o.value=v;this.checked=false;if(this.inbox)this.inbox(v)};
		else o.set=function(v){this.checked=(this.value==v?true:false);if(this.inbox)this.inbox(v)};
	}
	else if(o.tagName=="IMG")o.set=function(v){this.data=v;if(this.inbox)this.inbox(v);else this.src=v;};
	else if(o.tagName=="A"){o.set=function(v){this.data=v;if(this.inbox)this.inbox(v);else this.href=v;};return true;}
	else{o.set=function(v){this.data=v;if(this.inbox)this.inbox(v);else this.innerHTML=v;};return true;}
},
split:function(s){
	var o={};if(!s||s=="")return o;	
	try
	{	var l=s.split("&");
		for (i=0; i<l.length; i++)
		{	var t=l[i].split("=");
			if(t.length!=2) continue;
			o[t[0].trim()]=unescape(t[1].trim());
		}
	}catch(e){}
	return o;
},
reset:function(o){
	if(o.id=="key")return;
	if(o.value)o.value=o.defaultValue;
	if(o.checked)o.checked=o.defaultChecked;
	if(o.selectedIndex)o.selectedIndex=0;
	if(o.old!=void(0))o.innerHTML=o.old;
},
search:function(n,o){
if(!Search[n])
Search[n]={
	name:n,
	all:{},
	control:[],
	select:function(){
		try	
		{	var p=this.xml();
			for (var i=0;i<this.control.length;i++) Control[this.control[i]].select(p);
		}
		catch(e){alert(e.message)}
	},
	reset:function(){for(var e in this.all) F.reset(this.all[e])},
	xml:function(){
		var p="";
		for(var e in this.all)
		{	var t=F.get(this.all[e]);
			if(t)p+="<"+e+">"+ F.xmlIn(t)+"</"+e+">";
		}
		return p;
	},
	init:function(o){
		var t,n=o.getAttribute("name");
		if(t=o.getAttribute("para")){eval("t={"+t+"}");for(var e in t) this.all[e]={get:function(){return t[e]}};}
		if(t=o.getAttribute("act")){
			o.setAttribute("root",this.name);
			return o.onclick=function(){Search[this.getAttribute("root")].select()};
		}
		if(n){
			b=F.assign(o,this);
			if(F.query[n])o.set(F.query[n]);
			return this.all[n]=o;
		}
		for(var i=0;i<o.childNodes.length;i++)if(o.childNodes[i].nodeType==1)this.init(o.childNodes[i]);
	}};
	Search[n].init(o);
},
control:function(n,o,b){
	if(!Control[n])
	Control[n]={
		name:n,
		all:{},
		para:{},
		info:{},
		control:{},
		table:{},
		init:function(o){
				if(o.tagName=="FORM")return;
				if(o.id.indexOf("warn_")>=0){o.old=o.innerHTML;return this.info[o.id]=o;}
				var t,b=true;
				if(t=o.getAttribute("search")) return F.search(t,o);
				if(t=o.getAttribute("act"))
				{	o.root=this.name;
					return o.onclick=function(){
						this.blur();
						var c=Control[this.root],a=this.getAttribute("act");
						c.source=this;
						if(a=="key")c.act(F.get(c.key,true));
						else if(a=="keylist")c.act(c.keylist());
						else c.submit();
						return false};
				}
				var n=o.getAttribute("name"),g=o.getAttribute("group");;
				if(n&&n!="special"){
					if(o.getAttribute("list"))
					{	if(t=o.tHead){
						for(var i=0;i<t.childNodes.length;i++)if(t.childNodes[i].nodeType==1)this.init(t.childNodes[i])};
						if(t=o.tFoot){
						for(var i=0;i<t.childNodes.length;i++)if(t.childNodes[i].nodeType==1)this.init(t.childNodes[i])};
						this.all[n]=F.list(n,o,this);
						if(t=o.getAttribute("hide"))this.all[n].setHide(t);
						return;
					}
					else if(g=="link"){
						if(!Search[this.search])F.search(this.search);
						b=F.assign(o,Search[this.search]);Search[this.search].all[n]=o;
					}
					else{b=F.assign(o,this);this.all[n]=o;}
				}
				else if(g) b=F.assign(o,this);
				if(o.id=="key") this.key=o;
				if(b)for(var i=0;i<o.childNodes.length;i++)if(o.childNodes[i].nodeType==1)this.init(o.childNodes[i]);
		},
		keylist:function(){for(var e in this.table)return List[this.table[e]].keylist();},
		selected:function(){for(var e in this.table)return List[this.table[e]].selected();},
		act:function(p){
			if(!Act_Before(this.source,this,p)) return;
			try{Service.call(this.source.getAttribute("cmd"),"<id>"+F.xmlIn(p)+"</id>",this)}
			catch(e){this.abort(e)}
		},
		submit:function(){
			var p={},t,i=0;
			try
			{	for(var e in this.all)if(!this.all[e].ref&&(t=F.get(this.all[e]))){p[e]=t;i++;}
				if(!Act_Before(this.source,this,p)) return;
				if(i==0)throw {message:lang.form}
				var s=""
				for(var e in p)s+=" "+e+'="'+F.xmlIn(p[e])+'"';
				Service.call(this.source.getAttribute("cmd"),"<data><row"+s+"/></data>",this);
			}
			catch(e){this.abort(e)}
			return false;
		},
		page:function(p){this.fresh(p)},
		abort:function(e){
			this.source=null;
			this.busy=false;
			alert(e.message);
		},
		fresh:function(p){
			if(!Select_Before(this)) return;
			this.action="select";
			var t,s="",l=['page','pagesize','order'];
			if(p){
				if(this.all.page)this.all.page.set(p);
				for(var e in this.table)List[e].wait();
			}
			for(var i=0;i<l.length;i++)if(this.all[l[i]])s+="<"+l[i]+">"+F.get(this.all[l[i]],true)+"</"+l[i]+">";
			try{Service.call(this.cmd,"<para>"+this.oldpara+s+"</para>",this)}
			catch(e){this.abort(e)}
		},
		paraxml:function(){
			var p="";
			for(var e in this.para)p+="<"+e+">"+this.para[e]+"</"+e+">";
			return p;
		},
		select:function(s){
			if(!s)s=this.search?Search[this.search].xml():"";
			s+=this.paraxml();
			this.oldpara=s;
			this.fresh(1);
		},
		reset:function(){
			for(var e in this.all)F.reset(this.all[e]);
			for(var e in this.info)F.reset(this.info[e]);
		},
		adapt:function(d){
			if(this.action=="select"){
				for(var e in this.all){
					var w=F.id("warn_"+e);
					if(w){w.className="warn";w.innerHTML=w.old;}
					if(e=="pagesize"){if(d[e])this.all[e].set(d[e]);}
					else this.all[e].set(d[e]!=null?d[e]:((e=="page"||e=="maxcount"||e=="maxpage")?0:""));
				}
				Select_After(this,d);
			}
			else{
				for(var e in d) if(this.all[e])this.all[e].set(d[e]);
				if(this.source)	Act_After(this.source,this,d);
			}
			this.busy=false;
			this.action="";
			this.source=null;
			if(this.all.validcode){
				this.all.validcode.set("");
				var t=F.id("validpic");
				t.src="/getcode.ashx?"+Date.parse(new Date());
			}
		}
	};
	var c=Control[n];
	if(o.getAttribute("control")){
		var t;
		if(t=o.getAttribute("cmd"))c.cmd=t;
		if(t=o.getAttribute("select")){c.cmd=t;c.auto=true;}
		if(t=o.getAttribute("para"))eval("c.para="+t);
		if(!(t=o.getAttribute("query")))t="common";
		if(t!="none") c.search=t;
	}
	c.init(o);
},
list:function(n,o,p){
	if(!List[n])
	List[n]={
		name:n,
		ref:o,
		base:[],
		col:1,
		row:[],
		all:[],
		setHide:function(v){
			this.hide=v?eval(v):[];
			var th=this.ref.tHead.getElementsByTagName("TH");
			for(var j=0;j<th.length;j++){
				var b=false;
				for(var k=0; k<this.hide.length; k++){
					if(this.hide[k]==j){b=true;break;}
				}
				th[j].style.display=b?"none":"";
			}
		},
		wait:function(b){
			var s=this.ref.tBodies;
			if(s[1]||s[2]){
				if(s[2])s[2].style.display=b?"none":"";
				s[0].style.display=b==1?"":"none";
				s[1].style.display=b==2?"":"none";
			}
		},
		kind:o.getAttribute("list"),
		init:function(o,k){
			if(!this.all[k])this.all[k]={};
			var n=o.getAttribute("act");
			if(n){
				o.setAttribute("root",this.name);
				o.setAttribute("line",k);
				o.onclick=function(){
					this.blur();
					var l=List[this.getAttribute("root")],k=parseInt(this.getAttribute("line")),n=this.getAttribute("act");
					l.source=this;
					if(n=="key")l.act(k,F.get(l.all[k][l.key],true));
					else l.submit(k);
					return false;
				};
			}
			if(n=o.getAttribute("name"))
			{	if(o.getAttribute("list"))
				{	if(n=o.tHead){
					for(var i=0;i<n.childNodes.length;i++)if(n.childNodes[i].nodeType==1)this.init(n.childNodes[i],k)};
					if(n=o.tFoot){
					for(var i=0;i<n.childNodes.length;i++)if(n.childNodes[i].nodeType==1)this.init(n.childNodes[i],k)};
					return;
				}
				var b=F.assign(o);
				if(o.id=="key")this.key=n;
				this.all[k][n]=o;
				if(!b)return;
			}
			for(var i=0;i<o.childNodes.length;i++)if(o.childNodes[i].nodeType==1)this.init(o.childNodes[i],k);
		},
		act:function(k,p){
			if(!Act_Before(this.source,this.all[k],p)) return;
			var cmd=this.source.getAttribute("cmd");
			if(!cmd)return;
			this.line=k;
			try{Service.call(cmd,"<id>"+F.xmlIn(p)+"</id>",this)}
			catch(e){this.abort(e)}
		},
		submit:function(k){
			var p={},t,i=0;
			try{
				for(var e in this.all[k])if((t=F.get(this.all[k][e]))!=null){p[e]=t;i++;}
				if(this.source&&!Act_Before(this.source,this.all[k],p)) return;
				if(i==0) throw{message:lang.form}

				this.line=k;
				var s=""
				for(var e in p) s+=" "+e+"=\"" + F.xmlIn(p[e])+"\"";
				Service.call(this.source.getAttribute("cmd"),"<data><row"+s+"/></data>",this);
			}
			catch(e){this.abort(e)}
		},
		abort:function(e){
			this.source=null;
			this.busy=false;
			alert(e.message);
		},
		adapt:function(d){
			for(var e in d)if(this.all[this.line][e])this.all[this.line][e].set(d[e]);
			this.busy=false;
			if(this.source)Act_After(this.source,this.all[this.line],d);
			this.source=null;
		},
		gen:function(m){
			var b=this.base,u=this.row.length,c=this.col;
			if(this.kind=="grid")
			{	for(var i=u;i<m;i++)
				{	var k=Math.floor(i/c);
					if(k>=this.row.length)	this.row[k]=b[0].root.appendChild(b[0].ref.cloneNode(true));
					for(var j=0;j<c;j++) this.init(this.row[k].cells[j],i*c+j);
				}
			}
			else
			{	for(var i=u;i<m;i++)
				{	this.row[i]=[];
					for(var j=0;j<b.length;j++)
					{	var tr=b[j].root.appendChild(b[j].ref.cloneNode(true));
						tr.className=i%2==0?"r1":"r2";
						this.init(this.row[i][j]=tr,i);
					}
				}
			}
		},
		selected:function(){
			var p=[];
			if(!this.key)return p;
			for(var i=0;i<this.all.length;i++)
				if(this.all[i][this.key].checked && this.all[i][this.key].value) p[p.length]=this.all[i];
			return p;
		},
		keylist:function(){
			var p=this.selected(),s="";
			for(var i=0;i<p.length;i++)s+=F.get(p[i][this.key])+",";
			return s.substring(0,s.length-1);
		},
		set:function(v){
			var r=this.row,c=this.col,l=0;
			for(var e in v){l=v[e].length;break};
			if(l==0)return this.wait(2);
			if(this.all.length<l) this.gen(l);
			for(var i=0;i<this.all.length;i++)for(var e in this.all[i])this.all[i][e].set(i<l&&v[e]?v[e][i]:"");
			l=Math.floor((l-1)/c)+1;
			var k=0;
			for(;k<l;k++)for(var j=0;j<r[k].length;j++) r[k][j].style.display="";
			for(;k<r.length;k++)for(var j=0;j<r[k].length;j++) r[k][j].style.display="none";
			if(this.hide){
				var tr=this.ref.tBodies[0].rows;
				for(var i=0;i<tr.length;i++){
					var td=tr[i].getElementsByTagName("TD");
					for(var j=0;j<td.length;j++){
						var b=false;
						for(var k=0; k<this.hide.length; k++){
							if(this.hide[k]==j){b=true;break;}
						}
						td[j].style.display=b?"none":"";
					}
				}
			}
			this.wait(1);
		}
	};
	var t=List[n],h=o.tBodies[0],l=h.childNodes,b=h.rows[0].bgColor,w=h.rows[0].cells.length;
	switch(t.kind)
	{	case "flat":
			for(var i=0;i<l.length;i++)if(l[i].nodeType==1)t.base[t.base.length]={root:l[i],ref:l.removeChild(l[i].cells[1])};
			break;
		case "grid":
			var tr=h.rows,k=0;
			for(var i=0;i<tr.length;i++)
			{	t.row[i]=[tr[i]];
				var td=tr[i].cells;
				for(var j=0;j<td.length;j++){t.init(td[j],k);k++}
				if(i==0)t.col=td.length;
				t.base[t.base.length]={root:h,ref:tr[i]};
			}
			break;
		default:
			while(l.length>0){
				var e=h.removeChild(l[0]);
				if(e.nodeType==1)t.base[t.base.length]={root:h,ref:e};
			}
			break;
	};
	if(o.getAttribute("loading")){
		var u=o.tBodies.length,m=(m=o.getAttribute("min"))?parseInt(m):200,u=o.insertBefore(document.createElement("tbody"),o.tFoot),v=u.appendChild(document.createElement("tr")).appendChild(document.createElement("td"));
		u.style.display="none";
		v.colSpan=w;
		if(b)v.bgColor=b;
		v.vAlign="middle";
		v.align="center";
		v.style.height=m+"px";
		v.innerHTML="<div style='letter-spacing:1px;line-height:24px'>Loading Data...<br/><img src='/js/pic/loading.gif' width=187 height=10></div>";
	}
	if(!p.table[n])p.table[n]=t.name;
	return t;
}};
F.query=F.split(location.search.replace("?",""));
function init(o){
	var lang=document.body.lang;
	F.addJs("/js/lang/"+(lang?lang:"zh")+".js");
	if(Cookie.get("islogin")!="ok") Login_Before();
	if(!o)o=document.body;
	initLoop(o);
	for(var e in Control){
		var c=Control[e],t;
		if(t=c.search){
			if(Search[t])Search[t].control[Search[t].control.length]=e;
			else c.search=void(0);
		}
		if(c.cmd&&c.auto)c.select();
	}
};
function initLoop(o){
	var n;
	if(n=o.getAttribute("control"))F.control(n,o);
	else if(n=o.getAttribute("search")) F.search(n,o);
	else {for(var i=0;i<o.childNodes.length;i++)if(o.childNodes[i].nodeType==1)initLoop(o.childNodes[i]);}
};
var Hot={
	para:[],
	clear:function(){this.para.length=0;},
	add:function(o,w,h){this.para[this.para.length]={o:o,w:w,h:h};},
	exec:function(v){
		if(v>=this.para.length) return;
		var p=this.para[v];
		try{this.set(p.o,p.w,p.h);} catch(e){}
	},
	start:function(d){if(!d)d=300;for(var i=0;i<this.para.length;i++)setTimeout("Hot.exec("+i+")",d*i);},
	set:function(o,mw,mh){
	try{
		if(!o) return;
		var d=document.getElementById("hotrect");
		if(!d){d=document.body.appendChild(document.createElement("DIV"));d.id="hotrect";}
		var t=o,lx=0,ly=0;if(!d)return;
		for (; t!=null; lx+=t.offsetLeft,ly+=t.offsetTop,t=t.offsetParent);
		if(!mw)mw=300; if(!mh)mh=300;
		var a=(mw-o.offsetWidth)/12,b=(mh-o.offsetHeight)/12;
		d.style.width=mw+"px";
		d.style.height=mh+"px";
		d.style.left=parseInt(lx-(mw-o.offsetWidth)/2)+"px";
		d.style.top=parseInt(ly-(mh-o.offsetHeight)/2)+"px";
		if(F.ie)d.filters.Alpha.opacity=100; else d.style.MozOpacity=1;
		this.cfg={fly:d,source:o,x:lx,y:ly,"a":a,"b":b,w:o.offsetWidth,h:o.offsetHeight,time:setTimeout("Hot.fly()",10)};
		d.style.display="block";
	}catch(e){}
	},
	fly:function(){
	try{
		var d=this.cfg.fly;if(!d)return;
		clearTimeout(this.cfg.time);
		if(d.offsetWidth>this.cfg.w+2||d.offsetHeight>this.cfg.h+2){
			d.style.width=parseInt(d.offsetWidth-this.cfg.a>this.cfg.w?d.offsetWidth-this.cfg.a:this.cfg.w-2)+"px";
			d.style.height=parseInt(d.offsetHeight-this.cfg.b>this.cfg.h-2?d.offsetHeight-this.cfg.b:this.cfg.h-2)+"px";
			d.style.left=parseInt(this.cfg.x-(d.offsetWidth-2-this.cfg.w)/2)+"px";
			d.style.top=parseInt(this.cfg.y-(d.offsetHeight-2-this.cfg.h)/2)+"px";
			return this.cfg.time=setTimeout("Hot.fly()",10);
		}
		if(F.ie&&d.filters.Alpha.opacity>0){d.filters.Alpha.opacity-=5;this.cfg.time=setTimeout("Hot.fly()",10);}
		else if(!F.ie&&d.style.MozOpacity>0){d.style.MozOpacity-=0.05;this.cfg.time=setTimeout("Hot.fly()",10);}
		else{d.style.display="none";d.style.left=d.style.top="0px";}
	}catch(e){}
	}
};
var Turn={
	set:function(o,v){
		if(this.cfg) clearTimeout(this.cfg.time);
		this.cfg={ref:o,txt:o.getAttribute("text"),pos:0,inc:(v?v:1),time:setTimeout("Turn.fly()",5)};
		this.cfg.len=this.cfg.txt.length;
	},
	fly:function(){
		if(!this.cfg||this.cfg.pos>=this.cfg.len+this.cfg.inc) return;
		var s=this.cfg.txt.substring(0,this.cfg.pos);
		for(var i=this.cfg.pos;i<this.cfg.len;i++) s+=String.fromCharCode(Math.random()*100%25+97);
		this.cfg.pos+=this.cfg.inc;
		this.cfg.ref.innerHTML=s;
		this.cfg.time=setTimeout("Turn.fly()",5);
	}
};
function trancolor(o,b){
	var l=o.getElementsByTagName("INPUT");
	o.style.backgroundColor=b?'#EBF1FF':'white';
	for(var i=0;i<l.length;i++)
	if(l[i].type=="text"||l[i].type=="password"){
		if(b==true)l[i].style.backgroundColor=l[i].readOnly==true?o.style.backgroundColor:"white";
		else l[i].style.backgroundColor='white';
	}
}
function trans(o,b){o.className=b?"show":"";}
Cookie.set("zone",new Date().getTimezoneOffset()*-1,24000);