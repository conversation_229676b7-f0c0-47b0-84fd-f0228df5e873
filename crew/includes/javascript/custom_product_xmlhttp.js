var remark_link_pool = Array();
var asynMode = true;

function DOMCall(name) {
	if (document.getElementById)
    	return document.getElementById(name);
	else if (document.all)
		return document.all[name];
	else if (document.layers)
		return document.layers[name];
}

function initInfoCaptions() {
   	// create a dynamic layer and attach it to the HTML document.
	objBody = document.getElementsByTagName("body").item(0);
    objContainer = document.createElement('div');
    objContainer.setAttribute('id', 'infocaption');
    
    objBody.appendChild(objContainer);
}

//Get List of cdkeys for this product
function getCDKeys(product_no) {
	var numRows = document.getElementById('cdkey_rows_' + product_no).value;

	var cdkey_id_arr = new Array();
	for (i=0; i < numRows; i++)
	{
		cdkey_id_arr[i] = document.getElementById("gcp_code_id_" + product_no + "[" + i + "]").value;
	}
	return cdkey_id_arr;

}

//Get batch CDKEY
function getCDKeyImg_list(styleClass, product_no)
{
	var cdkey_id_arr = new Array();
	cdkey_id_arr = getCDKeys(product_no);

	if (styleClass == 'hide') {
		hideShow_list('unloading', product_no);
		for (i=0; i < cdkey_id_arr.length; i++) {
      		var cdkey_tbody = DOMCall('cdkey_img_' + cdkey_id_arr[i] + '_sec');
			cdkey_tbody.className = 'hide';
			//Update the individual key's show/hide link
			hideShow('hide', cdkey_id_arr[i], product_no);
  		}
  		hideShow_list(styleClass, product_no);
	} else {			
		hideShow_list('loading', product_no);
		getCDKeyImg_multiple(cdkey_id_arr, "<?=SID;?>", product_no);
	}
}

function getCDKeyImg_multiple(cdkey_id_arr, product_no) {
    var server_action 	= 'get_cdkey_image_multiple';
    var ref_url		  	= "custom_product_xmlhttp.php?action="+server_action+"&cp_id="+cdkey_id_arr;
    var item_no		  	= product_no.split('_');

    jQuery.ajax({
        url:ref_url,
        dataType: 'xml',
        timeout: 60000,
        error: function(){

        },
        success: function(xml) {
            for (i=0; i < item_no[1]; i++) {
                var j = i + 1;
                var nav_div		= DOMCall("nav_" + item_no[0] + '_' + j);
                var cdkey_image = jQuery(xml).find("cdkey_image > image_" + cdkey_id_arr[i]).text();

                nav_div.innerHTML = cdkey_image;
                //Update the individual key's show/hide link
                hideShow('show', cdkey_id_arr[i], product_no);
            } //end for

            hideShow_list('show', product_no);
        }
    });
}

//Get single CDKEY
function getCDKeyImg(styleClass, cdkey_id, SID, product_no) {
    var nav_div = DOMCall('nav_' + product_no);
    var sub_div = DOMCall("nav_sub_" + cdkey_id);

    if (styleClass == 'hide') {
        hideShow('unloading', cdkey_id, product_no);
        hideShow(styleClass, cdkey_id, product_no);
    } else {
        hideShow('loading', cdkey_id, product_no);

        var server_action = 'get_cdkey_image';

        var ref_url = "custom_product_xmlhttp.php?action="+server_action+"&cp_id="+cdkey_id+"&SID="+SID;

        jQuery.ajax({
            url:ref_url,
            dataType: 'xml',
            timeout: 60000,
            error: function(){

            },
            success: function(xml) {
                nav_div.innerHTML = jQuery(xml).find('cdkey_image').text();
                hideShow(styleClass, cdkey_id, product_no);
            }
        });
    } // end else
}

// add item to select element the less elegant, but compatible way.
function appendToSelect(select, value, content) {
	var opt = document.createElement('option');
	opt.value = value;
	opt.text = content;
	select.options.add(opt);
}

function clearOptionList(obj) {
	while (obj.options.length > 0) {
		obj.remove(0);
	}
}

function showMainInfo(DisplayMsg) {
	hideMainInfo();
	if (typeof DisplayMsg == 'undefined') {
		return;
	}
	var objInfo = DOMCall('infocaption');
	var scroll = getScroll();
	
	objInfo.innerHTML = '<div class="xmlHttpMainInfo">' + DisplayMsg + '</div>';
	objInfo.style.visibility = 'visible';
	
	objInfo.style.position = "absolute";
	
	objInfo.style.top = 5 + scroll.y;
	
	var msg_left_pos = getWindowWidth() - objInfo.offsetWidth;
	objInfo.style.left = msg_left_pos - 20;
}

function hideMainInfo() {
	var objInfo = DOMCall('infocaption');
	objInfo.style.visibility = 'hidden';
	objInfo.innerHTML = '';
}

function getWindowWidth() {
	var windowWidth = 0;
	if (typeof(window.innerWidth) == 'number') {
		windowWidth = window.innerWidth;
	} else {
		if (document.documentElement && document.documentElement.clientWidth) {
			windowWidth = document.documentElement.clientWidth;
		} else {
			if (document.body && document.body.clientWidth) {
				windowWidth = document.body.clientWidth;
			}
		}
	}
	return windowWidth;
}

function getScroll() {
    if (document.body.scrollTop != undefined) { // IE model
        var ieBox = document.compatMode != "CSS1Compat";
        var cont = ieBox ? document.body : document.documentElement;
        return {x : cont.scrollLeft, y : cont.scrollTop};
    } else {
        return {x : window.pageXOffset, y : window.pageYOffset};
    }
}

window.onload = function() {
	initInfoCaptions();
}



