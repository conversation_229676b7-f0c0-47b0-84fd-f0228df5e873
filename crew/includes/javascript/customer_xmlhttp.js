var stateSelectionTitle = Array();
stateSelectionTitle['id'] = '';
stateSelectionTitle['text'] = 'Please Select';

function DOMCall(name) {
	if (document.getElementById)
    	return document.getElementById(name);
	else if (document.all)
		return document.all[name];
	else if (document.layers)
		return document.layers[name];
}

function initInfoCaptions() {
   	// create a dynamic layer and attach it to the HTML document.
	objBody = document.getElementsByTagName("body").item(0);
    objContainer = document.createElement('div');
    objContainer.setAttribute('id', 'infocaption');
    
    objBody.appendChild(objContainer);
}

function refreshCountryCode (country_id, sel1) {
	var div_objRef1 = DOMCall(sel1);
	var server_action = 'refresh_country_code';
	
	div_objRef1.innerHTML = '';
	
	if (country_id.value != '') {
		var ref_url = "customer_xmlhttp.php?action="+server_action+"&country_id="+country_id.value;
		
		div_objRef1.innerHTML = 'Loading...';
		
         jQuery.ajax({
            url:ref_url,
            dataType: 'xml',
            timeout: 60000,
            error: function(){
            },
            success: function(xml) {
                var country_code = jQuery(xml).find('country_dialing_code').text();
	      		
                div_objRef1.innerHTML = '+' + country_code;
            }
        });	
	}
}

function verifyEmail(code, error_msg) {//## ONLY IN CHECKOUT SUCCESS ##
	var error_msg = error_msg || ''; // support multiple language
	
	var server_action = 'verify_email';
	var ref_url = "customer_xmlhttp.php?action="+server_action+"&code="+code;

	jQuery('.NonJSEConfirmEmail').disabled = true;
	document.getElementById('email_verify_div').innerHTML = '<table border="0" cellpadding="0" cellspacing="0"><tr><td class="main">Please Wait...</td></tr></table>';
	
	if (code.length != 12) {
		document.getElementById('email_verify_div').innerHTML = '<table border="0" cellpadding="0" cellspacing="0" width="100%"><tr><td class="main"><span class="redIndicator">'+error_msg+'</span></td></tr></table>';
		jQuery('.NonJSEConfirmEmail').disabled = false;
	} else {
        jQuery.ajax({
            url:ref_url,
            dataType: 'xml',
            timeout: 60000,
            error: function(){

            },
            success: function(xml) {
                var verify = jQuery(xml).find('verify').text();
	      		var email_address = jQuery(xml).find('email_address').text();
	      		if (typeof(verify) != 'undefined' && verify != null) {
	      			if (verify == 1) {
	      				var result_html = '<table border="0" cellpadding="0" cellspacing="0" width="100%">';
	      				result_html += '<tr>';
	      				result_html += '<td class="inputLabel" width="20%" nowrap>E-mail Address:</td>';
	      				result_html += '<td><table border="0" cellpadding="0" cellspacing="0"><tr><td class="inputField" nowrap>'+email_address+'</td><td>&nbsp;<img src="/images/icons/tick.gif"></td></tr></table></td>';
	      				result_html += '</tr>';
	      				result_html += '</table>';
	      				
	      				document.getElementById('js_email_first_location_mode').innerHTML = result_html;
	      			} else {
	      				document.getElementById('email_verify_div').innerHTML = '<table border="0" cellpadding="0" cellspacing="0" width="100%"><tr><td class="main"><span class="redIndicator">Invalid Verification Code.</span></td></tr></table>';
						jQuery('.NonJSEConfirmEmail').disabled = false;
	      			}
	      		}
            }
        });		
	}
}
//Not longer using, replace by action in checkout_success page
//function resendSerial() {
//	var server_action = 'resend_serial';
//	var ref_url = "customer_xmlhttp.php?action="+server_action;
//	
//	document.email_verify_form.NonJSEConfirmEmail.disabled = true;
//	
//	xmlhttp.open("GET", ref_url);
//	xmlhttp.onreadystatechange = function() {
//      	if (xmlhttp.readyState == 4 && xmlhttp.status == 200) {
//      		var resend = xmlhttp.responseXML.getElementsByTagName("resend")[0];
//      		var email_address = xmlhttp.responseXML.getElementsByTagName("email_address")[0];
//      		
//      		if (typeof(resend) != 'undefined' && resend != null) {
//      			if (resend.firstChild.data == 1) {
//      				document.getElementById('email_verify_div').innerHTML = '<table border="0" cellpadding="0" cellspacing="0" width="100%"><tr><td class="main"><span class="greenIndicator">A new activation code has been sent.</span></td></tr></table>';
//	      			document.email_verify_form.NonJSEConfirmEmail.disabled = false;
//      			} else if (resend.firstChild.data == 2) {
//      				var result_html = '<table border="0" cellpadding="0" cellspacing="0" width="100%">';
//      				result_html += '<tr>';
//      				result_html += '<td class="inputLabel" width="20%" nowrap>E-mail Address:</td>';
//      				result_html += '<td><table border="0" cellpadding="0" cellspacing="0"><tr><td class="inputField" nowrap>'+email_address.firstChild.data+'</td><td>&nbsp;<img src="/images/icons/tick.gif"></td></tr></table></td>';
//      				result_html += '</tr>';
//      				result_html += '</table>';
//      				
//      				document.getElementById('js_email_first_location_mode').innerHTML = result_html;
//      			} else {
//      				document.getElementById('email_verify_div').innerHTML = '<table border="0" cellpadding="0" cellspacing="0" width="100%"><tr><td class="main"><span class="redIndicator">A error occur, please try later.</span></td></tr></table>';
//      				document.email_verify_form.NonJSEConfirmEmail.disabled = false;
//      			}
//      		}
//      	}
//    }
//    xmlhttp.send(null);
//}

//Not longer using, replace by verifyTelephone() in checkout_success and account_activate page.
//function verifyTelephone(sel1, sel2, sel3, sel4) {
//	var div_objRef1 = DOMCall(sel1);
//	var div_objRef2 = DOMCall(sel2);
//	var div_objRef3 = DOMCall(sel3);
//	var div_objRef4 = DOMCall(sel4);
//	var server_action = 'verify_telephone';
//	var ref_url = "customer_xmlhttp.php?action="+server_action;
//	
//	div_objRef1.innerHTML = 'Loading...';
//	div_objRef2.innerHTML = 'Loading...';
//	div_objRef3.innerHTML = 'Loading...';
//	div_objRef4.innerHTML = 'Loading...';
//    
//	jQuery.ajax({
//        url:ref_url,
//        dataType: 'xml',
//        timeout: 60000,
//        error: function(){
//
//        },
//        success: function(xml) {
//            var goto_url = jQuery(xml).find('goto_url').text();
//            var heading_message = jQuery(xml).find('heading_message').text();
//            var confirmation_table = jQuery(xml).find('confirmation_table').text();
//            var telephone_table = jQuery(xml).find('telephone_table').text();
//            var error_link = jQuery(xml).find('error_link').text();
//
//            if (typeof (goto_url) != 'undefined' && goto_url != null) {
//            window.location = goto_url;
//            } else {
//                if (typeof (telephone_table) != 'undefined' && telephone_table != null) {
//                    div_objRef1.innerHTML = telephone_table;
//                }
//
//                if (typeof (confirmation_table) != 'undefined' && confirmation_table != null) {
//                    div_objRef2.innerHTML = confirmation_table;
//                }
//
//                if (typeof (heading_message) != 'undefined' && heading_message != null) {
//                    div_objRef3.innerHTML = heading_message;
//                }
//
//                if (typeof (error_link) != 'undefined' && error_link != null) {
//                    div_objRef4.innerHTML = error_link;
//                }
//            }
//        }
//    });	
//}

//Not longer using
//function confirmTelephone(code) {
//	var server_action = 'confirm_telephone';
//	var ref_url = "customer_xmlhttp.php?action="+server_action+"&code="+code;
//	jQuery.ajax({
//        url:ref_url,
//        dataType: 'xml',
//        timeout: 60000,
//        error: function(){
//
//        },
//        success: function(xml) {
//            var result = jQuery(xml).find('result').text();
//            var goto_url = jQuery(xml).find('goto_url').text();
//            if (typeof (result) != 'undefined' && result != null) {
//				alert(result);
//			}
//            if (typeof (goto_url) != 'undefined' && goto_url != null) {
//				window.location = goto_url;
//			}
//        }
//    });	
//}

//Replace by refreshStateList() in account_edit.tpl.php
//function refreshDynamicSelectOptions (sel1, sel2, sel3, _lang, show_title, onfocus_css, onblur_css) {
//	var div_objRef = DOMCall(sel2);
//	var objRef = DOMCall(sel3);
//	var res;
//    var server_action = 'state_list';
//    var selection_available = false;
//    
//    if (objRef.type == 'text') {
//    	objRef = document.createElement("SELECT");
//    	objRef.name = sel3;
//    	objRef.id = sel3;
//    	objRef.className = onblur_css;
//    	objRef.setAttribute('onfocus', 'input_on_focus(this, \''+onfocus_css+'\')');
//    	objRef.setAttribute('onblur', 'input_on_blur(this, \''+onblur_css+'\')');
//    } else {
//    	clearOptionList(objRef);
//    }
//    
//    div_objRef.innerHTML = 'Loading ...';
//    sel1.disabled = true;
//	
//	var ref_url = "customer_xmlhttp.php?action="+server_action+"&country_id="+sel1.value+"&lang="+_lang;
//	xmlhttp.open("GET", ref_url); 
//    xmlhttp.onreadystatechange = function() { 
//      	if (xmlhttp.readyState == 4 && xmlhttp.status == 200) { 
//      		//res = xmlhttp.responseText;
//      		
//      		clearOptionList(objRef);
//      		var selection = xmlhttp.responseXML.getElementsByTagName("selection")[0];
//      		
//      		if (typeof (selection) != 'undefined' && selection != null) {
//      			if (show_title == true) {
//      				appendToSelect(objRef, stateSelectionTitle['id'], stateSelectionTitle['text']);
//      			}
//      			
//      			var option_sel = '';
//	      		for (var i=0; i < selection.childNodes.length; i++) {
//	      			option_sel = selection.getElementsByTagName("option")[i];
//	      			appendToSelect(objRef, option_sel.getAttribute("index"), option_sel.firstChild.nodeValue);
//			    }
//      		}
//      		
//      		if ((show_title == true && objRef.options.length > 1) || (show_title == false && objRef.options.length > 0)) {
//      			selection_available = true;
//      		} else {
//      			selection_available = false;
//      		}
//      		
//      		if (!selection_available) {
//      			objRef = document.createElement("INPUT");
//		    	objRef.name = sel3;
//		    	objRef.id = sel3;
//		    	objRef.className = onblur_css;
//    			objRef.setAttribute('onfocus', 'input_on_focus(this, \''+onfocus_css+'\')');
//    			objRef.setAttribute('onblur', 'input_on_blur(this, \''+onblur_css+'\')');
//      		}
//      		
//      		div_objRef.innerHTML = '';
//      		div_objRef.appendChild(objRef);
//      		sel1.disabled = false;
//      	}
//    }
//    
//    xmlhttp.send(null);
//}

// add item to select element the less elegant, but compatible way.
function appendToSelect(select, value, content) {
	var opt = document.createElement('option');
	opt.value = value;
	opt.text = content;
	select.options.add(opt);
}

function clearOptionList(obj) {
	while (obj.options.length > 0) {
		obj.remove(0);
	}
}

function showMainInfo(DisplayMsg) {
	hideMainInfo();
	if (typeof DisplayMsg == 'undefined') {
		return;
	}
	var objInfo = DOMCall('infocaption');
	var scroll = getScroll();
	
	objInfo.innerHTML = '<div class="xmlHttpMainInfo">' + DisplayMsg + '</div>';
	objInfo.style.visibility = 'visible';
	
	objInfo.style.position = "absolute";
	
	objInfo.style.top = 5 + scroll.y;
	
	var msg_left_pos = getWindowWidth() - objInfo.offsetWidth;
	objInfo.style.left = msg_left_pos - 20;
}

function hideMainInfo() {
	var objInfo = DOMCall('infocaption');
	objInfo.style.visibility = 'hidden';
	objInfo.innerHTML = '';
}

function getWindowWidth() {
	var windowWidth = 0;
	if (typeof(window.innerWidth) == 'number') {
		windowWidth = window.innerWidth;
	} else {
		if (document.documentElement && document.documentElement.clientWidth) {
			windowWidth = document.documentElement.clientWidth;
		} else {
			if (document.body && document.body.clientWidth) {
				windowWidth = document.body.clientWidth;
			}
		}
	}
	return windowWidth;
}

// Not longer been call in any script
//function onGameSelection(gameListObj, languages_id) {
//	var server_action = 'get_buyback_server_list';
//	var ref_url = "customer_xmlhttp.php?action="+server_action+"&buyback_cat_id="+gameListObj.value+"&lang="+languages_id;
//   	document.getElementById('wbb_div_msgField1').innerHTML = 'Loading...';
//	
//	var dependantInputs = new Array('wbb_input_game_select', 'wbb_input_server_select');
//	disableElement(dependantInputs, true);
//    var dependantElements = new Array('wbb_tbody_error', 'wbb_tbody_notice', 'wbb_tbody_step4', 'wbb_tbody_step3', 'wbb_tbody_step2');
//    changeElementDisplay(dependantElements, 'hide');
//    
//    var server_selection = DOMCall('wbb_input_server_select');
//	xmlhttp.open("GET", ref_url);
//	xmlhttp.onreadystatechange = function() { 
//      	if (xmlhttp.readyState == 4 && xmlhttp.status == 200) {
//      	    //alert(xmlhttp.responseText);
//      		var server_arr = xmlhttp.responseXML.getElementsByTagName('server');
//      		var selection_value = '';
//      		var selection_content = '';
//      		clearOptionList(server_selection);
//
//            //No servers, etc      		
//            var msg_id = 0;
//			var error_msg_obj = xmlhttp.responseXML.getElementsByTagName('error')[0];
//      		if (typeof (error_msg_obj) != 'undefined' && error_msg_obj != null) {
//      			var msg_id = parseInt(error_msg_obj.firstChild.data);
//      		}
//      	    
//      	    if (msg_id > 0) {
//          	    showError(msg_id);
//      	    } else {      	    
//      		    for (var j=0; j<server_arr.length; j++) {
//          		    selection_value = server_arr[j].getElementsByTagName('id')[0].firstChild.nodeValue;
//          		    selection_content = server_arr[j].getElementsByTagName('name')[0].firstChild.nodeValue;
//                    appendToSelect(server_selection, selection_value, selection_content);
//    	        }
//      		}
//        	disableElement(dependantInputs, false);
//        	document.getElementById('wbb_div_msgField1').innerHTML = '';
//      	}
//    }
//    xmlhttp.send(null);    
//}

// Not longer been call in any script
//function onServerSelection(serverListObj, languages_id) {
//	var server_action = 'get_buyback_product_info';
//	var parent_cat_id = DOMCall('wbb_input_game_select').value;
//	var ref_url = "customer_xmlhttp.php?action="+server_action+"&buyback_cat_id="+serverListObj.value+"&buyback_parent_cat_id="+parent_cat_id+"&lang="+languages_id;
//	document.getElementById('wbb_div_msgField2').innerHTML = 'Loading...';
//	
//	var dependantInputs = new Array('wbb_input_game_select', 'wbb_input_server_select', 'wbb_input_qty', 'wbb_button_step2');
//	disableElement(dependantInputs, true);
//    var dependantElements = new Array('wbb_tbody_error', 'wbb_tbody_notice', 'wbb_tbody_step4', 'wbb_tbody_step3', 'wbb_tbody_step2');
//    changeElementDisplay(dependantElements, 'hide');
//	
//	var errorMsg = '';
//	
//    if (serverListObj.value > 0) {
//    	xmlhttp.open("GET", ref_url);
//    	xmlhttp.onreadystatechange = function() { 
//          	if (xmlhttp.readyState == 4 && xmlhttp.status == 200) {
//          	    //alert(xmlhttp.responseText);
//                var msg_id = 0;
//    			var notice_msg_obj = xmlhttp.responseXML.getElementsByTagName('notice')[0];
//          		if (typeof (notice_msg_obj) != 'undefined' && notice_msg_obj != null) {
//          			var msg_id = parseInt(notice_msg_obj.firstChild.data);
//          		}
//          	    if (msg_id > 0) {
//              	    showNotice(msg_id);            	    
//          	    }
//          	    
//                var msg_id = 0;
//    			var error_msg_obj = xmlhttp.responseXML.getElementsByTagName('error')[0];
//          		if (typeof (error_msg_obj) != 'undefined' && error_msg_obj != null) {
//          			var msg_id = parseInt(error_msg_obj.firstChild.data);
//          		}
//          	    if (msg_id > 0) {
//              	    showError(msg_id);            	    
//                    dependantInputs = new Array('wbb_input_game_select', 'wbb_input_server_select');
//                    disableElement(dependantInputs, false);
//          	    } else {      	    
//                    document.getElementById('wbb_span_uom').innerHTML = xmlhttp.responseXML.getElementsByTagName('uom')[0].firstChild.data;
//                    document.getElementById('wbb_span_max_value').innerHTML = xmlhttp.responseXML.getElementsByTagName('max')[0].firstChild.data;
//                    document.getElementById('wbb_span_product_name').innerHTML = xmlhttp.responseXML.getElementsByTagName('product_name')[0].firstChild.data;
//                    document.getElementById('wbb_span_min_value').innerHTML = xmlhttp.responseXML.getElementsByTagName('min')[0].firstChild.data;
//                    document.getElementById('wbb_hidden_min_value').value = xmlhttp.responseXML.getElementsByTagName('min')[0].firstChild.data;
//                    document.getElementById('wbb_hidden_products_id').value = xmlhttp.responseXML.getElementsByTagName('product_id')[0].firstChild.data;
//                    document.getElementById('wbb_hidden_max_value').value = xmlhttp.responseXML.getElementsByTagName('max')[0].firstChild.data;
//                    document.getElementById('wbb_tbody_step2').className = 'show';
//                    disableElement(dependantInputs, false);           
//          	    }
//                document.getElementById('wbb_div_msgField2').innerHTML = '';
//          	}
//        }
//        xmlhttp.send(null);
//    } else {
//        dependantInputs = new Array('wbb_input_game_select', 'wbb_input_server_select');
//        disableElement(dependantInputs, false);
//        document.getElementById('wbb_div_msgField2').innerHTML = '';
//    }
//}    

// Not longer been call in any script
//function onStep2Submit(languages_id, sym_left, sym_right, dec) {
//    document.getElementById('wbb_div_msgField3').innerHTML = 'Loading...';
//    
//	var dependantInputs = new Array('wbb_input_game_select', 'wbb_input_server_select', 'wbb_input_qty');
//	disableElement(dependantInputs, true);
//    var dependantElements = new Array('wbb_tbody_step4', 'wbb_tbody_step3');
//    changeElementDisplay(dependantElements, 'hide');
//    document.getElementById('wbb_tbody_error').className = 'hide';
//    
//    //validate, else error tbody
//    var buyback_qty = DOMCall('wbb_input_qty').value;
//    var buyback_qty_min = parseInt(DOMCall('wbb_hidden_min_value').value);
//    var buyback_qty_max = parseInt(DOMCall('wbb_hidden_max_value').value);
//    
//    if ( (parseInt(buyback_qty) > 0) && (buyback_qty >= buyback_qty_min) && (buyback_qty <= buyback_qty_max) ) {
//    	var buyback_qty = DOMCall('wbb_input_qty').value;
//        var server_selection = DOMCall('wbb_input_server_select');        	
//        var products_id = DOMCall('wbb_hidden_products_id').value;
//        var server_selected_id = server_selection.selectedIndex
//        var buyback_server = server_selection.options[server_selected_id].text;          
//        var cat_id = server_selection.options[server_selected_id].value;          
//    	
//    	var server_action = 'get_buyback_price';
//    	
//    	var ref_url = "customer_xmlhttp.php?action="+server_action+"&buyback_products_id="+products_id+"&buyback_qty="+buyback_qty+"&lang="+languages_id;
//		
//    	xmlhttp.open("GET", ref_url);
//    	xmlhttp.onreadystatechange = function() { 
//          	if (xmlhttp.readyState == 4 && xmlhttp.status == 200) {
//          	    //alert(xmlhttp.responseText);
//                
//          	    var buyback_price_obj = xmlhttp.responseXML.getElementsByTagName('price')[0];
//	      		if (typeof (buyback_price_obj) != 'undefined' && buyback_price_obj != null) {
//	      			buyback_price = buyback_price_obj.firstChild.data;
//	      		}
//	      		
//        		buyback_price = currency(buyback_price, sym_left, sym_right, dec);
//        		
//                DOMCall('wbb_span_buyback_price').innerHTML = buyback_price;
//                DOMCall('wbb_hidden_unit_price').value = xmlhttp.responseXML.getElementsByTagName('unit_price')[0].firstChild.data;
//                DOMCall('wbb_span_product_descriptor').innerHTML = buyback_qty + ' ' + DOMCall('wbb_span_uom').innerHTML + ' of ' + ' ' + DOMCall('wbb_span_product_name').innerHTML;
//                DOMCall('wbb_span_buyback_server').innerHTML = buyback_server;
//                DOMCall('wbb_input_comment').disabled = false;
//                changeElementDisplay(dependantElements, 'show');
//                disableElement(dependantInputs, false);           
//                document.getElementById('wbb_div_msgField2').innerHTML = '';
//                document.getElementById('wbb_hidden_action').value = 'confirm';
//          	}
//        }
//        xmlhttp.send(null);
//    } else {
//        showError(1);
//    }
//    //ok, undo hides and locks
//	disableElement(dependantInputs, false);        
//    DOMCall('wbb_div_msgField3').innerHTML = '';
//}

function getScroll() {
    if (document.body.scrollTop != undefined) { // IE model
        var ieBox = document.compatMode != "CSS1Compat";
        var cont = ieBox ? document.body : document.documentElement;
        return {x : cont.scrollLeft, y : cont.scrollTop};
    } else {
        return {x : window.pageXOffset, y : window.pageYOffset};
    }
}

function updateExchangeRate(sel, cust_id, user_action) {
	var curr_code = DOMCall(sel);
	
	if (typeof(curr_code) == 'undefined' || curr_code == null) {
		return false;
	}

	if (curr_code.value != '') {
		var display_html = '';
		var ref_url = 'customer_xmlhttp.php?action='+user_action+'&curr_code='+curr_code.value+'&customer_id='+cust_id;

		blockUI_disable();
		jQuery.ajax({
			url: ref_url,
			type: 'GET',
			dataType: 'xml',
			timeout: 10000,
			error: function(){
			jQuery.unblockUI();

			},
			success: function(xml) {
				var error = jQuery(xml).find('error').text();
      			var exchange_rate = jQuery(xml).find('exchange_rate').text();
      			
      			if (error == '') {
      				jQuery('#exchange_rate_div').html(exchange_rate);
	      		} else {
	      			alert('Error! Please Try again.');
	      			location.reload(true);
	      		}
	      		
	      		jQuery.unblockUI();
			}
		});
	} else {
		jQuery('#exchange_rate_div').html('');
	}
}

window.onload = function() {
	initInfoCaptions();
}