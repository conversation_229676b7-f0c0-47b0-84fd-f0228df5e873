/* Use JavaScript to do the onfocus highlighting event */
/* Javascript is refering to http://www.webdeveloper.com/forum/showthread.php?t=171298 */
/* InputOnFocus : Object to activate focus & blur style changes for textfield in browsers */

window.onload = function ()
{
	var objIM = document.getElementById("instantmessage");
	
	if (typeof(objIM) != 'undefined' && objIM != null) {
		objIM.options[0].selected = true;
	}
}


/* add new instant message row */
var hasLoaded = false;
var ROW_BASE = 1;
var im_account_counter = 1;
var tr = document.getElementsByTagName("tr");

function createIM(im_account_limit)
{
	if (im_account_counter <= im_account_limit)
	{
		addNewIMRow();
	}
	else
	{
		alert("You only can create " + im_account_limit + " instant message accounts.");
	}
}

function addNewIMRow()
{
	var tbl = document.getElementById("InstantMessageTBody");
	var objIM = document.getElementById("instantmessage");
	
	var newTR = document.createElement("tr");
	var newTD1 = document.createElement("td");
	var newTD2 = document.createElement("td");
	var newTD3 = document.createElement("td");
	var newTD4 = document.createElement("td");

	if (objIM.options[objIM.selectedIndex].value == '')
	{
		//alert("Please select an Instant Message Type.");
	}
	else 
	{
		newTD1.innerHTML = objIM.options[objIM.selectedIndex].text + ":<input type='hidden' style='width:100px;height:21px;' name='instantmessageaccounttype[]' value='"+ objIM.options[objIM.selectedIndex].value +"'>";
		newTD2.innerHTML = "<input type='text' name='instantmessageaccount[]' style='width:100px;height:21px;' onfocus='this.className=\"focus\"' onblur='this.className=\"\"'>" + " &nbsp;<image src='/images/icon-collapse-small.gif' id='rmInstantMessageAccount' onclick='deleteIMRow(this)'>";
		newTD3.innerHTML = "&nbsp;";
		newTD4.innerHTML = "&nbsp;";
		
		newTD1.className = "ezInputLabel";
		newTD3.className = "ezInputLabel";
				
		if (objIM.options[objIM.selectedIndex].value == '0')
		{
			newTD1.innerHTML = objIM.options[objIM.selectedIndex].text + "&nbsp;IM:<input type='hidden' name='othersinstantmessageaccounttype[]' value='" + objIM.options[objIM.selectedIndex].value + "'>";
			newTD2.innerHTML = "<input type='text' name='othersinstantmessageaccountname[]' style='width:100px;height:21px;' onfocus='this.className=\"focus\"' onblur='this.className=\"\"'>";
			newTD3.innerHTML = "ID:";
			newTD4.innerHTML = "<input type='text' name='othersinstantmessageaccount[]' style='width:100px;height:21px;' onfocus='this.className=\"focus\"' onblur='this.className=\"\"'>" + " &nbsp;<image src='/images/icon-collapse-small.gif' id='rmInstantMessageAccount' onclick='deleteIMRow(this)'>";
		
			newTD1.className = "ezInputLabel";
			newTD3.className = "ezInputLabel";
		}

		newTD1.setAttribute('width', '135');
		newTD2.setAttribute('width', '135');
		newTD3.setAttribute('width', '30');
		newTD4.setAttribute('width', '135');
		
		newTD1.setAttribute('height', '25');
		newTD2.setAttribute('height', '25');
		newTD3.setAttribute('height', '25');
		newTD4.setAttribute('height', '25');
			
		newTR.appendChild(newTD1);
		newTR.appendChild(newTD2);
		newTR.appendChild(newTD3);
		newTR.appendChild(newTD4);
			
		tbl.appendChild(newTR);

		hasLoaded = true;
		im_account_counter = im_account_counter + 1;
	}
}


function deleteIMRow(obj)
{
	var rowArray = new Array(obj.parentNode.parentNode);
	
	if (hasLoaded)
	{
		if (tr.length == 1)
		{
			hasLoaded = false;
		}
		else
		{
			for (var i=0; i < rowArray.length; i++)
			{
				var delIndex = rowArray[i].sectionRowIndex;
				rowArray[i].parentNode.deleteRow(delIndex);
			}
			
			if (im_account_counter > 1)
			{
				im_account_counter = im_account_counter - 1;
			}
		}
	}
}

    
