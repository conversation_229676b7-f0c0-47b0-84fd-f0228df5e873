var comboHeight = (_isChrome ? 100 : 105);

var game_combo = dhtmlXComboFromSelect("cPath", 240);
game_combo.enableFilteringMode(true);
game_combo.enableOptionAutoWidth(false);
game_combo.enableOptionAutoHeight(true, comboHeight);
game_combo.setComboText(COMBO_SELECT_GAME);
game_combo._defaultText = COMBO_SELECT_GAME;

var prd_type_combo = dhtmlXComboFromSelect("product_type", 240);
prd_type_combo.enableFilteringMode(true);
prd_type_combo.enableOptionAutoWidth(true);
prd_type_combo.enableOptionAutoHeight(true, comboHeight);
prd_type_combo._defaultText = COMBO_SELECT_PRODUCT_TYPE;

var prd_combo = dhtmlXComboFromSelect("products_id", 160);
prd_combo.enableFilteringMode(true);
prd_combo.enableOptionAutoWidth(true);
prd_combo.enableOptionAutoHeight(true, comboHeight);

var prd_delivery_mode = dhtmlXComboFromSelect("ecDeliveryMode", 160);
prd_delivery_mode.enableFilteringMode(true);
prd_delivery_mode.enableOptionAutoWidth(true);
prd_delivery_mode.enableOptionAutoHeight(true, comboHeight);
prd_delivery_mode.setComboText(COMBO_SELECT_DELIVERY_MODE);
prd_delivery_mode._defaultText = COMBO_SELECT_DELIVERY_MODE;

var prd_dtu_character_combo;

// WINDOW LOAD
jQuery(document).ready(function() {
    ecActionOnOver();

    jQuery('#ecBox').parents('.mm-item-content lyr145').bind('mousemove', function(e) {
        if (jQuery('#ecBox').parents('.mm-item-content lyr145').css('display') == 'none') {
            jQuery('span[menuid=token-express-checkout]').parent().find('.dhx_combo_list').css({display: 'none'});
        }
    });
});


// RETRIEVE DATA
function ecActionOnOver() {
    jQuery('span[menuid=token-express-checkout]').parents('.mm-item').mouseover(function() {
        if ((jQuery('#ecBoxInitLoading').css('display') == 'block') && (jQuery('#ecInitLoad').val() == '')) {
            jQuery('#ecInitLoad').val('1');

            jQuery.getJSON('express_checkout_xmlhttp.php?action=game_available', function(data) {
                game_combo.clearAll();

                var _str = [];
                jQuery.each(data, function(cnt) {
                    _str.push([data[cnt].cPath, data[cnt].name]);
                });

                game_combo.addOption(_str);
                game_combo.enableOptionAutoWidth(true);
                game_combo.disable(false);

                jQuery('#ecBoxInitLoading').css({display: 'none'});

                jQuery("#ecBoxInfo").css({display: 'block'});
                jQuery("#ecBoxGame").css({display: 'block'});

                ecProdTypeBlock();
                ecProdCmbBlock();
                ecQtyBlock();
            });
        }
    });
}


function exp_prod_type_list() {
    var cPath = jQuery('#cPath').val();
    var cPath_dhtmlx = jQuery('#cPath_dhtmlx').val();

    ecInitState();

    ecBoxProdQtyHide();
    ecBoxModeHide();
    ecBoxDTUInputHide();
    ecBoxPriceOPHide();
    ecBoxBtnHide();
    ecBoxMessageHide();

    ecProdTypeBlock();
    ecProdCmbBlock();
    ecQtyBlock();

    prd_type_combo.disable(true);
    jQuery('#ecGrayBtn').css({display: 'block'});
    jQuery('#ecBoxPriceLoading').css({display: 'block'});

    if (!empty(cPath) && cPath.match(/[0-9_]/) && (cPath_dhtmlx == "false")) {
        prd_type_combo.setComboText('Loading...');

        jQuery('#ecBoxProdType').css({display: 'none'});
        jQuery('#ecBoxBtn').css({display: 'none'});
        jQuery('#ecBoxPriceOPLine').css({display: 'none'});

        jQuery.getJSON('express_checkout_xmlhttp.php?action=product_type&cPath=' + cPath, function(data) {
            prd_type_combo.clearAll();

            var _str = [];
            jQuery.each(data, function(cnt) {
                _str.push([data[cnt].tpl + ':~:' + data[cnt].cPath, data[cnt].name]);
            });

            prd_type_combo.addOption(_str);
            prd_type_combo.enableOptionAutoWidth(true);
            prd_type_combo.disable(false);

            jQuery('#ecBoxProdType').css({display: 'block'});
            jQuery("#ecProdType").unblock();
            jQuery('#ecBoxPriceLoading').css({display: 'none'});
        });

        prd_type_combo.setComboText(COMBO_SELECT_PRODUCT_TYPE);
    }
}


function exp_prod_list() {
    var gameOpt = jQuery('#cPath').val();
    var arr = explode(':~:', jQuery('#product_type').val());
    var tpl = arr[0];
    var cPath = arr[1];
    var cPath_dhtmlx = jQuery('#product_type_dhtmlx').val();
    var combo_text = '';

    jQuery('#products_id').val('')

    ecInitState();

    ecProdCmbBlock();
    ecQtyBlock();
    ecBoxBtnHide();
    ecBoxModeHide();
    ecBoxDTUInputHide();
    ecBoxPriceOPHide();
    ecBoxMessageHide();

    prd_combo.disable(true);

    jQuery('#ecBoxBtn').css({display: 'none'});
    jQuery('#ecBoxPriceOPLine').css({display: 'none'});

    jQuery('#ecGrayBtn').css({display: 'block'});
    jQuery('#ecBoxPriceLoading').css({display: 'block'});

    if (!empty(gameOpt) && gameOpt.match(/[0-9_]/) && !empty(cPath) && (cPath_dhtmlx == 'false')) {
        if (tpl == 0) {
            var combo_text = COMBO_SELECT_SERVERS;
        } else if (tpl == 1) {
            var combo_text = COMBO_SELECT_CATEGORIES;
        } else if (tpl == 2) {
            var combo_text = COMBO_SELECT_PRODUCT;
            jQuery('#ecQty').css({display: 'block'});
        }

        if (tpl == 0 || tpl == 1 || tpl == 2) {
            prd_combo._defaultText = combo_text;
            prd_combo.setComboText('Loading...');
            prd_combo.enableOptionAutoWidth(false); // reset autowidth

            jQuery('#ecBoxProdQty').css({display: 'none'});
            jQuery('#ecProdCmb').css({display: 'none'});
            jQuery('#ecQty').css({display: 'none'});
        }

        if (tpl == 4) {
            ecBoxProdQtyHide();

            jQuery.get('express_checkout_xmlhttp.php?action=fastlink&tpl=' + tpl + '&cPath=' + cPath, function(data) {
                jQuery('#ecBoxPriceLoading').css({display: 'none'});

                var arr = explode(':~:', jQuery('#product_type').val());
                var cur_tpl = arr[0];

                if (cur_tpl == tpl) {
                    var fastlink = data;

                    if (!empty(data)) {
                        jQuery('#ecBoxBtn').css({display: 'block'});

                        jQuery('#ecGrayBtn').css({display: 'none'});
                        jQuery('#ecGreenBtn').css({display: 'block'});

                        jQuery("#ecGreenCheckOut").click(function() {
                            expGATrack();
                            setTimeout(function() {
                                window.location.href = fastlink;
                            }, 500);
                            return false;

                        });
                        jQuery('#ecGreenCheckOut').attr('href', "javascript:void(0)");
                        jQuery('#ecGreenBtnText').html(BUTTON_EXP_CHECKOUT_BUY_NOW);
                    }
                }
            });
        } else {
            jQuery.getJSON('express_checkout_xmlhttp.php?action=products&tpl=' + tpl + '&cPath=' + cPath, function(data) {
                jQuery('#ecBoxPriceLoading').css({display: 'none'});

                var arr = explode(':~:', jQuery('#product_type').val());
                var cur_tpl = arr[0];

                if (cur_tpl == tpl) {
                    jQuery('#ecProdCmb').css({display: 'block'});
                    jQuery('#ecBoxProdQty').css({display: 'block'});

                    if (tpl == 2) {
                        jQuery('#ecQty').css({display: 'block'});
                    }

                    prd_combo.clearAll();

                    var _str = [];
                    jQuery.each(data, function(cnt) {
                        _str.push([data[cnt].id, data[cnt].name]);
                    });

                    prd_combo.addOption(_str);
                    prd_combo.enableOptionAutoWidth(true);
                    prd_combo.disable(false);

                    jQuery('#ecProdCmb').unblock();
                    jQuery('#ecQty').unblock();
                }
            });

            prd_combo.setComboText(combo_text);
        }
    }

}

function exp_prod_delivery_mode() {
    exp_price_op();
    if (jQuery("#ecDeliveryMode").val() == 6) {
        jQuery("#ecBoxDTUInput").show();
    } else {
        jQuery("#ecBoxDTUInput").hide();
    }
}

function exp_prod_status() {
    var gameOpt = jQuery('#cPath').val();
    var arr = explode(':~:', jQuery('#product_type').val());
    var tpl = arr[0];
    var cPath = arr[1];
    var pid = jQuery('#products_id').val();
    var pid_dhtmlx = jQuery('#products_id_dhtmlx').val();

    ecBoxModeHide();
    ecBoxDTUInputHide();
    ecBoxPriceOPHide();
    ecBoxBtnHide();
    ecBoxMessageHide();

    var btn_status = BUTTON_EXP_CHECKOUT_CHECKOUT;

    jQuery('#ecGrayBtnText').html(btn_status);
    jQuery('#ecBoxBtn').css({display: 'none'});

    jQuery('#ecBoxPriceLoading').css({display: 'block'});

    prd_delivery_mode._defaultText = COMBO_SELECT_DELIVERY_MODE;

    jQuery("#ecDeliveryMode").val('');

    if (!empty(gameOpt) && gameOpt.match(/[0-9_]/) && !empty(cPath) && !empty(pid) && is_int(tpl) && (pid_dhtmlx == 'false')) {
        if (tpl == 2) {
            jQuery.getJSON('express_checkout_xmlhttp.php?action=status&tpl=' + tpl + '&cPath=' + cPath + '&pid=' + pid, function(data) {
                jQuery('#ecBoxPriceLoading').css({display: 'none'});

                var delivery_mode = '';
                if (jQuery("#ecDeliveryMode").val().length > 0) {
                    delivery_mode = jQuery("#ecDeliveryMode").val();
                }

                if (delivery_mode != '') {
                    jQuery('#ecBoxBtn').css({display: 'block'});
                    jQuery('#ecGrayBtn').css({display: 'block'});
                    jQuery('#ecBoxPriceOPLine').css({display: 'block'});
                } else {
                    jQuery('#ecBoxPriceOPLine').css({display: 'none'});
                    jQuery('#ecGrayBtn').css({display: 'none'});
                }

                var show = data.show;
                var message = data.pre_order_time;

                if (tpl == 2) {

                    prd_delivery_mode.clearAll();

                    var _str = [];
                    jQuery.each(data.delivery_mode, function(key, val) {
                        _str.push([key, val]);
                    });

                    prd_delivery_mode.addOption(_str);
                    prd_delivery_mode.enableOptionAutoWidth(true);
                    prd_delivery_mode.disable(false);

                    if (data.delivery_mode_html.length > 0 && data.delivery_mode_html != '') {
                        jQuery("#ecBoxDTUInputHtml").html(data.delivery_mode_html);

                        if (jQuery("#ec_dtu_server_id").length > 0) {
                            var prd_dtu_server_combo = dhtmlXComboFromSelect("ec_dtu_server_id", 160);
                            prd_dtu_server_combo.enableFilteringMode(true);
                            prd_dtu_server_combo.enableOptionAutoWidth(true);
                            prd_dtu_server_combo.enableOptionAutoHeight(true, comboHeight);
                        }

                        if (jQuery("#ecCharacterListing").length > 0) {
                            prd_dtu_character_combo = dhtmlXComboFromSelect("ec_dtu_character_list", 160);
                            prd_dtu_character_combo.enableFilteringMode(true);
                            prd_dtu_character_combo.enableOptionAutoWidth(true);
                            prd_dtu_character_combo.enableOptionAutoHeight(true, comboHeight);

                            jQuery("#ecCharacterListing .dhx_combo_input").click(function() {
                                load_ec_character_list(pid)
                            });
                        }
                    }
                    jQuery("#ecBoxMode, #ecDeliveryMode").fadeIn();
                }

                var pre_order = data.is_future_product;

                if ((show == 1) || (pre_order != 1) || delivery_mode == 6) {
                    var btn_status = BUTTON_EXP_CHECKOUT_CHECKOUT;
                    var message = '';
                } else {
                    var btn_status = BUTTON_EXP_CHECKOUT_PRE_ORDER;
                    jQuery('#ecGrayBtnText').html(btn_status);
                }

                document.getElementById('bundle').value = data.bundle;
                jQuery('#ecQty').unblock();

                if (show == 0) {
                    var btn_status = BUTTON_EXP_CHECKOUT_OUT_OF_STOCK;
                    jQuery('#ecGrayBtnText').html(btn_status);
                }

                if (!empty(message) && typeof message != 'undefined') {
                    jQuery('#ecBoxMessenge').css({display: 'block'});
                    jQuery('#ecMessenge').html(TABLE_HEADING_DELIVERY_TIME + ': ' + message);
                }
            });
        } else {
            jQuery.get('express_checkout_xmlhttp.php?action=fastlink&pid=' + pid, function(data) {
                jQuery('#ecBoxPriceLoading').css({display: 'none'});
                var fastlink = data;
                if (!empty(data)) {
                    jQuery('#ecBoxBtn').css({display: 'block'});

                    jQuery('#ecGrayBtn').css({display: 'none'});
                    jQuery('#ecGreenBtn').css({display: 'block'});

                    jQuery("#ecGreenCheckOut").click(function() {
                        expGATrack();
                        setTimeout(function() {
                            window.location.href = fastlink;
                        }, 500);
                        return false;

                    });
                    jQuery('#ecGreenCheckOut').attr('href', "javascript:void(0)");
                    jQuery('#ecGreenBtnText').html(BUTTON_EXP_CHECKOUT_BUY_NOW);
                }
            });
        }
    } else {
        jQuery('#ecBoxPriceLoading').css({display: 'none'});
    }
}


function exp_price_op() {
    var arr = explode(':~:', jQuery('#product_type').val());
    var tpl = arr[0];
    var cPath = arr[1];
    var pid = jQuery('#products_id').val();
    var buyqty = parseInt(jQuery('#buyqty').val());
    var btn_status = jQuery('#ecGrayBtnText').html();
    jQuery('#ecBoxMessage').css({display: 'none'});

    var delivery_mode = '';
    if (jQuery("#ecDeliveryMode").val().length > 0) {
        delivery_mode = jQuery("#ecDeliveryMode").val();
    }

    ecBoxDTUInputHide();

    if (!empty(cPath) && (!empty(pid) && is_int(pid)) && (!empty(buyqty) && is_int(buyqty)) && !(tpl == 2 && delivery_mode == '')) {
        ecBoxPriceOPHide();
        ecBoxBtnHide();

        jQuery('#ecBoxPriceLoading').css({display: 'block'});
        jQuery('#ecGrayBtnText').html(btn_status);
        jQuery('#ecBoxBtn').css({display: 'none'});

        jQuery.getJSON('express_checkout_xmlhttp.php?action=status&tpl=' + tpl + '&cPath=' + cPath + '&pid=' + pid + '&buyqty=' + buyqty, function(data) {
            if ((document.getElementById('ecQty').style.display != 'none') && (parseInt(jQuery('#buyqty').val()) == buyqty)) {
                var show = data.show;
                var message = data.pre_order_time;

                jQuery('#ecBoxBtn').css({display: 'block'});

                if (show == 0 && delivery_mode != 6) {
                    var btn_status = BUTTON_EXP_CHECKOUT_OUT_OF_STOCK;
                    var message = TEXT_EXP_CHECKOUT_BULK_NOT_AVAILABLE;

                    ecBoxPriceOPHide();
                    jQuery('#ecGrayBtnText').html(btn_status);
                } else {
                    var pre_order = data.is_future_product;
                    var price = data.price;
                    var op = data.op;

                    jQuery('#ecBoxPriceOP').css({display: 'block'});

                    if (tpl == 2 && delivery_mode == '') {
                        jQuery('#ecBoxPriceOPLine').css({display: 'none'});
                        jQuery('#ecOP').css({display: 'none'});
                    } else {
                        jQuery('#ecGrayBtn').css({display: 'none'});

                        if ((show == 1) || (pre_order != 1) || delivery_mode == 6) {
                            var btn_status = BUTTON_EXP_CHECKOUT_CHECKOUT;

                            jQuery('#ecRedBtnText').html(btn_status);
                            jQuery('#ecRedBtn').css({display: 'block'});
                        } else {
                            var btn_status = BUTTON_EXP_CHECKOUT_PRE_ORDER;
                            jQuery('#ecYellowBtnText').html(btn_status);
                            jQuery('#ecYellowBtn').css({display: 'block'});
                        }

                        jQuery('#ecPriceTxt').html(price);
                        jQuery('#ecPrice').css({display: 'block'});

                    }

                    if (op == '') {
                        op = '0';
                    }

                    jQuery('#ecOPTxt').html(op);
                    jQuery('#ecOP').css({display: 'block'});

                    var exitmenu = function() {
                        var mlevent = jQuery('body').data('mlevent');
                        if (typeof mlevent !== 'undefined')
                        {
                            jQuery('.ogmMenu li.mm-item:first').bind('mouseleave', mlevent);
                        }
                    }
                    jQuery(".wTip").tipTip({maxWidth: "270px", edgeOffset: 0, defaultPosition: "left", keepAlive: true,
                        enter: function() {
                            var $menu = jQuery('.ogmMenu li.mm-item:first');
                            if (typeof $menu.data('events').mouseout !== 'undefined')
                            {
                                mlevent = $menu.data('events').mouseout[0];
                                jQuery('body').data('mlevent', mlevent);
                                $menu.unbind('mouseleave');
                            }
                        }, exit: exitmenu
                    });
                    jQuery('body').click(function() {
                        jQuery('#tiptip_holder').hide();
                        exitmenu();
                    });
                    document.getElementById('bundle').value = data.bundle;
                }

                if (!empty(message) && typeof message != 'undefined') {
                    jQuery('#ecBoxMessage').css({display: 'block'});
                    jQuery('#ecMessage').html(TABLE_HEADING_DELIVERY_TIME + ': ' + message);
                }
            } else {
                jQuery('#ecPriceOP').css({display: 'none'});
            }

            jQuery('#ecBoxPriceLoading').css({display: 'none'});
        });
    } else {
        ecBoxPriceOPHide();
        ecBoxBtnHide();
        jQuery('#ecOP').css({display: 'none'});
        jQuery('#ecBoxBtn').css({display: 'none'});
    }
}


function ecSelectDeliveryMethod(pass_id) {
    jQuery(".ec_delivery_mode_bg div.ec_delivery_mode_bg_content").hide();
    jQuery("#td_delivery_mode_" + pass_id + " div.ec_delivery_mode_bg_content").show();
    exp_price_op();
}


function expressCheckout() {
    var arr = explode(':~:', jQuery('#product_type').val());
    var tpl = arr[0];
    var cPath = arr[1];
    var pid = jQuery('#products_id').val();
    var buyqty = parseInt(jQuery('#buyqty').val());
    var bundle = jQuery('#bundle').val();

    var delivery_mode = '';
    var submit_flag = true;

    if (jQuery("#ecDeliveryMode").val().length > 0) {
        delivery_mode = jQuery("#ecDeliveryMode").val();
    }

    if (delivery_mode == 6) {
        var submit_data = 'action=direct_topup_insert_cart&delivery_mode=6&pid=' + pid + '&';

        if (delivery_mode == 6) {
            jQuery('#ecBoxMode input, #ecBoxMode select, #ecBoxMode textarea').each(function() {
                if (jQuery(this).attr('name') != 'game_info[server_dhtmlx]' && jQuery(this).attr('name') != 'game_info[character_dhtmlx]') {
                    if (jQuery(this).val() == '') {
                        jQuery(this).focus();
                        submit_flag = false;
                    }

                    if (jQuery(this).attr('name') == 'game_info[character]' && jQuery("#ecCharacterListing").length > 0) {
                        submit_data += jQuery(this).attr('name') + '=' + jQuery(this).val() + '##' + prd_dtu_character_combo.getComboText() + '&';
                    } else {
                        submit_data += jQuery(this).attr('name') + '=' + jQuery(this).val() + '&';
                    }
                }
            });
        }
        submit_data += 'txt_qty=' + buyqty + '&';

        if (submit_flag) {
            jQuery.ajax({
                url: CHECKOUT_XMLHTTP_LINK,
                data: submit_data,
                type: 'POST',
                dataType: 'xml',
                timeout: 60000,
                error: function() {

                },
                success: function(xml) {
                    if (jQuery(xml).find('error_message').length > 0 && jQuery(xml).find('error_message').text() != '') {
                        jQuery('#tr_top_up_error_msg').css({display: 'block'});
                        jQuery('#tr_top_up_error_msg #div_error_msg').html(jQuery(xml).find('error_message').text());
                    } else {
                        document.location.href = CHECKOUT_PAYMENT_LINK;
                    }
                }
            });
        }
    } else {
        if (!empty(cPath) && (!empty(pid) && is_int(pid)) && (!empty(buyqty) && is_int(buyqty))) {
            expGATrack();

            jQuery.ajax({
                type: 'POST',
                url: BUY_NOW_LINK,
                data: {cPath: cPath, 'action': 'buy_now', 'products_id': pid, 'custom_product': tpl, 'buyqty': buyqty, 'products_bundle': bundle, 'delivery_mode': delivery_mode},
                async: false,
                dataType: 'xml',
                complete: function() {
                    document.location.href = CHECKOUT_PAYMENT_LINK;
                }
            });
        }
    }
}



// FIELD BLOCK / UNBLOCK
function ecProdTypeBlock() {
    prd_type_combo.setComboText(COMBO_SELECT_PRODUCT_TYPE);

    var $ecProdType = jQuery('#ecProdType');
    if (!$ecProdType.data('blockUI.isBlocked')) {
        $ecProdType.block({message: null,
            overlayCSS: {
                backgroundColor: '#A3A3A3',
                opacity: '0.6',
                cursor: 'default',
                width: '241px'
            }
        });
    }
}

function ecProdCmbBlock() {
    prd_combo.setComboText(COMBO_SELECT_PRODUCT);

    var $ecProdCmb = jQuery('#ecProdCmb');
    if (!$ecProdCmb.data('blockUI.isBlocked')) {
        $ecProdCmb.block({message: null, overlayCSS: {backgroundColor: '#A3A3A3', opacity: '0.6', cursor: 'default'}});
    }
}

function ecQtyBlock() {
    document.getElementById('buyqty').value = 1;

    if (!jQuery('#ecQty').data('blockUI.isBlocked')) {
        jQuery('#ecQty').block({message: null, overlayCSS: {backgroundColor: '#A3A3A3', opacity: '0.6', cursor: 'default'}});
    }
}



// FIELD HIDE / SHOW
function ecInitState() {
    /* button */
    jQuery('#ecGrayBtnText').html(BUTTON_EXP_CHECKOUT_CHECKOUT);
    jQuery('#ecGreenCheckOut').attr('href', 'javascript: expressCheckout();');
    jQuery('#ecGreenCheckOut').removeAttr('onclick');
}

function ecBoxProdQtyHide() {
    jQuery('#ecBoxProdQty').css({display: 'none'});
    jQuery('#ecProdCmb').css({display: 'none'});
    jQuery('#ecQty').css({display: 'none'});

    document.getElementById('buyqty').value = COMBO_SELECT_QTY;
}

function ecBoxBtnHide() {
    jQuery('#ecYellowBtn').css({display: 'none'});
    jQuery('#ecRedBtn').css({display: 'none'});
    jQuery('#ecGreenBtn').css({display: 'none'});
    jQuery('#ecGrayBtn').css({display: 'block'});
}

function ecBoxModeHide() {
    jQuery('#ecBoxMode').css({display: 'none'});
}

function ecBoxDTUInputHide() {
    jQuery('#tr_top_up_error_msg').css({display: 'none'});
    jQuery('#div_error_msg').html('');
}

function ecBoxPriceOPHide() {
    jQuery('#ecPriceTxt').html('');
    jQuery('#ecOPTxt').html('');
    jQuery('#ecBoxPriceOP').css({display: 'none'});
}

function ecBoxMessageHide() {
    jQuery('#ecMessage').html('');
    jQuery('#ecBoxMessage').css({display: 'none'});
}



// VALIDATION
/* Qty Text onKeyPress action */
function expQtySubmit(e) {
    if (window.event) {
        key = window.event.keyCode; // IE
    } else {
        key = e.which;
    }

    if (key == 13) {
        exp_price_op();
    }
}


/* Google analytics */
function expGATrack() {
    if (typeof pageTracker != 'undefined' && !empty(GA_CUSTOMERS_GROUPS_NAME)) {
        pageTracker._setCustomVar(
                2,
                "Exp_Checkout",
                GA_CUSTOMERS_GROUPS_NAME,
                1
                );
        pageTracker._trackPageview();
    }
}

function load_ec_character_list(pid) {

    prd_dtu_character_combo._defaultText = 'Character';
    prd_dtu_character_combo.clearAll();

    jQuery('#tr_top_up_error_msg').hide();
    jQuery('#tr_top_up_error_msg #div_error_msg').html("");

    var _str = [];

    if (!jQuery("#ecCharacterListing").hasClass("character_list_loaded")) {
        var customer_input_account = "";
        var customer_input_server = "";

        if (jQuery("#ec_dtu_account").length > 0) {
            customer_input_account = jQuery("#ec_dtu_account").val();
        }

        if (jQuery("#ecServerListing").length > 0) {
            jQuery("#ecServerListing input").each(function() {
                if (jQuery(this).attr("name") == "game_info[server]") {
                    customer_input_server = jQuery(this).val();
                }
            });
        }

        jquery_notice_box('Loading...');

        jQuery.ajax({
            url: "checkout_xmlhttp.php?action=load_dtu_character_list&pid=" + pid + "&account=" + customer_input_account + "&server=" + customer_input_server,
            type: "GET",
            dataType: "xml",
            timeout: 60000,
            error: function() {
                jQuery.unblockUI();
                _str.push(['', 'Character']);
                prd_dtu_character_combo.addOption(_str);
            },
            success: function(xml) {
                jQuery.unblockUI();

                if (jQuery(xml).find("error").text() == "1") {
                    jQuery('#tr_top_up_error_msg').css({display: 'block'});
                    jQuery('#tr_top_up_error_msg #div_error_msg').html(jQuery(xml).find("error_message").text());
                } else {
                    _str.push(['', jQuery(xml).attr("default_text")]);

                    if (jQuery(xml).find("character").length > 0) {
                        jQuery(xml).find("character").each(function() {
                            _str.push([jQuery(this).attr("id"), jQuery(this).text()]);
                        });
                        prd_dtu_character_combo.addOption(_str);
                        prd_dtu_character_combo.enableOptionAutoWidth(true);
                        prd_dtu_character_combo.disable(false);

                    } else {
                        jQuery('#tr_top_up_error_msg').css({display: 'block'});
                        jQuery('#tr_top_up_error_msg #div_error_msg').html(jQuery(xml).find("error_message").text());
                    }
                }
            }
        });

        jQuery("#ecCharacterListing").addClass("character_list_loaded");
        jQuery("#ec_dtu_account, #ec_dtu_server_id").change(function() {
            jQuery("#ecCharacterListing").removeClass("character_list_loaded");
            jQuery("#ec_dtu_account, #ec_dtu_server_id").unbind("change");
        });
    }
}