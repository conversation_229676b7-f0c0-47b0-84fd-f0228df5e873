if(!Curse)
{
	var Curse={};
}

Curse.Browser={
	ie:navigator.appName.indexOf("Microsoft")!=-1,
	ie7:this.ie&&navigator.appVersion.indexOf("MSIE 7")!=-1,
	ie6:this.ie&&navigator.appVersion.indexOf("MSIE 6")!=-1,
	opera:!!window.opera,safari:navigator.userAgent.indexOf("Safari")!=-1,
	gecko:navigator.userAgent.indexOf("Gecko")!=-1&&navigator.userAgent.indexOf("KHTML")==-1};
	
	Curse.Client={
		viewportWidth:function(){
			return self.innerWidth||(document.documentElement.clientWidth||document.body.clientWidth);
			},viewportHeight:function(){
				return self.innerHeight||(document.documentElement.clientHeight||document.body.clientHeight);
			},viewportSize:function(){
				return{width:this.viewportWidth(),
					height:this.viewportHeight()
				};
			},
			
			scrollTop:function(){
				if(document.body.scrollTop){
					return document.body.scrollTop;
				}else if(document.documentElement&&!document.documentElement.scrollTop){
					return 0;
				}else if(document.documentElement&&document.documentElement.scrollTop){
					return document.documentElement.scrollTop;
				}else if(document.body&&document.body.scrollTop){
					return document.body.scrollTop;
				}
			}
		};
		function cg_args(a){
			var r=[];
			for(var i=0,len=a.length;i<len;++i){
				r.push(a[i]);
			}
			return r;
		}
				if(!Array.indexOf){
					Array.prototype.indexOf=function(obj){
						for(var i=0;i<this.length;i++){
							if(this[i]==obj){
								return i;
							}
						}
					return-1;
				}
			}
			if(!Curse.Browser.ie){
				HTMLElement.prototype.contains=function(oEl){
					if(oEl==this)return true;
					if(oEl==null)return false;
					try{
						return this.contains(oEl.parentNode)
					}
					catch(err){}
				};
			}
			
			Function.prototype.bindAsEventListener=function(object){
				var __method=this;return function(event){
					return __method.call(object,event||window.event);
				}
			}
			
			Function.prototype.bind=function(){
				var ref=this;var args=cg_args(arguments);
				var object=args.shift();return function(){
					return ref.apply(object,args.concat(cg_args(arguments)));
				};
			};
			
			function cg_iterateArray(arr,func,ud){
				var res;
				for(var i=0,len=arr.length;i<len;++i){
					res=func(arr[i],ud,arr,i);
					if(res!=null){
						arr[i]=res;
					}
				}
			}
			
			function cg_inArray(a,r,f,s){
				if(a==null){
					return-1;
				}
				if(f){
					return cg_inArrayF(a,r,f);
				}
				for(var i=s||0,len=a.length;i<len;++i){
					if(a[i]==r){
						return i;
					}
				}
				return-1;
			}

			function cg_inArrayF(a,b,c,d){
				for(var i=d||0,len=a.length;i<len;++i){
					if(c(a[i])==b){
						return i;
					}
				}
				return-1;
			}
			
			function cg_de(a){
				if(a){
					a.parentNode.removeChild(a);
				}
			}
			
			function cg_strcmp(a,b){
				if(a==b){
					return 0;
				}
				if(a==null){
					return-1;
				}
				if(b==null){
					return 1;
				}
				return a<b?-1:1;
			}
			
			function cg_cOr(a,b){
				for(var p in b){
					if(typeof b[p]=="object"){
						if(!a[p]){
							a[p]={};
						}
						cg_cOr(a[p],b[p]);
					}else{
						a[p]=b[p];
					}
				}
			}

			function cg_ce(a,b){
				var r=document.createElement(a);
				if(b){
					cg_cOr(r,b);
				}
				return r;
			}
			
			function cg_ae(a,b){
				return a.appendChild(b);
			}
			
			function cg_ge(a){
				return document.getElementById(a);
			}
			
			function cg_ia(parent,node,referenceNode){
				parent.insertBefore(node,referenceNode.nextSibling);
			}
			
			function cg_df(){
				this.blur();
			}
			
			function cg_gebt(a,b){
				return a.getElementsByTagName(b);
			}
			
			function cg_ct(a){
				return document.createTextNode(a);
			}
			
			function cg_rf(){
				return false;
			}
			
			function cg_ds(a){
				a.onmousedown=cg_rf;
				a.onselectstart=cg_rf;
				if(Curse.Browser.ie){
					a.onfocus=cg_df;
				}
			}
			
			function cg_cO(a,b){
				for(var p in b){
					a[p]=b[p];
				}
			}

			function cg_getShadowText(text,className){
				var shadowText=cg_ce("span");
				for(var i=-1;i<=1;++i){
					for(var j=-1;j<=1;++j){
						var d=cg_ce("div");
						d.style.position="absolute";
						d.style.whiteSpace="nowrap";
						d.style.left=i+"px";
						d.style.top=j+"px";
						if(i==0&&j==0){
							d.style.zIndex=4;
						}else{
							d.style.color="black";
							d.style.zIndex=2;
						}
						cg_ae(d,cg_ct(text));
						cg_ae(shadowText,d);
					}
				}
				shadowText.style.position="relative";
				shadowText.className="glow"+(className!=null?" "+className:"");
				var s=cg_ce("span");
				s.style.visibility="hidden";
				cg_ae(s,cg_ct(text));
				cg_ae(shadowText,s);
				return shadowText;
			}

			function cg_getLookupsFromCookie(name,delim1,delim2){
				var lookupList=[];
				var cookieList=cg_getCookie("Login."+name);
				cookieList=cg_utf8Decode(cookieList);
				if(!delim1){
					delim1=",";
				}
				if(!delim2){
					delim2="^";
				}
				if(cookieList){
					cookieList=cookieList.split(delim1);
					for(var i=0;i<cookieList.length;i++){
						lookupList.push(cookieList[i].split(delim2));
					}
				}
				return lookupList;
			}

			function cg_getLookupSelectBox(lookupName,selectName,container,hideEmpty,onchange,fromCookie,delim1,delim2,emptyLabel){
				if(fromCookie){
					var lookupList=cg_getLookupsFromCookie(lookupName,delim1,delim2);
				}else{
					var lookupList=Curse.Lookup[lookupName]
				}
				var objSelect=document.createElement("select");
				if(selectName){
					objSelect.name=selectName;objSelect.id="fi_"+selectName;
				}
				if(onchange){
					objSelect.onchange=function(){
						eval(onchange);
					};
				}
				if(!hideEmpty){
					var objOption=document.createElement("option");
					if(!emptyLabel){
						emptyLabel="";
					}
					objOption.text=emptyLabel;
					objOption.value="";
					objSelect.options.add(objOption)
				}
				if(!fromCookie){
					for(var p in lookupList){
						if(typeof p=='string'&&p!="indexOf"){
							var objOption=document.createElement("option");
							objOption.text=lookupList[p].replace("<br>"," - ");
							objOption.value=p;objSelect.options.add(objOption)
						}
					}
				}else{
					for(var i=0;i<lookupList.length;i++){
						var objOption=document.createElement("option");
						objOption.text=lookupList[i][1].replace("<br>"," - ");
						objOption.value=lookupList[i][0];objSelect.options.add(objOption)
					}
				}
				if(container){
					container.appendChild(objSelect);
					return objSelect
				}else{
					return objSelect;
				}
			}

			function cg_scrollTo(element,padding){
				var pos=cg_getPosition(element)
				scrollTo(0,pos.y-padding);
			}
			
			function cg_scrollTop(){
				if(document.body.scrollTop){
					return document.body.scrollTop;
				}else if(document.documentElement&&!document.documentElement.scrollTop){
					return 0;
				}else if(document.documentElement&&document.documentElement.scrollTop){
					return document.documentElement.scrollTop;
				}else if(document.body&&document.body.scrollTop){
					return document.body.scrollTop;
				}
			}
			
			function cg_addEventListener(eventSource,eventName,eventHandler){
				if(eventSource.addEventListener){
					eventSource.addEventListener(eventName,eventHandler,false);
				}else if(eventSource.attachEvent){
					eventName="on"+eventName;
					eventSource.attachEvent(eventName,eventHandler);
				}
			}
			
			function cg_removeEventListener(eventSource,eventName,eventHandler){
				if(eventSource.addEventListener){
					eventSource.removeEventListener(eventName,eventHandler,false);
				}else if(eventSource.detachEvent){
					eventSource.detachEvent("on"+eventName);
				}
			}
			
			function cg_hasClass(pElem,pClassName){
				if(!pElem.className){
					return;
				}
				if(pElem.className==pClassName){
					return true;
				}
				if(pElem.className.split(" ").indexOf(pClassName)>=0){
					return true;
				}
				return false;
			}
			
			function cg_removeClass(pElem,pClassName){
				if(!pElem.className){
					return;
				}
				var classArray=pElem.className.split(" ");
				for(var i=0;i<classArray.length;i++){
					if(classArray[i]==pClassName){
						classArray.splice(i,1);
						break;
					}
				}
				pElem.className=classArray.join(" ");
			}

			function cg_addClass(pElem,pClassName){
				var classArray=pElem.className.split(" ");
				for(var i=0;i<classArray.length;i++){
					if(classArray[i]==pClassName){
						return;
					}
				}
				classArray.push(pClassName);
				pElem.className=classArray.join(" ");
			}

			function cg_isArray(obj){
				if(obj.constructor.toString().toLowerCase().indexOf("function")==-1&&obj.constructor.toString().toLowerCase().indexOf("array")==-1){
					return false;
				}else{
					if(!obj.length){
						return false;
					}
					return true;
				}
			}
			
			function cg_getPosition(pElem){
				var left=0;
				var top=0;
				while(pElem.offsetParent){
					left+=pElem.offsetLeft;
					if(pElem.clientLeft){
						left+=pElem.clientLeft;
					}
					top+=pElem.offsetTop;
					if(pElem.clientTop){
						top+=pElem.clientTop;
					}
					pElem=pElem.offsetParent;
				}
				left+=pElem.offsetLeft;
				top+=pElem.offsetTop;
				return{x:left,y:top};
			}
			
			function cg_getScroll(){
				var x=0,y=0;if(typeof(window.pageYOffset)=="number"){
					x=window.pageXOffset;
					y=window.pageYOffset;
				}else{
					if(document.body&&(document.body.scrollLeft||document.body.scrollTop)){
						x=document.body.scrollLeft;
						y=document.body.scrollTop;
					}else{
						if(document.documentElement&&(document.documentElement.scrollLeft||document.documentElement.scrollTop)){
							x=document.documentElement.scrollLeft;
							y=document.documentElement.scrollTop;
						}
					}
				}
				return{x:x,y:y};
			}
			
			function cg_setTextNodes(n,b){
				if(a.nodeType==3){
					a.nodeValue=b;
				}else{
					for(var i=0;i<a.childNodes.length;++i){
						cg_setTextNodes(a.childNodes[i],b);
					}
				}
			}
			
			function cg_deleteCookie(name,path,domain){
				var curVal=cg_getCookie(name);
				if(curVal){
					document.cookie=name+"="+curVal+";expires=Thu, 01-Jan-1970 00:00:01 GMT;path=/";
				}
			}
			
			function cg_setCookie(name,value,exp_y,exp_m,exp_d,path,domain,secure){
				var cookie_string=name+"="+escape(value);
				var expires=new Date();
				if(exp_y){
					if(exp_m=null){
						exp_m=1;
					}
					if(exp_d=null){
						exp_m=1;
					}
					Expires.setTime(expires.getTime()+(1000*60*60*24*31));
				}else{
					expires.setDate(expires.getDate()+365);
				}
				cookie_string+="; expires="+expires.toGMTString();
				if(path)
					cookie_string+="; path="+escape(path);
				if(domain)
					cookie_string+="; domain="+escape(domain);if(secure)
				cookie_string+="; secure";document.cookie=cookie_string;
			}
			
			function cg_getCookie(cookie_name){
				var results=document.cookie.match(cookie_name+'=(.*?)(;|$)');
				if(results)
					return(unescape(results[1]));
				else
					return null;
			}

			function cg_getElementsByClassName(sClassName,sTag,oContainer,returnFirst){
				var searchObj;
				var results=new Array();
				if(!oContainer){
					oContainer=document;
				}
				if(sTag=="*"||!sTag){
					if(document.all){
						searchObj=oContainer.all;
					}else{
						searchObj=oContainer.getElementsByTagName("*");
					}
				}else{
					searchObj=oContainer.getElementsByTagName(sTag);
				}
				for(var i=0,el;((searchObj.all&&!neo.bw.isIE6up)?el=searchObj(i):el=searchObj.item(i));i++){
					if(el.className==sClassName){
						if(returnFirst){
							return el;
						}
						results.push(el);
					}
				}
				return results;
			}
			
			function cg_getQueryStringParam(param){
				var begin,end;if(self.location.search.length>1){
					begin=self.location.search.indexOf(param)
					if(begin==-1){
						return"";
					}
					begin=begin+param.length+1;end=self.location.search.indexOf("&",begin);
					if(end==(-1))end=self.location.search.length;
					return(self.location.search.substring(begin,end));
				}else if(self.location.hash.length>1){
					begin=self.location.hash.indexOf(param)+param.length+1;
					end=self.location.hash.indexOf("&",begin);
					if(end==(-1))end=self.location.hash.length;
					return(self.location.hash.substring(begin,end));
				}else 
					return("");
			}
			
			function cg_getEvent(e){
				if(!Curse.Browser.ie&&!e){
					return null;
				}
				if(!e){
					e=window.event;if(!e){
						return null;
					}
				}
				e._button=e.which?e.which:e.button;
				e._target=e.target?e.target:e.srcElement;
				e._relatedTarget=e.relatedTarget?e.relatedTarget:e.toElement;
				return e;
			}
			
			function cg_getEventTarget(e){
				e=cg_getEvent(e);
				if(!e){
					return null;
				}
				return e._target;
			}
			
			function cg_formatNumber(num){
				num=""+parseInt(num);
				if(num.length<=3){
					return num;
				}
				return cg_formatNumber(num.substr(0,num.length-3))+","+num.substr(num.length-3);
			}
			
			function cg_commify(nStr){
				nStr+='';
				x=nStr.split('.');
				x1=x[0];
				x2=x.length>1?'.'+x[1]:'';
				var rgx=/(\d+)(\d{3})/;
				while(rgx.test(x1)){
					x1=x1.replace(rgx,'$1'+','+'$2');
				}
				return x1+x2;
			}
			
			function cg_getTextValue(a){
				if(Curse.Browser.ie){
					return a.innerText;
				}else{
					return a.textContent;
				}
			}
			
			function cg_toggleDisplay(a){
				if(a.style.display=="none"){
					a.style.display="";
					return true;
				}else{
					a.style.display="none";
					return false;
				}
			}
			
			function cg_cancelBubbling(e){
				if(Curse.Browser.ie){
					e=window.event;
					e.cancelBubble=true;
				}else{
					e=cg_getEvent(e);
					if(!e){
						return;
					}
					e.stopPropagation();
				}
			}
			
			var cg_localTime;
			function cg_refreshDate(){
				cg_localTime=new Date().getTime();
			}
			
			cg_refreshDate();
			function cg_mod(divisee,base){
				return Math.round(divisee-(Math.floor(divisee/base)*base));
			}
			
			function cg_getLocalDateFromTime(time){
				return time;
			}
			
			function cg_getShortFriendlyTime(epoch,excludeTime){
				localDate=new Date(epoch);
				function getPlural(value,ifSingular,ifPlural){
					if(value==1){
						return ifSingular;
					}
					return ifPlural;
				}
				var shortFriendlyTime;
				var timeDifferenceMinutes=parseInt((cg_localTime-localDate.getTime())/1000/60);
				var timeDifferenceHours=parseInt(timeDifferenceMinutes/60);
				if(timeDifferenceMinutes<=1){
					return Localization.time_last_minute;
				}
				if(timeDifferenceHours<1){
					return Localization.replace("time_minute",timeDifferenceMinutes);
				}
				if(timeDifferenceHours<24){
					var extraMins=cg_mod(timeDifferenceMinutes,60);
					shortFriendlyTime=parseInt(timeDifferenceHours,null)+" hr";if(extraMins>0){
						return Localization.replace("time_hour_minute",timeDifferenceHours,extraMins);
					}else{
						return Localization.replace("time_hour",timeDifferenceHours);
					}
				}
				var timeDifferenceDays=parseInt(timeDifferenceHours/24);
				if(timeDifferenceDays<7){
					var extraHours=cg_mod(timeDifferenceHours,24);
					if(extraHours>0){
						if(timeDifferenceDays>1){
							return Localization.replace("time_days_hour",timeDifferenceDays,extraHours);
						}else{
							return Localization.replace("time_day_hour",timeDifferenceDays,extraHours);
						}
					}else{
						if(timeDifferenceDays>1){
							return Localization.replace("time_days",timeDifferenceDays,extraHours);
						}else{
							return Localization.replace("time_day",timeDifferenceDays,extraHours);
						}
					}
				}
				if(excludeTime){
					return localDate.getMonth()+1+"/"+localDate.getDate()+"/"+localDate.getFullYear();
				}else{
					return Localization.replace("time_full",localDate.getMonth()+1,localDate.getDate(),localDate.getFullYear(),localDate.toLocaleTimeString());
				}
			}
			
			function cg_trim(a){
				return a.replace(/^\s+/,'').replace(/\s+$/,'');
			}
			
			function cg_addOrReplace(a,b,c){
				if(b){
					cg_de(b);
				}
				cg_ae(a,c);
			}
			
			function cg_dbg(text){
				if(!cg_ge("debugPanel")){
					return;
				}
				if(cg_ge("debugPanel").style.display=="none"){
					return;
				}
				cg_ge("debugPanel").appendChild(document.createElement("br"));
				cg_ge("debugPanel").appendChild(document.createTextNode(text));
			}
			
			function cg_getRelativeLocation(ignoreEscape){
				var relativeLocation=self.location.href;
				var arrRelativeLocation=relativeLocation.split("/");
				relativeLocation=arrRelativeLocation[arrRelativeLocation.length-1];
				if(!ignoreEscape){
					relativeLocation=escape(relativeLocation);
				}
				return relativeLocation;
			}
			
			function cg_isDefined(object,variable){
				return(typeof(eval(object)[variable])!="undefined");
			}
			
			function cg_cookiesDisabled(){
				cg_setCookie("_cookieTest",true);
				return cg_getCookie("_cookieTest")==null;
			}
			
			
			
			
			if(typeof deconcept=="undefined"){
				var deconcept=new Object();
			}
			if(typeof deconcept.util=="undefined"){
				deconcept.util=new Object();
			}if(typeof deconcept.SWFObjectUtil=="undefined"){
				deconcept.SWFObjectUtil=new Object();
			}
			
			deconcept.SWFObject=function(_1,id,w,h,_5,c,_7,_8,_9,_a){
				if(!document.getElementById){
					return;
				}
				this.DETECT_KEY=_a?_a:"detectflash";
				this.skipDetect=deconcept.util.getRequestParameter(this.DETECT_KEY);
				this.params=new Object();
				this.variables=new Object();
				this.attributes=new Array();
				if(_1){
					this.setAttribute("swf",_1);
				}
				if(id){
					this.setAttribute("id",id);
				}
				if(w){
					this.setAttribute("width",w);
				}
				if(h){
					this.setAttribute("height",h);
				}
				if(_5){
					this.setAttribute("version",new deconcept.PlayerVersion(_5.toString().split(".")));
				}this.installedVer=deconcept.SWFObjectUtil.getPlayerVersion();
				if(!window.opera&&document.all&&this.installedVer.major>7){
					deconcept.SWFObject.doPrepUnload=true;
				}
				if(c){
					this.addParam("bgcolor",c);
				}
				var q=_7?_7:"high";
				this.addParam("quality",q);
				this.setAttribute("useExpressInstall",false);
				this.setAttribute("doExpressInstall",false);
				var _c=(_8)?_8:window.location;
				this.setAttribute("xiRedirectUrl",_c);
				this.setAttribute("redirectUrl","");
				if(_9){
					this.setAttribute("redirectUrl",_9);
				}
			};
			
			deconcept.SWFObject.prototype={
				useExpressInstall:function(_d){
					this.xiSWFPath=!_d?"expressinstall.swf":_d;
					this.setAttribute("useExpressInstall",true);
				},
				setAttribute:function(_e,_f){
					this.attributes[_e]=_f;
				},
				getAttribute:function(_10){
					return this.attributes[_10];
				},
				addParam:function(_11,_12){
					this.params[_11]=_12;
				},
				getParams:function(){
					return this.params;
				},
				addVariable:function(_13,_14){
					this.variables[_13]=_14;
				},
				getVariable:function(_15){
					return this.variables[_15];
				},
				getVariables:function(){
					return this.variables;
				},
				getVariablePairs:function(){
					var _16=new Array();
					var key;var _18=this.getVariables();
					for(key in _18){
						_16[_16.length]=key+"="+_18[key];
					}return _16;
				},
				getSWFHTML:function(){
					var _19="";
					if(navigator.plugins&&navigator.mimeTypes&&navigator.mimeTypes.length){
						if(this.getAttribute("doExpressInstall")){
							this.addVariable("MMplayerType","PlugIn");
							this.setAttribute("swf",this.xiSWFPath);
						}
						_19="<embed type=\"application/x-shockwave-flash\" src=\""+this.getAttribute("swf")+"\" width=\""+this.getAttribute("width")+"\" height=\""+this.getAttribute("height")+"\" style=\""+this.getAttribute("style")+"\"";
						_19+=" id=\""+this.getAttribute("id")+"\" name=\""+this.getAttribute("id")+"\" ";
						var _1a=this.getParams();
						for(var key in _1a){
							_19+=[key]+"=\""+_1a[key]+"\" ";
						}
						var _1c=this.getVariablePairs().join("&");
						if(_1c.length>0){
							_19+="flashvars=\""+_1c+"\"";
						}
						_19+="/>";
					}else{
						if(this.getAttribute("doExpressInstall")){
							this.addVariable("MMplayerType","ActiveX");
							this.setAttribute("swf",this.xiSWFPath);
						}
						_19="<object id=\""+this.getAttribute("id")+"\" classid=\"clsid:D27CDB6E-AE6D-11cf-96B8-444553540000\" width=\""+this.getAttribute("width")+"\" height=\""+this.getAttribute("height")+"\" style=\""+this.getAttribute("style")+"\">";
						_19+="<param name=\"movie\" value=\""+this.getAttribute("swf")+"\" />";
						var _1d=this.getParams();
						for(var key in _1d){
							_19+="<param name=\""+key+"\" value=\""+_1d[key]+"\" />";
						}
						var _1f=this.getVariablePairs().join("&");
						if(_1f.length>0){
							_19+="<param name=\"flashvars\" value=\""+_1f+"\" />";
						}
						_19+="</object>";
					}
					return _19;
				},
				write:function(_20){
					if(this.getAttribute("useExpressInstall")){
						var _21=new deconcept.PlayerVersion([6,0,65]);
						if(this.installedVer.versionIsValid(_21)&&!this.installedVer.versionIsValid(this.getAttribute("version"))){
							this.setAttribute("doExpressInstall",true);
							this.addVariable("MMredirectURL",escape(this.getAttribute("xiRedirectUrl")));
							document.title=document.title.slice(0,47)+" - Flash Player Installation";
							this.addVariable("MMdoctitle",document.title);
						}
					}
					if(this.skipDetect||this.getAttribute("doExpressInstall")||this.installedVer.versionIsValid(this.getAttribute("version"))){
						var n=(typeof _20=="string")?document.getElementById(_20):_20;n.innerHTML=this.getSWFHTML();
						return true;
					}else{
						if(this.getAttribute("redirectUrl")!=""){
							document.location.replace(this.getAttribute("redirectUrl"));
						}
					}return false;
				}
			};
			deconcept.SWFObjectUtil.getPlayerVersion=function(){
				var _23=new deconcept.PlayerVersion([0,0,0]);
				if(navigator.plugins&&navigator.mimeTypes.length){
					var x=navigator.plugins["Shockwave Flash"];
					if(x&&x.description){
						_23=new deconcept.PlayerVersion(x.description.replace(/([a-zA-Z]|\s)+/,"").replace(/(\s+r|\s+b[0-9]+)/,".").split("."));
					}
				}else{
					if(navigator.userAgent&&navigator.userAgent.indexOf("Windows CE")>=0){
						var axo=1;
						var _26=3;
						while(axo){
							try{
								_26++;
								axo=new ActiveXObject("ShockwaveFlash.ShockwaveFlash."+_26);
								_23=new deconcept.PlayerVersion([_26,0,0]);
							}catch(e){
								axo=null;
							}
						}
					}else{
						try{
							var axo=new ActiveXObject("ShockwaveFlash.ShockwaveFlash.7");
						}
						catch(e){
							try{
								var axo=new ActiveXObject("ShockwaveFlash.ShockwaveFlash.6");
								_23=new deconcept.PlayerVersion([6,0,21]);
								axo.AllowScriptAccess="always";
							}
							catch(e){
								if(_23.major==6){
									return _23;
								}
							}
							try{
								axo=new ActiveXObject("ShockwaveFlash.ShockwaveFlash");
							}
							catch(e){}
						}if(axo!=null){
							_23=new deconcept.PlayerVersion(axo.GetVariable("$version").split(" ")[1].split(","));
						}
					}
				}
				return _23;
			};
			
			deconcept.PlayerVersion=function(_29){
				this.major=_29[0]!=null?parseInt(_29[0]):0;
				this.minor=_29[1]!=null?parseInt(_29[1]):0;
				this.rev=_29[2]!=null?parseInt(_29[2]):0;
			};
			
			deconcept.PlayerVersion.prototype.versionIsValid=function(fv){
				if(this.major<fv.major){
					return false;
				}
				if(this.major>fv.major){
					return true;
				}
				if(this.minor<fv.minor){
					return false;
				}
				if(this.minor>fv.minor){
					return true;
				}
				if(this.rev<fv.rev){
					return false;
				}
				return true;
			};
			
			deconcept.util={
				getRequestParameter:function(_2b){
					var q=document.location.search||document.location.hash;
					if(_2b==null){
						return q;
					}
					if(q){
						var _2d=q.substring(1).split("&");
						for(var i=0;i<_2d.length;i++){
							if(_2d[i].substring(0,_2d[i].indexOf("="))==_2b){
								return _2d[i].substring((_2d[i].indexOf("=")+1));
							}
						}
					}
					return"";
				}
			};
			
			deconcept.SWFObjectUtil.cleanupSWFs=function(){
				var _2f=document.getElementsByTagName("OBJECT");
				for(var i=_2f.length-1;i>=0;i--){
					_2f[i].style.display="none";
					for(var x in _2f[i]){
						if(typeof _2f[i][x]=="function"){
							_2f[i][x]=function(){};
						}
					}
				}
			};
			
			if(deconcept.SWFObject.doPrepUnload){
				if(!deconcept.unloadSet){
					
					deconcept.SWFObjectUtil.prepUnload=function(){
						__flash_unloadHandler=function(){};
						__flash_savedUnloadHandler=function(){};
						window.attachEvent("onunload",deconcept.SWFObjectUtil.cleanupSWFs);
					};
					window.attachEvent("onbeforeunload",deconcept.SWFObjectUtil.prepUnload);
					deconcept.unloadSet=true;
				}
			}
			if(!document.getElementById&&document.all){
				document.getElementById=function(id){
					return document.all[id];
				};
			}
			
			var getQueryParamValue=deconcept.util.getRequestParameter;
			var FlashObject=deconcept.SWFObject;
			var SWFObject=deconcept.SWFObject;
			var cg_reverseAlphaArray=["z","y","x","w","v","u","t","s","r"];
			
			function cg_navToLogin(){
				var relativeLocation=cg_getRelativeLocation();
				if(relativeLocation.toLowerCase().indexOf("login.aspx")>=0){
					self.location="login.aspx";
				}else{
					self.location="login.aspx?referrer="+relativeLocation;
				}
			}
			
			function cg_centerElement(oElement){
				var viewportHeight=Curse.Client.viewportHeight();
				var viewportWidth=Curse.Client.viewportWidth();
				var newTop=(viewportHeight/2)-(oElement.offsetHeight/2);
				var newLeft=(viewportWidth/2)-(oElement.offsetWidth/2);
				oElement.style.top=(newTop+cg_scrollTop())+"px";
				oElement.style.left=newLeft+"px";
			}
			

			function cg_getFormAsString(formObject){
				returnString=formObject.action;
				formElements=formObject.elements;
				for(var i=formElements.length-1;i>=0;--i){
					if(i==formElements.length-1){
						returnString=returnString+"?";
					}else{
						returnString=returnString+"&";
					}
					returnString=returnString+encodeURI(formElements[i].name)+"="+encodeURIComponent(formElements[i].value.replace(/</g,"&lt;").replace(/>/g,"&gt;"));
				}
				return returnString;
			}

			function cg_getViewState(index){
				var currentHashArray=self.location.hash.substring(1).split(":");
				if(currentHashArray.length<index+1){
					return null;
				}
				return currentHashArray[index];
			}
			
			function cg_replace(){
				var args=cg_replace.arguments;
				var str=args[0];
				for(i=1;i<args.length;i++){
					str=str.replace(eval("/\\%"+i+"/g"),args[i]);
				}
				return str;
			}
			
			function cg_getStyle(elem,cssRule){
				var value="";
				if(document.defaultView&&document.defaultView.getComputedStyle){
					value=document.defaultView.getComputedStyle(elem,"").getPropertyValue(cssRule);
				}else if(elem.currentStyle){
					cssRule=cssRule.replace(/\-(\w)/g,function(match,p1){
						return p1.toUpperCase();
					});
					value=elem.currentStyle[cssRule];
				}
				return value;
			}
			
			function cg_endsWith(str,s){
				var reg=new RegExp(s+"$");
				return reg.test(str);
			}
			
			function cg_utf8Decode(utftext){
				if(utftext==null){
					return"";
				}
				var string="";
				var i=0;
				var c=c1=c2=0;
				while(i<utftext.length){
					c=utftext.charCodeAt(i);
					if(c<128){
						string+=String.fromCharCode(c);
						i++;
					}else if((c>191)&&(c<224)){
						c2=utftext.charCodeAt(i+1);
						string+=String.fromCharCode(((c&31)<<6)|(c2&63));
						i+=2;
					}else{
						c2=utftext.charCodeAt(i+1);
						c3=utftext.charCodeAt(i+2);
						string+=String.fromCharCode(((c&15)<<12)|((c2&63)<<6)|(c3&63));
						i+=3;
					}
				}
				return String;
			}
			
			function cg_getString(val){
				if(val==null){
					return"";
				}
				return val;
			}
			
			function cg_getBits(val){
				var bits=[];
				if(!val||val==0){
					return bits;
				}
				var dblFeatureCode=parseInt(val);
				var decFeatureCode=parseFloat(0.0);
				var bytFeatureId=1;
				while(dblFeatureCode>0){
					dblFeatureCode=dblFeatureCode/2;
					decFeatureCode=parseInt(dblFeatureCode);
					if(dblFeatureCode>decFeatureCode){
						bits.push(bytFeatureId);
					}
					dblFeatureCode=parseFloat(decFeatureCode);
					bytFeatureId=bytFeatureId+1
				}
				return bits;
			}
			if(!Curse){
				Curse={};
			}
			Curse.Ajax={
				http:false,format:'text',callback:function(data){},
				handler:false,error:false,opt:new Object(),
				getHTTPObject:function(){
					var http=false;
					if(typeof ActiveXObject!='undefined'){
						try{
							http=new ActiveXObject("Msxml2.XMLHTTP");
						}
						catch(e){
							try{
								http=new ActiveXObject("Microsoft.XMLHTTP");
							}
							catch(E){
								http=false;
							}
						}
					}else if(XMLHttpRequest){
						try{
							http=new XMLHttpRequest();
						}
						catch(e){
							http=false;
						}
					}
					return http;
				},
				
				load:function(url,callback,format,method,allowCache,queueFunction,queueFunctionArgs,callbackHost){
					this.init();
					if(!this.http||!URL)
						return;
					if(this.http.overrideMimeType)this.http.overrideMimeType('text/xml');
					this.callback=callback;
					this.callbackHost=callbackHost;
					this.queueFunction=queueFunction;
					this.queueFunctionArgs=queueFunctionArgs;
					if(!method)var method="GET";
					if(!format)var format="text";
					this.format=format.toLowerCase();
					method=method.toUpperCase();
					var ths=this;if(!allowCache){
						var now="uid="+new Date().getTime();
						url+=(url.indexOf("?")+1)?"&":"?";
						url+=now;
					}
					var parameters=null;
					if(method=="POST"){
						var parts=url.split("\?");
						url=parts[0];
						parameters=parts[1];
					}
					this.http.open(method,url,true);
					if(method=="POST"){
						this.http.setRequestHeader("Content-type","application/x-www-form-urlencoded");
						this.http.setRequestHeader("Content-length",parameters.length);
						if(!Curse.Browser.ie){
							this.http.setRequestHeader("Connection","close");
						}
					}
					if(this.handler){
						this.http.onreadystatechange=this.handler;
					}else{
						this.http.onreadystatechange=function(){
							if(!ths)
								return;
								var http=ths.http;
								if(http.readyState==4){
									if(http.status==200){
										var result="";
										if(http.responseText)
											result=http.responseText;
										if(ths.format.charAt(0)=="j"){
											result=result.replace(/[\n\r]/g,"");
											result=eval('('+result+')');
										}else if(ths.format.charAt(0)=="x"){
											result=http.responseXML;
										}
										if(ths.queueFunction){
											ths.queueFunction(result,ths.callback,ths.queueFunctionArgs);
										}else if(ths.callback){
											if(ths.callbackHost){
												ths.callback.bind(ths.callbackHost,result)();
											}else{
												ths.callback(result);
											}
										}
									}else{
										if(ths.opt.loadingIndicator)
											document.getElementsByTagName("body")[0].removeChild(ths.opt.loadingIndicator);
										if(ths.opt.loading)
											document.getElementById(ths.opt.loading).style.display="none";
											if(ths.error)
												ths.error(http.status);
									}
								}
							}
						}
						this.http.send(parameters);
					},
					
					bind:function(user_options){
						var opt={'url':'','onSuccess':false,'onError':false,'format':"text",'method':"GET",'update':"",'loading':"",'loadingIndicator':""}
						for(var key in opt){
							if(user_options[key]){
								opt[key]=user_options[key];
							}
						}
					this.opt=opt;
					if(!opt.URL)
						return;
					if(opt.onerror)
						this.error=opt.onerror;
					var div=false;
					if(opt.loadingIndicator){
						div=document.createElement("div");
						div.setAttribute("style","position:absolute;top:0px;left:0px;");
						div.setAttribute("class","loading-indicator");
						div.innerHTML=opt.loadingIndicator;
						document.getElementsByTagName("body")[0].appendChild(div);
						this.opt.loadingIndicator=div;
					}
					if(opt.loading)
						document.getElementById(opt.loading).style.display="block";
					this.load(opt.url,function(data){
						if(opt.onSuccess)opt.onSuccess(data);
						if(opt.update)document.getElementById(opt.update).innerHTML=data;
						if(div)document.getElementsByTagName("body")[0].removeChild(div);
						if(opt.loading)document.getElementById(opt.loading).style.display="none";
					},opt.format,opt.method);
				},
				
				init:function(){
					this.http=this.getHTTPObject();
				}
			};
			
			var cg_ajaxQueue=[];
			var cg_ajaxProcessing=false;
			function cg_queueAjaxRequest(url,callbackFunction,format,method,callbackArgs,allowCache){
				if(!isNewRequest()){
					return;
				}
				var ajaxRequest={};
				ajaxRequest.url=URL;
				ajaxRequest.callback=callbackFunction;
				ajaxRequest.callbackArgs=callbackArgs;
				ajaxRequest.format=format;
				ajaxRequest.method=method;
				ajaxRequest.allowCache=allowCache;
				cg_ajaxQueue.push(ajaxRequest);
				cg_processAjaxQueue();
				
				function isNewRequest(){
					if(cg_ajaxQueue.length==0){
						return true;
					}
					for(var i=0,len=cg_ajaxQueue.length;i<len;i++){
						if(cg_ajaxQueue[i].url==url){
							return false;
						}
					}
					return true;
				}
			}
			
			function cg_completeAjaxQueue(data,callback,callbackArgs){
				cg_ajaxProcessing=false;callback(data,callbackArgs);
				cg_processAjaxQueue();
			}
			
			function cg_processAjaxQueue(){
				if(cg_ajaxProcessing||cg_ajaxQueue.length==0){
					return;
				}
				cg_ajaxProcessing=true;
				var ajaxRequest=cg_ajaxQueue[0];
				cg_ajaxQueue.splice(0,1);
				Curse.Ajax.load(ajaxRequest.url,ajaxRequest.callback,ajaxRequest.format,ajaxRequest.method,ajaxRequest.allowCache,cg_completeAjaxQueue,ajaxRequest.callbackArgs);
			}
			
			if(!Curse){
				var Curse={};
			}
			
			Curse.Mouse={
				x:0,y:0,
				initialize:function(){
					cg_addEventListener(document,"mousemove",this.update);
					cg_addEventListener(document,"mouseout",this.update);
				},
				
				update:function(e){
					if(window.event){
						Curse.Mouse.x=window.event.clientX;
						Curse.Mouse.y=window.event.clientY;
						Curse.Mouse.y+=cg_scrollTop();
					}else {
						Curse.Mouse.x=e.pageX;
						Curse.Mouse.y=e.pageY;
					}
			}
		};
		
		Curse.Mouse.initialize();
		
		if(!Curse){
			var Curse={};
		}
		
		Curse.Icon=function(size,id,link,relatedID,elementID,overFn,overArgs,outFn,outArgs,num,quantity,showNum,spritePath,spritePosition,baseUrl,FileFormat){
			return this.initialize(size,id,link,relatedID,elementID,overFn,overArgs,outFn,outArgs,num,quantity,showNum,spritePath,spritePosition,baseUrl,FileFormat);
		};
		
		Curse.Icon.prototype={
			sizes:["small","medium","large"],
			paths:["s","m","l"],
			initialize:function(size,id,link,relatedID,elementID,overFn,overArgs,outFn,outArgs,num,quantity,showNum,spritePath,spritePosition,baseUrl,FileFormat){
				if(!FileFormat){
					FileFormat=Curse.Icon.fileFormat;
				}
				var icon=cg_ce("div");
				icon.className="icon"+this.sizes[size];
				var tile=cg_ce("div");
				tile.className="tile";
				if(!baseUrl){
					baseUrl="";
				}
				if(elementID){
					icon.id=elementID;
				}
				if(spritePath!=null){
					var sprite=cg_ce("div");
					sprite.className="sprite";
					sprite.style.backgroundImage="url("+spritePath+")";
					if(spritePosition){
						sprite.style.backgroundPosition=spritePosition;
					}
					cg_ae(icon,sprite);
					var border=cg_ce("var");
					cg_ae(tile,border);
					icon.divSprite=sprite;
					icon.varBorder=border;
				}else{
					icon.style.backgroundImage="url("+baseUrl+"icons/"+this.paths[size]+"/"+id+"."+Curse.Icon.fileFormat+")";
				}
				if(link||overFn){
					var a=cg_ce("a");
					if(relatedID){
						a._relatedID=relatedID;
						icon.aLink=a;
					}
					if(overFn){
						a.onmouseover=overFn.bind(this,a,overArgs);
					}
					if(outFn){
						a.onmouseout=outFn.bind(this,a,outArgs);
					}
					if(link){
						a.href=link;
					}else{
						a.href="javascript:;";
						cg_ds(a);
					}
					cg_ae(tile,a);
				}
				if(showNum||(num!=null&&(num>1||num.length))){
					var shadowText=cg_getShadowText(num,"r1");
					shadowText.style.right="0";
					shadowText.style.bottom="0";
					shadowText.style.position="absolute";
					cg_ae(tile,shadowText);
				}
				if(quantity!=null&&quantity>0){
					var shadowText=cg_getShadowText("("+quantity+")","r");
					shadowText.style.left="0";
					shadowText.style.top="0";
					shadowText.style.position="absolute";
					cg_ae(tile,shadowText);
				}
				cg_ae(icon,tile);
				return icon;
			},
			
			over:function(){
				if(this.relatedTooltip!=null){
					Tooltip.show(this,this.relatedTooltip,0,0);
				}
			},
			
			out:function(){
				Tooltip.hide();
			}
		};
		
		Curse.Icon.fileFormat="gif";
		if(!Curse){
			var Curse={};
		}
		
		Curse.Lookup={lookup:function(keyName){
			if(Curse.Lookup[keyName]!=null){
				return(Curse.Lookup[keyName]);
			}else{
				return"";
			}
		},
		
		add:function(){
			var args=Curse.Lookup.add.arguments;
			for(c=0;c<args.length;c+=3){
				this[args[c]+"."+args[c+1]]=args[c+2];
				if(!Curse.Lookup["LookupList_"+args[c]]){
					Curse.Lookup["LookupList_"+args[c]]=[];
				}
				Curse.Lookup["LookupList_"+args[c]].push([args[c+1],args[c+2]])
			}
		},
		
		lookupList:function(){
			var args=Curse.Lookup.lookupList.arguments;
			return Curse.Lookup[args[0]];
		},
		
		Localization:{
			replace:function(){
				var args=Curse.Lookup.Localization.replace.arguments;
				var str=Curse.Lookup.Localization[args[0]];
				if(!str){
					alert("Missing: "+args[0]);
				}
				for(i=1;i<args.length;i++){
					str=str.replace(eval("/\\%"+i+"/"),args[i]);
				}
				return str;
			}
		}
	}

Localization=Curse.Lookup.Localization;Localization._delete="Delete";
Localization._in="in";
Localization.a_spell="a Spell";
Localization.account_settings="Account Options";
Localization.achievement_points="Points";
Localization.achievements="Achievements";
Localization.activation_code_help="If you have not received a code within 24-hours, please check your spam folder.";
Localization.add="Add";
Localization.add_article="Add a Wiki Article";
Localization.add_comment="Add Comment";
Localization.add_item_tooltip_icon="Add Item Tooltip (with Icon)";
Localization.add_item_tooltip_no_icon="Add Item Tooltip (without Icon)";
Localization.add_map_location="Add Map Location";
Localization.add_spell_tooltip="Add Spell Tooltip (with Icon)";
Localization.add_spell_tooltip_no_icon="Add Spell Tooltip (without Icon)";
Localization.add_to_wishlist="Add to Wish List";
Localization.added="Added";Localization.agility="Agility";
Localization.also_get="Also get";
Localization.amulet="Amulet";
Localization.amulets="Amulets";
Localization.an_item="an Item";
Localization.any="Any";
Localization.approve="Approve";
Localization.approve_map_locations="Approve Map Locations";
Localization.approve_talent_builds="Approve Talent Builds";
Localization.arcane="Arcane";
Localization.arcane_damage="Arcane Damage";
Localization.arcane_resist="Arcane Resist";
Localization.armor="Armor";
Localization.armor_penetration="Armor Penetration";
Localization.article_created="This article was gathered by %1 %2 from %3 | Read the %4Full Article%5";
Localization.article_link="Article Link";
Localization.article_source="Article Source";
Localization.base_stats="Base Stats";
Localization.block="Block";
Localization.block_rating="Block Rating";
Localization.blocked_cookies="Your browser appears to be blocking cookies. You will not be able to login to WOWDB";
Localization.blood="Blood";
Localization.bonus_damage="Bonus Damage";
Localization.bonus_healing="Bonus Healing";
Localization.bonus_spell_damage="Bonus Spell Damage";
Localization.bookmark_search="Bookmark this Search";
Localization.bookmarks="Bookmarks";
Localization.bookmarks_appear_here="Your %1Bookmarks%2 will appear here";
Localization.browser_plugins="Browser Plugins";
Localization.buffs="Buffs";
Localization.bug_reporting_and_feedback="Bug Reporting & Feedback";
Localization.build_approve_error="Error Approving Build";
Localization.build_approved="Build Approved";
Localization.build_denied="Build Denied";
Localization.by="By";
Localization.by_filtering="By filtering your results, you can find exactly what you are searching for, and see up to 500 matches.";
Localization.cancel="Cancel";
Localization.category="Category";
Localization.char_enter_note="Note: You may enter up to %1 characters.";
Localization.character="Character";
Localization.characters="Characters";
Localization.check_spelling="Make sure all words are spelled correctly.";
Localization.choose="Choose";
Localization.choose_wishlist="Please choose the wish list you would like to add this to, or enter the name of a new one.";
Localization.class_colon="Class: %1";
Localization.class_name="Class";
Localization.classes="Classes";
Localization.classification="Classification";
Localization.click="Click";
Localization.click_bookmark="Click here to bookmark this search for later retrieval.";
Localization.click_here_add_wishlist="Click here to add %1 to your wish list.";
Localization.click_item_links="Click here for links to this item.";
Localization.click_map_toggle="Click the map to toggle zoom";
Localization.click_see_side_by_side="Click here to use this item for side-by-side tooltip comparisons in the %1 slot.";
Localization.click_spell_links="Click here for links to this spell.";
Localization.click_submit_build="Click here to submit this build to WOWDB";
Localization.click_to_learn="Click to learn";
Localization.click_to_tour="Click %1here%2 for an overview of our unique features, or click Close and continue browsing.";
Localization.click_to_untrain="Right or Ctrl-Click to Untrain";
Localization.click_view2="Click here to view this NPC.";
Localization.click_view4="Click here to view this quest.";
Localization.click_view5="Click here to view this object.";
Localization.cloak="Cloak";Localization.cloaks="Cloaks";
Localization.colon=":";
Localization.confirm_delete="Are you sure you want to delete this comment?";
Localization.contains="Contains";
Localization.contract_article_contents="Contract Article Contents";
Localization.contract_by_default="Contract By Default";
Localization.cookies_blocked="Cookies Blocked";
Localization.copy_and_paste_command="Copy and paste this command to an in-game chat window.";
Localization.copy_paste_to_comment="Copy and paste this to a WOWDB comment box.";
Localization.copy_url_link_talent="Copy this URL to link directly to this talent build.";
Localization.count="Count";
Localization.create_bookmarks="Create Bookmarks";
Localization.create_filter="Create a Filter";
Localization.create_hyperlink="Create a Hyperlink";
Localization.create_tooltip="Create %1 tooltip";
Localization.crit="Crit";
Localization.crit_chance="Crit Chance";
Localization.crit_rating="Crit Rating";
Localization.criteria="Criteria";
Localization.criteria_of="Criteria Of";
Localization.daily="Daily";
Localization.dam_per_sec="damage per second";
Localization.damage="Damage";
Localization.damage_colon="Damage:";
Localization.day="day";
Localization.days="days";
Localization.decreases_enemy_hit="Descreases chance of enemy scoring a critical hit on you by";
Localization.decreases_rep_with="Decreases Reputation With";
Localization.defense="Defense";
Localization.defense_rating="Defense Rating";Localization.defense_skill_rating="Defense Rating:";
Localization.defenses="Defenses";
Localization.delete_article="Delete Article";
Localization.delete_article_confirm="Are you sure you want to delete this article?";
Localization.delete_wishlist="Are you sure you want to delete this wish list?";
Localization.deny="Deny";
Localization.description_colon="Description: %1";
Localization.disable_tips="Disable Tips";
Localization.disenchant_level="Disenchant Level";
Localization.dodge="Dodge";
Localization.dodge_rating="Dodge Rating";
Localization.download_curse_client="Download the Curse Client";
Localization.dps="DPS";
Localization.dps_colon="Damage per Second:";
Localization.drop_rate="Drop Rate";
Localization.drops_by_class="Drops By Class";
Localization.drops_by_rarity="Drops by Quality";
Localization.drops_by_slot="Drops by Slot";
Localization.durability="Durability";
Localization.edit="Edit";
Localization.edit_article="Edit Article";
Localization.edit_map="Edit Map";
Localization.empty_wishlist="Your wish list is currently empty. To add something to it, simply browse or search the site, and click the &quot;Add to Wishlist&quot; button.";
Localization.ends_with_item="Ends At Item";
Localization.ends_with_npc="Ends At NPC";
Localization.ends_with_object="Ends At Object";
Localization.enter_at_least_x_chars="You must enter at least %1 characters.";
Localization.enter_bookmark_name="Please enter a name for this bookmark below.";
Localization.enter_hyperlink="Please enter the hyperlink details below.";
Localization.enter_reg_code_header="Enter the registration code";
Localization.enter_the_code="Enter the registration code";
Localization.enter_to_x_chars="Enter up to %1 characters";
Localization.enter_tooltip_id="Please enter the ID of the tooltip you wish to embed below.";
Localization.enter_url="Enter a URL, starting with %1|Label";
Localization.existing="Existing";
Localization.expand_article_contents="Expand Article Contents";
Localization.expand_by_default="Expand By Default";Localization.expertise="Expertise";
Localization.expertise_rating="Expertise Rating";
Localization.faction="Faction";
Localization.feral_power="Feral Attack Power";
Localization.filtering="filtering";
Localization.fire="Fire";
Localization.fire_damage="Fire Damage";
Localization.fire_resist="Fire Resist";
Localization.first="First";
Localization.first_to_submit="Be the first to %1submit one%2!";
Localization.first_visit="This appears to be your first visit to WOWDB!";
Localization.for_verification="For verification, please enter the text you see in the box:";
Localization.frost="Frost";
Localization.frost_damage="Frost Damage";
Localization.frost_resist="Frost Resist";
Localization.gender="Gender";
Localization.get_fewer="You can get fewer by";
Localization.glyph_calc="Glyph Calculator";
Localization.glyph_clickadd="Click to Add";
Localization.glyph_clickchange="Click to Change";
Localization.glyph_clickremove="Control-Click to Remove";
Localization.glyph_empty="Empty Glyph";
Localization.glyph_header="%1 Glyph";
Localization.glyph_link="Link to this build";
Localization.glyph_linktip="Copy this URL to link directly to this glyph build.";
Localization.glyph_major="Major Glyph";
Localization.glyph_majors="Major Glyphs";
Localization.glyph_minor="Minor Glyph";
Localization.glyph_minors="Minor Glyphs";
Localization.glyph_type="Glyph Type";
Localization.group="Group";
Localization.guild="Guild";
Localization.has_comment="Has Comment";
Localization.has_loot="Has Loot";
Localization.has_pickpocket_loot="Has Pickpocket Loot";
Localization.has_reward="Has Reward";
Localization.has_screenshot="Has Screenshot";
Localization.haste="Haste";
Localization.haste_rating="Haste Rating";
Localization.header="Header";
Localization.header_contribute="Post Comments and Screenshots";
Localization.health="Health";
Localization.here_are_suggestions="Here are some suggestions for finding what you are looking for";
Localization.hide_comment="Hide Comment";
Localization.hide_filter_form="Hide Filter Form";
Localization.hit_rating="Hit Rating";
Localization.holy_damage="Holy Damage";
Localization.holy_resist="Holy Resist";
Localization.home="Home";
Localization.hp="HP";
Localization.hr="hr";
Localization.if_equip="If you equip";
Localization.in_game_link="In-Game Link";
Localization.in_your_wishlist="In Your Wishlist";
Localization.in_zone="In Zone";
Localization.increases_rep_with="Increases Reputation With";
Localization.instance_type="Instance Type";
Localization.instructions_commenting="When posting a comment, please be sure that it is helpful and does not violate the terms of service. Comments in violation of the terms of service will be deleted. Users who post abusive comments will be permanently banned.";
Localization.instructions_screenshots="When posting a screenshot, please be sure that it features the focus of the current page and does not violate the terms of service. Screenshots in violation of the terms of service will be deleted. Please refer to the %1screenshot guide%2 to learn how to capture and submit screenshots.";
Localization.intellect="Intellect";
Localization.item_id="Item ID";
Localization.item_link="Item Link";
Localization.item_no_preview="This item does not have a 3D preview.";
Localization.items_appear="Items appear after posting";
Localization.label_edit_ok="Label Edit Successful";
Localization.last="Last";
Localization.last_edited_by="Last edited by ";
Localization.latest_additions="Latest Additions";
Localization.latest_comments="Latest Comments";
Localization.latest_rss_desc="The latest World of Warcraft Items, Quests and NPCs. Visit www.wowdb.com for a complete list!";
Localization.level="Level";
Localization.level_colon="Level: %1";
Localization.location="Location";
Localization.location_unknown="The location of %1 is not known.";
Localization.locations_you_add="Locations you add will be submitted for moderation. Once approved, they will be visible to all WOWDB users.";
Localization.login="Login";
Localization.login_register_to_comment="You are not logged in. Please %1Login%2 or %3Register%4 to post a comment";
Localization.login_register_to_screenshot="You are not logged in. Please %1Login%2 or %3Register%4 to post a screenshot.";
Localization.logout="Logout";
Localization.loot="Loot";
Localization.lootable="Lootable";
Localization.mana="Mana";
Localization.mana_per_5_sec="mana regenerated every 5 seconds while not casting";
Localization.mana_regen="Mana Regen";
Localization.melee="Melee";
Localization.melee_crit="Melee Crit Rating";Localization.melee_damage="Melee Damage";
Localization.melee_hit="Melee Hit Rating";
Localization.melee_power="Melee Attack Power";
Localization.melee_speed="Melee Speed";
Localization.meta="Meta";
Localization.min="min";
Localization.mode="Mode";
Localization.more_article="There\'s more to this article:";
Localization.mount="Mount";
Localization.mounts="Mounts";
Localization.my_account="My Account";
Localization.n_a="n/a";
Localization.name="Name";
Localization.nature="Nature";
Localization.nature_damage="Nature Damage";
Localization.nature_resist="Nature Resist";
Localization.new_list="New List";
Localization.next="Next";
Localization.next_rank="Next rank:";
Localization.next_screenshot="Next Screenshot";
Localization.no="No";Localization.no_comments="No comments have been submitted for %1.";
Localization.no_preview="This item does not have a 3D preview.";
Localization.no_results_matching="No results matching ";
Localization.no_screenshots="No screenshots have been submitted for %1.";
Localization.no_search_matches="No search results were found that match %1";
Localization.no_search_results="No search results were found";
Localization.npc="NPC";Localization.npc_family="Family";
Localization.npcs="NPCs";Localization.object="Object";
Localization.of="Of";
Localization.off_hand_frill="Off-hand Frill";
Localization.off_hand_frills="Off-hand Frills";
Localization.offhand_damage="Offhand Damage";
Localization.offhand_speed="Offhand Speed";
Localization.ok="Okay";Localization.on="on";
Localization.opt_rewards="Rewards";
Localization.or="or";
Localization.other="Other";
Localization.parry="Parry";
Localization.parry_rating="Parry Rating";
Localization.paste_the_code="Paste the code:";
Localization.patch="Patch";
Localization.patch_2_4_2="2.4.2";
Localization.penetration="Penetration";
Localization.penetration_spell="Spell Penetration:";
Localization.per_page="Per page:";
Localization.per_sec_no_cast="Per Second while not casting";
Localization.per_sec_no_combat="Per Second while not in combat";
Localization.pet_family="Pet Family";
Localization.pet_skills="Pet Skills";
Localization.petcalc_addbonus="Click to add 4 bonus points from the \'Beast Mastery\' talent";
Localization.petcalc_bonuspoints="Bonus Points:";
Localization.petcalc_changepet="Change Pet";
Localization.petcalc_rembonus="Click to remove the bonus points from the \'Beast Mastery\' talent";
Localization.petcalc_reset="Reset";
Localization.petcalc_title="Pet Talents";
Localization.physical_damage_resist="Physical Damage Resist";
Localization.pickpocketable="Pick Pocketable";
Localization.pieces="Pieces";
Localization.pin_item="Pin Item";
Localization.point_of_interest="Point of Interest";
Localization.points="Points";
Localization.post_a_comment="Post a Comment";
Localization.post_screenshot_note="Note: Only JPEG and PNG image formats are accepted. Your screenshot will need to be approved before appearing on the site.";
Localization.posted="Posted";
Localization.posted_by="Posted by";
Localization.potential_article="Potential WoWiki Article";
Localization.power="Power";Localization.preview="Preview";
Localization.preview_on_character="Preview on Character";
Localization.previous="Previous";
Localization.previous_screen="Previous Screenshot";
Localization.price="Price";
Localization.provide_search_criteria="Click here to provide additional criteria for your search.";
Localization.pve="PvE";
Localization.quest_link="Quest Link";
Localization.quested_in_zone="Quested In Zone";
Localization.question="???";
Localization.quests="Quests";
Localization.race="Race";
Localization.ranged="Ranged";
Localization.ranged_crit="Ranged Crit Rating";
Localization.ranged_damage="Ranged Damage";
Localization.ranged_hit="Ranged Hit Rating";
Localization.ranged_power="Ranged Attack Power";
Localization.ranged_skill="Ranged Weapon Skill";
Localization.rank="Rank";
Localization.rated_by_users="(by %1 users)";Localization.rating="Rating";
Localization.react="React";
Localization.reagent="Reagents";
Localization.reagents="Reagents";
Localization.realm="Realm Name";
Localization.recipes="Recipes";
Localization.reduces_crit_dam="Reduces damage taken from critical strikes by";
Localization.reduces_def_chance="Reduces chance to be dodged or parried by";
Localization.reduces_period_dam="Reduces damage taken from periodic damage by";
Localization.reduces_res="Reduces the target\'s resistance to your spells";
Localization.refine_by_name="Refine by name";
Localization.regen_mana="Mana per 5 sec:";
Localization.register="Register";
Localization.register_to_bookmark="%1Register%2 to %3Create Bookmarks%4";
Localization.remove="Remove";
Localization.remove_from_wishlist="Are you sure you want to remove this from your wish list?";
Localization.rep="Rep";
Localization.replies="Replies";
Localization.reply="Reply";
Localization.reputation="Reputation";
Localization.req="Req.";
Localization.req_personal_team_rating="Required personal and team arena rating";
Localization.reqd="Reqd.";
Localization.required_skill_level="Required Skill Level";
Localization.requires_account="This feature requires a WOWDB account. Click here to login or register.";
Localization.requires_faction_level="Requires Faction Level";
Localization.requires_level="Requires Level %1";
Localization.requires_object="Requires Object";
Localization.requires_points_in_talents="Requires %1 points in %2 Talents";
Localization.requires_reagent="Requires Reagent";
Localization.requires_tool="Requires Tool";
Localization.resilience="Resilience";
Localization.resilience_rating="Resilience Rating";
Localization.results_summary="%1 %2 were found.";
Localization.results_summary_filtering="You can get fewer by %1filtering%2 your results.";
Localization.reward="Reward";
Localization.rewards="Rewards";
Localization.rewards_by_class="Rewards By Class";
Localization.rewards_by_rarity="Rewards By Rarity";
Localization.rewards_by_slot="Rewards By Slot";
Localization.rewards_item="Rewards Item";
Localization.rewards_spell="Rewards Spell";
Localization.rewards_title="Rewards Title";
Localization.ring="Ring";
Localization.rings="Rings";
Localization.runic_power="Runic Power";
Localization.save="Save";
Localization.save_as_changes="";
Localization.save_changes="Save Changes";
Localization.school="School";
Localization.search="Search";
Localization.search_db="Search the database";
Localization.select_all="Select All";
Localization.select_location_type="Select the type of location, and enter a name for it using the form below.";
Localization.select_valid_screenshot="You must select a valid screenshot file.";Localization.shad_dam_increases="Your shadow damage increases your pet\'s Attack Power by";
Localization.shadow="Shadow";
Localization.shadow_damage="Shadow Damage";
Localization.shadow_resist="Shadow Resist";
Localization.share_this_quest="Use the following links to share this quest";
Localization.shield_block="Shield Block:";
Localization.shift_click_to_window="Shift click this link to put it into the default chat window";
Localization.shot_awaiting_moderation="This screenshot is awaiting moderation";
Localization.show_comment="Show Comment";
Localization.show_quest_givers_for="Show quest givers for: ";
Localization.side="Side";
Localization.site_url="http://www.wowdb.com/";
Localization.skill="Skill";
Localization.skills="Skills";
Localization.skinnable="Skinnable";
Localization.slot="Slot";
Localization.slots="Slots";
Localization.small="Small";
Localization.source="Source";
Localization.speed="Speed";
Localization.spell="Spell";
Localization.spell_cost="Mana/Energy/Rage cost";
Localization.spell_cost_percent="% of Base Mana";
Localization.spell_cost_rune_blood="Blood Runes";
Localization.spell_cost_rune_frost="Frost Runes";
Localization.spell_cost_rune_unholy="Unholy Runes";
Localization.spell_cost_runic_power="Runic Power";
Localization.spell_crit_rating="Spell Crit Rating";
Localization.spell_hit_rating="Spell Hit Rating";
Localization.spell_id="Spell ID";
Localization.spell_link="Spell Link";
Localization.spell_penetration="Spell Penetration";
Localization.spell_power="Spell Power";
Localization.spell_requires_spec="Requires a profession specialization";
Localization.spells_appear="Spells appear after posting";
Localization.spirit="Spirit";
Localization.stamina="Stamina";
Localization.standing="Standing";
Localization.start_rotation="Start Rotation";
Localization.starts_quest="Starts a Quest";
Localization.starts_with="Starts With";
Localization.starts_with_item="Starts From Item";
Localization.starts_with_npc="Starts From NPC";
Localization.starts_with_object="Starts From Object";
Localization.stats="Stats";
Localization.stop_rotation="Stop Rotation";
Localization.strength="Strength";
Localization.strike="Strike";
Localization.subject="Subject";
Localization.submit="Submit";
Localization.submit_a_screenshot="Submit a Screenshot";
Localization.submit_talent_build="Submit Talent Build";
Localization.submitted_by="Submitted by";
Localization.submitting="Submitting";
Localization.syndication="Syndication";
Localization.system_message="System Message";
Localization.take_the_tour="Take the Tour";
Localization.talent="Talents";
Localization.talent_calc="Talent Calculator";
Localization.talent_header="%1 Talents";
Localization.talent_link_build="Link to this build";
Localization.talent_lower_cap="Lower Cap to 70";
Localization.talent_raise_cap="Raise Cap to 80";
Localization.talent_rating="Rating:";
Localization.talent_reset_tree="Reset Tree";
Localization.talent_spec="Talent Spec";
Localization.talents="Talents";
Localization.territory="Territory";
Localization.text_entered_no_match="The text you entered does not match the text in the image.";
Localization.this_can_also_be_found=" %1 can also be found in: %2";
Localization.this_can_be_found_in=" %1 can be found in ";
Localization.this_entity1="This item";
Localization.this_entity2="This npc";
Localization.this_entity3="This zone";
Localization.this_entity4="This quest";
Localization.this_entity5="This object";
Localization.this_entity6="This spell";
Localization.this_entity7="This item set";
Localization.this_entity8="This faction";
Localization.time_day="%1 day ago";
Localization.time_day_hour="%1 day %2 hr ago";
Localization.time_days="%1 days ago";
Localization.time_days_hour="%1 days %2 hr ago";
Localization.time_full="%1/%2/%3 at %4";
Localization.time_hour="%1 hr ago";
Localization.time_hour_minute="%1 hr %2 min ago";
Localization.time_last_minute="in the last minute";
Localization.time_minute="%1 min ago";
Localization.to_edit_this_map="To %1 edit this map%2, click the Edit Map button, then click the desired point on the map.";
Localization.to_perform_fulltext="To perform a full text search, you must enter at least 3 letters.";
Localization.to_retreive_search="To easily retrieve a search you frequently perform, simply click the Bookmark this %1Search%2 button after filtering results.";
Localization.to_share_spell="Use the following links to share this spell";
Localization.today="Today";
Localization.today_at="Today at";
Localization.tools="Tools";
Localization.tooltip_attack_speed="Attack Speed (seconds):";
Localization.tooltip_block="Block Rating of %1 adds %2% Block<br>Your block stops %3 damage.";
Localization.tooltip_bonus_healing="Increases your healing by up to %1";
Localization.tooltip_dodge="Dodge Rating of %1 adds %2% Dodge<br>|cff888888(Before diminishing returns)|r";
Localization.tooltip_haste_rating="Haste rating %1 (%2% haste)";
Localization.tooltip_meleehit="Increases your melee chance to hit a target of level %1 by %2%<br><br>Armor penetration rating %3 (Enemy armor reduced by up to %4%).";
Localization.tooltip_meleepower="Increases damage with melee weapons by %1 damage per second.";
Localization.tooltip_parry="Parry Rating of %1 adds %2% Parry<br>|cff888888(Before diminishing returns)|r";
Localization.tooltip_pet_armor="Increases your pet\'s Armor by %1";
Localization.tooltip_pet_int="Increases your pet\'s Intellect by %1";
Localization.tooltip_pet_ranged_power="Increases your pet\'s Attack Power by %1";
Localization.tooltip_pet_spell_dmg="Increases your pet\'s Spell Damage by %1";
Localization.tooltip_pet_stamina="Increases your pet\'s Stamina by %1";
Localization.tooltip_ranged_attack_power="Increases damage with ranged weapons by %1 damage per second.";
Localization.tooltip_ranged_dmg="Increases damage with ranged weapons by %1 damage per second.";
Localization.tooltip_ranged_hit="Increases your ranged chance to hit a target of level %1 by %2%<br><br>Armor penetration rating %3 (Enemy Armor Reduced by up to %4%).";
Localization.tooltip_resilience="Reduces periodic damage and chance to be critically hit by %.";
Localization.tooltip_spell_haste="Increases the speed that your spells cast by %1%.";
Localization.tooltip_spell_hit="Increases your spell chance to hit a target of level  (Reduces enemy resistances by %4)";
Localization.tooltip_statagility="Increases Critical Hit chance by %1%<br>Increases Armor by %2";
Localization.tooltip_statarmor="Reduces Physical Damage taken by %1%";
Localization.tooltip_statdefense="Defense Rating %1 (+%2 Defense)<br>Increases chance to Dodge, Block and Parry by %3%<br>Decreases chance to be hit and critically hit by %4%<br>|cff888888(Before diminishing returns)|r";
Localization.tooltip_statint="Increases Mana by %1<br>Increases Spell Critical Hit by %2%";
Localization.tooltip_statresilience="Reduces periodic damage and chance to be critically hit by %.";
Localization.tooltip_statspellhit="Increases your spell chance to hit a target of level  (Reduces enemy resistances by %4)";
Localization.tooltip_statspirit="Increases Health Regeneration by %1 Per Second while not in combat";
Localization.tooltip_statspirit_mana="Increases Mana Regeneration by %1 Per 5 Seconds while not casting";
Localization.tooltip_statstamina="Increases Health by %1";
Localization.tooltip_statstrength="Increases Attack Power by %1";
Localization.trinket="Trinket";
Localization.trinkets="Trinkets";
Localization.try_different_keywords="Try different keywords.";
Localization.try_fewer_keywords="Try fewer keywords.";
Localization.try_general_keywords="Try more general keywords.";
Localization.type="Type";
Localization.type_to_update="Start typing the name of what you\'re searching for, and the results will update instantly.";
Localization.unholy="Unholy";
Localization.unpin_item="Unpin Item";
Localization.unrated="Unrated";
Localization.unsupported_screenshot="The file you selected is not a supported type.";
Localization.url="URL";
Localization.use_following_links="Use the following links to share this item";
Localization.view_item="View Item";
Localization.view_original_image="View Original Image";
Localization.view_set="View Set";
Localization.weapon_damage="Weapon Damage";
Localization.weapon_skill="Weapon Skill";
Localization.welcome="Welcome";
Localization.welcome_message="Welcome to WOWDB!";
Localization.were_found=" were found.";
Localization.wishlists="Wish Lists";
Localization.with_icon="with icon";
Localization.with_wowdb_account="With a WOWDB account you can save searches as Bookmarks for later retrieval.";
Localization.without_icon="without icon";
Localization.wowdb_link="WOWDB Link";
Localization.wowdb_tooltip="WOWDB Tooltip";
Localization.xp="XP";
Localization.xp_rewarded="XP Rewarded";
Localization.yes="Yes";
Localization.you_will_gain="You will gain: ";
Localization.you_will_lose="You will lose: ";
Localization.your_comment_posted="Your comment has been posted.";
Localization.your_results=" your results.";
Localization.your_shot_awaiting="Note: Your screenshot is awaiting moderation.";
Localization.zone_neutral_count="This zone has %1 neutral quest NPCs / objects.";
Localization.zone_npc_count="This zone has %1 Horde-friendly quest NPCs / objects.";

var cg_currentLanguage='en';
Curse.Lookup.initialize=function(){
	var _=Curse.Lookup;_.achievement_category={};
	_.achievement_category["14801"]="Alterac Valley";
	_.achievement_category["14802"]="Arathi Basin";
	_.achievement_category["165"]="Arena";
	_.achievement_category["123"]="Arenas";
	_.achievement_category["143"]="Attributes";
	_.achievement_category["153"]="Battlegrounds";
	_.achievement_category["124"]="Battlegrounds";
	_.achievement_category["162"]="Brewfest";
	_.achievement_category["130"]="Character";
	_.achievement_category["163"]="Children's Week";
	_.achievement_category["14808"]="Classic";
	_.achievement_category["14821"]="Classic";
	_.achievement_category["14861"]="Classic";
	_.achievement_category["14864"]="Classic";
	_.achievement_category["141"]="Combat";
	_.achievement_category["145"]="Consumables";
	_.achievement_category["170"]="Cooking";
	_.achievement_category["135"]="Creatures";
	_.achievement_category["122"]="Deaths";
	_.achievement_category["125"]="Dungeons";
	_.achievement_category["168"]="Dungeons & Raids";
	_.achievement_category["14807"]="Dungeons & Raids";
	_.achievement_category["14777"]="Eastern Kingdoms";
	_.achievement_category["97"]="Exploration";
	_.achievement_category["14803"]="Eye of the Storm";
	_.achievement_category["81"]="Feats of Strength";
	_.achievement_category["172"]="First Aid";
	_.achievement_category["171"]="Fishing";
	_.achievement_category["191"]="Gear";
	_.achievement_category["92"]="General";
	_.achievement_category["158"]="Hallow's End";
	_.achievement_category["136"]="Honorable Kills";
	_.achievement_category["14778"]="Kalimdor";
	_.achievement_category["137"]="Killing Blows";
	_.achievement_category["128"]="Kills";
	_.achievement_category["14806"]="Lich King Dungeon";
	_.achievement_category["14921"]="Lich King Heroic";
	_.achievement_category["14923"]="Lich King Heroic Raid";
	_.achievement_category["14922"]="Lich King Raid";
	_.achievement_category["187"]="Love is in the Air";
	_.achievement_category["160"]="Lunar Festival";
	_.achievement_category["161"]="Midsummer";
	_.achievement_category["159"]="Noble Garden";
	_.achievement_category["14780"]="Northrend";
	_.achievement_category["14779"]="Outland";
	_.achievement_category["95"]="Player vs. Player";
	_.achievement_category["21"]="Player vs. Player";
	_.achievement_category["173"]="Professions";
	_.achievement_category["169"]="Professions";
	_.achievement_category["133"]="Quests";
	_.achievement_category["96"]="Quests";
	_.achievement_category["152"]="Rated Arenas";
	_.achievement_category["147"]="Reputation";
	_.achievement_category["201"]="Reputation";
	_.achievement_category["127"]="Resurrection";
	_.achievement_category["178"]="Secondary Skills";
	_.achievement_category["132"]="Skills";
	_.achievement_category["131"]="Social";
	_.achievement_category["1"]="Statistics";
	_.achievement_category["14881"]="Strand of the Ancients";
	_.achievement_category["14805"]="The Burning Crusade";
	_.achievement_category["14865"]="The Burning Crusade";
	_.achievement_category["14862"]="The Burning Crusade";
	_.achievement_category["14822"]="The Burning Crusade";
	_.achievement_category["134"]="Travel";
	_.achievement_category["14804"]="Warsong Gulch";
	_.achievement_category["140"]="Wealth";
	_.achievement_category["156"]="Winter Veil";
	_.achievement_category["14901"]="Wintergrasp";
	_.achievement_category["154"]="World";
	_.achievement_category["126"]="World";
	_.achievement_category["155"]="World Events";
	_.achievement_category["14823"]="Wrath of the Lich King";
	_.achievement_category["14863"]="Wrath of the Lich King";
	_.achievement_category["14866"]="Wrath of the Lich King";
	_.achievement_side_id={};
	_.achievement_side_id["-1"]="Both";
	_.achievement_side_id["1"]="Alliance";
	_.achievement_side_id["0"]="Horde";
	_.binding_type={};
	_.binding_type["2"]="Binds when equipped";
	_.binding_type["1"]="Binds when picked up";
	_.binding_type["3"]="Binds when used";
	_.binding_type["4"]="Quest Item";
	_.binding_type["6"]="Binds to account";
	_.category_quests_misc={};
	_.category_quests_misc["372"]="Death Knight";
	_.category_quests_misc["371"]="Inscription";
	_.class_id={};_.class_id["6"]="Death Knight";
	_.class_id["11"]="Druid";
	_.class_id["3"]="Hunter";
	_.class_id["8"]="Mage";
	_.class_id["2"]="Paladin";
	_.class_id["5"]="Priest";
	_.class_id["4"]="Rogue";
	_.class_id["7"]="Shaman";
	_.class_id["9"]="Warlock";
	_.class_id["1"]="Warrior";
	_.class_spec_1={};
	_.class_spec_1["3"]="Arms PvE";
	_.class_spec_1["4"]="Arms PvP";
	_.class_spec_1["2"]="Fury PvE";
	_.class_spec_1["1"]="Protection PvE";
	_.class_spec_11={};
	_.class_spec_11["4"]="Balance PvP";
	_.class_spec_11["3"]="Feral Combat PvP";
	_.class_spec_11["5"]="Feral DPS PvE";
	_.class_spec_11["6"]="Feral tank PvE";
	_.class_spec_11["7"]="Moonkin PvE";
	_.class_spec_11["1"]="Restoration PvE";
	_.class_spec_11["2"]="Restoration PvP";
	_.class_spec_2={};
	_.class_spec_2["3"]="Heal PvE";
	_.class_spec_2["6"]="Heal PvP";
	_.class_spec_2["5"]="Holy DPS PvP";
	_.class_spec_2["2"]="Protection PvE";
	_.class_spec_2["1"]="Retribution PvE";
	_.class_spec_2["4"]="Retribution PvP";
	_.class_spec_3={};
	_.class_spec_3["4"]="Beast master";
	_.class_spec_3["2"]="Beast master PvE";
	_.class_spec_3["3"]="Marksman PvP";
	_.class_spec_3["1"]="Survival PvE";
	_.class_spec_4={};
	_.class_spec_4["4"]="Assassination Subtlety PvP";
	_.class_spec_4["1"]="Combat PvE";
	_.class_spec_4["3"]="Combat PvP";
	_.class_spec_4["2"]="Mutilate PvE";
	_.class_spec_4["5"]="Mutilate PvP";
	_.class_spec_5={};
	_.class_spec_5["2"]="Heal PvE";
	_.class_spec_5["4"]="Heal PvP";
	_.class_spec_5["1"]="Shadow PvE";
	_.class_spec_5["3"]="Shadow PvP";
	_.class_spec_6={};
	_.class_spec_6["3"]="Blood DPS PvE";
	_.class_spec_6["6"]="Blood PvP";
	_.class_spec_6["4"]="Frost DPS PvE";
	_.class_spec_6["7"]="Frost PvP";
	_.class_spec_6["1"]="Frost Tank PvE";
	_.class_spec_6["5"]="Unholy DPS PvE";
	_.class_spec_6["8"]="Unholy PvP";
	_.class_spec_6["2"]="Unholy Tank PvE";
	_.class_spec_7={};
	_.class_spec_7["3"]="Elemental PvE";
	_.class_spec_7["6"]="Elemental PvP";
	_.class_spec_7["2"]="Enhancement PvE";
	_.class_spec_7["5"]="Enhancement PvP";
	_.class_spec_7["1"]="Heal PvE";
	_.class_spec_7["4"]="Heal PvP";
	_.class_spec_8={};
	_.class_spec_8["4"]="Arcane Fire PvE";
	_.class_spec_8["7"]="Arcane Fire PvP";
	_.class_spec_8["3"]="Arcane Frost PvE";
	_.class_spec_8["6"]="Arcane Frost PvP";
	_.class_spec_8["2"]="Fire PvE";_.class_spec_8["1"]="Frost PvE";_.class_spec_8["5"]="Frost PvP";_.class_spec_9={};_.class_spec_9["2"]="Affliction Demonology PvE";_.class_spec_9["5"]="Affliction PvP";_.class_spec_9["4"]="Demonology (Solo) PvP";_.class_spec_9["1"]="Destruction Demonology PvE";_.class_spec_9["3"]="Destruction PvP";_.client_version={};_.client_version["8209,8268,8278"]="2.4.2 (PTR)";_.client_version["8478,8536,8600,8606"]="2.4.3";_.client_version["8634,9155,9183"]="3.0 (WotLK)";_.client_version["8089,8125"]="2.4";_.client_version["9464"]="3.0.8";_.client_version["7799"]="2.3.3";_.client_version["7741"]="2.3.2";_.client_version["7561,7741,7799"]="2.3";_.client_version["7359"]="2.2.3";_.client_version["7318"]="2.2.2";_.client_version["7272,7318,7359"]="2.2";_.client_version["6692"]="2.1";_.client_version["6546"]="2.0";_.disenchant_chance={};_.disenchant_chance["1"]="Extremely Low (1% - 2%)";_.disenchant_chance["6"]="Guaranteed (100%)";_.disenchant_chance["5"]="High (51% - 99%)";_.disenchant_chance["3"]="Low (15% - 24%)";_.disenchant_chance["4"]="Moderate (25 - 50%)";_.disenchant_chance["2"]="Very Low (3% - 14%)";_.drop_mode={};_.drop_mode["2"]="Drops in a Heroic Instance";_.drop_mode["1"]="Drops in a Normal Instance";_.entity_label={};_.entity_label["23"]="Achievement";_.entity_label["8"]="Faction";_.entity_label["10"]="Guild";_.entity_label["1"]="Item";_.entity_label["7"]="Item Set";_.entity_label["2"]="NPC";_.entity_label["5"]="Object";_.entity_label["9"]="Profile";_.entity_label["4"]="Quest";_.entity_label["6"]="Spell";_.entity_label["111"]="Talent Build";_.entity_label["200"]="User";_.entity_label["3"]="Zone";_.entity_label_plural={};_.entity_label_plural["23"]="Achievements";_.entity_label_plural["8"]="Factions";_.entity_label_plural["10"]="Guilds";_.entity_label_plural["7"]="Item Sets";_.entity_label_plural["1"]="Items";_.entity_label_plural["2"]="NPCs";_.entity_label_plural["5"]="Objects";_.entity_label_plural["9"]="Profiles";_.entity_label_plural["4"]="Quests";_.entity_label_plural["6"]="Spells";_.entity_label_plural["111"]="Talent Builds";_.entity_label_plural["200"]="Users";_.entity_link={};_.entity_link["23"]="achievement.aspx";_.entity_link["8"]="faction.aspx";_.entity_link["5"]="gameobject.aspx";_.entity_link["10"]="guild.aspx";_.entity_link["1"]="item.aspx";_.entity_link["7"]="itemset.aspx";_.entity_link["3"]="location.aspx";_.entity_link["2"]="npc.aspx";_.entity_link["9"]="profile.aspx";_.entity_link["4"]="quest.aspx";_.entity_link["6"]="spell.aspx";_.entity_link["111"]="talent.aspx";_.entity_link["200"]="user.aspx";_.faction_level={};_.faction_level["0"]="Hated";_.faction_level["1"]="Hostile";_.faction_level["2"]="Unfriendly";_.faction_level["3"]="Neutral";_.faction_level["4"]="Friendly";_.faction_level["5"]="Honored";_.faction_level["6"]="Revered";_.faction_level["7"]="Exalted";_.filter_drop_mode={};_.filter_drop_mode["1"]="Normal Mode Only";_.filter_drop_mode["2"]="Heroic Mode Only";_.filter_drop_mode["1|2"]="Normal or Heroic Mode";_.filter_drop_mode["1^2"]="Normal and Heroic Mode";_.filter_quest_side={};_.filter_quest_side["2|4|*"]="Any";_.filter_quest_side["2"]="Alliance";_.filter_quest_side["4"]="Horde";_.filter_quest_side["1"]="Both";_.filter_result_id={};_.filter_result_id["spell_effect_agility"]="Agility";_.filter_result_id["item_effect_attack_power"]="apow";_.filter_result_id["spell_effect_spell_damage_arcane"]="ArcaneDmg";_.filter_result_id["spell_resist_6"]="ArcaneRes";_.filter_result_id["item_required_pvp_rating"]="Arena Rating";_.filter_result_id["item_required_pvp_rating"]="ArenaRating";_.filter_result_id["spell_effect_penetration_armor"]="ArmorPen";_.filter_result_id["spell_effect_attack_power"]="AtkPwr";_.filter_result_id["item_effect_block_rating"]="block";_.filter_result_id["spell_effect_block_rating"]="BlockRating";_.filter_result_id["spell_cost_rune_blood"]="BloodRunes";_.filter_result_id["spell_cost"]="Cost";_.filter_result_id["spell_cost_percent"]="CostPct";_.filter_result_id["item_effect_crit_rating"]="crit";_.filter_result_id["spell_effect_crit_rating"]="CritRating";_.filter_result_id["spell_effect_defense_skill_rating"]="DefenseRating";_.filter_result_id["item_effect_dodge_rating"]="dodge";_.filter_result_id["spell_effect_dodge_rating"]="DodgeRating";_.filter_result_id["item_effect_expertise_rating"]="exp";_.filter_result_id["spell_effect_expertise_rating"]="ExpRating";_.filter_result_id["spell_effect_power_feral"]="FeralAtkPwr";_.filter_result_id["spell_effect_spell_damage_fire"]="FireDmg";_.filter_result_id["spell_resist_2"]="FireRes";_.filter_result_id["item_effect_power_feral"]="fpow";_.filter_result_id["spell_effect_spell_damage_frost"]="FrostDmg";_.filter_result_id["spell_resist_4"]="FrostRes";_.filter_result_id["spell_cost_rune_frost"]="FrostRunes";_.filter_result_id["item_effect_haste_rating"]="haste";_.filter_result_id["spell_effect_health"]="Health";_.filter_result_id["spell_effect_regen_health"]="HealthRegen";_.filter_result_id["item_effect_hit_rating"]="hit";_.filter_result_id["spell_effect_hit_rating"]="HitRating";_.filter_result_id["spell_effect_spell_damage_holy"]="HolyDmg";_.filter_result_id["spell_resist_1"]="HolyRes";_.filter_result_id["npc_health_max"]="HP";_.filter_result_id["spell_effect_intellect"]="Intellect";_.filter_result_id["spell_effect_mana"]="Mana";_.filter_result_id["spell_effect_regen_mana"]="ManaRegen";_.filter_result_id["item_effect_power_melee"]="mpow";_.filter_result_id["spell_effect_spell_damage_nature"]="NatureDmg";_.filter_result_id["spell_resist_3"]="NatureRes";_.filter_result_id["spell_effect_parry_rating"]="ParryRating";_.filter_result_id["spell_resist_0"]="PhysicalRes";_.filter_result_id["spell_effect_resilience_rating"]="ResilRating";_.filter_result_id["spell_cost_runic_power"]="RunicPower";_.filter_result_id["item_effect_shield_block"]="sblock";_.filter_result_id["216"]="score";_.filter_result_id["spell_effect_spell_damage_shadow"]="ShadowDmg";_.filter_result_id["spell_resist_5"]="ShadowRes";_.filter_result_id["spell_effect_shield_block"]="ShieldBlock";_.filter_result_id["spell_effect_penetration_spell"]="SpellPen";_.filter_result_id["spell_effect_spell_power"]="SpellPwr";_.filter_result_id["spell_effect_spirit"]="Spirit";_.filter_result_id["item_effect_spell_power"]="spower";_.filter_result_id["spell_effect_stamina"]="Stamina";_.filter_result_id["spell_effect_strength"]="Strength";_.filter_result_id["spell_cost_rune_unholy"]="UnholyRunes";_.filter_result_id["spell_effect_weapon_damage"]="WepnDmg";_.filter_result_id["quest_reward_xp"]="XP";_.filter_result_id["quest_reward_xp"]="XP";_.filter_yes_no={};_.filter_yes_no["1"]="Yes";_.filter_yes_no["0"]="No";_.gem_color={};_.gem_color["1|2|4|8"]="All";_.gem_color["8"]="Blue";_.gem_color["1"]="Meta";_.gem_color["2"]="Red";_.gem_color["4"]="Yellow";_.gender={};_.gender["3"]="Female";_.gender["2"]="Male";_.glyph_type={};_.glyph_type["0"]="Major Glyph";_.glyph_type["0"]="Major Glyph";_.glyph_type["1"]="Minor Glyph";_.item_effect_defense_skill_rating={};_.item_effect_defense_skill_rating["item_effect_defense_skill_rating"]="def";_.item_socket_color_id={};_.item_socket_color_id["8"]="Blue Socket";_.item_socket_color_id["1"]="Meta Socket";_.item_socket_color_id["2"]="Red Socket";_.item_socket_color_id["4"]="Yellow Socket";_.item_source={};_.item_source["4"]="Crafted";_.item_source["11"]="Disenchanting";_.item_source["1"]="Drop";_.item_source["6"]="Gathered";_.item_source["5"]="Mined";_.item_source["10"]="Pick Pocketed";_.item_source["7"]="Prospected";_.item_source["3"]="Quest";_.item_source["9"]="Skinned";_.item_source["2"]="Vendor";_.item_source["8"]="PvP";_.itemset_tag={};_.itemset_tag["13"]="";_.itemset_tag["14"]="";_.itemset_tag["1"]="";_.itemset_tag["2"]="";_.itemset_tag["3"]="";_.itemset_tag["4"]="";_.itemset_tag["5"]="";_.itemset_tag["6"]="";_.itemset_tag["7"]="";_.itemset_tag["9"]="";_.itemset_tag["8"]="";_.itemset_tag["11"]="";_.itemset_tag["10"]="";_.itemset_tag["15"]="";_.itemset_tag["16"]="";_.itemset_tag["17"]="";_.itemset_tag["18"]="";_.itemset_tag["19"]="";_.itemset_tag["20"]="";_.itemset_tag["12"]="";_.itemset_tag["21"]="";_.language_label={};_.language_label["en"]="English";_.language_label["fr"]="Français";_.language_label["cn"]="简体中文";_.locale_stylesheet={};_.locale_stylesheet["cn"]="main_cn.css";_.location_category_id={};_.location_category_id["7"]="Northrend";_.location_category_id["0"]="Eastern Kingdoms";_.location_category_id["1"]="Kalimdor";_.location_category_id["2"]="Outland";_.location_category_id["3"]="Dungeons";_.location_category_id["4"]="Raids";_.location_category_id["5"]="Battlegrounds";_.location_category_id["6"]="Arenas";_.location_expansion_id={};_.location_expansion_id["2"]="";_.location_expansion_id["1"]="The Burning Crusade";_.location_faction_id={};_.location_faction_id["1"]="Contested";_.location_faction_id["2"]="Alliance";_.location_faction_id["4"]="Horde";_.location_faction_id["5"]="PvP";_.location_faction_id["3"]="Sancutuary";_.location_type_id={};_.location_type_id["0"]="";_.location_type_id["4"]="Arena";_.location_type_id["7"]="City";_.location_type_id["6"]="Transit";_.location_type_id["3"]="Battleground";_.location_type_id["1"]="Dungeon";_.location_type_id["5"]="Heroic Dungeon";_.location_type_id["2"]="Raid";_.npc_classification_id={};_.npc_classification_id["3"]="Boss";_.npc_classification_id["1"]="Elite";_.npc_classification_id["4"]="Rare";_.npc_classification_id["2"]="Rare Elite";_.npc_family_id={};_.npc_family_id["38"]="Chimaera";_.npc_family_id["45"]="Core Hound";_.npc_family_id["39"]="Devilsaur";_.npc_family_id["40"]="Ghoul";_.npc_family_id["37"]="Moth";_.npc_family_id["43"]="Rhino";_.npc_family_id["41"]="Silithid";_.npc_family_id["46"]="Spirit Beast";_.npc_family_id["44"]="Wasp";_.npc_family_id["42"]="Worm";_.npc_type_id={};_.npc_type_id["1"]="Beast";_.npc_type_id["8"]="Critter";_.npc_type_id["3"]="Demon";_.npc_type_id["2"]="Dragonkin";_.npc_type_id["4"]="Elemental";_.npc_type_id["13"]="Gas Cloud";_.npc_type_id["5"]="Giant";_.npc_type_id["7"]="Humanoid";_.npc_type_id["9"]="Mechanical";_.npc_type_id["12"]="Non-combat Pet";_.npc_type_id["10"]="Not specified";_.npc_type_id["11"]="Totem";_.npc_type_id["6"]="Undead";_.object_type_id={};_.object_type_id["9"]="Books";_.object_type_id["3"]="Containers";_.object_type_id["25"]="Fish Schools";_.object_type_id["-2"]="Herbs";_.object_type_id["-3"]="Locked Chests";_.object_type_id["-1"]="Mineral Veins";_.object_type_id["2"]="Quest";_.patch_label={};_.patch_label["6546"]="2.0";_.patch_label["6692"]="2.1";_.patch_label["7272"]="2.2";_.patch_label["7318"]="2.2";_.patch_label["7741"]="2.3.2";_.patch_label["7799"]="2.3";_.patch_label["7561"]="2.3";_.patch_label["8209"]="2.4.2";_.patch_label["8278"]="2.4.2";_.patch_label["8268"]="2.4.2";_.patch_label["8125"]="2.4";_.patch_label["7897"]="2.4";_.patch_label["7923"]="2.4";_.patch_label["7948"]="2.4";_.patch_label["7958"]="2.4";_.patch_label["7962"]="2.4";_.patch_label["7979"]="2.4";_.patch_label["7994"]="2.4";_.patch_label["8016"]="2.4";_.patch_label["8031"]="2.4";_.patch_label["8049"]="2.4";_.patch_label["8063"]="2.4";_.patch_label["8089"]="2.4";_.patch_label["8478"]="2.4.3";_.patch_label["8536"]="2.4.3";_.patch_label["8600"]="2.4.3";_.patch_label["8606"]="2.4.3";_.patch_label["8634"]="3.0.1 (WotLK)";_.patch_label["9464"]="3.0.8";_.patch_label["9183"]="3.03 (WotLK)";_.pet_talent_tree={};_.pet_talent_tree["4"]="409";_.pet_talent_tree["5"]="409";_.pet_talent_tree["8"]="409";_.pet_talent_tree["6"]="409";_.pet_talent_tree["9"]="409";_.pet_talent_tree["43"]="409";_.pet_talent_tree["20"]="409";_.pet_talent_tree["21"]="409";_.pet_talent_tree["32"]="409";_.pet_talent_tree["44"]="410";_.pet_talent_tree["25"]="410";_.pet_talent_tree["37"]="410";_.pet_talent_tree["45"]="410";_.pet_talent_tree["11"]="410";_.pet_talent_tree["46"]="410";_.pet_talent_tree["1"]="410";_.pet_talent_tree["42"]="410";_.pet_talent_tree["12"]="410";_.pet_talent_tree["39"]="410";_.pet_talent_tree["7"]="410";_.pet_talent_tree["2"]="410";_.pet_talent_tree["38"]="411";_.pet_talent_tree["30"]="411";_.pet_talent_tree["26"]="411";_.pet_talent_tree["24"]="411";_.pet_talent_tree["33"]="411";_.pet_talent_tree["31"]="411";_.pet_talent_tree["34"]="411";_.pet_talent_tree["27"]="411";_.pet_talent_tree["35"]="411";_.pet_talent_tree["41"]="411";_.pet_talent_tree["3"]="411";_.pp_faction_id={};_.pp_faction_id["0"]="Alliance";_.pp_faction_id["1"]="Horde";_.profession_id={};_.profession_id["773"]="Inscription";_.quality={};_.quality["0"]="Poor";_.quality["1"]="Common";_.quality["2"]="Uncommon";_.quality["3"]="Rare";_.quality["4"]="Epic";_.quality["5"]="Legendary";_.quality["6"]="Artifact";_.quality["7"]="Heirloom";_.quest_side_id={};_.quest_side_id["1"]="Both";_.quest_side_id["2"]="Alliance";_.quest_side_id["4"]="Horde";_.quest_sort={};_.quest_sort["372"]="Death Knight";_.quest_sort["371"]="Inscription";_.quest_sort["373"]="Jewelcrafting";_.quest_type_id={};_.quest_type_id["87"]="";_.quest_type_id["83"]="Legendary";_.quest_type_id["21"]="Life";_.quest_type_id["88"]="Raid (10)";_.quest_type_id["89"]="Raid (25)";_.quest_type_id["82"]="World Event";_.quest_type_id["0"]="Normal";_.quest_type_id["1"]="Group";_.quest_type_id["81"]="Dungeon";_.quest_type_id["62"]="Raid";_.quest_type_id["41"]="PvP";_.quest_type_id["84"]="Escort";_.quest_type_id["85"]="Heroic";_.race_id={};_.race_id["10"]="Blood Elf";_.race_id["11"]="Draenei";_.race_id["3"]="Dwarf";_.race_id["7"]="Gnome";_.race_id["1"]="Human";_.race_id["4"]="Night Elf";_.race_id["2"]="Orc";_.race_id["6"]="Tauren";_.race_id["8"]="Troll";_.race_id["5"]="Undead";_.resistance_level={};_.resistance_level["5"]="Excellent";_.resistance_level["2"]="Fair";_.resistance_level["3"]="Good";_.resistance_level["0"]="None";_.resistance_level["1"]="Poor";_.resistance_level["4"]="Very Good";_.resistances={};_.resistances["6"]="Arcane";_.resistances["2"]="Fire";_.resistances["4"]="Frost";_.resistances["1"]="Holy";_.resistances["3"]="Nature";_.resistances["0"]="Physical";_.resistances["5"]="Shadow";_.site_url={};_.site_url["cn"]="cn.wowdb.com";_.site_url["fr"]="fr.wowdb.com";_.site_url["en"]="www.wowdb.com";_.skill_line_category_id={};_.skill_line_category_id["7"]="Class Skills";_.skill_line_category_id["-2"]="Talents";_.skill_line_category_id["11"]="Professions";_.skill_line_category_id["-3"]="Pet Skills";_.skill_line_category_id["9"]="Secondary Skills";_.skill_line_category_id["-4"]="Racial Abilities";_.skill_line_category_id["6"]="Weapon Skills";_.skill_line_category_id["8"]="Armor Proficiencies";_.skill_line_category_id["10"]="Languages";_.slot={};_.slot["17"]="Two-Hand";_.slot["24"]="Ammo";_.slot["18"]="Bag";_.slot["4"]="Shirt";_.slot["5"]="Chest";_.slot["16"]="Back";_.slot["8"]="Feet";_.slot["11"]="Finger";_.slot["10"]="Hands";_.slot["1"]="Head";_.slot["23"]="Held In Off-hand";_.slot["7"]="Legs";_.slot["2"]="Neck";_.slot["15"]="Ranged";_.slot["26"]="Ranged";_.slot["27"]="Ranged";_.slot["28"]="Relic";_.slot["14"]="Off Hand";_.slot["3"]="Shoulder";_.slot["19"]="Tabard";_.slot["25"]="Thrown";_.slot["12"]="Trinket";_.slot["6"]="Waist";_.slot["13"]="One-Hand";_.slot["21"]="Main Hand";_.slot["22"]="Off Hand";_.slot["9"]="Wrist";_.spell_cost_type_id={};_.spell_cost_type_id["-2"]="Health";_.spell_cost_type_id["3"]="Energy";_.spell_cost_type_id["2"]="Focus";_.spell_cost_type_id["0"]="Mana";_.spell_cost_type_id["1"]="Rage";_.spell_dispel_type_id={};_.spell_dispel_type_id["11"]="ZZOLD UNUSED";_.spell_focus_object_id={};_.spell_focus_object_id["1581"]="Ahn'kahet Brazier";_.spell_focus_object_id["1556"]="Altar of Kartak";_.spell_focus_object_id["1591"]="Altar of the Ebon Depths";_.spell_focus_object_id["1588"]="Alumeth's Soulstone";_.spell_focus_object_id["1550"]="Ancient Dirt Mound";_.spell_focus_object_id["1566"]="Archbishop Landgren's corpse and Lord-Commander Arete not be present";_.spell_focus_object_id["1529"]="Basecamp";_.spell_focus_object_id["1495"]="Beryl Force Shield";_.spell_focus_object_id["1475"]="Broken Tablet";_.spell_focus_object_id["1467"]="cannon on northern Alliance wall.";_.spell_focus_object_id["1472"]="Cauldron of Vrykul Blood.";_.spell_focus_object_id["1477"]="Center of Baleheim";_.spell_focus_object_id["1520"]="Center of the pit under the Surge Needle";_.spell_focus_object_id["1560"]="Convocation Summoning Circle";_.spell_focus_object_id["1592"]="Corpse of the Fallen Worg";_.spell_focus_object_id["1502"]="Crashed Flying Machine";_.spell_focus_object_id["1587"]="Cultists' Cauldron";_.spell_focus_object_id["1583"]="Dalaran Fountain";_.spell_focus_object_id["1496"]="Den of Dying Plague Cauldron";_.spell_focus_object_id["1522"]="depths below Vordrassil's Limb.";_.spell_focus_object_id["1521"]="depths below Vordrassil's remains.";_.spell_focus_object_id["1523"]="depths below Vordrassil's Tears.";_.spell_focus_object_id["1518"]="Drakil'jin Gongs";_.spell_focus_object_id["1548"]="Drakkari Pedestal";_.spell_focus_object_id["1544"]="Drakkari Ritual Statue";_.spell_focus_object_id["1509"]="Drakuru's Brazier";_.spell_focus_object_id["1510"]="East End of Thor Modan Tunnel";_.spell_focus_object_id["1491"]="En'kilah Cauldron";_.spell_focus_object_id["1564"]="Exposed Lifeblood Pillar Crystal";_.spell_focus_object_id["1549"]="Fallen Log";_.spell_focus_object_id["1505"]="Farshire Grain";_.spell_focus_object_id["1545"]="Finklestein's Cauldron";_.spell_focus_object_id["1514"]="Fizzcrank Paratrooper Teleporter";_.spell_focus_object_id["1513"]="Fizzcrank Practice Parachute Zone";_.spell_focus_object_id["1569"]="Frostgut's Altar";_.spell_focus_object_id["1540"]="Frostmourne Altar";_.spell_focus_object_id["1469"]="Frozen Waterfall";_.spell_focus_object_id["1570"]="Garm Teleporter";_.spell_focus_object_id["1503"]="Geyser Fields Steam Vent";_.spell_focus_object_id["1557"]="Great Lightning Stone";_.spell_focus_object_id["1586"]="Grimkor's Orb";_.spell_focus_object_id["1525"]="Grom'thar's Challenging Area";_.spell_focus_object_id["1524"]="Hansel Bauer";_.spell_focus_object_id["1473"]="Harris's Plague Samples.";_.spell_focus_object_id["1573"]="Icy Crater";_.spell_focus_object_id["1494"]="Imprisoned Beryl Sorcerer";_.spell_focus_object_id["1480"]="Iron Dwarf Transport Rune";_.spell_focus_object_id["1577"]="K3 Landing";_.spell_focus_object_id["1553"]="Landing Pad";_.spell_focus_object_id["1516"]="Ley Line Focus in the Lothalor Woodlands";_.spell_focus_object_id["1515"]="Ley Line Focus on the Beach";_.spell_focus_object_id["1517"]="Ley Line Focus Under the Lake";_.spell_focus_object_id["1568"]="Magmawyrm Resurrection Chamber";_.spell_focus_object_id["1580"]="Malykriss Furnace";_.spell_focus_object_id["1519"]="Marked Coldwind Tree";_.spell_focus_object_id["1546"]="Mistwhisper Weather Shrine";_.spell_focus_object_id["1585"]="Mord'rethar Plague Cauldron";_.spell_focus_object_id["1532"]="Necromantic Rune";_.spell_focus_object_id["1489"]="Nerub'ar Sinkhole";_.spell_focus_object_id["1551"]="Nerubian Crater";_.spell_focus_object_id["1527"]="New Hearthglen Lumbermill";_.spell_focus_object_id["1504"]="Nexus Dimensional Rift";_.spell_focus_object_id["1572"]="Northern Ice Spike";_.spell_focus_object_id["1508"]="Northwest Nexus Entrance";_.spell_focus_object_id["1530"]="Obsidian Dragonshrine Exit Path";_.spell_focus_object_id["1547"]="Offering Bowl";_.spell_focus_object_id["1579"]="Pile of Crusader Skulls";_.spell_focus_object_id["1507"]="proximity to a Scourge sinkhole.";_.spell_focus_object_id["1470"]="proximity to plaguehound cages at Vengeance Landing.";_.spell_focus_object_id["1555"]="Quetz'lun's Corpse and that the Prophet of Quetz'lun be present and unengaged";_.spell_focus_object_id["1552"]="Runeforge";_.spell_focus_object_id["1537"]="Ruuna's Crystal Ball";_.spell_focus_object_id["1582"]="Sanctum of Reanimation Slab";_.spell_focus_object_id["1506"]="Saragosa's Landing";_.spell_focus_object_id["1534"]="School of Northern Salmon";_.spell_focus_object_id["1567"]="School of Tasty Reef Fish";_.spell_focus_object_id["1543"]="Scourged Mummy Fire";_.spell_focus_object_id["1554"]="Scourgewagon";_.spell_focus_object_id["1466"]="Shattered Strand Cannon";_.spell_focus_object_id["1500"]="Shattered Sun Portal";_.spell_focus_object_id["1590"]="Smoldering Scrap";_.spell_focus_object_id["1490"]="Snarlfang's Totem";_.spell_focus_object_id["1575"]="Snowdrift";_.spell_focus_object_id["1561"]="Soaked Fertile Dirt";_.spell_focus_object_id["1497"]="South Sinkhole";_.spell_focus_object_id["1538"]="Squire Percy";_.spell_focus_object_id["1476"]="Sunken Boat";_.spell_focus_object_id["1578"]="Thane Illskar be present and that he and his champions not be engaged in battle";_.spell_focus_object_id["1571"]="Thane Ufrang the Mighty present and seated";_.spell_focus_object_id["1539"]="that you be closer to Alystros the Verdant Keeper";_.spell_focus_object_id["1541"]="that you be on the sands of the Bronze Dragonshrine";_.spell_focus_object_id["1576"]="the Broodmother's Nest";_.spell_focus_object_id["1468"]="The Thane's Pyre";_.spell_focus_object_id["1465"]="The Wyrmlord's Platform";_.spell_focus_object_id["1512"]="Toalu'u's Brazier";_.spell_focus_object_id["1562"]="Top of Sholazar Pillar";_.spell_focus_object_id["1563"]="Top of the Glimmering Pillar";_.spell_focus_object_id["1559"]="Top of the Suntouched Pillar";_.spell_focus_object_id["1511"]="Tua'kea's Fishing Hook";_.spell_focus_object_id["1596"]="Unlit Signal Fire";_.spell_focus_object_id["1565"]="Uzo Deathcaller";_.spell_focus_object_id["1574"]="Vanguard Infirmary";_.spell_focus_object_id["1528"]="Vordrassil's Sapling";_.spell_focus_object_id["1486"]="Vrykul Burial Mound";_.spell_focus_object_id["1542"]="Vrykul Hawk Roost";_.spell_focus_object_id["1479"]="Walt's Transport Rune";_.spell_focus_object_id["1558"]="Warlord Zol'Maz present and unengaged in combat.";_.spell_focus_object_id["1487"]="Westrift Seismic Anomaly";_.spell_focus_object_id["1526"]="Wintergarde Gryphon Station";_.spell_focus_object_id["1584"]="Ymirheim Peak Skulls";_.spell_focus_object_id["1492"]="you to be at or near the front of a Kvaldir ship.";_.spell_focus_object_id["1498"]="you to be at the crossroads, east of Warsong Hold with an Alliance Deserter in tow.";_.spell_focus_object_id["1536"]="you to be at the front of the Wintergarde Mausoleum.";_.spell_focus_object_id["1474"]="you to be closer to plaguehound cages.";_.spell_focus_object_id["1589"]="you to be near a Lexicon of Power";_.spell_focus_object_id["1533"]="you to be standing at one of the entrance points to Wintergarde Mine.";_.spell_focus_object_id["1493"]="you to be standing at the end of Warsong Jetty.";_.spell_focus_object_id["1535"]="you to be standing in the center of the ruins at the Forgotten Shore.";_.spell_focus_object_id["1499"]="you to be standing next to Bloodmage Laurith at the Bloodspore Plains.";_.spell_focus_object_id["1531"]="you to be standing next to Siege Engineer Quarterflash.";_.spell_form_id={};_.spell_form_id["23"]="";_.spell_form_id["24"]="";_.spell_form_id["892"]="";_.spell_form_id["26"]="Frenzy";_.spell_form_id["22"]="Metamorphosis";_.spell_form_id["11"]="REUSE";_.spell_form_id["12"]="REUSE";_.spell_form_id["13"]="REUSE";_.spell_form_id["9"]="Steve's Ghoul";_.spell_form_id["10"]="Tharon'ja Skeleton";_.spell_form_id["25"]="Undead";_.spell_mechanic_id={};_.spell_mechanic_id["31"]="enraged";_.spell_range={};_.spell_range["173"]="Anywhere (Combat Min Range)";_.spell_range["156"]="Boulder Range";_.spell_range["164"]="Catapult Range";_.spell_range["153"]="Charge, 60";_.spell_range["163"]="Death Grip";_.spell_range["172"]="Eleven yards";_.spell_range["170"]="Fifty Five Yards";_.spell_range["165"]="Fourteen yards";_.spell_range["139"]="Long Range Hunter Shoot";_.spell_range["160"]="Medium/Long Range";_.spell_range["162"]="Medium/Short Range";_.spell_range["168"]="Medium-Long Range (38)";_.spell_range["171"]="Min Range 5, 10";_.spell_range["176"]="Seventy yards";_.spell_range["161"]="Short/Long Range";_.spell_range["152"]="Super Long";_.spell_range["167"]="Super Long (Min Range)";_.spell_range["158"]="Super Long, 15 Min";_.spell_range["159"]="TEST - Long Range";_.spell_range["166"]="Thirteen yards";_.spell_range["169"]="Three Yards";_.spell_range["154"]="Tower 80, 10";_.spell_range["174"]="U L T R A";_.spell_range["150"]="Valgarde 8/100";_.stat_label={};_.stat_label["6"]="Armor";_.stat_label["19"]="Bonus Damage";_.stat_label["20"]="Bonus Healing";_.stat_label["13"]="Weapon Skill";_.stat_label["14"]="Weapon Skill";_.stat_label["18"]="Crit Rating";_.stat_label["28"]="Resilience";_.stat_label["24"]="Defense Rating";_.stat_label["30"]="Defense Rating";_.stat_label["12"]="Expertise";_.stat_label["25"]="Dodge Rating";_.stat_label["26"]="Parry Rating";_.stat_label["27"]="Block Rating";_.stat_label["10"]="Hit Rating";_.stat_label["17"]="Hit Rating";_.stat_label["21"]="Hit Rating";_.stat_label["11"]="Crit Rating";_.stat_label["36"]="HP";_.stat_label["23"]="Mana Regen";_.stat_label["37"]="Mana";_.stat_label["9"]="Melee Attack Power";_.stat_label["16"]="Ranged Attack Power";_.stat_label["15"]="Speed";_.stat_label["29"]="Haste Rating";_.stat_label["22"]="Penetration";_.stat_label["1"]="Strength";_.stat_label["2"]="Agility";_.stat_label["3"]="Stamina";_.stat_label["4"]="Intellect";_.stat_label["5"]="Spirit";_.stat_label["31"]="Arcane";_.stat_label["32"]="Fire";_.stat_label["34"]="Frost";_.stat_label["33"]="Nature";_.stat_label["35"]="Shadow";_.stat_label["40"]="Armor Penetration";_.stat_label["7"]="Melee Damage";_.stat_label["8"]="Melee Speed";_.stat_label["39"]="Offhand Damage";_.stat_label["38"]="Offhand Speed";_.talent_build_type={};_.talent_build_type["4"]="Arena";_.talent_build_type["2"]="PvE";_.talent_build_type["1"]="PvP";_.talent_build_type["3"]="Raid";_.totem_category_id={};_.totem_category_id["3"]="Air Totem";_.totem_category_id["14"]="Arclight Spanner";_.totem_category_id["162"]="Blacksmith Hammer";_.totem_category_id["13"]="Blacksmith Hammer (OLD)";_.totem_category_id["168"]="Bladed Pickaxe";_.totem_category_id["141"]="Drums";_.totem_category_id["2"]="Earth Totem";_.totem_category_id["4"]="Fire Totem";_.totem_category_id["169"]="Flint and Tinder";_.totem_category_id["161"]="Gnomish Army Knife";_.totem_category_id["15"]="Gyromatic Micro-Adjustor";_.totem_category_id["167"]="Hammer Pick";_.totem_category_id["81"]="Hollow Quill";_.totem_category_id["21"]="Master Totem";_.totem_category_id["165"]="Mining Pick";_.totem_category_id["11"]="Mining Pick (OLD)";_.totem_category_id["12"]="Philosopher's Stone";_.totem_category_id["62"]="Runed Adamantite Rod";_.totem_category_id["10"]="Runed Arcanite Rod";_.totem_category_id["101"]="Runed Azurite Rod";_.totem_category_id["189"]="Runed Cobalt Rod";_.totem_category_id["6"]="Runed Copper Rod";_.totem_category_id["63"]="Runed Eternium Rod";_.totem_category_id["41"]="Runed Fel Iron Rod";_.totem_category_id["8"]="Runed Golden Rod";_.totem_category_id["7"]="Runed Silver Rod";_.totem_category_id["190"]="Runed Titanium Rod";_.totem_category_id["9"]="Runed Truesilver Rod";_.totem_category_id["166"]="Skinning Knife";_.totem_category_id["1"]="Skinning Knife (OLD)";_.totem_category_id["121"]="Virtuoso Inking Set";_.totem_category_id["5"]="Water Totem";_.location_name=[];_.location_name["4494"]="Ahn'kahet: The Old Kingdom";_.location_name["3428"]="Ahn'Qiraj";_.location_name["2079"]="Alcaz Island";_.location_name["36"]="Alterac Mountains";_.location_name["2597"]="Alterac Valley";_.location_name["3526"]="Ammen Vale";_.location_name["3358"]="Arathi Basin";_.location_name["45"]="Arathi Highlands";_.location_name["331"]="Ashenvale";_.location_name["3790"]="Auchenai Crypts";_.location_name["3917"]="Auchindoun";_.location_name["4277"]="Azjol-Nerub";_.location_name["3477"]="Azjol-Nerub";_.location_name["16"]="Azshara";_.location_name["268"]="Azshara Crater";_.location_name["3524"]="Azuremyst Isle";_.location_name["3"]="Badlands";_.location_name["3959"]="Black Temple";_.location_name["719"]="Blackfathom Deeps";_.location_name["1584"]="Blackrock Depths";_.location_name["25"]="Blackrock Mountain";_.location_name["1583"]="Blackrock Spire";_.location_name["2677"]="Blackwing Lair";_.location_name["3702"]="Blade's Edge Arena";_.location_name["3522"]="Blade's Edge Mountains";_.location_name["4"]="Blasted Lands";_.location_name["3525"]="Bloodmyst Isle";_.location_name["35"]="Booty Bay";_.location_name["3537"]="Borean Tundra";_.location_name["46"]="Burning Steppes";_.location_name["221"]="Camp Narache";_.location_name["1941"]="Caverns of Time";_.location_name["3565"]="Cenarion Refuge";_.location_name["2918"]="Champions' Hall";_.location_name["3459"]="City";_.location_name["3905"]="Coilfang Reservoir";_.location_name["132"]="Coldridge Valley";_.location_name["2817"]="Crystalsong Forest";_.location_name["4395"]="Dalaran";_.location_name["279"]="Dalaran Crater";_.location_name["148"]="Darkshore";_.location_name["1657"]="Darnassus";_.location_name["41"]="Deadwind Pass";_.location_name["154"]="Deathknell";_.location_name["2257"]="Deeprun Tram";_.location_name["151"]="Designer Island";_.location_name["405"]="Desolace";_.location_name["2557"]="Dire Maul";_.location_name["65"]="Dragonblight";_.location_name["4196"]="Drak'Tharon Keep";_.location_name["269"]="Dun Algaz";_.location_name["1"]="Dun Morogh";_.location_name["14"]="Durotar";_.location_name["10"]="Duskwood";_.location_name["15"]="Dustwallow Marsh";_.location_name["139"]="Eastern Plaguelands";_.location_name["12"]="Elwynn Forest";_.location_name["3430"]="Eversong Woods";_.location_name["3820"]="Eye of the Storm";_.location_name["1116"]="Feathermoon Stronghold";_.location_name["361"]="Felwood";_.location_name["357"]="Feralas";_.location_name["3478"]="Gates of Ahn'Qiraj";_.location_name["3433"]="Ghostlands";_.location_name["876"]="GM Island";_.location_name["721"]="Gnomeregan";_.location_name["394"]="Grizzly Hills";_.location_name["3923"]="Gruul's Lair";_.location_name["4416"]="Gundrak";_.location_name["2917"]="Hall of Legends";_.location_name["4272"]="Halls of Lightning";_.location_name["4264"]="Halls of Stone";_.location_name["3535"]="Hellfire Citadel";_.location_name["3483"]="Hellfire Peninsula";_.location_name["3562"]="Hellfire Ramparts";_.location_name["267"]="Hillsbrad Foothills";_.location_name["495"]="Howling Fjord";_.location_name["3606"]="Hyjal Summit";_.location_name["210"]="Icecrown";_.location_name["1537"]="Ironforge";_.location_name["4080"]="Isle of Quel'Danas";_.location_name["3457"]="Karazhan";_.location_name["21"]="Kul Tiras";_.location_name["38"]="Loch Modan";_.location_name["170"]="Lordamere Lake";_.location_name["4095"]="Magisters' Terrace";_.location_name["3836"]="Magtheridon's Lair";_.location_name["3792"]="Mana-Tombs";_.location_name["2100"]="Maraudon";_.location_name["2717"]="Molten Core";_.location_name["493"]="Moonglade";_.location_name["215"]="Mulgore";_.location_name["3518"]="Nagrand";_.location_name["3698"]="Nagrand Arena";_.location_name["3456"]="Naxxramas";_.location_name["3523"]="Netherstorm";_.location_name["24"]="Northshire Abbey";_.location_name["9"]="Northshire Valley";_.location_name["2367"]="Old Hillsbrad Foothills";_.location_name["2159"]="Onyxia's Lair";_.location_name["1637"]="Orgrimmar";_.location_name["4298"]="Plaguelands: The Scarlet Enclave";_.location_name["2037"]="Quel'thalas";_.location_name["2437"]="Ragefire Chasm";_.location_name["722"]="Razorfen Downs";_.location_name["491"]="Razorfen Kraul";_.location_name["220"]="Red Cloud Mesa";_.location_name["44"]="Redridge Mountains";_.location_name["3429"]="Ruins of Ahn'Qiraj";_.location_name["3968"]="Ruins of Lordaeron";_.location_name["702"]="Rut'theran Village";_.location_name["796"]="Scarlet Monastery";_.location_name["2057"]="Scholomance";_.location_name["51"]="Searing Gorge";_.location_name["3607"]="Serpentshrine Cavern";_.location_name["3791"]="Sethekk Halls";_.location_name["3789"]="Shadow Labyrinth";_.location_name["209"]="Shadowfang Keep";_.location_name["188"]="Shadowglen";_.location_name["3520"]="Shadowmoon Valley";_.location_name["3703"]="Shattrath City";_.location_name["3711"]="Sholazar Basin";_.location_name["1377"]="Silithus";_.location_name["3487"]="Silvermoon City";_.location_name["130"]="Silverpine Forest";_.location_name["3679"]="Skettis";_.location_name["406"]="Stonetalon Mountains";_.location_name["1519"]="Stormwind City";_.location_name["4384"]="Strand of the Ancients";_.location_name["33"]="Stranglethorn Vale";_.location_name["2017"]="Stratholme";_.location_name["1417"]="Sunken Temple";_.location_name["3431"]="Sunstrider Isle";_.location_name["4075"]="Sunwell Plateau";_.location_name["8"]="Swamp of Sorrows";_.location_name["440"]="Tanaris";_.location_name["141"]="Teldrassil";_.location_name["3842"]="Tempest Keep";_.location_name["3845"]="Tempest Keep";_.location_name["3519"]="Terokkar Forest";_.location_name["330"]="Thandol Span";_.location_name["3848"]="The Arcatraz";_.location_name["17"]="The Barrens";
	_.location_name["3696"]="The Barrier Hills";
	_.location_name["2366"]="The Black Morass";_.location_name["3840"]="The Black Temple";_.location_name["3713"]="The Blood Furnace";_.location_name["3847"]="The Botanica";_.location_name["4100"]="The Culling of Stratholme";_.location_name["1581"]="The Deadmines";_.location_name["3557"]="The Exodar";_.location_name["4500"]="The Eye of Eternity";_.location_name["308"]="The Forbidding Sea";_.location_name["207"]="The Great Sea";_.location_name["214"]="The Great Sea";_.location_name["47"]="The Hinterlands";_.location_name["3849"]="The Mechanar";_.location_name["4265"]="The Nexus";_.location_name["3455"]="The North Sea";_.location_name["4493"]="The Obsidian Sanctum";_.location_name["4228"]="The Oculus";_.location_name["3714"]="The Shattered Halls";_.location_name["3717"]="The Slave Pens";_.location_name["3715"]="The Steamvault";_.location_name["717"]="The Stockade";_.location_name["67"]="The Storm Peaks";_.location_name["1477"]="The Temple of Atal'Hakkar";_.location_name["3716"]="The Underbog";_.location_name["457"]="The Veiled Sea";_.location_name["4415"]="The Violet Hold";_.location_name["293"]="Thoradin's Wall";_.location_name["400"]="Thousand Needles";_.location_name["1638"]="Thunder Bluff";_.location_name["1769"]="Timbermaw Hold";_.location_name["85"]="Tirisfal Glades";_.location_name["3540"]="Twisting Nether";_.location_name["1337"]="Uldaman";_.location_name["1497"]="Undercity";_.location_name["490"]="Un'Goro Crater";_.location_name["206"]="Utgarde Keep";_.location_name["1196"]="Utgarde Pinnacle";_.location_name["363"]="Valley of Trials";_.location_name["4603"]="Vault of Archavon";_.location_name["718"]="Wailing Caverns";_.location_name["3277"]="Warsong Gulch";_.location_name["28"]="Western Plaguelands";_.location_name["40"]="Westfall";_.location_name["11"]="Wetlands";_.location_name["4197"]="Wintergrasp";_.location_name["618"]="Winterspring";_.location_name["3521"]="Zangarmarsh";_.location_name["3805"]="Zul'Aman";_.location_name["66"]="Zul'Drak";_.location_name["1176"]="Zul'Farrak";_.location_name["1977"]="Zul'Gurub";_.location_has_modes=[];_.location_has_modes["3790"]="3790";_.location_has_modes["3562"]="3562";_.location_has_modes["4095"]="4095";_.location_has_modes["3792"]="3792";_.location_has_modes["2367"]="2367";_.location_has_modes["3791"]="3791";_.location_has_modes["3789"]="3789";_.location_has_modes["3848"]="3848";_.location_has_modes["2366"]="2366";_.location_has_modes["3713"]="3713";_.location_has_modes["3847"]="3847";_.location_has_modes["3849"]="3849";_.location_has_modes["3714"]="3714";_.location_has_modes["3717"]="3717";_.location_has_modes["3715"]="3715";_.location_has_modes["3716"]="3716";_.skill_line_id=[];_.skill_line_id["355"]="Affliction";_.skill_line_id["171"]="Alchemy";_.skill_line_id["237"]="Arcane";_.skill_line_id["26"]="Arms";_.skill_line_id["253"]="Assassination";_.skill_line_id["44"]="Axes";_.skill_line_id["574"]="Balance";_.skill_line_id["653"]="Bat";_.skill_line_id["210"]="Bear";_.skill_line_id["50"]="Beast Mastery";_.skill_line_id["261"]="Beast Training";_.skill_line_id["655"]="Bird of Prey";_.skill_line_id["164"]="Blacksmithing";_.skill_line_id["770"]="Blood";_.skill_line_id["756"]="Blood Elf Racial";_.skill_line_id["211"]="Boar";_.skill_line_id["45"]="Bows";_.skill_line_id["213"]="Carrion Bird";_.skill_line_id["209"]="Cat";_.skill_line_id["415"]="Cloth";_.skill_line_id["38"]="Combat";_.skill_line_id["185"]="Cooking";_.skill_line_id["214"]="Crab";_.skill_line_id["212"]="Crocolisk";_.skill_line_id["226"]="Crossbows";_.skill_line_id["173"]="Daggers";_.skill_line_id["95"]="Defense";_.skill_line_id["354"]="Demonology";_.skill_line_id["593"]="Destruction";_.skill_line_id["613"]="Discipline";_.skill_line_id["207"]="Doomguard";_.skill_line_id["760"]="Draenei Racial";_.skill_line_id["763"]="Dragonhawk";_.skill_line_id["118"]="Dual Wield";_.skill_line_id["101"]="Dwarven Racial";_.skill_line_id["375"]="Elemental Combat";_.skill_line_id["333"]="Enchanting";_.skill_line_id["202"]="Engineering";_.skill_line_id["373"]="Enhancement";_.skill_line_id["761"]="Felguard";
	_.skill_line_id["189"]="Felhunter";
	_.skill_line_id["134"]="Feral Combat";
	_.skill_line_id["8"]="Fire";_.skill_line_id["129"]="First Aid";_.skill_line_id["356"]="Fishing";_.skill_line_id["473"]="Fist Weapons";_.skill_line_id["771"]="Frost";_.skill_line_id["6"]="Frost";_.skill_line_id["256"]="Fury";_.skill_line_id["183"]="GENERIC (DND)";_.skill_line_id["270"]="Generic Hunter";_.skill_line_id["215"]="Gorilla";_.skill_line_id["46"]="Guns";_.skill_line_id["182"]="Herbalism";_.skill_line_id["56"]="Holy";_.skill_line_id["594"]="Holy";_.skill_line_id["654"]="Hyena";_.skill_line_id["188"]="Imp";_.skill_line_id["206"]="Infernal";_.skill_line_id["773"]="Inscription";_.skill_line_id["755"]="Jewelcrafting";_.skill_line_id["98"]="Language: Common";_.skill_line_id["113"]="Language: Darnassian";_.skill_line_id["139"]="Language: Demon Tongue";_.skill_line_id["138"]="Language: Draconic";_.skill_line_id["759"]="Language: Draenei";_.skill_line_id["111"]="Language: Dwarven";_.skill_line_id["313"]="Language: Gnomish";_.skill_line_id["673"]="Language: Gutterspeak";_.skill_line_id["141"]="Language: Old Tongue";_.skill_line_id["109"]="Language: Orcish";_.skill_line_id["115"]="Language: Taurahe";_.skill_line_id["137"]="Language: Thalassian";_.skill_line_id["140"]="Language: Titan";_.skill_line_id["315"]="Language: Troll";_.skill_line_id["414"]="Leather";_.skill_line_id["165"]="Leatherworking";_.skill_line_id["633"]="Lockpicking";_.skill_line_id["54"]="Maces";_.skill_line_id["413"]="Mail";_.skill_line_id["163"]="Marksmanship";_.skill_line_id["186"]="Mining";_.skill_line_id["764"]="Nether Ray";_.skill_line_id["126"]="Night Elf Racial";_.skill_line_id["125"]="Orc Racial";_.skill_line_id["293"]="Plate Mail";_.skill_line_id["229"]="Polearms";_.skill_line_id["257"]="Protection";_.skill_line_id["267"]="Protection";_.skill_line_id["753"]="Racial - Gnome";_.skill_line_id["754"]="Racial - Human";_.skill_line_id["733"]="Racial - Troll";_.skill_line_id["220"]="Racial - Undead";_.skill_line_id["217"]="Raptor";_.skill_line_id["767"]="Ravager";_.skill_line_id["374"]="Restoration";_.skill_line_id["573"]="Restoration";_.skill_line_id["184"]="Retribution";_.skill_line_id["762"]="Riding";_.skill_line_id["776"]="Runeforging";_.skill_line_id["236"]="Scorpid";_.skill_line_id["768"]="Serpent";_.skill_line_id["78"]="Shadow Magic";_.skill_line_id["433"]="Shield";_.skill_line_id["393"]="Skinning";_.skill_line_id["203"]="Spider";_.skill_line_id["765"]="Sporebat";_.skill_line_id["136"]="Staves";_.skill_line_id["39"]="Subtlety";_.skill_line_id["205"]="Succubus";_.skill_line_id["51"]="Survival";_.skill_line_id["43"]="Swords";_.skill_line_id["197"]="Tailoring";_.skill_line_id["218"]="Tallstrider";_.skill_line_id["124"]="Tauren Racial";_.skill_line_id["176"]="Thrown";_.skill_line_id["251"]="Turtle";_.skill_line_id["172"]="Two-Handed Axes";_.skill_line_id["160"]="Two-Handed Maces";_.skill_line_id["55"]="Two-Handed Swords";_.skill_line_id["162"]="Unarmed";_.skill_line_id["772"]="Unholy";_.skill_line_id["204"]="Voidwalker";_.skill_line_id["228"]="Wands";_.skill_line_id["766"]="Warp Stalker";_.skill_line_id["656"]="Wind Serpent";_.skill_line_id["208"]="Wolf";_.filter_required_skill_line_id=[];_.filter_required_skill_line_id["171"]="Alchemy";_.filter_required_skill_line_id["164"]="Blacksmithing";_.filter_required_skill_line_id["185"]="Cooking";_.filter_required_skill_line_id["333"]="Enchanting";_.filter_required_skill_line_id["202"]="Engineering";_.filter_required_skill_line_id["129"]="First Aid";_.filter_required_skill_line_id["356"]="Fishing";_.filter_required_skill_line_id["182"]="Herbalism";_.filter_required_skill_line_id["148"]="Horse Riding";_.filter_required_skill_line_id["773"]="Inscription";_.filter_required_skill_line_id["755"]="Jewelcrafting";_.filter_required_skill_line_id["165"]="Leatherworking";_.filter_required_skill_line_id["186"]="Mining";_.filter_required_skill_line_id["533"]="Raptor Riding";_.filter_required_skill_line_id["762"]="Riding";_.filter_required_skill_line_id["197"]="Tailoring";_.filter_required_skill_line_id["150"]="Tiger Riding";_.filter_required_skill_line_id["554"]="Undead Horsemanship";
	_.filter_crafted_by=[];_.filter_crafted_by["171"]="Alchemy";_.filter_crafted_by["164"]="Blacksmithing";_.filter_crafted_by["185"]="Cooking";_.filter_crafted_by["333"]="Enchanting";_.filter_crafted_by["202"]="Engineering";_.filter_crafted_by["129"]="First Aid";_.filter_crafted_by["773"]="Inscription";_.filter_crafted_by["755"]="Jewelcrafting";_.filter_crafted_by["165"]="Leatherworking";_.filter_crafted_by["186"]="Mining";_.filter_crafted_by["197"]="Tailoring";_.filter_reagent_for=[];_.filter_reagent_for["171"]="Alchemy";_.filter_reagent_for["164"]="Blacksmithing";_.filter_reagent_for["185"]="Cooking";_.filter_reagent_for["333"]="Enchanting";_.filter_reagent_for["202"]="Engineering";_.filter_reagent_for["129"]="First Aid";_.filter_reagent_for["773"]="Inscription";_.filter_reagent_for["755"]="Jewelcrafting";_.filter_reagent_for["165"]="Leatherworking";_.filter_reagent_for["186"]="Mining";_.filter_reagent_for["197"]="Tailoring";_.talent_category_id=[];_.talent_category_id["6"]="Frost";_.talent_category_id["8"]="Fire";_.talent_category_id["26"]="Arms";_.talent_category_id["38"]="Combat";_.talent_category_id["39"]="Subtlety";_.talent_category_id["50"]="Beast Mastery";_.talent_category_id["51"]="Survival";_.talent_category_id["56"]="Holy";_.talent_category_id["78"]="Shadow";_.talent_category_id["134"]="Feral Combat";_.talent_category_id["163"]="Marksmanship";_.talent_category_id["184"]="Retribution";_.talent_category_id["237"]="Arcane";_.talent_category_id["253"]="Assassination";_.talent_category_id["256"]="Fury";_.talent_category_id["257"]="Protection";_.talent_category_id["267"]="Protection";_.talent_category_id["354"]="Demonology";_.talent_category_id["355"]="Affliction";_.talent_category_id["373"]="Enhancement";_.talent_category_id["374"]="Restoration";_.talent_category_id["375"]="Elemental";_.talent_category_id["409"]="Tenacity";_.talent_category_id["410"]="Ferocity";_.talent_category_id["411"]="Cunning";_.talent_category_id["573"]="Restoration";_.talent_category_id["574"]="Balance";_.talent_category_id["593"]="Destruction";_.talent_category_id["594"]="Holy";_.talent_category_id["613"]="Discipline";_.talent_category_id["770"]="Blood";_.talent_category_id["771"]="Frost";_.talent_category_id["772"]="Unholy";_.item_type=[];_.item_type["-1.-1"]="";_.item_type["0.0"]="Consumable";_.item_type["0.1"]="Potion";_.item_type["0.2"]="Elixir";_.item_type["0.3"]="Flask";_.item_type["0.4"]="Scroll";_.item_type["0.5"]="Food & Drink";_.item_type["0.6"]="Item Enhancement";_.item_type["0.7"]="Bandage";_.item_type["0.8"]="Other";_.item_type["1.0"]="Bag";_.item_type["1.1"]="Soul Bag";_.item_type["1.2"]="Herb Bag";_.item_type["1.3"]="Enchanting Bag";_.item_type["1.4"]="Engineering Bag";_.item_type["1.5"]="Gem Bag";_.item_type["1.6"]="Mining Bag";_.item_type["1.7"]="Leatherworking Bag";_.item_type["1.8"]="Inscription Bag";_.item_type["2.0"]="One-Handed Axes";_.item_type["2.1"]="Two-Handed Axes";_.item_type["2.2"]="Bows";_.item_type["2.3"]="Guns";_.item_type["2.4"]="One-Handed Maces";_.item_type["2.5"]="Two-Handed Maces";_.item_type["2.6"]="Polearms";_.item_type["2.7"]="One-Handed Swords";_.item_type["2.8"]="Two-Handed Swords";_.item_type["2.9"]="";_.item_type["2.10"]="Staves";_.item_type["2.11"]="";_.item_type["2.12"]="";_.item_type["2.13"]="Fist Weapons";_.item_type["2.14"]="Miscellaneous";_.item_type["2.15"]="Daggers";_.item_type["2.16"]="Thrown";_.item_type["2.17"]="";_.item_type["2.18"]="Crossbows";_.item_type["2.19"]="Wands";_.item_type["2.20"]="Fishing Poles";_.item_type["3.0"]="Red";_.item_type["3.1"]="Blue";_.item_type["3.2"]="Yellow";_.item_type["3.3"]="Purple";_.item_type["3.4"]="Green";_.item_type["3.5"]="Orange";_.item_type["3.6"]="Meta";_.item_type["3.7"]="Simple";_.item_type["3.8"]="Prismatic";_.item_type["4.-5"]="Off-hand Frills";_.item_type["4.-4"]="Trinkets";_.item_type["4.-3"]="Amulets";_.item_type["4.-2"]="Rings";_.item_type["4.-1"]="Cloaks";_.item_type["4.0"]="Miscellaneous";_.item_type["4.1"]="Cloth";_.item_type["4.2"]="Leather";_.item_type["4.3"]="Mail";_.item_type["4.4"]="Plate";_.item_type["4.5"]="";
	_.item_type["4.6"]="Shields";_.item_type["4.7"]="Librams";
	_.item_type["4.8"]="Idols";_.item_type["4.9"]="Totems";_.item_type["4.10"]="Sigils";_.item_type["5.0"]="Reagent";_.item_type["6.0"]="";_.item_type["6.1"]="";_.item_type["6.2"]="Arrow";_.item_type["6.3"]="Bullet";_.item_type["6.4"]="";_.item_type["7.0"]="Trade Goods";_.item_type["7.1"]="Parts";_.item_type["7.2"]="Explosives";_.item_type["7.3"]="Devices";_.item_type["7.4"]="Jewelcrafting";_.item_type["7.5"]="Cloth";_.item_type["7.6"]="Leather";_.item_type["7.7"]="Metal & Stone";_.item_type["7.8"]="Meat";_.item_type["7.9"]="Herb";_.item_type["7.10"]="Elemental";_.item_type["7.11"]="Other";_.item_type["7.12"]="Enchanting";_.item_type["7.13"]="Materials";_.item_type["7.14"]="Armor Enchantment";_.item_type["7.15"]="Weapon Enchantment";_.item_type["8.0"]="Generic(OBSOLETE)";_.item_type["9.0"]="Book";_.item_type["9.1"]="Leatherworking";_.item_type["9.2"]="Tailoring";_.item_type["9.3"]="Engineering";_.item_type["9.4"]="Blacksmithing";_.item_type["9.5"]="Cooking";_.item_type["9.6"]="Alchemy";_.item_type["9.7"]="First Aid";_.item_type["9.8"]="Enchanting";_.item_type["9.9"]="Fishing";_.item_type["9.10"]="Jewelcrafting";_.item_type["10.0"]="Money(OBSOLETE)";_.item_type["11.0"]="";_.item_type["11.1"]="";_.item_type["11.2"]="Quiver";_.item_type["11.3"]="Ammo Pouch";_.item_type["12.0"]="Quest";_.item_type["13.0"]="Key";_.item_type["13.1"]="";_.item_type["14.0"]="Permanent";_.item_type["15.-1"]="Mounts";_.item_type["15.0"]="Junk";_.item_type["15.1"]="Reagent";_.item_type["15.2"]="Pet";_.item_type["15.3"]="Holiday";_.item_type["15.4"]="Other";_.item_type["15.5"]="Mount";_.item_type["16.1"]="Warrior";_.item_type["16.2"]="Paladin";_.item_type["16.3"]="Hunter";_.item_type["16.4"]="Rogue";_.item_type["16.5"]="Priest";_.item_type["16.6"]="Death Knight";_.item_type["16.7"]="Shaman";_.item_type["16.8"]="Mage";_.item_type["16.9"]="Warlock";_.item_type["16.11"]="Druid";_.itemset_type=[];_.itemset_type["2.0"]="One-Handed Axes";_.itemset_type["2.13"]="Fist Weapons";_.itemset_type["2.15"]="Daggers";_.itemset_type["2.4"]="One-Handed Maces";_.itemset_type["2.7"]="One-Handed Swords";_.itemset_type["4.-2"]="Rings";_.itemset_type["4.-3"]="Amulets";_.itemset_type["4.-4"]="Trinkets";_.itemset_type["4.0"]="Miscellaneous";_.itemset_type["4.1"]="Cloth";_.itemset_type["4.2"]="Leather";_.itemset_type["4.3"]="Mail";_.itemset_type["4.4"]="Plate";_.faction_id=[];_.faction_id["469"]="Alliance";_.faction_id["891"]="Alliance Forces";_.faction_id["1037"]="Alliance Vanguard";_.faction_id["1106"]="Argent Crusade";_.faction_id["529"]="Argent Dawn";_.faction_id["1012"]="Ashtongue Deathsworn";_.faction_id["87"]="Bloodsail Buccaneers";_.faction_id["21"]="Booty Bay";_.faction_id["910"]="Brood of Nozdormu";_.faction_id["609"]="Cenarion Circle";_.faction_id["942"]="Cenarion Expedition";_.faction_id["1118"]="Classic";_.faction_id["909"]="Darkmoon Faire";_.faction_id["530"]="Darkspear Trolls";_.faction_id["69"]="Darnassus";_.faction_id["577"]="Everlook";_.faction_id["930"]="Exodar";_.faction_id["1068"]="Explorers' League";_.faction_id["1104"]="Frenzyheart Tribe";_.faction_id["729"]="Frostwolf Clan";_.faction_id["369"]="Gadgetzan";_.faction_id["92"]="Gelkis Clan Centaur";_.faction_id["54"]="Gnomeregan Exiles";_.faction_id["946"]="Honor Hold";_.faction_id["67"]="Horde";_.faction_id["1052"]="Horde Expedition";_.faction_id["892"]="Horde Forces";_.faction_id["749"]="Hydraxian Waterlords";_.faction_id["47"]="Ironforge";_.faction_id["989"]="Keepers of Time";_.faction_id["1090"]="Kirin Tor";_.faction_id["1098"]="Knights of the Ebon Blade";_.faction_id["978"]="Kurenai";_.faction_id["1011"]="Lower City";_.faction_id["93"]="Magram Clan Centaur";_.faction_id["1015"]="Netherwing";_.faction_id["1038"]="Ogri'la";_.faction_id["76"]="Orgrimmar";_.faction_id["470"]="Ratchet";_.faction_id["349"]="Ravenholdt";_.faction_id["1031"]="Sha'tari Skyguard";_.faction_id["1077"]="Shattered Sun Offensive";_.faction_id["936"]="Shattrath City";_.faction_id["809"]="Shen'dralar";_.faction_id["1117"]="Sholazar Basin";_.faction_id["911"]="Silvermoon City";_.faction_id["890"]="Silverwing Sentinels";
	_.faction_id["970"]="Sporeggar";
	_.faction_id["169"]="Steamwheedle Cartel";
	_.faction_id["730"]="Stormpike Guard";
	_.faction_id["72"]="Stormwind";
	_.faction_id["70"]="Syndicate";
	_.faction_id["949"]="Test Faction 1";
	_.faction_id["948"]="Test Faction 2";
	_.faction_id["932"]="The Aldor";
	_.faction_id["980"]="The Burning Crusade";
	_.faction_id["933"]="The Consortium";
	_.faction_id["510"]="The Defilers";
	_.faction_id["1126"]="The Frostborn";
	_.faction_id["1067"]="The Hand of Vengeance";
	_.faction_id["1073"]="The Kalu'ak";
	_.faction_id["941"]="The Mag'har";
	_.faction_id["1105"]="The Oracles";
	_.faction_id["990"]="The Scale of the Sands";
	_.faction_id["934"]="The Scryers";
	_.faction_id["935"]="The Sha'tar";
	_.faction_id["1094"]="The Silver Covenant";
	_.faction_id["1119"]="The Sons of Hodir";
	_.faction_id["1064"]="The Taunka";
	_.faction_id["967"]="The Violet Eye";
	_.faction_id["1091"]="The Wyrmrest Accord";
	_.faction_id["59"]="Thorium Brotherhood";
	_.faction_id["947"]="Thrallmar";
	_.faction_id["81"]="Thunder Bluff";
	_.faction_id["576"]="Timbermaw Hold";
	_.faction_id["922"]="Tranquillien";
	_.faction_id["68"]="Undercity";
	_.faction_id["1050"]="Valiance Expedition";
	_.faction_id["1085"]="Warsong Offensive";
	_.faction_id["889"]="Warsong Outriders";
	_.faction_id["471"]="Wildhammer Clan";
	_.faction_id["589"]="Wintersaber Trainers";
	_.faction_id["1097"]="Wrath of the Lich King";
	_.faction_id["270"]="Zandalar Tribe";
	_.itemset_tag_new=[];
	_.itemset_tag_new["Arathi Basin Set"]="Arathi Basin Set";
	_.itemset_tag_new["Arena Season 1 Set"]="Arena Season 1 Set";
	_.itemset_tag_new["Arena Season 2 Set"]="Arena Season 2 Set";
	_.itemset_tag_new["Arena Season 3 Set"]="Arena Season 3 Set";
	_.itemset_tag_new["Dungeon Set 1"]="Dungeon Set 1";
	_.itemset_tag_new["Dungeon Set 2"]="Dungeon Set 2";
	_.itemset_tag_new["Dungeon Set 3"]="Dungeon Set 3";
	_.itemset_tag_new["Level 60 PvP Epic Set"]="Level 60 PvP Epic Set";
	_.itemset_tag_new["Level 60 PvP Rare Set"]="Level 60 PvP Rare Set";
	_.itemset_tag_new["Level 60 PvP Rare Set (Old)"]="Level 60 PvP Rare Set (Old)";
	_.itemset_tag_new["Level 70 PvP Rare Set"]="Level 70 PvP Rare Set";
	_.itemset_tag_new["Reputation PvP Set"]="Reputation PvP Set";
	_.itemset_tag_new["Ruins of Ahn'Qiraj Set"]="Ruins of Ahn'Qiraj Set";
	_.itemset_tag_new["Temple of Ahn'Qiraj Set"]="Temple of Ahn'Qiraj Set";
	_.itemset_tag_new["Tier 1 Raid Set"]="Tier 1 Raid Set";
	_.itemset_tag_new["Tier 2 Raid Set"]="Tier 2 Raid Set";
	_.itemset_tag_new["Tier 3 Raid Set"]="Tier 3 Raid Set";
	_.itemset_tag_new["Tier 4 Raid Set"]="Tier 4 Raid Set";
	_.itemset_tag_new["Tier 5 Raid Set"]="Tier 5 Raid Set";
	_.itemset_tag_new["Tier 6 Raid Set"]="Tier 6 Raid Set";
	_.itemset_tag_new["Zul'Gurub Set"]="Zul'Gurub Set";
};

Curse.Lookup.initialize();
var mn_1_$7=[
	[6,"Death Knight","search.aspx?browse=7.6"],
	[11,"Druid","search.aspx?browse=7.11"],
	[3,"Hunter","search.aspx?browse=7.3"],
	[8,"Mage","search.aspx?browse=7.8"],
	[2,"Paladin","search.aspx?browse=7.2"],
	[5,"Priest","search.aspx?browse=7.5"],
	[4,"Rogue","search.aspx?browse=7.4"],
	[7,"Shaman","search.aspx?browse=7.7"],
	[9,"Warlock","search.aspx?browse=7.9"],
	[1,"Warrior","search.aspx?browse=7.1"],
	[0,"","search.aspx?browse=7.0"]
];
	
var mn_1_2=[
	[0,"One-Handed Axes",],
	[1,"Two-Handed Axes",],
	[2,"Bows",],[3,"Guns",],
	[4,"One-Handed Maces",],
	[5,"Two-Handed Maces",],
	[6,"Polearms",],
	[7,"One-Handed Swords",],
	[8,"Two-Handed Swords",],
	[10,"Staves",],
	[13,"Fist Weapons",],
	[14,"Miscellaneous",],
	[15,"Daggers",],
	[16,"Thrown",],
	[18,"Crossbows",],
	[19,"Wands",],
	[20,"Fishing Poles",]
];

var mn_1_4_1=[
	[5,"Chest",],
	[8,"Feet",],
	[10,"Hands",],
	[1,"Head",],
	[7,"Legs",],
	[3,"Shoulder",],
	[6,"Waist",],
	[9,"Wrist",]
];

var mn_1_4_2=[
	[5,"Chest",],
	[8,"Feet",],
	[10,"Hands",],
	[1,"Head",],
	[7,"Legs",],
	[3,"Shoulder",],
	[6,"Waist",],
	[9,"Wrist",]
];

var mn_1_4_3=[
	[5,"Chest",],
	[8,"Feet",],
	[10,"Hands",],
	[1,"Head",],
	[7,"Legs",],
	[3,"Shoulder",],
	[6,"Waist",],
	[9,"Wrist",]
];

var mn_1_4_4=[
	[5,"Chest",],
	[8,"Feet",],
	[10,"Hands",],
	[1,"Head",],
	[7,"Legs",],
	[3,"Shoulder",],
	[6,"Waist",],
	[9,"Wrist",]
];

var mn_1_4=[
	[10,"Sigils",],
	[1,"Cloth",,mn_1_4_1],
	[2,"Leather",,mn_1_4_2],
	[3,"Mail",,mn_1_4_3],
	[4,"Plate",,mn_1_4_4],
	[-3,"Amulets",],
	[-1,"Cloaks",],
	[-5,"Off-hand Frills",],
	[-2,"Rings",],
	[-4,"Trinkets",],
	[8,"Idols",],
	[7,"Librams",],
	[0,"Miscellaneous",],
	[6,"Shields",],
	[9,"Totems",]
];

var mn_1_0=[
	[7,"Bandage",],
	[2,"Elixir",],
	[3,"Flask",],
	[5,"Food & Drink",],
	[6,"Item Enhancement",],
	[8,"Other",],
	[1,"Potion",],
	[4,"Scroll",],
	[0,"Consumable",]
];

var mn_1_1=[
	[8,"Inscription Bag",],
	[7,"Leatherworking Bag",],
	[0,"Bag",],[1,"Soul Bag",],
	[2,"Herb Bag",],
	[3,"Enchanting Bag",],
	[4,"Engineering Bag",],
	[5,"Gem Bag",],
	[6,"Mining Bag",]
];

var mn_1_7=[
	[14,"Armor Enchantment",],
	[5,"Cloth",],
	[10,"Elemental",],
	[12,"Enchanting",],
	[9,"Herb",],
	[6,"Leather",],
	[13,"Materials",],
	[8,"Meat",],
	[7,"Metal & Stone",],
	[11,"Other",],
	[15,"Weapon Enchantment",],
	[0,"Trade Goods",],
	[1,"Parts",],
	[2,"Explosives",],
	[3,"Devices",],
	[4,"Jewelcrafting",]
];

var mn_1_16=[
	[6,"Death Knight",],
	[11,"Druid",],
	[3,"Hunter",],
	[8,"Mage",],
	[2,"Paladin",],
	[5,"Priest",],
	[4,"Rogue",],
	[7,"Shaman",],
	[9,"Warlock",],
	[1,"Warrior",]
];

var mn_1_6=[
	[2,"Arrow",],
	[3,"Bullet",]
];

var mn_1_11=[
	[2,"Quiver",],
	[3,"Ammo Pouch",]
];

var mn_1_9=[
	[0,"Book",],
	[6,"Alchemy",],
	[4,"Blacksmithing",],
	[5,"Cooking",],
	[8,"Enchanting",],
	[3,"Engineering",],
	[7,"First Aid",],
	[9,"Fishing",],
	[10,"Jewelcrafting",],
	[1,"Leatherworking",],
	[2,"Tailoring",]
];

var mn_1_3=[
	[0,"Red",],
	[1,"Blue",],
	[2,"Yellow",],
	[3,"Purple",],
	[4,"Green",],
	[5,"Orange",],
	[6,"Meta",],
	[7,"Simple",],
	[8,"Prismatic",]
];

var mn_1_15=[
	[-1,"Mounts",],
	[3,"Holiday",],
	[0,"Junk",],
	[5,"Mount",],
	[4,"Other",],
	[2,"Pet",],
	[1,"Reagent",]
];

var mn_1=[
	[-7,"Item Sets","search.aspx?sf=1&browse=7",mn_1_$7],
	[2,"Weapons",,mn_1_2],
	[4,"Armor",,mn_1_4],
	[0,"Consumables",,mn_1_0],
	[1,"Containers",,mn_1_1],
	[7,"Trade Goods",,mn_1_7],
	[16,"Glyph",,mn_1_16],
	[6,"Projectiles",,mn_1_6],
	[11,"Quivers",,mn_1_11],
	[9,"Recipes",,mn_1_9],
	[5,"Reagents",],
	[3,"Gems",,mn_1_3],
	[15,"Miscellaneous",,mn_1_15],
	[12,"Quest",],[13,"Keys",]
];

var mn_2=[
	[1,"Beast",],
	[8,"Critter",],
	[3,"Demon",],
	[2,"Dragonkin",],
	[4,"Elemental",],
	[13,"Gas Cloud",],
	[5,"Giant",],
	[7,"Humanoid",],
	[9,"Mechanical",],
	[12,"Non-combat Pet",],
	[10,"Not specified",],
	[11,"Totem",],
	[6,"Undead",]
];

var mn_3=[
	[-1,"Wrath of the Lich King",],
	[0,"Eastern Kingdoms",],
	[1,"Kalimdor",],
	[2,"Outland",],
	[3,"Dungeons",],
	[4,"Raids",],
	[5,"Battlegrounds",],
	[6,"Arenas",],
	[7,"Northrend",]
];

var mn_4_7=[
	[3537,"Borean Tundra",],
	[4395,"Dalaran",],
	[65,"Dragonblight",],
	[394,"Grizzly Hills",],
	[495,"Howling Fjord",],
	[210,"Icecrown",],
	[3711,"Sholazar Basin",],
	[67,"The Storm Peaks",],
	[4197,"Wintergrasp",],
	[66,"Zul'Drak",]
];

var mn_4_0=[
	[36,"Alterac Mountains",],
	[45,"Arathi Highlands",],
	[3,"Badlands",],
	[25,"Blackrock Mountain",],
	[4,"Blasted Lands",],
	[35,"Booty Bay",],
	[46,"Burning Steppes",],
	[3459,"City",],
	[132,"Coldridge Valley",],
	[41,"Deadwind Pass",],
	[154,"Deathknell",],
	[2257,"Deeprun Tram",],
	[1,"Dun Morogh",],
	[10,"Duskwood",],
	[139,"Eastern Plaguelands",],
	[12,"Elwynn Forest",],
	[3430,"Eversong Woods",],
	[3433,"Ghostlands",],
	[267,"Hillsbrad Foothills",],
	[1537,"Ironforge",],
	[4080,"Isle of Quel'Danas",],
	[38,"Loch Modan",],
	[24,"Northshire Abbey",],
	[9,"Northshire Valley",],
	[44,"Redridge Mountains",],
	[51,"Searing Gorge",],
	[3487,"Silvermoon City",],
	[130,"Silverpine Forest",],
	[1519,"Stormwind City",],
	[33,"Stranglethorn Vale",],
	[3431,"Sunstrider Isle",],
	[8,"Swamp of Sorrows",],
	[47,"The Hinterlands",],
	[85,"Tirisfal Glades",],
	[1497,"Undercity",],
	[28,"Western Plaguelands",],
	[40,"Westfall",],
	[11,"Wetlands",]
];

var mn_4_1=[
	[2079,"Alcaz Island",],
	[3526,"Ammen Vale",],
	[331,"Ashenvale",],
	[16,"Azshara",],
	[3524,"Azuremyst Isle",],
	[3525,"Bloodmyst Isle",],
	[221,"Camp Narache",],
	[279,"Dalaran Crater",],
	[148,"Darkshore",],
	[1657,"Darnassus",],
	[405,"Desolace",],
	[14,"Durotar",],
	[15,"Dustwallow Marsh",],
	[1116,"Feathermoon Stronghold",],
	[361,"Felwood",],[357,"Feralas",],
	[876,"GM Island",],
	[2917,"Hall of Legends",],
	[493,"Moonglade",],
	[215,"Mulgore",],
	[1637,"Orgrimmar",],
	[220,"Red Cloud Mesa",],
	[702,"Rut'theran Village",],
	[188,"Shadowglen",],
	[1377,"Silithus",],
	[406,"Stonetalon Mountains",],
	[440,"Tanaris",],
	[141,"Teldrassil",],
	[17,"The Barrens",],
	[3557,"The Exodar",],
	[400,"Thousand Needles",],
	[1638,"Thunder Bluff",],
	[1769,"Timbermaw Hold",],
	[490,"Un'Goro Crater",],
	[363,"Valley of Trials",],
	[618,"Winterspring",]
];

var mn_4_2=[
	[3522,"Blade's Edge Mountains",],
	[3565,"Cenarion Refuge",],
	[3905,"Coilfang Reservoir",],
	[3483,"Hellfire Peninsula",],
	[3518,"Nagrand",],
	[3523,"Netherstorm",],
	[3520,"Shadowmoon Valley",],
	[3703,"Shattrath City",],
	[3679,"Skettis",],
	[3519,"Terokkar Forest",],
	[3696,"The Barrier Hills",],
	[3840,"The Black Temple",],
	[3521,"Zangarmarsh",]
];

var mn_4_3=[
	[4494,"Ahn'kahet: The Old Kingdom",],
	[3790,"Auchenai Crypts",],
	[3917,"Auchindoun",],
	[719,"Blackfathom Deeps",],
	[1584,"Blackrock Depths",],
	[1583,"Blackrock Spire",],
	[1941,"Caverns of Time",],
	[2557,"Dire Maul",],
	[4196,"Drak'Tharon Keep",],
	[721,"Gnomeregan",],
	[4416,"Gundrak",],
	[4272,"Halls of Lightning",],
	[4264,"Halls of Stone",],
	[3535,"Hellfire Citadel",],
	[4095,"Magisters' Terrace",],
	[3792,"Mana-Tombs",],
	[2100,"Maraudon",],
	[2437,"Ragefire Chasm",],
	[722,"Razorfen Downs",],
	[491,"Razorfen Kraul",],
	[796,"Scarlet Monastery",],
	[2057,"Scholomance",],
	[3789,"Shadow Labyrinth",],
	[209,"Shadowfang Keep",],
	[2017,"Stratholme",],
	[1417,"Sunken Temple",],
	[4100,"The Culling of Stratholme",],
	[1581,"The Deadmines",],
	[4265,"The Nexus",],
	[4228,"The Oculus",],
	[3717,"The Slave Pens",],
	[3715,"The Steamvault",],
	[717,"The Stockade",],
	[3716,"The Underbog",],
	[4415,"The Violet Hold",],
	[1337,"Uldaman",],
	[206,"Utgarde Keep",],
	[1196,"Utgarde Pinnacle",],
	[718,"Wailing Caverns",],
	[1176,"Zul'Farrak",]
];

var mn_4_4=[
	[3428,"Ahn'Qiraj",],
	[3477,"Azjol-Nerub",],
	[2677,"Blackwing Lair",],
	[3606,"Hyjal Summit",],
	[3457,"Karazhan",],
	[3836,"Magtheridon's Lair",],
	[2717,"Molten Core",],
	[3456,"Naxxramas",],
	[2159,"Onyxia's Lair",],
	[3429,"Ruins of Ahn'Qiraj",],
	[3607,"Serpentshrine Cavern",],
	[3842,"Tempest Keep",],
	[4500,"The Eye of Eternity",],
	[3805,"Zul'Aman",],
	[1977,"Zul'Gurub",]
];

var mn_4_5=[
	[2597,"Alterac Valley",],
	[3358,"Arathi Basin",],
	[3820,"Eye of the Storm",],
	[4384,"Strand of the Ancients",],
	[3277,"Warsong Gulch",]
];

var mn_4_$1=[
	[6,"Death Knight",],
	[11,"Druid",],
	[3,"Hunter",],
	[8,"Mage",],
	[2,"Paladin",],
	[5,"Priest",],
	[4,"Rogue",],
	[7,"Shaman",],
	[9,"Warlock",],
	[1,"Warrior",],
	[0,"",]
];

var mn_4_$2=[
	[171,"Alchemy",],
	[164,"Blacksmithing",],
	[185,"Cooking",],
	[202,"Engineering",],
	[129,"First Aid",],
	[356,"Fishing",],
	[182,"Herbalism",],
	[773,"Inscription",],
	[755,"Jewelcrafting",],
	[165,"Leatherworking",],
	[197,"Tailoring",]
];

var mn_4_$3=[
	[365,"Ahn'Qiraj War",],
	[25,"Battlegrounds",],
	[370,"Brewfest",],
	[364,"Darkmoon Faire",],
	[372,"Death Knight",],
	[1,"Epic",],
	[371,"Inscription",],
	[368,"Invasion",],
	[344,"Legendary",],
	[366,"Lunar Festival",],
	[369,"Midsummer",],
	[367,"Reputation",],
	[22,"Seasonal",],
	[284,"Special",],
	[221,"Treasure Map",]
];

var mn_4=[
	[-4,"Wrath of the Lich King",],
	[7,"Northrend",,mn_4_7],
	[0,"Eastern Kingdoms",,mn_4_0],
	[1,"Kalimdor",,mn_4_1],
	[2,"Outland",,mn_4_2],
	[3,"Dungeons",,mn_4_3],
	[4,"Raids",,mn_4_4],
	[5,"Battlegrounds",,mn_4_5],
	[-1,"Classes",,mn_4_$1],
	[-2,"Professions",,mn_4_$2],
	[-3,"Miscellaneous",,mn_4_$3]
];

var mn_5=[
	[9,"Books",],
	[3,"Containers",],
	[25,"Fish Schools",],
	[-2,"Herbs",],
	[-3,"Locked Chests",],
	[-1,"Mineral Veins",],
	[2,"Quest",]
];

var mn_6_7_6=[
	[770,"Blood",],
	[771,"Frost",],
	[776,"Runeforging",],
	[772,"Unholy",]
];

var mn_6_7_11=[
	[574,"Balance",],
	[134,"Feral Combat",],
	[573,"Restoration",]
];

var mn_6_7_3=[
	[50,"Beast Mastery",],
	[261,"Beast Training",],
	[163,"Marksmanship",],
	[51,"Survival",]
];

var mn_6_7_8=[
	[237,"Arcane",],
	[8,"Fire",],
	[6,"Frost",]
];

var mn_6_7_2=[
	[594,"Holy",],
	[267,"Protection",],
	[184,"Retribution",]
];

var mn_6_7_5=[
	[613,"Discipline",],
	[56,"Holy",],
	[78,"Shadow Magic",]
];

var mn_6_7_4=[
	[253,"Assassination",],
	[38,"Combat",],
	[633,"Lockpicking",],
	[39,"Subtlety",]
];
	
var mn_6_7_7=[
	[375,"Elemental Combat",],
	[373,"Enhancement",],
	[374,"Restoration",]
];

var mn_6_7_9=[
	[355,"Affliction",],
	[354,"Demonology",],
	[593,"Destruction",]
];

var mn_6_7_1=[
	[26,"Arms",],
	[256,"Fury",],
	[257,"Protection",]
];

var mn_6_7=[
	[6,"Death Knight",,mn_6_7_6],
	[11,"Druid",,mn_6_7_11],
	[3,"Hunter",,mn_6_7_3],
	[8,"Mage",,mn_6_7_8],
	[2,"Paladin",,mn_6_7_2],
	[5,"Priest",,mn_6_7_5],
	[4,"Rogue",,mn_6_7_4],
	[7,"Shaman",,mn_6_7_7],
	[9,"Warlock",,mn_6_7_9],
	[1,"Warrior",,mn_6_7_1]
];

var mn_6_$2_6=[
	[770,"Blood",],
	[771,"Frost",],
	[772,"Unholy",]
];

var mn_6_$2_11=[
	[574,"Balance",],
	[134,"Feral Combat",],
	[573,"Restoration",]
];

var mn_6_$2_3=[
	[50,"Beast Mastery",],
	[163,"Marksmanship",],
	[51,"Survival",]
];

var mn_6_$2_8=[
	[237,"Arcane",],
	[8,"Fire",],
	[6,"Frost",]
];

var mn_6_$2_2=[
	[594,"Holy",],
	[267,"Protection",],
	[184,"Retribution",]
];

var mn_6_$2_5=[
	[613,"Discipline",],
	[56,"Holy",],
	[78,"Shadow",]
];

var mn_6_$2_4=[
	[253,"Assassination",],
	[38,"Combat",],
	[39,"Subtlety",]
];

var mn_6_$2_7=[
	[375,"Elemental",],
	[373,"Enhancement",],
	[374,"Restoration",]
];

var mn_6_$2_9=[
	[355,"Affliction",],
	[354,"Demonology",],
	[593,"Destruction",]
];

var mn_6_$2_1=[
	[26,"Arms",],
	[256,"Fury",],
	[257,"Protection",]
];

var mn_6_$2=[
	[6,"Death Knight",,mn_6_$2_6],
	[11,"Druid",,mn_6_$2_11],
	[3,"Hunter",,mn_6_$2_3],
	[8,"Mage",,mn_6_$2_8],
	[2,"Paladin",,mn_6_$2_2],
	[5,"Priest",,mn_6_$2_5],
	[4,"Rogue",,mn_6_$2_4],
	[7,"Shaman",,mn_6_$2_7],
	[9,"Warlock",,mn_6_$2_9],
	[1,"Warrior",,mn_6_$2_1]
];

var mn_6_11=[
	[171,"Alchemy",],
	[164,"Blacksmithing",],
	[333,"Enchanting",],
	[202,"Engineering",],
	[182,"Herbalism",],
	[773,"Inscription",],
	[755,"Jewelcrafting",],
	[165,"Leatherworking",],
	[186,"Mining",],
	[393,"Skinning",],
	[197,"Tailoring",]
];

var mn_6_$3=[
	[653,"Bat",],
	[210,"Bear",],
	[655,"Bird of Prey",],
	[211,"Boar",],
	[213,"Carrion Bird",],
	[209,"Cat",],
	[214,"Crab",],
	[212,"Crocolisk",],
	[207,"Doomguard",],
	[763,"Dragonhawk",],
	[761,"Felguard",],
	[189,"Felhunter",],
	[270,"Generic Hunter",],
	[215,"Gorilla",],
	[654,"Hyena",],
	[188,"Imp",],
	[206,"Infernal",],
	[764,"Nether Ray",],
	[217,"Raptor",],
	[767,"Ravager",],
	[236,"Scorpid",],
	[768,"Serpent",],
	[203,"Spider",],
	[765,"Sporebat",],
	[205,"Succubus",],
	[218,"Tallstrider",],
	[251,"Turtle",],
	[204,"Voidwalker",],
	[766,"Warp Stalker",],
	[656,"Wind Serpent",],
	[208,"Wolf",]
];

var mn_6_9=[
	[185,"Cooking",],
	[129,"First Aid",],
	[356,"Fishing",],
	[762,"Riding",]
];

var mn_6_$4=[
	[756,"Blood Elf Racial",],
	[760,"Draenei Racial",],
	[101,"Dwarven Racial",],
	[126,"Night Elf Racial",],
	[125,"Orc Racial",],
	[753,"Racial - Gnome",],
	[754,"Racial - Human",],
	[733,"Racial - Troll",],
	[220,"Racial - Undead",],
	[124,"Tauren Racial",]
];

var mn_6=[
	[7,"Class Skills",,mn_6_7],
	[-2,"Talents",,mn_6_$2],
	[11,"Professions",,mn_6_11],
	[-3,"Pet Skills",,mn_6_$3],
	[9,"Secondary Skills",,mn_6_9],
	[-4,"Racial Abilities",,mn_6_$4],
	[6,"Weapon Skills",],
	[8,"Armor Proficiencies",],
	[10,"Languages",]
];

var mn_8=[
	[-1,"Other",],[469,"Alliance",],
	[891,"Alliance Forces",],
	[1037,"Alliance Vanguard",],
	[1118,"Classic",],
	[67,"Horde",],
	[1052,"Horde Expedition",],
	[892,"Horde Forces",],
	[936,"Shattrath City",],
	[1117,"Sholazar Basin",],
	[169,"Steamwheedle Cartel",],
	[980,"The Burning Crusade",],
	[1097,"Wrath of the Lich King",]
];

var mn_23_168=[
	[14805,"The Burning Crusade",],
	[14806,"Lich King Dungeon",],
	[14808,"Classic",],
	[14921,"Lich King Heroic",],
	[14922,"Lich King Raid",],
	[14923,"Lich King Heroic Raid",]
];

var mn_23_97=[
	[14777,"Eastern Kingdoms",],
	[14778,"Kalimdor",],
	[14779,"Outland",],
	[14780,"Northrend",]
];

var mn_23_95=[
	[165,"Arena",],
	[14801,"Alterac Valley",],
	[14802,"Arathi Basin",],
	[14803,"Eye of the Storm",],
	[14804,"Warsong Gulch",],
	[14881,"Strand of the Ancients",],
	[14901,"Wintergrasp",]
];

var mn_23_96=[
	[14861,"Classic",],
	[14862,"The Burning Crusade",],
	[14863,"Wrath of the Lich King",]
];

var mn_23_169=[
	[170,"Cooking",],
	[171,"Fishing",],
	[172,"First Aid",]
];

var mn_23_201=[
	[14864,"Classic",],
	[14865,"The Burning Crusade",],
	[14866,"Wrath of the Lich King",]
];

var mn_23_1=[
	[21,"Player vs. Player",],
	[122,"Deaths",],
	[128,"Kills",],
	[130,"Character",],
	[131,"Social",],
	[132,"Skills",],
	[133,"Quests",],
	[134,"Travel",],
	[141,"Combat",],
	[14807,"Dungeons & Raids",]
];

var mn_23_155=[
	[156,"Winter Veil",],
	[158,"Hallow's End",],
	[159,"Noble Garden",],
	[160,"Lunar Festival",],
	[161,"Midsummer",],
	[162,"Brewfest",],
	[163,"Children's Week",],
	[187,"Love is in the Air",]
];

var mn_23=[
	[168,"Dungeons & Raids",,mn_23_168],
	[97,"Exploration",,mn_23_97],
	[81,"Feats of Strength",],
	[92,"General",],
	[95,"Player vs. Player",,mn_23_95],
	[96,"Quests",,mn_23_96],
	[169,"Professions",,mn_23_169],
	[201,"Reputation",,mn_23_201],
	[1,"Statistics",,mn_23_1],
	[155,"World Events",,mn_23_155]
];

var mn_10_11=[
	[6,"Death Knight","glyph.aspx?id=6"],
	[11,"Druid","glyph.aspx?id=11"],
	[3,"Hunter","glyph.aspx?id=3"],
	[8,"Mage","glyph.aspx?id=8"],
	[2,"Paladin","glyph.aspx?id=2"],
	[5,"Priest","glyph.aspx?id=5"],
	[4,"Rogue","glyph.aspx?id=4"],
	[7,"Shaman","glyph.aspx?id=7"],
	[9,"Warlock","glyph.aspx?id=9"],
	[1,"Warrior","glyph.aspx?id=1"]
];

var mn_10_3=[
	[6,"Death Knight","talent.aspx?id=6"],
	[11,"Druid","talent.aspx?id=11"],
	[3,"Hunter","talent.aspx?id=3"],
	[8,"Mage","talent.aspx?id=8"],
	[2,"Paladin","talent.aspx?id=2"],
	[5,"Priest","talent.aspx?id=5"],
	[4,"Rogue","talent.aspx?id=4"],
	[7,"Shaman","talent.aspx?id=7"],
	[9,"Warlock","talent.aspx?id=9"],
	[1,"Warrior","talent.aspx?id=1"]
];

var mn_10=[
	[11,"Glyph Calculator","glyph.aspx?id=11",mn_10_11],
	[3,"Talent Calculator","talent.aspx?id=11",mn_10_3],
	[4,"Wish Lists","javascript:navToWishlist();"],
	[2,"Download the Curse Client","client.aspx"],
	[1,"Bug Reporting & Feedback","bugs.aspx"],
	[5,"Syndication","syndication.aspx"],
	[6,"Browser Plugins","plugins.aspx"],
	[7,"Latest Comments","latestComments.aspx"],
	[9,"Latest Screenshots","latestScreenshots.aspx"],
	[10,"Latest Users","latestUsers.aspx"],
	[8,"Latest Additions","latestAdditions.aspx"],
	[12,"Latest Profiles","latestProfiles.aspx"]
];

var mn_Main=[
	[9,"Profiles","search.aspx?browse=9&filters=217=1"],
	[1,"Items",,mn_1],[2,"NPCs",,mn_2],[3,"Zones",,mn_3],
	[4,"Quests",,mn_4],[5,"Objects",,mn_5],
	[6,"Spells",,mn_6],[8,"Factions",,mn_8],
	[23,"Achievements",,mn_23],
	[10,"Tools","$nonav$",mn_10]
];

Curse.Tooltip = {
	gameContainers:[],
	gameTooltips:[],
	gameIcons:[],
	
	getGame:function(game){
		if(game==null){
			return"none";
		}
	
	if(game.clientX!=null){
		game="none";
	}
return game;
},

getPrefix:function(game){
	if(game=="none"){
		return"";
	} else {
		return game+"_";
	}
},

initialize:function(game){
	var currentContainer;
	var currentPrefix="";
	var currentTooltips;
	var currentIcons;
	
	if(game==null){
		game="none";
	}

	if(Curse.Tooltip.gameContainers[game]){
		return;
	}
	
	if(game!="none"){
		currentPrefix=game+"_";
	}
	
	Curse.Tooltip.gameContainers[game]=cg_ce("div");
	Curse.Tooltip.gameContainers[game].className=currentPrefix+"tooltip-container";
	Curse.Tooltip.gameIcons[game]=[];
	Curse.Tooltip.gameTooltips[game]=[];
	for(var i=0;i<3;i++){
		Curse.Tooltip.gameIcons[game][i]=cg_ce("div");
		Curse.Tooltip.gameIcons[game][i].className="tooltip-icon";
		
		var iconFrame=cg_ce("div");
		cg_ae(Curse.Tooltip.gameIcons[game][i],iconFrame);
		cg_ae(Curse.Tooltip.gameContainers[game],Curse.Tooltip.gameIcons[game][i]);
		Curse.Tooltip.gameTooltips[game][i]=cg_ce("div");
		Curse.Tooltip.gameTooltips[game][i].className=currentPrefix+"tooltip";
		
		var td;
		Curse.Tooltip.gameTooltips[game][i]._text=td=cg_ce("td");
		
		var t=cg_ce("table"),
				tb=cg_ce("tbody"),
				tr1=cg_ce("tr"),
				tr2=cg_ce("tr"),
				th1=cg_ce("th"),
				th2=cg_ce("th"),
				th3=cg_ce("th");
				
				th1.style.backgroundPosition="top right";
				th2.style.backgroundPosition="bottom left";
				th3.style.backgroundPosition="bottom right";
				cg_ae(tr1,td);
				cg_ae(tr1,th1);
				cg_ae(tb,tr1);
				cg_ae(tr2,th2);
				cg_ae(tr2,th3);
				cg_ae(tb,tr2);
				cg_ae(t,tb);
				cg_ae(Curse.Tooltip.gameTooltips[game][i],t);
				cg_ae(Curse.Tooltip.gameContainers[game],Curse.Tooltip.gameTooltips[game][i]);
		}

	cg_ae(document.body,Curse.Tooltip.gameContainers[game]);
},

showTip:function(text,className,game){
	game=Curse.Tooltip.getGame(game);
	
	if(typeof(className)=="object"||className==null){
		className="r";
	}
	
	text="<span class="+className+">"+text+"</span>";
	
	Curse.Tooltip.show(text,null,null,game);
	Curse.Tooltip.updateSize(game);
	Curse.Tooltip.updatePosition(game);
},

hide:function(){
	Curse.Tooltip.currentTooltipId=null;
	for(var p in Curse.Tooltip.gameContainers){
		if(p=="indexOf"){continue;}
		if(typeof(Curse.Tooltip.gameContainers[p]) == 'object'){
			Curse.Tooltip.hideIcons(p);
			Curse.Tooltip.gameContainers[p].style.display="none";
			Curse.Tooltip.gameContainers[p].style.width="auto";
			for(var i=0;i<Curse.Tooltip.gameTooltips[p].length;i++){
				Curse.Tooltip.gameTooltips[p][i]._text.innerHTML="";
				Curse.Tooltip.gameTooltips[p][i].style.display="none";
			}
		}
	}
},

hideIcons:function(game){game=Curse.Tooltip.getGame(game);
		//alert(Curse.Tooltip.gameIcons[game].length);
	if (Curse.Tooltip.gameIcons[game].length > 0) {
		for(var i=0,len=Curse.Tooltip.gameIcons[game].length;i<len;i++){
			Curse.Tooltip.gameIcons[game][i].style.backgroundImage="";
			Curse.Tooltip.gameIcons[game][i].style.display="none";
		}
	}
},

show:function(text1,text2,text3,game){
	game=Curse.Tooltip.getGame(game);
	
	if(Curse.Tooltip.gameContainers[game]==null){
		Curse.Tooltip.initialize(game);
	}

	Curse.Tooltip.gameTooltips[game][0]._text.innerHTML=text1;
	Curse.Tooltip.gameTooltips[game][0].style.display="block";
	
	if(text2!=null){
		Curse.Tooltip.gameTooltips[game][1]._text.innerHTML=text2;
		Curse.Tooltip.gameTooltips[game][1].style.display="block";
	}

	if(text3!=null){
		Curse.Tooltip.gameTooltips[game][2]._text.innerHTML=text3;
		Curse.Tooltip.gameTooltips[game][2].style.display="block";
	}
	
	Curse.Tooltip.gameContainers[game].style.visibility="hidden";
	Curse.Tooltip.gameContainers[game].style.display="block";
	Curse.Tooltip.updateSize(game);
	Curse.Tooltip.updatePosition(game);
	Curse.Tooltip.gameContainers[game].style.visibility="visible";
},

updatePosition:function(game){
	game=Curse.Tooltip.getGame(game);
	
	if(!Curse.Tooltip.gameContainers[game]){
		return;
	}
	
	if(Curse.Tooltip.gameContainers[game].style.display!="block"){
		return;
	}
	
	var paddingWidth=15,
			paddingHeight=20,
			scrollPad=20,
			viewportHeight=Curse.Client.viewportHeight(),
			viewportWidth=Curse.Client.viewportWidth()-scrollPad,
			boundTop=Curse.Client.scrollTop(),
			boundBottom=viewportHeight+boundTop,
			tooltipHeight=Curse.Tooltip.gameContainers[game].offsetHeight,
			tooltipWidth=Curse.Tooltip.gameContainers[game].offsetWidth,
			boundLeft=0,
			boundRight=viewportWidth,
			
			mousePos={
				x:Curse.Mouse.x,
				y:Curse.Mouse.y
			},
			
			newTop=mousePos.y+paddingHeight,
			newLeft=mousePos.x+paddingWidth,
			inversePosition=false;

		Curse.Tooltip.orientation="bottom";
	
	if(Curse.Tooltip.inversed){
		Curse.Tooltip.orientation="top";
		paddingWidth=10;
	}
	
	if(Curse.Tooltip.inversed||(newTop+tooltipHeight)>=boundBottom){
		Curse.Tooltip.orientation="top";
		newTop=mousePos.y-tooltipHeight-paddingHeight;
		if(newTop<0){
			newTop=(viewportHeight-tooltipHeight)/2;
		}
	} else {
		newTop=mousePos.y+paddingHeight+10;
	}
			
	if(Curse.Tooltip.inversed||(newLeft+tooltipWidth)>=boundRight){
		newLeft=boundRight-Curse.Tooltip.gameContainers[game].offsetWidth;
		newLeft=mousePos.x-paddingWidth-Curse.Tooltip.gameContainers[game].offsetWidth;
	}

	if(newTop<boundTop){
		newTop=boundTop;
		Curse.Tooltip.orientation="top";
	}
	
	if(newLeft<boundLeft){
		newLeft=boundLeft;
	}
	
	Curse.Tooltip.gameContainers[game].style.top=newTop+"px";
	Curse.Tooltip.gameContainers[game].style.left=newLeft+"px";
	//alert("Curse.Mouse.x : "+Curse.Mouse.x+"\nCurse.Mouse.y : "+Curse.Mouse.y+"\nmousePos.y : "+mousePos.y+"\nmousePos.x : "+mousePos.x+"\npaddingHeight : "+paddingHeight+"\npaddingWidth : "+paddingWidth);
	Curse.Tooltip.updateOrientation(game);
},

updateOrientation:function(game){
	
	game=Curse.Tooltip.getGame(game);
	
	if(Curse.Tooltip.orientation=="top"&&Curse.Tooltip.gameContainers[game].offsetHeight>Curse.Tooltip.gameTooltips[game][0].offsetHeight){
		for(var i=0,len=Curse.Tooltip.gameTooltips[game].length;i<len;i++){
			if(Curse.Tooltip.gameTooltips[game][i].style.display=="block"){
				Curse.Tooltip.gameTooltips[game][i].style.top="auto";
				Curse.Tooltip.gameTooltips[game][i].style.bottom=(Curse.Tooltip.gameTooltips[game][i].offsetHeight-Curse.Tooltip.gameTooltips[game].offsetHeight)+"px";
				alert((Curse.Tooltip.gameTooltips[game][i].offsetHeight-Curse.Tooltip.gameTooltips[game].offsetHeight)+"px");
			}
		}
	} else {
		for(var i=0,len=Curse.Tooltip.gameTooltips[game].length;i<len;i++){
			Curse.Tooltip.gameTooltips[game][i].style.top=Curse.Tooltip.gameTooltips[game][i].style.bottom="auto";
		}
	}
},

updateSize:function(game){
	game=Curse.Tooltip.getGame(game);
	var width=0;
	for(var i=0;i<3;i++){
		width+=Curse.Tooltip.gameTooltips[game][i].offsetWidth+Curse.Tooltip.gameIcons[game][i].offsetWidth;
	}
	Curse.Tooltip.gameContainers[game].style.width=width+5+"px";
},

setIcons:function(args,game){
	game=Curse.Tooltip.getGame(game);
	var prefix=this.getPrefix(game);
	Curse.Tooltip.initialize(game);
	for(var i=0,len=args.length;i<len;i++){
		if(args[i]==null){
			continue;
		}
		if(args[i].customClass){
			Curse.Tooltip.gameIcons[game][args[i].index].className="tooltip-icon "+args[i].customClass;
		} else {
			Curse.Tooltip.gameIcons[game][args[i].index].className="tooltip-icon";
		}
		Curse.Tooltip.gameIcons[game][args[i].index].style.backgroundImage="url("+args[i].image+")";
		Curse.Tooltip.gameIcons[game][args[i].index].style.display="block";
	}
},

updateTooltip:function(index,text,game){
	game=Curse.Tooltip.getGame(game);
	
	Curse.Tooltip.gameTooltips[game][index]._cell.innerHTML=text;
},

setCurrentTooltip:function(id){
	Curse.Tooltip.currentTooltipId=id;
},

getCurrentTooltip:function(){
	return Curse.Tooltip.currentTooltipId;
},

setCurrentTooltipLink:function(link){
	Curse.Tooltip.CurrentTooltipLink=link;
},

getCurrentTooltipLink:function(){
	return Curse.Tooltip.CurrentTooltipLink;
	}
};


cg_addEventListener(document,"mousemove",Curse.Tooltip.updatePosition);

function makeTooltipBorder(div){
	div.className="tooltip-container";
	
	var tooltip=cg_ce("div");
	tooltip.className="tooltip";
	tooltip.style.display="block";
	
	var td;
	tooltip._text=td=cg_ce("td");
	
	var t=cg_ce("table"),tb=cg_ce("tbody"),
		tr1=cg_ce("tr"),
		tr2=cg_ce("tr"),
		th1=cg_ce("th"),
		th2=cg_ce("th"),
		th3=cg_ce("th");
		
	th1.style.backgroundPosition="top right";
	th2.style.backgroundPosition="bottom left";
	th3.style.backgroundPosition="bottom right";
	cg_ae(tr1,td);
	cg_ae(tr1,th1);
	cg_ae(tb,tr1);
	cg_ae(tr2,th2);
	cg_ae(tr2,th3);
	cg_ae(tb,tr2);
	cg_ae(t,tb);
	cg_ae(tooltip,t);
	cg_ae(div,tooltip);
	return tooltip._text;
}

if(!Curse.WOWDB){
	Curse.WOWDB={};
}

var cg_achievements={};

cg_achievements.addData=function(data){
	if(data.length==null){
		dataArray=[];
		dataArray[0]=data;
	} else {
		dataArray=data;
	}
	
	for(var i=0,len=dataArray.length;i<len;i++){
		cg_achievements[dataArray[i].id]=dataArray[i];
		cg_achievements[dataArray[i].id].createIcon=Curse.WOWDB.Achievement.createIcon;
		cg_achievements[dataArray[i].id].getName=Curse.WOWDB.Achievement.getName;
	}
};
	
Curse.WOWDB.Achievement={};

Curse.WOWDB.Achievement.createIcon=function(size,num,showNum,baseURL,url){
	if(!baseURL){
		baseURL="";
	}
	
	if(url==null){
		url=baseURL+"achievement.aspx?id="+this.id;
	}
	return new Curse.Icon(size,this.icon,url,this.id,null,Curse.WOWDB.Tooltip.handleAchievementLinkOver,{hideIcon:true},Curse.Tooltip.hide,null,num,null,showNum,null,null,baseURL);
};

Curse.WOWDB.Achievement.getName=function(){
	if(this.name){
		return this.name;
	}
	var name=this.tooltip.substring(this.tooltip.indexOf("<b"),this.tooltip.indexOf("</b>"));
			name=name.substring(name.indexOf(">")+1);
			this.name=name;return this.name;
};

var cg_enchants={};
cg_enchants.addData=function(data){
	if(data.length==null){
		dataArray=[];
		dataArray[0]=data;
	} else {
		dataArray=data;
	}
	for(var i=0,len=dataArray.length;i<len;i++){
		this[dataArray[i].id]=dataArray[i];
	}
}

if(!Curse.WOWDB){
	Curse.WOWDB={};
}

Curse.WOWDB.Enchant=function(data){
	cg_cO(this,data);
}

if(!Curse.WOWDB){
	Curse.WOWDB={};
}

if(!Curse.WOWDB.Tooltip){
	Curse.WOWDB.Tooltip={};
}

Curse.WOWDB.SocketInfo={1:[1],2:[2,10,6,14],4:[4,14,12,6],8:[8,12,10,14]};
Curse.WOWDB.SocketInfo.HasBonus=function(gems,slots){
	if(gems.length!=slots.length){
		return false;
	}
	
for(var k=0;k<slots.length;k++){
	var foundGem=false;for(var j=0;
	j<Curse.WOWDB.SocketInfo[slots[k]].length;j++){
		if(Curse.WOWDB.SocketInfo[slots[k]][j]==gems[k]){
			foundGem=true;
		}
	}
	
	if(!foundGem){
		return false;
		}
}
return true;
};

	Curse.WOWDB.Tooltip.baseURL="";
	Curse.WOWDB.Tooltip.handleSpellLinkOver=function(link,args,e){
		if(args==null){args={};}
		args.relatedLink=link;Curse.WOWDB.Tooltip.showSpellTooltip(link._relatedID,args,e);
		//alert('123');
	};
	
	Curse.WOWDB.Tooltip.showSpellTooltip=function(id,args,e){
		//alert(id);alert(args);alert(e);
		var spell=cg_wow_spells[id];
		//alert(spell);
		if(!spell||!spell.tooltip){
			//alert('aaa');
			Curse.Tooltip.setCurrentTooltip(id);Curse.WOWDB.Tooltip.loadAsync(id,args.relatedLink,Curse.WOWDB.Tooltip.handleAjaxSpell);return;
		}
		if(args){var hideIcon=args["hideIcon"];}
		if(!hideIcon){
			//alert('bbb');
			Curse.Tooltip.setIcons([{index:0,image:Curse.WOWDB.Tooltip.baseURL+"icons/m/"+spell.icon+".gif"}],"wow");
		}
		//alert(spell.tooltip);
		Curse.Tooltip.show(spell.tooltip,null,null,"wow");
	};
	
	Curse.WOWDB.Tooltip.handleAchievementLinkOver=function(link,args,e){
		if(args==null){
			args={};
		}
		args.relatedLink=link;
		Curse.WOWDB.Tooltip.showAchievementTooltip(link._relatedID,args,e);
	};
	
	Curse.WOWDB.Tooltip.showAchievementTooltip=function(id,args,e){
		var ach=cg_achievements[id];
		
		if(!ach||!ach.tooltip){
			Curse.Tooltip.setCurrentTooltip(id);
			Curse.WOWDB.Tooltip.loadAsync(id,args.relatedLink,Curse.WOWDB.Tooltip.handleAjaxAchievement);
			return;
		}
		
		if(args){
			var hideIcon=args["hideIcon"];
		}
		
		if(!hideIcon){
			Curse.Tooltip.setIcons([{index:0,image:Curse.WOWDB.Tooltip.baseURL+"icons/m/"+ach.icon+".gif"}],"wow");
		}
		
		Curse.Tooltip.show(ach.tooltip,null,null,"wow");
	};
	
	Curse.WOWDB.Tooltip.handleTalentLinkOver=function(link,args,e){
		Curse.WOWDB.Tooltip.showTalentTooltip(link._relatedID,args,e);
	};
	
	Curse.WOWDB.Tooltip.showTalentTooltip=function(id,args,e){
		var talent=args.talents.byRank[id];
		if(!talent){
			return;
		}
		
		Curse.Tooltip.show(talent.tooltip,null,null,"wow");
	};

	Curse.WOWDB.Tooltip.loadAsync=function(id,relatedLink,handler){
		if(cg_isDefined(window,'cg_wowdbsyndication')){
			cg_wowdbsyndication.addExternalReference(id,relatedLink,handler,true);
			return;
		}
		var url="ajaxTooltip.aspx?id="+id;
		if(handler==Curse.WOWDB.Tooltip.handleAjaxQuest){
			url+="&type=4";
		} else if(handler==Curse.WOWDB.Tooltip.handleAjaxSpell){
			url+="&type=6";
		}else if(handler==Curse.WOWDB.Tooltip.handleAjaxAchievement){
			url+="&type=23";
		}
	
		cg_queueAjaxRequest(url,handler,"text","get",relatedLink,true);
	};

	Curse.WOWDB.Tooltip.handleItemLinkOver=function(link,args,e){
		if(!link._relatedID){
			var lpattern=new RegExp("(item|spell)\\.aspx\\?id=(\\d+)");
			var match=lpattern.exec(link.href);
			
			if(!match){
				return;
			}
			link._relatedID=match[2];
		}
		
		var item=cg_wow_items[link._relatedID];
		var uniqueSyndTest=true;
		if(cg_isDefined(window,'cg_wowdbsyndication')){
			if((link.gems||link.enchants)&&link.uniqueID==null){
				if(link.gems.length>0||link.enchants.length>0){
					link.uniqueID=cg_wowdbsyndication.getUniqueID();
				}
			}
			if(!cg_wowdbsyndication.hasLoadedObject(500,link.uniqueID)&&link.uniqueID!=null){
				uniqueSyndTest=false;
			}
		}
		
		if(!item||!item.tooltip||!uniqueSyndTest){
			Curse.Tooltip.setCurrentTooltipLink(link);
			Curse.Tooltip.setCurrentTooltip(link._relatedID);
			Curse.WOWDB.Tooltip.loadAsync(link._relatedID,link,Curse.WOWDB.Tooltip.handleAjaxItem);
			return;
		}
		
		var hideIcon,character,isEquipped;
		if(args){
			hideIcon=args["hideIcon"];
			character=args["relatedCharacter"];
			isEquipped=args["isEquipped"];
		}
		
		var cids=Curse.WOWDB.Tooltip.getComparisonItems(item.id,item.slot,character,isEquipped);
		if(!args){
			args={};
		}
		
		args.relatedLink=link;Curse.WOWDB.Tooltip.showItemTooltip(cids,args);
	};

	Curse.WOWDB.Tooltip.showItemTooltip=function(itemids,args){
		var item,desc2,desc3;
		if(args==null){
			args={};
		}

		if(args.relatedCharacter!=null){
			var item=args.relatedCharacter.itemsById[itemids[0]];
		} else {
			var item=cg_wow_items[itemids[0]];
			if(!item||!item.tooltip){
				args.relatedLink._relatedID=itemids[0];
				Curse.Tooltip.setCurrentTooltipLink(args.relatedLink);
				Curse.Tooltip.setCurrentTooltip(args.relatedLink._relatedID);
				Curse.WOWDB.Tooltip.loadAsync(args.relatedLink._relatedID,args.relatedLink,Curse.WOWDB.Tooltip.handleAjaxItem);
				return;
			}
		}
		if(item==null){
			return;
		}
		
		var icons=[];
		var text=[];
		for(var i=0,len=itemids.length;i<len;i++){
			if(itemids[i]==null){
				continue;
			}
			if(i==0&&args.relatedCharacter!=null){
				var item=args.relatedCharacter.itemsById[itemids[i]];
			} else {
				var item=cg_wow_items[itemids[i]];
			}
			if(item!=null&&item.tooltip){
				if(i>0||!args.hideIcon){
					icons[i]={index:i,image:Curse.WOWDB.Tooltip.baseURL+"icons/m/"+item.icon+".gif"};
				}
				text[i]=item.tooltip;
				if(i>0){
					text[i]+=Curse.WOWDB.Tooltip.getStatDiffsHTML(cg_wow_items[itemids[0]],item);
				}
				if(i==0&&args.relatedLink.enchants!=null){
					for(var j=0;j<args.relatedLink.enchants.length;j++){
						text[i]=Curse.WOWDB.Tooltip.enchant(text[i],args.relatedLink.enchants[j]);
					}
				}
				if(i==0&&args.relatedLink.gems!=null){
					text[i]=Curse.WOWDB.Tooltip.socket(text[i],args.relatedLink.gems);
				}
			} else {
				Curse.WOWDB.Tooltip.loadAsync(itemids[i],args.relatedLink,Curse.WOWDB.Tooltip.handleAjaxItem);
			}
		}
		if(icons.length>0){
			Curse.Tooltip.setIcons(icons,"wow");
		}
		Curse.Tooltip.show(text[0],text[1],text[2],"wow");
	};
	
	Curse.WOWDB.Tooltip.reqLvlMask=Localization.replace("requires_level","");
	Curse.WOWDB.Tooltip.enchant=function(tooltip,enchantId){
		var enc=cg_enchants[enchantId];
		if(enc==null){
			return tooltip;
		}
		if(tooltip.indexOf(Localization.durability)!=-1){
			var ttxt="<span id=\"tooltipEnchant\" class=\"r2\">"+enc.name+"</span><br/>"+Localization.durability;tooltip=tooltip.replace(Localization.durability,ttxt);
		}else if(tooltip.indexOf(Curse.WOWDB.Tooltip.reqLvlMask)!=-1){
			var ttxt="<span id=\"tooltipEnchant\" class=\"r2\">"+enc.name+"</span><br/>"+Curse.WOWDB.Tooltip.reqLvlMask;tooltip=tooltip.replace(Curse.WOWDB.Tooltip.reqLvlMask,ttxt);
		}
		return tooltip;
	};
	
	Curse.WOWDB.Tooltip.getGem=function(gemItem){
		for(var p in cg_enchants){
			if(cg_enchants[p].item==gemItem){
				return p;
			}
		}
		return null;
	};
	
	Curse.WOWDB.Tooltip.socket=function(tooltip,gems){
		var socket=new RegExp('<a .*?socket.*?>.*?<.*?>',"g");
		var socketType=new RegExp('socket-.');
		var matches=tooltip.match(socket);
		if(matches!=null){
			var socketTypes=[];
			var gemTypes=[];
			for(var i=0;i<matches.length;i++){
				var socketText=matches[i];
				var socketNum=socketText.match(socketType);
				socketNum=socketNum[0].replace("socket-","");
				socketTypes[i]=socketNum;
			}
		for(var i=0;i<gems.length;i++){
			var enchantId=Curse.WOWDB.Tooltip.getGem(gems[i]);
			if(enchantId==null){
				continue;
			}
			var enc=cg_enchants[enchantId];if(enc==null){
				continue;
			}
			if(matches.length>i){
				gemTypes[i]=enc.gemType;
				var ttxt="<span id=\"gemText"+i+"\" class=\"socket\" style=\"background:transparent url(http://www.wowdb.com/icons/t/"+enc.icon+".gif) no-repeat scroll left center;\" >"+enc.name+'</span>';tooltip=tooltip.replace(matches[i],ttxt);
			}
		}
		
		var applyBonus=Curse.WOWDB.SocketInfo.HasBonus(gemTypes,socketTypes);
		if(applyBonus){
			tooltip=tooltip.replace(/\<span class=r0\>Socket Bonus/,"<span class=r2>Socket Bonus");
		}
	}
return tooltip;
};

	Curse.WOWDB.Tooltip.handleQuestLinkOver=function(link,e){
		Curse.WOWDB.Tooltip.showQuestTooltip(link._relatedID,link);
	};
	
	Curse.WOWDB.Tooltip.showQuestTooltip=function(questid,link){
		var quest=cg_wow_quests[questid];if(quest!=null){
			Curse.Tooltip.show(quest.tooltip,null,null,"wow");
		}else{
			Curse.Tooltip.setCurrentTooltip(link._relatedID);
			Curse.WOWDB.Tooltip.loadAsync(questid,link,Curse.WOWDB.Tooltip.handleAjaxQuest);
		}
	};
		
	Curse.WOWDB.Tooltip.handleAjaxItem=function(data,link){
		var newItem=eval('('+data+')');
		if(cg_wow_items[newItem.id]){
			cg_cO(cg_wow_items[newItem.id],newItem);
		}else{
			cg_wow_items.addData(newItem);
		}
		if(link.enchants!=null){}
		if(Curse.WOWDB.Tooltip.getCurrentTooltip()==link._relatedID){
			link.onmouseover();
		}
		return;
	};
	
	Curse.WOWDB.Tooltip.handleAjaxQuest=function(data,link){
		var newQuest=eval('('+data+')');
		cg_wow_quests.addData(newQuest);
		if(Curse.WOWDB.Tooltip.getCurrentTooltip()==link._relatedID){
			link.onmouseover();
		}
		return;
		};
	
	Curse.WOWDB.Tooltip.handleAjaxSpell=function(data,link){
		cg_wow_spells.addData(eval("("+data+")"));
		if(Curse.WOWDB.Tooltip.getCurrentTooltip()==link._relatedID){
			link.onmouseover();
		}
		return;
	};
	
	Curse.WOWDB.Tooltip.handleAjaxAchievement=function(data,link){
		cg_achievements.addData(eval("("+data+")"));
		if(Curse.WOWDB.Tooltip.getCurrentTooltip()==link._relatedID){
			link.onmouseover();
		}
		return;
	};
	
	Curse.WOWDB.Tooltip.getStatDiffs=function(compareToItem,masterItem){
		function calculateStatDiffs(stat1,stat2){
			function getCStat(stat,id){
				for(var i=0;i<stat.length;i++){
					if(stat[i].id==id){
						return stat[i].val;
					}
				}
				return 0;
			}
			var sdifs=[];
			var sdone=[];
			for(var i=0;i<stat1.length;i++){
				var stat=stat1[i];
				if((stat.id==0)||(stat.id==8)){
					continue;
				}
				if(!sdone[stat.id]){
					var val=0;var sname;
					if(stat.id==7||stat.id==14){
						var vals1=stat.val.split('-');
						var nval=(((vals1[0]*1)+(vals1[1]*1))/2.0)/getCStat(stat1,stat.id+1);
						var damageRange2=getCStat(stat2,stat.id);
						if(damageRange2==0){
							continue;
						}
						var vals2=damageRange2.split('-');
						var nval2=(((vals2[0]*1)+(vals2[1]*1))/2.0)/getCStat(stat2,stat.id+1);
						val=roundperc(nval-nval2);
						sname=Localization.dps;
					}else{
						var compVal=getCStat(stat2,stat.id);val=roundperc(stat.val-compVal);
						sname=statnames[stat.id];
					}
					sdifs.push({name:sname,val:val});
					sdone[stat.id]=true;
				}
			}
			for(var i=0;i<stat2.length;i++){
				var stat=stat2[i];
				if((stat.id==0)||(stat.id==8)){
					continue;
				}
				if(sdone[stat.id]==null){
					var val=getCStat(stat1,stat.id)-stat.val;
					sdifs.push({name:statnames[stat.id],val:val});
					sdone[stat.id]=1;
				}
			}
		return sdifs;
		}
	
		var masterStats=masterItem.stats;
		if(!masterStats){
			return;
		}
		var compareToStats=compareToItem.stats;
		if(!compareToStats){
			return;
		}
		if(masterStats==null||compareToStats==null){
			return null;
		}
		return calculateStatDiffs(masterStats,compareToStats);
	};

	Curse.WOWDB.Tooltip.getStatDiffsHTML=function(masterItem,compareToItem){
		if(masterItem.id==compareToItem.id){
			return"";
		}
		var arrStatDiffs=Curse.WOWDB.Tooltip.getStatDiffs(compareToItem,masterItem);
		if(arrStatDiffs==null){
			return"";
		}
		var tooltipHTML="<div class=\"itemTooltipStatChangeList\">"+Localization.if_equip+" "+masterItem.getNameWithStyle();
		var statGainsHTML=[];
		var statLossesHTML=[];
		
		for(var j=0;j<arrStatDiffs.length;j++){
			var sdif=arrStatDiffs[j];
			if(sdif.val<=0){
				continue;
			}
			statGainsHTML.push("<span class=r"+((sdif.val>0)?('2'):('9'))+">"+((sdif.val>0)?('+'):(''))+sdif.val+" "+sdif.name+"</span>");
		}

		for(var j=0;j<arrStatDiffs.length;j++){
			var sdif=arrStatDiffs[j];
			if(sdif.val>=0){
				continue;
			}
			statLossesHTML.push("<span class=r"+((sdif.val>0)?('2'):('9'))+">"+((sdif.val>0)?('+'):(''))+sdif.val+" "+sdif.name+"</span>");
		}
		
		if(statGainsHTML.length>0){
			tooltipHTML+="<br>"+Localization.you_will_gain+" "+statGainsHTML.join(", ");
		}

		if(statLossesHTML.length>0){
			tooltipHTML+="<br>"+Localization.you_will_lose+" "+statLossesHTML.join(", ");
		}

		tooltipHTML+="</div>";
		return tooltipHTML;
	};
	
	Curse.WOWDB.Tooltip.getComparisonItems=function(itemID,itemSlot,character,isEquippedItem){
		var cids=new Array(3);
		cids[0]=itemID;
		if(itemSlot==null||isEquippedItem!=null||cg_isDefined(window,'cg_wowdbsyndication')){
			return cids;
		}
		if(cg_isDefined(window,'cg_profileEditor')){
			character=cg_profileEditor.character;
		}
		if(character){
			if(cg_profileEditor.currentSlot&&cg_getCSlotFromISlot(itemSlot).indexOf(cg_profileEditor.currentSlot)>=0&&character.getCharItemBySlot(cg_profileEditor.currentSlot)){
				cids[1]=character.getCharItemBySlot(cg_profileEditor.currentSlot).id;
				return cids;
			}
			var cslot=cg_getCSlotFromISlot(itemSlot);
			for(var i=0;i<cslot.length;i++){
				var charItem=character.getCharItemBySlot(cslot[i]);
				if(charItem){
					cids[i+1]=charItem.id;
				}
			}
			return cids;
		}else{
			var citem=cg_buildTooltipIds(itemID,itemSlot)+"";
			cids=citem.split("|");
			if(cids[1]&&cids[1]=="null"){
				cids[1]=null;
			}
			if(cids[2]&&cids[2]=="null"){
				cids[2]=null;
			}
			
			return cids;
		}
		
		if(!citem){
			cids[0]=itemID;return cids;
		}else{}

		return cids;
	};
	
	function cg_buildTooltipIds(id,slot){
		if((slot=="17")||(slot=="21")){
			if((cg_getCookie("Settings.PinnedItem.item_17")!=null)&&(cg_getCookie("Settings.PinnedItem.item_17")!="null")){
				return id+"|"+cg_getCookie("Settings.PinnedItem.item_17");
			} else if((cg_getCookie("Settings.PinnedItem.item_21")!=null)&&(cg_getCookie("Settings.PinnedItem.item_21")!="null")){
				return id+"|"+cg_getCookie("Settings.PinnedItem.item_21");
			} else if((cg_getCookie("Settings.PinnedItem.item_13")!=null)&&(cg_getCookie("Settings.PinnedItem.item_13")!="null")){
				var vals=cg_getCookie("Settings.PinnedItem.item_13").split("|");return id+"|"+vals[0];
			}

			return id+"|"+cg_getCookie("Settings.PinnedItem.item_"+slot);
		}
		if((slot=="5")||(slot=="20")){
			if((cg_getCookie("Settings.PinnedItem.item_5")!=null)&&(cg_getCookie("Settings.PinnedItem.item_5")!="null")){
				return id+"|"+cg_getCookie("Settings.PinnedItem.item_5");
			} else if(cg_getCookie("Settings.PinnedItem.item_20")!=null&&cg_getCookie("Settings.PinnedItem.item_20")!="null"){
				return id+"|"+cg_getCookie("Settings.PinnedItem.item_20");
			}
			return id;
		} else if((slot=="22")||(slot=="14")||(slot=="23")){
			if((cg_getCookie("Settings.PinnedItem.item_22")!=null)&&(cg_getCookie("Settings.PinnedItem.item_22")!="null")){
				return id+"|"+cg_getCookie("Settings.PinnedItem.item_22");
			} else if((cg_getCookie("Settings.PinnedItem.item_14")!=null)&&(cg_getCookie("Settings.PinnedItem.item_14")!="null")){
				return id+"|"+cg_getCookie("Settings.PinnedItem.item_14");
			} else if((cg_getCookie("Settings.PinnedItem.item_23")!=null)&&(cg_getCookie("Settings.PinnedItem.item_23")!="null")){
				return id+"|"+cg_getCookie("Settings.PinnedItem.item_23");
			} else if((cg_getCookie("Settings.PinnedItem.item_13")!=null)&&(cg_getCookie("Settings.PinnedItem.item_13")!="null")){
				var vals=cg_getCookie("Settings.PinnedItem.item_13").split("|");
				if((vals[1]!="")&&(vals[1]!=null)){
					return id+"|"+vals[1];
				}
			}

			return id+"|"+cg_getCookie("Settings.PinnedItem.item_"+slot);
		} else if(slot=="13") {
			var txt=id;if((cg_getCookie("Settings.PinnedItem.item_13")!="null")&&(cg_getCookie("Settings.PinnedItem.item_13")!=null)){
				var vals=cg_getCookie("Settings.PinnedItem.item_13").split("|");
				if((vals[0]!="")&&(vals[0]!=null)&&(vals[0]!="null")){
					txt+="|"+vals[0];
				}
			}
		if((cg_getCookie("Settings.PinnedItem.item_17")!="null")&&(cg_getCookie("Settings.PinnedItem.item_17")!=null)){
			txt+="|"+cg_getCookie("Settings.PinnedItem.item_17");
		}
		if((cg_getCookie("Settings.PinnedItem.item_21")!="null")&&(cg_getCookie("Settings.PinnedItem.item_21")!=null)){
			txt+="|"+cg_getCookie("Settings.PinnedItem.item_21");
		}
		if((cg_getCookie("Settings.PinnedItem.item_22")!="null")&&(cg_getCookie("Settings.PinnedItem.item_22")!=null)){
			txt+="|"+cg_getCookie("Settings.PinnedItem.item_22");
		}
		if((cg_getCookie("Settings.PinnedItem.item_14")!="null")&&(cg_getCookie("Settings.PinnedItem.item_14")!=null)){
			txt+="|"+cg_getCookie("Settings.PinnedItem.item_14");
		}
		if((cg_getCookie("Settings.PinnedItem.item_23")!="null")&&(cg_getCookie("Settings.PinnedItem.item_23")!=null)){
			txt+="|"+cg_getCookie("Settings.PinnedItem.item_23");
		}
		if((cg_getCookie("Settings.PinnedItem.item_13")!="null")&&(cg_getCookie("Settings.PinnedItem.item_13")!=null)){
			if((vals[1]!="")&&(vals[1]!=null)&&(vals[1]!="null")){
				txt+="|"+vals[1];
			}
		}
		
		return txt;
	} else {
		if((cg_getCookie("Settings.PinnedItem.item_"+slot)!=null)&&(cg_getCookie("Settings.PinnedItem.item_"+slot)!="null")){
			var txt=id;
			vals=cg_getCookie("Settings.PinnedItem.item_"+slot).split("|");
			if((vals[0]!=null)&&(vals[0]!="")){
				txt+="|"+vals[0];
			}
			if((vals[1]!=null)&&(vals[1]!="")){
				txt+="|"+vals[1];
			}
			return txt;
		} else {
			return id;
		}
	}
};

if(!Curse.WOWDB){
	Curse.WOWDB={};
}

var cg_wow_items={};
cg_wow_items.addData=function(data){
	if(data.length==null){
		dataArray=[];
		dataArray[0]=data;
	} else {
		dataArray=data;
	}
	
	for(var i=0,len=dataArray.length;i<len;i++){
		cg_wow_items[dataArray[i].id]=dataArray[i];
		cg_wow_items[dataArray[i].id].getNameWithStyle=Curse.WOWDB.Item.getNameWithStyle;
		cg_wow_items[dataArray[i].id].getNameWithRarity=Curse.WOWDB.Item.getNameWithRarity;
		cg_wow_items[dataArray[i].id].getName=Curse.WOWDB.Item.getName;
		cg_wow_items[dataArray[i].id].createIcon=Curse.WOWDB.Item.createIcon;
		cg_wow_items[dataArray[i].id].isCharacterItem=Curse.WOWDB.Item.isCharacterItem;
		cg_wow_items[dataArray[i].id].isEquipableItem=Curse.WOWDB.Item.isEquipableItem;
	}
};

Curse.WOWDB.Item={};
Curse.WOWDB.Item.isCharacterItem=function(){
	return Curse.WOWDB.Item.armorSlots.indexOf(this.slot)>=0;
};

Curse.WOWDB.Item.isEquipableItem=function(slot){
	return Curse.WOWDB.Item.heldSlots.indexOf(this.slot)>=0;
};

Curse.WOWDB.Item.armorSlots=[1,2,3,4,5,6,7,8,9,10,11,12,16,20];
Curse.WOWDB.Item.heldSlots=[12,13,14,15,17,21,22,23,25,26,28];
Curse.WOWDB.Item.createIcon=function(size,num,showNum,baseURL,url){
	if(!baseURL){
		baseURL="";
	}
	if(url==null){
		url=baseURL+"item.aspx?id="+this.id;
	}

	return new Curse.Icon(size,this.icon,url,this.id,null,Curse.WOWDB.Tooltip.handleItemLinkOver,{hideIcon:true},Curse.Tooltip.hide,null,num,null,showNum,null,null,baseURL);
};

Curse.WOWDB.Item.getNameWithStyle=function(){
	if(this.nameWithStyle){
		return this.nameWithStyle;
	}
	var name="<span "+this.tooltip.substring(2,this.tooltip.indexOf("</b>"))+"</span>";
	this.nameWithStyle=name;return this.nameWithStyle;
};

Curse.WOWDB.Item.getName=function(){
	if(this.name){
		return this.name;
	}
	if(!this.tooltip){
		return"";
	}
	var name=this.tooltip.substring(2,this.tooltip.indexOf("</b>"));
	name=name.substring(name.indexOf(">")+1);
	this.name=name;return this.name;
};

Curse.WOWDB.Item.getNameWithRarity=function(){
	if(!this.name){
		name=this.getName();
	}
	if(!this.tooltip){
		return"";
	}
	if(!this.rarity){
		this.rarity=this.tooltip.substring(10,this.tooltip.indexOf(">"));
	}

	return{
		name:this.name,rarity:this.rarity
	};
};

Curse.WOWDB.Item.fixSocketText=function(item){
	var tooltip=item.tooltip;
	var socket=new RegExp('(\\<a href=\\"search\\.aspx\\?[\\w|\\d|\\s|=|&]+\\" class=\\"socket\\-\\d \\w\\d\\"\\>)',"g");
	var socketng=new RegExp('(\\<a href=\\"search\\.aspx\\?[\\w|\\d|\\s|=|&]+\\" class=\\"socket\\-\\d \\w\\d\\"\\>)');
	var socketTypeReg=new RegExp('socket-([0-9])');
	var matches=tooltip.match(socket);var sockets=[];
	if(matches!=null){
		for(var i=0;i<matches.length;i++){
			var socket={};
			socket.index=i;
			var socketType=socketTypeReg.exec(matches[i]);
			socket.type=socketType[1];
			sockets.push(socket);
			item.gtot++;
			tooltip=tooltip.replace(socketng,matches[i].replace(/\>/," id='socketLine"+i+"'>"));
		}
	}
	item.sockets=sockets;
	item.tooltip=tooltip;
};

if(Curse.WOWDB.Site){
	cg_items=cg_wow_items;
}

if(!Curse.WOWDB){
	Curse.WOWDB={};
}

var cg_wow_quests={};
cg_wow_quests.addData=function(data){
	if(data.length==null){
		dataArray=[];
		dataArray[0]=data;
	} else {
		dataArray=data;
	}
	
	for(var i=0,len=dataArray.length;i<len;i++){
		cg_wow_quests[dataArray[i].id]=dataArray[i];
		cg_wow_quests[dataArray[i].id].getName=Curse.WOWDB.Quest.getName;
	}
};

Curse.WOWDB.Quest={};
Curse.WOWDB.Quest.getName=function(){
	if(this.name){
		return this.name;
	}
	var name=this.tooltip.substring(this.tooltip.indexOf("<b"),this.tooltip.indexOf("</b>"));
	name=name.substring(name.indexOf(">")+1);
	this.name=name;return this.name;
};

if(Curse.WOWDB.Site){
	cg_quests=cg_wow_quests;
}

if(!Curse.WOWDB){
	Curse.WOWDB={};
}

var cg_wow_spells={};
cg_wow_spells.addData=function(data){
	if(data.length==null){
		var dataArray=[];
		dataArray[0]=data;
	} else {
		var dataArray=data;
	}

	for(var i=0,len=dataArray.length;i<len;i++){
		cg_wow_spells[dataArray[i].id]=dataArray[i];
		cg_wow_spells[dataArray[i].id].createIcon=Curse.WOWDB.Spell.createIcon;
		cg_wow_spells[dataArray[i].id].getName=Curse.WOWDB.Spell.getName;
	}
};
	
Curse.WOWDB.Spell={};
Curse.WOWDB.Spell.createIcon=function(size,baseURL,url){
	if(!baseURL){
		baseURL="";
	}
	if(!url){
		url=baseURL+"spell.aspx?id="+this.id;
	}
	return new Curse.Icon(size,this.icon,url,this.id,null,Curse.WOWDB.Tooltip.handleSpellLinkOver,{hideIcon:true},Curse.Tooltip.hide,null,null,null,null,null,null,baseURL);
};

Curse.WOWDB.Spell.getName=function(){
	if(this.name){
		return this.name;
	}
	var name=this.tooltip.substring(this.tooltip.indexOf("<b>")+3,this.tooltip.indexOf("</b>"));this.name=name;return this.name;};if(Curse.WOWDB.Site){cg_spells=cg_wow_spells;}
if(!Curse.WOWDB)
	{Curse.WOWDB={};
}

Curse.WOWDB.Syndication=function(){
	this.initialize();
};

Curse.WOWDB.Syndication.prototype={
	uniqueID:0,baseURL:"http://www.wowdb.com/",bindEvents:function(){
		var closure=this.parseLinks.bindAsEventListener(this);
		cg_addEventListener(window,"load",closure);
	},getUniqueID:function(){
		return this.uniqueID++;
	},initialize:function(){
		if(this.baseURL.indexOf("$siteurl")==0){
			this.baseURL="http://localhost/wowdb/";
		}
		Curse.WOWDB.Tooltip.baseURL=this.baseURL;
		this.insertStyleSheet();
		this.bindEvents();
		this.loadedObjects={};
		this.links={};
		this.externalReferences=[];
	},insertStyleSheet:function(){
		var hd=document.getElementsByTagName("head")[0];
		var css=document.createElement("link");
		css.rel="stylesheet";
		css.type="text/css";
		/*css.href=this.baseURL+"tooltip.css";*/
		css.href="/guides/templates/rhuk_milkyway/css/tooltip.css";
		hd.appendChild(css);
	},hasLoadedObject:function(type,id){
		if(this.loadedObjects[type+"_"+id]==true){
			return true;
		}
		return false;
	},addExternalReference:function(id,relatedLink,handler,loadNow){
		var type=null;
		if(handler==Curse.WOWDB.Tooltip.handleAjaxItem){
			type="1";
		}else if(handler==Curse.WOWDB.Tooltip.handleAjaxQuest){
			type="4";
		}else if(handler==Curse.WOWDB.Tooltip.handleAjaxSpell){
			type="6";
		}else if(handler==Curse.WOWDB.Tooltip.handleAjaxAchievement){
			type="23";
		}
		if(relatedLink.uniqueID==null&&type=="1"&&(relatedLink.enchants!=null||relatedLink.gems!=null)){
			relatedLink.uniqueID=this.getUniqueID();
		}
		if(relatedLink.uniqueID!=null){
			if(this.loadedObjects["500_"+relatedLink.uniqueID]){
				return;
			}
			this.loadedObjects["500_"+relatedLink.uniqueID]=true;
		} else {
			if(this.loadedObjects[type+"_"+id]){
				return;
			}
			this.loadedObjects[type+"_"+id]=true;
		}

		var url="ExTooltips.aspx?id="+id+"&type="+type;
		if(type=="1"&&relatedLink.enchants!=null){
			if(relatedLink.enchants.length>0){
				var enchantString="";
				for(var i=0;i<relatedLink.enchants.length;i++){
					enchantString+=","+relatedLink.enchants[i];
				}
				url+="&enchants="+enchantString.substring(1,enchantString.length);
			}
		}
		
		if(type=="1"&&relatedLink.gems!=null){
			if(relatedLink.gems.length>0){
				var gemString="";
				for(var i=0;i<relatedLink.gems.length;i++){
					gemString+=","+relatedLink.gems[i];
				}
				url+="&gems="+gemString.substring(1,gemString.length);
			}
		}
		
		if(loadNow){
			var hd=document.getElementsByTagName("head")[0];
			var oscript=document.createElement("script");
			oscript.type="text/javascript";
			oscript.src=this.baseURL+url;hd.appendChild(oscript);
		} else {
			this.externalReferences.push(url);
		}
	},addObject:function(type,data){
		var link=null;switch(type){
			case 1:this.handleItemLoad(data);
			break;
			case 4:this.handleQuestLoad(data);
			break;
			case 6:this.handleSpellLoad(data);
			break;
			case 23:this.handleAchievementLoad(data);
			break;
			case 104:this.handleEnchantLoad(data);
			break;
		}
	},parseLinks:function(){
		var linksCol=document.getElementsByTagName("a");
		var lpattern=new RegExp("wowdb\\.com\\/(item|spell|quest|achievement)\\.aspx\\?id=(\\d+)");
		for(var i=0;i<linksCol.length;i++){
			var match=lpattern.exec(linksCol[i].href);if(match){
				var entityTypeID=1;var entityType=match[1];
				var entityID=match[2];
				linksCol[i].id=entityType+"_"+entityID;linksCol[i]._relatedID=entityID;
				if(this.links[linksCol[i].id]==null){
					this.links[linksCol[i].id]=[];
				}
				this.links[linksCol[i].id].push(linksCol[i]);
				if(entityType=="item"){
					this.setItemLink(linksCol[i]);
				}else if(entityType=="spell"){
					this.setSpellLink(linksCol[i]);
				}else if(entityType=="quest"){
					linksCol[i].onmouseover=Curse.WOWDB.Tooltip.handleQuestLinkOver.bind(this,linksCol[i]);
				}else if(entityType=="achievement"){
					this.setAchievementLink(linksCol[i]);
				}
				linksCol[i].onmouseout=Curse.Tooltip.hide;
			}
		}
		var hd=document.getElementsByTagName("head")[0];
		for(var i=0;i<this.externalReferences.length;i++){
			var oscript=document.createElement("script");
			oscript.type="text/javascript";
			oscript.src=this.baseURL+this.externalReferences[i];
			hd.appendChild(oscript);
		}
		this.externalReferences=[];
	},handleEnchantLoad:function(data){
		cg_enchants.addData(data);
	},handleSpellLoad:function(data){
		cg_wow_spells.addData(data);
		var linkArray=this.links["spell_"+data.id];
		var id=data.id;
		for(var i=0;i<linkArray.length;i++){
			var link=linkArray[i];
			if(link.className.indexOf("wowdb-spell-full")==0){
				var arrLinkStyle=link.className.split("-");
				if(arrLinkStyle.length==4){
					iconSize=arrLinkStyle[3];
				}
				var fullSpell=this.getFullSpell(cg_wow_spells[id],null,iconSize,null);
				oReplace=link.parentNode.replaceChild(fullSpell,link);
			}else if(link.className.indexOf("wowdb-spell-text")==0){
				var spellLink=this.getSpellLink(cg_wow_spells[id],null);
				oReplace=link.parentNode.replaceChild(spellLink,link);
			}else{
				if(Curse.Tooltip.getCurrentTooltip()==link._relatedID){
					link.onmouseover();
				}
			}
		}
	},handleAchievementLoad:function(data){
		cg_achievements.addData(data);
		var linkArray=this.links["achievement_"+data.id];
		var id=data.id;
		for(var i=0;i<linkArray.length;i++){
			var link=linkArray[i];
			if(link.className.indexOf("wowdb-achievement-full")==0){
				var arrLinkStyle=link.className.split("-");
				if(arrLinkStyle.length==4){
					iconSize=arrLinkStyle[3];
				}
				var fullAch=this.getFullAchievement(cg_achievements[id],null,iconSize,null);
				oReplace=link.parentNode.replaceChild(fullAch,link);
			}else if(link.className.indexOf("wowdb-achievement-text")==0){
				var achLink=this.getAchievementLink(cg_achievements[id],null);
				oReplace=link.parentNode.replaceChild(achLink,link);
			}else{
				if(Curse.Tooltip.getCurrentTooltip()==link._relatedID){
					link.onmouseover();
				}
			}
		}
	},handleQuestLoad:function(data){
		cg_wow_quests.addData(data);
		var linkArray=this.links["quest_"+data.id];
		var id=data.id;
		for(var i=0;i<linkArray.length;i++){
			var link=linkArray[i];
			if(Curse.Tooltip.getCurrentTooltip()==link._relatedID){
				link.onmouseover();
			}
		}
	},handleItemLoad:function(data){
		cg_wow_items.addData(data);
		var linkArray=this.links["item_"+data.id];
		var id=data.id;
		for(var i=0;i<linkArray.length;i++){
			var link=linkArray[i];
			if(link.className.indexOf("wowdb-item-full")==0){
				var arrLinkStyle=link.className.split("-");
				if(arrLinkStyle.length==4){
					iconSize=arrLinkStyle[3];
				}
				var fullItem=this.getFullItem(cg_wow_items[id],null,iconSize,null);
				oReplace=link.parentNode.replaceChild(fullItem,link);
			}else if(link.className.indexOf("wowdb-item-text")==0){
				var textItem=this.getItemLink(cg_wow_items[id],null);
				oReplace=link.parentNode.replaceChild(textItem,link);
			}else{
				if(Curse.Tooltip.getCurrentTooltipLink()==link){
					link.onmouseover();
				}
			}
		}
	},getSpellLink:function(spell,tooltipLabel){
		if(!tooltipLabel){
			tooltipLabel=spell.getName();
		}
		var a=cg_ce("a");
		cg_ae(a,cg_ct(tooltipLabel));
		a.href=this.baseURL+"spell.aspx?id="+spell.id;
		a._relatedID=spell.id;
		a.onmouseover=Curse.WOWDB.Tooltip.handleSpellLinkOver.bind(this,a);
		a.onmouseout=Curse.Tooltip.hide;
		return a;
	},getAchievementLink:function(ach,tooltipLabel){
		if(!tooltipLabel){
			tooltipLabel=ach.getName();
		}
		var a=cg_ce("a");
		cg_ae(a,cg_ct(tooltipLabel));
		a.href=this.baseURL+"achievement.aspx?id="+ach.id;
		a._relatedID=ach.id;
		a.onmouseover=Curse.WOWDB.Tooltip.handleAchievementLinkOver.bind(this,a);
		a.onmouseout=Curse.Tooltip.hide;
		return a;
},getItemLink:function(item,tooltipLabel){
		if(!tooltipLabel){
			tooltipLabel=item.getNameWithRarity().name;
		}
		var a=cg_ce("a");
		a.className="r"+item.getNameWithRarity().rarity;
		cg_ae(a,cg_ct(tooltipLabel));
		a.href=this.baseURL+"item.aspx?id="+item.id;
		a._relatedID=item.id;
		a.onmouseover=Curse.WOWDB.Tooltip.handleItemLinkOver.bind(this,a);
		a.onmouseout=Curse.Tooltip.hide;return a;
	},getFullItem:function(item,tooltipLabel,iconSize,iconOnly){
		if(iconOnly==null){
			iconOnly=false;
		}
		var a=this.getItemLink(item,tooltipLabel);
		if(!iconSize){
			iconSize=1;
		}else{
			switch(iconSize){
				case"small":iconSize=0;
				break;
				case"medium":iconSize=1;
				break;
				case"large":iconSize=2;
				break;
			}
		}
		var tbl=document.createElement("TABLE");
		tbl.className="wowdb-table";
		var tbody=cg_ce('tbody');
		var row=cg_ce('tr');
		var oIconCell=cg_ce('td');
		var oLinkCell=cg_ce('td');
		cg_ae(oIconCell,item.createIcon(iconSize,0,0,this.baseURL));
		cg_ae(oLinkCell,a);
		row.appendChild(oIconCell);
		row.appendChild(oLinkCell);
		tbody.appendChild(row);
		tbl.appendChild(tbody);
		return tbl;
	},getFullSpell:function(spell,tooltipLabel,iconSize,iconOnly){
		if(iconOnly==null){
			iconOnly=false;
		}
		var a=this.getSpellLink(spell,tooltipLabel);
		if(!iconSize){
			iconSize=1;
		}else{
			switch(iconSize){
				case"small":iconSize=0;
				break;
				case"medium":iconSize=1;
				break;
				case"large":iconSize=2;
				break;
			}
		}
		var tbl=document.createElement("TABLE");
		tbl.className="wowdb-table";
		var tbody=cg_ce('tbody');
		var row=cg_ce('tr');
		var oIconCell=cg_ce('td');
		var oLinkCell=cg_ce('td');
		cg_ae(oIconCell,spell.createIcon(iconSize,this.baseURL));
		cg_ae(oLinkCell,a);row.appendChild(oIconCell);
		row.appendChild(oLinkCell);
		tbody.appendChild(row);
		tbl.appendChild(tbody);
		
		return tbl;
	},getFullAchievement:function(ach,tooltipLabel,iconSize,iconOnly){
		if(iconOnly==null){
			iconOnly=false;
		}
		var a=this.getAchievementLink(ach,tooltipLabel);
		if(!iconSize){
			iconSize=1;
		}else{
			switch(iconSize){
				case"small":iconSize=0;
				break;
				case"medium":iconSize=1;
				break;
				case"large":iconSize=2;
				break;
			}
		}
		var tbl=document.createElement("TABLE");
		tbl.className="wowdb-table";
		var tbody=cg_ce('tbody');
		var row=cg_ce('tr');
		var oIconCell=cg_ce('td');
		var oLinkCell=cg_ce('td');
		cg_ae(oIconCell,ach.createIcon(iconSize,0,false,this.baseURL));
		cg_ae(oLinkCell,a);
		row.appendChild(oIconCell);
		row.appendChild(oLinkCell);
		tbody.appendChild(row);
		tbl.appendChild(tbody);
		return tbl;
	},getEnchants:function(link){
		var enchants=new Array();
		for(var i=0;i<link.attributes.length;i++){
			var attr=link.attributes[i];
			if(attr.nodeName=="enchants"){
				enchants.push(attr.nodeValue);
			}
		}
		return enchants;
	},getGems:function(link){
		var gems=new Array();
		gems[0]=0;
		gems[1]=0;
		gems[2]=0;
		var foundGems=false;
		for(var i=0;i<link.attributes.length;i++){
			var attr=link.attributes[i];
			if(attr.nodeName=="gems"){
				foundGems=true;
				var gemStrings=attr.nodeValue.split(",");
				for(var j=0;j<gemStrings.length;j++){
					gems[j]=gemStrings[j];
				}
			}
		}
		if(foundGems){
			return gems;
		}else{
			return[];
		}
	},setItemLink:function(link){
		var enchants=this.getEnchants(link);
		if(enchants.length<1){
			enchants=[];
		}
		link.enchants=enchants;
		var gems=this.getGems(link);
		link.gems=gems;
		if(link.className.indexOf("wowdb-item")<0){
			link.onmouseover=Curse.WOWDB.Tooltip.handleItemLinkOver.bind(this,link);
			//alert(link.onmouseover);return;
		}
		this.addExternalReference(link._relatedID,link,Curse.WOWDB.Tooltip.handleAjaxItem);
	},setSpellLink:function(link){
		if(link.className.indexOf("wowdb-spell")<0){
			link.onmouseover=Curse.WOWDB.Tooltip.handleSpellLinkOver.bind(this,link);
			//alert(link.onmouseover);return;
		}
		this.addExternalReference(link._relatedID,link,Curse.WOWDB.Tooltip.handleAjaxSpell);
	},setAchievementLink:function(link){
		if(link.className.indexOf("wowdb-achievement")<0){
			link.onmouseover=Curse.WOWDB.Tooltip.handleAchievementLinkOver.bind(this,link);
			return;
		}
		this.addExternalReference(link._relatedID,link,Curse.WOWDB.Tooltip.handleAjaxAchievement);
	},detectDoctype:function(){
		var re=/\s+(X?HTML)\s+([\d\.]+)\s*([^\/]+)*\//gi;
		var res=false;
		if(typeof document.namespaces!="undefined"){
			res=document.all[0].nodeType==8?re.test(document.all[0].nodeValue):false;
		}else{
			res=document.doctype!=null?re.test(document.doctype.publicId):false;
		}
    if(res){
    	res=new Object();
    	res['xhtml']=RegExp.$1;
    	res['version']=RegExp.$2;
    	res['importance']=RegExp.$3;
    	
    	return res;
    }else{
    	return null;
    }
   },useFixedWidths:function(){
   	if(!Curse.Browser.ie){
   	
   	return false;
   }
   if(Curse.Browser.ie6){
   	return true;
   }
   if(Curse.Browser.ie7){
   	var doctype=this.detectDoctype();
   	if(doctype==null){
   		return true;
   	}else{
   		if(doctype.version&&doctype.version=="4.0"){
   			return true;
   		}
		return false;
	}
}
}
};
var cg_wowdbsyndication=new Curse.WOWDB.Syndication();