<?php
/*
  $Id: form_check.js.php,v 1.9 2009/04/14 04:16:46 pooifong.chin Exp $

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2003 osCommerce

  Released under the GNU General Public License
*/
?>
<script language="javascript"><!--
var form = "";
var submitted = false;
var error = false;
var error_message = "";

function Trim(strValue) {
	return LTrim(RTrim(strValue));
}

function LTrim(strValue) {
	var LTRIMrgExp = /^\s */;
	return strValue.replace(LTRIMrgExp, '');
}

function RTrim(strValue) {
	var RTRIMrgExp = /\s *$/;
	return strValue.replace(RTRIMrgExp, '');
}

function check_input(field_name, field_size, message) {
  if (form.elements[field_name] && (form.elements[field_name].type != "hidden")) {
    var field_value = form.elements[field_name].value;
	
	if (form.elements[field_name].type.indexOf("select") === 0) {	// Alternative for State dynamically generated selection box / text box
		;
	} else {
	    if (field_value == '' || field_value.length < field_size) {
	      error_message = error_message + "* " + message + "\n";
	      error = true;
	    }
	}
  }
}

function check_radio(field_name, message) {
  var isChecked = false;

  if (form.elements[field_name] && (form.elements[field_name].type != "hidden")) {
    var radio = form.elements[field_name];

    for (var i=0; i<radio.length; i++) {
      if (radio[i].checked == true) {
        isChecked = true;
        break;
      }
    }

    if (isChecked == false) {
      error_message = error_message + "* " + message + "\n";
      error = true;
    }
  }
}

function check_select(field_name, field_default, message) {
  if (form.elements[field_name] && (form.elements[field_name].type != "hidden")) {
    var field_value = form.elements[field_name].value;

    if (field_value == field_default) {
      error_message = error_message + "* " + message + "\n";
      error = true;
    }
  }
}

function check_dob(dob_month, dob_day, dob_year, message_1, message_2, message_3, message_4) {
	var day = form.elements[dob_day].value;
	var month = form.elements[dob_month].value;
	var year = form.elements[dob_year].value.length;
	var day_month_message_1 = "";
	var day_month_message_2 = "";
	var count = 0;
	var month_day_message = 0;
	
	var year_message = "";
	
	if (month == '') {
		day_month_message_2 = day_month_message_2 + " month";
		message_1 = message_1 + " month";
		count = count + 1;
		month_day_message = 1;
	}
	
	if (day == '') {
		month_day_message = 1;
		if (count == 0){
			count = count + 1;
			day_month_message_2 = day_month_message_2 + " day";
			message_1 = message_1 + " day";
		} else if(count == 1) {
			count = count + 1;
			day_month_message_2 = day_month_message_2 + " and day";
			message_1 = message_1 + " and day";
		}
	}
	
	if (year != 4) {
		count++;
		year_message = message_4;
	}
	
	if (count == 0) {
		message_1 = '';	
		message_2 = '';
		message_3 = '';
	}
	
	if (month_day_message < 1) {
		message_1 = '';
		message_2 = '';
		message_3 = '';
	}
	
	if (count > 0) {
		error_message = error_message + "* " + message_1 + day_month_message_1 + message_2 + day_month_message_2 + message_3 + year_message + "\n";
		error = true;
	}
}

function check_password(field_name_1, field_name_2, field_size, message_1, message_2) {
  if (form.elements[field_name_1] && (form.elements[field_name_1].type != "hidden")) {
    var password = form.elements[field_name_1].value;
    var confirmation = form.elements[field_name_2].value;

    if (password == '' || password.length < field_size) {
      error_message = error_message + "* " + message_1 + "\n";
      error = true;
    } else if (password != confirmation) {
      error_message = error_message + "* " + message_2 + "\n";
      error = true;
    }
  }
}


function check_password_new(field_name_1, field_name_2, field_name_3, field_size, message_1, message_2, message_3) {
  if (form.elements[field_name_1] && (form.elements[field_name_1].type != "hidden")) {
    var password_current = form.elements[field_name_1].value;
    var password_new = form.elements[field_name_2].value;
    var password_confirmation = form.elements[field_name_3].value;

    if (password_current == '' || password_current.length < field_size) {
      error_message = error_message + "* " + message_1 + "\n";
      error = true;
    } else if (password_new == '' || password_new.length < field_size) {
      error_message = error_message + "* " + message_2 + "\n";
      error = true;
    } else if (password_new != password_confirmation) {
      error_message = error_message + "* " + message_3 + "\n";
      error = true;
    }
  }
}

function ss(){
	if (!comments_validation())
	return false;
 }

function check_form(form_name) {
  if (submitted == true) {
    alert("<?php echo JS_ERROR_SUBMITTED; ?>");
    return false;
  }

  error = false;
  form = eval('document.'+form_name);
  //form = form_name;
  error_message = "<?php echo JS_ERROR; ?>";

<?php if (ACCOUNT_GENDER == 'true') echo '  check_radio("gender", "' . ENTRY_GENDER_ERROR . '");' . "\n"; ?>

  check_input("firstname", <?php echo ENTRY_FIRST_NAME_MIN_LENGTH; ?>, "<?php echo ENTRY_FIRST_NAME_ERROR; ?>");
  check_input("lastname", <?php echo ENTRY_LAST_NAME_MIN_LENGTH; ?>, "<?php echo ENTRY_LAST_NAME_ERROR; ?>");

<?php //if (ACCOUNT_DOB == 'true') echo '  check_input("dob", ' . ENTRY_DOB_MIN_LENGTH . ', "' . ENTRY_DATE_OF_BIRTH_ERROR . '");' . "\n"; ?>
<? if(ACCOUNT_DOB == 'true') echo ' check_dob("dob_month", "dob_day", "dob_year", "' . ENTRY_DATE_OF_BIRTH_ERROR_1 . '", "' . ENTRY_DATE_OF_BIRTH_ERROR_2 . '", "' . ENTRY_DATE_OF_BIRTH_ERROR_3 . '", "' . ENTRY_DATE_OF_BIRTH_ERROR_4 . '")'; ?>

  check_input("email_address", <?php echo ENTRY_EMAIL_ADDRESS_MIN_LENGTH; ?>, "<?php echo ENTRY_EMAIL_ADDRESS_ERROR; ?>");
  check_input("street_address", <?php echo ENTRY_STREET_ADDRESS_MIN_LENGTH; ?>, "<?php echo ENTRY_STREET_ADDRESS_ERROR; ?>");
  check_input("postcode", <?php echo ENTRY_POSTCODE_MIN_LENGTH; ?>, "<?php echo ENTRY_POST_CODE_ERROR; ?>");
  check_input("city", <?php echo ENTRY_CITY_MIN_LENGTH; ?>, "<?php echo ENTRY_CITY_ERROR; ?>");

<?php if (ACCOUNT_STATE == 'true') echo '  check_input("state", ' . ENTRY_STATE_MIN_LENGTH . ', "' . ENTRY_STATE_ERROR . '");' . "\n"; ?>

  check_select("country", "", "<?php echo ENTRY_COUNTRY_ERROR; ?>");
  check_select("country_code_id", "", "<?php echo ENTRY_LOCATION_ERROR; ?>");
  check_input("telephone", <?php echo ENTRY_TELEPHONE_MIN_LENGTH; ?>, "<?php echo ENTRY_TELEPHONE_NUMBER_ERROR; ?>");

  check_password("password", "confirmation", <?php echo ENTRY_PASSWORD_MIN_LENGTH; ?>, "<?php echo ENTRY_PASSWORD_ERROR; ?>", "<?php echo ENTRY_PASSWORD_ERROR_NOT_MATCHING; ?>");
  check_password_new("password_current", "password_new", "password_confirmation", <?php echo ENTRY_PASSWORD_MIN_LENGTH; ?>, "<?php echo ENTRY_PASSWORD_ERROR; ?>", "<?php echo ENTRY_PASSWORD_NEW_ERROR; ?>", "<?php echo ENTRY_PASSWORD_NEW_ERROR_NOT_MATCHING; ?>");
	
  	if (typeof(comments_validation) == 'function') {
  		var comment_check_result = comments_validation();
  		
		if (comment_check_result != '') {
			error = true;
			error_message += ("\n" + comment_check_result);
		}
	}
  
  if (error == true) {
    alert(error_message);
    return false;
  }  else {
    submitted = true;
    return true;
  }
  
}

<?php // +Country-State Selector ?>
function refresh_form(form_name) {
	form_name.action.value = 'refresh';
	form_name.state.value = '';
	form_name.submit();
	return true;
}

<?php // -Country-State Selector ?>

//--></script>
