/*
  $Id: general.js,v 1.20 2010/08/27 10:12:52 boonhock Exp $

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2003 osCommerce

  Released under the GNU General Public License
*/

function escapeFilteringText(id_text) {
	return id_text.replace(new RegExp(/[\&\.\,\(\)\'\:\!]/gi),"");
}

function SetFocus(TargetFormName) {
	var target = 0;
	
	if (TargetFormName != "") {
		for (i=0; i<document.forms.length; i++) {
			if (document.forms[i].name == TargetFormName) {
				target = i;
				break;
			}
		}
	}

	var TargetForm = document.forms[target];

	for (i=0; i<TargetForm.length; i++) {
		if ((TargetForm.elements[i].type != "image") && (TargetForm.elements[i].type != "hidden") && (TargetForm.elements[i].type != "reset") && (TargetForm.elements[i].type != "submit") ) {
			TargetForm.elements[i].focus();

			if ((TargetForm.elements[i].type == "text") || (TargetForm.elements[i].type == "password") ) {
				TargetForm.elements[i].select();
			}

			break;
		}
	}
}

function setCheckboxes(the_form, control_field, check_items)
{
	if (document.getElementById(control_field).value > 0) {	// if currently selected all
		var do_check = 0;
		document.getElementById(control_field).value = 0;
	} else { // if currently not selected all
		var do_check = 1;
		document.getElementById(control_field).value = 1;
	}

	var checkObjArray = check_items.split('##');
	for (var opt=0; opt < checkObjArray.length; opt++) {
		var check_item = checkObjArray[opt];
		var elts      = (typeof(document.forms[the_form].elements[check_item+'[]']) != 'undefined')
						? document.forms[the_form].elements[check_item+'[]']
						: "";
		var elts_cnt  = (typeof(elts.length) != 'undefined')
						? elts.length
						: 0;

		if (elts_cnt) {
	    	for (var i = 0; i < elts_cnt; i++) {
	        	elts[i].checked = do_check;
	    	} // end for
		} else if (elts!='') {
	       	elts.checked = do_check;
		} // end if... else
	}
    return true;
} // end of the 'setCheckboxes()' function

function gotoPage(page_url) {
	if (page_url != '')
		location.href = page_url;
}

function RemoveFormatString(TargetElement, FormatString) {
  if (TargetElement.value == FormatString) {
    TargetElement.value = "";
  }

  TargetElement.select();
}

function CheckDateRange(from, to) {
  if (Date.parse(from.value) <= Date.parse(to.value)) {
    return true;
  } else {
    return false;
  }
}

function IsValidDate(DateToCheck, FormatString) {
  var strDateToCheck;
  var strDateToCheckArray;
  var strFormatArray;
  var strFormatString;
  var strDay;
  var strMonth;
  var strYear;
  var intday;
  var intMonth;
  var intYear;
  var intDateSeparatorIdx = -1;
  var intFormatSeparatorIdx = -1;
  var strSeparatorArray = new Array("-"," ","/",".");
  var strMonthArray = new Array("jan","feb","mar","apr","may","jun","jul","aug","sep","oct","nov","dec");
  var intDaysArray = new Array(31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31);

  strDateToCheck = DateToCheck.toLowerCase();
  strFormatString = FormatString.toLowerCase();
  
  if (strDateToCheck.length != strFormatString.length) {
    return false;
  }

  for (i=0; i<strSeparatorArray.length; i++) {
    if (strFormatString.indexOf(strSeparatorArray[i]) != -1) {
      intFormatSeparatorIdx = i;
      break;
    }
  }

  for (i=0; i<strSeparatorArray.length; i++) {
    if (strDateToCheck.indexOf(strSeparatorArray[i]) != -1) {
      intDateSeparatorIdx = i;
      break;
    }
  }

  if (intDateSeparatorIdx != intFormatSeparatorIdx) {
    return false;
  }

  if (intDateSeparatorIdx != -1) {
    strFormatArray = strFormatString.split(strSeparatorArray[intFormatSeparatorIdx]);
    if (strFormatArray.length != 3) {
      return false;
    }

    strDateToCheckArray = strDateToCheck.split(strSeparatorArray[intDateSeparatorIdx]);
    if (strDateToCheckArray.length != 3) {
      return false;
    }

    for (i=0; i<strFormatArray.length; i++) {
      if (strFormatArray[i] == 'mm' || strFormatArray[i] == 'mmm') {
        strMonth = strDateToCheckArray[i];
      }

      if (strFormatArray[i] == 'dd') {
        strDay = strDateToCheckArray[i];
      }

      if (strFormatArray[i] == 'yyyy') {
        strYear = strDateToCheckArray[i];
      }
    }
  } else {
    if (FormatString.length > 7) {
      if (strFormatString.indexOf('mmm') == -1) {
        strMonth = strDateToCheck.substring(strFormatString.indexOf('mm'), 2);
      } else {
        strMonth = strDateToCheck.substring(strFormatString.indexOf('mmm'), 3);
      }

      strDay = strDateToCheck.substring(strFormatString.indexOf('dd'), 2);
      strYear = strDateToCheck.substring(strFormatString.indexOf('yyyy'), 2);
    } else {
      return false;
    }
  }

  if (strYear.length != 4) {
    return false;
  }

  intday = parseInt(strDay, 10);
  if (isNaN(intday)) {
    return false;
  }
  if (intday < 1) {
    return false;
  }

  intMonth = parseInt(strMonth, 10);
  if (isNaN(intMonth)) {
    for (i=0; i<strMonthArray.length; i++) {
      if (strMonth == strMonthArray[i]) {
        intMonth = i+1;
        break;
      }
    }
    if (isNaN(intMonth)) {
      return false;
    }
  }
  if (intMonth > 12 || intMonth < 1) {
    return false;
  }

  intYear = parseInt(strYear, 10);
  if (isNaN(intYear)) {
    return false;
  }
  if (IsLeapYear(intYear) == true) {
    intDaysArray[1] = 29;
  }

  if (intday > intDaysArray[intMonth - 1]) {
    return false;
  }
  
  return true;
}

function IsLeapYear(intYear) {
  if (intYear % 100 == 0) {
    if (intYear % 400 == 0) {
      return true;
    }
  } else {
    if ((intYear % 4) == 0) {
      return true;
    }
  }

  return false;
}

function disabledFormInputs(frm_name, disable_mode) {
	var temp_id, temp_hidden_obj;
	
	frm = document.forms[frm_name];
	if (frm == null)	return;
	
	for (i=0; i < frm.length; i++) {
		switch (frm.elements[i].type) {
			case 'text':
				frm.elements[i].disabled = disable_mode;
				break;
			case 'textarea':
				frm.elements[i].disabled = disable_mode;
				break;
			case 'select-one':
				frm.elements[i].disabled = disable_mode;
				break;
			case 'checkbox':
				frm.elements[i].disabled = disable_mode;
				break;
			case 'submit':
				frm.elements[i].disabled = disable_mode;
				break;
			case 'button':
				frm.elements[i].disabled = disable_mode;
				break;
			default:
				//alert(frm.elements[i].type);
				break;
		}
	}
	
	var divs = frm.getElementsByTagName('div');
    if(divs.length > 0) {
        for (var b = 0; b < divs.length; b++) {
        	temp_id = divs[b].id;
        	temp_hidden_obj = DOMCall(temp_id + '_hidden');
        	
        	if (temp_hidden_obj != null) {
        		if (disable_mode) {
        			divs[b].className = 'hide';
        			temp_hidden_obj.className = 'show';
	        	} else {
	        		divs[b].className = 'show';
	        		temp_hidden_obj.className = 'hide';
	        	}
        	}
        }
    } else {
    	temp_id = divs.id;
    	temp_hidden_obj = DOMCall(temp_id + '_hidden');
    	
    	if (temp_hidden_obj != null) {
    		if (disable_mode) {
    			divs.className = 'hide';
    			temp_hidden_obj.className = 'show';
        	} else {
        		divs.className = 'show';
        		temp_hidden_obj.className = 'hide';
        	}
    	}
    }
}

function rowOverEffect(object, class_name) {
	if (object.className == 'dataTableRow') {
  		object.className = 'dataTableRowOver';
  	} else if (class_name != '' && class_name != undefined && object.className != 'rowSelected'){
  		object.className = class_name;
  	}
}

function rowOutEffect(object, class_name) {
  	if (object.className == 'dataTableRowOver') {
  		object.className = 'dataTableRow';
	} else if (class_name != '' && class_name != undefined && object.className != 'rowSelected'){
  		object.className = class_name;
  	}
}

function rowClicked (object, class_name) {
  	if (object.className == 'rowSelected') {
  		object.className = class_name;
	} else {
  		object.className = 'rowSelected';
  	}
}

function confirm_delete(s, t, loc) {
	answer = confirm('Are you sure to delete '+ (trim_str(s) != '' ? "<" + s + "> " : '') + t + ' record?')
	if (answer !=0) {
		location = loc;
	}
}

function bookmark(url, title) {
  	if ((navigator.appName == "Microsoft Internet Explorer") && (parseInt(navigator.appVersion) >= 4)) {
  		window.external.AddFavorite(url,title);
  	} else if (navigator.appName == "Netscape") {
    	window.sidebar.addPanel(title,url,"");
  	} else {
    	alert("Press CTRL-D (Netscape) or CTRL-T (Opera) to bookmark");
  	}
}

function trim_str(strValue) {
	return ltrim_str(rtrim_str(strValue));
}

function ltrim_str(strValue) {
	var LTRIMrgExp = /^\s */;
	return strValue.replace(LTRIMrgExp, '');
}

function rtrim_str(strValue) {
	var RTRIMrgExp = /\s *$/;
	return strValue.replace(RTRIMrgExp, '');
}

function URLEncode(plain_text) {
	// The Javascript escape and unescape functions do not correspond
	// with what browsers actually do...
	var SAFECHARS = "0123456789" +					// Numeric
					"ABCDEFGHIJKLMNOPQRSTUVWXYZ" +	// Alphabetic
					"abcdefghijklmnopqrstuvwxyz" +
					"-_.!~*'()";					// RFC2396 Mark characters
	var HEX = "0123456789ABCDEF";

	var plaintext = plain_text;
	var encoded = "";
	for (var i = 0; i < plaintext.length; i++ ) {
		var ch = plaintext.charAt(i);
	    if (ch == " ") {
		    encoded += "+";				// x-www-urlencoded, rather than %20
		} else if (SAFECHARS.indexOf(ch) != -1) {
		    encoded += ch;
		} else {
		    var charCode = ch.charCodeAt(0);
			if (charCode > 255) {
			    alert( "Unicode Character '"
                        + ch
                        + "' cannot be encoded using standard URL encoding.\n" +
				          "(URL encoding only supports 8-bit characters.)\n" +
						  "A space (+) will be substituted." );
				encoded += "+";
			} else {
				encoded += "%";
				encoded += HEX.charAt((charCode >> 4) & 0xF);
				encoded += HEX.charAt(charCode & 0xF);
			}
		}
	} // for

	return encoded;
}

function URLDecode(encoded_text) {
   	// Replace + with ' '
   	// Replace %xx with equivalent character
   	// Put [ERROR] in output if %xx is invalid.
   	var HEXCHARS = "0123456789ABCDEFabcdef";
   	var encoded = encoded_text;
   	var plaintext = "";
   	var i = 0;
   	while (i < encoded.length) {
       	var ch = encoded.charAt(i);
	   	if (ch == "+") {
	       	plaintext += " ";
		   	i++;
	   	} else if (ch == "%") {
			if (i < (encoded.length-2)
					&& HEXCHARS.indexOf(encoded.charAt(i+1)) != -1
					&& HEXCHARS.indexOf(encoded.charAt(i+2)) != -1 ) {
				plaintext += unescape( encoded.substr(i,3) );
				i += 3;
			} else {
				alert( 'Bad escape combination near ...' + encoded.substr(i) );
				plaintext += "%[ERROR]";
				i++;
			}
		} else {
		   	plaintext += ch;
		   	i++;
		}
	} // while
	return plaintext;
}

function addEvent(obj, type, fn) {
	if (obj.attachEvent) {
    	obj['e'+type+fn] = fn;
    	obj[type+fn] = function(){obj['e'+type+fn]( window.event );}
    	obj.attachEvent( 'on'+type, obj[type+fn] );
  	} else {
    	obj.addEventListener( type, fn, false );
    }
}

//newState = true to disable, false to enable
function disableElement(elementIDArray, newState) {
    for (var i=0; i<elementIDArray.length; i++) {
        document.getElementById(elementIDArray[i]).disabled = newState;
    }
}

function changeElementDisplay(elementIDArray, className) {
    for (var i=0; i<elementIDArray.length; i++) {
        document.getElementById(elementIDArray[i]).className = className;
    }
}

function noEnterKey(e_obj) {
	var characterCode;

	if (e_obj && e_obj.which) {		//if which property of event object is supported (NN4)
		e_obj = e_obj;
		characterCode = e_obj.which; 	//character code is contained in NN4's which property
	} else {
		e_obj = event;
		characterCode = e_obj.keyCode; 	//character code is contained in IE's keyCode property
	}

	if(characterCode == 13){ 	//if generated character code is equal to ascii 13 (if enter key)
		return false;
	} else {
		return true;
	}
}

function checkForSelection(the_form, check_item) {
	var selectionMade = false;
	
	var elts      = (typeof(document.forms[the_form].elements[check_item]) != 'undefined')
    	          ? document.forms[the_form].elements[check_item]
        	      : "";
	var elts_cnt  = (typeof(elts.length) != 'undefined')
    	          ? elts.length
        	      : 0;
	
	if (elts_cnt) {
    	for (var i = 0; i < elts_cnt; i++) {
        	if (elts[i].checked == true) {
        		selectionMade = true;
        		break;
        	}
    	} // end for
	} else if (elts!='') {
       	if (elts.checked == true) {
       		selectionMade = true;
       	}
	} // end if... else
    return selectionMade;
}

function validateInteger( strValue ) {
	var objRegExp  = /(^\d\d*$)/;

	//check for integer characters
	return objRegExp.test(strValue);
}

function numbersOnly(myfield, e, dec) {
	var key;
	var keychar;
	
	if (window.event) {
		key = window.event.keyCode;
	} else if (e) {
   		key = e.which;
	} else {
   		return true;
	}
   	
	keychar = String.fromCharCode(key);
	// control keys
	if ((key==null) || (key==0) || (key==8) || (key==9) || (key==13) || (key==27) ) {
		return true;
	} else if ((("0123456789").indexOf(keychar) > -1)) {
		return true;
	} else if (dec && (keychar == ".")) {
		myfield.form.elements[dec].focus();
		return false;
	} else {
		return false;
	}
}

function validateSignInteger( strValue ) {
	var objRegExp  = /^[\+\-]?\d\d*$/;

	//check for integer characters
	return objRegExp.test(strValue);
}

function validateMayHvPtSignDecimal(strValue) {
	var objRegExp  = /^[\+\-]?\d+(\.\d+)?$/;

	//check for integer characters
	return objRegExp.test(strValue);
}

function currencyValidation(str) {
	var curchr, retval, fullstop;
	str.toString();
	retval=1;
	if (str) {
	    fullstop=0;
		for (i=0; i < str.length; i++) {
			curchr = str.charAt(i) ;
			if (curchr!='.' && curchr!=',' && curchr!='0' && !parseInt(curchr)) {
				retval = 0;
			} else if (curchr=='.') {
			    fullstop++;
			}
		}
		if (fullstop>1) {
		    retval=0;
		}
	}
	return retval;
}

function validateDate( strValue ) {
	/************************************************
	DESCRIPTION: Validates that a string contains only
	    valid dates with 2 digit month, 2 digit day,
	    4 digit year. Date separator can be ., -, or /.
	    Uses combination of regular expressions and
	    string parsing to validate date.
	    Ex. yyyy/mm/dd or yyyy-mm-dd or yyyy.mm.dd

	PARAMETERS:
	   strValue - String to be tested for validity

	RETURNS:
	   True if valid, otherwise false.

	REMARKS:
	   Avoids some of the limitations of the Date.parse()
	   method such as the date separator character.
	*************************************************/
	//var objRegExp = /^\d{4}(\-|\/|\.)\d{1,2}\1\d{1,2}$/
	if (strValue.indexOf(':') > 1) {
		var dateTimeArray = strValue.split(' ');
		var iDate = dateTimeArray[0];
		var iTime = dateTimeArray[1];

		var timeRegExp=new RegExp("^([0-1]?[0-9]|2[0-3]):[0-5]?[0-9]$");
		if (timeRegExp.test(iTime)==false) {
			return false;
		}
	} else {
		var iDate = strValue;
	}
	var objRegExp = /^\d{4}(\-)\d{1,2}\1\d{1,2}$/

	//check to see if in correct format
	if(!objRegExp.test(iDate))
		return false; //doesn't match pattern, bad date
	else {
		var arrayDate = iDate.split(RegExp.$1); //split date into month, day, year
		var intDay = parseInt(arrayDate[2],10);
		var intYear = parseInt(arrayDate[0],10);
		var intMonth = parseInt(arrayDate[1],10);

		//check for valid month
		if(intMonth > 12 || intMonth < 1) {
			return false;
		}

	    //create a lookup for months not equal to Feb.
	    var arrayLookup = { '01' : 31,'03' : 31, '04' : 30,'05' : 31,'06' : 30,'07' : 31,
	                        '08' : 31,'09' : 30,'10' : 31,'11' : 30,'12' : 31}

	    //check if month value and day value agree
	    if(arrayLookup[arrayDate[1]] != null) {
	    	if(intDay <= arrayLookup[arrayDate[1]] && intDay != 0)
	        	return true; //found in lookup table, good date
	    }

	    //check for February
		var booLeapYear = (intYear % 4 == 0 && (intYear % 100 != 0 || intYear % 400 == 0));
	    if( ((booLeapYear && intDay <= 29) || (!booLeapYear && intDay <=28)) && intDay !=0)
	      	return true; //Feb. had valid number of days
	}

	return false; //any other values, bad date
}

function validateEmail(str) {
	var at="@";
	var dot=".";
	var lat=str.indexOf(at);
	var lstr=str.length;
	var ldot=str.indexOf(dot);

	if (str.indexOf(at)==-1) {
	   return false;
	}

	if (str.indexOf(at)==-1 || str.indexOf(at)==0 || str.indexOf(at)== (lstr-1)) {
	   return false;
	}

	if (str.indexOf(dot)==-1 || str.indexOf(dot)==0 || str.indexOf(dot)==(lstr-1)) {
	    return false;
	}

	if (str.indexOf(at,(lat+1))!=-1) {
	    return false;
	}

	if (str.substring(lat-1,lat)==dot || str.substring(lat+1,lat+2)==dot) {
	    return false;
	}

	if (str.indexOf(dot,(lat+2))==-1) {
		return false;
	}

	if (str.indexOf(" ")!=-1) {
		return false;
	}

	return true;
}

function f_scrollTop() {
	return f_filterResults (
		window.pageYOffset ? window.pageYOffset : 0,
		document.documentElement ? document.documentElement.scrollTop : 0,
		document.body ? document.body.scrollTop : 0
	);
}

function f_filterResults(n_win, n_docel, n_body) {
	var n_result = n_win ? n_win : 0;
	if (n_docel && (!n_result || (n_result > n_docel)))
		n_result = n_docel;
	return n_body && (!n_result || (n_result > n_body)) ? n_body : n_result;
}

function input_on_focus(input_field, css_name) {
	input_field.className = css_name;
}

function input_on_blur(input_field, css_name) {
	if (trim_str(input_field.value) == '') {
		input_field.className = css_name;
	}
}

// Input fields effect
function getFocus (elObjID, msgObjID, msg)
{
	var classNameOnFocus = "focus";
	var elObj = document.getElementById(elObjID);

	if (elObj)
	{	
		elObj.className = classNameOnFocus;

		if (elObjID && msgObjID)
		{
			displayMessage(elObjID, msgObjID, msg);
		}
	}
}

function getBlur (elObjID, msgObjID, msg)
{
	var classNameOnBlur = "ezInputField";
	var elObj = document.getElementById(elObjID);

	if (elObj)
	{	
		elObj.className = classNameOnBlur;

		if (elObjID && msgObjID)
		{
			//displayMessage(elObjID, msgObjID, msg);
			hideMessage(elObjID, msgObjID);
		}
	}
}

function displayMessage(elID, msgID, msg)
{
	if (document.getElementById(elID) && document.getElementById(msgID))
 	{
 		var elObj = document.getElementById(elID);
 		var msgObj= document.getElementById(msgID);
 	
 		if ((elObj.value.length == 0 || trim_str(elObj.value) == '' || elObj.value == null) && msgID && msg)
 		{
 			document.getElementById(msgID).innerHTML = msg;
 			document.getElementById(msgID).style.color = "#666666";
 		}
 		else if (msg == msgObj.innerHTML)
 		{
 			hideMessage(elID, msgID);
 		}
 	}
 
}

function hideMessage(elID, msgID)
{
	if (document.getElementById(msgID) != null) // && document.getElementById(elID).value.length > 0)
	{
		document.getElementById(msgID).innerHTML = "";
	}
}

function URLEncode(plain_text) {
	// The Javascript escape and unescape functions do not correspond
	// with what browsers actually do...
	var SAFECHARS = "0123456789" +					// Numeric
					"ABCDEFGHIJKLMNOPQRSTUVWXYZ" +	// Alphabetic
					"abcdefghijklmnopqrstuvwxyz" +
					"-_.!~*'()";					// RFC2396 Mark characters
	var HEX = "0123456789ABCDEF";

	var plaintext = plain_text;
	var encoded = "";
	for (var i = 0; i < plaintext.length; i++ ) {
		var ch = plaintext.charAt(i);
	    if (ch == " ") {
		    encoded += "+";				// x-www-urlencoded, rather than %20
		} else if (SAFECHARS.indexOf(ch) != -1) {
		    encoded += ch;
		} else {
		    var charCode = ch.charCodeAt(0);
			if (charCode > 255) {
			    alert( "Unicode Character '"
                        + ch
                        + "' cannot be encoded using standard URL encoding.\n" +
				          "(URL encoding only supports 8-bit characters.)\n" +
						  "A space (+) will be substituted." );
				encoded += "+";
			} else {
				encoded += "%";
				encoded += HEX.charAt((charCode >> 4) & 0xF);
				encoded += HEX.charAt(charCode & 0xF);
			}
		}
	} // for

	return encoded;
}

function clear_input_field(input_field_id){//clear the input field onclick
		document.getElementById(input_field_id).value = '';		
}

function blockUI_disable ()
{
    jQuery.blockUI({ 
		css: { 
	        border: 'none', 
	        padding: '15px', 
	        backgroundColor: '#000', 
	        '-webkit-border-radius': '10px', 
	        '-moz-border-radius': '10px', 
	        opacity: '.5', 
	        color: '#fff' 
	    }
	 }); 
}

/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

/*
 * jQuery blockUI plugin
 * Version 2.08 (06/11/2008)
 * @requires jQuery v1.2.3 or later
 *
 * Examples at: http://malsup.com/jquery/block/
 * Copyright (c) 2007-2008 M. Alsup
 * Dual licensed under the MIT and GPL licenses:
 * http://www.opensource.org/licenses/mit-license.php
 * http://www.gnu.org/licenses/gpl.html
 * 
 * Thanks to Amir-Hossein Sobhi for some excellent contributions!
 */

;(function($) {

if (/1\.(0|1|2)\.(0|1|2)/.test($.fn.jquery) || /^1.1/.test($.fn.jquery)) {
    alert('blockUI requires jQuery v1.2.3 or later!  You are using v' + $.fn.jquery);
    return;
}

// global $ methods for blocking/unblocking the entire page
$.blockUI   = function(opts) { install(window, opts); };
$.unblockUI = function(opts) { remove(window, opts); };

// plugin method for blocking element content
$.fn.block = function(opts) {
    return this.each(function() {
        if ($.css(this,'position') == 'static')
            this.style.position = 'relative';
        if ($.browser.msie) 
            this.style.zoom = 1; // force 'hasLayout'
        install(this, opts);
    });
};

// plugin method for unblocking element content
$.fn.unblock = function(opts) {
    return this.each(function() {
        remove(this, opts);
    });
};

$.blockUI.version = 2.08; // 2nd generation blocking at no extra cost!

// override these in your code to change the default behavior and style
$.blockUI.defaults = {
    // message displayed when blocking (use null for no message)
    message:  '<h1>Please wait...</h1>',
    
    // styles for the message when blocking; if you wish to disable
    // these and use an external stylesheet then do this in your code:
    // $.blockUI.defaults.css = {};
    css: { 
        padding:        0,
        margin:         0,
        width:          '30%', 
        top:            '40%', 
        left:           '35%', 
        textAlign:      'center', 
        color:          '#000', 
        border:         '3px solid #aaa',
        backgroundColor:'#fff',
        cursor:         'wait'
    },
    
    // styles for the overlay
    overlayCSS:  { 
        backgroundColor:'#000', 
        opacity:        '0.6' 
    },
    
    // z-index for the blocking overlay
    baseZ: 1000,
    
    // set these to true to have the message automatically centered
    centerX: true, // <-- only effects element blocking (page block controlled via css above)
    centerY: true,
    
    // allow body element to be stetched in ie6; this makes blocking look better
    // on "short" pages.  disable if you wish to prevent changes to the body height
    allowBodyStretch: true,
    
    // be default blockUI will supress tab navigation from leaving blocking content;
    constrainTabKey: true,
    
    // fadeOut time in millis; set to 0 to disable fadeout on unblock
    fadeOut:  400,
    
    // if true, focus will be placed in the first available input field when
    // page blocking
    focusInput: true,
    
    // suppresses the use of overlay styles on FF/Linux (due to performance issues with opacity)
    applyPlatformOpacityRules: true,
    
    // callback method invoked when unblocking has completed; the callback is
    // passed the element that has been unblocked (which is the window object for page
    // blocks) and the options that were passed to the unblock call:
    //     onUnblock(element, options)
    onUnblock: null
};

// private data and functions follow...

var ie6 = $.browser.msie && /MSIE 6.0/.test(navigator.userAgent);
var pageBlock = null;
var pageBlockEls = [];

function install(el, opts) {
    var full = (el == window);
    var msg = opts && opts.message !== undefined ? opts.message : undefined;
    opts = $.extend({}, $.blockUI.defaults, opts || {});
    opts.overlayCSS = $.extend({}, $.blockUI.defaults.overlayCSS, opts.overlayCSS || {});
    var css = $.extend({}, $.blockUI.defaults.css, opts.css || {});
    msg = msg === undefined ? opts.message : msg;

    // remove the current block (if there is one)
    if (full && pageBlock) 
        remove(window, {fadeOut:0}); 
    
    // if an existing element is being used as the blocking content then we capture
    // its current place in the DOM (and current display style) so we can restore
    // it when we unblock
    if (msg && typeof msg != 'string' && (msg.parentNode || msg.jquery)) {
        var node = msg.jquery ? msg[0] : msg;
        var data = {};
        $(el).data('blockUI.history', data);
        data.el = node;
        data.parent = node.parentNode;
        data.display = node.style.display;
        data.position = node.style.position;
        data.parent.removeChild(node);
    }
    
    var z = opts.baseZ;
    
    // blockUI uses 3 layers for blocking, for simplicity they are all used on every platform;
    // layer1 is the iframe layer which is used to supress bleed through of underlying content
    // layer2 is the overlay layer which has opacity and a wait cursor
    // layer3 is the message content that is displayed while blocking
    
    var lyr1 = ($.browser.msie) ? $('<iframe class="blockUI" style="z-index:'+ z++ +';border:none;margin:0;padding:0;position:absolute;width:100%;height:100%;top:0;left:0" src="javascript:false;"></iframe>')
                                : $('<div class="blockUI" style="display:none"></div>');
    var lyr2 = $('<div class="blockUI" style="z-index:'+ z++ +';cursor:wait;border:none;margin:0;padding:0;width:100%;height:100%;top:0;left:0"></div>');
    var lyr3 = full ? $('<div class="blockUI blockMsg blockPage" style="z-index:'+z+';position:fixed"></div>')
                    : $('<div class="blockUI blockMsg blockElement" style="z-index:'+z+';display:none;position:absolute"></div>');

    // if we have a message, style it
    if (msg) 
        lyr3.css(css);

    // style the overlay
    if (!opts.applyPlatformOpacityRules || !($.browser.mozilla && /Linux/.test(navigator.platform))) 
        lyr2.css(opts.overlayCSS);
    lyr2.css('position', full ? 'fixed' : 'absolute');
    
    // make iframe layer transparent in IE
    if ($.browser.msie) 
        lyr1.css('opacity','0.0');

    $([lyr1[0],lyr2[0],lyr3[0]]).appendTo(full ? 'body' : el);
    
    // ie7 must use absolute positioning in quirks mode and to account for activex issues (when scrolling)
    var expr = $.browser.msie && (!$.boxModel || $('object,embed', full ? null : el).length > 0);
    if (ie6 || expr) {
        // give body 100% height
        if (full && opts.allowBodyStretch && $.boxModel)
            $('html,body').css('height','100%');

        // fix ie6 issue when blocked element has a border width
        if ((ie6 || !$.boxModel) && !full) {
            var t = sz(el,'borderTopWidth'), l = sz(el,'borderLeftWidth');
            var fixT = t ? '(0 - '+t+')' : 0;
            var fixL = l ? '(0 - '+l+')' : 0;
        }

        // simulate fixed position
        $.each([lyr1,lyr2,lyr3], function(i,o) {
            var s = o[0].style;
            s.position = 'absolute';
            if (i < 2) {
                full ? s.setExpression('height','document.body.scrollHeight > document.body.offsetHeight ? document.body.scrollHeight : document.body.offsetHeight + "px"')
                     : s.setExpression('height','this.parentNode.offsetHeight + "px"');
                full ? s.setExpression('width','jQuery.boxModel && document.documentElement.clientWidth || document.body.clientWidth + "px"')
                     : s.setExpression('width','this.parentNode.offsetWidth + "px"');
                if (fixL) s.setExpression('left', fixL);
                if (fixT) s.setExpression('top', fixT);
            }
            else if (opts.centerY) {
                if (full) s.setExpression('top','(document.documentElement.clientHeight || document.body.clientHeight) / 2 - (this.offsetHeight / 2) + (blah = document.documentElement.scrollTop ? document.documentElement.scrollTop : document.body.scrollTop) + "px"');
                s.marginTop = 0;
            }
        });
    }
    
    // show the message
    lyr3.append(msg).show();
    if (msg && (msg.jquery || msg.nodeType))
        $(msg).show();

    // bind key and mouse events
    bind(1, el, opts);
        
    if (full) {
        pageBlock = lyr3[0];
        pageBlockEls = $(':input:enabled:visible',pageBlock);
        if (opts.focusInput)
            setTimeout(focus, 20);
    }
    else
        center(lyr3[0], opts.centerX, opts.centerY);
};

// remove the block
function remove(el, opts) {
    var full = el == window;
    var data = $(el).data('blockUI.history');
    opts = $.extend({}, $.blockUI.defaults, opts || {});
    bind(0, el, opts); // unbind events
    var els = full ? $('body').children().filter('.blockUI') : $('.blockUI', el);
    
    if (full) 
        pageBlock = pageBlockEls = null;

    if (opts.fadeOut) {
        els.fadeOut(opts.fadeOut);
        setTimeout(function() { reset(els,data,opts,el); }, opts.fadeOut);
    }
    else
        reset(els, data, opts, el);
};

// move blocking element back into the DOM where it started
function reset(els,data,opts,el) {
    els.each(function(i,o) {
        // remove via DOM calls so we don't lose event handlers
        if (this.parentNode) 
            this.parentNode.removeChild(this);
    });
    if (data && data.el) {
        data.el.style.display = data.display;
        data.el.style.position = data.position;
        data.parent.appendChild(data.el);
        $(data.el).removeData('blockUI.history');
    }
    if (typeof opts.onUnblock == 'function')
        opts.onUnblock(el,opts);
};

// bind/unbind the handler
function bind(b, el, opts) {
    var full = el == window, $el = $(el);
    
    // don't bother unbinding if there is nothing to unbind
    if (!b && (full && !pageBlock || !full && !$el.data('blockUI.isBlocked'))) 
        return;
    if (!full) 
        $el.data('blockUI.isBlocked', b);
        
    // bind anchors and inputs for mouse and key events
    var events = 'mousedown mouseup keydown keypress click';
    b ? $(document).bind(events, opts, handler) : $(document).unbind(events, handler);

// former impl...
//    var $e = $('a,:input');
//    b ? $e.bind(events, opts, handler) : $e.unbind(events, handler);
};

// event handler to suppress keyboard/mouse events when blocking
function handler(e) {
    // allow tab navigation (conditionally)
    if (e.keyCode && e.keyCode == 9) {
        if (pageBlock && e.data.constrainTabKey) {
            var els = pageBlockEls;
            var fwd = !e.shiftKey && e.target == els[els.length-1];
            var back = e.shiftKey && e.target == els[0];
            if (fwd || back) {
                setTimeout(function(){focus(back)},10);
                return false;
            }
        }
    }
    // allow events within the message content
    if ($(e.target).parents('div.blockMsg').length > 0)
        return true;
        
    // allow events for content that is not being blocked
    return $(e.target).parents().children().filter('div.blockUI').length == 0;
};

function focus(back) {
    if (!pageBlockEls) 
        return;
    var e = pageBlockEls[back===true ? pageBlockEls.length-1 : 0];
    if (e) 
        e.focus();
};

function center(el, x, y) {
    var p = el.parentNode, s = el.style;
    var l = ((p.offsetWidth - el.offsetWidth)/2) - sz(p,'borderLeftWidth');
    var t = ((p.offsetHeight - el.offsetHeight)/2) - sz(p,'borderTopWidth');
    if (x) s.left = l > 0 ? (l+'px') : '0';
    if (y) s.top  = t > 0 ? (t+'px') : '0';
};

function sz(el, p) { 
    return parseInt($.css(el,p))||0; 
};
})(jQuery);
