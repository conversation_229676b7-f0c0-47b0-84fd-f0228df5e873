<SCRIPT language="JavaScript" type="text/javascript">
var pageTracker = _gat._getTracker("<?=GOOGLE_ANALYTICS_ACCOUNT_ID?>");
pageTracker._trackPageview();
pageTracker._addTrans(
	"<?=$orders['orders_id']?>", // order ID - required
	"<?=STORE_NAME?>", // affiliation or store name
	"<?=$order->info['total_value']?>", // total - required
	"0.00", // tax
	"0", // shipping
	"<?=$order->customer['city']?>", // city
	"<?=$order->customer['state']?>", // state or province
	"<?=$order->customer['country']?>" // country
);
<?
$total_product_count = count($order->products);

for ($p_cnt=0; $p_cnt < $total_product_count; $p_cnt++) {
?>
	pageTracker._addItem(
		"<?=$orders['orders_id']?>", // order ID - required
		"<?=$order->products[$p_cnt]['id']?>", // SKU/code
		"<?=$order->products[$p_cnt]['name']?>", // product name
		"<?=$order->products[$p_cnt]['name']?>", // category or variation
		"<?=$order->products[$p_cnt]['final_price']?>", // unit price - required
		"<?=$order->products[$p_cnt]['qty']?>" // quantity - required
	);
<?
}
?>
pageTracker._trackTrans();
</SCRIPT>