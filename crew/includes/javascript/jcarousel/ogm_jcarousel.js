//jQuery(document).ready(function() { 
	jQuery('#slide_holder').agile_carousel({
		slide_buttons: "yes",
		slide_directory: "images",
		slide_doctype: "html",
		slide_timer_length: "5000",
		slide_timer_on: "yes",
		transition_duration: 1000,
		transition_easing: "easeInOutSine",	
		transition_type: "fade",
			water_mark: "no"
	});
	jQuery("#slide_buttons div").click(function() {
		if (jQuery(this).css('top')=='10px') {
			jQuery(this).animate({top: 0});
		}
	});
	force_add_reflex = function() {
		if (jQuery("#slide_holder canvas").length ==0 || jQuery("#slide_holder canvas").length != jQuery("#slide_buttons li").length) {
			if (isIE){ 
				addIEReflex(); 
			}else { 
				addReflex();
			}	
			setTimeout('force_add_reflex()', 500);
		}
	}
	setTimeout('force_add_reflex()', 500);
//});