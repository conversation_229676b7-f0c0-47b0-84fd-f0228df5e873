var os_anim_start = 5000;
var os_anim_time = 500;
var os_force_change_time = 200;

var os_count = -1;
var os_timer = -1;
var os_current = -1;
var os_next = -1;
var os_nav_timer = -1;
var os_lock = false;

jQuery(document).ready(function() {
	osInit();
});

function osInit() {
	os_count = jQuery(".os_container").children(".os").length;
	os_current = 1;
	if (os_count > 1) {
		jQuery('#os_click').click(osClick);
		jQuery('.os_nav_btn').click(osMoveTo);
		
		osStartTimer();
		osNavBtnOff()
	} else {
		jQuery('#os_click').click(osClick);
	}
}

function osStopTimer(){
	if (os_timer != -1) {
		clearInterval(os_timer);
		os_timer = -1;
	}
}

function osStartTimer(){
	if (os_timer == -1) {
		os_timer = setInterval("osOnTimer()", os_anim_start);
	}
}

function osOnTimer() {
	if (!os_lock) {
		osShowNext();
	}
}

function osShowNext(c) {
	var b = 0;
	
	if (typeof(c) != 'undefined') {
		os_next = c;
		b = os_force_change_time;
	} else {
		os_next = parseInt(os_current) + 1;
		if (os_next > os_count) {
			os_next = 1;
		}
		
		if (os_next < 1) {
			os_next = 1;
		}
		
		b = os_anim_time;
	}
	
	jQuery('#os_' + os_next).css('z-index', os_count-1);
	osMarkActive(os_next);
	
	var a = jQuery('#os_' + os_next).attr('alt');
	jQuery('#os_nav_title').html(a);
	
	jQuery('#os_' + os_current).fadeOut(b, osAfterAnim);
}

function osAfterAnim() {
	if (os_next != -1) {
		jQuery('#os_' + os_current).css('z-index', 1);
		jQuery('#os_' + os_next).css('z-index', os_count);
		jQuery('#os_' + os_current).css({display: 'block'});
		os_current = os_next;
		os_next = -1;
	}
}

function osStop() {
	os_lock = true;
	osStopTimer();
	osNavShow();
	
	if (os_next != -1) {
		jQuery('#os_' + os_current).stop();
		jQuery('#os_' + os_current).css('opacity', 1);
		osAfterAnim();
	}
}

function osStart() {
	os_lock = false;
	osStartTimer();
	osNavHide();
}

function osMarkActive(a) {
	jQuery('.os_nav_btn').removeClass('active');
	jQuery('#os_nav_' + a).addClass('active');
}

function osMoveTo() {
	if (!jQuery(this).hasClass('active')) {
		osStop();
		var b = jQuery(this).attr('id');
		var a = b.replace('os_nav_', '');
		a = parseInt(a);
		
		if (a != 0) {
			osShowNext(a);
		}
	}
}

function osClick() {
	var fn = jQuery('#os_' + os_current).attr('onsubmit');
	eval(fn);
	fn();
}

function bigSpotNaviButtonOn() {
	var b = jQuery(this).attr('id');
	var c = parseInt(b.replace('os_nav_', ''));
	var a = jQuery('#os_' + c).attr('alt');
	jQuery('#os_nav_title').html(a);
}

function osNavBtnOff() {
	var a = jQuery('#os_' + os_current).attr('alt');
	jQuery('#os_nav_title').html(a);
}

function osNavShow() {
	clearTimeout(os_nav_timer);
	jQuery('.os_nav').fadeIn();
}

function osNavHide() {
	clearTimeout(os_nav_timer);
	os_nav_timer = setTimeout('jQuery(".os_nav").fadeOut()', 1000);
}