/*-- required & default rules --*/


#slide_holder {
height: 254px;
overflow: hidden;
position: relative;
text-align: left;
width: 598px;
/* -- width: 1533px; */
z-index: 800;
cursor:pointer;
}
#slide_holder_inner {
height: 254px;
/*left: 7px;*/
overflow: hidden;
position: absolute;
/*top: 8px;*/
width: 100%;
z-index: 2000;
}

/*-- for slide divs --*/

#slide_holder_inner div {
position: absolute;
}
/*-- for divs in html slides to position relative --*/
div#slide_holder_inner div.slide div {
position: relative;
}
div#carousel_controls {
height: 90%;
width: 592px;
z-index: 4000;
}

/*-- for slide buttons --*/

#slide_buttons li:hover, #pause_button, .prev_button, .next_button, #first_button, #last_button {
cursor: hand;
z-index: 5000;
}
#slide_buttons li span, #pause_button span, .prev_button span, .next_button span, #first_button span, #last_button span, #hover_next_button span, #hover_prev_button span, #water_mark span {
margin-left: -10000px;
}
#slide_buttons li, #pause_button, .prev_button, .next_button, #first_button, #last_button, #water_mark {
background-repeat: no-repeat;
overflow: hidden;
top: 0px;
}

#slide_buttons div{
	top: 10px;
}

ul#slide_buttons {
	position:absolute;
	z-index:5000;
	top:75%;
	width:100%;
}
ul#slide_buttons li { 
	float: left;
	height: 100px;
	width: 80px;
	list-style: none;
	margin: 10px 4px 0 4px;
	display: inline;
	z-index: 5000;
}
ul#slide_buttons li:hover {
	display:block;
	z-index: 5000;
	cursor: pointer;
}
ul#slide_buttons li#button_selected {
	display:block;
	z-index: 5000;
	
}
ul#slide_buttons li#button_selected:hover {
	display:block;
	z-index: 5000;
}

ul#slide_buttons img {
	cursor:pointer;
}
/*-- first & last buttons --*/

#first_button {
background-image: url(images/first.png);
height: 29px;
left: 354px;
width: 34px;
}
#first_button:hover {
background-image: url(images/first_hover.png);
}
#last_button {
background-image: url(images/last.png);
height: 29px;
left: 474px;
width: 34px;
}
#last_button:hover {
background-image: url(images/last_hover.png);
}

/*-- end first & last buttons --*/

/*-- next & prev buttons --*/
#nex_prev_buttons {
position: absolute;
left: 0;
z-index: 5000;
}
#next_button {
background-image: url(images/next.png);
height: 29px;
left: 443px;
width: 31px;
}
#next_button:hover {
background-image: url(images/next_hover.png);
}
#prev_button {
background-image: url(images/prev.png);
height: 29px;
left: 388px;
width: 31px;
}
#prev_button:hover {
background-image: url(images/prev_hover.png);
}
/*-- end next & prev buttons --*/


/*-- looks like pause button --*/
.pause_button {
background-image: url(images/pause.png);
height: 29px;
left: 419px;
width: 24px;
}
.pause_button:hover {
background-image: url(images/pause_hover.png);
}
/*-- looks like play button --*/
.paused_button {
background-image: url(images/play.png);
height: 29px;
left: 419px;
width: 24px;
}
.paused_button:hover {
background-image: url(images/play_hover.png);
}

/*-- hover next & hoverprev buttons --*/

#hover_next_button, #hover_prev_button {
height: 125px;
background-repeat: no-repeat;
overflow: hidden;
width: 77px;
top: 35px;
z-index: 6000;
}
#hover_next_button span {
}
#hover_next_button {
background-image: url(images/hover_next.png);
background-position: 21px 35px;
padding: 30px 0 30px 30px;
right: 0;
}
#hover_prev_button {
background-image: url(images/hover_prev.png);
background-position: 8px 35px;
padding: 30px 30px 30px 0;
left: 0;
}

/*-- end next & prev buttons --*/

/*-- rules for slide captions --*/

.slide_captions {
color: #333;
left: 20px;
overflow: visible;
top: 50px;
z-index: 6000;
}

.slide_captions span {
font-family: Arial;
font-size: 12px;
color: white;
overflow: visible;
}

/*-- end rules for slide captions --*/

/*-- rules for watermark --*/

#water_mark {
background-image: url(images/water_mark.png);
height: 30px;
left: 202px;
width: 246px;
top: 208px;
z-index: 6000;
}

#water_mark span {
}

/*-- end rules for slide captions --*/

/*-- slide number display --*/

#slide_number_display {
background-image: url(images/bg_swatch.png);
background-position: 0 0;
background-repeat: no-repeat;
color: #333;
font-family: Arial, Helvetica, sans-serif;
font-size: 15px;
height: 23px;
padding: 4px;
right: 7px;
text-align: center;
top: 208px;
width: 37px;
z-index: 5000;
}

/*-- for carousel view --*/

#row_of_slides {
position: absolute;
top: 0;
}
#row_of_slides .slide {
position: absolute;
}
				
/*-- end for carousel view --*/				

/*-- end number display --*/

/*-- end required & default rules --*/

/*-- example rules --*/
#slide_holder {
/*margin: 20px auto 0 auto;*/
}
