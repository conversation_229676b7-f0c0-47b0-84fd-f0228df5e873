/*
  jQuery MegaMenu Plugin
  Author: GeekTantra
  Author URI: http://www.geektantra.com
*/

jQuery.fn.megamenu = function(options) {
	var $megamenu_object = this;
	
	$megamenu_object.children("li.mm-item:not(.partition)").each(function(){
		var li_obj = jQuery(this);
		var mm_item_content_obj = jQuery(this).find(".mm-item-content");
		var $mm_item_link = jQuery(this).find(".mm-item-link");
		mm_item_content_obj.hide();
		
		// Activation Method Starts
		jQuery(this).bind("mouseover", function(e){
			e.stopPropagation();
			li_obj.addClass("mm-item-hover");

			mm_item_content_obj.css({
				'top': ($mm_item_link.position().top + $mm_item_link.outerHeight()) +"px", //($mm_item_link.offset().top + $mm_item_link.outerHeight()) - 1 +"px",
				'left': ($mm_item_link.position().left) -12 + 'px'
			});
			
			var mm_object_right_end = $megamenu_object.offset().left + $megamenu_object.outerWidth();
			// Coordinates of the right end of the megamenu object
			var mm_content_right_end = $mm_item_link.offset().left + mm_item_content_obj.outerWidth() - 5;
			// Coordinates of the right end of the megamenu content
			if( mm_content_right_end >= mm_object_right_end ) { // Menu content exceeding the outer box
				mm_item_content_obj.css({
					'left': ($mm_item_link.offset().left - (mm_content_right_end - mm_object_right_end)) - 2 + 'px'
				}); // Limit megamenu inside the outer box
			}
			
			mm_item_content_obj.show();
		});
		// Activation Method Ends
		// Deactivation Method Starts
		jQuery(this).bind("mouseleave", function(e){
			e.stopPropagation();
			jQuery(this).find("div.mm-item-content").hide();
			li_obj.removeClass("mm-item-hover");
		});
		//    Deactivation Method Ends
	});
	this.show();
};