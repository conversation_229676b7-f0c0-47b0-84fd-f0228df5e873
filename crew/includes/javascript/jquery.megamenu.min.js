jQuery.fn.megamenu=function(options){var $megamenu_object=this;$megamenu_object.children("li.mm-item:not(.partition)").each(function(){var li_obj=jQuery(this);var $mm_item_content=jQuery(this).find(".mm-item-content");var $mm_item_link=jQuery(this).find(".mm-item-link");$mm_item_content.hide();jQuery(this).bind("mouseover",function(e){e.stopPropagation();var mm_item_content_obj=jQuery(this).find("div.mm-item-content");li_obj.addClass("mm-item-hover");mm_item_content_obj.css({'top':($mm_item_link.position().top+$mm_item_link.outerHeight())+"px",'left':($mm_item_link.position().left)-12+'px'});var mm_object_right_end=$megamenu_object.offset().left+$megamenu_object.outerWidth();var mm_content_right_end=$mm_item_link.offset().left+$mm_item_content.outerWidth()-5;if(mm_content_right_end>=mm_object_right_end){mm_item_content_obj.css({'left':($mm_item_link.offset().left-(mm_content_right_end-mm_object_right_end))-2+'px'});}
mm_item_content_obj.show();});jQuery(this).bind("mouseleave",function(e){e.stopPropagation();jQuery(this).find("div.mm-item-content").hide();li_obj.removeClass("mm-item-hover");});});this.show();};