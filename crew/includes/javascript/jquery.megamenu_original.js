/*
  jQuery MegaMenu Plugin
  Author: GeekTantra
  Author URI: http://www.geektantra.com
*/

jQuery.fn.megamenu = function(options) {
	options = jQuery.extend({
					activate_action: "mouseover",	// mouseover
					deactivate_action: "mouseleave",  // mouseleave
					show_method: "simple",
					hide_method: "simple",
					justify: "left",
					enable_js_shadow: true,
					shadow_size: 3,
					mm_timeout: 0
				}, options);
	var $megamenu_object = this;
	
	if( options.activate_action == "click" ) options.mm_timeout = 0;

	$megamenu_object.children("li.mm-item:not(.partition)").each(function(){
		var li_obj = jQuery(this);
		var $mm_item_content = jQuery(this).find(".mm-item-content");
		var $mm_item_link = jQuery(this).find(".mm-item-link");
		$mm_item_content.hide();

		jQuery(document).bind("click", function(){
			jQuery(".mm-item-content").hide();
			li_obj.removeClass("mm-item-hover");
		});
	
		jQuery(this).bind("click", function(e){
			e.stopPropagation();
		});
		
		var $mm_timer = 0;
		// Activation Method Starts
		jQuery(this).bind(options.activate_action, function(e){
			e.stopPropagation();
			var mm_item_content_obj = jQuery(this).find("div.mm-item-content");
			clearTimeout($mm_timer);
			$mm_timer = setTimeout(function(){ //Emulate HoverIntent
				var top_adj = -1;
				li_obj.addClass("mm-item-hover");

				mm_item_content_obj.css({
					'top': ($mm_item_link.position().top + $mm_item_link.outerHeight()) +"px", //($mm_item_link.offset().top + $mm_item_link.outerHeight()) - 1 +"px",
					'left': ($mm_item_link.position().left) -12 + 'px'
				})
				
				var mm_object_right_end = $megamenu_object.offset().left + $megamenu_object.outerWidth();
				// Coordinates of the right end of the megamenu object
				var mm_content_right_end = $mm_item_link.offset().left + $mm_item_content.outerWidth() - 5 ;
				// Coordinates of the right end of the megamenu content
				if( mm_content_right_end >= mm_object_right_end ) { // Menu content exceeding the outer box
					mm_item_content_obj.css({
						'left': ($mm_item_link.offset().left - (mm_content_right_end - mm_object_right_end)) - 2 + 'px'
					}); // Limit megamenu inside the outer box
				}
				
				switch(options.show_method) {
					case "simple":
						mm_item_content_obj.show();
						break;
					case "slideDown":
						mm_item_content_obj.height("auto");
						mm_item_content_obj.slideDown('fast');
						break;
					case "fadeIn":
						mm_item_content_obj.fadeTo('fast', 1);
						break;
					default:
						mm_item_content_obj.each( options.show_method );
						break;
				}
			}, options.mm_timeout);
		});
		
		// Activation Method Ends
		// Deactivation Method Starts
		jQuery(this).bind(options.deactivate_action, function(e){
			e.stopPropagation();
			clearTimeout($mm_timer);
			var mm_item_content_obj = jQuery(this).find("div.mm-item-content");
			//      mm_item_content_obj.stop();
			switch(options.hide_method) {
				case "simple":
					mm_item_content_obj.hide();
					li_obj.removeClass("mm-item-hover");
					break;
				case "slideUp":
					mm_item_content_obj.slideUp( 'fast',  function() {
						li_obj.removeClass("mm-item-hover");
					});
					break;
				case "fadeOut":
					mm_item_content_obj.fadeOut( 'fast', function() {
						li_obj.removeClass("mm-item-hover");
					});
					break;
				default:
					mm_item_content_obj.each( options.hide_method );
					li_obj.removeClass("mm-item-hover");
					break;
			}
			
			if(mm_item_content_obj.length < 1) li_obj.removeClass("mm-item-hover");
		});
	//    Deactivation Method Ends
	});
	
	this.find(">li:last").after('<li class="clear-fix"></li>');
	this.show();
};
