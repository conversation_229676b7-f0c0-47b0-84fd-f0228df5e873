jQuery(document).ready(function(){
	jQuery('input:text , input:password').not(jQuery('.noFocusInput, .noFocusInput input')).not(jQuery('#filter_field_input')).not(jQuery('#search_field_input')).not(jQuery('#zone_country_filter')).focus( function() { changeOnFocusClass(this); } );
	jQuery('input:text , input:password').not(jQuery('.noFocusInput, .noFocusInput input')).not(jQuery('#filter_field_input')).not(jQuery('#search_field_input')).not(jQuery('#zone_country_filter')).blur( function() { changeOnBlurClass(this); } );
	
//	jQuery('#footer_cart_popup_box').hover( hover_over_mini_cart, hover_out_mini_cart );
	
	jQuery('#testimonial_scroll div').each(function (i) {
		id = id + 1;
		jQuery(this).Attr('id' , 'testimonial_scroll'+id);
		jQuery(this).hide();
	});
});

var minicart_interval;

function changeOnFocusClass(obj) {
	obj.className = 'inputfocus';
}

function changeOnBlurClass(obj) {
	obj.className = '';
}

function show_zones() {
	if (jQuery('#select_box_zones').css('display') == 'none') {
		var left_coordinate = jQuery('#select_link_zones').offset().left;
		var top_coordinate = jQuery('#select_link_zones').offset().top;
		var left_coordinate1 = left_coordinate + 5;
		var top_coordinate1 = top_coordinate + 5;

		jQuery('#select_box_zones').css('top',top_coordinate1+'px');
		jQuery('#select_box_zones').css('left',left_coordinate1+'px');

		jQuery('#select_box_zones_background').css('top',top_coordinate+'px');
		jQuery('#select_box_zones_background').css('left',left_coordinate+'px');

		jQuery('#select_box_zones').show();
		jQuery('#select_box_zones_background').show();
	}

	jQuery('#zone_country_filter').focus();
}

function hide_zones() {
	if (jQuery('#select_box_zones').css('display') != 'none') {
		jQuery('#select_box_zones').hide();
		jQuery('#select_box_zones_background').hide();
	}
}

function show_currencies() {
	if (jQuery('#select_box_currencies').css('display') == "none") {
		var left_coordinate = jQuery("#select_link_currencies").offset().left;
		var top_coordinate = jQuery("#select_link_currencies").offset().top;
		var left_coordinate1 = left_coordinate + 5;
		var top_coordinate1 = top_coordinate + 5;

		jQuery('#select_box_currencies').css('top',top_coordinate1+'px');
		jQuery('#select_box_currencies').css('left',left_coordinate1+'px');

		jQuery('#select_box_currencies_background').css('top',top_coordinate+'px');
		jQuery('#select_box_currencies_background').css('left',left_coordinate+'px');

		jQuery('#select_box_currencies').show();
		jQuery('#select_box_currencies_background').show();
	}
}

function hide_currencies() {
	if (jQuery('#select_box_currencies').css('display') != 'none') {
		jQuery('#select_box_currencies').hide();
		jQuery('#select_box_currencies_background').hide();
	}
}

function googleTranslateElementInit() {
	new google.translate.TranslateElement({
		pageLanguage: 'en',
		includedLanguages: 'af,sq,ar,hy,az,eu,be,bg,ca,hr,cs,da,nl,et,tl,fi,fr,gl,ka,de,el,ht,iw,hi,hu,is,id,ga,it,ja,ko,la,lv,lt,mk,ms,mt,no,fa,pl,pt,ro,ru,sr,sk,sl,es,sw,sv,th,tr,uk,ur,vi,cy,yi',
		multilanguagePage: true,
		gaTrack: true,
    	gaId: 'UA-318255-1'
	}, 'google_translate_element');
}

function submitenter(myfield, e) {
	var keycode;
	if (window.event) keycode = window.event.keyCode;
	else if (e) keycode = e.which;
	else return true;

	if (keycode == 13) {
	   myfield.form.submit();
	   return false;
	} else {
	   return true;
	}
}

function disableenter(myfield, e) {
	var keycode;
	if (window.event) keycode = window.event.keyCode;
	else if (e) keycode = e.which;
	else return true;
	
	if (keycode == 13){
		return false;
	} else {
		return true;
	}
}

function tongle_news (id) {
	if (jQuery('#'+id+'_long').css('display') == 'none') {
		jQuery('#'+id+'_long').slideDown();
		jQuery('#'+id+'_icon').attr('src','/images/icon-collapse.gif');
	} else {
		jQuery('#'+id+'_long').slideUp();
		jQuery('#'+id+'_icon').attr('src','/images/icon-expand.gif');
	}
}

function set_localization_value(key , value, javascript) {
	var url_path = config.regionalFormAction;
	var path_ext = url_path.indexOf('?') == -1 ? '?' : '&';
	
	javascript = javascript || '';
	if (javascript!='') {
		eval(javascript);
	}
	window.location = url_path + path_ext + key + '=' + value;
}

function add_to_shopping_cart_cp (url) {
	var buyqty = jQuery('#buyqty').val();
	var products_id = jQuery('#products_id').val();
	var products_bundle = jQuery('#products_bundle').val();
	var runflag = submitValidate();

	if (runflag == true) {
		jQuery.ajax({
			type: "POST",
			url: url,
			data: {buyqty : buyqty, products_id : products_id, products_bundle: products_bundle},
			async: false,
			dataType: "xml",
			success: function(data){
				jQuery(data).find('result').each(function(){
			        var type = jQuery('type', this).text();
			        
			        if (type == 'redirect') {
				        var url = jQuery('url', this).text();
					
						document.location.href = url;
					} else {
				        var total_item = jQuery('total_item', this).text();
				        var product_name = jQuery('product_name', this).text();
				        var subtotal = jQuery('subtotal', this).text();
		
						scroll(0, 0);
						jQuery('#mini_cart_added_item').html(product_name);
						jQuery('#mini_cart_total b').html(subtotal);
						jQuery('.itemQty').html(total_item);
						
						PopUpAddToCart();
						
						clearInterval(minicart_interval);
						
						minicart_interval = setInterval("hide_footer_popup('footer_cart')", 8000);
					}
			    });
			}
		});
		hideFancyBox();
	}
	realign_fancybox("popup_add_to_cart");
}

function hover_over_mini_cart() {
	clearInterval(minicart_interval);
}

function hover_out_mini_cart() {
	minicart_interval = setInterval("hide_footer_popup('footer_cart')", 1000);
}

function add_to_shopping_cart (url ,buyqty, products_bundle, buy_now_qty) {
	if (buyqty == '-1' && jQuery("#customqty").length) {
		buyqty = jQuery("#customqty").val();
    }

	jQuery.ajax({
		type: "POST",
		url: url,
		data: {buyqty : buyqty , products_bundle : products_bundle, buy_now_qty : buy_now_qty},
		async: false,
		dataType: "xml",
		success: function(data){
			jQuery(data).find('result').each(function(){
		        var type = jQuery('type', this).text();
		        
		        if (type == 'redirect') {
			        var url = jQuery('url', this).text();
				
					document.location.href = url;
				} else {
			        var total_item = jQuery('total_item', this).text();
			        var product_name = jQuery('product_name', this).text();
			        var subtotal = jQuery('subtotal', this).text();
	
					scroll(0,0);
					
					jQuery('#mini_cart_added_item').html(product_name);
					jQuery('#mini_cart_total b').html(subtotal);
					jQuery('.itemQty').html(total_item);
					
					PopUpAddToCart();
	
					clearInterval(minicart_interval);
					
					//jQuery('#footer_popup_box').hover( hover_over_mini_cart, hover_out_mini_cart );
					minicart_interval = setInterval("hide_footer_popup('footer_cart')", 8000);
				}
		    });
		}
	});
	hideFancyBox();
	realign_fancybox("popup_add_to_cart");
}

function add_to_shopping_cart_cur(url, buyqty, products_bundle, buy_now_qty) {
	var runflag = add_to_cart_form_checking();
	
	var delivery_mode = '';
	var extra_info_char_name = '';
	var extra_info_char_online_hr = '';
	var extra_info_char_online_dur = '';
	var delivery_mode = '';
	var extra_info_char_name = '';
	var extra_info_char_account_name = '';
	var extra_info_char_account_pwd = '';
	var delivery_mode = '';
	var extra_info_char_name = '';
	var extra_info_char_wow_account = '';	
	
	if (runflag) {
		if (jQuery('#delivery_mode_f2f').attr('checked')) {
			delivery_mode = jQuery('#delivery_mode_f2f').val();
			extra_info_char_name = jQuery('#extra_info_char_name').val();
			//extra_info_char_online_hr = jQuery('#extra_info_char_online_hr').val();
			//extra_info_char_online_dur = jQuery('#extra_info_char_online_dur').val();
		} else if (jQuery('#delivery_mode_guya').attr('checked')) {
			delivery_mode = jQuery('#delivery_mode_guya').val();
			extra_info_char_name = jQuery('#extra_info_char_name').val();
			extra_info_char_account_name = jQuery('#extra_info_char_account_name').val();
			extra_info_char_account_pwd = jQuery('#extra_info_char_account_pwd').val();
			extra_info_char_wow_account = jQuery('#extra_info_char_wow_account').val();
		} else if (jQuery('#delivery_mode_mail').attr('checked')) {
			delivery_mode = jQuery('#delivery_mode_mail').val();
			extra_info_char_name = jQuery('#extra_info_char_name').val();
		} else if (jQuery('#delivery_mode_open_store').attr('checked')) {
			delivery_mode = jQuery('#delivery_mode_open_store').val();
			extra_info_char_name = jQuery('#extra_info_char_name').val();
		}
		
		jQuery.ajax({
			type: "POST",
			url: url,
			data: {buyqty : buyqty , products_bundle : products_bundle, buy_now_qty : buy_now_qty, "extra_info[delivery_mode]" : delivery_mode, "extra_info[char_name]" : extra_info_char_name, "extra_info[char_online_time]" : extra_info_char_online_hr, "extra_info[char_online_dur]" : extra_info_char_online_dur, "extra_info[char_account_name]" : extra_info_char_account_name, "extra_info[char_account_pwd]" : extra_info_char_account_pwd, "extra_info[char_wow_account]" : extra_info_char_wow_account},
			async: false,
			dataType: "xml",
			success: function(data){
				jQuery(data).find('result').each(function() {
			        var type = jQuery('type', this).text();
			        
			        if (type == 'redirect') {
				        var url = jQuery('url', this).text();
					
						document.location.href = url;
					} else {
				        var total_item = jQuery('total_item', this).text();
				        var product_name = jQuery('product_name', this).text();
				        var subtotal = jQuery('subtotal', this).text();
		
						scroll(0, 0);
						jQuery('#mini_cart_added_item').html(product_name);
						jQuery('#mini_cart_total b').html(subtotal);
						jQuery('.itemQty').html(total_item);
						
						PopUpAddToCart();
						
						clearInterval(minicart_interval);
						
						//jQuery('#footer_popup_box').hover( hover_over_mini_cart, hover_out_mini_cart );
						minicart_interval = setInterval("hide_footer_popup('footer_cart')",8000);
					}
			    });
			}
		});
		
		//hideMe();
		hideFancyBox();
		hide_fancybox('general_popup_box');
	}
	realign_fancybox("popup_add_to_cart");
}

/**
* hoverIntent is similar to jQuery's built-in "hover" function except that
* instead of firing the onMouseOver event immediately, hoverIntent checks
* to see if the user's mouse has slowed down (beneath the sensitivity
* threshold) before firing the onMouseOver event.
* 
* hoverIntent r5 // 2007.03.27 // jQuery 1.1.2+
* <http://cherne.net/brian/resources/jquery.hoverIntent.html>
* 
* hoverIntent is currently available for use in all personal or commercial 
* projects under both MIT and GPL licenses. This means that you can choose 
* the license that best suits your project, and use it accordingly.
* 
* // basic usage (just like .hover) receives onMouseOver and onMouseOut functions
* $("ul li").hoverIntent( showNav , hideNav );
* 
* // advanced usage receives configuration object only
* $("ul li").hoverIntent({
*	sensitivity: 7, // number = sensitivity threshold (must be 1 or higher)
*	interval: 100,   // number = milliseconds of polling interval
*	over: showNav,  // function = onMouseOver callback (required)
*	timeout: 0,   // number = milliseconds delay before onMouseOut function call
*	out: hideNav    // function = onMouseOut callback (required)
* });
* 
* @param  f  onMouseOver function || An object with configuration options
* @param  g  onMouseOut function  || Nothing (use configuration options object)
* <AUTHOR> Cherne <<EMAIL>>
*/
(function($) {
	$.fn.hoverIntent = function(f,g) {
		// default configuration options
		var cfg = {
			sensitivity: 7,
			interval: 100,
			timeout: 0
		};
		// override configuration options with user supplied object
		cfg = $.extend(cfg, g ? { over: f, out: g } : f );

		// instantiate variables
		// cX, cY = current X and Y position of mouse, updated by mousemove event
		// pX, pY = previous X and Y position of mouse, set by mouseover and polling interval
		var cX, cY, pX, pY;

		// A private function for getting mouse position
		var track = function(ev) {
			cX = ev.pageX;
			cY = ev.pageY;
		};

		// A private function for comparing current and previous mouse position
		var compare = function(ev, ob) {
			ob.hoverIntent_t = clearTimeout(ob.hoverIntent_t);
			// compare mouse positions to see if they've crossed the threshold
			if ( ( Math.abs(pX-cX) + Math.abs(pY-cY) ) < cfg.sensitivity ) {
				$(ob).unbind("mousemove",track);
				// set hoverIntent state to true (so mouseOut can be called)
				ob.hoverIntent_s = 1;
				return cfg.over.apply(ob,[ev]);
			} else {
				// set previous coordinates for next time
				pX = cX; pY = cY;
				// use self-calling timeout, guarantees intervals are spaced out properly (avoids JavaScript timer bugs)
				ob.hoverIntent_t = setTimeout( function(){compare(ev, ob);} , cfg.interval );
			}
		};

		// A private function for delaying the mouseOut function
		var delay = function(ev,ob) {
			ob.hoverIntent_t = clearTimeout(ob.hoverIntent_t);
			ob.hoverIntent_s = 0;
			return cfg.out.apply(ob,[ev]);
		};

		// A private function for handling mouse 'hovering'
		var handleHover = function(e) {
			// next three lines copied from jQuery.hover, ignore children onMouseOver/onMouseOut
			var p = (e.type == "mouseover" ? e.fromElement : e.toElement) || e.relatedTarget;
			while ( p && p != this ) { try { p = p.parentNode; } catch(e) { p = this; } }
			if ( p == this ) { return false; }

			// copy objects to be passed into t (required for event object to be passed in IE)
			var ev = jQuery.extend({},e);
			var ob = this;

			// cancel hoverIntent timer if it exists
			if (ob.hoverIntent_t) { ob.hoverIntent_t = clearTimeout(ob.hoverIntent_t); }

			// else e.type == "onmouseover"
			if (e.type == "mouseover") {
				// set "previous" X and Y position based on initial entry point
				pX = ev.pageX; pY = ev.pageY;
				// update "current" X and Y position based on mousemove
				$(ob).bind("mousemove",track);
				// start polling interval (self-calling timeout) to compare mouse coordinates over time
				if (ob.hoverIntent_s != 1) { ob.hoverIntent_t = setTimeout( function(){compare(ev,ob);} , cfg.interval );}

			// else e.type == "onmouseout"
			} else {
				// unbind expensive mousemove event
				$(ob).unbind("mousemove",track);
				// if hoverIntent state is true, then call the mouseOut function after the specified delay
				if (ob.hoverIntent_s == 1) { ob.hoverIntent_t = setTimeout( function(){delay(ev,ob);} , cfg.timeout );}
			}
		};

		// bind the function to the two event listeners
		return this.mouseover(handleHover).mouseout(handleHover);
	};
})(jQuery);

/**
* <AUTHOR> Sharp
* @url http://remysharp.com/2007/01/25/jquery-tutorial-text-box-hints/
*/

(function ($) {

$.fn.hint = function (blurClass) {
 if (!blurClass) blurClass = 'blur';

 return this.each(function () {
 // get jQuery instance of 'this'
 var $$ = $(this);

 // get it once since it won't change
 var title = $$.attr('title');

 // only apply logic if the element has the attribute
 if (title) {

 // Note this is a one liner

 // on blur, set value to title attr if text is blank
 $$.blur(function () {
 if ($$.val() == '') {
 $$.val(title).addClass(blurClass);
 }
 })

 // on focus, set value to blank if current value matches title attr
 .focus(function () {
 if ($$.val() == title) {
 $$.val('').removeClass(blurClass);
 }
 })

 // clear the pre-defined text when form is submitted
 .parents('form:first').submit(function () {
 if ($$.val() == title) {
 $$.val('').removeClass(blurClass);
 }
 }).end()

 // now change all inputs to title
 .blur();

 // counteracts the effect of Firefox's autocomplete stripping the blur effect
 if ($.browser.mozilla && !$$.attr('autocomplete')) {
 setTimeout(function () {
 if ($$.val() == title) $$.val('');
 $$.blur();
 }, 10);
 }
 }
 });
};

})(jQuery);

/*
 * jQuery blockUI plugin
 * Version 2.08 (06/11/2008)
 * @requires jQuery v1.2.3 or later
 *
 * Examples at: http://malsup.com/jquery/block/
 * Copyright (c) 2007-2008 M. Alsup
 * Dual licensed under the MIT and GPL licenses:
 * http://www.opensource.org/licenses/mit-license.php
 * http://www.gnu.org/licenses/gpl.html
 * 
 * Thanks to Amir-Hossein Sobhi for some excellent contributions!
 */

;(function($) {

if (/1\.(0|1|2)\.(0|1|2)/.test($.fn.jquery) || /^1.1/.test($.fn.jquery)) {
    alert('blockUI requires jQuery v1.2.3 or later!  You are using v' + $.fn.jquery);
    return;
}

// global $ methods for blocking/unblocking the entire page
$.blockUI   = function(opts) { install(window, opts); };
$.unblockUI = function(opts) { remove(window, opts); };

// plugin method for blocking element content
$.fn.block = function(opts) {
    return this.each(function() {
        if ($.css(this,'position') == 'static')
            this.style.position = 'relative';
        if ($.browser.msie) 
            this.style.zoom = 1; // force 'hasLayout'
        install(this, opts);
    });
};

// plugin method for unblocking element content
$.fn.unblock = function(opts) {
    return this.each(function() {
        remove(this, opts);
    });
};

$.blockUI.version = 2.08; // 2nd generation blocking at no extra cost!

// override these in your code to change the default behavior and style
$.blockUI.defaults = {
    // message displayed when blocking (use null for no message)
    message:  '<h1>Please wait...</h1>',
    
    // styles for the message when blocking; if you wish to disable
    // these and use an external stylesheet then do this in your code:
    // $.blockUI.defaults.css = {};
    css: { 
        padding:        0,
        margin:         0,
        width:          '30%', 
        top:            '40%', 
        left:           '35%', 
        textAlign:      'center', 
        color:          '#000', 
        border:         '3px solid #aaa',
        backgroundColor:'#fff',
        cursor:         'wait'
    },
    
    // styles for the overlay
    overlayCSS:  { 
        backgroundColor:'#000', 
        opacity:        '0.6' 
    },
    
    // z-index for the blocking overlay
    baseZ: 1000,
    
    // set these to true to have the message automatically centered
    centerX: true, // <-- only effects element blocking (page block controlled via css above)
    centerY: true,
    
    // allow body element to be stetched in ie6; this makes blocking look better
    // on "short" pages.  disable if you wish to prevent changes to the body height
    allowBodyStretch: true,
    
    // be default blockUI will supress tab navigation from leaving blocking content;
    constrainTabKey: true,
    
    // fadeOut time in millis; set to 0 to disable fadeout on unblock
    fadeOut:  400,
    
    // if true, focus will be placed in the first available input field when
    // page blocking
    focusInput: true,
    
    // suppresses the use of overlay styles on FF/Linux (due to performance issues with opacity)
    applyPlatformOpacityRules: true,
    
    // callback method invoked when unblocking has completed; the callback is
    // passed the element that has been unblocked (which is the window object for page
    // blocks) and the options that were passed to the unblock call:
    //     onUnblock(element, options)
    onUnblock: null
};

// private data and functions follow...

var ie6 = $.browser.msie && /MSIE 6.0/.test(navigator.userAgent);
var pageBlock = null;
var pageBlockEls = [];

function install(el, opts) {
    var full = (el == window);
    var msg = opts && opts.message !== undefined ? opts.message : undefined;
    opts = $.extend({}, $.blockUI.defaults, opts || {});
    opts.overlayCSS = $.extend({}, $.blockUI.defaults.overlayCSS, opts.overlayCSS || {});
    var css = $.extend({}, $.blockUI.defaults.css, opts.css || {});
    msg = msg === undefined ? opts.message : msg;

    // remove the current block (if there is one)
    if (full && pageBlock) 
        remove(window, {fadeOut:0}); 
    
    // if an existing element is being used as the blocking content then we capture
    // its current place in the DOM (and current display style) so we can restore
    // it when we unblock
    if (msg && typeof msg != 'string' && (msg.parentNode || msg.jquery)) {
        var node = msg.jquery ? msg[0] : msg;
        var data = {};
        $(el).data('blockUI.history', data);
        data.el = node;
        data.parent = node.parentNode;
        data.display = node.style.display;
        data.position = node.style.position;
        data.parent.removeChild(node);
    }
    
    var z = opts.baseZ;
    
    // blockUI uses 3 layers for blocking, for simplicity they are all used on every platform;
    // layer1 is the iframe layer which is used to supress bleed through of underlying content
    // layer2 is the overlay layer which has opacity and a wait cursor
    // layer3 is the message content that is displayed while blocking
    
    var lyr1 = ($.browser.msie) ? $('<iframe class="blockUI" style="z-index:'+ z++ +';border:none;margin:0;padding:0;position:absolute;width:100%;height:100%;top:0;left:0" src="javascript:false;"></iframe>')
                                : $('<div class="blockUI" style="display:none"></div>');
    var lyr2 = $('<div class="blockUI" style="z-index:'+ z++ +';cursor:wait;border:none;margin:0;padding:0;width:100%;height:100%;top:0;left:0"></div>');
    var lyr3 = full ? $('<div class="blockUI blockMsg blockPage" style="z-index:'+z+';position:fixed"></div>')
                    : $('<div class="blockUI blockMsg blockElement" style="z-index:'+z+';display:none;position:absolute"></div>');

    // if we have a message, style it
    if (msg) 
        lyr3.css(css);

    // style the overlay
    if (!opts.applyPlatformOpacityRules || !($.browser.mozilla && /Linux/.test(navigator.platform))) 
        lyr2.css(opts.overlayCSS);
    lyr2.css('position', full ? 'fixed' : 'absolute');
    
    // make iframe layer transparent in IE
    if ($.browser.msie) 
        lyr1.css('opacity','0.0');

    $([lyr1[0],lyr2[0],lyr3[0]]).appendTo(full ? 'body' : el);
    
    // ie7 must use absolute positioning in quirks mode and to account for activex issues (when scrolling)
    var expr = $.browser.msie && (!$.boxModel || $('object,embed', full ? null : el).length > 0);
    if (ie6 || expr) {
        // give body 100% height
        if (full && opts.allowBodyStretch && $.boxModel)
            $('html,body').css('height','100%');

        // fix ie6 issue when blocked element has a border width
        if ((ie6 || !$.boxModel) && !full) {
            var t = sz(el,'borderTopWidth'), l = sz(el,'borderLeftWidth');
            var fixT = t ? '(0 - '+t+')' : 0;
            var fixL = l ? '(0 - '+l+')' : 0;
        }

        // simulate fixed position
        $.each([lyr1,lyr2,lyr3], function(i,o) {
            var s = o[0].style;
            s.position = 'absolute';
            if (i < 2) {
                full ? s.setExpression('height','document.body.scrollHeight > document.body.offsetHeight ? document.body.scrollHeight : document.body.offsetHeight + "px"')
                     : s.setExpression('height','this.parentNode.offsetHeight + "px"');
                full ? s.setExpression('width','jQuery.boxModel && document.documentElement.clientWidth || document.body.clientWidth + "px"')
                     : s.setExpression('width','this.parentNode.offsetWidth + "px"');
                if (fixL) s.setExpression('left', fixL);
                if (fixT) s.setExpression('top', fixT);
            }
            else if (opts.centerY) {
                if (full) s.setExpression('top','(document.documentElement.clientHeight || document.body.clientHeight) / 2 - (this.offsetHeight / 2) + (blah = document.documentElement.scrollTop ? document.documentElement.scrollTop : document.body.scrollTop) + "px"');
                s.marginTop = 0;
            }
        });
    }
    
    // show the message
    lyr3.append(msg).show();
    if (msg && (msg.jquery || msg.nodeType))
        $(msg).show();

    // bind key and mouse events
    bind(1, el, opts);
        
	if (full) {
		pageBlock = lyr3[0];
		pageBlockEls = $(':input:enabled:visible',pageBlock);
		if (opts.focusInput)
			setTimeout(focus, 20);
	} else {
        center(lyr3[0], opts.centerX, opts.centerY);
	}
};

// remove the block
function remove(el, opts) {
    var full = el == window;
    var data = $(el).data('blockUI.history');
    opts = $.extend({}, $.blockUI.defaults, opts || {});
    bind(0, el, opts); // unbind events
    var els = full ? $('body').children().filter('.blockUI') : $('.blockUI', el);
    
    if (full) 
        pageBlock = pageBlockEls = null;

    if (opts.fadeOut) {
        els.fadeOut(opts.fadeOut);
        setTimeout(function() { reset(els,data,opts,el); }, opts.fadeOut);
    } else {
        reset(els, data, opts, el);
	}
};

// move blocking element back into the DOM where it started
function reset(els,data,opts,el) {
    els.each(function(i,o) {
        // remove via DOM calls so we don't lose event handlers
        if (this.parentNode) 
            this.parentNode.removeChild(this);
    });
    if (data && data.el) {
        data.el.style.display = data.display;
        data.el.style.position = data.position;
        data.parent.appendChild(data.el);
        $(data.el).removeData('blockUI.history');
    }
    if (typeof opts.onUnblock == 'function')
        opts.onUnblock(el,opts);
};

// bind/unbind the handler
function bind(b, el, opts) {
    var full = el == window, $el = $(el);
    
    // don't bother unbinding if there is nothing to unbind
    if (!b && (full && !pageBlock || !full && !$el.data('blockUI.isBlocked'))) 
        return;
    if (!full) 
        $el.data('blockUI.isBlocked', b);
        
    // bind anchors and inputs for mouse and key events
    var events = 'mousedown mouseup keydown keypress click';
    b ? $(document).bind(events, opts, handler) : $(document).unbind(events, handler);

// former impl...
//    var $e = $('a,:input');
//    b ? $e.bind(events, opts, handler) : $e.unbind(events, handler);
};

// event handler to suppress keyboard/mouse events when blocking
function handler(e) {
    // allow tab navigation (conditionally)
    if (e.keyCode && e.keyCode == 9) {
        if (pageBlock && e.data.constrainTabKey) {
            var els = pageBlockEls;
            var fwd = !e.shiftKey && e.target == els[els.length-1];
            var back = e.shiftKey && e.target == els[0];
            if (fwd || back) {
                setTimeout(function(){focus(back)},10);
                return false;
            }
        }
    }
    // allow events within the message content
    if ($(e.target).parents('div.blockMsg').length > 0)
        return true;
        
    // allow events for content that is not being blocked
    return $(e.target).parents().children().filter('div.blockUI').length == 0;
};

function focus(back) {
    if (!pageBlockEls) 
        return;
    var e = pageBlockEls[back===true ? pageBlockEls.length-1 : 0];
    if (e) 
        e.focus();
};

function center(el, x, y) {
    var p = el.parentNode, s = el.style;
    var l = ((p.offsetWidth - el.offsetWidth)/2) - sz(p,'borderLeftWidth');
    var t = ((p.offsetHeight - el.offsetHeight)/2) - sz(p,'borderTopWidth');
    if (x) s.left = l > 0 ? (l+'px') : '0';
    if (y) s.top  = t > 0 ? (t+'px') : '0';
};

function sz(el, p) { 
    return parseInt($.css(el,p))||0; 
};

})(jQuery);

function client_height_adjust () {
	if (typeof(window.innerWidth) == 'number') { //not IE
		return 20;
	} else {
		return 20;
	}
}

function forgotPasswordAction() {
	jQuery("#fpContent").css('display','');
	jQuery("#fpLink").css('display','none');
	realign_fancybox("login_table");
}

function forgotPasswordAction_login() {
	jQuery("#fpContent").css('display','');
	jQuery("#fpLink").css('display','none');
	realign_fancybox_login("login_table_login");
}

function set_fancybox_position () {
	var boxTopValue = 0, boxLeftValue = 0;
	//Get the screen height and width
	var docH = jQuery(document).height();
	var winW = jQuery(window).width();
	var winH = jQuery(window).height();
	
	boxTopValue = winH/2 - jQuery('#fancy_content').height()/2 - 30;
	boxLeftValue = winW/2 - 245;
	
	jQuery("#fancy_box").css({'display':'block', 'left':boxLeftValue, 'visibility':'visible', 'top':boxTopValue});
	jQuery("#fancy_box_Bg").css({'display':'block', 'position':'absolute', 'width':winW, 'height':docH, 'visibility':'visible'});
	
	if (typeof(window.innerWidth) == 'number') {
		jQuery("#fancy_box").css({'position':'fixed'});
	}
}

function set_theLayer_position() {
	var boxTopValue = 0, boxLeftValue = 0;
	var docH = jQuery(document).height();
	var winW = jQuery(window).width();
	
	boxTopValue = (jQuery(window).height()/2) - (jQuery('#theLayer').height()/2);
	if (navigator.userAgent.toLowerCase().indexOf('msie') > -1 && boxTopValue > 0) {
		boxTopValue = boxTopValue - 50;
	}
	
	boxLeftValue = (winW/2) - (jQuery('#theLayer table').width()/2);
	
	jQuery("#theLayer").css({ 'position':'fixed', 'display':'block', 'left':boxLeftValue, 'visibility':'visible', 'top':boxTopValue});
	jQuery("#theLayer_Bg").css({'display':'block', 'position':'absolute', 'width':winW, 'height':docH, 'visibility':'visible'});
}

function realign_fancybox (table_id) {
	if(jQuery("#fancy_box").css('display') == 'block') {
		jQuery("#fancy_box").css('height',jQuery("#"+table_id).height()+client_height_adjust());
		
		if (jQuery("#"+table_id).width() > jQuery("#fancy_box").width()) {
			jQuery("#fancy_box").css('width',jQuery("#"+table_id).width()+10);
		}
		
		var boxTopValue = 0, boxLeftValue = 0;
		var docH = jQuery(document).height();
		var winW = jQuery(window).width();
		var winH = jQuery(window).height();
		
		boxTopValue = winH/2 - jQuery('#fancy_content').height()/2 - 30;
		boxLeftValue = winW/2 - 245;
		
		jQuery("#fancy_box").css({'left':boxLeftValue, 'top':boxTopValue});
		jQuery("#fancy_box_Bg").css({'width':winW, 'height':docH});
		
		if(typeof(window.innerWidth) != 'number'){
			var scrollWinH = (jQuery(window).height()/2) - 222 + jQuery(this).scrollTop();	
			jQuery("#fancy_box").css({top:scrollWinH+"px"});
		}
	}
}

function hideFancyBox() {
	jQuery('#footerBar').css('display', 'block');
	jQuery("#fancy_close").css('display','none');
	jQuery("#fancy_box").css({'display':'none','visibility':'hidden'});
	jQuery("#fancy_box_Bg").css({'display':'none','visibility':'hidden'});
	jQuery("body").css('overflow','');
}

function set_fancybox_position_login () {
	var boxTopValue = 0, boxLeftValue = 0;
	//Get the screen height and width
	var docH = jQuery(document).height();
	var winW = jQuery(window).width();
	var winH = jQuery(window).height();
	
	boxTopValue = winH/2 - jQuery('#fancy_content_login').height()/2 - 30;
	boxLeftValue = winW/2 - 245;
	
	jQuery("#fancy_box_login").css({'display':'block', 'left':boxLeftValue, 'visibility':'visible', 'top':boxTopValue});
	jQuery("#fancy_box_Bg").css({'display':'block', 'position':'absolute', 'width':winW, 'height':docH, 'visibility':'visible'});
	
	if (typeof(window.innerWidth) == 'number') {
		jQuery("#fancy_box_login").css({'position':'fixed'});
	}
}

function realign_fancybox_login (table_id) {
	if(jQuery("#fancy_box_login").css('display') == 'block') {
		jQuery("#fancy_box_login").css('height',jQuery("#"+table_id).height()+client_height_adjust());
		
		var boxTopValue = 0, boxLeftValue = 0;
		var docH = jQuery(document).height();
		var winW = jQuery(window).width();
		var winH = jQuery(window).height();
		
		boxTopValue = winH/2 - jQuery('#fancy_content_login').height()/2 - 30;
		boxLeftValue = winW/2 - 245;
		
		jQuery("#fancy_box_login").css({'left':boxLeftValue, 'top':boxTopValue});
		jQuery("#fancy_box_Bg").css({'width':winW, 'height':docH});
	}
}

function hideFancyBox_login() {
	jQuery("#fancy_box_login").css({'display':'none','visibility':'hidden'});
	jQuery("#fancy_box_Bg").css({'display':'none','visibility':'hidden'});
	jQuery("body").css('overflow','');
}

function preSubmitRegionalForm() {
	var no_error = true;
	
	if (jQuery("#div_language").html() == config.loadingText) {
		no_error = false;
	}
	
	if (jQuery("#div_currency").html() == config.loadingText) {
		no_error = false;
	}
	
	if (no_error) {
		document.getElementById('regionalForm').submit();
	}
}
/*
function generateRegionalContent() {
	var pop_out;
	
	jQuery("#footerBar").css('display', 'none');
	set_fancybox_position();
	
	jQuery.ajax({
		type: "post",
		url: config.generateRegionalBox_ajaj_url,
		data: 'regional_action=preload',
		dataType: 'json',
		beforeSend:  function() {
			pop_out = '<table cellspacing="0" cellpadding="0" width="100%"><tr><td>';
			pop_out += '<table id="regional_table" width="100%" bgcolor="#ffffff" cellspacing="2" cellpadding="10" style="border: 5px solid white">';
			pop_out += '<tr><td align="center" style="text-align:center;"><img src="/images/loading.gif" width="20" height="20"></td></tr>';
			pop_out += '</table>';
			pop_out += '</td></tr></table>';
			
			jQuery("#fancy_content").html(pop_out);
			realign_fancybox("regional_table");
		},
		success: function(data){
			if(config.cookies_enabled == "1") {jQuery("#fancy_close").css('display','block');}
			pop_out = '<form id="regionalForm" name="regionalForm" method="post" action="'+config.regionalFormAction+'">';
	  		pop_out += '<table id="regional_table" border="0" cellspacing="5" cellpadding="3" width="100%" style="background-color:#ffffff;padding:5px 12px;">';
			pop_out += '<tr><td colspan="2" class="footerPopupTitle"><b class="largeFont">'+config.regionalTitle+'</b></td></tr>';
			pop_out += '<tr><td class="smallText" colspan="2"><div style="padding-top: 3px; margin-top: 0px;" class="solidGreyLine"><!-- --></div></td></tr>';
			pop_out += '<tr><td class="smallText"><font class="largeText"><b>'+config.entryCountry+' </b></font></td>';
			pop_out += '<td>';
			
			if (data.gst != '') {
				pop_out += data.gst;
			} else if (data.country.length > 0) {
				pop_out += '<select id="id_country" name="RegionalSet[loc_country]">';
				var selected_country = '';
				jQuery.each(data.country, function(key, val) {
					selected_country = config.defaultCountry == val.id ? "selected='selected'" : '';
					pop_out += '<option value="'+val.id+'" '+selected_country+'>'+val.text+'</option>';
				});
				pop_out += '</select>';
			} else {
				pop_out += '<select><option value="">Not found.</option></select>';
			}
			
			pop_out += '</td></tr>';
			pop_out += '<tr><td class="smallText"><font class="largeText"><b>'+config.entryCurrency+' </b></font></td>';
			pop_out += '<td><div id="div_currency"></div></td></tr>';
			pop_out += '<tr><td class="smallText" colspan="2"><div style="padding-top: 5px; margin-top: 0px;" class="dottedLine"><!-- --></div></td></tr>';
			pop_out += '<tr><td class="smallText" valign="top"><font class="largeText"><b>'+config.entryLanguage+' </b></font></td>';
			pop_out += '<td><div id="div_language"></div></td></tr>';
			pop_out += '<tr><td class="smallText" colspan="2"><div class="dottedLine" style="padding-top:5px;margin-top:0px;"><!-- --></div></td></tr>';
			pop_out += '<tr>';
			pop_out += '	<td align="right" colspan="3">';
			pop_out += '		<table width="100%" border="0" cellspacing="0" cellpadding="0">';
			pop_out += '			<tr>';
			pop_out += '				<td align="right" style="padding:2px 10px 10px 10px;font-size:12px;text-align:right;">'+config.regionalNote+'</td>';
			pop_out += '				<td style="padding:2px 0px 10px 0px;" width="80">';
			pop_out += '					<div id="home" class="ashGreybutton">';
			pop_out += '						<a href="javascript:preSubmitRegionalForm();">';
			pop_out += '							<span>';
			pop_out += '								<font>'+config.actionSave+'</font>';
			pop_out += '							</span>';
			pop_out += '						</a>';
			pop_out += '					</div>';
			pop_out += '				</td>';
			pop_out += '			</tr>';
			pop_out += '		</table>';
			pop_out += '	</td>';
			pop_out += '</tr>';
			pop_out += '</table></form>';
			
			jQuery("#fancy_content").html(pop_out);
			realign_fancybox("regional_table");
			
			jQuery("#id_country").change(function () {
				jQuery("#div_language, #div_currency").html(config.loadingText);
				realign_fancybox("regional_table");
				
				jQuery.ajax({
					type: "post",
					url: config.generateRegionalBox_ajax_url,
					data: {'regional_action':'getRegionalContent', 'country_ajx':jQuery(this).val()},
					dataType: 'xml',
					success: function(xml){
						if (jQuery(xml).find('region_currency').length > 0) { // GST valid
							jQuery(xml).find("region_currency").each(function(){
								pop_out = '<input type="hidden" name="RegionalSet[currency]" value="'+jQuery(this).attr('id')+'">'+jQuery(this).text();
							});
						} else {
							pop_out = '<select name="RegionalSet[currency]">';
							if (jQuery(xml).find('currency').length > 0) {
								jQuery(xml).find("currency").each(function(){
									pop_out += '<option value="'+jQuery(this).attr('id')+'" '+jQuery(this).attr('selected')+'>'+jQuery(this).text()+'</option>\n';
								});
							} else {
								pop_out += '<option value="">Not found.</option>';
							}
							pop_out += '</select>';
						}
						
						jQuery("#div_currency").html(pop_out);
						
						pop_out = '';
						if (jQuery(xml).find('language').length > 0) {
							jQuery(xml).find("language").each(function(){
								pop_out += '<input id="'+jQuery(this).attr('id')+'" type="radio" name="RegionalSet[language]" value="'+jQuery(this).attr('id')+'" '+jQuery(this).attr('selected')+'><label for="'+jQuery(this).attr('id')+'">'+jQuery(this).text()+'</label><br>';
							});
						}
						
						jQuery("#div_language").html(pop_out);
						realign_fancybox("regional_table");
					}
				});
			}).change();
		}
	});
}
*/
function generateRegionalContent() {
	show_fancybox('region_popup_box');
	
	jQuery('#footer_local_submit').attr('href', 'javascript:void(0);');
	jQuery("#regionalForm_footer").attr('action', config.regionalFormAction);
	//jQuery("#"+id+"_popup_box").css('width', '450px');
	
	jQuery.post(config.generateRegionalBox_ajaj_url, { regional_action: "preload" }, function(data) {
		if (data.gst != '') {
			var country_html = data.gst;
		} else {
			var country_html = '<select id="id_country" name="RegionalSet[loc_country]" onChange="set_language(this)">';
			
			var selected_country = '';
			jQuery.each(data.country, function(key, val) {
				selected_country = config.defaultCountry == val.id ? "selected='selected'" : '';
				country_html += '<option value="'+val.id+'" '+selected_country+'>'+val.text+'</option>';
			});
			country_html += '</select>';
		}
		
		jQuery("#footer_local_country").html(country_html);
		jQuery('#footer_local_submit').attr('href', 'javascript:document.regionalForm_footer.submit();');
	}, "json");
}

function generateLoginContent() {
	var pop_out;
	
	set_fancybox_position_login();
	
	realign_fancybox_login("login_table_login");
	
	jQuery("#fancy_close_login").pngfix();
	
	jQuery('div#fancy_content_login #loginContent #entry_email').focus();
	
	jQuery("div#fancy_content_login #loginContent").click(function() {
		jQuery("#act_login").attr('checked', true);
	});
	
	jQuery("#fpContent").click(function() {
		jQuery("#act_password").attr('checked', true);
	});
}

function blackbox_tooltip(content, location_id, css_position, css_bottom, css_top, css_left, css_right) {
	var css_position = css_position === undefined || css_position == '' ? '' : css_position;
	
	if (css_top === undefined || css_top == '') {
		var css_bottom = css_bottom === undefined || css_bottom == '' ? '' : css_bottom+'px';
	} else {
		var css_bottom = '';
	}
	
	if (css_bottom == '') {
		var css_top = css_top === undefined || css_top == '' ? (jQuery("#"+location_id).offset().top - 63)+'px' : css_top+'px';	
	} else {
		var css_top = '';
	}
	
	if (css_right === undefined || css_right == '') {
		var css_left = css_left === undefined || css_left == '' ? (jQuery("#"+location_id).offset().left - 20)+'px' : css_left+'px';
	} else {
		var css_left = '';
	}
	
	if (css_left == '') {
		var css_right = css_right === undefined || css_right == '' ? '' : css_right+'px';
	} else {
		var css_right = '';
	}
	
	//jQuery(".popupBubble").css({'position': css_position, 'bottom': css_bottom, 'top': css_top, 'left': css_left, 'right': css_right});
	// Fix IE
	jQuery(".popupBubble").attr('style', 'position:'+css_position+';bottom:'+css_bottom+';top:'+css_top+';left:'+css_left+';right:'+css_right);
	jQuery('#blackbox_tooptips_content').html(content);
	
	jQuery('#blackbox_tooptips').show();
	jQuery('.popupBubble').show();
}

function bottom_bar () {
	var scroll_position = (jQuery(window).height() + jQuery(window).scrollTop()) - parseInt(jQuery('#footerBar').css("height"));
	jQuery('#footerBar').animate({'top':scroll_position+"px" },{queue: false, duration: 1});
	scroll_position = '';
}

function submit_reset_pw_form () {
    jQuery.ajax({
        type: "post",
        url: 'customer_xmlhttp.php?action=reset_password',
        data: jQuery("#reset_password_form").serialize(),
        dataType: 'xml',
        beforeSend:  function() {jQuery('#reset_password_form div.error_msg').html('')},
        error: function(){jQuery('#reset_password_form div.error_msg').html('Please try again...')},
        success: function(xml) {
            jQuery(xml).find('response').each(function(){
				var status = jQuery("status", this).text();
				if (status == 'success') {
					window.location.reload();
				} else {
					jQuery('#reset_password_form div.error_msg').html(status);
				}
			});
        }
    });
}

jQuery(document).ready(function(){
    jQuery("#select_link_zones, #error_region_link").bind("click", function(e){
		generateRegionalContent();
    });
    
	jQuery("#fancy_login_box").bind("click", function(e){
		generateLoginContent();
	});
	
    if (config.splash_enabled != 0) {
        custom_general_popup_box('<div class="splash '+config.splash_enabled+'" style="cursor: pointer;padding:0px;"><div class="splash_'+config.splash_enabled+'_img" style="margin: 0px auto;"></div></div>', '', '970px', {padding:'0px'});
    }
    
	if (config.cookies_enabled == "1") {
		jQuery("#fancy_close").pngfix();
		
		jQuery("#fancy_close").bind("click", function(e){
			hideFancyBox();			
		});
		
		if (config.special_notice_cookies_enabled == 0) {
			var coordinate_left = jQuery('#footer_local_td').offset().left - 25;
			jQuery('#ogm_special_notice').css('left', coordinate_left);
			jQuery('#ogm_special_notice').css('display', 'block');
			
		}
        
        if (config.reset_password != "0" && window.location.pathname!='/gift_card.php' && window.location.pathname!='/logoff.php') {
            show_fancybox('reset_password_popup_box');
        }else if (config.reset_phone == '1'){
            show_fancybox('reset_phone_popup_box');
        }
	} else {
		generateRegionalContent();
	}
});

/* Count down box */
function calcage(secs, num1, num2) {
  var s = Math.floor(secs/num1);
  s = ((num2==0) ? (s>99 ? 99 : s) : (s%num2)).toString();
  if (s.length<2){s="0"+s;}
  return s;
}
function countdown(cids) {
	var cid,secs;
	for (x in cids) {
		cid = cids[x];
		secs = parseInt(jQuery("#cbd_"+cid).val(),10)-1;
		if (secs < 0){return window.location.reload();}
		jQuery("#cbd_"+cid).val(secs);
		jQuery("#cb_"+cid+">div>div.md>span.digit").html(calcage(secs,86400,0));
		jQuery("#cb_"+cid+">div>div.mh>span.digit").html(calcage(secs,3600,24));
		jQuery("#cb_"+cid+">div>div.mm>span.digit").html(calcage(secs,60,60));
		jQuery("#cb_"+cid+">div>div.ms>span.digit").html(calcage(secs,1,60));
	}
}
/* Count down box */