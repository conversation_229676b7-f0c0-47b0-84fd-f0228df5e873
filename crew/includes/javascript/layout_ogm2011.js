jQuery(document).ready(function(){
	/* Tab Handler */
	jQuery('.tabItem').each(function(index, ele){
		var selected_id;
		jQuery('#'+this.id).bind('click', function(e){
			selected_id = this.id;
			jQuery('#'+selected_id).parent().children('div[id^="tab"][id!="'+selected_id+'"]').children('div[class*="_selected"]').each(function(i){
				jQuery(this).removeClass(jQuery(this).attr('class').split(' ').slice(-1).toString());
			});
			jQuery('#'+selected_id).children('div:not([class*="_selected"])').each(function(i){
				jQuery(this).addClass(jQuery(this).attr("class")+'_selected');
			});
			
			jQuery('#'+selected_id+'_content').parent().children('div[id^="tab"][id*="_content"][id!="'+selected_id+'_content"]').hide();
			jQuery('#'+selected_id+'_content').show();
			html_content_generator(selected_id);
	    });
	});
	/* Tab Handler */
	
	/* Vertical Menu Handler */
	/* --- Expand or Collapse ---  */
	jQuery('.vmenu>.vm_header').each(function(index, ele){
		jQuery(this).bind('click', function(e){
			var selected_id = this.id;
			
			if (jQuery(this).hasClass('vm_header_selected')) {
				jQuery(this).removeClass('vm_header_selected');
			} else {
				jQuery(this).not('[class~="vm_header_selected"]').addClass('vm_header_selected');
			}
			jQuery(this).next('[class~="vm_content"]').toggle();
		 });
	});
	
	/* --- Expand 1 Collapse All --- */
	jQuery('.vmenu2>.vm_header').each(function(index, ele){
		jQuery(this).bind('click', function(e){
			var selected_id = this.id;
			
			if (jQuery(this).hasClass('vm_header_selected')) {
				jQuery(this).removeClass('vm_header_selected');
			} else {
				jQuery(this).parent().children('li[class~="vm_header_selected"]').each(function(i){
					jQuery(this).removeClass('vm_header_selected');
					jQuery(this).next('[class~="vm_content"]').toggle();
			    });
			    
			    jQuery(this).not('[class~="vm_header_selected"]').addClass('vm_header_selected');
			}
			jQuery(this).next('[class~="vm_content"]').toggle();
		 });
	});
	/* Vertical Menu Handler */
	
	/* POP UP */
	jQuery('#password_forgotten_link').bind('click', function(e){
		popup_box_switch('login_popup_box', 'forgot_password_popup_box');
	});
	jQuery('#login_return_link').bind('click', function(e){
		popup_box_switch('forgot_password_popup_box', 'login_popup_box');
	});
	jQuery('div.popup_close_button').bind('click', function(e){
        var m_div_obj=jQuery(this).parent(),
            parent_id=m_div_obj.attr('id');
        if(parent_id=='general_popup_box'){
            m_div_obj.css({width:'475px'});
            m_div_obj.find('div.popup_close_button, div[class^="nrc_"]').show();
            m_div_obj.find('div.header').remove();
            m_div_obj.find('#general_content').removeAttr('style').css({padding:'40px'}).html('');            
        }
		hide_fancybox(parent_id);
	});
    
    // close general popup box on screen click 
    if(config.splash_enabled!=0){
        jQuery('div[id="general_popup_box"]:visible #general_content>div.splash').parents('#general_popup_box').bind('click', function(){
            jQuery.ajax({
                type: "post",
                url: 'customer_xmlhttp.php?action=clear_splash',
                dataType: 'xml',
                error: function(){alert('Please try again...')},
                success: function() {
                    jQuery('div[id="general_popup_box"]:visible>div.popup_close_button').click();
                    config.splash_enabled=0;
                }
            });
        });
    }
	/* POP UP */
	
	/* Custom Drop Down */
	jQuery(".customdropdown").each(function(index, ele){
		var bid = jQuery(this).attr("id");
		var gid = jQuery(this).attr("class").split(' ').slice(-1);
		
		jQuery("#"+bid+" dt a").click(function() {
            jQuery("dl[class~=customdropdown][id!="+bid+"] dd ul").hide();
            jQuery("#"+bid+" dd ul").toggle();
        });
        
        jQuery("#"+bid+" dd ul li a").click(function() {
            var text = jQuery(this).html();
            jQuery("#"+bid+" dt a").addClass("selected");
            jQuery("#"+bid+" dt a span.sel").html(text);
            jQuery("#"+bid+" dd ul").hide();
			
			cdd_trigger_action('selected', gid, bid, '');
        });
        
        jQuery(document).bind('click', function(e) {
            var clicked = $(e.target);
            if (! clicked.parents().hasClass("customdropdown")) {
                jQuery("#"+bid+" dd ul").hide();
            }
        });
	});
	
	jQuery('.customdropdown2').each(function(index, ele){
		var bid = jQuery(this).attr("id");
		var gid = jQuery(this).attr("class").split(' ').slice(-1);
		
		jQuery('#'+bid+' dt a span[class~="sel"]').click(function() {
            jQuery('dl[class~=customdropdown2][id!='+bid+'] dd ul').hide();
            jQuery('#'+bid+' dd ul').toggle();
        });
        
        jQuery('#'+bid+' dt a span[class~="arrow"]').click(function() {
        	if(jQuery(this).is('[class~="cancel"]')) {
        		cdd_trigger_cancel_action(gid, bid, true);
        	} else {
        		jQuery('dl[class~=customdropdown2][id!='+bid+'] dd ul').hide();
            	jQuery('#'+bid+' dd ul').toggle();
        	}
        });
        
        jQuery("#"+bid+" dd ul li a").click(function() {
    		var text = jQuery(this).html();
            jQuery("#"+bid+" dt a span.sel").html(text);
            
    		if(jQuery('#'+bid+' dt a span.sel span.value').html() == '0') {
            	jQuery('#'+bid+' dt a').removeClass('selected');
            	jQuery('#'+bid+' dt a span[class="arrow"]').removeClass('cancel');
            } else {
            	jQuery('#'+bid+' dt a').addClass('selected');
            	jQuery('#'+bid+' dt a span[class="arrow"]').not('[class="cancel"]').addClass('cancel');
            }
            
            jQuery("#"+bid+" dd ul").hide();
            cdd_trigger_action('selected', gid, bid, '');
        });
        
        jQuery(document).bind('click', function(e) {
            var clicked = jQuery(e.target);
            if (! clicked.parents().hasClass("customdropdown2")) {
                jQuery("#"+bid+" dd ul").hide();
            }
        });
	});
	/* Custom Drop Down */
    
    jQuery('.placeholder').each(function(i) {
        var item = jQuery(this);
        var text = item.attr('rel');
        var form = item.parents('form:first');
        
        if (item.val() === '') {
            item.val(text);
            item.css('color', '#888');
        }
        item.bind('focus.placeholder', function(event) {
            if (item.val() === text)
                item.val('');
            item.css('color', '');
        });
        
        item.bind('blur.placeholder', function(event) {
            if (item.val() === '') {
                item.val(text);
                item.css('color', '#888');
            }
        });
        
        form.bind("submit.placeholder", function(event) {
            if (item.val() === text)
            item.val("");
        });
    });
    
    if (jQuery(".ogm_tooltips").length>0) {
        jQuery(".ogm_tooltips").ogm_tooltips( {
            method : 'mouseover',
            image_up : '../../images/triangle_face_up.gif',
            image_close : '../../images/icons/cancel-round.png'
        });
    }

    if (jQuery(".ogm_dtu_tooltips").length>0) {
        jQuery(".ogm_dtu_tooltips").ogm_tooltips( {
            method : 'click',
            image_up : '../../images/triangle_face_up.gif',
            image_close : '../../images/icons/cancel-round.png',
            width : '160'
        });
    }
    
    jQuery("#noticeBar div.hide-icon").bind('click', function(e) {        
        jQuery("#noticeBar").hide();
    });
});

/* Tab Content Handler */
function html_content_generator (content_id) {
	var suffix_id = content_id.split("_")[1];
	
	if (suffix_id != undefined){
		switch (suffix_id) {
			case '':
				break;
			default:
				break;
		}
	}
}

function row_tab (row_obj, classname, action) {
	if (action =="Over") {
		jQuery(row_obj).removeClass();
		jQuery(row_obj).addClass(classname+action);
	} else {
		jQuery(row_obj).removeClass();
		jQuery(row_obj).addClass(classname+action);
	}
}
/* Tab Content Handler */


/* POP UP */
function show_fancybox (div_id) {
	var boxTopValue = 0, boxLeftValue = 0, winHAdj = 60, fboxBgColor='#000',
        docH = jQuery(document).height(),
        winW = jQuery(window).width(),
        winH = (window.innerHeight == undefined) ? jQuery(window).height() : window.innerHeight, //Opera return wrong height if use jQuery(window).height();
        gc_obj = jQuery('div[id="general_popup_box"] #general_content>div');
    
    if(gc_obj.hasClass('splash')){
        winHAdj=0;fboxBgColor=gc_obj.hasClass('xmas') ? '#FFF' : fboxBgColor;
        if(div_id == 'general_popup_box') jQuery('div[id="general_popup_box"]').find('.popup_close_button, #general_popup_box_content > div[class^="nrc_"]:not([class*="nrc_mdl"])').css('display','none');
    }else if(jQuery('div[id$="_popup_box"]:visible').length && jQuery('div[id$="_popup_box"]:visible').attr('id')!=div_id){ return;}
    
	boxTopValue = (winH - jQuery('#'+div_id).height())/2 - winHAdj;
	boxLeftValue = (winW-jQuery('#'+div_id).width())/2;
    
	jQuery("#"+div_id).css({'display':'block', 'left':boxLeftValue, 'visibility':'visible', 'top':boxTopValue, 'position':'fixed'});
	jQuery("#fancy_box_Bg").css({'display':'block', 'width':winW, 'height':docH, 'visibility':'visible', 'background-color':fboxBgColor});
	
	if(typeof(window.innerWidth) != 'number'){
		jQuery("#"+div_id).css({'position':'fixed'});
	}
}

function hide_fancybox (div_id) {
	jQuery("#fancy_close").css('display','none');
	jQuery("#"+div_id).css({'display':'none','visibility':'hidden'});
    jQuery("#fancy_box_Bg").css({'background-color':''});
    if(jQuery('div[id$="_popup_box"]:visible').length < 1){
        jQuery("#fancy_box_Bg").css({'display':'none','visibility':'hidden'});
        jQuery("body").css('overflow','');
    }
}

function popup_box_switch (sfrom_div_id, sto_div_id) {
    xdomainLoginPopup('stype=fpop&origin=' + HTTP_ORIGIN_URL, TEXT_ACCOUNT_LOGIN_HEADER);  
}

jQuery(window).bind('resize', function() {
	jQuery('.fancy_box:visible').each(function(i){
		show_fancybox(jQuery(this).attr('id'));
	});
});

/* Search All Game */
function update_fav (pid) {
	jQuery.ajax({
		type: "post",
		url: 'search_all_games_xmlhttp.php',
		data: {'action':'update_fav', 'pid':pid},
		dataType: 'xml',
		beforeSend:  function() {
			blockUI_disable();
		},
		error: function(){
			blockUI_disable('<h1>Error: Please try again later...</h1>');
			setTimeout("jQuery.unblockUI()", 3000);
		},
		success: function(xml) {
			jQuery(xml).find('response').each(function(){
				var result = jQuery("result", this).text();
				var msg = jQuery("message", this).text();
				if (result == '-1') {
					jQuery.unblockUI();
					xdomainLoginPopup('stype=fpop&origin=' + HTTP_ORIGIN_URL, TEXT_ACCOUNT_LOGIN_HEADER);
				} else {
					blockUI_disable(msg);
					jQuery("#"+pid).removeClass('fav_star_selected');
					if (result == '1') jQuery("#"+pid).addClass('fav_star_selected');
					setTimeout("jQuery.unblockUI()", 3000);
				}
			});
		}
	});
}
/* Search All Game */


/* Custom Drop Down */
function cdd_trigger_cancel_action (group_id, dd_id, trigger_action){
	jQuery('#'+dd_id+' dd ul li a').each(function(i){
		if (jQuery(this).children('span[class~="value"]').html() == '0') {
			jQuery("#"+dd_id+" dt a span.sel").html(jQuery(this).html());
			jQuery('#'+dd_id+' dt a').removeClass('selected');
			jQuery('#'+dd_id+' dt a span[class~="cancel"]').removeClass('cancel');
			if (trigger_action)	cdd_trigger_action('cancel', group_id, dd_id, trigger_action);
		}
	});
}

function cdd_trigger_action (act, group_id, dd_id, ext) {
	switch (group_id.toString()) {
		case 'sag':	// Search All Games
			jQuery('#f_fav').val('0');
			jQuery('#fav-star').removeClass('fav_star_selected');
			reload_sag_data();
			break;
		case 'pl': // Product Listing
			load_products_info(jQuery("#"+dd_id).find("dt a span.value").html(), dd_id);
			break;
		default:
			if (act=='cancel'){}else if(act=='selected'){}
			break;
	}
}
/* Custom Drop Down */


/* White Tooltip */
function trigger_whitebox_tooltip(content_id, location_id) {
	var content_html = jQuery('#'+content_id).html();
	whitebox_tooltip(content_html, location_id, 'fixed', 33);
}

function hide_whitebox_tooltip(){
	jQuery('#whitebox_tooltips').hide();
}

function whitebox_tooltip(content, location_id, css_position, css_bottom, css_top, css_left, css_right) {
	var css_position = css_position === undefined || css_position == '' ? '' : css_position;
	
	if (css_top === undefined || css_top == '') {
		var css_bottom = css_bottom === undefined || css_bottom == '' ? '' : css_bottom+'px';
	} else {
		var css_bottom = '';
	}
	
	if (css_bottom == '') {
		var css_top = css_top === undefined || css_top == '' ? (jQuery("#"+location_id).offset().top - 63)+'px' : css_top+'px';	
	} else {
		var css_top = '';
	}
	
	if (css_right === undefined || css_right == '') {
		var css_left = css_left === undefined || css_left == '' ? (jQuery("#"+location_id).offset().left - 16)+'px' : css_left+'px';
	} else {
		var css_left = '';
	}
	
	if (css_left == '') {
		var css_right = css_right === undefined || css_right == '' ? '' : css_right+'px';
	} else {
		var css_right = '';
	}
	
	jQuery("#whitebox_tooltips").attr('style', 'position:'+css_position+';bottom:'+css_bottom+';top:'+css_top+';left:'+css_left+';right:'+css_right);
	jQuery('#whitebox_tooltips_content').html(content);
	jQuery('#whitebox_tooltips').show();
}
/* White Tooltip */

/* Region Fancy POP UP / Black Tooltip / Footer Bar Add To Cart */
function trigger_blackbox_tooltip(content_id, location_id) {
	var content_html = jQuery('#'+content_id).html();
	blackbox_tooltip(content_html, location_id, 'fixed', 35);
}

function hide_blackbox_tooltip(){
	jQuery('#blackbox_tooptips').hide();
	jQuery('.popupBubble').hide();
}

function hide_special_notice() {
	jQuery("#ogm_special_notice").css('display', 'none');
	jQuery.get("/index.php?action=set_special_notice_cookie");
}

function PopUpAddToCart() {
	jQuery("#footer_cart_popup_box").css({'display':'block', 'visibility':'visible'});
	jQuery("#footer_checkout_td").addClass('footer_column_selected');
}

function set_language(obj) {
	jQuery("#div_language, #div_currency").html(config.loadingText);
	realign_fancybox("regional_table");
	jQuery('#footer_local_submit').attr('onClick', '');
	
	jQuery.ajax({
		type: "post",
		url: config.generateRegionalBox_ajax_url,
		data: {'regional_action':'getRegionalContent', 'country_ajx':jQuery(obj).val()},
		dataType: 'xml',
		success: function(xml){
			if (jQuery(xml).find('region_currency').length > 0) { // GST valid
				jQuery(xml).find("region_currency").each(function(){
					pop_out = '<input type="hidden" name="RegionalSet[currency]" value="'+jQuery(this).attr('id')+'">'+jQuery(this).text();
				});
			} else {
				pop_out = '<select name="RegionalSet[currency]">';
				if (jQuery(xml).find('currency').length > 0) {
					jQuery(xml).find("currency").each(function(){
						pop_out += '<option value="'+jQuery(this).attr('id')+'" '+jQuery(this).attr('selected')+' style="width:277px">'+jQuery(this).text()+'</option>\n';
					});
				} else {
					pop_out += '<option value="" style="width:277px">Not found.</option>';
				}
			}
			pop_out += '</select>';
			jQuery("#div_currency").html(pop_out);
			
			pop_out = '<select name="RegionalSet[language]">';
			if (jQuery(xml).find('language').length > 0) {
				jQuery(xml).find("language").each(function(){
					pop_out += '<option value="'+jQuery(this).attr('id')+'" '+jQuery(this).attr('selected')+' style="width:277px">'+jQuery(this).text()+'</option>\n';
				});
			} else {
				pop_out += '<option value="" style="width:277px">Not found.</option>';
			}
			
			jQuery("#div_language").html(pop_out);
			realign_fancybox("regional_table");
			jQuery('#footer_local_submit').attr('href', 'javascript:document.regionalForm_footer.submit();');
		}
	});
}

function PopUpContent(id) {
	if (typeof(minicart_interval) != 'undefined') {
		clearInterval(minicart_interval);
	}
	
	show_fancybox(id);
	
	if (id == 'region_popup_box') {
		jQuery('#footer_local_submit').attr('href', 'javascript:void(0);');
		jQuery("#regionalForm_footer").attr('action', config.regionalFormAction);
		//jQuery("#"+id+"_popup_box").css('width', '450px');
		
		jQuery.post(config.generateRegionalBox_ajaj_url, { regional_action: "preload" }, function(data) {
			if (data.gst != '') {
				var country_html = data.gst;
			} else {
				var country_html = '<select id="id_country" name="RegionalSet[loc_country]" onChange="set_language(this)" style="width:305px">';
				
				var selected_country = '';
				jQuery.each(data.country, function(key, val) {
					selected_country = config.defaultCountry == val.id ? "selected='selected'" : '';
					country_html += '<option value="'+val.id+'" '+selected_country+'>'+val.text+'</option>';
				});
				country_html += '</select>';
			}
			
			jQuery("#footer_local_country").html(country_html);
			jQuery('#footer_local_submit').attr('href', 'javascript:document.regionalForm_footer.submit();');
		}, "json");
	}
}

function hide_footer_popup(id) {
	jQuery("#footer_checkout_td").removeClass('footer_column_selected');
	jQuery("#"+id+"_popup_box").css({'display':'none','visibility':'hidden'});
	clearInterval(minicart_interval);
}
/* Region Fancy POP UP / Footer Bar Tooltip / Footer Bar Add To Cart */

function ask_qna(){
    jQuery.ajax({
        type: "POST",
        url: 'checkout_jsonhttp.php?action=get_qna_content',
        async: false,
        dataType: "json",
        beforeSend: function() {},
        error: function(){
            alert('Please try again.');
        },
        success: function(data){
            var width = data.width!=undefined?data.width:'530px';
            
            if(data.redirect!=undefined){
                document.location.href = data.redirect;
            }else{
                custom_general_popup_box(data.content, data.header, width, {padding:'0px'});
            }
        }
    });
}

function cfm_qna(){
    jQuery.ajax({
        type: "POST",
        url: 'checkout_jsonhttp.php?action=cfm_qna',
        data: jQuery("#ask_qna_form").serialize(),
        async: false,
        dataType: "json",
        beforeSend:  function() {
            jQuery('#ask_qna_form div.error_msg font').html('');
        },
        error: function(){
            alert('Please try again.');
        },
        success: function(data){
            if(data.redirect!=undefined){
                document.location.href = data.redirect;
            }else{
                if(data.err && data.msg!=undefined){
                    jQuery('#ask_qna_form div.error_msg font').html(data.msg);
                } else {
                    hide_fancybox('general_popup_box');
                }
            }
        }
    });
}

function updateDTUextra(obj) {
    var parent_td_obj=jQuery(obj).parents('td.name,td.qty'),
        wvalue=parseInt(parent_td_obj.find('span.ext2').attr('data-default')),
        xvalue=parseInt(jQuery(obj).val()),
        xvalue=!isNaN(xvalue)?xvalue:1,
        dvalue=wvalue*xvalue;
    parent_td_obj.find('span.ext2').html(dvalue);
}

/* Buy Code */
function pfv(obj_id, cfm) {
    jQuery.ajax({
        type: "POST",
        url: 'checkout_jsonhttp.php?action=get_flow',
        data: {index:obj_id, confirmed:(cfm?cfm:0)},
        async: false,
        dataType: "json",
        timeout: 60000,
        beforeSend: function(){
//            blockUI_disable()
        },
		error: function(xhr, errorType, exception){
			blockUI_disable('<h1>'+compile_error_msg(xhr, errorType, exception)+'</h1>');
			setTimeout("jQuery.unblockUI()", 3000);
		},
        success: function(data){
//            jQuery.unblockUI();
//            setTimeout(function(){
                if(data.redirect!=undefined){
                    document.location.href = data.redirect;
                }else if(data.idx!='0' && data.nxt!=undefined){
                    if(data.nxt=='login'){blockUI_disable();document.location.href = LOGIN_URL;}  
                    else if(data.nxt=='sc_choice'){pfv_popup(data)}
                    else if(data.nxt=='confirm_order'){pfv_popup_confirm_order(data.idx)}
                    else if(data.nxt=='ask_password'){pfv_popup(data)}
                    else if(data.nxt=='ask_qna'){pfv_popup(data)}
                }else if(data.msg){alert(data.msg)}
//            }, 500);
        }
    });
}

function custom_general_popup_box(html_content, header_title, width, content_css, htitle_ext) {
    var width=width?width:'450px',
        content_css = content_css?content_css:{},
        header_title_ext = htitle_ext?htitle_ext:'';
    
    if(header_title!=''){
        if(jQuery('#general_popup_box_content div.srcContent>div.header').hasClass('header')){
            jQuery('#general_popup_box_content div.srcContent>div.header>span.hd1').html(header_title);
        }else{
            jQuery('#general_popup_box_content div.srcContent').prepend('<div class="pfv_header header"><span class="hd1">'+header_title+'</span><div class="ext"><span class="hd5"></span></div></div>');   
        }
        jQuery('#general_popup_box_content div.srcContent>div.header>div.ext>span').html(header_title_ext);
    }else{}
    
    jQuery('#general_popup_box').css({width:width});
    jQuery('#general_content').css(content_css).html(html_content);
    show_fancybox('general_popup_box');
}

function pfv_show_dm_info(btn_obj){
    jQuery('table.purchase_confirm_box td.dm div.gshow div.entry input').attr('disabled','disabled');
    jQuery('table.purchase_confirm_box td.dm div.group').removeClass('gshow');
    jQuery('#'+btn_obj.id).parents('div.group').addClass('gshow');
    jQuery('table.purchase_confirm_box td.dm div.gshow div.entry input').removeAttr('disabled');
    jQuery('table.purchase_confirm_box td.dm>div.error_msg').hide().html('');
    
    jQuery('div.dmnote div').removeClass('noteshow');
    jQuery('#note_'+btn_obj.id).addClass('noteshow');
}

function pfv_popup(html_data){
    var width = html_data.width!=undefined?html_data.width:'',
        css = html_data.css_padding!=undefined?{padding: html_data.css_padding}:{padding:'0px'};
    custom_general_popup_box(html_data.content, html_data.header, width, css);
}

function pfv_popup_confirm_order(idx){
    if(jQuery('#'+idx).length){
        var data_obj = jQuery('#data_'+idx),
            buy_qty = data_obj.find('input[name="custom_qty"]').length && parseInt(data_obj.find('input[name="custom_qty"]').val()) > 0 ? data_obj.find('input[name="custom_qty"]').val() : '1',
            pname = data_obj.attr('data-name'),
            pass_mid = data_obj.attr('data-dm'),
            imgLoader = '<div style="padding:10px;"><div class="load_img" style="margin: 0px auto;"></div></div>';
            
        custom_general_popup_box(imgLoader, pname, '680px', {padding:'0px'});
        make_checkout_req(pfv_compile_data(idx, buy_qty, 11, pass_mid));
    }
}

function pfv_cfm_password(idx){
    jQuery('#'+idx+'_form div.error_msg').html('');
    var resp_obj = make_checkout_req(jQuery("#"+idx+"_form").serialize(), 'cfm_password');
    
    if(resp_obj.err && resp_obj.data!=undefined && resp_obj.data.msg!=undefined){
        jQuery('#'+idx+'_form div.error_msg').html(resp_obj.data.msg);
    }
}

function pfv_cfm_coupon(btn_obj){
    var btn_id = btn_obj.id,
        tbl_obj = jQuery('#'+btn_id).parents('table.purchase_confirm_box'),
        idx = tbl_obj.attr('id').replace("tbl_", ""),
        buy_qty = tbl_obj.find('select[name="game_info[qty]"]').val(),
        extra_params=tbl_obj.find('form#coupon_form') ? tbl_obj.find('form#coupon_form').serialize() : '',
        imgLoader = '<div style="padding:10px;"><div class="load_img" style="margin: 0px auto;"></div></div>',
        ot_box = jQuery('#'+btn_id).parents('tr').children('td.ot'),
        resp_obj = {};
    
    ot_box.html(imgLoader);
    pfv_confirm_order_error_handler(idx, resp_obj);
    make_checkout_req(pfv_compile_data(idx, buy_qty, 12, undefined, extra_params));
}

function pfv_confirm_order_error_handler(idx, error_obj){
    var tbl_obj=jQuery('#tbl_'+idx),qty_msg=[],i=0,btn_obj=tbl_obj.find('td.ot>table tr.footer>td.button>div.main_btn'),dots_obj=btn_obj.find('span.dots');
    tbl_obj.find('select[name="game_info[qty]"]').attr('disabled','disabled');
    tbl_obj.find('td.qty>div.error_msg').hide().html('');
    tbl_obj.find('td.dm>div.error_msg').hide().html('');
    
    if(error_obj.err!=undefined){
        tbl_obj.find('select[name="game_info[qty]"]').removeAttr('disabled');
        if(dots_obj.is(":visible")){
            btn_obj.find('span.label').html(btn_obj.find('span.label').attr('data-cfm'));
            window.clearInterval(dots_obj.attr('data-id'));
            dots_obj.hide();
        }
        
        if(error_obj.err){
            if(error_obj.data.msg4qty!=undefined){qty_msg[i++] = error_obj.data.msg4qty;}
            if(error_obj.data.msg4dm!=undefined){tbl_obj.find('td.dm>div.error_msg').show().html(error_obj.data.msg4dm);}
            if(qty_msg.length>0){tbl_obj.find('td.qty>div.error_msg').show().html(qty_msg.join('<br>'));}
        }
    }else{
        var intv,dots='.';
        if(btn_obj.length){
            btn_obj.find('span.label').html(btn_obj.find('span.label').attr('data-load'));
            dots_obj.show().html(dots);
            intv = setInterval(function(){if(dots_obj.html()=='...'){dots='.'}else{dots+='.'}dots_obj.html(dots)}, 800);
            dots_obj.attr('data-id', intv);
        }
    }
}

function pfv_confirm_order_qty(select_obj){
    var sel_id = select_obj.id,
        tbl_obj = jQuery('#'+sel_id).parents('table.purchase_confirm_box'),
        idx = tbl_obj.attr('id').replace("tbl_", ""),
        buy_qty = select_obj.value,
        imgLoader = '<div style="padding:10px;"><div class="load_img" style="margin: 0px auto;"></div></div>',
        ot_box = jQuery('#'+sel_id).parents('tr').children('td.ot'),
        resp_obj = {};
    
    // before
    ot_box.html(imgLoader);
    pfv_confirm_order_error_handler(idx, resp_obj);
    make_checkout_req(pfv_compile_data(idx, buy_qty, 12));
}

function pfv_confirm_order(btn_obj){
    var btn_id=btn_obj.id,
        tbl_obj=jQuery('#'+btn_id).parents('table.purchase_confirm_box'),
        idx=tbl_obj.attr('id').replace("tbl_", ""),
        buy_qty=jQuery('#pfv_qty').val(),
        mid=jQuery('input[name=rd_delivery_mode]:checked').val(),
        extra_params=tbl_obj.find('form#dm_form') ? tbl_obj.find('form#dm_form').serialize() : '',
        resp_obj={};
    
    if (!pfv_isfreezed()){
        if(failed_validate_confirm_order(idx)){
            return;
        }

        pfv_confirm_order_error_handler(idx, resp_obj);
        make_checkout_req(pfv_compile_data(idx, buy_qty, 13, mid, extra_params));
    }
}

function failed_validate_confirm_order(idx){
    var rtn=false,
        tbl_obj=jQuery('#tbl_'+idx),
        fname,fval,fdval,err_msg=[],i=0;
    if(jQuery('#data_'+idx).attr('data-cpt-id')=='0'){
        if(tbl_obj.find('form#dm_form')){
            tbl_obj.find('form#dm_form div.entry:visible input[name^="extra_info"]').each(function(){
               fname=jQuery(this).attr('name').replace('extra_info[','').replace(']','');
               fval=jQuery(this).val();
               fdval=jQuery(this).attr('default_text');
               if(fval==fdval && tbl_obj.find('form#dm_form input[name="err_'+fname+'"]').length){
                   err_msg[i++]= "* "+tbl_obj.find('form#dm_form input[name="err_'+fname+'"]').val();
               }
            });
        }
    }else{
        tbl_obj.find("form#dm_form .dtu_customer_input_game_info").each(function(){
            if(jQuery(this).val()=="" || jQuery(this).val()==jQuery(this).attr("default_text")) {
                err_msg[i++]= "* "+tbl_obj.find('form#dm_form input[name="err_dtu_msg"]').val();
                return false;
            }
        });
    }
    
    if(err_msg.length>0){
        rtn = true;
        tbl_obj.find('td.dm>div.error_msg').show().html(err_msg.join('<br>'));
    }
    return rtn;
}

function pfv_compile_data(idx, buy_qty, confirmed, pass_mid, extra_params){
    var data_obj=jQuery('#data_'+idx),
        pid=data_obj.attr('data-pid'),
        hla=data_obj.attr('data-hla')==undefined?'':data_obj.attr('data-hla'),
        mid=(pass_mid!=undefined?pass_mid:data_obj.attr('data-dm')),
        ctype_id=(data_obj.attr('data-cpt-id')),
        products_bundle=(data_obj.attr('data-products-bundle')),
        buy_now_qty='',
        extra=(extra_params!=undefined&&extra_params!='')?'&'+extra_params:'';
        
    if(ctype_id==1){
        extra=extra+'&'+data_obj.parents('form').serialize();
    }   //pwl form
    
    return ('index='+idx+'&buyqty='+buy_qty+'&products_bundle='+products_bundle+'&buy_now_qty='+buy_now_qty+'&products_id='+pid+'&cpt_id='+ctype_id+'&delivery_mode='+mid+'&confirmed='+confirmed+'&hla='+hla+extra);
}

function pfv_callback(resp_obj){
    if(resp_obj.data!=undefined){
        var idx=resp_obj.data.idx;
        
        if(resp_obj.data.cfm!=undefined){
            var cfm=resp_obj.data.cfm;
            
            if(cfm=='11'){//pop up
                pfv_unfreeze();
                if(jQuery('#general_popup_box:visible').length==1){
                    custom_general_popup_box(resp_obj.data.content, resp_obj.data.pname, '680px', {padding:'0px'}, resp_obj.data.header_ext);
                    pfv_confirm_order_error_handler(idx, resp_obj);
                }
            }else if(cfm=='12'){//changed qty
                pfv_unfreeze();
                jQuery('#tbl_'+idx).find('td.ot').html(resp_obj.data.content);
                pfv_confirm_order_error_handler(idx, resp_obj);
            }else if(cfm=='13'){
                if(resp_obj.err){
                    pfv_unfreeze();
                    pfv_confirm_order_error_handler(idx, resp_obj);
                }else{
                    pfv(idx, 14);
                }
            }
        }
    }
}

function pfv_beforesend(){
    if(jQuery('#general_popup_box table.purchase_confirm_box').length){
        jQuery('#general_popup_box table.purchase_confirm_box').addClass('freeze');
        jQuery('#general_popup_box>div.popup_close_button').addClass('popup_close_button_freeze').removeClass('popup_close_button');
    }
}

function pfv_isfreezed() {
    if(jQuery('#general_popup_box table.purchase_confirm_box').length){
        if (jQuery('#general_popup_box table.purchase_confirm_box').hasClass('freeze')){
            return true;
        }
    }
    
    return false;
}

function pfv_unfreeze(){
    if(jQuery('#general_popup_box table.purchase_confirm_box').length){
        jQuery('#general_popup_box table.purchase_confirm_box').removeClass('freeze');
        jQuery('#general_popup_box>div.popup_close_button_freeze').addClass('popup_close_button').removeClass('popup_close_button_freeze');
    }
}

function make_checkout_req(pass_data, act){
    var action = act!=undefined?act:'buy_code',
        async_switch = action=='buy_code'?true:false,
        return_obj = {err:false};
   
    if (!pfv_isfreezed()){
        jQuery.ajax({
            type: "POST",
            url: 'checkout_jsonhttp.php?action='+action,
            data: pass_data,
            async: async_switch,
            dataType: "json",
            timeout: 60000,
            beforeSend: pfv_beforesend,
            error: function(xhr, errorType, exception){
                pfv_unfreeze();
                return_obj.err = true;
                alert(compile_error_msg(xhr, errorType, exception));
            },
            success: function(data){
                if(data.redirect!=undefined){
                    document.location.href=data.redirect;

                }else if(data.cfm!=undefined){
                    return_obj.err=data.err;
                    return_obj.data=data;
                    pfv_callback(return_obj);
                }else{
                    pfv_unfreeze();
                    return_obj.err=data.err;
                    return_obj.data=data;
                }
            }
        });
    }
    
    return return_obj;
}

function attach_cpage(url){
    document.location.href=url+((url!=undefined && url.search(/\?/)>0)?'&':'?')+'next_url='+encodeURIComponent(config.regionalFormAction);
}
/* Buy Code */

function compile_error_msg(xhr, errorType, exception) {
    var message;
    var statusErrorMap = {
        '400' : "Server understood the request but request content was invalid.",
        '401' : "Unauthorised access.",
        '403' : "Forbidden resouce can't be accessed",
        '500' : "Internal Server Error.",
        '503' : "Service Unavailable"
    };
    if (xhr.status) {
        message =statusErrorMap[xhr.status];
        if(!message){
            message = xhr.status + " Unknow Error \n.";
        }
    }else if(exception=='parsererror'){
        message="Error.\nParsing JSON Request failed.";
    }else if(exception=='timeout'){
        message="Request Time out.";
    }else if(exception=='abort'){
        message="Request was aborted by the server";
    }else {
        message = exception + " Request failed. Please retry \n.";
    }
    
    if(errorType!=undefined){
        message=errorType+': '+message;
    }
    
    if(xhr.responseText!=undefined){
        message+=' ('+xhr.responseText+')';
    }
    
    return message;
}