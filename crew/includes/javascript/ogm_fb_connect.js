function do_redirect(to) {
	var form = document.createElement('form');
	var hiddenField = document.createElement('input');
	
	form.method = 'post';
	form.id = 'FBlink_selection_form';
	form.action = (location.href.search(/logoff.php/i) > 0 || location.href.search(/knowledgebase/i) > 0) ? '/' : location.href;
	
	if (to == 'login') {
		hiddenField.type = 'hidden';
		hiddenField.name = 'fb_action';
		hiddenField.value = 'pre_login';
		form.appendChild(hiddenField);
		
		//submit the form so that the page reloads the new data
		document.body.appendChild(form);
		document.getElementById('FBlink_selection_form').submit();
	}
}

function trigger_FB_login (url) {
    var newwindow;
    var  screenX    = typeof window.screenX != 'undefined' ? window.screenX : window.screenLeft,
         screenY    = typeof window.screenY != 'undefined' ? window.screenY : window.screenTop,
         outerWidth = typeof window.outerWidth != 'undefined' ? window.outerWidth : document.body.clientWidth,
         outerHeight = typeof window.outerHeight != 'undefined' ? window.outerHeight : (document.body.clientHeight - 22),
         width    = 500,
         height   = 270,
         left     = parseInt(screenX + ((outerWidth - width) / 2), 10),
         top      = parseInt(screenY + ((outerHeight - height) / 2.5), 10),
         features = (
            'width=' + width +
            ',height=' + height +
            ',left=' + left +
            ',top=' + top
          );

    newwindow=window.open(url,'Login_by_facebook',features);

    if (window.focus) {newwindow.focus()}
    return false;
}

function fb_account_link_selection_popup () {
	var pop_out = 	'<form name="fb_connect_form" action="'+FB_config.js_FILENAME_EXPRESS_LOGIN+'" method="POST">' +
                    '<table id="fb_connect_table" width="100%" bgcolor="#ffffff" cellspacing="5" cellpadding="3" style="border: 5px solid white">'+
                    '<tr><td colspan="2"><b class="largeFont">'+FB_config.txt_expressLoginTitle+'</b></td></tr>' +
                    '<tr><td class="smallText" colspan="2"><div class="dottedLine" style="padding-top:5px;margin-top:0px;"><!-- --></div></td></tr>' +
                    '<tr><td style="height:55px;">'+FB_config.txt_expressLoginUserInfo[0]+'</td>' +
                        '<td>'+FB_config.txt_expressLoginUserInfo[1]+'</td></tr>' + 
                    '<tr><td class="smallText" colspan="2"><div class="dottedLine" style="padding-top:5px;margin-top:0px;"><!-- --></div></td></tr>' +
                    '<tr><td class="smallText"><input id="selected_new" type="radio" name="fb_action" value="selected_new" checked></td>' +
                        '<td class="smallText"><font class="largeText"><label for="selected_new">'+FB_config.lbl_expressLoginFirstSel+'</label></font></td></tr>' +
                    '<tr><td class="smallText"><input id="selected_link" type="radio" name="fb_action" value="selected_link"></td>' +
                        '<td class="smallText"><font class="largeText"><label for="selected_link">'+FB_config.lbl_expressLoginSecondSel+'</label></font></td></tr>' +
                    '<tr><td class="smallText" colspan="2"><div class="dottedLine" style="padding-top:5px;margin-top:0px;"><!-- --></div></td></tr>' +
                    '<tr><td align=right colspan="2"><div class="green_button_fix_width"><a href="javascript:document.fb_connect_form.submit();"><span><font>'+config.actionContinue+'</font></span></a></div></td></tr>' +
                    '</table>' + 
                    '</form>';
	
    jQuery("#general_content").html(pop_out);
    jQuery("#general_content").css('padding','0px');
    show_fancybox("general_popup_box");
    
    jQuery('div.popup_close_button').bind('click', function(e){
        window.location.href = FB_config.js_fb_logout_link;
        parent.jQuery.fancybox.close();
	});
}

jQuery(document).ready(function(){
	if (FB_config.js_prelink_selection_flag) {
		fb_account_link_selection_popup();
	}
});