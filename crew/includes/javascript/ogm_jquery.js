function starteditor (id) {
	tinyMCE.execCommand('mceAddControl', false , id);
	jQuery('#'+id+'_start').hide();
	jQuery('#'+id+'_stop').show();
}

function stopeditor (id) {
	tinyMCE.execCommand('mceRemoveControl', false , id);
	jQuery('#'+id+'_start').show();
	jQuery('#'+id+'_stop').hide();
}

function jquery_confirm_box(box_content, box_button_type, box_alert_mode, box_title, timer, button_ok, button_cancel) {
	box_content = box_content || '';
	box_button_type = box_button_type || '';
	box_alert_mode = box_alert_mode || 0;
	box_title = box_title || '';
	button_ok = button_ok || 'OK';
	button_cancel = button_cancel || 'Cancel';
	
	if (box_alert_mode == 0) {
		var display_content_class = 'jwindow_popup_content_blue';
		var display_title_class = 'jwindow_popup_title_blue';
	} else {
		var display_content_class = 'jwindow_popup_content_red';
		var display_title_class = 'jwindow_popup_title_red';
	}

	display_html = '<table width="100%" class="'+display_content_class+'">';
	if (box_title != '') {
		display_html += '<tr>';
		display_html += '  <td class="'+display_title_class+'" width="90%" align="right">';
		display_html += '    <div style="font-size:16px;float:left;">'+box_title+'</div>';
		display_html += '  </td>';
		display_html += '</tr>';
	}
	display_html += '<tr height="50">';
	display_html += '  	<td class="'+display_content_class+'" valign="top" align="center">' + box_content + '</td>';
	display_html += '</tr>';
	/*
		0 Splash~~
		1 OK	// jQuery("#jconfirm_submit").click(
		2 OK and Cancel 
	*/
	if (box_button_type > 0) {
		display_html += '<tr height="60">';
		display_html += '  	<td class="'+display_content_class+'" align="center" bgcolor="#96BDFF" style="text-align:center;">';
		display_html += '  	<br/>';
		if (box_button_type > 0) {
			display_html += '		<span style="padding: 10px 30px 10px 30px;border: 1px #000 solid;text-align:center;font-size:14px;background-color: #e3ebf9;cursor:pointer;" id="jconfirm_submit" onClick="jQuery.unblockUI();">'+button_ok+'</span>&nbsp;&nbsp;&nbsp;&nbsp;';
		}
		
		if (box_button_type > 1) {
			display_html += '		<span style="padding: 10px 20px 10px 20px;border: 1px #000 solid;text-align:center;font-size:14px;background-color: #e3ebf9;cursor:pointer;" id="jconfirm_cancel" onClick="jQuery.unblockUI();return false;">'+button_cancel+'</span>';
		}
		
		display_html += '  	<br/>';
		display_html += '  </td>';
		display_html += '</tr>';
	}
	display_html += '</table>';

	timer = timer || 0;
	jQuery.blockUI.defaults.message = display_html;
	jQuery.blockUI({ 
		overlayCSS: {
			backgroundColor:'#96BDFF',
			opacity:'0.20', 
			cursor:'default'
		},
		css: { 
			border:	'0px',
			textAlign:'left', 
			cursor:'default',
			width:'500', 
            top:  (jQuery(window).height() - 250) /2 + 'px', 
            left: (jQuery(window).width() - 500) /2 + 'px', 
	        padding: '0px', 
	        opacity: '1'
		}
	});
	if ( timer > 0) {
		setTimeout(jQuery.unblockUI, (timer * 1000)); 
	}
}

function jquery_notice_box(box_content, box_button_type, icon_loading) {
	box_content = box_content || '';
	box_button_type = box_button_type || '';
	
	icon_loading = icon_loading || 'images/loading.gif';
	
	button_ok = 'OK';
	button_cancel = 'Cancel';
	
	display_html = '<table width="100%" id="jquery_notice_box">';
	display_html += '<tr>';
	display_html += '  	<td style="text-align:center;" valign="top"><img src="' + icon_loading + '"></td>';
	display_html += '</tr>';
	display_html += '<tr height="50">';
	display_html += '  	<td style="color:white;text-align:center;font-family:Arial,Verdana,sans-serif;font-size:12px;" valign="top">' + box_content + '</td>';
	display_html += '</tr>';
	/*
		0 Splash~~
		1 OK	// jQuery("#jconfirm_submit").click(
		2 OK and Cancel 
	*/
	if (box_button_type > 0) {
		display_html += '<tr height="60">';
		display_html += '  	<td align="center" style="text-align:center;">';
		display_html += '  	<br/>';
		if (box_button_type > 0) {
			display_html += '		<span style="padding: 10px 30px 10px 30px;border: 1px #000 solid;text-align:center;font-size:14px;background-color: #e3ebf9;cursor:pointer;" id="jconfirm_submit" onClick="jQuery.unblockUI();">'+button_ok+'</span>&nbsp;&nbsp;&nbsp;&nbsp;';
		} else if (box_button_type > 1) {
			display_html += '		<span style="padding: 10px 30px 10px 30px;border: 1px #000 solid;text-align:center;font-size:14px;background-color: #e3ebf9;cursor:pointer;" id="jconfirm_submit" onClick="jQuery.unblockUI();">'+button_ok+'</span>&nbsp;&nbsp;&nbsp;&nbsp;';
			display_html += '		<span style="padding: 10px 20px 10px 20px;border: 1px #000 solid;text-align:center;font-size:14px;background-color: #e3ebf9;cursor:pointer;" id="jconfirm_cancel" onClick="jQuery.unblockUI();return false;">'+button_cancel+'</span>';
		}		
		display_html += '  	<br/>';
		display_html += '  </td>';
		display_html += '</tr>';
	}
	display_html += '</table>';

	jQuery.blockUI.defaults.message = display_html;
	
	var msg_width = jQuery("#jquery_notice_box").width();
	var msg_height = jQuery("#jquery_notice_box").height();
	
	if (msg_width < 500) {
		msg_width = 500;
	}
	
	jQuery.blockUI({ 
		overlayCSS: {
			backgroundColor:'#000000',
			opacity:'0.9', 
			cursor:'default'
		},
		css: { 
			border:	'0px',
			textAlign:'left', 
			cursor:'default',
			width: msg_width, 
            top: (jQuery(window).height()/3) + 'px', 
            left: ((jQuery(window).width()/2) - (msg_width/2)) + 'px', 
	        padding: '0px', 
	        opacity: '1',
			backgroundColor:'#000000'
		}
	});
}

function tongle_description(id, action) {
	// SlideDown() / SlideUp() not very smooth in IE
	// Solution: Change to Show() / Hide()
	var idPanel = '#' + id;
	
	if (action == '') {
		var imgCollapse = '/images/icon-collapse.gif';
		var imgExpand = '/images/icon-expand.gif';
		
		// Animation without short desc
		if (jQuery(idPanel + '_short').text() == '') {
			if (jQuery(idPanel + '_long').css('display') == 'none') {
				jQuery(idPanel + '_long').slideDown();
				jQuery(idPanel + '_icon').attr('src', imgCollapse);
			} else {
				jQuery(idPanel + '_long').hide();
				jQuery(idPanel + '_icon').attr('src', imgExpand);
			}
		} else { // Animation with short desc
			if (jQuery(idPanel + '_long').css('display') == 'none') {
				jQuery(idPanel + '_short').slideUp();
				jQuery(idPanel + '_long').slideDown();
				jQuery(idPanel + '_icon').attr('src',imgCollapse);
			} else {
				jQuery(idPanel + '_long').slideUp();
				jQuery(idPanel + '_short').slideDown();
				jQuery(idPanel + '_icon').attr('src',imgExpand);
			}
		}
	} else {
		var expandClass = action + 'Expand';
		var collapseClass = action + 'Collapse';
		
		if (jQuery(idPanel + '_icon').hasClass(expandClass)) {
			jQuery(idPanel + '_icon').removeClass(expandClass);
			jQuery(idPanel + '_icon').addClass(collapseClass);
			jQuery(idPanel + '_long').show();
		} else {
			jQuery(idPanel + '_icon').removeClass(collapseClass);
			jQuery(idPanel + '_icon').addClass(expandClass);
			jQuery(idPanel + '_long').hide();
		}
	}
}

function game_region_toggle(region_obj, selected_grtl) {
	var region_id = region_obj.id.replace("gr","");
	var div_id = '#' + region_obj.id;
	var isExpand = jQuery(div_id).hasClass(jQuery(div_id).attr("class") + '_expand');
	
	reset_filter_value(true);
	
	if (isExpand) {
		// Collapse Section
		jQuery('#gl'+region_id+' > li > a[class*="gr_content_selected"]').removeClass('gr_content_selected');
		if (selected_grtl != '')	jQuery('#l'+selected_grtl).addClass('gr_content_selected');
		jQuery('#f_grtl').val(0);
	} else {
		// Expand Section
		toggle_inner_list(div_id, false);
		jQuery('#f_grtl').val(selected_grtl);
	}
	
	get_game_list();
}

function game_region_language_toggle(region_id, grtl_id) {
	jQuery('#f_grtl').val(grtl_id);
	jQuery('#gl'+region_id+' > li > a[class*="gr_content_selected"]').removeClass('gr_content_selected');
	jQuery('#l'+grtl_id).addClass('gr_content_selected');
	
	reset_filter_value(false);
	get_game_list();
}

function reset_filter_value(collapse_box) {
	// Need to remove this?
	var current_div_id = '';
	
	jQuery('#f_alpha').val(0);
	jQuery('#f_product_type').val(0);
	jQuery('#f_platform').val(0);
	jQuery('#f_genre').val(0);
	jQuery('#sag_game_filter_bar tr td div[class*="filter_selected"]').removeClass('filter_selected');
	
	if (collapse_box) {
		jQuery('div.leftBoxMiddle > li > div').each(function(index, ele){
			current_div_id = '#' + jQuery(this).attr("id");
			toggle_inner_list(current_div_id, true);
		});
	}
}

function toggle_inner_list(current_div_id, only_collapse) {
	var current_class = '';
	
	if (only_collapse) {
		if (jQuery(current_div_id).attr("class").search(/\_expand/) >= 0) {
			current_class = jQuery(current_div_id).attr("class").split(' ').slice(-1).toString();
			jQuery(current_div_id).removeClass(current_class);
			
			jQuery(current_div_id + ' > div[class="gr_partition"]:first > div').each(function(index, ele){
				current_class = jQuery(this).attr("class").split(' ').slice(-1).toString();
				jQuery(this).removeClass(current_class);
			});
			
			jQuery(current_div_id + ' > div[class*="gr_m"]').each(function(index, ele){
				current_class = jQuery(this).attr("class").split(' ').slice(-1).toString();
				jQuery(this).removeClass(current_class);
			});
			
			jQuery(current_div_id + ' > div[class="gr_partition"]:last > div').each(function(index, ele){
				current_class = jQuery(this).attr("class").split(' ').slice(-1).toString();
				jQuery(this).removeClass(current_class);
			});
			
			jQuery(current_div_id+'_long').hide();
		} 
	} else if (jQuery(current_div_id).attr("class").search(/\_expand/) < 0) {
		current_class = jQuery(current_div_id).attr("class") + '_expand';
		jQuery(current_div_id).addClass(current_class);
		
		jQuery(current_div_id + ' > div[class="gr_partition"]:first > div').each(function(index, ele){
			current_class = jQuery(this).attr("class") + '_expand';
			jQuery(this).addClass(current_class);
		});
		
		jQuery(current_div_id + ' > div[class*="gr_m"]').each(function(index, ele){
			current_class = jQuery(this).attr("class") + '_expand';
			jQuery(this).addClass(current_class);
		});
		
		jQuery(current_div_id + ' > div[class="gr_partition"]:last > div').each(function(index, ele){
			current_class = jQuery(this).attr("class") + '_expand';
			jQuery(this).addClass(current_class);
		});
		
		jQuery(current_div_id+'_long').show();
	} else {
		toggle_inner_list(current_div_id, true);
	}
}

function get_game_list() {
	jQuery.ajax({
		type: "post",
		url: 'search_all_games_xmlhttp.php',
		data: {'action':'get_games_list', 'grtl_id':jQuery('#f_grtl').val(), 'platform':jQuery('#f_platform').val(), 'genre':jQuery('#f_genre').val(),'product_type':jQuery('#f_product_type').val(),'alphabet':jQuery('#f_alpha').val()},
		dataType: 'xml',
		beforeSend:  function() {
			pop_out = '<div style="padding:10px;text-align:center"><img src="/images/loading.gif" width="20" height="20"></div>';
			jQuery("#game_content").html(pop_out);
		},
		success: function(xml){
			jQuery("#game_content").html(jQuery(xml).find('result').text());
			
			if (jQuery(xml).find('result').length > 0) {
				jQuery('#div_no_results').hide();
			}
		}
	});
}

function blockUI_disable(pass_msg) {
	pass_msg = pass_msg || '<h1>Please wait...</h1>';
    jQuery.blockUI({ 
    	message:  pass_msg,
		css: { 
	        border: 'none', 
	        padding: '15px', 
	        backgroundColor: '#000', 
	        '-webkit-border-radius': '10px', 
	        '-moz-border-radius': '10px', 
	        opacity: '.5', 
	        color: '#fff' 
	    }
	 }); 
}

function callAutoComplete(search_input, lookup_data_var, callback_fn) {
	jQuery('#'+search_input).autocomplete(lookup_data_var, {
		matchContains: true,
		minChars: 0,
		max: 999999999,
		formatItem: function(row, i, max) {
			return html_entity_decode(row.to);
		},
		formatMatch: function(row, i, max) {
			return row.name + " " + html_entity_decode(row.to);
		},
		formatResult: function(row) {
			return html_entity_decode(row.to);
		}
	});
	
	jQuery('#'+search_input).result(callback_fn).next().click(function() {
		jQuery(this).prev().search();
	});
}

function box_load_customers_testimonial() {
	jQuery.ajax({
		type: "get",
		url: "customer_xmlhttp.php?action=customers_testimonial",
		success: function(xml){
			jQuery("#boxes_customers_testimonial").html(jQuery(xml).find('testimonial').text());
			var headline_count;
			var headline_interval;
			var old_headline = 0;
			var current_headline = 0;
			
			headline_count = jQuery("div.headline").size();
			jQuery("div.headline").css("top","205px");
			
			headline_rotate();
			
			headline_interval = setInterval(headline_rotate,5000); //time in milliseconds
			jQuery("#scrollup").hover(function() {
				clearInterval(headline_interval);
				}, function() {
					headline_interval = setInterval(headline_rotate,5000); //time in milliseconds
			});
			
			function headline_rotate() {
				current_headline = (old_headline + 1) % headline_count; 
				jQuery("div.headline:eq(" + old_headline + ")").animate({top: -205},"slow", function() {
					jQuery(this).css("top","210px");
				});
			
				jQuery("div.headline:eq(" + current_headline + ")").show().animate({top: 5},"slow");  
				old_headline = current_headline;
			}
			
		}
	});
}

function loc_load_country() {
	jQuery.ajax({
		type: "GET",
		url: "customer_xmlhttp.php?action=get_zone_country",
		async: false,
		dataType: "xml",
		success: function(xml){
			var zone_country_info = '';
			jQuery(xml).find('response').each(function(){
				zone_country_info += jQuery("zone_country_info", this).text();
			});
			
			jQuery(".select_box_zones_countries").append(html_entity_decode(zone_country_info));
		}
	});
}

function filter_out_country(action) {
	if (action == "clear") {
		jQuery("#zone_country_filter").val("");
	}

	var filter = jQuery("#zone_country_filter").val();
	var temp_hold_country;
	
	jQuery("div.select_box_zones_countries div").each(function(){
		var idname = jQuery(this).text();

		idname = escapeFilteringText(trim_str(idname));
		idname = idname.replace(new RegExp(/[,]/gi),"");
		idname = idname.replace(new RegExp(/[ \'\//\(\)]/gi),"_");

		temp_hold_country = idname;
		temp_hold_country = temp_hold_country.substring(filter.length,0).toLowerCase();
		
		if (filter.toLowerCase() == temp_hold_country){
			jQuery("#zone_country_"+idname).show();
		} else {
			jQuery("#zone_country_"+idname).hide();
		}
	})
}

function get_captcha_img(captcha_obj_id) {
	var ref_url = "customer_xmlhttp.php?action=get_captcha_img";
	
	jQuery.ajax({
		url: ref_url,
		type: 'GET',
		dataType: 'xml',
		timeout: 10000,
		error: function(){
		},
		success: function(xml) {
  			var captcha_xml = jQuery(xml).find('captcha_img').text();
  			jQuery(captcha_obj_id).html(captcha_xml);
		}
	});
}

function toggleAll(id, check_all_id) {
	jQuery("#"+id+" input:checkbox").attr('checked', jQuery("#"+check_all_id).is(':checked'));
}

function toggleSelectAll(id, check_all_id) {
	if (jQuery("#"+id+" input:checkbox").length > jQuery("#"+id+" input:checked").length) {
		jQuery("#"+check_all_id).attr('checked', false); 
	}
	
	if (jQuery("#"+id+" input:checkbox").length == jQuery("#"+id+" input:checked").length + jQuery("#"+check_all_id).length) {
		jQuery("#"+check_all_id).attr('checked', true);
	}
}

function countdown_timer(id, box_id, callback, yrs, mths, days, hrs, mins, secs) {
	yrs = yrs != '' ? yrs : 0;
	mths = mths != '' ? mths : 0;
	days = days != '' ? days : 0;
	hrs = hrs != '' ? hrs : 0;
	mins = mins != '' ? mins : 0;
	secs = secs != '' ? secs : 0;
	
	var endtime = new Date();
	endtime = new Date(yrs, mths - 1, days, hrs, mins, secs);
	jQuery('#'+id).countdown({until: endtime, onExpiry: callback, compact: true, description: ''});

	if (box_id != '') {
		jQuery('#'+box_id).show();
	}
}

function validate_char_length(field_id, maxlength, error_field, error_msg) {
	if (jQuery('#'+field_id).val().length > maxlength) {
		jQuery('#'+error_field).html(error_msg);
		return false;
	} else {
		return true;
	}
}

/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

/*
 * jQuery blockUI plugin
 * Version 2.38 (29-MAR-2011)
 * @requires jQuery v1.2.3 or later
 *
 * Examples at: http://malsup.com/jquery/block/
 * Copyright (c) 2007-2010 M. Alsup
 * Dual licensed under the MIT and GPL licenses:
 * http://www.opensource.org/licenses/mit-license.php
 * http://www.gnu.org/licenses/gpl.html
 *
 * Thanks to Amir-Hossein Sobhi for some excellent contributions!
 */

;(function($) {

if (/1\.(0|1|2)\.(0|1|2)/.test($.fn.jquery) || /^1.1/.test($.fn.jquery)) {
	alert('blockUI requires jQuery v1.2.3 or later!  You are using v' + $.fn.jquery);
	return;
}

$.fn._fadeIn = $.fn.fadeIn;

var noOp = function() {};

// this bit is to ensure we don't call setExpression when we shouldn't (with extra muscle to handle
// retarded userAgent strings on Vista)
var mode = document.documentMode || 0;
var setExpr = $.browser.msie && (($.browser.version < 8 && !mode) || mode < 8);
var ie6 = $.browser.msie && /MSIE 6.0/.test(navigator.userAgent) && !mode;

// global $ methods for blocking/unblocking the entire page
$.blockUI   = function(opts) { install(window, opts); };
$.unblockUI = function(opts) { remove(window, opts); };

// convenience method for quick growl-like notifications  (http://www.google.com/search?q=growl)
$.growlUI = function(title, message, timeout, onClose) {
	var $m = $('<div class="growlUI"></div>');
	if (title) $m.append('<h1>'+title+'</h1>');
	if (message) $m.append('<h2>'+message+'</h2>');
	if (timeout == undefined) timeout = 3000;
	$.blockUI({
		message: $m, fadeIn: 700, fadeOut: 1000, centerY: false,
		timeout: timeout, showOverlay: false,
		onUnblock: onClose, 
		css: $.blockUI.defaults.growlCSS
	});
};

// plugin method for blocking element content
$.fn.block = function(opts) {
	return this.unblock({ fadeOut: 0 }).each(function() {
		if ($.css(this,'position') == 'static')
			this.style.position = 'relative';
		if ($.browser.msie)
			this.style.zoom = 1; // force 'hasLayout'
		install(this, opts);
	});
};

// plugin method for unblocking element content
$.fn.unblock = function(opts) {
	return this.each(function() {
		remove(this, opts);
	});
};

$.blockUI.version = 2.38; // 2nd generation blocking at no extra cost!

// override these in your code to change the default behavior and style
$.blockUI.defaults = {
	// message displayed when blocking (use null for no message)
	message:  '<h1>Please wait...</h1>',

	title: null,	  // title string; only used when theme == true
	draggable: true,  // only used when theme == true (requires jquery-ui.js to be loaded)
	
	theme: false, // set to true to use with jQuery UI themes
	
	// styles for the message when blocking; if you wish to disable
	// these and use an external stylesheet then do this in your code:
	// $.blockUI.defaults.css = {};
	css: {
		padding:	0,
		margin:		0,
		width:		'30%',
		top:		'40%',
		left:		'35%',
		textAlign:	'center',
		color:		'#000',
		border:		'3px solid #aaa',
		backgroundColor:'#fff',
		cursor:		'wait'
	},
	
	// minimal style set used when themes are used
	themedCSS: {
		width:	'30%',
		top:	'40%',
		left:	'35%'
	},

	// styles for the overlay
	overlayCSS:  {
		backgroundColor: '#000',
		opacity:	  	 0.6,
		cursor:		  	 'wait'
	},

	// styles applied when using $.growlUI
	growlCSS: {
		width:  	'350px',
		top:		'10px',
		left:   	'',
		right:  	'10px',
		border: 	'none',
		padding:	'5px',
		opacity:	0.6,
		cursor: 	'default',
		color:		'#fff',
		backgroundColor: '#000',
		'-webkit-border-radius': '10px',
		'-moz-border-radius':	 '10px',
		'border-radius': 		 '10px'
	},
	
	// IE issues: 'about:blank' fails on HTTPS and javascript:false is s-l-o-w
	// (hat tip to Jorge H. N. de Vasconcelos)
	iframeSrc: /^https/i.test(window.location.href || '') ? 'javascript:false' : 'about:blank',

	// force usage of iframe in non-IE browsers (handy for blocking applets)
	forceIframe: false,

	// z-index for the blocking overlay
	baseZ: 1000,

	// set these to true to have the message automatically centered
	centerX: true, // <-- only effects element blocking (page block controlled via css above)
	centerY: true,

	// allow body element to be stetched in ie6; this makes blocking look better
	// on "short" pages.  disable if you wish to prevent changes to the body height
	allowBodyStretch: true,

	// enable if you want key and mouse events to be disabled for content that is blocked
	bindEvents: true,

	// be default blockUI will supress tab navigation from leaving blocking content
	// (if bindEvents is true)
	constrainTabKey: true,

	// fadeIn time in millis; set to 0 to disable fadeIn on block
	fadeIn:  200,

	// fadeOut time in millis; set to 0 to disable fadeOut on unblock
	fadeOut:  400,

	// time in millis to wait before auto-unblocking; set to 0 to disable auto-unblock
	timeout: 0,

	// disable if you don't want to show the overlay
	showOverlay: true,

	// if true, focus will be placed in the first available input field when
	// page blocking
	focusInput: true,

	// suppresses the use of overlay styles on FF/Linux (due to performance issues with opacity)
	applyPlatformOpacityRules: true,
	
	// callback method invoked when fadeIn has completed and blocking message is visible
	onBlock: null,

	// callback method invoked when unblocking has completed; the callback is
	// passed the element that has been unblocked (which is the window object for page
	// blocks) and the options that were passed to the unblock call:
	//	 onUnblock(element, options)
	onUnblock: null,

	// don't ask; if you really must know: http://groups.google.com/group/jquery-en/browse_thread/thread/36640a8730503595/2f6a79a77a78e493#2f6a79a77a78e493
	quirksmodeOffsetHack: 4,

	// class name of the message block
	blockMsgClass: 'blockMsg'
};

// private data and functions follow...

var pageBlock = null;
var pageBlockEls = [];

function install(el, opts) {
	var full = (el == window);
	var msg = opts && opts.message !== undefined ? opts.message : undefined;
	opts = $.extend({}, $.blockUI.defaults, opts || {});
	opts.overlayCSS = $.extend({}, $.blockUI.defaults.overlayCSS, opts.overlayCSS || {});
	var css = $.extend({}, $.blockUI.defaults.css, opts.css || {});
	var themedCSS = $.extend({}, $.blockUI.defaults.themedCSS, opts.themedCSS || {});
	msg = msg === undefined ? opts.message : msg;

	// remove the current block (if there is one)
	if (full && pageBlock)
		remove(window, {fadeOut:0});

	// if an existing element is being used as the blocking content then we capture
	// its current place in the DOM (and current display style) so we can restore
	// it when we unblock
	if (msg && typeof msg != 'string' && (msg.parentNode || msg.jquery)) {
		var node = msg.jquery ? msg[0] : msg;
		var data = {};
		$(el).data('blockUI.history', data);
		data.el = node;
		data.parent = node.parentNode;
		data.display = node.style.display;
		data.position = node.style.position;
		if (data.parent)
			data.parent.removeChild(node);
	}

	var z = opts.baseZ;

	// blockUI uses 3 layers for blocking, for simplicity they are all used on every platform;
	// layer1 is the iframe layer which is used to supress bleed through of underlying content
	// layer2 is the overlay layer which has opacity and a wait cursor (by default)
	// layer3 is the message content that is displayed while blocking

	var lyr1 = ($.browser.msie || opts.forceIframe) 
		? $('<iframe class="blockUI" style="z-index:'+ (z++) +';display:none;border:none;margin:0;padding:0;position:absolute;width:100%;height:100%;top:0;left:0" src="'+opts.iframeSrc+'"></iframe>')
		: $('<div class="blockUI" style="display:none"></div>');
	
	var lyr2 = opts.theme 
	 	? $('<div class="blockUI blockOverlay ui-widget-overlay" style="z-index:'+ (z++) +';display:none"></div>')
	 	: $('<div class="blockUI blockOverlay" style="z-index:'+ (z++) +';display:none;border:none;margin:0;padding:0;width:100%;height:100%;top:0;left:0"></div>');

	var lyr3, s;
	if (opts.theme && full) {
		s = '<div class="blockUI ' + opts.blockMsgClass + ' blockPage ui-dialog ui-widget ui-corner-all" style="z-index:'+z+';display:none;position:fixed">' +
				'<div class="ui-widget-header ui-dialog-titlebar ui-corner-all blockTitle">'+(opts.title || '&nbsp;')+'</div>' +
				'<div class="ui-widget-content ui-dialog-content"></div>' +
			'</div>';
	}
	else if (opts.theme) {
		s = '<div class="blockUI ' + opts.blockMsgClass + ' blockElement ui-dialog ui-widget ui-corner-all" style="z-index:'+z+';display:none;position:absolute">' +
				'<div class="ui-widget-header ui-dialog-titlebar ui-corner-all blockTitle">'+(opts.title || '&nbsp;')+'</div>' +
				'<div class="ui-widget-content ui-dialog-content"></div>' +
			'</div>';
	}
	else if (full) {
		s = '<div class="blockUI ' + opts.blockMsgClass + ' blockPage" style="z-index:'+z+';display:none;position:fixed"></div>';
	}			
	else {
		s = '<div class="blockUI ' + opts.blockMsgClass + ' blockElement" style="z-index:'+z+';display:none;position:absolute"></div>';
	}
	lyr3 = $(s);

	// if we have a message, style it
	if (msg) {
		if (opts.theme) {
			lyr3.css(themedCSS);
			lyr3.addClass('ui-widget-content');
		}
		else 
			lyr3.css(css);
	}

	// style the overlay
	if (!opts.theme && (!opts.applyPlatformOpacityRules || !($.browser.mozilla && /Linux/.test(navigator.platform))))
		lyr2.css(opts.overlayCSS);
	lyr2.css('position', full ? 'fixed' : 'absolute');

	// make iframe layer transparent in IE
	if ($.browser.msie || opts.forceIframe)
		lyr1.css('opacity',0.0);

	//$([lyr1[0],lyr2[0],lyr3[0]]).appendTo(full ? 'body' : el);
	var layers = [lyr1,lyr2,lyr3], $par = full ? $('body') : $(el);
	$.each(layers, function() {
		this.appendTo($par);
	});
	
	if (opts.theme && opts.draggable && $.fn.draggable) {
		lyr3.draggable({
			handle: '.ui-dialog-titlebar',
			cancel: 'li'
		});
	}

	// ie7 must use absolute positioning in quirks mode and to account for activex issues (when scrolling)
	var expr = setExpr && (!$.boxModel || $('object,embed', full ? null : el).length > 0);
	if (ie6 || expr) {
		// give body 100% height
		if (full && opts.allowBodyStretch && $.boxModel)
			$('html,body').css('height','100%');

		// fix ie6 issue when blocked element has a border width
		if ((ie6 || !$.boxModel) && !full) {
			var t = sz(el,'borderTopWidth'), l = sz(el,'borderLeftWidth');
			var fixT = t ? '(0 - '+t+')' : 0;
			var fixL = l ? '(0 - '+l+')' : 0;
		}

		// simulate fixed position
		$.each([lyr1,lyr2,lyr3], function(i,o) {
			var s = o[0].style;
			s.position = 'absolute';
			if (i < 2) {
				full ? s.setExpression('height','Math.max(document.body.scrollHeight, document.body.offsetHeight) - (jQuery.boxModel?0:'+opts.quirksmodeOffsetHack+') + "px"')
					 : s.setExpression('height','this.parentNode.offsetHeight + "px"');
				full ? s.setExpression('width','jQuery.boxModel && document.documentElement.clientWidth || document.body.clientWidth + "px"')
					 : s.setExpression('width','this.parentNode.offsetWidth + "px"');
				if (fixL) s.setExpression('left', fixL);
				if (fixT) s.setExpression('top', fixT);
			}
			else if (opts.centerY) {
				if (full) s.setExpression('top','(document.documentElement.clientHeight || document.body.clientHeight) / 2 - (this.offsetHeight / 2) + (blah = document.documentElement.scrollTop ? document.documentElement.scrollTop : document.body.scrollTop) + "px"');
				s.marginTop = 0;
			}
			else if (!opts.centerY && full) {
				var top = (opts.css && opts.css.top) ? parseInt(opts.css.top) : 0;
				var expression = '((document.documentElement.scrollTop ? document.documentElement.scrollTop : document.body.scrollTop) + '+top+') + "px"';
				s.setExpression('top',expression);
			}
		});
	}

	// show the message
	if (msg) {
		if (opts.theme)
			lyr3.find('.ui-widget-content').append(msg);
		else
			lyr3.append(msg);
		if (msg.jquery || msg.nodeType)
			$(msg).show();
	}

	if (($.browser.msie || opts.forceIframe) && opts.showOverlay)
		lyr1.show(); // opacity is zero
	if (opts.fadeIn) {
		var cb = opts.onBlock ? opts.onBlock : noOp;
		var cb1 = (opts.showOverlay && !msg) ? cb : noOp;
		var cb2 = msg ? cb : noOp;
		if (opts.showOverlay)
			lyr2._fadeIn(opts.fadeIn, cb1);
		if (msg)
			lyr3._fadeIn(opts.fadeIn, cb2);
	}
	else {
		if (opts.showOverlay)
			lyr2.show();
		if (msg)
			lyr3.show();
		if (opts.onBlock)
			opts.onBlock();
	}

	// bind key and mouse events
	bind(1, el, opts);

	if (full) {
		pageBlock = lyr3[0];
		pageBlockEls = $(':input:enabled:visible',pageBlock);
		if (opts.focusInput)
			setTimeout(focus, 20);
	}
	else
		center(lyr3[0], opts.centerX, opts.centerY);

	if (opts.timeout) {
		// auto-unblock
		var to = setTimeout(function() {
			full ? $.unblockUI(opts) : $(el).unblock(opts);
		}, opts.timeout);
		$(el).data('blockUI.timeout', to);
	}
};

// remove the block
function remove(el, opts) {
	var full = (el == window);
	var $el = $(el);
	var data = $el.data('blockUI.history');
	var to = $el.data('blockUI.timeout');
	if (to) {
		clearTimeout(to);
		$el.removeData('blockUI.timeout');
	}
	opts = $.extend({}, $.blockUI.defaults, opts || {});
	bind(0, el, opts); // unbind events
	
	var els;
	if (full) // crazy selector to handle odd field errors in ie6/7
		els = $('body').children().filter('.blockUI').add('body > .blockUI');
	else
		els = $('.blockUI', el);

	if (full)
		pageBlock = pageBlockEls = null;

	if (opts.fadeOut) {
		els.fadeOut(opts.fadeOut);
		setTimeout(function() { reset(els,data,opts,el); }, opts.fadeOut);
	}
	else
		reset(els, data, opts, el);
};

// move blocking element back into the DOM where it started
function reset(els,data,opts,el) {
	els.each(function(i,o) {
		// remove via DOM calls so we don't lose event handlers
		if (this.parentNode)
			this.parentNode.removeChild(this);
	});

	if (data && data.el) {
		data.el.style.display = data.display;
		data.el.style.position = data.position;
		if (data.parent)
			data.parent.appendChild(data.el);
		$(el).removeData('blockUI.history');
	}

	if (typeof opts.onUnblock == 'function')
		opts.onUnblock(el,opts);
};

// bind/unbind the handler
function bind(b, el, opts) {
	var full = el == window, $el = $(el);

	// don't bother unbinding if there is nothing to unbind
	if (!b && (full && !pageBlock || !full && !$el.data('blockUI.isBlocked')))
		return;
	if (!full)
		$el.data('blockUI.isBlocked', b);

	// don't bind events when overlay is not in use or if bindEvents is false
	if (!opts.bindEvents || (b && !opts.showOverlay)) 
		return;

	// bind anchors and inputs for mouse and key events
	var events = 'mousedown mouseup keydown keypress';
	b ? $(document).bind(events, opts, handler) : $(document).unbind(events, handler);
};

// event handler to suppress keyboard/mouse events when blocking
function handler(e) {
	// allow tab navigation (conditionally)
	if (e.keyCode && e.keyCode == 9) {
		if (pageBlock && e.data.constrainTabKey) {
			var els = pageBlockEls;
			var fwd = !e.shiftKey && e.target === els[els.length-1];
			var back = e.shiftKey && e.target === els[0];
			if (fwd || back) {
				setTimeout(function(){focus(back)},10);
				return false;
			}
		}
	}
	var opts = e.data;
	// allow events within the message content
	if ($(e.target).parents('div.' + opts.blockMsgClass).length > 0)
		return true;

	// allow events for content that is not being blocked
	return $(e.target).parents().children().filter('div.blockUI').length == 0;
};

function focus(back) {
	if (!pageBlockEls)
		return;
	var e = pageBlockEls[back===true ? pageBlockEls.length-1 : 0];
	if (e)
		e.focus();
};

function center(el, x, y) {
	var p = el.parentNode, s = el.style;
	var l = ((p.offsetWidth - el.offsetWidth)/2) - sz(p,'borderLeftWidth');
	var t = ((p.offsetHeight - el.offsetHeight)/2) - sz(p,'borderTopWidth');
	if (x) s.left = l > 0 ? (l+'px') : '0';
	if (y) s.top  = t > 0 ? (t+'px') : '0';
};

function sz(el, p) {
	return parseInt($.css(el,p))||0;
};

})(jQuery);