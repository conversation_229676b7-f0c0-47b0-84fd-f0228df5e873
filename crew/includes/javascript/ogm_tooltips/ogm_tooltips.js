(function($) {  
	$.fn.ogm_tooltips = function( options ) {
		
		var settings = {'method' : 'mouseover',
						'point' : 'up',
						'width' : 'auto',
						'image_up' : 'tooltip_bg.gif',
						'image_down' : 'tooltip_bg_down.gif',
						'image_close' : 'icon_close.gif',
						'link_close' : ''
						 };
		
		return this.each(function() {
			var corvered_area_x1;
			var corvered_area_x2;
			var corvered_area_y1;
			var corvered_area_y2;
			var tooptips_top;
			var tooptips_left;
			var ogm_unique_code;
			
			if ( options ) { 
				$.extend( settings, options );
			}
			
			jQuery(".ogm_tooltips_close").hide();
			if (settings.method == 'click') {
				jQuery(".ogm_tooltips_close").show();
				jQuery(this).click(function() {
					
					if (settings.width != '') {
						jQuery("#ogm_tooltips").css('width', settings.width);
						
						var content_width = settings.width;
						content_width = content_width - 15;
						if (content_width < 0) {
							content_width = 0;
						}
						
						jQuery(".ogm_tooltips_content").css('width', content_width + 'px');
					}
					
					jQuery(".ogm_tooltips_close .ogm_tooltips_link").html('');
					jQuery(".ogm_tooltips_close .ogm_tooltips_icon").css('background', '');
					
					if (settings.method == 'click') {
						jQuery(".ogm_tooltips_close .ogm_tooltips_link").html(settings.link_close);
						jQuery(".ogm_tooltips_close .ogm_tooltips_icon").css('background', 'url('+settings.image_close+')');
						jQuery('.ogm_tooltips_close .ogm_tooltips_icon').css('background-repeat','no-repeat');
					}
					
					if (settings.point == 'up') {
						jQuery(".ogm_tooltips_triangle").css('background', 'url('+settings.image_up+')');
						jQuery(".ogm_tooltips_triangle").css('top', '-9px');
					} else {
						jQuery(".ogm_tooltips_triangle").css('background', 'url('+settings.image_down+')');
						jQuery(".ogm_tooltips_triangle").css('top', '20px');
					}
					
					jQuery('div#ogm_tooltips div.ogm_tooltips_title').show();
					if (jQuery(this).attr('ogm_title')!='') {
						jQuery('div#ogm_tooltips div.ogm_tooltips_title').html(jQuery(this).attr('ogm_title'));
					} else {
						jQuery('div#ogm_tooltips div.ogm_tooltips_title').hide();
					}
					
					jQuery('div#ogm_tooltips div.ogm_tooltips_content').html(jQuery(this).attr('ogm_content'));
					
					tooptips_top = 0;
					tooptips_left = 0;
					
					if (navigator.userAgent.toLowerCase().indexOf('msie') > -1) {
						if (settings.point == 'up') {
							tooptips_top = (jQuery(this).position().top + (jQuery(this).height()+9));
						} else {
							tooptips_top = (jQuery(this).position().top - (jQuery(this).height()+9));
						}
						tooptips_left = ((jQuery(this).position().left) + (jQuery(this).width()/2) - 16) - 2;
					} else {
						if (settings.point == 'up') {
							tooptips_top = (jQuery(this).position().top + (jQuery(this).height()+9));
						} else {
							tooptips_top = (jQuery(this).position().top - (jQuery(this).height()+9));
						}
						tooptips_left = ((jQuery(this).position().left) + (jQuery(this).width()/2) - 16);
					}
					
					ogm_unique_code = tooptips_top + '-' + tooptips_left;
					jQuery('#ogm_tooltips').attr('ogm_unique_code', ogm_unique_code);
					
					jQuery('#ogm_tooltips').css('top', tooptips_top + 'px');
					jQuery('#ogm_tooltips').css('left', tooptips_left + 'px');
					
					jQuery(".ogm_tooltips_close .ogm_tooltips_icon, .ogm_tooltips_close .ogm_tooltips_link").click(function(){
						jQuery('#ogm_tooltips').hide();
					});
					jQuery('#ogm_tooltips').show();
				});
			} else {
				jQuery(this).hover(
					function (e) {
						
						if (settings.width != '') {
							jQuery("#ogm_tooltips").css('width', settings.width);
						}
						
						jQuery(".ogm_tooltips_close .ogm_tooltips_link").html('');
						jQuery(".ogm_tooltips_close .ogm_tooltips_icon").css('background', '');
						
						if (settings.method == 'click') {
							jQuery(".ogm_tooltips_close .ogm_tooltips_link").html(settings.link_close);
							jQuery(".ogm_tooltips_close .ogm_tooltips_icon").css('background', 'url('+settings.image_close+')');
							jQuery('.ogm_tooltips_close .ogm_tooltips_icon').css('background-repeat','no-repeat');
						}
						
						if (settings.point == 'up') {
							jQuery(".ogm_tooltips_triangle").css('background', 'url('+settings.image_up+')');
							jQuery(".ogm_tooltips_triangle").css('top', '-9px');
						} else {
							jQuery(".ogm_tooltips_triangle").css('background', 'url('+settings.image_down+')');
							jQuery(".ogm_tooltips_triangle").css('top', '20px');
						}
						
						jQuery('div#ogm_tooltips div.ogm_tooltips_title').show();
						if (jQuery(this).attr('ogm_title')!='') {
							jQuery('div#ogm_tooltips div.ogm_tooltips_title').html(jQuery(this).attr('ogm_title'));
						} else {
							jQuery('div#ogm_tooltips div.ogm_tooltips_title').hide();
						}
						
						jQuery('div#ogm_tooltips div.ogm_tooltips_content').html(jQuery(this).attr('ogm_content'));
						
						tooptips_top = 0;
						tooptips_left = 0;
						
						if (navigator.userAgent.toLowerCase().indexOf('msie') > -1) {
							tooptips_top = (jQuery(this).position().top + (jQuery(this).height()+9));
							tooptips_left = ((jQuery(this).position().left) + (jQuery(this).width()/2) - 16) - 2;
						} else {
							tooptips_top = (jQuery(this).position().top + (jQuery(this).height()+9));
							tooptips_left = ((jQuery(this).position().left) + (jQuery(this).width()/2) - 16);
						}
						
						corvered_area_x1 = (jQuery(this).position().left) - 10;
						corvered_area_x2 = (jQuery(this).position().left + jQuery(this).width()) + 10;
						corvered_area_y1 = jQuery(this).position().top - 10;
						corvered_area_y2 = ((jQuery(this).position().top+jQuery(this).height()) + 9) + 10;
						
						ogm_unique_code = tooptips_top + '-' + tooptips_left;
						jQuery('#ogm_tooltips').attr('ogm_unique_code', ogm_unique_code);
						
						jQuery('#ogm_tooltips').css('top', tooptips_top + 'px');
						jQuery('#ogm_tooltips').css('left', tooptips_left + 'px');
						jQuery('#ogm_tooltips').show();
					}, 
					function (e) {
						var mouse_x;
						var mouse_y;
						
						check_ogm_tooltips_onblur = function () {
							jQuery(document).mousemove(function(e){mouse_x = e.pageX;mouse_y = e.pageY;});
							
							if ((corvered_area_y1 >= mouse_y) || (corvered_area_x1 >= mouse_x) || (mouse_y >= corvered_area_y2) || (mouse_x >= corvered_area_x2)) {
								jQuery('#ogm_tooltips[ogm_unique_code='+ogm_unique_code+']').hide();
							} else {
								if (jQuery('#ogm_tooltips').css('display') != 'none') {
									setTimeout("check_ogm_tooltips_onblur()",1000);
								}
							}
						}
						check_ogm_tooltips_onblur();
					}
				);
				
				var tooltips_area_x1;
				var tooltips_area_x2;
				var tooltips_area_y1;
				var tooltips_area_y2;
				var ogm_unique_code2;
				
				jQuery('#ogm_tooltips').hover(
					function (e) {
						tooltips_area_x1 = jQuery(this).position().left;
						tooltips_area_x2 = jQuery(this).position().left + jQuery(this).width();
						tooltips_area_y1 = jQuery(this).position().top;
						tooltips_area_y2 = jQuery(this).position().top+jQuery(this).height();
						
						ogm_unique_code2 = jQuery(this).position().left+'-'+jQuery(this).position().top;
						jQuery('#ogm_tooltips').attr('ogm_unique_code', ogm_unique_code2);
						
						tooltips_area_x1 = tooltips_area_x1 - 10;
						tooltips_area_x2 = tooltips_area_x2 + 20;
						tooltips_area_y1 = tooltips_area_y1 - 10;
						tooltips_area_y2 = tooltips_area_y2 + 10;
						
						jQuery('#ogm_tooltips').show();
					}, 
					function (e) {
						var t;
						var mouse_x;
						var mouse_y;
						
						check_ogm_tooltips_onblur = function () {
							jQuery(document).mousemove(function(e){mouse_x = e.pageX;mouse_y = e.pageY;});
							
							if ((tooltips_area_y1 >= mouse_y) || (tooltips_area_x1 >= mouse_x) || (mouse_y >= tooltips_area_y2) || (mouse_x >= tooltips_area_x2)) {
								jQuery('#ogm_tooltips[ogm_unique_code='+ogm_unique_code2+']').hide();
							} else {
								if (jQuery('#ogm_tooltips').css('display') != 'none') {
									setTimeout("check_ogm_tooltips_onblur()",1000);
								}
							}
						}
						check_ogm_tooltips_onblur();
					}
				);
			}
		});
	};
})(jQuery);