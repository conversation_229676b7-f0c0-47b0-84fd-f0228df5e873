<SCRIPT language="JavaScript" type="text/javascript">
<!--

function getPMLists (curr_sel, pm_div, opt_div, pm_id, b_id) {
	var obj_ref = curr_sel;
	var pm_obj = DOMCall(pm_div);
	var div_option_obj = DOMCall(opt_div);
	var res;
    var server_action = 'refresh_pm_fields';
    
    pm_obj.innerHTML = 'Loading ...';
    div_option_obj.innerHTML = '';
    obj_ref.disabled = true;
    
	var ref_url = "payment_xmlhttp.php?action="+server_action+"&pm_id="+pm_id+"&b_id="+b_id+"&cur_code="+obj_ref.value;
	
	blockUI_disable();
	jQuery.ajax({
		url: ref_url,
		type: 'GET',
		dataType: 'xml',
		timeout: 10000,
		error: function(){
			jQuery.unblockUI();
		},
		success: function(xml) {
      		if (jQuery(xml).find('option_selection').text() != '') {
				pm_obj.innerHTML = jQuery(xml).find('option_selection').text();
			} else {
      			pm_obj.innerHTML = '';
      		}
      		
      		obj_ref.disabled = false;
      		jQuery.unblockUI();
		}
	});
	
    return true;
}

function getPMOptions (pm_sel, opt_div, b_id) {
	var obj_ref = pm_sel;
	var div_option_obj = DOMCall(opt_div);
	var res;
    var server_action = (b_id != null && b_id != '') ? 'edit_pm_fields' : 'add_pm_fields';
    
    div_option_obj.innerHTML = 'Loading ...';
    obj_ref.disabled = true;
	
	var ref_url = "payment_xmlhttp.php?action="+server_action+"&b_id="+b_id+"&pm_id="+obj_ref.value;
	
	blockUI_disable();
	jQuery.ajax({
		url: ref_url,
		type: 'GET',
		dataType: 'xml',
		timeout: 10000,
		error: function(){
			jQuery.unblockUI();
		},
		success: function(xml) {
      		if (jQuery(xml).find('option_selection').text() != '') {
				div_option_obj.innerHTML = jQuery(xml).find('option_selection').text();
			} else {
      			div_option_obj.innerHTML = '';
      		}
      		
      		obj_ref.disabled = false;
      		jQuery.unblockUI();
		}
	});
}

function getPMInfo (pb_sel, bal_cur, bal_txt, opt_div) {
	var obj_ref = pb_sel;
	var bal_obj = DOMCall(bal_txt);
	var div_option_obj = DOMCall(opt_div);
	var res;
    var server_action = 'withdraw_pm_info';
    var js_error = false;
    
    if (obj_ref.value != '') {
	    if (trim_str(bal_obj.value) == '') {
	    	alert('<?=JS_ERROR_NO_WITHDRAW_AMOUNT?>');
	    	js_error = true;
	    } else if (!currencyValidation(trim_str(bal_obj.value))) {
	   		alert('<?=JS_ERROR_INVALID_WITHDRAW_AMOUNT?>');
	   		js_error = true;
	    }
    }
    
    if (js_error) {
    	obj_ref.selectedIndex = 0;
    	return false;
	} else {
	    div_option_obj.innerHTML = 'Loading ...';
	    obj_ref.disabled = true;
		
		var ref_url = "payment_xmlhttp.php?action="+server_action+"&b_id="+obj_ref.value+"&w_cur="+bal_cur+"&w_amt="+bal_obj.value;
		
		blockUI_disable();
		jQuery.ajax({
			url: ref_url,
			type: 'GET',
			dataType: 'xml',
			timeout: 10000,
			error: function(){
				jQuery.unblockUI();
			},
			success: function(xml) {
	      		if (jQuery(xml).find('withdraw_info').text() != '') {
					div_option_obj.innerHTML = jQuery(xml).find('withdraw_info').text();
				} else {
	      			div_option_obj.innerHTML = '';
	      		}
	      		
	      		obj_ref.disabled = false;
	      		jQuery.unblockUI();
			}
		});
	}
}
//--></SCRIPT>