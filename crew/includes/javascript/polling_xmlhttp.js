function submitPollVote(user_id, poll_id, option_id, aa, div_name, form_name, input_name, tpl) {
	showPollLoading();
	var url="polling_xmlhttp.php"
	url=url+"?action=vote"
	url=url+"&aa="+aa
	url=url+"&pid="+poll_id
	url=url+"&oid="+option_id
	url=url+"&uid="+user_id
	url=url+"&tpl="+tpl
	url=url+"&sid="+Math.random()
	ajaxRequest(url)
}

function submitPollComment(user_id, poll_id, div_name, form_name, comment, email, tpl){
	showPollLoading();
	var url="polling_xmlhttp.php";
	url=url+"?action=comment";
	url=url+"&uid="+user_id;
	url=url+"&tpl="+tpl;
	url=url+"&pid="+poll_id;
	url=url+"&comment="+URLEncode(comment);
	url=url+"&email="+email;
	url=url+"&sid="+Math.random();
	ajaxRequest(url)
}

function showPollResult(poll_id, user_id, tpl) { 
	showPollLoading();
	var url="polling_xmlhttp.php"
	url=url+"?action=show_polls_result"
	url=url+"&pid="+poll_id
	url=url+"&uid="+user_id
	url=url+"&tpl="+tpl
	url=url+"&sid="+Math.random()
	ajaxRequest(url)
} 

function showPollComment(poll_id, user_id, tpl) {
	showPollLoading();
	var url="polling_xmlhttp.php"
	url=url+"?action=show_comment_form"
	url=url+"&uid="+user_id
	url=url+"&tpl="+tpl
	url=url+"&pid="+poll_id
	url=url+"&sid="+Math.random()
	ajaxRequest(url)
} 

function showPollQuestions(user_id, tpl) {
	showPollLoading();
	var url="polling_xmlhttp.php"
	url=url+"?action=show_polls_questions"
	url=url+"&uid="+user_id
	url=url+"&tpl="+tpl
	url=url+"&sid="+Math.random()
	ajaxRequest(url)
} 

function showPollLoading(){
	document.getElementById("poll_votes").innerHTML="<table width='90%' border='0' align='center'><tr><td valign='top' style='color:#000000;font-size:10px;'> <img src='images/loading.gif' />Loading...</td></tr></table>";
}

function ajaxRequest(ref_url) {
    jQuery.ajax({
		url:ref_url,
		dataType: 'xml',
		timeout: 60000,
		error: function(){
			
		},
		success: function(xml) {
			switch (jQuery(xml).find('response').children()[0].tagName){ 
                case 'error':
                    window.location.href = jQuery(xml).find('error').text();
                    break;
                case 'polls':
                    var poll_questions_table = '';
                    poll_questions_table += '<table border="0" width="100%" cellspacing="0" cellpadding="0">';
                    jQuery(xml).find("polls").each(function() { 
                        poll_questions_table += '<tr>';
                        poll_questions_table += '<td align="center">';
                        poll_questions_table += jQuery(xml).find("polls").text();
                        poll_questions_table += '</td>';
                        poll_questions_table += '</tr>';
                    })
                    poll_questions_table += '</table>';
                    document.getElementById("poll_votes").innerHTML=poll_questions_table;
                    break;
                case 'result':
                    document.getElementById("poll_votes").innerHTML=jQuery(xml).find("result").text();
                    break;
                case 'comment':
                    document.getElementById("poll_votes").innerHTML=jQuery(xml).find("comment").text();
                    break;
                default:
                    //
            }
		}
	});
}
