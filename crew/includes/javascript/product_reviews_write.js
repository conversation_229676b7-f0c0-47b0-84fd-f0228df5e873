function checkForm() {
  var error = 0;
  var error_message = "<?php echo JS_ERROR; ?>";

  var review = document.product_reviews_write.review.value;
  var cus_name = document.product_reviews_write.customers_firstname.value;

  
  //if (review.length < <?php //echo REVIEW_TEXT_MIN_LENGTH; ?>) {
    //error_message = error_message + "<?php //echo JS_REVIEW_TEXT; ?>";
    //error = 1;
  //}
  
  if (cus_name.length < 1) {
    error_message = error_message + "<?php echo JS_REVIEW_CUSNAME; ?>";
    error = 1;
  }
  
  if (review.length < 10) {
    error_message = error_message + "<?php echo JS_REVIEW_TEXT; ?>";
    error = 1;
  }
  if ((document.product_reviews_write.rating[0].checked) || (document.product_reviews_write.rating[1].checked) || (document.product_reviews_write.rating[2].checked) || (document.product_reviews_write.rating[3].checked) || (document.product_reviews_write.rating[4].checked)) {
  } else {
    error_message = error_message + "<?php echo JS_REVIEW_RATING; ?>";
    error = 1;
  }

  if (error == 1) {
    alert(error_message);
    return false;
  } else {
    return true;
  }
}

function popupWindow(url) {
  window.open(url,'popupWindow','toolbar=no,location=no,directories=no,status=no,menubar=no,scrollbars=no,resizable=yes,copyhistory=no,width=100,height=100,screenX=150,screenY=150,top=150,left=150')
}