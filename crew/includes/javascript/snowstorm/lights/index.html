<html>
<head>
<title>Smashable Christmas Lights</title>
<link rel="stylesheet" media="screen" href="christmaslights.css" />
<script type="text/javascript" src="soundmanager2-nodebug-jsmin.js"></script>
<script type="text/javascript" src="http://yui.yahooapis.com/combo?2.6.0/build/yahoo-dom-event/yahoo-dom-event.js&2.6.0/build/animation/animation-min.js"></script>
<script type="text/javascript" src="christmaslights.js"></script>
<script type="text/javascript">
var urlBase = './';
soundManager.url = './';
</script>
</head>

<body>

<div>

 <div id="loading">
  <h1>Christmas Light Smashfest 2008: Prototype</h1>
  <h2>Rendering...</h2>
 </div>

 <div id="lights">
  <!-- lights go here -->
 </div>

 <div style="position:absolute;bottom:3px;left:3px">
  <a href="?size=pico">pico</a> | <a href="?size=tiny">tiny</a> | <a href="?size=small">small</a> | <a href="?size=medium">medium</a> | <a href="?size=large">large</a>
 </div>

</div>



</body>
</html>