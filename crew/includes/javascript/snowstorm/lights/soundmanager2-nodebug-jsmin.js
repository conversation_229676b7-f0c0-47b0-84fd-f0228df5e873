/*
   SoundManager 2: Javascript Sound for the Web
   --------------------------------------------
   http://schillmania.com/projects/soundmanager2/

   Copyright (c) 2008, <PERSON>. All rights reserved.
   Code licensed under the BSD License:
   http://schillmania.com/projects/soundmanager2/license.txt

   V2.91a.20081205
*/
var soundManager=null;function SoundManager(b,a){this.flashVersion=8;this.debugMode=false;this.useConsole=true;this.consoleOnly=false;this.waitForWindowLoad=false;this.nullURL="null.mp3";this.allowPolling=true;this.useMovieStar=false;this.useHighPerformance=true;this.bgColor="#ffffff";this.defaultOptions={autoLoad:false,stream:true,autoPlay:false,onid3:null,onload:null,whileloading:null,onplay:null,onpause:null,onresume:null,whileplaying:null,onstop:null,onfinish:null,onbeforefinish:null,onbeforefinishtime:5000,onbeforefinishcomplete:null,onjustbeforefinish:null,onjustbeforefinishtime:200,multiShot:true,position:null,pan:0,volume:100};this.flash9Options={isMovieStar:null,usePeakData:false,useWaveformData:false,useEQData:false};this.movieStarOptions={onmetadata:null,useVideo:false};var e=null;var d=this;this.version=null;this.versionNumber="V2.91a.20081205";this.movieURL=null;this.url=null;this.altURL=null;this.swfLoaded=false;this.enabled=false;this.o=null;this.id=(a||"sm2movie");this.oMC=null;this.sounds={};this.soundIDs=[];this.muted=false;this.isIE=(navigator.userAgent.match(/MSIE/i));this.isSafari=(navigator.userAgent.match(/safari/i));this.isGecko=(navigator.userAgent.match(/gecko/i));this.debugID="soundmanager-debug";this._debugOpen=true;this._didAppend=false;this._appendSuccess=false;this._didInit=false;this._disabled=false;this._windowLoaded=false;this._hasConsole=(typeof console!="undefined"&&typeof console.log!="undefined");this._debugLevels=["log","info","warn","error"];this._defaultFlashVersion=8;this.filePatterns={flash8:/.mp3/i,flash9:/.mp3/i};this.netStreamTypes=["aac","flv","mov","mp4","m4v","f4v","m4a","mp4v","3gp","3g2"];this.netStreamPattern=new RegExp(".("+this.netStreamTypes.join("|")+")","i");this.filePattern=null;this.features={peakData:false,waveformData:false,eqData:false};this.sandbox={type:null,types:{remote:"remote (domain-based) rules",localWithFile:"local with file access (no internet access)",localWithNetwork:"local with network (internet access only, no local access)",localTrusted:"local, trusted (local + internet access)"},description:null,noRemote:null,noLocal:null};this._setVersionInfo=function(){if(d.flashVersion!=8&&d.flashVersion!=9){alert('soundManager.flashVersion must be 8 or 9. "'+d.flashVersion+'" is invalid. Reverting to '+d._defaultFlashVersion+".");d.flashVersion=d._defaultFlashVersion}d.version=d.versionNumber+(d.flashVersion==9?" (AS3/Flash 9)":" (AS2/Flash 8)");if(d.flashVersion>8){d.defaultOptions=d._mergeObjects(d.defaultOptions,d.flash9Options)}if(d.flashVersion>8&&d.useMovieStar){d.defaultOptions=d._mergeObjects(d.defaultOptions,d.movieStarOptions);d.filePatterns.flash9=new RegExp(".(mp3|"+d.netStreamTypes.join("|")+")","i")}else{d.useMovieStar=false}d.filePattern=d.filePatterns[(d.flashVersion!=8?"flash9":"flash8")];d.movieURL=(d.flashVersion==8?"soundmanager2.swf":"soundmanager2_flash9.swf");d.features.peakData=d.features.waveformData=d.features.eqData=(d.flashVersion==9)};this._overHTTP=(document.location?document.location.protocol.match(/http/i):null);this._waitingforEI=false;this._initPending=false;this._tryInitOnFocus=(this.isSafari&&typeof document.hasFocus=="undefined");this._isFocused=(typeof document.hasFocus!="undefined"?document.hasFocus():null);this._okToDisable=!this._tryInitOnFocus;this.useAltURL=!this._overHTTP;this.supported=function(){return(d._didInit&&!d._disabled)};this.getMovie=function(f){return d.isIE?window[f]:(d.isSafari?document.getElementById(f)||document[f]:document.getElementById(f))};this.loadFromXML=function(f){try{d.o._loadFromXML(f)}catch(g){d._failSafely();return true}};this.createSound=function(g){if(!d._didInit){throw new Error("soundManager.createSound(): Not loaded yet - wait for soundManager.onload() before calling sound-related methods")}if(arguments.length==2){g={id:arguments[0],url:arguments[1]}}var h=d._mergeObjects(g);var f=h;if(d._idCheck(f.id,true)){return d.sounds[f.id]}if(d.flashVersion>8&&d.useMovieStar){if(f.isMovieStar===null){f.isMovieStar=(f.url.match(d.netStreamPattern)?true:false)}if(f.isMovieStar&&(f.usePeakData||f.useWaveformData||f.useEQData)){f.usePeakData=false;f.useWaveformData=false;f.useEQData=false}}d.sounds[f.id]=new e(f);d.soundIDs[d.soundIDs.length]=f.id;if(d.flashVersion==8){d.o._createSound(f.id,f.onjustbeforefinishtime)}else{d.o._createSound(f.id,f.url,f.onjustbeforefinishtime,f.usePeakData,f.useWaveformData,f.useEQData,f.isMovieStar,(f.isMovieStar?f.useVideo:false))}if(f.autoLoad||f.autoPlay){window.setTimeout(function(){if(d.sounds[f.id]){d.sounds[f.id].load(f)}},20)}if(f.autoPlay){if(d.flashVersion==8){d.sounds[f.id].playState=1}else{d.sounds[f.id].play()}}return d.sounds[f.id]};this.createVideo=function(f){if(arguments.length==2){f={id:arguments[0],url:arguments[1]}}if(d.flashVersion>=9){f.isMovieStar=true;f.useVideo=true}else{return false}return d.createSound(f)};this.destroySound=function(g,f){if(!d._idCheck(g)){return false}for(var h=0;h<d.soundIDs.length;h++){if(d.soundIDs[h]==g){d.soundIDs.splice(h,1);continue}}d.sounds[g].unload();if(!f){d.sounds[g].destruct()}delete d.sounds[g]};this.destroyVideo=this.destroySound;this.load=function(f,g){if(!d._idCheck(f)){return false}d.sounds[f].load(g)};this.unload=function(f){if(!d._idCheck(f)){return false}d.sounds[f].unload()};this.play=function(f,g){if(!d._idCheck(f)){if(typeof g!="Object"){g={url:g}}if(g&&g.url){g.id=f;d.createSound(g)}else{return false}}d.sounds[f].play(g)};this.start=this.play;this.setPosition=function(f,g){if(!d._idCheck(f)){return false}d.sounds[f].setPosition(g)};this.stop=function(f){if(!d._idCheck(f)){return false}d.sounds[f].stop()};this.stopAll=function(){for(var f in d.sounds){if(d.sounds[f] instanceof e){d.sounds[f].stop()}}};this.pause=function(f){if(!d._idCheck(f)){return false}d.sounds[f].pause()};this.pauseAll=function(){for(var f=d.soundIDs.length;f--;){d.sounds[d.soundIDs[f]].pause()}};this.resume=function(f){if(!d._idCheck(f)){return false}d.sounds[f].resume()};this.resumeAll=function(){for(var f=d.soundIDs.length;f--;){d.sounds[d.soundIDs[f]].resume()}};this.togglePause=function(f){if(!d._idCheck(f)){return false}d.sounds[f].togglePause()};this.setPan=function(f,g){if(!d._idCheck(f)){return false}d.sounds[f].setPan(g)};this.setVolume=function(g,f){if(!d._idCheck(g)){return false}d.sounds[g].setVolume(f)};this.mute=function(f){if(typeof f!="string"){f=null}if(!f){for(var g=d.soundIDs.length;g--;){d.sounds[d.soundIDs[g]].mute()}d.muted=true}else{if(!d._idCheck(f)){return false}d.sounds[f].mute()}};this.muteAll=function(){d.mute()};this.unmute=function(f){if(typeof f!="string"){f=null}if(!f){for(var g=d.soundIDs.length;g--;){d.sounds[d.soundIDs[g]].unmute()}d.muted=false}else{if(!d._idCheck(f)){return false}d.sounds[f].unmute()}};this.unmuteAll=function(){d.unmute()};this.setPolling=function(f){if(!d.o||!d.allowPolling){return false}d.o._setPolling(f)};this.disable=function(f){if(d._disabled){return false}d._disabled=true;for(var g=d.soundIDs.length;g--;){d._disableObject(d.sounds[d.soundIDs[g]])}d.initComplete();d._disableObject(d)};this.canPlayURL=function(f){return(f?(f.match(d.filePattern)?true:false):null)};this.getSoundById=function(g,h){if(!g){throw new Error("SoundManager.getSoundById(): sID is null/undefined")}var f=d.sounds[g];return f};this.onload=function(){soundManager._wD("<em>Warning</em>: soundManager.onload() is undefined.",2)};this.onerror=function(){};this._idCheck=this.getSoundById;var c=function(){return false};c._protected=true;this._disableObject=function(g){for(var f in g){if(typeof g[f]=="function"&&typeof g[f]._protected=="undefined"){g[f]=c}}f=null};this._failSafely=function(){if(!d._disabled){d.disable()}};this._normalizeMovieURL=function(f){if(f){if(f.match(/.swf/)){f=f.substr(0,f.lastIndexOf(".swf"))}if(f.lastIndexOf("/")!=f.length-1){f=f+"/"}}return(f&&f.lastIndexOf("/")!=-1?f.substr(0,f.lastIndexOf("/")+1):"./")+d.movieURL};this._getDocument=function(){return(document.body?document.body:(document.documentElement?document.documentElement:document.getElementsByTagName("div")[0]))};this._getDocument._protected=true;this._createMovie=function(l,j){if(d._didAppend&&d._appendSuccess){return false}if(window.location.href.indexOf("debug=1")+1){d.debugMode=true}d._didAppend=true;d._setVersionInfo();var q=(j?j:d.url);var i=(d.altURL?d.altURL:q);d.url=d._normalizeMovieURL(d._overHTTP?q:i);j=d.url;var k=null;if(d.useHighPerformance&&navigator.userAgent.match(/firefox\/2/i)){k="Warning: disabling highPerformance, incompatible with Firefox 2.x";d.useHighPerformance=false}if(d.useHighPerformance&&d.useMovieStar){k="Warning: disabling highPerformance, not applicable with movieStar mode on";d.useHighPerformance=false}var p={name:l,id:l,src:j,width:"100%",height:"100%",quality:"high",allowScriptAccess:"always",bgcolor:d.bgColor,pluginspage:"http://www.macromedia.com/go/getflashplayer",type:"application/x-shockwave-flash"};var t={id:l,data:j,type:"application/x-shockwave-flash",width:"100%",height:"100%"};var m={movie:j,AllowScriptAccess:"always",quality:"high",bgcolor:d.bgColor};if(d.useHighPerformance&&!d.useMovieStar){p.wmode="transparent";m.wmode="transparent"}var h=null;var o=null;if(d.isIE){h=document.createElement("div");var f='<object id="'+l+'" data="'+j+'" type="application/x-shockwave-flash" width="100%" height="100%"><param name="movie" value="'+j+'" /><param name="AllowScriptAccess" value="always" /><param name="quality" value="high" />'+(d.useHighPerformance&&!d.useMovieStar?'<param name="wmode" value="transparent" /> ':"")+'<param name="bgcolor" value="'+d.bgColor+'" /><!-- --></object>'}else{h=document.createElement("embed");for(o in p){if(p.hasOwnProperty(o)){h.setAttribute(o,p[o])}}}var n="soundManager._createMovie(): appendChild/innerHTML set failed. May be app/xhtml+xml DOM-related.";var g=d._getDocument();if(g){d.oMC=document.getElementById("sm2-container")?document.getElementById("sm2-container"):document.createElement("div");if(!d.oMC.id){d.oMC.id="sm2-container";d.oMC.className="movieContainer";var v=null;if(d.useHighPerformance){v={position:"fixed",width:"8px",height:"8px",bottom:"0px",left:"0px",zIndex:-1}}else{v={position:"absolute",width:"1px",height:"1px",bottom:"0px",left:"0px"}}var u=null;for(u in v){if(v.hasOwnProperty(u)){d.oMC.style[u]=v[u]}}try{if(!d.isIE){d.oMC.appendChild(h)}g.appendChild(d.oMC);if(d.isIE){d.oMC.innerHTML=f}d._appendSuccess=true}catch(r){throw new Error(n)}}else{d.oMC.appendChild(h);if(d.isIE){d.oMC.innerHTML=f}d._appendSuccess=true}g=null}};this._writeDebug=function(f,h,g){};this._writeDebug._protected=true;this._wD=this._writeDebug;this._toggleDebug=function(){};this._toggleDebug._protected=true;this._debug=function(){};this._mergeObjects=function(g,f){var k={};for(var h in g){if(g.hasOwnProperty(h)){k[h]=g[h]}}var j=(typeof f=="undefined"?d.defaultOptions:f);for(var l in j){if(j.hasOwnProperty(l)&&typeof k[l]=="undefined"){k[l]=j[l]}}return k};this.createMovie=function(f){if(f){d.url=f}d._initMovie()};this.go=this.createMovie;this._initMovie=function(){if(d.o){return false}d.o=d.getMovie(d.id);if(!d.o){d._createMovie(d.id,d.url);d.o=d.getMovie(d.id)}};this.waitForExternalInterface=function(){if(d._waitingForEI){return false}d._waitingForEI=true;if(d._tryInitOnFocus&&!d._isFocused){return false}setTimeout(function(){if(!d._didInit&&d._okToDisable){d._failSafely()}},750)};this.handleFocus=function(){if(d._isFocused||!d._tryInitOnFocus){return true}d._okToDisable=true;d._isFocused=true;if(d._tryInitOnFocus){window.removeEventListener("mousemove",d.handleFocus,false)}d._waitingForEI=false;setTimeout(d.waitForExternalInterface,500);if(window.removeEventListener){window.removeEventListener("focus",d.handleFocus,false)}else{if(window.detachEvent){window.detachEvent("onfocus",d.handleFocus)}}};this.initComplete=function(){if(d._didInit){return false}d._didInit=true;if(d._disabled){d.onerror.apply(window);return false}if(d.waitForWindowLoad&&!d._windowLoaded){if(window.addEventListener){window.addEventListener("load",d.initUserOnload,false)}else{if(window.attachEvent){window.attachEvent("onload",d.initUserOnload)}}return false}else{d.initUserOnload()}};this.initUserOnload=function(){d.onload.apply(window)};this.init=function(){d._initMovie();if(d._didInit){return false}if(window.removeEventListener){window.removeEventListener("load",d.beginDelayedInit,false)}else{if(window.detachEvent){window.detachEvent("onload",d.beginDelayedInit)}}try{d.o._externalInterfaceTest(false);d.setPolling(true);d.enabled=true}catch(f){d._failSafely();d.initComplete();return false}d.initComplete()};this.beginDelayedInit=function(){d._windowLoaded=true;setTimeout(d.waitForExternalInterface,500);setTimeout(d.beginInit,20)};this.beginInit=function(){if(d._initPending){return false}d.createMovie();d._initMovie();d._initPending=true;return true};this.domContentLoaded=function(){if(document.removeEventListener){document.removeEventListener("DOMContentLoaded",d.domContentLoaded,false)}d.go()};this._externalInterfaceOK=function(){if(d.swfLoaded){return false}d.swfLoaded=true;d._tryInitOnFocus=false;if(d.isIE){setTimeout(d.init,100)}else{d.init()}};this._setSandboxType=function(f){var g=d.sandbox;g.type=f;g.description=g.types[(typeof g.types[f]!="undefined"?f:"unknown")];if(g.type=="localWithFile"){g.noRemote=true;g.noLocal=false}else{if(g.type=="localWithNetwork"){g.noRemote=false;g.noLocal=true}else{if(g.type=="localTrusted"){g.noRemote=false;g.noLocal=false}}}};this.destruct=function(){d.disable(true)};e=function(f){var g=this;this.sID=f.id;this.url=f.url;this.options=d._mergeObjects(f);this.instanceOptions=this.options;this._iO=this.instanceOptions;this._debug=function(){};this._debug();this.id3={};this.resetProperties=function(h){g.bytesLoaded=null;g.bytesTotal=null;g.position=null;g.duration=null;g.durationEstimate=null;g.loaded=false;g.playState=0;g.paused=false;g.readyState=0;g.muted=false;g.didBeforeFinish=false;g.didJustBeforeFinish=false;g.instanceOptions={};g.instanceCount=0;g.peakData={left:0,right:0};g.waveformData=[];g.eqData=[]};g.resetProperties();this.load=function(h){if(typeof h!="undefined"){g._iO=d._mergeObjects(h);g.instanceOptions=g._iO}else{h=g.options;g._iO=h;g.instanceOptions=g._iO}if(typeof g._iO.url=="undefined"){g._iO.url=g.url}if(g._iO.url==g.url&&g.readyState!==0&&g.readyState!=2){return false}g.loaded=false;g.readyState=1;g.playState=(h.autoPlay?1:0);try{if(d.flashVersion==8){d.o._load(g.sID,g._iO.url,g._iO.stream,g._iO.autoPlay,(g._iO.whileloading?1:0))}else{d.o._load(g.sID,g._iO.url,g._iO.stream?true:false,g._iO.autoPlay?true:false);if(g._iO.isMovieStar&&g._iO.autoLoad&&!g._iO.autoPlay){g.pause()}}}catch(i){d.onerror();d.disable()}};this.unload=function(){if(g.readyState!==0){if(g.readyState!=2){g.setPosition(0)}d.o._unload(g.sID,d.nullURL);g.resetProperties()}};this.destruct=function(){d.o._destroySound(g.sID);d.destroySound(g.sID,true)};this.play=function(i){if(!i){i={}}g._iO=d._mergeObjects(i,g._iO);g._iO=d._mergeObjects(g._iO,g.options);g.instanceOptions=g._iO;if(g.playState==1){var h=g._iO.multiShot;if(!h){return false}}if(!g.loaded){if(g.readyState===0){g._iO.stream=true;g._iO.autoPlay=true;g.load(g._iO)}else{if(g.readyState==2){return false}}}if(g.paused){g.resume()}else{g.playState=1;if(!g.instanceCount||d.flashVersion==9){g.instanceCount++}g.position=(typeof g._iO.position!="undefined"&&!isNaN(g._iO.position)?g._iO.position:0);if(g._iO.onplay){g._iO.onplay.apply(g)}g.setVolume(g._iO.volume);g.setPan(g._iO.pan);d.o._start(g.sID,g._iO.loop||1,(d.flashVersion==9?g.position:g.position/1000))}};this.start=this.play;this.stop=function(h){if(g.playState==1){g.playState=0;g.paused=false;if(g._iO.onstop){g._iO.onstop.apply(g)}d.o._stop(g.sID,h);g.instanceCount=0;g._iO={}}};this.setPosition=function(h){if(typeof h=="undefined"){h=0}g._iO.position=h;d.o._setPosition(g.sID,(d.flashVersion==9?g._iO.position:g._iO.position/1000),(g.paused||!g.playState))};this.pause=function(){if(g.paused||g.playState===0){return false}g.paused=true;d.o._pause(g.sID);if(g._iO.onpause){g._iO.onpause.apply(g)}};this.resume=function(){if(!g.paused||g.playState===0){return false}g.paused=false;d.o._pause(g.sID);if(g._iO.onresume){g._iO.onresume.apply(g)}};this.togglePause=function(){if(!g.playState){g.play({position:(d.flashVersion==9?g.position:g.position/1000)});return false}if(g.paused){g.resume()}else{g.pause()}};this.setPan=function(h){if(typeof h=="undefined"){h=0}d.o._setPan(g.sID,h);g._iO.pan=h};this.setVolume=function(h){if(typeof h=="undefined"){h=100}d.o._setVolume(g.sID,(d.muted&&!g.muted)||g.muted?0:h);g._iO.volume=h};this.mute=function(){g.muted=true;d.o._setVolume(g.sID,0)};this.unmute=function(){g.muted=false;d.o._setVolume(g.sID,typeof g._iO.volume!="undefined"?g._iO.volume:g.options.volume)};this._whileloading=function(h,i,j){if(!g._iO.isMovieStar){g.bytesLoaded=h;g.bytesTotal=i;g.duration=Math.floor(j);g.durationEstimate=parseInt((g.bytesTotal/g.bytesLoaded)*g.duration,10);if(g.readyState!=3&&g._iO.whileloading){g._iO.whileloading.apply(g)}}else{g.bytesLoaded=h;g.bytesTotal=i;g.duration=Math.floor(j);g.durationEstimate=g.duration;if(g.readyState!=3&&g._iO.whileloading){g._iO.whileloading.apply(g)}}};this._onid3=function(m,h){var n=[];for(var l=0,k=m.length;l<k;l++){n[m[l]]=h[l]}g.id3=d._mergeObjects(g.id3,n);if(g._iO.onid3){g._iO.onid3.apply(g)}};this._whileplaying=function(i,j,h,k){if(isNaN(i)||i===null){return false}g.position=i;if(g._iO.usePeakData&&typeof j!="undefined"&&j){g.peakData={left:j.leftPeak,right:j.rightPeak}}if(g._iO.useWaveformData&&typeof h!="undefined"&&h){g.waveformData=h}if(g._iO.useEQData&&typeof k!="undefined"&&k){g.eqData=k}if(g.playState==1){if(g._iO.whileplaying){g._iO.whileplaying.apply(g)}if(g.loaded&&g._iO.onbeforefinish&&g._iO.onbeforefinishtime&&!g.didBeforeFinish&&g.duration-g.position<=g._iO.onbeforefinishtime){g._onbeforefinish()}}};this._onload=function(h){h=(h==1?true:false);g.loaded=h;g.readyState=h?3:2;if(g._iO.onload){g._iO.onload.apply(g)}};this._onbeforefinish=function(){if(!g.didBeforeFinish){g.didBeforeFinish=true;if(g._iO.onbeforefinish){g._iO.onbeforefinish.apply(g)}}};this._onjustbeforefinish=function(h){if(!g.didJustBeforeFinish){g.didJustBeforeFinish=true;if(g._iO.onjustbeforefinish){g._iO.onjustbeforefinish.apply(g)}}};this._onfinish=function(){g.playState=0;g.paused=false;if(g._iO.onfinish){g._iO.onfinish.apply(g)}if(g._iO.onbeforefinishcomplete){g._iO.onbeforefinishcomplete.apply(g)}g.didBeforeFinish=false;g.didJustBeforeFinish=false;if(g.instanceCount){g.instanceCount--;if(!g.instanceCount){g.setPosition(0);g.instanceCount=0;g.instanceOptions={}}}else{g.setPosition(0)}};this._onmetadata=function(h){if(!h.width&&!h.height){h.width=320;h.height=240}g.metadata=h;g.width=h.width;g.height=h.height;if(g._iO.onmetadata){g._iO.onmetadata.apply(g)}d.wD("SMSound.onmetadata() complete")}};if(window.addEventListener){window.addEventListener("focus",d.handleFocus,false);window.addEventListener("load",d.beginDelayedInit,false);window.addEventListener("unload",d.destruct,false);if(d._tryInitOnFocus){window.addEventListener("mousemove",d.handleFocus,false)}}else{if(window.attachEvent){window.attachEvent("onfocus",d.handleFocus);window.attachEvent("onload",d.beginDelayedInit);window.attachEvent("unload",d.destruct)}else{soundManager.onerror();soundManager.disable()}}if(document.addEventListener){document.addEventListener("DOMContentLoaded",d.domContentLoaded,false)}}soundManager=new SoundManager();