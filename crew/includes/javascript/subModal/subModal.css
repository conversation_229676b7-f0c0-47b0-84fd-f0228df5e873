/**
 * POPUP CONTAINER STYLES
 */
#popupMask {
	position: absolute;
	z-index: 200;
	top: -5px;
	left: 0px;
	width: 100%;
	height: 150%;
	opacity: .75;
	filter: alpha(opacity=75)!important;
	/* this hack is so it works in IE
	 * I find setting the color in the css gives me more flexibility 
	 * than the PNG solution.
	 */
	background-color:transparent !important;
	background-color: #333333;
	/* this hack is for opera support
	 * you can uncomment the background-image if you don't care about opera.
	 * this gives you the flexibility to use any bg color that you want, instead of the png
	 */
	background-image: url("maskBG-1.png") !important; // For browsers Moz, Opera, etc.
	background-repeat: repeat;
	display:none;
}
#popupContainer {
	position: absolute;
	z-index: 201;
	top: 0px;
	left: 0px;
	display:none;
	padding: 0px;
}

#popupInner {
	border: 5px solid #ffffff;
	background-color: #bfc6c4;
	font-family: Arial, Tahoma, Verdana, Helvetica, sans-serif;
	font-size: 12px;
	color: #333333;
	text-align: left;
}

#popupFrame {
	margin: 0.3cm;
	width: 100%;
	height: 100%;
	padding: 1px;
	position: relative;
	z-index: 202;
}

#popupTitle {
	color: #333333;
}

#popupTitle p {
	font-size: 1.2em;
	font-weight: bold;
	text-align: left;
	margin-top: 0.5cm;
	margin-bottom: 0.3cm;
}

#popupMesg {
	text-align: left;
	color: #333333;
}

#popupMesgTop {
	border-top: 1px solid #000000;
	color: #333333;
}

#popupMesgTop p {
	text-align: left;
	margin-top: 0.3cm;
	margin-bottom: 0.3cm;
}

#popupMesgBottom {
	border-bottom: 1px solid #000000;
	color: #333333;
}

#popupMesgBottom p {
	text-align: right;
	margin-top: 0.6cm;
	margin-bottom: 0.4cm;
}

#popupBottom {
	text-align: center;
	color: #333333;
}

#popupBottom p {
	margin-top: 0.5cm;
	margin-bottom: 0.7cm;
}

#popupBottom a {
	font-family: Arial, Tahoma, Verdana, Helvetica, sans-serif;
	font-weight: bold;
	font-size: 12px;
	color: #00619F;
}

#popupControls {
	float: right;
	cursor: pointer;
	cursor: hand;
	font-size: 11px;
	color: #333333;
	font-weight: bold;
	z-index: 203;
	margin-top: 0.2cm;
}

