<script language="javascript"><!--

function onBuybackGameSelection(game_cat_id, languages_id, server_product_id_autoselect) {
	document.getElementById('table_heading_product_unit_name').innerHTML = '';
	
    var wbb_div_server_listing = DOMCall('wbb_div_server_listing');
	var div_game_reopen_count_down_obj = DOMCall('div_game_reopen_count_down');
	
	div_game_reopen_count_down_obj.innerHTML = '';
	
	var server_action = 'get_buyback_server_list';
	var ref_url = "supplier_xmlhttp.php?action="+server_action+"&buyback_cat_id="+game_cat_id+"&slang="+languages_id+"&buyback_products_id="+server_product_id_autoselect;
	
	jQuery.ajax({
		url:ref_url,
		dataType: 'xml',
		timeout: 80000,
		error: function(){
			
		},
		success: function(xml) {
			jQuery("#server_listing_loading").css({display: "none"});
			
			//No servers, etc
			var msg_id = 0;
      	   	if (jQuery(xml).find("servers > error").length) {
      			msg_id = jQuery(xml).find("servers > error").text();
      		}

      	    if (msg_id == 0) {
      	    	var game = jQuery(xml).find("game").text();
      	    	if (jQuery(xml).find("game > list_status_msg").length) {
      	    		div_game_reopen_count_down_obj.innerHTML = jQuery(xml).find("game > list_status_msg").text();
	      		}
				
				var server_arr = jQuery(xml).find("server");
				document.getElementById('game_name').innerHTML = '[ <b>' + jQuery(xml).find("game_name").text() + '</b> ] - ';
				
				if (server_arr.length > 0) {
					document.getElementById('table_heading_product_unit_name').innerHTML = server_arr[0].getElementsByTagName('qty_unit')[0].firstChild.nodeValue;
					wbb_div_server_listing.innerHTML = '';
					divhtml = '';
					divhtml = '<table border="0" cellpadding="0" cellspacing="0" width="100%">';
	      		    for (var j=0; j<server_arr.length; j++) {
	          		    var products_id = server_arr[j].getElementsByTagName('id')[0].firstChild.nodeValue;
	          		    var products_name = server_arr[j].getElementsByTagName('name')[0].firstChild.nodeValue;
	          		    var products_unit_price = server_arr[j].getElementsByTagName('unit_price')[0].firstChild.nodeValue;
	          		    var min_qty = server_arr[j].getElementsByTagName('min_qty')[0].firstChild.nodeValue;
	          		    var max_qty = server_arr[j].getElementsByTagName('max_qty')[0].firstChild.nodeValue;
	          		    var products_is_buyback_int = server_arr[j].getElementsByTagName('is_buyback')[0].firstChild.nodeValue;
	          		    if (products_is_buyback_int == '1') {
	          		    	var products_is_buyback_bool = true;
	          		    } else {
	          		    	var products_is_buyback_bool = false;
	          		    }
						//update the div
						add_server_listing_row(products_name, products_unit_price, products_is_buyback_bool ? min_qty+'-'+max_qty : '&nbsp;', game_cat_id, products_id, products_is_buyback_int);
	    	        }
	    	        divhtml += '</table>';
	    	        show_server_listing_div();
				} else {
					wbb_div_server_listing.innerHTML = '<span class="title-text"><?=TEXT_NO_ITEMS_TO_BUYBACK?></span>';
				}
      		} else {
      			wbb_div_server_listing.innerHTML = '<span class="title-text"><?=TEXT_NO_ITEMS_TO_BUYBACK?></span>';
      		}
		}
	});
	
}

function onOrderHistoryRefresh(order_status_id, product_type, order_no, game_cat_id, start_date, end_date, languages_id) {
	var server_action = 'search_order_history';
	var ref_url = "supplier_xmlhttp.php?action="+server_action+"&order_status_id="+order_status_id+"&product_type="+product_type+"&order_no="+URLEncode(order_no)+"&game_cat_id="+URLEncode(game_cat_id)+"&start_date="+URLEncode(start_date)+"&end_date="+URLEncode(end_date);
	
	var odh_div_search_results = DOMCall('odh_div_search_results');
	
	odh_div_search_results.innerHTML = '<br><p class="title-text" align="center" style="text-align:center;">'+global_loading_message+'<\/p>';
	
	
	jQuery.ajax({
		url:ref_url,
		dataType: 'xml',
		timeout: 60000,
		error: function(){
			
		},
		success: function(xml) {
			document.getElementById('num_results').innerHTML = jQuery(xml).find('num_results').text();
			
			var row_arr = jQuery(xml).find('row');
			if (row_arr.length > 0) {
				odh_div_search_results.innerHTML = '';
				divhtml = '';
      		    for (var j = 0; j < row_arr.length; j++) {
					//update the div
					add_search_results_row(row_arr[j], order_status_id, (j%2));
    	        }
    	        
    	        show_search_results_div();
    	        toggleCountdown(1);
				disableDivInputs(false);
			} else {
				odh_div_search_results.innerHTML = '<span class="title-text"><?=TEXT_NO_RESULTS?></span>';
			}
		}
	});
}

function show_payment_report(payment_id) {
	document.getElementById('wbb_div_msgField_'+payment_id).innerHTML = 'Loading...';
	
	var server_action = 'show_payment_report';
	var ref_url = "supplier_xmlhttp.php?action="+server_action+"&payment_id="+payment_id;
	
	jQuery.ajax({
		url: ref_url,
		dataType: 'html',
		success: function(response_data) {
			jQuery('#general_content').html(response_data);
			show_fancybox('general_popup_box');
			document.getElementById('wbb_div_msgField_'+payment_id).innerHTML = '';
		}
	});
	jQuery('#show_payment_report').empty();
}

function onFavLinksGameSelection(gameListObj, languages_id, server_product_id_autoselect) {
	var game_cat_id = gameListObj.value;
	var server_action = 'get_buyback_server_list';

    var ref_url = "supplier_xmlhttp.php?action="+server_action+"&buyback_cat_id="+game_cat_id+"&slang="+languages_id;

   	document.getElementById('fvl_div_msgField1').innerHTML = '<table border="0"><tr><td class="main" style="text-align:center;">'+global_loading_message+'</td></tr></table>';

	var dependantInputs = new Array('fvl_input_game_select', 'fvl_input_product_select');
	disableElement(dependantInputs, true);

    var dependantElements = new Array('fvl_tbody_step2', 'fvl_tbody_error', 'fvl_tbody_notice');
    changeElementDisplay(dependantElements, 'hide');

    var server_selection = DOMCall('fvl_input_product_select');
    var fvl_div_game_cat_id = DOMCall('fvl_div_game_cat_id');

	var serverListInput = new Array('fvl_input_product_select');
	var gameListInput = new Array('fvl_input_game_select');

    if (game_cat_id == 0) {
		//clear server select list
		clearOptionList(server_selection);
        appendToSelect(server_selection, '0', '--');
		disableElement(serverListInput, true);
		disableElement(gameListInput, false);
		document.getElementById('fvl_div_msgField1').innerHTML = '';
		return;
	}
    
    jQuery.ajax({
		url:ref_url,
		dataType: 'xml',
		timeout: 60000,
		error: function(){
			
		},
		success: function(xml) {
            clearOptionList(server_selection);
			
            //No servers, etc
            var msg_id = 0;
			var error_msg_obj = jQuery(xml).find('error').text();
      		if (typeof (error_msg_obj) != 'undefined' && error_msg_obj != null) {
      			var msg_id = parseInt(error_msg_obj);
      		}
			
			//check if we need to bother updating the server list div
			if (game_cat_id != fvl_div_game_cat_id.value) {
				fvl_div_game_cat_id.value = game_cat_id;
			}
      	    
      	    if (msg_id > 0) {
          	    showError(msg_id);
				disableElement(serverListInput, true);
                appendToSelect(server_selection, '0', '--');
				disableElement(gameListInput, false);
      	    } else {
				var server_arr = jQuery(xml).find('server');
      		    for (var j=0; j<server_arr.length; j++) {
          		    var scat_id = server_arr[j].getElementsByTagName('id')[0].firstChild.nodeValue;
          		    var scat_name = server_arr[j].getElementsByTagName('name')[0].firstChild.nodeValue;
          		    //update the dropdown
                    appendToSelect(server_selection, scat_id, scat_name);
    	        }
    	        disableElement(dependantInputs, false);
    	        document.getElementById('fvl_tbody_step2').className = 'show';
      		}
      		
        	document.getElementById('fvl_div_msgField1').innerHTML = '';
        	
        	if (server_product_id_autoselect > 0) {
				server_selection.value = server_product_id_autoselect;
				onBuybackServerSelection(server_selection, languages_id);
        	}
		}
	});
}
//--></script>