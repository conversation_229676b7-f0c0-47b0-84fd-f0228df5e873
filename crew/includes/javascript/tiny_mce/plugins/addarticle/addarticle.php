<?php
require('../../../../configure.php');
require('../../../../functions/database.php');
require('../../../../database_tables.php');

// make a connection to the database... now
tep_db_connect() or die('Unable to connect to database server!');

$language_obj = array();

$sql = "SELECT configuration_value FROM ".TABLE_CONFIGURATION." WHERE configuration_key = 'DEFAULT_LANGUAGE'";
$result_sql = tep_db_query($sql);
$configuration_row = tep_db_fetch_array($result_sql);

$default_language = $configuration_row['configuration_value'];

$sql = "SELECT * FROM ".TABLE_LANGUAGES." ORDER BY sort_order ASC";
$result_sql = tep_db_query($sql);
while ($language_rows = tep_db_fetch_array($result_sql)) {
	$defaultflaq = ($default_language == $language_rows['code']) ? "default" : "";
	
	$row_obj = array();
	$row_obj = array( 'code' => $language_rows['code'],
	'languages_id' => $language_rows['languages_id'],
	'name' => $language_rows['name'],
	'default' => $defaultflaq);

	if ($defaultflaq == "default")
		$defaultlanguages_id = $language_rows['languages_id'];

	$language_obj[] = $row_obj; 
}

$sql1 = "SELECT DISTINCT cms_menu_type FROM ".TABLE_CMS_MENU." ORDER BY cms_menu_type ASC";
$result_sql1 = tep_db_query($sql1);
while ($rows1 = tep_db_fetch_array($result_sql1)) {
	$menu_type_options [] = array ('id' => $rows1["cms_menu_type"], 'text' => $rows1["cms_menu_type"]);
}

?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>{#addarticle_dlg.title}</title>
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css">
<script language="JavaScript" src="../../../jquery.js"></script>
<script type="text/javascript">

jQuery.noConflict();

</script>
<script type="text/javascript" src="../../tiny_mce_popup.js"></script>
<script type="text/javascript" src="js/addarticle.js"></script>
<script type="text/javascript" src="../../utils/form_utils.js"></script>
</head>
<body>
<form name="addarticle" onsubmit="aa_check_form();return false;" action="#">
<table border="0" cellpadding="4" cellspacing="0">
	<tr>
	  <td nowrap="nowrap">Article Title : </td>
	  <td>
			<td><input id="aa_menu_title" name="aa_menu_title" type="text" value="" size="50" /></td>
		</td>
	</tr>
	<tr>
	  <td nowrap="nowrap">SEO URL Alias :</td>
	  <td>
			<td><input id="aa_seo_url" name="aa_seo_url" type="text" value="" size="50" /></td>
		</td>
	</tr>
</table>
<span class="fieldRequired">* All field is required</span>
<br><br>
<center>
	<input type="button" id="insert" name="insert" value="{#insert}" onclick="aa_check_form();return false;" />
	<input type="button" id="cancel" name="cancel" value="{#cancel}" onclick="tinyMCEPopup.close();" />
</center>
<input type=hidden id="languages_id" name="languages_id" value="<?php echo $defaultlanguages_id; ?>">
<input type=hidden id="menu_id" name="menu_id" value="">
<input type=hidden id="href" name="href" value="">
<input type=hidden name="menu_type" id="menu_type" value="3">
</form>
</body>
</html>