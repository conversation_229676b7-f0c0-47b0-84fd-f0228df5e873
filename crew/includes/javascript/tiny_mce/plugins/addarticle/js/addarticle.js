/* Functions for the addarticle plugin popup */

tinyMCEPopup.requireLangPack();

function preinit() {
	var url;

	if (url = tinyMCEPopup.getParam("external_link_list_url"))
		document.write('<script language="javascript" type="text/javascript" src="' + tinyMCEPopup.editor.documentBaseURI.toAbsolute(url) + '"></script>');
}

function changeClass() {
	var formObj = document.forms[0];
	formObj.classes.value = getSelectValue(formObj, 'classlist');
}

function init() {
	tinyMCEPopup.resizeToInnerSize();

	var formObj = document.forms[0];
	var inst = tinyMCEPopup.editor;
	var elm = inst.selection.getNode();
	var action = "insert";
	var html;
	var temptitle;

	elm = inst.dom.getParent(elm, "A");
	if (elm != null && elm.nodeName == "A")
		action = "update";

	formObj.insert.value = tinyMCEPopup.getLang(action, 'Insert', true); 

	if (action == "update") {
	}
	else {
		temptitle = inst.selection.getContent({format : 'text'});

		// Setup form data
		setFormValue('aa_menu_title', temptitle);
		setFormValue('aa_seo_url', temptitle);
	}

	window.focus();
}

function setFormValue(name, value) {
	document.forms[0].elements[name].value = value;
}

function setAttrib(elm, attrib, value) {
	var formObj = document.forms[0];
	var valueElm = formObj.elements[attrib.toLowerCase()];

	if (typeof(value) == "undefined" || value == null) {
		value = "";

		if (valueElm)
			value = valueElm.value;
	}

	if (value != "") {
		elm.setAttribute(attrib.toLowerCase(), value);

		if (attrib == "style")
			attrib = "style.cssText";

		if (attrib.substring(0, 2) == 'on')
			value = 'return true;' + value;

		if (attrib == "class")
			attrib = "className";

		eval('elm.' + attrib + "=value;");
	} else
		elm.removeAttribute(attrib);
}

function insertAction() {
	var menutype = "";
	var formObj = document.forms[0];

	menutype = document.forms[0].menu_type.value;

	var url = "../../../../../menu_management.php?action=addarticle&languages_id="+escape(document.forms[0].languages_id.value)+"&menu_type="+escape(menutype)+"&menu_title="+escape(document.forms[0].aa_menu_title.value)+"&seo_url="+escape(document.forms[0].aa_seo_url.value);
	jQuery.get(url , function(data){
		if (data) {
			jQuery('#href').val(data);
			insertAction1(data);
		}
	});
}

function insertAction1(data) {
	var inst = tinyMCEPopup.editor;
	var elm, elementArray, i, flag = "no";

	elm = inst.selection.getNode();

	elm = inst.dom.getParent(elm, "A");
	tinyMCEPopup.execCommand("mceBeginUndoLevel");

	// Create new anchor elements

	tinyMCEPopup.execCommand("CreateLink", false, "#mce_temp_url#");

	elementArray = tinymce.grep(inst.dom.select("a"), function(n) {return inst.dom.getAttrib(n, 'href') == '#mce_temp_url#';});
	for (i=0; i<elementArray.length; i++) {
		elm = elementArray[i];

		// Move cursor to end
		try {
			tinyMCEPopup.editor.selection.collapse(false);
		} catch (ex) {
			// Ignore
		}

		setAllAttribs(elm,data);
	}

	tinyMCEPopup.execCommand("mceEndUndoLevel");
	tinyMCEPopup.close();
}

function setAllAttribs(elm,data) {
	var formObj = document.forms[0];
	var href = data;
	var menu_title = formObj.aa_menu_title.value;

	setAttrib(elm, 'href', href);
	setAttrib(elm, 'mce_href', href);
	setAttrib(elm, 'title' , menu_title);

	// Refresh in old MSIE
	if (tinyMCE.isMSIE5)
		elm.outerHTML = elm.outerHTML;
}

function getSelectValue(form_obj, field_name) {
	var elm = form_obj.elements[field_name];

	if (elm == null || elm.options == null)
		return "";

	return elm.options[elm.selectedIndex].value;
}

function aa_check_form() {
	var aa_error = 0;
	var aa_error_message = "Errors have occured during the process of your form!\nPlease make the following corrections:\n\n";

	if (document.addarticle.aa_menu_title.value == "") {
		aa_error_message = aa_error_message + "* The 'Menu Title' entry must be entered.\n";
		aa_error = 1;
	}

	if (aa_error == 1) {
		alert(aa_error_message);
		return false;
	}
	else {
		insertAction();
	}
}

// While loading
preinit();
tinyMCEPopup.onInit.add(init);
