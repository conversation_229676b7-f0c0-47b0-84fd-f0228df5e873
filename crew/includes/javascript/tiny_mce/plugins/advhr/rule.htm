<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
	<title>{#advhr.advhr_desc}</title>
	<script type="text/javascript" src="../../tiny_mce_popup.js"></script>
	<script type="text/javascript" src="js/rule.js"></script>
	<script type="text/javascript" src="../../utils/mctabs.js"></script>
	<script type="text/javascript" src="../../utils/form_utils.js"></script>
	<link href="css/advhr.css" rel="stylesheet" type="text/css" />
	<base target="_self" />
</head>
<body>
<form onsubmit="AdvHRDialog.update();return false;" action="#">
	<div class="tabs">
		<ul>
			<li id="general_tab" class="current"><span><a href="javascript:mcTabs.displayTab('general_tab','general_panel');" onmousedown="return false;">{#advhr.advhr_desc}</a></span></li>
		</ul>
	</div>

	<div class="panel_wrapper">
		<div id="general_panel" class="panel current">
			<table border="0" cellpadding="4" cellspacing="0">
                    <tr>
                        <td><label for="width">{#advhr_dlg.width}</label></td>
                        <td nowrap="nowrap">
                            <input id="width" name="width" type="text" value="" />
                            <select name="width2" id="width2">
                                <option value="">px</option>
                                <option value="%">%</option>
                            </select>
                        </td>
                    </tr>
                    <tr>
                        <td><label for="size">{#advhr_dlg.size}</label></td>
                        <td><select id="size" name="size">
                            <option value="">Normal</option>
                            <option value="1">1</option>
                            <option value="2">2</option>
                            <option value="3">3</option>
                            <option value="4">4</option>
                            <option value="5">5</option>
                        </select></td>
                    </tr>
                    <tr>
                        <td><label for="noshade">{#advhr_dlg.noshade}</label></td>
                        <td><input type="checkbox" name="noshade" id="noshade" class="radio" /></td>
                    </tr>
            </table>
		</div>
	</div>

	<div class="mceActionPanel">
		<div style="float: left">
			<input type="button" id="insert" name="insert" value="{#insert}" onclick="AdvHRDialog.update();" />
		</div>

		<div style="float: right">
			<input type="button" id="cancel" name="cancel" value="{#cancel}" onclick="tinyMCEPopup.close();" />
		</div>
	</div>
</form>
</body>
</html>
