<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
	<title>{#fullpage_dlg.title}</title>
	<script type="text/javascript" src="../../tiny_mce_popup.js"></script>
	<script type="text/javascript" src="../../utils/mctabs.js"></script>
	<script type="text/javascript" src="../../utils/form_utils.js"></script>
	<script type="text/javascript" src="js/fullpage.js"></script>
	<link href="css/fullpage.css" rel="stylesheet" type="text/css" />
	<base target="_self" />
</head>
<body id="advlink" style="display: none">
    <form onsubmit="updateAction();return false;" name="fullpage" action="#">
		<div class="tabs">
			<ul>
				<li id="meta_tab" class="current"><span><a href="javascript:mcTabs.displayTab('meta_tab','meta_panel');" onmousedown="return false;">{#fullpage_dlg.meta_tab}</a></span></li>
				<li id="appearance_tab"><span><a href="javascript:mcTabs.displayTab('appearance_tab','appearance_panel');" onmousedown="return false;">{#fullpage_dlg.appearance_tab}</a></span></li>
				<li id="advanced_tab"><span><a href="javascript:mcTabs.displayTab('advanced_tab','advanced_panel');" onmousedown="return false;">{#fullpage_dlg.advanced_tab}</a></span></li>
			</ul>
		</div>

		<div class="panel_wrapper">
			<div id="meta_panel" class="panel current">
				<fieldset>
					<legend>{#fullpage_dlg.meta_props}</legend>

					<table border="0" cellpadding="4" cellspacing="0">
						<tr>
							<td nowrap="nowrap"><label for="metatitle">{#fullpage_dlg.meta_title}</label>&nbsp;</td>
							<td><input type="text" id="metatitle" name="metatitle" value="" /></td>
						</tr>
						<tr>
							<td nowrap="nowrap"><label for="metakeywords">{#fullpage_dlg.meta_keywords}</label>&nbsp;</td>
							<td><textarea id="metakeywords" name="metakeywords" rows="4"></textarea></td>
						</tr>
						<tr>
							<td nowrap="nowrap"><label for="metadescription">{#fullpage_dlg.meta_description}</label>&nbsp;</td>
							<td><textarea id="metadescription" name="metadescription" rows="4"></textarea></td>
						</tr>
						<tr>
							<td nowrap="nowrap"><label for="metaauthor">{#fullpage_dlg.author}</label>&nbsp;</td>
							<td><input type="text" id="metaauthor" name="metaauthor" value="" /></td>
						</tr>
						<tr>
							<td nowrap="nowrap"><label for="metacopyright">{#fullpage_dlg.copyright}</label>&nbsp;</td>
							<td><input type="text" id="metacopyright" name="metacopyright" value="" /></td>
						</tr>
						<tr>
							<td nowrap="nowrap"><label for="metarobots">{#fullpage_dlg.meta_robots}</label>&nbsp;</td>
							<td>
								<select id="metarobots" name="metarobots">
											<option value="">{#not_set}</option> 
											<option value="index,follow">{#fullpage_dlg.meta_index_follow}</option>
											<option value="index,nofollow">{#fullpage_dlg.meta_index_nofollow}</option>
											<option value="noindex,follow">{#fullpage_dlg.meta_noindex_follow}</option>
											<option value="noindex,nofollow">{#fullpage_dlg.meta_noindex_nofollow}</option>
								</select>
							</td>
						</tr>
					</table>
				</fieldset>

				<fieldset>
					<legend>{#fullpage_dlg.langprops}</legend>

					<table border="0" cellpadding="4" cellspacing="0">
						<tr>
							<td class="column1"><label for="docencoding">{#fullpage_dlg.encoding}</label></td> 
							<td>
								<select id="docencoding" name="docencoding"> 
										<option value="">{#not_set}</option>
								</select>
							</td> 
						</tr>
						<tr>
							<td nowrap="nowrap"><label for="doctypes">{#fullpage_dlg.doctypes}</label>&nbsp;</td>
							<td>
								<select id="doctypes" name="doctypes">
										<option value="">{#not_set}</option>
								</select>
							</td>
						</tr>
						<tr>
							<td nowrap="nowrap"><label for="langcode">{#fullpage_dlg.langcode}</label>&nbsp;</td>
							<td><input type="text" id="langcode" name="langcode" value="" /></td>
						</tr>
						<tr>
							<td class="column1"><label for="langdir">{#fullpage_dlg.langdir}</label></td> 
							<td>
								<select id="langdir" name="langdir"> 
										<option value="">{#not_set}</option> 
										<option value="ltr">{#fullpage_dlg.ltr}</option> 
										<option value="rtl">{#fullpage_dlg.rtl}</option> 
								</select>
							</td> 
						</tr>
						<tr>
							<td nowrap="nowrap"><label for="xml_pi">{#fullpage_dlg.xml_pi}</label>&nbsp;</td>
							<td><input type="checkbox" id="xml_pi" name="xml_pi" class="checkbox" /></td>
						</tr>
					</table>
				</fieldset>
			</div>

			<div id="appearance_panel" class="panel">
				<fieldset>
					<legend>{#fullpage_dlg.appearance_textprops}</legend>

					<table border="0" cellpadding="4" cellspacing="0">
						<tr>
							<td class="column1"><label for="fontface">{#fullpage_dlg.fontface}</label></td> 
							<td>
								<select id="fontface" name="fontface" onchange="changedStyleField(this);">
										<option value="">{#not_set}</option>
								</select>
							</td> 
						</tr>

						<tr>
							<td class="column1"><label for="fontsize">{#fullpage_dlg.fontsize}</label></td> 
							<td>
								<select id="fontsize" name="fontsize" onchange="changedStyleField(this);">
										<option value="">{#not_set}</option>
								</select>
							</td>
						</tr>

						<tr>
							<td class="column1"><label for="textcolor">{#fullpage_dlg.textcolor}</label></td> 
							<td>
								<table border="0" cellpadding="0" cellspacing="0">
									<tr>
										<td><input id="textcolor" name="textcolor" type="text" value="" size="9" onchange="updateColor('textcolor_pick','textcolor');changedStyleField(this);" /></td>
										<td id="textcolor_pickcontainer">&nbsp;</td>
									</tr>
								</table>
							</td>
						</tr>
					</table>
				</fieldset>

				<fieldset>
					<legend>{#fullpage_dlg.appearance_bgprops}</legend>

					<table border="0" cellpadding="4" cellspacing="0">
						<tr>
							<td class="column1"><label for="bgimage">{#fullpage_dlg.bgimage}</label></td> 
							<td>
								<table border="0" cellpadding="0" cellspacing="0">
									<tr>
										<td><input id="bgimage" name="bgimage" type="text" value="" onchange="changedStyleField(this);" /></td>
										<td id="bgimage_pickcontainer">&nbsp;</td>
									</tr>
								</table>
							</td>
						</tr>
						<tr>
							<td class="column1"><label for="bgcolor">{#fullpage_dlg.bgcolor}</label></td> 
							<td>
								<table border="0" cellpadding="0" cellspacing="0">
									<tr>
										<td><input id="bgcolor" name="bgcolor" type="text" value="" size="9" onchange="updateColor('bgcolor_pick','bgcolor');changedStyleField(this);" /></td>
										<td id="bgcolor_pickcontainer">&nbsp;</td>
									</tr>
								</table>
							</td> 
						</tr>
					</table>
				</fieldset>

				<fieldset>
					<legend>{#fullpage_dlg.appearance_marginprops}</legend>

					<table border="0" cellpadding="4" cellspacing="0">
						<tr>
							<td class="column1"><label for="leftmargin">{#fullpage_dlg.left_margin}</label></td> 
							<td><input id="leftmargin" name="leftmargin" type="text" value="" onchange="changedStyleField(this);" /></td>
							<td class="column1"><label for="rightmargin">{#fullpage_dlg.right_margin}</label></td> 
							<td><input id="rightmargin" name="rightmargin" type="text" value="" onchange="changedStyleField(this);" /></td>
						</tr>
						<tr>
							<td class="column1"><label for="topmargin">{#fullpage_dlg.top_margin}</label></td> 
							<td><input id="topmargin" name="topmargin" type="text" value="" onchange="changedStyleField(this);" /></td>
							<td class="column1"><label for="bottommargin">{#fullpage_dlg.bottom_margin}</label></td> 
							<td><input id="bottommargin" name="bottommargin" type="text" value="" onchange="changedStyleField(this);" /></td>
						</tr>
					</table>
				</fieldset>

				<fieldset>
					<legend>{#fullpage_dlg.appearance_linkprops}</legend>

					<table border="0" cellpadding="4" cellspacing="0">
						<tr>
							<td class="column1"><label for="link_color">{#fullpage_dlg.link_color}</label></td> 
							<td>
								<table border="0" cellpadding="0" cellspacing="0">
									<tr>
										<td><input id="link_color" name="link_color" type="text" value="" size="9" onchange="updateColor('link_color_pick','link_color');changedStyleField(this);" /></td>
										<td id="link_color_pickcontainer">&nbsp;</td>
									</tr>
								</table>
							</td>

							<td class="column1"><label for="visited_color">{#fullpage_dlg.visited_color}</label></td> 
							<td>
								<table border="0" cellpadding="0" cellspacing="0">
									<tr>
										<td><input id="visited_color" name="visited_color" type="text" value="" size="9" onchange="updateColor('visited_color_pick','visited_color');changedStyleField(this);" /></td>
										<td id="visited_color_pickcontainer">&nbsp;</td>
									</tr>
								</table>
							</td>
						</tr>

						<tr>
							<td class="column1"><label for="active_color">{#fullpage_dlg.active_color}</label></td> 
							<td>
								<table border="0" cellpadding="0" cellspacing="0">
									<tr>
										<td><input id="active_color" name="active_color" type="text" value="" size="9" onchange="updateColor('active_color_pick','active_color');changedStyleField(this);" /></td>
										<td id="active_color_pickcontainer">&nbsp;</td>
									</tr>
								</table>
							</td>

							<td>&nbsp;</td>
							<td>&nbsp;</td>

<!--							<td class="column1"><label for="hover_color">{#fullpage_dlg.hover_color}</label></td> 
							<td>
								<table border="0" cellpadding="0" cellspacing="0">
									<tr>
										<td><input id="hover_color" name="hover_color" type="text" value="" size="9" onchange="changedStyleField(this);" /></td>
										<td id="hover_color_pickcontainer">&nbsp;</td>
									</tr>
								</table>
							</td> -->
						</tr>
					</table>
				</fieldset>

				<fieldset>
					<legend>{#fullpage_dlg.appearance_style}</legend>

					<table border="0" cellpadding="4" cellspacing="0">
						<tr>
							<td class="column1"><label for="stylesheet">{#fullpage_dlg.stylesheet}</label></td> 
							<td><table border="0" cellpadding="0" cellspacing="0">
									<tr>
										<td><input id="stylesheet" name="stylesheet" type="text" value="" /></td>
										<td id="stylesheet_browsercontainer">&nbsp;</td>
									</tr>
								</table></td>
						</tr>
						<tr>
							<td class="column1"><label for="style">{#fullpage_dlg.style}</label></td> 
							<td><input id="style" name="style" type="text" value="" onchange="changedStyleField(this);" /></td>
						</tr>
					</table>
				</fieldset>
			</div>

			<div id="advanced_panel" class="panel">
				<div id="addmenu">
					<table border="0" cellpadding="0" cellspacing="0">
						<tr><td><a href="javascript:addHeadElm('title');" onmousedown="return false;"><span>{#fullpage_dlg.add_title}</span></a></td></tr>
						<tr><td><a href="javascript:addHeadElm('meta');" onmousedown="return false;"><span>{#fullpage_dlg.add_meta}</span></a></td></tr>
						<tr><td><a href="javascript:addHeadElm('script');" onmousedown="return false;"><span>{#fullpage_dlg.add_script}</span></a></td></tr>
						<tr><td><a href="javascript:addHeadElm('style');" onmousedown="return false;"><span>{#fullpage_dlg.add_style}</span></a></td></tr>
						<tr><td><a href="javascript:addHeadElm('link');" onmousedown="return false;"><span>{#fullpage_dlg.add_link}</span></a></td></tr>
						<tr><td><a href="javascript:addHeadElm('base');" onmousedown="return false;"><span>{#fullpage_dlg.add_base}</span></a></td></tr>
						<tr><td><a href="javascript:addHeadElm('comment');" onmousedown="return false;"><span>{#fullpage_dlg.add_comment}</span></a></td></tr>
					</table>
				</div>

				<fieldset>
					<legend>{#fullpage_dlg.head_elements}</legend>

					<div class="headlistwrapper">
						<div class="toolbar">
							<div style="float: left">
								<a id="addbutton" href="javascript:showAddMenu();" onmousedown="return false;" class="addbutton" title="{#fullpage_dlg.add}"></a>
								<a href="#" onmousedown="return false;" class="removebutton" title="{#fullpage_dlg.remove}"></a>
							</div>
							<div style="float: right">
								<a href="#" onmousedown="return false;" class="moveupbutton" title="{#fullpage_dlg.moveup}"></a>
								<a href="#" onmousedown="return false;" class="movedownbutton" title="{#fullpage_dlg.movedown}"></a>
							</div>
							<br style="clear: both" />
						</div>
						<select id="headlist" size="26" onchange="updateHeadElm(this.options[this.selectedIndex].value);">
							<option value="title_0">&lt;title&gt;Some title bla bla bla&lt;/title&gt;</option>
							<option value="meta_1">&lt;meta name="keywords"&gt;Some bla bla bla&lt;/meta&gt;</option>
							<option value="meta_2">&lt;meta name="description"&gt;Some bla bla bla bla bla bla bla bla bla&lt;/meta&gt;</option>
							<option value="script_3">&lt;script language=&quot;javascript&quot;&gt;...&lt;/script&gt;</option>
							<option value="style_4">&lt;style&gt;...&lt;/style&gt;</option>
							<option value="base_5">&lt;base href="." /&gt;</option>
							<option value="comment_6">&lt;!-- ... --&gt;</option>
							<option value="link_7">&lt;link href="." /&gt;</option>
						</select>
					</div>
				</fieldset>

				<fieldset id="meta_element">
					<legend>{#fullpage_dlg.meta_element}</legend>

					<table border="0" cellpadding="4" cellspacing="0">
						<tr>
							<td class="column1"><label for="element_meta_type">{#fullpage_dlg.type}</label></td> 
							<td><select id="element_meta_type">
										<option value="name">name</option>
										<option value="http-equiv">http-equiv</option>
								</select></td>
						</tr>
						<tr>
							<td class="column1"><label for="element_meta_name">{#fullpage_dlg.name}</label></td> 
							<td><input id="element_meta_name" name="element_meta_name" type="text" value="" /></td>
						</tr>
						<tr>
							<td class="column1"><label for="element_meta_content">{#fullpage_dlg.content}</label></td> 
							<td><input id="element_meta_content" name="element_meta_content" type="text" value="" /></td>
						</tr>
					</table>

					<input type="button" id="meta_updateelement" class="updateElementButton" name="update" value="{#update}" onclick="updateElement();" />
				</fieldset>

				<fieldset id="title_element">
					<legend>{#fullpage_dlg.title_element}</legend>

					<table border="0" cellpadding="4" cellspacing="0">
						<tr>
							<td class="column1"><label for="element_title">{#fullpage_dlg.meta_title}</label></td> 
							<td><input id="element_title" name="element_title" type="text" value="" /></td>
						</tr>
					</table>

					<input type="button" id="title_updateelement" class="updateElementButton" name="update" value="{#update}" onclick="updateElement();" />
				</fieldset>

				<fieldset id="script_element">
					<legend>{#fullpage_dlg.script_element}</legend>

					<div class="tabs">
						<ul>
							<li id="script_props_tab" class="current"><span><a href="javascript:mcTabs.displayTab('script_props_tab','script_props_panel');" onmousedown="return false;">{#fullpage_dlg.properties}</a></span></li>
							<li id="script_value_tab"><span><a href="javascript:mcTabs.displayTab('script_value_tab','script_value_panel');" onmousedown="return false;">{#fullpage_dlg.value}</a></span></li>
						</ul>
					</div>

					<br style="clear: both" />

					<div class="panel_wrapper">
						<div id="script_props_panel" class="panel current">
							<table border="0" cellpadding="4" cellspacing="0">
								<tr>
									<td class="column1"><label for="element_script_type">{#fullpage_dlg.type}</label></td> 
									<td><select id="element_script_type">
										<option value="text/javascript">text/javascript</option>
										<option value="text/jscript">text/jscript</option>
										<option value="text/vbscript">text/vbscript</option>
										<option value="text/vbs">text/vbs</option>
										<option value="text/ecmascript">text/ecmascript</option>
										<option value="text/xml">text/xml</option>
									</select></td>
								</tr>
								<tr>
									<td class="column1"><label for="element_script_src">{#fullpage_dlg.src}</label></td> 
									<td><table border="0" cellpadding="0" cellspacing="0">
									<tr>
										<td><input id="element_script_src" name="element_script_src" type="text" value="" /></td>
										<td id="script_src_pickcontainer">&nbsp;</td>
									</tr>
								</table></td>
								</tr>
								<tr>
									<td class="column1"><label for="element_script_charset">{#fullpage_dlg.charset}</label></td> 
									<td><select id="element_script_charset"><option value="">{#not_set}</option></select></td>
								</tr>
								<tr>
									<td class="column1"><label for="element_script_defer">{#fullpage_dlg.defer}</label></td> 
									<td><input type="checkbox" id="element_script_defer" name="element_script_defer" class="checkbox" /></td>
								</tr>
							</table>
						</div>

						<div id="script_value_panel" class="panel">
							<textarea id="element_script_value"></textarea>
						</div>
					</div>

					<input type="button" id="script_updateelement" class="updateElementButton" name="update" value="{#update}" onclick="updateElement();" />
				</fieldset>

				<fieldset id="style_element">
					<legend>{#fullpage_dlg.style_element}</legend>

					<div class="tabs">
						<ul>
							<li id="style_props_tab" class="current"><span><a href="javascript:mcTabs.displayTab('style_props_tab','style_props_panel');" onmousedown="return false;">{#fullpage_dlg.properties}</a></span></li>
							<li id="style_value_tab"><span><a href="javascript:mcTabs.displayTab('style_value_tab','style_value_panel');" onmousedown="return false;">{#fullpage_dlg.value}</a></span></li>
						</ul>
					</div>

					<br style="clear: both" />

					<div class="panel_wrapper">
						<div id="style_props_panel" class="panel current">
							<table border="0" cellpadding="4" cellspacing="0">
								<tr>
									<td class="column1"><label for="element_style_type">{#fullpage_dlg.type}</label></td> 
									<td><select id="element_style_type">
										<option value="text/css">text/css</option>
									</select></td>
								</tr>
								<tr>
									<td class="column1"><label for="element_style_media">{#fullpage_dlg.media}</label></td> 
									<td><select id="element_style_media"></select></td>
								</tr>
							</table>
						</div>

						<div id="style_value_panel" class="panel">
							<textarea id="element_style_value"></textarea>
						</div>
					</div>

					<input type="button" id="style_updateelement" class="updateElementButton" name="update" value="{#update}" onclick="updateElement();" />
				</fieldset>

				<fieldset id="base_element">
					<legend>{#fullpage_dlg.base_element}</legend>

					<table border="0" cellpadding="4" cellspacing="0">
						<tr>
							<td class="column1"><label for="element_base_href">{#fullpage_dlg.href}</label></td> 
							<td><input id="element_base_href" name="element_base_href" type="text" value="" /></td>
						</tr>
						<tr>
							<td class="column1"><label for="element_base_target">{#fullpage_dlg.target}</label></td> 
							<td><input id="element_base_target" name="element_base_target" type="text" value="" /></td>
						</tr>
					</table>

					<input type="button" id="base_updateelement" class="updateElementButton" name="update" value="{#update}" onclick="updateElement();" />
				</fieldset>

				<fieldset id="link_element">
					<legend>{#fullpage_dlg.link_element}</legend>

					<div class="tabs">
						<ul>
							<li id="link_general_tab" class="current"><span><a href="javascript:mcTabs.displayTab('link_general_tab','link_general_panel');" onmousedown="return false;">{#fullpage_dlg.general_props}</a></span></li>
							<li id="link_advanced_tab"><span><a href="javascript:mcTabs.displayTab('link_advanced_tab','link_advanced_panel');" onmousedown="return false;">{#fullpage_dlg.advanced_props}</a></span></li>
						</ul>
					</div>

					<br style="clear: both" />

					<div class="panel_wrapper">
						<div id="link_general_panel" class="panel current">
							<table border="0" cellpadding="4" cellspacing="0">
								<tr>
									<td class="column1"><label for="element_link_href">{#fullpage_dlg.href}</label></td> 
									<td><table border="0" cellpadding="0" cellspacing="0">
									<tr>
										<td><input id="element_link_href" name="element_link_href" type="text" value="" /></td>
										<td id="link_href_pickcontainer">&nbsp;</td>
									</tr>
								</table></td>
								</tr>
								<tr>
									<td class="column1"><label for="element_link_title">{#fullpage_dlg.meta_title}</label></td> 
									<td><input id="element_link_title" name="element_link_title" type="text" value="" /></td>
								</tr>
								<tr>
									<td class="column1"><label for="element_link_type">{#fullpage_dlg.type}</label></td> 
									<td><select id="element_link_type" name="element_link_type">
										<option value="text/css">text/css</option>
										<option value="text/javascript">text/javascript</option>
									</select></td>
								</tr>
								<tr>
									<td class="column1"><label for="element_link_media">{#fullpage_dlg.media}</label></td> 
									<td><select id="element_link_media" name="element_link_media"></select></td>
								</tr>
								<tr>
									<td><label for="element_style_rel">{#fullpage_dlg.rel}</label></td>
									<td><select id="element_style_rel" name="element_style_rel"> 
											<option value="">{#not_set}</option> 
											<option value="stylesheet">Stylesheet</option>
											<option value="alternate">Alternate</option>
											<option value="designates">Designates</option>
											<option value="start">Start</option>
											<option value="next">Next</option>
											<option value="prev">Prev</option>
											<option value="contents">Contents</option>
											<option value="index">Index</option>
											<option value="glossary">Glossary</option>
											<option value="copyright">Copyright</option>
											<option value="chapter">Chapter</option>
											<option value="subsection">Subsection</option>
											<option value="appendix">Appendix</option>
											<option value="help">Help</option>
											<option value="bookmark">Bookmark</option>
										</select> 
									</td>
								</tr>
							</table>
						</div>

						<div id="link_advanced_panel" class="panel">
							<table border="0" cellpadding="4" cellspacing="0">
								<tr>
									<td class="column1"><label for="element_link_charset">{#fullpage_dlg.charset}</label></td> 
									<td><select id="element_link_charset"><option value="">{#not_set}</option></select></td>
								</tr>
								<tr>
									<td class="column1"><label for="element_link_hreflang">{#fullpage_dlg.hreflang}</label></td> 
									<td><input id="element_link_hreflang" name="element_link_hreflang" type="text" value="" /></td>
								</tr>
								<tr>
									<td class="column1"><label for="element_link_target">{#fullpage_dlg.target}</label></td> 
									<td><input id="element_link_target" name="element_link_target" type="text" value="" /></td>
								</tr>
								<tr>
									<td><label for="element_style_rev">{#fullpage_dlg.rev}</label></td>
									<td><select id="element_style_rev" name="element_style_rev"> 
											<option value="">{#not_set}</option> 
											<option value="alternate">Alternate</option> 
											<option value="designates">Designates</option> 
											<option value="stylesheet">Stylesheet</option> 
											<option value="start">Start</option> 
											<option value="next">Next</option> 
											<option value="prev">Prev</option> 
											<option value="contents">Contents</option> 
											<option value="index">Index</option> 
											<option value="glossary">Glossary</option> 
											<option value="copyright">Copyright</option> 
											<option value="chapter">Chapter</option> 
											<option value="subsection">Subsection</option> 
											<option value="appendix">Appendix</option> 
											<option value="help">Help</option> 
											<option value="bookmark">Bookmark</option> 
										</select> 
									</td>
								</tr>
							</table>
						</div>
					</div>

					<input type="button" id="link_updateelement" class="updateElementButton" name="update" value="{#update}" onclick="updateElement();" />
				</fieldset>

				<fieldset id="comment_element">
					<legend>{#fullpage_dlg.comment_element}</legend>

					<textarea id="element_comment_value"></textarea>

					<input type="button" id="comment_updateelement" class="updateElementButton" name="update" value="{#update}" onclick="updateElement();" />
				</fieldset>
			</div>
		</div>

		<div class="mceActionPanel">
			<div style="float: left">
				<input type="button" id="insert" name="update" value="{#update}" onclick="updateAction();" />
			</div>

			<div style="float: right">
				<input type="button" id="cancel" name="cancel" value="{#cancel}" onclick="tinyMCEPopup.close();" />
			</div>
		</div>
    </form>
</body>
</html>
