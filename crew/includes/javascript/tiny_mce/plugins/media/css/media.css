#id, #name, #hspace, #vspace, #class_name, #align {
	width: 100px;
}

#hspace, #vspace {
	width: 50px;
}

#flash_quality, #flash_align, #flash_scale, #flash_salign, #flash_wmode {
	width: 100px;
}

#flash_base, #flash_flashvars {
	width: 240px;
}

#width, #height {
	width: 40px;
}

#src, #media_type {
	width: 250px;
}

#class {
	width: 120px;
}

#prev {
	margin: 0;
	border: 1px solid black;
	width: 99%;
	height: 230px;
	overflow: auto;
}

.panel_wrapper div.current {
	height: 390px;
	overflow: auto;
}

#flash_options, #shockwave_options, #qt_options, #wmp_options, #rmp_options {
	display: none;
}

.mceAddSelectValue {
	background-color: #DDDDDD;
}

#qt_starttime, #qt_endtime, #qt_fov, #qt_href, #qt_moveid, #qt_moviename, #qt_node, #qt_pan, #qt_qtsrc, #qt_qtsrcchokespeed, #qt_target, #qt_tilt, #qt_urlsubstituten, #qt_volume {
	width: 70px;
}

#wmp_balance, #wmp_baseurl, #wmp_captioningid, #wmp_currentmarker, #wmp_currentposition, #wmp_defaultframe, #wmp_playcount, #wmp_rate, #wmp_uimode, #wmp_volume {
	width: 70px;
}

#rmp_console, #rmp_numloop, #rmp_controls, #rmp_scriptcallbacks {
	width: 70px;
}

#shockwave_swvolume, #shockwave_swframe, #shockwave_swurl, #shockwave_swstretchvalign, #shockwave_swstretchhalign, #shockwave_swstretchstyle {
	width: 90px;
}

#qt_qtsrc {
	width: 200px;
}
