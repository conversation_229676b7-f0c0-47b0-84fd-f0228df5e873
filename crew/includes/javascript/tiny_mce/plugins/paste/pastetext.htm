<html xmlns="http://www.w3.org/1999/xhtml">
<head>
	<title>{#paste.paste_text_desc}</title>
	<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
	<script type="text/javascript" src="../../tiny_mce_popup.js"></script>
	<script type="text/javascript" src="js/pastetext.js"></script>
	<base target="_self" />
</head>
<body onresize="resizeInputs();" style="display:none; overflow:hidden;">
<form name="source" onsubmit="saveContent();">
	<div style="float: left" class="title">{#paste.paste_text_desc}</div>

	<div style="float: right">
		<input type="checkbox" name="linebreaks" id="linebreaks" class="wordWrapCode" checked="checked" /><label for="linebreaks">{#paste_dlg.text_linebreaks}</label>
	</div>

	<br style="clear: both" />

	<div>{#paste_dlg.text_title}</div>

	<textarea name="htmlSource" id="htmlSource" rows="15" cols="100" style="width: 100%; height: 100%; font-family: 'Courier New',Courier,mono; font-size: 12px;" dir="ltr" wrap="soft"></textarea>

	<div class="mceActionPanel">
		<div style="float: left">
			<input type="button" name="insert" value="{#insert}" onclick="saveContent();" id="insert" />
		</div>

		<div style="float: right">
			<input type="button" name="cancel" value="{#cancel}" onclick="tinyMCEPopup.close();" id="cancel" />
		</div>
	</div>
</form>
</body> 
</html>