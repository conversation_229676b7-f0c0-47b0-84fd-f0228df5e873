tinyMCE.addI18n('en.searchreplace_dlg',{
searchnext_desc:"Find again",
notfound:"The search has been completed. The search string could not be found.",
search_title:"Find",
replace_title:"Find/Replace",
allreplaced:"All occurrences of the search string were replaced.",
findwhat:"Find what",
replacewith:"Replace with",
direction:"Direction",
up:"Up",
down:"Down",
mcase:"Match case",
findnext:"Find next",
replace:"Replace",
replaceall:"Replace all"
});