<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
	<title>{#advanced_dlg.colorpicker_title}</title>
	<script type="text/javascript" src="../../tiny_mce_popup.js"></script>
	<script type="text/javascript" src="../../utils/mctabs.js"></script>
	<script type="text/javascript" src="js/color_picker.js"></script>
	<base target="_self" />
</head>
<body id="colorpicker" style="display: none">
	<div class="tabs">
		<ul>
			<li id="picker_tab" class="current"><span><a href="javascript:mcTabs.displayTab('picker_tab','picker_panel');" onmousedown="return false;">{#advanced_dlg.colorpicker_picker_tab}</a></span></li>
			<li id="rgb_tab"><span><a href="javascript:;" onclick="generateWebColors();mcTabs.displayTab('rgb_tab','rgb_panel');" onmousedown="return false;">{#advanced_dlg.colorpicker_palette_tab}</a></span></li>
			<li id="named_tab"><span><a  href="javascript:;" onclick="generateNamedColors();javascript:mcTabs.displayTab('named_tab','named_panel');" onmousedown="return false;">{#advanced_dlg.colorpicker_named_tab}</a></span></li>
		</ul>
	</div>

	<div class="panel_wrapper">
		<div id="picker_panel" class="panel current">
			<fieldset>
				<legend>{#advanced_dlg.colorpicker_picker_title}</legend>
				<div id="picker">
					<img id="colors" src="img/colorpicker.jpg" onclick="computeColor(event)" onmousedown="isMouseDown = true;return false;" onmouseup="isMouseDown = false;" onmousemove="if (isMouseDown && isMouseOver) computeColor(event); return false;" onmouseover="isMouseOver=true;" onmouseout="isMouseOver=false;" />

					<div id="light">
						<!-- Will be filled with divs -->
					</div>

					<br style="clear: both" />
				</div>
			</fieldset>
		</div>

		<div id="rgb_panel" class="panel">
			<fieldset>
				<legend>{#advanced_dlg.colorpicker_palette_title}</legend>
				<div id="webcolors">
					<!-- Gets filled with web safe colors-->
				</div>

				<br style="clear: both" />
			</fieldset>
		</div>

		<div id="named_panel" class="panel">
			<fieldset>
				<legend>{#advanced_dlg.colorpicker_named_title}</legend>
				<div id="namedcolors">
					<!-- Gets filled with named colors-->
				</div>

				<br style="clear: both" />

				<div id="colornamecontainer">
					{#advanced_dlg.colorpicker_name} <span id="colorname"></span>
				</div>
			</fieldset>
		</div>
	</div>

	<div class="mceActionPanel">
		<div style="float: left">
			<input type="button" id="insert" name="insert" value="{#apply}" onclick="insertAction();" />
		</div>

		<div id="preview"></div>

		<div id="previewblock">
			<label for="color">{#advanced_dlg.colorpicker_color}</label> <input id="color" type="text" size="8" maxlength="8" class="text" />
		</div>
	</div>
</body>
</html>
