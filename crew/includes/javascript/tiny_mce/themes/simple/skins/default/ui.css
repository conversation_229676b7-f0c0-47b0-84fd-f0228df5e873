/* Reset */
.defaultSkin table, .defaultSkin tbody, .defaultSkin a, .defaultSkin img, .defaultSkin tr, .defaultSkin div, .defaultSkin td, .defaultSkin iframe, .defaultSkin span, .defaultSkin * {border:0; margin:0; padding:0; background:transparent; white-space:nowrap; text-decoration:none; font-weight:normal; cursor:default; color:#000}

/* Containers */
.defaultSkin {position:relative}
.defaultSkin table.mceLayout {background:#F0F0EE; border:1px solid #CCC;}
.defaultSkin iframe {display:block; background:#FFF; border-bottom:1px solid #CCC;}
.defaultSkin .mceToolbar {height:24px;}

/* Layout */
.defaultSkin span.icon, .defaultSkin img.icon {display:block; width:20px; height:20px}
.defaultSkin .icon {background:url(../../img/icons.gif) no-repeat 20px 20px}

/* Button */
.defaultSkin .mceButton {display:block; border:1px solid #F0F0EE; width:20px; height:20px}
.defaultSkin a.mceButtonEnabled:hover {border:1px solid #0A246A; background-color:#B2BBD0}
.defaultSkin a.mceButtonActive {border:1px solid #0A246A; background-color:#C2CBE0}
.defaultSkin .mceButtonDisabled span {opacity:0.3; filter:alpha(opacity=30)}

/* Separator */
.defaultSkin .mceSeparator {display:block; background:url(../../img/icons.gif) -180px 0; width:2px; height:20px; margin:0 2px 0 4px}

/* Theme */
.defaultSkin span.bold {background-position:0 0}
.defaultSkin span.italic {background-position:-60px 0}
.defaultSkin span.underline {background-position:-140px 0}
.defaultSkin span.strikethrough {background-position:-120px 0}
.defaultSkin span.undo {background-position:-160px 0}
.defaultSkin span.redo {background-position:-100px 0}
.defaultSkin span.cleanup {background-position:-40px 0}
.defaultSkin span.insertunorderedlist {background-position:-20px 0}
.defaultSkin span.insertorderedlist {background-position:-80px 0}
