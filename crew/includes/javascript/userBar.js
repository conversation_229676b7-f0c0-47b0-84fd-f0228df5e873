if (!('g2g' in window))
    window['g2g'] = {};

var REGION_SETTING_FIRST_CLICK = 0;
var CTRY_JSON, REGION_JSON, REFER;

g2g.userBar = {
    init: function(_ctry, _cur, _lang) {
        jQuery('#reg-setting').click(function() {
            if (REGION_JSON) {
                g2g.userBar.regCtry(_ctry);
                g2g.userBar.regLangCur(_ctry, _cur, _lang);
            } else if (REGION_SETTING_FIRST_CLICK === 0) {
                REGION_SETTING_FIRST_CLICK = 1;
                
                jQuery("#reg_ctry").prev('.select2-container.g2g-select2').children('.select2-choice').children('span.select2-chosen').html('<i>Loading ...</i>');
                jQuery("#reg_cur").prev('.select2-container.g2g-select2').children('.select2-choice').children('span.select2-chosen').html('<i>Loading ...</i>');
                jQuery("#reg_lang").prev('.select2-container.g2g-select2').children('.select2-choice').children('span.select2-chosen').html('<i>Loading ...</i>');
                jQuery.ajax({
                    type: 'GET',
                    url: COUNTRY_STATIC_DOMAIN,
                    contentType: "application/json",
                    jsonpCallback: 'country',
                    dataType: 'jsonp',
                    crossDomain: true,
                    error: function() {
                        REGION_SETTING_FIRST_CLICK = 0;
                    },
                    success: function(ctry_data) {
                        CTRY_JSON = ctry_data;
                        jQuery.ajax({
                            type: 'GET',
                            url: REGION_STATIC_DOMAIN,
                            contentType: "application/json",
                            jsonpCallback: 'region',
                            dataType: 'jsonp',
                            crossDomain: true,
                            error: function() {
                                REGION_SETTING_FIRST_CLICK = 0;
                            },
                            success: function(reg_data) {
                                REFER = reg_data.refer;
                                REGION_JSON = reg_data;
                                g2g.userBar.regCtry(_ctry);
                                g2g.userBar.regLangCur(_ctry, _cur, _lang);
                            }
                        });
                    }
                });
            }
        });

        jQuery('#reg_ctry').change(function() {
            g2g.userBar.regLangCur(jQuery('#reg_ctry').val(), jQuery('#reg_cur').val(), jQuery('#reg_lang').val());
        });
    },
    regCtry: function(_ctry) {
        jQuery('#reg_ctry option').remove();
        jQuery.each(CTRY_JSON, function(code, items) {
            var _sel = "";
            if (code == _ctry) {
                _sel = " selected";
            }
            jQuery('#reg_ctry').append('<option value="' + code + '"' + _sel + '>' + items.name + '</option>');
            jQuery("#reg_ctry").prev('.select2-container.g2g-select2').children('.select2-choice').children('span.select2-chosen').text(jQuery("#reg_ctry option:selected").text());
        });
    },
    regLangCur: function(_ctry, _cur, _lang) {
        if ((typeof(_ctry) != undefined || _ctry != null) && (typeof(_cur) != undefined || _cur != null) && (typeof(_lang) != undefined || _lang != null)) {
            country = REGION_JSON[_ctry];

            // Invalid country, get default currency and language list
            if ((typeof(country) == undefined || country == null)) {
                country = REGION_JSON['default'];
            } else {
                //no currency set for country
                if (country["currency"] == undefined || country["currency"] == null) {
                    country["currency"] = REGION_JSON['default']['currency'];
                }
                // no languge set for country,
                if (country["language"] == undefined || country["language"] == null) {
                    country["language"] = REGION_JSON['default']['language'];
                }
            }

            jQuery.each(country, function(type, typeItems) {
                switch (type) {
                    case "currency":
                        _opt = "#reg_cur";
                        _select = _cur;
                        break;
                    case "language":
                        _opt = "#reg_lang";
                        _select = _lang;
                        break;
                }

                g2g.userBar.setLangCurDropdown(_opt, typeItems, _select, type);
            });
        }
    },
    setLangCurDropdown: function(_opt, typeItems, _select, type) {
        var _sel_opt = false;
        var defaultSelected = REGION_JSON['default'][type]['def'];

        jQuery(_opt + ' option').remove();
        jQuery.each(typeItems, function(tkey, tval) {
            if (tkey == "val") {
                jQuery.each(tval, function(id, name) {
                    var _sel = "";
                    if (id == _select) {
                        _sel = " selected";
                        _sel_opt = true;
                    }
                    jQuery(_opt).append('<option value="' + id + '"' + _sel + '>' + REFER[type][id] + '</option>');
                });
            } else if (tkey == "def" && tval != "") {
                defaultSelected = tval;
            }
        });

        if ((_sel_opt == false) || (_select == "")) {
            jQuery(_opt + " option[value='" + defaultSelected + "']").prop('selected', true);
        }
        jQuery(_opt).prev('.select2-container.g2g-select2').children('.select2-choice').children('span.select2-chosen').text(jQuery(_opt + " option:selected").text());
    }
};

PROFILE_ONCLICK = true;
g2g.profile = {
    getCustomerScWor: function() {
        jQuery('#ub-profile').click(function() {
            if (PROFILE_ONCLICK) {
                PROFILE_ONCLICK = false;
                jQuery.ajax({
                    url: '/shasso.php?action=getCustomerScWor',
//                    type: 'POST',
                    dataType: 'json',
                    success: function(data) {
                        jQuery('#ub-profile-dd li a .tokenIcon').css('display', 'none');
                        
                        if (!data.wor.error) {
                            jQuery('#ub-profile-dd li a .tokenIcon').css('display', 'inline-block');
                        }
                        
                        jQuery('#ub-sc').html(data.sc.val);
                        jQuery('#ub-wor').html(data.wor.val);
                        
                        setTimeout(function() {
                            PROFILE_ONCLICK = true;
                        }, REFRESH_TIME);
                    }
                });
            }
        });
    }
};

function g2gResponsiveSelect2() {
    var width = jQuery(window).width();
    if (width < 1200) {
        jQuery('select.g2g-select2').removeClass('select2-offscreen');
        jQuery('.select2-container.g2g-select2').css('display', 'none');
    } else {
        jQuery('select.g2g-select2').addClass('select2-offscreen');
        jQuery('.select2-container.g2g-select2').css('display', 'inline-block');
    }
    jQuery(window).resize(function() {
        if (jQuery(this).width() != width) {
            width = jQuery(this).width();
            if (width < 1200) {
                jQuery('select.g2g-select2').removeClass('select2-offscreen');
                jQuery('.select2-container.g2g-select2').css('display', 'none');
            } else {
                jQuery('select.g2g-select2').addClass('select2-offscreen');
                jQuery('.select2-container.g2g-select2').css('display', 'inline-block');
            }
        }
    });
}