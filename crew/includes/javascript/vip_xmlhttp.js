function get_full_server_list(game_cat_id, list_by, table_name) {
	var server_action = 'full_server_list';
	/*
	var server_list = document.getElementById('cID');
	if(typeof(server_list)!= 'undefined' && server_list != null){
		if (server_list.value > 0){
			server_list.disabled = true;
		}
	}
	
	var server_load = document.getElementById('select_server');
	server_load.className = "loadingImg";
	*/
	reset_table(table_name, 0);

	var ref_url = "vip_xmlhttp.php?action="+server_action+"&game_id="+game_cat_id+"&list_by="+list_by;
	jQuery.ajax({
		url:ref_url,
		dataType: 'xml',
		timeout: 60000,
		error: function(){
			
		},
		success: function(xml) {
			var servers = jQuery(xml).find('servers');
      		
      		if (typeof (servers) != 'undefined' && servers != null) {
      			var server_list = jQuery(xml).find('server');

                if (server_list.length > 0) {
					var thisTable = DOMCall(table_name);
					var thisTbody = thisTable.getElementsByTagName("tbody")[0];

                    for (var list_cnt=0; list_cnt < server_list.length; list_cnt++) {
						var products_id = typeof (server_list[list_cnt].getElementsByTagName('id')[0]) != "undefined" && server_list[list_cnt].getElementsByTagName('id')[0] != null ? server_list[list_cnt].getElementsByTagName('id')[0].firstChild.data : '';
						var products_name = typeof (server_list[list_cnt].getElementsByTagName('name')[0]) != "undefined" && server_list[list_cnt].getElementsByTagName('name')[0] != null ? server_list[list_cnt].getElementsByTagName('name')[0].firstChild.data : '';
						var qty = typeof (server_list[list_cnt].getElementsByTagName('qty')[0]) != "undefined" && server_list[list_cnt].getElementsByTagName('qty')[0] != null ? server_list[list_cnt].getElementsByTagName('qty')[0].firstChild.data : '';
	          		    var action_link = typeof (server_list[list_cnt].getElementsByTagName('link')[0]) != "undefined" && server_list[list_cnt].getElementsByTagName('link')[0] != null ? server_list[list_cnt].getElementsByTagName('link')[0].firstChild.data : '';
	          		    
	          		    //var row_style = (thisTable.rows.length % 2) ? 'ordersListingEven' : 'ordersListingOdd' ;
						var row_style = 'ordersListing';
						
						if (list_cnt != 0) {
							var myNewRowSeparator = thisTbody.insertRow(thisTable.rows.length);
							var myNewCellSeparator = myNewRowSeparator.insertCell(myNewRowSeparator.cells.length);
							myNewCellSeparator.setAttribute('colspan', '3');
							myNewCellSeparator.innerHTML = '<div class="row_separator"></div>';
						}
						
	          		    var myNewRow = thisTbody.insertRow(thisTable.rows.length);
	          		    myNewRow.className = row_style.toString();
	          		    myNewRow.setAttribute("onmouseover","this.className ='ordersListingOver'",0);
	          		    myNewRow.setAttribute("onmouseout","this.className ='ordersListingOut'",0);
	          		    myNewRow.setAttribute('height', 25);
	          		    
	          		    var myNewCell = myNewRow.insertCell(myNewRow.cells.length);
						//myNewCell.className = 'ordersRecords';
						myNewCell.setAttribute('width', '60%');
						myNewCell.setAttribute("style", "padding-left:5");
						myNewCell.innerHTML = products_name;
						
						var myNewCell = myNewRow.insertCell(myNewRow.cells.length);
						//myNewCell.className = 'ordersRecords';
						myNewCell.setAttribute('align', 'center');
						myNewCell.setAttribute('width', '20%');
						myNewCell.innerHTML = qty;
						
						var myNewCell = myNewRow.insertCell(myNewRow.cells.length);
						//myNewCell.className = 'ordersRecords';
						myNewCell.setAttribute('align', 'center');
						myNewCell.setAttribute('width', '20%');
						myNewCell.innerHTML = action_link;
	    	        }   
				}
      		}	
      		//server_list.disabled = false;
		}
	});
}

function vip_add_inv(obj, pid) {
	var server_action = 'add_server';
	var qty = jQuery("#inv_qty_"+pid).val();
	var thisRow = obj.parentNode.parentNode;
	var qty_cell = thisRow.childNodes[1];
	var action_link_cell = thisRow.childNodes[2];
	
	var ref_url = "vip_xmlhttp.php?action="+server_action+"&pid="+pid+"&qty="+qty;
	jQuery.ajax({
		url:ref_url,
		dataType: 'xml',
		timeout: 60000,
		error: function(){
			
		},
		success: function(xml) {
      		var error_code = typeof (jQuery(xml).find('error').text()) != "undefined" && jQuery(xml).find('error').text() != null ? jQuery(xml).find('error').text() : '';
            
      		if (error_code == '1') {
      			alert('Error!');
      		} else {
      			var qty_info = typeof (jQuery(xml).find('qty').text()) != "undefined" && jQuery(xml).find('qty').text() != null ? jQuery(xml).find('qty').text() : '';
	      		var link_info = typeof (jQuery(xml).find('link').text()) != "undefined" && jQuery(xml).find('link').text() != null ? jQuery(xml).find('link').text() : '';
	      		
      			qty_cell.innerHTML = qty_info;
				action_link_cell.innerHTML = link_info;
      		}
		}
	});
}

function vip_del_sup_inv(pid) {
	var server_action = 'del_server';
	
	var ref_url = "vip_xmlhttp.php?action="+server_action+"&pid="+pid+"&return_xml=no";
	jQuery.ajax({
		url:ref_url,
		dataType: 'xml',
		timeout: 60000,
		error: function(){
			
		},
		success: function(xml) {
      		var error_code = typeof (jQuery(xml).find('error').text()) != "undefined" && jQuery(xml).find('error').text() != null ? jQuery(xml).find('error').text() : '';
            
      		if (error_code == '1') {
      			alert('Error!');
      		} else {
      			jQuery("#row_"+pid).fadeOut('slow');
      		}
		}
	});
}

function vip_del_inv(obj, pid) {
	var server_action = 'del_server';
	
	var thisRow = obj.parentNode.parentNode;
	var qty_cell = thisRow.childNodes[1];
	var action_link_cell = thisRow.childNodes[2];
	
	var ref_url = "vip_xmlhttp.php?action="+server_action+"&pid="+pid;
	jQuery.ajax({
		url:ref_url,
		dataType: 'xml',
		timeout: 60000,
		error: function(){
			
		},
		success: function(xml) {
      		var error_code = typeof (jQuery(xml).find('error').text()) != "undefined" && jQuery(xml).find('error').text() != null ? jQuery(xml).find('error').text() : '';
            
      		if (error_code == '1') {
      			alert('Error!');
      		} else {
      			var qty_info = typeof (jQuery(xml).find('qty').text()) != "undefined" && jQuery(xml).find('qty').text() != null ? jQuery(xml).find('qty').text() : '';
	      		var link_info = typeof (jQuery(xml).find('link').text()) != "undefined" && jQuery(xml).find('link').text() != null ? jQuery(xml).find('link').text() : '';
	      		
      			qty_cell.innerHTML = qty_info;
				action_link_cell.innerHTML = link_info;
      		}
		}
	});
}

function reset_table(table_name, remain_first_N) {
	var thisTable = DOMCall(table_name);
	
	if (thisTable != null) {
		for (var i=thisTable.rows.length-1; i >= remain_first_N; i--) {
			var thisTbody = thisTable.getElementsByTagName("tbody")[0];
			thisTbody.deleteRow(i);
		}
	}
}

function show_product_buyback_info(responseXMLInfo) {
	var bo_exact_qty_msg = DOMCall('span_match_bo_exact_qty_msg');
	
	var selected_product_info = responseXMLInfo.getElementsByTagName("selected_product")[0];
	
	document.getElementById('wbb_div_msgField2').innerHTML = global_loading_message;
	
	if (typeof (selected_product_info) != 'undefined' && selected_product_info != null) {
		var dependantInputs = new Array('wbb_input_game_select', 'wbb_input_product_select', 'wbb_input_qty', 'wbb_button_confirm');
		disableElement(dependantInputs, true);
		
		var dependantElements = new Array('wbb_div_add_shortcut', 'wbb_tbody_error', 'wbb_tbody_notice', 'wbb_tbody_step2');
	    changeElementDisplay(dependantElements, 'hide');
		
		var errorMsg = '';
		
		var msg_id = 0;
		var notice_msg_obj = selected_product_info.getElementsByTagName('notice')[0];
		if (typeof (notice_msg_obj) != 'undefined' && notice_msg_obj != null) {
			var msg_id = parseInt(notice_msg_obj.firstChild.data);
		}
	    if (msg_id > 0) {
	  	    showNotice(msg_id);
	    }
		
	    var msg_id = 0;
		var error_msg_obj = selected_product_info.getElementsByTagName('error')[0];
		if (typeof (error_msg_obj) != 'undefined' && error_msg_obj != null) {
			var msg_id = parseInt(error_msg_obj.firstChild.data);
		}
		
	    if (msg_id > 0) {
	  	    showError(msg_id);
	        dependantInputs = new Array('wbb_input_game_select', 'wbb_input_product_select');
	        disableElement(dependantInputs, false);
	    } else {
	        var unit_price = selected_product_info.getElementsByTagName('unit_price')[0].firstChild.data;
	        var unit_price_display = selected_product_info.getElementsByTagName('unit_price_display')[0].firstChild.data;
	        
	        document.getElementById('wbb_span_unit_price').innerHTML = unit_price_display;
	        document.getElementById('wbb_hidden_unit_price').value = unit_price;
			
	        document.getElementById('wbb_span_max_value').innerHTML = selected_product_info.getElementsByTagName('max')[0].firstChild.data;
	        document.getElementById('wbb_span_product_name').innerHTML = selected_product_info.getElementsByTagName('product_name')[0].firstChild.data;
	        document.getElementById('wbb_hidden_product_name').value = selected_product_info.getElementsByTagName('product_name')[0].firstChild.data;
	        document.getElementById('wbb_span_min_value').innerHTML = selected_product_info.getElementsByTagName('min')[0].firstChild.data;
	        document.getElementById('wbb_hidden_min_value').value = selected_product_info.getElementsByTagName('min')[0].firstChild.data;
	        document.getElementById('wbb_hidden_products_id').value = selected_product_info.getElementsByTagName('product_id')[0].firstChild.data;
	        document.getElementById('wbb_span_product_unit_name').innerHTML = selected_product_info.getElementsByTagName('product_unit_name')[0].firstChild.data;
	        
	        if (bo_exact_qty_msg != null) {
		        if (typeof (selected_product_info.getElementsByTagName('bo_exact_qty_msg')[0]) != 'undefined' && selected_product_info.getElementsByTagName('bo_exact_qty_msg')[0] != null) {
		        	bo_exact_qty_msg.innerHTML = selected_product_info.getElementsByTagName('bo_exact_qty_msg')[0].firstChild.data;
		        } else {
		        	bo_exact_qty_msg.innerHTML = '';
		        }
	        }
	        
	        document.getElementById('wbb_tbody_step2').className = 'show';
	        document.getElementById('wbb_div_add_shortcut').className = 'generalTextShow';
	        disableElement(dependantInputs, false);
	    }
	    document.getElementById('wbb_div_msgField2').innerHTML = '';
	}
}

function onBuybackServerSelection(serverListObj, languages_id) {
	var server_action = 'get_buyback_product_info';
	var parent_cat_id = DOMCall('wbb_input_game_select').value;
	var ref_url = "supplier_xmlhttp.php?action="+server_action+"&buyback_products_id="+serverListObj.value+"&buyback_parent_cat_id="+parent_cat_id+"&slang="+languages_id;
	
	document.getElementById('wbb_div_msgField2').innerHTML = global_loading_message;
	
	var dependantInputs = new Array('wbb_input_game_select', 'wbb_input_product_select', 'wbb_input_qty', 'wbb_button_confirm');
	disableElement(dependantInputs, true);
    
    var dependantElements = new Array('wbb_div_add_shortcut', 'wbb_tbody_error', 'wbb_tbody_notice', 'wbb_tbody_step2');
    changeElementDisplay(dependantElements, 'hide');
	
	var errorMsg = '';
	
    if (serverListObj.value > 0) {
        jQuery.ajax({
            url:ref_url,
            dataType: 'xml',
            timeout: 60000,
            error: function(){

            },
            success: function(xml) {
                show_product_buyback_info(xml);	// Call another function handle the rest of the result
            }
        });
    } else {
        dependantInputs = new Array('wbb_input_game_select', 'wbb_input_product_select');
        disableElement(dependantInputs, false);
        document.getElementById('wbb_div_msgField2').innerHTML = '';
    }
}

function show_order_report(buyback_req_grp_id, languages_id) {
	document.getElementById('wbb_div_msgField_'+buyback_req_grp_id).innerHTML = global_loading_message;
	var server_action = 'show_order_report';
	var ref_url = "supplier_xmlhttp.php?action="+server_action+"&buyback_req_grp_id="+buyback_req_grp_id+"&slang="+languages_id;
    jQuery.ajax({
        url:ref_url,
        dataType: 'xml',
        timeout: 60000,
        error: function(){

        },
        success: function(xml) {
            jQuery("#wbb_show_game_name").html(jQuery(xml).find('game_name').text());
            jQuery("#wbb_show_server_name").html(jQuery(xml).find('server_name').text());
            jQuery("#wbb_show_request_quantity").html(jQuery(xml).find('req_qty').text());
            jQuery("#wbb_show_uom").html(jQuery(xml).find('uom').text());
            jQuery("#wbb_show_product_name").html(jQuery(xml).find('product_name').text());
            jQuery("#wbb_show_total_price").html(jQuery(xml).find('total_price').text());
            jQuery("#wbb_show_sender_character").html(jQuery(xml).find('sender_character').text());
            jQuery("#wbb_show_restk_character").html(jQuery(xml).find('restk_character').text());
            jQuery("#wbb_show_delivery_time").html(jQuery(xml).find('delivery_time').text());
            jQuery("#wbb_show_contact_name").html(jQuery(xml).find('contact_name').text());
            jQuery("#wbb_show_contact_no").html(jQuery(xml).find('contact_no').text());
            jQuery("#wbb_show_comments").html(jQuery(xml).find('show_comments').text());
            jQuery("#wbb_show_order_reference").html(jQuery(xml).find('order_reference').text());

            //this are parameters for the popup div
            var w, h, l, t;
            w = 700;
            h = 500;
            l = screen.width/4;
            t = screen.height/4;
            displayFloatingDiv('order_report', 'Order Report', w, h, l, t);
            //order_report_content is preset in tpl as height=1px and width=1px so blank zone not apparent when hidden
            //so reset the height and width here

            jQuery("#order_report_content").css({ width: w + 'px' });
            jQuery("#order_report_content").css({ height: h + 'px' });

            jQuery("#wbb_div_msgField_"+buyback_req_grp_id).html('');
        }
    });	
}

function show_payment_report(payment_id) {
	document.getElementById('wbb_div_msgField_'+payment_id).innerHTML = 'Loading...';
	
	var server_action = 'show_payment_report';
	var ref_url = "supplier_xmlhttp.php?action="+server_action+"&payment_id="+payment_id;
    jQuery.ajax({
        url:ref_url,
        dataType: 'xml',
        timeout: 60000,
        error: function(){

        },
        success: function(xml) {
            res = jQuery(xml.responseText).html();

            new popUp(300, f_scrollTop()+200, 600, 500, payment_id, res, "white", "#00385c", "9pt sans-serif", '&#20184;&#27454;&#25253;&#21578; - '+ payment_id, "#00385c", "white", "lightgrey", "#00568c", "black", true, false, false, true, false, false, '', '', 'cir_close.gif', 'resize.gif');

            document.getElementById('wbb_div_msgField_'+payment_id).innerHTML = '';
        }
    });
}

function onFavLinksGameSelection(gameListObj, languages_id, server_product_id_autoselect) {
    var game_cat_id = gameListObj.value;
	var server_action = 'get_buyback_server_list';
	var ref_url = "supplier_xmlhttp.php?action="+server_action+"&buyback_cat_id="+game_cat_id+"&slang="+languages_id;

   	document.getElementById('fvl_div_msgField1').innerHTML = global_loading_message;

	var dependantInputs = new Array('fvl_input_game_select', 'fvl_input_product_select');
	disableElement(dependantInputs, true);

    var dependantElements = new Array('fvl_tbody_step2', 'fvl_tbody_error', 'fvl_tbody_notice');
    changeElementDisplay(dependantElements, 'hide');

    var server_selection = DOMCall('fvl_input_product_select');
    var fvl_div_game_cat_id = DOMCall('fvl_div_game_cat_id');

	var serverListInput = new Array('fvl_input_product_select');
	var gameListInput = new Array('fvl_input_game_select');

    if (game_cat_id == 0) {
		//clear server select list
		clearOptionList(server_selection);
        appendToSelect(server_selection, '0', '--');
		disableElement(serverListInput, true);
		disableElement(gameListInput, false);
		document.getElementById('fvl_div_msgField1').innerHTML = '';
		return;
	}

	jQuery.ajax({
		url:ref_url,
		dataType: 'xml',
		timeout: 60000,
		error: function(){
			
		},
		success: function(xml) {
            clearOptionList(server_selection);
			
            //No servers, etc
            var msg_id = 0;
			var error_msg_obj = jQuery(xml).find('error').text();
      		if (typeof (error_msg_obj) != 'undefined' && error_msg_obj != null) {
      			var msg_id = parseInt(error_msg_obj);
      		}
			
			//check if we need to bother updating the server list div
			if (game_cat_id != fvl_div_game_cat_id.value) {
				fvl_div_game_cat_id.value = game_cat_id;
			}
      	    
      	    if (msg_id > 0) {
          	    showError(msg_id);
				disableElement(serverListInput, true);
                appendToSelect(server_selection, '0', '--');
				disableElement(gameListInput, false);
      	    } else {
				var server_arr = jQuery(xml).find('server');
      		    for (var j=0; j<server_arr.length; j++) {
          		    var scat_id = server_arr[j].getElementsByTagName('id')[0].firstChild.nodeValue;
          		    var scat_name = server_arr[j].getElementsByTagName('name')[0].firstChild.nodeValue;
          		    //update the dropdown
                    appendToSelect(server_selection, scat_id, scat_name);
    	        }
    	        disableElement(dependantInputs, false);
    	        document.getElementById('fvl_tbody_step2').className = 'show';
      		}
      		
        	document.getElementById('fvl_div_msgField1').innerHTML = '';
        	
        	if (server_product_id_autoselect > 0) {
				server_selection.value = server_product_id_autoselect;
				onBuybackServerSelection(server_selection, languages_id);
        	}
		}
	});
}
/*
function get_vip_awaitng_order(){

	if (xmlhttp.readyState > 0 && xmlhttp.readyState < 4) return;
		
	var server_action = 'get_vip_awaiting_order';
	var ref_url = "vip_xmlhttp.php?action="+server_action;
	
	xmlhttp.open("GET", ref_url);
    xmlhttp.onreadystatechange = function() {
      	if (xmlhttp.readyState == 4 && xmlhttp.status == 200) {
      	    //alert(xmlhttp.responseText);
			
			document.getElementById('total_order').innerHTML = xmlhttp.responseXML.getElementsByTagName('total_order')[0].firstChild.data;
			
			var orders_arr = xmlhttp.responseXML.getElementsByTagName('orders');
			if (orders_arr.length > 0) {
				vip_awaiting_order_list.innerHTML = '';
				vipdivhtml = '';
      		    for (var orders_count=0; orders_count < orders_arr.length; orders_count++) {
      		    
					//update the div
					add_vip_result_row(orders_arr[orders_count]);
    	        }
    	        
    	        show_search_results_div('vip');
    	        toggleCountdown(1, 'vip');
    	        alert('done');
			} else {
				document.getElementById('timer_vip_main').innerHTML = '<span id="timer_vip_display" name="timer_vip_display"></span>';
				vip_awaiting_order_list.innerHTML = '<br><p class="title-text" align="center">'+global_no_result_found+'</p>';
			}
      	}
    }
    xmlhttp.send(null);
}
*/
function onVipAwaitingOrderRefresh() {
	var server_action = 'get_vip_offer';
	var ref_url = "vip_xmlhttp.php?action="+server_action;
	
	var vip_awaiting_order_list = DOMCall('vip_awaiting_order_list');
   	vip_awaiting_order_list.innerHTML = '<br><p class="title-text" align="center" style="text-align:center;">'+global_loading_message+'</p>';
    
   	jQuery.ajax({
        url:ref_url,
        dataType: 'xml',
        timeout: 60000,
        error: function(){

        },
        success: function(xml) {
            if(jQuery(xml).find('gotoURL').length > 0 && jQuery(xml).find('gotoURL').text() != ''){
                document.location.href=jQuery(xml).find('gotoURL').text();
                return false;
            }
            
            document.getElementById('total_order').innerHTML = jQuery(xml).find('total_order').text();

            var orders_arr = jQuery(xml).find('orders');
            if (orders_arr.length > 0) {
                vip_awaiting_order_list.innerHTML = '';
                vipdivhtml = '';
                for (var orders_count=0; orders_count < orders_arr.length; orders_count++) {
                    //update the div
                    add_vip_result_row(orders_arr[orders_count]);
                }
                show_search_results_div('vip');
            } else {
                vip_awaiting_order_list.innerHTML = '<br><p class="title-text" align="center" style="text-align:center;">'+global_no_result_found+'</p>';
            }

            toggleCountdown(true);
        }
    });
}

function onVipOrderHistoryRefresh(order_status_id, product_type, order_no, game_cat_id, start_date, end_date, languages_id) {
	var server_action = 'get_vip_order';
	var ref_url = "vip_xmlhttp.php?action="+server_action+"&order_status_id="+order_status_id+"&product_type="+product_type+"&order_no="+URLEncode(order_no)+"&game_cat_id="+URLEncode(game_cat_id)+"&start_date="+URLEncode(start_date)+"&end_date="+URLEncode(end_date);
	
	var vipodh_div_search_results = DOMCall('vipodh_div_search_results');
	vipodh_div_search_results.innerHTML = '<br><p class="title-text" align="center" style="text-align:center;">'+global_loading_message+'<\/p>';
	jQuery.ajax({
        url:ref_url,
        dataType: 'xml',
        timeout: 60000,
        error: function(){

        },
        success: function(xml) {
            document.getElementById('num_results').innerHTML = jQuery(xml).find('num_results').text();

            var row_arr = jQuery(xml).find('row');
			if (row_arr.length > 0) {
				vipodh_div_search_results.innerHTML = '';
				divhtml = '';
				var last_cnt = 1;
				var total_row = row_arr.length;
				
      		    for (var row_cnt=0; row_cnt<row_arr.length; row_cnt++) {
					//update the div
					if (last_cnt == total_row) {
						var is_last_row = 1;
					} else {
						var is_last_row = 0;
					}
					
					add_search_results_row(row_arr[row_cnt], is_last_row);
					
					last_cnt++;
    	        }
    	        show_search_results_div(null);
				disableDivInputs(false);
			} else {
				vipodh_div_search_results.innerHTML = '<br><p class="title-text" align="center" style="text-align:center;">'+global_no_result_found+'</p>';
			}
        }
    });
}