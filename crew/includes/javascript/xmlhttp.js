// add item to select element the less elegant, but compatible way.
function appendToSelect(select, value, content) {
	var opt = document.createElement('option');
	opt.value = value;
	opt.text = content;
	select.options.add(opt);
}

function clearOptionList(obj) {
	while (obj.options.length > 0) {
		obj.remove(0);
	}
//    appendToSelect(obj, '0', 'Please Select');
}

function showMainInfo(DisplayMsg) {
	hideMainInfo();
	if (typeof DisplayMsg == 'undefined') {
		return;
	}
	var objInfo = DOMCall('infocaption');
	var scroll = getScroll();

	objInfo.innerHTML = '<div class="xmlHttpMainInfo">' + DisplayMsg + '</div>';
	objInfo.style.visibility = 'visible';

	objInfo.style.position = "absolute";

	objInfo.style.top = 5 + scroll.y;

	var msg_left_pos = getWindowWidth() - objInfo.offsetWidth;
	objInfo.style.left = msg_left_pos - 20;
}

function hideMainInfo() {
	var objInfo = DOMCall('infocaption');
	objInfo.style.visibility = 'hidden';
	objInfo.innerHTML = '';
}

function getWindowWidth() {
	var windowWidth = 0;
	if (typeof(window.innerWidth) == 'number') {
		windowWidth = window.innerWidth;
	} else {
		if (document.documentElement && document.documentElement.clientWidth) {
			windowWidth = document.documentElement.clientWidth;
		} else {
			if (document.body && document.body.clientWidth) {
				windowWidth = document.body.clientWidth;
			}
		}
	}
	return windowWidth;
}

function getScroll() {
    if (document.body.scrollTop != undefined) { // IE model
        var ieBox = document.compatMode != "CSS1Compat";
        var cont = ieBox ? document.body : document.documentElement;
        return {x : cont.scrollLeft, y : cont.scrollTop};
    } else {
        return {x : window.pageXOffset, y : window.pageYOffset};
    }
}

var stateSelectionTitle = Array();
stateSelectionTitle['id'] = '';
if (typeof(pls_select) != 'undefined' && pls_select != null) {
	stateSelectionTitle['text'] = pls_select;
} else {
	stateSelectionTitle['text'] = 'Please Select';
}

function DOMCall(name) {
	if (document.getElementById)
    	return document.getElementById(name);
	else if (document.all)
		return document.all[name];
	else if (document.layers)
		return document.layers[name];
}

function initInfoCaptions() {
   	// create a dynamic layer and attach it to the HTML document.
	objBody = document.getElementsByTagName("body").item(0);
    objContainer = document.createElement('div');
    objContainer.setAttribute('id', 'infocaption');

    objBody.appendChild(objContainer);
}

window.onload = function() {
	initInfoCaptions();
}
