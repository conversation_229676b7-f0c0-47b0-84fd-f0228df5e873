<?php
define('BOX_INFORMATION_GV', 'FAQ Voucher Hadiah');
define('VOUCHER_BALANCE', 'Kredit <PERSON>ko');
define('BOX_HEADING_GIFT_VOUCHER', 'Akun Voucher Hadiah'); 
define('GV_FAQ', 'FAQ Voucher Hadiah');
define('ERROR_REDEEMED_AMOUNT', '<PERSON><PERSON><PERSON>, Anda telah menebus ');
define('ERROR_NO_REDEEM_CODE', 'Anda tidak memasukkan kode tebusan.');  
define('ERROR_NO_INVALID_REDEEM_GV', 'Kode Gift Voucher tidak sah'); 
define('TABLE_HEADING_CREDIT', 'Kredit yang tersedia');
define('GV_HAS_VOUCHERA', 'Anda memiliki dana di Akun Voucher Hadiah anda. Jika anda mau <br>
                         <PERSON><PERSON> anda mau, Anda boleh mengirimkan dana-dana tersebut dengan <a class="pageResults" href="');
       
define('GV_HAS_VOUCHERB', '"><b>email</b></a> kepada seseorang'); 
define('ENTRY_AMOUNT_CHECK_ERROR', 'Anda tidak memiliki cukup saldo untuk mengirimkan jumlah ini.'); 
define('BOX_SEND_TO_FRIEND', 'Kirim Voucher Hadiah');

define('VOUCHER_REDEEMED', 'Voucher telah ditebus');
define('CART_COUPON', 'Kupon :');
define('CART_COUPON_INFO', 'lebih info');

define('TEXT_WITHDRAWABLE_CREDIT', 'Kredit yang dapat withdraw');
define('TEXT_WITHDRAW', 'Withdraw');
define('TEXT_STATEMENT', 'Laporan');

define('TEXT_CART_AMOUNT', 'Jumlah Troli :');
?>