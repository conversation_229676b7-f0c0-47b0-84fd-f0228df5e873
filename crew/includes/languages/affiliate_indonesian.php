<?php
define('BOX_INFORMATION_AFFILIATE', 'Program Afiliasi');
define('BOX_HEADING_AFFILIATE', 'PROGRAM AFILIASI');
define('BOX_HEADING_AFFILIATE_NEWS', ' Berita Afiliasi');
define('BOX_AFFILIATE_INFO', 'Informasi Afiliasi');
define('BOX_AFFILIATE_SUMMARY', 'Ringkasan Afiliasi');
define('BOX_AFFILIATE_ACCOUNT', 'Edit Akun Afilia<PERSON>');
define('BOX_AFFILIATE_CLICKRATE', 'Laporan Clickthrough');
define('BOX_AFFILIATE_PAYMENT', 'Laporan Pembayaran');
define('BOX_AFFILIATE_SALES', 'Laporan <PERSON>');
define('BOX_AFFILIATE_BANNERS', 'Spanduk Afiliasi');
define('BOX_AFFILIATE_CONTACT', '<PERSON><PERSON><PERSON><PERSON>');
define('BOX_AFFILIATE_FAQ', 'FAQ Program Afiliasi');
define('BOX_AFFILIATE_LOGIN', 'Log Masuk Afiliasi');
define('BOX_AFFILIATE_LOGOUT', 'Log Keluar Afiliasi');

define('ENTRY_AFFILIATE_PAYMENT_DETAILS', 'Dibayar kepada:');
define('ENTRY_AFFILIATE_ACCEPT_AGB', 'Cek di sini untuk menunjukkan bahwa Anda telah membaca dan menyetujui <a target="_new" href="' . tep_href_link(FILENAME_AFFILIATE_TERMS, '', 'SSL') . '">Syarat & Kondisi Asosiasi</a>.');
define('ENTRY_AFFILIATE_AGB_ERROR', '�&nbsp;<small><font color="#FF0000">Anda harus menerima Syarat & Kondisi  Asosiasi kami</font></small>');
define('ENTRY_AFFILIATE_PAYMENT_CHECK', 'Periksa Nama Penerima Pembayaran:');
define('ENTRY_AFFILIATE_PAYMENT_CHECK_TEXT', '');
define('ENTRY_AFFILIATE_PAYMENT_CHECK_ERROR', '&nbsp;<small><font color="#FF0000">wajib</font></small>');
define('ENTRY_AFFILIATE_PAYMENT_PAYPAL', 'Email Akun Paypal:');
define('ENTRY_AFFILIATE_PAYMENT_PAYPAL_TEXT', '');
define('ENTRY_AFFILIATE_PAYMENT_PAYPAL_ERROR', '&nbsp;<small><font color="#FF0000">wajib</font></small>');
define('ENTRY_AFFILIATE_PAYMENT_BANK_NAME', 'Nama Bank:');
define('ENTRY_AFFILIATE_PAYMENT_BANK_NAME_TEXT', '');
define('ENTRY_AFFILIATE_PAYMENT_BANK_NAME_ERROR', '&nbsp;<small><font color="#FF0000">wajib</font></small>');
define('ENTRY_AFFILIATE_PAYMENT_BANK_ACCOUNT_NAME', 'Name Akun:');
define('ENTRY_AFFILIATE_PAYMENT_BANK_ACCOUNT_NAME_TEXT', '');
define('ENTRY_AFFILIATE_PAYMENT_BANK_ACCOUNT_NAME_ERROR', '&nbsp;<small><font color="#FF0000">wajib</font></small>');
define('ENTRY_AFFILIATE_PAYMENT_BANK_ACCOUNT_NUMBER', 'Nomor Akun');
define('ENTRY_AFFILIATE_PAYMENT_BANK_ACCOUNT_NUMBER_TEXT', '');
define('ENTRY_AFFILIATE_PAYMENT_BANK_ACCOUNT_NUMBER_ERROR', '&nbsp;<small><font color="#FF0000">wajib</font></small>');
define('ENTRY_AFFILIATE_PAYMENT_BANK_BRANCH_NUMBER', 'Nomor ABA/BSB (Nomor Cabang):');
define('ENTRY_AFFILIATE_PAYMENT_BANK_BRANCH_NUMBER_TEXT', '');
define('ENTRY_AFFILIATE_PAYMENT_BANK_BRANCH_NUMBER_ERROR', '&nbsp;<small><font color="#FF0000">wajib</font></small>');
define('ENTRY_AFFILIATE_PAYMENT_BANK_SWIFT_CODE', 'Kode SWIFT:');
define('ENTRY_AFFILIATE_PAYMENT_BANK_SWIFT_CODE_TEXT', '');
define('ENTRY_AFFILIATE_PAYMENT_BANK_SWIFT_CODE_ERROR', '&nbsp;<small><font color="#FF0000">wajib</font></small>');
define('ENTRY_AFFILIATE_COMPANY', 'Perusahaan');
define('ENTRY_AFFILIATE_COMPANY_TEXT', '');
define('ENTRY_AFFILIATE_COMPANY_ERROR', '&nbsp;<small><font color="#FF0000">wajib</font></small>');
define('ENTRY_AFFILIATE_COMPANY_TAXID', 'Nomor identifikasi VAT.:');
define('ENTRY_AFFILIATE_COMPANY_TAXID_TEXT', '');
define('ENTRY_AFFILIATE_COMPANY_TAXID_ERROR', '&nbsp;<small><font color="#FF0000">wajib</font></small>');
define('ENTRY_AFFILIATE_HOMEPAGE', 'Halaman Beranda');
define('ENTRY_AFFILIATE_HOMEPAGE_TEXT', 'wajib (http://)');
define('ENTRY_AFFILIATE_HOMEPAGE_ERROR', 'Entri homepage Anda diperlukan (http://).');

define('CATEGORY_PAYMENT_DETAILS', 'Anda mendapatkan uang Anda dengan :');

define('TEXT_ORIGIN_AFFILIATE_LOGIN', 'Jika Anda sudah memiliki akun afiliasi dengan kami, silakan login di <a href="%s">halaman login afiliasi</a>.');
?>