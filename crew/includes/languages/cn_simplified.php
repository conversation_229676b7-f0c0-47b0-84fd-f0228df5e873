<?php

/*
  $Id: english.php,v 1.15 2005/04/04 07:08:34 weichen Exp
 */

// look in your $PATH_LOCALE/locale directory for available locales
// or type locale -a on the server.
// Examples:
// on RedHat try 'en_US'
// on FreeBSD try 'en_US.ISO_8859-1'
// on Windows try 'en', or 'English'

@setlocale(LC_TIME, 'en_US.ISO_8859-1');

define('DATE_FORMAT_SHORT', '%m/%d/%Y');  //这是用于字符串格式()
define('DATE_FORMAT_LONG', '%A, %d %B, %Y'); // 这是用于字符串格式()
define('DATE_FORMAT', 'm/d/Y'); // 这是用于日期()
define('DATE_TIME_FORMAT', DATE_FORMAT_SHORT . ' %H:%M:%S');

define('LOCAL_STORE_NAME', 'OffGamers');

// #CHAVEIRO16# BEGIN PHPBB2
define('BOX_BBINDEX', 'tmf.Forum');
// #CHAVEIRO16# END PHPBB2
//CGDiscountSpecials start
define('PRICES_LOGGED_IN_TEXT', '必须为价格先登录!');
//CGDiscountSpecials end
// if USE_DEFAULT_LANGUAGE_CURRENCY 时所用的语言改变
define('LANGUAGE_CURRENCY', 'USD');
define('LANGUAGE_ZONES', '4');

// 全球参赛作品为 <html> tag
define('HTML_PARAMS', 'dir="LTR" lang="cn" xmlns="http://www.w3.org/1999/xhtml" xmlns:fb="http://www.facebook.com/2008/fbml"');

// 字符的网页和电子邮件
define('CHARSET', 'UTF-8');
define('EMAIL_CHARSET', 'gb2312');

// 网页标题
define('TITLE', STORE_NAME);

// 开始的最新消息
define('TABLE_HEADING_LATEST_NEWS', '最新消息');
define('ALL_PRODUCTS_LINK', '输入游戏关键字');

// header text in includes/header.php
define('HEADER_TITLE_CREATE_ACCOUNT', '注册帐户');
define('HEADER_TITLE_MY_ACCOUNT', '我的帐户');
define('HEADER_TITLE_CART_CONTENTS', '查看购物车');
define('HEADER_TITLE_CHECKOUT', '结账');
define('HEADER_TITLE_TOP', '首页');
define('HEADER_TITLE_CATALOG', '产品目录');
define('HEADER_TITLE_LOGOFF', '登出');
define('HEADER_TITLE_LOGIN', '我的帐户');
define('HEADER_TITLE_MY_FAVOURITE_LINKS', '我最喜爱的链接');
define('HEADER_TITLE_MY_PAYMENT_HISTORY', '账户明细');
define('HEADER_TITLE_MY_VIP_ACCOUNT', 'VIP 帐户');
define('HEADER_TITLE_MY_VIP_INVENTORY_UPDATE', '库存批量管理列表');
define('HEADER_TITLE_MY_VIP_REGISTER_SERVER', '登记库存列表');
define('HEADER_TITLE_MY_VIP_ORDERS_HISTORY', '订单操作');
define('HEADER_TITLE_MY_VIP_REPORT', '销售报告');
define('HEADER_TITLE_SHOW_ALL', '查看所有');

define('HEADER_RECOMMENDED_PAYMENT_GATEWAY', '付款方式');
define('HEADER_GAME_NEWS_STORE_ANNOUNCEMENT', '游戏新闻/商店公布');

define('TEXT_LINK_SECURE_LOGIN', '安全方式登录');

define('TEXT_SECURITY_CODE', '验证码');
define('TEXT_LATEST_NEWS', '最新新闻');
define('TEXT_NOTICE_BOARD', '布告栏');
define('TEXT_GAME_INFO', '游戏资讯');
define('TEXT_TIPS', 'Tips');
define('TEXT_POLL_QUESTION', '评估我们的服务.');
define('TEXT_ACCOUNT_FREEZE', '抱歉，您的帐号已被封锁，请联系网站客服谢谢');
define('TEXT_PIN_NUMBER', 'PIN 号码');
define('TEXT_PINNUMBER_REQUIRED', '需要PIN 号码才能更新 : %s');

define('TEXT_ACCOUNT_TOTAL', '账号总额');
define('TEXT_ACTIVE', '正常');
define('TEXT_BACK', '返回');
define('TEXT_CONFIRM', '确定');
define('TEXT_FINISHED', '完成，请现在新建我的账号.');
define('TEXT_INACTIVE', '已满');
define('TEXT_MAIN_PLEASE_SELECT_GAME', '《---请选择游戏，查询报价');
define('TEXT_MESSAGE_LIST_CLOSE', '已提交订单已饱和，请您在倒数到0后再行提交。（现在倒数：<b>%d</b>）');
define('TEXT_MESSAGE_BREAK_TIME', '卧虎网站暂停交易，服务器将在倒数时间到来时重新启动，给您带来不便敬请谅解 （倒数时间：%02d小时%02d分钟）');
define('TEXT_WEBSITE_NOTE_DESC', '<ol><li>游戏提示您：与本公司交易咨询时，请认准网站公布的采购QQ，避免有人冒充卧虎游戏采购客服进而给您带来不必要的损失。<li>供应商注册卧虎游戏帐号时会设定相应的密保问答（Q&A），这是我们提供相应安全服务的最后保障，请妥善保管并牢记答案；如因密保问答（Q&A）丢失或泄漏导致您帐号资金和信息损失，卧虎公司概不负责，请千万注意。<li>卧虎游戏针对游戏服务器暂时维护（重启）时的交易金币回流现象，提醒用户务必在维护（重启）后交易，如因用户不遵守此规定造成的交易损失，卧虎游戏概不负责。<li>卧虎游戏投诉与意见信箱：<a href="mailto:<EMAIL>?subject=卧虎游戏投诉或意见信箱"><span class="redIndicator"><EMAIL></span></a> 接到投诉后12小时内回复解决意见</li></ol>');
define('TEXT_OPTIONAL', '(可不填)');
define('TEXT_VIP_ORDER_FLOW', '流程说明');

define('TEXT_NEXT', '下一页');
define('TEXT_SAVE', '储存');
define('TEXT_SEARCH', '寻找');
define('TEXT_SUBMIT', '提交');
define('TEXT_UPDATE', '更新');
define('TEXT_UPDATE_AND_NEXT', '更新 >>');
define('TEXT_WITHDRAW', '提款');

define('TEXT_ORDERS', "订单");
define('TEXT_PENDING', "审核中");
define('TEXT_PROCESSING', "处理中");

define('JAVASCRIPT_ENFORCEMENT', '请在浏览器中启用JavaScript以便体验到我们网站所有的自定义功能,包括购买产品.');

// footer text in includes/footer.php
define('FOOTER_TEXT_REQUESTS_SINCE', '请求自');
define('OGM_SPECIAL_NOTICE', '<div style="font-size:15px;font-weight:bold;">看看我们的新玩意!</div>
                        	<div style="">近期我们已对网站商铺中的工具栏进行了调整。<br />
                        	 在新工具栏中，您可进行如下操作： <br />
                        	 - 登陆或注册OffGamers帐户(也适用于Facebook Connect！)。<br />
                        	 - 设置/更新您的区域设置（国家，货币和语言）。 <br />
                        	 - 查看/更新您购物车内的物品或结账付款。<br />
                        	</div>');

//customer order comment remark show at backend (admin)
define('TEXT_VIP_BUYBACK_ORDER_REFERENCE_REMARK', 'Delivered via VIP Order: ##BO##%d##');
define('TEXT_BUYBACK_ORDER_REFERENCE_REMARK', 'Delivered via Buyback Order: ##BO##%d##');
define('TEXT_BUYBACK_ORDER_ITEMS_DELIVERED_REMARK', 'The following items have been delivered:');
define('TEXT_BUYBACK_ORDER_ITEMS_DEDUCTED_REMARK', 'The following items have been deducted:');

// 案文日，月，年
define('DAY', '日');
define('DAY2', '天');
define('MONTH', '月');
define('YEAR', '年');
define('HOURS', '小时');
define('MINUTES', '分钟');
define('SECONDS', '秒');

// 案文性别
define('MALE', '男性');
define('FEMALE', '女性');
define('MALE_ADDRESS', '先生');
define('FEMALE_ADDRESS', '女士');

// 电子邮件贺卡
define('EMAIL_SEPARATOR', '------------------------------------------------------');
define('EMAIL_GREET_MR', '亲爱的. %s先生 ,' . "\n\n");
define('EMAIL_GREET_MS', '亲爱的. %s女士,' . "\n\n");
define('EMAIL_GREET_NONE', '亲爱的 %s,' . "\n\n");

// 电子邮件回购
define('EMAIL_BUYBACK_UPDATE_SUBJECT', implode(' ', array(EMAIL_SUBJECT_PREFIX, "回购订单#%s最新情况")));
define('EMAIL_BUYBACK_UPDATE_BODY', '回购订单摘要:' . "\n" . EMAIL_SEPARATOR . "\n");
define('EMAIL_BUYBACK_UPDATE_ORDER_NUMBER', '订单号码: %s');
define('EMAIL_BUYBACK_UPDATE_DATE_ORDERED', '订单日期: %s');
define('EMAIL_BUYBACK_UPDATE_STATUS', '状况: %s');
define('EMAIL_BUYBACK_UPDATE_FOOTER', "\n\n" . STORE_EMAIL_SIGNATURE);

define('EMAIL_BUYBACK_UPDATE_CUSTOMER_EMAIL', '客户的电邮地址: %s');
define('EMAIL_BUYBACK_UPDATE_PRODUCTS', '产品');
define('EMAIL_BUYBACK_UPDATE_ORDER_TOTAL', '总计: %s %s');
define('EMAIL_NEW_BUYBACK_STATUS', '状况:待定中');
define('EMAIL_NEW_BUYBACK_ORDER_NUMBER', '回购订单号码: %s');
define('EMAIL_NEW_BUYBACK_DATE_ORDERED', '回购订单日期: %s');
define('EMAIL_NEW_BUYBACK_CUSTOMER_EMAIL', '电子邮件地址: %s');

define('EMAIL_BUYBACK_PRESALES_SUBJECT', '回购服务器通知');
define('EMAIL_LOCAL_STORE_EMAIL', '<EMAIL>');
define('EMAIL_BUYBACK_PRESALES_TITLE', '感谢您的支持！你现在可以登录到您的 ' . STORE_NAME . ' 并出售您已选的服务器游戏货币.');
define('EMAIL_BUYBACK_PRESALES_SUMMARY_TITLE', '回购服务器摘要:');
define('EMAIL_BUYBACK_PRESALES_PRODUCT', '服务器 %s');
define('EMAIL_BUYBACK_PRESALES_MIN_QTY', '最低: %d');
define('EMAIL_BUYBACK_PRESALES_MAX_QTY', '最高: %d');
define('EMAIL_BUYBACK_PRESALES_COMMENTS', '注意：' . "\n\n" . STORE_NAME . ' 只提供游戏货币回购给成员，而是先到先得，不要犹豫和错过了出售游戏货币的机会。谢谢您！');
define('EMAIL_LOCAL_STORE_EMAIL_SIGNATURE', "\n" . STORE_NAME . "\n" . '*************************************************' . "\n" . 'E-mail: <a href="mailto:' . EMAIL_LOCAL_STORE_EMAIL . '">' . EMAIL_LOCAL_STORE_EMAIL . '</a>');

define('EMAIL_TEXT_STATUS_UPDATE_TITLE', '');
define('EMAIL_TEXT_ORDER_NUMBER', '订单号码:');
define('EMAIL_TEXT_DATE_ORDERED', '订单日期:');
define('EMAIL_TEXT_INVOICE_URL', '详述的发票：');
define('EMAIL_TEXT_SUBJECT', '更新订单#%d');
define('EMAIL_TEXT_PARTIAL_DELIVERY', '');
define('EMAIL_TEXT_CLOSING', '如果您要咨询或寻求帮助，请联系我们的在线客服平台或者发送您想咨询的问题到 ' . EMAIL_TO . '。 与我们联系时，请不要忘记附上您的订单号码，以方便我们尽快解决您的问题。 再次感谢您在' . STORE_NAME . '购物。 \n');

// 案文的出生日期，例如
define('DOB_FORMAT_STRING', 'mm/dd/yyyy');

// Mantis # 0000024 @ ************ - Add new boxes
define('BOX_HEADING_ACCOUNT_LEFT_BOX_MY_ACCOUNT', '我的帐户');
define('BOX_HEADING_ACCOUNT_LEFT_BOX_MY_PROFILE', '我的简介');
define('BOX_HEADING_ACCOUNT_LEFT_BOX_STORE_CREDITS', '购物代金券');
define('BOX_HEADING_ACCOUNT_LEFT_BOX_BUYER', '买方');
define('BOX_HEADING_ACCOUNT_LEFT_BOX_SELLER', '卖方');
define('BOX_HEADING_ACCOUNT_LEFT_BOX_VIP_SELLER', 'VIP 卖方');
define('BOX_HEADING_ACCOUNT_LEFT_BOX_AFFILIATE', '合作联盟');

define('MY_ACCOUNT_ACCOUNT_OVERVIEW_LINK', '概况');
define('MY_ACCOUNT_FACEBOOK_CONNECT', 'Facebook Connect');
define('MY_ACCOUNT_EDIT_PROFILE_LINK', '编辑简介');
define('MY_ACCOUNT_CHANGE_SECRET_QNA_LINK', '更改密保问答');

define('MY_ACCOUNT_NOTIFICATION_SETTINGS_LINK', '通知设置');
define('MY_ACCOUNT_MANAGE_NEWSLETTERS_LINK', '管理电子月刊');
define('MY_ACCOUNT_OFFGAMERS_POINTS', 'OP');
define('MY_ACCOUNT_MY_ORDERS_LINK', '我的订单');
define('MY_ACCOUNT_VERIFY_PHONE_NUMBER_LINK', '手机号码验证');
define('MY_ACCOUNT_VERIFY_EMAIL_LINK', '电子邮址验证');
define('MY_ACCOUNT_VERIFICATION_SUBMISSION_FORM', '身份验证提交表');

define('MY_ACCOUNT_STORE_CREDITS_HISTORY_LINK', '购物代金券历史记录');
define('MY_ACCOUNT_STORE_CREDITS_CONVERSION_LINK', '购物代金券转换');
define('MY_ACCOUNT_STORE_CREDITS_TOPUP_LINK', '充值购物代金券');

define('MY_ACCOUNT_BUY_ORDERS_HISTORY_LINK', '购买订单历史纪录');
define('MY_ACCOUNT_MY_ORDERS_LINK', '我的订单');
define('MY_ACCOUNT_STORE_CREDITS_LINK', '购物代金券');
define('MY_ACCOUNT_SPENDING_LIMIT_LINK', '支出限额');

define('MY_ACCOUNT_SELL_ORDER_HISTORY_LINK', '出售订单历史记录');
define('MY_ACCOUNT_WITHDRAW_STATEMENT_LINK', '提款明细');
define('MY_ACCOUNT_WITHDRAW_MONEY_LINK', '提款');
define('MY_ACCOUNT_PAYMENT_STATUS_LINK', ' 付款状态');
define('MY_ACCOUNT_FAVORITE_SERVERS_LINK', '快捷下单');

// header text in includes/boxes/my_account.php VIP SELLER
define('MY_ACCOUNT_VIP_INVENTORY_UPDATE', '库存批量管理列表');
define('MY_ACCOUNT_VIP_REGISTER_SERVER', '登记库存列表');
define('MY_ACCOUNT_VIP_ORDERS_HISTORY', '订单操作');
define('MY_ACCOUNT_VIP_REPORT', '销售报告');
// M#0000024

define('MY_ACCOUNT_AFFILIATE_OVERVIEW_LINK', '合作联盟概况');
define('MY_ACCOUNT_INVITE_FRIENDS_LINK', '邀请好友');

// Mantis # 0000024 @ ************ - My Account Phase 2
define('TITLE_MY_PROFILE', '我的简介');
define('IMAGE_BUTTON_SAVE_PROFILE', '保存简介');
define('MY_ACCOUNT_CURRENT_ORDERS', '当前订单');
define('MY_ACCOUNT_COMPLETED_ORDERS', '已完成订单');
define('MY_ACCOUNT_CANCELLED_ORDERS', '已取消订单');

// My Account Home alert messages
define('TEXT_CLICK_HERE', '点击这里');
define('TEXT_LIVE_SUPPORT', '客户服务');
define('TEXT_LIVE_CHAT', '&nbsp;提交问题单');

// login box text in includes/boxes/loginbox.php
define('BOX_HEADING_LOGIN_BOX_MY_ACCOUNT', '我的帐户');
define('LOGIN_BOX_MY_ACCOUNT', '帐户概观');
define('LOGIN_BOX_ACCOUNT_EDIT', '编辑资料');
define('LOGIN_BOX_ACCOUNT_PAYMENT_EDIT', '支付方式');
define('LOGIN_BOX_ACCOUNT_STORE_CREDIT', '购物代金券');
define('LOGIN_BOX_ADDRESS_BOOK', '编辑地址');
define('LOGIN_BOX_VERIFY_EMAIL', '确认电子邮件<span class="requiredInfo">*<span>');
define('LOGIN_BOX_ACTIVATE_ACCOUNT', '启动帐户<span class="requiredInfo">*<span>');
define('LOGIN_BOX_ACCOUNT_HISTORY', '查看订单历史');
define('LOGIN_BOX_PRODUCT_NOTIFICATIONS', '产品通知');
define('LOGIN_BOX_BUYBACK_ORDER_HISTORY', '查看回购历史');
define('LOGIN_BOX_BUYBACK_FAVOURITE', '查看最喜爱的回购');
define('LOGIN_BOX_ACCOUNT_ACTIVATION', '启动帐户');
define('LOGIN_BOX_PASSWORD_FORGOTTEN', '忘记密码?');
define('LOGIN_BOX_HEADING', '登陆或注册');
define('LOGIN_BOX_LOGIN_SELECTION', '我已经是OffGamers会员，我想现在登陆。');
define('LOGIN_BOX_CREATE_ACCOUNT_SELECTION', '我是新客户，我想创建新账户。');
define('LOGIN_BOX_SEND_PASSWORD_SELECTION', '请输入您的邮箱地址，我们会将您的密码发送至您的邮箱。');
define('LOGIN_EMAIL', '电子邮件地址');
define('LOGIN_PASSWORD', '密码');
define('LOGIN_PASSWORD_NEW', '新密码');
define('LOGIN_PASSWORD_NEW_CONFIRMATION', '确认新密码');
define('LOGIN_RETURN', '返回登录');
define('CONFIRM_LOGIN_PASSWORD_DESC', '为安全起见，您用购物代金券付款时，需要输入OffGamers登录名及密码。');

define('TEXT_WITHDRAWABLE_CREDIT', '可取回金额');
define('HEADER_TITLE_MY_WITHDRAW_MONEY', '提款');

// categories box text in includes/boxes/categories.php
define('BOX_HEADING_CATEGORIES', '类别');

// manufacturers box text in includes/boxes/manufacturers.php
define('BOX_HEADING_MANUFACTURERS', '制造商');

// whats_new box text in includes/boxes/whats_new.php
define('BOX_HEADING_WHATS_NEW', '新消息');

// quick_find box text in includes/boxes/quick_find.php
define('BOX_HEADING_SEARCH', '快速搜索');
define('BOX_SEARCH_TEXT', '用关键字寻找你的产品.');
define('TEXT_ALL_CATEGORIES', '所有类别');
define('BOX_SEARCH_ADVANCED_SEARCH', '高级搜索');

// specials box text in includes/boxes/specials.php
define('BOX_HEADING_SPECIALS', '特销');

// reviews box text in includes/boxes/reviews.php
define('BOX_HEADING_REVIEWS', '评语');
define('BOX_REVIEWS_WRITE_REVIEW', '写关于这个产品的评语!');
define('BOX_REVIEWS_NO_REVIEWS', '目前没有任何产品评论');
define('BOX_REVIEWS_TEXT_OF_5_STARS', '%s之5星!');

// shopping_cart box text in includes/boxes/shopping_cart.php
define('BOX_HEADING_SHOPPING_CART', '购物车');
define('SHOPPING_CART_BOX_EDIT_CART', '编辑购物车');
define('BOX_SHOPPING_CART_EMPTY', '没有产品');
define('TEXT_LATEST_PRODUCT_ADDED', '已添加到您的购物车:');

// order_history box text in includes/boxes/order_history.php
define('BOX_HEADING_CUSTOMER_ORDERS', '订单历史');

// best_sellers box text in includes/boxes/best_sellers.php
define('BOX_HEADING_BESTSELLERS', '最好卖');
define('BOX_HEADING_BESTSELLERS_IN', '最好卖在<br>&nbsp;&nbsp;');

// notifications box text in includes/boxes/products_notifications.php
define('BOX_HEADING_NOTIFICATIONS', '通知');
define('BOX_NOTIFICATIONS_NOTIFY', '<b>%s</b>更新时通知我');
define('BOX_NOTIFICATIONS_NOTIFY_REMOVE', '<b>%s</b>更新时不必通知我');

// manufacturer box text
define('BOX_HEADING_MANUFACTURER_INFO', '制造商 Info');
define('BOX_MANUFACTURER_INFO_HOMEPAGE', '%s 网页');
define('BOX_MANUFACTURER_INFO_OTHER_PRODUCTS', '其他产品');

// languages box text in includes/boxes/languages.php
define('BOX_HEADING_LANGUAGES', '语言');

// currencies box text in includes/boxes/currencies.php
define('BOX_HEADING_CURRENCIES', '选择您的货币');

define('BOX_HEADING_HELP_DESK', '帮助台');

// polling box text in includes/boxes/polling.php
define('BOX_HEADING_POLLING', 'offgamers投票');

// information box text in includes/boxes/information.php
define('BOX_HEADING_INFORMATION', '资料');
define('BOX_INFORMATION_CONDITIONS', '使用条件');
define('BOX_INFORMATION_SHIPPING', '运送和退货');
define('BOX_INFORMATION_ABOUT_US', '关于我们');
define('BOX_HEADING_LINKS', '链接');

define('BOX_INFORMATION_NEWS', '最新动态与促销活动');
define('BOX_INFORMATION_TOS', '服务条款');
define('BOX_INFORMATION_PRIVACY', '隐私权政策');
define('BOX_INFORMATION_FAQ', '常见问题解答');
define('BOX_INFORMATION_CONTACT_WEB', '联系我们的表格');
define('BOX_INFORMATION_CONTACT', '联系我们');
define('BOX_INFORMATION_PROMOTION', '促销');
define('BOX_INFORMATION_DISCLAIMER', '免责声明');
define('BOX_LINKS', '链接');

// tell a friend box text in includes/boxes/tell_a_friend.php
define('BOX_HEADING_TELL_A_FRIEND', '推荐给朋友');
define('BOX_TELL_A_FRIEND_TEXT', '把产品推荐给您认识的人.');

// regional setting
define('BOX_HEADING_REGIONAL_SETTING', '区域设定');
define('TEXT_REGIONAL_COUNTRY', '区域');
define('TEXT_LANGUAGE', '语言');
define('TEXT_REGIONAL_NOTE', '您的设置将作为下次访问时的默认区域设置。');

//BEGIN allprods modification
define('BOX_INFORMATION_ALLPRODS', '查看所有产品');
//END allprods modification

define('BOX_CHANGE_PASSWORD', '更改密码');
define('BOX_IMPORTANT_NOTICE', '重要通知');
define('BOX_ERROR_INVALID_ANSWER', '答案不正确。');
define('BOX_ERROR_INVALID_LAST4DIGIT', '号码后4位数字不正确。');
define('BOX_SUCCESS_VALIDATE_LAST4DIGIT', '您的安全码已经成功发送至您注册的邮箱。');
define('BOX_CHANGE_CONTACT_NOTICE', '自2012年5月16日起，我们的电话验证系统，将不再支持固定电话或网络电话号码验证。为了进一步保障您账户的安全性，请符合以下条件的OffGamers会员在您的账户中，更新手机号码 :-<br>A.账户中联系方式登记为固定电话的客户<br>B.有多个账户，并且共享电话号码的客户<br><br>这一举措是为了配合我们即将开放的最新电话验证系统，由此给您带来的不便，敬请谅解！');
define('BOX_UPDATE_YOUR_MOBILE_NUMBER', '更新您的手机号码');
define('BOX_CHANGE_CONTACT_FOR_SECURITY_REASONS', '为安全起见，请将您账户内的电话号码，更新为您目前使用的手机号码以完成您的账户验证。您可以通过填写电话号码的后4位或者提交密保问答，来完成此过程。');
define('BOX_CHANGE_CONTACT_FILL_LAST4DIGIT', '鉴于安全认证，请输入您当前电话号码的最后4位数字:');
define('BOX_CHANGE_CONTACT_FILL_LAST4DIGIT_DESC', '如果您之前填写的手机号码已经丢失或者不再使用，请申请安全口令。安全码将发送到您注册的电子邮箱中。');
define('BOX_CHANGE_CONTACT_FOOTER_DESC', '如果您有任何疑问，请不要犹豫，立即<a href="' . tep_href_link(FILENAME_CUSTOMER_SUPPORT, '', 'SSL') . '">联系我们</a>。 我们的客服很荣幸为您服务。');
define('BOX_CHANGE_CONTACT_OR', '或');

// checkout procedure text
define('CHECKOUT_BAR_DELIVERY', '送货<br>资料');
define('CHECKOUT_BAR_INFO', '结账资料');
define('CHECKOUT_BAR_PAYMENT', '付款<br>资料');
define('CHECKOUT_BAR_CONFIRMATION', '确认');
define('CHECKOUT_BAR_FINISHED', '完成');

// pull down default text
define('TEXT_ALL_PAGES', '全部');
define('PULL_DOWN_DEFAULT', '请选择');
define('TYPE_BELOW', '下面键入');
define('TEXT_REFRESH_WHEN_CHANGE', '(改变时网页将刷新)');
define('PULL_DOWN_DEFAULT_GAME_CARD', '选择游戏点卡价格');

// JavaScript的信息
define('JS_ERROR', '处理表格过程中发生错误.\n\n请做出以下改正:\n\n');

define('JS_REVIEW_TEXT', '* \'Review Text\'必须有至少' . '10' . '字符.\n');
define('JS_REVIEW_RATING', '* 产品评估需要评分.\n');
define('JS_REVIEW_CUSNAME', '*  您必须输入您的 \'First Name\'.\n');
define('JS_ERROR_NO_PAYMENT_MODULE_SELECTED', '* 请为您的订单选择付款方式.\n');
define('JS_ERROR_NO_SHIPPING_MODULE_SELECTED', '* 请为您的订单选择送货方式r.\n');

define('JS_ERROR_SUBMITTED', '这种表格已提交. 请按下确定,然后请耐心等待这个过程.');

define('JS_ERROR_PRODUCT_EMPTY', '请选择游戏点卡价格');

define('ERROR_NO_PAYMENT_MODULE_SELECTED', '请为您的订单选择付款方式.');
define('ERROR_NO_PAYMENT_MODULE_NEEDED', '因为可以从您的商店信用中扣除总金额，所以您不必选择付款方式.');
define('ERROR_CART_COMMENTS_REQUIRED', '请在您的订单里填写所需的留言!');
define('ERROR_SIGNUP_CART_COMMENTS_REQUIRED', '请在登记表格填写所需的留言!');

define('ERROR_PLS_ENTER_ACCOUNT_NAME', '请输入帐户名称');
define('ERROR_PLS_ENTER_CHAR_NAME', '请输入游戏角色名');
define('ERROR_PLS_ENTER_PASSWORD', '请输入密码');
define('ERROR_EMAIL_VERIFICATION_CODE', '验证码必须包含12位数字。');
define('ERROR_WRONG_PASSWORD', '密码错误，请重试。');

define('CATEGORY_COMPANY', '公司详情');
define('CATEGORY_VERIFY_EMAIL', '电子邮件验证');
define('CATEGORY_PERSONAL', '您的个人详细资料');
define('CATEGORY_ADDRESS', '您的地址');
define('CATEGORY_CONTACT', '您的联络资料');
define('CATEGORY_OPTIONS', '选项');
define('CATEGORY_PASSWORD', '您的密码');

define('ENTRY_ACCOUNT_NAME', '帐户名称');
define('ENTRY_WOW_ACCOUNT_NAME', '魔兽帐号名');
define('ENTRY_ADD_CHARACTER_NAME', '填写角色的名称');
define('ENTRY_CHARACTER_INGAME_TIME', '<td class="smallText"><div style="padding:5px 10px 5px 20px;float:left">距离登录时间:</div></td><td class="smallText">%s <span class="redIndicator">*</span>小时 %s</td>');
define('ENTRY_CHARACTER_INGAME_DURATION', '<td class="smallText"><div style="padding:5px 10px 5px 20px;float:left">游戏里持续时间:</div></td><td class="smallText">%s 小时 %s</td>');
define('TEXT_CHARACTER_INGAME_NOTE', '这是您结账后,将登录游戏的时间');
define('TEXT_CHARACTER_INGAME_HOUR_NOTE', '这是您在游戏内的时间');
define('ENTRY_ORDER_GUYA_ACCOUNT', '<td class="smallText" style="padding:5px 10px 5px 20px;" align="left" nowrap>帐户名称:</td><td class="smallText">%s <span class="redIndicator">*</span></td>');
define('ENTRY_ORDER_GUYA_PWD', '<td class="smallText" style="padding:5px 10px 5px 20px;" align="left" nowrap>密码:</td><td class="smallText">%s <span class="redIndicator">*</span></td>');
define('ENTRY_QNA', '<td class="smallText" style="padding:5px 10px 5px 20px;" align="left" nowrap>答案:</td><td class="smallText">%s <span class="redIndicator">*</span></td>');
define('ENTRY_ORDER_GUYA_WOW_ACCOUNT_INFO', '<td class="smallText" style="padding:5px 10px 5px 20px;" align="left">&nbsp;</td><td class="smallText">购买魔兽金币，请填写您的Battle.net账户.</td>');
define('ENTRY_ORDER_GUYA_WOW_ACCOUNT_INFO_TEXT', '购买魔兽金币，请填写您的Battle.net账户。');
define('ENTRY_ORDER_GUYA_WOW_ACCOUNT', '<td class="smallText" style="padding:5px 10px 5px 20px;" align="left" nowrap>魔兽帐号名:</td><td class="smallText">%s 可选</td>');
define('ENTRY_ORDER_GUYA_WOW_ACCOUNT_INFO2', '<td class="smallText" style="padding:5px 10px 5px 20px;" align="left">&nbsp;</td><td class="smallText">仅适用于魔兽.</td>');
define('ENTRY_ORDER_GUYA_WOW_ACCOUNT_INFO2_TEXT', '仅适用于魔兽。');
define('TEXT_ORDER_FACE_TO_FACE_NOTE', '如果您选择面对面或者摆摊交易方式，请选择并调整在线时长。<b>(<font color="red">注意</font>:“我在线”按钮启动后，我们才会为您寻找货源)</b><br /><br /><span class="redIndicator">注：购买<b>无尽之夜</b>时，请填写您的角色名字和物品名字 "(例如：playername@handle(Item Name) "购买钻石星芒的交易方式只能通过拍卖场交易。</span><br /><br /><span class="redIndicator">注：购买<b>暗黑3</b>ss时，请填写您的battletag (例如：ABC#1234) 并非账户名。</span>');
define('TEXT_ORDER_GUYA_NOTE', '我们将登录到您的帐户然后把货币转让去您所指定的角色.');
define('TEXT_ORDER_MAIL_NOTE', '我们将货币寄去您所指定的角色的游戏邮箱里<br /><br /><span class="redIndicator">注：购买行会战争2时，请填写您的display name (例如：ABC.1234)。</span>');
define('TEXT_ORDER_OPEN_STORE_NOTE', '一旦您将交易方式更改为面对面交易或摆摊交易,请选择您的在线时段 <b>(<font color="red">注意</font>: 只有您点击"我在线"后，我们才会开始为您寻找货源)</b>');
define('TEXT_ORDER_CHANGE_DEFAULT_CHARACTER', '更改您的默认游戏角色');
define('TEXT_PERSONAL_INFO_REQUIRED', '在付款前请填写您的 %s，以便我们验证和处理您的订单。（在以后的订单中就无需再这样做）');
define('ENTRY_VERIFY_EMAIL', '电子邮件确认');
define('ENTRY_VERIFY_EMAIL_TEXT', '*');
define('ENTRY_VERIFY_EMAIL_ERROR', '电子邮件确认必须符合您的电子邮件.');
define('ENTRY_COMPANY', '公司名称:');
define('ENTRY_COMPANY_ERROR', '');
define('ENTRY_COMPANY_TEXT', '');
define('ENTRY_GENDER', '性别:');
define('ENTRY_GENDER_ERROR', '请选择您的性别.');
define('ENTRY_GENDER_TEXT', '*');
define('ENTRY_CUSTOMER_IDENTIFY_NUMBER', '身份证号码：');
define('ENTRY_FIRST_NAME', '名字:');
define('ENTRY_FIRST_NAME_ERROR', '您的名字必须至少 ' . ENTRY_FIRST_NAME_MIN_LENGTH . '字符.');
define('ENTRY_FIRST_NAME_TEXT', '*');
define('ENTRY_LAST_NAME', '姓氏:');
define('ENTRY_LAST_NAME_ERROR', '您的姓氏必须至少 ' . ENTRY_LAST_NAME_MIN_LENGTH . ' 字符.');
define('ENTRY_LAST_NAME_TEXT', '*');
define('ENTRY_DATE_OF_BIRTH', '出生日期:');
define('ENTRY_DATE_OF_BIRTH_OVER_ERROR', '出生日期错误，请重新输入');
define('ENTRY_DATE_OF_BIRTH_FUTURE_ERROR', '出生日期错误，你已进入一个未来的日期');
define('ENTRY_DATE_OF_BIRTH_ERROR', '您的出生日期必须包含一个有效的日期.');
define('ENTRY_DATE_OF_BIRTH_ERROR_1', '你必须选择一个');
define('ENTRY_DATE_OF_BIRTH_ERROR_2', ' 从');
define('ENTRY_DATE_OF_BIRTH_ERROR_3', ' 列表');
define('ENTRY_DATE_OF_BIRTH_ERROR_4', '年必须包含4位数字.');
define('ENTRY_DATE_OF_BIRTH_TEXT', '* (eg. 05/21/1970)');
define('ENTRY_EMAIL_ADDRESS', '邮箱地址:');
define('ENTRY_EMAIL_ADDRESS_ERROR', '您的电子邮件地址，需至少 ' . ENTRY_EMAIL_ADDRESS_MIN_LENGTH . '字符.');
define('ENTRY_EMAIL_ADDRESS_CHECK_ERROR', '您的电子邮件地址似乎有错误-请作出任何必要的更正.');
define('ENTRY_EMAIL_ADDRESS_ERROR_EXISTS', '您的电子邮件地址存在我们的记录，请 <a href="' . tep_href_link(FILENAME_LOGIN) . '"> 登入 </a>或如果您忘记了密码 <a href="' . tep_href_link(FILENAME_PASSWORD_FORGOTTEN) . '">按此</a>.');
define('ENTRY_EMAIL_ADDRESS_TEXT', '*');
define('ENTRY_STREET_ADDRESS', '街道地址:');
define('ENTRY_STREET_ADDRESS_ERROR', '您的街道地址必须至少 ' . ENTRY_STREET_ADDRESS_MIN_LENGTH . '字符.');
define('ENTRY_STREET_ADDRESS_TEXT', '*');
define('ENTRY_SUBURB', '郊区:');
define('ENTRY_SUBURB_ERROR', '');
define('ENTRY_SUBURB_TEXT', '');
define('ENTRY_POST_CODE', '邮编:');
define('ENTRY_POST_CODE_ERROR', '您的邮递区必须至少 ' . ENTRY_POSTCODE_MIN_LENGTH . '字符.');
define('ENTRY_POST_CODE_TEXT', '*');
define('ENTRY_CITY', '城市:');
define('ENTRY_CITY_ERROR', '您所在的城市必须至少' . ENTRY_CITY_MIN_LENGTH . '字符.');
define('ENTRY_CITY_TEXT', '*');
define('ENTRY_STATE', '州/省:');
define('ENTRY_STATE_ERROR', '您的州必须至少' . ENTRY_STATE_MIN_LENGTH . '字符.');
define('ENTRY_STATE_ERROR_SELECT', '请从列表选择一个州.');
define('ENTRY_STATE_TEXT', '*');
define('ENTRY_COUNTRY', '国家:');
define('ENTRY_LOCATION', '位置:');
define('ENTRY_COUNTRY_ERROR', '请从列表选择一个国家.');
define('ENTRY_LOCATION_ERROR', '请从列表选择一个位置.');
define('ENTRY_COUNTRY_TEXT', '*');
define('ENTRY_TELEPHONE_NUMBER', '手机号码:');
define('ENTRY_TELEPHONE_NUMBER_ERROR', '您的手机号码必须至少' . ENTRY_TELEPHONE_MIN_LENGTH . '字符.');
define('ENTRY_TELEPHONE_NUMBER_TEXT', '*');
define('ENTRY_FAX_NUMBER', '传真号码:');
define('ENTRY_FAX_NUMBER_ERROR', '');
define('ENTRY_FAX_NUMBER_TEXT', '');
define('ENTRY_NEWSLETTER', '电子报:');
define('ENTRY_NEWSLETTER_TEXT', '订阅我们的月刊，将获得特别优惠，独家促销活动！');
define('ENTRY_NEWSLETTER_YES', '订阅');
define('ENTRY_NEWSLETTER_NO', '退订');
define('ENTRY_NEWSLETTER_ERROR', '');
define('ENTRY_PASSWORD', '密码:');
define('ENTRY_REMEMBER_ME', '记住我的帐号');
define('ENTRY_PASSWORD_ERROR', '您的密码必须至少 ' . ENTRY_PASSWORD_MIN_LENGTH . ' 字符.');
define('ENTRY_PASSWORD_ERROR_NOT_MATCHING', '密码确认必须符合您的密码.');
define('ENTRY_PASSWORD_TEXT', '*');
define('ENTRY_PASSWORD_CONFIRMATION', '确认密码:');
define('ENTRY_PASSWORD_CONFIRMATION_TEXT', '*');
define('ENTRY_PASSWORD_CURRENT', '当前密码:');
define('ENTRY_PASSWORD_CURRENT_TEXT', '*');
define('ENTRY_PASSWORD_CURRENT_ERROR', '您的密码必须至少 ' . ENTRY_PASSWORD_MIN_LENGTH . ' 字符.');
define('ENTRY_PASSWORD_NEW', '新密码:');
define('ENTRY_PASSWORD_NEW_TEXT', '*');
define('ENTRY_PASSWORD_NEW_ERROR', '您的新密码必须至少 ' . ENTRY_PASSWORD_MIN_LENGTH . ' 字符.');
define('ENTRY_PASSWORD_NEW_ERROR_NOT_MATCHING', '密码确认必须符合您的新密码.');
define('PASSWORD_HIDDEN', '-隐藏-');
define('ENTRY_SERIAL_ERROR', '请输入序号');
define('ENTRY_SERIAL_CHECK_ERROR', '您的序列中必须包含12个字符.');
define('ENTRY_MISMATCH_ANSWER_ERROR', '验证码不正确，请重新输入。');
define('ENTRY_MISMATCH_ANSWER_RESEND_LINK', '否则，请点击<a href="%s">这里</a>。');
define('ENTRY_CHARACTER_NAME', '角色名');
define('ENTRY_REBATE', '总共回扣');
define('ENTRY_ADDED_BONUS', '额外赠送');
define('ENTRY_DELIVERY_INFORMATION', '输入您的账户资料');

define('TEXT_NAME', '名字');
define('TEXT_BILLING_ADDRESS', '地址');
define('TEXT_STATE_OR_ZIP', '州/邮编');
define('TEXT_FIRST_NAME', '名字');
define('TEXT_LAST_NAME', '姓氏');
define('TEXT_CONTACT_NUMBER', '联系电话');
define('TEXT_BILLING_ADDRESS1', '地址1');
define('TEXT_COUNTRY_CODE', '国家代码');
define('TEXT_POST_CODE', '邮递区号');
define('TEXT_CITY', '城市');
define('TEXT_COUNTRY', '国家');
define('TEXT_STATE', '州/省');
define('TEXT_SECRET_QUESTION', '帐户保护问题');
define('TEXT_ANSWER', '答案');
define('TEXT_SELLING_DELIVERY_TIME', '发货时间');
define('TEXT_OP', 'OP');
define('TEXT_REBATE', '');
define('TEXT_TOTAL_PAY', '应付金额');

define('SECTION_TITLE_FORM_PM_REQUIRED_INFO', '所需资料:');
define('JS_ERROR_EMPTY_SEARCH_INPUT', '请输入搜索.');

define('FORM_REQUIRED_MSG', '我们向您索取信息只是为了让您享受更高档次的服务.');
define('FORM_REQUIRED_INFORMATION', '* 所需资料');

// constants for use in tep_prev_next_display function
define('TEXT_RESULT_PAGE', '结果页面:');
define('TEXT_DISPLAY_NUMBER_OF_PRODUCTS', '展示<b>%d</b> 至 <b>%d</b> (<b>%d</b> 結果)');
define('TEXT_DISPLAY_NUMBER_OF_ORDERS', '展示 <b>%d</b> 至 <b>%d</b> (<b>%d</b> 订单)');
define('TEXT_DISPLAY_NUMBER_OF_REVIEWS', '展示 <b>%d</b> 至 <b>%d</b> (<b>%d</b> 评语)');
define('TEXT_DISPLAY_NUMBER_OF_PRODUCTS_NEW', '展示<b>%d</b> 至 <b>%d</b> (<b>%d</b> 新产品)');
define('TEXT_DISPLAY_NUMBER_OF_SPECIALS', '展示 <b>%d</b> 至 <b>%d</b> (<b>%d</b> 特销)');

define('PREVNEXT_TITLE_FIRST_PAGE', '第一页');
define('PREVNEXT_TITLE_PREVIOUS_PAGE', '前一页');
define('PREVNEXT_TITLE_NEXT_PAGE', '下一页');
define('PREVNEXT_TITLE_LAST_PAGE', '最后一页');
define('PREVNEXT_TITLE_PAGE_NO', '页 %d');
define('PREVNEXT_TITLE_PREV_SET_OF_NO_PAGE', '前一套％ d个页面');
define('PREVNEXT_TITLE_NEXT_SET_OF_NO_PAGE', '下一组％ d个页面');
define('PREVNEXT_BUTTON_FIRST', '首先');
define('PREVNEXT_BUTTON_PREV', '上一页');
define('PREVNEXT_BUTTON_NEXT', '下一页');
define('PREVNEXT_BUTTON_LAST', '上一页');

define('BUTTON_FIRST', '首页');
define('BUTTON_LAST', '最后一页');

define('BUTTON_SUBMIT', '提交');
define('BUTTON_CONFIRM', '确认');
define('BUTTON_CANCEL', '取消');
define('BUTTON_OK', '确认');

define('BUTTON_ADD', '添加');
define('BUTTON_ADD_PM', '添加收款账户');
define('BUTTON_AGREE', '同意');
define('BUTTON_BACK', '返回');

define('BUTTON_YES', '是');
define('BUTTON_BATCH_UPDATE', '集体更新');
define('BUTTON_CANCEL', '取消');
define('BUTTON_CONFIRM', '确定');
define('BUTTON_CONTINUE', '继续');
define('BUTTON_DELETE', '删除');
define('BUTTON_DISAGREE', '不同意');
define('BUTTON_EDIT', '更改');
define('BUTTON_REMOVE', '删除');
define('BUTTON_NO', '否');

define('BUTTON_EXPORT', '输出');
define('BUTTON_EXPORT_TEMPLATE', '输出模板');
define('BUTTON_IMPORT', '输入');
define('BUTTON_PRESALE_NOTICE', '加入开收通知');
define('BUTTON_PRESALE_REMOVE', '删除开收通知');
define('BUTTON_RESET', '重设');
define('BUTTON_REFRESH', '刷新');
define('BUTTON_REPORT', '报告');
define('BUTTON_SAVE_CHANGES', '保存');
define('BUTTON_SEARCH', '搜索');
define('BUTTON_SKIP_NOW', '跳过');
define('BUTTON_SHOW_ALL_PRODUCTS', '显示全部服务器');
define('BUTTON_SHOW_SELLING_PRODUCTS', '显示销售服务器');
define('BUTTON_SIGN_IN', '登入');
define('BUTTON_SIGN_UP', '注册');
define('BUTTON_SIGN_OUT', '登出');
define('BUTTON_RESEND_SECURITY_TOKEN', '重发安全码');

define('ALT_BUTTON_ADD', '添加');
define('ALT_BUTTON_AGREE', '同意');

define('ALT_BUTTON_BATCH_UPDATE', '集体更新');
define('ALT_BUTTON_CANCEL', '取消');
define('ALT_BUTTON_DELETE', '删除');
define('ALT_BUTTON_DISAGREE', '不同意');
define('ALT_BUTTON_EDIT', '更改');

define('ALT_BUTTON_EXPORT', '输出');
define('ALT_BUTTON_IMPORT', '输入');
define('ALT_BUTTON_PRESALE_NOTICE', '加入开收通知');
define('ALT_BUTTON_PRESALE_REMOVE', '删除开收通知');
define('ALT_BUTTON_REFRESH', '刷新');
define('ALT_BUTTON_SIGN_OUT', '登出');

define('BUTTON_MIN_CHAR_LENGTH', 11);
define('IMAGE_BUTTON_ADD_ADDRESS', '添加地址');
define('IMAGE_BUTTON_ADDRESS_BOOK', '通讯录');
define('IMAGE_BUTTON_BACK', '返回');
define('IMAGE_BUTTON_BUY_NOW', '立即购买');
define('IMAGE_BUTTON_CHANGE_ADDRESS', '更改地址');
define('IMAGE_BUTTON_CHECKOUT', '结账');
define('IMAGE_BUTTON_CONFIRM_ORDER', '确认订单');
define('IMAGE_BUTTON_SECURE_CHECKOUT', '安全结账');
define('IMAGE_BUTTON_CONFIRM', '确认订单');
define('IMAGE_BUTTON_CONFIRM_CODE', '确认代码');
define('IMAGE_BUTTON_CONTINUE', '继续');
define('IMAGE_BUTTON_CONTINUE_SHOPPING', '继续购物');
define('IMAGE_BUTTON_CONVERT_NOW', '转换');
define('IMAGE_BUTTON_DELETE', '删除');
define('IMAGE_BUTTON_DOWNLOAD', '下载');
define('IMAGE_BUTTON_EDIT_ACCOUNT', '编辑帐户');
define('IMAGE_BUTTON_HISTORY', '订单历史');
define('IMAGE_BUTTON_IN_CART', '立刻购买');
define('IMAGE_BUTTON_DIRECT_TOP_UP', '立即充值');
define('IMAGE_BUTTON_LOGIN', '登入');
define('IMAGE_BUTTON_SEND_PASSWORD', '发送密码');
define('IMAGE_BUTTON_NOTIFICATIONS', '通知');
define('IMAGE_BUTTON_OUT_OF_STOCK', '售完');
define('IMAGE_BUTTON_PRE_ORDER', '预订');
define('IMAGE_BUTTON_QUICK_FIND', '搜索');
define('IMAGE_BUTTON_REMOVE_NOTIFICATIONS', '删除通知');
define('IMAGE_BUTTON_REVIEWS', '评语');
define('IMAGE_BUTTON_SEARCH', '搜索');
define('IMAGE_BUTTON_SHIPPING_OPTIONS', '运输选项');
define('IMAGE_BUTTON_TELL_A_FRIEND', '推荐朋友');
define('IMAGE_BUTTON_UPDATE', '更新');
define('IMAGE_BUTTON_CONFIRM_TEL', '确认');
define('IMAGE_BUTTON_VERIFY', '验证');
define('IMAGE_BUTTON_UPDATE_CART', '更新购物车');
define('IMAGE_BUTTON_WRITE_REVIEW', '填写审查');
define('IMAGE_BUTTON_REDEEM_VOUCHER', '兑换');
define('IMAGE_BUTTON_SELL_MORE', '出售更多');
define('IMAGE_BUTTON_TRANSFER_NOW', '现在转让');
define('IMAGE_BUTTON_ADD', '添加');
define('IMAGE_BUTTON_NEXT', '下一个');
define('IMAGE_BUTTON_REFRESH', '刷新');
define('IMAGE_BUTTON_YES', '是');
define('IMAGE_BUTTON_YES2', '是的');
define('IMAGE_BUTTON_TOP_UP_STORE_CREDITS', '购买购物代金券');
define('IMAGE_BUTTON_BUY_CODE', '购买代码');
define('IMAGE_BUTTON_PAY_WITH_SC_CURRENCY', '支付 %s (购物代金券)');
define('IMAGE_BUTTON_PAY_WITH_WEBSITE_CURRENCY', '支付 %s');
define('IMAGE_BUTTON_PAY_WITHOUT_SC', '无购物代金券支付');
define('TEXT_INFO_PAYMENT_CONFIRM_CAPTION', '通过%s结账');

define('ALT_BUTTON_ADD_PM', '新增付款账户');
define('ALT_BUTTON_BACK', '返回上一页');
define('ALT_BUTTON_CONFIRM', '确认');
define('ALT_BUTTON_CONTINUE', '继续');
define('ALT_BUTTON_RESET', '重置');
define('ALT_BUTTON_SEARCH', '搜索');
define('ALT_BUTTON_SHOW_ALL_PRODUCTS', '点击这里查看所有服务器.');
define('ALT_BUTTON_SHOW_SELLING_PRODUCTS', '点击这里展示您只出售的服务器.');
define('ALT_BUTTON_SUBMIT', '提交表格');
define('ALT_BUTTON_SIGN_IN', '登入');
define('ALT_BUTTON_SIGN_UP', '注册');
define('ALT_BUTTON_UPDATE', '更新');

define('TABLE_HEADING_SERVER', '服务器');
define('TABLE_HEADING_ACTION', '运作');
define('TABLE_HEADING_PER_UNIT', '单价');
define('TABLE_HEADING_STATUS', '状态');
define('TABLE_HEADING_QTY', '收购量.');
define('TABLE_HEADING_DELIVERY_TIME', '发货时间');
define('TABLE_HEADING_DELIVERY_METHOD_SUPPORT', '选择交易方式');

define('TITLE_TRANS_PAYMENT', '付款 %s');

define('SMALL_IMAGE_BUTTON_DELETE', '删除');
define('SMALL_IMAGE_BUTTON_EDIT', '编辑');
define('SMALL_IMAGE_BUTTON_VIEW', '查看');

define('ICON_ARROW_RIGHT', '更多');
define('ICON_CART', '在购物车');
define('ICON_ERROR', '错误');
define('ICON_SUCCESS', '成功');
define('ICON_WARNING', '警告');
define('ICON_NOTICE', '通知');
define('ICON_PROMO', '促销');

// CATALOG_PRODUCTS_WITH_IMAGES_mod
define('BOX_CATALOG_PRODUCTS_WITH_IMAGES', '打印产品目录');
define('IMAGE_BUTTON_UPSORT', '上升排序');
define('IMAGE_BUTTON_DOWNSORT', '下降排序desending');

// CD Key
define('TEXT_CDKEY_SUSPENDED_FOR_VIEWING', '由于付款纠纷，CD Key和月卡插图暂时封锁. 串行代码已转发给游戏制造商，以便采取适当行动. 请联系************************如果有错误.');

// Down For Maintenance
define('TEXT_BEFORE_DOWN_FOR_MAINTENANCE', '公告：本网站将在%s关闭进行%s的维护.');
define('TEXT_ADMIN_DOWN_FOR_MAINTENANCE', '通知：网站目前进行维修');


define('TEXT_GREETING_PERSONAL', '欢迎回来, <span class="greetUser">%s</span>! 你是否想看看哪 <a href="%s">新产品</a> 可购买?');
define('TEXT_GREETING_PERSONAL_RELOGON', '<small>如果您不是％ s，请利用您的帐户资料<a href="%s">登入</a>.</small>');
define('TEXT_GREETING_GUEST', '欢迎 <span class="greetUser">顾客</span>! 你是否想 <a href="%s">登入</a>? 或<a href="%s">注册一个帐户</a>?');
define('TEXT_YOU_ARE_HERE', '<b>您在这里:</b>');
define('TEXT_HOME', '首页');
define('TEXT_GAME', '游戏');
define('TEXT_SERVER', '服务器');
define('TEXT_NORMAL', '正常');
define('TEXT_GENERAL', '原价');
define('TEXT_FULL', '已满');
define('TEXT_QUANTITY', '数量');
define('TEXT_VIEW', '查看');

define('TEXT_ENTER', '输入');
define('TEXT_LIST_CLOSE', '关闭');
define('TEXT_IS_LOADING', '载入中...请稍候.');
define('TEXT_CONFIRM_DELETE', '您确定删除?');

//Common fields
define('TEXT_AMOUNT', '总额'); //$$$
define('TEXT_AMOUNT_WITH_CURRENCY', '总额'); //$$$
define('TEXT_STATUS', '状态');
define('TEXT_ACTION', '运作');
define('TEXT_ORDER_NO', '单子 #');
define('TEXT_ORDER_STATUS', '单子状态');
define('TEXT_PAYMENT_STATUS', '款项状态');
define('TEXT_START_DATE', '起始日期');
define('TEXT_END_DATE', '终结日期');
define('TEXT_LOADING_MESSAGE', '正在加载...');
define('TEXT_WEBSITE_TIPS', '免责声明');
define('TEXT_REFRESH', '刷新');
define('TEXT_REDEEM', '兑换');
define('TEXT_NO_RESULTS', '无相符显示');

define('TEXT_CHARACTER_NAME', '角色名: %s');
define('TEXT_CHAR_NAME', '角色名');
define('TEXT_CHARACTER_INGAME_TIME', '距离登陆时间: %s<br>结账后');
define('TEXT_CHARACTER_INGAME_DURATION', '游戏待续时间: %d 小时');
define('TEXT_CHARACTER_ACCOUNT_NAME', '帐户名称: %s');
define('TEXT_CHARACTER_ACCOUNT_PASSWORD', '密码: %s');
define('TEXT_CHARACTER_ACCOUNT_WOW', '魔兽帐号名: %s');
define('GAME_CURRENCY_TEMPLATE', '游戏货币商铺');
define('CD_KEY_TEMPLATE', '游戏点卡商铺');
define('PWL_TEMPLATE', '游戏代练商铺');
define('HIGH_LEVEL_ACCOUNT_TEMPLATE', '游戏帐号商铺');

define('URL_GAME_CURRENCY_TEMPLATE', 'game_currency_store');
define('URL_CD_KEY_TEMPLATE', 'game_card_store');
define('URL_PWL_TEMPLATE', 'power_leveling_store');
define('URL_HIGH_LEVEL_ACCOUNT_TEMPLATE', 'high_level_account_store');

define('GAME_CURRENCY_TEXT', '在游戏中的货币用于购买任何物品或技能..');
define('CD_KEY_TEXT', '游戏点卡在线充值...');
define('PWL_TEXT', '用于PVP服务器的游戏技能...');

define('LINK_GAME_CURRENCY', '游戏货币请按此.');
define('LINK_CD_KEY', '游戏点卡请按此.');
define('LINK_PWL', '游戏代练请按此.');

define('SHARE_THIS_TITLE', '分享收藏');
define('LINK_SHARE_THIS_PAGE', 'Share this page');
define('LINK_SHARE_THIS_PRODUCT', 'Share this product');
define('LINK_REDIRECT_MSG', '如果您的浏览器没有自动转到下一页面，请点击这里.');
define('LINK_MORE_PRODUCT_INFO', '更多产品信息');
define('LINK_HIDE_PRODUCT_INFO', '隐藏产品信息');
define('LINK_ALL_LANGUAGE', '所有语言');
define('LINK_ALL_PRODUCT_TYPE', '所有产品类型');
define('LINK_ALL_PLATFORM', '所有游戏平台');
define('LINK_ALL_GENRE', '所有游戏类别');

define('TEXT_SORT_PRODUCTS', '分类产品 ');
define('TEXT_DESCENDINGLY', '下降');
define('TEXT_ASCENDINGLY', '上升');
define('TEXT_BY', ' 于');
define('TEXT_NOT_AVAILABLE', 'N/A');

define('TEXT_REVIEW_BY', '于 %s');
define('TEXT_REVIEW_WORD_COUNT', '%s 字');
define('TEXT_REVIEW_RATING', '评级: %s [%s]');
define('TEXT_REVIEW_DATE_ADDED', '加入日期: %s');
define('TEXT_NO_REVIEWS', '目前没有任何产品评论.');

define('TEXT_CLICK_TO_VERIFY', '点击这里验证');
define('TEXT_EMAIL_NOT_VERIFY', '未验证');
define('TEXT_EMAIL_VERIFIED', '(验证) <span class="requiredInfo">*</span>');

define('TEXT_UNKNOWN_TAX_RATE', '未知税率');
define('TEXT_PRODUCT_NOT_FOUND', '此产品已下架.');
define('TEXT_PRODUCT_NOT_FOUND_OPTIONS', '<div class="mediumFont"><b>请尝试以下选项：</b></div>
												<div>
													<ul style="font-weight:normal;font-size:12px;list-style-type:square;padding-left:25px;margin-top:5px;">
														<li><a href="%s">浏览商铺内</a>的其他产品和服务</li>
														<li>进入<a href="%s">OffGamers首页</a></li>
													</ul>
												</div>');
define('TEXT_FIELD_REQUIRED', '&nbsp;<span class="requiredInfo">* 必填</span>');
define('TEXT_INSTANT_DELIVERY', '存货');
define('TEXT_INSTANT', 'Instant');
define('TEXT_MEMORY_USAGE', 'Memory Usage');
define('TITLE_TRANS_BUYBACK_ORDER', '采购订单 %s');
define('TITLE_BUYBACK_PAYMENT_REPORT', '&#20184;&#27454;&#25253;&#21578;');

define('ERROR_TEP_MAIL', '<font face="Verdana, Arial" size="2" color="#ff0000"><b><small>TEP ERROR:</small> 不能通过指定的SMTP服务器发送电子邮件. 请检查您的php.ini设置, 如有必要, 请改正的SMTP服务器.</b></font>');
define('ERROR_PAYMENT_CURRENCY_NOT_SUPPORTED', '错误：您所选择的支付网关, %s, 只支持 %s. 请选择不同的货币或请选择不同的支付网关.');
define('WARNING_INSTALL_DIRECTORY_EXISTS', '警告：安装目录存在于: ' . dirname($HTTP_SERVER_VARS['SCRIPT_FILENAME']) . '/install. 为了安全起见, 请删除这个目录.');
define('WARNING_CONFIG_FILE_WRITEABLE', '警告：我可以写此配置文件: ' . dirname($HTTP_SERVER_VARS['SCRIPT_FILENAME']) . '/includes/configure.php. 这是一个安全风险-请对这个档案设定用户权限.');
define('WARNING_SESSION_DIRECTORY_NON_EXISTENT', '警告：会议的目录不存在: ' . tep_session_save_path() . '. 直到创建此目录, 会议将不会操作.');
define('WARNING_SESSION_DIRECTORY_NOT_WRITEABLE', '警告：我不能够写入会议的目录: ' . tep_session_save_path() . '. 直到正确的用户权限设置，会议将不会操作.');
define('WARNING_SESSION_AUTO_START', '警告： session.auto_start启用-请在php.ini中禁用此PHP功能并重新启动Web伺服器.');
define('WARNING_DOWNLOAD_DIRECTORY_NON_EXISTENT', '警告：可下载的产品目录不存在: ' . DIR_FS_DOWNLOAD . '. 直到这个目录是有效的, 可下载的产品将不会操作.');
define('WARNING_PRODUCTS_LOW_QUANTITY', '该订单的限定已超过了. ');
define('WARNING_PRODUCTS_SUGGESTED_QUANTITY', '建议的数量是％d.');
define('WARNING_PRODUCTS_OVERWEIGHT', '我们很抱歉！您的总重量为这项产品已超过最大重量!');
define('WARNING_PRODUCTS_UNDERWEIGHT', '我们很抱歉！您的产品总重量超过了限定的重量!');
define('WARNING_EMPTY_SELECTIONS', '至少要选择一个!');
define('WARNING_ORDER_AMOUNT_ZERO', '结账失败！您的购物车产品没有价格.');
define('WARNING_MUST_BE_VIP_MEMBER', '警告：只有VIP订单才具有此功能');
define('SUCCESS_DYNAMIC_CART_ADDED', '此产品已成功添加到购物车! <a href="%s">点击这里查看购物车.</a>');
define('SUCCESS_DYNAMIC_CART_UPDATED', '此产品的购物车已成功更新! <a href="%s">点击这里查看购物车.</a>');

define('ERROR_NO_UPLOAD_FILE', '错误：上载文件不存在。');
define('ERROR_FILESIZE_EXCEED', '错误：文件大小超过限制。');
define('ERROR_UPLOAD_PARTIAL', '错误：文件只有部分被上传。');
define('ERROR_NO_TMP_DIR', '错误：找不到临时文件夹。');
define('ERROR_DESTINATION_DOES_NOT_EXIST', '错误：上载目录不存在。');
define('ERROR_DESTINATION_NOT_WRITEABLE', '错误：上载目录不可写。');
define('ERROR_FILE_NOT_SAVED', '错误：上载文件未存盘。');
define('ERROR_FILETYPE_NOT_ALLOWED', '错误：文件类型错误。');
define('SUCCESS_FILE_SAVED_SUCCESSFULLY', '成功：上载文件已存盘。');
define('WARNING_NO_FILE_UPLOADED', '警告：未上传任何文件。');
define('WARNING_FILE_UPLOADS_DISABLED', '警告：在 php.ini 设置文件中，文件上载功能已被解除。');
define('ERROR_PAGE_ACCESS_DENIED', '错误：您无权限访问该页面。');

define('TEXT_CCVAL_ERROR_INVALID_DATE', '所输入的信用卡截止日期错误.<br>请检查日期，然后再试一次.');
define('TEXT_CCVAL_ERROR_INVALID_NUMBER', '所输入的信用卡号码错误.<br>请检查该号码并再试一次.');
define('TEXT_CCVAL_ERROR_UNKNOWN_CARD', '所输入的信用卡首4个号码: %s<br>如这数字是错的，我们不能接受这型的信用卡.<br>如有错误，请再试一次.');

define('TEXT_ERROR_ACTIVATE_ACCOUNT', '以便结账，请启动您的帐户.');
define('TEXT_STOCK_NOT_AVAILABLE', '您要求购买的产品数量不足。如果要大宗购物，请联系 <a href="mailto:<EMAIL>"><EMAIL></a>');
define('TEXT_CUSTOMER_VERIFIED_PAYMENT_EMAIL_NOTICE_SUBJECT', '客户付款电邮地址验证通知');
define('TEXT_CUSTOMER_NAME', '客户名称: ');
define('TEXT_CUSTOMER_PAYMENT_EMAIL', '付款电邮地址: ');
define('TEXT_NEWSLETTER_MSG', '<div style=\"font-family: Arial, Verdana, sans-serif; font-weight: bold; font-size: 12px; color: green;\">请注：</div>" . "<span class=\"latestNewsBoxContents\">发给您的电子邮件将从@offgamers.com --- 如果您使用的反垃圾邮件过滤器，请务必添加此域到您的"优良者名单"或"住址名册". <a href="http://www.offgamers.com/user-guide-whitelist-emails-i-491.ogm" target="_blank">要学习如何， 请按此</a>.</span>');

define('EMAIL_TEXT_AFFILIATE_NOTIFICATION_SUBJECT', '合作联盟销售 #%d (订单总额: %s)');
define('EMAIL_TEXT_AFFILIATE_NOTIFICATION_INFO', '以下是由您推介的销售:' . "\n\n" . '订单日期: %s' . "\n" . '订单ID: %s' . "\n" . '订单总额: %s' . "\n");

define('LOG_SALES_ORDER', '##%d##: ##%d## ##%d##');

//define('LOG_SC_STAT_SC_CONVERT', '赊账金额转换');
define('LOG_SC_STAT_SC_CONVERT', 'Store Credit Currency Conversion');
define('LOG_SC_ACTIVITY_TYPE_PURCHASE', 'P');
define('LOG_SC_ACTIVITY_TYPE_CANCEL', 'X');
define('LOG_SC_ACTIVITY_TYPE_CONVERT', 'V');

//付款弹出菜单
define('POP_PAYMENT_REPORT_PAYMENT_MESSAGE', '付款留言');
define('POP_PAYMENT_REPORT_DATETIME', '日期/时间');

// 定义为交易状况更新通知
define('EMAIL_CUSTOMER_ORDER_UPDATE_NOTIFICATION_SUBJECT', '客户订单更新通知 #%d');
define('EMAIL_TRANS_UPDATE_NOTIFICATION_CONTENT', '订单 ID: %s' . "\n" . '订单日期: %s' . "\n" . '订单总额: %s' . "\n" . '付款方式: %s' . "\n\n" . '更新类型: %s' . "\n" . '状态更新: %s -> %s' . "\n" . '更新日期: %s' . "\n" . '更新IP: %s' . "\n" . '更新用户: %s' . "\n\n" . '更新评论:' . "\n" . '%s');
define('EMAIL_TEXT_TRANS_UPDATE_MANUAL_NOTIFICATION', '手动');
define('EMAIL_TEXT_TRANS_UPDATE_AUTO_NOTIFICATION', '自动');

// 投票站
define('BUTTON_VIEW_RESULT', '结果');
define('BUTTON_VOTE', '投票');

// 电子邮件验证
define('EMAIL_SUBJECT_2', '电子邮件地址验证');
define('EMAIL_VERIFY_CONTENT', '您在最近使用了此电邮地址注册 ' . STORE_OWNER . '。 请点击下面的链接， 完成您的注册:' . "\n");
define('EMAIL_VERIFY_CONTENT_ADDRESS_INFO', '(如果点击链接不能操作，请复制并粘贴到您的浏览器.)' . "\n\n");
define('EMAIL_MANUAL_ACTIVATE_EMAIL_2', '您也可以在<a href="' . tep_href_link(FILENAME_ACCOUNT_ACTIVATE, 'verify=email&auto_mail=yes', 'SSL') . '">' . tep_href_link(FILENAME_ACCOUNT_ACTIVATE, 'verify=email&auto_mail=yes', 'SSL') . '</a>，输入以下的资料， 手动验证您的电子邮件地址 .' . "\n\n" . '电子邮件地址: ');
define('EMAIL_MANUAL_ACTIVATE_CODE_2', "\n" . '验证码: ');
define('EMAIL_CONTACT', '如有任何的询问，请使用我们的在线客服，或通过电子邮件联络我们 ' . EMAIL_TO . '. 谢谢您在' . STORE_NAME . "购物.\n\n\n");
define('EMAIL_FOOTER', STORE_EMAIL_SIGNATURE);

define('TEXT_FRAUD_DISCLAIMER', '<br>
		<table style="border-right: 2px dashed #ff0000; padding-right: 10px; border-top: 2px dashed #ff0000; padding-left: 10px; padding-bottom: 15px; margin: 0px 0px 15px; border-left: 2px dashed #ff0000; padding-top: 10px; border-bottom: 2px dashed #ff0000; background-color: #fffef2;">
        	<tr>
				<td>
					<div style="font-family: Arial, Verdana, sans-serif; font-weight: 700; font-size: 16px; color: #ff0000; border-bottom: 1px dashed #ff0000; text-align: left">Attn : Fraudsters and scammers</div>
					<BR><span class="latestNewsBoxContents">
					预告，信用卡欺诈是一项联邦罪行，一律由 <a href="http://www.fbi.gov/cyberinvest/cyberhome.htm" target="_blank">FBI</a> (FEDERAL BUREAU OF INVESTIGATION), <a href="http://www.secretservice.gov/financial_crimes.shtml" target="_blank">美国秘密服务</a> 和它们的合作伙伴包括 <a href="http://www.interpol.int/Public/FinancialCrime/default.asp" target="_blank">INTERPOL
					国际</a>举办调查工作. 法律要求我们报告这种侵权行为，而我们非常乐意的遵守这一任务. <br>
					<br>
					offgamers将提供所有的资料，包括* IP地址， MAC地址，注册的VoIP电话号码，反向查找资料，记录的谈话， *汇编欺诈的命令，以官员和地方当局采取行动追求调查和起诉犯罪者。一旦刑事案件已完成，一个对违约方的民事案将被提交，以收回OffGamers所受到的任何及所有损害（赔偿财务或其他方面）. <br>
					<br>
					请注意， offgamers察觉与 <a href="http://www.ic3.gov/" target="_blank">报告了</a> 许多企图欺骗，诈骗，欺诈，窃取身份，侵入和非法进入我们的服务器的事件.  这警告是让大家知道我们非常重视我们订货系统的安全，任何企图以欺诈手段的地方，命令是否由假冒，伪造，篡改，虚假声称，冒用信用卡，或有关的活动，在他涉嫌与身份证明文件和商业欺诈，虚假文书将立即被报告。
					<br>
					<br>
					美国秘密服务是首要的联邦机构，专责调查欺诈的行为及其有关活动（标题18 ，美国法典第1029） 。不要被蒙蔽，认为在背后利用欺骗性IP地址和VoIP电话线是安全的. <a href="http://www.fbi.gov/contact/legat/legat.htm" target="_blank">FBI</a>, <a href="http://www.secretservice.gov/field_offices.shtml#over" target="_blank">美国秘密服务</a>, 与 <a href="http://www.interpol.int/Public/ICPO/Members/default.asp" target="_blank">INTERPOL</a>;
					在世界各地操作，并会与 <a href="http://www.fbi.gov/page2/march06/cats030606.htm" target="_blank">本地的官员</a> 合作确保犯罪者被逮捕 。从offgamers窃取， 10年坐牢，是不值得。
					</span>
				</td>
			</tr>
		</table>');

define('TEXT_GOLD_DELIVERY_NOTES', '');

define('TEXT_PAYMENT_VERIFICATION_NOTES', '');

define('TEXT_PAYMENT_GATEWAY', '支付网关 ');
define('TEXT_ITEMS_IN_MY_CART', '<a href="%s" style="float:none;padding:0;">%s</a> 购物车里的产品');
define('TEXT_ITEM_IN_MY_CART', '购物车里的产品');
define('TEXT_ITEM_IN_MY_CART_FOR_FOOTER', '购物车 (%s)');
define('TEXT_TYPE_TO_FILTER_PRODUCTS', '过滤型产品 ');
define('TEXT_SEARCH_WHOLE_STORE', '搜索整个商店 ');
define('TEXT_SUBSCRIBE_NEWSLETTER', '现在订阅');
define('TEXT_VIEW_ALL_TESTIMONIALS', '查看全部客户证言');
define('TEXT_GAME_NOT_SUPPORT', '您的国家不支持这游戏');
define('TEXT_GAME_NOT_SUPPORT_DESC', '由于游戏出版商的限定， 您所选泽的国家被禁止玩这游戏');
define('TEXT_BACK_TO_TOP', ' 回到顶部');
define('TEXT_VIEW_MY_CART', '查看购物车');
define('TEXT_SWITCH_TO_MOBILE', 'Click here switch to mobile version');
define('TEXT_COPYRIGHT_INFORMATION', '版权所有 &copy;' . date('Y') . ' OffGamers Global Pte Ltd。保留所有权利。 &nbsp&nbsp <a href="' . tep_href_link('/terms-of-service-cn-simp-i-630.ogm') . '">服务条款</a> &nbsp|&nbsp <a href="' . tep_href_link('privacy-policy-cn-simp-i-628.ogm') . '">隐私权政策</a>');

define('TAB_LATEST_UPDATE', 'LATEST UPDATE :');

// New Layout
define('TEXT_MUST_LOGIN', '需要登入才被允许');

define('TEXT_CURRENCY', '货币');
define('TOOLTIPS_MEMBER_ID', 'OffGamers ID');
define('TOOLTIPS_MEMBER_STATUS', '客户等级');
define('TOOLTIPS_STORE_CREDIT_BALANCE', '购物代金券余额');
define('TOOLTIPS_OFFGAMERS_POINTS', 'OP是您购物完成后退给您的奖励积分，可以用于兑换代金券。<br/><br/><a href=\'http://kb.offgamers.com/zhcn/category/my-account/wor-token\' target=\'_blank\'>更多详情</a>');
define('TOOLTIPS_WITHDRAWABLE_BALANCE', '货款余额');

define('TEXT_LOGIN', '&nbsp;登录');
define('TEXT_LOGOUT', '&nbsp;登出');
define('TEXT_TRACK_BUYBACK_ORDER', '&nbsp;购买订单追踪');
define('TEXT_TRACK_SELLING_ORDER', '&nbsp;出售订单追踪');
define('TEXT_REGISTER', '&nbsp;注册帐号');
define('TEXT_GREETING_GUEST_NEW', '<font style="color:#92CFF3;">您尚未登录, 请<a id="fancy_login_box" href="javascript:void(0);" class="whiteText"><b>登录</b></a>或 <a href="' . tep_href_link(FILENAME_LOGIN, '', 'SSL') . '" class="whiteText"><b>注册帐号</b></a>.</font>');
define('TEXT_OGM_SERVICE_GUARANTEE', '<font style="font-weight:bold;font-size:12px;">OGM 服务保证</font><br><font class="smallestFont">虚码/点卡</font><br><br><b>保证退款</b><br>如您对于代练过程有所不满 <br><br><b>户口安全</b><br>如您有所不满');
define('TEXT_JOIN_AFFILIATE_PROGRAM', '即刻加入共享利润');
define('TEXT_VIEW_PAYMENT_GATEWAY', '查看更多付款方式');
define('TEXT_SERVICE_GURANTEED_ICON', 'icon_serviceguarantee_cn.gif');
define('TEXT_MORE_DETAILS', 'More Details');
define('TEXT_COOKIE_NOTICE_DESC', 'OffGamers 使用 cookie 来优化我们网站上您的在线体验。通过继续使用我们的网站满足您的游戏需求，您同意使用这样的 cookie。');

define('BUTTON_PROCEED_TO_CHECKOUT', '&nbsp;进行结账');
define('BUTTON_TOP_GAME_SELECTION', '热销游戏选择');
define('BUTTON_VIEW_ALL_GAMES', '全部游戏');
define('BUTTON_MORE_GAMES', '更多游戏');
define('HEADER_PRODUCT_SELECTION', '产品选择');

define('HEADER_GAMES_SELECTION', '游戏');
define('HEADER_CUSTOMER_TESTIMONIAL', '客户证言');
define('HEADER_CONFIRM_BILLING_INFO', '请确认/完成您的帐单信息');

define('HEADER_NEWSLETTER', '电子月刊');
define('HEADER_SERVICES', '额外服务');
define('HEADER_EXPANDED_INFORMATION', '附加客户说明：');
define('BOX_HEADER_AVAILABLE_PRODUCT', '产品系列');

define('BOX_EVENT_AND_PROMOTION', '促销活动');

define('BOX_GAME_GUIDES', '游戏指南');
define('BOX_HEADING_ITEM_ADDED_TO_SHOPPING_CART', '产品已添加到购物车');
define('BOX_STORE_CREDIT', '购物代金券');

define('HTML_AFFILIATE_BOX', '<div style="width:100%;text-align:left;"><b><span style="font-size:16px;">高额佣金诱惑，简单注册步骤</span></b></div>');
define('HTML_CUSTOMER_TESTIMONIAL_BOX', "<div id='scrollup' onclick='document.location.href=\"/customer-testimonials-i-621.ogm\"'>
											 <div class='headline'>Superb service and support as usual. No, even better this time. You even called me to check if i had recived the gold. Great discreetion as usual!<br>I just wanna thank you again..</div>
	                                         <div class='headline'>I just wanted to say that I was pleased with your service. The face-to-face delivery was sooner than I anticipated, I actually was AFK because I didn't think it would come so soon! Thanks for making ..</div>
	                                         <div class='headline'>IS EXCELENT!!!!! YOU GAIN A CLIENT FOR EVER!!!!<br>THX !</div>
	                                         <div class='headline'>Hey, I just wanted to say thank you for providing great service, especially with your representatives for dealing with me at 3 in the morning, being very incoherent. I doubt this is the place to send it..</div>
	                                         <div class='headline'>Thanks for the fast, great delivery for first time buyer. Support and Delivery were excellent and prompt.<br><b>Andryo (11-Oct-08)</b></div>
	                                         <div class='headline'>Hi Again! :)<br>Just wanted to say thanks again for another wonderful purchase. Thanks for making my gaming sessions fun again.<br><b>John W. (02-Oct-08)</b></div>
	                                         <div class='headline'>Hi there,<br>I've not actually bought any gold from you (yet) but I'd like to say this has to be THE best game currency / leveling website around. The page layouts..<br><b>Andy M. (17-Sep-08)</b></div>
	                                         <div class='headline'>HELLO!<br>I M VERY PLEASED WITH YOUR FAST SERVICE AND SUPPORT!!!! AND FAST CONTACT ON THE PHONE. WITH PLEASURE AGAIN!!!<br><b>B.G. (04-Sep-08</b></div>
	                                         <div class='headline'>Just saying thanks for the great service. Was a lot faster then I expected!<br><b>J.B. (02-Sep-08)</b></div>
	                                         <div class='headline'>I don't think I've ever had this level of service before, it's both great and just amazing. I can't even begin to tell you all the praise that I have for your men and women of the company.<br><b>F.C. (01-Jun-08)</b></div>
	                                         <div class='headline'>Great service, in 24hrs got multiple levels over 60. Also gained good amount of gold. Website is slightly slow at loading from page to page, but service is awesome.<br><b>Brian (18-May-08)</b></div>
	                                         <div class='headline'>You guys are awesome! The flexi-hours leveling takes the edge off of the otherwise completely monotonous grind. I'm excited every time I log back in after your levelers work..<br><b>Sean C. (17-May-08)</b></div>
                                             <div class='headline'>Great service!!!! Will definately be back again soon. Keep up the great work and thank you.<br><b>J. C.(25-Mar-08)</b></div>
                                             <div class='headline'>Company is brilliant, even woke me up so I could collect what I ordered. Thanks to Saran and Vignes for their help and guidance on areas of the matter I did not know. 10 out of 10<br><b>A. Cook.(22-Mar-08)</b></div>
                                             <div class='headline'>Ive bought alot of gold from you guys on bloodscalp now, always proccessed and delivered very fast! I also bought WoW and burning crusade from you guys.<br><b>K.W.(20-Mar-08)</b></div>
                                             <div class='headline'>I just wanted to say Thank You, you made my experience quick, easy, and pleasant. Great job keep up the good work. I will happily do business with you again.<br><b>N.B.(19-Mar-08)</b></div>
                                             <div class='headline'>Just wanted to let you guys know that the service is beyond exceptional. =)<br><b>A.E.(05-Mar-2008)</b></div>
                                             <div class='headline'>I am very pleased with the service that you guys have provided. I am very happy to find a excellent service and I will not go to any other. Keep up the good job!<br><b>M.D.(02-Mar-2008)</b></div>
                                             <div class='headline'>I just want to thank you for comleting my order in such a short time, it is nice to see that after the new year celebration, you are back at full force. Marks: 20+ out of 10 ( more than excellent )</div>
                                             <div class='headline'>Thanks and good luck in the futer :)<br><b>M.M.(27-Feb-2008)</b></div>
                                             <div class='headline'>Saran, one of your live chat representatives, was very helpful and informative in helping me with my issues.<br><b>Satisfied Customer(24-Feb-2008)</b></div>
                                             <div class='headline'>I just wanted to say that i ordered from you twice and so far everything has been fantastic though.. the live support is a little bland..<br><b>J.M(21-Feb-2008)</b></div>
                                             <div class='headline'>I wanted to thank you for the speedy process of my order for the CD-Key for WOW Burning Crusade EU. It arrived within minutes of verification and worked like a charm! <br><b>D.F.(11-Feb-08)</b></div>
                                             <div class='headline'>I was completely satisfied the way User Azhan responded and took care of my order issues. I just thought your supervisors should be aware of this person.<br><b>R.N(5-Feb-08)</b></div>
                                             <div class='headline'>You've got the best service folks working there, thanks.<br><b>Y.N.(3-Feb-08)</b></div>
                                             <div class='headline'>I just wanted to let you know that Azhan was wonderful. He stayed with me and continued to try and find out what was wrong with my order until the matter was resolved.<br><b>T.G.(2-Feb-08)</b></div>
                                             <div class='headline'>I wanted to let you know that your service is amazing and I apperciate it very much, azhan answered my questions 110% to his abilitys, and I wanted to leave a positive feedback.<br><b>S.A (29-Jan-08)</b></div>
                                             <div class='headline'>Would just like to say, you guys are fantastic, ordered my wow key, and it was delivered within 45 secs. Breathtaking service, thank you.<br><b>Karl (19-Jan-08)</b></div>
                                             <div class='headline'>I just purchased the WoW BatlleChest. I recieved it within 5 min. very fast delivery. You guys are awesome.<br><b>B.M (25-Dec-07)</b></div>
                                             <div class='headline'>just received the final installment of my 5000 gold purchase and all I can say is you are the BEST supplier I've found.<br><b>LMS (24-Nov-07)</b></div>
                                             <div class='headline'>Just thought I'd send an email to let you all know what great service you've been giving me. Absolutely no problems and very fast and efficient service, I will be doing business with you.<br><b>Shaun. (26-Sep-07)</b></div>
                                             <div class='headline'>Awesome service!! I just bought a 60 day pre-paid WOW Gamecard (EU) and i got the code delivered within 10 minutes. I will definitively buy more from you later :)<br><b>V.F. (23-Sep-07)</b></div>
                                             <div class='headline'>id like to say Ram. D is doing a very good job and i bet you guys do it to not the first time i use offgamers not the last i love the serivce here and i can rely on you guys..<br><b>M.D. (02-Sep-07)</b></div>
                                             <div class='headline'>I am writing you only for tell THANKS! Your service was awesome. I'll recommend you to all my friends!<br><b>R.A. (20-Jan-07)</b></div>
                                             <div class='headline'>Just wanted to say thanks, I've always ordered power leveling for my characters from you guys and you do a great job. Also I am really pleased with how you are on the top.<br><b>D.S. (15-Dec-06)</b></div>
                                             <div class='headline'>Delivery was made at the time promised, even with notification of shortage possibly affecting delivery. Thank you for aiding in making my gaming experience awesome!<br><b>M. T. (13-Oct-06)</b></div>
                                             <div class='headline'>Thanx alot and yes and ur so so so so good<br><b>H. A. (13-Oct-06)</b></div>
                                             <div class='headline'>due to fast delivery, easy-to-access site, i could find anything i needed really easy. The live chat is really nice, with people answering almost immediately.<br><b>E. H. (11-Oct-06)</b></div>
                                             <div class='headline'>Thank you for your services and distinctive the unlimited support.<br><b>S. (06-Oct-06)</b></div>
                                             <div class='headline'>Hello, I just wanted to say that OffGamers deliver perfect service and it is allways fast and simple. Keep up the good work and thank you once again for this perfect service.<br><b>F. H. (22-Jul-06)</b></div>
                                             <div class='headline'>Hi Guys,Just thought I'd send an email to let you all know what great service you've been giving me. Absolutely no problems and very fast and efficient service.<br><b>S.O. (13-Jul-06)</b></div>
                                             <div class='headline'>I have been a customer at OffGamers for quite some time now. Never have I been assigned a more helpful agent than Saran. Saran re-assured me OffGamers is the best.<br><b>K.M. (29-Apr-06)</b></div>
                                             <div class='headline'>I have been buying gold for years from IGE and other sites.. Only to get the run-around when it came to deliveries and customer service. You guys is the most professional..<br><b>C. R. (29-Apr-06)</b></div>
                                             <div class='headline'>I am very impressed with the quality of your workmanship and the speed in which you completed the power level. Thank you for your professionalism.<br><b>D. D. (21-Apr-06)</b></div>
                                             <div class='headline'>Ever since I finally found the profiler on your site I've been checking my character and bags a bunch and I gotta say; Thanks for not vendoring/trashing..<br><b>S. R. (19-Apr-06)</b></div>
                                             <div class='headline'>Thank you once again for an amazing service and quick one at that. You guys are a great team thank you very much.<br><b>S. (09-Mar-06)</b></div>
                                             <div class='headline'>Awesome job on the power leveling guys, I logged the toon on to take a quick look and she was already lvl 32... and in half the time you quoted at that. Again, great work!<br><b>J. F. (27-Feb-06)</b></div>
                                             <div class='headline'>I dont know what to sayi'm very happy about CD key and its proive for me that customers is number one in Off Gamers and i will deal with you again and again<br><b>R. (29-Dec-05)</b></div>
											 </div>");

define('HTML_NEWSLETTER_BOX', ' 订阅我们的月刊会给予优惠，独家促销活动!');
define('HEADER_DELIVERY_INFORMATION', '发货信息');
define('HEADER_CHANGE_MOBILE', '更换手机号码');
define('HEADER_STORE_CREDIT_USE', '代金券购货');
define('TEXT_STORE_CREDIT_USE', '<p>如果您要使用代金券购货金额为您当前订单付款，您需要检查您购买的产品的币种是否与您的代金券购货金额的币种相符。</p><p>您想改将购物车内产品的币种转变为代金券购货金额的币种？</p>');
define('TEXT_STORE_CREDIT_CONVERT', '<p>In order to use the store credits for your current order, you need to convert your store credits to the currency that matches that of your current order.</p><p>Current store credits balance: %s<br/>After conversion: %s<br/>Conversion rate: %s (updated daily)<p>Would you like to change the currency of your current store credit balance to match that of your current order?</p>');
define('TEXT_STORE_CREDIT_CONVERT_IN_MY_ACCOUNT', '<p>如果您要使用代金券购货金额为您当前订单付款，您需要检查您购买的产品的币种是否与您的代金券购货金额的币种相符。</p><p>您想改将代金券购货金额的币种转变为购物车内产品的币种？</p>');
define('TEXT_STORE_CREDIT_POPUP_DESCRIPTION', '您的购物代金券的币种与您事先选择的网站币种不同。<b>请按照以下任一方法进行修改：-</b>');
define('TEXT_STORE_CREDIT_POPUP_CHANGE_WEBSITE_CURRENCY', '将网站币种换成与购物代金券相同的币种(%s)');
define('TEXT_STORE_CREDIT_POPUP_CHANGE_STORE_CREDIT_CURRENCY', '将购物代金券币种换成与网站相同的币种(%s)');
define('TEXT_STORE_CREDIT_POPUP_CONTINUE_WITH_NO_CHANGE', '不使用购物代金券支付本次交易。');
define('TEXT_LOGIN_PASSWORD', '登录密码');

define('LINK_CHANGE_DELIVERY_INFO', '更改发货信息');
define('TEXT_TRADE_MODE', '交易方式: %s');
define('TEXT_TRADE_METHOD', '交易方式: <b>%s</b>');
define('OPTION_FACE_TO_FACE', '面对面交易');
define('OPTION_PUT_IN_MY_ACCOUNT', '放入我的帐户');
define('OPTION_BY_MAIL', '邮寄');
define('OPTION_OTHERS', '其他方式');
define('OPTION_OPEN_STORE', '摆摊');

define('BUYBACK_SUPPLIER_MODE_F2F', '面对面交易');
define('BUYBACK_SUPPLIER_MODE_MAIL', '邮寄');
define('BUYBACK_SUPPLIER_MODE_PIMA', '放入我的帐户');
define('BUYBACK_SUPPLIER_MODE_T2O', '与网站交易');
define('BUYBACK_SUPPLIER_MODE_T2C', '与客户交易');
define('BUYBACK_SUPPLIER_MODE_OPEN_STORE', '摆摊');

define('HEADER_TOTAL', '总计: ');
define('TEXT_NUMERIC', '数字标识号');

define('BREADCRUMB_BUY', '买');
define('BREADCRUMB_SELL', '卖');

define('TEXT_PROMOTION_STATUS', '当前状态');
define('TEXT_PROMOTION_PRODUCTS', '促销产品');
define('TEXT_RETYPE', '重新输入');
define('TEXT_SOFTPIN', '虚拟代码');
define('TEXT_STATUS_LIMITED_STOCK', '库存有限');
define('TEXT_STATUS_FAST_SELLING', '热销中');
define('TEXT_STATUS_PRICE_SLASH', '减价');

// Locking / Unlocking log tag
if (!defined('ORDERS_LOG_LOCK_ORDER'))
    define('ORDERS_LOG_LOCK_ORDER', '##l_1##');
if (!defined('ORDERS_LOG_UNLOCK_OWN_ORDER'))
    define('ORDERS_LOG_UNLOCK_OWN_ORDER', '##ul_1##');
if (!defined('ORDERS_LOG_UNLOCK_OTHER_PEOPLE_ORDER'))
    define('ORDERS_LOG_UNLOCK_OTHER_PEOPLE_ORDER', '##ul_2##%s:~:%s');

define('LOG_PARTIAL_DELIVERY_SALES', '##%d##: Partial Delivery Sales');
define('LOG_CDKEY_ID_STR', 'CD Key ID: %s');
define('LOG_BUYBACK_ORDER', '##BUYBACK:%d##');

define('LIVE_SUPPORT', "<script type=\"text/javascript\">
								var bom;
								(function(){
									var E=navigator.userAgent.toLowerCase();
									var FF=E.indexOf(\"firefox\")!=-1,B=E.indexOf(\"opera\")!=-1,G=E.indexOf(\"safari\")!=-1,A=!B&&!G&&E.indexOf(\"gecko\")>-1,C=!B&&E.indexOf(\"msie\")!=-1,F=!B&&E.indexOf(\"msie 6\")!=-1,D=!B&&E.indexOf(\"msie 7\")!=-1;
									bom ={isOpera:B,isSafari:G,isGecko:A,isIE:C,isIE6:F,isIE7:D,isFireFox:FF};
								})();

								function CheckInstallQQ(){
									var Link='tencent://message/?uin=800080228&WebSiteName=bizapp.qq.com&Menu=yes';
									try{
										if (bom.isIE){
											var xmlhttp=new ActiveXObject(\"TimwpDll.TimwpCheck\");
											var  n = xmlhttp.GetVersion();
											if (n >= 2.1){
												this.location.href=Link;
											}else{
												alert(\"OffGamers网温馨提示：\\r\\n　　请您访问http://im.qq.com/下载新版的QQ/TM以支持与拍拍店主在线交流！\");
												window.target=\"_top\";
												window.open(\"http://im.qq.com/\");
											}
										}else if (bom.isFireFox){
											this.location.href=Link;
										}else{
											alert(\"OffGamers网温馨提示：\\r\\n　　您使用的浏览器不支持QQ临时会话功能，建议您加对方为好友，或使用IE/TT/FireFox浏览器访问。\");
											return false;
										}
									}catch(e){
										alert(\"OffGamers网温馨提示：\\r\\n　　请您访问http://im.qq.com/下载新版的QQ/TM以支持与拍拍店主在线交流！\");
										window.target=\"_top\";
										window.open(\"http://im.qq.com/\");
									}
								}
							</script>

							<img style=\"CURSOR: pointer\" onclick=\"window.location.href='" . tep_href_link(FILENAME_CUSTOMER_SUPPORT) . " ';\" border=\"0\" SRC=\"http://image.offgamers.com/livechat/reponline.gif\" />
							<div class=\"dottedLine\"></div>
							<div class=\"menuTitle\" style=\"padding:0px 8px;text-align:center;\">客服QQ : <font color=\"red\">800080228</font></div>
							<div style=\"padding: 0px 8px;float:right;\">
								<img style=\"CURSOR: pointer\" onclick=\"javascript:CheckInstallQQ()\" border=\"0\" SRC=\"http://wpa.qq.com/pa?p=1:800080228:7\" />
							</div>");

define('B2C_SELLING_LIVE_SUPPORT', "<script type=\"text/javascript\">var __lc={};__lc.license=1306592;__lc.skill=1;__lc.params=[{name: 'identifier ', value: 'OGM_CUSTOMER_NAME' },{name: 'discountGroup', value: ' OGM_CUSTOMER_GROUP ' },{name: 'customerEmail', value: 'OGM_CUSTOMER_EMAIL' }];(function(){var lc=document.createElement('script');lc.type='text/javascript';lc.async=true;lc.src=('https:'==document.location.protocol ? 'https://' : 'http://') + 'cdn.livechatinc.com/tracking.js';var s=document.getElementsByTagName('script')[0];s.parentNode.insertBefore(lc, s);})();</script><style type=\"text/css\">#livechat-compact-container,#livechat-full{z-index: 565 !important;}#livechat-eye-catcher{bottom: 69px !important;z-index: 565 !important;}</style>");

define('TEXT_SELLER_INFO_DESC', 'OffGamers旨在为企业个人提供一个安全可靠的交易环境来出售他们多余的游戏货币。
									<br><br>为了营造一个安全，公正，有竞争力的游戏环境， 我们极力反对使用游戏外挂，窃用，非法的宏指令，第三方程序,游戏里的非法广告和不道德的行为。任何卖方被发现有以上行为将被列入黑名单，我们将没收其款项作为惩罚的一种形式。
									<br><br>OffGamers联系来自世界各地的卖方和买方，我们向您保证我们将严格保密您的个人信息，并为交易双方完成交易提供最好的服务平台。
									<br><br><div class="dottedLine"><!-- --></div><br>
									<h2>重要通知</h2><br>
									&#8250; <h3>注册须知：</h3> 请牢记注册帐户时候设立的密保问答，这是修改您帐号资料的重要凭证； 请留下您的真实姓名和联络方式（本人手机、QQ、MSN、座机等），这是核实您身份的重要依据； 如果您没有认真对待密码问答和留下真实信息的话，会产生很多诸如无法修改帐户信息、无法提取货款等麻烦，请务必认真对待！<br><br>
									&#8250; <h3>交易截图:</h3> 为了加快完单速度，确保交易截图的完整性和安全性，请各位供货商在完成交易后一小时内上传截图，我们也会第一时间检查截图并做完单处理；如果超过一小时还没有接收到截图，系统会自动挂单5天并开始联络顾客确认完单。所以，为了避免对应的麻烦，请各位供货商及时上传截图。谢谢您的合作。<br><br>
									&#8250; <h3>订单期限:</h3> 各位供货商请注意，届时所有订单都必须在一小时内交易完毕。如果您无法在此段时间内交易，请立即联系我们的即时客服平台。对于在此时间段外完成交易的订单，OffGamers概不负责。<br><br>
									&#8250; <a href="http://kb.offgamers.com/zhcn/?p=149">出售金币的步骤</a>
									<br>&#8250; <a href="http://kb.offgamers.com/zhcn/?p=576">魔兽-卖方指南</a>
									<br>&#8250; <a href="http://kb.offgamers.com/zhcn/?p=407">卖方指南</a>
									<br>&#8250; <a href="http://www.offgamers.com/zh-CN/buyback-terms-and-conditions-cn-simp-i-631.ogm">卖方条款</a>
									<br>&#8250; <a href="http://kb.offgamers.com/zhcn/?p=159">购物代金券/货款余额指南</a>');

require(DIR_WS_LANGUAGES . 'add_ccgvdc_english.php');

// Captcha
define('ERROR_INVALID_CODE', '您输入的验证码不正确，无法提交.');
define('TEXT_CAPTCHA_ANSWER', 'Ans.');
define('TEXT_REFRESH_CAPTCHA', '无法看清？, <a href="javascript:void(0);" onClick="get_captcha_img(\'%s\');">请换一张</a>');
define('TEXT_REFRESH_CAPTCHA_VIP', '无法看清？, <a href="javascript:void(0);" onClick="get_captcha_img(\\\'%s\\\');">请换一张</a>');
define('TEXT_CAPTCHA_INSTRUCTION', '如果答案为负值, 请按示例输入(-8)');
define('NOTE_DO_NOT_RETURN_GOODS', '<b>重要提示：</b> 交易完成后，OffGamers供货商绝不会要求您退还游戏币。也请您不要将游戏币退还给自称为OffGamers供货商的任何人或退还给与您交易的角色名相同的人员。');

// Facebook Connect
define('ERROR_INVALID_FB_UID', 'Facebook UID错误.');
define('SUCCESS_FB_DISCONNECTED', '<h3><font color=red>您的facebook帐户已断开</font></h3>');
define('HEADER_FB_CONNECT_SELECTION', '与Facebook连接');
define('TEXT_FB_CONNECT_SELECTION', '您好%s, <br>您有OffGamers帐户吗?');
define('OPTION_FB_CONNECT_SELECTION_FIRST', '<b>没有，我初次来OffGamers！</b><br>创建新OffGamers帐户！');
define('OPTION_FB_CONNECT_SELECTION_SECOND', '<b>是的，我有OffGamers帐户！</b><br>将我的Facebook帐户与OffGamers帐户相连接！');

// HLA
define('TEXT_ADVANCED_SEARCH_OPTIONS', '其他筛选条件');

define('TEXT_HLA_ANY', '全部');

define('TABLE_HLA_HEADING_LEVEL', '等级');
define('TABLE_HLA_HEADING_RACE', '种族');
define('TABLE_HLA_HEADING_CLASS', '职业');
define('TABLE_HLA_HEADING_REFERRENCE', '编号');
define('TABLE_HLA_HEADING_PRICE', '价格');
define('TABLE_HLA_HEADING_ACTION', '运作');

define('TEXT_HLA_VIEW_PROFILE', '&#8250;&nbsp;查看英雄榜');
define('TEXT_HLA_ALTERNATE_CHARACTERS', '其它角色(ID)');

define('TEXT_HLA_CONFIRMATION_NOTES', '<font color="red"><b>重要提示:</b></font> 如果Blizzard发现同一个账号在很短时间通过不同IP登录，那么就会锁定此账号。<br /><br />预防措施：<br />a)玩家可以通过登录战网自动识别新IP来避免新购买的账号被锁定。<br /><br />b)购入帐号后，建议玩家马上把<a href="http://battle.net" target="_blank">战网</a><font color="red"><b>国家区域</b></font>的设定改为成本身的国家区域。');

define('EMAIL_HLA_NEW_BUYBACK_ORDER_NUMBER', 'Buyback Order Number: %s');
define('EMAIL_HLA_NEW_BUYBACK_DATE_ORDERED', 'Buyback Order Date: %s');
define('EMAIL_HLA_NEW_BUYBACK_CUSTOMER_EMAIL', 'E-mail Address: %s');
define('EMAIL_HLA_NEW_BUYBACK_STATUS', 'Status: %s');
define('EMAIL_HLA_NEW_BUYBACK_SUBJECT', implode(' ', array(EMAIL_SUBJECT_PREFIX, "New Buyback Order #%s")));
define('EMAIL_HLA_NEW_BUYBACK_BODY', "Thank you for selling your items to " . STORE_NAME . ".\n\n Supplier Order Summary:\n" . EMAIL_SEPARATOR . "\n");
define('EMAIL_HLA_NEW_BUYBACK_PRODUCTS', "\n\n" . 'Products');
define('EMAIL_HLA_NEW_BUYBACK_ORDER_TOTAL', 'Total: %s');
define('EMAIL_HLA_NEW_BUYBACK_COMMENTS', 'Extra Information:');
define('EMAIL_HLA_NEW_BUYBACK_ORDER_CLOSING', "Please contact us via our Online Live Support service or e-<NAME_EMAIL> if you face an issue with the buyback. Please remember to include your Buyback Order Number when contacting us to expedite the process.\n\nYou will receive an e-mail notification whenever there is an update in your buyback order status. You can also review your buyback orders and check their status by signing in to OffGamers.com and clicking on \"View Buyback Order History\".\n\nThank you for supporting " . STORE_NAME . '.');

define('EMAIL_HLA_NEW_BUYBACK_ORDER_FOOTER', "\n\n" . EMAIL_FOOTER);

define('EMAIL_HLA_BUYBACK_ORDER_GUIDE', '
	<b>Order Flow : [Pending] --> [Processing] --> [Completed] or [Canceled]</b>

	[Pending] : Order submission incomplete. Kindly login to OffGamers.com and submit the missing character information.
	[Processing] : This delivery is being registered into our system.
	[Completed] : This delivery has been registered completely.
	[Canceled] : The order has been canceled.

	<b>Note 1:</b> Please ensure the Account information you provided is 100% accurate.

	<b>Note 2:</b> Once the Account information been submitted, please do not login to the account, either via the account management page or ingame.

	Important: Please take note that Blizzard may lock the account if they detect a different IP accessing the account within a short period of time. As a precaution:

	<b>Note 3:</b> Once your order is in the [Completed] status, payment will be made available to you within 15 minutes at your OffGamers Account. Use the [Withdraw] function to withdraw your payment and have it credited to a payment method of your choosing.
	');

define('EMAIL_HLA_SUPPLIER_SUBMITTED_INFO', '
	<b>Order Flow : [Pending] --> [Processing] --> [Completed] or [Canceled]</b>

	Submitted information
	');

define('TEXT_HLA_SEARCH_NO_RECORDS', '抱歉,并无相对应角色/帐号可供选择。<br /> 请重新选择。');

define('HLA_REGION', '区域');
define('HLA_RACE', '种族');
define('HLA_CLASS', '职业');
define('HLA_SIDE', '阵营');
define('HLA_LEVEL', '等级');
define('HLA_GENDER', '性别');
define('HLA_PRICE', '价格');
define('HLA_TALENT', '天赋');
define('HLA_REFERENCE_ID', '参考编号(ID)');
define('HLA_SERVER', '服务器(区)');

// WOW : Race
define('HLA_RACE_BLOOD_ELF', '血精灵');
define('HLA_RACE_DRAENEI', '德莱尼');
define('HLA_RACE_DWARF', '矮人');
define('HLA_RACE_GNOME', '侏儒');
define('HLA_RACE_GOBLIN', '地精');
define('HLA_RACE_HUMAN', '人类');
define('HLA_RACE_NIGHT_ELF', '暗夜精灵');
define('HLA_RACE_ORC', '兽人');
define('HLA_RACE_PANDAREN', '熊猫人');
define('HLA_RACE_TAUREN', '牛头');
define('HLA_RACE_TROLL', '巨魔');
define('HLA_RACE_UNDEAD', '亡灵');
define('HLA_RACE_WORGEN', '狼人');

// Warhammer : Race
define('HLA_RACE_CHAOS', '混沌军团');
define('HLA_RACE_DARK_ELF', '黯精灵');
define('HLA_RACE_WARHAMMER_DWARF', '矮人王国');
define('HLA_RACE_EMPIRE', '人类帝国');
define('HLA_RACE_GREENSKIN', '绿皮部落');
define('HLA_RACE_HIGH_ELF', '高精灵');

// Age Of Conan : Race
define('HLA_RACE_AQUILONIAN', '亚奎隆人');
define('HLA_RACE_CIMMERIAN', '西米里人');
define('HLA_RACE_STYGIAN', '冥族');

// AION : Race
define('HLA_RACE_ASMODIAN', '魔族');
define('HLA_RACE_ELYOS', '天族');

// RIFT : Race
define('HLA_RACE_DEFIANT', '守护者');
define('HLA_RACE_GUARDIAN', '挑战者');

// WOW : Class
define('HLA_CLASS_DEATH_KNIGHT', '死亡骑士');
define('HLA_CLASS_DRUID', '德鲁伊');
define('HLA_CLASS_HUNTER', '猎人');
define('HLA_CLASS_MAGE', '法师');
define('HLA_CLASS_MONK', '武僧');
define('HLA_CLASS_PALADIN', '圣骑士');
define('HLA_CLASS_PRIEST', '牧师');
define('HLA_CLASS_ROGUE', '潜行者');
define('HLA_CLASS_SHAMAN', '萨满祭司');
define('HLA_CLASS_WARLOCK', '术士');
define('HLA_CLASS_WARRIOR', '战士');

// Warhammer : Class
define('HLA_CLASS_ARCHMAGE', '大魔导师');
define('HLA_CLASS_BLACK_ORC', '黑兽人');
define('HLA_CLASS_BLAZING_SUN_KNIGHT', '焰阳骑士');
define('HLA_CLASS_BRIGHT_WIZARD', '炽法师');
define('HLA_CLASS_CHOPPA', '碎骨兽人');
define('HLA_CLASS_CHOSEN', '混沌战士');
define('HLA_CLASS_DARK_ELF_BLACK_GUARD', '黑暗卫士');
define('HLA_CLASS_DISCIPLE_OF_KHAINE', '卡恩使徒');
define('HLA_CLASS_ENGINEER', '工程师');
define('HLA_CLASS_IRONBREAKER', '铁锤勇士');
define('HLA_CLASS_MAGUS', '混沌奥术师');
define('HLA_CLASS_MARAUDER', '混沌掠夺者');
define('HLA_CLASS_RUNE_PRIEST', '符文牧师');
define('HLA_CLASS_SHADOW_WARRIOR', '阴影战士');
define('HLA_CLASS_SLAYER', '碎铁战士');
define('HLA_CLASS_SORCERESS', '黑巫师');
define('HLA_CLASS_SQUIG_HERDER', '牙突牧者');
define('HLA_CLASS_SWORDMASTER', '御剑士');
define('HLA_CLASS_WARRIOR_PRIEST', '战斗牧师');
define('HLA_CLASS_WHITE_LION', '白狮');
define('HLA_CLASS_WITCH_ELF', '巫灵');
define('HLA_CLASS_WITCH_HUNTER', '猎巫人');
define('HLA_CLASS_ZEALOT', '混沌战士');

define('HLA_CLASS_WARHAMMER_SHAMAN', '地精萨满');

// Age Of Conan : Class
define('HLA_CLASS_ASSASSIN', '刺客');
define('HLA_CLASS_BARBARIAN', '野蛮人');
define('HLA_CLASS_BEAR_SHAMAN', '熊魂萨满');
define('HLA_CLASS_CONQUEROR', '征服者');
define('HLA_CLASS_DARK_TEMPLAR', '黑暗圣堂');
define('HLA_CLASS_DEMONOLOGIST', '恶魔学家');
define('HLA_CLASS_GUARDIAN', '守护者');
define('HLA_CLASS_HERALD_OF_XOTLI', '罗里神使');
define('HLA_CLASS_NECROMANCER', '死灵法师');
define('HLA_CLASS_PRIEST_OF_MITRA', '密特拉牧师');
define('HLA_CLASS_RANGER', '游侠');
define('HLA_CLASS_TEMPEST_OF_SET', '暴风赛特');

// AION : Class
define('HLA_CLASS_CHANTER', '护法星');
define('HLA_CLASS_CLERIC', '治愈星');
define('HLA_CLASS_GLADIATOR', '剑星');
define('HLA_CLASS_SORCERER', '精灵星');
define('HLA_CLASS_SPIRITMASTER', '魔道星');
define('HLA_CLASS_TEMPLAR', '守护星');
define('HLA_CLASS_AION_RANGER', '弓星');
define('HLA_CLASS_AION_ASSASSIN', '杀星');

define('HLA_SIDE_ALLIANCE', '联盟');
define('HLA_SIDE_HORDE', '部落');

define('HLA_GENDER_MALE', '男');
define('HLA_GENDER_FEMALE', '女');

define('HLA_TALENT_AFFLICTION', '痛苦&nbsp;&nbsp;(术士)');
define('HLA_TALENT_ARCANE', '奥术&nbsp;&nbsp;(法师)');
define('HLA_TALENT_ARMS', '武器&nbsp;&nbsp;(战士)');
define('HLA_TALENT_ASSASSINATION', '刺杀&nbsp;&nbsp;(潜行者)');
define('HLA_TALENT_BALANCE', '平衡&nbsp;&nbsp;(德鲁伊)');
define('HLA_TALENT_BEAST_MASTERY', '野兽掌握&nbsp;&nbsp;(猎人)');
define('HLA_TALENT_BLOOD', '鲜血&nbsp;&nbsp;(死亡骑士)');
define('HLA_TALENT_COMBAT', '战斗&nbsp;&nbsp;(潜行者)');
define('HLA_TALENT_DEMONOLOGY', '恶魔学识&nbsp;&nbsp;(术士)');
define('HLA_TALENT_DESTRUCTION', '毁灭&nbsp;&nbsp;(术士)');
define('HLA_TALENT_DISCIPLINE', '戒律&nbsp;&nbsp;(牧师)');
define('HLA_TALENT_ELEMENTAL', '元素战斗&nbsp;&nbsp;(萨满祭司)');
define('HLA_TALENT_ENHANCEMENT', '增强&nbsp;&nbsp;(萨满祭司)');
define('HLA_TALENT_FERAL_COMBAT', '野性战斗&nbsp;&nbsp;(德鲁伊)');
define('HLA_TALENT_FIRE', '火焰&nbsp;&nbsp;(法师)');
define('HLA_TALENT_FROST', '冰霜&nbsp;&nbsp;(法师,死亡骑士)');
define('HLA_TALENT_FURY', '狂怒&nbsp;&nbsp;(战士)');
define('HLA_TALENT_HOLY', '神圣&nbsp;&nbsp;(圣骑士,牧师)');
define('HLA_TALENT_MARKSMANSHIP', '射击&nbsp;&nbsp;(猎人)');
define('HLA_TALENT_PROTECTION', '防护&nbsp;&nbsp;(圣骑士,战士)');
define('HLA_TALENT_RESTORATION', '恢复&nbsp;&nbsp;(萨满祭司,德鲁伊)');
define('HLA_TALENT_RETRIBUTION', '惩戒&nbsp;&nbsp;(圣骑士)');
define('HLA_TALENT_SHADOW', '暗影&nbsp;&nbsp;(牧师)');
define('HLA_TALENT_SUBTLETY', '敏锐&nbsp;&nbsp;(潜行者)');
define('HLA_TALENT_SURVIVAL', '生存&nbsp;&nbsp;(猎人)');
define('HLA_TALENT_UNHOLY', '邪恶&nbsp;&nbsp;(死亡骑士)');

// Warhammer : Server
define('HLA_SERVER_BADLANDS', 'Badlands');
define('HLA_SERVER_DARK_CRAG', 'Dark Crag');
define('HLA_SERVER_DARKLANDS', 'Darklands');
define('HLA_SERVER_GORFANG', 'Gorfang');
define('HLA_SERVER_HELDENHAMMER', 'Heldenhammer');
define('HLA_SERVER_IRON_ROCK', 'Iron Rock');
define('HLA_SERVER_IRONCLAW', 'Ironclaw');
define('HLA_SERVER_IRONFIST', 'Ironfist');
define('HLA_SERVER_MAGNUS', 'Magnus');
define('HLA_SERVER_MONOLITH', 'Monolith');
define('HLA_SERVER_OSTERMARK', 'Ostermark');
define('HLA_SERVER_PHOENIX_THRONE', 'Phoenix Throne');
define('HLA_SERVER_PRAAG', 'Praag');
define('HLA_SERVER_SKULL_THRONE', 'Skull Throne');
define('HLA_SERVER_THORGRIM', 'Thorgrim');
define('HLA_SERVER_VOLKMAR', 'Volkmar');
define('HLA_SERVER_VORTEX', 'Vortex');
define('HLA_SERVER_WASTELAND', 'Wasteland');

// Age Of Conan : Server
define('HLA_SERVER_BLOODSPIRE', 'Bloodspire');
define('HLA_SERVER_CIMMERIA', 'Cimmeria');
define('HLA_SERVER_GWAHLUR', 'gwahlur');
define('HLA_SERVER_SET', 'Set');
define('HLA_SERVER_TYRANNY', 'Tyranny');
define('HLA_SERVER_WICCANA', 'Wiccana');

// AION US: Server
define('HLA_SERVER_ISRAPHEL', 'Israphel');
define('HLA_SERVER_NEZEKAN', 'Nezekan');
define('HLA_SERVER_SIEL', 'Siel');
define('HLA_SERVER_VAIZEL', 'Vaizel');
define('HLA_SERVER_ZIKEL', 'Zikel');

// AION EU: Server
define('HLA_SERVER_BALDER', 'Balder');
define('HLA_SERVER_KROMEDE', 'Kromede');
define('HLA_SERVER_PERENTO', 'Perento');
define('HLA_SERVER_SPATALOS', 'Spatalos');
define('HLA_SERVER_SUTHRAN', 'Suthran');
define('HLA_SERVER_TELEMACHUS', 'Telemachus');
define('HLA_SERVER_THOR', 'Thor');
define('HLA_SERVER_URTEM', 'Urtem');

define('ERROR_INVALID_TOP_UP_ACCOUNT', '充值帐户资讯错误.');
define('ERROR_DTU_EXCEED_TOP_UP_LIMIT', '您的充值已達到限額。');
define('ERROR_UNABLE_VALIDATE_TOP_UP_ACCOUNT', "游戏官网服务器维护，暂时无法使用“直充服务”。网站恢复使用时，将另行通知。");
define('ERROR_DTU_NO_CHARACTERS_IN_LIST', "您所提供的帐户资讯沒有角色記錄。");

define('TABLE_HEADING_DELIVERY_MODE', '发货方式');

define('TEXT_INFO_SEND_TO_MY_ACCOUNT', '发送至OffGamers帐户');
define('TEXT_INFO_CHOOSE_PREFFERED_DELIVERY_MODE', '选择您的首选发货方式');
define('TEXT_INFO_DELIVERY_MODE', '发货方式');
define('TEXT_INFO_DELIVERY_SEND_TO_ACCOUNT', 'OffGamers帐户');
define('TEXT_INFO_DELIVERY_SEND_TO_ACCOUNT_DESCRIPTION', "在OffGamers购买订单历史记录里查看。<a href='http://kb.offgamers.com/zhcn/?p=155' target='new'>更多详情</a>");
define('TEXT_INFO_DELIVERY_DIRECT_TOP_UP', '直充服务');
define('TEXT_INFO_DELIVERY_DIRECT_TOP_UP_DESCRIPTION', "请填写您帐户帐号，所购买的产品会直接充值到您的帐户内。<a href='http://kb.offgamers.com/zhcn/?p=145' target='new'>更多详情</a>");
define('TEXT_INFO_DELIVERY_IN_STORE_PICKUP', '经销铺购买');
define('TEXT_INFO_DELIVERY_IN_STORE_PICKUP_DESCRIPTION', '请至Pudu OffGamers经销铺领取您的产品。');
define('TEXT_INFO_VIEW_CODE_IN_ACCOUNT_HISTORY', "在OffGamers购买订单历史记录里查看。<br><a href='http://kb.offgamers.com/zhcn/?p=155' target='new'>更多详情</a>");
define('TEXT_INFO_MOBILE_PHONE_NUMBER_IS_VALID', '<span class="requiredInfo">*</span> 请确保您当前的手机号码是有效的。否则，请点击<a href="' . tep_href_link(FILENAME_ACCOUNT_EDIT, '', 'SSL') . '">这里</a>编辑。');

define('TEXT_PWL_IMPORTANT_NOTICE', '我们将会在1至2小时内开始代练您的订单，请不要登录已经提交给我们做代练的账号因为这可能会导致IP地址发生冲突而把您的账号列入被GM封号的风险。');

define('ERROR_INVALID_QUANTITY', '数量不正确');
define('ENTRY_QTY', '数量');
define('ENTRY_DELIVERY_METHOD', '交易方式');

define('TEXT_INFO_DDOS_ATTACH_SUBJECT', '最新公告');
define('TEXT_INFO_DDOS_ATTACH_CONTECT', '欢迎各位前去浏览<a href="http://www.facebook.com/OffGamers" style="color:red;text-decoration: underline;">Facebook</a>和<a href="http://twitter.com/#!/offgamers" style="color:red;text-decoration: underline;">Twitter</a>中的OffGamers页面，了解更多关于OffGamers网站、产品以及服务的更新信息。谢谢！');

define('ERROR_CHECKOUT_QUANTITY_CONTROL_EXCEED', '您购买的产品数量已达到该产品的最大购买量');
define('ERROR_CHECKOUT_QUANTITY_CONTROL_ORDERS', '您购买的产品已达到该产品的最大购买量。请检查产品发货时间，查看购买上限。 <br />');

define('TEXT_EXP_CHECKOUT_QTY_NORMAL', '&laquo; 返回');
define('TEXT_EXP_CHECKOUT_QTY_BULK', '点击这里购买更多');
define('TEXT_EXP_CHECKOUT_BULK_NOT_AVAILABLE', '您要求购买的产品数量不足。如果要大宗购物，请联系<a href="mailto:<EMAIL>"><EMAIL></a>');

define('COMBO_SELECT_QTY', '数量');
define('COMBO_SELECT_DELIVERY_MODE', '发货方式');

define('WARNING_PRODUCT_PRICE_WITHOUT_GST', '税前价格');

//Telesign phone verification
define('TEXT_CALL_LANGUAGE', '接听电话语言:');
define('IMAGE_BUTTON_TEXT_ME_NOW', '立即发送短信');
define('TEXT_SMS_NOTES', '"立即发送短信"是一种短信功能，且只适用于移动电话。短信内容为英文。');
//
define('TEXT_INFO_VERIFYING', '核实中');
define('TEXT_INFO_SEARCH_NO_PRODUCT_FOUND', '<b>未找到您要的产品。</b>请尝试使用其它关键词进行搜索或者在<a href="' . tep_href_link(FILENAME_SEARCH_ALL_GAMES) . '">商铺</a>内寻找。');
define('TEXT_INFO_BROWSE_ALL_RESULTS', '浏览所有搜索结果');
define('MENU_TITLE_LOGIN_ACCOUNT', '登录账户');
define('MENU_TITLE_REGISTER_ACCOUNT', '注册账户');
define('MENU_TITLE_CHECKOUT', '结账');
define('MENU_TITLE_BACK_TO_STORE', '返回购物商城');
define('BUTTON_BROWSE_IN_STORE', '浏览商城');
define('BUTTON_ALL_PAYMENT_METHODS', '所有付款方式');
define('HEADER_LOGIN_TO_YOUR_ACCOUNT', '会员登入');
define('LOGIN_WITH_FB_TITLE', '或登录:');
define('BUTTON_READ_MORE_NEWS', '更多新闻');
define('MENU_HEADER_GROUP_BY_PLATFORM', '游戏平台');
define('MENU_HEADER_GROUP_BY_PRODUCT_TYPE', '产品类型');

define('EMAIL_G2G_BUYBACK_SUBJECT', "New Buyback Order #%s");
define('EMAIL_G2G_BUYBACK_BODY', "Thank you for selling your items to %s.\n\n Supplier Order Summary:\n %s \n");
define('EMAIL_G2G_BUYBACK_ORDER_NUMBER', 'Buyback Order Number: %s');
define('EMAIL_G2G_BUYBACK_DATE_ORDERED', 'Buyback Order Date: %s');
define('EMAIL_G2G_BUYBACK_CUSTOMER_EMAIL', 'E-mail Address: %s');
define('EMAIL_G2G_BUYBACK_PRODUCTS', "\n\n" . 'Products');
define('EMAIL_G2G_BUYBACK_ORDER_TOTAL', 'Total: %s');
define('EMAIL_G2G_BUYBACK_COMMENTS', 'Extra Information:');
define('EMAIL_G2G_BUYBACK_STATUS', 'Status: Pending');
define('EMAIL_G2G_BUYBACK_ORDER_GUIDE', '<b>Order Flow : [Pending] --> [Processing] --> [Completed] or [Canceled]</b><br /><br />[Pending] : Order submission incomplete. Kindly login to OffGamers.com and submit the missing character information.<br />[Processing] : This delivery is being registered into our system.<br />[Completed] : This delivery has been registered completely.<br />[Canceled] : The order has been canceled.<br /><br /><b>Note 1:</b> Please ensure the Account information you provided is 100% accurate.<br /><br /><b>Note 2:</b> Once the Account information been submitted, please do not login to the account, either via the account management page or ingame.<br /><br />Important: Please take note that Blizzard may lock the account if they detect a different IP accessing the account within a short period of time. As a precaution: <br /><br /><b>Note 3:</b> Once your order is in the [Completed] status, payment will be made available to you within 15 minutes at your OffGamers Account. Use the [Withdraw] function to withdraw your payment and have it credited to a payment method of your choosing.');
define('EMAIL_G2G_BUYBACK_ORDER_CLOSING', "Please contact us via our Online Live Support service or e-mail to %s if you face an issue with the buyback. Please remember to include your Buyback Order Number when contacting us to expedite the process.\n\nYou will receive an e-mail notification whenever there is an update in your buyback order status. You can also review your buyback orders and check their status by signing in to OffGamers.com and clicking on \"View Buyback Order History\".\n\nThank you for supporting %s.");

define('TEXT_ERROR_MAX_PENDING_ORDER', '您的帐户有%d待审单子，请检查以前的订单付款状态');
define('EMAIL_MAX_PENDING_ORDER_SUBJECT', 'Customer ID #%d Hit Maximum Pending Order');
define('EMAIL_MAX_PENDING_ORDER_TEXT', 'Customer ID #%d Hit Maximum Pending Order, kindly liaise with customer.');

define('TEXT_DEFAULT_CONTACT_CS_ERROR_MESSAGE','如果错误仍然存在，请点击<a href=\'http://support.offgamers.com/support/home\'>这里</a>联系我们。');

define('TEXT_SIGN_UP', '注册');
define('TEXT_LOG_IN', '登录');
define('TEXT_OR_CONNECT', '或连接');

define('TEXT_MY_OGM', '我的OffGamers');
define('TEXT_OVERVIEW', '概述');
define('TEXT_BUY_HISTORY', '购买记录');
define('TEXT_SELL_HISTORY', '销售记录');
define('TEXT_REQUEST_PAYMENT', '提款');

define('TEXT_ACCOUNT_ID', '帐户ID');
define('TEXT_BUY_STATUS', '买家状态');
define('TEXT_MANAGE_PROFILE', '帐号管理');
define('TEXT_SOCIAL_CONNECT', '社交连接');
define('TEXT_STORE_CREDITS', '购物代金券');
define('TEXT_WOR', 'WOR币');

define('TEXT_REGIONAL_TITLE', '您下次访问的区域设置。');
define('BTN_SAVE_CHANGES', '保存');

define('TEXT_OGM_DOMAIN', 'OffGamers.com');
define('TEXT_OGM_CAPTION', 'MMO游戏 CDkeys 和游戏卡');
define('TEXT_G2G_DOMAIN', 'G2G.com');
define('TEXT_G2G_CAPTION', 'Gamer2Gamer市场');
define('TEXT_GMZ_DOMAIN', 'Gamernizer.com');
define('TEXT_GMZ_CAPTION', '玩免费多人浏览器游戏');
?>