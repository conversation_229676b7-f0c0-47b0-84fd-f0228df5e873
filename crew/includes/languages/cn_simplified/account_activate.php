<?php 
define('NAVBAR_TITLE_1', '激活帐户');   
define('NAVBAR_TITLE_2', '验证电子邮件');
define('NAVBAR_TITLE_3', '验证手机号码');
define('NAVBAR_TITLE_4', '验证订单上的手机号码');
define('HEADING_TITLE_1', '电子邮箱地址认证'); 
define('HEADING_TITLE_2', '手机号码验证');

// Phone Verification
define('TEXT_TITLE_ENTER_PHONE_NUMBER', '请确保以下手机号码是正确且能联系到您。');
define('ENTRY_CONTACT_NUMBER_EXIST_ERROR', "您提供的手机号码已使用或不存在。请提供另一个手机号码。");

define('TEXT_VERIFY_INSTRUCTION_LINK', '这是什么？');
define('TEXT_COUNTRY_NOT_SUPPORT', '（我们的手机电话验证系统不支持的国家）');
define('ERROR_REPORT', '错误报告');
define('TEXT_RESEND_VERIFICATION_CODE', '<a href= "' . tep_href_link(FILENAME_ACCOUNT_ACTIVATE, 'action=resend') . '">重新发送验证码</a>');
define('TEXT_CHANGE_PHONE_NUMBER', '(不是您的手机号码? <a id="link_edit_phone_number" href="' . tep_href_link(FILENAME_ACCOUNT_EDIT) . '">请点击修改</a>.)');

define('CODE_MATCH_MESSAGE', '您的手机号码确认成功。 谢谢您。');
define('CODE_NOT_MATCH_RETRY_MESSAGE', '您输入的确认码不正确，请重试。');
define('CODE_NOT_MATCH_MESSAGE', '你输入的验证码不正确。 我们会尽快审核您的订单。');
define('REQUEST_REACHED_MAX_ATTEMPT_ERROR', '您已达到今日尝试次数上限，请24小时后重试。');

define('TABLE_HEADING_COMMENTS', '输入关于订单进程的评论。');

define('EDIT_TELEPHONE' , '（编辑）');

// Error Report Content
define('EMAIL_TITLE', '手机号码验证错误报告');
define('EMAIL_ADMIN_NAME', '客户服务');
define('EMAIL_CONTENT_1', '<b>报告日期和时间:</b>');
define('EMAIL_CONTENT_2', '<b>客户姓名:</b>');
define('EMAIL_CONTENT_3', '<b>客户 ID:</b>');
define('EMAIL_CONTENT_4', '<b>订单 ID:</b>');
define('EMAIL_CONTENT_5', '<b>订单日期和时间:</b>');
define('EMAIL_CONTENT_6', '<b>订单状态:</b>');
define('EMAIL_CONTENT_7', '<b>手机电话所在地:</b>');
define('EMAIL_CONTENT_8', '<b>手机号码:</b>');

define('EMAIL_REPORT_SEND_MESSAGE', '已发送email给我们的助理。');

// 电子邮件验证
define('ENTRY_COUNTRY_CODE', '国家代码:&nbsp;:');
define('ENTRY_VERIFICATION_CODE', '验证码:');
define('MESSAGE_SUCCESS_MAIL_2','邮箱验证邮件已重新发至您的邮箱。');
define('MESSAGE_VERIFY_FAIL', '电子邮件验证失败。');
define('MESSAGE_EMAIL_VERIFIED', '您的电子邮件已被验证了。');
define('MESSAGE_VERIFIED_SUCCESS', '恭喜您！电子邮件验证成功。');
define('MESSAGE_VERIFIED_NOT_MATCH', '电子邮件或验证码不正确。');
define('MESSAGE_ERROR_MAIL', '电子邮件验证失败。');

define('MANUAL_VERIFY_EMAIL_1', '为了完成您的email地址确认进程，请输入OffGamers发送给您的邮件里的验证码。');
define('MANUAL_VERIFY_EMAIL_2', '请在这里输入验证您的电子邮箱地址的验证码。' . "<br><br>");
define('MANUAL_VERIFY_EMAIL_LOGIN', '');
define('MANUAL_VERIFY_EMAIL_LOGOUT', '您的电子邮件');

define('MANUAL_VERIFY_EMAIL_LINK', '<a href= "' . tep_href_link(FILENAME_ACCOUNT_ACTIVATE, 'action=resend') . '">请点击这里获取重新发到您的邮箱地址的验证码。</a>' . '<br><br>');

define('TEXT_REQUEST_VERIFY','%s,请按此以便证码邮件被从发至您的电子邮件. %s');
define('TEXT_REQUEST_EMAIL_LOGIN_1', '确认邮箱地址');
define('TEXT_REQUEST_EMAIL_LOGOUT_1', '请输入邮箱地址');
define('TEXT_REQUEST_EMAIL_LOGIN_2', ' 如果电子邮件地址是不正确，请到账户资料更改，填入正确的电子邮件.');
define('TEXT_REQUEST_EMAIL_LOGOUT_2', '');

//发送电子邮件给客户， 当户口被激活的客户是合作联盟会员
define('EMAIL_CUSTOMER_SUBJECT', '用户激活成功');
define('EMAIL_CUSTOMER_CONTENT', '您已经成功启动' . STORE_NAME . '您的账户，联盟账户正等待认可，大约需要12小时，请耐心等待.' . "\n\n");
  
define('EMAIL_CUSTOMER_LOGIN_DETAILS', '您可登陆您的' . STORE_NAME . '、账号使用以下的登陆资料:' . "\n");  
define('EMAIL_LOGIN_ADDRESS', '登陆邮件地址: <a href= "' . tep_href_link(FILENAME_LOGIN) . '">'. tep_href_link(FILENAME_LOGIN) .'</a>');  
define('EMAIL_ADDRESS', "\n". '电子邮箱地址: ');
define('EMAIL_CUSTOMER_PASSWORD', "\n". '用户密码: ');
define('EMAIL_CUSTOMER_PASSWORD_CHANGE', "\n\n". '登陸後請更改密碼，您的聯盟登陸將與您的賬號登陸一樣');

define('IMAGE_BUTTON_CALL_ME_NOW', '现在打电话给我');

?> 