<?
/*
  	$Id: account_buyback.php,v 1.7 2007/09/28 10:16:57 weichen Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2003 osCommerce
	
  	Released under the GNU General Public License
*/

define('NAVBAR_TITLE', '出售游戏帐户');   

define('HEADING_TITLE', '出售游戏帐户'); 

define('TEXT_TITLE_CHOOSE_GAME', '请选择游戏:');  
define('BUYBACK_SECTION_MAIN_CHAR', '主要角色资料');   
define('BUYBACK_SECTION_ALTERNATE_CHAR', '后补角色资料');

define('ENTRY_BUYBACK_ACCOUNT_SERVER', '服务器:');
define('ENTRY_BUYBACK_CUSTOMER_NAME', '角色名称:');
define('ENTRY_BUYBACK_ACCOUNT_MAIN_CHAR_SERVER', '境界:');
define('ENTRY_BUYBACK_ACCOUNT_ALT_CHAR_SERVER', '境界:');
define('ENTRY_BUYBACK_MAIN_CHAR_LVL', '角色等级:');     
define('ENTRY_BUYBACK_ACCOUNT_ALT_CHAR_LVL', '等级:');
define('ENTRY_BUYBACK_MAIN_CHAR_RACE', '种族:');
define('ENTRY_BUYBACK_MAIN_CHAR_CLASS', '角色职业:'); 
define('ENTRY_BUYBACK_ALT_CHAR_CLASS', '角色职业:');
define('ENTRY_BUYBACK_MAIN_CHAR_GENDER', '角色性别:');
define('ENTRY_BUYBACK_MAIN_CHAR_EQUIPMENT', '装备:');
define('ENTRY_BUYBACK_MAIN_CHAR_MOUNT', '坐骑:');
define('ENTRY_BUYBACK_MAIN_CHAR_GOLD', '金币总量:'); 
define('ENTRY_BUYBACK_ACCOUNT_STATUS', '账户:');  
define('ENTRY_BUYBACK_ACCOUNT_CDKEY', 'CD Key虚码:');  
define('ENTRY_BUYBACK_ACCOUNT_SECRET', '秘密问题与答案:'); 
define('ENTRY_BUYBACK_ASKING_PRICE', '出口询问价格:'); 
define('ENTRY_BUYBACK_OTHER_INFO', '其他信息:');
define('ENTRY_GAME_CURRENCY_TEXT', '* (eg.: 50, 100)');
define('OPTION_ACTIVE', '活跃');
define('OPTION_INACTIVE', '非活跃');
define('OPTION_AVAILABLE', '可用的');
define('OPTION_NOT_AVAILABLE', '非可用');
define('OPTION_REMEMBER', '记住');
define('OPTION_FORGOTTEN', '忘记');

define('ENTRY_BUYBACK_NAME_ERROR', '请输入您的姓名.');
define('ENTRY_BUYBACK_MAIN_CHAR_SERVER_ERROR', '请选择您的主要角色境界.');
define('ENTRY_BUYBACK_MAIN_CHAR_LVL_ERROR', '请选择您的主要角色目前的等级.');
define('ENTRY_BUYBACK_MAIN_CHAR_RACE_ERROR', '请选择你的角色种族.');
define('ENTRY_BUYBACK_MAIN_CHAR_CLASS_ERROR', '请选择您的主要角色职业.');
define('ENTRY_BUYBACK_MAIN_CHAR_GENDER_ERROR', '请选择您的主要角色性别.');
define('ENTRY_BUYBACK_MAIN_CHAR_EQUIPMENT_ERROR', '请选择您的主要角色设备.');
define('ENTRY_BUYBACK_MAIN_CHAR_MOUNT_ERROR', '请选择您的主要角色坐骑.');
define('ENTRY_BUYBACK_MAIN_CHAR_GOLD_ERROR', '请输入一个有效的金币数量.');
define('ENTRY_BUYBACK_MAIN_ACC_STATUS_ERROR', '请注明您的帐户状态.');
define('ENTRY_BUYBACK_MAIN_ACC_CDKEY_ERROR', '请注明是否您帐户的CD Key可用.');
define('ENTRY_BUYBACK_MAIN_ACC_SECRET_ERROR', '请注明您帐户的秘密问题和答复可用.');
define('ENTRY_BUYBACK_ASKING_PRICE_ERROR', '请输入正确的价格.');

define('ENTRY_BUYBACK_ALT_CHAR_LVL_ERROR', '请选择您后补角色目前的等级.');
define('ENTRY_BUYBACK_ALT_CHAR_CLASS_ERROR', '请选择您后补角色的职业.');
define('ENTRY_BUYBACK_CANNOT_ACCEPT_ERROR', ' 我们只采购具备CDK，密保问答和角色装备良好的帐户。');
define('TEXT_CHAR_CLASS_NOT_FOUND', '<i>您的角色职业不在列表? 请再回来查看，请隔时、日多回来观览，因为我们暂时只回购被列的职业</i>');
define('TEXT_BUYBACK_ACCOUNT_NO_ALT_CHARACTER', '没有任何后补角色的资料。');
define('TEXT_BUYBACK_CANNOT_CONFIRM_MESSAGE', '请在上述检查并确认您的帐户资料. 如果有任何错误，请点击％ s回去更改。<br><br>我可以证实，所有上述资料是正确的 %s');
define('TEXT_BUYBACK_ACCOUNT_THANK_YOU_MESSAGE', '感谢您询问我们的帐户回购服务.<br>我们将在48小时内与议价透过电子邮件回复您.<br>如有任何疑问，请联络我们。');
define('TEXT_BUYBACK_ACCOUNT_CONFIRM_FOOTER', '<span class="greetUser">谢谢您</span>');
define('TEXT_ACCOUNT_BUYBACK_DISCLAIMER', '<br><b><span class="messageStackWarning">请注意，我们需要您帐户相关的CD Key虚码，秘密问题和答案，与电话号码。请在出售之前请把这些资料准备，以便我们加快处理速度.</span></b>' . "<br><br>" . 
										  'OffGamers保有拒绝负责个人资料被窃取、窜改、毁损、灭失或泄漏的帐号权利。我们不担保任何损失即使交易过程已完成。我们以采取相应的预防欺诈控制措施，实行适时维护回购帐号，所有欺诈案件将会被依法给予惩罚/处分。简而言之，所有欺诈、帐号被盗、企图倒账，将会采取严厉行动。若您提交询问两天之后没有收到我们的电子回邮，表示我们的不需要您的虎口，不将被收购。');

define('EMAIL_SUBJECT', '帐户回购的询问');
define('EMAIL_SEPARATOR', '------------------------------------------------------');
define('EMAIL_CONTENTS_HEADER', '--------------------这是来自OFFGAMERS.COM的自动发送邮件 --------------------' . "\n\n" . '感谢您联络哦OffGamers.com. 我们已收到您的帐户资料:-' . "\n\n");
define('EMAIL_SECTION_PARTICULARS', ENTRY_BUYBACK_CUSTOMER_NAME . ' %s' . "\n" . ENTRY_EMAIL_ADDRESS . ' %s' . "\n\n");
define('EMAIL_SECTION_MAIN_CHARACTER', BUYBACK_SECTION_MAIN_CHAR . "\n" . EMAIL_SEPARATOR . "\n" . ENTRY_BUYBACK_ACCOUNT_MAIN_CHAR_SERVER . ' %s' . "\n" . ENTRY_BUYBACK_MAIN_CHAR_LVL . ' %d' . "\n" . ENTRY_BUYBACK_MAIN_CHAR_RACE . ' %s' . "\n" . ENTRY_BUYBACK_MAIN_CHAR_CLASS . ' %s' . "\n" . ENTRY_BUYBACK_MAIN_CHAR_GENDER . ' %s' . "\n\n" . ENTRY_BUYBACK_MAIN_CHAR_EQUIPMENT . ' %s' . "\n" . ENTRY_BUYBACK_MAIN_CHAR_MOUNT . ' %s' . "\n" . ENTRY_BUYBACK_MAIN_CHAR_GOLD . ' %d' . "\n\n");
define('EMAIL_SECTION_ACCOUNT_INFO', ENTRY_BUYBACK_ACCOUNT_STATUS . ' %s' . "\n" . ENTRY_BUYBACK_ACCOUNT_CDKEY . ' %s' . "\n" . ENTRY_BUYBACK_ACCOUNT_SECRET . ' %s' . "\n\n" . ENTRY_BUYBACK_ASKING_PRICE . ' US $%.2f' . "\n\n" . ENTRY_BUYBACK_OTHER_INFO . "\n" . '%s' . "\n\n");
define('EMAIL_SECTION_ALT_CHARACTER', ENTRY_BUYBACK_ACCOUNT_ALT_CHAR_SERVER . ' %s' . "\n" . ENTRY_BUYBACK_ACCOUNT_ALT_CHAR_LVL . ' %d' . "\n" . ENTRY_BUYBACK_ALT_CHAR_CLASS . ' %s' . "\n\n");
define('EMAIL_CONTENTS_FOOTER', '请注:我们48小时，回顾您的询问，我们会在尽快时间与议价透过电子邮件回复您。' . "\n" . '再次感谢您选择OffGamers.' . "\n\n" . '------------------------------------------------------信息完毕 -------------------------------------------------------------');
define('EMAIL_FOOTER', STORE_EMAIL_SIGNATURE);
?>