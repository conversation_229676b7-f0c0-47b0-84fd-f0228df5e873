<?
/*
  	$Id: account_edit.php,v 1.7 2007/12/21 09:02:46 chan Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2003 osCommerce
	
  	Released under the GNU General Public License
*/

define('NAVBAR_TITLE_1', '我的帐户');
define('NAVBAR_TITLE_2', '编辑帐户');

define('HEADING_TITLE', '编辑简介');
define('MY_ACCOUNT_TITLE', '我的帐户');

// My Account phase 2 - Edit Profile
define('TITLE_LOGIN_EMAIL_PASSWORD', "登陆email和密码");
define('TITLE_PERSONAL_DETAILS', "个人信息");
define('TITLE_BILLING_ADDRESS_FOR_CREDIT_CARD', "通讯录（信用卡付款的账单信息）");

define('ENTRY_COUNTRY_CODE', '国家代码:&nbsp;');
define('ENTRY_COUNTRY_CODE_TEXT', '*');
define('ENTRY_CURRENT_CONTACT_NUMBER', '当前手机号码:&nbsp;');
define('ENTRY_CONTACT_NUMBER', '手机号码:&nbsp;');
define('ENTRY_CONTACT_NUMBER_TEXT', '*');
define('ENTRY_CONTACT_NUMBER_EG', '手机号码例如：（12）xxxxxxx &nbsp;');
define('ENTRY_INSTANT_MESSENGER', '即时通讯&nbsp;（IM）:');
define('ENTRY_BILLING_ADDRESS1', '地址1:&nbsp;');
define('ENTRY_BILLING_ADDRESS2', '地址2:&nbsp;');
define('ENTRY_NEW_COUNTRY_CODE', '新国家代码:&nbsp;');
define('ENTRY_NEW_CONTACT_NUMBER', '新手机号码:&nbsp;');

define('ENTRY_PASSWORD_NEW_CONFIRMATION', '确认新密码:');
define('ENTRY_PASSWORD_NEW_CONFIRMATION_TEXT', '*');

define('ENTRY_PASSWORD_CURRENT_NOT_MATCH_ERROR', '您当前的密码不正确。');
define('ENTRY_PASSWORD_NEW_ERROR', "您的新密码至少%d字符。");
define('ENTRY_PASSWORD_NEW_NOT_MATCH_ERROR', '您的确认密码应与新密码相符。');
define('ENTRY_PASSWORD_CONFIRMATION_ERROR', "您的确认密码至少%d字符。");
define('ENTRY_CONTACT_NUMBER_ERROR', "您的手机号码至少%d位数。");
define('ENTRY_CONTACT_NUMBER_EXIST_ERROR', "您输入的手机号码已被另外的账户使用，请输入其他的手机号码。");

define('ENTRY_EMAIL_JS_ERROR', "您输入的email不存在。");
define('ENTRY_FIRSTNAME_JS_ERROR', "名字至少%d 字符。");
define('ENTRY_LASTNAME_JS_ERROR', "姓氏至少%d字符。");
define('ENTRY_PASSWORD_CURRENT_JS_ERROR', "当前密码至少%d字符且只包括字母和数字。");
define('ENTRY_PASSWORD_JS_ERROR', "新密码至少%d字符且只包括字母和数字。");
define('ENTRY_PASSWORD_CONFIRMATION_JS_ERROR', "确认密码至少%d字符且只包括字母和数字。");
define('ENTRY_PASSWORD_CONFIRMATION_NOT_MATCH_JS_ERROR', "确认密码与新密码不符。");
define('ENTRY_CONTACT_NUMBER_JS_ERROR', "您的手机号码不存在。");
define('ENTRY_DOB_JS_ERROR', "您的出生日期不存在。");
define('ENTRY_SECRET_QUESTION_JS_ERROR', "您需从密保问题下拉菜单中选择一个密保问题。");
define('ENTRY_ANSWER_JS_ERROR', "您的答案至少包括%d字符。");
define('ENTRY_SIGNUP_TERMSANDCONDITION_JS_ERROR', "您必须同意我们的服务提案款&隐私权政策。");  

define('ENTRY_EMAIL_NOTICE', "请使用正确的email地址，以方便我们确认您的账户。");
define('ENTRY_FIRSTNAME_NOTICE', "请使用您身份证上的名字。");
define('ENTRY_LASTNAME_NOTICE', "请使用您身份证上的姓氏。");
define('ENTRY_PASSWORD_NOTICE', "您的密码至少需要%d字符，且区分大小写。");
define('ENTRY_CONFIRM_PASSWORD_NOTICE', "请重新输入您的密码。");
define('ENTRY_CONTACTNUMBER_NOTICE', "请提供有效的手机号码。如果您忘记密保问答，它将用于跟您核实信息以便重置密保问答。");
define('ENTRY_ANSWER_NOTICE', "为了确保您的账户安全，在更改，更新信息以及撤销资金的过程中，我们需要您的密保来确认。");
define('ENTRY_IM_NOTICE', "我们将通过您提供的即时通讯工具，在交货的时候联系您。");
define('ENTRY_DOB_NOTICE', "我们要求您的法定年龄。");
 
define('SUCCESS_ACCOUNT_UPDATED', '您的帐户已成功更新.');
define('SUCCESS_ACCOUNT_MOBILE_UPDATED', '您的手机号码已经更换成功。');
define('CHANGE_ACCOUNT_SETTING', '请联络 <a href="mailto:'.EMAIL_TO.'">'.EMAIL_TO.'</a> 欲更改名字，姓氏和性别.');
define('ERROR_EMAIL_EXISTED', '您已成功注册，请发送电子邮件与我们联络<a href="' . tep_href_link(FILENAME_CONTACT_US) . '">商店主人</a> 如果您想结合您的帐户.');

define('ENTRY_TELEPHONENUMBER_NOTICE', '请提供给我们一个有效的电话号码，我们将打电话向您确认信息。如果您忘记了您的密码保护问题，我们同样可以打电话给您重新设置您的密保问题和答案。');
define('ENTRY_SECRETQNA_NOTICE', '请在空白处填写您的密码保护问题的答案，以便更新您的账户信息。');

define('MANUAL_ACTIVATE_ACCOUNT','%s 请检查您的电子邮件 %s 为激活代码. 为了使您享有更好的服务，请激活您的帐户。电子邮件可能会出现在您的垃圾邮件或垃圾邮件文件夹. 请与我们联络 <a href="mailto:'.EMAIL_TO.'">'.EMAIL_TO.'</a> 如果你无法收到激活电子邮件. <br><br>请输入%s的激活代码，以激活您的帐户. 您将被重新向到登录页中后，成功地激活您的帐户.' . "<br><br>");
define('MANUAL_ACTIVATE_ACCOUNT_REG_1', '您已成功注册. ');
define('MANUAL_ACTIVATE_ACCOUNT_REG_2', '现在');
define('MANUAL_ACTIVATE_ACCOUNT_REG_3', '');
define('MANUAL_ACTIVATE_EMAIL_EXIST_LINK', 'e-mail=');

define('EMAIL_ACCOUNT_EDIT_PASSWORD_CHANGE_SUBJECT', '账户密码已更改');
define('EMAIL_ACCOUNT_EDIT_PASSWORD_CHANGE', '尊敬的客户，' . "\n\n" . '您的OffGamers账户密码已更改。' . "\n\n" . '如果不是您本人操作，请立即更改您注册的邮箱密码，并联系我们的客服平台或*********************。');
?>