<?
/*
  	$Id: account_history_info.php,v 1.11 2007/12/27 09:01:24 wailai Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2002 osCommerce
	
  	Released under the GNU General Public License
*/

//@************
define('NAVBAR_TITLE_1', '我的帐户');
define('NAVBAR_TITLE_2', '购买订单历史记录');

if ($history_type=='buyback') {
	define('NAVBAR_TITLE_3', '回购订单'); //define('NAVBAR_TITLE_3', 'Buyback Order #%s'); 
	define('NAVBAR_TITLE_4', '回购订单详情');	
	define('HEADING_TITLE', '回购');	//define('HEADING_TITLE', 'Buyback Order Information'); 
	define('HEADING_ORDER_NUMBER', '回购订单号码: %s'); //@************
} else {
	$history_type = 'orders';
	$navbar3 = ($orders_type=="current" ? MY_ACCOUNT_CURRENT_ORDERS : ($orders_type=="completed" ? MY_ACCOUNT_COMPLETED_ORDERS : ""));
	define('NAVBAR_TITLE_3', $navbar3);
	define('NAVBAR_TITLE_4', '购买订单详情');	//define('NAVBAR_TITLE_3', 'Order #%s'); 
	define('HEADING_TITLE', '购买');	//define('HEADING_TITLE', 'Order Information');	
	define('HEADING_ORDER_NUMBER', '购买订单号码: %s');
}

define('ALERT_ORDER_TELEPHONE_VERIFICATION', "<div style=\"display:inline-block; font: bold 16px Arial,Tahoma,Verdana,Helvetica,sans-serif; color: black; padding: 0px 0px 10px 5px;\">与此订单有关的电话号码没有验证。<div style=\"display: block; font-weight: normal;\"><b>%s</b>要求在您方便接听电话时打电话给您来一次电话认证。</div></div>");
define('ALERT_ORDER_STATUS_PENDING', "<table><tr><td><div style=\"display:block; font: bold 16px Arial,Tahoma,Verdana,Helvetica,sans-serif; color: black; padding: 0px 0px 10px 5px;\">一旦收到款项，我们会立刻处理您的订单。<div style=\"display: block; font-weight: normal;\">通常情况下，我们的支付处理方会对一些付款要求进行强制性检查从而导致付款延迟。 
												如果您已为您的订单打款了，但是发现订单仍处于审核状态，请先检查您的银行账户、银行卡或电子钱包里的钱是否已经完全支付出去。如果确定款项已成功支付，那么再联系我们的客服平台。
												如果确定款项已成功支付，那么再联系我们的客服平台。
									 		   </div></div></td></tr></table>");
define('ALERT_ORDER_STATUS_PENDING_CURRENCY', "<table><tr><td><div style=\"display:block; font: bold 16px Arial,Tahoma,Verdana,Helvetica,sans-serif; color: black; padding: 0px 0px 10px 5px;\">每次交易完毕后，请确认所收到的金币数。<div style=\"display: block; font-weight: normal;\">每次交易完毕后，请您在订单状态页面确认您已收到供货商发送的金币。
												如果您没有确认，系统会在供货商确认已发货之时72小时后自动为您确认。<b>这也将确保您的订单会尽快被处理，OP也将尽快打到您的账户。</b>
									 		   </div></div></td></tr></table>");
define('DATE_FORMAT_ORDER_DATE', '%d %b %Y');
define('DATE_FORMAT_ORDER_MESSAGE', '%d %b, %Y');

define('HEADING_ORDER_STATUS', '状态: %s');
define('HEADING_ORDER_DATE', '订单日期: %s');
define('HEADER_DELIVERY_DISPUTE', '发货纠纷');

define('TITLE_PRODUCT_LIST', '产品列表');
define('TITLE_QUANTITY', '数量');
define('TITLE_AMOUNT', '总额');
define('TITLE_WHAT_TRADING_METHOD', '本次交易使用了哪种交易方式?');

define('SUB_TITLE_SUB_TOTAL', '次级共计: %s'); 
define('SUB_TITLE_ESTIMATED_TOTAL', '预计总量: %s');
define('SUB_TITLE_AMOUNT_DELIVERED', '已发货金额: %s');
define('SUB_TITLE_AMOUNT_REFUNDED', '退款金额: %s');

define('TEXT_REASON', '原因:');
define('TEXT_DETAILS', '详情:');
define('TEXT_DISPUTE_DESC', '如果您未收到我们发送给您的产品，请向我们发送一份举报信。收到您的举报后，我们会立即调查您的问题并在最短时间内将调查结果告知您。');
define('TEXT_DISPUTE_DETAILS', '详情（如有）');
define('TEXT_CHARACTER_NAME', '角色名: %s');
define('TEXT_IN_GAME_TIME', '登录时间: 结帐后%d小时');
define('TEXT_IN_GAME_DURATION', '游戏里持续时间: %d小时');
define('TEXT_TOTAL_DELIVERED_GOLD', '发货总数: %s ');
define('TEXT_GOLD', '金币');
define('TEXT_CURRENT_LEVEL', '当前等级: %s');
define('TEXT_LEVEL', '等级');
define('TEXT_HOURS_PASSED', '已耗时: %s');
define('TEXT_HOURS', '小时');
define('TEXT_REFUNDED', '已退款');
define('TEXT_SHOW_ALL', '显示全部');
define('TEXT_HIDE_ALL', '隐藏全部');
define('TEXT_DOWNLOAD_ALL', '全部下载');
define('TEXT_GIFT_CARD', '礼品卡:');
define('TEXT_AMOUNT_IN_RED', '<span style="color:red;">- %s</span>');
define('TEXT_DISCOUNT_COUPON_NUMBER', '折扣券: %s');
define('TEXT_DISCOUNT_AMOUNT', '<span style="color:red;">- %s</span>');
define('TEXT_STORE_CREDIT', '代金券购货: ');
define('TEXT_STORE_CREDIT_AMOUNT', '<span style="color:red;">- %s</span>');
define('TEXT_PAYMENT_METHOD', '付款信息: %s');
define('TEXT_IS_UNLOADING', '卸载……');

define('TEXT_REMAINING_TIME', '剩余时间');
define('TEXT_AUTO_CONFIRM_DELIVERY_NOTE', '请确认您是否已收到我们发送的产品，否则系统会在72小时内自动确认收到。');
define('TEXT_DID_YOU_RECEIVE', '您收到产品了吗?');
define('TEXT_DID_YOU_ABLE_TO_LOGIN', '您可以登入吗?');
define('TEXT_DELIVERY_CONFIRMED', '已确认收到');
define('TEXT_DID_NOT_RECEIVE', '未收到');
define('TEXT_ROLLED_BACK_DELIVERED_AMT', '系统已经回滚已发数量');
define('TEXT_HLA_AUTO_CONFIRM_DELIVERY_NOTE', '<table cellspacing="0" cellpadding="0" border="0"><tr><td nowrap valign="top"><font color="#FF0000"><b>重要提示：&nbsp;</b><br />订单会在3天内由系统自动完成。基于账号安全因素，<br />基于游戏账号安全性考虑，一旦客户订单帐号资料已发，针对这样的订单我们概不退款。</font></td></tr></table>');
define('TEXT_HLA_SECRET_ANSWER_NOTE', '确认能登陆后，点击 "是" 系统便会自动发帐号的(密保) Secret Question & Answer');

define('TEXT_TOP_UP_STATUS', '充值状态');
define('TEXT_TOP_UP_MESSAGE', '充值讯息');

define('TEXT_BUYBACK_OPENED', '采购开启');
define('TEXT_BUYBACK_CLOSED', '采购关闭');
define('TEXT_BUYBACK_OPENED_DURATION', '时限设为 (%s) 小时在游戏内');
define('TEXT_BUYBACK_OPENED_DURATION_EXTEND', '时限设为+ (%s) 小时在游戏内');
define('TEXT_BUYBACK_CLOSED_UNABLE_LOCATE_CUSTOMER', '单子无法联络上顾客 (%s)');
define('TEXT_BUYBACK_CLOSED_DURATION_TIME_OUT', '在游戏内时限已过');
define('TEXT_BUYBACK_CLOSED_MANUALLY', '用户设 -(%s) 小时提早关闭采购');

define('TEXT_TOTAL_ACCUMULATED_OPEN_TIME', '总共累计开启采购时间：( %s )');
define('TEXT_PROVIDE_DELIVERY_INFO', '您未提供任何发货信息。<a href="javascript:void(0);" onClick="showMe(\'%s\');"><b>点击这里</b></a>提交发货信息，以便我们安排交易。');

define('BUTTON_EXTEND_TIME', '延长时间');
define('BUTTON_IM_ONLINE_NOW', '我在线');
define('ENTRY_IN_GAME_DURATION', '在线时长:');
define('ENTRY_HOURS', '小时');

define('IMAGE_BUTTON_SUBMIT_PAYMENT_INFO', '提交付款信息');
define('IMAGE_BUTTON_PAY_FOR_THIS_ORDER_NOW', '为该订单付款');
define('IMAGE_BUTTON_SHOW', '显示');
define('IMAGE_BUTTON_HIDE', '隐藏');
define('IMAGE_BUTTON_HIDE_CD_KEY_CODE', '隐藏 CD Key 代码');
define('IMAGE_BUTTON_CHARACTERS_PROFILER', '角色分析器');
define('LINK_CANCEL_THIS_ORDER', '取消该订单');
define('LINK_COMPLAINT_THIS_ORDER', '投诉单子');

define('ERROR_ORDER_IN_TRADING', '您的单子已经在交易状态中，请立即联络我们的客服争取而外的协助。');
define('ACCOUNT_HISTORY_DELIVERY_MODE_NOTES', "<b>请注:</b> 我们会根据您所选的交易方式尽快为您的订单安排交易。如果在您要求更改交易方式之前就已交易完毕，OffGamers不承担任何责任。");

define('MESSAGE_ORDER_CANCELLED', '该订单将在 %s 小时 %s 分钟后取消。');
define('MESSAGE_PAYMENT_INFORMATION', '如果您已为该订单付款，请提交您的付款信息。');

define('HEADER_ORDER_MESSAGE', '订单信息');
define('HEADER_DELIVERY_INFO_TITLE', '一旦您登陆游戏后，我们会立即为您的订单寻找货源。');
define('HEADER_PWL_DELIVERY_INFO_TITLE', '我们将会在代练单子完成后才购买金币。如果有任何疑问，请联系我们的即时平台。');
define('HEADER_DATE', '日期');
define('HEADER_TIME', '时间');
define('HEADER_DELIVERED_AMOUNT', '接货数量');
define('HEADER_DELIVERED_CHARACTER', '接货ID');
define('HEADER_ACTION', '运作');


// Cancel Customer Order
define('BUTTON_CANCEL_ORDER_NO', '否定');
define('BUTTON_CANCEL_ORDER_YES', '确定');
define('BUTTON_CANCEL_ORDER_OK', '确定');

define('HEADING_TITLE_CANCEL_ORDER', '订单取消请求');

define('TITLE_CANCEL_ORDER_NUMBER', '订单号码:');
define('TITLE_CANCEL_ORDER_PAYMENT_METHOD', '付款方式:');
define('TITLE_CANCEL_ORDER_AMOUNT', '总额:');

define('TEXT_DISCOUNT_COUPON', '折扣券');

define('MESSAGE_CANCEL_ORDER_CONFIRM', '您确定要取消此订单?');
define('MESSAGE_CANCEL_ORDER_CANCELLED_1', '订单已取消。');
define('MESSAGE_CANCEL_ORDER_CANCELLED_2', '订单已取消。款项会在24小时内以购物代金券的形式退还至您的OffGamers账户。');
define('MESSAGE_CANCEL_ORDER_CANCELLED_3', '此订单正等待取消中。如果您的订单款项（全部/部分）是以购物代金券支付的，请联系我们的客服平台，以加速订单的取消。');
define('MESSAGE_UNAUTHORISED_CANCEL_ORDER', '此订单不属于您，您无法取消该订单。');
?>