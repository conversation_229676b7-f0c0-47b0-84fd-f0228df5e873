<?
/*
  	$Id: account_payment_edit.php,v 1.2 2007/03/06 06:23:17 weichen Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2003 osCommerce
	
  	Released under the GNU General Public License
*/
define('NAVBAR_TITLE_1', BOX_HEADING_LOGIN_BOX_MY_ACCOUNT);
define('NAVBAR_TITLE_2', LOGIN_BOX_ACCOUNT_PAYMENT_EDIT);

if ($action == 'add_pm') {
	define('HEADING_TITLE', '新增付款账户');
} else if ($action == 'edit_pm') {
	define('HEADING_TITLE', '编辑付款账户');
} else {
	define('HEADING_TITLE', LOGIN_BOX_ACCOUNT_PAYMENT_EDIT);
}

define('TABLE_HEADING_PM_PAYMENT_ACCOUNT', '支付账户');
define('TEXT_CONFIRM_DELETE', ' 您确定删除');
define('TEXT_CONFIRM_DELETE_RECORD', ' 记录吗?');

define('TEXT_BUYBACK_LINK', '出售您游戏中的货币');   
define('TEXT_ANNOUNCEMENT', '<span style="color: red">*重要通知*</span> 2010年10月27日起，各位供货商的PayPal (in USD)货款支付方式将自动修订为新版PayPal (in USD)支付方式。 Paypal (in USD) 新版账户提款时比旧版支付手续费的更少。欲知更多信息，请点击<a href="my_payment_history.php?action=show_balance#paypal">这里</a>查看。');

//For payment module------------------------------------------------------------
define('TEXT_ALL_PAGES', '所有页面');  
define('HEADER_FORM_ACC_STAT_TITLE', '账户说明');  
define('ENTRY_ORDER_START_DATE', '登陆时间<br><small>(YYYY-MM-DD)</small>');  
define('ENTRY_ORDER_END_DATE', '退出时间<br><small>(YYYY-MM-DD)</small>');    
define('ENTRY_PAYMENT_ID', '付款编号');  
define('ENTRY_ORDER_ID', '订单地址'); 
define('ENTRY_RECORDS_PER_PAGE', '纪录每页');  
define('ENTRY_PAYMENT_STATUS', '付款情况');    
define('TEXT_LANG_RECORDS_PER_PAGE', '');
define('TABLE_HEADING_ACC_STAT_DATETIME', '日期/时间');
define('TABLE_HEADING_ACC_STAT_ACTIVITY', '活动');   
define('TABLE_HEADING_ACC_STAT_PAYMENT_STATUS', '付款情况');
define('TABLE_HEADING_ACC_STAT_DEBIT', '借记');  
define('TABLE_HEADING_ACC_STAT_CREDIT', '信用度');  
define('TABLE_HEADING_ACC_STAT_BALANCE', '剩余');
define('TEXT_ACC_STAT_NOT_APPLICABLE', '-');
define('TEXT_ACC_STAT_PAYMENT_STATUS_UPDATE_DATE', '%s on %s');
define('TEXT_ACC_STAT_ADMIN_COMMENT', '评论:');
define('SECTION_TITLE_FORM_PM_REQUIRED_INFO', '必填信息:');
define('TEXT_PM_NO_EXISTING_PAYMENT_ACCOUNT', '您有0付款的账户纪录.<br><br>如果您打算出售给offgamers ，请点击添加付款账户设置一个新的付款账户.');
define('LINK_PM_EDIT_PM_ACCOUNT', '编辑付款账户');
define('LINK_PM_DELETE_PM_ACCOUNT', '删除付款账户');
define('SUCCESS_PM_DELETE_PM_ACCOUNT', '成功：付款账户已成功删除.');
define('ERROR_INVALID_PM_BOOK_SELECTION', '错误：您正在编辑无效付款账户.');
define('ERROR_INVALID_PM_SELECTION', '错误：请选择有效的付款方法.');
define('ERROR_EMPTY_PM_ALIAS', '错误：请输入账户名称.');
define('ERROR_PM_FIELD_INFO_REQUIRED', "错误：请填写 %s 信息!");
define('ERROR_PM_ACCOUNT_BOOK_FULL', '您的付款账户，本书是充分. 请删除不必要的账户，以节省一个新的.');
define('ENTRY_FORM_PM_SELECT_CURRENCY', '付款账户货币币种:');
define('ENTRY_FORM_PM_SELECT_PM', '付款账户:');
define('ENTRY_FORM_PM_ALIAS', '账户名称:');

// Edit Payment Account Information
define('TABLE_HEADING_PAYMENT_ACCOUNT', '汇款账户');
define('TABLE_HEADING_PAYMENT_INFO', '详细账户信息');
define('TABLE_HEADING_PAYMENT_ACTION', '行动');
define('TEXT_PM_REACHED_MAX', '您的账户中已有%d种支付方式，无法再添加新的支付方式。<br>请删除已创建的支付方式后再添加。');
//--end for payment module------------------------------------------ -------------- 
?>