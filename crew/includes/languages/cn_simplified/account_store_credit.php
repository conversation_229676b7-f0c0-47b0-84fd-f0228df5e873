<?
/*
  	$Id: account_store_credit.php,v 1.0 2008/12/15 11:56:38 wilson.sun Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2003 osCommerce
	
  	Released under the GNU General Public License
*/
define('NAVBAR_TITLE_1', BOX_HEADING_LOGIN_BOX_MY_ACCOUNT);
define('NAVBAR_TITLE_2', LOGIN_BOX_ACCOUNT_STORE_CREDIT);

define('NAVBAR_TITLE', HEADER_TITLE_MY_ACCOUNT);
define('HEADING_TITLE', LOGIN_BOX_ACCOUNT_STORE_CREDIT);

define('TABLE_HEADING_STORE_CREDIT_CONVERSION', '购物代金券转换');
define('TABLE_HEADING_CONVERSION_TEXT', '如果您购买的商品是以非%s结算的，请先用此页面将“购物代金券”币种转化为商品页面所显示的币种。');
define('TEXT_CONFIRM_CONVERT', '您确定要执行转换?');
define('TEXT_CONVERT_MESSAGE', '目前您的OffGamers账户内的购物代金券余额为%s，如果转换成%s，您的余额则为%s。汇率为%f。');

//For payment module------------------------------------------------------------
define('SUCCESS_STORE_CREDIT_CONVERSION', '成功: 购物代金券余额已成功转换.');
define('ERROR_INVALID_STORE_CREDIT_CURRENCY_SELECTION', '错误：请选择正确的货币种类');
define('ERROR_INVALID_STORE_CREDIT_CURRENCY', '抱歉，OffGamers已不再支持您所选的购物代金券货币币种。<br><br>请发电子邮件至<a href="mailto:<EMAIL>"><EMAIL></a>，提交新的首选默认货币币种。');
define('ERROR_NO_CONVERSION_ALLOW_PENDING_PAYMENTS_FOUND', '抱歉，您有一项等待支出的付款申请，所以暂时无法转换您的购物代金券币种。请于24小时后重试。');
define('ERROR_NO_CONVERSION_ALLOW_GST', '抱歉，您无法转换您的购物代金券币种。');
define('ERROR_SC_CONVERSION', '抱歉，您无法转换您的购物代金券币种。请发电子邮件至<a href="mailto:<EMAIL>"><EMAIL></a>。');

define('ENTRY_FORM_MY_STORE_CREDIT', '我的购物代金券:');
define('ENTRY_FORM_CONVERT_TO', '将货币转换为:');
define('ENTRY_FORM_EXCHANGE_RATE', '汇率:');
define('ENTRY_FORM_NEW_STORE_CREDIT', '新的购物代金券:');
define('ENTRY_FORM_ATTENTION', '注意:');

define('TABLE_HEADING_SC_CONVERSION_CURRENCY_CONVERSION_HISTORY', '货币转换历史记录');
define('TABLE_HEADING_SC_CONVERSION_MY_SC', '我的购物代金券');
define('TABLE_HEADING_SC_CONVERSION_RATE', '转换率');
define('TABLE_HEADING_SC_CONVERSION_CONVERTED', '已转换');
//--end for payment module--------------------------------------------------------

define('QUESTION_AND_ANSWER_HEADER', '问与答');
define('QUESTION_AND_ANSWER_CONTENTS', '<b>如何购买购物代金券?</b><br><br>

点击 <a href="http://kb.offgamers.com/zhcn/?p=119" target="_blank">这里</a> 了解如何购买购物代金券。');
?>