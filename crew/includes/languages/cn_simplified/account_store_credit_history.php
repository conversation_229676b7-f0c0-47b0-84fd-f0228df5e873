﻿<?
/*
  	$Id: account_store_credit.php,v 1.0 2008/12/15 11:56:38 wilson.sun Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2003 osCommerce
	
  	Released under the GNU General Public License
*/
define('NAVBAR_TITLE_1', BOX_HEADING_LOGIN_BOX_MY_ACCOUNT);
define('NAVBAR_TITLE_2', LOGIN_BOX_ACCOUNT_STORE_CREDIT);

define('NAVBAR_TITLE', HEADER_TITLE_MY_ACCOUNT);
define('HEADING_TITLE', LOGIN_BOX_ACCOUNT_STORE_CREDIT);

define('TABLE_HEADING_STORE_CREDIT_HISTORY', '购物代金券历史记录');

define('TAB_HEADING_HISTORY', '历史记录');
define('TAB_HEADING_STORE_CREDIT_PURCHASED', '所购买的购物代金券');
define('TAB_HEADING_STORE_CREDIT_USED', '所消费的购物代金券');

define('TABLE_HEADING_HISTORY_DATE', '日期');
define('TABLE_HEADING_HISTORY_TYPE', '类型');
define('TABLE_HEADING_HISTORY_AMOUNT', '金额');
define('TABLE_HEADING_HISTORY_BALANCE', '余额');

define('TABLE_HEADING_SC_PURCHASED_DATE', '日期');
define('TABLE_HEADING_SC_PURCHASED_ORDER', '订单号');
define('TABLE_HEADING_SC_PURCHASED_AMOUNT', '金额');

define('TABLE_HEADING_SC_USED_DATE', '日期');
define('TABLE_HEADING_SC_USED_ORDER', '订单号');
define('TABLE_HEADING_SC_USED_AMOUNT', '金额');

define('TEXT_SC_HISTORY_STATUS_MI', '手动添加');
define('TEXT_SC_HISTORY_STATUS_MR', '手动扣除');
define('TEXT_SC_HISTORY_STATUS_PW', 'OffGamers付款');
define('TEXT_SC_HISTORY_STATUS_R', '退款');
define('TEXT_SC_HISTORY_STATUS_V', '购物代金券转换');
define('TEXT_SC_HISTORY_STATUS_X', '取消');
define('TEXT_SC_HISTORY_STATUS_D', 'OP兑换');
define('TEXT_SC_HISTORY_STATUS_S', '购物代金券已发数量');
define('TEXT_SC_HISTORY_STATUS_XS', '购物代金券奖励数量(+%s%%)');
define('TEXT_SC_HISTORY_STATUS_C', '赔偿');
define('TEXT_SC_HISTORY_STATUS_P', '已消费的购物代金券');
define('TEXT_SC_HISTORY_STATUS_GC', '礼品卡兑换');

define('TEXT_SC_HISTORY_STATUS_S_DEDUCT', '购物代金券扣除额（无效交易）');
define('TEXT_SC_HISTORY_STATUS_XS_DEDUCT', '购物代金券奖励扣除额（无效交易）');

define('TEXT_MY_STORE_CREDIT_BALANCE', '我的购物代金券余额: <b>%s</b>');
define('TEXT_TOP_UP_STORE_CREDITS', '充值购物代金券');
define('TEXT_OR_CONVERT_NOW', '或立刻转换');
?>