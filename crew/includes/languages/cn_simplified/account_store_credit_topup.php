<?
/*
  	$Id: account_store_credit_topup.php,v 1.4 2010/01/06 10:53:06 wilson.sun Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2003 osCommerce
	
  	Released under the GNU General Public License
*/
define('NAVBAR_TITLE_1', BOX_HEADING_LOGIN_BOX_MY_ACCOUNT);
define('NAVBAR_TITLE_2', LOGIN_BOX_ACCOUNT_STORE_CREDIT);

define('NAVBAR_TITLE', HEADER_TITLE_MY_ACCOUNT);
define('HEADING_TITLE', LOGIN_BOX_ACCOUNT_STORE_CREDIT);

define('TABLE_HEADING_STORE_CREDIT_TOPUP', '充值购物代金券');
define('TABLE_HEADING_STORE_CREDIT_TOPUP_TEXT', '在OffGamers购物结账时您可使用购物代金券来代替现金或信用卡付款。如果您账户内有现成的购物代金券，无论您是购物还是预定产品，一切都更加便捷。');
define('TABLE_HEADING_STORE_CREDIT_TOPUP_NEWS_TEXT', '我们很高兴地通知大家，我们优化了服务器功能以便大家在 <a href="' . tep_href_link(FILENAME_ACCOUNT_STORE_CREDIT_TOPUP, '', 'SSL') . '">OffGamers.com</a>购买购物代金券时可以达到快速结账。当您成功完成交易后，购物代金券将会立即打到您的账户中。');
define('TABLE_HEADING_STORE_CREDIT_TOPUP_PROMOTION_TEXT', '凡购买购物代金券即可额外获得%f%！');
define('TEXT_MINIMUM_STORE_CREDITS_TOPUP', '购物代金券最小购买金额%s: %s');

define('ENTRY_FORM_BUY_TEXT', '我想买 <b>%s</b> ');
define('ENTRY_FORM_STORE_CREDITS_TEXT', '<b>%s</b> 购物代金券');

define('QUESTION_AND_ANSWER_HEADER', '问与答');

define('QUESTION_AND_ANSWER_CONTENTS', '<table width="100%"><tr><td width="35px" style="text-align:left;vertical-align: top;"><div style="padding:0px 10px;text-align:center;">'.tep_image(DIR_WS_ICONS . 'note.gif', ICON_SUCCESS).'</div></td><td width="*%" align="left">请注意，购物代金券一经售出，概不退款。</td></tr></table><br><b>如何购买购物代金券?</b><br><br>

点击 <a href="http://kb.offgamers.com/zhcn/?p=119" target="_blank">这里</a> 了解如何购买购物代金券。');

define('TEXT_INFO_MINIMUM_ORDERS_SC','您输入的购物代金券金额无效。');
define('TEXT_INFO_EXTRA_SC_PROMO','凡购买购物代金券即可<font style=\'font-family: Arial, Verdana, Helvetica, sans-serif;font-size: 15px;font-weight: bold;text-transform: normal;display: inline;color: #004B91;text-decoration: none;\'>额外获得%s%%</font>!');
define('TEXT_INFO_ENTERED_SC_PROMO_AMOUNT1','价格：');
define('TEXT_INFO_ENTERED_SC_PROMO_AMOUNT2','购买');
define('TEXT_INFO_ENTERED_SC_PROMO_AMOUNT3','购物代金券');

define('TEXT_LAST_DAY_ENJOY_EXTRA_SC','2013年5月26日将是享受5％购物代金券奖励的最后一天。<a href=http://www.offgamers.com/extra-5-store-credits-ln-4812.ogm" rel="_nofollow" target="_blank">更多信息请点击</a>');
?>