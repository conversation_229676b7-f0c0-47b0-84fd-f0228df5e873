<?php
/*
  $Id: address_book_process.php,v 1.3 2005/06/09 09:22:51 weichen Exp $

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2003 osCommerce

  Released under the GNU General Public License
*/

define('NAVBAR_TITLE_1', '我的帐户');
define('NAVBAR_TITLE_2', '通讯录');

define('NAVBAR_TITLE_ADD_ENTRY', '新登陆地址');
define('NAVBAR_TITLE_MODIFY_ENTRY', '更改登陆名称');
define('NAVBAR_TITLE_DELETE_ENTRY', '删除登陆地址');

define('HEADING_TITLE', '通讯录');
define('HEADING_TITLE_ADD_ENTRY', '新通讯录');
define('HEADING_TITLE_MODIFY_ENTRY', '更新通讯录');
define('HEADING_TITLE_DELETE_ENTRY', '删除通讯录');

define('DELETE_ADDRESS_TITLE', '删除地址');
define('DELETE_ADDRESS_DESCRIPTION', '确认删除？');
define('NEW_ADDRESS_TITLE', '新通讯录');

define('SELECTED_ADDRESS', '确认地址');
define('SET_AS_PRIMARY', '定为常用地址. (姓名和性别，将默认帐户设置)');

define('SUCCESS_ADDRESS_BOOK_ENTRY_DELETED', '删除成功.');
define('SUCCESS_ADDRESS_BOOK_ENTRY_UPDATED', '成功更新地址簿.');

define('WARNING_PRIMARY_ADDRESS_DELETION', '主地址不能被删除. 请设置另一个地址作为主地址，然后再试一次.');

define('ERROR_NONEXISTING_ADDRESS_BOOK_ENTRY', ' 通讯地址不存在.');
define('ERROR_ADDRESS_BOOK_FULL', '通讯簿已满，请删除部分地址.');

?>