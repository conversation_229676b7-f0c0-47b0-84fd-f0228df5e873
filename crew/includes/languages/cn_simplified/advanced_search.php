<?php
/*
  $Id: advanced_search.php,v 1.3 2004/11/30 10:07:42 subrat Exp $
*/

define('NAVBAR_TITLE_1', '高级搜索'); 
define('NAVBAR_TITLE_2', '搜索结果'); 

define('HEADING_TITLE', '高级搜索');   
define('HEADING_TITLE_1', '高级搜寻');
define('HEADING_TITLE_2', '符合搜索条件');  

define('HEADING_SEARCH_CRITERIA', '关键词');  

define('TEXT_SEARCH_IN_DESCRIPTION', '在产品说明中搜索');  
define('ENTRY_CATEGORIES', '类别:');
define('ENTRY_INCLUDE_SUBCATEGORIES', '包含类别');
define('ENTRY_MANUFACTURERS', '制造商:');  
define('ENTRY_PRICE_FROM', '价格:');  
define('ENTRY_PRICE_TO', '价格:');
define('ENTRY_DATE_FROM', '开始日期:');  
define('ENTRY_DATE_TO', '截止日期:');     
define('ENTRY_SORT_BY', '排序:');   
define('ENTRY_ITEM_PER_PAGE', '每页项目:');
define('ENTRY_ASCENDING', '升序');  
define('ENTRY_DESCENDING', '降序');   

define('TEXT_SEARCH_HELP_LINK', '帮助 [?]'); 
//define('TEXT_ALL_CATEGORIES', '所有类别');  '
define('TEXT_ALL_MANUFACTURERS', '所有制造商');  

define('HEADING_SEARCH_HELP', '帮助搜索');   

define('TEXT_SEARCH_HELP', '输入的关键词可能是分开的，为更有精确搜寻结果。<br><br>例如：<u>微软和滑鼠</u>搜寻结果可能出现以上两种产品，不过，<u>滑鼠和键盘</u>搜寻结果可能是两种或一种产品。相关度越高的说明，检索出来的内容和关键词越相近。<br><br>为完全匹配，可使用双引号。<br><br>举例来说，<u>"笔记本电脑"</u>将会出现更精确搜寻结果。<br><br>括号内可用为进一步影响搜寻结果。<br><br>例如，<u>微软和（键盘或滑鼠或“编程语言”）</u>.');
define('TEXT_CLOSE_WINDOW', '关闭窗口 [x]');
define('TEXT_ITEM_ADDED_TO_CART', '该产品已添加到您的购物车 <a href="shopping_cart.php">购物车.</a>');
                                                       
define('TABLE_HEADING_IMAGE', '');
define('TABLE_HEADING_MODEL', '模型'); 
define('TABLE_HEADING_PRODUCTS', '产品名称');  
define('TABLE_HEADING_MANUFACTURER', '运营商');  
define('TABLE_HEADING_QUANTITY', '数量');    
define('TABLE_HEADING_PRICE', '价格');   
define('TABLE_HEADING_WEIGHT', '净重');  
define('TABLE_HEADING_BUY_NOW', '立即购买');  
define('TABLE_HEADING_PRODUCT_SORT', '按产品类别排序');  

define('TEXT_NO_PRODUCTS', '未搜索到相关内容.');  

define('ERROR_AT_LEAST_ONE_INPUT', ' 在搜索表单中至少输入一个内容.'); 
define('ERROR_INVALID_FROM_DATE', '无效日期自    起.');  
define('ERROR_INVALID_TO_DATE', '无效日期到     止.');
define('ERROR_TO_DATE_LESS_THAN_FROM_DATE', '截止日期必须大于开始日期.');  
define('ERROR_PRICE_FROM_MUST_BE_NUM', '最低价格.');
define('ERROR_PRICE_TO_MUST_BE_NUM', '最高价格.');
define('ERROR_PRICE_TO_LESS_THAN_PRICE_FROM', '最高价格必须大于最低价格.'); 
define('ERROR_INVALID_KEYWORDS', '无效的关键词.');
?>
