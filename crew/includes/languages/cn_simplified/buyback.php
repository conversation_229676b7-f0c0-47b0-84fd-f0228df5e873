<?php

/*
  $Id: buyback.php,v 1.27 2007/12/19 10:59:46 weichen Exp $

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2003 osCommerce

  Released under the GNU General Public License
 */
define('NAVBAR_TITLE_1', '出售金币/游戏币');
define('NAVBAR_TITLE', '出售金币/游戏币');
define('HEADING_TITLE', '出售金币/游戏币');

define('HEADER_MY_SELLING_CART', "我的销售购物车");
define('HEADER_MY_VIP_SELLING_CART', "我的VIP销售购物车");

define('TEXT_HEADING_SERVER', '服务器名称');
define('TEXT_SELL_THIS_LINK', '下单');
define('TEXT_ADD_TO_FAVOURITES_LINK', '加入我的收藏');
define('TEXT_MIN', '最低限值.');
define('TEXT_MAX', '最高限值.');
define('TABLE_HEADING_STEP_1', '1.选择游戏和服务器');
define('TABLE_HEADING_STEP_2', '2. 销售金额 :');
define('TABLE_HEADING_STEP_3', '3. 评价我们提供的商品 :');
define('TABLE_HEADING_SUBMITTED_INFO', '提交资料');
define('TABLE_HEADING_SELLER_INFO', '卖方信息');

define('ENTRY_CHARACTER_NAME', '角色名称');
define('ENTRY_DELIVERY_TIME', '交货时间');
define('ENTRY_NAME', '姓名');
define('ENTRY_CONTACT_NO', '联系电话');
define('ENTRY_ADDITIONAL_COMMENTS', '其他评论');
define('ENTRY_SELECT_GAME', '选择游戏');
define('ENTRY_SELECT_SERVER', '选择服务器');
define('ENTRY_ENTER_SELLING_AMOUNT', '销售金额');
define('ENTRY_DEALING_TYPE', '交易类型');

define('TEXT_DEALING_TYPE_ON_GAME', '面对面');
define('TEXT_DEALING_TYPE_ON_MAIL', '邮寄');

define('TEXT_MATCH_BACKORDER_EXACT_AMOUNT', '(如果提交以上%d个,我们只接受第%d,我们不接受任何数量的范围内%d - %d个)');
define('TEXT_MEETING_POINT', '汇点');
define('TEXT_CHARACTER_NAME_HELP', '<span class="redIndicator">* 请提供您的角色名称。</span>');
define('TEXT_DELIVERY_TIME_HELP', '<span class="redIndicator">* 超过交易时间此命令将会被取消。</span>');
define('TEXT_BUYBACK_CONTACT_NO_HELP', '<span class="redIndicator">* 请提供一个有效的联络电话，以便安排交货的游戏。</span>');
define('TEXT_ORDER_POSTED_INSTRUCTIONS', '您的销售订单已成功提交。现在正在交易。');
define('TEXT_OF', '的');
define('TEXT_NOTES', '债券');
define('TEXT_TOTAL_PRICE', '总价格');
define('TEXT_ADD_SHORTCUT_TO', '添加服务器的捷径 :');
define('TEXT_ADDITIONAL_COMMENTS', '其他评论:');
define('TEXT_TERMS_AND_CONDITIONS', '合同条款');
define('TEXT_NO_ITEMS_TO_BUYBACK', '没有回收项目.');
define('TEXT_GAME', '游戏: ');
define('TEXT_SERVER', '服务器: ');
define('TEXT_MIN', '最少');
define('TEXT_MAX', '最多');
define('TEXT_PRICE_PER_UNIT', '单价');
define('TEXT_PRODUCT', '产品名称');
define('TEXT_FOR_YOUR', '您的');
define('TEXT_ON_SERVER', '在服务器上');
define('TEXT_BUTTON_LABEL_STEP2', '下一步');
define('TEXT_BUTTON_LABEL_STEP3', '我接受');
define('TEXT_BUTTON_LABEL_UPDATE', '更新');
define('TEXT_SELECT_YOUR_GAME', '-- 选择您的游戏--');
define('TEXT_SELECT_YOUR_SERVER', '-- 选择您的服务器--');
define('ERROR_BUYBACK_QUANTITY_INVALID', "您输入的金额不正确. <br/>请输入限额内金额。");
define('ERROR_QUANTITY_INVALID', "输入错误数量");
define('TEXT_FORM_INCOMPLETE', "你没有进入所有必填字段.<br/>Please try again。");
define('TEXT_FORM_AGREEMENT_ALERT', '您必须接收协议才能继续进行');
define('WARNING_BUYBACK_FORM_STILL_PROCESSING', '此服务器仍在处理您上次的回购，<br /> 请尝试不同的服务器。');
define('TEXT_MAIN', '请选择您要出售的服务器和项目名称.');
define('TEXT_NOT_ACCEPT_BUYBACK', " 很抱歉，我们目前不接受回收服务器，.<br /> 请在24小时内再检查一次，或者发送电子邮件到…… <a href=\"mailto:<EMAIL>\"><EMAIL></a> 查看详细步骤。");
define('TEXT_EMAIL_IF_MORE_SUPPLY', '如果您能提供的金币数大于网站采购量，或您能长期稳定为我们提供金币货源，请发邮件至 <a href="mailto:<EMAIL>"><EMAIL></a>');
define('TEXT_NO_PAYMENT_ACCOUNT', "请申请一个支付账户，然后才进行您的回购秩序。");
define('TEXT_BUYBACK_IS_OFF', '我们已暂时关闭回购网站,请在24小时内再检查一次,或发送邮件到<a href="mailto:<EMAIL>"><EMAIL></a>查看进一步的细节.点击<a href="http://www.offgamers.com">这儿</a>返回到主网页。');
define('TEXT_AGREEMENT_ACCEPTANCE', '我已阅读并接收该协议.');
define('TEXT_CHAR_DISCONNECTED', '因不可预见的情况，游戏已断开，请等待3分钟解决后会移交给您，请您耐心等待');

define('BUYBACK_FORM_FOOTER_MESSAGE', '请从游戏列表中选择游戏，并从服务器名单中选择您的服务器，为了符合客户的要求，我们不断更新数据库库存服务器的游戏,您可以提供黄金或其他任何游戏货币给我们，并获得即时付款，关于合作秩序，我们的工作人员将会帮助您；同时,我们也回购游戏帐户,将您的账户出售给我们，价格不会让您失望,出售MMO游戏帐户,我们提供的价格超出了您的期望.<a href="http://www.offgamers.com/account_buyback.php">在这里出售帐户</a>。<br><br>我们不支持或购买通过不明方法/来源获得的游戏货币(外挂， dupes ，黑客攻击等)。');

define('ERROR_BUYBACK_SENDER_ID_EXISTS', '发送角色账号已存在，请检查您的发件人的角色账');
define('ERROR_BUYBACK_NOT_BUYBACK_GAME', '由于时间的原因我们目前无法接纳任何回购，请稍后再试.');
define('ERROR_MANDATORY_ISBLANK', '领域不完整: %s');
define('ERROR_SUBMITTED_BY_OTHER_USER', '这单子已被其他客户提交。请重试。');
define('ERROR_BUYBACK_QTY_NOT_EXIST', '回购数量不正确。请重试。');
define('ERROR_BUYBACK_ORDER_CLOSED', '对不起! 此单已关闭，请再次刷最新可以提交的服务器。');

define('TEXT_CHARACTER_NAME', '角色名称');
define('TEXT_DEALING_TYPE', '交易类型');
define('TEXT_DELIVERY_TIME', '交货时间');
define('TEXT_NAME', '名');
define('TEXT_TERMS_AND_CONDITIONS_AGREEMENT', '协议条款');

define('ENTRY_GAME_SELECTION', '游戏');
define('ENTRY_SERVER_SELECTION', '服务器');
define('ENTRY_MEETING_POINT', '交易地点');
define('ENTRY_TRADE_METHOD', '成交类型');
define('ENTRY_SELLING_QTY', '数量');
define('ENTRY_TOTAL_SELLING_PRICE', '总额');
define('ENTRY_CHARACTER_ID', '角色名');
define('ENTRY_CONTACT', '联络号码');
define('ENTRY_COMMENTS', '附加评论');

define('EMAIL_SEPARATOR', '------------------------------------------------------');
define('EMAIL_NEW_BUYBACK_SUBJECT', implode(' ', array(EMAIL_SUBJECT_PREFIX, "新的回购订单＃ %s")));
define('EMAIL_NEW_BUYBACK_BODY', "感谢您出售您的项目 " . STORE_NAME . ".\n\n 供应商的订单摘要:\n" . EMAIL_SEPARATOR . "\n");
define('EMAIL_NEW_BUYBACK_PRODUCTS', "\n\n" . '产品名称');
define('EMAIL_NEW_BUYBACK_ORDER_TOTAL', '所有产品: %s');
define('EMAIL_NEW_BUYBACK_COMMENTS', '其他信息:');
define('EMAIL_NEW_BUYBACK_ORDER_CLOSING', "通过我们的在线服务或电子邮件于我们联系，或电邮 " . EMAIL_TO . " 邮件内容包括安排的时间收集所有的股票回购项目。请记住,包括您的回购订单号.\n\n您将收到一封电子邮件通知每当有一个更新在您的回购订单状态。您也可以检查您的回购订单，并检查他们的身份，签署了在以offgamers.com 并点击 \"查看回购订单历史\"感谢您的支持 " . STORE_NAME . '。');
define('EMAIL_FOOTER', "\n\n" . STORE_EMAIL_SIGNATURE);
define('EMAIL_NEW_BUYBACK_ORDER_FOOTER', "\n\n" . EMAIL_FOOTER);



define('TEXT_NOTICE_MSG', '备注：请确认你接收的ID,小心被诈骗');
define('TEXT_HEADING_STEP1', '步骤 1 : 指定服务器和数量');
define('TEXT_HEADING_STEP2', '步骤 2 : 指定个人资料');
define('TEXT_HEADING_STEP3', '步骤 3 : 确认');

define('TEXT_HEADING_SERVER_LIST', '服务器目录');
define('TEXT_HEADING_UNIT_PRICE', '单价');
define('TABLE_HEADING_PURCHASE_QTY', '收购量');
define('TEXT_HEADING_STATUS', '状态');

define('TEXT_HEADING_SUMMARY', '订单总结');
define('TEXT_HEADING_CHARACTER_INFO', '角色名');
define('TEXT_HEADING_CONTACT_INFO', '联络资料');
define('TEXT_HEADING_PAYMENT_INFO', '付款资料');

define('TEXT_SELECT_LINK', '下单');
define('TEXT_OR', '或');

define('TEXT_UNIT_PRICE', '单价 : ');
define('TEXT_MEETING_POINT_INFO', '<span class="redIndicator">联盟：闪金镇（GoldShire）// 部落：剃刀岭（Razor Hill）</span>');
define('TEXT_ADD_TO_MANUFACTURER_LIST', '包收单子');
define('TEXT_INSTRUCTIONS_HEADER1', '收货');
define('TEXT_INSTRUCTIONS_LEFT', '请填写一下地细节和进行下一步');

define('TEXT_CONTACT_NAME', '名字');

define('TEXT_PAY_TO_ACCOUNT', '受益人');
define('TEXT_PAY_TO_ACCOUNT_HELP', '请填入你的数量，才能付款给你');
define('TEXT_ORDER_REFERENCE', ' 参考单子');

define('TEXT_ADD_NEW', '继续下单');
define('TEXT_DELIVER_NOW', '现在交易');

define('TEXT_MANDATORY_ISBLANK', '还没完整填入: %s');
define('TEXT_FORM_MAX_ORDERS_REACHED', '你的订单已经达到最高的数量，请稍等那些单子完成了， 再提交新的单');
define('TEXT_INCOMPLETE_FIELDS', '还没完整填入， 请再尝试');
define('TEXT_INVALID_SEVER_SELECTION', '没有此游戏/服务器，请再尝试');
define('TEXT_QTY_DELIVERED', '发货数量');
define('TEXT_DELIVERED_ID', '发货ID');

define('BUTTON_TRADE_NOW', '现在交易');

define('TITLE_WHY_SELL_TO_US', "为何出货给我们?");
define('TEXT_WHY_SELL_TO_US', "1) 快速安全的交易渠道. <br/><br/> 2) 实时更新收货列表. <br/><br/> 3) 为您提供真诚的买家. <br/><br/> 4) 付款争议零风险.<br/><br/>");

if (!defined('ORDERS_LOG_UNLOCK_OWN_ORDER'))
    define('ORDERS_LOG_UNLOCK_OWN_ORDER', '##ul_1##');

define('WARNING_PLS_SELECT_GAME', "请选择游戏");
define('WARNING_PLS_SELECT_SERVER', "请选择服务器");
define('WARNING_PLS_FILL_IN_SELLING_QTY', "请填入数量");
define('WARNING_PLS_FILL_IN_CHAR_ID', "请填入名字");
define('WARNING_PLS_FILL_IN_CONTACT', "请填入联络号码");
define('WARNING_PLS_CHECK_EMAIL_VERIFIED_AND_IM', "注意！<br/>请检查您账户中的邮箱地址、即时联络方式（QQ、MSN等）、移动或座机号码是否都已正确填写，如有缺失则无法完成下单和完单流程.");
define('WARNING_VERIFY_EMAIL_LINK', "<a href='%s'>电子邮址验证</a>");
define('WARNING_VERIFY_PHONE_LINK', "<a href='%s'>电话号码验证</a>");
define('WARNING_FILL_IN_IM', "<a href='#' id='account_edit_link'>填写即时联络方式</a>");

define('LINK_SELL_YOUR_GAME_CURRENCY_PROCEDURE', 'Sell your game currency procedure');
define('LINK_SELLER_GUIDE', 'Seller Guide');
define('LINK_TERMS_N_CONDITIONS', 'Terms & Conditions');
define('LINK_WOW_SELLER_GUIDE', 'WOW - Seller Guide');

define('LINK_READ_MORE', '更多');

define('TEXT_PRICE_NOTES', '我们的买入价是以美金单位为准，其它货币单位均为参考价。每天的早上08:30-08:45我们会更新本日的美金汇率，也就是说在08:45之后提现的会按照当日更新的汇率折算;08:30之前的提款都是按照前一日汇率进行结算。');

define('TEXT_PRICE_NOTES_2', '注意：我们将以美元支付您的款项');
?>