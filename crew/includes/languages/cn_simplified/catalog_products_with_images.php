<?php
/*
  $Id: catalog_products_with_images.php V 3.0
  by <PERSON> <<EMAIL>> V 3.0

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2002 osCommerce

  Released under the GNU General Public License
*/

define('TOP_BAR_TITLE', '打印目录'); 
//define('HEADING_TITLE', '<table border="0" width="100%" cellspacing="3" cellpadding="3"><tr><td width="30%" class="pageHeading"><h5>' . STORE_NAME_ADDRESS . '</h5></td><td width="70%"><h6><u>注意</U>:<br> 所有价格如有变更恕不另行通知. 所有产品将以加拿大货币结算. 货币的功能，只是为了您的方便. 此目录是电流对日

define('HEADING_TITLE', '<h6>'.STORE_NAME.'<br>'.nl2br(STORE_NAME_ADDRESS).'<br>Email: <a href="mailto:'.STORE_OWNER_EMAIL_ADDRESS.'">'.STORE_OWNER_EMAIL_ADDRESS.'</a><br>注：所有价格如有变更恕不另行通知. 所有产品将在我们的结算货币在我们的商店而已.<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;货币的功能，只是为了您的方便. 此目录是电流对日印.</h6>');

// comment the above line and uncomment out the line above it if you have an older OSC Release before Nov1 2002
define('TABLE_HEADING_IMAGES', '形象'); 
define('TABLE_HEADING_MANUFACTURERS', '制造商');  
define('TABLE_HEADING_PRODUCTS', '名称');  
define('TABLE_HEADING_DESCRIPTION', '简介'); 
define('TABLE_HEADING_OPTIONS','选项');  
define('TABLE_HEADING_CATEGORIES', '类别'); 
define('TABLE_HEADING_MODEL', '样式');  
define('TABLE_HEADING_UPC', 'UPC');
define('TABLE_HEADING_QUANTITY', '数量'); 
define('TABLE_HEADING_WEIGHT', '重量');  
define('TABLE_HEADING_PRICE', '价格');    
define('BOX_STATS_PRODUCTS_WITH_IMAGES', '打印目录');  
define('FONT_STYLE_TOP_BAR', '打印目录');  
define('NAVBAR_TITLE', '打印目录');      
define('TABLE_HEADING_DATE','加入日期');    
define('BOX_CATALOG_NEXT', '下一个'); 
define('BOX_CATALOG_PREV', '上一页');  
?>
