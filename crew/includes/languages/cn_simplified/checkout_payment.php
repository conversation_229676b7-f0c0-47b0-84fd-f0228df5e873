<?php
/*
  $Id: checkout_payment.php,v 1.16 2008/07/22 05:01:46 edwin.wang Exp $

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2002 osCommerce

  Released under the GNU General Public License
*/

define('NAVBAR_TITLE_1', '运送');  
define('NAVBAR_TITLE_2', '其他信息');  
define('NAVBAR_TITLE_3', '付款方式');  

define('HEADING_TITLE', '付款情况');   
define('HEADING_SUBTITLE', '请用%s进行支付');

define('TABLE_HEADING_AMOUNT', '总额');
define('TABLE_HEADING_QUANTITY', '数量.');
define('TABLE_HEADING_PRODUCT', '产品');

define('ENTRY_PAYMENT_SURCHARGE', '处理费');
define('ENTRY_PAYMENT_CONTACT_NUMBER', '联系电话：');
define('ENTRY_PAYMENT_CUSTOMER_IDENTIFY_NUMBER', '身份证号码：<BR>（最后4个号码）');
define('ENTRY_PAYMENT_CUSTOMER_SURNAME', 'Surname');
define('ENTRY_PAYMENT_CUSTOMER_HOUSENUMBER', 'House Number');
define('ENTRY_PAYMENT_CUSTOMER_STREET', 'Street');
define('ENTRY_PAYMENT_CUSTOMER_ZIP', 'Zip');
define('ENTRY_PAYMENT_CUSTOMER_CITY', 'City');
define('ENTRY_PAYMENT_CUSTOMER_ACCOUNTNAME', 'Account Name');
define('ENTRY_PAYMENT_CUSTOMER_ACCOUNTNUMBER', 'Account Number');
define('ENTRY_PAYMENT_CUSTOMER_DIRECTDEBITTEXT', 'Direct Debit Text');
define('ENTRY_PAYMENT_CUSTOMER_BANKCODE', 'Bank Code');
define('ENTRY_PAYMENT_CUSTOMER_BRANCHCODE', 'Branch Code');
define('ENTRY_PAYMENT_CUSTOMER_VOUCHER_NUMBER', 'Voucher Number');
define('ENTRY_PAYMENT_CUSTOMER_VOUCHER_VALUE', 'Voucher Value');

define('TEXT_INFO_IDENTIFY_NUMBER_EXAMPLE', '（123456-14-<font color="red">XXXX</font>）');
define('TEXT_INFO_CURRENT_CHECKOUT_CURRENCY', '当前结账货币');
define('TEXT_INFO_CHANGE_CHECKOUT_CURRENCY', '如要查看其它付款方式，请先更换结账货币。');

define('TEXT_ORDER_FULLY_COVERED_BY_CREDITS', '因为可以从您的商店信用中扣除总金额，所以您不必选择付款方式.');
define('TEXT_ORDER_ZERO_AMOUNT_CHECKOUT', '您不必选择付款方式.');
define('TEXT_EDIT_ADDRESS', '编辑地址？');
define('LINK_EDIT_TELEPHONE', '编辑联系电话？');
define('TEXT_FEATURED_PAYMENT_METHODS', '推荐付款方式');
define('TEXT_SHOPPING_CART', '购物栏 : '); 
define('TEXT_STORE_CREDIT', '商店存款: ');  
define('TEXT_TOTAL', '共计: ');   

define('LINK_EDIT_SHOPPING_CART', '编辑购物车');
define('LINK_UPDATE_CUSTOMER_PAYMENT_INFO', '确定');

define('JS_ERROR_TITLE_PAYMENT_INFORMATION', '付款资料错误');

define('TEXT_INFO_PAYMENT_SURCHARGE_DESC', '<b>处理费</b><br />请注意，处理费适用于特定付款方式的每一笔交易。 <a href="http://kb.offgamers.com/zhcn/?p=128" target="_blank">更多详情</a>');

define('TEXT_INFO_PAYMENT_IN_USD_OR', '或');
?>