<?php
/*
  $Id: checkout_success.php,v 1.11 2008/01/24 02:47:51 gan Exp $

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2002 osCommerce

  Released under the GNU General Public License
*/
define('NAVBAR_TITLE_1', '下单完成');
define('NAVBAR_TITLE_SHOPPING_CART', '购物车');
define('NAVBAR_TITLE_ORDER_PAYMENT', '订单款项 ');
define('NAVBAR_TITLE_2', '成功');
define('HEADING_TITLE', '完成');
define('HEADING_EMAIL_VERIFICATION_TITLE', '确认您的电子邮件');
define('HEADING_PHONE_VERIFICATION_TITLE', '确认您的手机号码.');

define('TEXT_SUCCESS', '您的订单已成功处理!');
define('TEXT_TITLE_CONFIRM_EMAIL', '代码将被发送到您的电子邮件地址，为了让我们能够处理您的订单. 您必须完成这一步.');

define('TEXT_HEADING_DELIVERY_INFO', '送货详情 :');
define('TEXT_CURRENCY_DELIVERY_INFO', '<b>游戏金币 : </b>请点击这里设置"<a href="%s">我在线</a>"');
define('TEXT_CDK_DELIVERY_INFO', '<b>CDK/点卡/游戏点数: </b>查看您的代码 从您的订单历史接到交付的电子邮件.');
define('TEXT_PWL_DELIVERY_INFO', '<b>代练服务 : </b>一旦我们收到您的订单，我们将通过电子邮件通知您');
define('ENTRY_CHECKOUT_SUCCESS_VERIFICATION_CODE', '确认码:');
define('TEXT_TITLE_ENTER_PHONE_NUMBER', '请确保以下手机号码是正确和有效的.');
define('TEXT_TITLE_ENTER_PHONE_VERIFICATION_CODE', '请输入发送到您电话中的4位数的验证码. 如果您5分钟内没有收到呼叫，请点击“报告错误”链接.');
define('TEXT_COUNTRY_NOT_SUPPORT', '(国家不支持我们的手机电话核查系统)');
define('ERROR_REPORT', '报告错误');
define('TEXT_PHONE_VERIFICATION_PHONE_TYPE_NOT_SUPPORTED', '输入手机号码类型是不支持');
define('CODE_MATCH_MESSAGE', '谢谢您，您已经成功验证您的手机号码.');
define('CODE_NOT_MATCH_RETRY_MESSAGE', '您输入确认码不正确. 请稍后再试.');
define('CODE_NOT_MATCH_MESSAGE', '您输入确认码不正确. 我们将会手动验证.');  

define('TABLE_HEADING_COMMENTS', '请为这处理进度输入评语');  

define('EDIT_TELEPHONE' , '(编辑)');

define('TABLE_HEADING_DOWNLOAD_DATE', '截止日期: ');  
define('TABLE_HEADING_DOWNLOAD_COUNT', ' 其余下载');   
define('HEADING_DOWNLOAD', '在这里下载您的产品:');    
define('FOOTER_DOWNLOAD', '在稍后的时间您也可以下载您的产品 \'%s\'');  
define('PAYPAL_NAVBAR_TITLE_2_OK', '成功'); // PAYPALIPN   
define('PAYPAL_NAVBAR_TITLE_2_PENDING', '您的订单正在处理中.'); // PAYPALIPN   
define('PAYPAL_NAVBAR_TITLE_2_FAILED', '付款失败'); // PAYPALIPN  （您的付款失败）  
define('PAYPAL_HEADING_TITLE_OK', '您的订单正在处理!'); // PAYPALIPN
define('PAYPAL_HEADING_TITLE_PENDING', '您的订单正在处理中!'); // PAYPALIPN   
define('PAYPAL_HEADING_TITLE_FAILED', '您的付款失败!'); // PAYPALIPN  
define('PAYPAL_TEXT_SUCCESS_OK', '您的订单已成功处理! 我们的产品将在2-5个工作日内到达目的地.'); // PAYPALIPN  
define('PAYPAL_TEXT_SUCCESS_PENDING', '您的订单正在处理中!'); // PAYPALIPN    （您的订单正在处理中）
define('PAYPAL_TEXT_SUCCESS_FAILED', '您的付款已失败！请确认您给支付宝付款的信息是正确的.'); // PAYPALIPN

define('TEXT_NO_ORDER_MADE_INFO_MSG', '你暂时没有下任何订单.');  

// Error Report Content
define('EMAIL_TITLE', '手机电话核查错误报告');
define('EMAIL_ADMIN_NAME', '客户支持');
define('EMAIL_CONTENT_1', '<b>报告日期与时间:</b>');
define('EMAIL_CONTENT_2', '<b>客户名称:</b>');
define('EMAIL_CONTENT_3', '<b>客户编号:</b>');
define('EMAIL_CONTENT_4', '<b>订单编号:</b>');
define('EMAIL_CONTENT_5', '<b>订单日期与时间:</b>');
define('EMAIL_CONTENT_6', '<b>订单状态:</b>');
define('EMAIL_CONTENT_7', '<b>手机电话位置:</b>');
define('EMAIL_CONTENT_8', '<b>手机号码:</b>');

define('EMAIL_REPORT_SEND_MESSAGE', '电子邮件已发送成功.');

define('ENTRY_VERIFICATION_CODE', '验证码:');  

define('ENTRY_COUNTRY_CODE', '国家/地区代码 :');
define('IMAGE_BUTTON_CALL_ME_NOW', '现在打电话给我');
define('IMAGE_BUTTON_RETURN_TO_MAIN_PAGE', '返回首页');
define('MESSAGE_EMAIL_VERIFIED', '您的电子邮件已被验证了。');
define('MESSAGE_SUCCESS_MAIL_2', '验证码邮件已成功发送到您的邮箱。');
define('MESSAGE_ERROR_MAIL', '电子邮件验证失败。');
define('TEXT_CHECKOUT_SUCCESS_HEADING_MSG', '谢谢！您的订单号为 %s');
define('TEXT_CHECKOUT_SUCCESS_DESC_MSG', '您的订单处理中，将于<span class="num">%d</span>秒后成功下单。');
define('TEXT_CHECKOUT_SUCCESS_HEADING_MSG2', '恭喜您，下单成功。');
define('TEXT_CHECKOUT_SUCCESS_DESC_MSG2', '您的订单号为 <b>%s</b>');
define('TEXT_CHECK_STORE_CREDIT_LINK', '查询购物代金券余额 ');
define('TEXT_CHECK_OFFGAMERS_POINT_LINK', '查询OP');
define('TEXT_CHECK_SHARE_WITH_FRIENDS_LINK', '与朋友分享:');
define('TEXT_DISCOUNT_COUPONS', '折扣優惠卷:'); //yellow box
define('TEXT_LIVE_CHAT_LINK', '即时聊天');
define('TEXT_ORDER_SUMMARY', '<div style="display:inline-block; font: bold 12px Arial,Tahoma,Verdana,Helvetica,sans-serif; color: black; padding: 0px 0px 0px 0px;">订单概要:</div>');			
define('TEXT_RESEND_VERIFICATION_CODE', '<a href="%s">重发确认码</a>');
define('TEXT_TRACK_STATUS_LINK', '查看此订单状态');
define('TEXT_TOTAL', '共计:'); //yellow box
define('TEXT_SELECTION_BASED_RECOMENDATION', '<b>基于您的选择，你可能对以下物品感兴趣:</b>');
define('TEXT_SUB_TOTAL', '小计:'); //yellow box
define('TEXT_SURCHARGE', '附加费:'); //yellow box
define('TEXT_STORE_CREDIT', '商店存款:'); //yellow box
define('BUTTON_VIEW_ORDER', '查看订单');
?>