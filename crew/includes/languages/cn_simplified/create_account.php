<?
/*
  	$Id: create_account.php,v 1.13 2007/12/21 09:03:49 chan Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2003 osCommerce
	
  	Released under the GNU General Public License
*/

define('NAVBAR_TITLE', '注册帐户'); 
define('HEADING_TITLE', '注册');  

define('TEXT_ORIGIN_LOGIN', '如果您已经有一个帐户，请在 <a href="%s">login page</a>.登录');  
                            

define('TABLE_HEADING_COMMENTS', '其他信息');  

define('EMAIL_SUBJECT', '激活帐户');
define('EMAIL_WELCOME', '我们欢迎你到 ' . STORE_NAME . ', 您的一站式网上商店为您所有的游戏虚拟物品和高水准的代练服务 我们会向所有电子邮件包括密码重置和订单的更新发送这个电子邮件地址' . "\n\n");
  
define('EMAIL_INFO', "\n\n" . '与一帐户 ' . STORE_NAME . ", 您将登录到我们的商店:-"."\n\n"); 
define('EMAIL_LINK_INFO', '你可以点击下面的链接，以激活您的帐户.' . "\n\n");  
define('EMAIL_LINK_ADDRESS_INFO', "\n\n" . '如果您无法点击链接，复制并粘贴整个下面的网址进入您的网页浏览器.' . "\n\n");
define('EMAIL_MANUAL_ACTIVATE_EMAIL', "\n" . '您也可以手动激活您的帐户 <a href="' . tep_href_link(FILENAME_ACCOUNT_ACTIVATE, 'aov=activate') . '">' . tep_href_link(FILENAME_ACCOUNT_ACTIVATE, 'aov=activate') . '</a> 通过输入以下信息<br><br>电子邮件地址: ');
define('EMAIL_MANUAL_ACTIVATE_CODE', "<br>" . '激活代码: ');
define('EMAIL_TEXT', '- 更新您的联系信息或更改您的帐户密码,'."\n".'- 订阅或退订我们的电子报,'."\n".'- 为稍后结帐,您的购物车将自动保存内容,'."\n".'- 自动保存的内容，您的购物车为稍后结帐,'."\n".'- 收到特别的促销和折扣告示 (只适用于订阅我们的电子报).'."\n\n");
define('EMAIL_CONTACT', '如有没有任何查询或协助，请使用我们的在线客服，或通过发送电子邮件查询 ' . EMAIL_TO . '. 谢谢你购物 ' . STORE_NAME . ".\n\n\n");

define('EMAIL_WARNING', '注意：您收到此电子邮件，因为你已经注册了一个帐户 在' . STORE_NAME . " 使用这个电子邮件地址. 如果您的电子邮件地址在未经您的同意下使用请发邮到 " . '<a href="mailto:' . EMAIL_TO . '">'.EMAIL_TO.'</a> 立即取消此帐户.' . " 我们也建议你改变您的贝宝帐户密码 (如果您拥有) 由于骗徒通常注册使用其中一种的电子邮件地址后，他们获得了相应的贝宝帐户 (通过钓鱼网站，已储存的密码检索，键盘记录程序，间谍软体，等).  当然，这也可能是我们的客户在进入电子邮件地址疏误.  请理解我们的商店不容忍网际网路诈骗.  这就是为什么我们实施的电子邮件地址核实所有新帐户注册和没有购买可以作出不首先核实电子邮件地址." . "\n");
define('EMAIL_FOOTER', STORE_EMAIL_SIGNATURE);

define('EMAIL_GV_INCENTIVE_HEADER', '作为的一部分，我们欢迎新客户，我们向您发送了一个电子礼券价值的％ s');

define('EMAIL_GV_REDEEM', '该兑换的代码是％ s ，您可以输入兑现代码时，检查后，作出了购买');
    define('EMAIL_GV_LINK', '或走这条连结 ');
define('EMAIL_COUPON_INCENTIVE_HEADER', '恭喜，让您第一次访问我们的网上商店更有价值的经验' . "\n" .
                                        '  下面为了你创造的折扣优惠券' . "\n\n");
define('EMAIL_COUPON_REDEEM', '使用优惠券输入兑现代码，这是％ s期间结帐, ' . "\n" .
                               '过后购买');
?>