<?
/*
  	$Id: customer_support.php,v 1.7 2007/09/28 10:16:57 weichen Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2003 osCommerce
	
  	Released under the GNU General Public License
*/

define('NAVBAR_TITLE', '提交问题单');
define('HEADING_TITLE', '提交问题单');
define('TEXT_LONG_DESCRIPTION', '有意见要发表？有问题要问？您来对地方了。请填写以下表格- 确保您已选择联系我们的原因，请记住，您提供的资料越详细，我们给您的回复也就越详细。谢谢！');
define('TEXT_ISSUE_1', '我们将优先考虑每个提交的请求。我们的目标是能及时、顺利地将金币交易给您。');
define('TEXT_ISSUE_2', '您无需再在线等待金币交易了！ <b>Put in My Account</b> <sup>客户的选择</sup> 是金币订单中最受欢迎的一种交易方式, 您只需向我们提供您的账号信息，我们将为金币直接放入您的账号内。<br><br>我们将优先考虑每个提交的请求。我们的目标是能及时、顺利地将金币交易给您。');
define('SUCCESS_CONTACT_CS_FORM_SENT', '信息发送成功');
define('TECHNICAL_DIFFICULTIES', '若有任何需要咨询的问题，回馈信息或投诉，<br>
								  请将问题发送至support<EMAIL>。<br>
								  对于由此造成的不便，请谅解。');
define('SUBMISSION_FORM_REQUEST_LINK_TITLE', '选择最适合您的问题的表格：');
define('LINK_POWER_LEVELING_ACCOUNT_RESUBMISSION', '代练帐号重新提交');
define('LINK_GAME_CARD_ISSUES_SUBMISSION', '游戏代码问题提交');
define('LINK_FOR_PUT_INTO_MY_ACCOUNT_DELIVERY', '要求“PIMA-放入我的账户”交易');
define('TEMPORARY_CLOSED_DOWN_MESSAGE', '各位访客, <br><br>客服平台将于6月14日晚间22点至15日上午9点进行定期维护。如果您有任何问题，请发信到support<EMAIL>。<br>谢谢。');
	
define('HEADING_SEND_A_MESSAGE', '发送信息');
define('HEADING_CHAT_LIVE', '买 - 即时聊天');
define('HEADING_BUYBACK_CHAT_LIVE', '卖 - 即时聊天');
define('HEADING_YOUR_EMAIL_ADDRESS', '您的电子邮件地址 :');
define('HEADING_ISSUE', '问题 :');
define('HEADING_PHONE_NUMBER', '电话号码 :');
define('HEADING_ORDER_NUMBER', '订单号 :');
define('HEADING_PRODUCT_TYPE', '产品类型 :');
define('HEADING_MESSAGE', '信息 :');
define('HEADING_TELL_US_YOUR_ISSUE', '请告诉我们您遇到/想咨询的问题 :');
define('HEADING_GAME_TITLE', '游戏名称 :');
define('HEADING_WILL_BE_IN_GAME', '距离登录时间 :');
define('HEADING_IN_GAME_DURATION', '游戏内持续时间 :');
define('HEADING_ACCOUNT_NUMBER', '账户号码 :');
define('HEADING_ACCOUNT_PASSWORD', '账号密码 :');
define('HEADING_CHARACTER_NAME', '角色名 :');
define('HEADING_ACCOUNT_NAME', '账号名 :');
define('HEADING_REGION', '区域 :');
define('HEADING_FACTION', '阵营 :');
define('HEADING_SERVER', '服务器 :');
define('HEADING_ERROR_FOUND', '发现的问题 :');
define('HEADING_ATTACH_FILE', '附件 :');
define('HEADING_USERNAME', '游戏帐号 :');
define('HEADING_REALM', '区 :');
define('HEADING_CLASS', '职业 :');
define('HEADING_PAYMENT_METHOD', '付款方式 :');
define('HEADING_SELL_ORDER_NUMBER', '出售订单编号 :');

define('HEADING_SCREENSHOT_1', '截图1 :');
define('HEADING_SCREENSHOT_2', '截图2 :');
define('HEADING_SCREENSHOT_3', '截图3 :');

define('BUTTON_SEND_MESSAGE', '发送信息');
define('BUTTON_RESET', '重置');
define('BUTTON_CHAT_NOW', '开始聊天');

define('OPTION_ISSUE_1', '面对面交易');
define('OPTION_ISSUE_2', 'PIMA(限于魔兽)');
define('OPTION_ISSUE_3', '游戏代码问题');
define('OPTION_ISSUE_4', '重新提交代练账号');
define('OPTION_ISSUE_5', '订单延迟/未开始');
define('OPTION_ISSUE_6', '对当前订单的咨询');
define('OPTION_ISSUE_7', '对取消的订单的咨询');
define('OPTION_ISSUE_8', '付款时出现的问题');
define('OPTION_ISSUE_9', '卖金币过程中出现的问题');
define('OPTION_ISSUE_10', '对以后的产品/促销活动的咨询');
define('OPTION_ISSUE_11', '客户的反馈/证言/投诉');
define('OPTION_ISSUE_12', '其它');

define('OPTION_CHAT_ISSUE_1', '需要更多的产品介绍');
define('OPTION_CHAT_ISSUE_2', '询问有关ETA/库存');
define('OPTION_CHAT_ISSUE_3', '询问以后的产品发行问题');
define('OPTION_CHAT_ISSUE_4', '付款方式咨询');
define('OPTION_CHAT_ISSUE_5', '要退款');
define('OPTION_CHAT_ISSUE_6', '订单延迟、未发货');
define('OPTION_CHAT_ISSUE_7', '询问关于延迟订单的情况');
define('OPTION_CHAT_ISSUE_8', '没有收到');
define('OPTION_CHAT_ISSUE_9', '需要在游戏内交易');
define('OPTION_CHAT_ISSUE_10', '无法看清/模糊/无法使用');
define('OPTION_CHAT_ISSUE_11', '我的订单在哪？');
define('OPTION_CHAT_ISSUE_12', '其它');

define('ERROR_ORDER_NUMBER', '订单号无效.');
define('ERROR_PHONE_NUMBER', '电话号码无效.');
define('ERROR_ACCOUNT_NAME', '帐号号码无效.');
define('ERROR_ACCOUNT_PASSWORD', '帐号密码无效.');
define('ERROR_CHARACTER_NAME', '角色名无效.');
define('ERROR_REGION', '区域无效.');
define('ERROR_FACTION', '阵营无效.');
define('ERROR_SERVER', '伺服器无效.');
define('ERROR_REALM', '区无效.');
define('ERROR_CLASS', '职业无效.');
define('ERROR_PRODUCT_TYPE', '产品类型无效.');
define('ERROR_PAYMENT_METHOD', '付款方式无效.');
define('ERROR_SELL_ORDER_NUMBER', '无效的出售订单号.');
define('ERROR_EMAIL_ADDRESS', '电子邮件地址未填.');
define('ERROR_MESSAGE', '信息未填.');

$game_title_array = array(	array	('id'=>'World of Warcraft', 'text'=>'魔兽'),
							array	('id'=>'Age of Conan', 'text'=>'询问有关ETA/库存'),
							array	('id'=>'Silk Road Online', 'text'=>'丝路'),
							array	('id'=>'Lineage II', 'text'=>'天堂2'),
							array	('id'=>'Requiem: Bloodymare', 'text'=>'安魂曲'),
							array	('id'=>'Other', 'text'=>'其它')
						);

$will_be_in_game_array = array(	array	('id'=>'0 hour (Now)', 'text'=>'0 小时 (现在)'),
							array	('id'=>'1 hour', 'text'=>'1小时'),
							array	('id'=>'2 hours', 'text'=>'2小时'),
							array	('id'=>'3 hours', 'text'=>'3小时'),
							array	('id'=>'4 hours', 'text'=>'4小时'),
							array	('id'=>'5 hours', 'text'=>'5小时'),
							array	('id'=>'6 hours', 'text'=>'6小时'),
							array	('id'=>'7 hours', 'text'=>'7小时'),
							array	('id'=>'8 hours', 'text'=>'8小时'),
							array	('id'=>'9 hours', 'text'=>'9小时'),
							array	('id'=>'10 hours', 'text'=>'10小时'),
							array	('id'=>'11 hours', 'text'=>'11小时'),
							array	('id'=>'12 hours', 'text'=>'12小时')
						);

$in_game_duration_array = array(	array	('id'=>'1 hour', 'text'=>'1小时'),
									array	('id'=>'2 hours', 'text'=>'2小时'),
									array	('id'=>'3 hours', 'text'=>'3小时'),
									array	('id'=>'4 hours', 'text'=>'4小时'),
									array	('id'=>'5 hours', 'text'=>'5小时'),
									array	('id'=>'6 hours', 'text'=>'6小时'),
									array	('id'=>'7 hours', 'text'=>'7小时'),
									array	('id'=>'8 hours', 'text'=>'8小时'),
									array	('id'=>'9 hours', 'text'=>'9小时'),
									array	('id'=>'10 hours', 'text'=>'10小时'),
									array	('id'=>'11 hours', 'text'=>'11小时'),
									array	('id'=>'12 hours', 'text'=>'12小时')
						);


$region_array = array(	array	('id'=>'US', 'text'=>'美服'),
						array	('id'=>'Europe', 'text'=>'欧服')
					);

$faction_array = array(	array	('id'=>'Alliance', 'text'=>'联盟'),
						array	('id'=>'Horde', 'text'=>'部落')
					);

$error_found_array = array(	array	('id'=>'Invalid', 'text'=>'Invalid'),
							array	('id'=>'Unable to view', 'text'=>'代码模糊'),
							array	('id'=>'In use', 'text'=>'代码已使用'),
							array	('id'=>'Others', 'text'=>'其它')
					);

$product_array = array(	array	('id'=>'', 'text'=>PULL_DOWN_DEFAULT),
						array	('id'=>'Game Card', 'text'=>'游戏点卡'),
						array	('id'=>'Game Currency', 'text'=>'游戏金币'),
						array	('id'=>'Power Leveling', 'text'=>'游戏代练'),
						array	('id'=>'Other', 'text'=>'其它')
					);
?>