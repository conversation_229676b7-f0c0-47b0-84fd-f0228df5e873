<?
// All email related text must be defined here in case email using different CHARSET than webpage
// e-mail greeting
define('EMAIL_GREET_MR', '亲爱的 %s先生,' . "\n\n");
define('EMAIL_GREET_MS', '亲爱的  %s小姐,' . "\n\n");
define('EMAIL_GREET_NONE', '亲爱的 %s,' . "\n\n");

define('EMAIL_DISPLAY_CURRENCY', "￥");

define('EMAIL_LOCAL_STORE_NAME', '卧虎游戏');
define('EMAIL_LOCAL_STORE_EMAIL_SIGNATURE', '顺祝商祺'."\n\n".'卧虎游戏团队 启'."\n\n".'*************************************************'."\n".EMAIL_LOCAL_STORE_NAME.' - 您的游戏联盟'."\n".'*************************************************'."\n".'电邮: <a href="mailto:'.EMAIL_LOCAL_STORE_EMAIL.'">' . EMAIL_LOCAL_STORE_EMAIL.'</a>');
define('EMAIL_LOCAL_EMAIL_SUBJECT_PREFIX', '[卧虎游戏]');

define('EMAIL_BUYBACK_ORDER_GUIDE', '
<b>	订单流程 : [待定] -> “ [处理] - >” [完成]或[取消]</b>

[待定] ： 请稍等,我们将提供您我们角色的资料.
[处理] ： 交付正在被我们的系统处理.
[完成] ： 提供已注册完全。
[取消] ： 该订单已被取消.

<b>注意1: 请确保您所提供的角色名称是100%准确。如角色名称出现错误引起问题， 我们不承担任何的责任所.
注意2: 交易完毕后，请填入[出货数量],以便我们支付给您
注意3: [提交数量]和[出货数量]需相同。如果数量不同，我们不承担任何帐户关闭的责任.
注意4: 一旦您的订单[完成]后，付款将15分钟内提供给您.您可通过OffGamers帐户, 使用[撤回]功能把付款撤回您所选择的支付方式.</b>
');

// Sign Up Account
define('EMAIL_SIGNUP_STEP1_SUBJECT', implode(' ', array(EMAIL_LOCAL_EMAIL_SUBJECT_PREFIX, "激活帐号信")));
define('EMAIL_SIGNUP_STEP2_SUBJECT', implode(' ', array(EMAIL_LOCAL_EMAIL_SUBJECT_PREFIX, "成功启动帐号")));

define('EMAIL_SIGNUP_STEP1_BODY', '谢谢您加入我们供应商阵容。'. "\n\n" .
					 EMAIL_LOCAL_STORE_NAME.'由始至终都相信，我们的成功都是靠供应商的配合与支持，秉承着这个观念，我们将尽其所能维护供应商利益，永远保持供应商与卧虎游戏双赢的局面。我们恳切地希望能与您长期地合作，因为只有这样我们才能一起在这个竞争力高的行业共同成长。' . "\n\n" .
					'注册的2个步骤：'. "\n\n" .
					'步骤 1 ：提交注册表格.. 完成' . "\n\n" .
					'步骤 2 : 启动帐号:: 等待中' . "\n\n" .
					'请执行这个链接和及提交启动表格以完成注册过程。' . "\n" .
					'您可以点击下面的链接（如果您的电子邮件系统允许),或粘贴下面链接拷贝到您的浏览器。' . "\n\n" .
					'<a href="%s">%s</a>'. "\n\n" .
					'或拷贝与黏贴这个启动密码入激活帐号网页所提供的启动密码格子：'."\n\n" .
					'<b>'."%s".'</b>');

define('EMAIL_SIGNUP_STEP2_BODY', '您的'.EMAIL_LOCAL_STORE_NAME.'帐号已被启动。'. "\n\n" .
					'注册包括2个步骤：'. "\n\n" .
					'<b><u>步骤 1 ：提交注册表格.. <i>完成</i></u></b>' . "\n\n" .
					'<b><u>步骤 2 : 启动帐号:: <i>完成</i></u></b>' . "\n\n" .
					'现在您可以在'.EMAIL_LOCAL_STORE_NAME.'登入' . "\n\n");

define('EMAIL_SIGNUP_STEP2_VIP_BODY','欢迎加入'.EMAIL_LOCAL_STORE_NAME.'VIP成员计划！' . "\n\n" .
'成为'.EMAIL_LOCAL_STORE_NAME.'VIP成员您将会享受到无比自由的感觉：' . "\n\n" .
'1、自由更新所拥有的游戏、服务器、库存等；' . "\n" .
'2、自由选择想出售的金币数量，如果销售单没有达到您要求的供货数量则不会被交易通知所骚扰；' . "\n" .
'3、自由选择接受的支付货币——美金人民币随您挑，同时登入VIP交易模块会使您更加清晰的管理自己的交易单和交易款项；' . "\n" .
'4、自由享受供货商VIP等级晋升所带来的利润分配比例的提高（'.EMAIL_LOCAL_STORE_NAME.'会将金币销售价格透明化地告知您，我们一起共享利润分配）' . "\n" );

define('EMAIL_ADMIN_SIGNUP_SUCCESS_BODY', '新的帐号已被启动。'. "\n\n" .
						'供应商电子信箱：%s'. "\n" );

define('EMAIL_SIGNUP_FOOTER', "\n\n" . EMAIL_LOCAL_STORE_EMAIL_SIGNATURE);

//upgrade/downgrade rank
define('EMAIL_RANK_CHANGE_SUBJECT', implode(' ', array(EMAIL_LOCAL_EMAIL_SUBJECT_PREFIX, "会员级别更改通知")));
define('EMAIL_RANK_LEVEL_UP', '上升至');
define('EMAIL_RANK_LEVEL_DOWN', '下降至');
define('EMAIL_NO_RANK_AVAILABLE', '没等级');
define('EMAIL_RANK_CHANGE_BODY', '你目前的级别 ：%s '. "\n" .
'等级：由 %s　%s　%s ' . "\n" .
'分享卧虎游戏金币卖价百分比：由　%s%%　%s　%s%% ' . "\n" .
'总积分 : %s ' . "\n" .
'所需要的下一等级升级分数 : %s ' . "\n\n");

define('EMAIL_RANK_CHANGE_SUGGEST_BODY', '建议1 从VIP1等级升级，就是从VIP1级开始向上排列，给人一种无限发展的可能' . "\n" .
'建议2 晋升等级增加的分享比例要减少些，比方说晋升一级2%-5%' . "\n" .
'建议3 可以适当考虑每晋升一个等级相应的称呼可以改变'."\n");

define('EMAIL_RANK_CHANGED_FOOTER', "\n\n" . EMAIL_LOCAL_STORE_EMAIL_SIGNATURE);

// New Buyback Order
define('EMAIL_NEW_BUYBACK_SUBJECT', implode(' ', array(EMAIL_LOCAL_EMAIL_SUBJECT_PREFIX, "新单子 #%s 已在审核中")));

define('EMAIL_NEW_BUYBACK_BODY', "谢谢您卖货物给 ".EMAIL_LOCAL_STORE_NAME.".\n\n 供应商单子总结：\n".EMAIL_SEPARATOR."\n");
define('EMAIL_NEW_BUYBACK_ORDER_NUMBER', '单子编号: %s');
define('EMAIL_NEW_BUYBACK_DATE_ORDERED', '单子日期: %s');
define('EMAIL_NEW_BUYBACK_CUSTOMER_EMAIL', '供应商信箱邮址: %s');
define('EMAIL_NEW_BUYBACK_PRODUCTS', '产品');
define('EMAIL_NEW_BUYBACK_ORDER_TOTAL', '总额: %s %s');
define('EMAIL_NEW_BUYBACK_COMMENTS', '附加信息:');
define('EMAIL_NEW_BUYBACK_STATUS', '状态: 审核');
define('EMAIL_NEW_BUYBACK_ORDER_CLOSING', '您可以登入 <a href="'.tep_href_link('/', '', 'SSL', false).'">'.tep_href_link('/', '', 'SSL', false).'</a> 然后点入“单子状态”来回顾你的订单纪录、订单状态以及接货ID');

define('EMAIL_NEW_BUYBACK_ORDER_FOOTER', "\n\n" . EMAIL_LOCAL_STORE_EMAIL_SIGNATURE);
// Buyback Order Update
define('EMAIL_BUYBACK_UPDATE_SUBJECT', implode(' ', array(EMAIL_LOCAL_EMAIL_SUBJECT_PREFIX, "单子更新 #%s 已被取消")));

define('EMAIL_BUYBACK_UPDATE_BODY', "谢谢您卖货物给 ".EMAIL_LOCAL_STORE_NAME.".\n\n 供应商单子总结：\n".EMAIL_SEPARATOR."\n");
define('EMAIL_BUYBACK_UPDATE_ORDER_NUMBER', '单子编号: %s');
define('EMAIL_BUYBACK_UPDATE_DATE_ORDERED', '单子日期: %s');
define('EMAIL_BUYBACK_UPDATE_CUSTOMER_EMAIL', '供应商信箱邮址: %s');
define('EMAIL_BUYBACK_UPDATE_PRODUCTS', '产品');
define('EMAIL_BUYBACK_UPDATE_ORDER_TOTAL', '总额: %s %s');
define('EMAIL_BUYBACK_UPDATE_COMMENTS', '附加信息:');

define('EMAIL_BUYBACK_UPDATE_FOOTER', "\n\n" . EMAIL_LOCAL_STORE_EMAIL_SIGNATURE);

define('EMAIL_PAYMENT_PAYMENT_UPDATE_SUBJECT', implode(' ', array(EMAIL_LOCAL_EMAIL_SUBJECT_PREFIX, "款项 #%s 已在审核中")));

define('EMAIL_PAYMENT_TEXT_TITLE', '您的提款资料为,');
define('EMAIL_PAYMENT_TEXT_SUMMARY_TITLE', '款项总结：');
define('EMAIL_PAYMENT_TEXT_PAYMENT_NUMBER', '款项编号：%s');
define('EMAIL_PAYMENT_TEXT_PAID_AMOUNT', '支付总额: %s');
define('EMAIL_PAYMENT_TEXT_PAYMENT_DATE', '提款日期：%s');
define('EMAIL_PAYMENT_TEXT_STATUS_UPDATE', "状态：%s");
define('EMAIL_PAYMENT_TEXT_COMMENTS', '评论：' . "\n%s\n");
define('EMAIL_PAYMENT_WITHDRAW_PROCESS_GUIDE', '
<b>提款流程：提款 --> 审核 --> 处理 --> 完成/取消</b>

提款：款项必须从【结存余额】提出
审核：等待财务部审核款项数目
处理：财务部正运行发款
完成：款项已发到供应商银行账号
取消：单子已被供应商或卧虎游戏取消
');
define('EMAIL_PAYMENT_FOOTER', EMAIL_LOCAL_STORE_EMAIL_SIGNATURE);

//Email payment status
$PAYMENT_STATUS_ARRAY = array('审核' => '1',//Pending
                              '处理' => '2',//Processing
                              '完成' => '3',//Completed
                              '取消' => '4' //Canceled
                              );

// Pre-Sale
define('EMAIL_BUYBACK_PRESALES_SUBJECT', implode(' ', array(EMAIL_LOCAL_EMAIL_SUBJECT_PREFIX, "开收服务器通知")));

define('EMAIL_BUYBACK_PRESALES_TITLE', '谢谢您的支持！目前您可以马上登入'.EMAIL_LOCAL_STORE_NAME.'售卖您所登记的服务器');
define('EMAIL_BUYBACK_PRESALES_SUMMARY_TITLE', '开收服务器总结：');
define('EMAIL_BUYBACK_PRESALES_PRODUCT', '服务器：%s');
define('EMAIL_BUYBACK_PRESALES_MIN_QTY', '最低数量：%d');
define('EMAIL_BUYBACK_PRESALES_MAX_QTY', '最高数量：%d');
define('EMAIL_BUYBACK_PRESALES_COMMENTS', '附加信息:'."\n\n".EMAIL_LOCAL_STORE_NAME.'的开收服务只是开放给有登记开收服务的供应商，采购的方式是以先卖为快，请不要耽误您出货的机会。谢谢！');
define('EMAIL_BUYBACK_PRESALES_FOOTER', "\n\n" . EMAIL_LOCAL_STORE_EMAIL_SIGNATURE);

//vip new vip order
define('EMAIL_NEW_VIP_ORDER_SUBJECT', implode(' ', array(EMAIL_LOCAL_EMAIL_SUBJECT_PREFIX, "新VIP单子 #%s 已在%s中 %s ")));
define('EMAIL_TRADING_MODE', '交易方式：%s');

$VIP_TRADE_MODE = array('vip_deal_on_game' => '与客户交易',
						'ofp_deal_with_customers' => '与客户交易'
                       );

//Email buyback order status
$BUYBACK_ORDER_STATUS_ARRAY = array('审核' => '1',//Pending
		                            '处理' => '2',//Processing
		                            '完成' => '3',//Completed
		                            '取消' => '4' //Canceled
		                            );

define('EMAIL_NEW_VIP_ORDERS_SUMMARY','供应商单子总结：
------------------------------------------------------
单子编号: %s
单子日期: %s
供应商信箱邮址: %s
交易方式：%s

产品
------------------------------------------------------
%s x %s = %s
------------------------------------------------------
总额: %s

附加信息:


状态: %s ');


define('EMAIL_ADMIN_NEW_VIP_ORDERS_SUMMARY','供应商单子总结：
------------------------------------------------------
单子编号: %s
单子日期: %s
供应商信箱邮址: %s
交易方式：%s

产品
------------------------------------------------------
%s
------------------------------------------------------
总额: %s

附加信息:


状态: %s ');


define('EMAIL_VIP_ORDER_GUIDE', '
<b>与客户交易下单流程：【审核】 --> 【处理】 --> 【完成】或【取消】</b>

【审核】 ：直接发货给客户，客户的ID将会出现在我们的网站上。发货前记得得密客户交易前密码，商量好地点，交易时得截一个完整的图，最后得和客户拿交易完毕密码。
【处理】 ：交易完毕密码不正确，等待我们的客服确认。
【完成】 ：交易完毕密码正确，您所发数量已被确认。
【取消】 ：单子已被供货商或卧虎游戏取消

备注1 ：下单时必须提供准确发货ID，否则单子出现问题将不被处理。
备注2 ：发货后请填上【已发数量】，否则将延误货款支付。
备注3 ：【草单数量】与【已发数量】必须一致，不然封号时将不被处理。
备注4 ：单子在完成后的15分钟，单子款项会自动支入供应商的【结存余额】。供应商可以在15分钟后，自行选择提款支入先前已设好的银行账号。
');

// Buyback Order Cancellation notification
define('EMAIL_STAT_SUBJECT', implode(' ', array(EMAIL_LOCAL_EMAIL_SUBJECT_PREFIX, "Alert on China Buyback Order Cancellation")));
define('EMAIL_STAT_TITLE', 'Today\'s Statistics:');
define('EMAIL_STAT_FORMULAR', 'Total Canceled / Total Orders'."\n".'= %s / %s'."\n".'= %s%%');
define('EMAIL_STAT_CUSTOMER_NAME', 'China Buyback Customer: %s');
define('EMAIL_STAT_CUSTOMER_EMAIL', 'China Buyback Customer E-mail: %s');
define('EMAIL_STAT_DATE', 'Date: %s');
?>