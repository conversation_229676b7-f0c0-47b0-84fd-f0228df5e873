<?
define('HEADING_TITLE', '活动'); 
define('NAVBAR_TITLE', HEADING_TITLE);
define('FORM_TITLE', '提交表格');  

define('ENTRY_SUBMIT_PICTURE', '提交图片:');  
define('ENTRY_SERVER', '服务器:');   
define('ENTRY_CHAR_NAME', '角色名称:');     
define('ENTRY_EMAIL', '电子邮件:');  
define('ENTRY_OGM_ORDER_NUM', 'OffGamers 订单号码:');  
define('ENTRY_EVENT_ORDER_NO', '订单号码.');  

define('TEXT_SS_SIZE_AND_FORMAT', '(只收高达 %sKB 的 .jpg图片)');
define('ERROR_CANNOT_LEFT_BLANK', '不能留空');  
define('INVALID_ORDER_NUMBER_ERROR', '订单号码错误.');  
define('CHARACTER_NAME_ERROR', '请填写个角色名称.');
define('CHAR_LEFT', '剩余的字符.');  

define('EVENT_REQUIRED_FIELD_ERROR', '必填字段不能留空.');  
define('EVENT_CONTENT_ERROR', '请填满文字字段.');  

define('EVENT_CONTENT_LENGTH_ERROR', '信息长度太短。最少％s个字符.');  
define('SERVER_ERROR', '请选择一个服务器.');   
define('EVENT_SS_EMPTY_ERROR', '请点击寻找并上载图片.');  
define('EVENT_SS_EXTENSION_ERROR', '文件批量錯誤.');   
define('EVENT_SS_ERROR', '图片文件错误。请稍后再试.');  
//define('EVENT_SS_SIZE_EXCEED_LIMIT_ERROR', '图片超过％skb。请重新上载.'); 
define('EVENT_SS_SIZE_EXCEED_LIMIT_ERROR', '文件太大，请重新上载.');  

define('EVENT_SS_SUBMITTED_ERROR', '此订单之前已被提交。请再试另一个订单号码.');  
define('ERROR_QUOTA', '提交失败限量超过了，请稍等15分钟，然后再试.');
               
define('EVENT_SUBMIT_SUCCESS', '谢谢您的合作');
            
define('TEXT_LOGIN_EVENT_MESSAGE', '祝你好运,尽情游戏吧!');
//define('TEXT_LOGOFF_EVENT_MESSAGE', '请登录您的offgamers帐户，上载您的文件. 并不需提供订单号码.');
                  
define('TEXT_LOGOFF_EVENT_MESSAGE', '<a href="%s">登陆</a>后立即提交! 没有OffGamers帐户? <a href="%s">注册</a>.');
define('TEXT_ARTICLE', '文章');
define('TEXT_SCREEN_SHOT', '截图');

//define('CUSTOMER_NOTIFICATION_EVENT_SUBJECT', '文件上载通知');
define('CUSTOMER_NOTIFICATION_EVENT_SUBJECT', '提交成功');
//define('CUSTOMER_NOTIFICATION_EVENT_CONTENT', '***************************************************' . "\n" . '这是一个自发得的电子邮件-请别回复.' . "\n". '***************************************************' . "\n" . "恭喜,\n\n" . "您的订单号码是 %s, %s" . "\n" . '您已经成功上载您的 %s' . "\n" . '更多的促销和宣传活动新闻，请观览 <a href="http:\\www.offgamers.com">' . STORE_NAME . '</a>' . "\n" . '如果您需要任何求助 ，请联络 <a href="mailto:' . EMAIL_TO . '">' . EMAIL_TO . '</a>' . "\n\n" . '感谢您参与' . "\n\n" . EMAIL_FOOTER);
define('CUSTOMER_NOTIFICATION_EVENT_CONTENT', '***************************************************' . "\n" . '这是一个自发得的电子邮件-请别回复.' . "\n" . '***************************************************' . "\n" . "恭喜,\n\n" . "你的 [ %s ]". "\n" . '您已经成功上载您的 %s' . "\n" . '更多的促销和宣传活动新闻，请访问 <a href="http:\\www.offgamers.com">' . STORE_NAME . '</a>' . "\n" . '如果您需要任何求助 ，请联络 <a href="mailto:' . EMAIL_TO . '">' . EMAIL_TO . '</a>' . "\n\n" . '感谢您参与' . "\n\n" . '谨,' . "\n" . 'OffGamers - 您的游戏联盟' . "\n" . '<a href="http:\\www.offgamers.com">' . STORE_NAME . '</a>');
define('ADMIN_EVENT_CONTENT', '订单号码: %s' . "\n" . '角色名称: %s' . "\n" . '电子邮件: %s' . "\n" . '地址: ' . "\n" . '%s' . "\n" . '电话: %s' . "\n" . '付款方式: %s' . "\n" . '金额数目: %s' . "\n" . '订单状态: %s' . "\n" . '%s' . "\n\n");
define('ADMIN_SS_EMAIL_SUBJECT', '完美世界屏幕截图竞赛');
define('ADMIN_ARTICLE_EMAIL_SUBJECT', '卓越之剑\秘诀活动');
?>