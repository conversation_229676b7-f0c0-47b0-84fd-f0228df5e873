<?php
/*
  $Id: gift_card.php,v 1.11 2014/06/25 10:17:16 weesiong Exp $
  
  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2003 osCommerce

  Released under the GNU General Public License
*/

define('NAVBAR_GC_TITLE', 'OffGamers礼品卡');
define('TAB_HEADING_1', '用礼品卡购物');
define('TAB_HEADING_2', '兑换礼品卡');
define('TAB_HEADING_3', '购买礼品卡');

define('HEADING_STEP_1_TITLE', '第1步：礼品卡信息');
define('HEADING_STEP_2_TITLE','第2步：登陆或注册');
define('HEADING_PWGC_STEP_2_TITLE','第2步：购物');
define('HEADING_PWGC_STEP_3_TITLE','第3步：登陆或注册');

define('EMAIL_CHANGE_YOUR_PASSWORD_BODY', '恭喜您成为' . STORE_NAME . '会员。您近期以此电邮注册' . STORE_NAME . '账户，以下为您的账户信息:'."\n\n".'电邮: <b>%s</b>'."\n".'密码: <b>%s</b>'."\n\n".'为了您的账户安全，我们建议您点击以下链接，更改您的登入密码:'."\n".'<a href="%s">更改我的登入密码</a>'."\n\n".'如需任何查询或帮助，请使用我们的在线客户服务或者发送请求至<a href="mailto:' . EMAIL_TO . '">'.EMAIL_TO.'</a>.');
define('EMAIL_CHANGE_YOUR_PASSWORD_SUBJECT', '欢迎您加入' . STORE_NAME);
define('EMAIL_FOOTER', STORE_EMAIL_SIGNATURE);

define('USER_MEMBER_STATUS_NOT_ALLOW', '您的会员资格不支持兑换礼品卡。请联系我们<a target="_blank" href="mailto:<EMAIL>"><EMAIL></a>。');
define('USER_GIFTCARD_ERROR', '您输入的编号或代码错误，请重试。如果问题依然存在，请联系我们<a target="_blank" href="mailto:<EMAIL>"><EMAIL></a>。');
define('USER_GIFTCARD_DEACTIVATED', '此代码已使用，如果您购买了此代码，请联系我们<a target="_blank" href="mailto:<EMAIL>"><EMAIL></a>。');
define('USER_GIFTCARD_REDEEMED', '此代码已使用，如果您购买了此代码，请联系我们<a target="_blank" href="mailto:<EMAIL>"><EMAIL></a>。');
define('USER_GIFTCARD_UNKNOWN_ERROR', '请重新输入代码，如果您依然收到同样的错误信息，请联系我们<a target="_blank" href="mailto:<EMAIL>"><EMAIL></a>。');
define('USER_GIFTCARD_INFO_MISSING', '请输入编号或代码。');
define('USER_REDEEM_GIFTCARD_FAILURE', '请重试并且再次点击充值按钮，如果问题依然存在，请联系我们<a target="_blank" href="mailto:<EMAIL>"><EMAIL></a>。');
define('USER_GIFTCARD_CURRENCY_INVALID', '抱歉，我们的商店不再支持以下礼品卡货币: AED, SAR, KWD, PHP, JPY and HKD. 如有任何疑问，烦请联系<a target="_blank" href="mailto:<EMAIL>"><EMAIL></a>.');

define('DF_TEXT_GIFT_CARD_VALUE', '礼品卡价值%s');
define('DF_TEXT_REDEEM_GIFT_CARD_CONFIRMATION', '礼品卡价值%s (兑换为购物代金券%s)');

define('TEXT_GC_QUANTITY', '数量');
define('TEXT_SERIAL_NO', '编号');
define('TEXT_PIN_CODE', '代码');
define('TEXT_TOTAL_VALUE', '总值');
define('TEXT_LOGIN_PASSWORD', '我的登录密码');
define('TEXT_NAME', '我的姓名');
define('TEXT_EMAIL', '我的电邮');
define('TEXT_GC_WELCOME_BACK', '欢迎回来<b>%1$s</b> (不是%1$s? <a href="javascript:void(0);" onclick="%2$s">点此登出</a>)');
define('TEXT_GC_PC', '件');
define('TEXT_GC_MANAY_PC', '');
define('TEXT_PRODUCT', '产品');
define('TEXT_PURCHASE_FOR_MYSELF', '自用');
define('TEXT_PURCHASE_AS_GIFT', '作为礼物');
define('TEXT_PURCHASE_WITH_GIFT_CARD_CONFIRMATION', '您确认本次购买以OffGamers Gift Card支付吗？');
define('TEXT_SELECT', '选择');
define('TEXT_IS_EXISTING_CUSTOMER', '现有客户');
define('TEXT_IS_NEW_CUSTOMER', '我是新客戶');

define('TEXT_GC_SUBHEADER', '千款游戏产品一卡解决！');
define('TEXT_GC_SUB_DESC', 'OffGamers礼品卡是专为取代传统礼品券，并为广大客户提供更加便捷的服务而设计的。有了OffGamers礼品卡您只需动动手指，即可送礼物给亲人，朋友以及同事。 直接在线订购，充值礼品卡，将是您理想的选择。');

// FAQ
define('TEXT_GC_FAQ_AWARD_TITLE', 'OffGamers 礼品卡奖励榜');
define('TEXT_GC_FAQ_H1', '什么是OffGamers礼品卡');
define('TEXT_GC_FAQ_C1', 'OffGamers礼品卡是专为取代传统礼品券，并为广大客户提供更加便捷的服务而设计的。有了OffGamers礼品卡您只需动动手指，即可送礼物给亲人，朋友以及同事。');
define('TEXT_GC_FAQ_H2', '为什么选择OffGamers礼品卡?');
define('TEXT_GC_FAQ_C2', '全年 365天，无论何时，无论何地，有了礼品卡，您都可以在OffGamers购物商城享受购物乐趣。礼 品卡是您购物清单上任何人的完美礼物，真正能满足他们的需要。直接在线订购，充值礼品卡 ，将是您理想的选择。');
define('TEXT_GC_FAQ_H3', '如何购买OffGamers礼品卡?');
define('TEXT_GC_FAQ_C3', '首先登入您的OffGamers账户。选择"购买礼品卡"或者<a href="'.tep_href_link(FILENAME_GIFT_CARD, 'action=purchase_gc', 'SSL').'">点此</a>浏览OffGamers礼品卡页面。选择您所需的礼品卡面值以及付款 方式。填写您和收件人的详细信息，点击"进行结账"完成交易。');

// Display Message
define('SUCCESS_REDEEMED', '您的OffGamers礼品卡兑换成功！');
define('SUCCESS_CREDITED', '您的账户已计入%s购物代金券。');
define('SUCCESS_PURCHASE_WITH_GIFT_CARD', '恭喜！您的订单已成功完成！');
define('SUCCESS_PURCHASE_WITH_GIFT_CARD_ORDER_INFO', '您的订单号码<b>%s</b>');
define('SUCCESS_CREDIT_BALANCE', '您的OffGamers购物代金券余额: <b>%s</b>');
define('SUCCESS_CREDIT_BALANCE_DESC', '您OffGamers里礼品卡内的余额已作为购物代金券记入您的账户内。');

define('ERROR_NOT_ENOUGH_CREDIT', '请注意：您的OffGamers礼品卡内余额不足，无法支付本次费用。请选择价格便宜的产品或减少购买数量，或者在线获取<a href="'.tep_href_link(FILENAME_GIFT_CARD, 'action=purchase_gc', 'SSL').'">OffGamers礼品卡</a>。');
define('ERROR_NOT_ENOUGH_CREDIT_AFTER_QTY', '请注意：您的OffGamers礼品卡内余额不足，无法支付本次费用。请选择价格便宜的产品或减少购买数量。'. "\n" .'如果您的账户内的购物代金券余额充足，您可使用购物代金券支付余下差额。');
define('ERROR_INVALID_FORM_SUBMIT', '注意，您输入的信息中有一处错误，请确认输入正确的信息。');

// Button
define('BUTTON_REDEEM', "充值");
define('BUTTON_VIEW_ORDER', '查看订单');
define('BUTTON_VIEW_SC_BALANCE', '查看购物代金券余额');
define('BUTTON_CHECK_OP_BALANCE', '查看您的OP');
define('BUTTON_BROWSE_MORE_GAMES', '浏览更多游戏');
define('BUTTON_CONFIRM_CHECKOUT', '确认结帐');
?>