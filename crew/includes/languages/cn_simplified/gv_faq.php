<?php
/*
  $Id: gv_faq.php,v 1.2 2004/09/23 05:36:09 stanley Exp $

  The Exchange Project - Community Made Shopping!
  http://www.theexchangeproject.org

  Gift Voucher System v1.0
  Copyright (c) 2001,2002 <PERSON>
  http://www.phesis.org

  Released under the GNU General Public License
*/

define('NAVBAR_TITLE', '礼券常见问题解答');   
define('HEADING_TITLE', '礼券常见问题解答');   

define('TEXT_INFORMATION', '<a name="Top"></a>
  <a href="'.tep_href_link(FILENAME_GV_FAQ,'faq_item=1','NONSSL').'">购买礼券</a><br>  
  <a href="'.tep_href_link(FILENAME_GV_FAQ,'faq_item=2','NONSSL').'">如何发送礼券</a><br>  
  <a href="'.tep_href_link(FILENAME_GV_FAQ,'faq_item=3','NONSSL').'">使用礼券购买</a><br> 
  <a href="'.tep_href_link(FILENAME_GV_FAQ,'faq_item=4','NONSSL').'">兑换礼券</a><br>
  <a href="'.tep_href_link(FILENAME_GV_FAQ,'faq_item=5','NONSSL').'">当出现问题</a><br>  
');
switch ($HTTP_GET_VARS['faq_item']) {
  case '1':
define('SUB_HEADING_TITLE','购买礼券.');  
define('SUB_HEADING_TEXT','购买礼券就像在我们的商店购买其他产品一样. 您可以使用常用的付款方式结账.一旦购买了礼券价格将被添加到您的个人礼券帐户. 如果您的礼卷账户有余颌，您可以在购物车看到您的数额，您也可通过购物车利用电子邮件发送礼券给别人.');
  break;
  case '2':
define('SUB_HEADING_TITLE','如何发送礼券.');  
define('SUB_HEADING_TEXT','您可通过购物车利用电子邮件发送礼券.在每一页的右手栏，你可以找到本网购物车的链接','. 
                           当您在发送礼券，你必须供应以下的资料:
			   收信人的姓名。
                           收信人的电子邮件地址.
                           您要发送的数量. (注意：您不必传送的全部的数额.)
                           一个短信息会出现.
                           在发出之前，请确保您已输入资料是正确的。 在礼券被发送之前, 您可以随时随地的改变以上所列的资料。');  
  break;
  case '3':
  define('SUB_HEADING_TITLE','使用礼券购买.');  
  define('SUB_HEADING_TEXT','如果您的礼券帐户有余颌，您可以使用这些余颌购买其他物品. 在结帐阶段，额外的框将会出现. 打勾这个格子将会利用您的礼券帐户余颌付此订单. 请注:如果您沒有足够的余颌, 就得选择一中付款方式付此订单. 如果您的余颌超过售价,订单价值将自动被扣,剩余的余颌将会存在您的礼券帐户.');
 
  break;
  case '4':
  define('SUB_HEADING_TITLE','兑换礼券.');  
  define('SUB_HEADING_TEXT','如果您通过电子邮件收到礼券，您将可以看到发送人的资料,以及他人的信息. 电子邮件也将包含礼券代码. 您可以把礼券代码印出，以供日后参考。现在，您可以通过两种方式兑换礼券:
  <br>  1. 点击邮件里的链接，将把您带到商店的兑换礼券的网页。请在本店注册账户,以便您可以使用所收到的礼券.
<br>2. 在结帐过程中 ，在同一页上您选择的付款方式 会有一个格子，输入礼券代码, 然后按兑换. 礼券价格将添加到您的礼券帐户。然后，您可以从我们的商店使用余颌购买任何物品');
  break;
  case '5':
  define('SUB_HEADING_TITLE','当出现问题.');
  define('SUB_HEADING_TEXT','任何礼券系统的疑问，请联络'. STORE_OWNER_EMAIL_ADDRESS . '. 在电子邮件中,请供應詳細的资料. ');
  break;
  default:
  define('SUB_HEADING_TITLE','');
  define('SUB_HEADING_TEXT','请从上述的问题选择一项.');

  }
?>