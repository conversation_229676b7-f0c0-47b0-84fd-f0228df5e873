<?php
/*
  	$Id: gv_send.php,v 1.3 2006/03/24 07:51:24 weichen Exp $
	
  	The Exchange Project - Community Made Shopping!
  	http://www.theexchangeproject.org
	
  	Gift Voucher System v1.0
  	Copyright (c) 2001,2002 <PERSON>
  	http://www.phesis.org
	
  	Released under the GNU General Public License
*/

define('HEADING_TITLE', '发送礼卷'); 
define('NAVBAR_TITLE', '发送礼卷');    
define('EMAIL_SUBJECT', '詢問来自 ' . STORE_NAME); 
define('HEADING_TEXT','<br>请在下面输入您要发送的礼券资料，如需更多详情, 请参阅我们的<a href="' . tep_href_link(FILENAME_GV_FAQ,'','NONSSL').'">'.GV_FAQ.'.</a><br>');  
define('ENTRY_NAME', '收信人姓名:');  
define('ENTRY_EMAIL', '收信人的电邮地址:');  
define('ENTRY_MESSAGE', '信息给收信人:'); 
define('ENTRY_AMOUNT', '礼券价格:');  
define('ERROR_ENTRY_AMOUNT_CHECK', '&nbsp;&nbsp;<span class="errorText">礼券价格错误</span>');
define('ERROR_ENTRY_EMAIL_ADDRESS_CHECK', '&nbsp;&nbsp;<span class="errorText">电邮地址错误</span>');
define('MAIN_MESSAGE', '您決定发送价格 %s的礼券给 %s, 电邮地址是 %s<br><br>电邮的内容如下:<br><br>Dear %s<br><br>');

define('PERSONAL_MESSAGE', '%s 说');
define('TEXT_SUCCESS', '恭喜您，您的礼券已发送成功');  

define('EMAIL_SEPARATOR', '----------------------------------------------------------------------------------------');
define('EMAIL_GV_TEXT_HEADER', '恭喜您，您已收到價值的％s的禮券');  
define('EMAIL_GV_TEXT_CODE', '您的礼券兑换代码是%s');  
define('EMAIL_GV_TEXT_SUBJECT', '%s送的礼物');  
define('EMAIL_GV_FROM', '这礼券是%s发送给您的');  
define('EMAIL_GV_MESSAGE', '信息:');  
define('EMAIL_GV_SEND_TO', '您好, %s');
//define('EMAIL_GV_REDEEM', '兑换礼卷,请点击以下的链接. 如遇到任何问题,请记下您的兑换代码%s.;

define('EMAIL_GV_TEXT_TO_REDEEM', '請观览<a href="%s">%s</a>, 在结账过程中输入您兑换代码.');
define('EMAIL_GV_LINK', '兑换礼卷请按此'); 
define('EMAIL_GV_VISIT', ' 或观览 ');  
define('EMAIL_GV_ENTER', ' 输入礼卷代码 ');  
/*
define('EMAIL_GV_FIXED_FOOTER', '如果利用上面的链接兑换礼券出现问题, ' . "\n" . 
                                '您也可以通过本商店的结账过程输入礼卷代码.' . "\n\n");
       
*/
define('EMAIL_GV_FIXED_FOOTER', '如有任何询问或疑问，您可以使用我们的在线客服，或通过电子邮件' . EMAIL_TO . '联络我们. 请记得提供您的礼卷兑换代码，以便我们处理您的询问.');
define('EMAIL_GV_SHOP_FOOTER', STORE_EMAIL_SIGNATURE);
  

?>