<?
/*
  	$Id: index.php,v 1.10 2008/01/02 02:18:54 weichen Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2003 osCommerce
	
  	Released under the GNU General Public License
*/

define('TEXT_MAIN', '这是一个默认设置的全方位购物网osCommerce项目，产品显示为演示目的, <b>任何购买的产品将无法传送，也没有将客户帐单</b>. 任何资料上看到的这些产品是被视为虚构.<br><br><table border="0" width="100%" cellspacing="5" cellpadding="2"><tr><td class="main" valign="top">' . tep_image(DIR_WS_IMAGES . 'default/1.gif') . '</td><td class="main" valign="top"><b>错误信息</b><br><br>如果有任何错误或警告讯息，提醒如上所示，请在进行前搞定.<br><br>错误讯息显示在网页的最上方，与一个完整的 <span class="messageStackError">背景</span> color.<br><br>几个执行检查，以确保一个健康的设置在您的网上商店 - 这些检查可以禁用通过修改适当的参数在底部的包括/application_top.php file.</td></tr><td class="main" valign="top">' . tep_image(DIR_WS_IMAGES . 'default/2.gif') . '</td><td class="main" valign="top"><b>Editing Page Texts</b><br><br>文本显示，在这里可以进行修改，在下列档案，对每一种语言的基础:<br><br><nobr class="messageStackSuccess">[path to catalog]/includes/languages/' . $language . '/' . FILENAME_DEFAULT . '</nobr><br><br>该文件可以进行编辑，手动，或透过管理工具与 <nobr class="messageStackSuccess">Languages->' . ucfirst($language) . '->Define</nobr> or <nobr class="messageStackSuccess">Tools->File Manager</nobr> modules.<br><br>	
案文是设置在下列方式:<br><br><nobr>define(\'TEXT_MAIN\', \'<span class="messageStackSuccess">这是一个默认设置的全方位购物网osCommerce项目...</span>\');</nobr><br><br>文中强调的绿色可能会对其进行修改-这是很重要的保持界定（ ）的t ext_main关键字. To remove the text for TEXT_MAIN completely, the following example is used where only two single quote characters exist:<br><br><nobr>define(\'TEXT_MAIN\', \'\');</nobr><br><br>More information concerning the PHP define() function can be read <a href="http://www.php.net/define" target="_blank"><u>here</u></a>.</td></tr><tr><td class="main" valign="top">' . tep_image(DIR_WS_IMAGES . 'default/3.gif') . '</td><td class="main" valign="top"><b>Securing The Administration Tool</b><br><br>It is important to secure the Administration Tool as there is currently no security implementation available.</td></tr><tr><td class="main" valign="top">' . tep_image(DIR_WS_IMAGES . 'default/4.gif') . '</td><td class="main" valign="top"><b>Online Documentation</b><br><br>Online documentation can be read at the <a href="http://wiki.oscommerce.com" target="_blank"><u>osCommerce Wiki Documentation Effort</u></a> site.<br><br>社区支持是可在 <a href="http://forums.oscommerce.com" target="_blank"><u>全方位购物网osCommerce社会支持论坛</u></a> site.</td></tr></table><br>如果你想下载的解决电源，这店，或如果你想继续向全方位购物网osCommerce项目，请访问 <a href="http://www.oscommerce.com" target="_blank"><u>support site of osCommerce</u></a>. 这店是上运行的全方位购物网osCommerce版本 <font color="#f0000"><b>' . PROJECT_VERSION . '</b></font>.');
define('TABLE_HEADING_NEW_PRODUCTS', '新产品 %s');  
define('TABLE_HEADING_UPCOMING_PRODUCTS', '即将到来的产品');  
define('TABLE_HEADING_DATE_EXPECTED', '日期预期');   
define('TABLE_HEADING_DEFAULT_SPECIALS', '特价为 %s');  

define('TABLE_HEADING_PRODUCTS', '产品');  
define('TABLE_HEADING_ETA', 'ETA');
define('TABLE_HEADING_PRICES', '价格');  
define('TABLE_HEADING_DELIVERY_TIME', 'Delivery Time');
define('TABLE_HEADING_PRODUCTS_LIST', '产品列表');
define('TEXT_DTU_TO_GAME_ACCOUNT', '游戏点数直充服务');

define('LINK_MORE_INFO', '更多详情');
define('LINK_HIDE_INFO', '隐藏详情'); 

if ($tpl == 0) {
	define('NAVBAR_TITLE', GAME_CURRENCY_TEMPLATE);
} else if ($tpl == 1) {
	define('NAVBAR_TITLE', PWL_TEMPLATE);
} else if ($tpl == 2) {
	define('NAVBAR_TITLE', CD_KEY_TEMPLATE);
} else {
	define('NAVBAR_TITLE', TEXT_HOME);
}

define('CDKEY_MESSAGE', '一旦您的订单处理，电子邮件将被邮寄 <i>instantly</i> 详细说明检索的屏幕截图您的游戏点数通过您的订单历史一页.<br><br>* 您的游戏点数 ，是全新的和尚未被使用.<br>* 您的游戏点数 ，是需要进入游戏里创建帐户.<br>* 没有物理航运在游戏盒里.<br> * 我们不提供任何下载游戏.<br><br><FONT color=red><B>IMPORTANT NOTE:</B></FONT> 游戏点数和预付卡一旦出售将不退还.');

if ( ($category_depth == 'products') || (isset($HTTP_GET_VARS['manufacturers_id'])) ) {
	define('HEADING_TITLE', '在看看我们这里有什么');  
  	define('TABLE_HEADING_IMAGE', '');
  	define('TABLE_HEADING_MODEL', '模型');
  	define('TABLE_HEADING_PRODUCTS', '产品名称'); 
  	define('TABLE_HEADING_MANUFACTURER', '制造商');
  	define('TABLE_HEADING_QUANTITY', '数量'); 
  	define('TABLE_HEADING_PRICE', '价格'); 
  	define('TABLE_HEADING_WEIGHT', '重量');  
  	define('TABLE_HEADING_BUY_NOW', '立即购买');  
  	define('TABLE_HEADING_PRODUCT_SORT', '排序');

  	define('TEXT_NO_PRODUCTS', '产品即将推出。请继续收看.');  
  	define('TEXT_NO_PRODUCTS2', '这些产品不能在这制造商找到.');  
  	define('TEXT_NUMBER_OF_PRODUCTS', '产品数量: '); 
  	define('TEXT_SHOW', '<b>Show:</b>');
  	define('TEXT_BUY', '购买1 \'');
  	define('TEXT_NOW', '\' 立即');  
  	define('TEXT_ALL_CATEGORIES', '所有类别');  
  	define('TEXT_ALL_MANUFACTURERS', '所有制造商');   
} elseif ($category_depth == '顶端') {
  	define('HEADING_TITLE', '');
} elseif ($category_depth == 'nested') {
  	define('HEADING_TITLE', '类别');  
  	define('TEXT_NO_PRODUCTS', '请选择您的类别.');  
} else {
	if ((int)$current_category_id > 0) {
  		define('HEADING_TITLE', tep_get_categories_heading_title($current_category_id, $languages_id));
  	} else {
  		define('HEADING_TITLE', '');
  	}
  	define('ERROR_PAGE_NOT_FOUND', '网页未找到');   
  	define('TEXT_NO_PRODUCTS', '请选择您的类别');   
  	define('TEXT_VIEW_PRODUCTS', '查看产品');
  	define('TEXT_NO_PRODUCTS', 'Products will be uploaded soon. Please stay tuned.');   
}

define('ERROR_REGION_HEADING_TITLE', '抱歉，');
define('ERROR_REGION_SUB_HEADING_TITLE', '此产品不对您所在的地区开放。');

define('ERROR_REGION_TEXT_OPTION_HEADING', '请尝试按下列选项之一进行操作：');
define('ERROR_REGION_TEXT_OPTION_REGION_SETTING', '更换您的');
define('ERROR_REGION_TEXT_OPTION_REGION_SETTING_LINK', '区域设定');
define('ERROR_REGION_TEXT_OPTION_BROWSE_STORE_LINK', '浏览商铺内');
define('ERROR_REGION_TEXT_OPTION_BROWSE_STORE', '的其他产品和服务');
define('ERROR_REGION_TEXT_OPTION_HOME', '进入');
define('ERROR_REGION_TEXT_OPTION_HOME_LINK', 'OffGamers首页');

/*-- Tab Content --*/
define('TAB_HEADER_HOT_DIRECT_TOP_UP', '热门直充服务');
define('TAB_HEADER_BEST_SELLING', '热销产品');

define('TAB_TEXT_PLATFORM', '游戏平台: ');
define('TAB_TEXT_GENRE', '游戏类别: ');

define('TAB_BUTTON_BUY_NOW', '立刻购买');

define('ERROR_HOT_DIRECT_TOP_UP', 'No Direct Top-Up Available.');
define('ERROR_BEST_SELLING', 'No Best Selling Games Available.');

// Product Listing : Publisher Game
define('SUPPORTED_GAME_LIST', 'SUPPORTED GAME LIST');
?>
