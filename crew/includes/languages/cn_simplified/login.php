<?php
/*
  $Id: login.php,v 1.9 2008/06/10 05:33:45 edwin.wang Exp $

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2003 osCommerce

  Released under the GNU General Public License
*/

define('NAVBAR_TITLE', '注册帐号');
define('HEADING_TITLE', '<span style="color:#2982a9;">登录</span></b> 或创建一个 ' . STORE_NAME . ' 帐户.');

define('TEXT_PASSWORD_FORGOTTEN', '忘记您的密码?');
define('TEXT_RESET_PASSWORD', '重设密码');
define('TEXT_ACTIVATE_ACCOUNT', '激活帐户');
define('TEXT_SIGN_IN_TROUBLE', '现在就联络我们的<a href="%s">客服</a>!');
define('TEXT_LOGIN_ERROR', '无法登陆，请稍后再尝试或%s.');
define('TEXT_LOGIN_EMAIL_ACTIVATE_ERROR', '该帐户未活跃.<br><a href="'. tep_href_link(FILENAME_ACCOUNT_ACTIVATE, 'aov=activate').'">'.TEXT_ACTIVATE_ACCOUNT.'</a> or <a href="'.tep_href_link(FILENAME_ACCOUNT_ACTIVATE, 'action=resend&aov=activate').'">重新发送活跃户口至电子邮件</a> %s');
define('TEXT_VISITORS_CART', '%s 登陆以后，您的"游客购物车"里的内容将与您的"会员购物车"里的内容合并。[<a href="javascript:session_win(\'' . tep_href_link(FILENAME_INFO_SHOPPING_CART) . '\');">更多详情</a>]');
define('TEXT_RETURNING_CUSTOMER', '会员登入');
define('TEXT_NEW_CUSTOMER', '新客户');
define('TEXT_CREATE_ACCOUNT_MSG', '如果您没有offgamers会员帐号 ，请创建一个.');
define('TEXT_SIGN_IN_MSG', '如果你有offgamers的会员帐号 ，请登录.');
define('TEXT_SEND_PASSWORD_MSG', '请输入您的电子邮件.');
define('TEXT_SIGNUP_SUCCESS', '恭喜， %s! 您已经成功注册为 ' . STORE_NAME . ' 会员. %s');
define('TEXT_WHAT_TODO', '下一步您想做什么?');
define('TEXT_SIGNUP_SUCCESS_END', '...或者从网页上面的菜单栏里选择您想购买的游戏.');
define('TEXT_CHECKOUT_REDIRECT_MSG', '稍后您将转到结账页面完成结账过程.');
define('TEXT_SEE_ALL_OFFER', '查看我们出售的产品');
define('TEXT_SELL_US_CURRENCIES', '向我们出售您的金币');
define('TEXT_CAPTCHA_ERROR', '错误，您输入的验证码错误。请刷新新验证码或者重新输入。');
define('TEXT_CAPTCHA_MISSING_ERROR', '错误，请输入验证码。');
define('ERROR_INCOMPLETE_LOGIN_INFO','错误：邮箱地址不存在，请重试。');

define('LINK_BACK_TO_WHERE_I_COME', '回到上一页面');
define('LINK_GOTO_HOMEPAGE', '进入首页');
define('LINK_GOTO_MY_ACCOUNT', '进入我的账户');

define('TEXT_MAIN', '如果您忘记密码，请在下面输入您的电子邮件地址，我们将向您发送一封电子邮件载有您的新密码.');
define('TEXT_NO_EMAIL_ADDRESS_FOUND', '	错误：在我们的记录中没有发现电子邮件地址，请稍后再试.');
define('EMAIL_PASSWORD_REMINDER_SUBJECT', '密码重置');
define('EMAIL_PASSWORD_REMINDER_BODY', '您的帐户密码已重置为以下。你现在可以登录到 ' . STORE_NAME . ' 与您的电子邮件地址, %s, 和新密码. 如有任何查询或协助，请使用我们的在线生活支援服务，或通过电子邮件发送查询 ' . EMAIL_TO . ". 谢谢你购物 " . STORE_NAME . ".\n\n" . '新密码: %s' . "\n");
define('EMAIL_FOOTER', STORE_EMAIL_SIGNATURE);

define('SUCCESS_PASSWORD_SENT', '成功：新密码已发送到您的电子邮件地址.');

define('EMAIL_WELCOME', '我们欢迎你 ' . STORE_NAME . ', 您的一个一站式的网上商店为您所有的游戏虚拟项目和电力的需求水准.  我们将寄给所有电子邮件，包括密码重置和秩序的更新，这个电子邮件地址.' . "\n\n");
define('EMAIL_INFO', "\n\n" . '与一帐户 ' . STORE_NAME . ", 您将会登录到我们的商店:-"."\n\n");
define('EMAIL_LINK_INFO', '你可以点击下面的链接，以激活您的帐户.' . "\n\n");
define('EMAIL_LINK_ADDRESS_INFO', "\n\n" . '如果您无法点击链接，复制并粘贴整个下面的网址进入该地址的窗口，您的网页浏览器.' . "\n\n");
define('EMAIL_MANUAL_ACTIVATE_EMAIL', "\n" . '您也可以激活您的帐户手动 <a href="' . tep_href_link(FILENAME_ACCOUNT_ACTIVATE, 'aov=activate') . '">' . tep_href_link(FILENAME_ACCOUNT_ACTIVATE, 'aov=activate') . '</a> 通过输入以下信息<br><br>E-mail Address: ');
define('EMAIL_MANUAL_ACTIVATE_CODE', "<br>" . '激活代码: ');
define('EMAIL_TEXT', '- 更新您的联系信息或更改您的帐户密码,'."\n".'- 订阅或退订我们的电子报,'."\n".'- 记载的您目前的秩序，历史和回购的历史,'."\n".'- 自动保存的内容，您的购物车为稍后结帐,'."\n".'- 收到特别的促销和折扣告示 (只适用于如果您是我们的订阅电子报).'."\n\n");
define('EMAIL_CONTACT', '如有任何查询或协助，请使用我们的在线生活支援服务，或通过电子邮件发送查询 ' . EMAIL_TO . '. 谢谢您购物 ' . STORE_NAME . ".\n\n\n");
define('EMAIL_WARNING', '注意：您收到此电子邮件，因为你已经签署了一个帐户 ' . STORE_NAME . " 与这个电子邮件地址. 如果您的电子邮件地址被用于未经您的同意，请E - mail " . '<a href="mailto:' . EMAIL_TO . '">'.EMAIL_TO.'</a> 立即取消此帐户.' . " 我们想建议你改变您的贝宝帐户密码（如果你拥有一） ，因为骗徒通常注册使用其中一种的电子邮件地址后，他们获得了相应的贝宝帐户（通过钓鱼网站，已储存的密码检索，键盘记录程序，间谍软件等）.  当然，这也可能我们的客户进入e - mail地址时打错字.  请理解我们的商店零容忍网际网路诈骗.  这就是为什么我们实施的电子邮件地址核实所有新帐户注册和没有购买可以作出不首先核实电子邮件地址." . "\n");
define('EMAIL_FOOTER', STORE_EMAIL_SIGNATURE);

define('EMAIL_GV_INCENTIVE_HEADER', '作为的一部分，我们欢迎新客户，我们向您发送了一个电子礼券价值的％ s');
define('EMAIL_GV_REDEEM', '该赎回的代码是％ s ，您可以输入兑现代码时，检查后，作出了购买');
define('EMAIL_GV_LINK', '或走这条连结 ');
define('EMAIL_COUPON_INCENTIVE_HEADER', '祝贺，让您第一次访问我们的网上商店更有价值的经验' . "\n" .
                                        '  下面是细节的折扣优惠券创造了只为你' . "\n\n");
define('EMAIL_COUPON_REDEEM', "	使用优惠券输入兑现代码，这是 %s 期间结帐, \n 当购买过后");

define('EMAIL_MAXMIND_SUBJECT', 'Maxmind GeoIP 的错误');
define('EMAIL_MAXMIND_CONTENT', 'Maxmind GeoIP 出现问题.');


	
// Mantis # 0000024 @ ************ - New label
define('HEADER_CREATE_ACCOUNT', '创建帐户');
define('EZ_COUNTRY_CODE', '国家代码:&nbsp;');
define('EZ_CONTACT_NUMBER', '手机号码:&nbsp;');
define('EZ_CONTACT_NUMBER_TEXT', '*');
define('EZ_CONTACT_NUMBER_EG', '移动电话 例如: (12) xxxxxxx &nbsp;');
define('EZ_SECRET_QUESTION', '帐户保护问题:&nbsp;');
define('EZ_SECRET_QUESTION_TEXT', '*');
define('EZ_ANSWER', '答案:&nbsp;');
define('EZ_ANSWER_TEXT', '*');
define('EZ_OPTIONAL_INFORMATION_HEADER', '可选填资料&nbsp;');
define('EZ_STATISTICS_PURPOSE', '统计目的:&nbsp;');
define('EZ_INSTANT_MESSENGER', '即时通讯帐号&nbsp;(IM):');
define('EZ_CREDITCARD_BILLING_ADDRESS', '信用卡付款账单地址:&nbsp;');
define('EZ_BILLING_ADDRESS1', '住址1:&nbsp;');
define('EZ_BILLING_ADDRESS2', '住址2:&nbsp;');

// Mantis # 0000024 @ ************ - Error message
define('ENTRY_EMAIL_JS_ERROR', "电子邮箱地址不正确。 邮箱地址必须至少包含6个字符。");
define('ENTRY_FIRSTNAME_JS_ERROR', "您的名字必须至少包含%d个字符。");
define('ENTRY_LASTNAME_JS_ERROR', "您的姓氏必须至少 包含%d个字符。");
define('ENTRY_PASSWORD_JS_ERROR', "您的密码必须至少%d个字符。");
define('ENTRY_PASSWORD_CONFIRMATION_JS_ERROR', "密码确认必须与新密码相符。");
define('ENTRY_CONTACT_NUMBER_JS_ERROR', "您的联系电话必须至少包含3个字符。");
define('ENTRY_DOB_JS_ERROR', "您的生日日期不正确。");
define('ENTRY_SECRET_QUESTION_JS_ERROR', "您必须选择任何一项帐户保护問題.");
define('ENTRY_ANSWER_JS_ERROR', "您的答案必须至少包含%d个字符。");
define('ENTRY_SIGNUP_TERMSANDCONDITION_JS_ERROR', "您必须同意我们的服务条款和隐私权政策");  // @ 200811061014


// Mantis # 0000024 @ 200812081656 - Drop down notice
define('ENTRY_EMAIL_NOTICE', "请使用正确的邮箱地址方便我们验证您的帐户。");
define('ENTRY_FIRSTNAME_NOTICE', "请输入您证件上的名字。");
define('ENTRY_LASTNAME_NOTICE', "请输入您证件上的姓氏。");
define('ENTRY_PASSWORD_NOTICE', "密码至少输入%d位字符,区分大小写");
define('ENTRY_CONFIRM_PASSWORD_NOTICE', "请重新输入您的密码。");
define('ENTRY_CONTACTNUMBER_NOTICE', "请提供给我们一个有效的电话号码，我们将打电话向您确认信息。如果您忘记了您的密码保护问题，我们同样可以打电话给您重新设置您的密保问题和答案。");
define('ENTRY_ANSWER_NOTICE', "为了确保您的账户安全，在改变，更新信息和撤销资金的过程中我们需要您的密码保护问题和答案来验证。");
define('ENTRY_IM_NOTICE', "我们将通过您所提供的IM联系, 在交货的时候联络您。");
define('ENTRY_DOB_NOTICE', "为了法律原因。");


// Mantis # 0000024 @ 200809261143 - Error message
define('ENTRY_PASSWORD_CONFIRMATION_ERROR', "您的 密码确认 必须包含至少%d字符.");
define('ENTRY_SECRET_QUESTION_ERROR', "您必须选择任何一项帐户保护問題.");
define('ENTRY_ANSWER_ERROR', "您的 答案 必须包含至少%d字符.");
define('ENTRY_CONTACT_NUMBER_ERROR', "您的 联系电话 必须包含至少%d字符.");
define('ENTRY_CONTACT_NUMBER_EXIST_ERROR', "您输入的手机号码已被另外的账户使用，请输入其他的手机号码。");
define('ENTRY_SIGNUP_TERMSANDCONDITION_ERROR', "您必须同意我们的服务条款和隐私权政策");  // @ 200811061014

//Mantis # 0000068 @ 200906221044
define('ENTRY_REFERRAL_ID', '输入转介ID:');

// Mantis # 0000024 @ 200810311721 - New text statement in new layout screen
define('TEXT_REASON_TO_JOIN_MEMBER', '您为什么需要创建一个OffGamers成员?<br>您可以在这里得到什么好处?');
define('TEXT_SEND_PASSWORD', '请输入您的邮箱地址.');
define('TEXT_FORGOTTEN_PASSWORD', '忘了密码');


// Mantis # 0000024 @ 200811051724 - New text statement in latest layout screen
define('TEXT_DO_YOU_AGREE', '您是否同意? ');
define('TEXT_I_HAVE_READ_AND_AGREE', '我已经阅读并同意 ');
define('TEXT_TERMS_AND_POLICY', '<a href="terms-of-service-i-5.ogm" target="terms">服务条款</a>, <a href="privacy-policy-i-4.ogm" target="terms">隐私权政策</a> & <a href="affiliate-policy-i-773.ogm" target="terms">联盟政策</a>.');

// survey form
define('JS_SURVEY_TITLE', '调查问卷');
define('JS_SURVEY_QUESTION', '您从哪里认识我们');

define('JS_SURVEY_ANSWER_OTHERS', '其他');
define('JS_SURVEY_ANSWER_GAMEAXIS', 'GameAxis');
define('JS_SURVEY_ANSWER_GAMES', 'Games');
define('JS_SURVEY_ANSWER_PCGAMERS', 'PC Gamers');
define('JS_SURVEY_ANSWER_PCCOM', 'PC.COM');

// Express Login
define('HEADER_ACCOUNT_LOGIN', '帐户登录');

// New Layout 2011 - Login
define('FORGOT_PASSWORD_HEADER','<h3>忘记密码？</h3>');
define('HAVING_TROUBLE_HEADER','<h3>无法注册帐号？</h3>');
define('NOT_A_MEMBER_YET', '赶快加入OffGamers，免费注册！');
define('BUTTON_REGISTER_NOW', '立即注册');
define('NOT_MEMBER_YET_HEADER','<h3>还未注册会员吗？</h3>');

// New Layout 2011 - Register
define('REGISTER_ACCOUNT_HEADER','注册帐户');
define('BUTTON_JOIN_US_NOW','现在就加入我们！');
define('REGISTER_USING_FB','或注册：');
define('RETURNING_CUSTOMER_HEADER', '<h3>我是老客户，</h3>');
define('BUTTON_LOGIN_HERE','立即登录');
define('WHY_OGM_HEADER','<h3>Why OffGamers?</h3>');
define('BUY_AND_DOWNLOAD_GAMES', 'Buy and download games');
define('JOIN_CONVERSATION', 'Join the conversation');
define('GET_SUPPORT', 'Get support for your games');
define('CHEAP_GAMES_NOT_ENOUGH', 'If cheap games aren\'t enough...');

//oauth login
define('TEXT_LOGIN_OR_SIGNUP', '使用OffGamers账号登入游戏或<a target="new" href="%s" id="register_account_link" onClick="_gaq.push([\'_trackEvent\', \'Acquisition\', \'Signup\', \'R3K\']);return targetopener(this,1);">现在注册！</a>');
define('TEXT_OR_LOGIN_WITH', '或使用以下方式登入：');
define('ERROR_UNRECOGNISE_USER_OR_PASSWORD', 'Unrecognised email or password.');
define('TEXT_FORGOT_PASSWORD', '忘记密码？');
define('TEXT_LOGIN_TNC', '通过点击注册或进入游戏，表明您已阅读并同意相关的<a href="http://www.offgamers.com/terms-of-service-i-5.ogm">服务条款</a>以及<a href="http://www.offgamers.com/privacy-policy-i-4.ogm">隐私条例</a>');
define('TEXT_LOGIN_GAME_HEADER', '登入游戏');
define('TEXT_FORGOT_PASSWORD_HEADER', '忘记密码？');
define('TEXT_ACCOUNT_LOGIN_HEADER', '会员登入');
?>