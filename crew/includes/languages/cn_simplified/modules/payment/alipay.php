<?
	/*
  	$Id: alipay.php,v 1.2 2009/02/09 05:29:30 boonhock Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	DevosC, Developing open source Code
  	http://www.devosc.com
	
  	Copyright (c) 2002 osCommerce
  	Copyright (c) 2004 DevosC.com
	
  	Released under the GNU General Public License
*/

define('MODULE_PAYMENT_ALIPAY_TEXT_TITLE', 'Alipay');
define('MODULE_PAYMENT_ALIPAY_TEXT_CART', '进入' . MODULE_PAYMENT_ALIPAY_TEXT_TITLE);
define('MODULE_PAYMENT_ALIPAY_TEXT_DISPLAY_TITLE', MODULE_PAYMENT_ALIPAY_TEXT_TITLE);

define('MODULE_PAYMENT_ALIPAY_TEXT_DESCRIPTION', '此页面主要为确认'.MODULE_PAYMENT_ALIPAY_TEXT_TITLE.'资料');

define('MODULE_PAYMENT_ALIPAY_TEXT_TITLE_PROCESSING', '款项处理中');
define('MODULE_PAYMENT_ALIPAY_TEXT_DESCRIPTION_PROCESSING', '如果此页停留超过5秒钟，请您点击贝宝登出以便完成您的单子.');
define('MODULE_PAYMENT_ALIPAY_TEXT_ERROR_MESSAGE', '您的'.MODULE_PAYMENT_ALIPAY_TEXT_TITLE.'付款失败，请重试或选择其它付款方式进行付款!');
?>