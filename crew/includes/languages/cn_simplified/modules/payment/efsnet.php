<?php
/*
  $Id: efsnet.php,v 1.3 2008/06/10 05:34:53 edwin.wang Exp $

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2003 osCommerce

  Released under the GNU General Public License
*/
  define('MODULE_PAYMENT_EFSNET_TEXT_TITLE', 'EFSNet');
  define('MODULE_PAYMENT_EFSNET_TEXT_CART', '进入 EFSNet');
  define('MODULE_PAYMENT_EFSNET_TEXT_DESCRIPTION', '测试信用卡资料:<br><br>CC#: ****************<br>限期: 任何');
  define('MODULE_PAYMENT_EFSNET_TEXT_TYPE', '信用卡公司:');
  define('MODULE_PAYMENT_EFSNET_TEXT_CREDIT_CARD_OWNER', '信用卡卡主:');
  define('MODULE_PAYMENT_EFSNET_TEXT_CREDIT_CARD_NUMBER', '信用卡号码:');
  define('MODULE_PAYMENT_EFSNET_TEXT_CREDIT_CARD_EXPIRES', '信用卡使用限期:');
  define('MODULE_PAYMENT_EFSNET_TEXT_JS_CC_OWNER', '* 信用卡卡主名字不能少于 ' . CC_OWNER_MIN_LENGTH . ' 字符.\n');
  define('MODULE_PAYMENT_EFSNET_TEXT_JS_CC_NUMBER', '* 信用卡号码不能少于 ' . CC_NUMBER_MIN_LENGTH . ' 号码.\n');
  define('MODULE_PAYMENT_EFSNET_TEXT_ERROR_MESSAGE', '信用卡处理出现错误. 请再重试一遍.');
  define('MODULE_PAYMENT_EFSNET_TEXT_DECLINED_MESSAGE', '您的信用卡不能使用. 请使用别的信用卡或联系您的银行以获知更多详情.');
  define('MODULE_PAYMENT_EFSNET_TEXT_ERROR', '信用卡错误!');
?>
