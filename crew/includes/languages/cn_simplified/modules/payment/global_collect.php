<?
/*
  	$Id: mazooma.php,v 1.2 2009/06/09 05:11:31 boonhock Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2002 osCommerce
	
  	Released under the GNU General Public License
*/

define('MODULE_PAYMENT_GLOBAL_COLLECT_TEXT_TITLE', 'Global Collect');
define('MODULE_PAYMENT_GLOBAL_COLLECT_TEXT_CART', '进入' . MODULE_PAYMENT_GLOBAL_COLLECT_TEXT_TITLE);
define('MODULE_PAYMENT_GLOBAL_COLLECT_TEXT_DISPLAY_TITLE', MODULE_PAYMENT_GLOBAL_COLLECT_TEXT_TITLE);
define('MODULE_PAYMENT_GLOBAL_COLLECT_TEXT_DESCRIPTION', '此页面主要为确认'.MODULE_PAYMENT_GLOBAL_COLLECT_TEXT_TITLE.'资料');
define('MODULE_PAYMENT_GLOBAL_COLLECT_TEXT_PAYMENT_METHOD_OPTIONS', '付款方式: ');
define('MODULE_PAYMENT_GLOBAL_COLLECT_TEXT_JS_PM', '请选择您的付款方式为 '.MODULE_PAYMENT_GLOBAL_COLLECT_TEXT_DISPLAY_TITLE.'.');
define('MODULE_PAYMENT_GLOBAL_COLLECT_ERROR_PM', '错误: 请选择您的付款方式为 '.MODULE_PAYMENT_GLOBAL_COLLECT_TEXT_DISPLAY_TITLE.'.');

define('MODULE_PAYMENT_GLOBAL_COLLECT_TEXT_ERROR_MESSAGE', '处理交易时发生错误，请重试。');
define('MODULE_PAYMENT_GLOBAL_COLLECT_TEXT_ERROR_MESSAGE_20', '处理交易时发生错误，请重试。');
define('MODULE_PAYMENT_GLOBAL_COLLECT_TEXT_ERROR_MESSAGE_25', MODULE_PAYMENT_GLOBAL_COLLECT_TEXT_ERROR_MESSAGE_20);
define('MODULE_PAYMENT_GLOBAL_COLLECT_TEXT_ERROR_MESSAGE_30', MODULE_PAYMENT_GLOBAL_COLLECT_TEXT_ERROR_MESSAGE_20);
define('MODULE_PAYMENT_GLOBAL_COLLECT_TEXT_ERROR_MESSAGE_50', "There was an issue with the connectivity between you and the bank.");
define('MODULE_PAYMENT_GLOBAL_COLLECT_TEXT_ERROR_MESSAGE_100', "抱歉，您的信用卡发卡银行可能出现问题。请联系信用卡发卡银行解决此问题后再重试。 可能原因：1) 您的信用卡已透支。 2) 您需通知信用卡发卡行预先核准您在我们网站上的购物。 3) 这是您首次使用信用卡在线购物。");
define('MODULE_PAYMENT_GLOBAL_COLLECT_TEXT_ERROR_MESSAGE_160', "出错了！无法完成下单过程，请重试。");
define('MODULE_PAYMENT_GLOBAL_COLLECT_TEXT_ERROR_MESSAGE_180', "抱歉，您输入的信息错误。请输入正确的信用卡信息。");

?>