<?php
/*
  $Id: ipayment.php,v 1.3 2008/06/10 05:34:53 edwin.wang Exp $

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2002 osCommerce

  Released under the GNU General Public License
*/

  define('MODULE_PAYMENT_IPAYMENT_TEXT_TITLE', 'iPayment');
  define('MODULE_PAYMENT_IPAYMENT_TEXT_CART', ' 进入 iPayment');
  define('MODULE_PAYMENT_IPAYMENT_TEXT_DESCRIPTION', '测试信用卡资料:<br><br>CC#: ****************<br>限期: 任何');
  define('IPAYMENT_ERROR_HEADING', '信用卡处理中出现错误');
  define('IPAYMENT_ERROR_MESSAGE', '请检查您的信用卡资料!');
  define('MODULE_PAYMENT_IPAYMENT_TEXT_CREDIT_CARD_OWNER', '信用卡卡主:');
  define('MODULE_PAYMENT_IPAYMENT_TEXT_CREDIT_CARD_NUMBER', '信用卡号码:');
  define('MODULE_PAYMENT_IPAYMENT_TEXT_CREDIT_CARD_EXPIRES', '信用卡使用限期:');
  define('MODULE_PAYMENT_IPAYMENT_TEXT_CREDIT_CARD_CHECKNUMBER', '信用卡审核号:');
  define('MODULE_PAYMENT_IPAYMENT_TEXT_CREDIT_CARD_CHECKNUMBER_LOCATION', '(位于信用卡背面)');

  define('MODULE_PAYMENT_IPAYMENT_TEXT_JS_CC_OWNER', '* 信用卡卡主名字不能少于 ' . CC_OWNER_MIN_LENGTH . ' 字符.\n');
  define('MODULE_PAYMENT_IPAYMENT_TEXT_JS_CC_NUMBER', '* 信用卡号码不能少于 ' . CC_NUMBER_MIN_LENGTH . ' 号码.\n');
?>