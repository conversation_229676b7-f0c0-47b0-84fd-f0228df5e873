<?php
/*
  	$Id: moneybookers.php,v 1.8 2008/06/10 05:34:53 edwin.wang Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2002 osCommerce
	
  	Released under the GNU General Public License
*/

define('MODULE_PAYMENT_MONEYBOOKERS_TEXT_TITLE', 'Moneybookers.com');
define('MODULE_PAYMENT_MONEYBOOKERS_TEXT_CART', '进入 Moneybookers.com');
//define('MODULE_PAYMENT_MONEYBOOKERS_TEXT_DISPLAY_TITLE', 'Moneybookers.com (卧虎游戏\' 推荐的付款方式)');	// for title displaying purpose if different from MODULE_PAYMENT_MONEYBOOKERS_TEXT_TITLE (Mo
define('MODULE_PAYMENT_MONEYBOOKERS_TEXT_DISPLAY_TITLE', 'Moneybookers.com');	// for title displaying purpose if different from MODULE_PAYMENT_MONEYBOOKERS_TEXT_TITLE (Moneybookers.com)
define('MODULE_PAYMENT_MONEYBOOKERS_TEXT_DESCRIPTION', 'Moneybookers.com');
define('MODULE_PAYMENT_MONEYBOOKERS_TEXT_CONFIRMATION', MODULE_PAYMENT_MONEYBOOKERS_MESSAGE);
define('MODULE_PAYMENT_MONEYBOOKERS_TEXT_IMPORTANT_NOTICE', '如果这是您第一次在我们的网站通过 Moneybookers 付账, 此方式必须通过 Moneybookers 的1次审核 (每一个 MB 账号). 此审核过程于周日以不超过24小时或于周末以不超过48小时完成.');
define('MODULE_PAYMENT_MONEYBOOKERS_TEXT_EMAIL_FOOTER', str_replace(array("<br>", "<BR>", "\r\n"), array("\n", "\n", ''), MODULE_PAYMENT_MONEYBOOKERS_EMAIL_MESSAGE));

define('MODULE_PAYMENT_MONEYBOOKERS_NOCURRENCY_ERROR', '安装货币不被 moneybookers.com 接纳!');
define('MODULE_PAYMENT_MONEYBOOKERS_ERRORTEXT1', '款项错误=');
define('MODULE_PAYMENT_MONEYBOOKERS_ERRORTEXT2', '&error=您通过 moneybookers.com 的款项已经失败. 请再重试一遍, 或选择其他的付款方式!');
define('MODULE_PAYMENT_MONEYBOOKERS_ORDER_TEXT', ' 单子日期: ');
define('MODULE_PAYMENT_MONEYBOOKERS_TEXT_ERROR', '款项错误!');
define('MODULE_PAYMENT_MONEYBOOKERS_TRANSACTION_FAILED_TEXT', '您的'.MODULE_PAYMENT_MONEYBOOKERS_TEXT_TITLE.'付款失败，请重试或选择其它付款方式进行付款!');
define('MODULE_PAYMENT_MONEYBOOKERS_ORDER_COMMENT1', '');
define('MODULE_PAYMENT_MONEYBOOKERS_ORDER_COMMENT2', '当您联系我们在线客服，请使用 '.STORE_NAME.' 您的单子号以询问更多详情. 这样能促使我们更快以及更有效地协助您. 您也能够在 Moneybookers.com > My Account > history 的 "Reference" 一栏中看见您的单子号. 谢谢.');

// Moneybookers Account Verification E-mail
define('TEXT_MB_EMAIL_VERIFY_INSTRUCTION_CONTENT', "\n\n\n" . 'Please kindly verify this e-mail account by clicking on the following link or copy and paste the link to your browser.  This link will confirm that you are the owner of this e-mail address and we will not ask for any Moneybookers information in the page.  Please understand that this step is taken to protect Moneybookers account owners from unauthorized charges and this is a one-time process unless you change your Moneybookers e-mail.' . "\n\n");
define('TEXT_MB_PAYMENT_MAKE_EMAIL_CONTENT1', 'You have made a Moneybookers payment of ');
define('TEXT_MB_PAYMENT_MAKE_EMAIL_CONTENT2', ' to OffGamers using the following account:' . "\n");
define('TEXT_MB_EMAIL_VERIFY_ENDING_CONTENT', "\n\n\n" . 'If you have not made any purchase at OffGamers, please report this fraudulent <NAME_EMAIL> immediately and we also advise you to report the case to Moneybookers at their website and change your Moneybookers password immediately.  Thank you for shopping at ' . STORE_NAME . '.');
define('TEXT_MB_VERIFICATION_TITLE', 'Moneybookers Account Verification');
define('TEXT_NOTICE_OF_MB_VERIFICATION_SEND_TITLE', 'Moneybookers Account Verification Notice');
define('TEXT_MB_EMAIL_VERIFICATION_NOTICE_HEADING_CONTENT', "\n\n" . 'In order to protect the owner of the Moneybookers account from fraudulent use, a verification e-mail with "Subject: ');
define('TEXT_MB_EMAIL_VERIFICATION_NOTICE_ENDING_CONTENT', '" has been sent to the e-mail address shown above.  Please kindly check the e-mail account and follow the instructions stated to verify your Moneybookers e-mail account.  Thank you for helping us to serve you better.');
define('EMAIL_FOOTER', STORE_EMAIL_SIGNATURE);
define('EMAIL_CONTACT', 'For any enquiries or assistance, please use our Online Live Support service or e-mail your enquiries to ' . EMAIL_TO . '. Thank you for shopping at ' . STORE_NAME . ".\n\n\n");
define('REMARK_MB_VERIFICATION_EMAIL_SENT', 'Moneybookers verification e-mail sent.');
define('REMARK_MB_VERIFICATION_EMAIL_NOTICE_SENT', 'Moneybookers verification e-mail notice sent.');
?>