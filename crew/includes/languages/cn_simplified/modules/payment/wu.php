<?php
/*
  	$Id: wu.php,v 1.2 2008/06/10 05:34:53 edwin.wang Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2002 osCommerce
	
  	Released under the GNU General Public License
*/

define('MODULE_PAYMENT_WU_TEXT_RECEIVER', '接受人 ');
define('MODULE_PAYMENT_WU_TEXT_CART', '确认单子');
define('MODULE_PAYMENT_WU_TEXT_SENDER', '发送人 ');
define('MODULE_PAYMENT_WU_ENTRY_MCTN', 'MCTN: ');
define('MODULE_PAYMENT_WU_ENTRY_AMOUNT', '数量: ');
define('MODULE_PAYMENT_WU_ENTRY_CURRENCY', '金额: ');
define('MODULE_PAYMENT_WU_ENTRY_FIRST_NAME', '名字: ');
define('MODULE_PAYMENT_WU_ENTRY_LAST_NAME', '姓氏: ');
define('MODULE_PAYMENT_WU_ENTRY_ADRESS', '住址: ');
define('MODULE_PAYMENT_WU_ENTRY_ZIP', '邮政编码: ');
define('MODULE_PAYMENT_WU_ENTRY_CITY', '居住城市: ');
define('MODULE_PAYMENT_WU_ENTRY_COUNTRY', '居住国家: ');
define('MODULE_PAYMENT_WU_ENTRY_PHONE', '联系电话: ');
define('MODULE_PAYMENT_WU_ENTRY_QUESTION', '秘密问题: ');
define('MODULE_PAYMENT_WU_ENTRY_ANSWER', '秘密答案: '); 

define('MODULE_PAYMENT_WU_TEXT_TITLE', '西联');
define('MODULE_PAYMENT_WU_TEXT_DISPLAY_TITLE', '西联');	// for title displaying purpose if different from MODULE_PAYMENT_WU_TEXT_TITLE
define('MODULE_PAYMENT_WU_TEXT_DESCRIPTION', '西联款项模式');
define('MODULE_PAYMENT_WU_TEXT_CONFIRMATION', MODULE_PAYMENT_WU_MESSAGE);
//define('MODULE_PAYMENT_WU_TEXT_EMAIL_FOOTER', "您的款项将付给:\n\n" . MODULE_PAYMENT_WU_ENTRY_FIRST_NAME . MODULE_PAYMENT_WU_RECEIVER_FIRST_NAME . " - " . MODULE_PAYMENT_WU_ENTRY_LAST_NAME . MODULE_PAYMENT_WU_RECEIVER_LAST_NAME . " - "  . MODULE_PAYMENT_WU_ENTRY_ADRESS . MODULE_PAYMENT_WU_RECEIVER_ADRESS . " - "  . MODULE_PAYMENT_WU_ENTRY_ZIP . MODULE_PAYMENT_WU_RECEIVER_ZIP . " - "  . MODULE_PAYMENT_WU_ENTRY_CITY . MODULE_PAYMENT_WU_RECEIVER_CITY . " - "  . MODULE_PAYMENT_WU_ENTRY_COUNTRY . MODULE_PAYMENT_WU_RECEIVER_COUNTRY . " - "  . MODULE_PAYMENT_WU_ENTRY_PHONE . MODULE_PAYMENT_WU_RECEIVER_PHONE . "\n\n" . '您的单子将会在我们收到 MCTN 款项号码后开始运输.');
define('MODULE_PAYMENT_WU_TEXT_EMAIL_FOOTER', str_replace(array("<br>", "<BR>", "\r\n"), array("\n", "\n", ''), MODULE_PAYMENT_WU_EMAIL_MESSAGE));
?>