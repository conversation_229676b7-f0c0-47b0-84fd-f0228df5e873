<?
/*
  $Id: my_order_history.php,v 1.15 2008/12/10 02:06:22 keepeng.foong Exp $

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2003 osCommerce

  Released under the GNU General Public License
*/

define('HEADER_ORDER_REPORT', '我的订单详情');
define('HEADER_TITLE_MY_ORDER_HISTORY', '单子状态');  

define('HEADING_TITLE', HEADER_TITLE_MY_ORDER_HISTORY);
define('NAVBAR_TITLE', HEADER_TITLE_MY_ORDER_HISTORY);

define('TEXT_ALL_GAMES', '* 所有游戏 *');
define('TEXT_ALL_ORDER_STATUS', '* 所有单子状态 *');

define('TEXT_TIME_LEFT', '剩余时间 (分钟)');
define('TEXT_ORDERS_FOUND', '搜索的纪录记录 ');
define('TEXT_RESULT_SEARCH', '搜索结果 %s');


define('TEXT_PRODUCT_TYPE', '产品类型');
define('TABLE_HEADING_RECEIVER_CHAR_NAME', '接货ID');
define('TEXT_REQUEST_QTY', '草单数量');
define('TEXT_SENT_QTY', '已发数量');

define('TEXT_EXPIRES', '单子取消时间:');
define('TEXT_MINUTES', '分钟');

define('TEXT_PENDING_DELIVERY', '等待发送');
define('TEXT_PENDING_PAYMENT', '等待结款');
define('TEXT_RECEIVED', '已被接收');

define('TEXT_PENDING', '草單');
define('TEXT_PROCESSING', '結單');
define('TEXT_COMPLETED', '已被確認結單');
define('TEXT_CANCELLED', '已被刪除');

define('TEXT_GAME_CURRENCY', '游戏金币');
define('TEXT_HIGH_LEVEL_ACCOUNT', '游戏帐号');

define('TEXT_ERROR_MSG', '错误信息');
define('TEXT_ERROR_TRYAGAIN', '发生错误! 请重试。');
define('TEXT_ERROR_INVALID_QTY', '数额错误。 请重试。');
define('TEXT_ERROR_NO_SS_AFTER_UPLOADED', '没发货后截图。 请重试。');
define('TEXT_ERROR_NO_SS_BEFORE_UPLOADED', '没发货前截图。 请重试。');
define('TEXT_SECONDS_COUNTDOWN', '(%s秒后刷新)');
 
define('ERROR_UPLOAD_FAILED', '上载截图失败，请稍后再重试。');
 
define('TEXT_ADDITIONAL_COMMENTS', '另外的评论');
define('TEXT_CHARACTER_NAME', '发送者ID');
define('TEXT_CHARACTER_NAME_IN_PREPARATION', '<span class="redIndicator">正在准备中。。。</span>');
 
define('TEXT_CHARACTER_NAME_IN_PREPARATION_QUEUE', '<span class="redIndicator">请稍等, 还有%s位就到您</span>');
define('TEXT_CHARACTER_NAME_HELP', '请给我们提供您的ID,以便我们安排接收。');
define('TEXT_CONTACT_NAME', '姓名');
define('TEXT_CONTACT_NO', '联络号码');
define('TEXT_DELIVERY_TIME', '发送时间');
define('TEXT_DELIVERY_TIME_HELP', '提醒: 超出的单子将被删除');
define('TEXT_FILE_SIZE', '(体积: %d KB)');
define('TEXT_OF', 'of');
define('TEXT_ORDER_REFERENCE', '单子参考');
define('TEXT_SCREENSHOT_BEFORE_SEND_GOLD','发货前截图');
define('TEXT_SCREENSHOT_AFTER_SENT_GOLD','发货后截图');
define('TEXT_TOTAL_PRICE', '总数');
define('TEXT_UPLOAD_SS', '上载截图');
define('TEXT_PROCEED', '请按照规定在游戏中转移地点，我们将在五分钟之内联络您的游戏角色。如果我们在2分钟之内无法联络您的游戏角色，订单将被取消，你将不得不与我们设立一个新的订单.');

define('TEXT_USER_REQUIREMENT', '请上传交易截图两张备查. 要求如下：');
define('TEXT_FIRST_SS_TITLE', '1. 交易前截图:');
define('TEXT_FIRST_SS_DESC', '交易时的截图需显示双方交易界面, 双绿单绿均可, 同时界面要显示交易金币数量, 交易时间, 包裹金币数量, 交易双方处于正常交易状态（而非一方显示繁忙）');
define('TEXT_SECOND_SS_TITLE', '2. 交易后截图:');
define('TEXT_SECOND_SS_DESC', '交易后截图界面要显示"Trade complete", 交易时间、包裹剩余金币等');
define('TEXT_SS_REF_TITLE', '3. 截图可参照范例:');
define('TEXT_SS_REF_DESC', '<a href="http://kb.offgamers.com/zhcn/?p=576">http://kb.offgamers.com/zhcn/?p=576</a>');
define('TEXT_REQUIREMENT_TITLE', '4. 截图要求:');
define('TEXT_REQUIREMENT_DESC', 'jpg格式, 每张大小不得超过 800kb.');
define('TEXT_MORE_THAN_2_PHOTO', '5. 凡是交易超过2张截图的交易，请将剩余的截图经QQ， MSN 或 电邮support<EMAIL> 转送给我们');

define('TEXT_HELP_ORDER_STATUS_SELECT', '审核：在【单子状态】等待接货ID出现，然后进入游戏交易（当面交易方式/游戏内邮寄方式）后，必须确认填上【已发数量】，否则款项将不被支入【结存余额】。<br><br>处理：等待卧虎游戏员工确认确实已发数量，少或多于【草单数量】的单子，将会被视为免费金币单子。<br><br>完成：单子在完成后的15分钟，单子款项会自动支入供应商的【结存余额】。供应商可以在15分钟后，自行选择提款支入先前已设好的银行账号。<br><br>取消：单子已被供应商或卧虎游戏取消。');

define('SUCCESS_ORDER_HISTORY_ORDER_CANCEL_SUCCESS', '成功: 单子已经成功取消。');
define('ERROR_ORDER_HISTORY_ORDER_CANCEL_FAILED', '错误: 单子取消不成功。');

define('JS_CONFIRM_CANCEL_ORDER', '确定取消这单子？');
define('JS_NO_SS_BEFORE_TRADE', '请上载发货前截图');
define('JS_NO_SS_AFTER_TRADED', '请上载发货后截图');
define('JS_INVALID_SS_FILE_EXT', '上载截图错误, 只允许 *.jpg, *.jpeg 文档名');

define('LINK_SEND_STOCK_FAQ', '[发货必知常识]');

define('EMAIL_TEXT_CLOSING', '谢谢支持 '.STORE_NAME.'.');	// unique
define('ENTRY_RECEIVER_CHAR_NAME', '接货ID');

define('TABLE_HEADING_RECEIVER_CHAR_NAME', '游戏角色名称');  
define('TABLE_HEADING_DELIVERY_METHOD', '交易类型');
define('TABLE_HEADING_ORDER_NUMBER', '订单编号.');
define('TABLE_HEADING_GAME', '游戏');  
define('TABLE_HEADING_SERVER', '服务器');  
define('TABLE_HEADING_REQUEST_QTY', '提交数量');  
define('TABLE_HEADING_SENT_QTY', '已发数量');  
define('TABLE_HEADING_AMOUNT', '金额');  
define('TABLE_HEADING_STATUS', '状态');  
define('TABLE_HEADING_RECEIVER_CHAR', '正在接收角色');
define('TABLE_HEADING_DELIVERED_ID', '发货ID');
define('TABLE_HEADING_CONFIRM_QUANTITY', '已发数量');

define('ENTRY_RECEIVER_CHAR_NAME', '正在接受角色帐号');  
define('ENTRY_ORDER_STATUS', '订单状态');  
define('ENTRY_ORDER_NUMBER', '订单 #');  
define('ENTRY_PRODUCT_TYPE', '产品类型');  
define('ENTRY_GAME', '游戏');
define('ENTRY_START_DATE', '开始日期');   
define('ENTRY_END_DATE', '结束日期');

define('ORDERS_LOG_UNLOCK_OWN_ORDER', '##ul_1##');

define('POPUP_HEADER_BUYBACK_ORDER_CANCEL', '取消单子');
define('POPUP_TEXT_DETAILS', '详情 (如有)');
define('POPUP_LIMIT_CHARACTERS', '(%s 字符)');
define('POPUP_LIMIT_CHARACTERS_ERROR', '文字字符串过长');
?>