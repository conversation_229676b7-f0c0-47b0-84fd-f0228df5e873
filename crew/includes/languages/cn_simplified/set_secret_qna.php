<?php

// Mantis # 0000024 @ 200810241055 - Add popup message & form's text fields
define('HEADER_TITLE_SECRET_QUESTION', '注册机密问题还有答案');
define('HEADER_TITLE_CHANGE_SECRET_QNA', '更改密保问题及答案');

define('SET_SECRET_QUESTION', '机密问题:&nbsp;');
define('SET_SECRET_QUESTION_TEXT', '*');
define('SET_ANSWER', '答案:&nbsp;');
define('SET_ANSWER_TEXT', '*');
define('ENTRY_SECRET_QUESTION_ERROR', '请从列表选择一项机密问题');
define('ENTRY_ANSWER_ERROR', "您的答案必须包含至少 %d 个字符.");

define('TEXT_REGISTER_SECRET_QUESTION_AND_ANSWER','请注册您的 <a href='. tep_href_link(FILENAME_SET_SECRET_QNA) .'>机密问题还有答案</a> 来更改你的信息'); 

// Show done message @ 200810311207
define('ENTRY_UPDATE_DONE', '您的机密问答成功被更新了.');
define('ENTRY_SAVE_DONE', '您的机密问答成功被保存了.');

define('HIDDEN_PBLINK', '<input type="hidden" id="hid_value" name="hid_value">'); 
define('POPUP_TEXT_CHANGE_TO_SECRET_QUESTION_AND_ANSWER_NOTICE', tep_db_input('<div id="popupTitle"><p>机密问答保安系统建设同知</p></div><div id="popupMesgTop"><p>亲爱的贵宾客户,</p></div><p id="popupMesg">为了提高您户口的安全,PIN代碼系統将被我们最新推介的机密问答保安系统代替,让您更方便,更放心的使用本商店的网络与服务. 您可在户口管理页内建设机密问答保安, 代替您现在使用的PIN代码.<br><br>清在<span class="redIndicator">15天之内</span>建设您的机密问答. 如果建设在<span class="redIndicator">15天内</span>没被完成, 我们的系统将自动冻结您的户口. 更多关于机密问答保安系统的详情,请参阅我们的资料中心或联络我们的客服. 为保障您的帐号安全，谢谢您的合作.<br></p><div id="popupMesgBottom"><p>OffGamers队</p></div><div id="popupBottom"><p><a href="%s" class="subModelLink" onclick="document.getElementById(\'hid_value\').value=\''.stripslashes($_SESSION['ssqna']='popuplink').'\'">建设机密问答保安</a><br><a href="javascript:;" %s class="subModelLink">稍后再建设</a><br>剩下的时间: %d 天</p></div>'));
define('POPUP_TEXT_CHANGE_TO_SECRET_QUESTION_AND_ANSWER_TITLE', '机密问答保安系统建设同知');
//##

// Mantis # 0000024 @ 200810271754 - Add form's text fields
 
define('TEXT_PIN_NUMBER', 'PIN代码');
define('TEXT_HELP_PIN_NUMBER', '请提供您的PIN代码');
define('TEXT_PINNUMBER_REQUIRED', '请提供PIN代码以便更新此字段: %s');
define('TEXT_IS_MANDATORY', '显着<span class="redIndicator">*</span>的字段必定要填');
define('TEXT_SAVE', '保存');
define('TEXT_MUST_LOGIN', '进入此部分前,请登陆您的账户.');

define('BUTTON_SAVE', '保存');
define('BUTTON_REQUEST_TOKEN', '获取验证码');
define('TEXT_SECRET_QNA_ISSET', '为了安全起见，您的密保问题及密码不能在此网页更改。请联系<a href="'.tep_href_link(FILENAME_CUSTOMER_SUPPORT, '', 'SSL').'">客服代表</a> 请求更改。');

define('ENTRY_SECRET_ANSWER_QUESTION_NOTICE', '您的密保问答是保护您账户安全的保护手段。当您在提取货款或修改OffGamers账户内的信息时，您都需填写密保问答。');
define('ENTRY_SECRET_ANSWER_NOTICE', '请保护好您的密保问答，切勿告知他人。');
define('ENTRY_CONTACT_NUMBER_EXIST_ERROR', "您提供的手机号码已使用或不存在。请提供另一个手机号码。");
define('TEXT_CHANGE_PHONE_NUMBER', '(不是您的手机号码? <a id="link_edit_phone_number" href="' . tep_href_link(FILENAME_ACCOUNT_EDIT) . '">请点击修改</a>.)');

define('EMAIL_USER_RESET_PIN_SUBJECT', implode(' ', array(EMAIL_SUBJECT_PREFIX, '您的安全码已重设')));
define('EMAIL_USER_RESET_PIN_BODY', '您的新安全码是%s。 这安全码会在您收到这邮件的24小时过期。');

define('TEXT_SECRET_QNA', '验证码');
define('TEXT_DORMANT_ACCOUNT_QNA_REQUEST', '超过90天没有登录帐户，需要使用安全令牌重新激活帐户才可以购物。<br><br>索取的验证码将在10分钟内失效。若欲索取新的验证码每天只限不超过3次。');
define('TEXT_FORGOT_QNA', '无法验证或索取安全码？ 请<a href="' . tep_href_link(FILENAME_CUSTOMER_SUPPORT) . '">联系</a>我们求助。');
define('ENTRY_QNA_MISMATCH_ANSWER_ERROR', '不好意思，验证码错误，请重试。');

define('TEXT_REQUEST_TOKEN_MSG', '您的验证码将在10分钟后失效。同一天内，申请验证码不应超过3次。');
define('TEXT_REQUEST_TOKEN_SUCCESS_MSG', '验证码已通过短信息发送至您注册的手机号码%s。');
define('TEXT_REQUEST_TOKEN_REUSE_MSG', '请输入当前的验证码。');
define('TEXT_REQUEST_TOKEN_FAIL_MSG', '您的验证码使用次数已超过最大限制，请24小时后重试。');
define('TEXT_REQUEST_TOKEN_HELP_MSG', '验证码已发送至您的手机。如果您提供的手机号码无效或已丢失，请联系我们的<a href="%s" class="whiteText">客服</a>寻求帮助。');
?>