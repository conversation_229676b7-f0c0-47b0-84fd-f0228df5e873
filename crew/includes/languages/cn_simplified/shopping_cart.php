<?php
/*
  $Id: shopping_cart.php,v 1.10 2007/11/14 04:57:51 weichen Exp $

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2002 osCommerce

  Released under the GNU General Public License
*/

define('NAVBAR_TITLE', '购物车内容');
define('HEADING_TITLE', '我的购物车有什么?');
define('TABLE_HEADING_UNIT_PRICE', '单位价格');
define('TABLE_HEADING_AMOUNT', '总额');
define('TABLE_HEADING_QUANTITY', '数量.');
define('TABLE_HEADING_MODEL', '模型');
define('TABLE_HEADING_CATEGORIES', '类别');
define('TABLE_HEADING_PRODUCTS', '产品列表');
define('TABLE_HEADING_TOTAL', '总计');
define('TABLE_HEADING_POINTS_EARNED', 'OP');

define('TEXT_CART_EMPTY', '您的购物车是空的.');

define('SUB_TITLE_SUB_TOTAL', '小计:');
define('SUB_TITLE_TOTAL', '总计:');

define('OUT_OF_STOCK_CANT_CHECKOUT', '显著' . STOCK_MARK_PRODUCT_OUT_OF_STOCK . '的产品沒有您要购买的数量.请输入较小的数量.  谢谢.');
define('OUT_OF_STOCK_CAN_CHECKOUT', '显著' . STOCK_MARK_PRODUCT_OUT_OF_STOCK . ' 的产品沒有您要购买的数量.<br>您可以随意购买产品,但必须查看我们的存货数量.');

define('INFO_INACTIVE_PRODUCT', '抱歉，该产品已售完，请将其从您的购物车内移除再结账!');
define('INFO_EMPTY_SELECTIONS', '您的选择列表是空的!');
define('INFO_OVERWEIGHT', '此产品已超过最大重量!');

define('TEXT_TOTAL', '总共 :');
?>