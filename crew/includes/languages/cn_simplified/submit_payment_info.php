﻿<?php

define('HEADING_TITLE', '离线付款信息表格');
define('NAVBAR_TITLE', '我的简介');

define('NAVBAR_TITLE_1', '我的账户');
define('NAVBAR_TITLE_2', '购买订单历史记录');
define('NAVBAR_TITLE_3', '购买订单详情');
define('NAVBAR_TITLE_4', '离线付款信息');

define('TITLE_ORDER_NUMBER', '订单号码:');
define('TITLE_PAYMENT_METHOD', '付款方式:');
define('TITLE_PAYMENT_INFO', '付款信息:');

define('JS_ERROR_MESSAGE_1', '请填写“付款信息”。');
define('MESSAGE_OFFLINE_PAYMENT_INFO_SENT_SUCCESS', '您的离线付款信息已成功提交。');
define('MESSAGE_OFFLINE_PAYMENT_INFO_SENT_FAIL', '您的离线付款信息无法提交。');
define('MESSAGE_UNAUTHORISED_SENT_OFFLINE_PAYMENT_INFO', '此订单不属于您，您无权发送离线付款信息。');

define('MESSAGE_NRP_USING', '您已填写付款信息。');

define('EMAIL_SUBJECT', '离线付款信息的提交: 订单 ID # %s');
define('EMAIL_TEMPLATE_OF_OFFLINE_PAYMENT_INFO_SUBMITTED', 
'亲爱的 %s,'."\n\n".
'感谢您提交您的付款详情 ：'."\n\n".
'订单日期: %s'."\n".
'订单ID: %s'."\n".
'订单数量: %s'."\n".
'订单状态: %s'."\n".
'付款方式: %s'."\n".
'客户姓名: %s'."\n\n".
'该订单的付款信息已更新：'."\n\n".
'%s'."\n\n".
'如果以上信息有误，请立即发邮件到********************* 联系我们。 我们会在每周一至周五9：00-17：00收取款项。 任何在周末支付的款项会在下一个工作日收取。 如果成功收取款项后，我们将发一封邮件给您。'."\n\n".
'如果您要咨询或寻求帮助，请联系我们的在线客服平台或者发送您想咨询的问题到 <EMAIL> 。 与我们联系时， 请不要忘记附上您的订单号码， 以方便我们尽快解决您的问题。 再次感谢您在OffGamers 购物。'."\n\n\n".
'祝您好运，'."\n".
'OffGamers团队'."\n\n".
'***************************************************************'."\n\n".
'OffGamers-您的游戏联盟'."\n".
'24/7 客户服务&即时发货'."\n\n".
'***************************************************************'."\n\n".
'在线客服平台： http://support.offgamers.com'."\n".
'E-mail: <EMAIL>'."\n".
'USA (Delaware): (302) 565-4955'."\n".
'United Kingdom (London): (020) 8816-8318'."\n");

?> 