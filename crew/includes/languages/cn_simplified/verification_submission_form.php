﻿<?php

define('HEADING_TITLE', '身份验证提交表');
define('FORM_TAB_TITLE', '提交表');
define('FORM_TAB_SUB_TITLE', '为确保身份验证过程安全顺畅，请确保您所提交的所有文件都正确无误。');

define('REMARK_UPLOAD_SIZE_LIMIT', '(<i>最大内存500kb,只限jpg,gif或png格式</i>)');

define('ENTRY_PURCHASE_AUTHORIZATION_FORM', '购买登记表格。');
define('ENTRY_UTILITY_BILL', '与您的账单信息相符的水电费账单。');
define('ENTRY_PHOTO_IDENTIFICATION', '带照片的有效证件（未过期），且证件上的姓名地址与您的信用卡上的账单地址相符。');
define('ENTRY_CREDIT_CARD_FRONT', '显示信用卡正面前4位数字和后4位数字的复印件。<a href="http://kb.offgamers.com/zhcn/?p=141" target="_blank">点击这里查看验证指南</a>');
define('ENTRY_CREDIT_CARD_BACK', '显示信用卡背面签名的复印件。（为安全起见，请掩盖CVV和信用卡背面的印刷数字。）<a href="http://kb.offgamers.com/zhcn/?p=141" target="_blank">点击这里查看验证指南</a>');
?>