<?
/*
  $Id: wpcallback.php,v MS1a 2003/04/06 21:30
  Author : 	<PERSON> (grae<PERSON>@conkie.net)
  Title: WorldPay Payment Callback Module V4.0 Version 1.4

  Revisions:
	Version MS1a Cleaned up code, moved static English to language file to allow for bi-lingual use,
	        Now posting language code to WP, Redirect on failure now to Checkout Payment,
			Reduced re-direct time to 8 seconds, added MD5, made callback dynamic 
			NOTE: YOU MUST CHANGE THE CALLBACK URL IN WP ADMIN TO <wpdisplay item="MC_callback">
	Version 1.4 Removes boxes to prevent users from clicking away before update, 
			Fixes currency for Yen, 
			Redirects to Checkout_Process after 10 seconds or click by user
	Version 1.3 Fixes problem with Multi Currency
	Version 1.2 Added Sort Order and Default order status to work with snapshots after 14 Jan 2003
	Version 1.1 Added Worldpay Pre-Authorisation ability
	Version 1.0 Initial Payment Module

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2003
  Released under the GNU General Public License
*/

define('NAVBAR_TITLE', 'WorldPay');  
define('WP_TEXT_HEADING', 'WorldPay的回应:');  
define('HEADING_TITLE', '付款失败');  
//define('HEADING_TITLE', '付款失败' . STORE_NAME);  
define('WP_TEXT_SUCCESS', '成功收到您的付款.');  
define('WP_TEXT_SUCCESS_WAIT', '<span class="errorText">请稍等...</span> 您的订单正在进行中.<br>如果您没被自动导向，10秒后，请点击继续.');
define('WP_TEXT_FAILURE', '您的付款失败,订单处理失败. 请点击“继续”返回到付款方式选择网页, 如需要进一步的援助, 请联络 <a href="mailto:'.EMAIL_TO.'">'.EMAIL_TO.'</a>.');

define('WP_TEXT_FAILURE_WAIT', '<b><font color="#FF0000">请稍等...</font></b><br>如果您没被自动导向，10秒后，请点击继续.');

define('MODULE_PAYMENT_WORLDPAY_TEXT_ERROR_MESSAGE', '您的信用卡处理失败,请稍后再试.');
?>