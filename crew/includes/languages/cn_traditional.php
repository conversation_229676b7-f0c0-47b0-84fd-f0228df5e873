<?php

/*
  $Id: english.php,v 1.15 2005/04/04 07:08:34 weichen Exp
 */

// look in your $PATH_LOCALE/locale directory for available locales
// or type locale -a on the server.
// Examples:
// on RedHat try 'en_US'
// on FreeBSD try 'en_US.ISO_8859-1'
// on Windows try 'en', or 'English'

@setlocale(LC_TIME, 'en_US.ISO_8859-1');

define('DATE_FORMAT_SHORT', '%m/%d/%Y'); //這是用於字符串格式()
define('DATE_FORMAT_LONG', '%A, %d %B, %Y'); //這是用於字符串格式()
define('DATE_FORMAT', 'm/d/Y'); //這是用於日期()
define('DATE_TIME_FORMAT', DATE_FORMAT_SHORT . ' %H:%M:%S');

define('LOCAL_STORE_NAME', 'OffGamers');

// #CHAVEIRO16# BEGIN PHPBB2
define('BOX_BBINDEX', 'tmf.Forum');
// #CHAVEIRO16# END PHPBB2
//CGDiscountSpecials start
define('PRICES_LOGGED_IN_TEXT', '登陸以後才能查看價格!');
//CGDiscountSpecials end
// if USE_DEFAULT_LANGUAGE_CURRENCY時所用的語言改變
define('LANGUAGE_CURRENCY', 'USD');
define('LANGUAGE_ZONES', '4');

//全球參賽作品為<html> tag
define('HTML_PARAMS', 'dir="LTR" lang="cn" xmlns="http://www.w3.org/1999/xhtml" xmlns:fb="http://www.facebook.com/2008/fbml"');

//字符的網頁和電子郵件
define('CHARSET', 'UTF-8');
define('EMAIL_CHARSET', 'gb2312');

//網頁標題
define('TITLE', STORE_NAME);

//開始的最新消息
define('TABLE_HEADING_LATEST_NEWS', '最新消息');
define('ALL_PRODUCTS_LINK', '輸入遊戲關鍵字');

// header text in includes/header.php
define('HEADER_TITLE_CREATE_ACCOUNT', '註冊帳戶');
define('HEADER_TITLE_MY_ACCOUNT', '我的帳戶');
define('HEADER_TITLE_CART_CONTENTS', '查看購物車');
define('HEADER_TITLE_CHECKOUT', '結賬');
define('HEADER_TITLE_TOP', '首頁');
define('HEADER_TITLE_CATALOG', '產品目錄');
define('HEADER_TITLE_LOGOFF', '登出');
define('HEADER_TITLE_LOGIN', '我的帳戶');
define('HEADER_TITLE_MY_FAVOURITE_LINKS', '我最喜愛的鏈接');
define('HEADER_TITLE_MY_PAYMENT_HISTORY', '賬戶明細');
define('HEADER_TITLE_MY_VIP_ACCOUNT', 'VIP帳戶');
define('HEADER_TITLE_MY_VIP_INVENTORY_UPDATE', '庫存批量管理列表');
define('HEADER_TITLE_MY_VIP_REGISTER_SERVER', '登記庫存列表');
define('HEADER_TITLE_MY_VIP_ORDERS_HISTORY', '訂單操作');
define('HEADER_TITLE_MY_VIP_REPORT', '銷售報告');
define('HEADER_TITLE_SHOW_ALL', '查看所有');

define('HEADER_RECOMMENDED_PAYMENT_GATEWAY', '付款方式');
define('HEADER_GAME_NEWS_STORE_ANNOUNCEMENT', '遊戲新聞/商店公佈');

define('TEXT_LINK_SECURE_LOGIN', '安全方式登錄');

define('TEXT_SECURITY_CODE', '驗證碼');
define('TEXT_LATEST_NEWS', '最新新聞');
define('TEXT_NOTICE_BOARD', '佈告欄');
define('TEXT_GAME_INFO', '遊戲資訊');
define('TEXT_TIPS', 'Tips');
define('TEXT_POLL_QUESTION', '評估我們的服務.');
define('TEXT_ACCOUNT_FREEZE', '抱歉，您的帳號已被封鎖，請聯繫網站客服謝謝');
define('TEXT_PIN_NUMBER', 'PIN號碼');
define('TEXT_PINNUMBER_REQUIRED', '需要PIN號碼才能更新: %s');

define('TEXT_ACCOUNT_TOTAL', '賬號總額');
define('TEXT_ACTIVE', '正常');
define('TEXT_BACK', '返回');
define('TEXT_CONFIRM', '確定');
define('TEXT_FINISHED', '完成，請現在新建我的帳號.');
define('TEXT_INACTIVE', '已滿');
define('TEXT_MAIN_PLEASE_SELECT_GAME', '《---請選擇遊戲，查詢報價');
define('TEXT_MESSAGE_LIST_CLOSE', '已提交訂單已飽和，請您在倒數到0後再行提交。（現在倒數：<b>%d</b>）');
define('TEXT_MESSAGE_BREAK_TIME', '臥虎網站暫停交易，服務器將在倒數時間到來時重新啟動，給您帶來不便敬請諒解（倒數時間：%02d小時%02d分鐘）');
define('TEXT_WEBSITE_NOTE_DESC', '<ol><li>遊戲提示您：與本公司交易諮詢時，請認准網站公佈的採購QQ，避免有人冒充臥虎遊戲採購客服進而給您帶來不必要的損失。<li>供應商註冊臥虎遊戲帳號時會設定相應的密保問答（Q&A），這是我們提供相應安全服務的最後保障，請妥善保管並牢記答案；如因密保問答（Q&A）丟失或洩漏導致您帳號資金和信息損失，臥虎公司概不負責，請千萬注意。<li>臥虎遊戲針對遊戲服務器暫時維護（重啟）時的交易金幣回流現象，提醒用戶務必在維護（重啟）後交易，如因用戶不遵守此規定造成的交易損失，臥虎遊戲概不負責。<li>臥虎遊戲投訴與意見信箱：<a href="mailto:<EMAIL> ?subject=臥虎遊戲投訴或意見信箱"><span class="redIndicator"><EMAIL></span></a>接到投訴後12小時內回复解決意見</li></ol>');
define('TEXT_OPTIONAL', '(可不填)');
define('TEXT_VIP_ORDER_FLOW', '流程說明');

define('TEXT_NEXT', '下一頁');
define('TEXT_SAVE', '儲存');
define('TEXT_SEARCH', '尋找');
define('TEXT_SUBMIT', '提交');
define('TEXT_UPDATE', '更新');
define('TEXT_UPDATE_AND_NEXT', '更新>>');
define('TEXT_WITHDRAW', '提款');

define('TEXT_ORDERS', "訂單");
define('TEXT_PENDING', "審核中");
define('TEXT_PROCESSING', "處理中");

define('JAVASCRIPT_ENFORCEMENT', '請在瀏覽器中啟用JavaScript以便體驗到我們網站所有的自定義功能,包括購買產品.');

// footer text in includes/footer.php
define('FOOTER_TEXT_REQUESTS_SINCE', '請求自');
define('OGM_SPECIAL_NOTICE', '<div style="font-size:15px;font-weight:bold;">看看我們的新玩意!</div>
                        	<div style="">近期我們已對網站商舖中的工具欄進行了調整。<br />
                        	 在新工具欄中，您可進行如下操作： <br />
                        	 - 登陸或註冊OffGamers帳戶(也適用於Facebook Connect！)。<br />
                        	 - 設置/更新您的區域設置（國家，貨幣和語言）。 <br />
                        	 - 查看/更新您購物車內的物品或結賬付款。<br />
                        	</div>');

//customer order comment remark
define('TEXT_VIP_BUYBACK_ORDER_REFERENCE_REMARK', 'Delivered via VIP Order: ##BO##%d##');
define('TEXT_BUYBACK_ORDER_REFERENCE_REMARK', 'Delivered via Buyback Order: ##BO##%d##');

define('TEXT_BUYBACK_ORDER_ITEMS_DELIVERED_REMARK', 'The following items have been delivered:');
define('TEXT_BUYBACK_ORDER_ITEMS_DEDUCTED_REMARK', 'The following items have been deducted:');

//案文日，月，年
define('DAY', '日');
define('DAY2', '天');
define('MONTH', '月');
define('YEAR', '年');
define('HOURS', '小時');
define('MINUTES', '分鐘');
define('SECONDS', '秒');

//案文性別
define('MALE', '男性');
define('FEMALE', '女性');
define('MALE_ADDRESS', '先生');
define('FEMALE_ADDRESS', '女士');

//電子郵件賀卡
define('EMAIL_SEPARATOR', '------------------------------------------- -----------');
define('EMAIL_GREET_MR', '親愛的. %s先生,' . "\n\n");
define('EMAIL_GREET_MS', '親愛的. %s女士,' . "\n\n");
define('EMAIL_GREET_NONE', '親愛的%s,' . "\n\n");

//電子郵件回購
define('EMAIL_BUYBACK_UPDATE_SUBJECT', implode(' ', array(EMAIL_SUBJECT_PREFIX, "回購訂單#%s最新情況")));
define('EMAIL_BUYBACK_UPDATE_BODY', '回購訂單摘要:' . "\n" . EMAIL_SEPARATOR . "\n");
define('EMAIL_BUYBACK_UPDATE_ORDER_NUMBER', '訂單號碼: %s');
define('EMAIL_BUYBACK_UPDATE_DATE_ORDERED', '訂單日期: %s');
define('EMAIL_BUYBACK_UPDATE_STATUS', '狀況: %s');
define('EMAIL_BUYBACK_UPDATE_FOOTER', "\n\n" . STORE_EMAIL_SIGNATURE);

define('EMAIL_BUYBACK_UPDATE_CUSTOMER_EMAIL', '客戶的電郵地址: %s');
define('EMAIL_BUYBACK_UPDATE_PRODUCTS', '產品');
define('EMAIL_BUYBACK_UPDATE_ORDER_TOTAL', '總計: %s %s');
define('EMAIL_NEW_BUYBACK_STATUS', '狀況:待定中');
define('EMAIL_NEW_BUYBACK_ORDER_NUMBER', '回購訂單號碼: %s');
define('EMAIL_NEW_BUYBACK_DATE_ORDERED', '回購訂單日期: %s');
define('EMAIL_NEW_BUYBACK_CUSTOMER_EMAIL', '電子郵件地址: %s');

define('EMAIL_BUYBACK_PRESALES_SUBJECT', '回購服務器通知');
define('EMAIL_LOCAL_STORE_EMAIL', '<EMAIL>');
define('EMAIL_BUYBACK_PRESALES_TITLE', '感謝您的支持！你現在可以登錄到您的' . STORE_NAME . '並出售您已選的服務器遊戲貨幣.');
define('EMAIL_BUYBACK_PRESALES_SUMMARY_TITLE', '回購服務器摘要:');
define('EMAIL_BUYBACK_PRESALES_PRODUCT', '服務器%s');
define('EMAIL_BUYBACK_PRESALES_MIN_QTY', '最低: %d');
define('EMAIL_BUYBACK_PRESALES_MAX_QTY', '最高: %d');
define('EMAIL_BUYBACK_PRESALES_COMMENTS', '注意：' . "\n\n" . STORE_NAME . '只提供遊戲貨幣回購給成員，而是先到先得，不要猶豫和錯過了出售遊戲貨幣的機會。謝謝您！');
define('EMAIL_LOCAL_STORE_EMAIL_SIGNATURE', "\n" . STORE_NAME . "\n" . '******************************* ******************' . "\n" . 'E-mail: <a href="mailto:' . EMAIL_LOCAL_STORE_EMAIL . '">' . EMAIL_LOCAL_STORE_EMAIL . '</a>');

define('EMAIL_TEXT_STATUS_UPDATE_TITLE', '');
define('EMAIL_TEXT_ORDER_NUMBER', '訂單號碼:');
define('EMAIL_TEXT_DATE_ORDERED', '訂單日期:');
define('EMAIL_TEXT_INVOICE_URL', '詳述的發票：');
define('EMAIL_TEXT_SUBJECT', '更新訂單#%d');
define('EMAIL_TEXT_PARTIAL_DELIVERY', '');
define('EMAIL_TEXT_CLOSING', '如果您要諮詢或尋求幫助，請聯繫我們的在線客服平台或者發送您想諮詢的問題到' . EMAIL_TO . '。與我們聯繫時，請不要忘記附上您的訂單號碼，以方便我們盡快解決您的問題。再次感謝您在' . STORE_NAME . '購物。 \n');

//案文的出生日期，例如
define('DOB_FORMAT_STRING', 'mm/dd/yyyy');

// Mantis # 0000024 @ ************ - Add new boxes
define('BOX_HEADING_ACCOUNT_LEFT_BOX_MY_ACCOUNT', '我的帳戶');
define('BOX_HEADING_ACCOUNT_LEFT_BOX_MY_PROFILE', '我的簡介');
define('BOX_HEADING_ACCOUNT_LEFT_BOX_STORE_CREDITS', '購物代金券');
define('BOX_HEADING_ACCOUNT_LEFT_BOX_BUYER', '買方');
define('BOX_HEADING_ACCOUNT_LEFT_BOX_SELLER', '賣方');
define('BOX_HEADING_ACCOUNT_LEFT_BOX_VIP_SELLER', 'VIP賣方');
define('BOX_HEADING_ACCOUNT_LEFT_BOX_AFFILIATE', '合作聯盟');

define('MY_ACCOUNT_ACCOUNT_OVERVIEW_LINK', '概況');
define('MY_ACCOUNT_EDIT_PROFILE_LINK', '編輯簡介');
define('MY_ACCOUNT_FACEBOOK_CONNECT', 'Facebook Connect');
define('MY_ACCOUNT_CHANGE_SECRET_QNA_LINK', '更改密保問答');

define('MY_ACCOUNT_NOTIFICATION_SETTINGS_LINK', '通知設置');
define('MY_ACCOUNT_MANAGE_NEWSLETTERS_LINK', '管理電子月刊');
define('MY_ACCOUNT_OFFGAMERS_POINTS', 'OP');
define('MY_ACCOUNT_MY_ORDERS_LINK', '我的訂單');
define('MY_ACCOUNT_VERIFY_PHONE_NUMBER_LINK', '手機號碼驗証');
define('MY_ACCOUNT_VERIFY_EMAIL_LINK', '電子郵址驗證');
define('MY_ACCOUNT_VERIFICATION_SUBMISSION_FORM', '身份驗證提交表');

define('MY_ACCOUNT_STORE_CREDITS_HISTORY_LINK', '購物代金券歷史記錄');
define('MY_ACCOUNT_STORE_CREDITS_CONVERSION_LINK', '購物代金券轉換');
define('MY_ACCOUNT_STORE_CREDITS_TOPUP_LINK', '充值購物代金券');

define('MY_ACCOUNT_BUY_ORDERS_HISTORY_LINK', '購買訂單歷史紀錄');
define('MY_ACCOUNT_MY_ORDERS_LINK', '我的訂單');
define('MY_ACCOUNT_STORE_CREDITS_LINK', '購物代金卷');
define('MY_ACCOUNT_SPENDING_LIMIT_LINK', '支出限額');

define('MY_ACCOUNT_SELL_ORDER_HISTORY_LINK', '出售訂單歷史記錄');
define('MY_ACCOUNT_WITHDRAW_STATEMENT_LINK', '提款明細');
define('MY_ACCOUNT_WITHDRAW_MONEY_LINK', '提款');
define('MY_ACCOUNT_PAYMENT_STATUS_LINK', '付款狀態');
define('MY_ACCOUNT_FAVORITE_SERVERS_LINK', '快捷下單');

// header text in includes/boxes/my_account.php VIP SELLER
define('MY_ACCOUNT_VIP_INVENTORY_UPDATE', '庫存批量管理列表');
define('MY_ACCOUNT_VIP_REGISTER_SERVER', '登記庫存列表');
define('MY_ACCOUNT_VIP_ORDERS_HISTORY', '訂單操作');
define('MY_ACCOUNT_VIP_REPORT', '銷售報告');
// M#0000024

define('MY_ACCOUNT_AFFILIATE_OVERVIEW_LINK', '合作聯盟概況');
define('MY_ACCOUNT_INVITE_FRIENDS_LINK', '邀請好友');

// Mantis # 0000024 @ ************ - My Account Phase 2
define('TITLE_MY_PROFILE', '我的簡介');
define('IMAGE_BUTTON_SAVE_PROFILE', '保存簡介');
define('MY_ACCOUNT_CURRENT_ORDERS', '當前訂單');
define('MY_ACCOUNT_COMPLETED_ORDERS', '已完成訂單');
define('MY_ACCOUNT_CANCELLED_ORDERS', '已取消訂單');

// My Account Home alert messages
define('TEXT_CLICK_HERE', '點擊這裡');
define('TEXT_LIVE_SUPPORT', '客戶服務');
define('TEXT_LIVE_CHAT', '&nbsp;提交問題單');

// login box text in includes/boxes/loginbox.php
define('BOX_HEADING_LOGIN_BOX_MY_ACCOUNT', '我的帳戶');
define('LOGIN_BOX_MY_ACCOUNT', '帳戶概觀');
define('LOGIN_BOX_ACCOUNT_EDIT', '編輯資料');
define('LOGIN_BOX_ACCOUNT_PAYMENT_EDIT', '支付方式');
define('LOGIN_BOX_ACCOUNT_STORE_CREDIT', '購物代金券');
define('LOGIN_BOX_ADDRESS_BOOK', '編輯地址');
define('LOGIN_BOX_VERIFY_EMAIL', '確認電子郵件<span class="requiredInfo">*<span>');
define('LOGIN_BOX_ACTIVATE_ACCOUNT', '啟動帳戶<span class="requiredInfo">*<span>');
define('LOGIN_BOX_ACCOUNT_HISTORY', '查看訂單歷史');
define('LOGIN_BOX_PRODUCT_NOTIFICATIONS', '產品通知');
define('LOGIN_BOX_BUYBACK_ORDER_HISTORY', '查看回購歷史');
define('LOGIN_BOX_BUYBACK_FAVOURITE', '查看最喜愛的回購');
define('LOGIN_BOX_ACCOUNT_ACTIVATION', '啟動帳戶');
define('LOGIN_BOX_PASSWORD_FORGOTTEN', '忘記密碼?');
define('LOGIN_BOX_HEADING', '登陸或注冊');
define('LOGIN_BOX_LOGIN_SELECTION', '我已經是OffGamers會員，我想現在登陸。');
define('LOGIN_BOX_CREATE_ACCOUNT_SELECTION', '我是新客戶，我想創建新賬戶。');
define('LOGIN_BOX_SEND_PASSWORD_SELECTION', '請輸入您的郵箱地址，我們會將您的密碼發送至您的郵箱。');
define('LOGIN_EMAIL', '電子郵件地址');
define('LOGIN_PASSWORD', '密碼');
define('LOGIN_PASSWORD_NEW', '新密碼');
define('LOGIN_PASSWORD_NEW_CONFIRMATION', '確認新密碼');
define('LOGIN_RETURN', '返回登錄');
define('CONFIRM_LOGIN_PASSWORD_DESC', '為安全起見，您用購物代金券付款時，需要輸入OffGamers登錄名及密碼。');

define('TEXT_WITHDRAWABLE_CREDIT', '可取回金額');
define('HEADER_TITLE_MY_WITHDRAW_MONEY', '提款');

// categories box text in includes/boxes/categories.php
define('BOX_HEADING_CATEGORIES', '類別');

// manufacturers box text in includes/boxes/manufacturers.php
//define('BOX_HEADING_MANUFACTURERS', '製造商');
define('BOX_HEADING_MANUFACTURERS', '製造商');

// whats_new box text in includes/boxes/whats_new.php
define('BOX_HEADING_WHATS_NEW', '新消息');

// quick_find box text in includes/boxes/quick_find.php
define('BOX_HEADING_SEARCH', '快速搜索');
define('BOX_SEARCH_TEXT', '用關鍵字尋找你的產品.');
define('TEXT_ALL_CATEGORIES', '所有類別');
define('BOX_SEARCH_ADVANCED_SEARCH', '高級搜索');

// specials box text in includes/boxes/specials.php
define('BOX_HEADING_SPECIALS', '特銷');

// reviews box text in includes/boxes/reviews.php
define('BOX_HEADING_REVIEWS', '評語');
define('BOX_REVIEWS_WRITE_REVIEW', '寫關於這個產品的評語!');
define('BOX_REVIEWS_NO_REVIEWS', '目前沒有任何產品評論');
define('BOX_REVIEWS_TEXT_OF_5_STARS', '%s之5星!');

// shopping_cart box text in includes/boxes/shopping_cart.php
define('BOX_HEADING_SHOPPING_CART', '購物車');
define('SHOPPING_CART_BOX_EDIT_CART', '編輯購物車');
define('BOX_SHOPPING_CART_EMPTY', '沒有產品');
define('TEXT_LATEST_PRODUCT_ADDED', '已添加到您的購物車:');

// order_history box text in includes/boxes/order_history.php
define('BOX_HEADING_CUSTOMER_ORDERS', '訂單歷史');

// best_sellers box text in includes/boxes/best_sellers.php
define('BOX_HEADING_BESTSELLERS', '最好賣');
define('BOX_HEADING_BESTSELLERS_IN', '最好賣在<br>&nbsp;&nbsp;');

// notifications box text in includes/boxes/products_notifications.php
define('BOX_HEADING_NOTIFICATIONS', '通知');
define('BOX_NOTIFICATIONS_NOTIFY', '<b>%s</b>更新時通知我');
define('BOX_NOTIFICATIONS_NOTIFY_REMOVE', '<b>%s</b>更新時不必通知我');

// manufacturer box text
define('BOX_HEADING_MANUFACTURER_INFO', '製造商Info');
define('BOX_MANUFACTURER_INFO_HOMEPAGE', '%s網頁');
define('BOX_MANUFACTURER_INFO_OTHER_PRODUCTS', '其他產品');

// languages box text in includes/boxes/languages.php
define('BOX_HEADING_LANGUAGES', '語言');

// currencies box text in includes/boxes/currencies.php
define('BOX_HEADING_CURRENCIES', '選擇您的貨幣');

define('BOX_HEADING_HELP_DESK', '幫助台');

// polling box text in includes/boxes/polling.php
define('BOX_HEADING_POLLING', 'offgamers投票');

// information box text in includes/boxes/information.php
define('BOX_HEADING_INFORMATION', '資料');
define('BOX_INFORMATION_CONDITIONS', '使用條件');
define('BOX_INFORMATION_SHIPPING', '運送和退貨');
define('BOX_INFORMATION_ABOUT_US', '關於我們');
define('BOX_HEADING_LINKS', '鏈接');

define('BOX_INFORMATION_NEWS', '最新動態與促銷活動');
define('BOX_INFORMATION_TOS', '服務條款');
define('BOX_INFORMATION_PRIVACY', '隱私權政策');
define('BOX_INFORMATION_FAQ', '常見問題解答');
define('BOX_INFORMATION_CONTACT_WEB', '聯繫我們的表格');
define('BOX_INFORMATION_CONTACT', '聯繫我們');
define('BOX_INFORMATION_PROMOTION', '促銷');
define('BOX_INFORMATION_DISCLAIMER', '免責聲明');
define('BOX_LINKS', '鏈接');

// tell a friend box text in includes/boxes/tell_a_friend.php
define('BOX_HEADING_TELL_A_FRIEND', '推薦給朋友');
define('BOX_TELL_A_FRIEND_TEXT', '把產品推薦給您認識的人.');

// regional setting
define('BOX_HEADING_REGIONAL_SETTING', '區域設定');
define('TEXT_REGIONAL_COUNTRY', '區域');
define('TEXT_LANGUAGE', '語言');
define('TEXT_REGIONAL_NOTE', '您的設置將作為下次訪問時的默認區域設置。');

//BEGIN allprods modification
define('BOX_INFORMATION_ALLPRODS', '查看所有產品');
//END allprods modification

define('BOX_CHANGE_PASSWORD', '更改密碼');
define('BOX_IMPORTANT_NOTICE', '重要通知');
define('BOX_ERROR_INVALID_ANSWER', '答案不正確。');
define('BOX_ERROR_INVALID_LAST4DIGIT', '號碼後4位數字不正確。');
define('BOX_SUCCESS_VALIDATE_LAST4DIGIT', '您的安全碼已經成功發送至您註冊的郵箱。');
define('BOX_CHANGE_CONTACT_NOTICE', '自2012年5月16日起，我們的電話驗證系統，將不再支持固定電話或網絡電話號碼驗證。為了進一步保障您賬戶的安全性，請符合以下條件的OffGamers會員在您的賬戶中，更新手機號碼:-<br>A.賬戶中聯繫方式登記為固定電話的客戶<br>B.有多個賬戶，並且共享電話號碼的客戶<br><br>這一舉措是為了配合我們即將開放的最新電話驗證系統，由此給您帶來的不便，敬請諒解！');
define('BOX_UPDATE_YOUR_MOBILE_NUMBER', '更新您的手機號碼');
define('BOX_CHANGE_CONTACT_FOR_SECURITY_REASONS', '為安全起見，請將您賬戶內的電話號碼，更新為您目前使用的手機號碼以完成您的賬戶驗證。您可以通過填寫電話號碼的後4位或者提交密保問答，來完成此過程。');
define('BOX_CHANGE_CONTACT_FILL_LAST4DIGIT', '鑑於安全認證，請輸入您當前電話號碼的最後4位數字:');
define('BOX_CHANGE_CONTACT_FILL_LAST4DIGIT_DESC', '如果您之前填寫的手機號碼已經丟失或者不再使用，請申請安全口令。安全碼將發送到您註冊的電子郵箱中。');
define('BOX_CHANGE_CONTACT_FOOTER_DESC', '如果您有任何疑問，請不要猶豫，立即<a href="' . tep_href_link(FILENAME_CUSTOMER_SUPPORT, '', 'SSL') . '">聯繫我們</ A>。我們的客服很榮幸為您服務。');
define('BOX_CHANGE_CONTACT_OR', '或');

// checkout procedure text
define('CHECKOUT_BAR_DELIVERY', '送貨<br>資料');
define('CHECKOUT_BAR_INFO', '結賬資料');
define('CHECKOUT_BAR_PAYMENT', '付款<br>資料');
define('CHECKOUT_BAR_CONFIRMATION', '確認');
define('CHECKOUT_BAR_FINISHED', '完成');

// pull down default text
define('TEXT_ALL_PAGES', '全部');
define('PULL_DOWN_DEFAULT', '請選擇');
define('TYPE_BELOW', '下面鍵入');
define('TEXT_REFRESH_WHEN_CHANGE', '(改變時網頁將刷新)');
define('PULL_DOWN_DEFAULT_GAME_CARD', '選擇遊戲點卡價格');

// JavaScript的信息
define('JS_ERROR', '處理表格過程中發生錯誤.\n\n請做出以下改正:\n\n');

define('JS_REVIEW_TEXT', '* \'Review Text\'必須有至少' . '10' . '字符.\n');
define('JS_REVIEW_RATING', '*產品評估需要評分.\n');
define('JS_REVIEW_CUSNAME', '*您必須輸入您的\'First Name\'.\n');
define('JS_ERROR_NO_PAYMENT_MODULE_SELECTED', '*請為您的訂單選擇付款方式.\n');
define('JS_ERROR_NO_SHIPPING_MODULE_SELECTED', '*請為您的訂單選擇送貨方式r.\n');

define('JS_ERROR_SUBMITTED', '這種表格已提交.請按下確定,然後請耐心等待這個過程.');

define('JS_ERROR_PRODUCT_EMPTY', '請選擇遊戲點卡價格');

define('ERROR_NO_PAYMENT_MODULE_SELECTED', '請為您的訂單選擇付款方式.');
define('ERROR_NO_PAYMENT_MODULE_NEEDED', '因為可以從您的商店信用中扣除總金額，所以您不必選擇付款方式.');
define('ERROR_CART_COMMENTS_REQUIRED', '請在您的訂單裡填寫所需的留言!');
define('ERROR_SIGNUP_CART_COMMENTS_REQUIRED', '請在登記表格填寫所需的留言!');

define('ERROR_PLS_ENTER_ACCOUNT_NAME', '請輸入帳戶名稱');
define('ERROR_PLS_ENTER_CHAR_NAME', '請輸入遊戲角色名');
define('ERROR_PLS_ENTER_PASSWORD', '請輸入密碼');
define('ERROR_EMAIL_VERIFICATION_CODE', '驗證碼必須包含12位數字。');
define('ERROR_WRONG_PASSWORD', '密碼錯誤，請重試。');

define('CATEGORY_COMPANY', '公司詳情');
define('CATEGORY_VERIFY_EMAIL', '電子郵件驗證');
define('CATEGORY_PERSONAL', '您的個人詳細資料');
define('CATEGORY_ADDRESS', '您的地址');
define('CATEGORY_CONTACT', '您的聯絡資料');
define('CATEGORY_OPTIONS', '選項');
define('CATEGORY_PASSWORD', '您的密碼');

define('ENTRY_ACCOUNT_NAME', '賬號名');
define('ENTRY_WOW_ACCOUNT_NAME', '魔獸帳號名');
define('ENTRY_ADD_CHARACTER_NAME', '填寫角色的名稱');
define('ENTRY_CHARACTER_INGAME_TIME', '<td class="smallText"><div style="padding:5px 10px 5px 20px;float:left">距離登錄時間:</div></td><td class="smallText ">%s <span class="redIndicator">*</span>小時%s</td>');
define('ENTRY_CHARACTER_INGAME_DURATION', '<td class="smallText"><div style="padding:5px 10px 5px 20px;float:left">遊戲裡持續時間:</div></td><td class=" smallText">%s小時%s</td>');
define('TEXT_CHARACTER_INGAME_NOTE', '這是您結賬後,將登錄遊戲的時間');
define('TEXT_CHARACTER_INGAME_HOUR_NOTE', '這是您在遊戲內的時間');
define('ENTRY_ORDER_GUYA_ACCOUNT', '<td class="smallText" style="padding:5px 10px 5px 20px;" align="left" nowrap>帳戶名稱:</td><td class="smallText" >%s <span class="redIndicator">*</span></td>');
define('ENTRY_ORDER_GUYA_PWD', '<td class="smallText" style="padding:5px 10px 5px 20px;" align="left" nowrap>密碼:</td><td class="smallText"> %s <span class="redIndicator">*</span></td>');
define('ENTRY_QNA', '<td class="smallText" style="padding:5px 10px 5px 30px;" align="left" nowrap>答案:</td><td class="smallText">%s <span class="redIndicator">*</span></td>');
define('ENTRY_ORDER_GUYA_WOW_ACCOUNT_INFO', '<td class="smallText" style="padding:5px 10px 5px 20px;" align="left">&nbsp;</td><td class="smallText">購買魔獸金幣, 請填寫您的Battle.net賬戶.</td>');
define('ENTRY_ORDER_GUYA_WOW_ACCOUNT_INFO_TEXT', '購買魔獸金幣, 請填寫您的Battle.net賬戶。');
define('ENTRY_ORDER_GUYA_WOW_ACCOUNT', '<td class="smallText" style="padding:5px 10px 5px 20px;" align="left" nowrap>魔獸帳號名:</td><td class="smallText">%s 可選</td>');
define('ENTRY_ORDER_GUYA_WOW_ACCOUNT_INFO2', '<td class="smallText"><div style="padding:5px 10px 5px 20px;float:left"></div></td><td class="smallText">僅適用於魔獸.</td>');
define('ENTRY_ORDER_GUYA_WOW_ACCOUNT_INFO2_TEXT', '僅適用於魔獸。');
define('TEXT_ORDER_FACE_TO_FACE_NOTE', '如果您選擇面對面或者擺攤交易方式，請選擇並調整在線時長。<b>(<font color="red">注意</font>:“我在線”按鈕啟動後，我們才會為您尋找貨源)</b><br /><br /><span class="redIndicator">注：購買<b>無盡之夜</b>時，請填寫您的角色名字和物品名字"(例如：playername@handle(Item Name) "購買鑽石星芒的交易方式只能通過拍賣場交易。</span><br /><br /><span class="redIndicator">注：購買<b>暗黑3</b>時，請填寫您的battletag (例如：ABC#1234) 並非賬戶名。</span>');
define('TEXT_ORDER_GUYA_NOTE', '我們將登錄到您的帳戶然後把貨幣轉讓去您所指定的角色.');
define('TEXT_ORDER_MAIL_NOTE', '我們將貨幣寄去您所指定的角色的遊戲郵箱裡<br /><br /><span class="redIndicator">注：購買行會戰爭2時，請填寫您的display name (例如：ABC.1234)。</span>');
define('TEXT_ORDER_OPEN_STORE_NOTE', '一旦您將交易方式更改為面對面交易或擺攤交易,請選擇您的在線時段 <b>(<font color="red">注意</font>: 只有您點擊"我在線"後，我們才會開始為您尋找貨源)</b>');
define('TEXT_ORDER_CHANGE_DEFAULT_CHARACTER', '更改您的默認遊戲角色');
define('TEXT_PERSONAL_INFO_REQUIRED', '在付款前請填寫您的 %s，以便我們驗證和處理您的訂單。（在以後的訂單中就無需再這樣做）');
define('ENTRY_VERIFY_EMAIL', '電子郵件確認');
define('ENTRY_VERIFY_EMAIL_TEXT', '*');
define('ENTRY_VERIFY_EMAIL_ERROR', '電子郵件確認必須符合您的電子郵件.');
define('ENTRY_COMPANY', '公司名稱:');
define('ENTRY_COMPANY_ERROR', '');
define('ENTRY_COMPANY_TEXT', '');
define('ENTRY_GENDER', '性別:');
define('ENTRY_GENDER_ERROR', '請選擇您的性別.');
define('ENTRY_GENDER_TEXT', '*');
define('ENTRY_CUSTOMER_IDENTIFY_NUMBER', '身份證號碼：');
define('ENTRY_FIRST_NAME', '名字:');
define('ENTRY_FIRST_NAME_ERROR', '您的名字必須至少' . ENTRY_FIRST_NAME_MIN_LENGTH . '字符.');
define('ENTRY_FIRST_NAME_TEXT', '*');
define('ENTRY_LAST_NAME', '姓氏:');
define('ENTRY_LAST_NAME_ERROR', '您的姓氏必須至少' . ENTRY_LAST_NAME_MIN_LENGTH . '字符.');
define('ENTRY_LAST_NAME_TEXT', '*');
define('ENTRY_DATE_OF_BIRTH', '出生日期:');
define('ENTRY_DATE_OF_BIRTH_OVER_ERROR', '出生日期錯誤，請重新輸入');
define('ENTRY_DATE_OF_BIRTH_FUTURE_ERROR', '出生日期錯誤，你已進入一個未來的日期');
define('ENTRY_DATE_OF_BIRTH_ERROR', '您的出生日期必須包含一個有效的日期.');
define('ENTRY_DATE_OF_BIRTH_ERROR_1', '你必須選擇一個');
define('ENTRY_DATE_OF_BIRTH_ERROR_2', '從');
define('ENTRY_DATE_OF_BIRTH_ERROR_3', '列表');
define('ENTRY_DATE_OF_BIRTH_ERROR_4', '年必須包含4位數字.');
define('ENTRY_DATE_OF_BIRTH_TEXT', '* (eg. 05/21/1970)');
define('ENTRY_EMAIL_ADDRESS', '郵箱地址:');
define('ENTRY_EMAIL_ADDRESS_ERROR', '您的電子郵件地址，需至少' . ENTRY_EMAIL_ADDRESS_MIN_LENGTH . '字符.');
define('ENTRY_EMAIL_ADDRESS_CHECK_ERROR', '您的電子郵件地址似乎有錯誤-請作出任何必要的更正.');
define('ENTRY_EMAIL_ADDRESS_ERROR_EXISTS', '您的電子郵件地址存在我們的記錄，請<a href="' . tep_href_link(FILENAME_LOGIN) . '">登入</a>或如果您忘記了密碼<a href=" ' . tep_href_link(FILENAME_PASSWORD_FORGOTTEN) . '">按此</a>.');
define('ENTRY_EMAIL_ADDRESS_TEXT', '*');
define('ENTRY_STREET_ADDRESS', '街道地址:');
define('ENTRY_STREET_ADDRESS_ERROR', '您的街道地址必須至少' . ENTRY_STREET_ADDRESS_MIN_LENGTH . '字符.');
define('ENTRY_STREET_ADDRESS_TEXT', '*');
define('ENTRY_SUBURB', '郊區:');
define('ENTRY_SUBURB_ERROR', '');
define('ENTRY_SUBURB_TEXT', '');
define('ENTRY_POST_CODE', '郵編:');
define('ENTRY_POST_CODE_ERROR', '您的郵遞區必須至少' . ENTRY_POSTCODE_MIN_LENGTH . '字符.');
define('ENTRY_POST_CODE_TEXT', '*');
define('ENTRY_CITY', '城市:');
define('ENTRY_CITY_ERROR', '您所在的城市必須至少' . ENTRY_CITY_MIN_LENGTH . '字符.');
define('ENTRY_CITY_TEXT', '*');
define('ENTRY_STATE', '州/省:');
define('ENTRY_STATE_ERROR', '您的州必須至少' . ENTRY_STATE_MIN_LENGTH . '字符.');
define('ENTRY_STATE_ERROR_SELECT', '請從列表選擇一個州.');
define('ENTRY_STATE_TEXT', '*');
define('ENTRY_COUNTRY', '國家:');
define('ENTRY_LOCATION', '位置:');
define('ENTRY_COUNTRY_ERROR', '請從列表選擇一個國家.');
define('ENTRY_LOCATION_ERROR', '請從列表選擇一個位置.');
define('ENTRY_COUNTRY_TEXT', '*');
define('ENTRY_TELEPHONE_NUMBER', '手機號碼:');
define('ENTRY_TELEPHONE_NUMBER_ERROR', '您的電話號碼必須至少' . ENTRY_TELEPHONE_MIN_LENGTH . '字符.');
define('ENTRY_TELEPHONE_NUMBER_TEXT', '*');
define('ENTRY_FAX_NUMBER', '傳真號碼:');
define('ENTRY_FAX_NUMBER_ERROR', '');
define('ENTRY_FAX_NUMBER_TEXT', '');
define('ENTRY_NEWSLETTER', '電子報:');
define('ENTRY_NEWSLETTER_TEXT', '訂閱我們的月刊，將獲得特別優惠，獨家促銷活動！');
define('ENTRY_NEWSLETTER_YES', '訂閱');
define('ENTRY_NEWSLETTER_NO', '退訂');
define('ENTRY_NEWSLETTER_ERROR', '');
define('ENTRY_PASSWORD', '密碼:');
define('ENTRY_REMEMBER_ME', '記住我的帳號');
define('ENTRY_PASSWORD_ERROR', '您的密碼必須至少' . ENTRY_PASSWORD_MIN_LENGTH . '字符.');
define('ENTRY_PASSWORD_ERROR_NOT_MATCHING', '密碼確認必須符合您的密碼.');
define('ENTRY_PASSWORD_TEXT', '*');
define('ENTRY_PASSWORD_CONFIRMATION', '確認密碼:');
define('ENTRY_PASSWORD_CONFIRMATION_TEXT', '*');
define('ENTRY_PASSWORD_CURRENT', '當前密碼:');
define('ENTRY_PASSWORD_CURRENT_TEXT', '*');
define('ENTRY_PASSWORD_CURRENT_ERROR', '您的密碼必須至少' . ENTRY_PASSWORD_MIN_LENGTH . '字符.');
define('ENTRY_PASSWORD_NEW', '新密碼:');
define('ENTRY_PASSWORD_NEW_TEXT', '*');
define('ENTRY_PASSWORD_NEW_ERROR', '您的新密碼必須至少' . ENTRY_PASSWORD_MIN_LENGTH . '字符.');
define('ENTRY_PASSWORD_NEW_ERROR_NOT_MATCHING', '密碼確認必須符合您的新密碼.');
define('PASSWORD_HIDDEN', '-隱藏-');
define('ENTRY_SERIAL_ERROR', '請輸入序號');
define('ENTRY_SERIAL_CHECK_ERROR', '您的序列中必須包含12個字符.');
define('ENTRY_MISMATCH_ANSWER_ERROR', '驗證碼不正確，請重新輸入。');
define('ENTRY_MISMATCH_ANSWER_RESEND_LINK', '否則，請點擊<a href="%s">這裡</a>。');
define('ENTRY_CHARACTER_NAME', '角色名');
define('ENTRY_REBATE', '總共回扣');
define('ENTRY_ADDED_BONUS', '額外贈送');
define('ENTRY_DELIVERY_INFORMATION', '輸入您的賬戶資料');

define('TEXT_NAME', '名字');
define('TEXT_BILLING_ADDRESS', '地址');
define('TEXT_STATE_OR_ZIP', '州/郵編');
define('TEXT_FIRST_NAME', '名字');
define('TEXT_LAST_NAME', '姓氏');
define('TEXT_CONTACT_NUMBER', '聯繫電話');
define('TEXT_BILLING_ADDRESS1', '地址1');
define('TEXT_COUNTRY_CODE', '國家代碼');
define('TEXT_POST_CODE', '郵遞區號');
define('TEXT_CITY', '城市');
define('TEXT_COUNTRY', '國家');
define('TEXT_STATE', '州/省');
define('TEXT_SECRET_QUESTION', '帳戶保護問題');
define('TEXT_ANSWER', '答案');
define('TEXT_SELLING_DELIVERY_TIME', 'Delivery Time');
define('TEXT_OP', 'OP');
define('TEXT_REBATE', '');
define('TEXT_TOTAL_PAY', '應付金額');

define('SECTION_TITLE_FORM_PM_REQUIRED_INFO', '所需資料:');
define('JS_ERROR_EMPTY_SEARCH_INPUT', '請輸入搜索.');

define('FORM_REQUIRED_MSG', '我們向您索取信息隻是為了讓您享受更高檔次的服務.');
define('FORM_REQUIRED_INFORMATION', '*所需資料');

// constants for use in tep_prev_next_display function
define('TEXT_RESULT_PAGE', '結果頁面:');
define('TEXT_DISPLAY_NUMBER_OF_PRODUCTS', '展示<b>%d</b>至<b>%d</b> (<b>%d</b>結果)');
define('TEXT_DISPLAY_NUMBER_OF_ORDERS', '展示<b>%d</b>至<b>%d</b> (<b>%d</b>訂單)');
define('TEXT_DISPLAY_NUMBER_OF_REVIEWS', '展示<b>%d</b>至<b>%d</b> (<b>%d</b>評語)');
define('TEXT_DISPLAY_NUMBER_OF_PRODUCTS_NEW', '展示<b>%d</b>至<b>%d</b> (<b>%d</b>新產品)');
define('TEXT_DISPLAY_NUMBER_OF_SPECIALS', '展示<b>%d</b>至<b>%d</b> (<b>%d</b>特銷)');

define('PREVNEXT_TITLE_FIRST_PAGE', '第一頁');
define('PREVNEXT_TITLE_PREVIOUS_PAGE', '前一頁');
define('PREVNEXT_TITLE_NEXT_PAGE', '下一頁');
define('PREVNEXT_TITLE_LAST_PAGE', '最後一頁');
define('PREVNEXT_TITLE_PAGE_NO', '頁%d');
define('PREVNEXT_TITLE_PREV_SET_OF_NO_PAGE', '前一套％ d個頁面');
define('PREVNEXT_TITLE_NEXT_SET_OF_NO_PAGE', '下一組％ d個頁面');
define('PREVNEXT_BUTTON_FIRST', '首先');
define('PREVNEXT_BUTTON_PREV', '先前');
define('PREVNEXT_BUTTON_NEXT', '下一步');
define('PREVNEXT_BUTTON_LAST', '上一步');

define('BUTTON_FIRST', '首頁');
define('BUTTON_LAST', '最後一頁');

define('BUTTON_SUBMIT', '提交');
define('BUTTON_CONFIRM', '確認');
define('BUTTON_CANCEL', '取消');
define('BUTTON_OK', '確認');

define('BUTTON_ADD', '添加');
define('BUTTON_ADD_PM', '添加收款賬戶');
define('BUTTON_AGREE', '同意');
define('BUTTON_BACK', '返回');

define('BUTTON_YES', '是');
define('BUTTON_BATCH_UPDATE', '集體更新');
define('BUTTON_CANCEL', '取消');
define('BUTTON_CONFIRM', '確定');
define('BUTTON_CONTINUE', '繼續');
define('BUTTON_DELETE', '刪除');
define('BUTTON_DISAGREE', '不同意');
define('BUTTON_EDIT', '更改');
define('BUTTON_REMOVE', '删除');
define('BUTTON_NO', '否');

define('BUTTON_EXPORT', '輸出');
define('BUTTON_EXPORT_TEMPLATE', '輸出模板');
define('BUTTON_IMPORT', '輸入');
define('BUTTON_PRESALE_NOTICE', '加入開收通知');
define('BUTTON_PRESALE_REMOVE', '刪除開收通知');
define('BUTTON_RESET', '重設');
define('BUTTON_REFRESH', '刷新');
define('BUTTON_REPORT', '報告');
define('BUTTON_SAVE_CHANGES', '保存');
define('BUTTON_SEARCH', '搜索');
define('BUTTON_SKIP_NOW', '跳過');
define('BUTTON_SHOW_ALL_PRODUCTS', '顯示全部服務器');
define('BUTTON_SHOW_SELLING_PRODUCTS', '顯示銷售服務器');
define('BUTTON_SIGN_IN', '登入');
define('BUTTON_SIGN_UP', '註冊');
define('BUTTON_SIGN_OUT', '登出');
define('BUTTON_RESEND_SECURITY_TOKEN', '重發安全碼');

define('ALT_BUTTON_ADD', '添加');
define('ALT_BUTTON_AGREE', '同意');

define('ALT_BUTTON_BATCH_UPDATE', '集體更新');
define('ALT_BUTTON_CANCEL', '取消');
define('ALT_BUTTON_DELETE', '刪除');
define('ALT_BUTTON_DISAGREE', '不同意');
define('ALT_BUTTON_EDIT', '更改');

define('ALT_BUTTON_EXPORT', '輸出');
define('ALT_BUTTON_IMPORT', '輸入');
define('ALT_BUTTON_PRESALE_NOTICE', '加入開收通知');
define('ALT_BUTTON_PRESALE_REMOVE', '刪除開收通知');
define('ALT_BUTTON_REFRESH', '刷新');
define('ALT_BUTTON_SIGN_OUT', '登出');

define('BUTTON_MIN_CHAR_LENGTH', 11);
define('IMAGE_BUTTON_ADD_ADDRESS', '添加地址');
define('IMAGE_BUTTON_ADDRESS_BOOK', '通訊錄');
define('IMAGE_BUTTON_BACK', '返回');
define('IMAGE_BUTTON_BUY_NOW', '立即購買');
define('IMAGE_BUTTON_CHANGE_ADDRESS', '更改地址');
define('IMAGE_BUTTON_CHECKOUT', '結賬');
define('IMAGE_BUTTON_CONFIRM_ORDER', '確認訂單');
define('IMAGE_BUTTON_SECURE_CHECKOUT', '安全結賬');
define('IMAGE_BUTTON_CONFIRM', '確認訂單');
define('IMAGE_BUTTON_CONFIRM_CODE', '確認代碼');
define('IMAGE_BUTTON_CONTINUE', '繼續');
define('IMAGE_BUTTON_CONTINUE_SHOPPING', '繼續購物');
define('IMAGE_BUTTON_CONVERT_NOW', '转换');
define('IMAGE_BUTTON_DELETE', '刪除');
define('IMAGE_BUTTON_DOWNLOAD', '下載');
define('IMAGE_BUTTON_EDIT_ACCOUNT', '編輯帳戶');
define('IMAGE_BUTTON_HISTORY', '訂單歷史');
define('IMAGE_BUTTON_IN_CART', '立刻購買');
define('IMAGE_BUTTON_DIRECT_TOP_UP', '直接充值');
define('IMAGE_BUTTON_LOGIN', '登入');
define('IMAGE_BUTTON_SEND_PASSWORD', '發送密碼');
define('IMAGE_BUTTON_NOTIFICATIONS', '通知');
define('IMAGE_BUTTON_OUT_OF_STOCK', '售完');
define('IMAGE_BUTTON_PRE_ORDER', '預訂');
define('IMAGE_BUTTON_QUICK_FIND', '搜索');
define('IMAGE_BUTTON_REMOVE_NOTIFICATIONS', '刪除通知');
define('IMAGE_BUTTON_REVIEWS', '評語');
define('IMAGE_BUTTON_SEARCH', '搜索');
define('IMAGE_BUTTON_SHIPPING_OPTIONS', '運輸選項');
define('IMAGE_BUTTON_TELL_A_FRIEND', '推薦朋友');
define('IMAGE_BUTTON_UPDATE', '更新');
define('IMAGE_BUTTON_CONFIRM_TEL', '確認');
define('IMAGE_BUTTON_VERIFY', '驗證');
define('IMAGE_BUTTON_UPDATE_CART', '更新購物車');
define('IMAGE_BUTTON_WRITE_REVIEW', '填寫審查');
define('IMAGE_BUTTON_REDEEM_VOUCHER', '兌換');
define('IMAGE_BUTTON_SELL_MORE', '出售更多');
define('IMAGE_BUTTON_TRANSFER_NOW', '現在轉讓');
define('IMAGE_BUTTON_ADD', '添加');
define('IMAGE_BUTTON_NEXT', '下一個');
define('IMAGE_BUTTON_REFRESH', '刷新');
define('IMAGE_BUTTON_YES', '是');
define('IMAGE_BUTTON_YES2', '是的');
define('IMAGE_BUTTON_TOP_UP_STORE_CREDITS', '購買購物代金券');
define('IMAGE_BUTTON_BUY_CODE', '購買代碼');
define('IMAGE_BUTTON_PAY_WITH_SC_CURRENCY', '支付 %s (購物代金券)');
define('IMAGE_BUTTON_PAY_WITH_WEBSITE_CURRENCY', '支付 %s');
define('IMAGE_BUTTON_PAY_WITHOUT_SC', '無購物代金券支付');
define('TEXT_INFO_PAYMENT_CONFIRM_CAPTION', '通過%s結賬');

define('ALT_BUTTON_ADD_PM', '新增付款賬戶');
define('ALT_BUTTON_BACK', '返回上一頁');
define('ALT_BUTTON_CONFIRM', '確認');
define('ALT_BUTTON_CONTINUE', '繼續');
define('ALT_BUTTON_RESET', '重置');
define('ALT_BUTTON_SEARCH', '搜索');
define('ALT_BUTTON_SHOW_ALL_PRODUCTS', '點擊這裡查看所有服務器.');
define('ALT_BUTTON_SHOW_SELLING_PRODUCTS', '點擊這裡展示您只出售的服務器.');
define('ALT_BUTTON_SUBMIT', '提交表格');
define('ALT_BUTTON_SIGN_IN', '登入');
define('ALT_BUTTON_SIGN_UP', '註冊');
define('ALT_BUTTON_UPDATE', '更新');

define('TABLE_HEADING_SERVER', '服務器');
define('TABLE_HEADING_ACTION', '運作');
define('TABLE_HEADING_PER_UNIT', '單價');
define('TABLE_HEADING_STATUS', '狀態');
define('TABLE_HEADING_QTY', '收購量.');
define('TABLE_HEADING_DELIVERY_TIME', '发货时间');
define('TABLE_HEADING_DELIVERY_METHOD_SUPPORT', '选择交易方式');

define('TITLE_TRANS_PAYMENT', '付款%s');

define('SMALL_IMAGE_BUTTON_DELETE', '刪除');
define('SMALL_IMAGE_BUTTON_EDIT', '編輯');
define('SMALL_IMAGE_BUTTON_VIEW', '查看');

define('ICON_ARROW_RIGHT', '更多');
define('ICON_CART', '在購物車');
define('ICON_ERROR', '錯誤');
define('ICON_SUCCESS', '成功');
define('ICON_WARNING', '警告');
define('ICON_NOTICE', '通知');
define('ICON_PROMO', '促銷');

// CATALOG_PRODUCTS_WITH_IMAGES_mod
define('BOX_CATALOG_PRODUCTS_WITH_IMAGES', '打印產品目錄');
define('IMAGE_BUTTON_UPSORT', '上升排序');
define('IMAGE_BUTTON_DOWNSORT', '下降排序desending');

// CD Key
define('TEXT_CDKEY_SUSPENDED_FOR_VIEWING', '由於付款糾紛，CD Key和月卡插圖暫時封鎖.串行代碼已轉發給遊戲製造商，以便採取適當行動.請聯繫************************如果有錯誤.');

// Down For Maintenance
define('TEXT_BEFORE_DOWN_FOR_MAINTENANCE', '公告：本網站將在%s關閉進行%s的維護.');
define('TEXT_ADMIN_DOWN_FOR_MAINTENANCE', '通知：網站目前進行維修');

define('TEXT_GREETING_PERSONAL', '歡迎回來, <span class="greetUser">%s</span>!你是否想看看哪<a href="%s">新產品</a>可購買?');
define('TEXT_GREETING_PERSONAL_RELOGON', '<small>如果您不是％ s，請利用您的帳戶資料<a href="%s">登入</a>.</small>');
define('TEXT_GREETING_GUEST', '歡迎<span class="greetUser">顧客</span>!你是否想<a href="%s">登入</a>?或<a href="%s" >註冊一個帳戶</a>?');
define('TEXT_YOU_ARE_HERE', '<b>您在這裡:</b>');
define('TEXT_HOME', '首頁');
define('TEXT_GAME', '遊戲');
define('TEXT_SERVER', '服務器');
define('TEXT_NORMAL', '正常');
define('TEXT_GENERAL', '原價');
define('TEXT_FULL', '已滿');
define('TEXT_QUANTITY', '數量');
define('TEXT_VIEW', '查看');

define('TEXT_ENTER', '輸入');
define('TEXT_LIST_CLOSE', '關閉');
define('TEXT_IS_LOADING', '載入中...請稍候.');
define('TEXT_CONFIRM_DELETE', '您確定刪除?');

//Common fields
define('TEXT_AMOUNT', '總額'); //$$$
define('TEXT_AMOUNT_WITH_CURRENCY', '總額'); //$$$
define('TEXT_STATUS', '狀態');
define('TEXT_ACTION', '運作');
define('TEXT_ORDER_NO', '單子#');
define('TEXT_ORDER_STATUS', '單子狀態');
define('TEXT_PAYMENT_STATUS', '款項狀態');
define('TEXT_START_DATE', '起始日期');
define('TEXT_END_DATE', '終結日期');
define('TEXT_LOADING_MESSAGE', '正在加載...');
define('TEXT_WEBSITE_TIPS', '免責聲明');
define('TEXT_REFRESH', '刷新');
define('TEXT_REDEEM', '兌換');
define('TEXT_NO_RESULTS', '無相符顯示');

define('TEXT_CHARACTER_NAME', '角色名: %s');
define('TEXT_CHAR_NAME', '角色名');
define('TEXT_CHARACTER_INGAME_TIME', '距離登陸時間: %s<br>結賬後');
define('TEXT_CHARACTER_INGAME_DURATION', '游戲待續時間: %d小時');
define('TEXT_CHARACTER_ACCOUNT_NAME', '帳戶名稱: %s');
define('TEXT_CHARACTER_ACCOUNT_PASSWORD', '密碼: %s');
define('TEXT_CHARACTER_ACCOUNT_WOW', '魔獸帳號名: %s');
define('GAME_CURRENCY_TEMPLATE', '遊戲貨幣商舖');
define('CD_KEY_TEMPLATE', '遊戲點卡商舖');
define('PWL_TEMPLATE', '遊戲代練商舖');
define('HIGH_LEVEL_ACCOUNT_TEMPLATE', '遊戲帳號商舖');

define('URL_GAME_CURRENCY_TEMPLATE', 'game_currency_store');
define('URL_CD_KEY_TEMPLATE', 'game_card_store');
define('URL_PWL_TEMPLATE', 'power_leveling_store');
define('URL_HIGH_LEVEL_ACCOUNT_TEMPLATE', 'high_level_account_store');

define('GAME_CURRENCY_TEXT', '在遊戲中的貨幣用於購買任何物品或技能..');
define('CD_KEY_TEXT', '遊戲點卡在線充值...');
define('PWL_TEXT', '用於PVP服務器的遊戲技能...');

define('LINK_GAME_CURRENCY', '遊戲貨幣請按此.');
define('LINK_CD_KEY', '遊戲點卡請按此.');
define('LINK_PWL', '遊戲代練請按此.');

define('SHARE_THIS_TITLE', '分享收藏');
define('LINK_SHARE_THIS_PAGE', 'Share this page');
define('LINK_SHARE_THIS_PRODUCT', 'Share this product');
define('LINK_REDIRECT_MSG', 'Click here is your browser doesn\'t not automatically redirect you to the next page.');
define('LINK_MORE_PRODUCT_INFO', '更多產品信息');
define('LINK_HIDE_PRODUCT_INFO', '隱藏產品信息');
define('LINK_ALL_LANGUAGE', '所有語言');
define('LINK_ALL_PRODUCT_TYPE', '所有產品類型');
define('LINK_ALL_PLATFORM', '所有遊戲平台');
define('LINK_ALL_GENRE', '所有遊戲類別');

define('TEXT_SORT_PRODUCTS', '分類產品');
define('TEXT_DESCENDINGLY', '下降');
define('TEXT_ASCENDINGLY', '上升');
define('TEXT_BY', '於');
define('TEXT_NOT_AVAILABLE', 'N/A');

define('TEXT_REVIEW_BY', '於%s');
define('TEXT_REVIEW_WORD_COUNT', '%s字');
define('TEXT_REVIEW_RATING', '評級: %s [%s]');
define('TEXT_REVIEW_DATE_ADDED', '加入日期: %s');
define('TEXT_NO_REVIEWS', '目前沒有任何產品評論.');

define('TEXT_CLICK_TO_VERIFY', '點擊這裡驗證');
define('TEXT_EMAIL_NOT_VERIFY', '未驗證');
define('TEXT_EMAIL_VERIFIED', '(驗證) <span class="requiredInfo">*</span>');

define('TEXT_UNKNOWN_TAX_RATE', '未知稅率');
define('TEXT_PRODUCT_NOT_FOUND', '此產品已下架.');
define('TEXT_PRODUCT_NOT_FOUND_OPTIONS', '<div class="mediumFont"><b>請嘗試以下選項：</b></div>
												<div>
													<ul style="font-weight:normal;font-size:12px;list-style-type:square;padding-left:25px;margin-top:5px;">
														<li><a href="%s">瀏覽商舖內</a>的其他產品和服務</li>
														<li>進入<a href="%s">OffGamers首頁</a></li>
													</ul>
												</div>');
define('TEXT_FIELD_REQUIRED', '&nbsp;<span class="requiredInfo">* 必填</span>');
define('TEXT_INSTANT_DELIVERY', '存貨');
define('TEXT_INSTANT', 'Instant');
define('TEXT_MEMORY_USAGE', 'Memory Usage');
define('TITLE_TRANS_BUYBACK_ORDER', '採購訂單%s');
define('TITLE_BUYBACK_PAYMENT_REPORT', '付款報告');

define('ERROR_TEP_MAIL', '<font face="Verdana, Arial" size="2" color="#ff0000"><b><small>TEP ERROR:</small>不能通過指定的SMTP服務器發送電子郵件.請檢查您的php.ini設置,如有必要,請改正的SMTP服務器.</b></font>');
define('ERROR_PAYMENT_CURRENCY_NOT_SUPPORTED', '錯誤：您所選擇的支付網關, %s,只支持%s.請選擇不同的貨幣或請選擇不同的支付網關.');
define('WARNING_INSTALL_DIRECTORY_EXISTS', '警告：安裝目錄存在於: ' . dirname($HTTP_SERVER_VARS['SCRIPT_FILENAME']) . '/install.為了安全起見,請刪除這個目錄.');
define('WARNING_CONFIG_FILE_WRITEABLE', '警告：我可以寫此配置文件: ' . dirname($HTTP_SERVER_VARS['SCRIPT_FILENAME']) . '/includes/configure.php.這是一個安全風險-請對這個檔案設定用戶權限.');
define('WARNING_SESSION_DIRECTORY_NON_EXISTENT', '警告：會議的目錄不存在: ' . tep_session_save_path() . '.直到創建此目錄,會議將不會操作.');
define('WARNING_SESSION_DIRECTORY_NOT_WRITEABLE', '警告：我不能夠寫入會議的目錄: ' . tep_session_save_path() . '.直到正確的用戶權限設置，會議將不會操作.');
define('WARNING_SESSION_AUTO_START', '警告： session.auto_start啟用-請在php.ini中禁用此PHP功能並重新啟動Web伺服器.');
define('WARNING_DOWNLOAD_DIRECTORY_NON_EXISTENT', '警告：可下載的產品目錄不存在: ' . DIR_FS_DOWNLOAD . '.直到這個目錄是有效的,可下載的產品將不會操作.');
define('WARNING_PRODUCTS_LOW_QUANTITY', '該訂單的限定已超過了. ');
define('WARNING_PRODUCTS_SUGGESTED_QUANTITY', '建議的數量是％d.');
define('WARNING_PRODUCTS_OVERWEIGHT', '我們很抱歉！您的總重量為這項產品已超過最大重量!');
define('WARNING_PRODUCTS_UNDERWEIGHT', '我們很抱歉！您的產品總重量超過了限定的重量!');
define('WARNING_EMPTY_SELECTIONS', '至少要選擇一個!');
define('WARNING_ORDER_AMOUNT_ZERO', '結賬失敗！您的購物車產品沒有價格.');
define('WARNING_MUST_BE_VIP_MEMBER', '警告：只有VIP訂單才具有此功能');
define('SUCCESS_DYNAMIC_CART_ADDED', '此產品已成功添加到購物車! <a href="%s">點擊這裡查看購物車.</a>');
define('SUCCESS_DYNAMIC_CART_UPDATED', '此產品的購物車已成功更新! <a href="%s">點擊這裡查看購物車.</a>');

define('ERROR_NO_UPLOAD_FILE', '錯誤：上載文件不存在。');
define('ERROR_FILESIZE_EXCEED', '錯誤：文件大小超過限制。');
define('ERROR_UPLOAD_PARTIAL', '錯誤：文件只有部分被上傳。');
define('ERROR_NO_TMP_DIR', '錯誤：找不到臨時文件夾。');
define('ERROR_DESTINATION_DOES_NOT_EXIST', '錯誤：上载目錄不存在。');
define('ERROR_DESTINATION_NOT_WRITEABLE', '錯誤：上载目錄不可寫。');
define('ERROR_FILE_NOT_SAVED', '錯誤：上载文件未存盤。');
define('ERROR_FILETYPE_NOT_ALLOWED', '錯誤：文件類型錯誤。');
define('SUCCESS_FILE_SAVED_SUCCESSFULLY', '成功：上载文件已存盤。');
define('WARNING_NO_FILE_UPLOADED', '警告：未上传任何文件。');
define('WARNING_FILE_UPLOADS_DISABLED', '錯誤：在 php.ini 設置文件中，文件上载功能已被解除。');
define('ERROR_PAGE_ACCESS_DENIED', '錯誤：您無權限訪問該頁面。');

define('TEXT_CCVAL_ERROR_INVALID_DATE', '所輸入的信用卡截止日期錯誤.<br>請檢查日期，然後再試一次.');
define('TEXT_CCVAL_ERROR_INVALID_NUMBER', '所輸入的信用卡號碼錯誤.<br>請檢查該號碼並再試一次.');
define('TEXT_CCVAL_ERROR_UNKNOWN_CARD', '所輸入的信用卡首4個號碼: %s<br>如這數字是錯的，我們不能接受這型的信用卡.<br>如有錯誤，請再試一次.');

define('TEXT_ERROR_ACTIVATE_ACCOUNT', '以便結賬，請啟動您的帳戶.');
define('TEXT_STOCK_NOT_AVAILABLE', '您要求購買的產品數量不足。如果要大宗購物，請聯繫 <a href="mailto:<EMAIL>"><EMAIL></a>');
define('TEXT_CUSTOMER_VERIFIED_PAYMENT_EMAIL_NOTICE_SUBJECT', '客戶付款電郵地址驗證通知');
define('TEXT_CUSTOMER_NAME', '客戶名稱: ');
define('TEXT_CUSTOMER_PAYMENT_EMAIL', '付款電郵地址: ');
define('TEXT_NEWSLETTER_MSG', '<div style=\"font-family: Arial, Verdana, sans-serif; font-weight: bold; font-size: 12px; color: green;\">請注：</div >" . "<span class=\"latestNewsBoxContents\">發給您的電子郵件將從@offgamers.com ---如果您使用的反垃圾郵件過濾器，請務必添加此域到您的"優良者名單"或"住址名冊". <a href="http://www.offgamers.com/user-guide-whitelist-emails-i-491.ogm" target="_blank">要學習如何，請按此</a>.</span>');

define('EMAIL_TEXT_AFFILIATE_NOTIFICATION_SUBJECT', '合作聯盟銷售#%d (訂單總額: %s)');
define('EMAIL_TEXT_AFFILIATE_NOTIFICATION_INFO', '以下是由您推介的銷售:' . "\n\n" . '訂單日期: %s' . "\n" . '訂單ID: %s' . "\n" . '訂單總額: %s' . "\n");

define('LOG_SALES_ORDER', '##%d##: ##%d## ##%d##');

define('LOG_SC_STAT_SC_CONVERT', 'Store Credit Currency Conversion');
define('LOG_SC_ACTIVITY_TYPE_PURCHASE', 'P');
define('LOG_SC_ACTIVITY_TYPE_CANCEL', 'X');
define('LOG_SC_ACTIVITY_TYPE_CONVERT', 'V');

//付款彈出菜單
define('POP_PAYMENT_REPORT_PAYMENT_MESSAGE', '付款留言');
define('POP_PAYMENT_REPORT_DATETIME', '日期/時間');

//定義為交易狀況更新通知
define('EMAIL_CUSTOMER_ORDER_UPDATE_NOTIFICATION_SUBJECT', '客戶訂單更新通知#%d');
define('EMAIL_TRANS_UPDATE_NOTIFICATION_CONTENT', '訂單ID: %s' . "\n" . '訂單日期: %s' . "\n" . '訂單總額: %s' . "\n" . '付款方式: %s' . "\n\n" . '更新類型: %s' . "\n" . '狀態更新: %s -> %s' . "\n" . '更新日期: %s' . " \n" . '更新IP: %s' . "\n" . '更新用戶: %s' . "\n\n" . '更新評論:' . "\n" . '%s');
define('EMAIL_TEXT_TRANS_UPDATE_MANUAL_NOTIFICATION', '手動');
define('EMAIL_TEXT_TRANS_UPDATE_AUTO_NOTIFICATION', '自動');

//投票站
define('BUTTON_VIEW_RESULT', '結果');
define('BUTTON_VOTE', '投票');

//電子郵件驗證
define('EMAIL_SUBJECT_2', '電子郵件地址驗證');
define('EMAIL_VERIFY_CONTENT', '您在最近使用了此電郵地址註冊' . STORE_OWNER . '。請點擊下面的鏈接，完成您的註冊:' . "\n");
define('EMAIL_VERIFY_CONTENT_ADDRESS_INFO', '(如果點擊鏈接不能操作，請複制並粘貼到您的瀏覽器.)' . "\n\n");
define('EMAIL_MANUAL_ACTIVATE_EMAIL_2', '您也可以在<a href="' . tep_href_link(FILENAME_ACCOUNT_ACTIVATE, 'verify=email&auto_mail=yes', 'SSL') . '">' . tep_href_link(FILENAME_ACCOUNT_ACTIVATE, 'verify=email&auto_mail=yes', 'SSL') . '</a>，輸入以下的資料，手動驗證您的電子郵件地址.' . "\n\n" . '電子郵件地址: ');
define('EMAIL_MANUAL_ACTIVATE_CODE_2', "\n" . '驗證碼: ');
define('EMAIL_CONTACT', '如有任何的詢問，請使用我們的在線客服，或通過電子郵件聯絡我們' . EMAIL_TO . '. 謝謝您在' . STORE_NAME . "購物.\n\n\n");
define('EMAIL_FOOTER', STORE_EMAIL_SIGNATURE);

define('TEXT_FRAUD_DISCLAIMER', '<br>
<table style="border-right: 2px dashed #ff0000; padding-right: 10px; border-top: 2px dashed #ff0000; padding-left: 10px; padding-bottom: 15px; margin: 0px 0px 15px; border-left : 2px dashed #ff0000; padding-top: 10px; border-bottom: 2px dashed #ff0000; background-color: #fffef2;">
         <tr>
<td>
<div style="font-family: Arial, Verdana, sans-serif; font-weight: 700; font-size: 16px; color: #ff0000; border-bottom: 1px dashed #ff0000; text-align: left"> Attn : Fraudsters and scammers</div>
<BR><span class="latestNewsBoxContents">
預告，信用卡欺詐是一項聯邦罪行，一律由<a href="http://www.fbi.gov/cyberinvest/cyberhome.htm" target="_blank">FBI</a> (FEDERAL BUREAU OF INVESTIGATION) , <a href="http://www.secretservice.gov/financial_crimes.shtml" target="_blank">美國秘密服務</a>和它們的合作夥伴包括<a href="http://www. interpol.int/Public/FinancialCrime/default.asp" target="_blank">INTERPOL
國際</a>舉辦調查工作.法律要求我們報告這種侵權行為，而我們非常樂意的遵守這一任務. <br>
<br>
offgamers將提供所有的資料，包括* IP地址， MAC地址，註冊的VoIP電話號碼，反向查找資料，記錄的談話， *彙編欺詐的命令，以官員和地方當局採取行動追求調查和起訴犯罪者。一旦刑事案件已完成，一個對違約方的民事案將被提交，以收回OffGamers所受到的任何及所有損害（賠償財務或其他方面）. <br>
<br>
請注意， offgamers察覺與<a href="http://www.ic3.gov/" target="_blank">報告了</a>許多企圖欺騙，詐騙，欺詐，竊取身份，侵入和非法進入我們的服務器的事件.這警告是讓大家知道我們非常重視我們訂貨系統的安全，任何企圖以欺詐手段的地方，命令是否由假冒，偽造，篡改，虛假聲稱，冒用信用卡，或有關的活動，在他涉嫌與身份證明文件和商業欺詐，虛假文書將立即被報告。
<br>
<br>
美國秘密服務是首要的聯邦機構，專責調查欺詐的行為及其有關活動（標題18 ，美國法典第1029） 。不要被蒙蔽，認為在背後利用欺騙性IP地址和VoIP電話線是安全的. <a href="http://www.fbi.gov/contact/legat/legat.htm" target="_blank">FBI </a>, <a href="http://www.secretservice.gov/field_offices.shtml#over" target="_blank">美國秘密服務</a>,與<a href="http:// www.interpol.int/Public/ICPO/Members/default.asp" target="_blank">INTERPOL</a>;
在世界各地操作，並會與<a href="http://www.fbi.gov/page2/march06/cats030606.htm" target="_blank">本地的官員</a>合作確保犯罪者被逮捕。從offgamers竊取， 10年坐牢，是不值得。
</span>
</td>
</tr>
</table>');

define('TEXT_GOLD_DELIVERY_NOTES', '');

define('TEXT_PAYMENT_VERIFICATION_NOTES', '');

define('TEXT_PAYMENT_GATEWAY', '支付網關');
define('TEXT_ITEMS_IN_MY_CART', '<a href="%s" style="float:none;padding:0;">%s</a> 購物車裡的產品');
define('TEXT_ITEM_IN_MY_CART', '購物車裡的產品');
define('TEXT_ITEM_IN_MY_CART_FOR_FOOTER', '購物車 (%s)');
define('TEXT_TYPE_TO_FILTER_PRODUCTS', '過濾型產品');
define('TEXT_SEARCH_WHOLE_STORE', '搜索整個商店');
define('TEXT_SUBSCRIBE_NEWSLETTER', '現在訂閱');
define('TEXT_VIEW_ALL_TESTIMONIALS', '查看全部客戶證言');
define('TEXT_GAME_NOT_SUPPORT', '您的國家不支持這遊戲');
define('TEXT_GAME_NOT_SUPPORT_DESC', '由於遊戲出版商的限定，您所選澤的國家被禁止玩這遊戲');
define('TEXT_BACK_TO_TOP', '回到頂部');
define('TEXT_VIEW_MY_CART', '查看購物車');
define('TEXT_SWITCH_TO_MOBILE', 'Click here switch to mobile version');
define('TEXT_COPYRIGHT_INFORMATION', '版權所有&copy;' . date('Y') . ' OffGamers Global Pte Ltd。保留所有權利。 &nbsp&nbsp <a href="' . tep_href_link('/terms-of-service-cn-simp-i-630.ogm') . '">服務條款</a> &nbsp|&nbsp <a href="' . tep_href_link('privacy-policy-cn-simp-i-628.ogm') . '">隱私權政策</a>');

define('TAB_LATEST_UPDATE', 'LATEST UPDATE :');

// New Layout
define('TEXT_MUST_LOGIN', '需要登入才被允許');
define('TEXT_CURRENCY', '貨幣');
define('TOOLTIPS_MEMBER_ID', 'OffGamers ID');
define('TOOLTIPS_MEMBER_STATUS', '客戶等級');
define('TOOLTIPS_STORE_CREDIT_BALANCE', '購物代金券餘額');
define('TOOLTIPS_OFFGAMERS_POINTS', 'OP是您購物完成後退給您的獎勵積分，可以用於兌換代金券。<br/><br/><a href=\'http://kb.offgamers.com/zhcn/category/my-account/wor-token/\' target=\'_blank\'>更多詳情</a>');
define('TOOLTIPS_WITHDRAWABLE_BALANCE', '貨款餘額');

define('TEXT_LOGIN', '&nbsp;登錄');
define('TEXT_LOGOUT', '&nbsp;登出');
define('TEXT_TRACK_BUYBACK_ORDER', '&nbsp;購買訂單追蹤');
define('TEXT_TRACK_SELLING_ORDER', '&nbsp;出售訂單追蹤');
define('TEXT_REGISTER', '&nbsp;註冊帳號');
define('TEXT_GREETING_GUEST_NEW', '<font style="color:#92CFF3;">您尚未登錄,請<a id="fancy_login_box" href="javascript:void(0);" class="whiteText" ><b>登錄</b></a>或<a href="' . tep_href_link(FILENAME_LOGIN, '', 'SSL') . '" class="whiteText"><b>註冊帳號</b> </a>.</font>');
define('TEXT_OGM_SERVICE_GUARANTEE', '<font style="font-weight:bold;font-size:12px;">OGM服務保證</font><br><font class="smallestFont">虛碼/點卡</font><br><br><b>保證退款</b><br>如您對於代練過程有所不滿<br><br><b>戶口安全</b><br>如您有所不滿');
define('TEXT_JOIN_AFFILIATE_PROGRAM', '即刻加入共享利潤');
define('TEXT_VIEW_PAYMENT_GATEWAY', '查看更多付款方式');
define('TEXT_SERVICE_GURANTEED_ICON', 'icon_serviceguarantee_cn.gif');
define('TEXT_MORE_DETAILS', 'More Details');
define('TEXT_COOKIE_NOTICE_DESC', 'OffGamers 使用cookie 來優化我們網站上您的在線體驗。通過繼續使用我們的網站滿足您的遊戲需求，您同意使用這樣的cookie。');

define('BUTTON_PROCEED_TO_CHECKOUT', '&nbsp;進行結賬');
define('BUTTON_TOP_GAME_SELECTION', '熱銷遊戲選擇');
define('BUTTON_VIEW_ALL_GAMES', '全部遊戲');
define('BUTTON_MORE_GAMES', '更多遊戲');
define('HEADER_PRODUCT_SELECTION', '產品選擇');

define('HEADER_GAMES_SELECTION', '遊戲');
define('HEADER_CUSTOMER_TESTIMONIAL', '客戶證言');
define('HEADER_CONFIRM_BILLING_INFO', '請確認/完成您的帳單信息');

define('HEADER_NEWSLETTER', '電子月刊');
define('HEADER_SERVICES', '額外服務');
define('HEADER_EXPANDED_INFORMATION', '附加客戶說明：');
define('BOX_HEADER_AVAILABLE_PRODUCT', '產品系列');

define('BOX_EVENT_AND_PROMOTION', '促銷活動');

define('BOX_GAME_GUIDES', '遊戲指南');
define('BOX_HEADING_ITEM_ADDED_TO_SHOPPING_CART', '產品已添加到購物車');
define('BOX_STORE_CREDIT', '充值賬戶余額');

define('HTML_AFFILIATE_BOX', '<div style="width:100%;text-align:left;"><b><span style="font-size:16px;">高額佣金誘惑，簡單注冊步驟</ span></b></div>');
define('HTML_CUSTOMER_TESTIMONIAL_BOX', "<div id='scrollup' onclick='document.location.href=\"/customer-testimonials-i-621.ogm\"'>
<div class='headline'>Superb service and support as usual. No, even better this time. You even called me to check if i had recived the gold. Great discreetion as usual!<br>I just wanna thank you again. .</div>
<div class='headline'>I just wanted to say that I was pleased with your service. The face-to-face delivery was sooner than I anticipated, I actually was AFK because I didn't think it would come so soon! Thanks for making ..</div>
<div class='headline'>IS EXCELENT!!!!! YOU GAIN A CLIENT FOR EVER!!!!<br>THX !</div>
<div class='headline'>Hey, I just wanted to say thank you for providing great service, especially with your representatives for dealing with me at 3 in the morning, being very incoherent. I doubt this is the place to send it. .</div>
<div class='headline'>Thanks for the fast, great delivery for first time buyer. Support and Delivery were excellent and prompt.<br><b>Andryo (11-Oct-08)</b></div>
<div class='headline'>Hi Again! :)<br>Just wanted to say thanks again for another wonderful purchase. Thanks for making my gaming sessions fun again.<br><b>John W. (02-Oct- 08)</b></div>
<div class='headline'>Hi there,<br>I've not actually bought any gold from you (yet) but I'd like to say this has to be THE best game currency / leveling website around. The page layouts ..<br><b>Andy M. (17-Sep-08)</b></div>
<div class='headline'>HELLO!<br>IM VERY PLEASED WITH YOUR FAST SERVICE AND SUPPORT!!!! AND FAST CONTACT ON THE PHONE. WITH PLEASURE AGAIN!!!<br><b>BG (04-Sep -08</b></div>
<div class='headline'>Just saying thanks for the great service. Was a lot faster then I expected!<br><b>JB (02-Sep-08)</b></div>
<div class='headline'>I don't think I've ever had this level of service before, it's both great and just amazing. I can't even begin to tell you all the praise that I have for your men and women of the company.<br><b>FC (01-Jun-08)</b></div>
<div class='headline'>Great service, in 24hrs got multiple levels over 60. Also gained good amount of gold. Website is slightly slow at loading from page to page, but service is awesome.<br><b>Brian ( 18-May-08)</b></div>
<div class='headline'>You guys are awesome! The flexi-hours leveling takes the edge off of the otherwise completely monotonous grind. I'm excited every time I log back in after your levelers work..<br><b >Sean C. (17-May-08)</b></div>
                                              <div class='headline'>Great service!!!! Will definately be back again soon. Keep up the great work and thank you.<br><b>JC(25-Mar-08)</b></ div>
                                              <div class='headline'>Company is brilliant, even woke me up so I could collect what I ordered. Thanks to Saran and Vignes for their help and guidance on areas of the matter I did not know. 10 out of 10<br ><b>A. Cook.(22-Mar-08)</b></div>
                                              <div class='headline'>Ive bought alot of gold from you guys on bloodscalp now, always proccessed and delivered very fast! I also bought WoW and burning crusade from you guys.<br><b>KW(20-Mar- 08)</b></div>
                                              <div class='headline'>I just wanted to say Thank You, you made my experience quick, easy, and pleasant. Great job keep up the good work. I will happily do business with you again.<br><b> NB(19-Mar-08)</b></div>
                                              <div class='headline'>Just wanted to let you guys know that the service is beyond exceptional. =)<br><b>AE(05-Mar-2008)</b></div>
                                              <div class='headline'>I am very pleased with the service that you guys have provided. I am very happy to find a excellent service and I will not go to any other. Keep up the good job!<br><b >MD(02-Mar-2008)</b></div>
                                              <div class='headline'>I just want to thank you for comleting my order in such a short time, it is nice to see that after the new year celebration, you are back at full force. Marks: 20+ out of 10 ( more than excellent )</div>
                                              <div class='headline'>Thanks and good luck in the futer :)<br><b>MM(27-Feb-2008)</b></div>
                                              <div class='headline'>Saran, one of your live chat representatives, was very helpful and informative in helping me with my issues.<br><b>Satisfied Customer(24-Feb-2008)</b></ div>
                                              <div class='headline'>I just wanted to say that i ordered from you twice and so far everything has been fantastic though.. the live support is a little bland..<br><b>JM(21-Feb- 2008)</b></div>
                                              <div class='headline'>I wanted to thank you for the speedy process of my order for the CD-Key for WOW Burning Crusade EU. It arrived within minutes of verification and worked like a charm! <br><b>DF (11-Feb-08)</b></div>
                                              <div class='headline'>I was completely satisfied the way User Azhan responded and took care of my order issues. I just thought your supervisors should be aware of this person.<br><b>RN(5-Feb-08 )</b></div>
                                              <div class='headline'>You've got the best service folks working there, thanks.<br><b>YN(3-Feb-08)</b></div>
                                              <div class='headline'>I just wanted to let you know that Azhan was wonderful. He stayed with me and continued to try and find out what was wrong with my order until the matter was resolved.<br><b>TG (2-Feb-08)</b></div>
                                              <div class='headline'>I wanted to let you know that your service is amazing and I apperciate it very much, azhan answered my questions 110% to his abilitys, and I wanted to leave a positive feedback.<br><b >SA (29-Jan-08)</b></div>
                                              <div class='headline'>Would just like to say, you guys are fantastic, ordered my wow key, and it was delivered within 45 secs. Breathtaking service, thank you.<br><b>Karl (19-Jan- 08)</b></div>
                                              <div class='headline'>I just purchased the WoW BatlleChest. I recieved it within 5 min. very fast delivery. You guys are awesome.<br><b>BM (25-Dec-07)</b></div>
                                              <div class='headline'>just received the final installment of my 5000 gold purchase and all I can say is you are the BEST supplier I've found.<br><b>LMS (24-Nov-07)</ b></div>
                                              <div class='headline'>Just thought I'd send an email to let you all know what great service you've been giving me. Absolutely no problems and very fast and efficient service, I will be doing business with you.< br><b>Shaun. (26-Sep-07)</b></div>
                                              <div class='headline'>Awesome service!! I just bought a 60 day pre-paid WOW Gamecard (EU) and i got the code delivered within 10 minutes. I will definitively buy more from you later :)<br>< b>VF (23-Sep-07)</b></div>
                                              <div class='headline'>id like to say Ram. D is doing a very good job and i bet you guys do it to not the first time i use offgamers not the last i love the serivce here and i can rely on you guys..<br><b>MD (02-Sep-07)</b></div>
                                              <div class='headline'>I am writing you only for tell THANKS! Your service was awesome. I'll recommend you to all my friends!<br><b>RA (20-Jan-07)</b> </div>
                                              <div class='headline'>Just wanted to say thanks, I've always ordered power leveling for my characters from you guys and you do a great job. Also I am really pleased with how you are on the top.<br> <b>DS (15-Dec-06)</b></div>
                                              <div class='headline'>Delivery was made at the time promised, even with notification of shortage possibly affecting delivery. Thank you for aiding in making my gaming experience awesome!<br><b>MT (13-Oct-06) </b></div>
                                              <div class='headline'>Thanx alot and yes and ur so so so so good<br><b>HA (13-Oct-06)</b></div>
                                              <div class='headline'>due to fast delivery, easy-to-access site, i could find anything i needed really easy. The live chat is really nice, with people answering almost immediately.<br><b>EH ( 11-Oct-06)</b></div>
                                              <div class='headline'>Thank you for your services and distinctive the unlimited support.<br><b>S. (06-Oct-06)</b></div>
                                              <div class='headline'>Hello, I just wanted to say that OffGamers deliver perfect service and it is allways fast and simple. Keep up the good work and thank you once again for this perfect service.<br><b>FH (22-Jul-06)</b></div>
                                              <div class='headline'>Hi Guys,Just thought I'd send an email to let you all know what great service you've been giving me. Absolutely no problems and very fast and efficient service.<br><b> SO (13-Jul-06)</b></div>
                                              <div class='headline'>I have been a customer at OffGamers for quite some time now. Never have I been assigned a more helpful agent than Saran. Saran re-assured me OffGamers is the best.<br><b>KM (29-Apr-06)</b></div>
                                              <div class='headline'>I have been buying gold for years from IGE and other sites.. Only to get the run-around when it came to deliveries and customer service. You guys is the most professional..<br>< b>CR (29-Apr-06)</b></div>
                                              <div class='headline'>I am very impressed with the quality of your workmanship and the speed in which you completed the power level. Thank you for your professionalism.<br><b>DD (21-Apr-06)</b></div>
                                              <div class='headline'>Ever since I finally found the profiler on your site I've been checking my character and bags a bunch and I gotta say; Thanks for not vendoring/trashing..<br><b>SR ( 19-Apr-06)</b></div>
                                              <div class='headline'>Thank you once again for an amazing service and quick one at that. You guys are a great team thank you very much.<br><b>S. (09-Mar-06)</ b></div>
                                              <div class='headline'>Awesome job on the power leveling guys, I logged the toon on to take a quick look and she was already lvl 32... and in half the time you quoted at that. Again, great work! <br><b>JF (27-Feb-06)</b></div>
                                              <div class='headline'>I dont know what to sayi'm very happy about CD key and its proive for me that customers is number one in Off Gamers and i will deal with you again and again<br><b>R . (29-Dec-05)</b></div>
</div>");

define('HTML_NEWSLETTER_BOX', '訂閱我們的月刊會給予優惠，獨家促銷活動!');
define('HEADER_DELIVERY_INFORMATION', '發貨信息');
define('HEADER_CHANGE_MOBILE', '更換手機號碼');
define('HEADER_STORE_CREDIT_USE', '代金券购货');
define('TEXT_STORE_CREDIT_USE', '<p>如果您要使用代金券购货金额为您当前订单付款，您需要检查您购买的产品的币种是否与您的代金券购货金额的币种相符。</p><p>您想改将购物车内产品的币种转变为代金券购货金额的币种？</p>');
define('TEXT_STORE_CREDIT_CONVERT', '<p>In order to use the store credits for your current order, you need to convert your store credits to the currency that matches that of your current order.</p><p>Current store credits balance: %s<br/>After conversion: %s<br/>Conversion rate: %s (updated daily)<p>Would you like to change the currency of your current store credit balance to match that of your current order?</p>');
define('TEXT_STORE_CREDIT_CONVERT_IN_MY_ACCOUNT', '<p>如果您要使用代金券购货金额为您当前订单付款，您需要检查您购买的产品的币种是否与您的代金券购货金额的币种相符。</p><p>您想改将代金券购货金额的币种转变为购物车内产品的币种？</p>');
define('TEXT_STORE_CREDIT_POPUP_DESCRIPTION', '您的購物代金券的幣種與您事先選擇的網站幣種不同。 <b>請按照以下任一方法進行修改：-</b>');
define('TEXT_STORE_CREDIT_POPUP_CHANGE_WEBSITE_CURRENCY', '將網站幣種換成與購物代金券相同的幣種(%s).');
define('TEXT_STORE_CREDIT_POPUP_CHANGE_STORE_CREDIT_CURRENCY', '將購物代金券幣種換成與網站相同的幣種(%s).');
define('TEXT_STORE_CREDIT_POPUP_CONTINUE_WITH_NO_CHANGE', 'Store Credits will not be used for this purchase.');
define('TEXT_LOGIN_PASSWORD', '登錄密碼');

define('LINK_CHANGE_DELIVERY_INFO', '更改發貨信息');
define('TEXT_TRADE_MODE', '交易方式: %s');
define('TEXT_TRADE_METHOD', '交易方式: <b>%s</b>');
define('OPTION_FACE_TO_FACE', '面對面交易');
define('OPTION_PUT_IN_MY_ACCOUNT', '放入我的帳戶');
define('OPTION_BY_MAIL', '郵寄');
define('OPTION_OTHERS', '其他方式');
define('OPTION_OPEN_STORE', '擺攤');

define('BUYBACK_SUPPLIER_MODE_F2F', '面對面交易');
define('BUYBACK_SUPPLIER_MODE_MAIL', '郵寄');
define('BUYBACK_SUPPLIER_MODE_PIMA', '放入我的帳戶');
define('BUYBACK_SUPPLIER_MODE_T2O', '與網站交易');
define('BUYBACK_SUPPLIER_MODE_T2C', '與客戶交易');
define('BUYBACK_SUPPLIER_MODE_OPEN_STORE', '擺攤');

define('HEADER_TOTAL', '總計: ');
define('TEXT_NUMERIC', '數字標識號');

define('BREADCRUMB_BUY', '買');
define('BREADCRUMB_SELL', '賣');

define('TEXT_PROMOTION_STATUS', '當前狀態');
define('TEXT_PROMOTION_PRODUCTS', '促銷產品');
define('TEXT_RETYPE', '重新輸入');
define('TEXT_SOFTPIN', '虛擬代碼');
define('TEXT_STATUS_LIMITED_STOCK', '庫存有限');
define('TEXT_STATUS_FAST_SELLING', '熱銷中');
define('TEXT_STATUS_PRICE_SLASH', '減價');

// Locking / Unlocking log tag
if (!defined('ORDERS_LOG_LOCK_ORDER'))
    define('ORDERS_LOG_LOCK_ORDER', '##l_1##');
if (!defined('ORDERS_LOG_UNLOCK_OWN_ORDER'))
    define('ORDERS_LOG_UNLOCK_OWN_ORDER', '##ul_1##');
if (!defined('ORDERS_LOG_UNLOCK_OTHER_PEOPLE_ORDER'))
    define('ORDERS_LOG_UNLOCK_OTHER_PEOPLE_ORDER', '##ul_2##%s:~:%s');

define('LOG_PARTIAL_DELIVERY_SALES', '##%d##: Partial Delivery Sales');
define('LOG_CDKEY_ID_STR', 'CD Key ID: %s');
define('LOG_BUYBACK_ORDER', '##BUYBACK:%d##');

define('LIVE_SUPPORT', "<script type=\"text/javascript\">
								var bom;
								(function(){
									var E=navigator.userAgent.toLowerCase();
									var FF=E.indexOf(\"firefox\")!=-1,B=E.indexOf(\"opera\")!=-1,G=E.indexOf(\"safari\")!=-1,A=!B&&!G&&E.indexOf(\"gecko\")>-1,C=!B&&E.indexOf(\"msie\")!=-1,F=!B&&E.indexOf(\"msie 6\")!=-1,D=!B&&E.indexOf(\"msie 7\")!=-1;
									bom ={isOpera:B,isSafari:G,isGecko:A,isIE:C,isIE6:F,isIE7:D,isFireFox:FF};
								})();

								function CheckInstallQQ(){
									var Link='tencent://message/?uin=800080228&WebSiteName=bizapp.qq.com&Menu=yes';
									try{
										if (bom.isIE){
											var xmlhttp=new ActiveXObject(\"TimwpDll.TimwpCheck\");
											var  n = xmlhttp.GetVersion();
											if (n >= 2.1){
												this.location.href=Link;
											}else{
												alert(\"OffGamers网温馨提示：\\r\\n　　请您访问http://im.qq.com/下载新版的QQ/TM以支持与拍拍店主在线交流！\");
												window.target=\"_top\";
												window.open(\"http://im.qq.com/\");
											}
										}else if (bom.isFireFox){
											this.location.href=Link;
										}else{
											alert(\"OffGamers网温馨提示：\\r\\n　　您使用的浏览器不支持QQ临时会话功能，建议您加对方为好友，或使用IE/TT/FireFox浏览器访问。\");
											return false;
										}
									}catch(e){
										alert(\"OffGamers网温馨提示：\\r\\n　　请您访问http://im.qq.com/下载新版的QQ/TM以支持与拍拍店主在线交流！\");
										window.target=\"_top\";
										window.open(\"http://im.qq.com/\");
									}
								}
							</script>
							<img style=\"CURSOR: pointer\" onclick=\"javascript:(bom.isIE? window.open('http://bizapp.qq.com/webc.htm?new=0&sid=800080228&eid=&o=&q=7', '_blank', 'height=544, width=644,toolbar=no,scrollbars=no,menubar=no,status=no'):window.open('http://webchat.b.qq.com/webchat.htm?sid=2188z8p8p8p8z8p8K8K8z', '_blank', 'height=400, width=500,toolbar=no,scrollbars=no,menubar=no,status=no'));\" border=\"0\" SRC=\"http://image.offgamers.com/livechat/reponline.gif\" />
							<div class=\"dottedLine\"></div>
							<div class=\"menuTitle\" style=\"padding:0px 8px;text-align:center;\">客服QQ : <font color=\"red\">800080228</font></div>
							<div style=\"padding: 0px 8px;float:right;\">
								<img style=\"CURSOR: pointer\" onclick=\"javascript:CheckInstallQQ()\" border=\"0\" SRC=\"http://wpa.qq.com/pa?p=1:800080228:7\" />
							</div>");
define('B2C_SELLING_LIVE_SUPPORT', "<script type=\"text/javascript\">var __lc={};__lc.license=1306592;__lc.skill=1;__lc.params=[{name: 'identifier ', value: 'OGM_CUSTOMER_NAME' },{name: 'discountGroup', value: ' OGM_CUSTOMER_GROUP ' },{name: 'customerEmail', value: 'OGM_CUSTOMER_EMAIL' }];(function(){var lc=document.createElement('script');lc.type='text/javascript';lc.async=true;lc.src=('https:'==document.location.protocol ? 'https://' : 'http://') + 'cdn.livechatinc.com/tracking.js';var s=document.getElementsByTagName('script')[0];s.parentNode.insertBefore(lc, s);})();</script><style type=\"text/css\">#livechat-compact-container,#livechat-full{z-index: 565 !important;}#livechat-eye-catcher{bottom: 69px !important;z-index: 565 !important;}</style>");
define('TEXT_SELLER_INFO_DESC', 'OffGamers旨在為企業個人提供一個安全可靠的交易環境來出售他們多餘的遊戲貨幣。
								<br><br>為了營造一個安全，公正，有競爭力的遊戲環境，我們極力反對使用遊戲外掛，竊用，非法的宏指令，第三方程序,遊戲裡的非法廣告和不道德的行為。任何賣方被發現有以上行為將被列入黑名單，我們將沒收其款項作為懲罰的一種形式。
								<br><br>OffGamers聯繫來自世界各地的賣方和買方，我們向您保證我們將嚴格保密您的個人信息，並為交易雙方完成交易提供最好的服務平台。
								<br><br><div class="dottedLine"><!-- --></div><br>
								<h2>重要通知</h2><br>
								&#8250; <h3>註冊須知：</h3> 請牢記註冊帳戶時候設立的密保問答，這是修改您帳號資料的重要憑證；請留下您的真實姓名和聯絡方式（本人手機、QQ、MSN、座機等），這是核實您身份的重要依據； 如果您沒有認真對待密碼問答和留下真實資訊的話，會產生很多諸如無法修改帳戶資訊、無法提取貨款等麻煩，請務必認真對待！<br><br>
								&#8250; <h3>交易截圖:</h3> 為了加快完單速度，確保交易截圖的完整性和安全性，請各位供貨商在完成交易後一小時內上傳截圖，我們也會第一時間檢查截圖並做完單處理；如果超過一小時還沒有接收到截圖，系統會自動掛單5天並開始聯絡顧客確認完單。所以，為了避免對應的麻煩，請各位供貨商及時上傳截圖。謝謝您的合作。<br><br>
								&#8250; <h3>訂單期限:</h3> 各位供貨商請注意，屆時所有訂單都必須在一小時內交易完畢。如果您無法在此段時間內交易，請立即聯繫我們的即時客服平台。對於在此時間段外完成交易的訂單，OffGamers概不負責。<br><br>
								&#8250; <a href="http://kb.offgamers.com/zhcn/?p=149">出售金幣的步驟</a>
								<br>&#8250; <a href="http://kb.offgamers.com/zhcn/?p=576">魔獸-賣方指南</a>
								<br>&#8250; <a href="http://kb.offgamers.com/zhcn/?p=407">賣方指南</a>
								<br>&#8250; <a href="http://www.offgamers.com/zh-CN/buyback-terms-and-conditions-cn-simp-i-631.ogm">賣方條款</a>
								<br>&#8250; <a href="http://kb.offgamers.com/zhcn/?p=159">購物代金券/貨款餘額指南</a>');

require(DIR_WS_LANGUAGES . 'add_ccgvdc_english.php');

// Captcha
define('ERROR_INVALID_CODE', '您輸入的驗證碼不正確，無法提交.');
define('TEXT_CAPTCHA_ANSWER', 'Ans.');
define('TEXT_REFRESH_CAPTCHA', '無法看清？, <a href="javascript:void(0);" onClick="get_captcha_img(\'%s\');">請換一張</a>');
define('TEXT_REFRESH_CAPTCHA_VIP', '無法看清？, <a href="javascript:void(0);" onClick="get_captcha_img(\\\'%s\\\');">請換一張</a>');
define('TEXT_CAPTCHA_INSTRUCTION', '如果答案為負值 , 請按示例輸入 (-8)');
define('NOTE_DO_NOT_RETURN_GOODS', '<b>重要提示：</b> 交易完成後, OffGamers供貨商絕不會要求您退還遊戲幣。也請您不要將遊戲幣退還給自稱為OffGamers供貨商的任何人或退還給與您交易的角色名相同的人員。');

// Facebook Connect
define('ERROR_INVALID_FB_UID', 'Facebook UID錯誤.');
define('SUCCESS_FB_DISCONNECTED', '<h3><font color=red>您的facebook帳戶已斷開</font></h3>');
define('HEADER_FB_CONNECT_SELECTION', '與Facebook連接');
define('TEXT_FB_CONNECT_SELECTION', '您好%s, <br>您有OffGamers帳戶嗎?');
define('OPTION_FB_CONNECT_SELECTION_FIRST', '<b>沒有，我初次來 OffGamers！ </b><br>創建新 OffGamers帳戶！');
define('OPTION_FB_CONNECT_SELECTION_SECOND', '<b>是的，我有 OffGamers帳戶！ </b><br>將我的 Facebook帳戶與OffGamers帳戶相連接！');

// HLA
define('TEXT_ADVANCED_SEARCH_OPTIONS', '其他筛选条件');

define('TEXT_HLA_ANY', '全部');

define('TABLE_HLA_HEADING_LEVEL', '等級');
define('TABLE_HLA_HEADING_RACE', '種族');
define('TABLE_HLA_HEADING_CLASS', '職業');
define('TABLE_HLA_HEADING_REFERRENCE', '參考編號');
define('TABLE_HLA_HEADING_PRICE', '價格');
define('TABLE_HLA_HEADING_ACTION', '運作');

define('TEXT_HLA_VIEW_PROFILE', '&#8250;&nbsp;查看英雄榜');
define('TEXT_HLA_ALTERNATE_CHARACTERS', '其它角色(ID)');

define('TEXT_HLA_CONFIRMATION_NOTES', '<font color="red"><b>重要提示:</b></font> 如果Blizzard發現同一個賬號在很短時間通過不同IP登錄，那麼就會鎖定此賬號。<br /><br />預防措施：<br />a)玩家可以通過登錄戰網自動識別新IP來避免新購買的賬號被鎖定。<br /><br />b)購入帳號後，建議玩家馬上把<a href="http://battle.net" target="_blank">戰網</a><font color="red"><b>國家區域</b></font>的設定改為成本身的國家區域。');

define('EMAIL_HLA_NEW_BUYBACK_ORDER_NUMBER', 'Buyback Order Number: %s');
define('EMAIL_HLA_NEW_BUYBACK_DATE_ORDERED', 'Buyback Order Date: %s');
define('EMAIL_HLA_NEW_BUYBACK_CUSTOMER_EMAIL', 'E-mail Address: %s');
define('EMAIL_HLA_NEW_BUYBACK_STATUS', 'Status: %s');
define('EMAIL_HLA_NEW_BUYBACK_SUBJECT', implode(' ', array(EMAIL_SUBJECT_PREFIX, "New Buyback Order #%s")));
define('EMAIL_HLA_NEW_BUYBACK_BODY', "Thank you for selling your items to " . STORE_NAME . ".\n\n Supplier Order Summary:\n" . EMAIL_SEPARATOR . "\n");
define('EMAIL_HLA_NEW_BUYBACK_PRODUCTS', "\n\n" . 'Products');
define('EMAIL_HLA_NEW_BUYBACK_ORDER_TOTAL', 'Total: %s');
define('EMAIL_HLA_NEW_BUYBACK_COMMENTS', 'Extra Information:');
define('EMAIL_HLA_NEW_BUYBACK_ORDER_CLOSING', "Please contact us via our Online Live Support service or e-<NAME_EMAIL> if you face an issue with the buyback. Please remember to include your Buyback Order Number when contacting us to expedite the process.\n\nYou will receive an e-mail notification whenever there is an update in your buyback order status. You can also review your buyback orders and check their status by signing in to OffGamers.com and clicking on \"View Buyback Order History\".\n\nThank you for supporting " . STORE_NAME . '.');

define('EMAIL_HLA_NEW_BUYBACK_ORDER_FOOTER', "\n\n" . EMAIL_FOOTER);

define('EMAIL_HLA_BUYBACK_ORDER_GUIDE', '
<b>Order Flow : [Pending] --> [Processing] --> [Completed] or [Canceled]</b>

[Pending] : Order submission incomplete. Kindly login to OffGamers.com and submit the missing character information.
[Processing] : This delivery is being registered into our system.
[Completed] : This delivery has been registered completely.
[Canceled] : The order has been canceled.

<b>Note 1:</b> Please ensure the Account information you provided is 100% accurate.

<b>Note 2:</b> Once the Account information been submitted, please do not login to the account, either via the account management page or ingame.

Important: Please take note that Blizzard may lock the account if they detect a different IP accessing the account within a short period of time. As a precaution:

<b>Note 3:</b> Once your order is in the [Completed] status, payment will be made available to you within 15 minutes at your OffGamers Account. Use the [Withdraw] function to withdraw your payment and have it credited to a payment method of your choosing.
');

define('EMAIL_HLA_SUPPLIER_SUBMITTED_INFO', '
<b>Order Flow : [Pending] --> [Processing] --> [Completed] or [Canceled]</b>

Submitted information
');

define('TEXT_HLA_SEARCH_NO_RECORDS', '抱歉,並無相對應角色/帳號可供選擇。<br />請重新選擇。');

define('HLA_REGION', '區域');
define('HLA_RACE', '種族');
define('HLA_CLASS', '職業');
define('HLA_SIDE', '陣營');
define('HLA_LEVEL', '等級');
define('HLA_GENDER', '性別');
define('HLA_PRICE', '價格');
define('HLA_TALENT', '天賦');
define('HLA_REFERENCE_ID', '參考編號(ID)');
define('HLA_SERVER', '服務器(區)');

// WOW : Race
define('HLA_RACE_BLOOD_ELF', '血精靈');
define('HLA_RACE_DRAENEI', '德萊尼');
define('HLA_RACE_DWARF', '矮人');
define('HLA_RACE_GNOME', '侏儒');
define('HLA_RACE_GOBLIN', '地精');
define('HLA_RACE_HUMAN', '人類');
define('HLA_RACE_NIGHT_ELF', '暗夜精靈');
define('HLA_RACE_ORC', '獸人');
define('HLA_RACE_PANDAREN', '熊貓人');
define('HLA_RACE_TAUREN', '牛頭');
define('HLA_RACE_TROLL', '巨魔');
define('HLA_RACE_UNDEAD', '亡靈');
define('HLA_RACE_WORGEN', '狼人');

// Warhammer : Race
define('HLA_RACE_CHAOS', '混沌軍團');
define('HLA_RACE_DARK_ELF', '黯精靈');
define('HLA_RACE_WARHAMMER_DWARF', '矮人王國');
define('HLA_RACE_EMPIRE', '人類帝國');
define('HLA_RACE_GREENSKIN', '綠皮部落');
define('HLA_RACE_HIGH_ELF', '高精靈');

// Age Of Conan : Race
define('HLA_RACE_AQUILONIAN', '亞奎隆人');
define('HLA_RACE_CIMMERIAN', '西米里人');
define('HLA_RACE_STYGIAN', '冥族');

// AION : Race
define('HLA_RACE_ASMODIAN', '魔族');
define('HLA_RACE_ELYOS', '天族');

// RIFT : Race
define('HLA_RACE_DEFIANT', '守護者');
define('HLA_RACE_GUARDIAN', '挑戰者');

// WOW : Class
define('HLA_CLASS_DEATH_KNIGHT', '死亡騎士');
define('HLA_CLASS_DRUID', '德魯伊');
define('HLA_CLASS_HUNTER', '獵人');
define('HLA_CLASS_MAGE', '法師');
define('HLA_CLASS_MONK', '武僧');
define('HLA_CLASS_PALADIN', '聖騎士');
define('HLA_CLASS_PRIEST', '牧師');
define('HLA_CLASS_ROGUE', '潛行者');
define('HLA_CLASS_SHAMAN', '薩滿祭司');
define('HLA_CLASS_WARLOCK', '術士');
define('HLA_CLASS_WARRIOR', '戰士');

// Warhammer : Class
define('HLA_CLASS_ARCHMAGE', '大魔導師');
define('HLA_CLASS_BLACK_ORC', '黑獸人');
define('HLA_CLASS_BLAZING_SUN_KNIGHT', '焰陽騎士');
define('HLA_CLASS_BRIGHT_WIZARD', '熾法師');
define('HLA_CLASS_CHOPPA', '碎骨獸人');
define('HLA_CLASS_CHOSEN', '混沌戰士');
define('HLA_CLASS_DARK_ELF_BLACK_GUARD', '黑暗衛士');
define('HLA_CLASS_DISCIPLE_OF_KHAINE', '卡恩使徒');
define('HLA_CLASS_ENGINEER', '工程師');
define('HLA_CLASS_IRONBREAKER', '鐵鎚勇士');
define('HLA_CLASS_MAGUS', '混沌奧術師');
define('HLA_CLASS_MARAUDER', '混沌掠奪者');
define('HLA_CLASS_RUNE_PRIEST', '符文牧師');
define('HLA_CLASS_SHADOW_WARRIOR', '陰影戰士');
define('HLA_CLASS_SLAYER', '碎鐵戰士');
define('HLA_CLASS_SORCERESS', '黑巫師');
define('HLA_CLASS_SQUIG_HERDER', '牙突牧者');
define('HLA_CLASS_SWORDMASTER', '御劍士');
define('HLA_CLASS_WARRIOR_PRIEST', '戰鬥牧師');
define('HLA_CLASS_WHITE_LION', '白獅');
define('HLA_CLASS_WITCH_ELF', '巫靈');
define('HLA_CLASS_WITCH_HUNTER', '獵巫人');
define('HLA_CLASS_ZEALOT', '混沌戰士');

define('HLA_CLASS_WARHAMMER_SHAMAN', '地精薩滿');

// Age Of Conan : Class
define('HLA_CLASS_ASSASSIN', '刺客');
define('HLA_CLASS_BARBARIAN', '野蠻人');
define('HLA_CLASS_BEAR_SHAMAN', '熊魂薩滿');
define('HLA_CLASS_CONQUEROR', '征服者');
define('HLA_CLASS_DARK_TEMPLAR', '黑暗聖堂');
define('HLA_CLASS_DEMONOLOGIST', '惡魔學家');
define('HLA_CLASS_GUARDIAN', '守護者');
define('HLA_CLASS_HERALD_OF_XOTLI', '羅里神使');
define('HLA_CLASS_NECROMANCER', '死靈法師');
define('HLA_CLASS_PRIEST_OF_MITRA', '密特拉牧師');
define('HLA_CLASS_RANGER', '遊俠');
define('HLA_CLASS_TEMPEST_OF_SET', '暴風賽特');

// AION : Class
define('HLA_CLASS_CHANTER', '護法星');
define('HLA_CLASS_CLERIC', '治愈星');
define('HLA_CLASS_GLADIATOR', '劍星');
define('HLA_CLASS_SORCERER', '精靈星');
define('HLA_CLASS_SPIRITMASTER', '魔道星');
define('HLA_CLASS_TEMPLAR', '守護星');
define('HLA_CLASS_AION_RANGER', '弓星');
define('HLA_CLASS_AION_ASSASSIN', '殺星');

define('HLA_SIDE_ALLIANCE', '聯盟');
define('HLA_SIDE_HORDE', '部落');

define('HLA_GENDER_MALE', '男');
define('HLA_GENDER_FEMALE', '女');

define('HLA_TALENT_AFFLICTION', '痛苦&nbsp;&nbsp;(術士)');
define('HLA_TALENT_ARCANE', '奧術&nbsp;&nbsp;(法師)');
define('HLA_TALENT_ARMS', '武器&nbsp;&nbsp;(戰士)');
define('HLA_TALENT_ASSASSINATION', '刺殺&nbsp;&nbsp;(潛行者)');
define('HLA_TALENT_BALANCE', '平衡&nbsp;&nbsp;(德魯伊)');
define('HLA_TALENT_BEAST_MASTERY', '野獸掌握&nbsp;&nbsp;(獵人)');
define('HLA_TALENT_BLOOD', '鮮血&nbsp;&nbsp;(死亡騎士)');
define('HLA_TALENT_COMBAT', '戰鬥&nbsp;&nbsp;(潛行者)');
define('HLA_TALENT_DEMONOLOGY', '惡魔學識&nbsp;&nbsp;(術士)');
define('HLA_TALENT_DESTRUCTION', '毀滅&nbsp;&nbsp;(術士)');
define('HLA_TALENT_DISCIPLINE', '戒律&nbsp;&nbsp;(牧師)');
define('HLA_TALENT_ELEMENTAL', '元素戰鬥&nbsp;&nbsp;(薩滿祭司)');
define('HLA_TALENT_ENHANCEMENT', '增強&nbsp;&nbsp;(薩滿祭司)');
define('HLA_TALENT_FERAL_COMBAT', '野性戰鬥&nbsp;&nbsp;(德魯伊)');
define('HLA_TALENT_FIRE', '火焰&nbsp;&nbsp;(法師)');
define('HLA_TALENT_FROST', '冰霜&nbsp;&nbsp;(法師,死亡騎士)');
define('HLA_TALENT_FURY', '狂怒&nbsp;&nbsp;(戰士)');
define('HLA_TALENT_HOLY', '神聖&nbsp;&nbsp;(聖騎士,牧師)');
define('HLA_TALENT_MARKSMANSHIP', '射擊&nbsp;&nbsp;(獵人)');
define('HLA_TALENT_PROTECTION', '防護&nbsp;&nbsp;(聖騎士,戰士)');
define('HLA_TALENT_RESTORATION', '恢復&nbsp;&nbsp;(薩滿祭司,德魯伊)');
define('HLA_TALENT_RETRIBUTION', '懲戒&nbsp;&nbsp;(聖騎士)');
define('HLA_TALENT_SHADOW', '暗影&nbsp;&nbsp;(牧師)');
define('HLA_TALENT_SUBTLETY', '敏銳&nbsp;&nbsp;(潛行者)');
define('HLA_TALENT_SURVIVAL', '生存&nbsp;&nbsp;(獵人)');
define('HLA_TALENT_UNHOLY', '邪惡&nbsp;&nbsp;(死亡騎士)');

// Warhammer : Server
define('HLA_SERVER_BADLANDS', 'Badlands');
define('HLA_SERVER_DARK_CRAG', 'Dark Crag');
define('HLA_SERVER_DARKLANDS', 'Darklands');
define('HLA_SERVER_GORFANG', 'Gorfang');
define('HLA_SERVER_HELDENHAMMER', 'Heldenhammer');
define('HLA_SERVER_IRON_ROCK', 'Iron Rock');
define('HLA_SERVER_IRONCLAW', 'Ironclaw');
define('HLA_SERVER_IRONFIST', 'Ironfist');
define('HLA_SERVER_MAGNUS', 'Magnus');
define('HLA_SERVER_MONOLITH', 'Monolith');
define('HLA_SERVER_OSTERMARK', 'Ostermark');
define('HLA_SERVER_PHOENIX_THRONE', 'Phoenix Throne');
define('HLA_SERVER_PRAAG', 'Praag');
define('HLA_SERVER_SKULL_THRONE', 'Skull Throne');
define('HLA_SERVER_THORGRIM', 'Thorgrim');
define('HLA_SERVER_VOLKMAR', 'Volkmar');
define('HLA_SERVER_VORTEX', 'Vortex');
define('HLA_SERVER_WASTELAND', 'Wasteland');

// Age Of Conan : Server
define('HLA_SERVER_BLOODSPIRE', 'Bloodspire');
define('HLA_SERVER_CIMMERIA', 'Cimmeria');
define('HLA_SERVER_GWAHLUR', 'gwahlur');
define('HLA_SERVER_SET', 'Set');
define('HLA_SERVER_TYRANNY', 'Tyranny');
define('HLA_SERVER_WICCANA', 'Wiccana');

// AION US: Server
define('HLA_SERVER_ISRAPHEL', 'Israphel');
define('HLA_SERVER_NEZEKAN', 'Nezekan');
define('HLA_SERVER_SIEL', 'Siel');
define('HLA_SERVER_VAIZEL', 'Vaizel');
define('HLA_SERVER_ZIKEL', 'Zikel');

// AION EU: Server
define('HLA_SERVER_BALDER', 'Balder');
define('HLA_SERVER_KROMEDE', 'Kromede');
define('HLA_SERVER_PERENTO', 'Perento');
define('HLA_SERVER_SPATALOS', 'Spatalos');
define('HLA_SERVER_SUTHRAN', 'Suthran');
define('HLA_SERVER_TELEMACHUS', 'Telemachus');
define('HLA_SERVER_THOR', 'Thor');
define('HLA_SERVER_URTEM', 'Urtem');

define('ERROR_INVALID_TOP_UP_ACCOUNT', '充值帳戶資訊錯誤。');
define('ERROR_DTU_EXCEED_TOP_UP_LIMIT', '您的充值已達到限額。');
define('ERROR_UNABLE_VALIDATE_TOP_UP_ACCOUNT', "遊戲官網服務器維護，暫時無法使用“直充服務”。網站恢復使用時，將另行通知。");
define('ERROR_DTU_NO_CHARACTERS_IN_LIST', "您所提供的帳戶資訊沒有角色記錄。");

define('TABLE_HEADING_DELIVERY_MODE', '發貨方式');

define('TEXT_INFO_SEND_TO_MY_ACCOUNT', '發送至OffGamers賬戶');
define('TEXT_INFO_CHOOSE_PREFFERED_DELIVERY_MODE', '選擇您的首選發貨方式');
define('TEXT_INFO_DELIVERY_MODE', '發貨方式');
define('TEXT_INFO_DELIVERY_SEND_TO_ACCOUNT', '發送至OffGamers賬戶');
define('TEXT_INFO_DELIVERY_DIRECT_TOP_UP', '官方充值');
define('TEXT_INFO_VIEW_CODE_IN_ACCOUNT_HISTORY', '在"我的賬戶"內查看');
define('TEXT_INFO_MOBILE_PHONE_NUMBER_IS_VALID', '<span class="requiredInfo">*</span> 請確保您當前的手機號碼是有效的。否則，請點擊<a href="' . tep_href_link(FILENAME_ACCOUNT_EDIT, '', 'SSL') . '">這裡</a>編輯。');

define('TEXT_PWL_IMPORTANT_NOTICE', '我們將會在1至2小時內開始代練您的訂單，請不要登錄已經提交給我們做代練的賬號因為這可能會導致IP地址發生衝突而把您的賬號列入被GM封號的風險。');

define('ERROR_INVALID_QUANTITY', '數量不正確');
define('ERROR_INVALID_TOP_UP_ACCOUNT', '充值帳戶資訊錯誤。');

define('ERROR_CHECKOUT_QUANTITY_CONTROL_EXCEED', '您購買的產品數量已達到該產品的最大購買量');
define('ERROR_CHECKOUT_QUANTITY_CONTROL_ORDERS', '您購買的產品已達到該產品的最大購買量。請檢查產品發貨時間，查看購買上限。 <br />');

define('TEXT_EXP_CHECKOUT_QTY_NORMAL', '&laquo; 返回');
define('TEXT_EXP_CHECKOUT_QTY_BULK', '點擊這裡購買更多');
define('TEXT_EXP_CHECKOUT_BULK_NOT_AVAILABLE', '您要求購買的產品數量不足。如果要大宗購物，請聯繫<a href="mailto:<EMAIL>"><EMAIL></a>');

define('COMBO_SELECT_QTY', '數量');
define('COMBO_SELECT_DELIVERY_MODE', '發貨方式');
define('ENTRY_QTY', '數量');
define('ENTRY_DELIVERY_METHOD', '交易方式');

define('WARNING_PRODUCT_PRICE_WITHOUT_GST', '稅前價格');

//Telesign phone verification
define('TEXT_CALL_LANGUAGE', '接聽電話語言:');
define('IMAGE_BUTTON_TEXT_ME_NOW', '立即發送短信');
define('TEXT_SMS_NOTES', '"立即發送短信"是一種短信功能，且只適用於移動電話。短信內容為英文。');

define('TEXT_INFO_VERIFYING', '核實中');
define('TEXT_INFO_SEARCH_NO_PRODUCT_FOUND', '<b>未找到您要的產品。</b>請嘗試使用其它關鍵詞進行搜索或者在<a href="' . tep_href_link(FILENAME_SEARCH_ALL_GAMES) . '">商舖</a >內尋找。');
define('TEXT_INFO_BROWSE_ALL_RESULTS', '瀏覽所有搜索結果');
define('MENU_TITLE_LOGIN_ACCOUNT', '登錄賬戶');
define('MENU_TITLE_REGISTER_ACCOUNT', '註冊賬戶');
define('MENU_TITLE_CHECKOUT', '結賬');
define('MENU_TITLE_BACK_TO_STORE', '返回購物商城');
define('BUTTON_BROWSE_IN_STORE', '瀏覽商城');
define('BUTTON_ALL_PAYMENT_METHODS', '所有付款方式');
define('HEADER_LOGIN_TO_YOUR_ACCOUNT', '會員登入');
define('LOGIN_WITH_FB_TITLE', '或登錄:');
define('BUTTON_READ_MORE_NEWS', '更多新聞');
define('MENU_HEADER_GROUP_BY_PLATFORM', '遊戲平台');
define('MENU_HEADER_GROUP_BY_PRODUCT_TYPE', '產品類型');

define('EMAIL_G2G_BUYBACK_SUBJECT', "New Buyback Order #%s");
define('EMAIL_G2G_BUYBACK_BODY', "Thank you for selling your items to %s.\n\n Supplier Order Summary:\n %s \n");
define('EMAIL_G2G_BUYBACK_ORDER_NUMBER', 'Buyback Order Number: %s');
define('EMAIL_G2G_BUYBACK_DATE_ORDERED', 'Buyback Order Date: %s');
define('EMAIL_G2G_BUYBACK_CUSTOMER_EMAIL', 'E-mail Address: %s');
define('EMAIL_G2G_BUYBACK_PRODUCTS', "\n\n" . 'Products');
define('EMAIL_G2G_BUYBACK_ORDER_TOTAL', 'Total: %s');
define('EMAIL_G2G_BUYBACK_COMMENTS', 'Extra Information:');
define('EMAIL_G2G_BUYBACK_STATUS', 'Status: Pending');
define('EMAIL_G2G_BUYBACK_ORDER_GUIDE', '<b>Order Flow : [Pending] --> [Processing] --> [Completed] or [Canceled]</b><br /><br />[Pending] : Order submission incomplete. Kindly login to OffGamers.com and submit the missing character information.<br />[Processing] : This delivery is being registered into our system.<br />[Completed] : This delivery has been registered completely.<br />[Canceled] : The order has been canceled.<br /><br /><b>Note 1:</b> Please ensure the Account information you provided is 100% accurate.<br /><br /><b>Note 2:</b> Once the Account information been submitted, please do not login to the account, either via the account management page or ingame.<br /><br />Important: Please take note that Blizzard may lock the account if they detect a different IP accessing the account within a short period of time. As a precaution: <br /><br /><b>Note 3:</b> Once your order is in the [Completed] status, payment will be made available to you within 15 minutes at your OffGamers Account. Use the [Withdraw] function to withdraw your payment and have it credited to a payment method of your choosing.');
define('EMAIL_G2G_BUYBACK_ORDER_CLOSING', "Please contact us via our Online Live Support service or e-mail to %s if you face an issue with the buyback. Please remember to include your Buyback Order Number when contacting us to expedite the process.\n\nYou will receive an e-mail notification whenever there is an update in your buyback order status. You can also review your buyback orders and check their status by signing in to OffGamers.com and clicking on \"View Buyback Order History\".\n\nThank you for supporting %s.");

define('TEXT_ERROR_MAX_PENDING_ORDER', '您的帳戶有%d待審單子，請檢查以前的訂單付款狀態');
define('EMAIL_MAX_PENDING_ORDER_SUBJECT', 'Customer ID #%d Hit Maximum Pending Order');
define('EMAIL_MAX_PENDING_ORDER_TEXT', 'Customer ID #%d Hit Maximum Pending Order, kindly liaise with customer.');

define('TEXT_DEFAULT_CONTACT_CS_ERROR_MESSAGE','如果錯誤仍然存在，請點擊<a href=\'http://support.offgamers.com/support/home\'>這裡</a>聯繫我們。');

define('TEXT_SIGN_UP', '註冊');
define('TEXT_LOG_IN', '登錄');
define('TEXT_OR_CONNECT', '或連接');
    
define('TEXT_MY_OGM', '我的OffGamers');
define('TEXT_OVERVIEW', '概述');
define('TEXT_BUY_HISTORY', '購買記錄');
define('TEXT_SELL_HISTORY', '銷售記錄');
define('TEXT_REQUEST_PAYMENT', '提款');
    
define('TEXT_ACCOUNT_ID', '帳戶ID');
define('TEXT_BUY_STATUS', '買家狀態');
define('TEXT_MANAGE_PROFILE', '帳號管理');
define('TEXT_SOCIAL_CONNECT', '社交連接');
define('TEXT_STORE_CREDITS', '購物代金券');
define('TEXT_WOR', 'WOR幣');
    
define('TEXT_REGIONAL_TITLE', '您下次訪問的區域設置。');
define('BTN_SAVE_CHANGES', '保存');
    
define('TEXT_OGM_DOMAIN', 'OffGamers.com');
define('TEXT_OGM_CAPTION', 'MMO遊戲 CDkeys 和遊戲卡');
define('TEXT_G2G_DOMAIN', 'G2G.com');
define('TEXT_G2G_CAPTION', 'Gamer2Gamer市場');
define('TEXT_GMZ_DOMAIN', 'Gamernizer.com');
define('TEXT_GMZ_CAPTION', '玩免費多人瀏覽器遊戲');
?>