<? 
/* 
   $Id: account_history_info.php,v 1.11 2007/12/27 09:01:24 wailai Exp $ 

   osCommerce, Open Source E-Commerce Solutions 
   http://www.oscommerce.com 

   Copyright (c) 2002 osCommerce 

   Released under the GNU General Public License 
*/ 

//@************ 
define('NAVBAR_TITLE_1', '我的帳戶'); 
define('NAVBAR_TITLE_2', '購買訂單歷史記錄'); 

if ($history_type=='buyback') { 
define('NAVBAR_TITLE_3', '回購訂單'); //define('NAVBAR_TITLE_3', 'Buyback Order #%s'); 
define('NAVBAR_TITLE_4', '回購訂單詳情'); 
define('HEADING_TITLE', '回購'); //define('HEADING_TITLE', 'Buyback Order Information'); 
define('HEADING_ORDER_NUMBER', '回購訂單號碼: %s'); //@************ 
} else { 
$history_type = 'orders'; 
$navbar3 = ($orders_type=="current" ? MY_ACCOUNT_CURRENT_ORDERS : ($orders_type=="completed" ? MY_ACCOUNT_COMPLETED_ORDERS : "")); 
define('NAVBAR_TITLE_3', $navbar3); 
define('NAVBAR_TITLE_4', '購買訂單詳情'); //define('NAVBAR_TITLE_3', 'Order #%s'); 
define('HEADING_TITLE', '購買'); //define('HEADING_TITLE', 'Order Information'); 
define('HEADING_ORDER_NUMBER', '購買訂單號碼: %s'); 
} 

define('ALERT_ORDER_TELEPHONE_VERIFICATION', "<div style=\"display:inline-block; font: bold 16px Arial,Tahoma,Verdana,Helvetica,sans-serif; color: black; padding: 0px 0px 10px 5px;\">與此訂單有關的電話號碼沒有驗證。<div style=\"display: block; font-weight: normal;\"><b>%s</b>要求在您方便接聽電話時打電話給您來一次電話認證。</div></div>"); 
define('ALERT_ORDER_STATUS_PENDING', "<table><tr><td><div style=\"display:block; font: bold 16px Arial,Tahoma,Verdana,Helvetica,sans-serif; color: black; padding: 0px 0px 10px 5px;\">一旦收到款項，我們會立刻處理您的訂單。<div style=\"display: block; font-weight: normal;\">通常情況下，我們的支付處理方會對一些付款要求進行強制性檢查從而導致付款延遲。
												如果您已為您的訂單打款了，但是發現訂單仍處於審核狀態，請先檢查您的銀行賬戶、銀行卡或電子錢包裡的錢是否已經完全支付出去。如果確定款項已成功支付，那麼再聯繫我們的客服平台。
												如果確定款項已成功支付，那麼再聯繫我們的客服平台。
									 		   </div></div></td></tr></table>");
define('ALERT_ORDER_STATUS_PENDING_CURRENCY', "<table><tr><td><div style=\"display:block; font: bold 16px Arial,Tahoma,Verdana,Helvetica,sans-serif; color: black; padding: 0px 0px 10px 5px;\">每次交易完畢後，請確認所收到的金幣數。<div style=\"display: block; font-weight: normal;\">每次交易完畢後，請您在訂單狀態頁面確認您已收到供貨商發送的金幣。
												如果您沒有確認，系統會在供貨商確認已發貨之時72小時後自動為您確認。<b>這也將確保您的訂單會儘快被處理,OP也將盡快打到您的賬戶。</b>
									 		   </div></div></td></tr></table>");
define('DATE_FORMAT_ORDER_DATE', '%d %b %Y'); 
define('DATE_FORMAT_ORDER_MESSAGE', '%d %b, %Y'); 

define('HEADING_ORDER_STATUS', '狀態: %s'); 
define('HEADING_ORDER_DATE', '訂單日期: %s'); 
define('HEADER_DELIVERY_DISPUTE', '發貨糾紛');

define('TITLE_PRODUCT_LIST', '產品列表'); 
define('TITLE_QUANTITY', '數量'); 
define('TITLE_AMOUNT', '總額'); 
define('TITLE_WHAT_TRADING_METHOD', '本次交易使用了哪種交易方式?');

define('SUB_TITLE_SUB_TOTAL', '次級共計: %s'); 
define('SUB_TITLE_ESTIMATED_TOTAL', '預計總量: %s'); 
define('SUB_TITLE_AMOUNT_DELIVERED', '已發貨金額: %s'); 
define('SUB_TITLE_AMOUNT_REFUNDED', '退款金額: %s'); 

define('TEXT_REASON', '原因:');
define('TEXT_DETAILS', '詳情:');
define('TEXT_DISPUTE_DESC', '如果您未收到我們發送給您的產品，請向我們發送一份舉報信。收到您的舉報後，我們會立即調查您的問題並在最短時間內將調查結果告知您。');
define('TEXT_DISPUTE_DETAILS', '詳情（如有）');
define('TEXT_CHARACTER_NAME', '角色名: %s'); 
define('TEXT_IN_GAME_TIME', '登錄時間:結帳後%d小時'); 
define('TEXT_IN_GAME_DURATION', '遊戲裡持續時間: %d小時'); 
define('TEXT_TOTAL_DELIVERED_GOLD', '發貨總數: %s '); 
define('TEXT_GOLD', '金幣'); 
define('TEXT_CURRENT_LEVEL', '當前等級: %s'); 
define('TEXT_LEVEL', '等級'); 
define('TEXT_HOURS_PASSED', '已耗時: %s'); 
define('TEXT_HOURS', '小時'); 
define('TEXT_REFUNDED', '已退款'); 
define('TEXT_SHOW_ALL', '顯示全部'); 
define('TEXT_HIDE_ALL', '隱藏全部'); 
define('TEXT_DOWNLOAD_ALL', '全部下載');
define('TEXT_GIFT_CARD', '禮品卡:');
define('TEXT_AMOUNT_IN_RED', '<span style="color:red;">- %s</span>');
define('TEXT_DISCOUNT_COUPON_NUMBER', '折扣券: %s'); 
define('TEXT_DISCOUNT_AMOUNT', '<span style="color:red;">- %s</span>'); 
define('TEXT_STORE_CREDIT', '代金券購貨: '); 
define('TEXT_STORE_CREDIT_AMOUNT', '<span style="color:red;">- %s</span>'); 
define('TEXT_PAYMENT_METHOD', '付款信息: %s'); 
define('TEXT_IS_UNLOADING', '卸載……'); 

define('TEXT_REMAINING_TIME', '剩餘時間');
define('TEXT_AUTO_CONFIRM_DELIVERY_NOTE', '請確認您是否已收到我們發送的產品，否則係統會在72小時內自動確認收到。');
define('TEXT_DID_YOU_RECEIVE', '您收到產品了嗎?');
define('TEXT_DID_YOU_ABLE_TO_LOGIN', '您可以登入嗎?');
define('TEXT_DELIVERY_CONFIRMED', '已確認收到');
define('TEXT_DID_NOT_RECEIVE', '未收到');
define('TEXT_ROLLED_BACK_DELIVERED_AMT', '系統已經回滾已發數量');
define('TEXT_HLA_AUTO_CONFIRM_DELIVERY_NOTE', '<table cellspacing="0" cellpadding="0" border="0"><tr><td nowrap valign="top"><font color="#FF0000"><b>重要提示：&nbsp;</b><br />訂單會在3天內由系統自動完成。基於賬號安全因素，<br />基於遊戲賬號安全性考慮，一旦客戶訂單帳號資料已發，針對這樣的訂單我們概不退款。</font></td></tr></table>');
define('TEXT_HLA_SECRET_ANSWER_NOTE', '確認能登陸後，點擊 "是" 系統便會自動發帳號的(密保) Secret Question & Answer');

define('TEXT_TOP_UP_STATUS', '充值狀態');
define('TEXT_TOP_UP_MESSAGE', '充值訊息');

define('TEXT_BUYBACK_OPENED', '採購開啟');
define('TEXT_BUYBACK_CLOSED', '採購關閉');
define('TEXT_BUYBACK_OPENED_DURATION', '時限設為 (%s) 小時在遊戲內');
define('TEXT_BUYBACK_OPENED_DURATION_EXTEND', '時限設為+ (%s) 小時在遊戲內');
define('TEXT_BUYBACK_CLOSED_UNABLE_LOCATE_CUSTOMER', '單子無法聯絡上顧客 (%s)');
define('TEXT_BUYBACK_CLOSED_DURATION_TIME_OUT', '在遊戲內時限已過');
define('TEXT_BUYBACK_CLOSED_MANUALLY', '用戶設 -(%s) 小時提早關閉採購');

define('TEXT_TOTAL_ACCUMULATED_OPEN_TIME', '總共累計開啟採購時間：( %s )');
define('TEXT_PROVIDE_DELIVERY_INFO', '您未提供任何發貨信息。<a href="javascript:void(0);" onClick="showMe(\'%s\');"><b>點擊這裡</b></a>提交發貨信息，以便我們安排交易。');

define('BUTTON_EXTEND_TIME', '延長時間');
define('BUTTON_IM_ONLINE_NOW', '我在線');
define('ENTRY_IN_GAME_DURATION', '在線時長:');
define('ENTRY_HOURS', '小時');

define('IMAGE_BUTTON_SUBMIT_PAYMENT_INFO', '提交付款信息'); 
define('IMAGE_BUTTON_PAY_FOR_THIS_ORDER_NOW', '為該訂單付款'); 
//define('IMAGE_BUTTON_SHOW_CD_KEY_CODE', 'Show CD Key Code'); 
define('IMAGE_BUTTON_SHOW', '顯示'); 
define('IMAGE_BUTTON_HIDE', '隱藏'); 
define('IMAGE_BUTTON_HIDE_CD_KEY_CODE', '隱藏CD Key代碼'); 
define('IMAGE_BUTTON_CHARACTERS_PROFILER', '角色分析器'); 
define('LINK_CANCEL_THIS_ORDER', '取消該訂單'); 
define('LINK_COMPLAINT_THIS_ORDER', '投訴單子');

define('ERROR_ORDER_IN_TRADING', '您的單子已經在交易狀態中，請立即聯絡我們的客服爭取而外的協助。');
define('ACCOUNT_HISTORY_DELIVERY_MODE_NOTES', "<b>請注:</b> 我們會根據您所選的交易方式盡快為您的訂單安排交易。如果在您要求更改交易方式之前就已交易完畢，OffGamers不承擔任何責任。");

define('MESSAGE_ORDER_CANCELLED', '該訂單將在%s小時%s分鐘後取消。'); 
define('MESSAGE_PAYMENT_INFORMATION', '如果您已為該訂單付款，請提交您的付款信息。'); 

define('HEADER_ORDER_MESSAGE', '訂單信息'); 
define('HEADER_DELIVERY_INFO_TITLE', '一旦您登陸游戲後，我們會立即為您的訂單尋找貨源。');
define('HEADER_PWL_DELIVERY_INFO_TITLE', '我們將會在代練單子完成後才購買金幣。如果有任何疑問，請聯繫我們的即時平台。');
define('HEADER_DATE', '日期');
define('HEADER_TIME', '時間');
define('HEADER_DELIVERED_AMOUNT', '接貨數量');
define('HEADER_DELIVERED_CHARACTER', '接貨ID');
define('HEADER_ACTION', '運作');


// Cancel Customer Order
define('BUTTON_CANCEL_ORDER_NO', '否定');
define('BUTTON_CANCEL_ORDER_YES', '確定');
define('BUTTON_CANCEL_ORDER_OK', '確定');

define('HEADING_TITLE_CANCEL_ORDER', '訂單取消請求');

define('TITLE_CANCEL_ORDER_NUMBER', '訂單號碼:');
define('TITLE_CANCEL_ORDER_PAYMENT_METHOD', '付款方式:');
define('TITLE_CANCEL_ORDER_AMOUNT', '總額:');

define('TEXT_DISCOUNT_COUPON', '折扣券');

define('MESSAGE_CANCEL_ORDER_CONFIRM', '您確定要取消此訂單?');
define('MESSAGE_CANCEL_ORDER_CANCELLED_1', '訂單已取消。');
define('MESSAGE_CANCEL_ORDER_CANCELLED_2', '訂單已取消。款項會在24小時內以購物代金券的形式退還至您的OffGamers賬戶。');
define('MESSAGE_CANCEL_ORDER_CANCELLED_3', '此訂單正等待取消中。如果您的訂單款項（全部/部分）是以購物代金券支付的，請聯繫我們的客服平台，以加速訂單的取消。');
define('MESSAGE_UNAUTHORISED_CANCEL_ORDER', '此訂單不屬於您，您無法取消該訂單。');
?>