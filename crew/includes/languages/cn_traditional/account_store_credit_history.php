﻿<?
/*
  	$Id: account_store_credit.php,v 1.0 2008/12/15 11:56:38 wilson.sun Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2003 osCommerce
	
  	Released under the GNU General Public License
*/
define('NAVBAR_TITLE_1', BOX_HEADING_LOGIN_BOX_MY_ACCOUNT);
define('NAVBAR_TITLE_2', LOGIN_BOX_ACCOUNT_STORE_CREDIT);

define('NAVBAR_TITLE', HEADER_TITLE_MY_ACCOUNT);
define('HEADING_TITLE', LOGIN_BOX_ACCOUNT_STORE_CREDIT);

define('TABLE_HEADING_STORE_CREDIT_HISTORY', '購物代金券歷史記錄');

define('TAB_HEADING_HISTORY', '歷史記錄');
define('TAB_HEADING_STORE_CREDIT_PURCHASED', '所購買的購物代金券');
define('TAB_HEADING_STORE_CREDIT_USED', '所消費的購物代金券');

define('TABLE_HEADING_HISTORY_DATE', '日期');
define('TABLE_HEADING_HISTORY_TYPE', '類型');
define('TABLE_HEADING_HISTORY_AMOUNT', '金額');
define('TABLE_HEADING_HISTORY_BALANCE', '餘額');

define('TABLE_HEADING_SC_PURCHASED_DATE', '日期');
define('TABLE_HEADING_SC_PURCHASED_ORDER', '訂單號');
define('TABLE_HEADING_SC_PURCHASED_AMOUNT', '金額');

define('TABLE_HEADING_SC_USED_DATE', '日期');
define('TABLE_HEADING_SC_USED_ORDER', '訂單號');
define('TABLE_HEADING_SC_USED_AMOUNT', '金額');

define('TEXT_SC_HISTORY_STATUS_MI', '手動添加');
define('TEXT_SC_HISTORY_STATUS_MR', '手動扣除');
define('TEXT_SC_HISTORY_STATUS_PW', 'OffGamers付款');
define('TEXT_SC_HISTORY_STATUS_R', '退款');
define('TEXT_SC_HISTORY_STATUS_V', '購物代金券轉換');
define('TEXT_SC_HISTORY_STATUS_X', '取消');
define('TEXT_SC_HISTORY_STATUS_D', 'OP兌換');
define('TEXT_SC_HISTORY_STATUS_S', '購物代金券已發數量');
define('TEXT_SC_HISTORY_STATUS_XS', '購物代金券獎勵數量(+%s%%)');
define('TEXT_SC_HISTORY_STATUS_C', '賠償');
define('TEXT_SC_HISTORY_STATUS_P', '已消費的購物代金券');
define('TEXT_SC_HISTORY_STATUS_GC', '禮品卡兌換');

define('TEXT_SC_HISTORY_STATUS_S_DEDUCT', '購物代金券扣除額（無效交易）');
define('TEXT_SC_HISTORY_STATUS_XS_DEDUCT', '購物代金券獎勵扣除額（無效交易）');

define('TEXT_MY_STORE_CREDIT_BALANCE', '我的購物代金券餘額: <b>%s</b>');
define('TEXT_TOP_UP_STORE_CREDITS', '充值購物代金券');
define('TEXT_OR_CONVERT_NOW', '或立刻轉換');
?>