<?php 
/* 
   $Id: advanced_search.php,v 1.3 2004/11/30 10:07:42 subrat Exp $ 
*/ 

define('NAVBAR_TITLE_1', '高級搜索'); 
define('NAVBAR_TITLE_2', '搜索結果'); 

define('HEADING_TITLE', '高級搜索'); 
define('HEADING_TITLE_1', '高級搜尋'); 
define('HEADING_TITLE_2', '符合搜索條件'); 

define('HEADING_SEARCH_CRITERIA', '關鍵詞'); 

define('TEXT_SEARCH_IN_DESCRIPTION', '在產品說明中搜索'); 
define('ENTRY_CATEGORIES', '類別:'); 
define('ENTRY_INCLUDE_SUBCATEGORIES', '包含類別'); 
define('ENTRY_MANUFACTURERS', '製造商:'); 
define('ENTRY_PRICE_FROM', '價格:'); 
define('ENTRY_PRICE_TO', '價格:'); 
define('ENTRY_DATE_FROM', '開始日期:'); 
define('ENTRY_DATE_TO', '截止日期:'); 
define('ENTRY_SORT_BY', '排序:'); 
define('ENTRY_ITEM_PER_PAGE', '每頁項目:'); 
define('ENTRY_ASCENDING', '升序'); 
define('ENTRY_DESCENDING', '降序'); 

define('TEXT_SEARCH_HELP_LINK', '幫助[?]'); 
//define('TEXT_ALL_CATEGORIES', '所有類別'); ' 
define('TEXT_ALL_MANUFACTURERS', '所有製造商'); 

define('HEADING_SEARCH_HELP', '幫助搜索'); 

define('TEXT_SEARCH_HELP', '輸入的關鍵詞可能是分開的，為更有精確搜尋結果。<br><br>例如：<u>微軟和滑鼠</u>搜尋結果可能出現以上兩種產品，不過，<u>滑鼠和鍵盤</u>搜尋結果可能是兩種或一種產品。相關度越高的說明，檢索出來的內容和關鍵詞越相近。<br><br>為完全匹配，可使用雙引號。<br><br>舉例來說，<u>"筆記本電腦"</u>將會出現更精確搜尋結果。<br><br>括號內可用為進一步影響搜尋結果。<br><br>例如，<u>微軟和（鍵盤或滑鼠或“編程語言”）</u>.'); 
define('TEXT_CLOSE_WINDOW', '關閉窗口[x]'); 
define('TEXT_ITEM_ADDED_TO_CART', '該產品已添加到您的購物車<a href="shopping_cart.php">購物車.</a>'); 
                                                        
define('TABLE_HEADING_IMAGE', ''); 
define('TABLE_HEADING_MODEL', '模型'); 
define('TABLE_HEADING_PRODUCTS', '產品名稱'); 
define('TABLE_HEADING_MANUFACTURER', '運營商'); 
define('TABLE_HEADING_QUANTITY', '數量'); 
define('TABLE_HEADING_PRICE', '價格'); 
define('TABLE_HEADING_WEIGHT', '淨重'); 
define('TABLE_HEADING_BUY_NOW', '立即購買'); 
define('TABLE_HEADING_PRODUCT_SORT', '按產品類別排序'); 

define('TEXT_NO_PRODUCTS', '未搜索到相關內容.'); 

define('ERROR_AT_LEAST_ONE_INPUT', '在搜索表單中至少輸入一個內容.'); 
define('ERROR_INVALID_FROM_DATE', '無效日期自起.'); 
define('ERROR_INVALID_TO_DATE', '無效日期到止.'); 
define('ERROR_TO_DATE_LESS_THAN_FROM_DATE', '截止日期必須大於開始日期.'); 
define('ERROR_PRICE_FROM_MUST_BE_NUM', '最低價格.'); 
define('ERROR_PRICE_TO_MUST_BE_NUM', '最高價格.'); 
define('ERROR_PRICE_TO_LESS_THAN_PRICE_FROM', '最高價格必須大於最低價格.'); 
define('ERROR_INVALID_KEYWORDS', '無效的關鍵詞.'); 
?>
