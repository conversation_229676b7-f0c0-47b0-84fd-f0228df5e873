<? 
/* 
   $Id: create_account.php,v 1.13 2007/12/21 09:03:49 chan Exp $ 

   osCommerce, Open Source E-Commerce Solutions 
   http://www.oscommerce.com 

   Copyright (c) 2003 osCommerce 

   Released under the GNU General Public License 
*/ 

define('NAVBAR_TITLE', '註冊帳戶'); 
define('HEADING_TITLE', '註冊'); 

define('TEXT_ORIGIN_LOGIN', '如果您已經有一個帳戶，請在<a href="%s">login page</a>.登錄'); 
                             

define('TABLE_HEADING_COMMENTS', '其他信息'); 

define('EMAIL_SUBJECT', '激活帳戶'); 
define('EMAIL_WELCOME', '我們歡迎你到' . STORE_NAME . ',您的一站式網上商店為您所有的遊戲虛擬物品和高水準的代練服務我們會向所有電子郵件包括密碼重置和訂單的更新發送這個電子郵件地址' . "\n\n"); 
   
define('EMAIL_INFO', "\n\n" . '與一帳戶' . STORE_NAME . ",您將登錄到我們的商店:-"."\n\n"); 
define('EMAIL_LINK_INFO', '你可以點擊下面的鏈接，以激活您的帳戶.' . "\n\n"); 
define('EMAIL_LINK_ADDRESS_INFO', "\n\n" . '如果您無法點擊鏈接，複製並粘貼整個下面的網址進入您的網頁瀏覽器.' . "\n\n"); 
define('EMAIL_MANUAL_ACTIVATE_EMAIL', "\n" . '您也可以手動激活您的帳戶<a href="' . tep_href_link(FILENAME_ACCOUNT_ACTIVATE, 'aov=activate') . '">' . tep_href_link(FILENAME_ACCOUNT_ACTIVATE, 'aov =activate') . '</a>通過輸入以下信息<br><br>電子郵件地址: '); 
define('EMAIL_MANUAL_ACTIVATE_CODE', "<br>" . '激活代碼: '); 
define('EMAIL_TEXT', '-更新您的聯繫信息或更改您的帳戶密碼,'."\n".'-訂閱或退訂我們的電子報,'."\n".'-為稍後結帳,您的購物車將自動保存內容,'."\n".'-自動保存的內容，您的購物車為稍後結帳,'."\n".'-收到特別的促銷和折扣告示(只適用於訂閱我們的電子報).'."\n\n"); 
define('EMAIL_CONTACT', '如有沒有任何查詢或協助，請使用我們的在線客服，或通過發送電子郵件查詢' . EMAIL_TO . '.謝謝你購物' . STORE_NAME . ".\n\n\n" ); 

define('EMAIL_WARNING', '注意：您收到此電子郵件，因為你已經註冊了一個帳戶在' . STORE_NAME . "使用這個電子郵件地址.如果您的電子郵件地址在未經您的同意下使用請發郵到" . '<a href="mailto:' . EMAIL_TO . '">'.EMAIL_TO.'</a>立即取消此帳戶.' . "我們也建議你改變您的貝寶帳戶密碼(如果您擁有)由於騙徒通常註冊使用其中一種的電子郵件地址後，他們獲得了相應的貝寶帳戶(通過釣魚網站，已儲存的密碼檢索，鍵盤記錄程序，間諜軟體，等).當然，這也可能是我們的客戶在進入電子郵件地址疏誤.請理解我們的商店不容忍網際網路詐騙.這就是為什麼我們實施的電子郵件地址核實所有新帳戶註冊和沒有購買可以作出不首先核實電子郵件地址." . "\n"); 
define('EMAIL_FOOTER', STORE_EMAIL_SIGNATURE); 

define('EMAIL_GV_INCENTIVE_HEADER', '作為的一部分，我們歡迎新客戶，我們向您發送了一個電子禮券價值的％ s'); 

define('EMAIL_GV_REDEEM', '該兌換的代碼是％ s ，您可以輸入兌現代碼時，檢查後，作出了購買'); 
     define('EMAIL_GV_LINK', '或走這條連結'); 
define('EMAIL_COUPON_INCENTIVE_HEADER', '恭喜，讓您第一次訪問我們的網上商店更有價值的經驗' . "\n" . 
                                         '下面為了你創造的折扣優惠券' . "\n\n"); 
define('EMAIL_COUPON_REDEEM', '使用優惠券輸入兌現代碼，這是％ s期間結帳, ' . "\n" . 
                                '過後購買'); 
?>