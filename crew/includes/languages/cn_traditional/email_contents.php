<?
// All email related text must be defined here in case email using different CHARSET than webpage
// e-mail greeting
define('EMAIL_GREET_MR', '親愛的%s先生,' . "\n\n");
define('EMAIL_GREET_MS', '親愛的%s小姐,' . "\n\n");
define('EMAIL_GREET_NONE', '親愛的%s,' . "\n\n");

define('EMAIL_DISPLAY_CURRENCY', "￥");

define('EMAIL_LOCAL_STORE_NAME', '臥虎遊戲');
define('EMAIL_LOCAL_STORE_EMAIL_SIGNATURE', '順祝商祺'."\n\n".'臥虎遊戲團隊啟'."\n\n".'************** ***********************************'."\n".EMAIL_LOCAL_STORE_NAME.' -您的遊戲聯盟'."\n".'****************************************** *******'."\n".'電郵: <a href="mailto:'.EMAIL_LOCAL_STORE_EMAIL.'">' . EMAIL_LOCAL_STORE_EMAIL.'</a>');
define('EMAIL_LOCAL_EMAIL_SUBJECT_PREFIX', '[臥虎遊戲]');

define('EMAIL_BUYBACK_ORDER_GUIDE', '
<b>訂單流程: [待定] -> “ [處理] - >” [完成]或[取消]</b>

[待定] ：請稍等,我們將提供您我們角色的資料.
[處理] ：交付正在被我們的系統處理.
[完成] ：提供已註冊完全。
[取消] ：該訂單已被取消.

<b>注意1:請確保您所提供的角色名稱是100%準確。如角色名稱出現錯誤引起問題，我們不承擔任何的責任所.
注意2:交易完畢後，請填入[出貨數量],以便我們支付給您
注意3: [提交數量]和[出貨數量]需相同。如果數量不同，我們不承擔任何帳戶關閉的責任.
注意4:一旦您的訂單[完成]後，付款將15分鐘內提供給您.您可通過OffGamers帳戶,使用[撤回]功能把付款撤回您所選擇的支付方式.</b>
');

// Sign Up Account
define('EMAIL_SIGNUP_STEP1_SUBJECT', implode(' ', array(EMAIL_LOCAL_EMAIL_SUBJECT_PREFIX, "激活帳號信")));
define('EMAIL_SIGNUP_STEP2_SUBJECT', implode(' ', array(EMAIL_LOCAL_EMAIL_SUBJECT_PREFIX, "成功啟動帳號")));

define('EMAIL_SIGNUP_STEP1_BODY', '謝謝您加入我們供應商陣容。'. "\n\n" .
EMAIL_LOCAL_STORE_NAME.'由始至終都相信，我們的成功都是靠供應商的配合與支持，秉承著這個觀念，我們將盡其所能維護供應商利益，永遠保持供應商與臥虎遊戲雙贏的局面。我們懇切地希望能與您長期地合作，因為只有這樣我們才能一起在這個競爭力高的行業共同成長。 ' . "\n\n" .
'註冊的2個步驟：'. "\n\n" .
'步驟1 ：提交註冊表格..完成' . "\n\n" .
'步驟2 :啟動帳號::等待中' . "\n\n" .
'請執行這個鏈接和及提交啟動表格以完成註冊過程。 ' . "\n" .
'您可以點擊下面的鏈接（如果您的電子郵件系統允許),或粘貼下面鏈接拷貝到您的瀏覽器。 ' . "\n\n" .
'<a href="%s">%s</a>'. "\n\n" .
'或拷貝與黏貼這個啟動密碼入激活帳號網頁所提供的啟動密碼格子：'."\n\n" .
'<b>'."%s".'</b>');

define('EMAIL_SIGNUP_STEP2_BODY', '您的'.EMAIL_LOCAL_STORE_NAME.'帳號已被啟動。'. "\n\n" .
'註冊包括2個步驟：'. "\n\n" .
'<b><u>步驟1 ：提交註冊表格.. <i>完成</i></u></b>' . "\n\n" .
'<b><u>步驟2 :啟動帳號:: <i>完成</i></u></b>' . "\n\n" .
'現在您可以在'.EMAIL_LOCAL_STORE_NAME.'登入' . "\n\n");

define('EMAIL_SIGNUP_STEP2_VIP_BODY','歡迎加入'.EMAIL_LOCAL_STORE_NAME.'VIP成員計劃！' . "\n\n" .
'成為'.EMAIL_LOCAL_STORE_NAME.'VIP成員您將會享受到無比自由的感覺：' . "\n\n" .
'1、自由更新所擁有的遊戲、服務器、庫存等；' . "\n" .
'2、自由選擇想出售的金幣數量，如果銷售單沒有達到您要求的供貨數量則不會被交易通知所騷擾；' . "\n" .
'3、自由選擇接受的支付貨幣——美金人民幣隨您挑，同時登入VIP交易模塊會使您更加清晰的管理自己的交易單和交易款項；' . "\n" .
'4、自由享受供貨商VIP等級晉升所帶來的利潤分配比例的提高（'.EMAIL_LOCAL_STORE_NAME.'會將金幣銷售價格透明化地告知您，我們一起共享利潤分配）' . "\n" );

define('EMAIL_ADMIN_SIGNUP_SUCCESS_BODY', '新的帳號已被啟動。'. "\n\n" .
'供應商電子信箱：%s'. "\n" );

define('EMAIL_SIGNUP_FOOTER', "\n\n" . EMAIL_LOCAL_STORE_EMAIL_SIGNATURE);

//upgrade/downgrade rank
define('EMAIL_RANK_CHANGE_SUBJECT', implode(' ', array(EMAIL_LOCAL_EMAIL_SUBJECT_PREFIX, "會員級別更改通知")));
define('EMAIL_RANK_LEVEL_UP', '上升至');
define('EMAIL_RANK_LEVEL_DOWN', '下降至');
define('EMAIL_NO_RANK_AVAILABLE', '沒等級');
define('EMAIL_RANK_CHANGE_BODY', '你目前的級別：%s '. "\n" .
'等級：由%s%s%s ' . "\n" .
'分享臥虎遊戲金幣賣價百分比：由%s%%%s%s%% ' . "\n" .
'總積分: %s ' . "\n" .
'所需要的下一等級升級分數: %s ' . "\n\n");

define('EMAIL_RANK_CHANGE_SUGGEST_BODY', '建議1從VIP1等級升級，就是從VIP1級開始向上排列，給人一種無限發展的可能' . "\n" .
'建議2晉升等級增加的分享比例要減少些，比方說晉升一級2%-5%' . "\n" .
'建議3可以適當考慮每晉升一個等級相應的稱呼可以改變'."\n");

define('EMAIL_RANK_CHANGED_FOOTER', "\n\n" . EMAIL_LOCAL_STORE_EMAIL_SIGNATURE);

// New Buyback Order
define('EMAIL_NEW_BUYBACK_SUBJECT', implode(' ', array(EMAIL_LOCAL_EMAIL_SUBJECT_PREFIX, "新單子#%s已在審核中")));

define('EMAIL_NEW_BUYBACK_BODY', "謝謝您賣貨物給".EMAIL_LOCAL_STORE_NAME.".\n\n供應商單子總結：\n".EMAIL_SEPARATOR."\n");
define('EMAIL_NEW_BUYBACK_ORDER_NUMBER', '單子編號: %s');
define('EMAIL_NEW_BUYBACK_DATE_ORDERED', '單子日期: %s');
define('EMAIL_NEW_BUYBACK_CUSTOMER_EMAIL', '供應商信箱郵址: %s');
define('EMAIL_NEW_BUYBACK_PRODUCTS', '產品');
define('EMAIL_NEW_BUYBACK_ORDER_TOTAL', '總額: %s %s');
define('EMAIL_NEW_BUYBACK_COMMENTS', '附加信息:');
define('EMAIL_NEW_BUYBACK_STATUS', '狀態:審核');
define('EMAIL_NEW_BUYBACK_ORDER_CLOSING', '您可以登入<a href="'.tep_href_link('/', '', 'SSL', false).'">'.tep_href_link('/', '', 'SSL', false).'</a>然後點入“單子狀態”來回顧你的訂單紀錄、訂單狀態以及接貨ID');

define('EMAIL_NEW_BUYBACK_ORDER_FOOTER', "\n\n" . EMAIL_LOCAL_STORE_EMAIL_SIGNATURE);
// Buyback Order Update
define('EMAIL_BUYBACK_UPDATE_SUBJECT', implode(' ', array(EMAIL_LOCAL_EMAIL_SUBJECT_PREFIX, "單子更新#%s已被取消")));

define('EMAIL_BUYBACK_UPDATE_BODY', "謝謝您賣貨物給".EMAIL_LOCAL_STORE_NAME.".\n\n供應商單子總結：\n".EMAIL_SEPARATOR."\n");
define('EMAIL_BUYBACK_UPDATE_ORDER_NUMBER', '單子編號: %s');
define('EMAIL_BUYBACK_UPDATE_DATE_ORDERED', '單子日期: %s');
define('EMAIL_BUYBACK_UPDATE_CUSTOMER_EMAIL', '供應商信箱郵址: %s');
define('EMAIL_BUYBACK_UPDATE_PRODUCTS', '產品');
define('EMAIL_BUYBACK_UPDATE_ORDER_TOTAL', '總額: %s %s');
define('EMAIL_BUYBACK_UPDATE_COMMENTS', '附加信息:');

define('EMAIL_BUYBACK_UPDATE_FOOTER', "\n\n" . EMAIL_LOCAL_STORE_EMAIL_SIGNATURE);

define('EMAIL_PAYMENT_PAYMENT_UPDATE_SUBJECT', implode(' ', array(EMAIL_LOCAL_EMAIL_SUBJECT_PREFIX, "款項#%s已在審核中")));

define('EMAIL_PAYMENT_TEXT_TITLE', '您的提款資料為,');
define('EMAIL_PAYMENT_TEXT_SUMMARY_TITLE', '款項總結：');
define('EMAIL_PAYMENT_TEXT_PAYMENT_NUMBER', '款項編號：%s');
define('EMAIL_PAYMENT_TEXT_PAID_AMOUNT', '支付總額: %s');
define('EMAIL_PAYMENT_TEXT_PAYMENT_DATE', '提款日期：%s');
define('EMAIL_PAYMENT_TEXT_STATUS_UPDATE', "狀態：%s");
define('EMAIL_PAYMENT_TEXT_COMMENTS', '評論：' . "\n%s\n");
define('EMAIL_PAYMENT_WITHDRAW_PROCESS_GUIDE', '
<b>提款流程：提款-->審核-->處理-->完成/取消</b>

提款：款項必須從【結存餘額】提出
審核：等待財務部審核款項數目
處理：財務部正運行發款
完成：款項已發到供應商銀行賬號
取消：單子已被供應商或臥虎遊戲取消
');
define('EMAIL_PAYMENT_FOOTER', EMAIL_LOCAL_STORE_EMAIL_SIGNATURE);

//Email payment status
$PAYMENT_STATUS_ARRAY = array('審核' => '1',//Pending
                               '處理' => '2',//Processing
                               '完成' => '3',//Completed
                               '取消' => '4' //Canceled
                               );

// Pre-Sale
define('EMAIL_BUYBACK_PRESALES_SUBJECT', implode(' ', array(EMAIL_LOCAL_EMAIL_SUBJECT_PREFIX, "開收服務器通知")));

define('EMAIL_BUYBACK_PRESALES_TITLE', '謝謝您的支持！目前您可以馬上登入'.EMAIL_LOCAL_STORE_NAME.'售賣您所登記的服務器');
define('EMAIL_BUYBACK_PRESALES_SUMMARY_TITLE', '開收服務器總結：');
define('EMAIL_BUYBACK_PRESALES_PRODUCT', '服務器：%s');
define('EMAIL_BUYBACK_PRESALES_MIN_QTY', '最低數量：%d');
define('EMAIL_BUYBACK_PRESALES_MAX_QTY', '最高數量：%d');
define('EMAIL_BUYBACK_PRESALES_COMMENTS', '附加信息:'."\n\n".EMAIL_LOCAL_STORE_NAME.'的開收服務只是開放給有登記開收服務的供應商，採購的方式是以先賣為快，請不要耽誤您出貨的機會。謝謝！');
define('EMAIL_BUYBACK_PRESALES_FOOTER', "\n\n" . EMAIL_LOCAL_STORE_EMAIL_SIGNATURE);

//vip new vip order
define('EMAIL_NEW_VIP_ORDER_SUBJECT', implode(' ', array(EMAIL_LOCAL_EMAIL_SUBJECT_PREFIX, "新VIP單子 #%s 已在%s中 %s")));
define('EMAIL_TRADING_MODE', '交易方式：%s');

$VIP_TRADE_MODE = array('vip_deal_on_game' => '與客戶交易',
'ofp_deal_with_customers' => '與客戶交易'
                        );

//Email buyback order status
$BUYBACK_ORDER_STATUS_ARRAY = array('審核' => '1',//Pending
'處理' => '2',//Processing
'完成' => '3',//Completed
'取消' => '4' //Canceled
);

define('EMAIL_NEW_VIP_ORDERS_SUMMARY','供應商單子總結：
-------------------------------------------------- ----
單子編號: %s
單子日期: %s
供應商信箱郵址: %s
交易方式：%s

產品
-------------------------------------------------- ----
%s x %s = %s
-------------------------------------------------- ----
總額: %s

附加信息:


狀態: %s ');


define('EMAIL_ADMIN_NEW_VIP_ORDERS_SUMMARY','供應商單子總結：
-------------------------------------------------- ----
單子編號: %s
單子日期: %s
供應商信箱郵址: %s
交易方式：%s

產品
-------------------------------------------------- ----
%s
-------------------------------------------------- ----
總額: %s

附加信息:


狀態: %s ');


define('EMAIL_VIP_ORDER_GUIDE', '
<b>與客戶交易下單流程：【審核】 --> 【處理】 --> 【完成】或【取消】</b>

【審核】 ：直接發貨給客戶，客戶的ID將會出現在我們的網站上。發貨前記得得密客戶交易前密碼，商量好地點，交易時得截一個完整的圖，最後得和客戶拿交易完畢密碼。
【處理】 ：交易完畢密碼不正確，等待我們的客服確認。
【完成】 ：交易完畢密碼正確，您所發數量已被確認。
【取消】 ：單子已被供貨商或臥虎遊戲取消

備註1 ：下單時必須提供準確發貨ID，否則單子出現問題將不被處理。
備註2 ：發貨後請填上【已發數量】，否則將延誤貨款支付。
備註3 ：【草單數量】與【已發數量】必須一致，不然封號時將不被處理。
備註4 ：單子在完成後的15分鐘，單子款項會自動支入供應商的【結存餘額】。供應商可以在15分鐘後，自行選擇提款支入先前已設好的銀行賬號。
');

// Buyback Order Cancellation notification
define('EMAIL_STAT_SUBJECT', implode(' ', array(EMAIL_LOCAL_EMAIL_SUBJECT_PREFIX, "Alert on China Buyback Order Cancellation")));
define('EMAIL_STAT_TITLE', 'Today\'s Statistics:');
define('EMAIL_STAT_FORMULAR', 'Total Canceled / Total Orders'."\n".'= %s / %s'."\n".'= %s%%');
define('EMAIL_STAT_CUSTOMER_NAME', 'China Buyback Customer: %s');
define('EMAIL_STAT_CUSTOMER_EMAIL', 'China Buyback Customer E-mail: %s');
define('EMAIL_STAT_DATE', 'Date: %s');
?>