<? 
define('HEADING_TITLE', '活動'); 
define('NAVBAR_TITLE', HEADING_TITLE); 
define('FORM_TITLE', '提交表格'); 

define('ENTRY_SUBMIT_PICTURE', '提交圖片:'); 
define('ENTRY_SERVER', '服務器:'); 
define('ENTRY_CHAR_NAME', '角色名稱:'); 
define('ENTRY_EMAIL', '電子郵件:'); 
define('ENTRY_OGM_ORDER_NUM', 'OffGamers訂單號碼:'); 
define('ENTRY_EVENT_ORDER_NO', '訂單號碼.'); 

define('TEXT_SS_SIZE_AND_FORMAT', '(只收高達%sKB的.jpg圖片)'); 
define('ERROR_CANNOT_LEFT_BLANK', '不能留空'); 
define('INVALID_ORDER_NUMBER_ERROR', '訂單號碼錯誤.'); 
define('CHARACTER_NAME_ERROR', '請填寫個角色名稱.'); 
define('CHAR_LEFT', '剩餘的字符.'); 

define('EVENT_REQUIRED_FIELD_ERROR', '必填字段不能留空.'); 
define('EVENT_CONTENT_ERROR', '請填滿文字字段.'); 

define('EVENT_CONTENT_LENGTH_ERROR', '信息長度太短。最少％s個字符.'); 
define('SERVER_ERROR', '請選擇一個服務器.'); 
define('EVENT_SS_EMPTY_ERROR', '請點擊尋找並上載圖片.'); 
define('EVENT_SS_EXTENSION_ERROR', '文件批量錯誤.'); 
define('EVENT_SS_ERROR', '圖片文件錯誤。請稍後再試.'); 
//define('EVENT_SS_SIZE_EXCEED_LIMIT_ERROR', '圖片超過％skb。請重新上載.'); 
define('EVENT_SS_SIZE_EXCEED_LIMIT_ERROR', '文件太大，請重新上載.'); 

define('EVENT_SS_SUBMITTED_ERROR', '此訂單之前已被提交。請再試另一個訂單號碼.'); 
define('ERROR_QUOTA', '提交失敗限量超過了，請稍等15分鐘，然後再試.'); 
                
define('EVENT_SUBMIT_SUCCESS', '謝謝您的合作'); 

define('TEXT_LOGIN_EVENT_MESSAGE', '祝你好運,盡情遊戲吧!'); 
//define('TEXT_LOGOFF_EVENT_MESSAGE', '請登錄您的offgamers帳戶，上載您的文件.並不需提供訂單號碼.'); 
                   
define('TEXT_LOGOFF_EVENT_MESSAGE', '<a href="%s">登陸</a>後立即提交!沒有OffGamers帳戶? <a href="%s">註冊</a>.'); 
define('TEXT_ARTICLE', '文章'); 
define('TEXT_SCREEN_SHOT', '截圖'); 

//define('CUSTOMER_NOTIFICATION_EVENT_SUBJECT', '文件上載通知'); 
define('CUSTOMER_NOTIFICATION_EVENT_SUBJECT', '提交成功'); 
//define('CUSTOMER_NOTIFICATION_EVENT_CONTENT', '***************************************** **********' . "\n" . '這是一個自發得的電子郵件-請別回复.' . "\n". '*********** ****************************************' . "\n" . "恭喜, \n\n" . "您的訂單號碼是%s, %s" . "\n" . '您已經成功上載您的%s' . "\n" . '更多的促銷和宣傳活動新聞，請觀覽<a href="http:\\www.offgamers.com">' . STORE_NAME . '</a>' . "\n" . '如果您需要任何求助，請聯絡<a href="mailto :' . EMAIL_TO . '">' . EMAIL_TO . '</a>' . "\n\n" . '感謝您參與' . "\n\n" . EMAIL_FOOTER); 
define('CUSTOMER_NOTIFICATION_EVENT_CONTENT', '******************************************* ********' . "\n" . '這是一個自發得的電子郵件-請別回复.' . "\n" . '************* **************************************' . "\n" . "恭喜,\n \n" . "你的[ %s ]". "\n" . '您已經成功上載您的%s' . "\n" . '更多的促銷和宣傳活動新聞，請訪問<a href= "http:\\www.offgamers.com">' . STORE_NAME . '</a>' . "\n" . '如果您需要任何求助，請聯絡<a href="mailto:' . EMAIL_TO . '" >' . EMAIL_TO . '</a>' . "\n\n" . '感謝您參與' . "\n\n" . '謹,' . "\n" . 'OffGamers -您的遊戲聯盟' . "\n" . '<a href="http:\\www.offgamers.com">' . STORE_NAME . '</a>'); 
define('ADMIN_EVENT_CONTENT', '訂單號碼: %s' . "\n" . '角色名稱: %s' . "\n" . '電子郵件: %s' . "\n" . '地址: ' . "\n" . '%s' . "\n" . '電話: %s' . "\n" . '付款方式: %s' . "\n" . '金額數目: %s' . " \n" . '訂單狀態: %s' . "\n" . '%s' . "\n\n"); 
define('ADMIN_SS_EMAIL_SUBJECT', '完美世界屏幕截圖競賽'); 
define('ADMIN_ARTICLE_EMAIL_SUBJECT', '卓越之劍\秘訣活動'); 
?>