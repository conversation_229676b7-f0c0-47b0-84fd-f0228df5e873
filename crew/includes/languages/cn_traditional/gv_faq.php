<?php 
/* 
   $Id: gv_faq.php,v 1.2 2004/09/23 05:36:09 stanley Exp $ 

   The Exchange Project - Community Made Shopping! 
   http://www.theexchangeproject.org 

   Gift Voucher System v1.0 
   Copyright (c) 2001,2002 <PERSON> 
   http://www.phesis.org 

   Released under the GNU General Public License 
*/ 

define('NAVBAR_TITLE', '禮券常見問題解答'); 
define('HEADING_TITLE', '禮券常見問題解答'); 

define('TEXT_INFORMATION', '<a name="Top"></a> 
   <a href="'.tep_href_link(FILENAME_GV_FAQ,'faq_item=1','NONSSL').'">購買禮券</a><br> 
   <a href="'.tep_href_link(FILENAME_GV_FAQ,'faq_item=2','NONSSL').'">如何發送禮券</a><br> 
   <a href="'.tep_href_link(FILENAME_GV_FAQ,'faq_item=3','NONSSL').'">使用禮券購買</a><br> 
   <a href="'.tep_href_link(FILENAME_GV_FAQ,'faq_item=4','NONSSL').'">兌換禮券</a><br> 
   <a href="'.tep_href_link(FILENAME_GV_FAQ,'faq_item=5','NONSSL').'">當出現問題</a><br> 
'); 
switch ($HTTP_GET_VARS['faq_item']) { 
   case '1': 
define('SUB_HEADING_TITLE','購買禮券.'); 
define('SUB_HEADING_TEXT','購買禮券就像在我們的商店購買其他產品一樣.您可以使用常用的付款方式結賬.一旦購買了禮券價格將被添加到您的個人禮券帳戶.如果您的禮卷賬戶有餘頜，您可以在購物車看到您的數額，您也可通過購物車利用電子郵件發送禮券給別人.'); 
   break; 
   case '2': 
define('SUB_HEADING_TITLE','如何發送禮券.'); 
define('SUB_HEADING_TEXT','您可通過購物車利用電子郵件發送禮券.在每一頁的右手欄，你可以找到本網購物車的鏈接','. 
                           當您在發送禮券，你必須供應以下的資料: 
收信人的姓名。 
                           收信人的電子郵件地址. 
                           您要發送的數量. (注意：您不必傳送的全部的數額.) 
                           一個短信息會出現. 
                           在發出之前，請確保您已輸入資料是正確的。在禮券被發送之前,您可以隨時隨地的改變以上所列的資料。 '); 
   break; 
   case '3': 
   define('SUB_HEADING_TITLE','使用禮券購買.'); 
   define('SUB_HEADING_TEXT','如果您的禮券帳戶有餘頜，您可以使用這些餘頜購買其他物品.在結帳階段，額外的框將會出現.打勾這個格子將會利用您的禮券帳戶餘頜付此訂單.請注:如果您沒有足夠的餘頜,就得選擇一中付款方式付此訂單.如果您的餘頜超過售價,訂單價值將自動被扣,剩餘的餘頜將會存在您的禮券帳戶.'); 
  
   break; 
   case '4': 
   define('SUB_HEADING_TITLE','兌換禮券.'); 
   define('SUB_HEADING_TEXT','如果您通過電子郵件收到禮券，您將可以看到發送人的資料,以及他人的信息.電子郵件也將包含禮券代碼.您可以把禮券代碼印出，以供日後參考。現在，您可以通過兩種方式兌換禮券: 
   <br> 1.點擊郵件裡的鏈接，將把您帶到商店的兌換禮券的網頁。請在本店註冊賬戶,以便您可以使用所收到的禮券. 
<br>2.在結帳過程中，在同一頁上您選擇的付款方式會有一個格子，輸入禮券代碼,然後按兌換.禮券價格將添加到您的禮券帳戶。然後，您可以從我們的商店使用餘頜購買任何物品'); 
   break; 
   case '5': 
   define('SUB_HEADING_TITLE','當出現問題.'); 
   define('SUB_HEADING_TEXT','任何礼券系統的疑問，請聯絡'. STORE_OWNER_EMAIL_ADDRESS . '.在電子郵件中,請供應詳細的資料. '); 
   break; 
   default: 
   define('SUB_HEADING_TITLE',''); 
   define('SUB_HEADING_TEXT','請從上述的問題選擇一項.'); 

   } 
?>