<?php 
/* 
   $Id: gv_send.php,v 1.3 2006/03/24 07:51:24 weichen Exp $ 

   The Exchange Project - Community Made Shopping! 
   http://www.theexchangeproject.org 

   Gift Voucher System v1.0 
   Copyright (c) 2001,2002 <PERSON> 
   http://www.phesis.org 

   Released under the GNU General Public License 
*/ 

define('HEADING_TITLE', '發送禮卷'); 
define('NAVBAR_TITLE', '發送禮卷'); 
define('EMAIL_SUBJECT', '詢問來自' . STORE_NAME); 
define('HEADING_TEXT','<br>請在下面輸入您要發送的禮券資料，如需更多詳情,請參閱我們的<a href="' . tep_href_link(FILENAME_GV_FAQ,'','NONSSL'). '">'.GV_FAQ.'.</a><br>'); 
define('ENTRY_NAME', '收信人姓名:'); 
define('ENTRY_EMAIL', '收信人的電郵地址:'); 
define('ENTRY_MESSAGE', '信息給收信人:'); 
define('ENTRY_AMOUNT', '禮券價格:'); 
define('ERROR_ENTRY_AMOUNT_CHECK', '&nbsp;&nbsp;<span class="errorText">禮券價格錯誤</span>'); 
define('ERROR_ENTRY_EMAIL_ADDRESS_CHECK', '&nbsp;&nbsp;<span class="errorText">電郵地址錯誤</span>'); 
define('MAIN_MESSAGE', '您決定發送價格%s的禮券給%s,電郵地址是%s<br><br>電郵的內容如下:<br><br>Dear %s<br><br> '); 

define('PERSONAL_MESSAGE', '%s說'); 
define('TEXT_SUCCESS', '恭喜您，您的禮券已發送成功'); 

define('EMAIL_SEPARATOR', '------------------------------------------- ---------------------------------------------'); 
define('EMAIL_GV_TEXT_HEADER', '恭喜您，您已收到價值的％s的禮券'); 
define('EMAIL_GV_TEXT_CODE', '您的禮券兌換代碼是%s'); 
define('EMAIL_GV_TEXT_SUBJECT', '%s送的禮物'); 
define('EMAIL_GV_FROM', '這禮券是%s發送給您的'); 
define('EMAIL_GV_MESSAGE', '信息:'); 
define('EMAIL_GV_SEND_TO', '您好, %s'); 
//define('EMAIL_GV_REDEEM', '兌換禮卷,請點擊以下的鏈接.如遇到任何問題,請記下您的兌換代碼%s.; 

define('EMAIL_GV_TEXT_TO_REDEEM', '請觀覽<a href="%s">%s</a>,在結賬過程中輸入您兌換代碼.'); 
define('EMAIL_GV_LINK', '兌換禮卷請按此'); 
define('EMAIL_GV_VISIT', '或觀覽'); 
define('EMAIL_GV_ENTER', '輸入禮卷代碼'); 
/* 
define('EMAIL_GV_FIXED_FOOTER', '如果利用上面的鏈接兌換禮券出現問題, ' . "\n" . 
                                 '您也可以通過本商店的結賬過程輸入禮卷代碼.' . "\n\n"); 
        
*/ 
define('EMAIL_GV_FIXED_FOOTER', '如有任何詢問或疑問，您可以使用我們的在線客服，或通過電子郵件' . EMAIL_TO . '聯絡我們.請記得提供您的禮卷兌換代碼，以便我們處理您的詢問.'); 
define('EMAIL_GV_SHOP_FOOTER', STORE_EMAIL_SIGNATURE); 
   

?>