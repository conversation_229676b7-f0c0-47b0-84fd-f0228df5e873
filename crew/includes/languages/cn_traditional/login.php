<?php 
/* 
   $Id: login.php,v 1.9 2008/06/10 05:33:45 edwin.wang Exp $ 

   osCommerce, Open Source E-Commerce Solutions 
   http://www.oscommerce.com 

   Copyright (c) 2003 osCommerce 

   Released under the GNU General Public License 
*/ 

define('NAVBAR_TITLE', '註冊帳號'); 
define('HEADING_TITLE', '<span style="color:#2982a9;">登錄</span></b>或創建一個' . STORE_NAME . '帳戶.');

define('TEXT_PASSWORD_FORGOTTEN', '忘記您的密碼?');
define('TEXT_RESET_PASSWORD', '重設密碼');
define('TEXT_ACTIVATE_ACCOUNT', '激活帳戶');
define('TEXT_SIGN_IN_TROUBLE', '現在就聯絡我們的<a href="%s">客服</a>');
define('TEXT_LOGIN_ERROR', '無法登陸，請稍後再嘗試或%s.');
define('TEXT_LOGIN_EMAIL_ACTIVATE_ERROR', '該帳戶未活躍.<br><a href="'. tep_href_link(FILENAME_ACCOUNT_ACTIVATE, 'aov=activate').'">'.TEXT_ACTIVATE_ACCOUNT.'</a> or <a href= "'.tep_href_link(FILENAME_ACCOUNT_ACTIVATE, 'action=resend&aov=activate').'">重新發送活躍戶口至電子郵件</a> %s');
define('TEXT_VISITORS_CART', '%s登陸以後，您的"遊客購物車"裡的內容將與您的"會員購物車"裡的內容合併。[<a href="javascript:session_win(\'' . tep_href_link(FILENAME_INFO_SHOPPING_CART) . '\');">更多詳情</a>]');
define('TEXT_RETURNING_CUSTOMER', '會員登入');
define('TEXT_NEW_CUSTOMER', '新客戶');
define('TEXT_CREATE_ACCOUNT_MSG', '如果您沒有offgamers會員帳號，請創建一個.');
define('TEXT_SIGN_IN_MSG', '如果你有offgamers的會員帳號，請登錄.');
define('TEXT_SEND_PASSWORD_MSG', '請輸入您的電子郵件.');
define('TEXT_SIGNUP_SUCCESS', '恭喜， %s! 您已經成功註冊為 ' . STORE_NAME . ' 會員. %s');
define('TEXT_WHAT_TODO', '下一步您想做什麼?');
define('TEXT_SIGNUP_SUCCESS_END', '...或者從網頁上面的菜單欄裡選擇您想購買的遊戲.');
define('TEXT_CHECKOUT_REDIRECT_MSG', '稍後您將轉到結賬頁面完成結賬過程.');
define('TEXT_SEE_ALL_OFFER', '查看我們出售的產品');
define('TEXT_SELL_US_CURRENCIES', '向我們出售您的金幣');
define('TEXT_CAPTCHA_ERROR', '錯誤，您輸入的驗證碼錯誤。請刷新新驗證碼或者重新輸入。');
define('TEXT_CAPTCHA_MISSING_ERROR', '錯誤，請輸入驗證碼。');
define('ERROR_INCOMPLETE_LOGIN_INFO','錯誤：郵箱地址不存在，請重試。');

define('TEXT_MAIN', '如果您忘記密碼，請在下面輸入您的電子郵件地址，我們將向您發送一封電子郵件載有您的新密碼.');
define('TEXT_NO_EMAIL_ADDRESS_FOUND', '錯誤：在我們的記錄中沒有發現電子郵件地址，請稍後再試.');
define('EMAIL_PASSWORD_REMINDER_SUBJECT', '密碼重置');
define('EMAIL_PASSWORD_REMINDER_BODY', '您的帳戶密碼已重置為以下。你現在可以登錄到' . STORE_NAME . '與您的電子郵件地址, %s,和新密碼.如有任何查詢或協助，請使用我們的在線生活支援服務，或通過電子郵件發送查詢' . EMAIL_TO . ".謝謝你購物" . STORE_NAME . ".\n\n" . '新密碼: %s' . "\n");
define('EMAIL_FOOTER', STORE_EMAIL_SIGNATURE);

define('SUCCESS_PASSWORD_SENT', '成功：新密碼已發送到您的電子郵件地址.');

define('EMAIL_WELCOME', '我們歡迎你' . STORE_NAME . ',您的一個一站式的網上商店為您所有的遊戲虛擬項目和電力的需求水準.我們將寄給所有電子郵件，包括密碼重置和秩序的更新，這個電子郵件地址.' . "\n\n"); 
define('EMAIL_INFO', "\n\n" . '與一帳戶' . STORE_NAME . ",您將會登錄到我們的商店:-"."\n\n");
define('EMAIL_LINK_INFO', '你可以點擊下面的鏈接，以激活您的帳戶.' . "\n\n");
define('EMAIL_LINK_ADDRESS_INFO', "\n\n" . '如果您無法點擊鏈接，複製並粘貼整個下面的網址進入該地址的窗口，您的網頁瀏覽器.' . "\n\n");
define('EMAIL_MANUAL_ACTIVATE_EMAIL', "\n" . '您也可以激活您的帳戶手動<a href="' . tep_href_link(FILENAME_ACCOUNT_ACTIVATE, 'aov=activate') . '">' . tep_href_link(FILENAME_ACCOUNT_ACTIVATE, 'aov =activate') . '</a>通過輸入以下信息<br><br>E-mail Address: ');
define('EMAIL_MANUAL_ACTIVATE_CODE', "<br>" . '激活代碼: '); 
define('EMAIL_TEXT', '-更新您的聯繫信息或更改您的帳戶密碼,'."\n".'-訂閱或退訂我們的電子報,'."\n".'-記載的您目前的秩序，歷史和回購的歷史,'."\n".'-自動保存的內容，您的購物車為稍後結帳,'."\n".'-收到特別的促銷和折扣告示(只適用於如果您是我們的訂閱電子報).'."\n\n"); 
define('EMAIL_CONTACT', '如有任何查詢或協助，請使用我們的在線生活支援服務，或通過電子郵件發送查詢' . EMAIL_TO . '.謝謝您購物' . STORE_NAME . ".\n\n\n "); 
define('EMAIL_WARNING', '注意：您收到此電子郵件，因為你已經簽署了一個帳戶' . STORE_NAME . "與這個電子郵件地址.如果您的電子郵件地址被用於未經您的同意，請E - mail " . '<a href="mailto:' . EMAIL_TO . '">'.EMAIL_TO.'</a>立即取消此帳戶.' . "我們想建議你改變您的貝寶帳戶密碼（如果你擁有一） ，因為騙徒通常註冊使用其中一種的電子郵件地址後，他們獲得了相應的貝寶帳戶（通過釣魚網站，已儲存的密碼檢索，鍵盤記錄程序，間諜軟件等）.當然，這也可能我們的客戶進入e - mail地址時打錯字.請理解我們的商店零容忍網際網路詐騙.這就是為什麼我們實施的電子郵件地址核實所有新帳戶註冊和沒有購買可以作出不首先核實電子郵件地址." . "\n"); 
define('EMAIL_FOOTER', STORE_EMAIL_SIGNATURE); 

define('EMAIL_GV_INCENTIVE_HEADER', '作為的一部分，我們歡迎新客戶，我們向您發送了一個電子禮券價值的％ s');
define('EMAIL_GV_REDEEM', '該贖回的代碼是％ s ，您可以輸入兌現代碼時，檢查後，作出了購買'); 
define('EMAIL_GV_LINK', '或走這條連結'); 
define('EMAIL_COUPON_INCENTIVE_HEADER', '祝賀，讓您第一次訪問我們的網上商店更有價值的經驗' . "\n" . 
                                         '下面是細節的折扣優惠券創造了只為你' . "\n\n");
define('EMAIL_COUPON_REDEEM', "使用優惠券輸入兌現代碼，這是%s期間結帳, \n當購買過後");

define('EMAIL_MAXMIND_SUBJECT', 'Maxmind GeoIP的錯誤');
define('EMAIL_MAXMIND_CONTENT', 'Maxmind GeoIP出現問題.');

// Mantis # 0000024 @ ************ - New label
define('HEADER_CREATE_ACCOUNT', '創建帳戶');

define('EZ_COUNTRY_CODE', '國家代碼:&nbsp;'); 
define('EZ_CONTACT_NUMBER', '手機號碼:&nbsp;'); 
define('EZ_CONTACT_NUMBER_TEXT', '*'); 
define('EZ_CONTACT_NUMBER_EG', '移動電話例如: (12) xxxxxxx &nbsp;'); 
define('EZ_SECRET_QUESTION', '帳戶保護問題:&nbsp;'); 
define('EZ_SECRET_QUESTION_TEXT', '*'); 
define('EZ_ANSWER', '答案:&nbsp;'); 
define('EZ_ANSWER_TEXT', '*'); 
define('EZ_OPTIONAL_INFORMATION_HEADER', '可選填資料&nbsp;'); 
define('EZ_STATISTICS_PURPOSE', '統計目的:&nbsp;'); 
define('EZ_INSTANT_MESSENGER', '即時通訊帳號&nbsp;(IM):'); 
define('EZ_CREDITCARD_BILLING_ADDRESS', '信用卡付款賬單地址:&nbsp;'); 
define('EZ_BILLING_ADDRESS1', '住址1:&nbsp;'); 
define('EZ_BILLING_ADDRESS2', '住址2:&nbsp;'); 

// Mantis # 0000024 @ ************ - Error message 
define('ENTRY_EMAIL_JS_ERROR', "電子郵箱地址不正確。 郵箱地址必須至少包含6個字符。"); 
define('ENTRY_FIRSTNAME_JS_ERROR', "您的名字必須至少包含%d個字符。"); 
define('ENTRY_LASTNAME_JS_ERROR', "您的姓氏必須至少包含%d個字符。"); 
define('ENTRY_PASSWORD_JS_ERROR', "您的密碼必須至少%d個字符。"); 
define('ENTRY_PASSWORD_CONFIRMATION_JS_ERROR', "密碼確認必須與新密碼相符。"); 
define('ENTRY_CONTACT_NUMBER_JS_ERROR', "您的聯繫電話必須至少包含3個字符。"); 
define('ENTRY_DOB_JS_ERROR', "您的生日日期不正確。"); 
define('ENTRY_SECRET_QUESTION_JS_ERROR', "您必須選擇任何一項帳戶保護問題."); 
define('ENTRY_ANSWER_JS_ERROR', "您的答案必須至少包含%d個字符。"); 
define('ENTRY_SIGNUP_TERMSANDCONDITION_JS_ERROR', "您必須同意我們的服務條款和隱私權政策"); // @ 200811061014 


// Mantis # 0000024 @ 200812081656 - Drop down notice 
define('ENTRY_EMAIL_NOTICE', "請使用正確的郵箱地址方便我們驗證您的帳戶。"); 
define('ENTRY_FIRSTNAME_NOTICE', "請輸入您證件上的名字。"); 
define('ENTRY_LASTNAME_NOTICE', "請輸入您證件上的姓氏。"); 
define('ENTRY_PASSWORD_NOTICE', "密碼至少輸入%d位字符,區分大小寫"); 
define('ENTRY_CONFIRM_PASSWORD_NOTICE', "請重新輸入您的密碼。"); 
define('ENTRY_CONTACTNUMBER_NOTICE', "請提供給我們一個有效的電話號碼，我們將打電話向您確認信息。如果您忘記了您的密碼保護問題，我們同樣可以打電話給您重新設置您的密保問題和答案。"); 
define('ENTRY_ANSWER_NOTICE', "為了確保您的賬戶安全，在改變，更新信息和撤銷資金的過程中我們需要您的密碼保護問題和答案來驗證。"); 
define('ENTRY_IM_NOTICE', "我們將通過您所提供的IM聯繫,在交貨的時候聯絡您。"); 
define('ENTRY_DOB_NOTICE', "為了法律原因。"); 

// Mantis # 0000024 @ 200809261143 - Error message 
define('ENTRY_PASSWORD_CONFIRMATION_ERROR', "您的密碼確認必須包含至少%d字符."); 
define('ENTRY_SECRET_QUESTION_ERROR', "您必須選擇任何一項帳戶保護問題."); 
define('ENTRY_ANSWER_ERROR', "您的答案必須包含至少%d字符."); 
define('ENTRY_CONTACT_NUMBER_ERROR', "您的聯繫電話必須包含至少%d字符.");
define('ENTRY_CONTACT_NUMBER_EXIST_ERROR', "您輸入的手機號碼已被另外的賬戶使用，請輸入其他的手機號碼。");
define('ENTRY_SIGNUP_TERMSANDCONDITION_ERROR', "您必須同意我們的服務條款和隱私權政策"); // @ 200811061014 

//Mantis # 0000068 @ 200906221044
define('ENTRY_REFERRAL_ID', '輸入轉介ID:');

// Mantis # 0000024 @ 200810311721 - New text statement in new layout screen 
define('TEXT_REASON_TO_JOIN_MEMBER', '您為什麼需要創建一個OffGamers成員?<br>您可以在這裡得到什麼好處?'); 
define('TEXT_SEND_PASSWORD', '請輸入您的郵箱地址.'); 
define('TEXT_FORGOTTEN_PASSWORD', '忘了密碼'); 


// Mantis # 0000024 @ 200811051724 - New text statement in latest layout screen 
define('TEXT_DO_YOU_AGREE', '您是否同意? '); 
define('TEXT_I_HAVE_READ_AND_AGREE', '我已經閱讀並同意'); 
define('TEXT_TERMS_AND_POLICY', '<a href="terms-of-service-i-5.ogm" target="terms">服務條款</a>, <a href="privacy-policy-i-4. ogm" target="terms">隱私權政策</a> & <a href="affiliate-policy-i-773.ogm" target="terms">聯盟政策</a>.'); 

// M#0000024 

// survey form 
define('JS_SURVEY_TITLE', '調查問卷'); 
define('JS_SURVEY_QUESTION', '您從哪裡認識我們'); 

define('JS_SURVEY_ANSWER_OTHERS', '其他'); 
define('JS_SURVEY_ANSWER_GAMEAXIS', 'GameAxis'); 
define('JS_SURVEY_ANSWER_GAMES', 'Games'); 
define('JS_SURVEY_ANSWER_PCGAMERS', 'PC Gamers'); 
define('JS_SURVEY_ANSWER_PCCOM', 'PC.COM'); 

// Express Login
define('HEADER_ACCOUNT_LOGIN', '帳戶登錄');

// New Layout 2011 - Login
define('FORGOT_PASSWORD_HEADER','<h3>忘記密碼？</h3>');
define('HAVING_TROUBLE_HEADER','<h3>無法註冊帳號？</h3>');
define('NOT_A_MEMBER_YET', '趕快加入OffGamers，免費註冊！');
define('BUTTON_REGISTER_NOW', '立即註冊');
define('NOT_MEMBER_YET_HEADER','<h3>還未註冊會員嗎？</h3>');

// New Layout 2011 - Register
define('REGISTER_ACCOUNT_HEADER','註冊賬戶');
define('BUTTON_JOIN_US_NOW','現在就加入我們！');
define('REGISTER_USING_FB','或註冊：');
define('RETURNING_CUSTOMER_HEADER', '<h3>我是老客戶，</h3>');
define('BUTTON_LOGIN_HERE','立即登錄');
define('WHY_OGM_HEADER','<h3>Why OffGamers?</h3>');
define('BUY_AND_DOWNLOAD_GAMES', 'Buy and download games');
define('JOIN_CONVERSATION', 'Join the conversation');
define('GET_SUPPORT', 'Get support for your games');
define('CHEAP_GAMES_NOT_ENOUGH', 'If cheap games aren\'t enough...');

//oauth login
define('TEXT_LOGIN_OR_SIGNUP', '使用OffGamers賬號登入遊戲或<a target="new" href="%s" id="register_account_link" onClick="_gaq.push([\'_trackEvent\', \'Acquisition\', \'Signup\', \'R3K\']);return targetopener(this,1);">現在註冊！</a>');
define('TEXT_OR_LOGIN_WITH', '或使用以下方式登入：');
define('ERROR_UNRECOGNISE_USER_OR_PASSWORD', 'Unrecognised email or password.');
define('TEXT_FORGOT_PASSWORD', '忘記密碼？');
define('TEXT_LOGIN_TNC', '通过点击注册或进入游戏，表明您已阅读并同意相关的<a href="http://www.offgamers.com/terms-of-service-i-5.ogm">服務條款</a>以及<a href="http://www.offgamers.com/privacy-policy-i-4.ogm">隱私條例</a>');
define('TEXT_LOGIN_GAME_HEADER', '登入遊戲');
define('TEXT_FORGOT_PASSWORD_HEADER', '忘記密碼？');
define('TEXT_ACCOUNT_LOGIN_HEADER', '會員登入');
?>