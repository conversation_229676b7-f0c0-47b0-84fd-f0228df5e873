<?
/*
  	$Id: alipay.php,v 1.2 2009/02/09 05:29:30 boonhock Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	DevosC, Developing open source Code
  	http://www.devosc.com
	
  	Copyright (c) 2002 osCommerce
  	Copyright (c) 2004 DevosC.com
	
  	Released under the GNU General Public License
*/

define('MODULE_PAYMENT_ALIPAY_TEXT_TITLE', 'Alipay'); //Do not change!
define('MODULE_PAYMENT_ALIPAY_TEXT_CART', '進入' . MODULE_PAYMENT_ALIPAY_TEXT_TITLE);
define('MODULE_PAYMENT_ALIPAY_TEXT_DISPLAY_TITLE', MODULE_PAYMENT_ALIPAY_TEXT_TITLE);

define('MODULE_PAYMENT_ALIPAY_TEXT_DESCRIPTION', '此頁面主要為確認'.MODULE_PAYMENT_ALIPAY_TEXT_TITLE.'資料');

define('MODULE_PAYMENT_ALIPAY_TEXT_TITLE_PROCESSING', '款項處理中');
define('MODULE_PAYMENT_ALIPAY_TEXT_DESCRIPTION_PROCESSING', '如果此頁停留超過5秒鐘，請您點擊貝寶登出以便完成您的單子.');
define('MODULE_PAYMENT_ALIPAY_TEXT_ERROR_MESSAGE', '您的'.MODULE_PAYMENT_ALIPAY_TEXT_TITLE.'付款失败，请重试或选择其它付款方式进行付款!');
?>