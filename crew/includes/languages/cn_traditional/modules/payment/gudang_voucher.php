<?php
/*
  	$Id: gudang_voucher.php,v 1.1 2012/05/15 08:28:38 sionghuat.chng Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2002 osCommerce
	
  	Released under the GNU General Public License
*/

define('MODULE_PAYMENT_GUDANG_VOUCHER_TEXT_TITLE', 'Gudang Voucher');
define('MODULE_PAYMENT_GUDANG_VOUCHER_TEXT_CART', '進入Gudang Voucher');
define('MODULE_PAYMENT_GUDANG_VOUCHER_TEXT_DISPLAY_TITLE', 'Gudang Voucher');	// for title displaying purpose if different from MODULE_PAYMENT_GUDANG_VOUCHER_TEXT_TITLE (Gudang Voucher)
define('MODULE_PAYMENT_GUDANG_VOUCHER_TEXT_DESCRIPTION', 'Gudang Voucher');
//define('MODULE_PAYMENT_GUDANG_VOUCHER_TEXT_CONFIRMATION', MODULE_PAYMENT_GUDANG_VOUCHER_MESSAGE);
//define('MODULE_PAYMENT_GUDANG_VOUCHER_TEXT_EMAIL_FOOTER', str_replace(array("<br>", "<BR>", "\r\n"), array("\n", "\n", ''), MODULE_PAYMENT_GUDANG_VOUCHER_EMAIL_MESSAGE));
define('MODULE_PAYMENT_GUDANG_VOUCHER_TEXT_PAYMENT_METHOD_OPTIONS', '付款方式: ');
define('MODULE_PAYMENT_GUDANG_VOUCHER_TEXT_JS_PM', '請選擇您的付款方式為'.MODULE_PAYMENT_GUDANG_VOUCHER_TEXT_DISPLAY_TITLE.'.');
define('MODULE_PAYMENT_GUDANG_VOUCHER_TEXT_ERROR', '款項錯誤!');
define('MODULE_PAYMENT_GUDANG_VOUCHER_ERROR_PM', '錯誤:請選擇您的付款方式為'.MODULE_PAYMENT_GUDANG_VOUCHER_TEXT_DISPLAY_TITLE.'.');
define('MODULE_PAYMENT_GUDANG_VOUCHER_TEXT_ERROR_MESSAGE', '您的Gudang Voucher付款失敗，請重試或選擇其它付款方式進行付款!');
?>