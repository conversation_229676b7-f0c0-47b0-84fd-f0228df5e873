<?php 
/* 
   $Id: moneybookers.php,v 1.8 2008/06/10 05:34:53 edwin.wang Exp $ 

   osCommerce, Open Source E-Commerce Solutions 
   http://www.oscommerce.com 

   Copyright (c) 2002 osCommerce 

   Released under the GNU General Public License 
*/ 

define('MODULE_PAYMENT_MONEYBOOKERS_TEXT_TITLE', 'Moneybookers.com'); 
define('MODULE_PAYMENT_MONEYBOOKERS_TEXT_CART', '進入Moneybookers.com'); 
//define('MODULE_PAYMENT_MONEYBOOKERS_TEXT_DISPLAY_TITLE', 'Moneybookers.com (臥虎遊戲\'推薦的付款方式)'); // for title displaying purpose if different from MODULE_PAYMENT_MONEYBOOKERS_TEXT_TITLE (Mo 
define('MODULE_PAYMENT_MONEYBOOKERS_TEXT_DISPLAY_TITLE', 'Moneybookers.com'); // for title displaying purpose if different from MODULE_PAYMENT_MONEYBOOKERS_TEXT_TITLE (Moneybookers.com) 
define('MODULE_PAYMENT_MONEYBOOKERS_TEXT_DESCRIPTION', 'Moneybookers.com'); 
define('MODULE_PAYMENT_MONEYBOOKERS_TEXT_CONFIRMATION', MODULE_PAYMENT_MONEYBOOKERS_MESSAGE); 
define('MODULE_PAYMENT_MONEYBOOKERS_TEXT_IMPORTANT_NOTICE', '如果這是您第一次在我們的網站通過Moneybookers付賬,此方式必須通過Moneybookers的1次審核(每一個MB賬號).此審核過程於週日以不超過24小時或於週末以不超過48小時完成.'); 
define('MODULE_PAYMENT_MONEYBOOKERS_TEXT_EMAIL_FOOTER', str_replace(array("<br>", "<BR>", "\r\n"), array("\n", "\n", ''), MODULE_PAYMENT_MONEYBOOKERS_EMAIL_MESSAGE)); 

define('MODULE_PAYMENT_MONEYBOOKERS_NOCURRENCY_ERROR', '安裝貨幣不被moneybookers.com接納!'); 
define('MODULE_PAYMENT_MONEYBOOKERS_ERRORTEXT1', '款項錯誤='); 
define('MODULE_PAYMENT_MONEYBOOKERS_ERRORTEXT2', '&error=您通過moneybookers.com的款項已經失敗.請再重試一遍,或選擇其他的付款方式!'); 
define('MODULE_PAYMENT_MONEYBOOKERS_ORDER_TEXT', '單子日期: '); 
define('MODULE_PAYMENT_MONEYBOOKERS_TEXT_ERROR', '款項錯誤!'); 
define('MODULE_PAYMENT_MONEYBOOKERS_TRANSACTION_FAILED_TEXT', '您的'.MODULE_PAYMENT_MONEYBOOKERS_TEXT_TITLE.'付款失敗，請重試或選擇其它付款方式進行付款!');
define('MODULE_PAYMENT_MONEYBOOKERS_ORDER_COMMENT1', ''); 
define('MODULE_PAYMENT_MONEYBOOKERS_ORDER_COMMENT2', '當您聯繫我們在線客服，請使用'.STORE_NAME.'您的單子號以詢問更多詳情.這樣能促使我們更快以及更有效地協助您.您也能夠在Moneybookers. com > My Account > history的"Reference"一欄中看見您的單子號.謝謝.'); 

// Moneybookers Account Verification E-mail
define('TEXT_MB_EMAIL_VERIFY_INSTRUCTION_CONTENT', "\n\n\n" . 'Please kindly verify this e-mail account by clicking on the following link or copy and paste the link to your browser.  This link will confirm that you are the owner of this e-mail address and we will not ask for any Moneybookers information in the page.  Please understand that this step is taken to protect Moneybookers account owners from unauthorized charges and this is a one-time process unless you change your Moneybookers e-mail.' . "\n\n");
define('TEXT_MB_PAYMENT_MAKE_EMAIL_CONTENT1', 'You have made a Moneybookers payment of ');
define('TEXT_MB_PAYMENT_MAKE_EMAIL_CONTENT2', ' to OffGamers using the following account:' . "\n");
define('TEXT_MB_EMAIL_VERIFY_ENDING_CONTENT', "\n\n\n" . 'If you have not made any purchase at OffGamers, please report this fraudulent <NAME_EMAIL> immediately and we also advise you to report the case to Moneybookers at their website and change your Moneybookers password immediately.  Thank you for shopping at ' . STORE_NAME . '.');
define('TEXT_MB_VERIFICATION_TITLE', 'Moneybookers Account Verification');
define('TEXT_NOTICE_OF_MB_VERIFICATION_SEND_TITLE', 'Moneybookers Account Verification Notice');
define('TEXT_MB_EMAIL_VERIFICATION_NOTICE_HEADING_CONTENT', "\n\n" . 'In order to protect the owner of the Moneybookers account from fraudulent use, a verification e-mail with "Subject: ');
define('TEXT_MB_EMAIL_VERIFICATION_NOTICE_ENDING_CONTENT', '" has been sent to the e-mail address shown above.  Please kindly check the e-mail account and follow the instructions stated to verify your Moneybookers e-mail account.  Thank you for helping us to serve you better.');
define('EMAIL_FOOTER', STORE_EMAIL_SIGNATURE);
define('EMAIL_CONTACT', 'For any enquiries or assistance, please use our Online Live Support service or e-mail your enquiries to ' . EMAIL_TO . '. Thank you for shopping at ' . STORE_NAME . ".\n\n\n");
define('REMARK_MB_VERIFICATION_EMAIL_SENT', 'Moneybookers verification e-mail sent.');
define('REMARK_MB_VERIFICATION_EMAIL_NOTICE_SENT', 'Moneybookers verification e-mail notice sent.');
?>