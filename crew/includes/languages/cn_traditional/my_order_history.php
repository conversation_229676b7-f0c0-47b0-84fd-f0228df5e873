<? 
/* 
   $Id: my_order_history.php,v 1.15 2008/12/10 02:06:22 keepeng.foong Exp $ 

   osCommerce, Open Source E-Commerce Solutions 
   http://www.oscommerce.com 

   Copyright (c) 2003 osCommerce 

   Released under the GNU General Public License 
*/ 

define('HEADER_ORDER_REPORT', '我的訂單詳情'); 
define('HEADER_TITLE_MY_ORDER_HISTORY', '單子狀態'); 

define('HEADING_TITLE', HEADER_TITLE_MY_ORDER_HISTORY); 
define('NAVBAR_TITLE', HEADER_TITLE_MY_ORDER_HISTORY); 

define('TEXT_ALL_GAMES', '*所有遊戲*'); 
define('TEXT_ALL_ORDER_STATUS', '*所有單子狀態*'); 

define('TEXT_TIME_LEFT', '剩餘時間(分鐘)'); 
define('TEXT_ORDERS_FOUND', '搜索的紀錄記錄'); 
define('TEXT_RESULT_SEARCH', '搜索結果 %s'); 


define('TEXT_PRODUCT_TYPE', '產品類型'); 
define('TABLE_HEADING_RECEIVER_CHAR_NAME', '接貨ID'); 
define('TEXT_REQUEST_QTY', '草單數量'); 
define('TEXT_SENT_QTY', '已發數量'); 

define('TEXT_EXPIRES', '單子取消時間:'); 
define('TEXT_MINUTES', '分鐘'); 

define('TEXT_PENDING_DELIVERY', '等待發送'); 
define('TEXT_PENDING_PAYMENT', '等待結款'); 
define('TEXT_RECEIVED', '已被接收'); 

define('TEXT_PENDING', '草單'); 
define('TEXT_PROCESSING', '結單'); 
define('TEXT_COMPLETED', '已被確認結單'); 
define('TEXT_CANCELLED', '已被刪除'); 

define('TEXT_GAME_CURRENCY', '遊戲金幣'); 
define('TEXT_HIGH_LEVEL_ACCOUNT', '遊戲帳號');

define('TEXT_ERROR_MSG', '錯誤信息'); 
define('TEXT_ERROR_TRYAGAIN', '發生錯誤!請重試。'); 
define('TEXT_ERROR_INVALID_QTY', '數額錯誤。請重試。'); 
define('TEXT_ERROR_NO_SS_AFTER_UPLOADED', '沒發貨後截圖。請重試。'); 
define('TEXT_ERROR_NO_SS_BEFORE_UPLOADED', '沒發貨前截圖。請重試。'); 
define('TEXT_SECONDS_COUNTDOWN', '(%s秒後刷新)'); 
  
define('ERROR_UPLOAD_FAILED', '上載截圖失敗，請稍後再重試。'); 
  
define('TEXT_ADDITIONAL_COMMENTS', '另外的評論'); 
define('TEXT_CHARACTER_NAME', '發送者ID'); 
define('TEXT_CHARACTER_NAME_IN_PREPARATION', '<span class="redIndicator">正在準備中。。。</span>'); 
  
define('TEXT_CHARACTER_NAME_IN_PREPARATION_QUEUE', '<span class="redIndicator">請稍等,還有%s位就到您</span>'); 
define('TEXT_CHARACTER_NAME_HELP', '請給我們提供您的ID,以便我們安排接收。'); 
define('TEXT_CONTACT_NAME', '姓名'); 
define('TEXT_CONTACT_NO', '聯絡號碼'); 
define('TEXT_DELIVERY_TIME', '發送時間'); 
define('TEXT_DELIVERY_TIME_HELP', '提醒:超出的單子將被刪除'); 
define('TEXT_FILE_SIZE', '(體積: %d KB)'); 
define('TEXT_OF', 'of'); 
define('TEXT_ORDER_REFERENCE', '單子參考'); 
define('TEXT_SCREENSHOT_BEFORE_SEND_GOLD','發貨前截圖'); 
define('TEXT_SCREENSHOT_AFTER_SENT_GOLD','發貨後截圖'); 
define('TEXT_TOTAL_PRICE', '總數'); 
define('TEXT_UPLOAD_SS', '上載截圖'); 
define('TEXT_PROCEED', '請按照規定在遊戲中轉移地點，我們將在五分鐘之內聯絡您的遊戲角色。如果我們在2分鐘之內無法聯絡您的遊戲角色，訂單將被取消，你將不得不與我們設立一個新的訂單.'); 

define('TEXT_USER_REQUIREMENT', '請上傳交易截圖兩張備查.要求如下：'); 
define('TEXT_FIRST_SS_TITLE', '1.交易前截圖:'); 
define('TEXT_FIRST_SS_DESC', '交易時的截圖需顯示雙方交易界面,雙綠單綠均可,同時界面要顯示交易金幣數量,交易時間,包裹金幣數量,交易雙方處於正常交易狀態（而非一方顯示繁忙）'); 
define('TEXT_SECOND_SS_TITLE', '2.交易後截圖:'); 
define('TEXT_SECOND_SS_DESC', '交易後截圖界面要顯示"Trade complete",交易時間、包裹剩餘金幣等'); 
define('TEXT_SS_REF_TITLE', '3.截圖可參照範例:'); 
define('TEXT_SS_REF_DESC', '<a href="http://kb.offgamers.com/zhcn/?p=576">http://kb.offgamers.com/zhcn/?p=576</a>'); 
define('TEXT_REQUIREMENT_TITLE', '4.截圖要求:'); 
define('TEXT_REQUIREMENT_DESC', 'jpg格式,每張大小不得超過800kb.'); 
define('TEXT_MORE_THAN_2_PHOTO', '5.凡是交易超過2張截圖的交易，請將剩餘的截圖經QQ， MSN或電郵************************轉送給我們'); 

define('TEXT_HELP_ORDER_STATUS_SELECT', '審核：在【單子狀態】等待接貨ID出現，然後進入遊戲交易（當面交易方式/遊戲內郵寄方式）後，必須確認填上【已發數量】，否則款項將不被支入【結存餘額】。<br><br>處理：等待臥虎遊戲員工確認確實已發數量，少或多於【草單數量】的單子，將會被視為免費金幣單子。<br ><br>完成：單子在完成後的15分鐘，單子款項會自動支入供應商的【結存餘額】。供應商可以在15分鐘後，自行選擇提款支入先前已設好的銀行賬號。 <br><br>取消：單子已被供應商或臥虎遊戲取消。'); 

define('SUCCESS_ORDER_HISTORY_ORDER_CANCEL_SUCCESS', '成功:單子已經成功取消。'); 
define('ERROR_ORDER_HISTORY_ORDER_CANCEL_FAILED', '錯誤:單子取消不成功。'); 

define('JS_CONFIRM_CANCEL_ORDER', '確定取消這單子？'); 
define('JS_NO_SS_BEFORE_TRADE', '請上載發貨前截圖'); 
define('JS_NO_SS_AFTER_TRADED', '請上載發貨後截圖'); 
define('JS_INVALID_SS_FILE_EXT', '上載截圖錯誤,只允許*.jpg, *.jpeg文檔名'); 

define('LINK_SEND_STOCK_FAQ', '[發貨必知常識]'); 

define('EMAIL_TEXT_CLOSING', '謝謝支持'.STORE_NAME.'.'); // unique 
define('ENTRY_RECEIVER_CHAR_NAME', '接貨ID'); 

define('TABLE_HEADING_RECEIVER_CHAR_NAME', '遊戲角色名稱'); 
define('TABLE_HEADING_DELIVERY_METHOD', '交易類型');
define('TABLE_HEADING_ORDER_NUMBER', '訂單編號.'); 
define('TABLE_HEADING_GAME', '遊戲'); 
define('TABLE_HEADING_SERVER', '服務器'); 
define('TABLE_HEADING_REQUEST_QTY', '提交數量'); 
define('TABLE_HEADING_SENT_QTY', '已發數量'); 
define('TABLE_HEADING_AMOUNT', '金額'); 
define('TABLE_HEADING_STATUS', '狀態'); 
define('TABLE_HEADING_RECEIVER_CHAR', '正在接收角色'); 
define('TABLE_HEADING_DELIVERED_ID', '發貨ID');
define('TABLE_HEADING_CONFIRM_QUANTITY', '已發數量');

define('ENTRY_RECEIVER_CHAR_NAME', '正在接受角色帳號'); 
define('ENTRY_ORDER_STATUS', '訂單狀態'); 
define('ENTRY_ORDER_NUMBER', '訂單#'); 
define('ENTRY_PRODUCT_TYPE', '產品類型'); 
define('ENTRY_GAME', '遊戲'); 
define('ENTRY_START_DATE', '開始日期'); 
define('ENTRY_END_DATE', '結束日期'); 

define('ORDERS_LOG_UNLOCK_OWN_ORDER', '##ul_1##');

define('POPUP_HEADER_BUYBACK_ORDER_CANCEL', '取消單子');
define('POPUP_TEXT_DETAILS', '詳情 (如有)');
define('POPUP_LIMIT_CHARACTERS', '(%s 字符)');
define('POPUP_LIMIT_CHARACTERS_ERROR', '文字字符串過長');
?>