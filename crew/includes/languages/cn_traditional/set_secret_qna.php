<?php 

// Mantis # 0000024 @ 200810241055 - Add popup message & form's text fields 
define('HEADER_TITLE_SECRET_QUESTION', '註冊機密問題還有答案'); 
define('HEADER_TITLE_CHANGE_SECRET_QNA', '更改密保問題及答案'); 

define('SET_SECRET_QUESTION', '機密問題:&nbsp;'); 
define('SET_SECRET_QUESTION_TEXT', '*'); 
define('SET_ANSWER', '答案:&nbsp;'); 
define('SET_ANSWER_TEXT', '*'); 
define('ENTRY_SECRET_QUESTION_ERROR', '請從列表選擇一項機密問題'); 
define('ENTRY_ANSWER_ERROR', "您的答案必須包含至少%d個字符."); 

define('TEXT_REGISTER_SECRET_QUESTION_AND_ANSWER','請註冊您的<a href='. tep_href_link(FILENAME_SET_SECRET_QNA) .'>機密問題還有答案</a>來更改你的信息'); 

// Show done message @ 200810311207 
define('ENTRY_UPDATE_DONE', '您的機密問答成功被更新了.'); 
define('ENTRY_SAVE_DONE', '您的機密問答成功被保存了.'); 

define('HIDDEN_PBLINK', '<input type="hidden" id="hid_value" name="hid_value">'); 
define('POPUP_TEXT_CHANGE_TO_SECRET_QUESTION_AND_ANSWER_NOTICE', tep_db_input('<div id="popupTitle"><p>機密問答保安系統建設同知</p></div><div id="popupMesgTop"><p>親愛的貴賓客戶,</p></div><p id="popupMesg">為了提高您戶口的安全,PIN代碼系統將被我們最新推介的機密問答保安系統代替,讓您更方便,更放心的使用本商店的網絡與服務.您可在戶口管理頁內建設機密問答保安,代替您現在使用的PIN代碼.<br><br>清在<span class="redIndicator">15天之內</span>建設您的機密問答.如果建設在<span class="redIndicator">15天內</span>沒被完成,我們的系統將自動凍結您的戶口.更多關於機密問答保安系統的詳情,請參閱我們的資料中心或聯絡我們的客服.為保障您的帳號安全，謝謝您的合作.<br></p><div id="popupMesgBottom"><p>OffGamers隊</p></div><div id="popupBottom"><p><a href="%s" class="subModelLink" onclick="document.getElementById(\'hid_value\').value=\''.stripslashes($_SESSION['ssqna' ]='popuplink').'\'">建設機密問答保安</a><br><a href="javascript:;" %s class="subModelLink">稍後再建設</a><br >剩下的時間: %d天</p></div>')); 
define('POPUP_TEXT_CHANGE_TO_SECRET_QUESTION_AND_ANSWER_TITLE', '機密問答保安系統建設同知'); 
//## 

// Mantis # 0000024 @ 200810271754 - Add form's text fields 
  
define('TEXT_PIN_NUMBER', 'PIN代碼'); 
define('TEXT_HELP_PIN_NUMBER', '請提供您的PIN代碼'); 
define('TEXT_PINNUMBER_REQUIRED', '請提供PIN代碼以便更新此字段: %s'); 
define('TEXT_IS_MANDATORY', '顯著<span class="redIndicator">*</span>的字段必定要填'); 
define('TEXT_SAVE', '保存'); 
define('TEXT_MUST_LOGIN', '進入此部分前,請登陸您的賬戶.'); 

define('BUTTON_SAVE', '保存'); 
define('BUTTON_REQUEST_TOKEN', '獲取驗證碼');
define('TEXT_SECRET_QNA_ISSET', '為了安全起見，您的密保問題及密碼不能在此網頁更改。請聯繫<a href="'.tep_href_link(FILENAME_CUSTOMER_SUPPORT, '', 'SSL').'">客服代表</a>請求更改。'); 

define('ENTRY_SECRET_ANSWER_QUESTION_NOTICE', '您的密保問答是保護您賬戶安全的保護手段。當您在提取貨款或修改OffGamers賬戶內的信息時，您都需填寫密保問答。');
define('ENTRY_SECRET_ANSWER_NOTICE', '請保護好您的密保問答，切勿告知他人。');
define('ENTRY_CONTACT_NUMBER_EXIST_ERROR', "您提供的手機號碼已使用或不存在。請提供另一個手機號碼。");
define('TEXT_CHANGE_PHONE_NUMBER', '(不是您的手機號碼? <a id="link_edit_phone_number" href="' . tep_href_link(FILENAME_ACCOUNT_EDIT) . '">請點擊修改</a>.)');

define('EMAIL_USER_RESET_PIN_SUBJECT', implode(' ', array(EMAIL_SUBJECT_PREFIX, '您的安全碼已重設')));
define('EMAIL_USER_RESET_PIN_BODY', '您的新安全碼是%s。這安全碼會在您收到這郵件的24小時過期。');

define('TEXT_SECRET_QNA', '驗證碼');
define('TEXT_DORMANT_ACCOUNT_QNA_REQUEST', '超過90天沒有登錄帳戶，需要使用安全標識重新激活帳戶才可以購物。<br><br>索取的驗證碼將在10分鐘內失效。若欲索取新的驗證碼每天只限不超過3次。');
define('TEXT_FORGOT_QNA', '無法驗證或索取安全碼？請 <a href="' . tep_href_link(FILENAME_CUSTOMER_SUPPORT) . '">聯繫</a>我們求助。');
define('ENTRY_QNA_MISMATCH_ANSWER_ERROR', '不好意思，驗證碼錯誤，請重試。');

define('TEXT_REQUEST_TOKEN_MSG', '您的驗證碼將在10分鐘後失效。同一天內，申請驗證碼不應超過3次。');
define('TEXT_REQUEST_TOKEN_SUCCESS_MSG', '驗證碼已通過短信息發送至您註冊的手機號碼%s。');
define('TEXT_REQUEST_TOKEN_REUSE_MSG', '請輸入當前的驗證碼。');
define('TEXT_REQUEST_TOKEN_FAIL_MSG', '您的驗證碼使用次數已超過最大限制，請24小時後重試。');
define('TEXT_REQUEST_TOKEN_HELP_MSG', '驗證碼已發送至您的手機。如果您提供的手機號碼無效或已丟失，請聯繫我們的<a href="%s" class="whiteText">客服</a>尋求幫助。');
?>