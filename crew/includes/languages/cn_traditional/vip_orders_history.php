<?php 

define('HEADING_TITLE', HEADER_TITLE_MY_VIP_ORDERS_HISTORY);
define('NAVBAR_TITLE', HEADER_TITLE_MY_VIP_ORDERS_HISTORY);

define('HEADER_ORDER_REPORT', '訂單報告');

define('TABLE_HEADING_DELIVERY_METHOD', '交易類型');
define('TABLE_HEADING_CONFIRM_QUANTITY', '已發數量');
define('TABLE_HEADING_ORDER_QUANTITY', '訂單數量');
define('TABLE_HEADING_ORDER_AWAITING_ACCEPT_QUANTITY', '分配數額');
define('TABLE_HEADING_RECEIVER_CHAR_NAME', '接貨ID');
define('TABLE_HEADING_SENDERS_CHARACTER_ID', '發貨ID');
define('TABLE_HEADING_SERVER_NAME', '服務器');
define('TABLE_HEADING_SUBMIT_QUANTITY', '提交數量');
define('TABLE_HEADING_PRICE_PER_UNIT', '單價');

define('BUTTON_TRADE_WITH_US', '與網站交易');
define('BUTTON_TRADE_CUSTOMERS', '與客戶交易');
define('BUTTON_TRADE_CANCEL', '取消');
define('ENTRY_RECEIVER_CHAR_NAME', '接貨ID');

define('TEXT_ALL_GAMES', '*所有遊戲*');
define('TEXT_ALL_ORDER_STATUS', '*所有單子狀態*');


define('TEXT_ADDITIONAL_COMMENTS', '另外的評論');
define('TEXT_CHARACTER_NAME_IN_PREPARATION', '<span class="redIndicator">準備中 ...</span>');
define('TEXT_CHARACTER_NAME_IN_PREPARATION_QUEUE', '<span class="redIndicator">請稍等,還有%s位就到您</span>');

define('TEXT_CHARACTER_NAME', '發送者ID');
define('TEXT_COUNTDOWN', '倒數:');

define('TEXT_CONTACT_NAME', '姓名');
define('TEXT_CONTACT_NO', '聯絡號碼');
define('TEXT_DELIVERY_TIME', '發送時間');
define('TEXT_RESULT_SEARCH', '搜索結果 %s'); 
define('TEXT_EXPIRES', '有效期至:');


define('TEXT_ERROR_MSG', '錯誤信息');
define('TEXT_ERROR_INVALID_QTY', '數額錯誤。請重試。');
//define('TEXT_ERROR_INVALID_CUSTOMERS_CODE', '收貨口令錯誤。請重試。');
define('TEXT_ERROR_NO_SS_AFTER_UPLOADED', '沒發貨後截圖。請重試。');
define('TEXT_ERROR_NO_SS_BEFORE_UPLOADED', '沒發貨前截圖。請重試。');
define('TEXT_ERROR_SS_AFTER_SIZE_EXCEED_LIMIT', '發貨後截圖超過%sKB。請重試。');
define('TEXT_ERROR_SS_BEFORE_SIZE_EXCEED_LIMIT', '發貨前截圖超過%sKB。請重試。');
define('TEXT_ERROR_SS_AFTER_EXTENSION_WRONG', '發貨後截圖文檔錯誤。請重試。');
define('TEXT_ERROR_SS_BEFORE_EXTENSION_WRONG', '發貨前截圖文檔錯誤。請重試。');
define('TEXT_ERROR_GET_DATA_FAILED', '尋找資料失敗');
define('TEXT_FILE_SIZE', '(體積: %d KB)');
define('TEXT_GAME_CURRENCY', '遊戲金幣');

define('TEXT_MATCH_BACKORDER_EXACT_AMOUNT', '（若出貨高於%d，只可直接提交%d，%d-%d之間本站不予接受）');

define('TEXT_MATCH_BACKORDER_AMOUNT_ONLY', '(只可直接提交%d)');
define('TEXT_MINUTES', '分');


define('TEXT_NO_RECORD_FOUND', '沒相關資料');
define('TEXT_OF', ' X ');
define('TEXT_ORDERS_FOUND', '搜索的紀錄記錄');
define('TEXT_ORDER_REFERENCE', '單子參考');
define('TEXT_PRODUCT_TYPE', '產品類型');


define('TEXT_QUANTITY_INVALID', "你輸入不對的數量，請輸入最高和最低的之間的額量");

define('TEXT_QUANTITY_MAX_NOT_MATCH', "你輸入不對的數量，請輸入和分配數額一樣的數量");
define('TEXT_SCREENSHOT_BEFORE_SEND_GOLD','發貨前截圖');
define('TEXT_SCREENSHOT_AFTER_SENT_GOLD','發貨後截圖');


define('TEXT_TRADE_WITH_US_PRICE', '與網站交易價格：');
define('TEXT_TRADE_CUSTOMERS_PRICE', '與客戶交易價格：');
define('TEXT_TOTAL_PRICE', '總數');
define('TEXT_SECONDS', '秒');
define('TEXT_SECONDS_COUNTDOWN', '(%s秒後刷新)');
define('TEXT_VIP_AWAITING_ORDERS_LIST', 'VIP訂單分配');
define('TEXT_VIP_ORDERS_HISTORY', 'VIP單子狀態');


define('TEXT_WAIT_FOR_REFRESH', '正在刷新，請稍等');

define('TEXT_UPLOAD_SS', '上載截圖');

define('TEXT_USER_REQUIREMENT', '請上傳交易截圖兩張備查.要求如下：');
define('TEXT_FIRST_SS_TITLE', '1.交易前截圖:');
define('TEXT_FIRST_SS_DESC', '交易時的截圖需顯示雙方交易界面,雙綠單綠均可,同時界面要顯示交易金幣數量,交易時間,包裹金幣數量,交易雙方處於正常交易狀態（而非一方顯示繁忙）');
define('TEXT_SECOND_SS_TITLE', '2.交易後截圖:');
define('TEXT_SECOND_SS_DESC', '交易後截圖界面要顯示"Trade complete",交易時間、包裹剩餘金幣等');
define('TEXT_SS_REF_TITLE', '3.截圖可參照範例:');
define('TEXT_SS_REF_DESC', '<a href="http://kb.offgamers.com/zhcn/?p=576">http://kb.offgamers.com/zhcn/?p=576</a>');
define('TEXT_REQUIREMENT_TITLE', '4.截圖要求:');
define('TEXT_REQUIREMENT_DESC', 'jpg格式,每張大小不得超過800kb.');
define('TEXT_MORE_THAN_2_PHOTO', '5.凡是交易超過2張截圖的交易，請將剩餘的截圖經QQ， MSN或電郵************************轉送給我們');

define('SUCCESS_ORDER_HISTORY_ORDER_CANCEL_SUCCESS', '成功:單子已經成功取消。');
define('ERROR_ORDER_HISTORY_ORDER_CANCEL_FAILED', '錯誤:單子取消不成功。');
define('ERROR_NO_TRADE_CHARACTER', '請填寫發貨ID');
define('ERROR_INVALID_QTY', '數額錯誤。請重試。');
define('ERROR_PLS_TRY_AGAIN', '請重試。');
define('ERROR_SUBMITTED_BY_OTHER_USER', '這單子已被其他客户提交。 請重試。');

define('WARNING_INVALID_ORDERS', '單子不存在');
define('JS_CONFIRM_CANCEL_ORDER', '確定取消單子?');
//define('JS_NO_CUSTOMERS_CODE', '請填寫收貨口令');

define('JS_NO_SS_BEFORE_TRADE', '請上載發貨前截圖');

define('JS_NO_SS_AFTER_TRADED', '請上載發貨後截圖');
define('JS_INVALID_SS_FILE_EXT', '上載截圖錯誤,只允許*.jpg, *.jpeg文檔名');
define('ERROR_UPLOAD_FAILED', '上載截圖失敗，請稍後再重試。');

define('TEXT_HELP_ORDER_STATUS_SELECT', '審核：在【單子狀態】等待接貨ID出現，然後進入遊戲交易（當面交易方式/遊戲內郵寄方式）後，必須確認填上【已發數量】，否則款項將不被支入【結存餘額】。<br><br>處理：等待臥虎遊戲員工確認確實已發數量，少或多於【草單數量】的單子，將會被視為免費金幣單子。<br ><br>完成：單子在完成後的15分鐘，單子款項會自動支入供應商的【結存餘額】。供應商可以在15分鐘後，自行選擇提款支入先前已設好的銀行賬號。 <br><br>取消：單子已被供應商或臥虎遊戲取消。');

define('COMMENTS_VIP_TRADE_COMPLETED', 'trade customer completed');
define('COMMENTS_PROCESSING_TO_COMPLETED', '您的貨款將在15分鐘後匯入您的臥虎賬號【結存餘額】。');

define('TEXT_PLS_CLICK_SEARCH_BTN', '請點擊搜索按鈕進行搜索');
?>