<?php

/*
  $Id: english.php,v 1.15 2005/04/04 07:08:34 weichen Exp
 */

// look in your $PATH_LOCALE/locale directory for available locales
// or type locale -a on the server.
// Examples:
// on RedHat try 'en_US'
// on FreeBSD try 'en_US.ISO_8859-1'
// on Windows try 'en', or 'English'

@setlocale(LC_TIME, 'en_US.ISO_8859-1');

define('DATE_FORMAT_SHORT', '%m/%d/%Y');  // this is used for strftime()
define('DATE_FORMAT_LONG', '%A, %d %B, %Y'); // this is used for strftime()
define('DATE_FORMAT', 'm/d/Y'); // this is used for date()
define('DATE_TIME_FORMAT', DATE_FORMAT_SHORT . ' %H:%M:%S');

define('LOCAL_STORE_NAME', 'OffGamers');

// #CHAVEIRO16# BEGIN PHPBB2
define('BOX_BBINDEX', 'tmf.Forum');
// #CHAVEIRO16# END PHPBB2
//CGDiscountSpecials start
define('PRICES_LOGGED_IN_TEXT', 'Must be logged in for prices!');
//CGDiscountSpecials end
////
// Return date in raw format
// $date should be in format mm/dd/yyyy
// raw date is in format YYYYMMDD, or DDMMYYYY
// if USE_DEFAULT_LANGUAGE_CURRENCY is true, use the following currency, instead of the applications default currency (used when changing language)
define('LANGUAGE_CURRENCY', 'USD');

// Global entries for the <html> tag
define('HTML_PARAMS', 'dir="LTR" lang="en" xmlns="http://www.w3.org/1999/xhtml" xmlns:fb="http://www.facebook.com/2008/fbml"');

// charset for web pages and emails
//define('CHARSET', 'iso-8859-1');
define('CHARSET', 'UTF-8');
define('EMAIL_CHARSET', 'UTF-8');

// page title
define('TITLE', STORE_NAME);

// BEGIN latest news
define('TABLE_HEADING_LATEST_NEWS', 'Latest News');
define('ALL_PRODUCTS_LINK', 'Find my game');

// header text in includes/header.php
define('HEADER_TITLE_CREATE_ACCOUNT', 'Create an Account');
define('HEADER_TITLE_MY_ACCOUNT', 'My Account');
define('HEADER_TITLE_CART_CONTENTS', 'View Cart');
define('HEADER_TITLE_CHECKOUT', 'Checkout');
define('HEADER_TITLE_TOP', 'Home');
define('HEADER_TITLE_CATALOG', 'Catalog');
define('HEADER_TITLE_LOGOFF', 'Log Off');
define('HEADER_TITLE_LOGIN', 'My Account');
define('HEADER_TITLE_MY_FAVOURITE_LINKS', 'My Favourite Links');
define('HEADER_TITLE_MY_PAYMENT_HISTORY', 'Account Statement');
define('HEADER_TITLE_MY_VIP_ACCOUNT', 'VIP account');
define('HEADER_TITLE_MY_VIP_INVENTORY_UPDATE', 'Edit Inventory List');
define('HEADER_TITLE_MY_VIP_REGISTER_SERVER', 'Register Inventory');
define('HEADER_TITLE_MY_VIP_ORDERS_HISTORY', 'Order List');
define('HEADER_TITLE_MY_VIP_REPORT', 'Sales Report');

define('HEADER_TITLE_SHOW_ALL', 'View All');
define('HEADER_GAME_NEWS_STORE_ANNOUNCEMENT', 'Game News / Store Announcement');

define('TEXT_SAVE', 'Save');

define('JAVASCRIPT_ENFORCEMENT', 'Please enable JavaScript  in your browser to experience all the custom features of our site, including the ability to make a purchase.');

// footer text in includes/footer.php
define('FOOTER_TEXT_REQUESTS_SINCE', 'requests since');
define('OGM_SPECIAL_NOTICE', '<div style="font-size:15px;font-weight:bold;">Check out our new toy!</div>
                        	<div style="">We\'ve made some minor tweaks to our webstore and installed a brand new toolbar! <br />
                        	 With the newly installed toolbar, you can: <br />
                        	 - Login or Register an account on OffGamers. (Supports Facebook Connect too!)<br />
                        	 - Set/Update your Regional Settings (Country, Currency & Language). <br />
                        	 - View/Update the item(s) in your shopping cart or Checkout and make payment.<br />
                        	</div>');

// text for day, month, year
define('DAY', 'Day');
define('DAY2', 'Day');
define('MONTH', 'Month');
define('YEAR', 'Year');
define('HOURS', 'Hours');
define('MINUTES', 'Minutes');
define('SECONDS', 'Seconds');

// text for gender
define('MALE', 'Male');
define('FEMALE', 'Female');
define('MALE_ADDRESS', 'Mr.');
define('FEMALE_ADDRESS', 'Ms.');

// e-mail greeting
define('EMAIL_SEPARATOR', '------------------------------------------------------');
define('EMAIL_GREET_MR', 'Dear Mr. %s,' . "\n\n");
define('EMAIL_GREET_MS', 'Dear Ms. %s,' . "\n\n");
define('EMAIL_GREET_NONE', 'Dear %s,' . "\n\n");

// e-mail buyback
define('EMAIL_BUYBACK_UPDATE_SUBJECT', implode(' ', array(EMAIL_SUBJECT_PREFIX, "Buyback Order Update #%s")));
define('EMAIL_BUYBACK_UPDATE_BODY', 'Buyback Order Summary:' . "\n" . EMAIL_SEPARATOR . "\n");
define('EMAIL_BUYBACK_UPDATE_ORDER_NUMBER', 'Order Number: %s');
define('EMAIL_BUYBACK_UPDATE_DATE_ORDERED', 'Order Date: %s');
define('EMAIL_BUYBACK_UPDATE_FOOTER', "\n\n" . STORE_EMAIL_SIGNATURE);

define('EMAIL_BUYBACK_UPDATE_CUSTOMER_EMAIL', 'Customer\'s Email Address: %s');
define('EMAIL_BUYBACK_UPDATE_PRODUCTS', 'Product');
define('EMAIL_BUYBACK_UPDATE_ORDER_TOTAL', 'Total: %s %s');
define('EMAIL_NEW_BUYBACK_STATUS', 'Status: Pending');
define('EMAIL_NEW_BUYBACK_ORDER_NUMBER', 'Buyback Order Number: %s');
define('EMAIL_NEW_BUYBACK_DATE_ORDERED', 'Buyback Order Date: %s');
define('EMAIL_NEW_BUYBACK_CUSTOMER_EMAIL', 'E-mail Address: %s');
define('EMAIL_BUYBACK_UPDATE_STATUS', 'Buyback Order Status: %s');

define('EMAIL_BUYBACK_PRESALES_SUBJECT', 'Buyback Server Notification');
define('EMAIL_LOCAL_STORE_EMAIL', '<EMAIL>');
define('EMAIL_BUYBACK_PRESALES_TITLE', 'Thank you for your support! You may now login to your ' . STORE_NAME . ' and sell your game currencies for the server that you have chosen.');
define('EMAIL_BUYBACK_PRESALES_SUMMARY_TITLE', 'Buyback Server Summary:');
define('EMAIL_BUYBACK_PRESALES_PRODUCT', 'Server %s');
define('EMAIL_BUYBACK_PRESALES_MIN_QTY', 'Min: %d');
define('EMAIL_BUYBACK_PRESALES_MAX_QTY', 'Max: %d');
define('EMAIL_BUYBACK_PRESALES_COMMENTS', 'Note:' . "\n\n" . STORE_NAME . ' is only available to members, and game currency buyback is on a first come first serve basis, do not hesitate and miss out on this opportunity to sell your game currencies to us. Thank you!');
define('EMAIL_LOCAL_STORE_EMAIL_SIGNATURE', "\n" . STORE_NAME . "\n" . '*************************************************' . "\n" . 'E-mail: <a href="mailto:' . EMAIL_LOCAL_STORE_EMAIL . '">' . EMAIL_LOCAL_STORE_EMAIL . '</a>');

define('EMAIL_TEXT_STATUS_UPDATE_TITLE', '');
define('EMAIL_TEXT_ORDER_NUMBER', 'Order Number:');
define('EMAIL_TEXT_DATE_ORDERED', 'Order Date:');
define('EMAIL_TEXT_INVOICE_URL', 'Detailed Invoice:');
define('EMAIL_TEXT_SUBJECT', 'Order Update #%d');
define('EMAIL_TEXT_PARTIAL_DELIVERY', '');
define('EMAIL_TEXT_CLOSING', 'For any enquiries or assistance, you may use our Online Live Support service or e-mail your enquiries to ' . EMAIL_TO . '. Please remember to include your Order Number when contacting us to expedite the process. Thanks again for shopping at ' . STORE_NAME . ".\n");

// text for date of birth example
define('DOB_FORMAT_STRING', 'mm/dd/yyyy');

// Mantis # 0000024 @ ************ - Add new boxes
define('BOX_HEADING_ACCOUNT_LEFT_BOX_MY_ACCOUNT', 'MY ACCOUNT HOME');
define('BOX_HEADING_ACCOUNT_LEFT_BOX_MY_PROFILE', 'MY PROFILE');
define('BOX_HEADING_ACCOUNT_LEFT_BOX_STORE_CREDITS', 'STORE CREDITS');
define('BOX_HEADING_ACCOUNT_LEFT_BOX_BUYER', 'BUYER');
define('BOX_HEADING_ACCOUNT_LEFT_BOX_SELLER', 'SELLER');
define('BOX_HEADING_ACCOUNT_LEFT_BOX_VIP_SELLER', 'VIP SELLER');
define('BOX_HEADING_ACCOUNT_LEFT_BOX_AFFILIATE', 'AFFILIATE');

define('MY_ACCOUNT_ACCOUNT_OVERVIEW_LINK', 'Overview');
define('MY_ACCOUNT_EDIT_PROFILE_LINK', 'Edit Profile');
define('MY_ACCOUNT_FACEBOOK_CONNECT', 'Facebook Connect');
define('MY_ACCOUNT_CHANGE_SECRET_QNA_LINK', 'Change Secret Q&A');
define('MY_ACCOUNT_NOTIFICATION_SETTINGS_LINK', 'Notification Settings');
define('MY_ACCOUNT_MANAGE_NEWSLETTERS_LINK', 'Manage Newsletters');
define('MY_ACCOUNT_OFFGAMERS_POINTS', 'OP');
define('MY_ACCOUNT_VERIFY_PHONE_NUMBER_LINK', 'Verify Mobile Number');
define('MY_ACCOUNT_VERIFY_EMAIL_LINK', 'Verify Email Address');
define('MY_ACCOUNT_VERIFICATION_SUBMISSION_FORM', 'Verification Submission Form');

define('MY_ACCOUNT_STORE_CREDITS_HISTORY_LINK', 'My Store Credits History');
define('MY_ACCOUNT_STORE_CREDITS_CONVERSION_LINK', 'Store Credits Conversion');
define('MY_ACCOUNT_STORE_CREDITS_TOPUP_LINK', 'Top Up Store Credits');

define('MY_ACCOUNT_BUY_ORDERS_HISTORY_LINK', 'Buy Order History');
define('MY_ACCOUNT_MY_ORDERS_LINK', 'My Orders');
define('MY_ACCOUNT_SPENDING_LIMIT_LINK', 'Spending Limits');

define('MY_ACCOUNT_SELL_ORDER_HISTORY_LINK', 'Sell Order History');
define('MY_ACCOUNT_WITHDRAW_STATEMENT_LINK', 'Withdraw Statement');
define('MY_ACCOUNT_WITHDRAW_MONEY_LINK', 'Withdraw Money');
define('MY_ACCOUNT_PAYMENT_STATUS_LINK', 'Payment Status');
define('MY_ACCOUNT_FAVORITE_SERVERS_LINK', 'Favorite Servers');

// header text in includes/boxes/my_account.php VIP SELLER
define('MY_ACCOUNT_VIP_INVENTORY_UPDATE', 'Edit Inventory List');
define('MY_ACCOUNT_VIP_REGISTER_SERVER', 'Register Inventory');
define('MY_ACCOUNT_VIP_ORDERS_HISTORY', 'Order List');
define('MY_ACCOUNT_VIP_REPORT', 'Sales Report');
// M#0000024

define('MY_ACCOUNT_AFFILIATE_OVERVIEW_LINK', 'Affiliate Overview');
define('MY_ACCOUNT_INVITE_FRIENDS_LINK', 'Invite Your Friends');

// Mantis # 0000024 @ ************ - My Account Phase 2
define('TITLE_MY_PROFILE', 'My Profile');
define('IMAGE_BUTTON_SAVE_PROFILE', 'Save Profile');
define('MY_ACCOUNT_CURRENT_ORDERS', 'Current Orders History');
define('MY_ACCOUNT_COMPLETED_ORDERS', 'Completed Orders History');
define('MY_ACCOUNT_CANCELLED_ORDERS', 'Cancelled Orders History');

// My Account Home alert messages
define('TEXT_CLICK_HERE', 'Click here');
define('TEXT_LIVE_SUPPORT', 'Live Support');
define('TEXT_LIVE_CHAT', 'Submit a Ticket');

// login box text in includes/boxes/loginbox.php
define('BOX_HEADING_LOGIN_BOX_MY_ACCOUNT', 'My Account');
define('LOGIN_BOX_MY_ACCOUNT', 'Account Overview');
define('LOGIN_BOX_ACCOUNT_EDIT', 'Edit Information');
define('LOGIN_BOX_ACCOUNT_PAYMENT_EDIT', 'Disbursement Methods'); //define('LOGIN_BOX_ACCOUNT_PAYMENT_EDIT','Payment Information');
define('LOGIN_BOX_ACCOUNT_STORE_CREDIT', 'My Store Credits');
define('LOGIN_BOX_ADDRESS_BOOK', 'Edit Address');
define('LOGIN_BOX_VERIFY_EMAIL', 'Confirm E-mail<span class="requiredInfo">*<span>');
define('LOGIN_BOX_ACTIVATE_ACCOUNT', 'Activate Account<span class="requiredInfo">*<span>');
define('LOGIN_BOX_ACCOUNT_HISTORY', 'View Order History');
define('LOGIN_BOX_PRODUCT_NOTIFICATIONS', 'Product Notifications');
define('LOGIN_BOX_BUYBACK_ORDER_HISTORY', 'View Buyback History');
define('LOGIN_BOX_BUYBACK_FAVOURITE', 'View Buyback Favourite');
define('LOGIN_BOX_ACCOUNT_ACTIVATION', 'Activate Account');
define('LOGIN_BOX_PASSWORD_FORGOTTEN', 'Forgot Password?');
define('LOGIN_BOX_HEADING', 'LOGIN OR REGISTER');
define('LOGIN_BOX_LOGIN_SELECTION', 'I\'m a Returning Customer and I want to login now.');
define('LOGIN_BOX_CREATE_ACCOUNT_SELECTION', 'I\'m a <b>New Customer</b> and I want to create a new account now.');
define('LOGIN_BOX_SEND_PASSWORD_SELECTION', 'Kindly enter your E-Mail Address and we will send you the password to your email.');
define('LOGIN_EMAIL', 'E-mail Address');
define('LOGIN_PASSWORD', 'Password');
define('LOGIN_PASSWORD_NEW', 'New Password');
define('LOGIN_PASSWORD_NEW_CONFIRMATION', 'Confirm New Password');
define('LOGIN_RETURN', 'Return to Login');
define('CONFIRM_LOGIN_PASSWORD_DESC', 'For security purposes, you are required to fill in OffGamers login password to purchase with your OffGamers Store Credits.');

define('TEXT_WITHDRAWABLE_CREDIT', 'Withdrawable Credit');
define('HEADER_TITLE_MY_WITHDRAW_MONEY', 'Withdraw Money');

// categories box text in includes/boxes/categories.php
define('BOX_HEADING_CATEGORIES', 'Categories');

// manufacturers box text in includes/boxes/manufacturers.php
//define('BOX_HEADING_MANUFACTURERS', 'Manufacturers');
define('BOX_HEADING_MANUFACTURERS', 'Manufacturers');

// whats_new box text in includes/boxes/whats_new.php
define('BOX_HEADING_WHATS_NEW', 'What\'s New?');

// quick_find box text in includes/boxes/quick_find.php
define('BOX_HEADING_SEARCH', 'Quick Search');
define('BOX_SEARCH_TEXT', 'Use keywords to find the product you are looking for.');
define('TEXT_ALL_CATEGORIES', 'All Categories');
define('BOX_SEARCH_ADVANCED_SEARCH', 'Advanced Search');

// specials box text in includes/boxes/specials.php
define('BOX_HEADING_SPECIALS', 'Specials');

// reviews box text in includes/boxes/reviews.php
define('BOX_HEADING_REVIEWS', 'Reviews');
define('BOX_REVIEWS_WRITE_REVIEW', 'Write a review on this product!');
define('BOX_REVIEWS_NO_REVIEWS', 'There are currently no product reviews');
define('BOX_REVIEWS_TEXT_OF_5_STARS', '%s of 5 Stars!');

// shopping_cart box text in includes/boxes/shopping_cart.php
define('BOX_HEADING_SHOPPING_CART', 'Shopping Cart');
define('SHOPPING_CART_BOX_EDIT_CART', 'Edit Shopping Cart');
define('BOX_SHOPPING_CART_EMPTY', 'No Item');
define('TEXT_LATEST_PRODUCT_ADDED', 'Added to your shopping cart:');

// order_history box text in includes/boxes/order_history.php
define('BOX_HEADING_CUSTOMER_ORDERS', 'Order History');

// best_sellers box text in includes/boxes/best_sellers.php
define('BOX_HEADING_BESTSELLERS', 'Bestsellers');
define('BOX_HEADING_BESTSELLERS_IN', 'Bestsellers in<br>&nbsp;&nbsp;');

// notifications box text in includes/boxes/products_notifications.php
define('BOX_HEADING_NOTIFICATIONS', 'Notifications');
define('BOX_NOTIFICATIONS_NOTIFY', 'Notify me of updates to <b>%s</b>');
define('BOX_NOTIFICATIONS_NOTIFY_REMOVE', 'Do not notify me of updates to <b>%s</b>');

// manufacturer box text
define('BOX_HEADING_MANUFACTURER_INFO', 'Manufacturer Info');
define('BOX_MANUFACTURER_INFO_HOMEPAGE', '%s Homepage');
define('BOX_MANUFACTURER_INFO_OTHER_PRODUCTS', 'Other products');

// languages box text in includes/boxes/languages.php
define('BOX_HEADING_LANGUAGES', 'Languages');

// currencies box text in includes/boxes/currencies.php
define('BOX_HEADING_CURRENCIES', 'Choose Your Currencies');

define('BOX_HEADING_HELP_DESK', 'Help Desk');

// polling box text in includes/boxes/polling.php
define('BOX_HEADING_POLLING', 'OffGamers Polls');

// information box text in includes/boxes/information.php
define('BOX_HEADING_INFORMATION', 'Information');
define('BOX_INFORMATION_CONDITIONS', 'Conditions of Use');
define('BOX_INFORMATION_SHIPPING', 'Shipping & Returns');
define('BOX_INFORMATION_ABOUT_US', 'About Us');
define('BOX_HEADING_LINKS', 'Link');

define('BOX_INFORMATION_NEWS', 'NEWS & PROMOTIONS');
define('BOX_INFORMATION_TOS', 'Terms of Service');
define('BOX_INFORMATION_PRIVACY', 'Privacy Policy');
define('BOX_INFORMATION_FAQ', 'FAQs');
define('BOX_INFORMATION_CONTACT_WEB', 'Contact Us Form');
define('BOX_INFORMATION_CONTACT', 'Contact Us');
define('BOX_INFORMATION_PROMOTION', 'Promotions');
define('BOX_INFORMATION_DISCLAIMER', 'Disclaimer');
define('BOX_LINKS', 'Links');

// tell a friend box text in includes/boxes/tell_a_friend.php
define('BOX_HEADING_TELL_A_FRIEND', 'Tell A Friend');
define('BOX_TELL_A_FRIEND_TEXT', 'Tell someone you know about this product.');

// regional setting
define('BOX_HEADING_REGIONAL_SETTING', 'REGIONAL SETTINGS');
define('TEXT_REGIONAL_COUNTRY', 'Country');
define('TEXT_LANGUAGE', 'Language');
define('TEXT_REGIONAL_NOTE', 'Set your default regional setting for next visit.');

//BEGIN allprods modification
define('BOX_INFORMATION_ALLPRODS', 'View All Items');
//END allprods modification

define('BOX_CHANGE_PASSWORD', 'CHANGE PASSWORD');
define('BOX_IMPORTANT_NOTICE', 'IMPORTANT NOTICE');
define('BOX_ERROR_INVALID_ANSWER', 'Invalid answer.');
define('BOX_ERROR_INVALID_LAST4DIGIT', 'Invalid last 4 digit phone number.');
define('BOX_SUCCESS_VALIDATE_LAST4DIGIT', 'Your security token has been successfully sent to your registered email address.');
define('BOX_CHANGE_CONTACT_NOTICE', 'Effective from 16th May 2012, our system will no longer accept landlines or VOIP numbers for call verification. In order to enhance account security, any OffGamers account holders that falls under the following criterias below, are required to update their mobile number to their OffGamers profile :-<br>A. Customers with existing accounts and with landlines number registered to their current profiles.<br>B. Accounts that shared similar phone numbers with multiple accounts/profiles.<br><br>This measure is necessary to accommodate with our latest mobile verification, which we will be releasing at later stages. We do apologize in advance for the inconvenience we might have caused on this matter.');
define('BOX_UPDATE_YOUR_MOBILE_NUMBER', 'UPDATE YOUR MOBILE NUMBER');
define('BOX_CHANGE_CONTACT_FOR_SECURITY_REASONS', 'For security reasons, please complete your account verification by filling in your current mobile number. To complete this process, you could either fill in the last 4 digit of your previous contact number or by submitting your secret question and answer for authentication.');
define('BOX_CHANGE_CONTACT_FILL_LAST4DIGIT', 'For authentication purposes, kindly fill in the last 4 digit of your current phone number below:');
define('BOX_CHANGE_CONTACT_FILL_LAST4DIGIT_DESC', 'If the mobile number is lost or no longer in use, do request for a security token below . The code will be sent to your registered e-mail.');
define('BOX_CHANGE_CONTACT_FOOTER_DESC', 'If you have any inquiries, please do not hesitate to <a href="' . tep_href_link(FILENAME_CUSTOMER_SUPPORT, '', 'SSL') . '">contact us</a> as our operators will be glad to assist you.');
define('BOX_CHANGE_CONTACT_OR', 'or');

// checkout procedure text
define('CHECKOUT_BAR_DELIVERY', 'Delivery<br>Information');
define('CHECKOUT_BAR_INFO', 'Checkout Info');
define('CHECKOUT_BAR_PAYMENT', 'Payment<br>Information');
define('CHECKOUT_BAR_CONFIRMATION', 'Confirmation');
define('CHECKOUT_BAR_FINISHED', 'Finished');

// pull down default text
define('TEXT_ALL_PAGES', 'All');
define('PULL_DOWN_DEFAULT', 'Please Select');
define('TYPE_BELOW', 'Type Below');
define('TEXT_REFRESH_WHEN_CHANGE', '(Page will refresh when changed)');
define('PULL_DOWN_DEFAULT_GAME_CARD', 'Select game card value');

// javascript messages
define('JS_ERROR', 'Errors have occured during the process of your form.\n\nPlease make the following corrections:\n\n');

define('JS_REVIEW_TEXT', '* The \'Review Text\' must have at least ' . '10' . ' characters.\n');
define('JS_REVIEW_RATING', '* You must rate the product for your review.\n');
define('JS_REVIEW_CUSNAME', '* You must enter your \'First Name\'.\n');
define('JS_ERROR_NO_PAYMENT_MODULE_SELECTED', '* Please select a payment method for your order.\n');
define('JS_ERROR_NO_SHIPPING_MODULE_SELECTED', '* Please select a shipping method for your order.\n');

define('JS_ERROR_SUBMITTED', 'This form has already been submitted. Please press Ok and wait for this process to be completed.');

define('JS_ERROR_PRODUCT_EMPTY', 'Please select a game card vale');

define('ERROR_NO_PAYMENT_MODULE_SELECTED', 'Please select a payment method for your order.');
define('ERROR_NO_PAYMENT_MODULE_NEEDED', 'You do not need to select a payment method since the total amount can be fully debited from your store credit.');
define('ERROR_CART_COMMENTS_REQUIRED', 'Please filled in required comments about your order!');
define('ERROR_SIGNUP_CART_COMMENTS_REQUIRED', 'Please filled in required comments in the registration form!');

define('ERROR_PLS_ENTER_ACCOUNT_NAME', 'Please enter Account name');
define('ERROR_PLS_ENTER_CHAR_NAME', 'Please enter Character name');
define('ERROR_PLS_ENTER_PASSWORD', 'Please enter Password');
define('ERROR_EMAIL_VERIFICATION_CODE', 'Verification Code must contain 12 characters.');
define('ERROR_WRONG_PASSWORD', 'Invalid password, please try again');

define('CATEGORY_COMPANY', 'Company Details');
define('CATEGORY_VERIFY_EMAIL', 'Email Verification');
define('CATEGORY_PERSONAL', 'Your Personal Details');
define('CATEGORY_ADDRESS', 'Your Address');
define('CATEGORY_CONTACT', 'Your Contact Information');
define('CATEGORY_OPTIONS', 'Options');
define('CATEGORY_PASSWORD', 'Your Password');

define('ENTRY_ACCOUNT_NAME', 'Account Name');
define('ENTRY_WOW_ACCOUNT_NAME', 'WOW Account Name');
define('ENTRY_ADD_CHARACTER_NAME', 'Fill in character name exactly in');
define('ENTRY_CHARACTER_INGAME_TIME', '<td class="smallText"><div style="padding:5px 10px 5px 20px;float:left">In game time:</div></td><td class="smallText">%s <span class="redIndicator">*</span>hour(s) %s</td>');
define('ENTRY_CHARACTER_INGAME_DURATION', '<td class="smallText"><div style="padding:5px 10px 5px 20px;float:left">In game duration:</div></td><td class="smallText">%s hour(s) %s</td>');
define('TEXT_CHARACTER_INGAME_NOTE', 'This is the time when you are going to log into the game after checkout in hours.');
define('TEXT_CHARACTER_INGAME_HOUR_NOTE', 'This is the approximate duration will be logged into the game in hours.');
define('ENTRY_ORDER_GUYA_ACCOUNT', '<td class="smallText" style="padding:5px 10px 5px 20px;" align="left" nowrap>Account name:</td><td class="smallText">%s <span class="redIndicator">*</span></td>');
define('ENTRY_ORDER_GUYA_PWD', '<td class="smallText" style="padding:5px 10px 5px 20px;" align="left" nowrap>Password:</td><td class="smallText">%s <span class="redIndicator">*</span></td>');
define('ENTRY_QNA', '<td class="smallText" style="padding:5px 10px 5px 20px;" align="left" nowrap>Answer:</td><td class="smallText">%s <span class="redIndicator">*</span></td>');
define('ENTRY_ORDER_GUYA_WOW_ACCOUNT_INFO', '<td class="smallText" style="padding:5px 10px 5px 20px;" align="left">&nbsp;</td><td class="smallText">For World of Warcraft gold purchases, please fill in your Battle.net account.</td>');
define('ENTRY_ORDER_GUYA_WOW_ACCOUNT_INFO_TEXT', 'For World of Warcraft gold purchases, please fill in your Battle.net account.');
define('ENTRY_ORDER_GUYA_WOW_ACCOUNT', '<td class="smallText" style="padding:5px 10px 5px 20px;" align="left" nowrap>WOW Account Name:</td><td class="smallText">%s Optional</td>');
define('ENTRY_ORDER_GUYA_WOW_ACCOUNT_INFO2', '<td class="smallText" style="padding:5px 10px 5px 20px;" align="left">&nbsp;</td><td class="smallText">Applicable only for World of Warcraft.</td>');
define('ENTRY_ORDER_GUYA_WOW_ACCOUNT_INFO2_TEXT', 'Applicable only for World of Warcraft.');
define('TEXT_ORDER_FACE_TO_FACE_NOTE', 'You could adjust the in-game duration if Face to Face Delivery or Open Store Delivery method is selected. <b>(Note: We will start sourcing for suppliers after the "I am online now" button is initiated)</b><br /><br /><span class="redIndicator">Note:For <b>NeverWinter</b>, Please fill in your full character name and Item name - Example:"playername@handle(Item Name)" Astral Diamond purchases as the trading method is through Auction House only.</span><br /><br /><span class="redIndicator">Note: For <b>Diablo 3</b>, Please fill in your battletag (Example: ABC#1234) instead of character name.</span>');
define('TEXT_ORDER_GUYA_NOTE', 'We will log in to your account and transfer the gold onto the character you specified.');
define('TEXT_ORDER_MAIL_NOTE', 'We will deliver to the character name you specified via in-game mail.<br /><br /><span class="redIndicator">Note : For Guild War 2, Please fill in your "Display Name" (Example: ABC.1234) instead of character name. (Either "display name" or character name are valid, but "display name" could help us to speed up the delivery)</span>');
define('TEXT_ORDER_OPEN_STORE_NOTE', 'You will be able to opt your in-game duration once you have changed your delivery method to Face to Face delivery OR Open Store delivery. <b>(Note: We will only start sourcing for your order once you click on "I am online now")</b>');
define('TEXT_ORDER_CHANGE_DEFAULT_CHARACTER', 'Change your default character');
define('TEXT_PERSONAL_INFO_REQUIRED', '%s are required proceeding to make payment in order for us to verify and process your orders. (You will not be asked to do this for your future orders)');
define('ENTRY_VERIFY_EMAIL', 'Email Confirmation');
define('ENTRY_VERIFY_EMAIL_TEXT', '*');
define('ENTRY_VERIFY_EMAIL_ERROR', 'Email Confirmation must match your Email.');
define('ENTRY_COMPANY', 'Company Name:');
define('ENTRY_COMPANY_ERROR', '');
define('ENTRY_COMPANY_TEXT', '');
define('ENTRY_GENDER', 'Gender:');
define('ENTRY_GENDER_ERROR', 'Please select your Gender.');
define('ENTRY_GENDER_TEXT', '*');
define('ENTRY_CUSTOMER_IDENTIFY_NUMBER', 'IC number:');
define('ENTRY_FIRST_NAME', 'First Name:');
define('ENTRY_FIRST_NAME_ERROR', 'Your First Name must contain a minimum of ' . ENTRY_FIRST_NAME_MIN_LENGTH . ' characters.');
define('ENTRY_FIRST_NAME_TEXT', '*');
define('ENTRY_LAST_NAME', 'Last Name:');
define('ENTRY_LAST_NAME_ERROR', 'Your Last Name must contain a minimum of ' . ENTRY_LAST_NAME_MIN_LENGTH . ' characters.');
define('ENTRY_LAST_NAME_TEXT', '*');
define('ENTRY_DATE_OF_BIRTH', 'Date of Birth:');
define('ENTRY_DATE_OF_BIRTH_OVER_ERROR', 'Invalid birth\'s year, please re-enter again');
define('ENTRY_DATE_OF_BIRTH_FUTURE_ERROR', 'Invalid birth\'s year, you have enter a future date');
define('ENTRY_DATE_OF_BIRTH_ERROR', 'Your Date of Birth must contain a valid date.');
define('ENTRY_DATE_OF_BIRTH_ERROR_1', 'You must select a');
define('ENTRY_DATE_OF_BIRTH_ERROR_2', ' from the');
define('ENTRY_DATE_OF_BIRTH_ERROR_3', ' pull down menu. ');
define('ENTRY_DATE_OF_BIRTH_ERROR_4', 'Your year must contain 4 digits.');
define('ENTRY_DATE_OF_BIRTH_TEXT', '* (eg. 05/21/1970)');
define('ENTRY_EMAIL_ADDRESS', 'E-mail Address:');
define('ENTRY_EMAIL_ADDRESS_ERROR', 'Your E-Mail Address must contain a minimum of ' . ENTRY_EMAIL_ADDRESS_MIN_LENGTH . ' characters.');
define('ENTRY_EMAIL_ADDRESS_CHECK_ERROR', 'Your E-Mail Address does not appear to be valid - please make any necessary corrections.');
define('ENTRY_EMAIL_ADDRESS_ERROR_EXISTS', 'We notice that your e-mail address exists in our records, please <a href="' . tep_href_link(FILENAME_LOGIN) . '"> sign in </a> with the e-mail address or <a href="' . tep_href_link(FILENAME_PASSWORD_FORGOTTEN) . '">click here</a> if you have forgotten your password.');
define('ENTRY_EMAIL_ADDRESS_TEXT', '*');
define('ENTRY_STREET_ADDRESS', 'Street Address:');
define('ENTRY_STREET_ADDRESS_ERROR', 'Your Street Address must contain a minimum of ' . ENTRY_STREET_ADDRESS_MIN_LENGTH . ' characters.');
define('ENTRY_STREET_ADDRESS_TEXT', '*');
define('ENTRY_SUBURB', 'Suburb:');
define('ENTRY_SUBURB_ERROR', '');
define('ENTRY_SUBURB_TEXT', '');
define('ENTRY_POST_CODE', 'Zip/Post Code:');
define('ENTRY_POST_CODE_ERROR', 'Your Post Code must contain a minimum of ' . ENTRY_POSTCODE_MIN_LENGTH . ' characters.');
define('ENTRY_POST_CODE_TEXT', '*');
define('ENTRY_CITY', 'City:');
define('ENTRY_CITY_ERROR', 'Your City must contain a minimum of ' . ENTRY_CITY_MIN_LENGTH . ' characters.');
define('ENTRY_CITY_TEXT', '*');
define('ENTRY_STATE', 'State/Province:');
define('ENTRY_STATE_ERROR', 'Your State must contain a minimum of ' . ENTRY_STATE_MIN_LENGTH . ' characters.');
define('ENTRY_STATE_ERROR_SELECT', 'Please select a state from the States pull down menu.');
define('ENTRY_STATE_TEXT', '*');
define('ENTRY_COUNTRY', 'Country:');
define('ENTRY_LOCATION', 'Location:');
define('ENTRY_COUNTRY_ERROR', 'You must select a country from the Countries pull down menu.');
define('ENTRY_LOCATION_ERROR', 'You must select a location from the Location pull down menu.');
define('ENTRY_COUNTRY_TEXT', '*');
define('ENTRY_DELIVERY_ADDRESS_ERROR', 'You must select a country from the Countries pull down menu.');
define('ENTRY_DELIVERY_ADDRESS_TEXT', '*');
define('ENTRY_TELEPHONE_NUMBER', 'Mobile Phone Number:');
define('ENTRY_TELEPHONE_NUMBER_ERROR', 'Your Mobile Phone Number must contain a minimum of ' . ENTRY_TELEPHONE_MIN_LENGTH . ' characters.');
define('ENTRY_TELEPHONE_NUMBER_TEXT', '*');
define('ENTRY_FAX_NUMBER', 'Fax Number:');
define('ENTRY_FAX_NUMBER_ERROR', '');
define('ENTRY_FAX_NUMBER_TEXT', '');
define('ENTRY_NEWSLETTER', 'Newsletter:');
define('ENTRY_NEWSLETTER_TEXT', 'Subscribe to our newsletter for special offers, exclusives and promotions.');
define('ENTRY_NEWSLETTER_YES', 'Subscribed');
define('ENTRY_NEWSLETTER_NO', 'Unsubscribed');
define('ENTRY_NEWSLETTER_ERROR', '');
define('ENTRY_PASSWORD', 'Password:');
define('ENTRY_REMEMBER_ME', 'Remember Me');
define('ENTRY_PASSWORD_ERROR', 'Your Password must contain a minimum of ' . ENTRY_PASSWORD_MIN_LENGTH . ' characters.');
define('ENTRY_PASSWORD_ERROR_NOT_MATCHING', 'The Confirm Password must match your Password.');
define('ENTRY_PASSWORD_TEXT', '*');
define('ENTRY_PASSWORD_CONFIRMATION', 'Confirm Password:');
define('ENTRY_PASSWORD_CONFIRMATION_TEXT', '*');
define('ENTRY_PASSWORD_CURRENT', 'Current Password:');
define('ENTRY_PASSWORD_CURRENT_TEXT', '*');
define('ENTRY_PASSWORD_CURRENT_ERROR', 'Your current Password must contain a minimum of ' . ENTRY_PASSWORD_MIN_LENGTH . ' characters.');
define('ENTRY_PASSWORD_NEW', 'New Password:');
define('ENTRY_PASSWORD_NEW_TEXT', '*');
define('ENTRY_PASSWORD_NEW_ERROR', 'Your new Password must contain a minimum of ' . ENTRY_PASSWORD_MIN_LENGTH . ' characters.');
define('ENTRY_PASSWORD_NEW_ERROR_NOT_MATCHING', 'The Password Confirmation must match your New Password.');
define('PASSWORD_HIDDEN', '--HIDDEN--');
define('ENTRY_SERIAL_ERROR', 'Please enter the serial code');
define('ENTRY_SERIAL_CHECK_ERROR', 'Your serial must contain 12 characters.');
define('ENTRY_MISMATCH_ANSWER_ERROR', 'Your security token is invalid. Please try again.');
define('ENTRY_MISMATCH_ANSWER_RESEND_LINK', 'Click <a href="%s">here</a>.');
define('ENTRY_CHARACTER_NAME', 'Character Name');
define('ENTRY_REBATE', 'Total Rebate');
define('ENTRY_ADDED_BONUS', 'Extra Bonus');
define('ENTRY_DELIVERY_INFORMATION', 'Please enter your game account info');

define('FORM_REQUIRED_MSG', 'Personal information obtained are used solely for the purpose of enhancing the functionality and level of service.');
define('FORM_REQUIRED_INFORMATION', '* Required information');
define('SECTION_TITLE_FORM_PM_REQUIRED_INFO', 'Required Information:');

define('TEXT_NAME', 'Name');
define('TEXT_BILLING_ADDRESS', 'Address');
define('TEXT_STATE_OR_ZIP', 'State/Zip');
define('TEXT_FIRST_NAME', 'First Name');
define('TEXT_LAST_NAME', 'Last Name');
define('TEXT_CONTACT_NUMBER', 'Contact Number');
define('TEXT_BILLING_ADDRESS1', 'Address1');
define('TEXT_COUNTRY_CODE', 'Country Code');
define('TEXT_POST_CODE', 'Zip/Post Code');
define('TEXT_CITY', 'City');
define('TEXT_COUNTRY', 'Country');
define('TEXT_STATE', 'State/Province');
define('TEXT_SECRET_QUESTION', 'Secret Question');
define('TEXT_ANSWER', 'Answer');
define('TEXT_SELLING_DELIVERY_TIME', 'Delivery Time');
define('TEXT_OP', 'OP');
define('TEXT_REBATE', 'Rebate');
define('TEXT_TOTAL_PAY', 'Payable Amount');

define('JS_ERROR_EMPTY_SEARCH_INPUT', 'Your search must not be empty.');
// constants for use in tep_prev_next_display function
define('TEXT_RESULT_PAGE', 'Result Pages:');
define('TEXT_DISPLAY_NUMBER_OF_PRODUCTS', 'Displaying <b>%d</b> to <b>%d</b> (of <b>%d</b> results)');
define('TEXT_DISPLAY_NUMBER_OF_ORDERS', 'Displaying <b>%d</b> to <b>%d</b> (of <b>%d</b> orders)');
define('TEXT_DISPLAY_NUMBER_OF_REVIEWS', 'Displaying <b>%d</b> to <b>%d</b> (of <b>%d</b> reviews)');
define('TEXT_DISPLAY_NUMBER_OF_PRODUCTS_NEW', 'Displaying <b>%d</b> to <b>%d</b> (of <b>%d</b> new products)');
define('TEXT_DISPLAY_NUMBER_OF_SPECIALS', 'Displaying <b>%d</b> to <b>%d</b> (of <b>%d</b> specials)');

define('PREVNEXT_TITLE_FIRST_PAGE', 'First Page');
define('PREVNEXT_TITLE_PREVIOUS_PAGE', 'Previous Page');
define('PREVNEXT_TITLE_NEXT_PAGE', 'Next Page');
define('PREVNEXT_TITLE_LAST_PAGE', 'Last Page');
define('PREVNEXT_TITLE_PAGE_NO', 'Page %d');
define('PREVNEXT_TITLE_PREV_SET_OF_NO_PAGE', 'Previous Set of %d Pages');
define('PREVNEXT_TITLE_NEXT_SET_OF_NO_PAGE', 'Next Set of %d Pages');
define('PREVNEXT_BUTTON_FIRST', '&lt;FIRST');
define('PREVNEXT_BUTTON_PREV', 'Prev');
define('PREVNEXT_BUTTON_NEXT', 'Next');
define('PREVNEXT_BUTTON_LAST', 'LAST&gt;');

define('BUTTON_FIRST', 'FIRST');
define('BUTTON_LAST', 'LAST');

define('BUTTON_YES', 'Yes');
define('BUTTON_OK', 'OK');
define('BUTTON_SUBMIT', 'Submit');
define('BUTTON_CONFIRM', 'Confirm');
define('BUTTON_CANCEL', 'Cancel');
define('BUTTON_ADD', 'Add');
define('BUTTON_DELETE', 'Delete');
define('BUTTON_BATCH_UPDATE', 'Batch update');
define('BUTTON_EXPORT', 'Export');
define('BUTTON_EXPORT_TEMPLATE', 'Export template');
define('BUTTON_IMPORT', 'Import');
define('BUTTON_REFRESH', 'Refresh');
define('BUTTON_RESET', 'Reset');
define('BUTTON_REPORT', 'Report');
define('BUTTON_NO', 'No');

define('BUTTON_ADD_PM', 'Add Payment Account');
define('BUTTON_AGREE', 'Agree');
define('BUTTON_BACK', 'Back');
define('BUTTON_DELETE', 'Delete');
define('BUTTON_DISAGREE', 'Disagree');
define('BUTTON_EDIT', 'Edit');
define('BUTTON_REMOVE', 'Remove');
define('BUTTON_PRESALE_NOTICE', 'Pre-Sale E-mail');
define('BUTTON_PRESALE_REMOVE', 'Pre-Sale Remove');
define('BUTTON_SAVE_CHANGES', 'Save changes');
define('BUTTON_SEARCH', 'Search');
define('BUTTON_SKIP_NOW', 'Skip now');
define('BUTTON_SHOW_ALL_PRODUCTS', 'Show All Servers');
define('BUTTON_SHOW_SELLING_PRODUCTS', 'Show Selling Servers');
define('BUTTON_SIGN_IN', 'Sign In');
define('BUTTON_SIGN_UP', 'Sign Up');
define('BUTTON_SIGN_OUT', 'Sign Out');
define('BUTTON_RESEND_SECURITY_TOKEN', 'Resend security token');

define('BUTTON_MIN_CHAR_LENGTH', 11);
define('IMAGE_BUTTON_ADD_ADDRESS', 'Add Address');
define('IMAGE_BUTTON_ADDRESS_BOOK', 'Address Book');
define('IMAGE_BUTTON_BACK', 'Back');
define('IMAGE_BUTTON_BUY_NOW', 'Buy Now');
define('IMAGE_BUTTON_CHANGE_ADDRESS', 'Change Address');
define('IMAGE_BUTTON_CHECKOUT', 'Proceed Checkout');
define('IMAGE_BUTTON_CONFIRM_ORDER', 'Confirm Order');
define('IMAGE_BUTTON_SECURE_CHECKOUT', 'Secure Checkout');
define('IMAGE_BUTTON_CONFIRM', 'Confirm Order');
define('IMAGE_BUTTON_CONFIRM_CODE', 'Confirm Code');
define('IMAGE_BUTTON_CONTINUE', 'Continue');
define('IMAGE_BUTTON_CONTINUE_SHOPPING', 'Continue Shopping');
define('IMAGE_BUTTON_CONVERT_NOW', 'Convert Now');
define('IMAGE_BUTTON_DELETE', 'Delete');
define('IMAGE_BUTTON_DOWNLOAD', 'Download');
define('IMAGE_BUTTON_EDIT_ACCOUNT', 'Edit Account');
define('IMAGE_BUTTON_HISTORY', 'Order History');
define('IMAGE_BUTTON_IN_CART', 'Add to Cart');
define('IMAGE_BUTTON_DIRECT_TOP_UP', 'Direct Top-up');
define('IMAGE_BUTTON_LOGIN', 'Login');
define('IMAGE_BUTTON_SEND_PASSWORD', 'Send Password');
define('IMAGE_BUTTON_NOTIFICATIONS', 'Notifications');
define('IMAGE_BUTTON_OUT_OF_STOCK', 'Out of Stock');
define('IMAGE_BUTTON_PRE_ORDER', 'Pre-Order');
define('IMAGE_BUTTON_QUICK_FIND', 'Search');
define('IMAGE_BUTTON_REMOVE_NOTIFICATIONS', 'Remove Notifications');
define('IMAGE_BUTTON_REVIEWS', 'Reviews');
define('IMAGE_BUTTON_SEARCH', 'Search');
define('IMAGE_BUTTON_SHIPPING_OPTIONS', 'Shipping Options');
define('IMAGE_BUTTON_TELL_A_FRIEND', 'Tell a Friend');
define('IMAGE_BUTTON_UPDATE', 'Update');
define('IMAGE_BUTTON_CONFIRM_TEL', 'Confirm');
define('IMAGE_BUTTON_VERIFY', 'Verify');
define('IMAGE_BUTTON_UPDATE_CART', 'Update Cart');
define('IMAGE_BUTTON_WRITE_REVIEW', 'Write Review');
define('IMAGE_BUTTON_REDEEM_VOUCHER', 'Redeem');
define('IMAGE_BUTTON_SELL_MORE', 'Sell More');
define('IMAGE_BUTTON_TRANSFER_NOW', 'Transfer Now');
define('IMAGE_BUTTON_ADD', 'Add');
define('IMAGE_BUTTON_NEXT', 'Next');
define('IMAGE_BUTTON_REFRESH', 'Refresh');
define('IMAGE_BUTTON_YES', 'Yes');
define('IMAGE_BUTTON_YES2', 'Yes');
define('IMAGE_BUTTON_TOP_UP_STORE_CREDITS', 'Top Up Store Credits');
define('IMAGE_BUTTON_BUY_CODE', 'Buy Code');
define('IMAGE_BUTTON_PAY_WITH_SC_CURRENCY', 'Pay with %s (Store Credit)');
define('IMAGE_BUTTON_PAY_WITH_WEBSITE_CURRENCY', 'Pay with %s');
define('IMAGE_BUTTON_PAY_WITHOUT_SC', 'Pay without Store Credit');
define('TEXT_INFO_PAYMENT_CONFIRM_CAPTION', 'Checkout will proceed to %s');

define('ALT_BUTTON_ADD_PM', 'Add Method');  //define('ALT_BUTTON_ADD_PM', 'Add Payment Account');
define('ALT_BUTTON_BACK', 'Back to previous page');
define('ALT_BUTTON_CONFIRM', 'Confirm');
define('ALT_BUTTON_CONTINUE', 'Continue');
define('ALT_BUTTON_RESET', 'Reset');
define('ALT_BUTTON_SEARCH', 'Search');
define('ALT_BUTTON_SHOW_ALL_PRODUCTS', 'Click here to show all servers.');
define('ALT_BUTTON_SHOW_SELLING_PRODUCTS', 'Click here to show your selling servers only.');
define('ALT_BUTTON_SUBMIT', 'Submit form');
define('ALT_BUTTON_SIGN_IN', 'Sign In');
define('ALT_BUTTON_SIGN_UP', 'Sign Up');
define('ALT_BUTTON_UPDATE', 'Update');

define('TABLE_HEADING_SERVER', 'Server');
define('TABLE_HEADING_ACTION', 'Action');
define('TABLE_HEADING_PER_UNIT', 'Per Unit');
define('TABLE_HEADING_STATUS', 'Status');
define('TABLE_HEADING_QTY', 'Qty.');
define('TABLE_HEADING_DELIVERY_TIME', 'Delivery Time');
define('TABLE_HEADING_DELIVERY_METHOD_SUPPORT', 'Delivery Method Support');

define('TITLE_TRANS_PAYMENT', 'Payment %s');

define('SMALL_IMAGE_BUTTON_DELETE', 'Delete');
define('SMALL_IMAGE_BUTTON_EDIT', 'Edit');
define('SMALL_IMAGE_BUTTON_VIEW', 'View');

define('ICON_ARROW_RIGHT', 'more');
define('ICON_CART', 'In Cart');
define('ICON_ERROR', 'Error');
define('ICON_NOTICE', 'Notice');
define('ICON_SUCCESS', 'Success');
define('ICON_WARNING', 'Warning');
define('ICON_PROMO', 'Promo');

// CATALOG_PRODUCTS_WITH_IMAGES_mod
define('BOX_CATALOG_PRODUCTS_WITH_IMAGES', 'Printable Catalog');
define('IMAGE_BUTTON_UPSORT', 'Sort Asending');
define('IMAGE_BUTTON_DOWNSORT', 'Sort Desending');

// CD Key
define('TEXT_CDKEY_SUSPENDED_FOR_VIEWING', 'The CD Key/Time Card image is temporarily blocked due to a payment dispute. The serial code has been forwarded to the game maker for appropriate action. <NAME_EMAIL> if this is a mistake.');

// Down For Maintenance
define('TEXT_BEFORE_DOWN_FOR_MAINTENANCE', 'NOTICE: This website will be down for maintenance on %s for a period of %s.');
define('TEXT_ADMIN_DOWN_FOR_MAINTENANCE', 'NOTICE: the website is currently Down For Maintenance to the public');

define('TEXT_GREETING_PERSONAL', 'Welcome back, <span class="greetUser">%s</span>! Would you like to see which <a href="%s">new products</a> are available to purchase?');
define('TEXT_GREETING_PERSONAL_RELOGON', '<small>If you are not %s, please <a href="%s">log yourself in</a> with your account information.</small>');
define('TEXT_GREETING_GUEST', 'Welcome <span class="greetUser">Guest</span>! Would you like to <a href="%s">log yourself in</a>? Or would you prefer to <a href="%s">create an account</a>?');
define('TEXT_YOU_ARE_HERE', '<b>You are here:</b>');
define('TEXT_HOME', 'Home');
define('TEXT_GAME', 'Game');
define('TEXT_SERVER', 'Server');
define('TEXT_NORMAL', 'Normal');
define('TEXT_GENERAL', 'Regular');
define('TEXT_FULL', 'Full');
define('TEXT_QUANTITY', 'Qty');
define('TEXT_VIEW', 'View');
define('TEXT_REFRESH', 'Refresh');
define('TEXT_REDEEM', 'Redeem');

define('TEXT_LIST_CLOSE', 'Close');
define('TEXT_ENTER', 'Enter');
define('TEXT_IS_LOADING', 'Loading... Please Wait.');
define('TEXT_SEARCH', 'Search');

//Common fields
define('TEXT_SERVER', 'Server');
define('TEXT_GAME', 'Game');
define('TEXT_QUANTITY', 'Qty'); //units
define('TEXT_AMOUNT', 'Amount'); //$$$
define('TEXT_AMOUNT_WITH_CURRENCY', 'Amount'); //$$$
define('TEXT_STATUS', 'Status');
define('TEXT_ACTION', 'Action');
define('TEXT_ORDER_NO', 'Order #');
define('TEXT_ORDER_STATUS', 'Order Status');
define('TEXT_PAYMENT_STATUS', 'Payment Status');
define('TEXT_START_DATE', 'Start Date');
define('TEXT_END_DATE', 'End Date');
define('TEXT_LOADING_MESSAGE', 'Loading ...');
define('TEXT_CONFIRM_DELETE', 'Confirm to delete?');
define('TEXT_CHARACTER_NAME', 'Character Name: %s');
define('TEXT_CHAR_NAME', 'Character Name');
define('TEXT_CHARACTER_INGAME_TIME', 'Character in game: %s<br>after checkout');
define('TEXT_CHARACTER_INGAME_DURATION', 'Duration in game: %d Hour(s)');
define('TEXT_CHARACTER_ACCOUNT_NAME', 'Account Name: %s');
define('TEXT_CHARACTER_ACCOUNT_PASSWORD', 'Password: %s');
define('TEXT_CHARACTER_ACCOUNT_WOW', 'WOW Account Name: %s');
define('GAME_CURRENCY_TEMPLATE', 'Game Currency Store');
define('CD_KEY_TEMPLATE', 'Game Card Store');
define('PWL_TEMPLATE', 'Power Leveling Store');
define('HIGH_LEVEL_ACCOUNT_TEMPLATE', 'High Level Account Store');
define('TEXT_NO_RESULTS', 'No results found');

define('URL_GAME_CURRENCY_TEMPLATE', 'game_currency_store');
define('URL_CD_KEY_TEMPLATE', 'game_card_store');
define('URL_PWL_TEMPLATE', 'power_leveling_store');
define('URL_HIGH_LEVEL_ACCOUNT_TEMPLATE', 'high_level_account_store');

define('GAME_CURRENCY_TEXT', 'in-game money used for purchase any items or skill set...');
define('CD_KEY_TEXT', 'CD Key & Game Points top up online...');
define('PWL_TEXT', 'in-game skill set on different level used on PvP server...');

define('LINK_GAME_CURRENCY', 'Click here for in-game currency packages.');
define('LINK_CD_KEY', 'Click here for CD Keys, Pre-Paid Cards, Game Points, etcs.');
define('LINK_PWL', 'Click here for Items and Power Leveling Services.');
define('SHARE_THIS_TITLE', 'Share This');
define('LINK_SHARE_THIS_PAGE', 'Share this page');
define('LINK_SHARE_THIS_PRODUCT', 'Share this product');
define('LINK_REDIRECT_MSG', 'Click here is your browser doesn\'t not automatically redirect you to the next page.');
define('LINK_MORE_PRODUCT_INFO', 'More Product info');
define('LINK_HIDE_PRODUCT_INFO', 'Hide Product info');
define('LINK_ALL_LANGUAGE', 'All Language');
define('LINK_ALL_PRODUCT_TYPE', 'All Product Type');
define('LINK_ALL_PLATFORM', 'All Platform');
define('LINK_ALL_GENRE', 'All Genre');

define('TEXT_SORT_PRODUCTS', 'Sort products ');
define('TEXT_DESCENDINGLY', 'descendingly');
define('TEXT_ASCENDINGLY', 'ascendingly');
define('TEXT_BY', ' by ');
define('TEXT_NOT_AVAILABLE', 'N/A');

define('TEXT_REVIEW_BY', 'by %s');
define('TEXT_REVIEW_WORD_COUNT', '%s words');
define('TEXT_REVIEW_RATING', 'Rating: %s [%s]');
define('TEXT_REVIEW_DATE_ADDED', 'Date Added: %s');
define('TEXT_NO_REVIEWS', 'There are currently no product reviews.');

define('TEXT_CLICK_TO_VERIFY', 'Click here to verify');
define('TEXT_EMAIL_NOT_VERIFY', 'Not Verified');
define('TEXT_EMAIL_VERIFIED', '(Verified) <span class="requiredInfo">*</span>');

define('TEXT_UNKNOWN_TAX_RATE', 'Unknown tax rate');
define('TEXT_PRODUCT_NOT_FOUND', 'This product is no longer available.');
define('TEXT_PRODUCT_NOT_FOUND_OPTIONS', '<div class="mediumFont"><b>Please try one of the following options:</b></div>
												<div>
													<ul style="font-weight:normal;font-size:12px;list-style-type:square;padding-left:25px;margin-top:5px;">
														<li><a href="%s">Browse the store</a> for other products & services.</li>
														<li>Go to <a href="%s">our homepage</a></li>
													</ul>
												</div>');

define('TEXT_FIELD_REQUIRED', '&nbsp;<span class="requiredInfo">* Required</span>');
define('TEXT_INSTANT_DELIVERY', 'In Stock');
define('TEXT_INSTANT', 'Instant');
define('TEXT_MEMORY_USAGE', 'Memory Usage');
define('TITLE_TRANS_BUYBACK_ORDER', 'Buyback Order %s');
define('TITLE_BUYBACK_PAYMENT_REPORT', 'Payment Report');

define('TEXT_ORDERS', "orders");
define('TEXT_PENDING', "pending");
define('TEXT_PROCESSING', "processing");

define('ERROR_TEP_MAIL', '<font face="Verdana, Arial" size="2" color="#ff0000"><b><small>TEP ERROR:</small> Cannot send the email through the specified SMTP server. Please check your php.ini setting and correct the SMTP server if necessary.</b></font>');
define('ERROR_PAYMENT_CURRENCY_NOT_SUPPORTED', 'Error: The Payment Gateway you have selected, %s, only supports %s. Please select a different currency that is supported or please select a different Payment Gateway.');
define('WARNING_INSTALL_DIRECTORY_EXISTS', 'Warning: Installation directory exists at: ' . dirname($HTTP_SERVER_VARS['SCRIPT_FILENAME']) . '/install. Please remove this directory for security reasons.');
define('WARNING_CONFIG_FILE_WRITEABLE', 'Warning: I am able to write to the configuration file: ' . dirname($HTTP_SERVER_VARS['SCRIPT_FILENAME']) . '/includes/configure.php. This is a potential security risk - please set the right user permissions on this file.');
define('WARNING_SESSION_DIRECTORY_NON_EXISTENT', 'Warning: The sessions directory does not exist: ' . tep_session_save_path() . '. Sessions will not work until this directory is created.');
define('WARNING_SESSION_DIRECTORY_NOT_WRITEABLE', 'Warning: I am not able to write to the sessions directory: ' . tep_session_save_path() . '. Sessions will not work until the right user permissions are set.');
define('WARNING_SESSION_AUTO_START', 'Warning: session.auto_start is enabled - please disable this php feature in php.ini and restart the web server.');
define('WARNING_DOWNLOAD_DIRECTORY_NON_EXISTENT', 'Warning: The downloadable products directory does not exist: ' . DIR_FS_DOWNLOAD . '. Downloadable products will not work until this directory is valid.');
define('WARNING_PRODUCTS_LOW_QUANTITY', 'The order limit has been exceeded. ');
define('WARNING_PRODUCTS_SUGGESTED_QUANTITY', 'Suggested quantity is %d.');
define('WARNING_PRODUCTS_OVERWEIGHT', 'We are sorry! Your total weight for this product exceeds the maximum weight!');
define('WARNING_PRODUCTS_UNDERWEIGHT', 'We are sorry! Your total weight for this product less than the product total weight!<br>Please edit your selections to match the total weight exactly.');
define('WARNING_EMPTY_SELECTIONS', 'Please select at least one item!');
define('WARNING_ORDER_AMOUNT_ZERO', 'Checkout failed! Your shopping cart products has no price value.');
define('SUCCESS_DYNAMIC_CART_ADDED', 'This product has been successfully added to cart! <a href="%s">Click here to view cart.</a>');
define('SUCCESS_DYNAMIC_CART_UPDATED', 'This product\'s shopping cart has been successfully updated! <a href="%s">Click here to view cart.</a>');

define('TEXT_CCVAL_ERROR_INVALID_DATE', 'The expiry date entered for the credit card is invalid.<br>Please check the date and try again.');
define('TEXT_CCVAL_ERROR_INVALID_NUMBER', 'The credit card number entered is invalid.<br>Please check the number and try again.');
define('TEXT_CCVAL_ERROR_UNKNOWN_CARD', 'The first four digits of the number entered are: %s<br>If that number is correct, we do not accept that type of credit card.<br>If it is wrong, please try again.');

define('TEXT_ERROR_ACTIVATE_ACCOUNT', 'Please activate your account in order to checkout.');
define('TEXT_STOCK_NOT_AVAILABLE', 'Quantity requested not available. To buy in bulk, kindly contact <a href="mailto:<EMAIL>"><EMAIL></a>');
define('TEXT_CUSTOMER_VERIFIED_PAYMENT_EMAIL_NOTICE_SUBJECT', 'Customer Payment E-mail Address Verified Notification');
define('TEXT_CUSTOMER_NAME', 'Customer Name: ');
define('TEXT_CUSTOMER_PAYMENT_EMAIL', 'Payment E-mail Address: ');
define('TEXT_NEWSLETTER_MSG', "<div style=\"font-family: Arial, Verdana, sans-serif; font-weight: bold; font-size: 12px; color: green;\">Important Note:</div>" . '<span class="latestNewsBoxContents">To ensure receipt of our emails, please add <a href="mailto:<EMAIL>"><EMAIL></a> to your \'Whitelist\' or \'Address Book\'. <a href="http://www.offgamers.com/user-guide-whitelist-emails-i-491.ogm" target="_blank">Click here to learn how</a>.</span>');

define('EMAIL_TEXT_AFFILIATE_NOTIFICATION_SUBJECT', 'Affiliate Sales #%d (Order Amount: %s)');
define('EMAIL_TEXT_AFFILIATE_NOTIFICATION_INFO', 'There is sales referred by you as below:' . "\n\n" . 'Order Date: %s' . "\n" . 'Order ID: %s' . "\n" . 'Order Amount: %s' . "\n");

define('LOG_SALES_ORDER', '##%d##: ##%d## ##%d##');

define('LOG_SC_STAT_SC_CONVERT', 'Store Credit Currency Conversion');
define('LOG_SC_ACTIVITY_TYPE_PURCHASE', 'P');
define('LOG_SC_ACTIVITY_TYPE_CANCEL', 'X');
define('LOG_SC_ACTIVITY_TYPE_CONVERT', 'V');

//payment pop up menu
define('POP_PAYMENT_REPORT_PAYMENT_MESSAGE', 'Payment Message');
define('POP_PAYMENT_REPORT_DATETIME', 'Date/Time');

// Definitions for Transaction Status Update Notification
define('EMAIL_CUSTOMER_ORDER_UPDATE_NOTIFICATION_SUBJECT', 'Customer Order Update Notification #%d');
define('EMAIL_TRANS_UPDATE_NOTIFICATION_CONTENT', 'Order ID: %s' . "\n" . 'Order Date: %s' . "\n" . 'Order Amount: %s' . "\n" . 'Payment Method: %s' . "\n\n" . 'Update Type: %s' . "\n" . 'Status Update: %s -> %s' . "\n" . 'Update Date: %s' . "\n" . 'Update IP: %s' . "\n" . 'Update User: %s' . "\n\n" . 'Update Comment:' . "\n" . '%s');
define('EMAIL_TEXT_TRANS_UPDATE_MANUAL_NOTIFICATION', 'Manual');
define('EMAIL_TEXT_TRANS_UPDATE_AUTO_NOTIFICATION', 'Automatic');

// Polling
define('BUTTON_VIEW_RESULT', 'Result');
define('BUTTON_VOTE', 'Vote');

// File upload message
define('ERROR_NO_UPLOAD_FILE', 'Error: Source file does not exist.');
define('ERROR_NO_TMP_DIR', 'Error: Missing a temporary folder.');
define('ERROR_DESTINATION_DOES_NOT_EXIST', 'Error: Destination does not exist.');
define('ERROR_DESTINATION_NOT_WRITEABLE', 'Error: Destination not writeable.');
define('ERROR_FILESIZE_EXCEED', 'Error: File size has been exceeded the limit allowed.');
define('ERROR_UPLOAD_PARTIAL', 'Error: The uploaded file was only partially uploaded.');
define('ERROR_FILE_NOT_SAVED', 'Error: File upload not saved.');
define('ERROR_FILETYPE_NOT_ALLOWED', 'Error: File upload type not allowed.');

define('SUCCESS_FILE_SAVED_SUCCESSFULLY', 'Success: File upload saved successfully.');
define('WARNING_NO_FILE_UPLOADED', 'Warning: No file uploaded.');
define('WARNING_MUST_BE_VIP_MEMBER', 'Warning: Only VIP in order to have this feature.');

define('WARNING_FILE_UPLOADS_DISABLED', 'Warning: File uploads are disabled in the php.ini configuration file.');
define('WARNING_NOTHING_TO_EXPORT', 'Warning: There is no any data to be exported.');
define('WARNING_NOTHING_TO_IMPORT', 'Warning: There is no any data to be imported.');

define('ERROR_PAGE_ACCESS_DENIED', 'Error: You do not have permission to access this page.');

// Email verification
define('EMAIL_SUBJECT_2', 'E-mail Address Verification');
define('EMAIL_VERIFY_CONTENT', 'Recently, you have registered ' . STORE_OWNER . ' using this email address. To complete your registration, follow the link below:' . "\n");
define('EMAIL_VERIFY_CONTENT_ADDRESS_INFO', '(If clicking on the link doesn\'t work, try copying and pasting it into your browser.)' . "\n\n");
define('EMAIL_MANUAL_ACTIVATE_EMAIL_2', 'You may also verify your e-mail address manually at <a href="' . tep_href_link(FILENAME_ACCOUNT_ACTIVATE, 'verify=email&auto_mail=yes', 'SSL') . '">' . tep_href_link(FILENAME_ACCOUNT_ACTIVATE, 'verify=email&auto_mail=yes', 'SSL') . '</a> by entering the following information.' . "\n\n" . 'E-mail Address: ');
define('EMAIL_MANUAL_ACTIVATE_CODE_2', "\n" . 'Verification Code: ');
define('EMAIL_CONTACT', 'For any enquiries or assistance, please use our Online Live Support service or e-mail your enquiries to ' . EMAIL_TO . '. Thank you for shopping at ' . STORE_NAME . ".\n\n\n");
define('EMAIL_FOOTER', STORE_EMAIL_SIGNATURE);

//customer order comment remark
define('TEXT_VIP_BUYBACK_ORDER_REFERENCE_REMARK', 'Delivered via VIP Order: ##BO##%d##');
define('TEXT_BUYBACK_ORDER_REFERENCE_REMARK', 'Delivered via Buyback Order: ##BO##%d##');

define('TEXT_BUYBACK_ORDER_ITEMS_DELIVERED_REMARK', 'The following items have been delivered:');
define('TEXT_BUYBACK_ORDER_ITEMS_DEDUCTED_REMARK', 'The following items have been deducted:');

define('TEXT_FRAUD_DISCLAIMER', '<br>
		<table style="border-right: 2px dashed #ff0000; padding-right: 10px; border-top: 2px dashed #ff0000; padding-left: 10px; padding-bottom: 15px; margin: 0px 0px 15px; border-left: 2px dashed #ff0000; padding-top: 10px; border-bottom: 2px dashed #ff0000; background-color: #fffef2;">
        	<tr>
				<td>
					<div style="font-family: Arial, Verdana, sans-serif; font-weight: 700; font-size: 16px; color: #ff0000; border-bottom: 1px dashed #ff0000; text-align: left">Attn : Fraudsters and scammers</div>
					<BR><span class="latestNewsBoxContents">
					PLEASE BE FOREWARNED THAT CREDIT CARD FRAUD IS A FEDERAL CRIME THAT IS
					INVESTIGATED BY THE <a href="http://www.fbi.gov/cyberinvest/cyberhome.htm" target="_blank">FBI</a> (FEDERAL BUREAU OF INVESTIGATION), <a href="http://www.secretservice.gov/financial_crimes.shtml" target="_blank">UNITED
					STATES SECRET SERVICE</a> AND THEIR PARTNERS INCLUDING <a href="http://www.interpol.int/Public/FinancialCrime/default.asp" target="_blank">INTERPOL
					INTERNATIONAL</a>.  BY MANDATE OF LAW, WE ARE REQUIRED TO REPORT SUCH
					ABUSES AND WE WILL HAPPILY COMPLY WITH THAT MANDATE. <br>
					<br>
					OFFGAMERS WILL SUPPLY ALL INFORMATION INCLUDING *IP ADDRESSES, M.A.C
					ADDRESSES, VOIP REGISTERED PHONE NUMBERS, REVERSE LOOKUP INFORMATION,
					RECORDED CONVERSATIONS, *COMPILED FROM FRAUDULENT ORDERS TO OFFICIALS
					AND LOCAL AUTHORITIES WHO WILL THEN PURSUE INVESTIGATION AND PROSECUTION
					OF THE CRIMINAL ACTION TAKEN BY THE OFFENDING PARTIES.  ONCE THE
					CRIMINAL CASE HAS BEEN COMPLETED, A SEPARATE CIVIL CASE IS THEN
					SUBMITTED AGAINST THE OFFENDING PARTY TO RECOVER ANY AND ALL DAMAGES
					(FINANCIAL OR OTHERWISE) THAT IS INCURRED BY OFFGAMERS. <br>
					<br>
					PLEASE BE AWARE THAT OFFGAMERS HAS DETECTED AND <a href="http://www.ic3.gov/" target="_blank">REPORTED</a> MANY ATTEMPTS
					TO CHEAT, SCAM, FRAUDULENTLY STEAL AN IDENTITY, INTRUSIVE AND
					UNAUTHORIZED ENTRY INTO OUR SERVERS.  LET THIS BE A WARNING TO ALL THAT
					WE TAKE THE SECURITY OF OUR ORDERING SYSTEM VERY SERIOUSLY AND ANY
					ATTEMPT TO FRAUDULENTLY PLACE AN ORDER WHETHER BY IMPERSONATION,
					FORGERY, ALTERATION, FALSE CLAIMS, FRAUDULENTLY USE A CREDIT CARD, OR
					RELATED ACTIVITY IN CONNECTION WITH IDENTIFICATION DOCUMENTS AND
					FRAUDULENT COMMERCIAL, FICTITIOUS INSTRUMENTS WILL BE REPORTED IMMEDIATELY. <br>
					<br>
					THE SECRET SERVICE IS THE PRIMARY FEDERAL AGENCY TASKED WITH
					INVESTIGATING ACCESS DEVICE FRAUD AND ITS RELATED ACTIVITIES UNDER TITLE
					18, UNITED STATES CODE, SECTION 1029.  DO NOT  BE FOOLED TO THINK THAT
					IT IS SAFE TO OPERATE BEHIND A SPOOFED IP ADDRESS AND A VOIP
					PHONE-LINE.  THE <a href="http://www.fbi.gov/contact/legat/legat.htm" target="_blank">FBI</a>, <a href="http://www.secretservice.gov/field_offices.shtml#over" target="_blank">US SECRET SERVICE</a>, AND <a href="http://www.interpol.int/Public/ICPO/Members/default.asp" target="_blank">INTERPOL</a>; OPERATES
					FROM OFFICES AROUND THE WORLD AND WILL WORK WITH <a href="http://www.fbi.gov/page2/march06/cats030606.htm" target="_blank">LOCAL AUTHORITIES</a> TO
					ENSURE THE ARREST OF OFFENDING INDIVIDUAL(S).  IT IS IN OUR OPINION THAT
					A DECADE BEHIND BARS IS JUST NOT WORTH TO STEAL FROM OFFGAMERS.
					</span>
				</td>
			</tr>
		</table>');

define('TEXT_GOLD_DELIVERY_NOTES', '');

define('TEXT_PAYMENT_VERIFICATION_NOTES', '');

define('TEXT_PAYMENT_GATEWAY', 'Payment Gateway');
define('TEXT_ITEMS_IN_MY_CART', '<a href="%s" style="float:none;padding:0;">%s</a> item(s) in cart');
define('TEXT_ITEM_IN_MY_CART', 'item(s) in cart');
define('TEXT_ITEM_IN_MY_CART_FOR_FOOTER', 'Cart (%s)');
define('LINK_VIEW_ALL_PAYMENT_GATEWAY', 'View all payment gateway');
define('TEXT_TYPE_TO_FILTER_PRODUCTS', 'Type to Filter Products');
define('TEXT_SEARCH_WHOLE_STORE', 'Search Whole Store');
define('TEXT_SUBSCRIBE_NEWSLETTER', 'Subscribe Now');
define('TEXT_SUBSCRIBE', 'Subscribe');
define('TEXT_VIEW_ALL_TESTIMONIALS', 'View All Testimonials');
define('TEXT_MAIN_PLEASE_SELECT_GAME', '<---Please select game');
define('TEXT_BACK_TO_TOP', 'Back to Top');
define('TEXT_VIEW_MY_CART', 'VIEW MY CART');
define('TEXT_SWITCH_TO_MOBILE', 'Click here switch to mobile version');
define('TEXT_GAME_NOT_SUPPORT', 'Your country does not support this game');
define('TEXT_GAME_NOT_SUPPORT_DESC', 'Due to game publisher restrictions, you are not allowed to access this game for the country you have chosen.');

define('TEXT_COPYRIGHT_INFORMATION', 'Copyright &copy;' . date('Y') . ' OffGamers Global Pte Ltd. All rights reserved. &nbsp&nbsp <a href="' . tep_href_link('terms-of-service-i-5.ogm') . '">Terms & Conditions</a> &nbsp|&nbsp <a href="' . tep_href_link('/privacy-policy-i-4.ogm') . '">Privacy Policy</a>');
define('TAB_LATEST_UPDATE', 'LATEST UPDATE :');

// New Layout
define('TEXT_CURRENCY', 'Currency');
define('TEXT_MUST_LOGIN', 'Login required to access this section.');
define('TOOLTIPS_MEMBER_ID', 'OffGamers ID');
define('TOOLTIPS_MEMBER_STATUS', 'Member Status');
define('TOOLTIPS_STORE_CREDIT_BALANCE', 'Store Credit Balance');
define('TOOLTIPS_OFFGAMERS_POINTS', 'OPs are rebate points given upon completion of your purchase. It can be redeemed for Store Credit.<br/><br/><a href=\'http://kb.offgamers.com/en/category/my-account/wor-token/\' target=\'_blank\'>Read more</a>');
define('TOOLTIPS_WITHDRAWABLE_BALANCE', 'Seller Credits');
define('TEXT_LOGIN', 'Login');
define('TEXT_LOGOUT', 'Logout');
define('TEXT_TRACK_BUYBACK_ORDER', 'Track Buy Orders');
define('TEXT_TRACK_SELLING_ORDER', 'Track Sell Orders');
define('TEXT_REGISTER', 'Register');
define('TEXT_GREETING_GUEST_NEW', '<font style="color:#92CFF3;">You are not logged in. <a id="fancy_login_box" href="javascript:void(0);" class="whiteText"><b>Login</b></a> now or <a href="' . tep_href_link(FILENAME_LOGIN, '', 'SSL') . '" class="whiteText"><b>register</b></a>.</font>');
define('TEXT_OGM_SERVICE_GUARANTEE', '<font style="font-weight:bold;font-size:12px;">OGM SERVICE GUARANTEE</font><br><font class="smallestFont">for CD Key / Game Points</font><br><br><b>Money Back Guarantee</b><br>Should you not satisfied with the progress of your....<br><br><b>Safety Account</b><br>Should you be not satisfied with the');
define('TEXT_JOIN_AFFILIATE_PROGRAM', 'Click to learn more...');
define('TEXT_VIEW_PAYMENT_GATEWAY', 'View All Payment Methods'); //define('TEXT_VIEW_PAYMENT_GATEWAY', 'View All Payment Gateways');
define('TEXT_SERVICE_GURANTEED_ICON', 'icon_serviceguarantee_en.gif');
define('TEXT_MORE_DETAILS', 'More Details');
define('TEXT_COOKIE_NOTICE_DESC', 'OffGamers uses cookies to optimize your online experience on our website. By continuing to use our website for your gaming needs,<br>you are consenting to use such cookies.');

define('BUTTON_PROCEED_TO_CHECKOUT', 'Checkout');
define('BUTTON_TOP_GAME_SELECTION', 'BROWSE BY GAMES');
define('BUTTON_VIEW_ALL_GAMES', 'View All Games');
define('BUTTON_MORE_GAMES', 'More Games');

define('HEADER_PRODUCT_SELECTION', 'BROWSE BY STORE');
define('HEADER_GAMES_SELECTION', 'GAMES SELECTION');
define('HEADER_CUSTOMER_TESTIMONIAL', 'CUSTOMER TESTIMONIAL');
define('HEADER_CONFIRM_BILLING_INFO', 'PLEASE CONFIRM/COMPLETE YOUR BILLING INFO');

define('HEADER_RECOMMENDED_PAYMENT_GATEWAY', 'PAYMENT METHODS'); //define('HEADER_RECOMMENDED_PAYMENT_GATEWAY', 'FEATURED PAYMENTS');
define('HEADER_NEWSLETTER', 'NEWSLETTER');
define('HEADER_SERVICES', 'SERVICES');
define('HEADER_EXPANDED_INFORMATION', 'EXTRA NOTE FOR CUSTOMERS:');
define('BOX_HEADER_AVAILABLE_PRODUCT', 'PRODUCTS');
define('TEXT_SELLER_INFO_DESC', 'OffGamers aims to provide a safe and secure trading environment for enterprising individuals to sell their excess ingame currency.
									<br><br>To promote a safe, fair and competitive gaming environment, we do not condone the use of bots, hacks, illegal macros, third party programs, illegal advertising and unethical behaviour in game. Any sellers suspected of any of the mentioned acts will be blacklisted and payments confiscated as a form of penalty.
									<br><br>OffGamers has been connecting buyers and sellers from all over the world, we assure you that your personal particulars are kept strictly confidential all the while providing the best service platform for traders to conduct their business.
									<br><br><div class="dottedLine"><!-- --></div><br>
									<h2>Notices</h2><br>
									&#8250; <h3>Registration Notice:</h3> Please ensure that all contact information (e.g. Mobile no., QQ, MSN, etc.) provided is correct and that you remember your \'Secret Question and Answer\' at all times to ensure a smooth processing of your transactions. All these information will be used as a verification while you edit your profile or perform withdrawals.<br><br>
									&#8250; <h3>Trade Screenshots:</h3> Please be informed that all sellers are encourages to submit all trading screenshots within 1 hour after trade is completed. All orders with screenshots submitted will be handled with priority and completed immediately. Should there be no screenshots submitted within the hour, the order will be automatically put on hold for up to 5 days and the customer will be contacted to verify that trade is complete. Please ensure that all screenshots are submitted on time to avoid unnecessary delays to the completion of your order, thank you for your cooperation.<br><br>
									&#8250; <h3>Order Expiry:</h3> Please take note that all orders will expire within 1 hour. Should you be unable to establish trade with our customers within that time frame, please do contact our live support immediately. OffGamers will not be liable for any trade done outside the 1 hour time-frame of each order.<br><br>
									&#8250; <a href="http://kb.offgamers.com/?p=252">Sell Your Game Currency Procedure</a>
									<br>&#8250; <a href="http://kb.offgamers.com/?p=310">WOW - Seller Guide</a>
									<br>&#8250; <a href="http://kb.offgamers.com/?p=269">Seller Guide</a>
									<br>&#8250; <a href="/sell-gold-service-i-301.ogm">Seller Terms & Conditions</a>
									<br>&#8250; <a href="http://kb.offgamers.com/?p=374">Store Credit / Withdrawable Balance Guide</a>');

define('BOX_EVENT_AND_PROMOTION', 'Events & Promotions');
define('BOX_GAME_GUIDES', 'Game Guides');
define('BOX_HEADING_ITEM_ADDED_TO_SHOPPING_CART', 'Item Added to Shopping Cart');
define('BOX_STORE_CREDIT', 'Store Credits');
define('HTML_AFFILIATE_BOX', '<div style="width:100%;text-align:left;"><b><span style="font-size:16px;">Make Money With Us</span></b><br>Big commissions and easy setup.</div>');

define('HTML_NEWSLETTER_BOX', 'Subscribe to our newsletter for special offers, exclusives and promotions.');
define('HEADER_DELIVERY_INFORMATION', 'Delivery Information');
define('HEADER_CHANGE_MOBILE', 'CHANGE MOBILE PHONE NUMBER');
define('HEADER_STORE_CREDIT_USE', 'Store Credit');
define('TEXT_STORE_CREDIT_USE', '<p>In order to use the store credits for your current order, you need to check out in the currency that matches that of your store credits.</p><p>Would you like to change the currency of your shopping cart to match that of your store credits?</p>');
//    define('TEXT_PAY_WITH_STORE_CREDIT_HEADER', 'Pay with Store Credit currency');
//    define('TEXT_PAY_WITH_STORE_CREDIT_DESC', 'Current checkout page currency will change according to your current available store credits currency');
//    define('TEXT_PAY_WITH_EXISTING_CURRENCY_HEADER', 'Pay with existing currency');
//    define('TEXT_PAY_WITH_EXISTING_CURRENCY_DESC', 'Your Store Credit currency will be converted. Your will be re-directed to the Store Credits Conversion page.');

define('TEXT_STORE_CREDIT_CONVERT', '<p>In order to use the store credits for your current order, you need to convert your store credits to the currency that matches that of your current order.</p><p>Current store credits balance: %s<br/>After conversion: %s<br/>Conversion rate: %s (updated daily)<p>Would you like to change the currency of your current store credit balance to match that of your current order?</p>');
define('TEXT_STORE_CREDIT_CONVERT_IN_MY_ACCOUNT', '<p>In order to use your store credits for this current order, the currency of your store credits and shopping cart must match.</p><p>Would you like to change the currency of your store credits to match that of your shopping cart to continue?</p>');
define('TEXT_STORE_CREDIT_POPUP_DESCRIPTION', 'There seems to be a difference between the avalable Store Credit of yours with the site currency you had picked previously. <b>Would you like to:-</b>');
define('TEXT_STORE_CREDIT_POPUP_CHANGE_WEBSITE_CURRENCY', 'Change website currency to match my Store Credits currency(%s)');
define('TEXT_STORE_CREDIT_POPUP_CHANGE_STORE_CREDIT_CURRENCY', 'Change my Store Credits currency to match website currency(%s)');
define('TEXT_STORE_CREDIT_POPUP_CONTINUE_WITH_NO_CHANGE', 'Store Credits will not be used for this purchase.');
define('TEXT_LOGIN_PASSWORD', 'LOGIN PASSWORD');

define('LINK_CHANGE_DELIVERY_INFO', 'Change delivery info');
define('TEXT_TRADE_MODE', 'Trade Mode:  %s');
define('TEXT_TRADE_METHOD', 'Trade Method:  <b>%s</b>');
define('OPTION_FACE_TO_FACE', 'Face to face trade');
define('OPTION_PUT_IN_MY_ACCOUNT', 'Put into my account');
define('OPTION_BY_MAIL', 'Mail');
define('OPTION_OTHERS', 'Others');
define('OPTION_OPEN_STORE', 'Open Store');

define('BUYBACK_SUPPLIER_MODE_F2F', 'Face to face');
define('BUYBACK_SUPPLIER_MODE_MAIL', 'Mail');
define('BUYBACK_SUPPLIER_MODE_PIMA', 'Put into my account');
define('BUYBACK_SUPPLIER_MODE_T2O', 'Trade with OffGamers');
define('BUYBACK_SUPPLIER_MODE_T2C', 'Trade with buyer');
define('BUYBACK_SUPPLIER_MODE_OPEN_STORE', 'Open Store');

define('HEADER_TOTAL', 'Total: ');
define('TEXT_NUMERIC', 'NUMERIC');
define('BREADCRUMB_BUY', 'Buy');
define('BREADCRUMB_SELL', 'Sell');

define('TEXT_PROMOTION_STATUS', 'Status');
define('TEXT_PROMOTION_PRODUCTS', 'PROMOTION PRODUCTS');
define('TEXT_RETYPE', 'Retype ');
define('TEXT_SOFTPIN', 'SOFTPIN');
define('TEXT_STATUS_LIMITED_STOCK', 'Limited Stock');
define('TEXT_STATUS_FAST_SELLING', 'Fast Selling');
define('TEXT_STATUS_PRICE_SLASH', 'Price Slash');

// Locking / Unlocking log tag
if (!defined('ORDERS_LOG_LOCK_ORDER'))
    define('ORDERS_LOG_LOCK_ORDER', '##l_1##');
if (!defined('ORDERS_LOG_UNLOCK_OWN_ORDER'))
    define('ORDERS_LOG_UNLOCK_OWN_ORDER', '##ul_1##');
if (!defined('ORDERS_LOG_UNLOCK_OTHER_PEOPLE_ORDER'))
    define('ORDERS_LOG_UNLOCK_OTHER_PEOPLE_ORDER', '##ul_2##%s:~:%s');

define('LOG_PARTIAL_DELIVERY_SALES', '##%d##: Partial Delivery Sales');
define('LOG_CDKEY_ID_STR', 'CD Key ID: %s');
define('LOG_BUYBACK_ORDER', '##BUYBACK:%d##');
define('LOG_QTY_ADJUST', "Quantity Adjustment \n%s");

define('LIVE_SUPPORT', '');
define('B2C_SELLING_LIVE_SUPPORT', "<script type=\"text/javascript\">var __lc={};__lc.license=1306592;__lc.group = 7;__lc.params=[{name: 'identifier ', value: 'OGM_CUSTOMER_NAME' },{name: 'discountGroup', value: 'OGM_CUSTOMER_GROUP' },{name: 'customerEmail', value: 'OGM_CUSTOMER_EMAIL' }];(function(){var lc=document.createElement('script');lc.type='text/javascript';lc.async=true;lc.src=('https:'==document.location.protocol ? 'https://' : 'http://') + 'cdn.livechatinc.com/tracking.js';var s=document.getElementsByTagName('script')[0];s.parentNode.insertBefore(lc, s);})();</script><style type=\"text/css\">#livechat-compact-container,#livechat-full{z-index: 565 !important;}#livechat-eye-catcher{bottom: 69px !important;z-index: 565 !important;}</style>");

require(DIR_WS_LANGUAGES . 'add_ccgvdc_english.php');

// Captcha
define('ERROR_INVALID_CODE', 'Sorry, the code you entered was invalid.');
define('TEXT_CAPTCHA_ANSWER', 'Ans.');
define('TEXT_REFRESH_CAPTCHA', 'If you can\'t read this, <a href="javascript:void(0);" onClick="get_captcha_img(\'%s\');">try another one</a>');
define('TEXT_REFRESH_CAPTCHA_VIP', 'If you can\\\'t read this, <a href="javascript:void(0);" onClick="get_captcha_img(\\\'%s\\\');">try another one</a>');
define('TEXT_CAPTCHA_INSTRUCTION', 'For negative value, enter e.g.(-8)');
define('NOTE_DO_NOT_RETURN_GOODS', '<b>Important:</b> Legit sellers will never ask you to return the game currencies after the trade is complete. Do not return to anyone claiming to be our sellers or even to the same character ID that traded with you.');

// Facebook Connect
define('ERROR_INVALID_FB_UID', 'Invalid facebook user ID.');
define('SUCCESS_FB_DISCONNECTED', '<h3><font color=red>Your facebook account have been disconnected</font></h3>');
define('HEADER_FB_CONNECT_SELECTION', 'Connect with Facebook');
define('TEXT_FB_CONNECT_SELECTION', 'Hi %s,<br>Do you have an existing OffGamers Account?');
define('OPTION_FB_CONNECT_SELECTION_FIRST', '<b>No, I\'m new to OffGamers!</b><br>Create a new OffGamers account!');
define('OPTION_FB_CONNECT_SELECTION_SECOND', '<b>Yes I do have a OffGamers account!</b><br>Link my Facebook and OffGamers account!');

// HLA
define('TEXT_ADVANCED_SEARCH_OPTIONS', 'Advanced Search Options');

define('TEXT_HLA_ANY', 'Any');

define('TABLE_HLA_HEADING_LEVEL', 'LEVEL');
define('TABLE_HLA_HEADING_RACE', 'RACE');
define('TABLE_HLA_HEADING_CLASS', 'CLASS');
define('TABLE_HLA_HEADING_REFERRENCE', 'REF.');
define('TABLE_HLA_HEADING_PRICE', 'PRICE');
define('TABLE_HLA_HEADING_ACTION', 'ACTION');

define('TEXT_HLA_VIEW_PROFILE', '&#8250;&nbsp;View profile');
define('TEXT_HLA_ALTERNATE_CHARACTERS', 'Alternate Characters');

define('EMAIL_HLA_NEW_BUYBACK_ORDER_NUMBER', 'Buyback Order Number: %s');
define('EMAIL_HLA_NEW_BUYBACK_DATE_ORDERED', 'Buyback Order Date: %s');
define('EMAIL_HLA_NEW_BUYBACK_CUSTOMER_EMAIL', 'E-mail Address: %s');
define('EMAIL_HLA_NEW_BUYBACK_STATUS', 'Status: %s');
define('EMAIL_HLA_NEW_BUYBACK_SUBJECT', implode(' ', array(EMAIL_SUBJECT_PREFIX, "New Buyback Order #%s")));
define('EMAIL_HLA_NEW_BUYBACK_BODY', "Thank you for selling your items to " . STORE_NAME . ".\n\n Supplier Order Summary:\n" . EMAIL_SEPARATOR . "\n");
define('EMAIL_HLA_NEW_BUYBACK_PRODUCTS', "\n\n" . 'Products');
define('EMAIL_HLA_NEW_BUYBACK_ORDER_TOTAL', 'Total: %s');
define('EMAIL_HLA_NEW_BUYBACK_COMMENTS', 'Extra Information:');
define('EMAIL_HLA_NEW_BUYBACK_ORDER_CLOSING', "Please contact us via our Online Live Support service or e-<NAME_EMAIL> if you face an issue with the buyback. Please remember to include your Buyback Order Number when contacting us to expedite the process.\n\nYou will receive an e-mail notification whenever there is an update in your buyback order status. You can also review your buyback orders and check their status by signing in to OffGamers.com and clicking on \"View Buyback Order History\".\n\nThank you for supporting " . STORE_NAME . '.');

define('EMAIL_HLA_NEW_BUYBACK_ORDER_FOOTER', "\n\n" . EMAIL_FOOTER);

define('EMAIL_HLA_BUYBACK_ORDER_GUIDE', '
	<b>Order Flow : [Pending] --> [Processing] --> [Completed] or [Canceled]</b>

	[Pending] : Order submission incomplete. Kindly login to OffGamers.com and submit the missing character information.
	[Processing] : This delivery is being registered into our system.
	[Completed] : This delivery has been registered completely.
	[Canceled] : The order has been canceled.

	<b>Note 1:</b> Please ensure the Account information you provided is 100% accurate.

	<b>Note 2:</b> Once the Account information been submitted, please do not login to the account, either via the account management page or ingame.

	Important: Please take note that Blizzard may lock the account if they detect a different IP accessing the account within a short period of time. As a precaution:

	<b>Note 3:</b> Once your order is in the [Completed] status, payment will be made available to you within 15 minutes at your OffGamers Account. Use the [Withdraw] function to withdraw your payment and have it credited to a payment method of your choosing.
	');

define('EMAIL_HLA_SUPPLIER_SUBMITTED_INFO', '
	<b>Order Flow : [Pending] --> [Processing] --> [Completed] or [Canceled]</b>

	Submitted information
	');

define('TEXT_HLA_SEARCH_NO_RECORDS', 'Sorry, there are no characters / accounts available according to your selection. <br />Please modify your requirements and search again.');

define('HLA_REGION', 'Region');
define('HLA_RACE', 'Race');
define('HLA_CLASS', 'Class');
define('HLA_SIDE', 'Faction');
define('HLA_LEVEL', 'Level');
define('HLA_GENDER', 'Gender');
define('HLA_PRICE', 'Price');
define('HLA_TALENT', 'Talent');
define('HLA_REFERENCE_ID', 'Reference ID');
define('HLA_SERVER', 'Server');

// WOW : Race
define('HLA_RACE_BLOOD_ELF', 'Blood Elf');
define('HLA_RACE_DRAENEI', 'Draenei');
define('HLA_RACE_DWARF', 'Dwarf');
define('HLA_RACE_GNOME', 'Gnome');
define('HLA_RACE_GOBLIN', 'Goblin');
define('HLA_RACE_HUMAN', 'Human');
define('HLA_RACE_NIGHT_ELF', 'Night Elf');
define('HLA_RACE_ORC', 'Orc');
define('HLA_RACE_PANDAREN', 'Pandaren');
define('HLA_RACE_TAUREN', 'Tauren');
define('HLA_RACE_TROLL', 'Troll');
define('HLA_RACE_UNDEAD', 'Undead');
define('HLA_RACE_WORGEN', 'Worgen');

// Warhammer : Race
define('HLA_RACE_CHAOS', 'Chaos');
define('HLA_RACE_DARK_ELF', 'Dark Elf');
define('HLA_RACE_WARHAMMER_DWARF', 'Dwarf');
define('HLA_RACE_EMPIRE', 'Empire');
define('HLA_RACE_GREENSKIN', 'Greenskin');
define('HLA_RACE_HIGH_ELF', 'High Elf');

// Age Of Conan : Race
define('HLA_RACE_AQUILONIAN', 'Aquilonian');
define('HLA_RACE_CIMMERIAN', 'Cimmerian');
define('HLA_RACE_STYGIAN', 'Stygian');

// AION : Race
define('HLA_RACE_ASMODIAN', 'Asmodian');
define('HLA_RACE_ELYOS', 'Elyos');

// RIFT : Race
define('HLA_RACE_DEFIANT', 'Defiant');
define('HLA_RACE_GUARDIAN', 'Guardian');

// WOW : Class
define('HLA_CLASS_DEATH_KNIGHT', 'Death Knight');
define('HLA_CLASS_DRUID', 'Druid');
define('HLA_CLASS_HUNTER', 'Hunter');
define('HLA_CLASS_MAGE', 'Mage');
define('HLA_CLASS_MONK', 'Monk');
define('HLA_CLASS_PALADIN', 'Paladin');
define('HLA_CLASS_PRIEST', 'Priest');
define('HLA_CLASS_ROGUE', 'Rogue');
define('HLA_CLASS_SHAMAN', 'Shaman');
define('HLA_CLASS_WARLOCK', 'Warlock');
define('HLA_CLASS_WARRIOR', 'Warrior');

// Warhammer : Class
define('HLA_CLASS_ARCHMAGE', 'Archmage');
define('HLA_CLASS_BLACK_ORC', 'Black Orc');
define('HLA_CLASS_BLAZING_SUN_KNIGHT', 'Blazing Sun Knight');
define('HLA_CLASS_BRIGHT_WIZARD', 'Bright Wizard');
define('HLA_CLASS_CHOPPA', 'Choppa');
define('HLA_CLASS_CHOSEN', 'Chosen');
define('HLA_CLASS_DARK_ELF_BLACK_GUARD', 'Dark Elf Black Guard');
define('HLA_CLASS_DISCIPLE_OF_KHAINE', 'Disciple of Khaine');
define('HLA_CLASS_ENGINEER', 'Engineer');
define('HLA_CLASS_IRONBREAKER', 'Ironbreaker');
define('HLA_CLASS_MAGUS', 'Magus');
define('HLA_CLASS_MARAUDER', 'Marauder');
define('HLA_CLASS_RUNE_PRIEST', 'Rune Priest');
define('HLA_CLASS_SHADOW_WARRIOR', 'Shadow Warrior');
define('HLA_CLASS_SLAYER', 'Slayer');
define('HLA_CLASS_SORCERESS', 'Sorceress');
define('HLA_CLASS_SQUIG_HERDER', 'Squig Herder');
define('HLA_CLASS_SWORDMASTER', 'Swordmaster');
define('HLA_CLASS_WARRIOR_PRIEST', 'Warrior Priest');
define('HLA_CLASS_WHITE_LION', 'White Lion');
define('HLA_CLASS_WITCH_ELF', 'Witch Elf');
define('HLA_CLASS_WITCH_HUNTER', 'Witch Hunter');
define('HLA_CLASS_ZEALOT', 'Zealot');

define('HLA_CLASS_WARHAMMER_SHAMAN', 'Shaman');

// Age Of Conan : Class
define('HLA_CLASS_ASSASSIN', 'Assassin');
define('HLA_CLASS_BARBARIAN', 'Barbarian');
define('HLA_CLASS_BEAR_SHAMAN', 'Bear Shaman');
define('HLA_CLASS_CONQUEROR', 'Conqueror');
define('HLA_CLASS_DARK_TEMPLAR', 'Dark Templar');
define('HLA_CLASS_DEMONOLOGIST', 'Demonologist');
define('HLA_CLASS_GUARDIAN', 'Guardian');
define('HLA_CLASS_HERALD_OF_XOTLI', 'Herald of Xotli');
define('HLA_CLASS_NECROMANCER', 'Necromancer');
define('HLA_CLASS_PRIEST_OF_MITRA', 'Priest of Mitra');
define('HLA_CLASS_RANGER', 'Ranger');
define('HLA_CLASS_TEMPEST_OF_SET', 'Tempest of Set');

// AION : Class
define('HLA_CLASS_CHANTER', 'Chanter');
define('HLA_CLASS_CLERIC', 'Cleric');
define('HLA_CLASS_GLADIATOR', 'Gladiator');
define('HLA_CLASS_SORCERER', 'Sorcerer');
define('HLA_CLASS_SPIRITMASTER', 'Spiritmaster');
define('HLA_CLASS_TEMPLAR', 'Templar');
define('HLA_CLASS_AION_RANGER', 'Ranger');
define('HLA_CLASS_AION_ASSASSIN', 'Assassin');

define('HLA_SIDE_ALLIANCE', 'Alliance');
define('HLA_SIDE_HORDE', 'Horde');

define('HLA_GENDER_MALE', 'Male');
define('HLA_GENDER_FEMALE', 'Female');

define('HLA_TALENT_AFFLICTION', 'Affliction&nbsp;&nbsp;(Warlock)');
define('HLA_TALENT_ARCANE', 'Arcane&nbsp;&nbsp;(Mage)');
define('HLA_TALENT_ARMS', 'Arms&nbsp;&nbsp;(Warrior)');
define('HLA_TALENT_ASSASSINATION', 'Assassination&nbsp;&nbsp;(Rogue)');
define('HLA_TALENT_BALANCE', 'Balance&nbsp;&nbsp;(Druid)');
define('HLA_TALENT_BEAST_MASTERY', 'Beast Mastery&nbsp;&nbsp;(Hunter)');
define('HLA_TALENT_BLOOD', 'Blood&nbsp;&nbsp;(Death Knight)');
define('HLA_TALENT_COMBAT', 'Combat&nbsp;&nbsp;(Rogue)');
define('HLA_TALENT_DEMONOLOGY', 'Demonology&nbsp;&nbsp;(Warlock)');
define('HLA_TALENT_DESTRUCTION', 'Destruction&nbsp;&nbsp;(Warlock)');
define('HLA_TALENT_DISCIPLINE', 'Discipline&nbsp;&nbsp;(Priest)');
define('HLA_TALENT_ELEMENTAL', 'Elemental&nbsp;&nbsp;(Shaman)');
define('HLA_TALENT_ENHANCEMENT', 'Enhancement&nbsp;&nbsp;(Shaman)');
define('HLA_TALENT_FERAL_COMBAT', 'Feral Combat&nbsp;&nbsp;(Druid)');
define('HLA_TALENT_FIRE', 'Fire&nbsp;&nbsp;(Mage)');
define('HLA_TALENT_FROST', 'Frost&nbsp;&nbsp;(Mage,Death Knight)');
define('HLA_TALENT_FURY', 'Fury&nbsp;&nbsp;(Warrior)');
define('HLA_TALENT_HOLY', 'Holy&nbsp;&nbsp;(Paladin,Priest)');
define('HLA_TALENT_MARKSMANSHIP', 'Marksmanship&nbsp;&nbsp;(Hunter)');
define('HLA_TALENT_PROTECTION', 'Protection&nbsp;&nbsp;(Paladin,Warrior)');
define('HLA_TALENT_RESTORATION', 'Restoration&nbsp;&nbsp;(Shaman,Druid)');
define('HLA_TALENT_RETRIBUTION', 'Retribution&nbsp;&nbsp;(Paladin)');
define('HLA_TALENT_SHADOW', 'Shadow&nbsp;&nbsp;(Priest)');
define('HLA_TALENT_SUBTLETY', 'Subtlety&nbsp;&nbsp;(Rogue)');
define('HLA_TALENT_SURVIVAL', 'Survival&nbsp;&nbsp;(Hunter)');
define('HLA_TALENT_UNHOLY', 'Unholy&nbsp;&nbsp;(Death Knight)');

// Warhammer : Server
define('HLA_SERVER_BADLANDS', 'Badlands');
define('HLA_SERVER_DARK_CRAG', 'Dark Crag');
define('HLA_SERVER_DARKLANDS', 'Darklands');
define('HLA_SERVER_GORFANG', 'Gorfang');
define('HLA_SERVER_HELDENHAMMER', 'Heldenhammer');
define('HLA_SERVER_IRON_ROCK', 'Iron Rock');
define('HLA_SERVER_IRONCLAW', 'Ironclaw');
define('HLA_SERVER_IRONFIST', 'Ironfist');
define('HLA_SERVER_MAGNUS', 'Magnus');
define('HLA_SERVER_MONOLITH', 'Monolith');
define('HLA_SERVER_OSTERMARK', 'Ostermark');
define('HLA_SERVER_PHOENIX_THRONE', 'Phoenix Throne');
define('HLA_SERVER_PRAAG', 'Praag');
define('HLA_SERVER_SKULL_THRONE', 'Skull Throne');
define('HLA_SERVER_THORGRIM', 'Thorgrim');
define('HLA_SERVER_VOLKMAR', 'Volkmar');
define('HLA_SERVER_VORTEX', 'Vortex');
define('HLA_SERVER_WASTELAND', 'Wasteland');

// Age Of Conan : Server
define('HLA_SERVER_BLOODSPIRE', 'Bloodspire');
define('HLA_SERVER_CIMMERIA', 'Cimmeria');
define('HLA_SERVER_GWAHLUR', 'gwahlur');
define('HLA_SERVER_SET', 'Set');
define('HLA_SERVER_TYRANNY', 'Tyranny');
define('HLA_SERVER_WICCANA', 'Wiccana');

// AION US: Server
define('HLA_SERVER_ISRAPHEL', 'Israphel');
define('HLA_SERVER_NEZEKAN', 'Nezekan');
define('HLA_SERVER_SIEL', 'Siel');
define('HLA_SERVER_VAIZEL', 'Vaizel');
define('HLA_SERVER_ZIKEL', 'Zikel');

// AION EU: Server
define('HLA_SERVER_BALDER', 'Balder');
define('HLA_SERVER_KROMEDE', 'Kromede');
define('HLA_SERVER_PERENTO', 'Perento');
define('HLA_SERVER_SPATALOS', 'Spatalos');
define('HLA_SERVER_SUTHRAN', 'Suthran');
define('HLA_SERVER_TELEMACHUS', 'Telemachus');
define('HLA_SERVER_THOR', 'Thor');
define('HLA_SERVER_URTEM', 'Urtem');

define('ERROR_INVALID_TOP_UP_ACCOUNT', 'Invalid Top-up info.');
define('ERROR_DTU_EXCEED_TOP_UP_LIMIT', 'Exceed Top-up limit.');
define('ERROR_UNABLE_VALIDATE_TOP_UP_ACCOUNT', "The publisher's servers are currently down. As a result, the Direct Top Up option is down until further notice. It'll be back as soon as possible.");
define('ERROR_DTU_NO_CHARACTERS_IN_LIST', "There is no character returned based on the information given.");

define('TABLE_HEADING_DELIVERY_MODE', 'Delivery Mode');

define('TEXT_INFO_SEND_TO_MY_ACCOUNT', 'SEND TO MY ACCOUNT');
define('TEXT_INFO_CHOOSE_PREFFERED_DELIVERY_MODE', 'Choose your preffered delivery method');
define('TEXT_INFO_DELIVERY_MODE', 'Delivery Method');
define('TEXT_INFO_DELIVERY_SEND_TO_ACCOUNT', 'OffGamers Account');
define('TEXT_INFO_DELIVERY_SEND_TO_ACCOUNT_DESCRIPTION', "Codes viewable in my OffGamers order history. <a href='http://kb.offgamers.com/?p=1284' target='new'>more details</a>");
define('TEXT_INFO_DELIVERY_DIRECT_TOP_UP', 'Direct Top Up');
define('TEXT_INFO_DELIVERY_DIRECT_TOP_UP_DESCRIPTION', "Fill in your account username and the product <br>will be directly topped-up into the account provided. <a href='http://kb.offgamers.com/?p=1270' target='new'>more details</a>");
define('TEXT_INFO_DELIVERY_IN_STORE_PICKUP', 'In-store Pickup');
define('TEXT_INFO_DELIVERY_IN_STORE_PICKUP_DESCRIPTION', 'Collect your product(s) at OffGamers Pudu.');
define('TEXT_INFO_VIEW_CODE_IN_ACCOUNT_HISTORY', "Codes viewable in my OffGamers order history. <br><a href='http://kb.offgamers.com/?p=1284' target='new'>more details</a>");
define('TEXT_INFO_MOBILE_PHONE_NUMBER_IS_VALID', '<span class="requiredInfo">*</span> Please ensure your mobile number is valid for authentication purposes. Click <a href="' . tep_href_link(FILENAME_ACCOUNT_EDIT, '', 'SSL') . '">HERE</a> to edit.');

define('TEXT_PWL_IMPORTANT_NOTICE', 'Kindly take note that the actual starting time of it would take about 1 to 2 hours. Please do not login to the account that has been submitted to us for Power Leveling as this might cause a conflict in login IPs and put your account at risk of being flagged by the game makers.');

define('ERROR_INVALID_QUANTITY', 'Invalid quantity');
define('ENTRY_QTY', 'Quantity');
define('ENTRY_DELIVERY_METHOD', 'Delivery Method');

define('TEXT_INFO_DDOS_ATTACH_SUBJECT', 'Latest Annoucement');
define('TEXT_INFO_DDOS_ATTACH_CONTECT', 'We kindly advise all our valued users to check our <a href="http://www.facebook.com/OffGamers" style="color:red;text-decoration: underline;">Facebook</a> and <a href="http://twitter.com/#!/offgamers" style="color:red;text-decoration: underline;">Twitter</a> pages for further developments and updates pertaining <br>the OffGamers site, products and services. Thank you');

define('ERROR_CHECKOUT_QUANTITY_CONTROL_EXCEED', 'You have exceeded the purchase limit set on this product.');
define('ERROR_CHECKOUT_QUANTITY_CONTROL_ORDERS', 'You have exceeded the purchase limit set on this product. Please check the delivery time on the product to view the purchase limit set. <br />');

define('TEXT_EXP_CHECKOUT_QTY_NORMAL', '&laquo; Back');
define('TEXT_EXP_CHECKOUT_QTY_BULK', 'Click here to purchase more');
define('TEXT_EXP_CHECKOUT_BULK_NOT_AVAILABLE', 'Quantity requested not available. To buy in bulk, kindly contact <a href="mailto:<EMAIL>"><EMAIL></a>');

define('COMBO_SELECT_QTY', 'Qty.');
define('COMBO_SELECT_DELIVERY_MODE', 'Delivery Mode.');
define('WARNING_PRODUCT_PRICE_WITHOUT_GST', 'before %s');

//Telesign phone verification
define('TEXT_CALL_LANGUAGE', 'Call Language:');
define('IMAGE_BUTTON_TEXT_ME_NOW', 'Text Me Now');
define('TEXT_SMS_NOTES', '"Text Me Now" is an SMS feature and only for mobile phone users. All text messages will be in English only.');

define('TEXT_INFO_VERIFYING', 'Verifying');
define('TEXT_INFO_SEARCH_NO_PRODUCT_FOUND', '<b>No products found.</b>Try another search or browse in <a href="' . tep_href_link(FILENAME_SEARCH_ALL_GAMES) . '">Store</a> instead.');
define('TEXT_INFO_BROWSE_ALL_RESULTS', 'Browse All Results');
define('MENU_TITLE_LOGIN_ACCOUNT', 'LOGIN ACCOUNT');
define('MENU_TITLE_REGISTER_ACCOUNT', 'REGISTER ACCOUNT');
define('MENU_TITLE_CHECKOUT', 'CHECKOUT');
define('MENU_TITLE_BACK_TO_STORE', 'BACK TO STORE');
define('BUTTON_BROWSE_IN_STORE', 'Browse in store');
define('BUTTON_ALL_PAYMENT_METHODS', 'All payment methods');
define('HEADER_LOGIN_TO_YOUR_ACCOUNT', 'LOGIN TO YOUR ACCOUNT');
define('LOGIN_WITH_FB_TITLE', 'or login using:');
define('BUTTON_READ_MORE_NEWS', 'Read more news');
define('MENU_HEADER_GROUP_BY_PLATFORM', 'BY PLATFORM');
define('MENU_HEADER_GROUP_BY_PRODUCT_TYPE', 'BY PRODUCT TYPE');

define('EMAIL_G2G_BUYBACK_SUBJECT', "New Buyback Order #%s");
define('EMAIL_G2G_BUYBACK_BODY', "Thank you for selling your items to %s.\n\n Supplier Order Summary:\n %s \n");
define('EMAIL_G2G_BUYBACK_ORDER_NUMBER', 'Buyback Order Number: %s');
define('EMAIL_G2G_BUYBACK_DATE_ORDERED', 'Buyback Order Date: %s');
define('EMAIL_G2G_BUYBACK_CUSTOMER_EMAIL', 'E-mail Address: %s');
define('EMAIL_G2G_BUYBACK_PRODUCTS', "\n\n" . 'Products');
define('EMAIL_G2G_BUYBACK_ORDER_TOTAL', 'Total: %s');
define('EMAIL_G2G_BUYBACK_COMMENTS', 'Extra Information:');
define('EMAIL_G2G_BUYBACK_STATUS', 'Status: Pending');
define('EMAIL_G2G_BUYBACK_ORDER_GUIDE', '<b>Order Flow : [Pending] --> [Processing] --> [Completed] or [Canceled]</b><br /><br />[Pending] : Order submission incomplete. Kindly login to OffGamers.com and submit the missing character information.<br />[Processing] : This delivery is being registered into our system.<br />[Completed] : This delivery has been registered completely.<br />[Canceled] : The order has been canceled.<br /><br /><b>Note 1:</b> Please ensure the Account information you provided is 100% accurate.<br /><br /><b>Note 2:</b> Once the Account information been submitted, please do not login to the account, either via the account management page or ingame.<br /><br />Important: Please take note that Blizzard may lock the account if they detect a different IP accessing the account within a short period of time. As a precaution: <br /><br /><b>Note 3:</b> Once your order is in the [Completed] status, payment will be made available to you within 15 minutes at your OffGamers Account. Use the [Withdraw] function to withdraw your payment and have it credited to a payment method of your choosing.');
define('EMAIL_G2G_BUYBACK_ORDER_CLOSING', "Please contact us via our Online Live Support service or e-mail to %s if you face an issue with the buyback. Please remember to include your Buyback Order Number when contacting us to expedite the process.\n\nYou will receive an e-mail notification whenever there is an update in your buyback order status. You can also review your buyback orders and check their status by signing in to OffGamers.com and clicking on \"View Buyback Order History\".\n\nThank you for supporting %s.");

define('TEXT_ERROR_MAX_PENDING_ORDER', 'You have %d pending order, kindly check on previous orders\' payment status.');
define('EMAIL_MAX_PENDING_ORDER_SUBJECT', 'Customer ID #%d Hit Maximum Pending Order');
define('EMAIL_MAX_PENDING_ORDER_TEXT', 'Customer ID #%d Hit Maximum Pending Order, kindly liaise with customer.');

define('TEXT_DEFAULT_CONTACT_CS_ERROR_MESSAGE','Please try once more and if the issue presist, please do not hesitate to contact us directly by clicking <a href=\'http://support.offgamers.com/support/home\'>here</a>.');

define('TEXT_SIGN_UP', 'Sign up');
define('TEXT_LOG_IN', 'Login');
define('TEXT_OR_CONNECT', 'or connect');

define('TEXT_MY_OGM', 'MyOffGamers');
define('TEXT_OVERVIEW', 'Overview');
define('TEXT_BUY_HISTORY', 'Buy History');
define('TEXT_SELL_HISTORY', 'Sell History');
define('TEXT_REQUEST_PAYMENT', 'Request Payment');

define('TEXT_ACCOUNT_ID', 'Account ID');
define('TEXT_BUY_STATUS', 'Buy Status');
define('TEXT_MANAGE_PROFILE', 'Manage Profile');
define('TEXT_SOCIAL_CONNECT', 'Social Connect');
define('TEXT_STORE_CREDITS', 'Store Credits');
define('TEXT_WOR', 'OP');

define('TEXT_REGIONAL_TITLE', 'Regional settings for your next visit.');
define('BTN_SAVE_CHANGES', 'Save');

define('TEXT_OGM_DOMAIN', 'OffGamers.com');
define('TEXT_OGM_CAPTION', 'MMO Games CD Keys and Game Cards');
define('TEXT_G2G_DOMAIN', 'G2G.com');
define('TEXT_G2G_CAPTION', 'Gamer2Gamer Marketplace');
define('TEXT_GMZ_DOMAIN', 'Gamernizer.com');
define('TEXT_GMZ_CAPTION', 'Play Free Multiplayer Browser Games');
?>