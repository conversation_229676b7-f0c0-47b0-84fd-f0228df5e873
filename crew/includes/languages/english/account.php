<?php
/*
  $Id: account.php,v 1.13 2013/07/18 06:49:07 hungnian.yong Exp $
  
  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2003 osCommerce

  Released under the GNU General Public License
*/

define('NAVBAR_TITLE', 'My Account');
define('HEADING_TITLE', 'My Account');

define('OVERVIEW_TITLE', 'Overview');
define('OVERVIEW_SHOW_ALL_ORDERS', '(show all orders)');
define('OVERVIEW_PREVIOUS_ORDERS', 'Previous Orders');

define('MY_ACCOUNT_TITLE', 'My Account');
define('MY_ACCOUNT_INFORMATION', 'View or change my account information.');
define('MY_ACCOUNT_ADDRESS_BOOK', 'View or change entries in my address book.');
define('MY_ACCOUNT_PASSWORD', 'Change my account password.');

define('MY_ORDERS_TITLE', 'My Orders');
define('MY_ORDERS_VIEW', 'View the orders I have made.');

define('EMAIL_NOTIFICATIONS_TITLE', 'E-Mail Notifications');
define('EMAIL_NOTIFICATIONS_NEWSLETTERS', 'Subscribe or unsubscribe from newsletters.');
define('EMAIL_NOTIFICATIONS_PRODUCTS', 'View or change my product notification list.');

// Mantis # 0000024 @ ************ - My Account Home alert messages
define('ALERT_EMAIL_VERIFICATION', "<div style=\"display:inline-block; font: bold 16px Arial,Tahoma,Verdana,Helvetica,sans-serif; color:black; padding: 0px 0px 10px 5px;\">You have not verified your email address. <div style=\"display: block; font-weight:normal;\"><b>%s</b> to verify your email address.</div></div>");
define('ALERT_TELEPHONE_VERIFICATION', "<div style=\"display:inline-block; font: bold 16px Arial,Tahoma,Verdana,Helvetica,sans-serif; color: black; padding: 0px 0px 10px 5px;\">You have not verified your mobile phone number.<div style=\"display: block; font-weight: normal;\"><b>%s</b> to request for a mobile phone verification when you are available to pick up the call or SMS.</div></div>");
define('ALERT_ORDER_DISPUTES', "<div style=\"display:inline-block; font: bold 16px Arial,Tahoma,Verdana,Helvetica,sans-serif; color: black; padding: 0px 0px 10px 5px;\">You have orders on hold because of disputes.<div style=\"display: block; font-weight: normal;\">Please contact our <b>%s</b> operators to resolve the issue.</div></div>");
define('ALERT_ORDER_NEEDS_PAYMENT_INFO', "<div style=\"display:inline-block; font: bold 16px Arial,Tahoma,Verdana,Helvetica,sans-serif; color: black; padding: 0px 0px 10px 5px;\">You have order(s) that requires payment information.<div style=\"display: block; font-weight: normal;\"><b>%s</b> to submit payment information. Please do so at the soonest possible time to avoid cancelation of your order.</div></div>");

// Title
define('TITLE_MY_LATEST_ORDERS', "My Latest Orders");

// Text
define('SYMBOL_COLON', ": ");
define('TEXT_BUY_ORDER', "BUY ORDER");
define('TEXT_SELL_ORDER', "SELL ORDER");
define('TEXT_LEGEND_GAME_CARD', "Game Card");
define('TEXT_LEGEND_GAME_CURRENCY', "Game Currency");
define('TEXT_LEGEND_POWER_LEVELING', "Power Leveling");
define('TEXT_LEGEND_STORE_CREDIT', "Store Credit");
define('TEXT_LEGEND_HIGH_LEVEL_ACCOUNT', "High Level Account");
define('TEXT_MEMBER_ID', "OffGamers ID");
define('TEXT_MEMBER_STATUS', "member status");
define('TEXT_CREDIT_BALANCE', "Store Credit Balance");
define('TEXT_CURRENT_BALANCE', "Seller Credits");
define('TEXT_LOYALTY_POINT', "Reputation Points");
define('TEXT_PASSWORD', "Password");
define('TEXT_MY_BILLING_ADDRESS', "My Billing Address");
define('TEXT_EDIT_NOW', "Edit now");
define('TEXT_CHANGE_NOW', "Change now");
define('TEXT_NO_LAST_BUY_ORDERS',"<div style=\"text-align:center;\">You have no new orders in the last 14 days.</div>");
define('TEXT_NO_LAST_SELL_ORDERS',"<div style=\"text-align:center;\">You have no new orders in the last 14 days.</div>");


// Button
define('BUTTON_VIEW_PAST_BUY_ORDERS', "View Past Buy Orders");
define('BUTTON_VIEW_PAST_SELL_ORDERS', "View Past Sell Orders");
?>