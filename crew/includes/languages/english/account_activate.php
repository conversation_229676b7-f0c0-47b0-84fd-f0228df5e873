<?
define('NAVBAR_TITLE_1', 'Account Activate');
define('NAVBAR_TITLE_2', 'Verify E-mail');
define('NAVBAR_TITLE_3', 'Verify Mobile Phone');
define('NAVBAR_TITLE_4', 'Verify Order\'s Mobile Phone');
define('HEADING_TITLE_1', 'E-mail Address Verification');
define('HEADING_TITLE_2', 'Mobile Phone Verification');

// Phone Verification
define('TEXT_TITLE_ENTER_PHONE_NUMBER', 'Please ensure that the following mobile phone number is correct and reachable.');
define('ENTRY_CONTACT_NUMBER_EXIST_ERROR', "The mobile number you had provide is in used or invalid. Please key in another mobile number.");

define('TEXT_VERIFY_INSTRUCTION_LINK', 'What is this?');
define('TEXT_COUNTRY_NOT_SUPPORT', '(Country not supported by our mobile phone verification system)');
define('ERROR_REPORT', 'Report error');
define('TEXT_RESEND_VERIFICATION_CODE', '<a href= "' . tep_href_link(FILENAME_ACCOUNT_ACTIVATE, 'action=resend') . '">Resend confirmation code</a>');
define('TEXT_CHANGE_PHONE_NUMBER', '(Not your mobile phone number? <a id="link_edit_phone_number" href="' . tep_href_link(FILENAME_ACCOUNT_EDIT) . '">Change here</a>.)');

define('CODE_MATCH_MESSAGE', 'You have successfully verified your mobile phone number. Thank you.');
define('CODE_NOT_MATCH_RETRY_MESSAGE', 'The confirmation code that you have entered is incorrect. Please try again.');
define('CODE_NOT_MATCH_MESSAGE', 'The confirmation code that you have entered is incorrect. We will manually review this order as soon as we could.');
define('REQUEST_REACHED_MAX_ATTEMPT_ERROR', 'You have reached your maximum attempts for today. Please try again after 24 hours.');

define('TABLE_HEADING_COMMENTS', 'Enter a comment for the order processed');

define('EDIT_TELEPHONE' , '(Edit)');

// Error Report Content
define('EMAIL_TITLE', 'Mobile phone Verification Error Report');
define('EMAIL_ADMIN_NAME', 'Customers Support');
define('EMAIL_CONTENT_1', '<b>Report Date & Time:</b>');
define('EMAIL_CONTENT_2', '<b>Customer Name:</b>');
define('EMAIL_CONTENT_3', '<b>Customer ID:</b>');
define('EMAIL_CONTENT_4', '<b>Order ID:</b>');
define('EMAIL_CONTENT_5', '<b>Order Date & Time:</b>');
define('EMAIL_CONTENT_6', '<b>Order Status:</b>');
define('EMAIL_CONTENT_7', '<b>Mobile phone Location:</b>');
define('EMAIL_CONTENT_8', '<b>Mobile phone Number:</b>');

define('EMAIL_REPORT_SEND_MESSAGE', 'An e-mail have been sent to our assistants.');


// Email Verification
define('ENTRY_COUNTRY_CODE', 'Country Code:');
define('ENTRY_VERIFICATION_CODE', 'Verification Code:');
define('MESSAGE_SUCCESS_MAIL_2', "A verifications e-mail has been re-sent to your registered e-mail address.");
define('MESSAGE_VERIFY_FAIL', 'E-mail verification fail.');
define('MESSAGE_EMAIL_VERIFIED', 'Your e-mail address has already been verified.');
define('MESSAGE_VERIFIED_SUCCESS', 'Your e-mail address has been successfully verified, thank you.');
define('MESSAGE_VERIFIED_NOT_MATCH', 'The combination of e-mail address and verification code entered is not valid.');
define('MESSAGE_ERROR_MAIL', 'Fail to verify e-mail.');

define('MANUAL_VERIFY_EMAIL_1', 'To complete the process of confirming your email address, please enter the confirmation code from the email that OffGamers sent you.');
define('MANUAL_VERIFY_EMAIL_2', 'Please enter %s the verification code here to verify your e-mail address' . "<br><br>");
define('MANUAL_VERIFY_EMAIL_LOGIN', '');
define('MANUAL_VERIFY_EMAIL_LOGOUT', 'your e-mail address');

define('MANUAL_VERIFY_EMAIL_LINK', '<a href= "' . tep_href_link(FILENAME_ACCOUNT_ACTIVATE, 'action=resend') . '">' . 'Click here to have the verification code resent to your e-mail address.</a>' . "<br><br>");

define('TEXT_REQUEST_VERIFY','%s and click "Continue" to have the verification code resent to your e-mail address. %s');
define('TEXT_REQUEST_EMAIL_LOGIN_1', 'Please check whether your e-mail address is correct');
define('TEXT_REQUEST_EMAIL_LOGOUT_1', 'Please enter your e-mail address');
define('TEXT_REQUEST_EMAIL_LOGIN_2', ' If the e-mail address is incorrect, please go to the edit information page to change it.');
define('TEXT_REQUEST_EMAIL_LOGOUT_2', '');

// Send email to customer if customer is affiliate with login details when account activation done
define('EMAIL_CUSTOMER_SUBJECT', 'Successful Customer Account Activation');
define('EMAIL_CUSTOMER_CONTENT', 'You have successfully activated your ' . STORE_NAME . ' customer account, while your affiliate account is pending for approval. Affiliate approval usually takes less than 12 hours.' . "\n\n");

define('EMAIL_CUSTOMER_LOGIN_DETAILS', 'Meanwhile, you may login to ' . STORE_NAME . ' as customer using the following:' . "\n");
define('EMAIL_LOGIN_ADDRESS', 'Login Address: <a href= "' . tep_href_link(FILENAME_LOGIN) . '">'. tep_href_link(FILENAME_LOGIN) .'</a>');
define('EMAIL_ADDRESS', "\n". 'E-mail Address: ');
define('EMAIL_CUSTOMER_PASSWORD', "\n". 'Password: ');
define('EMAIL_CUSTOMER_PASSWORD_CHANGE', "\n\n". 'Do change your password after you have login. Take note that your affiliate login will be the same as your customer login.');

define('IMAGE_BUTTON_CALL_ME_NOW', 'Call Me Now');
?>
