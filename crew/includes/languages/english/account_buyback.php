<?
/*
  	$Id: account_buyback.php,v 1.8 2010/07/30 10:57:33 henry.chow Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2003 osCommerce
	
  	Released under the GNU General Public License
*/

define('NAVBAR_TITLE', 'Sell Your Game Accounts');

define('HEADING_TITLE', 'Sell Your Game Accounts');

define('TEXT_TITLE_CHOOSE_GAME', 'Please Select Game:');
define('BUYBACK_SECTION_MAIN_CHAR', 'MAIN CHARACTER INFORMATION');
define('BUYBACK_SECTION_ALTERNATE_CHAR', 'ALTERNATE CHARACTER INFORMATION');

define('ENTRY_BUYBACK_ACCOUNT_SERVER', 'Server:');
define('ENTRY_BUYBACK_CUSTOMER_NAME', 'Name:');
define('ENTRY_BUYBACK_ACCOUNT_MAIN_CHAR_SERVER', 'Realm:');
define('ENTRY_BUYBACK_ACCOUNT_ALT_CHAR_SERVER', 'Realm:');
define('ENTRY_BUYBACK_MAIN_CHAR_LVL', 'Character Level:');
define('ENTRY_BUYBACK_ACCOUNT_ALT_CHAR_LVL', 'Level:');
define('ENTRY_BUYBACK_MAIN_CHAR_RACE', 'Race:');
define('ENTRY_BUYBACK_MAIN_CHAR_CLASS', 'Class:');
define('ENTRY_BUYBACK_ALT_CHAR_CLASS', 'Class:');
define('ENTRY_BUYBACK_MAIN_CHAR_GENDER', 'Gender:');
define('ENTRY_BUYBACK_MAIN_CHAR_EQUIPMENT', 'Equipment:');
define('ENTRY_BUYBACK_MAIN_CHAR_MOUNT', 'Mount:');
define('ENTRY_BUYBACK_MAIN_CHAR_GOLD', 'Gold:');
define('ENTRY_BUYBACK_ACCOUNT_STATUS', 'Account:');
define('ENTRY_BUYBACK_ACCOUNT_CDKEY', 'CD Keys:');
define('ENTRY_BUYBACK_ACCOUNT_SECRET', 'Secret Q&A:');
define('ENTRY_BUYBACK_ASKING_PRICE', 'Asking Price:');
define('ENTRY_BUYBACK_OTHER_INFO', 'Additional Information:');

define('ENTRY_GAME_CURRENCY_TEXT', '* (eg.: 50, 100)');

define('OPTION_ACTIVE', 'Active');
define('OPTION_INACTIVE', 'Non Active');
define('OPTION_AVAILABLE', 'Available');
define('OPTION_NOT_AVAILABLE', 'Non Available');
define('OPTION_REMEMBER', 'Remember');
define('OPTION_FORGOTTEN', 'Forgotten');

define('ENTRY_BUYBACK_NAME_ERROR', 'Please enter your name.');
define('ENTRY_BUYBACK_MAIN_CHAR_SERVER_ERROR', 'Please select your main character\'s realm.');
define('ENTRY_BUYBACK_MAIN_CHAR_LVL_ERROR', 'Please select your main character\'s current level.');
define('ENTRY_BUYBACK_MAIN_CHAR_RACE_ERROR', 'Please select your main character\'s race.');
define('ENTRY_BUYBACK_MAIN_CHAR_CLASS_ERROR', 'Please select your main character\'s class.');
define('ENTRY_BUYBACK_MAIN_CHAR_GENDER_ERROR', 'Please select your main character\'s gender.');
define('ENTRY_BUYBACK_MAIN_CHAR_EQUIPMENT_ERROR', 'Please select your main character\'s equipment.');
define('ENTRY_BUYBACK_MAIN_CHAR_MOUNT_ERROR', 'Please select your main character\'s mount.');
define('ENTRY_BUYBACK_MAIN_CHAR_GOLD_ERROR', 'Please enter a valid gold amount.');
define('ENTRY_BUYBACK_MAIN_ACC_STATUS_ERROR', 'Please specify your account status.');
define('ENTRY_BUYBACK_MAIN_ACC_CDKEY_ERROR', 'Please specify does your account\'s CD Key is available.');
define('ENTRY_BUYBACK_MAIN_ACC_SECRET_ERROR', 'Please specify does your account\'s secret question and answer are available.');
define('ENTRY_BUYBACK_ASKING_PRICE_ERROR', 'Please enter a valid price value.');

define('ENTRY_BUYBACK_ALT_CHAR_LVL_ERROR', 'Please select your the current level for alternate character %d.');
define('ENTRY_BUYBACK_ALT_CHAR_CLASS_ERROR', 'Please select your the class for alternate character %d.');
define('ENTRY_BUYBACK_CANNOT_ACCEPT_ERROR', 'We only purchase accounts with CD keys, Secret Q&A and appropriately geared characters.');
define('TEXT_CHAR_CLASS_NOT_FOUND', '<i>Your character class is not listed? Please do check back with us from time to time as we are only accepting specific classes for the time being.</i>');
define('TEXT_BUYBACK_ACCOUNT_NO_ALT_CHARACTER', 'There is no any alternate characters information.');
define('TEXT_BUYBACK_CANNOT_CONFIRM_MESSAGE', 'Please check and confirm on your account information above. Should there be any mistake, please hit %s to go back and make the changes.<br><br>I confirm all the above information is correct %s');
define('TEXT_BUYBACK_ACCOUNT_THANK_YOU_MESSAGE', 'Thank you for inquiring our account buyback service.<br>We will get back to you within 48 hours with the best offer via email.<br>Do feel free to contact us should there be any queries.');
define('TEXT_BUYBACK_ACCOUNT_CONFIRM_FOOTER', '<span class="greetUser">THANK YOU</span>');
define('TEXT_ACCOUNT_BUYBACK_DISCLAIMER', '<br><b><span class="messageStackWarning">Please acknowledge that we need your account\'s CD Key, Secret Question and Answer, and Phone Number that was registered to your account. Do have these ready before selling your account to us to speed up the processing speed of your sale.</span></b>' . "<br><br>" . 
										  'OffGamers reserve all rights to not accept any offers should we have the slightest hint that any account sold is stolen or compromised. We will not be liable for any loss claims once the sale is completed. We take serious measures dealing with fraudulent cases due to the vulnerable nature of account buybacks and all cases will be handled by authority forces and penalties/punishments will not be limited to minors. Serious actions will be taken against all fraud cases, sales of stolen accounts and attempts to regain an account after sales. If you do not receive any reply from us after submission for more than 2 days, that means the description of your account is no longer needed by us.');

define('EMAIL_SUBJECT', 'Account Buyback Inquiry');
define('EMAIL_SEPARATOR', '------------------------------------------------------');
define('EMAIL_CONTENTS_HEADER', '--------------------THIS IS AN AUTOMATED MESSAGE SENT FROM OFFGAMERS.COM --------------------' . "\n\n" . 'Thank you for contacting OffGamers.com. We have received your account information as below:-' . "\n\n");
define('EMAIL_SECTION_PARTICULARS', ENTRY_BUYBACK_CUSTOMER_NAME . ' %s' . "\n" . ENTRY_EMAIL_ADDRESS . ' %s' . "\n\n");
define('EMAIL_SECTION_MAIN_CHARACTER', BUYBACK_SECTION_MAIN_CHAR . "\n" . EMAIL_SEPARATOR . "\n" . ENTRY_BUYBACK_ACCOUNT_MAIN_CHAR_SERVER . ' %s' . "\n" . ENTRY_BUYBACK_MAIN_CHAR_LVL . ' %d' . "\n" . ENTRY_BUYBACK_MAIN_CHAR_RACE . ' %s' . "\n" . ENTRY_BUYBACK_MAIN_CHAR_CLASS . ' %s' . "\n" . ENTRY_BUYBACK_MAIN_CHAR_GENDER . ' %s' . "\n\n" . ENTRY_BUYBACK_MAIN_CHAR_EQUIPMENT . ' %s' . "\n" . ENTRY_BUYBACK_MAIN_CHAR_MOUNT . ' %s' . "\n" . ENTRY_BUYBACK_MAIN_CHAR_GOLD . ' %d' . "\n\n");
define('EMAIL_SECTION_ACCOUNT_INFO', ENTRY_BUYBACK_ACCOUNT_STATUS . ' %s' . "\n" . ENTRY_BUYBACK_ACCOUNT_CDKEY . ' %s' . "\n" . ENTRY_BUYBACK_ACCOUNT_SECRET . ' %s' . "\n\n" . ENTRY_BUYBACK_ASKING_PRICE . ' US $%.2f' . "\n\n" . ENTRY_BUYBACK_OTHER_INFO . "\n" . '%s' . "\n\n");
define('EMAIL_SECTION_ALT_CHARACTER', ENTRY_BUYBACK_ACCOUNT_ALT_CHAR_SERVER . ' %s' . "\n" . ENTRY_BUYBACK_ACCOUNT_ALT_CHAR_LVL . ' %d' . "\n" . ENTRY_BUYBACK_ALT_CHAR_CLASS . ' %s' . "\n\n");
define('EMAIL_CONTENTS_FOOTER', 'Please allow up to 48 hours to review your proposal and we will get back to you with our best quote as soon as possible.' . "\n" . 'Thank you again for choosing OffGamers.' . "\n\n" . '------------------------------------------------------END OF MESSAGE -------------------------------------------------------------');
define('EMAIL_FOOTER', STORE_EMAIL_SIGNATURE);
?>