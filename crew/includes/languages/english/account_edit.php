<?
/*
  	$Id: account_edit.php,v 1.13 2012/10/10 11:55:13 weesiong Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2003 osCommerce
	
  	Released under the GNU General Public License
*/

define('NAVBAR_TITLE_1', 'My Account');
define('NAVBAR_TITLE_2', 'Edit Account');

define('HEADING_TITLE', 'Edit Profile');
define('MY_ACCOUNT_TITLE', 'My Account');

define('TITLE_LOGIN_EMAIL_PASSWORD', "LOGIN E-MAIL &amp; PASSWORD");
define('TITLE_PERSONAL_DETAILS', "PERSONAL DETAILS");
define('TITLE_BILLING_ADDRESS_FOR_CREDIT_CARD', "ADDRESS BOOK (BILLING ADDRESS FOR CREDIT CARD PAYMENT)");

define('ENTRY_COUNTRY_CODE', 'Country Code:&nbsp;');
define('ENTRY_COUNTRY_CODE_TEXT', '*');
define('ENTRY_CURRENT_CONTACT_NUMBER', 'Current Mobile Phone Number:&nbsp;');
define('ENTRY_CONTACT_NUMBER', 'Mobile Phone Number:&nbsp;');
define('ENTRY_CONTACT_NUMBER_TEXT', '*');
define('ENTRY_INSTANT_MESSENGER', 'Instant Messenger&nbsp;(IM):');
define('ENTRY_BILLING_ADDRESS1', 'Address1:&nbsp;');
define('ENTRY_BILLING_ADDRESS2', 'Address2:&nbsp;');
define('ENTRY_NEW_COUNTRY_CODE', 'New Country Code:&nbsp;');
define('ENTRY_NEW_CONTACT_NUMBER', 'New Mobile Phone Number:&nbsp;');

define('ENTRY_PASSWORD_NEW_CONFIRMATION', 'Confirm New Password:');
define('ENTRY_PASSWORD_NEW_CONFIRMATION_TEXT', '*');

define('ENTRY_PASSWORD_CURRENT_NOT_MATCH_ERROR', 'Your Current Password is incorrect.');
define('ENTRY_PASSWORD_NEW_ERROR', "Your New Password must be at least %d characters.");
define('ENTRY_PASSWORD_NEW_NOT_MATCH_ERROR', 'Your Confirm Password must match your New Password.');
define('ENTRY_PASSWORD_CONFIRMATION_ERROR', "Your Confirm Password must be at least %d characters.");
define('ENTRY_CONTACT_NUMBER_ERROR', "Your mobile phone number must be at least %d number.");
define('ENTRY_CONTACT_NUMBER_EXIST_ERROR', "The mobile number you had provide is in used or invalid. Please key in another mobile number.");

define('ENTRY_EMAIL_JS_ERROR', "The email provided does not appear to be valid.");
define('ENTRY_FIRSTNAME_JS_ERROR', "First name must be at least %d character long.");
define('ENTRY_LASTNAME_JS_ERROR', "Last name must  be at least %d character long.");
define('ENTRY_PASSWORD_CURRENT_JS_ERROR', "Current Password must be at least %d characters and can only contains letter and number.");
define('ENTRY_PASSWORD_JS_ERROR', "New Password must be at least %d characters and can only contains letter and number.");
define('ENTRY_PASSWORD_CONFIRMATION_JS_ERROR', "Confirm Password must be at least %d characters and can only contains letter and number.");
define('ENTRY_PASSWORD_CONFIRMATION_NOT_MATCH_JS_ERROR', "New Password does not match with Confirm Password.");
define('ENTRY_CONTACT_NUMBER_JS_ERROR', "Your mobile phone number does not appear to be valid.");
define('ENTRY_DOB_JS_ERROR', "Your birthdate does not appear to be valid.");
define('ENTRY_SECRET_QUESTION_JS_ERROR', "You must select a secret question from the Secret Question pull down menu.");
define('ENTRY_ANSWER_JS_ERROR', "Your Answer must contain a minimum of %d characters.");
define('ENTRY_SIGNUP_TERMSANDCONDITION_JS_ERROR', "You must agree to our Terms of Service & Privacy Policy");  

define('ENTRY_EMAIL_NOTICE', "Please use a valid email address as we need to email you to confirm your account.");
define('ENTRY_FIRSTNAME_NOTICE', "Please use the first name as appears on your ID.");
define('ENTRY_LASTNAME_NOTICE', "Please use the last name as appears on your ID.");
define('ENTRY_PASSWORD_NOTICE', "Your password must be at least %d characters long and it is case sensitive.");
define('ENTRY_CONFIRM_PASSWORD_NOTICE', "Type in your password again for verification purposes.");
define('ENTRY_CONTACTNUMBER_NOTICE', "Please provide a valid mobile phone number. It will be used for verification purposes and also to reset your Secret Question & Answer if you have forgotten it.");
define('ENTRY_ANSWER_NOTICE', "To ensure the security of your account, your secret question and answer will be used for verification purposes during changing, updating info and also for withdrawal of funds and it is not case sensitive.");
define('ENTRY_IM_NOTICE', "If you tell us your IM address, we will use this method to contact you for delivery whenever possible.");
define('ENTRY_DOB_NOTICE', "We are asking for legal reasons.");

define('SUCCESS_ACCOUNT_UPDATED', 'Your account has been successfully updated.');
define('SUCCESS_ACCOUNT_MOBILE_UPDATED', 'Your mobile phone number has been changed successfully.');
define('CHANGE_ACCOUNT_SETTING', 'Please contact <a href="mailto:'.EMAIL_TO.'">'.EMAIL_TO.'</a> for change of First Name, Last Name and Gender.');
define('ERROR_EMAIL_EXISTED', 'The e-mail address that you have entered exist in our records.  Please e-mail us at <a href="' . tep_href_link(FILENAME_CONTACT_US) . '">store owner</a> if you would like to combine the accounts.');

define('ENTRY_TELEPHONENUMBER_NOTICE', 'Please provide a valid phone number. Your phone number will be used for verification purposes and also to reset your Secret Question & Answer if you have forgotten it.');
define('ENTRY_SECRETQNA_NOTICE', 'Please fill in the blank for the answer to your secret question to proceed with the updated to your account information.');

define('MANUAL_ACTIVATE_ACCOUNT','%s Please check your e-mail account %s for the activation code.  You may only sign in to enjoy your member privileges after activating your account.  The e-mail may appear in your spam mail or junk mail folder. Please contact us at <a href="mailto:'.EMAIL_TO.'">'.EMAIL_TO.'</a> if you have trouble receiving the activation e-mail. <br><br>Please enter %s the activation code here to activate your account. you will be redirected to sign in page after successfully activating your account.' . "<br><br>");
define('MANUAL_ACTIVATE_ACCOUNT_REG_1', 'Your account has been successfully created. ');
define('MANUAL_ACTIVATE_ACCOUNT_REG_2', 'now');
define('MANUAL_ACTIVATE_ACCOUNT_REG_3', '');
define('MANUAL_ACTIVATE_EMAIL_EXIST_LINK', 'e-mail=');

define('EMAIL_ACCOUNT_EDIT_PASSWORD_CHANGE_SUBJECT', 'Password Changed');
define('EMAIL_ACCOUNT_EDIT_PASSWORD_CHANGE', 'Dear Valued Customer,' . "\n\n" . 'This is to inform you that your password has been changed.' . "\n\n" . 'Please contact our live support or <NAME_EMAIL> if you think someones has access to the email address associated with your OffGamers account. Kindly change the password for your email as well.');
?>