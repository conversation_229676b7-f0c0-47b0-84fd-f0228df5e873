<?
/*
  	$Id: account_history_info.php,v 1.33 2013/05/13 07:23:07 hungnian.yong Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2002 osCommerce
	
  	Released under the GNU General Public License
*/

//@************
define('NAVBAR_TITLE_1', 'My Account');
define('NAVBAR_TITLE_2', 'Buy Order History');

if ($history_type=='buyback') {
	define('NAVBAR_TITLE_3', 'Buyback Orders');
	define('NAVBAR_TITLE_4', 'Buyback Order Details');	
	define('HEADING_TITLE', 'Buyback');
	define('HEADING_ORDER_NUMBER', 'Buyback Order No.: %s'); //@************
} else {
	$history_type = 'orders';
	$navbar3 = ($orders_type=="current" ? MY_ACCOUNT_CURRENT_ORDERS : ($orders_type=="completed" ? MY_ACCOUNT_COMPLETED_ORDERS : ""));
	define('NAVBAR_TITLE_3', $navbar3);
	define('NAVBAR_TITLE_4', 'Buy Order Details');
	define('HEADING_TITLE', 'Buyer');
	define('HEADING_ORDER_NUMBER', 'Order No.: %s');
}

define('ALERT_ORDER_TELEPHONE_VERIFICATION', "<div style=\"display:inline-block; font: bold 16px Arial,Tahoma,Verdana,Helvetica,sans-serif; color: black; padding: 0px 0px 10px 5px;\">You have not verified your telephone number associated with this order.<div style=\"display: block; font-weight: normal;\"><b>%s</b> to request for a telephone verification when you are available to pick up the call.</div></div>");
define('ALERT_ORDER_STATUS_PENDING', "<table><tr><td><div style=\"display:block; font: bold 16px Arial,Tahoma,Verdana,Helvetica,sans-serif; color: black; padding: 0px 0px 10px 5px;\">Your order will be processed once payment is received.<div style=\"display: block; font-weight: normal;\">Often a payment may be held up due to mandatory verification checks carried by our payment processors.
												If you have already been charged for an order but find that your Order is under the Pending status please verify that your bank account, card or e-wallet has been charged before contacting our live support staff.
									 		   </div></div></td></tr></table>");
define('ALERT_ORDER_STATUS_PENDING_CURRENCY', "<table><tr><td><div style=\"display:block; font: bold 16px Arial,Tahoma,Verdana,Helvetica,sans-serif; color: black; padding: 0px 0px 10px 5px;\">Please confirm all deliveries made.<div style=\"display: block; font-weight: normal;\">Make sure all deliveries for your orders are confirmed via the order status page once they are made.
												Should there be no confirmation by you, deliveries will be automatically confirmed after 72 hours from the moment the supplier notifies us that the deliveries have been made. <b>This will also ensure that your order is processed sooner so that the OP can be deposited into your account.</b>
									 		   </div></div></td></tr></table>");
define('DATE_FORMAT_ORDER_DATE', '%d %b %Y');
define('DATE_FORMAT_ORDER_MESSAGE', '%d %b, %Y');

define('HEADING_ORDER_STATUS', 'Status: %s');
define('HEADING_ORDER_DATE', 'Order Date: %s');
define('HEADER_DELIVERY_DISPUTE', 'Delivery Dispute');

define('TITLE_PRODUCT_LIST', 'PRODUCT LIST');
define('TITLE_QUANTITY', 'QTY.');
define('TITLE_AMOUNT', 'AMOUNT');
define('TITLE_WHAT_TRADING_METHOD', 'What was the trading method used for delivery?');

define('SUB_TITLE_SUB_TOTAL', 'Sub Total: %s'); 
define('SUB_TITLE_ESTIMATED_TOTAL', 'Estimated Total: %s');
define('SUB_TITLE_AMOUNT_DELIVERED', 'Amount Delivered: %s');
define('SUB_TITLE_AMOUNT_REFUNDED', 'Amount Refunded: %s');

define('TEXT_REASON', 'Reason:');
define('TEXT_DETAILS', 'Details:');
define('TEXT_DISPUTE_DESC', 'You are about to file a report on a delivery that you did not receive. Upon receiving your report, we will investigate your claim and we will get back to you on the result of the investigation at the soonest possible time.');
define('TEXT_DISPUTE_DETAILS', 'Details (if any)');
define('TEXT_CHARACTER_NAME', 'Character Name: %s');
define('TEXT_IN_GAME_TIME', 'In Game Time: %d Hrs after checkout');
define('TEXT_IN_GAME_DURATION', 'In Game Duration: %d Hr(s)');
define('TEXT_TOTAL_DELIVERED_GOLD', 'Total Delivered: %s ');
define('TEXT_GOLD', 'Gold');
define('TEXT_CURRENT_LEVEL', 'Current Level: %s');
define('TEXT_LEVEL', 'Level');
define('TEXT_HOURS_PASSED', 'Hours Passed: %s');
define('TEXT_HOURS', 'Hours');
define('TEXT_REFUNDED', 'Refunded');
define('TEXT_SHOW_ALL', 'Show All');
define('TEXT_HIDE_ALL', 'Hide All');
define('TEXT_DOWNLOAD_ALL', 'Download All');
define('TEXT_GIFT_CARD', 'Gift Card:');
define('TEXT_AMOUNT_IN_RED', '<span style="color:red;">- %s</span>');
define('TEXT_DISCOUNT_COUPON_NUMBER', 'Discount Coupon: %s');
define('TEXT_DISCOUNT_AMOUNT', '<span style="color:red;">- %s</span>');
define('TEXT_STORE_CREDIT', 'Store Credit: ');
define('TEXT_STORE_CREDIT_AMOUNT', '<span style="color:red;">- %s</span>');
define('TEXT_PAYMENT_METHOD', 'Payment Method: %s');
define('TEXT_IS_UNLOADING', 'Unloading ...');
define('TEXT_REMAINING_TIME', 'Remaining Time');
define('TEXT_AUTO_CONFIRM_DELIVERY_NOTE', 'Delivery will be automatically confirmed in 72 hours');
define('TEXT_DID_YOU_RECEIVE', 'Did you receive?');
define('TEXT_DID_YOU_ABLE_TO_LOGIN', 'Did you able to login?');
define('TEXT_AUTO_CONFIRM_DELIVERED', 'Delivery will be automatically confirmed in 72 hours.');
define('TEXT_DELIVERY_CONFIRMED', 'Delivery Confirmed');
define('TEXT_DID_NOT_RECEIVE', 'Did not receive');
define('TEXT_ROLLED_BACK_DELIVERED_AMT', 'Delivered amount has been rolled back by system');
define('TEXT_HLA_AUTO_CONFIRM_DELIVERY_NOTE', '<table cellspacing="0" cellpadding="0" border="0"><tr><td nowrap valign="top"><font color="#FF0000"><b>Important :&nbsp;</b></font></td><td><font color="#FF0000">Be inform that order will be completed by system in 3 days, if there no any complain received. <br />We are apologize and please understand that in order to ensure and maintain account security, all order are non-refundable after the account information had delivered.</font></td></tr></table>');
define('TEXT_HLA_SECRET_ANSWER_NOTE', 'Secret Question and Answer: <br /> will delivered by System after confirmation had made By Clicking "Yes" able to login');

define('TEXT_TOP_UP_STATUS', 'Top-up Status');
define('TEXT_TOP_UP_MESSAGE', 'Top-up Message');

define('TEXT_BUYBACK_OPENED', 'Buyback Opened');
define('TEXT_BUYBACK_CLOSED', 'Buyback Closed');
define('TEXT_BUYBACK_OPENED_DURATION', 'Duration set to (%s) hrs ingame.');
define('TEXT_BUYBACK_OPENED_DURATION_EXTEND', 'Duration set to + (%s) hrs ingame extended time');
define('TEXT_BUYBACK_CLOSED_UNABLE_LOCATE_CUSTOMER', 'Unable to locate a customer for this order (%s)');
define('TEXT_BUYBACK_CLOSED_DURATION_TIME_OUT', 'In-game duration time out');
define('TEXT_BUYBACK_CLOSED_MANUALLY', 'User set -(%s) hrs manual time out');

define('TEXT_TOTAL_ACCUMULATED_OPEN_TIME', 'Total Accumulated buyback Open time : ( %s )');
define('TEXT_PROVIDE_DELIVERY_INFO', 'You did not provide any delivery information. <a href="javascript:void(0);" onClick="showMe(\'%s\');"><b>Click here now</b></a> to provide your delivery info so that we can proceed to arrange for delivery(s).');

define('BUTTON_EXTEND_TIME', 'EXTEND TIME');
define('BUTTON_IM_ONLINE_NOW', 'I AM ONLINE NOW');
define('ENTRY_IN_GAME_DURATION', 'In Game Duration:');
define('ENTRY_HOURS', 'Hr(s)');

define('IMAGE_BUTTON_SUBMIT_PAYMENT_INFO', 'Submit payment info');
define('IMAGE_BUTTON_PAY_FOR_THIS_ORDER_NOW', 'Pay for this order now');
//define('IMAGE_BUTTON_SHOW_CD_KEY_CODE', 'Show CD Key Code');
define('IMAGE_BUTTON_SHOW', 'Show');
define('IMAGE_BUTTON_HIDE', 'Hide');
define('IMAGE_BUTTON_HIDE_CD_KEY_CODE', 'Hide CD Key Code');
define('IMAGE_BUTTON_CHARACTERS_PROFILER', 'Character\'s Profiler');
define('LINK_CANCEL_THIS_ORDER', 'Cancel this order');
define('LINK_COMPLAINT_THIS_ORDER', 'Complain this order');

define('ERROR_ORDER_IN_TRADING', 'Your order is already in trading mode. Please contact our customer support for further assistance.');
define('ACCOUNT_HISTORY_DELIVERY_MODE_NOTES', "<b>Important:</b> We\'ll do our best to make the arrangements required for the chosen delivery method. OffGamers will not be held liable should any trade or deliveries be completed before the changes are applied to your order.");

define('MESSAGE_ORDER_CANCELLED', 'This order will be cancelled in %s hours %s mins.');
define('MESSAGE_PAYMENT_INFORMATION', 'If you have already made the payment for this order, please submit your payment information now.');

define('HEADER_ORDER_MESSAGE', 'ORDER MESSAGE');
define('HEADER_DELIVERY_INFO_TITLE', 'We will start sourcing for your order as soon as you are able to log on the game.');
define('HEADER_PWL_DELIVERY_INFO_TITLE', 'We will start sourcing for your order as soon as your powerleveling order completed. For further information please contact our live support.');
define('HEADER_DATE', 'Date');
define('HEADER_TIME', 'Time');
define('HEADER_DELIVERED_AMOUNT', 'Delivered Amount');
define('HEADER_DELIVERED_CHARACTER', 'Delivered Character');
define('HEADER_ACTION', 'Action');


// Cancel Customer Order
define('BUTTON_CANCEL_ORDER_NO', 'No, I\'m reconsidering');
define('BUTTON_CANCEL_ORDER_YES', 'Yes, please cancel');
define('BUTTON_CANCEL_ORDER_OK', 'OK');

define('HEADING_TITLE_CANCEL_ORDER', 'Order Cancellation Request');

define('TITLE_CANCEL_ORDER_NUMBER', 'Order Number:');
define('TITLE_CANCEL_ORDER_PAYMENT_METHOD', 'Payment Method:');
define('TITLE_CANCEL_ORDER_AMOUNT', 'Amount:');

define('TEXT_DISCOUNT_COUPON', 'Discount Coupon');

define('MESSAGE_CANCEL_ORDER_CONFIRM', 'Are you sure you want to cancel the order?');
define('MESSAGE_CANCEL_ORDER_CANCELLED_1', 'This order has been cancelled.');
define('MESSAGE_CANCEL_ORDER_CANCELLED_2', 'This order has been cancelled. Store Credits will be refunded within 24hours.');
define('MESSAGE_CANCEL_ORDER_CANCELLED_3', 'This order is currently in queue, pending for cancellation. If your orders are associated with store credits, please kindly contact our 24/7 live support to expedite the cancellation process.');
define('MESSAGE_UNAUTHORISED_CANCEL_ORDER', 'This Order is not belong to you. You have no authorisation to cancel it.');
?>