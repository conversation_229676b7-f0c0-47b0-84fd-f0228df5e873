<?
/*
  	$Id: account_payment_edit.php,v 1.11 2012/08/02 07:04:22 weichen Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2003 osCommerce
	
  	Released under the GNU General Public License
*/
define('NAVBAR_TITLE_1', BOX_HEADING_LOGIN_BOX_MY_ACCOUNT);
define('NAVBAR_TITLE_2', LOGIN_BOX_ACCOUNT_PAYMENT_EDIT);

if ($action == 'add_pm') {
	define('HEADING_TITLE', 'Add Payment Method');
} else if ($action == 'edit_pm') {
	define('HEADING_TITLE', 'Edit Payment Method');
} else {
	define('HEADING_TITLE', LOGIN_BOX_ACCOUNT_PAYMENT_EDIT);
}

define('TABLE_HEADING_PM_PAYMENT_ACCOUNT', 'Payment Account');
define('TEXT_CONFIRM_DELETE', 'Are you sure you want to delete?');
define('TEXT_CONFIRM_DELETE_RECORD', ' record?');

define('TEXT_BUYBACK_LINK', 'Sell Your In-Game Currencies');
define('TEXT_ANNOUNCEMENT', '<span style="color: red">*Important*</span> Please kindly note that effective from 27 October 2010, suppliers will automatically be merged into a new and revised PayPal (in USD) as a disbursement method. The Paypal (in USD) new version will result in revised fees which will be lower than the Paypal (in USD) old version. For further information, please refer to <a href="my_payment_history.php?action=show_balance#paypal">here</a>.');

//For payment module------------------------------------------------------------
define('TEXT_ALL_PAGES', 'All pages');
define('HEADER_FORM_ACC_STAT_TITLE', 'Account Statement');
define('ENTRY_ORDER_START_DATE', 'Start Date<br><small>(YYYY-MM-DD)</small>');
define('ENTRY_ORDER_END_DATE', 'End Date<br><small>(YYYY-MM-DD)</small>');
define('ENTRY_PAYMENT_ID', 'Payment ID');
define('ENTRY_ORDER_ID', 'Order ID');
define('ENTRY_RECORDS_PER_PAGE', 'Records per page');
define('ENTRY_PAYMENT_STATUS', 'Payment Status');
define('TEXT_LANG_RECORDS_PER_PAGE', '');
define('TABLE_HEADING_ACC_STAT_DATETIME', 'Date/Time');
define('TABLE_HEADING_ACC_STAT_ACTIVITY', 'Activity');
define('TABLE_HEADING_ACC_STAT_PAYMENT_STATUS', 'Payment Status');
define('TABLE_HEADING_ACC_STAT_DEBIT', 'Debit');
define('TABLE_HEADING_ACC_STAT_CREDIT', 'Credit');
define('TABLE_HEADING_ACC_STAT_BALANCE', 'Balance');
define('TEXT_ACC_STAT_NOT_APPLICABLE', '-');
define('TEXT_ACC_STAT_PAYMENT_STATUS_UPDATE_DATE', '%s on %s');
define('TEXT_ACC_STAT_ADMIN_COMMENT', 'Comment:');
define('SECTION_TITLE_FORM_PM_REQUIRED_INFO', 'Required Information:');
define('TEXT_PM_NO_EXISTING_PAYMENT_ACCOUNT', 'You have 0 payment account in record.<br><br>If you intend to sell to OffGamers, please click on ADD METHOD to setup a new payment account.');
define('LINK_PM_EDIT_PM_ACCOUNT', 'Edit payment account');
define('LINK_PM_DELETE_PM_ACCOUNT', 'Delete payment account');
define('SUCCESS_PM_DELETE_PM_ACCOUNT', 'Success: Payment account has been successfully deleted.');
define('ERROR_INVALID_PM_BOOK_SELECTION', 'Error: You are editing invalid payment account.');
define('ERROR_INVALID_PM_SELECTION', 'Error: Please select valid payment method.');
define('ERROR_EMPTY_PM_ALIAS', 'Error: Payment title is required.');
define('ERROR_PM_FIELD_INFO_REQUIRED', 'Error: Please provide "%s" information!');
define('ERROR_PM_ACCOUNT_BOOK_FULL', 'Your payment account book is full. Please delete an unneeded account to save a new one.');
define('ENTRY_FORM_PM_SELECT_CURRENCY', 'Payment Currency:');
define('ENTRY_FORM_PM_SELECT_PM', 'Payment Method:');
define('ENTRY_FORM_PM_ALIAS', 'Payment Description:');

// Edit Payment Account Information
define('TABLE_HEADING_PAYMENT_ACCOUNT', 'PAYMENT ACCOUNT');
define('TABLE_HEADING_PAYMENT_INFO', 'PAYMENT ACCOUNT DETAILS');
define('TABLE_HEADING_PAYMENT_ACTION', 'ACTION');
define('TEXT_PM_REACHED_MAX', 'You have reached %d maximum disbursement methods.<br>You will have to remove some to add more.');
//--end for payment module--------------------------------------------------------
?>