﻿<?
/*
  	$Id: account_store_credit_history.php,v 1.7 2013/05/10 05:02:43 hungnian.yong Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2003 osCommerce
	
  	Released under the GNU General Public License
*/
define('NAVBAR_TITLE_1', BOX_HEADING_LOGIN_BOX_MY_ACCOUNT);
define('NAVBAR_TITLE_2', LOGIN_BOX_ACCOUNT_STORE_CREDIT);

define('NAVBAR_TITLE', HEADER_TITLE_MY_ACCOUNT);
define('HEADING_TITLE', LOGIN_BOX_ACCOUNT_STORE_CREDIT);

define('TABLE_HEADING_STORE_CREDIT_HISTORY', 'My Store Credits History');

define('TAB_HEADING_HISTORY', 'History');
define('TAB_HEADING_STORE_CREDIT_PURCHASED', 'Store Credits Purchased');
define('TAB_HEADING_STORE_CREDIT_USED', 'Store Credits Used');

define('TABLE_HEADING_HISTORY_DATE', 'DATE');
define('TABLE_HEADING_HISTORY_TYPE', 'TYPE');
define('TABLE_HEADING_HISTORY_AMOUNT', 'AMOUNT');
define('TABLE_HEADING_HISTORY_BALANCE', 'BALANCE');

define('TABLE_HEADING_SC_PURCHASED_DATE', 'DATE');
define('TABLE_HEADING_SC_PURCHASED_ORDER', 'ORDER #');
define('TABLE_HEADING_SC_PURCHASED_AMOUNT', 'AMOUNT');

define('TABLE_HEADING_SC_USED_DATE', 'DATE');
define('TABLE_HEADING_SC_USED_ORDER', 'ORDER #');
define('TABLE_HEADING_SC_USED_AMOUNT', 'AMOUNT');

define('TEXT_SC_HISTORY_STATUS_MI', 'Manual Addition');
define('TEXT_SC_HISTORY_STATUS_MR', 'Manual Deduction');
define('TEXT_SC_HISTORY_STATUS_PW', 'OffGamers Payment');
define('TEXT_SC_HISTORY_STATUS_R', 'Refund');
define('TEXT_SC_HISTORY_STATUS_V', 'Store Credits Conversion');
define('TEXT_SC_HISTORY_STATUS_X', 'Cancellation');
define('TEXT_SC_HISTORY_STATUS_D', 'OP Redeem');
define('TEXT_SC_HISTORY_STATUS_S', 'Store Credits Delivered');
define('TEXT_SC_HISTORY_STATUS_XS', 'Bonus Store Credits Delivered (+%s%%)');
define('TEXT_SC_HISTORY_STATUS_C', 'Compensation');
define('TEXT_SC_HISTORY_STATUS_P', 'Store Credits Spent');
define('TEXT_SC_HISTORY_STATUS_GC', 'Gift Card Redemption');

define('TEXT_SC_HISTORY_STATUS_S_DEDUCT', 'Store Credit Deduction (Invalid Transaction)');
define('TEXT_SC_HISTORY_STATUS_XS_DEDUCT', 'Bonus Store Credits Deduction (Invalid Transaction)');

define('TEXT_MY_STORE_CREDIT_BALANCE', 'My Store Credits Balance: <b>%s</b>');
define('TEXT_TOP_UP_STORE_CREDITS', 'Top Up Store Credits');
define('TEXT_OR_CONVERT_NOW', 'or Convert Now');
?>