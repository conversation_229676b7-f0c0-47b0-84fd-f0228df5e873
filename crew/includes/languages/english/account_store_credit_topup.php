<?php

/*
  $Id: account_store_credit_topup.php,v 1.13 2013/11/26 09:44:20 weesiong Exp $

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2003 osCommerce

  Released under the GNU General Public License
 */
define('NAVBAR_TITLE_1', BOX_HEADING_LOGIN_BOX_MY_ACCOUNT);
define('NAVBAR_TITLE_2', LOGIN_BOX_ACCOUNT_STORE_CREDIT);

define('NAVBAR_TITLE', HEADER_TITLE_MY_ACCOUNT);
define('HEADING_TITLE', LOGIN_BOX_ACCOUNT_STORE_CREDIT);

define('TABLE_HEADING_STORE_CREDIT_TOPUP', 'Top Up Store Credits');
define('TABLE_HEADING_STORE_CREDIT_TOPUP_TEXT', 'Use Store Credits as an alternative payment method to using cash or credit cards when making purchases on OffGamers. With a readily available line of credit in your account in the form of store credits, purchases and pre-orders can be made quicker with minimal hassle.');
define('TABLE_HEADING_STORE_CREDIT_TOPUP_NEWS_TEXT', 'We are pleased to inform everyone that we\'ve added a new patch to our servers and expect instantaneous checkout on Store Credits purchases at <a href="' . tep_href_link(FILENAME_ACCOUNT_STORE_CREDIT_TOPUP, '', 'SSL') . '">OffGamers.com</a>. The Store Credits will be available in your account instantly, after completing the transaction successfully.');
define('TABLE_HEADING_STORE_CREDIT_TOPUP_PROMOTION_TEXT', 'Get %f% extra Store Credit with every purchase!');
define('TEXT_MINIMUM_STORE_CREDITS_TOPUP', 'Minimum purchase of Store Credit for %s: %s.');

define('ENTRY_FORM_BUY_TEXT', 'I want to buy <b>%s</b> ');
define('ENTRY_FORM_STORE_CREDITS_TEXT', '<b>%s</b> Store Credits');

define('QUESTION_AND_ANSWER_HEADER', 'QUESTION & ANSWER');
define('QUESTION_AND_ANSWER_CONTENTS', '<table width="100%"><tr><td width="35px" style="text-align:left;vertical-align: top;"><div style="padding:0px 10px;text-align:center;">' . tep_image(DIR_WS_ICONS . 'note.gif', ICON_SUCCESS) . '</div></td><td width="*%" align="left">Please kindly note that store credits are non-refundable or withdrawable once purchase is made and credited</td></tr></table><br><b>How Do I Buy Store Credits?</b><br><br>

Navigate <a href="http://kb.offgamers.com/en/2013/05/29/ordering-store-credits/" target="_blank">Here</a> to learn how to purchase Store Credits for your account.');

define('TEXT_INFO_MINIMUM_ORDERS_SC', 'You have entered an invalid store credit amount.');
define('TEXT_INFO_EXTRA_SC_PROMO', 'Get <font style=\'font-family: Arial, Verdana, Helvetica, sans-serif;font-size: 15px;font-weight: bold;text-transform: normal;display: inline;color: #004B91;text-decoration: none;\'>%s%% extra Store Credit</font> with every purchase.');
define('TEXT_INFO_ENTERED_SC_PROMO_AMOUNT1', 'Price: ');
define('TEXT_INFO_ENTERED_SC_PROMO_AMOUNT2', ' for ');
define('TEXT_INFO_ENTERED_SC_PROMO_AMOUNT3', ' Store Credits');

define('TEXT_LAST_DAY_ENJOY_EXTRA_SC', 'Last Day to Enjoy 5% Extra Store Credit will be on 26 May 2013. <a href=http://www.offgamers.com/extra-5-store-credits-ln-4812.ogm" rel="_nofollow" target="_blank">More info here</a>');
?>