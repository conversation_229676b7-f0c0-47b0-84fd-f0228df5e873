<?php

/*
  $Id: buyback.php,v 1.42 2013/08/02 05:03:29 chingyen Exp $

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2003 osCommerce

  Released under the GNU General Public License
 */
define('NAVBAR_TITLE_1', 'SELL YOUR GAME CURRENCY');
define('NAVBAR_TITLE', 'SELL YOUR GAME CURRENCY');
define('HEADING_TITLE', 'SELL YOUR GAME CURRENCY');

define('TEXT_HEADING_SERVER', 'Server');
define('TABLE_HEADING_SELLER_INFO', 'SELLER INFORMATION');

define('TEXT_SELL_THIS_LINK', 'Sell this');
define('TEXT_ADD_TO_FAVOURITES_LINK', 'Add to favourites');

define('TABLE_HEADING_STEP_1', '1. Select game and server');
define('TABLE_HEADING_STEP_2', '2. Enter selling amount :');
define('TABLE_HEADING_STEP_3', '3. Review our offer :');
define('TABLE_HEADING_SUBMITTED_INFO', 'Submitted Information');

define('ENTRY_CHARACTER_NAME', 'Character ID');
define('ENTRY_DELIVERY_TIME', 'Delivery Time');
define('ENTRY_NAME', 'Name');
define('ENTRY_CONTACT_NO', 'Contact Number');
define('ENTRY_ADDITIONAL_COMMENTS', 'Additional Comments');
define('ENTRY_SELECT_GAME', 'Select Game');
define('ENTRY_SELECT_SERVER', 'Server');
define('ENTRY_ENTER_SELLING_AMOUNT', 'Enter Selling Amount');
define('ENTRY_DEALING_TYPE', 'Dealing Type');
define('ENTRY_GAME_SELECTION', 'Game Selection');
define('ENTRY_SERVER_SELECTION', 'Server Selection');
define('ENTRY_MEETING_POINT', 'Meeting Point');
define('ENTRY_TRADE_METHOD', 'Trade Method');
define('ENTRY_SELLING_QTY', 'Selling Qty');
define('ENTRY_TOTAL_SELLING_PRICE', 'Total Selling Price');
define('ENTRY_CHARACTER_ID', 'Character ID');
define('ENTRY_CONTACT', 'Contact');
define('ENTRY_COMMENTS', 'Comments');

define('TEXT_DEALING_TYPE_ON_GAME', 'Face to face Transaction');
define('TEXT_DEALING_TYPE_ON_MAIL', 'Mail Transaction');

define('TEXT_MATCH_BACKORDER_EXACT_AMOUNT', '(If submit more than %d, we only accept %d, we do not accept any quantity in the range %d-%d)');
define('TEXT_MEETING_POINT', 'Meeting Point');
define('TEXT_CHARACTER_NAME_HELP', '<span class="redIndicator">* Note: Kindly provide your character name for us to arrange the trade.</span>');
define('TEXT_DELIVERY_TIME_HELP', '<span class="redIndicator">* Note: This order will be canceled when delivery time have been exceeded.</span>');
define('TEXT_BUYBACK_CONTACT_NO_HELP', '<span class="redIndicator">* Note: Kindly provide a valid contact number so that we are able to reach you and arrange delivery in-game.</span>');
define('TEXT_ORDER_POSTED_INSTRUCTIONS', 'Your sell order has been submitted Successfully. Click Transfer Now to arrange for a transfer of your currencies right away of Sell More to submit another sell order.');
define('TEXT_OF', 'of');
define('TEXT_NOTES', 'Notes');
define('TEXT_TOTAL_PRICE', 'Total Price');
define('TEXT_ADD_SHORTCUT_TO', 'Add Server Shortcut to :');
define('TEXT_ADDITIONAL_COMMENTS', 'Additional Comments:');
define('TEXT_TERMS_AND_CONDITIONS', 'Terms and Conditions');
define('TEXT_NO_ITEMS_TO_BUYBACK', 'No items to buyback.');
define('TEXT_GAME', 'game : ');
define('TEXT_SERVER', 'server : ');
define('TEXT_PRICE_PER_UNIT', 'Price per unit');
define('TEXT_PRODUCT', 'Product');
define('TEXT_FOR_YOUR', 'for your');
define('TEXT_ON_SERVER', 'on server');
define('TEXT_BUTTON_LABEL_STEP2', 'Next');
define('TEXT_BUTTON_LABEL_STEP3', 'I ACCEPT');
define('TEXT_BUTTON_LABEL_UPDATE', 'UPDATE');
define('TEXT_SELECT_YOUR_GAME', '-- Select Your Game--');
define('TEXT_SELECT_YOUR_SERVER', '-- Select Your Server--');
define('ERROR_BUYBACK_QUANTITY_INVALID', "You have entered an incorrect amount. <br/>Please enter an amount within the minimum and maximum quantity.");
define('ERROR_QUANTITY_INVALID', "Quantity Invalid");
define('TEXT_FORM_INCOMPLETE', "You have not entered all the required fields.<br/>Please try again.");
define('TEXT_FORM_AGREEMENT_ALERT', 'You must accept the agreement to proceed.');
define('WARNING_BUYBACK_FORM_STILL_PROCESSING', 'We are still processing your last buyback order for this server.<br />Please try a different server.');
define('TEXT_MAIN', 'Please select a server and the name of the item you want to sell.');
define('TEXT_NOT_ACCEPT_BUYBACK', " Sorry we are not currently accepting buybacks for the selected server.<br /> Please check again in 24 hours or e-mail to <a href=\"mailto:<EMAIL>\"><EMAIL></a> for further details.");
define('TEXT_EMAIL_IF_MORE_SUPPLY', 'Please kindly E-mail <a href="mailto:<EMAIL>"><EMAIL></a> if you have more than the maximum quantity stated or have a constant supply of in-game currencies available.');
define('TEXT_NO_PAYMENT_ACCOUNT', "Kindly set up a payment account before proceeding with your buyback order.");
define('TEXT_BUYBACK_IS_OFF', 'We have temporarily turned off website buyback. Please check again in 24 hours or e-mail to <a href="mailto:<EMAIL>"><EMAIL></a> for further details. Click <a href="http://www.offgamers.com">here</a> to return to the main page');

define('BUYBACK_FORM_FOOTER_MESSAGE', '	Select from our list of games and choose your server from the server list. Due to large demands from our customers, we constantly update our inventory database of game servers. Supply your WoW gold or any other MMO game currency to us and get instant payment. Our buyback team operates 24/7 and will be there to assist you in your buyback order.<br><br>
										Besides WoW Gold and MMO game currencies, we are also buying MMO game accounts. Sell your account to us for a price offer that you won\'t deny. We offer prices that are beyond your expectations for MMO game account selling. <a href="http://www.offgamers.com/account_buyback.php">Sell account here</a>.<br><br>
										Note: We do not support or buy game currencies acquired through unehtical methods/sources(bots, dupes, hacks etc).');

define('TEXT_AGREEMENT_ACCEPTANCE', 'I have read and therefore accept the agreement.');

define('TEXT_CHAR_DISCONNECTED', 'Our Receiving Character has been disconnected from the game due to unforeseen circumstances, please allow us 3minutes to resolve this matter and complete the transfer with you. Your patience is highly appreciated');

define('ERROR_BUYBACK_SENDER_ID_EXISTS', 'Error: Sender Character ID has been exists, please check your Sender Character ID');
define('ERROR_BUYBACK_NOT_BUYBACK_GAME', 'Error:, We are currently unable to accept any buybacks for the time being, please do check back with us again later.');
define('ERROR_MANDATORY_ISBLANK', 'Incomplete field : %s');
define('ERROR_SUBMITTED_BY_OTHER_USER', 'This order has been submitted by other user. Please try again.');
define('ERROR_BUYBACK_QTY_NOT_EXIST', 'Buyback quantity does not exists. Please re-submit');
define('ERROR_BUYBACK_ORDER_CLOSED', 'Sorry! This order has been closed. Please refresh the server to get the latest order.');

define('TEXT_DEALING_TYPE', 'Dealing Type');
define('TEXT_DELIVERY_TIME', 'Delivery Time');
define('TEXT_NAME', 'Name');
define('TEXT_TERMS_AND_CONDITIONS_AGREEMENT', 'Terms and Conditions Agreement');

define('TEXT_NOTICE_MSG', 'Reminder: Kindly confirm ID of receiver, beware of scammer.');
define('TEXT_HEADING_STEP1', 'Step 1 : Specify server and quantity');
define('TEXT_HEADING_STEP2', 'Step 2 : Specify personal details');
define('TEXT_HEADING_STEP3', 'Step 3 : Confirmation');
define('TEXT_HEADING_SERVER_LIST', 'Server Listing');
define('TEXT_HEADING_UNIT_PRICE', 'Unit Price');
define('TABLE_HEADING_PURCHASE_QTY', 'Quantity');
define('TEXT_HEADING_STATUS', 'Status');
define('TEXT_HEADING_SUMMARY', 'Order Summary');
define('TEXT_HEADING_CHARACTER_INFO', 'Character Info');
define('TEXT_HEADING_CONTACT_INFO', 'Contact Info');
define('TEXT_HEADING_PAYMENT_INFO', 'Payment Info');
define('TEXT_SELECT_LINK', 'Select');
define('TEXT_MIN', 'Min : ');
define('TEXT_MAX', 'Max : ');
define('TEXT_OF', 'of');
define('TEXT_OR', 'or');
define('TEXT_NOTES', 'Notes:');
define('TEXT_UNIT_PRICE', 'Unit Price : ');
define('TEXT_ADD_TO_MANUFACTURER_LIST', 'Manufacturer List');
define('TEXT_INSTRUCTIONS_HEADER1', 'Buyback');
define('TEXT_INSTRUCTIONS_LEFT', 'Kindly provide the following details and proceed to the next step.');
//define('TEXT_CHARACTER_NAME_RECEIVER', 'Receiver Character ID');
define('TEXT_CONTACT_NAME', 'Name');
define('TEXT_PAY_TO_ACCOUNT', 'Pay to');
define('TEXT_PAY_TO_ACCOUNT_HELP', 'Kindly select an account for us to deliver payment.');
define('TEXT_ORDER_REFERENCE', 'Order Reference');
define('TEXT_ADD_NEW', 'Place another order');
define('TEXT_DELIVER_NOW', 'Deliver now');
define('TEXT_MANDATORY_ISBLANK', 'Incomplete field : %s');
define('TEXT_FORM_MAX_ORDERS_REACHED', 'You have reached the maximum number of Pending buyback orders. Please wait for those orders to complete before submitting more orders.');
define('TEXT_INCOMPLETE_FIELDS', 'Incomplete field(s) detected. Please try again.');
define('TEXT_INVALID_SEVER_SELECTION', 'Invalid Game/Server Selection. Please try again.');
define('TEXT_QTY_DELIVERED', 'Qty. Delivered');
define('TEXT_DELIVERED_ID', 'Delivered ID');

define('BUTTON_TRADE_NOW', 'Trade Now');

define('EMAIL_SEPARATOR', '------------------------------------------------------');
define('EMAIL_NEW_BUYBACK_SUBJECT', implode(' ', array(EMAIL_SUBJECT_PREFIX, "New Buyback Order #%s")));
define('EMAIL_NEW_BUYBACK_BODY', "Thank you for selling your items to " . STORE_NAME . ".\n\n Supplier Order Summary:\n" . EMAIL_SEPARATOR . "\n");
define('EMAIL_NEW_BUYBACK_PRODUCTS', "\n\n" . 'Products');
define('EMAIL_NEW_BUYBACK_ORDER_TOTAL', 'Total: %s');
define('EMAIL_NEW_BUYBACK_COMMENTS', 'Extra Information:');
define('EMAIL_NEW_BUYBACK_ORDER_CLOSING', "Please contact us now via our Online Live Support service or e-mail to " . EMAIL_TO . " to arrange a time for collection of all the buyback items.  Please remember to include your Buyback Order Number when contacting us to expedite the process.\n\nYou will receive an e-mail notification whenever there is an update in your buyback order status.  You can also review your buyback orders and check their status by signing in to OffGamers.com and clicking on \"View Buyback Order History\".\n\nThank you for supporting " . STORE_NAME . '.');
define('EMAIL_FOOTER', "\n\n" . STORE_EMAIL_SIGNATURE);
define('EMAIL_NEW_BUYBACK_ORDER_FOOTER', "\n\n" . EMAIL_FOOTER);

define('LINK_SELL_YOUR_GAME_CURRENCY_PROCEDURE', 'Sell your game currency procedure');
define('LINK_SELLER_GUIDE', 'Seller Guide');
define('LINK_TERMS_N_CONDITIONS', 'Terms & Conditions');
define('LINK_WOW_SELLER_GUIDE', 'WOW - Seller Guide');

define('LINK_READ_MORE', 'Read More');


define('TITLE_WHY_SELL_TO_US', "Why sell to us?");
//define('TITLE_RULES_OF_SELLING', "Rules of Selling");
define('TEXT_WHY_SELL_TO_US', "1) We assure a quick and secure trade. <br/><br/> 2) Our list is updated in real time. <br/><br/> 3) We connect you to serious buyers. <br/><br/> 4) Zero risk of payment disputes.<br/><br/>");
//define('TEXT_RULES_OF_SELLING', "1) You read our Buyback FAQ. How to sell my virtual currencies. <br/><br/> 2) You need to sell my virtual currencies. <br/><br/> 3) OffGamers Buyback Guide. Have you read. <br/><br/> 4) Our Buyback FAQ. <a href='mailto:<EMAIL>'>welisten.cn<br/>@offgamers.com</a> different for selling and buyback.");

define('HEADER_MY_SELLING_CART', "MY SELLING CART");
define('HEADER_MY_VIP_SELLING_CART', "MY VIP SELLING CART");

define('WARNING_PLS_SELECT_GAME', "Please Select Game");
define('WARNING_PLS_SELECT_SERVER', "Please Select Server");
define('WARNING_PLS_FILL_IN_SELLING_QTY', "Please Fill In Selling Qty");
define('WARNING_PLS_FILL_IN_CHAR_ID', "Please Fill In Character ID");
define('WARNING_PLS_FILL_IN_CONTACT', "Please Fill In Contact No.");
define('WARNING_PLS_CHECK_EMAIL_VERIFIED_AND_IM', "Attention!<br/>Please make sure that you insert a valid and active email address, IM (QQ, MSN, etc.), mobile or fix line number when signing up or else you wouldn't be able to place any order if we cannot verify those required information.");
define('WARNING_VERIFY_EMAIL_LINK', "<a href='%s'>Verify your email</a>");
define('WARNING_VERIFY_PHONE_LINK', "<a href='%s'>Phone verification</a>");
define('WARNING_FILL_IN_IM', "<a href='#' id='account_edit_link'>Fill in your IM</a>");

define('TEXT_PRICE_NOTES', 'All prices are set in USD. The non-USD prices are provided solely as a reference base on the daily average currency conversion rate which is updated daily at 8:30am (GMT +8). However, if you place your order after 8:45am (GMT +8)
							and withdraw before 8:30am (GMT +8) the next day, you will get the
							same amount as shown.');

define('TEXT_PRICE_NOTES_2', 'note: you will be credited in USD');
?>
