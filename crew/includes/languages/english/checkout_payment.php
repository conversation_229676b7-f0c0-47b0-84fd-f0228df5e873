<?php
/*
  $Id: checkout_payment.php,v 1.41 2014/08/20 11:04:44 weichen Exp $

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2002 osCommerce

  Released under the GNU General Public License
*/

define('NAVBAR_TITLE_1', 'Shipping');
define('NAVBAR_TITLE_2', 'Additional Information');
define('NAVBAR_TITLE_3', 'Payment Method');

define('HEADING_TITLE', 'Payment Information');
define('HEADING_SUBTITLE', 'SELECT PAYMENT METHOD IN %s');

define('TABLE_HEADING_AMOUNT', 'AMOUNT');
define('TABLE_HEADING_QUANTITY', 'QTY.');
define('TABLE_HEADING_PRODUCT', 'PRODUCT');

define('ENTRY_PAYMENT_SURCHARGE', 'Processing Fee');
define('ENTRY_PAYMENT_CONTACT_NUMBER', 'Contact Number:');
define('ENTRY_PAYMENT_CUSTOMER_IDENTIFY_NUMBER', 'IC number:<BR>(Last 4 Digit)');
define('ENTRY_PAYMENT_CUSTOMER_SURNAME', 'Surname');
define('ENTRY_PAYMENT_CUSTOMER_HOUSENUMBER', 'House Number');
define('ENTRY_PAYMENT_CUSTOMER_STREET', 'Street');
define('ENTRY_PAYMENT_CUSTOMER_ZIP', 'Zip');
define('ENTRY_PAYMENT_CUSTOMER_CITY', 'City');
define('ENTRY_PAYMENT_CUSTOMER_ACCOUNTNAME', 'Account Name');
define('ENTRY_PAYMENT_CUSTOMER_ACCOUNTNUMBER', 'Account Number');
define('ENTRY_PAYMENT_CUSTOMER_DIRECTDEBITTEXT', 'Direct Debit Text');
define('ENTRY_PAYMENT_CUSTOMER_BANKCODE', 'Bank Code');
define('ENTRY_PAYMENT_CUSTOMER_BRANCHCODE', 'Branch Code');
define('ENTRY_PAYMENT_CUSTOMER_VOUCHER_NUMBER', 'Voucher Number');
define('ENTRY_PAYMENT_CUSTOMER_VOUCHER_VALUE', 'Voucher Value');

define('TEXT_INFO_IDENTIFY_NUMBER_EXAMPLE', '(123456-14-<font color="red">XXXX</font>)');
define('TEXT_INFO_CURRENT_CHECKOUT_CURRENCY', 'Current Checkout Currency');
define('TEXT_INFO_CHANGE_CHECKOUT_CURRENCY', 'To view other payment methods, you will have to change your checkout currency.');

define('TEXT_ORDER_FULLY_COVERED_BY_CREDITS', 'You do not need to select a payment method since the total amount can be fully debited from your store credit.');
define('TEXT_ORDER_ZERO_AMOUNT_CHECKOUT', 'You do not need to select a payment method.');
define('TEXT_EDIT_ADDRESS', 'Edit Address?');
define('LINK_EDIT_TELEPHONE', 'Edit Contact Number?');
define('TEXT_FEATURED_PAYMENT_METHODS', 'FEATURED PAYMENT METHODS');
define('TEXT_SHOPPING_CART', 'Shopping Cart: ');
define('TEXT_STORE_CREDIT', 'Store Credit: ');
define('TEXT_TOTAL', 'Total: ');

define('LINK_EDIT_SHOPPING_CART', 'Edit Cart');
define('LINK_UPDATE_CUSTOMER_PAYMENT_INFO', 'Confirm');

define('JS_ERROR_TITLE_PAYMENT_INFORMATION', 'Payment Information Error');

define('TEXT_INFO_PAYMENT_SURCHARGE_DESC', '<b>Processing Fee</b><br />Please take note that processing fee is applicable per transaction, for selected payment methods only. <a href="http://kb.offgamers.com/?p=508" target="_blank">More Info</a>.');

define('TEXT_INFO_PAYMENT_IN_USD_OR', 'OR');
?>