<?php
/*
  $Id: checkout_success.php,v 1.20 2013/05/10 05:04:12 hungnian.yong Exp $

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2002 osCommerce

  Released under the GNU General Public License
*/

define('NAVBAR_TITLE_1', 'Order Placed');
define('NAVBAR_TITLE_SHOPPING_CART', 'Shopping Cart');
define('NAVBAR_TITLE_ORDER_PAYMENT', 'Order Payment');
define('NAVBAR_TITLE_2', 'Success');
define('HEADING_TITLE', 'Finished');
define('HEADING_EMAIL_VERIFICATION_TITLE', 'Confirm your email');
define('HEADING_PHONE_VERIFICATION_TITLE', 'Verify your mobile phone no.');

define('TEXT_SUCCESS', 'Your order has been successfully processed!');
define('TEXT_TITLE_CONFIRM_EMAIL', 'A code will be sent to your e-mail address registered below. You must complete this step in order for us to process your order.');

define('TEXT_HEADING_DELIVERY_INFO', 'Delivery information :');
define('TEXT_CURRENCY_DELIVERY_INFO', '<b>In Game Currency : </b>Click here to show that you\'re "<a href="%s">online</a>"');
define('TEXT_CDK_DELIVERY_INFO', '<b>CD Key / Time Card / Game Points : </b>View your code(s)/pin(s) from your order history upon receiving delivery email.');
define('TEXT_PWL_DELIVERY_INFO', '<b>Power Leveling Service : </b>We will notify you via email once we submit your order for leveling');
define('ENTRY_CHECKOUT_SUCCESS_VERIFICATION_CODE', 'Confirmation Code:');
define('TEXT_TITLE_ENTER_PHONE_NUMBER', 'Please ensure that the following mobile phone number is correct and reachable.');
define('TEXT_TITLE_ENTER_PHONE_VERIFICATION_CODE', 'Please enter the 4-digit verification code that was provided in the phone call. If you do not receive a call within 5 minutes, please kindly click on "Report error" link.');
define('TEXT_COUNTRY_NOT_SUPPORT', '(Country not supported by our mobile phone verification system)');
define('ERROR_REPORT', 'Report error');
define('TEXT_PHONE_VERIFICATION_PHONE_TYPE_NOT_SUPPORTED', 'Entered mobile phone number type is not supported');
define('CODE_MATCH_MESSAGE', 'You have successfully verified your mobile phone number. Thank you.');
define('CODE_NOT_MATCH_RETRY_MESSAGE', 'The confirmation code that you have entered is incorrect. Please try again.');
define('CODE_NOT_MATCH_MESSAGE', 'The confirmation code that you have entered is incorrect. We will manually review this order as soon as we could.');

define('TABLE_HEADING_COMMENTS', 'Enter a comment for the order processed');

define('EDIT_TELEPHONE' , '(Edit)');

define('TABLE_HEADING_DOWNLOAD_DATE', 'Expiry date: ');
define('TABLE_HEADING_DOWNLOAD_COUNT', ' downloads remaining');
define('HEADING_DOWNLOAD', 'Download your products here:');
define('FOOTER_DOWNLOAD', 'You can also download your products at a later time at \'%s\'');
define('PAYPAL_NAVBAR_TITLE_2_OK', 'Success'); // PAYPALIPN
define('PAYPAL_NAVBAR_TITLE_2_PENDING', 'Your order is being processed.'); // PAYPALIPN
define('PAYPAL_NAVBAR_TITLE_2_FAILED', 'Your payment has failed'); // PAYPALIPN
define('PAYPAL_HEADING_TITLE_OK', 'Your Order Has Been Processed!'); // PAYPALIPN
define('PAYPAL_HEADING_TITLE_PENDING', 'Your Order is being processed!'); // PAYPALIPN
define('PAYPAL_HEADING_TITLE_FAILED', 'Your payment has failed!'); // PAYPALIPN
define('PAYPAL_TEXT_SUCCESS_OK', 'Your order has been successfully processed! Your products will arrive at their destination within 2-5 working days.'); // PAYPALIPN
define('PAYPAL_TEXT_SUCCESS_PENDING', 'Your order is being processed!'); // PAYPALIPN
define('PAYPAL_TEXT_SUCCESS_FAILED', 'Your payment has failed! Please verify that the info your write to pay with PayPal is correct.'); // PAYPALIPN

define('TEXT_NO_ORDER_MADE_INFO_MSG', 'You have not make any order yet.');

// Error Report Content
define('EMAIL_TITLE', 'Mobile phone Verification Error Report');
define('EMAIL_ADMIN_NAME', 'Customers Support');
define('EMAIL_CONTENT_1', '<b>Report Date & Time:</b>');
define('EMAIL_CONTENT_2', '<b>Customer Name:</b>');
define('EMAIL_CONTENT_3', '<b>Customer ID:</b>');
define('EMAIL_CONTENT_4', '<b>Order ID:</b>');
define('EMAIL_CONTENT_5', '<b>Order Date & Time:</b>');
define('EMAIL_CONTENT_6', '<b>Order Status:</b>');
define('EMAIL_CONTENT_7', '<b>Mobile phone Location:</b>');
define('EMAIL_CONTENT_8', '<b>Mobile phone Number:</b>');

define('EMAIL_REPORT_SEND_MESSAGE', 'An e-mail have been sent to our assistants.');

define('ENTRY_VERIFICATION_CODE', 'Verification Code:');

define('ENTRY_COUNTRY_CODE', 'Country Code:');
define('IMAGE_BUTTON_CALL_ME_NOW', 'Call Me Now');
define('IMAGE_BUTTON_RETURN_TO_MAIN_PAGE', 'Return to homepage');
define('MESSAGE_EMAIL_VERIFIED', 'Your e-mail address has already been verified.');
define('MESSAGE_SUCCESS_MAIL_2', "An e-mail address verification's e-mail have been resent to your email account.");
define('MESSAGE_ERROR_MAIL', 'Fail to verify e-mail.');
define('TEXT_CHECKOUT_SUCCESS_HEADING_MSG', 'Thank you! Your order number is %s');
define('TEXT_CHECKOUT_SUCCESS_DESC_MSG', 'Your order is in processing and will be successfully placed in <span class="num">%d</span> sec.');
define('TEXT_CHECKOUT_SUCCESS_HEADING_MSG2', 'Congratulations! Your order is successful.');
define('TEXT_CHECKOUT_SUCCESS_DESC_MSG2', 'Your order number is <b>%s</b>');
define('TEXT_CHECK_STORE_CREDIT_LINK', 'Check store credit balance');
define('TEXT_CHECK_OFFGAMERS_POINT_LINK', 'Check OP balance');
define('TEXT_CHECK_SHARE_WITH_FRIENDS_LINK', 'Share with your friends:');
define('TEXT_DISCOUNT_COUPONS', 'Dis. Coupon:'); //yellow box
define('TEXT_LIVE_CHAT_LINK', 'Live Chat');
define('TEXT_ORDER_SUMMARY', '<div style="display:inline-block; font: bold 12px Arial,Tahoma,Verdana,Helvetica,sans-serif; color: black; padding: 0px 0px 0px 0px;">Order Summary:</div>');
define('TEXT_RESEND_VERIFICATION_CODE', '<a href= "%s">Resend confirmation code</a>');
define('TEXT_TRACK_STATUS_LINK', 'Track the status of this order');
define('TEXT_TOTAL', 'Total:'); //yellow box 
define('TEXT_SELECTION_BASED_RECOMENDATION', '<b>Based on your selection, you may be interested in the following items:</b>');
define('TEXT_SUB_TOTAL', 'Sub-Total:'); //yellow box
define('TEXT_SURCHARGE', 'Surcharge:'); //yellow box
define('TEXT_STORE_CREDIT', 'Store Credit:'); //yellow box
define('BUTTON_VIEW_ORDER', 'View order');
?>