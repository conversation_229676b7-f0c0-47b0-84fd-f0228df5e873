<?
/*
  	$Id: create_account.php,v 1.13 2007/12/21 09:03:49 chan Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2003 osCommerce
	
  	Released under the GNU General Public License
*/

define('NAVBAR_TITLE', 'Create an Account');

define('HEADING_TITLE', 'Sign Up');

define('TEXT_ORIGIN_LOGIN', 'If you already have an account with us, please login at the <a href="%s">login page</a>.');

define('TABLE_HEADING_COMMENTS', 'Additional Information');

define('EMAIL_SUBJECT', 'Account Activation');
define('EMAIL_WELCOME', 'We welcome you to ' . STORE_NAME . ', your one-stop online store for all your gaming virtual item and power leveling needs.  We will send all e-mails, including password reset and order update, to this e-mail address.' . "\n\n");
define('EMAIL_INFO', "\n\n" . 'With an account at ' . STORE_NAME . ", you will be able to sign in to our store to:-"."\n\n");
define('EMAIL_LINK_INFO', 'You may click on the link below to activate your account.' . "\n\n");
define('EMAIL_LINK_ADDRESS_INFO', "\n\n" . 'If you are unable to click on the link, copy and paste the entire url below into the address window of your web browser.' . "\n\n");
define('EMAIL_MANUAL_ACTIVATE_EMAIL', "\n" . 'You may also activate your account manually at <a href="' . tep_href_link(FILENAME_ACCOUNT_ACTIVATE, 'aov=activate') . '">' . tep_href_link(FILENAME_ACCOUNT_ACTIVATE, 'aov=activate') . '</a> by entering the following information<br><br>E-mail Address: ');
define('EMAIL_MANUAL_ACTIVATE_CODE', "<br>" . 'Activation Code: ');
define('EMAIL_TEXT', '- update your contact information or change your account password,'."\n".'- subscribe to or unsubscribe from our newsletters,'."\n".'- track the status of your current order, order history and buyback history,'."\n".'- automatically save the content of your shopping cart for later checkout,'."\n".'- receive special promotion and discount notices (only applicable if you are subscribed to our newsletters).'."\n\n");
define('EMAIL_CONTACT', 'For any enquiries or assistance, please use our Online Live Support service or e-mail your enquiries to ' . EMAIL_TO . '. Thank you for shopping at ' . STORE_NAME . ".\n\n\n");
define('EMAIL_WARNING', 'NOTE: You received this e-mail because you have signed up an account at ' . STORE_NAME . " with this e-mail address. If your e-mail address was used without your consent, please e-mail " . '<a href="mailto:' . EMAIL_TO . '">'.EMAIL_TO.'</a> for immediate removal of this account.' . " We would like to suggest you to change your PayPal account password (if you own one) because fraudsters usually sign up using one's e-mail address after they have gained access to the corresponding PayPal account (via phishing site, saved password retrieval, keylogger, spyware, etc).  Of course, it may also be a genuine typo mistake by our Customer when entering the e-mail address.  Please understand that our store has zero tolerance for internet fraud.  That is the reason we implemented e-mail address verification for all new account registrations and no purchase can be made without first verifying the e-mail address." . "\n");
define('EMAIL_FOOTER', STORE_EMAIL_SIGNATURE);

define('EMAIL_GV_INCENTIVE_HEADER', 'As part of our welcome to new customers, we have sent you an e-Gift Voucher worth %s');
define('EMAIL_GV_REDEEM', 'The redeem code for is %s, you can enter the redeem code when checking out, after making a purchase');
define('EMAIL_GV_LINK', 'or by following this link ');
define('EMAIL_COUPON_INCENTIVE_HEADER', 'Congratulation, to make your first visit to our online shop a more rewarding experience' . "\n" .
                                        '  below are details of a Discount Coupon created just for you' . "\n\n");
define('EMAIL_COUPON_REDEEM', 'To use the coupon enter the redeem code which is %s during checkout, ' . "\n" .
                               'after making a purchase');
?>