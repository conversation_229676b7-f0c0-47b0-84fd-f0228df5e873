<?
define('HEADING_TITLE', 'Event');
define('NAVBAR_TITLE', HEADING_TITLE);
define('FORM_TITLE', 'Submission Form');

define('ENTRY_SUBMIT_PICTURE', 'Submit Picture:');
define('ENTRY_SERVER', 'Server:');
define('ENTRY_CHAR_NAME', 'Character Name:');
define('ENTRY_EMAIL', 'Email:');
define('ENTRY_OGM_ORDER_NUM', 'OffGamers Order Number:');
define('ENTRY_EVENT_ORDER_NO', 'Order No.');

define('TEXT_SS_SIZE_AND_FORMAT', '(Maximum %sKB in .jpg format only)');
define('ERROR_CANNOT_LEFT_BLANK', 'cannot be left blank');
define('INVALID_ORDER_NUMBER_ERROR', 'Invalid order number.');
define('CHARACTER_NAME_ERROR', 'Please complete character name.');
define('CHAR_LEFT', 'characters left.');

define('EVENT_REQUIRED_FIELD_ERROR', 'Required field cannot left blank.');
define('EVENT_CONTENT_ERROR', 'Please complete text fields.');

define('EVENT_CONTENT_LENGTH_ERROR', 'Your message length is invalid. mininum %s characters.');
define('SERVER_ERROR', 'Please select a server.');
define('EVENT_SS_EMPTY_ERROR', 'Please click BROWSE and upload picture.');
define('EVENT_SS_EXTENSION_ERROR', 'Invalid picture format.');
define('EVENT_SS_ERROR', 'Picture file error. Please try again.');
//define('EVENT_SS_SIZE_EXCEED_LIMIT_ERROR', 'Picture size exceeded %s kb. Please try again.');
define('EVENT_SS_SIZE_EXCEED_LIMIT_ERROR', 'File upload size exceeded. Please try again.');

define('EVENT_SS_SUBMITTED_ERROR', 'This Order have been submitted before. Please try another order number.');
define('ERROR_QUOTA', 'Your failed filled quota has been used up, please wait 15 minutes before trying again.');

define('EVENT_SUBMIT_SUCCESS', 'Thank you for submitting.');

define('TEXT_LOGIN_EVENT_MESSAGE', 'Happy Gaming! Wish you good luck!');
//define('TEXT_LOGOFF_EVENT_MESSAGE', 'Please login to your OffGamers Account and upload your files. All are welcome, no order number is required.');
define('TEXT_LOGOFF_EVENT_MESSAGE', '<a href="%s">Login</a> and submit now! Don\'t have an OffGamers Account? <a href="%s">Sign up here</a>.');
define('TEXT_ARTICLE', 'article');
define('TEXT_SCREEN_SHOT', 'screen shot');

//define('CUSTOMER_NOTIFICATION_EVENT_SUBJECT', 'File Upload Notification');
define('CUSTOMER_NOTIFICATION_EVENT_SUBJECT', 'Successful Submission');
//define('CUSTOMER_NOTIFICATION_EVENT_CONTENT', '***************************************************' . "\n" . 'THIS IS AN AUTOMATED EMAIL - PLEASE DO NOT REPLY.' . "\n" . '***************************************************' . "\n" . "Congratulations,\n\n" . "Your Order Number is %s, %s" . "\n" . 'You have successfully uploaded your %s' . "\n" . 'For more promotions and events news, please visit <a href="http:\\www.offgamers.com">' . STORE_NAME . '</a>' . "\n" . 'Should you require any asistance, please do not hesitate to contact us at <a href="mailto:' . EMAIL_TO . '">' . EMAIL_TO . '</a>' . "\n\n" . 'Thank you for participating' . "\n\n" . EMAIL_FOOTER);
define('CUSTOMER_NOTIFICATION_EVENT_CONTENT', '***************************************************' . "\n" . 'THIS IS AN AUTOMATED EMAIL - PLEASE DO NOT REPLY.' . "\n" . '***************************************************' . "\n" . "Congratulations,\n\n" . "Your [ %s ]". "\n" . 'You have successfully uploaded your %s' . "\n" . 'For more promotions and events news, please visit <a href="http:\\www.offgamers.com">' . STORE_NAME . '</a>' . "\n" . 'Should you require any assistance, please do not hesitate to contact us at <a href="mailto:' . EMAIL_TO . '">' . EMAIL_TO . '</a>' . "\n\n" . 'Thank you for participating' . "\n\n" . 'Regards,' . "\n" . 'OffGamers - Your Gaming Alliance' . "\n" . '<a href="http:\\www.offgamers.com">' . STORE_NAME . '</a>');
define('ADMIN_EVENT_CONTENT', 'Order ID: %s' . "\n" . 'Character Name: %s' . "\n" . 'Email: %s' . "\n" . 'Address: ' . "\n" . '%s' . "\n" . 'Telephone: %s' . "\n" . 'Payment Method: %s' . "\n" . 'Purchase Amount: %s' . "\n" . 'Order Status: %s' . "\n" . '%s' . "\n\n");
define('ADMIN_SS_EMAIL_SUBJECT', 'PW Screenshot Contest');
define('ADMIN_ARTICLE_EMAIL_SUBJECT', 'GE Gamers\'Tips Event');

?>