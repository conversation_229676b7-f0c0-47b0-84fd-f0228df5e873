<?php
define('EXPRESS_CHECKOUT_TITLE', 'EXPRESS CHECKOUT');
define('EXPRESS_CHECKOUT_INFORMATION', 'You can either place a quick order or search product with a quick link:');


// combobox
define('COMBO_SELECT_GAME', 'Select/type game name');
define('COMBO_SELECT_PRODUCT_TYPE', 'Select/type product type');
define('COMBO_SELECT_SERVERS', 'Select/type server name');
define('COMBO_SELECT_CATEGORIES', 'Select/type categories');
define('COMBO_SELECT_PRODUCT', 'Select/type product name');


// button
define('BUTTON_EXP_CHECKOUT_CHECKOUT', 'Proceed Checkout');
define('BUTTON_EXP_CHECKOUT_BUY_NOW', 'Buy now');
define('BUTTON_EXP_CHECKOUT_OUT_OF_STOCK', 'Out of stock');
define('BUTTON_EXP_CHECKOUT_PRE_ORDER', 'Pre-Order');
define('BUTTON_EXP_CHECKOUT_CLOSE', 'CLOSE');


// text
define('TEXT_EXP_SUB_TOTAL', 'Sub-total:');
define('TEXT_EXP_TOTAL_REBATE', 'Total rebate:');
?>