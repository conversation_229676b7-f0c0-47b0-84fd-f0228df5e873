<?php
/*
  $Id: gift_card.php,v 1.12 2014/06/25 10:17:16 weesiong Exp $
  
  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2003 osCommerce

  Released under the GNU General Public License
*/

define('NAVBAR_GC_TITLE', 'OffGamers Gift Card');
define('TAB_HEADING_1', 'SHOP WITH GIFT CARD');
define('TAB_HEADING_2', 'REDEEM GIFT CARD');
define('TAB_HEADING_3', 'BUY GIFT CARD');

define('HEADING_STEP_1_TITLE', 'STEP 1: Gift Card Info');
define('HEADING_STEP_2_TITLE','STEP 2: Login or Register');
define('HEADING_PWGC_STEP_2_TITLE','STEP 2: Shopping');
define('HEADING_PWGC_STEP_3_TITLE','STEP 3: Login or Register');

define('EMAIL_CHANGE_YOUR_PASSWORD_BODY', 'Congratulations for becoming an ' . STORE_NAME . ' member. You have registered ' . STORE_NAME . ' account recently with using this email address.'."\n".'Below are your account details:'."\n\n".'Email: <b>%s</b>'."\n".'Password: <b>%s</b>'."\n\n".'For security purposes, we suggest you to change your login password. Follow the link below:'."\n".'<a href="%s">Change my login password</a>'."\n\n".'For any enquiries or assistance, please use our Online Live Support service or e-mail your enquiries to <a href="mailto:' . EMAIL_TO . '">'.EMAIL_TO.'</a>.');
define('EMAIL_CHANGE_YOUR_PASSWORD_SUBJECT', 'Welcome to ' . STORE_NAME);
define('EMAIL_FOOTER', STORE_EMAIL_SIGNATURE);

define('USER_MEMBER_STATUS_NOT_ALLOW', 'Your member status is not allow to redeem gift card. Please contact <a target="_blank" href="mailto:<EMAIL>"><EMAIL></a>.');
define('USER_GIFTCARD_ERROR', 'Serial Number or Code entered does not match. Please try again. If this problem persist, please contact us at <a target="_blank" href="mailto:<EMAIL>"><EMAIL></a>.');
define('USER_GIFTCARD_DEACTIVATED', 'This code is already in used. If you have bought this code, please contact us at <a target="_blank" href="mailto:<EMAIL>"><EMAIL></a>.');
define('USER_GIFTCARD_REDEEMED', 'This code is already in used. If you have bought this code, please contact us at <a target="_blank" href="mailto:<EMAIL>"><EMAIL></a>.');
define('USER_GIFTCARD_UNKNOWN_ERROR', 'Please key in the Code again. If you are still receiving the same error message, please contact us at <a target="_blank" href="mailto:<EMAIL>"><EMAIL></a>.');
define('USER_GIFTCARD_INFO_MISSING', 'Please enter Serial Number / Pin Code.');
define('USER_REDEEM_GIFTCARD_FAILURE', 'Please try again and click on the Redeem button once more. If this problem persist, please contact us at <a target="_blank" href="mailto:<EMAIL>"><EMAIL></a>.');
define('USER_GIFTCARD_CURRENCY_INVALID', 'Apologize, Gift Card\'s currency: AED, SAR, KWD, PHP, JPY and HKD are no longer supported in our store. For any enquiries, kindly contact our customer support at <a target="_blank" href="mailto:<EMAIL>"><EMAIL></a>.');

define('DF_TEXT_GIFT_CARD_VALUE', 'Gift Card Value: %s');
define('DF_TEXT_REDEEM_GIFT_CARD_CONFIRMATION', 'Gift Card Value: %s (to Store Credit Currency: %s)');

define('TEXT_GC_QUANTITY', "Quantity");
define('TEXT_SERIAL_NO', "Serial No.");
define('TEXT_PIN_CODE', "PIN Code");
define('TEXT_TOTAL_VALUE', 'Total Value');
define('TEXT_LOGIN_PASSWORD', 'My Login Password');
define('TEXT_NAME', 'My Name');
define('TEXT_EMAIL', 'My Email');
define('TEXT_GC_WELCOME_BACK', 'Welcome back <b>%1$s</b> (Not %1$s? <a href="javascript:void(0);" onclick="%2$s">Logout here</a>)');
define('TEXT_GC_PC', ' pc');
define('TEXT_GC_MANAY_PC', 's');
define('TEXT_PRODUCT', 'Product');
define('TEXT_PURCHASE_FOR_MYSELF', 'For myself');
define('TEXT_PURCHASE_AS_GIFT', 'As a gift');
define('TEXT_PURCHASE_WITH_GIFT_CARD_CONFIRMATION', 'Do you confirm this purchase and paying with your OffGamers Gift Card?');
define('TEXT_SELECT', 'Select');
define('TEXT_IS_EXISTING_CUSTOMER', 'Existing customer');
define('TEXT_IS_NEW_CUSTOMER', 'I\'m a new customer');

define('TEXT_GC_SUBHEADER', 'ONE SOLUTION FOR THOUSANDS OF GAMING CONTENTS!');
define('TEXT_GC_SUB_DESC', 'OffGamers Gift Card solution is designed to replace the traditional gift certificates and offers excellent conveniences to all users. The ease of providing gifts to your loved ones, friends and colleagues, could be achieved right from your fingertips. With direct online ordering, redeeming gifts online would be an ideal way for everyone.');

// FAQ
define('TEXT_GC_FAQ_AWARD_TITLE', 'OffGamers Gift Card Award');
define('TEXT_GC_FAQ_H1', 'What Is OffGamers Gift Card');
define('TEXT_GC_FAQ_C1', 'OffGamers Gift Card is designed to replace the traditional gift certificates and offers excellent conveniences to all users. The ease of providing gifts to your loved ones, friends and colleagues, can be achieved right from your fingertips.');
define('TEXT_GC_FAQ_H2', 'Why use OffGamers Gift Card?');
define('TEXT_GC_FAQ_C2', 'Gift Card allows anyone to shop at OffGamers Store 24/7, 365 days, anytime and anywhere. Gift cards are the perfect gift for anyone your shopping list, because this would allow the recipient to pick out what they really want. With direct online ordering, redeeming gifts online would be an ideal way for everyone.');
define('TEXT_GC_FAQ_H3', 'How to purchase OffGamers Gift Card?');
define('TEXT_GC_FAQ_C3', 'Log in to your OffGamers account firstly. Select on "Buy Gift Card" tab or <a href="'.tep_href_link(FILENAME_GIFT_CARD, 'action=purchase_gc', 'SSL').'">click here</a> to navigate to OffGamers Gift Card section. Choose your desired Gift Card denomination you wish to acquire and select the preferred payment method. Fill in your details and recipient\'s information whom you wish to send the gift to and click on "Proceed Checkout" to complete the transaction.');

// Display Message
define('SUCCESS_REDEEMED', 'You have successfully redeemed your Gift Card!');
define('SUCCESS_CREDITED', 'Your account has been credited %s Store Credits.');
define('SUCCESS_PURCHASE_WITH_GIFT_CARD', 'Congratulation! Your order is successfully completed.');
define('SUCCESS_PURCHASE_WITH_GIFT_CARD_ORDER_INFO', 'Your order number is <b>%s</b>');
define('SUCCESS_CREDIT_BALANCE', 'Your Store Credit balance: <b>%s</b>');
define('SUCCESS_CREDIT_BALANCE_DESC', 'Your balance from the OffGamers Gift Card have been credited<br> as Store Credit into your account.');

define('ERROR_NOT_ENOUGH_CREDIT', 'Please note that your OffGamers Gift Card(s) amount isn\'t sufficient to purchase this product. Kindly choose a lower value or quantity, or get a OffGamers Gift Card online <a href="'.tep_href_link(FILENAME_GIFT_CARD, 'action=purchase_gc', 'SSL').'">here</a>.');
define('ERROR_NOT_ENOUGH_CREDIT_AFTER_QTY', 'Please note that your OffGamers Gift Card(s) amount isn\'t sufficient to purchase this product. Kindly choose a lower value or quantity.'. "\n" .'If you have sufficient Store Credit in your account, the Store Credit amount will be used to offset the difference.');
define('ERROR_INVALID_FORM_SUBMIT', 'There\'s been an error that needs your attention on one of the input fields. Please ensure all fields are correct.');

// Button
define('BUTTON_REDEEM', "Redeem");
define('BUTTON_VIEW_ORDER', 'View order');
define('BUTTON_VIEW_SC_BALANCE', 'View Store Credits balance');
define('BUTTON_CHECK_OP_BALANCE', 'Check your OP balance');
define('BUTTON_BROWSE_MORE_GAMES', 'Browse more games');
define('BUTTON_CONFIRM_CHECKOUT', 'Confirm Checkout');
?>