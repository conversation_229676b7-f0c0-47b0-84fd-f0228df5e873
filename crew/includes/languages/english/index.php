<?
/*
  	$Id: index.php,v 1.17 2012/07/16 02:39:21 weesiong Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2003 osCommerce
	
  	Released under the GNU General Public License
*/

define('TEXT_MAIN', 'This is a default setup of the osCommerce project, products shown are for demonstrational purposes, <b>any products purchased will not be delivered nor will the customer be billed</b>. Any information seen on these products is to be treated as fictional.<br><br><table border="0" width="100%" cellspacing="5" cellpadding="2"><tr><td class="main" valign="top">' . tep_image(DIR_WS_IMAGES . 'default/1.gif') . '</td><td class="main" valign="top"><b>Error Messages</b><br><br>If there are any error or warning messages shown above, please correct them first before proceeding.<br><br>Error messages are displayed at the very top of the page with a complete <span class="messageStackError">background</span> color.<br><br>Several checks are performed to ensure a healthy setup of your online store - these checks can be disabled by editing the appropriate parameters at the bottom of the includes/application_top.php file.</td></tr><td class="main" valign="top">' . tep_image(DIR_WS_IMAGES . 'default/2.gif') . '</td><td class="main" valign="top"><b>Editing Page Texts</b><br><br>The text shown here can be modified in the following file, on each language basis:<br><br><nobr class="messageStackSuccess">[path to catalog]/includes/languages/' . $language . '/' . FILENAME_DEFAULT . '</nobr><br><br>That file can be edited manually, or via the Administration Tool with the <nobr class="messageStackSuccess">Languages->' . ucfirst($language) . '->Define</nobr> or <nobr class="messageStackSuccess">Tools->File Manager</nobr> modules.<br><br>The text is set in the following manner:<br><br><nobr>define(\'TEXT_MAIN\', \'<span class="messageStackSuccess">This is a default setup of the osCommerce project...</span>\');</nobr><br><br>The text highlighted in green may be modified - it is important to keep the define() of the TEXT_MAIN keyword. To remove the text for TEXT_MAIN completely, the following example is used where only two single quote characters exist:<br><br><nobr>define(\'TEXT_MAIN\', \'\');</nobr><br><br>More information concerning the PHP define() function can be read <a href="http://www.php.net/define" target="_blank"><u>here</u></a>.</td></tr><tr><td class="main" valign="top">' . tep_image(DIR_WS_IMAGES . 'default/3.gif') . '</td><td class="main" valign="top"><b>Securing The Administration Tool</b><br><br>It is important to secure the Administration Tool as there is currently no security implementation available.</td></tr><tr><td class="main" valign="top">' . tep_image(DIR_WS_IMAGES . 'default/4.gif') . '</td><td class="main" valign="top"><b>Online Documentation</b><br><br>Online documentation can be read at the <a href="http://wiki.oscommerce.com" target="_blank"><u>osCommerce Wiki Documentation Effort</u></a> site.<br><br>Community support is available at the <a href="http://forums.oscommerce.com" target="_blank"><u>osCommerce Community Support Forums</u></a> site.</td></tr></table><br>If you wish to download the solution powering this shop, or if you wish to contribute to the osCommerce project, please visit the <a href="http://www.oscommerce.com" target="_blank"><u>support site of osCommerce</u></a>. This shop is running on osCommerce version <font color="#f0000"><b>' . PROJECT_VERSION . '</b></font>.');
define('TABLE_HEADING_NEW_PRODUCTS', 'New Products For %s');
define('TABLE_HEADING_UPCOMING_PRODUCTS', 'Upcoming Products');
define('TABLE_HEADING_DATE_EXPECTED', 'Date Expected');
define('TABLE_HEADING_DEFAULT_SPECIALS', 'Specials For %s');

define('TABLE_HEADING_PRODUCTS', 'Products');
define('TABLE_HEADING_ETA', 'ETA');
define('TABLE_HEADING_PRICES', 'Prices');
define('TABLE_HEADING_DELIVERY_TIME', 'Delivery Time');
define('TABLE_HEADING_PRODUCTS_LIST', 'PRODUCT LIST');
define('TEXT_DTU_TO_GAME_ACCOUNT', 'Direct Top Up to Game Account');

define('LINK_MORE_INFO', 'More info');
define('LINK_HIDE_INFO', 'Hide info');

if ($tpl == 0) {
	define('NAVBAR_TITLE', GAME_CURRENCY_TEMPLATE);
} else if ($tpl == 1) {
	define('NAVBAR_TITLE', PWL_TEMPLATE);
} else if ($tpl == 2) {
	define('NAVBAR_TITLE', CD_KEY_TEMPLATE);
} else if ($tpl == 3) {
	define('NAVBAR_TITLE', STORE_CREDIT_TEMPLATE);
} else {
	define('NAVBAR_TITLE', TEXT_HOME);
}

define('CDKEY_MESSAGE', 'Once your order is processed, an e-mail will be mailed <i>instantly</i> with detailed instructions on retrieving the screenshot of your CD Key through your order history page.<br><br>* your CD key is brand new and has never been used.<br>* your CD key is the access key required to create the game account.<br>* there is no physical shipping of the game box.<br> * we do not provide downloads of the game for the time being.<br><br><FONT color=red><B>IMPORTANT NOTE:</B></FONT> CD Keys and Pre-Paid Cards sold are non-refundable.');

if ( ($category_depth == 'products') || (isset($HTTP_GET_VARS['manufacturers_id'])) ) {
	define('HEADING_TITLE', 'Let\'s See What We Have Here');
  	define('TABLE_HEADING_IMAGE', '');
  	define('TABLE_HEADING_MODEL', 'Model');
  	define('TABLE_HEADING_PRODUCTS', 'Product Name');
  	define('TABLE_HEADING_MANUFACTURER', 'Manufacturer');
  	define('TABLE_HEADING_QUANTITY', 'Quantity');
  	define('TABLE_HEADING_PRICE', 'Price');
  	define('TABLE_HEADING_WEIGHT', 'Weight');
  	define('TABLE_HEADING_BUY_NOW', 'Buy Now');
  	define('TABLE_HEADING_PRODUCT_SORT', 'Sort');
  	
  	define('TEXT_NO_PRODUCTS', 'Products will be uploaded soon. Please stay tuned.');
  	define('TEXT_NO_PRODUCTS2', 'There is no product available from this manufacturer.');
  	define('TEXT_NUMBER_OF_PRODUCTS', 'Number of Products: ');
  	define('TEXT_SHOW', '<b>Show:</b>');
  	define('TEXT_BUY', 'Buy 1 \'');
  	define('TEXT_NOW', '\' now');
  	define('TEXT_ALL_CATEGORIES', 'All Categories');
  	define('TEXT_ALL_MANUFACTURERS', 'All Manufacturers');
} elseif ($category_depth == 'top') {
  	define('HEADING_TITLE', '');
} elseif ($category_depth == 'nested') {
  	define('HEADING_TITLE', 'Categories');
  	define('TEXT_NO_PRODUCTS', 'Please Select Your Category.');
} else {
	if ((int)$current_category_id > 0) {
		define('HEADING_TITLE', tep_get_categories_heading_title($current_category_id, $languages_id));
  	} else {
  		define('HEADING_TITLE', '');
  	}
  	define('ERROR_PAGE_NOT_FOUND', 'Page Not Found');
  	define('TEXT_NO_PRODUCTS', 'Please Select Your Category');
  	define('TEXT_VIEW_PRODUCTS', 'View Products');
  	define('TEXT_NO_PRODUCTS', 'Products will be uploaded soon. Please stay tuned.');
}


define('ERROR_REGION_HEADING_TITLE', 'Sorry...');
define('ERROR_REGION_SUB_HEADING_TITLE', 'This product is currently not available for your region.');

define('ERROR_REGION_TEXT_OPTION_HEADING', 'Please try one of the following options:');
define('ERROR_REGION_TEXT_OPTION_REGION_SETTING', 'Change your ');
define('ERROR_REGION_TEXT_OPTION_REGION_SETTING_LINK', 'region setting');
define('ERROR_REGION_TEXT_OPTION_BROWSE_STORE_LINK', 'Browse the store');
define('ERROR_REGION_TEXT_OPTION_BROWSE_STORE', ' for other products & services.');
define('ERROR_REGION_TEXT_OPTION_HOME', 'Go to ');
define('ERROR_REGION_TEXT_OPTION_HOME_LINK', 'our homepage');

// Main Page : Tab Content
define('TAB_HEADER_HOT_DIRECT_TOP_UP', 'HOT DIRECT TOP UP');
define('TAB_HEADER_BEST_SELLING', 'BEST SELLING PRODUCTS');

define('TAB_TEXT_PLATFORM', 'Platform: ');
define('TAB_TEXT_GENRE', 'Genre: ');

define('TAB_BUTTON_BUY_NOW', 'Buy now');

define('ERROR_HOT_DIRECT_TOP_UP', 'No Direct Top-Up Available.');
define('ERROR_BEST_SELLING', 'No Best Selling Games Available.');

// Product Listing : Publisher Game
define('SUPPORTED_GAME_LIST', 'SUPPORTED GAME LIST');
?>
