<?php
/*
  $Id: login.php,v 1.45 2013/10/01 09:58:07 wenbin.ng Exp $

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2003 osCommerce

  Released under the GNU General Public License
*/

define('NAVBAR_TITLE', 'Sign In');
define('HEADING_TITLE', '<span style="color:#2982a9;">Sign in</span></b> or create an ' . STORE_NAME . ' account.');

define('TEXT_PASSWORD_FORGOTTEN', 'Forgot Your Password?');
define('TEXT_RESET_PASSWORD', 'reset password');
define('TEXT_ACTIVATE_ACCOUNT', 'Activate account');
define('TEXT_SIGN_IN_TROUBLE', 'Contact our <a href="%s">customer support</a> now!');
define('TEXT_LOGIN_ERROR', 'Invalid login, please try again or %s.');
define('TEXT_LOGIN_EMAIL_ACTIVATE_ERROR', 'Account not activated.<br><a href="'. tep_href_link(FILENAME_ACCOUNT_ACTIVATE, 'aov=activate').'">'.TEXT_ACTIVATE_ACCOUNT.'</a> or <a href="'.tep_href_link(FILENAME_ACCOUNT_ACTIVATE, 'action=resend&aov=activate').'">Resend activation email</a> %s');
define('TEXT_VISITORS_CART', '%s Your &quot;Visitors Cart&quot; contents will be merged with your &quot;Members Cart&quot; contents once you have logged on. <a href="javascript:session_win(\'' . tep_href_link(FILENAME_INFO_SHOPPING_CART) . '\');">[More Info]</a>');
define('TEXT_RETURNING_CUSTOMER', 'Returning Customer');
define('TEXT_NEW_CUSTOMER', 'New Customer');
define('TEXT_CREATE_ACCOUNT_MSG', 'If you don\'t have an account with OffGamers, please create one.');
define('TEXT_SIGN_IN_MSG', 'If you have an account with OffGamers, please sign in.');
define('TEXT_SEND_PASSWORD_MSG', 'Please enter you E-mail.');
define('TEXT_SIGNUP_SUCCESS', 'Welcome %s! You have successfully signed up to be ' . STORE_NAME . ' member. %s');
define('TEXT_WHAT_TODO', 'What would you like to do next?');
define('TEXT_SIGNUP_SUCCESS_END', '... or start shopping by selecting your game from the menu on top.');
define('TEXT_CHECKOUT_REDIRECT_MSG', 'You will be redirected to complete the checkout process shortly.');
define('TEXT_SEE_ALL_OFFER', 'see all that we have to offer');
define('TEXT_SELL_US_CURRENCIES', 'sell us your currencies');
define('TEXT_CAPTCHA_ERROR', 'Error incorrect captcha phrase typed in. Please click refresh for a new captcha or key in the captcha phrase again');
define('TEXT_CAPTCHA_MISSING_ERROR', 'Error captcha must be filled out');
define('ERROR_INCOMPLETE_LOGIN_INFO','Error : Invalid email address. Please try again.');

define('LINK_BACK_TO_WHERE_I_COME', 'Back to the page where I came from');
define('LINK_GOTO_HOMEPAGE', 'Goto homepage');
define('LINK_GOTO_MY_ACCOUNT', 'Goto My Account');

define('TEXT_MAIN', 'If you\'ve forgotten your password, enter your e-mail address below and we\'ll send you an e-mail message containing your new password.');
define('TEXT_NO_EMAIL_ADDRESS_FOUND', 'Error: The E-Mail Address was not found in our records, please try again.');
define('EMAIL_PASSWORD_REMINDER_SUBJECT', 'Password Reset');
define('EMAIL_PASSWORD_REMINDER_BODY', 'Your account password has been reset to the following. You may now sign in to ' . STORE_NAME . ' with your e-mail address, %s, and the new password. For any enquiries or assistance, please use our Online Live Support service or e-mail your enquiries to ' . EMAIL_TO . ". Thank you for shopping at " . STORE_NAME . ".\n\n" . 'New Password: %s' . "\n");
define('EMAIL_FOOTER', STORE_EMAIL_SIGNATURE);

define('SUCCESS_PASSWORD_SENT', 'Success: A new password has been sent to your e-mail address.');

define('EMAIL_WELCOME', 'We welcome you to ' . STORE_NAME . ', your one-stop online store for all your gaming virtual item and power leveling needs.  We will send all e-mails, including password reset and order update, to this e-mail address.' . "\n\n");
define('EMAIL_INFO', "\n\n" . 'With an account at ' . STORE_NAME . ", you will be able to sign in to our store to:-"."\n\n");
define('EMAIL_LINK_INFO', 'You may click on the link below to activate your account.' . "\n\n");
define('EMAIL_LINK_ADDRESS_INFO', "\n\n" . 'If you are unable to click on the link, copy and paste the entire url below into the address window of your web browser.' . "\n\n");
define('EMAIL_MANUAL_ACTIVATE_EMAIL', "\n" . 'You may also activate your account manually at <a href="' . tep_href_link(FILENAME_ACCOUNT_ACTIVATE, 'aov=activate') . '">' . tep_href_link(FILENAME_ACCOUNT_ACTIVATE, 'aov=activate') . '</a> by entering the following information<br><br>E-mail Address: ');
define('EMAIL_MANUAL_ACTIVATE_CODE', "<br>" . 'Activation Code: ');
define('EMAIL_TEXT', '- update your contact information or change your account password,'."\n".'- subscribe to or unsubscribe from our newsletters,'."\n".'- track the status of your current order, order history and buyback history,'."\n".'- automatically save the content of your shopping cart for later checkout,'."\n".'- receive special promotion and discount notices (only applicable if you are subscribed to our newsletters).'."\n\n");
define('EMAIL_CONTACT', 'For any enquiries or assistance, please use our Online Live Support service or e-mail your enquiries to ' . EMAIL_TO . '. Thank you for shopping at ' . STORE_NAME . ".\n\n\n");
define('EMAIL_WARNING', 'NOTE: You received this e-mail because you have signed up an account at ' . STORE_NAME . " with this e-mail address. If your e-mail address was used without your consent, please e-mail " . '<a href="mailto:' . EMAIL_TO . '">'.EMAIL_TO.'</a> for immediate removal of this account.' . " We would like to suggest you to change your PayPal account password (if you own one) because fraudsters usually sign up using one's e-mail address after they have gained access to the corresponding PayPal account (via phishing site, saved password retrieval, keylogger, spyware, etc).  Of course, it may also be a genuine typo mistake by our Customer when entering the e-mail address.  Please understand that our store has zero tolerance for internet fraud.  That is the reason we implemented e-mail address verification for all new account registrations and no purchase can be made without first verifying the e-mail address." . "\n");
define('EMAIL_FOOTER', STORE_EMAIL_SIGNATURE);

define('EMAIL_GV_INCENTIVE_HEADER', 'As part of our welcome to new customers, we have sent you an e-Gift Voucher worth %s');
define('EMAIL_GV_REDEEM', 'The redeem code for is %s, you can enter the redeem code when checking out, after making a purchase');
define('EMAIL_GV_LINK', 'or by following this link ');
define('EMAIL_COUPON_INCENTIVE_HEADER', 'Congratulation, to make your first visit to our online shop a more rewarding experience' . "\n" .
                                        '  below are details of a Discount Coupon created just for you' . "\n\n");
define('EMAIL_COUPON_REDEEM', 'To use the coupon enter the redeem code which is %s during checkout, ' . "\n" .
                               'after making a purchase');

define('EMAIL_MAXMIND_SUBJECT', 'Maxmind Geo IP Error');
define('EMAIL_MAXMIND_CONTENT', 'Maxmind Geo IP does not working.');

	
// Mantis # 0000024 @ ************ - New label
define('HEADER_CREATE_ACCOUNT', 'Create Account');
define('EZ_COUNTRY_CODE', 'Country Code:&nbsp;');
define('EZ_CONTACT_NUMBER', 'Mobile Phone:&nbsp;');
define('EZ_CONTACT_NUMBER_TEXT', '*');
define('EZ_CONTACT_NUMBER_EG', 'mobile phone eg: (12) xxxxxxx &nbsp;');
define('EZ_SECRET_QUESTION', 'Secret Question:&nbsp;');
define('EZ_SECRET_QUESTION_TEXT', '*');
define('EZ_ANSWER', 'Answer:&nbsp;');
define('EZ_ANSWER_TEXT', '*');
define('EZ_OPTIONAL_INFORMATION_HEADER', 'OPTIONAL INFORMATION&nbsp;');
define('EZ_STATISTICS_PURPOSE', 'Statistics Purpose:&nbsp;');
define('EZ_INSTANT_MESSENGER', 'Instant Messenger&nbsp;(IM):');
define('EZ_CREDITCARD_BILLING_ADDRESS', 'Billing Address for Credit Card Payment:&nbsp;');
define('EZ_BILLING_ADDRESS1', 'Address1:&nbsp;');
define('EZ_BILLING_ADDRESS2', 'Address2:&nbsp;');

// Mantis # 0000024 @ ************ - Error message
define('ENTRY_EMAIL_JS_ERROR', "The email provided does not appear to be valid. Your E-Mail Address must contain a minimum of 6 characters.");
define('ENTRY_FIRSTNAME_JS_ERROR', "Your First Name must contain a minimum of %d characters.");
define('ENTRY_LASTNAME_JS_ERROR', "Your Last Name must contain a minimum of %d characters.");
define('ENTRY_PASSWORD_JS_ERROR', "Your Password must contain a minimum of %d characters.");
define('ENTRY_PASSWORD_CONFIRMATION_JS_ERROR', "The Password Confirmation must match your New Password.");
define('ENTRY_CONTACT_NUMBER_JS_ERROR', "Your phone number must be at least 3 number.");
define('ENTRY_DOB_JS_ERROR', "Your birthdate does not appear to be valid.");
define('ENTRY_SECRET_QUESTION_JS_ERROR', "You must select a secret question from the Secret Question pull down menu.");
define('ENTRY_ANSWER_JS_ERROR', "Your Answer must contain a minimum of %d characters.");
define('ENTRY_SIGNUP_TERMSANDCONDITION_JS_ERROR', "You must agree to our Terms of Service & Privacy Policy");  // @ ************

// Mantis # 0000024 @ ************ - Drop down notice
define('ENTRY_EMAIL_NOTICE', "Please use a valid email address as we need to email you to confirm your account.");
define('ENTRY_FIRSTNAME_NOTICE', "Please use the first name as appears on your ID.");
define('ENTRY_LASTNAME_NOTICE', "Please use the last name as appears on your ID.");
define('ENTRY_PASSWORD_NOTICE', "Your password must be at least %d characters long and it is case sensitive.");
define('ENTRY_CONFIRM_PASSWORD_NOTICE', "Type in your password again for verification purposes.");
define('ENTRY_CONTACTNUMBER_NOTICE', "Please provide a valid mobile phone number. It will be used for verification purposes and also to reset your Secret Question & Answer if you have forgotten it.");
define('ENTRY_ANSWER_NOTICE', "To ensure the security of your account, your secret question and answer will be used for verification purposes during changing, updating info and also for withdrawal of funds and it is not case sensitive.");
define('ENTRY_IM_NOTICE', "If you tell us your IM address, we will use this method to contact you for delivery whenever possible.");
define('ENTRY_DOB_NOTICE', "We are asking for legal reasons.");

// Mantis # 0000024 @ ************ - Error message
define('ENTRY_PASSWORD_CONFIRMATION_ERROR', "Your confirmation password must be at least %d characters.");
define('ENTRY_SECRET_QUESTION_ERROR', "You must select a secret question from the Secret Question pull down menu.");
define('ENTRY_ANSWER_ERROR', "Your answer must be at least %d character.");
define('ENTRY_CONTACT_NUMBER_ERROR', "Your phone number must be at least %d number.");
define('ENTRY_CONTACT_NUMBER_EXIST_ERROR', "We notice that mobile phone number you provide has been used. Please provide another mobile phone number.");
define('ENTRY_REFERRAL_ID_ERROR', 'Invalid Referral ID provided');
define('ENTRY_SIGNUP_TERMSANDCONDITION_ERROR', "You must agree to our Terms of Services & Privacy Policy.");  // @ ************ || 200901121317

// Mantis # 0000024 @ 200810311721 - New text statement in new layout screen
define('TEXT_REASON_TO_JOIN_MEMBER', 'Why you need to create a OffGamers member?<br>What benefit you can get over here?');
define('TEXT_SEND_PASSWORD', 'Kindly enter your email address.');
define('TEXT_FORGOTTEN_PASSWORD', 'FORGOTTEN PASSWORD');

// Mantis # 0000024 @ 200811051724 - New text statement in latest layout screen
define('TEXT_DO_YOU_AGREE', 'Do you agree? ');
define('TEXT_I_HAVE_READ_AND_AGREE', 'I have read and agree to the ');
define('TEXT_TERMS_AND_POLICY', '<a href="terms-of-service-i-5.ogm" target="terms">Terms of Service</a>, <a href="privacy-policy-i-4.ogm" target="terms">Privacy Policy</a> & <a href="affiliate-policy-i-773.ogm" target="terms">Affiliate Policy</a>.');

//Mantis # 0000068 @ ************
define('ENTRY_REFERRAL_ID', 'Referral ID:');

// ##
// survey form
define('JS_SURVEY_TITLE', 'Survey Form');
define('JS_SURVEY_QUESTION', 'Where do you hear about us');

define('JS_SURVEY_ANSWER_OTHERS', 'Others');
define('JS_SURVEY_ANSWER_GAMEAXIS', 'GameAxis');
define('JS_SURVEY_ANSWER_GAMES', 'Games');
define('JS_SURVEY_ANSWER_PCGAMERS', 'PC Gamers');
define('JS_SURVEY_ANSWER_PCCOM', 'PC.COM');

// Express Login
define('HEADER_ACCOUNT_LOGIN', 'Account Login');

// New Layout 2011 - Login
define('FORGOT_PASSWORD_HEADER','<h3>Forgot Password?</h3>');
define('HAVING_TROUBLE_HEADER','<h3>Having trouble signing-in?</h3>');
define('NOT_A_MEMBER_YET', 'Join OffGamers now and it\'s FREE!!!');
define('BUTTON_REGISTER_NOW', 'Register Now');
define('NOT_MEMBER_YET_HEADER','<h3>Not a member yet?</h3>');

// New Layout 2011 - Register
define('REGISTER_ACCOUNT_HEADER','REGISTER ACCOUNT');
define('BUTTON_JOIN_US_NOW','Join us now!');
define('REGISTER_USING_FB','or register using:');
define('RETURNING_CUSTOMER_HEADER', '<h3>I\'m a returning customer,</h3>');
define('BUTTON_LOGIN_HERE','Login here');
define('WHY_OGM_HEADER','<h3>Why OffGamers?</h3>');
define('BUY_AND_DOWNLOAD_GAMES', 'Buy and download games');
define('JOIN_CONVERSATION', 'Join the conversation');
define('GET_SUPPORT', 'Get support for your games');
define('CHEAP_GAMES_NOT_ENOUGH', 'If cheap games aren\'t enough...');

//oauth login
define('TEXT_LOGIN_OR_SIGNUP', 'Login game with your registered OffGamers account or <a target="new" href="%s" id="register_account_link" onClick="_gaq.push([\'_trackEvent\', \'Acquisition\', \'Signup\', \'R3K\']);return targetopener(this,true);">Sign up now!</a>');
define('TEXT_OR_LOGIN_WITH', 'or login with:');
define('ERROR_UNRECOGNISE_USER_OR_PASSWORD', 'Unrecognised email or password.');
define('TEXT_FORGOT_PASSWORD', 'Forgot Password?');
define('TEXT_LOGIN_TNC', 'By clicking sign up and playnow, you are indicating that you have read and agree to our <a href="http://www.offgamers.com/terms-of-service-i-5.ogm">Terms of Service</a>, and <a href="http://www.offgamers.com/privacy-policy-i-4.ogm">Privacy Policy</a>');
define('TEXT_LOGIN_GAME_HEADER', 'LOGIN GAME');
define('TEXT_FORGOT_PASSWORD_HEADER', 'FORGOT PASSWORD?');
define('TEXT_ACCOUNT_LOGIN_HEADER', 'OFFGAMERS SINGLE SIGN-ON ACCOUNT');
?>