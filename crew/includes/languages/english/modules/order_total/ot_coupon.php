<?php
/*
  $Id: ot_coupon.php,v 1.9 2011/05/12 07:53:18 weichen Exp $

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2002 osCommerce

  Released under the GNU General Public License
*/

  define('MODULE_ORDER_TOTAL_COUPON_TITLE', 'Discount Coupons ');
  define('MODULE_ORDER_TOTAL_COUPON_DISPLAY_TITLE', 'Discount Coupons ');
  define('MODULE_ORDER_TOTAL_COUPON_HEADER', 'Discount Coupons');
  define('MODULE_ORDER_TOTAL_COUPON_DESCRIPTION', 'Discount Coupon');
  define('SHIPPING_NOT_INCLUDED', ' [Shipping not included]');
  define('TAX_NOT_INCLUDED', ' [Tax not included]');
  define('MODULE_ORDER_TOTAL_COUPON_USER_PROMPT', '');
  define('ERROR_NO_INVALID_REDEEM_COUPON', 'Invalid Coupon Code');
  define('ERROR_INVALID_STARTDATE_COUPON', 'This coupon is not available yet');
  define('ERROR_INVALID_FINISDATE_COUPON', 'This coupon has expired');
  define('ERROR_INVALID_USES_COUPON', 'This coupon could only be used ');
  define('TIMES', ' times.');
  define('ERROR_INVALID_USES_USER_COUPON', 'You have used the coupon the maximum number of times allowed per customer.');
  define('REDEEMED_COUPON', 'a coupon worth ');
  define('REDEEMED_MIN_ORDER', 'on orders over ');
  define('REDEEMED_RESTRICTIONS', ' [Product-Category restrictions apply]');
  define('LINK_COUPON_CODE', 'Redeem Discount Coupon');

//  define('TEXT_GV_INFO_TO_CUSTOMER', '<br>If your voucher balance is sufficient to cover your order amount, please kindly select "Western Union" as your payment method and e-mail us your Order ID at <a href="mailto:'.EMAIL_TO.'">'.EMAIL_TO.'</a>. If your voucher balance is not sufficient to cover your order amount, please choose your preferred payment method for the remaining amount.');
?>