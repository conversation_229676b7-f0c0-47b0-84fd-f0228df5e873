<?php
/*
  	$Id: cimb.php,v 1.1 2009/03/25 04:58:29 boonhock Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2002 osCommerce
	
  	Released under the GNU General Public License
*/

define('MODULE_PAYMENT_CIMB_TEXT_TITLE', 'CIMB');
define('MODULE_PAYMENT_CIMB_TEXT_CART', 'Proceed to CIMB');
define('MODULE_PAYMENT_CIMB_TEXT_DISPLAY_TITLE', 'Cimbclicks');	// for title displaying purpose if different from MODULE_PAYMENT_CIMB_TEXT_TITLE (iPay88)
define('MODULE_PAYMENT_CIMB_TEXT_DESCRIPTION', 'CIMB');
define('MODULE_PAYMENT_CIMB_TEXT_PAYMENT_METHOD_OPTIONS', 'Pay with: ');
define('MODULE_PAYMENT_CIMB_TEXT_JS_PM', 'Please select your payment option with '.MODULE_PAYMENT_CIMB_TEXT_DISPLAY_TITLE.'.');
define('MODULE_PAYMENT_CIMB_TEXT_ERROR', 'Payment error!');
define('MODULE_PAYMENT_CIMB_ERROR_PM', 'Error: Please select your payment option with '.MODULE_PAYMENT_CIMB_TEXT_DISPLAY_TITLE.'.');
define('MODULE_PAYMENT_CIMB_TEXT_ERROR_MESSAGE', 'Your payment transaction at CIMB has failed. Please try again, or select an other payment option!');
?>