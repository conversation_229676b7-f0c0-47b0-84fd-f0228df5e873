<?php
/*
  $Id: global_collect.php,v 1.3 2014/09/18 12:30:41 sionghuat.chng Exp $

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2003 osCommerce / <PERSON>

  Released under the GNU General Public License
*/

define('MODULE_PAYMENT_GLOBAL_COLLECT_TEXT_TITLE', 'Global Collect'); //Do not change!
define('MODULE_PAYMENT_GLOBAL_COLLECT_TEXT_CART', 'Proceed to ' . MODULE_PAYMENT_GLOBAL_COLLECT_TEXT_TITLE);
define('MODULE_PAYMENT_GLOBAL_COLLECT_TEXT_DISPLAY_TITLE', MODULE_PAYMENT_GLOBAL_COLLECT_TEXT_TITLE);	// for title displaying purpose if different from MODULE_PAYMENT_GLOBAL_COLLECT_TEXT_TITLE

define('MODULE_PAYMENT_GLOBAL_COLLECT_TEXT_DESCRIPTION', ' This is checkout confirmation for ' . MODULE_PAYMENT_GLOBAL_COLLECT_TEXT_TITLE);

define('MODULE_PAYMENT_GLOBAL_COLLECT_TEXT_TITLE_PROCESSING', 'Processing transaction');
define('MODULE_PAYMENT_GLOBAL_COLLECT_TEXT_DESCRIPTION_PROCESSING', 'If this page appears for more than 5 seconds, please click the Alipay Checkout button to complete your order.');
define('MODULE_PAYMENT_GLOBAL_COLLECT_IMAGE_BUTTON_CHECKOUT', 'Global Collect Checkout');

define('MODULE_PAYMENT_GLOBAL_COLLECT_TEXT_ERROR_MESSAGE', 'There seems to be a problem trying to complete your purchase order.');
define('MODULE_PAYMENT_GLOBAL_COLLECT_TEXT_ERROR_MESSAGE_20', "There seems to be a problem getting your transaction through.");
define('MODULE_PAYMENT_GLOBAL_COLLECT_TEXT_ERROR_MESSAGE_25', MODULE_PAYMENT_GLOBAL_COLLECT_TEXT_ERROR_MESSAGE_20);
define('MODULE_PAYMENT_GLOBAL_COLLECT_TEXT_ERROR_MESSAGE_30', MODULE_PAYMENT_GLOBAL_COLLECT_TEXT_ERROR_MESSAGE_20);
define('MODULE_PAYMENT_GLOBAL_COLLECT_TEXT_ERROR_MESSAGE_50', "There was an issue with the connectivity between you and the bank.");
define('MODULE_PAYMENT_GLOBAL_COLLECT_TEXT_ERROR_MESSAGE_100', "Oh no! There seems to be an issue with your credit card issuing bank. Try contacting your credit card issuing bank to resolve this issue and try again. A few possible scenario that might have happened are:- 1) Your credit limit has been reached. 2) You need to inform your card issuing bank to pre-approve your purchase from our website. 3) This is your first time internet purchase with the new credit card.");
define('MODULE_PAYMENT_GLOBAL_COLLECT_TEXT_ERROR_MESSAGE_160', "There seems to be a problem trying to complete your purchase order.");
define('MODULE_PAYMENT_GLOBAL_COLLECT_TEXT_ERROR_MESSAGE_180', "I'm sorry but I'm afraid you might have entered the wrong details. Please try again with the correct credit card information.");

?>