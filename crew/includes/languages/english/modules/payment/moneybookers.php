<?php
/*
  	$Id: moneybookers.php,v 1.13 2013/03/25 10:40:49 hungnian.yong Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2002 osCommerce
	
  	Released under the GNU General Public License
*/

define('MODULE_PAYMENT_MONEYBOOKERS_TEXT_TITLE', 'Moneybookers.com');
define('MODULE_PAYMENT_MONEYBOOKERS_TEXT_CART', 'Proceed to Moneybookers.com');
//define('MODULE_PAYMENT_MONEYBOOKERS_TEXT_DISPLAY_TITLE', 'Moneybookers.com (OffGamers\' preferred payment method)');	// for title displaying purpose if different from MODULE_PAYMENT_MONEYBOOKERS_TEXT_TITLE (Moneybookers.com)
define('MODULE_PAYMENT_MONEYBOOKERS_TEXT_DISPLAY_TITLE', 'Moneybookers.com');	// for title displaying purpose if different from MODULE_PAYMENT_MONEYBOOKERS_TEXT_TITLE (Moneybookers.com)
define('MODULE_PAYMENT_MONEYBOOKERS_TEXT_DESCRIPTION', 'Moneybookers.com');
//define('MODULE_PAYMENT_MONEYBOOKERS_TEXT_CONFIRMATION', MODULE_PAYMENT_MONEYBOOKERS_MESSAGE);
define('MODULE_PAYMENT_MONEYBOOKERS_TEXT_IMPORTANT_NOTICE', 'If this is your first payment via Moneybookers on our website, the payment is subjected to a 1 time verification by Moneybookers (per MB account). The verification process may take up to 24 hours on weekdays and 48 hours on weekends.');
//define('MODULE_PAYMENT_MONEYBOOKERS_TEXT_EMAIL_FOOTER', str_replace(array("<br>", "<BR>", "\r\n"), array("\n", "\n", ''), MODULE_PAYMENT_MONEYBOOKERS_EMAIL_MESSAGE));
define('MODULE_PAYMENT_MONEYBOOKERS_TEXT_PAYMENT_METHOD_OPTIONS', 'Pay with: ');
define('MODULE_PAYMENT_MONEYBOOKERS_NOCURRENCY_ERROR', 'There\'s no moneybookers.com accepted currency installed!');
define('MODULE_PAYMENT_MONEYBOOKERS_ERRORTEXT1', 'payment_error=');
define('MODULE_PAYMENT_MONEYBOOKERS_ERRORTEXT2', '&error=There was an error during your payment at moneybookers.com!');
define('MODULE_PAYMENT_MONEYBOOKERS_ORDER_TEXT', ' Date of the order: ');
define('MODULE_PAYMENT_MONEYBOOKERS_TEXT_ERROR', 'Payment error!');
define('MODULE_PAYMENT_MONEYBOOKERS_TRANSACTION_FAILED_TEXT', 'Your payment transaction at moneybookers.com has failed. Please try again, or select an other payment option!');
define('MODULE_PAYMENT_MONEYBOOKERS_ORDER_COMMENT1', '');
define('MODULE_PAYMENT_MONEYBOOKERS_ORDER_COMMENT2', 'Please kindly refer to '.STORE_NAME.' Order ID when you contact our Customer Support regarding this order. This will allow us to assist you more quickly and efficiently. You may also find the Order ID in "Reference" column under Moneybookers.com > My Account > history. Thanks.');

// Moneybookers Account Verification E-mail
define('TEXT_MB_EMAIL_VERIFY_INSTRUCTION_CONTENT', "\n\n\n" . 'Please kindly verify this e-mail account by clicking on the following link or copy and paste the link to your browser.  This link will confirm that you are the owner of this e-mail address and we will not ask for any Moneybookers information in the page.  Please understand that this step is taken to protect Moneybookers account owners from unauthorized charges and this is a one-time process unless you change your Moneybookers e-mail.' . "\n\n");
define('TEXT_MB_PAYMENT_MAKE_EMAIL_CONTENT1', 'You have made a Moneybookers payment of ');
define('TEXT_MB_PAYMENT_MAKE_EMAIL_CONTENT2', ' to OffGamers using the following account:' . "\n");
define('TEXT_MB_EMAIL_VERIFY_ENDING_CONTENT', "\n\n\n" . 'If you have not made any purchase at OffGamers, please report this fraudulent <NAME_EMAIL> immediately and we also advise you to report the case to Moneybookers at their website and change your Moneybookers password immediately.  Thank you for shopping at ' . STORE_NAME . '.');
define('TEXT_MB_VERIFICATION_TITLE', 'Moneybookers Account Verification');
define('TEXT_NOTICE_OF_MB_VERIFICATION_SEND_TITLE', 'Moneybookers Account Verification Notice');
define('TEXT_MB_EMAIL_VERIFICATION_NOTICE_HEADING_CONTENT', "\n\n" . 'In order to protect the owner of the Moneybookers account from fraudulent use, a verification e-mail with "Subject: ');
define('TEXT_MB_EMAIL_VERIFICATION_NOTICE_ENDING_CONTENT', '" has been sent to the e-mail address shown above.  Please kindly check the e-mail account and follow the instructions stated to verify your Moneybookers e-mail account.  Thank you for helping us to serve you better.');
define('EMAIL_FOOTER', STORE_EMAIL_SIGNATURE);
define('EMAIL_CONTACT', 'For any enquiries or assistance, please use our Online Live Support service or e-mail your enquiries to ' . EMAIL_TO . '. Thank you for shopping at ' . STORE_NAME . ".\n\n\n");
define('REMARK_MB_VERIFICATION_EMAIL_SENT', 'Moneybookers verification e-mail sent.');
define('REMARK_MB_VERIFICATION_EMAIL_NOTICE_SENT', 'Moneybookers verification e-mail notice sent.');
?>