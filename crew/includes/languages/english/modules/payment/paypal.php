<?
/*
  	$Id: paypal.php,v 1.9 2008/12/12 07:56:55 boonhock Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	DevosC, Developing open source Code
  	http://www.devosc.com
	
  	Copyright (c) 2002 osCommerce
  	Copyright (c) 2004 DevosC.com
	
  	Released under the GNU General Public License
*/

define('MODULE_PAYMENT_PAYPAL_TEXT_TITLE', 'PayPal'); //Do not change!
define('MODULE_PAYMENT_PAYPAL_TEXT_CART', 'Proceed to PayPal');
define('MODULE_PAYMENT_PAYPAL_TEXT_DISPLAY_TITLE', 'PayPal');	// for title displaying purpose if different from MODULE_PAYMENT_PAYPAL_TEXT_TITLE

define('MODULE_PAYMENT_PAYPAL_TEXT_DESCRIPTION', ' This is checkout confirmation for PayPal ');

define('MODULE_PAYMENT_PAYPAL_CC_TEXT','Credit Card');
define('MODULE_PAYMENT_PAYPAL_CC_DESCRIPTION','You do not need to be a PayPal member to pay by credit card');
define('MODULE_PAYMENT_PAYPAL_CC_URL_TEXT','<font color="blue"><u>[info]</u></font>');

define('MODULE_PAYMENT_PAYPAL_CUSTOMER_COMMENTS', 'Add Comments About Your Order');
define('MODULE_PAYMENT_PAYPAL_TEXT_TITLE_PROCESSING', 'Processing transaction');
define('MODULE_PAYMENT_PAYPAL_TEXT_DESCRIPTION_PROCESSING', 'If this page appears for more than 5 seconds, please click the PayPal Checkout button to complete your order.');
define('MODULE_PAYMENT_PAYPAL_IMAGE_BUTTON_CHECKOUT', 'PayPal Checkout');
//define('MODULE_PAYMENT_PAYPAL_TEXT_CONFIRMATION', MODULE_PAYMENT_PAYPAL_MESSAGE);
//define('MODULE_PAYMENT_PAYPAL_TEXT_EMAIL_FOOTER', str_replace(array("<br>", "<BR>", "\r\n"), array("\n", "\n", ''), MODULE_PAYMENT_PAYPAL_EMAIL_MESSAGE));
?>