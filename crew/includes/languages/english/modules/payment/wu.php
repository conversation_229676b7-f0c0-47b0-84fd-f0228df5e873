<?php
/*
  	$Id: wu.php,v 1.3 2008/12/12 07:56:55 boonhock Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2002 osCommerce
	
  	Released under the GNU General Public License
*/

define('MODULE_PAYMENT_WU_TEXT_RECEIVER', 'Receiver ');
define('MODULE_PAYMENT_WU_TEXT_CART', 'Confirm Order');
define('MODULE_PAYMENT_WU_TEXT_SENDER', 'Sender ');
define('MODULE_PAYMENT_WU_ENTRY_MCTN', 'MCTN: ');
define('MODULE_PAYMENT_WU_ENTRY_AMOUNT', 'Amount: ');
define('MODULE_PAYMENT_WU_ENTRY_CURRENCY', 'Currency: ');
define('MODULE_PAYMENT_WU_ENTRY_FIRST_NAME', 'First Name: ');
define('MODULE_PAYMENT_WU_ENTRY_LAST_NAME', 'Last Name: ');
define('MODULE_PAYMENT_WU_ENTRY_ADRESS', 'Adress: ');
define('MODULE_PAYMENT_WU_ENTRY_ZIP', 'Zip Code: ');
define('MODULE_PAYMENT_WU_ENTRY_CITY', 'City: ');
define('MODULE_PAYMENT_WU_ENTRY_COUNTRY', 'Country: ');
define('MODULE_PAYMENT_WU_ENTRY_PHONE', 'Phone: ');
define('MODULE_PAYMENT_WU_ENTRY_QUESTION', 'Question: ');
define('MODULE_PAYMENT_WU_ENTRY_ANSWER', 'Answer: ');

define('MODULE_PAYMENT_WU_TEXT_TITLE', 'Western Union');
define('MODULE_PAYMENT_WU_TEXT_DISPLAY_TITLE', 'Western Union');	// for title displaying purpose if different from MODULE_PAYMENT_WU_TEXT_TITLE
define('MODULE_PAYMENT_WU_TEXT_DESCRIPTION', 'Western Union Payment Module');
//define('MODULE_PAYMENT_WU_TEXT_CONFIRMATION', MODULE_PAYMENT_WU_MESSAGE);
//define('MODULE_PAYMENT_WU_TEXT_EMAIL_FOOTER', "Make Payable To:\n\n" . MODULE_PAYMENT_WU_ENTRY_FIRST_NAME . MODULE_PAYMENT_WU_RECEIVER_FIRST_NAME . " - " . MODULE_PAYMENT_WU_ENTRY_LAST_NAME . MODULE_PAYMENT_WU_RECEIVER_LAST_NAME . " - "  . MODULE_PAYMENT_WU_ENTRY_ADRESS . MODULE_PAYMENT_WU_RECEIVER_ADRESS . " - "  . MODULE_PAYMENT_WU_ENTRY_ZIP . MODULE_PAYMENT_WU_RECEIVER_ZIP . " - "  . MODULE_PAYMENT_WU_ENTRY_CITY . MODULE_PAYMENT_WU_RECEIVER_CITY . " - "  . MODULE_PAYMENT_WU_ENTRY_COUNTRY . MODULE_PAYMENT_WU_RECEIVER_COUNTRY . " - "  . MODULE_PAYMENT_WU_ENTRY_PHONE . MODULE_PAYMENT_WU_RECEIVER_PHONE . "\n\n" . 'Your order will not ship until we receive MCTN payment number.');
define('MODULE_PAYMENT_WU_TEXT_EMAIL_FOOTER', str_replace(array("<br>", "<BR>", "\r\n"), array("\n", "\n", ''), MODULE_PAYMENT_WU_EMAIL_MESSAGE));
?>