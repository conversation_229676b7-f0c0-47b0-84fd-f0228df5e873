<?php

define('HEADING_TITLE', 'My Buyback Favourite');
define('NAVBAR_TITLE', 'My Buyback Favourite');

define('TABLE_HEADING_PRESALE_NOTIFY', 'Pre-Sale Notify');

define('TEXT_LINKS_FOUND', '%s Links Found');
define('TEXT_UNIT_PRICE', 'Unit Price');
define('TEXT_ADD_NEW', 'Add');
define('TEXT_HEADING_PRESALE_ADD_NOTIFY', 'Add Pre-sale Notify');
define('TEXT_PRESALE_REMOVE_NOTIFY', 'Remove Pre-Sale Notify');

define('TEXT_SELL_THIS_LINK', 'Sell this');

define('TEXT_NOT_ACCEPT_BUYBACK', " Sorry we are not currently accepting buybacks for the selected game.<br /> Please check again in 24 hours or e-mail to <a href=\"mailto:<EMAIL>\"><EMAIL><\/a> for further details.");
define('TEXT_FORM_INCOMPLETE', 'Incomplete field');
define('TEXT_QUANTITY_INVALID', "You have entered an incorrect amount. Please enter an amount within the minimum and maximum quantity.");

define('TEXT_ERROR_TRYAGAIN', 'An error has occured. Please try again.');
define('TEXT_PLACE_ORDER', 'Place Order');

define('TEXT_FAVOURITE_EXISTS', 'This favourite link already exists.');
define('TEXT_FILTER_BY', 'Filter By');

define('SELECT_TEXT_DELETE_FAV', 'Delete my favourite');

define('JS_CONFIRM_DELETE_FAV', 'Are you sure you want to delete favourite link?');

define('SUCCESS_ADD_PRESALE', 'Success: Server has been successfully added to pre-sale notification list.');
define('SUCCESS_REMOVE_PRESALE', 'Success: Server has been successfully removed from pre-sale notification list.');
define('SUCCESS_ADD_FAV', 'Success: My Favourite has been successfully added.');
define('SUCCESS_REMOVE_FAV', 'Success: My Favourite has been successfully deleted.');
?>
