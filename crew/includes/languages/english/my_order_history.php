<?php

/*
  $Id: my_order_history.php,v 1.17 2013/08/02 05:03:29 chingyen Exp $

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2003 osCommerce

  Released under the GNU General Public License
 */

define('HEADER_TITLE_MY_ORDER_HISTORY', 'My Order Buyback History');

define('HEADING_TITLE', HEADER_TITLE_MY_ORDER_HISTORY);
define('NAVBAR_TITLE', HEADER_TITLE_MY_ORDER_HISTORY);

define('HEADER_ORDER_REPORT', 'Order Report');

define('TEXT_ALL_GAMES', '* All Games *');
define('TEXT_ALL_ORDER_STATUS', 'All Order Status');

define('TEXT_EXPIRES', 'Order will be canceled in:');
define('TEXT_MINUTES', 'mins');

define('TEXT_TIME_LEFT', 'Time Left (mins)');
define('TEXT_RESULT_SEARCH', '%s result has been search');

define('TEXT_PENDING_DELIVERY', 'Pending Delivery');
define('TEXT_PENDING_PAYMENT', 'Pending Payment');
define('TEXT_RECEIVED', 'Received');

define('TEXT_PENDING', '1st List');
define('TEXT_PROCESSING', 'Second List');
define('TEXT_COMPLETED', 'Confirmed List');
define('TEXT_CANCELLED', 'Cancelled');

define('TEXT_GAME_CURRENCY', 'Game Currency');
define('TEXT_HIGH_LEVEL_ACCOUNT', 'High Level Account');

define('TEXT_ERROR_MSG', 'Error Message');
define('TEXT_ERROR_TRYAGAIN', 'An error has occured. Please try again.');
define('TEXT_ERROR_INVALID_QTY', 'Invalid Quantity. Please try again.');
define('TEXT_ERROR_NO_SS_AFTER_UPLOADED', 'No after trade screenshot. Please try again.');
define('TEXT_ERROR_NO_SS_BEFORE_UPLOADED', 'No before trade screenshot. Please try again.');
define('TEXT_SECONDS_COUNTDOWN', '(Refreshing in %s sec)');

define('TEXT_CHARACTER_NAME_IN_PREPARATION_QUEUE', '<span class="redIndicator">Queuing for Transfer, your place in queue: %s</span>');
define('TEXT_CHARACTER_NAME', 'Sender Character ID');
define('TEXT_CHARACTER_NAME_IN_PREPARATION', '<span class="redIndicator">Wait for update</span>');
define('TEXT_CHARACTER_NAME_HELP', 'Kindly provide your character name for us to arrange receipt.');
define('TEXT_DELIVERY_TIME', 'Delivery Time');
define('TEXT_DELIVERY_TIME_HELP', 'Note: Order will be cancelled if exceeded.');
define('TEXT_CONTACT_NAME', 'Name');
define('TEXT_CONTACT_NO', 'Contact Number');
define('TEXT_ADDITIONAL_COMMENTS', 'Additional Comments');
define('TEXT_ORDER_REFERENCE', 'Order Reference');
define('TEXT_TOTAL_PRICE', 'Total Amount');
define('TEXT_FILE_SIZE', '(File Size: %d KB)');
define('TEXT_OF', 'of');
define('TEXT_PROCEED', 'Please kindly proceed to the designated in-game transfer location, we will be contacting your character within 5minutes. Should we be not able to contact your character within 2minutes, the order will be cancelled and you will have to place a new order with us.');
define('TEXT_UPLOAD_SS', 'Upload');

define('SUCCESS_ORDER_HISTORY_ORDER_CANCEL_SUCCESS', 'Success: Buyback order has been successfully canceled.');
define('ERROR_ORDER_HISTORY_ORDER_CANCEL_FAILED', 'Error: Buyback order cancellation failed.');
define('TEXT_SCREENSHOT_BEFORE_SEND_GOLD', 'Screenshot before trade');
define('TEXT_SCREENSHOT_AFTER_SENT_GOLD', 'Screenshot after trade');

define('JS_CONFIRM_CANCEL_ORDER', 'Confirm cancel this order?');
define('JS_NO_SS_BEFORE_TRADE', 'Please upload before send gold screenshot');
define('JS_NO_SS_AFTER_TRADED', 'Please upload after send gold screenshot');
define('JS_INVALID_SS_FILE_EXT', 'Error: Screenshot upload, Only allowed *.jpg, *.jpeg file type');
define('ERROR_UPLOAD_FAILED', 'Screenshot Upload failed. Please try later.');

define('LINK_SEND_STOCK_FAQ', 'Submit Order FAQ');

define('EMAIL_TEXT_CLOSING', 'Thank you for supporting ' . STORE_NAME . '.'); // unique

define('TABLE_HEADING_RECEIVER_CHAR_NAME', 'Character Name');
define('TABLE_HEADING_DELIVERY_METHOD', 'Delivery Method');
define('TABLE_HEADING_ORDER_NUMBER', 'Order No.');
define('TABLE_HEADING_GAME', 'Game');
define('TABLE_HEADING_SERVER', 'Server');
define('TABLE_HEADING_REQUEST_QTY', 'Qty Submitted');
define('TABLE_HEADING_SENT_QTY', 'Qty Delivered');
define('TABLE_HEADING_AMOUNT', 'Amount');
define('TABLE_HEADING_STATUS', 'Status');
define('TABLE_HEADING_RECEIVER_CHAR', 'Receiving Character');
define('TABLE_HEADING_DELIVERED_ID', 'Delivered ID');
define('TABLE_HEADING_CONFIRM_QUANTITY', 'Delivered QTY');

define('ENTRY_RECEIVER_CHAR_NAME', 'Receiver Character ID');
define('ENTRY_ORDER_STATUS', 'Order Status');
define('ENTRY_ORDER_NUMBER', 'Order #');
define('ENTRY_PRODUCT_TYPE', 'Product Type');
define('ENTRY_GAME', 'Game');
define('ENTRY_START_DATE', 'Start Date');
define('ENTRY_END_DATE', 'End Date');

define('ORDERS_LOG_UNLOCK_OWN_ORDER', '##ul_1##');

define('TEXT_USER_REQUIREMENT', 'Please upload your before and after trade screen shot which to have the required fields as:');
define('TEXT_FIRST_SS_TITLE', '1. First Screen Shot:');
define('TEXT_FIRST_SS_DESC', 'Trade window with Customer character and make sure your bags are open to show the gold you have. Show the Gold amount to be traded according to the buyback order you have made;');
define('TEXT_SECOND_SS_TITLE', '2. Second Screen Shot:');
define('TEXT_SECOND_SS_DESC', 'Floating text will appear with the words "Trade Complete" after clicking on "trade" and Trade Completion time with bags are open to show the gold you have after trade complete.');
define('TEXT_SS_REF_TITLE', '3. Screenshot reference:');
define('TEXT_SS_REF_DESC', '<a href="http://kb.offgamers.com/?p=310">http://kb.offgamers.com/?p=310</a>');
define('TEXT_REQUIREMENT_TITLE', '4. Requirement:');
define('TEXT_REQUIREMENT_DESC', '*.jpg format. Each file size cannot exceed 800kb.');
define('TEXT_MORE_THAN_2_PHOTO', '5. If exceed than 2 screen shot, please send us the rest of the screen shot via MSN or <NAME_EMAIL>');

define('POPUP_HEADER_BUYBACK_ORDER_CANCEL', 'Buyback Order Cancel');
define('POPUP_TEXT_DETAILS', 'Details (if any)');
define('POPUP_LIMIT_CHARACTERS', '(%s characters)');
define('POPUP_LIMIT_CHARACTERS_ERROR', 'Character limit has been exceeded');
?>