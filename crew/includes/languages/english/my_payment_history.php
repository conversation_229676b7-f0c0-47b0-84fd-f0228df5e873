<?
/*
  $Id: my_payment_history.php,v 1.16 2013/07/18 06:49:26 hungnian.yong Exp $

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2003 osCommerce

  Released under the GNU General Public License
*/

define('HEADING_TITLE', HEADER_TITLE_MY_PAYMENT_HISTORY);
define('NAVBAR_TITLE', HEADER_TITLE_MY_PAYMENT_HISTORY);

define('TEXT_PAYMENT_ID', 'Payment No');
define('TEXT_PAYMENT_METHOD', 'Payment Method');
define('TEXT_DATE_REQUESTED', 'Date Requested');
define('TEXT_RESULTS_FOUND', 'Results Found');
define('TEXT_AMOUNT_PAID', 'Amount Paid');
define('TEXT_REMAINING', 'Remaining');
define('TEXT_DATE', 'Date');
define('TEXT_TIME', 'Time');

define('TEXT_ACCOUNT', 'Account');
define('TEXT_WITHDRAW_AMT', 'Withdraw Amount');
define('TEXT_CLOSE', 'Close');
define('TEXT_INVALID_AMOUNT', 'Invalid Amount.');
define('TEXT_CANCEL', 'Cancel');
define('TEXT_OK', 'OK');
define('TEXT_CONFIRM_WITHDRAWAL', 'Confirm Withdrawal?');
define('TEXT_REQUEST_SUCCESSFULL', 'Request Successful');

define('TEXT_ANNOUNCEMENT', '<span style="color: red">*Important*</span> Please kindly note that effective from 27 October 2010, suppliers will automatically be merged into a new and revised PayPal (in USD) as a disbursement method. The Paypal (in USD) new version will result in revised fees which will be lower than the Paypal (in USD) old version. For further information, please refer to <a href="my_payment_history.php?action=show_balance#paypal">here</a>.');

define('ENTRY_WITHDRAW_CURRENT_BALANCE', 'Current Balance:');
define('ENTRY_WITHDRAW_RESERVE_AMOUNT', 'Reserve:');
define('ENTRY_WITHDRAW_AVAILABLE_BALANCE', 'Available for Withdrawal:');
define('ENTRY_WITHDRAW_AMOUNT', 'Withdraw Amount:');
define('ENTRY_WITHDRAW_PAYMENT_ACCOUNT', 'Withdraw To:');
define('ENTRY_WITHDRAW_FINAL_AMOUNT', 'Receivable Amount:');

define('ENTRY_WITHDRAW_PAYMENT_METHOD', 'Payment Method:');
define('ENTRY_WITHDRAW_MIN_AMT', '&nbsp;- Minimum Amount:');
define('ENTRY_WITHDRAW_MAX_AMT', '&nbsp;- Maximum Amount:');
define('ENTRY_WITHDRAW_FEES', '&nbsp;- Withdraw Fees:');

//For payment module------------------------------------------------------------
define('HEADER_FORM_ACC_STAT_TITLE', 'Account Statement');
define('ENTRY_ORDER_START_DATE', 'Start Date<br><small>(YYYY-MM-DD)</small>');
define('ENTRY_ORDER_END_DATE', 'End Date<br><small>(YYYY-MM-DD)</small>');
define('ENTRY_PAYMENT_ID', 'Payment ID');
define('ENTRY_ORDER_ID', 'Order ID');
define('ENTRY_RECORDS_PER_PAGE', 'Records per page');
define('ENTRY_PAYMENT_STATUS', 'Payment Status');
define('TEXT_LANG_RECORDS_PER_PAGE', '');
define('TABLE_HEADING_ACC_STAT_DATETIME', 'Date/Time');
define('TABLE_HEADING_ACC_STAT_ACTIVITY', 'Activity');
define('TABLE_HEADING_ACC_STAT_PAYMENT_STATUS', 'Payment Status');
define('TABLE_HEADING_ACC_STAT_DEBIT', 'Debit');
define('TABLE_HEADING_ACC_STAT_CREDIT', 'Credit');
define('TABLE_HEADING_ACC_STAT_ACTION', 'Action');
define('TABLE_HEADING_ACC_STAT_BALANCE', 'Balance');
define('TEXT_ACC_STAT_NOT_APPLICABLE', '-');
define('TEXT_ACC_STAT_PAYMENT_STATUS_UPDATE_DATE', '%s on %s');
define('TEXT_ACC_STAT_ADMIN_COMMENT', 'Comment:');
define('TEXT_PM_NO_EXISTING_PAYMENT_ACCOUNT', 'There is no any payment account records yet.');
define('LINK_PM_EDIT_PM_ACCOUNT', 'Edit payment account');
define('LINK_PM_DELETE_PM_ACCOUNT', 'Delete payment account');
define('SUCCESS_PM_DELETE_PM_ACCOUNT', 'Success: Payment account has been successfully deleted.');
define('ERROR_INVALID_PM_BOOK_SELECTION', 'Error: You are editing invalid payment account.');
define('ERROR_INVALID_PM_SELECTION', 'Error: Please select valid payment method.');
define('ERROR_EMPTY_PM_ALIAS', 'Error: Payment title is required.');
define('ERROR_PM_FIELD_INFO_REQUIRED', 'Error: Please provide "%s" information!');
define('ERROR_PM_ACCOUNT_BOOK_FULL', 'Your payment account book is full. Please delete an unneeded account to save a new one.');
//--end for payment module--------------------------------------------------------
define('COMMENT_WITHDRAW_FUNDS_REQUESTED', 'Request for funds to be withdrawn to %s(%s)');
define('TEXT_NO_WITHDRAW_LIMIT', 'Any');
define('TEXT_WITHDRAW_FEES_FREE', 'Free');

define('LINK_WITHDRAW_ACCOUNT_STATEMENT', '[<a href="%s">Account Statement</a>]');

define('ERROR_WITHDRAW_NO_CURRENCY_ACCOUNT_INFO', 'Please select the currency account to withdraw.');
define('ERROR_WITHDRAW_NO_AVAILABLE_FUNDS', 'You have no available funds to withdraw or your withdraw amount exceed the available funds.');
define('ERROR_WITHDRAW_NO_PAYMENT_ACCOUNT_BOOK', 'Please select which account to receive the funds.');
define('ERROR_WITHDRAW_NO_WITHDRAW_AMOUNT', 'Please enter your withdraw amount.');
define('ERROR_WITHDRAW_NOT_SUCCESS', 'Your funds withdraw is failed.');
define('ERROR_WITHDRAW_DISABLE_WITHDRAWAL', "Your payment withdrawal capability has been disabled.\n\rPlease contact <a href='mailto:<EMAIL>'>Customer Support</a> for detail.");
define('WARNING_WITHDRAW_FEES_HIGHER_THAN_WITHDRAW_AMOUNT', 'Withdrawal fees are higher than the withdrawal amount.');
define('WARNING_WITHDRAW_NOT_ALLOWED_PAYMENT_METHOD', '%s is not available for this withdraw amount.');
define('SUCCESS_WITHDRAW_SUCCESS', 'Your funds has been successfully withdraw.');

define('CONTENTS_WARNING_WITHDRAW_NOT_ALLOWED_PAYMENT_METHOD', '<span class="redIndicator">%s is not available for this withdraw amount</span>');

define('JS_ERROR_NO_WITHDRAW_AMOUNT', 'Please enter your withdraw amount');
define('JS_ERROR_INVALID_WITHDRAW_AMOUNT', 'Invalid withdraw amount');

// E-mail
define('EMAIL_PAYMENT_PAYMENT_UPDATE_SUBJECT', implode(' ', array(EMAIL_SUBJECT_PREFIX, "Payment Update #%s")));

define('EMAIL_PAYMENT_TEXT_TITLE', 'You request to withdraw %s via %s from your OffGamers Store Balance account.');
define('EMAIL_PAYMENT_TEXT_SUMMARY_TITLE', 'Payment Summary:');
define('EMAIL_PAYMENT_TEXT_PAYMENT_NUMBER', 'Payment Number: %s');
define('EMAIL_PAYMENT_TEXT_PAYMENT_DATE', 'Payment Date: %s');
define('EMAIL_PAYMENT_TEXT_COMMENTS', 'Comment:' . "\n%s\n");
define('EMAIL_PAYMENT_TEXT_STATUS_UPDATE', "Status: %s");

define('EMAIL_PAYMENT_WITHDRAW_CLOSING', 'Thank you for supporting ' . STORE_NAME . '.');
define('EMAIL_PAYMENT_FOOTER', EMAIL_FOOTER);

define('TEXT_MANUAL_ADDITION', 'Manual Addition');
define('TEXT_MANUAL_DEDUCTION', 'Manual Deduction');
define('TEXT_COMPENSATE', 'Compensate');
define('TEXT_PAYMENT_CANCELLATION', 'Cancelation of Payment %s');
define('TEXT_REDEEM_CANCELLATION', 'Cancelation of Redemption %s');

define('QUESTION_AND_ANSWER_HEADER', 'QUESTION & ANSWER');
define('QUESTION_AND_ANSWER_CONTENTS', "<b>How Do I Withdraw My Money?</b><br><br>

The 'Withdraw' functions allow you to withdraw available funds from your seller's account to any of your selected payment account (<a href=\"http://kb.offgamers.com/?p=269\">Click here to learn how to setup a payment account</a>.) depending on your preferred currency.
<br><br>

Examples:<br>
<i>If you need to withdraw your money in RMB, you may select your 'Alipay' account ; If you need to withdraw your money in USD, you may select your 'Paypal' account.</i>
<br><br>

To learn how to make your withdrawals, <a href=\"http://kb.offgamers.com/?p=374\">click here</a>.
<br><br>


<b>How do I make a purchase from the OffGamers store with my Seller Credits?</b><br><br>

If you wish to make purchases from our store, you can always withdraw your money into an OffGamers Store Voucher payment account, reloading your OffGamers account Store Credit balance so that you can make your purchases at any time. <a href=\"http://kb.offgamers.com/?p=763\">Click here to learn how to withdraw your money into OffGamers Store Credits</a>.
<br><br>


<a name=\"paypal\"></a><b>What happened to the \"PayPal (in USD)\" option?</b><br><br>

This option has been revised and a new system has been implemented automatically, bringing more benefits to our loyal suppliers.
<br><br>


<b>When will the PayPal (in USD) new version come into effect?</b><br><br>

The PayPal (in USD) new version will come into effect on 27 October 2010. Once in effect, the PayPal (in USD) will no longer follow the old system. Rest assured that the payout will still be handled by Paypal and no security issues will be compromised.
<br><br>


<b>What is the difference between the PayPal (in USD) old version and PayPal (in USD) new version?</b><br><br>

PayPal (in USD) old version charges the Supplier US $1 per Payout request from us. PayPal (in USD) new version costs the Supplier 2% per Payout request with a maximum charge of US $1. This means the Supplier may be able to save some money with PayPal (in USD) new version. Please also note that there will no longer be fees incurred on the recipient's side, as was the case when using the PayPal (in USD) old version.
<br><br>

<table width=\"100%\" cellspacing=\"0\" cellpadding=\"4\" border=\"1\">
	<tbody>
	<tr>
		<td width=\"25%\" bgcolor=\"#cccccc\">
			<p><b>Disbursement Method</b></p>
		</td>
		<td width=\"40%\" bgcolor=\"#cccccc\">
			<p><b>Fees</b></p>
		</td>
		<td width=\"32%\" bgcolor=\"#cccccc\">
			<p><b>Recipient</b></p>
		</td>
	</tr>
	<tr>
		<td>
			<p>PayPal (in USD) old version</p>
		</td>
		<td>
			<p>US $1 per payout request.</p>
		</td>
		<td>
			<p>The recipient is charged a small fee for receiving the payment.</p>
		</td>
	</tr>
	<tr>
		<td>
			<p>PayPal (in USD) new version</p>
		</td>
		<td>
			<p>2% per Payout request with a maximum charge of US $50.</p>
		</td>
		<td>
			<p>The recipient incurs no additional fee for receiving the payment.</p>
		</td>
	</tr>
</tbody>
</table>
");
?>