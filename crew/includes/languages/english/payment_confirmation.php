<?php
define('HEADING_CONFIRM_PAYMENT', 'CONFIRM YOUR PAYMENT');
define('CONTENT_PAYMENT_REVIEW', 'Review the payment details below and click <b>Confirm to pay</b> to complete your secure payment.');
define('TITLE_PRODUCT_TITLE', 'Product');
define('TITLE_QUANTITY', 'Quantity');
define('TITLE_ORDER_AMOUNT', 'Unit Price');
define('TITLE_PAY_TO', 'Pay to');
define('TITLE_PROCESSING_FEE', 'Processing Fee');
define('TITLE_TOTAL_AMOUNT', 'Total Amount');
define('TITLE_BUTTON_CANCEL', 'Cancel');
define('TITLE_BUTTON_CONFIRM_PAYMENT', '1 Click Paypal Express Checkout');
define('TITLE_DEVICEPIN_MESSAGE', 'Security PIN send to your email');
define('TITLE_DEVICEPIN', 'Security PIN');
define('ERROR_DEVICEPIN_MISSING', 'Security PIN is Missing');
define('ERROR_DEVICEPIN_INCORRECT', 'Security PIN is Incorrect');
define('EMAIL_DEVICE_TITLE', 'Your OffGamers account: Accessing from different device / browser');
define('EMAIL_DEVICE_HEADER', 'Dear %s,');
define('EMAIL_DEVICE_CONTENT_1', 'Are you checking out with <b>Payment method: %s</b> from a new browser or new device? Here is the Security PIN to complete the checkout:');
define('EMAIL_DEVICE_CONTENT_2', 'If you suspect someone else may be attempting to access your account, please <a href= "' . HTTP_SHASSO_PORTAL . '/profile/index">change your password</a> immediately.');
define('EMAIL_DEVICE_CONTENT_3', 'Thank you for maintaining the security of your account.');
define('EMAIL_FOOTER', STORE_EMAIL_SIGNATURE);
define('LINK_RESEND_SECURITY_PIN', 'Resend Security Pin');
define('ERROR_MAXIMUN_SECURITY_PIN_TRIAL', 'Maximum %s attempts occured, please try again in an hour time.');
?>