<?php

// Mantis # 0000024 @ 200810241055 - Add popup message & form's text fields
define('HEADER_TITLE_SECRET_QUESTION', 'Set Secret Question And Answer');
define('HEADER_TITLE_CHANGE_SECRET_QNA', 'Change Secret Q&A');

define('SET_SECRET_QUESTION', 'Secret Question:&nbsp;');
define('SET_SECRET_QUESTION_TEXT', '*');
define('SET_ANSWER', 'Answer:&nbsp;');
define('SET_ANSWER_TEXT', '*');
define('ENTRY_SECRET_QUESTION_ERROR', 'You must select a secret question from the Secret Question pull down menu.');
define('ENTRY_ANSWER_ERROR', "Your Answer must contain a minimum of %d characters.");

define('TEXT_REGISTER_SECRET_QUESTION_AND_ANSWER','Please register your <a href='. tep_href_link(FILENAME_SET_SECRET_QNA) .'>Secret Question & Answer</a> in order to update your profile');

// Show done message @ ************
define('ENTRY_UPDATE_DONE', 'Your Secret Question and Answer are updated successfully.');
define('ENTRY_SAVE_DONE', 'Your Secret Question and Answer are saved successfully.');

define('HIDDEN_PBLINK', '<input type="hidden" id="hid_value" name="hid_value">'); 
define('POPUP_TEXT_CHANGE_TO_SECRET_QUESTION_AND_ANSWER_NOTICE', tep_db_input('<div id="popupTitle"><p>Secret Q&A Security Method Setup Notice</p></div><div id="popupMesgTop"><p>Dear Valued Customer,</p></div><p id="popupMesg">Bringing account security to a higher level, we will be replacing the usual PIN Number Method with our new <span class="redIndicator">Secret Q&A Security Method</span>, a newly-introduced feature which is much more convenient and secure. You may setup your Secret Q&A through the account management page, and it will replace your PIN Number feature once setup is complete.<br><br>Please complete your setup within the next <span class="redIndicator">15 days</span>. Should the setup be not completed within the <span class="redIndicator">15 days</span> period, your account will be automatically frozen by our system.<br><br>For more information on our Secret Q&A Security Method, please refer to our info center or you may contact our customer service.<br><br>Thank you for your efforts in ensuring the security of your account.<br></p><div id="popupMesgBottom"><p>OffGamers Team</p></div><div id="popupBottom"><p><a href="%s" class="subModelLink" onclick="document.getElementById(\'hid_value\').value=\''.stripslashes($_SESSION['ssqna']='popuplink').'\'">Setup For Secret Q&A Security Method Now</a><br><a href="javascript:;" %s class="subModelLink">I\'ll Do It Later</a><br>Time Left: %d Days</p></div>'));
define('POPUP_TEXT_CHANGE_TO_SECRET_QUESTION_AND_ANSWER_TITLE', 'Secret Q&A Security Method Setup Notice');
//##

// Mantis # 0000024 @ ************ - Add form's text fields
define('TEXT_PIN_NUMBER', 'PIN Number');
define('TEXT_HELP_PIN_NUMBER', 'Kindly provide your PIN Number');
define('TEXT_PINNUMBER_REQUIRED', 'PIN Number is required to update this field : %s');
define('TEXT_IS_MANDATORY', 'Fields marked with <span class="redIndicator">*</span> is mandatory');
define('TEXT_SAVE', 'Save');
define('TEXT_MUST_LOGIN', 'Login required to access this section.');

define('BUTTON_SAVE', 'Save');
define('BUTTON_REQUEST_TOKEN', 'Request a Security Token');
define('TEXT_SECRET_QNA_ISSET', 'Due to security purposes, your secret Question and Answer cannot be changed through this page. Please contact a <a href="'.tep_href_link(FILENAME_CUSTOMER_SUPPORT, '', 'SSL').'">support representative</a> to request for change.');
// You have registered your Secret Question and Answer in our system, if you would like to reset it, please kindly contact our '. LIVE_SUPPORT_LINK .' to reset your Secret Question and Answer. Thank you.

define('ENTRY_SECRET_ANSWER_QUESTION_NOTICE', 'Your Secret Question & Answer is a security measure to ensure the safety of your account. It will be requested when changes are made to your OffGamers account, during withdrawal of your Withdrawable Store Credits and more.');
define('ENTRY_SECRET_ANSWER_NOTICE', 'Please ensure that the Secret Question & Answer is known only to you and do not share it with anyone else.');
define('ENTRY_CONTACT_NUMBER_EXIST_ERROR', "The mobile number you had provide is in used or invalid. Please key in another mobile number.");
define('TEXT_CHANGE_PHONE_NUMBER', '(Not your mobile phone number? <a id="link_edit_phone_number" href="' . tep_href_link(FILENAME_ACCOUNT_EDIT) . '">Change here</a>.)');

define('EMAIL_USER_RESET_PIN_SUBJECT', implode(' ', array(EMAIL_SUBJECT_PREFIX, 'Your Security Token has been reset')));
define('EMAIL_USER_RESET_PIN_BODY', 'Your new Security Token is %s. This token will expired in 24 hours from the time you receive this email.');

define('TEXT_SECRET_QNA', 'SECURITY TOKEN');
define('TEXT_DORMANT_ACCOUNT_QNA_REQUEST', 'Dormant accounts for more than 90 days are required to enter the Security Token to reactivate the account for purchases.<br><br>Your security token will expire in 10 minutes. Subsequent request for new security token should not exceed more than 3 times in a single day.');
define('TEXT_FORGOT_QNA', 'Not able to verify or receive security token? Please <a href="' . tep_href_link(FILENAME_CUSTOMER_SUPPORT) . '">contact us</a> immediately for assistance.');
define('ENTRY_QNA_MISMATCH_ANSWER_ERROR', 'Sorry, the security token provided is incorrect. Please try again.');

define('TEXT_REQUEST_TOKEN_MSG', 'Your security token will expire in 10 minutes. Subsequent request for new security token should not exceed more than 3 times in a single day.');
define('TEXT_REQUEST_TOKEN_SUCCESS_MSG', 'Security token is sent via SMS to your registered mobile phone number %s.');
define('TEXT_REQUEST_TOKEN_REUSE_MSG', 'Please key in the previous security token.');
define('TEXT_REQUEST_TOKEN_FAIL_MSG', 'You have exceeded the allowable limit for security tokens. Please try again after 24 hours.');
define('TEXT_REQUEST_TOKEN_HELP_MSG', 'Security token is sent via SMS to your registered mobile phone number. Lost number or invalid number, please contact our <a href="%s" class="whiteText">Customer Service</a>.');
?>