<?php

define('HEADING_TITLE', 'Verification Submission Form');
define('FORM_TAB_TITLE', 'Submission Form');
define('FORM_TAB_SUB_TITLE', 'To ensure a smooth verification process, please confirm that all documents are correct/as per required.');

define('REMARK_UPLOAD_SIZE_LIMIT', '(<i>500kb size limit,jpg,gif or png only</i>)');

define('ENTRY_PURCHASE_AUTHORIZATION_FORM', 'Purchase authorization form.');
define('ENTRY_UTILITY_BILL', 'Utility bill that matches your billing information.');
define('ENTRY_PHOTO_IDENTIFICATION', 'Valid photo Identification (which has not expired) revealing your name and address that matches the billing address of your credit card or paypal.');
define('ENTRY_CREDIT_CARD_FRONT', 'Copy of the front of your credit card showing only the first 4 digits and the last 4 digits. <a href="http://kb.offgamers.com/en/category/buy-purchase/verification/" target="_blank">Click here for verification guidelines</a>');
define('ENTRY_CREDIT_CARD_BACK', 'Copy of the back of your credit card with signature. (For security reasons,please blank out the CVV and credit card digit impressions shown at the back of the card.) <a href="http://kb.offgamers.com/en/category/buy-purchase/verification/" target="_blank">Click here for verification guidelines</a>');
?>