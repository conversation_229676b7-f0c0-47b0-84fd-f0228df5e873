<?php

define('HEADING_TITLE', HEADER_TITLE_MY_VIP_ORDERS_HISTORY);
define('NAVBAR_TITLE', 'VIP Seller');

define('HEADER_ORDER_REPORT', 'Order Report');

define('TABLE_HEADING_DELIVERY_METHOD', 'Delivery Method');
define('TABLE_HEADING_CONFIRM_QUANTITY', 'Delivered QTY');
define('TABLE_HEADING_ORDER_QUANTITY', 'Order QTY');
define('TABLE_HEADING_ORDER_AWAITING_ACCEPT_QUANTITY', 'QTY Assign');
define('TABLE_HEADING_RECEIVER_CHAR_NAME', 'Receiver ID');
define('TABLE_HEADING_SENDERS_CHARACTER_ID', 'Sender ID');
define('TABLE_HEADING_SERVER_NAME', 'Server');
define('TABLE_HEADING_SUBMIT_QUANTITY', 'Submit QTY');
define('TABLE_HEADING_PRICE_PER_UNIT', 'Unit Price');
define('BUTTON_TRADE_WITH_US', 'Trade with OffGamers');
define('BUTTON_TRADE_CUSTOMERS', 'Trade with buyer');
define('BUTTON_TRADE_CANCEL', 'cancel');
define('ENTRY_RECEIVER_CHAR_NAME', 'Receiver ID');
define('TEXT_ALL_GAMES', '* All Games *');
define('TEXT_ALL_ORDER_STATUS', '* All Orders Status *');
define('TEXT_ADDITIONAL_COMMENTS', 'Other Comments');
define('TEXT_CHARACTER_NAME_IN_PREPARATION', '<span class="redIndicator">Being Prepared...</span>');
define('TEXT_CHARACTER_NAME_IN_PREPARATION_QUEUE', '<span class="redIndicator">Queuing for Transfer, your place in queue</span>');
define('TEXT_CHARACTER_NAME', 'Sender ID');
define('TEXT_COUNTDOWN', 'Countdown:');
define('TEXT_CONTACT_NAME', 'Name');
define('TEXT_CONTACT_NO', 'Contact Number');
define('TEXT_DELIVERY_TIME', 'Delivery Time');
define('TEXT_RESULT_SEARCH', '%s result has been search');
define('TEXT_EXPIRES', 'Valid until:');
define('TEXT_ERROR_MSG', 'Error Message');
define('TEXT_ERROR_INVALID_QTY', 'Invalid Quantity. Please try again.');
define('TEXT_ERROR_NO_SS_AFTER_UPLOADED', 'No after trade screenshot. Please try again.');
define('TEXT_ERROR_NO_SS_BEFORE_UPLOADED', 'No before trade screenshot. Please try again.');
define('TEXT_ERROR_SS_AFTER_SIZE_EXCEED_LIMIT', 'After delivered screen shot was exceed %sKB. Please try again');
define('TEXT_ERROR_SS_BEFORE_SIZE_EXCEED_LIMIT', 'Before deliver screen shot was exceed %sKB. Please try again');
define('TEXT_ERROR_SS_AFTER_EXTENSION_WRONG', 'After delivered screen shot file format incorrect. Please try again');
define('TEXT_ERROR_SS_BEFORE_EXTENSION_WRONG', 'Before deliver screen shot file format incorrect. Please try again');
define('TEXT_ERROR_GET_DATA_FAILED', 'Database search failure');
define('TEXT_FILE_SIZE', '(Capacity: %d KB)');
define('TEXT_GAME_CURRENCY', 'Game Currency');
define('TEXT_MATCH_BACKORDER_EXACT_AMOUNT', '(If submit more than %d ,we only accept %d ,we do not accept any quantity in the range %d-%d)');
define('TEXT_MATCH_BACKORDER_AMOUNT_ONLY', '(Only can submit %d)');
define('TEXT_MINUTES', 'mins');
define('TEXT_NO_RECORD_FOUND', 'Do not have related information');
define('TEXT_OF', ' X ');
define('TEXT_ORDERS_FOUND', 'Search results');
define('TEXT_ORDER_REFERENCE', 'Order Reference');
define('TEXT_PRODUCT_TYPE', 'Product type');
define('TEXT_QUANTITY_INVALID', "You have entered an incorrect amount , Please enter an amount between the minimum and maximum quantity");
define('TEXT_QUANTITY_MAX_NOT_MATCH', "You have entered an incorrect amount , please enter same amount as system assigned");
define('TEXT_SCREENSHOT_BEFORE_SEND_GOLD', 'Screenshot before trade');
define('TEXT_SCREENSHOT_AFTER_SENT_GOLD', 'Screenshot after trade');
define('TEXT_TRADE_WITH_US_PRICE', 'trade with OffGamers price: ');
define('TEXT_TRADE_CUSTOMERS_PRICE', 'trade with buyer price: ');
define('TEXT_TOTAL_PRICE', 'Total Amount');
define('TEXT_SECONDS', 'secs');
define('TEXT_VIP_AWAITING_ORDERS_LIST', 'VIP order assign');
define('TEXT_VIP_ORDERS_HISTORY', 'VIP order status');
define('TEXT_WAIT_FOR_REFRESH', 'Refreshing ,please wait');
define('TEXT_SECONDS_COUNTDOWN', '(Refreshing in %s sec)');
define('TEXT_UPLOAD_SS', 'Upload');
define('TEXT_USER_REQUIREMENT', 'Please upload your before and after trade screen shot which to have the required fields as:');
define('TEXT_FIRST_SS_TITLE', '1. First Screen Shot:');
define('TEXT_FIRST_SS_DESC', 'Trade window with Customer character and make sure your bags are open to show the gold you have. Show the Gold amount to be traded according to the buyback order you have made;');
define('TEXT_SECOND_SS_TITLE', '2. Second Screen Shot:');
define('TEXT_SECOND_SS_DESC', 'Floating text will appear with the words "Trade Complete" after clicking on "trade" and Trade Completion time with bags are open to show the gold you have after trade complete.');
define('TEXT_SS_REF_TITLE', '3. Screenshot reference:');
define('TEXT_SS_REF_DESC', '<a href="http://kb.offgamers.com/?p=310">http://kb.offgamers.com/?p=310</a>');
define('TEXT_REQUIREMENT_TITLE', '4. Requirement:');
define('TEXT_REQUIREMENT_DESC', '*.jpg format. Each file size cannot exceed 800kb.');
define('TEXT_MORE_THAN_2_PHOTO', '5. If exceed than 2 screen shot, please send us the rest of the screen shot via MSN or <NAME_EMAIL>');
define('SUCCESS_ORDER_HISTORY_ORDER_CANCEL_SUCCESS', 'Success: Buyback order has been successfully canceled.');
define('ERROR_ORDER_HISTORY_ORDER_CANCEL_FAILED', 'Error: Buyback order cancellation failed.');
define('ERROR_NO_TRADE_CHARACTER', 'Please fill in sender ID');
define('ERROR_INVALID_QTY', 'Invalid Quantity. Please try again.');
define('ERROR_SUBMITTED_BY_OTHER_USER', 'This order has been submitted by other user. Please try again.');
define('ERROR_PLS_TRY_AGAIN', 'Please try again.');
define('WARNING_INVALID_ORDERS', 'Order not existing');
define('JS_CONFIRM_CANCEL_ORDER', 'Confirm cancel this order?');
define('JS_NO_SS_BEFORE_TRADE', 'Please upload before send gold screenshot');
define('JS_NO_SS_AFTER_TRADED', 'Please upload after send gold screenshot');
define('JS_INVALID_SS_FILE_EXT', 'Error: Screenshot upload, Only allowed *.jpg, *.jpeg file type');
define('ERROR_UPLOAD_FAILED', 'Screenshot Upload failed. Please try later.');
define('TEXT_HELP_ORDER_STATUS_SELECT', 'Dear Customer, Our Receiving Character is now ready. Please kindly confirm the Character we show in your order and invite our Character into party. Please ensure you have trade the amount to the character name provided 100% accurate. OffGamers will not be liable for any claims on orders sent to the wrong character or wrong amount. <br>Kindly remind here, to take the Screen Shot of Trade Process until the Trade is Completed. Make sure the Buyback Status is "Pending" before proceed any trade. DO NOT trade any gold if the Buyback Status is already "Cancel", or OffGamers will not assume the responsibility for any problem. Once the transfer is complete, remember to fill in the [Sent Qty] box, and also Upload the Screen Shot. Any extra Screen Shot you may send it to our Mailbox : <EMAIL><br><b>Important:<br>-Check with customer if they can see the gold in trade box, (with asking : Can you see xxx gold?), and then only initiate trade with the customer. This method avoids Blizzard trading operation bug.<br>-If the receiving character is busy for the moment, please do not initiate the trade and close the trade window.</b>');
define('COMMENTS_VIP_TRADE_COMPLETED', 'trade customer completed');
define('COMMENTS_PROCESSING_TO_COMPLETED', 'Your amount will be credited to your OffGamers account in 15 mins.');
define('TEXT_PLS_CLICK_SEARCH_BTN', 'Please click on the SEARCH button to view result.');
?>