<?
define('HEADING_TITLE', HEADER_TITLE_MY_VIP_REGISTER_SERVER);
define('NAVBAR_TITLE', 'VIP Seller');

define('TEXT_SELECT_SERVER', 'Select Games:');
define('TEXT_PLEASE_SELECT', 'Please choose');
define('TEXT_WORKING_DAY', 'Supply day:');
define('TEXT_WORKING_TIME', 'Supply time:');
 
define('TEXT_DAY_0', 'Sunday');
define('TEXT_DAY_1', 'Monday');
define('TEXT_DAY_2', 'Tuesday');
define('TEXT_DAY_3', 'Wednesday');
define('TEXT_DAY_4', 'Thursday');
define('TEXT_DAY_5', 'Friday');
define('TEXT_DAY_6', 'Saturday');
define('TEXT_TIME_ZONE', '(GMT +0800)');

define('BUTTON_VIP_REG_SERVER_SAVE', 'Save');

define('BUTTON_VIP_REG_SERVER_ADD_SERVER', 'Add');
define('BUTTON_VIP_REG_SERVER_DELETE_SERVER', 'Delete');

define('TABLE_HEADER_SERVER_NAME', 'Server');
define('TABLE_HEADER_SERVER_QUANTITY', 'Daily production');
define('TABLE_HEADER_SERVER_SELECTION', 'Select');

define('NOTE_SELECT_SERVER', 'Please select the server and fill the quantity you able to supply. Afterward click "Add". We will going to inform by below method when we are starting buyback :SMS , Email , phone call etc');

define('SUCCESS_VIP_SETTING_UPDATED', 'Success : Setup is updated');
define('SUCCESS_VIP_WORKING_DAYS_UPDATED', 'Success : Supply day is updated');
define('SUCCESS_VIP_WORKING_TIME_UPDATED', 'Success : Supply hour is updated');

define('JS_ERROR_INVALID_WORKING_TIME', 'Supply hour format incorrect');
?>