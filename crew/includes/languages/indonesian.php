<?php

/*
  $Id: english.php,v 1.15 2005/04/04 07:08:34 weichen Exp
 */

// look in your $PATH_LOCALE/locale directory for available locales
// or type locale -a on the server.
// Examples:
// on RedHat try 'en_US'
// on FreeBSD try 'en_US.ISO_8859-1'
// on Windows try 'en', or 'English'

@setlocale(LC_TIME, 'en_US.ISO_8859-1');

define('DATE_FORMAT_SHORT', '%m/%d/%Y');  // this is used for strftime()
define('DATE_FORMAT_LONG', '%A, %d %B, %Y'); // this is used for strftime()
define('DATE_FORMAT', 'm/d/Y'); // this is used for date()
define('DATE_TIME_FORMAT', DATE_FORMAT_SHORT . ' %H:%M:%S');

define('LOCAL_STORE_NAME', 'OffGamers');

// #CHAVEIRO16# BEGIN PHPBB2
define('BOX_BBINDEX', 'tmf.Forum');
// #CHAVEIRO16# END PHPBB2
//CGDiscountSpecials start
define('PRICES_LOGGED_IN_TEXT', 'Harus login untuk harga!');
//CGDiscountSpecials end
////
// Return date in raw format
// $date should be in format mm/dd/yyyy
// raw date is in format YYYYMMDD, or DDMMYYYY
// if USE_DEFAULT_LANGUAGE_CURRENCY is true, use the following currency, instead of the applications default currency (used when changing language)
define('LANGUAGE_CURRENCY', 'USD');

// Global entries for the <html> tag
define('HTML_PARAMS', 'dir="LTR" lang="en" xmlns="http://www.w3.org/1999/xhtml" xmlns:fb="http://www.facebook.com/2008/fbml"');

// charset for web pages and emails
//define('CHARSET', 'iso-8859-1');
define('CHARSET', 'UTF-8');
define('EMAIL_CHARSET', 'UTF-8');

// page title
define('TITLE', STORE_NAME);

// BEGIN latest news
define('TABLE_HEADING_LATEST_NEWS', 'Berita Terbaru');
define('ALL_PRODUCTS_LINK', 'Cari game saya');

// header text in includes/header.php
define('HEADER_TITLE_CREATE_ACCOUNT', 'Daftar Akun Baru');
define('HEADER_TITLE_MY_ACCOUNT', 'Akun Saya');
define('HEADER_TITLE_CART_CONTENTS', 'Lihat Troli');
define('HEADER_TITLE_CHECKOUT', 'Checkout');
define('HEADER_TITLE_TOP', 'Home');
define('HEADER_TITLE_CATALOG', 'Katalog');
define('HEADER_TITLE_LOGOFF', 'Log Kelur');
define('HEADER_TITLE_LOGIN', 'Akun Saya');
define('HEADER_TITLE_MY_FAVOURITE_LINKS', 'Taut Favorit saya');
define('HEADER_TITLE_MY_PAYMENT_HISTORY', 'Pernyataan Akun');
define('HEADER_TITLE_MY_VIP_ACCOUNT', 'Akun VIP');
define('HEADER_TITLE_MY_VIP_INVENTORY_UPDATE', 'Edit Daftar Inventaris');
define('HEADER_TITLE_MY_VIP_REGISTER_SERVER', 'Daftar Inventaris');
define('HEADER_TITLE_MY_VIP_ORDERS_HISTORY', 'Daftar Order');
define('HEADER_TITLE_MY_VIP_REPORT', 'Laporan Penjualan');

define('HEADER_TITLE_SHOW_ALL', 'Lihat Semua');
define('HEADER_GAME_NEWS_STORE_ANNOUNCEMENT', 'Berita Game / Pengumuman Toko');

define('TEXT_SAVE', 'Simpan');

define('JAVASCRIPT_ENFORCEMENT', 'Silakan mengaktifkan JavaScript di peramban Anda untuk mengalami semua ciri kustom dari situs kami, termasuk kemampuan untuk melakukan pembelian..');

// footer text in includes/footer.php
define('FOOTER_TEXT_REQUESTS_SINCE', 'kunjungan sejak');
define('OGM_SPECIAL_NOTICE', '<div style="font-size:15px;font-weight:bold;">Lihat perubahan baru kami!</div>
                        	<div style="">Kami telah membuat beberapa tweak kecil untuk toko web kami dan memasang toolbar baru! <br />
                        	 Dengan toolbar baru diinstal, Anda dapat: <br />
                        	 - Login atau Daftar akun di OffGamers. (Menyokong Facebook Connect juga!)<br />
                        	 - Menyetel/Memperbarui Setelan Daerah Anda (Negara, Mata Uang & Bahasa)<br />
                        	 - Lihat / Memperbarui barang dalam troli belanja Anda atau Checkout dan melakukan pembayaran<br />
                        	</div>');

// text for day, month, year
define('DAY', 'Hari');
define('DAY2', 'Hari');
define('MONTH', 'Bulan');
define('YEAR', 'Tahun');
define('HOURS', 'Jam');
define('MINUTES', 'Menit');
define('SECONDS', 'Detik');

// text for gender
define('MALE', 'Pria');
define('FEMALE', 'Wanita');
define('MALE_ADDRESS', 'Tuan');
define('FEMALE_ADDRESS', 'Nona');

// e-mail greeting
define('EMAIL_SEPARATOR', '------------------------------------------------------');
define('EMAIL_GREET_MR', 'Tuan yang terhormat %s,' . "\n\n");
define('EMAIL_GREET_MS', 'Nona yang terhormat %s,' . "\n\n");
define('EMAIL_GREET_NONE', '%s,' . "\n\n");

// e-mail buyback
define('EMAIL_BUYBACK_UPDATE_SUBJECT', implode(' ', array(EMAIL_SUBJECT_PREFIX, "Pembaruan Order Buyback #%s")));
define('EMAIL_BUYBACK_UPDATE_BODY', 'Ringkasan Order Buyback:' . "\n" . EMAIL_SEPARATOR . "\n");
define('EMAIL_BUYBACK_UPDATE_ORDER_NUMBER', 'Nomor Order: %s');
define('EMAIL_BUYBACK_UPDATE_DATE_ORDERED', 'Tanggal Order: %s');
define('EMAIL_BUYBACK_UPDATE_FOOTER', "\n\n" . STORE_EMAIL_SIGNATURE);

define('EMAIL_BUYBACK_UPDATE_CUSTOMER_EMAIL', 'Alamat Email pelanggan: %s');
define('EMAIL_BUYBACK_UPDATE_PRODUCTS', 'Produk');
define('EMAIL_BUYBACK_UPDATE_ORDER_TOTAL', 'Jumlah: %s %s');
define('EMAIL_NEW_BUYBACK_STATUS', 'Status: Pending');
define('EMAIL_NEW_BUYBACK_ORDER_NUMBER', 'Nomor Order Buyback: %s');
define('EMAIL_NEW_BUYBACK_DATE_ORDERED', 'Tanggal Order Buyback: %s');
define('EMAIL_NEW_BUYBACK_CUSTOMER_EMAIL', 'Alamat Email: %s');
define('EMAIL_BUYBACK_UPDATE_STATUS', 'Status Order Buyback: %s');

define('EMAIL_BUYBACK_PRESALES_SUBJECT', 'Notifikasi Server Buyback');
define('EMAIL_LOCAL_STORE_EMAIL', '<EMAIL>');
define('EMAIL_BUYBACK_PRESALES_TITLE', 'Terima kasih atas dukungan Anda! Sekarang Anda dapat log masuk ke ' . STORE_NAME . ' dan menjual mata uang game untuk server yang telah Anda pilih. ');
define('EMAIL_BUYBACK_PRESALES_SUMMARY_TITLE', 'Ringkasan Server Buyback:');
define('EMAIL_BUYBACK_PRESALES_PRODUCT', 'Server %s');
define('EMAIL_BUYBACK_PRESALES_MIN_QTY', 'Min: %d');
define('EMAIL_BUYBACK_PRESALES_MAX_QTY', 'Max: %d');
define('EMAIL_BUYBACK_PRESALES_COMMENTS', 'Catatan:' . "\n\n" . STORE_NAME . ' hanya tersedia untuk anggota, dan pembelian mata uang game adalah atas dasar datang pertama melayani, jangan ragu dan kehilangan kesempatan ini untuk menjual mata uang permainan Anda kepada kami. Terima kasih! !');
define('EMAIL_LOCAL_STORE_EMAIL_SIGNATURE', "\n" . STORE_NAME . "\n" . '*************************************************' . "\n" . 'E-mail: <a href="mailto:' . EMAIL_LOCAL_STORE_EMAIL . '">' . EMAIL_LOCAL_STORE_EMAIL . '</a>');

define('EMAIL_TEXT_STATUS_UPDATE_TITLE', '');
define('EMAIL_TEXT_ORDER_NUMBER', 'Nomor Order:');
define('EMAIL_TEXT_DATE_ORDERED', 'Tanggal Order:');
define('EMAIL_TEXT_INVOICE_URL', 'Faktur Terperinci:');
define('EMAIL_TEXT_SUBJECT', 'Pembaruan Order #%d');
define('EMAIL_TEXT_PARTIAL_DELIVERY', '');
define('EMAIL_TEXT_CLOSING', 'Untuk  pertanyaan atau bantuan, Anda dapat menggunakan Online Live Support kami atau email pertanyaan ke ' . EMAIL_TO . '. Harap ingat untuk menyertakan nomor order Anda ketika menghubungi kami untuk mempercepat proses. Terima kasih lagi untuk berbelanja di ' . STORE_NAME . ".\n");

// text for date of birth example
define('DOB_FORMAT_STRING', 'mm/dd/yyyy');

// Mantis # 0000024 @ ************ - Add new boxes
define('BOX_HEADING_ACCOUNT_LEFT_BOX_MY_ACCOUNT', 'AKUN SAYA');
define('BOX_HEADING_ACCOUNT_LEFT_BOX_MY_PROFILE', 'PROFIL SAYA');
define('BOX_HEADING_ACCOUNT_LEFT_BOX_STORE_CREDITS', 'KREDIT TOKO');
define('BOX_HEADING_ACCOUNT_LEFT_BOX_BUYER', 'PEMBELI');
define('BOX_HEADING_ACCOUNT_LEFT_BOX_SELLER', 'PENJUAL');
define('BOX_HEADING_ACCOUNT_LEFT_BOX_VIP_SELLER', 'PENJUAL VIP');
define('BOX_HEADING_ACCOUNT_LEFT_BOX_AFFILIATE', 'AFILIANSI');

define('MY_ACCOUNT_ACCOUNT_OVERVIEW_LINK', 'Intisari');
define('MY_ACCOUNT_EDIT_PROFILE_LINK', 'Edit Profil');
define('MY_ACCOUNT_FACEBOOK_CONNECT', 'Facebook Connect');
define('MY_ACCOUNT_CHANGE_SECRET_QNA_LINK', 'Mengubah Q&A Rahasia');
define('MY_ACCOUNT_NOTIFICATION_SETTINGS_LINK', 'Setelan Notifikasi');
define('MY_ACCOUNT_MANAGE_NEWSLETTERS_LINK', 'Kelola Nawala');
define('MY_ACCOUNT_OFFGAMERS_POINTS', 'OP');
define('MY_ACCOUNT_VERIFY_PHONE_NUMBER_LINK', 'Verifikasi Nomor Ponsel');
define('MY_ACCOUNT_VERIFY_EMAIL_LINK', 'Verifikasi Alamat Email');
define('MY_ACCOUNT_VERIFICATION_SUBMISSION_FORM', 'Formulir Penyerahan Verifikasi');

define('MY_ACCOUNT_STORE_CREDITS_HISTORY_LINK', 'Sejarah Kredit Toko saya');
define('MY_ACCOUNT_STORE_CREDITS_CONVERSION_LINK', 'Konversi Kredit Toko');
define('MY_ACCOUNT_STORE_CREDITS_TOPUP_LINK', 'Top-Up Kredit Toko');

define('MY_ACCOUNT_BUY_ORDERS_HISTORY_LINK', 'Sejarah Order Beli');
define('MY_ACCOUNT_MY_ORDERS_LINK', 'Order Saya');
define('MY_ACCOUNT_SPENDING_LIMIT_LINK', 'Batas Pembelanjaan');

define('MY_ACCOUNT_SELL_ORDER_HISTORY_LINK', 'Sejarah Order Jual');
define('MY_ACCOUNT_WITHDRAW_STATEMENT_LINK', 'Penyataan Penarikan ');
define('MY_ACCOUNT_WITHDRAW_MONEY_LINK', 'Menarik Uang');
define('MY_ACCOUNT_PAYMENT_STATUS_LINK', 'Status Pembayaran');
define('MY_ACCOUNT_FAVORITE_SERVERS_LINK', 'Server Favorit');

// header text in includes/boxes/my_account.php VIP SELLER
define('MY_ACCOUNT_VIP_INVENTORY_UPDATE', 'Edit Daftar Inventaris');
define('MY_ACCOUNT_VIP_REGISTER_SERVER', 'Daftar Inventaris');
define('MY_ACCOUNT_VIP_ORDERS_HISTORY', 'Daftar Order');
define('MY_ACCOUNT_VIP_REPORT', 'Laporan Penjualan');
// M#0000024

define('MY_ACCOUNT_AFFILIATE_OVERVIEW_LINK', 'Intisari Afiliasi');
define('MY_ACCOUNT_INVITE_FRIENDS_LINK', 'Mengajak Teman-teman Anda ');

// Mantis # 0000024 @ ************ - My Account Phase 2
define('TITLE_MY_PROFILE', 'Profil Saya');
define('IMAGE_BUTTON_SAVE_PROFILE', 'Simpan Profil');
define('MY_ACCOUNT_CURRENT_ORDERS', 'Sejarah Order Sekarang');
define('MY_ACCOUNT_COMPLETED_ORDERS', 'Sejarah Order Selesai');
define('MY_ACCOUNT_CANCELLED_ORDERS', 'Sejarah Order Batal');

// My Account Home alert messages
define('TEXT_CLICK_HERE', 'Klik di sini');
define('TEXT_LIVE_SUPPORT', 'Live Support');
define('TEXT_LIVE_CHAT', 'Live Chat');

// login box text in includes/boxes/loginbox.php
define('BOX_HEADING_LOGIN_BOX_MY_ACCOUNT', 'Akun Saya');
define('LOGIN_BOX_MY_ACCOUNT', 'Intisari Akun');
define('LOGIN_BOX_ACCOUNT_EDIT', 'Edit Informasi');
define('LOGIN_BOX_ACCOUNT_PAYMENT_EDIT', 'Metode Penyaluran Dana'); //define('LOGIN_BOX_ACCOUNT_PAYMENT_EDIT','Payment Information');
define('LOGIN_BOX_ACCOUNT_STORE_CREDIT', 'Kredit Toko Saya');
define('LOGIN_BOX_ADDRESS_BOOK', 'Edit Alamat');
define('LOGIN_BOX_VERIFY_EMAIL', 'Mengkonfirmasi Email<span class="requiredInfo">*<span>');
define('LOGIN_BOX_ACTIVATE_ACCOUNT', 'Aktifkan Akun<span class="requiredInfo">*<span>');
define('LOGIN_BOX_ACCOUNT_HISTORY', 'Lihat Sejarah Order');
define('LOGIN_BOX_PRODUCT_NOTIFICATIONS', 'Notifikasi Produk');
define('LOGIN_BOX_BUYBACK_ORDER_HISTORY', 'Lihat Sejarah Buyback');
define('LOGIN_BOX_BUYBACK_FAVOURITE', 'Lihat Buyback Favorit');
define('LOGIN_BOX_ACCOUNT_ACTIVATION', 'Aktifkan Akun');
define('LOGIN_BOX_PASSWORD_FORGOTTEN', 'Lupa Sandi?');
define('LOGIN_BOX_HEADING', 'LOGIN ATAU DAFTAR');
define('LOGIN_BOX_LOGIN_SELECTION', 'Aku Pelanggan Kembali dan saya ingin login sekarang.');
define('LOGIN_BOX_CREATE_ACCOUNT_SELECTION', 'Saya pelanggan baru dan saya ingin membuat akun baru sekarang. ');
define('LOGIN_BOX_SEND_PASSWORD_SELECTION', 'Mohon masuk Alamat Email Anda dan kami akan mengirimkan sandi ke email Anda.');
define('LOGIN_EMAIL', 'Alamat Email');
define('LOGIN_PASSWORD', 'Sandi');
define('LOGIN_PASSWORD_NEW', 'Sandi Baru');
define('LOGIN_PASSWORD_NEW_CONFIRMATION', 'Menkonfirmasi Sandi Baru');
define('LOGIN_RETURN', 'Kembali ke Login');
define('CONFIRM_LOGIN_PASSWORD_DESC', 'Untuk tujuan keamanan, Anda diminta untuk mengisi sandi login OffGamers untuk membeli dengan Kredit Toko OffGamers Anda.');

define('TEXT_WITHDRAWABLE_CREDIT', 'Kredit Penjual');
define('HEADER_TITLE_MY_WITHDRAW_MONEY', 'Menarik Uang');

// categories box text in includes/boxes/categories.php
define('BOX_HEADING_CATEGORIES', 'Kategori');

// manufacturers box text in includes/boxes/manufacturers.php
//define('BOX_HEADING_MANUFACTURERS', 'Manufactures');
define('BOX_HEADING_MANUFACTURERS', 'manufaktur');

// whats_new box text in includes/boxes/whats_new.php
define('BOX_HEADING_WHATS_NEW', 'Apa yang baru?');

// quick_find box text in includes/boxes/quick_find.php
define('BOX_HEADING_SEARCH', 'Pencarian Cepat');
define('BOX_SEARCH_TEXT', 'Gunakan kata kunci untuk menemukan produk yang Anda ingin cari.');
define('TEXT_ALL_CATEGORIES', 'Semua Kategori');
define('BOX_SEARCH_ADVANCED_SEARCH', 'Pencarian Lanjutan');

// specials box text in includes/boxes/specials.php
define('BOX_HEADING_SPECIALS', 'Spesial');

// reviews box text in includes/boxes/reviews.php
define('BOX_HEADING_REVIEWS', 'Ulasan');
define('BOX_REVIEWS_WRITE_REVIEW', 'Menulis ulasan pada produk ini!');
define('BOX_REVIEWS_NO_REVIEWS', ' Tidak ada ulasan produk saat ini');
define('BOX_REVIEWS_TEXT_OF_5_STARS', '%s dari 5 Bintang!');

// shopping_cart box text in includes/boxes/shopping_cart.php
define('BOX_HEADING_SHOPPING_CART', 'Troli Belanja');
define('SHOPPING_CART_BOX_EDIT_CART', 'Edit Troli Belanja');
define('BOX_SHOPPING_CART_EMPTY', 'Tidak ada barang');
define('TEXT_LATEST_PRODUCT_ADDED', 'Ditambahkan ke troli belanja Anda:');

// order_history box text in includes/boxes/order_history.php
define('BOX_HEADING_CUSTOMER_ORDERS', 'Sejarah Order');

// best_sellers box text in includes/boxes/best_sellers.php
define('BOX_HEADING_BESTSELLERS', 'Terlaris');
define('BOX_HEADING_BESTSELLERS_IN', 'Terlaris di<br>&nbsp;&nbsp;');

// notifications box text in includes/boxes/products_notifications.php
define('BOX_HEADING_NOTIFICATIONS', 'Notifikasi');
define('BOX_NOTIFICATIONS_NOTIFY', 'Beritahu saya tentang perbaruan untuk <b>%s</b>');
define('BOX_NOTIFICATIONS_NOTIFY_REMOVE', 'Jangan beritahu saya tentang pembaharuan untuk <b>%s</b>');

// manufacturer box text
define('BOX_HEADING_MANUFACTURER_INFO', 'Info Manufaktur');
define('BOX_MANUFACTURER_INFO_HOMEPAGE', '%s Halaman Muka');
define('BOX_MANUFACTURER_INFO_OTHER_PRODUCTS', 'Produk Lainnya');

// languages box text in includes/boxes/languages.php
define('BOX_HEADING_LANGUAGES', 'Bahasa');

// currencies box text in includes/boxes/currencies.php
define('BOX_HEADING_CURRENCIES', 'Pilih Mata Uang Anda');

define('BOX_HEADING_HELP_DESK', 'Desk Bantuan');

// polling box text in includes/boxes/polling.php
define('BOX_HEADING_POLLING', 'Pemungutan Suara OffGamers');

// information box text in includes/boxes/information.php
define('BOX_HEADING_INFORMATION', 'Informasi');
define('BOX_INFORMATION_CONDITIONS', 'Kondisi Penggunaan');
define('BOX_INFORMATION_SHIPPING', 'Pengiriman & Pengembalian');
define('BOX_INFORMATION_ABOUT_US', 'Tentang Kami');
define('BOX_HEADING_LINKS', 'Taut');

define('BOX_INFORMATION_NEWS', 'BERITA & PROMOSI');
define('BOX_INFORMATION_TOS', 'Persyaratan Layanan');
define('BOX_INFORMATION_PRIVACY', 'Kebijakan Privasi');
define('BOX_INFORMATION_FAQ', 'FAQ');
define('BOX_INFORMATION_CONTACT_WEB', 'Formulir Hubungi Kami');
define('BOX_INFORMATION_CONTACT', 'Hubungi Kami');
define('BOX_INFORMATION_PROMOTION', 'Promosi');
define('BOX_INFORMATION_DISCLAIMER', 'Sangkalan');
define('BOX_LINKS', 'Taut');

// tell a friend box text in includes/boxes/tell_a_friend.php
define('BOX_HEADING_TELL_A_FRIEND', 'Beritahu seorang teman');
define('BOX_TELL_A_FRIEND_TEXT', 'Beritahu seseorang yang Anda kenal tentang produk ini ');

// regional setting
define('BOX_HEADING_REGIONAL_SETTING', 'SETELAN WILAYAH');
define('TEXT_REGIONAL_COUNTRY', 'Negara');
define('TEXT_LANGUAGE', 'Bahasa');
define('TEXT_REGIONAL_NOTE', 'Menyetel setelan wilayah default Anda untuk kunjungan berikutnya.');

//BEGIN allprods modification
define('BOX_INFORMATION_ALLPRODS', 'Lihat semua barang');
//END allprods modification

define('BOX_CHANGE_PASSWORD', 'MENGUBAH SANDI');
define('BOX_IMPORTANT_NOTICE', 'PEMBERITAHUAN PENTING');
define('BOX_ERROR_INVALID_ANSWER', 'Jawaban tidak valid.');
define('BOX_ERROR_INVALID_LAST4DIGIT', '4 digit terakhir nomor telepon yang tidak valid.');
define('BOX_SUCCESS_VALIDATE_LAST4DIGIT', 'Token keamanan Anda telah berhasil dikirim ke alamat email Anda yang terdaftar.');
define('BOX_CHANGE_CONTACT_NOTICE', 'Efektif mulai tanggal 16 Mei 2012, sistem kami tidak akan lagi menerima sambungan telepon rumah atau nomor VOIP untuk verifikasi panggilan. Dalam rangka meningkatkan keamanan akun, setiap pemegang akun OffGamers yang berada di bawah kriteria berikut, akan diperlukan untuk memperbarui nomor ponsel mereka di profil OffGamers mereka : -:-<br>A. Pelanggan dengan akun yang ada dan dengan nomor sambungan telepon terdaftar untuk profil mereka sekarang.<br>B. Akun yang berbagi nomor telepon yang sama dengan beberapa akun / profil yang lain.<br><br>Langkah ini diperlukan untuk mengakomodasi dengan verifikasi ponsel terbaru kami, yang kami akan merilis pada tahap kemudian. Kami mohon maaf sebelumnya atas ketidaknyamanan kami mungkin telah disebabkan.');
define('BOX_UPDATE_YOUR_MOBILE_NUMBER', 'MEMPERBARUI NOMOR PONSEL ANDA');
define('BOX_CHANGE_CONTACT_FOR_SECURITY_REASONS', 'Untuk alasan keamanan, lengkapi verifikasi akun Anda dengan mengisi nomor ponsel Anda. Untuk melengkapi proses ini, Anda bisa mengisi 4 digit terakhir dari nomor kontak Anda sebelumnya atau dengan mengirimkan pertanyaan rahasia dan jawabannya untuk otentikasi.');
define('BOX_CHANGE_CONTACT_FILL_LAST4DIGIT', 'Untuk tujuan otentikasi, silakan mengisi 4 digit terakhir dari nomor telepon saat Anda di bawah ini:');
define('BOX_CHANGE_CONTACT_FILL_LAST4DIGIT_DESC', 'Jika nomor ponsel hilang atau tidak lagi digunakan, silakan untuk melakukan permintaan untuk token keamanan di bawah. Kode akan dikirim ke alamat email yang terdaftar.');
define('BOX_CHANGE_CONTACT_FOOTER_DESC', 'Jika Anda memiliki pertanyaan, jangan ragu untuk <a href="' . tep_href_link(FILENAME_CUSTOMER_SUPPORT, '', 'SSL') . '">menghubungi kami</a> dan operator kami akan membantu Anda.');
define('BOX_CHANGE_CONTACT_OR', 'atau');

// checkout procedure text
define('CHECKOUT_BAR_DELIVERY', 'Informasi<br>Pengiriman');
define('CHECKOUT_BAR_INFO', 'Info Checkout');
define('CHECKOUT_BAR_PAYMENT', 'Informasi<br>Pembayaran');
define('CHECKOUT_BAR_CONFIRMATION', 'Konformasi');
define('CHECKOUT_BAR_FINISHED', 'Selesai');

// pull down default text
define('TEXT_ALL_PAGES', 'Semua');
define('PULL_DOWN_DEFAULT', 'Silakan Pilih');
define('TYPE_BELOW', 'Ketik Di Bawah');
define('TEXT_REFRESH_WHEN_CHANGE', '(Halaman ini akan refresh ketika berubah)');
define('PULL_DOWN_DEFAULT_GAME_CARD', 'Pilih nilai kartu game ');

// javascript messages
define('JS_ERROR', 'Error telah terjadi selama proses formulir Anda.\n\nSilakan membuat koreksi berikut:\n\n');

define('JS_REVIEW_TEXT', '* \'Teks Ulasan\' harus memiliki setidaknya ' . '10' . ' karakter.\n');
define('JS_REVIEW_RATING', '* Anda harus menilai produk untuk ulasan Anda.\n');
define('JS_REVIEW_CUSNAME', '* Anda harus memasukkan \'Nama Depan\' Anda.\n');
define('JS_ERROR_NO_PAYMENT_MODULE_SELECTED', '* Silakan pilih cara pembayaran untuk order Anda.\n');
define('JS_ERROR_NO_SHIPPING_MODULE_SELECTED', '* Silakan pilih metode pengiriman untuk order Anda.\n');

define('JS_ERROR_SUBMITTED', 'Formulir ini telah disampaikan. Silakan tekan Ok dan tunggu proses ini selesai.');

define('JS_ERROR_PRODUCT_EMPTY', 'Silakan pilih nilai kartu game');

define('ERROR_NO_PAYMENT_MODULE_SELECTED', 'Silakan pilih cara pembayaran untuk order Anda.');
define('ERROR_NO_PAYMENT_MODULE_NEEDED', 'Anda tidak perlu memilih cara pembayaran karena jumlah dapat didebet dari kredit toko Anda sepenuhnya.');
define('ERROR_CART_COMMENTS_REQUIRED', 'Silakan mengisi komentar yang diperlukan tentang order Anda!');
define('ERROR_SIGNUP_CART_COMMENTS_REQUIRED', 'Silakan mengisi komentar yang dibutuhkan dalam formulir pendaftaran!');

define('ERROR_PLS_ENTER_ACCOUNT_NAME', 'Silakan masukkan name akun');
define('ERROR_PLS_ENTER_CHAR_NAME', 'Silakan masukkan name karakter');
define('ERROR_PLS_ENTER_PASSWORD', 'Silakan masukkan sandi');
define('ERROR_EMAIL_VERIFICATION_CODE', 'Kode verifikasi harus berisi 12 karakter.');
define('ERROR_WRONG_PASSWORD', 'Sandi yang tidak valid, silakan coba lagi');

define('CATEGORY_COMPANY', 'Rincian Perusahaan');
define('CATEGORY_VERIFY_EMAIL', 'Verifikasi Email');
define('CATEGORY_PERSONAL', 'Data Pribadi Anda');
define('CATEGORY_ADDRESS', 'Alamat Anda');
define('CATEGORY_CONTACT', 'Informasi Kontak Anda');
define('CATEGORY_OPTIONS', 'Opsi');
define('CATEGORY_PASSWORD', 'Sandi Anda');

define('ENTRY_ACCOUNT_NAME', 'Nama Akun');
define('ENTRY_WOW_ACCOUNT_NAME', 'Nama Akun WOW');
define('ENTRY_ADD_CHARACTER_NAME', 'Isi nama karakter tepatnya di');
define('ENTRY_CHARACTER_INGAME_TIME', '<td class="smallText"><div style="padding:5px 10px 5px 20px;float:left">Waktu dalam game:</div></td><td class="smallText">%s <span class="redIndicator">*</span>%s jam</td>');
define('ENTRY_CHARACTER_INGAME_DURATION', '<td class="smallText"><div style="padding:5px 10px 5px 20px;float:left">Durasi dalam game:</div></td><td class="smallText">%s jam %s</td>');
define('TEXT_CHARACTER_INGAME_NOTE', 'Ini adalah sama ketika Anda akan masuk ke dalam game setelah checkout, dalam jam.');
define('TEXT_CHARACTER_INGAME_HOUR_NOTE', 'Ini adalah durasi estimasi akan dicatat ke dalam permainan dalam jam.');
define('ENTRY_ORDER_GUYA_ACCOUNT', '<td class="smallText" style="padding:5px 10px 5px 20px;" align="left" nowrap>Nama Akun:</td><td class="smallText">%s <span class="redIndicator">*</span></td>');
define('ENTRY_ORDER_GUYA_PWD', '<td class="smallText" style="padding:5px 10px 5px 20px;" align="left" nowrap>Sandi:</td><td class="smallText">%s <span class="redIndicator">*</span></td>');
define('ENTRY_QNA', '<td class="smallText" style="padding:5px 10px 5px 20px;" align="left" nowrap>Jawaban:</td><td class="smallText">%s <span class="redIndicator">*</span></td>');
define('ENTRY_ORDER_GUYA_WOW_ACCOUNT_INFO', '<td class="smallText" style="padding:5px 10px 5px 20px;" align="left">&nbsp;</td><td class="smallText">Untuk pembelian emas World of Warcraft, silakan isi akun Battle.net Anda.</td>');
define('ENTRY_ORDER_GUYA_WOW_ACCOUNT_INFO_TEXT', 'Untuk pembelian emas World of Warcraft, silakan isi akun Battle.net Anda.');
define('ENTRY_ORDER_GUYA_WOW_ACCOUNT', '<td class="smallText" style="padding:5px 10px 5px 20px;" align="left" nowrap>Nama Akun WoW:</td><td class="smallText">%s Opsi</td>');
define('ENTRY_ORDER_GUYA_WOW_ACCOUNT_INFO2', '<td class="smallText" style="padding:5px 10px 5px 20px;" align="left">&nbsp;</td><td class="smallText">Berlaku hanya untuk World of Warcraft.</td>');
define('ENTRY_ORDER_GUYA_WOW_ACCOUNT_INFO2_TEXT', 'Berlaku hanya untuk World of Warcraft.');
define('TEXT_ORDER_FACE_TO_FACE_NOTE', 'Anda akan dapat memilih durasi dalam game Anda setelah Anda telah mengubah metode pengiriman Anda untuk motode Tatap Muka ATAU Metode Stor Terbuka. <b>(Catatan: Kami hanya akan mula mencari untuk pesanan Anda setelah Anda klik pada "Saya online sekarang")</b>');
define('TEXT_ORDER_GUYA_NOTE', 'Kami akan masuk ke akun Anda dan mentransfer emas ke karakter Anda tentukan.');
define('TEXT_ORDER_MAIL_NOTE', 'Kami akan mengirimkan kepada nama karakter ditetapkan Anda melalui surat dalam game.');
define('TEXT_ORDER_OPEN_STORE_NOTE', 'Anda akan dapat memilih durasi dalam game Anda setelah Anda telah mengubah metode pengiriman Anda untuk motode Tatap Muka ATAU Metode Stor Terbuka. <b>(Note: Kami hanya akan mula mencari untuk pesanan Anda setelah Anda klik pada "Saya online sekarang")</b>');
define('TEXT_ORDER_CHANGE_DEFAULT_CHARACTER', 'Mengubah karakter standar anda');
define('TEXT_PERSONAL_INFO_REQUIRED', '%s diperlukan untuk melakukan pembayaran bagi kita untuk memverifikasi dan memproses order Anda. (Anda tidak akan diminta untuk melakukan hal ini untuk order masa depan Anda)');
define('ENTRY_VERIFY_EMAIL', 'Nama Perusahaan');
define('ENTRY_VERIFY_EMAIL_TEXT', '*');
define('ENTRY_VERIFY_EMAIL_ERROR', 'Konfirmasi email harus cocok Email Anda.');
define('ENTRY_COMPANY', 'Nama Perusahaan:');
define('ENTRY_COMPANY_ERROR', '');
define('ENTRY_COMPANY_TEXT', '');
define('ENTRY_GENDER', 'Gender:');
define('ENTRY_GENDER_ERROR', 'Silakan pilih gender Anda.');
define('ENTRY_GENDER_TEXT', '*');
define('ENTRY_CUSTOMER_IDENTIFY_NUMBER', 'Nomor IC:');
define('ENTRY_FIRST_NAME', 'Name Depan:');
define('ENTRY_FIRST_NAME_ERROR', 'Nama depan Anda harus berisi minimal ' . ENTRY_FIRST_NAME_MIN_LENGTH . ' karakter.');
define('ENTRY_FIRST_NAME_TEXT', '*');
define('ENTRY_LAST_NAME', 'Nama Belakang:');
define('ENTRY_LAST_NAME_ERROR', 'Nama Belakang Anda harus berisi minimal ' . ENTRY_LAST_NAME_MIN_LENGTH . ' karakter.');
define('ENTRY_LAST_NAME_TEXT', '*');
define('ENTRY_DATE_OF_BIRTH', 'Tanggal Lahir:');
define('ENTRY_DATE_OF_BIRTH_OVER_ERROR', 'Tahun Lahir tidak valid, masukkan kembali lagi');
define('ENTRY_DATE_OF_BIRTH_FUTURE_ERROR', 'Tahun Lahir tidak valid, Anda telah memasukkan tanggal yang akan datang');
define('ENTRY_DATE_OF_BIRTH_ERROR', 'Tanggal Lahir Anda harus berisi tanggal yang valid.');
define('ENTRY_DATE_OF_BIRTH_ERROR_1', 'Anda harus memilih');
define('ENTRY_DATE_OF_BIRTH_ERROR_2', ' dari');
define('ENTRY_DATE_OF_BIRTH_ERROR_3', ' menu tarik bawah. ');
define('ENTRY_DATE_OF_BIRTH_ERROR_4', 'Tahun Anda harus terdiri dari 4 digit.');
define('ENTRY_DATE_OF_BIRTH_TEXT', '* (misalnya 05/21/1970)');
define('ENTRY_EMAIL_ADDRESS', 'Alamat Email:');
define('ENTRY_EMAIL_ADDRESS_ERROR', 'Alamat Email Anda harus berisi minimal ' . ENTRY_EMAIL_ADDRESS_MIN_LENGTH . ' karakter.');
define('ENTRY_EMAIL_ADDRESS_CHECK_ERROR', 'Alamat Email Anda tidak valid - silakan membuat koreksi yang diperlukan.');
define('ENTRY_EMAIL_ADDRESS_ERROR_EXISTS', 'Kami melihat bahwa Email Anda berwujud di catatan kami, silakan <a href="' . tep_href_link(FILENAME_LOGIN) . '"> Login </a> dengan alamat email atau <a href="' . tep_href_link(FILENAME_PASSWORD_FORGOTTEN) . '">Klik di sini</a> jika Anda lupa sandi Anda.');
define('ENTRY_EMAIL_ADDRESS_TEXT', '*');
define('ENTRY_STREET_ADDRESS', 'Alamat Jalan:');
define('ENTRY_STREET_ADDRESS_ERROR', 'Alamat Jalan Anda harus berisi minimal ' . ENTRY_STREET_ADDRESS_MIN_LENGTH . ' karakter.');
define('ENTRY_STREET_ADDRESS_TEXT', '*');
define('ENTRY_SUBURB', 'Bagian Pinggir Kota:');
define('ENTRY_SUBURB_ERROR', '');
define('ENTRY_SUBURB_TEXT', '');
define('ENTRY_POST_CODE', 'Zip/Kode Pos :');
define('ENTRY_POST_CODE_ERROR', 'Kode Pos Anda harus berisi minimal ' . ENTRY_POSTCODE_MIN_LENGTH . ' karakter.');
define('ENTRY_POST_CODE_TEXT', '*');
define('ENTRY_CITY', 'Kota:');
define('ENTRY_CITY_ERROR', 'Kota Anda harus berisi minimal ' . ENTRY_CITY_MIN_LENGTH . ' karakter.');
define('ENTRY_CITY_TEXT', '*');
define('ENTRY_STATE', 'Negara Bagian/Provinsi:');
define('ENTRY_STATE_ERROR', 'Negara Bagian Anda harus berisi minimal ' . ENTRY_STATE_MIN_LENGTH . ' karakter.');
define('ENTRY_STATE_ERROR_SELECT', 'Silakan pilih negara bagian dari menu tarik bawah.');
define('ENTRY_STATE_TEXT', '*');
define('ENTRY_COUNTRY', 'Negara:');
define('ENTRY_LOCATION', 'Tempat:');
define('ENTRY_COUNTRY_ERROR', 'Anda harus memilih negara dari menu tarik bawah.');
define('ENTRY_LOCATION_ERROR', 'Anda harus memilih tempat dari menu tarik bawah.');
define('ENTRY_COUNTRY_TEXT', '*');
define('ENTRY_TELEPHONE_NUMBER', 'Nomor Ponsel:');
define('ENTRY_TELEPHONE_NUMBER_ERROR', 'Nomor Ponsel Anda harus berisi minimal ' . ENTRY_TELEPHONE_MIN_LENGTH . ' karakter.');
define('ENTRY_TELEPHONE_NUMBER_TEXT', '*');
define('ENTRY_FAX_NUMBER', 'Nomor Fax:');
define('ENTRY_FAX_NUMBER_ERROR', '');
define('ENTRY_FAX_NUMBER_TEXT', '');
define('ENTRY_NEWSLETTER', 'Nawala:');
define('ENTRY_NEWSLETTER_TEXT', 'Langgan Nawala kami untuk penawaran khusus, eksklusif dan promosi.');
define('ENTRY_NEWSLETTER_YES', 'Berlangganan');
define('ENTRY_NEWSLETTER_NO', 'Berhenti Berlangganan');
define('ENTRY_NEWSLETTER_ERROR', '');
define('ENTRY_PASSWORD', 'Sandi:');
define('ENTRY_REMEMBER_ME', 'Ingat Saya');
define('ENTRY_PASSWORD_ERROR', 'Sandi Anda harus berisi minimal ' . ENTRY_PASSWORD_MIN_LENGTH . ' karakter.');
define('ENTRY_PASSWORD_ERROR_NOT_MATCHING', 'Sandi Konfirm harus cocok dengan sandi Anda.');
define('ENTRY_PASSWORD_TEXT', '*');
define('ENTRY_PASSWORD_CONFIRMATION', 'Konfirm Sandi:');
define('ENTRY_PASSWORD_CONFIRMATION_TEXT', '*');
define('ENTRY_PASSWORD_CURRENT', 'Sandi Sekarang:');
define('ENTRY_PASSWORD_CURRENT_TEXT', '*');
define('ENTRY_PASSWORD_CURRENT_ERROR', 'Sandi Sekarang Anda harus berisi minimal ' . ENTRY_PASSWORD_MIN_LENGTH . ' karakter.');
define('ENTRY_PASSWORD_NEW', 'Sandi Baru:');
define('ENTRY_PASSWORD_NEW_TEXT', '*');
define('ENTRY_PASSWORD_NEW_ERROR', 'Sandi baru Anda harus berisi minimal ' . ENTRY_PASSWORD_MIN_LENGTH . ' karakter.');
define('ENTRY_PASSWORD_NEW_ERROR_NOT_MATCHING', 'Konfirmasi Sandi harus cocok dengan Sandi Baru Anda.');
define('PASSWORD_HIDDEN', '--TERSEMBUNYI--');
define('ENTRY_SERIAL_ERROR', 'Silakan masukkan kode serial');
define('ENTRY_SERIAL_CHECK_ERROR', 'Kode Serial Anda harus berisi 12 karakter.');
define('ENTRY_MISMATCH_ANSWER_ERROR', 'Jawaban Anda tidak cocok dengan jawaban terdaftar.');
define('ENTRY_MISMATCH_ANSWER_RESEND_LINK', 'Klik <a href="%s">disini</a>.');
define('ENTRY_CHARACTER_NAME', 'Nama Karakter');
define('ENTRY_REBATE', 'Jumlah Rebate');
define('ENTRY_ADDED_BONUS', 'Bonus Ekstra');
define('ENTRY_DELIVERY_INFORMATION', 'Silakan masukkan informasi akun game Anda');

define('FORM_REQUIRED_MSG', 'Informasi pribadi yang diperoleh digunakan semata-mata untuk tujuan meningkatkan fungsi dan tingkat pelayanan.');
define('FORM_REQUIRED_INFORMATION', '* Informasi Diperlukan');
define('SECTION_TITLE_FORM_PM_REQUIRED_INFO', 'Informasi Diperlukan:');

define('TEXT_NAME', 'Nama');
define('TEXT_BILLING_ADDRESS', 'Alamat');
define('TEXT_STATE_OR_ZIP', 'State/Zip');
define('TEXT_FIRST_NAME', 'Nama Depan');
define('TEXT_LAST_NAME', 'Nama Belakang');
define('TEXT_CONTACT_NUMBER', 'Nomor Kontak');
define('TEXT_BILLING_ADDRESS1', 'Alamat');
define('TEXT_COUNTRY_CODE', 'Kode Negara');
define('TEXT_POST_CODE', 'Zip/Kode Pos');
define('TEXT_CITY', 'Kota');
define('TEXT_COUNTRY', 'Negara');
define('TEXT_STATE', 'Negara Bahgian/Provinsi');
define('TEXT_SECRET_QUESTION', 'Pertanyaan Rahasia');
define('TEXT_ANSWER', 'Jawaban');
define('TEXT_SELLING_DELIVERY_TIME', 'Waktu Pengiriman');
define('TEXT_OP', 'OP');
define('TEXT_REBATE', 'Rebate');
define('TEXT_TOTAL_PAY', 'Jumlah Dibayar');

define('JS_ERROR_EMPTY_SEARCH_INPUT', 'Pencarian Anda tidak boleh kosong.');
// constants for use in tep_prev_next_display function
define('TEXT_RESULT_PAGE', 'Hasil Pencarian:');
define('TEXT_DISPLAY_NUMBER_OF_PRODUCTS', 'Menampilkan <b>%d</b> ke <b>%d</b> (dari <b>%d</b> hasil)');
define('TEXT_DISPLAY_NUMBER_OF_ORDERS', 'Menampilkan <b>%d</b> ke <b>%d</b> (dari <b>%d</b> order)');
define('TEXT_DISPLAY_NUMBER_OF_REVIEWS', 'Menampilkan <b>%d</b> ke <b>%d</b> (dari <b>%d</b> ulasan)');
define('TEXT_DISPLAY_NUMBER_OF_PRODUCTS_NEW', 'Menampilkan <b>%d</b> ke <b>%d</b> (dari <b>%d</b> produk baru)');
define('TEXT_DISPLAY_NUMBER_OF_SPECIALS', 'Menampilkan <b>%d</b> ke <b>%d</b> (dari <b>%d</b> spesial)');

define('PREVNEXT_TITLE_FIRST_PAGE', 'Halaman Pertama');
define('PREVNEXT_TITLE_PREVIOUS_PAGE', 'Halaman Sebelumnya');
define('PREVNEXT_TITLE_NEXT_PAGE', 'Halaman Berikutnya');
define('PREVNEXT_TITLE_LAST_PAGE', 'Halaman Terakhir ');
define('PREVNEXT_TITLE_PAGE_NO', 'Halaman %d');
define('PREVNEXT_TITLE_PREV_SET_OF_NO_PAGE', 'Set sebelumnya dari %d Halaman');
define('PREVNEXT_TITLE_NEXT_SET_OF_NO_PAGE', 'Set berikutnya dari %d Halaman');
define('PREVNEXT_BUTTON_FIRST', '&lt;PERTAMA');
define('PREVNEXT_BUTTON_PREV', 'Sebelum');
define('PREVNEXT_BUTTON_NEXT', 'Berikut');
define('PREVNEXT_BUTTON_LAST', 'TERAKHIR&gt;');

define('BUTTON_FIRST', 'PERTAMA');
define('BUTTON_LAST', 'TERAKHIR');

define('BUTTON_YES', 'Ya');
define('BUTTON_OK', 'OK');
define('BUTTON_SUBMIT', 'Menyerah');
define('BUTTON_CONFIRM', 'Pasti');
define('BUTTON_CANCEL', 'Batal');
define('BUTTON_ADD', 'Tambah');
define('BUTTON_DELETE', 'Hapus');
define('BUTTON_BATCH_UPDATE', 'Pembaruan Sekumpulan');
define('BUTTON_EXPORT', 'Ekspor');
define('BUTTON_EXPORT_TEMPLATE', 'Ekspor templat');
define('BUTTON_IMPORT', 'Impor');
define('BUTTON_REFRESH', 'Muat Ulang');
define('BUTTON_RESET', 'Reset');
define('BUTTON_REPORT', 'Lapor');
define('BUTTON_NO', 'Tidak');

define('BUTTON_ADD_PM', 'Tambah Akun Pembayaran');
define('BUTTON_AGREE', 'Setuju');
define('BUTTON_BACK', 'Kembali');
define('BUTTON_DELETE', 'Hapus');
define('BUTTON_DISAGREE', 'Bantah');
define('BUTTON_EDIT', 'Edit');
define('BUTTON_REMOVE', 'Hapus');
define('BUTTON_PRESALE_NOTICE', 'Email Pra-Jualan');
define('BUTTON_PRESALE_REMOVE', 'Hapuskan Pra-Jualan');
define('BUTTON_SAVE_CHANGES', 'Simpan Perubahan');
define('BUTTON_SEARCH', 'Cari');
define('BUTTON_SKIP_NOW', 'Loncat sekarang');
define('BUTTON_SHOW_ALL_PRODUCTS', 'Tampilkan semua server');
define('BUTTON_SHOW_SELLING_PRODUCTS', 'Tampilkan server jualan');
define('BUTTON_SIGN_IN', 'Masuk');
define('BUTTON_SIGN_UP', 'Daftar');
define('BUTTON_SIGN_OUT', 'Keluar');
define('BUTTON_RESEND_SECURITY_TOKEN', 'Kirim ulang token keamanan');

define('BUTTON_MIN_CHAR_LENGTH', 11);
define('IMAGE_BUTTON_ADD_ADDRESS', 'Tambah Alamat');
define('IMAGE_BUTTON_ADDRESS_BOOK', 'Buku Alamat');
define('IMAGE_BUTTON_BACK', 'Kembali');
define('IMAGE_BUTTON_BUY_NOW', 'Beli');
define('IMAGE_BUTTON_CHECKOUT', 'Lanjut ke Checkout');
define('IMAGE_BUTTON_CHANGE_ADDRESS', 'Ubah Alamat');
define('IMAGE_BUTTON_CONFIRM_ORDER', 'Konfirm order');
define('IMAGE_BUTTON_SECURE_CHECKOUT', 'Checkout terlindung');
define('IMAGE_BUTTON_CONFIRM', 'Konfirm Order');
define('IMAGE_BUTTON_CONFIRM_CODE', 'Konfirm Kode');
define('IMAGE_BUTTON_CONTINUE', 'Sambung');
define('IMAGE_BUTTON_CONTINUE_SHOPPING', 'Lanjutkan Belanja');
define('IMAGE_BUTTON_CONVERT_NOW', 'Mengkonversi Sekarang');
define('IMAGE_BUTTON_DELETE', 'Hapus');
define('IMAGE_BUTTON_DOWNLOAD', 'Unduh');
define('IMAGE_BUTTON_EDIT_ACCOUNT', 'Edit Akun');
define('IMAGE_BUTTON_HISTORY', 'Sejarah Order');
define('IMAGE_BUTTON_IN_CART', 'Tambah ke troli');
define('IMAGE_BUTTON_DIRECT_TOP_UP', 'Top-Up');
define('IMAGE_BUTTON_LOGIN', 'Login');
define('IMAGE_BUTTON_SEND_PASSWORD', 'Kirim Sandi');
define('IMAGE_BUTTON_NOTIFICATIONS', 'Notifikasi');
define('IMAGE_BUTTON_OUT_OF_STOCK', 'Habis Terjual');
define('IMAGE_BUTTON_PRE_ORDER', 'Pra-order');
define('IMAGE_BUTTON_QUICK_FIND', 'Cari');
define('IMAGE_BUTTON_REMOVE_NOTIFICATIONS', 'Hapuskan Notifikasi');
define('IMAGE_BUTTON_REVIEWS', 'Ulasan');
define('IMAGE_BUTTON_SEARCH', 'Cari');
define('IMAGE_BUTTON_SHIPPING_OPTIONS', 'Opsi Pengiriman');
define('IMAGE_BUTTON_TELL_A_FRIEND', 'Beritahu Teman');
define('IMAGE_BUTTON_UPDATE', 'Memperbarui');
define('IMAGE_BUTTON_CONFIRM_TEL', 'Konfirm');
define('IMAGE_BUTTON_VERIFY', 'Periksa');
define('IMAGE_BUTTON_UPDATE_CART', 'Pembaruan Troli');
define('IMAGE_BUTTON_WRITE_REVIEW', 'Menulis Ulasan');
define('IMAGE_BUTTON_REDEEM_VOUCHER', 'Menebus');
define('IMAGE_BUTTON_SELL_MORE', 'Menjual Lebih');
define('IMAGE_BUTTON_TRANSFER_NOW', 'Transfer Sekarang');
define('IMAGE_BUTTON_ADD', 'Tambah');
define('IMAGE_BUTTON_NEXT', 'berikut');
define('IMAGE_BUTTON_REFRESH', 'Muat Ulang');
define('IMAGE_BUTTON_YES', 'Ya');
define('IMAGE_BUTTON_YES2', 'Ya');
define('IMAGE_BUTTON_TOP_UP_STORE_CREDITS', 'Top-up Kredit Toko');
define('IMAGE_BUTTON_BUY_CODE', 'Beli Kode');
define('IMAGE_BUTTON_PAY_WITH_SC_CURRENCY', 'Bayar dengan %s (Kredit Toko)');
define('IMAGE_BUTTON_PAY_WITH_WEBSITE_CURRENCY', 'Bayar dengan %s');
define('IMAGE_BUTTON_PAY_WITHOUT_SC', 'Bayar tanpa Kredit Toko');
define('TEXT_INFO_PAYMENT_CONFIRM_CAPTION', 'Checkout akan melanjutkan ke %s');

define('ALT_BUTTON_ADD_PM', 'Tambah Cara');  //define('ALT_BUTTON_ADD_PM', 'Tambah Payment Akun');
define('ALT_BUTTON_BACK', 'Kembali ke halaman sebelumnya');
define('ALT_BUTTON_CONFIRM', 'Konfirm');
define('ALT_BUTTON_CONTINUE', 'Lanjut');
define('ALT_BUTTON_RESET', 'Reset');
define('ALT_BUTTON_SEARCH', 'Cari');
define('ALT_BUTTON_SHOW_ALL_PRODUCTS', 'Klik disini untuk menampilkan semua server.');
define('ALT_BUTTON_SHOW_SELLING_PRODUCTS', 'Klik disini untuk menampilkan hanya server jual Anda.');
define('ALT_BUTTON_SUBMIT', 'Menyerah Formulir');
define('ALT_BUTTON_SIGN_IN', 'Masuk');
define('ALT_BUTTON_SIGN_UP', 'Daftar');
define('ALT_BUTTON_UPDATE', 'Memperbarui');

define('TABLE_HEADING_SERVER', 'Server');
define('TABLE_HEADING_ACTION', 'Tindakan');
define('TABLE_HEADING_PER_UNIT', 'Per Unit');
define('TABLE_HEADING_STATUS', 'Status');
define('TABLE_HEADING_QTY', 'Kuantitas.');
define('TABLE_HEADING_DELIVERY_TIME', 'Waktu Pengiriman');
define('TABLE_HEADING_DELIVERY_METHOD_SUPPORT', 'Metode Pengiriman disokong');

define('TITLE_TRANS_PAYMENT', 'Pembayaran %s');

define('SMALL_IMAGE_BUTTON_DELETE', 'Hapus');
define('SMALL_IMAGE_BUTTON_EDIT', 'Edit');
define('SMALL_IMAGE_BUTTON_VIEW', 'Lihat');

define('ICON_ARROW_RIGHT', 'Lebih');
define('ICON_CART', 'Dalam Troli');
define('ICON_ERROR', 'Error');
define('ICON_NOTICE', 'Maklumat');
define('ICON_SUCCESS', 'Sukses');
define('ICON_WARNING', 'Peringatan');
define('ICON_PROMO', 'Promosi');

// CATALOG_PRODUCTS_WITH_IMAGES_mod
define('BOX_CATALOG_PRODUCTS_WITH_IMAGES', 'Katalog Cetak');
define('IMAGE_BUTTON_UPSORT', 'Urutan Menaik');
define('IMAGE_BUTTON_DOWNSORT', 'Urutan Menurun');

// CD Key
define('TEXT_CDKEY_SUSPENDED_FOR_VIEWING', 'CD Key /gambar kartu masa diblokir sementara karena sengketa pembayaran. Kode serial telah diteruskan ke pembuat game untuk tindakan yang sesuai. Silakan hubungi "" jika ini adalah kesilapan.');

// Down For Maintenance
define('TEXT_BEFORE_DOWN_FOR_MAINTENANCE', 'MAKLUMAT: : Situs ini akan luring untuk pemeliharaan pada %s untuk jangka waktu %s.');
define('TEXT_ADMIN_DOWN_FOR_MAINTENANCE', 'MAKLUMAT: MAKLUMAT: Situs ini sedang ditutup saat ini untuk pemeliharaan kepada umum');

define('TEXT_GREETING_PERSONAL', 'Selamat Kembali, <span class="greetUser">%s</span>! Adakah Anda ingin melihat mana <a href="%s">produk baru</a> yang tersedia untuk membeli?');
define('TEXT_GREETING_PERSONAL_RELOGON', '<small>Jika Anda tidak %s, silakan<a href="%s">login</a> dengan informasi akun Anda.</small>');
define('TEXT_GREETING_GUEST', 'Welcome <span class="greetUser">Pengunjung</span>! Adakah Anda ingin <a href="%s">login<</a>? Atau Anda ingin memilih untuk <a href="%s">membuat akun</a>?');
define('TEXT_YOU_ARE_HERE', '<b>Anda di sini:</b>');
define('TEXT_HOME', 'Halaman Utama');
define('TEXT_GAME', 'Game');
define('TEXT_SERVER', 'Server');
define('TEXT_NORMAL', 'Biasa');
define('TEXT_GENERAL', 'Reguler');
define('TEXT_FULL', 'Penuh');
define('TEXT_QUANTITY', 'Kuantitas');
define('TEXT_VIEW', 'Lihat');
define('TEXT_REFRESH', 'Muat Ulang');
define('TEXT_REDEEM', 'Menebus');

define('TEXT_LIST_CLOSE', 'Tutup');
define('TEXT_ENTER', 'Masuk');
define('TEXT_IS_LOADING', 'Loading... Harap Tunggu.');
define('TEXT_SEARCH', 'Cari');

//Common fields
define('TEXT_SERVER', 'Server');
define('TEXT_GAME', 'Game');
define('TEXT_QUANTITY', 'Qty'); //units
define('TEXT_AMOUNT', 'Jumlah'); //$$$
define('TEXT_AMOUNT_WITH_CURRENCY', 'Jumlah Uang'); //$$$
define('TEXT_STATUS', 'Status');
define('TEXT_ACTION', 'Tindakan');
define('TEXT_ORDER_NO', 'Order #');
define('TEXT_ORDER_STATUS', 'Status Order ');
define('TEXT_PAYMENT_STATUS', 'Status Pembayaran');
define('TEXT_START_DATE', 'Tanggal Mula');
define('TEXT_END_DATE', 'Tanggal Akhir');
define('TEXT_LOADING_MESSAGE', 'Loading ...');
define('TEXT_CONFIRM_DELETE', 'Konfirm untuk menghapus?');
define('TEXT_CHARACTER_NAME', 'Nama Karakter: %s');
define('TEXT_CHAR_NAME', 'Nama Karakter');
define('TEXT_CHARACTER_INGAME_TIME', 'Karakter dalam game: %s<br>setelah checkout');
define('TEXT_CHARACTER_INGAME_DURATION', 'Durasi dalam game : %d Jam');
define('TEXT_CHARACTER_ACCOUNT_NAME', 'Nama Akun: %s');
define('TEXT_CHARACTER_ACCOUNT_PASSWORD', 'Sandi: %s');
define('TEXT_CHARACTER_ACCOUNT_WOW', 'Nama Akun WoW: %s');
define('GAME_CURRENCY_TEMPLATE', 'Toko Mata Uang Game');
define('CD_KEY_TEMPLATE', 'Toko Kartu Game');
define('PWL_TEMPLATE', 'Toko Power Leveling');
define('HIGH_LEVEL_ACCOUNT_TEMPLATE', 'Toko Akun Level Tinggi');
define('TEXT_NO_RESULTS', 'Tidak ada hasil yang ditemukan');

define('URL_GAME_CURRENCY_TEMPLATE', 'toko_mata_uang_game');
define('URL_CD_KEY_TEMPLATE', 'toko_kartu_game');
define('URL_PWL_TEMPLATE', 'toko_power_leveling');
define('URL_HIGH_LEVEL_ACCOUNT_TEMPLATE', 'toko_akun_level_tinggi');

define('GAME_CURRENCY_TEXT', 'Uang game yang digunakan untuk membeli suatu barang atau set bakat ...');
define('CD_KEY_TEXT', 'CD Key & game top up secara online...');
define('PWL_TEXT', 'set bakat dalam game pada level yang berbeda digunakan pada server PvP ... ...');

define('LINK_GAME_CURRENCY', 'Klik di sini untuk dalam paket mata uang game.');
define('LINK_CD_KEY', 'Klik di sini untuk CD Key, Kartu Pra-Bayar, Poin game, dll.');
define('LINK_PWL', 'Klik di sini untuk barang dalam game dan power leveling.');
define('SHARE_THIS_TITLE', 'Berbagi ini');
define('LINK_SHARE_THIS_PAGE', 'Berbagi halaman ini');
define('LINK_SHARE_THIS_PRODUCT', 'Berbagi produk ini');
define('LINK_REDIRECT_MSG', 'Klik di sini adalah peramban Anda tidak mengarahkan Anda ke halaman berikutnya secara otomatis.');
define('LINK_MORE_PRODUCT_INFO', 'Lebih Info Produk');
define('LINK_HIDE_PRODUCT_INFO', 'Sembunyi Info Produk');
define('LINK_ALL_LANGUAGE', 'Semua Bahasa');
define('LINK_ALL_PRODUCT_TYPE', 'Semua Jenis Produk');
define('LINK_ALL_PLATFORM', 'Semua Platform');
define('LINK_ALL_GENRE', 'Semua Genre');

define('TEXT_SORT_PRODUCTS', 'Urutkan Produk');
define('TEXT_DESCENDINGLY', 'Menurun');
define('TEXT_ASCENDINGLY', 'Menaik');
define('TEXT_BY', ' dengan ');
define('TEXT_NOT_AVAILABLE', 'N/A');

define('TEXT_REVIEW_BY', 'oleh %s');
define('TEXT_REVIEW_WORD_COUNT', '%s kata');
define('TEXT_REVIEW_RATING', 'Penilaian: %s [%s]');
define('TEXT_REVIEW_DATE_ADDED', 'Tanggal ditambahkan: %s');
define('TEXT_NO_REVIEWS', 'Tidak ada ulasan produk.');

define('TEXT_CLICK_TO_VERIFY', 'Klik di sini untuk memverifikasi');
define('TEXT_EMAIL_NOT_VERIFY', 'Tidak Diverifikasi ');
define('TEXT_EMAIL_VERIFIED', '(Diverifikasi) <span class="requiredInfo">*</span>');

define('TEXT_UNKNOWN_TAX_RATE', 'Tingkat pajak yang tidak dikenal');
define('TEXT_PRODUCT_NOT_FOUND', 'Produk ini tidak lagi tersedia.');
define('TEXT_PRODUCT_NOT_FOUND_OPTIONS', '<div class="mediumFont"><b>Silakan coba salah satu dari pilihan berikut:</b></div>
												<div>
													<ul style="font-weight:normal;font-size:12px;list-style-type:square;padding-left:25px;margin-top:5px;">
														<li><a href="%s">Mejelajah toko untuk</a> produk lain & layanan.</li>
														<li>Pergi ke <a href="%s">Halaman Beranda kami</a></li>
													</ul>
												</div>');

define('TEXT_FIELD_REQUIRED', '&nbsp;<span class="requiredInfo">* Diperlukan</span>');
define('TEXT_INSTANT_DELIVERY', 'Ada Stok');
define('TEXT_INSTANT', 'Segera');
define('TEXT_MEMORY_USAGE', 'Penggunaan memori');
define('TITLE_TRANS_BUYBACK_ORDER', 'Order Buyback %s');
define('TITLE_BUYBACK_PAYMENT_REPORT', 'Laporan Pembayaran');

define('TEXT_ORDERS', "order");
define('TEXT_PENDING', "tertunda");
define('TEXT_PROCESSING', "pemrosesan");

define('ERROR_TEP_MAIL', '<font face="Verdana, Arial" size="2" color="#ff0000"><b><small>TEP ERROR:</small> ERROR: Tidak dapat mengirim email melalui server SMTP tertentu. Silakan periksa setelan php.ini Anda dan memperbaiki server SMTP jika perlu. </b></font>');
define('ERROR_PAYMENT_CURRENCY_NOT_SUPPORTED', 'Error: gerbang pembayaran yang Anda telah memilih, %s, hanya mendukung %s. Silakan pilih mata uang lain yang didukung atau silakan pilih gateway pembayaran lain.');
define('WARNING_INSTALL_DIRECTORY_EXISTS', 'Peringatan: Direktori instalasi wujud di: ' . dirname($HTTP_SERVER_VARS['SCRIPT_FILENAME']) . '/install. Hapus direktori ini untuk alasan keamanan.');
define('WARNING_CONFIG_FILE_WRITEABLE', 'Peringatan: Saya dapat menulis ke dalam file konfigurasi: ' . dirname($HTTP_SERVER_VARS['SCRIPT_FILENAME']) . '/includes/configure.php. Ini adalah resiko keamanan yang potensial - Silakan menyetel pengguna yang berhak mengakses file ini.');
define('WARNING_SESSION_DIRECTORY_NON_EXISTENT', 'Peringatan: Direktori sesi tidak wujud: ' . tep_session_save_path() . '.  Sesi tidak akan berfungsi sampai direktori ini dibuat.');
define('WARNING_SESSION_DIRECTORY_NOT_WRITEABLE', 'Peringatan: Saya tidak dapat menulis ke direktori sesi: ' . tep_session_save_path() . '. Sesi tidak akan berfungsi sampai pengguna yang berhak ditetapkan.');
define('WARNING_SESSION_AUTO_START', 'Peringatan: session.auto_start telah diaktifkan - silakan menonaktifkan fitur php ini di php.ini dan restart server web.');
define('WARNING_DOWNLOAD_DIRECTORY_NON_EXISTENT', 'Peringatan: Direktori produk unduhan tidak wujud: ' . DIR_FS_DOWNLOAD . '. produk unduhan tidak akan berfungsi sampai direktori ini valid.');
define('WARNING_PRODUCTS_LOW_QUANTITY', 'Batas order telah terlampaui . ');
define('WARNING_PRODUCTS_SUGGESTED_QUANTITY', 'Kuantitas yang disarankan adalah %d.');
define('WARNING_PRODUCTS_OVERWEIGHT', 'Mohon Maaf! Jumlah berat Anda untuk produk ini melebihi berat maksimum!');
define('WARNING_PRODUCTS_UNDERWEIGHT', 'Mohon maaf! Jumlah berat Anda untuk produk ini kurang dari berat total produk! Silakan mengedit pilihan Anda agar sesuai dengan jumlah berat.');
define('WARNING_EMPTY_SELECTIONS', 'Silakan pilih setidaknya satu barang!');
define('WARNING_ORDER_AMOUNT_ZERO', 'Checkout gagal! Produk dalam troli belanja tidak memiliki nilai harga.');
define('SUCCESS_DYNAMIC_CART_ADDED', 'Produk ini telah berhasil ditambahkan ke troli! <a href="%s">Klik di sini untuk melihat troli.</a>');
define('SUCCESS_DYNAMIC_CART_UPDATED', 'Troili belanja produk ini telah berhasil diperbarui! <a href="%s">Klik di sini untuk melihat troli.</a>');

define('TEXT_CCVAL_ERROR_INVALID_DATE', 'Tanggal kedaluwarsa untuk kartu kredit tidak valid.<br>Silakan periksa tanggal dan coba lagi.');
define('TEXT_CCVAL_ERROR_INVALID_NUMBER', 'Nomor kartu kredit yang dimasukkan tidak valid.<br>Silakan periksa nomor dan coba lagi.');
define('TEXT_CCVAL_ERROR_UNKNOWN_CARD', 'Empat digit pertama dari nomor yang dimasukkan adalah: %s<br>Jika angka itu benar, kami tidak menerima jenis kartu kredit. Jika salah, silakan coba lagi.');

define('TEXT_ERROR_ACTIVATE_ACCOUNT', 'Silakan aktifkan akun Anda untuk checkout.');
define('TEXT_STOCK_NOT_AVAILABLE', 'Jumlah yang diminta tidak tersedia. Untuk membeli dalam jumlah besar, silakan hubungi <a href="mailto:<EMAIL>"><EMAIL></a>');
define('TEXT_CUSTOMER_VERIFIED_PAYMENT_EMAIL_NOTICE_SUBJECT', 'Notifikasi Verifikasi Alamat Email Pembayaran Pelanggan');
define('TEXT_CUSTOMER_NAME', 'Nama Pelanggan: ');
define('TEXT_CUSTOMER_PAYMENT_EMAIL', 'Alamat Email Pembayaran: ');
define('TEXT_NEWSLETTER_MSG', "<div style=\"font-family: Arial, Verdana, sans-serif; font-weight: bold; font-size: 12px; color: green;\">Catatan Penting:</div>" . '<span class="latestNewsBoxContents">Untuk memastikan penerimaan email kami, silakan tambahkan <a href="mailto:<EMAIL>"><EMAIL></a> ke \'Whitelist\' atau \'Buku Alamat\' Anda. <a href="http://www.offgamers.com/user-guide-whitelist-emails-i-491.ogm" target="_blank">Klik di sini untuk belajar bagaimana</a>.</span>');

define('EMAIL_TEXT_AFFILIATE_NOTIFICATION_SUBJECT', 'Penjualan Afiliasi  #%d (Jumlah Order: %s)');
define('EMAIL_TEXT_AFFILIATE_NOTIFICATION_INFO', 'Penjualan disebut oleh Anda sebagai berikut:' . "\n\n" . 'Tanggal Order: %s' . "\n" . 'ID Order: %s' . "\n" . 'Jumlah Order: %s' . "\n");

define('LOG_SALES_ORDER', '##%d##: ##%d## ##%d##');

define('LOG_SC_STAT_SC_CONVERT', 'Konversi Mata Uang Kredit Toko');
define('LOG_SC_ACTIVITY_TYPE_PURCHASE', 'P');
define('LOG_SC_ACTIVITY_TYPE_CANCEL', 'X');
define('LOG_SC_ACTIVITY_TYPE_CONVERT', 'V');

//payment pop up menu
define('POP_PAYMENT_REPORT_PAYMENT_MESSAGE', 'Pesan Pembayaran');
define('POP_PAYMENT_REPORT_DATETIME', 'Tanggal/Waktu');

// Definitions for Transaction Status Update Notification
define('EMAIL_CUSTOMER_ORDER_UPDATE_NOTIFICATION_SUBJECT', 'Notifikasi Pembaruan Order Pelanggan #%d');
define('EMAIL_TRANS_UPDATE_NOTIFICATION_CONTENT', 'ID Order: %s' . "\n" . 'Tanggal Order: %s' . "\n" . 'Jumlah Order: %s' . "\n" . 'Cara Pembayaran: %s' . "\n\n" . 'Jenis Pembaruan: %s' . "\n" . 'Status Pembaruan: %s -> %s' . "\n" . 'Tanggal Pembaruan: %s' . "\n" . 'IP Pembaruan: %s' . "\n" . 'Pengguna Pembaruan: %s' . "\n\n" . 'Komentar Pembaruan:' . "\n" . '%s');
define('EMAIL_TEXT_TRANS_UPDATE_MANUAL_NOTIFICATION', 'Manual');
define('EMAIL_TEXT_TRANS_UPDATE_AUTO_NOTIFICATION', 'Otomatis');

// Polling
define('BUTTON_VIEW_RESULT', 'Hasil');
define('BUTTON_VOTE', 'Undi');

// File upload message
define('ERROR_NO_UPLOAD_FILE', 'Error: Error: Sumber file tidak wujud.');
define('ERROR_NO_TMP_DIR', 'Error: Hilang folder sementara.');
define('ERROR_DESTINATION_DOES_NOT_EXIST', 'Error: Destinasi tidak wujud.');
define('ERROR_DESTINATION_NOT_WRITEABLE', 'Error: Destinasi tidak dapat ditulisi .');
define('ERROR_FILESIZE_EXCEED', 'Error: Ukuran file telah melebihi batas yang diizinkan.');
define('ERROR_UPLOAD_PARTIAL', 'Error:  File yang diunggah hanya berhasil diunggah sebagian .');
define('ERROR_FILE_NOT_SAVED', 'Error: File diunggah tidak disimpan.');
define('ERROR_FILETYPE_NOT_ALLOWED', 'Error: Jenis file diunggah tidak diizinkan.');

define('SUCCESS_FILE_SAVED_SUCCESSFULLY', 'Sukses: File diunggah berhasil disimpan.');
define('WARNING_NO_FILE_UPLOADED', 'Peringatan: Tidak ada file diunggah.');
define('WARNING_MUST_BE_VIP_MEMBER', 'Peringatan: Hanya VIP memiliki fitur ini.');

define('WARNING_FILE_UPLOADS_DISABLED', 'Peringatan: Unggahan file dinonaktifkan dalam file konfigurasi php.ini.');
define('WARNING_NOTHING_TO_EXPORT', 'Peringatan: Tidak ada data yang akan diekspor.');
define('WARNING_NOTHING_TO_IMPORT', 'Peringatan: Tidak ada data yang akan diimpor.');

define('ERROR_PAGE_ACCESS_DENIED', 'Error: Anda tidak memiliki izin untuk mengakses halaman ini.');

// Email verification
define('EMAIL_SUBJECT_2', 'Verifikasi Alamat Email');
define('EMAIL_VERIFY_CONTENT', 'Baru-baru ini, Anda telah mendaftar ' . STORE_OWNER . ' menggunakan alamat email ini. Untuk melengkapi pendaftaran Anda, ikuti taut di bawah ini:' . "\n");
define('EMAIL_VERIFY_CONTENT_ADDRESS_INFO', '(Jika mengklik taut tidak berfungsi, coba salin dan sisipkan ke peramban Anda.) ' . "\n\n");
define('EMAIL_MANUAL_ACTIVATE_EMAIL_2', 'Anda juga dapat memverifikasi alamat e-mail Anda secara manual di<a href="' . tep_href_link(FILENAME_ACCOUNT_ACTIVATE, 'verify=email&auto_mail=yes', 'SSL') . '">' . tep_href_link(FILENAME_ACCOUNT_ACTIVATE, 'verify=email&auto_mail=yes', 'SSL') . '</a> dengan memasukkan informasi berikut. ' . "\n\n" . 'Alamat Email: ');
define('EMAIL_MANUAL_ACTIVATE_CODE_2', "\n" . 'Kode Verifikasi: ');
define('EMAIL_CONTACT', 'Untuk sebarang pertanyaan atau bantuan, silakan gunakan Online Live Support atau email pertanyaan Anda ke ' . EMAIL_TO . '. Terima kasih untuk berbelanja di ' . STORE_NAME . ".\n\n\n");
define('EMAIL_FOOTER', STORE_EMAIL_SIGNATURE);

//customer order comment remark
define('TEXT_VIP_BUYBACK_ORDER_REFERENCE_REMARK', 'Disampaikan melalui order VIP: ##BO##%d##');
define('TEXT_BUYBACK_ORDER_REFERENCE_REMARK', 'Disampaikan melalui order Buyback: ##BO##%d##');

define('TEXT_BUYBACK_ORDER_ITEMS_DELIVERED_REMARK', 'Barang berikut telah disampaikan:');
define('TEXT_BUYBACK_ORDER_ITEMS_DEDUCTED_REMARK', 'Barang berikut telah dikurangi:');

define('TEXT_FRAUD_DISCLAIMER', '<br>
		<table style="border-right: 2px dashed #ff0000; padding-right: 10px; border-top: 2px dashed #ff0000; padding-left: 10px; padding-bottom: 15px; margin: 0px 0px 15px; border-left: 2px dashed #ff0000; padding-top: 10px; border-bottom: 2px dashed #ff0000; background-color: #fffef2;">
        	<tr>
				<td>
					<div style="font-family: Arial, Verdana, sans-serif; font-weight: 700; font-size: 16px; color: #ff0000; border-bottom: 1px dashed #ff0000; text-align: left">Attn : Fraudsters and scammers</div>
					<BR><span class="latestNewsBoxContents">
					PLEASE BE FOREWARNED THAT CREDIT CARD FRAUD IS A FEDERAL CRIME THAT IS
					INVESTIGATED BY THE <a href="http://www.fbi.gov/cyberinvest/cyberhome.htm" target="_blank">FBI</a> (FEDERAL BUREAU OF INVESTIGATION), <a href="http://www.secretservice.gov/financial_crimes.shtml" target="_blank">UNITED
					STATES SECRET SERVICE</a> AND THEIR PARTNERS INCLUDING <a href="http://www.interpol.int/Public/FinancialCrime/default.asp" target="_blank">INTERPOL
					INTERNATIONAL</a>.  BY MANDATE OF LAW, WE ARE REQUIRED TO REPORT SUCH
					ABUSES AND WE WILL HAPPILY COMPLY WITH THAT MANDATE. <br>
					<br>
					OFFGAMERS WILL SUPPLY ALL INFORMATION INCLUDING *IP ADDRESSES, M.A.C
					ADDRESSES, VOIP REGISTERED PHONE NUMBERS, REVERSE LOOKUP INFORMATION,
					RECORDED CONVERSATIONS, *COMPILED FROM FRAUDULENT ORDERS TO OFFICIALS
					AND LOCAL AUTHORITIES WHO WILL THEN PURSUE INVESTIGATION AND PROSECUTION
					OF THE CRIMINAL ACTION TAKEN BY THE OFFENDING PARTIES.  ONCE THE
					CRIMINAL CASE HAS BEEN COMPLETED, A SEPARATE CIVIL CASE IS THEN
					SUBMITTED AGAINST THE OFFENDING PARTY TO RECOVER ANY AND ALL DAMAGES
					(FINANCIAL OR OTHERWISE) THAT IS INCURRED BY OFFGAMERS. <br>
					<br>
					PLEASE BE AWARE THAT OFFGAMERS HAS DETECTED AND <a href="http://www.ic3.gov/" target="_blank">REPORTED</a> MANY ATTEMPTS
					TO CHEAT, SCAM, FRAUDULENTLY STEAL AN IDENTITY, INTRUSIVE AND
					UNAUTHORIZED ENTRY INTO OUR SERVERS.  LET THIS BE A WARNING TO ALL THAT
					WE TAKE THE SECURITY OF OUR ORDERING SYSTEM VERY SERIOUSLY AND ANY
					ATTEMPT TO FRAUDULENTLY PLACE AN ORDER WHETHER BY IMPERSONATION,
					FORGERY, ALTERATION, FALSE CLAIMS, FRAUDULENTLY USE A CREDIT CARD, OR
					RELATED ACTIVITY IN CONNECTION WITH IDENTIFICATION DOCUMENTS AND
					FRAUDULENT COMMERCIAL, FICTITIOUS INSTRUMENTS WILL BE REPORTED IMMEDIATELY. <br>
					<br>
					THE SECRET SERVICE IS THE PRIMARY FEDERAL AGENCY TASKED WITH
					INVESTIGATING ACCESS DEVICE FRAUD AND ITS RELATED ACTIVITIES UNDER TITLE
					18, UNITED STATES CODE, SECTION 1029.  DO NOT  BE FOOLED TO THINK THAT
					IT IS SAFE TO OPERATE BEHIND A SPOOFED IP ADDRESS AND A VOIP
					PHONE-LINE.  THE <a href="http://www.fbi.gov/contact/legat/legat.htm" target="_blank">FBI</a>, <a href="http://www.secretservice.gov/field_offices.shtml#over" target="_blank">US SECRET SERVICE</a>, AND <a href="http://www.interpol.int/Public/ICPO/Members/default.asp" target="_blank">INTERPOL</a>; OPERATES
					FROM OFFICES AROUND THE WORLD AND WILL WORK WITH <a href="http://www.fbi.gov/page2/march06/cats030606.htm" target="_blank">LOCAL AUTHORITIES</a> TO
					ENSURE THE ARREST OF OFFENDING INDIVIDUAL(S).  IT IS IN OUR OPINION THAT
					A DECADE BEHIND BARS IS JUST NOT WORTH TO STEAL FROM OFFGAMERS.
					</span>
				</td>
			</tr>
		</table>');

define('TEXT_GOLD_DELIVERY_NOTES', '');

define('TEXT_PAYMENT_VERIFICATION_NOTES', '');

define('TEXT_PAYMENT_GATEWAY', 'Gateway Pembayaran');
define('TEXT_ITEMS_IN_MY_CART', '<a href="%s" style="float:none;padding:0;">%s</a> barang dalam troli');
define('TEXT_ITEM_IN_MY_CART', 'barang dalam troli');
define('TEXT_ITEM_IN_MY_CART_FOR_FOOTER', 'Troli (%s)');
define('LINK_VIEW_ALL_PAYMENT_GATEWAY', 'Lihat semua gateway pembayaran');
define('TEXT_TYPE_TO_FILTER_PRODUCTS', 'Ketik untuk menapis Produk');
define('TEXT_SEARCH_WHOLE_STORE', 'Cari seluruh Toko');
define('TEXT_SUBSCRIBE_NEWSLETTER', 'Langgan Sekarang');
define('TEXT_SUBSCRIBE', 'Langgan');
define('TEXT_VIEW_ALL_TESTIMONIALS', 'Lihat Semua Tesimonial');
define('TEXT_MAIN_PLEASE_SELECT_GAME', '<---Silakan pilih game');
define('TEXT_BACK_TO_TOP', 'Kembali ke Atas');
define('TEXT_VIEW_MY_CART', 'LIHAT TROLI SAYA');
define('TEXT_SWITCH_TO_MOBILE', 'Klik di sini beralih ke versi mobile ');
define('TEXT_GAME_NOT_SUPPORT', 'Negara Anda tidak mendukung game ini');
define('TEXT_GAME_NOT_SUPPORT_DESC', 'Karena pembatasan penerbit game, Anda tidak diizinkan untuk mengakses game ini untuk negara yang telah Anda pilih.');

define('TEXT_COPYRIGHT_INFORMATION', 'Hak Cipta &copy; ' . date('Y') . ' OffGamers Global Pte Ltd. Semua hak cipta dilindungi. &nbsp&nbsp <a href="' . tep_href_link('terms-of-service-i-5.ogm') . '">Syarat & Kondisi</a> &nbsp|&nbsp <a href="' . tep_href_link('/privacy-policy-i-4.ogm') . '">Kebijakan Privasi</a>');
define('TAB_LATEST_UPDATE', 'PEMBARUAN TERBARU:');

// New Layout
define('TEXT_CURRENCY', 'Mata Uang');
define('TEXT_MUST_LOGIN', 'Login diperlukan untuk mengakses bagian ini.');
define('TOOLTIPS_MEMBER_ID', 'ID OffGamers');
define('TOOLTIPS_MEMBER_STATUS', 'Status Anggota');
define('TOOLTIPS_STORE_CREDIT_BALANCE', 'Saldo Kredit Toko');
define('TOOLTIPS_OFFGAMERS_POINTS', 'OP adalah point hadiah yang diberikan setelah pembelian berhasil Anda. OP dapat ditukarkan menjadi Store Credit.<br/><br/><a href=\'http://kb.offgamers.com/en/category/my-account/wor-token\' target=\'_blank\'>Baca lebih lanjut</a>');
define('TOOLTIPS_WITHDRAWABLE_BALANCE', 'Kredit Penjual');
define('TEXT_LOGIN', 'Login');
define('TEXT_LOGOUT', 'Logout');
define('TEXT_TRACK_BUYBACK_ORDER', 'Melacak Order Beli');
define('TEXT_TRACK_SELLING_ORDER', 'Melacak Order Jual');
define('TEXT_REGISTER', 'Daftar');
define('TEXT_GREETING_GUEST_NEW', '<font style="color:#92CFF3;">Anda belum login. <a id="fancy_login_box" href="javascript:void(0);" class="whiteText"><b>Login</b></a> sekarang atau <a href="' . tep_href_link(FILENAME_LOGIN, '', 'SSL') . '" class="whiteText"><b>daftar</b></a>.</font>');
define('TEXT_OGM_SERVICE_GUARANTEE', '<font style="font-weight:bold;font-size:12px;">JAMINAN SERPIS OGM</font><br><font class="smallestFont">untuk CD Key / Poin Game</font><br><br><b>Jaminan Uang Kembali</b><br>ika Anda tidak puas dengan progres....<br><br><b>Keselamatan Akun</b><br>Jika Anda akan tidak puas dengan');
define('TEXT_JOIN_AFFILIATE_PROGRAM', 'Klik untuk lebih lanjut...');
define('TEXT_VIEW_PAYMENT_GATEWAY', 'Lihat semua Cara Pembayaran'); //define('TEXT_VIEW_PAYMENT_GATEWAY', 'View All Payment Gateways');
define('TEXT_SERVICE_GURANTEED_ICON', 'icon_serviceguarantee_en.gif');
define('TEXT_MORE_DETAILS', 'Lebih Lanjut');
define('TEXT_COOKIE_NOTICE_DESC', 'OffGamers menggunakan Cookies untuk mengoptimalkan pengalaman online Anda pada situs web kami. Dengan terus menggunakan website kami untuk kebutuhan gaming Anda, Anda menyetujui untuk menggunakan Cookies tersebut.');

define('BUTTON_PROCEED_TO_CHECKOUT', 'Checkout');
define('BUTTON_TOP_GAME_SELECTION', 'Menelusuri Dengan Game');
define('BUTTON_VIEW_ALL_GAMES', 'Lihat Semua Game');
define('BUTTON_MORE_GAMES', 'Lebih Game');

define('HEADER_PRODUCT_SELECTION', 'MENELUSURI DENGAN TOKO');
define('HEADER_GAMES_SELECTION', 'SELEKSI GAME');
define('HEADER_CUSTOMER_TESTIMONIAL', 'TESTIMONIAL PELANGGAN');
define('HEADER_CONFIRM_BILLING_INFO', 'MOHON LENGKAPKAN INFO PEMBAYARAN ANDA');

define('HEADER_RECOMMENDED_PAYMENT_GATEWAY', 'CARA PEMBAYARAN'); //define('HEADER_RECOMMENDED_PAYMENT_GATEWAY', 'FEATURED PAYMENTS');
define('HEADER_NEWSLETTER', 'NAWALA');
define('HEADER_SERVICES', 'LAYANAN');
define('HEADER_EXPANDED_INFORMATION', 'CATATAN EKSTRA UNTUK PELANGGAN:');
define('BOX_HEADER_AVAILABLE_PRODUCT', 'PRODUK');
define('TEXT_SELLER_INFO_DESC', 'OffGamers bertujuan untuk menyediakan lingkungan perdagangan yang aman bagi individu giat untuk menjual mata uang game kelebihan mereka..
									<br><br>Untuk mempromosikan lingkungan gaming yang aman, adil dan kompetitif, kami tidak membenarkan penggunaan bots, hacks, macro ilegal, program pihak ketiga, iklan ilegal dan perilaku tidak etis dalam game. Setiap penjual yang diduga dengan melanggar salah satu tindakan yang disebutkan akan di-daftar hitam dan pembayaran disita sebagai bentuk hukuman.
									<br><br>OffGamers telah menghubungkan pembeli dan penjual dari seluruh dunia, kami menjamin Anda bahwa data pribadi Anda dirahasiakan sambil memberikan platform layanan terbaik bagi para pedagang untuk melakukan bisnis mereka.
									<br><br><div class="dottedLine"><!-- --></div><br>
									<h2>Pemberitahuan</h2><br>
									&#8250; <h3>Notis Registrasi:</h3> Pastikan bahwa semua informasi kontak (Nomor Ponsel misalnya, QQ, MSN, dll) disediakan adalah benar dan bahwa Anda ingat \'Secret Question and Answer\' Anda setiap saat untuk memastikan proses kelancaran transaksi Anda. Semua informasi tersebut akan digunakan sebagai verifikasi sementara Anda mengubah profil Anda atau melakukan penarikan. <br><br>Mohon diperhatikan bahwa semua penjual yang mendorong untuk menyerahkan semua cuplikan pengiriman dalam waktu 1 jam setelah pengiriman selesai. Semua order dengan cuplikan diajukan akan ditangani dengan prioritas dan diselesaikan segera. Apabila tidak ada screenshot disampaikan dalam waktu satu jam, order akan secara otomatis ditunda hingga 5 hari dan pelanggan akan dihubungi untuk memverifikasi bahwa pengiriman telah selesai. Harap pastikan bahwa semua cuplikan layar yang disampaikan tepat waktu untuk menghindari penundaan yang tidak perlu untuk penyelesaian order Anda, terima kasih atas kerjasama Anda. <br><br>
                                    &#8250; <h3>Cuplipkan Layar Pengiriman:</h3> Please be informed that all sellers are encourages to submit all trading screenshots within 1 hour after trade is completed. All orders with screenshots submitted will be handled with priority and completed immediately. Should there be no screenshots submitted within the hour, the order will be automatically put on hold for up to 5 days and the customer will be contacted to verify that trade is complete. Please ensure that all screenshots are submitted on time to avoid unnecessary delays to the completion of your order, thank you for your cooperation.<br><br>
									&#8250; <h3>Kadaluarsa Order:</h3> Harap perhatikan bahwa semua order akan berakhir dalam waktu 1 jam. Jika Anda tidak dapat berdagang dengan pelanggan kami dalam jangka waktu tersebut, harap hubungi Live Support kami segera. OffGamers tidak akan bertanggung jawab atas pengiriman yang dilakukan di luar jam jangka waktu 1 jam untuk setiap order.<br><br>
									&#8250; <a href="http://kb.offgamers.com/en/component/kb/article/trading_in_game_all_you_need_to_know">Prosedur untuk menjual Mata Uang game Anda</a>
									<br>&#8250; <a href="/wow-gold-trading-procedures-i-345.ogm">Panduan Penjual untuk WoW</a>
									<br>&#8250; <a href="/sell-gold-service-guide-i-299.ogm">Panduan Penjual</a>
									<br>&#8250; <a href="/sell-gold-service-i-301.ogm">Syarat & Kondisi Penjual</a>
									<br>&#8250; <a href="http://kb.offgamers.com/en/component/kb/article/guide_transfer_withdrawable_store_credits_to_your_payment_account">Panduan Kredit Toko / Saldo Penarikan</a>');

define('BOX_EVENT_AND_PROMOTION', 'Acara & Promosi');
define('BOX_GAME_GUIDES', 'Panduan Game');
define('BOX_HEADING_ITEM_ADDED_TO_SHOPPING_CART', 'Barang Ditambahkan ke dalam Troli Belanja');
define('BOX_STORE_CREDIT', 'Kredit Toko');
define('HTML_AFFILIATE_BOX', '<div style="width:100%;text-align:left;"><b><span style="font-size:16px;">Cari Uang Dengan Kami</span></b><br>Komisi besar dan setup yang mudah.</div>');

define('HTML_NEWSLETTER_BOX', 'Langgani nawala kami untuk penawaran khusus, eksklusif dan promosi.');
define('HEADER_DELIVERY_INFORMATION', 'Informasi Pengiriman');
define('HEADER_STORE_CREDIT_USE', 'Kredit Toko');
define('TEXT_STORE_CREDIT_USE', '<p>Untuk menggunakan kredit toko untuk order Anda, Anda perlu checkout dalam mata uang yang sesuai dengan kredit toko Anda.</p><p>Adakah Anda ingin mengganti mata uang troli belanja Anda untuk mengguna? </p>');

define('TEXT_STORE_CREDIT_CONVERT', '<p>Untuk menggunakan kredit toko untuk order Anda, Anda perlu mengkonversi kredit toko Anda ke mata uang yang cocok dengan order Anda ini.</p><p>Saldo kredit toko saat: %s<br/>Setelah konversi: %s<br/>Nilai konversi: %s (diperbaharui setiap hari)<p>Adakah Anda ingin mengganti mata uang dari saldo kredit toko Anda saat ini untuk cocok dengan order Anda? </p>');
define('TEXT_STORE_CREDIT_CONVERT_IN_MY_ACCOUNT', '<p>Untuk menggunakan kredit toko Anda untuk order ini, mata uang kredit toko Anda dan troli belanja harus sesuai.</p><p>Adakah Anda ingin mengganti mata uang dari kredit toko Anda untuk menyesuaikan bahwa troli belanja Anda untuk melanjutkan?</p>');
define('TEXT_STORE_CREDIT_POPUP_DESCRIPTION', 'Tampaknya ada perbedaan antara Kredit Toko yang tersedia dari Anda dengan mata uang situs yang Anda telah memilih sebelumnya. <b>Apakah Anda ingin:-</b>');
define('TEXT_STORE_CREDIT_POPUP_CHANGE_WEBSITE_CURRENCY', 'Mengubah mata uang situs untuk mencocokkan mata uang Kredit Toko saya(%s)');
define('TEXT_STORE_CREDIT_POPUP_CHANGE_STORE_CREDIT_CURRENCY', 'Mengubah mata uang Kredit Toko saya untuk mencocokkan mata uang situs(%s)');
define('TEXT_STORE_CREDIT_POPUP_CONTINUE_WITH_NO_CHANGE', 'Kredit Toko tidak akan digunakan untuk pembelian ini.');
define('TEXT_LOGIN_PASSWORD', 'SANDI LOGIN');

define('LINK_CHANGE_DELIVERY_INFO', ' Mengubah informasi pengiriman');
define('TEXT_TRADE_MODE', 'Mode Pengiriman:  %s');
define('TEXT_TRADE_METHOD', 'Metode Pengiriman:  <b>%s</b>');
define('OPTION_FACE_TO_FACE', 'Pengiriman Tatap Muka');
define('OPTION_PUT_IN_MY_ACCOUNT', 'Letak dalam akun saya');
define('OPTION_BY_MAIL', 'Surat');
define('OPTION_OTHERS', 'Lainnya');
define('OPTION_OPEN_STORE', 'Buka Toko');

define('BUYBACK_SUPPLIER_MODE_F2F', 'Tatap Muka');
define('BUYBACK_SUPPLIER_MODE_MAIL', 'Surat');
define('BUYBACK_SUPPLIER_MODE_PIMA', 'Letak dalam akun saya');
define('BUYBACK_SUPPLIER_MODE_T2O', 'Berdagang dengan OffGamers');
define('BUYBACK_SUPPLIER_MODE_T2C', 'Berdagang dengan Pembeli');
define('BUYBACK_SUPPLIER_MODE_OPEN_STORE', 'Buka Toko');

define('HEADER_TOTAL', 'Jumlah: ');
define('TEXT_NUMERIC', 'NUMERIK');
define('BREADCRUMB_BUY', 'Beli');
define('BREADCRUMB_SELL', 'Jual');

define('TEXT_PROMOTION_STATUS', 'Status');
define('TEXT_PROMOTION_PRODUCTS', 'PRODUK PROMOSI');
define('TEXT_RETYPE', 'Ketik Ulang ');
define('TEXT_SOFTPIN', 'SOFTPIN');
define('TEXT_STATUS_LIMITED_STOCK', 'Stok terbatas');
define('TEXT_STATUS_FAST_SELLING', 'Jual cepat');
define('TEXT_STATUS_PRICE_SLASH', 'Potongan Harga');

// Locking / Unlocking log tag
if (!defined('ORDERS_LOG_LOCK_ORDER'))
    define('ORDERS_LOG_LOCK_ORDER', '##l_1##');
if (!defined('ORDERS_LOG_UNLOCK_OWN_ORDER'))
    define('ORDERS_LOG_UNLOCK_OWN_ORDER', '##ul_1##');
if (!defined('ORDERS_LOG_UNLOCK_OTHER_PEOPLE_ORDER'))
    define('ORDERS_LOG_UNLOCK_OTHER_PEOPLE_ORDER', '##ul_2##%s:~:%s');

define('LOG_PARTIAL_DELIVERY_SALES', '##%d##: Jualan Pengiriman bahgian');
define('LOG_CDKEY_ID_STR', 'ID CD Key: %s');
define('LOG_BUYBACK_ORDER', '##BUYBACK:%d##');

define('LIVE_SUPPORT', "");
define('B2C_SELLING_LIVE_SUPPORT', "<script type='text/javascript'>var __lc={};__lc.license=1306592;__lc.skill=3;__lc.params=[{name: 'identifier ', value: 'OGM_CUSTOMER_NAME' },{name: 'discountGroup', value: ' OGM_CUSTOMER_GROUP ' },{name: 'customerEmail', value: 'OGM_CUSTOMER_EMAIL' }];(function(){var lc=document.createElement('script');lc.type='text/javascript';lc.async=true;lc.src=('https:'==document.location.protocol ? 'https://' : 'http://') + 'cdn.livechatinc.com/tracking.js';var s=document.getElementsByTagName('script')[0];s.parentNode.insertBefore(lc, s);})();</script><style type='text/css'>#livechat-compact-container,#livechat-full{z-index: 565 !important;}#livechat-eye-catcher{bottom: 69px !important;z-index: 565 !important;}</style>");

require(DIR_WS_LANGUAGES . 'add_ccgvdc_english.php');

// Captcha
define('ERROR_INVALID_CODE', 'Maaf, kode yang dimasukkan tidak valid.');
define('TEXT_CAPTCHA_ANSWER', 'Jawaban');
define('TEXT_REFRESH_CAPTCHA', 'Jika Anda tidak bisa membaca ini, <a href="javascript:void(0);" onClick="get_captcha_img(\'%s\');">coba lagi</a>');
define('TEXT_REFRESH_CAPTCHA_VIP', 'Jika Anda tidak bisa membaca ini, <a href="javascript:void(0);" onClick="get_captcha_img(\\\'%s\\\');">coba lagi</a>');
define('TEXT_CAPTCHA_INSTRUCTION', 'Untuk nilai negatif, masukkan misalnya (-8)');
define('NOTE_DO_NOT_RETURN_GOODS', '<b>Penting:</b> penjual legit tidak akan pernah meminta Anda untuk mengembalikan uang game setelah pengiriman selesai. Jangan kembali ke siapa pun yang mengaku sebagai penjual kita atau bahkan ke ID karakter sama yang diperdagangkan dengan Anda.');

// Facebook Connect
define('ERROR_INVALID_FB_UID', 'ID Pengguna facebook tidak valid.');
define('SUCCESS_FB_DISCONNECTED', '<h3><font color=red>Akun Facebook Anda telah terputus</font></h3>');
define('HEADER_FB_CONNECT_SELECTION', 'Connect dengan Facebook');
define('TEXT_FB_CONNECT_SELECTION', 'Hi %s,<br>Adakah Anda memiliki Akun OffGamers?');
define('OPTION_FB_CONNECT_SELECTION_FIRST', '<b>No, Tidak, aku baru untuk OffGamers!</b><br>Daftar akun baru OffGamers!');
define('OPTION_FB_CONNECT_SELECTION_SECOND', '<b>Ya saya memiliki akun OffGamers!</b><br>Menghubungkan akun Facebook dan OffGamers saya!');

// HLA
define('TEXT_ADVANCED_SEARCH_OPTIONS', 'Pilihan Pencarian Lanjut');

define('TEXT_HLA_ANY', 'Apa saja');

define('TABLE_HLA_HEADING_LEVEL', 'LEVEL');
define('TABLE_HLA_HEADING_RACE', 'BANGSA');
define('TABLE_HLA_HEADING_CLASS', 'KELAS');
define('TABLE_HLA_HEADING_REFERRENCE', 'REFERENSI');
define('TABLE_HLA_HEADING_PRICE', 'HARGA');
define('TABLE_HLA_HEADING_ACTION', 'TINDANKAN');

define('TEXT_HLA_VIEW_PROFILE', '&#8250;&nbsp;Lihat Profil');
define('TEXT_HLA_ALTERNATE_CHARACTERS', 'Karakter Alternatif');

define('EMAIL_HLA_NEW_BUYBACK_ORDER_NUMBER', 'Order Buyback Baru: %s');
define('EMAIL_HLA_NEW_BUYBACK_DATE_ORDERED', 'Tanggal Order Buyback: %s');
define('EMAIL_HLA_NEW_BUYBACK_CUSTOMER_EMAIL', 'Alamat Email: %s');
define('EMAIL_HLA_NEW_BUYBACK_STATUS', 'Status: %s');
define('EMAIL_HLA_NEW_BUYBACK_SUBJECT', implode(' ', array(EMAIL_SUBJECT_PREFIX, "Order Buyback Baru #%s")));
define('EMAIL_HLA_NEW_BUYBACK_BODY', "Terima kasih untuk menjual barang-barang Anda ke " . STORE_NAME . ".\n\n Ringkasan order Pemasok:\n" . EMAIL_SEPARATOR . "\n");
define('EMAIL_HLA_NEW_BUYBACK_PRODUCTS', "\n\n" . 'Produk');
define('EMAIL_HLA_NEW_BUYBACK_ORDER_TOTAL', 'Jumlah: %s');
define('EMAIL_HLA_NEW_BUYBACK_COMMENTS', 'Informasi Ekstra:');
define('EMAIL_HLA_NEW_BUYBACK_ORDER_CLOSING', "Silakan hubungi kami melalui Online Live Support kami atau <NAME_EMAIL> jikaanda menghadapi masalah dengan buyback. Harap ingat untuk menyertakan Nomor order  buyback Anda ketika menghubungi kami untuk mempercepat proses.\n\n Anda akan menerima notifikasi email setiap kali ada pembaruan di status order buyback Anda. Anda juga dapat meninjau order buyback dan memeriksa status mereka dengan login ke OffGamers.com dan mengklik pada \"View Buyback Order History\" .\n\nTerima kasih untuk mendukung " . STORE_NAME . '.');

define('EMAIL_HLA_NEW_BUYBACK_ORDER_FOOTER', "\n\n" . EMAIL_FOOTER);

define('EMAIL_HLA_BUYBACK_ORDER_GUIDE', '
	<b>Alur order : [Pending] --> [Processing] --> [Completed] atau [Canceled]</b>

	[Pending] : Order diserah tidak lengkap. Mohon login ke OffGamers.com dan menyampaikan informasi karakter yang hilang.
	[Processing] : Pengiriman ini sedang didaftarkan ke dalam sistem kami.
	[Completed] : Pengiriman ini telah terdaftar sepenuhnya
	[Canceled] : Order telah dibatalkan.

	<b>Catatan 1:</b> Silakan memastikan Informasi Akun Anda berikan adalah 100% akurat

	<b>Catatan 2:</b> Setelah Informasi Akun telah disampaikan, jangan login ke akun, baik melalui halaman pengelolaan akun atau dalam game.

	Penting: Harap perhatikan bahwa Blizzard dapat mengunci akun jika mereka mendeteksi IP yang berbeda, mengakses akun dalam waktu singkat. Sebagai tindakan pencegahan:

	<b>Catatan 3:</b> Setelah order Anda di status [Completed], pembayaran akan tersedia untuk Anda dalam waktu 15 menit di akun OffGamers Anda. Gunakan fungsi [Menarik] untuk  mengambil pembayaran Anda dan mengkreditkan ke cara pembayaran yang Anda pilih.
	');

define('EMAIL_HLA_SUPPLIER_SUBMITTED_INFO', '
	<b>Alur Order : [Pending] --> [Processing] --> [Completed] atau [Canceled]</b>

	Informasi Diserah
	');

define('TEXT_HLA_SEARCH_NO_RECORDS', 'Maaf, tidak ada karakter / akun yang tersedia sesuai dengan pilihan Anda. Silakan memodifikasi persyaratan Anda dan cari lagi.');

define('HLA_REGION', 'Daerah');
define('HLA_RACE', 'Bangsa');
define('HLA_CLASS', 'Kelas');
define('HLA_SIDE', 'Faksi');
define('HLA_LEVEL', 'Level');
define('HLA_GENDER', 'Gender');
define('HLA_PRICE', 'Harga');
define('HLA_TALENT', 'Talent');
define('HLA_REFERENCE_ID', 'ID Referensi ');
define('HLA_SERVER', 'Server');

// WOW : Race
define('HLA_RACE_BLOOD_ELF', 'Blood Elf');
define('HLA_RACE_DRAENEI', 'Draenei');
define('HLA_RACE_DWARF', 'Dwarf');
define('HLA_RACE_GNOME', 'Gnome');
define('HLA_RACE_GOBLIN', 'Goblin');
define('HLA_RACE_HUMAN', 'Human');
define('HLA_RACE_NIGHT_ELF', 'Night Elf');
define('HLA_RACE_ORC', 'Orc');
define('HLA_RACE_PANDAREN', 'Pandaren');
define('HLA_RACE_TAUREN', 'Tauren');
define('HLA_RACE_TROLL', 'Troll');
define('HLA_RACE_UNDEAD', 'Undead');
define('HLA_RACE_WORGEN', 'Worgen');

// Warhammer : Race
define('HLA_RACE_CHAOS', 'Chaos');
define('HLA_RACE_DARK_ELF', 'Dark Elf');
define('HLA_RACE_WARHAMMER_DWARF', 'Dwarf');
define('HLA_RACE_EMPIRE', 'Empire');
define('HLA_RACE_GREENSKIN', 'Greenskin');
define('HLA_RACE_HIGH_ELF', 'High Elf');

// Age Of Conan : Race
define('HLA_RACE_AQUILONIAN', 'Aquilonian');
define('HLA_RACE_CIMMERIAN', 'Cimmerian');
define('HLA_RACE_STYGIAN', 'Stygian');

// AION : Race
define('HLA_RACE_ASMODIAN', 'Asmodian');
define('HLA_RACE_ELYOS', 'Elyos');

// RIFT : Race
define('HLA_RACE_DEFIANT', 'Defiant');
define('HLA_RACE_GUARDIAN', 'Guardian');

// WOW : Class
define('HLA_CLASS_DEATH_KNIGHT', 'Death Knight');
define('HLA_CLASS_DRUID', 'Druid');
define('HLA_CLASS_HUNTER', 'Hunter');
define('HLA_CLASS_MAGE', 'Mage');
define('HLA_CLASS_MONK', 'Monk');
define('HLA_CLASS_PALADIN', 'Paladin');
define('HLA_CLASS_PRIEST', 'Priest');
define('HLA_CLASS_ROGUE', 'Rogue');
define('HLA_CLASS_SHAMAN', 'Shaman');
define('HLA_CLASS_WARLOCK', 'Warlock');
define('HLA_CLASS_WARRIOR', 'Warrior');

// Warhammer : Class
define('HLA_CLASS_ARCHMAGE', 'Archmage');
define('HLA_CLASS_BLACK_ORC', 'Black Orc');
define('HLA_CLASS_BLAZING_SUN_KNIGHT', 'Blazing Sun Knight');
define('HLA_CLASS_BRIGHT_WIZARD', 'Bright Wizard');
define('HLA_CLASS_CHOPPA', 'Choppa');
define('HLA_CLASS_CHOSEN', 'Chosen');
define('HLA_CLASS_DARK_ELF_BLACK_GUARD', 'Dark Elf Black Guard');
define('HLA_CLASS_DISCIPLE_OF_KHAINE', 'Disciple of Khaine');
define('HLA_CLASS_ENGINEER', 'Engineer');
define('HLA_CLASS_IRONBREAKER', 'Ironbreaker');
define('HLA_CLASS_MAGUS', 'Magus');
define('HLA_CLASS_MARAUDER', 'Marauder');
define('HLA_CLASS_RUNE_PRIEST', 'Rune Priest');
define('HLA_CLASS_SHADOW_WARRIOR', 'Shadow Warrior');
define('HLA_CLASS_SLAYER', 'Slayer');
define('HLA_CLASS_SORCERESS', 'Sorceress');
define('HLA_CLASS_SQUIG_HERDER', 'Squig Herder');
define('HLA_CLASS_SWORDMASTER', 'Swordmaster');
define('HLA_CLASS_WARRIOR_PRIEST', 'Warrior Priest');
define('HLA_CLASS_WHITE_LION', 'White Lion');
define('HLA_CLASS_WITCH_ELF', 'Witch Elf');
define('HLA_CLASS_WITCH_HUNTER', 'Witch Hunter');
define('HLA_CLASS_ZEALOT', 'Zealot');

define('HLA_CLASS_WARHAMMER_SHAMAN', 'Shaman');

// Age Of Conan : Class
define('HLA_CLASS_ASSASSIN', 'Assassin');
define('HLA_CLASS_BARBARIAN', 'Barbarian');
define('HLA_CLASS_BEAR_SHAMAN', 'Bear Shaman');
define('HLA_CLASS_CONQUEROR', 'Conqueror');
define('HLA_CLASS_DARK_TEMPLAR', 'Dark Templar');
define('HLA_CLASS_DEMONOLOGIST', 'Demonologist');
define('HLA_CLASS_GUARDIAN', 'Guardian');
define('HLA_CLASS_HERALD_OF_XOTLI', 'Herald of Xotli');
define('HLA_CLASS_NECROMANCER', 'Necromancer');
define('HLA_CLASS_PRIEST_OF_MITRA', 'Priest of Mitra');
define('HLA_CLASS_RANGER', 'Ranger');
define('HLA_CLASS_TEMPEST_OF_SET', 'Tempest of Set');

// AION : Class
define('HLA_CLASS_CHANTER', 'Chanter');
define('HLA_CLASS_CLERIC', 'Cleric');
define('HLA_CLASS_GLADIATOR', 'Gladiator');
define('HLA_CLASS_SORCERER', 'Sorcerer');
define('HLA_CLASS_SPIRITMASTER', 'Spiritmaster');
define('HLA_CLASS_TEMPLAR', 'Templar');
define('HLA_CLASS_AION_RANGER', 'Ranger');
define('HLA_CLASS_AION_ASSASSIN', 'Assassin');

define('HLA_SIDE_ALLIANCE', 'Alliance');
define('HLA_SIDE_HORDE', 'Horde');

define('HLA_GENDER_MALE', 'Pria');
define('HLA_GENDER_FEMALE', 'Wanita');

define('HLA_TALENT_AFFLICTION', 'Affliction&nbsp;&nbsp;(Warlock)');
define('HLA_TALENT_ARCANE', 'Arcane&nbsp;&nbsp;(Mage)');
define('HLA_TALENT_ARMS', 'Arms&nbsp;&nbsp;(Warrior)');
define('HLA_TALENT_ASSASSINATION', 'Assassination&nbsp;&nbsp;(Rogue)');
define('HLA_TALENT_BALANCE', 'Balance&nbsp;&nbsp;(Druid)');
define('HLA_TALENT_BEAST_MASTERY', 'Beast Mastery&nbsp;&nbsp;(Hunter)');
define('HLA_TALENT_BLOOD', 'Blood&nbsp;&nbsp;(Death Knight)');
define('HLA_TALENT_COMBAT', 'Combat&nbsp;&nbsp;(Rogue)');
define('HLA_TALENT_DEMONOLOGY', 'Demonology&nbsp;&nbsp;(Warlock)');
define('HLA_TALENT_DESTRUCTION', 'Destruction&nbsp;&nbsp;(Warlock)');
define('HLA_TALENT_DISCIPLINE', 'Discipline&nbsp;&nbsp;(Priest)');
define('HLA_TALENT_ELEMENTAL', 'Elemental&nbsp;&nbsp;(Shaman)');
define('HLA_TALENT_ENHANCEMENT', 'Enhancement&nbsp;&nbsp;(Shaman)');
define('HLA_TALENT_FERAL_COMBAT', 'Feral Combat&nbsp;&nbsp;(Druid)');
define('HLA_TALENT_FIRE', 'Fire&nbsp;&nbsp;(Mage)');
define('HLA_TALENT_FROST', 'Frost&nbsp;&nbsp;(Mage,Death Knight)');
define('HLA_TALENT_FURY', 'Fury&nbsp;&nbsp;(Warrior)');
define('HLA_TALENT_HOLY', 'Holy&nbsp;&nbsp;(Paladin,Priest)');
define('HLA_TALENT_MARKSMANSHIP', 'Marksmanship&nbsp;&nbsp;(Hunter)');
define('HLA_TALENT_PROTECTION', 'Protection&nbsp;&nbsp;(Paladin,Warrior)');
define('HLA_TALENT_RESTORATION', 'Restoration&nbsp;&nbsp;(Shaman,Druid)');
define('HLA_TALENT_RETRIBUTION', 'Retribution&nbsp;&nbsp;(Paladin)');
define('HLA_TALENT_SHADOW', 'Shadow&nbsp;&nbsp;(Priest)');
define('HLA_TALENT_SUBTLETY', 'Subtlety&nbsp;&nbsp;(Rogue)');
define('HLA_TALENT_SURVIVAL', 'Survival&nbsp;&nbsp;(Hunter)');
define('HLA_TALENT_UNHOLY', 'Unholy&nbsp;&nbsp;(Death Knight)');

// Warhammer : Server
define('HLA_SERVER_BADLANDS', 'Badlands');
define('HLA_SERVER_DARK_CRAG', 'Dark Crag');
define('HLA_SERVER_DARKLANDS', 'Darklands');
define('HLA_SERVER_GORFANG', 'Gorfang');
define('HLA_SERVER_HELDENHAMMER', 'Heldenhammer');
define('HLA_SERVER_IRON_ROCK', 'Iron Rock');
define('HLA_SERVER_IRONCLAW', 'Ironclaw');
define('HLA_SERVER_IRONFIST', 'Ironfist');
define('HLA_SERVER_MAGNUS', 'Magnus');
define('HLA_SERVER_MONOLITH', 'Monolith');
define('HLA_SERVER_OSTERMARK', 'Ostermark');
define('HLA_SERVER_PHOENIX_THRONE', 'Phoenix Throne');
define('HLA_SERVER_PRAAG', 'Praag');
define('HLA_SERVER_SKULL_THRONE', 'Skull Throne');
define('HLA_SERVER_THORGRIM', 'Thorgrim');
define('HLA_SERVER_VOLKMAR', 'Volkmar');
define('HLA_SERVER_VORTEX', 'Vortex');
define('HLA_SERVER_WASTELAND', 'Wasteland');

// Age Of Conan : Server
define('HLA_SERVER_BLOODSPIRE', 'Bloodspire');
define('HLA_SERVER_CIMMERIA', 'Cimmeria');
define('HLA_SERVER_GWAHLUR', 'gwahlur');
define('HLA_SERVER_SET', 'Set');
define('HLA_SERVER_TYRANNY', 'Tyranny');
define('HLA_SERVER_WICCANA', 'Wiccana');

// AION US: Server
define('HLA_SERVER_ISRAPHEL', 'Israphel');
define('HLA_SERVER_NEZEKAN', 'Nezekan');
define('HLA_SERVER_SIEL', 'Siel');
define('HLA_SERVER_VAIZEL', 'Vaizel');
define('HLA_SERVER_ZIKEL', 'Zikel');

// AION EU: Server
define('HLA_SERVER_BALDER', 'Balder');
define('HLA_SERVER_KROMEDE', 'Kromede');
define('HLA_SERVER_PERENTO', 'Perento');
define('HLA_SERVER_SPATALOS', 'Spatalos');
define('HLA_SERVER_SUTHRAN', 'Suthran');
define('HLA_SERVER_TELEMACHUS', 'Telemachus');
define('HLA_SERVER_THOR', 'Thor');
define('HLA_SERVER_URTEM', 'Urtem');

define('ERROR_INVALID_TOP_UP_ACCOUNT', 'Info Top-Up tidak valid.');
define('ERROR_DTU_EXCEED_TOP_UP_LIMIT', 'Melampaui batas Top-up.');
define('ERROR_UNABLE_VALIDATE_TOP_UP_ACCOUNT', "Server penerbit kini luring. Akibatnya, pilihan Top-Up Langsung akan luring sampai pemberitahuan lebih lanjut. Ini akan kembali secepat mungkin.");
define('ERROR_DTU_NO_CHARACTERS_IN_LIST', "Tidak ada karakter yang dikembalikan berdasarkan informasi yang diberikan.");

define('TABLE_HEADING_DELIVERY_MODE', 'Mode Pengiriman');

define('TEXT_INFO_SEND_TO_MY_ACCOUNT', 'KIRIM KE AKUN SAYA');
define('TEXT_INFO_CHOOSE_PREFFERED_DELIVERY_MODE', 'Pilih metode pengiriman pilihan Anda');
define('TEXT_INFO_DELIVERY_MODE', 'Metode Pengiriman');
define('TEXT_INFO_DELIVERY_SEND_TO_ACCOUNT', 'Akun OffGamers');
define('TEXT_INFO_DELIVERY_SEND_TO_ACCOUNT_DESCRIPTION', "Kode yang ditampilkan dalam sejarah order OffGamers saya. <a href='http://kb.offgamers.com/en/component/kb/article/user_guide_soft_pins' target='new'>lebih lanjut</a>");
define('TEXT_INFO_DELIVERY_DIRECT_TOP_UP', 'Top-Up Langsung');
define('TEXT_INFO_DELIVERY_DIRECT_TOP_UP_DESCRIPTION', "Isikan info akun Anda dan top-up <br>akan dimasukkan ke akun yang diberikan. <a href='http://kb.offgamers.com/en/component/kb/article/direct_top_up_method' target='new'>lebih lanjut</a>");
define('TEXT_INFO_DELIVERY_IN_STORE_PICKUP', 'Pungutan di Toko');
define('TEXT_INFO_DELIVERY_IN_STORE_PICKUP_DESCRIPTION', 'Mengumpulkan produk Anda di OffGamers Pudu');
define('TEXT_INFO_DELIVERY_SMS', 'SMS');
define('TEXT_INFO_VIEW_CODE_IN_ACCOUNT_HISTORY', "Kode yang dapat dilihat dalam Sejarah Order OffGamers. <br><a href='http://kb.offgamers.com/en/component/kb/article/user_guide_soft_pins' target='new'>lebih lanjut</a>");
define('TEXT_INFO_MOBILE_PHONE_NUMBER_IS_VALID', '<span class="requiredInfo">*</span> Pastikan nomor ponsel Anda valid untuk tujuan otentikasi. Klik <a href="' . tep_href_link(FILENAME_ACCOUNT_EDIT, '', 'SSL') . '">DISINI</a> untuk mengedit.');

define('ERROR_INVALID_QUANTITY', 'Kuantitas tidak valid');

define('ERROR_INVALID_QUANTITY', 'Kuantitas tidak valid');
define('ENTRY_QTY', 'Kuantitas');
define('ENTRY_DELIVERY_METHOD', 'Metode Pengiriman');

define('TEXT_INFO_DDOS_ATTACH_SUBJECT', 'Pengumuman Terbaru ');
define('TEXT_INFO_DDOS_ATTACH_CONTECT', 'Kami menyarankan semua pengguna kami untuk memeriksa halaman <a href="http://www.facebook.com/OffGamers" style="color:red;text-decoration: underline;">Facebook</a> and <a href="http://twitter.com/#!/offgamers" style="color:red;text-decoration: underline;">Twitter</a> halaman kami untuk perkembangan lebih lanjut dan pembaruan yang berkaitan <br>tentang halaman beranda OffGamers, produk dan layanan. Terima kasih');

define('ERROR_CHECKOUT_QUANTITY_CONTROL_EXCEED', 'Anda telah melebihi batas pembelian yang ditetapkan untuk produk ini.');
define('ERROR_CHECKOUT_QUANTITY_CONTROL_ORDERS', 'Anda telah melebihi batas pembelian yang ditetapkan untuk produk ini. Silakan periksa waktu pengiriman pada produk untuk melihat batas pembelian yang ditetapkan.<br />');

define('TEXT_EXP_CHECKOUT_QTY_NORMAL', '&laquo; Kembali');
define('TEXT_EXP_CHECKOUT_QTY_BULK', 'Klik di sini untuk membeli lebih ');
define('TEXT_EXP_CHECKOUT_BULK_NOT_AVAILABLE', 'Kuantitas yang diminta tidak tersedia. Untuk membeli dalam jumlah besar, silakan hubungi <a href="mailto:<EMAIL>"><EMAIL></a>');

define('COMBO_SELECT_QTY', 'Kuantitas');
define('COMBO_SELECT_DELIVERY_MODE', 'Metode Pengiriman.');
define('WARNING_PRODUCT_PRICE_WITHOUT_GST', 'sebelum %s');

//Telesign phone verification
define('TEXT_CALL_LANGUAGE', 'Bahasa Panggilan:');
define('IMAGE_BUTTON_TEXT_ME_NOW', 'SMS saya Sekarang');
define('TEXT_SMS_NOTES', '"SMS Saya Sekarang" adalah fitur SMS dan hanya untuk pengguna ponsel. Semua pesan teks akan dalam bahasa Inggris.');

define('TEXT_INFO_VERIFYING', 'Verifying');
define('TEXT_INFO_SEARCH_NO_PRODUCT_FOUND', '<b>Tidak ada produk ditemui.</b>Coba pencarian lain atau menelusuri di <a href="' . tep_href_link(FILENAME_SEARCH_ALL_GAMES) . '">Toko</a>.');
define('TEXT_INFO_BROWSE_ALL_RESULTS', 'Menelusuri Hasil');
define('MENU_TITLE_LOGIN_ACCOUNT', 'LOGIN AKUN');
define('MENU_TITLE_REGISTER_ACCOUNT', 'DAFTAR AKUN');
define('MENU_TITLE_CHECKOUT', 'CHECKOUT');
define('MENU_TITLE_BACK_TO_STORE', 'KEMBALI KE TOKO');
define('BUTTON_BROWSE_IN_STORE', 'Menelusuri di toko');
define('BUTTON_ALL_PAYMENT_METHODS', 'Semua cara pembayaran');
define('HEADER_LOGIN_TO_YOUR_ACCOUNT', 'LOGIN KE AKUN ANDA');
define('LOGIN_WITH_FB_TITLE', 'atau login dengan:');
define('BUTTON_READ_MORE_NEWS', 'Baca lebih berita');
define('MENU_HEADER_GROUP_BY_PLATFORM', 'DENGAN PLATFORM');
define('MENU_HEADER_GROUP_BY_PRODUCT_TYPE', 'DENGAN JENIS PRODUK');

define('EMAIL_G2G_BUYBACK_SUBJECT', "New Buyback Order #%s");
define('EMAIL_G2G_BUYBACK_BODY', "Thank you for selling your items to %s.\n\n Supplier Order Summary:\n %s \n");
define('EMAIL_G2G_BUYBACK_ORDER_NUMBER', 'Buyback Order Number: %s');
define('EMAIL_G2G_BUYBACK_DATE_ORDERED', 'Buyback Order Date: %s');
define('EMAIL_G2G_BUYBACK_CUSTOMER_EMAIL', 'E-mail Address: %s');
define('EMAIL_G2G_BUYBACK_PRODUCTS', "\n\n" . 'Products');
define('EMAIL_G2G_BUYBACK_ORDER_TOTAL', 'Total: %s');
define('EMAIL_G2G_BUYBACK_COMMENTS', 'Extra Information:');
define('EMAIL_G2G_BUYBACK_STATUS', 'Status: Pending');
define('EMAIL_G2G_BUYBACK_ORDER_GUIDE', '<b>Order Flow : [Pending] --> [Processing] --> [Completed] or [Canceled]</b><br /><br />[Pending] : Order submission incomplete. Kindly login to OffGamers.com and submit the missing character information.<br />[Processing] : This delivery is being registered into our system.<br />[Completed] : This delivery has been registered completely.<br />[Canceled] : The order has been canceled.<br /><br /><b>Note 1:</b> Please ensure the Account information you provided is 100% accurate.<br /><br /><b>Note 2:</b> Once the Account information been submitted, please do not login to the account, either via the account management page or ingame.<br /><br />Important: Please take note that Blizzard may lock the account if they detect a different IP accessing the account within a short period of time. As a precaution: <br /><br /><b>Note 3:</b> Once your order is in the [Completed] status, payment will be made available to you within 15 minutes at your OffGamers Account. Use the [Withdraw] function to withdraw your payment and have it credited to a payment method of your choosing.');
define('EMAIL_G2G_BUYBACK_ORDER_CLOSING', "Please contact us via our Online Live Support service or e-mail to %s if you face an issue with the buyback. Please remember to include your Buyback Order Number when contacting us to expedite the process.\n\nYou will receive an e-mail notification whenever there is an update in your buyback order status. You can also review your buyback orders and check their status by signing in to OffGamers.com and clicking on \"View Buyback Order History\".\n\nThank you for supporting %s.");

define('TEXT_ERROR_MAX_PENDING_ORDER', 'Anda memiliki %d order yang tersekat, silakan memeriksa status pembayaran pesanan sebelumnya.');
define('EMAIL_MAX_PENDING_ORDER_SUBJECT', 'Customer ID #%d Hit Maximum Pending Order');
define('EMAIL_MAX_PENDING_ORDER_TEXT', 'Customer ID #%d Hit Maximum Pending Order, kindly liaise with customer.');

define('TEXT_DEFAULT_CONTACT_CS_ERROR_MESSAGE','Silakan coba sekali lagi dan jika presist masalah, jangan ragu untuk menghubungi kami dengan mengklik <a href=\'http://support.offgamers.com/support/home\'>di sini</a>.');

define('TEXT_SIGN_UP', 'Daftar');
define('TEXT_LOG_IN', 'login');
define('TEXT_OR_CONNECT', 'atau hubungkan');
    
define('TEXT_MY_OGM', 'MyOffGamers');
define('TEXT_OVERVIEW', 'Ikhtisar');
define('TEXT_BUY_HISTORY', 'History Beli');
define('TEXT_SELL_HISTORY', 'History Jual');
define('TEXT_REQUEST_PAYMENT', 'Permintaan Pembayaran');
    
define('TEXT_ACCOUNT_ID', 'ID akun');
define('TEXT_BUY_STATUS', 'Status Pembeli');
define('TEXT_MANAGE_PROFILE', 'Atur Profil');
define('TEXT_SOCIAL_CONNECT', 'Conect Social');
define('TEXT_STORE_CREDITS', 'Kredit Store');
define('TEXT_WOR', 'OP');

define('TEXT_REGIONAL_TITLE', 'Pengaturan regional untuk kunjungan Anda berikutnya.');
define('BTN_SAVE_CHANGES', 'Simpan');
    
define('TEXT_OGM_DOMAIN', 'OffGamers.com');
define('TEXT_OGM_CAPTION', 'MMO Game CD Keys dan Game Card');
define('TEXT_G2G_DOMAIN', 'G2G.com');
define('TEXT_G2G_CAPTION', 'Gamer2Gamer Marketplace');
define('TEXT_GMZ_DOMAIN', 'Gamernizer.com');
define('TEXT_GMZ_CAPTION', 'Mainkan gratis Multiplayer Browser Game');
    
?>