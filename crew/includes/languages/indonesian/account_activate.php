<?
define('NAVBAR_TITLE_1', '<PERSON>ga<PERSON><PERSON><PERSON>n');
define('NAVBAR_TITLE_2', 'Memverifikasi Email');
define('NAVBAR_TITLE_3', 'Memverifikasi Ponsel');
define('NAVBAR_TITLE_4', 'Memverifikasi Ponsel Order');
define('HEADING_TITLE_1', 'Verifikasi Alamat Email');
define('HEADING_TITLE_2', 'Verifikasi Ponsel');

// Phone Verification
define('TEXT_TITLE_ENTER_PHONE_NUMBER', 'Pastikan bahwa nomor telepon berikut mobile benar dan terjangkau.');
define('ENTRY_CONTACT_NUMBER_EXIST_ERROR', "Kami melihat bahwa nomor ponsel yang Anda berikan telah digunakan. Harap berikan nomor ponsel lain.");

define('TEXT_VERIFY_INSTRUCTION_LINK', 'Apa ini?');
define('TEXT_COUNTRY_NOT_SUPPORT', '(Negara tidak didukung oleh sistem verifikasi ponsel kami)');
define('ERROR_REPORT', 'Laporkan error');
define('TEXT_RESEND_VERIFICATION_CODE', '<a href= "' . tep_href_link(FILENAME_ACCOUNT_ACTIVATE, 'action=resend') . '">Kirim ulang kode konfirmasi</a>');
define('TEXT_CHANGE_PHONE_NUMBER', '(Bukan nomor ponsel Anda? <a id="link_edit_phone_number" href="' . tep_href_link(FILENAME_ACCOUNT_EDIT) . '">Edit Di sini</a>.)');

define('CODE_MATCH_MESSAGE', 'Anda telah berhasil memverifikasi nomor ponsel Anda. Terima kasih.');
define('CODE_NOT_MATCH_RETRY_MESSAGE', 'Kode konfirmasi dimasukkan Anda adalah salah. Silakan coba lagi.');
define('CODE_NOT_MATCH_MESSAGE', 'Kode konfirmasi dimasukkan Anda adalah salah. Kami akan meninjau secara manual order ini secepat mungkin.');
define('REQUEST_REACHED_MAX_ATTEMPT_ERROR', 'Anda telah mencapai percobaan maksimum untuk hari ini. Silakan coba lagi setelah 24 jam.');

define('TABLE_HEADING_COMMENTS', 'Masukkan komentar untuk order diproses');

define('EDIT_TELEPHONE' , '(Edit)');

// Error Report Content
define('EMAIL_TITLE', 'Laporan Error Verifikasi Ponsel');
define('EMAIL_ADMIN_NAME', 'Dukungan Pelanggan');
define('EMAIL_CONTENT_1', '<b>Tanggal & Waktu Laporan:</b>');
define('EMAIL_CONTENT_2', '<b>Nama Pelanggan:</b>');
define('EMAIL_CONTENT_3', '<b>ID Pelanggan:</b>');
define('EMAIL_CONTENT_4', '<b>ID Order:</b>');
define('EMAIL_CONTENT_5', '<b>Tanggal & Waktu Order:</b>');
define('EMAIL_CONTENT_6', '<b>Status Order:</b>');
define('EMAIL_CONTENT_7', '<b>Lokasi Ponsel:</b>');
define('EMAIL_CONTENT_8', '<b>Nomor Ponsel:</b>');

define('EMAIL_REPORT_SEND_MESSAGE', 'Email telah dikirim ke asisten kami.');


// Email Verification
define('ENTRY_COUNTRY_CODE', 'Kode Negara:');
define('ENTRY_VERIFICATION_CODE', 'Kode Verifikasi:');
define('MESSAGE_SUCCESS_MAIL_2', "Email verifikasi telah dikirimkan kembali ke alamat email terdaftar.");
define('MESSAGE_VERIFY_FAIL', 'Verfikasi email gagal.');
define('MESSAGE_EMAIL_VERIFIED', 'Alamat Email Anda telah diverifikasi.');
define('MESSAGE_VERIFIED_SUCCESS', 'Alamat email Anda telah berhasil diverifikasi, Terima kasih.');
define('MESSAGE_VERIFIED_NOT_MATCH', 'Kombinasi alamat email dan kode verifikasi yang dimasukkan tidak valid.');
define('MESSAGE_ERROR_MAIL', 'Gagal memverifikasi email.');

define('MANUAL_VERIFY_EMAIL_1', 'Untuk menyelesaikan proses mengkonfirmasi alamat email Anda, silakan masukkan kode konfirmasi dari email yang dikirim Anda dari OffGamers.');
define('MANUAL_VERIFY_EMAIL_2', 'Silakan masukkan %s kode verifikasi di sini untuk memverifikasi alamat email Anda' . "<br><br>");
define('MANUAL_VERIFY_EMAIL_LOGIN', '');
define('MANUAL_VERIFY_EMAIL_LOGOUT', 'alamat email Anda');

define('MANUAL_VERIFY_EMAIL_LINK', '<a href= "' . tep_href_link(FILENAME_ACCOUNT_ACTIVATE, 'action=resend') . '">' . 'Klik di sini untuk mendapatkan kode verifikasi dikirim ulang ke alamat email Anda.</a>' . "<br><br>");

define('TEXT_REQUEST_VERIFY','%s dan klik "Lanjut" untuk memiliki kode verifikasi dikirim ulang ke alamat email Anda. %s');
define('TEXT_REQUEST_EMAIL_LOGIN_1', 'Silakan periksa apakah alamat email Anda benar');
define('TEXT_REQUEST_EMAIL_LOGOUT_1', 'Silakan masukkan alamat email Anda');
define('TEXT_REQUEST_EMAIL_LOGIN_2', 'Jika alamat email tidak benar, silakan kunjungi halaman edit informasi untuk mengubahnya.');
define('TEXT_REQUEST_EMAIL_LOGOUT_2', '');

// Send email to customer if customer is affiliate with login details when account activation done
define('EMAIL_CUSTOMER_SUBJECT', 'Aktivasi Akun Pelanggan Berhasil');
define('EMAIL_CUSTOMER_CONTENT', 'Anda telah berhasil mengaktifkan ' . STORE_NAME . ' akun pelanggan anda , sedangkan akun afiliasi Anda sedang menunggu untuk mendapatkan persetujuan. Persetujuan afiliasi biasanya memakan waktu kurang dari 12 jam.' . "\n\n");

define('EMAIL_CUSTOMER_LOGIN_DETAILS', 'Sementara itu, Anda dapat login ke ' . STORE_NAME . ' sebagai pelanggan menggunakan berikut:' . "\n");
define('EMAIL_LOGIN_ADDRESS', 'Alamat Login: <a href= "' . tep_href_link(FILENAME_LOGIN) . '">'. tep_href_link(FILENAME_LOGIN) .'</a>');
define('EMAIL_ADDRESS', "\n". 'Alamat Email: ');
define('EMAIL_CUSTOMER_PASSWORD', "\n". 'Sandi: ');
define('EMAIL_CUSTOMER_PASSWORD_CHANGE', "\n\n". 'Silakan mengubah Sandi Anda setelah Anda login. Perhatikan bahwa login afiliasi Anda akan sama sebagai login pelanggan Anda.');

define('IMAGE_BUTTON_CALL_ME_NOW', 'Telepon Saya Sekarang');
?>
