<?
/*
  	$Id: account_edit.php,v 1.3 2013/06/07 09:57:07 chingyen Exp $

  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com

  	Copyright (c) 2003 osCommerce

  	Released under the GNU General Public License
*/

define('NAVBAR_TITLE_1', '<PERSON><PERSON><PERSON>');
define('NAVBAR_TITLE_2', 'Edit Akun');

define('HEADING_TITLE', 'Edit Profil');
define('MY_ACCOUNT_TITLE', '<PERSON>ku<PERSON>');

define('TITLE_LOGIN_EMAIL_PASSWORD', "EMAIL LOGIN &amp; SANDI");
define('TITLE_PERSONAL_DETAILS', "RINCIAN PRIBADI");
define('TITLE_BILLING_ADDRESS_FOR_CREDIT_CARD', "BUKU ALAMAT (ALAMAT PENAGIHAN UNTUK PEMBAYARAN KARTU KREDIT)");

define('ENTRY_COUNTRY_CODE', 'Kode Negara:&nbsp;');
define('ENTRY_COUNTRY_CODE_TEXT', '*');
define('ENTRY_CURRENT_CONTACT_NUMBER', 'Nomor Ponsel Sekarang:&nbsp;');
define('ENTRY_CONTACT_NUMBER', 'Nomor Ponsel:&nbsp;');
define('ENTRY_CONTACT_NUMBER_TEXT', '*');
define('ENTRY_INSTANT_MESSENGER', 'Instant Messenger&nbsp;(IM):');
define('ENTRY_BILLING_ADDRESS1', 'Alamat1:&nbsp;');
define('ENTRY_BILLING_ADDRESS2', 'Alamat2:&nbsp;');
define('ENTRY_NEW_COUNTRY_CODE', 'Kode Negara Baru:&nbsp;');
define('ENTRY_NEW_CONTACT_NUMBER', 'Nomor Ponsel Baru:&nbsp;');

define('ENTRY_PASSWORD_NEW_CONFIRMATION', 'Confirm New Password:');
define('ENTRY_PASSWORD_NEW_CONFIRMATION_TEXT', '*');

define('ENTRY_PASSWORD_CURRENT_NOT_MATCH_ERROR', 'Sandi Sekarang Anda tidak benar.');
define('ENTRY_PASSWORD_NEW_ERROR', "Sandi baru Anda harus berisi minimal %d karakter.");
define('ENTRY_PASSWORD_NEW_NOT_MATCH_ERROR', 'Sandi Konfirmasi Anda harus cocok Sandi Baru Anda.');
define('ENTRY_PASSWORD_CONFIRMATION_ERROR', "Sandi Konfirmasi Anda harus berisi minimal %d karakter.");
define('ENTRY_CONTACT_NUMBER_ERROR', "Nomor ponsel Anda harus berisi minimal %d angka.");
define('ENTRY_CONTACT_NUMBER_EXIST_ERROR', "Kami melihat bahwa nomor ponsel yang Anda berikan telah digunakan atau tidak valid. Mohon berikan nomor ponsel lain.");

define('ENTRY_EMAIL_JS_ERROR', "Email yang diberikan tidak valid.");
define('ENTRY_FIRSTNAME_JS_ERROR', "Nama depan harus berisi minimal %d karakter.");
define('ENTRY_LASTNAME_JS_ERROR', "Nama belakang harus berisi minimal %d karakter.");
define('ENTRY_PASSWORD_CURRENT_JS_ERROR', "Sandi Konformasi harus berisi minimal %d karakter dan hanya dapat berisi huruf dan angka.");
define('ENTRY_PASSWORD_JS_ERROR', "Sandi harus berisi minimal %d karakter dan hanya dapat berisi huruf dan angka.");
define('ENTRY_PASSWORD_CONFIRMATION_JS_ERROR', "Confirm Password must be at least %d characters and can only contains letter and number.");
define('ENTRY_PASSWORD_CONFIRMATION_NOT_MATCH_JS_ERROR', "New Password does not match with Confirm Password.");
define('ENTRY_CONTACT_NUMBER_JS_ERROR', "Nomor ponsel Anda tidak valid");
define('ENTRY_DOB_JS_ERROR', "Tanggal lahir Anda tidak valid.");
define('ENTRY_SECRET_QUESTION_JS_ERROR', "Anda harus memilih pertanyaan rahasia dari menu turun yang tersedia.");
define('ENTRY_ANSWER_JS_ERROR', "Jawaban Anda harus berisi minimal %d karakter.");
define('ENTRY_SIGNUP_TERMSANDCONDITION_JS_ERROR', "Anda harus menyetujui Persyaratan Layanan & Kebijakan Privasi");

define('ENTRY_EMAIL_NOTICE', "Harap gunakan alamat email yang valid karena kami perlu email Anda untuk mengkonfirmasi akun Anda.");
define('ENTRY_FIRSTNAME_NOTICE', "Silakan gunakan nama depan seperti ditampilkan di ID Anda.");
define('ENTRY_LASTNAME_NOTICE', "Silakan gunakan nama belakang seperti ditampilkan di ID Anda.");
define('ENTRY_PASSWORD_NOTICE', "Sandi Anda harus berisi minimal %d karakter panjang dan itu adalah peka huruf.");
define('ENTRY_CONFIRM_PASSWORD_NOTICE', "Ketikkan Sandi Anda lagi untuk keperluan verifikasi.");
define('ENTRY_CONTACTNUMBER_NOTICE', "Harap memberikan nomor ponsel yang valid. Ini akan digunakan untuk keperluan verifikasi dan juga untuk me-reset Pertanyaan & Jawaban Rahasia Anda jika Anda telah terlupa.");
define('ENTRY_ANSWER_NOTICE', "Untuk memastikan keamanan akun Anda, pertanyaan rahasia Anda dan jawabannya akan digunakan untuk keperluan verifikasi ketika mengedit, pembaruan info dan juga untuk penarikan dana dan tidak peka huruf.");
define('ENTRY_IM_NOTICE', "Jika Anda memberitahu kami alamat IM Anda, kami akan menggunakan metode ini untuk menghubungi Anda untuk pengiriman jika perlu.");
define('ENTRY_DOB_NOTICE', "Kami meminta untuk alasan hukum.");

define('SUCCESS_ACCOUNT_UPDATED', 'Akun Anda telah berhasil diperbarui.');
define('SUCCESS_ACCOUNT_MOBILE_UPDATED', 'Nomor ponsel Anda telah berhasil diubah.');
define('CHANGE_ACCOUNT_SETTING', 'Silakan hubungi <a href="mailto:'.EMAIL_TO.'">'.EMAIL_TO.'</a> untuk mengedit Nama Pertama, Nama Belakang dan Gender.');
define('ERROR_EMAIL_EXISTED', 'Alamat email yang Anda masukkan ada dalam catatan kami. Silakan email kami di <a href="' . tep_href_link(FILENAME_CONTACT_US) . '">pemilik toko</a> jika Anda ingin menggabungkan akun.');

define('ENTRY_TELEPHONENUMBER_NOTICE', 'Harap memberikan nomor telepon yang valid. Nomor telepon Anda akan digunakan untuk keperluan verifikasi dan juga untuk reset Pertanyaan & Jawaban Rahasia Anda jika Anda telah terlupa.');
define('ENTRY_SECRETQNA_NOTICE', 'Silakan mengisi kolom jawaban untuk pertanyaan rahasia Anda untuk melanjutkan pembaruan informasi akun Anda.');

define('MANUAL_ACTIVATE_ACCOUNT','%s Silakan periksa e-mail akun Anda %s untuk kode aktivasi.  Anda hanya dapat masuk untuk menikmati hak istimewa anggota Anda setelah mengaktifkan akun Anda. E-mail dapat muncul dalam email spam atau folder junk mail. Silakan hubungi kami di <a href="mailto:'.EMAIL_TO.'">'.EMAIL_TO.'</a> jika Anda mengalami kesulitan menerima aktivasi e-mail. <br><br>Silakan masuk %s kode aktivasi di sini untuk mengaktifkan akun Anda. Anda akan diarahkan ke halaman log masuk setelah berhasil mengaktifkan akun Anda.' . "<br><br>");
define('MANUAL_ACTIVATE_ACCOUNT_REG_1', 'Akun Anda telah berhasil dibuat. ');
define('MANUAL_ACTIVATE_ACCOUNT_REG_1', 'Akun Anda telah berhasil dibuat. ');
define('MANUAL_ACTIVATE_ACCOUNT_REG_2', 'segera');
define('MANUAL_ACTIVATE_ACCOUNT_REG_3', '');
define('MANUAL_ACTIVATE_EMAIL_EXIST_LINK', 'email=');

define('EMAIL_ACCOUNT_EDIT_PASSWORD_CHANGE_SUBJECT', 'Sandi telah diubah');
define('EMAIL_ACCOUNT_EDIT_PASSWORD_CHANGE', 'Pelanggan Yang Terhormat,' . "\n\n" . 'Ini adalah untuk memberitahukan Anda bahwa sandi Anda telah diubah.' . "\n\n" . 'Tolong hubungi live support kami atau <NAME_EMAIL> jika Anda berpikir seseorang memiliki akses ke alamat email yang terkait dengan akun OffGamers Anda. Mohon juga mengubah password untuk email Anda.');
?>