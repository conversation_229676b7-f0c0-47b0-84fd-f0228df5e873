<?
/*
  	$Id: account_payment_edit.php,v 1.2 2013/06/05 09:43:12 chingyen Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2003 osCommerce
	
  	Released under the GNU General Public License
*/
define('NAVBAR_TITLE_1', BOX_HEADING_LOGIN_BOX_MY_ACCOUNT);
define('NAVBAR_TITLE_2', LOGIN_BOX_ACCOUNT_PAYMENT_EDIT);

if ($action == 'add_pm') {
	define('HEADING_TITLE', 'Tambah Cara Pembayaran ');
} else if ($action == 'edit_pm') {
	define('HEADING_TITLE', 'Edit Cara Pem<PERSON>aran');
} else {
	define('HEADING_TITLE', LOGIN_BOX_ACCOUNT_PAYMENT_EDIT);
}

define('TABLE_HEADING_PM_PAYMENT_ACCOUNT', '<PERSON><PERSON><PERSON>');
define('TEXT_CONFIRM_DELETE', '<PERSON><PERSON>h Anda ingin menghapus?');
define('TEXT_CONFIRM_DELETE_RECORD', 'catatkan?');

define('TEXT_BUYBACK_LINK', 'Jual Mata Uang game Anda');
define('TEXT_ANNOUNCEMENT', '<span style="color: red">*Penting*</span> Mohon dicatat bahwa efektif mulai 17 November 2010, pemasok akan tidak dapat mengguna Western Union. Oleh karena itu kami sangat menyarankan pemasok untuk beralih ke Paypal, Wire Transfer atau Moneybookers sebagai metode penggantian Anda. ntuk informasi lebih lanjut, silakan lihat <a href="http://space.offgamers.com/en/blogs/official-blogs/402-november-2010/4146-western-union-removed-as-disbursement-method" target="_blank">Di Sini</a>.<p></p><span style="color: red">*Penting*</span> Mohon dicatat bahwa efektif mulai 27 Oktober 2010, pemasok akan digabung menjadi PayPal baru dan revisi (dalam USD) sebagai metode penarikan secara otomatis.  Paypal  versi baru (dalam USD) akan menghasilkan biaya revisi yang akan lebih rendah daripada versi lama Paypal (dalam USD). Untuk informasi lebih lanjut, silakan lihat  <a href="my_payment_history.php?action=show_balance#paypal">Di Sini</a>.');

//For payment module------------------------------------------------------------
define('TEXT_ALL_PAGES', 'Semua Halaman');
define('HEADER_FORM_ACC_STAT_TITLE', 'Pernyataan Akun');
define('ENTRY_ORDER_START_DATE', 'Tanggal Mula<br><small>(YYYY-MM-DD)</small>');
define('ENTRY_ORDER_END_DATE', 'Tanggal Akhir<br><small>(YYYY-MM-DD)</small>');
define('ENTRY_PAYMENT_ID', 'ID Pembayaran');
define('ENTRY_ORDER_ID', 'ID Order');
define('ENTRY_RECORDS_PER_PAGE', 'Catatan per halaman');
define('ENTRY_PAYMENT_STATUS', 'Status Pembayaran');
define('TEXT_LANG_RECORDS_PER_PAGE', '');
define('TABLE_HEADING_ACC_STAT_DATETIME', 'Tanggal/Waktu');
define('TABLE_HEADING_ACC_STAT_ACTIVITY', 'Aktivitas');
define('TABLE_HEADING_ACC_STAT_PAYMENT_STATUS', 'Status Pembayaran');
define('TABLE_HEADING_ACC_STAT_DEBIT', 'Debet');
define('TABLE_HEADING_ACC_STAT_CREDIT', 'Kredit');
define('TABLE_HEADING_ACC_STAT_BALANCE', 'Saldo');
define('TEXT_ACC_STAT_NOT_APPLICABLE', '-');
define('TEXT_ACC_STAT_PAYMENT_STATUS_UPDATE_DATE', '%s di %s');
define('TEXT_ACC_STAT_ADMIN_COMMENT', 'Komentar:');
define('SECTION_TITLE_FORM_PM_REQUIRED_INFO', 'Informasi diperlukan:');
define('TEXT_PM_NO_EXISTING_PAYMENT_ACCOUNT', 'Anda memiliki 0 akun pembayaran dalam catatan.<br><br>ika Anda berniat untuk menjual kepada OffGamers, silakan klik pada TAMBAHKAN METODE untuk membuat akun pembayaran baru .');
define('LINK_PM_EDIT_PM_ACCOUNT', 'Edit Akun Pembayaran');
define('LINK_PM_DELETE_PM_ACCOUNT', 'Hapus Akun Pembayaran');
define('SUCCESS_PM_DELETE_PM_ACCOUNT', 'Sukses: Pembayaran akun telah berhasil dihapus.');
define('ERROR_INVALID_PM_BOOK_SELECTION', 'Error: Anda megedit akun pembayaran yang tidak valid.');
define('ERROR_INVALID_PM_SELECTION', 'Error: Silakan pilih metode pembayaran yang valid.');
define('ERROR_EMPTY_PM_ALIAS', 'Error: Judul Pembayaran diperlukan .');
define('ERROR_PM_FIELD_INFO_REQUIRED', 'Error: Mohon berikan informasi "%s"!');
define('ERROR_PM_ACCOUNT_BOOK_FULL', 'Buku akun Pembayaran Anda penuh.. Silakan menghapus akun yang tidak dibutuhkan untuk menyimpan yang baru.');
define('ENTRY_FORM_PM_SELECT_CURRENCY', 'Mata Uang Pembayaran:');
define('ENTRY_FORM_PM_SELECT_PM', 'Cara Pembayaran:');
define('ENTRY_FORM_PM_ALIAS', 'Deskrepsi Pembayaran:');

// Edit Payment Account Information
define('TABLE_HEADING_PAYMENT_ACCOUNT', 'AKUN PEMBAYARAN');
define('TABLE_HEADING_PAYMENT_INFO', 'RINCIAN AKUN PEMBAYARAN');
define('TABLE_HEADING_PAYMENT_ACTION', 'AKSI');
define('TEXT_PM_REACHED_MAX', 'Anda telah mencapai %d metode penyaluran maksimum.<br>Anda harus menghapus beberapa menambahkan lebih banyak.');
//--end for payment module--------------------------------------------------------
?>