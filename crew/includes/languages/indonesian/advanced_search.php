<?php
/*
  $Id: advanced_search.php,v 1.2 2013/06/05 09:43:12 chingyen Exp $
*/

define('NAVBAR_TITLE_1', 'Pencarian Lanjutan');
define('NAVBAR_TITLE_2', '<PERSON>il Pencarian');

define('HEADING_TITLE', 'Pencarian Lanjutan');
define('HEADING_TITLE_1', 'Pencarian Lanjutan');
define('HEADING_TITLE_2', 'Produk yang memenuhi kriteria pencarian');

define('HEADING_SEARCH_CRITERIA', 'Kata Kunci');

define('TEXT_SEARCH_IN_DESCRIPTION', 'Termasuk Deskripsi Produk ');
define('ENTRY_CATEGORIES', 'Kategori:');
define('ENTRY_INCLUDE_SUBCATEGORIES', 'Termasuk Sub-Kategori');
define('ENTRY_MANUFACTURERS', 'Manufaktur:');
define('ENTRY_PRICE_FROM', '<PERSON><PERSON>i:');
define('ENTRY_PRICE_TO', '<PERSON><PERSON>:');
define('ENTRY_DATE_FROM', 'Tanggal Dari:');
define('ENTRY_DATE_TO', 'Tanggal Ke:');
define('ENTRY_SORT_BY', 'Mengurut Dengan:');
define('ENTRY_ITEM_PER_PAGE', 'Barang per Halaman:');
define('ENTRY_ASCENDING', 'Urutan Menaik');
define('ENTRY_DESCENDING', 'Urutan Menurun');

define('TEXT_SEARCH_HELP_LINK', 'Bantuan [?]');

//define('TEXT_ALL_CATEGORIES', 'Semua Kategori ');
define('TEXT_ALL_MANUFACTURERS', 'Semua Manufaktor');

define('HEADING_SEARCH_HELP', 'Bantuan Pencarian');
define('TEXT_SEARCH_HELP', 'Kata kunci dapat dipisahkan dengan AND dan / atau OR untuk hasil pencarian lebih akuratnya.<br><br>Sebagai contoh, <u>Microsoft AND mouse</u> akan menampilkan pencarian yang mengandung kedua kata tersebut. Namun, untuk<u>mouse OR keyboard</u>, set hasil kembali akan mengandung kedua atau salah satu kata.<br><br>Hasil yang cocok dapat dicari dengan melampirkan kata kunci dalam tanda kutip ganda.<br><br>Misalnya, <u>"notebook computer"</u> w akan menghasilkan serangkaian hasil yang sama persis dengan kata.<br><br>Kurungan dapat digunakan untuk kontrol lebih lanjut tentang set hasil.<br><br>Misalnya, <u>Microsoft and (keyboard or mouse or "visual basic")</u>.');
define('TEXT_CLOSE_WINDOW', 'Tutup Jendela [x]');
define('TEXT_ITEM_ADDED_TO_CART', 'Barang telah ditambahkan ke <a href="shopping_cart.php">troli belanja.</a>');

define('TABLE_HEADING_IMAGE', '');
define('TABLE_HEADING_MODEL', 'Model');
define('TABLE_HEADING_PRODUCTS', 'Nama Produk ');
define('TABLE_HEADING_MANUFACTURER', 'Manufaktur');
define('TABLE_HEADING_QUANTITY', 'Kuantitas');
define('TABLE_HEADING_PRICE', 'Harga');
define('TABLE_HEADING_WEIGHT', 'Berat');
define('TABLE_HEADING_BUY_NOW', 'Beli Sekarang');
define('TABLE_HEADING_PRODUCT_SORT', 'Urutan');

define('TEXT_NO_PRODUCTS', 'Tidak ada produk yang cocok dengan kriteria pencarian .');

define('ERROR_AT_LEAST_ONE_INPUT', 'Setidaknya salah satu kolom dalam bentuk pencarian harus dimasukkan.');
define('ERROR_INVALID_FROM_DATE', 'Tanggal Dari yang tidak valid.');
define('ERROR_INVALID_TO_DATE', 'Tanggal Ke yang tidak valid.');
define('ERROR_TO_DATE_LESS_THAN_FROM_DATE', 'Tanggal Dari harus lebih besar dari atau sama dengan Tanggal Ke.');
define('ERROR_PRICE_FROM_MUST_BE_NUM', 'Harga Dari harus berupa angka.');
define('ERROR_PRICE_TO_MUST_BE_NUM', 'Harga Ke harus berupa angka.');
define('ERROR_PRICE_TO_LESS_THAN_PRICE_FROM', 'Harga Dari harus lebih besar dari atau sama dengan Harga Ke.');
define('ERROR_INVALID_KEYWORDS', 'Kata Kunci yang tidak valid.');
?>
