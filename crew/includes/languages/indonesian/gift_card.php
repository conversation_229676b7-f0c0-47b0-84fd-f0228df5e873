<?php
/*
  $Id: gift_card.php,v 1.3 2014/06/25 10:17:16 weesiong Exp $
  
  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2003 osCommerce

  Released under the GNU General Public License
*/

define('NAVBAR_GC_TITLE', 'Kartu Hadiah OffGamers');
define('TAB_HEADING_1', 'BELI DENGAN KARTU HADIAH');
define('TAB_HEADING_2', 'MENEBUS KARTU HADIAH');
define('TAB_HEADING_3', 'MEMBELI KARTU HADIAH');

define('HEADING_STEP_1_TITLE', 'LANGKAH 1: Info Kartu Hadiah');
define('HEADING_STEP_2_TITLE','LANGKAH 2: Login atau Daftar');
define('HEADING_PWGC_STEP_2_TITLE','LANGKAH 2: Beli');
define('HEADING_PWGC_STEP_3_TITLE','LANGKAH 3: Login atau Daftar');

define('EMAIL_CHANGE_YOUR_PASSWORD_BODY', '<PERSON>iah untuk menjadi seorang anggota ' . STORE_NAME . ' member. You have registered ' . STORE_NAME . ' akun baru dengan menggunakan alamat email.'."\n".'Berikut adalah rincian akun Anda:'."\n\n".'Email: <b>%s</b>'."\n".'Sandi: <b>%s</b>'."\n\n".'Untuk tujuan keamanan, kami sarankan Anda untuk mengubah sandi login Anda. Ikuti taut di bawah ini:'."\n".'<a href="%s>Ubah sandi login saya</a>'."\n\n".'Untuk sebarang pertanyaan atau bantuan, silakan guna layanan Online Live Support kami atau email pertanyaan Anda ke <a href="mailto:' . EMAIL_TO . '">'.EMAIL_TO.'</a>.');
define('EMAIL_CHANGE_YOUR_PASSWORD_SUBJECT', 'Selamat Datang ke ' . STORE_NAME);
define('EMAIL_FOOTER', STORE_EMAIL_SIGNATURE);

define('USER_MEMBER_STATUS_NOT_ALLOW', 'Status anggota Anda tidak memungkinkan untuk menebus kartu hadiah. Silakan hubungi <a target="_blank" href="mailto:<EMAIL>"><EMAIL></a>.');
define('USER_GIFTCARD_ERROR', 'Nomor Seri atau Kode dimasukkan tidak cocok. Silakan coba lagi. Jika masalah ini berlanjut, silakan hubungi kami di <a target="_blank" href="mailto:<EMAIL>"><EMAIL></a>.');
define('USER_GIFTCARD_DEACTIVATED', 'Kode ini sudah digunakan. Jika Anda telah membeli kode ini, silakan hubungi kami di <a target="_blank" href="mailto:<EMAIL>"><EMAIL></a>.');
define('USER_GIFTCARD_REDEEMED', 'Kode ini sudah digunakan. Jika Anda telah membeli kode ini, silakan hubungi kami di <a target="_blank" href="mailto:<EMAIL>"><EMAIL></a>.');
define('USER_GIFTCARD_UNKNOWN_ERROR', 'Silakan isikan Kode lagi. Jika Anda masih menerima pesan kesalahan yang sama, silakan hubungi kami di <a target="_blank" href="mailto:<EMAIL>"><EMAIL></a>.');
define('USER_GIFTCARD_INFO_MISSING', 'Masukkan Nomor Seri / Kode Pin.');
define('USER_REDEEM_GIFTCARD_FAILURE', 'Silakan coba lagi dan klik pada tombol Menebus sekali lagi. Jika masalah ini berlanjut, silakan hubungi kami di  <a target="_blank" href="mailto:<EMAIL>"><EMAIL></a>.');
define('USER_GIFTCARD_CURRENCY_INVALID', 'Mohon maaf, mata uang Gift Card: AED, SAR, KWD, PHP, JPY and HKD tidak lagi didukung di toko kami. Jika ada pertanyaan silakan hubungi kami di <a target="_blank" href="mailto:<EMAIL>"><EMAIL></a>.');

define('DF_TEXT_GIFT_CARD_VALUE', 'Nilai Kartu Hadiah: %s');
define('DF_TEXT_REDEEM_GIFT_CARD_CONFIRMATION', 'Nilai Kartu Hadiah: %s (ke mata uang kredit toko: %s)');

define('TEXT_GC_QUANTITY', "Kuantitas");
define('TEXT_SERIAL_NO', "Nomor Seri");
define('TEXT_PIN_CODE', "Kode PIN");
define('TEXT_TOTAL_VALUE', 'Jumlah Nilai');
define('TEXT_LOGIN_PASSWORD', 'Sandi Login Saya');
define('TEXT_NAME', 'Nama Saya');
define('TEXT_EMAIL', 'Email Saya');
define('TEXT_GC_WELCOME_BACK', 'Selamat kembali <b>%1$s</b> (Bukan %1$s? <a href="javascript:void(0);" onclick="%2$s">Logout sini</a>)');
define('TEXT_GC_PC', ' pc');
define('TEXT_GC_MANAY_PC', 's');
define('TEXT_PRODUCT', 'Produk');
define('TEXT_PURCHASE_FOR_MYSELF', 'Untuk saya');
define('TEXT_PURCHASE_AS_GIFT', 'Sebagai hadiah');
define('TEXT_PURCHASE_WITH_GIFT_CARD_CONFIRMATION', 'Apakah Anda mengkonfirmasi pembelian ini dan membayar dengan Kartu Hadiah OffGamers Anda?');
define('TEXT_SELECT', 'Pilih');
define('TEXT_IS_EXISTING_CUSTOMER', 'Pelanggan lama');
define('TEXT_IS_NEW_CUSTOMER', 'Saya pelanggan baru');

define('TEXT_GC_SUBHEADER', 'HADIAH SEMPURNA UNTUK GAMERS SERUPA');
define('TEXT_GC_SUB_DESC', 'Solusi Kartu Hadiah OffGamers dirancang untuk menggantikan sertifikat hadiah tradisional dan menawarkan kenyamanan yang sangat baik untuk semua pengguna. Kemudahan memberikan hadiah kepada orang yang Anda cintai, teman dan kolega, dapat dicapai langsung dari ujung jari Anda. Dengan pemesanan langsung online, menebus hadiah online akan menjadi cara yang ideal untuk semua orang.');

// FAQ
define('TEXT_GC_FAQ_AWARD_TITLE', 'Penghargaan Kartu Hadiah OffGamers');
define('TEXT_GC_FAQ_H1', 'Apakah itu Kartu Hadiah OffGamers');
define('TEXT_GC_FAQ_C1', 'Kartu Hadiah OffGamers dirancang untuk menggantikan sertifikat hadiah tradisional dan menawarkan kenyamanan yang sangat baik untuk semua pengguna. Kemudahan memberikan hadiah kepada orang yang Anda cintai, teman dan kolega, dapat dicapai langsung dari ujung jari Anda.');
define('TEXT_GC_FAQ_H2', 'Mengapa menggunakan Kartu Hadiah OffGamers?');
define('TEXT_GC_FAQ_C2', 'Kartu Hadiah memungkinkan setiap orang untuk berbelanja di Toko OffGamers 24/7, 365 hari, kapan saja dan di mana saja. Kartu hadiah adalah hadiah yang sempurna bagi siapa pun, karena ini akan memungkinkan penerima untuk memilih apa yang mereka inginkan. Dengan pemesanan langsung online, hadiah menebus online akan menjadi cara yang ideal untuk semua orang.');
define('TEXT_GC_FAQ_H3', 'Bagaimana untuk membeli Kartu Hadiah OffGamers?');
define('TEXT_GC_FAQ_C3', 'Masuk ke akun OffGamers Anda terlebih dahulu. Pilih pada tab "Beli Kartu Hadiah" atau <a href="'.tep_href_link(FILENAME_GIFT_CARD, 'action=purchase_gc', 'SSL').'">klik di sini</a> untuk menavigasi ke bagian kartu hadiah OffGamers. Pilih denominasi kartu hadiah yang Anda ingin memperoleh dan memilih cara pembayaran. Isikan rincian Anda dan informasi penerima yang Anda ingin mengirim hadiah ke dan klik pada "Lanjutkan Checkout" untuk menyelesaikan transaksi.');

// Display Message
define('SUCCESS_REDEEMED', 'Anda telah berhasil menebus Kartu Hadiah Anda!');
define('SUCCESS_CREDITED', 'Akun Anda telah dikreditkan Kredit Toko %s.');
define('SUCCESS_PURCHASE_WITH_GIFT_CARD', 'Tahniah! Order Anda telah selesai.');
define('SUCCESS_PURCHASE_WITH_GIFT_CARD_ORDER_INFO', 'Nomor order Anda adalah <b>%s</b>');
define('SUCCESS_CREDIT_BALANCE', 'Saldo Kredit Toko Anda: <b>%s</b>');
define('SUCCESS_CREDIT_BALANCE_DESC', 'Saldo dari Kartu Hadiah OffGamers telah dikreditkan <br> sebagai Kredit Toko ke Akun Anda.');

define('ERROR_NOT_ENOUGH_CREDIT', 'Harap dicatat bahwa jumlah Kartu Hadiah OffGamers Anda tidak cukup untuk membeli produk ini. Mohon memilih nilai atau kuantitas yang lebih rendah, atau mendapatkan Kartu Hadiah OffGamers online di <a href="'.tep_href_link(FILENAME_GIFT_CARD, 'action=purchase_gc', 'SSL').'">sini</a>.');
define('ERROR_NOT_ENOUGH_CREDIT_AFTER_QTY', 'Harap dicatat bahwa jumlah Kartu Hadiah OffGamers Anda tidak cukup untuk membeli produk ini. Mohon memilih nilai atau kuantitas yang lebih rendah. Jika Anda memiliki kredit toko yang cukup dalam akun Anda, jumlah kredit toko akan digunakan untuk mengimbangi perbedaan.');
define('ERROR_INVALID_FORM_SUBMIT', 'Kesalahan telah terjadi yang membutuhkan perhatian Anda, ketika Anda mengisi salah satu kolom . Pastikan semua kolom diisi adalah benar.');

// Button
define('BUTTON_REDEEM', "Menebus");
define('BUTTON_VIEW_ORDER', 'Lihat order');
define('BUTTON_VIEW_SC_BALANCE', 'Lihat saldo Kredit Toko');
define('BUTTON_CHECK_OP_BALANCE', 'Periksa saldo OP Anda');
define('BUTTON_BROWSE_MORE_GAMES', 'Menelusuri lebih game');
define('BUTTON_CONFIRM_CHECKOUT', 'Konfirm Checkout');
?>