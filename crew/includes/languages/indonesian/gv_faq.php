<?php
/*
  $Id: gv_faq.php,v 1.1 2013/05/27 10:03:34 weichen Exp $

  The Exchange Project - Community Made Shopping!
  http://www.theexchangeproject.org

  Gift Voucher System v1.0
  Copyright (c) 2001,2002 <PERSON>
  http://www.phesis.org

  Released under the GNU General Public License
*/

define('NAVBAR_TITLE', 'FAQ Voucher Hadiah');
define('HEADING_TITLE', 'FAQ Voucher Hadiah');

define('TEXT_INFORMATION', '<a name="Top"></a>
  <a href="'.tep_href_link(FILENAME_GV_FAQ,'faq_item=1','NONSSL').'">Pembelian Voucher Hadiah</a><br>
  <a href="'.tep_href_link(FILENAME_GV_FAQ,'faq_item=2','NONSSL').'">Cara Mengirim Voucher Hadiah</a><br>
  <a href="'.tep_href_link(FILENAME_GV_FAQ,'faq_item=3','NONSSL').'">Membeli dengan Voucher Hadiah</a><br>
  <a href="'.tep_href_link(FILENAME_GV_FAQ,'faq_item=4','NONSSL').'">Menebus Voucher Hadiah</a><br>
  <a href="'.tep_href_link(FILENAME_GV_FAQ,'faq_item=5','NONSSL').'">Ketika Masalah Terjadi</a><br>
');
switch ($HTTP_GET_VARS['faq_item']) {
  case '1':
define('SUB_HEADING_TITLE','Pembelian Voucher Hadiah.');
define('SUB_HEADING_TEXT',' Voucher Hadiah yang dibeli seperti barang lainnya di toko kami. Anda dapat 
  bayar  bagi mereka menggunakan toko metode pembayaran biasa..
  Setelah dibeli nilai dari Voucher Hadiah akan ditambahkan ke Akun Voucher Hadiah 
  pribadi Anda. Jika Anda memiliki dana dalam Akun Voucher Hadiah Anda, Anda akan 
  dapat melihat bahwa jumlah sekarang ditunjukkan dalam kotak Troli Belanja, dan juga 
  menyediakan taut ke halaman di mana Anda dapat mengirim Voucher Hadiah kepada 
  seseorang melalui email.');
  break;
  case '2':
define('SUB_HEADING_TITLE','Cara Mengirim Voucher Hadiah.');
define('SUB_HEADING_TEXT','Untuk mengirim Voucher Hadiah Anda harus pergi ke halaman 
  Kirim Voucher Hadiah kami. Anda dapat menemukan taut ke halaman ini dalam kotak Troli 
  Belanja pada bagian sisi kanan dari setiap halaman.
  Ketika Anda mengirim sebuah Voucher Hadiah, Anda perlu menentukan informasi berikut.
  Nama orang yang Anda kirimkan Voucher Hadiah.
  Alamat email dari orang yang Anda kirimkan Voucher Hadiah.
  Jumlah yang Anda ingin mengirim. (Catatan Anda tidak perlu mengirim seluruh jumlah
  yang berada dalam Akun Voucher Hadiah Anda.)
  Sebuah pesan singkat yang akan muncul dalam email.
  Mohon pastikan bahwa Anda telah memasukkan semua informasi dengan benar,
  meskipun Anda akan diberikan kesempatan untuk mengubahnya sebanyak yang Anda inginkan 
  sebelum email tersebut dikirimkan.');  
  break;
  case '3':
  define('SUB_HEADING_TITLE','Membeli dengan Voucher Hadiah.');
  define('SUB_HEADING_TEXT','Jika Anda memiliki dana dalam Akun Voucher Hadiah Anda, Anda 
  dapat menggunakan dana tersebut untuk membeli barang-barang lainnya di toko kami. Dalam 
  proses checkout, sebuah kotak tambahan akan muncul. Mencentang kotak ini akan menerapkan 
  dana dalam Akun Voucher Hadiah Anda. Harap diberitahu bahwa Anda masih akan harus memilih 
  cara pembayaran lainnya jika tidak ada cukup dana dalam Akun Voucher Hadiah Anda untuk 
  membayar biaya pembelian Anda. Jika Anda memiliki dana dalam Akun Voucher Hadiah Anda 
  daripada total biaya pembelian Anda maka saldo akan ditinggalkan di Akun Voucher Hadiah 
  Anda untuk pembelian masa depan .');
  break;
  case '4':
  define('SUB_HEADING_TITLE','Menebus Voucher Hadiah.');
  define('SUB_HEADING_TEXT','Jika Anda menerima Voucher Hadiah melalui email itu akan 
  berisi rincian yang mengirimkan Voucher Hadiah, bersama dengan sebuah pesan singkat 
  dari mereka. ni mungkin idea yang baik untuk mencetak email ini untuk referensi di 
  masa mendatang. Sekarang Anda dapat menebus Voucher Hadiah dalam dua cara.<br>
  1. Dengan mengklik taut dalam email mengandung untuk tujuan ini mengungkapkan. Ini akan 
  membawa Anda ke toko Gunakan halaman Voucher. Anda akan yang diminta untuk membuat akun, 
  sebelum Kupon Hadiah tersebut divalidasi dan ditempatkan pada akun Kupon Hadiah Anda dan 
  siap untuk Anda belanjakan produk apa saja yang Anda inginkan.<br>
  2. Ketika pemroses checkout, pada halaman yang sama bahwa Anda memilih metode pembayaran 
  akan ada kotak untuk memasukkan Kode Gunakan. Masukkan kode di sini, dan klik tombol 
  Menebus. Kode akan divalidasi dan ditambahkan ke akun Kupon Hadiah Anda. Anda kemudian 
  dapat menggunakan jumlah tersebut untuk membeli suatu barang dari toko kami ');
  break;
  case '5':
  define('SUB_HEADING_TITLE','Ketika Masalah Terjadi.');
  define('SUB_HEADING_TEXT','Untuk setiap pertanyaan yang berkenaan dengan Sistem Voucher 
  Hadiah, silakan hubungi toko melalui email di '. STORE_OWNER_EMAIL_ADDRESS . '. Pastikan 
  Anda memberikan informasi sebanyak mungkin dalam email. ');
  break;
  default:
  define('SUB_HEADING_TITLE','');
  define('SUB_HEADING_TEXT','Silakan pilih salah satu dari pertanyaan di atas.');

  }
?>