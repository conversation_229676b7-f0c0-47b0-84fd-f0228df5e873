<?php
/*
  	$Id: gv_send.php,v 1.2 2013/06/05 09:43:11 chingyen Exp $
	
  	The Exchange Project - Community Made Shopping!
  	http://www.theexchangeproject.org
	
  	Gift Voucher System v1.0
  	Copyright (c) 2001,2002 <PERSON>
  	http://www.phesis.org
	
  	Released under the GNU General Public License
*/

define('HEADING_TITLE', '<PERSON><PERSON>');
define('NAVBAR_TITLE', '<PERSON><PERSON>');
define('EMAIL_SUBJECT', 'Pertanyaan dari ' . STORE_NAME);
define('HEADING_TEXT','<br>ilakan masukkan di bawah rincian Gift Voucher yang ingin Anda kirimkan. Untuk informasi lebih lanjut, silakan lihat <a href="' . tep_href_link(FILENAME_GV_FAQ,'','NONSSL').'">'.GV_FAQ.'.</a><br>');
define('ENTRY_NAME', '<PERSON><PERSON>:');
define('ENTRY_EMAIL', '<PERSON><PERSON><PERSON>:');
define('ENTRY_MESSAGE', '<PERSON>esan untuk Penerima:');
define('ENTRY_AMOUNT', 'Jumlah Voucher Hadiah:');
define('ERROR_ENTRY_AMOUNT_CHECK', '&nbsp;&nbsp;<span class="errorText">Jumlah tidak valid</span>');
define('ERROR_ENTRY_EMAIL_ADDRESS_CHECK', '&nbsp;&nbsp;<span class="errorText">Alamat email tidak valid</span>');
define('MAIN_MESSAGE', 'Anda telah memutuskan untuk mengirim voucher hadiah senilai %s ke %s yang alamat email adalah %s<br><br>Teks yang menyertai email akan dibaca<br><br>Kepada Yth %s<br><br>
                        Anda telah dikirim sebuah Voucher Hadiah senilai %s oleh %s');

define('PERSONAL_MESSAGE', '%s mengatakan');
define('TEXT_SUCCESS', 'Tahniah, Gift Voucher Anda telah berhasil dikirim');

define('EMAIL_SEPARATOR', '----------------------------------------------------------------------------------------');
define('EMAIL_GV_TEXT_HEADER', 'Tahniah, Anda telah menerima hadiah voucher senilai %s');
define('EMAIL_GV_TEXT_CODE', 'Tahniah, Anda telah menerima hadiah voucher senilai %s');
define('EMAIL_GV_TEXT_SUBJECT', 'Hadiah dari %s');
define('EMAIL_GV_FROM', 'Voucher Hadiah ini telah dikirimkan kepada Anda oleh %s');
define('EMAIL_GV_MESSAGE', 'Dengan pesan yang mengatakan ');
define('EMAIL_GV_SEND_TO', 'Hi, %s');
//define('EMAIL_GV_REDEEM', 'Untuk menebus Voucher Hadiah, silakan klik pada link di bawah ini. Silakan juga menuliskan kode penebusan %s. Dalam kasus Anda mengalami masalah .');
define('EMAIL_GV_TEXT_TO_REDEEM', 'Untuk menebus Voucher Hadiah, silakan kunjungi <a href="%s">%s</a> dan masukkan kode penebusan ketika proses checkout .');
define('EMAIL_GV_LINK', 'Untuk menebus silakan klik');
define('EMAIL_GV_VISIT', ' atau kunjungi ');
define('EMAIL_GV_ENTER', ' dan masukkan kode ');
/*
define('EMAIL_GV_FIXED_FOOTER', 'Jika Anda memiliki masalah menebus Voucher Hadiah sila menggunakan taut di atas, ' . "\n" . 
                                'Anda juga dapat memasukkan kode Voucher Hadiah selama proses checkout di toko kami.' . "\n\n");
*/
define('EMAIL_GV_FIXED_FOOTER', 'Untuk sembarang pertanyaan atau bantuan, Anda dapat menggunakan layanan Online Live Support kami atau email pertanyaan Anda ke ' . EMAIL_TO . '. Harap ingat untuk memasukkan Kode Penebusan Anda ketika menghubungi kami untuk mempercepat proses.');
define('EMAIL_GV_SHOP_FOOTER', STORE_EMAIL_SIGNATURE);
?>