<DIV id=datacontainer onmouseover=scrollspeed=0 style="LEFT: 0px; WIDTH: 100%; POSITION: absolute; TOP: 218px; HEIGHT: 266px" onmouseout=scrollspeed=cache>
<P>&nbsp;</P>
<P>&nbsp;</P>
<P><B></B>&nbsp;</P>
<P><B></B>&nbsp;</P>
<P><B></B>&nbsp;</P>
<P><B>Willkommen bei uns!</B></P>
<P><STRONG></STRONG>&nbsp;</P>
<P align=center><IMG style="WIDTH: 120px; HEIGHT: 130px" height=153 alt="" hspace=0 src="http://localhost/sec_html/images/0_large.jpg" width=145 align=baseline border=0></P>
<P>&nbsp;</P>
<P>&nbsp;</P>
<P>&nbsp;</P>
<P>&nbsp;</P>
<P>&nbsp;</P></DIV>
<SCRIPT language=JavaScript1.2>
//Specify speed of scroll. Larger=faster (ie: 5)
var scrollspeed=cache=2
function initialize(){
marqueeheight=document.all? parent.document.all.datamain.height : parent.document.getElementById("datamain").getAttribute("height")
dataobj=document.all? document.all.datacontainer : document.getElementById("datacontainer")
dataobj.style.top=5
thelength=dataobj.offsetHeight
scrolltest()
}
function scrolltest(){
dataobj.style.top=parseInt(dataobj.style.top)-scrollspeed
if (parseInt(dataobj.style.top)<thelength*(-1))
dataobj.style.top=5
setTimeout("scrolltest()",50)
}
window.onload=initialize
</SCRIPT>