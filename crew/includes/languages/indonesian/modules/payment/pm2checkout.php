<?php
/*
  $Id: pm2checkout.php,v 1.1 2013/05/27 10:10:24 weichen Exp $

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2002 osCommerce

  Released under the GNU General Public License
*/

	define('MODULE_PAYMENT_2CHECKOUT_TEXT_TITLE', 'Kartu Kredit (MasterCard/Visa/AMEX/Discover)');
    define('MODULE_PAYMENT_2CHECKOUT_TEXT_CART', 'Lanjutkan ke Kartu Kredit (MasterCard/Visa/AMEX/Discover)');
	define('MODULE_PAYMENT_2CHECKOUT_TEXT_DISPLAY_TITLE', 'Kartu Kredit (MasterCard/Visa/AMEX/Discover)');	// for title displaying purpose if different from MODULE_PAYMENT_PAYPAL_TEXT_TITLE
	define('MODULE_PAYMENT_2CHECKOUT_TEXT_DESCRIPTION', 'Info Ujian Kartu Kredit:<br><br>CC#: ****************<br><PERSON><PERSON><PERSON><PERSON><PERSON>: Apa pun');
	define('MODULE_PAYMENT_2CHECKOUT_TEXT_TYPE', 'Jenis:');
	define('MODULE_PAYMENT_2CHECKOUT_TEXT_CREDIT_CARD_OWNER', 'Pemilik Kartu Kredit:');
	define('MODULE_PAYMENT_2CHECKOUT_TEXT_CREDIT_CARD_OWNER_FIRST_NAME', 'Nama Depan Pemilik Kartu Kredit:');
	define('MODULE_PAYMENT_2CHECKOUT_TEXT_CREDIT_CARD_OWNER_LAST_NAME', 'Nama Belakang Pemilik Kartu Kredit:');
	define('MODULE_PAYMENT_2CHECKOUT_TEXT_CREDIT_CARD_OWNER_STREET_ADDRESS', 'Alamat Jalan Pemilik Kartu Kredit:');
	define('MODULE_PAYMENT_2CHECKOUT_TEXT_CREDIT_CARD_OWNER_SUBURB', 'Kota Bahgian Pemilik Kartu Kredit:');
	define('MODULE_PAYMENT_2CHECKOUT_TEXT_CREDIT_CARD_OWNER_POSTCODE', 'Kode Pos Pemilik Kartu Kredit:');
	define('MODULE_PAYMENT_2CHECKOUT_TEXT_CREDIT_CARD_OWNER_CITY', 'Kota Pemilik Kartu Kredit:');
	define('MODULE_PAYMENT_2CHECKOUT_TEXT_CREDIT_CARD_OWNER_STATE', 'Negeri Bahgian Pemilik Kartu Kredit');
	define('MODULE_PAYMENT_2CHECKOUT_TEXT_CREDIT_CARD_OWNER_COUNTRY', 'Negara Pemilik Kartu Kredit:');
	define('MODULE_PAYMENT_2CHECKOUT_TEXT_CREDIT_CARD_NUMBER', 'Nomor Kartu Kredit:');
	define('MODULE_PAYMENT_2CHECKOUT_TEXT_CREDIT_CARD_EXPIRES', 'Tanggal Kadaluwarsa Kartu Kredit:');
	define('MODULE_PAYMENT_2CHECKOUT_TEXT_CREDIT_CARD_CHECKNUMBER', 'Kode Keamanan Kartu Kredit:');
	define('MODULE_PAYMENT_2CHECKOUT_TEXT_CREDIT_CARD_CHECKNUMBER_LOCATION', '(terletak di bagian belakang kartu kredit)');
	define('MODULE_PAYMENT_2CHECKOUT_TEXT_JS_CC_NUMBER', '* Nomor Kartu Kredit harus berisi setidaknya ' . CC_NUMBER_MIN_LENGTH . ' karakter.\n');
	define('MODULE_PAYMENT_2CHECKOUT_TEXT_ERROR_MESSAGE', 'Error telah terjadi ketika pemrosesan kartu kredit Anda. Silakan coba lagi.');
	define('MODULE_PAYMENT_2CHECKOUT_TEXT_ERROR_HASH_MESSAGE', 'Pembayaran Anda tampaknya datang dari situs lain kemudian CheckOut. Harap jangan melanjutkan prosedur checkout DAN hubungi kami! !');  
	define('MODULE_PAYMENT_2CHECKOUT_TEXT_ERROR', 'Error Kartu Kredit!');
	define('MODULE_PAYMENT_2CHECKOUT_CURRENCY_CONVERSITION', ' - Harga akan dikonversi ke Dolar US sebelum konfirmasi.');
	define('MODULE_PAYMENT_2CHECKOUT_TEXT_CONFIRMATION', MODULE_PAYMENT_2CHECKOUT_MESSAGE);
?>