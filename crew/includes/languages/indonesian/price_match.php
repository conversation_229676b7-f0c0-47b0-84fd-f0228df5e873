<?php
/*
  $Id: price_match.php,v 1.2 2013/06/05 09:43:11 chingyen Exp $

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2002 osCommerce

  Released under the GNU General Public License
*/

define('HEADING_TITLE', '<PERSON><PERSON><PERSON>');
define('NAVBAR_TITLE', '<PERSON><PERSON><PERSON>');
define('TEXT_SUCCESS', 'Permintaan Anda telah berhasil dikirim ke Pemilik Toko.');
define('EMAIL_SUBJECT', '[' . STORE_NAME . '] ' . NAVBAR_TITLE);

define('TEXT_FOUND_CHEAPER_INFO', '<PERSON><PERSON>a menemukan Toko lain yang menjual lebih murah, kami akan mencocokkannya
		Gunakan formulir di bawah ini untuk meminta harga cocok dan sespesifik setepat mungkin untuk menghindari
		penundaan yang tidak perlu.');

define('ENTRY_NAME', '<PERSON><PERSON>:');
define('ENTRY_EMAIL', '<PERSON><PERSON><PERSON>:');
define('ENTRY_ITEM_NAME', 'Nama Barang:');
define('ENTRY_ITEM_URL', 'URL Barang:');
define('ENTRY_PRICE', 'Harga (USD):');
define('ENTRY_COMMENTS', 'Komentar:');
define('ENTRY_REALM', 'Alam:');
define('ENTRY_REALM_LADDER', 'Tangga:');
define('ENTRY_REALM_HARDCORE', 'Hardcore:');
?>