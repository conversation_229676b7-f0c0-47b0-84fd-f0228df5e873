<?php

// Mantis # 0000024 @ 200810241055 - Add popup message & form's text fields
define('HEADER_TITLE_SECRET_QUESTION', 'Tetapkan Pertanyaan Dan Jawaban Rahasia');
define('HEADER_TITLE_CHANGE_SECRET_QNA', '<PERSON>bah Tanya Jawa<PERSON>');

define('SET_SECRET_QUESTION', 'Pertanyaan Rahasia:&nbsp;');
define('SET_SECRET_QUESTION_TEXT', '*');
define('SET_ANSWER', '<PERSON><PERSON><PERSON>:&nbsp;');
define('SET_ANSWER_TEXT', '*');
define('ENTRY_SECRET_QUESTION_ERROR', 'Anda harus memilih pertanyaan rahasia dari menu turun Pertanyaan Rahasia.');
define('ENTRY_ANSWER_ERROR', "Jawaban Anda harus berisi minimal %d karakter.");

define('TEXT_REGISTER_SECRET_QUESTION_AND_ANSWER','<PERSON><PERSON><PERSON> mendaftar <a href='. tep_href_link(FILENAME_SET_SECRET_QNA) .'>Pertanyaan & Jawaban Rahasia</a> Anda untuk memperbarui profil Anda');

// Show done message @ 200810311207
define('ENTRY_UPDATE_DONE', 'Pertanyaan dan Jawaban Rahasia Anda telah berhasil diperbarui.');
define('ENTRY_SAVE_DONE', 'Pertanyaan dan Jawaban Rahasia Anda telah berhasil disimpan.');

define('HIDDEN_PBLINK', '<input type="hidden" id="hid_value" name="hid_value">'); 
define('POPUP_TEXT_CHANGE_TO_SECRET_QUESTION_AND_ANSWER_NOTICE', tep_db_input('<div id="popupTitle"><p>Notis Setup Metode Keamanan Tanya Jawab Rahasia</p></div><div id="popupMesgTop"><p>Pelanggan Yang Terhormat,</p></div><p id="popupMesg">>Untuk membawa keamanan akun ke tingkat yang lebih tinggi, kita akan mengganti Metode PIN Nomor biasa dengan <span class="redIndicator">Metode Keamanan Tanya Jawab Rahasia</span>, baru kami, fitur baru diperkenalkan yang jauh lebih nyaman dan mengamankan. Anda dapat. Anda dapat menetapakan Tanya Jawab Rahasia Anda melalui halaman manajemen akun, dan itu akan menggantikan fitur PIN Nomor Anda setelah setup selesai.<br><br>Silakan menyelesaikan konfigurasi Anda dalam jangka waktu<span class="redIndicator">15 hari</span>. Harus setup akan tidak selesai dalam jangka waktu <span class="redIndicator">15 hari</span>, akun Anda akan dibekukan secara otomatis oleh sistem kami.<br><br>Metode Keamanan Tanya Jawab Rahasia , lihat pusat informasi kami atau Anda dapat menghubungi layanan pelanggan kami.<br><br>Terima kasih untuk usaha Anda dalam memastikan keamanan akun Anda.<br></p><div id="popupMesgBottom"><p>Offgamers Team</p></div><div id="popupBottom"><p><a href="%s" class="subModelLink" onclick="document.getElementById(\'hid_value\').value=\''.stripslashes($_SESSION['ssqna']='popuplink').'\'">Setup Untuk Metode Keamanan Rahasia Q & A Sekarang</a><br><a href="javascript:;" %s class="subModelLink">Aku akan melakukannya nanti</a><br>Waktu Sisa: %d Hari</p></div>'));
define('POPUP_TEXT_CHANGE_TO_SECRET_QUESTION_AND_ANSWER_TITLE', 'Notis Setup Metode Keamanan Tanya Jawab Rahasia');
//##

// Mantis # 0000024 @ 200810271754 - Add form's text fields
define('TEXT_PIN_NUMBER', 'Nomor PIN');
define('TEXT_HELP_PIN_NUMBER', 'Silakan berikan Nombor PIN Anda');
define('TEXT_PINNUMBER_REQUIRED', 'Nomor PIN diperlukan untuk memperbarui kolom ini : %s');
define('TEXT_IS_MANDATORY', 'Kolom yang ditandai dengan <span class="redIndicator">*</span> adalah wajib');
define('TEXT_SAVE', 'Simpan');
define('TEXT_MUST_LOGIN', 'Login diperlukan untuk mengakses bagian ini.');

define('BUTTON_SAVE', 'Simpan');
define('BUTTON_REQUEST_TOKEN', 'Meminta Token Keamanan');
define('TEXT_SECRET_QNA_ISSET', 'Karena tujuan keamanan, Pertanyaan dan Jawaban rahasia Anda tidak dapat diubah melalui halaman ini. Silakan hubungi <a href="'.tep_href_link(FILENAME_CUSTOMER_SUPPORT, '', 'SSL').'">Wakil Dukungan</a> untuk meminta perubahan.');
// You have registered your Secret Question and Answer in our system, if you would like to reset it, please kindly contact our '. LIVE_SUPPORT_LINK .' to reset your Secret Question and Answer. Thank you.

define('ENTRY_SECRET_ANSWER_QUESTION_NOTICE', 'Pertanyaan & Jawaban Rahasia Anda adalah tindakan pengamanan untuk menjamin keamanan akun Anda. Ini akan diminta jika ada perubahan ke akun OffGamers Anda, selama penarikan Kredit Penjual Anda dan banyak lagi.');
define('ENTRY_SECRET_ANSWER_NOTICE', 'Pastikan bahwa Pertanyaan & Jawaban Rahasia hanya diketahui oleh Anda dan tidak berbagi dengan orang lain.');
define('ENTRY_CONTACT_NUMBER_EXIST_ERROR', "Nomor ponsel yang Anda masukkan telah digunakan atau tidak sah. Harap masukkan nomor ponsel lain.");
define('TEXT_CHANGE_PHONE_NUMBER', '(Bukan nomor ponsel Anda? <a id="link_edit_phone_number" href="' . tep_href_link(FILENAME_ACCOUNT_EDIT) . '">Ubah sini</a>.)');

define('EMAIL_USER_RESET_PIN_SUBJECT', implode(' ', array(EMAIL_SUBJECT_PREFIX, 'Token Keamanan Anda sudah di-reset')));
define('EMAIL_USER_RESET_PIN_BODY', 'Token Keamanan baru Anda adalah %s. Token ini akan berakhir dalam 24 jam dari saat Anda menerima email ini.');

define('TEXT_SECRET_QNA', 'TOKEN KEAMANAN');
define('TEXT_DORMANT_ACCOUNT_QNA_REQUEST', 'Akun yang tidak aktif untuk lebih dari 90 hari diminta memasukkan Token Keamanan untuk mengaktifkan kembali akun sebelum melalukan pembelian.<br><br>Token Keamanan Anda akan berakhir dalam 10 menit. Permintaan berikutnya untuk token keamanan baru tidak boleh melebihi dari 3 kali dalam satu hari.');
define('TEXT_FORGOT_QNA', 'Tidak dapat memverifikasi atau menerima token keamanan? Silakan <a href="' . tep_href_link(FILENAME_CUSTOMER_SUPPORT) . '">hubungi kami</a> segera untuk bantuan.');
define('ENTRY_QNA_MISMATCH_ANSWER_ERROR', 'Maaf, token keamanan yang diberikan tidak benar. Silakan coba lagi.');

define('TEXT_REQUEST_TOKEN_MSG', 'Token Keamanan Anda akan berakhir dalam 10 menit. Permintaan berikutnya untuk token keamanan baru tidak boleh melebihi dari 3 kali dalam satu hari.');
define('TEXT_REQUEST_TOKEN_SUCCESS_MSG', 'Token Keamanan yang dikirim melalui SMS ke nomor ponsel Anda yang terdaftar %s.');
define('TEXT_REQUEST_TOKEN_REUSE_MSG', 'Silakan ketik dalam token keamanan token sebelumnya.');
define('TEXT_REQUEST_TOKEN_FAIL_MSG', 'Anda telah melebihi batas yang diijinkan untuk token keamanan. Silakan coba lagi setelah 24 jam.');
define('TEXT_REQUEST_TOKEN_HELP_MSG', 'token keamanan yang dikirim melalui SMS ke nomor ponsel Anda yang terdaftar. Nomor hilang atau nomor yang tidak sah, silakan hubungi <a href="%s" class="whiteText">Layanan Pelanggan</a> kami.');
?>