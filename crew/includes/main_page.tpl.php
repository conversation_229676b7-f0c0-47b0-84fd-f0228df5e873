<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<?
//Get current time 
    $mtime = microtime(); 
//Split seconds and microseconds 
    $mtime = explode(" ",$mtime); 
//Create one value for start time 
    $mtime = $mtime[1] + $mtime[0]; 
//Write start time into a variable 
    $tstart = $mtime; 
?>

<html <?php echo HTML_PARAMS; ?>>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=<?php echo CHARSET; ?>">
<?php require(DIR_WS_INCLUDES . 'meta_tags.php'); ?>
<title><?php echo META_TAG_TITLE; ?></title>
<base href="<?php echo (($request_type == 'SSL') ? HTTPS_SERVER : HTTP_SERVER) . DIR_WS_CATALOG; ?>">
<meta name="description" content="<?php echo META_TAG_DESCRIPTION; ?>">
<meta name="keywords" content="<?php echo META_TAG_KEYWORDS; ?>">

<link rel="stylesheet" type="text/css" href="<? echo THEMA_STYLE;?>">
<?php if ($javascript) { require(DIR_WS_JAVASCRIPT . $javascript); } ?>

</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0">
<?
  if ( CENTER_SHOP_ON == 'on' ) {
?>
        <table border="<?php echo CENTER_SHOP_BORDER; ?>" align="<? echo CENTER_SHOP_ALIGN; ?>" width="<?php echo CENTER_SHOP_WIDTH; ?>" cellpadding="<?php echo CENTER_SHOP_PADDING; ?>" cellspacing="0">
        <tr><td>
           <table border="<?php echo CENTER_SHOP_BORDER; ?>" align="<? echo CENTER_SHOP_ALIGN; ?>" width="<?php echo CENTER_SHOP_WIDTH; ?>" cellpadding="<?php echo CENTER_SHOP_CELLPADDING; ?>" cellspacing="<?php echo CENTER_SHOP_CELLSPACING; ?>">
		   <tr><td width="25%" align="left" class="headerNavigation">
<?php }else{ ?>
	  <table align="center" width="600" border="0"  cellpadding="0" cellspacing="0">
	  <tr><td>
	  <table align="center" border="0" cellpadding="0" cellspacing="3" width="100%">
  <? } ?>
		
			

  <tr>
    <td>

<?php require(DIR_WS_INCLUDES . 'warnings.php'); ?>


<!-- header //-->
<?
  if ( CENTER_SHOP_ON == 'on' ) {
?>
	      <table border="<?php echo CENTER_SHOP_BORDER; ?>" align="<? echo CENTER_SHOP_ALIGN; ?>" width="<?php echo CENTER_SHOP_WIDTH; ?>" cellspacing="0" cellpadding="0">
<?php }else{ ?>
	<table border="0" align="center" width="100%" cellspacing="0" cellpadding="0">
  <? } ?>
  
    <tr> 
          <td width="280" height="126" rowspan="2">
          <?
		    $flash_url = THEMA_IMAGES."flash/header.swf";
		    echo "<object classid=\"clsid:D27CDB6E-AE6D-11cf-96B8-************\" codebase=\"http://download.macromedia.com/pub/shockwave/cabs/flash/swflash.cab#version=6,0,29,0\" width=\"280\" height=\"126\">
		              <param name=\"movie\" value=\"$flash_url\">
		              <param name=\"quality\" value=\"high\">
		              <embed src=\"$flash_url\" quality=\"high\" pluginspage=\"http://www.macromedia.com/go/getflashplayer\" type=\"application/x-shockwave-flash\" width=\"280\" height=\"126\"></embed></object>";     
     	  ?>
          </td>
          <td height="45" colspan="2" align="right" valign="bottom"> 
            <table border="0" cellspacing="4" cellpadding="0">
              <tr>
                <td class="smalltext"><p><?php if (tep_session_is_registered('customer_id')) { ?><a href="<?php echo tep_href_link(FILENAME_LOGOFF, '', 'SSL'); ?>" class="headerNavigation"><?php echo HEADER_TITLE_LOGOFF; ?></a>&nbsp;&nbsp;|&nbsp;&nbsp;<?php } ?><a href="<?php echo tep_href_link(FILENAME_ACCOUNT, '', 'SSL'); ?>" class="headerNavigation"><?php echo HEADER_TITLE_MY_ACCOUNT; ?></a>&nbsp;|&nbsp;
                 <a href="<?php echo tep_href_link(FILENAME_SHOPPING_CART); ?>" class="headerNavigation"><?php echo HEADER_TITLE_CART_CONTENTS; ?></a> &nbsp;|&nbsp; <a href="<?php echo tep_href_link(FILENAME_CHECKOUT_SHIPPING, '', 'SSL'); ?>" class="headerNavigation"><?php echo HEADER_TITLE_CHECKOUT; ?></a>&nbsp;&nbsp;|&nbsp;&nbsp;<script type="text/javascript" language="javascript" src="https://support.offgamers.com/lh/live.php?text=x&amp;"></script></p>
                  </td>
              </tr>
            </table></td>
        </tr>
        
   <tr>
    <td height="81" align="center" background="<?=THEMA_IMAGES?>/header05.jpg"><a href="index.php">
    <?
		    $flash_url = THEMA_IMAGES."flash/logo.swf";
		    echo "<object classid=\"clsid:D27CDB6E-AE6D-11cf-96B8-************\" codebase=\"http://download.macromedia.com/pub/shockwave/cabs/flash/swflash.cab#version=6,0,29,0\" width=\"288\" height=\"81\">
		              <param name=\"movie\" value=\"$flash_url\">
		              <param name=\"quality\" value=\"high\">
		              <embed src=\"$flash_url\" quality=\"high\" pluginspage=\"http://www.macromedia.com/go/getflashplayer\" type=\"application/x-shockwave-flash\" width=\"288\" height=\"81\"></embed></object>";     
     	  ?>
    </a></td>
    <td width="280" align="right" background="<?=THEMA_IMAGES?>/header05.jpg"><img src="<?=THEMA_IMAGES?>/header04.jpg" width="86" height="81"></td>
   </tr>
</table>
<span class="headerNavigation"><?php echo $breadcrumb->trail(' &rsaquo; '); ?></span>   
<br>     
<?
  if ( CENTER_SHOP_ON == 'on' ) {
?>
		    <table border="<?php echo CENTER_SHOP_BORDER; ?>" align="<? echo CENTER_SHOP_ALIGN; ?>" width="<?php echo CENTER_SHOP_WIDTH; ?>" cellpadding="<?php echo CENTER_SHOP_PADDING; ?>" cellspacing="0">
		    <tr><td valign="top">
	      		<table border="<?php echo CENTER_SHOP_BORDER; ?>" width="<?php echo LEFT_BOX_WIDTH; ?>" cellspacing="0" cellpadding="0">
      	
<?php } else {?>
	<table border="0" width="100%" cellspacing="0" cellpadding="2">
	  <tr><td width="<?php echo BOX_WIDTH; ?>" valign="top">
	    <table border="0" width="<?php echo BOX_WIDTH; ?>" cellspacing="0" cellpadding="0">
    <? } ?>
		<!-- left_navigation //-->
		<?php require(DIR_WS_INCLUDES . 'column_left.php'); ?>
		<!-- left_navigation_eof //-->
    	</table>
    	</td>

		<!-- content //-->
		<td width="1" bgcolor="#282820"><img src="images/pixel_trans.gif" border="0" alt="" width="1" height="1"></td>	
		<td width="100%" valign="top" align="center">
    	
		<?
		  if ( CENTER_SHOP_ON == 'on' ) {
		?>
		          <table width="<?php echo CENTER_BOX_WIDTH; ?>" align="<?php echo CENTER_BOX_ALIGN; ?>" border="<?php echo CENTER_SHOP_BORDER; ?>" cellpadding="<?php echo CENTER_SHOP_PADDING; ?>" cellspacing="0">
		          <tr>
		          <td valign="top" align="center">
		<?php }else{ ?>
		
		        <table border="0" align="center" cellpadding="0" cellspacing="0">
		        <tr><td valign="top" align="center">
		<? } ?>
				<?php 
				  if (isset($content_template)) {
				    require(DIR_WS_CONTENT . $content_template);
				  } else {
				    require(DIR_WS_CONTENT . $content . '.tpl.php');
				  }
				?>
		    </td>
		  </tr>
		</table>
		
		</td>
		<!-- content_eof //-->
		<td width="1" bgcolor="#282820"><img src="images/pixel_trans.gif" border="0" alt="" width="1" height="1"></td>	
		<td width="8"><img src="images/pixel_trans.gif" border="0" alt="" width="10" height="0"></td>	
    	<td width="100%" valign="top" align="center" class="systemBox">
    
    <?
		  if ( CENTER_SHOP_ON == 'on' ) {
		?>
		          <table width="<?php echo RIGHT_BOX_WIDTH; ?>" align="<?php echo RIGHT_BOX_ALIGN; ?>" border="<?php echo CENTER_SHOP_BORDER; ?>" cellpadding="<?php echo CENTER_SHOP_PADDING; ?>" cellspacing="0">
		<?php }else{ ?>
		
		        <table border="0" align="center" cellpadding="0" cellspacing="0">
		<? } ?>
		

<!-- right_navigation //-->
<?php require(DIR_WS_INCLUDES . 'column_right.php'); ?>
<!-- right_navigation_eof //-->
    </table></td>
  </tr>
</table>
	
<tr>
<td colspan="2" rowspan="2" align="center">
	<!-- footer //--><center>
	<?php require(DIR_WS_INCLUDES . 'footer.php'); ?>
	</center>
	<!-- footer_eof //-->
</td></tr>
</TABLE>
</body>
</html>
