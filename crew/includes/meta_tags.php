<?
/***********************************************************
	FILE: 	meta_tags.php
	USE : 	This file controls the title, meta description,
			and meta keywords of every page on your web site.
			See the install docs for instructions.
***********************************************************/

// Define Primary Section Output
define('PRIMARY_SECTION', ' | ');

// Define Secondary Section Output
define('SECONDARY_SECTION', ' - ');

// Define Tertiary Section Output
define('TERTIARY_SECTION', ', ');

// Optional customization options for each language
switch ($languages_id) {
  	case '1':	// English language
    	//Extra keywords that will be outputted on every page
    	$mt_extra_keywords = '';
		//Descriptive tagline of your web site
		$web_site_tagline = TERTIARY_SECTION . '';
		break;
  	case '2':	// CN Simplified language
    	//Extra keywords that will be outputted on every page
    	$mt_extra_keywords = '';
		//Descriptive tagline of your web site
		$web_site_tagline = TERTIARY_SECTION . '';
		break;
  	case '3':	// Spanish language
    	//Extra keywords that will be outputted on every page
    	$mt_extra_keywords = '';
		//Descriptive tagline of your web site
		$web_site_tagline = TERTIARY_SECTION . '';
		break;
}

// Clear web site tagline if not customized
if ($web_site_tagline == TERTIARY_SECTION) {
	$web_site_tagline = '';
}

// Get all top category names for use with web site keywords
/* Commented to not display all the main categories name
$mt_categories_query = tep_db_query("select cd.categories_name from " . TABLE_CATEGORIES . " c, " . TABLE_CATEGORIES_DESCRIPTION . " cd where c.parent_id = '0' and c.categories_id = cd.categories_id and cd.language_id='" . (int)$languages_id ."'");
while ($mt_categories = tep_db_fetch_array($mt_categories_query))  {
	$mt_keywords_string .= $mt_categories['categories_name'] . ', ';
}
*/

$seo_run_content_meta = true;

$browser_title = tep_not_null(BROWSER_TITLE) ? BROWSER_TITLE . PRIMARY_SECTION : '';
define('WEB_SITE_KEYWORDS', $mt_keywords_string . $mt_extra_keywords . (tep_not_null(META_KEYWORDS) ? META_KEYWORDS . TERTIARY_SECTION : '') );
$seo_filename = $filename;
switch ($filename) {
  	case FILENAME_DEFAULT:
  		if (isset($_GET['cPath'])) {
	  		$seo_querystring = 'cPath='.$_GET['cPath'];//.'&'.'tpl='.$seo_querystring_tpl; 
		}

		break;

  	case FILENAME_CUSTOM_PRODUCT_INFO:
			$seo_filename = FILENAME_DEFAULT;
			$seo_querystring = 'cPath='.$page_info->categories_id.'&product';
			
			//print_r($page_info);
		break;

  	case FILENAME_PRODUCT_INFO:
  		if (isset($_GET['products_id']))
	  		$seo_querystring = 'products_id='.$_GET['products_id']; 

		break;

  	case FILENAME_CMS_CONTENT:
  		if (isset($_GET['menu_id']))
	  		$seo_querystring = 'menu_id='.$_GET['menu_id']; 

		break;

  	case FILENAME_INFOLINKS:
  		if (isset($_GET['content_id']) && isset($_GET['id']))
	  		$seo_querystring = 'content_id='.$_GET['content_id'].'&id='.$_GET['id']; 
  		else if (isset($_GET['id']))
	  		$seo_querystring = 'id='.$_GET['id']; 
		break;

  	case FILENAME_NEWS:
  		if (isset($_GET['news_id']) && isset($_GET['news_type']))
	  		$seo_querystring = 'news_id='.$_GET['news_id'].'&news_type='.$_GET['news_type'];

		break;

  	default:
		;
}

if ($seo_querystring) {
	$seo_meta_sql = "	SELECT seo_meta_id,seo_meta_title,seo_meta_description,seo_meta_keywords,seo_meta_robots 
						FROM " . TABLE_SEO_META_TAG . " 
						WHERE seo_meta_baseurl='".tep_db_input($seo_filename)."'
							AND language_id = '". $languages_id ."'
							AND seo_meta_query_string = '".tep_db_input($seo_querystring)."'";
	$seo_meta_result = tep_db_query($seo_meta_sql);
	
	if ($seo_meta_row = tep_db_fetch_array($seo_meta_result)) {
    	switch ($content) {
    		case CONTENT_CUSTOM_PRODUCT_INFO:
    			$meta_product_name = tep_get_products_name($HTTP_GET_VARS['products_id'], $languages_id);
    			define('META_TAG_TITLE', strip_tags(stripslashes(str_replace("##PRODUCT_CATEGORY_NAME##", $meta_product_name, $seo_meta_row['seo_meta_title']))));
				define('META_TAG_DESCRIPTION', strip_tags(stripslashes(str_replace("##PRODUCT_CATEGORY_NAME##", $meta_product_name, $seo_meta_row['seo_meta_description']))));
				define('META_TAG_KEYWORDS', strip_tags(stripslashes($seo_meta_row['seo_meta_keywords'])));
				define('META_TAG_ROBOTS', strip_tags(stripslashes($seo_meta_row['seo_meta_robots'])));
				
				break;
    		default:
		    	define('META_TAG_TITLE', strip_tags(stripslashes($seo_meta_row['seo_meta_title'])));
				define('META_TAG_DESCRIPTION', strip_tags(stripslashes($seo_meta_row['seo_meta_description'])));
				define('META_TAG_KEYWORDS', strip_tags(stripslashes($seo_meta_row['seo_meta_keywords'])));
				define('META_TAG_ROBOTS', strip_tags(stripslashes($seo_meta_row['seo_meta_robots'])));
		}
		$seo_run_content_meta = false;
	}
}

if ($seo_run_content_meta) {
	if ($content != CONTENT_BUYBACK && !tep_not_null($_GET['news_id']))	define('META_TAG_DESCRIPTION', META_DESCRIPTION);
	
	switch ($content) {
	  	case CONTENT_ADVANCED_SEARCH:
	    	define('META_TAG_TITLE', $browser_title . NAVBAR_TITLE_1 . PRIMARY_SECTION . TITLE . $web_site_tagline);
			//define('META_TAG_DESCRIPTION', TITLE . PRIMARY_SECTION . NAVBAR_TITLE_1 . SECONDARY_SECTION . WEB_SITE_KEYWORDS);
			define('META_TAG_KEYWORDS', WEB_SITE_KEYWORDS . NAVBAR_TITLE_1);
			
			break;
	  	case CONTENT_ADVANCED_SEARCH_RESULT:
		    define('META_TAG_TITLE', $browser_title . NAVBAR_TITLE_2 . PRIMARY_SECTION . TITLE . $web_site_tagline);
			//define('META_TAG_DESCRIPTION', TITLE . PRIMARY_SECTION . NAVBAR_TITLE_2 . SECONDARY_SECTION . WEB_SITE_KEYWORDS);
			define('META_TAG_KEYWORDS', WEB_SITE_KEYWORDS . NAVBAR_TITLE_2);
			
			break;
		//START UPDATE
	  	case CONTENT_ACCOUNT_EDIT:
		    define('META_TAG_TITLE', $browser_title . HEADING_TITLE . PRIMARY_SECTION . TITLE . $web_site_tagline);
			//define('META_TAG_DESCRIPTION', TITLE . PRIMARY_SECTION . NAVBAR_TITLE_1 . SECONDARY_SECTION . WEB_SITE_KEYWORDS);
			define('META_TAG_KEYWORDS', WEB_SITE_KEYWORDS . NAVBAR_TITLE_1);
			
			break;
		case CONTENT_ACCOUNT_ACTIVATE:
			define('META_TAG_TITLE', $browser_title . HEADING_TITLE_1 . PRIMARY_SECTION . TITLE . $web_site_tagline);
			define('META_TAG_KEYWORDS', WEBSITE_KEYWORDS. NAVBAR_TITLE_1);
			
			break;
	  	case CONTENT_ACCOUNT_HISTORY:
		    define('META_TAG_TITLE', $browser_title . HEADING_TITLE . PRIMARY_SECTION . TITLE . $web_site_tagline);
			//define('META_TAG_DESCRIPTION', TITLE . PRIMARY_SECTION . NAVBAR_TITLE_1 . SECONDARY_SECTION . WEB_SITE_KEYWORDS);
			define('META_TAG_KEYWORDS', WEB_SITE_KEYWORDS . NAVBAR_TITLE_1);
			
			break;
	  	case CONTENT_ACCOUNT_HISTORY_INFO:
		    define('META_TAG_TITLE', $browser_title . HEADING_TITLE . PRIMARY_SECTION . TITLE . $web_site_tagline);
			//define('META_TAG_DESCRIPTION', TITLE . PRIMARY_SECTION . NAVBAR_TITLE_1 . SECONDARY_SECTION . WEB_SITE_KEYWORDS);
			define('META_TAG_KEYWORDS', WEB_SITE_KEYWORDS . NAVBAR_TITLE_1);
			
			break;
	  	case CONTENT_ACCOUNT_PAYMENT_EDIT:
		    define('META_TAG_TITLE', $browser_title . HEADING_TITLE . PRIMARY_SECTION . TITLE . $web_site_tagline);
			//define('META_TAG_DESCRIPTION', TITLE . PRIMARY_SECTION . NAVBAR_TITLE_1 . SECONDARY_SECTION . WEB_SITE_KEYWORDS);
			define('META_TAG_KEYWORDS', WEB_SITE_KEYWORDS . NAVBAR_TITLE_1);
			
			break;
	  	case CONTENT_ACCOUNT_NEWSLETTERS:
		    define('META_TAG_TITLE', $browser_title . HEADING_TITLE . PRIMARY_SECTION . TITLE . $web_site_tagline);
			//define('META_TAG_DESCRIPTION', TITLE . PRIMARY_SECTION . NAVBAR_TITLE_1 . SECONDARY_SECTION . WEB_SITE_KEYWORDS);
			define('META_TAG_KEYWORDS', WEB_SITE_KEYWORDS . NAVBAR_TITLE_1);
			
			break;
	  	case CONTENT_ACCOUNT_NOTIFICATIONS:
		    define('META_TAG_TITLE', $browser_title . HEADING_TITLE . PRIMARY_SECTION . TITLE . $web_site_tagline);
			//define('META_TAG_DESCRIPTION', TITLE . PRIMARY_SECTION . NAVBAR_TITLE_1 . SECONDARY_SECTION . WEB_SITE_KEYWORDS);
			define('META_TAG_KEYWORDS', WEB_SITE_KEYWORDS . NAVBAR_TITLE_1);
			
			break;
	  	case CONTENT_ACCOUNT_PASSWORD:
		    define('META_TAG_TITLE', $browser_title . HEADING_TITLE . PRIMARY_SECTION . TITLE . $web_site_tagline);
			//define('META_TAG_DESCRIPTION', TITLE . PRIMARY_SECTION . NAVBAR_TITLE_1 . SECONDARY_SECTION . WEB_SITE_KEYWORDS);
			define('META_TAG_KEYWORDS', WEB_SITE_KEYWORDS . NAVBAR_TITLE_1);
			
			break;
	  	case CONTENT_ADDRESS_BOOK:
		    define('META_TAG_TITLE', $browser_title . HEADING_TITLE . PRIMARY_SECTION . TITLE . $web_site_tagline);
			//define('META_TAG_DESCRIPTION', TITLE . PRIMARY_SECTION . NAVBAR_TITLE_1 . SECONDARY_SECTION . WEB_SITE_KEYWORDS);
			define('META_TAG_KEYWORDS', WEB_SITE_KEYWORDS . NAVBAR_TITLE_1);
			
			break;
	  	case CONTENT_ADDRESS_BOOK_PROCESS:
		    define('META_TAG_TITLE', $browser_title . HEADING_TITLE . PRIMARY_SECTION . TITLE . $web_site_tagline);
			//define('META_TAG_DESCRIPTION', TITLE . PRIMARY_SECTION . NAVBAR_TITLE_1 . SECONDARY_SECTION . WEB_SITE_KEYWORDS);
			define('META_TAG_KEYWORDS', WEB_SITE_KEYWORDS . NAVBAR_TITLE_1);
			
			break;
	  	case CONTENT_ADDRESS_BOOK_PROCESS:
		    define('META_TAG_TITLE', $browser_title . HEADING_TITLE . PRIMARY_SECTION . TITLE . $web_site_tagline);
			//define('META_TAG_DESCRIPTION', TITLE . PRIMARY_SECTION . NAVBAR_TITLE_1 . SECONDARY_SECTION . WEB_SITE_KEYWORDS);
			define('META_TAG_KEYWORDS', WEB_SITE_KEYWORDS . NAVBAR_TITLE_1);
			
			break;
		case CONTENT_BUYBACK:
			define('META_TAG_TITLE', 'Sell wow gold, Sell world of warcraft gold, MMO Game Currencies');
			define('META_TAG_DESCRIPTION', 'Earn money with your WOW Gold, MMO In Game Currencies, gaming account on all games by selling to OffGamers. We provide 24/7 Friendly Live Support & Multiple Instant Payment.');
			define('META_TAG_KEYWORDS', 'Sell wow gold, world of warcraft gold, sell game currencies, sell account, buyback, gold, servers, adena, platinum, instant payment, earn money');
			
			break;
	  	case CONTENT_CHECKOUT_CONFIRMATION:
		    define('META_TAG_TITLE', $browser_title . HEADING_TITLE . PRIMARY_SECTION . TITLE . $web_site_tagline);
			//define('META_TAG_DESCRIPTION', TITLE . PRIMARY_SECTION . HEADING_TITLE . SECONDARY_SECTION . WEB_SITE_KEYWORDS);
			define('META_TAG_KEYWORDS', WEB_SITE_KEYWORDS . HEADING_TITLE);
			
			break;
		case CONTENT_CHECKOUT_INFO:
	  	case CONTENT_CHECKOUT_PAYMENT:
	  	case CONTENT_CHECKOUT_PAYMENT_ADDRESS:
	 	case CONTENT_CHECKOUT_SHIPPING:
	 	case CONTENT_CHECKOUT_SHIPPING_ADDRESS:
		    define('META_TAG_TITLE', $browser_title . HEADING_TITLE . PRIMARY_SECTION . TITLE . $web_site_tagline);
			//define('META_TAG_DESCRIPTION', TITLE . PRIMARY_SECTION . HEADING_TITLE . SECONDARY_SECTION . WEB_SITE_KEYWORDS);
			define('META_TAG_KEYWORDS', WEB_SITE_KEYWORDS . HEADING_TITLE);
			
			break;
	  	case CONTENT_CHECKOUT_SUCCESS:
		    define('META_TAG_TITLE', $browser_title . HEADING_TITLE . PRIMARY_SECTION . TITLE . $web_site_tagline);
			//define('META_TAG_DESCRIPTION', TITLE . PRIMARY_SECTION . HEADING_TITLE . SECONDARY_SECTION . WEB_SITE_KEYWORDS);
			define('META_TAG_KEYWORDS', WEB_SITE_KEYWORDS . HEADING_TITLE);
			
			break;
	  	case CONTENT_CREATE_ACCOUNT_SUCCESS:
		    define('META_TAG_TITLE', $browser_title . HEADING_TITLE . PRIMARY_SECTION . TITLE . $web_site_tagline);
			//define('META_TAG_DESCRIPTION', TITLE . PRIMARY_SECTION . HEADING_TITLE . SECONDARY_SECTION . WEB_SITE_KEYWORDS);
			define('META_TAG_KEYWORDS', WEB_SITE_KEYWORDS . HEADING_TITLE);
			
			break;
	  	case CONTENT_INDEX_DEFAULT:
		    $browser_title = preg_replace("'(.*?)(".preg_quote(PRIMARY_SECTION).")'is", "\\1", $browser_title);
		    define('META_TAG_TITLE', $browser_title . HEADING_TITLE);
			//define('META_TAG_DESCRIPTION', TITLE . PRIMARY_SECTION . HEADING_TITLE . SECONDARY_SECTION . WEB_SITE_KEYWORDS);
			define('META_TAG_KEYWORDS', WEB_SITE_KEYWORDS . HEADING_TITLE);
			
		    break;
	  	case CONTENT_INDEX_NESTED:
		    $mt_category_query = tep_db_query("select IF(categories_heading_title IS NULL, categories_name, categories_heading_title) as cat_title from " . TABLE_CATEGORIES_DESCRIPTION . " where categories_id = '" . (int)$current_category_id . "' and language_id = '" . (int)$languages_id . "'");
		    $mt_category = tep_db_fetch_array($mt_category_query);
		    
		    define('META_TAG_TITLE', $browser_title . strip_tags($mt_category['cat_title']) . PRIMARY_SECTION . TITLE . $web_site_tagline);
			//define('META_TAG_DESCRIPTION', TITLE . PRIMARY_SECTION . $mt_category['categories_name']) . SECONDARY_SECTION . WEB_SITE_KEYWORDS;
			define('META_TAG_KEYWORDS', WEB_SITE_KEYWORDS . strip_tags($mt_category['cat_title']));
			
		    break;
	  	case CONTENT_INDEX_PRODUCTS:
	    	if (isset($HTTP_GET_VARS['manufacturers_id'])) {
		  		$mt_manufacturer_query = tep_db_query("select manufacturers_name from " . TABLE_MANUFACTURERS . " where manufacturers_id = '" . (int)$HTTP_GET_VARS['manufacturers_id'] . "'");
	      		$mt_manufacturer = tep_db_fetch_array($mt_manufacturer_query);
		  		
		  		define('META_TAG_TITLE', $browser_title . $mt_manufacturer['manufacturers_name'] . PRIMARY_SECTION . TITLE . $web_site_tagline);
		  		//define('META_TAG_DESCRIPTION', TITLE . PRIMARY_SECTION . $mt_manufacturer['manufacturers_name']) . SECONDARY_SECTION . WEB_SITE_KEYWORDS;
		  		define('META_TAG_KEYWORDS', WEB_SITE_KEYWORDS . $mt_manufacturer['manufacturers_name']);
			} else {
	      		$mt_category_query = tep_db_query("select IF(categories_heading_title IS NULL, categories_name, categories_heading_title) as cat_title from " . TABLE_CATEGORIES_DESCRIPTION . " where categories_id = '" . (int)$current_category_id . "' and language_id = '" . (int)$languages_id . "'");
	      		$mt_category = tep_db_fetch_array($mt_category_query);
		  		
		  		define('META_TAG_TITLE', $browser_title . strip_tags($mt_category['cat_title']) . PRIMARY_SECTION . TITLE . $web_site_tagline);
		  		//define('META_TAG_DESCRIPTION', TITLE . PRIMARY_SECTION . $mt_category['categories_name']) . SECONDARY_SECTION . WEB_SITE_KEYWORDS;
		  		define('META_TAG_KEYWORDS', WEB_SITE_KEYWORDS . strip_tags($mt_category['cat_title']));
	    	}
			
	    	break;
	  	case CONTENT_POPUP_IMAGE:
		    define('META_TAG_TITLE', $browser_title . $products['products_name'] . PRIMARY_SECTION . TITLE . $web_site_tagline);
			//define('META_TAG_DESCRIPTION', TITLE . PRIMARY_SECTION . $products['products_name'] . SECONDARY_SECTION . WEB_SITE_KEYWORDS);
			define('META_TAG_KEYWORDS', WEB_SITE_KEYWORDS . $products['products_name']);
			
	    	break;
	  	case CONTENT_POPUP_SEARCH_HELP:
	    	define('META_TAG_TITLE', $browser_title . HEADING_SEARCH_HELP . PRIMARY_SECTION . TITLE . $web_site_tagline);
			//define('META_TAG_DESCRIPTION', TITLE . PRIMARY_SECTION . HEADING_SEARCH_HELP . SECONDARY_SECTION . WEB_SITE_KEYWORDS);
			define('META_TAG_KEYWORDS', WEB_SITE_KEYWORDS . HEADING_SEARCH_HELP);
			
	    	break;
	  	case CONTENT_PRODUCT_INFO:
	  	case CONTENT_CUSTOM_PRODUCT_INFO:
	    	$mt_product_info_query = tep_db_query("select p.products_id, p.products_display, pd.products_name, pd.products_description, p.products_model, p.products_price, p.products_tax_class_id from " . TABLE_PRODUCTS . " p, " . TABLE_PRODUCTS_DESCRIPTION . " pd where p.products_status = '1' and p.products_id = '" . (int)$HTTP_GET_VARS['products_id'] . "' and pd.products_id = p.products_id and pd.language_id = '" . (int)$languages_id . "'");
	    	
	    	if ($mt_product_info = tep_db_fetch_array($mt_product_info_query)) {
				if ($mt_product_info["products_display"]) {
			    	if ($mt_new_price = tep_get_products_special_price($mt_product_info['products_id'])) {
			      		$mt_products_price = $currencies->display_price($mt_product_info['products_id'], $mt_product_info['products_price'], tep_get_tax_rate($mt_product_info['products_tax_class_id'])) . $currencies->display_price($mt_product_info['products_id'], $mt_new_price, tep_get_tax_rate($mt_product_info['products_tax_class_id']));
			    	} else {
			      		$mt_products_price = $currencies->display_price($mt_product_info['products_id'], $mt_product_info['products_price'], tep_get_tax_rate($mt_product_info['products_tax_class_id']));
			    	}
					
			    	if (tep_not_null($mt_product_info['products_model'])) {
			      		$mt_products_name = $mt_product_info['products_name'] . ' [' . $mt_product_info['products_model'] . ']';
			    	} else {
			      		$mt_products_name = $mt_product_info['products_name'];
			    	}
				} else {
					$mt_products_name = TEXT_PRODUCT_NOT_FOUND;
					$mt_products_price = '';
				}
			} else {
				$mt_products_name = TEXT_PRODUCT_NOT_FOUND;
				$mt_products_price = '';
			}
			
			$mt_products_name = strip_tags($mt_products_name);
			$mt_products_description = substr(strip_tags(stripslashes($mt_product_info['products_description'])), 0, 100);
			
			if ($content == CONTENT_PRODUCT_INFO)
	    		define('META_TAG_TITLE', $browser_title . $mt_products_name . SECONDARY_SECTION . $mt_products_price . (tep_not_null($mt_products_price) ? PRIMARY_SECTION : '') . TITLE . $web_site_tagline);
	    	else
	    		define('META_TAG_TITLE', $browser_title . $mt_products_name . PRIMARY_SECTION . TITLE . $web_site_tagline);
			//define('META_TAG_DESCRIPTION', TITLE . PRIMARY_SECTION . $mt_products_name . SECONDARY_SECTION . $mt_products_description . '...');
			define('META_TAG_KEYWORDS', WEB_SITE_KEYWORDS . $mt_products_name);
			
	    	break;
	  	case CONTENT_PRODUCT_REVIEWS:
	    	$mt_review_query = tep_db_query("select p.products_id, pd.products_name, p.products_model, p.products_price, p.products_tax_class_id from " . TABLE_PRODUCTS . " p, " . TABLE_PRODUCTS_DESCRIPTION . " pd where p.products_status = '1' and p.products_id = '" . (int)$HTTP_GET_VARS['products_id'] . "' and pd.products_id = p.products_id and pd.language_id = '" . (int)$languages_id . "'");
	    	$mt_review = tep_db_fetch_array($mt_review_query);
			
	    	if ($mt_new_price = tep_get_products_special_price($mt_review['products_id'])) {
	      		$mt_products_price = $currencies->display_price($mt_review['products_id'], $mt_review['products_price'], tep_get_tax_rate($mt_review['products_tax_class_id'])) . $currencies->display_price($mt_review['products_id'], $mt_new_price, tep_get_tax_rate($mt_review['products_tax_class_id']));
	    	} else {
	      		$mt_products_price = $currencies->display_price($mt_review['products_id'], $mt_review['products_price'], tep_get_tax_rate($mt_review['products_tax_class_id']));
	    	}
			
	    	if (tep_not_null($mt_review['products_model'])) {
	      		$mt_products_name = $mt_review['products_name'] . ' [' . $mt_review['products_model'] . ']';
	    	} else {
	      		$mt_products_name = $mt_review['products_name'];
	    	}
			
	    	define('META_TAG_TITLE', $browser_title . $mt_products_name . SECONDARY_SECTION . $mt_products_price . PRIMARY_SECTION . TITLE . TERTIARY_SECTION . NAVBAR_TITLE);
			//define('META_TAG_DESCRIPTION', TITLE . PRIMARY_SECTION . NAVBAR_TITLE . SECONDARY_SECTION . $mt_products_name . SECONDARY_SECTION . $mt_products_price);
			define('META_TAG_KEYWORDS', WEB_SITE_KEYWORDS . $mt_products_name);
			
	    	break;
	  	case CONTENT_PRODUCT_REVIEWS_INFO:
	    	$mt_review_query = tep_db_query("select rd.reviews_text, r.reviews_rating, r.reviews_id, r.customers_name, p.products_id, p.products_price, p.products_tax_class_id, p.products_model, pd.products_name from " . TABLE_REVIEWS . " r, " . TABLE_REVIEWS_DESCRIPTION . " rd, " . TABLE_PRODUCTS . " p, " . TABLE_PRODUCTS_DESCRIPTION . " pd where r.reviews_id = '" . (int)$HTTP_GET_VARS['reviews_id'] . "' and r.reviews_id = rd.reviews_id and rd.languages_id = '" . (int)$languages_id . "' and r.products_id = p.products_id and p.products_status = '1' and p.products_id = pd.products_id and pd.language_id = '". (int)$languages_id . "'");
	    	$mt_review = tep_db_fetch_array($mt_review_query);
			
	    	if ($mt_new_price = tep_get_products_special_price($mt_review['products_id'])) {
	      		$mt_products_price = $currencies->display_price($mt_review['products_id'], $mt_review['products_price'], tep_get_tax_rate($mt_review['products_tax_class_id'])) . $currencies->display_price($mt_review['products_id'], $mt_new_price, tep_get_tax_rate($mt_review['products_tax_class_id']));
	    	} else {
	      		$mt_products_price = $currencies->display_price($mt_review['products_id'], $mt_review['products_price'], tep_get_tax_rate($mt_review['products_tax_class_id']));
	    	}
			
	    	if (tep_not_null($mt_review['products_model'])) {
	      		$mt_products_name = $mt_review['products_name'] . ' [' . $mt_review['products_model'] . ']';
	    	} else {
	      		$mt_products_name = $mt_review['products_name'];
	    	}
			
			$mt_review_text = substr(strip_tags(stripslashes($mt_review['reviews_text'])), 0, 60);
	    	$mt_reviews_rating = SUB_TITLE_RATING . ' ' . sprintf(TEXT_OF_5_STARS, $mt_review['reviews_rating']);
			
	    	define('META_TAG_TITLE', $browser_title . $mt_products_name . SECONDARY_SECTION . $mt_products_price . PRIMARY_SECTION . TITLE . TERTIARY_SECTION . NAVBAR_TITLE);
			//define('META_TAG_DESCRIPTION', TITLE . PRIMARY_SECTION . NAVBAR_TITLE . SECONDARY_SECTION . $mt_products_name . SECONDARY_SECTION . $mt_review['customers_name'] . SECONDARY_SECTION . $mt_review_text . '...' . SECONDARY_SECTION . $mt_reviews_rating);
			define('META_TAG_KEYWORDS', WEB_SITE_KEYWORDS . $mt_products_name . ' ' . $mt_products_price . ' ' . $mt_review['customers_name'] . ' ' . $mt_reviews_rating);
			
	    	break;
        case CONTENT_SEARCH_LATEST_NEWS:
            if ($_GET['news_id'] > 0) {
                $this_site_id = 0;
                if (defined('SITE_ID')) {
                    $this_site_id = SITE_ID;
                }

                $news_display_sites_where_str = "FIND_IN_SET( '" . $this_site_id . "', news_display_sites)";
                $status_check_where_clause = "where ln.status = '1' and $news_display_sites_where_str";
                $news_sql = "   SELECT lnd.news_id, lnd.headline, lnd.latest_news_summary
                                FROM ". TABLE_LATEST_NEWS. "  as ln 
                                INNER JOIN " . TABLE_LATEST_NEWS_DESCRIPTION . "  as lnd 
                                    ON (ln.news_id = lnd .news_id) $status_check_where_clause  
                                AND ( IF(lnd.language_id = '" . $_SESSION['languages_id'] . "' && lnd.headline <> '', 1, 
                                          IF ((SELECT count(lnd.news_id) > 0 from  " . TABLE_LATEST_NEWS_DESCRIPTION . " as lnd
                                                WHERE " . $news_display_sites_where_str . " 
                                                AND lnd.news_id ='" . $_GET['news_id'] . "' 
                                                AND lnd.language_id ='" . $_SESSION['languages_id'] . "'
                                                AND lnd.headline <> ''), 
                                                0,
                                                lnd.language_id = '".$default_languages_id."')
                                                )
                                             ) 
                                AND lnd.headline <> '' 
                                AND lnd.news_id ='" . $_GET['news_id'] . "'"; 

                $news_query = tep_db_query($news_sql);
                $news_row = tep_db_fetch_array($news_query);
                
                define('META_TAG_TITLE', $news_row['headline'] . PRIMARY_SECTION . strip_tags(NAVBAR_TITLE) . PRIMARY_SECTION . LOCAL_STORE_NAME);
                if (tep_not_null($news_row['latest_news_summary'])) {
                    define('META_TAG_DESCRIPTION', $news_row['headline'] . ',' . $news_row['latest_news_summary']);
                } else {
                    define('META_TAG_DESCRIPTION', $news_row['headline']);
                }
                define('META_TAG_KEYWORDS', WEB_SITE_KEYWORDS . NAVBAR_TITLE);
            } else {
                define('META_TAG_TITLE', strip_tags(NAVBAR_TITLE) . PRIMARY_SECTION . TITLE . $web_site_tagline);
                define('META_TAG_KEYWORDS', WEB_SITE_KEYWORDS . NAVBAR_TITLE);
            }
            break;
	  	default:
	    	define('META_TAG_TITLE', $browser_title . strip_tags(NAVBAR_TITLE) . PRIMARY_SECTION . TITLE . $web_site_tagline);
			//define('META_TAG_DESCRIPTION', TITLE . PRIMARY_SECTION . NAVBAR_TITLE . SECONDARY_SECTION . WEB_SITE_KEYWORDS);
			define('META_TAG_KEYWORDS', WEB_SITE_KEYWORDS . NAVBAR_TITLE);
	}
	
	if (!defined('META_TAG_ROBOTS')) {
		define('META_TAG_ROBOTS', '1');
	}
}
?>