<?
/*
  	$Id: address_book_details.php,v 1.7 2010/09/23 12:31:45 weichen Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2003 osCommerce
	
  	Released under the GNU General Public License
*/

if (!isset($process)) $process = false;

$customer_account_select_sql = "SELECT customers_gender, customers_firstname, customers_lastname FROM " . TABLE_CUSTOMERS ." WHERE customers_id = '" . (int)$customer_id . "'";
$customer_account_result_sql = tep_db_query($customer_account_select_sql);
$customer_account_row = tep_db_fetch_array($customer_account_result_sql);

if ((isset($HTTP_GET_VARS['edit']) && ($_SESSION['customer_default_address_id'] != $HTTP_GET_VARS['edit'])) || (isset($HTTP_GET_VARS['edit']) == false) ) {
	$editable_name = true;
} else {
	$editable_name = false;
}
?>
	<script language="javascript" src="includes/javascript/customer_xmlhttp.js"></script>
	<table border="0" width="100%" cellspacing="0" cellpadding="2" class="inputBox">
  		<tr class="inputContents">
    		<td>
    			<table border="0" width="100%" cellspacing="0" cellpadding="2">
      				<tr>
        				<td class="main"><b><?=NEW_ADDRESS_TITLE?></b></td>
        				<td class="requiredInfo" align="right"><?=FORM_REQUIRED_INFORMATION?></td>
      				</tr>
    			</table>
    		</td>
  		</tr>
  		<tr>
    		<td>
    			<table border="0" width="100%" cellspacing="1" cellpadding="2" class="inputNestedBox">
      				<tr class="inputNestedBoxContents">
        				<td>
        					<table border="0" cellspacing="2" cellpadding="2">
        					<?/*<tbody id="editable_name" class="<?=$editable_name ? "show" : "hide"?>">*/?>
        						<tr>
            						<td class="inputLabel"><?=ENTRY_FIRST_NAME?></td>
            						<td class="inputField"><?=tep_draw_input_field('firstname', $entry['entry_firstname']) . '&nbsp;' . (tep_not_null(ENTRY_FIRST_NAME_TEXT) ? '<span class="requiredInfo">' . ENTRY_FIRST_NAME_TEXT . '</span>': '')?></td>
          						</tr>
          						<tr>
            						<td class="inputLabel"><?=ENTRY_LAST_NAME?></td>
            						<td class="inputField"><?=tep_draw_input_field('lastname', $entry['entry_lastname']) . '&nbsp;' . (tep_not_null(ENTRY_LAST_NAME_TEXT) ? '<span class="requiredInfo">' . ENTRY_LAST_NAME_TEXT . '</span>': '')?></td>
          						</tr>
<?
  	if (ACCOUNT_GENDER == 'true') {
    	if (isset($gender)) {
      		$male = ($gender == 'm') ? true : false;
    	} else {
      		$male = ($entry['entry_gender'] == 'm') ? true : false;
    	}
    	$female = !$male;
?>
          						<tr>
            						<td class="inputLabel"><?=ENTRY_GENDER?></td>
            						<td class="inputField"><?=tep_draw_radio_field('gender', 'm', $male) . '&nbsp;&nbsp;' . MALE . '&nbsp;&nbsp;' . tep_draw_radio_field('gender', 'f', $female) . '&nbsp;&nbsp;' . FEMALE . '&nbsp;' . (tep_not_null(ENTRY_GENDER_TEXT) ? '<span class="requiredInfo">' . ENTRY_GENDER_TEXT . '</span>': '')?></td>
          						</tr>
<?	} ?>
						 <? //</tbody> ?>
						 <?	/*<tbody id="noneditable_name" class="<?=$editable_name ? "hide" : "show"?>">
        						<tr>
            						<td class="inputLabel"><?=ENTRY_FIRST_NAME?></td>
            						<td class="inputField"><?=$customer_account_row["customers_firstname"]?></td>
          						</tr>
          						<tr>
            						<td class="inputLabel"><?=ENTRY_LAST_NAME?></td>
            						<td class="inputField"><?=$customer_account_row["customers_lastname"]?></td>
          						</tr>
<?
  	if (ACCOUNT_GENDER == 'true') {
  		$gender_str = $customer_account_row["customers_gender"] == 'm' ? MALE : FEMALE;
?>
          						<tr>
            						<td class="inputLabel"><?=ENTRY_GENDER?></td>
            						<td class="inputField"><?=$gender_str?></td>
          						</tr>
<?	} ?>
							</tbody>*/ ?>
          						<tr>
            						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
          						</tr>
<?	if (ACCOUNT_COMPANY == 'true') { ?>
          						<tr>
						            <td class="inputLabel"><?=ENTRY_COMPANY?></td>
						            <td class="inputField"><?=tep_draw_input_field('company', $entry['entry_company']) . '&nbsp;' . (tep_not_null(ENTRY_COMPANY_TEXT) ? '<span class="requiredInfo">' . ENTRY_COMPANY_TEXT . '</span>': '')?></td>
          						</tr>
          						<tr>
            						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
          						</tr>
<?	} ?>
          						<tr>
						            <td class="inputLabel"><?=ENTRY_STREET_ADDRESS?></td>
						            <td class="inputField"><?=tep_draw_input_field('street_address', $entry['entry_street_address']) . '&nbsp;' . (tep_not_null(ENTRY_STREET_ADDRESS_TEXT) ? '<span class="requiredInfo">' . ENTRY_STREET_ADDRESS_TEXT . '</span>': '')?></td>
          						</tr>
<?	if (ACCOUNT_SUBURB == 'true') { ?>
          						<tr>
            						<td class="inputLabel"><?=ENTRY_SUBURB?></td>
            						<td class="inputField"><?=tep_draw_input_field('suburb', $entry['entry_suburb']) . '&nbsp;' . (tep_not_null(ENTRY_SUBURB_TEXT) ? '<span class="requiredInfo">' . ENTRY_SUBURB_TEXT . '</span>': '')?></td>
          						</tr>
<?  } ?>
          						<tr>
            						<td class="inputLabel"><?=ENTRY_POST_CODE?></td>
            						<td class="inputField"><?=tep_draw_input_field('postcode', $entry['entry_postcode']) . '&nbsp;' . (tep_not_null(ENTRY_POST_CODE_TEXT) ? '<span class="requiredInfo">' . ENTRY_POST_CODE_TEXT . '</span>': '')?></td>
          						</tr>
          						<tr>
            						<td class="inputLabel"><?=ENTRY_CITY?></td>
            						<td class="inputField"><?=tep_draw_input_field('city', $entry['entry_city']) . '&nbsp;' . (tep_not_null(ENTRY_CITY_TEXT) ? '<span class="requiredInfo">' . ENTRY_CITY_TEXT . '</span>': '')?></td>
          						</tr>
          						<tr>
            						<td class="inputLabel"><?=ENTRY_COUNTRY?></td>
            						<td class="inputField"><?=tep_get_country_list('country', $entry['entry_country_id'], 'onChange="refreshDynamicSelectOptions(this, \'state_div\', \'state\', \''.(int)$languages_id.'\', true);"') . '&nbsp;' . (tep_not_null(ENTRY_COUNTRY_TEXT) ? '<span class="requiredInfo">' . ENTRY_COUNTRY_TEXT . '</span>': '')?></td>
          						</tr>
<?	if (ACCOUNT_STATE == 'true') { ?>
          						<tr>
            						<td class="inputLabel"><?=ENTRY_STATE?></td>
            						<td class="inputField">
            							<div id="state_div" style="float:left;">
<?
		$zones_array = array( array('id' => '', 'text' => PULL_DOWN_DEFAULT) );
		if ($process == true) {
	      	if ($entry_state_has_zones == true) {
	        	$zones_select_sql = "SELECT zone_id, zone_name FROM " . TABLE_ZONES . " WHERE zone_country_id = '" . (int)$country . "' ORDER BY zone_name";
	        	$zones_result_sql = tep_db_query($zones_select_sql);
	        	while ($zones_row = tep_db_fetch_array($zones_result_sql)) {
	          		$zones_array[] = array('id' => $zones_row['zone_id'], 'text' => $zones_row['zone_name']);
	        	}
	        	echo tep_draw_pull_down_menu('state', $zones_array, $state, 'id="state"');
	      	} else {
	        	echo tep_draw_input_field('state', $state, 'id="state"');
	      	}
	    } else {
	    	$cur_zone_name = tep_get_zone_name($entry['entry_country_id'], $entry['entry_zone_id'], '');
	    	if (tep_not_null($cur_zone_name)) {
	    		$zones_select_sql = "SELECT zone_id, zone_name FROM " . TABLE_ZONES . " WHERE zone_country_id = '" . (int)$entry['entry_country_id'] . "' ORDER BY zone_name";
	        	$zones_result_sql = tep_db_query($zones_select_sql);
	        	while ($zones_row = tep_db_fetch_array($zones_result_sql)) {
	          		$zones_array[] = array('id' => $zones_row['zone_id'], 'text' => $zones_row['zone_name']);
	        	}
	        	echo tep_draw_pull_down_menu('state', $zones_array, $entry['entry_zone_id'], 'id="state"');
	    	} else {
	    		echo tep_draw_input_field('state', $entry['entry_state'], 'id="state"');
	    	}
	    }
    	
		echo '							</div>';
    	if (tep_not_null(ENTRY_STATE_TEXT)) echo '&nbsp;<span class="requiredInfo">' . ENTRY_STATE_TEXT;
?>
									</td>
          						</tr>
<?	}
	
	if ((isset($HTTP_GET_VARS['edit']) && ($_SESSION['customer_default_address_id'] != $HTTP_GET_VARS['edit'])) || (isset($HTTP_GET_VARS['edit']) == false) ) { ?>
          						<tr>
            						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
          						</tr>
          						<tr>
            						<?/*<td colspan="2" class="inputField"><?=tep_draw_checkbox_field('primary', 'on', false, 'id="primary" onClick="primaryAccountSelection(this);"') . ' ' . SET_AS_PRIMARY?></td>*/?>
          							<td colspan="2" class="inputField"><?=tep_draw_checkbox_field('primary', 'on', false, 'id="primary"') . ' ' . SET_AS_PRIMARY?></td>
          						</tr>
<? 	} ?>
        					</table>
        				</td>
      				</tr>
    			</table>
    		</td>
  		</tr>
	</table>
	<script>
	<!--
		function primaryAccountSelection(primary_obj) {
			if (primary_obj.checked) {
				document.getElementById('editable_name').className = "hide";
				document.getElementById('noneditable_name').className = "show";
			} else {
				document.getElementById('editable_name').className = "show";
				document.getElementById('noneditable_name').className = "hide";
			}
		}
	//-->
	</script>