<?
/*
	$Id: also_purchased_products.php,v 1.6 2011/08/04 09:34:31 weichen Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2003 osCommerce
	
  	Released under the GNU General Public License
*/

if (isset($_REQUEST["products_id"])) {
	$also_purchased_select_sql = "	SELECT p.products_id, pd.products_image 
									FROM " . TABLE_ORDERS_PRODUCTS . " opa, " . 
										TABLE_ORDERS_PRODUCTS . " opb, " . 
										TABLE_ORDERS . " o, " . 
										TABLE_PRODUCTS . " p, " . 
										TABLE_PRODUCTS_DESCRIPTION . " pd 
									WHERE opa.products_id = '" . (int)$_REQUEST["products_id"] . "' 
										AND opa.products_bundle_id = 0 
										AND opb.products_bundle_id = 0 
										AND opa.orders_id = opb.orders_id 
										AND opb.products_id <> '" . (int)$_REQUEST["products_id"] . "' 
										AND opb.products_id = p.products_id AND opb.orders_id = o.orders_id 
										AND p.products_status = '1' 
										AND ( p.products_bundle='yes' OR p.products_bundle_dynamic='yes' OR (p.products_bundle<>'yes' AND p.products_bundle_dynamic<>'yes' AND p.products_display='1'))
										AND pd.language_id = '1'								
									GROUP BY p.products_id 
									ORDER BY o.date_purchased desc limit " . MAX_DISPLAY_ALSO_PURCHASED ;
	$orders_query = tep_db_query($also_purchased_select_sql);
	//$orders_query = tep_db_query("select p.products_id, p.products_image from " . TABLE_ORDERS_PRODUCTS . " opa, " . TABLE_ORDERS_PRODUCTS . " opb, " . TABLE_ORDERS . " o, " . TABLE_PRODUCTS . " p where opa.products_id = '" . (int)$HTTP_GET_VARS['products_id'] . "' and opa.orders_id = opb.orders_id and opb.products_id != '" . (int)$HTTP_GET_VARS['products_id'] . "' and opb.products_id = p.products_id and opb.orders_id = o.orders_id and p.products_status = '1' group by p.products_id order by o.date_purchased desc limit " . MAX_DISPLAY_ALSO_PURCHASED);
    $num_products_ordered = tep_db_num_rows($orders_query);
    if ($num_products_ordered >= MIN_DISPLAY_ALSO_PURCHASED) {
?>
<!-- also_purchased_products //-->
<?
		$info_box_contents = array();
      	$info_box_contents[] = array(	'params' => 'class="latestNewsBoxHeading"',
      									'text' => TEXT_ALSO_PURCHASED_PRODUCTS);
		
      	new contentBoxHeading($info_box_contents);
		
      	$row = 0;
      	$col = 0;
      	$info_box_contents = array();
      	while ($orders = tep_db_fetch_array($orders_query)) {
        	$orders['products_name'] = tep_get_products_name($orders['products_id']);
        	if (tep_not_null($orders['products_image'])) {
				$img_source = tep_image(THEMA_IMAGES."products/" . $orders['products_image'], $orders['products_name'], '', '');
      		} else {
      			$pro_img = "label_nopic.gif";
      			$img_source = tep_image(THEMA_IMAGES."products/" . $pro_img, $orders['products_name'], '', '');
      		}
        	
        	$prod_cPath = tep_get_product_path($orders['products_id']);
        	$lc_text = '<table width="130" border="0" cellspacing="0" cellpadding="0"><tr><td height="168" align="center" valign="center" style="background:url(\''.THEMA_IMAGES.'box_products.gif'.'\'); background-repeat:no-repeat; background-position:center center;"><a href="' . tep_href_link(FILENAME_PRODUCT_INFO, 'cPath='.$prod_cPath.'&products_id=' . $orders['products_id']) . '" class="productNavigation">' . $img_source . '</a></td></tr><tr><td height="22" align="center"><a href="' . tep_href_link(FILENAME_PRODUCT_INFO, 'cPath='.$prod_cPath.'&products_id=' . $orders['products_id']) . '">' . $orders['products_name'] . '</a></td></tr></table>';
        	
        	$info_box_contents[$row][$col] = array(	'align' => 'center',
        											'valign' => 'top',
                                               		'params' => '',
                                               		'text' => $lc_text
                                               		);
			
        	$col ++;
        	if ($col > 2) {
          		$col = 0;
          		$row ++;
        	}
      	}
		new contentBox($info_box_contents, 'class="latestNewsBox"', 'class="latestNewsBoxContents"');
      	//new contentBox($info_box_contents);
?>
<!-- also_purchased_products_eof //-->
<?
	}
}
?>