<?
include_once(DIR_FS_CATALOG_MODULES . 'anti_fraud/genesis/admin/languages/'.$language.'/genesis_inc.lng.php');

if ($order->info['orders_aft_executed'] == -1) {
	$genesis_status = 0;	// 0 means Force not to rerun
} else {
	$genesis_status = 1;
}
?>
<table border="0" cellspacing="0" cellpadding="0">
	<tr>
		<td class="pageHeading" valign="top" colspan="2">
			<b><?=TABLE_HEADING_GENESIS_PROJECT?></b>
		</td>
	</tr>
	<tr>
		<td><BR></td>
	</tr>
	<tr>
		<td>
			<table border="0" cellspacing="2" cellpadding="0" width="100%">
				<tr>
					<td class="main"><b><?=ENTRY_STATUS?></b></td>
					<td><?=tep_switch_image($genesis_status, tep_href_link(FILENAME_ORDERS, tep_get_all_get_params().'subaction=set_orders_aft_executed&orders_aft_executed=0'), tep_href_link(FILENAME_ORDERS, tep_get_all_get_params().'subaction=set_orders_aft_executed&orders_aft_executed=-1'))?></td>
				</tr>
			</table>
		</td>
	</tr>
<?	if ($order->info['orders_aft_executed'] != -1) { ?>
	<tr>
		<td><BR></td>
	</tr>
	<tr>
		<td colspan="2">
			<?=tep_draw_form('reexecute_aft_live', FILENAME_ORDERS, tep_get_all_get_params(), 'post', '')?>
				<table border="0" cellspacing="0" cellpadding="0">
					<tr>
						<td>
						<?
							echo tep_draw_hidden_field('subaction', 'reexecute_genesis');
							echo tep_submit_button(BUTTON_EXECUTE_AFT_LIVE, ALT_BUTTON_EXECUTE_AFT_LIVE, ' onclick="rerun_genesis_project(this.form);" style="padding: 5px;" ', 'inputButton');
						?>
						</td>
					</tr>
				</table>
			</form>
		</td>
	</tr>
<?	} ?>
</table>