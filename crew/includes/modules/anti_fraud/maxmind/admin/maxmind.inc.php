<?php
/*
  	$Id: maxmind.inc.php,v 1.47 2015/10/21 09:34:56 sionghuat.chng Exp $
	
  	Developer: <PERSON><PERSON>
  	Copyright (c) 2005 SKC Venture
	
  	Released under the GNU General Public License
*/

include_once(DIR_FS_CATALOG_MODULES . 'anti_fraud/maxmind/admin/languages/'.$language.'/maxmind_inc.lng.php');

$maxmind_telephone_info_row = FALSE;    // maxmind info is not required to show.
$phone_identification_supported_country_array = array('Canada', 'United States');

$maxmind_obj = new ogm_maxmind();

if ($maxmind_obj->is_supported($order->info['payment_methods_parent_id'])) {
    $customer_maxmind_history_select_sql = "SELECT * 
                                            FROM " . TABLE_MAXMIND_HISTORY . " 
                                            WHERE orders_id = '" . tep_db_input($oID) ."'
                                            ORDER BY maxmind_history_date DESC 
                                            LIMIT 1";
	$customer_maxmind_history_result_sql = tep_db_query($customer_maxmind_history_select_sql);
	if ($maxmind_history_row = tep_db_fetch_array($customer_maxmind_history_result_sql)) {
    } else if ($order->info['orders_status'] == '7') {
		$genesis_order_select_sql = "	SELECT orders_id
										FROM " . TABLE_CRON_GENESIS_ORDERS . "
										WHERE orders_id = " . tep_db_input($oID);
		$genesis_order_result_sql = tep_db_query($genesis_order_select_sql);
		if ($genesis_order_row = tep_db_fetch_array($genesis_order_result_sql)) {
		} else {
			$maxmind_obj->crew_score_request($oID);
			$customer_maxmind_history_select_sql = "SELECT * 
													FROM " . TABLE_MAXMIND_HISTORY . " 
													WHERE orders_id = '" . tep_db_input($oID) . "' 
													ORDER BY maxmind_history_date DESC 
													LIMIT 1";
			$customer_maxmind_history_result_sql = tep_db_query($customer_maxmind_history_select_sql);
			$maxmind_history_row = tep_db_fetch_array($customer_maxmind_history_result_sql);   
		}
	}
	
    if ($maxmind_history_row) {
        $all_maxmind_history_select_sql = " SELECT * 
                                            FROM " . TABLE_MAXMIND_HISTORY . " 
                                            WHERE orders_id = '" . tep_db_input($oID) . "' 
                                                AND maxmind_history_date != '" .$maxmind_history_row['maxmind_history_date']. "'";
        $all_maxmind_history_result_sql = tep_db_query($all_maxmind_history_select_sql);

        if ($maxmind_history_row['maxmind_history_source'] == "Customer's Billing Address") {
            $country_select_sql = "SELECT billing_country AS country_name FROM " . TABLE_ORDERS . " WHERE orders_id = '" . tep_db_input($oID) . "'";
            $country_result = tep_db_query($country_select_sql);
            $country_row = tep_db_fetch_array($country_result);
        } else if ($maxmind_history_row['maxmind_history_source'] == "Customer Information") {
            $country_select_sql = "SELECT customers_country AS country_name FROM " . TABLE_ORDERS . " WHERE orders_id = '" . tep_db_input($oID) . "'";
            $country_result = tep_db_query($country_select_sql);
            $country_row = tep_db_fetch_array($country_result);
        } else if($maxmind_history_row['maxmind_history_source'] == "Paypal") {
            $country_select_sql = "SELECT address_country AS country_name FROM " . TABLE_PAYPAL . " WHERE invoice = '" . tep_db_input($oID) . "'";
            $country_result = tep_db_query($country_select_sql);
            $country_row = tep_db_fetch_array($country_result);
        } else if($maxmind_history_row['maxmind_history_source'] == "PaypalEC") {
            $country_select_sql = "SELECT address_country AS country_name FROM " . TABLE_PAYPALEC . " WHERE paypal_order_id = '" . tep_db_input($oID) . "'";
            $country_result = tep_db_query($country_select_sql);
            $country_row = tep_db_fetch_array($country_result);
		}
    }
    
    unset($maxmind_obj);
?>
    <table border="0" cellspacing="0" cellpadding="0" width="100%">
        <tr>
            <td colspan="2"><?=tep_draw_separator()?></td>
        </tr>
		<tr>
			<td class="pageHeading" valign="top" colspan="2"><b><?=TABLE_HEADING_MAXMIND_INFO?></b></td>
		</tr>
		<tr>
			<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
		</tr>
<?
    if ($maxmind_history_row) {
        if (tep_db_num_rows($all_maxmind_history_result_sql) > 0) {
?>
		<tr>
			<td>
				<table border="0" cellspacing="0" cellpadding="0">
					<tr>
						<td class="main"><b><?=ENTRY_MAXMIND_REQUESTED_HISTORY?></b></td>
					</tr>
<?
            while ($all_maxmind_history_row = tep_db_fetch_array($all_maxmind_history_result_sql)) {
                echo '<tr>
                            <td class="main">
                                <a href="javascript:openDGDialog(\''. tep_href_link(FILENAME_MAXMIND_HISTORY, 'maxmind_date='.$all_maxmind_history_row['maxmind_history_date'].'&order_id='.$all_maxmind_history_row['orders_id'].'') . '\', 1100, 310, \'\');" class="actionLink">'.tep_datetime_short($all_maxmind_history_row['maxmind_history_date'], PREFERRED_DATE_TIME_FORMAT).'&nbsp;'.$all_maxmind_history_row['score'].'</a>
                            </td>
                        </tr>';
            }
?>
				</table>
			</td>
		</tr>
<?
        }
?>
		<tr>
			<td>
				<table border="0" cellspacing="1" cellpadding="0" align="center">
					<tr>
<?
//		for ($maxmind_score_loop=0; $maxmind_score_loop<=10; $maxmind_score_loop++) {
//			echo '		<td class="main" valign="middle">';
//				
//			if ((int)$maxmind_history_row['score'] == $maxmind_score_loop) {
//				echo '		<div class="maxmind_'.$maxmind_score_loop.'_selected"><div style="_position:absolute; _top:50%; display:table-cell; vertical-align:middle;"><div style="_position:relative; _top:-50%; _left:-50%">'.$maxmind_score_loop.'</div></div></div>';
//			} else {
//				echo '		<div class="maxmind_'.$maxmind_score_loop.'_score">'.$maxmind_score_loop.'</div>';
//			}
//			echo '		</td>';
//		}
?>
					</tr>
				</table>
			</td>
		</tr>
		<tr>
			<td>
				<table border="0" cellspacing="0" cellpadding="0" width="100%">
					<tr>
						<td>
							<table border="0" cellspacing="0" cellpadding="0">
								<tr>
									<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
								</tr>
								<tr>
									<td class="main"><b><?=ENTRY_MAXMIND_ID?></b></td>
									<td class="main"><?='&nbsp;' . $maxmind_history_row['maxmind_id']?></td>
								</tr>
								<tr>
									<td class="main"><b><?=ENTRY_MAXMIND_HISTORY_DATE?></b></td>
									<td class="main"><?='&nbsp;' . tep_datetime_short($maxmind_history_row['maxmind_history_date'], PREFERRED_DATE_TIME_FORMAT)?></td>
								</tr>
								<tr>
									<td class="main"><b><?=CUSTOMERS_INFO_MESSAGE?></b></td>
									<td class="main"><?='&nbsp;' . $maxmind_history_row['maxmind_history_source']?></td>
								</tr>
                                <tr>
									<td class="main"><b><?=ENTRY_QUERIES_REMAINING?></b></td>
									<td class="main"><?='&nbsp;' . $maxmind_history_row['queries_remaining']?></td>
								</tr>
<?
        if (tep_not_null($maxmind_history_row['error'])) {
?>
								<tr>
									<td class="main"><b><?=ENTRY_ERROR?></b></td>
									<td class="main"><?='<span class="redIndicator">&nbsp;' . $maxmind_history_row['error'] . ERROR_MESSAGE?></td>
								</tr>
<?
        }
?>
								<tr>
									<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
								</tr>
							</table>
						</td>
					</tr>
				</table>
			</td>
		</tr>
		<tr>
			<td>
				<table border="0" width="100%" cellspacing="3" cellpadding="0">
					<tr>
						<td class="main" valign="top" style="width: 350px;">
							<table border="1" width="100%" cellspacing="0" cellpadding="0">
								<tr>
									<td class="main" nowrap>
										<b><?=ENTRY_IP_COUNTRY_CODE?></b>
									</td>
									<td class="main" align="center" nowrap><span id="span_maxmind_ip_country_code"></span></td>
								</tr>
								<tr>
									<td class="main" nowrap>
										<b><?=ENTRY_IP_REGION?></b>
									</td>
									<td class="main" align="center" nowrap><span id="span_maxmind_ip_region"></span></td>
								</tr>
								<tr>
									<td class="main" nowrap>
										<b><?=ENTRY_IP_CITY?></b>
									</td>
									<td class="main" align="center" nowrap><span id="span_maxmind_ip_city"></span></td>
								</tr>
								<tr>
									<td class="main" nowrap>
										<b><?=ENTRY_COUNTRY_MATCH?></b>
									</td>
									<td class="main" align="center" nowrap><span id="span_maxmind_country_match"></span></td>
								</tr>
								<tr>
									<td class="main" nowrap>
										<b><?=ENTRY_IP_AREA_CODE?></b>
									</td>
									<td class="main" align="center" nowrap><span id="span_maxmind_ip_area_code"></span></td>
								</tr>
                                <tr>
									<td class="main" nowrap>
										<b><?=ENTRY_IP_POSTAL_CODE?></b>
									</td>
									<td class="main" align="center" nowrap><span id="span_maxmind_ip_postal_code"></span></td>
								</tr>
                                <tr>
									<td class="main" nowrap>
										<b><?=ENTRY_IP_TIME_ZONE?></b>
									</td>
									<td class="main" align="center" nowrap><span id="span_maxmind_ip_time_zone"></span></td>
								</tr>
							</table>
						</td>
						<td class="main" valign="top" style="width: 350px;">
							<table border="1" width="100%" cellspacing="0" cellpadding="0">
								<tr>
									<td class="main" nowrap>
										<b><?=ENTRY_ANONYMOUS_PROXY?></b>
									</td>
									<td class="main" align="center" nowrap><span id="span_maxmind_anonymous_proxy"></span></td>
								</tr>
								<tr>
									<td class="main" nowrap>
										<b><?=ENTRY_PROXY_SCORE?></b>
									</td>
									<td class="main" align="center" nowrap><span id="span_maxmind_proxy_score"></span></td>
								</tr>
								<tr>
									<td class="main" nowrap>
										<b><?=ENTRY_TRANSPROXY?></b>
									</td>
									<td class="main" align = "center" nowrap><span id="span_maxmind_is_trans_proxy"></span></td>
								</tr>
                                <tr>
									<td class="main" nowrap>
										<b><?=ENTRY_CORPORATE_PROXY?></b>
									</td>
									<td class="main" align = "center" nowrap><span id="span_maxmind_ip_corporate_proxy"></span></td>
								</tr>
								<tr>
									<td class="main" nowrap>
										<b><?=ENTRY_IP_ISP?></b>
									</td>
									<td class="main" align="center" nowrap><span id="span_maxmind_ip_isp"></span></td>
								</tr>
								<tr>
									<td class="main" nowrap>
										<b><?=ENTRY_IP_ORGNIZATION?></b>
									</td>
									<td class="main" align="center" nowrap><span id="span_maxmind_ip_organization"></span></td>
								</tr>
							</table>
							<?=RISK_INFO_MESSAGE?>
						</td>
						<td class="main" valign="top">
							<table border="1" width="100%" cellspacing="0" cellpadding="0">
								<tr>
									<td class="main" nowrap>
										<b><?=ENTRY_IP_NET_SPEED_CELL?></b>
									</td>
									<td class="main" align="center" nowrap><span id="span_maxmind_ip_net_speed_cell"></span></td>
								</tr>
								<tr>
									<td class="main" nowrap>
										<b><?=ENTRY_IP_USER_TYPE?></b>
									</td>
									<td class="main" align="center" nowrap><span id="span_maxmind_ip_user_type"></span></td>
								</tr>
								<tr>
									<td class="main" nowrap>
										<b><?=ENTRY_DISTANCE?></b>
									</td>
									<td class="main" align="center" nowrap><span id="span_maxmind_distance"></span></td>
								</tr>
								<tr>
									<td class="main" nowrap>
										<b><?=ENTRY_IP_ACCURACY_RADIUS?></b>
									</td>
									<td class="main" align="center" nowrap><span id="span_maxmind_ip_accuracy_radius"></span></td>
								</tr>
								<tr>
									<td class="main" nowrap>
										<b><?=ENTRY_SCORE?></b>
									</td>
									<td class="main" align="center" nowrap><span id="span_maxmind_score"></span></td>
								</tr>
								<tr>
									<td class="main" nowrap>
										<b><?=ENTRY_HIGH_RISK_COUNTRY?>&nbsp;-&nbsp;<?echo $country_row['country_name']?>:</b>
									</td>
									<td class="main" align="center" nowrap><span id="span_maxmind_high_risk_country"></span></td>
								</tr>
							</table>
						</td>
					</tr>
					<tr>
						<td colspan="3"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
					</tr>
				</table>
                <script>
                jQuery.ajax({
                    type: 'get',
                    url:'anti_fraud_xmlhttp.php',
                    data:'action=get_maxmind_history&oID=<?=$oID?>&limit=1',
                    success: function(xml) {
                        jQuery(xml).find("maxmind").each(function(){
                            jQuery(this).find('info').each(function(){
                                if (jQuery("#span_maxmind_" + jQuery(this).find('key').text()).length > 0) {
                                    jQuery("#span_maxmind_" + jQuery(this).find('key').text()).html(jQuery(this).find('display').text());
                                }
                            });
                        });
                    }
                });
            </script>
			</td>
		</tr>
<?
	}
	
	if ($order->info['orders_status'] != '1') { ?>
		<tr>
			<td>
				<table border="0" width="100%" cellspacing="3" cellpadding="0">
					<tr>
						<td class="main"><b><?=TEXT_BIN_NUMBER?></b>&nbsp;<?=tep_draw_input_field('bin_number')?></td>
						<td class="main"><b><?=TEXT_BIN_NAME?></b>&nbsp;<?=tep_draw_input_field('bin_name')?></td>
						<td class="main"><b><?=TEXT_BIN_PHONE?></b>&nbsp;<?=tep_draw_input_field('bin_phone')?></td>	
					</tr>
					<tr>
						<td colspan="3" align="right">
						<?
							echo tep_draw_form('maxmind_request_form', FILENAME_ORDERS, tep_get_all_get_params(), 'post');
							echo tep_draw_hidden_field('subaction', 'maxmind_info_update');
							echo tep_submit_button(IMAGE_UPDATE, IMAGE_UPDATE, 'name="btn_maxmind_update"', 'inputButton');
							echo '</form>';
						?>
						</td>
					</tr>
				</table>
			</td>
		</tr>
<?
	}
?>
    </table>
<?php
}
?>