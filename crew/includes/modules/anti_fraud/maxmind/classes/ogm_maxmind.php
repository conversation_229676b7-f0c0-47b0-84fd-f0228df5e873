<?php
include_once dirname(__FILE__) . DIRECTORY_SEPARATOR . 'credit_card_fraud_detection.php';
include_once dirname(__FILE__) . DIRECTORY_SEPARATOR . 'telephone_verification.php';

class ogm_maxmind {
    const NOT_CHECK_KEYWORD = 'Not Check';
    const OLD_VERSION_API = 'PHP/1.4';
    private static $phone_identification_supported_country_array = array('Canada', 'United States');
    private static $no_pg_address_payment_array = array('adyen.php', 'payu.php','bibit.php', 'global_collect.php', 'paypal.php', 'moneybookers.php', 'worldpay.php', 'ipay88.php', 'googlewallet.php', 'smart2pay_globalpay.php', 'smart2pay.php', 'paypalec.php', 'coda.php');
	private $proxy_mode = false;
	private $maxmind_country_conversion, $api_url, $api_check_field, $countryCode;
    public $pg_filename = array();
    public $phone_type = array(
        'no-respond' => 0,
        'Fixed Line' => 1, 
        'Mobile' => 2,
        'PrePaid Mobile' => 3,
        'Toll-Free' => 4,
        'Non-Fixed VoIP' =>5,
        'Pager' => 6,
        'Payphone' => 7,
        'Invalid Number' => 8,
        'Restricted Number' => 9,
        'Personal' => 10,
        'Voicemail' => 11,
        'Other' => 20,
    );

    public function __construct($config = null) {
        $this->maxmind_country_conversion = array(
            'Russia' => 'Russian Federation', 
            'Slovakia' => 'Slovakia (Slovak Republic)', 
            'Viet Nam' => 'Vietnam', 
            'South Korea' => 'Korea, Republic of', 
            'Brunei' => 'Brunei Darussalam'
        );
	}
    
    public function get_latest_history($orders_id, $field = '*') {
        $maxmind_score_select_sql = "	SELECT " . $field . "
										FROM " . TABLE_MAXMIND_HISTORY . "
										WHERE orders_id = '" . tep_db_input($orders_id) . "'
										ORDER BY maxmind_history_date DESC
										LIMIT 1";
		$maxmind_score_result_sql = tep_db_query($maxmind_score_select_sql);
        if ($maxmind_score_row = tep_db_fetch_array($maxmind_score_result_sql)) {
            return $maxmind_score_row;
        } else {
            return FALSE;
        }
    }
    
    private function get_payment_method_filename($payment_methods_parent_id) {
        if (!isset($this->pg_filename[$payment_methods_parent_id])) {
            $payment_gateway_filename_select_sql = "SELECT payment_methods_filename 
                                                    FROM " . TABLE_PAYMENT_METHODS . "
                                                    WHERE payment_methods_id = '" . $payment_methods_parent_id . "'";
            $payment_gateway_filename_result_sql = tep_db_query($payment_gateway_filename_select_sql);
            if ($payment_gateway_filename_row = tep_db_fetch_array($payment_gateway_filename_result_sql)) {
                $this->pg_filename[$payment_methods_parent_id] = strtolower($payment_gateway_filename_row['payment_methods_filename']);
            } else {
                $this->pg_filename[$payment_methods_parent_id] = '';
            }
        }
        
        return $this->pg_filename[$payment_methods_parent_id];
    }
    
    public function get_score_from_history($orders_id) {
        $return_int = 0;
        
		if ($maxmind_score_row = $this->get_latest_history($orders_id, 'score')) {
			$return_int = $maxmind_score_row['score'];
		} else {
			$return_int = $this->score_request($orders_id);
		}
        
        return $return_int;
    }
	
	private function set_request_url($url){
        $this->api_url = $url;
    }
    
    private function set_check_field($field) {
        $this->api_check_field = $field;
    }

    /**
     * Set the country code directly
     *
     * @param integer $code
     * @return void
     */
    public function setCountryCode($code = NULL) {
        if ($code != NULL) {
            $this->countryCode = $this->sanitizePhoneNumber($code);
        }
    }
    
    public function getCountryCode() {
        return $this->countryCode;
    }
    
    public function is_supported($payment_methods_parent_id, $no_pg_address_payment_array = NULL) {
        $no_pg_address_payment_array = $no_pg_address_payment_array == NULL ? self::$no_pg_address_payment_array : $no_pg_address_payment_array;
        
        if ($no_pg_address_payment_array) {
            if (in_array($this->get_payment_method_filename($payment_methods_parent_id), $no_pg_address_payment_array)) {
                return TRUE;
            }
        }
        
        return FALSE;
    }

    private function prepare_request($input_data, $version = 'latest') {
        $return_array = array();
        
        $ccfs = new CreditCardFraudDetection();
        
        switch ($version) {
            case self::OLD_VERSION_API:
                $check_field = $this->api_check_field;
                $api_url = $this->api_url;
                $allowed_field_array = array('l' => 1, 'phone' => 1);
                
                $input_data["l"] = MAXMIND_LICENSE_KEY;
                break;
            default:
                $check_field = $ccfs->check_field;
                $api_url = $ccfs->url;
                $allowed_field_array = array();
                
                $input_data["license_key"] = MAXMIND_LICENSE_KEY;
                break;
        }
        
        $ccfs->set_url($api_url);
        $ccfs->set_is_proxy($this->proxy_mode);
        $ccfs->set_check_field($check_field);
        $ccfs->set_allowed_fields($allowed_field_array);
        $ccfs->input($input_data);
        
//		$ccfs->isSecure = 0;                    // If you want to disable Secure HTTPS or don't have Curl and OpenSSL installed
		$ccfs->timeout = 15;                    // set the timeout to be five seconds
		$ccfs->query();
		
		// then we get the result from the server
		if ($h = $ccfs->output()) {
            // then finally we print out the result
            $outputkeys = array_keys($h);
            $numoutputkeys = count($h);

            for ($i = 0; $i < $numoutputkeys; $i++) {
                if ($key = $outputkeys[$i]) {
                    $return_array[$key] = $h[$key];
                }
            }
        }
        
        return $return_array;
    }
    
	public function score_request($order_id, $no_pg_address_payment_array = array()) {
        include_once(DIR_WS_CLASSES . 'order.php');
        
        $return_int = 0;
        $city = '';
        $region = '';
        $postal = '';
        $country = '';
        $country_form = '';
        $info_message = '';
        $bin_num = isset($_POST['bin_number']) ? tep_db_prepare_input($_POST['bin_number']) : '';
        $bin_name = isset($_POST['bin_name']) ? tep_db_prepare_input($_POST['bin_name']) : '';
        $bin_phone = isset($_POST['bin_phone']) ? tep_db_prepare_input($_POST['bin_phone']) : '';
        
		$order = new order($order_id);
  		
		if ($this->get_payment_method_filename($order->info['payment_methods_parent_id']) == 'paypal.php') {
			include_once(dirname(__FILE__) . '/../../../payment/paypal/classes/ipn_query.class.php');
			$paypal = new ipn_query($order_id);
			
			if(tep_not_null($paypal->customer['address_city']) && tep_not_null($paypal->customer['address_state']) && tep_not_null($paypal->customer['address_zip']) && tep_not_null($paypal->customer['address_country'])) {
				$city = $paypal->customer['address_city'];
				$region = $paypal->customer['address_state'];
				$postal = $paypal->customer['address_zip'];
				$country = $paypal->customer['address_country'];
				
				$domain_name = explode("@", $paypal->customer['payer_email']);
				$info_message = "Paypal";
			} else if (tep_not_null($order->billing['city']) && tep_not_null($order->billing['state']) && tep_not_null($order->billing['postcode']) && tep_not_null($order->billing['country'])){
				$city = $order->billing['city'];
				$region = $order->billing['state'];
				$postal = $order->billing['postcode'];
				$country = $order->billing['country'];
				
				$domain_name = explode("@", $order->customer['email_address']);
				$info_message = "Customer's Billing Address";
			} else {
				$city = $order->customer['city'];
				$region = $order->customer['state'];
				$postal = $order->customer['postcode'];
				$country = $order->customer['country'];
				
				$domain_name = explode("@", $order->customer['email_address']);
				$info_message = "Customer's Profile Address";
			}
		} else if ($this->get_payment_method_filename($order->info['payment_methods_parent_id']) == 'paypalEC.php') {
			$paypal_select_sql = "	SELECT address_city, address_state, address_zip, address_country, payer_email
									FROM " . TABLE_PAYPALEC . "
									WHERE paypal_order_id = '" . tep_db_input($order_id) . "'";
			$paypal_result_sql = tep_db_query($paypal_select_sql);
			if ($paypal_row = tep_db_fetch_array($paypal_result_sql)) {
				if(tep_not_null($paypal_row['address_city']) && tep_not_null($paypal_row['address_state']) && tep_not_null($paypal_row['address_zip']) && tep_not_null($paypal_row['address_country'])) {
					$city = $paypal_row['address_city'];
					$region = $paypal_row['address_state'];
					$postal = $paypal_row['address_zip'];
					$country = $paypal_row['address_country'];

					$domain_name = explode("@", $paypal_row['payer_email']);
					$info_message = "PaypalEC";
				} else if (tep_not_null($order->billing['city']) && tep_not_null($order->billing['state']) && tep_not_null($order->billing['postcode']) && tep_not_null($order->billing['country'])){
					$city = $order->billing['city'];
					$region = $order->billing['state'];
					$postal = $order->billing['postcode'];
					$country = $order->billing['country'];

					$domain_name = explode("@", $order->customer['email_address']);
					$info_message = "Customer's Billing Address";
				} else {
					$city = $order->customer['city'];
					$region = $order->customer['state'];
					$postal = $order->customer['postcode'];
					$country = $order->customer['country'];

					$domain_name = explode("@", $order->customer['email_address']);
					$info_message = "Customer's Profile Address";
				}
			}
		} else if ($no_pg_address_payment_array == array() || $this->is_supported($order->info['payment_methods_parent_id'], $no_pg_address_payment_array)) {
			if (tep_not_null($order->billing['city']) && tep_not_null($order->billing['state']) && tep_not_null($order->billing['postcode']) && $order->billing['country']){
				$city = $order->billing['city'];
				$region = $order->billing['state'];
				$postal = $order->billing['postcode'];
				$country = $order->billing['country'];
				
				$domain_name = explode("@", $order->customer['email_address']);
				$info_message = "Customer's Billing Address";
			} else {
				$city = $order->customer['city'];
				$region = $order->customer['state'];
				$postal = $order->customer['postcode'];
				$country = $order->customer['country'];
                
                $domain_name = explode("@", $order->customer['email_address']);
				$info_message = "Customer's Profile Address"; // Country is returned as part of the whole address in WorldPay, so cannot get the country value.
			}
		}
		
        if ($city != '') {
            if ($country != '') {
                if (isset($this->maxmind_country_conversion[$country])) {
                    $country = $this->maxmind_country_conversion[$country];
                }

                $coun = tep_get_countries_info($country, 'countries_name');
                $country_form = $coun['countries_iso_code_2'];
            }

            // Required fields
            $h = array();
            $h["i"] = $order->info['remote_addr'];          // set the client ip address
            $h["city"] = $city;                             // set the billing city
            $h["region"] = $region;                         // set the billing state
            $h["postal"] = $postal;                         // set the billing zip code
            $h["country"] = $country_form;                  // set the billing country

            // Recommended fields
            $h["domain"] = isset($domain_name[1]) ? $domain_name[1] : '';                 // Email domain
            $h["bin"] = $bin_num;                           // bank identification number
            $h["forwardedIP"] = $order->info['remote_addr']; // X-Forwarded-For or Client-IP HTTP Header
            $h["emailMD5"] = "";                            // CreditCardFraudDetection.php will take
            $h["usernameMD5"] = "";                         // Newly added 2013
            $h["passwordMD5"] = "";                         // Newly added 2013

            // Optional fields
            $h["binName"] = $bin_name;                      // bank name
            $h["binPhone"] = $bin_phone;                    // bank customer service phone number on back of credit card
            $h["custPhone"] = $country_form == "US" ? preg_replace('/[^\d]/', '', $order->customer['telephone']) : "";		// Area-code and local prefix of customer phone number
            $h["requested_type"] = "";	// Which level (free, city, premium) of CCFD to use
            $h["shipAddr"] = "";	// Shipping Address
            $h["shipCity"] = "";	// the City to Ship to
            $h["shipRegion"] = "";	// the Region to Ship to
            $h["shipPostal"] = "";	// the Postal Code to Ship to
            $h["shipCountry"] = "";	// the country to Ship to			
            $h["txnID"] = "";			// Transaction ID
            $h["sessionID"] = "";		// Session ID
            $h["accept_language"] = "";
            $h["user_agent"] = "";
            
            if ($maxmind_info = $this->prepare_request($h)) {
                $score = isset($maxmind_info["score"]) ? $maxmind_info["score"] : bcdiv($maxmind_info["riskScore"], 10, 1);
                
                $sql_data_array = array(
                    'orders_id' => tep_db_prepare_input($order_id),
                    'maxmind_history_date' => 'now()',
                    'distance' => (int)$maxmind_info["distance"],
                    'country_match' => $maxmind_info["countryMatch"] != '' ? tep_db_prepare_input($maxmind_info["countryMatch"]) : self::NOT_CHECK_KEYWORD,
                    'ip_country_code' => tep_db_prepare_input($maxmind_info["countryCode"]),
                    'free_mail' => $maxmind_info["freeMail"] != '' ? tep_db_prepare_input($maxmind_info["freeMail"]) : self::NOT_CHECK_KEYWORD,
                    'anonymous_proxy ' => $maxmind_info["anonymousProxy"] != '' ? tep_db_prepare_input($maxmind_info["anonymousProxy"]) : self::NOT_CHECK_KEYWORD,
                    'score' => $score,
                    'bin_match' => $maxmind_info["binMatch"] != '' ? tep_db_prepare_input($maxmind_info["binMatch"]) : self::NOT_CHECK_KEYWORD,
                    'bin_country_code' => $maxmind_info["binCountry"] != '' ? tep_db_prepare_input($maxmind_info["binCountry"]) : self::NOT_CHECK_KEYWORD,
                    'error' => tep_db_prepare_input($maxmind_info["err"]),
                    'proxy_score' => tep_db_prepare_input($maxmind_info["proxyScore"]),
//                    'spam_score' => tep_db_prepare_input($maxmind_info["spamScore"]), // has been deprecated
                    'ip_region' => $maxmind_info["ip_region"] != '' ? tep_db_prepare_input($maxmind_info["ip_region"]) : self::NOT_CHECK_KEYWORD,
                    'ip_city' => $maxmind_info["ip_city"] != '' ? tep_db_prepare_input($maxmind_info["ip_city"]) : self::NOT_CHECK_KEYWORD,
                    'ip_latitude' => tep_db_prepare_input($maxmind_info["ip_latitude"]),
                    'ip_longitude' => tep_db_prepare_input($maxmind_info["ip_longitude"]),
                    'bin_name' => $maxmind_info["binName"] != '' ? tep_db_prepare_input($maxmind_info["binName"]) : self::NOT_CHECK_KEYWORD,
                    'ip_isp' => $maxmind_info["ip_isp"] != '' ? tep_db_prepare_input($maxmind_info["ip_isp"]) : self::NOT_CHECK_KEYWORD,
                    'ip_organization' => $maxmind_info["ip_org"] != '' ? tep_db_prepare_input($maxmind_info["ip_org"]) : self::NOT_CHECK_KEYWORD,
                    'ip_accuracy_radius' => tep_db_prepare_input($maxmind_info['ip_accuracyRadius']),
                    'ip_postal_code' => $maxmind_info["ip_postalCode"] != '' ? tep_db_prepare_input($maxmind_info["ip_postalCode"]) : self::NOT_CHECK_KEYWORD,
                    'ip_area_code' => $maxmind_info["ip_areaCode"] != '' ? tep_db_prepare_input($maxmind_info["ip_areaCode"]) : self::NOT_CHECK_KEYWORD,
                    'ip_time_zone' => $maxmind_info["ip_timeZone"] != '' ? tep_db_prepare_input($maxmind_info["ip_timeZone"]) : self::NOT_CHECK_KEYWORD,
                    'ip_user_type' => $maxmind_info["ip_userType"] != '' ? tep_db_prepare_input($maxmind_info["ip_userType"]) : self::NOT_CHECK_KEYWORD,
                    'ip_net_speed_cell' => $maxmind_info["ip_netSpeedCell"] != '' ? tep_db_prepare_input($maxmind_info["ip_netSpeedCell"]) : self::NOT_CHECK_KEYWORD,
                    'is_trans_proxy' => $maxmind_info["isTransProxy"] != '' ? tep_db_prepare_input($maxmind_info["isTransProxy"]) : self::NOT_CHECK_KEYWORD,
                    'ip_corporate_proxy' => $maxmind_info["ip_corporateProxy"] != '' ? tep_db_prepare_input($maxmind_info["ip_corporateProxy"]) : self::NOT_CHECK_KEYWORD,
                    'bin_name_match' => $maxmind_info["binNameMatch"] != '' ? tep_db_prepare_input($maxmind_info["binNameMatch"]) : self::NOT_CHECK_KEYWORD,
                    'bin_phone_match' => $maxmind_info["binPhoneMatch"] != '' ? tep_db_prepare_input($maxmind_info["binPhoneMatch"]) : self::NOT_CHECK_KEYWORD,
                    'bin_phone' => $maxmind_info["binPhone"] != '' ? tep_db_prepare_input($maxmind_info["binPhone"]) : self::NOT_CHECK_KEYWORD,
                    'customer_phone_in_billing_location' => $maxmind_info["custPhoneInBillingLoc"] != '' ? tep_db_prepare_input($maxmind_info["custPhoneInBillingLoc"]) : self::NOT_CHECK_KEYWORD,
                    'high_risk_country' => tep_db_prepare_input($maxmind_info["highRiskCountry"]),
                    'queries_remaining' => $maxmind_info["****************"] != '' ? tep_db_prepare_input($maxmind_info["****************"]) : self::NOT_CHECK_KEYWORD,
                    'city_postal_match' => $maxmind_info["cityPostalMatch"] != '' ? tep_db_prepare_input($maxmind_info["cityPostalMatch"]) : self::NOT_CHECK_KEYWORD,
                    'shipping_city_postal_match' => $maxmind_info["shipCityPostalMatch"] != '' ? tep_db_prepare_input($maxmind_info["shipCityPostalMatch"]) : self::NOT_CHECK_KEYWORD,
                    'maxmind_history_source' => $info_message,
                    'maxmind_id' => $maxmind_info["maxmindID"] != '' ? tep_db_prepare_input($maxmind_info["maxmindID"]) : self::NOT_CHECK_KEYWORD
                );
                
                if (tep_db_perform(TABLE_MAXMIND_HISTORY, $sql_data_array)) {
                    $return_int = $score;
                }
            }
        }
        
        unset($order);
        
		return $return_int;
	}
    
    public function crew_score_request($order_id) {
        return $this->score_request($order_id, self::$no_pg_address_payment_array);
    }
    
    public function telephone_type_request($customers_telephone_country, $customers_telephone, $customers_country_international_dialing_code = NULL) {
        $return_array = FALSE;
		$customers_telephone = preg_replace('/[^\d]/', '', trim($customers_telephone));
		
        $this->setCountryCode($customers_country_international_dialing_code);
        $this->set_check_field('refid');

        if (in_array(trim($customers_telephone_country), self::$phone_identification_supported_country_array)) {
            $this->set_request_url("app/phonetype_http");
            $h = array("phone" => $customers_telephone);
        } else {
            $this->set_request_url("app/phone_id_http");
            $h = array("phone" => '+' . $this->getCountryCode() . $customers_telephone);
        }

        if ($maxmind_telephone_info = $this->prepare_request($h, self::OLD_VERSION_API)) {
            $return_array = array(
                'TYPEOFPHONE' => $maxmind_telephone_info["phoneType"],
                'CITY' => isset($maxmind_telephone_info["city"]) ? $maxmind_telephone_info["city"] : '',
                'STATE' => isset($maxmind_telephone_info["state"]) ? $maxmind_telephone_info["state"] : '',
                'ZIP' => isset($maxmind_telephone_info["zip"]) ? $maxmind_telephone_info["zip"] : '',
                'COUNTRYNAME' => isset($maxmind_telephone_info["countryname"]) ? $maxmind_telephone_info["countryname"] : '',
                'LATITUDE' => isset($maxmind_telephone_info["latitude"]) ? $maxmind_telephone_info["latitude"] : '',
                'LONGITUDE' => isset($maxmind_telephone_info["longitude"]) ? $maxmind_telephone_info["longitude"] : '',
                'error' => $maxmind_telephone_info["err"],
                'REFERENCEID' => $maxmind_telephone_info["refid"]
            );
        }
        
        return $return_array;
	}
    
    public function phone_verification_request($complete_telephone, $code, $languages_id = 1) {}
	
	/**
     * Sanitizes a phone number trimming it & stripping it of all whitespace, parenthesis,
     * plus and minus characters.
     *
     * @param string $phoneNumber phone number without country code
     * @return string valid phone number
     */
    private function sanitizePhoneNumber($phoneNumber) {
        // remove whitespaces, dashes and brackets
        $phoneNumber = trim($phoneNumber);
        $pattern = array("/-/", "/\+/", "/\s/", "/\(/", "/\)/");
        $replace = '';
        $phoneNumber = preg_replace($pattern, $replace, $phoneNumber);

        // trim zeros from the begining
        $phoneNumber = ltrim($phoneNumber, '0');

        return $phoneNumber;
    }
}
?>
