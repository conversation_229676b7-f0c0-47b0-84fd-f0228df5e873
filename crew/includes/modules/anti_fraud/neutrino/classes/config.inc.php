<?php if (!class_exists('ogm_neutrino')) die('No direct access allowed.');

switch ('development') {
    case 'production':
        define('NEUTRINO_API_URL', 'https://neutrinoapi.com');
        define('NEUTRINO_API_USER_ID', 'OGM');
        define('NEUTRINO_API_SIGNATURE', '5LueS4Ba2KNmHQsXKDW4iHnGqhii6NgG');
        break;
    case 'development':
        define('NEUTRINO_API_URL', 'https://neutrinoapi.com');
        define('NEUTRINO_API_USER_ID', 'OGM');
        define('NEUTRINO_API_SIGNATURE', '5LueS4Ba2KNmHQsXKDW4iHnGqhii6NgG');
        break;
}