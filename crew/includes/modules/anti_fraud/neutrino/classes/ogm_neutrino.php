<?php

class ogm_neutrino {

    public $provider_name = 'neutrino';
    private $curl_obj;
    public $phone_type = array(
        'mobile' => 2,
        'fixed-line' => 1,
        'toll-free' => 4,
        'voip' => 5,
        'premium-rate' => 6,
        'hlr-mobile' => 3,
        'hlr-fixed-line' => 11,
        'hlr-toll-free' => 14,
        'hlr-voip' => 15,
        'hlr-premium-rate' => 16,
        'hlr-invalid-mobile' => 17,
        'hlr-missing' => 18,
        'hlr-unknown' => 19,
        'unknown' => 10,
        'no-respond' => 0
    );

    public function __construct($config = null) {
        $this->curl_obj = new curl();
    }

    private function isJson($params) {
        return json_decode($params) instanceof stdClass;
    }

    /**
     * Set the country code directly
     *
     * @param integer $code
     * @return void
     */
    public function setCountryCode($code = NULL) {
        if ($code != NULL) {
            $this->countryCode = $this->sanitizePhoneNumber($code);
        }
    }

    public function getCountryCode() {
        return $this->countryCode;
    }

    public function get_phone_type_id($phone_type) {
        return isset($this->phone_type[$phone_type]) ? $this->phone_type[$phone_type] : 10;
    }

    private function prepare_request($action, $request_data, $total_try = 0) {
        $return_array = array();

        $request_url = NEUTRINO_API_URL . '/' . $action;

        $request_data['user-id'] = NEUTRINO_API_USER_ID;
        $request_data['api-key'] = NEUTRINO_API_SIGNATURE;

        try {
            if ($total_try < 3) {
                $response = $this->curl_obj->curl_get($request_url, $request_data);

                if ($this->isJson($response)) {
                    $return_array = json_decode($response, TRUE);
                } else {
                    $curl_err = $this->curl_obj->get_error();

                    if (isset($curl_err['error_code']) && in_array($curl_err['error_code'], array(35))) {
                        $this->reportError(array('request' => array('action' => $action, 'data' => $request_data['number']), 'response' => $response, 'curl_error' => $this->curl_obj->get_error(), 'try' => $total_try), "Method [prepare_request]");
                        $return_array = $this->prepare_request($action, $request_data, ++$total_try);
                    } else {
                        throw new Exception('NO_RESPONSE');
                    }
                }
            }
        } catch (Exception $e) {
            unset($request_data['api-key']);
            $this->reportError(array('request' => array('action' => $action, 'data' => $request_data), 'response' => $response, 'curl_error' => $this->curl_obj->get_error(), 'error' => $e->getMessage()), "Method [prepare_request]");
        }

        unset($request_data);

        return $return_array;
    }

    public function telephone_type_request($customers_telephone_country, $customers_telephone, $customers_country_international_dialing_code = NULL) {
        $phone_type = '';
        $err = '';
        $phone = '+' . $this->getCountryCode() . $this->sanitizePhoneNumber($customers_telephone);
        $hlr_lookup_array = array();
        $phone_validate_array = $this->queryPhoneValidate($phone);

        if ($phone_validate_array !== array()) {
            if (!isset($phone_validate_array['api-error-msg'])) {
                $phone_type = isset($phone_validate_array['type']) ? $phone_validate_array['type'] : '';

                if ($phone_type == 'unknown') {
                    $hlr_lookup_array = $this->queryHLRLookup($phone);

                    if ($hlr_lookup_array !== array()) {
                        if (!isset($hlr_lookup_array['api-error-msg'])) {
                            $phone_type = isset($hlr_lookup_array['number-type']) ? $hlr_lookup_array['number-type'] : 'hlr-missing';
                            $mcc = isset($hlr_lookup_array['mcc']) ? $hlr_lookup_array['mcc'] : '';
                            $mnc = isset($hlr_lookup_array['mnc']) ? $hlr_lookup_array['mnc'] : '';

                            if ($phone_type == 'mobile') {
                                if (!tep_not_empty($mcc) || !tep_not_empty($mnc)) {
                                    $phone_type = 'invalid-mobile';
                                }
                            }

                            if ($phone_type != 'hlr-missing') {
                                $phone_type = 'hlr-' . $phone_type;
                            }
                        } else {
                            $err = 'queryPhoneValidate::' . $hlr_lookup_array['api-error-msg'];
                        }
                    } else {
                        $err = 'queryHLRLookup::NO_RESPONSE';
                    }
                }
            } else {
                $err = 'queryPhoneValidate::' . $phone_validate_array['api-error-msg'];
            }
        } else {
            $err = 'queryPhoneValidate::NO_RESPONSE';
        }

        return array(
            'response_pv' => $phone_validate_array,
            'response_hlr' => $hlr_lookup_array,
            'TYPEOFPHONE' => $this->get_phone_type_id($phone_type),
            'CITY' => '',
            'STATE' => '',
            'ZIP' => '',
            'COUNTRYNAME' => isset($phone_validate_array["location"]) ? $phone_validate_array["location"] : '',
            'LATITUDE' => '',
            'LONGITUDE' => '',
            'REFERENCEID' => $phone,
            'error' => $err
        );
    }

    public function phone_verification_request($complete_telephone, $code, $languages_id = 1) {
        
    }

    private function queryHLRLookup($phone_number) {
        $request_action = 'hlr-lookup';
        $request_array = array(
            'number' => $phone_number
        );

        return $this->prepare_request($request_action, $request_array);
    }

    private function queryPhoneValidate($phone_number) {
        $request_action = 'phone-validate';
        $request_array = array(
            'number' => $phone_number
        );

        return $this->prepare_request($request_action, $request_array);
    }

    /**
     * Sanitizes a phone number trimming it & stripping it of all whitespace, parenthesis,
     * plus and minus characters.
     *
     * @param string $phoneNumber phone number without country code
     * @return string valid phone number
     */
    private function sanitizePhoneNumber($phoneNumber) {
        // remove whitespaces, dashes and brackets
        $phoneNumber = trim($phoneNumber);
        $pattern = array("/-/", "/\+/", "/\s/", "/\(/", "/\)/");
        $replace = '';
        $phoneNumber = preg_replace($pattern, $replace, $phoneNumber);

        // trim zeros from the begining
        $phoneNumber = ltrim($phoneNumber, '0');

        return $phoneNumber;
    }

    private function reportError($response_data, $ext_subject = '') {
        ob_start();
        echo "<pre>" . $ext_subject;
        echo "<br>================================================RESPONSE================================================<br>";
        print_r($response_data);
        echo "<br>========================================================================================================";
        $response_data = ob_get_contents();
        ob_end_clean();

        $subject = '[OFFGAMERS] Neutrino API Error - ' . date("F j, Y H:i");

        @tep_mail('OffGamers', '<EMAIL>', $subject, $response_data, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
    }

}

include_once dirname(__FILE__) . DIRECTORY_SEPARATOR . 'config.inc.php';

if (class_exists('curl') != TRUE) {
    include_once dirname(__FILE__) . DIRECTORY_SEPARATOR . '..' . DIRECTORY_SEPARATOR . '..' . DIRECTORY_SEPARATOR . '..' . DIRECTORY_SEPARATOR . '..' . DIRECTORY_SEPARATOR . 'classes/curl.php';
}
?>