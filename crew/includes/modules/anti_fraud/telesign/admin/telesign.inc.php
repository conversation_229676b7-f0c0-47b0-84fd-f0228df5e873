<?php
include_once(DIR_FS_CATALOG_MODULES . 'anti_fraud/telesign/admin/languages/'.$language.'/telesign_inc.lng.php');

$telesign_telephone_info_row = FALSE;
$maxmind_history_row = array();
$phone_identification_supported_country_array = array('Canada', 'United States');

$payment_gateway_filename_select_sql = "SELECT payment_methods_filename 
										FROM " . TABLE_PAYMENT_METHODS . "
										WHERE payment_methods_id = '".(int)$order->info['payment_methods_parent_id']."'";
$payment_gateway_filename_result_sql = tep_db_query($payment_gateway_filename_select_sql);
$payment_gateway_filename_row = tep_db_fetch_array($payment_gateway_filename_result_sql);

$payment_gateway_maxmind_filename_array = array('adyen.php','bibit.php', 'global_collect.php', 'paypal.php', 'moneybookers.php', 'worldpay.php', 'ipay88.php', 'googlewallet.php', 'smart2pay_globalpay.php', 'smart2pay.php', 'paypalec.php', 'payu.php', 'coda.php');
$payment_gateway_maxmind_array = array(64, 27, 44, 25, 17, 297);

$payment_gateway_maxmind_flag = (in_array(strtolower($order->info['payment_methods_parent_id']), $payment_gateway_maxmind_array) || in_array(strtolower($payment_gateway_filename_row['payment_methods_filename']), $payment_gateway_maxmind_filename_array) ? true : false);

$phone_id_code[300] = array('phone_type' => 'Undetermined', 'risk_level' => 'Medium', 'class' => 'redIndicator');
$phone_id_code[301] = array('phone_type' => 'Fixed Line', 'risk_level' => 'Low', 'class' => 'greenIndicator');
$phone_id_code[302] = array('phone_type' => 'Mobile', 'risk_level' => 'Low-Medium', 'class' => 'greenIndicator');
$phone_id_code[303] = array('phone_type' => 'PrePaid Mobile', 'risk_level' => 'Medium-High', 'class' => 'greenIndicator');
$phone_id_code[304] = array('phone_type' => 'Toll-Free', 'risk_level' => 'High', 'class' => 'redIndicator');
$phone_id_code[305] = array('phone_type' => 'Non-Fixed VoIP', 'risk_level' => 'High', 'class' => 'redIndicator');
$phone_id_code[306] = array('phone_type' => 'Pager', 'risk_level' => 'High', 'class' => 'redIndicator');
$phone_id_code[307] = array('phone_type' => 'Payphone', 'risk_level' => 'High', 'class' => 'greenIndicator');
$phone_id_code[308] = array('phone_type' => 'Invalid Number', 'risk_level' => 'High', 'class' => 'redIndicator');
$phone_id_code[309] = array('phone_type' => 'Restricted Number', 'risk_level' => 'High', 'class' => 'redIndicator');
$phone_id_code[310] = array('phone_type' => 'Personal', 'risk_level' => 'Medium-Low', 'class' => 'redIndicator');
$phone_id_code[311] = array('phone_type' => 'Voicemail', 'risk_level' => 'Medium-High', 'class' => 'redIndicator');
$phone_id_code[320] = array('phone_type' => 'Other', 'risk_level' => 'Medium-High', 'class' => 'redIndicator');

if ($payment_gateway_maxmind_flag) {
	$customer_maxmind_history_select_sql = "SELECT * 
                                            FROM " . TABLE_MAXMIND_HISTORY . " 
                                            WHERE orders_id = '" . tep_db_input($oID) ."' 
                                            ORDER BY maxmind_history_date DESC 
                                            LIMIT 1";
	$customer_maxmind_history_result_sql = tep_db_query($customer_maxmind_history_select_sql);
	$maxmind_history_row = tep_db_fetch_array($customer_maxmind_history_result_sql);

	if ($order->info['orders_status'] == '7' && tep_db_num_rows($customer_maxmind_history_result_sql) == 0) {
		$maxmind_obj = new ogm_maxmind();
        $maxmind_obj->crew_score_request($oID);
        unset($maxmind_obj);
        
		$customer_maxmind_history_select_sql = "SELECT * 
                                                FROM " . TABLE_MAXMIND_HISTORY . " 
                                                WHERE orders_id = '" . tep_db_input($oID) . "' 
                                                ORDER BY maxmind_history_date DESC 
                                                LIMIT 1";
		$customer_maxmind_history_result_sql = tep_db_query($customer_maxmind_history_select_sql);
		$maxmind_history_row = tep_db_fetch_array($customer_maxmind_history_result_sql);
	}
	
	$all_maxmind_history_select_sql = " SELECT * 
                                        FROM " . TABLE_MAXMIND_HISTORY . " 
                                        WHERE orders_id = '" . tep_db_input($oID) . "' 
                                            AND maxmind_history_date != '" .$maxmind_history_row['maxmind_history_date']. "'";
	$all_maxmind_history_result_sql = tep_db_query($all_maxmind_history_select_sql);
	
	if ($maxmind_history_row['maxmind_history_source'] == "Customer's Billing Address"){
		$country_select_sql = "SELECT billing_country AS country_name FROM " . TABLE_ORDERS . " WHERE orders_id = '" . tep_db_input($oID) . "'";
		$country_result = tep_db_query($country_select_sql);
		$country_row = tep_db_fetch_array($country_result);
	} else if ($maxmind_history_row['maxmind_history_source'] == "Customer Information"){
		$country_select_sql = "SELECT customers_country AS country_name FROM " . TABLE_ORDERS . " WHERE orders_id = '" . tep_db_input($oID) . "'";
		$country_result = tep_db_query($country_select_sql);
		$country_row = tep_db_fetch_array($country_result);
	} else if($maxmind_history_row['maxmind_history_source'] == "Paypal") {
		$country_select_sql = "SELECT address_country AS country_name FROM " . TABLE_PAYPAL . " WHERE invoice = '" . tep_db_input($oID) . "'";
		$country_result = tep_db_query($country_select_sql);
		$country_row = tep_db_fetch_array($country_result);
	}  else if($maxmind_history_row['maxmind_history_source'] == "PaypalEC") {
		$country_select_sql = "SELECT address_country AS country_name FROM " . TABLE_PAYPALEC . " WHERE paypal_order_id = '" . tep_db_input($oID) . "'";
		$country_result = tep_db_query($country_select_sql);
		$country_row = tep_db_fetch_array($country_result);
	}
}
?>
	<table border="0" cellspacing="0" cellpadding="0" width="100%">
<?
if ($telesign_telephone_info_row !== FALSE) {
?>
        <tr>
			<td class="pageHeading" valign="top" colspan="2"><b><?=TABLE_HEADING_TELESIGN_TELEPHONE_IDENTIFICATION?></b></td>
		</tr>
		<tr>
			<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
		</tr>
        <tr>
			<td>
				<table border="0" cellspacing="0" cellpadding="2" width="<?=(in_array(trim($order->customer['telephone_country']), $phone_identification_supported_country_array) ? '40%' : '68%')?>">
					<tr>
						<td>
							<table border="0" cellspacing="0" cellpadding="2" width="100%">
<?
    if (tep_db_num_rows($telesign_telephone_info_result_sql) > 0) {
?>
								<tr>
									<td class="main" valign="top">
										<table border="1" cellspacing="0" cellpadding="1" width="100%">
											<tr>
												<td class="main"><b><?=ENTRY_TELEPHONE_NUMBER?></b></td>
												<td class="main" align="center"><?='+' . $order->customer['order_country_code'] . ' ' . $telephone_number?></td>
											</tr>
											<tr>
												<td class="main"><b><?=ENTRY_PHONE_TYPE?></b></td>
												<td class="main" align="center">
												<?
													echo '<span class="' . $phone_id_code[$telesign_telephone_info_row['customers_telephone_type']]['class'] . '">' . $telesign_telephone_info_row['customers_telephone_type'] . ' - ' . $phone_id_code[$telesign_telephone_info_row['customers_telephone_type']]['phone_type'] . '</span>' . '&nbsp;&nbsp; <a href=' . tep_href_link('popup/telesign_codes.html') . ' target=blank>' . tep_image(DIR_WS_IMAGES . 'icon_info.gif', IMAGE_ICON_INFO) . '</a>';
												?>
												</td>
											</tr>
											<tr>
												<td class="main"><b><?=ENTRY_RISK_LEVEL?></b></td>
												<td class="main" align="center"><span class="<?=$phone_id_code[$telesign_telephone_info_row['customers_telephone_type']]['class']?>"><?=$phone_id_code[$telesign_telephone_info_row['customers_telephone_type']]['risk_level']?></span></td>
											</tr>
											<tr>
												<td class="main"><b><?=ENTRY_REQUESTED_DATE?></b></td>
												<td class="main" align="center"><?=tep_date_short($telesign_telephone_info_row['requested_date'], PREFERRED_DATE_FORMAT)?></td>
											</tr>
<?
        if (tep_not_null($telesign_telephone_info_row['error']) && $telesign_telephone_info_row['error'] != '0') {
?>
											<tr>
												<td class="main"><b><?=ENTRY_ERROR?></b></td>
												<td class="main" align="center"><span class="redIndicator"><?=$telesign_telephone_info_row['error']?></span></td>
											</tr>
<?
        }
?>
											<tr>
												<td class="main"><b><?=ENTRY_TELESIGN_REFERENCE_ID?></b></td>
												<td class="main" align="center">
													<?
													if (tep_not_null($telesign_telephone_info_row['maxmind_telephone_identification_id'])) {
														echo $telesign_telephone_info_row['maxmind_telephone_identification_id'];
													} else {
														echo TEXT_OPTION_NOT_APPLICABLE;
													}
													?>
												</td>
											</tr>
<?
        $call_language = $phone_verification_obj->get_call_language($order->customer['order_country_code'].$order->customer['telephone']);
        if (tep_not_null($call_language)) {
            echo '							<tr>
												<td class="main"><b>'.ENTRY_CALL_LANGUAGE.'</b></td>
												<td class="main" align="center">'.
													$call_language
													.'
												</td>
											</tr>';
        }
?>
										</table>
									</td>
<?      if (tep_not_null($telesign_telephone_info_row['city']) || tep_not_null($telesign_telephone_info_row['state']) || tep_not_null($telesign_telephone_info_row['postcode']) || tep_not_null($telesign_telephone_info_row['countries_name']) || tep_not_null($telesign_telephone_info_row['latitude']) || tep_not_null($telesign_telephone_info_row['longitude'])) {	?>
									<td class="main" valign="top">
										<table border="1" cellspacing="0" cellpadding="1" width="100%">
											<tr>
												<td class="main"><b><?=ENTRY_CITY?></b></td>
												<td class="main" align="center">
													<?
													if (tep_not_null($telesign_telephone_info_row['city'])) {
														echo $telesign_telephone_info_row['city'];
													} else {
														echo TEXT_OPTION_NOT_APPLICABLE;
													}
													?>
												</td>
											</tr>
											<tr>
												<td class="main"><b><?=ENTRY_STATE?></b></td>
												<td class="main" align="center">
													<?
													if (tep_not_null($telesign_telephone_info_row['state'])) {
														echo $telesign_telephone_info_row['state'];
													} else {
														echo TEXT_OPTION_NOT_APPLICABLE;
													}
													?>
												</td>
											</tr>
											<tr>
												<td class="main"><b><?=ENTRY_POST_CODE?></b></td>
												<td class="main" align="center">
													<?
													if (tep_not_null($telesign_telephone_info_row['postcode'])) {
														echo $telesign_telephone_info_row['postcode'];
													} else {
														echo TEXT_OPTION_NOT_APPLICABLE;
													}
													?>
												</td>
											</tr>
											<tr>
												<td class="main"><b><?=ENTRY_COUNTRY?></b></td>
												<td class="main" align="center">
													<?
													if (tep_not_null($telesign_telephone_info_row['countries_name'])) {
														echo $telesign_telephone_info_row['countries_name'];
													} else {
														echo TEXT_OPTION_NOT_APPLICABLE;
													}
													?>
												</td>
											</tr>
											<tr>
												<td class="main"><b><?=ENTRY_LATITUDE?></b></td>
												<td class="main" align="center">
													<?
													if (tep_not_null($telesign_telephone_info_row['latitude'])) {
														echo $telesign_telephone_info_row['latitude'];
													} else {
														echo TEXT_OPTION_NOT_APPLICABLE;
													}
													?>
												</td>
											</tr>
											<tr>
												<td class="main"><b><?=ENTRY_LONGITUDE?></b></td>
												<td class="main" align="center">
													<?
													if (tep_not_null($telesign_telephone_info_row['longitude'])) {
														echo $telesign_telephone_info_row['longitude'];
													} else {
														echo TEXT_OPTION_NOT_APPLICABLE;
													}
													?>
												</td>
											</tr>
											<tr>
												<td class="main"><b><?=ENTRY_LONGITUDE?></b></td>
												<td class="main" align="center">
													<?
													if (tep_not_null($telesign_telephone_info_row['longitude'])) {
														echo $telesign_telephone_info_row['longitude'];
													} else {
														echo TEXT_OPTION_NOT_APPLICABLE;
													}
													?>
												</td>
											</tr>
										</table>
									</td>
								</tr>
<?	
        }
    } else {
?>
								<tr>
									<td class="main" align="center"><?=TEXT_OPTION_NOT_APPLICABLE?></td>
								</tr>
<?
    }
?>
							</table>
						</td>
<?
    if ($phone_verification_obj->get_service_provider_status()) {
        echo				'<td class="main" valign="bottom">' .
                                tep_draw_form('maxmind_phone_request_form', FILENAME_ORDERS, tep_get_all_get_params(), 'post') .
                                tep_draw_hidden_field('subaction', 'maxmind_phone_info_update') .
                                tep_submit_button(IMAGE_UPDATE, IMAGE_UPDATE, 'name="btn_maxmind_phone_update"', 'inputButton') .
                                '</form>
                            </td>';
    }
    unset($phone_verification_obj);
?>
					</tr>
				</table>
			</td>
		</tr>
<?
}

if ($payment_gateway_maxmind_flag) {
?>
		<!--tr>
			<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
		</tr>
		<tr>
			<td><?=tep_draw_separator()?></td>
		</tr-->
		<tr>
			<td class="pageHeading" valign="top" colspan="2"><b><?=TABLE_HEADING_MAXMIND_INFO?></b></td>
		</tr>
		<tr>
			<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
		</tr>
<?	
    if (tep_db_num_rows($all_maxmind_history_result_sql) > 0) {
?>
		<tr>
			<td>
				<table border="0" cellspacing="0" cellpadding="0">
					<tr>
						<td class="main"><b><?=ENTRY_MAXMIND_REQUESTED_HISTORY?></b></td>
					</tr>
<?
		while ($all_maxmind_history_row = tep_db_fetch_array($all_maxmind_history_result_sql)) {
			echo '<tr>
						<td class="main">
							<a href="javascript:openDGDialog(\''. tep_href_link(FILENAME_MAXMIND_HISTORY, 'maxmind_date='.$all_maxmind_history_row['maxmind_history_date'].'&order_id='.$all_maxmind_history_row['orders_id'].'') . '\', 1100, 310, \'\');" class="actionLink">'.tep_datetime_short($all_maxmind_history_row['maxmind_history_date'], PREFERRED_DATE_TIME_FORMAT).'&nbsp;'.$all_maxmind_history_row['score'].'</a>
						</td>
					</tr>';
		}
?>
				</table>
			</td>
		</tr>
<?
	}
    
	if(count($maxmind_history_row) > 0) {
?>
		<tr>
			<td>
				<table border="0" cellspacing="1" cellpadding="0" align="center">
					<tr>
<?
		for ($maxmind_score_loop=0; $maxmind_score_loop<=10; $maxmind_score_loop++) {
			echo '		<td class="main" valign="middle">';
				
			if ((int)$maxmind_history_row['score'] == $maxmind_score_loop) {
				echo '		<div class="maxmind_'.$maxmind_score_loop.'_selected"><div style="_position:absolute; _top:50%; display:table-cell; vertical-align:middle;"><div style="_position:relative; _top:-50%; _left:-50%">'.$maxmind_score_loop.'</div></div></div>';
			} else {
				echo '		<div class="maxmind_'.$maxmind_score_loop.'_score">'.$maxmind_score_loop.'</div>';
			}
			echo '		</td>';
		}
?>
					</tr>
				</table>
			</td>
		</tr>
		<tr>
			<td>
				<table border="0" cellspacing="0" cellpadding="0" width="100%">
					<tr>
						<td>
							<table border="0" cellspacing="0" cellpadding="0">
								<tr>
									<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
								</tr>
								<tr>
									<td class="main"><b><?=ENTRY_MAXMIND_ID?></b></td>
									<td class="main"><?='&nbsp;' . $maxmind_history_row['maxmind_id']?></td>
								</tr>
								<tr>
									<td class="main"><b><?=ENTRY_MAXMIND_HISTORY_DATE?></b></td>
									<td class="main"><?='&nbsp;' . tep_datetime_short($maxmind_history_row['maxmind_history_date'], PREFERRED_DATE_TIME_FORMAT)?></td>
								</tr>
								<tr>
									<td class="main"><b><?=CUSTOMERS_INFO_MESSAGE?></b></td>
									<td class="main"><?='&nbsp;' . $maxmind_history_row['maxmind_history_source']?></td>
								</tr>
<?		if(tep_not_null($maxmind_history_row['error'])) {	?>
								<tr>
									<td class="main"><b><?=ENTRY_ERROR?></b></td>
									<td class="main"><?='<span class="redIndicator">&nbsp;' . $maxmind_history_row['error']?></td>
								</tr>
<?		}	?>
								<tr>
									<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
								</tr>
							</table>
						</td>
					</tr>
				</table>
			</td>
		</tr>
		<tr>
			<td>
				<table border="0" width="100%" cellspacing="3" cellpadding="0">
					<tr>
						<td class="main" valign="top">
							<table border="1" width="100%" cellspacing="0" cellpadding="0">
								<tr>
									<td class="main" nowrap>
										<b><?=ENTRY_DISTANCE?></b>
									</td>
									<td class="main" align="center" nowrap><span id="span_maxmind_distance"></span></td>
								</tr>
								<tr>
									<td class="main" nowrap>
										<b><?=ENTRY_IP_COUNTRY_CODE?></b>
									</td>
									<td class="main" align="center" nowrap><span id="span_maxmind_ip_country_code"></span></td>
								</tr>
								<tr>
									<td class="main" nowrap>
										<b><?=ENTRY_IP_REGION?></b>
									</td>
									<td class="main" align="center" nowrap><span id="span_maxmind_ip_region"></span></td>
								</tr>
								<tr>
									<td class="main" nowrap>
										<b><?=ENTRY_IP_CITY?></b>
									</td>
									<td class="main" align="center" nowrap><span id="span_maxmind_ip_city"></span></td>
								</tr>
								<tr>
									<td class="main" nowrap>
										<b><?=ENTRY_COUNTRY_MATCH?></b>
									</td>
									<td class="main" align="center" nowrap><span id="span_maxmind_country_match"></span></td>
								</tr>
								<tr>
									<td class="main" nowrap>
										<b><?=ENTRY_HIGH_RISK_COUNTRY?>&nbsp;-&nbsp;<?echo $country_row['country_name']?>:</b>
									</td>
									<td class="main" align="center" nowrap><span id="span_maxmind_high_risk_country"></span></td>
								</tr>
								<tr>
									<td class="main" nowrap>
										<b><?=ENTRY_CITY_POSTAL_MATCH?></b>
									</td>
									<td class="main" align="center" nowrap><span id="span_maxmind_city_postal_match"></span></td>
								<tr>
									<td class="main" nowrap>
										<b><?=ENTRY_CUSTOMER_PHONE_IN_BILLING_LOCATION?></b>
									</td>
									<td class="main" align="center" nowrap><span id="span_maxmind_customer_phone_in_billing_location"></span></td>
								</tr>
								<tr>
									<td class="main" nowrap>
										<b><?=ENTRY_FREE_MAIL?></b>
									</td>
									<td class="main" align="center" nowrap><span id="span_maxmind_free_mail"></span></td>
								</tr>
							</table>
						</td>
						<td class="main" valign="top">
							<table border="1" width="100%" cellspacing="0" cellpadding="0">
								<tr>
									<td class="main" nowrap>
										<b><?=ENTRY_SCORE?></b>
									</td>
									<td class="main" align="center" nowrap><span id="span_maxmind_score"></span></td>
								</tr>
								<tr>
									<td class="main" nowrap>
										<b><?=ENTRY_ANONYMOUS_PROXY?></b>
									</td>
									<td class="main" align="center" nowrap><span id="span_maxmind_anonymous_proxy"></span></td>
								</tr>
								<tr>
									<td class="main" nowrap>
										<b><?=ENTRY_PROXY_SCORE?></b>
									</td>
									<td class="main" align="center" nowrap><span id="span_maxmind_proxy_score"></span></td>
								</tr>
								<tr>
									<td class="main" nowrap>
										<b><?=ENTRY_SPAM_SCORE?></b>
									</td>
									<td class="main" align = "center" nowrap><span id="span_maxmind_spam_score"></span></td>
								</tr>
								<tr>
									<td class="main" nowrap>
										<b><?=ENTRY_IP_ISP?></b>
									</td>
									<td class="main" align="center" nowrap><span id="span_maxmind_ip_isp"></span></td>
								</tr>
								<tr>
									<td class="main" nowrap>
										<b><?=ENTRY_IP_ORGNIZATION?></b>
									</td>
									<td class="main" align="center" nowrap><span id="span_maxmind_ip_organization"></span></td>
								</tr>
							</table>
							<?=RISK_INFO_MESSAGE?>
						</td>
						<td class="main" valign="top">
							<table border="1" width="100%" cellspacing="0" cellpadding="0">
								<tr>
									<td class="main" nowrap>
										<b><?=ENTRY_BIN_COUNTRY_CODE?></b>
									</td>
									<td class="main" align="center" nowrap>
									<?
										if ($maxmind_history_row['bin_country_code'] == "Not Check") {
											echo '<span class="redIndicator">Not checked</span>';
										} else if($maxmind_history_row['ip_isp'] == "NA"){
											echo '<span class="redIndicator">'.$maxmind_history_row['bin_country_code'].'</span>';
										} else {
											echo $maxmind_history_row['bin_country_code'];
										}
									?>
									</td>
								</tr>
								<tr>
									<td class="main" nowrap>
										<b><?=ENTRY_BIN_MATCH?></b>
									</td>
									<td class="main" align="center" nowrap><span id="span_maxmind_bin_match"></span></td>
								</tr>
								<tr>
									<td class="main" nowrap>
										<b><?=ENTRY_BIN_NAME_MATCH?></b>
									</td>
									<td class="main" align="center" nowrap><span id="span_maxmind_bin_name_match"></span></td>
								</tr>
								<tr>
									<td class="main" nowrap>
										<b><?=ENTRY_BIN_NAME?></b>
									</td>
									<td class="main" align="center" nowrap><span id="span_maxmind_bin_name"></span></td>
								</tr>
								<tr>
									<td class="main" nowrap>
										<b><?=ENTRY_BIN_PHONE_MATCH?></b>
									</td>
									<td class="main" align="center" nowrap><span id="span_maxmind_bin_phone_match"></span></td>
								</tr>
								<tr>
									<td class="main" nowrap>
										<b><?=ENTRY_BIN_PHONE?></b>
									</td>
									<td class="main" align="center" nowrap><span id="span_maxmind_bin_phone"></span></td>
								</tr>
							</table>
						</td>
					</tr>
					<tr>
						<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
					</tr>
				</table>
			</td>
		</tr>
<?
	}
	
	if ($order->info['orders_status'] != '1') { ?>
		<tr>
			<td>
				<table border="0" width="100%" cellspacing="3" cellpadding="0">
					<tr>
						<td class="main"><b><?=TEXT_BIN_NUMBER?></b>&nbsp;<?=tep_draw_input_field('bin_number')?></td>
						<td class="main"><b><?=TEXT_BIN_NAME?></b>&nbsp;<?=tep_draw_input_field('bin_name')?></td>
						<td class="main"><b><?=TEXT_BIN_PHONE?></b>&nbsp;<?=tep_draw_input_field('bin_phone')?></td>	
					</tr>
					<tr>
						<td colspan="3" align="right">
						<?
							echo tep_draw_form('maxmind_request_form', FILENAME_ORDERS, tep_get_all_get_params(), 'post');
							echo tep_draw_hidden_field('subaction', 'maxmind_info_update');
							echo tep_submit_button(IMAGE_UPDATE, IMAGE_UPDATE, 'name="btn_maxmind_update"', 'inputButton');
							echo '</form>';
						?>
						</td>
					</tr>
				</table>
			</td>
		</tr>
<?
	}
}
?>
	</table>
	<script>
		jQuery.ajax({
			type: 'get',
			url:'anti_fraud_xmlhttp.php',
			data:'action=get_maxmind_history&oID=<?=$oID?>&limit=1',
			success: function(xml) {
				jQuery(xml).find("maxmind").each(function(){
					jQuery(this).find('info').each(function(){
						if (jQuery("#span_maxmind_" + jQuery(this).find('key').text()).length > 0) {
							jQuery("#span_maxmind_" + jQuery(this).find('key').text()).html(jQuery(this).find('display').text());
						}
					});
				});
			}
		});
	</script>