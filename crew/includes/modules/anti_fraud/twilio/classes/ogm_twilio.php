<?php

include_once dirname(__FILE__) . DIRECTORY_SEPARATOR . 'Services/Twilio.php';

class ogm_twilio {

    public $provider_name = 'twilio';
    public $phone_type = array(
        'mobile' => 2,
        'voip' => 5,
        'landline' => 1,
        'unknown' => 10,
    );
    protected $sid;
    protected $token;
    private $proxy_mode = false;

    public function __construct() {
        $this->sid = ANTIFRAUD_TWILIO_SID;
        $this->token = ANTIFRAUD_TWILIO_TOKEN;
    }

    /**
     * Set the country code directly
     *
     * @param integer $code
     * @return void
     */
    public function setCountryCode($code = NULL) {
        if ($code != NULL) {
            $this->countryCode = $this->sanitizePhoneNumber($code);
        }
    }

    public function getCountryCode() {
        return $this->countryCode;
    }

    public function get_phone_type_id($phone_type) {
        return isset($this->phone_type[$phone_type]) ? $this->phone_type[$phone_type] : 10;
    }

    public function telephone_type_request($customers_telephone_country, $customers_telephone, $customers_country_international_dialing_code = NULL) {
        $phone_type = $this->phone_type['unknown'];
        $err = '';
        $this->setCountryCode($customers_country_international_dialing_code);
        $phone = '+' . $this->getCountryCode() . $this->sanitizePhoneNumber($customers_telephone);

        $client = new Lookups_Services_Twilio($this->sid, $this->token);
        if ($this->proxy_mode) {
            $client->http->curlopts[CURLOPT_PROXY] = 'http://my-proxy.offgamers.lan:3128';
            $client->http->curlopts[CURLOPT_PROXYPORT] = 'http://my-proxy.offgamers.lan:3128';
        }

        $result = $client->phone_numbers->get($phone, array("Type" => "carrier"));
        try {
            //Must silent warning because if SDK runs behind proxy, warning will appear because of code issue
            $carrier = @$result->carrier;
            $phone_type = $this->phone_type[$carrier->type];
        } catch (Exception $ex) {
            $err = $ex->getMessage();
        }
        return array(
            'TYPEOFPHONE' => $phone_type,
            'CITY' => '',
            'STATE' => '',
            'ZIP' => '',
            'COUNTRYNAME' => '',
            'LATITUDE' => '',
            'LONGITUDE' => '',
            'REFERENCEID' => $phone,
            'error' => $err
        );
    }

    /**
     * Sanitizes a phone number trimming it & stripping it of all whitespace, parenthesis,
     * plus and minus characters.
     *
     * @param string $phoneNumber phone number without country code
     * @return string valid phone number
     */
    private function sanitizePhoneNumber($phoneNumber) {
        // remove whitespaces, dashes and brackets
        $phoneNumber = trim($phoneNumber);
        $pattern = array("/-/", "/\+/", "/\s/", "/\(/", "/\)/");
        $replace = '';
        $phoneNumber = preg_replace($pattern, $replace, $phoneNumber);

        // trim zeros from the begining
        $phoneNumber = ltrim($phoneNumber, '0');

        return $phoneNumber;
    }

}

?>