<?php

include_once dirname(__FILE__) . DIRECTORY_SEPARATOR . '../restock_api.php';

class aspin_api extends restock_api {

    const SUCCESS_ACK_FLAG = '1';
    const ASPIN_CURRENCY = 'SGD';
    const KEY_FORMAT = 'ePIN : %s<br>ePINSerialNo : %s<br>Expiry Date : %s';

    public static $service_api_pattern = array(
        array('prefix_len' => 5, 'prefix_text' => 'ASPIN')
    );
    public $request_aspin_merchant_balance;
    public $request_aspin_transactioncode;
    public $aspin_actual_amount;
    public $api_cardinfo_url;
    public $order_product_price;
    public $request_security_token;
    public $resp_hash_data;
    public $resp_error_code;
    private $distributor_code = ASPIN_API_DISTRIBUTOR_CODE;
    private $distributor_secret_code = ASPIN_API_DISTRIBUTOR_SECRET_CODE;

    public function __construct() {
        parent::__construct();
        $this->api_url = ASPIN_API_URL;
        $this->api_cardinfo_url = ASPIN_API_CARDINFO_URL;
        $this->API_PROVIDER = 'ASPIN';
        $this->CHECK_METHOD = 'CheckCard';
        $this->GET_METHOD = 'GetCard';
        $this->SUCCESS_STATUSFLAG = TRUE;

        $this->curl_obj = new curl();
    }

    public static function isSupportedSKU($sku) {
        $return = false;

        $skuParts = explode('_', $sku);
        # pattern ASPIN_ACSG_XXXX
        if (isset($skuParts[1])) {
            $pattern_array = self::$service_api_pattern;

            foreach ($pattern_array as $pattern) {
                if (substr($sku, 0, $pattern['prefix_len']) === $pattern['prefix_text']) {
                    $return = true;
                    break;
                }
            }
        }

        return $return;
    }

    protected function getServiceCode($sku) {
        $skuParts = explode('ASPIN_', $sku);
        $itemCode = isset($skuParts[1]) ? $skuParts[1] : '';
        return $itemCode;
    }

    protected function getAmount($sku) {
        return $this->aspin_actual_amount;
    }

    private function generateRequestArray($process, $sku) {
        $return_data = '';
        $item_code = $this->getServiceCode($sku);
        $utc_date_time = gmdate('YmdHis');

        switch ($process) {
            case 'product_price_check':
                $request_hash = hash('sha256', $this->distributor_code . $item_code . $utc_date_time . $this->distributor_secret_code);
                $return_data = array(
                    'ItemCode' => $item_code, //product id 
                    'DistributorCode' => $this->distributor_code,
                    'hash' => strtoupper($request_hash),
                    'timestamp' => $utc_date_time
                );
                break;
            case 'buy_product':
                $client_ip = '***************';    //tep_get_ip_address();
                $request_id = $this->request_transactioncode;
                $request_hash = hash('sha256', $client_ip . $this->distributor_code . $request_id . $this->request_security_token . $utc_date_time . $this->distributor_secret_code);
                $return_data = array(
                    'ClientIP' => $client_ip,
                    'DistributorCode' => $this->distributor_code,
                    'RequestId' => $request_id,
                    'SecurityToken' => $this->request_security_token,
                    'utcDatetime' => $utc_date_time,
                    'ReferenceID' => '',
                    'Hashdata' => strtoupper($request_hash),
                );
                break;
        }

        return $return_data;
    }

    public function preRequest($sku, $api_method = NULL, $total_try = 0) {
        $return_bool = FALSE;
        $response = '';
        $request_str = '';
        
        if ($total_try < 3) {
            if ($api_method == 'product_price_check') {
                $request_str = $this->generateRequestArray('product_price_check', $sku);
                $this->curl_obj->custom_request_type = false;
                $this->curl_obj->http_header = array('Expect:');
                $response = $this->curl_obj->curl_get($this->api_cardinfo_url, $request_str);
//                $response = json_encode(array(
//                    'Currency' => 'SGD',
//                    'DiscountAmount' => '0',
//                    'ErrorCode' => '1',
//                    'ErrorDesc' => 'SUCCESS',
//                    'HashData' => 'ECD917C0822D090922E24F35463F847A756D29C5FDF8E18257D01277EF451BCC',
//                    'ItemCode' => 'ACSG005',
//                    'ItemPrice' => '5',
//                    'Notify' => '',
//                    'SecurityToken' => '5D0F9D44899946C8969FE69F3C983880',
//                    'TaxAmount' => '0.00',
//                    'UtcDateTime' => '20160929104102'
//                ));
            } else if ($api_method == 'buy_product') {
                $request_str = $this->generateRequestArray('buy_product', $sku);
                $this->curl_obj->custom_request_type = "PUT";
                $encode_json_request_str = json_encode($request_str);
                $this->curl_obj->http_header = array('Content-Type: application/json', 'Content-Length: ' . strlen($encode_json_request_str));
                $response = $this->curl_obj->curl_post($this->api_url, $encode_json_request_str);
//                $response = json_encode(array(
//                    'Completed_Datetime' => '20160929104103',
//                    'Currency' => 'SGD',
//                    'DiscountAmount' => '0',
//                    'ErrorCode' => '1',
//                    'ErrorDesc' => 'SUCCESS',
//                    'ExpDate' => '2018-Sep-30',
//                    'Hashdata' => 'E216AF0628CBF359E0E448CCE8386FDDB55F61132C97C5192408BFCA7E072254',
//                    'ItemCode' => 'ACSG005',
//                    'ItemPrice' => '5',
//                    'RequestId' => '295388',
//                    'SecurityToken' => '5D0F9D44899946C8969FE69F3C983880',
//                    'TaxAmount' => '0',
//                    'TransactionId' => 'TPCIB196242894',
//                    'ePIN' => '233243',
//                    'ePINSerialNo' => '54324234'
//                ));
            }

            $this->resetAPIData();
            $this->loadAPIData($sku, $response);
            $this->logAPIData($sku);

            if ($this->resp_status === true) {
                $return_bool = TRUE;
            } else {
                $this->reportError(array('request' => $request_str, 'response' => $response, 'curlError' => $this->curl_obj->get_error(), 'sku' => $sku, 'method' => $this->request_method, 'try_count' => $total_try, 'error_msg' => $this->resp_error), 'preRequest');
            }
        }

        return $return_bool;
    }

    public function checkCode($sku, $qty, $trans_code) {
        $return_bool = FALSE;

        if (self::isSupportedSKU($sku)) {
            $this->setMethod($this->CHECK_METHOD);
            if ($trans_code = $this->logAPIData($sku, self::FLAG_INIT)) {
                $this->setQuantity($qty);
                $this->setTransCode($trans_code);
                $return_bool = $this->preRequest($sku, 'product_price_check');
            }
        }

        return $return_bool;
    }

    protected function getCode($sku, $qty, $renew_transactioncode = true) {
        $return_bool = FALSE;

        if (self::isSupportedSKU($sku)) {
            $this->setMethod($this->GET_METHOD);
            $this->setQuantity($qty);

            if ($trans_code = $this->logAPIData($sku, self::FLAG_INIT)) {
                $this->setTransCode($trans_code);
                $return_bool = $this->preRequest($sku, 'buy_product');
            }
        }

        return $return_bool;
    }

    public function processBatchRestock($product_id, $order_product_store_price, $sku, $qty, $extra_params = array()) {
        $this->order_product_price = $order_product_store_price;
        return parent::processBatchRestock($product_id, $order_product_store_price, $sku, $qty, $extra_params);
    }

    protected function loadAPIData($sku, $raw_response = NULL, $data_array = array()) {
        if (!is_null($raw_response)) {
            $data_array = json_decode($raw_response, true);
        }

        if (isset($data_array['Hashdata']) && !empty($data_array['Hashdata'])) {
            $this->resp_hash_data = $data_array['Hashdata'];
        }

        # this token will be used in next request
        if (isset($data_array['SecurityToken']) && !empty($data_array['SecurityToken'])) {
            $this->request_security_token = $data_array['SecurityToken'];
            $this->resp_ordernumber = 'ST(' . $this->request_method . '):' . $data_array['SecurityToken'];
        }

        if (isset($data_array['RequestId']) && !empty($data_array['RequestId'])) {
            $this->resp_transactioncode = $data_array['RequestId'];
        }

        if (isset($data_array['TransactionId']) && !empty($data_array['TransactionId'])) {
            $this->resp_ordernumber = $data_array['TransactionId'];
        }

        if (isset($data_array['ErrorCode']) && !is_null($data_array['ErrorCode']) && $data_array['ErrorCode'] == 1) {
            $this->resp_status = TRUE;
        } else {
            $this->resp_status = FALSE;
            $this->resp_error = $data_array['ErrorCode'];

            if (isset($data_array['ErrorDesc']) && !empty($data_array['ErrorDesc'])) {
                $this->resp_error .= ':' . $data_array['ErrorDesc'] . ':' . $this->resp_ordernumber . ':' . $this->resp_transactioncode . ':' . $this->request_security_token;
            }
        }

        if (isset($data_array['ItemPrice']) && !is_null($data_array['ItemPrice'])) {
            $this->resp_currency_code = (isset($data_array['Currency']) && !empty($data_array['Currency'])) ? $data_array['Currency'] : 'SGD';
            $this->resp_currency_settle_amt = $data_array['ItemPrice'];
            $this->aspin_actual_amount = $data_array['ItemPrice'];
        }

        if ((isset($data_array['ePINSerialNo']) && !empty($data_array['ePINSerialNo'])) || (isset($data_array['ePIN']) && !empty($data_array['ePIN']))) {
            $this->resp_items = array();
            $pin = (isset($data_array['ePIN']) && !empty($data_array['ePIN'])) ? $data_array['ePIN'] : '-';
            $serial_no = (isset($data_array['ePINSerialNo']) && !empty($data_array['ePINSerialNo'])) ? $data_array['ePINSerialNo'] : '-';
            $exp_date = (isset($data_array['ExpDate']) && !empty($data_array['ePINSerialNo'])) ? $data_array['ExpDate'] : '-';
            
            $this->resp_items[] = array(
                'log_id' => '',
                'token' => sprintf(self::KEY_FORMAT, $pin, $serial_no, $exp_date),
            );

            $data_array['ePINSerialNo'] = (isset($data_array['ePINSerialNo']) && !empty($data_array['ePINSerialNo'])) ? 'xxxx' : '-';
            $data_array['ePIN'] = (isset($data_array['ePIN']) && !empty($data_array['ePIN'])) ? 'xxxx' : '-';
        }
        
//        $this->reportError(array('res' => $data_array), 'loadAPIData');
    }

}

include_once dirname(__FILE__) . DIRECTORY_SEPARATOR . 'config.inc.php';
?>