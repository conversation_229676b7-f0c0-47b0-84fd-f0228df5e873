<?php if (!class_exists('eclub_api')) die('No direct access allowed.');

switch ('development') {
    case 'production':
        define('ECSTORE_API_URL', 'https://reseller.eclubstore.com/merchant-panel/request_xml.php');
        define('ECSTORE_API_EMAIL', '<EMAIL>');
        define('ECSTORE_API_PASSWORD', 'offgamers123');
        define('ECSTORE_API_KEY', '475SKjC4YQoYAvox2SYEQS5iN6jPUOQI');
        define('ECSTORE_API_SSL_ENABLED', true);
        break;
    case 'development':
        define('ECSTORE_API_URL', 'http://test.eclubstore.com/reseller/merchant-panel/request_xml.php');
        define('ECSTORE_API_EMAIL', '<EMAIL>');
        define('ECSTORE_API_PASSWORD', 'temp1234');
        define('ECSTORE_API_KEY', '0a1tZhK1W0uQkh5OSC74CFKwf65f3O1c');
        define('ECSTORE_API_SSL_ENABLED', false);
        break;
}