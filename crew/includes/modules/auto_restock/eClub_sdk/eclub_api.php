<?php

include_once dirname(__FILE__) . DIRECTORY_SEPARATOR . '../restock_api.php';

class eclub_api extends restock_api {

    public static $SKU_SUPPORT = array(
        'ESW02_P' => array('amt' => 2, 'settle_amt' => 1.8),
        'ESW03_P' => array('amt' => 3, 'settle_amt' => 2.85),
        'ESW05_P' => array('amt' => 5, 'settle_amt' => 4.5),
        'ESW10_P' => array('amt' => 10, 'settle_amt' => 9),
        'ESW12_P' => array('amt' => 12, 'settle_amt' => 11.4),
        'ESW15_P' => array('amt' => 15, 'settle_amt' => 13.5),
        'ESW20_P' => array('amt' => 20, 'settle_amt' => 18),
        'ESW30_P' => array('amt' => 30, 'settle_amt' => 27),
        'ESW50_P' => array('amt' => 50, 'settle_amt' => 45),
        'ESW60_P' => array('amt' => 60, 'settle_amt' => 54),
        
        'ESW05MYR_P' => array('amt' => 5, 'settle_amt' => 1.53),
        'ESW20MYR_P' => array('amt' => 20, 'settle_amt' => 6.11),
        'ESW50MYR_P' => array('amt' => 50, 'settle_amt' => 15.28),
        'ESW100MYR_P' => array('amt' => 100, 'settle_amt' => 30.56),
        'ESW200MYR_P' => array('amt' => 200, 'settle_amt' => 61.11),
        
        'ESW05SGD_P' => array('amt' => 5, 'settle_amt' => 3.88),
        'ESW10SGD_P' => array('amt' => 10, 'settle_amt' => 7.77),
        'ESW20SGD_P' => array('amt' => 20, 'settle_amt' => 15.53),
        'ESW30SGD_P' => array('amt' => 30, 'settle_amt' => 23.30),
        'ESW50SGD_P' => array('amt' => 50, 'settle_amt' => 38.84),
        'ESW75SGD_P' => array('amt' => 75, 'settle_amt' => 58.25),
        
        'ESW50THB_P' => array('amt' => 50, 'settle_amt' => 1.52),
        'ESW200THB_P' => array('amt' => 200, 'settle_amt' => 6.07),
        'ESW350THB_P' => array('amt' => 350, 'settle_amt' => 10.62),
        'ESW1000THB_P' => array('amt' => 1000, 'settle_amt' => 30.35),
        'ESW2000THB_P' => array('amt' => 2000, 'settle_amt' => 60.69),
        
        'ESW50PHP_P' => array('amt' => 50, 'settle_amt' => 1.12),
        'ESW250PHP_P' => array('amt' => 250, 'settle_amt' => 5.57),
        'ESW500PHP_P' => array('amt' => 500, 'settle_amt' => 11.14),
        'ESW1000PHP_P' => array('amt' => 1000, 'settle_amt' => 22.28),
        'ESW2200PHP_P' => array('amt' => 2200, 'settle_amt' => 49.03),
        
        'ESW12000IDR_P' => array('amt' => 12000, 'settle_amt' => 0.99),
        'ESW45000IDR_P' => array('amt' => 45000, 'settle_amt' => 3.72),
        'ESW60000IDR_P' => array('amt' => 60000, 'settle_amt' => 4.96),
        'ESW90000IDR_P' => array('amt' => 90000, 'settle_amt' => 7.43),
        'ESW120000IDR_P' => array('amt' => 120000, 'settle_amt' => 9.92),
        'ESW250000IDR_P' => array('amt' => 250000, 'settle_amt' => 20.66),
        'ESW400000IDR_P' => array('amt' => 400000, 'settle_amt' => 33.05),
        'ESW600000IDR_P' => array('amt' => 600000, 'settle_amt' => 49.57),
        
        'ESW02_P2' => array('amt' => 2, 'settle_amt' => 1.8),
        'ESW03_P2' => array('amt' => 3, 'settle_amt' => 2.85),
        'ESW05_P2' => array('amt' => 5, 'settle_amt' => 4.5),
        'ESW10_P2' => array('amt' => 10, 'settle_amt' => 9),
        'ESW12_P2' => array('amt' => 12, 'settle_amt' => 11.4),
        'ESW15_P2' => array('amt' => 15, 'settle_amt' => 13.5),
        'ESW20_P2' => array('amt' => 20, 'settle_amt' => 18),
        'ESW30_P2' => array('amt' => 30, 'settle_amt' => 27),
        'ESW50_P2' => array('amt' => 50, 'settle_amt' => 45),
        'ESW60_P2' => array('amt' => 60, 'settle_amt' => 54),
        
        'ESW05MYR_P2' => array('amt' => 5, 'settle_amt' => 1.53),
        'ESW20MYR_P2' => array('amt' => 20, 'settle_amt' => 6.11),
        'ESW50MYR_P2' => array('amt' => 50, 'settle_amt' => 15.28),
        'ESW100MYR_P2' => array('amt' => 100, 'settle_amt' => 30.56),
        'ESW200MYR_P2' => array('amt' => 200, 'settle_amt' => 61.11),
        
        'ESW05SGD_P2' => array('amt' => 5, 'settle_amt' => 3.88),
        'ESW10SGD_P2' => array('amt' => 10, 'settle_amt' => 7.77),
        'ESW20SGD_P2' => array('amt' => 20, 'settle_amt' => 15.53),
        'ESW30SGD_P2' => array('amt' => 30, 'settle_amt' => 23.30),
        'ESW50SGD_P2' => array('amt' => 50, 'settle_amt' => 38.84),
        'ESW75SGD_P2' => array('amt' => 75, 'settle_amt' => 58.25),
        
        'ESW50THB_P2' => array('amt' => 50, 'settle_amt' => 1.52),
        'ESW200THB_P2' => array('amt' => 200, 'settle_amt' => 6.07),
        'ESW350THB_P2' => array('amt' => 350, 'settle_amt' => 10.62),
        'ESW1000THB_P2' => array('amt' => 1000, 'settle_amt' => 30.35),
        'ESW2000THB_P2' => array('amt' => 2000, 'settle_amt' => 60.69),
        
        'ESW50PHP_P2' => array('amt' => 50, 'settle_amt' => 1.12),
        'ESW250PHP_P2' => array('amt' => 250, 'settle_amt' => 5.57),
        'ESW500PHP_P2' => array('amt' => 500, 'settle_amt' => 11.14),
        'ESW1000PHP_P2' => array('amt' => 1000, 'settle_amt' => 22.28),
        'ESW2200PHP_P2' => array('amt' => 2200, 'settle_amt' => 49.03),
        
        'ESW12000IDR_P2' => array('amt' => 12000, 'settle_amt' => 0.99),
        'ESW45000IDR_P2' => array('amt' => 45000, 'settle_amt' => 3.72),
        'ESW60000IDR_P2' => array('amt' => 60000, 'settle_amt' => 4.96),
        'ESW90000IDR_P2' => array('amt' => 90000, 'settle_amt' => 7.43),
        'ESW120000IDR_P2' => array('amt' => 120000, 'settle_amt' => 9.92),
        'ESW250000IDR_P2' => array('amt' => 250000, 'settle_amt' => 20.66),
        'ESW400000IDR_P2' => array('amt' => 400000, 'settle_amt' => 33.05),
        'ESW600000IDR_P2' => array('amt' => 600000, 'settle_amt' => 49.57),
    );
    public static $service_api_pattern = array(
        array('prefix_len' => 3, 'prefix_text' => 'ESW')
    );
    
    private $api_key,
            $api_email,
            $api_password,
            $api_sku;

    public function __construct() {
        parent::__construct();

        $this->api_email = ECSTORE_API_EMAIL;
        $this->api_password = ECSTORE_API_PASSWORD;
        $this->api_key = ECSTORE_API_KEY;

        $this->SUCCESS_STATUSFLAG = 1;
        $this->API_PROVIDER = 'ECLUB';
        $this->CHECK_METHOD = 'order_check';
        $this->GET_METHOD = 'order_submission';
    }

    /*
     * Pattern MINT_500_USD : USD $5.00
     */
    public static function isSupportedSKU($sku) {
        $return = false;
        $skuParts = explode('_', $sku);
        
        if (isset($skuParts[1]) && ($skuParts[1] == 'P' || $skuParts[1] == 'P2')) {
            $pattern_array = self::$service_api_pattern;
            
            foreach ($pattern_array as $pattern) {
                if (substr($sku, 0, $pattern['prefix_len']) === $pattern['prefix_text']) {
                    $return = true;
                    break;
                }
            }
        }

        return $return;
    }

    protected function getAmount($sku) {
        $skuParts = explode('_', $sku);
        
        if (isset($skuParts[0])) {
            return preg_replace("/[^0-9]/","", $skuParts[0]);
        }
        
        return 0;
    }
    
    protected function preConfAPI($sku) {
        $skuParts = explode('_', $sku);
        
        if (isset($skuParts[1]) && $skuParts[1] == 'P2') {
            $this->api_url = ECSTORE_API2_URL;
            $this->api_sku = $skuParts[0] . '_P';
        } else {
            $this->api_url = ECSTORE_API_URL;
            $this->api_sku = $sku;
        }
    }

    protected function generateRequestString($sku) {
        $return_xml = '';

        switch ($this->request_method) {
            case $this->CHECK_METHOD:
                $return_xml = '<?xml version="1.0" encoding="UTF8" ?>' .
                        '<request>' .
                        '<Type>' . $this->request_method . '</Type>' .
                        '<MerchantEmail>' . $this->api_email . '</MerchantEmail>' .
                        '<Signature>' . $this->generateSignature() . '</Signature>' .
                        '<TransactionCode>' . $this->request_transactioncode . '</TransactionCode>' .
                        '</request>';
                break;
            case $this->GET_METHOD:
                $return_xml = '<?xml version="1.0" encoding="UTF8" ?>' .
                        '<request>' .
                        '<Type>' . $this->GET_METHOD . '</Type>' .
                        '<MerchantEmail>' . $this->api_email . '</MerchantEmail>' .
                        '<Signature>' . $this->generateSignature() . '</Signature>' .
                        '<TransactionCode>' . $this->request_transactioncode . '</TransactionCode>' .
                        '<Items>' .
                        '<Item>' .
                        '<SKU>' . $this->api_sku . '</SKU>' .
                        '<Qty>' . $this->request_quantity . '</Qty>' .
                        '</Item>' .
                        '</Items>' .
                        '</request>';
                break;
        }

        return $return_xml;
    }

    /*
     * eclub API does not required to pre-check for code
     */

    protected function checkCode($sku, $qty, $trans_code) {
        return true;
    }

    public function checkOrder($sku, $trans_code) {
        $return_bool = FALSE;

        if (self::isSupportedSKU($sku)) {
            $this->setMethod($this->CHECK_METHOD);
            $this->setTransCode($trans_code);

            $return_bool = $this->preRequest($sku);
        }

        return $return_bool;
    }

    protected function getCode($sku, $qty, $renew_transactioncode = true) {
        $return_bool = FALSE;

        if (self::isSupportedSKU($sku)) {
            $this->preConfAPI($sku);
            $return_bool = parent::getCode($sku, $qty, $renew_transactioncode);
        }

        return $return_bool;
    }

    public function processBatchRestock($product_id, $order_product_store_price, $sku, $qty, $extra_params = array()) {
        return parent::processBatchRestock($product_id, $order_product_store_price, $sku, $qty, $extra_params);
    }

    protected function loadAPIData($raw_response = NULL, $data_array = array()) {
        if (!is_null($raw_response)) {
            $data_array = $this->parseXML($raw_response);
        }

        if (isset($data_array['transactioncode']) && !is_null($data_array['transactioncode'])) {
            $this->resp_transactioncode = $data_array['transactioncode'];
        }

        if (isset($data_array['items']) && is_array($data_array['items'])) {
            $this->resp_items = array();
            $items_array = isset($data_array['items']['item'][0]) ? $data_array['items']['item'] : array($data_array['items']['item']);

            foreach ($items_array as $idx => $item_array) {
                if (isset($item_array['codes']['code'])) {
                    $code_array = is_array($item_array['codes']['code']) ? $item_array['codes']['code'] : array($item_array['codes']['code']);

                    foreach ($code_array as $code) {
                        $this->resp_items[] = array(
                            'log_id' => '',
                            'token' => $code,
                            // 'sku' => $item_array['sku']
                        );
                    }
                }
            }
        }

        if (isset($data_array['status']) && !is_null($data_array['status'])) {
            $this->resp_status = $data_array['status'];
        }

        if (isset($data_array['ordernumber']) && !is_null($data_array['ordernumber'])) {
            $this->resp_ordernumber = $this->api_sku . ':' . (is_string($data_array['ordernumber']) ? $data_array['ordernumber'] : '');
        }

        if (isset($data_array['sysmessage']) && !is_null($data_array['sysmessage'])) {
            $this->resp_error = $data_array['sysmessage'] != 'successful' ? $data_array['sysmessage'] : '';
        }
    }

    private function parseXML($raw_data, $return_array = array()) {
        if (is_string($raw_data)) {
            $xml = simplexml_load_string($raw_data);
            $raw_data = (array) $xml;
        }

        foreach ($raw_data as $key => $value) {
            $key = strtolower($key);

            if (is_object($value) || is_array($value)) {
                $return_array[$key] = $this->parseXML((array) $value);
            } else {
                $return_array[$key] = $value;
            }
        }

        return $return_array;
    }

    private function generateSignature() {
        return $this->merchant_signature($this->api_key . md5($this->api_password) . $this->request_transactioncode);
    }

    private function merchant_signature($source) {
        //formula of string
        //merchant_key . merchant_password . merchant_transaction_code
        return base64_encode($this->hex2bin(sha1($source)));
    }

    private function hex2bin($hexSource) {
        $bin = '';
        for ($i = 0; $i < strlen($hexSource); $i = $i + 2) {
            $bin .= chr(hexdec(substr($hexSource, $i, 2)));
        }
        return $bin;
    }

}

include_once dirname(__FILE__) . DIRECTORY_SEPARATOR . 'config.inc.php';
//include_once dirname(__FILE__) . DIRECTORY_SEPARATOR . 'merchant_api_signature.php';
?>