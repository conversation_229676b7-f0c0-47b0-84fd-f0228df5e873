<?php

include_once dirname(__FILE__) . DIRECTORY_SEPARATOR . '../restock_api.php';
include_once(DIR_WS_CLASSES . 'cache_abstract.php');
include_once(DIR_WS_CLASSES . 'memcache.php');
include_once(DIR_WS_CLASSES . 'curl.php');
include_once(DIR_FS_ADMIN . 'includes/classes/slack_notification.php');

class ecovoucher_api extends restock_api
{
    const KEY_FORMAT = 'Serial : %s<br>Pin : %s<br>Expiry : %s';
    public static $SKU_SUPPORT = array();
    public static $service_api_pattern = array(
        array('prefix_len' => 3, 'prefix_text' => 'ECO')
    );

    public $products_code, $products_amount;

    private $api_key, $api_secret, $base_url, $products_price, $order_product_price, $products_list, $memcached_obj, $cost_margin, $last_error;

    protected $curl_obj;

    public function __construct()
    {
        parent::__construct();
        $this->api_key = ECOVOUCHER_API_KEY;
        $this->api_secret = ECOVOUCHER_API_SECRET;
        $this->base_url = ECOVOUCHER_API_URL;
        $this->API_PROVIDER = 'ECOVOUCHER';
        $this->GET_METHOD = 'issue';
        $this->CHECK_METHOD = 'products';
        $this->min_margin = ECOVOUCHER_MIN_MARGIN;
        $this->low_margin = ECOVOUCHER_LOW_MARGIN;
        $this->cost_margin = ECOVOUCHER_COST_MARGIN;
        $this->memcached_obj = new OGM_Cache_MemCache();
        $this->curl_obj = new curl();
    }

    public static function isSupportedSKU($sku)
    {
        $return = false;
        $skuParts = explode('-', $sku);

        if (isset($skuParts[1])) {
            $pattern_array = self::$service_api_pattern;

            foreach ($pattern_array as $pattern) {
                if (substr($sku, 0, $pattern['prefix_len']) === $pattern['prefix_text']) {
                    $return = true;
                    break;
                }
            }
        }

        return $return;
    }

    protected function getAmount($sku)
    {
        return $this->products_price;
    }

    public function checkCode($sku, $qty, $op_id)
    {
        $key = '/ecovoucher/products/type';
        $cache_data = $this->memcached_obj->fetch($key);
        $this->orders_products_id = $op_id;
        if ($cache_data !== false) {
            $this->products_list = $cache_data;
        } else {
            $this->setMethod($this->CHECK_METHOD);
            $this->preRequest($sku, $this->CHECK_METHOD);
            if ($this->products_list) {
                $this->memcached_obj->store($key, $this->products_list, 3600);
            }
        }
        return $this->checkMargin($sku);
    }

    private function checkMargin($sku)
    {
        list($prefix, $amount, $currency) = explode('_', $sku);
        $country = explode('-', $prefix)[1];
        $amount = substr($amount, 0, -2);
        foreach ($this->products_list as $product) {
            if ($product['currencyCode'] == strtoupper($currency)) {
                foreach ($product['amounts'] as $_amount) {
                    if ($_amount == $amount) {
                        foreach ($product['countries'] as $_country) {
                            if ($_country['isoCode'] == strtoupper($country)) {
                                $this->products_code = $product['code'];
                                $this->products_amount = $_amount;
                                $distributor_cost = (double)$_amount;
                                $distributor_cost = $distributor_cost - ($distributor_cost * ($this->cost_margin / 100));
                                $cost_mst = $this->getCurrencyObj()->advance_currency_conversion($distributor_cost, $product['currencyCode'], DEFAULT_CURRENCY, false, 'spot');
                                $margin = (($this->order_product_price - $cost_mst) / $this->order_product_price) * 100;
                                $margin = number_format($margin, 2);
                                if ($margin > $this->min_margin) {
                                    if ($margin <= $this->low_margin) {
                                        $this->lowMarginReport($sku, $product['currencyCode'], $distributor_cost, $cost_mst, $this->order_product_price, $margin, 'LOW_MARGIN');
                                    }
                                    return true;
                                } else {
                                    $this->lowMarginReport($sku, $product['currencyCode'], $distributor_cost, $cost_mst, $this->order_product_price, $margin, 'MIN_MARGIN');
                                    return false;
                                }
                            }
                        }
                    }
                }
            }
        }
        $this->reportError(array(
            'sku' => $sku,
            'error' => 'No Matched SKU from ECOVOUCHER'
        ), 'preRequest');
        return false;
    }

    public function getCode($sku, $qty, $renew_transactioncode = true)
    {
        $return_bool = false;

        if (self::isSupportedSKU($sku)) {

            $this->setMethod($this->GET_METHOD);
            $this->setQuantity($qty);

            if ($trans_code = $this->logAPIData($sku, self::FLAG_INIT)) {
                $this->setTransCode($trans_code);
                $return_bool = $this->preRequest($sku, $this->GET_METHOD);
            }
        }

        return $return_bool;
    }

    public function processBatchRestock($product_id, $order_product_store_price, $sku, $qty, $extra_params = array())
    {
        $this->order_product_price = $order_product_store_price;
        return parent::processBatchRestock($product_id, $order_product_store_price, $sku, $qty, $extra_params);
    }

    private function generateTimeStamp()
    {
        // datetime format with ISO-8601 format
        return (new DateTime())->format(DateTime::ATOM);
    }

    public function preRequest($sku, $api_method = null, $total_try = 0)
    {
        $return_bool = false;

        if($total_try == 0){
            $this->last_error = null;
        }

        if ($total_try < 3) {

            $this->resetAPIData();
            $_eco_timestamp = $this->generateTimeStamp();
            if ($api_method == $this->GET_METHOD) {
                $code = $this->products_code;
                $amount = number_format($this->products_amount, 2);
                $url = $this->base_url . "/" . $this->GET_METHOD;
                $request_method = 'POST';
                $signature = $this->generateSignature($amount . $this->api_key . $code . $_eco_timestamp . $this->api_secret);
                $body = json_encode(array('distributorCode' => $this->api_key, 'productCode' => $code, 'amount' => $amount));

            } elseif ($api_method == $this->CHECK_METHOD) {
                $request_method = 'GET';
                $url = $this->base_url . "/" . $this->CHECK_METHOD . '/' . $this->api_key;
                $signature = $this->generateSignature($this->api_key . $_eco_timestamp . $this->api_secret);
            }

            $headers = array(
                'Content-Type: application/json',
                'timestamp: ' . $_eco_timestamp,
                'hashCode: ' . $signature
            );

            $response = $this->curl_obj->curl_request($request_method, $url, $headers, $body, false);
            $this->loadAPIData($response);
            $this->logAPIData($sku);

            if ($this->resp_status == true) {
                $return_bool = true;
            } else {
                $return_bool = $this->preRequest($sku, $api_method, ++$total_try);
                $this->reportError(array(
                    'curlError' => $this->curl_obj->get_error(),
                    'sku' => $sku,
                    'method' => $api_method,
                    'try_count' => $total_try,
                    'url' => $url,
                    'key' => $this->api_key,
                    'signature' => $signature,
                    'error_msg' => $this->resp_error,
                    'response' => $response,
                ), 'preRequest');
            }
        }

        return $return_bool;
    }

    protected function loadAPIData($raw_response = null, $data_array = array())
    {
        $this->resp_status = false;
        $error = $this->curl_obj->get_error();
        $status = $this->curl_obj->http_status;
        $data_array = json_decode($raw_response, true);
        if (!empty($raw_response) && $status == 200 && empty($error)) {
            if ($this->request_method == $this->CHECK_METHOD) {
                $this->products_list = $data_array['products'];
                $this->resp_status = true;
            } elseif ($this->request_method == $this->GET_METHOD) {
                $this->products_price = $data_array['amount'];
                $this->resp_currency_code = $data_array['currency'];
                $this->resp_currency_settle_amt = $data_array['amount'] - ($data_array['amount'] * ($this->cost_margin / 100));
                $dt = new DateTime($data_array['expirationTimestamp']);
                $dt->setTimezone(new DateTimeZone('UTC'));
                $expiry = $dt->format('Y-m-d H:i:s') . ' (GMT+0)';
                $this->resp_items[] = array(
                    'log_id' => '',
                    'token' => sprintf(self::KEY_FORMAT, $data_array['code'], $data_array['pin'], $expiry),
                );
                // Ecovoucher does not return transaction code
                $this->resp_transactioncode = $this->request_transactioncode;
                // Ecovoucher does not return order id, use our order id instead
                $this->resp_ordernumber = $this->resp_transactioncode;
                $this->resp_status = true;
            }
        } elseif ($status > 200) {
            if (json_last_error() > 0) {
                if (!empty($this->last_error)) {
                    //Publisher Return Error 429 when too many request
                    $this->resp_error = $this->last_error;
                } else {
                    $this->resp_error = 'Too Many Request / Error Decode Json Content';
                }
            } elseif (!empty($data_array['code']) && !empty($data_array['message'])) {
                $this->resp_error = "[$status]" . $data_array['code'] . " : " . $data_array['message'];
            } else {
                $this->resp_error = json_encode($raw_response);
            }
            $this->last_error = $this->resp_error;
        } elseif (!empty($error)) {
            if ($error['error_message'] && !empty($error['error_message'])) {
                $this->resp_error = $error['error_message'];
            } elseif (isset($error['response']) && !empty($error['response'])) {
                $this->resp_error = $error['response'];
            } else {
                $this->resp_error = $error['error_code'];
            }
        } else {
            $this->resp_error = 'empty response';
        }
    }

    private function generateSignature($payload)
    {
        return hash('sha256', $payload);
    }


}

include_once dirname(__FILE__) . DIRECTORY_SEPARATOR . 'config.inc.php';
