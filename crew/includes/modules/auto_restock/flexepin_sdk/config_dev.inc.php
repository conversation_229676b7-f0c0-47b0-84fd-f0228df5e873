<?php if (!class_exists('flexepin_api')) die('No direct access allowed.');

switch ('development') {
    case 'production':
        define('FLEXEPIN_API_URL', '');
        define('FLEXEPIN_API_KEY', '');
        define('FLEXEPIN_API_SECRET', '');
        define('FLEXEPIN_API_TERMINAL', '');
        define('FLEXEPIN_LOW_MARGIN',3);
        define('FLEXEPIN_MIN_MARGIN',1);
        define('SLACK_WEBHOOK_RESTOCK_MIN_MARGIN','T0ADR1HAN/B1AE4E21G/lNnfxJQBW5gFny58wH0ekq2M');
        break;
    case 'development':
        define('FLEXEPIN_API_URL', 'https://testrest.flexepin.com');
        define('FLEXEPIN_API_KEY', 'OFFGAMER248YDFBE');
        define('FLEXEPIN_API_SECRET', 'GO2746QHGNLOURGN');
        define('FLEXEPIN_API_TERMINAL', 'OFFGAMERS');
        define('FLEXEPIN_LOW_MARGIN',3);
        define('FLEXEPIN_MIN_MARGIN',1);
        define('SLACK_WEBHOOK_RESTOCK_MIN_MARGIN','T0ADR1HAN/B1AE4E21G/lNnfxJQBW5gFny58wH0ekq2M');
        break;
}