<?php

include_once dirname(__FILE__) . DIRECTORY_SEPARATOR . '../restock_api.php';
include_once(DIR_WS_CLASSES . 'cache_abstract.php');
include_once(DIR_WS_CLASSES . 'memcache.php');
include_once(DIR_WS_CLASSES . 'curl.php');
include_once(DIR_FS_ADMIN . 'includes/classes/slack_notification.php');

class flexepin_api extends restock_api
{
    const KEY_FORMAT = 'Serial : %s<br>Pin : %s<br>Expiry : %s (yyyy-mm-dd)';
    public static $SKU_SUPPORT = array();
    public static $service_api_pattern = array(
        array('prefix_len' => 3, 'prefix_text' => 'FLX')
    );

    private $api_key, $api_secret, $base_url, $products_price, $order_product_price, $voucher_list, $memcached_obj;

    protected $curl_obj;

    public function __construct()
    {
        parent::__construct();
        $this->api_key = FLEXEPIN_API_KEY;
        $this->api_secret = FLEXEPIN_API_SECRET;
        $this->base_url = FLEXEPIN_API_URL;
        $this->API_PROVIDER = 'FLEXEPIN';
        $this->GET_METHOD = 'voucher/purchase';
        $this->CHECK_METHOD = 'voucher/types';
        $this->min_margin = FLEXEPIN_MIN_MARGIN;
        $this->low_margin = FLEXEPIN_LOW_MARGIN;
        $this->memcached_obj = new OGM_Cache_MemCache();
        $this->curl_obj = new curl();
    }

    public static function isSupportedSKU($sku)
    {
        $return = false;
        $skuParts = explode('_', $sku);

        if (isset($skuParts[1])) {
            $pattern_array = self::$service_api_pattern;

            foreach ($pattern_array as $pattern) {
                if (substr($sku, 0, $pattern['prefix_len']) === $pattern['prefix_text']) {
                    $return = true;
                    break;
                }
            }
        }

        return $return;
    }

    protected function getAmount($sku)
    {
        return $this->products_price;
    }

    protected function checkCode($sku, $qty, $op_id)
    {
        $key = '/flexepin/voucher/types';
        $cache_data = $this->memcached_obj->fetch($key);
        $this->orders_products_id = $op_id;
        if ($cache_data !== false) {
            $this->voucher_list = $cache_data;
        } else {
            $this->setMethod($this->CHECK_METHOD);
            $this->preRequest($sku, $this->CHECK_METHOD);
            if ($this->voucher_list) {
                $this->memcached_obj->store($key, $this->voucher_list, 3600);
            }
        }
        return $this->checkMargin($sku);
    }

    private function checkMargin($sku)
    {
        list($code, $value) = $this->getPublisherSKU($sku);
        $orders_id = $this->getOrdersIdByOrdersProductsId($this->orders_products_id);
        $orders_currency = $this->getOrdersCurrencyRate($orders_id);
        foreach ($this->voucher_list as $voucher) {
            if ($voucher['code'] == $code) {
                $distributor_cost = (double)$voucher['distributor_cost'];
                $cost_mst = $this->getCurrencyObj()->advance_currency_conversion($distributor_cost, $voucher['currency'], DEFAULT_CURRENCY, false, 'spot');
                if(isset($orders_currency['currency']) && isset($orders_currency['currency_value']) && $orders_currency['currency'] == strtoupper($voucher['currency'])){
                    $selling_price = $this->order_product_price * $orders_currency['currency_value'];
                    $margin = (($selling_price - $distributor_cost) / $selling_price) * 100;
                }
                else{
                    $margin = (($this->order_product_price - $cost_mst) / $this->order_product_price) * 100;
                }
                $margin = number_format($margin, 2);
                if ($margin > $this->min_margin) {
                    if ($margin  <= $this->low_margin) {
                        $this->lowMarginReport($code, $voucher['currency'], $distributor_cost, $cost_mst, $this->order_product_price, $margin, 'LOW_MARGIN');
                    }
                    return true;
                } else {
                    $this->lowMarginReport($code, $voucher['currency'], $distributor_cost, $cost_mst, $this->order_product_price, $margin, 'MIN_MARGIN');
                    return false;
                }
            }
        }
        $this->reportError(array(
            'sku' => $sku,
            'error' => 'No Matched SKU from FLEXEPIN'
        ), 'preRequest');
        return false;
    }

    public function getCode($sku, $qty, $renew_transactioncode = true)
    {
        $return_bool = false;

        if (self::isSupportedSKU($sku)) {
            $this->setMethod($this->GET_METHOD);
            $this->setQuantity($qty);

            if ($trans_code = $this->logAPIData($sku, self::FLAG_INIT)) {
                $this->setTransCode($trans_code);
                $return_bool = $this->preRequest($sku, $this->GET_METHOD);
            }
        }

        return $return_bool;
    }

    public function processBatchRestock($product_id, $order_product_store_price, $sku, $qty, $extra_params = array())
    {
        $this->order_product_price = $order_product_store_price;
        return parent::processBatchRestock($product_id, $order_product_store_price, $sku, $qty, $extra_params);
    }

    public function preRequest($sku, $api_method = null, $total_try = 0)
    {
        $return_bool = false;
        $response = '';
        $errorRespond = '';
        $theParam = '';

        if ($total_try < 3) {

            $this->resetAPIData();

            if ($api_method == $this->GET_METHOD) {
                $requestUri = 'voucher/purchase';
                $requestMethod = 'PUT';
                $transaction_id = (string)$this->request_transactioncode;
                $qty = "1";
                $terminalId = FLEXEPIN_API_TERMINAL;
                list($code, $value) = $this->getPublisherSKU($sku);
                $param = $code . "/" . $value . "/" . $qty . "/" . $terminalId . "/" . $transaction_id;
            } else {
                if ($api_method == $this->CHECK_METHOD) {
                    $requestUri = $this->CHECK_METHOD;
                    $requestMethod = 'GET';
                }
            }

            $requestUri = '/' . $requestUri;
            if (isset($param)) {
                $theParam = '/' . $param;
            }
            $url = $this->base_url . $requestUri . $theParam;

            // Generate a nonce as microtime, with as-string handling to avoid problems with 32bits systems
            $mt = explode(' ', microtime());
            $nonce = $mt[1] . substr($mt[0], 2, 6);

            $payload = '';
            $payload .= $requestMethod . "\n";
            $payload .= $requestUri . $theParam . "\n";
            $payload .= $nonce . "\n";

            $signature = $this->generateSignature($payload);

            $headers = array('AUTHENTICATION: HMAC ' . $this->api_key . ':' . $signature . ':' . $nonce);
            $response = $this->curl_obj->curl_request($requestMethod, $url, $headers);

            $this->loadAPIData($response);
            $this->logAPIData($sku);

            if ($this->resp_status == true) {
                $return_bool = true;
            } else {
                $return_bool = $this->preRequest($sku, $api_method, ++$total_try);
                $this->reportError(array(
                    'curlError' => $this->curl_obj->get_error(),
                    'sku' => $sku,
                    'method' => $api_method,
                    'try_count' => $total_try,
                    'url' => $url,
                    'key' => $this->api_key,
                    'nonce' => $nonce,
                    'payload' => $payload,
                    'signature' => $signature,
                    'error_msg' => $this->resp_error,
                    'response' => $response,
                    'Error Respond' => $errorRespond
                ), 'preRequest');
            }
        }

        return $return_bool;
    }

    private function getPublisherSKU($sku)
    {
        $sku = explode('_', $sku);
        $amt = $amt_str = substr($sku[1], 0, -2);
        while (strlen($amt_str) < 3) {
            $amt_str = '0' . $amt_str;
        }
        $publisher_sku = 'FLEX' . $sku[2] . $amt_str;
        return array($publisher_sku, $amt);
    }

    protected function loadAPIData($raw_response = null, $data_array = array())
    {
        if (!empty($raw_response)) {
            $data_array = json_decode($raw_response, true);

            if (isset($data_array['error'])) {
                if (isset($data_array['description'])) {
                    $this->resp_error = $this->getErrorMsg($data_array['error']);
                } else {
                    $this->resp_error = $data_array['error'];
                }
            }
            if (isset($data_array['result']) && $data_array['result'] == 0) {
                if (isset($data_array['transaction_id']) && !is_null($data_array['transaction_id'])) {
                    $this->resp_transactioncode = $data_array['transaction_id'];
                }

                if (isset($data_array['trans_no']) && !is_null($data_array['trans_no'])) {
                    $this->resp_ordernumber = $data_array['trans_no'];
                } else {
                    $this->resp_ordernumber = '';
                }

                if ($this->request_method == $this->GET_METHOD && isset($data_array['vouchers']) && is_array($data_array['vouchers'])) {
                    $this->resp_items = array();
                    $items_array = $data_array['vouchers'];

                    foreach ($items_array as $idx => $item_array) {
                        $this->products_price = $item_array['value'];
                        $this->resp_currency_code = $item_array['currency'];
                        $this->resp_currency_settle_amt = $item_array['cost'];
                        $this->resp_items[] = array(
                            'log_id' => '',
                            'token' => sprintf(self::KEY_FORMAT, $item_array['serial'], $item_array['pin'], $item_array['expiry']),
                        );
                    }
                }
                if ($this->request_method == $this->CHECK_METHOD && isset($data_array['types']) && is_array($data_array['types'])) {
                    $this->voucher_list = $data_array['types'];
                }
                $this->resp_status = true;
            } else {
                $this->resp_status = false;
                if (isset($data_array['result'])) {
                    $this->resp_error = $this->getErrorMsg($data_array['result']);
                } else {
                    if (isset($data_array['description'])) {
                        $this->resp_error = $data_array['description'];
                    } else {
                        if (!empty($data_array)) {
                            $this->resp_error = json_encode($data_array);
                        } else {
                            $this->resp_error = 'empty response';
                        }
                    }
                }
            }
        } else {
            $this->resp_error = 'empty response';
        }
    }

    private function getErrorMsg($status)
    {
        $str = '';
        $status = (int)$status;
        switch ($status) {
            case 0 :
                $str = 'success';
                break;
            case 503:
                $str = 'Error - Maintenance Mode';
                break;
            case 4019:
                $str = 'Voucher Type or Value is not accepted';
                break;
            case 4043:
                $str = 'Voucher Type or Value is not enabled';
                break;
            case 4075:
                $str = 'The voucher can not be found';
                break;
                break;
            case 5000:
                $str = 'Store data is not available';
                break;
            case 5500:
                $str = 'Transaction Value Exceeded';
                break;
            case 5501:
                $str = 'Transaction Count Exceeded';
                break;
            default:
                $str = (string)$status;
                break;
        }
        return $str;
    }

    private function generateSignature($payload)
    {
        return hash_hmac('sha256', $payload, $this->api_secret);
    }


}

include_once dirname(__FILE__) . DIRECTORY_SEPARATOR . 'config.inc.php';
