<?php

include_once dirname(__FILE__) . DIRECTORY_SEPARATOR . '../restock_api.php';
include_once(DIR_WS_CLASSES . 'cache_abstract.php');
include_once(DIR_WS_CLASSES . 'memcache.php');
include_once(DIR_WS_CLASSES . 'curl.php');
include_once(DIR_FS_ADMIN . 'includes/classes/slack_notification.php');

class fulu_api extends restock_api
{
    const CHECK_METHOD = 'kamenwang.goods.get', GET_METHOD = 'kamenwang.order.cardorder.add';
    public static $CODE_FORMAT_MAPPING = array(
        'CardNumber' => 'Serial (卡号)',
        'CardPwd' => 'Pin (卡密)',
        'CardDeadline' => 'Expiry'
    );
    public static $SKU_SUPPORT = array();
    public static $service_api_pattern = array(
    );

    private $api_key, $api_secret, $request_url, $products_price, $order_product_price, $product_details, $memcached_obj, $resp_error_no;

    protected $curl_obj;

    public function __construct()
    {
        parent::__construct();
        $this->api_key = FULU_API_ID;
        $this->api_secret = FULU_API_SECRET;
        $this->request_url = FULU_API_URL;
        $this->API_PROVIDER = 'FULU';
        $this->GET_METHOD = 'voucher/purchase';
        $this->CHECK_METHOD = 'voucher/types';
        $this->min_margin = FULU_MIN_MARGIN;
        $this->low_margin = FULU_LOW_MARGIN;
        $this->memcached_obj = new OGM_Cache_MemCache();
        $this->curl_obj = new curl();
    }

    public static function isSupportedSKU($sku)
    {
        $return = false;
        $skuParts = explode('_', $sku);

        if (isset($skuParts[1])) {
            $pattern_array = self::$service_api_pattern;

            foreach ($pattern_array as $pattern) {
                if (substr($sku, 0, $pattern['prefix_len']) === $pattern['prefix_text']) {
                    $return = true;
                    break;
                }
            }
        }

        return $return;
    }

    protected function getAmount($sku)
    {
        return $this->products_price;
    }

    protected function checkCode($sku, $qty, $op_id)
    {
        $key = '/fulu/products/' . $sku;
        $cache_data = $this->memcached_obj->fetch($key);
        $this->orders_products_id = $op_id;
        if ($cache_data !== false) {
            $this->product_details = $cache_data;
        } else {
            $this->setMethod(self::CHECK_METHOD);
            $this->preRequest($sku, self::CHECK_METHOD);
            if ($this->product_details) {
                $this->memcached_obj->store($key, $this->product_details, 3600);
            }
        }
        return $this->checkMargin($sku);
    }

    private function checkMargin($sku)
    {
        $code = $this->getPublisherSKU($sku);
        if ($this->product_details['GoodsID'] == $code) {
            $distributor_cost = (double)$this->product_details['PurchasePrice'];
            $cost_mst = $this->getCurrencyObj()->advance_currency_conversion($distributor_cost, 'CNY', DEFAULT_CURRENCY,
                false, 'spot');
            $margin = (($this->order_product_price - $cost_mst) / $this->order_product_price) * 100;
            $margin = number_format($margin, 2);
            if ($margin > $this->min_margin) {
                if ($margin <= $this->low_margin) {
                    $this->lowMarginReport($code, 'CNY', $distributor_cost, $cost_mst, $this->order_product_price,
                        $margin, 'LOW_MARGIN');
                }
                return true;
            } else {
                $this->lowMarginReport($code, 'CNY', $distributor_cost, $cost_mst, $this->order_product_price, $margin,
                    'MIN_MARGIN');
                return false;
            }
        }
        $this->reportError(array(
            'sku' => $sku,
            'error' => 'No Matched SKU from FULU'
        ), 'preRequest');
        return false;
    }

    public function getCode($sku, $qty, $renew_transactioncode = true)
    {
        $return_bool = false;
        if (self::isSupportedSKU($sku)) {
            $this->setMethod(self::GET_METHOD);
            $this->setQuantity($qty);

            if ($trans_code = $this->logAPIData($sku, self::FLAG_INIT)) {
                $this->setTransCode($trans_code);
                $return_bool = $this->preRequest($sku, self::GET_METHOD);
            }
        }

        return $return_bool;
    }

    public function processBatchRestock($product_id, $order_product_store_price, $sku, $qty, $extra_params = array())
    {
        $this->order_product_price = $order_product_store_price;
        return parent::processBatchRestock($product_id, $order_product_store_price, $sku, $qty, $extra_params);
    }

    public function preRequest($sku, $api_method = null, $total_try = 0)
    {
        $return_bool = false;
        $publisher_sku = $this->getPublisherSKU($sku);
        if ($total_try < 3) {
            $this->resetAPIData();
            if ($api_method == self::GET_METHOD) {
                $params = array(
                    'customerid' => $this->api_key,
                    'method' => self::GET_METHOD,
                    'timestamp' => $this->getRequestTimeStamp(),
                    'productid' => $publisher_sku,
                    'customerorderno' => (string)$this->request_transactioncode,
                    'buynum' => 1,
                    'v' => '1.0'
                );
            } elseif ($api_method == self::CHECK_METHOD) {
                $params = array(
                    'customerid' => $this->api_key,
                    'method' => self::CHECK_METHOD,
                    'timestamp' => $this->getRequestTimeStamp(),
                    'goodsid' => $publisher_sku,
                    'v' => '1.0'
                );
            } else {
                return false;
            }

            $response = $this->processRequest($params);
            $this->loadAPIData($response);
            $this->logAPIData($sku);

            if ($this->resp_status == true) {
                $return_bool = true;
            } else {
                $return_bool = $this->preRequest($sku, $api_method, ++$total_try);
                $this->reportError(array(
                    'curlError' => $this->curl_obj->get_error(),
                    'sku' => $sku,
                    'method' => $api_method,
                    'try_count' => $total_try,
                    'key' => $this->api_key,
                    'error' => $this->curl_obj->get_error(),
                    'error_msg' => $this->resp_error,
                    'response' => $response
                ), 'preRequest');
            }
        }

        return $return_bool;
    }

    private function getPublisherSKU($sku)
    {
        $sku = explode('_', $sku);
        return $sku[1];
    }

    protected function loadAPIData($raw_response = null)
    {
        if ($this->validateResponse($raw_response, $this->curl_obj->get_error())) {
            if (empty($this->resp_error_no)) {
                if ($this->request_method == self::GET_METHOD) {
                    if (isset($raw_response['OrderId']) && !is_null($raw_response['OrderId'])) {
                        $this->resp_transactioncode = $this->request_transactioncode;
                        $this->resp_ordernumber = $raw_response['OrderId'];
                        $this->resp_items = array();
                        $items_array = $raw_response['Cards'];
                        $this->resp_currency_code = 'CNY';
                        $this->resp_currency_settle_amt = $raw_response['PurchasePrice'];
                        foreach ($items_array as $idx => $item_array) {
                            $token = array();
                            foreach (self::$CODE_FORMAT_MAPPING as $key => $value) {
                                if (isset($item_array[$key])) {
                                    $token = $value . ' : ' . $item_array[$key];
                                    unset($item_array[$key]);
                                }
                            }
                            foreach ($item_array as $key => $value) {
                                $token[] = $key . ' : ' . $value;
                            }
                            $this->resp_items[] = array(
                                'log_id' => '',
                                'token' => implode("<br><br>", $token)
                            );
                        }
                    }
                }
                if ($this->request_method == self::CHECK_METHOD) {
                    $this->product_details = $raw_response['GoodsInfo'];
                }
                $this->resp_status = true;
            } else {
                $this->resp_status = false;
                $this->resp_error = $this->curl_obj->get_error();
            }
        }
    }

    private function getRequestTimestamp()
    {
        return date("Y-m-d H:i:s");
    }

    private function generatePostFields($params = array(), $secret)
    {
        ksort($params);
        $postFields = urldecode(http_build_query($params, '', '&'));
        $postFields .= '&sign=' . $this->generateSignature($postFields . $secret);
        return $postFields;
    }

    private function generateSignature($str)
    {
        return md5($str);
    }

    private function processRequest($params = array())
    {
        $postFields = $this->generatePostFields($params, $this->api_secret);
        $response = $this->curl_obj->curl_request('POST', $this->request_url, array(), $postFields);
        $response = $this->parse_xml_to_array($response);
        return $response;
    }

    private function validateResponse($response, $curl_error)
    {
        if (empty($curl_error)) {
            if (isset($response['MessageCode'])) {
                $this->resp_error_no = $response['MessageCode'];
                $this->resp_error = $response['MessageInfo'];
                return false;
            } else {
                return true;
            }
        }
        $this->resp_error_no = $curl_error['error_code'];
        $this->resp_error = $curl_error['error_message'];
        return false;
    }

    public function parse_xml_to_array($raw_xml)
    {
        $rx = '/\<\?xml.*encoding=[\'"](.*?)[\'"].*?>/m';

        if (preg_match($rx, $raw_xml, $m)) {
            $encoding = strtoupper($m[1]);
        } else {
            $encoding = "UTF-8";
        }

        if ($encoding == "UTF-8" || $encoding == "US-ASCII" || $encoding == "ISO-8859-1") {
            $parser = xml_parser_create($encoding);
        } else {
            if (function_exists('mb_convert_encoding')) {
                $encoded_source = @mb_convert_encoding($raw_xml, "UTF-8", $encoding);
            }

            if ($encoded_source != null) {
                $raw_xml = str_replace($m[0], '<?xml version="1.0" encoding="utf-8"?>', $encoded_source);
            }
        }
        $xml_object = simplexml_load_string($raw_xml);
        $xml_array = json_decode(@json_encode($xml_object), 1);

        return $xml_array;
    }


}

include_once dirname(__FILE__) . DIRECTORY_SEPARATOR . 'config.inc.php';
