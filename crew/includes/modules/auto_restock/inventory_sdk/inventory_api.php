<?php

include_once dirname(__FILE__) . DIRECTORY_SEPARATOR . '../ms_inventory_api.php';
include_once(DIR_WS_CLASSES . 'cache_abstract.php');
include_once(DIR_WS_CLASSES . 'memcache.php');
include_once(DIR_WS_CLASSES . 'curl.php');
include_once(DIR_FS_ADMIN . 'includes/classes/slack_notification.php');

class inventory_api extends ms_inventory_api
{
    public static $service_api_pattern = array(
        array('prefix_len' => 3, 'prefix_text' => 'CWS'),
        array('prefix_len' => 2, 'prefix_text' => 'MR'),
        array('prefix_len' => 3,'prefix_text' => 'UNI'),
        array('prefix_len' => 3,'prefix_text' => 'RZR'),
        array('prefix_len' => 3,'prefix_text' => 'CLB'),
        array('prefix_len' => 3,'prefix_text' => 'APZ'),
        array('prefix_len' => 3,'prefix_text' => 'BHN'),
        array('prefix_len' => 3,'prefix_text' => 'CMP'),
        array('prefix_len' => 7,'prefix_text' => 'SVTC365'),
        array('prefix_len' => 6,'prefix_text' => 'VTC365'),
        array('prefix_len' => 4,'prefix_text' => 'VVTC'),
        array('prefix_len' => 4,'prefix_text' => 'SVTC'),
        array('prefix_len' => 6,'prefix_text' => 'BAMBOO'),
        array('prefix_len' => 3,'prefix_text' => 'MYC'),
        array('prefix_len' => 4,'prefix_text' => 'PISB'),
        array('prefix_len' => 3,'prefix_text' => 'JTN'),
        array('prefix_len' => 4,'prefix_text' => 'FULU'),
        array('prefix_len' => 4,'prefix_text' => 'BIGO'),
        array('prefix_len' => 5,'prefix_text' => 'BHNV2'),
        array('prefix_len' => 4,'prefix_text' => 'TWBS'),
        array('prefix_len' => 4,'prefix_text' => 'AZTC'),
        array('prefix_len' => 4,'prefix_text' => 'XOXO'),
        array('prefix_len' => 4,'prefix_text' => 'OBV2'),
        array('prefix_len' => 4,'prefix_text' => 'TIKI'),
        array('prefix_len' => 2, 'prefix_text' => 'PF'),
        array('prefix_len' => 3, 'prefix_text' => 'BST'),
        array('prefix_len' => 5, 'prefix_text' => 'DEVCO'),
        array('prefix_len' => 2, 'prefix_text' => 'QC'),
        array('prefix_len' => 3, 'prefix_text' => 'BNV'),
        array('prefix_len' => 3, 'prefix_text' => 'ASP'),
        array('prefix_len' => 3, 'prefix_text' => 'ENB'),
        array('prefix_len' => 4, 'prefix_text' => 'WOGI'),
        array('prefix_len' => 5, 'prefix_text' => 'RAZER'),
        array('prefix_len' => 3, 'prefix_text' => 'ECV'),
        array('prefix_len' => 3, 'prefix_text' => 'EZC'),
        array('prefix_len' => 7, 'prefix_text' => 'DTONEGC'),
        array('prefix_len' => 3, 'prefix_text' => 'LKC'),
        array('prefix_len' => 4, 'prefix_text' => 'AQPG'),
        array('prefix_len' => 3, 'prefix_text' => 'PRO'),
        array('prefix_len' => 3, 'prefix_text' => 'NEO'),
    );
}