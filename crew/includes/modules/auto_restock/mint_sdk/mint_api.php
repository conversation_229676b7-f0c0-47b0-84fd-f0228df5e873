<?php
include_once dirname(__FILE__) . DIRECTORY_SEPARATOR . '../restock_api.php';

class mint_api extends restock_api 
{
    const SUCCESS_STATUSFLAG = 'success';
    const SKU_MARGIN = 6.5; //percent 

    public static $service_api_pattern = array(
        array('prefix_len' => 4, 'prefix_text' => 'MINT')
    );
    private $api_public_key,
            $api_private_key;

    public function __construct() {
        parent::__construct();

        $this->api_url = MINT_API_URL;
        $this->api_public_key = MINT_API_PUBLIC_KEY;
        $this->api_private_key = MINT_API_PRIVATE_KEY;
        
        $this->API_PROVIDER = 'MINT';
        $this->GET_METHOD = 'generateEPin';
    }

    /*
     * Pattern MINT_500_USD : USD $5.00
     */
    public static function isSupportedSKU($sku) {
        $return = false;
        $skuParts = explode('_', $sku);

        if (isset($skuParts[1]) && strlen($skuParts[1]) > 2 && isset($skuParts[2])) {
            $pattern_array = self::$service_api_pattern;
            
            foreach ($pattern_array as $pattern) {
                if (substr($sku, 0, $pattern['prefix_len']) === $pattern['prefix_text']) {
                    $return = true;
                    break;
                }
            }
        }

        return $return;
    }

    private function generateSignature($timestamp, $params) {
        return md5($this->api_public_key . $timestamp . implode('', $params) . $this->api_private_key);
    }

    protected function generateRequestString($sku) {
        $timestamp = time();
        $params = array(
            $this->getCurrency($sku),
            $this->getAmount($sku),
            $this->getService($sku),
        );

        return json_encode(array(
            'jsonrpc' => "2.0",
            'method' => $this->request_method,
            'params' => array_merge(
                    array(array(
                    'publicKey' => $this->api_public_key,
                    'timestamp' => (string) $timestamp,
                    'signature' => $this->generateSignature($timestamp, $params)
                )), $params
            ),
            'id' => $this->request_transactioncode
        ));
    }

    /* 
     * MINT API does not required to pre-check for code
     */
    protected function checkCode($sku, $qty, $trans_code) {
        return true;
    }

    protected function getCode($sku, $qty, $renew_transactioncode = true) {
        $return_bool = FALSE;

        if (self::isSupportedSKU($sku)) {
            $return_bool = parent::getCode($sku, $qty, $renew_transactioncode);
        }

        return $return_bool;
    }

    protected function getAmount($sku) {
        $skuParts = explode('_', $sku);
        // $return_amt = floatval(substr($skuParts[1], 0, strlen($skuParts[1]) - 2) . '.' . substr($skuParts[1], -1, 2));
        $return_amt = number_format(($skuParts[1]/100), 2, '.', '');
        
        return $return_amt;
    }

    protected function getCurrency($sku) {
        $skuParts = explode('_', $sku);

        return $skuParts[2];
    }

    private function getService($sku) {
        return 0;
    }

    protected function loadAPIData($raw_response = NULL, $data_array = array()) {
        if (!is_null($raw_response)) {
            $data_array = json_decode($raw_response, true);
        }

        if (isset($data_array['result'])) {
            $this->resp_items = array();
            
            switch ($this->request_method) {
                case $this->GET_METHOD;
                    $this->resp_items[] = array(
                        'log_id' => '',
                        'token' => $data_array['result']['code'],
                        // 'sku' => ''
                    );
                    break;
            }

            $this->resp_status = self::SUCCESS_STATUSFLAG;
        }

        if (isset($data_array['id']) && !is_null($data_array['id'])) {
            $this->resp_transactioncode = $this->resp_ordernumber = $data_array['id'];
        }

        if (isset($data_array['error']) && !is_null($data_array['error'])) {
            $this->resp_error = $data_array['error']['code'] . ' : ' . $data_array['error']['message'];
            $this->resp_status = 'error';

            $this->reportError(array('request_transactioncode' => $this->request_transactioncode, 'raw_response' => $raw_response), 'loadAPIData');
        }
    }

    public function processBatchRestock($product_id, $order_product_store_price, $sku, $qty, $extra_params = array()) {
        return parent::processBatchRestock($product_id, $order_product_store_price, $sku, $qty, $extra_params);
    }

    protected function logAPIData($sku, $flag_state = '', $log_id = 0, $custom_products_code_id = '') {
        $return_init = 0;
        $flag_state = $flag_state != '' ? $flag_state : ($this->resp_status != self::SUCCESS_STATUSFLAG ? self::FLAG_ERROR : self::FLAG_NEW);

        switch ($flag_state) {
            case self::FLAG_NEW:
                if (is_array($this->resp_items) && count($this->resp_items) == 1 && tep_not_empty($this->request_transactioncode)) {
                    $settle_amount_info = $this->getSettleAmount($sku);
                    
                    $query_array = array(
                        'method' => $this->request_method,
                        'sku' => tep_db_prepare_input($sku),
                        'currency_code' => $settle_amount_info['currency_code'],
                        'currency_rate' => $settle_amount_info['currency_rate'],
                        'currency_settle_amount' => $settle_amount_info['currency_settle_amount'],
                        'amount' => $this->getAmount($sku),
                        'settle_amount' => $settle_amount_info['settle_amount'],
                        'ack' => tep_db_prepare_input($this->resp_status),
                        'serialnumber' => tep_db_prepare_input($this->resp_ordernumber),
                        'flag_state' => $flag_state
                    );
                    
                    if ($this->request_transactioncode == $this->resp_transactioncode) {
                        $query_array['token'] = tep_db_prepare_input($this->resp_items[0]['token']);
                        if (tep_db_perform(TABLE_LOG_API_RESTOCK, $query_array, 'update', " id = '" . $this->request_transactioncode . "'")) {
                            $this->resp_items[0]['log_id'] = $this->request_transactioncode;
                            
                            $return_init = $this->request_transactioncode;
                        } else {
                            $this->resp_items[0]['log_id'] = FALSE;
                            $this->reportError($query_array, 'Failed to update logAPIData()');
                        }
                    } else {
                        $this->resp_items[0]['log_id'] = FALSE;
                        $this->reportError($query_array, 'Failed to update logAPIData() due to request_transactioncode !== resp_transactioncode ' . $this->request_transactioncode . ' !== ' . $this->resp_transactioncode);
                    }                    
                }
                break;
            case self::FLAG_INIT:
                $query_array = array(
                    'method' => $this->request_method,
                    'sku' => tep_db_prepare_input($sku),
                    'currency_code' => '',
                    'currency_rate' => '',
                    'flag_state' => $flag_state,
                    'api_provider' => $this->API_PROVIDER,
                    'created_datetime' => 'now()'
                );

                if (tep_db_perform(TABLE_LOG_API_RESTOCK, $query_array)) {
                    $return_init = tep_db_insert_id();
                }
                break;
            case self::FLAG_UPLOADING:
                $sql_data_array = array(
                    'flag_state' => $flag_state
                );
                tep_db_perform(TABLE_LOG_API_RESTOCK, $sql_data_array, 'update', " id = '" . $log_id . "'");
                break;
            case self::FLAG_UPLOADED:
                
                $publisher_id = $this->getPublisherId($log_id);
                
                $sql_data_array = array(
                    'flag_state' => $flag_state,
                    'custom_products_code_id' => $custom_products_code_id,
                    'publishers_id' => $publisher_id,
                    'token' => ''
                );
                tep_db_perform(TABLE_LOG_API_RESTOCK, $sql_data_array, 'update', " id = '" . $log_id . "'");
                break;
            default:
                $query_array = array(
                    'method' => $this->request_method,
                    'sku' => tep_db_prepare_input($sku),
                    'currency_code' => '',
                    'currency_rate' => '',
                    'ack' => tep_db_prepare_input($this->resp_status),
                    'serialnumber' => tep_db_prepare_input($this->resp_ordernumber),
                    'flag_state' => $flag_state,
                    'api_provider' => $this->API_PROVIDER,
                    'error_msg' => tep_db_prepare_input($this->resp_error),
                    'created_datetime' => 'now()'
                );

                if (!tep_db_perform(TABLE_LOG_API_RESTOCK, $query_array)) {
                    $this->reportError($query_array, 'Failed to insert error in logAPIData()');
                }
                break;
        }

        return $return_init;
    }

}

include_once dirname(__FILE__) . DIRECTORY_SEPARATOR . 'config.inc.php';
?>