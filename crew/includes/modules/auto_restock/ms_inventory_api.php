<?php

include_once(DIR_WS_CLASSES . 'cache_abstract.php');
include_once(DIR_WS_CLASSES . 'memcache.php');
include_once(DIR_WS_CLASSES . 'curl.php');
include_once(DIR_FS_ADMIN . 'includes/classes/slack_notification.php');

abstract class ms_inventory_api
{
    protected $curl_obj,
        $cpc_obj,
        $currency_obj;

    public static $service_api_pattern = array(
        array('prefix_len' => 3, 'prefix_text' => 'OGM')
    );

    public function __construct()
    {
        include_once(DIR_WS_CLASSES . 'curl.php');
        include_once(DIR_WS_CLASSES . 'custom_product_code.php');
        include_once(DIR_WS_LANGUAGES . 'english.php');

        $this->curl_obj = new curl();
        $this->cpc_obj = new custom_product_code();
    }


    public static function isSupportedSKU($sku)
    {
        $return = false;
        $skuParts = explode('_', $sku);

        if (isset($skuParts[1])) {
            $pattern_array = static::$service_api_pattern;

            foreach ($pattern_array as $pattern) {
                if (substr($sku, 0, $pattern['prefix_len']) === $pattern['prefix_text']) {
                    $return = true;
                    break;
                }
            }
        }

        return $return;
    }

    public function processBatchRestock($product_id, $order_product_store_price, $sku, $qty, $extra_params = array())
    {
        $orders_products_id = isset($extra_params['orders_products_id']) ? $extra_params['orders_products_id'] : '';
        try {
            $params = array(
                'sku' => $sku,
                'products_id' => (int)$product_id,
                'orders_products_id' => (int)$orders_products_id,
                'quantity' => (int)$qty,
                'orders_products_price' => (double)$order_product_store_price,
                'orders_id' => 0
            );

            $this->apiRestockRequest($params);

        } catch (Exception $e) {

        }

        return true;
    }

    protected function apiRestockRequest($params)
    {
        $headers = array(
            'Content-Type: application/json'
        );

        $config = unserialize(MICRO_SERVICE_ORDER);

        $timestamp = time();

        $attach = array(
            'api_key' => $config['key'],
            'timestamp' => $timestamp,
        );

        $body = array_merge($attach, ['data' => $params]);

        ksort($body);

        $body = json_encode($body);

        $signature = hash_hmac('sha256', $body, $config['secret']);

        $headers[] = 'signature: ' . $signature;

        $this->curl_obj->timeout = 300;

        $response = $this->curl_obj->curl_request('POST', $config['baseUrl'] . '/restock/index', $headers, $body, false);

    }
}