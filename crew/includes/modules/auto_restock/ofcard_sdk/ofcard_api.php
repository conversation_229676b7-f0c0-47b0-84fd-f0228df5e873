<?php

include_once dirname(__FILE__) . DIRECTORY_SEPARATOR . '../restock_api.php';

class ofcard_api extends restock_api {

    const API_VERSION = '6.0';
    const SUCCESS_ACK_FLAG = '1';
    const MIN_MARGIN = 0.05; //percent;
    const OFCARD_CURRENCY = 'CNY';
    const KEY_FORMAT = 'Card No : %s<br>Card Pass : %s<br>Expiry Date : %s (yyyy-mm-dd)';

    public static $service_api_pattern = array(
        array('prefix_len' => 6, 'prefix_text' => 'OFCARD')
    );
    public $request_ofcard_merchant_balance;
    public $request_ofcard_transactioncode;
    public $ofcard_actual_amount;
    public $api_cardinfo_url;
    public $order_product_price;
    public $resp_card_id;
    public $resp_settle_amt;
    public $resp_error_code;
    private $user_id = OFCART_API_USER_ID,
            $user_password = OFCART_API_USER_PASSWORD;

    public function __construct() {
        parent::__construct();

        $this->api_url = OFCART_API_URL;
        $this->api_cardinfo_url = OFCART_API_CARDINFO_URL;
        $this->API_PROVIDER = 'OFCARD';
        $this->CHECK_METHOD = 'CheckCard';
        $this->GET_METHOD = 'GetCard';
        $this->SUCCESS_STATUSFLAG = TRUE;
        
        $this->curl_obj = new curl();
    }

    public static function isSupportedSKU($sku) {
        $return = false;

        $skuParts = explode('_', $sku);
        # pattern OFCARD_NNNNN
        if (isset($skuParts[1])) {
            $pattern_array = self::$service_api_pattern;
            
            foreach ($pattern_array as $pattern) {
                if (substr($sku, 0, $pattern['prefix_len']) === $pattern['prefix_text']) {
                    $return = true;
                    break;
                }
            }
        }

        return $return;
    }

    protected function getServiceCode($sku) {
        $skuParts = explode('_', $sku);

        return 'OFCARD' . str_pad($skuParts[1], 4, "0", STR_PAD_LEFT);
    }

    protected function getAmount($sku) {
        return $this->ofcard_actual_amount;
    }

    private function generateRequestArray($process, $sku) {
        $return_data = '';
        $sporder_time = date('YmdHis');
        $skuParts = explode('_', $sku);
        $cardid = isset($skuParts[1]) ? $skuParts[1] : '';
        $userpws = $this->generateSignature($this->user_password);
        $KeyStr = 'OFCARD';
        
        switch ($process) {
            case 'product_price_check':
                $return_data = array(
                    'userid' => $this->user_id,
                    'userpws' => $userpws,
                    'cardid' => $cardid, //product id 
                    'version' => self::API_VERSION
                );
                break;
            case 'buy_product':
                $return_data = array(
                    'userid' => $this->user_id,
                    'userpws' => $userpws,
                    'cardid' => $cardid,
                    'cardnum' => '1',
                    'sporder_id' => $this->request_transactioncode,
                    'sporder_time' => $sporder_time,
                    'md5_str' => strtoupper($this->generateSignature($this->user_id . $userpws . $cardid . '1' . $this->request_transactioncode . $sporder_time . $KeyStr)),
                    'version' => self::API_VERSION
                );
                break;
            default:
                $return_data = array(
                    'userid' => $this->user_id,
                    'userpws' => $userpws,
                    'version' => self::API_VERSION
                );
        }

        return $return_data;
    }

    public function preRequest($sku, $api_method = NULL, $total_try = 0) {
        $return_bool = FALSE;

        if ($total_try < 3) {
            if ($api_method == 'product_price_check') {
                $response = $this->curl_obj->curl_get($this->api_cardinfo_url, $this->generateRequestArray('product_price_check', $sku));
            } else if ($api_method == 'buy_product') {
                $request_str = $this->generateRequestArray('buy_product', $sku);
                $response = $this->curl_obj->curl_get($this->api_url, $request_str);
                /*
                $skuParts = explode('_', $sku);
                $tempCardId = isset($skuParts[1]) ? $skuParts[1] : '';
                $response = '<?xml version="1.0" encoding="gb2312"?>
                                        <orderinfo>
                                          <err_msg></err_msg>
                                        <retcode>1</retcode>
                                          <orderid>S0703280004</orderid>		
                                          <cardid>' . $tempCardId . '</cardid>					
                                          <ordercash>48.5</ordercash>						
                                          <cardname>欧飞1分钱支付体验卡</cardname>	
                                          <sporder_id>' . $this->request_transactioncode . '</sporder_id> 				
                                        <cards>
                                            <card>
                                          <cardno>cardno12345</cardno> 					
                                          <cardpws>545345253y2</cardpws>			
                                          <expiretime>2015-12-30</expiretime>	
                                             </card>
                                          </cards>
                                          </orderinfo>'; */
                $response = str_replace('gb2312', 'utf-8', $response);
            }

            $this->resetAPIData();
            $this->loadAPIData($sku, $response);
            $this->logAPIData($sku);

            if ($this->resp_status === true) {
                $return_bool = TRUE;
            } else {
                $this->reportError(array('curlError' => $this->curl_obj->get_error(), 'sku' => $sku, 'method' => $this->request_method, 'try_count' => $total_try, 'error_msg' => $this->resp_error), 'preRequest');
                
                # When Not Insufficient merchant store credit
                if ($this->resp_error_code != 1007) {
                    $return_bool = $this->preRequest($sku, $api_method, ++$total_try);
                }
            }
        }

        return $return_bool;
    }

    public function checkCode($sku, $qty, $trans_code) {
        $return_bool = FALSE;

        if (self::isSupportedSKU($sku)) {
            $this->setMethod($this->CHECK_METHOD);
            if ($trans_code = $this->logAPIData($sku, self::FLAG_INIT)) {
                $this->setQuantity($qty);
                $this->setTransCode($trans_code);
                $return_bool = $this->preRequest($sku, 'product_price_check');

                if ($return_bool) {
                    $margin = ($this->order_product_price - $this->resp_settle_amt) / $this->order_product_price;

                    if ($margin > self::MIN_MARGIN) {
                        $return_bool = TRUE;
                    } else {
                        $this->reportError(array(
                            'sku' => $sku,
                            'transcode' => $trans_code,
                            'resp_settle_amt' => $this->resp_settle_amt,
                            'order_product_store_price' => $this->order_product_price,
                            'error_msg' => 'Margin is lower than expected'
                                ), 'checkCode()');
                    }
                }
            }
        }

        return $return_bool;
    }

    protected function getCode($sku, $qty, $renew_transactioncode = true) {
        $return_bool = FALSE;
        
        if (self::isSupportedSKU($sku)) {
            $this->setMethod($this->GET_METHOD);
            $this->setQuantity($qty);
            
            if ($trans_code = $this->logAPIData($sku, self::FLAG_INIT)) {
                $this->setTransCode($trans_code);
                $return_bool = $this->preRequest($sku, 'buy_product');   
            }
        }

        return $return_bool;
    }

    public function processBatchRestock($product_id, $order_product_store_price, $sku, $qty, $extra_params = array()) {
        $this->order_product_price = $order_product_store_price;
        return parent::processBatchRestock($product_id, $order_product_store_price, $sku, $qty, $extra_params);
    }

    protected function loadAPIData($sku, $raw_response = NULL, $data_array = array()) {
        if (!is_null($raw_response)) {
            $data_array = $this->parseXML($raw_response, $data_array);
        }

        if (isset($data_array['retcode']) && !is_null($data_array['retcode']) && $data_array['retcode'] == 1) {
            //$this->resp_status = $data_array['retcode'];
            $this->resp_status = TRUE;
        } else {
            $this->resp_status = FALSE;
            $this->resp_error = $this->resp_error_code = $data_array['retcode'];
            
            if (isset($data_array['err_msg']) && !is_null($data_array['err_msg']) && !is_array($data_array['err_msg'])) {
                $this->resp_error .= ':' . $data_array['err_msg'] . ':' . $this->getErrorDesc($data_array['retcode']);
            }
        }

        if (isset($data_array['ret_leftcredit']) && !is_null($data_array['ret_leftcredit']) && isset($data_array['ret_leftcredit']) && $data_array['retcode'] == 1) {
            $this->request_ofcard_merchant_balance = $data_array['ret_leftcredit'];
        }

        if (isset($data_array['sporder_id']) && !is_null($data_array['sporder_id'])) {
            $this->resp_transactioncode = $data_array['sporder_id'];
        }

        if (isset($data_array['orderid']) && !is_null($data_array['orderid'])) {
            $this->resp_ordernumber = $data_array['orderid'];
        } else {
            $this->resp_ordernumber = '';
        }

        if (isset($data_array['cardid']) && !is_null($data_array['cardid'])) {
            $this->resp_ordernumber .= ":" . $data_array['cardid'];
        }

        if (isset($data_array['cards']['card']['cardno']) && !is_null($data_array['cards']['card']['cardno'])) {
            $this->resp_items = array();
            
            foreach ($data_array['cards'] as $idx => $item_array) {
                if (isset($item_array['cardno'])) {
                    $this->resp_items[] = array(
                        'log_id' => '',
                        'token' => sprintf(self::KEY_FORMAT, $item_array['cardno'], $item_array['cardpws'], $item_array['expiretime']),
                        // 'sku' => $sku,
                    );
                }
            }
        }

        if (isset($data_array['ordercash']) && !is_null($data_array['ordercash'])) {
            $this->currency_settle_amt = $data_array['ordercash'];
            $this->currency_rate = $this->getCurrencyObj()->get_value(self::OFCARD_CURRENCY, 'sell');
            $this->resp_settle_amt = $this->getCurrencyObj()->advance_currency_conversion($data_array['ordercash'], self::OFCARD_CURRENCY, DEFAULT_CURRENCY);
        } else if (isset($data_array['ret_cardinfos']['card']) && is_array($data_array['ret_cardinfos']['card'])) {
            foreach ($data_array['ret_cardinfos'] as $idx => $item_array) {
                if (isset($item_array['inprice'])) {
                    $this->resp_currency_code = self::OFCARD_CURRENCY;
                    $this->resp_currency_settle_amt = $item_array['inprice'];
                    // $this->resp_settle_amt = $this->getCurrencyObj()->advance_currency_conversion($item_array['inprice'], self::OFCARD_CURRENCY, DEFAULT_CURRENCY);
                    $this->ofcard_actual_amount = $item_array['pervalue'];
                    break;
                }
            }
        }
    }

    private function parseXML($raw_data, $return_array = array()) {
        if (is_string($raw_data)) {
            $xml = simplexml_load_string($raw_data);
            $raw_data = (array) $xml;
        }

        foreach ($raw_data as $key => $value) {
            $key = strtolower($key);

            if (is_object($value) || is_array($value)) {
                $return_array[$key] = $this->parseXML((array) $value);
            } else {
                $return_array[$key] = $value;
            }
        }

        return $return_array;
    }

    private function generateSignature($plaintext) {
        return md5($plaintext);
    }

    private function getErrorDesc($code) {
        $return_str = '';

        switch ($code) {
            case '1':
                $return_str = 'Success';
                break;
            case '5':
                $return_str = 'Error Command';
                break;
            case '6':
                $return_str = 'API Version incorrect';
                break;
            case '8':
                $return_str = 'Invalid Merchant';
                break;
            case '9':
                $return_str = 'Unknown Error';
                break;
            case '10':
                $return_str = 'Undefined';
                break;
            case '11':
                $return_str = 'Publisher service in maintenance , Not able to do top up';
                break;
            case '12':
                $return_str = 'Stock Insufficient';
                break;
            case '1001':
                $return_str = 'Publisher name validation error';
                break;
            case '1002':
                $return_str = 'Publisher IP validation error';
                break;
            case '1003':
                $return_str = 'MD5 validation error';
                break;
            case '1004':
                $return_str = 'Current product are not able to be use';
                break;
            case '1005':
                $return_str = 'Purchase product over system request';
                break;
            case '1007':
                $return_str = 'Insufficient merchant store credit';
                break;
            case '1008':
                $return_str = 'Parameter required is missing / lack';
                break;
            case '1009':
                $return_str = 'Invalid member account';
                break;
            case '1007':
                $return_str = 'Insufficient merchant store credit';
                break;
            case '1008':
                $return_str = 'Parameter required is missing / lack';
                break;
            case '9999':
                $return_str = 'Unknown Error(If returning result, kindly call api/query.do Api to check order status，confirm order status to avoid unneccesary lost)';
                break;
            case '105':
                $return_str = 'Fail Request(If returning result, kindly call api/query.do Api to check order status，confirm order status to avoid unneccesary lost)';
                break;
            case '302':
                $return_str = 'Your account temporary are not able to proceed trading.';
                break;
            case '305':
                $return_str = 'Unknown Topup Failed Error';
                break;
            case '319':
                $return_str = 'Incorrect Topup Phone number';
                break;
            case '320':
                $return_str = 'IC format not valid';
                break;
            case '321':
                $return_str = 'Temporary Not support current type phone number topup';
                break;
            case '331':
                $return_str = 'Order creation failed';
                break;
            case '334':
                $return_str = 'Order creation timeout(If returning result, kindly call api/query.do Api to check order status，confirm order status to avoid unneccesary lost)';
                break;
            case '1043':
                $return_str = 'Payment timeout，Order process fail(If returning result, kindly call api/query.do Api to check order status,confirm order status to avoid unneccesary lost)';
                break;
            case '2000':
                $return_str = 'Order number not exist';
                break;
            case '2001':
                $return_str = 'Order transaction already been process';
                break;
            case '2010':
                $return_str = 'Error occured, order process failed';
                break;
            case '9998':
                $return_str = 'Parameter format error';
                break;
            case '1017':
                $return_str = 'Card serial validation error';
                break;
            case '1018':
                $return_str = 'Petrol card topup fail, didn’t create order';
                break;
            case '1990':
                $return_str = 'Error Product card type';
                break;
            case '1992':
                $return_str = 'No corresponding product';
                break;
            case '1993':
                $return_str = 'No corresponding product';
                break;
            default:
                $return_str = 'Error description not found for error code: ' . $code;
        }

        return $return_str;
    }

}

include_once dirname(__FILE__) . DIRECTORY_SEPARATOR . 'config.inc.php';
?>