<?php

include_once dirname(__FILE__) . DIRECTORY_SEPARATOR . '../restock_api.php';
include_once(DIR_WS_CLASSES . 'cache_abstract.php');
include_once(DIR_WS_CLASSES . 'memcache.php');
include_once(DIR_WS_CLASSES . 'curl.php');
include_once(DIR_FS_ADMIN . 'includes/classes/slack_notification.php');

class openbucks_api extends restock_api
{
    const KEY_FORMAT = 'Card Number : %s<br>Pin : %s';
    public static $SKU_SUPPORT = array();
    public static $service_api_pattern = array(
        array('prefix_len' => 2, 'prefix_text' => 'OB')
    );

    public $products_code, $products_amount;

    private $secret_key, $public_key, $base_url, $products_price, $order_product_price, $server_ip;

    protected $curl_obj;

    public function __construct()
    {
        parent::__construct();
        $this->secret_key = OPENBUCKS_API_SECRET_KEY;
        $this->public_key = OPENBUCKS_API_PUBLIC_KEY;
        $this->base_url = OPENBUCKS_API_URL;
        $this->API_PROVIDER = 'OPENBUCKS';
        $this->GET_METHOD = 'card/issue';
        $this->min_margin = OPENBUCKS_MIN_MARGIN;
        $this->low_margin = OPENBUCKS_LOW_MARGIN;
        $this->server_ip = OPENBUCKS_OFFGAMERS_SERVER_IP;
        $this->curl_obj = new curl();
    }

    public static function isSupportedSKU($sku)
    {
        $return = false;
        $skuParts = explode('_', $sku);

        if (isset($skuParts[1])) {
            $pattern_array = self::$service_api_pattern;

            foreach ($pattern_array as $pattern) {
                if (substr($sku, 0, $pattern['prefix_len']) === $pattern['prefix_text']) {
                    $return = true;
                    break;
                }
            }
        }

        return $return;
    }

    protected function getAmount($sku)
    {
        return $this->products_price;
    }

    public function checkCode($sku, $qty, $op_id)
    {
        // No Margin Checking Required
        return true;
    }

    public function getCode($sku, $qty, $renew_transactioncode = true)
    {
        $return_bool = false;

        if (self::isSupportedSKU($sku)) {

            $this->setMethod($this->GET_METHOD);
            $this->setQuantity($qty);

            if ($trans_code = $this->logAPIData($sku, self::FLAG_INIT)) {
                $this->setTransCode($trans_code);
                $return_bool = $this->preRequest($sku, $this->GET_METHOD);
            }
        }

        return $return_bool;
    }

    public function processBatchRestock($product_id, $order_product_store_price, $sku, $qty, $extra_params = array())
    {
        $this->order_product_price = $order_product_store_price;
        return parent::processBatchRestock($product_id, $order_product_store_price, $sku, $qty, $extra_params);
    }

    private function generateTimeStamp()
    {
        // datetime format with ISO-8601 format
        return (new DateTime())->format(DateTime::ATOM);
    }

    private function getPublisherSKU($sku)
    {
        $sku = explode('_', $sku);
        return $sku[1];
    }

    public function preRequest($sku, $api_method = null, $total_try = 0)
    {
        $return_bool = false;

        if ($total_try < 3) {
            $this->resetAPIData();
            $publisher_sku = $this->getPublisherSKU($sku);
            if ($api_method == $this->GET_METHOD) {
                $url = $this->base_url . "/" . $this->GET_METHOD;
                $request_params = [
                    "public_key" => $this->public_key,
                    "tracking_id" => $this->request_transactioncode,
                    "requested_on" => $this->generateTimeStamp(),
                    "buyer_id" => "none",
                    "buyer_ip" => $this->server_ip,
                    "card_api_id" => "openbuckscard",
                    "denom" => $publisher_sku,
                    "currency" => "USD",
                    "delivery" => "No"
                ];

                $request_params['signature'] = $this->generateSignature($request_params, $this->secret_key);

                $body = json_encode($request_params);
            }

            $headers = array(
                'Content-Type: application/json'
            );

            $response = $this->curl_obj->curl_request('POST', $url, $headers, $body, false);
            $this->loadAPIData($response);
            $this->logAPIData($sku);

            if ($this->resp_status == true) {
                $return_bool = true;
            } else {
                $return_bool = $this->preRequest($sku, $api_method, ++$total_try);
                $this->reportError(array(
                    'curlError' => $this->curl_obj->get_error(),
                    'sku' => $sku,
                    'method' => $api_method,
                    'try_count' => $total_try,
                    'url' => $url,
                    'error_msg' => $this->resp_error,
                    'response' => $response,
                ), 'preRequest');
            }
        }

        return $return_bool;
    }

    protected function loadAPIData($raw_response = null, $data_array = array())
    {
        $this->resp_status = false;

        $data_array = json_decode($raw_response, true);
        if ($this->request_method == $this->GET_METHOD && json_last_error() == 0 && (empty($data_array['error_message']) || strtoupper($data_array['error_message']) == 'OK')) {
            if($this->validateResponseSignature($data_array)){
                $this->products_price = $data_array['denom'];
                $this->resp_currency_code = $data_array['currency'];
                $this->resp_currency_settle_amt = $data_array['net_cost'];
                $this->resp_items[] = array(
                    'log_id' => '',
                    'token' => sprintf(self::KEY_FORMAT, $data_array['card_number'], $data_array['pin']),
                );

                //OpenBucks does not return transaction code
                $this->resp_transactioncode = $this->request_transactioncode;
                $this->resp_ordernumber = $data_array['transaction_id'];
                $this->resp_status = true;
            }
        }

        if(!$this->resp_status && empty($this->resp_error)){
            if (json_last_error() > 0) {
                $this->resp_error = 'Error Decode Json Content';
            } elseif (!empty($data_array['error_message'])) {
                $this->resp_error = $data_array['error_message'];
            }
            else{
                $this->resp_error = json_encode($raw_response);
            }
        }
    }

    private function validateResponseSignature($params){
        $signature = (!empty($params['signature']) ? $params['signature'] : '');
        unset($params['signature']);
        unset($params['completed_on']);
        return $signature == $this->generateSignature($params,$this->secret_key);
    }

    private function generateSignature($request_params, $secret_key)
    {
        ksort($request_params);
        $data = implode(',', array_map(function ($v, $k) {
                return $k . '=' . $v;
            }, $request_params, array_keys($request_params))) . "," . $secret_key;
        return hash('sha256', $data);
    }


}

include_once dirname(__FILE__) . DIRECTORY_SEPARATOR . 'config.inc.php';
