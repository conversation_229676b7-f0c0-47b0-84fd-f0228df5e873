<?php

include_once dirname(__FILE__) . DIRECTORY_SEPARATOR . '../restock_api.php';

class pisb_api extends restock_api {
    
    public static $service_api_pattern = array(
//        array('prefix_len' => 4, 'prefix_text' => 'PISB')
    );
    
    public $pisb_actual_amount, $respondTransactionId;
    
    private $api_key,
            $api_merchantcode,
            $api_sku;

    public function __construct() {
        parent::__construct();

        $this->api_merchantcode = PISB_API_MERCHANTCODE;
        $this->api_key = PISB_API_KEY;
        $this->api_url = PISB_API_URL;

        $this->API_PROVIDER = 'PISB';
        $this->GET_METHOD = 'get_pins';
        $this->LANG = 'en';
    }

    public static function isSupportedSKU($sku) {
        $return = false;
        $skuParts = explode('_', $sku);
        #pattern PISB_1_4
        if (isset($skuParts[1])) {
            $pattern_array = self::$service_api_pattern;
            
            foreach ($pattern_array as $pattern) {
                if (substr($sku, 0, $pattern['prefix_len']) === $pattern['prefix_text']) {
                    $return = true;
                    break;
                }
            }
        }

        return $return;
    }

    protected function getAmount($sku) {
        return $this->pisb_actual_amount;
    }
    
    protected function getGroup($sku) {
        $skuParts = explode('_', $sku);
        
        if (isset($skuParts[1])) {
            return preg_replace("/[^0-9]/","", $skuParts[1]);
        }
        
        return 0;
    }
    
    protected function getVoucherID($sku) {
        $skuParts = explode('_', $sku);
        
        if (isset($skuParts[2])) {
            return preg_replace("/[^0-9]/","", $skuParts[2]);
        }
        
        return 0;
    }


    /*
     * PISB API does not required to pre-check for code
     */

    protected function checkCode($sku, $qty, $trans_code) {
        return true;
    }

    protected function getCode($sku, $qty, $renew_transactioncode = true) {
        $return_bool = FALSE;
        $qty = 1;
        
        if (self::isSupportedSKU($sku)) {
            $this->setMethod($this->GET_METHOD);
            $this->setQuantity($qty);
            
            if ($trans_code = $this->logAPIData($sku, self::FLAG_INIT)) {
                $this->setTransCode($trans_code);
                $return_bool = $this->preRequest($sku, 'get_pins'); 
            }
        }

        return $return_bool;
    }
    
    public function processBatchRestock($product_id, $order_product_store_price, $sku, $qty, $extra_params = array()) {
        $this->order_product_price = $order_product_store_price;
        return parent::processBatchRestock($product_id, $order_product_store_price, $sku, $qty, $extra_params);
    }

    private function generateRequestArray($process, $sku) {
        $pisbTransactionId = 'PISB' . $this->request_transactioncode;
        $pisbquantity = 1;
        $getGroup = $this->getGroup($sku);
        $getVoucherID = $this->getVoucherID($sku);
        
        $return_data = array(
                'merchant_code' => PISB_API_MERCHANTCODE,
                'method' => 'get_pins',
                'request_id' => trim($pisbTransactionId),
                'gp_id' =>  trim($getGroup), 
                'voucher_id' => trim($getVoucherID),
                'quantity' => trim($pisbquantity),
                'language' => 'en',
                'hash' => $this->generate_hash($pisbTransactionId, $getGroup, $getVoucherID, $pisbquantity)
            );
            
        return json_encode($return_data, true);
    }

    public function preRequest($sku, $api_method = NULL, $total_try = 0) {
        $return_bool = FALSE;

        if ($total_try < 3) {

            $this->resetAPIData();
            
            if ($api_method == 'get_pins') {
                $url = PISB_API_URL;
                $ejson_data = $this->generateRequestArray($this->GET_METHOD, $sku);

                $ch = curl_init($url);
                curl_setopt($ch, CURLOPT_POST, true);
                curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: text/json')); 
                curl_setopt($ch, CURLOPT_POSTFIELDS, $ejson_data);
                curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); 
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true); 
                curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 30);
                $response = curl_exec($ch);
            }
            
            $this->loadAPIData($sku, $response);
            $this->logAPIData($sku);

            if ($this->resp_status == TRUE) {
                $return_bool = TRUE;
            } else {
                $return_bool = $this->preRequest($sku, $api_method, ++$total_try);
                $this->reportError(array('curlError' => $this->curl_obj->get_error(), 'sku' => $sku, 'method' => $this->request_method, 'try_count' => $total_try,'data' => $this->generateRequestArray($this->GET_METHOD, $sku), 'respond' => $response, 'error_msg' => $this->resp_error), 'preRequest');
            }
        }

        return $return_bool;
    }
    
    protected function loadAPIData($sku, $raw_response = NULL, $data_array = array()) {
        if (!is_null($raw_response)) {
            $data_array = json_decode($raw_response, true);
        }
        
        if (isset($data_array['status']) && !is_null($data_array['status']) && $data_array['status'] == 0) {
            if (isset($data_array['pins'])) {
                $softpin = 'Activation Code : '. $data_array['pins'][0]['PIN_1'];
                if(!empty($data_array['pins'][0]['PIN_3'])){
                    $softpin .= '<br>Genshin Impact items Code : '. $data_array['pins'][0]['PIN_3'];
                }
                $this->resp_items = array();
                $this->resp_items[] = array(
                    'log_id' => '',
                    'token' => $softpin,
                );
            }

            if (isset($data_array['request_id']) && !is_null($data_array['request_id'])) {
                $respondTransactionId = str_replace('PISB','',$data_array['request_id']);
                $this->resp_transactioncode = $respondTransactionId;
            }

            if (isset($data_array['voucher']) && !is_null($data_array['voucher'])) {
                $this->resp_currency_code = $data_array['total_currency'];
                $this->resp_currency_settle_amt = $data_array['total'];
                $this->pisb_actual_amount = $data_array['voucher_value'];
            }
        
            $this->resp_status = TRUE;
        } else {
            $this->resp_status = FALSE;
            $this->resp_error = $data_array['message'];

            if (isset($data_array['message']) && !empty($data_array['message'])) {
                $this->resp_error .= ':' . $data_array['status'] . ':' . $data_array['message'] . ':' . $data_array['request_id'] . ':' .$data_array['voucher'] ;
            }
        }
    }
    
    private function safe_b64encode($string) {
        $data = base64_encode($string);
        $data = str_replace(array('+','/','='),array('-','_',''),$data);
        return $data;
    }
    
    private function safe_b64decode($string) {
        $data = $string;
        $data = str_replace(array('-','_'),array('+','/'),$string); 
        $mod4 = strlen($data) % 4;
        if ($mod4) {
               $data .= substr('====', $mod4);
        }
        return base64_decode($data);

    }
    
    private function generate_hash($pisbTransactionId, $getGroup, $getVoucherID, $pisbquantity) {
        return md5(trim('@@'. PISB_API_KEY .''. PISB_API_MERCHANTCODE .'get_pins'. $pisbTransactionId .''. $getGroup .''. $getVoucherID .''. $pisbquantity .'en@@'));
    }

    private function encrypt($value, $key)
    {
        if (!$value) { return false; }
        
        $block = mcrypt_get_block_size(MCRYPT_RIJNDAEL_128, MCRYPT_MODE_CBC); 
        $padding = $block - (strlen($value) % $block);
        $value .= str_repeat(chr($padding), $padding);
        
        $iv = mcrypt_create_iv(mcrypt_get_iv_size(MCRYPT_RIJNDAEL_128, MCRYPT_MODE_CBC), MCRYPT_DEV_URANDOM);
        $ciphertext = mcrypt_encrypt(MCRYPT_RIJNDAEL_128, $key, $value, MCRYPT_MODE_CBC, $iv);
        
        # prepend the IV in hex for it to be available for decryption 
        $ciphertext = bin2hex($iv) . $this->safe_b64encode($ciphertext);
        return trim($this->safe_b64encode($ciphertext));
    }


    private function decrypt($value, $key)
    {
        if (!$value) { return false; }
        
        $ciphertext_dec = $this->safe_b64decode($value);
        $plain_iv = substr($ciphertext_dec, 0, 32); 
        if (!ctype_xdigit($plain_iv) || strlen($plain_iv) != 32) return; 
        # get the 1st 32 chars of the value and convert to 16bytes binary 
        $iv_dec = pack("H*", $plain_iv);
        
        # get the string starting from position 32
        $ciphertext_dec = $this->safe_b64decode(substr($ciphertext_dec, 32));
        $text = mcrypt_decrypt(MCRYPT_RIJNDAEL_128, $key, $ciphertext_dec, MCRYPT_MODE_CBC, $iv_dec);

        return trim(preg_replace('/[\x00-\x1f]/', '', $text));

    }
    
    protected function reportError($response_data, $ext_subject = '') {
        include_once(DIR_WS_CLASSES . 'slack_notification.php');
        $slack = new slack_notification();
        $data = json_encode(array(
            'text' => '[OG Crew] ' . $this->API_PROVIDER . ' API Error - ' . date("F j, Y H:i") . ' from ' . (isset($_SERVER['SERVER_NAME']) && !empty($_SERVER['SERVER_NAME']) ? $_SERVER['SERVER_NAME'] : '-'),
            'attachments' => array(
                array(
                    'color' => 'warning',
                    'text' => $ext_subject . "\n\n" . json_encode($response_data)
                )
            )
        ));
        $slack->send(SLACK_WEBHOOK_DEV_DEBUG, $data);
    }
    
}

include_once dirname(__FILE__) . DIRECTORY_SEPARATOR . 'config.inc.php';
?>