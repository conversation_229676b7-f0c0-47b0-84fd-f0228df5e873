<?php

class restock_api
{

    const FLAG_INIT = 'I';
    const FLAG_NEW = 'N';
    const FLAG_UPLOADING = 'U';
    const FLAG_UPLOADED = 'S';
    const FLAG_ERROR = 'E';
    const DEFAULT_ADMIN_EMAIL = 'system';

    public $API_PROVIDER = '-',
        $CHECK_METHOD = 'check',
        $GET_METHOD = 'get',
        $SUCCESS_STATUSFLAG = true;

    /*
     * SKU pattern && SKU support has to be define in each API class
     */
    public $api_url,
        $request_method,
        $request_transactioncode,
        $request_quantity,
        $resp_transactioncode,
        $resp_items,
        $resp_status,
        $resp_error,
        $resp_ordernumber,
        $resp_currency_code = '',
        $resp_currency_settle_amt = 0,
        $currency_code = 'USD',
        $currency_rate = '1',
        $currency_settle_amt = null,
        $product_id = 0,
        $orders_products_id = 0,
        $orders_id, $min_margin, $low_margin;

    protected $curl_obj,
        $cpc_obj,
        $currency_obj;

    public function __construct()
    {
        include_once(DIR_WS_CLASSES . 'curl.php');
        include_once(DIR_WS_CLASSES . 'custom_product_code.php');
        include_once(DIR_WS_LANGUAGES . 'english.php');

        $this->curl_obj = new curl();
        $this->cpc_obj = new custom_product_code();
    }

    /*
     * isSupportedSKU has to define for each API
     */
    //    protected static function isSupportedSKU($sku) {
    //        return false;
    //    }

    /*
     * getAmount has to define for each API
     */
    protected function getAmount($sku)
    {
        $return_amt = 0;

        return $return_amt;
    }

    /*
     * getSettleAmount has to define for each API
     */
    protected function getSettleAmount($sku)
    {
        $currency_code = '';
        $currency_rate = '';
        $currency_settle_amount = 0;
        $settle_amount = 0;

        if (!empty($this->resp_currency_code) && !empty($this->resp_currency_settle_amt)) {
            $currency_code = $this->resp_currency_code;
            $currency_settle_amount = $this->resp_currency_settle_amt;

            if ($this->getCurrencyObj()->is_set($currency_code)) {
                $currency_rate = $this->getCurrencyObj()->get_value($currency_code, 'sell');
                $settle_amount = $this->getCurrencyObj()->advance_currency_conversion($currency_settle_amount,
                    $currency_code, DEFAULT_CURRENCY);
            }
        } else {
            $select_sql = " SELECT products_cost, products_currency
                            FROM " . TABLE_PRODUCTS_COST . "
                            WHERE products_id = '" . $this->product_id . "'";
            $result_sql = tep_db_query($select_sql);
            if ($row = tep_db_fetch_array($result_sql)) {
                $currency_code = $row['products_currency'];
                $currency_settle_amount = $row['products_cost'];

                $currency_rate = $this->getCurrencyObj()->get_value($currency_code, 'sell');
                $settle_amount = $this->getCurrencyObj()->advance_currency_conversion($currency_settle_amount,
                    $currency_code, DEFAULT_CURRENCY);
            }
        }

        return array(
            'currency_code' => $currency_code,
            'currency_rate' => $currency_rate,
            'currency_settle_amount' => $currency_settle_amount,
            'settle_amount' => $settle_amount,
        );
    }

    protected function getCurrencyObj()
    {
        if (class_exists('currencies') != true) {
            include_once dirname(__FILE__) . DIRECTORY_SEPARATOR . '../../classes/currencies.php';
        }

        if (!$this->currency_obj) {
            $this->currency_obj = new currencies();
        }

        return $this->currency_obj;
    }

    protected function checkCode($sku, $qty, $trans_code)
    {
        $this->setMethod($this->CHECK_METHOD);
        $this->setQuantity($qty);
        $this->setTransCode($trans_code);

        return $this->preRequest($sku);
    }

    protected function failedReattempt($sku, $curl_error_array, $total_attempt)
    {
        $return_bool = false;

        $this->reportError(array(
            'curlError' => $curl_error_array,
            'apiError' => $this->resp_error,
            'sku' => $sku,
            'method' => $this->request_method,
            'try_count' => $total_attempt
        ), 'preRequest');

        if ($total_attempt < 2) {
            $return_bool = true;
        }

        return $return_bool;
    }

    /*
     * getCode has to define for each API
     */
    protected function getCode($sku, $qty, $renew_transactioncode = true)
    {
        $this->setMethod($this->GET_METHOD);
        $this->setQuantity($qty);

        if ($renew_transactioncode) {
            if ($trans_code = $this->logAPIData($sku, self::FLAG_INIT)) {
                $this->setTransCode($trans_code);
            }
        }

        return $this->preRequest($sku);
    }

    protected function preRequest($sku, $api_method = null, $total_try = 0)
    {
        $return_bool = false;

        $this->resetAPIData();
        $this->loadAPIData($this->curl_obj->curl_post($this->api_url, $this->generateRequestString($sku)));

        if ($this->logAPIData($sku)) {
            $return_bool = true;
        } else {
            if ($this->failedReattempt($sku, $this->curl_obj->get_error(), $total_try)) {
                $return_bool = $this->preRequest($sku, $api_method, ++$total_try);
            }
        }

        return $return_bool;
    }

    protected function resetAPIData($default = '')
    {
        foreach (get_class_vars(get_class($this)) as $name => $value) {
            if (substr($name, 0, 5) == 'resp_') {
                $this->$name = $default;
            }
        }
    }

    protected function setTransCode($trans_code)
    {
        $this->request_transactioncode = $trans_code;
    }

    protected function setMethod($method)
    {
        if (!is_null($method)) {
            $this->request_method = $method;
        }
    }

    protected function setQuantity($qty)
    {
        $this->request_quantity = (int)$qty;
    }

    protected function processBatchRestock($product_id, $order_product_store_price, $sku, $qty, $extra_params = array())
    {
        $custom_products_code_array = array();
        $code_status = isset($extra_params['code_status']) ? $extra_params['code_status'] : '1';
        $orders_products_id = isset($extra_params['orders_products_id']) ? $extra_params['orders_products_id'] : '';
        $this->product_id = $product_id;
        $this->orders_products_id = $orders_products_id;

        try {
            for ($count = 0; $count < $qty; $count++) {
                // 19/05/2019 Glendon : reset request_transaction code to prevent overwritting of previous api record on multiple quantity case
                $this->request_transactioncode = null;
                if ($this->checkCode($sku, 1, $orders_products_id) !== false) {
                    if ($this->getCode($sku, 1) !== false) {
                        foreach ($this->resp_items as $code_array) {
                            if (empty($code_array['file_type'])) {
                                $code_array['file_type'] = 'soft';
                            }
                            $custom_products_code_id = $this->uploadCode($code_array['log_id'], $code_array['token'],
                                $product_id, $code_status, $code_array['file_type']);

                            // create cd key file
                            if ($custom_products_code_id !== false) {
                                $custom_products_code_array[] = $custom_products_code_id;
                            }
                        }
                    } //Prevent Multiple Notification for same orders products
                    else {
                        $this->failedDeliveryNotification($sku);
                        break;
                    }
                } //Prevent Multiple Notification for same orders products
                else {
                    $this->resp_error = 'Fail to check margin/SKU not available.';
                    $this->failedDeliveryNotification($sku);
                    break;
                }
            }
        } catch (Exception $e) {
            $this->reportError(array(
                'product_id' => $product_id,
                'sku' => $sku,
                'qty' => $qty,
                'e' => $e->getMessage()
            ), 'processBatchRestock()');
        }

        $this->addProductAvailableQty($product_id, $custom_products_code_array);

        return $custom_products_code_array !== array();
    }

    private function addProductAvailableQty($product_id, $cdkey_id_arr)
    {
        global $log_object;

        if (is_object($log_object)) {
            $adj_qty = count($cdkey_id_arr);

            if ($adj_qty) {
                $products_quantity_select_sql = "	SELECT products_quantity, products_actual_quantity 
                                                    FROM " . TABLE_PRODUCTS . " 
                                                    WHERE products_id=" . $product_id;
                $products_quantity_result_sql = tep_db_query($products_quantity_select_sql);
                if ($products_quantity_row = tep_db_fetch_array($products_quantity_result_sql)) {
                    $old_prod_available_qty = $products_quantity_row['products_quantity'];
                    $old_prod_actual_qty = $products_quantity_row['products_actual_quantity'];
                    $new_prod_available_qty = $old_prod_available_qty + $adj_qty;
                    $new_prod_actual_qty = $old_prod_actual_qty + $adj_qty;

                    $product_sql = "UPDATE " . TABLE_PRODUCTS . " 
                                        SET products_quantity = IF(products_quantity IS NULL, 0, products_quantity) + " . $adj_qty . ", 
                                            products_actual_quantity = IF(products_actual_quantity IS NULL, 0, products_actual_quantity) + " . $adj_qty . " 
                                    WHERE products_id = " . $product_id;
                    if (tep_db_query($product_sql)) {
                        $log_object->insert_log($product_id, 'products_quantity',
                            $old_prod_available_qty . ':~:' . $old_prod_actual_qty,
                            $new_prod_available_qty . ':~:' . $new_prod_actual_qty, sprintf(LOG_QTY_ADJUST, ''),
                            sprintf(LOG_CDKEY_ID_STR, implode(',', $cdkey_id_arr)));
                    }

                    return true;
                }
            }
        } else {
            $this->reportError('Error: Missing object log_object', 'addProductAvailableQty()');
        }

        return false;
    }

    /*
     * status : 0 - sold
     * status : 1 - available
     * status : -2 - on hold
     */
    private function uploadCode($log_id, $token, $product_id, $status = '0', $file_type = 'soft')
    {
        $return_bool = false;
        $timestamp = date('Y-m-d H:i:s');

        if ($log_id) {
            $insert_array = array(
                'products_id' => $product_id,
                'status_id' => $status,
                'file_name' => $log_id,
                'file_type' => $file_type,
                'code_date_added' => $timestamp,
                'code_date_modified' => $timestamp,
                'code_uploaded_by' => self::DEFAULT_ADMIN_EMAIL,
                'remarks' => $this->API_PROVIDER . '_API'
            );

            if (tep_db_perform(TABLE_CUSTOM_PRODUCTS_CODE, $insert_array)) {
                $API_flag = self::FLAG_UPLOADING;
                $custom_products_code_id = tep_db_insert_id();
                $key_string = tep_encrypt_data(base64_encode($token));

                if ($this->cpc_obj->uploadCode($key_string, $custom_products_code_id, $product_id,
                        $timestamp) === true) {
                    $API_flag = self::FLAG_UPLOADED;
                    $return_bool = $custom_products_code_id;
                }

                $this->logAPIData('', $API_flag, $log_id, $return_bool);
            }
        }

        return $return_bool;
    }

    protected function logAPIData($sku, $flag_state = '', $log_id = 0, $custom_products_code_id = '')
    {
        $return_init = 0;
        $flag_state = $flag_state != '' ? $flag_state : ($this->resp_status != $this->SUCCESS_STATUSFLAG ? self::FLAG_ERROR : self::FLAG_NEW);

        switch ($flag_state) {
            case self::FLAG_NEW:
                if (is_array($this->resp_items) && count($this->resp_items) > 0) {
                    if (count($this->resp_items) == 1) {
                        $settle_amount_info = $this->getSettleAmount($sku);

                        $query_array = array(
                            'method' => $this->request_method,
                            'sku' => tep_db_prepare_input($sku),
                            'currency_code' => $settle_amount_info['currency_code'],
                            'currency_rate' => $settle_amount_info['currency_rate'],
                            'currency_settle_amount' => $settle_amount_info['currency_settle_amount'],
                            //($this->currency_settle_amt != '') ? $this->currency_settle_amt : $settle_amount,
                            'amount' => $this->getAmount($sku),
                            'settle_amount' => $settle_amount_info['settle_amount'],
                            'token' => tep_db_prepare_input($this->resp_items[0]['token']),
                            'ack' => tep_db_prepare_input($this->resp_status),
                            'serialnumber' => tep_db_prepare_input($this->resp_ordernumber),
                            'flag_state' => $flag_state
                        );

                        if ($this->request_transactioncode == $this->resp_transactioncode) {
                            if (tep_db_perform(TABLE_LOG_API_RESTOCK, $query_array, 'update',
                                " id = '" . $this->request_transactioncode . "'")) {
                                $this->resp_items[0]['log_id'] = $this->request_transactioncode;

                                $return_init = $this->request_transactioncode;
                            } else {
                                $log_query_array = array(
                                    'transaction_id' => $this->request_transactioncode,
                                    'log_method' => $this->request_method,
                                    'log_sku' => tep_db_prepare_input($sku),
                                    'log_ack' => tep_db_prepare_input($this->resp_status),
                                    'log_flag_state' => $flag_state,
                                    'log_api_provider' => $this->API_PROVIDER,
                                    'log_msg' => 'Failed to update logAPIData()',
                                    'log_created_datetime' => 'now()'
                                );
                                $this->resp_items[0]['log_id'] = false;
                                $this->reportError($query_array, 'Failed to update logAPIData()');
                                tep_db_perform(TABLE_LOG_API_RESTOCK_TRANSACTION, $log_query_array);
                            }
                        } else {
                            $log_query_array = array(
                                'transaction_id' => $this->request_transactioncode,
                                'log_method' => $this->request_method,
                                'log_sku' => tep_db_prepare_input($sku),
                                'log_ack' => tep_db_prepare_input($this->resp_status),
                                'log_flag_state' => $flag_state,
                                'log_api_provider' => $this->API_PROVIDER,
                                'log_msg' => 'Failed to update logAPIData() due to request_transactioncode !== resp_transactioncode ' . $this->request_transactioncode . ' !== ' . $this->resp_transactioncode,
                                'log_created_datetime' => 'now()'
                            );
                            $this->resp_items[0]['log_id'] = false;
                            $this->reportError($query_array,
                                'Failed to update logAPIData() due to request_transactioncode !== resp_transactioncode ' . $this->request_transactioncode . ' !== ' . $this->resp_transactioncode);
                            tep_db_perform(TABLE_LOG_API_RESTOCK_TRANSACTION, $log_query_array);
                        }
                    } else {
                        # this is not in used yet
                        $settle_amount_info = $this->getSettleAmount($sku);

                        $query_array = array(
                            'method' => $this->request_method,
                            'sku' => tep_db_prepare_input($sku),
                            'currency_code' => $settle_amount_info['currency_code'],
                            'currency_rate' => $settle_amount_info['currency_rate'],
                            'currency_settle_amount' => $settle_amount_info['currency_settle_amount'],
                            'amount' => $this->getAmount($sku),
                            'settle_amount' => $settle_amount_info['settle_amount'],
                            'ack' => tep_db_prepare_input($this->resp_status),
                            'serialnumber' => tep_db_prepare_input($this->resp_ordernumber),
                            'flag_state' => $flag_state,
                            'api_provider' => $this->API_PROVIDER,
                            'created_datetime' => 'now()'
                        );

                        foreach ($this->resp_items as $idx => $item_array) {
                            $query_array['token'] = tep_db_prepare_input($item_array['token']);

                            if (tep_db_perform(TABLE_LOG_API_RESTOCK, $query_array)) {
                                $this->resp_items[$idx]['log_id'] = tep_db_insert_id();
                            } else {
                                $this->resp_items[$idx]['log_id'] = false;
                                $this->reportError($query_array, 'Failed to insert logAPIData()');
                            }
                        }

                        $return_init = 1;
                    }
                } else {
                    // log status only
                    if($this->request_transactioncode){
                        $settle_amount_info = $this->getSettleAmount($sku);

                        $query_array = array(
                            'method' => $this->request_method,
                            'sku' => tep_db_prepare_input($sku),
                            'currency_code' => $settle_amount_info['currency_code'],
                            'currency_rate' => $settle_amount_info['currency_rate'],
                            'currency_settle_amount' => $settle_amount_info['currency_settle_amount'],
                            'amount' => $this->getAmount($sku),
                            'settle_amount' => $settle_amount_info['settle_amount'],
                            'ack' => tep_db_prepare_input($this->resp_status),
                            'serialnumber' => tep_db_prepare_input($this->resp_ordernumber),
                            'flag_state' => $flag_state,
                            'api_provider' => $this->API_PROVIDER,
                            'created_datetime' => 'now()'
                        );

                        tep_db_perform(TABLE_LOG_API_RESTOCK, $query_array, 'update',
                            " id = '" . $this->request_transactioncode . "'");
                    }

                    $return_init = 1;
                }
                break;
            case self::FLAG_INIT:
                $query_array = array(
                    'method' => $this->request_method,
                    'sku' => tep_db_prepare_input($sku),
                    'currency_code' => '',
                    'currency_rate' => '',
                    'flag_state' => $flag_state,
                    'api_provider' => $this->API_PROVIDER,
                    'created_datetime' => 'now()'
                );

                if (tep_db_perform(TABLE_LOG_API_RESTOCK, $query_array)) {
                    $return_init = tep_db_insert_id();
                }
                break;
            case self::FLAG_UPLOADING:
                $sql_data_array = array(
                    'flag_state' => $flag_state
                );
                tep_db_perform(TABLE_LOG_API_RESTOCK, $sql_data_array, 'update', " id = '" . $log_id . "'");
                break;
            case self::FLAG_UPLOADED:

                $publisher_id = $this->getPublisherId($log_id);

                $sql_data_array = array(
                    'flag_state' => $flag_state,
                    'custom_products_code_id' => $custom_products_code_id,
                    'publishers_id' => $publisher_id,
                    'token' => ''
                );
                tep_db_perform(TABLE_LOG_API_RESTOCK, $sql_data_array, 'update', " id = '" . $log_id . "'");
                break;
            default:
                $query_array = array(
                    'method' => $this->request_method,
                    'sku' => tep_db_prepare_input($sku),
                    'currency_code' => '',
                    'currency_rate' => '',
                    'ack' => tep_db_prepare_input($this->resp_status),
                    'serialnumber' => tep_db_prepare_input($this->resp_ordernumber),
                    'flag_state' => $flag_state,
                    'api_provider' => $this->API_PROVIDER,
                    'error_msg' => tep_db_prepare_input($this->resp_error),
                    'created_datetime' => 'now()'
                );

                if (!tep_db_perform(TABLE_LOG_API_RESTOCK, $query_array)) {
                    $this->reportError($query_array, 'Failed to insert error in logAPIData()');
                }

                break;
        }

        return $return_init;
    }

    /*
     * getPublisherId has to define for each API
     */
    protected function getPublisherId($log_id)
    {
        $select_sql = " SELECT api_provider, method
                        FROM " . TABLE_LOG_API_RESTOCK . "
                        WHERE id = '" . $log_id . "'";
        $result_sql = tep_db_query($select_sql);
        if ($row_sql = tep_db_fetch_array($result_sql)) {
            $publisher_sql = "SELECT publishers_replenish_id
                            FROM " . TABLE_PUBLISHERS_REPLENISH . "
                            WHERE publishers_api_provider = '" . $row_sql['api_provider'] . "'
                                AND publishers_api_method = '" . $row_sql['method'] . "'
                            LIMIT 1";
            $publisher_result = tep_db_query($publisher_sql);
            if ($publisher_row = tep_db_fetch_array($publisher_result)) {
                $publisher_id = $publisher_row['publishers_replenish_id'];
            }
        }

        return $publisher_id;
    }

    protected function getOrdersIdByOrdersProductsId($opId)
    {
        $orders_id = 0;
        $op_sql = "SELECT orders_id
                            FROM " . TABLE_ORDERS_PRODUCTS . "
                            WHERE orders_products_id = '" . $opId . "'
                            LIMIT 1";
        $op_results = tep_db_query($op_sql);
        if ($op_row = tep_db_fetch_array($op_results)) {
            $orders_id = $op_row['orders_id'];
        }

        return $orders_id;
    }

    protected function getOrdersCurrencyRate($oid)
    {
        $order_sql = 'SELECT currency, currency_value
                            FROM ' . TABLE_ORDERS . "
                            WHERE orders_id = '" . $oid . "'
                            LIMIT 1";
        $o_results = tep_db_query($order_sql);
        if ($o_row = tep_db_fetch_array($o_results)) {
            return ['currency' => $o_row['currency'], 'currency_value' => $o_row['currency_value']];
        }

        return null;
    }

    protected function getOrdersProductsInfoByOrdersProductsId($opId)
    {
        $products_name = '';
        $orders_id = 0;
        $op_sql = "SELECT products_name,orders_id
                            FROM " . TABLE_ORDERS_PRODUCTS . "
                            WHERE orders_products_id = '" . $opId . "'
                            LIMIT 1";
        $op_results = tep_db_query($op_sql);
        if ($op_row = tep_db_fetch_array($op_results)) {
            return $op_row;
        }

        return false;
    }

    protected function reportError($response_data, $ext_subject = '')
    {
        include_once(DIR_WS_CLASSES . 'slack_notification.php');
        $slack = new slack_notification();
        $data = json_encode(array(
            'text' => '[OG Crew] ' . $this->API_PROVIDER . ' API Error - ' . date("F j, Y H:i") . ' from ' . (isset($_SERVER['SERVER_NAME']) && !empty($_SERVER['SERVER_NAME']) ? $_SERVER['SERVER_NAME'] : '-'),
            'attachments' => array(
                array(
                    'color' => 'warning',
                    'text' => $ext_subject . "\n\n" . json_encode($response_data)
                )
            )
        ));
        $slack->send(SLACK_WEBHOOK_DEV_DEBUG, $data);
    }

    public function failedDeliveryNotification($sku)
    {
        $slack = new slack_notification();
        $products_info = $this->getOrdersProductsInfoByOrdersProductsId($this->orders_products_id);

        if ($products_info) {
            $orders_id = $products_info['orders_id'];
            $products_title = $products_info['products_name'];
            $error = $this->resp_error;
            if (empty($error)) {
                $curl_error = $this->curl_obj->get_error();
                $error = ((isset($curl_error['error_message']) && !empty($curl_error['error_message'])) ? $curl_error['error_message'] . ' (' . $curl_error['error_code'] . ')' : 'Empty Response from publishers api');
            }
            $data = json_encode(array(
                'text' => '*Failed Delivery #' . $orders_id . ' delivery on ' . $this->API_PROVIDER . ' Replenish API*',
                'attachments' => array(
                    array(
                        'color' => 'warning',
                        'text' => "Orders ID : <" . HTTPS_SERVER . "orders.php?oID=" . $orders_id . "&action=edit|" . $orders_id . ">\n Product Name : " . $products_title . " \n SKU : " . $sku . " \n Error : " . $error . " \n `Action` : Reattempt delivery or contact publishers with error message"
                    )
                )
            ));

            $slack->send(SLACK_WEBHOOK_RESTOCK_FAIL_URL, $data);
        }

    }

    public function lowMarginReport($sku, $cost_cur, $cost_amt, $cost_mst, $price, $margin, $type)
    {
        $slack = new slack_notification();
        if (strtoupper($cost_cur) !== DEFAULT_CURRENCY) {
            $cost_str = $cost_cur . ' ' . number_format($cost_amt,
                    2) . '(~' . DEFAULT_CURRENCY . ' ' . number_format($cost_mst, 2) . ')';
        } else {
            $cost_str = $cost_cur . ' ' . number_format($cost_amt, 2);
        }
        $orders_id = $this->getOrdersIdByOrdersProductsId($this->orders_products_id);
        switch ($type) {
            case 'LOW_MARGIN':
                $data = json_encode(array(
                    'text' => '*Low Margin Order #' . $orders_id . ' delivery on ' . $this->API_PROVIDER . ' Replenish API*',
                    'attachments' => array(
                        array(
                            'color' => 'warning',
                            'text' => "Product ID : <" . HTTPS_SERVER . "/categories.php?pID=" . $this->product_id . "&action=new_product|" . $this->product_id . "> \n SKU : " . $sku . " \n Cost : " . $cost_str . " \n Selling Price : " . DEFAULT_CURRENCY . " " . number_format($price,
                                    2) . " \n Actual Margin : " . $margin . "% (<= " . $this->low_margin . "%) \n `Action` : Revise Selling & Cost Setting"
                        )
                    )
                ));
                break;
            case 'MIN_MARGIN':
                $data = json_encode(array(
                    'text' => '*Order #' . $orders_id . ' blocked from delivery on ' . $this->API_PROVIDER . ' Replenish API*',
                    'attachments' => array(
                        array(
                            'color' => 'danger',
                            'text' => "Product ID : <" . HTTPS_SERVER . "/categories.php?pID=" . $this->product_id . "&action=new_product|" . $this->product_id . "> \n SKU : " . $sku . " \n Cost : " . $cost_str . " \n Selling Price : " . DEFAULT_CURRENCY . " " . number_format($price,
                                    2) . " \n Actual Margin : " . $margin . "% (<= " . $this->min_margin . "%) \n `Action` : Revise Selling & Cost Setting then process the order <" . HTTPS_SERVER . "/orders.php?oID=" . $orders_id . "&action=edit|#" . $orders_id . ">"
                        )
                    )
                ));
                break;
        }

        $slack->send(SLACK_WEBHOOK_RESTOCK_MIN_MARGIN, $data);
    }

}

?>