<?php
include_once(DIR_WS_CLASSES . 'slack_notification.php');
class rixty_api
{
    const API_PROVIDER = 'RIXTY';
    const MIN_MARGIN = 0.065;
    const FLAG_NEW = 'N';
    const FLAG_UPLOADING = 'U';
    const FLAG_UPLOADED = 'S';
    const FLAG_ERROR = 'E';
    const GET_METHOD = 'GetSKU';
    const CHECK_METHOD = 'CheckSKU';
    const DEFAULT_ADMIN_EMAIL = 'system';
    const SUCCESS_ACK_FLAG = 'Success';

    public static $service_api_pattern = array(
        array('prefix_len' => 4, 'prefix_text' => 'RXT-')
    );

    public  $resp_currency_settle_amt,
            $resp_currency_code,
            $currency_obj,
            $product_id,
            $orders_products_id;

    // private static $SKU_SUPPORT = array(
    //     'RXT-00500-P', 'RXT-01000-P',
    //     'RXT-02000-P', 'RXT-02500-P',
    //     'RXT-05000-P', 'RXT-10000-P'
    // );

    private $request_method,
            $api_url,
            $api_signature,
            $resp_rixty_id, $resp_desc, $resp_amt, $resp_token, $resp_ack, $resp_error, $resp_serialnumber,
            $curl_obj, $cpc_obj;

	public function __construct() {
        include_once(DIR_WS_CLASSES . 'curl.php');
        include_once(DIR_WS_CLASSES . 'custom_product_code.php');
        include_once(DIR_WS_LANGUAGES . 'english.php');

        $this->api_url = RIXTY_API_URL;
        $this->api_signature = RIXTY_API_SIGNATURE;

        $this->curl_obj = new curl();
        $this->cpc_obj = new custom_product_code();
	}

    public static function isSupportedSKU($sku) {
        $return = false;

        $skuParts = explode('-', $sku);
        if (isset($skuParts[2]) && $skuParts[2] == 'P') {
            $pattern_array = self::$service_api_pattern;

            foreach ($pattern_array as $pattern) {
                if (substr($sku, 0, $pattern['prefix_len']) === $pattern['prefix_text']) {
                    $return = true;
                    break;
                }
            }
        }

        return $return;
    }

    private function generateRequestArray($sku) {
        return array(
            'method' => $this->request_method,
            'signature' => $this->api_signature,
            'sku' => $sku,
        );
    }

    public function getCode($sku) {
        $return_bool = FALSE;

        if (self::isSupportedSKU($sku)) {
            $this->setMethod(self::GET_METHOD);
            $return_bool = $this->preRequest($sku);
        }

        return $return_bool;
    }

    /*
     * getSettleAmount has to define for each API
     */
    protected function getSettleAmount($sku) {
        $currency_code = '';
        $currency_rate = '';
        $currency_settle_amount = 0;
        $settle_amount = 0;
        $this->resp_currency_code = 'USD';

        if (!empty($this->resp_currency_code) && !empty($this->resp_currency_settle_amt)) {
            $currency_code = $this->resp_currency_code;
            $currency_settle_amount = $this->resp_currency_settle_amt;

            if ($this->getCurrencyObj()->is_set($currency_code)) {
                $currency_rate = $this->getCurrencyObj()->get_value($currency_code, 'sell');
                $settle_amount = $this->getCurrencyObj()->advance_currency_conversion($currency_settle_amount, $currency_code, DEFAULT_CURRENCY);
            }
        } else {
            $select_sql = " SELECT products_cost, products_currency
                            FROM " . TABLE_PRODUCTS_COST . "
                            WHERE products_id = '" . $this->product_id . "'";
            $result_sql = tep_db_query($select_sql);
            if ($row = tep_db_fetch_array($result_sql)) {
                $currency_code = $row['products_currency'];
                $currency_settle_amount = $row['products_cost'];

                $currency_rate = $this->getCurrencyObj()->get_value($currency_code, 'sell');
                $settle_amount = $this->getCurrencyObj()->advance_currency_conversion($currency_settle_amount, $currency_code, DEFAULT_CURRENCY);
            }
        }

        return array(
            'currency_code' => $currency_code,
            'currency_rate' => $currency_rate,
            'currency_settle_amount' => $currency_settle_amount,
            'settle_amount' => $settle_amount,
        );
    }

    protected function getCurrencyObj() {
        if (class_exists('currencies') != TRUE) {
            include_once dirname(__FILE__) . DIRECTORY_SEPARATOR . '../../../classes/currencies.php';
        }

        if (!$this->currency_obj) {
            $this->currency_obj = new currencies();
        }

        return $this->currency_obj;
    }

    private function setMethod($method) {
        if (!is_null($method)) {
            $this->request_method = $method;
        }
    }

    public function checkCode($sku) {
        $return_bool = FALSE;

        if (self::isSupportedSKU($sku)) {
            $this->setMethod(self::CHECK_METHOD);
            $return_bool = $this->preRequest($sku);
        }

        return $return_bool;
	}

    private function loadAPIData($raw_response = NULL, $data_array = array()) {
        if (!is_null($raw_response)) {
            $data_array = $this->splitQueryToArray(urldecode($raw_response));
        }

        if (isset($data_array['DESC']) && !is_null($data_array['DESC'])) {
            $this->resp_desc = $data_array['DESC'];
        }

        if (isset($data_array['AMT']) && !is_null($data_array['AMT'])) {
            $this->resp_amt = $data_array['AMT'];
        }

        if (isset($data_array['SETTLEAMT']) && !is_null($data_array['SETTLEAMT'])) {
            $this->resp_currency_settle_amt = $data_array['SETTLEAMT'];
        }

        if (isset($data_array['TOKEN']) && !is_null($data_array['TOKEN'])) {
            $this->resp_token = $data_array['TOKEN'];
        }

        if (isset($data_array['ACK']) && !is_null($data_array['ACK'])) {
            $this->resp_ack = $data_array['ACK'];
        }

        if (isset($data_array['SERIALNUMBER']) && !is_null($data_array['SERIALNUMBER'])) {
            $this->resp_serialnumber = $data_array['SERIALNUMBER'];
        }

        if (isset($data_array['ERRORMESSAGE']) && !is_null($data_array['ERRORMESSAGE'])) {
            $this->resp_error = $data_array['ERRORMESSAGE'];
        }

        if (isset($data_array['rixty_id']) && $data_array['rixty_id'] > 0) {
            $this->resp_rixty_id = $data_array['rixty_id'];
        }
    }

    public function preRequest($sku, $api_method = NULL, $total_try = 0) {
        $return_bool = FALSE;

        if ($total_try < 3) {
            $this->resetAPIData();
            $response = $this->curl_obj->curl_get($this->api_url, $this->generateRequestArray($sku));

            $this->loadAPIData($response);
            $this->logAPIData($sku);

            if ($this->resp_ack == self::SUCCESS_ACK_FLAG) {
                $return_bool = TRUE;
            } else {
                $return_bool = $this->preRequest($sku, $api_method, ++$total_try);
                $this->reportError(array('curlError' => $this->curl_obj->get_error(), 'sku' => $sku, 'method' => $this->request_method, 'try_count' => $total_try,'error_msg'=>$this->resp_error), 'preRequest');
            }
        }

        return $return_bool;
    }

    public function processBatchRestock($product_id, $order_product_store_price, $sku, $qty, $extra_params = array()) {
        $custom_products_code_array = array();
        $code_status = isset($extra_params['code_status']) ? $extra_params['code_status'] : '1';
        $orders_products_id = isset($extra_params['orders_products_id']) ? $extra_params['orders_products_id'] : '';
        $this->product_id = $product_id;
        $this->orders_products_id = $orders_products_id;
        $slack_notified = false;
        if ($qty > 0) {    // if partial delivered
            try {
                if (self::isSupportedSKU($sku) && $order_product_store_price > 0) {
                    if ($this->checkCode($sku) !== FALSE) {
                        $margin = ($order_product_store_price - $this->resp_currency_settle_amt) / $order_product_store_price;

                        if ($margin > RIXTY_MIN_MARGIN) {
                            for ($count = 0; $count < $qty; $count++) {
                                if ($this->getCode($sku) !== FALSE) {
                                    $custom_products_code_id = $this->uploadCode($product_id, $code_status);

                                    // create cd key file
                                    if ($custom_products_code_id !== FALSE) {
                                        $custom_products_code_array[] = $custom_products_code_id;
                                    }
                                }
                                elseif(!$slack_notified){
                                    $slack_notified = true;
                                    $this->failedDeliveryNotification($sku);
                                }
                            }

                            $this->addProductAvailableQty($product_id, $custom_products_code_array);
                        } else {
                            // report settle amt > expected
                            try{
                                $this->lowMarginReport($sku,'USD', $this->resp_currency_settle_amt, $order_product_store_price, $margin);
                                $this->reportError(array('product_id' => $product_id, 'resp_currency_settle_amt' => $this->resp_currency_settle_amt, 'order_product_store_price' => $order_product_store_price,'error_msg'=>'Settle amount is greater than expected'), 'processBatchRestock()');
                            } catch(Exception $e){
                                //Do Nothing
                            }
                        }
                    }
                    //Prevent Multiple Notification for same orders products
                    elseif(!$slack_notified){
                        $this->resp_error = 'Fail to check margin/SKU not available.';
                        $slack_notified = true;
                        $this->failedDeliveryNotification($sku);
                    }
                }
            } catch (Exception $e) {
                $return_bool = FALSE;
                $this->reportError(array('product_id' => $product_id, 'sku' => $sku, 'qty' => $qty, 'e' => $e->getMessage()), 'processBatchRestock()');
            }
        }

        return $custom_products_code_array !== array();
    }

    public function processRestock($product_id, $order_product_store_price, $sku, $code_status = '0') {
        $return_bool = FALSE;

        if (self::isSupportedSKU($sku) && $order_product_store_price > 0) {
            if ($this->checkCode($sku) !== FALSE) {
                $margin = ($order_product_store_price - $this->resp_currency_settle_amt) / $order_product_store_price;

                if ($margin > RIXTY_MIN_MARGIN) {
                    if ($this->getCode($sku) !== FALSE) {
                        $custom_products_code_id = $this->uploadCode($product_id, $code_status);

                        // create cd key file
                        if ($custom_products_code_id !== FALSE) {
                            $this->addProductAvailableQty($product_id, array($custom_products_code_id));
                            $return_bool = TRUE;
                        }
                    }
                } else {
                    // report settle amt > expected
                    $this->reportError(array('product_id' => $product_id, 'resp_currency_settle_amt' => $this->resp_currency_settle_amt, 'order_product_store_price' => $order_product_store_price,'error_msg'=>'Settle amount is greater than expected'), 'processRestock()');
                }
            }
        }

        return $return_bool;
    }

    private function resetAPIData($default = '') {
        foreach (get_class_vars(get_class($this)) as $name => $value) {
            if (substr($name, 0, 5) == 'resp_') {
                $this->$name = $default;
            }
        }
    }

    private function splitQueryToArray($data_str) {
        $return_array = array();

        if(!empty($data_str)){
            $pairs = explode("&", $data_str);

            foreach ($pairs as $pair) {
                list($key, $val) = explode("=", $pair);
                $return_array[$key] = $val;
            }
        }

        return $return_array;
    }

    private function addProductAvailableQty($product_id, $cdkey_id_arr) {
        global $log_object;

        if (is_object($log_object)) {
            $adj_qty = count($cdkey_id_arr);

            if ($adj_qty) {
                $products_quantity_select_sql = "	SELECT products_quantity, products_actual_quantity 
                                                    FROM ".TABLE_PRODUCTS." 
                                                    WHERE products_id=" . $product_id;
                $products_quantity_result_sql = tep_db_query($products_quantity_select_sql);
                if ($products_quantity_row = tep_db_fetch_array($products_quantity_result_sql)) {
                    $old_prod_available_qty = $products_quantity_row['products_quantity'];
                    $old_prod_actual_qty = $products_quantity_row['products_actual_quantity'];
                    $new_prod_available_qty = $old_prod_available_qty + $adj_qty;
                    $new_prod_actual_qty = $old_prod_actual_qty + $adj_qty;

                    $product_sql = "UPDATE " . TABLE_PRODUCTS . " 
                                        SET products_quantity = IF(products_quantity IS NULL, 0, products_quantity) + " . $adj_qty . ", 
                                            products_actual_quantity = IF(products_actual_quantity IS NULL, 0, products_actual_quantity) + " . $adj_qty . " 
                                    WHERE products_id = " . $product_id;
                    if (tep_db_query($product_sql)) {
                        $log_object->insert_log($product_id, 'products_quantity', $old_prod_available_qty.':~:'.$old_prod_actual_qty, $new_prod_available_qty.':~:'.$new_prod_actual_qty, sprintf(LOG_QTY_ADJUST, ''), sprintf(LOG_CDKEY_ID_STR, implode(',', $cdkey_id_arr)));
                    }

                    return TRUE;
                }
            }
        } else {
            $this->reportError('Error: Missing object log_object', 'addProductAvailableQty()');
        }

        return FALSE;
    }

    /*
     * status : 0 - sold
     * status : 1 - available
     * status : -2 - on hold
     */
    private function uploadCode($product_id, $status = '0', $code = NULL, $purchase_orders_id = NULL) {
        $this->loadAPIData(NULL, array('TOKEN' => $code, 'rixty_id' => $purchase_orders_id));
        $timestamp = date('Y-m-d H:i:s');
        $return_bool = FALSE;

        $insert_array = array(
            'products_id' => $product_id,
            'status_id' => $status,
            'file_name' => $this->resp_rixty_id,
            'file_type' => 'soft',
            'code_date_added' => $timestamp,
            'code_date_modified' => $timestamp,
            'code_uploaded_by' => self::DEFAULT_ADMIN_EMAIL,
            'remarks' => self::API_PROVIDER . '_API'
        );

        if (tep_db_perform(TABLE_CUSTOM_PRODUCTS_CODE, $insert_array)) {
            $API_flag = self::FLAG_UPLOADING;
            $custom_products_code_id = tep_db_insert_id();
            $key_string = tep_encrypt_data(base64_encode($this->resp_token));

            if ($this->cpc_obj->uploadCode($key_string, $custom_products_code_id, $product_id, $timestamp) === TRUE) {
                $API_flag = self::FLAG_UPLOADED;
                $return_bool = $custom_products_code_id;
            }

            $this->logAPIData('', $API_flag, $this->resp_rixty_id, $return_bool);
        }

        return $return_bool;
    }

    private function logAPIData($sku, $flag_state = '', $log_id = 0, $custom_products_code_id = '') {
        $return_init = 0;
        $flag_state = $flag_state != '' ? $flag_state : ($this->resp_ack !== 'Success' ? self::FLAG_ERROR : self::FLAG_NEW);

        switch ($flag_state) {
            case self::FLAG_UPLOADING:
                $sql_data_array = array(
                    'flag_state' => $flag_state
                );

                $return_init = tep_db_perform(TABLE_LOG_API_RESTOCK, $sql_data_array, 'update', " id = '" . $log_id . "'");
                break;
            case self::FLAG_UPLOADED:

                $publisher_id = $this->getPublisherId($log_id);

                $sql_data_array = array(
                    'flag_state' => $flag_state,
                    'custom_products_code_id' => $custom_products_code_id,
                    'publishers_id' => $publisher_id,
                    'token' => ''
                );

                $return_init = tep_db_perform(TABLE_LOG_API_RESTOCK, $sql_data_array, 'update', " id = '" . $log_id . "'");
                break;
            case self::FLAG_ERROR:
                $log_query_array = array(
                    'transaction_id' => $log_id,
                    'log_method' => $this->request_method,
                    'log_sku' => tep_db_prepare_input($sku),
                    'log_ack' => tep_db_prepare_input($this->resp_ack),
                    'log_flag_state' => self::FLAG_ERROR,
                    'log_api_provider' => self::API_PROVIDER,
                    'log_msg' => $this->resp_error,
                    'log_created_datetime' => 'now()'
                );
                tep_db_perform(TABLE_LOG_API_RESTOCK_TRANSACTION, $log_query_array);
            case self::FLAG_NEW:
            default:
                $settle_amount_info = $this->getSettleAmount($sku);

                $query_array = array(
                    'description' => tep_db_prepare_input($this->resp_desc),
                    'amount' => tep_db_prepare_input($this->resp_amt),
                    'settle_amount' => $settle_amount_info['settle_amount'],
                    'currency_code' => $settle_amount_info['currency_code'],
                    'currency_rate' => $settle_amount_info['currency_rate'],
                    'currency_settle_amount' => $settle_amount_info['currency_settle_amount'],
                    'token' => tep_db_prepare_input($this->resp_token),
                    'ack' => tep_db_prepare_input($this->resp_ack),
                    'serialnumber' => tep_db_prepare_input($this->resp_serialnumber),
                    'error_msg' => tep_db_prepare_input($this->resp_error),
                    'flag_state' => $flag_state,
                    'method' => $this->request_method,
                    'sku' => tep_db_prepare_input($sku),
                    'api_provider' => self::API_PROVIDER,
                    'created_datetime' => 'now()'
                );

                if (tep_db_perform(TABLE_LOG_API_RESTOCK, $query_array)) {
                    $this->loadAPIData(NULL, array('rixty_id' => tep_db_insert_id()));
                    $return_init = TRUE;

                    $log_query_array = array(
                        'transaction_id' => tep_db_insert_id(),
                        'log_method' => $this->request_method,
                        'log_sku' => tep_db_prepare_input($sku),
                        'log_ack' => tep_db_prepare_input($this->resp_ack),
                        'log_flag_state' => $flag_state,
                        'log_api_provider' => self::API_PROVIDER,
                        'log_msg' => '',
                        'log_created_datetime' => 'now()'
                    );
                    tep_db_perform(TABLE_LOG_API_RESTOCK_TRANSACTION, $log_query_array);

                } else {
                    $log_query_array = array(
                        'transaction_id' => tep_db_insert_id(),
                        'log_method' => $this->request_method,
                        'log_sku' => tep_db_prepare_input($sku),
                        'log_ack' => tep_db_prepare_input($this->resp_ack),
                        'log_flag_state' => $flag_state,
                        'log_api_provider' => self::API_PROVIDER,
                        'log_msg' => 'logAPIData()',
                        'log_created_datetime' => 'now()'
                    );
                    tep_db_perform(TABLE_LOG_API_RESTOCK_TRANSACTION, $log_query_array);

                    $this->reportError($query_array, 'logAPIData()');
                    $return_init = FALSE;
                }
                break;
        }

        return $return_init;
    }

    /*
     * getPublisherId has to define for each API
     */
    private function getPublisherId($log_id) {

        $select_sql = " SELECT api_provider, method
                        FROM " . TABLE_LOG_API_RESTOCK . "
                        WHERE id = '" . $log_id . "'";
        $result_sql = tep_db_query($select_sql);
        if ($row_sql = tep_db_fetch_array($result_sql)) {
            $publisher_sql = "SELECT publishers_replenish_id
                            FROM " . TABLE_PUBLISHERS_REPLENISH . "
                            WHERE publishers_api_provider = '" . $row_sql['api_provider'] . "'
                                AND publishers_api_method = '" . $row_sql['method'] . "'
                            LIMIT 1";
            $publisher_result = tep_db_query($publisher_sql);
            if ($publisher_row = tep_db_fetch_array($publisher_result)) {
                $publisher_id = $publisher_row['publishers_replenish_id'];
            }
        }

        return $publisher_id;
    }

    protected function reportError($response_data, $ext_subject = '')
    {
        $slack = new slack_notification();
        $data = json_encode(array(
            'text' => '[OG Crew] Rixty API Error API Error - ' . date("F j, Y H:i") . ' from ' . (isset($_SERVER['SERVER_NAME']) && !empty($_SERVER['SERVER_NAME']) ? $_SERVER['SERVER_NAME'] : '-'),
            'attachments' => array(
                array(
                    'color' => 'warning',
                    'text' => $ext_subject . "\n\n" . json_encode($response_data)
                )
            )
        ));
        $slack->send(SLACK_WEBHOOK_DEV_DEBUG, $data);
    }

    public function failedDeliveryNotification($sku)
    {
        $slack = new slack_notification();
        $products_info = $this->getOrdersProductsInfoByOrdersProductsId($this->orders_products_id);

        if($products_info){
            $orders_id = $products_info['orders_id'];
            $products_title = $products_info['products_name'];
            $error = $this->resp_error;
            if(empty($error)){
                $curl_error = $this->curl_obj->get_error();
                $error = ((isset($curl_error['error_message']) && !empty($curl_error['error_message'])) ? $curl_error['error_message'] .' ('.$curl_error['error_code'].')' : 'Empty Response from publishers api');
            }
            $data = json_encode(array(
                'text' => '*Failed Delivery #' . $orders_id . ' delivery on ' . self::API_PROVIDER . ' Replenish API*',
                'attachments' => array(
                    array(
                        'color' => 'warning',
                        'text' => "Orders ID : <" . HTTPS_SERVER . "orders.php?oID=" . $orders_id . "&action=edit|" . $orders_id . ">\n Product Name : " . $products_title ." \n SKU : " . $sku . " \n Error : ". $error ." \n `Action` : Reattempt delivery or contact publishers with error message"
                    )
                )
            ));

            $slack->send(SLACK_WEBHOOK_RESTOCK_FAIL_URL, $data);
        }

    }

    protected function getOrdersIdByOrdersProductsId($opId)
    {
        $orders_id = 0;
        $op_sql = "SELECT orders_id
                            FROM " . TABLE_ORDERS_PRODUCTS . "
                            WHERE orders_products_id = '" . $opId . "'
                            LIMIT 1";
        $op_results = tep_db_query($op_sql);
        if ($op_row = tep_db_fetch_array($op_results)) {
            $orders_id = $op_row['orders_id'];
        }

        return $orders_id;
    }

    protected function getOrdersProductsInfoByOrdersProductsId($opId)
    {
        $products_name = '';
        $orders_id = 0;
        $op_sql = "SELECT products_name,orders_id
                            FROM " . TABLE_ORDERS_PRODUCTS . "
                            WHERE orders_products_id = '" . $opId . "'
                            LIMIT 1";
        $op_results = tep_db_query($op_sql);
        if ($op_row = tep_db_fetch_array($op_results)) {
            return $op_row;
        }

        return false;
    }


    public function lowMarginReport($sku, $cost_cur, $cost_amt, $price, $margin)
    {
        $slack = new slack_notification();
        $cost_str = $cost_cur . ' ' . number_format($cost_amt, 2);
        $orders_id = $this->getOrdersIdByOrdersProductsId($this->orders_products_id);

        $data = json_encode(array(
            'text' => '*Order #' . $orders_id . ' blocked from delivery on ' . self::API_PROVIDER . ' Replenish API*',
            'attachments' => array(
                array(
                    'color' => 'danger',
                    'text' => "Product ID : <" . HTTPS_SERVER . "/categories.php?pID=" . $this->product_id . "&action=new_product|" . $this->product_id . "> \n SKU : " . $sku . " \n Cost : " . $cost_str . " \n Selling Price : " . DEFAULT_CURRENCY . " " . number_format($price,
                            2) . " \n Actual Margin : " . $margin . "% (<= " . RIXTY_MIN_MARGIN * 100 . "%) \n `Action` : Revise Selling & Cost Setting then process the order <" . HTTPS_SERVER . "/orders.php?oID=" . $orders_id . "&action=edit|#" . $orders_id . ">"
                )
            )
        ));

        $slack->send(SLACK_WEBHOOK_RESTOCK_MIN_MARGIN, $data);
    }
}

include_once dirname(__FILE__) . DIRECTORY_SEPARATOR . 'config.inc.php';
?>