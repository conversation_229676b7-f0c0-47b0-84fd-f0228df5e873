<?php if (!class_exists('vtc_api')) die('No direct access allowed.');

switch ('production') {
    case 'production':
        define('VTC_API_ACCOUNT', 'offgamers1');
        define('VTC_API_URL', 'http://sandbox3.vtcebank.vn/PayAPI/WS/GoodsPaygate.asmx?wsdl');
        define('VTC_REQUEST_API_PRIVATE_KEY', '<rsakeyvalue>
<modulus>m0H32tabZfMf21D3llbGtXoqsgR71fd7JQ/1/BLTIMeObx89XCikY/JyKmE/LHQLlSPr7DdmH4mIluOks9d87sJ1XfPRvWfECyRKFdGCu8mFF/jiQ603Yz+m/375vgbK39dzgDbKpMbKhYU3gQ9FpLdSPW/Lw87p409Y5Vg5P8E=</modulus>
<exponent>AQAB</exponent>
<p>y8mKcJll/5JGaYwf+oyosZ6rVUrtKJAK/d+jB8DDVdBsJhK1WZd7EpA9vBINUQWluplHGT91ZnTnCMBvD0qA4Q==</p>
<q>wwlcGhiZHwYCJfd+NuEQtjpASdLbGAZVuSPfZ2iBrPK8qcT6TXEnVo3Rv4zuAbG74Gl/P5htkRhhG9VMuOs64Q==</q>
<dp>w/EnAZkdL51PwpCO9vNkCFTN3JbMbWICj5QGR6AasVpTglAeuuQh2/mAwmKBF1Rcw4w5hpczpK3mrs2Ie6VhQQ==</dp>
<dq>wOxyQZIRbOGpoFq3a5unx0nJq/y6IoKpqxBz/TsgAq69toUVPyNt0S1JzFugVuazvGE+sO6bewoNjNWqJcTxoQ==</dq>
<inverseq>Cr67Yr5Co77+ZujHVdz6pH5ABhD5Hxf8/X2bbRYRNOQT++SuDo5+hT/1DjYnJleL1dgPG+LYi6n75bi/H6yC9Q==</inverseq>
<d>Z4o0+zKH7BOZuS9kDlTMrBZPBu1KkMU80Ni2GWeMeZ3b9WuGBkHsb+MuE+lKsiCp4MpV+fBwILQd7VLfcjhRTfdTcAMhcktl7Zw+R2Tw3ee5ZNJw4EbvkbsCHNNBGpN/eaaPWSWmIuHJs+ZuWzIGRedJb4hVGCtHzQtVoBA06AE=</d>
</rsakeyvalue>');
        define('VTC_RESPONSE_API_PUBLIC_KEY', '<RSAKeyValue>
<Modulus>uQqMJA5dXrJti/oQsAsAbMTA5BvPJ/WvcpVIlrHKmaqlBa92WSZNmEYbs4kh7gH05cj7jsQEZoydS1+NFSJPpVhcz/NM55fIZQbHb1vyvVycyYmCKGawFW7l589al0oP0CJNc0a4BKx+aE6+ShvyOAFFt6NgVDq500RXCIGoevE=</Modulus>
<Exponent>AQAB</Exponent>
</RSAKeyValue>');
        define('VTC_TRIPLE_DES_KEY', '0123456789ABCDEF');
        break;
    case 'development':
        define('VTC_API_ACCOUNT', 'offgamers1');
        define('VTC_API_URL', 'http://sandbox3.vtcebank.vn/PayAPI/WS/GoodsPaygate.asmx?wsdl');
        define('VTC_REQUEST_API_PRIVATE_KEY', '<rsakeyvalue>
<modulus>m0H32tabZfMf21D3llbGtXoqsgR71fd7JQ/1/BLTIMeObx89XCikY/JyKmE/LHQLlSPr7DdmH4mIluOks9d87sJ1XfPRvWfECyRKFdGCu8mFF/jiQ603Yz+m/375vgbK39dzgDbKpMbKhYU3gQ9FpLdSPW/Lw87p409Y5Vg5P8E=</modulus>
<exponent>AQAB</exponent>
<p>y8mKcJll/5JGaYwf+oyosZ6rVUrtKJAK/d+jB8DDVdBsJhK1WZd7EpA9vBINUQWluplHGT91ZnTnCMBvD0qA4Q==</p>
<q>wwlcGhiZHwYCJfd+NuEQtjpASdLbGAZVuSPfZ2iBrPK8qcT6TXEnVo3Rv4zuAbG74Gl/P5htkRhhG9VMuOs64Q==</q>
<dp>w/EnAZkdL51PwpCO9vNkCFTN3JbMbWICj5QGR6AasVpTglAeuuQh2/mAwmKBF1Rcw4w5hpczpK3mrs2Ie6VhQQ==</dp>
<dq>wOxyQZIRbOGpoFq3a5unx0nJq/y6IoKpqxBz/TsgAq69toUVPyNt0S1JzFugVuazvGE+sO6bewoNjNWqJcTxoQ==</dq>
<inverseq>Cr67Yr5Co77+ZujHVdz6pH5ABhD5Hxf8/X2bbRYRNOQT++SuDo5+hT/1DjYnJleL1dgPG+LYi6n75bi/H6yC9Q==</inverseq>
<d>Z4o0+zKH7BOZuS9kDlTMrBZPBu1KkMU80Ni2GWeMeZ3b9WuGBkHsb+MuE+lKsiCp4MpV+fBwILQd7VLfcjhRTfdTcAMhcktl7Zw+R2Tw3ee5ZNJw4EbvkbsCHNNBGpN/eaaPWSWmIuHJs+ZuWzIGRedJb4hVGCtHzQtVoBA06AE=</d>
</rsakeyvalue>');
        define('VTC_RESPONSE_API_PUBLIC_KEY', '<RSAKeyValue>
<Modulus>uQqMJA5dXrJti/oQsAsAbMTA5BvPJ/WvcpVIlrHKmaqlBa92WSZNmEYbs4kh7gH05cj7jsQEZoydS1+NFSJPpVhcz/NM55fIZQbHb1vyvVycyYmCKGawFW7l589al0oP0CJNc0a4BKx+aE6+ShvyOAFFt6NgVDq500RXCIGoevE=</Modulus>
<Exponent>AQAB</Exponent>
</RSAKeyValue>');
        define('VTC_TRIPLE_DES_KEY', '0123456789ABCDEF');
        break;
}