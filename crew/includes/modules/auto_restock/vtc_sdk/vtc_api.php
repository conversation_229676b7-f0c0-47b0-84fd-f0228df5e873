<?php
include_once dirname(__FILE__) . DIRECTORY_SEPARATOR . '../restock_api.php';

/*
 * VTC staging URL: http://sandbox1.vtcebank.vn/pay.vtc.vn/
 * Username: offgamers Password: xrar8998XR 
 */
class vtc_api extends restock_api 
{
    const KEY_FORMAT = 'Codes : %s<br>Serial : %s<br>Expiry Date : %s (dd/MM/yyyy)';
    const API_VERSION = '1.0';
    const PARTNER_CODE = 'offgamers';

    public static $SKU_SUPPORT = array(
        #Viettel
        'VTC_27_10000' => array('amt' => 10000, 'settle_amt' => 0.45),
        'VTC_27_20000' => array('amt' => 20000, 'settle_amt' => 0.90),
        'VTC_27_30000' => array('amt' => 30000, 'settle_amt' => 1.36),
        'VTC_27_50000' => array('amt' => 50000, 'settle_amt' => 2.27),
        'VTC_27_100000' => array('amt' => 100000, 'settle_amt' => 4.54),
        'VTC_27_200000' => array('amt' => 200000, 'settle_amt' => 9.08),
        'VTC_27_300000' => array('amt' => 300000, 'settle_amt' => 13.62),
        'VTC_27_500000' => array('amt' => 500000, 'settle_amt' => 22.70),
        
        #Vinaphone
        'VTC_28_10000' => array('amt' => 10000, 'settle_amt' => 0.46),
        'VTC_28_20000' => array('amt' => 20000, 'settle_amt' => 0.92),
        'VTC_28_30000' => array('amt' => 30000, 'settle_amt' => 1.38),
        'VTC_28_50000' => array('amt' => 50000, 'settle_amt' => 2.30),
        'VTC_28_100000' => array('amt' => 100000, 'settle_amt' => 4.61),
        'VTC_28_200000' => array('amt' => 200000, 'settle_amt' => 9.21),
        'VTC_28_300000' => array('amt' => 300000, 'settle_amt' => 13.82),
        'VTC_28_500000' => array('amt' => 500000, 'settle_amt' => 23.03),
        
        #Mobifone
        'VTC_29_10000' => array('amt' => 10000, 'settle_amt' => 0.46),
        'VTC_29_20000' => array('amt' => 20000, 'settle_amt' => 0.92),
        'VTC_29_30000' => array('amt' => 30000, 'settle_amt' => 1.38),
        'VTC_29_50000' => array('amt' => 50000, 'settle_amt' => 2.30),
        'VTC_29_100000' => array('amt' => 100000, 'settle_amt' => 4.61),
        'VTC_29_200000' => array('amt' => 200000, 'settle_amt' => 9.21),
        'VTC_29_300000' => array('amt' => 300000, 'settle_amt' => 13.82),
        'VTC_29_500000' => array('amt' => 500000, 'settle_amt' => 23.03),
        
        #Sfone
        'VTC_30_10000' => array('amt' => 10000, 'settle_amt' => 0.44),
        'VTC_30_20000' => array('amt' => 20000, 'settle_amt' => 0.89),
        'VTC_30_30000' => array('amt' => 30000, 'settle_amt' => 1.33),
        'VTC_30_50000' => array('amt' => 50000, 'settle_amt' => 2.22),
        'VTC_30_100000' => array('amt' => 100000, 'settle_amt' => 4.44),
        'VTC_30_200000' => array('amt' => 200000, 'settle_amt' => 8.88),
        'VTC_30_300000' => array('amt' => 300000, 'settle_amt' => 13.32),
        'VTC_30_500000' => array('amt' => 500000, 'settle_amt' => 22.20),
        
        #Gmobile
        'VTC_173_10000' => array('amt' => 10000, 'settle_amt' => 0.46),
        'VTC_173_20000' => array('amt' => 20000, 'settle_amt' => 0.92),
        'VTC_173_30000' => array('amt' => 30000, 'settle_amt' => 1.38),
        'VTC_173_50000' => array('amt' => 50000, 'settle_amt' => 2.30),
        'VTC_173_100000' => array('amt' => 100000, 'settle_amt' => 4.61),
        'VTC_173_200000' => array('amt' => 200000, 'settle_amt' => 9.21),
        'VTC_173_300000' => array('amt' => 300000, 'settle_amt' => 13.82),
        'VTC_173_500000' => array('amt' => 500000, 'settle_amt' => 23.03),
        
        #​Vietnam Mobile
        'VTC_154_10000' => array('amt' => 10000, 'settle_amt' => 0.46),
        'VTC_154_20000' => array('amt' => 20000, 'settle_amt' => 0.91),
        'VTC_154_50000' => array('amt' => 50000, 'settle_amt' => 2.28),
        'VTC_154_100000' => array('amt' => 100000, 'settle_amt' => 4.56),
        'VTC_154_200000' => array('amt' => 200000, 'settle_amt' => 9.12),
        'VTC_154_300000' => array('amt' => 300000, 'settle_amt' => 13.68),
        'VTC_154_500000' => array('amt' => 500000, 'settle_amt' => 22.79),
        
        #VTC Card
        'VTC_114_10000' => array('amt' => 10000, 'settle_amt' => 0.45),
        'VTC_114_20000' => array('amt' => 20000, 'settle_amt' => 0.90),
        'VTC_114_50000' => array('amt' => 50000, 'settle_amt' => 2.25),
        'VTC_114_100000' => array('amt' => 100000, 'settle_amt' => 4.49),
        'VTC_114_200000' => array('amt' => 200000, 'settle_amt' => 8.89),
        'VTC_114_300000' => array('amt' => 300000, 'settle_amt' => 13.34),
        'VTC_114_500000' => array('amt' => 500000, 'settle_amt' => 22.23),
        'VTC_114_1000000' => array('amt' => 1000000, 'settle_amt' => 44.46),
        
        #LIKE
        'VTC_321_20000' => array('amt' => 20000, 'settle_amt' => 0.86),
        'VTC_321_50000' => array('amt' => 50000, 'settle_amt' => 2.15),
        'VTC_321_100000' => array('amt' => 100000, 'settle_amt' => 4.30),
        'VTC_321_500000' => array('amt' => 500000, 'settle_amt' => 21.51),
        'VTC_321_1000000' => array('amt' => 1000000, 'settle_amt' => 43.02),

        #OnCash
        'VTC_166_20000' => array('amt' => 20000, 'settle_amt' => 0.87),
        'VTC_166_60000' => array('amt' => 60000, 'settle_amt' => 2.61),
        'VTC_166_100000' => array('amt' => 100000, 'settle_amt' => 4.35),
        'VTC_166_200000' => array('amt' => 200000, 'settle_amt' => 8.70),
        'VTC_166_500000' => array('amt' => 500000, 'settle_amt' => 21.74),

        #Zing Card
        'VTC_67_10000' => array('amt' => 10000, 'settle_amt' => 0.43),
        'VTC_67_20000' => array('amt' => 20000, 'settle_amt' => 0.87),
        'VTC_67_50000' => array('amt' => 50000, 'settle_amt' => 2.17),
        'VTC_67_100000' => array('amt' => 100000, 'settle_amt' => 4.35),
        'VTC_67_200000' => array('amt' => 200000, 'settle_amt' => 8.70),
        'VTC_67_500000' => array('amt' => 500000, 'settle_amt' => 21.74),

        #Gate Card
        'VTC_68_10000' => array('amt' => 10000, 'settle_amt' => 0.44),
        'VTC_68_20000' => array('amt' => 20000, 'settle_amt' => 0.87),
        'VTC_68_50000' => array('amt' => 50000, 'settle_amt' => 2.16),
        'VTC_68_100000' => array('amt' => 100000, 'settle_amt' => 4.31),
        'VTC_68_200000' => array('amt' => 200000, 'settle_amt' => 8.62),
        'VTC_68_500000' => array('amt' => 500000, 'settle_amt' => 21.54),
        'VTC_68_1000000' => array('amt' => 1000000, 'settle_amt' => 43.07),
        'VTC_68_2000000' => array('amt' => 2000000, 'settle_amt' => 86.14),
        
        #Bid Defender
        'VTC_144_130000' => array('amt' => 130000, 'settle_amt' => 5.08),
        'VTC_144_170000' => array('amt' => 170000, 'settle_amt' => 6.65),
        'VTC_144_190000' => array('amt' => 190000, 'settle_amt' => 7.43),
        'VTC_144_250000' => array('amt' => 250000, 'settle_amt' => 9.78),
        
        #Garena VN
        'VTC_319_20000' => array('amt' => 20000, 'settle_amt' => 0.83),
        'VTC_319_50000' => array('amt' => 50000, 'settle_amt' => 2.08),
        'VTC_319_100000' => array('amt' => 100000, 'settle_amt' => 4.16),
        'VTC_319_200000' => array('amt' => 200000, 'settle_amt' => 8.31),
        'VTC_319_500000' => array('amt' => 500000, 'settle_amt' => 20.79),

    );
    
    public static $service_api_pattern = array(
        array('prefix_len' => 3, 'prefix_text' => 'VTC')
    );
    
    public $request_vtc_transactioncode;
    
    private $api_public_key,
            $api_private_key,
            $triple_des_key,
            $partner_code,
            $rsa_obj,
            $triple_des_obj;

    public function __construct() {
        parent::__construct();

        $this->api_url = VTC_API_URL;
        $this->api_public_key = VTC_RESPONSE_API_PUBLIC_KEY;
        $this->api_private_key = VTC_REQUEST_API_PRIVATE_KEY;
        $this->triple_des_key = VTC_TRIPLE_DES_KEY;
        $this->partner_code = defined('VTC_API_ACCOUNT') ? VTC_API_ACCOUNT : self::PARTNER_CODE;
        
        $this->SUCCESS_STATUSFLAG = 1;
        $this->API_PROVIDER = 'VTC';
        $this->CHECK_METHOD = 'BuyCard';
        $this->GET_METHOD = 'GetCard';
    }

    /*
     * Pattern VTC_50 : 10000, 20000, 30000, 50000, 100000, 200000, 300000, 500000
     */
    public static function isSupportedSKU($sku) {
        $return = false;
        $skuParts = explode('_', $sku);

        if (isset($skuParts[2])) {
            $pattern_array = self::$service_api_pattern;
            
            foreach ($pattern_array as $pattern) {
                if (substr($sku, 0, $pattern['prefix_len']) === $pattern['prefix_text']) {
                    $return = true;
                    break;
                }
            }
        }

        return $return;
    }
	
    protected function getAmount($sku) {
        $skuParts = explode('_', $sku);
        
        if (isset($skuParts[2])) {
            return $skuParts[2];
        }
        
        return 0;
    }
    
    protected function getServiceCode($sku) {
        $skuParts = explode('_', $sku);

        return 'VTC' . str_pad($skuParts[1], 4, "0", STR_PAD_LEFT);
    }

    private function getRSAObj() {
        if (is_null($this->rsa_obj)) {
            include_once('phpseclib0.3.8/Crypt/RSA.php');
            $this->rsa_obj = new Crypt_RSA();
        }
        
        return $this->rsa_obj;
    }
    
    private function getTripleDesObj() {
        if (is_null($this->triple_des_obj)) {
            include_once('phpseclib0.3.8/Crypt/TripleDES.php');
            $this->triple_des_obj = new Crypt_TripleDES(CRYPT_DES_MODE_ECB);
        }
        
        return $this->triple_des_obj;
    }
    
    protected function preRequest($sku, $api_method = NULL, $total_try = 0) {
        $return_bool = FALSE;
        $proxy_setting = array();
        $error_array = array();
        
        $this->resetAPIData();

        if ($this->curl_obj->connect_via_proxy) {
            $proxy_setting = array(
                "trace" => 1,
                "exceptions" => 0,
                'proxy_host' => "my-proxy.offgamers.lan",
                'proxy_port' => 3128
            );
        }
        
        try {
            $client = new SoapClient($this->api_url, $proxy_setting);
            $response = $client->RequestTransaction($this->generateRequestString($sku));
        } catch (SoapFault $e) {
            $error_array = array(
                'error_code' => $e->getCode(),
                'error_message' => "SOAP Fault: " . $e->getMessage(),
            );
        }
        
        unset($client);

        $this->loadAPIData($response, array('sku' => $sku));

        if ($this->logAPIData($sku)) {
            $return_bool = TRUE;
        } else if ($this->failedReattempt($sku, $error_array, $total_try)) {
            $return_bool = $this->preRequest($sku, $api_method, ++$total_try);
        }
        
        return $return_bool;
    }
    
    protected function generateRequestString($sku) {
        $return_xml = '';

        switch ($this->request_method) {
            case $this->CHECK_METHOD:
                $serviveCode = $this->getServiceCode($sku);
                $amt = $this->getAmount($sku);
                $trans_date = date('YmdHis');
                $plaintext = $serviveCode . '-' . $amt . '-' . $this->request_quantity . '-' . $this->partner_code . '-' . $trans_date . '-' . $this->request_transactioncode;
                $return_xml = <<< EOL
<?xml version="1.0" encoding="UTF8" ?>
<RequestData>
<ServiceCode>{$serviveCode}</ServiceCode>
<Amount>{$amt}</Amount>
<Quantity>{$this->request_quantity}</Quantity>
<TransDate>{$trans_date}</TransDate>
<OrgTransID>{$this->request_transactioncode}</OrgTransID>
<DataSign>{$this->generateSignature($plaintext)}</DataSign>
</RequestData>
EOL;
                break;
            case $this->GET_METHOD:
                $serviveCode = $this->getServiceCode($sku);
                $amt = $this->getAmount($sku);
                $plaintext = $serviveCode . '-' . $amt . '-' . $this->partner_code . '-' . $this->request_vtc_transactioncode;
                
                $return_xml = <<< EOL
<?xml version="1.0" encoding="utf-8" ?>
<RequestData>
<ServiceCode>{$serviveCode}</ServiceCode>
<Amount>{$amt}</Amount>
<OrgTransID>{$this->request_vtc_transactioncode}</OrgTransID>
<DataSign>{$this->generateSignature($plaintext)}</DataSign>
</RequestData>
EOL;
                break;
        }

        return array(
            'requesData' => $return_xml,
            'partnerCode' => $this->partner_code,
            'commandType' => $this->request_method,
            'version' => self::API_VERSION
        );
    }
    
    // temporary enable access from outside
    public function checkCode($sku, $qty, $trans_code) {
        $return_bool = FALSE;
        
        if (self::isSupportedSKU($sku)) {
            $this->setMethod($this->CHECK_METHOD);
            
            if ($trans_code = $this->logAPIData($sku, self::FLAG_INIT)) {
                $this->setQuantity($qty);
                $this->setTransCode($trans_code);

                $return_bool = $this->preRequest($sku);
            }
        }
        
        return $return_bool;
    }
    
    protected function getCode($sku, $qty, $renew_transactioncode = true) {
        $return_bool = FALSE;
        
        if (self::isSupportedSKU($sku)) {
            $return_bool = parent::getCode($sku, $qty, false);
        }
        
        return $return_bool;
    }
    
    public function processBatchRestock($product_id, $order_product_store_price, $sku, $qty, $extra_params = array()) {
        return parent::processBatchRestock($product_id, $order_product_store_price, $sku, $qty, $extra_params);
    }
    
    protected function loadAPIData($raw_response = NULL, $data_array = array()) {
        if (!is_null($raw_response)) {
            $data_array = $this->parseXML($raw_response, $data_array);
        }
        
        if (isset($data_array['vtc_transactioncode']) && !is_null($data_array['vtc_transactioncode'])) {
            $this->request_vtc_transactioncode = $data_array['vtc_transactioncode'];
        }
        
        if (isset($data_array['transactioncode']) && !is_null($data_array['transactioncode'])) {
            $this->resp_transactioncode = $data_array['transactioncode'];
        }
        
        if (isset($data_array['items']) && is_array($data_array['items'])) {
            $this->resp_items = array();
            
            foreach ($data_array['items'] as $idx => $item_array) {
                if (isset($item_array['code'])) {
                    $this->resp_items[] = array(
                        'log_id' => '',
                        'token' => $item_array['code'],
//                        'sku' => $item_array['sku']
                    );
                }
            }
        }
        
        if (isset($data_array['status']) && !is_null($data_array['status'])) {
            $this->resp_status = $data_array['status'];
        }
        
        if (isset($data_array['ordernumber']) && !is_null($data_array['ordernumber'])) {
            $this->resp_ordernumber = is_string($data_array['ordernumber']) ? $data_array['ordernumber'] : '';
        }
        
        if (isset($data_array['sysmessage']) && !is_null($data_array['sysmessage'])) {
            $this->resp_error = $data_array['sysmessage'];
        }
    }
    
    private function parseXML($raw_data, $return_array = array()) {
        $sku = '';
        
        if (isset($return_array['sku']) && !is_null($return_array['sku'])) {
            $sku = $return_array['sku'];
        }
        
        if (isset($raw_data->RequestTransactionResult)) {
            switch ($this->request_method) {
                case $this->CHECK_METHOD:
                    $response_array = explode('|', $raw_data->RequestTransactionResult);
                    $response_ciphertext = array_pop($response_array);
                    $response_plaintext = implode('-', $response_array);
                    
                    $status = array_shift($response_array);
                    $transactioncode = array_shift($response_array);
                    
                    $this->getRSAObj()->loadKey($this->api_public_key, CRYPT_RSA_PUBLIC_FORMAT_XML);
                    
                    if ($this->getRSAObj()->verify($response_plaintext, base64_decode($response_ciphertext)) === true) {
                        if ($status === '1') {
                            $vtc_transactioncode = array_shift($response_array);
                            
                            $return_array = array(
                                'status' => $this->SUCCESS_STATUSFLAG,
                                'transactioncode' => $transactioncode,
                                'vtc_transactioncode' => $vtc_transactioncode,
                                'ordernumber' => $vtc_transactioncode,
                            );
                        } else {
                            $return_array = array(
                                'status' => $status,
                                'transactioncode' => $transactioncode,
                                'sysmessage' => 'Error: ' . $transactioncode . ' : ' . $this->getErrorDesc($status),
                            );
                        }
                    } else {
                        $return_array = array(
                            'status' => 0,
                            'transactioncode' => $transactioncode,
                            'sysmessage' => $this->CHECK_METHOD . ' verifying failed for ' . $response_plaintext . ' with ' . base64_decode($response_ciphertext),
                        );
                    }
                    break;
                case $this->GET_METHOD:
                    $response_getCard_encrypted = base64_decode($raw_data->RequestTransactionResult);

                    // decrypt getCard result
                    $tripleDesKey = md5($this->triple_des_key);
                    $tripleDesKey = str_ireplace('-', '', $tripleDesKey);
                    $tripleDesKey = strtolower(str_ireplace(' ', '+', $tripleDesKey));
                    $tripleDesKey = substr($tripleDesKey, 0, 24);

                    $this->getTripleDesObj()->setKey($tripleDesKey);
                    $response_getCard = $this->getTripleDesObj()->decrypt($response_getCard_encrypted);

                    if ($response_getCard_array = explode('|', $response_getCard)) {
                        $status = $response_getCard_array[0];
                        $card_code = '';
                        $card_serial = '';
                        $expired_date = '';
                        
                        if ($status === '1') {
                            if ($card_info_array = explode(':', $response_getCard_array[2])) {
                                list($card_code, $card_serial, $expired_date) = $card_info_array;
                            }
                            
                            $return_array = array(
                                'status' => $this->SUCCESS_STATUSFLAG,
                                'transactioncode' => $this->request_transactioncode,
                                'ordernumber' => $response_getCard_array[1] . '::' . $card_serial . '::' . $expired_date,  // VTC transaction ID :: Card Serial number
                                'items' => array(
                                    array('code' => sprintf(self::KEY_FORMAT, $card_code, $card_serial, $expired_date), 'sku' => $sku)
                                )
                            );
                        } else {
                            $return_array = array(
                                'status' => $status,
                                'transactioncode' => $this->request_transactioncode,
                                'ordernumber' => $raw_data->RequestTransactionResult,
                                'sysmessage' => 'Error: ' . $response_getCard . ' - ' . $this->getErrorDesc($status),
                            );
                        }
                    } else {
                        $return_array = array(
                            'status' => 0,
                            'transactioncode' => $this->request_transactioncode,
                            'ordernumber' => $raw_data->RequestTransactionResult,
                            'sysmessage' => 'Error on tripleDES decryption',
                        );
                    }
                        
                    break;
            }
        }
        
        return $return_array;
    }
    
    private function generateSignature($plaintext) {
        $this->getRSAObj()->loadKey($this->api_private_key, CRYPT_RSA_PRIVATE_FORMAT_XML);
        $this->getRSAObj()->setSignatureMode(CRYPT_RSA_SIGNATURE_PKCS1);
        return base64_encode($this->getRSAObj()->sign($plaintext));
    }
    
    private function getErrorDesc($code) {
        $return_str = '';
        
        switch ($code) {
            case '0':
                $return_str = 'Transaction not defined';
                break;
            case '-1':
                $return_str = 'System error';
                break;
            case '-55':
                $return_str = 'Account balance is not enough to execute this transaction';
                break;
            case '-99':
                $return_str = 'Unknown error';
                break;
            case '-302':
                $return_str = 'Partner is not exist or temporary stop working';
                break;
            case '-304':
                $return_str = 'Service is not exists or temporary stop working';
                break;
            case '-305':
                $return_str = 'Signature invalid';
                break;
            case '-306':
                $return_str = 'Denominations invalid';
                break;
            case '-307':
                $return_str = 'Topup account is not exists or invalid';
                break;
            case '-308':
                $return_str = 'Request Date invalid';
                break;
            case '-309':
                $return_str = 'Transaction date invalid';
                break;
            case '-310':
                $return_str = 'Out of limit';
                break;
            case '-311':
                $return_str = 'Request Data or Partner Code invalid';
                break;
            case '-315':
                $return_str = 'CommandType null';
                break;
            case '-316':
                $return_str = 'Version null';
                break;
            case '-317':
                $return_str = 'Quantity must greater than 0';
                break;
            case '-318':
                $return_str = 'ServiceCode invalid';
                break;
            case '-320':
                $return_str = 'System interrupt';
                break;
            case '-348':
                $return_str = 'Block account';
                break;
            case '-350':
                $return_str = 'Account not exists';
                break;
            case '-500':
                $return_str = 'Card type is expired or temporary suspend';
                break;
            case '-501':
                $return_str = 'Topup failed';
                break;
            default:
                $return_str = 'Error description not found for error code: ' . $code;
        }
        
        return $return_str;
    }

}

include_once dirname(__FILE__) . DIRECTORY_SEPARATOR . 'config.inc.php';
?>