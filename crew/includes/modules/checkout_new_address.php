<?php
/*
  	$Id: checkout_new_address.php,v 1.4 2005/07/12 10:30:32 weichen Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2003 osCommerce
	
  	Released under the GNU General Public License
*/

if (!isset($process)) $process = false;
?>
<script language="javascript" src="includes/javascript/customer_xmlhttp.js"></script>
<table border="0" width="100%" cellspacing="0" cellpadding="2">
<?
if (ACCOUNT_GENDER == 'true') {
	if (isset($gender)) {
		$male = ($gender == 'm') ? true : false;
		$female = ($gender == 'f') ? true : false;
    } else {
		$male = false;
		$female = false;
    }
?>
  	<tr>
    	<td class="inputLabel"><?=ENTRY_GENDER?></td>
    	<td class="inputField"><? echo tep_draw_radio_field('gender', 'm', $male) . '&nbsp;&nbsp;' . MALE . '&nbsp;&nbsp;' . tep_draw_radio_field('gender', 'f', $female) . '&nbsp;&nbsp;' . FEMALE . '&nbsp;' . (tep_not_null(ENTRY_GENDER_TEXT) ? '<span class="requiredInfo">' . ENTRY_GENDER_TEXT . '</span>': ''); ?></td>
  	</tr>
<?
}
?>
  	<tr>
    	<td class="inputLabel"><?=ENTRY_FIRST_NAME?></td>
    	<td class="inputField"><? echo tep_draw_input_field('firstname') . '&nbsp;' . (tep_not_null(ENTRY_FIRST_NAME_TEXT) ? '<span class="requiredInfo">' . ENTRY_FIRST_NAME_TEXT . '</span>': ''); ?></td>
  	</tr>
 	<tr>
    	<td class="inputLabel"><?=ENTRY_LAST_NAME?></td>
    	<td class="inputField"><? echo tep_draw_input_field('lastname') . '&nbsp;' . (tep_not_null(ENTRY_LAST_NAME_TEXT) ? '<span class="requiredInfo">' . ENTRY_LAST_NAME_TEXT . '</span>': ''); ?></td>
  	</tr>
<?
if (ACCOUNT_COMPANY == 'true') {
?>
  	<tr>
    	<td class="inputLabel"><?=ENTRY_COMPANY?></td>
    	<td class="inputField"><? echo tep_draw_input_field('company') . '&nbsp;' . (tep_not_null(ENTRY_COMPANY_TEXT) ? '<span class="requiredInfo">' . ENTRY_COMPANY_TEXT . '</span>': ''); ?></td>
  	</tr>
<?
}
?>
  	<tr>
    	<td class="inputLabel"><?=ENTRY_STREET_ADDRESS?></td>
    	<td class="inputField"><? echo tep_draw_input_field('street_address') . '&nbsp;' . (tep_not_null(ENTRY_STREET_ADDRESS_TEXT) ? '<span class="requiredInfo">' . ENTRY_STREET_ADDRESS_TEXT . '</span>': ''); ?></td>
  	</tr>
<?
if (ACCOUNT_SUBURB == 'true') {
?>
  	<tr>
    	<td class="inputLabel"><?=ENTRY_SUBURB?></td>
    	<td class="inputField"><? echo tep_draw_input_field('suburb') . '&nbsp;' . (tep_not_null(ENTRY_SUBURB_TEXT) ? '<span class="requiredInfo">' . ENTRY_SUBURB_TEXT . '</span>': ''); ?></td>
  	</tr>
<?
}
?>
  	<tr>
    	<td class="inputLabel"><?=ENTRY_POST_CODE?></td>
    	<td class="inputField"><? echo tep_draw_input_field('postcode') . '&nbsp;' . (tep_not_null(ENTRY_POST_CODE_TEXT) ? '<span class="requiredInfo">' . ENTRY_POST_CODE_TEXT . '</span>': ''); ?></td>
  	</tr>
  	<tr>
    	<td class="inputLabel"><?=ENTRY_CITY?></td>
    	<td class="inputField"><? echo tep_draw_input_field('city') . '&nbsp;' . (tep_not_null(ENTRY_CITY_TEXT) ? '<span class="requiredInfo">' . ENTRY_CITY_TEXT . '</span>': ''); ?></td>
  	</tr>
  	<tr>
    	<td class="inputLabel"><?=ENTRY_COUNTRY?></td>
    	<td class="inputField"><? echo tep_get_country_list('country', $country, 'onChange="refreshDynamicSelectOptions(this, \'state_div\', \'state\', \''.(int)$languages_id.'\', true);"') . '&nbsp;' . (tep_not_null(ENTRY_COUNTRY_TEXT) ? '<span class="requiredInfo">' . ENTRY_COUNTRY_TEXT . '</span>': ''); ?></td>
  	</tr>
<?
if (ACCOUNT_STATE == 'true') {
?>
  	<tr>
    	<td class="inputLabel"><?=ENTRY_STATE?></td>
    	<td class="inputField">
    		<div id="state_div" style="float:left;">
<?
	if ($process == true) {
      	if ($entry_state_has_zones == true) {
        	$zones_array = array( array('id' => '', 'text' => PULL_DOWN_DEFAULT) );
        	$zones_select_sql = "SELECT zone_id, zone_name FROM " . TABLE_ZONES . " WHERE zone_country_id = '" . (int)$country . "' ORDER BY zone_name";
        	$zones_result_sql = tep_db_query($zones_select_sql);
        	while ($zones_row = tep_db_fetch_array($zones_result_sql)) {
          		$zones_array[] = array('id' => $zones_row['zone_id'], 'text' => $zones_row['zone_name']);
        	}
        	echo tep_draw_pull_down_menu('state', $zones_array, $state, 'id="state"');
      	} else {
      		echo tep_draw_input_field('state', $state, 'id="state"');
      	}
	} else {
		echo tep_draw_input_field('state', '', 'id="state"');
    }
	echo '	</div>';
    if (tep_not_null(ENTRY_STATE_TEXT)) echo '&nbsp;<span class="requiredInfo">' . ENTRY_STATE_TEXT;
?>
    	</td>
	</tr>
<?
}
?>
</table>