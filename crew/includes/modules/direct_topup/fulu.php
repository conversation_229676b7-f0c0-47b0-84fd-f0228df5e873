<?php

include_once(DIR_WS_CLASSES . 'direct_topup.php');
include_once(DIR_WS_CLASSES . 'curl.php');
include_once(DIR_WS_CLASSES . 'currencies.php');
include_once(DIR_WS_CLASSES . 'slack_notification.php');

class dtu_fulu extends direct_topup
{
    public $title, $enable;
    private $request_url, $merchant_id, $secret_key, $error_code, $error_msg, $game_id;

    const GAME_TOP_UP = 'G', DATA_TOP_UP = 'D', TALKTIME_TOP_UP = 'T';
    const GET_INFO_METHOD = 'kamenwang.goods.get', GET_SERVER_METHOD = 'kamenwang.goods.template.get', GAME_TOP_UP_METHOD = 'kamenwang.order.add', TALKTIME_TOP_UP_METHOD = 'kamenwang.phoneorder.add', DATA_TOP_UP_METHOD = 'kamenwang.trafficgoods.add', ORDER_CHECKING_METHOD = 'kamenwang.order.get';

    const MIN_MARGIN = 1, LOW_MARGIN = 3;

    const CALLBACK_TEMPLATE = '<?xml version="1.0" encoding="utf-8"?><root><ret><status>%s</status></ret></root>';

    function dtu_fulu()
    {
        $this->title = 'Fulu';
        $this->enable = true;
    }

    function get_title()
    {
        return $this->title;
    }

    function get_enable()
    {
        return $this->enable;
    }

    function draw_admin_input($pID = '')
    {
        $publishers_configuration_array = array();
        $publishers_configuration_select_sql = "SELECT publishers_configuration_key, publishers_configuration_value
												FROM " . TABLE_PUBLISHERS_CONFIGURATION . "
												WHERE publishers_id = '" . (int)$pID . "'";
        $publishers_configuration_result_sql = tep_db_query($publishers_configuration_select_sql);
        while ($publishers_configuration_row = tep_db_fetch_array($publishers_configuration_result_sql)) {
            $publishers_configuration_array[$publishers_configuration_row['publishers_configuration_key']] = $publishers_configuration_row['publishers_configuration_value'];
        }

        $display_html = '	<table border="0" cellspacing="2" cellpadding="2">
								<tr>
									<td class="main">API Url</td>
									<td class="main">' . tep_draw_input_field('configuration[TOP_UP_URL]', (isset($publishers_configuration_array['TOP_UP_URL']) ? $publishers_configuration_array['TOP_UP_URL'] : ''), '', false) . ' 
										' . tep_draw_radio_field('configuration[TOP_UP_URL_FLAG]', '1', (isset($publishers_configuration_array['TOP_UP_URL_FLAG']) && $publishers_configuration_array['TOP_UP_URL_FLAG'] == 1 ? 1 : 0)) . ' On 
										' . tep_draw_radio_field('configuration[TOP_UP_URL_FLAG]', '0', (!isset($publishers_configuration_array['TOP_UP_URL_FLAG']) || $publishers_configuration_array['TOP_UP_URL_FLAG'] == 0 ? 1 : 0)) . ' Off 
									</td>
								</tr>
                                <tr>
									<td class="main">Notify Url</td>
									<td class="main">' . tep_draw_input_field('configuration[NOTIFY_URL]', (isset($publishers_configuration_array['NOTIFY_URL']) ? $publishers_configuration_array['NOTIFY_URL'] : ''), '', false) . ' 
										' . tep_draw_radio_field('configuration[NOTIFY_URL_FLAG]', '1', (isset($publishers_configuration_array['NOTIFY_URL_FLAG']) && $publishers_configuration_array['NOTIFY_URL_FLAG'] == 1 ? 1 : 0)) . ' On 
										' . tep_draw_radio_field('configuration[NOTIFY_URL_FLAG]', '0', (!isset($publishers_configuration_array['NOTIFY_URL_FLAG']) || $publishers_configuration_array['NOTIFY_URL_FLAG'] == 0 ? 1 : 0)) . ' Off 
									</td>
								</tr>
                                <tr>
									<td class="main">' . ENTRY_PUBLISHERS_SECRET_KEY . ':</td>
									<td class="main">' . tep_draw_password_field('configuration[SECRET_KEY]', (isset($publishers_configuration_array['SECRET_KEY']) ? $publishers_configuration_array['SECRET_KEY'] : ''), '', false) . '</td>
								</tr>
							</table>';
        return $display_html;
    }

    function draw_admin_game_input($action, $publishers_games_array)
    {
        switch ($action) {
            /* GAME SETTING */
            case 'new_game':
            case 'edit_game':
                $publishers_game_name = '';
                $publishers_games_daily_limit = 0;
                $publishers_games_today_topped_amount = 0;
                $publishers_games_status = 1;
                if ($action == 'edit_game') {
                    $publishers_game_select_sql = "	SELECT publishers_game, categories_id, publishers_games_remark, 
														publishers_games_pending_message, publishers_games_reloaded_message, 
														publishers_games_failed_message, publishers_games_daily_limit, 
														publishers_games_today_topped_amount, publishers_games_status 
													FROM " . TABLE_PUBLISHERS_GAMES . " AS pg
													WHERE publishers_games_id = '" . (int)$publishers_games_array['publishers_games_id'] . "'
														AND publishers_id = '" . (int)$publishers_games_array['publishers_id'] . "'";
                    $publishers_game_result_sql = tep_db_query($publishers_game_select_sql);
                    if ($publishers_game_row = tep_db_fetch_array($publishers_game_result_sql)) {
                        $publishers_game_name = $publishers_game_row['publishers_game'];
                        $cat_id = $publishers_game_row['categories_id'];
                        $publishers_games_daily_limit = $publishers_game_row['publishers_games_daily_limit'];
                        $publishers_games_today_topped_amount = $publishers_game_row['publishers_games_today_topped_amount'];
                        $publishers_game_remark = $publishers_game_row['publishers_games_remark'];
                        $publishers_games_pending_message = $publishers_game_row['publishers_games_pending_message'];
                        $publishers_games_reloaded_message = $publishers_game_row['publishers_games_reloaded_message'];
                        $publishers_games_failed_message = $publishers_game_row['publishers_games_failed_message'];
                        $publishers_games_status = $publishers_game_row['publishers_games_status'];
                    }

                    $publishers_games_configuration_select_sql = "	SELECT publishers_games_configuration_value, publishers_games_configuration_key 
																	FROM " . TABLE_PUBLISHERS_GAMES_CONFIGURATION . " AS pg
																	WHERE publishers_games_id = '" . (int)$publishers_games_array['publishers_games_id'] . "'";
                    $publishers_games_configuration_result_sql = tep_db_query($publishers_games_configuration_select_sql);
                    while ($publishers_games_configuration_row = tep_db_fetch_array($publishers_games_configuration_result_sql)) {
                        $publishers_games_configuration_array[$publishers_games_configuration_row['publishers_games_configuration_key']] = $publishers_games_configuration_row['publishers_games_configuration_value'];
                    }
                }

                $games_name_array = array();
                if ($action == 'new') {
                    $games_name_array[] = array(
                        'id' => '',
                        'text' => PULL_DOWN_DEFAULT
                    );
                }

                tep_db_connect_og();
                $categories_games_array = [];
                $categories_select_sql = "SELECT DISTINCT(categories_id) FROM category";
                $categories_result_sql = tep_db_query($categories_select_sql, 'db_og_link');
                while ($categories_row = tep_db_fetch_array($categories_result_sql)) {
                    $categories_games_array[] = $categories_row['categories_id'];
                }

                $games_name_select_sql = "	SELECT c.categories_id, cd.categories_name
											FROM " . TABLE_CATEGORIES . " as c
											INNER JOIN " . TABLE_CATEGORIES_DESCRIPTION . " as cd
												ON c.categories_id = cd.categories_id 
											WHERE c.categories_id IN ('" . implode("','", $categories_games_array) . "')
												AND cd.language_id='1'";
                $games_name_result_sql = tep_db_query($games_name_select_sql);
                while ($games_name_row = tep_db_fetch_array($games_name_result_sql)) {
                    $games_name_array[] = array(
                        'id' => $games_name_row['categories_id'],
                        'text' => $games_name_row['categories_name']
                    );
                }

                $display_html = '<table border="0" width="100%" cellspacing="0" cellpadding="2">
					      			<tr>
					        			<td class="formAreaTitle">' . ($action == 'new_game' ? HEADING_TITLE_INSERT : HEADING_TITLE_UPDATE) . '</td>
					      			</tr>
					      			<tr>
					        			<td class="formArea">
					        				<table border="0" cellspacing="2" cellpadding="2" class="main">
					          					<tr>
					            					<td width="160px">' . ENTRY_GAME_NAME . ':</td>
					            					<td width="400px">' . tep_draw_pull_down_menu('sel_categories_games', $games_name_array, $cat_id) . '</td>
													<td>Top-up URL:</td>
													<td>' . tep_draw_input_field('publishers_games_configuration[TOP_UP_URL]', (isset($publishers_games_configuration_array['TOP_UP_URL']) ? $publishers_games_configuration_array['TOP_UP_URL'] : ''), '', false) . '</td>
													<td>' . tep_draw_checkbox_field('publishers_games_configuration[PARENT_TOP_UP_URL_FLAG]', 1, (isset($publishers_games_configuration_array['PARENT_TOP_UP_URL_FLAG']) || (!isset($publishers_games_configuration_array['TOP_UP_URL']) || !tep_not_null($publishers_games_configuration_array['TOP_UP_URL'])) ? 1 : 0), ' id="chk_top_up_url" ') . ' Follow publisher setting</td>
					          					</tr>
					          					<tr>
					            					<td>' . ENTRY_GAME_STATUS . ':</td>
					            					<td>' . tep_draw_radio_field('publishers_games_status', 1, $publishers_games_status) . 'On ' . tep_draw_radio_field('publishers_games_status', 0, (!$publishers_games_status)) . 'Off </td>
					            					<td>Notify URL:</td>
													<td>' . tep_draw_input_field('publishers_games_configuration[NOTIFY_URL]', (isset($publishers_games_configuration_array['NOTIFY_URL']) ? $publishers_games_configuration_array['NOTIFY_URL'] : ''), '', false) . '</td>
													<td>' . tep_draw_checkbox_field('publishers_games_configuration[PARENT_NOTIFY_URL_FLAG]', 1, (isset($publishers_games_configuration_array['PARENT_NOTIFY_URL_FLAG']) || (!isset($publishers_games_configuration_array['NOTIFY_URL']) || !tep_not_null($publishers_games_configuration_array['NOTIFY_URL'])) ? 1 : 0), ' id="chk_top_up_url" ') . ' Follow publisher setting</td>
					          					</tr>
					          					<tr>
					            					<td>' . ENTRY_TOP_UP_DAILY_LIMIT . ':</td>
					            					<td>' . tep_draw_input_field('publishers_top_up_daily_limit', $publishers_games_daily_limit, ' id="publishers_top_up_daily_limit" ', false) . '</td>
					          					</tr>
					          					<tr>
					            					<td>' . ENTRY_DAILY_TOPPED_AMOUNT . ':</td>
					            					<td>' . number_format($publishers_games_today_topped_amount, 2, ".", "") . '
					            					&nbsp;&nbsp;&nbsp;<a href="' . tep_href_link(FILENAME_PUBLISHERS_GAMES, tep_get_all_get_params(array(
                            'pID',
                            'pgID',
                            'flag',
                            'action',
                            'subaction',
                            'product_id'
                        )) . 'pID=' . (int)$_REQUEST['pID'] . '&pgID=' . (int)$_REQUEST['pgID'] . '&action=edit&subaction=reset_amount') . '">' . LINK_RESET_TOP_UP_AMOUNT . '</a></td>
					          					</tr>
					          					<tr>
					            					<td>' . ENTRY_GAME_MAPPING . ':</td>
					            					<td>' . tep_draw_input_field('publishers_game', $publishers_game_name, ' id="publishers_game" ', false) . '</td>
					          					</tr>
					          					<tr>
					            					<td valign="top">' . ENTRY_GAME_PENDING_MESSAGE . ':</td>
					            					<td><textarea name="txt_pending_message" cols="50" rows="5">' . $publishers_games_pending_message . '</textarea></td>
													<td class="main" colspan="3">&nbsp;</td>
					          					</tr>
					          					<tr>
					            					<td valign="top">' . ENTRY_GAME_RELOADED_MESSAGE . ':</td>
					            					<td><textarea name="txt_reloaded_message" cols="50" rows="5">' . $publishers_games_reloaded_message . '</textarea></td>
					          					</tr>
					          					<tr>
					            					<td valign="top">' . ENTRY_GAME_FAILED_MESSAGE . ':</td>
					            					<td><textarea name="txt_failed_message" cols="50" rows="5">' . $publishers_games_failed_message . '</textarea></td>
					          					</tr>
					          					<tr>
					            					<td valign="top">' . ENTRY_GAME_REMARK . ':</td>
					            					<td><textarea name="txt_remark" cols="50" rows="5">' . $publishers_game_remark . '</textarea></td>
					          					</tr>
								      			<tr>
								      				<td>&nbsp;</td>
								        			<td align="left" class="main">' . ($action == 'new_game' ? '<input type="submit" class="inputButton" value="Insert">' : '<input type="submit" class="inputButton" value="Update">') . '&nbsp;<input type="button" class="inputButton" value="Cancel" onclick="location.href=\'' . tep_href_link(FILENAME_PUBLISHERS_GAMES, tep_get_all_get_params(array('action', 'subaction')) . 'action=edit') . '\'"></td>
								      			</tr>
					        				</table>
					        			</td>
					      			</tr>
					      			<tr>
					        			<td>' . tep_draw_separator('pixel_trans.gif', '1', '10') . '</td>
					      			</tr>
					      		</table>';
                break;
        }
        return $display_html;
    }

    function draw_admin_customer_input($action, $publishers_games_array)
    {
        switch ($action) {
            /* GAME SETTING */
            case 'new_product':
            case 'edit_product':
                $languages = tep_get_languages();

                $products_id = '';
                $publishers_game_name = '';

                $publishers_game_select_sql = "	SELECT publishers_game
												FROM " . TABLE_PUBLISHERS_GAMES . " AS pg
												WHERE publishers_games_id = '" . (int)$publishers_games_array['publishers_games_id'] . "'
													AND publishers_id = '" . (int)$publishers_games_array['publishers_id'] . "'";
                $publishers_game_result_sql = tep_db_query($publishers_game_select_sql);
                if ($publishers_game_row = tep_db_fetch_array($publishers_game_result_sql)) {
                    $publishers_game_name = $publishers_game_row['publishers_game'];
                }

                $def_amount = '';
                $def_amount_type = 'currency';
                $def_product_code = '';
                $def_account_flag = 0;
                $def_server_flag = 0;
                $def_character_flag = 0;

                $def_sort_order_account = '50000';

                for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
                    $def_label_account[$languages[$n]['id']] = '';
                }

                $def_sort_order_server = '50000';
                $def_english_label_server = '';
                $def_chinese_label_server = '';

                $def_sort_order_character = '50000';
                $def_english_label_character = '';
                $def_chinese_label_character = '';

                $def_sort_order_account_platform = '50000';
                $def_english_label_account_platform = '';
                $def_chinese_label_account_platform = '';

                $def_sync_publisher_character_flag = 0;

                $top_up_info_array = array();
                if ($action == 'edit_product') {
                    $publishers_products_select_sql = "	SELECT p.products_id, p.products_cat_path, pd.products_name 
														FROM " . TABLE_PUBLISHERS_PRODUCTS . " AS pp 
														INNER JOIN " . TABLE_PRODUCTS . " AS p 
															ON pp.products_id = p.products_id
														INNER JOIN " . TABLE_PRODUCTS_DESCRIPTION . " AS pd 
															ON pp.products_id = pd.products_id
														WHERE pp.publishers_games_id = '" . (int)$publishers_games_array['publishers_games_id'] . "'
															AND pp.products_id = '" . (int)$publishers_games_array['products_id'] . "' 
															AND pd.language_id = 1 
															AND pd.products_name <> ''";
                    $publishers_products_result_sql = tep_db_query($publishers_products_select_sql);
                    $publishers_products_row = tep_db_fetch_array($publishers_products_result_sql);
                    $products_id = $publishers_products_row['products_id'];
                    $products_cat_path_display = $publishers_products_row['products_cat_path'] . ' > ' . $publishers_products_row['products_name'];

                    $top_up_info_select_sql = "	SELECT top_up_info_key, top_up_info_value, top_up_info_type_id, sort_order, top_up_info_display, languages_id 
												FROM " . TABLE_TOP_UP_INFO . " AS tui
												LEFT JOIN " . TABLE_TOP_UP_INFO_LANG . " AS tuil 
													ON tui.top_up_info_id = tuil.top_up_info_id
												WHERE products_id = '" . (int)$publishers_games_array['products_id'] . "'";
                    $top_up_info_result_sql = tep_db_query($top_up_info_select_sql);
                    while ($top_up_info_row = tep_db_fetch_array($top_up_info_result_sql)) {
                        switch ($top_up_info_row['top_up_info_key']) {
                            case 'sync_publisher_character_flag':
                                $def_sync_publisher_character_flag = $top_up_info_row['top_up_info_value'];
                                break;
                            case 'amount':
                                $def_amount = $top_up_info_row['top_up_info_value'];
                                break;
                            case 'amount_type':
                                $def_amount_type = $top_up_info_row['top_up_info_value'];
                                break;
                            case 'product_code':
                                $def_product_code = $top_up_info_row['top_up_info_value'];
                                break;
                            case 'account':
                                $def_label_account[$top_up_info_row['languages_id']] = $top_up_info_row['top_up_info_display'];
                                $def_sort_order_account = $top_up_info_row['sort_order'];
                                $def_account_flag = 1;
                                break;
                            case 'server':
                                $def_sort_order_server = $top_up_info_row['sort_order'];
                                $def_label_server[$top_up_info_row['languages_id']] = $top_up_info_row['top_up_info_display'];
                                $def_server_flag = 1;
                                break;
                            case 'character':
                                $def_sort_order_character = $top_up_info_row['sort_order'];
                                $def_label_character[$top_up_info_row['languages_id']] = $top_up_info_row['top_up_info_display'];
                                $def_character_flag = 1;
                                break;
                            case 'account_platform':
                                $def_sort_order_account_platform = $top_up_info_row['sort_order'];
                                $def_label_account_platform[$top_up_info_row['languages_id']] = $top_up_info_row['top_up_info_display'];
                                $def_account_platform_flag = 1;
                                break;
                        }
                    }
                }

                $display_html = '<table border="0" cellspacing="2" cellpadding="2" class="main" width="100%">
			          					<tr>
			            					<td width="100px" valign="top">' . ENTRY_PRODUCT_ID . ':</td>
			            					<td class="reportRecords">';
                if ($action == 'new_product') {
                    $display_html .= tep_draw_input_field('product_id', $products_id, ' id="games_product_id" onblur="load_product_cat_path(this.value)" ', false) . '&nbsp;(<a href="javascript:openDGDialog(' . tep_href_link(FILENAME_POPUP_PRODUCTS_LIST, 'direct_top_up=1&fname=log_files.php') . ', 600, 250)">Product List</a>)';
                } else {
                    $display_html .= $products_id . tep_draw_hidden_field('product_id', $products_id);
                }
                $display_html .= '				<div id="div_cat_path">' . (tep_not_null($products_cat_path_display) ? $products_cat_path_display : '') . '</div>
											</td>
			    	      				</tr>
			          					<tr>
			            					<td colspan="2">
			            						<fieldset>
			            							<legend>Configuration</legend>
							        				<table border="0" cellspacing="0" cellpadding="2" class="main">
							          					<tr valign="top" class="reportListingOdd">
							            					<td class="reportBoxHeading" width="100px">&nbsp;</td>
							            					<td class="reportBoxHeading" width="150px">&nbsp;</td>
							            					<td class="reportBoxHeading" width="300px" align="center">Description</td>
							            					<td class="reportBoxHeading" width="100px" align="center">' . TABLE_HEADING_SORT_ORDER . '</td>';
                for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
                    $display_html .= '					<td class="reportBoxHeading" width="100px" align="center">' . $languages[$i]['name'] . '</td>';
                }
                $display_html .= '						<td class="reportBoxHeading" width="150px" align="center">' . TABLE_HEADING_SYSTEM_TYPE . '</td>
															<td class="reportBoxHeading" width="100px">Use in API Signature?</td>
							          					</tr>
							          					<tr valign="top" class="reportListingOdd" onmouseover="rowOverEffect(this, \'reportListingRowOver\')" onmouseout="rowOutEffect(this, \'reportListingOdd\')" onclick="rowClicked(this, \'reportListingOdd\')">
							            					<td class="reportRecords">' . ENTRY_AMOUNT_TYPE . ':</td>
							            					<td class="reportRecords">';
                $amount_type_array = array();
                $amount_type_array[] = array('id' => 'currency', 'text' => 'Currency');
                $amount_type_array[] = array('id' => 'point', 'text' => 'Point');
                $display_html .= tep_draw_pull_down_menu('sel_amount_type', $amount_type_array, $def_amount_type, false);
                $display_html .= '						</td>
							            					<td class="reportRecords">\'point\' or \'currency\' for this top-up amount.</td>
							            					<td class="reportRecords" align="center">-</td>';
                for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
                    $display_html .= '					<td class="reportRecords" align="center">-</td>';
                }
                $display_html .= '							<td class="reportRecords" align="center">-</td>
							            					<td class="reportRecords" align="center"></td>
							          					</tr>
							          					<tr valign="top" class="reportListingEven" onmouseover="rowOverEffect(this, \'reportListingRowOver\')" onmouseout="rowOutEffect(this, \'reportListingEven\')" onclick="rowClicked(this, \'reportListingEven\')">
							            					<td class="reportRecords">' . ENTRY_AMOUNT . ':</td>
							            					<td class="reportRecords">' . tep_draw_input_field('txt_amount', $def_amount, ' id="txt_amount" ', false) . '</td>
							            					<td class="reportRecords">Deno face value to be top-up.<br>(total to be top-up = quantity * amount)</td>
							            					<td class="reportRecords" align="center">-</td>';
                for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
                    $display_html .= '						<td class="reportRecords" align="center">-</td>';
                }
                $display_html .= '         					<td class="reportRecords" align="center">-</td>
							            					<td class="reportRecords" align="center"></td>
							          					</tr>
                                                        <tr valign="top" class="reportListingEven" onmouseover="rowOverEffect(this, \'reportListingRowOver\')" onmouseout="rowOutEffect(this, \'reportListingEven\')" onclick="rowClicked(this, \'reportListingEven\')">
							            					<td class="reportRecords">' . ENTRY_PRODUCT_CODE . ':</td>
							            					<td class="reportRecords">' . tep_draw_input_field('txt_product_code', $def_product_code, ' id="txt_product_code" ', false) . '</td>
							            					<td class="reportRecords">Product reference code used at Publisher end</td>
							            					<td class="reportRecords" align="center">-</td>';
                for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
                    $display_html .= '						<td class="reportRecords" align="center">-</td>';
                }
                $display_html .= '         					<td class="reportRecords" align="center">-</td>
							            					<td class="reportRecords" align="center"></td>
							          					</tr>
							          					<tr valign="top" class="reportListingOdd" onmouseover="rowOverEffect(this, \'reportListingRowOver\')" onmouseout="rowOutEffect(this, \'reportListingOdd\')" onclick="rowClicked(this, \'reportListingOdd\')">
							            					<td class="reportRecords">' . ENTRY_ACCOUNT . ':</td>
							            					<td class="reportRecords">-</td>
															<td class="reportRecords">Game\'s account name to be top-up.</td>
							            					<td class="reportRecords" align="center">' . tep_draw_input_field('txt_sort_order_account', $def_sort_order_account, ' id="txt_sort_order_account" size="10" ', false) . '</td>';
                for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
                    $display_html .= '						<td class="reportRecords" align="center">' . tep_draw_input_field('txt_label_account[' . $languages[$i]['id'] . ']', $def_label_account[$languages[$i]['id']], ' id="txt_label_account_' . $languages[$i]['id'] . '" size="10" ', false) . '</td>';
                }
                $display_html .= '								<td class="reportRecords" nowrap>' . tep_draw_radio_field('rd_account', '1', ($def_account_flag ? true : false)) . '&nbsp;Display&nbsp;&nbsp;' . tep_draw_radio_field('rd_account', '0', (!$def_account_flag ? true : false)) . '&nbsp;Disable
							            					</td>
							            					<td class="reportRecords" align="center">Always</td>
							          					</tr>
							          					<tr valign="top" class="reportListingEven" onmouseover="rowOverEffect(this, \'reportListingRowOver\')" onmouseout="rowOutEffect(this, \'reportListingEven\')" onclick="rowClicked(this,\'reportListingEven\')">
							            					<td class="reportRecords">' . ENTRY_SERVER . ':</td>
							            					<td class="reportRecords">-</td>
							            					<td class="reportRecords">Server ID of the selected game.</td>
							            					<td class="reportRecords" align="center">' . tep_draw_input_field('txt_sort_order_server', $def_sort_order_server, ' id="txt_sort_order_server" size="10" ', false) . '</td>';
                for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
                    $display_html .= '						<td class="reportRecords" align="center">' . tep_draw_input_field('txt_label_server[' . $languages[$i]['id'] . ']', $def_label_server[$languages[$i]['id']], ' id="txt_label_server' . $languages[$i]['id'] . '" size="10" ', false) . '</td>';
                }

                $display_html .= '							<td class="reportRecords" nowrap>' . tep_draw_radio_field('rd_server', '1', ($def_server_flag ? true : false)) . '&nbsp;Display&nbsp;&nbsp;' . tep_draw_radio_field('rd_server', '0', (!$def_server_flag ? true : false)) . '&nbsp;Disable
															</td>
							            					<td class="reportRecords" align="center">Always</td>
							          					</tr>
							          					<tr valign="top" class="reportListingOdd" onmouseover="rowOverEffect(this, \'reportListingRowOver\')" onmouseout="rowOutEffect(this, \'reportListingOdd\')" onclick="rowClicked(this, \'reportListingOdd\')">
							            					<td class="reportRecords">' . ENTRY_CHARACTER . ':</td>
							            					<td class="reportRecords">' . tep_draw_checkbox_field('chk_sync_publisher_character', 1, $def_sync_publisher_character_flag, ' id="chk_sync_publisher_character" ') . ' sync with publisher</td>
							            					<td class="reportRecords">Character in game to be top-up.</td>
							            					<td class="reportRecords" align="center">' . tep_draw_input_field('txt_sort_order_character', $def_sort_order_character, ' id="txt_sort_order_character" size="10" ', false) . '</td>';
                for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
                    $display_html .= '						<td class="reportRecords" align="center">' . tep_draw_input_field('txt_label_character[' . $languages[$i]['id'] . ']', $def_label_character[$languages[$i]['id']], ' id="txt_label_character' . $languages[$i]['id'] . '" size="10" ', false) . '</td>';
                }
                $display_html .= '		  					<td class="reportRecords" nowrap>' . tep_draw_radio_field('rd_character', '1', ($def_character_flag ? true : false)) . '&nbsp;Display&nbsp;&nbsp;' . tep_draw_radio_field('rd_character', '0', (!$def_character_flag ? true : false)) . '&nbsp;Disable
							            					</td>
							            					<td class="reportRecords" align="center">Always</td>
							          					</tr>
							          					<tr valign="top" class="reportListingEven" onmouseover="rowOverEffect(this, \'reportListingRowOver\')" onmouseout="rowOutEffect(this, \'reportListingOdd\')" onclick="rowClicked(this, \'reportListingOdd\')">
							            					<td class="reportRecords">' . ENTRY_ACCOUNT_PLATFORM . ':</td>
							            					<td class="reportRecords">&nbsp;</td>
							            					<td class="reportRecords">Game account platform.</td>
							            					<td class="reportRecords" align="center">' . tep_draw_input_field('txt_sort_order_account_platform', $def_sort_order_account_platform, ' id="txt_sort_order_account_platform" size="10" ', false) . '</td>';
                for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
                    $display_html .= '						<td class="reportRecords" align="center">' . tep_draw_input_field('txt_label_account_platform[' . $languages[$i]['id'] . ']', $def_label_account_platform[$languages[$i]['id']], ' id="txt_label_account_platform' . $languages[$i]['id'] . '" size="10" ', false) . '</td>';
                }
                $display_html .= '		  					<td class="reportRecords" nowrap>' . tep_draw_radio_field('rd_account_platform', '1', ($def_account_platform_flag ? true : false)) . '&nbsp;Display&nbsp;&nbsp;' . tep_draw_radio_field('rd_account_platform', '0', (!$def_account_platform_flag ? true : false)) . '&nbsp;Disable
							            					</td>
							            					<td class="reportRecords" align="center">When \'Display\'</td>
							          					</tr>
							          				</table>
								          		</fieldset>
			            					</td>
			          					</tr>
						      			<tr>
						      				<td>&nbsp;</td>
						        			<td align="left" class="main">' . ($action == 'new_product' ? '<input type="submit" class="inputButton" value="Insert">' : '<input type="submit" class="inputButton" value="Update">') . '&nbsp;<input type="button" class="inputButton" value="Cancel" onclick="location.href=\'' . tep_href_link(FILENAME_PUBLISHERS_GAMES, tep_get_all_get_params(array('action', 'subaction')) . 'action=edit') . '\'"></td>
						      			</tr>
			        				</table>';
                break;
        }
        return $display_html;
    }

    function validate_admin_update_input($param, &$msg)
    {
        if (!isset($_REQUEST['configuration']['SECRET_KEY']) || !$_REQUEST['configuration']['SECRET_KEY']) {
            $msg = ERROR_INVALID_PUBLISHERS_SECRET_KEY;
            return false;
        } else {
            //
        }
        return true;
    }

    function validate_admin_game_input($param, &$msg)
    {
        return true;
    }

    function validate_admin_customer_input($param, &$msg)
    {
        return true;
    }

    private function getRequestTimestamp()
    {
        return date("Y-m-d H:i:s");
    }

    private function generatePostFields($params = array(), $secret, $_secretOnly = false)
    {
        ksort($params);
        $postFields = urldecode(http_build_query($params, '', '&'));
        if ($_secretOnly == true) {
            return $this->generateSignature($postFields . $secret);
        }
        $postFields .= '&sign=' . $this->generateSignature($postFields . $secret);
        return $postFields;
    }

    private function generateSignature($str)
    {
        return md5($str);
    }

    public function checkOrderStatus($top_up_id, $orders_products_id)
    {
        $action = self::ORDER_CHECKING_METHOD;
        $params = array(
            'customerid' => $this->merchant_id,
            'method' => $action,
            'timestamp' => $this->getRequestTimeStamp(),
            'customerorderno' => $top_up_id,
            'v' => '1.0'
        );

        $response = $this->processRequest($action, $params, array(
            'publishers_id' => $this->publishers_id,
            'top_up_id' => (int)$top_up_id,
            'orders_products_id' => (int)$orders_products_id
        ));
        return $response;
    }

    public function validateCallBackRequest($data)
    {
        $signature_source = array();
        $sign = '';
        foreach ($data as $key => $value) {
            if (strtolower($key) != 'sign') {
                $signature_source[strtolower($key)] = $value;
            } else {
                $sign = $value;
            }
        }
        $self_sign = $this->generatePostFields($signature_source, $this->secret_key, true);
        if ($sign == $self_sign) {
            return true;
        }
        return false;
    }

    public function processCallBackRequest($data)
    {
        $top_up_array = array();
        $_res = false;
        header("Content-type: text/xml");
        $orders_top_up_id = (isset($data['CustomerOrderNo']) ? $data['CustomerOrderNo'] : null);
        if (!empty($orders_top_up_id)) {
            $orders_top_up_sql = "SELECT otu.top_up_id, otu.publishers_ref_id, otu.publishers_id, otu.orders_products_id
                                  FROM " . TABLE_ORDERS_TOP_UP . " AS otu
                                  WHERE otu.top_up_id = " . (int)$orders_top_up_id . "
                                    LIMIT 1";
            $orders_top_up_result = tep_db_query($orders_top_up_sql);
            if ($orders_top_up_row = tep_db_fetch_array($orders_top_up_result)) {
                $publishers_configuration_array = array();
                $publishers_configuration_select_sql = "SELECT publishers_configuration_key, publishers_configuration_value
												FROM " . TABLE_PUBLISHERS_CONFIGURATION . "
												WHERE publishers_id = '" . (int)$orders_top_up_row['publishers_id'] . "'";
                $publishers_configuration_result_sql = tep_db_query($publishers_configuration_select_sql);
                while ($publishers_configuration_row = tep_db_fetch_array($publishers_configuration_result_sql)) {
                    $publishers_configuration_array[$publishers_configuration_row['publishers_configuration_key']] = $publishers_configuration_row['publishers_configuration_value'];
                }
                $this->publishers($orders_top_up_row['publishers_id']);
                $this->request_url = $publishers_configuration_array['TOP_UP_URL'];
                $this->merchant_id = $publishers_configuration_array['OGM_MERCHANT_ID'];
                $this->secret_key = $publishers_configuration_array['SECRET_KEY'];
                $top_up_array['orders_products_id'] = $orders_top_up_row['orders_products_id'];
                $top_up_array['top_up_id'] = $orders_top_up_id;
                if ($this->validateCallBackRequest($data)) {
                    $check_order_status = $this->checkOrderStatus($orders_top_up_id, $top_up_array['orders_products_id']);
                    if ($check_order_status['Status'] == '成功') {
                        $orders_top_up_data_sql = array(
                            'publishers_response_time' => 'now()',
                            'top_up_timestamp' => 'now()'
                        );
                        $orders_top_up_remark_data_sql = array(
                            'top_up_id' => (int)$orders_top_up_id,
                            'data_added' => 'now()',
                            'remark' => 'Top-up: Reloaded ' . $check_order_status['StatusMsg']
                        );
                        $orders_top_up_data_sql['top_up_status'] = '3';
                        tep_db_perform(TABLE_ORDERS_TOP_UP_REMARK, $orders_top_up_remark_data_sql);
                        tep_db_perform(TABLE_ORDERS_TOP_UP, $orders_top_up_data_sql, 'update', " top_up_id = '" . (int)$orders_top_up_id . "' ");
                        $top_up_array['status'] = 'reloaded';
                        $top_up_array['publisher_ref_id'] = $check_order_status['OrderId'];
                        $_res = true;
                    } elseif ($check_order_status['Status'] == '失败') {
                        $orders_top_up_data_sql = array(
                            'publishers_response_time' => 'now()',
                            'top_up_timestamp' => 'now()'
                        );
                        $orders_top_up_remark_data_sql = array(
                            'top_up_id' => (int)$orders_top_up_id,
                            'data_added' => 'now()',
                            'remark' => 'Top-up: Failed ' . $check_order_status['StatusMsg']
                        );
                        $orders_top_up_data_sql['top_up_status'] = '10';
                        tep_db_perform(TABLE_ORDERS_TOP_UP_REMARK, $orders_top_up_remark_data_sql);
                        tep_db_perform(TABLE_ORDERS_TOP_UP, $orders_top_up_data_sql, 'update', " top_up_id = '" . (int)$orders_top_up_id . "' ");
                        $top_up_array['status'] = 'failed';
                        // Use to inform Fulu we receive the order status
                        $_res = true;
                        $this->reportError($check_order_status, 'Direct Top Up Failed');
                    } else {
                        $this->reportError($check_order_status, 'Order Status Checking Failure');
                    }
                }
            }
        }
        echo sprintf(self::CALLBACK_TEMPLATE, ($_res ? 'True' : 'False'));
        return $top_up_array;
    }

    private function processRequest($action, $params = array(), $data = array())
    {
        $request_log = 'url: ' . $this->request_url . "\n";
        ob_start();
        echo "<pre>";
        print_r($params);
        $request_log .= ob_get_contents();
        ob_end_clean();

        $api_log_obj = new direct_topup_api_log($action, $request_log, $data);
        $curl_obj = new curl();

        $postFields = $this->generatePostFields($params, $this->secret_key);
        $response = $curl_obj->curl_request('POST', $this->request_url, array(), $postFields);
        $response = $this->parse_xml_to_array($response);
        $api_log_obj->end_log('2000', @json_encode($response, JSON_UNESCAPED_UNICODE), array('publishers_id' => $this->publishers_id));
        if ($this->validateResponse($response, $curl_obj->get_error())) {
            return $response;
        } else {
            $this->reportError(array(
                'request' => $params,
                'response' => $response,
                'error' => array('code' => $this->error_code, 'msg' => $this->error_msg)
            ));
            return false;
        }
    }

    private function validateResponse($response, $curl_error)
    {
        if (empty($curl_error)) {
            if (isset($response['MessageCode'])) {
                $this->error_code = $response['MessageCode'];
                $this->error_msg = $response['MessageInfo'];
                return false;
            } else {
                return true;
            }
        }
        $this->error_code = $curl_error['error_code'];
        $this->error_msg = $curl_error['error_message'];
        return false;
    }

    public function getGameTemplateId($item_id)
    {
        $action = self::GET_INFO_METHOD;
        $params = array(
            'customerid' => $this->merchant_id,
            'method' => $action,
            'timestamp' => $this->getRequestTimeStamp(),
            'goodsid' => $item_id,
            'v' => '1.0'
        );

        $response = $this->processRequest($action, $params);
        return $response;
    }

    public function getUnitPrice($pid)
    {
        $unit_price_select_sql = "   SELECT products_price, products_base_currency
                                            FROM products
                                            WHERE products_id = '" . tep_db_input($pid) . "'";
        $unit_price_result_sql = tep_db_query($unit_price_select_sql);
        $unit_price_row = tep_db_fetch_array($unit_price_result_sql);

        if ($unit_price_row['products_price'] == 'CNY') {
            $unit_price = $unit_price_row['products_price'];
        } else {
            $currency_obj = new currencies();
            $unit_price = $currency_obj->advance_currency_conversion($unit_price_row['products_price'], $unit_price_row['products_base_currency'], 'CNY');
        }
        return $unit_price;
    }

    public function calculateMargin($orders_id, $item_id, $products_id, $unit_price, $qty)
    {
        $action = self::GET_INFO_METHOD;
        $params = array(
            'customerid' => $this->merchant_id,
            'method' => $action,
            'timestamp' => $this->getRequestTimeStamp(),
            'goodsid' => $item_id,
            'v' => '1.0'
        );
        $response = $this->processRequest($action, $params);

        if ($response) {
            $purchase_price = $response['GoodsInfo']['PurchasePrice'] * $qty;
            $margin = (($unit_price - $purchase_price) / $unit_price) * 100;
            if ($margin > self::MIN_MARGIN) {
                if ($margin <= self::LOW_MARGIN) {
                    $this->lowMarginReport($orders_id, $products_id, $item_id, $unit_price, $purchase_price, $margin, 'LOW_MARGIN');
                }
                return true;
            } else {
                $this->lowMarginReport($orders_id, $products_id, $item_id, $unit_price, $purchase_price, $margin, 'MIN_MARGIN');
                return false;
            }
            return true;
        }
        return false;
    }


    /**
     * @param $orders_products_id
     * @param $publisher_id
     * @param $top_up_id
     * @param $amount_type (Currency / Point)
     * @param $amount
     * @param $quantity
     * @param $account
     * @param $character
     * @param $game (Publishers Game Mapping Column)
     * @param string $server
     * @param string $account_platform
     * @param array $product_array => some useful data ['pid' => products_id, top_up_info => ['publishers_games_id' => 'publishers_games_id']]
     * (Sample Data converted to JSON below)
     * {"pid":"118913","topup_info":{"amount_type":{"top_up_info_id":"840","products_id":"118913","top_up_info_title":"Amount Type","top_up_info_key":"amount_type","top_up_info_description":"Top-up amount type","top_up_info_value":"currency","top_up_info_type_id":"1","sort_order":"50000","last_modified":null,"date_added":"2018-09-13 12:47:46","last_modified_by":"0","use_function":"","set_function":""},"amount":{"top_up_info_id":"841","products_id":"118913","top_up_info_title":"Base Amount","top_up_info_key":"amount","top_up_info_description":"Top-up base Amount","top_up_info_value":"1","top_up_info_type_id":"1","sort_order":"50000","last_modified":null,"date_added":"2018-09-13 12:47:46","last_modified_by":"0","use_function":"","set_function":""},"product_code":{"top_up_info_id":"842","products_id":"118913","top_up_info_title":"Product Code","top_up_info_key":"product_code","top_up_info_description":"Product reference from Publisher","top_up_info_value":"1222586","top_up_info_type_id":"1","sort_order":"50000","last_modified":null,"date_added":"2018-09-13 12:47:46","last_modified_by":"0","use_function":"","set_function":""},"sync_publisher_character_flag":{"top_up_info_id":"843","products_id":"118913","top_up_info_title":"Sync publisher character","top_up_info_key":"sync_publisher_character_flag","top_up_info_description":"Sync publisher character","top_up_info_value":"1","top_up_info_type_id":"1","sort_order":"50000","last_modified":null,"date_added":"2018-09-13 12:47:46","last_modified_by":"0","use_function":"","set_function":""},"account":{"top_up_info_id":"844","products_id":"118913","top_up_info_title":"Customer's Account","top_up_info_key":"account","top_up_info_description":"Customer's Account","top_up_info_value":"","top_up_info_type_id":"2","sort_order":"50000","last_modified":null,"date_added":"2018-09-13 12:47:46","last_modified_by":"0","use_function":"","set_function":""},"retype_account_flag":{"top_up_info_id":"845","products_id":"118913","top_up_info_title":"Retype Account","top_up_info_key":"retype_account_flag","top_up_info_description":"Retype Account","top_up_info_value":"0","top_up_info_type_id":"1","sort_order":"50000","last_modified":null,"date_added":"2018-09-13 12:47:46","last_modified_by":"0","use_function":"","set_function":""},"server":{"top_up_info_id":"846","products_id":"118913","top_up_info_title":"Server","top_up_info_key":"server","top_up_info_description":"Customer's game servers","top_up_info_value":"","top_up_info_type_id":"2","sort_order":"50000","last_modified":null,"date_added":"2018-09-13 12:47:46","last_modified_by":"0","use_function":"","set_function":""},"character":{"top_up_info_id":"847","products_id":"118913","top_up_info_title":"Customer's character","top_up_info_key":"character","top_up_info_description":"Customer's character","top_up_info_value":"","top_up_info_type_id":"2","sort_order":"50000","last_modified":null,"date_added":"2018-09-13 12:47:46","last_modified_by":"0","use_function":"","set_function":""},"account_platform":{"top_up_info_id":"848","products_id":"118913","top_up_info_title":"Account Platform","top_up_info_key":"account_platform","top_up_info_description":"Account Platform","top_up_info_value":"","top_up_info_type_id":"2","sort_order":"50000","last_modified":null,"date_added":"2018-09-13 16:01:46","last_modified_by":"0","use_function":"","set_function":""}},"publisher_games_id":"63"}
     * @return array|mixed
     */
    public function do_top_up(
        $orders_products_id,
        $publisher_id,
        $top_up_id,
        $amount_type,
        $amount,
        $quantity,
        $account,
        $character,
        $game,
        $server = '1',
        $account_platform = '',
        $product_array = array()
    ) {
        $curl_response_array = array();

        $orders_top_up_data_sql = array(
            'publishers_response_time' => 'now()',
            'top_up_timestamp' => tep_db_prepare_input(time())
        );
        $orders_top_up_remark_data_sql = array(
            'top_up_id' => (int)$top_up_id,
            'data_added' => 'now()'
        );

        $this->publishers($publisher_id);
        $get_publishers_conf_array = $this->get_publishers_conf();
        $this->game_id = $product_array['publisher_games_id'];
        $this->merchant_id = $get_publishers_conf_array['OGM_MERCHANT_ID']['publishers_configuration_value'];
        $this->request_url = $get_publishers_conf_array['TOP_UP_URL']['publishers_configuration_value'];
        $notify_url = (isset($get_publishers_conf_array['NOTIFY_URL']['publishers_configuration_value']) ? $get_publishers_conf_array['NOTIFY_URL']['publishers_configuration_value'] : false);
        $this->secret_key = $get_publishers_conf_array['SECRET_KEY']['publishers_configuration_value'];
        $publishers_ref_id = $this->getOrderTopUpPublishersRefId($top_up_id);
        // Prevent duplicate order submission
        if (!empty($publishers_ref_id)) {
            $curl_response_array['top_up_status'] = 'pending';
            $curl_response_array['result_code'] = '1200';
            $curl_response_array['publisher_ref_id'] = $publishers_ref_id;
            return $curl_response_array;
        } else {
            $products_id = $product_array['pid'];
            $unit_price = $this->getUnitPrice($products_id);
            $item_id = $product_array['topup_info']['product_code']['top_up_info_value'];
            $selling_price = number_format($unit_price * $quantity, 2, '.', '');
            // Get deno by actual purchased quantity * deno set in publishers product page
            $deno = $quantity * $amount;
            $orders_id = $this->getOrdersIdByOrdersProductsId($orders_products_id);
            $product_code = $product_array['topup_info']['product_code']['top_up_info_value'];

            list($item_id, $product_type) = explode("_", $product_code);
            $margin_checking = $this->calculateMargin($orders_id, $item_id, $products_id, $selling_price, $deno);
            if ($margin_checking) {
                // Region and server default is -1
                switch ($product_type) {
                    case self::GAME_TOP_UP:
                        $action = self::GAME_TOP_UP_METHOD;
                        $params = array(
                            'customerid' => $this->merchant_id,
                            'method' => $action,
                            'timestamp' => $this->getRequestTimeStamp(),
                            'v' => '1.0',
                            'customerorderno' => $top_up_id,
                            'productid' => $item_id,
                            'buynum' => $deno,
                            'chargeaccount' => $account
                        );
                        $fields_mapping = $this->get_publishers_games_conf($this->game_id, 'ACCOUNT_FIELD_MAPPING');

                        if (!empty($fields_mapping)) {
                            $fields_mapping = json_decode($fields_mapping, 1);
                            foreach ($fields_mapping as $field => $value) {
                                if ($field['server'] == 'ChargeGameRegionServer' || $field['server'] === 'ChargeGameRegion' || $field['server'] === 'ChargeRegionServer') {
                                    if (!empty($server) && $server != 0) {
                                        if ($field['server'] === 'ChargeRegionServer') {
                                            $params['chargeregion'] = $server;
                                        } else {
                                            $params['chargegame'] = $server;
                                        }
                                        if (!empty($character)) {
                                            $data = explode($character, '..');
                                            if (isset($data[0]) && !empty($data[0])) {
                                                if ($field['server'] === 'ChargeRegionServer') {
                                                    $params['chargeserver'] = $data[0];
                                                } else {
                                                    $params['chargeregion'] = $data[0];
                                                }
                                            }
                                            if (isset($data[1]) && !empty($data[1])) {
                                                $params['chargeserver'] = $server;
                                            }
                                        }
                                    }
                                } elseif ($field['server'] && !empty($server) && $server != 0) {
                                    $params[$field['server']] = $server;
                                }

                                if ($field['platform'] && !empty($account_platform) && $account_platform != 0) {
                                    $params[$field['platform']] = $account_platform;
                                }
                            }
                        }
                        break;
                    case self::DATA_TOP_UP:
                    case self::TALKTIME_TOP_UP:
                        $action = ($product_type == self::DATA_TOP_UP ? self::DATA_TOP_UP_METHOD : self::TALKTIME_TOP_UP_METHOD);
                        $params = array(
                            'customerid' => $this->merchant_id,
                            'method' => $action,
                            'timestamp' => $this->getRequestTimeStamp(),
                            'v' => '1.0',
                            'customerorderno' => $top_up_id,
                            'productid' => $item_id,
                            'chargeparvalue' => $deno,
                            'chargephone' => $account
                        );
                        break;
                }
                if (!empty($notify_url)) {
                    $params['notifyurl'] = $notify_url;
                }
                $response = $this->processRequest($action, $params, array(
                    'publishers_id' => $this->publishers_id,
                    'top_up_id' => (int)$top_up_id,
                    'orders_products_id' => (int)$orders_products_id
                ));

                if ($response) {
                    if (isset($response['StatusMsg']) && $response['StatusMsg'] === '创建订单成功') {
                        //Status: Pending, Result Code : Pending Publishers Response
                        $orders_top_up_data_sql['top_up_status'] = '1';
                        $orders_top_up_data_sql['publishers_ref_id'] = tep_db_prepare_input($response['OrderId']);
                        $orders_top_up_data_sql['game'] = $game;
                        $orders_top_up_data_sql['server'] = $server;
                        $orders_top_up_data_sql['account'] = $account;
                        $orders_top_up_data_sql['`character`'] = $character;
                        $orders_top_up_data_sql['top_up_response_info'] = tep_db_prepare_input(json_encode($response, JSON_UNESCAPED_UNICODE));
                        $orders_top_up_remark_data_sql['remark'] = 'Top-up: Pending';

                        $curl_response_array['top_up_status'] = 'pending';
                        $curl_response_array['result_code'] = '1200';
                        $curl_response_array['publisher_ref_id'] = $response['OrderId'];
                    }
                }
                if (empty($curl_response_array)) {
                    //Status Fail, Result Code : Incomplete Request
                    $orders_top_up_data_sql['top_up_status'] = '10';
                    $orders_top_up_data_sql['result_code'] = '1001';
                    $orders_top_up_remark_data_sql['remark'] = 'Top-up: Failed' . $this->error_msg;

                    $curl_response_array['top_up_status'] = 'failed';
                    $curl_response_array['result_code'] = '1001';
                    $curl_response_array['error_code'] = $this->error_code;
                    $curl_response_array['error_msg'] = $this->error_msg;
                }
            } else {
                $result_code = '1510';
                $this->error_msg = $this->get_result_code_description($result_code);
                // Status Fail, Results Code : Margin Block
                $orders_top_up_data_sql['top_up_status'] = '10';
                $orders_top_up_data_sql['result_code'] = $result_code;
                $orders_top_up_remark_data_sql['remark'] = 'Top-up: Failed ' . $this->error_msg;

                $curl_response_array['top_up_status'] = 'failed';
                $curl_response_array['result_code'] = $result_code;
                $curl_response_array['error_msg'] = $this->error_msg;

                $this->reportError($curl_response_array);
            }
        }
        tep_db_perform(TABLE_ORDERS_TOP_UP_REMARK, $orders_top_up_remark_data_sql);
        tep_db_perform(TABLE_ORDERS_TOP_UP, $orders_top_up_data_sql, 'update', " top_up_id = '" . (int)$top_up_id . "' ");

        return $curl_response_array;
    }

    private function getOrderTopUpPublishersRefId($top_up_id)
    {
        $orders_top_up_select_sql = "SELECT otu.publishers_ref_id
									FROM " . TABLE_ORDERS_TOP_UP . " AS otu
									WHERE otu.top_up_id = $top_up_id
									LIMIT 1";
        $orders_top_up_result_sql = tep_db_query($orders_top_up_select_sql);
        if ($orders_top_up_row = tep_db_fetch_array($orders_top_up_result_sql)) {
            return $orders_top_up_row['publishers_ref_id'];
        }
    }

    public function get_server_list($publisher_id, $game_info, $get_publishers_conf_array)
    {
        if (tep_not_null($game_info)) {
            if (!isset($get_publishers_conf_array['TOP_UP_URL_FLAG']['publishers_configuration_value']) || !$get_publishers_conf_array['TOP_UP_URL_FLAG']['publishers_configuration_value']) {
                return true;
            }
            $url = '';
            if (!tep_not_null($url)) {
                $this->request_url = $get_publishers_conf_array['TOP_UP_URL']['publishers_configuration_value'];
            }
            if (!tep_not_null($this->request_url)) {
                return true;
            }
            $this->publishers($game_info['publishers_id']);
            $this->game_id = $game_info['publishers_games_id'];
            $this->merchant_id = $get_publishers_conf_array['OGM_MERCHANT_ID']['publishers_configuration_value'];
            $this->secret_key = $get_publishers_conf_array['SECRET_KEY']['publishers_configuration_value'];
            list($product_code, $product_type) = explode('_', $game_info['publishers_game']);

            switch ($product_type) {
                case self::GAME_TOP_UP:
                    $templateId = $this->getGameTemplateId($product_code);
                    if ($templateId) {
                        $action = self::GET_SERVER_METHOD;
                        $params = array(
                            'customerid' => $this->merchant_id,
                            'method' => $action,
                            'timestamp' => $this->getRequestTimeStamp(),
                            'templateguid' => $templateId['GoodsInfo']['TemplateGuid'],
                            'v' => '1.0'
                        );
                        $response = $this->processRequest($action, $params);
                        $this->processTemplateData($response);
                    }
                    break;
                // No Necessary to get server for data / talktime topup
                case self::TALKTIME_TOP_UP:
                case self::DATA_TOP_UP:
                    break;
            }
        }
        return true;
    }

    private function getAttributes($item)
    {
        if (isset($item['@attributes'])) {
            return $item['@attributes']['name'];
        } else {
            return $item;
        }
    }

    private function isAssoc(array $arr)
    {
        if (array() === $arr) {
            return false;
        }
        ksort($arr);
        return array_keys($arr) !== range(0, count($arr) - 1);
    }

    private function processTemplateData($data)
    {
        $data_mapping = array();
        $character_list_in_used = false;
        $types = [];
        $server_list = [];
        $char_list = [];
        $platform_list = [];

        foreach ($data['Types']['Type'] as $type) {
            if ($type !== 'ChargeAccount' && $type !== 'ChargeNum') {
                $types[] = $type;
            }
        }
        if (!count($types)) {
            return true;
        }
        foreach ($types as $index => $type) {
            switch ($type) {
                case 'ChargeGameRegionServer':
                case 'ChargeGameRegion':
                    $character_list_in_used = true;
                    unset($types[$index]);
                    $data_mapping['server'] = $type;

                    if (!isset($data['Games']['ChargeGame']['@attributes']) && !isset($data['Games']['ChargeGame']['@attributes']['name'])) {
                        foreach ($data['Games']['ChargeGame'] as $g_index => $game) {
                            $game_name = $this->getAttributes($game);
                            $server_list[$game_name] = $game_name;
                            if (isset($game['ChargeRegion'])) {
                                if (!$this->isAssoc($game['ChargeRegion'])) {
                                    foreach ($game['ChargeRegion'] as $r_index => $region) {
                                        $region_name = $this->getAttributes($region);
                                        if (isset($region['ChargeServer'])) {
                                            if (is_array($region['ChargeServer'])) {
                                                foreach ($region['ChargeServer'] as $s_index => $server) {
                                                    $value = $region_name . ".." . $server;
                                                    $char_list[$game_name][$value] = $value;
                                                }
                                            } else {
                                                $value = $region_name . ".." . $region['ChargeServer'];
                                                $char_list[$game_name][$value] = $value;
                                            }
                                        } else {
                                            $char_list[$game_name][$region_name] = $region_name;
                                        }
                                    }
                                } else {
                                    $region_name = $this->getAttributes($game['ChargeRegion']);
                                    $region = $game['ChargeRegion'];
                                    if (isset($region['ChargeServer'])) {
                                        if (!$this->isAssoc($region['ChargeServer'])) {
                                            foreach ($region['ChargeServer'] as $s_index => $server) {
                                                $value = $region_name . ".." . $server;
                                                $char_list[$game_name][$value] = $value;
                                            }
                                        } else {
                                            $value = $region_name . ".." . $region['ChargeServer'];
                                            $char_list[$game_name][$value] = $value;
                                        }
                                    } else {
                                        $char_list[$game_name][$region_name] = $region_name;
                                    }
                                }
                            }
                        }
                    } else {
                        $game = $data['Games']['ChargeGame'];
                        $game_name = $this->getAttributes($game);
                        $server_list[$game_name] = $game_name;
                        if (isset($game['ChargeRegion'])) {
                            if (is_array($game['ChargeRegion'])) {
                                foreach ($game['ChargeRegion'] as $r_index => $region) {
                                    $region_name = $this->getAttributes($region);
                                    if (isset($region['ChargeServer'])) {
                                        if (is_array($region['ChargeServer'])) {
                                            foreach ($region['ChargeServer'] as $s_index => $server) {
                                                $value = $region_name . ".." . $server;
                                                $char_list[$game_name][$value] = $value;
                                            }
                                        } else {
                                            $char_list[$game_name][$region['ChargeServer']] = $region['ChargeServer'];
                                        }
                                    } else {
                                        $char_list[$game_name][$region_name] = $region_name;
                                    }
                                }
                            } else {
                                $region_name = $this->getAttributes($game['ChargeRegion']);
                                $char_list[$game_name][$region_name] = $region_name;
                            }
                        }
                    }
                    break;
                case 'ChargeRegionServer':
                    $character_list_in_used = true;
                    unset($types[$index]);
                    $data_mapping['server'] = $type;
                    if (!$this->isAssoc($data['Games']['ChargeRegion'])) {
                        foreach ($data['Games']['ChargeRegion'] as $g_index => $game) {
                            $game_name = $this->getAttributes($game);
                            $server_list[$game_name] = $game_name;
                            if (isset($game['ChargeServer'])) {
                                foreach ($game['ChargeServer'] as $s_index => $server) {
                                    $value = $this->getAttributes($server);
                                    $char_list[$game_name][] = $value;
                                }
                            }
                        }
                    } else {
                        $game = $data['Games']['ChargeRegion'];
                        $game_name = $this->getAttributes($game);
                        $server_list[$game_name] = $game_name;
                        if (isset($game['ChargeServer'])) {
                            foreach ($game['ChargeServer'] as $s_index => $server) {
                                $value = $this->getAttributes($server);
                                $char_list[$game_name][$value] = $value;
                            }
                        }
                    }
                    break;
            }
        }
        if ($character_list_in_used && count($types) > 1) {
            $this->reportError($data);
        } elseif ($character_list_in_used && count($types)) {
            $data_mapping['platform'] = $types[0];
            $_data = explode("|", $data[$types[0]]);
            foreach ($_data as $server) {
                $platform_list[$server] = $server;
            }
        } elseif ($character_list_in_used) {

        } elseif (count($types) === 1) {
            $data_mapping['server'] = $types[0];
            $_data = explode("|", $data[$types[0]]);
            foreach ($_data as $server) {
                $server_list[$server] = $server;
            }
        } elseif (count($types) === 2) {
            $data_mapping['server'] = $types[0];
            $_data = explode("|", $data[$types[0]]);
            foreach ($_data as $server) {
                $server_list[$server] = $server;
            }
            $data_mapping['platform'] = $types[1];
            $_data2 = explode("|", $data[$types[1]]);
            foreach ($_data2 as $platform) {
                $platform_list[$platform] = $platform;
            }
        } else {
            $this->reportError($data);
        }


        $direct_topup_obj = new direct_topup();
        if (!empty($data_mapping)) {
            $this->set_publishers_game_configuration('ACCOUNT_FIELD_MAPPING', $data_mapping);
        }
        if (!empty($server_list)) {
            $direct_topup_obj->set_publishers_game_server($this->game_id, $server_list);
        }
        if (!empty($char_list)) {
            $this->set_publishers_game_configuration('ACCOUNT_CHARACTER_LIST', $char_list);
        }
        if (!empty($platform_list)) {
            $this->set_publishers_game_configuration('ACCOUNT_PLATFORM_LIST', $platform_list);
        }
    }

    private function set_publishers_game_configuration($key, $data)
    {
        $publishers_games_configuration_data_sql = array();
        $publishers_games_configuration_data_sql['publishers_games_configuration_value'] = json_encode($data, JSON_UNESCAPED_UNICODE);

        $publishers_games_conf_select_sql = "	SELECT publishers_games_configuration_id
                                                        FROM " . TABLE_PUBLISHERS_GAMES_CONFIGURATION . "
                                                        WHERE publishers_games_id = '" . $this->game_id . "'
                                                            AND publishers_games_configuration_key = '" . $key . "'";
        $publishers_games_conf_result = tep_db_query($publishers_games_conf_select_sql);
        if ($publishers_games_conf_row = tep_db_fetch_array($publishers_games_conf_result)) {
            $publishers_games_configuration_data_sql['last_modified'] = 'now()';
            tep_db_perform(TABLE_PUBLISHERS_GAMES_CONFIGURATION, $publishers_games_configuration_data_sql, 'update', " publishers_games_configuration_id = '" . $publishers_games_conf_row['publishers_games_configuration_id'] . "' ");
        } else {
            $publishers_games_configuration_data_sql['publishers_games_configuration_key'] = tep_db_prepare_input($key);
            $publishers_games_configuration_data_sql['publishers_games_id'] = $this->game_id;
            $publishers_games_configuration_data_sql['sort_order'] = '50000';
            $publishers_games_configuration_data_sql['date_added'] = 'now()';
            tep_db_perform(TABLE_PUBLISHERS_GAMES_CONFIGURATION, $publishers_games_configuration_data_sql);
        }
    }

    public function save_top_up_list($publisher_id, $start_time, $end_time = '', $filename = '')
    {
        return;
    }

    public function parse_xml_to_array($raw_xml)
    {
        $rx = '/\<\?xml.*encoding=[\'"](.*?)[\'"].*?>/m';

        if (preg_match($rx, $raw_xml, $m)) {
            $encoding = strtoupper($m[1]);
        } else {
            $encoding = "UTF-8";
        }

        if ($encoding == "UTF-8" || $encoding == "US-ASCII" || $encoding == "ISO-8859-1") {
            $parser = xml_parser_create($encoding);
        } else {
            if (function_exists('mb_convert_encoding')) {
                $encoded_source = @mb_convert_encoding($raw_xml, "UTF-8", $encoding);
            }

            if ($encoded_source != null) {
                $raw_xml = str_replace($m[0], '<?xml version="1.0" encoding="utf-8"?>', $encoded_source);
            }
        }

        $xml_object = simplexml_load_string($raw_xml);
        $xml_array = json_decode(@json_encode($xml_object), 1);

        return $xml_array;
    }

    public function getOrdersIdByOrdersProductsId($opId)
    {
        $orders_id = 0;
        $op_sql = "SELECT orders_id
                    FROM " . TABLE_ORDERS_PRODUCTS . "
                    WHERE orders_products_id = '" . $opId . "'
                    LIMIT 1";
        $op_results = tep_db_query($op_sql);
        if ($op_row = tep_db_fetch_array($op_results)) {
            $orders_id = $op_row['orders_id'];
        }

        return $orders_id;
    }

    public function reportError($response_data, $ext_subject = '')
    {
        $slack = new slack_notification();
        $data = json_encode(array(
            'text' => '[OG Crew] ' . $this->title . ' API Error - ' . date("F j, Y H:i") . ' from ' . (isset($_SERVER['SERVER_NAME']) && !empty($_SERVER['SERVER_NAME']) ? $_SERVER['SERVER_NAME'] : '-'),
            'attachments' => array(
                array(
                    'color' => 'warning',
                    'text' => $ext_subject . "\n\n" . json_encode($response_data)
                )
            )
        ));
        $slack->send(SLACK_WEBHOOK_DEV_DEBUG, $data);
    }


    public function lowMarginReport($orders_id, $products_id, $item_id, $unit_price, $purchase_price, $margin, $type)
    {
        $slack = new slack_notification();
        $data = json_encode(array(
            'text' => '[OG Crew] ' . $mail_subject,
            'attachments' => array(
                array(
                    'color' => 'warning',
                    'text' => $mail_content
                )
            )
        ));
        $slack->send(SLACK_WEBHOOK_DEV_DEBUG, $data);
    }

}

?>
