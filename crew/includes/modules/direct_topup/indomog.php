<?php

include_once(DIR_WS_CLASSES . 'direct_topup.php');
include_once(DIR_WS_CLASSES . 'curl.php');
include_once(DIR_WS_CLASSES . 'currencies.php');

class dtu_indomog extends direct_topup {

    public $title, $enable;

    const RESERVE_METHOD = '5001';
    const RESERVE_METHOD_NAME = 'Reserve';
    const CONFIRM_METHOD = '5002';
    const CONFIRM_METHOD_NAME = 'Confirm';
    const SHOP_METHOD = '5003';
    const SHOP_METHOD_NAME = 'Shop';

    public $request_type = '';
    public $request_type_name = '';
    public $secret_key = '';

    function dtu_indomog() {
        $this->title = 'Indomog';
        $this->enable = true;
    }

    function get_title() {
        return $this->title;
    }

    function get_enable() {
        return $this->enable;
    }

    function draw_admin_input($pID = '') {
        $publishers_configuration_array = array();
        $publishers_configuration_select_sql = "SELECT publishers_configuration_key, publishers_configuration_value
												FROM " . TABLE_PUBLISHERS_CONFIGURATION . "
												WHERE publishers_id = '" . (int) $pID . "'";
        $publishers_configuration_result_sql = tep_db_query($publishers_configuration_select_sql);
        while ($publishers_configuration_row = tep_db_fetch_array($publishers_configuration_result_sql)) {
            $publishers_configuration_array[$publishers_configuration_row['publishers_configuration_key']] = $publishers_configuration_row['publishers_configuration_value'];
        }

        $display_html = '	<table border="0" cellspacing="2" cellpadding="2">
								<tr>
									<td class="main">Top-up URL:</td>
									<td class="main">' . tep_draw_input_field('configuration[TOP_UP_URL]', (isset($publishers_configuration_array['TOP_UP_URL']) ? $publishers_configuration_array['TOP_UP_URL'] : ''), '', false) . ' 
										' . tep_draw_radio_field('configuration[TOP_UP_URL_FLAG]', '1', (isset($publishers_configuration_array['TOP_UP_URL_FLAG']) && $publishers_configuration_array['TOP_UP_URL_FLAG'] == 1 ? 1 : 0)) . ' On 
										' . tep_draw_radio_field('configuration[TOP_UP_URL_FLAG]', '0', (!isset($publishers_configuration_array['TOP_UP_URL_FLAG']) || $publishers_configuration_array['TOP_UP_URL_FLAG'] == 0 ? 1 : 0)) . ' Off 
									</td>
								</tr>
  <tr>
									<td class="main">Validate Account URL:</td>
									<td class="main">' . tep_draw_input_field('configuration[VALIDATE_GAME_ACC_URL]', (isset($publishers_configuration_array['VALIDATE_GAME_ACC_URL']) ? $publishers_configuration_array['VALIDATE_GAME_ACC_URL'] : ''), '', false) . ' 
										' . tep_draw_radio_field('configuration[VALIDATE_GAME_ACC_URL_FLAG]', '1', (isset($publishers_configuration_array['VALIDATE_GAME_ACC_URL_FLAG']) && $publishers_configuration_array['VALIDATE_GAME_ACC_URL_FLAG'] == 1 ? 1 : 0)) . ' On 
										' . tep_draw_radio_field('configuration[VALIDATE_GAME_ACC_URL_FLAG]', '0', (!isset($publishers_configuration_array['VALIDATE_GAME_ACC_URL_FLAG']) || $publishers_configuration_array['VALIDATE_GAME_ACC_URL_FLAG'] == 0 ? 1 : 0)) . ' Off 
									</td>
								
                              
                                <tr>
									<td class="main">' . ENTRY_PUBLISHERS_SECRET_KEY . ':</td>
									<td class="main">' . tep_draw_password_field('configuration[SECRET_KEY]', (isset($publishers_configuration_array['SECRET_KEY']) ? $publishers_configuration_array['SECRET_KEY'] : ''), '', false) . '</td>
								</tr>
							</table>';
        return $display_html;
    }

    function draw_admin_game_input($action, $publishers_games_array) {
        switch ($action) {
            /* GAME SETTING */
            case 'new_game':
            case 'edit_game':
                $publishers_game_name = '';
                $publishers_games_daily_limit = 0;
                $publishers_games_today_topped_amount = 0;
                $publishers_games_status = 1;
                if ($action == 'edit_game') {
                    $publishers_game_select_sql = "	SELECT publishers_game, categories_id, publishers_games_remark, 
														publishers_games_pending_message, publishers_games_reloaded_message, 
														publishers_games_failed_message, publishers_games_daily_limit, 
														publishers_games_today_topped_amount, publishers_games_status 
													FROM " . TABLE_PUBLISHERS_GAMES . " AS pg
													WHERE publishers_games_id = '" . (int) $publishers_games_array['publishers_games_id'] . "'
														AND publishers_id = '" . (int) $publishers_games_array['publishers_id'] . "'";
                    $publishers_game_result_sql = tep_db_query($publishers_game_select_sql);
                    if ($publishers_game_row = tep_db_fetch_array($publishers_game_result_sql)) {
                        $publishers_game_name = $publishers_game_row['publishers_game'];
                        $cat_id = $publishers_game_row['categories_id'];
                        $publishers_games_daily_limit = $publishers_game_row['publishers_games_daily_limit'];
                        $publishers_games_today_topped_amount = $publishers_game_row['publishers_games_today_topped_amount'];
                        $publishers_game_remark = $publishers_game_row['publishers_games_remark'];
                        $publishers_games_pending_message = $publishers_game_row['publishers_games_pending_message'];
                        $publishers_games_reloaded_message = $publishers_game_row['publishers_games_reloaded_message'];
                        $publishers_games_failed_message = $publishers_game_row['publishers_games_failed_message'];
                        $publishers_games_status = $publishers_game_row['publishers_games_status'];
                    }

                    $publishers_games_configuration_select_sql = "	SELECT publishers_games_configuration_value, publishers_games_configuration_key 
																	FROM " . TABLE_PUBLISHERS_GAMES_CONFIGURATION . " AS pg
																	WHERE publishers_games_id = '" . (int) $publishers_games_array['publishers_games_id'] . "'";
                    $publishers_games_configuration_result_sql = tep_db_query($publishers_games_configuration_select_sql);
                    while ($publishers_games_configuration_row = tep_db_fetch_array($publishers_games_configuration_result_sql)) {
                        $publishers_games_configuration_array[$publishers_games_configuration_row['publishers_games_configuration_key']] = $publishers_games_configuration_row['publishers_games_configuration_value'];
                    }
                }

                $games_name_array = array();
                if ($action == 'new') {
                    $games_name_array[] = array('id' => '',
                        'text' => PULL_DOWN_DEFAULT);
                }

                tep_db_connect_og();
                $categories_games_array = [];
                $categories_select_sql = "SELECT DISTINCT(categories_id) FROM category";
                $categories_result_sql = tep_db_query($categories_select_sql, 'db_og_link');
                while ($categories_row = tep_db_fetch_array($categories_result_sql)) {
                    $categories_games_array[] = $categories_row['categories_id'];
                }

                $games_name_select_sql = "	SELECT c.categories_id, cd.categories_name
											FROM " . TABLE_CATEGORIES . " as c
											INNER JOIN " . TABLE_CATEGORIES_DESCRIPTION . " as cd
												ON c.categories_id = cd.categories_id 
											WHERE c.categories_id IN ('" . implode("','", $categories_games_array) . "')
												AND cd.language_id='1'";
                $games_name_result_sql = tep_db_query($games_name_select_sql);
                while ($games_name_row = tep_db_fetch_array($games_name_result_sql)) {
                    $games_name_array[] = array('id' => $games_name_row['categories_id'],
                        'text' => $games_name_row['categories_name']);
                }

                $display_html = '<table border="0" width="100%" cellspacing="0" cellpadding="2">
					      			<tr>
					        			<td class="formAreaTitle">' . ($action == 'new_game' ? HEADING_TITLE_INSERT : HEADING_TITLE_UPDATE) . '&nbsp;-&nbsp;' . $publishers_row['publishers_name'] . '</td>
					      			</tr>
					      			<tr>
					        			<td class="formArea">
					        				<table border="0" cellspacing="2" cellpadding="2" class="main">
					          					<tr>
					            					<td width="160px">' . ENTRY_GAME_NAME . ':</td>
					            					<td width="400px">' . tep_draw_pull_down_menu('sel_categories_games', $games_name_array, $cat_id) . '</td>
													<td>Top-up URL:</td>
													<td>' . tep_draw_input_field('publishers_games_configuration[TOP_UP_URL]', (isset($publishers_games_configuration_array['TOP_UP_URL']) ? $publishers_games_configuration_array['TOP_UP_URL'] : ''), '', false) . '</td>
													<td>' . tep_draw_checkbox_field('publishers_games_configuration[PARENT_TOP_UP_URL_FLAG]', 1, (isset($publishers_games_configuration_array['PARENT_TOP_UP_URL_FLAG']) || (!isset($publishers_games_configuration_array['TOP_UP_URL']) || !tep_not_null($publishers_games_configuration_array['TOP_UP_URL'])) ? 1 : 0), ' id="chk_top_up_url" ') . ' Follow publisher setting</td>
					          					</tr>
					          					<tr>
					            					<td>' . ENTRY_GAME_STATUS . ':</td>
					            					<td>' . tep_draw_radio_field('publishers_games_status', 1, $publishers_games_status) . 'On ' . tep_draw_radio_field('publishers_games_status', 0, (!$publishers_games_status)) . 'Off </td>
													
					          					<tr>
					            					<td>' . ENTRY_TOP_UP_DAILY_LIMIT . ':</td>
					            					<td>' . tep_draw_input_field('publishers_top_up_daily_limit', $publishers_games_daily_limit, ' id="publishers_top_up_daily_limit" ', false) . '</td>
													
					          					</tr>
					          					<tr>
					            					<td>' . ENTRY_DAILY_TOPPED_AMOUNT . ':</td>
					            					<td>' . number_format($publishers_games_today_topped_amount, 2, ".", "") . '
					            					&nbsp;&nbsp;&nbsp;<a href="' . tep_href_link(FILENAME_PUBLISHERS_GAMES, tep_get_all_get_params(array('pID', 'pgID', 'flag', 'action', 'subaction', 'product_id')) . 'pID=' . (int) $_REQUEST['pID'] . '&pgID=' . (int) $_REQUEST['pgID'] . '&action=edit&subaction=reset_amount') . '">' . LINK_RESET_TOP_UP_AMOUNT . '</a></td>
													
					          					</tr>
					          					<tr>
					            					<td>' . ENTRY_GAME_MAPPING . ':</td>
					            					<td>' . tep_draw_input_field('publishers_game', $publishers_game_name, ' id="publishers_game" ', false) . '</td>
													
					          					</tr>
					          					<tr>
					            					<td valign="top">' . ENTRY_GAME_PENDING_MESSAGE . ':</td>
					            					<td><textarea name="txt_pending_message" cols="50" rows="5">' . $publishers_games_pending_message . '</textarea></td>
													<td class="main" colspan="3">&nbsp;</td>
					          					</tr>
					          					<tr>
					            					<td valign="top">' . ENTRY_GAME_RELOADED_MESSAGE . ':</td>
					            					<td><textarea name="txt_reloaded_message" cols="50" rows="5">' . $publishers_games_reloaded_message . '</textarea></td>
					          					</tr>
					          					<tr>
					            					<td valign="top">' . ENTRY_GAME_FAILED_MESSAGE . ':</td>
					            					<td><textarea name="txt_failed_message" cols="50" rows="5">' . $publishers_games_failed_message . '</textarea></td>
					          					</tr>
					          					<tr>
					            					<td valign="top">' . ENTRY_GAME_REMARK . ':</td>
					            					<td><textarea name="txt_remark" cols="50" rows="5">' . $publishers_game_remark . '</textarea></td>
					          					</tr>
								      			<tr>
								      				<td>&nbsp;</td>
								        			<td align="left" class="main">' . ($action == 'new_game' ? '<input type="submit" class="inputButton" value="Insert">' : '<input type="submit" class="inputButton" value="Update">') . '&nbsp;<input type="button" class="inputButton" value="Cancel" onclick="location.href=\'' . tep_href_link(FILENAME_PUBLISHERS_GAMES, tep_get_all_get_params(array('action', 'subaction')) . 'action=edit') . '\'"></td>
								      			</tr>
					        				</table>
					        			</td>
					      			</tr>
					      			<tr>
					        			<td>' . tep_draw_separator('pixel_trans.gif', '1', '10') . '</td>
					      			</tr>
					      		</table>';
                break;
        }
        return $display_html;
    }

    function draw_admin_customer_input($action, $publishers_games_array) {
        switch ($action) {
            /* GAME SETTING */
            case 'new_product':
            case 'edit_product':
                $languages = tep_get_languages();

                $products_id = '';
                $publishers_game_name = '';

                $publishers_game_select_sql = "	SELECT publishers_game
												FROM " . TABLE_PUBLISHERS_GAMES . " AS pg
												WHERE publishers_games_id = '" . (int) $publishers_games_array['publishers_games_id'] . "'
													AND publishers_id = '" . (int) $publishers_games_array['publishers_id'] . "'";
                $publishers_game_result_sql = tep_db_query($publishers_game_select_sql);
                if ($publishers_game_row = tep_db_fetch_array($publishers_game_result_sql)) {
                    $publishers_game_name = $publishers_game_row['publishers_game'];
                }

                $def_amount = '';
                $def_amount_type = 'currency';
                $def_product_code = '';
                $def_account_flag = 0;
                $def_server_flag = 0;
                $def_character_flag = 0;

                $def_sort_order_account = '50000';

                for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
                    $def_label_account[$languages[$n]['id']] = '';
                }

                $def_sort_order_server = '50000';
                $def_english_label_server = '';
                $def_chinese_label_server = '';

                $def_sort_order_character = '50000';
                $def_english_label_character = '';
                $def_chinese_label_character = '';

                $def_sort_order_account_platform = '50000';
                $def_english_label_account_platform = '';
                $def_chinese_label_account_platform = '';

                $def_sync_publisher_character_flag = 0;

                $top_up_info_array = array();
                if ($action == 'edit_product') {
                    $publishers_products_select_sql = "	SELECT p.products_id, p.products_cat_path, pd.products_name 
														FROM " . TABLE_PUBLISHERS_PRODUCTS . " AS pp 
														INNER JOIN " . TABLE_PRODUCTS . " AS p 
															ON pp.products_id = p.products_id
														INNER JOIN " . TABLE_PRODUCTS_DESCRIPTION . " AS pd 
															ON pp.products_id = pd.products_id
														WHERE pp.publishers_games_id = '" . (int) $publishers_games_array['publishers_games_id'] . "'
															AND pp.products_id = '" . (int) $publishers_games_array['products_id'] . "' 
															AND pd.language_id = 1 
															AND pd.products_name <> ''";
                    $publishers_products_result_sql = tep_db_query($publishers_products_select_sql);
                    $publishers_products_row = tep_db_fetch_array($publishers_products_result_sql);
                    $products_id = $publishers_products_row['products_id'];
                    $products_cat_path_display = $publishers_products_row['products_cat_path'] . ' > ' . $publishers_products_row['products_name'];

                    $top_up_info_select_sql = "	SELECT top_up_info_key, top_up_info_value, top_up_info_type_id, sort_order, top_up_info_display, languages_id 
												FROM " . TABLE_TOP_UP_INFO . " AS tui
												LEFT JOIN " . TABLE_TOP_UP_INFO_LANG . " AS tuil 
													ON tui.top_up_info_id = tuil.top_up_info_id
												WHERE products_id = '" . (int) $publishers_games_array['products_id'] . "'";
                    $top_up_info_result_sql = tep_db_query($top_up_info_select_sql);
                    while ($top_up_info_row = tep_db_fetch_array($top_up_info_result_sql)) {
                        switch ($top_up_info_row['top_up_info_key']) {
                            case 'sync_publisher_character_flag':
                                $def_sync_publisher_character_flag = $top_up_info_row['top_up_info_value'];
                                break;
                            case 'amount':
                                $def_amount = $top_up_info_row['top_up_info_value'];
                                break;
                            case 'amount_type':
                                $def_amount_type = $top_up_info_row['top_up_info_value'];
                                break;
                            case 'product_code':
                                $def_product_code = $top_up_info_row['top_up_info_value'];
                                break;
                            case 'account':
                                $def_label_account[$top_up_info_row['languages_id']] = $top_up_info_row['top_up_info_display'];
                                $def_sort_order_account = $top_up_info_row['sort_order'];
                                $def_account_flag = 1;
                                break;
                            case 'server':
                                $def_sort_order_server = $top_up_info_row['sort_order'];
                                $def_label_server[$top_up_info_row['languages_id']] = $top_up_info_row['top_up_info_display'];
                                $def_server_flag = 1;
                                break;
                            case 'character':
                                $def_sort_order_character = $top_up_info_row['sort_order'];
                                $def_label_character[$top_up_info_row['languages_id']] = $top_up_info_row['top_up_info_display'];
                                $def_character_flag = 1;
                                break;
                            case 'account_platform':
                                $def_sort_order_account_platform = $top_up_info_row['sort_order'];
                                $def_label_account_platform[$top_up_info_row['languages_id']] = $top_up_info_row['top_up_info_display'];
                                $def_account_platform_flag = 1;
                                break;
                        }
                    }
                }

                $display_html = '<table border="0" cellspacing="2" cellpadding="2" class="main" width="100%">
			          					<tr>
			            					<td width="100px" valign="top">' . ENTRY_PRODUCT_ID . ':</td>
			            					<td class="reportRecords">';
                if ($action == 'new_product') {
                    $display_html .= tep_draw_input_field('product_id', $products_id, ' id="games_product_id" onblur="load_product_cat_path(this.value)" ', false) . '&nbsp;(<a href="javascript:openDGDialog(' . tep_href_link(FILENAME_POPUP_PRODUCTS_LIST, 'direct_top_up=1&fname=log_files.php') . ', 600, 250)">Product List</a>)';
                } else {
                    $display_html .= $products_id . tep_draw_hidden_field('product_id', $products_id);
                }
                $display_html .= '				<div id="div_cat_path">' . (tep_not_null($products_cat_path_display) ? $products_cat_path_display : '') . '</div>
											</td>
			    	      				</tr>
			          					<tr>
			            					<td colspan="2">
			            						<fieldset>
			            							<legend>Configuration</legend>
							        				<table border="0" cellspacing="0" cellpadding="2" class="main">
							          					<tr valign="top" class="reportListingOdd">
							            					<td class="reportBoxHeading" width="100px">&nbsp;</td>
							            					<td class="reportBoxHeading" width="150px">&nbsp;</td>
							            					<td class="reportBoxHeading" width="300px" align="center">Description</td>
							            					<td class="reportBoxHeading" width="100px" align="center">' . TABLE_HEADING_SORT_ORDER . '</td>';
                for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
                    $display_html .= '					<td class="reportBoxHeading" width="100px" align="center">' . $languages[$i]['name'] . '</td>';
                }
                $display_html .= '						<td class="reportBoxHeading" width="150px" align="center">' . TABLE_HEADING_SYSTEM_TYPE . '</td>
															<td class="reportBoxHeading" width="100px">Use in API Signature?</td>
							          					</tr>
							          					<tr valign="top" class="reportListingOdd" onmouseover="rowOverEffect(this, \'reportListingRowOver\')" onmouseout="rowOutEffect(this, \'reportListingOdd\')" onclick="rowClicked(this, \'reportListingOdd\')">
							            					<td class="reportRecords">' . ENTRY_AMOUNT_TYPE . ':</td>
							            					<td class="reportRecords">';
                $amount_type_array = array();
                $amount_type_array[] = array('id' => 'currency', 'text' => 'Currency');
                $amount_type_array[] = array('id' => 'point', 'text' => 'Point');
                $display_html .= tep_draw_pull_down_menu('sel_amount_type', $amount_type_array, $def_amount_type, false);
                $display_html .= '						</td>
							            					<td class="reportRecords">\'point\' or \'currency\' for this top-up amount.</td>
							            					<td class="reportRecords" align="center">-</td>';
                for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
                    $display_html .= '					<td class="reportRecords" align="center">-</td>';
                }
                $display_html .= '							<td class="reportRecords" align="center">-</td>
							            					<td class="reportRecords" align="center"></td>
							          					</tr>
							          					<tr valign="top" class="reportListingEven" onmouseover="rowOverEffect(this, \'reportListingRowOver\')" onmouseout="rowOutEffect(this, \'reportListingEven\')" onclick="rowClicked(this, \'reportListingEven\')">
							            					<td class="reportRecords">' . ENTRY_AMOUNT . ':</td>
							            					<td class="reportRecords">' . tep_draw_input_field('txt_amount', $def_amount, ' id="txt_amount" ', false) . '</td>
							            					<td class="reportRecords">Deno face value to be top-up.<br>(total to be top-up = quantity * amount)</td>
							            					<td class="reportRecords" align="center">-</td>';
                for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
                    $display_html .= '						<td class="reportRecords" align="center">-</td>';
                }
                $display_html .= '         					<td class="reportRecords" align="center">-</td>
							            					<td class="reportRecords" align="center"></td>
							          					</tr>
                                                        <tr valign="top" class="reportListingEven" onmouseover="rowOverEffect(this, \'reportListingRowOver\')" onmouseout="rowOutEffect(this, \'reportListingEven\')" onclick="rowClicked(this, \'reportListingEven\')">
							            					<td class="reportRecords">' . ENTRY_PRODUCT_CODE . ':</td>
							            					<td class="reportRecords">' . tep_draw_input_field('txt_product_code', $def_product_code, ' id="txt_product_code" ', false) . '</td>
							            					<td class="reportRecords">Product reference code used at Publisher end</td>
							            					<td class="reportRecords" align="center">-</td>';
                for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
                    $display_html .= '						<td class="reportRecords" align="center">-</td>';
                }
                $display_html .= '         					<td class="reportRecords" align="center">-</td>
							            					<td class="reportRecords" align="center"></td>
							          					</tr>
							          					<tr valign="top" class="reportListingOdd" onmouseover="rowOverEffect(this, \'reportListingRowOver\')" onmouseout="rowOutEffect(this, \'reportListingOdd\')" onclick="rowClicked(this, \'reportListingOdd\')">
							            					<td class="reportRecords">' . ENTRY_ACCOUNT . ':</td>
							            					<td class="reportRecords">-</td>
															<td class="reportRecords">Game\'s account name to be top-up.</td>
							            					<td class="reportRecords" align="center">' . tep_draw_input_field('txt_sort_order_account', $def_sort_order_account, ' id="txt_sort_order_account" size="10" ', false) . '</td>';
                for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
                    $display_html .= '						<td class="reportRecords" align="center">' . tep_draw_input_field('txt_label_account[' . $languages[$i]['id'] . ']', $def_label_account[$languages[$i]['id']], ' id="txt_label_account_' . $languages[$i]['id'] . '" size="10" ', false) . '</td>';
                }
                $display_html .= '								<td class="reportRecords" nowrap>' .
                        tep_draw_radio_field('rd_account', '1', ($def_account_flag ? true : false)) . '&nbsp;Display&nbsp;&nbsp;' .
                        tep_draw_radio_field('rd_account', '0', (!$def_account_flag ? true : false)) . '&nbsp;Disable
							            					</td>
							            					<td class="reportRecords" align="center">Always</td>
							          					</tr>
							          					<tr valign="top" class="reportListingEven" onmouseover="rowOverEffect(this, \'reportListingRowOver\')" onmouseout="rowOutEffect(this, \'reportListingEven\')" onclick="rowClicked(this,\'reportListingEven\')">
							            					<td class="reportRecords">' . ENTRY_SERVER . ':</td>
							            					<td class="reportRecords">-</td>
							            					<td class="reportRecords">Server ID of the selected game.</td>
							            					<td class="reportRecords" align="center">' . tep_draw_input_field('txt_sort_order_server', $def_sort_order_server, ' id="txt_sort_order_server" size="10" ', false) . '</td>';
                for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
                    $display_html .= '						<td class="reportRecords" align="center">' . tep_draw_input_field('txt_label_server[' . $languages[$i]['id'] . ']', $def_label_server[$languages[$i]['id']], ' id="txt_label_server' . $languages[$i]['id'] . '" size="10" ', false) . '</td>';
                }

                $display_html .= '							<td class="reportRecords" nowrap>' .
                        tep_draw_radio_field('rd_server', '1', ($def_server_flag ? true : false)) . '&nbsp;Display&nbsp;&nbsp;' .
                        tep_draw_radio_field('rd_server', '0', (!$def_server_flag ? true : false)) . '&nbsp;Disable
															</td>
							            					<td class="reportRecords" align="center">Always</td>
							          					</tr>
							          					<tr valign="top" class="reportListingOdd" onmouseover="rowOverEffect(this, \'reportListingRowOver\')" onmouseout="rowOutEffect(this, \'reportListingOdd\')" onclick="rowClicked(this, \'reportListingOdd\')">
							            					<td class="reportRecords">' . ENTRY_CHARACTER . ':</td>
							            					<td class="reportRecords">' . tep_draw_checkbox_field('chk_sync_publisher_character', 1, $def_sync_publisher_character_flag, ' id="chk_sync_publisher_character" ') . ' sync with publisher</td>
							            					<td class="reportRecords">Character in game to be top-up.</td>
							            					<td class="reportRecords" align="center">' . tep_draw_input_field('txt_sort_order_character', $def_sort_order_character, ' id="txt_sort_order_character" size="10" ', false) . '</td>';
                for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
                    $display_html .= '						<td class="reportRecords" align="center">' . tep_draw_input_field('txt_label_character[' . $languages[$i]['id'] . ']', $def_label_character[$languages[$i]['id']], ' id="txt_label_character' . $languages[$i]['id'] . '" size="10" ', false) . '</td>';
                }
                $display_html .= '		  					<td class="reportRecords" nowrap>' .
                        tep_draw_radio_field('rd_character', '1', ($def_character_flag ? true : false)) . '&nbsp;Display&nbsp;&nbsp;' .
                        tep_draw_radio_field('rd_character', '0', (!$def_character_flag ? true : false)) . '&nbsp;Disable
							            					</td>
							            					<td class="reportRecords" align="center">Always</td>
							          					</tr>
							          					<tr valign="top" class="reportListingEven" onmouseover="rowOverEffect(this, \'reportListingRowOver\')" onmouseout="rowOutEffect(this, \'reportListingOdd\')" onclick="rowClicked(this, \'reportListingOdd\')">
							            					<td class="reportRecords">' . ENTRY_ACCOUNT_PLATFORM . ':</td>
							            					<td class="reportRecords">&nbsp;</td>
							            					<td class="reportRecords">Game account platform.</td>
							            					<td class="reportRecords" align="center">' . tep_draw_input_field('txt_sort_order_account_platform', $def_sort_order_account_platform, ' id="txt_sort_order_account_platform" size="10" ', false) . '</td>';
                for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
                    $display_html .= '						<td class="reportRecords" align="center">' . tep_draw_input_field('txt_label_account_platform[' . $languages[$i]['id'] . ']', $def_label_account_platform[$languages[$i]['id']], ' id="txt_label_account_platform' . $languages[$i]['id'] . '" size="10" ', false) . '</td>';
                }
                $display_html .= '		  					<td class="reportRecords" nowrap>' .
                        tep_draw_radio_field('rd_account_platform', '1', ($def_account_platform_flag ? true : false)) . '&nbsp;Display&nbsp;&nbsp;' .
                        tep_draw_radio_field('rd_account_platform', '0', (!$def_account_platform_flag ? true : false)) . '&nbsp;Disable
							            					</td>
							            					<td class="reportRecords" align="center">When \'Display\'</td>
							          					</tr>
							          				</table>
								          		</fieldset>
			            					</td>
			          					</tr>
						      			<tr>
						      				<td>&nbsp;</td>
						        			<td align="left" class="main">' . ($action == 'new_product' ? '<input type="submit" class="inputButton" value="Insert">' : '<input type="submit" class="inputButton" value="Update">') . '&nbsp;<input type="button" class="inputButton" value="Cancel" onclick="location.href=\'' . tep_href_link(FILENAME_PUBLISHERS_GAMES, tep_get_all_get_params(array('action', 'subaction')) . 'action=edit') . '\'"></td>
						      			</tr>
			        				</table>';
                break;
        }
        return $display_html;
    }

    function validate_admin_update_input($param, &$msg) {
        if (!isset($_REQUEST['configuration']['SECRET_KEY']) || !$_REQUEST['configuration']['SECRET_KEY']) {
            $msg = ERROR_INVALID_PUBLISHERS_SECRET_KEY;
            return false;
        } else {
            //
        }
        return true;
    }

    function validate_admin_game_input($param, &$msg) {
        return true;
    }

    function validate_admin_customer_input($param, &$msg) {
        return true;
    }

    private function generateRequestXML($param) {

        $return_xml = '<?xml version="1.0"?>' .
                '<methodCall>' .
                '<methodName>' . $this->request_type_name . '</methodName>' .
                '<params>' .
                '<param>' .
                '<value>' .
                '<struct>';

        $data_type = 'string';

        foreach ($param as $name => $value) {
            if ($name === 'Now') {
                $data_type = 'datetime.iso8601';
            }

            $return_xml.= '<member>
                                 <name>' . $name . '</name>
                                 <value><' . $data_type . '>' . $value . '</' . $data_type . '></value>
                           </member>';
        }

        $return_xml.= '</struct>' .
                '</value>' .
                '</param>' .
                '</params>' .
                '</methodCall>';

        return $return_xml;
    }

    private function generateSignature($param) {
        return sha1(implode('', $param) . $this->secret_key);
    }

    public function do_top_up($orders_products_id, $publisher_id, $top_up_id, $amount_type, $amount, $quantity, $account, $character, $game, $server = '1', $account_platform = '', $product_array = array()) {
        $action = 'do_top_up';
        $result_code = '1000';
        $response_log = '';
        $this->delete_reserved_products();

        if (tep_not_null($character)) {
            if (stristr($character, '##')) {
                $character_tmp = explode("##", $character);
                $character = $character_tmp[0];
            }
        }


        $this->publishers($publisher_id);
        $get_publishers_conf_array = $this->get_publishers_conf();

        $url = $get_publishers_conf_array['TOP_UP_URL']['publishers_configuration_value'];

        $this->secret_key = $get_publishers_conf_array['SECRET_KEY']['publishers_configuration_value'];

        $indomog_dtu_log_select_sql = " SELECT indomog_log_id, indomog_qid, indomog_pid, (TIMESTAMPDIFF(HOUR, created_datetime, NOW() ) <= 2) as valid
                                        FROM " . TABLE_INDOMOG_DTU_LOGS . " 
                                        WHERE  email_hp = '" . tep_db_input($account) . "' 
                                            AND products_id = '" . tep_db_input($product_array['pid']) . "'
                                            AND indomog_verify_log_id != 0 
                                            AND indomog_topup_log_id = 0 
                                        ORDER BY created_datetime DESC 
                                        LIMIT 1";

        $indomog_dtu_log_result_sql = tep_db_query($indomog_dtu_log_select_sql);
        $indomog_pid = '';
        $indomog_qid = 0;
        $indomog_log_id = 0;

        if ($indomog_dtu_log_row = tep_db_fetch_array($indomog_dtu_log_result_sql)) {
            $indomog_pid = $indomog_dtu_log_row['indomog_pid'];
            
            if ($indomog_dtu_log_row['valid'] == '1') {
                $indomog_qid = $indomog_dtu_log_row['indomog_qid'];
                $indomog_log_id = $indomog_dtu_log_row['indomog_log_id'];
            }
        }

        $timestamp_iso8601 = date('Ymd\TH:i:s');
        $new_indomog_qid = 0;

        if ($indomog_qid) {
            $this->request_type = self::CONFIRM_METHOD;
            $this->request_type_name = self::CONFIRM_METHOD_NAME;

            $param = array(
                'RMID' => $get_publishers_conf_array['OGM_MERCHANT_ID']['publishers_configuration_value'],
                'QID' => $indomog_qid,
                'RC' => $this->request_type,
                'IPD' => tep_get_ip_address(),
                'Now' => $timestamp_iso8601,
            );
        } else {
            $this->request_type = self::SHOP_METHOD;
            $this->request_type_name = self::SHOP_METHOD_NAME;

            $indomog_pid = $indomog_pid == '' ? $this->get_top_up_info_product_code($product_array['pid']) : $indomog_pid;
            $indomog_log_id = $this->create_indomog_log($account, $product_array['pid'], $indomog_pid, strtoupper($this->request_type_name));
            $new_indomog_qid = date('Ymd') . substr(str_pad($indomog_log_id, 4, 0, STR_PAD_LEFT), -4);

            $param = array('RMID' => $get_publishers_conf_array['OGM_MERCHANT_ID']['publishers_configuration_value'],
                'QID' => $new_indomog_qid,
                'RC' => $this->request_type,
                'IPD' => tep_get_ip_address(),
                'EmailHP' => $account,
                'ProdID' => $indomog_pid,
                'Qty' => 1,
                'ProdAccID' => $account,
                'prodBillID' => '',
                'Remark' => $this->get_publisher_remark($publisher_id),
                'Now' => $timestamp_iso8601,
            );
        }



        $request_signature = $this->generateSignature($param);
        $param['Signature'] = $request_signature;

        $curl_response_array = $this->curl_send_request_with_xml($url, $param);

        /* Start API Request Log */
        $request_log = 'url: ' . $url . "\n";
        ob_start();
        echo "<pre>";
        print_r($param);
        $request_log .= ob_get_contents();
        ob_end_clean();

        $api_log_obj = new direct_topup_api_log($action, $request_log, array('publishers_id' => (int) $publisher_id,
            'top_up_id' => (int) $top_up_id,
            'orders_products_id' => (int) $orders_products_id));

        $indomog_topup_log_id = $api_log_obj->api_log_id;


        $response_signature = isset($curl_response_array['Signature']) ? $curl_response_array['Signature'] : '';

        $orders_top_up_data_sql = array('publishers_response_time' => 'now()',
            'top_up_timestamp' => tep_db_prepare_input(time()));

        if (isset($curl_response_array['RspCode']) && $curl_response_array['RspCode'] == '000') {
            $orders_top_up_data_sql['publishers_ref_id'] = tep_db_prepare_input($curl_response_array['TrxID']);
            $orders_top_up_data_sql['game'] = $game;
            $orders_top_up_data_sql['server'] = $server;
            $orders_top_up_data_sql['account'] = $account;
            $orders_top_up_data_sql['`character`'] = $character;

            $orders_top_up_data_sql['top_up_response_info'] = tep_db_prepare_input(json_encode($curl_response_array));
        }

        if (isset($curl_response_array['RspCode'])) {
            switch ($curl_response_array['RspCode']) {
                case '000':
                    if (strcmp($response_signature, $request_signature) != 0) {
                        $curl_response_array['top_up_status'] = 'FAILED';
                        $orders_top_up_data_sql['top_up_status'] = '10';
                        $result_code = '1002';
                        $orders_top_up_remark_data_sql = array('top_up_id' => (int) $top_up_id,
                            'data_added' => 'now()',
                            'remark' => $this->get_result_code_description($result_code));
                        tep_db_perform(TABLE_ORDERS_TOP_UP_REMARK, $orders_top_up_remark_data_sql);
                    } else {
                        $curl_response_array['top_up_status'] = 'RELOADED';
                        $result_code = '2000';
                        $orders_top_up_data_sql['top_up_status'] = '3';
                        $orders_top_up_remark_data_sql = array('top_up_id' => (int) $top_up_id,
                            'data_added' => 'now()',
                            'remark' => 'Top-up: Reloaded');
                        tep_db_perform(TABLE_ORDERS_TOP_UP_REMARK, $orders_top_up_remark_data_sql);
                    }
                    break;
                default:
                    $curl_response_array['top_up_status'] = 'FAILED';
                    $orders_top_up_data_sql['top_up_status'] = '10';
                    $result_code = '1000';

                    $orders_top_up_remark_data_sql = array('top_up_id' => (int) $top_up_id,
                        'data_added' => 'now()',
                        'remark' => 'Top-up: Failed');
                    tep_db_perform(TABLE_ORDERS_TOP_UP_REMARK, $orders_top_up_remark_data_sql);
                    break;
            }
        }

        tep_db_perform(TABLE_ORDERS_TOP_UP, $orders_top_up_data_sql, 'update', " top_up_id = '" . (int) $top_up_id . "' ");

        $indomog_trans_id = isset($curl_response_array['TrxID']) ? $curl_response_array['TrxID'] : '';

        /* Start API Response Log */
        $api_log_obj->end_log($result_code, $response_log, array('publishers_id' => (int) $publisher_id,
            'top_up_id' => (int) $top_up_id,
            'publisher_ref_id' => $indomog_trans_id));
        /* End API Response Log */


        $curl_response_array['publisher_ref_id'] = $indomog_trans_id;
        $curl_response_array['result_code'] = $result_code;

        if ($indomog_qid) {
            $update_array = array('indomog_topup_log_id ' => $indomog_topup_log_id);
        } else if ($new_indomog_qid) {
            $update_array = array(
                'indomog_verify_log_id' => $indomog_topup_log_id,
                'indomog_topup_log_id ' => $indomog_topup_log_id,
                'indomog_qid' => $new_indomog_qid);
        }
        $this->update_indomog_log($indomog_log_id, $update_array);

        return $curl_response_array;
    }

   
    public function delete_reserved_products() {
        $indomog_dtu_log_delete_sql = " DELETE FROM " . TABLE_INDOMOG_DTU_LOGS . "  
                                        WHERE indomog_request_type = 'RESERVE' 
                                            AND indomog_topup_log_id = 0 
                                            AND TIMESTAMPDIFF(DAY, created_datetime, NOW() ) > 2";
        tep_db_query($indomog_dtu_log_delete_sql);
    }


    public function validate_game_acc($publisher_id, $games_acc_array, &$curl_response_array = '') {
        global $memcache_obj;

        $action = 'validate_game_acc';
        $this->request_type = self::RESERVE_METHOD;
        $this->request_type_name = self::RESERVE_METHOD_NAME;
        
        $error = true;
        $result_code = '1000';
        $log_response = '';


        $this->publishers($publisher_id);
        $get_publishers_conf_array = $this->get_publishers_conf();

        if (!isset($get_publishers_conf_array['VALIDATE_GAME_ACC_URL_FLAG']['publishers_configuration_value']) || !$get_publishers_conf_array['VALIDATE_GAME_ACC_URL_FLAG']['publishers_configuration_value']) {
            return true;
        }

        $url = '';
        if (isset($games_acc_array['publishers_games_id'])) {
            $validate_game_acc_url_flag = $this->get_publishers_games_conf($games_acc_array['publishers_games_id'], 'PARENT_VALIDATE_GAME_ACC_URL_FLAG');
            if (!$validate_game_acc_url_flag) {
                $url = $this->get_publishers_games_conf($games_acc_array['publishers_games_id'], 'VALIDATE_GAME_ACC_URL');
            }
        }

        if (!tep_not_null($url)) {
            $url = $get_publishers_conf_array['VALIDATE_GAME_ACC_URL']['publishers_configuration_value'];
        }

        if (!tep_not_null($url)) {
            return true;
        }

        $this->secret_key = $get_publishers_conf_array['SECRET_KEY']['publishers_configuration_value'];


        $email_hp = $games_acc_array['account'];
        $indomog_pid = $this->get_top_up_info_product_code($games_acc_array['product_id']);
        $timestamp_iso8601 = date('Ymd\TH:i:s');

        $indomog_log_id = $this->create_indomog_log($email_hp, $games_acc_array['product_id'], $indomog_pid, strtoupper($this->request_type_name));
        $indomog_qid = date('Ymd') . substr(str_pad($indomog_log_id, 4, 0, STR_PAD_LEFT), -4);

        $param = array('RMID' => $get_publishers_conf_array['OGM_MERCHANT_ID']['publishers_configuration_value'],
            'QID' => $indomog_qid,
            'RC' => $this->request_type,
            'IPD' => tep_get_ip_address(),
            'EmailHP' => $games_acc_array['account'],
            'ProdID' => $indomog_pid,
            'Remark' => $this->get_publisher_remark($publisher_id),
            'Now' => $timestamp_iso8601,
        );


        $request_signature = $this->generateSignature($param);
        $param['Signature'] = $request_signature;


        /* Start API Request Log */
        $request_log = 'url: ' . $url . "\n";
        ob_start();
        echo "<pre>";
        print_r($param);
        $request_log .= ob_get_contents();
        ob_end_clean();

        $api_log_obj = new direct_topup_api_log($action, $request_log, array('publishers_id' => (int) $publisher_id));
        /* End API Request Log */

        $indomog_verify_log_id = (int) $api_log_obj->api_log_id;
        $raw_response = '';
        $curl_response = $this->curl_send_request_with_xml($url, $param, $raw_response);
        $xml_array = $curl_response;
        $response_signature = isset($curl_response['Signature']) ? $curl_response['Signature'] : '';
        if (is_array($xml_array)) {

            if ($response_signature == $request_signature && isset($xml_array['RspCode']) && $xml_array['RspCode'] == '000' && isset($xml_array['QID']) && $xml_array['QID'] == $indomog_qid) {

                if ($this->update_indomog_log($indomog_log_id, array('indomog_verify_log_id' => $indomog_verify_log_id, 'indomog_qid' => tep_db_prepare_input($indomog_qid)))) {
                    $error = false;
                    $result_code = '2000';
                }
            } else {
                $result_code = '2000';
            }
        }

        if ($error) {
            $curl_response_array['game_acc_status'] = 'NOT_RELOADABLE';
            $curl_response_array['result_code'] = $result_code;
        }

        ob_start();
        echo "<pre>";
        print_r($xml_array);
        $log_response .= ob_get_contents();
        ob_end_clean();

        /* Start API Response Log */
        $api_log_obj->end_log($result_code, $log_response, array('publishers_id' => (int) $publisher_id));
        /* End API Response Log */

        return ($error === false ? true : false );
    }

    private function curl_send_request_with_xml($url, $param, &$curl_response = '') {
        $curl_obj = new curl();
        $curl_obj->http_header = array('Content-Type: text/xml');
        $curl_response = $curl_obj->curl_post($url, $this->generateRequestXML($param));
        $response = $this->_parse_xml_to_array($curl_response);
        $response_array = array();
        if (isset($response['params']['param']['value']['struct']['member']) && is_array($response['params']['param']['value']['struct']['member'])) {
            foreach ($response['params']['param']['value']['struct']['member'] as $name => $attribute) {
                foreach ($attribute['value'] as $value) {
                    $response_array[$attribute['name']] = $value;
                }
            }
        }
        return $response_array;
    }

    public function create_indomog_log($email_hp, $products_id, $indomog_pid, $request_type) {
        $log_data_sql = array();
        $log_data_sql['indomog_request_type'] = tep_db_prepare_input($request_type);
        $log_data_sql['products_id'] = tep_db_prepare_input($products_id);
        $log_data_sql['email_hp'] = tep_db_prepare_input($email_hp);
        $log_data_sql['indomog_pid'] = tep_db_prepare_input($indomog_pid);
        $log_data_sql['created_datetime'] = 'now()';
        tep_db_perform(TABLE_INDOMOG_DTU_LOGS, $log_data_sql);
        return tep_db_insert_id();
    }

    public function update_indomog_log($indomog_log_id, $log_data_sql = array()) {
        if ($log_data_sql) {
            return tep_db_perform(TABLE_INDOMOG_DTU_LOGS, $log_data_sql, 'update', " indomog_log_id = '" . $indomog_log_id . "' ");
        }
        return false;
    }

    private function _parse_xml_to_array($raw_xml) {
        $rx = '/\<\?xml.*encoding=[\'"](.*?)[\'"].*?>/m';

        if (preg_match($rx, $raw_xml, $m)) {
            $encoding = strtoupper($m[1]);
        } else {
            $encoding = "UTF-8";
        }

        if ($encoding == "UTF-8" || $encoding == "US-ASCII" || $encoding == "ISO-8859-1") {
            $parser = xml_parser_create($encoding);
        } else {
            if (function_exists('mb_convert_encoding')) {
                $encoded_source = @mb_convert_encoding($raw_xml, "UTF-8", $encoding);
            }

            if ($encoded_source != NULL) {
                $raw_xml = str_replace($m[0], '<?xml version="1.0" encoding="utf-8"?>', $encoded_source);
            }
        }

        $xml_object = simplexml_load_string($raw_xml);
        $xml_array = json_decode(@json_encode($xml_object), 1);

        return $xml_array;
    }

    public function get_publisher_remark($publisher_id) {
        $publishers_remark = '';
        $publishers_select_sql = "	SELECT publishers_remark
						FROM " . TABLE_PUBLISHERS . "
						WHERE publishers_id = '" . (int) $publisher_id . "'";

        $publishers_result_sql = tep_db_query($publishers_select_sql);

        if ($publishers_row = tep_db_fetch_array($publishers_result_sql)) {
            $publishers_remark = $publishers_row['publishers_remark'];
        }
        return $publishers_remark;
    }

    public function get_top_up_info_product_code($product_id) {
        $top_up_info_product_code = '';
        $top_up_info_select_sql = "SELECT top_up_info_value 
                                    FROM " . TABLE_TOP_UP_INFO . " 
                                    WHERE products_id = '" . tep_db_input($product_id) . "'
                                          AND top_up_info_key='product_code'";
        $top_up_info_result_sql = tep_db_query($top_up_info_select_sql);

        if ($top_up_info_row = tep_db_fetch_array($top_up_info_result_sql)) {
            $top_up_info_product_code = $top_up_info_row['top_up_info_value'];
        }
        return $top_up_info_product_code;
    }

}
?>