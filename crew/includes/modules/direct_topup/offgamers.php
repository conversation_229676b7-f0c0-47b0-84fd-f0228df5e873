<?

include_once(DIR_WS_CLASSES . 'direct_topup.php');
include_once(DIR_WS_CLASSES . 'curl.php');

class dtu_offgamers extends direct_topup {

    var $title, $enable;

    function dtu_offgamers() {
        $this->title = 'OffGamers';
        $this->enable = true;
    }

    function get_title() {
        return $this->title;
    }

    function get_enable() {
        return $this->enable;
    }

    function draw_admin_input($pID = '') {
        $publishers_configuration_array = array();
        $publishers_configuration_select_sql = "SELECT publishers_configuration_key, publishers_configuration_value
												FROM " . TABLE_PUBLISHERS_CONFIGURATION . "
												WHERE publishers_id = '" . (int) $pID . "'";
        $publishers_configuration_result_sql = tep_db_query($publishers_configuration_select_sql);
        while ($publishers_configuration_row = tep_db_fetch_array($publishers_configuration_result_sql)) {
            $publishers_configuration_array[$publishers_configuration_row['publishers_configuration_key']] = $publishers_configuration_row['publishers_configuration_value'];
        }

        $display_html = '	<table border="0" cellspacing="2" cellpadding="2">
								<tr>
									<td class="main">Top-up URL:</td>
									<td class="main">' . tep_draw_input_field('configuration[TOP_UP_URL]', (isset($publishers_configuration_array['TOP_UP_URL']) ? $publishers_configuration_array['TOP_UP_URL'] : ''), '', false) . '
										' . tep_draw_radio_field('configuration[TOP_UP_URL_FLAG]', '1', (isset($publishers_configuration_array['TOP_UP_URL_FLAG']) && $publishers_configuration_array['TOP_UP_URL_FLAG'] == 1 ? 1 : 0)) . ' On
										' . tep_draw_radio_field('configuration[TOP_UP_URL_FLAG]', '0', (!isset($publishers_configuration_array['TOP_UP_URL_FLAG']) || $publishers_configuration_array['TOP_UP_URL_FLAG'] == 0 ? 1 : 0)) . ' Off
									</td>
								</tr>
								<tr>
									<td class="main">Validate Account URL:</td>
									<td class="main">' . tep_draw_input_field('configuration[VALIDATE_GAME_ACC_URL]', (isset($publishers_configuration_array['VALIDATE_GAME_ACC_URL']) ? $publishers_configuration_array['VALIDATE_GAME_ACC_URL'] : ''), '', false) . '
										' . tep_draw_radio_field('configuration[VALIDATE_GAME_ACC_URL_FLAG]', '1', (isset($publishers_configuration_array['VALIDATE_GAME_ACC_URL_FLAG']) && $publishers_configuration_array['VALIDATE_GAME_ACC_URL_FLAG'] == 1 ? 1 : 0)) . ' On
										' . tep_draw_radio_field('configuration[VALIDATE_GAME_ACC_URL_FLAG]', '0', (!isset($publishers_configuration_array['VALIDATE_GAME_ACC_URL_FLAG']) || $publishers_configuration_array['VALIDATE_GAME_ACC_URL_FLAG'] == 0 ? 1 : 0)) . ' Off
									</td>
								</tr>
								<tr>
									<td class="main">Check Status URL:</td>
									<td class="main">' . tep_draw_input_field('configuration[STATUS_URL]', (isset($publishers_configuration_array['STATUS_URL']) ? $publishers_configuration_array['STATUS_URL'] : ''), '', false) . '
										' . tep_draw_radio_field('configuration[STATUS_URL_FLAG]', '1', (isset($publishers_configuration_array['STATUS_URL_FLAG']) && $publishers_configuration_array['STATUS_URL_FLAG'] == 1 ? 1 : 0)) . ' On
										' . tep_draw_radio_field('configuration[STATUS_URL_FLAG]', '0', (!isset($publishers_configuration_array['STATUS_URL_FLAG']) || $publishers_configuration_array['STATUS_URL_FLAG'] == 0 ? 1 : 0)) . ' Off
									</td>
								</tr>
								<tr>
									<td class="main">Get Server URL:</td>
									<td class="main">' . tep_draw_input_field('configuration[SERVER_URL]', (isset($publishers_configuration_array['SERVER_URL']) ? $publishers_configuration_array['SERVER_URL'] : ''), '', false) . '
										' . tep_draw_radio_field('configuration[SERVER_URL_FLAG]', '1', (isset($publishers_configuration_array['SERVER_URL_FLAG']) && $publishers_configuration_array['SERVER_URL_FLAG'] == 1 ? 1 : 0)) . ' On
										' . tep_draw_radio_field('configuration[SERVER_URL_FLAG]', '0', (!isset($publishers_configuration_array['SERVER_URL_FLAG']) || $publishers_configuration_array['SERVER_URL_FLAG'] == 0 ? 1 : 0)) . ' Off
									</td>
								</tr>
								<tr>
									<td class="main">Get Account Platform URL:</td>
									<td class="main">' . tep_draw_input_field('configuration[GET_ACCOUNT_PLATFORM_URL]', (isset($publishers_configuration_array['GET_ACCOUNT_PLATFORM_URL']) ? $publishers_configuration_array['GET_ACCOUNT_PLATFORM_URL'] : ''), '', false) . '
										' . tep_draw_radio_field('configuration[GET_ACCOUNT_PLATFORM_FLAG]', '1', (isset($publishers_configuration_array['GET_ACCOUNT_PLATFORM_FLAG']) && $publishers_configuration_array['GET_ACCOUNT_PLATFORM_FLAG'] == 1 ? 1 : 0)) . ' On
										' . tep_draw_radio_field('configuration[GET_ACCOUNT_PLATFORM_FLAG]', '0', (!isset($publishers_configuration_array['GET_ACCOUNT_PLATFORM_FLAG']) || $publishers_configuration_array['GET_ACCOUNT_PLATFORM_FLAG'] == 0 ? 1 : 0)) . ' Off
									</td>
								</tr>
								<tr>
									<td class="main">Get Character URL:</td>
									<td class="main">' . tep_draw_input_field('configuration[CHARACTER_URL]', (isset($publishers_configuration_array['CHARACTER_URL']) ? $publishers_configuration_array['CHARACTER_URL'] : ''), '', false) . '
										' . tep_draw_radio_field('configuration[CHARACTER_URL_FLAG]', '1', (isset($publishers_configuration_array['CHARACTER_URL_FLAG']) && $publishers_configuration_array['CHARACTER_URL_FLAG'] == 1 ? 1 : 0)) . ' On
										' . tep_draw_radio_field('configuration[CHARACTER_URL_FLAG]', '0', (!isset($publishers_configuration_array['CHARACTER_URL_FLAG']) || $publishers_configuration_array['CHARACTER_URL_FLAG'] == 0 ? 1 : 0)) . ' Off
									</td>
								</tr>
								<tr>
									<td class="main">Get Credit Balance URL:</td>
									<td class="main">' . tep_draw_input_field('configuration[CHECK_CREDIT_BALANCE_URL]', (isset($publishers_configuration_array['CHECK_CREDIT_BALANCE_URL']) ? $publishers_configuration_array['CHECK_CREDIT_BALANCE_URL'] : ''), '', false) . '
										' . tep_draw_radio_field('configuration[CHECK_CREDIT_BALANCE_URL_FLAG]', '1', (isset($publishers_configuration_array['CHECK_CREDIT_BALANCE_URL_FLAG']) && $publishers_configuration_array['CHECK_CREDIT_BALANCE_URL_FLAG'] == 1 ? 1 : 0)) . ' On
										' . tep_draw_radio_field('configuration[CHECK_CREDIT_BALANCE_URL_FLAG]', '0', (!isset($publishers_configuration_array['CHECK_CREDIT_BALANCE_URL_FLAG']) || $publishers_configuration_array['CHECK_CREDIT_BALANCE_URL_FLAG'] == 0 ? 1 : 0)) . ' Off
									</td>
								</tr>
								<tr>
									<td class="main">Get Top-up List URL:</td>
									<td class="main">' . tep_draw_input_field('configuration[SAVE_TOP_UP_LIST_URL]', (isset($publishers_configuration_array['SAVE_TOP_UP_LIST_URL']) ? $publishers_configuration_array['SAVE_TOP_UP_LIST_URL'] : ''), '', false) . '
										' . tep_draw_radio_field('configuration[SAVE_TOP_UP_LIST_URL_FLAG]', '1', (isset($publishers_configuration_array['SAVE_TOP_UP_LIST_URL_FLAG']) && $publishers_configuration_array['SAVE_TOP_UP_LIST_URL_FLAG'] == 1 ? 1 : 0)) . ' On
										' . tep_draw_radio_field('configuration[SAVE_TOP_UP_LIST_URL_FLAG]', '0', (!isset($publishers_configuration_array['SAVE_TOP_UP_LIST_URL_FLAG']) || $publishers_configuration_array['SAVE_TOP_UP_LIST_URL_FLAG'] == 0 ? 1 : 0)) . ' Off
									</td>
								</tr>
								<tr>
									<td class="main">' . ENTRY_PUBLISHERS_SECRET_KEY . ':</td>
									<td class="main">' . tep_draw_password_field('configuration[SECRET_KEY]', (isset($publishers_configuration_array['SECRET_KEY']) ? $publishers_configuration_array['SECRET_KEY'] : ''), '', false) . '</td>
								</tr>
								<tr>
									<td class="main">' . ENTRY_PUBLISHERS_PUBLIC_KEY_FILENAME . ':</td>
									<td class="main">' . tep_draw_input_field('configuration[PUBLIC_KEY]', (isset($publishers_configuration_array['PUBLIC_KEY']) ? $publishers_configuration_array['PUBLIC_KEY'] : ''), '', false) . '</td>
								</tr>
								<tr>
									<td class="main">' . ENTRY_PUBLISHERS_PRIVATE_KEY_FILENAME . ':</td>
									<td class="main">' . tep_draw_input_field('configuration[PRIVATE_KEY]', (isset($publishers_configuration_array['PRIVATE_KEY']) ? $publishers_configuration_array['PRIVATE_KEY'] : ''), '', false) . '</td>
								</tr>
							</table>';
        return $display_html;
    }

    function draw_admin_game_input($action, $publishers_games_array) {
        switch ($action) {
            /* GAME SETTING */
            case 'new_game':
            case 'edit_game':
                $publishers_game_name = '';
                $publishers_games_daily_limit = 0;
                $publishers_games_today_topped_amount = 0;
                $publishers_games_status = 1;
                if ($action == 'edit_game') {
                    $publishers_game_select_sql = "	SELECT publishers_game, categories_id, publishers_games_remark,
														publishers_games_pending_message, publishers_games_reloaded_message,
														publishers_games_failed_message, publishers_games_daily_limit,
														publishers_games_today_topped_amount, publishers_games_status
													FROM " . TABLE_PUBLISHERS_GAMES . " AS pg
													WHERE publishers_games_id = '" . (int) $publishers_games_array['publishers_games_id'] . "'
														AND publishers_id = '" . (int) $publishers_games_array['publishers_id'] . "'";
                    $publishers_game_result_sql = tep_db_query($publishers_game_select_sql);
                    if ($publishers_game_row = tep_db_fetch_array($publishers_game_result_sql)) {
                        $publishers_game_name = $publishers_game_row['publishers_game'];
                        $cat_id = $publishers_game_row['categories_id'];
                        $publishers_games_daily_limit = $publishers_game_row['publishers_games_daily_limit'];
                        $publishers_games_today_topped_amount = $publishers_game_row['publishers_games_today_topped_amount'];
                        $publishers_game_remark = $publishers_game_row['publishers_games_remark'];
                        $publishers_games_pending_message = $publishers_game_row['publishers_games_pending_message'];
                        $publishers_games_reloaded_message = $publishers_game_row['publishers_games_reloaded_message'];
                        $publishers_games_failed_message = $publishers_game_row['publishers_games_failed_message'];
                        $publishers_games_status = $publishers_game_row['publishers_games_status'];
                    }

                    $publishers_games_configuration_select_sql = "	SELECT publishers_games_configuration_value, publishers_games_configuration_key
																	FROM " . TABLE_PUBLISHERS_GAMES_CONFIGURATION . " AS pg
																	WHERE publishers_games_id = '" . (int) $publishers_games_array['publishers_games_id'] . "'";
                    $publishers_games_configuration_result_sql = tep_db_query($publishers_games_configuration_select_sql);
                    while ($publishers_games_configuration_row = tep_db_fetch_array($publishers_games_configuration_result_sql)) {
                        $publishers_games_configuration_array[$publishers_games_configuration_row['publishers_games_configuration_key']] = $publishers_games_configuration_row['publishers_games_configuration_value'];
                    }
                }

                $games_name_array = array();
                if ($action == 'new') {
                    $games_name_array[] = array('id' => '',
                        'text' => PULL_DOWN_DEFAULT);
                }

                tep_db_connect_og();
                $categories_games_array = [];
                $categories_select_sql = "SELECT DISTINCT(categories_id) FROM category";
                $categories_result_sql = tep_db_query($categories_select_sql, 'db_og_link');
                while ($categories_row = tep_db_fetch_array($categories_result_sql)) {
                    $categories_games_array[] = $categories_row['categories_id'];
                }

                $games_name_select_sql = "	SELECT c.categories_id, cd.categories_name
											FROM " . TABLE_CATEGORIES . " as c
											INNER JOIN " . TABLE_CATEGORIES_DESCRIPTION . " as cd
												ON c.categories_id = cd.categories_id
											WHERE c.categories_id IN ('" . implode("','", $categories_games_array) . "')
												AND cd.language_id='1'";
                $games_name_result_sql = tep_db_query($games_name_select_sql);
                while ($games_name_row = tep_db_fetch_array($games_name_result_sql)) {
                    $games_name_array[] = array('id' => $games_name_row['categories_id'],
                        'text' => $games_name_row['categories_name']);
                }

                $display_html = '<table border="0" width="100%" cellspacing="0" cellpadding="2">
					      			<tr>
					        			<td class="formAreaTitle">' . ($action == 'new_game' ? HEADING_TITLE_INSERT : HEADING_TITLE_UPDATE) . '&nbsp;-&nbsp;' . $publishers_row['publishers_name'] . '</td>
					      			</tr>
					      			<tr>
					        			<td class="formArea">
					        				<table border="0" cellspacing="2" cellpadding="2" class="main">
					          					<tr>
					            					<td width="160px">' . ENTRY_GAME_NAME . ':</td>
					            					<td width="400px">' . tep_draw_pull_down_menu('sel_categories_games', $games_name_array, $cat_id) . '</td>
													<td>Top-up URL:</td>
													<td>' . tep_draw_input_field('publishers_games_configuration[TOP_UP_URL]', (isset($publishers_games_configuration_array['TOP_UP_URL']) ? $publishers_games_configuration_array['TOP_UP_URL'] : ''), '', false) . '</td>
													<td>' . tep_draw_checkbox_field('publishers_games_configuration[PARENT_TOP_UP_URL_FLAG]', 1, (isset($publishers_games_configuration_array['PARENT_TOP_UP_URL_FLAG']) || (!isset($publishers_games_configuration_array['TOP_UP_URL']) || !tep_not_null($publishers_games_configuration_array['TOP_UP_URL'])) ? 1 : 0), ' id="chk_top_up_url" ') . ' Follow publisher setting</td>
					          					</tr>
					          					<tr>
					            					<td>' . ENTRY_GAME_STATUS . ':</td>
					            					<td>' . tep_draw_radio_field('publishers_games_status', 1, $publishers_games_status) . 'On ' . tep_draw_radio_field('publishers_games_status', 0, (!$publishers_games_status)) . 'Off </td>
													<td>' . ENTRY_VALIDATE_ACCOUNT_URL . ':</td>
					            					<td>' . tep_draw_input_field('publishers_games_configuration[VALIDATE_GAME_ACC_URL]', (isset($publishers_games_configuration_array['VALIDATE_GAME_ACC_URL']) ? $publishers_games_configuration_array['VALIDATE_GAME_ACC_URL'] : ''), '', false) . '</td>
													<td>' . tep_draw_checkbox_field('publishers_games_configuration[PARENT_VALIDATE_GAME_ACC_URL_FLAG]', 1, (isset($publishers_games_configuration_array['PARENT_VALIDATE_GAME_ACC_URL_FLAG']) || (!isset($publishers_games_configuration_array['VALIDATE_GAME_ACC_URL']) || !tep_not_null($publishers_games_configuration_array['VALIDATE_GAME_ACC_URL'])) ? 1 : 0), '') . ' Follow publisher setting</td>
					          					</tr>
					          					<tr>
					            					<td>' . ENTRY_TOP_UP_DAILY_LIMIT . ':</td>
					            					<td>' . tep_draw_input_field('publishers_top_up_daily_limit', $publishers_games_daily_limit, ' id="publishers_top_up_daily_limit" ', false) . '</td>
													<td class="main">Check Status URL:</td>
													<td class="main">' . tep_draw_input_field('publishers_games_configuration[STATUS_URL]', (isset($publishers_games_configuration_array['STATUS_URL']) ? $publishers_games_configuration_array['STATUS_URL'] : ''), '', false) . '</td>
													<td>' . tep_draw_checkbox_field('publishers_games_configuration[PARENT_STATUS_URL_FLAG]', 1, (isset($publishers_games_configuration_array['PARENT_STATUS_URL_FLAG']) || (!isset($publishers_games_configuration_array['STATUS_URL']) || !tep_not_null($publishers_games_configuration_array['STATUS_URL'])) ? 1 : 0), '') . ' Follow publisher setting</td>
					          					</tr>
					          					<tr>
					            					<td>' . ENTRY_DAILY_TOPPED_AMOUNT . ':</td>
					            					<td>' . number_format($publishers_games_today_topped_amount, 2, ".", "") . '
					            					&nbsp;&nbsp;&nbsp;<a href="' . tep_href_link(FILENAME_PUBLISHERS_GAMES, tep_get_all_get_params(array('pID', 'pgID', 'flag', 'action', 'subaction', 'product_id')) . 'pID=' . (int) $_REQUEST['pID'] . '&pgID=' . (int) $_REQUEST['pgID'] . '&action=edit&subaction=reset_amount') . '">' . LINK_RESET_TOP_UP_AMOUNT . '</a></td>
													<td class="main">Get Server URL:</td>
													<td class="main">' . tep_draw_input_field('publishers_games_configuration[SERVER_URL]', (isset($publishers_games_configuration_array['SERVER_URL']) ? $publishers_games_configuration_array['SERVER_URL'] : ''), '', false) . '</td>
													<td>' . tep_draw_checkbox_field('publishers_games_configuration[PARENT_SERVER_URL_FLAG]', 1, (isset($publishers_games_configuration_array['PARENT_SERVER_URL_FLAG']) || (!isset($publishers_games_configuration_array['SERVER_URL']) || !tep_not_null($publishers_games_configuration_array['SERVER_URL'])) ? 1 : 0), '') . ' Follow publisher setting</td>
					          					</tr>
					          					<tr>
					            					<td>' . ENTRY_GAME_MAPPING . ':</td>
					            					<td>' . tep_draw_input_field('publishers_game', $publishers_game_name, ' id="publishers_game" ', false) . '</td>
													<td class="main">Get Character URL:</td>
													<td class="main">' . tep_draw_input_field('publishers_games_configuration[CHARACTER_URL]', (isset($publishers_games_configuration_array['CHARACTER_URL']) ? $publishers_games_configuration_array['CHARACTER_URL'] : ''), '', false) . '</td>
													<td>' . tep_draw_checkbox_field('publishers_games_configuration[PARENT_CHARACTER_URL_FLAG]', 1, (isset($publishers_games_configuration_array['PARENT_CHARACTER_URL_FLAG']) || (!isset($publishers_games_configuration_array['CHARACTER_URL']) || !tep_not_null($publishers_games_configuration_array['CHARACTER_URL'])) ? 1 : 0), '') . ' Follow publisher setting</td>
					          					</tr>
					          					<tr>
					            					<td valign="top">' . ENTRY_GAME_PENDING_MESSAGE . ':</td>
					            					<td><textarea name="txt_pending_message" cols="50" rows="5">' . $publishers_games_pending_message . '</textarea></td>
													<td class="main">Get Account Platform URL:</td>
													<td class="main">' . tep_draw_input_field('publishers_games_configuration[GET_ACCOUNT_PLATFORM_URL]', (isset($publishers_games_configuration_array['GET_ACCOUNT_PLATFORM_URL']) ? $publishers_games_configuration_array['GET_ACCOUNT_PLATFORM_URL'] : ''), '', false) . '</td>
													<td>' . tep_draw_checkbox_field('publishers_games_configuration[PARENT_ACCOUNT_PLATFORM_FLAG]', 1, (isset($publishers_games_configuration_array['PARENT_ACCOUNT_PLATFORM_FLAG']) || (!isset($publishers_games_configuration_array['GET_ACCOUNT_PLATFORM_URL']) || !tep_not_null($publishers_games_configuration_array['GET_ACCOUNT_PLATFORM_URL'])) ? 1 : 0), '') . ' Follow publisher setting</td>
					          					</tr>
					          					<tr>
					            					<td valign="top">' . ENTRY_GAME_RELOADED_MESSAGE . ':</td>
					            					<td><textarea name="txt_reloaded_message" cols="50" rows="5">' . $publishers_games_reloaded_message . '</textarea></td>
                                                    <td colspan="2">' . tep_draw_checkbox_field('publishers_games_configuration[CONVERT_CUSTOMER_ID_TO_EMAIL_FLAG]', 1, (isset($publishers_games_configuration_array['CONVERT_CUSTOMER_ID_TO_EMAIL_FLAG']) ? 1 : 0), '') . 'Convert Customer ID to Customer Email</td>
					          					</tr>
					          					<tr>
					            					<td valign="top">' . ENTRY_GAME_FAILED_MESSAGE . ':</td>
					            					<td><textarea name="txt_failed_message" cols="50" rows="5">' . $publishers_games_failed_message . '</textarea></td>
					          					</tr>
					          					<tr>
					            					<td valign="top">' . ENTRY_GAME_REMARK . ':</td>
					            					<td><textarea name="txt_remark" cols="50" rows="5">' . $publishers_game_remark . '</textarea></td>
					          					</tr>
								      			<tr>
								      				<td>&nbsp;</td>
								        			<td align="left" class="main">' . ($action == 'new_game' ? '<input type="submit" class="inputButton" value="Insert">' : '<input type="submit" class="inputButton" value="Update">') . '&nbsp;<input type="button" class="inputButton" value="Cancel" onclick="location.href=\'' . tep_href_link(FILENAME_PUBLISHERS_GAMES, tep_get_all_get_params(array('action', 'subaction')) . 'action=edit') . '\'"></td>
								      			</tr>
					        				</table>
					        			</td>
					      			</tr>
					      			<tr>
					        			<td>' . tep_draw_separator('pixel_trans.gif', '1', '10') . '</td>
					      			</tr>
					      		</table>';
                break;
        }
        return $display_html;
    }

    function draw_admin_customer_input($action, $publishers_games_array) {
        switch ($action) {
            /* GAME SETTING */
            case 'new_product':
            case 'edit_product':
                $languages = tep_get_languages();

                $products_id = '';
                $publishers_game_name = '';

                $publishers_game_select_sql = "	SELECT publishers_game
												FROM " . TABLE_PUBLISHERS_GAMES . " AS pg
												WHERE publishers_games_id = '" . (int) $publishers_games_array['publishers_games_id'] . "'
													AND publishers_id = '" . (int) $publishers_games_array['publishers_id'] . "'";
                $publishers_game_result_sql = tep_db_query($publishers_game_select_sql);
                if ($publishers_game_row = tep_db_fetch_array($publishers_game_result_sql)) {
                    $publishers_game_name = $publishers_game_row['publishers_game'];
                }

                $def_amount = '';
                $def_amount_type = 'currency';
                $def_account_flag = 0;
                $def_server_flag = 0;
                $def_character_flag = 0;

                $def_sort_order_account = '50000';

                for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
                    $def_label_account[$languages[$n]['id']] = '';
                }

                $def_sort_order_server = '50000';
                $def_english_label_server = '';
                $def_chinese_label_server = '';

                $def_sort_order_character = '50000';
                $def_english_label_character = '';
                $def_chinese_label_character = '';

                $def_sort_order_account_platform = '50000';
                $def_english_label_account_platform = '';
                $def_chinese_label_account_platform = '';

                $def_sync_publisher_character_flag = 0;
                $def_retype_account_flag = 0;

                $top_up_info_array = array();
                if ($action == 'edit_product') {
                    $publishers_products_select_sql = "	SELECT p.products_id, p.products_cat_path, pd.products_name
														FROM " . TABLE_PUBLISHERS_PRODUCTS . " AS pp
														INNER JOIN " . TABLE_PRODUCTS . " AS p
															ON pp.products_id = p.products_id
														INNER JOIN " . TABLE_PRODUCTS_DESCRIPTION . " AS pd
															ON pp.products_id = pd.products_id
														WHERE pp.publishers_games_id = '" . (int) $publishers_games_array['publishers_games_id'] . "'
															AND pp.products_id = '" . (int) $publishers_games_array['products_id'] . "'
															AND pd.language_id = 1
															AND pd.products_name <> ''";
                    $publishers_products_result_sql = tep_db_query($publishers_products_select_sql);
                    $publishers_products_row = tep_db_fetch_array($publishers_products_result_sql);
                    $products_id = $publishers_products_row['products_id'];
                    $products_cat_path_display = $publishers_products_row['products_cat_path'] . ' > ' . $publishers_products_row['products_name'];

                    $top_up_info_select_sql = "	SELECT top_up_info_key, top_up_info_value, top_up_info_type_id, sort_order, top_up_info_display, languages_id
												FROM " . TABLE_TOP_UP_INFO . " AS tui
												LEFT JOIN " . TABLE_TOP_UP_INFO_LANG . " AS tuil
													ON tui.top_up_info_id = tuil.top_up_info_id
												WHERE products_id = '" . (int) $publishers_games_array['products_id'] . "'";
                    $top_up_info_result_sql = tep_db_query($top_up_info_select_sql);
                    while ($top_up_info_row = tep_db_fetch_array($top_up_info_result_sql)) {
                        switch ($top_up_info_row['top_up_info_key']) {
                            case 'retype_account_flag':
                                $def_retype_account_flag = $top_up_info_row['top_up_info_value'];
                                break;
                            case 'sync_publisher_character_flag':
                                $def_sync_publisher_character_flag = $top_up_info_row['top_up_info_value'];
                                break;
                            case 'amount':
                                $def_amount = $top_up_info_row['top_up_info_value'];
                                break;
                            case 'amount_type':
                                $def_amount_type = $top_up_info_row['top_up_info_value'];
                                break;
                            case 'account':
                                $def_label_account[$top_up_info_row['languages_id']] = $top_up_info_row['top_up_info_display'];
                                $def_sort_order_account = $top_up_info_row['sort_order'];
                                $def_account_flag = 1;
                                break;
                            case 'server':
                                $def_sort_order_server = $top_up_info_row['sort_order'];
                                $def_label_server[$top_up_info_row['languages_id']] = $top_up_info_row['top_up_info_display'];
                                $def_server_flag = 1;
                                break;
                            case 'character':
                                $def_sort_order_character = $top_up_info_row['sort_order'];
                                $def_label_character[$top_up_info_row['languages_id']] = $top_up_info_row['top_up_info_display'];
                                $def_character_flag = 1;
                                break;
                            case 'account_platform':
                                $def_sort_order_account_platform = $top_up_info_row['sort_order'];
                                $def_label_account_platform[$top_up_info_row['languages_id']] = $top_up_info_row['top_up_info_display'];
                                $def_account_platform_flag = 1;
                                break;
                        }
                    }
                }

                $display_html = '<table border="0" cellspacing="2" cellpadding="2" class="main" width="100%">
			          					<tr>
			            					<td width="100px" valign="top">' . ENTRY_PRODUCT_ID . ':</td>
			            					<td class="reportRecords">';
                if ($action == 'new_product') {
                    $display_html .= tep_draw_input_field('product_id', $products_id, ' id="games_product_id" onblur="load_product_cat_path(this.value)" ', false) . '&nbsp;(<a href="javascript:openDGDialog(' . tep_href_link(FILENAME_POPUP_PRODUCTS_LIST, 'direct_top_up=1&fname=log_files.php') . ', 600, 250)">Product List</a>)';
                } else {
                    $display_html .= $products_id . tep_draw_hidden_field('product_id', $products_id);
                }
                $display_html .= '				<div id="div_cat_path">' . (tep_not_null($products_cat_path_display) ? $products_cat_path_display : '') . '</div>
											</td>
			    	      				</tr>
			          					<tr>
			            					<td colspan="2">
			            						<fieldset>
			            							<legend>Configuration</legend>
							        				<table border="0" cellspacing="0" cellpadding="2" class="main">
							          					<tr valign="top" class="reportListingOdd">
							            					<td class="reportBoxHeading" width="100px">&nbsp;</td>
							            					<td class="reportBoxHeading" width="150px">&nbsp;</td>
							            					<td class="reportBoxHeading" width="300px" align="center">Description</td>
							            					<td class="reportBoxHeading" width="100px" align="center">' . TABLE_HEADING_SORT_ORDER . '</td>';
                for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
                    $display_html .= '					<td class="reportBoxHeading" width="100px" align="center">' . $languages[$i]['name'] . '</td>';
                }
                $display_html .= '						<td class="reportBoxHeading" width="150px" align="center">' . TABLE_HEADING_SYSTEM_TYPE . '</td>
															<td class="reportBoxHeading" width="100px">Use in API Signature?</td>
							          					</tr>
							          					<tr valign="top" class="reportListingOdd" onmouseover="rowOverEffect(this, \'reportListingRowOver\')" onmouseout="rowOutEffect(this, \'reportListingOdd\')" onclick="rowClicked(this, \'reportListingOdd\')">
							            					<td class="reportRecords">' . ENTRY_AMOUNT_TYPE . ':</td>
							            					<td class="reportRecords">';
                $amount_type_array = array();
                $amount_type_array[] = array('id' => 'currency', 'text' => 'Currency');
                $amount_type_array[] = array('id' => 'point', 'text' => 'Point');
                $display_html .= tep_draw_pull_down_menu('sel_amount_type', $amount_type_array, $def_amount_type, false);
                $display_html .= '						</td>
							            					<td class="reportRecords">\'point\' or \'currency\' for this top-up amount.</td>
							            					<td class="reportRecords" align="center">-</td>';
                for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
                    $display_html .= '					<td class="reportRecords" align="center">-</td>';
                }
                $display_html .= '							<td class="reportRecords" align="center">-</td>
							            					<td class="reportRecords" align="center"></td>
							          					</tr>
							          					<tr valign="top" class="reportListingEven" onmouseover="rowOverEffect(this, \'reportListingRowOver\')" onmouseout="rowOutEffect(this, \'reportListingEven\')" onclick="rowClicked(this, \'reportListingEven\')">
							            					<td class="reportRecords">' . ENTRY_AMOUNT . ':</td>
							            					<td class="reportRecords">' . tep_draw_input_field('txt_amount', $def_amount, ' id="txt_amount" ', false) . '</td>
							            					<td class="reportRecords">Deno face value to be top-up.<br>(total to be top-up = quantity * amount)</td>
							            					<td class="reportRecords" align="center">-</td>';
                for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
                    $display_html .= '						<td class="reportRecords" align="center">-</td>';
                }
                $display_html .= '         					<td class="reportRecords" align="center">-</td>
							            					<td class="reportRecords" align="center"></td>
							          					</tr>
							          					<tr valign="top" class="reportListingOdd" onmouseover="rowOverEffect(this, \'reportListingRowOver\')" onmouseout="rowOutEffect(this, \'reportListingOdd\')" onclick="rowClicked(this, \'reportListingOdd\')">
							            					<td class="reportRecords">' . ENTRY_ACCOUNT . ':</td>
                                                            <td class="reportRecords">' . tep_draw_checkbox_field('chk_retype_account', 1, $def_retype_account_flag, ' id="chk_retype_account" ') . ' re-type account</td>
															<td class="reportRecords">Game\'s account name to be top-up.</td>
							            					<td class="reportRecords" align="center">' . tep_draw_input_field('txt_sort_order_account', $def_sort_order_account, ' id="txt_sort_order_account" size="10" ', false) . '</td>';
                for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
                    $display_html .= '						<td class="reportRecords" align="center">' . tep_draw_input_field('txt_label_account[' . $languages[$i]['id'] . ']', $def_label_account[$languages[$i]['id']], ' id="txt_label_account_' . $languages[$i]['id'] . '" size="10" ', false) . '</td>';
                }
                $display_html .= '								<td class="reportRecords" nowrap>' .
                        tep_draw_radio_field('rd_account', '1', ($def_account_flag ? true : false)) . '&nbsp;Display&nbsp;&nbsp;' .
                        tep_draw_radio_field('rd_account', '0', (!$def_account_flag ? true : false)) . '&nbsp;Disable
							            					</td>
							            					<td class="reportRecords" align="center">Always</td>
							          					</tr>
							          					<tr valign="top" class="reportListingEven" onmouseover="rowOverEffect(this, \'reportListingRowOver\')" onmouseout="rowOutEffect(this, \'reportListingEven\')" onclick="rowClicked(this,\'reportListingEven\')">
							            					<td class="reportRecords">' . ENTRY_SERVER . ':</td>
							            					<td class="reportRecords">-</td>
							            					<td class="reportRecords">Server ID of the selected game.</td>
							            					<td class="reportRecords" align="center">' . tep_draw_input_field('txt_sort_order_server', $def_sort_order_server, ' id="txt_sort_order_server" size="10" ', false) . '</td>';
                for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
                    $display_html .= '						<td class="reportRecords" align="center">' . tep_draw_input_field('txt_label_server[' . $languages[$i]['id'] . ']', $def_label_server[$languages[$i]['id']], ' id="txt_label_server' . $languages[$i]['id'] . '" size="10" ', false) . '</td>';
                }

                $display_html .= '							<td class="reportRecords" nowrap>' .
                        tep_draw_radio_field('rd_server', '1', ($def_server_flag ? true : false)) . '&nbsp;Display&nbsp;&nbsp;' .
                        tep_draw_radio_field('rd_server', '0', (!$def_server_flag ? true : false)) . '&nbsp;Disable
															</td>
							            					<td class="reportRecords" align="center">Always</td>
							          					</tr>
							          					<tr valign="top" class="reportListingOdd" onmouseover="rowOverEffect(this, \'reportListingRowOver\')" onmouseout="rowOutEffect(this, \'reportListingOdd\')" onclick="rowClicked(this, \'reportListingOdd\')">
							            					<td class="reportRecords">' . ENTRY_CHARACTER . ':</td>
							            					<td class="reportRecords">' . tep_draw_checkbox_field('chk_sync_publisher_character', 1, $def_sync_publisher_character_flag, ' id="chk_sync_publisher_character" ') . ' sync with publisher</td>
							            					<td class="reportRecords">Character in game to be top-up.</td>
							            					<td class="reportRecords" align="center">' . tep_draw_input_field('txt_sort_order_character', $def_sort_order_character, ' id="txt_sort_order_character" size="10" ', false) . '</td>';
                for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
                    $display_html .= '						<td class="reportRecords" align="center">' . tep_draw_input_field('txt_label_character[' . $languages[$i]['id'] . ']', $def_label_character[$languages[$i]['id']], ' id="txt_label_character' . $languages[$i]['id'] . '" size="10" ', false) . '</td>';
                }
                $display_html .= '		  					<td class="reportRecords" nowrap>' .
                        tep_draw_radio_field('rd_character', '1', ($def_character_flag ? true : false)) . '&nbsp;Display&nbsp;&nbsp;' .
                        tep_draw_radio_field('rd_character', '0', (!$def_character_flag ? true : false)) . '&nbsp;Disable
							            					</td>
							            					<td class="reportRecords" align="center">Always</td>
							          					</tr>
							          					<tr valign="top" class="reportListingEven" onmouseover="rowOverEffect(this, \'reportListingRowOver\')" onmouseout="rowOutEffect(this, \'reportListingOdd\')" onclick="rowClicked(this, \'reportListingOdd\')">
							            					<td class="reportRecords">' . ENTRY_ACCOUNT_PLATFORM . ':</td>
							            					<td class="reportRecords">&nbsp;</td>
							            					<td class="reportRecords">Game account platform.</td>
							            					<td class="reportRecords" align="center">' . tep_draw_input_field('txt_sort_order_account_platform', $def_sort_order_account_platform, ' id="txt_sort_order_account_platform" size="10" ', false) . '</td>';
                for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
                    $display_html .= '						<td class="reportRecords" align="center">' . tep_draw_input_field('txt_label_account_platform[' . $languages[$i]['id'] . ']', $def_label_account_platform[$languages[$i]['id']], ' id="txt_label_account_platform' . $languages[$i]['id'] . '" size="10" ', false) . '</td>';
                }
                $display_html .= '		  					<td class="reportRecords" nowrap>' .
                        tep_draw_radio_field('rd_account_platform', '1', ($def_account_platform_flag ? true : false)) . '&nbsp;Display&nbsp;&nbsp;' .
                        tep_draw_radio_field('rd_account_platform', '0', (!$def_account_platform_flag ? true : false)) . '&nbsp;Disable
							            					</td>
							            					<td class="reportRecords" align="center">When \'Display\'</td>
							          					</tr>
							          				</table>
								          		</fieldset>
			            					</td>
			          					</tr>
						      			<tr>
						      				<td>&nbsp;</td>
						        			<td align="left" class="main">' . ($action == 'new_product' ? '<input type="submit" class="inputButton" value="Insert">' : '<input type="submit" class="inputButton" value="Update">') . '&nbsp;<input type="button" class="inputButton" value="Cancel" onclick="location.href=\'' . tep_href_link(FILENAME_PUBLISHERS_GAMES, tep_get_all_get_params(array('action', 'subaction')) . 'action=edit') . '\'"></td>
						      			</tr>
			        				</table>';
                break;
        }
        return $display_html;
    }

    function validate_admin_update_input($param, &$msg) {
        if (!isset($_REQUEST['configuration']['PUBLIC_KEY']) || !$_REQUEST['configuration']['PUBLIC_KEY']) {
            $msg = ERROR_INVALID_PUBLISHERS_PUB_KEY;
            return false;
        } else if (!isset($_REQUEST['configuration']['PRIVATE_KEY']) || !$_REQUEST['configuration']['PRIVATE_KEY']) {
            $msg = ERROR_INVALID_PUBLISHERS_PRI_KEY;
            return false;
        } else if (!isset($_REQUEST['configuration']['SECRET_KEY']) || !$_REQUEST['configuration']['SECRET_KEY']) {
            $msg = ERROR_INVALID_PUBLISHERS_SECRET_KEY;
            return false;
        } else {
            //
        }
        return true;
    }

    function validate_admin_game_input($param, &$msg) {
        return true;
    }

    function validate_admin_customer_input($param, &$msg) {
        return true;
    }

    // call from api directory
    function do_top_up($orders_products_id, $publisher_id, $top_up_id, $amount_type, $amount, $quantity, $account, $character, $game, $server = '', $account_platform = '', $product_array = array()) {
        $action = 'do_top_up';

        if (tep_not_null($character)) {
            if (stristr($character, '##')) {
                $character_tmp = explode("##", $character);
                $character = $character_tmp[0];
            }
        }

        $curl_response_array = array();

        $this->publishers($publisher_id);
        $get_publishers_conf_array = $this->get_publishers_conf();

        if (!isset($get_publishers_conf_array['TOP_UP_URL_FLAG']['publishers_configuration_value']) || !$get_publishers_conf_array['TOP_UP_URL_FLAG']['publishers_configuration_value']) {
            return false;
        }

        $url = '';
        if (isset($product_array['publishers_games_id'])) {
            $top_up_url_flag = $this->get_publishers_games_conf($product_array['publishers_games_id'], 'PARENT_TOP_UP_URL_FLAG');
            if (!$top_up_url_flag) {
                $url = $this->get_publishers_games_conf($product_array['publishers_games_id'], 'TOP_UP_URL');
            }
        }

        if (!tep_not_null($url)) {
            $url = $get_publishers_conf_array['TOP_UP_URL']['publishers_configuration_value'];
        }

        if (!tep_not_null($url)) {
            return false;
        }

        $private_file = (isset($get_publishers_conf_array['PRIVATE_KEY']['publishers_configuration_value']) ? $get_publishers_conf_array['PRIVATE_KEY']['publishers_configuration_value'] : '');
        $public_file = (isset($get_publishers_conf_array['PUBLIC_KEY']['publishers_configuration_value']) ? $get_publishers_conf_array['PUBLIC_KEY']['publishers_configuration_value'] : '');

        if (!tep_not_null($private_file) || !file_exists(DIR_WS_MODULES . 'direct_topup/' . $private_file) || !tep_not_null($public_file) || !file_exists(DIR_WS_MODULES . 'direct_topup/' . $public_file)) {
            $message = '';
            $message .= 'Orders Products ID: ' . $orders_products_id . "\n";
            $message .= 'Publisher ID: ' . $publisher_id . "\n";
            $message .= 'Top Up ID: ' . $top_up_id . "\n";
            $message .= 'Private File: ' . $private_file . "\n";
            $message .= 'Public File: ' . $public_file . "\n";
            @tep_mail('my.dev', '<EMAIL>', 'Missing private/public key - Publisher (' . (int) $publisher_id . ')', $message, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);

            $orders_top_up_remark_data_sql = array('top_up_id' => (int) $top_up_id,
                'data_added' => 'now()',
                'remark' => 'Missing private/public key');
            tep_db_perform(TABLE_ORDERS_TOP_UP_REMARK, $orders_top_up_remark_data_sql);

            $curl_response_array['result_code'] = '';
            return $curl_response_array;
        }

        $timestamp = time();

        $top_up_tag_array = array($top_up_id,
            $account,
            $amount,
            $quantity,
            $game,
            $server,
            $amount_type,
            $character);
        if (tep_not_null($account_platform))
            $top_up_tag_array[] = $account_platform;

        $pre_top_up_tag = implode(":", $top_up_tag_array);
        while (strlen($pre_top_up_tag) > 116 && count($top_up_tag_array) > 0) {
            unset($top_up_tag_array[count($top_up_tag_array) - 1]);
            $pre_top_up_tag = implode(":", $top_up_tag_array);
        }
        $top_up_tag = $this->top_up_tag_encrypt($pre_top_up_tag, DIR_WS_MODULES . 'direct_topup/' . $private_file);

        $param = array('action' => $action,
            'amount_type' => $amount_type,
            'amount' => number_format($amount, 2, '.', ''),
            'quantity' => number_format($quantity, 2, '.', ''),
            'account' => $account,
            'character' => $character,
            'game' => $game,
            'server' => (tep_not_null($server) ? $server : ''),
            'merchant_id' => $get_publishers_conf_array['OGM_MERCHANT_ID']['publishers_configuration_value'],
            'timestamp' => $timestamp,
            'top_up_id' => $top_up_id,
            'top_up_tag' => $top_up_tag);

        if (tep_not_null($account_platform))
            $param['account_platform'] = $account_platform;

        $param['signature'] = $this->get_signature($param, $get_publishers_conf_array['SECRET_KEY']['publishers_configuration_value']);

        /* Start API Request Log */
        $request_log = 'url: ' . $url . "\n";
        ob_start();
        echo "<pre>";
        print_r($param);
        $request_log .= ob_get_contents();
        ob_end_clean();
        $api_log_obj = new direct_topup_api_log($action, $request_log, array('publishers_id' => (int) $publisher_id,
            'top_up_id' => (int) $top_up_id,
            'orders_products_id' => (int) $orders_products_id));
        /* End API Request Log */

        $curl_response = $this->curl_post($url, $param);
        $curl_response_array = json_decode($curl_response, true);

        $result_code = (isset($curl_response_array['result_code']) ? $curl_response_array['result_code'] : 1000);

        // check response signature
        $response_signature = sha1('account=' . (isset($curl_response_array['account']) ? $curl_response_array['account'] : '') .
                (tep_not_null($account_platform) ? '&account_platform=' . $account_platform : '') .
                '&after_balance=' . (isset($curl_response_array['after_balance']) ? $curl_response_array['after_balance'] : '') .
                '&before_balance=' . (isset($curl_response_array['before_balance']) ? $curl_response_array['before_balance'] : '') .
                '&character=' . (isset($curl_response_array['character']) ? $curl_response_array['character'] : '') .
                '&game=' . (isset($curl_response_array['game']) ? $curl_response_array['game'] : '') .
                '&publisher_ref_id=' . (isset($curl_response_array['publisher_ref_id']) ? $curl_response_array['publisher_ref_id'] : '') .
                '&result_code=' . (isset($curl_response_array['result_code']) ? $curl_response_array['result_code'] : '') .
                '&server=' . (isset($curl_response_array['server']) ? $curl_response_array['server'] : '') .
                '&top_up_status=' . (isset($curl_response_array['top_up_status']) ? $curl_response_array['top_up_status'] : '') .
                '&secret_key=' . $get_publishers_conf_array['SECRET_KEY']['publishers_configuration_value']);

        $orders_top_up_data_sql = array('publishers_response_time' => 'now()',
            'top_up_timestamp' => tep_db_prepare_input($timestamp));

        if ($curl_response_array['result_code'] == 2000) {
            $orders_top_up_data_sql['publishers_ref_id'] = tep_db_prepare_input($curl_response_array['publisher_ref_id']);
            $orders_top_up_data_sql['customer_before_balance'] = tep_db_prepare_input($curl_response_array['after_balance']);
            $orders_top_up_data_sql['customer_after_balance'] = tep_db_prepare_input($curl_response_array['before_balance']);
            $orders_top_up_data_sql['game'] = tep_db_prepare_input($curl_response_array['game']);
            $orders_top_up_data_sql['server'] = tep_db_prepare_input((isset($curl_response_array['server']) ? $curl_response_array['server'] : ''));
            $orders_top_up_data_sql['account'] = tep_db_prepare_input((isset($curl_response_array['account']) ? $curl_response_array['account'] : ''));
            // Purposely use `character` to prevent SQL Error due to character is reserve word in MySQL
            $orders_top_up_data_sql['`character`'] = tep_db_prepare_input((isset($curl_response_array['character']) ? $curl_response_array['character'] : ''));

            $top_up_response_info_array = array();
            if (tep_not_null($account_platform)) {
                $top_up_response_info_array['account_platform'] = $account_platform;
            }

            if (count($top_up_response_info_array)) {
                $orders_top_up_data_sql['top_up_response_info'] = tep_db_prepare_input(json_encode($top_up_response_info_array));
            }
        }

        if (isset($curl_response_array['top_up_status'])) {
            switch (strtolower($curl_response_array['top_up_status'])) {
                case 'reloaded':
                    if (strcmp($response_signature, $curl_response_array['signature']) != 0) {
                        $curl_response_array['top_up_status'] = 'failed';
                        $orders_top_up_data_sql['top_up_status'] = '10';
                        $result_code = '1002';
                        $orders_top_up_remark_data_sql = array('top_up_id' => (int) $top_up_id,
                            'data_added' => 'now()',
                            'remark' => $this->get_result_code_description($result_code));
                        tep_db_perform(TABLE_ORDERS_TOP_UP_REMARK, $orders_top_up_remark_data_sql);
                    } else {
                        $orders_top_up_data_sql['top_up_status'] = '3';
                        $orders_top_up_remark_data_sql = array('top_up_id' => (int) $top_up_id,
                            'data_added' => 'now()',
                            'remark' => 'Top-up: Reloaded');
                        tep_db_perform(TABLE_ORDERS_TOP_UP_REMARK, $orders_top_up_remark_data_sql);
                    }
                    break;
                case 'failed':
                    $orders_top_up_data_sql['top_up_status'] = '10';
                    $orders_top_up_remark_data_sql = array('top_up_id' => (int) $top_up_id,
                        'data_added' => 'now()',
                        'remark' => 'Top-up: Failed, ' . $this->get_result_code_description($curl_response_array['result_code']));
                    tep_db_perform(TABLE_ORDERS_TOP_UP_REMARK, $orders_top_up_remark_data_sql);
                    break;
                case 'not_found':
                    $orders_top_up_data_sql['top_up_status'] = '11';

                    $orders_top_up_remark_data_sql = array('top_up_id' => (int) $top_up_id,
                        'data_added' => 'now()',
                        'remark' => 'Top-up: Not found');
                    tep_db_perform(TABLE_ORDERS_TOP_UP_REMARK, $orders_top_up_remark_data_sql);

                    break;
                case 'pending':
                    $orders_top_up_data_sql['top_up_status'] = '1';

                    //reset
                    tep_db_query("DELETE FROM " . TABLE_TOP_UP_QUEUE . " WHERE top_up_id = '" . (int) $top_up_id . "'");

                    $top_up_queue_data_sql = array('top_up_id' => (int) $top_up_id,
                        'counter' => '0',
                        'check_date' => '0000-00-00 00:00:00',
                        'date_added' => 'now()');
                    tep_db_perform(TABLE_TOP_UP_QUEUE, $top_up_queue_data_sql);

                    $orders_top_up_remark_data_sql = array('top_up_id' => (int) $top_up_id,
                        'data_added' => 'now()',
                        'remark' => 'Top-up: Pending');
                    tep_db_perform(TABLE_ORDERS_TOP_UP_REMARK, $orders_top_up_remark_data_sql);

                    break;
                default:
                    $orders_top_up_data_sql['top_up_status'] = '1';
                    break;
            }
        }

        tep_db_perform(TABLE_ORDERS_TOP_UP, $orders_top_up_data_sql, 'update', " top_up_id = '" . (int) $top_up_id . "' ");

        /* Start API Response Log */
        $api_log_obj->end_log($result_code, $curl_response, array('publishers_id' => (int) $publisher_id,
            'top_up_id' => (int) $top_up_id,
            'publisher_ref_id' => $curl_response_array['publisher_ref_id']));
        /* End API Response Log */

        // end check response signature
        return $curl_response_array;
    }

    function validate_game_acc($publisher_id, $games_acc_array, &$curl_response_array = '') {
        $action = 'validate_game_acc';

        $this->publishers($publisher_id);
        $get_publishers_conf_array = $this->get_publishers_conf();

        if (!isset($get_publishers_conf_array['VALIDATE_GAME_ACC_URL_FLAG']['publishers_configuration_value']) || !$get_publishers_conf_array['VALIDATE_GAME_ACC_URL_FLAG']['publishers_configuration_value']) {
            return true;
        }

        $url = '';
        if (isset($games_acc_array['publishers_games_id'])) {
            $validate_game_acc_url_flag = $this->get_publishers_games_conf($games_acc_array['publishers_games_id'], 'PARENT_VALIDATE_GAME_ACC_URL_FLAG');
            if (!$validate_game_acc_url_flag) {
                $url = $this->get_publishers_games_conf($games_acc_array['publishers_games_id'], 'VALIDATE_GAME_ACC_URL');
            }
            $customer_id_conversion = $this->get_publishers_games_conf($games_acc_array['publishers_games_id'], 'CONVERT_CUSTOMER_ID_TO_EMAIL_FLAG');
            if ($customer_id_conversion == '1') {
                $converted_account = $this->convert_customer_email_to_id($games_acc_array['account']);
                $games_acc_array['account'] = !empty($converted_account) ? $converted_account : $games_acc_array['account'];
            }
        }

        if (!tep_not_null($url)) {
            $url = $get_publishers_conf_array['VALIDATE_GAME_ACC_URL']['publishers_configuration_value'];
        }

        if (!tep_not_null($url)) {
            return true;
        }

        $param = array('action' => $action,
            'account' => '',
            'character' => '',
            'game' => '',
            'server' => '',
            'merchant_id' => $get_publishers_conf_array['OGM_MERCHANT_ID']['publishers_configuration_value']);
        if (isset($games_acc_array['account']))
            $param['account'] = $games_acc_array['account'];
        if (isset($games_acc_array['character'])) {
            if (stristr($games_acc_array['character'], '##')) {
                $character_tmp = explode("##", $games_acc_array['character']);
                $param['character'] = $character_tmp[0];
            } else {
                $param['character'] = $games_acc_array['character'];
            }
        }
        if (isset($games_acc_array['account_platform']))
            $param['account_platform'] = $games_acc_array['account_platform'];

        if (isset($games_acc_array['game']))
            $param['game'] = $games_acc_array['game'];
        if (isset($games_acc_array['server']))
            $param['server'] = $games_acc_array['server'];

        $param['signature'] = $this->get_signature($param, $get_publishers_conf_array['SECRET_KEY']['publishers_configuration_value']);

        /* optional, don't have to include in signature , for publisher use to do their own topup limit checking */
        if (isset($games_acc_array['amount_type']))
            $param['amount_type'] = $games_acc_array['amount_type'];
        if (isset($games_acc_array['amount']))
            $param['amount'] = $games_acc_array['amount'];
        if (isset($games_acc_array['quantity']))
            $param['quantity'] = $games_acc_array['quantity'];
        /* end optional, don't have to include in signature */

        /* Start API Request Log */
        $request_log = 'url: ' . $url . "\n";
        ob_start();
        echo "<pre>";
        print_r($param);
        $request_log .= ob_get_contents();
        ob_end_clean();
        $api_log_obj = new direct_topup_api_log($action, $request_log, array('publishers_id' => (int) $publisher_id));
        /* End API Request Log */

        $curl_obj = new curl();
        $curl_response = $curl_obj->curl_post($url, $param);

        $curl_response_array = json_decode($curl_response, true);

        $result_code = (isset($curl_response_array['result_code']) ? $curl_response_array['result_code'] : 1000);

        // check response signature
        $response_signature = sha1('game_acc_status=' . (isset($curl_response_array['game_acc_status']) ? $curl_response_array['game_acc_status'] : '') . '&result_code=' . (isset($curl_response_array['result_code']) ? $curl_response_array['result_code'] : '') . '&secret_key=' . $get_publishers_conf_array['SECRET_KEY']['publishers_configuration_value']);
        if (strcmp($response_signature, $curl_response_array['signature']) != 0) {
            $result_code = '1002';
        }
        // end check response signature

        /* Start API Response Log */
        $api_log_obj->end_log($result_code, $curl_response, array('publishers_id' => (int) $publisher_id));
        /* End API Response Log */

        return ($result_code == '2000' && isset($curl_response_array['game_acc_status']) && $curl_response_array['game_acc_status'] == 'RELOADABLE' ? true : false );
    }

    function get_character_list($publisher_id, $games_acc_array, &$curl_response_array) {
        $action = 'get_character_list';

        $this->publishers($publisher_id);
        $get_publishers_conf_array = $this->get_publishers_conf();

        if (!isset($get_publishers_conf_array['CHARACTER_URL_FLAG']['publishers_configuration_value']) || !$get_publishers_conf_array['CHARACTER_URL_FLAG']['publishers_configuration_value']) {
            return true;
        }

        $url = '';
        if (isset($games_acc_array['publishers_games_id'])) {
            $get_character_list_flag = $this->get_publishers_games_conf($games_acc_array['publishers_games_id'], 'PARENT_CHARACTER_URL_FLAG');
            if (!$get_character_list_flag) {
                $url = $this->get_publishers_games_conf($games_acc_array['publishers_games_id'], 'CHARACTER_URL');
            }
            $customer_id_conversion = $this->get_publishers_games_conf($games_acc_array['publishers_games_id'], 'CONVERT_CUSTOMER_ID_TO_EMAIL_FLAG');
            if ($customer_id_conversion == '1') {
                $converted_account = $this->convert_customer_email_to_id($games_acc_array['account']);
                $games_acc_array['account'] = !empty($converted_account) ? $converted_account : $games_acc_array['account'];
            }
        }

        if (!tep_not_null($url)) {
            $url = $get_publishers_conf_array['CHARACTER_URL']['publishers_configuration_value'];
        }

        if (!tep_not_null($url)) {
            return true;
        }

        $param = array('action' => $action,
            'account' => '',
            'game' => '',
            'server' => '',
            'merchant_id' => $get_publishers_conf_array['OGM_MERCHANT_ID']['publishers_configuration_value']);
        if (isset($games_acc_array['account']))
            $param['account'] = $games_acc_array['account'];
        if (isset($games_acc_array['game']))
            $param['game'] = $games_acc_array['game'];
        if (isset($games_acc_array['server']))
            $param['server'] = $games_acc_array['server'];

        if (isset($games_acc_array['account_platform']))
            $param['account_platform'] = $games_acc_array['account_platform'];

        $param['signature'] = $this->get_signature($param, $get_publishers_conf_array['SECRET_KEY']['publishers_configuration_value']);

        /* Start API Request Log */
        $request_log = 'url: ' . $url . "\n";
        ob_start();
        echo "<pre>";
        print_r($param);
        $request_log .= ob_get_contents();
        ob_end_clean();
        $api_log_obj = new direct_topup_api_log($action, $request_log, array('publishers_id' => (int) $publisher_id));
        /* End API Request Log */

        $curl_obj = new curl();
        $curl_response = $curl_obj->curl_post($url, $param);
        $curl_response_array = json_decode($curl_response, true);

        $result_code = (isset($curl_response_array['result_code']) ? $curl_response_array['result_code'] : 1000);

        /* Start API Response Log */
        $api_log_obj->end_log($result_code, $curl_response, array('publishers_id' => (int) $publisher_id));
        /* End API Response Log */

        return (isset($curl_response_array['characters']) ? $curl_response_array['characters'] : array());
    }

    /* function to check OffGamers' orders status */

    function check_top_up_status($publisher_id, $publisher_ref_id) {
        $action = 'check_top_up_status';

        $this->publishers($publisher_id);
        $get_publishers_conf_array = $this->get_publishers_conf();

        if (!isset($get_publishers_conf_array['STATUS_URL_FLAG']['publishers_configuration_value']) || !$get_publishers_conf_array['STATUS_URL_FLAG']['publishers_configuration_value']) {
            return true;
        }

        $url = '';
        if (isset($games_acc_array['publishers_games_id'])) {
            $status_url_flag = $this->get_publishers_games_conf($games_acc_array['publishers_games_id'], 'PARENT_STATUS_URL_FLAG');
            if (!$status_url_flag) {
                $url = $this->get_publishers_games_conf($games_acc_array['publishers_games_id'], 'STATUS_URL');
            }
        }

        if (!tep_not_null($url)) {
            $url = $get_publishers_conf_array['STATUS_URL']['publishers_configuration_value'];
        }

        if (!tep_not_null($url)) {
            return true;
        }

        $orders_products_select_sql = "	SELECT orders_products_id, top_up_id
										FROM " . TABLE_ORDERS_TOP_UP . "
										WHERE publishers_id = '" . (int) $publisher_id . "'
											AND publishers_ref_id = '" . $publisher_ref_id . "'";
        $orders_products_result_sql = tep_db_query($orders_products_select_sql);
        if ($orders_products_row = tep_db_fetch_array($orders_products_result_sql)) {
            $param = array('action' => $action,
                'merchant_id' => $get_publishers_conf_array['OGM_MERCHANT_ID']['publishers_configuration_value'],
                'publisher_ref_id' => $publisher_ref_id);
            $param['signature'] = $this->get_signature($param, $get_publishers_conf_array['SECRET_KEY']['publishers_configuration_value']);

            /* Start API Request Log */
            $request_log = 'url: ' . $url . "\n";
            ob_start();
            echo "<pre>";
            print_r($param);
            $request_log .= ob_get_contents();
            ob_end_clean();
            $api_log_obj = new direct_topup_api_log($action, $request_log, array('publishers_id' => (int) $publisher_id, 'publisher_ref_id' => $publisher_ref_id));
            /* End API Request Log */

            $curl_obj = new curl();
            $curl_response = $curl_obj->curl_post($url, $param);
            $curl_response_array = json_decode($curl_response, true);

            if (isset($curl_response_array['result_code'])) {
                $result_code = $curl_response_array['result_code'];

                $top_up_status_data_sql = array();
                switch (strtolower($curl_response_array['top_up_status'])) {
                    case 'reloaded':
                        $top_up_status_data_sql['top_up_status'] = '3';
                        break;
                    case 'failed':
                        $top_up_status_data_sql['top_up_status'] = '10';
                        break;
                    case 'not_found':
                        $top_up_status_data_sql['top_up_status'] = '11';
                        break;
                    default:
                        $top_up_status_data_sql['top_up_status'] = '1';
                        break;
                }
                $top_up_status_data_sql['publishers_response_time'] = 'now()';
                tep_db_perform(TABLE_ORDERS_TOP_UP, $top_up_status_data_sql, 'update', " orders_products_id = '" . $orders_products_row['orders_products_id'] . "' ");

                $orders_top_up_remark_data_sql = array('top_up_id' => $orders_products_row['top_up_id'],
                    'data_added' => 'now()',
                    'remark' => 'Top-up Status: ' . $curl_response_array['top_up_status']);
                tep_db_perform(TABLE_ORDERS_TOP_UP_REMARK, $orders_top_up_remark_data_sql);
            } else {
                $orders_top_up_remark_data_sql = array('top_up_id' => $orders_products_row['top_up_id'],
                    'data_added' => 'now()',
                    'remark' => 'Failed to check top-up status, suspect API not implemented by Publisher.');
                tep_db_perform(TABLE_ORDERS_TOP_UP_REMARK, $orders_top_up_remark_data_sql);
            }

            /* Start API Response Log */
            $api_log_obj->end_log($result_code, $curl_response, array('publishers_id' => (int) $publisher_id, 'publisher_ref_id' => $publisher_ref_id));
            /* End API Response Log */
        }

        return $curl_response_array;
    }

    function get_credit_balance($publisher_id, $get_publishers_conf_array) {
        $action = 'check_credit_balance';

        if (!isset($get_publishers_conf_array['GET_CREDIT_BALANCE_URL_FLAG']['publishers_configuration_value']) || !$get_publishers_conf_array['GET_CREDIT_BALANCE_URL_FLAG']['publishers_configuration_value']) {
            return true;
        }

        $url = '';
        if (isset($games_acc_array['publishers_games_id'])) {
            $get_credit_balance_flag = $this->get_publishers_games_conf($games_acc_array['publishers_games_id'], 'PARENT_GET_CREDIT_BALANCE_URL_FLAG');
            if (!$get_credit_balance_flag) {
                $url = $this->get_publishers_games_conf($games_acc_array['publishers_games_id'], 'GET_CREDIT_BALANCE_URL');
            }
        }

        if (!tep_not_null($url)) {
            $url = $get_publishers_conf_array['GET_CREDIT_BALANCE_URL']['publishers_configuration_value'];
        }

        if (!tep_not_null($url)) {
            return true;
        }

        $param = array('action' => $action,
            'merchant_id' => $get_publishers_conf_array['OGM_MERCHANT_ID']['publishers_configuration_value']);
        $param['signature'] = $this->get_signature($param, $get_publishers_conf_array['SECRET_KEY']['publishers_configuration_value']);

        /* Start API Request Log */
        $request_log = 'url: ' . $url . "\n";
        ob_start();
        echo "<pre>";
        print_r($param);
        $request_log .= ob_get_contents();
        ob_end_clean();
        $api_log_obj = new direct_topup_api_log($action, $request_log, array('publishers_id' => (int) $publisher_id));
        /* End API Request Log */

        $curl_response = $this->curl_post($url, $param);
        $curl_response_array = json_decode($curl_response, true);

        $result_code = (isset($curl_response_array['result_code']) ? $curl_response_array['result_code'] : 1000);

        /* Start API Response Log */
        $api_log_obj->end_log($result_code, $curl_response, array('publishers_id' => (int) $publisher_id));
        /* End API Response Log */

        return $curl_response_array;
    }

    function get_signature($param, $key) {
        $action = array();
        if (isset($param['action'])) {
            $action['action'] = $param['action'];
        }
        unset($param['action']);
        ksort($param);
        reset($param);

        $param = array_merge($action, $param);

        $signature_array = array();
        foreach ($param as $key_loop => $data_loop) {
            $signature_array[] = $key_loop . '=' . $data_loop;
        }
        $signature_array[] = 'secret_key=' . $key;

        return sha1(implode("&", $signature_array));
    }

    public function verify_signature($params, $expected_sig, $key) {
        return $this->get_signature($params, $key) == $expected_sig;
    }

    public function get_server_list($publisher_id, $game_info, $get_publishers_conf_array) {
        $action = 'get_server_list';
        $curl_response_array = array();

        if (tep_not_null($game_info)) {

            if (!isset($get_publishers_conf_array['SERVER_URL_FLAG']['publishers_configuration_value']) || !$get_publishers_conf_array['SERVER_URL_FLAG']['publishers_configuration_value']) {
                return true;
            }

            $url = '';
            if (isset($game_info['publishers_games_id'])) {
                $get_server_list_flag = $this->get_publishers_games_conf($game_info['publishers_games_id'], 'PARENT_SERVER_URL_FLAG');
                if (!$get_server_list_flag) {
                    $url = $this->get_publishers_games_conf($game_info['publishers_games_id'], 'SERVER_URL');
                }
            }

            if (!tep_not_null($url)) {
                $url = $get_publishers_conf_array['SERVER_URL']['publishers_configuration_value'];
            }

            if (!tep_not_null($url)) {
                return true;
            }

            $param = array('action' => $action,
                'game' => $game_info['publishers_game'],
                'merchant_id' => $get_publishers_conf_array['OGM_MERCHANT_ID']['publishers_configuration_value']);
            $param['signature'] = $this->get_signature($param, $get_publishers_conf_array['SECRET_KEY']['publishers_configuration_value']);

            /* Start API Request Log */
            $request_log = 'url: ' . $url . "\n";
            ob_start();
            echo "<pre>";
            print_r($param);
            $request_log .= ob_get_contents();
            ob_end_clean();
            $api_log_obj = new direct_topup_api_log($action, $request_log, array('publishers_id' => (int) $publisher_id));
            /* End API Request Log */

            $curl_obj = new curl();
            $curl_response = $curl_obj->curl_post($url, $param);
            $curl_response_array = json_decode($curl_response, true);

            $result_code = (isset($curl_response_array['result_code']) ? $curl_response_array['result_code'] : 1000);

            /* Start API Response Log */
            $api_log_obj->end_log($result_code, $curl_response, array('publishers_id' => (int) $publisher_id));
            /* End API Response Log */
        }

        return $curl_response_array;
    }

    public function get_account_platform_list($publisher_id, $game_info, $get_publishers_conf_array) {
        $action = 'get_account_platform_list';
        $curl_response_array = array();

        if (tep_not_null($game_info)) {
            if (!isset($get_publishers_conf_array['GET_ACCOUNT_PLATFORM_FLAG']['publishers_configuration_value']) || (int) $get_publishers_conf_array['GET_ACCOUNT_PLATFORM_FLAG']['publishers_configuration_value'] == 0) {
                return $curl_response_array;
            }

            $url = '';
            if (isset($game_info['publishers_games_id'])) {
                if (method_exists($this, 'get_publishers_games_conf')) {
                    $get_account_platform_list_flag = $this->get_publishers_games_conf($game_info['publishers_games_id'], 'PARENT_ACCOUNT_PLATFORM_FLAG');
                } else {
                    include_once(DIR_WS_CLASSES . 'direct_topup.php');
                    $get_account_platform_list_flag = direct_topup::get_publishers_games_conf($game_info['publishers_games_id'], 'PARENT_ACCOUNT_PLATFORM_FLAG');
                }

                if (!$get_account_platform_list_flag) {
                    $url = $get_publishers_conf_array['GET_ACCOUNT_PLATFORM_URL']['publishers_configuration_value'];
                }
            }

            if (!tep_not_null($url)) {
                $url = $get_publishers_conf_array['GET_ACCOUNT_PLATFORM_URL']['publishers_configuration_value'];
            }

            if (!tep_not_null($url)) {
                return $curl_response_array;
            }

            $param = array('action' => $action,
                'game' => $game_info,
                'merchant_id' => $get_publishers_conf_array['OGM_MERCHANT_ID']['publishers_configuration_value']);
            $param['signature'] = $this->get_signature($param, $get_publishers_conf_array['SECRET_KEY']['publishers_configuration_value']);

            /* Start API Request Log */
            $request_log = 'url: ' . $url . "\n";
            ob_start();
            echo "<pre>";
            print_r($param);
            $request_log .= ob_get_contents();
            ob_end_clean();
            $api_log_obj = new direct_topup_api_log($action, $request_log, array('publishers_id' => (int) $publisher_id));
            /* End API Request Log */

            $curl_obj = new curl();

            $curl_response = $curl_obj->curl_post($url, $param);

            $curl_response_array = json_decode($curl_response, true);

            $result_code = (isset($curl_response_array['result_code']) ? $curl_response_array['result_code'] : 1000);

            /* Start API Response Log */
            $api_log_obj->end_log($result_code, $curl_response, array('publishers_id' => (int) $publisher_id));
            /* End API Response Log */
        }
        return $curl_response_array;
    }

    public function cron_update_server_list($publisher_id = '') {
        // GET publishers_games_id, publishers_id, publishers_game
        $publishers_game_array = $this->get_publishers_game($publisher_id);

        foreach ($publishers_game_array as $publisher_id => $publisher_game_array) {
            $this->publishers($publisher_id);
            $get_publishers_conf_array = $this->get_publishers_conf();

            foreach ($publisher_game_array as $game_id => $publishers_game) {
                $server_list_array = $this->get_server_list($publisher_id, $publishers_game, $get_publishers_conf_array);

                if (isset($server_list_array['servers']) && tep_not_null($server_list_array['servers'])) {
                    $this->set_publishers_game_server($game_id, $server_list_array['servers']);
                    unset($server_list_array);
                }
            }
        }

        unset($publishers_product_id_array);
    }

    public function cron_check_credit_balance() {
        $publishers_array = $this->get_all_publishers();
        $return_array = array();

        foreach ($publishers_array as $publisher_id => $publisher_info) {
            if ($publisher_info['publishers_status']) {
                $this->publishers($publisher_id);
                $get_publishers_conf_array = $this->get_publishers_conf();

                if (!isset($get_publishers_conf_array['API_CHECK_TOPUP_STATUS']['publishers_configuration_value']) || !$get_publishers_conf_array['API_CHECK_TOPUP_STATUS']['publishers_configuration_value'])
                    continue;

                if (!isset($get_publishers_conf_array['CHECK_CREDIT_BALANCE_URL_FLAG']['publishers_configuration_value']) || !$get_publishers_conf_array['SAVE_TOP_UP_LIST_URL_FLAG']['publishers_configuration_value']) {
                    return true;
                }
                $url = $get_publishers_conf_array['CHECK_CREDIT_BALANCE_URL']['publishers_configuration_value'];

                if (tep_not_null($url)) {
                    $return_array = $this->get_credit_balance($publisher_id, $get_publishers_conf_array);

                    $signature = $return_array['signature'];
                    $min_balance = $get_publishers_conf_array['MIN_BALANCE']['publishers_configuration_value'];
                    $min_balance_currency = $get_publishers_conf_array['MIN_BALANCE_CURRENCY']['publishers_configuration_value'];

                    unset($return_array['signature']);

                    if (!tep_not_null($signature) || (!$this->verify_signature($return_array, $signature, $get_publishers_conf_array['SECRET_KEY']['publishers_configuration_value']))) {
                        break;
                    } elseif (isset($return_array['merchant_balance']) && tep_not_null($return_array['merchant_balance'])) {
                        // Check currency accuracy
                        if ($return_array['merchant_currency'] != $min_balance_currency) {
                            $subject = "PUBLISHER ({$publisher_id}) CURRENCY CONFLICT";
                            $message = 'BALANCE REMAIN : ' . $return_array['merchant_balance'] . '<br>'
                                    . 'RETURN CURRENCY : ' . $return_array['merchant_currency'] . '<br>'
                                    . 'EXISTING CURRENCY : ' . $min_balance_currency . '<br>'
                                    . 'DATE TIME : ' . date("d/m/y H:i:s");

                            $email_to_array = tep_parse_email_string('my.dev <<EMAIL>>'); //Wei Chen <<EMAIL>>,

                            for ($email_to_cnt = 0; $email_to_cnt < count($email_to_array); $email_to_cnt++) {
                                @tep_mail($email_to_array[$email_to_cnt]['name'], $email_to_array[$email_to_cnt]['email'], implode(' ', array(EMAIL_SUBJECT_PREFIX, sprintf($subject, $publisher_id))), $message, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
                            }
                        } else if ($return_array['merchant_balance'] < $min_balance) {
                            $subject = "PUBLISHER ({$publisher_id}) BALANCE INSUFFICIENT";
                            $message = 'BALANCE REMAIN : ' . $return_array['merchant_balance'] . '<br>'
                                    . 'MIN BALANCE : ' . $min_balance . '<br>'
                                    . 'CURRENCY : ' . $return_array['merchant_currency'] . '<br>'
                                    . 'DATE TIME : ' . date("d/m/y H:i:s");

                            $email_to_array = tep_parse_email_string('my.dev <<EMAIL>>'); //Wei Chen <<EMAIL>>,

                            for ($email_to_cnt = 0; $email_to_cnt < count($email_to_array); $email_to_cnt++) {
                                @tep_mail($email_to_array[$email_to_cnt]['name'], $email_to_array[$email_to_cnt]['email'], implode(' ', array(EMAIL_SUBJECT_PREFIX, sprintf($subject, $publisher_id))), $message, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
                            }
                        }
                    }
                }
                unset($get_publishers_conf_array);
            }
        }
    }

    function top_up_tag_encrypt($text, $private_file) {
        // OPEN PRIVATE KEY FILE
        $fp = fopen($private_file, "r");
        $priv_key = fread($fp, 8192);
        fclose($fp);
        $res = openssl_get_privatekey($priv_key);

        // ENCRYPT DATA
        openssl_private_encrypt($text, $crypttext, $res);
        $crypttext = base64_encode($crypttext);
        return $crypttext;
    }

    function top_up_tag_decrypt($crypttext, $public_file) {
        // OPEN PUBLIC KEY FILE
        $fp = fopen($public_file, "r");
        $pub_key = fread($fp, 8192);
        fclose($fp);
        openssl_get_publickey($pub_key);

        // DECRYPT DATA
        $crypttext = base64_decode($crypttext);
        $crypttext2 = $crypttext;
        $newsource = "";
        openssl_public_decrypt($crypttext2, $newsource, $pub_key);
        return $newsource;
    }

    function save_top_up_list($publisher_id, $start_time, $end_time = '', $filename = '') {
        $this->publishers($publisher_id);
        $get_publishers_conf_array = $this->get_publishers_conf();

        if (!isset($get_publishers_conf_array['SAVE_TOP_UP_LIST_URL_FLAG']['publishers_configuration_value']) || !$get_publishers_conf_array['SAVE_TOP_UP_LIST_URL_FLAG']['publishers_configuration_value']) {
            return true;
        }

        $url = $get_publishers_conf_array['SAVE_TOP_UP_LIST_URL']['publishers_configuration_value'];
        if (!tep_not_null($url))
            return;

        if (!tep_not_null($end_time)) {
            $end_time = $start_time + (24 * 60 * 60);
        }

        $publishers_name_sql = "SELECT publishers_name
								FROM " . TABLE_PUBLISHERS . "
								WHERE publishers_id = '" . (int) $publisher_id . "'";
        $publishers_name_result = tep_db_query($publishers_name_sql);
        $publishers_name_row = tep_db_fetch_array($publishers_name_result);
        $publishers_name = $publishers_name_row['publishers_name'];

        $start_date_str = date("Y-m-d H:i:s", $start_time);
        $end_date_str = date("Y-m-d H:i:s", $end_time);

        $email_content = '';
        $email_content .= 'Publisher: ' . $publishers_name . "(" . (int) $publisher_id . ")\n";
        $email_content .= 'Start: ' . $start_date_str . "\n";
        $email_content .= 'End: ' . $end_date_str . "\n";

        $param = array('action' => 'get_top_up_list',
            'starttime' => $start_time,
            'endtime' => $end_time,
            'merchant_id' => $get_publishers_conf_array['OGM_MERCHANT_ID']['publishers_configuration_value']
        );
        $param['signature'] = $this->get_signature($param, $get_publishers_conf_array['SECRET_KEY']['publishers_configuration_value']);

        $curl_response = $this->curl_post($url, $param, $filename);

        $checked_array = array();

        if (tep_not_null($filename) && file_exists($filename)) {
            $curl_handle = fopen($filename, "r");

            $data_mismatch = 0;
            $email_flag = 0;
            $success_flag = 0;
            $failed_str = '';
            $missing_str = '';

            while (($data = fgetcsv($curl_handle, 1000, ",")) !== FALSE) {
                if (count($data) == 3) {
                    $top_up_id = $data[0];
                    $qty = $data[1];
                    $amount = $data[2];

                    $checked_array[] = $top_up_id;

                    if ((int) $top_up_id > 0) {
                        $orders_top_up_sql = "	SELECT op.orders_id, otu.top_up_id, tui.top_up_info_value, op.products_delivered_quantity
				    							FROM " . TABLE_ORDERS_TOP_UP . " AS otu
				    							INNER JOIN " . TABLE_ORDERS_PRODUCTS . " AS op
				    								ON op.orders_products_id = otu.orders_products_id
				    							INNER JOIN " . TABLE_TOP_UP_INFO . " AS tui
				    								ON op.products_id = tui.products_id
				    									AND tui.top_up_info_key = 'amount'
				    							WHERE otu.top_up_id = '" . (int) $top_up_id . "'
				    								AND otu.publishers_id = '" . (int) $publisher_id . "'
				    								AND otu.top_up_status = '3'"; // success top-up only
                        $orders_top_up_result = tep_db_query($orders_top_up_sql);
                        if ($orders_top_up_row = tep_db_fetch_array($orders_top_up_result)) {
                            if ((int) $qty == (int) $orders_top_up_row['products_delivered_quantity'] && number_format($amount, 2, '.', '') == number_format(($orders_top_up_row['top_up_info_value'] * (int) $orders_top_up_row['products_delivered_quantity']), 2, '.', '')) {
                                $success_flag++;
                            } else {
                                $failed_str .= " - " . $orders_top_up_row['orders_id'] . ' - ' . $orders_top_up_row['top_up_id'] . ' - ' . number_format($amount, 2, '.', '') . ' (qty: ' . $qty . ') != ' . number_format($orders_top_up_row['top_up_info_value'] * $orders_top_up_row['products_delivered_quantity'], 2, '.', '') . ' (qty: ' . $orders_top_up_row['products_delivered_quantity'] . ")\n";
                                $data_mismatch = 1;
                            }
                        } else {
                            $missing_str .= " - " . $top_up_id . "\n";
                            $data_mismatch = 1;
                        }
                    }
                }
            }
            fclose($curl_handle);
        }

        if ($success_flag > 0) {
            $email_content .= "\n\nSuccessed Top-up: True ( " . $success_flag . " topped )";
            $email_flag = 1;
        }

        if (tep_not_null($failed_str)) {
            $email_content .= "\n\nTop-up not matched:\n" . $failed_str;
            $email_flag = 1;
        }

        if (tep_not_null($missing_str)) {
            $email_content .= "\n\nTop-up ID successed at publisher's side but not in OffGamers:\n" . $missing_str;
            $email_flag = 1;
        }

        $topped_trans_sql = "	SELECT otu.top_up_id, op.orders_id
                                FROM " . TABLE_ORDERS_TOP_UP . " AS otu
                                INNER JOIN " . TABLE_ORDERS_PRODUCTS . " AS op
                                ON op.orders_products_id = otu.orders_products_id
                                WHERE otu.publishers_id = '" . (int) $publisher_id . "'
                                    AND otu.top_up_timestamp >= '" . $start_time . "'
                                    AND otu.top_up_timestamp <= '" . $end_time . "'
                                    AND otu.top_up_status = '3'";
        if (count($checked_array) > 0) {
            $topped_trans_sql .= " AND top_up_id NOT IN ('" . implode("','", $checked_array) . "')";
        }
        $topped_trans_result = tep_db_query($topped_trans_sql);

        if (tep_db_num_rows($topped_trans_result)) {
            $email_flag = 1;
            $email_content .= "\n\n";
            $email_content .= "Top-up ID successed in OffGamers but not at publisher's side:\n";
            while ($topped_trans_row = tep_db_fetch_array($topped_trans_result)) {
                $email_content .= $topped_trans_row['orders_id'] . ' - ' . $topped_trans_row["top_up_id"] . "\n";
                $data_mismatch = 1;
            }
        }

        if ($email_flag) {
            @tep_mail('my.dev', '<EMAIL>', '[OFFGAMERS] Top-up Report (' . (int) $publisher_id . ') - ' . $start_date_str . " - " . $end_date_str, $email_content, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
        }

        if ($data_mismatch) {
            $publishers_data_sql = array('publishers_status' => 0);
            tep_db_perform(TABLE_PUBLISHERS, $publishers_data_sql, 'update', " publishers_id = '" . (int) $publisher_id . "' ");

            @tep_mail('my.dev', '<EMAIL>', '[OFFGAMERS] Turn off publisher due to data mismatched', $email_content, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
        }
    }

}

?>