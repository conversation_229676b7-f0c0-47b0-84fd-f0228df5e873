<?php

include_once(DIR_WS_CLASSES . 'direct_topup.php');
include_once(DIR_WS_CLASSES . 'curl.php');
include_once(DIR_WS_CLASSES . 'currencies.php');
include_once(DIR_WS_CLASSES . 'slack_notification.php');

class dtu_unipin extends direct_topup
{
    public $title, $enable;

    private $request_url, $key, $secret, $error_code, $error_msg, $game_id;

    const GAME_LIST = 'in-game-topup/list';
    const GAME_DETAIL = 'in-game-topup/detail';
    const VALIDATE_USER = 'in-game-topup/user/validate';
    const CREATE_ORDER = 'in-game-topup/order/create';
    const CHECK_ORDER = 'in-game-topup/order/inquiry';

    const MIN_MARGIN = 1, LOW_MARGIN = 3;

    function dtu_unipin()
    {
        $this->title = 'Unipin';
        $this->enable = true;
    }

    function get_title()
    {
        return $this->title;
    }

    function get_enable()
    {
        return $this->enable;
    }

    function draw_admin_input($pID = '')
    {
        $publishers_configuration_array = array();
        $publishers_configuration_select_sql = "SELECT publishers_configuration_key, publishers_configuration_value
												FROM " . TABLE_PUBLISHERS_CONFIGURATION . "
												WHERE publishers_id = '" . (int)$pID . "'";
        $publishers_configuration_result_sql = tep_db_query($publishers_configuration_select_sql);
        while ($publishers_configuration_row = tep_db_fetch_array($publishers_configuration_result_sql)) {
            $publishers_configuration_array[$publishers_configuration_row['publishers_configuration_key']] = $publishers_configuration_row['publishers_configuration_value'];
        }

        $display_html = '<style>.toggle_a{cursor:pointer;color:blue;text-decoration: underline}</style><script>function toggleItem(a){var item=$(a).next();if(item.hasClass("hide")){item.show().removeClass("hide");}else{item.hide().addClass("hide");}}</script><table border="0" cellspacing="2" cellpadding="2">
								<tr>
									<td class="main">API Url</td>
									<td class="main">' . tep_draw_input_field('configuration[TOP_UP_URL]', (isset($publishers_configuration_array['TOP_UP_URL']) ? $publishers_configuration_array['TOP_UP_URL'] : ''), '', false) . ' 
									</td>
								</tr>
                                <tr>
									<td class="main">' . ENTRY_PUBLISHERS_SECRET_KEY . ':</td>
									<td class="main">' . tep_draw_password_field('configuration[SECRET_KEY]', (isset($publishers_configuration_array['SECRET_KEY']) ? $publishers_configuration_array['SECRET_KEY'] : ''), '', false) . '</td>
								</tr>' .
            (isset($publishers_configuration_array['GAME_LIST']) ?
                '<tr><td class="main" valign="top">Game List</td><td class="main">' .
                '<a class="toggle_a" onclick="toggleItem(this);">[toggle]</a><pre class="hide" style="display:none">' . json_encode(json_decode($publishers_configuration_array['GAME_LIST']), JSON_PRETTY_PRINT) . '</pre>' . '<textarea name="configuration[GAME_LIST]" style="display:none;">' . (isset($publishers_configuration_array['GAME_LIST']) ? $publishers_configuration_array['GAME_LIST'] : '') . '</textarea></td></tr>' : '') .
            '</table>';
        return $display_html;
    }

    function draw_admin_game_input($action, $publishers_games_array)
    {
        switch ($action) {
            /* GAME SETTING */
            case 'new_game':
            case 'edit_game':
                $publishers_game_name = '';
                $publishers_games_daily_limit = 0;
                $publishers_games_today_topped_amount = 0;
                $publishers_games_status = 1;
                if ($action == 'edit_game') {
                    $publishers_game_select_sql = "	SELECT publishers_game, categories_id, publishers_games_remark, 
														publishers_games_pending_message, publishers_games_reloaded_message, 
														publishers_games_failed_message, publishers_games_daily_limit, 
														publishers_games_today_topped_amount, publishers_games_status 
													FROM " . TABLE_PUBLISHERS_GAMES . " AS pg
													WHERE publishers_games_id = '" . (int)$publishers_games_array['publishers_games_id'] . "'
														AND publishers_id = '" . (int)$publishers_games_array['publishers_id'] . "'";
                    $publishers_game_result_sql = tep_db_query($publishers_game_select_sql);
                    if ($publishers_game_row = tep_db_fetch_array($publishers_game_result_sql)) {
                        $publishers_game_name = $publishers_game_row['publishers_game'];
                        $cat_id = $publishers_game_row['categories_id'];
                        $publishers_games_daily_limit = $publishers_game_row['publishers_games_daily_limit'];
                        $publishers_games_today_topped_amount = $publishers_game_row['publishers_games_today_topped_amount'];
                        $publishers_game_remark = $publishers_game_row['publishers_games_remark'];
                        $publishers_games_pending_message = $publishers_game_row['publishers_games_pending_message'];
                        $publishers_games_reloaded_message = $publishers_game_row['publishers_games_reloaded_message'];
                        $publishers_games_failed_message = $publishers_game_row['publishers_games_failed_message'];
                        $publishers_games_status = $publishers_game_row['publishers_games_status'];
                    }

                    $publishers_games_configuration_select_sql = "	SELECT publishers_games_configuration_value, publishers_games_configuration_key 
																	FROM " . TABLE_PUBLISHERS_GAMES_CONFIGURATION . " AS pg
																	WHERE publishers_games_id = '" . (int)$publishers_games_array['publishers_games_id'] . "'";
                    $publishers_games_configuration_result_sql = tep_db_query($publishers_games_configuration_select_sql);
                    while ($publishers_games_configuration_row = tep_db_fetch_array($publishers_games_configuration_result_sql)) {
                        $publishers_games_configuration_array[$publishers_games_configuration_row['publishers_games_configuration_key']] = $publishers_games_configuration_row['publishers_games_configuration_value'];
                    }
                }

                $games_name_array = array();
                if ($action == 'new') {
                    $games_name_array[] = array(
                        'id' => '',
                        'text' => PULL_DOWN_DEFAULT
                    );
                }

                tep_db_connect_og();
                $categories_games_array = [];
                $categories_select_sql = "SELECT DISTINCT(categories_id) FROM category";
                $categories_result_sql = tep_db_query($categories_select_sql, 'db_og_link');
                while ($categories_row = tep_db_fetch_array($categories_result_sql)) {
                    $categories_games_array[] = $categories_row['categories_id'];
                }

                $games_name_select_sql = "	SELECT c.categories_id, cd.categories_name
											FROM " . TABLE_CATEGORIES . " as c
											INNER JOIN " . TABLE_CATEGORIES_DESCRIPTION . " as cd
												ON c.categories_id = cd.categories_id 
											WHERE c.categories_id IN ('" . implode("','", $categories_games_array) . "')
												AND cd.language_id='1'";
                $games_name_result_sql = tep_db_query($games_name_select_sql);
                while ($games_name_row = tep_db_fetch_array($games_name_result_sql)) {
                    $games_name_array[] = array(
                        'id' => $games_name_row['categories_id'],
                        'text' => $games_name_row['categories_name']
                    );
                }

                $display_html = '<style>.toggle_a{cursor:pointer;color:blue;text-decoration: underline}</style><script>function toggleItem(a){var item=$(a).next();if(item.hasClass("hide")){item.show().removeClass("hide");}else{item.hide().addClass("hide");}}</script><table border="0" width="100%" cellspacing="0" cellpadding="2">
					      			<tr>
					        			<td class="formAreaTitle">' . ($action == 'new_game' ? HEADING_TITLE_INSERT : HEADING_TITLE_UPDATE) . '</td>
					      			</tr>
					      			<tr>
					        			<td class="formArea">
					        				<table border="0" cellspacing="2" cellpadding="2" class="main">
					          					<tr>
					            					<td width="160px">' . ENTRY_GAME_NAME . ':</td>
					            					<td width="400px">' . tep_draw_pull_down_menu('sel_categories_games', $games_name_array, $cat_id) . '</td>
					          					    <td rowspan="50" valign="top">
					          					        <table border="0" cellspacing="2" cellpadding="2">
					          					        <tbody>
					          					        <tr>
					          					            <td class="main" valign="top">Field Mapping:</td>
                                                            <td class="main"><a class="toggle_a" onclick="toggleItem(this);">[toggle]</a><pre class="hide" style="display:none">' . (isset($publishers_games_configuration_array['ACCOUNT_FIELD_MAPPING']) ? json_encode(json_decode($publishers_games_configuration_array['ACCOUNT_FIELD_MAPPING'], 1), JSON_PRETTY_PRINT) : '') . '
</pre><textarea name="publishers_games_configuration[ACCOUNT_FIELD_MAPPING]" style="display:none;">' . (isset($publishers_games_configuration_array['ACCOUNT_FIELD_MAPPING']) ? $publishers_games_configuration_array['ACCOUNT_FIELD_MAPPING'] : '') . '</textarea><textarea style="display:none;" name="publishers_games_configuration[ACCOUNT_DENO_LIST]">' . (isset($publishers_games_configuration_array['ACCOUNT_DENO_LIST']) ? $publishers_games_configuration_array['ACCOUNT_DENO_LIST'] : '') . '</textarea></td>
                                                        </tr>
                                                        <tr>
					          					            <td class="main" valign="top">Deno List:</td>
                                                            <td class="main"><a class="toggle_a" onclick="toggleItem(this);">[toggle]</a><pre class="hide" style="display:none">' . (isset($publishers_games_configuration_array['ACCOUNT_DENO_LIST']) ? json_encode(json_decode($publishers_games_configuration_array['ACCOUNT_DENO_LIST'], 1), JSON_PRETTY_PRINT) : '') . '
</pre></td>
                                                        </tr>
                                                        </table>
                                                    </td>
					          					</tr>
					          					<tr>
                                                    <td>' . ENTRY_GAME_STATUS . ':</td>
					            					<td>' . tep_draw_radio_field('publishers_games_status', 1, $publishers_games_status) . 'On ' . tep_draw_radio_field('publishers_games_status', 0, (!$publishers_games_status)) . 'Off 
					            					</td>
</tr>
					          					<tr>
					            					<td>' . ENTRY_TOP_UP_DAILY_LIMIT . ':</td>
					            					<td>' . tep_draw_input_field('publishers_top_up_daily_limit', $publishers_games_daily_limit, ' id="publishers_top_up_daily_limit" ', false) . '</td>
					          					</tr>
					          					<tr>
					            					<td>' . ENTRY_DAILY_TOPPED_AMOUNT . ':</td>
					            					<td>' . number_format($publishers_games_today_topped_amount, 2, ".", "") . '
					            					&nbsp;&nbsp;&nbsp;<a href="' . tep_href_link(FILENAME_PUBLISHERS_GAMES, tep_get_all_get_params(array(
                            'pID',
                            'pgID',
                            'flag',
                            'action',
                            'subaction',
                            'product_id'
                        )) . 'pID=' . (int)$_REQUEST['pID'] . '&pgID=' . (int)$_REQUEST['pgID'] . '&action=edit&subaction=reset_amount') . '">' . LINK_RESET_TOP_UP_AMOUNT . '</a></td>
					          					</tr>
					          					<tr>
					            					<td>' . ENTRY_GAME_MAPPING . ':</td>
					            					<td>' . tep_draw_input_field('publishers_game', $publishers_game_name, ' id="publishers_game" ', false) . '</td>
					          					</tr>
					          					<tr>
					            					<td valign="top">' . ENTRY_GAME_PENDING_MESSAGE . ':</td>
					            					<td><textarea name="txt_pending_message" cols="50" rows="5">' . $publishers_games_pending_message . '</textarea></td>
													<td class="main" colspan="3">&nbsp;</td>
					          					</tr>
					          					<tr>
					            					<td valign="top">' . ENTRY_GAME_RELOADED_MESSAGE . ':</td>
					            					<td><textarea name="txt_reloaded_message" cols="50" rows="5">' . $publishers_games_reloaded_message . '</textarea></td>
					          					</tr>
					          					<tr>
					            					<td valign="top">' . ENTRY_GAME_FAILED_MESSAGE . ':</td>
					            					<td><textarea name="txt_failed_message" cols="50" rows="5">' . $publishers_games_failed_message . '</textarea></td>
					          					</tr>
					          					<tr>
					            					<td valign="top">' . ENTRY_GAME_REMARK . ':</td>
					            					<td><textarea name="txt_remark" cols="50" rows="5">' . $publishers_game_remark . '</textarea></td>
					          					</tr>
								      			<tr>
								      				<td>&nbsp;</td>
								        			<td align="left" class="main">' . ($action == 'new_game' ? '<input type="submit" class="inputButton" value="Insert">' : '<input type="submit" class="inputButton" value="Update">') . '&nbsp;<input type="button" class="inputButton" value="Cancel" onclick="location.href=\'' . tep_href_link(FILENAME_PUBLISHERS_GAMES, tep_get_all_get_params(array('action', 'subaction')) . 'action=edit') . '\'"></td>
								      			</tr>
					        				</table>
					        			</td>
					      			</tr>
					      			<tr>
					        			<td>' . tep_draw_separator('pixel_trans.gif', '1', '10') . '</td>
					      			</tr>
					      		</table>';
                break;
        }
        return $display_html;
    }

    function draw_admin_customer_input($action, $publishers_games_array)
    {
        switch ($action) {
            /* GAME SETTING */
            case 'new_product':
            case 'edit_product':
                $languages = tep_get_languages();

                $products_id = '';
                $publishers_game_name = '';

                $publishers_game_select_sql = "	SELECT publishers_game
												FROM " . TABLE_PUBLISHERS_GAMES . " AS pg
												WHERE publishers_games_id = '" . (int)$publishers_games_array['publishers_games_id'] . "'
													AND publishers_id = '" . (int)$publishers_games_array['publishers_id'] . "'";
                $publishers_game_result_sql = tep_db_query($publishers_game_select_sql);
                if ($publishers_game_row = tep_db_fetch_array($publishers_game_result_sql)) {
                    $publishers_game_name = $publishers_game_row['publishers_game'];
                }

                $def_amount = '';
                $def_amount_type = 'currency';
                $def_product_code = '';
                $def_account_flag = 0;
                $def_server_flag = 0;
                $def_character_flag = 0;

                $def_sort_order_account = '50000';

                for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
                    $def_label_account[$languages[$n]['id']] = '';
                }

                $def_sort_order_server = '50000';
                $def_english_label_server = '';
                $def_chinese_label_server = '';

                $def_sort_order_character = '50000';
                $def_english_label_character = '';
                $def_chinese_label_character = '';

                $def_sort_order_account_platform = '50000';
                $def_english_label_account_platform = '';
                $def_chinese_label_account_platform = '';

                $def_sync_publisher_character_flag = 0;

                $top_up_info_array = array();
                if ($action == 'edit_product') {
                    $publishers_products_select_sql = "	SELECT p.products_id, p.products_cat_path, pd.products_name 
														FROM " . TABLE_PUBLISHERS_PRODUCTS . " AS pp 
														INNER JOIN " . TABLE_PRODUCTS . " AS p 
															ON pp.products_id = p.products_id
														INNER JOIN " . TABLE_PRODUCTS_DESCRIPTION . " AS pd 
															ON pp.products_id = pd.products_id
														WHERE pp.publishers_games_id = '" . (int)$publishers_games_array['publishers_games_id'] . "'
															AND pp.products_id = '" . (int)$publishers_games_array['products_id'] . "' 
															AND pd.language_id = 1 
															AND pd.products_name <> ''";
                    $publishers_products_result_sql = tep_db_query($publishers_products_select_sql);
                    $publishers_products_row = tep_db_fetch_array($publishers_products_result_sql);
                    $products_id = $publishers_products_row['products_id'];
                    $products_cat_path_display = $publishers_products_row['products_cat_path'] . ' > ' . $publishers_products_row['products_name'];

                    $top_up_info_select_sql = "	SELECT top_up_info_key, top_up_info_value, top_up_info_type_id, sort_order, top_up_info_display, languages_id 
												FROM " . TABLE_TOP_UP_INFO . " AS tui
												LEFT JOIN " . TABLE_TOP_UP_INFO_LANG . " AS tuil 
													ON tui.top_up_info_id = tuil.top_up_info_id
												WHERE products_id = '" . (int)$publishers_games_array['products_id'] . "'";
                    $top_up_info_result_sql = tep_db_query($top_up_info_select_sql);
                    while ($top_up_info_row = tep_db_fetch_array($top_up_info_result_sql)) {
                        switch ($top_up_info_row['top_up_info_key']) {
                            case 'sync_publisher_character_flag':
                                $def_sync_publisher_character_flag = $top_up_info_row['top_up_info_value'];
                                break;
                            case 'amount':
                                $def_amount = $top_up_info_row['top_up_info_value'];
                                break;
                            case 'amount_type':
                                $def_amount_type = $top_up_info_row['top_up_info_value'];
                                break;
                            case 'product_code':
                                $def_product_code = $top_up_info_row['top_up_info_value'];
                                break;
                            case 'account':
                                $def_label_account[$top_up_info_row['languages_id']] = $top_up_info_row['top_up_info_display'];
                                $def_sort_order_account = $top_up_info_row['sort_order'];
                                $def_account_flag = 1;
                                break;
                            case 'server':
                                $def_sort_order_server = $top_up_info_row['sort_order'];
                                $def_label_server[$top_up_info_row['languages_id']] = $top_up_info_row['top_up_info_display'];
                                $def_server_flag = 1;
                                break;
                            case 'character':
                                $def_sort_order_character = $top_up_info_row['sort_order'];
                                $def_label_character[$top_up_info_row['languages_id']] = $top_up_info_row['top_up_info_display'];
                                $def_character_flag = 1;
                                break;
                            case 'account_platform':
                                $def_sort_order_account_platform = $top_up_info_row['sort_order'];
                                $def_label_account_platform[$top_up_info_row['languages_id']] = $top_up_info_row['top_up_info_display'];
                                $def_account_platform_flag = 1;
                                break;
                        }
                    }
                }

                $display_html = '<table border="0" cellspacing="2" cellpadding="2" class="main" width="100%">
			          					<tr>
			            					<td width="100px" valign="top">' . ENTRY_PRODUCT_ID . ':</td>
			            					<td class="reportRecords">';
                if ($action == 'new_product') {
                    $display_html .= tep_draw_input_field('product_id', $products_id, ' id="games_product_id" onblur="load_product_cat_path(this.value)" ', false) . '&nbsp;(<a href="javascript:openDGDialog(' . tep_href_link(FILENAME_POPUP_PRODUCTS_LIST, 'direct_top_up=1&fname=log_files.php') . ', 600, 250)">Product List</a>)';
                } else {
                    $display_html .= $products_id . tep_draw_hidden_field('product_id', $products_id);
                }
                $display_html .= '				<div id="div_cat_path">' . (tep_not_null($products_cat_path_display) ? $products_cat_path_display : '') . '</div>
											</td>
			    	      				</tr>
			          					<tr>
			            					<td colspan="2">
			            						<fieldset>
			            							<legend>Configuration</legend>
							        				<table border="0" cellspacing="0" cellpadding="2" class="main">
							          					<tr valign="top" class="reportListingOdd">
							            					<td class="reportBoxHeading" width="100px">&nbsp;</td>
							            					<td class="reportBoxHeading" width="150px">&nbsp;</td>
							            					<td class="reportBoxHeading" width="300px" align="center">Description</td>
							            					<td class="reportBoxHeading" width="100px" align="center">' . TABLE_HEADING_SORT_ORDER . '</td>';
                for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
                    $display_html .= '					<td class="reportBoxHeading" width="100px" align="center">' . $languages[$i]['name'] . '</td>';
                }
                $display_html .= '						<td class="reportBoxHeading" width="150px" align="center">' . TABLE_HEADING_SYSTEM_TYPE . '</td>
															<td class="reportBoxHeading" width="100px">Use in API Signature?</td>
							          					</tr>
							          					<tr valign="top" class="reportListingOdd" onmouseover="rowOverEffect(this, \'reportListingRowOver\')" onmouseout="rowOutEffect(this, \'reportListingOdd\')" onclick="rowClicked(this, \'reportListingOdd\')">
							            					<td class="reportRecords">' . ENTRY_AMOUNT_TYPE . ':</td>
							            					<td class="reportRecords">';
                $amount_type_array = array();
                $amount_type_array[] = array('id' => 'currency', 'text' => 'Currency');
                $amount_type_array[] = array('id' => 'point', 'text' => 'Point');
                $display_html .= tep_draw_pull_down_menu('sel_amount_type', $amount_type_array, $def_amount_type, false);
                $display_html .= '						</td>
							            					<td class="reportRecords">\'point\' or \'currency\' for this top-up amount.</td>
							            					<td class="reportRecords" align="center">-</td>';
                for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
                    $display_html .= '					<td class="reportRecords" align="center">-</td>';
                }
                $display_html .= '							<td class="reportRecords" align="center">-</td>
							            					<td class="reportRecords" align="center"></td>
							          					</tr>
							          					<tr valign="top" class="reportListingEven" onmouseover="rowOverEffect(this, \'reportListingRowOver\')" onmouseout="rowOutEffect(this, \'reportListingEven\')" onclick="rowClicked(this, \'reportListingEven\')">
							            					<td class="reportRecords">' . ENTRY_AMOUNT . ':</td>
							            					<td class="reportRecords">' . tep_draw_input_field('txt_amount', $def_amount, ' id="txt_amount" ', false) . '</td>
							            					<td class="reportRecords">Deno face value to be top-up.<br>(total to be top-up = quantity * amount)</td>
							            					<td class="reportRecords" align="center">-</td>';
                for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
                    $display_html .= '						<td class="reportRecords" align="center">-</td>';
                }
                $display_html .= '         					<td class="reportRecords" align="center">-</td>
							            					<td class="reportRecords" align="center"></td>
							          					</tr>
                                                        <tr valign="top" class="reportListingEven" onmouseover="rowOverEffect(this, \'reportListingRowOver\')" onmouseout="rowOutEffect(this, \'reportListingEven\')" onclick="rowClicked(this, \'reportListingEven\')">
							            					<td class="reportRecords">' . ENTRY_PRODUCT_CODE . ':</td>
							            					<td class="reportRecords">' . tep_draw_input_field('txt_product_code', $def_product_code, ' id="txt_product_code" ', false) . '</td>
							            					<td class="reportRecords">Product reference code used at Publisher end</td>
							            					<td class="reportRecords" align="center">-</td>';
                for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
                    $display_html .= '						<td class="reportRecords" align="center">-</td>';
                }
                $display_html .= '         					<td class="reportRecords" align="center">-</td>
							            					<td class="reportRecords" align="center"></td>
							          					</tr>
							          					<tr valign="top" class="reportListingOdd" onmouseover="rowOverEffect(this, \'reportListingRowOver\')" onmouseout="rowOutEffect(this, \'reportListingOdd\')" onclick="rowClicked(this, \'reportListingOdd\')">
							            					<td class="reportRecords">' . ENTRY_ACCOUNT . ':</td>
							            					<td class="reportRecords">-</td>
															<td class="reportRecords">Game\'s account name to be top-up.</td>
							            					<td class="reportRecords" align="center">' . tep_draw_input_field('txt_sort_order_account', $def_sort_order_account, ' id="txt_sort_order_account" size="10" ', false) . '</td>';
                for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
                    $display_html .= '						<td class="reportRecords" align="center">' . tep_draw_input_field('txt_label_account[' . $languages[$i]['id'] . ']', $def_label_account[$languages[$i]['id']], ' id="txt_label_account_' . $languages[$i]['id'] . '" size="10" ', false) . '</td>';
                }
                $display_html .= '								<td class="reportRecords" nowrap>' . tep_draw_radio_field('rd_account', '1', ($def_account_flag ? true : false)) . '&nbsp;Display&nbsp;&nbsp;' . tep_draw_radio_field('rd_account', '0', (!$def_account_flag ? true : false)) . '&nbsp;Disable
							            					</td>
							            					<td class="reportRecords" align="center">Always</td>
							          					</tr>
							          					<tr valign="top" class="reportListingEven" onmouseover="rowOverEffect(this, \'reportListingRowOver\')" onmouseout="rowOutEffect(this, \'reportListingEven\')" onclick="rowClicked(this,\'reportListingEven\')">
							            					<td class="reportRecords">' . ENTRY_SERVER . ':</td>
							            					<td class="reportRecords">-</td>
							            					<td class="reportRecords">Server ID of the selected game.</td>
							            					<td class="reportRecords" align="center">' . tep_draw_input_field('txt_sort_order_server', $def_sort_order_server, ' id="txt_sort_order_server" size="10" ', false) . '</td>';
                for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
                    $display_html .= '						<td class="reportRecords" align="center">' . tep_draw_input_field('txt_label_server[' . $languages[$i]['id'] . ']', $def_label_server[$languages[$i]['id']], ' id="txt_label_server' . $languages[$i]['id'] . '" size="10" ', false) . '</td>';
                }

                $display_html .= '							<td class="reportRecords" nowrap>' . tep_draw_radio_field('rd_server', '1', ($def_server_flag ? true : false)) . '&nbsp;Display&nbsp;&nbsp;' . tep_draw_radio_field('rd_server', '0', (!$def_server_flag ? true : false)) . '&nbsp;Disable
															</td>
							            					<td class="reportRecords" align="center">Always</td>
							          					</tr>
							          					<tr valign="top" class="reportListingOdd" onmouseover="rowOverEffect(this, \'reportListingRowOver\')" onmouseout="rowOutEffect(this, \'reportListingOdd\')" onclick="rowClicked(this, \'reportListingOdd\')">
							            					<td class="reportRecords">' . ENTRY_CHARACTER . ':</td>
							            					<td class="reportRecords">' . tep_draw_checkbox_field('chk_sync_publisher_character', 1, $def_sync_publisher_character_flag, ' id="chk_sync_publisher_character" ') . ' sync with publisher</td>
							            					<td class="reportRecords">Character in game to be top-up.</td>
							            					<td class="reportRecords" align="center">' . tep_draw_input_field('txt_sort_order_character', $def_sort_order_character, ' id="txt_sort_order_character" size="10" ', false) . '</td>';
                for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
                    $display_html .= '						<td class="reportRecords" align="center">' . tep_draw_input_field('txt_label_character[' . $languages[$i]['id'] . ']', $def_label_character[$languages[$i]['id']], ' id="txt_label_character' . $languages[$i]['id'] . '" size="10" ', false) . '</td>';
                }
                $display_html .= '		  					<td class="reportRecords" nowrap>' . tep_draw_radio_field('rd_character', '1', ($def_character_flag ? true : false)) . '&nbsp;Display&nbsp;&nbsp;' . tep_draw_radio_field('rd_character', '0', (!$def_character_flag ? true : false)) . '&nbsp;Disable
							            					</td>
							            					<td class="reportRecords" align="center">Always</td>
							          					</tr>
							          					<tr valign="top" class="reportListingEven" onmouseover="rowOverEffect(this, \'reportListingRowOver\')" onmouseout="rowOutEffect(this, \'reportListingOdd\')" onclick="rowClicked(this, \'reportListingOdd\')">
							            					<td class="reportRecords">' . ENTRY_ACCOUNT_PLATFORM . ':</td>
							            					<td class="reportRecords">&nbsp;</td>
							            					<td class="reportRecords">Game account platform.</td>
							            					<td class="reportRecords" align="center">' . tep_draw_input_field('txt_sort_order_account_platform', $def_sort_order_account_platform, ' id="txt_sort_order_account_platform" size="10" ', false) . '</td>';
                for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
                    $display_html .= '						<td class="reportRecords" align="center">' . tep_draw_input_field('txt_label_account_platform[' . $languages[$i]['id'] . ']', $def_label_account_platform[$languages[$i]['id']], ' id="txt_label_account_platform' . $languages[$i]['id'] . '" size="10" ', false) . '</td>';
                }
                $display_html .= '		  					<td class="reportRecords" nowrap>' . tep_draw_radio_field('rd_account_platform', '1', ($def_account_platform_flag ? true : false)) . '&nbsp;Display&nbsp;&nbsp;' . tep_draw_radio_field('rd_account_platform', '0', (!$def_account_platform_flag ? true : false)) . '&nbsp;Disable
							            					</td>
							            					<td class="reportRecords" align="center">Always</td>
							          					</tr>
							          				</table>
								          		</fieldset>
			            					</td>
			          					</tr>
						      			<tr>
						      				<td>&nbsp;</td>
						        			<td align="left" class="main">' . ($action == 'new_product' ? '<input type="submit" class="inputButton" value="Insert">' : '<input type="submit" class="inputButton" value="Update">') . '&nbsp;<input type="button" class="inputButton" value="Cancel" onclick="location.href=\'' . tep_href_link(FILENAME_PUBLISHERS_GAMES, tep_get_all_get_params(array('action', 'subaction')) . 'action=edit') . '\'"></td>
						      			</tr>
			        				</table>';
                break;
        }
        return $display_html;
    }

    function validate_admin_update_input($param, &$msg)
    {
        if (!isset($_REQUEST['configuration']['SECRET_KEY']) || !$_REQUEST['configuration']['SECRET_KEY']) {
            $msg = ERROR_INVALID_PUBLISHERS_SECRET_KEY;
            return false;
        }
        return true;
    }

    function validate_admin_game_input($param, &$msg)
    {
        return true;
    }

    function validate_admin_customer_input($param, &$msg)
    {
        return true;
    }

    public function checkOrderStatus($top_up_id, $data)
    {
        $action = self::CHECK_ORDER;
        $response = $this->processRequest($action, ['reference_no' => 'DTU_' . $top_up_id], $data);
        return $response;
    }

    private function processRequest($action, $params = array(), $data = array())
    {
        $time = time();

        $header = [
            'partnerid: ' . $this->key,
            'timestamp: ' . $time,
            'path: ' . $action,
            'auth: ' . hash_hmac('sha256', $this->key . $time . $action, $this->secret),
            'Content-Type: ' . 'application/json'
        ];

        $url = $this->request_url . '/' . $action;

        $request_log = 'url: ' . $url . "\n";
        ob_start();
        echo "<pre>";
        print_r($header);
        print_r($params);
        $request_log .= ob_get_contents();
        ob_end_clean();

        $api_log_obj = new direct_topup_api_log($action, $request_log, $data);
        $curl_obj = new curl();
        $curl_obj->connect_via_proxy = true;
        $params = json_encode($params);
        $response = $curl_obj->curl_request('POST', $url, $header, $params);
        $api_log_obj->end_log('2000', $response, array('publishers_id' => $this->publishers_id));
        $response = json_decode($response, true);
        if ($this->validateResponse($response, $curl_obj->get_error())) {
            return $response;
        } else {
            if($this->error_code != 704){
                $this->reportError(array(
                    'request' => $params,
                    'response' => $response,
                    'error' => array('code' => $this->error_code, 'msg' => $this->error_msg)
                ));
            }
            return false;
        }
    }

    private function validateResponse($response, $curl_error)
    {
        if (empty($curl_error)) {
            if (isset($response['error'])) {
                $this->error_code = $response['error']['code'];
                $this->error_msg = $response['error']['message'];
                return false;
            } else {
                return true;
            }
        }
        $this->error_code = $curl_error['error_code'];
        $this->error_msg = $curl_error['error_message'];
        return false;
    }

    public function calculateMargin($products_id, $orders_products_id, $sku)
    {
        $currency_obj = new currencies();
        $op_row = $this->getSellingPrice($orders_products_id);
        $unit_price_cur = $op_row['final_price'] * $op_row['currency_value'];
        $selling_currency = $op_row['currency'];
        $unit_price_mst = $currency_obj->advance_currency_conversion($unit_price_cur, $selling_currency, 'USD', true, 'spot');

        $product_cost = $this->getProductsCost($products_id);
        $orders_id = $this->getOrdersIdByOrdersProductsId($orders_products_id);

        if ($product_cost['products_currency'] == 'USD') {
            $cost_mst = $product_cost['products_price'];
        } else {
            $cost_mst = $currency_obj->advance_currency_conversion($product_cost['products_cost'], $product_cost['products_currency'], 'USD', true, 'spot');
        }

        $cost_cur = $product_cost['products_currency'];

        if ($unit_price_mst && $cost_mst) {
            if($selling_currency == $product_cost['products_currency']){
                $margin = (($unit_price_cur - $product_cost['products_cost']) / $unit_price_cur) * 100;
            }
            else{
                $margin = (($unit_price_mst - $cost_mst) / $unit_price_mst) * 100;
            }

            if ($margin > self::MIN_MARGIN) {
                if ($margin <= self::LOW_MARGIN) {
                    $this->lowMarginReport($orders_id, $products_id, $sku, $cost_cur, $product_cost['products_cost'], $cost_mst, $selling_currency, $unit_price_cur, $unit_price_mst, $margin, 'LOW_MARGIN');
                }
                return true;
            } else {
                $this->lowMarginReport($orders_id, $products_id, $sku, $cost_cur, $product_cost['products_cost'], $cost_mst, $selling_currency, $unit_price_cur, $unit_price_mst, $margin, 'MIN_MARGIN');
                return false;
            }
        } else {
            $this->reportError('Missing Product Cost on ' . $products_id);
        }
        return false;
    }

    public function validateAccount($game_id, $user_data, $extra_params)
    {
        $data = $this->processRequest(self::VALIDATE_USER, [
            'game_code' => $game_id,
            'fields' => $user_data
        ], $extra_params);

        return (!empty($data['validation_token']) ? $data['validation_token'] : false);
    }

    /**
     * @param $orders_products_id
     * @param $publisher_id
     * @param $top_up_id
     * @param $amount_type (Currency / Point)
     * @param $amount
     * @param $quantity
     * @param $account
     * @param $character
     * @param $game (Publishers Game Mapping Column)
     * @param string $server
     * @param string $account_platform
     * @param array $product_array => some useful data ['pid' => products_id, top_up_info => ['publishers_games_id' => 'publishers_games_id']]
     * (Sample Data converted to JSON below)
     * {"pid":"118913","topup_info":{"amount_type":{"top_up_info_id":"840","products_id":"118913","top_up_info_title":"Amount Type","top_up_info_key":"amount_type","top_up_info_description":"Top-up amount type","top_up_info_value":"currency","top_up_info_type_id":"1","sort_order":"50000","last_modified":null,"date_added":"2018-09-13 12:47:46","last_modified_by":"0","use_function":"","set_function":""},"amount":{"top_up_info_id":"841","products_id":"118913","top_up_info_title":"Base Amount","top_up_info_key":"amount","top_up_info_description":"Top-up base Amount","top_up_info_value":"1","top_up_info_type_id":"1","sort_order":"50000","last_modified":null,"date_added":"2018-09-13 12:47:46","last_modified_by":"0","use_function":"","set_function":""},"product_code":{"top_up_info_id":"842","products_id":"118913","top_up_info_title":"Product Code","top_up_info_key":"product_code","top_up_info_description":"Product reference from Publisher","top_up_info_value":"1222586","top_up_info_type_id":"1","sort_order":"50000","last_modified":null,"date_added":"2018-09-13 12:47:46","last_modified_by":"0","use_function":"","set_function":""},"sync_publisher_character_flag":{"top_up_info_id":"843","products_id":"118913","top_up_info_title":"Sync publisher character","top_up_info_key":"sync_publisher_character_flag","top_up_info_description":"Sync publisher character","top_up_info_value":"1","top_up_info_type_id":"1","sort_order":"50000","last_modified":null,"date_added":"2018-09-13 12:47:46","last_modified_by":"0","use_function":"","set_function":""},"account":{"top_up_info_id":"844","products_id":"118913","top_up_info_title":"Customer's Account","top_up_info_key":"account","top_up_info_description":"Customer's Account","top_up_info_value":"","top_up_info_type_id":"2","sort_order":"50000","last_modified":null,"date_added":"2018-09-13 12:47:46","last_modified_by":"0","use_function":"","set_function":""},"retype_account_flag":{"top_up_info_id":"845","products_id":"118913","top_up_info_title":"Retype Account","top_up_info_key":"retype_account_flag","top_up_info_description":"Retype Account","top_up_info_value":"0","top_up_info_type_id":"1","sort_order":"50000","last_modified":null,"date_added":"2018-09-13 12:47:46","last_modified_by":"0","use_function":"","set_function":""},"server":{"top_up_info_id":"846","products_id":"118913","top_up_info_title":"Server","top_up_info_key":"server","top_up_info_description":"Customer's game servers","top_up_info_value":"","top_up_info_type_id":"2","sort_order":"50000","last_modified":null,"date_added":"2018-09-13 12:47:46","last_modified_by":"0","use_function":"","set_function":""},"character":{"top_up_info_id":"847","products_id":"118913","top_up_info_title":"Customer's character","top_up_info_key":"character","top_up_info_description":"Customer's character","top_up_info_value":"","top_up_info_type_id":"2","sort_order":"50000","last_modified":null,"date_added":"2018-09-13 12:47:46","last_modified_by":"0","use_function":"","set_function":""},"account_platform":{"top_up_info_id":"848","products_id":"118913","top_up_info_title":"Account Platform","top_up_info_key":"account_platform","top_up_info_description":"Account Platform","top_up_info_value":"","top_up_info_type_id":"2","sort_order":"50000","last_modified":null,"date_added":"2018-09-13 16:01:46","last_modified_by":"0","use_function":"","set_function":""}},"publisher_games_id":"63"}
     * @return array|mixed
     */
    public function do_top_up(
        $orders_products_id,
        $publisher_id,
        $top_up_id,
        $amount_type,
        $amount,
        $quantity,
        $account,
        $character,
        $game,
        $server = '',
        $platform = '',
        $product_array = array()
    ) {
        $response_array = array();

        $result_code = '';
        $completed_order = 0;

        $orders_top_up_data_sql = array(
            'publishers_response_time' => 'now()',
            'top_up_timestamp' => tep_db_prepare_input(time())
        );
        $orders_top_up_remark_data_sql = array(
            'top_up_id' => (int)$top_up_id,
            'data_added' => 'now()'
        );

        $this->publishers($publisher_id);
        $get_publishers_conf_array = $this->get_publishers_conf();
        $this->game_id = $product_array['publisher_games_id'];
        $game_code = $this->getPublishersGameCode($this->game_id);
        $this->request_url = $get_publishers_conf_array['TOP_UP_URL']['publishers_configuration_value'];
        $this->key = $get_publishers_conf_array['OGM_MERCHANT_ID']['publishers_configuration_value'];
        $this->secret = $get_publishers_conf_array['SECRET_KEY']['publishers_configuration_value'];

        $order_list = $this->getOrdersTopUpSubOrder($top_up_id);
        if ($quantity > count($order_list)) {
            $add_quantity = $quantity - count($order_list);
            $this->createOrdersTopUpSubOrder($top_up_id, $add_quantity);
            $order_list = $this->getOrdersTopUpSubOrder($top_up_id);
        }

        $products_id = $product_array['pid'];
        $sku = $product_array['topup_info']['product_code']['top_up_info_value'];

        if (in_array($game_code, unserialize(UNIPIN_DELAYED_DTU_PRODUCTS_CODE)) && time() - strtotime($product_array['orders_top_up']['top_up_created_date']) < 120) {
            $result_code = '1512';
            $this->error_code = $result_code;
            $this->error_msg = $this->get_result_code_description($result_code);
        }
        elseif ($this->calculateMargin($products_id, $orders_products_id, $sku)) {
            $field_mapping = json_decode($this->get_publishers_games_conf($this->game_id, 'ACCOUNT_FIELD_MAPPING'), 1);
            $user_data = [];
            foreach ($field_mapping as $field => $type) {
                $user_data[$type['name']] = ($type['type'] == 'number' ? (integer)$$field : $$field);
            }
            if (!empty($user_data)) {
                for ($i = 0; count($order_list) > $i; $i++) {
                    if ($token = $this->validateAccount($game_code, $user_data, [
                        'top_up_id' => $top_up_id,
                        'orders_products_id' => $orders_products_id
                    ])) {
                        $response = $this->processRequest(self::CREATE_ORDER, [
                            'game_code' => $game_code,
                            'validation_token' => $token,
                            'reference_no' => 'DTU_' . $order_list[$i]['id'],
                            'denomination_id' => $sku
                        ], [
                            'top_up_id' => $top_up_id,
                            'orders_products_id' => $orders_products_id
                        ]);

                        if ($this->isOrderSuccess($response)) {
                            $order_list[$i]['status'] = 1;
                            $this->updateOrdersTopUpSubOrder($order_list[$i]['id'], ['status' => 1, 'publisher_ref_id' => $response['transaction_number']]);
                        } elseif ($this->error_code == "708") {
                            if ($response = $this->checkOrderStatus($order_list[$i]['id'], [
                                'top_up_id' => $top_up_id,
                                'orders_products_id' => $orders_products_id
                            ])) {
                                if ($this->isOrderSuccess($response)) {
                                    $order_list[$i]['status'] = 1;
                                    $this->updateOrdersTopUpSubOrder($order_list[$i]['id'], ['status' => 1, 'publisher_ref_id' => $response['transaction_number']]);
                                } else {
                                    break;
                                }
                            } else {
                                // Error Creation Fail, Prevent Future order being created
                                break;
                            }
                        } else {
                            // Error Creation Fail, Prevent Future order being created
                            break;
                        }
                        $this->reset();
                    }
                }

                foreach ($order_list as $order) {
                    if ($order['status'] == 1) {
                        $completed_order += 1;
                    }
                }

                if ($completed_order == $quantity) {
                    $response_array['result_code'] = '2000';
                    $response_array['publisher_ref_id'] = $top_up_id;
                    $response_array['top_up_status'] = 'reloaded';

                    $orders_top_up_data_sql['top_up_status'] = '3';
                    $orders_top_up_remark_data_sql['remark'] = 'Top-up: Reloaded';
                }

            } else {
                $this->reportError('Missing Field Setting');
            }
        } else {
            // Status Fail, Results Code : Margin Block
            $result_code = '1510';
            $this->error_code = $result_code;
            $this->error_msg = $this->get_result_code_description($result_code);
        }

        if (empty($response_array)) {
            $response_array['result_code'] = (!empty($result_code) ? $result_code : '1001');
            $response_array['publisher_ref_id'] = '';
            $response_array['top_up_status'] = 'failed';
            $response_array['error_msg'] = $this->error_code . ' : ' . $this->error_msg;

            $orders_top_up_data_sql['top_up_status'] = '10';
            $orders_top_up_data_sql['result_code'] = $response_array['result_code'];
            $orders_top_up_remark_data_sql['remark'] = 'Top-up: Failed ' . $this->error_msg;
        }

        tep_db_perform(TABLE_ORDERS_TOP_UP_REMARK, $orders_top_up_remark_data_sql);
        tep_db_perform(TABLE_ORDERS_TOP_UP, $orders_top_up_data_sql, 'update', " top_up_id = '" . (int)$top_up_id . "' ");

        return $response_array;
    }

    private function createOrdersTopUpSubOrder($top_up_id, $quantity)
    {
        for ($i = 0; $i < $quantity; $i++) {
            tep_db_perform('orders_top_up_sub_order', [
                'top_up_id' => $top_up_id,
                'status' => 0,
                'publisher_ref_id' => 0,
                'created_at' => time(),
                'updated_at' => time()
            ]);
        }
    }

    private function reset()
    {
        $this->error_code = '';
        $this->error_msg = '';
    }

    private function isOrderSuccess($response)
    {
        if (!empty($response['status']) && !empty($response['reason']) && $response['status'] == 1 && $response['reason'] == 'Successful') {
            return true;
        }
        return false;
    }

    private function getOrdersTopUpSubOrder($top_up_id)
    {
        $top_up_list = [];
        $query = "SELECT `id`,`publisher_ref_id`,`status` FROM orders_top_up_sub_order WHERE top_up_id = '" . tep_db_input($top_up_id) . "'";
        $result = tep_db_query($query);
        while ($row = tep_db_fetch_array($result)) {
            $top_up_list[] = $row;
        }
        return $top_up_list;
    }

    private function updateOrdersTopUpSubOrder($id, $data)
    {
        $data['updated_at'] = time();
        tep_db_perform('orders_top_up_sub_order', $data, 'update', " id = '" . (int)$id . "' ");
    }

    private function getSellingPrice($op_id)
    {
        $query = "SELECT o.currency, o.currency_value, op.final_price FROM " . TABLE_ORDERS_PRODUCTS . " op 
                    LEFT JOIN ". TABLE_ORDERS ." o ON op.orders_id = o.orders_id WHERE op.orders_products_id = '" . tep_db_input($op_id) . "' LIMIT 1";
        $result = tep_db_query($query);
        if ($row = tep_db_fetch_array($result)) {
            return $row;
        }
    }

    private function getProductsCost($pid)
    {
        $unit_cost_select_sql = "   SELECT products_cost, products_currency
                                            FROM products_cost
                                            WHERE products_id = '" . tep_db_input($pid) . "'
                                            LIMIT 1";
        $unit_cost_result_sql = tep_db_query($unit_cost_select_sql);

        if ($unit_price_row = tep_db_fetch_array($unit_cost_result_sql)) {
            return $unit_price_row;
        }

        return false;
    }

    public function getPublishersGameCode($game_id)
    {
        $game_code = 0;
        $gc_sql = "SELECT publishers_game
                    FROM " . TABLE_PUBLISHERS_GAMES . "
                    WHERE publishers_games_id = '" . $game_id . "'
                    LIMIT 1";
        $gc_results = tep_db_query($gc_sql);
        if ($gc_row = tep_db_fetch_array($gc_results)) {
            $game_code = $gc_row['publishers_game'];
        }
        return $game_code;
    }

    public function getOrdersIdByOrdersProductsId($opId)
    {
        $orders_id = 0;
        $op_sql = "SELECT orders_id
                    FROM " . TABLE_ORDERS_PRODUCTS . "
                    WHERE orders_products_id = '" . $opId . "'
                    LIMIT 1";
        $op_results = tep_db_query($op_sql);
        if ($op_row = tep_db_fetch_array($op_results)) {
            $orders_id = $op_row['orders_id'];
        }
        return $orders_id;
    }

    private function getOrderTopUpPublishersRefId($top_up_id)
    {
        $orders_top_up_select_sql = "SELECT otu.publishers_ref_id
									FROM " . TABLE_ORDERS_TOP_UP . " AS otu
									WHERE otu.top_up_id = $top_up_id
									LIMIT 1";
        $orders_top_up_result_sql = tep_db_query($orders_top_up_select_sql);
        if ($orders_top_up_row = tep_db_fetch_array($orders_top_up_result_sql)) {
            return $orders_top_up_row['publishers_ref_id'];
        }
    }

    public function get_game_list($publisher_id, $publisher_conf)
    {
        $this->key = $publisher_conf['OGM_MERCHANT_ID']['publishers_configuration_value'];
        $this->request_url = $publisher_conf['TOP_UP_URL']['publishers_configuration_value'];
        $this->secret = $publisher_conf['SECRET_KEY']['publishers_configuration_value'];

        $data = $this->processRequest(self::GAME_LIST);

        $game_list = [];
        if (!empty($data['game_list'])) {
            foreach ($data['game_list'] as $game) {
                if (isset($game['game_code'])) {
                    $game_list[$game['game_code']] = $game['game_name'];
                }
            }
        }

        if (!isset($publisher_conf['GAME_LIST'])) {
            tep_db_perform(TABLE_PUBLISHERS_CONFIGURATION, array(
                "publishers_id" => $publisher_id,
                "publishers_configuration_title" => '',
                "publishers_configuration_key" => tep_db_prepare_input('GAME_LIST'),
                "publishers_configuration_value" => tep_db_prepare_input(json_encode($game_list)),
                "publishers_configuration_description" => tep_db_prepare_input(''),
                "date_added" => 'now()',
                "last_modified_by" => (!empty($_SESSION['login_id']) ? $_SESSION['login_id'] : 0)
            ));
            $this->reportError($game_list);
        } else {
            $list = json_decode($publisher_conf['GAME_LIST']['publishers_configuration_value'], 1);

            $new_game = array_diff_key($game_list, $list);
            $removed_game = array_diff_key($list, $game_list);

            if (count($new_game) || count($removed_game)) {
                $this->set_publishers_conf('GAME_LIST', json_encode($game_list));
            }
        }
    }

    public function get_server_list($publisher_id, $game_info, $get_publishers_conf_array)
    {
        $this->key = $get_publishers_conf_array['OGM_MERCHANT_ID']['publishers_configuration_value'];
        $this->request_url = $get_publishers_conf_array['TOP_UP_URL']['publishers_configuration_value'];
        $this->secret = $get_publishers_conf_array['SECRET_KEY']['publishers_configuration_value'];
        $publisher_game_list = json_decode($get_publishers_conf_array['GAME_LIST']['publishers_configuration_value'], 1);

        if(!isset($publisher_game_list[$game_info['publishers_game']])){
            return;
        };

        $this->publishers($game_info['publishers_id']);
        $this->game_id = $game_info['publishers_games_id'];

        $game_config = $this->get_publishers_games_conf($this->game_id);

        $data = $this->processRequest(self::GAME_DETAIL, [
            'game_code' => $game_info['publishers_game']
        ]);

        if ($data) {
            $field_list = (!empty($game_config['ACCOUNT_FIELD_MAPPING']['publishers_games_configuration_value']) ? json_decode($game_config['ACCOUNT_FIELD_MAPPING']['publishers_games_configuration_value'], 1) : []);
            $deno_list = (!empty($game_config['ACCOUNT_DENO_LIST']['publishers_games_configuration_value']) ? json_decode($game_config['ACCOUNT_DENO_LIST']['publishers_games_configuration_value'], 1) : []);

            $this->processTemplateData($data, $field_list, $deno_list);
        }

        return true;
    }

    private function processTemplateData($data, $field_list, $deno_list)
    {
        $input_count = 0;
        $dropdown_count = 0;
        $data_mapping = [];
        $server_list = [];
        $platform_list = [];
        $denomination_list = [];

        // Only Allow Two Input Field and two dropdown field, any further required customization
        if (!empty($data['fields'])) {
            foreach ($data['fields'] as $field) {
                if ($field['type'] == 'string' || $field['type'] == 'number') {
                    if ($input_count === 0) {
                        $data_mapping['account'] = ['name' => $field['name'], 'type' => $field['type']];
                    } elseif ($input_count === 1 || !empty($data_mapping['character'])) {
                        $data_mapping['character'] = ['name' => $field['name'], 'type' => $field['type']];
                    } else {
                        $this->reportError($data);
                        return;
                    }
                    $input_count++;
                } elseif ($field['type'] == 'dropdown') {
                    if ($dropdown_count == 0) {
                        if (empty($data_mapping['server'])) {
                            $data_mapping['server'] = ['name' => $field['name'], 'type' => $field['type']];
                            foreach ($field['data'] as $server) {
                                $server_list[$server['value']] = $server['name'];
                            }
                        }
                    } elseif ($dropdown_count == 1) {
                        $data_mapping['platform'] = ['name' => $field['name'], 'type' => $field['type']];
                        foreach ($field['data'] as $platform) {
                            $platform_list[$platform['value']] = $platform['name'];
                        }
                    } else {
                        $this->reportError($data);
                        return;
                    }
                } else {
                    $this->reportError($data);
                    return;
                }
            }
        }

        foreach ($data['denominations'] as $deno) {
            $denomination_list[$deno['id']] = $deno;
        }

        $direct_topup_obj = new direct_topup();

        if (!empty($data_mapping)) {
            if (array_diff_key($data_mapping, $field_list) || array_diff_key($field_list, $data_mapping)) {
                $this->set_publishers_game_configuration('ACCOUNT_FIELD_MAPPING', $data_mapping);
            }

        }
        if (!empty($denomination_list)) {
            $new_deno_list = array_diff_key($denomination_list, $deno_list);
            $removed_deno_list = array_diff_key($deno_list, $denomination_list);
            ksort($denomination_list);
            $this->set_publishers_game_configuration('ACCOUNT_DENO_LIST', $denomination_list);
        }
        if (!empty($platform_list)) {
            $this->set_publishers_game_configuration('ACCOUNT_PLATFORM_LIST', $platform_list);
        }
        if (!empty($server_list)) {
            $direct_topup_obj->set_publishers_game_server($this->game_id, $server_list);
        }
    }

    private function set_publishers_game_configuration($key, $data)
    {
        $publishers_games_configuration_data_sql = array();
        $publishers_games_configuration_data_sql['publishers_games_configuration_value'] = json_encode($data, JSON_UNESCAPED_UNICODE);

        $publishers_games_conf_select_sql = "	SELECT publishers_games_configuration_id
                                                        FROM " . TABLE_PUBLISHERS_GAMES_CONFIGURATION . "
                                                        WHERE publishers_games_id = '" . $this->game_id . "'
                                                            AND publishers_games_configuration_key = '" . $key . "'";
        $publishers_games_conf_result = tep_db_query($publishers_games_conf_select_sql);
        if ($publishers_games_conf_row = tep_db_fetch_array($publishers_games_conf_result)) {
            $publishers_games_configuration_data_sql['last_modified'] = 'now()';
            tep_db_perform(TABLE_PUBLISHERS_GAMES_CONFIGURATION, $publishers_games_configuration_data_sql, 'update', " publishers_games_configuration_id = '" . $publishers_games_conf_row['publishers_games_configuration_id'] . "' ");
        } else {
            $publishers_games_configuration_data_sql['publishers_games_configuration_key'] = tep_db_prepare_input($key);
            $publishers_games_configuration_data_sql['publishers_games_id'] = $this->game_id;
            $publishers_games_configuration_data_sql['sort_order'] = '50000';
            $publishers_games_configuration_data_sql['date_added'] = 'now()';
            tep_db_perform(TABLE_PUBLISHERS_GAMES_CONFIGURATION, $publishers_games_configuration_data_sql);
        }
    }

    // Top-Up Report, Not Using in this service
    public function save_top_up_list($publisher_id, $start_time, $end_time = '', $filename = '')
    {
        return;
    }

    public function reportError($response_data, $ext_subject = '')
    {
        $slack = new slack_notification();
        $data = json_encode(array(
            'text' => '[OG Crew] ' . $this->title . ' API Error - ' . date("F j, Y H:i") . ' from ' . (isset($_SERVER['SERVER_NAME']) && !empty($_SERVER['SERVER_NAME']) ? $_SERVER['SERVER_NAME'] : '-'),
            'attachments' => array(
                array(
                    'color' => 'warning',
                    'text' => $ext_subject . "\n\n" . json_encode($response_data)
                )
            )
        ));
        $slack->send(SLACK_WEBHOOK_DEV_DEBUG, $data);
    }

    public function lowMarginReport($orders_id, $products_id, $sku, $cost_cur, $cost_amt, $cost_mst, $price_cur, $price_amt, $price_mst, $margin, $type)
    {
        $slack = new slack_notification();
        $currency_obj = new currencies();
        $margin = round($margin,4);
        if (strtoupper($cost_cur) !== DEFAULT_CURRENCY) {
            $cost_str = $currency_obj->format($cost_amt, false, $cost_cur) . '(~' . $currency_obj->format($cost_mst, false, DEFAULT_CURRENCY) . ')';
        } else {
            $cost_str = $currency_obj->format($cost_amt, false, $cost_cur);
        }

        if (strtoupper($price_cur) !== DEFAULT_CURRENCY) {
            $price_str = $currency_obj->format($price_amt, false, $price_cur) . '(~' . $currency_obj->format($price_mst, false, DEFAULT_CURRENCY) . ')';
        } else {
            $price_str = $currency_obj->format($price_amt, false, $price_cur);
        }

        $cost_str = html_entity_decode($cost_str);
        $price_str = html_entity_decode($price_str);

        $crew_url = (defined('HTTPS_CREW_SERVER') ? HTTPS_CREW_SERVER : HTTPS_SERVER);

        switch ($type) {
            case 'LOW_MARGIN':
                $data = json_encode(array(
                    'text' => '*Low Margin Order #' . $orders_id . ' delivery on ' . $this->title . ' Direct Top-up API*',
                    'attachments' => array(
                        array(
                            'color' => 'warning',
                            'text' => "Product ID : <" . $crew_url . "/categories.php?pID=" . $products_id . "&action=new_product|" . $products_id . "> \n SKU : " . $sku . " \n Cost : " . $cost_str . " \n Selling Price : " . $price_str . " \n Actual Margin : " . $margin . "% (<= " . self::LOW_MARGIN . "%) \n `Action` : Revise Selling & Cost Setting"
                        )
                    )
                ));
                break;
            case 'MIN_MARGIN':
                $data = json_encode(array(
                    'text' => '*Order #' . $orders_id . ' blocked from delivery on ' . $this->title . ' Direct Top-up API*',
                    'attachments' => array(
                        array(
                            'color' => 'danger',
                            'text' => "Product ID : <" . $crew_url . "/categories.php?pID=" . $products_id . "&action=new_product|" . $products_id . "> \n SKU : " . $sku . " \n Cost : " . $cost_str . " \n Selling Price : " . $price_str . " \n Actual Margin : " . $margin . "% (<= " . self::MIN_MARGIN . "%) \n `Action` : Revise Selling & Cost Setting then process the order <" . $crew_url . "/orders.php?oID=" . $orders_id . "&action=edit|#" . $orders_id . ">"
                        )
                    )
                ));
                break;
        }

        $slack->send(SLACK_WEBHOOK_DTU, $data);
    }

}

?>
