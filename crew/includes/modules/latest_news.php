<?
/*
  	$Id: latest_news.php,v 1.32 2012/07/13 07:09:12 weesiong Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2002 Will Mays
	
  	Released under the GNU General Public License
*/
?>

<!-- latest_news //-->
<?
if(!function_exists('eval_buffer')) {
	function eval_buffer($string) {
   		ob_start();
   		eval("$string[2];");
   		$return = ob_get_contents();
   		ob_end_clean();
   		return $return;
	}
}

if(!function_exists('eval_print_buffer')) {
	function eval_print_buffer($string) {
   		ob_start();
   		eval("print $string[2];");
   		$return = ob_get_contents();
   		ob_end_clean();
   		return $return;
	}
}

if(!function_exists('eval_html')) {
	function eval_html($string) {
   		$string = preg_replace_callback("/(<\?=)(.*?)\?>/si",
        	                           "eval_print_buffer",$string);
   		return preg_replace_callback("/(<\?php|<\?)(.*?)\?>/si",
        	                         "eval_buffer",$string);
	}
}

if (sizeof($cPath_array) > 0) {
	$cat_ids_array = array(end($cPath_array), -999);
} else {
	$cat_ids_array = array(0); // main page cat id
}

$news_groups_cat = array();
$news_select_sql = "	SELECT news_id 
						FROM ".TABLE_LATEST_NEWS_CATEGORIES."  
						WHERE categories_id IN (".implode(",", $cat_ids_array).")";
$news_result_sql = tep_db_query($news_select_sql);
while ($news_row = tep_db_fetch_array($news_result_sql)) {
	$news_groups_cat[] = $news_row['news_id'];
}

$news_cat_where_str = "news_id IN ('".implode("','", $news_groups_cat)."')";
unset($news_groups_cat);

$group_news_select_sql = "	SELECT news_groups_id 
							FROM " . TABLE_LATEST_NEWS_GROUPS . " 
							WHERE news_groups_id = '". $LATEST_NEWS_TYPE . "'";
$group_news_result_sql = tep_db_query($group_news_select_sql);
$group_news = tep_db_fetch_array($group_news_result_sql);

$this_site_id = 0;
if (defined('SITE_ID')) {
	$this_site_id = SITE_ID;
}

$news_display_sites_where_str = "FIND_IN_SET('" . $this_site_id . "', news_display_sites)";
$custom_products_type_where_str = " (custom_products_type = '' OR FIND_IN_SET('" . (int)$ctype . "', custom_products_type))";

$cnt = 0;
$news_count_select_sql = "	SELECT COUNT(news_id) AS total_news
							FROM " . TABLE_LATEST_NEWS . " 
							WHERE status = '1' 
								AND $news_cat_where_str 
								AND $news_display_sites_where_str
								AND news_groups_id = '". $group_news["news_groups_id"] ."' 
								AND $custom_products_type_where_str";
$news_count_result_sql = tep_db_query($news_count_select_sql);
if ($news_count_row = tep_db_fetch_array($news_count_result_sql)) {
	$cnt = $news_count_row["total_news"];
}

if (LATEST_NEWS_BOX == 'classic' || LATEST_NEWS_BOX == 'OGM2008') {
	if ($LATEST_NEWS_TYPE) {
// headline and content hav been removed
// display in buy expanded information 
		$latest_news_select_sql = "	SELECT news_id, date_added, news_groups_id, url 
									FROM " . TABLE_LATEST_NEWS . " 
									WHERE status = '1' 
										AND " . $news_display_sites_where_str . " 
										AND " . $news_cat_where_str . " 
										AND news_groups_id = '". $group_news['news_groups_id'] ."' 
										AND (custom_products_type = '' OR FIND_IN_SET('" . (int)$ctype . "', custom_products_type))
									ORDER BY date_added DESC";
	    $latest_news_query = tep_db_query($latest_news_select_sql);
	} else {
		$latest_news_select_sql = "	SELECT news_id, date_added, url, news_groups_id 
									FROM " . TABLE_LATEST_NEWS . " 
									WHERE status = '1' 
										AND " . $news_display_sites_where_str . " 
										AND " . $news_cat_where_str . " 
										AND (custom_products_type = '' OR FIND_IN_SET('" . (int)$ctype . "', custom_products_type))
									ORDER BY date_added DESC";
		$latest_news_query = tep_db_query($latest_news_select_sql);	
	}
} else {
	if ($LATEST_NEWS_TYPE) {
	   	$latest_news_query = tep_db_query(' SELECT news_id, date_added, url, news_groups_id
											FROM ' . TABLE_LATEST_NEWS . " 
											WHERE status = '1' 
												AND " . $news_display_sites_where_str . " 
												AND " . $news_cat_where_str . " 
												AND news_groups_id = '". $group_news['news_groups_id'] ."' 
												AND (custom_products_type = '' OR FIND_IN_SET('" . (int)$ctype . "', custom_products_type))
											ORDER BY date_added DESC");
	} else {
		$latest_news_query = tep_db_query('	SELECT news_id, date_added, url, news_groups_id 
											FROM ' . TABLE_LATEST_NEWS . " 
											WHERE status = '1' 
												AND " . $news_display_sites_where_str . " 
												AND " . $news_cat_where_str . " 
												AND (custom_products_type = '' OR FIND_IN_SET('" . (int)$ctype . "', custom_products_type))
											ORDER BY date_added DESC");
	}
}

if (!tep_db_num_rows($latest_news_query)) { // there are no news
	echo '<!-- no news -->';
} else {
	if (LATEST_NEWS_BOX == 'OGM2008') {
	    $row = 0;
		$info_box_contents = '<div class="vspacing"></div><span class="hd1" style="padding:20px">' . HEADER_EXPANDED_INFORMATION . '</span>';
	    while ($latest_news = tep_db_fetch_array($latest_news_query)) {
			$latest_news_des_select_sql = '	SELECT headline, content, latest_news_summary
											FROM ' . TABLE_LATEST_NEWS_DESCRIPTION . " 
											WHERE  news_id = '" . $latest_news['news_id'] . "'
											and headline <> ''
											and language_id = ". $languages_id;
            
			$latest_news_des_result_sql = tep_db_query ($latest_news_des_select_sql);
			$latest_news_des_row = tep_db_fetch_array($latest_news_des_result_sql); 
            
			if(!tep_not_null($latest_news_des_row['content'])) {
				$default_news_select_sql = "SELECT content, headline, latest_news_summary
											FROM " . TABLE_LATEST_NEWS_DESCRIPTION . "
											WHERE news_id = '". $latest_news['news_id'] ."'
												AND is_default = 1";
				$default_news_result_sql = tep_db_query($default_news_select_sql);
				if ($default_news_row = tep_db_fetch_array($default_news_result_sql)) {
					$latest_news_des_row['content'] = $default_news_row['content'];
					$latest_news_des_row['headline'] = $default_news_row['headline'];
					$latest_news_des_row['latest_news_summary'] = $default_news_row['latest_news_summary'];
				}
			}
			// changes hav been made from $latest_news to $latest_news_des_row 
	    	if (tep_not_null($latest_news_des_row['latest_news_summary'])) {
	    		$show_content = $latest_news_des_row['latest_news_summary'];
	    		$read_more_link = ' <a href="' . tep_href_link(FILENAME_NEWS, "news_id=".$latest_news['news_id']."&news_type=".$group_news['news_groups_id']).'" class="latestNewsLink">[Read More]</a>';
	    	} else {
	    		$show_content = $latest_news_des_row['content'];
	    		$read_more_link = '';
	    	}

			if (preg_match_all("/(?:#DC_)([^#]+)(?:#)?/is", $show_content, $sys_token_array)) {
				if (count($sys_token_array[1])) {
					for ($sysCnt=0; $sysCnt < count($sys_token_array[1]); $sysCnt++) {
						$coupon_stat_select_sql = "	SELECT (c.uses_per_coupon - COUNT(crt.unique_id)) AS remaining_uses
													FROM " . TABLE_COUPONS . " AS c 
													LEFT JOIN " . TABLE_COUPON_REDEEM_TRACK . " AS crt 
														ON c.coupon_id=crt.coupon_id 
													WHERE coupon_code = '" . $sys_token_array[1][$sysCnt] . "'
													GROUP BY crt.coupon_id";
						$coupon_stat_result_sql = tep_db_query($coupon_stat_select_sql);
	    				echo $coupon_stat_select_sql;
	    				if ($coupon_stat_row = tep_db_fetch_array($coupon_stat_result_sql)) {
	    					$show_content = str_replace($sys_token_array[0][$sysCnt], $coupon_stat_row['remaining_uses'], $show_content);
	    				}
					}
				}
			}
			
			if ($row != 0){
				$info_box_contents .= '<div class="dottedLine" ><!-- --></div>';
			}
			$info_box_contents .= '<div id="news_'.$latest_news['news_id'].'" style="padding: 20px">';
			$info_box_contents .= '<b>'.eval_html($latest_news_des_row["headline"]).'</b><br/>';
			//$info_box_contents .= tep_date_short($latest_news['date_added']).'<br/>';
			$info_box_contents .= eval_html($latest_news_des_row["content"]);
			$info_box_contents .= '</div>';
	      	$row++;
		}
		if (tep_not_null($info_box_contents) && tep_not_null($latest_news_des_row)) {
            if (isset($display_content) && $display_content=='simple'){
                echo $info_box_contents;
            } else {
                echo $page_obj->get_html_simple_rc_box('',$info_box_contents,33);
            }
		}
	} else {
		$info_box_contents = array();

	    if (LATEST_NEWS_BOX == 'advanced') {
	    	echo '	<script LANGUAGE="JavaScript">
	    				var singletext = new Array();
	    			</script>';
	    } else {
			echo '<div style="border: #cccccc; background: #ffffed; border-width:1px; border-style:solid; padding:10px">';
	    }

	    $info_box_contents = array();
	    $row = 0;

	    while ($latest_news = tep_db_fetch_array($latest_news_query)) {
		
			$latest_news_des_select_sql = '	SELECT headline, content, latest_news_summary
											FROM ' . TABLE_LATEST_NEWS_DESCRIPTION . " 
											WHERE  news_id = '" . $latest_news['news_id'] . "'
												and headline <> ''
												and (IF(language_id = '". $languages_id ."' , 1, IF (( SELECT COUNT(news_id) > 0 
																									 FROM ". TABLE_LATEST_NEWS_DESCRIPTION." 
																									 WHERE news_id = '" . $latest_news['news_id'] . "'
																									 	and language_id = '".$languages_id."'
																									 	and headline <> ''
																										), 0, language_id = '".$default_languages_id."')))";
			$latest_news_des_result_sql = tep_db_query ($latest_news_des_select_sql); 
			$latest_news_des_row = tep_db_fetch_array($latest_news_des_result_sql); 
			
// content changed to $latest_news_des_row from $latest_news
	    	if (tep_not_null($latest_news_des_row['latest_news_summary'])) {
	    		$show_content = $latest_news_des_row['latest_news_summary'];
	    		$read_more_link = ' <a href="' . tep_href_link(FILENAME_NEWS, "news_id=".$latest_news['news_id']."&news_type=".$group_news['news_groups_id']).'" class="latestNewsLink">[Read More]</a>';
	    	} else {
	    		$show_content = $latest_news_des_row['content'];
	    		$read_more_link = '';
	    	}

			if (preg_match_all("/(?:#DC_)([^#]+)(?:#)?/is", $show_content, $sys_token_array)) {
				if (count($sys_token_array[1])) {
					for ($sysCnt=0; $sysCnt < count($sys_token_array[1]); $sysCnt++) {
						$coupon_stat_select_sql = "	SELECT (c.uses_per_coupon - COUNT(crt.unique_id)) AS remaining_uses
													FROM " . TABLE_COUPONS . " AS c 
													LEFT JOIN " . TABLE_COUPON_REDEEM_TRACK . " AS crt 
														ON c.coupon_id = crt.coupon_id 
													WHERE coupon_code = '" . $sys_token_array[1][$sysCnt] . "'
													GROUP BY crt.coupon_id";
						$coupon_stat_result_sql = tep_db_query($coupon_stat_select_sql);

	    				if ($coupon_stat_row = tep_db_fetch_array($coupon_stat_result_sql)) {
	    					$show_content = str_replace($sys_token_array[0][$sysCnt], $coupon_stat_row['remaining_uses'], $show_content);
	    				}
					}
				}
			}
// headline all changed to $latest_news_result from $latest_news
			if (LATEST_NEWS_BOX == 'small') {
				echo	'<a href="' . tep_href_link(FILENAME_NEWS, "news_id=".$latest_news['news_id']) .'" class="latestNewsLink">'
	                    ."<b><div class='latestNewsDate'>".tep_date_short($latest_news['date_added'])
		                .'</div></b>&nbsp;-&nbsp;'
	        	        	            .eval_html($latest_news_des_row['headline']) . '</a><br>';
	      	} else if (LATEST_NEWS_BOX == 'classic') {
	       		if ($latest_news['url']) {
	         		echo 	'<div class="latestNewsTitle">'.eval_html($latest_news_des_row["headline"]).'</div>'.
							'<div class="latestNewsDate">'.tep_date_long($latest_news["date_added"]).'</div>'
							.'<table border="0"><tr><td>&nbsp;&nbsp;</td><td class="latestNewsBoxContents">' . nl2br(eval_html($show_content)) . $read_more_link . '<br><br><a href=http://'.$latest_news["url"].' class="latestNewsLink">[URL]</a>' . '</td></tr></table>';
				} else {
	       			echo  	'<div class="latestNewsTitle">'.eval_html($latest_news_des_row["headline"]).'</div>'.
	       					'<div class="latestNewsDate">'.tep_date_long($latest_news["date_added"]).'</div>'
	       					.'<table border="0"><tr><td>&nbsp;&nbsp;</td><td class="latestNewsBoxContents">' . nl2br(eval_html($show_content)) . $read_more_link . '</td></tr></table>';
				}

			} else if (LATEST_NEWS_BOX == 'advanced') {
				echo '	<script LANGUAGE="JavaScript">
							singletext['.$row.'] = "<table border=\"0\" height=\"100%\"><tr><td valign=\"middle\" class=\"main\"><a href=\"' . tep_href_link(FILENAME_NEWS, "news_id=".$latest_news['news_id']."&news_type=".$group_news['news_groups_id']) . '\"><b>'.eval_html($latest_news_des_row["headline"]).'</b> '.tep_date_long($latest_news["date_added"]).'</a></td></tr></table>";
						</script>';
			} else {
	        	echo '<table border="0" cellspacing="0" cellpadding="0"><tr>'
	                 .'<td class="latestNewsBoxContents">'
	                 .'<a href="' . tep_href_link(FILENAME_NEWS, "news_id=".$latest_news['news_id']) . '" class="latestNewsLink">'
	                 .tep_date_short($latest_news['date_added'])."</a></td>"
	                 .'<td class="latestNewsBoxContents">&nbsp;-&nbsp;</td>'
	                 .'<td class="latestNewsBoxContents"><b>'
	                 .'<a href="' . tep_href_link(FILENAME_NEWS, "news_id=".$latest_news['news_id']) . '" class="latestNewsLink">'
	                  .eval_html($latest_news_des_row['headline']) . '</a></b></td></tr></table>';
	     	}
	      	$row++;
		}
		
	    if (LATEST_NEWS_BOX == 'advanced') {
	    	echo '
	    	<script LANGUAGE="JavaScript">
				var swidth=350;
				var sheight=26;
				var sspeed=2;
				var restart=sspeed;
				var rspeed=sspeed;
				var spause=2000;
				var sbcolor="#ccffcc";
				var ii=0;
				function start() {
					if(document.getElementById) {
						ns6div=document.getElementById(\'iens6div\');
						ns6div.style.top=sheight;
						ns6div.innerHTML=singletext[0];
						sizeup=ns6div.offsetHeight;ns6scroll();
					} else if(document.layers) {
						ns4layer=document.ns4div.document.ns4div1;
						ns4layer.top=sheight;
						ns4layer.document.write(singletext[0]);
						ns4layer.document.close();
						sizeup=ns4layer.document.height;ns4scroll();
					} else if(document.all) {
						iediv=iens6div;iediv.style.pixelTop=sheight;
						iediv.innerHTML=singletext[0];
						sizeup=iediv.offsetHeight;iescroll();
					}
				}

				function iescroll() {
					if(iediv.style.pixelTop > 0 && iediv.style.pixelTop <= sspeed) {
						iediv.style.pixelTop=0;
						setTimeout("iescroll()",spause);
					} else if(iediv.style.pixelTop>=sizeup*-1) {
						iediv.style.pixelTop-=sspeed;
						setTimeout("iescroll()",100);
					} else {
						if (ii==singletext.length-1)ii=0;else ii++;iediv.style.pixelTop=sheight;iediv.innerHTML=singletext[ii];sizeup=iediv.offsetHeight;iescroll();}}

				function ns4scroll() {
					if (ns4layer.top>0&&ns4layer.top<=sspeed) {
						ns4layer.top=0;
						setTimeout("ns4scroll()",spause);
					} else if (ns4layer.top>=sizeup*-1) {
						ns4layer.top-=sspeed;setTimeout("ns4scroll()",100);
					} else {
						if (ii==singletext.length-1)ii=0;else ii++;ns4layer.top=sheight;ns4layer.document.write(singletext[ii]);ns4layer.document.close();sizeup=ns4layer.document.height;ns4scroll();}}
	
				function ns6scroll() {
					if(parseInt(ns6div.style.top) > 0 && parseInt(ns6div.style.top) <= sspeed) {
						ns6div.style.top=0;setTimeout("ns6scroll()",spause);
					}else if(parseInt(ns6div.style.top)>=sizeup*-1) {
						ns6div.style.top=parseInt(ns6div.style.top)-sspeed;setTimeout("ns6scroll()",100);
					} else {
						if (ii==singletext.length-1)ii=0;else ii++;
						ns6div.style.top=sheight;ns6div.innerHTML=singletext[ii];sizeup=ns6div.offsetHeight;ns6scroll();
					}
				}
				</script>
				<table border="0" width="632px" cellspacing="0" cellpadding="0" background="images/roundbox-news.gif">
					<tr height="26px">
						<td width="150px"></td>
						<td valign="middle">
							<script language="JavaScript">
								if(document.getElementById||document.all){document.write(\'<div style="position:relative;overflow:hidden;width:\'+swidth+\';height:\'+sheight+\';clip:rect(0 \'+swidth+\' \'+sheight+\' 0);" onmouseover="sspeed=0;" onmouseout="sspeed=restart"><div id="iens6div" style="position:relative;width:\'+swidth+\';"></div></div>\');}
							</script>
						</td>
					</tr>
				</table>';
	    } else {
	    	echo '</div>';
	    }
	}
}
?>
<!-- latest_news_eof //-->