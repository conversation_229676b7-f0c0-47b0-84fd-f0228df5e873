<?php
/*
  $Id: new_products.php,v 1.5 2010/02/05 11:01:10 henry.chow Exp $

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2003 osCommerce

  Released under the GNU General Public License
*/
?>
<!-- new_products //-->
<?php
  $info_box_contents = array();
//  $info_box_contents[] = array('text' => sprintf(TABLE_HEADING_NEW_PRODUCTS, strftime('%B')));
  $info_box_contents[] = array('align' => 'left', 'text' => '<a href="' . tep_href_link(FILENAME_PRODUCTS_NEW) . '">' . sprintf(TABLE_HEADING_NEW_PRODUCTS, strftime('%B') . '</a>'));
//  new contentBoxHeading($info_box_contents);
  new infoBoxHeading($info_box_contents, false, false, tep_href_link(FILENAME_PRODUCTS_NEW));
  
    //CGDiscountSpecials start
  if ( (!isset($new_products_category_id)) || ($new_products_category_id == '0') ) {
    //$new_products_query = tep_db_query("select p.products_id, p.products_image, p.products_tax_class_id, p.products_price from " . TABLE_PRODUCTS . " p where products_status = '1' order by p.products_date_added desc limit " . MAX_DISPLAY_NEW_PRODUCTS);
    //Categories Status
	$new_products_query = tep_db_query("SELECT p.products_id, pd.products_image, p.products_tax_class_id, 
											IF(s.status, s.specials_new_products_price, p.products_price) as products_price 
										FROM " . TABLE_PRODUCTS . " p 
										LEFT JOIN " . TABLE_SPECIALS . " s 
											ON p.products_id = s.products_id,
										LEFT JOIN " . TABLE_PRODCUTS_DESCRIPTION . " pd 
											ON p.products_id = pd.products_id,
										" . TABLE_PRODUCTS_TO_CATEGORIES . " p2c, " . TABLE_CATEGORIES . " c 
										WHERE c.categories_status='1' 
											AND p.products_id = p2c.products_id 
											AND p2c.categories_id = c.categories_id 
											AND products_status = '1' 
										ORDER BY p.products_date_added 
										DESC LIMIT " . MAX_DISPLAY_NEW_PRODUCTS);

  } else {
    //$new_products_query = tep_db_query("select distinct p.products_id, p.products_image, p.products_tax_class_id, p.products_price as products_price from " . TABLE_PRODUCTS . " p, " . TABLE_PRODUCTS_TO_CATEGORIES . " p2c, " . TABLE_CATEGORIES . " c where p.products_id = p2c.products_id and p2c.categories_id = c.categories_id and c.parent_id = '" . (int)$new_products_category_id . "' and p.products_status = '1' order by p.products_date_added desc limit " . MAX_DISPLAY_NEW_PRODUCTS);
    //Categories Status
    $new_products_query = tep_db_query("SELECT distinct p.products_id, pd.products_image, p.products_tax_class_id, 
    										IF(s.status, s.specials_new_products_price, p.products_price) as products_price 
    									FROM " . TABLE_PRODUCTS . " p 
    									LEFT JOIN " . TABLE_SPECIALS . " s 
    										ON p.products_id = s.products_id, 
    									LEFT JOIN " . TABLE_PRODUCTS_DESCRIPTION . " pd 
    										ON p.products_id = pd.products_id,	
    									" . TABLE_PRODUCTS_TO_CATEGORIES . " p2c, " . TABLE_CATEGORIES . " c 
    									WHERE c.categories_status='1' 
    										AND p.products_id = p2c.products_id 
    										AND p2c.categories_id = c.categories_id 
    										AND c.parent_id = '" . (int)$new_products_category_id . "' 
    										AND p.products_status = '1' 
    									ORDER BY p.products_date_added 
    									DESC LIMIT " . MAX_DISPLAY_NEW_PRODUCTS);

  }
  
  $row = 0;
  $col = 0;
  $info_box_contents = array();
  while ($new_products = tep_db_fetch_array($new_products_query)) {
    $new_products['products_name'] = tep_get_products_name($new_products['products_id']);

	//CGDiscountSpecials start
    if ($new_price = tep_get_products_special_price($new_products['products_id'])) {
		$new_products['products_price'] = $new_price;
        $info_box_contents[$row][$col] = array('align' => 'center',
                                           'params' => 'class="smallText" width="33%" valign="top"',
                                           'text' => '<a href="' . tep_href_link(FILENAME_PRODUCT_INFO, 'products_id=' . $new_products['products_id']) . '">' . tep_image(DIR_WS_IMAGES . $new_products['products_image'], $new_products['products_name'], SMALL_IMAGE_WIDTH, SMALL_IMAGE_HEIGHT) . '</a><br><a href="' . tep_href_link(FILENAME_PRODUCT_INFO, 'products_id=' . $new_products['products_id']) . '">' . $new_products['products_name'] . '</a><br>' . $currencies->display_price_nodiscount($new_products['products_id'], $new_products['products_price'], tep_get_tax_rate($new_products['products_tax_class_id'])));
	} else {
	    $info_box_contents[$row][$col] = array('align' => 'center',
                                           'params' => 'class="smallText" width="33%" valign="top"',
                                           'text' => '<a href="' . tep_href_link(FILENAME_PRODUCT_INFO, 'products_id=' . $new_products['products_id']) . '">' . tep_image(DIR_WS_IMAGES . $new_products['products_image'], $new_products['products_name'], SMALL_IMAGE_WIDTH, SMALL_IMAGE_HEIGHT) . '</a><br><a href="' . tep_href_link(FILENAME_PRODUCT_INFO, 'products_id=' . $new_products['products_id']) . '">' . $new_products['products_name'] . '</a><br>' . $currencies->display_price($new_products['products_id'], $new_products['products_price'], tep_get_tax_rate($new_products['products_tax_class_id'])));
	}
    //CGDiscountSpecials end
    /*
    $info_box_contents[$row][$col] = array('align' => 'center',
                                           'params' => 'class="smallText" width="33%" valign="top"',
                                           'text' => '<a href="' . tep_href_link(FILENAME_PRODUCT_INFO, 'products_id=' . $new_products['products_id']) . '">' . tep_image(DIR_WS_IMAGES . $new_products['products_image'], $new_products['products_name'], SMALL_IMAGE_WIDTH, SMALL_IMAGE_HEIGHT) . '</a><br><a href="' . tep_href_link(FILENAME_PRODUCT_INFO, 'products_id=' . $new_products['products_id']) . '">' . $new_products['products_name'] . '</a><br>' . $currencies->display_price($new_products['products_price'], tep_get_tax_rate($new_products['products_tax_class_id'])));
    */
    $col ++;
    if ($col > 2) {
      $col = 0;
      $row ++;
    }
  }

  new contentBox($info_box_contents);
?>
<!-- new_products_eof //-->
