<?php
/*
  $Id: ot_coupon.php,v 1.36 2015/01/08 03:02:56 weesiong Exp $

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2002 osCommerce

  Released under the GNU General Public License
*/

class ot_coupon {
	var $title, $output;

    function ot_coupon() {
      	$this->code = 'ot_coupon';
      	$this->header = MODULE_ORDER_TOTAL_COUPON_HEADER;
      	$this->title = MODULE_ORDER_TOTAL_COUPON_TITLE;
      	$this->display_title = MODULE_ORDER_TOTAL_COUPON_DISPLAY_TITLE;
      	$this->description = MODULE_ORDER_TOTAL_COUPON_DESCRIPTION;
      	$this->user_prompt = '';
      	$this->enabled = MODULE_ORDER_TOTAL_COUPON_STATUS;
      	$this->sort_order = MODULE_ORDER_TOTAL_COUPON_SORT_ORDER;
      	$this->include_shipping = MODULE_ORDER_TOTAL_COUPON_INC_SHIPPING;
      	$this->include_tax = MODULE_ORDER_TOTAL_COUPON_INC_TAX;
      	$this->calculate_tax = MODULE_ORDER_TOTAL_COUPON_CALC_TAX;
      	$this->tax_class  = MODULE_ORDER_TOTAL_COUPON_TAX_CLASS;
      	$this->credit_class = true;
      	$this->output = array();
   		$this->allow_show = true;
    }

  	function process() {
    	global $PHP_SELF, $order, $currencies, $currency;
    	$order_total=$this->get_order_total();
    	$od_amount = $this->calculate_credit($order_total);
    	//$this->deduction = $od_amount;
    	if ($this->calculate_tax != 'none') {
      		$tod_amount = $this->calculate_tax_deduction($order_total, $this->deduction, $this->calculate_tax);
    	}
    	if ($od_amount > 0) {
    		//$od_amount_in_usd = $currencies->advance_currency_conversion($od_amount, $currency, DEFAULT_CURRENCY, true, 'sell');
    		$od_amount_in_usd = $od_amount;
    		$od_amount_in_currency = $currencies->advance_currency_conversion($od_amount, DEFAULT_CURRENCY, $currency, false, 'buy');
	    	$this->deduction = $od_amount_in_usd;
      		$order->info['total'] = $order->info['total'] - $od_amount_in_usd;
      		
      		$this->output[] = array('title' => $this->title . ': ' . $this->coupon_code,
      								'display_title' => $this->display_title,
                     				'text' => '<b>' . $currencies->format($od_amount_in_currency,false) . '</b>',
                     				'value' => $od_amount_in_usd);
    	}
  	}

  	function selection_test() {
    	return false;
  	}

  	function pre_confirmation_check($order_total) {
    	global $customer_id, $currencies, $currency;
    	return $this->calculate_credit($order_total);
    	//return $currencies->advance_currency_conversion($this->calculate_credit($order_total), DEFAULT_CURRENCY, $currency, false, 'buy');
    }

  	function use_credit_amount() {
    	return $output_string;
  	}

	function credit_selection($amt_display='', $display_type = 'standard') {
    	global $customer_id, $currencies, $language, $error_coupon;

		// If the order has non-store credit product, display the coupon redeem box
    	if ($this->allow_show) {
            if ($display_type == 'standard') {
                $link_submit = '<a href="javascript:;" name="submit_redeem" onClick="submitFunction(); document.checkout_payment.submit();">
                                    <b>' . IMAGE_BUTTON_REDEEM_VOUCHER . '</b>
                                </a>';
                $selection_string = '<tr class="tr_redeem_voucher">
                                        <td style="padding-left:15px;height:45px;">
                                            <table border="0" cellspacing="0" cellpadding="0" width="100%">
                                                <tr>
                                                    <td valign="top">
                                                        <a href="javascript:void(0);" onclick="javascript:jQuery(\'.tr_redeem_voucher\').toggle();">' . LINK_COUPON_CODE . '</a>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                        <td valign="top" class="paymentPriceValue" style="color:red;text-align:right;padding:15px 33px 0px 0px;">' . (tep_not_null($amt_display) ? '- '.$amt_display : '<font style="color:#CCCCCC;">- '.$currencies->format(0).'</font>') . '</td>
                                    </tr>
                                    <tr class="tr_redeem_voucher" style="display:none;">
                                        <td style="padding-left:15px;height:45px;background-color:#EDEDED;" colspan="2">
                                            <table border="0" cellspacing="0" cellpadding="0" width="100%">
                                                <tr>
                                                    <td valign="top" style="padding-top:3px;width:230px;">' . 
                                                        tep_draw_input_field('gv_redeem_code', '', 'size="22"') . 
                                    '				</td>
        										<td valign="top" style="padding-top:3px;width:200px;">' . 
                                                        tep_image_button2('gray_short', 'javascript:void(0);', '<span class="left" style="width:115px;">' . IMAGE_BUTTON_REDEEM_VOUCHER . '</span>', 60, ' style="width:60px;" onClick="submitFunction(); document.checkout_payment.submit();" ') .
                                    '				</td>
                                                    <td style="text-align:center;" width="*%">' . 
                                                        '<span onclick="javascript:jQuery(\'.tr_redeem_voucher\').toggle();" style="cursor:pointer;">' . tep_image('images/icons/error-small.png') . '</span>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>';
            } else if ($display_type == 'basic') {
                $tr_redeem_vocher_input_show = (isset($error_coupon) && $error_coupon !== '' && $error_coupon !== FALSE) ? TRUE : FALSE;
                $link_submit = '<a href="javascript:;" name="submit_redeem" onClick="submitFunction(); document.checkout_payment.submit();">
                                    <b>' . IMAGE_BUTTON_REDEEM_VOUCHER . '</b>
                                </a>';
                $selection_string = '<tr class="tr_redeem_voucher" style="'.($tr_redeem_vocher_input_show?'display:none':'').'">
                                        <td class="lbl">
                                            <table border="0" cellspacing="0" cellpadding="0" width="100%">
                                                <tr>
                                                    <td valign="top">
                                                        <a href="javascript:void(0);" onclick="javascript:jQuery(\'.tr_redeem_voucher\').toggle();">' . LINK_COUPON_CODE . '</a>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                        <td class="paymentPriceValue">' . (tep_not_null($amt_display) ? '- '.$amt_display : '<font style="color:#CCCCCC;">- '.$currencies->format(0).'</font>') . '</td>
                                    </tr>
                                    <tr class="tr_redeem_voucher" style="'.($tr_redeem_vocher_input_show?'':'display:none').'">
                                        <td class="lbl" style="height:45px;background-color:#EDEDED;" colspan="2">
                                            <table border="0" cellspacing="0" cellpadding="0" width="100%">
                                                <tr>
                                                    <td valign="top" style="padding-top:3px;width:230px;">' . 
                                                        tep_draw_input_field('gv_redeem_code', '', 'size="22"') . 
                                    '				</td>
                                                    <td>' . 
                                                        tep_image_button2('gray_short', 'javascript:void(0);', '<span class="left" style="width:115px;">' . IMAGE_BUTTON_REDEEM_VOUCHER . '</span>', 60, ' id="pfv_btn_coupon" style="width:60px;" onclick="pfv_cfm_coupon(this);" ') .
                                    '				</td>
                                                    <td style="text-align: right;" width="*%">' . 
                                                        '<span onclick="javascript:jQuery(\'.tr_redeem_voucher\').toggle();jQuery(this).parents(\'table\').find(\'input\').val(\'\');jQuery(this).parents(\'table\').find(\'div.error_msg\').remove();" style="cursor:pointer;">' . tep_image('/images/icons/error-small.png') . '</span>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td colspan="3">'.($tr_redeem_vocher_input_show?'<div class="error_msg icon_err_msg" style="display:block; margin:0;">'.$error_coupon.'</div>':'').'</td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>';
            }
		} else {
    		$selection_string = '';
		}
		return $selection_string;
    }

  	function collect_posts() {
    	global $HTTP_POST_VARS, $customer_id, $currencies, $cc_id, $error_coupon;
        $error = FALSE;
        $error_message = '';
        
    	if ($HTTP_POST_VARS['gv_redeem_code']) {
			// get some info from the coupon table
      		$coupon_query=tep_db_query("select coupon_id, coupon_amount, coupon_type, coupon_minimum_order,
                                       uses_per_coupon, uses_per_user, uses_per_coupon_unlimited, uses_per_user_unlimited, restrict_to_products,
                                       restrict_to_categories, restrict_to_customers_groups from " . TABLE_COUPONS . "
                                       where coupon_code='".$HTTP_POST_VARS['gv_redeem_code']."'
                                       and coupon_active='Y'");
      		$coupon_result=tep_db_fetch_array($coupon_query);

      		if ($coupon_result['coupon_type'] != 'G') {
        		if (tep_db_num_rows($coupon_query)==0) {
                    $error = TRUE;
                    $error_message = ERROR_NO_INVALID_REDEEM_COUPON;
        		}
                
                if ($error !== TRUE) {
                    $date_query=tep_db_query("select coupon_start_date from " . TABLE_COUPONS . "
                                            where coupon_start_date <= now() and
                                            coupon_code='".$HTTP_POST_VARS['gv_redeem_code']."'");

                    if (tep_db_num_rows($date_query)==0) {
                        $error = TRUE;
                        $error_message = ERROR_INVALID_STARTDATE_COUPON;
                    }
                }
                
                if ($error !== TRUE) {
                    $date_query=tep_db_query("select coupon_expire_date from " . TABLE_COUPONS . "
                                                where coupon_expire_date >= now() and
                                                coupon_code='".$HTTP_POST_VARS['gv_redeem_code']."'");

                    if (tep_db_num_rows($date_query)==0) {
                        $error = TRUE;
                        $error_message = ERROR_INVALID_FINISDATE_COUPON;
                    }
                }
                
                if ($error !== TRUE) {
                    if ($coupon_result['restrict_to_customers_groups'] != 'ALL') {
                        if (!in_array($_SESSION['customers_groups_id'], explode(',', $coupon_result['restrict_to_customers_groups']))) {
                            $error = TRUE;
                            $error_message = ERROR_NO_INVALID_REDEEM_COUPON;
                        }
                    }
                }
                
        		$coupon_count = tep_db_query("select coupon_id from " . TABLE_COUPON_REDEEM_TRACK . "
                                          	where coupon_id = '" . $coupon_result['coupon_id']."'");
        		$coupon_count_customer = tep_db_query("select coupon_id from " . TABLE_COUPON_REDEEM_TRACK . "
                                                   		where coupon_id = '" . $coupon_result['coupon_id']."' and
                                                   		customer_id = '" . $customer_id . "'");
                if ($error !== TRUE) {
                    if ($coupon_result['uses_per_coupon_unlimited']=='N') {
                        if (tep_db_num_rows($coupon_count)>=$coupon_result['uses_per_coupon'] && $coupon_result['uses_per_coupon'] > 0) {
                            $error = TRUE;
                            $error_message = ERROR_INVALID_USES_COUPON;
                        }
                    }
                }
                
                if ($error !== TRUE) {
                    if ($coupon_result['uses_per_user_unlimited']=='N') {
                        if (tep_db_num_rows($coupon_count_customer)>=$coupon_result['uses_per_user'] && $coupon_result['uses_per_user'] > 0) {
                            $error = TRUE;
                            $error_message = ERROR_INVALID_USES_USER_COUPON;
                        }
                    }
                }
                
                if ($error !== TRUE) {
                    if ($coupon_result['coupon_type']=='S') {
                        $coupon_amount = $order->info['shipping_cost'];
                    } else {
                        $coupon_amount = $currencies->format($coupon_result['coupon_amount']) . ' ';
                    }
                    if ($coupon_result['type']=='P') $coupon_amount = $coupon_result['coupon_amount'] . '% ';
                    if ($coupon_result['coupon_minimum_order']>0) $coupon_amount .= 'on orders greater than ' .  $coupon_result['coupon_minimum_order'];
                    $_SESSION['cc_id'] = $coupon_result['coupon_id'];
                }
      		}
      		if ($HTTP_POST_VARS['submit_redeem_coupon_x'] && !$HTTP_POST_VARS['gv_redeem_code']) {
                $error = TRUE;
                $error_message = ERROR_NO_REDEEM_CODE;
            }
            
            if ($error === TRUE) {
                if (isset($error_coupon) && $error_coupon === FALSE) {
                    $error_coupon = $error_message;
                } else {
                    tep_redirect(tep_href_link(FILENAME_CHECKOUT_PAYMENT, 'error_message=' . urlencode($error_message), 'SSL'));
                }
            }
    	}
  	}

  	function calculate_credit($amount) {
    	global $customer_id, $order, $cc_id, $currencies, $currency;
    	$cc_id = $_SESSION['cc_id'];
    	$od_amount = 0;
    	//we need to overwrite the amount value with internal calculated order total amount,
    	//which is deducted the store credit product type amounts.
    	$amount = $this->get_order_total();

    	if ($cc_id) {
      		$coupon_query = tep_db_query("select coupon_code, coupon_amount, coupon_minimum_order, restrict_to_products, restrict_to_categories, coupon_type from " . TABLE_COUPONS . " where coupon_id = '" . $cc_id . "'");
      		if ($coupon_result = tep_db_fetch_array($coupon_query)) {
        		$this->coupon_code = $coupon_result['coupon_code'];
        		// discount coupon default currency is USD. Need to convert to check out currency before deduction.
        		// NOTES: comments this out as it is of no use to convert the coupon amount (fixed/percentage) to check out currency
        		//$c_deduct = $currencies->advance_currency_conversion($coupon_result['coupon_amount'], DEFAULT_CURRENCY, $currency, false, 'sell');
        		$c_deduct = $coupon_result['coupon_amount'];

        		if ($coupon_result['coupon_type']=='S') $c_deduct = $order->info['shipping_cost'];
				
        		if ($this->get_order_total() >= $coupon_result['coupon_minimum_order']) {
	          		if (($coupon_result['coupon_type'] == 'P' || $coupon_result['coupon_type'] == 'F') && ($coupon_result['restrict_to_products'] || $coupon_result['restrict_to_categories']) ) {
		          		//if restrict to products is set, ignore restrict to categories.
		          		$eligible_discount_amount = 0;
		            	for ($i=0; $i<sizeof($order->products); $i++) {
		              		if ($coupon_result['restrict_to_products']) {
		              			$coupon_prod_ids = split_dep("[,]", $coupon_result['restrict_to_products']);
		              			if (in_array(tep_get_prid($order->products[$i]['id']), $coupon_prod_ids)) {
		              				$pr_c = $order->products[$i]['storage_price']['final_price'] * $order->products[$i]['qty'];
		              				if ($coupon_result['coupon_type'] == 'P') {
				              			$pod_amount = round($pr_c*100)/100*$c_deduct/100;	// change from 10 to 100 to increase the rounded to have 2 decimals
				              			$od_amount = $od_amount + $pod_amount;
				              		} else {
				              			$eligible_discount_amount += $pr_c;
				              		}
		              			}
		              		} else {	//restrict_to_categories
								$coupon_cat_ids = split_dep("[,]", $coupon_result['restrict_to_categories']);
								$my_path = tep_get_product_path(tep_get_prid($order->products[$i]['id']));
								$product_cat_ids = split_dep("[_]", $my_path);
								
								$matching_cat_ids = array_intersect($product_cat_ids, $coupon_cat_ids);
								if (count($matching_cat_ids) > 0) {
									//calculate once per product if catid specified in coupon setup matches its catid, or any of its parent's catid
									(float)$pr_c = $order->products[$i]['storage_price']['final_price'] * $order->products[$i]['qty'];
									if ($coupon_result['coupon_type'] == 'P') {
										$od_amount += round($pr_c * 100)/100 * $c_deduct/100;	// change from 10 to 100 to increase the rounded to have 2 decimals
									} else {
										$eligible_discount_amount += $pr_c;
									}
								}
		              		}
		            	}// end loop product
		            	
		            	if ($coupon_result['coupon_type'] != 'P') {
		            		if ($c_deduct > $eligible_discount_amount) {
		            			$od_amount = $eligible_discount_amount;
		            		} else {
		            			$od_amount = $c_deduct;
		            		}
		            	}
	          		} elseif ($coupon_result['coupon_type'] !='P') {
						$od_amount = $c_deduct;
	          		} else {
						$od_amount = $amount * $coupon_result['coupon_amount'] / 100;
	          		}
				}// end minimum order amount ok.
			}
			
      		if ($od_amount > $amount) $od_amount = $amount;
    	}
    	
    	return $od_amount;
  	}

	function calculate_tax_deduction($amount, $od_amount, $method) {
    	global $customer_id, $order, $cc_id, $cart;
    	$cc_id = $_SESSION['cc_id'];
    	$coupon_query = tep_db_query("select coupon_code from " . TABLE_COUPONS . " where coupon_id = '" . $cc_id . "'");
    	
    	if (tep_db_num_rows($coupon_query) !=0 ) {
      		$coupon_result = tep_db_fetch_array($coupon_query);
      		$coupon_get = tep_db_query("select coupon_amount, coupon_minimum_order, restrict_to_products, restrict_to_categories, coupon_type from " . TABLE_COUPONS . " where coupon_code = '". $coupon_result['coupon_code'] . "'");
      		$get_result = tep_db_fetch_array($coupon_get);
      		if ($get_result['coupon_type'] != 'S') {
      			if ($get_result['restrict_to_products'] || $get_result['restrict_to_categories']) {
		        // What to do here.
		        // Loop through all products and build a list of all product_ids, price, tax class
		        // at the same time create total net amount.
		        // then
		        // for percentage discounts. simply reduce tax group per product by discount percentage
		        // or
		        // for fixed payment amount
		        // calculate ratio based on total net
		        // for each product reduce tax group per product by ratio amount.
	        		$products = $cart->get_products();
	        		for ($i=0; $i<sizeof($products); $i++) {
			     		$t_prid = tep_get_prid($products[$i]['id']);
			          	$cc_query = tep_db_query("select products_tax_class_id from " . TABLE_PRODUCTS . " where products_id = '" . $t_prid . "'");
			          	$cc_result = tep_db_fetch_array($cc_query);
			          	$valid_product = false;
	          			
	          			if ($get_result['restrict_to_products']) {
	            			$pr_ids = split_dep("[,]", $get_result['restrict_to_products']);
	            			for ($p = 0; $p < sizeof($pr_ids); $p++) {
	              				if ($pr_ids[$p] == $t_prid) $valid_product = true;
	            			}
	          			}
	          			
	          			if ($get_result['restrict_to_categories']) {
	            			$cat_ids = split_dep("[,]", $get_result['restrict_to_categories']);
	            			
							$my_path = tep_get_product_path($t_prid);
							$product_cat_ids = split_dep("[_]", $my_path);
							
							$matching_cat_ids = array_intersect($product_cat_ids, $cat_ids);
							if (count($matching_cat_ids) > 0) {
								$valid_product = true;
	            			}
	          			}
	          			
	          			if ($valid_product) {
	            			$valid_array[] = array(	'product_id' => $t_prid,
	                                 				'products_price' => $products[$i]['final_price'] * $products[$i]['quantity'],
	                                 				'products_tax_class' => $cc_result['products_tax_class_id']);
	            			$total_price += $products[$i]['final_price'] * $products[$i]['quantity'];
	          			}
	        		}
	        		
	        		if ($valid_product) {
	        			if ($get_result['coupon_type'] == 'P') {
	          				$ratio = $get_result['coupon_amount']/100;
	        			} else {
	          				$ratio = $od_amount / $total_price;
	        			}
	        			
	        			if ($get_result['coupon_type'] == 'S') $ratio = 1;
	      				if ($method=='Credit Note') {
	        				$tax_rate = tep_get_tax_rate($this->tax_class, $order->delivery['country']['id'], $order->delivery['zone_id']);
	        				$tax_desc = tep_get_tax_description($this->tax_class, $order->delivery['country']['id'], $order->delivery['zone_id']);
	        				if ($get_result['coupon_type'] == 'P') {
	          					$tod_amount = $od_amount / (100 + $tax_rate)* $tax_rate;
	        				} else {
	          					$tod_amount = $order->info['tax_groups'][$tax_desc] * $od_amount/100;
	        				}
	        				$order->info['tax_groups'][$tax_desc] -= $tod_amount;
	        				$order->info['total'] -= $tod_amount;
	      				} else {
	        				for ($p=0; $p<sizeof($valid_array); $p++) {
	          					$tax_rate = tep_get_tax_rate($valid_array[$p]['products_tax_class'], $order->delivery['country']['id'], $order->delivery['zone_id']);
	          					$tax_desc = tep_get_tax_description($valid_array[$p]['products_tax_class'], $order->delivery['country']['id'], $order->delivery['zone_id']);
	          					if ($tax_rate > 0) {
	            					$tod_amount[$tax_desc] += ($valid_array[$p]['products_price'] * $tax_rate)/100 * $ratio;
	            					$order->info['tax_groups'][$tax_desc] -= ($valid_array[$p]['products_price'] * $tax_rate)/100 * $ratio;
	            					$order->info['total'] -= ($valid_array[$p]['products_price'] * $tax_rate)/100 * $ratio;
	          					}
	        				}
	      				}
	    			}
	  			} else {
	        		if ($get_result['coupon_type'] =='F') {
	          			$tod_amount = 0;
	          			if ($method=='Credit Note') {
	            			$tax_rate = tep_get_tax_rate($this->tax_class, $order->delivery['country']['id'], $order->delivery['zone_id']);
	            			$tax_desc = tep_get_tax_description($this->tax_class, $order->delivery['country']['id'], $order->delivery['zone_id']);
	            			$tod_amount = $od_amount / (100 + $tax_rate)* $tax_rate;
	            			$order->info['tax_groups'][$tax_desc] -= $tod_amount;
	          			} else {
	            			$ratio1 = $od_amount/$amount;
	            			reset($order->info['tax_groups']);
	            			while (list($key, $value) = each($order->info['tax_groups'])) {
	              				$tax_rate = tep_get_tax_rate_from_desc($key);
	              				$net = $tax_rate * $order->info['tax_groups'][$key];
	              				if ($net>0) {
	                				$god_amount = $order->info['tax_groups'][$key] * $ratio1;
	                				$tod_amount += $god_amount;
	                				$order->info['tax_groups'][$key] = $order->info['tax_groups'][$key] - $god_amount;
	              				}
	            			}
	          			}
	          			$order->info['total'] -= $tod_amount;
	        		}
	        		
	        		if ($get_result['coupon_type'] =='P') {
	          			$tod_amount=0;
	          			if ($method=='Credit Note') {
	            			$tax_desc = tep_get_tax_description($this->tax_class, $order->delivery['country']['id'], $order->delivery['zone_id']);
	            			$tod_amount = $order->info['tax_groups'][$tax_desc] * $od_amount/100;
	            			$order->info['tax_groups'][$tax_desc] -= $tod_amount;
	          			} else {
	            			reset($order->info['tax_groups']);
	            			while (list($key, $value) = each($order->info['tax_groups'])) {
	              				$god_amout=0;
	              				$tax_rate = tep_get_tax_rate_from_desc($key);
	              				$net = $tax_rate * $order->info['tax_groups'][$key];
	              				if ($net>0) {
	                				$god_amount = $order->info['tax_groups'][$key] * $get_result['coupon_amount']/100;
	                				$tod_amount += $god_amount;
	                				$order->info['tax_groups'][$key] = $order->info['tax_groups'][$key] - $god_amount;
	              				}
	            			}
	          			}
	          			$order->info['tax'] -= $tod_amount;
	        		}
	      		}
	    	}
	    }
    	
   	 	return $tod_amount;
	}

function update_credit_account($i) {
  return false;
 }

 function apply_credit($new_order_id) {
   global $insert_id, $customer_id, $REMOTE_ADDR, $cc_id;
   $cc_id = $_SESSION['cc_id'];
   if ($this->deduction !=0) {
     tep_db_query("insert into " . TABLE_COUPON_REDEEM_TRACK . " (coupon_id, redeem_date, redeem_ip, customer_id, order_id) values ('" . $cc_id . "', now(), '" . $REMOTE_ADDR . "', '" . $customer_id . "', '" . $insert_id . "')");
   }
   tep_session_unregister('cc_id');
 }

  	function get_order_total() {
    	global $order, $cart, $customer_id, $cc_id, $currencies, $currency;

    	$cc_id = $_SESSION['cc_id'];
    	$order_total = $currencies->advance_currency_conversion($cart->total, $currency, DEFAULT_CURRENCY, false, 'sell');
		// Check if gift voucher is in cart and adjust total
    	$products = $cart->get_products();
    	
    	for ($i=0; $i<sizeof($products); $i++) {
      		$t_prid = tep_get_prid($products[$i]['id']);
      		$gv_query = tep_db_query("select products_price, products_tax_class_id, products_model from " . TABLE_PRODUCTS . " where products_id = '" . $t_prid . "'");
      		$gv_result = tep_db_fetch_array($gv_query);
      		if (ereg_dep('^GIFT', addslashes($gv_result['products_model']))) {
        		$qty = $cart->get_quantity($t_prid);
        		$products_tax = tep_get_tax_rate($gv_result['products_tax_class_id']);
        		if ($this->include_tax =='false') {
           			$gv_amount = $gv_result['products_price'] * $qty;
        		} else {
          			$gv_amount = ($gv_result['products_price'] + tep_calculate_tax($gv_result['products_price'],$products_tax)) * $qty;
        		}
        		$order_total=$order_total - $gv_amount;
      		}
    	}
    	
    	// get total order amount that excluded custom product type 3
    	// can not use ot_total amount cause the amount has included surcharge.
    	$non_type3_total = 0;
    	$type3_total = 0;
    	
    	foreach($products as $idx => $product_array) {
      		if ($product_array['custom_products_type_id'] == 3) {
	      		$type3_total = $type3_total + ($product_array['final_price'] * $product_array['quantity']);
      		}
    	}
    	$non_type3_total = $order_total - $type3_total;
    	$order_total = $order_total - $type3_total;
    	
    	if ($non_type3_total == 0) {
    		// since there is no non-type 3 products in the cart, dont show coupon box
    		$this->allow_show = false;
    	}
    	
    	if ($this->include_tax == 'false') $order_total=$order_total-$order->info['tax'];
    	if ($this->include_shipping == 'false') $order_total=$order_total-$order->info['shipping_cost'];
	    // OK thats fine for global coupons but what about restricted coupons
	    // where you can only redeem against certain products/categories.
	    // and I though this was going to be easy !!!
   		$coupon_query=tep_db_query("select coupon_code from " . TABLE_COUPONS . " where coupon_id='".$cc_id."'");
   		if (tep_db_num_rows($coupon_query) !=0) {
     		$coupon_result=tep_db_fetch_array($coupon_query);
     		$coupon_get=tep_db_query("select coupon_amount, coupon_minimum_order,restrict_to_products,restrict_to_categories, coupon_type from " . TABLE_COUPONS . " where coupon_code='".$coupon_result['coupon_code']."'");
     		$get_result=tep_db_fetch_array($coupon_get);

     		$total_price = 0;
     		if ($get_result['restrict_to_products']) {
       			$pr_ids = split_dep("[,]", $get_result['restrict_to_products']);

       			$products_array = $cart->get_products();

       			for ($i = 0; $i < sizeof($pr_ids); $i++) {
         			if (is_array($order->products)) {
           				for ($oProdCnt=0; $oProdCnt < count($order->products); $oProdCnt++) {
           					$products_id = $order->products[$oProdCnt]['id'];

             				if ($products_id == $pr_ids[$i]) {
               					$total_price += $order->products[$oProdCnt]['qty'] * $order->products[$oProdCnt]['storage_price']['final_price'];
             				}
           				}
         			}
       			}
       			$order_total = $total_price;
     		} else if ($get_result['restrict_to_categories']) {
       			$cat_ids = split_dep("[,]", $get_result['restrict_to_categories']);
				
     			if (is_array($order->products)) {
       				for ($oProdCnt=0; $oProdCnt < count($order->products); $oProdCnt++) {
       					$products_id = $order->products[$oProdCnt]['id'];
						
						$my_path = tep_get_product_path($products_id);
						$product_cat_ids = split_dep("[_]", $my_path);
						
						$matching_cat_ids = array_intersect($product_cat_ids, $cat_ids);
						if (count($matching_cat_ids) > 0) {
							$total_price += $order->products[$oProdCnt]['qty'] * $order->products[$oProdCnt]['storage_price']['final_price'];
         				}
       				}
     			}
       			
       			$order_total = $total_price;
     		}
   		}
		
   		return number_format($order_total,8,'.','');
  	}

	function get_product_price($product_id) {
    	global $cart, $order;

    	$products_id = tep_get_prid($product_id);
 		// products price
    	$qty = $cart->contents[$product_id]['qty'];
    	$product_query = tep_db_query("select products_id, products_price, products_tax_class_id, products_weight from " . TABLE_PRODUCTS . " where products_id='" . $product_id . "'");
    	if ($product = tep_db_fetch_array($product_query)) {
      		$prid = $product['products_id'];
      		$products_tax = tep_get_tax_rate($product['products_tax_class_id']);
      		$products_price = $product['products_price'];
      		$specials_query = tep_db_query("select specials_new_products_price from " . TABLE_SPECIALS . " where products_id = '" . $prid . "' and status = '1'");
      		if (tep_db_num_rows ($specials_query)) {
        		$specials = tep_db_fetch_array($specials_query);
        		$products_price = $specials['specials_new_products_price'];
      		}
      		if ($this->include_tax == 'true') {
        		$total_price += ($products_price + tep_calculate_tax($products_price, $products_tax)) * $qty;
      		} else {
        		$total_price += $products_price * $qty;
      		}

			// attributes price
      		if (isset($cart->contents[$product_id]['attributes'])) {
        		reset($cart->contents[$product_id]['attributes']);
        		while (list($option, $value) = each($cart->contents[$product_id]['attributes'])) {
          			$attribute_price_query = tep_db_query("select options_values_price, price_prefix from " . TABLE_PRODUCTS_ATTRIBUTES . " where products_id = '" . $prid . "' and options_id = '" . $option . "' and options_values_id = '" . $value . "'");
          			$attribute_price = tep_db_fetch_array($attribute_price_query);
          			if ($attribute_price['price_prefix'] == '+') {
            			if ($this->include_tax == 'true') {
	              			$total_price += $qty * ($attribute_price['options_values_price'] + tep_calculate_tax($attribute_price['options_values_price'], $products_tax));
	            		} else {
	              			$total_price += $qty * ($attribute_price['options_values_price']);
	            		}
          			} else {
	            		if ($this->include_tax == 'true') {
	              			$total_price -= $qty * ($attribute_price['options_values_price'] + tep_calculate_tax($attribute_price['options_values_price'], $products_tax));
	            		} else {
	              			$total_price -= $qty * ($attribute_price['options_values_price']);
	            		}
	          		}
	        	}
	      	}
		}
    	if ($this->include_shipping == 'true') $total_price += $order->info['shipping_cost'];
    	return $total_price;
	}

    function check() {
      if (!isset($this->check)) {
        $check_query = tep_db_query("select configuration_value from " . TABLE_CONFIGURATION . " where configuration_key = 'MODULE_ORDER_TOTAL_COUPON_STATUS'");
        $this->check = tep_db_num_rows($check_query);
      }

      return $this->check;
    }

    function keys() {
      return array('MODULE_ORDER_TOTAL_COUPON_STATUS', 'MODULE_ORDER_TOTAL_COUPON_SORT_ORDER', 'MODULE_ORDER_TOTAL_COUPON_INC_SHIPPING', 'MODULE_ORDER_TOTAL_COUPON_INC_TAX', 'MODULE_ORDER_TOTAL_COUPON_CALC_TAX', 'MODULE_ORDER_TOTAL_COUPON_TAX_CLASS');
    }

    function install() {
      tep_db_query("insert into " . TABLE_CONFIGURATION . " (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, set_function, date_added) values ('Display Total', 'MODULE_ORDER_TOTAL_COUPON_STATUS', 'true', 'Do you want to display the Discount Coupon value?', '6', '1','tep_cfg_select_option(array(\'true\', \'false\'), ', now())");
      tep_db_query("insert into " . TABLE_CONFIGURATION . " (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, date_added) values ('Sort Order', 'MODULE_ORDER_TOTAL_COUPON_SORT_ORDER', '9', 'Sort order of display.', '6', '2', now())");
      tep_db_query("insert into " . TABLE_CONFIGURATION . " (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, set_function ,date_added) values ('Include Shipping', 'MODULE_ORDER_TOTAL_COUPON_INC_SHIPPING', 'true', 'Include Shipping in calculation', '6', '5', 'tep_cfg_select_option(array(\'true\', \'false\'), ', now())");
      tep_db_query("insert into " . TABLE_CONFIGURATION . " (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, set_function ,date_added) values ('Include Tax', 'MODULE_ORDER_TOTAL_COUPON_INC_TAX', 'true', 'Include Tax in calculation.', '6', '6','tep_cfg_select_option(array(\'true\', \'false\'), ', now())");
      tep_db_query("insert into " . TABLE_CONFIGURATION . " (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, set_function ,date_added) values ('Re-calculate Tax', 'MODULE_ORDER_TOTAL_COUPON_CALC_TAX', 'None', 'Re-Calculate Tax', '6', '7','tep_cfg_select_option(array(\'None\', \'Standard\', \'Credit Note\'), ', now())");
      tep_db_query("insert into " . TABLE_CONFIGURATION . " (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, use_function, set_function, date_added) values ('Tax Class', 'MODULE_ORDER_TOTAL_COUPON_TAX_CLASS', '0', 'Use the following tax class when treating Discount Coupon as Credit Note.', '6', '0', 'tep_get_tax_class_title', 'tep_cfg_pull_down_tax_classes(', now())");
    }

    function remove() {
      $keys = '';
      $keys_array = $this->keys();
      for ($i=0; $i<sizeof($keys_array); $i++) {
        $keys .= "'" . $keys_array[$i] . "',";
      }
      $keys = substr($keys, 0, -1);

      tep_db_query("delete from " . TABLE_CONFIGURATION . " where configuration_key in (" . $keys . ")");
    }
  }
?>