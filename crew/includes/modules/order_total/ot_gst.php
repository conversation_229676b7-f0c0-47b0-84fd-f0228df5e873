<?php

class ot_gst {
	public $title = '';
	public $output = array();
	public $gst_percentage = 0;
	public $gst_title = '';
	public $gst_display_title = '';
	public $round_decimal_point = 4;
	
	public function __construct() {
		$this->code = 'ot_gst';
		$this->title = MODULE_ORDER_TOTAL_GST_TITLE;
		$this->description = MODULE_ORDER_TOTAL_GST_DESCRIPTION;
		$this->enabled = ((MODULE_ORDER_TOTAL_GST_STATUS == 'true') ? true : false);
		$this->sort_order = MODULE_ORDER_TOTAL_GST_SORT_ORDER;
		$this->output = array();
	}
	
	public function process() {
		global $order, $currencies;
		
		include_once(DIR_WS_CLASSES . 'localization.php');
		localization::verify_gst_condition();
		
		if (tep_not_null($_SESSION['RegionGST']['loc_country']) && ($_SESSION['RegionGST']['currency']) && ($_SESSION['RegionGST']['tax_title'])) {
			$this->get_gst_tax_info();
			
			$order_total = $this->get_order_total();
			$gst = $this->calculate_gst($order_total);
			
			if ($gst > 0) {
				$gst_display = $currencies->advance_currency_conversion($gst, DEFAULT_CURRENCY, $_SESSION['currency'], false, 'buy');
				$gst_display = number_format($gst_display, $currencies->currencies[$_SESSION['currency']]['decimal_places'], $currencies->currencies[$_SESSION['currency']]['decimal_point'], '');
				
				$order->info['total'] = (double)trim($order->info['total']) + (double)trim($gst);
				
				$this->output[] = array('title' => $this->gst_title . ' (' . $this->gst_percentage . '%):',
										'display_title' => $this->gst_display_title . ':',
										'text' => '<b>' . $currencies->format($gst_display, true, $_SESSION['currency'], 1) . '</b>',
										'display_text' => '+ ' . $currencies->format($gst_display, true, $_SESSION['currency'], 1),
										'value' => $gst);
			}
		}
	}
	
	public function sc_process() {
		global $order, $currencies;
		
		include_once(DIR_WS_CLASSES . 'localization.php');
		localization::verify_gst_condition();
		
		if (tep_not_null($_SESSION['RegionGST']['loc_country']) && ($_SESSION['RegionGST']['currency']) && ($_SESSION['RegionGST']['tax_title'])) {
			$this->get_gst_tax_info();
			
			list($customers_sc_currency, $order_total) = $this->get_order_sc_total();
			
			if ($customers_sc_currency != DEFAULT_CURRENCY) {
				$order_total = $currencies->advance_currency_conversion($order_total, $customers_sc_currency, $_SESSION['currency']);
				$order_total = $currencies->advance_currency_conversion($order_total, $_SESSION['currency'], DEFAULT_CURRENCY, false, 'sell');
			}
			
			$gst = $this->calculate_gst($order_total);
			
			if ($gst > 0) {
				$sc_gst = $currencies->advance_currency_conversion($gst, DEFAULT_CURRENCY, $_SESSION['currency'], false, 'buy');
				$gst_display = number_format($sc_gst, $currencies->currencies[$_SESSION['currency']]['decimal_places'], $currencies->currencies[$_SESSION['currency']]['decimal_point'], '');
				
				if (tep_not_null($_SESSION['sc_gst']) && ($_SESSION['sc_gst_checked'] == false)) {
					$_SESSION['sc_gst'] = (double)trim($sc_gst);
					$_SESSION['sc_gst_checked'] = true;
				}
				
				$order->info['total'] = (double)trim($order->info['total']) + (double)trim($gst);
				
				$this->output[] = array('title' => $this->gst_title . ' (' . $this->gst_percentage . '%):',
										'display_title' => $this->gst_display_title . ':',
										'text' => '<b>' . $currencies->format($gst_display, true, $_SESSION['currency'], 1) . '</b>',
										'display_text' => '+ ' . $currencies->format($gst_display, true, $_SESSION['currency'], 1),
										'value' => $gst);
			}
		}
	}
	
	public function get_gst_currency($country) {
		$gst_currency = '';
		
		$gst_currency_select_sql = "SELECT otc.currency 
									FROM " . TABLE_COUNTRIES . " AS c 
									INNER JOIN " . TABLE_ORDERS_TAX_CONFIGURATION . " AS otc 
										ON otc.country_code = c.countries_iso_code_2 
									WHERE c.countries_id = '" . $country . "' 
										AND otc.orders_tax_status = '1'
									LIMIT 1";
		$gst_currency_result_sql = tep_db_query($gst_currency_select_sql);
		if ($row = tep_db_fetch_array($gst_currency_result_sql)) {
			$gst_currency = $row['currency'];
		}
		
		return $gst_currency;
	}
	
	public function get_gst_title() {
		$title = '';
		
		$gst_title_select_sql = "	SELECT otcd.orders_tax_title
									FROM " . TABLE_COUNTRIES . " AS c 
									INNER JOIN " . TABLE_ORDERS_TAX_CONFIGURATION . " AS otc 
										ON otc.country_code = c.countries_iso_code_2 
									INNER JOIN " . TABLE_ORDERS_TAX_CONFIGURATION_DESCRIPTION . " AS otcd 
										ON otcd.orders_tax_id = otc.orders_tax_id 
									WHERE c.countries_id = '" . $_SESSION['RegionGST']['loc_country'] . "' 
										AND otc.orders_tax_status = '1' 
										AND otc.currency = '" . $_SESSION['RegionGST']['currency'] . "' 
										AND otcd.language_id = '" . ( tep_not_null($_SESSION['languages_id']) ? $_SESSION['languages_id'] : SYSTEM_DEFAULT_LANGUAGE_ID ) . "'";
		$gst_title_result_sql = tep_db_query($gst_title_select_sql);
		if ($row = tep_db_fetch_array($gst_title_result_sql)) {
			$title = $row['orders_tax_title'];
		}
		
		$this->gst_display_title = $title;
		return $title;
	}
	
	public function get_gst_tax_info() {
		$percentage = 0;
		$title = '';
		$display_title = '';
		
		$gst_tax_title_select_sql = "	SELECT otc.orders_tax_percentage, otcd1.orders_tax_title AS orders_tax_title, 
											otcd2.orders_tax_title AS orders_tax_display_title 
										FROM " . TABLE_COUNTRIES . " AS c 
										INNER JOIN " . TABLE_ORDERS_TAX_CONFIGURATION . " AS otc 
											ON otc.country_code = c.countries_iso_code_2 
										INNER JOIN " . TABLE_ORDERS_TAX_CONFIGURATION_DESCRIPTION . " AS otcd1 
											ON otcd1.orders_tax_id = otc.orders_tax_id 
										INNER JOIN " . TABLE_ORDERS_TAX_CONFIGURATION_DESCRIPTION . " AS otcd2 
											ON otcd2.orders_tax_id = otc.orders_tax_id 
										WHERE c.countries_id = '" . $_SESSION['country'] . "' 
											AND otc.orders_tax_status = '1' " .
											( tep_not_null($_SESSION['currency']) ? " AND otc.currency = '" . $_SESSION['currency'] . "' " : '' ) . " 
											AND otcd1.language_id = '" . $_SESSION['default_languages_id'] . "' 
											AND otcd2.language_id = '" . ( tep_not_null($_SESSION['languages_id']) ? $_SESSION['languages_id'] : $_SESSION['default_languages_id'] ) . "'";
		$gst_tax_title_result_sql = tep_db_query($gst_tax_title_select_sql);
		if ($row = tep_db_fetch_array($gst_tax_title_result_sql)) {
			if (tep_not_null($row['orders_tax_percentage']) && ($row['orders_tax_percentage'] > 0)) {
				$percentage = $row['orders_tax_percentage'];
				$title = $row['orders_tax_title'];
				$display_title = $row['orders_tax_display_title'];
			}
		}
		
		$this->gst_percentage = $percentage;
		$this->gst_title = $title;
		$this->gst_display_title = (tep_not_null($display_title) ? $display_title . ' (' . $percentage . '%)' : '');
	}
	
	public function calculate_gst($order_total) {
		if (tep_not_null($this->gst_percentage)) {
			$percentage = ( $this->gst_percentage > 0 ? $this->gst_percentage / 100 : 0 );
			$gst = $order_total * $percentage;
			
			return tep_round($gst, $this->round_decimal_point);
		} else {
			return false;
		}
	}
	
	function get_order_total() {
		global $order;
		
		return $order->info['total'];
	}
	
	function get_order_sc_total() {
		global $currencies;
		
		$sc_orders = array();
		
		$customers_ordered_sc = 0;
		$customers_sc_currency = tep_get_customer_store_credit_currency($_SESSION['customer_id'], false);	// SC Product currency always follow Customer SC Balance Currency
		$customers_sc_select_sql = "SELECT cscq.customers_sc_cart_quantity, p.custom_products_type_id, p.products_price, 
										p.custom_products_type_id 
									FROM " . TABLE_CUSTOMERS_SC_CART . " AS cscq 
									INNER JOIN " . TABLE_PRODUCTS . " AS p 
										ON p.products_id = cscq.products_id 
									WHERE cscq.customers_id = '" . (int)$_SESSION['customer_id'] . "'";
		$customers_sc_result_sql = tep_db_query($customers_sc_select_sql);
		if ($customers_sc_row = tep_db_fetch_array($customers_sc_result_sql)) {
			if ($customers_sc_row['custom_products_type_id'] == 3) {
				$customers_ordered_sc = $customers_sc_row['customers_sc_cart_quantity'];
			} else {
				$customers_ordered_sc = $customers_sc_row['products_price'];
			}
		}
		
		$sc_orders = array ( $customers_sc_currency, $customers_ordered_sc );
		
		return $sc_orders;
	}
	
	
	// crew module installation
	public function check() {
		if (!isset($this->check)) {
			$check_query = tep_db_query("SELECT configuration_value FROM " . TABLE_CONFIGURATION . " WHERE configuration_key = 'MODULE_ORDER_TOTAL_GST_STATUS'");
			$this->check = tep_db_num_rows($check_query);
		}

		return $this->check;
	}
	
	public function keys() {
		return array('MODULE_ORDER_TOTAL_GST_STATUS', 'MODULE_ORDER_TOTAL_GST_SORT_ORDER');
	}
	
	public function install() {
		tep_db_query("INSERT INTO " . TABLE_CONFIGURATION . " (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, set_function, date_added) values ('Display Total', 'MODULE_ORDER_TOTAL_GST_STATUS', 'true', 'Do you want to display the Orders Tax value?', '6', '1','tep_cfg_select_option(array(\'true\', \'false\'), ', NOW())");
		tep_db_query("INSERT INTO " . TABLE_CONFIGURATION . " (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, date_added) values ('Sort Order', 'MODULE_ORDER_TOTAL_GST_SORT_ORDER', '800', 'Sort order of display.', '6', '2', NOW())");
	}
	
	function remove() {
		tep_db_query("DELETE FROM " . TABLE_CONFIGURATION . " WHERE configuration_key IN ('" . implode("', '", $this->keys()) . "')");
	}
}
?>