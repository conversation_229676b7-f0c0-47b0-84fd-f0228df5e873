<?php
/*
  	$Id: ot_gv.php,v 1.34 2014/09/25 08:21:28 sionghuat.chng Exp $

  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com

  	Copyright (c) 2002 osCommerce

  	Released under the GNU General Public License
*/

class ot_gv {
	var $title, $output;

    function ot_gv()
    {
      	$this->code = 'ot_gv';
      	$this->display_title = MODULE_ORDER_TOTAL_GV_DISPLAY_TITLE;
      	$this->title = MODULE_ORDER_TOTAL_GV_TITLE;
      	//$this->header = MODULE_ORDER_TOTAL_GV_HEADER;
      	$this->description = MODULE_ORDER_TOTAL_GV_DESCRIPTION;
      	$this->user_prompt = MODULE_ORDER_TOTAL_GV_USER_PROMPT;
      	$this->enabled = MODULE_ORDER_TOTAL_GV_STATUS;
      	$this->sort_order = MODULE_ORDER_TOTAL_GV_SORT_ORDER;
      	$this->include_shipping = MODULE_ORDER_TOTAL_GV_INC_SHIPPING;
      	$this->include_tax = MODULE_ORDER_TOTAL_GV_INC_TAX;
      	$this->calculate_tax = MODULE_ORDER_TOTAL_GV_CALC_TAX;
      	$this->credit_tax = MODULE_ORDER_TOTAL_GV_CREDIT_TAX;
      	$this->tax_class  = MODULE_ORDER_TOTAL_GV_TAX_CLASS;
      	$this->show_redeem_box = MODULE_ORDER_TOTAL_GV_REDEEM_BOX;
      	$this->credit_class = true;
      	//$this->checkbox = $this->user_prompt . '<input type="checkbox" onClick="submitFunction()" name="' . 'c' . $this->code . '" CHECKED>';
      	$this->checkbox = $this->user_prompt . '<input type="hidden" value="on" name="' . 'c' . $this->code . '">';
      	$this->output = array();
      	$this->store_credit_only = false;
        $this->store_credit_remain_formatted = '';
    }

    function process()
    {
      	global $order, $currencies, $currency;

        $ot_cid = $this->get_customer_id();

      	if ($_SESSION['cot_gv'] && round($order->info['total'], $currencies->currencies[$currency]['decimal_places']) > 0) {
        	if ($this->user_has_gv_account($ot_cid)) {
                $order_total = $this->get_order_total();
                // convert to payment checkout currency
                $order_total = $currencies->advance_currency_conversion($order_total, DEFAULT_CURRENCY, $currency, false, 'buy');
                $html_od_amount = $this->calculate_credit($order_total);

                if ($this->calculate_tax != "None") {
                    $tod_amount = $this->calculate_tax_deduction($order_total, $html_od_amount, $this->calculate_tax);
                    $html_od_amount = $this->calculate_credit($order_total);
                }
                $od_amount = $currencies->advance_currency_conversion($html_od_amount, $currency, DEFAULT_CURRENCY, false, 'sell');

                if ($_SESSION['need_sc_usage_qna'] != 'true') {
                    $conv_order_total = $currencies->advance_currency_conversion($order->info['total'], DEFAULT_CURRENCY, $currency, false, 'buy');
                    $conv_total = $conv_order_total - $html_od_amount;
                    if (($conv_total == 0) && ($od_amount != $order->info['total'])) {
                        $od_amount = $order->info['total'];
                    }
                    $this->deduction = $od_amount;
                    $order->info['total'] = number_format($currencies->advance_currency_conversion($conv_total, $currency, DEFAULT_CURRENCY, false, 'sell'), 6, '.', '');
                }

                $display_html = '<table border="0" cellspacing="0" cellpadding="0" width="100%">
                                    <tr>
                                        <td valign="top">
                                            ' . TEXT_INFO_STORE_CREDIT_DEDUCTION;
                if ($od_amount == 0 && $this->store_credit_only === false) {
//                    $display_html .= ' <a href="javascript:showSCConfirm();">(' . $this->description . ')</a>';
                } else if ($_SESSION['need_sc_usage_qna'] == 'true') {
                    $display_html .= ' <a href="javascript:checkoutQnA();">(' . $this->description . ')</a>';
                }
                $display_html .= 	'	</td>
                                    </tr>';
                $display_html .= '</table>';

                if ($this->store_credit_only === false && $_SESSION['need_sc_usage_qna'] != 'true') {
                    $sc_amount = ''.round($currencies->advance_currency_conversion($this->store_credit_amount, $currency, DEFAULT_CURRENCY, true, 'sell'), $currencies->currencies[$currency]['decimal_places']);
                    $total_amount = ''.round($order->info['total'], $currencies->currencies[$currency]['decimal_places']);
                    if ($od_amount > 0 || $sc_amount != $total_amount) {
                        $this->output[] = array('title' => $this->title .':',
                                                'display_title' => $display_html,
                                                'simple_display_title' => TEXT_INFO_STORE_CREDIT_DEDUCTION,
                                                'text' => '<b>' . $currencies->format($html_od_amount,true,$currency,1) . '</b>',
                                                'display_text' => ($html_od_amount > 0 ? '- ' . $currencies->format($html_od_amount,true,$currency,1) : '<font style="color:#CCCCCC;">- ' . $currencies->format($html_od_amount,true,$currency,1).'</font>'),
                                                'value' => $od_amount);
                    }
                } else {
                    $this->output[] = array('title' => '',
                                            'display_title' => $display_html,
                                            'simple_display_title' => TEXT_INFO_STORE_CREDIT_DEDUCTION,
                                            'text' => '',
                                            'display_text' => '<font style="color:#CCCCCC;">- ' . $currencies->format(0,true,$currency,1).'</font>',
                                            'value' => 0);
                }
        	} else {
    	   		$display_html = '<table border="0" cellspacing="0" cellpadding="0" width="100%">
    	   							<tr>
    	   								<td valign="top">
       										' . TEXT_INFO_STORE_CREDIT_DEDUCTION . '
       									</td>
       								</tr>
    	   						</table>';
	          	$this->output[] = array('title' => '',
		      							'display_title' => $display_html,
   			  							'text' => '',
										'display_text' => '<font style="color:#CCCCCC;">- ' . $currencies->format(0,true,$currency,1).'</font>',
										'value' => 0);
        	}
      	}
    }

    function selection_test()
    {
        $ot_cid = $this->get_customer_id();

      	if ($this->user_has_gv_account($ot_cid)) {
        	return true;
      	} else {
        	return false;
      	}
    }

	function pre_confirmation_check($order_total) {
		global $cot_gv, $order, $currencies, $currency;

		$od_amount = 0; // set the default amount we will send back
		if ($_SESSION['cot_gv'] && $order_total > 0) {
			// pre confirmation check doesn't do a true order process. It just attempts to see if
			// there is enough to handle the order. But depending on settings it will not be shown
			// all of the order so this is why we do this runaround jane. What do we know so far.
			// nothing. Since we need to know if we process the full amount we need to call get order total
			// if there has been something before us then
            $usd_order_total = $order_total;

            $ot_cid = $this->get_customer_id();

	      	if ($this->include_tax == 'false') {
				$order_total = $order_total - $order->info['tax'];
			}

  	    	if ($this->include_shipping == 'false') {
				$order_total = $order_total - $order->info['shipping_cost'];
			}

            $order_total = round($currencies->advance_currency_conversion($order_total, DEFAULT_CURRENCY, $currency, true, 'buy'), 6);
			$od_amount = $this->calculate_credit($order_total);
            $conv_total = $order_total - $od_amount;
			$od_amount = round($currencies->advance_currency_conversion($od_amount, $currency, DEFAULT_CURRENCY, false, 'sell'), 6);

            if (($conv_total == 0) && ($od_amount != $usd_order_total)) {
                $od_amount = $usd_order_total;
            }

			if ($this->calculate_tax != "None") {
				$tod_amount = $this->calculate_tax_deduction($order_total, $od_amount, $this->calculate_tax);
				$od_amount = $this->calculate_credit($order_total)+$tod_amount;
			}

            $is_customer_require_qna = validate_customer_domant_account($ot_cid);
            if ($is_customer_require_qna === true && $this->user_has_gv_account($ot_cid)) {
                $_SESSION['need_sc_usage_qna'] = 'true';
                $od_amount = 0;
            }
		} else {
            $this->calculate_credit($order_total);
        }

		return $od_amount;
	}

    function use_credit_amount()
    {
        global $currencies, $currency;

        $output_string = '';
        $_SESSION['cot_gv'] = false;
        $ot_cid = $this->get_customer_id();

      	if ($this->selection_test()) {
        	$output_string = sprintf($this->checkbox, $currencies->format($this->user_gv_amount($ot_cid), false, $currency)) . "\n";
      	}
      	return $output_string;
    }

    function update_credit_account($i) {
      	global $order, $insert_id, $REMOTE_ADDR, $currencies;

        $ot_cid = $this->get_customer_id();

      	if (ereg_dep('^GIFT', addslashes($order->products[$i]['model']))) {
        	$gv_order_amount = ($order->products[$i]['final_price'] * $order->products[$i]['qty']);
        	if ($this->credit_tax=='true') $gv_order_amount = $gv_order_amount * (100 + $order->products[$i]['tax']) / 100;

        	$gv_order_amount = $gv_order_amount * 100 / 100;
        	if (MODULE_ORDER_TOTAL_GV_QUEUE == 'false') {
          		// GV_QUEUE is true so release amount to account immediately
          		$gv_query=tep_db_query("select amount, sc_currency_id from " . TABLE_COUPON_GV_CUSTOMER . " where customer_id = '" . $ot_cid . "'");
          		$customer_gv = false;
          		$total_gv_amount = 0;
          		if ($gv_result = tep_db_fetch_array($gv_query)) {
            		$total_gv_amount = $gv_result['amount'];
            		$sc_currency_id = $gv_result['sc_currency_id'];
            		$customer_gv = true;

	          		if ($currencies->get_code_by_id($sc_currency_id) != DEFAULT_CURRENCY) {
          				$gv_order_amount = store_credit::get_store_credit_conversion($gv_order_amount, DEFAULT_CURRENCY, $currencies->get_code_by_id($sc_currency_id));
	          		}
          		}
      			$total_gv_amount = $total_gv_amount + $gv_order_amount;
          		if ($customer_gv) {
            		$gv_update=tep_db_query("update " . TABLE_COUPON_GV_CUSTOMER . " set amount = '" . $total_gv_amount . "' where customer_id = '" . $ot_cid . "'");
          		} else {
            		$gv_insert=tep_db_query("insert into " . TABLE_COUPON_GV_CUSTOMER . " (customer_id, amount) values ('" . $ot_cid . "', '" . $total_gv_amount . "')");
          		}
        	} else {
         		// GV_QUEUE is true - so queue the gv for release by store owner
          		$gv_insert=tep_db_query("insert into " . TABLE_COUPON_GV_QUEUE . " (customer_id, order_id, amount, date_created, ipaddr) values ('" . $ot_cid . "', '" . $insert_id . "', '" . $gv_order_amount . "', NOW(), '" . $REMOTE_ADDR . "')");
        	}
      	}
    }

    function credit_selection()
    {
      	global $currencies, $language;

      	$selection_string = '';

      	$gv_query = tep_db_query("select coupon_id from " . TABLE_COUPONS . " where coupon_type = 'G' and coupon_active='Y'");
      	if (0&&tep_db_num_rows($gv_query)) { // remove after 2011/06/01
        	$selection_string .= '<tr>' . "\n";
        	$selection_string .= '  <td width="10">' .  tep_draw_separator('pixel_trans.gif', '10', '1') .'</td>';
        	$selection_string .= '  <td class="creditRecords">' . "\n";
        	$image_submit = tep_image_submit(THEMA.'button_redeem.gif', IMAGE_BUTTON_REDEEM_VOUCHER, 'name="submit_redeem" onClick="submitFunction()"');
        	//$image_submit = '<input type="image" name="submit_redeem" onClick="submitFunction()" src="' . DIR_WS_LANGUAGES . $language . '/images/buttons/'.THEMA.'/button_redeem.gif" border="0" alt="' . IMAGE_BUTTON_REDEEM_VOUCHER . '" title = "' . IMAGE_BUTTON_REDEEM_VOUCHER . '">';
        	$selection_string .= TEXT_ENTER_GV_CODE . tep_draw_input_field('gv_redeem_code') . '&nbsp;&nbsp;' . $image_submit . '</td>';
        	//$selection_string .= '  <td align="right"' . $image_submit . '</td>';
        	$selection_string .= '  <td width="10">' . tep_draw_separator('pixel_trans.gif', '10', '1') . '</td>';
        	$selection_string .= '</tr>' . "\n";
      	}
    	return $selection_string;
    }

    function apply_credit($new_order_id)
    {
    	// Use up irreversible credit before using reversible credit
      	global $currencies;

        $ot_cid = $this->get_customer_id();
      	$sc_reverse_deduct = $sc_irreverse_deduct = 0;

		$customer_data = store_credit::get_customer_info($ot_cid);
		$customer_sc_currency = $currencies->get_code_by_id($customer_data['sc_currency_id']);

      	if ($_SESSION['cot_gv']) {	// Refer collect_posts() in order_total.php
      		$total_deduct_amount = (double)$this->deduction;
      		if ($total_deduct_amount > 0) {
	      		$sc_reverse_deduct = $sc_irreverse_deduct = 0;
	      		$update_sc_array = array();

	      		$sc_array = $this->user_gv_amount($ot_cid, false, $customer_sc_currency);
	      		if ($sc_array['sc_currency'] != DEFAULT_CURRENCY) {
	      			// WEI CHEN: Maybe need to use 'buy'
	      			$total_deduct_amount = store_credit::get_store_credit_conversion($this->deduction, DEFAULT_CURRENCY, $sc_array['sc_currency'], 'buy');
	      		}

	      		if ($sc_array['sc_irreverse'] > 0) {
		      		if ($total_deduct_amount > $sc_array['sc_irreverse']) {
		      			$sc_irreverse_deduct = $sc_array['sc_irreverse'];
		      			$total_deduct_amount -= $sc_array['sc_irreverse'];
		      		} else {
		      			$sc_irreverse_deduct = $total_deduct_amount;
		      			$total_deduct_amount = 0;
		      		}
	      		}

	      		if ($total_deduct_amount > 0 && $sc_array['sc_reverse'] > 0) {
		      		if ($total_deduct_amount > $sc_array['sc_reverse']) {
		      			$sc_reverse_deduct = $sc_array['sc_reverse'];
		      			$total_deduct_amount -= $sc_array['sc_reverse'];
		      		} else {
		      			$sc_reverse_deduct = $total_deduct_amount;
		      			$total_deduct_amount = 0;
		      		}
	      		}

	      		if ($sc_irreverse_deduct > 0) {
	      			$update_sc_array[] = array(	'field_name'=> 'sc_irreversible_amount',
												'operator'=> '-',
												'value'=> $sc_irreverse_deduct);
				}

				if ($sc_reverse_deduct > 0) {
	      			$update_sc_array[] = array(	'field_name'=> 'sc_reversible_amount',
												'operator'=> '-',
												'value'=> $sc_reverse_deduct);
				}

				$trans_info_array = array(	'type' => 'C',
											'id' => $new_order_id);
	      		$sc_module = new store_credit($ot_cid);
	      		$sc_module->set_store_credit_balance($update_sc_array, $trans_info_array, '', '');
	        }
      	}
      	return ($sc_irreverse_deduct + $sc_reverse_deduct);
    }

    function collect_posts()
    {
      	global $currencies, $HTTP_POST_VARS, $coupon_no, $REMOTE_ADDR;

     	if ($HTTP_POST_VARS['submit_redeem_x'] && $gv_result['coupon_type'] == 'G') tep_redirect(tep_href_link(FILENAME_CHECKOUT_PAYMENT, 'error_message=' . urlencode(ERROR_NO_REDEEM_CODE), 'SSL'));
   	}

    function calculate_credit($amount)
    {
      	global $order, $currencies, $currency;

        $ot_cid = $this->get_customer_id();
      	$gv_payment_amount = $this->user_gv_amount($ot_cid);

      	//$gv_amount = $gv_payment_amount;
      	$save_total_cost = $amount;
      	$full_cost = $save_total_cost - $gv_payment_amount;
      	if ($full_cost < 0) {
            $this->store_credit_remain_formatted = $currencies->format(round($full_cost*-1, $currencies->currencies[$currency]['decimal_places']), true, $currency, 1);
        	$full_cost = 0;
        	$gv_payment_amount = $save_total_cost;
      	} else {
            if ($this->selection_test()) {
                $this->store_credit_remain_formatted = $currencies->format(0, true, $currency, 1);
            } else {
                // customer does not have sc account
                $this->store_credit_remain_formatted = '-';
            }
        }

      	return round($gv_payment_amount, $currencies->currencies[$currency]['decimal_places']);
    }

    function calculate_tax_deduction($amount, $od_amount, $method)
    {
      	global $order;
      	switch ($method) {
        	case 'Standard':
        		$ratio1 = tep_round($od_amount / $amount,2);
        		$tod_amount = 0;
        		reset($order->info['tax_groups']);
        		while (list($key, $value) = each($order->info['tax_groups'])) {
          			$tax_rate = tep_get_tax_rate_from_desc($key);
          			$total_net += $tax_rate * $order->info['tax_groups'][$key];
        		}
        		if ($od_amount > $total_net) $od_amount = $total_net;
        		reset($order->info['tax_groups']);
        		while (list($key, $value) = each($order->info['tax_groups'])) {
          			$tax_rate = tep_get_tax_rate_from_desc($key);
          			$net = $tax_rate * $order->info['tax_groups'][$key];
          			if ($net > 0) {
            			$god_amount = $order->info['tax_groups'][$key] * $ratio1;
            			$tod_amount += $god_amount;
            			$order->info['tax_groups'][$key] = $order->info['tax_groups'][$key] - $god_amount;
          			}
        		}
        		$order->info['tax'] -= $tod_amount;
        		$order->info['total'] -= $tod_amount;

        		break;
        	case 'Credit Note':
          		$tax_rate = tep_get_tax_rate($this->tax_class, $order->delivery['country']['id'], $order->delivery['zone_id']);
          		$tax_desc = tep_get_tax_description($this->tax_class, $order->delivery['country']['id'], $order->delivery['zone_id']);
          		$tod_amount = $this->deduction / (100 + $tax_rate)* $tax_rate;
          		$order->info['tax_groups'][$tax_desc] -= $tod_amount;
				//$order->info['total'] -= $tod_amount;

        		break;
        	default:
      	}

      	return $tod_amount;
    }

    function user_has_gv_account($c_id)
    {
      	$gv_query = tep_db_query("select (sc_reversible_amount+sc_irreversible_amount) as amount from " . TABLE_COUPON_GV_CUSTOMER . " where customer_id = '" . $c_id . "'");
      	if ($gv_result = tep_db_fetch_array($gv_query)) {
        	if ($gv_result['amount']>0) {
          		return true;
        	}
      	}
      	return false;
    }

	// use this function to check same store credit currency before allow to use for purchase
	function user_gv_amount($c_id, $return_total=true, $currency_type='')
    {
    	global $currencies, $currency;

    	if (!$currency_type) $currency_type = $currency;
    	$customer_sc_array = array('sc_reverse'=>0,'sc_irreverse'=>0,'sc_reverse_reserve_amt'=>0,'sc_irreverse_reserve_amt'=>0);

    	$sc_obj = new store_credit($c_id);
    	$customer_sc_currency = (isset($sc_obj->credit_accounts['sc_currency_id'])) ? $currencies->get_code_by_id($sc_obj->credit_accounts['sc_currency_id']) : '';
    	if ($customer_sc_currency == $currency_type) {
    		$customer_sc_array = $sc_obj->get_store_credits_in_new_currency($currency_type);
    	}

    	if ($return_total)
      		return ((double)$customer_sc_array['sc_reverse'] + (double)$customer_sc_array['sc_irreverse']) - ((double)$customer_sc_array['sc_reverse_reserve_amt'] + (double)$customer_sc_array['sc_irreverse_reserve_amt']);
      	else
      		return array('sc_reverse'=>(double)$customer_sc_array['sc_reverse']-(double)$customer_sc_array['sc_reverse_reserve_amt'], 'sc_irreverse'=>(double)$customer_sc_array['sc_irreverse']-(double)$customer_sc_array['sc_irreverse_reserve_amt'], 'sc_currency' => $currency_type);
    }

    function get_order_total()
    {
      	global $order, $currencies, $currency;
      	$order_total = 0;
      	$original_total = 0;
      	$this->store_credit_amount = 0;
      	// find the order total amount from shopping cart that product is not a store credit
      	foreach ($order->products as $idx => $product_array) {
      		if (isset($product_array['storage_price'])) {
      			$final_price = $product_array['storage_price']['final_price'];
      		} else {
      			$final_price = $product_array['final_price'];
      		}
      		$original_total = $original_total + ($final_price * $product_array['qty']);
      		if ($product_array['custom_products_type_id'] != '3') {
   				$order_total = $order_total + ($final_price * $product_array['qty']);
      		}
      	}
      	$this->store_credit_amount = $original_total - $order_total;

      	// check if discount coupon is in use. Deduct the discount amount out.
      	if ($_SESSION['cc_id'] && isset($GLOBALS['ot_coupon']->deduction)) {
      		$order_total = $order_total - $GLOBALS['ot_coupon']->deduction;
      	}
        
        if (is_object($GLOBALS['ot_ogc']) && $GLOBALS['ot_ogc']->deduction > 0) {
            $order_total = bcsub($order_total, $GLOBALS['ot_ogc']->deduction, 8);
        }

      	//$order_total = $order->info['total'];
      	if ($this->include_tax == 'false') $order_total = $order_total - $order->info['tax'];
      	if ($this->include_shipping == 'false') $order_total = $order_total - $order->info['shipping_cost'];

		if ($order_total == 0 && $original_total > 0) {
			$this->store_credit_only = true;
		}

        return $order_total;
    }

    function check()
    {
      	if (!isset($this->check)) {
        	$check_query = tep_db_query("select configuration_value from " . TABLE_CONFIGURATION . " where configuration_key = 'MODULE_ORDER_TOTAL_GV_STATUS'");
        	$this->check = tep_db_num_rows($check_query);
      	}

      	return $this->check;
    }

    function keys()
    {
      	return array('MODULE_ORDER_TOTAL_GV_STATUS', 'MODULE_ORDER_TOTAL_GV_SORT_ORDER', 'MODULE_ORDER_TOTAL_GV_QUEUE', 'MODULE_ORDER_TOTAL_GV_INC_SHIPPING', 'MODULE_ORDER_TOTAL_GV_INC_TAX', 'MODULE_ORDER_TOTAL_GV_CALC_TAX', 'MODULE_ORDER_TOTAL_GV_TAX_CLASS', 'MODULE_ORDER_TOTAL_GV_CREDIT_TAX');
    }

    function install()
    {
      	tep_db_query("insert into " . TABLE_CONFIGURATION . " (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, set_function, date_added) values ('Display Total', 'MODULE_ORDER_TOTAL_GV_STATUS', 'true', 'Do you want to display the Gift Voucher value?', '6', '1','tep_cfg_select_option(array(\'true\', \'false\'), ', now())");
      	tep_db_query("insert into " . TABLE_CONFIGURATION . " (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, date_added) values ('Sort Order', 'MODULE_ORDER_TOTAL_GV_SORT_ORDER', '740', 'Sort order of display.', '6', '2', now())");
      	tep_db_query("insert into " . TABLE_CONFIGURATION . " (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, set_function, date_added) values ('Queue Purchases', 'MODULE_ORDER_TOTAL_GV_QUEUE', 'true', 'Do you want to queue purchases of the Gift Voucher?', '6', '3','tep_cfg_select_option(array(\'true\', \'false\'), ', now())");
      	tep_db_query("insert into " . TABLE_CONFIGURATION . " (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, set_function ,date_added) values ('Include Shipping', 'MODULE_ORDER_TOTAL_GV_INC_SHIPPING', 'true', 'Include Shipping in calculation', '6', '5', 'tep_cfg_select_option(array(\'true\', \'false\'), ', now())");
      	tep_db_query("insert into " . TABLE_CONFIGURATION . " (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, set_function ,date_added) values ('Include Tax', 'MODULE_ORDER_TOTAL_GV_INC_TAX', 'true', 'Include Tax in calculation.', '6', '6','tep_cfg_select_option(array(\'true\', \'false\'), ', now())");
      	tep_db_query("insert into " . TABLE_CONFIGURATION . " (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, set_function ,date_added) values ('Re-calculate Tax', 'MODULE_ORDER_TOTAL_GV_CALC_TAX', 'None', 'Re-Calculate Tax', '6', '7','tep_cfg_select_option(array(\'None\', \'Standard\', \'Credit Note\'), ', now())");
      	tep_db_query("insert into " . TABLE_CONFIGURATION . " (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, use_function, set_function, date_added) values ('Tax Class', 'MODULE_ORDER_TOTAL_GV_TAX_CLASS', '0', 'Use the following tax class when treating Gift Voucher as Credit Note.', '6', '0', 'tep_get_tax_class_title', 'tep_cfg_pull_down_tax_classes(', now())");
      	tep_db_query("insert into " . TABLE_CONFIGURATION . " (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, set_function ,date_added) values ('Credit including Tax', 'MODULE_ORDER_TOTAL_GV_CREDIT_TAX', 'false', 'Add tax to purchased Gift Voucher when crediting to Account', '6', '8','tep_cfg_select_option(array(\'true\', \'false\'), ', now())");
    }

    function remove()
    {
      	tep_db_query("delete from " . TABLE_CONFIGURATION . " where configuration_key in ('" . implode("', '", $this->keys()) . "')");
    }

    function get_customer_id () {
        global $customer_id;

        $return_int = 0;

        if (isset($_SESSION['customer_id']) && $_SESSION['customer_id']) {
            $return_int = $_SESSION['customer_id'];
        } else {
            $return_int = $customer_id;
        }

        return $return_int;
    }
}
?>