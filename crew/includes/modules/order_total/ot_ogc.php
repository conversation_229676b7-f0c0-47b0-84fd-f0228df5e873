<?php
/*
  $Id: ot_ogc.php,v 1.3 2014/03/06 09:37:25 weesiong Exp $

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2002 osCommerce

  Released under the GNU General Public License
*/

require_once(DIR_WS_CLASSES . 'gift_card.php');

class ot_ogc {
	var $title, $output;
    private $balance_order_total;
    private $gc_obj;
    public $deduction;
    
    function ot_ogc() {
        global $gift_card_obj;
      	$this->code = 'ot_ogc';
      	$this->display_title = MODULE_ORDER_TOTAL_OGC_TITLE;
      	$this->title = MODULE_ORDER_TOTAL_OGC_TITLE;
      	$this->description = MODULE_ORDER_TOTAL_OGC_DESCRIPTION;
        $this->enabled = MODULE_ORDER_TOTAL_OGC_STATUS;
        $this->sort_order = MODULE_ORDER_TOTAL_OGC_SORT_ORDER;
      	$this->credit_class = true;
      	$this->output = array();
      	$this->store_credit_only = false;
        $this->pincode_serialno = '';
        $this->balance_order_total = 0;
        $this->deduction = 0;
        
        if (is_object($gift_card_obj)) {
            $this->gc_obj = $gift_card_obj;
        } else {
            $this->gc_obj = new gift_card();
            $this->gc_obj->restore_pincode_by_session();
        }
    }
    
  	function process() {
        global $order, $currencies, $currency;
        
        $ot_cid = $this->get_customer_id();
        
        // this deduction will be used to store total gift card value in based currency and used deduct the total order amount in ot_gv file.
        $this->deduction = 0;
        
        if ($this->gc_obj->is_pincode_exist() && round($order->info['total'], $currencies->currencies[$currency]['decimal_places']) > 0) {
            $this->balance_order_total = $order->info['total'];
            $this->gc_obj->redeem_all_gift_cards(0, $currency, false);  // using regional currency setting & redeem without update user store credit
            
            $order_total = $currencies->advance_currency_conversion($order->info['total'], DEFAULT_CURRENCY, $currency, true, 'buy');
            $html_od_amount = $this->calculate_credit($order_total);
            $od_amount = round($currencies->advance_currency_conversion($html_od_amount, $currency, DEFAULT_CURRENCY, false, 'sell'), 8); // not to use the function round the figure, it will round up by using the currency's decimal points setting. We need it in 4 decimal points.
            
            $this->deduction = $od_amount;
            $order->info['total'] = $order->info['total'] - $od_amount;
            
      		if (round($order->info['total'], $currencies->currencies[$currency]['decimal_places']) <= 0) {
                $order->info['total'] = 0;
            } else {
//                $this->balance_order_total = 0;
            }
            
            if ($od_amount > 0) {
                $this->output[] = array('title' => $this->title . ': ' . $this->pincode_serialno,
                                        'display_title' => $this->display_title,
                                        'text' => '<b>' . $currencies->format($html_od_amount, true, $currency, 1) . '</b>',
                                        'value' => $od_amount);
            }
        }
    }
    
  	function pre_confirmation_check($order_total2) {
    	global $cot_gv, $order, $currencies, $currency;
        
        $order_total = $this->get_order_total();
        
		$od_amount = 0; // set the default amount we will send back
		if ($this->gc_obj->is_pincode_exist() && $order_total > 0) {
            if ($this->gc_obj->validate_all_gift_cards(0, $currency)) { // force using currency instead of user store credit currency
                $order_total = round($currencies->advance_currency_conversion($order_total, DEFAULT_CURRENCY, $currency, false, 'buy'), 6);
                $od_amount = $this->calculate_credit($order_total);
                $od_amount = round($currencies->advance_currency_conversion($od_amount, $currency, DEFAULT_CURRENCY, false, 'sell'), 6);
            }
		}
        
		return $od_amount;
    }
    
	function credit_selection($amt_display='') {}
    
  	function collect_posts() {}
    
  	function calculate_credit($amount) {
      	global $currencies, $currency;
        
        $gc_payment_amount = 0;
        
        $total_amount_arr = $this->gc_obj->get_last_pin_code_info();
        if (count($total_amount_arr)) {
            $gc_payment_amount = $total_amount_arr['total_redeem_gc_deno'];
            $this->pincode_serialno = $total_amount_arr['serial_no_used'];
        }
        
      	$full_cost = $amount - $gc_payment_amount;
      	if ($full_cost < 0) {
        	$gc_payment_amount = $amount;
      	}
      	
      	return round($gc_payment_amount, $currencies->currencies[$currency]['decimal_places']);
    }
    
    function update_credit_account($i) {}
    
    function apply_credit($new_order_id) {
        global $currency, $currencies;
        
        $ot_cid = $this->get_customer_id();
        
        if ($this->balance_order_total > 0) {
            $this->gc_obj->add_balance_gift_card_value_into_store_credit($new_order_id, $this->balance_order_total, DEFAULT_CURRENCY, $currency, $ot_cid);
        }
        
        $this->gc_obj->capture_pincode_by_session();
    }
    
    function get_customer_id () {
        global $customer_id;
        
        $return_int = 0;
        
        if (isset($_SESSION['order_customer_id']) && $_SESSION['order_customer_id']) {
            $return_int = $_SESSION['order_customer_id'];
        } else if (isset($_SESSION['customer_id']) && $_SESSION['customer_id']) {
            $return_int = $_SESSION['customer_id'];
        } else {
            $return_int = $customer_id;
        }
        
        return $return_int;
    }
    
    function get_order_total()
    {
      	global $order, $currencies, $currency;
      	$order_total = 0;
      	$original_total = 0;
      	$this->store_credit_amount = 0;
      	// find the order total amount from shopping cart that product is not a store credit
      	foreach ($order->products as $idx => $product_array) {
      		if (isset($product_array['storage_price'])) {
      			$final_price = $product_array['storage_price']['final_price'];
      		} else {
      			$final_price = $product_array['final_price'];
      		}
      		$original_total = $original_total + ($final_price * $product_array['qty']);
      		if ($product_array['custom_products_type_id'] != '3') {
   				$order_total = $order_total + ($final_price * $product_array['qty']);
      		}
      	}
      	$original_total = round($original_total, 6);
      	$order_total = round($order_total, 6);
      	$this->store_credit_amount = $original_total - $order_total;
      	
      	// check if discount coupon is in use. Deduct the discount amount out.
      	if ($_SESSION['cc_id'] && isset($GLOBALS['ot_coupon']->deduction)) {
      		$order_total = $order_total - $GLOBALS['ot_coupon']->deduction;
      	}
      	
      	//$order_total = $order->info['total'];
//      	if ($this->include_tax == 'false') $order_total = $order_total - $order->info['tax'];
//      	if ($this->include_shipping == 'false') $order_total = $order_total - $order->info['shipping_cost'];
      	
      	// since coupon can not be used to purchase store credit product, after calculated the order total,
      	// if order total amount (excluding store credit) is greater than order object total amount,
      	// this means, there are coupon used in the purchase, take the order object total amount as order total
      	if ($order_total > round($order->info['total'], 6)) $order_total = round($order->info['total'], 6);
		
		if ($order_total == 0 && $original_total > 0) {
			$this->store_credit_only = true;
		}
		
      	return round($order_total, 6);
    }
    
    function check()
    {
      	if (!isset($this->check)) {
        	$check_query = tep_db_query("select configuration_value from " . TABLE_CONFIGURATION . " where configuration_key = 'MODULE_ORDER_TOTAL_OGC_STATUS'");
        	$this->check = tep_db_num_rows($check_query);
      	}

      	return $this->check;
    }

    function keys()
    {
      	return array('MODULE_ORDER_TOTAL_OGC_STATUS', 'MODULE_ORDER_TOTAL_OGC_SORT_ORDER');
    }

    function install()
    {
      	tep_db_query("insert into " . TABLE_CONFIGURATION . " (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, set_function, date_added) values ('Display Total', 'MODULE_ORDER_TOTAL_OGC_STATUS', 'true', 'Do you want to display the Gift Card value?', '6', '1','tep_cfg_select_option(array(\'true\', \'false\'), ', now())");
      	tep_db_query("insert into " . TABLE_CONFIGURATION . " (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, date_added) values ('Sort Order', 'MODULE_ORDER_TOTAL_OGC_SORT_ORDER', '700', 'Sort order of display.', '6', '2', now())");
    }

    function remove()
    {
      	tep_db_query("delete from " . TABLE_CONFIGURATION . " where configuration_key in ('" . implode("', '", $this->keys()) . "')");
    }
}
?>