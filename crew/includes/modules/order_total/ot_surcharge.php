<?php
/*
  	$Id: ot_surcharge.php,v 1.17 2012/09/07 11:25:21 weesiong Exp $

  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com

  	Copyright (c) 2002 osCommerce

  	Released under the GNU General Public License
*/

class ot_surcharge {
	var $title, $output;
	var $selected_payment_id = 0;
	var $selected_gateway_code = '';
	var $selected_payment_info = '';
	var $selected_to_currency = '';
	var $selected_same_currency = false;
	var $round_decimal_point = 4;
	var $operators = array(
						'1' => '<' ,
						'2' => '<=',
						'3' => '>' ,
						'4' => '>='
						);
    var $fees_type = 'instant_purchase';
    
	function ot_surcharge($pass_currency='')
	{
		$this->code = 'ot_surcharge';
		$this->title = MODULE_ORDER_TOTAL_SURCHARGE_TITLE;
        $this->display_title = MODULE_ORDER_TOTAL_SURCHARGE_DISPLAY_TITLE;
		$this->description = MODULE_ORDER_TOTAL_SURCHARGE_DESCRIPTION;
		$this->enabled = ((MODULE_ORDER_TOTAL_SURCHARGE_STATUS == 'true') ? true : false);
		$this->sort_order = MODULE_ORDER_TOTAL_SURCHARGE_SORT_ORDER;

		$this->output = array();
		$this->selected_payment_id = 0;
		$this->selected_gateway_code = '';
		$this->selected_payment_info = '';
		$this->selected_to_currency = ( tep_not_null($pass_currency) ? $pass_currency : ( $_SESSION['currency'] ? $_SESSION['currency'] : DEFAULT_CURRENCY ) );
		$this->selected_same_currency = false;
		$this->round_decimal_point = 8;
	}

	function process()
	{
		global $order, $currencies, $payment_modules;

		// check is the order has been fully covered by store credit and discount coupon usage		
		if (is_object($payment_modules) && $payment_modules->check_credit_covers()) {
		} else {
			$this->collect_posts();
			
			// get surcharge in non-USD for display
			$surcharge_amount = round($this->calculate_surcharge(false, $surcharge_currency), 2);
			
			if ($_SESSION['currency'] == $surcharge_currency) {
				$display_surcharge_amount = number_format($surcharge_amount, $currencies->currencies[$_SESSION['currency']]['decimal_places'], $currencies->currencies[$_SESSION['currency']]['decimal_point'], '');
				//$ot_surcharge_amt = $currencies->advance_currency_conversion($surcharge_amount, $this->selected_to_currency, DEFAULT_CURRENCY, false, 'buy');
				//$ot_surcharge_amt = $this->apply_currency_exchange($surcharge_amount, $this->selected_to_currency, $this->selected_to_currency, 'sell');
				$ot_surcharge_amt = $currencies->advance_currency_conversion($surcharge_amount, $surcharge_currency, $_SESSION['currency'], false, 'buy');
				$ot_surcharge_amt = round($currencies->advance_currency_conversion($ot_surcharge_amt, $_SESSION['currency'], DEFAULT_CURRENCY, false, 'sell'), 4);
			} else {
				$ot_surcharge_amt = $currencies->advance_currency_conversion($surcharge_amount, $surcharge_currency, $this->selected_to_currency, false, 'buy');
				$ot_surcharge_amt = round($currencies->advance_currency_conversion($ot_surcharge_amt, $this->selected_to_currency, DEFAULT_CURRENCY, false, 'sell'), 4);
				//$display_surcharge_amount = $currencies->advance_currency_conversion($surcharge_amount, DEFAULT_CURRENCY, $this->selected_to_currency, false, 'buy');
				//$display_surcharge_amount = $this->apply_currency_exchange($surcharge_amount, $this->selected_to_currency, DEFAULT_CURRENCY, 'sell');
				$display_surcharge_amount = $currencies->advance_currency_conversion($surcharge_amount, $surcharge_currency, $this->selected_to_currency, false, 'buy');
				$display_surcharge_amount = number_format($display_surcharge_amount, $currencies->currencies[$_SESSION['currency']]['decimal_places'], $currencies->currencies[$_SESSION['currency']]['decimal_point'], '');
			}
			
			$order->info['total'] = (double)trim($order->info['total']) + (double)trim($ot_surcharge_amt);
			
			if ($surcharge_amount > 0) {
				$this->output[] = array('title' => $this->title . ':',
										'display_title' => $this->display_title . ':',
										'text' => '<b>' . $currencies->format($display_surcharge_amount, true, $this->selected_to_currency, 1) . '</b>',
										'display_text' => '+ ' . $currencies->format($display_surcharge_amount, true, $this->selected_to_currency, 1),
										'value' => $ot_surcharge_amt);
			}
		}
	}
	
	function sc_process() {
		global $order, $currencies, $payment_modules;
		
        $this->fees_type = 'sc_topup';  // SC Top Up has own Surcharge setting
        
		// check is the order has been fully covered by store credit and discount coupon usage		
		if (is_object($payment_modules) && $payment_modules->check_credit_covers()) {
		} else {
			$this->collect_posts();
			
			$customers_ordered_sc = 0;
			$surcharge_currency = $customers_sc_currency = tep_get_customer_store_credit_currency($_SESSION['customer_id'], false);	// SC Product currency always follow Customer SC Balance Currency
			$customers_sc_select_sql = "SELECT cscq.customers_sc_cart_quantity, p.custom_products_type_id, p.products_price 
										FROM " . TABLE_CUSTOMERS_SC_CART . " AS cscq 
										INNER JOIN " . TABLE_PRODUCTS . " AS p 
											ON p.products_id = cscq.products_id 
										WHERE cscq.customers_id = '" . (int)$_SESSION['customer_id'] . "'";
			$customers_sc_result_sql = tep_db_query($customers_sc_select_sql);
			if ($customers_sc_row = tep_db_fetch_array($customers_sc_result_sql)) {
				if ($customers_sc_row['custom_products_type_id'] == 3) {
					if ($customers_sc_currency == $_SESSION['currency']) {
						$customers_ordered_sc = $customers_sc_row['customers_sc_cart_quantity'];
					} else {
						$customers_ordered_sc = $currencies->advance_currency_conversion($customers_sc_row['customers_sc_cart_quantity'], $customers_sc_currency, $_SESSION['currency']);
					}
				} else {
					$customers_ordered_sc = $customers_sc_row['products_price'];
				}
			}
			$customers_ordered_sc = $customers_ordered_sc + $_SESSION['sc_gst'];
			
			// get surcharge in non-USD for display
			$surcharge_currency = $_SESSION['currency'];
			$surcharge_amount = round($this->calculate_surcharge(false, $surcharge_currency, $customers_ordered_sc), 2);
			
			// get surcharge in non-USD for display
			if ($_SESSION['currency'] == $surcharge_currency) {
				$display_surcharge_amount = number_format($surcharge_amount, $currencies->currencies[$_SESSION['currency']]['decimal_places'], $currencies->currencies[$_SESSION['currency']]['decimal_point'], '');
				$ot_surcharge_amt = $currencies->advance_currency_conversion($surcharge_amount, $surcharge_currency, $_SESSION['currency'], false, 'buy');
				$ot_surcharge_amt = round($currencies->advance_currency_conversion($ot_surcharge_amt, $_SESSION['currency'], DEFAULT_CURRENCY, false, 'sell'), 4);
			} else {
				$ot_surcharge_amt = $currencies->advance_currency_conversion($surcharge_amount, $surcharge_currency, $this->selected_to_currency, false, 'buy');
				$ot_surcharge_amt = round($currencies->advance_currency_conversion($ot_surcharge_amt, $this->selected_to_currency, DEFAULT_CURRENCY, false, 'sell'), 4);
				$display_surcharge_amount = $currencies->advance_currency_conversion($surcharge_amount, $surcharge_currency, $this->selected_to_currency, false, 'buy');
				$display_surcharge_amount = number_format($display_surcharge_amount, $currencies->currencies[$_SESSION['currency']]['decimal_places'], $currencies->currencies[$_SESSION['currency']]['decimal_point'], '');
			}
			
			$order->info['total'] = (double)trim($order->info['total']) + (double)trim($ot_surcharge_amt);
			
			if ($surcharge_amount > 0) {
				$this->output[] = array('title' => $this->title . ':',
										'display_title' => $this->display_title . ':',
										'text' => '<b>' . $currencies->format($display_surcharge_amount, true, $this->selected_to_currency, 1) . '</b>',
										'display_text' => '+ ' . $currencies->format($display_surcharge_amount, true, $this->selected_to_currency, 1),
										'value' => $ot_surcharge_amt);
			}
		}
	}
	
	function pre_confirmation_check() {
		global $cot_surcharge, $order, $currencies, $payment_modules;
		
		if (is_object($payment_modules) && $payment_modules->check_credit_covers()) {
			return 0;
		} else {
			$this->collect_posts();
			
			$surcharge_amount = round($this->calculate_surcharge(false, $surcharge_currency), 2);
			
			if ($_SESSION['currency'] == $surcharge_currency) {
				$surcharge_amount = number_format($surcharge_amount, $currencies->currencies[$_SESSION['currency']]['decimal_places'], $currencies->currencies[$_SESSION['currency']]['decimal_point'], '');
			} else {
				//$surcharge_amount = $currencies->advance_currency_conversion($surcharge_amount, DEFAULT_CURRENCY, $this->selected_to_currency, false, 'sell');
				//$surcharge_amount = $this->apply_currency_exchange($surcharge_amount, $this->selected_to_currency, DEFAULT_CURRENCY, 'sell');
				$surcharge_amount = round($currencies->advance_currency_conversion($surcharge_amount, $surcharge_currency, $_SESSION['currency'], false, 'buy'), 4);
				$surcharge_amount = number_format($surcharge_amount, $currencies->currencies[$_SESSION['currency']]['decimal_places'], $currencies->currencies[$_SESSION['currency']]['decimal_point'], '');
			}
			return $surcharge_amount;
		}
	}

	function collect_posts()
	{
		global $currencies, $HTTP_POST_VARS;
      	
		if (isset($_SESSION['selected_payment_info'])) {
			$this->selected_payment_info = $_SESSION['selected_payment_info'];
		} else if (isset($_SESSION['payment'])) {
			$this->selected_payment_info = $_SESSION['payment'];
		}
	}
	
	function unset_payment_info() {
	}
	
	function calculate_surcharge_by($payment_info='', $to_currency=DEFAULT_CURRENCY, $round_decimal=4, $pass_amount='') {
		global $order, $currencies;
		
		$this->selected_payment_info = $payment_info;
		$this->selected_to_currency = $surcharge_currency = $to_currency;
		$this->round_decimal_point = $round_decimal;
		
		$surcharge_amount = round($this->calculate_surcharge(false, $surcharge_currency, $pass_amount), 2);
		
		if ($this->selected_to_currency == $surcharge_currency) {
			$surcharge_amount = number_format($surcharge_amount, $currencies->currencies[$this->selected_to_currency]['decimal_places'], $currencies->currencies[$this->selected_to_currency]['decimal_point'], '');
		} else {
			//$surcharge_amount = $currencies->advance_currency_conversion($surcharge_amount, DEFAULT_CURRENCY, $this->selected_to_currency, false, 'buy');
			//$surcharge_amount = $this->apply_currency_exchange($surcharge_amount, $this->selected_to_currency, DEFAULT_CURRENCY, 'sell');
			$surcharge_amount = round($currencies->advance_currency_conversion($surcharge_amount, $surcharge_currency, $this->selected_to_currency, false, 'buy'), 4);
			$surcharge_amount = number_format($surcharge_amount, $currencies->currencies[$this->selected_to_currency]['decimal_places'], $currencies->currencies[$this->selected_to_currency]['decimal_point'], '');
		}
		
    	return $surcharge_amount;
	}
	
	function calculate_surcharge($usd_value = true, &$surcharge_currency, $pass_amount='')
	{
		global $order, $currencies, $currency, $payment_info, $credit_covers;
      	
      	if (!tep_not_null($pass_amount) && tep_session_is_registered('credit_covers') && $credit_covers) { return 0; }
		
		if (tep_not_null($this->selected_payment_info)) {
			$payment_info_array = explode(':~:', $this->selected_payment_info);
		} else {
			$this->selected_payment_info = $payment_info;
			$payment_info_array = explode(':~:', $payment_info);
		}
		if (sizeof($payment_info_array) > 1) {
			$this->selected_gateway_code = $payment_info_array[1];
			$this->selected_payment_id = $payment_info_array[2];
		} else {
			$this->selected_gateway_code = $payment_info;
			$this->selected_payment_id = 0;
		}
		
		if ($this->selected_payment_id === 0) {
			$payment_methods_select_sql = "	SELECT payment_methods_id
											FROM " . TABLE_PAYMENT_METHODS . "
											WHERE payment_methods_receive_status = '1'
												AND payment_methods_parent_id != '0'
												AND payment_methods_code = '".tep_db_input($this->selected_gateway_code)."'";
			$payment_methods_result_sql = tep_db_query($payment_methods_select_sql);
			
			while ($payment_methods_row = tep_db_fetch_array($payment_methods_result_sql)) {
				$this->selected_payment_id = $payment_methods_row['payment_methods_id'];
			}
		}
		
		if (tep_not_null($pass_amount)) {
			$order_total = $pass_amount;
		} else {
			$order_total = $this->get_order_total();
			$surcharge_currency = DEFAULT_CURRENCY;
		}
		$pm_surcharge_arrays = $this->get_surcharge_amount();
		
      	// if selected currency not the same as surcharge setting currency,
      	// choose buy rate if checkout is USD or spot rate for non-USD
		$currency_conversion_type = ($this->selected_to_currency == DEFAULT_CURRENCY) ? 'buy' : null ;
		
		$payment_fees = 0;
		$need_surcharge = false;
		$got_surcharge = false;
		$this->selected_same_currency = false;
		
		foreach ($pm_surcharge_arrays as $payment_fees_id => $pm_surcharge_array) {
			// if selected currency is same as surcharge setting currency,
			// convert the order_total amount to the selected currency value.
			if (!$usd_value && $this->selected_to_currency == $pm_surcharge_array['payment_methods_currency_code']) {
				if ($this->selected_to_currency == $pm_surcharge_array['currency_code']) {
					$this->selected_same_currency = true;
				}
				
				if (!tep_not_null($pass_amount)) {
					$surcharge_currency = $pm_surcharge_array['currency_code'];
					$order_total = $currencies->advance_currency_conversion($order_total, DEFAULT_CURRENCY, $pm_surcharge_array['currency_code'], false, 'buy');
				}
			}
			
			if (!$got_surcharge) {
				if (tep_not_null($pm_surcharge_array['payment_fees_min']) && $pm_surcharge_array['payment_fees_min'] == 0) {
					continue;
				}
				
				if (tep_not_null($pm_surcharge_array['payment_fees_operator']) && $pm_surcharge_array['payment_fees_operator'] > 0) {
					$this_operator = $this->operators[$pm_surcharge_array['payment_fees_operator']];
				}
				
				if (tep_not_null($pm_surcharge_array['payment_fees_min']) && $pm_surcharge_array['payment_fees_min'] > 0) {
					$converted_payment_fees_min = $pm_surcharge_array['payment_fees_min'];
					if ($converted_payment_fees_min > 0) {
						$condition_check = eval("if ($order_total $this_operator $converted_payment_fees_min) { return true; } else { return false; }");
					} else {
						$condition_check = false;
					}
					if ($condition_check) {
						$need_surcharge = true;
					}
				}
				
				if ($need_surcharge) {
					// step 1 : get pre-set surcharge amount
					if (!$usd_value && $this->selected_same_currency) {
						$converted_payment_fees_cost_value = $pm_surcharge_array['payment_fees_cost_value'];
					} else {
						$converted_payment_fees_cost_value = $currencies->advance_currency_conversion($pm_surcharge_array['payment_fees_cost_value'], $pm_surcharge_array['currency_code'], $surcharge_currency, false, 'buy');
					}
					$payment_fees = $converted_payment_fees_cost_value;
					
					// step 2 : add calculated surcharge percentage amount
					if (tep_not_null($pm_surcharge_array['payment_fees_cost_percent']) && $pm_surcharge_array['payment_fees_cost_percent'] > 0) {
						$payment_fees = $payment_fees + (($order_total * (double)$pm_surcharge_array['payment_fees_cost_percent']) / 100);
					}
					
					// step 3 : calculated payment fees check against pre-set minimum fees
					if (tep_not_null($pm_surcharge_array['payment_fees_cost_percent_min']) && $pm_surcharge_array['payment_fees_cost_percent_min'] > 0) {
						/*if (!$usd_value && $this->selected_same_currency) {
							$converted_payment_fees_cost_percent_min = $pm_surcharge_array['payment_fees_cost_percent_min'];
						} else {
							//$converted_payment_fees_cost_percent_min = $this->apply_currency_exchange($pm_surcharge_array['payment_fees_cost_percent_min'], $pm_surcharge_array['currency_code'], $pm_surcharge_array['currency_code'], $currency_conversion_type);
							$converted_payment_fees_cost_percent_min = $currencies->advance_currency_conversion($pm_surcharge_array['payment_fees_cost_percent_min'], $pm_surcharge_array['currency_code'], DEFAULT_CURRENCY, false, 'buy');
						}*/
						$converted_payment_fees_cost_percent_min = $pm_surcharge_array['payment_fees_cost_percent_min'];
						if ($payment_fees <= $converted_payment_fees_cost_percent_min) {
							$payment_fees = $converted_payment_fees_cost_percent_min;
						}
					}
					
					// step 4 : calculated payment fees check against pre-set maximum fees
					if (tep_not_null($pm_surcharge_array['payment_fees_cost_percent_max']) && $pm_surcharge_array['payment_fees_cost_percent_max'] > 0) {
						/*if (!$usd_value && $this->selected_same_currency) {
							$converted_payment_fees_cost_percent_max = $pm_surcharge_array['payment_fees_cost_percent_max'];
						} else {
							//$converted_payment_fees_cost_percent_max = $this->apply_currency_exchange($pm_surcharge_array['payment_fees_cost_percent_max'], $pm_surcharge_array['currency_code'], $pm_surcharge_array['currency_code'], $currency_conversion_type);
							$converted_payment_fees_cost_percent_max = $currencies->advance_currency_conversion($pm_surcharge_array['payment_fees_cost_percent_max'], $pm_surcharge_array['currency_code'], DEFAULT_CURRENCY, false, 'buy');
						}*/
						$converted_payment_fees_cost_percent_max = $pm_surcharge_array['payment_fees_cost_percent_max'];
						if ($payment_fees >= $converted_payment_fees_cost_percent_max) {
							$payment_fees = $converted_payment_fees_cost_percent_max;
						}
					}
					
					$got_surcharge = true;
				}
			}
		}
		
		return tep_round($payment_fees, $this->round_decimal_point);
	}

	function get_surcharge_amount()
	{
		$payment_id = $this->selected_payment_id;
		$pm_surcharge_array = array();
		$cust_grp_id = tep_get_customers_groups_id();
    	$payment_fees_table_name = ($this->fees_type == 'sc_topup' ? TABLE_PAYMENT_FEES_SC : TABLE_PAYMENT_FEES);
        
    	// First check if selected payment methods is using parent's configuration settings
    	$payment_methods_select_sql = "SELECT payment_fees_id 
    									FROM " . $payment_fees_table_name . "
    									WHERE payment_methods_id = " . tep_db_input($payment_id) . "
    									AND payment_methods_currency_code = '" . $this->selected_to_currency . "'
    									AND payment_methods_mode = 'RECEIVE'
    									AND payment_fees_customers_groups_id = '" . tep_db_input($cust_grp_id) . "'";
    	$payment_methods_result_sql = tep_db_query($payment_methods_select_sql);
		if (tep_db_num_rows($payment_methods_result_sql) <= 0) {
			// using parent's configuration settings, get the parent's setting
			$parent_methods_select_sql = "SELECT payment_methods_id, payment_methods_parent_id
											FROM " . TABLE_PAYMENT_METHODS . "
											WHERE payment_methods_id = " . tep_db_input($payment_id) . "
											AND payment_methods_receive_status = 1";
			$parent_methods_result_sql = tep_db_query($parent_methods_select_sql);
			if ($parent_methods_result_row = tep_db_fetch_array($parent_methods_result_sql)) {
				if ($parent_methods_result_row['payment_methods_parent_id'] > 0) {
					// since using parent's configuration settings, re-assign the payment methods id
					$payment_id = $parent_methods_result_row['payment_methods_parent_id'];
				}
			}
		}
		
		// check if using another customers groups' settings
    	$pm_cgrp_select_sql = "	SELECT payment_fees_follow_group 
    							FROM " . $payment_fees_table_name . "
    							WHERE payment_methods_id = " . tep_db_input($payment_id) . "
    							AND payment_methods_currency_code = '" . $this->selected_to_currency . "'
    							AND payment_methods_mode = 'RECEIVE'
    							AND payment_fees_customers_groups_id = '" . tep_db_input($cust_grp_id) . "'";
    	$pm_cgrp_result_sql = tep_db_query($pm_cgrp_select_sql);
		if ($pm_cgrp_row = tep_db_fetch_array($pm_cgrp_result_sql)) {
			if ($pm_cgrp_row['payment_fees_follow_group'] > 0) {
				// current customer group setting is following another group's setting.
				$cust_grp_id = $pm_cgrp_row['payment_fees_follow_group'];
			}
		}
		
		// Now retrieve the correct payment methods fees settings
		$payment_fees_select_sql = "SELECT * 
									FROM " . $payment_fees_table_name . "
									WHERE payment_methods_id = " . tep_db_input($payment_id) . "
									AND payment_methods_currency_code = '" . $this->selected_to_currency . "'
									AND payment_methods_mode = 'RECEIVE'
									AND payment_fees_customers_groups_id = '" . tep_db_input($cust_grp_id) . "'
									ORDER BY payment_fees_customers_groups_id, payment_methods_currency_code";
		$payment_fees_result_sql = tep_db_query($payment_fees_select_sql);
		while ($payment_fees_result_row = tep_db_fetch_array($payment_fees_result_sql)) {
			$pm_surcharge_array[$payment_fees_result_row['payment_fees_id']] = array();
			$pm_surcharge_array[$payment_fees_result_row['payment_fees_id']]['payment_fees_id']                  = $payment_fees_result_row['payment_fees_id'];
			$pm_surcharge_array[$payment_fees_result_row['payment_fees_id']]['payment_methods_id']               = $payment_fees_result_row['payment_methods_id'];
			$pm_surcharge_array[$payment_fees_result_row['payment_fees_id']]['payment_methods_currency_code']    = $payment_fees_result_row['payment_methods_currency_code'];
			$pm_surcharge_array[$payment_fees_result_row['payment_fees_id']]['payment_methods_mode']             = $payment_fees_result_row['payment_methods_mode'];
			$pm_surcharge_array[$payment_fees_result_row['payment_fees_id']]['payment_fees_operator']            = $payment_fees_result_row['payment_fees_operator'];
			$pm_surcharge_array[$payment_fees_result_row['payment_fees_id']]['payment_fees_min']                 = $payment_fees_result_row['payment_fees_min'];
			$pm_surcharge_array[$payment_fees_result_row['payment_fees_id']]['payment_fees_cost_value']          = $payment_fees_result_row['payment_fees_cost_value'];
			$pm_surcharge_array[$payment_fees_result_row['payment_fees_id']]['payment_fees_cost_percent']        = $payment_fees_result_row['payment_fees_cost_percent'];
			$pm_surcharge_array[$payment_fees_result_row['payment_fees_id']]['payment_fees_cost_percent_min']    = $payment_fees_result_row['payment_fees_cost_percent_min'];
			$pm_surcharge_array[$payment_fees_result_row['payment_fees_id']]['payment_fees_cost_percent_max']    = $payment_fees_result_row['payment_fees_cost_percent_max'];
			$pm_surcharge_array[$payment_fees_result_row['payment_fees_id']]['payment_fees_customers_groups_id'] = $payment_fees_result_row['payment_fees_customers_groups_id'];
			$pm_surcharge_array[$payment_fees_result_row['payment_fees_id']]['payment_fees_follow_group']        = $payment_fees_result_row['payment_fees_follow_group'];
			$pm_surcharge_array[$payment_fees_result_row['payment_fees_id']]['currency_code']                    = $payment_fees_result_row['currency_code'];
		}
		
		return $pm_surcharge_array;
	}

	function get_order_total()
	{
		global $order, $currencies;
		
		if (isset($_SESSION['order_total_amt_wo_surcharge']) && tep_not_null($_SESSION['order_total_amt_wo_surcharge'])) {
			$order_total_amount = $_SESSION['order_total_amt_wo_surcharge'];
		} else {
			$order_total_amount = $order->info['total'];
		}
		
		return $order_total_amount;
	}

	function check()
	{
		if (!isset($this->check)) {
			$check_query = tep_db_query("select configuration_value from " . TABLE_CONFIGURATION . " where configuration_key = 'MODULE_ORDER_TOTAL_SURCHARGE_STATUS'");
			$this->check = tep_db_num_rows($check_query);
		}

		return $this->check;
	}

	function keys()
	{
		return array('MODULE_ORDER_TOTAL_SURCHARGE_STATUS', 'MODULE_ORDER_TOTAL_SURCHARGE_SORT_ORDER');
	}

	function install()
	{
		tep_db_query("insert into " . TABLE_CONFIGURATION . " (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, set_function, date_added) values ('Display Total', 'MODULE_ORDER_TOTAL_SURCHARGE_STATUS', 'true', 'Do you want to display the payment gateway surcharge value?', '6', '1','tep_cfg_select_option(array(\'true\', \'false\'), ', now())");
		tep_db_query("insert into " . TABLE_CONFIGURATION . " (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, date_added) values ('Sort Order', 'MODULE_ORDER_TOTAL_SURCHARGE_SORT_ORDER', '850', 'Sort order of display.', '6', '2', now())");
	}

	function remove()
	{
		tep_db_query("delete from " . TABLE_CONFIGURATION . " where configuration_key in ('" . implode("', '", $this->keys()) . "')");
	}
}
?>