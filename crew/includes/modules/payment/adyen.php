<?php

require_once('payment_gateway.php');

class adyen extends payment_gateway {

    var $code, $adyenCurrencies, $merchant_id, $secret_word, $http_user, $http_password, $ip_list;

    function adyen($pm_id = '') {
        global $order, $languages_id, $default_languages_id, $currency;

        $this->code = 'adyen';
        $this->title = $this->code;
        $this->preferred = true;
        $this->filename = 'adyen.php';
        $this->auto_cancel_period = 30; // In minutes
        $this->connect_via_proxy = false; // localhost test - set to true, server test - set to false //need proxy to connect outside
        $this->force_to_checkoutprocess = true;
        $this->has_ipn = true;
        $this->payment_methods_id = 0;
        $this->payment_methods_parent_id = 0;

        $payment_methods_select_sql = "	SELECT  payment_methods_parent_id, payment_methods_code, 
												payment_methods_types_id, 
												payment_methods_receive_status_mode, payment_methods_id, 
												payment_methods_title, payment_methods_sort_order, 
												payment_methods_receive_status, payment_methods_legend_color, 
												payment_methods_logo 
										FROM " . TABLE_PAYMENT_METHODS . "
										WHERE 1 ";
        if ((int) $pm_id > 0) {
            $payment_methods_select_sql .= "	AND payment_methods_id = '" . (int) $pm_id . "'";
        } else {
            $payment_methods_select_sql .= "	AND payment_methods_filename = '" . tep_db_input($this->filename) . "'";
        }

        $payment_methods_result_sql = tep_db_query($payment_methods_select_sql);
        if ($payment_methods_row = tep_db_fetch_array($payment_methods_result_sql)) {
            $pm_desc_title_select_sql = "	SELECT payment_methods_description_title 
											FROM " . TABLE_PAYMENT_METHODS_DESCRIPTION . " 
											WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "' 
												AND (IF (languages_id = '" . tep_db_input($languages_id) . "', 1, IF ((	SELECT COUNT(payment_methods_id) > 0 
																														FROM " . TABLE_PAYMENT_METHODS_DESCRIPTION . " 
																														WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "' 
																															AND languages_id = '" . (int) $languages_id . "'), 0, languages_id = '" . (int) $default_languages_id . "')))";
            $pm_desc_title_result_sql = tep_db_query($pm_desc_title_select_sql);
            $pm_desc_title_row = tep_db_fetch_array($pm_desc_title_result_sql);
            $this->display_title = $pm_desc_title_row['payment_methods_description_title'];
            $this->description = $this->display_title;
            $this->enabled = $payment_methods_row['payment_methods_receive_status_mode'];
            $this->payment_methods_id = (int) $payment_methods_row['payment_methods_id'];
            $this->payment_methods_parent_id = (int) $payment_methods_row['payment_methods_parent_id'];
            $this->code = $payment_methods_row['payment_methods_code'];
            $this->title = $payment_methods_row['payment_methods_title'];
            $this->sort_order = $payment_methods_row['payment_methods_sort_order'];
            $this->legend_display_colour = $payment_methods_row['payment_methods_legend_color'];
        }

        $configuration_setting_array = $this->load_pm_setting();
        $this->order_status_id = $configuration_setting_array['MODULE_PAYMENT_ADYEN_ORDER_STATUS_ID'];
        $this->processing_status_id = $configuration_setting_array['MODULE_PAYMENT_ADYEN_PROCESSING_STATUS_ID'];
        $this->confirm_complete_days = $configuration_setting_array['MODULE_PAYMENT_ADYEN_CONFIRM_COMPLETE'];
        $this->require_address_information = $configuration_setting_array['MODULE_PAYMENT_ADYEN_MANDATORY_ADDRESS_FIELD'];
        $this->test_mode = ($configuration_setting_array['MODULE_PAYMENT_ADYEN_TEST_MODE'] == 'True' ? true : false);
        $this->message = $configuration_setting_array['MODULE_PAYMENT_ADYEN_MESSAGE'];
        $this->email_message = $configuration_setting_array['MODULE_PAYMENT_ADYEN_EMAIL_MESSAGE'];
        $this->email_footer = str_replace(array("<br>", "<BR>", "\r\n"), array("\n", "\n", ''), $this->email_message);

        $this->adyenCurrencies = $this->get_support_currencies($this->payment_methods_id);
        $this->defCurr = DEFAULT_CURRENCY;
        $this->order_processing_status = ((int) $this->processing_status_id > 0 ? $this->processing_status_id : 7 );
        $this->order_status = ( (int) $this->order_status_id > 0 ? $this->order_status_id : DEFAULT_ORDERS_STATUS_ID );
        $this->skin_code = 'vL0MhFIx'; //B2C skin
        if ($this->test_mode) {
            $this->form_action_url = 'https://test.adyen.com/hpp/pay.shtml';
            $this->ws_url = 'https://pal-test.adyen.com/pal/adapter/httppost';
            //for all PG
            //$this->form_action_url = 'https://test.adyen.com/hpp/select.shtml';
        } else {
            $this->form_action_url = 'https://live.adyen.com/hpp/pay.shtml';
            $this->ws_url = 'https://pal-live.adyen.com/pal/adapter/httppost';
            //for all PG
            //$this->form_action_url = 'https://live.adyen.com/hpp/select.shtml';
        }
    }

    function javascript_validation() {
        return false;
    }

    function selection() {
        global $languages_id, $default_languages_id;
        $selection_array = array();

        $payment_methods_select_sql = "	SELECT payment_methods_id, payment_methods_code, payment_methods_types_id, payment_methods_logo, payment_methods_receive_status_mode 
										FROM " . TABLE_PAYMENT_METHODS . " 
										WHERE payment_methods_receive_status = 1 
											AND payment_methods_receive_status_mode <> 0 
											AND payment_methods_parent_id = '" . (int) $this->payment_methods_id . "' 
										ORDER BY payment_methods_sort_order";
        $payment_methods_result_sql = tep_db_query($payment_methods_select_sql);
        while ($payment_methods_row = tep_db_fetch_array($payment_methods_result_sql)) {
            $pm_desc_title_select_sql = "	SELECT payment_methods_description_title 
											FROM " . TABLE_PAYMENT_METHODS_DESCRIPTION . " 
											WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "' 
												AND (IF (languages_id = '" . tep_db_input($languages_id) . "', 1, IF ((	SELECT COUNT(payment_methods_id) > 0 
																														FROM " . TABLE_PAYMENT_METHODS_DESCRIPTION . " 
																														WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "' 
																															AND languages_id = '" . (int) $languages_id . "'), 0, languages_id = 1)))";
            $pm_desc_title_result_sql = tep_db_query($pm_desc_title_select_sql);
            $pm_desc_title_row = tep_db_fetch_array($pm_desc_title_result_sql);

            $pm_array = $this->load_pm_setting(1, $payment_methods_row['payment_methods_id']);

            $selection_array[] = array('payment_gateway_code' => $this->code,
                'payment_methods_receive_status_mode' => $payment_methods_row['payment_methods_receive_status_mode'],
                'payment_methods_id' => $payment_methods_row['payment_methods_id'],
                'payment_methods_code' => $payment_methods_row['payment_methods_code'],
                'payment_methods_types_id' => $payment_methods_row['payment_methods_types_id'],
                'payment_methods_parent_id' => $this->payment_methods_id,
                'payment_methods_logo' => $payment_methods_row['payment_methods_logo'],
                'payment_methods_description_title' => $pm_desc_title_row['payment_methods_description_title'],
                'currency' => $this->get_support_currencies($payment_methods_row['payment_methods_id']),
                'confirm_complete_days' => $this->confirm_complete_days,
                'show_billing_address' => $this->require_address_information,
                'show_contact_number' => $pm_array['MODULE_PAYMENT_ADYEN_MANDATORY_CONTACT_FIELD'],
                'show_ic' => $pm_array['MODULE_PAYMENT_ADYEN_MANDATORY_IC_FIELD']
            );

            if ($payment_methods_row['payment_methods_receive_status_mode'] == '-1') {
                $pm_status_message_select = "	SELECT payment_methods_status_message 
												FROM " . TABLE_PAYMENT_METHODS_STATUS_DESCRIPTION . " 
												WHERE payment_methods_mode = 'RECEIVE' 
													AND payment_methods_status = '-1' 
													AND languages_id = '" . (int) $languages_id . "' 
													AND payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "'";
                $pm_status_message_result = tep_db_query($pm_status_message_select);
                $pm_status_message_row = tep_db_fetch_array($pm_status_message_result);
                $selection_array[sizeof($selection_array) - 1]['payment_methods_status_message'] = $pm_status_message_row['payment_methods_status_message'];
            }
        }

        return $selection_array;
    }

    function set_support_currencies($currency_array) {
        $this->adyenCurrencies = $currency_array;
    }

    function get_require_address_information() {
        return $this->require_address_information;
    }

    function draw_confirm_button() {
        return tep_image_button2('red', 'javascript:document.checkout_confirmation.submit()', MODULE_PAYMENT_ADYEN_TEXT_CART, 220);
    }

    function get_confirm_button_caption() {
        return MODULE_PAYMENT_ADYEN_TEXT_TITLE;
    }

    function pre_confirmation_check() {
        global $currency;

        if (in_array($currency, $this->adyenCurrencies)) {
            
        }

        return false;
    }

    function confirmation() {
        
    }

    function process_button() {
        global $HTTP_POST_VARS, $shipping_cost, $total_cost, $shipping_selected, $shipping_method, $currencies, $currency, $customer_id, $order, $languages_id;
        global $order_logged, $OFFGAMERS, $country;

        if ($this->force_to_checkoutprocess && !tep_session_is_registered('order_logged'))
            return;

        $country_info_array = tep_get_countries($country, true);
        $default_currency = $this->defCurr;
        $adyenCurrency = $currency;
        if (!in_array($adyenCurrency, $this->adyenCurrencies)) {
            $adyenCurrency = in_array($default_currency, $this->adyenCurrencies) ? $default_currency : $this->adyenCurrencies[0];
        }
        $this->get_merchant_account($adyenCurrency);
        $exponent = $this->get_adyen_exponent($adyenCurrency);
        $OrderAmt = number_format($order->info['total'] * $currencies->get_value($adyenCurrency), $currencies->get_decimal_places($adyenCurrency), '.', '');
        if ($exponent > 0) {
            $OrderAmt = ($OrderAmt * pow(10, $exponent));
        }
//        $OrderAmt = preg_replace('/[^\d]/', '', preg_quote(number_format($order->info['total'] * $currencies->get_value($adyenCurrency), $currencies->get_decimal_places($adyenCurrency), '.', '')));
        $hidden_field_array = array(
            'paymentAmount' => $OrderAmt,
            'currencyCode' => $adyenCurrency,
            'shipBeforeDate' => '',
            'merchantReference' => $order_logged,
            'skinCode' => $this->skin_code,
            'merchantAccount' => $this->merchant_id,
            'sessionValidity' => date('c', strtotime('+1 hour')),
            'shopperEmail' => $order->customer['email_address'],
            'shopperReference' => $customer_id,
            'recurringContract' => 'ONECLICK',
            'allowedMethods' => $this->code,
            'blockedMethods' => '',
            'shopperStatement' => 'Purchase from ' . STORE_NAME . ' #' . $order_logged,
            'merchantReturnData' => $order_logged,
            'billingAddressType' => '',
            'offset' => ''
        );

        foreach ($hidden_field_array as $field => $value) {
            $post_signature .= $value;
            $process_button_string .= tep_draw_hidden_field($field, $value, '', true);
        }

        $process_button_string .= tep_draw_hidden_field('merchantSig', base64_encode(pack("H*", hash_hmac('sha1', $post_signature, $this->secret_word))));
		$process_button_string .= tep_draw_hidden_field('countryCode', $country_info_array['countries_iso_code_2']);

		return $process_button_string;
	}
	
	function postValidation($postData) {
		global $currencies;
    }
	
  	function before_process() {
  		global $order, $order_logged, $currency, $currencies;
		
		if (!tep_session_is_registered('order_logged')) {
			if ($this->get_adyen_exponent($currency) === false) {
				tep_redirect(tep_href_link(FILENAME_CHECKOUT_PAYMENT, 'error_message=' . MODULE_PAYMENT_ADYEN_TEXT_ERROR_MESSAGE, 'SSL', true, false));
				exit;
			}
			return;
		}
	
        if (isset($_GET['authResult']) && ($_GET['authResult'] == 'AUTHORISED' || $_GET['authResult'] == 'PENDING') && isset($_GET['merchantReturnData']) && $_GET['merchantReturnData'] == $order_logged) {
            
		} else {
			tep_redirect(tep_href_link(FILENAME_CHECKOUT_PAYMENT, 'error_message=' . MODULE_PAYMENT_ADYEN_TEXT_ERROR_MESSAGE, 'SSL', true, false));
		}
    }

    function after_process() {
        return false;
    }

    function link_to_payment($new_order_id) {
        global $order_logged;

        if (tep_session_is_registered('order_logged'))
            tep_session_unregister('order_logged');

        tep_session_register('order_logged');
        $order_logged = $new_order_id;

        require(DIR_WS_INCLUDES . 'modules/payment/adyen/catalog/adyen_splash.php');
        return;
    }

    function post_capture($order_id, $capture_by = '') {
        global $login_email_address;

        if (tep_not_null($capture_by)) {
            $changed_by = $capture_by;
            $comments_type = 0;
        } else {
            $changed_by = $login_email_address;
            $comments_type = 1;
        }

        $result = array();

        $adyen_info_select_sql = "	SELECT adyen_event_code, adyen_currency, adyen_amount, adyen_psp_reference
									FROM " . TABLE_ADYEN . " 
									WHERE adyen_order_id = '" . tep_db_input($order_id) . "'";
		$adyen_info_result_sql = tep_db_query($adyen_info_select_sql);
		if ($adyen_info_row = tep_db_fetch_array($adyen_info_result_sql)) {
			if (trim($adyen_info_row['adyen_event_code']) == 'AUTHORISATION') {
				$this->get_merchant_account($adyen_info_row['adyen_currency']);

				$request = array(
					"action" => "Payment.capture",
					"modificationRequest.merchantAccount" => $this->merchant_id,
					"modificationRequest.modificationAmount.currency" => $adyen_info_row['adyen_currency'],
					"modificationRequest.modificationAmount.value" => $adyen_info_row['adyen_amount'],
					"modificationRequest.originalReference" => $adyen_info_row['adyen_psp_reference'],
					"modificationRequest.reference" => $order_id
				 );
				$ch = curl_init();
				curl_setopt($ch, CURLOPT_URL, $this->ws_url);
				curl_setopt($ch, CURLOPT_HEADER, false);
				curl_setopt($ch, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
                curl_setopt($ch, CURLOPT_USERPWD, $this->ws_user . ":" . $this->ws_password);
                curl_setopt($ch, CURLOPT_POST, count($request));
                curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($request));
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
                curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);

                $curl_result = curl_exec($ch);
                if ($curl_result === false) {
                    ob_start();
                    echo "========================ADYEN POST CAPTURE CURL FAILED=========================<BR>";
                    echo "Error: " . curl_error($ch);
                    echo "============================================================================<BR>";
                    echo "================================REQUEST DATA=============================<BR>";
                    print_r($request);
                    echo "============================================================================<BR>";
                    $debug_html = ob_get_contents();
                    ob_end_clean();
                    @tep_mail('Adyen API Report', '<EMAIL>', $order_id . ' - ADYEN POST CAPTURE CURL ERROR', $debug_html, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
                } else {
                    /**
                     * The response. You will receive a confirmation that we received your request: [capture-received].
                     *
                     * Please note: The result of the capture is sent via a notification with eventCode CAPTURE.
                     */
                    parse_str($curl_result, $result_array);
                    $adyen_payment_history_data_array = array('adyen_orders_id' => tep_db_prepare_input($order_id),
                        'adyen_event_code' => 'REQUEST CAPTURE',
                        'adyen_date' => 'now()',
                        'adyen_reason' => $result_array['modificationResult_pspReference'] . ' - ' . $result_array['modificationResult_response'],
                        'changed_by' => $changed_by
                    );
                    tep_db_perform(TABLE_ADYEN_STATUS_HISTORY, $adyen_payment_history_data_array);
                }
                curl_close($ch);
                if (isset($result_array['modificationResult_response']) && $result_array['modificationResult_response'] == '[capture-received]') {
                    $adyen_capture_update_sql = "	UPDATE " . TABLE_ADYEN . " 
													SET adyen_capture_request = 1 
													WHERE adyen_order_id = '" . tep_db_input($order_id) . "'";
                    tep_db_query($adyen_capture_update_sql);

                    $order_history_data_array = array('orders_id' => $order_id,
                        'orders_status_id' => '',
                        'date_added' => 'now()',
                        'customer_notified' => 0,
                        'comments' => 'Remotely post captured this order',
                        'comments_type' => $comments_type,
                        'set_as_order_remarks' => '0',
                        'changed_by' => $changed_by
                    );
                    tep_db_perform(TABLE_ORDERS_STATUS_HISTORY, $order_history_data_array);
                    tep_update_orders_status_counter($order_history_data_array);

                    $result = array('code' => 'success',
                        'text' => SUCCESS_ORDER_POST_CAPTURED);
                }
            } else if (trim($adyen_info_row['adyen_event_code']) == 'CAPTURE') {
                $result = array('code' => 'success',
                    'text' => ERROR_ORDER_ALREADY_CAPTURED);
            } else {
                $result = array('code' => 'error',
                    'text' => ERROR_ORDER_CANNOT_BE_CAPTURED);
            }
        } else {
            $result = array('code' => 'error',
                'text' => ERROR_ORDER_DOES_NOT_EXIST);
        }

        return $result;
    }

    function is_supported_currency($selected_currency) {
        return (in_array($selected_currency, $this->adyenCurrencies) ? true : false);
    }

    function output_error() {
        return false;
    }

    function get_adyen_exponent($currency_code) {
        $exponentArray = array(
            'CAD' => 2,
            'CHF' => 2,
            'DKK' => 2,
            'EUR' => 2,
            'GBP' => 2,
            'HKD' => 2,
            'JPY' => 0,
            'NOK' => 2,
            'NZD' => 2,
            'PLN' => 2,
            'SEK' => 2,
            'SGD' => 2,
            'USD' => 2,
            'ZAR' => 2,
            'AUD' => 2,
            'AED' => 2,
            'ARS' => 2,
            'BAM' => 2,
            'BGL' => 2,
            'BGN' => 2,
            'BHD' => 3,
            'BRL' => 2,
            'CLP' => 2,
            'CNY' => 2,
            'COP' => 2,
            'CSD' => 2,
            'CZK' => 2,
            'EEK' => 2,
            'EGP' => 2,
            'FJD' => 2,
            'HRK' => 2,
            'IDR' => 0,
            'ILS' => 2,
            'INR' => 2,
            'ISK' => 2,
            'JOD' => 3,
            'KES' => 2,
            'KRW' => 0,
            'KWD' => 3,
            'LBP' => 2,
            'LKR' => 2,
            'LTL' => 2,
            'LVL' => 2,
            'MAD' => 2,
            'MXP' => 2,
            'MXN' => 2,
            'MYR' => 2,
            'OMR' => 3,
            'PEN' => 2,
            'PHP' => 2,
            'PKR' => 2,
            'QAR' => 2,
            'ROL' => 2,
            'RON' => 2,
            'RUB' => 2,
            'SAR' => 2,
            'SKK' => 2,
            'THB' => 2,
            'TRY' => 2,
            'TWD' => 2,
            'UAH' => 2,
            'UYU' => 2,
            'ANG' => 2,
            'HUF' => 2,
            'XCD' => 2,
            'KZT' => 2,
            'AZN' => 2,
            'NGN' => 2,
            'BYR' => 0,
            'RSD' => 2,
            'VND' => 0,
            'ALL' => 2,
            'UZS' => 2,
            'VEF' => 2
        );

        return (isset($exponentArray[$currency_code]) && $exponentArray[$currency_code] >= 0) ? $exponentArray[$currency_code] : false;
    }

    function check() {
        if (!isset($this->_check)) {
            $payment_methods_select_sql = "	SELECT payment_methods_id
											FROM " . TABLE_PAYMENT_METHODS . " as pm
											WHERE pm.payment_methods_id = '" . (int) $this->payment_methods_id . "'";
            $payment_methods_result_sql = tep_db_query($payment_methods_select_sql);
            $this->_check = tep_db_num_rows($payment_methods_result_sql);
        }
        return $this->_check;
    }

    function is_processed_order($order_id) {
        $payment_status_select_sql = "	SELECT adyen_event_code 
										FROM " . TABLE_ADYEN . " 
										WHERE adyen_order_id = '" . tep_db_input($order_id) . "'";
        $payment_status_result_sql = tep_db_query($payment_status_select_sql);
        $payment_status_row = tep_db_fetch_array($payment_status_result_sql);

        if (in_array($payment_status_row['adyen_event_code'], array('AUTHORISATION', 'CAPTURE'))) {
            return true;
        } else {
            return false;
        }
    }

    function check_trans_status() {
        //for crew orders.php Verify Button to be active
    }

    function load_pm_setting($language_id = 1, $pm_id = '') {
        $pm_setting_array = array();
        //load value from DB

        if (tep_not_null($pm_id)) {
            $payment_method_id = $pm_id;

            $payment_methods_parent_id_select_sql = "	SELECT payment_methods_parent_id 
														FROM " . TABLE_PAYMENT_METHODS . " 
														WHERE payment_methods_id = '" . (int) $pm_id . "'";
            $payment_methods_parent_id_result_sql = tep_db_query($payment_methods_parent_id_select_sql);
            $payment_methods_parent_id_row = tep_db_fetch_array($payment_methods_parent_id_result_sql);

            $payment_gateway_id = $payment_methods_parent_id_row['payment_methods_parent_id'];
        } else {
            $payment_method_id = $this->payment_methods_id;
            $payment_gateway_id = $this->payment_methods_parent_id;
        }

        $payment_configuration_info_select_sql = "	SELECT pci.payment_configuration_info_key, pcid.payment_configuration_info_value
													FROM " . TABLE_PAYMENT_CONFIGURATION_INFO . " as pci
													LEFT JOIN " . TABLE_PAYMENT_CONFIGURATION_INFO_DESCRIPTION . " as pcid 
														ON pci.payment_configuration_info_id = pcid.payment_configuration_info_id
															AND pcid.languages_id = '" . (int) $language_id . "'
													WHERE pci.payment_methods_id = '" . (int) $payment_method_id . "'
													ORDER BY pci.payment_configuration_info_sort_order";
        $payment_configuration_info_result_sql = tep_db_query($payment_configuration_info_select_sql);
        if (tep_db_num_rows($payment_configuration_info_result_sql)) {
            while ($payment_configuration_info_row = tep_db_fetch_array($payment_configuration_info_result_sql)) {
                $pm_setting_array[$payment_configuration_info_row['payment_configuration_info_key']] = $payment_configuration_info_row['payment_configuration_info_value'];
            }
        } else if ((int) $payment_gateway_id > 0) { //load value from PG
            $payment_configuration_info_select_sql = "	SELECT pci.payment_configuration_info_key, pcid.payment_configuration_info_value
														FROM " . TABLE_PAYMENT_CONFIGURATION_INFO . " as pci
														LEFT JOIN " . TABLE_PAYMENT_CONFIGURATION_INFO_DESCRIPTION . " as pcid 
															ON pci.payment_configuration_info_id = pcid.payment_configuration_info_id
																AND pcid.languages_id = '" . (int) $language_id . "'
														WHERE pci.payment_methods_id = '" . (int) $payment_gateway_id . "'
														ORDER BY pci.payment_configuration_info_sort_order";
            $payment_configuration_info_result_sql = tep_db_query($payment_configuration_info_select_sql);
            while ($payment_configuration_info_row = tep_db_fetch_array($payment_configuration_info_result_sql)) {
                $pm_setting_array[$payment_configuration_info_row['payment_configuration_info_key']] = $payment_configuration_info_row['payment_configuration_info_value'];
            }
        }

        return $pm_setting_array;
    }

    function get_merchant_account($selected_currency) {
        $payment_methods_instance_id = 0;
        $payment_gateway_instance_select_sql = "SELECT pmi.payment_methods_instance_id, pmi.payment_methods_instance_follow_default, pmi.currency_code 
				 								FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " as pmi
				 								WHERE pmi.payment_methods_id = '" . (int) $this->payment_methods_id . "'
				 								LIMIT 1";
        $payment_gateway_instance_result_sql = tep_db_query($payment_gateway_instance_select_sql);
        if (tep_db_num_rows($payment_gateway_instance_result_sql)) {
            $payment_gateway_instance_select_sql = "SELECT pmi.payment_methods_instance_id, pmi.payment_methods_instance_follow_default 
					 								FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " as pmi
					 								WHERE pmi.currency_code = '" . tep_db_input($selected_currency) . "' 
					 									AND pmi.payment_methods_id = '" . (int) $this->payment_methods_id . "'";
            $payment_gateway_instance_result_sql = tep_db_query($payment_gateway_instance_select_sql);
            if ($payment_gateway_instance_row = tep_db_fetch_array($payment_gateway_instance_result_sql)) {
                if ((int) $payment_gateway_instance_row['payment_methods_instance_follow_default'] > 0) { // Follow Default Currency Setting
                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_follow_default'];
                } else { // Has own setting
                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_id'];
                }
            }
        } else {
            $payment_gateway_instance_select_sql = "SELECT pmi.payment_methods_instance_id, pmi.payment_methods_instance_follow_default 
					 								FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " as pmi
					 								INNER JOIN " . TABLE_PAYMENT_METHODS . " as pm
					 									ON pm.payment_methods_parent_id = pmi.payment_methods_id
					 								WHERE pmi.currency_code = '" . tep_db_input($selected_currency) . "' 
					 									AND pm.payment_methods_id = '" . (int) $this->payment_methods_id . "'";
            $payment_gateway_instance_result_sql = tep_db_query($payment_gateway_instance_select_sql);
            if ($payment_gateway_instance_row = tep_db_fetch_array($payment_gateway_instance_result_sql)) {
                if ((int) $payment_gateway_instance_row['payment_methods_instance_follow_default'] > 0) {
                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_follow_default'];
                } else {
                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_id'];
                }
            }
        }

        $payment_gateway_instance_setting_select_sql = "SELECT pmis.payment_methods_instance_setting_key, pmis.payment_methods_instance_setting_value 
						 								FROM " . TABLE_PAYMENT_METHODS_INSTANCE_SETTING . " as pmis
						 								WHERE pmis.payment_methods_instance_id = '" . (int) $payment_methods_instance_id . "'";
        $payment_gateway_instance_setting_result_sql = tep_db_query($payment_gateway_instance_setting_select_sql);
        while ($payment_gateway_instance_setting_row = tep_db_fetch_array($payment_gateway_instance_setting_result_sql)) {
            switch ($payment_gateway_instance_setting_row['payment_methods_instance_setting_key']) {
                case 'MODULE_PAYMENT_ADYEN_MERCHANT_CODE':
                    $this->merchant_id = $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];
                    break;
                case 'MODULE_PAYMENT_ADYEN_HMAC':
                    $this->secret_word = $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];
                    break;
                case 'MODULE_PAYMENT_ADYEN_HTTP_USERNAME':
                    $this->http_user = $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];
                    break;
                case 'MODULE_PAYMENT_ADYEN_HTTP_PASSWORD':
                    $this->http_password = $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];
                    break;
                case 'MODULE_PAYMENT_ADYEN_IP_LIST':
                    $this->ip_list = $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];
                    break;
                case 'MODULE_PAYMENT_ADYEN_WS_USERNAME':
                    $this->ws_user = $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];
                    break;
                case 'MODULE_PAYMENT_ADYEN_WS_PASSWORD':
                    $this->ws_password = $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];
                    break;
            }
        }
    }

    function install() {
        $install_configuration_flag = true;
        if ($this->payment_methods_id > 0) {
            if ($this->receive_status != 1) {
                $update_receive_status_sql_data = array("payment_methods_receive_status" => 1);
                tep_db_perform(TABLE_PAYMENT_METHODS, $update_receive_status_sql_data, 'update', "payment_methods_id = '" . (int) $this->payment_methods_id . "'");
            } else {
                $install_configuration_flag = false;
            }
        } else {
            $install_payment_gateway_data_sql = array('payment_methods_code' => $this->code,
                'payment_methods_send_status' => 0,
                'payment_methods_title' => $this->code,
                'payment_methods_types_id' => 0,
                'payment_methods_parent_id' => 0,
                'payment_methods_legend_color' => '#A3B9A3',
                'payment_methods_sort_order' => 6,
                'payment_methods_filename' => $this->filename,
                'payment_methods_logo' => '',
                'date_added' => 'now()',
                'last_modified' => 'now()',
                'payment_methods_receive_status' => 1,
                'payment_methods_receive_status_mode' => 0
            );
            tep_db_perform(TABLE_PAYMENT_METHODS, $install_payment_gateway_data_sql);
            $this->payment_methods_id = tep_db_insert_id();
        }
        if ($install_configuration_flag) {
            $install_key_array = array();
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Set Order Status',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_ADYEN_ORDER_STATUS_ID',
                    'payment_configuration_info_description' => 'Set the status of orders made with this payment module to this value.',
                    'payment_configuration_info_sort_order' => '1335',
                    'set_function' => 'tep_cfg_pull_down_order_statuses(',
                    'use_function' => 'tep_get_order_status_name',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => '1',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Set Order\'s "Processing" Status',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_ADYEN_PROCESSING_STATUS_ID',
                    'payment_configuration_info_description' => 'Set the initial processing status of orders made with this payment module to this value.',
                    'payment_configuration_info_sort_order' => '1340',
                    'set_function' => 'tep_cfg_pull_down_order_statuses(',
                    'use_function' => 'tep_get_order_status_name',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => '7',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Payment Message',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_ADYEN_MESSAGE',
                    'payment_configuration_info_description' => 'Payment message will show up during checkout process (smart2pay).',
                    'payment_configuration_info_sort_order' => '1350',
                    'set_function' => 'tep_cfg_textarea(',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'Thank you for shopping at OffGamers.com.',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Payment Email Message',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_ADYEN_EMAIL_MESSAGE',
                    'payment_configuration_info_description' => 'This message will show up in order confirmation e-mail.',
                    'payment_configuration_info_sort_order' => '1360',
                    'set_function' => 'tep_cfg_textarea(',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'Thank you for shopping at OffGamers.com.',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Confirm Complete (in days)',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_ADYEN_CONFIRM_COMPLETE',
                    'payment_configuration_info_description' => 'The number of days in which an order made by this payment method is confirm completed.',
                    'payment_configuration_info_sort_order' => '1265',
                    'set_function' => '',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => '0',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Test Mode',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_ADYEN_TEST_MODE',
                    'payment_configuration_info_description' => 'Testing Mode?',
                    'payment_configuration_info_sort_order' => '100',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Require Address Information',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_ADYEN_MANDATORY_ADDRESS_FIELD',
                    'payment_configuration_info_description' => 'Set address field as required info during checkout.',
                    'payment_configuration_info_sort_order' => '1400',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );
            foreach ($install_key_array as $data) {
                tep_db_perform(TABLE_PAYMENT_CONFIGURATION_INFO, $data['info']);
                $payment_conf_id = tep_db_insert_id();

                $data['desc']['payment_configuration_info_id'] = (int) $payment_conf_id;
                tep_db_perform(TABLE_PAYMENT_CONFIGURATION_INFO_DESCRIPTION, $data['desc']);
            }
        }
        return 1;
    }

    function merchant_information_keys() {
        include_once(DIR_WS_CLASSES . "payment_methods.php");
        
        return array(
            'MODULE_PAYMENT_ADYEN_MERCHANT_CODE' => array('field' => 'text', 'mapping' => 'MODULE_PAYMENT_ADYEN_MERCHANT_CODE'),
            'MODULE_PAYMENT_ADYEN_HMAC' => array('field' => 'text', 'mapping' => 'MODULE_PAYMENT_ADYEN_HMAC'),
            'MODULE_PAYMENT_ADYEN_HTTP_USERNAME' => array('field' => 'text', 'mapping' => 'MODULE_PAYMENT_ADYEN_HTTP_USERNAME'),
            'MODULE_PAYMENT_ADYEN_HTTP_PASSWORD' => array('field' => 'text', 'mapping' => 'MODULE_PAYMENT_ADYEN_HTTP_PASSWORD'),
            'MODULE_PAYMENT_ADYEN_IP_LIST' => array('field' => 'text', 'mapping' => 'MODULE_PAYMENT_ADYEN_IP_LIST'),
            'MODULE_PAYMENT_ADYEN_WS_USERNAME' => array('field' => 'text', 'mapping' => 'MODULE_PAYMENT_ADYEN_WS_USERNAME'),
            'MODULE_PAYMENT_ADYEN_WS_PASSWORD' => array('field' => 'text', 'mapping' => 'MODULE_PAYMENT_ADYEN_WS_PASSWORD'),
            'MODULE_PAYMENT_TAX' => array("field" => 'select', 'mapping' => "MODULE_PAYMENT_TAX", "select" => payment_methods::get_tax_option())
        );
    }

    function get_orders_payment_info_file() {
        return DIR_FS_CATALOG_MODULES . 'payment/adyen/admin/orders.inc.php';
    }

    function get_orders_list_payment_info_file() {
        return DIR_FS_CATALOG_MODULES . 'payment/adyen/admin/orders_list.inc.php';
    }

}

?>