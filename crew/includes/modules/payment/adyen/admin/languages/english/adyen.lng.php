<?php

define('TABLE_HEADING_ADYEN_DATE', 'History Date');
define('TABLE_HEADING_ADYEN_DESCRIPTION', 'Description');
define('TABLE_HEADING_ADYEN_CHANGED_BY', 'Changed By');
define('TABLE_HEADING_ADYEN_EVENT_CODE', 'Event Code');

//Define Merchant Key Language
define('MODULE_PAYMENT_ADYEN_MERCHANT_CODE', 'Merchant Code');
define('MODULE_PAYMENT_ADYEN_HMAC', 'HMAC');
define('MODULE_PAYMENT_ADYEN_HTTP_USERNAME', 'HTTP Username');
define('MODULE_PAYMENT_ADYEN_HTTP_PASSWORD', 'HTTP Password');
define('MODULE_PAYMENT_ADYEN_IP_LIST', 'IP List');
define('MODULE_PAYMENT_ADYEN_WS_USERNAME', 'Webservice Username');
define('MODULE_PAYMENT_ADYEN_WS_PASSWORD', 'Webservice Password');
define('MODULE_PAYMENT_TAX', 'Tax');

define('ENTRY_ADYEN_PAYMENT_TYPE', 'Payment Type');
define('ENTRY_ADYEN_PSP_REFERENCE', 'PSP Reference');
define('ENTRY_ADYEN_ORIGINAL_REFERENCE', 'Original Reference');
define('ENTRY_ADYEN_EVENT_DATE', 'Event Date');
define('ENTRY_ADYEN_MERCHANT_ACCOUNT', 'Merchant Account Code');
define('ENTRY_ADYEN_OPERATIONS', 'Operations');
define('ENTRY_ADYEN_CURRENCY', 'Payment Currency');
define('ENTRY_ADYEN_AMOUNT', 'Payment Amount');
define('ENTRY_ADYEN_CC_NO', 'Credit Card No');
define('ENTRY_ADYEN_CVC_RESULT', 'CVC Result');
define('ENTRY_ADYEN_AVS_RESULT', 'AVS Result');
define('ENTRY_ADYEN_EXPIRY_DATE', 'Credit Card Expiry Date');
define('ENTRY_ADYEN_AUTH_CODE', 'Authorisation Code');
define('ENTRY_ADYEN_FRAUD_SCORE', 'Fraud Score');
define('ENTRY_ADYEN_EXTRA_COST', 'Extra Cost');
define('ENTRY_ADYEN_AUTHENTICATION_RESULT', '3D Authentication Result');
define('ENTRY_ADYEN_AUTHENTICATION_OFFERED', '3D Authentication Offered');
define('ENTRY_ADYEN_PAYMENT_STATUS', 'Payment Status');
define('TEXT_STATUS_FAILED', 'Failed');
define('TEXT_STATUS_SUCCESS', 'Success');
define('TEXT_NOT_AVAILABLE', 'N/A');
define('ENTRY_ADYEN_CC_ISSUER_COUNTRY', 'Issuer Country');
?>