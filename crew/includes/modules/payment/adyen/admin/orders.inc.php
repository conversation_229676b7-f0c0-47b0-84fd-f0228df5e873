<?php
include_once(DIR_FS_CATALOG_MODULES . 'payment/adyen/admin/languages/' . $language . '/adyen.lng.php');

$adyen_trans_info_select_sql = "SELECT * FROM " . TABLE_ADYEN . " WHERE adyen_order_id ='" . (int)$oID . "'";
$adyen_trans_info_result_sql= tep_db_query($adyen_trans_info_select_sql);

$adyen_payment_status_history_info_select_sql = "SELECT * FROM " . TABLE_ADYEN_STATUS_HISTORY . " WHERE adyen_orders_id = '" . tep_db_input($oID) . "'";
$adyen_payment_status_history_info_result_sql = tep_db_query($adyen_payment_status_history_info_select_sql);
$payment_methods_obj = new adyen($order->info['payment_methods_id']);
?>
<table border="0" width="100%" cellspacing="0" cellpadding="2">
<?php ob_start(); 

?>
	<tr>
		<td class="main">
      		<table border="0" width="100%" cellspacing="0" cellpadding="2">
      			<tr>
      				<td class="main" width="12%" nowrap><b><?php echo ENTRY_PAYMENT_METHOD?></b></td>
      				<td class="main">
      					<div>
      						<?php if((int)$order->info['payment_methods_id']>0) { ?><div class="<?php echo ((int)$payment_info_array[$order->info['payment_methods_id']]->confirm_complete_days>0?'rp_icon':'nrp_icon')?>"><b><?php echo ((int)$payment_info_array[$order->info['payment_methods_id']]->confirm_complete_days>0?'RP':'NRP')?></b></div> <?php } ?>
      						<div style="width:30px; height:15px; background-color:<?php echo $payment_module_info['display_colour']?>; float:left; vertical-align:top; text-align:center;">&nbsp;</div>
      						<div style="vertical-align: top">
      							&nbsp;&nbsp;
<?php
	if ($view_payment_details_permission) {
		if (tep_not_null($last_payment_method) && $last_payment_method['payment_methods_id'] != $order->info['payment_methods_id']) {
			echo '<span class="redIndicator">'.(tep_not_null($payment_module_info['payment_methods_parent_title'])?$payment_module_info['payment_methods_parent_title'] . ' - ' :'') . $payment_module_info['payment_methods_title'].'</span>';
		} else {
			echo (tep_not_null($payment_module_info['payment_methods_parent_title'])?$payment_module_info['payment_methods_parent_title'] . ' - ' :'') . $payment_module_info['payment_methods_title'];
		}
		
		if (tep_not_null($last_payment_method) && $last_payment_method['payment_methods_id'] != $order->info['payment_methods_id']) {
			echo '&nbsp;&nbsp;<span class="redIndicator">(Last Payment Method: ' . (tep_not_null($last_payment_method['parent_payment_methods_title'])? $last_payment_method['parent_payment_methods_title'] . ' - ' : '' ) . $last_payment_method['payment_methods_title'] . ')</span>';
		}
	} else {
		echo (tep_not_null($payment_module_info['payment_methods_parent_title'])?$payment_module_info['payment_methods_parent_title'] . ' - ' :'') . $payment_module_info['payment_methods_title'];
	}
?>
							</div>
      					</div>
      				</td>
				</tr>
			</table>
		</td>
	</tr>
<?php
$payment_method_title_info = ob_get_contents();
ob_end_clean();

if (tep_db_num_rows($adyen_trans_info_result_sql) || tep_db_num_rows($adyen_payment_status_history_info_result_sql)) {
	$adyen_trans_info_row = tep_db_fetch_array($adyen_trans_info_result_sql);
	
	echo $payment_method_title_info;
	
	if (!$view_payment_details_permission) {
		;
	} else {
		$payment_methods_id_select_sql = "	SELECT pm.payment_methods_id 
											FROM " . TABLE_PAYMENT_METHODS . " AS pm
											INNER JOIN " . TABLE_PAYMENT_METHODS . " AS pg 
												ON (pm.payment_methods_parent_id = pg.payment_methods_id AND pg.payment_methods_filename = '" . tep_db_input('adyen.php') . "') 
											WHERE pm.payment_methods_code = '" . tep_db_input($adyen_trans_info_row['adyen_payment_method']) . "' 
											LIMIT 1";
		$payment_methods_id_result_sql = tep_db_query($payment_methods_id_select_sql);
		$payment_methods_id_row = tep_db_fetch_array($payment_methods_id_result_sql);
		$exponent = $payment_methods_obj->get_adyen_exponent($adyen_trans_info_row['adyen_currency']);
		
?>
  	<tr>
    	<td class="main">
      		<table border="0" width="100%" cellspacing="0" cellpadding="2">
      			<tr valign="top">
        			<td width="39%" nowrap>
              			<table border="0" cellspacing="0" cellpadding="2">
              				<tr>
                				<td class="main" valign="top" nowrap><b><?php echo ENTRY_ADYEN_PAYMENT_TYPE?></b>&nbsp;</td>
                				<td class="main" nowrap>
                				<?php
                					if ($payment_methods_id_row['payment_methods_id'] == $order->info['payment_methods_id']) {
                						echo ($adyen_trans_info_row['adyen_payment_method'] == 'mc' ? 'mastercard' : $adyen_trans_info_row['adyen_payment_method']);
                					} else {
                						echo '<span class="redIndicator">'.$adyen_trans_info_row['adyen_payment_method'].'<br><span class="smallText">Does not match checkout payment method.<br>'.$adyen_trans_info_row['adyen_payment_method'].' is the actual paid payment method.</span></span>';
                					}
                				?>
                				</td>
              				</tr>
              				<tr>
                				<td class="main" valign="top" nowrap><b><?php echo ENTRY_ADYEN_PSP_REFERENCE?></b>&nbsp;</td>
                				<td class="main" nowrap><?php echo (tep_not_null($adyen_trans_info_row['adyen_original_reference']) && $adyen_trans_info_row['adyen_original_reference'] > 0 ? $adyen_trans_info_row['adyen_original_reference'] : $adyen_trans_info_row['adyen_psp_reference']) ?></td>
              				</tr>
<!--              				<tr>
                				<td class="main" valign="top" nowrap><b><?php echo ENTRY_ADYEN_ORIGINAL_REFERENCE?></b>&nbsp;</td>
                				<td class="main" nowrap><?php echo $adyen_trans_info_row['adyen_original_reference']?></td>
              				</tr>-->
              				<tr>
                				<td class="main" valign="top" nowrap><b><?php echo ENTRY_ADYEN_EVENT_DATE?></b>&nbsp;</td>
                				<td class="main" nowrap><?php echo $adyen_trans_info_row['adyen_event_date']?></td>
              				</tr>
<!--						<tr>
                				<td class="main" valign="top" nowrap><b><?php echo ENTRY_ADYEN_MERCHANT_ACCOUNT?></b>&nbsp;</td>
                				<td class="main" nowrap><?php echo $adyen_trans_info_row['adyen_merchant_account_code']?></td>
              				</tr>-->
              				<tr>
                				<td class="main" valign="top" nowrap><b><?php echo ENTRY_ADYEN_OPERATIONS?></b>&nbsp;</td>
                				<td class="main" nowrap><?php echo $adyen_trans_info_row['adyen_operations']?></td>
              				</tr>
              				<tr>
                				<td class="main" valign="top" nowrap><b><?php echo ENTRY_ADYEN_CC_NO?></b>&nbsp;</td>
                				<td class="main" nowrap>
<?php
								if (tep_not_null($adyen_trans_info_row['adyen_cc_card_bin']) && tep_not_null($adyen_trans_info_row['adyen_cc_card_summary'])) {
									$card_number = $adyen_trans_info_row['adyen_cc_card_bin'] . '******' . $adyen_trans_info_row['adyen_cc_card_summary'];
									echo $card_number;
									
									$check_credit_card_array = array();
									$check_credit_card_select_sql = "	SELECT o.customers_id
																		FROM " . TABLE_ORDERS . " AS o 
																		INNER JOIN " . TABLE_ADYEN . " AS a 
																			ON o.orders_id = a.adyen_order_id
																		WHERE a.adyen_cc_card_bin = '" . tep_db_input($adyen_trans_info_row['adyen_cc_card_bin']) . "'
																			AND a.adyen_cc_card_summary = '" . tep_db_input($adyen_trans_info_row['adyen_cc_card_summary']) . "'
																		GROUP BY o.customers_id";
									$check_credit_card_result_sql = tep_db_query($check_credit_card_select_sql);
									while ($check_credit_card_row = tep_db_fetch_array($check_credit_card_result_sql)) {
										$check_credit_card_array[] = $check_credit_card_row['customers_id'];
									}
									if (count($check_credit_card_array)>1) {
            							echo 	tep_draw_form('cust_lists_share_cc', FILENAME_CUSTOMERS, 'action=show_report&customer_lists_subaction=do_search', 'post', 'target="_blank"') . "\n" . 
            							tep_draw_hidden_field('customer_share_id', tep_array_serialize($check_credit_card_array)) . "\n" . 
            									"<BR><span class='redIndicator'>Same credit card used in <a href='javascript:document.cust_lists_share_cc.submit();'>" . count($check_credit_card_array) . "</a> profiles.</span>" . 
												'</form>' . "\n";
									}
									$cc_check_info = array(	'card_number' => $card_number,
															'date_purchased' => $order->info['date_purchased'],
															'orders_id' => $order->order_id,
															'customers_id' => $order->customer['id']);
									$cc_verified_date = tep_get_payment_info_verified_date($cc_check_info, 'credit_card');
									echo "<br><span class='redIndicator'>First successful verified date by this user: ".(tep_not_null($cc_verified_date)?$cc_verified_date:"NEVER")."</span><br>(Data can only traced back to 2009-12-09)</span>";
								} else {
									echo "N/A";
								}
?>
								</td>
              				</tr>
              				<tr>
                				<td class="main" valign="top" nowrap><b><?php echo ENTRY_ADYEN_CC_ISSUER_COUNTRY?></b>&nbsp;</td>
                				<td class="main" nowrap><?php echo isset($adyen_trans_info_row['adyen_cc_issuer_country']) ? $adyen_trans_info_row['adyen_cc_issuer_country'] : ''?></td>
              				</tr>
              				<tr>
                				<td class="main" valign="top" nowrap><b><?php echo ENTRY_ADYEN_EXPIRY_DATE?></b>&nbsp;</td>
                				<td class="main" nowrap><?php echo $adyen_trans_info_row['adyen_cc_expiry_date']?></td>
              				</tr>
              				<tr>
                				<td class="main" valign="top" nowrap><b><?php echo ENTRY_ADYEN_AUTH_CODE?></b>&nbsp;</td>
                				<td class="main" nowrap><?php echo $adyen_trans_info_row['adyen_cc_auth_code']?></td>
              				</tr>
              				<tr>
                				<td class="main" valign="top" nowrap><b><?php echo ENTRY_ADYEN_EXTRA_COST?></b>&nbsp;</td>
                				<td class="main" nowrap>
							<?php 
									if ($adyen_trans_info_row['adyen_cc_extra_cost'] > 0) {
										$adyenCostAmt = $adyen_trans_info_row['adyen_cc_extra_cost'];
										if ($exponent > 0) {
											$adyenCostAmt = ($adyenCostAmt / pow(10, $exponent));
										}
										$adyenGrossAmt = number_format($adyenCostAmt, $currencies->currencies[$order->info["currency"]]['decimal_places'], $currencies->currencies[$order->info["currency"]]['decimal_point'], $currencies->currencies[$order->info["currency"]]['thousands_point']);
										echo $currencies->currencies[$order->info["currency"]]['symbol_left'] . $adyenGrossAmt . $currencies->currencies[$order->info["currency"]]['symbol_right'];
									}
							?>
								</td>
              				</tr>
              				<tr>
                				<td class="main" valign="top" nowrap><b><?php echo ENTRY_ADYEN_AUTHENTICATION_RESULT?></b>&nbsp;</td>
                				<td class="main" nowrap><?php echo ($adyen_trans_info_row['adyen_cc_three_d_auth'] == 'true' ? 'Yes' : 'No') ?></td>
              				</tr>
              				<tr>
                				<td class="main" valign="top" nowrap><b><?php echo ENTRY_ADYEN_AUTHENTICATION_OFFERED?></b>&nbsp;</td>
                				<td class="main" nowrap><?php echo ($adyen_trans_info_row['adyen_cc_three_d_auth_offer'] == 'true' ? 'Yes' : 'No')?></td>
              				</tr>
              			</table>
        			</td>
        			<td>
        				<table border="0" cellspacing="0" cellpadding="1">
        					<tr>
                				<td class="main" valign="top" nowrap><b><?php echo ENTRY_ADYEN_PAYMENT_STATUS?></b>&nbsp;</td>
                				<td class="main" nowrap>
                				<?php
                					if (tep_not_null($adyen_trans_info_row['adyen_success']) && $adyen_trans_info_row['adyen_success']=='true') {
                						echo TEXT_STATUS_SUCCESS;
                					} else {
                						echo TEXT_STATUS_FAILED;
                					}
								?>
                				</td>
              				</tr>
        					<tr>
        						<td colspan="2">
									<table border="1" cellspacing="0" cellpadding="2">
		        						<tr>
		        							<td class="smallText" nowrap><b><?php echo TABLE_HEADING_ADYEN_DATE?></b></td>
											<td class="smallText" nowrap><b><?php echo TABLE_HEADING_ADYEN_EVENT_CODE?></b></td>
		        							<td class="smallText" nowrap><b><?php echo TABLE_HEADING_ADYEN_DESCRIPTION?></b></td>
		        							<td class="smallText" nowrap><b><?php echo TABLE_HEADING_ADYEN_CHANGED_BY?></b></td>
		        						</tr>
<?php		while ($adyen_payment_status_history_info_row = tep_db_fetch_array($adyen_payment_status_history_info_result_sql)) {
        	echo ' 						<tr>
                							<td class="smallText" nowrap>'.$adyen_payment_status_history_info_row['adyen_date'].'</td>
                							<td class="smallText" nowrap>'.$adyen_payment_status_history_info_row['adyen_event_code'].'</td>
                							<td class="smallText" nowrap>'.$adyen_payment_status_history_info_row['adyen_reason'].'</td>
                							<td class="smallText" nowrap>'.(tep_not_null($adyen_payment_status_history_info_row['changed_by'])?$adyen_payment_status_history_info_row['changed_by']:'&nbsp;').'</td>
                						</tr>';
     	}
?>
									</table>
								</td>
        					</tr>
        				</table>
        			</td>
        			<td>
        				<table border="0" cellspacing="0" cellpadding="2">
        					<tr>
                				<td class="main" valign="top" nowrap><b><?php echo ENTRY_ADYEN_CURRENCY?></b>&nbsp;</td>
                				<td class="main" nowrap>
                				<?php
                					if (strtolower($adyen_trans_info_row['adyen_currency']) == strtolower($order->info['currency'])) {
                						echo $adyen_trans_info_row['adyen_currency'];
                					} else {
                						echo '<span class="redIndicator">'.$adyen_trans_info_row['adyen_currency'].'<br><span class="smallText">Does not match purchase currency.</span></span>';
                					}
                				?>
                				</td>
              				</tr>
              				<tr>
                				<td class="main" valign="top" nowrap><b><?php echo ENTRY_ADYEN_AMOUNT?></b>&nbsp;</td>
                				<td class="main">
                				<?php
									$adyenReturnAmt = $adyen_trans_info_row['adyen_amount'];
									if ($exponent > 0) {
										$adyenReturnAmt = ($adyenReturnAmt / pow(10, $exponent));
									}
									$adyenGrossAmt = number_format($adyenReturnAmt, $currencies->currencies[$order->info["currency"]]['decimal_places'], $currencies->currencies[$order->info["currency"]]['decimal_point'], $currencies->currencies[$order->info["currency"]]['thousands_point']);
									$adyenAmountFormatted = $currencies->currencies[$order->info["currency"]]['symbol_left'] . $adyenGrossAmt . $currencies->currencies[$order->info["currency"]]['symbol_right'];

									if ($adyenAmountFormatted != strip_tags($order->order_totals['ot_total']['text']) ) {
                						$mc_gross_display_text = '<span class="redIndicator">'.$adyenGrossAmt.'<br><span class="smallText">Does not match purchase amount.</span></span>';
                					} else {
										$mc_gross_display_text = $adyenGrossAmt;
									}
                					
                					echo $mc_gross_display_text;
                				?>
                				</td>
              				</tr>
              				<tr>
              					<td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
              				</tr>
              				<tr>
              					<td>
              						<table border="1" cellspacing="0" cellpadding="2">
              							<tr>
			                				<td class="main" valign="top" nowrap><b><?php echo ENTRY_ADYEN_CVC_RESULT?></b>&nbsp;</td>
			                				<td class="main"><?php echo (tep_not_null($adyen_trans_info_row['adyen_cc_cvc_result']) ? $adyen_trans_info_row['adyen_cc_cvc_result'] : TEXT_NOT_AVAILABLE)?></td>
			              				</tr>
              							<tr>
			                				<td class="main" valign="top" nowrap><b><?php echo ENTRY_ADYEN_AVS_RESULT?></b>&nbsp;</td>
			                				<td class="main"><?php echo (tep_not_null($adyen_trans_info_row['adyen_cc_avs_result']) ? $adyen_trans_info_row['adyen_cc_avs_result'] : TEXT_NOT_AVAILABLE)?></td>
			              				</tr>
			              				<tr>
			                				<td class="main" valign="top" nowrap><b><?php echo ENTRY_ADYEN_FRAUD_SCORE?></b>&nbsp;</td>
			                				<td class="main">
			                				<?php
			                					$risk_class = '';
			                					
			                					if ($adyen_trans_info_row['adyen_cc_fraud_score'] >= 50) {
			                						$risk_class = 'redIndicator';
			                					}
			                					
			                					echo (tep_not_null($adyen_trans_info_row['adyen_cc_fraud_score']) ? '<span class="'.$risk_class.'">' . $adyen_trans_info_row['adyen_cc_fraud_score'] . '</span>' : TEXT_NOT_AVAILABLE);
			                				?>
			                				</td>
			              				</tr>
              						</table>
              					</td>
              				</tr>
              			</table>
        			</td>
      			</tr>
      			<tr>
    				<td class="main" nowrap colspan="2" valign="top">
    					<table border="0" width="100%" cellspacing="0" cellpadding="2">
    						<tr>
            				<?php	//only take care of showing Post-Captured button if it is in Verifying and was Pre-Captured
            					if ($order->info['orders_status'] == '7' && $adyen_trans_info_row['adyen_event_code'] == 'AUTHORISATION') {
            						$button_displayed = false;
            						
            						$manual_authorise_permission = tep_admin_files_actions(FILENAME_ORDERS, 'ADM_WORLDPAY_MANUAL_AUTHORISATION');
            						echo '	<td width="140px" class="main">';
            						if ($manual_authorise_permission) {
                						echo tep_draw_form('manual_post_auth_order', FILENAME_ORDERS, tep_get_all_get_params(), 'post');
                						echo tep_draw_hidden_field('subaction', 'manual_post_authorisation');
                						echo '<input type="submit" name="ManualPostCaptureBtn" value="Manual Captured" title="Post Captured Manually" class="inputButton" onClick="if (confirm(\'Are you sure that you had post captured this order via the Adyen Merchant Interface?\')) { openNewWin(\'https://ca-live.adyen.com/ca/ca/login.shtml\', \'Adyen Admin\', \'location=1,status=1,menubar=1,resizable=1,directories=1,width=750,height=400\'); this.disabled=true;this.value=\'Please wait...\';this.form.submit(); } else { return false; }">';
                						echo '</form>';
                						$button_displayed = true;
                					}
            						echo '	</td>
											<td class="main">';
            						
        							$post_auth_btn_text = 'Post-Captured';
        							
        							if ($adyen_trans_info_row['adyen_capture_request'] == 1) {
        								$post_auth_btn_text .= ' (Awaiting for response)';
        							}
        							
            						echo tep_draw_form('post_capture_order', FILENAME_ORDERS, tep_get_all_get_params(), 'post');
									echo tep_draw_hidden_field('subaction', 'post_authorisation');
									echo '<input type="submit" name="PostCaptureBtn" value="'.$post_auth_btn_text.'" title="Post Authorise" class="inputButton" onClick="if (confirm(\'Are you sure to post captured this order remotely?\')) { this.disabled=true;this.value=\'Please wait...\';this.form.submit(); } else { return false; }">';
									echo "</form>";
									$button_displayed = true;
            						
            						echo '</td>';
								}
            				?>
    						</tr>
    					</table>
    				</td>
  				</tr>
      		</table>
   		</td>
  	</tr>
<?php
	}
} else {
	echo $payment_method_title_info;
}
?>
</table>