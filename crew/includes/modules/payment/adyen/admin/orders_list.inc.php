<?
include_once(DIR_FS_CATALOG_MODULES . 'payment/adyen/admin/languages/' . $language . '/adyen.lng.php');

$adyen_trans_info_select_sql = "SELECT * FROM " . TABLE_ADYEN . " WHERE adyen_order_id = '" . $order_obj->orders_id . "'";
$adyen_trans_info_result_sql = tep_db_query($adyen_trans_info_select_sql);
$payment_methods_obj = new adyen($order_obj->info['payment_methods_id']);
?>
<table border="0" width="100%" cellspacing="0" cellpadding="2">
	<tr>
		<td class="subInvoiceBoxHeading">
			<div style="border-bottom: 1px solid #996600; border-top: 1px solid #996600; padding: 2px;">Payment Details</div>
		</td>
	</tr>
	<?
	if ($adyen_trans_info_row = tep_db_fetch_array($adyen_trans_info_result_sql)) {
		$exponent = $payment_methods_obj->get_adyen_exponent($adyen_trans_info_row['adyen_currency']);
		?>
		<tr>
			<td class="main">
				<table border="0" width="100%" cellspacing="0" cellpadding="2">
					<tr valign="top">
						<td width="50%" valign="top">
							<table border="0" cellspacing="0" cellpadding="2">
								<tr>
									<td class="invoiceRecords" valign="top" nowrap><?= ENTRY_ADYEN_PAYMENT_TYPE ?></td>
									<td class="invoiceRecords" valign="top">:&nbsp;</td>
									<td class="invoiceRecords" nowrap><?= (tep_not_null($adyen_trans_info_row['adyen_payment_method']) ? $adyen_trans_info_row['adyen_payment_method'] : TEXT_NOT_AVAILABLE) ?></td>
								</tr>
								<tr>
									<td class="invoiceRecords" valign="top" nowrap><?= ENTRY_ADYEN_CURRENCY ?></td>
									<td class="invoiceRecords" valign="top">:&nbsp;</td>
									<td class="invoiceRecords" nowrap><?= $adyen_trans_info_row['adyen_currency'] ?></td>
								</tr>
								<tr>
									<td class="invoiceRecords" valign="top" nowrap><?= ENTRY_ADYEN_AMOUNT ?></td>
									<td class="invoiceRecords" valign="top">:&nbsp;</td>
									<td class="invoiceRecords">
										<?php
										$adyenReturnAmt = $adyen_trans_info_row['adyen_amount'];
										if ($exponent > 0) {
											$adyenReturnAmt = ($adyenReturnAmt / pow(10, $exponent));
										}
										$adyenGrossAmt = number_format($adyenReturnAmt, $currencies->currencies[$order_obj->info["currency"]]['decimal_places'], $currencies->currencies[$order_obj->info["currency"]]['decimal_point'], $currencies->currencies[$order_obj->info["currency"]]['thousands_point']);
										echo $adyenGrossAmt;
										?>

									</td>
								</tr>
								<tr>
									<td class="invoiceRecords" valign="top" nowrap><?= ENTRY_ADYEN_CVC_RESULT ?></td>
									<td class="invoiceRecords" valign="top">:&nbsp;</td>
									<td class="invoiceRecords"><?= (tep_not_null($adyen_trans_info_row['adyen_cc_cvc_result']) ? $adyen_trans_info_row['adyen_cc_cvc_result'] : TEXT_NOT_AVAILABLE) ?></td>
								</tr>
							</table>
						</td>
						<td valign="top">
							<table border="0" cellspacing="0" cellpadding="2">
								<tr>
									<td class="invoiceRecords" valign="top" nowrap><?= ENTRY_ADYEN_AVS_RESULT ?></td>
									<td class="invoiceRecords" valign="top">:&nbsp;</td>
									<td class="invoiceRecords"><?= (tep_not_null($adyen_trans_info_row['adyen_cc_avs_result']) ? $adyen_trans_info_row['adyen_cc_avs_result'] : TEXT_NOT_AVAILABLE) ?></td>
								</tr>
								<tr>
									<td class="invoiceRecords" valign="top" nowrap><?= ENTRY_ADYEN_FRAUD_SCORE ?></td>
									<td class="invoiceRecords" valign="top">:&nbsp;</td>
									<td class="invoiceRecords"><?= (tep_not_null($adyen_trans_info_row['adyen_cc_fraud_score']) ? $adyen_trans_info_row['adyen_cc_fraud_score'] : TEXT_NOT_AVAILABLE) ?></td>
								</tr>
								<tr>
									<td class="invoiceRecords" valign="top" nowrap><?= ENTRY_BB_PAYMENT_STATUS ?></td>
									<td class="invoiceRecords" valign="top">:&nbsp;</td>
									<td class="invoiceRecords"><?= (tep_not_null($adyen_trans_info_row['adyen_success']) && $adyen_trans_info_row['adyen_success'] == 'true' ? TEXT_STATUS_SUCCESS : TEXT_STATUS_FAILED) ?></td>
								</tr>
							</table>
						</td>
					</tr>
				</table>
			</td>
		</tr>
		<?
	} else {
		?>
		<tr>
			<td class="invoiceRecords">No further payment information is available.</td>
		</tr>
		<?
	}
	?>