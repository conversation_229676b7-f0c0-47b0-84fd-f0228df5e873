<?php

/*
  $Id: alipay.php, v1.0 2005/11/29 18:08:05
  Author : <PERSON><PERSON> (<EMAIL>)
  Title: Alipay Payment Module V1.0

  Revisions:
  Version 1.0 Initial Payment Module

  Copyright (c) 2005 SKC Venture

  Released under the GNU General Public License
 */

class alipay {

    var $code, $title, $description, $enabled, $alipayCurrencies;
    var $defCurr, $amount_decimal, $form_action_url, $check_trans_status_flag;
    var $partner_id, $security_code;

    function alipay($pm_id = 0) {
        global $order, $languages_id, $default_languages_id;

        //Basic info
        $this->payment_methods_id = 0;
        $this->payment_methods_parent_id = 0;
        $this->code = 'alipay';
        $this->filename = 'alipay.php';
        $this->title = $this->code;
        $this->preferred = true;

        $this->check_trans_status_flag = false;

        $this->form_action_url = 'https://mapi.alipay.com/gateway.do';
        $this->force_to_checkoutprocess = true;
        $this->has_ipn = true;
        $this->order_processing_status = 7;
        $this->auto_cancel_period = 60; // In minutes
        $this->connect_via_proxy = false;
        $this->amount_decimal = 2;

        $payment_methods_select_sql = "	SELECT payment_methods_parent_id, payment_methods_code, 
												payment_methods_types_id, 
												payment_methods_receive_status_mode, payment_methods_id, 
												payment_methods_title, payment_methods_sort_order, 
												payment_methods_receive_status, payment_methods_legend_color, 
												payment_methods_logo 
										FROM " . TABLE_PAYMENT_METHODS . " 
										WHERE 1 ";
        if ((int) $pm_id > 0) {
            $payment_methods_select_sql .= "	AND payment_methods_id = '" . (int) $pm_id . "'";
        } else {
            $payment_methods_select_sql .= "	AND payment_methods_filename = '" . tep_db_input($this->filename) . "'";
        }

        $payment_methods_result_sql = tep_db_query($payment_methods_select_sql);
        if ($payment_methods_row = tep_db_fetch_array($payment_methods_result_sql)) {
            $pm_desc_title_select_sql = "	SELECT payment_methods_description_title 
											FROM " . TABLE_PAYMENT_METHODS_DESCRIPTION . " 
											WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "' 
												AND (IF (languages_id = '" . tep_db_input($languages_id) . "', 1, IF ((	SELECT COUNT(payment_methods_id) > 0 
																														FROM " . TABLE_PAYMENT_METHODS_DESCRIPTION . " 
																														WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "' 
																															AND languages_id = '" . (int) $languages_id . "'), 0, languages_id = 1)))";
            $pm_desc_title_result_sql = tep_db_query($pm_desc_title_select_sql);
            $pm_desc_title_row = tep_db_fetch_array($pm_desc_title_result_sql);

            $this->payment_methods_id = (int) $payment_methods_row['payment_methods_id'];
            $this->payment_methods_parent_id = (int) $payment_methods_row['payment_methods_parent_id'];
            $this->code = $payment_methods_row['payment_methods_code'];
            $this->title = $payment_methods_row['payment_methods_title'];
            $this->display_title = $pm_desc_title_row['payment_methods_description_title'];
            $this->sort_order = $payment_methods_row['payment_methods_sort_order'];
            $this->enabled = $payment_methods_row['payment_methods_receive_status_mode'];
            $this->legend_display_colour = $payment_methods_row['payment_methods_legend_color'];
            $this->receive_status = $payment_methods_row['payment_methods_receive_status'];
            $this->description = $this->display_title;

            $this->alipayCurrencies = $this->get_support_currencies($this->payment_methods_id);

            $configuration_setting_array = $this->load_pm_setting();

            $this->paymethod = $configuration_setting_array['MODULE_PAYMENT_ALIPAY_PAYMETHOD'];
            $this->payment_type = $configuration_setting_array['MODULE_PAYMENT_ALIPAY_PAYMENT_TYPE'];
            $this->service = $configuration_setting_array['MODULE_PAYMENT_ALIPAY_SERVICE'];
            $this->sign_type = $configuration_setting_array['MODULE_PAYMENT_ALIPAY_SIGN_TYPE'];
            $this->order_status_id = $configuration_setting_array['MODULE_PAYMENT_ALIPAY_ORDER_STATUS_ID'];

            $this->processing_status_id = $configuration_setting_array['MODULE_PAYMENT_ALIPAY_PROCESSING_STATUS_ID'];
            $this->message = $configuration_setting_array['MODULE_PAYMENT_ALIPAY_MESSAGE'];
            $this->email_message = $configuration_setting_array['MODULE_PAYMENT_ALIPAY_EMAIL_MESSAGE'];
            $this->confirm_complete_days = $configuration_setting_array['MODULE_PAYMENT_ALIPAY_CONFIRM_COMPLETE'];
            $this->input_charset = $configuration_setting_array['MODULE_PAYMENT_ALIPAY_INPUT_CHARSET'];

            $this->require_address_information = $configuration_setting_array['MODULE_PAYMENT_ALIPAY_MANDATORY_ADDRESS_FIELD'];
            $this->require_ic_information = $configuration_setting_array['MODULE_PAYMENT_ALIPAY_MANDATORY_IC_FIELD'];
            $this->require_contact_information = $configuration_setting_array['MODULE_PAYMENT_ALIPAY_MANDATORY_CONTACT_FIELD'];
            $this->customer_payment_info = $configuration_setting_array['MODULE_PAYMENT_ALIPAY_CUSTOMER_PAYMENT_INFO'];

            if ((int) $this->order_status_id > 0) {
                $this->order_status = $this->order_status_id;
            }

            $this->email_footer = str_replace(array("<br>", "<BR>", "\r\n"), array("\n", "\n", ''), $this->email_message);
            $this->order_processing_status = (int) $this->processing_status_id > 0 ? $this->processing_status_id : 7;


            $this->defCurr = DEFAULT_CURRENCY;

            if ((int) $this->order_status_id > 0) {
                $this->order_status = $this->order_status_id;
            } else {
                $this->order_status = DEFAULT_ORDERS_STATUS_ID;
            }

            $this->email_footer = str_replace(array("<br>", "<BR>", "\r\n"), array("\n", "\n", ''), $this->email_message);
        }
    }

    function javascript_validation() {
        return false;
    }

    function selection() {
        global $languages_id, $default_languages_id;

        $selection_array = array();

        $payment_methods_select_sql = "	SELECT payment_methods_id, payment_methods_code, payment_methods_types_id, payment_methods_logo, payment_methods_receive_status_mode 
										FROM " . TABLE_PAYMENT_METHODS . " 
										WHERE payment_methods_receive_status = 1 
											AND payment_methods_receive_status_mode <> 0 
											AND payment_methods_parent_id = '" . (int) $this->payment_methods_id . "' 
										ORDER BY payment_methods_sort_order";
        $payment_methods_result_sql = tep_db_query($payment_methods_select_sql);
        while ($payment_methods_row = tep_db_fetch_array($payment_methods_result_sql)) {
            $pm_desc_title_select_sql = "	SELECT payment_methods_description_title 
											FROM " . TABLE_PAYMENT_METHODS_DESCRIPTION . " 
											WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "' 
												AND (IF (languages_id = '" . tep_db_input($languages_id) . "', 1, IF ((	SELECT COUNT(payment_methods_id) > 0 
																														FROM " . TABLE_PAYMENT_METHODS_DESCRIPTION . " 
																														WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "' 
																															AND languages_id = '" . (int) $languages_id . "'), 0, languages_id = 1)))";
            $pm_desc_title_result_sql = tep_db_query($pm_desc_title_select_sql);
            $pm_desc_title_row = tep_db_fetch_array($pm_desc_title_result_sql);

            $pm_array = $this->load_pm_setting(1, $payment_methods_row['payment_methods_id']);

            $selection_array[] = array('payment_gateway_code' => $this->code,
                'payment_methods_receive_status_mode' => $payment_methods_row['payment_methods_receive_status_mode'],
                'payment_methods_id' => $payment_methods_row['payment_methods_id'],
                'payment_methods_code' => $payment_methods_row['payment_methods_code'],
                'payment_methods_types_id' => $payment_methods_row['payment_methods_types_id'],
                'payment_methods_parent_id' => $this->payment_methods_id,
                'payment_methods_logo' => $payment_methods_row['payment_methods_logo'],
                'payment_methods_description_title' => $pm_desc_title_row['payment_methods_description_title'],
                'currency' => $this->get_support_currencies($payment_methods_row['payment_methods_id']),
                'show_billing_address' => $pm_array['MODULE_PAYMENT_ALIPAY_MANDATORY_ADDRESS_FIELD'],
                'confirm_complete_days' => $pm_array['MODULE_PAYMENT_ALIPAY_CONFIRM_COMPLETE'],
                'show_contact_number' => $pm_array['MODULE_PAYMENT_ALIPAY_MANDATORY_CONTACT_FIELD'],
                'show_ic' => $pm_array['MODULE_PAYMENT_ALIPAY_MANDATORY_IC_FIELD']
            );

            if ($payment_methods_row['payment_methods_receive_status_mode'] == '-1') { // Inactive
                $pm_status_message_select = "	SELECT payment_methods_status_message 
												FROM " . TABLE_PAYMENT_METHODS_STATUS_DESCRIPTION . " 
												WHERE payment_methods_mode = 'RECEIVE' 
													AND payment_methods_status = '-1' 
													AND languages_id = '" . (int) $languages_id . "' 
													AND payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "'";
                $pm_status_message_result = tep_db_query($pm_status_message_select);
                $pm_status_message_row = tep_db_fetch_array($pm_status_message_result);
                $selection_array[sizeof($selection_array) - 1]['payment_methods_status_message'] = $pm_status_message_row['payment_methods_status_message'];
            }
        }

        return $selection_array;
    }

    function get_support_currencies($pm_id, $by_zone = true) {
        global $zone_info_array;

        $default_currency = '';
        $support_currencies_array = array();

        $currency_code_select_sql = "	SELECT currency_code, payment_methods_instance_default 
    									FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " 
    									WHERE payment_methods_id = '" . (int) $pm_id . "' 
    									ORDER BY currency_code";
        $currency_code_result_sql = tep_db_query($currency_code_select_sql);

        if (tep_db_num_rows($currency_code_result_sql) > 0) {
            while ($currency_code_row = tep_db_fetch_array($currency_code_result_sql)) {
                $support_currencies_array[] = $currency_code_row['currency_code'];

                if ($currency_code_row['payment_methods_instance_default'] == 1) {
                    $default_currency = $currency_code_row['currency_code'];
                }
            }
        } else {
            if ($this->payment_methods_parent_id > 0) {
                $payment_methods_parent_id = $this->payment_methods_parent_id;
            } else {
                $payment_methods_parent_id = $this->payment_methods_id;
            }

            $parent_currency_code_select_sql = "	SELECT currency_code, payment_methods_instance_default 
			    									FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " 
			    									WHERE payment_methods_id = '" . (int) $payment_methods_parent_id . "' 
			    									ORDER BY currency_code";
            $parent_currency_code_result_sql = tep_db_query($parent_currency_code_select_sql);
            while ($parent_currency_code_row = tep_db_fetch_array($parent_currency_code_result_sql)) {
                $support_currencies_array[] = $parent_currency_code_row['currency_code'];

                if ($parent_currency_code_row['payment_methods_instance_default'] == 1) {
                    $default_currency = $parent_currency_code_row['currency_code'];
                }
            }
        }

        if (tep_not_null($default_currency)) {
            $this->defCurr = $default_currency;
        }

        if ($by_zone) {
            if (isset($zone_info_array)) {
                $support_currencies_array = array_intersect($zone_info_array[3]->zone_currency_id, $support_currencies_array);
                if (count($support_currencies_array)) {
                    array_multisort($support_currencies_array);
                } else {
                    $support_currencies_array = array($default_currency);
                }
            } else {
                $support_currencies_array = array($default_currency);
            }
        } else {
            $this->set_support_currencies($support_currencies_array);
            // All found currencies is supported. Normally this is used for backend script such as IPN call
        }

        return $support_currencies_array;
    }

    function get_require_address_information() {
        return $this->require_address_information;
    }

    function get_confirm_complete_days() {
        return $this->confirm_complete_days;
    }

    function get_pm_info() {
        return $this->message;
    }

    function get_email_pm_info() {
        return $this->email_message;
    }

    function draw_confirm_button() {
        return tep_image_button2('red', 'javascript:document.checkout_confirmation.submit()', MODULE_PAYMENT_ALIPAY_TEXT_CART, 200);
    }

    function get_confirm_button_caption() {
        return MODULE_PAYMENT_ALIPAY_TEXT_TITLE;
    }

    function pre_confirmation_check() {
        global $currency, $currencies, $messageStack;
        $supported_currency_str_array = array();

        if (!in_array($currency, $this->alipayCurrencies)) {
            
        }
    }

    function confirmation() {
        global $HTTP_POST_VARS;

        return array('title' => $this->message);
    }

    function process_button() {
        global $HTTP_POST_VARS, $shipping_cost, $total_cost, $shipping_selected, $shipping_method;
        global $currencies, $currency, $order, $languages_id;
        global $order_logged;

        if ($this->force_to_checkoutprocess && !tep_session_is_registered('order_logged'))
            return;

        /*         * ****************************************************************************
          Multi Currency
          Convert to USD currency
         * **************************************************************************** */
        $alipayCurrencies = $currency;
        if (!in_array($alipayCurrencies, $this->alipayCurrencies)) {
            $alipayCurrencies = $this->alipayCurrencies[0];
        }
        $this->get_merchant_account($alipayCurrencies);

        // Anti phishing
        $anti_phishing_response = $this->curl_connect($this->form_action_url . "?service=query_timestamp&partner=" . $this->partner_id);

        require_once(DIR_WS_CLASSES . 'ogm_xml_to_ary.php');
        $xml_array_obj = new ogm_xml_to_ary($anti_phishing_response, 'content');
        $xml_data_array = $xml_array_obj->get_ogm_xml_to_ary();

        $OrderAmt = number_format($order->info['total'] * $currencies->get_value($alipayCurrencies), $currencies->get_decimal_places($alipayCurrencies), '.', '');

        $alipay_post_array = array();

        $alipay_post_array['service'] = $this->service;
        $alipay_post_array['partner'] = $this->partner_id;
        $alipay_post_array['notify_url'] = tep_href_link(FILENAME_ALIPAY_IPN);
        $alipay_post_array['return_url'] = tep_href_link(FILENAME_CHECKOUT_PROCESS);
        $alipay_post_array['subject'] = 'Purchase from ' . STORE_NAME;

        $alipay_post_array['body'] = '';
        $alipay_post_array['out_trade_no'] = $order_logged;
        $alipay_post_array['total_fee'] = $OrderAmt;
        $alipay_post_array['payment_type'] = $this->payment_type;
        $alipay_post_array['paymethod'] = $this->paymethod;

        $alipay_post_array['defaultbank'] = (strtolower(trim($this->code)) == '#all#' ? '' : $this->code);
        $alipay_post_array['_input_charset'] = $this->input_charset;
        $alipay_post_array['seller_email'] = $this->seller_email;
        $alipay_post_array['buyer_email'] = '';
        $alipay_post_array['buyer_msg'] = '';

        $alipay_post_array['anti_phishing_key'] = $xml_data_array['alipay']['_c']['response']['_c']['timestamp']['_c']['encrypt_key']['_v'];
        $alipay_post_array['exter_invoke_ip'] = tep_get_ip_address();

        require_once(DIR_WS_MODULES . 'payment/alipay/alipay_service.php');

        $alipay_service = new alipay_service($alipay_post_array, $this->security_code);
        $alipay_post_array['sign'] = $alipay_service->get_sign();
        $alipay_post_array['sign_type'] = $this->sign_type;
        $sort_array = $alipay_service->arg_sort($alipay_post_array);

        $process_button_string = '';
        foreach ($alipay_post_array as $alipay_post_key_loop => $alipay_post_value_loop) {
            if (tep_not_null($alipay_post_value_loop)) {
                $process_button_string .= tep_draw_hidden_field($alipay_post_key_loop, $alipay_post_value_loop);
            }
        }

        return $process_button_string;
    }

    function before_process() {
        global $HTTP_POST_VARS, $order, $order_logged, $currency;

        if (!tep_session_is_registered('order_logged')) {
            return;
        } else {
            require_once(DIR_WS_MODULES . 'payment/alipay/alipay_notify.php');

            $alipayCurrencies = $order->info['currency'];
            if (!in_array($alipayCurrencies, $this->alipayCurrencies)) {
                $alipayCurrencies = $this->alipayCurrencies[0];
            }
            $this->get_merchant_account($alipayCurrencies);

            $alipay = new alipay_notify($this->partner_id, $this->security_code, $this->sign_type, $this->input_charset, 'http');
            $verify_result = $alipay->return_verify();

            if (!$verify_result) {
                tep_redirect(tep_href_link(FILENAME_CHECKOUT_PAYMENT, 'error_message=' . urlencode((isset($_REQUEST['error']) && tep_not_null($_REQUEST['error']) ? $_REQUEST['error'] : MODULE_PAYMENT_ALIPAY_TEXT_ERROR_MESSAGE)), 'SSL', true, false));
            }
        }
    }

    function after_process() {
        return false;
    }

    function link_to_payment($new_order_id) {
        global $order_logged;

        if (tep_session_is_registered('order_logged'))
            tep_session_unregister('order_logged');

        tep_session_register('order_logged');
        $order_logged = $new_order_id;
        require(DIR_WS_INCLUDES . 'modules/payment/alipay/catalog/alipay_splash.php');
        return;
    }

    function is_supported_currency($selected_currency) {
        return (in_array($selected_currency, $this->alipayCurrencies) ? true : false);
    }

    function output_error() {
        return false;
    }

    function check() {
        if (!isset($this->_check)) {
            $payment_methods_select_sql = "	SELECT payment_methods_id
											FROM " . TABLE_PAYMENT_METHODS . " as pm
											WHERE pm.payment_methods_id = '" . (int) $this->payment_methods_id . "'";
            $payment_methods_result_sql = tep_db_query($payment_methods_select_sql);
            $this->_check = tep_db_num_rows($payment_methods_result_sql);
        }
        return $this->_check;
    }

    function check_trans_status($order_id) {
        $result_array = array('');
        $order_info_select_sql = "	SELECT o.payment_methods_id, o.currency 
									FROM " . TABLE_ORDERS . " AS o 
									WHERE o.orders_id = '" . tep_db_input($order_id) . "'";
        $order_info_result_sql = tep_db_query($order_info_select_sql);
        if ($order_info_row = tep_db_fetch_array($order_info_result_sql)) {
            if ($order_info_row['payment_methods_id'] != $this->payment_methods_id)
                return;

            $this->get_merchant_account($order_info_row['currency']);

            $parameter = array("service" => "single_trade_query",
                "partner" => $this->partner_id,
                "_input_charset" => $this->input_charset,
                "out_trade_no" => $order_id
            );

            require_once(DIR_FS_CATALOG_MODULES . 'payment/alipay/alipay_service.php');

            $alipay_obj = new alipay_service($parameter, $this->security_code, $this->sign_type);
            $alipay_sign = $alipay_obj->get_sign();

            $response = $this->curl_connect($this->form_action_url . "?_input_charset=" . $this->input_charset . "&out_trade_no=" . $order_id . "&partner=" . $this->partner_id . "&service=single_trade_query&sign=" . $alipay_sign . "&sign_type=" . $this->sign_type);

            require_once(DIR_WS_CLASSES . 'ogm_xml_to_ary.php');
            $xml_array_obj = new ogm_xml_to_ary($response, 'content');
            $xml_data_array = $xml_array_obj->get_ogm_xml_to_ary();

            if (isset($xml_data_array['alipay']['_c']['is_success']['_v']) && strtoupper($xml_data_array['alipay']['_c']['is_success']['_v']) == 'T') {
                if ($xml_data_array['alipay']['_c']['response']['_c']['trade']['_c']['trade_status']['_v'] == 'TRADE_SUCCESS' || $xml_data_array['alipay']['_c']['response']['_c']['trade']['_c']['trade_status']['_v'] == 'TRADE_FINISHED') {
                    $this->check_trans_status_flag = true;
                }

                $alipay_payment_status_array = array('WAIT_BUYER_PAY' => 'Error: Pending',
                    'TRADE_FINISHED' => 'Successful',
                    'TRADE_SUCCESS' => 'Successful',
                    'TRADE_CLOSED' => 'Error: Unsuccessful');

                $alipay_payment_history_data_array = array('alipay_orders_id' => (int) $order_id,
                    'alipay_date' => 'now()',
                    'alipay_status' => trim($xml_data_array['alipay']['_c']['response']['_c']['trade']['_c']['trade_status']['_v']),
                    'alipay_description' => (isset($alipay_payment_status_array[strtoupper(trim($xml_data_array['alipay']['_c']['response']['_c']['trade']['_c']['trade_status']['_v']))]) ? $alipay_payment_status_array[strtoupper(trim($xml_data_array['alipay']['_c']['response']['_c']['trade']['_c']['trade_status']['_v']))] : 'Error: Unknown')
                );
                tep_db_perform(TABLE_ALIPAY_STATUS_HISTORY, $alipay_payment_history_data_array);
            } else {
                $alipay_history_data_array = array('alipay_orders_id' => $order_id,
                    'alipay_date' => 'now()',
                    'alipay_status' => 'TRADE_CLOSED',
                    'alipay_description' => 'Unknown - check transaction status failed');
                tep_db_perform(TABLE_ALIPAY_STATUS_HISTORY, $alipay_history_data_array);
            }
        }

        return $result_array;
    }

    function load_pm_setting($language_id = 1, $pm_id = '') {
        $pm_setting_array = array();
        //load value from DB

        if (tep_not_null($pm_id)) {
            $payment_method_id = $pm_id;

            $payment_methods_parent_id_select_sql = "	SELECT payment_methods_parent_id 
														FROM " . TABLE_PAYMENT_METHODS . " 
														WHERE payment_methods_id = '" . (int) $pm_id . "'";
            $payment_methods_parent_id_result_sql = tep_db_query($payment_methods_parent_id_select_sql);
            $payment_methods_parent_id_row = tep_db_fetch_array($payment_methods_parent_id_result_sql);

            $payment_gateway_id = $payment_methods_parent_id_row['payment_methods_parent_id'];
        } else {
            $payment_method_id = $this->payment_methods_id;
            $payment_gateway_id = $this->payment_methods_parent_id;
        }

        $payment_configuration_info_select_sql = "	SELECT pci.payment_configuration_info_key, pcid.payment_configuration_info_value
													FROM " . TABLE_PAYMENT_CONFIGURATION_INFO . " as pci
													LEFT JOIN " . TABLE_PAYMENT_CONFIGURATION_INFO_DESCRIPTION . " as pcid 
														ON pci.payment_configuration_info_id = pcid.payment_configuration_info_id
															AND pcid.languages_id = '" . (int) $language_id . "'
													WHERE pci.payment_methods_id = '" . (int) $payment_method_id . "'
													ORDER BY pci.payment_configuration_info_sort_order";
        $payment_configuration_info_result_sql = tep_db_query($payment_configuration_info_select_sql);
        if (tep_db_num_rows($payment_configuration_info_result_sql)) {
            while ($payment_configuration_info_row = tep_db_fetch_array($payment_configuration_info_result_sql)) {
                $pm_setting_array[$payment_configuration_info_row['payment_configuration_info_key']] = $payment_configuration_info_row['payment_configuration_info_value'];
            }
        } else if ((int) $payment_gateway_id > 0) { //load value from PG
            $payment_configuration_info_select_sql = "	SELECT pci.payment_configuration_info_key, pcid.payment_configuration_info_value
														FROM " . TABLE_PAYMENT_CONFIGURATION_INFO . " as pci
														LEFT JOIN " . TABLE_PAYMENT_CONFIGURATION_INFO_DESCRIPTION . " as pcid 
															ON pci.payment_configuration_info_id = pcid.payment_configuration_info_id
																AND pcid.languages_id = '" . (int) $language_id . "'
														WHERE pci.payment_methods_id = '" . (int) $payment_gateway_id . "'
														ORDER BY pci.payment_configuration_info_sort_order";
            $payment_configuration_info_result_sql = tep_db_query($payment_configuration_info_select_sql);
            while ($payment_configuration_info_row = tep_db_fetch_array($payment_configuration_info_result_sql)) {
                $pm_setting_array[$payment_configuration_info_row['payment_configuration_info_key']] = $payment_configuration_info_row['payment_configuration_info_value'];
            }
        }

        //static value
        $pm_setting_array['MODULE_PAYMENT_ALIPAY_PAYMENT_TYPE'] = "1";
        $pm_setting_array['MODULE_PAYMENT_ALIPAY_SERVICE'] = "create_direct_pay_by_user";
        $pm_setting_array['MODULE_PAYMENT_ALIPAY_SIGN_TYPE'] = "MD5";
        $pm_setting_array['MODULE_PAYMENT_ALIPAY_INPUT_CHARSET'] = "GBK";

        return $pm_setting_array;
    }

    function get_merchant_account($selected_currency) {
        $payment_methods_instance_id = 0;
        $payment_gateway_instance_select_sql = "SELECT pmi.payment_methods_instance_id, pmi.payment_methods_instance_follow_default, pmi.currency_code 
				 								FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " as pmi
				 								WHERE pmi.payment_methods_id = '" . (int) $this->payment_methods_id . "'
				 								LIMIT 1";
        $payment_gateway_instance_result_sql = tep_db_query($payment_gateway_instance_select_sql);
        if (tep_db_num_rows($payment_gateway_instance_result_sql)) {
            $payment_gateway_instance_select_sql = "SELECT pmi.payment_methods_instance_id, pmi.payment_methods_instance_follow_default 
					 								FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " as pmi
					 								WHERE pmi.currency_code = '" . tep_db_input($selected_currency) . "' 
					 									AND pmi.payment_methods_id = '" . (int) $this->payment_methods_id . "'";
            $payment_gateway_instance_result_sql = tep_db_query($payment_gateway_instance_select_sql);
            if ($payment_gateway_instance_row = tep_db_fetch_array($payment_gateway_instance_result_sql)) {
                if ((int) $payment_gateway_instance_row['payment_methods_instance_follow_default'] > 0) { // Follow Default Currency Setting
                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_follow_default'];
                } else { // Has own setting
                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_id'];
                }
            }
        } else {
            $payment_gateway_instance_select_sql = "SELECT pmi.payment_methods_instance_id, pmi.payment_methods_instance_follow_default 
					 								FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " as pmi
					 								INNER JOIN " . TABLE_PAYMENT_METHODS . " as pm
					 									ON pm.payment_methods_parent_id = pmi.payment_methods_id
					 								WHERE pmi.currency_code = '" . tep_db_input($selected_currency) . "' 
					 									AND pm.payment_methods_id = '" . (int) $this->payment_methods_id . "'";
            $payment_gateway_instance_result_sql = tep_db_query($payment_gateway_instance_select_sql);
            if ($payment_gateway_instance_row = tep_db_fetch_array($payment_gateway_instance_result_sql)) {
                if ((int) $payment_gateway_instance_row['payment_methods_instance_follow_default'] > 0) {
                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_follow_default'];
                } else {
                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_id'];
                }
            }
        }
        $payment_gateway_instance_setting_select_sql = "SELECT pmis.payment_methods_instance_setting_key, pmis.payment_methods_instance_setting_value 
						 								FROM " . TABLE_PAYMENT_METHODS_INSTANCE_SETTING . " as pmis
						 								WHERE pmis.payment_methods_instance_id = '" . (int) $payment_methods_instance_id . "'";
        $payment_gateway_instance_setting_result_sql = tep_db_query($payment_gateway_instance_setting_select_sql);
        while ($payment_gateway_instance_setting_row = tep_db_fetch_array($payment_gateway_instance_setting_result_sql)) {
            switch ($payment_gateway_instance_setting_row['payment_methods_instance_setting_key']) {
                case 'MODULE_PAYMENT_ALIPAY_SELLER_EMAIL':
                    $this->seller_email = $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];
                    break;
                case 'MODULE_PAYMENT_ALIPAY_PARTNER_ID':
                    $this->partner_id = $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];
                    break;
                case 'MODULE_PAYMENT_ALIPAY_SECURITY_CODE':
                    $this->security_code = $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];
                    break;
            }
        }
    }

    function curl_connect($url, $data = '') {

        $ch = curl_init($url);

        $agent = "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.0)";

        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_VERBOSE, 1);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 2);
        if (tep_not_null($data)) {
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, trim($data));
        }
        curl_setopt($ch, CURLOPT_TIMEOUT, 120);
        curl_setopt($ch, CURLOPT_USERAGENT, $agent);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);  //Windows 2003 Compatibility

        if ($this->connect_via_proxy) {
            curl_setopt($ch, CURLOPT_PROXY, 'http://*************:3128');
            curl_setopt($ch, CURLOPT_PROXYUSERPWD, 'devbackend:devonly');
        }
        $response = curl_exec($ch);
        return $response;
    }

    function install() {
        $install_configuration_flag = true;

        if ($this->payment_methods_id > 0) {
            if ($this->receive_status != 1) {
                $update_receive_status_sql_data = array("payment_methods_receive_status" => 1);
                tep_db_perform(TABLE_PAYMENT_METHODS, $update_receive_status_sql_data, 'update', "payment_methods_id = '" . (int) $this->payment_methods_id . "'");
            } else {
                $install_configuration_flag = false;
            }
        } else {
            $install_payment_gateway_data_sql = array('payment_methods_code' => $this->code,
                'payment_methods_send_status' => 0,
                'payment_methods_title' => $this->code,
                'payment_methods_types_id' => 0,
                'payment_methods_parent_id' => 0,
                'payment_methods_legend_color' => '#6E920D',
                'payment_methods_sort_order' => 6,
                'payment_methods_filename' => $this->filename,
                'payment_methods_logo' => '',
                'date_added' => 'now()',
                'last_modified' => 'now()',
                'payment_methods_receive_status' => 1,
                'payment_methods_receive_status_mode' => 0);
            tep_db_perform(TABLE_PAYMENT_METHODS, $install_payment_gateway_data_sql);
            $this->payment_methods_id = tep_db_insert_id();
        }

        if ($install_configuration_flag) {
            $install_key_array = array();
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Set Order Status',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_ALIPAY_ORDER_STATUS_ID',
                    'payment_configuration_info_description' => 'Set the status of orders made with this payment module to this value',
                    'payment_configuration_info_sort_order' => '100',
                    'set_function' => 'tep_cfg_pull_down_order_statuses(',
                    'use_function' => 'tep_get_order_status_name',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => '1',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Set Order\'s "Processing" Status',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_ALIPAY_PROCESSING_STATUS_ID',
                    'payment_configuration_info_description' => 'Set the initial processing status of orders made with this payment module to this value',
                    'payment_configuration_info_sort_order' => '200',
                    'set_function' => 'tep_cfg_pull_down_order_statuses(',
                    'use_function' => 'tep_get_order_status_name',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => '7',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Payment Message',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_ALIPAY_MESSAGE',
                    'payment_configuration_info_description' => 'Payment message will show up during checkout process (Alipay)',
                    'payment_configuration_info_sort_order' => '300',
                    'set_function' => 'tep_cfg_textarea(',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'Thank you for shopping at OffGamers.com.',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Payment Email Message',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_ALIPAY_EMAIL_MESSAGE',
                    'payment_configuration_info_description' => 'This message will show up in order confirmation e-mail.',
                    'payment_configuration_info_sort_order' => '400',
                    'set_function' => 'tep_cfg_textarea(',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'Thank you for shopping at OffGamers.com.',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Confirm Complete (in days)',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_ALIPAY_CONFIRM_COMPLETE',
                    'payment_configuration_info_description' => 'The number of days in which an order made by this payment method is confirm completed',
                    'payment_configuration_info_sort_order' => '500',
                    'set_function' => 'NULL',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => '0',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Pay Method',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_ALIPAY_PAYMETHOD',
                    'payment_configuration_info_description' => 'Pay Method:',
                    'payment_configuration_info_sort_order' => '600',
                    'set_function' => 'tep_cfg_key_select_option(array(\'bankPay\'=>\'bankPay\', \'2\'=>\'cartoon\', \'directPay\'=>\'directPay\'), ',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'bankPay',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Require Address Information',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_ALIPAY_MANDATORY_ADDRESS_FIELD',
                    'payment_configuration_info_description' => 'Set address field as required info during checkout.',
                    'payment_configuration_info_sort_order' => '1400',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Customer Payment Info',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_ALIPAY_CUSTOMER_PAYMENT_INFO',
                    'payment_configuration_info_description' => 'Set to true if this Payment Method required customer input for Payment Information required customer payment info.',
                    'payment_configuration_info_sort_order' => '1410',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Require IC Information',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_ALIPAY_MANDATORY_IC_FIELD',
                    'payment_configuration_info_description' => 'Set IC field as required info during checkout.',
                    'payment_configuration_info_sort_order' => '1420',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Require Contact Information',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_ALIPAY_MANDATORY_CONTACT_FIELD',
                    'payment_configuration_info_description' => 'Set Contact field as required info during checkout.',
                    'payment_configuration_info_sort_order' => '1430',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );
            foreach ($install_key_array as $data) {
                tep_db_perform(TABLE_PAYMENT_CONFIGURATION_INFO, $data['info']);
                $payment_conf_id = tep_db_insert_id();

                $data['desc']['payment_configuration_info_id'] = (int) $payment_conf_id;
                tep_db_perform(TABLE_PAYMENT_CONFIGURATION_INFO_DESCRIPTION, $data['desc']);
            }
        }
        return 1;
    }

    function remove() {
        tep_db_query("delete from " . TABLE_CONFIGURATION . " where configuration_key in ('" . implode("', '", $this->keys()) . "')");
    }

    function keys() {
        return array('MODULE_PAYMENT_ALIPAY_ORDER_STATUS_ID',
            'MODULE_PAYMENT_ALIPAY_PROCESSING_STATUS_ID',
            'MODULE_PAYMENT_ALIPAY_CONFIRM_COMPLETE',
            'MODULE_PAYMENT_ALIPAY_PAYMETHOD',
            'MODULE_PAYMENT_ALIPAY_MANDATORY_ADDRESS_FIELD',
            'MODULE_PAYMENT_ALIPAY_CUSTOMER_PAYMENT_INFO',
            'MODULE_PAYMENT_ALIPAY_MANDATORY_IC_FIELD',
            'MODULE_PAYMENT_ALIPAY_MANDATORY_CONTACT_FIELD',
            'MODULE_PAYMENT_ALIPAY_PAYMENT_TYPE',
            'MODULE_PAYMENT_ALIPAY_SERVICE',
            'MODULE_PAYMENT_ALIPAY_SIGN_TYPE'
        );
    }

    function configuration_information_keys() {
        return array('MODULE_PAYMENT_ALIPAY_ORDER_STATUS_ID',
            'MODULE_PAYMENT_ALIPAY_PROCESSING_STATUS_ID',
            'MODULE_PAYMENT_ALIPAY_CONFIRM_COMPLETE',
            'MODULE_PAYMENT_ALIPAY_PAYMETHOD',
            'MODULE_PAYMENT_ALIPAY_MANDATORY_ADDRESS_FIELD',
            'MODULE_PAYMENT_ALIPAY_CUSTOMER_PAYMENT_INFO',
            'MODULE_PAYMENT_ALIPAY_MANDATORY_IC_FIELD',
            'MODULE_PAYMENT_ALIPAY_MANDATORY_CONTACT_FIELD',
            'MODULE_PAYMENT_ALIPAY_PAYMENT_TYPE',
            'MODULE_PAYMENT_ALIPAY_SERVICE',
            'MODULE_PAYMENT_ALIPAY_SIGN_TYPE'
        );
    }

    function multi_lang_configuration_info_keys() {
        return array('MODULE_PAYMENT_ALIPAY_MESSAGE',
            'MODULE_PAYMENT_ALIPAY_EMAIL_MESSAGE');
    }

    function merchant_information_keys() {
        include_once(DIR_WS_CLASSES . "payment_methods.php");

        return array(
            'MODULE_PAYMENT_ALIPAY_SELLER_EMAIL' => array('field' => 'text', 'mapping' => 'MODULE_PAYMENT_ALIPAY_LNG_SELLER_EMAIL'),
            'MODULE_PAYMENT_ALIPAY_PARTNER_ID' => array('field' => 'text', 'mapping' => 'MODULE_PAYMENT_ALIPAY_LNG_PARTNER_ID'),
            'MODULE_PAYMENT_ALIPAY_SECURITY_CODE' => array('field' => 'text', 'mapping' => 'MODULE_PAYMENT_ALIPAY_LNG_SECURITY_CODE'),
            'MODULE_PAYMENT_TAX' => array("field" => 'select', 'mapping' => "MODULE_PAYMENT_TAX", "select" => payment_methods::get_tax_option())
        );
    }

    function get_pg_id() {
        return $this->payment_methods_id;
    }

    function get_orders_payment_info_file() {
        return DIR_FS_CATALOG_MODULES . 'payment/alipay/admin/orders.inc.php';
    }

    function get_orders_list_payment_info_file() {
        return DIR_FS_CATALOG_MODULES . 'payment/alipay/admin/orders_list.inc.php';
    }

    function get_ipn_class_file() {
        return DIR_FS_CATALOG_MODULES . 'payment/alipay/classes/alipay_ipn_class.php';
    }

    function charset_encode($input, $_output_charset, $_input_charset = "GBK") {
        $output = "";
        if (!isset($_output_charset))
            $_output_charset = $this->parameter['_input_charset '];
        if ($_input_charset == $_output_charset || $input == null) {
            $output = $input;
        } elseif (function_exists("mb_convert_encoding")) {
            $output = mb_convert_encoding($input, $_output_charset, $_input_charset);
        } elseif (function_exists("iconv")) {
            $output = iconv($_input_charset, $_output_charset, $input);
        }
        else
            die("sorry, you have no libs support for charset change.");
        return $output;
    }

}

?>