﻿<?
/*
  	$Id: orders.inc.php,v 1.4 2009/10/28 09:21:59 boonhock Exp $
	
  	Developer: Ho <PERSON>
  	Copyright (c) 2005 SKC Venture
  	
  	Released under the GNU General Public License
*/

$alipay_payment_status_array = array(	'WAIT_BUYER_PAY' => 'Pending',
										'TRADE_FINISHED' => 'Successful',
										'TRADE_SUCCESS' => 'Successful',
										'TRADE_CLOSED' => 'Unsuccessful');

$alipay_trans_info_select_sql = "	SELECT * 
									FROM " . TABLE_ALIPAY . "
									WHERE alipay_orders_id = '" . (int)$oID  . "'";
$alipay_trans_info_result_sql = tep_db_query($alipay_trans_info_select_sql);

$alipay_trans_history_select_sql = "	SELECT * 
										FROM " . TABLE_ALIPAY_STATUS_HISTORY . " 
										WHERE alipay_orders_id='" . (int)$oID . "'";
$alipay_trans_history_result_sql= tep_db_query($alipay_trans_history_select_sql);

include_once(DIR_FS_CATALOG_MODULES . 'payment/alipay/admin/languages/'.$language.'/alipay.lng.php');

?>
<table border="0" width="100%" cellspacing="0" cellpadding="2">
<? ob_start(); 

?>
	<tr>
		<td class="main" colspan="3">
      		<table border="0" width="100%" cellspacing="0" cellpadding="2">
      			<tr>
      				<td class="main" width="12%" nowrap><b><?=ENTRY_PAYMENT_METHOD?></b></td>
      				<td class="main">
      					<div>
      						<? if((int)$order->info['payment_methods_id']>0) { ?><div class="<?=((int)$payment_info_array[$order->info['payment_methods_id']]->confirm_complete_days>0?'rp_icon':'nrp_icon')?>"><b><?=((int)$payment_info_array[$order->info['payment_methods_id']]->confirm_complete_days>0?'RP':'NRP')?></b></div> <? } ?>
      						<div style="width:30px; height:15px; background-color:<?=$payment_module_info['display_colour']?>; float:left; vertical-align:top; text-align:center;"></div>
      						<div style="vertical-align: top">
      							&nbsp;&nbsp;
<?
	if ($view_payment_details_permission) {
		if (tep_not_null($last_payment_method) && $last_payment_method['payment_methods_id'] != $order->info['payment_methods_id']) {
			echo '<span class="redIndicator">'.(tep_not_null($payment_module_info['payment_methods_parent_title'])?$payment_module_info['payment_methods_parent_title'] . ' - ' :'') . $payment_module_info['payment_methods_title'].'</span>';
		} else {
			echo (tep_not_null($payment_module_info['payment_methods_parent_title'])?$payment_module_info['payment_methods_parent_title'] . ' - ' :'') . $payment_module_info['payment_methods_title'];
		}
		
		if (tep_not_null($last_payment_method) && $last_payment_method['payment_methods_id'] != $order->info['payment_methods_id']) {
			echo '&nbsp;&nbsp;<span class="redIndicator">(Last Payment Method: ' . (tep_not_null($last_payment_method['parent_payment_methods_title'])? $last_payment_method['parent_payment_methods_title'] . ' - ' : '' ) . $last_payment_method['payment_methods_title'] . ')</span>';
		}
	} else {
		echo (tep_not_null($payment_module_info['payment_methods_parent_title'])?$payment_module_info['payment_methods_parent_title'] . ' - ' :'') . $payment_module_info['payment_methods_title'];
	}
?>
							</div>
      					</div>
      				</td>
				</tr>
			</table>
		</td>
	</tr>
<?
$payment_method_title_info = ob_get_contents();
ob_end_clean();
echo $payment_method_title_info;
?>
  	<tr>
    	<td class="main">
      		<table border="0" width="100%" cellspacing="0" cellpadding="2">
      			<tr valign="top">
<?
if (tep_db_num_rows($alipay_trans_info_result_sql)) {
	$alipay_trans_found = true;
	if (!$view_payment_details_permission) {
?>
					<td class="main"><?=$payment_method_title_info?></td>
<?
	} else {
		if ($alipay_trans_info_row = tep_db_fetch_array($alipay_trans_info_result_sql)) {
?>
        			<td width="25%">
              			<table border="0" cellspacing="0" cellpadding="2">
							<tr>
								<td class="main" valign="top" nowrap><b><?=ENTRY_NOTIFY_TYPE?></b>&nbsp;</td>
			                	<td class="main" valign="top" nowrap><?=$alipay_trans_info_row["alipay_notify_type"]?></td>
							</tr>
							<tr>
								<td class="main" valign="top" nowrap><b><?=ENTRY_NOTIFY_ID?></b>&nbsp;</td>
			                	<td class="main" valign="top" nowrap><?=$alipay_trans_info_row["alipay_notify_id"]?></td>
							</tr>
							<tr>
								<td class="main" valign="top" nowrap><b><?=ENTRY_NOTIFY_TIME?></b>&nbsp;</td>
			                	<td class="main" valign="top" nowrap><?=$alipay_trans_info_row["alipay_notify_time"]?></td>
							</tr>
							<tr>
								<td class="main" valign="top" nowrap><b><?=ENTRY_SIGN?></b>&nbsp;</td>
			                	<td class="main" valign="top" nowrap><?=$alipay_trans_info_row["alipay_sign"]?></td>
							</tr>
							<tr>
								<td class="main" valign="top" nowrap><b><?=ENTRY_TRADE_NO?></b>&nbsp;</td>
			                	<td class="main" valign="top" nowrap><?=$alipay_trans_info_row["alipay_trade_no"]?></td>
							</tr>
							<tr>
								<td class="main" valign="top" nowrap><b><?=ENTRY_PAYMENT_TYPE?></b>&nbsp;</td>
			                	<td class="main" valign="top" nowrap><?=$alipay_trans_info_row["alipay_payment_type"]?></td>
							</tr>
							<tr>
								<td class="main" valign="top" nowrap><b><?=ENTRY_BUYER_ID?></b>&nbsp;</td>
			                	<td class="main" valign="top" nowrap><?=$alipay_trans_info_row["alipay_buyer_id"]?></td>
							</tr>
							<tr>
								<td class="main" valign="top" nowrap><b><?=ENTRY_BUYER_EMAIL?></b>&nbsp;</td>
			                	<td class="main" valign="top" nowrap><?=$alipay_trans_info_row["alipay_buyer_email"]?></td>
							</tr>
              			</table>
        			</td>
<?
		}
	}
}
?>
        			<td valign="top">
              			<table border="0" cellspacing="0" cellpadding="2">
<?	if ($alipay_trans_found) { ?>
        					<tr>
                				<td class="main" valign="top" nowrap><b><?="Payment Status:"?></b>&nbsp;</td>
                				<td class="main" nowrap><?
                					if (isset($alipay_payment_status_array[$alipay_trans_info_row["alipay_trade_status"]])) {
                						echo $alipay_payment_status_array[$alipay_trans_info_row["alipay_trade_status"]];
                					} else {
                						echo TEXT_STATUS_UNKNOWN;
                					}
                				?></td>
                			</tr>
<?	}	?>
        					<tr>
                				<td class="main" valign="top" nowrap colspan="2"><?
                					if ($order->info['orders_status'] == '1' || $order->info['orders_status'] == '7' || $order->info['orders_status'] == '2') {
	                					echo "&nbsp;&nbsp;";
										echo tep_draw_form('bb_status_check_form', FILENAME_ORDERS, tep_get_all_get_params(), 'post');
										echo tep_draw_hidden_field('subaction', 'payment_action');
										echo tep_draw_hidden_field('payment_action', 'check_trans_status');
										echo '<input type="submit" name="BBCheckTransStatusBtn" value="Check Payment Status" title="Check Payment Status" class="inputButton">';
										echo "</form>";
									}
								?></td>
              				</tr>
<?		if (tep_db_num_rows($alipay_trans_history_result_sql)) { ?>
              				<tr>
                				<td class="main" colspan="2" nowrap>
                					<table border="1" cellspacing="0" cellpadding="2">
        								<tr>
                							<td class="smallText" nowrap><b><?=TABLE_HEADING_ALIPAY_DATE?></b></td>
                							<td class="smallText" nowrap><b><?=TABLE_HEADING_ALIPAY_STATUS?></b></td>
                							<td class="smallText" nowrap><b><?=TABLE_HEADING_ALIPAY_DESCRIPTION?></b></td>
                						</tr>
<?
			while ($alipay_trans_history_row = tep_db_fetch_array($alipay_trans_history_result_sql)) {
        	echo ' 						<tr>
                							<td class="smallText" nowrap>'.$alipay_trans_history_row['alipay_date'].'</td>
                							<td class="smallText" nowrap>'.$alipay_payment_status_array[$alipay_trans_history_row['alipay_status']].'</td>
                							<td class="smallText" nowrap>';
            									echo $alipay_trans_history_row['alipay_description'];
            echo '							</td>
            							</tr>';
     		}
?>
                					</table>
                				</td>
                			</tr>
<?		}	?>
              			</table>
              		</td>
<?		if ($alipay_trans_found) {	?>
        			<td>
        				<table>
							<tr>
								<td class="main" valign="top" nowrap><b><?=ENTRY_ORDER_CURRENCY?></b>&nbsp;</td>
			                	<td class="main" valign="top" nowrap>
<?
			    					if (strtolower($alipay_trans_info_row["alipay_currency"]) == strtolower($order->info['currency'])) {
			    						echo $alipay_trans_info_row['alipay_currency'];
			    					} else {
			    						echo '<span class="redIndicator">'.$alipay_trans_info_row["alipay_currency"].'<br><span class="smallText">Does not match purchase currency.</span></span>';
			    					}
									echo '('.TEXT_INFO_CURRENCY_NOT_FROM_ALIPAY.')';			                		
?>
			                	</td>
							</tr>
							<tr>
								<td class="main" valign="top" nowrap><b><?=ENTRY_TOTAL_FEE?></b>&nbsp;</td>
			                	<td class="main" valign="top" nowrap><?
			    					$gross_display_text = number_format($alipay_trans_info_row["alipay_total_fee"], $currencies->currencies[$order->info["currency"]]['decimal_places'], $currencies->currencies[$order->info["currency"]]['decimal_point'], $currencies->currencies[$order->info["currency"]]['thousands_point']);
			    					if ($currencies->currencies[$order->info["currency"]]['symbol_left'].$gross_display_text.$currencies->currencies[$order->info["currency"]]['symbol_right'] != strip_tags($order->order_totals['ot_total']['text']) ) {
			    						$gross_display_text = '<span class="redIndicator">'.$gross_display_text.'<br><span class="smallText">Does not match purchase amount.</span></span>';
			    					}
			    					echo $gross_display_text;
			    				?>
							</tr>
        				</table>
        			</td>
<?
		}
?>
      			</tr>
      		</table>
   		</td>
  	</tr>
</table>