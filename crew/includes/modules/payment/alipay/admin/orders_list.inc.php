<?
/*
  	$Id: orders_list.inc.php,v 1.2 2009/07/08 09:57:18 boonhock Exp $
	
  	Developer: Ho <PERSON>
  	Copyright (c) 2005 SKC Venture
  	
  	Released under the GNU General Public License
*/
include_once(DIR_FS_CATALOG_MODULES . 'payment/alipay/admin/languages/'.$language.'/alipay.lng.php');

$alipay_trans_info_select_sql = "	SELECT * 
									FROM " . TABLE_ALIPAY . " 
									WHERE alipay_orders_id='" . (int)$order_obj->orders_id . "'";
$alipay_trans_info_result_sql= tep_db_query($alipay_trans_info_select_sql);
?>
<table border="0" width="100%" cellspacing="0" cellpadding="2">
	<tr>
		<td class="subInvoiceBoxHeading">
			<div style="border-bottom: 1px solid #996600; border-top: 1px solid #996600; padding: 2px;">Payment Details</div>
		</td>
	</tr>
<?
if ($alipay_trans_info_row = tep_db_fetch_array($alipay_trans_info_result_sql)) {
	
	$alipay_payment_status_array = array(	'WAIT_BUYER_PAY' => 'Pending',
											'TRADE_FINISHED' => 'Successful',
											'TRADE_SUCCESS' => 'Successful',
											'TRADE_CLOSED' => 'Unsuccessful');

	
?>
  	<tr>
    	<td class="main">
      		<table border="0" width="100%" cellspacing="0" cellpadding="2">
      			<tr valign="top">
        			<td width="40%">
              			<table border="0" cellspacing="0" cellpadding="2">
							<tr>
								<td class="main" valign="top" nowrap><b><?=ENTRY_NOTIFY_TYPE?></b>&nbsp;</td>
			                	<td class="main" valign="top" nowrap><?=$alipay_trans_info_row["alipay_notify_type"]?></td>
							</tr>
							<tr>
								<td class="main" valign="top" nowrap><b><?=ENTRY_TRADE_STATUS?></b>&nbsp;</td>
			                	<td class="main" valign="top" nowrap><?=(isset($alipay_payment_status_array[$alipay_trans_info_row["alipay_trade_status"]]) ? $alipay_payment_status_array[$alipay_trans_info_row["alipay_trade_status"]]:'')?></td>
							</tr>
							<tr>
								<td class="main" valign="top" nowrap><b><?=ENTRY_NOTIFY_ID?></b>&nbsp;</td>
			                	<td class="main" valign="top" nowrap><?=$alipay_trans_info_row["alipay_notify_id"]?></td>
							</tr>
							<tr>
								<td class="main" valign="top" nowrap><b><?=ENTRY_NOTIFY_TIME?></b>&nbsp;</td>
			                	<td class="main" valign="top" nowrap><?=$alipay_trans_info_row["alipay_notify_time"]?></td>
							</tr>
							<tr>
								<td class="main" valign="top" nowrap><b><?=ENTRY_SIGN?></b>&nbsp;</td>
			                	<td class="main" valign="top" nowrap><?=$alipay_trans_info_row["alipay_sign"]?></td>
							</tr>
							<tr>
								<td class="main" valign="top" nowrap><b><?=ENTRY_OUT_TRADE_NO?></b>&nbsp;</td>
			                	<td class="main" valign="top" nowrap><?=$alipay_trans_info_row["alipay_out_trade_no"]?></td>
							</tr>
							<tr>
								<td class="main" valign="top" nowrap><b><?=ENTRY_PAYMENT_TYPE?></b>&nbsp;</td>
			                	<td class="main" valign="top" nowrap><?=$alipay_trans_info_row["alipay_payment_type"]?></td>
							</tr>
							<tr>
								<td class="main" valign="top" nowrap><b><?=ENTRY_BUYER_ID?></b>&nbsp;</td>
			                	<td class="main" valign="top" nowrap><?=$alipay_trans_info_row["alipay_buyer_id"]?></td>
							</tr>
							<tr>
								<td class="main" valign="top" nowrap><b><?=ENTRY_BUYER_EMAIL?></b>&nbsp;</td>
			                	<td class="main" valign="top" nowrap><?=$alipay_trans_info_row["alipay_buyer_email"]?></td>
							</tr>
							<tr>
								<td class="main" valign="top" nowrap><b><?=ENTRY_ORDER_CURRENCY?></b>&nbsp;</td>
			                	<td class="main" valign="top" nowrap><?=$alipay_trans_info_row["alipay_currency"]?></td>
							</tr>
							<tr>
								<td class="main" valign="top" nowrap><b><?=ENTRY_TOTAL_FEE?></b>&nbsp;</td>
			                	<td class="main" valign="top" nowrap><?=$alipay_trans_info_row["alipay_total_fee"]?></td>
							</tr>
              			</table>
        			</td>
      			</tr>
      		</table>
   		</td>
  	</tr>
<?
} else {
?>
	<tr>
		<td class="invoiceRecords"><?=ENTRY_NO_PAYMENT_INFO?></td>
	</tr>
<?
}
?>
</table>