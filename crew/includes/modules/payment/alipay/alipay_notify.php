<?php

class alipay_notify {

    var $gateway;
    var $security_code;
    var $partner;
    var $sign_type;
    var $mysign;
    var $_input_charset;
    var $transport;

    function alipay_notify($partner, $security_code, $sign_type = "MD5", $_input_charset = "GBK", $transport = "https") {
        $this->partner = $partner;
        $this->security_code = $security_code;
        $this->sign_type = $sign_type;
        $this->mysign = "";
        $this->_input_charset = $_input_charset;
        $this->transport = $transport;
        if ($this->transport == "https") {
            $this->gateway = "https://mapi.alipay.com/gateway.do?";
        } else {
            $this->gateway = "http://notify.alipay.com/trade/notify_query.do?";
        }
    }

    function notify_verify() {
        if ($this->transport == "https") {
            $veryfy_url = $this->gateway . "service=notify_verify" . "&partner=" . $this->partner . "&notify_id=" . $_REQUEST["notify_id"];
        } else {
            $veryfy_url = $this->gateway . "notify_id=" . $_REQUEST["notify_id"] . "&partner=" . $this->partner;
        }

        $veryfy_result = $this->get_verify($veryfy_url);

        if (!eregi_dep("true$", $veryfy_result)) {
            $alipay_payment_history_data_array = array('alipay_orders_id' => (int) $_REQUEST['out_trade_no'],
                'alipay_date' => 'now()',
                'alipay_status' => trim($_REQUEST['trade_status']),
                'alipay_description' => 'Error: Verification failed (' . $veryfy_result . ')'
            );
            tep_db_perform(TABLE_ALIPAY_STATUS_HISTORY, $alipay_payment_history_data_array);
            return false;
        }

        $post = $this->para_filter($_REQUEST);
        $sort_post = $this->arg_sort($post);

        while (list ($key, $val) = each($sort_post)) {
            $arg .= $key . "=" . $val . "&";
        }

        $prestr = substr($arg, 0, count($arg) - 2);

        $this->mysign = $this->sign($prestr . $this->security_code);

        if (trim($this->mysign) != trim($_REQUEST["sign"])) {
            @tep_mail('<EMAIL> ', '<EMAIL> ', (int) $_REQUEST['out_trade_no'] . ' - Alipay Sign Not Match DEBUG E-MAIL', $this->mysign . '!=' . $_REQUEST["sign"], STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
            $alipay_payment_history_data_array = array('alipay_orders_id' => (int) $_REQUEST['out_trade_no'],
                'alipay_date' => 'now()',
                'alipay_status' => trim($_REQUEST['trade_status']),
                'alipay_description' => 'Error: sign not match (' . trim($this->mysign) . '!=' . trim($_REQUEST["sign"]) . ')'
            );
            tep_db_perform(TABLE_ALIPAY_STATUS_HISTORY, $alipay_payment_history_data_array);
        } else {
            return true;
        }
        return false;
    }

    function return_verify() {
        if ($this->transport == "https") {
            $veryfy_url = $this->gateway . "service=notify_verify" . "&partner=" . $this->partner . "&notify_id=" . $_GET["notify_id"];
        } else {
            $veryfy_url = $this->gateway . "notify_id=" . $_GET["notify_id"] . "&partner=" . $this->partner;
        }

        $veryfy_result = $this->get_verify($veryfy_url);
        $get = $this->para_filter($_GET);
        $sort_get = $this->arg_sort($get);

        while (list ($key, $val) = each($sort_get)) {
            if ($key != "sign" && $key != "sign_type")
                $arg .= $key . "=" . $val . "&";
        }
        $prestr = substr($arg, 0, count($arg) - 2);  // Truncate last &
        $this->mysign = $this->sign($prestr . $this->security_code);

        if (eregi_dep("true$", $veryfy_result) && $this->mysign == $_GET["sign"]) {
            return true;
        } else {
            return false;
        }
    }

    function get_verify($url, $time_out = "60") {
        $urlarr = parse_url($url);
        $errno = "";
        $errstr = "";
        $transports = "";
        if ($urlarr["scheme"] == "https") {
            $transports = "ssl://";
            $urlarr["port"] = "443";
        } else {
            $transports = "tcp://";
            $urlarr["port"] = "80";
        }
        $fp = @fsockopen($transports . $urlarr['host'], $urlarr['port'], $errno, $errstr, $time_out);
        if (!$fp) {
            die("ERROR: $errno - $errstr<br />\n");
        } else {
            fputs($fp, "POST " . $urlarr["path"] . " HTTP/1.1\r\n");
            fputs($fp, "Host: " . $urlarr["host"] . "\r\n");
            fputs($fp, "Content-type: application/x-www-form-urlencoded\r\n");
            fputs($fp, "Content-length: " . strlen($urlarr["query"]) . "\r\n");
            fputs($fp, "Connection: close\r\n\r\n");
            fputs($fp, $urlarr["query"] . "\r\n\r\n");
            while (!feof($fp)) {
                $info[] = @fgets($fp, 1024);
            }
            fclose($fp);

            $info = implode(",", $info);
            while (list ($key, $val) = each($_REQUEST)) {
                $arg .= $key . "=" . $val . "&";
            }

            return $info;
        }
    }

    function arg_sort($array) {
        ksort($array);
        reset($array);
        return $array;
    }

    function sign($prestr) {
        $sign = '';
        if ($this->sign_type == 'MD5') {
            $sign = md5($prestr);
        } elseif ($this->sign_type == 'DSA') {
            die("DSA 签名方法待后续开发，请先使用MD5签名方式");
        } else {
            die("支付宝暂不支持" . $this->sign_type . "类型的签名方式");
        }
        return $sign;
    }

    function para_filter($parameter) { //除去数组中的空值和签名模式
        $para = array();
        while (list ($key, $val) = each($parameter)) {
            if ($key == "sign" || $key == "sign_type" || $val == "") {
                continue;
            } else {
                $para[$key] = $parameter[$key];
            }
        }
        return $para;
    }

    function charset_encode($input, $_output_charset, $_input_charset = "GBK") {
        $output = "";
        if (!isset($_output_charset))
            $_output_charset = $this->parameter['_input_charset '];
        if ($_input_charset == $_output_charset || $input == null) {
            $output = $input;
        } elseif (function_exists("mb_convert_encoding")) {
            $output = mb_convert_encoding($input, $_output_charset, $_input_charset);
        } elseif (function_exists("iconv")) {
            $output = iconv($_input_charset, $_output_charset, $input);
        } else
            die("sorry, you have no libs support for charset change.");
        return $output;
    }

    function charset_decode($input, $_input_charset, $_output_charset = "GBK") {
        $output = "";
        if (!isset($_input_charset))
            $_input_charset = $this->_input_charset;
        if ($_input_charset == $_output_charset || $input == null) {
            $output = $input;
        } elseif (function_exists("mb_convert_encoding")) {
            $output = mb_convert_encoding($input, $_output_charset, $_input_charset);
        } elseif (function_exists("iconv")) {
            $output = iconv($_input_charset, $_output_charset, $input);
        } else
            die("sorry, you have no libs support for charset changes.");
        return $output;
    }

}

?>