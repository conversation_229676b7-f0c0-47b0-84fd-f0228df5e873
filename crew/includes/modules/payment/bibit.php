<?php

require_once('payment_gateway.php');

class bibit extends payment_gateway {

    var $code, $title, $description, $enabled, $sort_order, $preferred, $merchantCode, $merchantPassword, $bbCurrencies, $xml, $comment, $check_trans_status_flag;

    // class constructor
    function bibit($pm_id = '') {
        global $order, $languages_id, $default_languages_id, $defCurr;

        //Basic info
        $this->payment_methods_id = 0;
        $this->payment_methods_parent_id = 0;
        $this->code = 'bibit';
        $this->filename = 'bibit.php';
        $this->title = $this->code;
        $this->preferred = true;

        $this->check_trans_status_flag = false;

        $payment_methods_select_sql = "	SELECT payment_methods_parent_id, payment_methods_code, 
												payment_methods_types_id, 
												payment_methods_receive_status_mode, payment_methods_id, 
												payment_methods_title, payment_methods_sort_order, 
												payment_methods_receive_status, payment_methods_legend_color, 
												payment_methods_logo 
										FROM " . TABLE_PAYMENT_METHODS . " 
										WHERE 1 ";
        if ((int) $pm_id > 0) {
            $payment_methods_select_sql .= "	AND payment_methods_id = '" . (int) $pm_id . "'";
        } else {
            $payment_methods_select_sql .= "	AND payment_methods_filename = '" . tep_db_input($this->filename) . "'";
        }

        $payment_methods_result_sql = tep_db_query($payment_methods_select_sql);
        if ($payment_methods_row = tep_db_fetch_array($payment_methods_result_sql)) {
            $pm_desc_title_select_sql = "	SELECT payment_methods_description_title 
											FROM " . TABLE_PAYMENT_METHODS_DESCRIPTION . " 
											WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "' 
												AND (IF (languages_id = '" . tep_db_input($languages_id) . "', 1, IF ((	SELECT COUNT(payment_methods_id) > 0 
																														FROM " . TABLE_PAYMENT_METHODS_DESCRIPTION . " 
																														WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "' 
																															AND languages_id = '" . (int) $languages_id . "'), 0, languages_id = 1)))";
            $pm_desc_title_result_sql = tep_db_query($pm_desc_title_select_sql);
            $pm_desc_title_row = tep_db_fetch_array($pm_desc_title_result_sql);

            $this->payment_methods_id = (int) $payment_methods_row['payment_methods_id'];
            $this->payment_methods_parent_id = (int) $payment_methods_row['payment_methods_parent_id'];
            $this->code = $payment_methods_row['payment_methods_code'];
            $this->title = $payment_methods_row['payment_methods_title'];
            $this->display_title = $pm_desc_title_row['payment_methods_description_title']; // if title to be display for payment method is different from payment method value
            $this->sort_order = $payment_methods_row['payment_methods_sort_order'];
            $this->enabled = $payment_methods_row['payment_methods_receive_status_mode'];
            $this->legend_display_colour = $payment_methods_row['payment_methods_legend_color'];
            $this->receive_status = $payment_methods_row['payment_methods_receive_status'];
            $this->description = $this->display_title;

            $this->comment = (defined(MODULE_PAYMENT_BIBIT_TEXT_COMMENT) ? MODULE_PAYMENT_BIBIT_TEXT_COMMENT : '');
            $this->bbCurrencies = $this->get_support_currencies($this->payment_methods_id);

            $configuration_setting_array = $this->load_pm_setting();
            $this->test_mode = $configuration_setting_array['MODULE_PAYMENT_BIBIT_TEST_MODE'];
            $this->order_status_id = $configuration_setting_array['MODULE_PAYMENT_BIBIT_ORDER_STATUS_ID'];
            $this->useprecapture = $configuration_setting_array['MODULE_PAYMENT_BIBIT_USEPRECAPTURE'];
            $this->message = $configuration_setting_array['MODULE_PAYMENT_BIBIT_MESSAGE'];
            $this->email_message = $configuration_setting_array['MODULE_PAYMENT_BIBIT_EMAIL_MESSAGE'];
            $this->confirm_complete_days = $configuration_setting_array['MODULE_PAYMENT_BIBIT_CONFIRM_COMPLETE'];
            $this->manual_capture_notification_email = $configuration_setting_array['MODULE_PAYMENT_BIBIT_MANUAL_CAPTURE_NOTIFICATION_EMAIL'];
            $this->require_address_information = $configuration_setting_array['MODULE_PAYMENT_BIBIT_MANDATORY_ADDRESS_FIELD'];
            $this->require_ic_information = $configuration_setting_array['MODULE_PAYMENT_BIBIT_MANDATORY_IC_FIELD'];
            $this->require_contact_information = $configuration_setting_array['MODULE_PAYMENT_BIBIT_MANDATORY_CONTACT_FIELD'];

            $this->customer_payment_info = $configuration_setting_array['MODULE_PAYMENT_BIBIT_CUSTOMER_PAYMENT_INFO'];

            $this->defCurr = DEFAULT_CURRENCY;

            if ((int) $this->order_status_id > 0) {
                $this->order_status = $this->order_status_id;
            } else {
                $this->order_status = DEFAULT_ORDERS_STATUS_ID;
            }

            $this->email_footer = str_replace(array("<br>", "<BR>", "\r\n"), array("\n", "\n", ''), $this->email_message);
            $this->form_action_url = '';

            if ($this->test_mode == 'True') {
                $this->api_endpoint_domain = 'secure-test.worldpay.com';
            } else {
                $this->api_endpoint_domain = 'secure.worldpay.com';
            }
        }

        $this->force_to_checkoutprocess = true;
        $this->has_ipn = true;
        $this->order_processing_status = 7;
        $this->auto_cancel_period = 60; // In minutes
        $this->connect_via_proxy = false;
    }

    function javascript_validation() {
        return false;
    }

    function selection() {
        global $languages_id, $default_languages_id;

        $selection_array = array();

        $payment_methods_select_sql = "	SELECT payment_methods_id, payment_methods_code, payment_methods_types_id, payment_methods_logo, payment_methods_receive_status_mode 
										FROM " . TABLE_PAYMENT_METHODS . " 
										WHERE payment_methods_receive_status = 1 
											AND payment_methods_receive_status_mode <> 0 
											AND payment_methods_parent_id = '" . (int) $this->payment_methods_id . "' 
										ORDER BY payment_methods_sort_order";
        $payment_methods_result_sql = tep_db_query($payment_methods_select_sql);
        while ($payment_methods_row = tep_db_fetch_array($payment_methods_result_sql)) {
            $pm_desc_title_select_sql = "	SELECT payment_methods_description_title 
											FROM " . TABLE_PAYMENT_METHODS_DESCRIPTION . " 
											WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "' 
												AND (IF (languages_id = '" . tep_db_input($languages_id) . "', 1, IF ((	SELECT COUNT(payment_methods_id) > 0 
																														FROM " . TABLE_PAYMENT_METHODS_DESCRIPTION . " 
																														WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "' 
																															AND languages_id = '" . (int) $languages_id . "'), 0, languages_id = 1)))";
            $pm_desc_title_result_sql = tep_db_query($pm_desc_title_select_sql);
            $pm_desc_title_row = tep_db_fetch_array($pm_desc_title_result_sql);

            $pm_array = $this->load_pm_setting(1, $payment_methods_row['payment_methods_id']);

            $selection_array[] = array('payment_gateway_code' => $this->code,
                'payment_methods_receive_status_mode' => $payment_methods_row['payment_methods_receive_status_mode'],
                'payment_methods_id' => $payment_methods_row['payment_methods_id'],
                'payment_methods_code' => $payment_methods_row['payment_methods_code'],
                'payment_methods_types_id' => $payment_methods_row['payment_methods_types_id'],
                'payment_methods_parent_id' => $this->payment_methods_id,
                'payment_methods_logo' => $payment_methods_row['payment_methods_logo'],
                'payment_methods_description_title' => $pm_desc_title_row['payment_methods_description_title'],
                'currency' => $this->get_support_currencies($payment_methods_row['payment_methods_id']),
                'confirm_complete_days' => $pm_array['MODULE_PAYMENT_BIBIT_CONFIRM_COMPLETE'],
                'show_billing_address' => $pm_array['MODULE_PAYMENT_BIBIT_MANDATORY_ADDRESS_FIELD'],
                'show_contact_number' => $pm_array['MODULE_PAYMENT_BIBIT_MANDATORY_CONTACT_FIELD'],
                'show_ic' => $pm_array['MODULE_PAYMENT_BIBIT_MANDATORY_IC_FIELD']
            );


            if ($payment_methods_row['payment_methods_receive_status_mode'] == '-1') { // Inactive
                $pm_status_message_select = "	SELECT payment_methods_status_message 
												FROM " . TABLE_PAYMENT_METHODS_STATUS_DESCRIPTION . " 
												WHERE payment_methods_mode = 'RECEIVE' 
													AND payment_methods_status = '-1' 
													AND languages_id = '" . (int) $languages_id . "' 
													AND payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "'";
                $pm_status_message_result = tep_db_query($pm_status_message_select);
                $pm_status_message_row = tep_db_fetch_array($pm_status_message_result);
                $selection_array[sizeof($selection_array) - 1]['payment_methods_status_message'] = $pm_status_message_row['payment_methods_status_message'];
            }
        }

        return $selection_array;
    }

    function set_support_currencies($currency_array) {
        $this->bbCurrencies = $currency_array;
    }

    function get_require_address_information() {
        return $this->require_address_information;
    }

    function draw_confirm_button() {
        return tep_image_button2('red', 'javascript:document.checkout_confirmation.submit()', MODULE_PAYMENT_BIBIT_TEXT_CART, 200);
    }

    function get_confirm_button_caption() {
        return MODULE_PAYMENT_BIBIT_TEXT_TITLE;
    }

    function pre_confirmation_check() {
        global $currency;

        if (!in_array($currency, $this->bbCurrencies)) {
            
        }

        return false;
    }

    function process_button() {
        global $shipping_cost, $total_cost, $shipping_selected, $shipping_method, $currencies, $currency, $customer_id, $order, $languages_id;
        global $country;

        $country_info_array = tep_get_countries($country, true);

        if ($this->force_to_checkoutprocess && !isset($_SESSION['order_logged']))
            return;

        $bbCurrency = $currency;

        $payment_method_str = '';

        if (!in_array($bbCurrency, $this->bbCurrencies)) {
            $bbCurrency = in_array(DEFAULT_CURRENCY, $this->bbCurrencies) ? DEFAULT_CURRENCY : $this->bbCurrencies[0];
        }

        $exponent = $this->get_bb_exponent($bbCurrency);

        // Multi Currency
        $OrderAmt = number_format($order->info['total'] * $currencies->get_value($bbCurrency), $currencies->get_decimal_places($bbCurrency), '.', '');
        $OrderAmt = number_format($OrderAmt, $exponent, '.', '');
        // Multi Currency

        $bibit_url = $this->api_endpoint_domain;

        $firstname = $order->billing['firstname'];
        $lastname = $order->billing['lastname'];

        $this->get_merchant_account($bbCurrency);
        if (!empty($this->minPrice) && $OrderAmt < $this->minPrice) {
            tep_redirect(tep_href_link(FILENAME_CHECKOUT_PAYMENT, 'error_message=' . sprintf(MODULE_PAYMENT_BIBIT_MIN_CHECKOUT_LIMIT, $bbCurrency . $this->minPrice), 'SSL', true, false));
        }
        if (!empty($this->maxPrice) && $OrderAmt > $this->maxPrice) {
            tep_redirect(tep_href_link(FILENAME_CHECKOUT_PAYMENT, 'error_message=' . sprintf(MODULE_PAYMENT_BIBIT_MAX_CHECKOUT_LIMIT, $bbCurrency . $this->maxPrice), 'SSL', true, false));
        }
        $invoiceData = '<center>
	 						<table border="0" cellspacing="0" cellpadding="2">
								<tr>
									<td><b>Order ID:</b> ' . $_SESSION['order_logged'] . '</td>
								</tr>
								<tr>
									<td><b>Billing notice:</b></td>
								</tr>
								<tr>
									<td>Your payment will be handled by ' . MODULE_PAYMENT_BIBIT_TEXT_TITLE . ' Payments Services<br>This name may appear on your bank statement<br>http://www.worldpay.com</td>
								</tr>
							</table>
						</center>';
        $payment_method_str = '<include code="ALL"/>';

        $this->xml =
                '<?xml version="1.0"?>
            <!DOCTYPE paymentService PUBLIC "-//WorldPay/DTD WorldPay PaymentService v1//EN" "http://dtd.worldpay.com/paymentService_v1.dtd">
			<paymentService version="1.4" merchantCode="' . $this->merchantCode . '">
				<submit>
					<order orderCode="' . $_SESSION['order_logged'] . '">
						<description>' . MODULE_PAYMENT_BIBIT_TEXT_DESCRIPTION . '</description>
						<amount value="' . preg_replace('/[^\d]/', '', $OrderAmt) . '" currencyCode="' . $bbCurrency . '" exponent="' . $exponent . '"/>
						<orderContent>
							<![CDATA[' . $invoiceData . ']]>
						</orderContent>
						<paymentMethodMask>' . $payment_method_str . '</paymentMethodMask>
						<shopper>
							<shopperEmailAddress>' . $order->customer['email_address'] . '</shopperEmailAddress>
						</shopper>
						<shippingAddress>
							<address>
								<firstName>' . $firstname . '</firstName>
								<lastName>' . $lastname . '</lastName>
								<street>' . $order->billing['street_address'] . '</street>
								<postalCode>' . $order->billing['postcode'] . '</postalCode>
								<city>' . $order->billing['city'] . '</city>
								<countryCode>' . $order->billing['country']['iso_code_2'] . '</countryCode>
								<telephoneNumber>' . $order->customer['int_dialing_code'] . $order->customer['telephone'] . '</telephoneNumber>
							</address>
						</shippingAddress>
					</order>
				</submit>
			</paymentService>';

        $bibit_curl_url = 'https://' . $bibit_url . '/jsp/merchant/xml/paymentService.jsp';
        $result_array = $this->curl_connect($bibit_curl_url, $this->xml);

        if (isset($result_array['redirectURL'])) {
            $this->form_action_url = $result_array['redirectURL'] . '&country=' . urlencode($country_info_array['countries_iso_code_2']) . '&successURL=' . urlencode(tep_href_link(FILENAME_CHECKOUT_PROCESS)) . '&pendingURL=' . urlencode(tep_href_link(FILENAME_CHECKOUT_PROCESS)) . '&failureURL=' . urlencode(tep_href_link(FILENAME_BBCALLBACK, 'error_message=' . urlencode(MODULE_PAYMENT_BIBIT_TEXT_ERROR_MESSAGE), 'SSL')) . '&preferredPaymentMethod=' . urlencode($this->code);
        } else {
            tep_redirect(tep_href_link(FILENAME_CHECKOUT_PAYMENT, 'error_message=' . $result_array['errorText'], 'SSL', true, false));
        }

        return true;
    }

    function before_process() {
        $proceed = false;

        if (!isset($_SESSION['order_logged'])) {
            return;
        }

        $orderkey_array = explode('^', tep_db_prepare_input($_REQUEST["orderKey"]));

        $payment_status = trim(tep_db_prepare_input($_REQUEST['paymentStatus']));
        $mac_id_return = trim(tep_db_prepare_input($_REQUEST['mac']));
        $currency_return = trim(tep_db_prepare_input($_REQUEST['paymentCurrency']));
        $admin_code = trim($orderkey_array[0]);
        $merchant_code_return = trim($orderkey_array[1]);
        $order_id = trim($orderkey_array[2]);

        if (tep_not_null($currency_return)) {
            $order_currency = $currency_return;
        } else {
            /////////////////////////////////////////////////
            // Only for offline payment like Bank Transfer //
            /////////////////////////////////////////////////
            $currency_select_sql = "	SELECT currency 
	 									FROM " . TABLE_ORDERS . " 
	 									WHERE orders_id = '" . tep_db_input($order_id) . "'";
            $currency_result_sql = tep_db_query($currency_select_sql);
            if ($currency_row = tep_db_fetch_array($currency_result_sql)) {
                $order_currency = $currency_row['currency'];
            }
        }

        // use back the class file
        $this->get_merchant_account($order_currency);

        if ($admin_code == $this->adminCode && $merchant_code_return == $this->get_merchant_code()) {
            $exponent = $this->get_bb_exponent($currency_return);

            $bibit_return_amt = tep_db_prepare_input($_REQUEST['paymentAmount']);

            // The correct mac id for this order
            $mac_id = md5($this->adminCode . '^' . $this->merchantCode . '^' . $order_id . $bibit_return_amt . $currency_return . $payment_status . $this->macKey);

            if ($exponent > 0) {
                $bibit_return_amt = ($bibit_return_amt / pow(10, $exponent));
            }

            $bb_data_array = array('orders_id' => $order_id,
                'bibit_status' => $payment_status,
                'bibit_currency' => $currency_return,
                'bibit_amount' => $bibit_return_amt
            );

            if ($mac_id == $mac_id_return) {
                $bb_data_array['bibit_mac'] = $mac_id_return;
            }

            $orders_id_select_sql = "	SELECT orders_id 
										FROM " . TABLE_BIBIT . " 
										WHERE orders_id = '" . tep_db_input($order_id) . "'";
            $orders_id_result_sql = tep_db_query($orders_id_select_sql);

            if (tep_db_num_rows($orders_id_result_sql) > 0) {
                tep_db_perform(TABLE_BIBIT, $bb_data_array, 'update', "orders_id = '" . tep_db_input($order_id) . "'");
            } else {
                tep_db_perform(TABLE_BIBIT, $bb_data_array);
            }

            if ($payment_status != 'REFUSED') {
                $proceed = true;
            }
        }

        if (!$proceed || (!empty($order_id) && $_SESSION['order_logged'] != $order_id)) {
            tep_redirect(tep_href_link(FILENAME_CHECKOUT_PAYMENT, 'error_message=' . urlencode(MODULE_PAYMENT_BIBIT_TEXT_ERROR_MESSAGE), 'SSL', true, false));
        }
    }

    function after_process() {
        return false;
    }

    function link_to_payment($new_order_id) {
        global $order_logged;

        if (tep_session_is_registered('order_logged'))
            tep_session_unregister('order_logged');

        tep_session_register('order_logged');
        $order_logged = $new_order_id;
        require(DIR_WS_INCLUDES . 'modules/payment/bibit/catalog/bibit_splash.php');
        return;
    }

    function is_supported_currency($selected_currency) {
        return (in_array($selected_currency, $this->bbCurrencies) ? true : false);
    }

    function get_merchant_account($selected_currency) {
        $payment_methods_instance_id = 0;
        $payment_gateway_instance_select_sql = "SELECT pmi.payment_methods_instance_id, pmi.payment_methods_instance_follow_default, pmi.currency_code 
				 								FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " as pmi
				 								WHERE pmi.payment_methods_id = '" . (int) $this->payment_methods_id . "'
				 								LIMIT 1";
        $payment_gateway_instance_result_sql = tep_db_query($payment_gateway_instance_select_sql);
        if (tep_db_num_rows($payment_gateway_instance_result_sql)) {
            $payment_gateway_instance_select_sql = "SELECT pmi.payment_methods_instance_id, pmi.payment_methods_instance_follow_default 
					 								FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " as pmi
					 								WHERE pmi.currency_code = '" . tep_db_input($selected_currency) . "' 
					 									AND pmi.payment_methods_id = '" . (int) $this->payment_methods_id . "'";
            $payment_gateway_instance_result_sql = tep_db_query($payment_gateway_instance_select_sql);
            if ($payment_gateway_instance_row = tep_db_fetch_array($payment_gateway_instance_result_sql)) {
                if ((int) $payment_gateway_instance_row['payment_methods_instance_follow_default'] > 0) { // Follow Default Currency Setting
                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_follow_default'];
                } else { // Has own setting
                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_id'];
                }
            }
        } else {
            $payment_gateway_instance_select_sql = "SELECT pmi.payment_methods_instance_id, pmi.payment_methods_instance_follow_default 
					 								FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " as pmi
					 								INNER JOIN " . TABLE_PAYMENT_METHODS . " as pm
					 									ON pm.payment_methods_parent_id = pmi.payment_methods_id
					 								WHERE pmi.currency_code = '" . tep_db_input($selected_currency) . "' 
					 									AND pm.payment_methods_id = '" . (int) $this->payment_methods_id . "'";
            $payment_gateway_instance_result_sql = tep_db_query($payment_gateway_instance_select_sql);
            if ($payment_gateway_instance_row = tep_db_fetch_array($payment_gateway_instance_result_sql)) {
                if ((int) $payment_gateway_instance_row['payment_methods_instance_follow_default'] > 0) {
                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_follow_default'];
                } else {
                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_id'];
                }
            }
        }
        $payment_gateway_instance_setting_select_sql = "SELECT pmis.payment_methods_instance_setting_key, pmis.payment_methods_instance_setting_value 
						 								FROM " . TABLE_PAYMENT_METHODS_INSTANCE_SETTING . " as pmis
						 								WHERE pmis.payment_methods_instance_id = '" . (int) $payment_methods_instance_id . "'";
        $payment_gateway_instance_setting_result_sql = tep_db_query($payment_gateway_instance_setting_select_sql);
        while ($payment_gateway_instance_setting_row = tep_db_fetch_array($payment_gateway_instance_setting_result_sql)) {
            switch ($payment_gateway_instance_setting_row['payment_methods_instance_setting_key']) {
                case 'MODULE_PAYMENT_BIBIT_MERCHANT_CODE':
                    $this->merchantCode = $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];
                    break;
                case 'MODULE_PAYMENT_BIBIT_MERCHANT_PASSWORD':
                    $this->merchantPassword = $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];
                    break;
                case 'MODULE_PAYMENT_BIBIT_ADMIN_CODE':
                    $this->adminCode = $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];
                    break;
                case 'MODULE_PAYMENT_BIBIT_MAC_KEY':
                    $this->macKey = $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];
                    break;
                case 'MODULE_PAYMENT_BIBIT_PRICE_MIN':
                    $this->minPrice = $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];
                    break;
                case 'MODULE_PAYMENT_BIBIT_PRICE_MAX':
                    $this->maxPrice = $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];
                    break;
            }
        }
    }

    function get_bb_exponent($currency_code) {
        $exponent_select_sql = "SELECT bb_exponent 
    							FROM " . TABLE_BIBIT_CURRENCIES . " 
    							WHERE bb_currID = '" . tep_db_input($currency_code) . "'";
        $exponent_result_sql = tep_db_query($exponent_select_sql);
        $exponent_row = tep_db_fetch_array($exponent_result_sql);

        return (int) $exponent_row['bb_exponent'];
    }

    function check_trans_status($order_id) {
        global $currencies;
        $result_array = array('');

        $order_info_select_sql = "	SELECT o.payment_methods_id, o.currency, o.currency_value, ot.value, ot.text 
									FROM " . TABLE_ORDERS . " AS o 
									INNER JOIN " . TABLE_ORDERS_TOTAL . " AS ot 
										ON o.orders_id=ot.orders_id AND ot.class='ot_total' 
									WHERE o.orders_id = '" . tep_db_input($order_id) . "'";
        $order_info_result_sql = tep_db_query($order_info_select_sql);
        if ($order_info_row = tep_db_fetch_array($order_info_result_sql)) {
            if ($order_info_row['payment_methods_id'] != $this->payment_methods_id)
                return;

            $bibit_url = $this->api_endpoint_domain;

            $this->get_merchant_account($order_info_row['currency']);

            $this->xml =
                    '<?xml version="1.0"?>
                <!DOCTYPE paymentService PUBLIC "-//WorldPay//DTD  WorldPay PaymentService v1//EN" "http://dtd.worldpay.com/paymentService_v1.dtd"> 
				<paymentService version="1.4" merchantCode="' . $this->merchantCode . '">
					<inquiry>
						<orderInquiry orderCode="' . $order_id . '" />
					</inquiry>
				</paymentService>';

            $bibit_curl_url = 'https://' . $bibit_url . '/jsp/merchant/xml/paymentService.jsp';
            $result_array = $this->curl_connect($this->merchantCode . ':' . $this->merchantPassword, $bibit_curl_url, $this->xml);

            $path_info_array = pathinfo($_SERVER['SCRIPT_FILENAME']);

            // Validate amount
            //$bibit_mapping_array = array('VISA_ELECTRON-SSL' => 'VISA-SSL');
            $bibit_mapping_array = array();
            $bibit_return_system_code = $result_array['paymentMethod'];
            if (isset($bibit_mapping_array[$bibit_return_system_code])) {
                $bibit_return_system_code = $bibit_mapping_array[$bibit_return_system_code];
            }

            if (isset($result_array['lastEvent'])) {
                $bb_exponent = $this->get_bb_exponent($result_array['currencyCode']);

                $bibit_return_amt = $result_array['amountValue'];

                if ($bb_exponent > 0) {
                    $bibit_return_amt = ($result_array['amountValue'] / pow(10, $bb_exponent));
                }

                $bb_payment_data_array = array('bibit_status' => $result_array['lastEvent'],
                    'bibit_payment_method' => $result_array['paymentMethod']
                );

                if (strtoupper($result_array['lastEvent']) == 'AUTHORISED' || strtoupper($result_array['lastEvent']) == 'CAPTURED') { // Bibit returned after fee amount when status is SETTLED / REFUNDED
                    $bb_payment_data_array['bibit_currency'] = $result_array['currencyCode'];
                    $bb_payment_data_array['bibit_amount'] = $bibit_return_amt;
                }

                if (isset($result_array['CVCResultCode'])) {
                    $bb_payment_data_array['bibit_cvc_status'] = $result_array['CVCResultCode'];
                }

                if (isset($result_array['cardNumber'])) {
                    $bb_payment_data_array['bibit_card_number'] = $result_array['cardNumber'];
                }

                if (isset($result_array['AVSResultCode'])) {
                    $bb_payment_data_array['bibit_avs_status'] = $result_array['AVSResultCode'];
                }

                if (isset($result_array['riskScore'])) {
                    $bb_payment_data_array['bibit_risk_score'] = $result_array['riskScore'];
                }

                if (isset($result_array['ThreeDSecureResult'])) {
                    $bb_payment_data_array['bibit_cardholder_aut_result'] = $result_array['ThreeDSecureResult'];
                }

                $bibit_select_sql = "SELECT orders_id FROM " . TABLE_BIBIT . " WHERE orders_id='" . tep_db_input($order_id) . "'";
                $bibit_result_sql = tep_db_query($bibit_select_sql);

                if (tep_db_num_rows($bibit_result_sql)) {
                    tep_db_perform(TABLE_BIBIT, $bb_payment_data_array, 'update', "orders_id = '" . tep_db_input($order_id) . "'");
                } else {
                    $bb_payment_data_array['orders_id'] = $order_id;
                    tep_db_perform(TABLE_BIBIT, $bb_payment_data_array);
                }

                $bb_payment_history_data_array = array('bb_status' => $result_array['lastEvent'],
                    'bb_err_txt' => 'OK'
                );
                if ($bibit_return_system_code != $this->code) {
                    $bb_payment_history_data_array['bb_err_no'] = '0';
                    $bb_payment_history_data_array['bb_err_txt'] = 'Payment Method not matched, ' . $bibit_return_system_code . ' != ' . $this->code;
                } else if (isset($result_array['ISO8583ReturnCode'])) {
                    $bb_payment_history_data_array['bb_err_no'] = $result_array['ISO8583ReturnCode'];
                    $bb_payment_history_data_array['bb_err_txt'] = $result_array['ISO8583ReturnCodeDesc'];
                } else if (isset($result_array['errorCode']) && tep_not_null($result_array['errorCode'])) {
                    $bb_payment_history_data_array['bb_err_no'] = $result_array['errorCode'];
                    $bb_payment_history_data_array['bb_err_txt'] = $result_array['errorText'];
                } else {
                    $bb_payment_history_data_array['bb_err_txt'] = 'OK';
                }

                if ($result_array['lastEvent'] == 'AUTHORISED' || $result_array['lastEvent'] == 'CAPTURED') {
                    //check the payment_amount
                    $bb_gross_amt = number_format($bibit_return_amt, $currencies->currencies[$result_array['currencyCode']]['decimal_places'], $currencies->currencies[$result_array['currencyCode']]['decimal_point'], $currencies->currencies[$result_array['currencyCode']]['thousands_point']);

                    if ($currencies->currencies[$result_array['currencyCode']]['symbol_left'] . $bb_gross_amt . $currencies->currencies[$result_array['currencyCode']]['symbol_right'] == strip_tags($order_info_row['text']) && $order_info_row['currency'] == $result_array['currencyCode']) {
                        ob_start();
                        echo "<pre>";
                        print_r($result_array);
                        $debug_html = ob_get_contents();
                        ob_end_clean();

                        if (isset($result_array['errorCode']) && tep_not_null($result_array['errorCode'])) {
                            @tep_mail('<EMAIL>', '<EMAIL>', $order_id . ' - Bibit order authorized with error code', $debug_html, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
                        }
                        $this->check_trans_status_flag = true;
                    }
                }
            } else {
                $bb_payment_history_data_array = array('bb_err_no' => $result_array['errorCode'],
                    'bb_err_txt' => $result_array['errorText']
                );
            }

            $bb_payment_history_data_array['orders_id'] = $order_id;
            $bb_payment_history_data_array['bb_date'] = 'now()';

            $bb_payment_history_data_array['changed_by'] = (isset($path_info_array["basename"]) && $path_info_array["basename"] == 'cron_order_payment.php' ? 'cronjob' : (isset($_SESSION['login_email_address']) ? $_SESSION['login_email_address'] : 'system' ) );

            tep_db_perform(TABLE_BIBIT_PAYMENT_STATUS_HISTORY, $bb_payment_history_data_array);
        }

        return $result_array;
    }

    function post_capture($order_id, $capture_by = '') {
        global $login_email_address;

        if (tep_not_null($capture_by)) {
            $changed_by = $capture_by;
            $comments_type = 0;
        } else {
            $changed_by = $login_email_address;
            $comments_type = 1;
        }

        $result = array();

        $bibit_url = $this->api_endpoint_domain;

        $bibit_info_select_sql = "	SELECT bibit_status, bibit_currency, bibit_amount 
									FROM " . TABLE_BIBIT . " 
									WHERE orders_id = '" . tep_db_input($order_id) . "'";
        $bibit_info_result_sql = tep_db_query($bibit_info_select_sql);

        if ($bibit_info_row = tep_db_fetch_array($bibit_info_result_sql)) {
            if (trim($bibit_info_row['bibit_status']) == 'AUTHORISED') {
                $this->get_merchant_account($bibit_info_row['bibit_currency']);

                $bb_exponent = $this->get_bb_exponent($bibit_info_row['bibit_currency']);

                $bibit_amt = preg_replace('/[^\d]/', '', number_format($bibit_info_row['bibit_amount'], $bb_exponent));

                $this->xml =
                        '<?xml version="1.0"?>
                <!DOCTYPE paymentService PUBLIC "-//WorldPay//DTD WorldPay PaymentService v1//EN" "http://dtd.worldpay.com/paymentService_v1.dtd">
				<paymentService merchantCode="' . $this->merchantCode . '" version="1.4">
					<modify>
						<orderModification orderCode="' . $order_id . '">
							<capture>
								<amount value="' . $bibit_amt . '" currencyCode="' . $bibit_info_row['bibit_currency'] . '" exponent="' . $bb_exponent . '" debitCreditIndicator="credit"/>
							</capture>
						</orderModification>
					</modify>
				</paymentService>';

                $bibit_curl_url = 'https://' . $bibit_url . '/jsp/merchant/xml/paymentService.jsp';
                $result_array = $this->curl_connect($bibit_curl_url, $this->xml);

                if (isset($result_array['CaptureReceived'])) {
                    $bibit_capture_update_sql = "	UPDATE " . TABLE_BIBIT . " 
													SET bibit_capture_request = 1 
													WHERE orders_id = '" . tep_db_input($order_id) . "'";
                    tep_db_query($bibit_capture_update_sql);

                    $order_history_data_array = array('orders_id' => $order_id,
                        'orders_status_id' => '',
                        'date_added' => 'now()',
                        'customer_notified' => 0,
                        'comments' => 'Remotely post captured this order',
                        'comments_type' => $comments_type,
                        'set_as_order_remarks' => '0',
                        'changed_by' => $changed_by
                    );
                    tep_db_perform(TABLE_ORDERS_STATUS_HISTORY, $order_history_data_array);
                    tep_update_orders_status_counter($order_history_data_array);

                    $result = array('code' => 'success',
                        'text' => SUCCESS_ORDER_POST_CAPTURED);
                }
            } else if (trim($bibit_info_row['bibit_status']) == 'CAPTURED') {
                $result = array('code' => 'error',
                    'text' => ERROR_ORDER_ALREADY_CAPTURED);
            } else {
                $result = array('code' => 'error',
                    'text' => ERROR_ORDER_CANNOT_BE_CAPTURED);
            }
        } else {
            $result = array('code' => 'error',
                'text' => ERROR_ORDER_DOES_NOT_EXIST);
        }

        return $result;
    }

    function do_refund($order_id, $reference_id) {
        global $currencies;

        $result = array();

        $bibit_url = $this->api_endpoint_domain;

        $bibit_info_select_sql = "	SELECT bibit_status 
									FROM " . TABLE_BIBIT . " 
									WHERE orders_id = '" . tep_db_input($order_id) . "'";
        $bibit_info_result_sql = tep_db_query($bibit_info_select_sql);

        if ($bibit_info_row = tep_db_fetch_array($bibit_info_result_sql)) {
            if (trim($bibit_info_row['bibit_status']) == 'CAPTURED') {
                $order_info_select_sql = "	SELECT sr.store_refund_is_processed, sr.store_refund_amount, o.currency, o.currency_value 
                                            FROM " . TABLE_ORDERS . " AS o 
                                            INNER JOIN " . TABLE_STORE_REFUND . " AS sr 
                                                ON sr.store_refund_trans_id = o.orders_id 
                                            WHERE o.orders_id = '" . tep_db_input($order_id) . "' 
                                                AND sr.store_refund_id = '" . tep_db_input($reference_id) . "'";
                $order_info_result_sql = tep_db_query($order_info_select_sql);

                if ($order_info_row = tep_db_fetch_array($order_info_result_sql)) {
                    $this->get_merchant_account($order_info_row['currency']);

                    $bb_exponent = $this->get_bb_exponent($order_info_row['currency']);

                    $bibit_amt = preg_replace('/[^\d]/', '', number_format($currencies->apply_currency_exchange($order_info_row['store_refund_amount'], $order_info_row['currency'], $order_info_row['currency_value']), $bb_exponent));

                    $this->xml =
                            '<?xml version="1.0"?>
                    <!DOCTYPE paymentService PUBLIC "-//WorldPay//DTD WorldPay PaymentService v1//EN" "http://dtd.worldpay.com/paymentService_v1.dtd">
                    <paymentService merchantCode="' . $this->merchantCode . '" version="1.4">
                        <modify>
                            <orderModification orderCode="' . $order_id . '">
                                <refund>
                                    <amount value="' . $bibit_amt . '" currencyCode="' . $order_info_row['currency'] . '" exponent="' . $bb_exponent . '" debitCreditIndicator="credit"/>
                                </refund>
                            </orderModification>
                        </modify>
                    </paymentService>';

                    $bibit_curl_url = 'https://' . $bibit_url . '/jsp/merchant/xml/paymentService.jsp';
                    $result_array = $this->curl_connect($bibit_curl_url, $this->xml);

                    if (isset($result_array['RefundReceived'])) {
                        $store_refund_data_sql = array('store_refund_is_processed' => 1);
                        tep_db_perform(TABLE_STORE_REFUND, $store_refund_data_sql, 'update', " store_refund_id = '" . tep_db_input($reference_id) . "' ");

                        $result = array('code' => 'success',
                            'text' => 'Success refund');
                    }
                } else {
                    $result = array('code' => 'error',
                        'text' => 'Order is not in CAPTURED status');
                }
            } else {
                $result = array('code' => 'error',
                    'text' => 'Order is not in CAPTURED status');
            }
        } else {
            $result = array('code' => 'error',
                'text' => ERROR_ORDER_DOES_NOT_EXIST);
        }

        return $result;
    }

    function is_processed_order($order_id) {
        $payment_status_select_sql = "	SELECT bibit_status 
										FROM " . TABLE_BIBIT . " 
										WHERE orders_id = '" . tep_db_input($order_id) . "'";
        $payment_status_result_sql = tep_db_query($payment_status_select_sql);
        $payment_status_row = tep_db_fetch_array($payment_status_result_sql);

        if (in_array($payment_status_row['bibit_status'], array('AUTHORISED', 'CAPTURED', 'SETTLED'))) {
            return true;
        } else {
            return false;
        }
    }

    function get_merchant_code() {
        return $this->merchantCode;
    }

    function get_merchant_password() {
        return $this->merchantPassword;
    }

    function output_error() {
        return false;
    }

    function check() {
        if (!isset($this->_check)) {
            $payment_methods_select_sql = "	SELECT payment_methods_id
											FROM " . TABLE_PAYMENT_METHODS . " as pm
											WHERE pm.payment_methods_id = '" . (int) $this->payment_methods_id . "'";
            $payment_methods_result_sql = tep_db_query($payment_methods_select_sql);
            $this->_check = tep_db_num_rows($payment_methods_result_sql);
        }
        return $this->_check;
    }

    function load_pm_setting($language_id = 1, $pm_id = '') {
        $pm_setting_array = array();
        //load value from DB

        if (tep_not_null($pm_id)) {
            $payment_method_id = $pm_id;

            $payment_methods_parent_id_select_sql = "	SELECT payment_methods_parent_id 
														FROM " . TABLE_PAYMENT_METHODS . " 
														WHERE payment_methods_id = '" . (int) $pm_id . "'";
            $payment_methods_parent_id_result_sql = tep_db_query($payment_methods_parent_id_select_sql);
            $payment_methods_parent_id_row = tep_db_fetch_array($payment_methods_parent_id_result_sql);

            $payment_gateway_id = $payment_methods_parent_id_row['payment_methods_parent_id'];
        } else {
            $payment_method_id = $this->payment_methods_id;
            $payment_gateway_id = $this->payment_methods_parent_id;
        }

        $payment_configuration_info_select_sql = "	SELECT pci.payment_configuration_info_key, pcid.payment_configuration_info_value
													FROM " . TABLE_PAYMENT_CONFIGURATION_INFO . " as pci
													LEFT JOIN " . TABLE_PAYMENT_CONFIGURATION_INFO_DESCRIPTION . " as pcid 
														ON pci.payment_configuration_info_id = pcid.payment_configuration_info_id
															AND pcid.languages_id = '" . (int) $language_id . "'
													WHERE pci.payment_methods_id = '" . (int) $payment_method_id . "'
													ORDER BY pci.payment_configuration_info_sort_order";
        $payment_configuration_info_result_sql = tep_db_query($payment_configuration_info_select_sql);
        if (tep_db_num_rows($payment_configuration_info_result_sql)) {
            while ($payment_configuration_info_row = tep_db_fetch_array($payment_configuration_info_result_sql)) {
                $pm_setting_array[$payment_configuration_info_row['payment_configuration_info_key']] = $payment_configuration_info_row['payment_configuration_info_value'];
            }
        } else if ((int) $payment_gateway_id > 0) { //load value from PG
            $payment_configuration_info_select_sql = "	SELECT pci.payment_configuration_info_key, pcid.payment_configuration_info_value
														FROM " . TABLE_PAYMENT_CONFIGURATION_INFO . " as pci
														LEFT JOIN " . TABLE_PAYMENT_CONFIGURATION_INFO_DESCRIPTION . " as pcid 
															ON pci.payment_configuration_info_id = pcid.payment_configuration_info_id
																AND pcid.languages_id = '" . (int) $language_id . "'
														WHERE pci.payment_methods_id = '" . (int) $payment_gateway_id . "'
														ORDER BY pci.payment_configuration_info_sort_order";
            $payment_configuration_info_result_sql = tep_db_query($payment_configuration_info_select_sql);
            while ($payment_configuration_info_row = tep_db_fetch_array($payment_configuration_info_result_sql)) {
                $pm_setting_array[$payment_configuration_info_row['payment_configuration_info_key']] = $payment_configuration_info_row['payment_configuration_info_value'];
            }
        }

        return $pm_setting_array;
    }

    function install() {
        $install_configuration_flag = true;

        if ($this->payment_methods_id > 0) {
            if ($this->receive_status != 1) {
                $update_receive_status_sql_data = array("payment_methods_receive_status" => 1);
                tep_db_perform(TABLE_PAYMENT_METHODS, $update_receive_status_sql_data, 'update', "payment_methods_id = '" . (int) $this->payment_methods_id . "'");
            } else {
                $install_configuration_flag = false;
            }
        } else {
            $install_payment_gateway_data_sql = array('payment_methods_code' => $this->code,
                'payment_methods_send_status' => 0,
                'payment_methods_title' => $this->code,
                'payment_methods_types_id' => 0,
                'payment_methods_parent_id' => 0,
                'payment_methods_legend_color' => '#7C298A',
                'payment_methods_sort_order' => 4,
                'payment_methods_filename' => $this->filename,
                'payment_methods_logo' => '',
                'date_added' => 'now()',
                'last_modified' => 'now()',
                'payment_methods_receive_status' => '1',
                'payment_methods_receive_status_mode' => 0
            );
            tep_db_perform(TABLE_PAYMENT_METHODS, $install_payment_gateway_data_sql);
            $this->payment_methods_id = tep_db_insert_id();
        }

        if ($install_configuration_flag) {
            $install_key_array = array();
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Test Mode',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_BIBIT_TEST_MODE',
                    'payment_configuration_info_description' => 'Test Mode?',
                    'payment_configuration_info_sort_order' => '1160',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Set Order Status',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_BIBIT_ORDER_STATUS_ID',
                    'payment_configuration_info_description' => 'Set the status of orders made with this payment module to this value',
                    'payment_configuration_info_sort_order' => '1190',
                    'set_function' => 'tep_cfg_pull_down_order_statuses(',
                    'use_function' => 'tep_get_order_status_name',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => '0',
                    'languages_id' => 1
                )
            );
            /* 			$install_key_array[] = array(	'info' => array (	'payment_methods_id'=>(int)$this->payment_methods_id,
              'payment_configuration_info_title'=>'Payment Zone',
              'payment_configuration_info_key'=>'MODULE_PAYMENT_BIBIT_ZONE',
              'payment_configuration_info_description'=>'If a zone is selected, only enable this payment method for that zone.',
              'payment_configuration_info_sort_order'=>'1195',
              'set_function'=>'tep_cfg_pull_down_zone_classes(',
              'use_function'=>'tep_get_zone_class_title',
              'date_added'=>'now()'
              ),
              'desc' => array (	'payment_configuration_info_value'=>'0',
              'languages_id' => 1
              )
              ); */
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Payment Message',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_BIBIT_MESSAGE',
                    'payment_configuration_info_description' => 'Payment message will show up during checkout process (Bibit)',
                    'payment_configuration_info_sort_order' => '1200',
                    'set_function' => 'tep_cfg_textarea(',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'Thank you for shopping at SKC Store.',
                    'languages_id' => 1
                )
            );

            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Use Pre-Captured?',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_BIBIT_USEPRECAPTURE',
                    'payment_configuration_info_description' => 'Do you want to pre-captured payments? Default=False. You need to set in Bibit Merchant Interface before using it.',
                    'payment_configuration_info_sort_order' => '1210',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Payment Email Message',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_BIBIT_EMAIL_MESSAGE',
                    'payment_configuration_info_description' => 'Thank you for shopping at OffGamers.com.',
                    'payment_configuration_info_sort_order' => '1215',
                    'set_function' => 'tep_cfg_textarea(',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'Thank you for shopping at SKC Store.',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Confirm Complete (in days)',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_BIBIT_CONFIRM_COMPLETE',
                    'payment_configuration_info_description' => 'Select the default currency for the payment transactions. If the user selected currency is not available at moneybookers.com, this currency will be the payment currency.',
                    'payment_configuration_info_sort_order' => '1220',
                    'set_function' => '',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => '150',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Manual Post Captured Notification',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_BIBIT_MANUAL_CAPTURE_NOTIFICATION_EMAIL',
                    'payment_configuration_info_description' => 'Email address to which the notification email will be send to when someone manually capture Bibit payment.<br>(In "Name &lt;Email&gt;" format. Use \',\' as delimiter for multiple recipient)',
                    'payment_configuration_info_sort_order' => '1225',
                    'set_function' => '',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => '',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Require Address Information',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_BIBIT_MANDATORY_ADDRESS_FIELD',
                    'payment_configuration_info_description' => 'Set address field as required info during checkout.',
                    'payment_configuration_info_sort_order' => '1230',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Customer Payment Info',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_BIBIT_CUSTOMER_PAYMENT_INFO',
                    'payment_configuration_info_description' => 'Set to true if this Payment Method required customer input for Payment Information required customer payment info.',
                    'payment_configuration_info_sort_order' => '1235',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Require IC Information',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_BIBIT_MANDATORY_IC_FIELD',
                    'payment_configuration_info_description' => 'Set IC field as required info during checkout.',
                    'payment_configuration_info_sort_order' => '1240',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Require Contact Information',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_BIBIT_MANDATORY_CONTACT_FIELD',
                    'payment_configuration_info_description' => 'Set Contact field as required info during checkout.',
                    'payment_configuration_info_sort_order' => '1245',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );
            foreach ($install_key_array as $data) {
                tep_db_perform(TABLE_PAYMENT_CONFIGURATION_INFO, $data['info']);
                $payment_conf_id = tep_db_insert_id();

                $data['desc']['payment_configuration_info_id'] = (int) $payment_conf_id;
                tep_db_perform(TABLE_PAYMENT_CONFIGURATION_INFO_DESCRIPTION, $data['desc']);
            }
        }
        return 1;
    }

    function remove() {
        tep_db_query("DELETE FROM " . TABLE_CONFIGURATION . " WHERE configuration_key IN ('" . implode("', '", $this->keys()) . "')");
    }

    function keys() {
        return array('MODULE_PAYMENT_BIBIT_STATUS',
            'MODULE_PAYMENT_BIBIT_TEST_MODE',
            'MODULE_PAYMENT_BIBIT_ADMIN_CODE',
            'MODULE_PAYMENT_BIBIT_MERCHANT_CODE',
            'MODULE_PAYMENT_BIBIT_MERCHANT_PASSWORD',
            'MODULE_PAYMENT_BIBIT_MAC_KEY',
            'MODULE_PAYMENT_BIBIT_SORT_ORDER',
            'MODULE_PAYMENT_BIBIT_ORDER_STATUS_ID',
            'MODULE_PAYMENT_BIBIT_ZONE',
            'MODULE_PAYMENT_BIBIT_LEGEND_COLOUR',
            'MODULE_PAYMENT_BIBIT_USEPRECAPTURE',
            'MODULE_PAYMENT_BIBIT_MESSAGE',
            'MODULE_PAYMENT_BIBIT_EMAIL_MESSAGE',
            'MODULE_PAYMENT_BIBIT_CONFIRM_COMPLETE',
            'MODULE_PAYMENT_BIBIT_MANUAL_CAPTURE_NOTIFICATION_EMAIL');
    }

    function configuration_information_keys() {
        return array('MODULE_PAYMENT_BIBIT_TEST_MODE',
            'MODULE_PAYMENT_BIBIT_ORDER_STATUS_ID',
            //'MODULE_PAYMENT_BIBIT_ZONE',
            'MODULE_PAYMENT_BIBIT_USEPRECAPTURE',
            'MODULE_PAYMENT_BIBIT_MESSAGE',
            'MODULE_PAYMENT_BIBIT_EMAIL_MESSAGE',
            'MODULE_PAYMENT_BIBIT_CONFIRM_COMPLETE',
            'MODULE_PAYMENT_BIBIT_MANUAL_CAPTURE_NOTIFICATION_EMAIL',
            'MODULE_PAYMENT_BIBIT_MANDATORY_ADDRESS_FIELD',
            'MODULE_PAYMENT_BIBIT_CUSTOMER_PAYMENT_INFO',
            'MODULE_PAYMENT_BIBIT_MANDATORY_IC_FIELD',
            'MODULE_PAYMENT_BIBIT_MANDATORY_CONTACT_FIELD');
    }

    function multi_lang_configuration_info_keys() {
        return array('MODULE_PAYMENT_BIBIT_MESSAGE',
            'MODULE_PAYMENT_BIBIT_EMAIL_MESSAGE');
    }

    function merchant_information_keys() {
        include_once(DIR_WS_CLASSES . "payment_methods.php");

        return array(
            'MODULE_PAYMENT_BIBIT_ADMIN_CODE' => array("field" => 'text', "mapping" => "MODULE_PAYMENT_BIBIT_LNG_ADMIN_CODE"),
            'MODULE_PAYMENT_BIBIT_MERCHANT_CODE' => array("field" => 'text', "mapping" => "MODULE_PAYMENT_BIBIT_LNG_MERCHANT_CODE"),
            'MODULE_PAYMENT_BIBIT_MERCHANT_PASSWORD' => array("field" => 'text', "mapping" => "MODULE_PAYMENT_BIBIT_LNG_MERCHANT_PASSWORD"),
            'MODULE_PAYMENT_BIBIT_MAC_KEY' => array("field" => 'text', "mapping" => "MODULE_PAYMENT_BIBIT_LNG_MAC_KEY"),
            'MODULE_PAYMENT_BIBIT_PRICE_MIN' => array('field' => 'text', 'mapping' => 'MODULE_PAYMENT_BIBIT_LNG_PRICE_MIN'),
            'MODULE_PAYMENT_BIBIT_PRICE_MAX' => array('field' => 'text', 'mapping' => 'MODULE_PAYMENT_BIBIT_LNG_PRICE_MAX'),
            'MODULE_PAYMENT_TAX' => array("field" => 'select', 'mapping' => "MODULE_PAYMENT_TAX", "select" => payment_methods::get_tax_option())
        );
    }

    function startElement($parser, $name, $attrs) {
        global $globalresult, $currentTag;
        $currentTag = $name;

        switch (strtoupper($name)) {
            case "REFERENCE":
                $globalresult['referenceID'] = $attrs['ID'];
                break;
            case "ORDERSTATUS":
                $globalresult['orderCode'] = $attrs['ORDERCODE'];
                break;
            case "RISKSCORE":
                $globalresult['riskScore'] = $attrs['VALUE'];
                break;
            case "AUTHORISATIONID":
                $globalresult['authorisationId'] = $attrs['ID'];
                break;
            case "ERROR":
                $globalresult['errorCode'] = $attrs['CODE'];
                break;
            case "CVCRESULTCODE":
                $globalresult['CVCResultCode'] = $attrs['DESCRIPTION'];
                break;
            case "AVSRESULTCODE":
                $globalresult['AVSResultCode'] = $attrs['DESCRIPTION'];
                break;
            case "PAYMENTSERVICE":
                $globalresult['merchantCode'] = $attrs['MERCHANTCODE'];
                break;
            case "CAPTURERECEIVED":
                $globalresult['CaptureReceived'] = $attrs['ORDERCODE'];
                break;
            case "REFUNDRECEIVED":
                $globalresult['RefundReceived'] = $attrs['ORDERCODE'];
                break;
            case 'AMOUNT':
                $globalresult['currencyCode'] = $attrs['CURRENCYCODE'];
                $globalresult['amountValue'] = $attrs['VALUE'];
                break;
            case 'ISO8583RETURNCODE':
                $globalresult['ISO8583ReturnCode'] = 'ISO8583ReturnCode: ' . $attrs['CODE'];
                $globalresult['ISO8583ReturnCodeDesc'] = $attrs['DESCRIPTION'];
                break;
            case 'THREEDSECURERESULT':
                $globalresult['ThreeDSecureResult'] = $attrs['DESCRIPTION'];
                break;
            default:
                break;
        }
    }

    function characterData($parser, $result) {
        global $globalresult, $currentTag;
        // echo   "charData(parser,$result)   for   $currentTag <BR> ";

        switch (strtoupper($currentTag)) {
            case "LASTEVENT":
                $globalresult["lastEvent"] = $result;
                break;
            case "CARDNUMBER":
                $globalresult["cardNumber"] = $result;
                break;
            case "ERROR":
                $globalresult["errorText"] = $result;
                break;
            case "REFERENCE":
                $globalresult["redirectURL"] = $result;
                break;
            case "PAYMENTMETHOD":
                $globalresult["paymentMethod"] = $result;
                break;
            default:
                break;
        }
    }

    function endElement($parser, $name) {
        global $currentTag;
        $currentTag = "";
    }

    function ParseXML($bibitResult) {
        global $globalresult;

        $globalresult = array();
        //array($this, 'format_character_name')
        $xml_parser = xml_parser_create();
        // set callback functions
        xml_set_element_handler($xml_parser, array($this, 'startElement'), array($this, 'endElement'));
        xml_set_character_data_handler($xml_parser, array($this, 'characterData'));

        if (!xml_parse($xml_parser, $bibitResult)) {
            die(sprintf("XML error: %s at line %d", xml_error_string(xml_get_error_code($xml_parser)), xml_get_current_line_number($xml_parser)));
        }
        // clean up
        xml_parser_free($xml_parser);

        return $globalresult;
    }

    function curl_connect($url, $data) {
        $ch = curl_init($url);

        $agent = "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.0)";

        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data); //$xml is the xml string
        curl_setopt($ch, CURLOPT_HTTPHEADER, Array("Content-Type: text/xml"));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_NOPROGRESS, 0);
        curl_setopt($ch, CURLOPT_VERBOSE, 1);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 2);
        curl_setopt($ch, CURLOPT_TIMEOUT, 120);
        curl_setopt($ch, CURLOPT_USERAGENT, $agent);
        curl_setopt($ch, CURLOPT_HEADER, 0);
        curl_setopt($ch, CURLOPT_USERPWD, $this->merchantCode.':'.$this->merchantPassword);
        curl_setopt($ch, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
        if ($this->connect_via_proxy) {
            curl_setopt($ch, CURLOPT_PROXY, 'http://mvy-proxy:3128');
            curl_setopt($ch, CURLOPT_PROXYUSERPWD, 'devbackend:devonly');
        }
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);  //Windows 2003 Compatibility

        $response = curl_exec($ch);
        curl_close($ch);

        $result_array = $this->ParseXML($response);

        return $result_array;
    }

    function get_orders_payment_info_file() {
        return DIR_FS_CATALOG_MODULES . 'payment/bibit/admin/orders.inc.php';
    }

    function get_orders_list_payment_info_file() {
        return DIR_FS_CATALOG_MODULES . 'payment/bibit/admin/orders_list.inc.php';
    }

    function get_ipn_class_file() {
        return DIR_FS_CATALOG_MODULES . 'payment/bibit/classes/bibit_ipn_class.php';
    }

}

?>
