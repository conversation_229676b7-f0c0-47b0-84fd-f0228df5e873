<?
define('TABLE_HEADING_BB_DATE', 'History Date');
define('TABLE_HEADING_BB_STATUS', 'Status');
define('TABLE_HEADING_BB_CHANGED_BY', 'Changed By');

define('ENTRY_BB_PAYMENT_TYPE', 'Payment Type');
define('ENTRY_BB_CURRENCY', 'Currency');
define('ENTRY_BB_AMT', 'Amount');
define('ENTRY_BB_CVC_RESULT', 'CVC Result');
define('ENTRY_BB_AVS_RESULT', 'AVS Result');
define('ENTRY_BB_RISK_SCORE', 'Risk Score');
define('ENTRY_BB_PAYMENT_ID', 'Payment Id');
define('ENTRY_BB_MAC', 'Mac');
define('ENTRY_BB_PAYMENT_STATUS', 'Payment Status');

define('TEXT_STATUS_UNKNOWN', 'Unknown');

//key language
define('MODULE_PAYMENT_BIBIT_LNG_ADMIN_CODE', 'Admin Code');
define('MODULE_PAYMENT_BIBIT_LNG_MERCHANT_CODE', 'Merchant Code');
define('MODULE_PAYMENT_BIBIT_LNG_MERCHANT_PASSWORD', 'Merchant Password');
define('MODULE_PAYMENT_BIBIT_LNG_MAC_KEY', 'Mac Key');
define('MODULE_PAYMENT_BIBIT_LNG_PRICE_MIN', 'Minimun Price');
define('MODULE_PAYMENT_BIBIT_LNG_PRICE_MAX', 'Maximun Price');
define('MODULE_PAYMENT_TAX', 'Tax');

?>