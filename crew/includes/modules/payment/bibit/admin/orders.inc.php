<?
include_once(DIR_FS_CATALOG_MODULES . 'payment/bibit/admin/languages/' . $language . '/bibit.lng.php');

$bb_trans_info_select_sql = "SELECT * FROM " . TABLE_BIBIT . " WHERE orders_id='" . (int)$oID . "'";
$bb_trans_info_result_sql= tep_db_query($bb_trans_info_select_sql);

$bb_payment_status_history_info_select_sql = "SELECT * FROM " . TABLE_BIBIT_PAYMENT_STATUS_HISTORY . " WHERE orders_id = '" . tep_db_input($oID) . "'";
$bb_payment_status_history_info_result_sql = tep_db_query($bb_payment_status_history_info_select_sql);
?>
<table border="0" width="100%" cellspacing="0" cellpadding="2">
<? ob_start(); 

?>
	<tr>
		<td class="main">
      		<table border="0" width="100%" cellspacing="0" cellpadding="2">
      			<tr>
      				<td class="main" width="12%" nowrap><b><?=ENTRY_PAYMENT_METHOD?></b></td>
      				<td class="main">
      					<div>
      						<? if((int)$order->info['payment_methods_id']>0) { ?><div class="<?=((int)$payment_info_array[$order->info['payment_methods_id']]->confirm_complete_days>0?'rp_icon':'nrp_icon')?>"><b><?=((int)$payment_info_array[$order->info['payment_methods_id']]->confirm_complete_days>0?'RP':'NRP')?></b></div> <? } ?>
      						<div style="width:30px; height:15px; background-color:<?=$payment_module_info['display_colour']?>; float:left; vertical-align:top; text-align:center;">&nbsp;</div>
      						<div style="vertical-align: top">
      							&nbsp;&nbsp;
<?
	if ($view_payment_details_permission) {
		if (tep_not_null($last_payment_method) && $last_payment_method['payment_methods_id'] != $order->info['payment_methods_id']) {
			echo '<span class="redIndicator">'.(tep_not_null($payment_module_info['payment_methods_parent_title'])?$payment_module_info['payment_methods_parent_title'] . ' - ' :'') . $payment_module_info['payment_methods_title'].'</span>';
		} else {
			echo (tep_not_null($payment_module_info['payment_methods_parent_title'])?$payment_module_info['payment_methods_parent_title'] . ' - ' :'') . $payment_module_info['payment_methods_title'];
		}
		
		if (tep_not_null($last_payment_method) && $last_payment_method['payment_methods_id'] != $order->info['payment_methods_id']) {
			echo '&nbsp;&nbsp;<span class="redIndicator">(Last Payment Method: ' . (tep_not_null($last_payment_method['parent_payment_methods_title'])? $last_payment_method['parent_payment_methods_title'] . ' - ' : '' ) . $last_payment_method['payment_methods_title'] . ')</span>';
		}
	} else {
		echo (tep_not_null($payment_module_info['payment_methods_parent_title'])?$payment_module_info['payment_methods_parent_title'] . ' - ' :'') . $payment_module_info['payment_methods_title'];
	}
?>
							</div>
      					</div>
      				</td>
				</tr>
			</table>
		</td>
	</tr>
<?
$payment_method_title_info = ob_get_contents();
ob_end_clean();

if (tep_db_num_rows($bb_trans_info_result_sql) || tep_db_num_rows($bb_payment_status_history_info_result_sql)) {
	$bb_trans_info_row = tep_db_fetch_array($bb_trans_info_result_sql);
	
	echo $payment_method_title_info;
	
	if (!$view_payment_details_permission) {
		;
	} else {
		$bb_exponent_select_sql = "SELECT bb_exponent FROM " . TABLE_BIBIT_CURRENCIES . " WHERE bb_currID = '" . tep_db_input($bb_trans_info_row['bibit_currency']) . "' LIMIT 1";
		$bb_exponent_result_sql = tep_db_query($bb_exponent_select_sql);
		$bb_exponent_row = tep_db_fetch_array($bb_exponent_result_sql);
		
		$payment_methods_id_select_sql = "	SELECT pm.payment_methods_id 
											FROM " . TABLE_PAYMENT_METHODS . " AS pm
											INNER JOIN " . TABLE_PAYMENT_METHODS . " AS pg 
												ON (pm.payment_methods_parent_id = pg.payment_methods_id AND pg.payment_methods_filename = '" . tep_db_input('bibit.php') . "') 
											WHERE pm.payment_methods_code = '" . tep_db_input($bb_trans_info_row['bibit_payment_method']) . "' 
											LIMIT 1";
		$payment_methods_id_result_sql = tep_db_query($payment_methods_id_select_sql);
		$payment_methods_id_row = tep_db_fetch_array($payment_methods_id_result_sql);
?>
  	<tr>
    	<td class="main">
      		<table border="0" width="100%" cellspacing="0" cellpadding="2">
      			<tr valign="top">
        			<td width="39%" nowrap>
              			<table border="0" cellspacing="0" cellpadding="2">
              				<tr>
                				<td class="main" valign="top" nowrap><b><?="Payment Type:"?></b>&nbsp;</td>
                				<td class="main" nowrap>
                				<?
                					if ($payment_methods_id_row['payment_methods_id'] == $order->info['payment_methods_id']) {
                						echo $bb_trans_info_row['bibit_payment_method'];
                					} else {
                						echo '<span class="redIndicator">'.$bb_trans_info_row['bibit_payment_method'].'<br><span class="smallText">Does not match checkout payment method.<br>'.$bb_trans_info_row['bibit_payment_method'].' is the actual paid payment method.</span></span>';
                					}
                				?>
                				</td>
              				</tr>
              				<tr>
                				<td class="main" valign="top" nowrap><b><?="Payment ID:"?></b>&nbsp;</td>
                				<td class="main" nowrap><?=(tep_not_null($bb_trans_info_row['bibit_payment_id']) ? $bb_trans_info_row['bibit_payment_id'] : TEXT_NOT_AVAILABLE)?></td>
              				</tr>
              				<tr>
                				<td class="main" valign="top" nowrap><b><?="Mac:"?></b>&nbsp;</td>
                				<td class="main" nowrap><?=$bb_trans_info_row['bibit_mac']?></td>
              				</tr>
              				<tr>
                				<td class="main" valign="top" nowrap><b><?="Credit Card No:"?></b>&nbsp;</td>
                				<td class="main" nowrap>
<?
								if (tep_not_null($bb_trans_info_row['bibit_card_number'])) {
									echo $bb_trans_info_row['bibit_card_number'];
									
									$check_credit_card_array = array();
									$check_credit_card_select_sql = "	SELECT o.customers_id
																		FROM " . TABLE_ORDERS . " AS o 
																		INNER JOIN " . TABLE_BIBIT . " AS b 
																			ON o.orders_id = b.orders_id
																		WHERE b.bibit_card_number = '" . tep_db_input($bb_trans_info_row['bibit_card_number']) . "'
																		GROUP BY o.customers_id";
									$check_credit_card_result_sql = tep_db_query($check_credit_card_select_sql);
									while ($check_credit_card_row = tep_db_fetch_array($check_credit_card_result_sql)) {
										$check_credit_card_array[] = $check_credit_card_row['customers_id'];
									}
									if (count($check_credit_card_array)>1) {
            							echo 	tep_draw_form('cust_lists_share_cc', FILENAME_CUSTOMERS, 'action=show_report&customer_lists_subaction=do_search', 'post', 'target="_blank"') . "\n" . 
            							tep_draw_hidden_field('customer_share_id', tep_array_serialize($check_credit_card_array)) . "\n" . 
            									"<BR><span class='redIndicator'>Same credit card used in <a href='javascript:document.cust_lists_share_cc.submit();'>" . count($check_credit_card_array) . "</a> profiles.</span>" . 
												'</form>' . "\n";
									}
									$cc_check_info = array(	'card_number' => $bb_trans_info_row['bibit_card_number'],
															'date_purchased' => $order->info['date_purchased'],
															'orders_id' => $order->order_id,
															'customers_id' => $order->customer['id']);
									$cc_verified_date = tep_get_payment_info_verified_date($cc_check_info, 'credit_card');
									echo "<br><span class='redIndicator'>First successful verified date by this user: ".(tep_not_null($cc_verified_date)?$cc_verified_date:"NEVER")."</span><br>(Data can only traced back to 2009-12-09)</span>";
								} else {
									echo "N/A";
								}
?>
								</td>
              				</tr>
              				<tr>
                				<td class="main" valign="top" nowrap><b><?="Cardholder Authentication Result:"?></b>&nbsp;</td>
                				<td class="main" nowrap><?=$bb_trans_info_row['bibit_cardholder_aut_result']?></td>
              				</tr>
              			</table>
        			</td>
        			<td>
        				<table border="0" cellspacing="0" cellpadding="1">
        					<tr>
                				<td class="main" valign="top" nowrap><b><?="Payment Status:"?></b>&nbsp;</td>
                				<td class="main" nowrap>
                				<?
                					if (tep_not_null($bb_trans_info_row['bibit_status'])) {
                						echo $bb_trans_info_row['bibit_status'];
                					} else {
                						echo TEXT_STATUS_UNKNOWN;
                					}
									
                				//	if ($order->info['orders_status'] == '1' || $order->info['orders_status'] == '7' || $order->info['orders_status'] == '2') {
	                					echo "&nbsp;&nbsp;";
										echo tep_draw_form('bb_status_check_form', FILENAME_ORDERS, tep_get_all_get_params(), 'post');
										echo tep_draw_hidden_field('subaction', 'payment_action');
										echo tep_draw_hidden_field('payment_action', 'check_trans_status');
										echo '<input type="submit" name="BBCheckTransStatusBtn" value="Check Payment Status" title="Check Payment Status" class="inputButton">';
										echo "</form>";
								//	}
								?>
                				</td>
              				</tr>
        					<tr>
        						<td colspan="2">
									<table border="1" cellspacing="0" cellpadding="2">
		        						<tr>
		        							<td class="smallText" nowrap><b><?=TABLE_HEADING_BB_DATE?></b></td>
		        							<td class="smallText" nowrap><b><?=TABLE_HEADING_BB_STATUS?></b></td>
		        							<td class="smallText" nowrap><b><?=TABLE_HEADING_BB_CHANGED_BY?></b></td>
		        						</tr>
<?		while ($bb_payment_status_history_info_row = tep_db_fetch_array($bb_payment_status_history_info_result_sql)) {
        	echo ' 						<tr>
                							<td class="smallText" nowrap>'.$bb_payment_status_history_info_row['bb_date'].'</td>
                							<td class="smallText" nowrap>'.($bb_payment_status_history_info_row['bb_err_no'] === '0' ? $bb_payment_status_history_info_row['bb_err_txt'] . ' - ' . $bb_payment_status_history_info_row['bb_status'] : $bb_payment_status_history_info_row['bb_err_no'] . ' - ' . $bb_payment_status_history_info_row['bb_err_txt']).'</td>
                							<td class="smallText" nowrap>'.(tep_not_null($bb_payment_status_history_info_row['changed_by'])?$bb_payment_status_history_info_row['changed_by']:'&nbsp;').'</td>
                						</tr>';
     	}
?>
									</table>
								</td>
        					</tr>
        				</table>
        			</td>
        			<td>
        				<table border="0" cellspacing="0" cellpadding="2">
        					<tr>
                				<td class="main" valign="top" nowrap><b><?="Payment Currency:"?></b>&nbsp;</td>
                				<td class="main" nowrap>
                				<?
                					if (strtolower($bb_trans_info_row['bibit_currency']) == strtolower($order->info['currency'])) {
                						echo $bb_trans_info_row['bibit_currency'];
                					} else {
                						echo '<span class="redIndicator">'.$bb_trans_info_row['bibit_currency'].'<br><span class="smallText">Does not match purchase currency.</span></span>';
                					}
                				?>
                				</td>
              				</tr>
              				<tr>
                				<td class="main" valign="top" nowrap><b><?="Payment Amount:"?></b>&nbsp;</td>
                				<td class="main">
                				<?
                					$mc_gross_display_text = $bb_trans_info_row['bibit_amount'];
                					$bb_gross_amt = number_format($bb_trans_info_row['bibit_amount'], $currencies->currencies[$order->info["currency"]]['decimal_places'], $currencies->currencies[$order->info["currency"]]['decimal_point'], $currencies->currencies[$order->info["currency"]]['thousands_point']);
                					
                					if ($currencies->currencies[$order->info["currency"]]['symbol_left'].$bb_gross_amt.$currencies->currencies[$order->info["currency"]]['symbol_right'] != strip_tags($order->order_totals['ot_total']['text']) ) {
                						$mc_gross_display_text = '<span class="redIndicator">'.$bb_trans_info_row['bibit_amount'].'<br><span class="smallText">Does not match purchase amount.</span></span>';
                					}
                					
                					echo $mc_gross_display_text;
                				?>
                				</td>
              				</tr>
              				<tr>
              					<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
              				</tr>
              				<tr>
              					<td>
              						<table border="1" cellspacing="0" cellpadding="2">
              							<tr>
			                				<td class="main" valign="top" nowrap><b><?="CVC result:"?></b>&nbsp;</td>
			                				<td class="main"><?=(tep_not_null($bb_trans_info_row['bibit_cvc_status']) ? $bb_trans_info_row['bibit_cvc_status'] : TEXT_NOT_AVAILABLE)?></td>
			              				</tr>
              							<tr>
			                				<td class="main" valign="top" nowrap><b><?="AVS result:"?></b>&nbsp;</td>
			                				<td class="main"><?=(tep_not_null($bb_trans_info_row['bibit_avs_status']) ? $bb_trans_info_row['bibit_avs_status'] : TEXT_NOT_AVAILABLE)?></td>
			              				</tr>
			              				<tr>
			                				<td class="main" valign="top" nowrap><b><?="Risk Score:"?></b>&nbsp;</td>
			                				<td class="main">
			                				<?
			                					$risk_class = '';
			                					
			                					if ($bb_trans_info_row['bibit_risk_score'] >= 50) {
			                						$risk_class = 'redIndicator';
			                					}
			                					
			                					echo (tep_not_null($bb_trans_info_row['bibit_risk_score']) ? '<span class="'.$risk_class.'">' . $bb_trans_info_row['bibit_risk_score'] . '</span>' : TEXT_NOT_AVAILABLE);
			                				?>
			                				</td>
			              				</tr>
              						</table>
              					</td>
              				</tr>
              			</table>
        			</td>
      			</tr>
      			<tr>
    				<td class="main" nowrap colspan="2" valign="top">
    					<table border="0" width="100%" cellspacing="0" cellpadding="2">
    						<tr>
            				<?	//only take care of showing Post-Captured button if it is in Verifying and was Pre-Captured
            					if ($order->info['orders_status'] == '7' && $bb_trans_info_row['bibit_status'] == 'AUTHORISED') {
            						$button_displayed = false;
            						
            						$manual_authorise_permission = tep_admin_files_actions(FILENAME_ORDERS, 'ADM_WORLDPAY_MANUAL_AUTHORISATION');
            						echo '	<td width="140px" class="main">';
            						if ($manual_authorise_permission) {
                						echo tep_draw_form('manual_post_auth_order', FILENAME_ORDERS, tep_get_all_get_params(), 'post');
                						echo tep_draw_hidden_field('subaction', 'manual_post_authorisation');
                						echo '<input type="submit" name="ManualPostCaptureBtn" value="Manual Captured" title="Post Captured Manually" class="inputButton" onClick="if (confirm(\'Are you sure that you had post captured this order via the Bibit Merchant Interface?\')) { openNewWin(\'https://secure.worldpay.com/sso/public/auth/login.html?serviceIdentifier=merchantadmin\', \'WorldPay Admin\', \'location=1,status=1,menubar=1,resizable=1,directories=1,width=750,height=400\'); this.disabled=true;this.value=\'Please wait...\';this.form.submit(); } else { return false; }">';
                						echo '</form>';
                						$button_displayed = true;
                					}
            						echo '	</td>
											<td class="main">';
            						
        							$post_auth_btn_text = 'Post-Captured';
        							
        							if ($bb_trans_info_row['bibit_capture_request'] == 1) {
        								$post_auth_btn_text .= ' (Awaiting for response)';
        							}
        							
            						echo tep_draw_form('post_capture_order', FILENAME_ORDERS, tep_get_all_get_params(), 'post');
									echo tep_draw_hidden_field('subaction', 'post_authorisation');
									echo '<input type="submit" name="PostCaptureBtn" value="'.$post_auth_btn_text.'" title="Post Authorise" class="inputButton" onClick="if (confirm(\'Are you sure to post captured this order remotely?\')) { this.disabled=true;this.value=\'Please wait...\';this.form.submit(); } else { return false; }">';
									echo "</form>";
									$button_displayed = true;
            						
            						echo '</td>';
								}
            				?>
    						</tr>
    					</table>
    				</td>
  				</tr>
      		</table>
   		</td>
  	</tr>
<?
	}
} else {
	echo $payment_method_title_info;
	
	if ($order->info['orders_status'] == '1' || $order->info['orders_status'] == '7' || $order->info['orders_status'] == '2') {
		echo '<tr>
    			<td class="main">';
		echo tep_draw_form('bb_status_check_form', FILENAME_ORDERS, tep_get_all_get_params(), 'post');
		echo tep_draw_hidden_field('subaction', 'payment_action');
		echo tep_draw_hidden_field('payment_action', 'check_trans_status');
		echo '<input type="submit" name="BBCheckTransStatusBtn" value="Check Payment Status" title="Check Payment Status" class="inputButton">';
		echo '</form>
				</td>
			  </tr>';
	}
}
?>
</table>