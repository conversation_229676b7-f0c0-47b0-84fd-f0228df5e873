<?
include_once(DIR_FS_CATALOG_MODULES . 'payment/bibit/admin/languages/' . $language . '/bibit.lng.php');

$bb_trans_info_select_sql = "SELECT * FROM " . TABLE_BIBIT . " WHERE orders_id = '" . $order_obj->orders_id . "'";
$bb_trans_info_result_sql= tep_db_query($bb_trans_info_select_sql);
?>
<table border="0" width="100%" cellspacing="0" cellpadding="2">
	<tr>
		<td class="subInvoiceBoxHeading">
			<div style="border-bottom: 1px solid #996600; border-top: 1px solid #996600; padding: 2px;">Payment Details</div>
		</td>
	</tr>
<?
if ($bb_trans_info_row = tep_db_fetch_array($bb_trans_info_result_sql)) {
?>
	<tr>
    	<td class="main">
      		<table border="0" width="100%" cellspacing="0" cellpadding="2">
      			<tr valign="top">
        			<td width="50%" valign="top">
              			<table border="0" cellspacing="0" cellpadding="2">
              				<tr>
                				<td class="invoiceRecords" valign="top" nowrap><?=ENTRY_BB_PAYMENT_TYPE?></td>
                				<td class="invoiceRecords" valign="top">:&nbsp;</td>
                				<td class="invoiceRecords" nowrap><?=(tep_not_null($bb_trans_info_row['bibit_payment_method']) ? $bb_trans_info_row['bibit_payment_method'] : TEXT_NOT_AVAILABLE)?></td>
              				</tr>
              				<tr>
              					<td class="invoiceRecords" valign="top" nowrap><?=ENTRY_BB_PAYMENT_ID?></td>
                				<td class="invoiceRecords" valign="top">:&nbsp;</td>
                				<td class="invoiceRecords" nowrap><?=(tep_not_null($bb_trans_info_row['bibit_payment_id']) ? $bb_trans_info_row['bibit_payment_id'] : TEXT_NOT_AVAILABLE)?></td>
							</tr>
              				<tr>
                				<td class="invoiceRecords" valign="top" nowrap><?=ENTRY_BB_CURRENCY?></td>
                				<td class="invoiceRecords" valign="top">:&nbsp;</td>
                				<td class="invoiceRecords" nowrap><?=$bb_trans_info_row['bibit_currency']?></td>
              				</tr>
              				<tr>
                				<td class="invoiceRecords" valign="top" nowrap><?=ENTRY_BB_AMT?></td>
                				<td class="invoiceRecords" valign="top">:&nbsp;</td>
                				<td class="invoiceRecords"><?=$bb_trans_info_row['bibit_amount']?></td>
              				</tr>
              				<tr>
                				<td class="invoiceRecords" valign="top" nowrap><?=ENTRY_BB_CVC_RESULT?></td>
                				<td class="invoiceRecords" valign="top">:&nbsp;</td>
                				<td class="invoiceRecords"><?=(tep_not_null($bb_trans_info_row['bibit_cvc_status']) ? $bb_trans_info_row['bibit_cvc_status'] : TEXT_NOT_AVAILABLE)?></td>
              				</tr>
              			</table>
        			</td>
        			<td valign="top">
              			<table border="0" cellspacing="0" cellpadding="2">
              				<tr>
                				<td class="invoiceRecords" valign="top" nowrap><?=ENTRY_BB_AVS_RESULT?></td>
                				<td class="invoiceRecords" valign="top">:&nbsp;</td>
                				<td class="invoiceRecords"><?=(tep_not_null($bb_trans_info_row['bibit_avs_status']) ? $bb_trans_info_row['bibit_avs_status'] : TEXT_NOT_AVAILABLE)?></td>
              				</tr>
              				<tr>
                				<td class="invoiceRecords" valign="top" nowrap><?=ENTRY_BB_RISK_SCORE?></td>
                				<td class="invoiceRecords" valign="top">:&nbsp;</td>
                				<td class="invoiceRecords"><?=(tep_not_null($bb_trans_info_row['bibit_risk_score']) ? $bb_trans_info_row['bibit_risk_score'] : TEXT_NOT_AVAILABLE)?></td>
              				</tr>
              				<tr>
                				<td class="invoiceRecords" valign="top" nowrap><?=ENTRY_BB_MAC?></td>
                				<td class="invoiceRecords" valign="top">:&nbsp;</td>
                				<td class="invoiceRecords" nowrap><?=$bb_trans_info_row['bibit_mac']?></td>
              				</tr>
              				<tr>
                				<td class="invoiceRecords" valign="top" nowrap><?=ENTRY_BB_PAYMENT_STATUS?></td>
                				<td class="invoiceRecords" valign="top">:&nbsp;</td>
                				<td class="invoiceRecords"><?=(tep_not_null($bb_trans_info_row['bibit_status']) ? $bb_trans_info_row['bibit_status'] : TEXT_STATUS_UNKNOWN)?></td>
              				</tr>
              			</table>
              		</td>
      			</tr>
      		</table>
   		</td>
  	</tr>
<?
} else {
?>
	<tr>
		<td class="invoiceRecords">No further payment information is available.</td>
	</tr>
<?
}
?>