<?php
define('TABLE_HEADING_BITPAY_DATE', 'History Date');
define('TABLE_HEADING_BITPAY_CHANGED_BY', 'Changed By');
//Define Merchant Key Language
define('MODULE_PAYMENT_BITPAY_API_KEY', 'API Key');
define('MODULE_PAYMENT_TAX', 'Tax');
define('ENTRY_BITPAY_STATUS', 'Payment Status');
define('ENTRY_BITPAY_INVOICE_ID', 'Invoice Id');
define('ENTRY_BITPAY_BTC_PRICE', 'BTC Price');
define('ENTRY_BITPAY_PRICE', 'Amount');
define('ENTRY_BITPAY_CURRENCY', 'Currency');
define('ENTRY_BITPAY_INVOICE_TIME', 'Invoice Time');
define('ENTRY_BITPAY_EXPIRATION_TIME', 'Expiration Time');
define('ENTRY_BITPAY_CURRENT_TIME', 'Current Time');
define('ENTRY_BITPAY_BTC_PAID', 'BTC Paid');
define('ENTRY_BITPAY_BTC_DUE', 'BTC Due');
define('ENTRY_BITPAY_RATE', 'Rate');
define('ENTRY_BITPAY_EXCEPTION_STATUS', 'Exception Status');
define('ENTRY_BITPAY_GUID', 'GuId');
define('ENTRY_BITPAY_CREATED_DATE', 'Created Date');
define('ENTRY_BITPAY_LAST_MODIFIED', 'Last Modified');
define('ENTRY_BITPAY_REASON', 'Reason');
define('ENTRY_BITPAY_INVOICE_ID', 'Invoice Id');

?>