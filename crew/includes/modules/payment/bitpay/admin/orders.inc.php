<?php
include_once(DIR_FS_CATALOG_MODULES . 'payment/bitpay/admin/languages/' . $language . '/bitpay.lng.php');
$bitpay_trans_info_select_sql = "SELECT * FROM " . TABLE_BITPAY . " WHERE order_id ='" . (int)$oID . "'";
$bitpay_trans_info_result_sql= tep_db_query($bitpay_trans_info_select_sql);
$bitpay_payment_status_history_info_select_sql = "SELECT * FROM " . TABLE_BITPAY_STATUS_HISTORY . " WHERE order_id = '" . $oID . "'";
$bitpay_payment_status_history_info_result_sql = tep_db_query($bitpay_payment_status_history_info_select_sql);
?>
<table border="0" width="100%" cellspacing="0" cellpadding="2">
<?php ob_start(); 
?>
	<tr>
		<td class="main">
      		<table border="0" width="100%" cellspacing="0" cellpadding="2">
      			<tr>
      				<td class="main" width="12%" nowrap><b><?php echo ENTRY_PAYMENT_METHOD?></b></td>
      				<td class="main">
      					<div>
      						<?php if((int)$order->info['payment_methods_id']>0) { ?><div class="<?php echo ((int)$payment_info_array[$order->info['payment_methods_id']]->confirm_complete_days>0?'rp_icon':'nrp_icon')?>"><b><?php echo ((int)$payment_info_array[$order->info['payment_methods_id']]->confirm_complete_days>0?'RP':'NRP')?></b></div> <?php } ?>
      						<div style="width:30px; height:15px; background-color:<?php echo $payment_module_info['display_colour']?>; float:left; vertical-align:top; text-align:center;">&nbsp;</div>
      						<div style="vertical-align: top">
      							&nbsp;&nbsp;
<?php
	if ($view_payment_details_permission) {
		if (tep_not_null($last_payment_method) && $last_payment_method['payment_methods_id'] != $order->info['payment_methods_id']) {
			echo '<span class="redIndicator">'.(tep_not_null($payment_module_info['payment_methods_parent_title'])?$payment_module_info['payment_methods_parent_title'] . ' - ' :'') . $payment_module_info['payment_methods_title'].'</span>';
		} else {
			echo (tep_not_null($payment_module_info['payment_methods_parent_title'])?$payment_module_info['payment_methods_parent_title'] . ' - ' :'') . $payment_module_info['payment_methods_title'];
		}
		
		if (tep_not_null($last_payment_method) && $last_payment_method['payment_methods_id'] != $order->info['payment_methods_id']) {
			echo '&nbsp;&nbsp;<span class="redIndicator">(Last Payment Method: ' . (tep_not_null($last_payment_method['parent_payment_methods_title'])? $last_payment_method['parent_payment_methods_title'] . ' - ' : '' ) . $last_payment_method['payment_methods_title'] . ')</span>';
		}
	} else {
		echo (tep_not_null($payment_module_info['payment_methods_parent_title'])?$payment_module_info['payment_methods_parent_title'] . ' - ' :'') . $payment_module_info['payment_methods_title'];
	}
?>
							</div>
      					</div>
      				</td>
				</tr>
			</table>
		</td>
	</tr>
<?php
$payment_method_title_info = ob_get_contents();
ob_end_clean();
if (tep_db_num_rows($bitpay_trans_info_result_sql) || tep_db_num_rows($bitpay_payment_status_history_info_result_sql)) {
	$bitpay_trans_info_row = tep_db_fetch_array($bitpay_trans_info_result_sql);
	echo $payment_method_title_info;
	
	if (!$view_payment_details_permission) {
		;
	} else {
?>
  	<tr>
    	<td class="main">
      		<table border="0" width="100%" cellspacing="0" cellpadding="2">
      			<tr valign="top">
        			<td width="39%" nowrap>
              			<table border="0" cellspacing="0" cellpadding="2">
              				<tr>
                				<td class="main" valign="top" nowrap><b><?php echo ENTRY_BITPAY_INVOICE_ID?></b>&nbsp;</td>
                				<td class="main" nowrap><?php echo $bitpay_trans_info_row['invoice_id'] ?></td>
              				</tr>
             				<tr>
                				<td class="main" valign="top" nowrap><b><?php echo ENTRY_BITPAY_STATUS?></b>&nbsp;</td>
                				<td class="main" nowrap><?php echo $bitpay_trans_info_row['status']?></td>
              				</tr>
              				<tr>
                				<td class="main" valign="top" nowrap><b><?php echo ENTRY_BITPAY_BTC_PRICE?></b>&nbsp;</td>
                				<td class="main" nowrap><?php echo $bitpay_trans_info_row['btc_price']?></td>
              				</tr>
              				<tr>
                				<td class="main" valign="top" nowrap><b><?php echo ENTRY_BITPAY_PRICE?></b>&nbsp;</td>
                				<td class="main" nowrap><?php echo $bitpay_trans_info_row['price']?></td>
              				</tr>
              				<tr>
                				<td class="main" valign="top" nowrap><b><?php echo ENTRY_BITPAY_CURRENCY?></b>&nbsp;</td>
                				<td class="main" nowrap><?php echo $bitpay_trans_info_row['currency']?></td>
              				</tr>
              				<tr>
                				<td class="main" valign="top" nowrap><b><?php echo ENTRY_BITPAY_INVOICE_TIME?></b>&nbsp;</td>
                				<td class="main" nowrap><?php echo ((isset($bitpay_trans_info_row['invoice_time']) && !empty($bitpay_trans_info_row['invoice_time'])) ? date('Y-m-d H:i:s', $bitpay_trans_info_row['invoice_time']/1000) : '')?></td>
              				</tr>
              				<tr>
                				<td class="main" valign="top" nowrap><b><?php echo ENTRY_BITPAY_EXPIRATION_TIME?></b>&nbsp;</td>
								<td class="main" nowrap><?php echo ((isset($bitpay_trans_info_row['expiration_time']) && !empty($bitpay_trans_info_row['expiration_time'])) ? date('Y-m-d H:i:s', $bitpay_trans_info_row['expiration_time']/1000) : '')?></td>
              				</tr>
              				<tr>
                				<td class="main" valign="top" nowrap><b><?php echo ENTRY_BITPAY_CURRENT_TIME?></b>&nbsp;</td>
								<td class="main" nowrap><?php echo ((isset($bitpay_trans_info_row['currently_time']) && !empty($bitpay_trans_info_row['currently_time'])) ? date('Y-m-d H:i:s', $bitpay_trans_info_row['currently_time']/1000) : '')?></td>
              				</tr>
              				<tr>
                				<td class="main" valign="top" nowrap><b><?php echo ENTRY_BITPAY_BTC_PAID?></b>&nbsp;</td>
                				<td class="main" nowrap><?php echo $bitpay_trans_info_row['btc_paid']?></td>
              				</tr>
							<tr>
                				<td class="main" valign="top" nowrap><b><?php echo ENTRY_BITPAY_BTC_DUE?></b>&nbsp;</td>
                				<td class="main" nowrap><?php echo $bitpay_trans_info_row['btc_due']?></td>
              				</tr>
							<tr>
                				<td class="main" valign="top" nowrap><b><?php echo ENTRY_BITPAY_RATE?></b>&nbsp;</td>
                				<td class="main" nowrap><?php echo $bitpay_trans_info_row['rate']?></td>
              				</tr>
							<tr>
                				<td class="main" valign="top" nowrap><b><?php echo ENTRY_BITPAY_EXCEPTION_STATUS?></b>&nbsp;</td>
                				<td class="main" nowrap><?php echo $bitpay_trans_info_row['exception_status']?></td>
              				</tr>
							<tr>
                				<td class="main" valign="top" nowrap><b><?php echo ENTRY_BITPAY_GUID?></b>&nbsp;</td>
                				<td class="main" nowrap><?php echo $bitpay_trans_info_row['guid']?></td>
              				</tr>
							<tr>
                				<td class="main" valign="top" nowrap><b><?php echo ENTRY_BITPAY_CREATED_DATE?></b>&nbsp;</td>
                				<td class="main" nowrap><?php echo $bitpay_trans_info_row['date_added']?></td>
              				</tr>
							<tr>
                				<td class="main" valign="top" nowrap><b><?php echo ENTRY_BITPAY_LAST_MODIFIED?></b>&nbsp;</td>
                				<td class="main" nowrap><?php echo $bitpay_trans_info_row['date_modified']?></td>
              				</tr>
							<tr>
								<td class="main" nowrap>
	              				<?php               					
                					if ($order->info['orders_status'] == '1' || $order->info['orders_status'] == '7' || $order->info['orders_status'] == '2') {
	                					echo "&nbsp;&nbsp;";
										echo tep_draw_form('bitpay_status_check_form', FILENAME_ORDERS, tep_get_all_get_params(), 'post');
										echo tep_draw_hidden_field('subaction', 'payment_action');
										echo tep_draw_hidden_field('payment_action', 'check_trans_status');
										echo tep_submit_button('Check Payment Status', 'Check Payment Status', 'name="bitpayCheckTransStatusBtn"', 'inputButton');
										echo "</form>";
									}
								?>
								</td>
							</tr>
              			</table>
        			</td>
        			<td>
        				<table border="0" cellspacing="0" cellpadding="1">
              				</tr>
        					<tr>
        						<td colspan="2">
									<table border="1" cellspacing="0" cellpadding="2">
		        						<tr>
		        							<td class="smallText" nowrap><b><?php echo TABLE_HEADING_BITPAY_DATE?></b></td>
											<td class="smallText" nowrap><b><?php echo ENTRY_BITPAY_STATUS?></b></td>
		        							<td class="smallText" nowrap><b><?php echo ENTRY_BITPAY_REASON?></b></td>
		        							<td class="smallText" nowrap><b><?php echo TABLE_HEADING_BITPAY_CHANGED_BY?></b></td>
		        						</tr>
<?php		while ($bitpay_payment_status_history_info_row = tep_db_fetch_array($bitpay_payment_status_history_info_result_sql)) {
        	echo ' 						<tr>
                							<td class="smallText" nowrap>'.$bitpay_payment_status_history_info_row['date'].'</td>
                							<td class="smallText" nowrap>'.$bitpay_payment_status_history_info_row['status'].'</td>
                							<td class="smallText" nowrap>'.$bitpay_payment_status_history_info_row['reason'].'</td>
                							<td class="smallText" nowrap>'.(tep_not_null($bitpay_payment_status_history_info_row['changed_by'])?$bitpay_payment_status_history_info_row['changed_by']:'&nbsp;').'</td>
                						</tr>';
     	}
?>
									</table>
								</td>
        					</tr>
        				</table>
        			</td>
        			<td>
        				<table border="0" cellspacing="0" cellpadding="2">
        					<tr>
                				<td class="main" valign="top" nowrap><b><?php echo ENTRY_BITPAY_CURRENCY?></b>&nbsp;</td>
                				<td class="main" nowrap>
                				<?php
                					if (strtolower($bitpay_trans_info_row['currency']) == strtolower($order->info['currency'])) {
                						echo $bitpay_trans_info_row['currency'];
                					} else {
                						echo '<span class="redIndicator">'.$bitpay_trans_info_row['currency'].'<br><span class="smallText">Does not match purchase currency.</span></span>';
                					}
                				?>
                				</td>
              				</tr>
              				<tr>
                				<td class="main" valign="top" nowrap><b><?php echo ENTRY_BITPAY_PRICE?></b>&nbsp;</td>
                				<td class="main">
                				<?php
			
									$bitpayGrossAmt = number_format($bitpay_trans_info_row['price'], $currencies->currencies[$bitpay_trans_info_row['currency']]['decimal_places'], $currencies->currencies[$bitpay_trans_info_row['currency']]['decimal_point'], $currencies->currencies[$bitpay_trans_info_row['currency']]['thousands_point']);
									$bitpayAmountFormatted = $currencies->currencies[$bitpay_trans_info_row['currency']]['symbol_left'] . $bitpayGrossAmt . $currencies->currencies[$bitpay_trans_info_row['currency']]['symbol_right'];
									if ($bitpayAmountFormatted != strip_tags($order->order_totals['ot_total']['text']) ) {
                						$mc_gross_display_text = '<span class="redIndicator">'.$bitpayAmountFormatted.'<br><span class="smallText">Does not match purchase amount.</span></span>';
                					} else {
										$mc_gross_display_text = $bitpayAmountFormatted;
									}
                					
                					echo $mc_gross_display_text;
                				?>
                				</td>
              				</tr>
              				<tr>
              					<td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
              				</tr>
              			</table>
        			</td>
      			</tr>
      		</table>
   		</td>
  	</tr>
<?php
	}
} else {
	echo $payment_method_title_info;
?>
	<tr>
		<td class="main" nowrap>
		<?
			if ($order->info['orders_status'] == '1' || $order->info['orders_status'] == '7' || $order->info['orders_status'] == '2') {
				echo "&nbsp;&nbsp;";
				echo tep_draw_form('bitpay_status_check_form', FILENAME_ORDERS, tep_get_all_get_params(), 'post');
				echo tep_draw_hidden_field('subaction', 'payment_action');
				echo tep_draw_hidden_field('payment_action', 'check_trans_status');
				echo tep_submit_button('Check Payment Status', 'Check Payment Status', 'name=bitpayCheckTransStatusBtn"', 'inputButton');
				echo "</form>";
			}
		?>
		</td>
	</tr>
<?php
}
?>
	
</table>