<?
include_once(DIR_FS_CATALOG_MODULES . 'payment/bitpay/admin/languages/' . $language . '/bitpay.lng.php');
$bitpay_trans_info_select_sql = "SELECT * FROM " . TABLE_BITPAY . " WHERE order_id = '" . $order_obj->orders_id . "'";
$bitpay_trans_info_result_sql = tep_db_query($bitpay_trans_info_select_sql);
?>
<table border="0" width="100%" cellspacing="0" cellpadding="2">
	<tr>
		<td class="subInvoiceBoxHeading">
			<div style="border-bottom: 1px solid #996600; border-top: 1px solid #996600; padding: 2px;">Payment Details</div>
		</td>
	</tr>
	<?
	if ($bitpay_trans_info_row = tep_db_fetch_array($bitpay_trans_info_result_sql)) {
		?>
		<tr>
			<td class="main">
				<table border="0" width="100%" cellspacing="0" cellpadding="2">
					<tr valign="top">
						<td width="50%" valign="top">
							<table border="0" cellspacing="0" cellpadding="2">
								
								<tr>
									<td class="invoiceRecords" valign="top" nowrap><?= ENTRY_BITPAY_CURRENCY ?></td>
									<td class="invoiceRecords" valign="top">:&nbsp;</td>
									<td class="invoiceRecords" nowrap><?= $bitpay_trans_info_row['currency'] ?></td>
								</tr>
								<tr>
									<td class="invoiceRecords" valign="top" nowrap><?= ENTRY_BITPAY_PRICE ?></td>
									<td class="invoiceRecords" valign="top">:&nbsp;</td>
									<td class="invoiceRecords">
										<?php
										$bitpayGrossAmt = number_format($bitpay_trans_info_row['price'], $currencies->currencies[$bitpay_trans_info_row['currency']]['decimal_places'], $currencies->currencies[$bitpay_trans_info_row['currency']]['decimal_point'], $currencies->currencies[$bitpay_trans_info_row['currency']]['thousands_point']);
										$bitpayAmountFormatted = $currencies->currencies[$bitpay_trans_info_row['currency']]['symbol_left'] . $bitpayGrossAmt . $currencies->currencies[$bitpay_trans_info_row['currency']]['symbol_right'];
										echo $bitpayAmountFormatted;
										?>
									</td>
								</tr>
								<tr>
									<td class="invoiceRecords" valign="top" nowrap><?= ENTRY_BITPAY_STATUS ?></td>
									<td class="invoiceRecords" valign="top">:&nbsp;</td>
									<td class="invoiceRecords"><?= (tep_not_null($bitpay_trans_info_row['status']) ? $bitpay_trans_info_row['status'] : TEXT_NOT_AVAILABLE) ?></td>
								</tr>
							</table>
						</td>
						<td valign="top">
							<table border="0" cellspacing="0" cellpadding="2">
								<tr>
									<td class="invoiceRecords" valign="top" nowrap><?= ENTRY_BITPAY_REASON ?></td>
									<td class="invoiceRecords" valign="top">:&nbsp;</td>
									<td class="invoiceRecords"><?= (tep_not_null($bitpay_trans_info_row['reason']) ? $bitpay_trans_info_row['reason'] : '') ?></td>
								</tr>
								<tr>
									<td class="invoiceRecords" valign="top" nowrap><?= ENTRY_BITPAY_INVOICE_ID ?></td>
									<td class="invoiceRecords" valign="top">:&nbsp;</td>
									<td class="invoiceRecords"><?= (tep_not_null($bitpay_trans_info_row['invoice_id']) ? $bitpay_trans_info_row['invoice_id'] : '') ?></td>
								</tr>
							</table>
						</td>
					</tr>
				</table>
			</td>
		</tr>
		<?
	} else {
		?>
		<tr>
			<td class="invoiceRecords">No further payment information is available.</td>
		</tr>
		<?
	}
	?>