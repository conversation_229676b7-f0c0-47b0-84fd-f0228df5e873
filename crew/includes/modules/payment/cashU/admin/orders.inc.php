<?
/*
  	$Id: orders.inc.php,v 1.6 2009/02/03 03:25:21 boonhock Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2005 SKC Venture
  	
  	Released under the GNU General Public License
*/

include_once(DIR_FS_CATALOG_MODULES . 'payment/cashU/admin/languages/'.$language.'/cashU.lng.php');

$cashu_trans_info_select_sql = "SELECT transaction_id, transaction_amount, transaction_currency FROM " . TABLE_PAYMENT_EXTRA_INFO . " WHERE orders_id='" . (int)tep_db_input($oID) . "'";
$cashu_trans_info_result_sql= tep_db_query($cashu_trans_info_select_sql);
?>
<table border="0" width="100%" cellspacing="0" cellpadding="2">
	<tr>
		<td class="main">
      		<table border="0" width="100%" cellspacing="0" cellpadding="2">
      			<tr>
      				<td class="main" width="12%" nowrap><b><?=ENTRY_PAYMENT_METHOD?></b></td>
      				<td class="main">
      					<div>
      						<? if((int)$order->info['payment_methods_id']>0) { ?><div class="<?=((int)$payment_info_array[$order->info['payment_methods_id']]->confirm_complete_days>0?'rp_icon':'nrp_icon')?>"><b><?=((int)$payment_info_array[$order->info['payment_methods_id']]->confirm_complete_days>0?'RP':'NRP')?></b></div> <? } ?>
      						<div style="width:30px; height:15px; background-color:<?=$payment_module_info['display_colour']?>; float:left; vertical-align:top; text-align:center;"></div>
      						<div style="vertical-align: top">
      							&nbsp;&nbsp;
<?
	if ($view_payment_details_permission) {
		if (tep_not_null($last_payment_method) && $last_payment_method['payment_methods_id'] != $order->info['payment_methods_id']) {
			echo '<span class="redIndicator">'.(tep_not_null($payment_module_info['payment_methods_parent_title'])?$payment_module_info['payment_methods_parent_title'] . ' - ' :'') . $payment_module_info['payment_methods_title'].'</span>';
		} else {
			echo (tep_not_null($payment_module_info['payment_methods_parent_title'])?$payment_module_info['payment_methods_parent_title'] . ' - ' :'') . $payment_module_info['payment_methods_title'];
		}
		
		if (tep_not_null($last_payment_method) && $last_payment_method['payment_methods_id'] != $order->info['payment_methods_id']) {
			echo '&nbsp;&nbsp;<span class="redIndicator">(Last Payment Method: ' . (tep_not_null($last_payment_method['parent_payment_methods_title'])? $last_payment_method['parent_payment_methods_title'] . ' - ' : '' ) . $last_payment_method['payment_methods_title'] . ')</span>';
		}
	} else {
		echo (tep_not_null($payment_module_info['payment_methods_parent_title'])?$payment_module_info['payment_methods_parent_title'] . ' - ' :'') . $payment_module_info['payment_methods_title'];
	}
?>
							</div>
      					</div>
      				</td>
				</tr>
<?	if ($view_payment_details_permission) {
		if ($cashu_trans_info_row = tep_db_fetch_array($cashu_trans_info_result_sql)) {
	?>
				<tr>
					<td class="main" valign="top" nowrap><b><?=ENTRY_CASHU_TRANSACTION_ID?></b>&nbsp;</td>
                	<td class="main" valign="top" nowrap><?=$cashu_trans_info_row["transaction_id"]?></td>
				</tr>
				<tr>
    				<td class="main" valign="top" nowrap><b><?=ENTRY_CASHU_PAYMENT_CURRENCY?></b>&nbsp;</td>
    				<td class="main" nowrap>
    				<?
    					if (strtolower($cashu_trans_info_row["transaction_currency"]) == strtolower($order->info['currency'])) {
    						echo $cashu_trans_info_row['transaction_currency'];
    					} else {
    						echo '<span class="redIndicator">'.$cashu_trans_info_row["transaction_currency"].'<br><span class="smallText">Does not match purchase currency.</span></span>';
    					}
    				?>
    				</td>
  				</tr>
  				<tr>
    				<td class="main" valign="top" nowrap><b><?ENTRY_CASHU_PAYMENT_AMOUNT?></b>&nbsp;</td>
    				<td class="main">
    				<?
    					$cashu_gross_display_text = $cashu_trans_info_row["transaction_amount"];
    					$cashu_gross_amt = number_format($cashu_trans_info_row["transaction_amount"], $currencies->currencies[$order->info["currency"]]['decimal_places'], $currencies->currencies[$order->info["currency"]]['decimal_point'], $currencies->currencies[$order->info["currency"]]['thousands_point']);
    					
    					if ($currencies->currencies[$order->info["currency"]]['symbol_left'].$cashu_gross_amt.$currencies->currencies[$order->info["currency"]]['symbol_right'] != strip_tags($order->order_totals['ot_total']['text']) ) {
    						$cashu_gross_display_text = '<span class="redIndicator">'.$cashu_trans_info_row['transaction_amount'].'<br><span class="smallText">Does not match purchase amount.</span></span>';
    					}
    					
    					echo $cashu_gross_display_text;
    				?>
    				</td>
  				</tr>
<?		}
	}
?>
			</table>
		</td>
	</tr>
</table>