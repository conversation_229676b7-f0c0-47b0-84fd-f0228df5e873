<?
include_once(DIR_FS_CATALOG_MODULES . 'payment/cimb/admin/languages/' . $language . '/cimb.lng.php');

$cimb_trans_info_select_sql = "SELECT * FROM " . TABLE_CIMB . " WHERE orders_id='" . (int)$oID . "'";
$cimb_trans_info_result_sql= tep_db_query($cimb_trans_info_select_sql);

$cimb_payment_status_history_info_select_sql = "SELECT * FROM " . TABLE_CIMB_STATUS_HISTORY . " WHERE orders_id = '" . tep_db_input($oID) . "'";
$cimb_payment_status_history_info_result_sql = tep_db_query($cimb_payment_status_history_info_select_sql);


$cimb_payment_status_array = array(	'S'=>'Success',
									'F'=>'Failed',
									'U'=>'Unknown',
									'D'=>'Transaction not processed');
?>
<table border="0" width="100%" cellspacing="0" cellpadding="2">
<? ob_start(); 

?>
	<tr>
		<td class="main">
      		<table border="0" width="100%" cellspacing="0" cellpadding="2">
      			<tr>
      				<td class="main" width="12%" nowrap><b><?=ENTRY_PAYMENT_METHOD?></b></td>
      				<td class="main">
      					<div>
      						<? if((int)$order->info['payment_methods_id']>0) { ?><div class="<?=((int)$payment_info_array[$order->info['payment_methods_id']]->confirm_complete_days>0?'rp_icon':'nrp_icon')?>"><b><?=((int)$payment_info_array[$order->info['payment_methods_id']]->confirm_complete_days>0?'RP':'NRP')?></b></div> <? } ?>
      						<div style="width:30px; height:15px; background-color:<?=$payment_module_info['display_colour']?>; float:left; vertical-align:top; text-align:center;">&nbsp;</div>
      						<div style="vertical-align: top">
      							&nbsp;&nbsp;
<?
	if ($view_payment_details_permission) {
		if (tep_not_null($last_payment_method) && $last_payment_method['payment_methods_id'] != $order->info['payment_methods_id']) {
			echo '<span class="redIndicator">'.(tep_not_null($payment_module_info['payment_methods_parent_title'])?$payment_module_info['payment_methods_parent_title'] . ' - ' :'') . $payment_module_info['payment_methods_title'].'</span>';
		} else {
			echo (tep_not_null($payment_module_info['payment_methods_parent_title'])?$payment_module_info['payment_methods_parent_title'] . ' - ' :'') . $payment_module_info['payment_methods_title'];
		}
		
		if (tep_not_null($last_payment_method) && $last_payment_method['payment_methods_id'] != $order->info['payment_methods_id']) {
			echo '&nbsp;&nbsp;<span class="redIndicator">(Last Payment Method: ' . (tep_not_null($last_payment_method['parent_payment_methods_title'])? $last_payment_method['parent_payment_methods_title'] . ' - ' : '' ) . $last_payment_method['payment_methods_title'] . ')</span>';
		}
	} else {
		echo (tep_not_null($payment_module_info['payment_methods_parent_title'])?$payment_module_info['payment_methods_parent_title'] . ' - ' :'') . $payment_module_info['payment_methods_title'];
	}
?>
							</div>
      					</div>
      				</td>
				</tr>
			</table>
		</td>
	</tr>
<?
$payment_method_title_info = ob_get_contents();
ob_end_clean();

if (tep_db_num_rows($cimb_trans_info_result_sql) || tep_db_num_rows($cimb_payment_status_history_info_result_sql)) {
	$cimb_trans_info_row = tep_db_fetch_array($cimb_trans_info_result_sql);
	
	echo $payment_method_title_info;
	
	if (!$view_payment_details_permission) {
		;
	} else {
?>
  	<tr>
    	<td class="main">
      		<table border="0" width="100%" cellspacing="0" cellpadding="2">
      			<tr valign="top">
        			<td width="39%" nowrap>
              			<table border="0" cellspacing="0" cellpadding="2">
              				<tr>
                				<td class="main" valign="top" nowrap><b><?=ENTRY_CIMB_CHANNEL_ID?></b>&nbsp;</td>
                				<td class="main" nowrap><?=$cimb_trans_info_row['cimb_channel_id']?></td>
              				</tr>
              				<tr>
                				<td class="main" valign="top" nowrap><b><?=ENTRY_CIMB_REFERENCE_NO?></b>&nbsp;</td>
                				<td class="main" nowrap><?=$cimb_trans_info_row['cimb_reference_no']?></td>
              				</tr>
              				<tr>
                				<td class="main" valign="top" nowrap><b><?=ENTRY_CIMB_TRANSACTION_DATE?></b>&nbsp;</td>
                				<td class="main" nowrap><?=$cimb_trans_info_row['cimb_transaction_date']?></td>
              				</tr>
              				<tr>
                				<td class="main" valign="top" nowrap><b><?=ENTRY_CIMB_TRANSACTION_TIME?></b>&nbsp;</td>
                				<td class="main" nowrap><?=$cimb_trans_info_row['cimb_transaction_time']?></td>
              				</tr>
              				<tr>
                				<td class="main" valign="top" nowrap><b><?=ENTRY_CIMB_USER_FULL_NAME?></b>&nbsp;</td>
                				<td class="main" nowrap><?=$cimb_trans_info_row['cimb_user_full_name']?></td>
              				</tr>
              			</table>
        			</td>
        			<td>
        				<table border="0" cellspacing="0" cellpadding="1">
        					<tr>
                				<td class="main" valign="top" nowrap><b><?=ENTRY_CIMB_TRANSACTION_STATUS?></b>&nbsp;</td>
                				<td class="main" nowrap>
                				<?
                					if (tep_not_null($cimb_trans_info_row['cimb_status'])) {
                						echo $cimb_payment_status_array[$cimb_trans_info_row['cimb_status']];
                					} else {
                						echo TEXT_STATUS_UNKNOWN;
                					}
									
                					if ($order->info['orders_status'] == '1' || $order->info['orders_status'] == '7' || $order->info['orders_status'] == '2') {
	                					echo "&nbsp;&nbsp;";
										echo tep_draw_form('cimb_status_check_form', FILENAME_ORDERS, tep_get_all_get_params(), 'post');
										echo tep_draw_hidden_field('subaction', 'payment_action');
										echo tep_draw_hidden_field('payment_action', 'check_trans_status');
										echo '<input type="submit" name="CIMBCheckTransStatusBtn" value="Check Payment Status" title="Check Payment Status" class="inputButton">';
										echo "</form>";
									}
								?>
                				</td>
              				</tr>
        					<tr>
        						<td colspan="2">
									<table border="1" cellspacing="0" cellpadding="2">
		        						<tr>
		        							<td class="smallText" nowrap><b><?=TABLE_HEADING_CIMB_DATE?></b></td>
		        							<td class="smallText" nowrap><b><?=TABLE_HEADING_CIMB_STATUS?></b></td>
		        							<td class="smallText" nowrap><b><?=TABLE_HEADING_CIMB_DESCRIPTION?></b></td>
		        						</tr>
<?		while ($cimb_payment_status_history_info_row = tep_db_fetch_array($cimb_payment_status_history_info_result_sql)) {
        	echo ' 						<tr>
                							<td class="smallText" nowrap>'.$cimb_payment_status_history_info_row['cimb_date'].'</td>
                							<td class="smallText" nowrap>'.(isset($cimb_payment_status_array[$cimb_payment_status_history_info_row['cimb_status']])?$cimb_payment_status_array[$cimb_payment_status_history_info_row['cimb_status']]:$cimb_payment_status_history_info_row['cimb_status']).'</td>
                							<td class="smallText" nowrap>'.$cimb_payment_status_history_info_row['cimb_description'].'</td>
                						</tr>';
     	}
?>
									</table>
								</td>
        					</tr>
        				</table>
        			</td>
        			<td>
        				<table border="0" cellspacing="0" cellpadding="2">
        					<tr>
                				<td class="main" valign="top" nowrap><b><?=ENTRY_CIMB_PAYMENT_CURRENCY?></b>&nbsp;</td>
                				<td class="main" nowrap><?=$cimb_trans_info_row['cimb_currency'] . ' ('.TEXT_INFO_CURRENCY_NOT_FROM_CIMB.')'?></td>
              				</tr>
              				<tr>
                				<td class="main" valign="top" nowrap><b><?=ENTRY_CIMB_PAYMENT_AMOUNT?></b>&nbsp;</td>
                				<td class="main">
                				<?
                					$mc_gross_display_text = $cimb_trans_info_row['cimb_amount'];
                					$cimb_gross_amt = number_format($cimb_trans_info_row['cimb_amount'], $currencies->currencies[$order->info["currency"]]['decimal_places'], $currencies->currencies[$order->info["currency"]]['decimal_point'], $currencies->currencies[$order->info["currency"]]['thousands_point']);
                					
                					if ($currencies->currencies[$order->info["currency"]]['symbol_left'].$cimb_gross_amt.$currencies->currencies[$order->info["currency"]]['symbol_right'] != strip_tags($order->order_totals['ot_total']['text']) ) {
                						$mc_gross_display_text = '<span class="redIndicator">'.$cimb_trans_info_row['cimb_amount'].'<br><span class="smallText">Does not match purchase amount.</span></span>';
                					}
                					
                					echo $mc_gross_display_text;
                				?>
                				</td>
              				</tr>
              				<tr>
              					<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
              				</tr>
              			</table>
        			</td>
      			</tr>
      		</table>
   		</td>
  	</tr>
<?
	}
} else {
	echo $payment_method_title_info;
	
	if ($order->info['orders_status'] == '1' || $order->info['orders_status'] == '7' || $order->info['orders_status'] == '2') {
		echo '<tr>
    			<td class="main">';
		echo tep_draw_form('cimb_status_check_form', FILENAME_ORDERS, tep_get_all_get_params(), 'post');
		echo tep_draw_hidden_field('subaction', 'payment_action');
		echo tep_draw_hidden_field('payment_action', 'check_trans_status');
		echo '<input type="submit" name="BBCheckTransStatusBtn" value="Check Payment Status" title="Check Payment Status" class="inputButton">';
		echo '</form>
				</td>
			  </tr>';
	}
}
?>
</table>