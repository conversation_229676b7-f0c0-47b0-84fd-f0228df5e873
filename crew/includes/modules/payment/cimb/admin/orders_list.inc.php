<?
include_once(DIR_FS_CATALOG_MODULES . 'payment/cimb/admin/languages/' . $language . '/cimb.lng.php');

$cimb_trans_info_select_sql = "SELECT * FROM " . TABLE_CIMB . " WHERE orders_id = '" . $order_obj->orders_id . "'";
$cimb_trans_info_result_sql= tep_db_query($cimb_trans_info_select_sql);

$cimb_payment_status_array = array(	'S'=>'Success',
									'F'=>'Failed',
									'U'=>'Unknown',
									'D'=>'Transaction not processed');
?>
<table border="0" width="100%" cellspacing="0" cellpadding="2">
	<tr>
		<td class="subInvoiceBoxHeading">
			<div style="border-bottom: 1px solid #996600; border-top: 1px solid #996600; padding: 2px;">Payment Details</div>
		</td>
	</tr>
<?
if ($cimb_trans_info_row = tep_db_fetch_array($cimb_trans_info_result_sql)) {
?>
	<tr>
    	<td class="main">
      		<table border="0" width="100%" cellspacing="0" cellpadding="2">
      			<tr valign="top">
        			<td width="50%">
              			<table border="0" cellspacing="0" cellpadding="2">
              				<tr>
                				<td class="invoiceRecords" valign="top" nowrap><?=ENTRY_CIMB_CHANNEL_ID?></td>
                				<td class="invoiceRecords" valign="top">:&nbsp;</td>
                				<td class="invoiceRecords" nowrap><?=(tep_not_null($cimb_trans_info_row['cimb_channel_id']) ? $cimb_trans_info_row['bibit_payment_method'] : TEXT_NOT_AVAILABLE)?></td>
              				</tr>
              				<tr>
                				<td class="invoiceRecords" valign="top" nowrap><?=ENTRY_CIMB_REFERENCE_NO?></td>
                				<td class="invoiceRecords" valign="top">:&nbsp;</td>
                				<td class="invoiceRecords" nowrap><?=$cimb_trans_info_row['cimb_reference_no']?></td>
              				</tr>
              				<tr>
                				<td class="invoiceRecords" valign="top" nowrap><?=ENTRY_CIMB_PAYMENT_CURRENCY?></td>
                				<td class="invoiceRecords" valign="top">:&nbsp;</td>
                				<td class="invoiceRecords"><?=$cimb_trans_info_row['cimb_currency']?></td>
              				</tr>
              				<tr>
                				<td class="invoiceRecords" valign="top" nowrap><?=ENTRY_CIMB_PAYMENT_AMOUNT?></td>
                				<td class="invoiceRecords" valign="top">:&nbsp;</td>
                				<td class="invoiceRecords"><?=$cimb_trans_info_row['cimb_amount']?></td>
              				</tr>
              			</table>
        			</td>
        			<td>
              			<table border="0" cellspacing="0" cellpadding="2">
              				<tr>
                				<td class="invoiceRecords" valign="top" nowrap><?=ENTRY_CIMB_USER_FULL_NAME?></td>
                				<td class="invoiceRecords" valign="top">:&nbsp;</td>
                				<td class="invoiceRecords"><?=(tep_not_null($cimb_trans_info_row['cimb_user_full_name']) ? $cimb_trans_info_row['bibit_cvc_status'] : TEXT_NOT_AVAILABLE)?></td>
              				</tr>
              				<tr>
                				<td class="invoiceRecords" valign="top" nowrap><?=ENTRY_CIMB_TRANSACTION_STATUS?></td>
                				<td class="invoiceRecords" valign="top">:&nbsp;</td>
                				<td class="invoiceRecords"><?=(isset($cimb_payment_status_array[$cimb_trans_info_row['cimb_status']])?$cimb_payment_status_array[$cimb_trans_info_row['cimb_status']]:'Unknown')?></td>
              				</tr>
              				<tr>
                				<td class="invoiceRecords" valign="top" nowrap><?=ENTRY_CIMB_TRANSACTION_DATE?></td>
                				<td class="invoiceRecords" valign="top">:&nbsp;</td>
                				<td class="invoiceRecords"><?=$cimb_trans_info_row['cimb_transaction_date']?></td>
              				</tr>
              				<tr>
                				<td class="invoiceRecords" valign="top" nowrap><?=ENTRY_CIMB_TRANSACTION_TIME?></td>
                				<td class="invoiceRecords" valign="top">:&nbsp;</td>
                				<td class="invoiceRecords" nowrap><?=$cimb_trans_info_row['cimb_transaction_time']?></td>
              				</tr>
              			</table>
              		</td>
      			</tr>
      		</table>
   		</td>
  	</tr>
<?
} else {
?>
	<tr>
		<td class="invoiceRecords">No further payment information is available.</td>
	</tr>
<?
}
?>