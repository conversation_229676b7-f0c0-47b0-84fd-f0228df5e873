<?php

require_once('payment_gateway.php');
require_once('coda/classes/Obj2xml.php');

class coda extends payment_gateway {

    var $code, $codaCurrencies;

    function coda($pm_id = '') {
        global $order, $languages_id, $default_languages_id, $currency;

        $this->code = 'coda';
        $this->title = $this->code;
        $this->preferred = true;
        $this->filename = 'coda.php';
        $this->auto_cancel_period = 30; // In minutes
        $this->connect_via_proxy = false; // localhost test - set to true, server test - set to false //need proxy to connect outside
        $this->force_to_checkoutprocess = true;
        $this->has_ipn = true;
        $this->payment_methods_id = 0;
        $this->payment_methods_parent_id = 0;

        $payment_methods_select_sql = "	SELECT  payment_methods_parent_id, payment_methods_code, 
												payment_methods_types_id, 
												payment_methods_receive_status_mode, payment_methods_id, 
												payment_methods_title, payment_methods_sort_order, 
												payment_methods_receive_status, payment_methods_legend_color, 
												payment_methods_logo 
										FROM " . TABLE_PAYMENT_METHODS . "
										WHERE 1 ";
        if ((int) $pm_id > 0) {
            $payment_methods_select_sql .= "	AND payment_methods_id = '" . (int) $pm_id . "'";
        } else {
            $payment_methods_select_sql .= "	AND payment_methods_filename = '" . tep_db_input($this->filename) . "'";
        }

        $payment_methods_result_sql = tep_db_query($payment_methods_select_sql);
        if ($payment_methods_row = tep_db_fetch_array($payment_methods_result_sql)) {
            $pm_desc_title_select_sql = "	SELECT payment_methods_description_title 
											FROM " . TABLE_PAYMENT_METHODS_DESCRIPTION . " 
											WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "' 
												AND (IF (languages_id = '" . tep_db_input($languages_id) . "', 1, IF ((	SELECT COUNT(payment_methods_id) > 0 
																														FROM " . TABLE_PAYMENT_METHODS_DESCRIPTION . " 
																														WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "' 
																															AND languages_id = '" . (int) $languages_id . "'), 0, languages_id = '" . (int) $default_languages_id . "')))";
            $pm_desc_title_result_sql = tep_db_query($pm_desc_title_select_sql);
            $pm_desc_title_row = tep_db_fetch_array($pm_desc_title_result_sql);
            $this->display_title = $pm_desc_title_row['payment_methods_description_title'];
            $this->description = $this->display_title;
            $this->enabled = $payment_methods_row['payment_methods_receive_status_mode'];
            $this->payment_methods_id = (int) $payment_methods_row['payment_methods_id'];
            $this->payment_methods_parent_id = (int) $payment_methods_row['payment_methods_parent_id'];
            $this->code = $payment_methods_row['payment_methods_code'];
            $this->title = $payment_methods_row['payment_methods_title'];
            $this->sort_order = $payment_methods_row['payment_methods_sort_order'];
            $this->legend_display_colour = $payment_methods_row['payment_methods_legend_color'];
        }

        $configuration_setting_array = $this->load_pm_setting();
        $this->order_status_id = $configuration_setting_array['MODULE_PAYMENT_CODA_ORDER_STATUS_ID'];
        $this->processing_status_id = $configuration_setting_array['MODULE_PAYMENT_CODA_PROCESSING_STATUS_ID'];
        $this->confirm_complete_days = $configuration_setting_array['MODULE_PAYMENT_CODA_CONFIRM_COMPLETE'];
        $this->require_address_information = $configuration_setting_array['MODULE_PAYMENT_CODA_MANDATORY_ADDRESS_FIELD'];
        $this->test_mode = ($configuration_setting_array['MODULE_PAYMENT_CODA_TEST_MODE'] == 'True' ? true : false);
        $this->message = $configuration_setting_array['MODULE_PAYMENT_CODA_MESSAGE'];
        $this->email_message = $configuration_setting_array['MODULE_PAYMENT_CODA_EMAIL_MESSAGE'];
        $this->email_footer = str_replace(array("<br>", "<BR>", "\r\n"), array("\n", "\n", ''), $this->email_message);

        $this->codaCurrencies = $this->get_support_currencies($this->payment_methods_id);
        $this->defCurr = DEFAULT_CURRENCY;
        $this->order_processing_status = ((int) $this->processing_status_id > 0 ? $this->processing_status_id : 7 );
        $this->order_status = ( (int) $this->order_status_id > 0 ? $this->order_status_id : DEFAULT_ORDERS_STATUS_ID );
        $this->checkout_mode = 'popup'; //iframe or popup
        $this->requestType = 'json'; //json or xml
        if ($this->test_mode) {
            $this->coda_css_url = 'https://sandbox.codapayments.com/airtime/css/airtime_v1.0.css';
            $this->coda_js_url = 'https://sandbox.codapayments.com/airtime/js/airtime_v1.0.js';
            $this->form_action_url = 'https://sandbox.codapayments.com/airtime/';
            $this->coda_status_url = 'https://sandbox.codapayments.com/airtime/api/restful/v1.0/Payment/inquiryPaymentResult/';
        } else {
            $this->coda_css_url = 'https://airtime.codapayments.com/airtime/css/airtime_v1.0.css';
            $this->coda_js_url = 'https://airtime.codapayments.com/airtime/js/airtime_v1.0.js';
            $this->form_action_url = 'https://airtime.codapayments.com/airtime/';
            $this->coda_status_url = 'https://airtime.codapayments.com/airtime/api/restful/v1.0/Payment/inquiryPaymentResult/';
        }
    }

    function javascript_validation() {
        return false;
    }

    function selection() {
        global $languages_id, $default_languages_id;
        $selection_array = array();

        $payment_methods_select_sql = "	SELECT payment_methods_id, payment_methods_code, payment_methods_types_id, payment_methods_logo, payment_methods_receive_status_mode 
										FROM " . TABLE_PAYMENT_METHODS . " 
										WHERE payment_methods_receive_status = 1 
											AND payment_methods_receive_status_mode <> 0 
											AND payment_methods_parent_id = '" . (int) $this->payment_methods_id . "' 
										ORDER BY payment_methods_sort_order";
        $payment_methods_result_sql = tep_db_query($payment_methods_select_sql);
        while ($payment_methods_row = tep_db_fetch_array($payment_methods_result_sql)) {
            $pm_desc_title_select_sql = "	SELECT payment_methods_description_title 
											FROM " . TABLE_PAYMENT_METHODS_DESCRIPTION . " 
											WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "' 
												AND (IF (languages_id = '" . tep_db_input($languages_id) . "', 1, IF ((	SELECT COUNT(payment_methods_id) > 0 
																														FROM " . TABLE_PAYMENT_METHODS_DESCRIPTION . " 
																														WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "' 
																															AND languages_id = '" . (int) $languages_id . "'), 0, languages_id = 1)))";
            $pm_desc_title_result_sql = tep_db_query($pm_desc_title_select_sql);
            $pm_desc_title_row = tep_db_fetch_array($pm_desc_title_result_sql);

            $pm_array = $this->load_pm_setting(1, $payment_methods_row['payment_methods_id']);

            $selection_array[] = array('payment_gateway_code' => $this->code,
                'payment_methods_receive_status_mode' => $payment_methods_row['payment_methods_receive_status_mode'],
                'payment_methods_id' => $payment_methods_row['payment_methods_id'],
                'payment_methods_code' => $payment_methods_row['payment_methods_code'],
                'payment_methods_types_id' => $payment_methods_row['payment_methods_types_id'],
                'payment_methods_parent_id' => $this->payment_methods_id,
                'payment_methods_logo' => $payment_methods_row['payment_methods_logo'],
                'payment_methods_description_title' => $pm_desc_title_row['payment_methods_description_title'],
                'currency' => $this->get_support_currencies($payment_methods_row['payment_methods_id']),
                'confirm_complete_days' => $this->confirm_complete_days,
                'show_billing_address' => $this->require_address_information,
                'show_contact_number' => $pm_array['MODULE_PAYMENT_CODA_MANDATORY_CONTACT_FIELD'],
                'show_ic' => $pm_array['MODULE_PAYMENT_CODA_MANDATORY_IC_FIELD']
            );

            if ($payment_methods_row['payment_methods_receive_status_mode'] == '-1') {
                $pm_status_message_select = "	SELECT payment_methods_status_message 
												FROM " . TABLE_PAYMENT_METHODS_STATUS_DESCRIPTION . " 
												WHERE payment_methods_mode = 'RECEIVE' 
													AND payment_methods_status = '-1' 
													AND languages_id = '" . (int) $languages_id . "' 
													AND payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "'";
                $pm_status_message_result = tep_db_query($pm_status_message_select);
                $pm_status_message_row = tep_db_fetch_array($pm_status_message_result);
                $selection_array[sizeof($selection_array) - 1]['payment_methods_status_message'] = $pm_status_message_row['payment_methods_status_message'];
            }
        }

        return $selection_array;
    }

    function set_support_currencies($currency_array) {
        $this->codaCurrencies = $currency_array;
    }

    function get_require_address_information() {
        return $this->require_address_information;
    }

    function draw_confirm_button() {
        return tep_image_button2('red', 'javascript:document.checkout_confirmation.submit()', MODULE_PAYMENT_CODA_TEXT_CART, 220);
    }

    function get_confirm_button_caption() {
        return MODULE_PAYMENT_CODA_TEXT_TITLE;
    }

    function pre_confirmation_check() {
        global $currency;

        if (in_array($currency, $this->codaCurrencies)) {
            
        }

        return false;
    }

    function confirmation() {
        
    }

    function process_button() {
        global $HTTP_POST_VARS, $shipping_cost, $total_cost, $shipping_selected, $shipping_method, $currencies, $currency, $customer_id, $order, $languages_id;
        global $order_logged, $OFFGAMERS, $country;

        if ($this->force_to_checkoutprocess && !tep_session_is_registered('order_logged'))
            return;
        $default_currency = $this->defCurr;
        $codaCurrency = $currency;
        if (!in_array($codaCurrency, $this->codaCurrencies)) {
            $codaCurrency = in_array($default_currency, $this->codaCurrencies) ? $default_currency : $this->codaCurrencies[0];
        }
        $this->get_merchant_account($codaCurrency);
        $OrderAmt = number_format($order->info['total'] * $currencies->get_value($codaCurrency), $currencies->get_decimal_places($codaCurrency), '.', '');
        if ($OrderAmt < $this->min_price || $OrderAmt > $this->max_price) {
            $minPrice = number_format($this->min_price, $currencies->currencies[$codaCurrency]['decimal_places'], $currencies->currencies[$codaCurrency]['decimal_point'], ',');
            $minPrice = $currencies->currencies[$codaCurrency]['symbol_left'] . $minPrice . $currencies->currencies[$codaCurrency]['symbol_right'];
            $maxPrice = number_format($this->max_price, $currencies->currencies[$codaCurrency]['decimal_places'], $currencies->currencies[$codaCurrency]['decimal_point'], ',');
            $maxPrice = $currencies->currencies[$codaCurrency]['symbol_left'] . $maxPrice . $currencies->currencies[$codaCurrency]['symbol_right'];
            tep_redirect(tep_href_link(FILENAME_CHECKOUT_PAYMENT, 'error_message=' . sprintf(MODULE_PAYMENT_CODA_CHECKOUT_LIMIT, $minPrice, $maxPrice), 'SSL', true, false));
        }
        $coda_request_array = array(
            'country' => $this->country_no,
            'currency' => $this->currency_no,
            'payType' => $this->payType,
            'requestType' => $this->requestType, //json or xml
            'orderId' => (string) $order_logged,
            'apiKey' => $this->secret_word,
            'items' => array(
                'code' => $order_logged,
                'name' => 'Purchase from ' . STORE_NAME,
                'price' => $OrderAmt
            ),
            'AccountId' => (string) $customer_id,
            'profile' => $customer_id
        );
        $result = $this->initTxn($coda_request_array);

        if (isset($result['resultCode']) && $result['resultCode'] == 0 && isset($result['txnId']) && !empty($result['txnId'])) {
            $_SESSION['codaTxnId'] = $result['txnId'];
            if ($this->checkout_mode == 'popup') {
                $process_button_string = '  
										<script type="text/javascript">
											airtime_checkout("' . $result['txnId'] . '");	
										</script>';
            } else {
                $referrer = $_SERVER['HTTP_REFERER'];
                $referrer = str_replace("\\+", "%20", $referrer);
                $referrer = str_replace("\\%21", "!", $referrer);
                $referrer = str_replace("\\%27", "'", $referrer);
                $referrer = str_replace("\\%28", "(", $referrer);
                $referrer = str_replace("\\%29", ")", $referrer);
                $referrer = str_replace("\\%7E", "~", $referrer);
                $process_button_string = '  
										<iframe width="640" scrolling="no" height="520" frameborder="1" src="' . $this->form_action_url . 'begin?host_url=' . $referrer . '&type=3&txn_id=' . $result['txnId'] . '&client_time=' . time() . '" id="inneriframe">
											&lt;p&gt;iframes are not supported by your browser.&lt;/p&gt;
										</iframe>';
            }
        } else {
            tep_redirect(tep_href_link(FILENAME_CHECKOUT_PAYMENT, 'error_message=' . MODULE_PAYMENT_CODA_TEXT_ERROR_MESSAGE, 'SSL', true, false));
        }

        return $process_button_string;
    }

    function postValidation($postData) {
        global $currencies;
    }

    function before_process() {
        global $order, $order_logged, $currency, $currencies;

        if (!tep_session_is_registered('order_logged'))
            return;
        $txnId = '';
        if (isset($_GET['TxnId']) && !empty($_GET['TxnId'])) {
            $txnId = $_GET['TxnId'];
        } else if (isset($_SESSION['codaTxnId'])) {
            $txnId = $_SESSION['codaTxnId'];
            unset($_SESSION['codaTxnId']);
        }
        if (!empty($txnId)) {
            $paymentResult = $this->check_trans_status($txnId);
            if (isset($paymentResult['resultCode']) && $paymentResult['resultCode'] == 0) {
                
            } else {
                tep_redirect(tep_href_link(FILENAME_CHECKOUT_PAYMENT, 'error_message=' . MODULE_PAYMENT_CODA_TEXT_ERROR_MESSAGE, 'SSL', true, false));
            }
        }
    }

    function after_process() {
        return false;
    }

    function link_to_payment($new_order_id) {
        global $order_logged;

        if (tep_session_is_registered('order_logged'))
            tep_session_unregister('order_logged');

        tep_session_register('order_logged');
        $order_logged = $new_order_id;

        require(DIR_WS_INCLUDES . 'modules/payment/coda/catalog/coda_splash.php');
        return;
    }

    function is_supported_currency($selected_currency) {
        return (in_array($selected_currency, $this->codaCurrencies) ? true : false);
    }

    function output_error() {
        return false;
    }

    function check() {
        if (!isset($this->_check)) {
            $payment_methods_select_sql = "	SELECT payment_methods_id
											FROM " . TABLE_PAYMENT_METHODS . " as pm
											WHERE pm.payment_methods_id = '" . (int) $this->payment_methods_id . "'";
            $payment_methods_result_sql = tep_db_query($payment_methods_select_sql);
            $this->_check = tep_db_num_rows($payment_methods_result_sql);
        }
        return $this->_check;
    }

    function is_processed_order($order_id) {
        
    }

    function load_pm_setting($language_id = 1, $pm_id = '') {
        $pm_setting_array = array();
        //load value from DB

        if (tep_not_null($pm_id)) {
            $payment_method_id = $pm_id;

            $payment_methods_parent_id_select_sql = "	SELECT payment_methods_parent_id 
														FROM " . TABLE_PAYMENT_METHODS . " 
														WHERE payment_methods_id = '" . (int) $pm_id . "'";
            $payment_methods_parent_id_result_sql = tep_db_query($payment_methods_parent_id_select_sql);
            $payment_methods_parent_id_row = tep_db_fetch_array($payment_methods_parent_id_result_sql);

            $payment_gateway_id = $payment_methods_parent_id_row['payment_methods_parent_id'];
        } else {
            $payment_method_id = $this->payment_methods_id;
            $payment_gateway_id = $this->payment_methods_parent_id;
        }

        $payment_configuration_info_select_sql = "	SELECT pci.payment_configuration_info_key, pcid.payment_configuration_info_value
													FROM " . TABLE_PAYMENT_CONFIGURATION_INFO . " as pci
													LEFT JOIN " . TABLE_PAYMENT_CONFIGURATION_INFO_DESCRIPTION . " as pcid 
														ON pci.payment_configuration_info_id = pcid.payment_configuration_info_id
															AND pcid.languages_id = '" . (int) $language_id . "'
													WHERE pci.payment_methods_id = '" . (int) $payment_method_id . "'
													ORDER BY pci.payment_configuration_info_sort_order";
        $payment_configuration_info_result_sql = tep_db_query($payment_configuration_info_select_sql);
        if (tep_db_num_rows($payment_configuration_info_result_sql)) {
            while ($payment_configuration_info_row = tep_db_fetch_array($payment_configuration_info_result_sql)) {
                $pm_setting_array[$payment_configuration_info_row['payment_configuration_info_key']] = $payment_configuration_info_row['payment_configuration_info_value'];
            }
        } else if ((int) $payment_gateway_id > 0) { //load value from PG
            $payment_configuration_info_select_sql = "	SELECT pci.payment_configuration_info_key, pcid.payment_configuration_info_value
														FROM " . TABLE_PAYMENT_CONFIGURATION_INFO . " as pci
														LEFT JOIN " . TABLE_PAYMENT_CONFIGURATION_INFO_DESCRIPTION . " as pcid 
															ON pci.payment_configuration_info_id = pcid.payment_configuration_info_id
																AND pcid.languages_id = '" . (int) $language_id . "'
														WHERE pci.payment_methods_id = '" . (int) $payment_gateway_id . "'
														ORDER BY pci.payment_configuration_info_sort_order";
            $payment_configuration_info_result_sql = tep_db_query($payment_configuration_info_select_sql);
            while ($payment_configuration_info_row = tep_db_fetch_array($payment_configuration_info_result_sql)) {
                $pm_setting_array[$payment_configuration_info_row['payment_configuration_info_key']] = $payment_configuration_info_row['payment_configuration_info_value'];
            }
        }

        return $pm_setting_array;
    }

    function get_merchant_account($selected_currency) {
        $payment_methods_instance_id = 0;
        $payment_gateway_instance_select_sql = "SELECT pmi.payment_methods_instance_id, pmi.payment_methods_instance_follow_default, pmi.currency_code 
				 								FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " as pmi
				 								WHERE pmi.payment_methods_id = '" . (int) $this->payment_methods_id . "'
				 								LIMIT 1";
        $payment_gateway_instance_result_sql = tep_db_query($payment_gateway_instance_select_sql);
        if (tep_db_num_rows($payment_gateway_instance_result_sql)) {
            $payment_gateway_instance_select_sql = "SELECT pmi.payment_methods_instance_id, pmi.payment_methods_instance_follow_default 
					 								FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " as pmi
					 								WHERE pmi.currency_code = '" . tep_db_input($selected_currency) . "' 
					 									AND pmi.payment_methods_id = '" . (int) $this->payment_methods_id . "'";
            $payment_gateway_instance_result_sql = tep_db_query($payment_gateway_instance_select_sql);
            if ($payment_gateway_instance_row = tep_db_fetch_array($payment_gateway_instance_result_sql)) {
                if ((int) $payment_gateway_instance_row['payment_methods_instance_follow_default'] > 0) { // Follow Default Currency Setting
                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_follow_default'];
                } else { // Has own setting
                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_id'];
                }
            }
        } else {
            $payment_gateway_instance_select_sql = "SELECT pmi.payment_methods_instance_id, pmi.payment_methods_instance_follow_default 
					 								FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " as pmi
					 								INNER JOIN " . TABLE_PAYMENT_METHODS . " as pm
					 									ON pm.payment_methods_parent_id = pmi.payment_methods_id
					 								WHERE pmi.currency_code = '" . tep_db_input($selected_currency) . "' 
					 									AND pm.payment_methods_id = '" . (int) $this->payment_methods_id . "'";
            $payment_gateway_instance_result_sql = tep_db_query($payment_gateway_instance_select_sql);
            if ($payment_gateway_instance_row = tep_db_fetch_array($payment_gateway_instance_result_sql)) {
                if ((int) $payment_gateway_instance_row['payment_methods_instance_follow_default'] > 0) {
                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_follow_default'];
                } else {
                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_id'];
                }
            }
        }

        $payment_gateway_instance_setting_select_sql = "SELECT pmis.payment_methods_instance_setting_key, pmis.payment_methods_instance_setting_value 
						 								FROM " . TABLE_PAYMENT_METHODS_INSTANCE_SETTING . " as pmis
						 								WHERE pmis.payment_methods_instance_id = '" . (int) $payment_methods_instance_id . "'";
        $payment_gateway_instance_setting_result_sql = tep_db_query($payment_gateway_instance_setting_select_sql);
        while ($payment_gateway_instance_setting_row = tep_db_fetch_array($payment_gateway_instance_setting_result_sql)) {
            switch ($payment_gateway_instance_setting_row['payment_methods_instance_setting_key']) {
                case 'MODULE_PAYMENT_CODA_SECRET':
                    $this->secret_word = $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];
                    break;
                case 'MODULE_PAYMENT_CODA_CURRENCY_NO':
                    $this->currency_no = $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];
                    break;
                case 'MODULE_PAYMENT_CODA_COUNTRY_NO':
                    $this->country_no = $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];
                    break;
                case 'MODULE_PAYMENT_CODA_PRICE_RANGE_MIN':
                    $this->min_price = $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];
                    break;
                case 'MODULE_PAYMENT_CODA_PRICE_RANGE_MAX':
                    $this->max_price = $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];
                    break;
                case 'MODULE_PAYMENT_CODA_PAYTYPE':
                    $this->payType = $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];
                    break;
            }
        }
    }

    function install() {
        $install_configuration_flag = true;
        if ($this->payment_methods_id > 0) {
            if ($this->receive_status != 1) {
                $update_receive_status_sql_data = array("payment_methods_receive_status" => 1);
                tep_db_perform(TABLE_PAYMENT_METHODS, $update_receive_status_sql_data, 'update', "payment_methods_id = '" . (int) $this->payment_methods_id . "'");
            } else {
                $install_configuration_flag = false;
            }
        } else {
            $install_payment_gateway_data_sql = array('payment_methods_code' => $this->code,
                'payment_methods_send_status' => 0,
                'payment_methods_title' => $this->code,
                'payment_methods_types_id' => 0,
                'payment_methods_parent_id' => 0,
                'payment_methods_legend_color' => '#A3B9A3',
                'payment_methods_sort_order' => 6,
                'payment_methods_filename' => $this->filename,
                'payment_methods_logo' => '',
                'date_added' => 'now()',
                'last_modified' => 'now()',
                'payment_methods_receive_status' => 1,
                'payment_methods_receive_status_mode' => 0
            );
            tep_db_perform(TABLE_PAYMENT_METHODS, $install_payment_gateway_data_sql);
            $this->payment_methods_id = tep_db_insert_id();
        }
        if ($install_configuration_flag) {
            $install_key_array = array();
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Set Order Status',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_CODA_ORDER_STATUS_ID',
                    'payment_configuration_info_description' => 'Set the status of orders made with this payment module to this value.',
                    'payment_configuration_info_sort_order' => '1335',
                    'set_function' => 'tep_cfg_pull_down_order_statuses(',
                    'use_function' => 'tep_get_order_status_name',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => '1',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Set Order\'s "Processing" Status',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_CODA_PROCESSING_STATUS_ID',
                    'payment_configuration_info_description' => 'Set the initial processing status of orders made with this payment module to this value.',
                    'payment_configuration_info_sort_order' => '1340',
                    'set_function' => 'tep_cfg_pull_down_order_statuses(',
                    'use_function' => 'tep_get_order_status_name',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => '7',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Payment Message',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_CODA_MESSAGE',
                    'payment_configuration_info_description' => 'Payment message will show up during checkout process (smart2pay).',
                    'payment_configuration_info_sort_order' => '1350',
                    'set_function' => 'tep_cfg_textarea(',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'Thank you for shopping at OffGamers.com.',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Payment Email Message',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_CODA_EMAIL_MESSAGE',
                    'payment_configuration_info_description' => 'This message will show up in order confirmation e-mail.',
                    'payment_configuration_info_sort_order' => '1360',
                    'set_function' => 'tep_cfg_textarea(',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'Thank you for shopping at OffGamers.com.',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Confirm Complete (in days)',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_CODA_CONFIRM_COMPLETE',
                    'payment_configuration_info_description' => 'The number of days in which an order made by this payment method is confirm completed.',
                    'payment_configuration_info_sort_order' => '1265',
                    'set_function' => '',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => '0',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Test Mode',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_CODA_TEST_MODE',
                    'payment_configuration_info_description' => 'Testing Mode?',
                    'payment_configuration_info_sort_order' => '100',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Require Address Information',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_CODA_MANDATORY_ADDRESS_FIELD',
                    'payment_configuration_info_description' => 'Set address field as required info during checkout.',
                    'payment_configuration_info_sort_order' => '1400',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );
            foreach ($install_key_array as $data) {
                tep_db_perform(TABLE_PAYMENT_CONFIGURATION_INFO, $data['info']);
                $payment_conf_id = tep_db_insert_id();

                $data['desc']['payment_configuration_info_id'] = (int) $payment_conf_id;
                tep_db_perform(TABLE_PAYMENT_CONFIGURATION_INFO_DESCRIPTION, $data['desc']);
            }
        }
        return 1;
    }

    function merchant_information_keys() {
        include_once(DIR_WS_CLASSES . "payment_methods.php");

        return array(
            'MODULE_PAYMENT_CODA_SECRET' => array('field' => 'text', 'mapping' => 'MODULE_PAYMENT_CODA_LNG_SECRET'),
            'MODULE_PAYMENT_CODA_CURRENCY_NO' => array('field' => 'text', 'mapping' => 'MODULE_PAYMENT_CODA_LNF_CURRENCY_NO'),
            'MODULE_PAYMENT_CODA_COUNTRY_NO' => array('field' => 'text', 'mapping' => 'MODULE_PAYMENT_CODA_LNG_COUNTRY_NO'),
            'MODULE_PAYMENT_CODA_PRICE_RANGE_MIN' => array('field' => 'text', 'mapping' => 'MODULE_PAYMENT_CODA_LNG_PRICE_RANGE_MIN'),
            'MODULE_PAYMENT_CODA_PRICE_RANGE_MAX' => array('field' => 'text', 'mapping' => 'MODULE_PAYMENT_CODA_LNG_PRICE_RANGE_MAX'),
            'MODULE_PAYMENT_CODA_PAYTYPE' => array('field' => 'text', 'mapping' => 'MODULE_PAYMENT_CODA_LNG_PAYTYPE'),
            'MODULE_PAYMENT_TAX' => array("field" => 'select', 'mapping' => "MODULE_PAYMENT_TAX", "select" => payment_methods::get_tax_option())
        );
    }

    function get_orders_payment_info_file() {
        return DIR_FS_CATALOG_MODULES . 'payment/coda/admin/orders.inc.php';
    }

    function get_orders_list_payment_info_file() {
        return DIR_FS_CATALOG_MODULES . 'payment/coda/admin/orders_list.inc.php';
    }

    function initTxn($codaRequestArray) {
        if ($this->requestType == 'xml') {
            return $this->initTxnXML($codaRequestArray);
        } else {
            return $this->initTxnJSON($codaRequestArray);
        }
    }

    function initTxnJSON($request) {
        $headers = array("Content-Type: application/json", "Accept: application/json");

        $json = json_encode(array("initRequest" => $request));

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $this->form_action_url . 'api/restful/v1.0/Payment/init/');
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $json);
        if ($this->connect_via_proxy)
            curl_setopt($ch, CURLOPT_PROXY, 'my-proxy.offgamers.lan:3128');

        $responseText = curl_exec($ch);
        $reader = json_decode(preg_replace('/"txnId":(\d+)/', '"txnId":"$1"', $responseText), true);

        $response = array();
        $response['resultCode'] = $reader['initResult']['resultCode'];
        $response['txnId'] = $reader['initResult']['txnId'];

        if ((int) $reader['initResult']['resultCode'] > 0) {
            $response['resultDesc'] = $reader['initResult']['resultDesc'];
        }

        return $response;
    }

    function initTxnXML($request) {
        $headers = array("Content-Type: application/xml", "Accept: application/xml");

        $converter = new Obj2xml("initRequest");
        $xml = $converter->toXml($request);

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $this->form_action_url . 'api/restful/v1.0/Payment/init/');
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $xml);
        if ($this->connect_via_proxy)
            curl_setopt($ch, CURLOPT_PROXY, 'my-proxy.offgamers.lan:3128');

        $responseText = curl_exec($ch);

        $xml = simplexml_load_string($responseText);
        $json = json_encode($xml);
        $array = json_decode(preg_replace('/"txnId":(\d+)/', '"txnId":"$1"', $json), TRUE);

        $response = array();
        $response['txnId'] = $array['txnId'];
        $response['resultCode'] = $array['resultCode'];

        if ((int) $response['resultCode'] > 0) {
            $response['resultDesc'] = $array['resultDesc'];
        }

        return $response;
    }

    function check_trans_status($txnId = '') {
        if (empty($txnId) && isset($_GET['oID']) && !empty($_GET['oID'])) {
            $coda_payment_select_sql = "	SELECT coda_txn_id
											FROM " . TABLE_CODA . "
											WHERE coda_order_id = '" . (int) tep_db_input($_GET['oID']) . "'";
            $coda_payment_result_sql = tep_db_query($coda_payment_select_sql);
            if ($coda_payment_result_row = tep_db_fetch_array($coda_payment_result_sql)) {
                $txnId = $coda_payment_result_row['coda_txn_id'];
            }
        }
        $responseArray = array();
        if ($this->requestType == 'xml') {
            $responseArray = $this->inquiryPaymentXML($txnId);
        } else {
            $responseArray = $this->inquiryPaymentJSON($txnId);
        }
        if (!empty($responseArray)) {
            $coda_payment_history_data_array = array('coda_orders_id' => $responseArray['orderId'],
                'coda_result_code' => $responseArray['resultCode'],
                'coda_result_description' => ($responseArray['resultCode'] == 216 ? 'Cancel Purchase' : $responseArray['resultDesc']),
                'coda_total_price' => $responseArray['totalPrice'],
                'coda_date' => 'now()',
                'changed_by' => 'system'
            );
            tep_db_perform(TABLE_CODA_STATUS_HISTORY, $coda_payment_history_data_array);
        }
        return $responseArray;
    }

    function inquiryPaymentJSON($txnId) {
        $headers = array("Content-Type: application/json", "Accept: application/json");

        $requestArray = array(
            'txnId' => $txnId,
            'apiKey' => $this->secret_word
        );
        $json = json_encode(array("inquiryPaymentRequest" => $requestArray));

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $this->coda_status_url);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $json);
        if ($this->connect_via_proxy)
            curl_setopt($ch, CURLOPT_PROXY, 'my-proxy.offgamers.lan:3128');

        $responseText = curl_exec($ch);
        $reader = json_decode(preg_replace('/"txnId":(\d+)/', '"txnId":"$1"', $responseText), true);

        $responseArray = array(
            'resultCode' => $reader['paymentResult']['resultCode'],
            'txnId' => $reader['paymentResult']['txnId'],
            'orderId' => $reader['paymentResult']['orderId'],
            'resultDesc' => $reader['paymentResult']['resultDesc'],
            'totalPrice' => $reader['paymentResult']['totalPrice']
        );

        return $responseArray;
    }

    function inquiryPaymentXML($txnId) {
        $headers = array("Content-Type: application/xml", "Accept: application/xml");

        $requestArray = array(
            'txnId' => $txnId,
            'apiKey' => $this->secret_word
        );

        $converter = new Obj2xml("inquiryPaymentRequest");
        $xml = $converter->toXml($requestArray);

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $this->coda_status_url);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $xml);
        if ($this->connect_via_proxy)
            curl_setopt($ch, CURLOPT_PROXY, 'my-proxy.offgamers.lan:3128');

        $responseText = curl_exec($ch);

        $xml = simplexml_load_string($responseText);
        $json = json_encode($xml);
        $array = json_decode(preg_replace('/"txnId":(\d+)/', '"txnId":"$1"', $json), TRUE);

        $responseArray = array(
            'orderId' => $array['orderId'],
            'txnId' => $array['txnId'],
            'resultCode' => $array['resultCode'],
            'resultDesc' => $array['resultDesc'],
            'totalPrice' => $array['totalPrice']
        );

        return $responseArray;
    }

}

?>