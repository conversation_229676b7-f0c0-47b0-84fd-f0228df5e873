<?php
include_once(DIR_FS_CATALOG_MODULES . 'payment/coda/admin/languages/' . $language . '/coda.lng.php');

$coda_trans_info_select_sql = "	SELECT coda_txn_id, coda_result_code, coda_total_price, coda_checksum
								FROM " . TABLE_CODA . " 
								WHERE coda_order_id = '" . (int)$oID . "'";
$coda_trans_info_result_sql= tep_db_query($coda_trans_info_select_sql);

$coda_trans_history_select_sql = "	SELECT coda_date, coda_result_description, coda_result_code, coda_total_price, changed_by
									FROM " . TABLE_CODA_STATUS_HISTORY . "
									WHERE coda_orders_id = '" . (int)$oID  . "' 
									ORDER BY coda_date";
$coda_trans_history_result_sql = tep_db_query($coda_trans_history_select_sql);
?>
<table border="0" width="100%" cellspacing="0" cellpadding="2">
<? ob_start(); ?>
	<tr>
		<td class="main">
      		<table border="0" width="100%" cellspacing="0" cellpadding="2">
      			<tr>
      				<td class="main" width="12%" nowrap><b><?=ENTRY_PAYMENT_METHOD?></b></td>
      				<td class="main">
      					<div>
      						<? if((int)$order->info['payment_methods_id']>0) { ?><div class="<?=((int)$payment_info_array[$order->info['payment_methods_id']]->confirm_complete_days>0?'rp_icon':'nrp_icon')?>"><b><?=((int)$payment_info_array[$order->info['payment_methods_id']]->confirm_complete_days>0?'RP':'NRP')?></b></div> <? } ?>
      						<div style="width:30px; height:15px; background-color:<?=$payment_module_info['display_colour']?>; float:left; vertical-align:top; text-align:center;">&nbsp;</div>
      						<div style="vertical-align: top">
      							&nbsp;&nbsp;
<?
	if ($view_payment_details_permission) {
		if (tep_not_null($last_payment_method) && $last_payment_method['payment_methods_id'] != $order->info['payment_methods_id']) {
			echo '<span class="redIndicator">'.(tep_not_null($payment_module_info['payment_methods_parent_title'])?$payment_module_info['payment_methods_parent_title'] . ' - ' :'') . $payment_module_info['payment_methods_title'].'</span>';
		} else {
			echo (tep_not_null($payment_module_info['payment_methods_parent_title'])?$payment_module_info['payment_methods_parent_title'] . ' - ' :'') . $payment_module_info['payment_methods_title'];
		}
		
		if (tep_not_null($last_payment_method) && $last_payment_method['payment_methods_id'] != $order->info['payment_methods_id']) {
			echo '&nbsp;&nbsp;<span class="redIndicator">(Last Payment Method: ' . (tep_not_null($last_payment_method['parent_payment_methods_title'])? $last_payment_method['parent_payment_methods_title'] . ' - ' : '' ) . $last_payment_method['payment_methods_title'] . ')</span>';
		}
	} else {
		echo (tep_not_null($payment_module_info['payment_methods_parent_title'])?$payment_module_info['payment_methods_parent_title'] . ' - ' :'') . $payment_module_info['payment_methods_title'];
	}
?>
							</div>
      					</div>
      				</td>
				</tr>
			</table>
		</td>
	</tr>
<?
$payment_method_title_info = ob_get_contents();
ob_end_clean();

if (tep_db_num_rows($coda_trans_info_result_sql) || tep_db_num_rows($coda_trans_history_result_sql)) {
	$coda_trans_info_row = tep_db_fetch_array($coda_trans_info_result_sql);
	
	echo $payment_method_title_info;
	if (!$view_payment_details_permission) {
		;
	} else {
?>
  	<tr>
    	<td class="main">
      		<table border="0" width="100%" cellspacing="0" cellpadding="2">
      			<tr valign="top">
        			<td width="35%">
              			<table border="0" cellspacing="0" cellpadding="2">
              				<tr>
                				<td class="main" valign="top" nowrap><b><?=ENTRY_CODA_TRANSACTION_ID?></b>&nbsp;</td>
                				<td class="main"><?=$coda_trans_info_row["coda_txn_id"]?></td>
              				</tr>
              				<tr>
                				<td class="main" valign="top" nowrap><b><?=ENTRY_CODA_RESULT_CODE?></b>&nbsp;</td>
                				<td class="main" nowrap><?=$coda_trans_info_row["coda_result_code"]?></td>
              				</tr>
              				<tr>
                				<td class="main" valign="top" nowrap><b><?=ENTRY_CODA_TOTAL_PRICE?></b>&nbsp;</td>
                				<td class="main" nowrap><?=$coda_trans_info_row["coda_total_price"]?></td>
              				</tr>
              				<tr>
                				<td class="main" valign="top" nowrap><b><?=ENTRY_CODA_CHECKSUM?></b>&nbsp;</td>
                				<td class="main" nowrap><?=$coda_trans_info_row["coda_checksum"]?></td>
              				</tr>
              			</table>
        			</td>
        			<td>
              			<table border="0" cellspacing="0" cellpadding="2">
              				<tr>
                				<td class="main" colspan="2" nowrap>
                					<table border="1" cellspacing="0" cellpadding="2">
        								<tr>
                							<td class="smallText" nowrap><b><?=TABLE_HEADING_CODA_DATE?></b></td>
                							<td class="smallText" nowrap><b><?=TABLE_HEADING_CODA_STATUS?></b></td>
                							<td class="smallText" nowrap><b><?=TABLE_HEADING_CODA_DESCRIPTION?></b></td>
											<td class="smallText" nowrap><b><?=TABLE_HEADING_CODA_TOTAL_PRICE?></b></td>
                							<td class="smallText" nowrap><b><?=TABLE_HEADING_CODA_CHANGED_BY?></b></td>
                						</tr>
<?		while ($coda_trans_history_row = tep_db_fetch_array($coda_trans_history_result_sql)) {
        	echo ' 						<tr>
                							<td class="smallText" nowrap>'.$coda_trans_history_row['coda_date'].'</td>
                							<td class="smallText" nowrap>'.($coda_trans_history_row['coda_result_code'] == 0 ? 'Successful' : (tep_not_null($coda_trans_history_row['coda_result_code']) ? $coda_trans_history_row['coda_result_code'] : 'Unknown')).'</td>
											<td class="smallText" nowrap>'.$coda_trans_history_row['coda_result_description'].'</td>
											<td class="smallText" nowrap>'.$coda_trans_history_row['coda_total_price'].'</td>
											<td class="smallText" nowrap>'.$coda_trans_history_row['changed_by'].'</td>
                						</tr>';
     	}
?>
                					</table>
									<br>
<?php 
								if ($order->info['orders_status'] == '1' || $order->info['orders_status'] == '7' || $order->info['orders_status'] == '2') {
	                					echo "&nbsp;&nbsp;";
										echo tep_draw_form('coda_status_check_form', FILENAME_ORDERS, tep_get_all_get_params(), 'post');
										echo tep_draw_hidden_field('subaction', 'payment_action');
										echo tep_draw_hidden_field('payment_action', 'check_trans_status');
										echo tep_submit_button('Check Payment Status', 'Check Payment Status', 'name="codaCheckTransStatusBtn"', 'inputButton');
										echo "</form>";
									}
?>
                				</td>
                			</tr>
              			</table>
              		</td>
      			</tr>
      		</table>
   		</td>
  	</tr>
<?
	}
} else {
	echo $payment_method_title_info;
}
?>
</table>