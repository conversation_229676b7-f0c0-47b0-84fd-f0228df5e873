<?

class dineromail {

    var $code, $title, $description, $enabled, $sort_order;
    var $preferred, $payee_code, $dineromail_acc_id, $dineromail_password, $dineromailCurrencies, $comment;
    var $check_trans_status_flag, $dineromail_checkout_country;

    function dineromail($pm_id = '') {
        global $order, $languages_id, $default_languages_id, $currency, $defCurr;
        global $customer_country_id, $amount_decimal;

        $this->code = 'dineromail';
        $this->filename = 'dineromail.php';
        $this->title = $this->code;
        $this->preferred = true;
        $this->auto_cancel_period = 60; // In minutes
        $this->order_processing_status = 7;
        $this->connect_via_proxy = false;
        $this->force_to_checkoutprocess = true;
        $this->has_ipn = true;
        $this->amount_decimal = 2;

        $this->check_trans_status_flag = false;

        $payment_methods_select_sql = "	SELECT payment_methods_parent_id, payment_methods_code, 
												payment_methods_types_id, 
												payment_methods_receive_status_mode, payment_methods_id, 
												payment_methods_title, payment_methods_sort_order, 
												payment_methods_receive_status, payment_methods_legend_color, 
												payment_methods_logo 
										FROM " . TABLE_PAYMENT_METHODS . " 
										WHERE 1 ";
        if ((int) $pm_id > 0) {
            $payment_methods_select_sql .= "	AND payment_methods_id = '" . (int) $pm_id . "'";
        } else {
            $payment_methods_select_sql .= "	AND payment_methods_filename = '" . tep_db_input($this->filename) . "'";
        }

        $payment_methods_result_sql = tep_db_query($payment_methods_select_sql);
        if ($payment_methods_row = tep_db_fetch_array($payment_methods_result_sql)) {
            $pm_desc_title_select_sql = "	SELECT payment_methods_description_title 
											FROM " . TABLE_PAYMENT_METHODS_DESCRIPTION . " 
											WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "' 
												AND (IF (languages_id = '" . tep_db_input($languages_id) . "', 1, IF ((	SELECT COUNT(payment_methods_id) > 0 
																														FROM " . TABLE_PAYMENT_METHODS_DESCRIPTION . " 
																														WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "' 
																															AND languages_id = '" . (int) $languages_id . "'), 0, languages_id = '" . (int) $default_languages_id . "')))";

            $pm_desc_title_result_sql = tep_db_query($pm_desc_title_select_sql);
            $pm_desc_title_row = tep_db_fetch_array($pm_desc_title_result_sql);

            $this->enabled = $payment_methods_row['payment_methods_receive_status_mode'];
            $this->payment_methods_id = (int) $payment_methods_row['payment_methods_id'];
            $this->payment_methods_parent_id = (int) $payment_methods_row['payment_methods_parent_id'];
            $this->code = $payment_methods_row['payment_methods_code'];
            $this->title = $payment_methods_row['payment_methods_title'];

            $this->display_title = $pm_desc_title_row['payment_methods_description_title'];
            $this->description = $this->display_title;
            $this->sort_order = $payment_methods_row['payment_methods_sort_order'];
            $this->legend_display_colour = $payment_methods_row['payment_methods_legend_color'];

            // DINEROMAIL
            $this->dineromailCurrencies = $this->get_support_currencies($this->payment_methods_id);

            $configuration_setting_array = $this->load_pm_setting();
            $this->order_status_id = $configuration_setting_array['MODULE_PAYMENT_DINEROMAIL_ORDER_STATUS_ID'];
            $this->processing_status_id = $configuration_setting_array['MODULE_PAYMENT_DINEROMAIL_PROCESSING_STATUS_ID'];
            $this->confirm_complete_days = $configuration_setting_array['MODULE_PAYMENT_DINEROMAIL_CONFIRM_COMPLETE'];
            $this->message = $configuration_setting_array['MODULE_PAYMENT_DINEROMAIL_MESSAGE'];
            $this->mediospago = $configuration_setting_array['MODULE_PAYMENT_DINEROMAIL_MEDIOSPAGO'];

            $this->email_message = $configuration_setting_array['MODULE_PAYMENT_DINEROMAIL_EMAIL_MESSAGE'];
            $this->email_footer = str_replace(array("<br>", "<BR>", "\r\n"), array("\n", "\n", ''), $this->email_message);
            $this->order_processing_status = ((int) $this->processing_status_id > 0 ? $this->processing_status_id : 7 );
            $this->order_status = ( (int) $this->order_status_id > 0 ? $this->order_status_id : DEFAULT_ORDERS_STATUS_ID );

            $this->require_address_information = $configuration_setting_array['MODULE_PAYMENT_DINEROMAIL_MANDATORY_ADDRESS_FIELD'];
            $this->require_ic_information = $configuration_setting_array['MODULE_PAYMENT_DINEROMAIL_MANDATORY_IC_FIELD'];
            $this->require_contact_information = $configuration_setting_array['MODULE_PAYMENT_DINEROMAIL_MANDATORY_CONTACT_FIELD'];
            $this->customer_payment_info = $configuration_setting_array['MODULE_PAYMENT_DINEROMAIL_CUSTOMER_PAYMENT_INFO'];
        }
    }

    function javascript_validation() {
        return false;
    }

    function selection() {
        global $languages_id, $default_languages_id;

        $selection_array = array();

        $payment_methods_select_sql = "	SELECT payment_methods_id, payment_methods_code, payment_methods_types_id, payment_methods_logo, payment_methods_receive_status_mode 
										FROM " . TABLE_PAYMENT_METHODS . " 
										WHERE payment_methods_receive_status = 1 
											AND payment_methods_receive_status_mode <> 0 
											AND payment_methods_parent_id = '" . (int) $this->payment_methods_id . "' 
										ORDER BY payment_methods_sort_order";
        $payment_methods_result_sql = tep_db_query($payment_methods_select_sql);
        while ($payment_methods_row = tep_db_fetch_array($payment_methods_result_sql)) {
            $pm_desc_title_select_sql = "	SELECT payment_methods_description_title 
											FROM " . TABLE_PAYMENT_METHODS_DESCRIPTION . " 
											WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "' 
												AND (IF (languages_id = '" . tep_db_input($languages_id) . "', 1, IF ((	SELECT COUNT(payment_methods_id) > 0 
																														FROM " . TABLE_PAYMENT_METHODS_DESCRIPTION . " 
																														WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "' 
																															AND languages_id = '" . (int) $languages_id . "'), 0, languages_id = '" . (int) $default_languages_id . "')))";
            $pm_desc_title_result_sql = tep_db_query($pm_desc_title_select_sql);
            $pm_desc_title_row = tep_db_fetch_array($pm_desc_title_result_sql);

            $pm_array = $this->load_pm_setting(1, $payment_methods_row['payment_methods_id']);

            $selection_array[] = array('payment_gateway_code' => $this->code,
                'payment_methods_receive_status_mode' => $payment_methods_row['payment_methods_receive_status_mode'],
                'payment_methods_id' => $payment_methods_row['payment_methods_id'],
                'payment_methods_code' => $payment_methods_row['payment_methods_code'],
                'payment_methods_types_id' => $payment_methods_row['payment_methods_types_id'],
                'payment_methods_parent_id' => $this->payment_methods_id,
                'payment_methods_logo' => $payment_methods_row['payment_methods_logo'],
                'payment_methods_description_title' => $pm_desc_title_row['payment_methods_description_title'],
                'currency' => $this->get_support_currencies($payment_methods_row['payment_methods_id']),
                'show_billing_address' => $pm_array['MODULE_PAYMENT_DINEROMAIL_MANDATORY_ADDRESS_FIELD'],
                'confirm_complete_days' => $pm_array['MODULE_PAYMENT_DINEROMAIL_CONFIRM_COMPLETE'],
                'show_contact_number' => $pm_array['MODULE_PAYMENT_DINEROMAIL_MANDATORY_CONTACT_FIELD'],
                'show_ic' => $pm_array['MANDATORY_IC_FIELD']
            );

            if ($payment_methods_row['payment_methods_receive_status_mode'] == '-1') { // Inactive
                $pm_status_message_select = "	SELECT payment_methods_status_message 
												FROM " . TABLE_PAYMENT_METHODS_STATUS_DESCRIPTION . " 
												WHERE payment_methods_mode = 'RECEIVE' 
													AND payment_methods_status = '-1' 
													AND languages_id = '" . (int) $languages_id . "' 
													AND payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "'";
                $pm_status_message_result = tep_db_query($pm_status_message_select);
                $pm_status_message_row = tep_db_fetch_array($pm_status_message_result);
                $selection_array[sizeof($selection_array) - 1]['payment_methods_status_message'] = $pm_status_message_row['payment_methods_status_message'];
            }
        }

        return $selection_array;
    }

    function get_require_address_information() {
        return $this->require_address_information;
    }

    function get_confirm_complete_days() {
        return $this->confirm_complete_days;
    }

    function get_pm_info() {
        return $this->message;
    }

    function get_email_pm_info() {
        return $this->email_message;
    }

    function draw_confirm_button() {
        return tep_image_button2('red', 'javascript:document.checkout_confirmation.submit()', MODULE_PAYMENT_DINEROMAIL_TEXT_CART, 200);
    }

    function get_confirm_button_caption() {
        return MODULE_PAYMENT_DINEROMAIL_TEXT_TITLE;
    }

    function pre_confirmation_check() {
        global $currency;

        if (in_array($currency, $this->dineromailCurrencies)) {
            
        }

        return false;
    }

    function set_support_currencies($currency_array) {
        $this->dineromailCurrencies = $currency_array;
    }

    function get_support_currencies($pm_id, $by_zone = true) {
        global $zone_info_array;

        $default_currency = '';
        $support_currencies_array = array();

        $currency_code_select_sql = "	SELECT currency_code, payment_methods_instance_default 
    									FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " 
    									WHERE payment_methods_id = '" . (int) $pm_id . "' 
    									ORDER BY currency_code";
        $currency_code_result_sql = tep_db_query($currency_code_select_sql);

        if (tep_db_num_rows($currency_code_result_sql) > 0) {
            while ($currency_code_row = tep_db_fetch_array($currency_code_result_sql)) {
                $support_currencies_array[] = $currency_code_row['currency_code'];

                if ($currency_code_row['payment_methods_instance_default'] == 1) {
                    $default_currency = $currency_code_row['currency_code'];
                }
            }
        } else {
            if ($this->payment_methods_parent_id > 0) {
                $payment_methods_parent_id = $this->payment_methods_parent_id;
            } else {
                $payment_methods_parent_id = $this->payment_methods_id;
            }

            $parent_currency_code_select_sql = "	SELECT currency_code, payment_methods_instance_default 
			    									FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " 
			    									WHERE payment_methods_id = '" . (int) $payment_methods_parent_id . "' 
			    									ORDER BY currency_code";
            $parent_currency_code_result_sql = tep_db_query($parent_currency_code_select_sql);
            while ($parent_currency_code_row = tep_db_fetch_array($parent_currency_code_result_sql)) {
                $support_currencies_array[] = $parent_currency_code_row['currency_code'];

                if ($parent_currency_code_row['payment_methods_instance_default'] == 1) {
                    $default_currency = $parent_currency_code_row['currency_code'];
                }
            }
        }

        if (tep_not_null($default_currency)) {
            $this->defCurr = $default_currency;
        }

        if ($by_zone) {
            if (isset($zone_info_array)) {
                $support_currencies_array = array_intersect($zone_info_array[3]->zone_currency_id, $support_currencies_array);
                if (count($support_currencies_array)) {
                    array_multisort($support_currencies_array);
                } else {
                    $support_currencies_array = array($default_currency);
                }
            } else {
                $support_currencies_array = array($default_currency);
            }
        } else {
            $this->set_support_currencies($support_currencies_array);
            // All found currencies is supported. Normally this is used for backend script such as IPN call
        }

        return $support_currencies_array;
    }

    function process_button() {
        global $HTTP_POST_VARS, $shipping_cost, $total_cost, $shipping_selected, $shipping_method, $order, $languages_id;
        global $order_logged, $OFFGAMERS, $currencies, $currency, $customer_id;

        if ($this->force_to_checkoutprocess && !tep_session_is_registered('order_logged')) {
            return;
        }

        $dineromailCurrency = $currency;
        if (!in_array($currency, $this->dineromailCurrencies)) {
            $dineromailCurrency = $this->dineromailCurrencies[0];
        }

        $this->get_merchant_account($dineromailCurrency);

        $TipoMoneda = 1;
        if ($dineromailCurrency == 'USD') {
            $TipoMoneda = 2;
        }

        $OrderAmt = number_format($order->info['total'] * $currencies->get_value($dineromailCurrency), $currencies->get_decimal_places($dineromailCurrency), '.', '');

        //TipoMoneda : 1 = Pesos, 2 = Dollars 
        $process_button_string = tep_draw_hidden_field('NombreItem', 'Purchase from ' . STORE_NAME) .
                tep_draw_hidden_field('TipoMoneda', $TipoMoneda) .
                tep_draw_hidden_field('PrecioItem', $OrderAmt) .
                tep_draw_hidden_field('usr_nombre', $order->customer['firstname']) .
                tep_draw_hidden_field('usr_apellido', $order->customer['lastname']) .
                tep_draw_hidden_field('usr_tel_numero', $order->customer['telephone']) .
                tep_draw_hidden_field('usr_email', $order->customer['email_address']) .
                tep_draw_hidden_field('E_Comercio', $this->dineromail_acc_id) .
                tep_draw_hidden_field('MediosPago', $this->mediospago) .
                tep_draw_hidden_field('trx_id', $order_logged) .
                tep_draw_hidden_field('image_url', '') .
                tep_draw_hidden_field('DireccionEnvio', '0') .
                tep_draw_hidden_field('DireccionExito', tep_href_link(FILENAME_CHECKOUT_PROCESS, '', 'SSL')) .
                tep_draw_hidden_field('DireccionFracaso', tep_href_link(FILENAME_CHECKOUT_PAYMENT, MODULE_PAYMENT_DINEROMAIL_TEXT_ERROR_MESSAGE));
        return $process_button_string;
    }

    function before_process() {
        global $HTTP_POST_VARS, $order, $order_logged, $currency;

        $this->get_merchant_account($currency);
        if (!tep_session_is_registered('order_logged')) {
            return;
        }
    }

    function after_process() {
        return false;
    }

    function link_to_payment($new_order_id) {
        global $order_logged;

        if (tep_session_is_registered('order_logged'))
            tep_session_unregister('order_logged');

        tep_session_register('order_logged');
        $order_logged = $new_order_id;
        require(DIR_WS_INCLUDES . 'modules/payment/dineromail/catalog/dineromail_splash.php');
        return;
    }

    function is_supported_currency($selected_currency) {
        return (in_array($selected_currency, $this->dineromailCurrencies) ? true : false);
    }

    function check_trans_status($order_id) {
        global $currencies;
        $dineromail_report_status = array('1' => 'CORRECT',
            '2' => 'INCORRECT XML',
            '3' => 'INVALID ACCOUNT NUMBER (SYNTAX VALIDATION)',
            '4' => 'INVALID PASSWORD (SYNTAX VALIDATION)',
            '5' => 'INVALID QUERY TYPE (SYNTAX VALIDATION)',
            '6' => 'INVALID OPERATION ID (SYNTAX VALIDATION)',
            '7' => 'INVALID ACCOUNT NUMBER OR PASSWORD',
            '8' => 'NO OPERATIONS TO REPORT ABOUT WERE FOUND');



        $order_info_select_sql = "	SELECT o.payment_methods_id, o.currency, o.currency_value, ot.value, ot.text 
									FROM " . TABLE_ORDERS . " AS o 
									INNER JOIN " . TABLE_ORDERS_TOTAL . " AS ot 
										ON o.orders_id=ot.orders_id AND ot.class='ot_total' 
									WHERE o.orders_id = '" . tep_db_input($order_id) . "'";
        $order_info_result_sql = tep_db_query($order_info_select_sql);
        if ($order_info_row = tep_db_fetch_array($order_info_result_sql)) {

            $dineromail_currencies = $this->get_support_currencies($this->payment_methods_id, false);
            $dineromail_currency = $order_info_row['currency'];
            if (!in_array($dineromail_currency, $dineromail_currencies)) {
                $dineromail_currency = $dineromail_currencies[0];
            }
            $this->get_merchant_account($dineromail_currency);

            $tipo = 1;
            $poststring = 'data=%3CREPORTE%3E%3CNROCTA%3E' . $this->dineromail_acc_id . '%3C%2FNROCTA%3E%3CDETALLE%3E%3CCONSULTA%3E%3CCLAVE%3E' . $this->dineromail_password . '%3C%2FCLAVE%3E%3CTIPO%3E' . $tipo . '%3C%2FTIPO%3E%3COPERACIONES%3E%3CID%3E' . $order_id . '%3C%2FID%3E%3C%2FOPERACIONES%3E%3C%2FCONSULTA%3E%3C%2FDETALLE%3E%3C%2FREPORTE%3E';
            $dineromail_xml_response = $this->curl_connect($this->form_status_url, $poststring);

            if ($dineromail_xml_response['estadoreporte'] == '1') {
                $dineromail_gross_amt = number_format($dineromail_xml_response['amount'], $currencies->currencies[$dineromail_currency]['decimal_places'], $currencies->currencies[$dineromail_currency]['decimal_point'], $currencies->currencies[$dineromail_currency]['thousands_point']);
                if ($currencies->currencies[$dineromail_currency]['symbol_left'] . $dineromail_gross_amt . $currencies->currencies[$dineromail_currency]['symbol_right'] == strip_tags($order_info_row['text'])) {
                    if ($dineromail_xml_response['status'] == '2') {
                        $this->check_trans_status_flag = true;

                        $dineromail_payment_history_data_array = array('orders_id' => (int) $order_id,
                            'dineromail_status_date' => 'now()',
                            'dineromail_status' => tep_db_prepare_input(($dineromail_xml_response['status'] ? $dineromail_xml_response['status'] : '')),
                            'dineromail_status_description' => "Status: " . $this->get_dineromail_status($dineromail_xml_response['status'])
                        );
                        tep_db_perform(TABLE_DINEROMAIL_STATUS_HISTORY, $dineromail_payment_history_data_array);
                    } else {
                        $dineromail_payment_history_data_array = array('orders_id' => (int) $order_id,
                            'dineromail_status_date' => 'now()',
                            'dineromail_status' => tep_db_prepare_input(($dineromail_xml_response['status'] ? $dineromail_xml_response['status'] : '')),
                            'dineromail_status_description' => "Status: " . $this->get_dineromail_status($dineromail_xml_response['status'])
                        );
                        tep_db_perform(TABLE_DINEROMAIL_STATUS_HISTORY, $dineromail_payment_history_data_array);
                    }
                } else {
                    $dineromail_payment_history_data_array = array('orders_id' => (int) $order_id,
                        'dineromail_status_date' => 'now()',
                        'dineromail_status' => tep_db_prepare_input(($dineromail_xml_response['status'] ? $dineromail_xml_response['status'] : '')),
                        'dineromail_status_description' => 'Error: Amount not match (' . $currencies->currencies[$dineromail_currency]['symbol_left'] . $dineromail_gross_amt . $currencies->currencies[$dineromail_currency]['symbol_right'] . ' != ' . strip_tags($order_info_row['text']) . ')'
                    );
                    tep_db_perform(TABLE_DINEROMAIL_STATUS_HISTORY, $dineromail_payment_history_data_array);
                }
            } else {
                $dineromail_payment_history_data_array = array('orders_id' => (int) $order_id,
                    'dineromail_status_date' => 'now()',
                    'dineromail_status' => tep_db_prepare_input(($dineromail_xml_response['status'] ? $dineromail_xml_response['status'] : '')),
                    'dineromail_status_description' => 'Error: Report = ' . (isset($dineromail_report_status[$dineromail_xml_response['estadoreporte']]) ? $dineromail_report_status[$dineromail_xml_response['estadoreporte']] : 'Unknown(' . $dineromail_xml_response['estadoreporte'] . ')')
                );
                tep_db_perform(TABLE_DINEROMAIL_STATUS_HISTORY, $dineromail_payment_history_data_array);
            }
        }
    }

    function get_payee_code() {
        return $this->payee_code;
    }

    function get_user_password() {
        return $this->user_password;
    }

    function output_error() {
        return false;
    }

    function check() {
        if (!isset($this->_check)) {
            $payment_methods_select_sql = "	SELECT payment_methods_id
											FROM " . TABLE_PAYMENT_METHODS . " as pm
											WHERE pm.payment_methods_id = '" . (int) $this->payment_methods_id . "'";
            $payment_methods_result_sql = tep_db_query($payment_methods_select_sql);
            $this->_check = tep_db_num_rows($payment_methods_result_sql);
        }
        return $this->_check;
    }

    function load_pm_setting($language_id = 1) {
        $pm_setting_array = array();
        //load value from DB
        $payment_configuration_info_select_sql = "	SELECT pci.payment_configuration_info_key, pcid.payment_configuration_info_value
													FROM " . TABLE_PAYMENT_CONFIGURATION_INFO . " as pci
													LEFT JOIN " . TABLE_PAYMENT_CONFIGURATION_INFO_DESCRIPTION . " as pcid 
														ON pci.payment_configuration_info_id = pcid.payment_configuration_info_id
															AND pcid.languages_id = '" . (int) $language_id . "'
													WHERE pci.payment_methods_id = '" . (int) $this->payment_methods_id . "'
													ORDER BY pci.payment_configuration_info_sort_order";
        $payment_configuration_info_result_sql = tep_db_query($payment_configuration_info_select_sql);
        if (tep_db_num_rows($payment_configuration_info_result_sql)) {
            while ($payment_configuration_info_row = tep_db_fetch_array($payment_configuration_info_result_sql)) {
                $pm_setting_array[$payment_configuration_info_row['payment_configuration_info_key']] = $payment_configuration_info_row['payment_configuration_info_value'];
            }
        } else if ((int) $this->payment_methods_parent_id > 0) { //load value from PG
            $payment_configuration_info_select_sql = "	SELECT pci.payment_configuration_info_key, pcid.payment_configuration_info_value
														FROM " . TABLE_PAYMENT_CONFIGURATION_INFO . " as pci
														LEFT JOIN " . TABLE_PAYMENT_CONFIGURATION_INFO_DESCRIPTION . " as pcid 
															ON pci.payment_configuration_info_id = pcid.payment_configuration_info_id
																AND pcid.languages_id = '" . (int) $language_id . "'
														WHERE pci.payment_methods_id = '" . (int) $this->payment_methods_parent_id . "'
														ORDER BY pci.payment_configuration_info_sort_order";
            $payment_configuration_info_result_sql = tep_db_query($payment_configuration_info_select_sql);
            while ($payment_configuration_info_row = tep_db_fetch_array($payment_configuration_info_result_sql)) {
                $pm_setting_array[$payment_configuration_info_row['payment_configuration_info_key']] = $payment_configuration_info_row['payment_configuration_info_value'];
            }
        }
        return $pm_setting_array;
    }

    function get_merchant_account($selected_currency) {

        $payment_methods_instance_id = 0;
        $payment_gateway_instance_select_sql = "SELECT pmi.payment_methods_instance_id, pmi.payment_methods_instance_follow_default, pmi.currency_code 
				 								FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " as pmi
				 								WHERE pmi.payment_methods_id = '" . (int) $this->payment_methods_id . "'
				 								LIMIT 1";
        $payment_gateway_instance_result_sql = tep_db_query($payment_gateway_instance_select_sql);
        if (tep_db_num_rows($payment_gateway_instance_result_sql)) {
            $payment_gateway_instance_select_sql = "SELECT pmi.payment_methods_instance_id, pmi.payment_methods_instance_follow_default 
					 								FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " as pmi
					 								WHERE pmi.currency_code = '" . tep_db_input($selected_currency) . "' 
					 									AND pmi.payment_methods_id = '" . (int) $this->payment_methods_id . "'";
            $payment_gateway_instance_result_sql = tep_db_query($payment_gateway_instance_select_sql);
            if ($payment_gateway_instance_row = tep_db_fetch_array($payment_gateway_instance_result_sql)) {
                if ((int) $payment_gateway_instance_row['payment_methods_instance_follow_default'] > 0) { // Follow Default Currency Setting
                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_follow_default'];
                } else { // Has own setting
                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_id'];
                }
            }
        } else {
            $payment_gateway_instance_select_sql = "SELECT pmi.payment_methods_instance_id, pmi.payment_methods_instance_follow_default 
					 								FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " as pmi
					 								INNER JOIN " . TABLE_PAYMENT_METHODS . " as pm
					 									ON pm.payment_methods_parent_id = pmi.payment_methods_id
					 								WHERE pmi.currency_code = '" . tep_db_input($selected_currency) . "' 
					 									AND pm.payment_methods_id = '" . (int) $this->payment_methods_id . "'";
            $payment_gateway_instance_result_sql = tep_db_query($payment_gateway_instance_select_sql);
            if ($payment_gateway_instance_row = tep_db_fetch_array($payment_gateway_instance_result_sql)) {
                if ((int) $payment_gateway_instance_row['payment_methods_instance_follow_default'] > 0) {
                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_follow_default'];
                } else {
                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_id'];
                }
            }
        }

        $payment_gateway_instance_setting_select_sql = "SELECT pmis.payment_methods_instance_setting_key, pmis.payment_methods_instance_setting_value 
						 								FROM " . TABLE_PAYMENT_METHODS_INSTANCE_SETTING . " as pmis
						 								WHERE pmis.payment_methods_instance_id = '" . (int) $payment_methods_instance_id . "'";
        $payment_gateway_instance_setting_result_sql = tep_db_query($payment_gateway_instance_setting_select_sql);
        while ($payment_gateway_instance_setting_row = tep_db_fetch_array($payment_gateway_instance_setting_result_sql)) {
            switch ($payment_gateway_instance_setting_row['payment_methods_instance_setting_key']) {
                case 'MODULE_PAYMENT_DINEROMAIL_E_COMERCIO':
                    $this->dineromail_id = $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];
                    break;
                case 'MODULE_PAYMENT_DINEROMAIL_NROCTA':
                    $this->dineromail_acc_id = $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];
                    break;
                case 'MODULE_PAYMENT_DINEROMAIL_CLAVE':
                    $this->dineromail_password = $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];
                    break;
                case 'MODULE_PAYMENT_DINEROMAIL_CHECKOUT_COUNTRY':
                    $this->dineromail_checkout_country = $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];
                    break;
            }
        }

        switch ($this->dineromail_checkout_country) {
            case 'mexico':
                $this->form_action_url = 'https://mexico.dineromail.com/Shop/Shop_Ingreso.asp';
                $this->form_status_url = 'https://mexico.dineromail.com/Vender/Consulta_IPN.asp';
                break;
            case 'brasil':
                $this->form_action_url = 'https://brasil.dineromail.com/dinero-tools/login/Shop/Shop_Ingreso.asp';
                $this->form_status_url = 'https://brasil.dineromail.com/Vender/Consulta_IPN.asp';
                break;
            case 'chile':
                $this->form_action_url = 'https://chile.dineromail.com/Shop/Shop_Ingreso.asp';
                $this->form_status_url = 'https://chile.dineromail.com/Vender/Consulta_IPN.asp';
                break;
            default:  //argentina
                $this->form_action_url = 'https://argentina.dineromail.com/Shop/Shop_Ingreso.asp';
                $this->form_status_url = 'https://argentina.dineromail.com/Vender/Consulta_IPN.asp';
                break;
        }
    }

    function get_pg_id() {
        return $this->payment_methods_id;
    }

    function install() {
        $install_configuration_flag = true;
        if ($this->payment_methods_id > 0) {
            if ($this->receive_status != 1) {
                $update_receive_status_sql_data = array("payment_methods_receive_status" => 1);
                tep_db_perform(TABLE_PAYMENT_METHODS, $update_receive_status_sql_data, 'update', "payment_methods_id = '" . (int) $this->payment_methods_id . "'");
            } else {
                $install_configuration_flag = false;
            }
        } else {

            $install_payment_gateway_data_sql = array('payment_methods_code' => $this->code,
                'payment_methods_send_status' => 0,
                'payment_methods_title' => $this->code,
                'payment_methods_types_id' => 0,
                'payment_methods_parent_id' => 0,
                'payment_methods_legend_color' => '#A68C12',
                'payment_methods_sort_order' => 5,
                'payment_methods_filename' => $this->filename,
                'payment_methods_logo' => '',
                'date_added' => 'now()',
                'last_modified' => 'now()',
                'payment_methods_receive_status' => 1,
                'payment_methods_receive_status_mode' => 0
            );
            tep_db_perform(TABLE_PAYMENT_METHODS, $install_payment_gateway_data_sql);
            $this->payment_methods_id = tep_db_insert_id();
        }
        if ($install_configuration_flag) {
            $install_key_array = array();
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Set Order Status',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_DINEROMAIL_ORDER_STATUS_ID',
                    'payment_configuration_info_description' => 'Set the status of orders made with this payment module to this value.',
                    'payment_configuration_info_sort_order' => '1335',
                    'set_function' => 'tep_cfg_pull_down_order_statuses(',
                    'use_function' => 'tep_get_order_status_name',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => '1',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Set Order\'s "Processing" Status',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_DINEROMAIL_PROCESSING_STATUS_ID',
                    'payment_configuration_info_description' => 'Set the initial processing status of orders made with this payment module to this value.',
                    'payment_configuration_info_sort_order' => '1340',
                    'set_function' => 'tep_cfg_pull_down_order_statuses(',
                    'use_function' => 'tep_get_order_status_name',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => '7',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Payment Message',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_DINEROMAIL_MESSAGE',
                    'payment_configuration_info_description' => 'Payment message will show up during checkout process.',
                    'payment_configuration_info_sort_order' => '1350',
                    'set_function' => 'tep_cfg_textarea(',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'Thank you for shopping at OffGamers.com.',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Payment Type',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_DINEROMAIL_MEDIOSPAGO',
                    'payment_configuration_info_description' => 'Payment type, eg 2 = Cash Methods or Barcodes, 7 = Account Balance / eWallet...',
                    'payment_configuration_info_sort_order' => '1355',
                    'set_function' => '',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => '',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Payment Email Message',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_DINEROMAIL_EMAIL_MESSAGE',
                    'payment_configuration_info_description' => 'This message will show up in order confirmation e-mail.',
                    'payment_configuration_info_sort_order' => '1360',
                    'set_function' => 'tep_cfg_textarea(',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'Thank you for shopping at OffGamers.com.',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Confirm Complete (in days)',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_DINEROMAIL_CONFIRM_COMPLETE',
                    'payment_configuration_info_description' => 'The number of days in which an order made by this payment method is confirm completed.',
                    'payment_configuration_info_sort_order' => '1265',
                    'set_function' => '',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => '0',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Require Address Information',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_DINEROMAIL_MANDATORY_ADDRESS_FIELD',
                    'payment_configuration_info_description' => 'Set address field as required info during checkout.',
                    'payment_configuration_info_sort_order' => '1400',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Customer Payment Info',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_DINEROMAIL_CUSTOMER_PAYMENT_INFO',
                    'payment_configuration_info_description' => 'Set to true if this Payment Method required customer input for Payment Information required customer payment info.',
                    'payment_configuration_info_sort_order' => '1410',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Business Logo',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_DINEROMAIL_IMAGE_URL',
                    'payment_configuration_info_description' => 'If you wish to add your own business logo, you will have to type in all this text, replacing the blue part for the URL that hosts your logo. Be careful: the file extension must be jpg or gif.',
                    'payment_configuration_info_sort_order' => '1430',
                    'set_function' => '',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => '',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Message',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_DINEROMAIL_MENSAJE',
                    'payment_configuration_info_description' => 'Allow buyer to send a message.',
                    'payment_configuration_info_sort_order' => '1440',
                    'set_function' => 'tep_cfg_key_select_option(array(	\'1\'=>\'Allowed\', 
																													\'0\'=>\'Not allowed\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => '1',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Require IC Information',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_DINEROMAIL_MANDATORY_IC_FIELD',
                    'payment_configuration_info_description' => 'Set IC field as required info during checkout.',
                    'payment_configuration_info_sort_order' => '1450',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Require Contact Information',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_DINEROMAIL_MANDATORY_CONTACT_FIELD',
                    'payment_configuration_info_description' => 'Set Contact field as required info during checkout.',
                    'payment_configuration_info_sort_order' => '1460',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );
            foreach ($install_key_array as $data) {
                tep_db_perform(TABLE_PAYMENT_CONFIGURATION_INFO, $data['info']);
                $payment_conf_id = tep_db_insert_id();

                $data['desc']['payment_configuration_info_id'] = (int) $payment_conf_id;
                tep_db_perform(TABLE_PAYMENT_CONFIGURATION_INFO_DESCRIPTION, $data['desc']);
            }
        }
        return 1;
    }

    function configuration_information_keys() {
        return array('MODULE_PAYMENT_DINEROMAIL_ORDER_STATUS_ID',
            'MODULE_PAYMENT_DINEROMAIL_PROCESSING_STATUS_ID',
            'MODULE_PAYMENT_DINEROMAIL_MESSAGE',
            'MODULE_PAYMENT_DINEROMAIL_EMAIL_MESSAGE',
            'MODULE_PAYMENT_DINEROMAIL_CONFIRM_COMPLETE',
            'MODULE_PAYMENT_DINEROMAIL_MEDIOSPAGO',
            'MODULE_PAYMENT_DINEROMAIL_MANDATORY_ADDRESS_FIELD',
            'MODULE_PAYMENT_DINEROMAIL_CUSTOMER_PAYMENT_INFO',
            'MODULE_PAYMENT_DINEROMAIL_CHECKOUT_COUNTRY',
            'MODULE_PAYMENT_DINEROMAIL_IMAGE_URL',
            'MODULE_PAYMENT_DINEROMAIL_MENSAJE',
            'MODULE_PAYMENT_DINEROMAIL_MANDATORY_IC_FIELD',
            'MODULE_PAYMENT_DINEROMAIL_MANDATORY_CONTACT_FIELD'
        );
    }

    function multi_lang_configuration_info_keys() {
        return array('MODULE_PAYMENT_DINEROMAIL_MESSAGE',
            'MODULE_PAYMENT_DINEROMAIL_EMAIL_MESSAGE');
    }

    function merchant_information_keys() {
        include_once(DIR_WS_CLASSES . "payment_methods.php");

        return array(
            'MODULE_PAYMENT_DINEROMAIL_E_COMERCIO' => array("field" => 'text', "mapping" => 'MODULE_PAYMENT_DINEROMAIL_LNG_E_COMERCIO'),
            'MODULE_PAYMENT_DINEROMAIL_NROCTA' => array("field" => 'text', "mapping" => 'MODULE_PAYMENT_DINEROMAIL_LNG_NROCTA'),
            'MODULE_PAYMENT_DINEROMAIL_CLAVE' => array("field" => 'text', "mapping" => 'MODULE_PAYMENT_DINEROMAIL_LNG_CLAVE'),
            'MODULE_PAYMENT_DINEROMAIL_CHECKOUT_COUNTRY' => array("field" => 'select',
                "mapping" => 'MODULE_PAYMENT_DINEROMAIL_LNG_CHECKOUT_COUNTRY',
                "select" => array("argentina" => "Argentina",
                    "brasil" => "Brasil",
                    "chile" => "Chile",
                    "mexico" => "Mexico")
            ),
            'MODULE_PAYMENT_TAX' => array("field" => 'select', 'mapping' => "MODULE_PAYMENT_TAX", "select" => payment_methods::get_tax_option())
        );
    }

    function remove() {
        tep_db_query("DELETE FROM " . TABLE_CONFIGURATION . " WHERE configuration_key IN ('" . implode("', '", $this->keys()) . "')");
    }

    function keys() {
        return array('MODULE_PAYMENT_DINEROMAIL_STATUS',
            'MODULE_PAYMENT_DINEROMAIL_TEST_MODE',
            'MODULE_PAYMENT_DINEROMAIL_PAYEE_CODE',
            'MODULE_PAYMENT_DINEROMAIL_USER_ID',
            'MODULE_PAYMENT_DINEROMAIL_USER_PASSWORD',
            'MODULE_PAYMENT_DINEROMAIL_SORT_ORDER',
            'MODULE_PAYMENT_DINEROMAIL_ORDER_STATUS_ID',
            'MODULE_PAYMENT_DINEROMAIL_PROCESSING_STATUS_ID',
            'MODULE_PAYMENT_DINEROMAIL_MESSAGE',
            'MODULE_PAYMENT_DINEROMAIL_LEGEND_COLOUR',
            'MODULE_PAYMENT_DINEROMAIL_EMAIL_MESSAGE',
            'MODULE_PAYMENT_DINEROMAIL_CONFIRM_COMPLETE');
    }

    function characterData($parser, $result) {
        global $globalresult, $currentTag;
        // echo   "charData(parser,$result)   for   $currentTag <BR> ";

        switch (strtoupper($currentTag)) {
            case "ESTADOREPORTE":
                $globalresult["estadoreporte"] = $result;
                break;
            case "ID":
                $globalresult["id"] = $result;
                break;
            case "MONTO":
                $globalresult["total_amount"] = $result;
                break;
            case "PRECIOUNITARIO":
                $globalresult["amount"] = $result;
                break;
            case "ESTADO":
                $globalresult["status"] = $result;
                break;
            case "MONEDA":
                $globalresult["currency_flag"] = $result;
                break;
            case "EMAIL":
                $globalresult["email"] = $result;
                break;
            case "METODOPAGO":
                $globalresult["payment_method"] = $result;
                break;
            case "MONTONETO":
                $globalresult["net_amount"] = $result;
                break;
            case "TIPONOTIFICACION":
                $globalresult["tipo_notification"] = $result;
                break;

            default:
                break;
        }
    }

    function startElement($parser, $name, $attrs) {
        global $globalresult, $currentTag;
        $currentTag = $name;
    }

    function endElement($parser, $name) {
        global $currentTag;
        $currentTag = "";
    }

    function ParseXML($xmlResult) {
        global $globalresult;
        //array($this, 'format_character_name')
        $xml_parser = xml_parser_create();
        // set callback functions
        xml_set_element_handler($xml_parser, array($this, 'startElement'), array($this, 'endElement'));
        xml_set_character_data_handler($xml_parser, array($this, 'characterData'));

        if (!xml_parse($xml_parser, $xmlResult)) {
            die(sprintf("XML error: %s at line %d", xml_error_string(xml_get_error_code($xml_parser)), xml_get_current_line_number($xml_parser)));
        }
        // clean up
        xml_parser_free($xml_parser);

        return $globalresult;
    }

    function curl_connect($url, $data) {

        require_once(DIR_WS_CLASSES . 'ogm_xml_to_ary.php');

        $ch = curl_init($url);

        $agent = "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.0)";
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_NOPROGRESS, 0);
        curl_setopt($ch, CURLOPT_VERBOSE, 1);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);  //Windows 2003 Compatibility
        curl_setopt($ch, CURLOPT_TIMEOUT, 120);
        if ($this->connect_via_proxy) {
            curl_setopt($ch, CURLOPT_PROXY, 'http://172.30.11.212:3128');
            curl_setopt($ch, CURLOPT_PROXYUSERPWD, 'devbackend:devonly');
        }

        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);  //Windows 2003 Compatibility
        $response = curl_exec($ch);
        curl_close($ch);

        $this->xml_array_obj = new ogm_xml_to_ary($response, 'content');
        $this->xml_array = $this->xml_array_obj->get_ogm_xml_to_ary();

        $globalresult["estadoreporte"] = (isset($this->xml_array['REPORTE']['_c']['ESTADOREPORTE']['_v']) ? $this->xml_array['REPORTE']['_c']['ESTADOREPORTE']['_v'] : '');
        if (isset($this->xml_array['REPORTE']['_c']['DETALLE']['_c']['OPERACIONES']['_c']['OPERACION']['_c']['ID']['_v'])) {
            $globalresult["id"] = (isset($this->xml_array['REPORTE']['_c']['DETALLE']['_c']['OPERACIONES']['_c']['OPERACION']['_c']['ID']['_v']) ? $this->xml_array['REPORTE']['_c']['DETALLE']['_c']['OPERACIONES']['_c']['OPERACION']['_c']['ID']['_v'] : '');
            $globalresult["total_amount"] = (isset($this->xml_array['REPORTE']['_c']['DETALLE']['_c']['OPERACIONES']['_c']['OPERACION']['_c']['MONTO']['_v']) ? $this->xml_array['REPORTE']['_c']['DETALLE']['_c']['OPERACIONES']['_c']['OPERACION']['_c']['MONTO']['_v'] : '');
            $globalresult["amount"] = (isset($this->xml_array['REPORTE']['_c']['DETALLE']['_c']['OPERACIONES']['_c']['OPERACION']['_c']['ITEMS']['_c']['ITEM']['_c']['PRECIOUNITARIO']['_v']) ? $this->xml_array['REPORTE']['_c']['DETALLE']['_c']['OPERACIONES']['_c']['OPERACION']['_c']['ITEMS']['_c']['ITEM']['_c']['PRECIOUNITARIO']['_v'] : '');
            $globalresult["status"] = (isset($this->xml_array['REPORTE']['_c']['DETALLE']['_c']['OPERACIONES']['_c']['OPERACION']['_c']['ESTADO']['_v']) ? $this->xml_array['REPORTE']['_c']['DETALLE']['_c']['OPERACIONES']['_c']['OPERACION']['_c']['ESTADO']['_v'] : '');
            $globalresult["currency_flag"] = (isset($this->xml_array['REPORTE']['_c']['DETALLE']['_c']['OPERACIONES']['_c']['OPERACION']['_c']['ITEMS']['_c']['ITEM']['_c']['MONEDA']['_v']) ? $this->xml_array['REPORTE']['_c']['DETALLE']['_c']['OPERACIONES']['_c']['OPERACION']['_c']['ITEMS']['_c']['ITEM']['_c']['MONEDA']['_v'] : '');
            $globalresult["email"] = (isset($this->xml_array['REPORTE']['_c']['DETALLE']['_c']['OPERACIONES']['_c']['OPERACION']['_c']['COMPRADOR']['_c']['EMAIL']['_v']) ? $this->xml_array['REPORTE']['_c']['DETALLE']['_c']['OPERACIONES']['_c']['OPERACION']['_c']['COMPRADOR']['_c']['EMAIL']['_v'] : '');
            $globalresult["payment_method"] = (isset($this->xml_array['REPORTE']['_c']['DETALLE']['_c']['OPERACIONES']['_c']['OPERACION']['_c']['METODOPAGO']['_v']) ? $this->xml_array['REPORTE']['_c']['DETALLE']['_c']['OPERACIONES']['_c']['OPERACION']['_c']['METODOPAGO']['_v'] : '');
            $globalresult["net_amount"] = (isset($this->xml_array['REPORTE']['_c']['DETALLE']['_c']['OPERACIONES']['_c']['OPERACION']['_c']['MONTONETO']['_v']) ? $this->xml_array['REPORTE']['_c']['DETALLE']['_c']['OPERACIONES']['_c']['OPERACION']['_c']['MONTONETO']['_v'] : '');
        } else if (isset($this->xml_array['REPORTE']['_c']['DETALLE']['_c']['OPERACIONES']['_c']['OPERACION'][0])) {
            $globalresult["id"] = (isset($this->xml_array['REPORTE']['_c']['DETALLE']['_c']['OPERACIONES']['_c']['OPERACION'][0]['_c']['ID']['_v']) ? $this->xml_array['REPORTE']['_c']['DETALLE']['_c']['OPERACIONES']['_c']['OPERACION'][0]['_c']['ID']['_v'] : '');
            $globalresult["total_amount"] = (isset($this->xml_array['REPORTE']['_c']['DETALLE']['_c']['OPERACIONES']['_c']['OPERACION'][0]['_c']['MONTO']['_v']) ? $this->xml_array['REPORTE']['_c']['DETALLE']['_c']['OPERACIONES']['_c']['OPERACION'][0]['_c']['MONTO']['_v'] : '');
            $globalresult["amount"] = (isset($this->xml_array['REPORTE']['_c']['DETALLE']['_c']['OPERACIONES']['_c']['OPERACION'][0]['_c']['ITEMS']['_c']['ITEM']['_c']['PRECIOUNITARIO']['_v']) ? $this->xml_array['REPORTE']['_c']['DETALLE']['_c']['OPERACIONES']['_c']['OPERACION'][0]['_c']['ITEMS']['_c']['ITEM']['_c']['PRECIOUNITARIO']['_v'] : '');
            $globalresult["status"] = (isset($this->xml_array['REPORTE']['_c']['DETALLE']['_c']['OPERACIONES']['_c']['OPERACION'][0]['_c']['ESTADO']['_v']) ? $this->xml_array['REPORTE']['_c']['DETALLE']['_c']['OPERACIONES']['_c']['OPERACION'][0]['_c']['ESTADO']['_v'] : '');
            $globalresult["currency_flag"] = (isset($this->xml_array['REPORTE']['_c']['DETALLE']['_c']['OPERACIONES']['_c']['OPERACION'][0]['_c']['ITEMS']['_c']['ITEM']['_c']['MONEDA']['_v']) ? $this->xml_array['REPORTE']['_c']['DETALLE']['_c']['OPERACIONES']['_c']['OPERACION'][0]['_c']['ITEMS']['_c']['ITEM']['_c']['MONEDA']['_v'] : '');
            $globalresult["email"] = (isset($this->xml_array['REPORTE']['_c']['DETALLE']['_c']['OPERACIONES']['_c']['OPERACION'][0]['_c']['COMPRADOR']['_c']['EMAIL']['_v']) ? $this->xml_array['REPORTE']['_c']['DETALLE']['_c']['OPERACIONES']['_c']['OPERACION'][0]['_c']['COMPRADOR']['_c']['EMAIL']['_v'] : '');
            $globalresult["payment_method"] = (isset($this->xml_array['REPORTE']['_c']['DETALLE']['_c']['OPERACIONES']['_c']['OPERACION'][0]['_c']['METODOPAGO']['_v']) ? $this->xml_array['REPORTE']['_c']['DETALLE']['_c']['OPERACIONES']['_c']['OPERACION'][0]['_c']['METODOPAGO']['_v'] : '');
            $globalresult["net_amount"] = (isset($this->xml_array['REPORTE']['_c']['DETALLE']['_c']['OPERACIONES']['_c']['OPERACION'][0]['_c']['MONTONETO']['_v']) ? $this->xml_array['REPORTE']['_c']['DETALLE']['_c']['OPERACIONES']['_c']['OPERACION'][0]['_c']['MONTONETO']['_v'] : '');
        }

        return $globalresult;
    }

    function get_orders_payment_info_file() {
        return DIR_FS_CATALOG_MODULES . 'payment/dineromail/admin/orders.inc.php';
    }

    function get_orders_list_payment_info_file() {
        return DIR_FS_CATALOG_MODULES . 'payment/dineromail/admin/orders_list.inc.php';
    }

    function get_ipn_class_file() {
        return DIR_FS_CATALOG_MODULES . 'payment/dineromail/classes/dineromail_ipn_class.php';
    }

    function get_dineromail_status($pass_status) {
        $dineromail_payment_status = array('1' => 'PAYMENT PENDING',
            '2' => 'CREDIT',
            '3' => 'CANCELLED');
        if (isset($dineromail_payment_status[$pass_status])) {
            return $dineromail_payment_status[$pass_status];
        } else {
            return $pass_status;
        }
    }

}

?>