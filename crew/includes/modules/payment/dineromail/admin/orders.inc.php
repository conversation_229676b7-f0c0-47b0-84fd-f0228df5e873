﻿<?
/*
  	$Id: orders.inc.php,v 1.1 2009/06/09 08:12:09 boonhock Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2005 SKC Venture
  	
  	Released under the GNU General Public License
*/

$dineromail_trans_info_select_sql = "	SELECT * 
										FROM " . TABLE_DINEROMAIL . "
										WHERE orders_id = '" . (int)$oID  . "'";
$dineromail_trans_info_result_sql = tep_db_query($dineromail_trans_info_select_sql);

$dineromail_trans_history_select_sql = "SELECT * 
										FROM " . TABLE_DINEROMAIL_STATUS_HISTORY . " 
										WHERE orders_id='" . (int)$oID . "'";
$dineromail_trans_history_result_sql= tep_db_query($dineromail_trans_history_select_sql);

include_once(DIR_FS_CATALOG_MODULES . 'payment/dineromail/admin/languages/'.$language.'/dineromail.lng.php');

?>
<table border="0" width="100%" cellspacing="0" cellpadding="2">
<? ob_start(); 

?>
	<tr>
		<td class="main">
      		<table border="0" width="100%" cellspacing="0" cellpadding="2">
      			<tr>
      				<td class="main" width="12%" nowrap><b><?=ENTRY_PAYMENT_METHOD?></b></td>
      				<td class="main">
      					<div>
      						<? if((int)$order->info['payment_methods_id']>0) { ?><div class="<?=((int)$payment_info_array[$order->info['payment_methods_id']]->confirm_complete_days>0?'rp_icon':'nrp_icon')?>"><b><?=((int)$payment_info_array[$order->info['payment_methods_id']]->confirm_complete_days>0?'RP':'NRP')?></b></div> <? } ?>
      						<div style="width:30px; height:15px; background-color:<?=$payment_module_info['display_colour']?>; float:left; vertical-align:top; text-align:center;"></div>
      						<div style="vertical-align: top">
      							&nbsp;&nbsp;
<?
	if ($view_payment_details_permission) {
		if (tep_not_null($last_payment_method) && $last_payment_method['payment_methods_id'] != $order->info['payment_methods_id']) {
			echo '<span class="redIndicator">'.(tep_not_null($payment_module_info['payment_methods_parent_title'])?$payment_module_info['payment_methods_parent_title'] . ' - ' :'') . $payment_module_info['payment_methods_title'].'</span>';
		} else {
			echo (tep_not_null($payment_module_info['payment_methods_parent_title'])?$payment_module_info['payment_methods_parent_title'] . ' - ' :'') . $payment_module_info['payment_methods_title'];
		}
		
		if (tep_not_null($last_payment_method) && $last_payment_method['payment_methods_id'] != $order->info['payment_methods_id']) {
			echo '&nbsp;&nbsp;<span class="redIndicator">(Last Payment Method: ' . (tep_not_null($last_payment_method['parent_payment_methods_title'])? $last_payment_method['parent_payment_methods_title'] . ' - ' : '' ) . $last_payment_method['payment_methods_title'] . ')</span>';
		}
	} else {
		echo (tep_not_null($payment_module_info['payment_methods_parent_title'])?$payment_module_info['payment_methods_parent_title'] . ' - ' :'') . $payment_module_info['payment_methods_title'];
	}
?>
							</div>
      					</div>
      				</td>
				</tr>
			</table>
		</td>
	</tr>
<?
$payment_method_title_info = ob_get_contents();
ob_end_clean();

if (tep_db_num_rows($dineromail_trans_info_result_sql) || tep_db_num_rows($dineromail_trans_history_result_sql)) {
	echo $payment_method_title_info;
	if (!$view_payment_details_permission) {
		;
	} else {
		$dineromail_payment_method_array = array(	'1'=>'DINEROMAIL FUNDS',
													'2'=>'CASH THROUGH PAGOFACIL, RAPIPAGO, COBROEXPRESS O BAPROPAGO',
													'3'=>'CREDIT CARD',
													'4'=>'BANK TRANSFER');
													
		$dineromail_payment_status = array(	'1'=>'PAYMENT PENDING',
											'2'=>'CREDIT',
											'3'=>'CANCELLED');
?>
  	<tr>
    	<td class="main">
      		<table border="0" width="100%" cellspacing="0" cellpadding="2">
      			<tr valign="top">
<?
		$dineromail_trans_found = false;
		if ($dineromail_trans_info_row = tep_db_fetch_array($dineromail_trans_info_result_sql)) {
			$dineromail_trans_found = true;
?>
        			<td width="25%">
              			<table border="0" cellspacing="0" cellpadding="2">
							<tr>
								<td class="main" valign="top" nowrap><b><?=ENTRY_DINEROMAIL_CUSTOMER_EMAIL?></b>&nbsp;</td>
			                	<td class="main" valign="top" nowrap><?=$dineromail_trans_info_row["dineromail_customer_email"]?></td>
							</tr>
							<tr>
								<td class="main" valign="top" nowrap><b><?=ENTRY_DINEROMAIL_NET_AMOUNT?></b>&nbsp;</td>
			                	<td class="main" valign="top" nowrap><?=$dineromail_trans_info_row["dineromail_net_amount"]?></td>
							</tr>
							<tr>
								<td class="main" valign="top" nowrap><b><?=ENTRY_DINEROMAIL_PAYMENT_METHOD?></b>&nbsp;</td>
			                	<td class="main" valign="top" nowrap><?=(isset($dineromail_payment_method_array[$dineromail_trans_info_row["dineromail_payment_method"]])?$dineromail_payment_method_array[$dineromail_trans_info_row["dineromail_payment_method"]]:'Unknown')?></td>
							</tr>
              			</table>
        			</td>
<?
		}
?>
        			<td>
              			<table border="0" cellspacing="0" cellpadding="2">
              				<tr>
                				<td class="main" valign="top" nowrap><b><?=ENTRY_DINEROMAIL_STATUS?></b>&nbsp;</td>
                				<td class="main" nowrap><?=isset($dineromail_payment_status[$dineromail_trans_info_row['dineromail_status']]) ? $dineromail_payment_status[$dineromail_trans_info_row['dineromail_status']] : TEXT_STATUS_UNKNOWN;?></td>
							</tr>
<?
	if (tep_db_num_rows($dineromail_trans_history_result_sql)) {
?>
              				<tr>
                				<td class="main" colspan="2" nowrap>
                					<table border="1" cellspacing="0" cellpadding="2">
        								<tr>
                							<td class="smallText" nowrap><b><?=TABLE_HEADING_DINEROMAIL_DATE?></b></td>
                							<td class="smallText" nowrap><b><?=TABLE_HEADING_DINEROMAIL_STATUS?></b></td>
                							<td class="smallText" nowrap><b><?=TABLE_HEADING_DINEROMAIL_DESCRIPTION?></b></td>
                						</tr>
<?
		while ($dineromail_trans_history_row = tep_db_fetch_array($dineromail_trans_history_result_sql)) {
        	echo ' 						<tr>
                							<td class="smallText" nowrap>'.$dineromail_trans_history_row['dineromail_status_date'].'</td>
                							<td class="smallText" nowrap>'.(isset($dineromail_payment_status[$dineromail_trans_history_row['dineromail_status']])?$dineromail_payment_status[$dineromail_trans_history_row['dineromail_status']]:$dineromail_trans_history_row['dineromail_status']).'</td>
                							<td class="smallText" nowrap>'.$dineromail_trans_history_row['dineromail_status_description'].'</td>
            							</tr>';
     	}
?>
                					</table>
                				</td>
                			</tr>
<?
	}
?>
              			</table>
              		</td>
<?
		if ($dineromail_trans_found) {
?>
        			<td>
        				<table>
							<tr>
								<td class="main" valign="top" nowrap><b><?=ENTRY_DINEROMAIL_ORDER_CURRENCY?></b>&nbsp;</td>
			                	<td class="main" valign="top" nowrap>
<?
			    					if (strtolower($dineromail_trans_info_row["dineromail_currency"]) == strtolower($order->info['currency'])) {
			    						echo $dineromail_trans_info_row['dineromail_currency'];
			    					} else {
			    						echo '<span class="redIndicator">'.$dineromail_trans_info_row["dineromail_currency"].'<br><span class="smallText">Does not match purchase currency.</span></span>';
			    					}
									echo '('.TEXT_INFO_CURRENCY_NOT_FROM_DINEROMAIL.')';			                		
?>
			                	</td>
							</tr>
							<tr>
								<td class="main" valign="top" nowrap><b><?=ENTRY_DINEROMAIL_ORDER_AMOUNT?></b>&nbsp;</td>
			                	<td class="main" valign="top" nowrap><?
			    					$gross_display_text = number_format($dineromail_trans_info_row["dineromail_amount"], $currencies->currencies[$order->info["currency"]]['decimal_places'], $currencies->currencies[$order->info["currency"]]['decimal_point'], $currencies->currencies[$order->info["currency"]]['thousands_point']);
			    					if ($currencies->currencies[$order->info["currency"]]['symbol_left'].$gross_display_text.$currencies->currencies[$order->info["currency"]]['symbol_right'] != strip_tags($order->order_totals['ot_total']['text']) ) {
			    						$gross_display_text = '<span class="redIndicator">'.$gross_display_text.'<br><span class="smallText">Does not match purchase amount.</span></span>';
			    					}
			    					echo $gross_display_text;
			    				?>
							</tr>
        				</table>
        			</td>
<?
	}
?>
      			</tr>
      		</table>
   		</td>
  	</tr>
<?
	}
} else {
	echo $payment_method_title_info;
}
?>
  	<tr>
  		<td>
<?
	if ($order->info['orders_status'] == '1' || $order->info['orders_status'] == '7' || $order->info['orders_status'] == '2') {
		echo "&nbsp;&nbsp;";
		echo tep_draw_form('dineromail_status_check_form', FILENAME_ORDERS, tep_get_all_get_params(), 'post');
		echo tep_draw_hidden_field('subaction', 'payment_action');
		echo tep_draw_hidden_field('payment_action', 'check_trans_status');
		echo tep_submit_button('Check Payment Status', 'Check Payment Status', 'name="dineromailCheckTransStatusBtn"', 'inputButton');
		echo "</form>";
	}
?>
 		</td>
  	</tr>
</table>