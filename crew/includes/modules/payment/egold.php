<?php

/*
  $Id: e-gold.php,v 1 2003/07/23

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2003 osCommerce

  Released under the GNU General Public License

  e-Gold module Crated by <PERSON>
  modified 2004/01/18 for osCommerce-2.2MS2 by rayservers.com

  Note: PAYMENT_METAL_ID
  - Buyer's Choice = 0
  - Gold = 1
  - Silver = 2
  - Platinum = 3
  - Palladium = 4

  PAYMENT_BATCH_NUM	-  32-bit nonzero positive integer

 */

class egold {

    var $code, $title, $display_title, $description, $sort_order, $enabled, $form_action_url;
    var $order_status, $order_processing_status;
    var $egCurrencies, $aCurrencies, $defCurr;
    var $eg_account_no, $eg_account_name, $eg_alt_passphrase, $valid_ip_template = '63.240.230.';
    var $payment_methods_id, $filename;

    // class constructor
    function egold($pm_id = '') {
        global $currencies, $order, $languages_id, $default_languages_id, $defCurr;

        //Basic info
        $this->payment_methods_id = 0;
        $this->payment_methods_parent_id = 0;

        $this->code = 'egold';
        $this->filename = 'egold.php';

        $this->force_to_checkoutprocess = true;
        $this->has_ipn = true;
        $this->auto_cancel_period = 60; // In minutes

        $payment_methods_select_sql = "	SELECT payment_methods_parent_id, payment_methods_code, 
												payment_methods_types_id, 
												payment_methods_receive_status_mode, payment_methods_id, 
												payment_methods_title, payment_methods_sort_order, 
												payment_methods_receive_status, payment_methods_legend_color, 
												payment_methods_logo 
										FROM " . TABLE_PAYMENT_METHODS . " 
										WHERE 1 ";
        if ((int) $pm_id > 0) {
            $payment_methods_select_sql .= "	AND payment_methods_id = '" . (int) $pm_id . "'";
        } else {
            $payment_methods_select_sql .= "	AND payment_methods_filename = '" . tep_db_input($this->filename) . "'";
        }

        $payment_methods_result_sql = tep_db_query($payment_methods_select_sql);

        if ($payment_methods_row = tep_db_fetch_array($payment_methods_result_sql)) {
            $pm_desc_title_select_sql = "	SELECT payment_methods_description_title 
											FROM " . TABLE_PAYMENT_METHODS_DESCRIPTION . " 
											WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "' 
												AND (IF (languages_id = '" . tep_db_input($languages_id) . "', 1, IF ((	SELECT COUNT(payment_methods_id) > 0 
																														FROM " . TABLE_PAYMENT_METHODS_DESCRIPTION . " 
																														WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "' 
																															AND languages_id = '" . (int) $languages_id . "'), 0, languages_id = 1)))";
            $pm_desc_title_result_sql = tep_db_query($pm_desc_title_select_sql);
            $pm_desc_title_row = tep_db_fetch_array($pm_desc_title_result_sql);

            $this->payment_methods_id = (int) $payment_methods_row['payment_methods_id'];
            $this->payment_methods_parent_id = (int) $payment_methods_row['payment_methods_parent_id'];
            $this->code = $payment_methods_row['payment_methods_code'];
            $this->title = $payment_methods_row['payment_methods_title']; //MODULE_PAYMENT_PAYPAL_TEXT_TITLE;
            $this->display_title = $pm_desc_title_row['payment_methods_description_title']; //MODULE_PAYMENT_PAYPAL_TEXT_DISPLAY_TITLE; 	// if title to be display for payment method is different from payment method value
            $this->sort_order = $payment_methods_row['payment_methods_sort_order']; //MODULE_PAYMENT_PAYPAL_SORT_ORDER;
            $this->enabled = $payment_methods_row['payment_methods_receive_status_mode'];
            $this->legend_display_colour = $payment_methods_row['payment_methods_legend_color'];
            $this->receive_status = $payment_methods_row['payment_methods_receive_status'];
            $this->preferred = true;
            $this->description = $this->display_title;
            $this->egCurrencies = $this->get_support_currencies($this->payment_methods_id);
            $this->defCurr = DEFAULT_CURRENCY;

            // take the default currency to be used at e-gold
            $this->defCurr = DEFAULT_CURRENCY;

            $configuration_setting_array = $this->load_pm_setting();
            $this->currency = (isset($configuration_setting_array['MODULE_PAYMENT_EGOLD_CURRENCY']) ? $configuration_setting_array['MODULE_PAYMENT_EGOLD_CURRENCY'] : '');
            //$this->zone = $configuration_setting_array['MODULE_PAYMENT_EGOLD_ZONE'];
            $this->order_status_id = $configuration_setting_array['MODULE_PAYMENT_EGOLD_ORDER_STATUS_ID'];
            $this->processing_status_id = $configuration_setting_array['MODULE_PAYMENT_EGOLD_PROCESSING_STATUS_ID'];
            $this->message = $configuration_setting_array['MODULE_PAYMENT_EGOLD_MESSAGE'];
            $this->email_message = $configuration_setting_array['MODULE_PAYMENT_EGOLD_EMAIL_MESSAGE'];
            $this->confirm_complete_days = $configuration_setting_array['MODULE_PAYMENT_EGOLD_CONFIRM_COMPLETE'];
            $this->require_address_information = $configuration_setting_array['MODULE_PAYMENT_EGOLD_MANDATORY_ADDRESS_FIELD'];
            $this->require_ic_information = $configuration_setting_array['MODULE_PAYMENT_EGOLD_MANDATORY_IC_FIELD'];
            $this->require_contact_information = $configuration_setting_array['MODULE_PAYMENT_EGOLD_MANDATORY_CONTACT_FIELD'];

            $this->customer_payment_info = $configuration_setting_array['MODULE_PAYMENT_EGOLD_CUSTOMER_PAYMENT_INFO'];

            if ((int) $this->order_status_id > 0) {
                $this->order_status = $this->order_status_id;
            } else {
                $this->order_status = DEFAULT_ORDERS_STATUS_ID;
            }
            $this->order_processing_status = $this->processing_status_id;
            $this->form_action_url = 'https://www.e-gold.com/sci_asp/payments.asp';
            $this->email_footer = str_replace(array("<br>", "<BR>", "\r\n"), array("\n", "\n", ''), $this->email_message);
        }

        $currency_select_sql = "SELECT code FROM " . TABLE_CURRENCIES;
        $currency_result_sql = tep_db_query($currency_select_sql);
        while ($currency_row = tep_db_fetch_array($currency_result_sql)) {
            $this->aCurrencies[] = $currency_row['code'];
        }
    }

    // class methods
    function javascript_validation() {
        return false;
    }

    function selection() {
        global $languages_id, $default_languages_id;

        $selection_array = array();

        $payment_methods_select_sql = "	SELECT payment_methods_id, payment_methods_code, payment_methods_types_id, payment_methods_logo, payment_methods_receive_status_mode 
										FROM " . TABLE_PAYMENT_METHODS . " 
										WHERE payment_methods_receive_status = 1 
											AND payment_methods_receive_status_mode <> 0 
											AND payment_methods_parent_id = '" . (int) $this->payment_methods_id . "' 
										ORDER BY payment_methods_sort_order";
        $payment_methods_result_sql = tep_db_query($payment_methods_select_sql);
        while ($payment_methods_row = tep_db_fetch_array($payment_methods_result_sql)) {
            $pm_desc_title_select_sql = "	SELECT payment_methods_description_title 
											FROM " . TABLE_PAYMENT_METHODS_DESCRIPTION . " 
											WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "' 
												AND (IF (languages_id = '" . tep_db_input($languages_id) . "', 1, IF ((	SELECT COUNT(payment_methods_id) > 0 
																														FROM " . TABLE_PAYMENT_METHODS_DESCRIPTION . " 
																														WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "' 
																															AND languages_id = '" . (int) $languages_id . "'), 0, languages_id = 1)))";
            $pm_desc_title_result_sql = tep_db_query($pm_desc_title_select_sql);
            $pm_desc_title_row = tep_db_fetch_array($pm_desc_title_result_sql);

            $pm_array = $this->load_pm_setting(1, $payment_methods_row['payment_methods_id']);

            $selection_array[] = array('payment_gateway_code' => $this->code,
                'payment_methods_receive_status_mode' => $payment_methods_row['payment_methods_receive_status_mode'],
                'payment_methods_id' => $payment_methods_row['payment_methods_id'],
                'payment_methods_code' => $payment_methods_row['payment_methods_code'],
                'payment_methods_types_id' => $payment_methods_row['payment_methods_types_id'],
                'payment_methods_parent_id' => $this->payment_methods_id,
                'payment_methods_logo' => $payment_methods_row['payment_methods_logo'],
                'payment_methods_description_title' => $pm_desc_title_row['payment_methods_description_title'],
                'currency' => $this->get_support_currencies($payment_methods_row['payment_methods_id']),
                'show_billing_address' => $pm_array['MODULE_PAYMENT_EGOLD_MANDATORY_ADDRESS_FIELD'],
                'confirm_complete_days' => $pm_array['MODULE_PAYMENT_EGOLD_CONFIRM_COMPLETE'],
                'show_contact_number' => $pm_array['MODULE_PAYMENT_EGOLD_MANDATORY_CONTACT_FIELD'],
                'show_ic' => $pm_array['MODULE_PAYMENT_EGOLD_MANDATORY_IC_FIELD']
            );

            if ($payment_methods_row['payment_methods_receive_status_mode'] == '-1') {
                $pm_status_message_select = "	SELECT payment_methods_status_message 
												FROM " . TABLE_PAYMENT_METHODS_STATUS_DESCRIPTION . " 
												WHERE payment_methods_mode = 'RECEIVE' 
													AND payment_methods_status = '-1' 
													AND languages_id = '" . (int) $languages_id . "' 
													AND payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "'";
                $pm_status_message_result = tep_db_query($pm_status_message_select);
                $pm_status_message_row = tep_db_fetch_array($pm_status_message_result);
                $selection_array[sizeof($selection_array) - 1]['payment_methods_status_message'] = $pm_status_message_row['payment_methods_status_message'];
            }
        }

        return $selection_array;
    }

    function set_support_currencies($currency_array) {
        $this->egCurrencies = $currency_array;
    }

    function get_support_currencies($pm_id, $by_zone = true) {
        global $zone_info_array;

        $default_currency = '';
        $support_currencies_array = array();

        $currency_code_select_sql = "	SELECT currency_code, payment_methods_instance_default 
    									FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " 
    									WHERE payment_methods_id = '" . (int) $pm_id . "' 
    									ORDER BY currency_code";
        $currency_code_result_sql = tep_db_query($currency_code_select_sql);

        if (tep_db_num_rows($currency_code_result_sql) > 0) {
            while ($currency_code_row = tep_db_fetch_array($currency_code_result_sql)) {
                $support_currencies_array[] = $currency_code_row['currency_code'];

                if ($currency_code_row['payment_methods_instance_default'] == 1) {
                    $default_currency = $currency_code_row['currency_code'];
                }
            }
        } else {
            if ($this->payment_methods_parent_id > 0) {
                $payment_methods_parent_id = $this->payment_methods_parent_id;
            } else {
                $payment_methods_parent_id = $this->payment_methods_id;
            }

            $parent_currency_code_select_sql = "	SELECT currency_code, payment_methods_instance_default 
			    									FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " 
			    									WHERE payment_methods_id = '" . (int) $payment_methods_parent_id . "' 
			    									ORDER BY currency_code";
            $parent_currency_code_result_sql = tep_db_query($parent_currency_code_select_sql);
            while ($parent_currency_code_row = tep_db_fetch_array($parent_currency_code_result_sql)) {
                $support_currencies_array[] = $parent_currency_code_row['currency_code'];

                if ($parent_currency_code_row['payment_methods_instance_default'] == 1) {
                    $default_currency = $parent_currency_code_row['currency_code'];
                }
            }
        }


        if (tep_not_null($default_currency)) {
            $this->defCurr = $default_currency;
        }

        if ($by_zone) {
            if (isset($zone_info_array)) {
                $support_currencies_array = array_intersect($zone_info_array[3]->zone_currency_id, $support_currencies_array);
                if (count($support_currencies_array)) {
                    array_multisort($support_currencies_array);
                } else {
                    $support_currencies_array = array($default_currency);
                }
            } else {
                $support_currencies_array = array($default_currency);
            }
        } else {
            $this->set_support_currencies($support_currencies_array);
            // All found currencies is supported. Normally this is used for backend script such as IPN call
        }

        return $support_currencies_array;
    }

    function get_require_address_information() {
        return $this->require_address_information;
    }

    function get_confirm_complete_days() {
        return $this->confirm_complete_days;
    }

    function get_pm_info() {
        return $this->message;
    }

    function get_email_pm_info() {
        return $this->email_message;
    }

    function draw_confirm_button() {
        return tep_image_button2('red', 'javascript:document.checkout_confirmation.submit()', MODULE_PAYMENT_EGOLD_TEXT_CART, 200);
    }

    function get_confirm_button_caption() {
        return MODULE_PAYMENT_EGOLD_TEXT_TITLE;
    }

    function pre_confirmation_check() {
        global $currency;

        if (in_array($currency, $this->egCurrencies)) {
            
        }

        return false;
    }

    function process_button() {
        global $HTTP_POST_VARS, $shipping_cost, $total_cost, $shipping_selected, $shipping_method, $currencies, $currency, $customer_id, $order, $languages_id;
        global $order_logged, $OFFGAMERS;

        if ($this->force_to_checkoutprocess && !tep_session_is_registered('order_logged'))
            return;

        $rate = $this->update_currency($this->defCurr); // This function also set current exchange rate in AUG per DEFAULT CURRENCY

        if (tep_not_null($rate)) {
            $currencies->currencies['AUG']['value'] = $rate;
        }

        // if the user currency is not supported by e-gold
        // fall back to the default currency set on the admin interface of the module
        $egCurrency = $currency;
        $this->get_merchant_account($egCurrency);

        if (!in_array($currency, $this->egCurrencies)) {
            $egCurrency = $this->eg_currency;
        }

        // This query ALWAYS find the result
        $payment_unit_select_sql = "SELECT egold_currencies_payment_unit 
									FROM " . TABLE_PAYMENT_EGOLD_CURRENCIES . " 
									WHERE egold_currencies_code = '" . $egCurrency . "'";
        $payment_unit_result_sql = tep_db_query($payment_unit_select_sql);
        $payment_unit_row = tep_db_fetch_array($payment_unit_result_sql);

        $process_button_string = tep_draw_hidden_field('PAYEE_ACCOUNT', $this->eg_account_no) .
                tep_draw_hidden_field('PAYEE_NAME', $this->eg_account_name) .
                tep_draw_hidden_field('PAYMENT_AMOUNT', number_format(($order->info['total']) * $currencies->currencies[$egCurrency]['value'], $currencies->currencies[$egCurrency]['decimal_places'], '.', '')) .
                tep_draw_hidden_field('PAYMENT_UNITS', $payment_unit_row['egold_currencies_payment_unit']) .
                tep_draw_hidden_field('PAYMENT_METAL_ID', '1') .
                tep_draw_hidden_field('PAYMENT_ID', $order_logged) .
                tep_draw_hidden_field('STATUS_URL', tep_href_link(FILENAME_EGOLD_IPN, '', 'SSL', false)) .
                tep_draw_hidden_field('PAYMENT_URL', tep_href_link(FILENAME_CHECKOUT_PROCESS, '', 'SSL')) .
                tep_draw_hidden_field('PAYMENT_URL_METHOD', 'POST') .
                tep_draw_hidden_field('NOPAYMENT_URL', tep_href_link(FILENAME_CHECKOUT_PAYMENT, 'payment_error=' . $this->code . '&error=' . urlencode(MODULE_PAYMENT_EGOLD_TEXT_ERROR_MESSAGE), 'SSL')) .
                tep_draw_hidden_field('NOPAYMENT_URL_METHOD', 'LINK') .
                tep_draw_hidden_field('BAGGAGE_FIELDS', 'MC_ORDER_ID') .
                tep_draw_hidden_field('MC_ORDER_ID', $order_logged) .
                tep_draw_hidden_field('SUGGESTED_MEMO', 'Purchase from ' . STORE_NAME);

        return $process_button_string;
    }

    function before_process() {
        // Buyer Normal Return Form
        global $order_logged, $HTTP_POST_VARS, $currencies, $currency, $order;

        if (!tep_session_is_registered('order_logged'))
            return; // This line does not mean there is payment error. It means the first time order been created

        $proceed = true;

        $egCurrency = $currency;
        if (!in_array($currency, $this->egCurrencies)) {
            $egCurrency = $this->eg_currency;
        }

        get_merchant_account($egCurrency);

        // This query ALWAYS find the result
        $payment_unit_select_sql = "SELECT egold_currencies_payment_unit 
									FROM " . TABLE_PAYMENT_EGOLD_CURRENCIES . " 
									WHERE egold_currencies_code = '" . $egCurrency . "'";
        $payment_unit_result_sql = tep_db_query($payment_unit_select_sql);
        $payment_unit_row = tep_db_fetch_array($payment_unit_result_sql);

        $calculated_payment_amt = number_format(($order->info['total']) * $currencies->currencies[$egCurrency]['value'], $currencies->currencies[$egCurrency]['decimal_places']);

        if (trim($HTTP_POST_VARS['PAYEE_ACCOUNT']) != $this->eg_account_no ||
                (double) $HTTP_POST_VARS['PAYMENT_AMOUNT'] != $calculated_payment_amt ||
                trim($HTTP_POST_VARS['PAYMENT_UNITS']) != trim($payment_unit_row['egold_currencies_payment_unit']) ||
                trim($HTTP_POST_VARS['PAYMENT_METAL_ID']) != '1' ||
                (int) $HTTP_POST_VARS['PAYMENT_BATCH_NUM'] <= 0 ||
                trim($HTTP_POST_VARS['PAYMENT_ID']) != $order_logged
        ) {
            $proceed = false;
        }

        if (!$proceed) {
            tep_redirect(tep_href_link(FILENAME_CHECKOUT_PAYMENT, 'error_message=' . urlencode(MODULE_PAYMENT_EGOLD_TEXT_ERROR_MESSAGE), 'SSL'));
        }
    }

    function after_process() {
        return false;
    }

    function link_to_payment($new_order_id) {
        global $order_logged;

        if (tep_session_is_registered('order_logged'))
            tep_session_unregister('order_logged');

        tep_session_register('order_logged');
        $order_logged = $new_order_id;
        require(DIR_WS_INCLUDES . 'modules/payment/egold/catalog/egold_splash.php');
        return;
    }

    function is_supported_currency($selected_currency) {
        return (in_array($selected_currency, $this->egCurrencies) ? true : false);
    }

    function get_error() {
        $error = array('title' => MODULE_PAYMENT_EGOLD_TEXT_ERROR,
            'error' => stripslashes(urldecode($_GET['error'])));
        return $error;
    }

    function output_error() {
        return false;
    }

    function check() {
        if (!isset($this->_check)) {
            $payment_methods_select_sql = "	SELECT payment_methods_id
											FROM " . TABLE_PAYMENT_METHODS . " as pm
											WHERE pm.payment_methods_id = '" . (int) $this->payment_methods_id . "'";
            $payment_methods_result_sql = tep_db_query($payment_methods_select_sql);
            $this->_check = tep_db_num_rows($payment_methods_result_sql);
        }
        return $this->_check;
    }

    function update_currency($base_currency) {
        // fetch the current value of gold
        unset($result);

        $grams_per_ounce = 31.1034768; //constant
        $url = 'https://www.e-gold.com/unsecure/metaldata.asp?LATEST=1&CUR=' . $base_currency . '&GOLD=1'; // you can use http as well, use deffCurr rather thab hard code USD in case our base currency is not USD

        $agent = "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.0)";
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_VERBOSE, 1);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 2);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_USERAGENT, $agent);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);  //Windows 2003 Compatibility
        $result = curl_exec($ch);
        curl_close($ch);

        if (tep_not_null($result)) {
            $result_array = explode(',', urldecode($result));
            $result = trim($result_array[1]);
            $rate = $grams_per_ounce / $result;
        }
        //set current exchange rate in AUG per USD
        if (tep_not_null($rate)) {
            $currency_update_sql = "UPDATE " . TABLE_CURRENCIES . " 
      								SET value = '" . $rate . "', 
      									last_updated = now() 
      								WHERE code = 'AUG'";
            tep_db_query($currency_update_sql);
        }

        return $rate;
    }

    function is_processed_order($order_id) {
        $payment_status_select_sql = "	SELECT egold_transaction_status 
										FROM " . TABLE_PAYMENT_EGOLD . " 
										WHERE orders_id = '" . tep_db_input($order_id) . "'";
        $payment_status_result_sql = tep_db_query($payment_status_select_sql);
        $payment_status_row = tep_db_fetch_array($payment_status_result_sql);

        return $payment_status_row['egold_transaction_status'] == 1 ? true : false;
    }

    function load_pm_setting($language_id = 1, $pm_id = '') {
        $pm_setting_array = array();
        //load value from DB

        if (tep_not_null($pm_id)) {
            $payment_method_id = $pm_id;

            $payment_methods_parent_id_select_sql = "	SELECT payment_methods_parent_id 
														FROM " . TABLE_PAYMENT_METHODS . " 
														WHERE payment_methods_id = '" . (int) $pm_id . "'";
            $payment_methods_parent_id_result_sql = tep_db_query($payment_methods_parent_id_select_sql);
            $payment_methods_parent_id_row = tep_db_fetch_array($payment_methods_parent_id_result_sql);

            $payment_gateway_id = $payment_methods_parent_id_row['payment_methods_parent_id'];
        } else {
            $payment_method_id = $this->payment_methods_id;
            $payment_gateway_id = $this->payment_methods_parent_id;
        }

        $payment_configuration_info_select_sql = "	SELECT pci.payment_configuration_info_key, pcid.payment_configuration_info_value
													FROM " . TABLE_PAYMENT_CONFIGURATION_INFO . " as pci
													LEFT JOIN " . TABLE_PAYMENT_CONFIGURATION_INFO_DESCRIPTION . " as pcid 
														ON pci.payment_configuration_info_id = pcid.payment_configuration_info_id
															AND pcid.languages_id = '" . (int) $language_id . "'
													WHERE pci.payment_methods_id = '" . (int) $payment_method_id . "'
													ORDER BY pci.payment_configuration_info_sort_order";
        $payment_configuration_info_result_sql = tep_db_query($payment_configuration_info_select_sql);
        if (tep_db_num_rows($payment_configuration_info_result_sql)) {
            while ($payment_configuration_info_row = tep_db_fetch_array($payment_configuration_info_result_sql)) {
                $pm_setting_array[$payment_configuration_info_row['payment_configuration_info_key']] = $payment_configuration_info_row['payment_configuration_info_value'];
            }
        } else if ((int) $payment_gateway_id > 0) { //load value from PG
            $payment_configuration_info_select_sql = "	SELECT pci.payment_configuration_info_key, pcid.payment_configuration_info_value
														FROM " . TABLE_PAYMENT_CONFIGURATION_INFO . " as pci
														LEFT JOIN " . TABLE_PAYMENT_CONFIGURATION_INFO_DESCRIPTION . " as pcid 
															ON pci.payment_configuration_info_id = pcid.payment_configuration_info_id
																AND pcid.languages_id = '" . (int) $language_id . "'
														WHERE pci.payment_methods_id = '" . (int) $payment_gateway_id . "'
														ORDER BY pci.payment_configuration_info_sort_order";
            $payment_configuration_info_result_sql = tep_db_query($payment_configuration_info_select_sql);
            while ($payment_configuration_info_row = tep_db_fetch_array($payment_configuration_info_result_sql)) {
                $pm_setting_array[$payment_configuration_info_row['payment_configuration_info_key']] = $payment_configuration_info_row['payment_configuration_info_value'];
            }
        }

        return $pm_setting_array;
    }

    function get_merchant_account($selected_currency) {
        $payment_methods_instance_id = 0;
        $payment_gateway_instance_select_sql = "SELECT pmi.payment_methods_instance_id, pmi.payment_methods_instance_follow_default, pmi.currency_code 
				 								FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " as pmi
				 								WHERE pmi.payment_methods_id = '" . (int) $this->payment_methods_id . "'
				 								LIMIT 1";
        $payment_gateway_instance_result_sql = tep_db_query($payment_gateway_instance_select_sql);
        if (tep_db_num_rows($payment_gateway_instance_result_sql)) {
            $payment_gateway_instance_select_sql = "SELECT pmi.payment_methods_instance_id, pmi.payment_methods_instance_follow_default 
					 								FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " as pmi
					 								WHERE pmi.currency_code = '" . tep_db_input($selected_currency) . "' 
					 									AND pmi.payment_methods_id = '" . (int) $this->payment_methods_id . "'";
            $payment_gateway_instance_result_sql = tep_db_query($payment_gateway_instance_select_sql);
            if ($payment_gateway_instance_row = tep_db_fetch_array($payment_gateway_instance_result_sql)) {
                if ((int) $payment_gateway_instance_row['payment_methods_instance_follow_default'] > 0) { // Follow Default Currency Setting
                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_follow_default'];
                } else { // Has own setting
                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_id'];
                }
            }
        } else {
            $payment_gateway_instance_select_sql = "SELECT pmi.payment_methods_instance_id, pmi.payment_methods_instance_follow_default 
					 								FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " as pmi
					 								INNER JOIN " . TABLE_PAYMENT_METHODS . " as pm
					 									ON pm.payment_methods_parent_id = pmi.payment_methods_id
					 								WHERE pmi.currency_code = '" . tep_db_input($selected_currency) . "' 
					 									AND pm.payment_methods_id = '" . (int) $this->payment_methods_id . "'";
            $payment_gateway_instance_result_sql = tep_db_query($payment_gateway_instance_select_sql);
            if ($payment_gateway_instance_row = tep_db_fetch_array($payment_gateway_instance_result_sql)) {
                if ((int) $payment_gateway_instance_row['payment_methods_instance_follow_default'] > 0) {
                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_follow_default'];
                } else {
                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_id'];
                }
            }
        }
        $payment_gateway_instance_setting_select_sql = "SELECT pmis.payment_methods_instance_setting_key, pmis.payment_methods_instance_setting_value 
						 								FROM " . TABLE_PAYMENT_METHODS_INSTANCE_SETTING . " as pmis
						 								WHERE pmis.payment_methods_instance_id = '" . (int) $payment_methods_instance_id . "'";
        $payment_gateway_instance_setting_result_sql = tep_db_query($payment_gateway_instance_setting_select_sql);
        while ($payment_gateway_instance_setting_row = tep_db_fetch_array($payment_gateway_instance_setting_result_sql)) {
            switch ($payment_gateway_instance_setting_row['payment_methods_instance_setting_key']) {
                case 'MODULE_PAYMENT_EGOLD_ACCOUNT':
                    $this->eg_account_no = $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];
                    break;
                case 'MODULE_PAYMENT_EGOLD_NAME':
                    $this->eg_account_name = $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];
                    break;
                case 'MODULE_PAYMENT_EGOLD_SECRET_WORD':
                    $this->eg_alt_passphrase = $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];
                    break;
            }
        }
    }

    function install() {
        $install_configuration_flag = true;

        if ($this->payment_methods_id > 0) {
            if ($this->receive_status != 1) {
                $update_receive_status_sql_data = array("payment_methods_receive_status" => 1);
                tep_db_perform(TABLE_PAYMENT_METHODS, $update_receive_status_sql_data, 'update', "payment_methods_id = '" . (int) $this->payment_methods_id . "'");
            } else {
                $install_configuration_flag = false;
            }
        } else {
            $install_payment_gateway_data_sql = array('payment_methods_code' => $this->code,
                'payment_methods_send_status' => 0,
                'payment_methods_title' => $this->code,
                'payment_methods_types_id' => 0,
                'payment_methods_parent_id' => 0,
                'payment_methods_legend_color' => '#00AEC5',
                'payment_methods_sort_order' => 20,
                'payment_methods_filename' => $this->filename,
                'payment_methods_logo' => '',
                'date_added' => 'now()',
                'last_modified' => 'now()',
                'payment_methods_receive_status' => 1,
                'payment_methods_receive_status_mode' => 0
            );
            tep_db_perform(TABLE_PAYMENT_METHODS, $install_payment_gateway_data_sql);
            $this->payment_methods_id = tep_db_insert_id();
        }

        if ($install_configuration_flag) {
            $install_key_array = array();
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Transaction Currency',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_EGOLD_CURRENCY',
                    'payment_configuration_info_description' => 'Select the default currency for the payment transactions. If the user selected currency is not available at e-gold, this currency will be the payment currency.',
                    'payment_configuration_info_sort_order' => '20',
                    'set_function' => 'tep_cfg_select_option(array(\'USD\',\'AUD\',\'CAD\',\'CHF\',\'EUR\',\'GBP\',\'JPY\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => strtoupper(DEFAULT_CURRENCY),
                    'languages_id' => 1
                )
            );
            /* 			$install_key_array[] = array(	'info' => array (	'payment_methods_id'=>(int)$this->payment_methods_id,
              'payment_configuration_info_title'=>'Payment Zone',
              'payment_configuration_info_key'=>'MODULE_PAYMENT_EGOLD_ZONE',
              'payment_configuration_info_description'=>'If a zone is selected, only enable this payment method for that zone.',
              'payment_configuration_info_sort_order'=>'25',
              'set_function'=>'tep_cfg_pull_down_zone_classes(',
              'use_function'=>'tep_get_zone_class_title',
              'date_added'=>'now()'
              ),
              'desc' => array (	'payment_configuration_info_value'=>'0',
              'languages_id' => 1
              )
              ); */
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Set Order Status',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_EGOLD_ORDER_STATUS_ID',
                    'payment_configuration_info_description' => 'Set the status of orders made with this payment module to this value',
                    'payment_configuration_info_sort_order' => '30',
                    'set_function' => 'tep_cfg_pull_down_order_statuses(',
                    'use_function' => 'tep_get_order_status_name',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => '1',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Set Order\'s "Processing" Status',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_EGOLD_PROCESSING_STATUS_ID',
                    'payment_configuration_info_description' => 'Set the initial processing status of orders made with this payment module to this value',
                    'payment_configuration_info_sort_order' => '35',
                    'set_function' => 'tep_cfg_pull_down_order_statuses(',
                    'use_function' => 'tep_get_order_status_name',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => '2',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Payment Message',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_EGOLD_MESSAGE',
                    'payment_configuration_info_description' => 'Payment message will show up during checkout process (e-gold)',
                    'payment_configuration_info_sort_order' => '40',
                    'set_function' => 'tep_cfg_textarea(',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'Thank you for shopping at OffGamers.com.',
                    'languages_id' => 1
                )
            );

            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Payment Email Message',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_EGOLD_EMAIL_MESSAGE',
                    'payment_configuration_info_description' => 'This message will show up in order confirmation e-mail.',
                    'payment_configuration_info_sort_order' => '45',
                    'set_function' => 'tep_cfg_textarea(',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'Thank you for shopping at OffGamers.com.',
                    'languages_id' => 1
                )
            );

            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Confirm Complete (in days)',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_EGOLD_CONFIRM_COMPLETE',
                    'payment_configuration_info_description' => 'The number of days in which an order made by this payment method is confirm completed',
                    'payment_configuration_info_sort_order' => '50',
                    'set_function' => 'NULL',
                    'use_function' => 'NULL',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => '0',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Require Address Information',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_EGOLD_MANDATORY_ADDRESS_FIELD',
                    'payment_configuration_info_description' => 'Set address field as required info during checkout.',
                    'payment_configuration_info_sort_order' => '60',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Customer Payment Info',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_EGOLD_CUSTOMER_PAYMENT_INFO',
                    'payment_configuration_info_description' => 'Set to true if this Payment Method required customer input for Payment Information required customer payment info.',
                    'payment_configuration_info_sort_order' => '70',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Require IC Information',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_EGOLD_MANDATORY_IC_FIELD',
                    'payment_configuration_info_description' => 'Set IC field as required info during checkout.',
                    'payment_configuration_info_sort_order' => '80',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Require Contact Information',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_EGOLD_MANDATORY_CONTACT_FIELD',
                    'payment_configuration_info_description' => 'Set Contact field as required info during checkout.',
                    'payment_configuration_info_sort_order' => '90',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );
            foreach ($install_key_array as $data) {
                tep_db_perform(TABLE_PAYMENT_CONFIGURATION_INFO, $data['info']);
                $payment_conf_id = tep_db_insert_id();

                $data['desc']['payment_configuration_info_id'] = (int) $payment_conf_id;
                tep_db_perform(TABLE_PAYMENT_CONFIGURATION_INFO_DESCRIPTION, $data['desc']);
            }


            // Insert new records into currencies table (for AUG)
            $select_sql = "	SELECT currencies_id 
							FROM " . TABLE_CURRENCIES . " 
							WHERE code ='AUG' ";
            $result_sql = tep_db_query($select_sql);
            if ($row_sql = tep_db_fetch_array($result_sql)) {
                ; // Do nothing since record is there
            } else {
                $rate = $this->update_currency(DEFAULT_CURRENCY);
                tep_db_query("insert into " . TABLE_CURRENCIES . " (title, code, symbol_left, symbol_right, decimal_point, thousands_point, decimal_places, value) values ('e-gold grams', 'AUG', 'AUG', '', '.', '', '8', '" . $rate . "')");
            }
        }
        return 1;
    }

    function remove() {
        tep_db_query("delete from " . TABLE_CONFIGURATION . " where configuration_key in ('" . implode("', '", $this->keys()) . "')");
    }

    function configuration_information_keys() {
        return array('MODULE_PAYMENT_EGOLD_CURRENCY',
            //'MODULE_PAYMENT_EGOLD_ZONE',
            'MODULE_PAYMENT_EGOLD_ORDER_STATUS_ID',
            'MODULE_PAYMENT_EGOLD_PROCESSING_STATUS_ID',
            'MODULE_PAYMENT_EGOLD_MESSAGE',
            'MODULE_PAYMENT_EGOLD_EMAIL_MESSAGE',
            'MODULE_PAYMENT_EGOLD_CONFIRM_COMPLETE',
            'MODULE_PAYMENT_EGOLD_MANDATORY_ADDRESS_FIELD',
            'MODULE_PAYMENT_EGOLD_CUSTOMER_PAYMENT_INFO',
            'MODULE_PAYMENT_EGOLD_MANDATORY_IC_FIELD',
            'MODULE_PAYMENT_EGOLD_MANDATORY_CONTACT_FIELD');
    }

    function multi_lang_configuration_info_keys() {
        return array('MODULE_PAYMENT_EGOLD_MESSAGE',
            'MODULE_PAYMENT_EGOLD_EMAIL_MESSAGE');
    }

    function merchant_information_keys() {
        include_once(DIR_WS_CLASSES . "payment_methods.php");

        return array(
            'MODULE_PAYMENT_EGOLD_ACCOUNT' => array("field" => 'text', 'mapping' => 'MODULE_PAYMENT_EGOLD_LNG_ACCOUNT'),
            'MODULE_PAYMENT_EGOLD_NAME' => array("field" => 'text', 'mapping' => 'MODULE_PAYMENT_EGOLD_LNG_NAME'),
            'MODULE_PAYMENT_EGOLD_SECRET_WORD' => array("field" => 'text', 'mapping' => 'MODULE_PAYMENT_EGOLD_LNG_SECRET_WORD'),
            'MODULE_PAYMENT_TAX' => array("field" => 'select', 'mapping' => "MODULE_PAYMENT_TAX", "select" => payment_methods::get_tax_option())
        );
    }

    function get_pg_id() {
        return $this->payment_methods_id;
    }

    function keys() {
        return array('MODULE_PAYMENT_EGOLD_STATUS',
            'MODULE_PAYMENT_EGOLD_ACCOUNT',
            'MODULE_PAYMENT_EGOLD_NAME',
            'MODULE_PAYMENT_EGOLD_SECRET_WORD',
            'MODULE_PAYMENT_EGOLD_CURRENCY',
            'MODULE_PAYMENT_EGOLD_SORT_ORDER',
            'MODULE_PAYMENT_EGOLD_ORDER_STATUS_ID',
            'MODULE_PAYMENT_EGOLD_PROCESSING_STATUS_ID',
            'MODULE_PAYMENT_EGOLD_ZONE',
            'MODULE_PAYMENT_EGOLD_MESSAGE',
            'MODULE_PAYMENT_EGOLD_EMAIL_MESSAGE',
            'MODULE_PAYMENT_EGOLD_CONFIRM_COMPLETE',
            'MODULE_PAYMENT_EGOLD_LEGEND_COLOUR');
    }

    function get_orders_payment_info_file() {
        return DIR_FS_CATALOG_MODULES . 'payment/egold/admin/orders.inc.php';
    }

    function get_orders_list_payment_info_file() {
        return DIR_FS_CATALOG_MODULES . 'payment/egold/admin/orders_list.inc.php';
    }

    function get_ipn_class_file() {
        return DIR_FS_CATALOG_MODULES . 'payment/egold/classes/egold_ipn_class.php';
    }

}

?>