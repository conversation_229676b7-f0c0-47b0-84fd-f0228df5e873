<?php

/*
  $Id: egold.lng.php, v1.0 2005/11/29 18:08:05
  Author : <PERSON> (<EMAIL>)
  Title: e-gold Payment Module V1.0

  Revisions:
  Version 1.0 Initial Payment Module

  Copyright (c) 2006 SKC Venture

  Released under the GNU General Public License
 */

//begin ADMIN text
define('ENTRY_EG_PAYER_ACCOUNT', 'Payer e-gold Account:');
define('ENTRY_EG_TRANSACTION_ID', 'Transaction ID:');
define('ENTRY_EG_PAYMENT_STATUS', 'Payment Status:');
define('ENTRY_EG_PAYMENT_DATE', 'Payment Date:');
define('ENTRY_EG_PAYMENT_AMOUNT', 'Payment Amount:');
define('ENTRY_EG_PAYMENT_OUNCES', 'Ounces (Troy) paid:');
define('ENTRY_EG_USD_PER_OUNCES', 'US Dollars per Ounce:');
define('ENTRY_EG_TRANSACTION_FEE', 'Payment Receive Fee (ounces):');

define('TEXT_EGOLD_PAYMENT_AMOUNT', '%.2f %s\' worth of %s');

define('MODULE_PAYMENT_EGOLD_LNG_ACCOUNT', 'E-Gold Account');
define('MODULE_PAYMENT_EGOLD_LNG_NAME', 'E-Gold Name');
define('MODULE_PAYMENT_EGOLD_LNG_SECRET_WORD', 'E-Gold Secret Word');
define('MODULE_PAYMENT_TAX', 'Tax');
?>
