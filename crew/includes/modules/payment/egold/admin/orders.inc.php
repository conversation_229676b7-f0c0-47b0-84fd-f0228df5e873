<?
/*
  	$Id: orders.inc.php,v 1.5 2009/02/03 03:25:21 boonhock Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2006 SKC Venture
  	
  	Released under the GNU General Public License
*/

include_once(DIR_FS_CATALOG_MODULES . 'payment/egold/admin/languages/' . $language . '/egold.lng.php');

$egold_payment_status = array(	'1' => 'Success',
								'0' => 'Failed');

$egold_payment_metal_array = array(	'0' => 'Buyer\' Choice',
									'1' => 'Gold',
									'2' => 'Silver',
									'3' => 'Platinum',
									'4' => 'Palladium'
								);

$egold_trans_info_select_sql = "SELECT eg.*, egc.egold_currencies_code 
								FROM " . TABLE_PAYMENT_EGOLD . " AS eg 
								INNER JOIN " . TABLE_PAYMENT_EGOLD_CURRENCIES . " AS egc 
									ON eg.egold_payment_units=egc.egold_currencies_payment_unit 
								WHERE orders_id = '" . (int)$oID . "'";
$egold_trans_info_result_sql = tep_db_query($egold_trans_info_select_sql);
?>
<table border="0" width="100%" cellspacing="0" cellpadding="2">
<? ob_start(); ?>
	<tr>
		<td class="main">
      		<table border="0" width="100%" cellspacing="0" cellpadding="2">
      			<tr>
      				<td class="main" width="12%" nowrap><b><?=ENTRY_PAYMENT_METHOD?></b></td>
      				<td class="main">
      					<div>
      						<? if((int)$order->info['payment_methods_id']>0) { ?><div class="<?=((int)$payment_info_array[$order->info['payment_methods_id']]->confirm_complete_days>0?'rp_icon':'nrp_icon')?>"><b><?=((int)$payment_info_array[$order->info['payment_methods_id']]->confirm_complete_days>0?'RP':'NRP')?></b></div> <? } ?>
      						<div style="width:30px; height:15px; background-color:<?=$payment_module_info['display_colour']?>; float:left; vertical-align:top; text-align:center;">&nbsp;</div>
      						<div style="vertical-align: top">
      							&nbsp;&nbsp;
<?
	if ($view_payment_details_permission) {
		if (tep_not_null($last_payment_method) && $last_payment_method['payment_methods_id'] != $order->info['payment_methods_id']) {
			echo '<span class="redIndicator">'.(tep_not_null($payment_module_info['payment_methods_parent_title'])?$payment_module_info['payment_methods_parent_title'] . ' - ' :'') . $payment_module_info['payment_methods_title'].'</span>';
		} else {
			echo (tep_not_null($payment_module_info['payment_methods_parent_title'])?$payment_module_info['payment_methods_parent_title'] . ' - ' :'') . $payment_module_info['payment_methods_title'];
		}
		
		if (tep_not_null($last_payment_method) && $last_payment_method['payment_methods_id'] != $order->info['payment_methods_id']) {
			echo '&nbsp;&nbsp;<span class="redIndicator">(Last Payment Method: ' . (tep_not_null($last_payment_method['parent_payment_methods_title'])? $last_payment_method['parent_payment_methods_title'] . ' - ' : '' ) . $last_payment_method['payment_methods_title'] . ')</span>';
		}
	} else {
		echo (tep_not_null($payment_module_info['payment_methods_parent_title'])?$payment_module_info['payment_methods_parent_title'] . ' - ' :'') . $payment_module_info['payment_methods_title'];
	}
?>
							</div>
      					</div>
      				</td>
				</tr>
			</table>
		</td>
	</tr>
<?
$payment_method_title_info = ob_get_contents();
ob_end_clean();

if ($egold_trans_info_row = tep_db_fetch_array($egold_trans_info_result_sql)) {
	echo $payment_method_title_info;
	if (!$view_payment_details_permission) {
		;
	} else {
?>
  	<tr>
    	<td class="main">
      		<table border="0" width="100%" cellspacing="0" cellpadding="2">
      			<tr valign="top">
        			<td width="35%">
              			<table border="0" cellspacing="0" cellpadding="2">
              				<tr>
                				<td class="main" valign="top" nowrap><b><?=ENTRY_EG_PAYER_ACCOUNT?></b>&nbsp;</td>
                				<td class="main" nowrap>
                				<?
                					$customer_with_this_email_array = tep_check_duplicate_payment_email($egold_trans_info_row["egold_payer_account"], 'egold');	// including himself
                					$email_used_by_other = (count($customer_with_this_email_array) > 1) ? 1 : 0 ;
                					
                					$total_name_used_in_egold = tep_get_distinct_payment_email_used ($cid, 'egold');
                					$total_email_used = (count($total_name_used_in_egold) > 1) ? 1 : 0 ;
                					
	                				echo '<span class="'.(($email_used_by_other+$total_email_used > 0) ? 'redIndicator' : 'blackIndicator').'">' . $egold_trans_info_row["egold_payer_account"] . '</span>';
	                				
	                				$email_alert_reason = '';
                					
                					if ($email_used_by_other == 1) {
	                					$email_alert_reason .=	tep_draw_form('cust_lists_share_email', FILENAME_CUSTOMERS, 'action=show_report&customer_lists_subaction=do_search', 'post', 'target="_blank"') . "\n" . 
																tep_draw_hidden_field('customer_share_id', tep_array_serialize($customer_with_this_email_array)) . "\n" . 
																'<span class="redIndicator">e-gold Account exists in <a href="javascript: document.cust_lists_share_email.submit();"><u>' . count($customer_with_this_email_array) . '</u></a> profiles.</span><br>' . 
																'</form>' . "\n";
									}
									
									$email_alert_reason .= ($total_email_used == 1) ? '<span class="redIndicator"><a href="'.tep_href_link(FILENAME_ORDERS, 'cID='.$cid).'" target="_blank"><u>' . count($total_name_used_in_egold) . '</u></a> e-gold Account used in e-gold orders.</span><br>' : '';
	                				
	                				if (tep_not_null($email_alert_reason)) {
                						echo '<br><span class="smallText">' . $email_alert_reason . '</span>';
                					}
                				?>
                				</td>
              				</tr>
              				<tr>
                				<td class="main" valign="top" nowrap><b><?=ENTRY_EG_TRANSACTION_ID?></b>&nbsp;</td>
                				<td class="main"><?=$egold_trans_info_row["egold_payment_batch_num"] != 0 ? $egold_trans_info_row["egold_payment_batch_num"] : '&nbsp;'?></td>
              				</tr>
              				<tr>
                				<td class="main" valign="top" nowrap><b><?=ENTRY_EG_PAYMENT_STATUS?></b>&nbsp;</td>
                				<td class="main" nowrap><?=$egold_payment_status[$egold_trans_info_row["egold_transaction_status"]]?></td>
              				</tr>
              				<tr>
                				<td class="main" valign="top" nowrap><b><?=ENTRY_EG_PAYMENT_DATE?></b>&nbsp;</td>
                				<td class="main"><?=strftime(PREFERRED_DATE_TIME_FORMAT, $egold_trans_info_row["egold_payment_timestamp"])?></td>
              				</tr>
              			</table>
              		</td>
              		<td>
              			<table border="0" cellspacing="0" cellpadding="2">
              				<tr>
                				<td class="main" valign="top" nowrap><b><?=ENTRY_EG_PAYMENT_AMOUNT?></b>&nbsp;</td>
                				<td class="main" nowrap><?=sprintf(TEXT_EGOLD_PAYMENT_AMOUNT, $egold_trans_info_row["egold_payment_amount"], $egold_trans_info_row["egold_currencies_code"], $egold_payment_metal_array[$egold_trans_info_row["egold_payment_metal_id"]])?></td>
              				</tr>
              				<tr>
                				<td class="main" valign="top" nowrap><b><?=ENTRY_EG_PAYMENT_OUNCES?></b>&nbsp;</td>
                				<td class="main" nowrap><?=$egold_trans_info_row["egold_actual_payment_ounces"]?></td>
              				</tr>
              				<tr>
                				<td class="main" valign="top" nowrap><b><?=ENTRY_EG_USD_PER_OUNCES?></b>&nbsp;</td>
                				<td class="main" nowrap><?=$egold_trans_info_row["egold_usd_per_ounce"]?></td>
              				</tr>
              				<tr>
                				<td class="main" valign="top" nowrap><b><?=ENTRY_EG_TRANSACTION_FEE?></b>&nbsp;</td>
                				<td class="main" nowrap><?=$egold_trans_info_row["egold_feeweight"]?></td>
              				</tr>
              			</table>
        			</td>
      			</tr>
      		</table>
   		</td>
  	</tr>
<?
	}
} else {
	echo $payment_method_title_info;
}
?>
</table>