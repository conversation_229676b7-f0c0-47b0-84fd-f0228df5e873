<?
/*
  	$Id: orders_list.inc.php,v 1.1 2007/02/13 05:47:22 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2007 SKC Venture
  	
  	Released under the GNU General Public License
*/

include_once(DIR_FS_CATALOG_MODULES . 'payment/egold/admin/languages/' . $language . '/egold.lng.php');

$egold_payment_status = array(	'1' => 'Success',
								'0' => 'Failed');

$egold_payment_metal_array = array(	'0' => 'Buyer\' Choice',
									'1' => 'Gold',
									'2' => 'Silver',
									'3' => 'Platinum',
									'4' => 'Palladium'
								);

$egold_trans_info_select_sql = "SELECT eg.*, egc.egold_currencies_code 
								FROM " . TABLE_PAYMENT_EGOLD . " AS eg 
								INNER JOIN " . TABLE_PAYMENT_EGOLD_CURRENCIES . " AS egc 
									ON eg.egold_payment_units=egc.egold_currencies_payment_unit 
								WHERE orders_id = '" . $order_obj->orders_id . "'";
$egold_trans_info_result_sql = tep_db_query($egold_trans_info_select_sql);
?>
<table border="0" width="100%" cellspacing="0" cellpadding="2">
	<tr>
		<td class="subInvoiceBoxHeading">
			<div style="border-bottom: 1px solid #996600; border-top: 1px solid #996600; padding: 2px;">Payment Details</div>
		</td>
	</tr>
<?
if ($egold_trans_info_row = tep_db_fetch_array($egold_trans_info_result_sql)) {
?>
  	<tr>
    	<td class="main">
      		<table border="0" width="100%" cellspacing="0" cellpadding="2">
      			<tr valign="top">
        			<td width="50%">
              			<table border="0" cellspacing="0" cellpadding="2">
              				<tr>
                				<td class="invoiceRecords" valign="top" nowrap><?=ENTRY_EG_PAYER_ACCOUNT?></td>
                				<td class="invoiceRecords" valign="top">:&nbsp;</td>
                				<td class="invoiceRecords" nowrap><?=$egold_trans_info_row["egold_payer_account"]?></td>
              				</tr>
              				<tr>
                				<td class="invoiceRecords" valign="top" nowrap><?=ENTRY_EG_TRANSACTION_ID?></td>
                				<td class="invoiceRecords" valign="top">:&nbsp;</td>
                				<td class="invoiceRecords" nowrap><?=$egold_trans_info_row["egold_payment_batch_num"] != 0 ? $egold_trans_info_row["egold_payment_batch_num"] : '&nbsp;'?></td>
              				</tr>
              				<tr>
                				<td class="invoiceRecords" valign="top" nowrap><?=ENTRY_EG_PAYMENT_STATUS?></td>
                				<td class="invoiceRecords" valign="top">:&nbsp;</td>
                				<td class="invoiceRecords"><?=$egold_payment_status[$egold_trans_info_row["egold_transaction_status"]]?></td>
              				</tr>
              				<tr>
                				<td class="invoiceRecords" valign="top" nowrap><?=ENTRY_EG_PAYMENT_DATE?></td>
                				<td class="invoiceRecords" valign="top">:&nbsp;</td>
                				<td class="invoiceRecords" nowrap><?=strftime(PREFERRED_DATE_TIME_FORMAT, $egold_trans_info_row["egold_payment_timestamp"])?></td>
              				</tr>
              			</table>
        			</td>
        			<td width="50%">
              			<table border="0 cellspacing="0" cellpadding="2">
              				<tr>
                				<td class="invoiceRecords" nowrap valign="top"><?=ENTRY_EG_PAYMENT_AMOUNT?></td>
                				<td class="invoiceRecords" valign="top">:&nbsp;</td>
                				<td class="invoiceRecords"><?=sprintf(TEXT_EGOLD_PAYMENT_AMOUNT, $egold_trans_info_row["egold_payment_amount"], $egold_trans_info_row["egold_currencies_code"], $egold_payment_metal_array[$egold_trans_info_row["egold_payment_metal_id"]])?></td>
              				</tr>
              				<tr>
                				<td class="invoiceRecords" nowrap valign="top"><?=ENTRY_EG_PAYMENT_OUNCES?></td>
                				<td class="invoiceRecords" valign="top">:&nbsp;</td>
                				<td class="invoiceRecords"><?=$egold_trans_info_row["egold_actual_payment_ounces"]?></td>
              				</tr>
              				<tr>
                				<td class="invoiceRecords" nowrap valign="top"><?=ENTRY_EG_PAYMENT_AMOUNT?></td>
                				<td class="invoiceRecords" valign="top">:&nbsp;</td>
                				<td class="invoiceRecords"><?=$egold_trans_info_row["egold_usd_per_ounce"]?></td>
              				</tr>
              				<tr>
                				<td class="invoiceRecords" nowrap valign="top"><?=ENTRY_EG_PAYMENT_AMOUNT?></td>
                				<td class="invoiceRecords" valign="top">:&nbsp;</td>
                				<td class="invoiceRecords"><?=$egold_trans_info_row["egold_feeweight"]?></td>
              				</tr>
              			</table>
        			</td>
      			</tr>
      		</table>
   		</td>
  	</tr>
<?
} else {
?>
	<tr>
		<td class="invoiceRecords">No further payment information is available.</td>
	</tr>
<?
}
?>
</table>