<?

/*
  $Id: global_collect.php, v1.0 2005/11/29 18:08:05
  Author : <PERSON><PERSON> (<EMAIL>)
  Title: Global Collect Payment Module V1.0

  Revisions:
  Version 1.0 Initial Payment Module

  Copyright (c) 2005 SKC Venture

  Released under the GNU General Public License
 */

require_once('payment_gateway.php');

class global_collect extends payment_gateway {

    var $code, $title, $description, $enabled, $gcCurrencies;
    var $defCurr, $amount_decimal;
    var $check_trans_status_flag;
    var $get_orders_status_version;

    function global_collect($pm_id = 0) {
        global $order, $languages_id, $default_languages_id;

        $this->payment_methods_id = 0;
        $this->payment_methods_parent_id = 0;

        $this->code = 'global_collect';
        $this->title = $this->code;

        $this->preferred = true;
        $this->filename = 'global_collect.php';

        $this->amount_decimal = 2;
        $this->force_to_checkoutprocess = true;
        $this->merchant_server_ip_address = '**************'; //$_SERVER['SERVER_ADDR']; //'************';		//IP address of merchant server 
        $this->connect_via_proxy = false;

        $this->check_trans_status_flag = false;

        $this->get_orders_status_version = '2.0';
        /*
          //			TESTING SERVER
          Merchant Link	-	For IP-check https://ps.gcsip.nl/wdl/wdl
          -	For Client Authentication https://ca.gcsip.nl/wdl/wdl
          Customer Link	-	For IP-check https://ps.gcsip.nl/hpp/hpp
          Payment Console	-	For IP-check https://ps.gcsip.nl/wpc/wpc
          -	For Client Authentication https://ca.gcsip.nl/wpc/wpc

          //			Production SERVER
          Merchant Link	-	For IP-check https://ps.gcsip.com/wdl/wdl
          -	For Client Authentication https://ca.gcsip.com/wdl/wdl
          Customer Link	-	For IP-check https://ps.gcsip.com/hpp/hpp
          Payment Console	-	For IP-check https://ps.gcsip.com/wpc/wpc
          -	For Client Authentication https://ca.gcsip.com/wpc/wpc
         */

        $this->customer_link_url = 'https://ps.gcsip.com/wdl/wdl';
        $this->form_action_url = 'https://ps.gcsip.com/wdl/wdl';
        $this->has_ipn = true;
        $this->auto_cancel_period = 60; // In minutes

        $payment_methods_select_sql = "	SELECT payment_methods_parent_id, payment_methods_code, 
												payment_methods_types_id, 
												payment_methods_receive_status_mode, payment_methods_id, 
												payment_methods_title, payment_methods_sort_order, 
												payment_methods_receive_status, payment_methods_legend_color, 
												payment_methods_logo 
										FROM " . TABLE_PAYMENT_METHODS . " 
										WHERE 1 ";
        if ((int) $pm_id > 0) {
            $payment_methods_select_sql .= "	AND payment_methods_id = '" . (int) $pm_id . "'";
        } else {
            $payment_methods_select_sql .= "	AND payment_methods_filename = '" . tep_db_input($this->filename) . "'";
        }

        $payment_methods_result_sql = tep_db_query($payment_methods_select_sql);
        if ($payment_methods_row = tep_db_fetch_array($payment_methods_result_sql)) {
            $pm_desc_title_select_sql = "	SELECT payment_methods_description_title 
											FROM " . TABLE_PAYMENT_METHODS_DESCRIPTION . " 
											WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "' 
												AND (IF (languages_id = '" . tep_db_input($languages_id) . "', 1, IF ((	SELECT COUNT(payment_methods_id) > 0 
																														FROM " . TABLE_PAYMENT_METHODS_DESCRIPTION . " 
																														WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "' 
																															AND languages_id = '" . (int) $languages_id . "'), 0, languages_id = 1)))";
            $pm_desc_title_result_sql = tep_db_query($pm_desc_title_select_sql);
            $pm_desc_title_row = tep_db_fetch_array($pm_desc_title_result_sql);

            $this->payment_methods_id = (int) $payment_methods_row['payment_methods_id'];
            $this->payment_methods_parent_id = (int) $payment_methods_row['payment_methods_parent_id'];
            $this->code = $payment_methods_row['payment_methods_code'];
            $this->title = $payment_methods_row['payment_methods_title'];
            $this->display_title = $pm_desc_title_row['payment_methods_description_title'];
            $this->sort_order = $payment_methods_row['payment_methods_sort_order'];
            $this->enabled = $payment_methods_row['payment_methods_receive_status_mode'];
            $this->legend_display_colour = $payment_methods_row['payment_methods_legend_color'];
            $this->receive_status = $payment_methods_row['payment_methods_receive_status'];
            $this->description = $this->display_title;

            $this->gcCurrencies = $this->get_support_currencies($this->payment_methods_id);

            $configuration_setting_array = $this->load_pm_setting();

            $this->testing = ($configuration_setting_array['MODULE_PAYMENT_GLOBAL_COLLECT_TEST_MODE'] == 'True' ? true : false);

            //$this->merchant_server_ip_address = $configuration_setting_array['MODULE_PAYMENT_GLOBAL_COLLECT_SERVER_IP'];
            $this->payment = $configuration_setting_array['MODULE_PAYMENT_GLOBAL_COLLECT_PAYMENT'];
            $this->message = $configuration_setting_array['MODULE_PAYMENT_GLOBAL_COLLECT_MESSAGE'];
            $this->email_message = $configuration_setting_array['MODULE_PAYMENT_GLOBAL_COLLECT_EMAIL_MESSAGE'];
            $this->order_status_id = $configuration_setting_array['MODULE_PAYMENT_GLOBAL_COLLECT_ORDER_STATUS_ID'];
            $this->processing_status_id = $configuration_setting_array['MODULE_PAYMENT_GLOBAL_COLLECT_PROCESSING_STATUS_ID'];

            $this->confirm_complete_days = $configuration_setting_array['MODULE_PAYMENT_GLOBAL_COLLECT_CONFIRM_COMPLETE'];
            $this->require_address_information = $configuration_setting_array['MODULE_PAYMENT_GLOBAL_COLLECT_MANDATORY_ADDRESS_FIELD'];
            $this->require_ic_information = $configuration_setting_array['MODULE_PAYMENT_GLOBAL_COLLECT_MANDATORY_IC_FIELD'];
            $this->require_contact_information = $configuration_setting_array['MODULE_PAYMENT_GLOBAL_COLLECT_MANDATORY_CONTACT_FIELD'];
            $this->customer_payment_info = $configuration_setting_array['MODULE_PAYMENT_GLOBAL_COLLECT_CUSTOMER_PAYMENT_INFO'];

            $this->show_city = (isset($configuration_setting_array['MODULE_PAYMENT_GLOBAL_COLLECT_MANDATORY_CITY']) ? $configuration_setting_array['MODULE_PAYMENT_GLOBAL_COLLECT_MANDATORY_CITY'] : '');
            $this->show_zip = (isset($configuration_setting_array['MODULE_PAYMENT_GLOBAL_COLLECT_MANDATORY_ZIP']) ? $configuration_setting_array['MODULE_PAYMENT_GLOBAL_COLLECT_MANDATORY_ZIP'] : '');
            $this->show_surname = (isset($configuration_setting_array['MODULE_PAYMENT_GLOBAL_COLLECT_MANDATORY_SURNAME']) ? $configuration_setting_array['MODULE_PAYMENT_GLOBAL_COLLECT_MANDATORY_SURNAME'] : '');
            $this->show_housenumber = (isset($configuration_setting_array['MODULE_PAYMENT_GLOBAL_COLLECT_MANDATORY_HOUSENUMBER']) ? $configuration_setting_array['MODULE_PAYMENT_GLOBAL_COLLECT_MANDATORY_HOUSENUMBER'] : '');
            $this->show_street = (isset($configuration_setting_array['MODULE_PAYMENT_GLOBAL_COLLECT_MANDATORY_STREET']) ? $configuration_setting_array['MODULE_PAYMENT_GLOBAL_COLLECT_MANDATORY_STREET'] : '');
            $this->show_accountname = (isset($configuration_setting_array['MODULE_PAYMENT_GLOBAL_COLLECT_MANDATORY_ACCOUNTNAME']) ? $configuration_setting_array['MODULE_PAYMENT_GLOBAL_COLLECT_MANDATORY_ACCOUNTNAME'] : '');
            $this->show_account_number = (isset($configuration_setting_array['MODULE_PAYMENT_GLOBAL_COLLECT_MANDATORY_ACCOUNT_NUMBER']) ? $configuration_setting_array['MODULE_PAYMENT_GLOBAL_COLLECT_MANDATORY_ACCOUNT_NUMBER'] : '');
            $this->show_directdebittext = (isset($configuration_setting_array['MODULE_PAYMENT_GLOBAL_COLLECT_MANDATORY_DIRECTDEBITTEXT']) ? $configuration_setting_array['MODULE_PAYMENT_GLOBAL_COLLECT_MANDATORY_DIRECTDEBITTEXT'] : '');
            $this->show_vouchernumber = (isset($configuration_setting_array['MODULE_PAYMENT_GLOBAL_COLLECT_MANDATORY_VOUCHERNUMBER']) ? $configuration_setting_array['MODULE_PAYMENT_GLOBAL_COLLECT_MANDATORY_VOUCHERNUMBER'] : '');
            $this->show_vouchervalue = (isset($configuration_setting_array['MODULE_PAYMENT_GLOBAL_COLLECT_MANDATORY_VOUCHERVALUE']) ? $configuration_setting_array['MODULE_PAYMENT_GLOBAL_COLLECT_MANDATORY_VOUCHERVALUE'] : '');

            if ((int) $this->order_status_id > 0) {
                $this->order_status = $this->order_status_id;
            }

            $this->email_footer = str_replace(array("<br>", "<BR>", "\r\n"), array("\n", "\n", ''), $this->email_message);
            $this->order_processing_status = (int) $this->processing_status_id > 0 ? $this->processing_status_id : 7;
            $this->form_submit_url = '';

            if ($this->testing) {
                $this->customer_link_url = 'https://ps.gcsip.nl/wdl/wdl';
                $this->form_action_url = 'https://ps.gcsip.nl/wdl/wdl';
            }
        }
    }

    function javascript_validation() {
        return false;
    }

    function selection() {
        global $languages_id, $default_languages_id, $language, $customer_id;

        include_once(DIR_WS_LANGUAGES . $language . '/modules/payment/global_collect.php');

        $selection_array = array();

        $payment_methods_select_sql = "	SELECT payment_methods_id, payment_methods_code, payment_methods_types_id, payment_methods_logo, payment_methods_receive_status_mode 
										FROM " . TABLE_PAYMENT_METHODS . " 
										WHERE payment_methods_receive_status = 1 
											AND payment_methods_receive_status_mode <> 0 
											AND payment_methods_parent_id = '" . (int) $this->payment_methods_id . "' 
										ORDER BY payment_methods_sort_order";
        $payment_methods_result_sql = tep_db_query($payment_methods_select_sql);
        while ($payment_methods_row = tep_db_fetch_array($payment_methods_result_sql)) {
            $pm_desc_title_select_sql = "	SELECT payment_methods_description_title 
											FROM " . TABLE_PAYMENT_METHODS_DESCRIPTION . " 
											WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "' 
												AND (IF (languages_id = '" . tep_db_input($languages_id) . "', 1, IF ((	SELECT COUNT(payment_methods_id) > 0 
																														FROM " . TABLE_PAYMENT_METHODS_DESCRIPTION . " 
																														WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "' 
																															AND languages_id = '" . (int) $languages_id . "'), 0, languages_id = 1)))";
            $pm_desc_title_result_sql = tep_db_query($pm_desc_title_select_sql);
            $pm_desc_title_row = tep_db_fetch_array($pm_desc_title_result_sql);

            $pm_array = $this->load_pm_setting(1, $payment_methods_row['payment_methods_id']);

            $selection_array[] = array('payment_gateway_code' => $this->code,
                'payment_methods_receive_status_mode' => $payment_methods_row['payment_methods_receive_status_mode'],
                'payment_methods_id' => $payment_methods_row['payment_methods_id'],
                'payment_methods_code' => $payment_methods_row['payment_methods_code'],
                'payment_methods_types_id' => $payment_methods_row['payment_methods_types_id'],
                'payment_methods_parent_id' => $this->payment_methods_id,
                'payment_methods_logo' => $payment_methods_row['payment_methods_logo'],
                'payment_methods_description_title' => $pm_desc_title_row['payment_methods_description_title'],
                'currency' => $this->get_support_currencies($payment_methods_row['payment_methods_id']),
                'confirm_complete_days' => $pm_array['MODULE_PAYMENT_GLOBAL_COLLECT_CONFIRM_COMPLETE'],
                'show_billing_address' => $pm_array['MODULE_PAYMENT_GLOBAL_COLLECT_MANDATORY_ADDRESS_FIELD'],
                'show_contact_number' => $pm_array['MODULE_PAYMENT_GLOBAL_COLLECT_MANDATORY_CONTACT_FIELD'],
                'show_ic' => $pm_array['MODULE_PAYMENT_GLOBAL_COLLECT_MANDATORY_IC_FIELD'],
                'show_surname' => $pm_array['MODULE_PAYMENT_GLOBAL_COLLECT_MANDATORY_SURNAME'],
                'show_housenumber' => $pm_array['MODULE_PAYMENT_GLOBAL_COLLECT_MANDATORY_HOUSENUMBER'],
                'show_street' => $pm_array['MODULE_PAYMENT_GLOBAL_COLLECT_MANDATORY_STREET'],
                'show_zip' => $pm_array['MODULE_PAYMENT_GLOBAL_COLLECT_MANDATORY_ZIP'],
                'show_city' => $pm_array['MODULE_PAYMENT_GLOBAL_COLLECT_MANDATORY_CITY'],
                'show_accountname' => $pm_array['MODULE_PAYMENT_GLOBAL_COLLECT_MANDATORY_ACCOUNTNAME'],
                'show_accountnumber' => $pm_array['MODULE_PAYMENT_GLOBAL_COLLECT_MANDATORY_ACCOUNTNUMBER'],
                'show_bankcode' => $pm_array['MODULE_PAYMENT_GLOBAL_COLLECT_MANDATORY_BANKCODE'],
                'show_branchcode' => $pm_array['MODULE_PAYMENT_GLOBAL_COLLECT_MANDATORY_BRANHCODE'],
                'show_directdebittext' => $pm_array['MODULE_PAYMENT_GLOBAL_COLLECT_MANDATORY_DIRECTDEBITTEXT'],
                'show_vouchernumber' => $pm_array['MODULE_PAYMENT_GLOBAL_COLLECT_MANDATORY_VOUCHERNUMBER'],
//                'show_vouchervalue' => $pm_array['MODULE_PAYMENT_GLOBAL_COLLECT_MANDATORY_VOUCHERVALUE']
            );

            if ($payment_methods_row['payment_methods_receive_status_mode'] == '-1') { // Inactive
                $pm_status_message_select = "	SELECT payment_methods_status_message 
												FROM " . TABLE_PAYMENT_METHODS_STATUS_DESCRIPTION . " 
												WHERE payment_methods_mode = 'RECEIVE' 
													AND payment_methods_status = '-1' 
													AND languages_id = '" . (int) $languages_id . "' 
													AND payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "'";
                $pm_status_message_result = tep_db_query($pm_status_message_select);
                $pm_status_message_row = tep_db_fetch_array($pm_status_message_result);
                $selection_array[sizeof($selection_array) - 1]['payment_methods_status_message'] = $pm_status_message_row['payment_methods_status_message'];
            }
        }

        return $selection_array;
    }

    function draw_confirm_button() {
        return tep_image_button2('red', 'javascript:document.checkout_confirmation.submit()', MODULE_PAYMENT_GLOBAL_COLLECT_TEXT_CART, 200);
    }

    function get_confirm_button_caption() {
        return MODULE_PAYMENT_GLOBAL_COLLECT_TEXT_TITLE;
    }

    function pre_confirmation_check() {
        global $currency, $currencies, $messageStack;
        $supported_currency_str_array = array();

        if (!in_array($currency, $this->gcCurrencies)) {
            
        }
    }

    function confirmation() {
        global $HTTP_POST_VARS;

        return array('title' => $this->message);
    }

    function get_support_currencies($pm_id, $by_zone = true) {
        global $zone_info_array;

        $default_currency = '';
        $support_currencies_array = array();

        $currency_code_select_sql = "	SELECT currency_code, payment_methods_instance_default 
    									FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " 
    									WHERE payment_methods_id = '" . (int) $pm_id . "' 
    									ORDER BY currency_code";
        $currency_code_result_sql = tep_db_query($currency_code_select_sql);
        if (tep_db_num_rows($currency_code_result_sql) > 0) {
            while ($currency_code_row = tep_db_fetch_array($currency_code_result_sql)) {
                $support_currencies_array[] = $currency_code_row['currency_code'];
                if ($currency_code_row['payment_methods_instance_default'] == 1) {
                    $default_currency = $currency_code_row['currency_code'];
                }
            }
        } else {
            if ($this->payment_methods_parent_id > 0) {
                $payment_methods_parent_id = $this->payment_methods_parent_id;
            } else {
                $payment_methods_parent_id = $this->payment_methods_id;
            }
            $parent_currency_code_select_sql = "	SELECT currency_code, payment_methods_instance_default 
			    									FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " 
			    									WHERE payment_methods_id = '" . (int) $payment_methods_parent_id . "' 
			    									ORDER BY currency_code";
            $parent_currency_code_result_sql = tep_db_query($parent_currency_code_select_sql);
            while ($parent_currency_code_row = tep_db_fetch_array($parent_currency_code_result_sql)) {
                $support_currencies_array[] = $parent_currency_code_row['currency_code'];
                if ($parent_currency_code_row['payment_methods_instance_default'] == 1) {
                    $default_currency = $parent_currency_code_row['currency_code'];
                }
            }
        }

        if (tep_not_null($default_currency)) {
            $this->defCurr = $default_currency;
        }

        if ($by_zone) {
            if (isset($zone_info_array)) {
                $support_currencies_array = array_intersect($zone_info_array[3]->zone_currency_id, $support_currencies_array);
                if (count($support_currencies_array)) {
                    array_multisort($support_currencies_array);
                } else {
                    $support_currencies_array = array($default_currency);
                }
            } else {
                $support_currencies_array = array($default_currency);
            }
        } else {
            $this->set_support_currencies($support_currencies_array);
        }
        return $support_currencies_array;
    }

    function set_support_currencies($currency_array) {
        $this->gcCurrencies = $currency_array;
    }

    function process_button() {
        global $HTTP_POST_VARS, $total_cost, $shipping_selected, $shipping_method;
        global $currencies, $currenshipping_costcy, $order, $languages_id;
        global $order_logged, $REMOTE_ADDR, $customer_id, $currency, $country;

        $language_code = 'en';

        if ($this->force_to_checkoutprocess && !tep_session_is_registered('order_logged'))
            return;

        $this->gcCurrencies = $this->get_support_currencies($this->payment_methods_id, false);

        $gcCurrencies = $currency;
        if (!in_array($gcCurrencies, $this->gcCurrencies)) {
            $gcCurrencies = $this->gcCurrencies[0];
        }
        $this->get_merchant_account($gcCurrencies);

        $countries_select_sql = "	SELECT countries_iso_code_2 
      								FROM " . TABLE_COUNTRIES . "
      								WHERE countries_id = '" . $country . "'";
        $countries_result_sql = tep_db_query($countries_select_sql);
        $countries_row = tep_db_fetch_array($countries_result_sql);
        $xml_country = $countries_row['countries_iso_code_2'];

        $order_amt = $order->info['total'] * $currencies->get_value($gcCurrencies);
        $formatted_order_amt = number_format($order_amt, 2, '.', '');

        $exponent = (int) (tep_not_null($this->exponent) ? $this->exponent : 2);
        if ($exponent > 0) {
            $formatted_order_amt = $formatted_order_amt * pow(10, $exponent);
        }

        switch ($this->payment) {
            case "1": //cc
                $hostedindicator = 1; // 0 �C Hosted Merchant Link is not to used , 1 �C Hosted Merchant Link is to be used (default if configured) 

                $submit_xml = "<XML>
								  <REQUEST>
								    <ACTION>INSERT_ORDERWITHPAYMENT</ACTION>
								    <META>
								      <MERCHANTID>" . $this->merchantid . "</MERCHANTID>
								      <IPADDRESS>" . $this->merchant_server_ip_address . "</IPADDRESS>
								      <VERSION>2.0</VERSION>
								    </META>
								    <PARAMS>
								      <ORDER>
								        <ORDERID>" . $order_logged . "</ORDERID>
								        <MERCHANTREFERENCE>" . $order_logged . "</MERCHANTREFERENCE>
										<IPADDRESSCUSTOMER>" . $REMOTE_ADDR . "</IPADDRESSCUSTOMER>
								        <AMOUNT>" . preg_replace('/[^\d]/', '', $formatted_order_amt) . "</AMOUNT>
								        <CURRENCYCODE>" . $gcCurrencies . "</CURRENCYCODE>
								        <COUNTRYCODE>" . $xml_country . "</COUNTRYCODE>
								        <LANGUAGECODE>" . $language_code . "</LANGUAGECODE>
								        <FIRSTNAME>" . $_SESSION['customer_first_name'] . "</FIRSTNAME>
								        <SURNAME>" . $_SESSION['customer_last_name'] . "</SURNAME>
								        <CUSTOMERID>" . $_SESSION['customer_id'] . "</CUSTOMERID>
								      </ORDER>
								      <PAYMENT>
								      	<AVSINDICATOR>1</AVSINDICATOR>
								      	<PAYMENTPRODUCTID>" . $this->code . "</PAYMENTPRODUCTID>
								        <AMOUNT>" . preg_replace('/[^\d]/', '', $formatted_order_amt) . "</AMOUNT>
								        <CURRENCYCODE>" . $gcCurrencies . "</CURRENCYCODE>
								        <LANGUAGECODE>" . $language_code . "</LANGUAGECODE>
								        <COUNTRYCODE>" . $xml_country . "</COUNTRYCODE>
								        <HOSTEDINDICATOR>" . $hostedindicator . "</HOSTEDINDICATOR>
								      </PAYMENT>
								    </PARAMS>
								  </REQUEST>
								</XML>";
                break;
            case "4": //For Bank Transfer and Online Bank Transfer (including Brazil (51) and Korea (52))
                $surname = '';
                $city = ''; // $order->billing['city'] ?
                $fiscalnumber = ''; // Required for payment product 51 (Bank Transfer Brazil) and 52 (Bank Transfer Korea).

                $submit_xml = "	<XML> 
									<REQUEST> 
										<ACTION>INSERT_ORDERWITHPAYMENT</ACTION> 
										<META> 
											<MERCHANTID>" . $this->merchantid . "</MERCHANTID>
											<IPADDRESS>" . $this->merchant_server_ip_address . "</IPADDRESS>
											<VERSION>2.0</VERSION>
										</META> 
										<PARAMS> 
											<ORDER> 
										        <ORDERID>" . $order_logged . "</ORDERID>
										        <MERCHANTREFERENCE>" . $order_logged . "</MERCHANTREFERENCE>
												<IPADDRESSCUSTOMER>" . $REMOTE_ADDR . "</IPADDRESSCUSTOMER>
										        <AMOUNT>" . preg_replace('/[^\d]/', '', $formatted_order_amt) . "</AMOUNT>
										        <CURRENCYCODE>" . $gcCurrencies . "</CURRENCYCODE>
										        <COUNTRYCODE>" . $xml_country . "</COUNTRYCODE>
										        <LANGUAGECODE>" . $language_code . "</LANGUAGECODE>
										        <FIRSTNAME>" . $_SESSION['customer_first_name'] . "</FIRSTNAME>
										        <SURNAME>" . $_SESSION['customer_last_name'] . "</SURNAME>
										        <CUSTOMERID>" . $_SESSION['customer_id'] . "</CUSTOMERID>
											</ORDER> 
											<PAYMENT>           
												<PAYMENTPRODUCTID>" . $this->code . "</PAYMENTPRODUCTID> 
										        <AMOUNT>" . preg_replace('/[^\d]/', '', $formatted_order_amt) . "</AMOUNT>
										        <CURRENCYCODE>" . $gcCurrencies . "</CURRENCYCODE>
												<COUNTRYCODE>" . $xml_country . "</COUNTRYCODE> 
												<FIRSTNAME>" . $firstname . "</FIRSTNAME> 
												<SURNAME>" . $lastname . "</SURNAME> 
												<LANGUAGECODE>" . $language_code . "</LANGUAGECODE> 
												<SURNAME>" . $surname . "</SURNAME>
												<CITY>" . $city . "</CITY>
												<FISCALNUMBER>" . $fiscalnumber . "</FISCALNUMBER> 
											</PAYMENT> 
										</PARAMS> 
									</REQUEST> 
								</XML>";
                break;
            case "8": //Real-time Bank Transfer
                /* 	page 72
                  In the case of giropay (816), iDEAL (809), Raifeissen ELBA (820) and IPS PRC Debit/Credit Card
                  (400) no values have to be posted by the merchant to the Real-time Bank. GlobalCollect
                  provides the complete URL that has to be used to redirect the consumer. */
                $account_number = '';
                $bank_code = '';
                $issuer_id = '';
                $expiration_period = '';
                switch ($this->code) {
                    case "400": // IPS PRC Debit/Credit Card
                        $xml_country = 'CN';
                        break;
                    case "402": // eCard (Poland)
                        $xml_country = 'PL';
                        break;
                    case "801": // ING Home��Pay (Belgium) page 72
                        $xml_country = 'BE';
                        break;
                    case "802": // Nordea E-maksu (Finland)
                        $xml_country = 'FI';
                        break;
                    case "803": // Nordea E-betaling (Denmark)
                        $xml_country = 'DK';
                        break;
                    case "805": // Nordea e-Betalning (Sweden)
                        $xml_country = 'SE';
                        break;
                    case "809": // iDEAL (Netherlands)
                        $xml_country = 'NL';
                        $issuer_id = '121';
                        $expiration_period = '15';
                        break;
                    case "810": // eNets (SG)
                        $xml_country = 'SG';
                        break;
                    case "816": // Giropay (Germany)
                        $xml_country = 'DE';
                        $account_number = '123456';
                        $bank_code = '********';
                        break;
                    case "820": // Raifeissen ELBA (eps Online-��berweisung Austria)
                        $xml_country = 'AT';
                        break;
                    case "830": // PaySafeCard
                        break;
                    case "836": // Sofort��berweisung
                        break;
                    case "840": // PayPal
                        break;
                    case "841": // Webmoney
                        break;
                }

                $submit_xml = "	<XML> 
									<REQUEST> 
										<ACTION>INSERT_ORDERWITHPAYMENT</ACTION> 
										<META> 
											<MERCHANTID>" . $this->merchantid . "</MERCHANTID>
											<IPADDRESS>" . $this->merchant_server_ip_address . "</IPADDRESS>
											<VERSION>2.0</VERSION>
										</META> 
										<PARAMS>
											<ORDER> 
										        <ORDERID>" . $order_logged . "</ORDERID>
										        <MERCHANTREFERENCE>" . $order_logged . "</MERCHANTREFERENCE>
												<IPADDRESSCUSTOMER>" . $REMOTE_ADDR . "</IPADDRESSCUSTOMER>
										        <AMOUNT>" . preg_replace('/[^\d]/', '', $formatted_order_amt) . "</AMOUNT>
										        <CURRENCYCODE>" . $gcCurrencies . "</CURRENCYCODE>
										        <COUNTRYCODE>" . $xml_country . "</COUNTRYCODE>
										        <LANGUAGECODE>" . $language_code . "</LANGUAGECODE>
										        <FIRSTNAME>" . $_SESSION['customer_first_name'] . "</FIRSTNAME>
										        <SURNAME>" . $_SESSION['customer_last_name'] . "</SURNAME>
										        <CUSTOMERID>" . $_SESSION['customer_id'] . "</CUSTOMERID>
											</ORDER>
											<PAYMENT>      
												<PAYMENTPRODUCTID>" . $this->code . "</PAYMENTPRODUCTID> 
												<ORDERID>" . $order_logged . "</ORDERID>
												<AMOUNT>" . preg_replace('/[^\d]/', '', $formatted_order_amt) . "</AMOUNT>
										        <CURRENCYCODE>" . $gcCurrencies . "</CURRENCYCODE>
												<COUNTRYCODE>" . $xml_country . "</COUNTRYCODE> 
												<LANGUAGECODE>" . $language_code . "</LANGUAGECODE>
												<ACCOUNTNUMBER>" . $account_number . "</ACCOUNTNUMBER>
												<BANKCODE>" . $bank_code . "</BANKCODE>
												<ISSUERID>" . $issuer_id . "</ISSUERID>
												<EXPIRATIONPERIOD>" . $expiration_period . "</EXPIRATIONPERIOD>
												<RETURNURL>" . tep_href_link(FILENAME_CHECKOUT_PROCESS) . "</RETURNURL>
											</PAYMENT> 
										</PARAMS> 
									</REQUEST> 
								</XML>";
                break;
            case "14": //Pre-Paid Methods 
                $cp_city = $_SESSION['checkout_city'];
                $cp_surname = $_SESSION['checkout_surname'];
                $cp_city = $_SESSION['checkout_city'];
                $cp_voucher_number = $_SESSION['checkout_voucher_number'];
                $cp_voucher_value = $_SESSION['checkout_voucher_value'];

                $submit_xml = "	<XML> 
									<REQUEST> 
										<ACTION>INSERT_ORDERWITHPAYMENT</ACTION> 
										<META> 
											<MERCHANTID>" . $this->merchantid . "</MERCHANTID>
											<IPADDRESS>" . $this->merchant_server_ip_address . "</IPADDRESS>
											<VERSION>2.0</VERSION>
										</META> 
										<PARAMS> 
											<ORDER> 
										        <ORDERID>" . $order_logged . "</ORDERID>
										        <MERCHANTREFERENCE>" . $order_logged . "</MERCHANTREFERENCE>
												<IPADDRESSCUSTOMER>" . $REMOTE_ADDR . "</IPADDRESSCUSTOMER>
										        <AMOUNT>" . preg_replace('/[^\d]/', '', $formatted_order_amt) . "</AMOUNT>
										        <CURRENCYCODE>" . $gcCurrencies . "</CURRENCYCODE>
										        <COUNTRYCODE>" . $xml_country . "</COUNTRYCODE>
										        <LANGUAGECODE>" . $language_code . "</LANGUAGECODE>
											</ORDER>
											<PAYMENT>      
												<COUNTRYCODE>" . $xml_country . "</COUNTRYCODE> 
												<SURNAME>" . $cp_surname . "</SURNAME> 
												<CITY>" . $cp_city . "</CITY>
												<VOUCHERNUMBER>" . $cp_voucher_number . "</VOUCHERNUMBER>
												<VOUCHERVALUE>" . $cp_voucher_value . "</VOUCHERVALUE>
											</PAYMENT> 
										</PARAMS> 
									</REQUEST> 
								</XML>";
            case "3": //For Direct Debit payments

                $cp_companydata = '';
                $cp_companyname = '';
                $cp_title = '';
                $cp_firstname = '';
                $cp_prefixsurname = '';

                $cp_surname = $_SESSION['checkout_surname'];
                $cp_street = $_SESSION['checkout_street'];
                $cp_housenumber = $_SESSION['checkout_housenumber'];
                $cp_additionaladdressinfo = '';
                $cp_city = $_SESSION['checkout_city'];
                $cp_zip = $_SESSION['checkout_zip'];

                $cp_state = '';
                $cp_bankcode = $_SESSION['checkout_bankcode'];
                $cp_bankname = '';
                $cp_branchcode = $_SESSION['checkout_branchcode'];
                $cp_bankcheckdigit = '';

                $cp_accountnumber = $_SESSION['checkout_accountnumber'];
                $cp_accountname = $_SESSION['checkout_accountname'];
                $cp_datecollect = '';
                $cp_directdebittext = $_SESSION['checkout_directdebittext'];
                $cp_authorisationid = '';

                $cp_customerbankstreet = '';
                $cp_customerbanknumber = '';
                $cp_customerbankzip = '';
                $cp_customerbankcity = '';
                $cp_bankfiliale = '';

                $cp_bankagenzia = '';
                $cp_domicilio = '';
                $cp_provincia = '';
                $cp_transactiontype = '';
                $cp_iban = '';
                $cp_addressline = '';

                /*
                  Transaction Type NL Values
                  01 - 	First collection using this account. For first collections on a Postgiro-account (referred to
                  as "onzuivere posten") the accountnumber will be verified against the accountname/city by
                  the Postbank. This will take one additional processing day.
                  02 - 	Other

                  Transaction Type GB Values
                  01 - 	First payment Direct Debit
                  17 - 	Direct Debit (nth payment)
                  18 - 	Re-presented Direct Debit (after failed attempt)
                  19 - 	Final payment Direct Debit
                  0N - 	(zero N) New or Reinstated Direct Debit Instruction
                  0C - 	(zero C) Cancellation of Direct Debit Instruction
                  0S - 	(zero S) Conversion of paper DDI to electronic DDI (only used once, when migrating from traditional DD to AUDDIS .
                  If 0N, 0C or 0S is used the amount must be set to zero.
                 */

                $submit_xml = "	<XML> 
									<REQUEST> 
										<ACTION>INSERT_ORDERWITHPAYMENT</ACTION> 
											<META> 
												<MERCHANTID>" . $this->merchantid . "</MERCHANTID>
												<IPADDRESS>" . $this->merchant_server_ip_address . "</IPADDRESS>
												<VERSION>2.0</VERSION>
											</META> 
							    			<PARAMS>
											<ORDER> 
												<ORDERID>" . $order_logged . "</ORDERID> 
												<MERCHANTREFERENCE>" . $order_logged . "</MERCHANTREFERENCE>
												<IPADDRESSCUSTOMER>" . $REMOTE_ADDR . "</IPADDRESSCUSTOMER>
												<AMOUNT>" . preg_replace('/[^\d]/', '', $formatted_order_amt) . "</AMOUNT> 
												<CURRENCYCODE>" . $gcCurrencies . "</CURRENCYCODE> 
										        <COUNTRYCODE>" . $xml_country . "</COUNTRYCODE>
										        <LANGUAGECODE>" . $language_code . "</LANGUAGECODE>
											</ORDER> 
											<PAYMENT> 
												<PAYMENTPRODUCTID>" . $this->code . "</PAYMENTPRODUCTID> 
												<AMOUNT>" . preg_replace('/[^\d]/', '', $formatted_order_amt) . "</AMOUNT> 
												<CURRENCYCODE>" . $gcCurrencies . "</CURRENCYCODE> 
												
												<COMPANYDATA>" . $cp_companydata . "</COMPANYDATA>
												<COMPANYNAME>" . $cp_companyname . "</COMPANYNAME>
												<COUNTRYCODE>" . $xml_country . "</COUNTRYCODE>
												<CUSTOMERID>" . $customer_id . "</CUSTOMERID>
												<TITLE>" . $cp_title . "</TITLE>
												<FIRSTNAME>" . $cp_firstname . "</FIRSTNAME>
												<PREFIXSURNAME>" . $cp_prefixsurname . "</PREFIXSURNAME>
												
												<SURNAME>" . $cp_surname . "</SURNAME>
												<STREET>" . $cp_street . "</STREET>
												<HOUSENUMBER>" . $cp_housenumber . "</HOUSENUMBER>
												<ADDITIONALADDRESSINFO>" . $cp_additionaladdressinfo . "</ADDITIONALADDRESSINFO>
												<ZIP>" . $cp_zip . "</ZIP>
												
												<CITY>" . $cp_city . "</CITY>
												<STATE>" . $cp_state . "</STATE>
												<BANKCODE>" . $cp_bankcode . "</BANKCODE>
												<BANKNAME>" . $cp_bankname . "</BANKNAME>
												<BRANCHCODE>" . $cp_branchcode . "</BRANCHCODE>
												
												<BANKCHECKDIGIT>" . $cp_bankcheckdigit . "</BANKCHECKDIGIT>
												<ACCOUNTNUMBER>" . $cp_accountnumber . "</ACCOUNTNUMBER>
												<ACCOUNTNAME>" . $cp_accountname . "</ACCOUNTNAME>
												<DATECOLLECT>" . $cp_datecollect . "</DATECOLLECT>
												<DIRECTDEBITTEXT>" . $cp_directdebittext . "</DIRECTDEBITTEXT>
												
												<AUTHORISATIONID>" . $cp_authorisationid . "</AUTHORISATIONID>
												<CUSTOMERBANKSTREET>" . $cp_customerbankstreet . "</CUSTOMERBANKSTREET>
												<CUSTOMERBANKNUMBER>" . $cp_customerbanknumber . "</CUSTOMERBANKNUMBER>
												<CUSTOMERBANKZIP>" . $cp_customerbankzip . "</CUSTOMERBANKZIP>
												<CUSTOMERBANKCITY>" . $cp_customerbankcity . "</CUSTOMERBANKCITY>
												
												<BANKFILIALE>" . $cp_bankfiliale . "</BANKFILIALE>
												<BANKAGENZIA>" . $cp_bankagenzia . "</BANKAGENZIA>
												<DOMICILIO>" . $cp_domicilio . "</DOMICILIO>
												<PROVINCIA>" . $cp_provincia . "</PROVINCIA>
												<TRANSACTIONTYPE>" . $cp_transactiontype . "</TRANSACTIONTYPE>
												
												<IBAN>" . $cp_iban . "</IBAN>
												<ADDRESSLINE>" . $cp_addressline . "</ADDRESSLINE>
											</PAYMENT> 
										</PARAMS> 
									</REQUEST> 
								</XML>";
                break;
        }

        $result_array = $this->curl_connect($this->customer_link_url, "IN=" . $submit_xml);
        
        if ($result_array['error']) {
            tep_redirect(tep_href_link(FILENAME_CHECKOUT_PAYMENT, 'error_message=' . urlencode(MODULE_PAYMENT_GLOBAL_COLLECT_TEXT_ERROR_MESSAGE), 'SSL', true, false));
        } else {
            $process_button_string = '';

            switch ($this->payment) {
                case "1": //Credit Card
                    if (is_array($result_array['ROW']['_c'])) {
                        foreach ($result_array['ROW']['_c'] as $result_key => $result_data) {
                            $process_button_string .= tep_draw_hidden_field($result_key, $result_data['_v']);
                        }
                    }

                    $this->form_submit_url = $result_array['ROW']['_c']['FORMACTION']['_v'];
                    $this->form_submit_method = $result_array['ROW']['_c']['FORMMETHOD']['_v'];

                    break;
                case "8": //real time bank transfer
                    if (isset($result_array['ROW']['_c']) && count($result_array['ROW']['_c'])) {
                        $this->form_submit_url = $result_array['ROW']['_c']['FORMACTION']['_v'];
                        $this->form_submit_method = $result_array['ROW']['_c']['FORMMETHOD']['_v'];
                    }
                    break;
                case "3": //For Direct Debit payments
                    if (isset($result_array['ROW']['_c']) && count($result_array['ROW']['_c'])) {
                        $this->form_submit_url = tep_href_link(FILENAME_CHECKOUT_PROCESS);
                        $this->form_submit_method = 'post';
                    }
                    break;
                case "4": //For Bank Transfer and Online Bank Transfer (including Brazil (51) and Korea (52))
                    if (isset($result_array['ROW']['_c']) && count($result_array['ROW']['_c'])) {
                        //$this->form_submit_url = tep_href_link(FILENAME_CHECKOUT_PROCESS);
                        //$this->form_submit_url = tep_href_link(FILENAME_CHECKOUT_SUCCESS, 'action=success', 'SSL');
                        //$this->form_submit_method = 'post';

                        $_SESSION['global_collect_success_msg'] = $this->email_message . '<BR><BR>' .
                                (isset($result_array['ROW']['_c']['BILLERID']['_v']) ? 'Bill ID: ' . $result_array['ROW']['_c']['BILLERID']['_v'] : '') . '<BR>' .
                                (isset($result_array['ROW']['_c']['BANKNAME']['_v']) ? 'Bank Name: ' . $result_array['ROW']['_c']['BANKNAME']['_v'] : '') . '<BR>' .
                                (isset($result_array['ROW']['_c']['ACCOUNTHOLDER']['_v']) ? 'Account Holder: ' . $result_array['ROW']['_c']['ACCOUNTHOLDER']['_v'] : '') . '<BR>' .
                                (isset($result_array['ROW']['_c']['SWIFTCODE']['_v']) ? 'Swift Code: ' . $result_array['ROW']['_c']['SWIFTCODE']['_v'] : '') . '<BR>' .
                                (isset($result_array['ROW']['_c']['BANKACCOUNTNUMBER']['_v']) ? 'Bank Account Number: ' . $result_array['ROW']['_c']['BANKACCOUNTNUMBER']['_v'] : '') . '<BR>' .
                                (isset($result_array['ROW']['_c']['PAYMENTREFERENCE']['_v']) ? 'Payment Reference: ' . $result_array['ROW']['_c']['PAYMENTREFERENCE']['_v'] : '') . '<BR>' .
                                (isset($result_array['ROW']['_c']['CUSTOMERPAYMENTREFERENCE']['_v']) ? 'Customer Payment Reference: ' . $result_array['ROW']['_c']['CUSTOMERPAYMENTREFERENCE']['_v'] : '') . '<BR>' .
                                (isset($result_array['ROW']['_c']['CITY']['_v']) ? 'City: ' . $result_array['ROW']['_c']['CITY']['_v'] : '') . '<BR>' .
                                (isset($result_array['ROW']['_c']['COUNTRYDESCRIPTION']['_v']) ? 'Country: ' . $result_array['ROW']['_c']['COUNTRYDESCRIPTION']['_v'] : '') . '<BR>';

                        $this->form_submit_url = tep_href_link(FILENAME_CHECKOUT_PROCESS);
                        $this->form_submit_method = 'post';
                    }
                    break;
                default:
                    if (isset($result_array['ROW']['_c']) && count($result_array['ROW']['_c'])) {
                        $this->form_submit_url = $result_array['ROW']['_c']['FORMACTION']['_v'];
                        $this->form_submit_method = $result_array['ROW']['_c']['FORMMETHOD']['_v'];
                    }
                    break;
            }
        }

        if ($result_array['error']) {
            tep_redirect(tep_href_link(FILENAME_CHECKOUT_PAYMENT, 'error_message=' . urlencode($result_array['error_code']), 'SSL', true, false));
        }
        return $process_button_string;
    }

    function before_process() {
        global $HTTP_POST_VARS, $order_logged, $messageStack, $is_sc_checkout;

        if (!tep_session_is_registered('order_logged')) {
            return;
        }

        $return_array = $this->check_trans_status($order_logged);

        if (!$this->check_trans_status_flag) {
            $error_msg = MODULE_PAYMENT_GLOBAL_COLLECT_TEXT_ERROR_MESSAGE;
            if (isset($return_array['global_collect_status_id'])) {
                switch ($return_array['global_collect_status_id']) {
                    case '20':
                        $error_msg = MODULE_PAYMENT_GLOBAL_COLLECT_TEXT_ERROR_MESSAGE_20;
                        break;
                    case '25':
                        $error_msg = MODULE_PAYMENT_GLOBAL_COLLECT_TEXT_ERROR_MESSAGE_25;
                        break;
                    case '30':
                        $error_msg = MODULE_PAYMENT_GLOBAL_COLLECT_TEXT_ERROR_MESSAGE_30;
                        break;
                    case '50':
                        $error_msg = MODULE_PAYMENT_GLOBAL_COLLECT_TEXT_ERROR_MESSAGE_50;
                        break;
                    case '100':
                        $error_msg = MODULE_PAYMENT_GLOBAL_COLLECT_TEXT_ERROR_MESSAGE_100;
                        break;
                    case '160':
                        $error_msg = MODULE_PAYMENT_GLOBAL_COLLECT_TEXT_ERROR_MESSAGE_160;
                        ;
                        break;
                    case '180':
                        $error_msg = MODULE_PAYMENT_GLOBAL_COLLECT_TEXT_ERROR_MESSAGE_180;
                        break;
                }
            }

            if (isset($is_sc_checkout) && $is_sc_checkout) {
                tep_redirect(tep_href_link(FILENAME_SC_CHECKOUT_PAYMENT, 'error_message=' . urlencode($error_msg), 'SSL', true, false));
            } else {
                tep_redirect(tep_href_link(FILENAME_CHECKOUT_PAYMENT, 'error_message=' . urlencode($error_msg), 'SSL', true, false));
            }
        }
    }

    function after_process() {
        global $order_logged, $payment, $$payment, $currencies, $log_object, $memcache_obj, $order;

        if (!is_object($order) || !isset($order->info['date_purchased'])) {
            require_once(DIR_WS_CLASSES . 'order.php');
            $order = new order($order_logged);
        }

        $process_flag = false;
        $order_select_sql = "	SELECT o.orders_id, o.orders_status, gc.global_collect_status_id
								FROM " . TABLE_ORDERS . " AS o 
								INNER JOIN " . TABLE_GLOBAL_COLLECT . " AS gc 
									ON o.orders_id = gc.global_collect_orders_id
							 	WHERE o.orders_id = '" . $order_logged . "'";
        $order_result_sql = tep_db_query($order_select_sql);
        if ($order_row_sql = tep_db_fetch_array($order_result_sql)) {

            $cache_key = TABLE_GLOBAL_COLLECT . '/ipn/order_id/' . $order_logged . '/gc_status/' . $order_row_sql["global_collect_status_id"];
            $cache_result = $memcache_obj->fetch($cache_key);
            if ($cache_result !== FALSE) { // Not first attempt
                return;
            } else {
                $memcache_obj->store($cache_key, '1', 300);
            }

            switch ($this->payment) {
                case '1': // credit card
                    if ($order_row_sql["orders_status"] == 1 && $order_row_sql["global_collect_status_id"] >= 600) {
                        $process_flag = true;
                    }
                    break;
                default:
                    if ($order_row_sql["orders_status"] == 1 && $order_row_sql["global_collect_status_id"] >= 800) {
                        $process_flag = true;
                    }
                    break;
            }
        }

        if ($process_flag) {
            include_once(DIR_WS_FUNCTIONS . 'custom_product_info.php');
            include_once(DIR_WS_CLASSES . 'anti_fraud.php');
            require_once(DIR_WS_CLASSES . 'log.php');

            $log_object = new log_files('system');

            $cur_order = new order($order_logged);

            $customer_notified = (SEND_EMAILS == 'true') ? '1' : '0';

            $orders_status_history_perform_array = array('action' => 'insert',
                'data' => array('orders_id' => (int) $order_logged,
                    'orders_status_id' => $$payment->order_processing_status,
                    'date_added' => 'now()',
                    'changed_by' => 'system',
                    'customer_notified' => $customer_notified
                )
            );
            if ($$payment->order_processing_status != $order_row_sql["orders_status"] && $$payment->order_processing_status > 1) {
                $cur_order->update_order_status($$payment->order_processing_status, $orders_status_history_perform_array, true);
            }
            unset($orders_status_history_perform_array);

            $products_ordered = '';
            reset($cur_order->products);

            for ($i = 0; $i < count($cur_order->products); $i++) {
                $currencies->product_instance_id = tep_not_null($cur_order->products[$i]['custom_content']['hla_account_id']) ? $order->cur_order[$i]['custom_content']['hla_account_id'] : '';

                $cat_path = tep_output_generated_category_path($order->products[$i]['id'], 'product');
                $product_email_display_str = $cat_path . (tep_not_null($cat_path) ? ' > ' : '') . $order->products[$i]['name'] . (tep_not_null($order->products[$i]['model']) ? ' (' . $order->products[$i]['model'] . ')' : '');
                $products_ordered .= $cur_order->products[$i]['qty'] . ' x ' . $product_email_display_str . ' = ' . $currencies->display_price_nodiscount($cur_order->products[$i]['id'], $cur_order->products[$i]['final_price'], $cur_order->products[$i]['tax'], $cur_order->products[$i]['qty']) . "\n";
            }

            if (isset($$payment->prepend_order_comment) && tep_not_null($$payment->prepend_order_comment)) {
                $prepend_order_comment_update_sql = "	UPDATE " . TABLE_ORDERS_STATUS_HISTORY . "
														SET comments = CONCAT('" . tep_db_input($$payment->prepend_order_comment) . "', comments)
														WHERE orders_id = '" . $order_row_sql["orders_id"] . "'";
                tep_db_query($prepend_order_comment_update_sql);
            }

            // Send order update 
            tep_status_update_notification('C', $order_row_sql["orders_id"], 'system', $order_row_sql["orders_status"], $$payment->order_processing_status, 'A');

            // Send affiliate notification e-mail
            tep_send_affiliate_notification($order_logged, $cur_order);
        }
    }

    function link_to_payment($new_order_id) {
        global $order_logged;

        if (tep_session_is_registered('order_logged'))
            tep_session_unregister('order_logged');

        tep_session_register('order_logged');
        $order_logged = $new_order_id;
        require(DIR_WS_INCLUDES . 'modules/payment/global_collect/catalog/global_collect_splash.php');
        return;
    }

    function is_supported_currency($selected_currency) {
        return (in_array($selected_currency, $this->gcCurrencies) ? true : false);
    }

    function output_error() {
        return false;
    }

    function get_email_pm_info() {
        // display bank information return by GC
        switch ($this->payment) {
            case "4":
                if (isset($_SESSION['global_collect_success_msg']) && tep_not_null($_SESSION['global_collect_success_msg'])) {
                    $this->email_message = $_SESSION['global_collect_success_msg'];
                    unset($_SESSION['global_collect_success_msg']);
                }
                break;
        }
        return $this->email_message;
    }

    function check() {
        if (!isset($this->_check)) {
            $payment_methods_select_sql = "	SELECT payment_methods_id
											FROM " . TABLE_PAYMENT_METHODS . " as pm
											WHERE pm.payment_methods_id = '" . (int) $this->payment_methods_id . "'";
            $payment_methods_result_sql = tep_db_query($payment_methods_select_sql);
            $this->_check = tep_db_num_rows($payment_methods_result_sql);
        }
        return $this->_check;
    }

    function check_trans_status($order_id) {
        global $currencies;

        $this->check_trans_status_flag = false;

        $order_info_select_sql = "	SELECT o.payment_methods_id, o.currency, o.currency_value, ot.value, ot.text 
									FROM " . TABLE_ORDERS . " AS o 
									INNER JOIN " . TABLE_ORDERS_TOTAL . " AS ot 
										ON o.orders_id=ot.orders_id AND ot.class='ot_total' 
									WHERE o.orders_id = '" . tep_db_input($order_id) . "'";
        $order_info_result_sql = tep_db_query($order_info_select_sql);

        if ($order_info_row = tep_db_fetch_array($order_info_result_sql)) {
            if ($order_info_row['payment_methods_id'] != $this->payment_methods_id)
                return;

            $gcCurrencies = $order_info_row['currency'];

            $this->get_merchant_account($gcCurrencies);

            $submit_xml = "	<XML> 
								<REQUEST> 
									<ACTION>GET_ORDERSTATUS</ACTION> 
									<META> 
										<MERCHANTID>" . $this->merchantid . "</MERCHANTID>
										<IPADDRESS>" . $this->merchant_server_ip_address . "</IPADDRESS>
										<VERSION>" . $this->get_orders_status_version . "</VERSION>
									</META> 
									<PARAMS> 
										<ORDER> 
									        <ORDERID>" . $order_id . "</ORDERID>
										</ORDER> 
									</PARAMS> 
								</REQUEST>
							</XML>";
            $result_array = $this->curl_connect($this->customer_link_url, "IN=" . $submit_xml);

            $global_collect_status_history_data_sql = array('global_collect_orders_id' => $order_id,
                'global_collect_date' => 'now()',
                'global_collect_status' => (int) $result_array['STATUS']['_c']['STATUSID']['_v']
            );

            if (isset($result_array['STATUS']['_c']) || isset($result_array['ROW']['0']['_c'])) {
                if (isset($result_array['STATUS']['_c'])) {
                    $returned_xml_obj = $result_array['STATUS']['_c'];
                } else {
                    $returned_xml_obj = $result_array['STATUS']['0']['_c'];
                }

                $gc_return_amt = (isset($returned_xml_obj['AMOUNT']['_v']) ? $returned_xml_obj['AMOUNT']['_v'] : 0);
                $exponent = (int) (tep_not_null($this->exponent) ? $this->exponent : 2);
                if ($exponent > 0) {
                    $gc_return_amt = $gc_return_amt / pow(10, $exponent);
                }
                $global_collect_gross_amt = number_format($gc_return_amt, $currencies->currencies[$gcCurrencies]['decimal_places'], $currencies->currencies[$gcCurrencies]['decimal_point'], $currencies->currencies[$gcCurrencies]['thousands_point']); // compare in OGM format

                if (!isset($returned_xml_obj['MERCHANTID']['_v']) || $returned_xml_obj['MERCHANTID']['_v'] != $this->merchantid) {
                    $global_collect_status_history_data_sql['global_collect_description'] = 'Merchant ID not matched (' . $returned_xml_obj['MERCHANTID']['_v'] . '!=' . $this->merchantid . ')';
                } else if (!isset($returned_xml_obj['ORDERID']['_v']) || $returned_xml_obj['ORDERID']['_v'] != $order_id) {
                    $global_collect_status_history_data_sql['global_collect_description'] = 'Order Id not matched (' . $returned_xml_obj['ORDERID']['_v'] . ')';
                } else if ($currencies->currencies[(isset($returned_xml_obj['CURRENCYCODE']['_v']) ? $returned_xml_obj['CURRENCYCODE']['_v'] : '')]['symbol_left'] . $global_collect_gross_amt . $currencies->currencies[(isset($returned_xml_obj['CURRENCYCODE']['_v']) ? $returned_xml_obj['CURRENCYCODE']['_v'] : '')]['symbol_right'] != strip_tags($order_info_row['text'])) {
                    $global_collect_status_history_data_sql['global_collect_description'] = 'Amount not matched (' . $currencies->currencies[(isset($returned_xml_obj['CURRENCYCODE']['_v']) ? $returned_xml_obj['CURRENCYCODE']['_v'] : '')]['symbol_left'] . $global_collect_gross_amt . $currencies->currencies[(isset($returned_xml_obj['CURRENCYCODE']['_v']) ? $returned_xml_obj['CURRENCYCODE']['_v'] : '')]['symbol_right'] . ' != ' . strip_tags($order_info_row['text']) . ')';
                } else {
                    if (isset($returned_xml_obj['STATUSID']['_v']) && (int) $returned_xml_obj['STATUSID']['_v'] >= 600) {
                        $this->check_trans_status_flag = true;
                    }
                    $global_collect_status_history_data_sql['global_collect_description'] = 'Return Code - ' . (int) $returned_xml_obj['STATUSID']['_v'];
                }

                $global_collect_payment_data_array = array('global_collect_payment_reference' => tep_db_prepare_input($returned_xml_obj['PAYMENTREFERENCE']['_v']),
                    'global_collect_additional_reference' => tep_db_prepare_input($returned_xml_obj['MERCHANTREFERENCE']['_v']),
                    'global_collect_status_id' => tep_db_prepare_input($returned_xml_obj['STATUSID']['_v']),
                    'global_collect_status_date' => tep_db_prepare_input($returned_xml_obj['STATUSDATE']['_v']),
                    'global_collect_currency_code' => tep_db_prepare_input($returned_xml_obj['CURRENCYCODE']['_v']),
                    'global_collect_amount' => number_format($gc_return_amt, $currencies->currencies[$gcCurrencies]['decimal_places'], $currencies->currencies[$gcCurrencies]['decimal_point'], ''),
                    'global_collect_effortid' => tep_db_prepare_input($returned_xml_obj['EFFORTID']['_v']),
                    'global_collect_attemptid' => tep_db_prepare_input($returned_xml_obj['ATTEMPTID']['_v']),
                    'global_collect_paymentproductid' => tep_db_prepare_input($returned_xml_obj['PAYMENTPRODUCTID']['_v']),
                    'global_collect_paymentmethodid' => tep_db_prepare_input($returned_xml_obj['PAYMENTMETHODID']['_v']),
                    'global_collect_cvv_result' => tep_db_prepare_input($returned_xml_obj['CVVRESULT']['_v']),
                    'global_collect_avs_result' => tep_db_prepare_input($returned_xml_obj['AVSRESULT']['_v']),
                    'global_collect_fraud_result' => tep_db_prepare_input($returned_xml_obj['FRAUDRESULT']['_v']),
                    'global_collect_fraud_code' => tep_db_prepare_input($returned_xml_obj['FRAUDCODE']['_v']),
                    'global_collect_cvv_result' => tep_db_prepare_input((isset($returned_xml_obj['CVVRESULT']['_v']) ? $returned_xml_obj['CVVRESULT']['_v'] : '')),
                    'global_collect_cc_last_4_digit' => tep_db_prepare_input((isset($returned_xml_obj['CREDITCARDNUMBER']['_v']) ? preg_replace('/([^0-9])+/', '', $returned_xml_obj['CREDITCARDNUMBER']['_v']) : '')),
                    'global_collect_cc_expiry_date' => tep_db_prepare_input((isset($returned_xml_obj['EXPIRYDATE']['_v']) ? $returned_xml_obj['EXPIRYDATE']['_v'] : '')),
                    'global_collect_eci' => tep_db_prepare_input((isset($returned_xml_obj['ECI']['_v']) ? $returned_xml_obj['ECI']['_v'] : ''))
                );

                // check order exist
                $global_collect_check_exist_select_sql = "	SELECT global_collect_orders_id 
															FROM " . TABLE_GLOBAL_COLLECT . " 
															WHERE global_collect_orders_id = '" . (int) $order_id . "'";
                $global_collect_check_exist_result_sql = tep_db_query($global_collect_check_exist_select_sql);
                if (tep_db_num_rows($global_collect_check_exist_result_sql)) {
                    tep_db_perform(TABLE_GLOBAL_COLLECT, $global_collect_payment_data_array, 'update', ' global_collect_orders_id = "' . (int) $order_id . '" ');
                } else {
                    $global_collect_payment_data_array['global_collect_orders_id'] = (int) $order_id;
                    tep_db_perform(TABLE_GLOBAL_COLLECT, $global_collect_payment_data_array);
                }
            } else {
                $global_collect_status_history_data_sql['global_collect_description'] = 'unknown';
            }

            $global_collect_status_history_data_sql['changed_by'] = (isset($path_info_array["basename"]) && $path_info_array["basename"] == 'cron_order_payment.php' ? 'cronjob' : (isset($_SESSION['login_email_address']) ? $_SESSION['login_email_address'] : 'system' ) );
            tep_db_perform(TABLE_GLOBAL_COLLECT_STATUS_HISTORY, $global_collect_status_history_data_sql);
        }

        return $global_collect_payment_data_array;
    }

    function load_pm_setting($language_id = 1, $pm_id = '') {
        $pm_setting_array = array();
        //load value from DB

        if (tep_not_null($pm_id)) {
            $payment_method_id = $pm_id;

            $payment_methods_parent_id_select_sql = "	SELECT payment_methods_parent_id 
														FROM " . TABLE_PAYMENT_METHODS . " 
														WHERE payment_methods_id = '" . (int) $pm_id . "'";
            $payment_methods_parent_id_result_sql = tep_db_query($payment_methods_parent_id_select_sql);
            $payment_methods_parent_id_row = tep_db_fetch_array($payment_methods_parent_id_result_sql);

            $payment_gateway_id = $payment_methods_parent_id_row['payment_methods_parent_id'];
        } else {
            $payment_method_id = $this->payment_methods_id;
            $payment_gateway_id = $this->payment_methods_parent_id;
        }

        $payment_configuration_info_select_sql = "	SELECT pci.payment_configuration_info_key, pcid.payment_configuration_info_value
													FROM " . TABLE_PAYMENT_CONFIGURATION_INFO . " as pci
													LEFT JOIN " . TABLE_PAYMENT_CONFIGURATION_INFO_DESCRIPTION . " as pcid 
														ON pci.payment_configuration_info_id = pcid.payment_configuration_info_id
															AND pcid.languages_id = '" . (int) $language_id . "'
													WHERE pci.payment_methods_id = '" . (int) $payment_method_id . "'
													ORDER BY pci.payment_configuration_info_sort_order";
        $payment_configuration_info_result_sql = tep_db_query($payment_configuration_info_select_sql);
        if (tep_db_num_rows($payment_configuration_info_result_sql)) {
            while ($payment_configuration_info_row = tep_db_fetch_array($payment_configuration_info_result_sql)) {
                $pm_setting_array[$payment_configuration_info_row['payment_configuration_info_key']] = $payment_configuration_info_row['payment_configuration_info_value'];
            }
        } else if ((int) $payment_gateway_id > 0) { //load value from PG
            $payment_configuration_info_select_sql = "	SELECT pci.payment_configuration_info_key, pcid.payment_configuration_info_value
														FROM " . TABLE_PAYMENT_CONFIGURATION_INFO . " as pci
														LEFT JOIN " . TABLE_PAYMENT_CONFIGURATION_INFO_DESCRIPTION . " as pcid 
															ON pci.payment_configuration_info_id = pcid.payment_configuration_info_id
																AND pcid.languages_id = '" . (int) $language_id . "'
														WHERE pci.payment_methods_id = '" . (int) $payment_gateway_id . "'
														ORDER BY pci.payment_configuration_info_sort_order";
            $payment_configuration_info_result_sql = tep_db_query($payment_configuration_info_select_sql);
            while ($payment_configuration_info_row = tep_db_fetch_array($payment_configuration_info_result_sql)) {
                $pm_setting_array[$payment_configuration_info_row['payment_configuration_info_key']] = $payment_configuration_info_row['payment_configuration_info_value'];
            }
        }

        return $pm_setting_array;
    }

    function get_merchant_account($selected_currency) {
        $payment_methods_instance_id = 0;
        $payment_gateway_instance_select_sql = "SELECT pmi.payment_methods_instance_id, pmi.payment_methods_instance_follow_default, pmi.currency_code 
				 								FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " as pmi
				 								WHERE pmi.payment_methods_id = '" . (int) $this->payment_methods_id . "'
				 								LIMIT 1";
        $payment_gateway_instance_result_sql = tep_db_query($payment_gateway_instance_select_sql);
        if (tep_db_num_rows($payment_gateway_instance_result_sql)) {
            $payment_gateway_instance_select_sql = "SELECT pmi.payment_methods_instance_id, pmi.payment_methods_instance_follow_default 
					 								FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " as pmi
					 								WHERE pmi.currency_code = '" . tep_db_input($selected_currency) . "' 
					 									AND pmi.payment_methods_id = '" . (int) $this->payment_methods_id . "'";
            $payment_gateway_instance_result_sql = tep_db_query($payment_gateway_instance_select_sql);
            if ($payment_gateway_instance_row = tep_db_fetch_array($payment_gateway_instance_result_sql)) {
                if ((int) $payment_gateway_instance_row['payment_methods_instance_follow_default'] > 0) { // Follow Default Currency Setting
                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_follow_default'];
                } else { // Has own setting
                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_id'];
                }
            }
        } else {
            $payment_gateway_instance_select_sql = "SELECT pmi.payment_methods_instance_id, pmi.payment_methods_instance_follow_default 
					 								FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " as pmi
					 								INNER JOIN " . TABLE_PAYMENT_METHODS . " as pm
					 									ON pm.payment_methods_parent_id = pmi.payment_methods_id
					 								WHERE pmi.currency_code = '" . tep_db_input($selected_currency) . "' 
					 									AND pm.payment_methods_id = '" . (int) $this->payment_methods_id . "'";
            $payment_gateway_instance_result_sql = tep_db_query($payment_gateway_instance_select_sql);
            if ($payment_gateway_instance_row = tep_db_fetch_array($payment_gateway_instance_result_sql)) {
                if ((int) $payment_gateway_instance_row['payment_methods_instance_follow_default'] > 0) {
                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_follow_default'];
                } else {
                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_id'];
                }
            }
        }
        $payment_gateway_instance_setting_select_sql = "SELECT pmis.payment_methods_instance_setting_key, pmis.payment_methods_instance_setting_value 
						 								FROM " . TABLE_PAYMENT_METHODS_INSTANCE_SETTING . " as pmis
						 								WHERE pmis.payment_methods_instance_id = '" . (int) $payment_methods_instance_id . "'";
        $payment_gateway_instance_setting_result_sql = tep_db_query($payment_gateway_instance_setting_select_sql);
        while ($payment_gateway_instance_setting_row = tep_db_fetch_array($payment_gateway_instance_setting_result_sql)) {
            switch ($payment_gateway_instance_setting_row['payment_methods_instance_setting_key']) {
                case 'MODULE_PAYMENT_GLOBAL_COLLECT_MERCHANTID':
                    $this->merchantid = $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];
                    break;
                case 'MODULE_PAYMENT_GLOBAL_COLLECT_EXPONENT':
                    $this->exponent = $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];
                    break;
            }
        }
    }

    function post_authorisation($oID) {
        require_once(DIR_WS_CLASSES . 'ogm_xml_to_ary.php');
        $globalresult = array();

        $order_info_select_sql = "	SELECT o.currency, gc.global_collect_capture_request 
									FROM " . TABLE_ORDERS . " AS o 
									INNER JOIN " . TABLE_GLOBAL_COLLECT . " AS gc 
										ON gc.global_collect_orders_id = o.orders_id
									WHERE o.orders_id = '" . tep_db_input($oID) . "'";
        $order_info_result_sql = tep_db_query($order_info_select_sql);
        $order_info_row = tep_db_fetch_array($order_info_result_sql);

        if ($order_info_row["global_collect_capture_request"] == '0') {
            $this->get_merchant_account($order_info_row['currency']);

            $submit_xml = "	<XML>
								<REQUEST>
									<ACTION>SET_PAYMENT</ACTION> 
									<META>
										<MERCHANTID>" . $this->merchantid . "</MERCHANTID>
										<IPADDRESS>" . $this->merchant_server_ip_address . "</IPADDRESS>
										<VERSION>1.0</VERSION>
									</META>
									<PARAMS>
										<PAYMENT>
									        <ORDERID>" . $oID . "</ORDERID>
									        <EFFORTID>1</EFFORTID>
									        <PAYMENTPRODUCTID>" . $this->code . "</PAYMENTPRODUCTID>
										</PAYMENT>
									</PARAMS>
								</REQUEST>
							</XML>";

            $ch = curl_init($url);
            $agent = "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.0)";
            curl_setopt($ch, CURLOPT_URL, $this->customer_link_url);
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $submit_xml);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($ch, CURLOPT_NOPROGRESS, 0);
            curl_setopt($ch, CURLOPT_VERBOSE, 1);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);  //Windows 2003 Compatibility
            curl_setopt($ch, CURLOPT_TIMEOUT, 120);
            if ($this->connect_via_proxy) {
                curl_setopt($ch, CURLOPT_PROXY, 'http://*************:3128');
                curl_setopt($ch, CURLOPT_PROXYUSERPWD, 'devbackend:devonly');
            }
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);  //Windows 2003 Compatibility
            $response = curl_exec($ch);
            curl_close($ch);

            $this->xml_array_obj = new ogm_xml_to_ary($response, 'content');
            $this->xml_array = $this->xml_array_obj->get_ogm_xml_to_ary();

            if (isset($this->xml_array['XML']['_c']['REQUEST']['_c']['RESPONSE']['_c']['RESULT']['_v'])) {
                $global_collect_status_history_data_sql = array('global_collect_orders_id' => $oID,
                    'global_collect_date' => 'now()',
                    'global_collect_status' => '');

                if ($this->xml_array['XML']['_c']['REQUEST']['_c']['RESPONSE']['_c']['RESULT']['_v'] == 'OK') {
                    $globalresult['RESULT'] = 'OK';
                    $globalresult['RESPONSEDATETIME'] = $this->xml_array['XML']['_c']['REQUEST']['_c']['RESPONSE']['_c']['META']['_c']['RESPONSEDATETIME']['_v'];
                    $globalresult['REQUESTID'] = $this->xml_array['XML']['_c']['REQUEST']['_c']['RESPONSE']['_c']['META']['_c']['REQUESTID']['_v'];
                    $global_collect_status_history_data_sql['global_collect_description'] = 'Set Payment Sent, Request ID: ' . $globalresult['REQUESTID'];
                    $global_collect_status_history_data_sql['changed_by'] = (isset($path_info_array["basename"]) && $path_info_array["basename"] == 'cron_order_payment.php' ? 'cronjob' : (isset($_SESSION['login_email_address']) ? $_SESSION['login_email_address'] : 'system' ) );
                    tep_db_perform(TABLE_GLOBAL_COLLECT_STATUS_HISTORY, $global_collect_status_history_data_sql);

                    // Need to update GC actual status else Processing button will not shown.
                    $global_collect_payment_data_array = array('global_collect_status_id' => '800');
                    tep_db_perform(TABLE_GLOBAL_COLLECT, $global_collect_payment_data_array, 'update', ' global_collect_orders_id = "' . (int) $oID . '" ');

                    $global_collect_status_history_data_sql = array('global_collect_orders_id' => $oID,
                        'global_collect_date' => 'now()',
                        'global_collect_status' => '800',
                        'global_collect_description' => 'Return Code - 800',
                        'changed_by' => 'system');
                    tep_db_perform(TABLE_GLOBAL_COLLECT_STATUS_HISTORY, $global_collect_status_history_data_sql);
                } else {
                    $globalresult['RESULT'] = 'NOK';
                    $globalresult['RESPONSEDATETIME'] = $this->xml_array['XML']['_c']['REQUEST']['_c']['RESPONSE']['_c']['META']['_c']['RESPONSEDATETIME']['_v'];
                    $globalresult['REQUESTID'] = $this->xml_array['XML']['_c']['REQUEST']['_c']['RESPONSE']['_c']['META']['_c']['REQUESTID']['_v'];
                    $globalresult['ERRORCODE'] = (isset($this->xml_array['XML']['_c']['REQUEST']['_c']['RESPONSE']['_c']['ERROR']['_c']['CODE']['_v']) ? $this->xml_array['XML']['_c']['REQUEST']['_c']['RESPONSE']['_c']['ERROR']['_c']['CODE']['_v'] : 'Null');
                    $globalresult['ERRORMESSAGE'] = (isset($this->xml_array['XML']['_c']['REQUEST']['_c']['RESPONSE']['_c']['ERROR']['_c']['MESSAGE']['_v']) ? $this->xml_array['XML']['_c']['REQUEST']['_c']['RESPONSE']['_c']['ERROR']['_c']['MESSAGE']['_v'] : 'Null');
                    $global_collect_status_history_data_sql['global_collect_description'] = "	Set Payment Failed, <br>
																								Request ID: " . $globalresult['REQUESTID'] . "<br>
																								Error Code: " . $globalresult['ERRORCODE'] . "<br>
																								Error Message: " . $globalresult['ERRORMESSAGE'];
                    $global_collect_status_history_data_sql['changed_by'] = (isset($path_info_array["basename"]) && $path_info_array["basename"] == 'cron_order_payment.php' ? 'cronjob' : (isset($_SESSION['login_email_address']) ? $_SESSION['login_email_address'] : 'system' ) );
                    tep_db_perform(TABLE_GLOBAL_COLLECT_STATUS_HISTORY, $global_collect_status_history_data_sql);
                }
            }
        }
        return $globalresult;
    }

    function do_refund($oID, $reference_id, $refund_amount = '') {
        global $currencies;

        require_once(DIR_WS_CLASSES . 'ogm_xml_to_ary.php');
        $global_result = array();

        //, gc.global_collect_refund_request // temporary remove this first.
        $order_info_select_sql = "	SELECT o.currency, gc.global_collect_status_id, 
										sr.store_refund_is_processed, sr.store_refund_amount, sr.store_refund_id, 
										o.currency, o.currency_value 
									FROM " . TABLE_ORDERS . " AS o 
									INNER JOIN " . TABLE_STORE_REFUND . " AS sr 
										ON sr.store_refund_trans_id = o.orders_id 
									INNER JOIN " . TABLE_GLOBAL_COLLECT . " AS gc 
										ON gc.global_collect_orders_id = o.orders_id
									WHERE o.orders_id = '" . tep_db_input($oID) . "' 
										AND sr.store_refund_id = '" . tep_db_input($reference_id) . "'";
        $order_info_result_sql = tep_db_query($order_info_select_sql);
        $order_info_row = tep_db_fetch_array($order_info_result_sql);

        if ($order_info_row["store_refund_is_processed"] == '0' && $order_info_row["global_collect_status_id"] >= 900) {
            $this->get_merchant_account($order_info_row['currency']);

            $submit_xml = "	<XML> 
								<REQUEST> 
									<ACTION>DO_REFUND</ACTION> 
									<META> 
										<MERCHANTID>" . $this->merchantid . "</MERCHANTID>
										<IPADDRESS>" . $this->merchant_server_ip_address . "</IPADDRESS>
										<VERSION>1.0</VERSION>
									</META> 
									<PARAMS> 
										<PAYMENT> 
									        <ORDERID>" . $oID . "</ORDERID>
									        <MERCHANTREFERENCE>" . $reference_id . "</MERCHANTREFERENCE>";
            if ($refund_amount != '') {
                $submit_xml .= "			<AMOUNT>" . preg_replace('/[^\d]/', '', $refund_amount) . "</AMOUNT>";
            } else {
                $submit_xml .= "			<AMOUNT>" . preg_replace('/[^\d]/', '', number_format($currencies->apply_currency_exchange($order_info_row['store_refund_amount'], $order_info_row['currency'], $order_info_row['currency_value']), 2)) . "</AMOUNT>";
            }
            $submit_xml .= "				<CURRENCYCODE>" . $order_info_row['currency'] . "</CURRENCYCODE>
										</PAYMENT> 
									</PARAMS> 
								</REQUEST>
							</XML>";

            $ch = curl_init($url);
            $agent = "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.0)";
            curl_setopt($ch, CURLOPT_URL, $this->customer_link_url);
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $submit_xml);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($ch, CURLOPT_NOPROGRESS, 0);
            curl_setopt($ch, CURLOPT_VERBOSE, 1);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);  //Windows 2003 Compatibility
            curl_setopt($ch, CURLOPT_TIMEOUT, 120);
            if ($this->connect_via_proxy) {
                curl_setopt($ch, CURLOPT_PROXY, 'http://*************:3128');
                curl_setopt($ch, CURLOPT_PROXYUSERPWD, 'devbackend:devonly');
            }
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);  //Windows 2003 Compatibility
            $response = curl_exec($ch);
            curl_close($ch);

            $this->xml_array_obj = new ogm_xml_to_ary($response, 'content');
            $this->xml_array = $this->xml_array_obj->get_ogm_xml_to_ary();

            $global_result['REQUESTID'] = (isset($this->xml_array['XML']['_c']['REQUEST']['_c']['RESPONSE']['_c']['META']['_c']['REQUESTID']['_v']) ? $this->xml_array['XML']['_c']['REQUEST']['_c']['RESPONSE']['_c']['META']['_c']['REQUESTID']['_v'] : '');

            if (strtoupper($this->xml_array['XML']['_c']['REQUEST']['_c']['RESPONSE']['_c']['RESULT']['_v']) == 'OK') {
                $global_result['RESULT'] = 'OK';

                $store_refund_data_sql = array('store_refund_is_processed' => 1);
                tep_db_perform(TABLE_STORE_REFUND, $store_refund_data_sql, 'update', " store_refund_id = '" . tep_db_input($reference_id) . "' ");
            } else {
                $global_result['RESULT'] = 'NOK';

                if (isset($this->xml_array['XML']['_c']['REQUEST']['_c']['RESPONSE']['_c']['ERROR']['_c']['CODE']['_v']))
                    $global_result['CODE'] = $this->xml_array['XML']['_c']['REQUEST']['_c']['RESPONSE']['_c']['ERROR']['_c']['CODE']['_v'];
                if (isset($this->xml_array['XML']['_c']['REQUEST']['_c']['RESPONSE']['_c']['ERROR']['_c']['MESSAGE']['_v']))
                    $global_result['MESSAGE'] = $this->xml_array['XML']['_c']['REQUEST']['_c']['RESPONSE']['_c']['ERROR']['_c']['MESSAGE']['_v'];
            }
        }
        return $global_result;
    }

    function do_cancel_set_payment($oID) {
        require_once(DIR_WS_CLASSES . 'ogm_xml_to_ary.php');
        $global_result = array('RESULT' => 'NOK');

        $order_info_select_sql = "	SELECT o.currency 
									FROM " . TABLE_ORDERS . " AS o 
									WHERE o.orders_id = '" . tep_db_input($oID) . "'";
        $order_info_result_sql = tep_db_query($order_info_select_sql);
        $order_info_row = tep_db_fetch_array($order_info_result_sql);

        $this->get_merchant_account($order_info_row['currency']);

        $submit_xml = "	<XML>
							<REQUEST>
								<ACTION>CANCEL_SET_PAYMENT</ACTION>
								<META>
									<MERCHANTID>" . $this->merchantid . "</MERCHANTID>
									<IPADDRESS>" . $this->merchant_server_ip_address . "</IPADDRESS>
									<VERSION>1.0</VERSION>
								</META>
								<PARAMS>
									<PAYMENT>
								        <ORDERID>" . $oID . "</ORDERID>
									</PAYMENT>
								</PARAMS>
							</REQUEST>
						</XML>";

        $ch = curl_init($url);
        $agent = "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.0)";
        curl_setopt($ch, CURLOPT_URL, $this->customer_link_url);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $submit_xml);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_NOPROGRESS, 0);
        curl_setopt($ch, CURLOPT_VERBOSE, 1);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);  //Windows 2003 Compatibility
        curl_setopt($ch, CURLOPT_TIMEOUT, 120);
        if ($this->connect_via_proxy) {
            curl_setopt($ch, CURLOPT_PROXY, 'http://*************:3128');
            curl_setopt($ch, CURLOPT_PROXYUSERPWD, 'devbackend:devonly');
        }
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);  //Windows 2003 Compatibility
        $response = curl_exec($ch);
        curl_close($ch);
        $this->log_msg(htmlentities($response), 'GC curl_connect');

        $this->xml_array_obj = new ogm_xml_to_ary($response, 'content');
        $this->xml_array = $this->xml_array_obj->get_ogm_xml_to_ary();

        if (strtoupper($this->xml_array['XML']['_c']['REQUEST']['_c']['RESPONSE']['_c']['RESULT']['_v']) == 'OK') {
            $global_result['RESULT'] = 'OK';
            $global_collect_status_history_data_sql = array();
            $global_collect_status_history_data_sql['global_collect_orders_id'] = (int) $oID;
            $global_collect_status_history_data_sql['global_collect_date'] = 'now()';
            $global_collect_status_history_data_sql['global_collect_description'] = tep_db_prepare_input('Cancel Set Payment Sent, Request ID: ' . $this->xml_array['XML']['_c']['REQUEST']['_c']['RESPONSE']['_c']['META']['_c']['REQUESTID']['_v']);
            $global_collect_status_history_data_sql['changed_by'] = (isset($_SESSION['login_email_address']) ? $_SESSION['login_email_address'] : 'system' );
            tep_db_perform(TABLE_GLOBAL_COLLECT_STATUS_HISTORY, $global_collect_status_history_data_sql);
        }

        return $global_result;
    }

    function do_cancel_payment($oID) {
        require_once(DIR_WS_CLASSES . 'ogm_xml_to_ary.php');
        $global_result = array('RESULT' => 'NOK');

        $order_info_select_sql = "	SELECT o.currency 
									FROM " . TABLE_ORDERS . " AS o 
									WHERE o.orders_id = '" . tep_db_input($oID) . "'";
        $order_info_result_sql = tep_db_query($order_info_select_sql);
        $order_info_row = tep_db_fetch_array($order_info_result_sql);

        $this->get_merchant_account($order_info_row['currency']);

        /* $effordid_select_sql = "SELECT store_refund_trans_id
          FROM " . TABLE_STORE_REFUND . "
          WHERE store_refund_trans_id = '" . tep_db_input($oID) . "'";
          $effordid_result_sql = tep_db_query($effordid_select_sql);
          $attempt_id = tep_db_num_rows($effordid_result_sql);
          $efford_id = ($attempt_id+1); */

        $submit_xml = "	<XML>
							<REQUEST>
								<ACTION>CANCEL_PAYMENT</ACTION>
								<META>
									<MERCHANTID>" . $this->merchantid . "</MERCHANTID>
									<IPADDRESS>" . $this->merchant_server_ip_address . "</IPADDRESS>
									<VERSION>1.0</VERSION>
								</META>
								<PARAMS>
									<PAYMENT>
								        <ORDERID>" . $oID . "</ORDERID>
								        <EFFORTID>1</EFFORTID>
								        <ATTEMPTID>1</ATTEMPTID>
									</PAYMENT>
								</PARAMS>
							</REQUEST>
						</XML>";

        $ch = curl_init($url);
        $agent = "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.0)";
        curl_setopt($ch, CURLOPT_URL, $this->customer_link_url);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $submit_xml);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_NOPROGRESS, 0);
        curl_setopt($ch, CURLOPT_VERBOSE, 1);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);  //Windows 2003 Compatibility
        curl_setopt($ch, CURLOPT_TIMEOUT, 120);
        if ($this->connect_via_proxy) {
            curl_setopt($ch, CURLOPT_PROXY, 'http://*************:3128');
            curl_setopt($ch, CURLOPT_PROXYUSERPWD, 'devbackend:devonly');
        }
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);  //Windows 2003 Compatibility
        $response = curl_exec($ch);
        curl_close($ch);

        $this->xml_array_obj = new ogm_xml_to_ary($response, 'content');
        $this->xml_array = $this->xml_array_obj->get_ogm_xml_to_ary();

        if (strtoupper($this->xml_array['XML']['_c']['REQUEST']['_c']['RESPONSE']['_c']['RESULT']['_v']) == 'OK') {
            $global_result['RESULT'] = 'OK';
            $global_result['REQUESTID'] = (isset($this->xml_array['XML']['_c']['REQUEST']['_c']['RESPONSE']['_c']['META']['_c']['REQUESTID']['_v']) ? $this->xml_array['XML']['_c']['REQUEST']['_c']['RESPONSE']['_c']['META']['_c']['REQUESTID']['_v'] : '');

            $global_collect_status_history_data_sql = array();
            $global_collect_status_history_data_sql['global_collect_orders_id'] = (int) $oID;
            $global_collect_status_history_data_sql['global_collect_date'] = 'now()';
            $global_collect_status_history_data_sql['global_collect_description'] = tep_db_prepare_input('Cancel Payment Sent, Request ID: ' . $this->xml_array['XML']['_c']['REQUEST']['_c']['RESPONSE']['_c']['META']['_c']['REQUESTID']['_v']);
            $global_collect_status_history_data_sql['changed_by'] = (isset($_SESSION['login_email_address']) ? $_SESSION['login_email_address'] : 'system' );
            tep_db_perform(TABLE_GLOBAL_COLLECT_STATUS_HISTORY, $global_collect_status_history_data_sql);
        } else {
            $global_result['RESULT'] = 'NOK';
            $global_result['MESSAGE'] = (isset($this->xml_array['XML']['_c']['REQUEST']['_c']['RESPONSE']['_c']['ERROR']['_c']['MESSAGE']['_v']) ? $this->xml_array['XML']['_c']['REQUEST']['_c']['RESPONSE']['_c']['ERROR']['_c']['MESSAGE']['_v'] : '');
            $global_result['CODE'] = (isset($this->xml_array['XML']['_c']['REQUEST']['_c']['RESPONSE']['_c']['ERROR']['_c']['CODE']['_v']) ? $this->xml_array['XML']['_c']['REQUEST']['_c']['RESPONSE']['_c']['ERROR']['_c']['CODE']['_v'] : '');

            $global_collect_status_history_data_sql = array();
            $global_collect_status_history_data_sql['global_collect_orders_id'] = (int) $oID;
            $global_collect_status_history_data_sql['global_collect_date'] = 'now()';
            $global_collect_status_history_data_sql['global_collect_description'] = tep_db_prepare_input('Cancel Payment Sent failed, ' . $global_result['MESSAGE']);
            $global_collect_status_history_data_sql['changed_by'] = (isset($_SESSION['login_email_address']) ? $_SESSION['login_email_address'] : 'system' );
            tep_db_perform(TABLE_GLOBAL_COLLECT_STATUS_HISTORY, $global_collect_status_history_data_sql);

            $this->check_trans_status($oID);
        }

        return $global_result;
    }

    function install() {
        $install_configuration_flag = true;

        if ($this->payment_methods_id > 0) {
            if ($this->receive_status != 1) {
                $update_receive_status_sql_data = array("payment_methods_receive_status" => 1);
                tep_db_perform(TABLE_PAYMENT_METHODS, $update_receive_status_sql_data, 'update', "payment_methods_id = '" . (int) $this->payment_methods_id . "'");
            } else {
                $install_configuration_flag = false;
            }
        } else {
            $install_payment_gateway_data_sql = array('payment_methods_code' => $this->code,
                'payment_methods_send_status' => 0,
                'payment_methods_title' => $this->code,
                'payment_methods_types_id' => 0,
                'payment_methods_parent_id' => 0,
                'payment_methods_legend_color' => '#6E920D',
                'payment_methods_sort_order' => 6,
                'payment_methods_filename' => $this->filename,
                'payment_methods_logo' => '',
                'date_added' => 'now()',
                'last_modified' => 'now()',
                'payment_methods_receive_status' => 1,
                'payment_methods_receive_status_mode' => 0);
            tep_db_perform(TABLE_PAYMENT_METHODS, $install_payment_gateway_data_sql);
            $this->payment_methods_id = tep_db_insert_id();
        }

        if ($install_configuration_flag) {
            $install_key_array = array();
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Set Order Status',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_GLOBAL_COLLECT_ORDER_STATUS_ID',
                    'payment_configuration_info_description' => 'Set the status of orders made with this payment module to this value',
                    'payment_configuration_info_sort_order' => '100',
                    'set_function' => 'tep_cfg_pull_down_order_statuses(',
                    'use_function' => 'tep_get_order_status_name',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => '1',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Set Order\'s "Processing" Status',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_GLOBAL_COLLECT_PROCESSING_STATUS_ID',
                    'payment_configuration_info_description' => 'Set the initial processing status of orders made with this payment module to this value',
                    'payment_configuration_info_sort_order' => '200',
                    'set_function' => 'tep_cfg_pull_down_order_statuses(',
                    'use_function' => 'tep_get_order_status_name',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => '7',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Payment Message',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_GLOBAL_COLLECT_MESSAGE',
                    'payment_configuration_info_description' => 'Payment message will show up during checkout process (Global Collect)',
                    'payment_configuration_info_sort_order' => '300',
                    'set_function' => 'tep_cfg_textarea(',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'Thank you for shopping at OffGamers.com.',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Payment Email Message',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_GLOBAL_COLLECT_EMAIL_MESSAGE',
                    'payment_configuration_info_description' => 'This message will show up in order confirmation e-mail.',
                    'payment_configuration_info_sort_order' => '400',
                    'set_function' => 'tep_cfg_textarea(',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'Thank you for shopping at OffGamers.com.',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Confirm Complete (in days)',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_GLOBAL_COLLECT_CONFIRM_COMPLETE',
                    'payment_configuration_info_description' => 'The number of days in which an order made by this payment method is confirm completed',
                    'payment_configuration_info_sort_order' => '500',
                    'set_function' => 'NULL',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => '0',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Test Mode',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_GLOBAL_COLLECT_TEST_MODE',
                    'payment_configuration_info_description' => 'Testing Mode?',
                    'payment_configuration_info_sort_order' => '100',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Payment',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_GLOBAL_COLLECT_PAYMENT',
                    'payment_configuration_info_description' => 'Payment details',
                    'payment_configuration_info_sort_order' => '1440',
                    'set_function' => 'tep_cfg_key_select_option(array(\'1\'=>\'Credit Card\',\'4\'=>\'Bank Transfer/Online Bank Transfer\',\'8\'=>\'Real-time Bank Transfer\',\'3\'=>\'Direct Debit\',\'14\'=>\'Vouchers\'),',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => '4',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Require Address Information',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_GLOBAL_COLLECT_MANDATORY_ADDRESS_FIELD',
                    'payment_configuration_info_description' => 'Set address field as required info during checkout.',
                    'payment_configuration_info_sort_order' => '1400',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Customer Payment Info',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_GLOBAL_COLLECT_CUSTOMER_PAYMENT_INFO',
                    'payment_configuration_info_description' => 'Set to true if this Payment Method required customer input for Payment Information required customer payment info.',
                    'payment_configuration_info_sort_order' => '1410',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Require IC Information',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_GLOBAL_COLLECT_MANDATORY_IC_FIELD',
                    'payment_configuration_info_description' => 'Set IC field as required info during checkout.',
                    'payment_configuration_info_sort_order' => '1420',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Require Contact Information',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_GLOBAL_COLLECT_MANDATORY_CONTACT_FIELD',
                    'payment_configuration_info_description' => 'Set Contact field as required info during checkout.',
                    'payment_configuration_info_sort_order' => '1430',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => $pm_row['payment_methods_id'],
                    'payment_configuration_info_title' => 'Require Surname Information',
                    'payment_configuration_info_key' => "MODULE_PAYMENT_GLOBAL_COLLECT_MANDATORY_SURNAME",
                    'payment_configuration_info_description' => 'Set Surname field as required info during checkout.',
                    'payment_configuration_info_sort_order' => '50000',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );

            $install_key_array[] = array('info' => array('payment_methods_id' => $pm_row['payment_methods_id'],
                    'payment_configuration_info_title' => 'Require House Number Information',
                    'payment_configuration_info_key' => "MODULE_PAYMENT_GLOBAL_COLLECT_MANDATORY_HOUSENUMBER",
                    'payment_configuration_info_description' => 'Set House Number field as required info during checkout.',
                    'payment_configuration_info_sort_order' => '50000',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => $pm_row['payment_methods_id'],
                    'payment_configuration_info_title' => 'Require Street Information',
                    'payment_configuration_info_key' => "MODULE_PAYMENT_GLOBAL_COLLECT_MANDATORY_HOUSENUMBER",
                    'payment_configuration_info_description' => 'Set Street field as required info during checkout.',
                    'payment_configuration_info_sort_order' => '50000',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => $pm_row['payment_methods_id'],
                    'payment_configuration_info_title' => 'Require Zip Information',
                    'payment_configuration_info_key' => "MODULE_PAYMENT_GLOBAL_COLLECT_MANDATORY_CITY",
                    'payment_configuration_info_description' => 'Set Zip field as required info during checkout.',
                    'payment_configuration_info_sort_order' => '50000',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => $pm_row['payment_methods_id'],
                    'payment_configuration_info_title' => 'Require Account Name Information',
                    'payment_configuration_info_key' => "MODULE_PAYMENT_GLOBAL_COLLECT_MANDATORY_ACCOUNTNAME",
                    'payment_configuration_info_description' => 'Set Account Name field as required info during checkout.',
                    'payment_configuration_info_sort_order' => '50000',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => $pm_row['payment_methods_id'],
                    'payment_configuration_info_title' => 'Require Account Number Information',
                    'payment_configuration_info_key' => "MODULE_PAYMENT_GLOBAL_COLLECT_MANDATORY_ACCOUNTNUMBER",
                    'payment_configuration_info_description' => 'Set Account Numb field as required info during checkout.',
                    'payment_configuration_info_sort_order' => '50000',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => $pm_row['payment_methods_id'],
                    'payment_configuration_info_title' => 'Require Direct Debit Text Information',
                    'payment_configuration_info_key' => "MODULE_PAYMENT_GLOBAL_COLLECT_MANDATORY_DIRECTDEBITTEXT",
                    'payment_configuration_info_description' => 'Set Direct Debit Text field as required info during checkout.',
                    'payment_configuration_info_sort_order' => '50000',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => $pm_row['payment_methods_id'],
                    'payment_configuration_info_title' => 'Require Voucher Number',
                    'payment_configuration_info_key' => "MODULE_PAYMENT_GLOBAL_COLLECT_MANDATORY_VOUCHERNUMBER",
                    'payment_configuration_info_description' => 'Set Require Voucher Number Text field as required info during checkout.',
                    'payment_configuration_info_sort_order' => '50000',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => $pm_row['payment_methods_id'],
                    'payment_configuration_info_title' => 'Require Voucher Amount',
                    'payment_configuration_info_key' => "MODULE_PAYMENT_GLOBAL_COLLECT_MANDATORY_VOUCHERVALUE",
                    'payment_configuration_info_description' => 'Set Require Voucher Amount Text field as required info during checkout.',
                    'payment_configuration_info_sort_order' => '50000',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Merchant Server IP Address',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_GLOBAL_COLLECT_SERVER_IP',
                    'payment_configuration_info_description' => 'Merchant Server IP Address pass over to Global Collect as reference.',
                    'payment_configuration_info_sort_order' => '500',
                    'set_function' => 'NULL',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => '*************',
                    'languages_id' => 1
                )
            );
            foreach ($install_key_array as $data) {
                tep_db_perform(TABLE_PAYMENT_CONFIGURATION_INFO, $data['info']);
                $payment_conf_id = tep_db_insert_id();

                $data['desc']['payment_configuration_info_id'] = (int) $payment_conf_id;
                tep_db_perform(TABLE_PAYMENT_CONFIGURATION_INFO_DESCRIPTION, $data['desc']);
            }
        }
        return 1;
    }

    function curl_connect($url, $data) {

        require_once(DIR_WS_CLASSES . 'ogm_xml_to_ary.php');

        $ch = curl_init($url);
        $agent = "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.0)";
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_NOPROGRESS, 0);
        curl_setopt($ch, CURLOPT_VERBOSE, 1);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);  //Windows 2003 Compatibility
        curl_setopt($ch, CURLOPT_TIMEOUT, 120);
        if ($this->connect_via_proxy) {
            curl_setopt($ch, CURLOPT_PROXY, 'http://*************:3128');
            curl_setopt($ch, CURLOPT_PROXYUSERPWD, 'devbackend:devonly');
        }

        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);  //Windows 2003 Compatibility
        $response = curl_exec($ch);
        curl_close($ch);
        $this->log_msg(htmlentities($response), 'GC curl_connect');

        $this->xml_array_obj = new ogm_xml_to_ary($response, 'content');
        $this->xml_array = $this->xml_array_obj->get_ogm_xml_to_ary();

        $globalresult = array();
        $globalresult["error"] = 0;

        if (isset($this->xml_array['XML']['_c']['REQUEST']['_c']['RESPONSE']['_c']['ERROR'])) {
            $globalresult = $this->xml_array['XML']['_c']['REQUEST']['_c']['RESPONSE'];
            $globalresult["error"] = 1;
        } else {
            return $this->xml_array['XML']['_c']['REQUEST']['_c']['RESPONSE']['_c'];
        }
        return $globalresult;
    }

    function keys() {
        return array();
    }

    function configuration_information_keys() {
        return array('MODULE_PAYMENT_GLOBAL_COLLECT_ORDER_STATUS_ID',
            'MODULE_PAYMENT_GLOBAL_COLLECT_PROCESSING_STATUS_ID',
            'MODULE_PAYMENT_GLOBAL_COLLECT_CONFIRM_COMPLETE',
            'MODULE_PAYMENT_GLOBAL_COLLECT_PAYMENT',
            'MODULE_PAYMENT_GLOBAL_COLLECT_MANDATORY_ADDRESS_FIELD',
            'MODULE_PAYMENT_GLOBAL_COLLECT_CUSTOMER_PAYMENT_INFO',
            'MODULE_PAYMENT_GLOBAL_COLLECT_MANDATORY_IC_FIELD',
            'MODULE_PAYMENT_GLOBAL_COLLECT_MANDATORY_CONTACT_FIELD'
        );
    }

    function multi_lang_configuration_info_keys() {
        return array('MODULE_PAYMENT_GLOBAL_COLLECT_MESSAGE',
            'MODULE_PAYMENT_GLOBAL_COLLECT_EMAIL_MESSAGE');
    }

    function merchant_information_keys() {
        include_once(DIR_WS_CLASSES . "payment_methods.php");

        return array(
            'MODULE_PAYMENT_GLOBAL_COLLECT_MERCHANTID' => array('field' => 'text', 'mapping' => 'MODULE_PAYMENT_GLOBAL_COLLECT_LNG_MERCHANTID'),
            'MODULE_PAYMENT_GLOBAL_COLLECT_EXPONENT' => array('field' => 'text', 'mapping' => 'MODULE_PAYMENT_GLOBAL_COLLECT_LNG_EXPONENT', 'default' => '2'),
            'MODULE_PAYMENT_TAX' => array("field" => 'select', 'mapping' => "MODULE_PAYMENT_TAX", "select" => payment_methods::get_tax_option())
        );
    }

    function get_orders_payment_info_file() {
        return DIR_FS_CATALOG_MODULES . 'payment/global_collect/admin/orders.inc.php';
    }

    function get_orders_list_payment_info_file() {
        return DIR_FS_CATALOG_MODULES . 'payment/global_collect/admin/orders_list.inc.php';
    }

    function get_ipn_class_file() {
        return DIR_FS_CATALOG_MODULES . 'payment/global_collect/classes/global_collect_ipn_class.php';
    }

    function get_payment_status_message($pass_status) {
        //Possible Payment Status ID's
        switch ($pass_status) {
            case '0' : return 'CREATED';
                break;
            case '20' : return 'PENDING AT MERCHANT';
                break;
            case '25' : return 'PENDING AT GLOBALCOLLECT';
                break;
            case '30' : return 'PENDING AT GLOBALCOLLECT';
                break;
            case '50' : return 'PENDING AT BANK /<br>ENROLLED (Credit Card Online)';
                break;
            case '55' : return 'PENDING AT CONSUMER';
                break;
            case '60' : return 'NOT ENROLLED';
                break;
            case '65' : return 'PENDING PAYMENT (CONSUMER AT BANK)';
                break;
            case '70' : return 'INDOUBT AT BANK';
                break;
            case '100' : return 'REJECTED';
                break;
            case '120' : return 'REJECTED BY BANK';
                break;
            case '125' : return 'CANCELLED BY BANK';
                break;
            case '130' : return 'FAILED VERIFICATION';
                break;
            case '140' : return 'EXPIRED AT BANK';
                break;
            case '150' : return 'TIMED OUT AT BANK';
                break;
            case '160' : return 'DENIED';
                break;
            case '170' : return 'AUTHORISATION EXPIRED';
                break;
            case '172' : return 'AUTHENTICATION_ENROLLMENT_EXPIRED';
                break;
            case '175' : return 'AUTHENTICATION_VALIDATION_EXPIRED';
                break;
            case '180' : return 'INVALED PARES OR NOT COMPLETED';
                break;
            case '200' : return 'CARDHOLDER AUTHENTICATED';
                break;
            case '220' : return 'COULD NOT AUTHENTICATE';
                break;
            case '230' : return 'CARDHOLDER NOT PARTICIPATING';
                break;
            case '280' : return 'INVALED PARES OR NOT COMPLETED';
                break;
            case '300' : return 'AUTHORISATION TESTED';
                break;
            case '310' : return 'NOT ENROLLED';
                break;
            case '320' : return 'COULD NOT AUTHENTICATE';
                break;
            case '330' : return 'CARDHOLDER NOT PARTICIPATING';
                break;
            case '350' : return 'CARDHOLDER AUTHENTICATED';
                break;
            case '400' : return 'REVISED';
                break;
            case '525' : return 'CHALLENGED';
                break;
            case '550' : return 'REFERRED';
                break;
            case '600' : return 'PENDING';
                break;
            case '650' : return 'PENDING VERIFICATION';
                break;
            case '800' : return 'READY';
                break;
            case '850' : return 'MARKED FOR SENDING';
                break;
            case '900' : return 'SENT';
                break;
            case '900' : return 'PROCESSED';
                break;
            case '950' : return 'INVOICE_SENT';
                break;
            case '975' : return 'SETTLEMENT IN PROGRESS';
                break;
            case '1000' : return 'PAID';
                break;
            case '1010' : return 'ACCOUNT DEBITED';
                break;
            case '1020' : return 'CORRECTED';
                break;
            case '1050' : return 'REMITTED';
                break;
            case '1100' : return 'REJECTED';
                break;
            case '1110' : return 'REFUSED BY ACCEPTING BANK';
                break;
            case '1120' : return 'REJECTED CARD PAYMENT ON INVOICE';
                break;
            case '1210' : return 'REFUSED BY CONSUMER BANK';
                break;
            case '1250' : return 'BOUNCED';
                break;
            case '1500' : return 'CHARGED BACK BY CONSUMER';
                break;
            case '1510' : return 'REVERSAL BY CONSUMER';
                break;
            case '1520' : return 'REVERSED';
                break;
            case '1800' : return 'REFUNDED';
                break;
            case '1810' : return 'CORRECTED REFUND';
                break;
            case '2000' : return 'ACCOUNT CREDITED';
                break;
            case '2110' : return 'REJECTED BY GLOBALCOLLECT';
                break;
            case '2120' : return 'REFUSED BY ACCEPTING BANK';
                break;
            case '2130' : return 'REFUSED BY CONSUMER BANK';
                break;
            case '2210' : return 'REVERSAL BY CONSUMER';
                break;
            case '2220' : return 'REVERSED';
                break;
            case '99999' : return 'CANCELLED';
                break;
            default :
                return 'unknown';
                break;
        }
    }

    function get_payment_status_description($pass_status) {
        //Possible Payment Status ID's
        switch ($pass_status) {
            case '0' : return 'The payment attempt was created.';
                break;
            case '20' : return 'The Hosted Merchant Link transaction is waiting for the consumer to be redirected by the merchant to WebCollect.';
                break;
            case '25' : return 'The Hosted Merchant Link transaction is waiting for the consumer to enter missing data on the payment pages of GlobalCollect.';
                break;
            case '30' : return 'The Hosted Merchant Link transaction is waiting for the consumer to be redirected by WebCollect to the payment pages of the bank (optionally after the completion of missing data).';
                break;
            case '50' : return 'The payment request and consumer have been forwarded to the payment pages of the bank. /<BR>The payment request and consumer have been forwarded to the authentication pages of the card issuer.';
                break;
            case '55' : return 'The consumer received all payment details to initiate the transaction. The consumer must go to the (bank) office to initiate the payment.';
                break;
            case '60' : return 'The consumer is not enrolled for 3D Secure authentications.';
                break;
            case '65' : return 'The consumer is at an office to initiate a transaction. The status is used when the supplier polls the WebCollect database to verify if a payment on an order is (still) possible.';
                break;
            case '70' : return 'The status of the payment is in doubt at the bank.';
                break;
            case '100' : return 'WebCollect rejected the payment instruction.';
                break;
            case '120' : return 'The bank rejected the payment.';
                break;
            case '125' : return 'The consumer cancelled the payment while on the bank��s payment pages.';
                break;
            case '130' : return 'The payment has failed.';
                break;
            case '140' : return 'The payment was not completed within the given set time limit by the consumer and is expired. The payment has failed.';
                break;
            case '150' : return 'WebCollect did not receive information regarding the outcome of the payment at the bank.';
                break;
            case '160' : return 'The transaction had been rejected for reasons of suspected fraud.';
                break;
            case '170' : return 'The authorisation is expired because no explicit settlement request was received in time.';
                break;
            case '172' : return 'The enrolment period was pending too long.';
                break;
            case '175' : return 'The validation period was pending too long.';
                break;
            case '180' : return 'The cardholder authentication response from the bank was invalid or not completed.';
                break;
            case '200' : return 'The cardholder was successfully authenticated.';
                break;
            case '220' : return 'The authentication service was out of order, cardholder could not be authenticated.';
                break;
            case '230' : return 'The cardholder is not participating in the 3D Secure authentication program.';
                break;
            case '280' : return 'The cardholder authentication response from the bank was invalid or not completed. Authorization not possible.';
                break;
            case '300' : return 'Authorisation tested. This payment will be re-authorised and settled offline.';
                break;
            case '310' : return 'The consumer is not enrolled for 3D Secure authentications. Authorization not possible.';
                break;
            case '320' : return 'The authentication service was out of order, cardholder could not be authenticated. Authorization not possible.';
                break;
            case '330' : return 'The cardholder is not participating in the 3D Secure authentication program. Authorization not possible.';
                break;
            case '350' : return 'The cardholder was successfully authenticated. Authorization not possible.';
                break;
            case '400' : return 'The consumer or WebCollect has revised the payment (with other payment product).';
                break;
            case '525' : return 'The payment was challenged and is pending.';
                break;
            case '550' : return 'The payment was referred. A ��manual�� authorisation attempt will be made shortly.';
                break;
            case '600' : return 'The payment instruction is pending waiting for a mandate (direct debit), settlement (credit card online) or acceptation (recurring orders).';
                break;
            case '650' : return 'The real-time bank payment is pending verification by the batch process. If followed by 50 PENDING AT BANK, the verification could not be carried out successfully.';
                break;
            case '800' : return 'GlobalCollect accepted the payment instruction. <BR>For Credit Card Online the payment is authorized, but not yet settled. <BR>For a Real-time Bank Transfer the return message from the bank indicates that the payment was successful.';
                break;
            case '850' : return 'Temporary status. The payment instruction was accepted and is being further processed.';
                break;
            case '900' : return 'Temporary status. The payment instruction was accepted and is being further processed.';
                break;
            case '900' : return 'The refund was processed.';
                break;
            case '950' : return 'The invoice was printed and sent.';
                break;
            case '975' : return 'The settlement file was sent for processing at the financial institution.';
                break;
            case '1000' : return 'The payment was paid.';
                break;
            case '1010' : return 'GlobalCollect debited the consumer account.';
                break;
            case '1020' : return 'GlobalCollect corrected the payment information given.';
                break;
            case '1050' : return 'The funds have been made available for remittance to the merchant.';
                break;
            case '1100' : return 'GlobalCollect rejected the payment attempt.';
                break;
            case '1110' : return 'The acquiring bank rejected the direct debit.';
                break;
            case '1120' : return 'Rejected Card Payment On Invoice.';
                break;
            case '1210' : return 'The bank of the consumer rejected the direct debit.';
                break;
            case '1250' : return 'The payment bounced.';
                break;
            case '1500' : return 'The payment was charged back by the consumer.';
                break;
            case '1510' : return 'The consumer reversed the direct debit payment.';
                break;
            case '1520' : return 'The payment was reversed.';
                break;
            case '1800' : return 'The payment was refunded.';
                break;
            case '1810' : return 'GlobalCollect corrected the refund information given.';
                break;
            case '2000' : return 'GlobalCollect credited the consumer account.';
                break;
            case '2110' : return 'GlobalCollect rejected the payout attempt.';
                break;
            case '2120' : return 'The acquiring bank rejected the payout attempt.';
                break;
            case '2130' : return 'The consumer bank rejected the payout attempt.';
                break;
            case '2210' : return 'The consumer reversed the payout.';
                break;
            case '2220' : return 'The payout was reversed.';
                break;
            case '99999' : return 'Payment/Refund/Payout attempt was cancelled by the merchant.';
                break;
            default :
                return 'unknown';
                break;
        }
    }

    function get_payment_method($pass_id) {
        switch ($pass_id) {
            case '1' : return 'Credit Card Online';
                break;
            case '2' : return 'Credit Card Batch';
                break;
            case '3' : return 'Direct Debit';
                break;
            case '4' : return 'Online Bank Transfer ';
                break;
            case '5' : return 'Cheque';
                break;
            case '6' : return 'Invoice';
                break;
            case '7' : return 'Bank Transfer';
                break;
            case '8' : return 'Real-time Bank Transfer';
                break;
            case '14' : return 'Vouchers';
                break;
            case '15' : return 'Cash';
                break;
            case '10' : return 'Bank Refunds';
                break;
            default :
                return 'unknown';
                break;
        }
    }

    function get_payment_product($pass_id) {
        switch ($pass_id) {
            case '1' : return 'Visa Online';
                break;
            case '2' : return 'American Express Online';
                break;
            case '3' : return 'MasterCard Online';
                break;
            case '9' : return 'Visa-AVS';
                break;
            case '10' : return 'MasterCard-AVS';
                break;
            case '111' : return 'Visa Delta';
                break;
            case '117' : return 'Maestro';
                break;
            case '118' : return 'Solo';
                break;
            case '121' : return 'Amex AVS';
                break;
            case '122' : return 'Visa Electron';
                break;
            case '123' : return 'Dankort';
                break;
            case '124' : return 'Laser';
                break;
            case '125' : return 'JCB';
                break;
            case '128' : return 'Discover';
                break;
            case '130' : return 'Carte Bleue Online';
                break;
            case '4' : return 'Visa';
                break;
            case '5' : return 'American Express';
                break;
            case '6' : return 'MasterCard';
                break;
            case '7' : return 'Diners Club';
                break;
            case '115' : return 'Mastercard';
                break;
            case '116' : return 'Carte Bleue';
                break;
            case '209' : return 'JCB Offline';
                break;
            case '701' : return 'The Netherlands (Eenmalige machtiging Nederland)';
                break;
            case '702' : return 'Germany (Lastschrift Deutschland)';
                break;
            case '703' : return 'Austria (Lastschrift Ostenreich)';
                break;
            case '704' : return 'France';
                break;
            case '705' : return 'United Kingdom';
                break;
            case '706' : return 'Belgium';
                break;
            case '707' : return 'Switzerland';
                break;
            case '708' : return 'Italy';
                break;
            case '709' : return 'Spain';
                break;
            case '711' : return 'The Netherlands (Doorlopende machtiging Nederland)';
                break;
            case '712' : return 'Recurring Germany';
                break;
            case '713' : return 'Recurring Austria';
                break;
            case '714' : return 'Recurring France';
                break;
            case '715' : return 'Recurring United Kingdom';
                break;
            case '716' : return 'Recurring Belgium';
                break;
            case '717' : return 'Recurring Switzerland';
                break;
            case '718' : return 'Recurring Italy';
                break;
            case '719' : return 'Recurring Spain';
                break;
            case '720' : return 'Recurring Germany (ELV)';
                break;
            case '730' : return 'USA';
                break;
            case '731' : return 'Canada';
                break;
            case '732' : return 'Australia';
                break;
            case '500' : return 'BPay';
                break;
            case '12' : return 'Cheque';
                break;
            case '201' : return 'Invoice';
                break;
            case '11' : return 'Bank Transfer';
                break;
            case '51' : return 'Bank Transfer Brazil (Dep��sito Identificado)';
                break;
            case '52' : return 'Bank Transfer Korea';
                break;
            case '400' : return 'IPS PRC Debit/Credit Card';
                break;
            case '402' : return 'eCard (Poland)';
                break;
            case '801' : return 'ING Home��Pay (Belgium)';
                break;
            case '802' : return 'Nordea E-maksu (Finland)';
                break;
            case '803' : return 'Nordea E-betaling (Denmark)';
                break;
            case '805' : return 'Nordea e-Betalning (Sweden)';
                break;
            case '809' : return 'iDEAL (Netherlands)';
                break;
            case '810' : return 'eNets (Singapore)';
                break;
            case '811' : return 'Danske (Denmark)';
                break;
            case '812' : return 'BG Bank (Denmark)';
                break;
            case '816' : return 'giropay (Germany)';
                break;
            case '817' : return 'INTERAC? Online (Canada)';
                break;
            case '818' : return 'Sampo (Finland)';
                break;
            case '819' : return 'Aktia (Finland)';
                break;
            case '820' : return 'Raifeissen (eps Online-��berweisung Austria)';
                break;
            case '821' : return 'Volksbanken Gruppe (eps Online-��berweisung Austria)';
                break;
            case '822' : return 'N? HYPO (eps Online-��berweisung Austria)';
                break;
            case '823' : return 'Voralberger HYPO (eps Online-��berweisung Austria)';
                break;
            case '824' : return 'Bankhaus Sp?ngler (eps Online-��berweisung Austria)';
                break;
            case '825' : return 'Hypo Tirol Bank (eps Online-��berweisung Austria)';
                break;
            case '826' : return 'Erste Bank und Sparkassen (eps Online-��berweisung Austria)';
                break;
            case '827' : return 'BAWAG (eps Online-��berweisung Austria)';
                break;
            case '828' : return 'P.S.K. (eps Online-��berweisung Austria)';
                break;
            case '829' : return 'Easy (eps Online-��berweisung Austria)';
                break;
            case '830' : return 'PaySafeCard (multiple countries)';
                break;
            case '831' : return 'Sparda-Bank (eps Online-��berweisung Austria)';
                break;
            case '836' : return 'Sofort��berweisung (various countries)';
                break;
            case '840' : return 'PayPal (various countries)';
                break;
            case '841' : return 'WebMoney (various countries)';
                break;
            case '843' : return 'Moneybookers (various countries)';
                break;
            case '844' : return 'Wallie (various countries)';
                break;
            case '845' : return 'cashU';
                break;
            case '850' : return 'Secure Vault (USA)';
                break;
            case '852' : return 'POLi (Australia)';
                break;
            case '856' : return 'eps Online-��berweisung (combines payment products 820-829 & 831)';
                break;
            case '1400' : return 'Ukash (multiple countries)';
                break;
            case '1501' : return 'Western Union';
                break;
            case '1503' : return 'Boleto Banc��rio (Brazil)';
                break;
            case '1505' : return 'Banco Santantder';
                break;
            case '1504' : return 'Konbini';
                break;
            case '1001' : return 'Bank Refunds (non country specific)';
                break;
            case '1002' : return 'Bank Refunds Australia';
                break;
            case '1003' : return 'Bank Refunds Austria';
                break;
            case '1004' : return 'Bank Refunds Belgium';
                break;
            case '1027' : return 'Bank Refunds Brazil';
                break;
            case '1035' : return 'Bank refunds Canada';
                break;
            case '1028' : return 'Bank Refunds China';
                break;
            case '1019' : return 'Bank Refunds Czech';
                break;
            case '1005' : return 'Bank Refunds Denmark';
                break;
            case '1020' : return 'Bank Refunds Estonia';
                break;
            case '1006' : return 'Bank Refunds Finland';
                break;
            case '1007' : return 'Bank Refunds France';
                break;
            case '1008' : return 'Bank Refunds Germany';
                break;
            case '1030' : return 'Bank Refunds Hong Kong';
                break;
            case '1021' : return 'Bank Refunds Hungary';
                break;
            case '1036' : return 'Bank refunds Indonesia-IDR';
                break;
            case '1037' : return 'Bank refunds Indonesia-USD';
                break;
            case '1022' : return 'Bank Refunds Ireland';
                break;
            case '1009' : return 'Bank Refunds Italy';
                break;
            case '1016' : return 'Bank Refunds Japan';
                break;
            case '1018' : return 'Bank Refunds Korea';
                break;
            case '1023' : return 'Bank Refunds Latvia';
                break;
            case '1029' : return 'Bank Refunds Luxembourg';
                break;
            case '1038' : return 'Bank refunds Malaysia';
                break;
            case '1011' : return 'Bank Refunds Norway';
                break;
            case '1039' : return 'Bank refunds Philippines';
                break;
            case '1024' : return 'Bank Refunds Poland';
                break;
            case '1017' : return 'Bank Refunds Portugal';
                break;
            case '1042' : return 'Bank refunds Romania-EUR';
                break;
            case '1043' : return 'Bank refunds Romania-RON';
                break;
            case '1034' : return 'Bank refunds Singapore';
                break;
            case '1032' : return 'Bank refunds Slovakia';
                break;
            case '1025' : return 'Bank Refunds Slovenia';
                break;
            case '1026' : return 'Bank Refunds South Africa';
                break;
            case '1012' : return 'Bank Refunds Spain';
                break;
            case '1013' : return 'Bank Refunds Sweden';
                break;
            case '1014' : return 'Bank Refunds Switzerland';
                break;
            case '1031' : return 'Bank Refunds Taiwan';
                break;
            case '1041' : return 'Bank refunds Thailand';
                break;
            case '1010' : return 'Bank Refunds The Netherlands';
                break;
            case '1015' : return 'Bank Refunds United Kingdom';
                break;
            case '1201' : return 'Bank payout The Netherlands';
                break;
            case '1202' : return 'Bank payout Germany';
                break;
            case '1203' : return 'Bank payout Austria';
                break;
            case '1204' : return 'Bank payout France';
                break;
            case '1205' : return 'Bank payout Great Britain';
                break;
            case '1206' : return 'Bank payout Belgium';
                break;
            case '1207' : return 'Bank payout Switzerland';
                break;
            case '1208' : return 'Bank payout Italy';
                break;
            case '1209' : return 'Bank payout Spain';
                break;
            case '1210' : return 'Bank payout Denmark';
                break;
            case '1211' : return 'Bank payout Norway';
                break;
            case '1212' : return 'Bank payout Finland';
                break;
            case '1213' : return 'Bank payout Sweden';
                break;
            case '1216' : return 'Bank payout Japan';
                break;
            case '1218' : return 'Bank payout South Korea';
                break;
            case '1230' : return 'Bank payout USA';
                break;
            case '1231' : return 'Bank payout Canada';
                break;
            case '1232' : return 'Bank payout Australia';
                break;
            default :
                return 'unknown';
                break;
        }
    }

    function get_avs_code($pass_code) {
        switch (strtoupper($pass_code)) {
            case 'A' : return 'Address (Street) matches, Zip does not';
                break;
            case 'B' : return 'Street address match for international transactions. Postal code not verified due to incompatible formats.';
                break;
            case 'C' : return 'Street address and postal code not verified for international transaction due to incompatible formats';
                break;
            case 'D' : return 'Street address and postal codes match for international transaction ';
                break;
            case 'E' : return 'AVS Error';
                break;
            case 'F' : return 'Address does match and five digit ZIP code does match (UK only)';
                break;
            case 'G' : return 'Address information is unavailable; international transaction; non-AVS participant';
                break;
            case 'I' : return 'Address information not verified for international transaction';
                break;
            case 'M' : return 'Street address and postal codes match for international transaction';
                break;
            case 'N' : return 'No Match on Address (Street) or Zip';
                break;
            case 'P' : return 'Postal codes match for international transaction. Street address not verified due to incompatible formats';
                break;
            case 'R' : return 'Retry, System unavailable or Timed out';
                break;
            case 'S' : return 'Service not supported by issuer';
                break;
            case 'U' : return 'Address information is unavailable';
                break;
            case 'W' : return '9 digit Zip matches, Address (Street) does not';
                break;
            case 'X' : return 'Exact AVS Match';
                break;
            case 'Y' : return 'Address (Street) and 5 digit Zip match';
                break;
            case 'Z' : return '5 digit Zip matches, Address (Street) does not';
                break;
            case '0' : return 'No service available';
                break;
            default :
                return $pass_code;
                break;
        }
    }

    function get_cvv_code($pass_code) {
        switch (strtoupper($pass_code)) {
            case 'M' : return 'CVV check performed and valid value';
                break;
            case 'N' : return 'CVV checked and no match';
                break;
            case 'P' : return 'CVV check not performed, not requested';
                break;
            case 'S' : return 'Card holder claims no CVV-code on card, issuer states CVV-code should be on card ';
                break;
            case 'U' : return 'Issuer not certified for CVV2';
                break;
            case 'X' : return 'Server provider did not respond';
                break;
            case '0' : return 'No service available';
                break;
            default :
                return $pass_code;
                break;
        }
    }

    function get_fraud_result_code($pass_code) {
        switch (strtoupper($pass_code)) {
            case 'N' : return 'No fraud Requested';
                break;
            case 'C' : return 'Challenged';
                break;
            case 'A' : return 'Accept';
                break;
            case 'D' : return 'Denied/Fraudulent ';
                break;
            case 'E' : return 'Error while checking';
                break;
            default :
                return $pass_code;
                break;
        }
    }

    function get_eci_code($pass_code) {
        switch (trim($pass_code)) {
            case '00' :
            case '0' :
                return "Failed authentication (no liability shift)";
                break;
            case '01' :
            case '1' :
                return "Incomplete authentication (MasterCard)";
                break;
            case '02' :
            case '2' :
                return "Successful authentication (MasterCard)";
                break;
            case '05' :
            case '5' :
                return "Successful authentication (Visa)";
                break;
            case '06' :
            case '6' :
                return "Authentication attempted (Visa)";
                break;
            case '07' :
            case '7' :
                return "Failed authentication (no liability shift)";
                break;
            default :
                return $pass_code;
                break;
        }
    }

    function get_fraud_code($pass_code) {
        switch ($pass_code) {
            case '0000' : return 'No Score';
                break;
            case '0100' : return 'Accept';
                break;
            case '0150' : return 'Always Accept';
                break;
            case '0200' : return 'Authorization Decline';
                break;
            case '0250' : return 'Always Deny';
                break;

            case '0300' : return 'Suspicious Usage';
                break;
            case '0330' : return 'Rule Challenge';
                break;
            case '0400' : return 'Suspicious Usage';
                break;
            case '0500' : return 'Questionable Usage';
                break;
            case '0600' :
            case '0610' : return 'Questionable Usage';
                break;

            case '0700' : return 'Velocity or Rules Threshold Violation';
                break;
            case '0800' : return 'Tumbling and/or Swapping Pattern Detected';
                break;
            case '901' : return 'Internal Error';
                break;
            case '902' : return 'Validation Error ';
                break;
            case '1000' : return 'Screening Service Always Accept';
                break;

            case '1300' : return 'Screening Challenge';
                break;
            case '1700' : return 'Screening Service Always Challenge';
                break;
            case '1800' : return 'Screening Service Challenge';
                break;
            case '2000' : return 'Screening Service Deny';
                break;

            default :
                return $pass_code;
                break;
        }
    }

    function log_msg($pass_msg, $pass_title = 'Global Correct DEBUG E-MAIL') {
        return 1;
        $handle = fopen('c:/log_text.txt', 'a');
        fwrite($handle, date("Y-m-d H:i:s") . "\n" . $pass_msg . "\n================================\n\n");
        fclose($handle);
    }

    function clear_temp_process($match_case) {
        $delete_temp_process_sql = "DELETE FROM " . TABLE_TEMP_PROCESS . " 
									WHERE page_name = 'checkout_process.php' 
										AND match_case = '" . $match_case . "'";
        tep_db_query($delete_temp_process_sql);
    }

}

?>