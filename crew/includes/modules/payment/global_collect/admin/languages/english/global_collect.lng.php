<?php

define('TABLE_HEADING_GLOBAL_COLLECT_DATE', 'Date');
define('TABLE_HEADING_GLOBAL_COLLECT_STATUS', 'Status');
define('TABLE_HEADING_GLOBAL_COLLECT_DESCRIPTION', 'Description');
define('TABLE_HEADING_GLOBAL_COLLECT_CHANGED_BY', 'Changed By');

define('MODULE_PAYMENT_GLOBAL_COLLECT_LNG_MERCHANTID', 'Merchant ID');
define('MODULE_PAYMENT_GLOBAL_COLLECT_LNG_EXPONENT', 'Exponent');
define('MODULE_PAYMENT_TAX', 'Tax');

define('ENTRY_GLOBAL_COLLECT_PAYMENT_REFERENCE', 'Payment Reference');
define('ENTRY_GLOBAL_COLLECT_ADDITIONAL_REFERENCE', 'Payment ID');
define('ENTRY_GLOBAL_COLLECT_PAYMENT_PRODUCTID', 'Payment Type');
define('ENTRY_GLOBAL_COLLECT_STATUS_ID', 'Payment Status');
define('ENTRY_GLOBAL_COLLECT_STATUS_DATE', 'Status Date');

define('ENTRY_GLOBAL_COLLECT_PAYMENT_METHOD_ID', 'Payment Detail');
define('ENTRY_GLOBAL_COLLECT_RECEIVED_DATE', 'Received Date');
define('ENTRY_GLOBAL_COLLECT_CURRENCY_CODE', 'Payment Currency');
define('ENTRY_GLOBAL_COLLECT_AMOUNT', 'Amount');
define('ENTRY_GLOBAL_COLLECT_ECI', 'Cardholder Authentication Result');

define('ENTRY_GLOBAL_COLLECT_NO_PAYMENT_INFO', 'Amount');
?>
