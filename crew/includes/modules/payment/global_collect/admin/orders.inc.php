<?
/*
  	$Id: orders.inc.php,v 1.9 2010/06/24 08:20:53 boonhock Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2005 SKC Venture
  	
  	Released under the GNU General Public License
*/

//$cancel_post_capture_order_permission = tep_admin_files_actions(FILENAME_ORDERS, 'ORDER_CANCEL_POST_CAPTURE');
$order_global_collect_cancel_payment_permission = tep_admin_files_actions(FILENAME_ORDERS, 'ORDER_GLOBAL_COLLECT_CANCEL_PAYMENT');
$order_global_collect_check_payment_permission = tep_admin_files_actions(FILENAME_ORDERS, 'ORDER_CHECK_GLOBAL_COLLECT_PAYMENT');

$global_collect_trans_info_select_sql = "	SELECT * 
											FROM " . TABLE_GLOBAL_COLLECT . "
											WHERE global_collect_orders_id = '" . (int)$oID  . "'";
$global_collect_trans_info_result_sql = tep_db_query($global_collect_trans_info_select_sql);

$global_collect_trans_history_select_sql = "SELECT * 
											FROM " . TABLE_GLOBAL_COLLECT_STATUS_HISTORY . " 
											WHERE global_collect_orders_id='" . (int)$oID . "'
											ORDER BY global_collect_status_history_id";
$global_collect_trans_history_result_sql= tep_db_query($global_collect_trans_history_select_sql);

include_once(DIR_FS_CATALOG_MODULES . 'payment/global_collect/admin/languages/'.$language.'/global_collect.lng.php');

?>
<table border="0" width="100%" cellspacing="0" cellpadding="2">
<? ob_start(); 

?>
	<tr>
		<td class="main">
      		<table border="0" width="100%" cellspacing="0" cellpadding="2">
      			<tr>
      				<td class="main" width="12%" nowrap><b><?=ENTRY_PAYMENT_METHOD?></b></td>
      				<td class="main">
      					<div>
      						<? if((int)$order->info['payment_methods_id']>0) { ?><div class="<?=((int)$payment_info_array[$order->info['payment_methods_id']]->confirm_complete_days>0?'rp_icon':'nrp_icon')?>"><b><?=((int)$payment_info_array[$order->info['payment_methods_id']]->confirm_complete_days>0?'RP':'NRP')?></b></div> <? } ?>
      						<div style="width:30px; height:15px; background-color:<?=$payment_module_info['display_colour']?>; float:left; vertical-align:top; text-align:center;"></div>
      						<div style="vertical-align: top">
      							&nbsp;&nbsp;
<?
	if ($view_payment_details_permission) {
		if (tep_not_null($last_payment_method) && $last_payment_method['payment_methods_id'] != $order->info['payment_methods_id']) {
			echo '<span class="redIndicator">'.(tep_not_null($payment_module_info['payment_methods_parent_title'])?$payment_module_info['payment_methods_parent_title'] . ' - ' :'') . $payment_module_info['payment_methods_title'].'</span>';
		} else {
			echo (tep_not_null($payment_module_info['payment_methods_parent_title'])?$payment_module_info['payment_methods_parent_title'] . ' - ' :'') . $payment_module_info['payment_methods_title'];
		}
		
		if (tep_not_null($last_payment_method) && $last_payment_method['payment_methods_id'] != $order->info['payment_methods_id']) {
			echo '&nbsp;&nbsp;<span class="redIndicator">(Last Payment Method: ' . (tep_not_null($last_payment_method['parent_payment_methods_title'])? $last_payment_method['parent_payment_methods_title'] . ' - ' : '' ) . $last_payment_method['payment_methods_title'] . ')</span>';
		}
	} else {
		echo (tep_not_null($payment_module_info['payment_methods_parent_title'])?$payment_module_info['payment_methods_parent_title'] . ' - ' :'') . $payment_module_info['payment_methods_title'];
	}
?>
							</div>
      					</div>
      				</td>
				</tr>
			</table>
		</td>
	</tr>
<?
$payment_method_title_info = ob_get_contents();
ob_end_clean();

if (tep_db_num_rows($global_collect_trans_history_result_sql)) {
	$last_global_collect_date = '0000-00-00 00:00:00';
	ob_start(); 
?>
			<table border="1" cellspacing="0" cellpadding="2">
				<tr>
					<td class="smallText" nowrap><b><?=TABLE_HEADING_GLOBAL_COLLECT_DATE?></b></td>
					<td class="smallText" nowrap><b><?=TABLE_HEADING_GLOBAL_COLLECT_STATUS?></b></td>
					<td class="smallText" nowrap><b><?=TABLE_HEADING_GLOBAL_COLLECT_DESCRIPTION?></b></td>
					<td class="smallText" nowrap><b><?=TABLE_HEADING_GLOBAL_COLLECT_CHANGED_BY?></b></td>
				</tr>
<?				while ($global_collect_trans_history_row = tep_db_fetch_array($global_collect_trans_history_result_sql)) {
					$last_global_collect_date = $global_collect_trans_history_row['global_collect_date'];
		        	echo '	<tr>
								<td class="smallText" nowrap valign="top">'.$global_collect_trans_history_row['global_collect_date'].'</td>
								<td class="smallText" nowrap valign="top">'.($global_collect_trans_history_row["global_collect_status"]== '800' && $payment_info_array[$order->info['payment_methods_id']]->pm_object->payment=='4'? '<span class="redIndicator">'.$payment_info_array[$order->info['payment_methods_id']]->pm_object->get_payment_status_message($global_collect_trans_history_row["global_collect_status"]).'<BR>(Bank Transfer/<BR>Online Bank Transfer)</span>':$payment_info_array[$order->info['payment_methods_id']]->pm_object->get_payment_status_message($global_collect_trans_history_row["global_collect_status"])).'</td>
								<td class="smallText" nowrap>';
								echo (tep_not_null($global_collect_trans_history_row['global_collect_description'])?$global_collect_trans_history_row['global_collect_description']:'&nbsp;');
		            echo '		</td>
								<td class="smallText" nowrap>'.(tep_not_null($global_collect_trans_history_row['changed_by'])?$global_collect_trans_history_row['changed_by']:'&nbsp;').'</td>
							</tr>';
     			}
?>
            </table>
<?
	$global_collect_trans_history_content = ob_get_contents();
	ob_end_clean();
}
$check_trans_status_btn_flag = false;
if (tep_db_num_rows($global_collect_trans_history_result_sql)) {
	$last_global_collect_date_diff = tep_day_diff($last_global_collect_date, date("Y-m-d H:i:s"), 'sec');
	if ($last_global_collect_date_diff > 60) {
		$check_trans_status_btn_flag = true;
	}
} else {
	$last_global_collect_date_diff = tep_day_diff($order->info['date_purchased'], date("Y-m-d H:i:s"), 'sec');
	if ($last_global_collect_date_diff > 300) {
		$check_trans_status_btn_flag = true;
	}
}

if (tep_db_num_rows($global_collect_trans_info_result_sql) || tep_db_num_rows($global_collect_trans_history_result_sql)) {
	echo $payment_method_title_info;
	if (!$view_payment_details_permission) {
		if ($order_global_collect_check_payment_permission) {
?>
  	<tr>
    	<td class="main">
<?
			echo "&nbsp;";
			echo tep_draw_form('gc_status_check_form', FILENAME_ORDERS, tep_get_all_get_params(), 'post');
			echo tep_draw_hidden_field('subaction', 'payment_action');
			echo tep_draw_hidden_field('payment_action', 'check_trans_status');
			echo '<input type="submit" name="GCCheckTransStatusBtn" value="Check Payment Status" title="Check Payment Status" class="inputButton">';
			echo "</form>";
?>
		</td>
	</tr>
<? 			if (tep_db_num_rows($global_collect_trans_history_result_sql)) { ?>
  	<tr>
    	<td class="main">
			<?=$global_collect_trans_history_content?>
		</td>
	</tr>
<?
			}
		}
	} else {
?>
  	<tr>
    	<td class="main">
      		<table border="0" width="100%" cellspacing="0" cellpadding="2">
      			<tr valign="top">
<?
		$global_collect_trans_found = false;
		if ($global_collect_trans_info_row = tep_db_fetch_array($global_collect_trans_info_result_sql)) {
			$global_collect_trans_found = true;
?>
        			<td width="35%">
              			<table border="0" cellspacing="0" cellpadding="2">
<?
/*
							<tr>
								<td class="main" valign="top" nowrap><b><?=ENTRY_GLOBAL_COLLECT_STATUS_DATE?></b>&nbsp;</td>
			                	<td class="main" valign="top" nowrap><?=$global_collect_trans_info_row["global_collect_status_date"]?></td>
							</tr>
							<tr>
								<td class="main" valign="top" nowrap><b><?=ENTRY_GLOBAL_COLLECT_PAYMENT_REFERENCE?></b>&nbsp;</td>
			                	<td class="main" valign="top" nowrap><?=$global_collect_trans_info_row["global_collect_payment_reference"]?></td>
							</tr>
*/
?>
							<tr>
								<td class="main" valign="top" nowrap><b><?=ENTRY_GLOBAL_COLLECT_PAYMENT_PRODUCTID?>:</b>&nbsp;</td>
			                	<td class="main" valign="top" nowrap><?=$payment_info_array[$order->info['payment_methods_id']]->pm_object->get_payment_product($global_collect_trans_info_row["global_collect_paymentproductid"])?></td>
							</tr>
							<tr>
								<td class="main" valign="top" nowrap><b><?=ENTRY_GLOBAL_COLLECT_PAYMENT_METHOD_ID?>:</b>&nbsp;</td>
			                	<td class="main" valign="top" nowrap><?=$payment_info_array[$order->info['payment_methods_id']]->pm_object->get_payment_method($global_collect_trans_info_row["global_collect_paymentmethodid"])?></td>
							</tr>
<?
/*							<tr>
								<td class="main" valign="top" nowrap><b><?=ENTRY_GLOBAL_COLLECT_ADDITIONAL_REFERENCE?>:</b>&nbsp;</td>
			                	<td class="main" valign="top" nowrap><?=$global_collect_trans_info_row["global_collect_additional_reference"]?></td>
							</tr>
*/
?>
<?
/*
							<tr>
								<td class="main" valign="top" nowrap><b><?=ENTRY_GLOBAL_COLLECT_RECEIVED_DATE?></b>&nbsp;</td>
			                	<td class="main" valign="top" nowrap><?=$global_collect_trans_info_row["global_collect_receiveddate"]?></td>
							</tr>

*/
?>
<?			if ($payment_info_array[$order->info['payment_methods_id']]->pm_object->payment=='1') {
				$check_credit_card_array = array();
				if (tep_not_null($global_collect_trans_info_row['global_collect_cc_last_4_digit']) && tep_not_null($global_collect_trans_info_row['global_collect_cc_expiry_date'])) {
					$check_credit_card_select_sql = "	SELECT o.customers_id
														FROM " . TABLE_ORDERS . " AS o 
														INNER JOIN " . TABLE_GLOBAL_COLLECT . " AS gc 
															ON o.orders_id = gc.global_collect_orders_id 
														WHERE gc.global_collect_cc_last_4_digit = '".tep_db_input($global_collect_trans_info_row['global_collect_cc_last_4_digit'])."'
															AND gc.global_collect_cc_expiry_date = '".tep_db_input($global_collect_trans_info_row['global_collect_cc_expiry_date'])."' 
														GROUP BY o.customers_id";
					$check_credit_card_result_sql = tep_db_query($check_credit_card_select_sql);
					while ($check_credit_card_row = tep_db_fetch_array($check_credit_card_result_sql)) {
						$check_credit_card_array[] = $check_credit_card_row['customers_id'];
					}
				}
?>
              				<tr>
                				<td class="main" valign="top" nowrap><b><?="Credit Card /Expiry Date:"?></b>&nbsp;</td>
                				<td class="main"><?
				if (tep_not_null($global_collect_trans_info_row['global_collect_cc_last_4_digit'])) {
					echo '**** **** **** '.$global_collect_trans_info_row['global_collect_cc_last_4_digit'];
				} else {
					echo TEXT_NOT_AVAILABLE;
				}
				echo ' / ';
				if (tep_not_null($global_collect_trans_info_row['global_collect_cc_expiry_date'])) {
					echo $global_collect_trans_info_row['global_collect_cc_expiry_date'];
				} else {
					echo TEXT_NOT_AVAILABLE;
				}
					
				if (count($check_credit_card_array)>1) {
					echo 	tep_draw_form('cust_lists_share_cc', FILENAME_CUSTOMERS, 'action=show_report&customer_lists_subaction=do_search', 'post', 'target="_blank"') . "\n" . 
					tep_draw_hidden_field('customer_share_id', tep_array_serialize($check_credit_card_array)) . "\n" . 
							"<BR><span class='redIndicator'>Same credit card used in <a href='javascript:document.cust_lists_share_cc.submit();'>" . count($check_credit_card_array) . "</a> profiles.</span>" . 
							'</form>' . "\n";
				}
				if (tep_not_null($global_collect_trans_info_row['global_collect_cc_last_4_digit']) && tep_not_null($global_collect_trans_info_row['global_collect_cc_expiry_date'])) {
					$cc_check_info = array(	'card_number' => $global_collect_trans_info_row['global_collect_cc_last_4_digit'],
											'date_purchased' => $order->info['date_purchased'],
											'orders_id' => $order->order_id,
											'customers_id' => $order->customer['id']);
					$cc_verified_date = tep_get_payment_info_verified_date($cc_check_info, 'credit_card');
					echo "<br><span class='redIndicator'>First successful verified date by this user: ".(tep_not_null($cc_verified_date)?$cc_verified_date:"NEVER")."</span><br>(Data can only traced back to 2009-12-09)";
				}
?>
								</td>
              				</tr>
							<tr>
								<td class="main" valign="top" nowrap><b><?=ENTRY_GLOBAL_COLLECT_ECI?>:</b>&nbsp;</td>
			                	<td class="main" valign="top" nowrap><?=(tep_not_null($global_collect_trans_info_row["global_collect_eci"])?$payment_info_array[$order->info['payment_methods_id']]->pm_object->get_eci_code($global_collect_trans_info_row["global_collect_eci"]):'n/a')?></td>
							</tr>
<?			}

			$post_auth_btn_text = 'Post-Captured';
			if ($global_collect_trans_info_row['global_collect_capture_request'] == 1) {
				$post_auth_btn_text .= ' (Awaiting for response)';
			}
			if ($order->info['orders_status'] == '7' && $global_collect_trans_info_row['global_collect_status_id'] == 600) {
?>

							<tr>
								<td class="main" valign="bottom" align="center" nowrap colspan="2" height="90px"><?
            						echo tep_draw_form('post_capture_order', FILENAME_ORDERS, tep_get_all_get_params(), 'post');
									echo tep_draw_hidden_field('subaction', 'post_authorisation');
									echo '<input type="submit" name="PostCaptureBtn" value="'.$post_auth_btn_text.'" title="Post Authorise" class="inputButton" onClick="if (confirm(\'Are you sure to post captured this order remotely?\')) { this.disabled=true;this.value=\'Please wait...\';this.form.submit(); } else { return false; }">';
									echo "</form>";
								?></td>
							</tr>
<?			}/* else if ($global_collect_trans_info_row['global_collect_status_id'] == 800 && $cancel_post_capture_order_permission) { 
				$cancel_post_auth_btn_text = 'Cancel Post-Captured';
			
							<tr>
								<td class="main" valign="bottom" align="center" nowrap colspan="2" height="90px"><?
            						echo tep_draw_form('cancel_post_capture_order', FILENAME_ORDERS, tep_get_all_get_params(), 'post');
									echo tep_draw_hidden_field('subaction', 'cancel_post_authorisation');
									echo '<input type="submit" name="CancelPostCaptureBtn" value="'.$cancel_post_auth_btn_text.'" title="Cancel Post Authorise" class="inputButton" onClick="if (confirm(\'Are you sure to post captured this order remotely?\')) { this.disabled=true;this.value=\'Please wait...\';this.form.submit(); } else { return false; }">';
									echo "</form>";
								?></td>
							</tr>
<?			} */?>
              			</table>
        			</td>
<?
		}
?>
        			<td>
              			<table border="0" cellspacing="0" cellpadding="2" width="430px">
							<tr>
								<td class="main" valign="top" nowrap><b><?=ENTRY_GLOBAL_COLLECT_STATUS_ID?></b>&nbsp;<?=($global_collect_trans_info_row["global_collect_status_id"]=='800' && $payment_info_array[$order->info['payment_methods_id']]->pm_object->payment=='4'? '<span class="redIndicator">'.$payment_info_array[$order->info['payment_methods_id']]->pm_object->get_payment_status_message($global_collect_trans_info_row["global_collect_status_id"]).'<BR>(Bank Transfer/Online Bank Transfer)</span>':$payment_info_array[$order->info['payment_methods_id']]->pm_object->get_payment_status_message($global_collect_trans_info_row["global_collect_status_id"]))?></td>
								<td class="main" align="right"><?
		if ($check_trans_status_btn_flag && ($order_global_collect_check_payment_permission || $order->info['orders_status'] == '1' || $order->info['orders_status'] == '7' || $order->info['orders_status'] == '2')) {
									echo tep_draw_form('gc_status_check_form', FILENAME_ORDERS, tep_get_all_get_params(), 'post');
									echo tep_draw_hidden_field('subaction', 'payment_action');
									echo tep_draw_hidden_field('payment_action', 'check_trans_status');
									echo '<input type="submit" name="GCCheckTransStatusBtn" value="Check Payment Status" title="Check Payment Status" class="inputButton">';
									echo "</form>";
		}
								?></td>
							</tr>
              				<tr>
                				<td class="main" colspan="2" nowrap>
                					<?=$global_collect_trans_history_content?>
                				</td>
                			</tr>
<?		if ($order_global_collect_cancel_payment_permission && $global_collect_trans_info_row['global_collect_status_id'] == 600 && $order->info['orders_status'] == '5') {  ?>
                			<tr>
								<td class="main" valign="top" colspan="2"><?
            						echo tep_draw_form('cancel_payment_order', FILENAME_ORDERS, tep_get_all_get_params(), 'post');
									echo tep_draw_hidden_field('subaction', 'cancel_payment_order');
									echo '<input type="submit" name="CancelPaymentBtn" value="Cancel Payment" title="Cancel Post Authorise" class="inputButton" onClick="if (confirm(\'Are you sure to cancel this payment remotely?\')) { this.disabled=true;this.value=\'Please wait...\';this.form.submit(); } else { return false; }">';
									echo "</form>";
								?></td>
							</tr>
<?		} ?>
              			</table>
              		</td>
<?
		if ($global_collect_trans_found) {
?>
        			<td>
        				<table>
							<tr>
								<td class="main" valign="top" nowrap><b><?=ENTRY_GLOBAL_COLLECT_CURRENCY_CODE?></b>&nbsp;</td>
			                	<td class="main" valign="top" nowrap>
<?
			    					if (strtolower($global_collect_trans_info_row["global_collect_currency_code"]) == strtolower($order->info['currency'])) {
			    						echo $global_collect_trans_info_row['global_collect_currency_code'];
			    					} else {
			    						echo '<span class="redIndicator">'.$global_collect_trans_info_row["global_collect_currency_code"].'<br><span class="smallText">Does not match purchase currency.</span></span>';
			    					}
?>
			                	</td>
							</tr>
							<tr>
								<td class="main" valign="top" nowrap><b><?=ENTRY_GLOBAL_COLLECT_AMOUNT?></b>&nbsp;</td>
			                	<td class="main" valign="top" nowrap><?
			    					$gross_display_text = number_format($global_collect_trans_info_row["global_collect_amount"], $currencies->currencies[$order->info["currency"]]['decimal_places'], $currencies->currencies[$order->info["currency"]]['decimal_point'], $currencies->currencies[$order->info["currency"]]['thousands_point']);
			    					if ($currencies->currencies[$order->info["currency"]]['symbol_left'].$gross_display_text.$currencies->currencies[$order->info["currency"]]['symbol_right'] != strip_tags($order->order_totals['ot_total']['text']) ) {
			    						$gross_display_text = '<span class="redIndicator">'.$gross_display_text.'<br><span class="smallText">Does not match purchase amount.</span></span>';
			    					}
			    					echo $gross_display_text;
			    				?>
							</tr>
        				</table>
        				<table border="1" cellspacing="0" cellpadding="2">
  							<tr>
                				<td class="main" valign="top" nowrap><b><?="CVV result:"?></b>&nbsp;</td>
                				<td class="main"><?=(tep_not_null($global_collect_trans_info_row['global_collect_cvv_result']) ? $payment_info_array[$order->info['payment_methods_id']]->pm_object->get_cvv_code($global_collect_trans_info_row['global_collect_cvv_result']) : TEXT_NOT_AVAILABLE)?></td>
              				</tr>
  							<tr>
                				<td class="main" valign="top" nowrap><b><?="AVS result:"?></b>&nbsp;</td>
                				<td class="main"><?=(tep_not_null($global_collect_trans_info_row['global_collect_avs_result']) ? $payment_info_array[$order->info['payment_methods_id']]->pm_object->get_avs_code($global_collect_trans_info_row['global_collect_avs_result']) : TEXT_NOT_AVAILABLE)?></td>
              				</tr>
              				<tr>
                				<td class="main" valign="top" nowrap><b><?="Fraud Result:"?></b>&nbsp;</td>
                				<td class="main"><?=(tep_not_null($global_collect_trans_info_row['global_collect_fraud_result']) ? $payment_info_array[$order->info['payment_methods_id']]->pm_object->get_fraud_result_code($global_collect_trans_info_row['global_collect_fraud_result']) : TEXT_NOT_AVAILABLE)?></td>
              				</tr>
              				<tr>
                				<td class="main" valign="top" nowrap><b><?="Fraud Code:"?></b>&nbsp;</td>
                				<td class="main"><?=(tep_not_null($global_collect_trans_info_row['global_collect_fraud_code']) ? $payment_info_array[$order->info['payment_methods_id']]->pm_object->get_fraud_code($global_collect_trans_info_row['global_collect_fraud_code']) : TEXT_NOT_AVAILABLE)?></td>
              				</tr>
        				</table>
        			</td>
<?
	}
?>
      			</tr>
      		</table>
   		</td>
  	</tr>
<?
	}
	if (isset($global_collect_trans_history_content)) unset($global_collect_trans_history_content);
} else {
	echo $payment_method_title_info;
	
	if ($check_trans_status_btn_flag) {
?>
	<tr>
		<td class="main" align="left" nowrap colspan="2"><?
			echo tep_draw_form('gc_status_check_form', FILENAME_ORDERS, tep_get_all_get_params(), 'post');
			echo tep_draw_hidden_field('subaction', 'payment_action');
			echo tep_draw_hidden_field('payment_action', 'check_trans_status');
			echo '<input type="submit" name="GCCheckTransStatusBtn" value="Check Payment Status" title="Check Payment Status" class="inputButton">';
			echo "</form>";
		?></td>
	</tr>
<?
	}
}
?>
</table>