<?
/*
  	$Id: orders_list.inc.php,v 1.2 2010/07/27 07:37:15 boonhock Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2005 SKC Venture
  	
  	Released under the GNU General Public License
*/
include_once(DIR_FS_CATALOG_MODULES . 'payment/global_collect/admin/languages/'.$language.'/global_collect.lng.php');

$global_collect_trans_info_select_sql = "	SELECT * 
											FROM " . TABLE_GLOBAL_COLLECT . " 
											WHERE global_collect_orders_id='" . (int)$order_obj->orders_id . "'";
$global_collect_trans_info_result_sql= tep_db_query($global_collect_trans_info_select_sql);
?>
<table border="0" width="100%" cellspacing="0" cellpadding="2">
	<tr>
		<td class="subInvoiceBoxHeading">
			<div style="border-bottom: 1px solid #996600; border-top: 1px solid #996600; padding: 2px;">Payment Details</div>
		</td>
	</tr>
<?
if ($global_collect_trans_info_row = tep_db_fetch_array($global_collect_trans_info_result_sql)) {
?>
  	<tr>
    	<td class="main">
      		<table border="0" width="100%" cellspacing="0" cellpadding="2">
      			<tr valign="top">
        			<td width="40%">
              			<table border="0" cellspacing="0" cellpadding="2">
							<tr>
								<td class="main" valign="top" nowrap width="140px"><b><?=ENTRY_GLOBAL_COLLECT_STATUS_ID?></b>&nbsp;</td>
			                	<td class="main" valign="top" nowrap width="300px"><?=$payment_methods_obj->payment_gateways_array[$order_obj->payment_methods_parent_id]->get_payment_status_message($global_collect_trans_info_row["global_collect_status_id"])?></td>
			                	<td class="main" valign="top" nowrap rowspan="5" width="200px">
							        <table border="1" cellspacing="0" cellpadding="2">
			  							<tr>
			                				<td class="main" valign="top" nowrap><b><?="CVV result:"?></b>&nbsp;</td>
			                				<td class="main"><?=(tep_not_null($global_collect_trans_info_row['global_collect_cvv_result']) ? $payment_methods_obj->payment_gateways_array[$order_obj->payment_methods_parent_id]->get_cvv_code($global_collect_trans_info_row['global_collect_cvv_result']) : TEXT_NOT_AVAILABLE)?></td>
			              				</tr>
			  							<tr>
			                				<td class="main" valign="top" nowrap><b><?="AVS result:"?></b>&nbsp;</td>
			                				<td class="main"><?=(tep_not_null($global_collect_trans_info_row['global_collect_avs_result']) ? $payment_methods_obj->payment_gateways_array[$order_obj->payment_methods_parent_id]->get_avs_code($global_collect_trans_info_row['global_collect_avs_result']) : TEXT_NOT_AVAILABLE)?></td>
			              				</tr>
			              				<tr>
			                				<td class="main" valign="top" nowrap><b><?="Fraud Result:"?></b>&nbsp;</td>
			                				<td class="main"><?=(tep_not_null($global_collect_trans_info_row['global_collect_fraud_result']) ? $payment_methods_obj->payment_gateways_array[$order_obj->payment_methods_parent_id]->get_fraud_result_code($global_collect_trans_info_row['global_collect_fraud_result']) : TEXT_NOT_AVAILABLE)?></td>
			              				</tr>
			              				<tr>
			                				<td class="main" valign="top" nowrap><b><?="Fraud Code:"?></b>&nbsp;</td>
			                				<td class="main"><?=(tep_not_null($global_collect_trans_info_row['global_collect_fraud_code']) ? $payment_methods_obj->payment_gateways_array[$order_obj->payment_methods_parent_id]->get_fraud_code($global_collect_trans_info_row['global_collect_fraud_code']) : TEXT_NOT_AVAILABLE)?></td>
			              				</tr>
			        				</table>
			                	</td>
							</tr>
							<tr>
								<td class="main" valign="top" nowrap><b><?=ENTRY_GLOBAL_COLLECT_CURRENCY_CODE?></b>&nbsp;</td>
			                	<td class="main" valign="top" nowrap><?=$global_collect_trans_info_row["global_collect_currency_code"]?></td>
							</tr>
							<tr>
								<td class="main" valign="top" nowrap><b><?=ENTRY_GLOBAL_COLLECT_AMOUNT?></b>&nbsp;</td>
			                	<td class="main" valign="top" nowrap><?=$global_collect_trans_info_row["global_collect_amount"]?></td>
							</tr>
							<tr>
								<td class="main" valign="top" nowrap colspan="2">&nbsp;</td>
							</tr>
							<tr>
								<td class="main" valign="top" nowrap colspan="2">&nbsp;</td>
							</tr>
              			</table>
        			</td>
      			</tr>
      		</table>
   		</td>
  	</tr>
<?
} else {
?>
	<tr>
		<td class="invoiceRecords"><?=ENTRY_GLOBAL_COLLECT_NO_PAYMENT_INFO?></td>
	</tr>
<?
}
?>
</table>