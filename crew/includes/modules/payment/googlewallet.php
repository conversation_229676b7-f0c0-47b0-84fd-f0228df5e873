<?php

require_once('payment_gateway.php');
require_once('googlewallet/classes/JWT.php');

class googlewallet extends payment_gateway {

    var $code, $check_trans_status_flag, $merchant_server_ip_address, $googlewalletCurrencies;

    function googlewallet($pm_id = '') {
        global $order, $languages_id, $default_languages_id, $currency;

        $this->code = 'googlewallet';
        $this->check_trans_status_flag = false;
        $this->merchant_server_ip_address = $_SERVER['SERVER_ADDR'];  //IP address of merchant server
        $this->title = $this->code;
        $this->preferred = true;
        $this->filename = 'googlewallet.php';
        $this->auto_cancel_period = 30; // In minutes
        $this->connect_via_proxy = false; // localhost test - set to true, server test - set to false //need proxy to connect outside
        $this->force_to_checkoutprocess = true;
        $this->has_ipn = true;
        $this->payment_methods_id = 0;
        $this->payment_methods_parent_id = 0;

        $payment_methods_select_sql = "	SELECT  payment_methods_parent_id, payment_methods_code, 
												payment_methods_types_id, 
												payment_methods_receive_status_mode, payment_methods_id, 
												payment_methods_title, payment_methods_sort_order, 
												payment_methods_receive_status, payment_methods_legend_color, 
												payment_methods_logo 
										FROM " . TABLE_PAYMENT_METHODS . "
										WHERE 1 ";
        if ((int) $pm_id > 0) {
            $payment_methods_select_sql .= "	AND payment_methods_id = '" . (int) $pm_id . "'";
        } else {
            $payment_methods_select_sql .= "	AND payment_methods_filename = '" . tep_db_input($this->filename) . "'";
        }

        $payment_methods_result_sql = tep_db_query($payment_methods_select_sql);
        if ($payment_methods_row = tep_db_fetch_array($payment_methods_result_sql)) {
            $pm_desc_title_select_sql = "	SELECT payment_methods_description_title 
											FROM " . TABLE_PAYMENT_METHODS_DESCRIPTION . " 
											WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "' 
												AND (IF (languages_id = '" . tep_db_input($languages_id) . "', 1, IF ((	SELECT COUNT(payment_methods_id) > 0 
																														FROM " . TABLE_PAYMENT_METHODS_DESCRIPTION . " 
																														WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "' 
																															AND languages_id = '" . (int) $languages_id . "'), 0, languages_id = '" . (int) $default_languages_id . "')))";
            $pm_desc_title_result_sql = tep_db_query($pm_desc_title_select_sql);
            $pm_desc_title_row = tep_db_fetch_array($pm_desc_title_result_sql);
            $this->display_title = $pm_desc_title_row['payment_methods_description_title'];
            $this->description = $this->display_title;
            $this->enabled = $payment_methods_row['payment_methods_receive_status_mode'];
            $this->payment_methods_id = (int) $payment_methods_row['payment_methods_id'];
            $this->payment_methods_parent_id = (int) $payment_methods_row['payment_methods_parent_id'];
            $this->code = $payment_methods_row['payment_methods_code'];
            $this->title = $payment_methods_row['payment_methods_title'];
            $this->sort_order = $payment_methods_row['payment_methods_sort_order'];
            $this->legend_display_colour = $payment_methods_row['payment_methods_legend_color'];
        }

        $configuration_setting_array = $this->load_pm_setting();
        $this->order_status_id = $configuration_setting_array['MODULE_PAYMENT_GOOGLEWALLET_ORDER_STATUS_ID'];
        $this->processing_status_id = $configuration_setting_array['MODULE_PAYMENT_GOOGLEWALLET_PROCESSING_STATUS_ID'];
        $this->confirm_complete_days = $configuration_setting_array['MODULE_PAYMENT_GOOGLEWALLET_CONFIRM_COMPLETE'];
        $this->require_address_information = $configuration_setting_array['MODULE_PAYMENT_GOOGLEWALLET_MANDATORY_ADDRESS_FIELD'];
        $this->test_mode = ($configuration_setting_array['MODULE_PAYMENT_GOOGLEWALLET_TEST_MODE'] == 'True' ? true : false);
        $this->message = $configuration_setting_array['MODULE_PAYMENT_GOOGLEWALLET_MESSAGE'];
        $this->email_message = $configuration_setting_array['MODULE_PAYMENT_GOOGLEWALLET_EMAIL_MESSAGE'];
        $this->email_footer = str_replace(array("<br>", "<BR>", "\r\n"), array("\n", "\n", ''), $this->email_message);


        $this->googlewalletCurrencies = $this->get_support_currencies($this->payment_methods_id);
        $this->defCurr = DEFAULT_CURRENCY;
        $this->order_processing_status = ((int) $this->processing_status_id > 0 ? $this->processing_status_id : 7 );
        $this->order_status = ( (int) $this->order_status_id > 0 ? $this->order_status_id : DEFAULT_ORDERS_STATUS_ID );


        if ($this->test_mode) {
            $this->form_action_url = 'https://sandbox.google.com/checkout/inapp/lib/buy.js';
        } else {
            $this->form_action_url = 'https://wallet.google.com/inapp/lib/buy.js';
        }
        $this->checkoutProcessUrl = tep_href_link(FILENAME_CHECKOUT_PROCESS, '', 'SSL');
    }

    function javascript_validation() {
        return false;
    }

    function selection() {
        global $languages_id, $default_languages_id;

        $selection_array = array();

        $payment_methods_select_sql = "	SELECT payment_methods_id, payment_methods_code, payment_methods_types_id, payment_methods_logo, payment_methods_receive_status_mode 
										FROM " . TABLE_PAYMENT_METHODS . " 
										WHERE payment_methods_receive_status = 1 
											AND payment_methods_receive_status_mode <> 0 
											AND payment_methods_parent_id = '" . (int) $this->payment_methods_id . "' 
										ORDER BY payment_methods_sort_order";
        $payment_methods_result_sql = tep_db_query($payment_methods_select_sql);
        while ($payment_methods_row = tep_db_fetch_array($payment_methods_result_sql)) {
            $pm_desc_title_select_sql = "	SELECT payment_methods_description_title 
											FROM " . TABLE_PAYMENT_METHODS_DESCRIPTION . " 
											WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "' 
												AND (IF (languages_id = '" . tep_db_input($languages_id) . "', 1, IF ((	SELECT COUNT(payment_methods_id) > 0 
																														FROM " . TABLE_PAYMENT_METHODS_DESCRIPTION . " 
																														WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "' 
																															AND languages_id = '" . (int) $languages_id . "'), 0, languages_id = 1)))";
            $pm_desc_title_result_sql = tep_db_query($pm_desc_title_select_sql);
            $pm_desc_title_row = tep_db_fetch_array($pm_desc_title_result_sql);

            $pm_array = $this->load_pm_setting(1, $payment_methods_row['payment_methods_id']);

            $selection_array[] = array('payment_gateway_code' => $this->code,
                'payment_methods_receive_status_mode' => $payment_methods_row['payment_methods_receive_status_mode'],
                'payment_methods_id' => $payment_methods_row['payment_methods_id'],
                'payment_methods_code' => $payment_methods_row['payment_methods_code'],
                'payment_methods_types_id' => $payment_methods_row['payment_methods_types_id'],
                'payment_methods_parent_id' => $this->payment_methods_id,
                'payment_methods_logo' => $payment_methods_row['payment_methods_logo'],
                'payment_methods_description_title' => $pm_desc_title_row['payment_methods_description_title'],
                'currency' => $this->get_support_currencies($payment_methods_row['payment_methods_id']),
                'confirm_complete_days' => $this->confirm_complete_days,
                'show_billing_address' => $this->require_address_information,
                'show_contact_number' => $pm_array['MODULE_PAYMENT_GOOGLEWALLET_MANDATORY_CONTACT_FIELD'],
                'show_ic' => $pm_array['MODULE_PAYMENT_GOOGLEWALLET_MANDATORY_IC_FIELD']
            );

            if ($payment_methods_row['payment_methods_receive_status_mode'] == '-1') {
                $pm_status_message_select = "	SELECT payment_methods_status_message 
												FROM " . TABLE_PAYMENT_METHODS_STATUS_DESCRIPTION . " 
												WHERE payment_methods_mode = 'RECEIVE' 
													AND payment_methods_status = '-1' 
													AND languages_id = '" . (int) $languages_id . "' 
													AND payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "'";
                $pm_status_message_result = tep_db_query($pm_status_message_select);
                $pm_status_message_row = tep_db_fetch_array($pm_status_message_result);
                $selection_array[sizeof($selection_array) - 1]['payment_methods_status_message'] = $pm_status_message_row['payment_methods_status_message'];
            }
        }

        return $selection_array;
    }

    function set_support_currencies($currency_array) {
        $this->googlewalletCurrencies = $currency_array;
    }

    function get_require_address_information() {
        return $this->require_address_information;
    }

    function draw_confirm_button() {
        return tep_image_button2('red', 'javascript:document.checkout_confirmation.submit()', MODULE_PAYMENT_GOOGLEWALLET_TEXT_CART, 220);
    }

    function get_confirm_button_caption() {
        return MODULE_PAYMENT_GOOGLEWALLET_TEXT_TITLE;
    }

    function pre_confirmation_check() {
        global $currency;

        if (in_array($currency, $this->googlewalletCurrencies)) {
            
        }

        return false;
    }

    function confirmation() {
        
    }

    function process_button() {
        global $HTTP_POST_VARS, $shipping_cost, $total_cost, $shipping_selected, $shipping_method, $currencies, $currency, $customer_id, $order, $languages_id;
        global $order_logged, $OFFGAMERS, $country;

        if ($this->force_to_checkoutprocess && !tep_session_is_registered('order_logged'))
            return;

        $default_currency = $this->defCurr;
        $googlewalletCurrency = $currency;
        if (!in_array($googlewalletCurrency, $this->googlewalletCurrencies)) {
            $googlewalletCurrency = in_array($default_currency, $this->googlewalletCurrencies) ? $default_currency : $this->googlewalletCurrencies[0];
        }
        $this->get_merchant_account($googlewalletCurrency);
        $OrderAmt = number_format($order->info['total'] * $currencies->get_value($googlewalletCurrency), $currencies->get_decimal_places($googlewalletCurrency), '.', '');
        $expiry = time() + 3600;
        $currentTime = time();
        $customString = $this->merchant_id . $order_logged . $OrderAmt . $googlewalletCurrency . $this->secret_word . $this->ogm_secret_word;
        $customSignature = hash('sha256', $customString);
        $customData = array('signature' => $customSignature);
        $googleWalletParams = array(
            "iss" => $this->merchant_id,
            "aud" => "Google",
            "typ" => "google/payments/inapp/item/v1",
            "exp" => $expiry,
            "iat" => $currentTime,
            "request" => array(
                "name" => $order_logged,
                "description" => 'Purchase from ' . STORE_NAME,
                "price" => $OrderAmt,
                "currencyCode" => $googlewalletCurrency,
                "sellerData" => json_encode($customData),
            )
        );
        $checkoutJWT = JWT::encode($googleWalletParams, $this->secret_word);

        $process_button_string = '  
                                <script type="text/javascript">
                                    google.payments.inapp.buy({
                                                                parameters: {},
                                                                jwt: "' . $checkoutJWT . '",
                                                                success: function(successResult) {
                                                                                                    post2Url("' . $this->checkoutProcessUrl . '", JSON.stringify(successResult));
                                                                },
                                                                failure: function(failureResult) { 
																									post2Url("' . $this->checkoutProcessUrl . '", JSON.stringify(failureResult));
																}
                                    });

								function post2Url(path, params) {
									var form = document.createElement("form");
									form.setAttribute("method", "post");
									form.setAttribute("action", path);
									
									var hiddenField = document.createElement("input");
									hiddenField.setAttribute("type", "hidden");
									hiddenField.setAttribute("name", "gwData");
									hiddenField.setAttribute("value", params);

									form.appendChild(hiddenField);
									
									document.body.appendChild(form);
									form.submit();
								}
                                </script>';

        return $process_button_string;
    }

    function postValidation($postData) {
        global $currencies;
        if (isset($postData['request']['name']) && $postData['request']['name']) {
            $order = new order($postData['request']['name']);
            if (isset($order->info['payment_methods_id']) && $order->info['payment_methods_id'] == $this->payment_methods_id) {
                if (isset($postData['request']['currencyCode']) && $postData['request']['currencyCode'] == $order->info['currency']) {
                    $OrderAmt = number_format($order->info['total_value'] * $currencies->get_value($order->info['currency']), $currencies->get_decimal_places($order->info['currency']), '.', '');
                    if (isset($postData['request']['price']) && bccomp($OrderAmt, $postData['request']['price']) == 0) {
                        $googlewalletHistoryDataArray = array('google_wallet_order_id' => tep_db_prepare_input($postData['request']['name']),
                            'google_wallet_date' => date('Y-m-d H:i:s'),
                            'google_wallet_changed_by' => 'SYSTEM'
                        );
                        if (isset($postData['jwt']) && !empty($postData['jwt'])) { //success event
                            $this->get_merchant_account($order->info['currency']);
                            $decodedInputData = JWT::decode($postData['jwt'], $this->secret_word);
                            if (isset($decodedInputData->aud) && $decodedInputData->aud == $this->merchant_id) {
                                $gw_data_select_sql = "	SELECT google_wallet_return_jwt, google_wallet_create_date, google_wallet_expired_date, google_wallet_transaction_id
														FROM " . TABLE_GOOGLE_WALLET . " 
														WHERE google_wallet_order_id = '" . tep_db_input($postData['request']['name']) . "'";
                                $gw_data_result_sql = tep_db_query($gw_data_select_sql);
                                if ($googleWalletTransData = tep_db_fetch_array($gw_data_result_sql)) {
                                    if (isset($googleWalletTransData['google_wallet_return_jwt']) && $googleWalletTransData['google_wallet_return_jwt'] == $postData['jwt']) {
                                        if (strtotime($googleWalletTransData['google_wallet_create_date']) < time() && strtotime($googleWalletTransData['google_wallet_expired_date']) > time()) {
                                            if (isset($decodedInputData->response->orderId) && $decodedInputData->response->orderId == $googleWalletTransData['google_wallet_transaction_id']) {
                                                $googlewalletCurrency = $decodedInputData->request->currencyCode;
                                                $googlewalletAmount = $decodedInputData->request->price;
                                                $googlewalletGrossAmt = number_format(($googlewalletAmount), $currencies->currencies[$googlewalletCurrency]['decimal_places'], $currencies->currencies[$googlewalletCurrency]['decimal_point'], $currencies->currencies[$googlewalletCurrency]['thousands_point']);
                                                $googlewalletAmount = $currencies->currencies[$googlewalletCurrency]['symbol_left'] . $googlewalletGrossAmt . $currencies->currencies[$googlewalletCurrency]['symbol_right'];
                                                if ($googlewalletAmount == $order->info['total']) {
                                                    $sellerData = json_decode($decodedInputData->request->sellerData, 1);
                                                    $customString = $this->merchant_id . $order->order_id . $OrderAmt . $order->info['currency'] . $this->secret_word . $this->ogm_secret_word;
                                                    $customSignature = hash('sha256', $customString);
                                                    if ($sellerData['signature'] == $customSignature) {
                                                        $this->process_this_order($order->order_id, $this, $this->order_processing_status, 'Success');
                                                        $googlewalletHistoryDataArray['google_wallet_status_id'] = 'SUCCESS';
                                                    } else {
                                                        ob_start();
                                                        echo "========================calculated_googlewallet_signature=========================<BR>";
                                                        echo "'" . $customSignature . "' == '" . $sellerData['signature'] . "'";
                                                        echo "============================================================================<BR>";
                                                        $debug_html = ob_get_contents();
                                                        ob_end_clean();
                                                        @tep_mail('<EMAIL>', '<EMAIL>', $order->order_id . 'INVALID GOOGLEWALLET SIGNATURE', $debug_html, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        tep_db_perform(TABLE_GOOGLE_WALLET_STATUS_HISTORY, $googlewalletHistoryDataArray);
                    }
                }
            }
        }
    }

    function before_process() {
        global $order, $order_logged, $currency;

        if (!tep_session_is_registered('order_logged'))
            return;

        $inputData = array();
        if (isset($_POST['gwData'])) {
            $inputData = json_decode(stripslashes($_POST['gwData']), 1);
        }
        if (tep_not_null($inputData['response']['errorType'])) {
            switch ($inputData['response']['errorType']) {
                case "MERCHANT_ERROR":
                case "POSTBACK_ERROR":
                case "INTERNAL_SERVER_ERROR":
                    $error_message = MODULE_PAYMENT_GOOGLEWALLET_TEXT_ERROR_MESSAGE;
                    break;
                case "PURCHASE_CANCELED" || "PURCHASE_CANCELLED":
                    $error_message = MODULE_PAYMENT_GOOGLEWALLET_CANCEL_PAYMENT_ERROR_MESSAGE;
                    break;
                default:
                    $error_message = MODULE_PAYMENT_GOOGLEWALLET_TEXT_ERROR_MESSAGE;
            }
            $googlewalletHistoryDataArray = array('google_wallet_order_id' => tep_db_prepare_input($inputData['request']['name']),
                'google_wallet_date' => date('Y-m-d H:i:s'),
                'google_wallet_changed_by' => 'SYSTEM',
                'google_wallet_status_id' => tep_db_prepare_input($inputData['response']['errorType']),
                'google_wallet_description' => $this->googleWalletErrorCollection($inputData['response']['errorType'])
            );
            tep_db_perform(TABLE_GOOGLE_WALLET_STATUS_HISTORY, $googlewalletHistoryDataArray);
            tep_redirect(tep_href_link(FILENAME_CHECKOUT_PAYMENT, 'error_message=' . $error_message, 'SSL', true, false));
        } else {
            $this->postValidation($inputData);
        }
    }

    function after_process() {
        return false;
    }

    function link_to_payment($new_order_id) {
        global $order_logged;

        if (tep_session_is_registered('order_logged'))
            tep_session_unregister('order_logged');

        tep_session_register('order_logged');
        $order_logged = $new_order_id;
        require(DIR_WS_INCLUDES . 'modules/payment/googlewallet/catalog/gw_splash.php');
        return;
    }

    function is_supported_currency($selected_currency) {
        return (in_array($selected_currency, $this->googlewalletCurrencies) ? true : false);
    }

    function check_trans_status($order_id, $ipn_call = 0) {
        
    }

    function output_error() {
        return false;
    }

    function check() {
        if (!isset($this->_check)) {
            $payment_methods_select_sql = "	SELECT payment_methods_id
											FROM " . TABLE_PAYMENT_METHODS . " as pm
											WHERE pm.payment_methods_id = '" . (int) $this->payment_methods_id . "'";
            $payment_methods_result_sql = tep_db_query($payment_methods_select_sql);
            $this->_check = tep_db_num_rows($payment_methods_result_sql);
        }
        return $this->_check;
    }

    function is_processed_order($order_id) {
        
    }

    function load_pm_setting($language_id = 1, $pm_id = '') {
        $pm_setting_array = array();
        //load value from DB

        if (tep_not_null($pm_id)) {
            $payment_method_id = $pm_id;

            $payment_methods_parent_id_select_sql = "	SELECT payment_methods_parent_id 
														FROM " . TABLE_PAYMENT_METHODS . " 
														WHERE payment_methods_id = '" . (int) $pm_id . "'";
            $payment_methods_parent_id_result_sql = tep_db_query($payment_methods_parent_id_select_sql);
            $payment_methods_parent_id_row = tep_db_fetch_array($payment_methods_parent_id_result_sql);

            $payment_gateway_id = $payment_methods_parent_id_row['payment_methods_parent_id'];
        } else {
            $payment_method_id = $this->payment_methods_id;
            $payment_gateway_id = $this->payment_methods_parent_id;
        }

        $payment_configuration_info_select_sql = "	SELECT pci.payment_configuration_info_key, pcid.payment_configuration_info_value
													FROM " . TABLE_PAYMENT_CONFIGURATION_INFO . " as pci
													LEFT JOIN " . TABLE_PAYMENT_CONFIGURATION_INFO_DESCRIPTION . " as pcid 
														ON pci.payment_configuration_info_id = pcid.payment_configuration_info_id
															AND pcid.languages_id = '" . (int) $language_id . "'
													WHERE pci.payment_methods_id = '" . (int) $payment_method_id . "'
													ORDER BY pci.payment_configuration_info_sort_order";
        $payment_configuration_info_result_sql = tep_db_query($payment_configuration_info_select_sql);
        if (tep_db_num_rows($payment_configuration_info_result_sql)) {
            while ($payment_configuration_info_row = tep_db_fetch_array($payment_configuration_info_result_sql)) {
                $pm_setting_array[$payment_configuration_info_row['payment_configuration_info_key']] = $payment_configuration_info_row['payment_configuration_info_value'];
            }
        } else if ((int) $payment_gateway_id > 0) { //load value from PG
            $payment_configuration_info_select_sql = "	SELECT pci.payment_configuration_info_key, pcid.payment_configuration_info_value
														FROM " . TABLE_PAYMENT_CONFIGURATION_INFO . " as pci
														LEFT JOIN " . TABLE_PAYMENT_CONFIGURATION_INFO_DESCRIPTION . " as pcid 
															ON pci.payment_configuration_info_id = pcid.payment_configuration_info_id
																AND pcid.languages_id = '" . (int) $language_id . "'
														WHERE pci.payment_methods_id = '" . (int) $payment_gateway_id . "'
														ORDER BY pci.payment_configuration_info_sort_order";
            $payment_configuration_info_result_sql = tep_db_query($payment_configuration_info_select_sql);
            while ($payment_configuration_info_row = tep_db_fetch_array($payment_configuration_info_result_sql)) {
                $pm_setting_array[$payment_configuration_info_row['payment_configuration_info_key']] = $payment_configuration_info_row['payment_configuration_info_value'];
            }
        }

        return $pm_setting_array;
    }

    function get_merchant_account($selected_currency) {
        $payment_methods_instance_id = 0;
        $payment_gateway_instance_select_sql = "SELECT pmi.payment_methods_instance_id, pmi.payment_methods_instance_follow_default, pmi.currency_code 
				 								FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " as pmi
				 								WHERE pmi.payment_methods_id = '" . (int) $this->payment_methods_id . "'
				 								LIMIT 1";
        $payment_gateway_instance_result_sql = tep_db_query($payment_gateway_instance_select_sql);
        if (tep_db_num_rows($payment_gateway_instance_result_sql)) {
            $payment_gateway_instance_select_sql = "SELECT pmi.payment_methods_instance_id, pmi.payment_methods_instance_follow_default 
					 								FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " as pmi
					 								WHERE pmi.currency_code = '" . tep_db_input($selected_currency) . "' 
					 									AND pmi.payment_methods_id = '" . (int) $this->payment_methods_id . "'";
            $payment_gateway_instance_result_sql = tep_db_query($payment_gateway_instance_select_sql);
            if ($payment_gateway_instance_row = tep_db_fetch_array($payment_gateway_instance_result_sql)) {
                if ((int) $payment_gateway_instance_row['payment_methods_instance_follow_default'] > 0) { // Follow Default Currency Setting
                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_follow_default'];
                } else { // Has own setting
                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_id'];
                }
            }
        } else {
            $payment_gateway_instance_select_sql = "SELECT pmi.payment_methods_instance_id, pmi.payment_methods_instance_follow_default 
					 								FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " as pmi
					 								INNER JOIN " . TABLE_PAYMENT_METHODS . " as pm
					 									ON pm.payment_methods_parent_id = pmi.payment_methods_id
					 								WHERE pmi.currency_code = '" . tep_db_input($selected_currency) . "' 
					 									AND pm.payment_methods_id = '" . (int) $this->payment_methods_id . "'";
            $payment_gateway_instance_result_sql = tep_db_query($payment_gateway_instance_select_sql);
            if ($payment_gateway_instance_row = tep_db_fetch_array($payment_gateway_instance_result_sql)) {
                if ((int) $payment_gateway_instance_row['payment_methods_instance_follow_default'] > 0) {
                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_follow_default'];
                } else {
                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_id'];
                }
            }
        }

        $payment_gateway_instance_setting_select_sql = "SELECT pmis.payment_methods_instance_setting_key, pmis.payment_methods_instance_setting_value 
						 								FROM " . TABLE_PAYMENT_METHODS_INSTANCE_SETTING . " as pmis
						 								WHERE pmis.payment_methods_instance_id = '" . (int) $payment_methods_instance_id . "'";
        $payment_gateway_instance_setting_result_sql = tep_db_query($payment_gateway_instance_setting_select_sql);
        while ($payment_gateway_instance_setting_row = tep_db_fetch_array($payment_gateway_instance_setting_result_sql)) {
            switch ($payment_gateway_instance_setting_row['payment_methods_instance_setting_key']) {
                case 'MODULE_PAYMENT_GOOGLEWALLET_ID':
                    $this->merchant_id = $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];
                    break;
                case 'MODULE_PAYMENT_GOOGLEWALLET_SECRET':
                    $this->secret_word = $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];
                    break;
                case 'MODULE_PAYMENT_OGM_SECRET':
                    $this->ogm_secret_word = $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];
                    break;
            }
        }
    }

    function install() {
        $install_configuration_flag = true;
        if ($this->payment_methods_id > 0) {
            if ($this->receive_status != 1) {
                $update_receive_status_sql_data = array("payment_methods_receive_status" => 1);
                tep_db_perform(TABLE_PAYMENT_METHODS, $update_receive_status_sql_data, 'update', "payment_methods_id = '" . (int) $this->payment_methods_id . "'");
            } else {
                $install_configuration_flag = false;
            }
        } else {
            $install_payment_gateway_data_sql = array('payment_methods_code' => $this->code,
                'payment_methods_send_status' => 0,
                'payment_methods_title' => $this->code,
                'payment_methods_types_id' => 0,
                'payment_methods_parent_id' => 0,
                'payment_methods_legend_color' => '#19D119',
                'payment_methods_sort_order' => 6,
                'payment_methods_filename' => $this->filename,
                'payment_methods_logo' => '',
                'date_added' => 'now()',
                'last_modified' => 'now()',
                'payment_methods_receive_status' => 1,
                'payment_methods_receive_status_mode' => 0
            );
            tep_db_perform(TABLE_PAYMENT_METHODS, $install_payment_gateway_data_sql);
            $this->payment_methods_id = tep_db_insert_id();
        }
        if ($install_configuration_flag) {
            $install_key_array = array();
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Set Order Status',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_GOOGLEWALLET_ORDER_STATUS_ID',
                    'payment_configuration_info_description' => 'Set the status of orders made with this payment module to this value.',
                    'payment_configuration_info_sort_order' => '1335',
                    'set_function' => 'tep_cfg_pull_down_order_statuses(',
                    'use_function' => 'tep_get_order_status_name',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => '1',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Set Order\'s "Processing" Status',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_GOOGLEWALLET_PROCESSING_STATUS_ID',
                    'payment_configuration_info_description' => 'Set the initial processing status of orders made with this payment module to this value.',
                    'payment_configuration_info_sort_order' => '1340',
                    'set_function' => 'tep_cfg_pull_down_order_statuses(',
                    'use_function' => 'tep_get_order_status_name',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => '7',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Payment Message',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_GOOGLEWALLET_MESSAGE',
                    'payment_configuration_info_description' => 'Payment message will show up during checkout process (smart2pay).',
                    'payment_configuration_info_sort_order' => '1350',
                    'set_function' => 'tep_cfg_textarea(',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'Thank you for shopping at OffGamers.com.',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Payment Email Message',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_GOOGLEWALLET_EMAIL_MESSAGE',
                    'payment_configuration_info_description' => 'This message will show up in order confirmation e-mail.',
                    'payment_configuration_info_sort_order' => '1360',
                    'set_function' => 'tep_cfg_textarea(',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'Thank you for shopping at OffGamers.com.',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Confirm Complete (in days)',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_GOOGLEWALLET_CONFIRM_COMPLETE',
                    'payment_configuration_info_description' => 'The number of days in which an order made by this payment method is confirm completed.',
                    'payment_configuration_info_sort_order' => '1265',
                    'set_function' => '',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => '0',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Test Mode',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_GOOGLEWALLET_TEST_MODE',
                    'payment_configuration_info_description' => 'Testing Mode?',
                    'payment_configuration_info_sort_order' => '100',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Require Address Information',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_GOOGLEWALLET_MANDATORY_ADDRESS_FIELD',
                    'payment_configuration_info_description' => 'Set address field as required info during checkout.',
                    'payment_configuration_info_sort_order' => '1400',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );
            foreach ($install_key_array as $data) {
                tep_db_perform(TABLE_PAYMENT_CONFIGURATION_INFO, $data['info']);
                $payment_conf_id = tep_db_insert_id();

                $data['desc']['payment_configuration_info_id'] = (int) $payment_conf_id;
                tep_db_perform(TABLE_PAYMENT_CONFIGURATION_INFO_DESCRIPTION, $data['desc']);
            }
        }
        return 1;
    }

    function configuration_information_keys() {
        return array('MODULE_PAYMENT_SMART2PAY_ORDER_STATUS_ID',
            'MODULE_PAYMENT_SMART2PAY_PROCESSING_STATUS_ID',
            'MODULE_PAYMENT_SMART2PAY_CONFIRM_COMPLETE',
            'MODULE_PAYMENT_SMART2PAY_TEST_MODE',
            'MODULE_PAYMENT_SMART2PAY_MANDATORY_ADDRESS_FIELD');
    }

    function multi_lang_configuration_info_keys() {
        return array('MODULE_PAYMENT_SMART2PAY_MESSAGE',
            'MODULE_PAYMENT_SMART2PAY_EMAIL_MESSAGE');
    }

    function merchant_information_keys() {
        include_once(DIR_WS_CLASSES . "payment_methods.php");

        return array(
            'MODULE_PAYMENT_GOOGLEWALLET_ID' => array('field' => 'text', 'mapping' => 'MODULE_PAYMENT_GOOGLEWALLET_LNG_ID'),
            'MODULE_PAYMENT_GOOGLEWALLET_SECRET' => array('field' => 'text', 'mapping' => 'MODULE_PAYMENT_GOOGLEWALLET_LNG_SECRET'),
            'MODULE_PAYMENT_OGM_SECRET' => array('field' => 'text', 'mapping' => 'MODULE_PAYMENT_GOOGLEWALLET_LNG_OGM_SECRETT'),
            'MODULE_PAYMENT_TAX' => array("field" => 'select', 'mapping' => "MODULE_PAYMENT_TAX", "select" => payment_methods::get_tax_option())
        );
    }

    private function googleWalletErrorCollection($errorKey) {
        switch ($errorKey) {
            case 'MERCHANT_ERROR':
                return 'purchase request contains errors such as a badly formatted JWT';
                break;

            case 'PURCHASE_CANCELLED':
                return 'buyer cancelled purchase or declined payment';
                break;

            case 'POSTBACK_ERROR':
                return 'failure to acknowledge postback notification';
                break;

            case 'INTERNAL_SERVER_ERROR':
                return 'internal Google error';
                break;
            default:
                return 'Payment Failed due to unknown condition';
        }
    }

    function process_this_order($orders_id, $payment_method_obj, $update_to_status, $payment_status) {
        global $log_object, $currencies, $languages_id;
        $orders_status_array = array();
        include_once(DIR_WS_CLASSES . 'anti_fraud.php');
        require_once(DIR_WS_CLASSES . 'log.php');

        $log_object = new log_files('system');
        $orders_status_query = tep_db_query("SELECT orders_status_id, orders_status_name FROM " . TABLE_ORDERS_STATUS . " WHERE language_id = '1' ORDER BY orders_status_sort_order");
        while ($orders_status = tep_db_fetch_array($orders_status_query)) {
            $orders_status_array[$orders_status['orders_status_id']] = $orders_status['orders_status_name'];
        }

        // Call this function when money reach our account
        $cur_order = new order($orders_id);

        if ($update_to_status == 7) {
            $order_process_status = 1;
        } else {
            $order_process_status = -1;
        }

        if ($cur_order->info['orders_status_id'] == $payment_method_obj->order_status) { // If the order still in initial status
            $order_comment = '';
            $deduct_stock_for_automated_payment = false;

            if ($order_process_status > 0) {
                $order_comment = sprintf(MODULE_PAYMENT_GOOGLEWALLET_TEXT_COMMENT, $payment_status);
                $deduct_stock_for_automated_payment = true;
            }

            $customer_notification = (SEND_EMAILS == 'true') ? '1' : '0';

            $orders_status_history_perform_array = array('action' => 'insert',
                'data' => array('orders_id' => $orders_id,
                    'orders_status_id' => $update_to_status,
                    'date_added' => 'now()',
                    'customer_notified' => $customer_notification,
                    'comments' => $order_comment
                )
            );

            $cur_order->update_order_status($update_to_status, $orders_status_history_perform_array, $deduct_stock_for_automated_payment);

            unset($orders_status_history_perform_array);
        }

        $customer_profile_select_sql = "SELECT customers_gender, customers_firstname, customers_lastname 
										FROM " . TABLE_CUSTOMERS . " 
										WHERE customers_id = '" . tep_db_input($cur_order->customer['id']) . "'";
        $customer_profile_result_sql = tep_db_query($customer_profile_select_sql);
        if ($customer_profile_row = tep_db_fetch_array($customer_profile_result_sql)) {
            $email_firstname = $customer_profile_row["customers_firstname"];
            $email_lastname = $customer_profile_row["customers_lastname"];
        } else {
            $email_firstname = $cur_order->customer['name'];
            $email_lastname = $cur_order->customer['name'];
        }

        $email_greeting = tep_get_email_greeting($email_firstname, $email_lastname, $customer_profile_row["customers_gender"]);

        if ($order_process_status > 0) {
            $email_order = $email_greeting . sprintf(EMAIL_TEXT_ORDER_THANK_YOU, $cur_order->info['total']) . "\n\n";

            if (is_object($payment_method_obj)) {
                $email_order .= EMAIL_TEXT_PAYMENT_METHOD . "\n" .
                        EMAIL_SEPARATOR . "\n";
                $payment_class = $payment_method_obj;
                $email_order .= strip_tags($payment_class->title) . "\n";
                if ($payment_class->email_footer) {
                    $email_order .= $payment_class->email_footer . "\n\n";
                } else {
                    $email_order .= "\n";
                }
            }

            if ($cur_order->delivery !== false) {
                $order_shipping_address = tep_address_format($cur_order->delivery['format_id'], $cur_order->delivery, 0, '', "\n");
                $email_order .= EMAIL_TEXT_DELIVERY_ADDRESS . "\n" .
                        EMAIL_SEPARATOR . "\n" .
                        $order_shipping_address . "\n\n";
            }

            $email_order .= EMAIL_TEXT_BILLING_ADDRESS . "\n" .
                    EMAIL_SEPARATOR . "\n" .
                    tep_address_format($cur_order->billing['format_id'], $cur_order->billing, 0, '', "\n") . "\n\n";

            $email_order .= EMAIL_TEXT_ORDER_SUMMARY . "\n" .
                    EMAIL_SEPARATOR . "\n" .
                    EMAIL_TEXT_ORDER_NUMBER . ' ' . $orders_id . "\n" .
                    EMAIL_TEXT_DATE_ORDERED . ' ' . tep_date_long($cur_order->info['date_purchased']) . ".\n\n";

            $email_order .= EMAIL_TEXT_PRODUCTS . "\n" .
                    EMAIL_SEPARATOR . "\n" .
                    $this->get_products_ordered($cur_order) .
                    EMAIL_SEPARATOR . "\n";

            for ($i = 0, $n = sizeof($cur_order->totals); $i < $n; $i++) {
                $email_order .= strip_tags($cur_order->totals[$i]['title']) . ' ' . strip_tags($cur_order->totals[$i]['text']) . "\n";
            }

            $orders_history_query = tep_db_query("select comments from " . TABLE_ORDERS_STATUS_HISTORY . " where orders_id = '" . tep_db_input($orders_id) . "' order by date_added limit 1");
            if (tep_db_num_rows($orders_history_query)) {
                $orders_history = tep_db_fetch_array($orders_history_query);
                $cur_order->info['comments'] = $orders_history['comments'];
            }

            if ($order->info['comments']) {
                $email_order .= "\n" . EMAIL_TEXT_EXTRA_INFO . "\n" .
                        tep_db_output(tep_db_prepare_input($cur_order->info['comments']));
            }

            $email_order .= "\n\n";

            $email_order = $email_order . EMAIL_TEXT_CLOSING . "\n\n" . EMAIL_FOOTER;

            tep_mail($cur_order->customer['name'], $cur_order->customer['email_address'], implode(' ', array(EMAIL_SUBJECT_PREFIX, sprintf(EMAIL_TEXT_SUBJECT, tep_db_input($orders_id)))), $email_order, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);

            // send emails to other people
            if (tep_not_null(SEND_EXTRA_ORDER_EMAILS_TO)) {
                // A copy of email to admin when new order is made
                $extra_email_array = explode(',', SEND_EXTRA_ORDER_EMAILS_TO);
                $extra_email_email_pattern = "/([^<]*?)(?:<)([^>]+)(?:>)/is";
                for ($receiver_cnt = 0; $receiver_cnt < count($extra_email_array); $receiver_cnt++) {
                    if (preg_match($extra_email_email_pattern, $extra_email_array[$receiver_cnt], $regs)) {
                        $receiver_name = trim($regs[1]);
                        $receiver_email = trim($regs[2]);
                        tep_mail($receiver_name, $receiver_email, implode(' ', array(EMAIL_SUBJECT_PREFIX, sprintf(EMAIL_TEXT_SUBJECT, tep_db_input($orders_id)))), $email_order, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
                    }
                }
            }

            // Send order update 
            tep_status_update_notification('C', tep_db_input($orders_id), 'system', $cur_order->info['orders_status_id'], $update_to_status, 'A');

            // Send affiliate notification e-mail
            tep_send_affiliate_notification($orders_id, $cur_order);
        } else {
            $email_order = $email_greeting . EMAIL_TEXT_STATUS_UPDATE_TITLE . EMAIL_TEXT_ORDER_NUMBER . ' ' . tep_db_input($orders_id) . "\n"
                    . EMAIL_TEXT_DATE_ORDERED . ' ' . tep_date_long($cur_order->info['date_purchased']) . ".\n\n";

            $email_order .= sprintf(EMAIL_TEXT_UPDATED_STATUS, ((int) $update_to_status > 0 ? $orders_status_array[$cur_order->info['orders_status_id']] . ' -> ' . $orders_status_array[$update_to_status] : $orders_status_array[$cur_order->info['orders_status_id']]))
                    . EMAIL_TEXT_CLOSING . "\n\n" . EMAIL_FOOTER;

            tep_mail($cur_order->customer['name'], $cur_order->customer['email_address'], implode(' ', array(EMAIL_SUBJECT_PREFIX, sprintf(EMAIL_TEXT_ORDER_UPDATE_SUBJECT, tep_db_input($orders_id)))), $email_order, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
        }
    }

    function get_products_ordered($order) {
        global $currencies, $languages_id;

        $products_ordered = '';
        for ($i = 0, $n = sizeof($order->products); $i < $n; $i++) {
            //------insert customer choosen option to order--------
            $attributes_exist = '0';
            $products_ordered_attributes = '';
            if (isset($order->products[$i]['attributes'])) {
                $attributes_exist = '1';
                for ($j = 0, $n2 = sizeof($order->products[$i]['attributes']); $j < $n2; $j++) {
                    if (DOWNLOAD_ENABLED == 'true') {
                        $attributes_query = "	select popt.products_options_name, poval.products_options_values_name, pa.options_values_price, pa.price_prefix, pad.products_attributes_maxdays, pad.products_attributes_maxcount , pad.products_attributes_filename
		                               			from " . TABLE_PRODUCTS_OPTIONS . " popt, " . TABLE_PRODUCTS_OPTIONS_VALUES . " poval, " . TABLE_PRODUCTS_ATTRIBUTES . " pa
		                               			left join " . TABLE_PRODUCTS_ATTRIBUTES_DOWNLOAD . " pad
		                                			on pa.products_attributes_id=pad.products_attributes_id
		                               			where pa.products_id = '" . $order->products[$i]['id'] . "'
					                                and pa.options_id = '" . $order->products[$i]['attributes'][$j]['option_id'] . "'
					                                and pa.options_id = popt.products_options_id
					                                and pa.options_values_id = '" . $order->products[$i]['attributes'][$j]['value_id'] . "'
					                                and pa.options_values_id = poval.products_options_values_id
					                                and popt.language_id = '" . $languages_id . "'
					                                and poval.language_id = '" . $languages_id . "'";
                        $attributes = tep_db_query($attributes_query);
                    } else {
                        $attributes = tep_db_query("select popt.products_options_name, poval.products_options_values_name, pa.options_values_price, pa.price_prefix from " . TABLE_PRODUCTS_OPTIONS . " popt, " . TABLE_PRODUCTS_OPTIONS_VALUES . " poval, " . TABLE_PRODUCTS_ATTRIBUTES . " pa where pa.products_id = '" . $order->products[$i]['id'] . "' and pa.options_id = '" . $order->products[$i]['attributes'][$j]['option_id'] . "' and pa.options_id = popt.products_options_id and pa.options_values_id = '" . $order->products[$i]['attributes'][$j]['value_id'] . "' and pa.options_values_id = poval.products_options_values_id and popt.language_id = '" . $languages_id . "' and poval.language_id = '" . $languages_id . "'");
                    }
                    $attributes_values = tep_db_fetch_array($attributes);
                    if ((DOWNLOAD_ENABLED == 'true') && isset($attributes_values['products_attributes_filename']) && tep_not_null($attributes_values['products_attributes_filename'])) {
                        $sql_data_array = array('orders_id' => $orders_id,
                            'orders_products_id' => $order->products[$i]['orders_products_id'],
                            'orders_products_filename' => $attributes_values['products_attributes_filename'],
                            'download_maxdays' => $attributes_values['products_attributes_maxdays'],
                            'download_count' => $attributes_values['products_attributes_maxcount']);
                        tep_db_perform(TABLE_ORDERS_PRODUCTS_DOWNLOAD, $sql_data_array);
                    }
                    $products_ordered_attributes .= "\n\t" . $attributes_values['products_options_name'] . ' ' . $attributes_values['products_options_values_name'];
                }
            }
            //------insert customer choosen option eof ----
            $total_weight += ($order->products[$i]['qty'] * $order->products[$i]['weight']);
            $total_tax += tep_calculate_tax($total_products_price, $products_tax) * $order->products[$i]['qty'];
            $total_cost += $total_products_price;

            $cat_path = tep_output_generated_category_path($order->products[$i]['id'], 'product');
            $product_email_display_str = $cat_path . (tep_not_null($cat_path) ? ' > ' : '') . $order->products[$i]['name'] . (tep_not_null($order->products[$i]['model']) ? ' (' . $order->products[$i]['model'] . ')' : '');

            $products_ordered .= $order->products[$i]['qty'] . ' x ' . $product_email_display_str . ' = ' . $currencies->format($order->products[$i]['final_price'] * $order->products[$i]['qty'], true, $order->info['currency'], $order->info['currency_value']) . $products_ordered_attributes . "\n";
        }

        return $products_ordered;
    }

    function get_orders_payment_info_file() {
        return DIR_FS_CATALOG_MODULES . 'payment/googlewallet/admin/orders.inc.php';
    }

    function get_orders_list_payment_info_file() {
        return DIR_FS_CATALOG_MODULES . 'payment/googlewallet/admin/orders_list.inc.php';
    }

}

?>