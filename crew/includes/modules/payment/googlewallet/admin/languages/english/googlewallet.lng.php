<?
define('TABLE_HEADING_GOOG<PERSON>WALLET_DATE', 'History Date');
define('TABLE_HEADING_GOOGLEWALLET_STATUS', 'Status');
define('TABLE_HEADING_GOOGLEWALLET_DESCRIPTION', 'Description');
define('TABLE_HEADING_GOOGLEWALLET_CHANGED_BY', 'Changed By');

//Define Merchant Key Language
define('MODULE_PAYMENT_GOOGLEWALLET_LNG_ID', 'GoogleWallet ID');
define('MODULE_PAYMENT_GOOGLEWALLET_LNG_SECRET', 'GoogleWallet Secret');
define('MODULE_PAYMENT_GOOGLEWALLET_LNG_OGM_SECRETT', 'OffGamers Secret(not from Google)');
define('MODULE_PAYMENT_TAX', 'Tax');

define('ENTRY_GOOGLEWALLET_TRANSACTION_ID', 'Transaction ID');
define('ENTRY_GOOGLEWALLET_CURRENCY', 'Currency');
define('ENTRY_GOOGLEWALLET_AMOUNT', 'Amount');
define('ENTRY_GOOGLEWALLET_CREATE_DATE', 'Created Date');
?>