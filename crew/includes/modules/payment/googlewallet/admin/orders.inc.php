<?php
include_once(DIR_FS_CATALOG_MODULES . 'payment/googlewallet/admin/languages/' . $language . '/googlewallet.lng.php');

$googlewallet_trans_info_select_sql = "	SELECT google_wallet_transaction_id, google_wallet_create_date, google_wallet_amount, google_wallet_currency
										FROM " . TABLE_GOOGLE_WALLET . " 
										WHERE google_wallet_order_id = '" . (int)$oID . "'";
$googlewallet_trans_info_result_sql= tep_db_query($googlewallet_trans_info_select_sql);

$googlewallet_trans_history_select_sql = "	SELECT google_wallet_status_id, google_wallet_date, google_wallet_description, google_wallet_changed_by
											FROM " . TABLE_GOOGLE_WALLET_STATUS_HISTORY . "
                                            WHERE google_wallet_order_id = '" . (int)$oID  . "' 
                                            ORDER BY google_wallet_date";
$googlewallet_trans_history_result_sql = tep_db_query($googlewallet_trans_history_select_sql);
?>
<table border="0" width="100%" cellspacing="0" cellpadding="2">
<? ob_start(); ?>
	<tr>
		<td class="main">
      		<table border="0" width="100%" cellspacing="0" cellpadding="2">
      			<tr>
      				<td class="main" width="12%" nowrap><b><?=ENTRY_PAYMENT_METHOD?></b></td>
      				<td class="main">
      					<div>
      						<? if((int)$order->info['payment_methods_id']>0) { ?><div class="<?=((int)$payment_info_array[$order->info['payment_methods_id']]->confirm_complete_days>0?'rp_icon':'nrp_icon')?>"><b><?=((int)$payment_info_array[$order->info['payment_methods_id']]->confirm_complete_days>0?'RP':'NRP')?></b></div> <? } ?>
      						<div style="width:30px; height:15px; background-color:<?=$payment_module_info['display_colour']?>; float:left; vertical-align:top; text-align:center;">&nbsp;</div>
      						<div style="vertical-align: top">
      							&nbsp;&nbsp;
<?
	if ($view_payment_details_permission) {
		if (tep_not_null($last_payment_method) && $last_payment_method['payment_methods_id'] != $order->info['payment_methods_id']) {
			echo '<span class="redIndicator">'.(tep_not_null($payment_module_info['payment_methods_parent_title'])?$payment_module_info['payment_methods_parent_title'] . ' - ' :'') . $payment_module_info['payment_methods_title'].'</span>';
		} else {
			echo (tep_not_null($payment_module_info['payment_methods_parent_title'])?$payment_module_info['payment_methods_parent_title'] . ' - ' :'') . $payment_module_info['payment_methods_title'];
		}
		
		if (tep_not_null($last_payment_method) && $last_payment_method['payment_methods_id'] != $order->info['payment_methods_id']) {
			echo '&nbsp;&nbsp;<span class="redIndicator">(Last Payment Method: ' . (tep_not_null($last_payment_method['parent_payment_methods_title'])? $last_payment_method['parent_payment_methods_title'] . ' - ' : '' ) . $last_payment_method['payment_methods_title'] . ')</span>';
		}
	} else {
		echo (tep_not_null($payment_module_info['payment_methods_parent_title'])?$payment_module_info['payment_methods_parent_title'] . ' - ' :'') . $payment_module_info['payment_methods_title'];
	}
?>
							</div>
      					</div>
      				</td>
				</tr>
			</table>
		</td>
	</tr>
<?
$payment_method_title_info = ob_get_contents();
ob_end_clean();

if (tep_db_num_rows($googlewallet_trans_info_result_sql) || tep_db_num_rows($googlewallet_trans_history_result_sql)) {
	$googlewallet_trans_info_row = tep_db_fetch_array($googlewallet_trans_info_result_sql);
	
	echo $payment_method_title_info;
	if (!$view_payment_details_permission) {
		;
	} else {
?>
  	<tr>
    	<td class="main">
      		<table border="0" width="100%" cellspacing="0" cellpadding="2">
      			<tr valign="top">
        			<td width="35%">
              			<table border="0" cellspacing="0" cellpadding="2">
              				<tr>
                				<td class="main" valign="top" nowrap><b><?=ENTRY_GOOGLEWALLET_TRANSACTION_ID?></b>&nbsp;</td>
                				<td class="main"><?=$googlewallet_trans_info_row["google_wallet_transaction_id"]?></td>
              				</tr>
              				<tr>
                				<td class="main" valign="top" nowrap><b><?=ENTRY_GOOGLEWALLET_CREATE_DATE?></b>&nbsp;</td>
                				<td class="main" nowrap><?=$googlewallet_trans_info_row["google_wallet_create_date"]?></td>
              				</tr>
              				<tr>
                				<td class="main" valign="top" nowrap><b><?=ENTRY_GOOGLEWALLET_CURRENCY?></b>&nbsp;</td>
                				<td class="main" nowrap><?=$googlewallet_trans_info_row["google_wallet_currency"]?></td>
              				</tr>
              				<tr>
                				<td class="main" valign="top" nowrap><b><?=ENTRY_GOOGLEWALLET_AMOUNT?></b>&nbsp;</td>
                				<td class="main" nowrap><?=$googlewallet_trans_info_row["google_wallet_amount"]?></td>
              				</tr>
              			</table>
        			</td>
        			<td>
              			<table border="0" cellspacing="0" cellpadding="2">
              				<tr>
                				<td class="main" colspan="2" nowrap>
                					<table border="1" cellspacing="0" cellpadding="2">
        								<tr>
                							<td class="smallText" nowrap><b><?=TABLE_HEADING_GOOGLEWALLET_DATE?></b></td>
                							<td class="smallText" nowrap><b><?=TABLE_HEADING_GOOGLEWALLET_STATUS?></b></td>
                							<td class="smallText" nowrap><b><?=TABLE_HEADING_GOOGLEWALLET_DESCRIPTION?></b></td>
                							<td class="smallText" nowrap><b><?=TABLE_HEADING_GOOGLEWALLET_CHANGED_BY?></b></td>
                						</tr>
<?		while ($googlewallet_trans_history_row = tep_db_fetch_array($googlewallet_trans_history_result_sql)) {
        	echo ' 						<tr>
                							<td class="smallText" nowrap>'.$googlewallet_trans_history_row['google_wallet_date'].'</td>
                							<td class="smallText" nowrap>'.($googlewallet_trans_history_row['google_wallet_status_id'] == 'SUCCESS' ? 'Successful payment' : (tep_not_null($googlewallet_trans_history_row['google_wallet_status_id']) ? $googlewallet_trans_history_row['google_wallet_status_id'] : 'Unknown')).'</td>
											<td class="smallText" nowrap>'.$googlewallet_trans_history_row['google_wallet_description'].'</td>
											<td class="smallText" nowrap>'.$googlewallet_trans_history_row['google_wallet_changed_by'].'</td>
                						</tr>';
     	}
?>
                					</table>
                				</td>
                			</tr>
              			</table>
              		</td>
      			</tr>
      		</table>
   		</td>
  	</tr>
<?
	}
} else {
	echo $payment_method_title_info;
}
?>
</table>