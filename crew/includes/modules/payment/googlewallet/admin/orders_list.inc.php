<?php
include_once(DIR_FS_CATALOG_MODULES . 'payment/googlewallet/admin/languages/' . $language . '/googlewallet.lng.php');

$googlewallet_trans_info_select_sql = "	SELECT google_wallet_transaction_id, google_wallet_create_date, google_wallet_amount, google_wallet_currency
										FROM " . TABLE_GOOGLE_WALLET . " 
										WHERE google_wallet_order_id = '" . $order_obj->orders_id . "'";
$googlewallet_trans_info_result_sql= tep_db_query($googlewallet_trans_info_select_sql);
?>
<table border="0" width="100%" cellspacing="0" cellpadding="2">
	<tr>
		<td class="subInvoiceBoxHeading">
			<div style="border-bottom: 1px solid #996600; border-top: 1px solid #996600; padding: 2px;">Payment Details</div>
		</td>
	</tr>
<?
if ($googlewallet_trans_info_row = tep_db_fetch_array($googlewallet_trans_info_result_sql)) {
?>
  	<tr>
    	<td class="main">
      		<table border="0" width="100%" cellspacing="0" cellpadding="2">
      			<tr valign="top">
        			<td width="100%">
              			<table border="0" cellspacing="0" cellpadding="2">
              				<tr>
                				<td class="invoiceRecords" valign="top" nowrap><?=ENTRY_GOOGLEWALLET_TRANSACTION_ID?></td>
                				<td class="invoiceRecords" valign="top">:&nbsp;</td>
                				<td class="invoiceRecords" nowrap><?=$googlewallet_trans_info_row["google_wallet_transaction_id"]?></td>
              				</tr>
              				<tr>
                				<td class="invoiceRecords" valign="top" nowrap><?=ENTRY_GOOGLEWALLET_CREATE_DATE?></td>
                				<td class="invoiceRecords" valign="top">:&nbsp;</td>
                				<td class="invoiceRecords"><?=$googlewallet_trans_info_row["google_wallet_create_date"]?></td>
              				</tr>
              				<tr>
                				<td class="invoiceRecords" valign="top" nowrap><?=ENTRY_GOOGLEWALLET_CURRENCY?></td>
                				<td class="invoiceRecords" valign="top">:&nbsp;</td>
                				<td class="invoiceRecords" nowrap><?=$googlewallet_trans_info_row["google_wallet_currency"]?></td>
              				</tr>
              				<tr>
                				<td class="invoiceRecords" valign="top" nowrap><?=ENTRY_GOOGLEWALLET_AMOUNT?></td>
                				<td class="invoiceRecords" valign="top">:&nbsp;</td>
                				<td class="invoiceRecords" nowrap><?=$googlewallet_trans_info_row["google_wallet_amount"]?></td>
              				</tr>
              			</table>
        			</td>
      			</tr>
      		</table>
   		</td>
  	</tr>
<?
} else {
?>
	<tr>
		<td class="invoiceRecords">No further payment information is available.</td>
	</tr>
<?
}
?>
</table>