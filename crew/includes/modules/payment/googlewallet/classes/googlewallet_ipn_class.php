<?php

class GooglewalletIpn {

    var $ipnResponse;

    function GooglewalletIpn($post_vars) {
        $this->init($post_vars);
    }

    function init($post_vars) {
        $this->ipnResponse = '';
        reset($post_vars);
        $this->ipnResponse = $post_vars;
        unset($post_vars);
    }

    //Test Merchant Code
    function validate_receiver_account($merchant_id) {
        if (!strcmp(strtolower($merchant_id), strtolower($this->ipnResponse->aud))) {
            return true;
        } else {
            return false;
        }
    }

    function validate_transaction_data($order, $googlewallet, $returnedSignature) {
		global $currencies;
        /*         * ********************************************************
          4. Compare the Signature from googlewallet with your own
          /******************************************************** */
		$OrderAmt = number_format($order->info['total_value'] * $currencies->get_value($order->info['currency']), $currencies->get_decimal_places($order->info['currency']), '.', '');
		
        $customString = $googlewallet->merchant_id.$this->get_order_id().$OrderAmt.$order->info['currency'].$googlewallet->secret_word.$googlewallet->ogm_secret_word;
        $calculatedGooglewalletSignature = hash('sha256',$customString);
		
        // Checking 4
        if ($calculatedGooglewalletSignature == $returnedSignature) { 
            return true;
        } else {
            ob_start();
            echo "========================calculated_googlewallet_signature=========================<BR>";
            echo "'" . $calculatedGooglewalletSignature . "' == '" . $returnedSignature . "'";
            echo "============================================================================<BR>";
            $debug_html = ob_get_contents();
            ob_end_clean();
			@tep_mail('<EMAIL>', '<EMAIL>', $this->get_order_id() . 'INVALID GOOGLEWALLET SIGNATURE', $debug_html, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
        }
    }

    function get_order_id() {
        return (isset($this->ipnResponse->request->name) ? $this->ipnResponse->request->name : '');
    }

    function authenticate($order) {
		global $currencies;

		$googlewalletCurrency = $this->ipnResponse->request->currencyCode;
        $googlewalletAmount = $this->ipnResponse->request->price;
        $googlewalletGrossAmt = number_format(($googlewalletAmount), $currencies->currencies[$googlewalletCurrency]['decimal_places'], $currencies->currencies[$googlewalletCurrency]['decimal_point'], $currencies->currencies[$googlewalletCurrency]['thousands_point']);
        $googlewalletAmount = $currencies->currencies[$googlewalletCurrency]['symbol_left'] . $googlewalletGrossAmt . $currencies->currencies[$googlewalletCurrency]['symbol_right'];
		
        if ($googlewalletAmount == $order->info['total']) {
           return true;
        } else {
            ob_start();
            echo "========================Amount Mismatched=========================<BR>";
            echo "'" . $googlewalletAmount . "' == '" . $checkoutDataAmountFormatted;
            echo "============================================================================<BR>";
            $debug_html = ob_get_contents();
            ob_end_clean();
			@tep_mail('<EMAIL>', '<EMAIL>', $this->get_order_id() . 'GOOGLEWALLET AMOUNT MISMATCHED', $debug_html, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
            return false;
        }
    }
}
?>