<?php

/*
  $Id: gudang_voucher.php

  Developer: <PERSON>
  Copyright (c) 2007 SKC Ventrue

  Released under the GNU General Public License
 */

require_once('payment_gateway.php');

class gudang_voucher extends payment_gateway {

    var $code, $title, $description, $sort_order, $enabled = false, $form_action_url, $response_referer_url;
    var $email_footer, $transaction_id, $GVCurrencies, $defCurr, $amount_decimal;
    var $check_trans_status_flag, $gudang_voucher_merchant_code, $gudang_voucher_merchant_key, $gudang_voucher_pm_ids;

    // class constructor
    function gudang_voucher($pm_id = '') {
        global $order, $languages_id, $default_languages_id;

        //Basic info
        $this->payment_methods_id = 0;
        $this->payment_methods_parent_id = 0;

        $this->code = 'gudang_voucher';
        $this->filename = 'gudang_voucher.php';

        $this->force_to_checkoutprocess = true;
        $this->has_ipn = true;
        $this->auto_cancel_period = 60; // In minutes
        $this->amount_decimal = 0;
        $this->help_icon = tep_image(DIR_WS_ICONS . 'help_info.gif', '', '12', '12', '');

        $this->transaction_id = '';

        $this->check_trans_status_flag = true;

        $payment_methods_select_sql = "	SELECT payment_methods_parent_id, payment_methods_code,
												payment_methods_types_id,
												payment_methods_receive_status_mode, payment_methods_id,
												payment_methods_title, payment_methods_sort_order,
												payment_methods_receive_status, payment_methods_legend_color,
												payment_methods_logo
										FROM " . TABLE_PAYMENT_METHODS . "
										WHERE 1 ";
        if ((int) $pm_id > 0) {
            $payment_methods_select_sql .= "	AND payment_methods_id = '" . (int) $pm_id . "'";
        } else {
            $payment_methods_select_sql .= "	AND payment_methods_filename = '" . tep_db_input($this->filename) . "'";
        }

        $payment_methods_result_sql = tep_db_query($payment_methods_select_sql);
        if ($payment_methods_row = tep_db_fetch_array($payment_methods_result_sql)) {
            $pm_desc_title_select_sql = "	SELECT payment_methods_description_title
											FROM " . TABLE_PAYMENT_METHODS_DESCRIPTION . "
											WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "'
												AND (IF (languages_id = '" . tep_db_input($languages_id) . "', 1, IF ((	SELECT COUNT(payment_methods_id) > 0
																														FROM " . TABLE_PAYMENT_METHODS_DESCRIPTION . "
																														WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "'
																															AND languages_id = '" . (int) $languages_id . "'), 0, languages_id = 1)))";
            $pm_desc_title_result_sql = tep_db_query($pm_desc_title_select_sql);
            $pm_desc_title_row = tep_db_fetch_array($pm_desc_title_result_sql);

            $this->payment_methods_id = (int) $payment_methods_row['payment_methods_id'];
            $this->payment_methods_parent_id = (int) $payment_methods_row['payment_methods_parent_id'];
            $this->code = $payment_methods_row['payment_methods_code'];
            $this->title = $payment_methods_row['payment_methods_title']; //MODULE_PAYMENT_PAYPAL_TEXT_TITLE;
            $this->display_title = $pm_desc_title_row['payment_methods_description_title']; //MODULE_PAYMENT_PAYPAL_TEXT_DISPLAY_TITLE; 	// if title to be display for payment method is different from payment method value
            $this->sort_order = $payment_methods_row['payment_methods_sort_order']; //MODULE_PAYMENT_PAYPAL_SORT_ORDER;
            $this->enabled = $payment_methods_row['payment_methods_receive_status_mode'];
            $this->legend_display_colour = $payment_methods_row['payment_methods_legend_color'];
            $this->receive_status = $payment_methods_row['payment_methods_receive_status'];
            $this->preferred = true;
            $this->description = $this->display_title;
            $this->GVCurrencies = $this->get_support_currencies($this->payment_methods_id);

            $configuration_setting_array = $this->load_pm_setting();
            //$this->currency = $configuration_setting_array['MODULE_PAYMENT_GUDANG_VOUCHER_CURRENCY'];
            $this->order_status_id = $configuration_setting_array['MODULE_PAYMENT_GUDANG_VOUCHER_ORDER_STATUS_ID'];
            $this->processing_status_id = $configuration_setting_array['MODULE_PAYMENT_GUDANG_VOUCHER_PROCESSING_STATUS_ID'];
            //$this->zone = $configuration_setting_array['MODULE_PAYMENT_GUDANG_VOUCHER_ZONE'];
            $this->message = $configuration_setting_array['MODULE_PAYMENT_GUDANG_VOUCHER_MESSAGE'];
            $this->email_message = $configuration_setting_array['MODULE_PAYMENT_GUDANG_VOUCHER_EMAIL_MESSAGE'];
            $this->confirm_complete_days = $configuration_setting_array['MODULE_PAYMENT_GUDANG_VOUCHER_CONFIRM_COMPLETE'];
            $this->require_address_information = $configuration_setting_array['MODULE_PAYMENT_GUDANG_VOUCHER_MANDATORY_ADDRESS_FIELD'];
            $this->require_ic_information = $configuration_setting_array['MODULE_PAYMENT_GUDANG_VOUCHER_MANDATORY_IC_FIELD'];
            $this->require_contact_information = $configuration_setting_array['MODULE_PAYMENT_GUDANG_VOUCHER_MANDATORY_CONTACT_FIELD'];
            $this->test_mode = $configuration_setting_array['MODULE_PAYMENT_GUDANG_VOUCHER_TEST_MODE'];
            $this->customer_payment_info = $configuration_setting_array['MODULE_PAYMENT_GUDANG_VOUCHER_CUSTOMER_PAYMENT_INFO'];

            // gudang_voucher only accept IDR currency
            $this->defCurr = 'IDR';

            if ((int) $this->order_status_id > 0) {
                $this->order_status = $this->order_status_id;
            } else {
                $this->order_status = DEFAULT_ORDERS_STATUS_ID;
            }

            $this->order_processing_status = $this->processing_status_id;
            $this->form_action_url = 'https://www.gudangvoucher.com/payment.php'; // gudang_voucher ePayment
            $this->payment_checking_url = 'https://www.gudangvoucher.com/cpayment.php '; // re-query payment
            $this->email_footer = str_replace(array("<br>", "<BR>", "\r\n"), array("\n", "\n", ''), $this->email_message);
        }
    }

    // class methods
    function javascript_validation() {
        return false;
    }

    function selection() {
        global $languages_id, $default_languages_id;

        $selection_array = array();

        $payment_methods_select_sql = "	SELECT payment_methods_id, payment_methods_code, payment_methods_types_id, payment_methods_logo, payment_methods_receive_status_mode
										FROM " . TABLE_PAYMENT_METHODS . "
										WHERE payment_methods_receive_status = 1
											AND payment_methods_receive_status_mode <> 0
											AND payment_methods_parent_id = '" . (int) $this->payment_methods_id . "'
										ORDER BY payment_methods_sort_order";
        $payment_methods_result_sql = tep_db_query($payment_methods_select_sql);
        while ($payment_methods_row = tep_db_fetch_array($payment_methods_result_sql)) {
            $pm_desc_title_select_sql = "	SELECT payment_methods_description_title
											FROM " . TABLE_PAYMENT_METHODS_DESCRIPTION . "
											WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "'
												AND (IF (languages_id = '" . tep_db_input($languages_id) . "', 1, IF ((	SELECT COUNT(payment_methods_id) > 0
																														FROM " . TABLE_PAYMENT_METHODS_DESCRIPTION . "
																														WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "'
																															AND languages_id = '" . (int) $languages_id . "'), 0, languages_id = 1)))";
            $pm_desc_title_result_sql = tep_db_query($pm_desc_title_select_sql);
            $pm_desc_title_row = tep_db_fetch_array($pm_desc_title_result_sql);

            $pm_array = $this->load_pm_setting(1, $payment_methods_row['payment_methods_id']);

            $selection_array[] = array('payment_gateway_code' => $this->code,
                'payment_methods_receive_status_mode' => $payment_methods_row['payment_methods_receive_status_mode'],
                'payment_methods_id' => $payment_methods_row['payment_methods_id'],
                'payment_methods_code' => $payment_methods_row['payment_methods_code'],
                'payment_methods_types_id' => $payment_methods_row['payment_methods_types_id'],
                'payment_methods_parent_id' => $this->payment_methods_id,
                'payment_methods_logo' => $payment_methods_row['payment_methods_logo'],
                'payment_methods_description_title' => $pm_desc_title_row['payment_methods_description_title'],
                'currency' => $this->get_support_currencies($payment_methods_row['payment_methods_id']),
                'confirm_complete_days' => $pm_array['MODULE_PAYMENT_GUDANG_VOUCHER_CONFIRM_COMPLETE'],
                'show_billing_address' => $pm_array['MODULE_PAYMENT_GUDANG_VOUCHER_MANDATORY_ADDRESS_FIELD'],
                'show_contact_number' => $pm_array['MODULE_PAYMENT_GUDANG_VOUCHER_MANDATORY_CONTACT_FIELD'],
                'show_ic' => $pm_array['MODULE_PAYMENT_GUDANG_VOUCHER_MANDATORY_IC_FIELD']
            );

            if ($payment_methods_row['payment_methods_receive_status_mode'] == '-1') {
                $pm_status_message_select = "	SELECT payment_methods_status_message
												FROM " . TABLE_PAYMENT_METHODS_STATUS_DESCRIPTION . "
												WHERE payment_methods_mode = 'RECEIVE'
													AND payment_methods_status = '-1'
													AND languages_id = '" . (int) $languages_id . "'
													AND payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "'";
                $pm_status_message_result = tep_db_query($pm_status_message_select);
                $pm_status_message_row = tep_db_fetch_array($pm_status_message_result);
                $selection_array[sizeof($selection_array) - 1]['payment_methods_status_message'] = $pm_status_message_row['payment_methods_status_message'];
            }
        }

        return $selection_array;
    }

    function set_support_currencies($currency_array) {
        $this->GVCurrencies = $currency_array;
    }

    function get_require_address_information() {
        return $this->require_address_information;
    }

    function draw_confirm_button() {
        return tep_image_button2('red', 'javascript:document.checkout_confirmation.submit()', MODULE_PAYMENT_GUDANG_VOUCHER_TEXT_CART, 200);
    }

    function get_confirm_button_caption() {
        return MODULE_PAYMENT_GUDANG_VOUCHER_TEXT_TITLE;
    }

    function pre_confirmation_check() {
        global $currency;

        if (in_array($currency, $this->GVCurrencies)) {
            
        }

        return false;
    }

    function process_button() {
        global $shipping_cost, $total_cost, $shipping_selected, $shipping_method, $currencies, $currency, $customer_id, $order, $languages_id;

        if ($this->force_to_checkoutprocess && !isset($_SESSION['order_logged']))
            return;

        // if the user currency is not supported by gudang_voucher
        // fall back to the default currency set on the admin interface of the module
        $GVCurrency = $currency;
        if (!in_array($GVCurrency, $this->GVCurrencies)) {
            $GVCurrency = $this->GVCurrencies[0];
        }

        $payment_gateway_amount = number_format($order->info['total'] * $currencies->get_value($GVCurrency), $this->amount_decimal, '.', '');

        $signature_payment_gateway_amount = preg_replace('/[^\d]/', '', preg_quote($payment_gateway_amount));

        $this->get_merchant_account($GVCurrency);

        $gudang_voucher_signature = base64_encode($this->hex2ascii(sha1($this->gudang_voucher_merchant_code . $this->gudang_voucher_merchant_key . $_SESSION['order_logged'] . $signature_payment_gateway_amount . strtoupper($GVCurrency))));

        $OrderAmt = number_format($order->info['total'] * $currencies->get_value($GVCurrency), $currencies->get_decimal_places($GVCurrency), '.', '');

        $process_button_string = tep_draw_hidden_field('merchantid', $this->gudang_voucher_merchant_code) .
                tep_draw_hidden_field('amount', $OrderAmt) .
                tep_draw_hidden_field('product', 'Order ID: ' . $_SESSION['order_logged']) .
                tep_draw_hidden_field('custom', urlencode($gudang_voucher_signature) . '_' . $_SESSION['order_logged']);

        if ($this->test_mode == 'On') {
            $process_button_string .= tep_draw_hidden_field('demo', 1);
        }

        return $process_button_string;
    }

    // manage returning data from gudang_voucher (errors, failures, success etc.)
    function before_process() {
        if (!isset($_SESSION['order_logged']) || !tep_not_null($_SESSION['order_logged'])) {
            return; // This line does not mean there is payment error. It means the first time order been created
        } else {
            ob_start();
            echo "=========================POST===========================<BR>";
            print_r($_POST);
            echo "========================================================<BR>";
            echo "=========================GET===========================<BR>";
            print_r($_GET);
            echo "========================================================<BR>";
            echo "========================REQUEST==========================<BR>";
            print_r($_REQUEST);
            echo "========================================================<BR>";
            echo "========================SESSION=========================<BR>";
            print_r($_SESSION);
            echo "========================================================<BR>";
            $debug_html = ob_get_contents();
            ob_end_clean();
            @tep_mail('<EMAIL>', '<EMAIL>', '[OFFGAMERS] Gudang Voucher BEFORE PROCESS DEBUG E-MAIL', $debug_html, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
            if (!isset($_GET['reference']) || !tep_not_null($_GET['reference'])) {
                tep_redirect(tep_href_link(FILENAME_CHECKOUT_PAYMENT, 'error_message=' . urlencode($gudang_voucher_ipn->key['status']), 'SSL'));
            }
        }
    }

    function after_process() {
        return false;
    }

    function link_to_payment($new_order_id) {
//		global $order_logged;
//
//		if (tep_session_is_registered('order_logged')) tep_session_unregister('order_logged');
//
//		tep_session_register('order_logged');
//		$order_logged = $new_order_id;
//		require(DIR_WS_INCLUDES . 'modules/payment/gudang_voucher/catalog/gudang_voucher_splash.php');
//	 	return ;

        if (isset($_SESSION['order_logged']))
            unset($_SESSION['order_logged']);

        $_SESSION['order_logged'] = $new_order_id;
        require(DIR_WS_INCLUDES . 'modules/payment/gudang_voucher/catalog/gudang_voucher_splash.php');
        return;
    }

    function get_error() {
        $error = array('title' => MODULE_PAYMENT_GUDANG_VOUCHER_TEXT_ERROR,
            'error' => stripslashes(urldecode($_GET['error'])));
        return $error;
    }

    function check() {
        if (!isset($this->_check)) {
            $this->_check = defined('MODULE_PAYMENT_GUDANG_VOUCHER_STATUS');
        }

        return $this->_check;
    }

    function is_processed_order($order_id) {
        $payment_status_select_sql = "	SELECT gudang_voucher_tran_status
										FROM " . TABLE_GUDANG_VOUCHER . "
										WHERE gudang_voucher_orders_id = '" . tep_db_input($order_id) . "'";
        $payment_status_result_sql = tep_db_query($payment_status_select_sql);
        $payment_status_row = tep_db_fetch_array($payment_status_result_sql);

        if ($payment_status_row['gudang_voucher_tran_status'] == 'SUCCESS') {
            return true;
        } else {
            return false;
        }
    }

    function is_supported_currency($selected_currency) {
        return (in_array($selected_currency, $this->GVCurrencies) ? true : false);
    }

    function load_pm_setting($language_id = 1, $pm_id = '') {
        $pm_setting_array = array();
        //load value from DB

        if (tep_not_null($pm_id)) {
            $payment_method_id = $pm_id;

            $payment_methods_parent_id_select_sql = "	SELECT payment_methods_parent_id
														FROM " . TABLE_PAYMENT_METHODS . "
														WHERE payment_methods_id = '" . (int) $pm_id . "'";
            $payment_methods_parent_id_result_sql = tep_db_query($payment_methods_parent_id_select_sql);
            $payment_methods_parent_id_row = tep_db_fetch_array($payment_methods_parent_id_result_sql);

            $payment_gateway_id = $payment_methods_parent_id_row['payment_methods_parent_id'];
        } else {
            $payment_method_id = $this->payment_methods_id;
            $payment_gateway_id = $this->payment_methods_parent_id;
        }

        $payment_configuration_info_select_sql = "	SELECT pci.payment_configuration_info_key, pcid.payment_configuration_info_value
													FROM " . TABLE_PAYMENT_CONFIGURATION_INFO . " as pci
													LEFT JOIN " . TABLE_PAYMENT_CONFIGURATION_INFO_DESCRIPTION . " as pcid
														ON pci.payment_configuration_info_id = pcid.payment_configuration_info_id
															AND pcid.languages_id = '" . (int) $language_id . "'
													WHERE pci.payment_methods_id = '" . (int) $payment_method_id . "'
													ORDER BY pci.payment_configuration_info_sort_order";
        $payment_configuration_info_result_sql = tep_db_query($payment_configuration_info_select_sql);
        if (tep_db_num_rows($payment_configuration_info_result_sql)) {
            while ($payment_configuration_info_row = tep_db_fetch_array($payment_configuration_info_result_sql)) {
                $pm_setting_array[$payment_configuration_info_row['payment_configuration_info_key']] = $payment_configuration_info_row['payment_configuration_info_value'];
            }
        } else if ((int) $payment_gateway_id > 0) { //load value from PG
            $payment_configuration_info_select_sql = "	SELECT pci.payment_configuration_info_key, pcid.payment_configuration_info_value
														FROM " . TABLE_PAYMENT_CONFIGURATION_INFO . " as pci
														LEFT JOIN " . TABLE_PAYMENT_CONFIGURATION_INFO_DESCRIPTION . " as pcid
															ON pci.payment_configuration_info_id = pcid.payment_configuration_info_id
																AND pcid.languages_id = '" . (int) $language_id . "'
														WHERE pci.payment_methods_id = '" . (int) $payment_gateway_id . "'
														ORDER BY pci.payment_configuration_info_sort_order";
            $payment_configuration_info_result_sql = tep_db_query($payment_configuration_info_select_sql);
            while ($payment_configuration_info_row = tep_db_fetch_array($payment_configuration_info_result_sql)) {
                $pm_setting_array[$payment_configuration_info_row['payment_configuration_info_key']] = $payment_configuration_info_row['payment_configuration_info_value'];
            }
        }

        return $pm_setting_array;
    }

    function get_merchant_account($selected_currency) {
        $payment_methods_instance_id = 0;
        $payment_gateway_instance_select_sql = "SELECT pmi.payment_methods_instance_id, pmi.payment_methods_instance_follow_default, pmi.currency_code
				 								FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " as pmi
				 								WHERE pmi.payment_methods_id = '" . (int) $this->payment_methods_id . "'
				 								LIMIT 1";
        $payment_gateway_instance_result_sql = tep_db_query($payment_gateway_instance_select_sql);
        if (tep_db_num_rows($payment_gateway_instance_result_sql)) {
            $payment_gateway_instance_select_sql = "SELECT pmi.payment_methods_instance_id, pmi.payment_methods_instance_follow_default
					 								FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " as pmi
					 								WHERE pmi.currency_code = '" . tep_db_input($selected_currency) . "'
					 									AND pmi.payment_methods_id = '" . (int) $this->payment_methods_id . "'";
            $payment_gateway_instance_result_sql = tep_db_query($payment_gateway_instance_select_sql);
            if ($payment_gateway_instance_row = tep_db_fetch_array($payment_gateway_instance_result_sql)) {
                if ((int) $payment_gateway_instance_row['payment_methods_instance_follow_default'] > 0) { // Follow Default Currency Setting
                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_follow_default'];
                } else { // Has own setting
                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_id'];
                }
            }
        } else {
            $payment_gateway_instance_select_sql = "SELECT pmi.payment_methods_instance_id, pmi.payment_methods_instance_follow_default
					 								FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " as pmi
					 								INNER JOIN " . TABLE_PAYMENT_METHODS . " as pm
					 									ON pm.payment_methods_parent_id = pmi.payment_methods_id
					 								WHERE pmi.currency_code = '" . tep_db_input($selected_currency) . "'
					 									AND pm.payment_methods_id = '" . (int) $this->payment_methods_id . "'";
            $payment_gateway_instance_result_sql = tep_db_query($payment_gateway_instance_select_sql);
            if ($payment_gateway_instance_row = tep_db_fetch_array($payment_gateway_instance_result_sql)) {
                if ((int) $payment_gateway_instance_row['payment_methods_instance_follow_default'] > 0) {
                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_follow_default'];
                } else {
                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_id'];
                }
            }
        }
        $payment_gateway_instance_setting_select_sql = "SELECT pmis.payment_methods_instance_setting_key, pmis.payment_methods_instance_setting_value
						 								FROM " . TABLE_PAYMENT_METHODS_INSTANCE_SETTING . " as pmis
						 								WHERE pmis.payment_methods_instance_id = '" . (int) $payment_methods_instance_id . "'";
        $payment_gateway_instance_setting_result_sql = tep_db_query($payment_gateway_instance_setting_select_sql);
        while ($payment_gateway_instance_setting_row = tep_db_fetch_array($payment_gateway_instance_setting_result_sql)) {
            switch ($payment_gateway_instance_setting_row['payment_methods_instance_setting_key']) {
                case 'MODULE_PAYMENT_GUDANG_VOUCHER_MERCHANT_CODE':
                    $this->gudang_voucher_merchant_code = $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];
                    break;
                case 'MODULE_PAYMENT_GUDANG_VOUCHER_MERCHANT_KEY':
                    $this->gudang_voucher_merchant_key = $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];
                    break;
            }
        }
    }

    function install() {
        $install_configuration_flag = true;

        if ($this->payment_methods_id > 0) {
            if ($this->receive_status != 1) {
                $update_receive_status_sql_data = array("payment_methods_receive_status" => 1);
                tep_db_perform(TABLE_PAYMENT_METHODS, $update_receive_status_sql_data, 'update', "payment_methods_id = '" . (int) $this->payment_methods_id . "'");
            } else {
                $install_configuration_flag = false;
            }
        } else {
            $install_payment_gateway_data_sql = array('payment_methods_code' => $this->code,
                'payment_methods_send_status' => 0,
                'payment_methods_title' => $this->code,
                'payment_methods_types_id' => 0,
                'payment_methods_parent_id' => 0,
                'payment_methods_legend_color' => '#333333',
                'payment_methods_sort_order' => 30,
                'payment_methods_filename' => $this->filename,
                'payment_methods_logo' => '',
                'date_added' => 'now()',
                'last_modified' => 'now()',
                'payment_methods_receive_status' => 1,
                'payment_methods_receive_status_mode' => 0
            );
            tep_db_perform(TABLE_PAYMENT_METHODS, $install_payment_gateway_data_sql);
            $this->payment_methods_id = tep_db_insert_id();
        }
        if ($install_configuration_flag) {
            $install_key_array = array();
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Test Mode',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_GUDANG_VOUCHER_TEST_MODE',
                    'payment_configuration_info_description' => "Set test mode",
                    'payment_configuration_info_sort_order' => '1029',
                    'set_function' => 'tep_cfg_select_option(array(\'Off\',\'On\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'Off',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Set Order Status',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_GUDANG_VOUCHER_ORDER_STATUS_ID',
                    'payment_configuration_info_description' => 'Set the status of orders made with this payment module to this value',
                    'payment_configuration_info_sort_order' => '1030',
                    'set_function' => 'tep_cfg_pull_down_order_statuses(',
                    'use_function' => 'tep_get_order_status_name',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => '1',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Set Order\'s "Processing" Status',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_GUDANG_VOUCHER_PROCESSING_STATUS_ID',
                    'payment_configuration_info_description' => 'Set the initial processing status of orders made with this payment module to this value',
                    'payment_configuration_info_sort_order' => '1035',
                    'set_function' => 'tep_cfg_pull_down_order_statuses(',
                    'use_function' => 'tep_get_order_status_name',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => '7',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Payment Zone',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_GUDANG_VOUCHER_ZONE',
                    'payment_configuration_info_description' => 'If a zone is selected, only enable this payment method for that zone.',
                    'payment_configuration_info_sort_order' => '1040',
                    'set_function' => 'tep_cfg_pull_down_zone_classes(',
                    'use_function' => 'tep_get_zone_class_title',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => '0',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Payment Message',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_GUDANG_VOUCHER_MESSAGE',
                    'payment_configuration_info_description' => 'Payment message will show up during checkout process (Gudang Voucher)',
                    'payment_configuration_info_sort_order' => '1045',
                    'set_function' => 'tep_cfg_textarea(',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'Thank you for shopping at SKC Store.',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Payment Email Message',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_GUDANG_VOUCHER_EMAIL_MESSAGE',
                    'payment_configuration_info_description' => 'This message will show up in order confirmation e-mail.',
                    'payment_configuration_info_sort_order' => '1045',
                    'set_function' => 'tep_cfg_textarea(',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'Thank you for shopping at OffGamers.com.',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Confirm Complete (in days)',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_GUDANG_VOUCHER_CONFIRM_COMPLETE',
                    'payment_configuration_info_description' => 'The number of days in which an order made by this payment method is confirm completed',
                    'payment_configuration_info_sort_order' => '1050',
                    'set_function' => '',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => '0',
                    'languages_id' => 1
                )
            );
            /* 			$install_key_array[] = array(	'info' => array (	'payment_methods_id'=>(int)$this->payment_methods_id,
              'payment_configuration_info_title'=>'Transaction Currency',
              'payment_configuration_info_key'=>'MODULE_PAYMENT_GUDANG_VOUCHER_CURRENCY',
              'payment_configuration_info_description'=>'Select the default currency for the payment transactions. If the user selected currency is not available at gudang voucher, this currency will be the payment currency.',
              'payment_configuration_info_sort_order'=>'1025',
              'set_function'=>'tep_cfg_select_option(array(\'MYR\'),',
              'use_function'=>'',
              'date_added'=>'now()'
              ),
              'desc' => array (	'payment_configuration_info_value'=>'MYR',
              'languages_id' => 1
              )
              ); */
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Require Address Information',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_GUDANG_VOUCHER_MANDATORY_ADDRESS_FIELD',
                    'payment_configuration_info_description' => 'Set address field as required info during checkout.',
                    'payment_configuration_info_sort_order' => '1030',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Customer Payment Info',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_GUDANG_VOUCHER_CUSTOMER_PAYMENT_INFO',
                    'payment_configuration_info_description' => 'Set to true if this Payment Method required customer input for Payment Information required customer payment info.',
                    'payment_configuration_info_sort_order' => '1035',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Require IC Information',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_GUDANG_VOUCHER_MANDATORY_IC_FIELD',
                    'payment_configuration_info_description' => 'Set IC field as required info during checkout.',
                    'payment_configuration_info_sort_order' => '1040',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Require Contact Information',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_GUDANG_VOUCHER_MANDATORY_CONTACT_FIELD',
                    'payment_configuration_info_description' => 'Set Contact field as required info during checkout.',
                    'payment_configuration_info_sort_order' => '1045',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );
            foreach ($install_key_array as $data) {
                tep_db_perform(TABLE_PAYMENT_CONFIGURATION_INFO, $data['info']);
                $payment_conf_id = tep_db_insert_id();

                $data['desc']['payment_configuration_info_id'] = (int) $payment_conf_id;
                tep_db_perform(TABLE_PAYMENT_CONFIGURATION_INFO_DESCRIPTION, $data['desc']);
            }
        }
        return 1;
    }

    function keys() {
        return array('MODULE_PAYMENT_GUDANG_VOUCHER_STATUS',
            'MODULE_PAYMENT_GUDANG_VOUCHER_MERCHANT_CODE',
            'MODULE_PAYMENT_GUDANG_VOUCHER_MERCHANT_KEY',
            'MODULE_PAYMENT_GUDANG_VOUCHER_PAYMENT_METHOD_ID',
            'MODULE_PAYMENT_GUDANG_VOUCHER_CURRENCY',
            'MODULE_PAYMENT_GUDANG_VOUCHER_SORT_ORDER',
            'MODULE_PAYMENT_GUDANG_VOUCHER_ORDER_STATUS_ID',
            'MODULE_PAYMENT_GUDANG_VOUCHER_PROCESSING_STATUS_ID',
            'MODULE_PAYMENT_GUDANG_VOUCHER_ZONE',
            'MODULE_PAYMENT_GUDANG_VOUCHER_TEST_MODE',
            'MODULE_PAYMENT_GUDANG_VOUCHER_MESSAGE',
            'MODULE_PAYMENT_GUDANG_VOUCHER_EMAIL_MESSAGE',
            'MODULE_PAYMENT_GUDANG_VOUCHER_CONFIRM_COMPLETE',
            'MODULE_PAYMENT_GUDANG_VOUCHER_LEGEND_COLOUR');
    }

    function configuration_information_keys() {
        return array('MODULE_PAYMENT_GUDANG_VOUCHER_CURRENCY',
            'MODULE_PAYMENT_GUDANG_VOUCHER_ORDER_STATUS_ID',
            'MODULE_PAYMENT_GUDANG_VOUCHER_PROCESSING_STATUS_ID',
            'MODULE_PAYMENT_GUDANG_VOUCHER_ZONE',
            'MODULE_PAYMENT_GUDANG_VOUCHER_MESSAGE',
            'MODULE_PAYMENT_GUDANG_VOUCHER_EMAIL_MESSAGE',
            'MODULE_PAYMENT_GUDANG_VOUCHER_CONFIRM_COMPLETE',
            'MODULE_PAYMENT_GUDANG_VOUCHER_TEST_MODE',
            'MODULE_PAYMENT_GUDANG_VOUCHER_MANDATORY_ADDRESS_FIELD',
            'MODULE_PAYMENT_GUDANG_VOUCHER_CUSTOMER_PAYMENT_INFO',
            'MODULE_PAYMENT_GUDANG_VOUCHER_MANDATORY_IC_FIELD',
            'MODULE_PAYMENT_GUDANG_VOUCHER_MANDATORY_CONTACT_FIELD');
    }

    function multi_lang_configuration_info_keys() {
        return array('MODULE_PAYMENT_GUDANG_VOUCHER_MESSAGE',
            'MODULE_PAYMENT_GUDANG_VOUCHER_EMAIL_MESSAGE');
    }

    function merchant_information_keys() {
        include_once(DIR_WS_CLASSES . "payment_methods.php");

        return array(
            'MODULE_PAYMENT_GUDANG_VOUCHER_MERCHANT_CODE' => array("field" => 'text', 'mapping' => 'MODULE_PAYMENT_GUDANG_VOUCHER_LNG_MERCHANT_CODE'),
            'MODULE_PAYMENT_GUDANG_VOUCHER_MERCHANT_KEY' => array("field" => 'text', 'mapping' => 'MODULE_PAYMENT_GUDANG_VOUCHER_LNG_MERCHANT_KEY'),
            'MODULE_PAYMENT_TAX' => array("field" => 'select', 'mapping' => "MODULE_PAYMENT_TAX", "select" => payment_methods::get_tax_option())
        );
    }

    // Parse the predefinied array to be 'module install' friendly
    // as it is used for select in the module's install() function
    function show_array($aArray, $appendKey = false) {
        $aFormatted = "array(";
        foreach ($aArray as $key => $sVal) {
            if ($appendKey) {
                $aFormatted .= "\'$key\' => \'" . str_replace('\\\\\\\\"', '"', addslashes(addslashes(addslashes($sVal)))) . "\', ";
            } else {
                $aFormatted .= "\'$sVal\', ";
            }
        }
        $aFormatted = substr($aFormatted, 0, strlen($aFormatted) - 2);
        return $aFormatted;
    }

    function check_trans_status($order_id) {
        global $currencies;
        $path_info_array = pathinfo($_SERVER['SCRIPT_FILENAME']);

        $order_info_select_sql = "	SELECT o.payment_methods_id, o.currency, o.currency_value, ot.value
									FROM " . TABLE_ORDERS . " AS o
									INNER JOIN " . TABLE_ORDERS_TOTAL . " AS ot
										ON o.orders_id=ot.orders_id AND ot.class='ot_total'
									WHERE o.orders_id = '" . tep_db_input($order_id) . "'";
        $order_info_result_sql = tep_db_query($order_info_select_sql);

        if ($order_info_row = tep_db_fetch_array($order_info_result_sql)) {
            if ($order_info_row['payment_methods_id'] != $this->payment_methods_id)
                return;

            /*             * ************************************************************************************
              2. Check the payment amount from gudang voucher is match with yours
              3. Do re-query payment status for successful payment transaction to double confirm
             * ************************************************************************************ */
            $GVCurrency = $order_info_row['currency'];

            $this->get_merchant_account($GVCurrency);

            $gudang_voucher_signature_select_sql = "	SELECT gudang_voucher_signature, gudang_voucher_reference_id
                                                        FROM " . TABLE_GUDANG_VOUCHER . "
                                                        WHERE gudang_voucher_orders_id = '" . (int) $order_id . "'";
            $gudang_voucher_signature_result_sql = tep_db_query($gudang_voucher_signature_select_sql);
            if ($gudang_voucher_signature_row = tep_db_fetch_array($gudang_voucher_signature_result_sql)) {
                $custom_signature = urlencode($gudang_voucher_signature_row['gudang_voucher_signature'] . '_' . $order_id);
            } else {
                return false;
            }

            $gv_param_value = $this->gudang_voucher_merchant_key . $this->gudang_voucher_merchant_code . $custom_signature;
            $gv_param_signature = md5($gv_param_value);
            // build URL to retrieve transaction result
            $data = '';
            $form_data = array('merchantid' => $this->gudang_voucher_merchant_code,
                'custom' => $custom_signature,
                'signature' => $gv_param_signature,
                'demo' => ($this->test_mode == 'On') ? 1 : 0
            );
            // concatenate order information variables to $data
            $data = http_build_query($form_data, null, '&');
            unset($result);

            $url = $this->payment_checking_url;

            $agent = "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.0)";
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_VERBOSE, 1);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 2);
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
            curl_setopt($ch, CURLOPT_TIMEOUT, 120);
            curl_setopt($ch, CURLOPT_USERAGENT, $agent);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);  //Windows 2003 Compatibility
            $result = curl_exec($ch);
            curl_close($ch);

            $xml_array = simplexml_load_string($result);

            /*             * *****************************************************
              Possible reply from gudang voucher
              SUCCESS -- Successful payment
              Access Denied -- Not Accessible
              Transaction Fail -- Cannot retrieve transaction
             * ***************************************************** */
            $gudang_voucher_payment_history_data_array['gudang_voucher_orders_id'] = $order_id;
            if (isset($xml_array->status) && $xml_array->status != '') {
                if ($xml_array->status == 'SUCCESS') {
                    $this->check_trans_status_flag = true;

                    $gv_success_sign_param_value = $this->gudang_voucher_merchant_key . $this->gudang_voucher_merchant_code . $custom_signature . $gudang_voucher_signature_row['gudang_voucher_reference_id'];
                    $gv_success_signature = md5($gv_success_sign_param_value);

                    if ($xml_array->signature != $gv_success_signature) {
                        $xml_array->status = 'FAILED (SIGNATURE NOT MATCH)';
                    }
                }

                $gudang_voucher_payment_history_data_array = array('gudang_voucher_orders_id' => $order_id,
                    'gudang_voucher_status_reference_id' => $xml_array->reference,
                    'gudang_voucher_status_key' => $xml_array->status,
                    'gudang_voucher_status_signature' => $xml_array->signature,
                    'gudang_voucher_status_date' => 'now()',
                    'gudang_voucher_changed_by' => (isset($path_info_array["basename"]) && $path_info_array["basename"] == 'cron_order_payment.php' ? 'cronjob' : (isset($_SESSION['login_email_address']) ? $_SESSION['login_email_address'] : 'system' ))
                );

                $gudang_voucher_payment_data_array = array('gudang_voucher_tran_status' => $xml_array->status);
                tep_db_perform(TABLE_GUDANG_VOUCHER, $gudang_voucher_payment_data_array, 'update', "gudang_voucher_orders_id = '" . tep_db_input($order_id) . "'");
            } else if (isset($xml_array->error)) {
                $gudang_voucher_payment_history_data_array = array('gudang_voucher_orders_id' => $order_id,
                    'gudang_voucher_status_key' => $xml_array->error,
                    'gudang_voucher_status_date' => 'now()',
                    'gudang_voucher_changed_by' => (isset($path_info_array["basename"]) && $path_info_array["basename"] == 'cron_order_payment.php' ? 'cronjob' : (isset($_SESSION['login_email_address']) ? $_SESSION['login_email_address'] : 'system' ))
                );
            } else {
                $gudang_voucher_payment_history_data_array = array('gudang_voucher_orders_id' => $order_id,
                    'gudang_voucher_status_key' => 'UNKNOWN',
                    'gudang_voucher_status_date' => 'now()',
                    'gudang_voucher_changed_by' => (isset($path_info_array["basename"]) && $path_info_array["basename"] == 'cron_order_payment.php' ? 'cronjob' : (isset($_SESSION['login_email_address']) ? $_SESSION['login_email_address'] : 'system' ))
                );
            }

            tep_db_perform(TABLE_GUDANG_VOUCHER_STATUS_HISTORY, $gudang_voucher_payment_history_data_array);
        }

        return $xml_array;
    }

    function hex2ascii($hex) {
        $ascii = '';
        $hex = str_replace(' ', '', $hex);
        for ($i = 0; $i < strlen($hex); $i = $i + 2) {
            $ascii.= chr(hexdec(substr($hex, $i, 2)));
        }
        return($ascii);
    }

    function get_orders_payment_info_file() {
        return DIR_FS_CATALOG_MODULES . 'payment/gudang_voucher/admin/orders.inc.php';
    }

    function get_orders_list_payment_info_file() {
        return DIR_FS_CATALOG_MODULES . 'payment/gudang_voucher/admin/orders_list.inc.php';
    }

    function get_ipn_class_file() {
        return DIR_FS_CATALOG_MODULES . 'payment/gudang_voucher/classes/gudang_voucher_ipn_class.php';
    }

}

?>