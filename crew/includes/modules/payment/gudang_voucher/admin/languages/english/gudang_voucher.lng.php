<?php
/*
  	$Id: iPay88.lng.php, v1.0 2007/08/13 18:08:05
  	Author : <PERSON> (<EMAIL>)
  	Title: iPay88 Payment Module V1.0
	
	Revisions:
		Version 1.0 Initial Payment Module
	
  	Copyright (c) 2007 SKC Venture
	
  	Released under the GNU General Public License
*/

//begin ADMIN text
define('ENTRY_GUDANG_VOUCHER_PAYMENT_METHOD', 'Gudang Voucher Payment Method:');
define('ENTRY_GUDANG_VOUCHER_TRANSACTION_ID', 'Transaction ID:');
define('ENTRY_GUDANG_VOUCHER_VOUCHER_CODE', 'Voucher Code:');
define('ENTRY_GUDANG_VOUCHER_PAYMENT_CURRENCY', 'Currency:');
define('ENTRY_GUDANG_VOUCHER_PAYMENT_AMOUNT', 'Amount:');
define('ENTRY_GUDANG_VOUCHER_PAYMENT_STATUS', 'Payment Status:');

define('TEXT_STATUS_UNKNOWN', 'Refer Transaction History');

define('TABLE_HEADING_GUDANG_VOUCHER_DATE', 'History Date');
define('TABLE_HEADING_GUDANG_VOUCHER_STATUS', 'Status');

define('MODULE_PAYMENT_GUDANG_VOUCHER_LNG_MERCHANT_CODE', 'Merchant Code');
define('MODULE_PAYMENT_GUDANG_VOUCHER_LNG_MERCHANT_KEY', 'Merchant Key');
define('MODULE_PAYMENT_TAX', 'Tax');

?>