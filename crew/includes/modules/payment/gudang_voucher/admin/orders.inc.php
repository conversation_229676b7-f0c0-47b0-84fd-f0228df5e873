<?php

require_once(DIR_FS_CATALOG_MODULES . 'payment/gudang_voucher.php');
include_once(DIR_FS_CATALOG_MODULES . 'payment/gudang_voucher/admin/languages/' . $language . '/gudang_voucher.lng.php');

$gudang_voucher = new gudang_voucher();

$gudang_voucher_trans_info_select_sql = "SELECT * FROM " . TABLE_GUDANG_VOUCHER . " WHERE gudang_voucher_orders_id = '" . (int)$oID . "'";
$gudang_voucher_trans_info_result_sql= tep_db_query($gudang_voucher_trans_info_select_sql);

$gudang_voucher_trans_history_select_sql = "SELECT gudang_voucher_status_key, gudang_voucher_status_date 
                                            FROM " . TABLE_GUDANG_VOUCHER_STATUS_HISTORY . "
                                            WHERE gudang_voucher_orders_id = '" . (int)$oID  . "' 
                                            ORDER BY gudang_voucher_status_date";
$gudang_voucher_trans_history_result_sql = tep_db_query($gudang_voucher_trans_history_select_sql);
?>
<table border="0" width="100%" cellspacing="0" cellpadding="2">
<? ob_start(); ?>
	<tr>
		<td class="main">
      		<table border="0" width="100%" cellspacing="0" cellpadding="2">
      			<tr>
      				<td class="main" width="12%" nowrap><b><?=ENTRY_PAYMENT_METHOD?></b></td>
      				<td class="main">
      					<div>
      						<? if((int)$order->info['payment_methods_id']>0) { ?><div class="<?=((int)$payment_info_array[$order->info['payment_methods_id']]->confirm_complete_days>0?'rp_icon':'nrp_icon')?>"><b><?=((int)$payment_info_array[$order->info['payment_methods_id']]->confirm_complete_days>0?'RP':'NRP')?></b></div> <? } ?>
      						<div style="width:30px; height:15px; background-color:<?=$payment_module_info['display_colour']?>; float:left; vertical-align:top; text-align:center;">&nbsp;</div>
      						<div style="vertical-align: top">
      							&nbsp;&nbsp;
<?
	if ($view_payment_details_permission) {
		if (tep_not_null($last_payment_method) && $last_payment_method['payment_methods_id'] != $order->info['payment_methods_id']) {
			echo '<span class="redIndicator">'.(tep_not_null($payment_module_info['payment_methods_parent_title'])?$payment_module_info['payment_methods_parent_title'] . ' - ' :'') . $payment_module_info['payment_methods_title'].'</span>';
		} else {
			echo (tep_not_null($payment_module_info['payment_methods_parent_title'])?$payment_module_info['payment_methods_parent_title'] . ' - ' :'') . $payment_module_info['payment_methods_title'];
		}
		
		if (tep_not_null($last_payment_method) && $last_payment_method['payment_methods_id'] != $order->info['payment_methods_id']) {
			echo '&nbsp;&nbsp;<span class="redIndicator">(Last Payment Method: ' . (tep_not_null($last_payment_method['parent_payment_methods_title'])? $last_payment_method['parent_payment_methods_title'] . ' - ' : '' ) . $last_payment_method['payment_methods_title'] . ')</span>';
		}
	} else {
		echo (tep_not_null($payment_module_info['payment_methods_parent_title'])?$payment_module_info['payment_methods_parent_title'] . ' - ' :'') . $payment_module_info['payment_methods_title'];
	}
?>
							</div>
      					</div>
      				</td>
				</tr>
			</table>
		</td>
	</tr>
<?
$payment_method_title_info = ob_get_contents();
ob_end_clean();

if (tep_db_num_rows($gudang_voucher_trans_info_result_sql) || tep_db_num_rows($gudang_voucher_trans_history_result_sql)) {
	$gudang_voucher_trans_info_row = tep_db_fetch_array($gudang_voucher_trans_info_result_sql);
	
	echo $payment_method_title_info;
	if (!$view_payment_details_permission) {
		;
	} else {
?>
  	<tr>
    	<td class="main">
      		<table border="0" width="100%" cellspacing="0" cellpadding="2">
      			<tr valign="top">
        			<td width="35%">
              			<table border="0" cellspacing="0" cellpadding="2">
              				<tr>
                				<td class="main" valign="top" nowrap><b><?=ENTRY_GUDANG_VOUCHER_PAYMENT_METHOD?></b>&nbsp;</td>
                				<td class="main" nowrap><?=$gudang_voucher->gudang_voucherPMID[$gudang_voucher_trans_info_row["gudang_voucher_payment_method"]]?></td>
              				</tr>
              				<tr>
                				<td class="main" valign="top" nowrap><b><?=ENTRY_GUDANG_VOUCHER_TRANSACTION_ID?></b>&nbsp;</td>
                				<td class="main"><?=$gudang_voucher_trans_info_row["gudang_voucher_reference_id"]?></td>
              				</tr>
              				<tr>
                				<td class="main" valign="top" nowrap><b><?=ENTRY_GUDANG_VOUCHER_VOUCHER_CODE?></b>&nbsp;</td>
                				<td class="main" nowrap><?=$gudang_voucher_trans_info_row["gudang_voucher_voucher_code"]?></td>
              				</tr>
              				<tr>
                				<td class="main" valign="top" nowrap><b><?=ENTRY_GUDANG_VOUCHER_PAYMENT_CURRENCY?></b>&nbsp;</td>
                				<td class="main" nowrap><?=$gudang_voucher_trans_info_row["gudang_voucher_currency"]?></td>
              				</tr>
              				<tr>
                				<td class="main" valign="top" nowrap><b><?=ENTRY_GUDANG_VOUCHER_PAYMENT_AMOUNT?></b>&nbsp;</td>
                				<td class="main" nowrap><?=$gudang_voucher_trans_info_row["gudang_voucher_amount"]?></td>
              				</tr>
              				<tr>
                				<td class="main" valign="top" nowrap><b><?=ENTRY_GUDANG_VOUCHER_PAYMENT_STATUS?></b>&nbsp;</td>
                				<td class="main" nowrap>
	              				<?
                					echo tep_not_null($gudang_voucher_trans_info_row["gudang_voucher_tran_status"]) ? $gudang_voucher_trans_info_row["gudang_voucher_tran_status"] : TEXT_STATUS_UNKNOWN;
                					
                					if ($order->info['orders_status'] == '1' || $order->info['orders_status'] == '7' || $order->info['orders_status'] == '2') {
	                					echo "&nbsp;&nbsp;";
										echo tep_draw_form('gudang_voucher_status_check_form', FILENAME_ORDERS, tep_get_all_get_params(), 'post');
										echo tep_draw_hidden_field('subaction', 'payment_action');
										echo tep_draw_hidden_field('payment_action', 'check_trans_status');
										echo tep_submit_button('Check Payment Status', 'Check Payment Status', 'name="GVCheckTransStatusBtn"', 'inputButton');
										echo "</form>";
									}
								?>
								</td>
							</tr>
              			</table>
        			</td>
        			<td>
              			<table border="0" cellspacing="0" cellpadding="2">
              				<tr>
                				<td class="main" colspan="2" nowrap>
                					<table border="1" cellspacing="0" cellpadding="2">
        								<tr>
                							<td class="smallText" nowrap><b><?=TABLE_HEADING_GUDANG_VOUCHER_DATE?></b></td>
                							<td class="smallText" nowrap><b><?=TABLE_HEADING_GUDANG_VOUCHER_STATUS?></b></td>
                						</tr>
<?		while ($gudang_voucher_trans_history_row = tep_db_fetch_array($gudang_voucher_trans_history_result_sql)) {
        	echo ' 						<tr>
                							<td class="smallText" nowrap>'.$gudang_voucher_trans_history_row['gudang_voucher_status_date'].'</td>
                							<td class="smallText" nowrap>'.($gudang_voucher_trans_history_row['gudang_voucher_status_key'] == 'SUCCESS' ? 'Successful payment' : (tep_not_null($gudang_voucher_trans_history_row['gudang_voucher_status_key']) ? $gudang_voucher_trans_history_row['gudang_voucher_status_key'] : 'Unknown')).'</td>
                						</tr>';
     	}
?>
                					</table>
                				</td>
                			</tr>
              			</table>
              		</td>
      			</tr>
      		</table>
   		</td>
  	</tr>
<?
	}
} else {
	echo $payment_method_title_info;
?>	
	<tr>
		<td class="main" nowrap>
		<?
			if ($order->info['orders_status'] == '1' || $order->info['orders_status'] == '7' || $order->info['orders_status'] == '2') {
				echo "&nbsp;&nbsp;";
				echo tep_draw_form('gudang_voucher_status_check_form', FILENAME_ORDERS, tep_get_all_get_params(), 'post');
				echo tep_draw_hidden_field('subaction', 'payment_action');
				echo tep_draw_hidden_field('payment_action', 'check_trans_status');
				echo tep_submit_button('Check Payment Status', 'Check Payment Status', 'name="GVCheckTransStatusBtn"', 'inputButton');
				echo "</form>";
			}
		?>
		</td>
	</tr>
<?
}
?>
</table>