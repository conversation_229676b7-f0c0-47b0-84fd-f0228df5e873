<?php

require_once(DIR_FS_CATALOG_MODULES . 'payment/gudang_voucher.php');
include_once(DIR_FS_CATALOG_MODULES . 'payment/gudang_voucher/admin/languages/' . $language . '/gudang_voucher.lng.php');

$gudang_voucher = new gudang_voucher();

$gudang_voucher_trans_info_select_sql = "   SELECT gudang_voucher_payment_method, gudang_voucher_reference_id, gudang_voucher_voucher_code, gudang_voucher_currency, gudang_voucher_amount, gudang_voucher_tran_status
                                            FROM " . TABLE_GUDANG_VOUCHER . " 
                                            WHERE gudang_voucher_orders_id = '" . $order_obj->orders_id . "'";
$gudang_voucher_trans_info_result_sql= tep_db_query($gudang_voucher_trans_info_select_sql);
?>
<table border="0" width="100%" cellspacing="0" cellpadding="2">
	<tr>
		<td class="subInvoiceBoxHeading">
			<div style="border-bottom: 1px solid #996600; border-top: 1px solid #996600; padding: 2px;">Payment Details</div>
		</td>
	</tr>
<?
if ($gudang_voucher_trans_info_row = tep_db_fetch_array($gudang_voucher_trans_info_result_sql)) {
?>
  	<tr>
    	<td class="main">
      		<table border="0" width="100%" cellspacing="0" cellpadding="2">
      			<tr valign="top">
        			<td width="100%">
              			<table border="0" cellspacing="0" cellpadding="2">
              				<tr>
                				<td class="invoiceRecords" valign="top" nowrap><?=ENTRY_GUDANG_VOUCHER_PAYMENT_METHOD?></td>
                				<td class="invoiceRecords" valign="top">:&nbsp;</td>
                				<td class="invoiceRecords" nowrap><?=$gudang_voucher->gudang_voucherPMID[$gudang_voucher_trans_info_row["gudang_voucher_payment_method"]]?></td>
              				</tr>
              				<tr>
                				<td class="invoiceRecords" valign="top" nowrap><?=ENTRY_GUDANG_VOUCHER_TRANSACTION_ID?></td>
                				<td class="invoiceRecords" valign="top">:&nbsp;</td>
                				<td class="invoiceRecords" nowrap><?=$gudang_voucher_trans_info_row["gudang_voucher_reference_id"]?></td>
              				</tr>
              				<tr>
                				<td class="invoiceRecords" valign="top" nowrap><?=ENTRY_GUDANG_VOUCHER_VOUCHER_CODE?></td>
                				<td class="invoiceRecords" valign="top">:&nbsp;</td>
                				<td class="invoiceRecords"><?=$gudang_voucher_trans_info_row["gudang_voucher_voucher_code"]?></td>
              				</tr>
              				<tr>
                				<td class="invoiceRecords" valign="top" nowrap><?=ENTRY_GUDANG_VOUCHER_PAYMENT_CURRENCY?></td>
                				<td class="invoiceRecords" valign="top">:&nbsp;</td>
                				<td class="invoiceRecords" nowrap><?=$gudang_voucher_trans_info_row["gudang_voucher_currency"]?></td>
              				</tr>
              				<tr>
                				<td class="invoiceRecords" valign="top" nowrap><?=ENTRY_GUDANG_VOUCHER_PAYMENT_AMOUNT?></td>
                				<td class="invoiceRecords" valign="top">:&nbsp;</td>
                				<td class="invoiceRecords" nowrap><?=$gudang_voucher_trans_info_row["gudang_voucher_amount"]?></td>
              				</tr>
              				<tr>
                				<td class="invoiceRecords" valign="top" nowrap><?=ENTRY_GUDANG_VOUCHER_PAYMENT_STATUS?></td>
                				<td class="invoiceRecords" valign="top">:&nbsp;</td>
                				<td class="invoiceRecords" nowrap><?=tep_not_null($gudang_voucher_trans_info_row["gudang_voucher_tran_status"]) ? $gudang_voucher_trans_info_row["gudang_voucher_tran_status"] : TEXT_STATUS_UNKNOWN;?></td>
              				</tr>
              			</table>
        			</td>
      			</tr>
      		</table>
   		</td>
  	</tr>
<?
} else {
?>
	<tr>
		<td class="invoiceRecords">No further payment information is available.</td>
	</tr>
<?
}
?>
</table>