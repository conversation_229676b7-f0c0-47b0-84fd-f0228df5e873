<?php

class gudang_voucher_ipn {

    var $key, $ipn_response;

    function gudang_voucher_ipn($post_vars) {
        $this->init($post_vars);
    }

    function init($post_vars) {
        $this->key = array();
        $this->ipn_response = '';
        $this->test_mode = 'Off';

        reset($post_vars);
        foreach ($post_vars as $var => $val) {
            $val = tep_db_prepare_input(trim($val));
            if ($var != '') {
                $this->key[$var] = $val;
            }
        }
        unset($post_vars);
        if (isset($this->key['development']) && $this->key['development'] == 'YES') {
            $this->test_mode = 'On';
        }
    }

    //Test Merchant Code
    function validate_receiver_account($merchant_id) {
        if (!strcmp(strtolower($merchant_id), strtolower($this->key['merchant_id']))) {
            return true;
        } else {
            return false;
        }
    }

    function validate_transaction_data($gudang_voucher) {
        /*         * ********************************************************
          4. Compare the Signature from GudangVoucher with your own
          /******************************************************** */

        // Check if this order already exists
        $payment_record_select_sql = "	SELECT gudang_voucher_orders_id
										FROM " . TABLE_GUDANG_VOUCHER . "
										WHERE gudang_voucher_orders_id = '" . tep_db_input($this->key['order_id']) . "'";
        $payment_record_result_sql = tep_db_query($payment_record_select_sql);
        if (tep_db_num_rows($payment_record_result_sql) == 0) {
            return false;
        }

        // Concatenation of merchant key, merchant code, order id, gudang_voucher amount, gudang_voucher currency
        $signature_payment_gateway_amount = preg_replace('/[^\d]/', '', preg_quote($this->key['nominal']));

        $calculated_gudang_voucher_signature = base64_encode($gudang_voucher->hex2ascii(sha1($gudang_voucher->gudang_voucher_merchant_code . $gudang_voucher->gudang_voucher_merchant_key . $this->key['order_id'] . $signature_payment_gateway_amount . strtoupper($this->key['currency']))));
        $returned_signature = $this->key['signature'];

        // Checking 4
        if ((isset($this->key['signature']) && $calculated_gudang_voucher_signature == $returned_signature)) { // Some error does not post back Signature
; // Some error does not post back Signature
        } else {
            $gudang_voucher_payment_history_data_array = array('gudang_voucher_orders_id' => $this->key['order_id'],
                'gudang_voucher_status_key' => 'Invalid signature',
                'gudang_voucher_status_date' => 'now()',
                'gudang_voucher_changed_by' => 'system'
            );
            tep_db_perform(TABLE_GUDANG_VOUCHER_STATUS_HISTORY, $gudang_voucher_payment_history_data_array);

            return false;
        }

        return true;
    }

    function get_order_id() {
        return $this->key['order_id'];
    }

    function authenticate($order, $orders_id, $payment_method_obj) {
        global $currencies;
        /*         * ************************************************************************************
          2. Check the payment amount from gudang_voucher is match with yours
          3. Do re-query payment status for successful payment transaction to double confirm
         * ************************************************************************************ */
        $GVCurrency = $order->info['currency'];
        if (!in_array($GVCurrency, $payment_method_obj->GVCurrencies)) {
            $GVCurrency = $payment_method_obj->defCurr;
        }

        $path_info_array = pathinfo($_SERVER['SCRIPT_FILENAME']);
        $gudang_voucher_gross_amt = number_format(preg_replace('/[^\d]/', '', preg_quote($this->key['nominal'])), $currencies->currencies[$GVCurrency]['decimal_places'], $currencies->currencies[$GVCurrency]['decimal_point'], $currencies->currencies[$GVCurrency]['thousands_point']);

        if ($currencies->currencies[$GVCurrency]['symbol_left'] . $gudang_voucher_gross_amt . $currencies->currencies[$GVCurrency]['symbol_right'] != $order->info['total']) {
            $gudang_voucher_payment_history_data_array = array('gudang_voucher_orders_id' => $orders_id,
                'gudang_voucher_status_key' => tep_db_prepare_input('Error: Amount not match (' . $currencies->currencies[$GVCurrency]['symbol_left'] . $gudang_voucher_gross_amt . $currencies->currencies[$GVCurrency]['symbol_right'] . ' != ' . $order->info['total'] . ')[Post]'),
                'gudang_voucher_status_date' => 'now()',
                'gudang_voucher_changed_by' => 'system'
            );
            tep_db_perform(TABLE_GUDANG_VOUCHER_STATUS_HISTORY, $gudang_voucher_payment_history_data_array);

            return false;
        }

        $xml_array = $payment_method_obj->check_trans_status($orders_id);

        $gv_param_returned_value = $payment_method_obj->gudang_voucher_merchant_key . $payment_method_obj->gudang_voucher_merchant_code . $custom_signature . $xml_array->reference;
        $gv_param_returned_signature = md5($gv_param_returned_value);

        if (isset($xml_array->status) && $xml_array->status == 'SUCCESS') {
            $this->process_this_order($orders_id, $payment_method_obj, $payment_method_obj->order_processing_status);
        }

        return ($xml_array->status == 'SUCCESS' ? true : false);
    }

    function process_this_order($orders_id, $payment_method_obj, $update_to_status) {
        global $currencies, $languages_id;

        $orders_status_array = array();
        $orders_status_query = tep_db_query("SELECT orders_status_id, orders_status_name from " . TABLE_ORDERS_STATUS . " WHERE language_id = '" . (int) $languages_id . "' ORDER BY orders_status_sort_order");
        while ($orders_status = tep_db_fetch_array($orders_status_query)) {
            $orders_status_array[$orders_status['orders_status_id']] = $orders_status['orders_status_name'];
        }

        // Call this function when money reach our account
        $cur_order = new order($orders_id);

        if ($cur_order->info['orders_status_id'] == $payment_method_obj->order_status) { // If the order still in initial status
            $order_comment = '';
            $customer_notification = (SEND_EMAILS == 'true') ? '1' : '0';

            //tep_db_query("insert into " . TABLE_ORDERS_STATUS_HISTORY . " (orders_id, orders_status_id, date_added, customer_notified, comments) values ('" . tep_db_input($orders_id) . "', '" . $update_to_status . "', now(), '" . $customer_notification . "', '" . tep_db_input($order_comment) . "')");

            $orders_status_history_perform_array = array('action' => 'insert',
                'data' => array('orders_id' => $orders_id,
                    'orders_status_id' => $update_to_status,
                    'date_added' => 'now()',
                    'customer_notified' => $customer_notification,
                    'comments' => $order_comment
                )
            );

            $cur_order->update_order_status($update_to_status, $orders_status_history_perform_array, true);

            unset($orders_status_history_perform_array);

            // E-mail section
            $customer_profile_select_sql = "SELECT customers_gender, customers_firstname, customers_lastname
											FROM " . TABLE_CUSTOMERS . "
											WHERE customers_id = '" . tep_db_input($cur_order->customer['id']) . "'";
            $customer_profile_result_sql = tep_db_query($customer_profile_select_sql);
            if ($customer_profile_row = tep_db_fetch_array($customer_profile_result_sql)) {
                $email_firstname = $customer_profile_row["customers_firstname"];
                $email_lastname = $customer_profile_row["customers_lastname"];
            } else {
                $email_firstname = $cur_order->customer['name'];
                $email_lastname = $cur_order->customer['name'];
            }

            $email_greeting = tep_get_email_greeting($email_firstname, $email_lastname, $customer_profile_row["customers_gender"]);

            $email_order = $email_greeting . sprintf(EMAIL_TEXT_ORDER_THANK_YOU, $currencies->format($cur_order->info['total_value'], true, $cur_order->info['currency'])) . "\n\n";

            if (is_object($payment_method_obj)) {
                $email_order .= EMAIL_TEXT_PAYMENT_METHOD . "\n" .
                        EMAIL_SEPARATOR . "\n";
                $payment_class = $payment_method_obj;
                $email_order .= strip_tags($payment_class->title) . "\n";
                if ($payment_class->email_footer) {
                    $email_order .= $payment_class->email_footer . "\n\n";
                } else {
                    $email_order .= "\n";
                }
            }

            if ($cur_order->delivery !== false) {
                $order_shipping_address = tep_address_format($cur_order->delivery['format_id'], $cur_order->delivery, 0, '', "\n");
                $email_order .= EMAIL_TEXT_DELIVERY_ADDRESS . "\n" .
                        EMAIL_SEPARATOR . "\n" .
                        $order_shipping_address . "\n\n";
            }

            $email_order .= EMAIL_TEXT_BILLING_ADDRESS . "\n" .
                    EMAIL_SEPARATOR . "\n" .
                    tep_address_format($cur_order->billing['format_id'], $cur_order->billing, 0, '', "\n") . "\n\n";

            $email_order .= EMAIL_TEXT_ORDER_SUMMARY . "\n" .
                    EMAIL_SEPARATOR . "\n" .
                    EMAIL_TEXT_ORDER_NUMBER . ' ' . $orders_id . "\n" .
                    EMAIL_TEXT_DATE_ORDERED . ' ' . tep_date_long($cur_order->info['date_purchased']) . ".\n\n";

            $email_order .= EMAIL_TEXT_PRODUCTS . "\n" .
                    EMAIL_SEPARATOR . "\n" .
                    $this->get_products_ordered($cur_order) .
                    EMAIL_SEPARATOR . "\n";

            for ($i = 0, $n = sizeof($cur_order->totals); $i < $n; $i++) {
                $email_order .= strip_tags($cur_order->totals[$i]['title']) . ' ' . strip_tags($cur_order->totals[$i]['text']) . "\n";
            }

            $orders_history_query = tep_db_query("select comments from " . TABLE_ORDERS_STATUS_HISTORY . " where orders_id = '" . tep_db_input($orders_id) . "' order by date_added limit 1");
            if (tep_db_num_rows($orders_history_query)) {
                $orders_history = tep_db_fetch_array($orders_history_query);
                $cur_order->info['comments'] = $orders_history['comments'];
            }

            if ($order->info['comments']) {
                $email_order .= "\n" . EMAIL_TEXT_EXTRA_INFO . "\n" .
                        tep_db_output(tep_db_prepare_input($cur_order->info['comments']));
            }

            $email_order .= "\n\n";

            $email_order = $email_order . EMAIL_TEXT_CLOSING . "\n\n" . EMAIL_FOOTER;

            tep_mail($cur_order->customer['name'], $cur_order->customer['email_address'], implode(' ', array(EMAIL_SUBJECT_PREFIX, sprintf(EMAIL_TEXT_SUBJECT, tep_db_input($orders_id)))), $email_order, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);

            // send emails to other people
            if (tep_not_null(SEND_EXTRA_ORDER_EMAILS_TO)) {
                // A copy of email to admin when new order is made
                $extra_email_array = explode(',', SEND_EXTRA_ORDER_EMAILS_TO);
                $extra_email_email_pattern = "/([^<]*?)(?:<)([^>]+)(?:>)/is";
                for ($receiver_cnt = 0; $receiver_cnt < count($extra_email_array); $receiver_cnt++) {
                    if (preg_match($extra_email_email_pattern, $extra_email_array[$receiver_cnt], $regs)) {
                        $receiver_name = trim($regs[1]);
                        $receiver_email = trim($regs[2]);
                        tep_mail($receiver_name, $receiver_email, implode(' ', array(EMAIL_SUBJECT_PREFIX, sprintf(EMAIL_TEXT_SUBJECT, tep_db_input($orders_id)))), $email_order, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
                    }
                }
            }

            // Send order update
            tep_status_update_notification('C', tep_db_input($orders_id), 'system', $cur_order->info['orders_status_id'], $update_to_status, 'A');

            // Send affiliate notification e-mail
            tep_send_affiliate_notification($orders_id, $cur_order);
        }
    }

    function get_products_ordered($order) {
        global $currencies, $languages_id;

        $products_ordered = '';
        for ($i = 0, $n = sizeof($order->products); $i < $n; $i++) {
            $product_instance_id = tep_not_null($order->products[$i]['custom_content']['hla_account_id']) ? $order->products[$i]['custom_content']['hla_account_id'] : '';
            $currencies->product_instance_id = $product_instance_id;

            //------insert customer choosen option to order--------
            $attributes_exist = '0';
            $products_ordered_attributes = '';
            if (isset($order->products[$i]['attributes'])) {
                $attributes_exist = '1';
                for ($j = 0, $n2 = sizeof($order->products[$i]['attributes']); $j < $n2; $j++) {
                    if (DOWNLOAD_ENABLED == 'true') {
                        $attributes_query = "	select popt.products_options_name, poval.products_options_values_name, pa.options_values_price, pa.price_prefix, pad.products_attributes_maxdays, pad.products_attributes_maxcount , pad.products_attributes_filename
		                               			from " . TABLE_PRODUCTS_OPTIONS . " popt, " . TABLE_PRODUCTS_OPTIONS_VALUES . " poval, " . TABLE_PRODUCTS_ATTRIBUTES . " pa
		                               			left join " . TABLE_PRODUCTS_ATTRIBUTES_DOWNLOAD . " pad
		                                			on pa.products_attributes_id=pad.products_attributes_id
		                               			where pa.products_id = '" . $order->products[$i]['id'] . "'
					                                and pa.options_id = '" . $order->products[$i]['attributes'][$j]['option_id'] . "'
					                                and pa.options_id = popt.products_options_id
					                                and pa.options_values_id = '" . $order->products[$i]['attributes'][$j]['value_id'] . "'
					                                and pa.options_values_id = poval.products_options_values_id
					                                and popt.language_id = '" . $languages_id . "'
					                                and poval.language_id = '" . $languages_id . "'";
                        $attributes = tep_db_query($attributes_query);
                    } else {
                        $attributes = tep_db_query("select popt.products_options_name, poval.products_options_values_name, pa.options_values_price, pa.price_prefix from " . TABLE_PRODUCTS_OPTIONS . " popt, " . TABLE_PRODUCTS_OPTIONS_VALUES . " poval, " . TABLE_PRODUCTS_ATTRIBUTES . " pa where pa.products_id = '" . $order->products[$i]['id'] . "' and pa.options_id = '" . $order->products[$i]['attributes'][$j]['option_id'] . "' and pa.options_id = popt.products_options_id and pa.options_values_id = '" . $order->products[$i]['attributes'][$j]['value_id'] . "' and pa.options_values_id = poval.products_options_values_id and popt.language_id = '" . $languages_id . "' and poval.language_id = '" . $languages_id . "'");
                    }
                    $attributes_values = tep_db_fetch_array($attributes);
                    if ((DOWNLOAD_ENABLED == 'true') && isset($attributes_values['products_attributes_filename']) && tep_not_null($attributes_values['products_attributes_filename'])) {
                        $sql_data_array = array('orders_id' => $orders_id,
                            'orders_products_id' => $order->products[$i]['orders_products_id'],
                            'orders_products_filename' => $attributes_values['products_attributes_filename'],
                            'download_maxdays' => $attributes_values['products_attributes_maxdays'],
                            'download_count' => $attributes_values['products_attributes_maxcount']);
                        tep_db_perform(TABLE_ORDERS_PRODUCTS_DOWNLOAD, $sql_data_array);
                    }
                    $products_ordered_attributes .= "\n\t" . $attributes_values['products_options_name'] . ' ' . $attributes_values['products_options_values_name'];
                }
            }
            //------insert customer choosen option eof ----
            $total_weight += ($order->products[$i]['qty'] * $order->products[$i]['weight']);
            $total_tax += tep_calculate_tax($total_products_price, $products_tax) * $order->products[$i]['qty'];
            $total_cost += $total_products_price;

            $cat_path = tep_output_generated_category_path($order->products[$i]['id'], 'product');
            $product_email_display_str = $cat_path . (tep_not_null($cat_path) ? ' > ' : '') . $order->products[$i]['name'] . (tep_not_null($order->products[$i]['model']) ? ' (' . $order->products[$i]['model'] . ')' : '');

            $products_ordered .= $order->products[$i]['qty'] . ' x ' . $product_email_display_str . ' = ' . $currencies->display_price($order->products[$i]['id'], $order->products[$i]['final_price'], $order->products[$i]['tax'], $order->products[$i]['qty']) . $products_ordered_attributes . "\n";
        }

        return $products_ordered;
    }

}

?>