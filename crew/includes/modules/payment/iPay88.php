<?php

/*
  $Id: iPay88.php,v 1.34 2015/02/16 07:43:47 chingyen Exp $

  Developer: <PERSON>
  Copyright (c) 2007 SKC Ventrue

  Released under the GNU General Public License
 */

class ipay88 {

    var $code, $title, $description, $sort_order, $enabled = false, $form_action_url, $response_referer_url;
    var $email_footer, $transaction_id, $iPay88Currencies, $defCurr, $amount_decimal;
    var $check_trans_status_flag, $ipay88_merchant_code, $ipay88_merchant_key, $ipay88_pm_ids;

    // class constructor
    function ipay88($pm_id = '') {
        global $order, $languages_id, $default_languages_id;

        //Basic info
        $this->payment_methods_id = 0;
        $this->payment_methods_parent_id = 0;

        $this->code = 'iPay88';
        $this->filename = 'iPay88.php';

        $this->force_to_checkoutprocess = true;
        $this->has_ipn = true;
        $this->auto_cancel_period = 60; // In minutes
        $this->help_icon = tep_image(DIR_WS_ICONS . 'help_info.gif', '', '12', '12', '');

        $this->transaction_id = '';

        $this->check_trans_status_flag = false;

        $payment_methods_select_sql = "	SELECT payment_methods_parent_id, payment_methods_code, 
												payment_methods_types_id, 
												payment_methods_receive_status_mode, payment_methods_id, 
												payment_methods_title, payment_methods_sort_order, 
												payment_methods_receive_status, payment_methods_legend_color, 
												payment_methods_logo 
										FROM " . TABLE_PAYMENT_METHODS . " 
										WHERE 1 ";
        if ((int) $pm_id > 0) {
            $payment_methods_select_sql .= "	AND payment_methods_id = '" . (int) $pm_id . "'";
        } else {
            $payment_methods_select_sql .= "	AND payment_methods_filename = '" . tep_db_input($this->filename) . "'";
        }

        $payment_methods_result_sql = tep_db_query($payment_methods_select_sql);
        if ($payment_methods_row = tep_db_fetch_array($payment_methods_result_sql)) {
            $pm_desc_title_select_sql = "	SELECT payment_methods_description_title 
											FROM " . TABLE_PAYMENT_METHODS_DESCRIPTION . " 
											WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "' 
												AND (IF (languages_id = '" . tep_db_input($languages_id) . "', 1, IF ((	SELECT COUNT(payment_methods_id) > 0 
																														FROM " . TABLE_PAYMENT_METHODS_DESCRIPTION . " 
																														WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "' 
																															AND languages_id = '" . (int) $languages_id . "'), 0, languages_id = 1)))";
            $pm_desc_title_result_sql = tep_db_query($pm_desc_title_select_sql);
            $pm_desc_title_row = tep_db_fetch_array($pm_desc_title_result_sql);

            $this->payment_methods_id = (int) $payment_methods_row['payment_methods_id'];
            $this->payment_methods_parent_id = (int) $payment_methods_row['payment_methods_parent_id'];
            $this->code = $payment_methods_row['payment_methods_code'];
            $this->title = $payment_methods_row['payment_methods_title']; //MODULE_PAYMENT_PAYPAL_TEXT_TITLE;
            $this->display_title = $pm_desc_title_row['payment_methods_description_title']; //MODULE_PAYMENT_PAYPAL_TEXT_DISPLAY_TITLE; 	// if title to be display for payment method is different from payment method value
            $this->sort_order = $payment_methods_row['payment_methods_sort_order']; //MODULE_PAYMENT_PAYPAL_SORT_ORDER;
            $this->enabled = $payment_methods_row['payment_methods_receive_status_mode'];
            $this->legend_display_colour = $payment_methods_row['payment_methods_legend_color'];
            $this->receive_status = $payment_methods_row['payment_methods_receive_status'];
            $this->preferred = true;
            $this->description = $this->display_title;
            $this->iPay88Currencies = $this->get_support_currencies($this->payment_methods_id);

            $configuration_setting_array = $this->load_pm_setting();
            //$this->currency = $configuration_setting_array['MODULE_PAYMENT_IPAY88_CURRENCY'];
            $this->order_status_id = $configuration_setting_array['MODULE_PAYMENT_IPAY88_ORDER_STATUS_ID'];
            $this->processing_status_id = $configuration_setting_array['MODULE_PAYMENT_IPAY88_PROCESSING_STATUS_ID'];
            //$this->zone = $configuration_setting_array['MODULE_PAYMENT_IPAY88_ZONE'];
            $this->message = $configuration_setting_array['MODULE_PAYMENT_IPAY88_MESSAGE'];
            $this->email_message = $configuration_setting_array['MODULE_PAYMENT_IPAY88_EMAIL_MESSAGE'];
            $this->confirm_complete_days = $configuration_setting_array['MODULE_PAYMENT_IPAY88_CONFIRM_COMPLETE'];
            $this->require_address_information = $configuration_setting_array['MODULE_PAYMENT_IPAY88_MANDATORY_ADDRESS_FIELD'];
            $this->require_ic_information = $configuration_setting_array['MODULE_PAYMENT_IPAY88_MANDATORY_IC_FIELD'];
            $this->require_contact_information = $configuration_setting_array['MODULE_PAYMENT_IPAY88_MANDATORY_CONTACT_FIELD'];

            $this->customer_payment_info = $configuration_setting_array['MODULE_PAYMENT_IPAY88_CUSTOMER_PAYMENT_INFO'];

            // iPay88 only accept MYR currency
            $this->defCurr = DEFAULT_CURRENCY;

            if ((int) $this->order_status_id > 0) {
                $this->order_status = $this->order_status_id;
            } else {
                $this->order_status = DEFAULT_ORDERS_STATUS_ID;
            }

            $this->order_processing_status = $this->processing_status_id;
            $this->form_action_url = 'https://www.mobile88.com/epayment/entry.asp'; // iPay88 ePayment
            $this->payment_checking_url = 'https://www.mobile88.com/epayment/enquiry.asp'; // re-query payment
            $this->response_referer_url = 'https://www.mobile88.com';
            $this->response_url = (defined(FILENAME_CHECKOUT_PROCESS) ? tep_href_link(FILENAME_CHECKOUT_PROCESS, '', 'SSL') : '');
            $this->backend_url = (defined(FILENAME_IPAY_IPN) ? tep_href_link(FILENAME_IPAY_IPN, '', 'SSL') : '');
            $this->email_footer = str_replace(array("<br>", "<BR>", "\r\n"), array("\n", "\n", ''), $this->email_message);

            $this->force_to_checkoutprocess = true;
            $this->has_ipn = true;
            $this->amount_decimal = 2;
            $this->auto_cancel_period = 60; // In minutes
        }
    }

    // class methods
    function javascript_validation() {
        $js = '	if (payment_value == "' . $this->code . '") {' . "\n" .
                '   // 	if (!checkForSelection("checkout_payment", "ipay88_pm_id")) {' . "\n" .
                '   //   		error_message = error_message + "' . MODULE_PAYMENT_IPAY88_TEXT_JS_PM . '";' . "\n" .
                '   //   		error = 1;' . "\n" .
                '   // 	}' . "\n" .
                '  }' . "\n";

        return $js;
    }

    function selection() {
        global $languages_id, $default_languages_id;

        $selection_array = array();

        $payment_methods_select_sql = "	SELECT payment_methods_id, payment_methods_code, payment_methods_types_id, payment_methods_logo, payment_methods_receive_status_mode 
										FROM " . TABLE_PAYMENT_METHODS . " 
										WHERE payment_methods_receive_status = 1 
											AND payment_methods_receive_status_mode <> 0 
											AND payment_methods_parent_id = '" . (int) $this->payment_methods_id . "' 
										ORDER BY payment_methods_sort_order";
        $payment_methods_result_sql = tep_db_query($payment_methods_select_sql);
        while ($payment_methods_row = tep_db_fetch_array($payment_methods_result_sql)) {
            $pm_desc_title_select_sql = "	SELECT payment_methods_description_title 
											FROM " . TABLE_PAYMENT_METHODS_DESCRIPTION . " 
											WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "' 
												AND (IF (languages_id = '" . tep_db_input($languages_id) . "', 1, IF ((	SELECT COUNT(payment_methods_id) > 0 
																														FROM " . TABLE_PAYMENT_METHODS_DESCRIPTION . " 
																														WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "' 
																															AND languages_id = '" . (int) $languages_id . "'), 0, languages_id = 1)))";
            $pm_desc_title_result_sql = tep_db_query($pm_desc_title_select_sql);
            $pm_desc_title_row = tep_db_fetch_array($pm_desc_title_result_sql);

            $pm_array = $this->load_pm_setting(1, $payment_methods_row['payment_methods_id']);

            $selection_array[] = array('payment_gateway_code' => $this->code,
                'payment_methods_receive_status_mode' => $payment_methods_row['payment_methods_receive_status_mode'],
                'payment_methods_id' => $payment_methods_row['payment_methods_id'],
                'payment_methods_code' => $payment_methods_row['payment_methods_code'],
                'payment_methods_types_id' => $payment_methods_row['payment_methods_types_id'],
                'payment_methods_parent_id' => $this->payment_methods_id,
                'payment_methods_logo' => $payment_methods_row['payment_methods_logo'],
                'payment_methods_description_title' => $pm_desc_title_row['payment_methods_description_title'],
                'currency' => $this->get_support_currencies($payment_methods_row['payment_methods_id']),
                'confirm_complete_days' => $pm_array['MODULE_PAYMENT_IPAY88_CONFIRM_COMPLETE'],
                'show_billing_address' => $pm_array['MODULE_PAYMENT_IPAY88_MANDATORY_ADDRESS_FIELD'],
                'show_contact_number' => $pm_array['MODULE_PAYMENT_IPAY88_MANDATORY_CONTACT_FIELD'],
                'show_ic' => $pm_array['MODULE_PAYMENT_IPAY88_MANDATORY_IC_FIELD']
            );

            if ($payment_methods_row['payment_methods_receive_status_mode'] == '-1') {
                $pm_status_message_select = "	SELECT payment_methods_status_message 
												FROM " . TABLE_PAYMENT_METHODS_STATUS_DESCRIPTION . " 
												WHERE payment_methods_mode = 'RECEIVE' 
													AND payment_methods_status = '-1' 
													AND languages_id = '" . (int) $languages_id . "' 
													AND payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "'";
                $pm_status_message_result = tep_db_query($pm_status_message_select);
                $pm_status_message_row = tep_db_fetch_array($pm_status_message_result);
                $selection_array[sizeof($selection_array) - 1]['payment_methods_status_message'] = $pm_status_message_row['payment_methods_status_message'];
            }
        }

        return $selection_array;
    }

    function set_support_currencies($currency_array) {
        $this->iPay88Currencies = $currency_array;
    }

    function get_support_currencies($pm_id, $by_zone = true) {
        global $zone_info_array;

        $default_currency = '';
        $support_currencies_array = array();

        $currency_code_select_sql = "	SELECT currency_code, payment_methods_instance_default 
    									FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " 
    									WHERE payment_methods_id = '" . (int) $pm_id . "' 
    									ORDER BY currency_code";
        $currency_code_result_sql = tep_db_query($currency_code_select_sql);

        if (tep_db_num_rows($currency_code_result_sql) > 0) {
            while ($currency_code_row = tep_db_fetch_array($currency_code_result_sql)) {
                $support_currencies_array[] = $currency_code_row['currency_code'];

                if ($currency_code_row['payment_methods_instance_default'] == 1) {
                    $default_currency = $currency_code_row['currency_code'];
                }
            }
        } else {
            if ($this->payment_methods_parent_id > 0) {
                $payment_methods_parent_id = $this->payment_methods_parent_id;
            } else {
                $payment_methods_parent_id = $this->payment_methods_id;
            }

            $parent_currency_code_select_sql = "	SELECT currency_code, payment_methods_instance_default 
			    									FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " 
			    									WHERE payment_methods_id = '" . (int) $payment_methods_parent_id . "' 
			    									ORDER BY currency_code";
            $parent_currency_code_result_sql = tep_db_query($parent_currency_code_select_sql);
            while ($parent_currency_code_row = tep_db_fetch_array($parent_currency_code_result_sql)) {
                $support_currencies_array[] = $parent_currency_code_row['currency_code'];

                if ($parent_currency_code_row['payment_methods_instance_default'] == 1) {
                    $default_currency = $parent_currency_code_row['currency_code'];
                }
            }
        }

        if (tep_not_null($default_currency)) {
            $this->defCurr = $default_currency;
        }

        if ($by_zone) {
            if (isset($zone_info_array)) {
                $support_currencies_array = array_intersect($zone_info_array[3]->zone_currency_id, $support_currencies_array);
                if (count($support_currencies_array)) {
                    array_multisort($support_currencies_array);
                } else {
                    $support_currencies_array = array($default_currency);
                }
            } else {
                $support_currencies_array = array($default_currency);
            }
        } else {
            $this->set_support_currencies($support_currencies_array);
            // All found currencies is supported. Normally this is used for backend script such as IPN call
        }

        return $support_currencies_array;
    }

    function get_require_address_information() {
        return $this->require_address_information;
    }

    function get_confirm_complete_days() {
        return $this->confirm_complete_days;
    }

    function get_pm_info() {
        return $this->message;
    }

    function get_email_pm_info() {
        return $this->email_message;
    }

    function draw_confirm_button() {
        return tep_image_button2('red', 'javascript:document.checkout_confirmation.submit()', MODULE_PAYMENT_IPAY88_TEXT_CART, 200);
    }

    function get_confirm_button_caption() {
        return MODULE_PAYMENT_IPAY88_TEXT_TITLE;
    }

    function pre_confirmation_check() {
        global $currency;

        if (in_array($currency, $this->iPay88Currencies)) {
            
        }

        return false;
    }

    function process_button() {
        global $HTTP_POST_VARS, $shipping_cost, $total_cost, $shipping_selected, $shipping_method, $currencies, $currency, $customer_id, $order, $languages_id;
        global $order_logged, $OFFGAMERS;

        if ($this->force_to_checkoutprocess && !tep_session_is_registered('order_logged'))
            return;

        // if the user currency is not supported by iPay88
        // fall back to the default currency set on the admin interface of the module
        $iPay88Currency = $currency;
        if (!in_array($iPay88Currency, $this->iPay88Currencies)) {
            $iPay88Currency = $this->iPay88Currencies[0];
        }

        $payment_gateway_amount = number_format($order->info['total'] * $currencies->get_value($iPay88Currency), $this->amount_decimal, '.', '');
        $item_name = '';

        for ($p_cnt = 0; $p_cnt < count($order->products); $p_cnt++) {
            $item_name .= ' ' . strip_tags($order->products[$p_cnt]['name']) . ' ,';
        }

        $item_name = substr_replace(trim($item_name), '', -1);
        $signature_payment_gateway_amount = preg_replace('/[^\d]/', '', preg_quote($payment_gateway_amount));

        $this->get_merchant_account($iPay88Currency);

        $iPay88_signature = base64_encode($this->hex2ascii(sha1($this->ipay88_merchant_key . $this->ipay88_merchant_code . $order_logged . $signature_payment_gateway_amount . strtoupper($iPay88Currency))));

        $process_button_string = tep_draw_hidden_field('MerchantCode', $this->ipay88_merchant_code) .
                tep_draw_hidden_field('PaymentId', $this->code) .
                tep_draw_hidden_field('RefNo', $order_logged) .
                tep_draw_hidden_field('Amount', $payment_gateway_amount) .
                tep_draw_hidden_field('Currency', $iPay88Currency) .
                tep_draw_hidden_field('ProdDesc', $item_name) .
                tep_draw_hidden_field('UserName', $order->billing['firstname'] . ' ' . $order->billing['lastname']) .
                tep_draw_hidden_field('UserEmail', $order->customer['email_address']) .
                tep_draw_hidden_field('UserContact', $order->customer['int_dialing_code'] . $order->customer['telephone']) .
                tep_draw_hidden_field('Remark', 'Purchase from ' . STORE_NAME) .
                tep_draw_hidden_field('Signature', $iPay88_signature) .
                tep_draw_hidden_field('ResponseURL', $this->response_url) .
                tep_draw_hidden_field('BackendURL', $this->backend_url);

        return $process_button_string;
    }

    // manage returning data from iPay88 (errors, failures, success etc.)
    function before_process() {
        global $order_logged;

        if (!tep_session_is_registered('order_logged'))
            return; // This line does not mean there is payment error. It means the first time order been created

        if (isset($_POST['RefNo']) && $_POST['RefNo'] == $_SESSION['order_logged']) {
            //
        } else {
            tep_redirect(tep_href_link(FILENAME_CHECKOUT_PAYMENT, 'error_message=' . urlencode($_POST['ErrDesc']), 'SSL'));
        }
    }

    function after_process() {
        return false;
    }

    function link_to_payment($new_order_id) {
        global $order_logged;

        if (tep_session_is_registered('order_logged'))
            tep_session_unregister('order_logged');

        tep_session_register('order_logged');
        $order_logged = $new_order_id;
        require(DIR_WS_INCLUDES . 'modules/payment/iPay88/catalog/iPay88_splash.php');
        return;
    }

    function get_error() {
        $error = array('title' => MODULE_PAYMENT_IPAY88_TEXT_ERROR,
            'error' => stripslashes(urldecode($_GET['error'])));
        return $error;
    }

    function check() {
        if (!isset($this->_check)) {
            $this->_check = defined('MODULE_PAYMENT_IPAY88_STATUS');
        }

        return $this->_check;
    }

    function is_processed_order($order_id) {
        $payment_status_select_sql = "	SELECT ipay88_status 
										FROM " . TABLE_PAYMENT_IPAY88 . " 
										WHERE ipay88_orders_id = '" . tep_db_input($order_id) . "'";
        $payment_status_result_sql = tep_db_query($payment_status_select_sql);
        $payment_status_row = tep_db_fetch_array($payment_status_result_sql);

        if ($payment_status_row['ipay88_status'] == 1) {
            return true;
        } else {
            $ipay88_trans_history_select_sql = "SELECT ipay88_status 
												FROM " . TABLE_PAYMENT_IPAY88_PAYMENT_STATUS_HISTORY . "
												WHERE ipay88_orders_id = '" . tep_db_input($order_id) . "' 
												ORDER BY ipay88_date DESC 
												LIMIT 1";
            $ipay88_trans_history_result_sql = tep_db_query($ipay88_trans_history_select_sql);
            $ipay88_trans_history_row = tep_db_fetch_array($ipay88_trans_history_result_sql);

            if ($ipay88_trans_history_row['ipay88_status'] == '1') {
                return true;
            } else {
                return false;
            }
        }
    }

    function is_supported_currency($selected_currency) {
        return (in_array($selected_currency, $this->iPay88Currencies) ? true : false);
    }

    function load_pm_setting($language_id = 1, $pm_id = '') {
        $pm_setting_array = array();
        //load value from DB

        if (tep_not_null($pm_id)) {
            $payment_method_id = $pm_id;

            $payment_methods_parent_id_select_sql = "	SELECT payment_methods_parent_id 
														FROM " . TABLE_PAYMENT_METHODS . " 
														WHERE payment_methods_id = '" . (int) $pm_id . "'";
            $payment_methods_parent_id_result_sql = tep_db_query($payment_methods_parent_id_select_sql);
            $payment_methods_parent_id_row = tep_db_fetch_array($payment_methods_parent_id_result_sql);

            $payment_gateway_id = $payment_methods_parent_id_row['payment_methods_parent_id'];
        } else {
            $payment_method_id = $this->payment_methods_id;
            $payment_gateway_id = $this->payment_methods_parent_id;
        }

        $payment_configuration_info_select_sql = "	SELECT pci.payment_configuration_info_key, pcid.payment_configuration_info_value
													FROM " . TABLE_PAYMENT_CONFIGURATION_INFO . " as pci
													LEFT JOIN " . TABLE_PAYMENT_CONFIGURATION_INFO_DESCRIPTION . " as pcid 
														ON pci.payment_configuration_info_id = pcid.payment_configuration_info_id
															AND pcid.languages_id = '" . (int) $language_id . "'
													WHERE pci.payment_methods_id = '" . (int) $payment_method_id . "'
													ORDER BY pci.payment_configuration_info_sort_order";
        $payment_configuration_info_result_sql = tep_db_query($payment_configuration_info_select_sql);
        if (tep_db_num_rows($payment_configuration_info_result_sql)) {
            while ($payment_configuration_info_row = tep_db_fetch_array($payment_configuration_info_result_sql)) {
                $pm_setting_array[$payment_configuration_info_row['payment_configuration_info_key']] = $payment_configuration_info_row['payment_configuration_info_value'];
            }
        } else if ((int) $payment_gateway_id > 0) { //load value from PG
            $payment_configuration_info_select_sql = "	SELECT pci.payment_configuration_info_key, pcid.payment_configuration_info_value
														FROM " . TABLE_PAYMENT_CONFIGURATION_INFO . " as pci
														LEFT JOIN " . TABLE_PAYMENT_CONFIGURATION_INFO_DESCRIPTION . " as pcid 
															ON pci.payment_configuration_info_id = pcid.payment_configuration_info_id
																AND pcid.languages_id = '" . (int) $language_id . "'
														WHERE pci.payment_methods_id = '" . (int) $payment_gateway_id . "'
														ORDER BY pci.payment_configuration_info_sort_order";
            $payment_configuration_info_result_sql = tep_db_query($payment_configuration_info_select_sql);
            while ($payment_configuration_info_row = tep_db_fetch_array($payment_configuration_info_result_sql)) {
                $pm_setting_array[$payment_configuration_info_row['payment_configuration_info_key']] = $payment_configuration_info_row['payment_configuration_info_value'];
            }
        }

        return $pm_setting_array;
    }

    function get_merchant_account($selected_currency) {
        $payment_methods_instance_id = 0;
        $payment_gateway_instance_select_sql = "SELECT pmi.payment_methods_instance_id, pmi.payment_methods_instance_follow_default, pmi.currency_code 
				 								FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " as pmi
				 								WHERE pmi.payment_methods_id = '" . (int) $this->payment_methods_id . "'
				 								LIMIT 1";
        $payment_gateway_instance_result_sql = tep_db_query($payment_gateway_instance_select_sql);
        if (tep_db_num_rows($payment_gateway_instance_result_sql)) {
            $payment_gateway_instance_select_sql = "SELECT pmi.payment_methods_instance_id, pmi.payment_methods_instance_follow_default 
					 								FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " as pmi
					 								WHERE pmi.currency_code = '" . tep_db_input($selected_currency) . "' 
					 									AND pmi.payment_methods_id = '" . (int) $this->payment_methods_id . "'";
            $payment_gateway_instance_result_sql = tep_db_query($payment_gateway_instance_select_sql);
            if ($payment_gateway_instance_row = tep_db_fetch_array($payment_gateway_instance_result_sql)) {
                if ((int) $payment_gateway_instance_row['payment_methods_instance_follow_default'] > 0) { // Follow Default Currency Setting
                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_follow_default'];
                } else { // Has own setting
                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_id'];
                }
            }
        } else {
            $payment_gateway_instance_select_sql = "SELECT pmi.payment_methods_instance_id, pmi.payment_methods_instance_follow_default 
					 								FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " as pmi
					 								INNER JOIN " . TABLE_PAYMENT_METHODS . " as pm
					 									ON pm.payment_methods_parent_id = pmi.payment_methods_id
					 								WHERE pmi.currency_code = '" . tep_db_input($selected_currency) . "' 
					 									AND pm.payment_methods_id = '" . (int) $this->payment_methods_id . "'";
            $payment_gateway_instance_result_sql = tep_db_query($payment_gateway_instance_select_sql);
            if ($payment_gateway_instance_row = tep_db_fetch_array($payment_gateway_instance_result_sql)) {
                if ((int) $payment_gateway_instance_row['payment_methods_instance_follow_default'] > 0) {
                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_follow_default'];
                } else {
                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_id'];
                }
            }
        }
        $payment_gateway_instance_setting_select_sql = "SELECT pmis.payment_methods_instance_setting_key, pmis.payment_methods_instance_setting_value 
						 								FROM " . TABLE_PAYMENT_METHODS_INSTANCE_SETTING . " as pmis
						 								WHERE pmis.payment_methods_instance_id = '" . (int) $payment_methods_instance_id . "'";
        $payment_gateway_instance_setting_result_sql = tep_db_query($payment_gateway_instance_setting_select_sql);
        while ($payment_gateway_instance_setting_row = tep_db_fetch_array($payment_gateway_instance_setting_result_sql)) {
            switch ($payment_gateway_instance_setting_row['payment_methods_instance_setting_key']) {
                case 'MODULE_PAYMENT_IPAY88_MERCHANT_CODE':
                    $this->ipay88_merchant_code = $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];
                    break;
                case 'MODULE_PAYMENT_IPAY88_MERCHANT_KEY':
                    $this->ipay88_merchant_key = $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];
                    break;
            }
        }
    }

    function install() {
        $install_configuration_flag = true;

        if ($this->payment_methods_id > 0) {
            if ($this->receive_status != 1) {
                $update_receive_status_sql_data = array("payment_methods_receive_status" => 1);
                tep_db_perform(TABLE_PAYMENT_METHODS, $update_receive_status_sql_data, 'update', "payment_methods_id = '" . (int) $this->payment_methods_id . "'");
            } else {
                $install_configuration_flag = false;
            }
        } else {
            $install_payment_gateway_data_sql = array('payment_methods_code' => $this->code,
                'payment_methods_send_status' => 0,
                'payment_methods_title' => $this->code,
                'payment_methods_types_id' => 0,
                'payment_methods_parent_id' => 0,
                'payment_methods_legend_color' => '#333333',
                'payment_methods_sort_order' => 30,
                'payment_methods_filename' => $this->filename,
                'payment_methods_logo' => '',
                'date_added' => 'now()',
                'last_modified' => 'now()',
                'payment_methods_receive_status' => 1,
                'payment_methods_receive_status_mode' => 0
            );
            tep_db_perform(TABLE_PAYMENT_METHODS, $install_payment_gateway_data_sql);
            $this->payment_methods_id = tep_db_insert_id();
        }
        if ($install_configuration_flag) {
            $install_key_array = array();
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Set Order Status',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_IPAY88_ORDER_STATUS_ID',
                    'payment_configuration_info_description' => 'Set the status of orders made with this payment module to this value',
                    'payment_configuration_info_sort_order' => '1030',
                    'set_function' => 'tep_cfg_pull_down_order_statuses(',
                    'use_function' => 'tep_get_order_status_name',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => '1',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Set Order\'s "Processing" Status',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_IPAY88_PROCESSING_STATUS_ID',
                    'payment_configuration_info_description' => 'Set the initial processing status of orders made with this payment module to this value',
                    'payment_configuration_info_sort_order' => '1035',
                    'set_function' => 'tep_cfg_pull_down_order_statuses(',
                    'use_function' => 'tep_get_order_status_name',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => '7',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Payment Zone',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_IPAY88_ZONE',
                    'payment_configuration_info_description' => 'If a zone is selected, only enable this payment method for that zone.',
                    'payment_configuration_info_sort_order' => '1040',
                    'set_function' => 'tep_cfg_pull_down_zone_classes(',
                    'use_function' => 'tep_get_zone_class_title',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => '0',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Payment Message',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_IPAY88_MESSAGE',
                    'payment_configuration_info_description' => 'Payment message will show up during checkout process (iPay88)',
                    'payment_configuration_info_sort_order' => '1045',
                    'set_function' => 'tep_cfg_textarea(',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'Thank you for shopping at SKC Store.',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Payment Email Message',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_IPAY88_EMAIL_MESSAGE',
                    'payment_configuration_info_description' => 'This message will show up in order confirmation e-mail.',
                    'payment_configuration_info_sort_order' => '1045',
                    'set_function' => 'tep_cfg_textarea(',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'Thank you for shopping at OffGamers.com.',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Confirm Complete (in days)',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_IPAY88_CONFIRM_COMPLETE',
                    'payment_configuration_info_description' => 'The number of days in which an order made by this payment method is confirm completed',
                    'payment_configuration_info_sort_order' => '1050',
                    'set_function' => '',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => '0',
                    'languages_id' => 1
                )
            );
            /* 			$install_key_array[] = array(	'info' => array (	'payment_methods_id'=>(int)$this->payment_methods_id,
              'payment_configuration_info_title'=>'Transaction Currency',
              'payment_configuration_info_key'=>'MODULE_PAYMENT_IPAY88_CURRENCY',
              'payment_configuration_info_description'=>'Select the default currency for the payment transactions. If the user selected currency is not available at iPay88, this currency will be the payment currency.',
              'payment_configuration_info_sort_order'=>'1025',
              'set_function'=>'tep_cfg_select_option(array(\'MYR\'),',
              'use_function'=>'',
              'date_added'=>'now()'
              ),
              'desc' => array (	'payment_configuration_info_value'=>'MYR',
              'languages_id' => 1
              )
              ); */
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Require Address Information',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_IPAY88_MANDATORY_ADDRESS_FIELD',
                    'payment_configuration_info_description' => 'Set address field as required info during checkout.',
                    'payment_configuration_info_sort_order' => '1030',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Customer Payment Info',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_IPAY88_CUSTOMER_PAYMENT_INFO',
                    'payment_configuration_info_description' => 'Set to true if this Payment Method required customer input for Payment Information required customer payment info.',
                    'payment_configuration_info_sort_order' => '1035',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Require IC Information',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_IPAY88_MANDATORY_IC_FIELD',
                    'payment_configuration_info_description' => 'Set IC field as required info during checkout.',
                    'payment_configuration_info_sort_order' => '1040',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Require Contact Information',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_IPAY88_MANDATORY_CONTACT_FIELD',
                    'payment_configuration_info_description' => 'Set Contact field as required info during checkout.',
                    'payment_configuration_info_sort_order' => '1045',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );
            foreach ($install_key_array as $data) {
                tep_db_perform(TABLE_PAYMENT_CONFIGURATION_INFO, $data['info']);
                $payment_conf_id = tep_db_insert_id();

                $data['desc']['payment_configuration_info_id'] = (int) $payment_conf_id;
                tep_db_perform(TABLE_PAYMENT_CONFIGURATION_INFO_DESCRIPTION, $data['desc']);
            }
        }
        return 1;
    }

    function remove() {
        tep_db_query("delete from " . TABLE_CONFIGURATION . " where configuration_key in ('" . implode("', '", $this->keys()) . "')");
    }

    function keys() {
        return array('MODULE_PAYMENT_IPAY88_STATUS',
            'MODULE_PAYMENT_IPAY88_MERCHANT_CODE',
            'MODULE_PAYMENT_IPAY88_MERCHANT_KEY',
            'MODULE_PAYMENT_IPAY88_PAYMENT_METHOD_ID',
            'MODULE_PAYMENT_IPAY88_CURRENCY',
            'MODULE_PAYMENT_IPAY88_SORT_ORDER',
            'MODULE_PAYMENT_IPAY88_ORDER_STATUS_ID',
            'MODULE_PAYMENT_IPAY88_PROCESSING_STATUS_ID',
            'MODULE_PAYMENT_IPAY88_ZONE',
            'MODULE_PAYMENT_IPAY88_MESSAGE',
            'MODULE_PAYMENT_IPAY88_EMAIL_MESSAGE',
            'MODULE_PAYMENT_IPAY88_CONFIRM_COMPLETE',
            'MODULE_PAYMENT_IPAY88_LEGEND_COLOUR');
    }

    function configuration_information_keys() {
        return array('MODULE_PAYMENT_IPAY88_CURRENCY',
            'MODULE_PAYMENT_IPAY88_ORDER_STATUS_ID',
            'MODULE_PAYMENT_IPAY88_PROCESSING_STATUS_ID',
            'MODULE_PAYMENT_IPAY88_ZONE',
            'MODULE_PAYMENT_IPAY88_MESSAGE',
            'MODULE_PAYMENT_IPAY88_EMAIL_MESSAGE',
            'MODULE_PAYMENT_IPAY88_CONFIRM_COMPLETE',
            'MODULE_PAYMENT_IPAY88_MANDATORY_ADDRESS_FIELD',
            'MODULE_PAYMENT_IPAY88_CUSTOMER_PAYMENT_INFO',
            'MODULE_PAYMENT_IPAY88_MANDATORY_IC_FIELD',
            'MODULE_PAYMENT_IPAY88_MANDATORY_CONTACT_FIELD');
    }

    function multi_lang_configuration_info_keys() {
        return array('MODULE_PAYMENT_IPAY88_MESSAGE',
            'MODULE_PAYMENT_IPAY88_EMAIL_MESSAGE');
    }

    function merchant_information_keys() {
        include_once(DIR_WS_CLASSES . "payment_methods.php");

        return array(
            'MODULE_PAYMENT_IPAY88_MERCHANT_CODE' => array("field" => 'text', 'mapping' => 'MODULE_PAYMENT_IPAY88_LNG_MERCHANT_CODE'),
            'MODULE_PAYMENT_IPAY88_MERCHANT_KEY' => array("field" => 'text', 'mapping' => 'MODULE_PAYMENT_IPAY88_LNG_MERCHANT_KEY'),
            'MODULE_PAYMENT_TAX' => array("field" => 'select', 'mapping' => "MODULE_PAYMENT_TAX", "select" => payment_methods::get_tax_option())
        );
    }

    function get_pg_id() {
        return $this->payment_methods_id;
    }

    // Parse the predefinied array to be 'module install' friendly
    // as it is used for select in the module's install() function
    function show_array($aArray, $appendKey = false) {
        $aFormatted = "array(";
        foreach ($aArray as $key => $sVal) {
            if ($appendKey) {
                $aFormatted .= "\'$key\' => \'" . str_replace('\\\\\\\\"', '"', addslashes(addslashes(addslashes($sVal)))) . "\', ";
            } else {
                $aFormatted .= "\'$sVal\', ";
            }
        }
        $aFormatted = substr($aFormatted, 0, strlen($aFormatted) - 2);
        return $aFormatted;
    }

    function check_trans_status($order_id) {
        global $currencies;

        $order_info_select_sql = "	SELECT o.payment_methods_id, o.currency, o.currency_value, ot.value 
									FROM " . TABLE_ORDERS . " AS o 
									INNER JOIN " . TABLE_ORDERS_TOTAL . " AS ot 
										ON o.orders_id=ot.orders_id AND ot.class='ot_total' 
									WHERE o.orders_id = '" . tep_db_input($order_id) . "'";
        $order_info_result_sql = tep_db_query($order_info_select_sql);

        if ($order_info_row = tep_db_fetch_array($order_info_result_sql)) {
            if ($order_info_row['payment_methods_id'] != $this->payment_methods_id)
                return;

            /*             * ************************************************************************************
              2. Check the payment amount from iPay88 is match with yours
              3. Do re-query payment status for successful payment transaction to double confirm
             * ************************************************************************************ */
            $iPay88Currency = $order_info_row['currency'];

            $this->get_merchant_account($iPay88Currency);

            $payment_amount = number_format($order_info_row['value'] * $order_info_row['currency_value'], $this->amount_decimal, '.', '');
            $signature_payment_amount = preg_replace('/[^\d.]/', '', preg_quote($payment_amount));

            // build URL to retrieve transaction result
            $data = '';

            $form_data = array('MerchantCode' => $this->ipay88_merchant_code,
                'RefNo' => $order_id,
                'Amount' => $signature_payment_amount
            );
            // concatenate order information variables to $data
            while (list($key, $value) = each($form_data)) {
                $data .= $key . '=' . urlencode(ereg_replace_dep(',', '', $value)) . '&';
            }

            // take the last & out for the string
            $data = substr($data, 0, -1);
            unset($result);

            $url = $this->payment_checking_url;

            $agent = "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.0)";
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_VERBOSE, 1);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 2);
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
            curl_setopt($ch, CURLOPT_TIMEOUT, 120);
            curl_setopt($ch, CURLOPT_USERAGENT, $agent);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);  //Windows 2003 Compatibility
            $result = curl_exec($ch);
            curl_close($ch);

            $result = urldecode($result);
            $result = trim($result);

            /*             * *****************************************************
              Possible reply from iPay88
              00 -- Successful payment
              Invalid parameters -- Parameters pass in incorrect
              Record not found  -- Cannot found the record
              Incorrect amount -- Amount different
              Payment fail -- Payment fail
             * ***************************************************** */

            if ($result == '00') {
                $this->check_trans_status_flag = true;
            }

            $ipay88_payment_data_array = array('ipay88_status' => ($result == '00' ? 1 : 0));
            tep_db_perform(TABLE_PAYMENT_IPAY88, $ipay88_payment_data_array, 'update', "ipay88_orders_id = '" . tep_db_input($order_id) . "'");

            $iPay88_payment_history_data_array = array('ipay88_orders_id' => $order_id,
                'ipay88_status_key' => $result,
                'ipay88_date' => 'now()',
                'ipay88_status' => ($result == '00' ? 1 : 0)
            );
            tep_db_perform(TABLE_PAYMENT_IPAY88_PAYMENT_STATUS_HISTORY, $iPay88_payment_history_data_array);
        }
    }

    function hex2ascii($hex) {
        $ascii = '';
        $hex = str_replace(' ', '', $hex);
        for ($i = 0; $i < strlen($hex); $i = $i + 2) {
            $ascii.= chr(hexdec(substr($hex, $i, 2)));
        }
        return($ascii);
    }

    function get_orders_payment_info_file() {
        return DIR_FS_CATALOG_MODULES . 'payment/iPay88/admin/orders.inc.php';
    }

    function get_orders_list_payment_info_file() {
        return DIR_FS_CATALOG_MODULES . 'payment/iPay88/admin/orders_list.inc.php';
    }

    function get_ipn_class_file() {
        return DIR_FS_CATALOG_MODULES . 'payment/iPay88/classes/iPay88_ipn_class.php';
    }

}

?>