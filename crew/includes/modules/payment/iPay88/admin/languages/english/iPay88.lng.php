<?php
/*
  	$Id: iPay88.lng.php, v1.0 2007/08/13 18:08:05
  	Author : <PERSON> (<EMAIL>)
  	Title: iPay88 Payment Module V1.0
	
	Revisions:
		Version 1.0 Initial Payment Module
	
  	Copyright (c) 2007 SKC Venture
	
  	Released under the GNU General Public License
*/

//begin ADMIN text
define('ENTRY_IPAY88_PAYMENT_METHOD', 'iPay88 Payment Method:');
define('ENTRY_IPAY88_TRANSACTION_ID', 'Transaction ID:');
define('ENTRY_IPAY88_AUTH_CODE', 'Bank\'s Approval Code:');
define('ENTRY_IPAY88_PAYMENT_CURRENCY', 'Currency:');
define('ENTRY_IPAY88_PAYMENT_AMOUNT', 'Amount:');
define('ENTRY_IPAY88_PAYMENT_STATUS', 'Payment Status:');

define('TEXT_STATUS_UNKNOWN', 'Refer Transaction History');

define('TABLE_HEADING_IPAY88_DATE', 'History Date');
define('TABLE_HEADING_IPAY88_STATUS', 'Status');

define('MODULE_PAYMENT_IPAY88_LNG_MERCHANT_CODE', 'Merchant Code');
define('MODULE_PAYMENT_IPAY88_LNG_MERCHANT_KEY', 'Merchant Key');
define('MODULE_PAYMENT_TAX', 'Tax');

?>