<?
/*
  	$Id: orders.inc.php,v 1.6 2009/02/03 03:25:21 boonhock Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2006 SKC Venture
  	
  	Released under the GNU General Public License
*/

require_once(DIR_FS_CATALOG_MODULES . 'payment/iPay88.php');
include_once(DIR_FS_CATALOG_MODULES . 'payment/iPay88/admin/languages/' . $language . '/iPay88.lng.php');

$ipay88 = new ipay88();

$ipay88_payment_status = array(	'0' => 'Failed',
								'1' => 'Success');

$ipay88_trans_info_select_sql = "SELECT * FROM " . TABLE_PAYMENT_IPAY88 . " WHERE ipay88_orders_id = '" . (int)$oID . "'";
$ipay88_trans_info_result_sql= tep_db_query($ipay88_trans_info_select_sql);

$ipay88_trans_history_select_sql = "SELECT ipay88_status_key, ipay88_date, ipay88_status 
									FROM " . TABLE_PAYMENT_IPAY88_PAYMENT_STATUS_HISTORY . "
									WHERE ipay88_orders_id = '" . (int)$oID  . "' 
									ORDER BY ipay88_date";
$ipay88_trans_history_result_sql = tep_db_query($ipay88_trans_history_select_sql);
?>
<table border="0" width="100%" cellspacing="0" cellpadding="2">
<? ob_start(); ?>
	<tr>
		<td class="main">
      		<table border="0" width="100%" cellspacing="0" cellpadding="2">
      			<tr>
      				<td class="main" width="12%" nowrap><b><?=ENTRY_PAYMENT_METHOD?></b></td>
      				<td class="main">
      					<div>
      						<? if((int)$order->info['payment_methods_id']>0) { ?><div class="<?=((int)$payment_info_array[$order->info['payment_methods_id']]->confirm_complete_days>0?'rp_icon':'nrp_icon')?>"><b><?=((int)$payment_info_array[$order->info['payment_methods_id']]->confirm_complete_days>0?'RP':'NRP')?></b></div> <? } ?>
      						<div style="width:30px; height:15px; background-color:<?=$payment_module_info['display_colour']?>; float:left; vertical-align:top; text-align:center;">&nbsp;</div>
      						<div style="vertical-align: top">
      							&nbsp;&nbsp;
<?
	if ($view_payment_details_permission) {
		if (tep_not_null($last_payment_method) && $last_payment_method['payment_methods_id'] != $order->info['payment_methods_id']) {
			echo '<span class="redIndicator">'.(tep_not_null($payment_module_info['payment_methods_parent_title'])?$payment_module_info['payment_methods_parent_title'] . ' - ' :'') . $payment_module_info['payment_methods_title'].'</span>';
		} else {
			echo (tep_not_null($payment_module_info['payment_methods_parent_title'])?$payment_module_info['payment_methods_parent_title'] . ' - ' :'') . $payment_module_info['payment_methods_title'];
		}
		
		if (tep_not_null($last_payment_method) && $last_payment_method['payment_methods_id'] != $order->info['payment_methods_id']) {
			echo '&nbsp;&nbsp;<span class="redIndicator">(Last Payment Method: ' . (tep_not_null($last_payment_method['parent_payment_methods_title'])? $last_payment_method['parent_payment_methods_title'] . ' - ' : '' ) . $last_payment_method['payment_methods_title'] . ')</span>';
		}
	} else {
		echo (tep_not_null($payment_module_info['payment_methods_parent_title'])?$payment_module_info['payment_methods_parent_title'] . ' - ' :'') . $payment_module_info['payment_methods_title'];
	}
?>
							</div>
      					</div>
      				</td>
				</tr>
			</table>
		</td>
	</tr>
<?
$payment_method_title_info = ob_get_contents();
ob_end_clean();

if (tep_db_num_rows($ipay88_trans_info_result_sql) || tep_db_num_rows($ipay88_trans_history_result_sql)) {
	$ipay88_trans_info_row = tep_db_fetch_array($ipay88_trans_info_result_sql);
	
	echo $payment_method_title_info;
	if (!$view_payment_details_permission) {
		;
	} else {
?>
  	<tr>
    	<td class="main">
      		<table border="0" width="100%" cellspacing="0" cellpadding="2">
      			<tr valign="top">
        			<td width="35%">
              			<table border="0" cellspacing="0" cellpadding="2">
              				<tr>
                				<td class="main" valign="top" nowrap><b><?=ENTRY_IPAY88_PAYMENT_METHOD?></b>&nbsp;</td>
                				<td class="main" nowrap><?=$ipay88->iPay88PMID[$ipay88_trans_info_row["ipay88_payment_method"]]?></td>
              				</tr>
              				<tr>
                				<td class="main" valign="top" nowrap><b><?=ENTRY_IPAY88_TRANSACTION_ID?></b>&nbsp;</td>
                				<td class="main"><?=$ipay88_trans_info_row["ipay88_trans_id"]?></td>
              				</tr>
              				<tr>
                				<td class="main" valign="top" nowrap><b><?=ENTRY_IPAY88_AUTH_CODE?></b>&nbsp;</td>
                				<td class="main" nowrap><?=$ipay88_trans_info_row["ipay88_bank_auth_code"]?></td>
              				</tr>
              				<tr>
                				<td class="main" valign="top" nowrap><b><?=ENTRY_IPAY88_PAYMENT_CURRENCY?></b>&nbsp;</td>
                				<td class="main" nowrap><?=$ipay88_trans_info_row["ipay88_currency"]?></td>
              				</tr>
              				<tr>
                				<td class="main" valign="top" nowrap><b><?=ENTRY_IPAY88_PAYMENT_AMOUNT?></b>&nbsp;</td>
                				<td class="main" nowrap><?=$ipay88_trans_info_row["ipay88_payment_amount"]?></td>
              				</tr>
              				<tr>
                				<td class="main" valign="top" nowrap><b><?=ENTRY_IPAY88_PAYMENT_STATUS?></b>&nbsp;</td>
                				<td class="main" nowrap>
	              				<?
                					echo isset($ipay88_payment_status[$ipay88_trans_info_row["ipay88_status"]]) ? $ipay88_payment_status[$ipay88_trans_info_row["ipay88_status"]] : TEXT_STATUS_UNKNOWN;
                					
                					if ($order->info['orders_status'] == '1' || $order->info['orders_status'] == '7' || $order->info['orders_status'] == '2') {
	                					echo "&nbsp;&nbsp;";
										echo tep_draw_form('ipay88_status_check_form', FILENAME_ORDERS, tep_get_all_get_params(), 'post');
										echo tep_draw_hidden_field('subaction', 'payment_action');
										echo tep_draw_hidden_field('payment_action', 'check_trans_status');
										echo tep_submit_button('Check Payment Status', 'Check Payment Status', 'name="iPay88CheckTransStatusBtn"', 'inputButton');
										echo "</form>";
									}
								?>
								</td>
							</tr>
              			</table>
        			</td>
        			<td>
              			<table border="0" cellspacing="0" cellpadding="2">
              				<tr>
                				<td class="main" colspan="2" nowrap>
                					<table border="1" cellspacing="0" cellpadding="2">
        								<tr>
                							<td class="smallText" nowrap><b><?=TABLE_HEADING_IPAY88_DATE?></b></td>
                							<td class="smallText" nowrap><b><?=TABLE_HEADING_IPAY88_STATUS?></b></td>
                						</tr>
<?		while ($ipay88_trans_history_row = tep_db_fetch_array($ipay88_trans_history_result_sql)) {
        	echo ' 						<tr>
                							<td class="smallText" nowrap>'.$ipay88_trans_history_row['ipay88_date'].'</td>
                							<td class="smallText" nowrap>'.($ipay88_trans_history_row['ipay88_status_key'] == '00' ? 'Successful payment' : (tep_not_null($ipay88_trans_history_row['ipay88_status_key']) ? $ipay88_trans_history_row['ipay88_status_key'] : 'Unknown')).'</td>
                						</tr>';
     	}
?>
                					</table>
                				</td>
                			</tr>
              			</table>
              		</td>
      			</tr>
      		</table>
   		</td>
  	</tr>
<?
	}
} else {
	echo $payment_method_title_info;
?>	
	<tr>
		<td class="main" nowrap>
		<?
			if ($order->info['orders_status'] == '1' || $order->info['orders_status'] == '7' || $order->info['orders_status'] == '2') {
				echo "&nbsp;&nbsp;";
				echo tep_draw_form('ipay88_status_check_form', FILENAME_ORDERS, tep_get_all_get_params(), 'post');
				echo tep_draw_hidden_field('subaction', 'payment_action');
				echo tep_draw_hidden_field('payment_action', 'check_trans_status');
				echo tep_submit_button('Check Payment Status', 'Check Payment Status', 'name="iPay88CheckTransStatusBtn"', 'inputButton');
				echo "</form>";
			}
		?>
		</td>
	</tr>
<?
}
?>
</table>