<?
/*
  	$Id: orders_list.inc.php,v 1.2 2007/08/15 13:09:28 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2007 SKC Venture
  	
  	Released under the GNU General Public License
*/

require_once(DIR_FS_CATALOG_MODULES . 'payment/iPay88.php');
include_once(DIR_FS_CATALOG_MODULES . 'payment/iPay88/admin/languages/' . $language . '/iPay88.lng.php');

$ipay88 = new ipay88();

$ipay88_payment_status = array(	'0' => 'Failed',
								'1' => 'Success');

$ipay88_trans_info_select_sql = "SELECT * FROM " . TABLE_PAYMENT_IPAY88 . " WHERE ipay88_orders_id = '" . $order_obj->orders_id . "'";
$ipay88_trans_info_result_sql= tep_db_query($ipay88_trans_info_select_sql);
?>
<table border="0" width="100%" cellspacing="0" cellpadding="2">
	<tr>
		<td class="subInvoiceBoxHeading">
			<div style="border-bottom: 1px solid #996600; border-top: 1px solid #996600; padding: 2px;">Payment Details</div>
		</td>
	</tr>
<?
if ($ipay88_trans_info_row = tep_db_fetch_array($ipay88_trans_info_result_sql)) {
?>
  	<tr>
    	<td class="main">
      		<table border="0" width="100%" cellspacing="0" cellpadding="2">
      			<tr valign="top">
        			<td width="100%">
              			<table border="0" cellspacing="0" cellpadding="2">
              				<tr>
                				<td class="invoiceRecords" valign="top" nowrap><?=ENTRY_IPAY88_PAYMENT_METHOD?></td>
                				<td class="invoiceRecords" valign="top">:&nbsp;</td>
                				<td class="invoiceRecords" nowrap><?=$ipay88->iPay88PMID[$ipay88_trans_info_row["ipay88_payment_method"]]?></td>
              				</tr>
              				<tr>
                				<td class="invoiceRecords" valign="top" nowrap><?=ENTRY_IPAY88_TRANSACTION_ID?></td>
                				<td class="invoiceRecords" valign="top">:&nbsp;</td>
                				<td class="invoiceRecords" nowrap><?=$ipay88_trans_info_row["ipay88_trans_id"]?></td>
              				</tr>
              				<tr>
                				<td class="invoiceRecords" valign="top" nowrap><?=ENTRY_IPAY88_AUTH_CODE?></td>
                				<td class="invoiceRecords" valign="top">:&nbsp;</td>
                				<td class="invoiceRecords"><?=$ipay88_trans_info_row["ipay88_bank_auth_code"]?></td>
              				</tr>
              				<tr>
                				<td class="invoiceRecords" valign="top" nowrap><?=ENTRY_IPAY88_PAYMENT_CURRENCY?></td>
                				<td class="invoiceRecords" valign="top">:&nbsp;</td>
                				<td class="invoiceRecords" nowrap><?=$ipay88_trans_info_row["ipay88_currency"]?></td>
              				</tr>
              				<tr>
                				<td class="invoiceRecords" valign="top" nowrap><?=ENTRY_IPAY88_PAYMENT_AMOUNT?></td>
                				<td class="invoiceRecords" valign="top">:&nbsp;</td>
                				<td class="invoiceRecords" nowrap><?=$ipay88_trans_info_row["ipay88_payment_amount"]?></td>
              				</tr>
              				<tr>
                				<td class="invoiceRecords" valign="top" nowrap><?=ENTRY_IPAY88_PAYMENT_STATUS?></td>
                				<td class="invoiceRecords" valign="top">:&nbsp;</td>
                				<td class="invoiceRecords" nowrap><?=isset($ipay88_payment_status[$ipay88_trans_info_row["ipay88_status"]]) ? $ipay88_payment_status[$ipay88_trans_info_row["ipay88_status"]] : TEXT_STATUS_UNKNOWN;?></td>
              				</tr>
              			</table>
        			</td>
      			</tr>
      		</table>
   		</td>
  	</tr>
<?
} else {
?>
	<tr>
		<td class="invoiceRecords">No further payment information is available.</td>
	</tr>
<?
}
?>
</table>