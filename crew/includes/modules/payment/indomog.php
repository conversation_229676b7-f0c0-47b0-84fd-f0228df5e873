<?php

/*
  $Id: indomog.php, v1.0 2005/11/29 18:08:05
  Author : <PERSON> (<EMAIL>)
  Title: indomog Payment Module V1.0

  Revisions:
  Version 1.0 Initial Payment Module

  Copyright (c) 2005 SKC Venture

  Released under the GNU General Public License

  $order->info['total'] : Always has the value in DEFAULT_CURRENCY
 */

class indomog {

    var $code, $title, $description, $enabled;

    // class constructor
    function indomog($pm_id = 0) {
        global $order, $languages_id, $default_languages_id, $defCurr;

        //Basic info
        $this->payment_methods_id = 0;
        $this->payment_methods_parent_id = 0;

        $this->code = 'indomog';
        $this->title = $this->code;

        $this->preferred = true;
        $this->filename = 'indomog.php';
        $this->check_trans_status_flag = false;

        $payment_methods_select_sql = "	SELECT payment_methods_parent_id, payment_methods_code, 
                                                payment_methods_types_id, 
                                                payment_methods_receive_status_mode, payment_methods_id, 
                                                payment_methods_title, payment_methods_sort_order, 
                                                payment_methods_receive_status, payment_methods_legend_color, 
                                                payment_methods_logo 
                                        FROM " . TABLE_PAYMENT_METHODS . " 
                                        WHERE 1 ";
        if ((int) $pm_id > 0) {
            $payment_methods_select_sql .= "	AND payment_methods_id = '" . (int) $pm_id . "'";
        } else {
            $payment_methods_select_sql .= "	AND payment_methods_filename = '" . tep_db_input($this->filename) . "'";
        }

        $payment_methods_result_sql = tep_db_query($payment_methods_select_sql);
        if ($payment_methods_row = tep_db_fetch_array($payment_methods_result_sql)) {
            $pm_desc_title_select_sql = "	SELECT payment_methods_description_title 
                                                FROM " . TABLE_PAYMENT_METHODS_DESCRIPTION . " 
                                                WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "' 
                                                        AND (IF (languages_id = '" . tep_db_input($languages_id) . "', 1, IF ((	SELECT COUNT(payment_methods_id) > 0 
                                                                                                                                FROM " . TABLE_PAYMENT_METHODS_DESCRIPTION . " 
                                                                                                                                WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "' 
                                                                                                                                        AND languages_id = '" . (int) $languages_id . "'), 0, languages_id = 1)))";
            $pm_desc_title_result_sql = tep_db_query($pm_desc_title_select_sql);
            $pm_desc_title_row = tep_db_fetch_array($pm_desc_title_result_sql);

            $this->payment_methods_id = (int) $payment_methods_row['payment_methods_id'];
            $this->payment_methods_parent_id = (int) $payment_methods_row['payment_methods_parent_id'];
            $this->code = $payment_methods_row['payment_methods_code'];
            $this->title = $payment_methods_row['payment_methods_title'];
            $this->display_title = $pm_desc_title_row['payment_methods_description_title'];  // if title to be display for payment method is different from payment method value
            $this->sort_order = $payment_methods_row['payment_methods_sort_order'];
            $this->enabled = $payment_methods_row['payment_methods_receive_status_mode'];
            $this->legend_display_colour = $payment_methods_row['payment_methods_legend_color'];
            $this->receive_status = $payment_methods_row['payment_methods_receive_status'];
            $this->description = $this->display_title;
            $this->indomogCurrencies = $this->get_support_currencies($this->payment_methods_id);
            $this->defCurr = DEFAULT_CURRENCY;

            $configuration_setting_array = $this->load_pm_setting();
            $this->mode = $configuration_setting_array['MODULE_PAYMENT_INDOMOG_MODE'];
            $this->order_status_id = $configuration_setting_array['MODULE_PAYMENT_INDOMOG_ORDER_STATUS_ID'];
            $this->order_processing_status = $this->processing_status_id = $configuration_setting_array['MODULE_PAYMENT_INDOMOG_PROCESSING_STATUS_ID'];
            $this->message = $configuration_setting_array['MODULE_PAYMENT_INDOMOG_MESSAGE'];
            $this->email_message = $configuration_setting_array['MODULE_PAYMENT_INDOMOG_EMAIL_MESSAGE'];
            $this->confirm_complete_days = $configuration_setting_array['MODULE_PAYMENT_INDOMOG_CONFIRM_COMPLETE'];
            $this->require_address_information = $configuration_setting_array['MODULE_PAYMENT_INDOMOG_MANDATORY_ADDRESS_FIELD'];
            $this->require_ic_information = $configuration_setting_array['MODULE_PAYMENT_INDOMOG_MANDATORY_IC_FIELD'];
            $this->require_contact_information = $configuration_setting_array['MODULE_PAYMENT_INDOMOG_MANDATORY_CONTACT_FIELD'];
            $this->customer_payment_info = $configuration_setting_array['MODULE_PAYMENT_INDOMOG_CUSTOMER_PAYMENT_INFO'];

            if ((int) $this->order_status_id > 0) {
                $this->order_status = $this->order_status_id;
            }

            $this->email_footer = str_replace(array("<br>", "<BR>", "\r\n"), array("\n", "\n", ''), $this->email_message);
            $this->order_processing_status = (int) $this->order_processing_status > 0 ? $this->order_processing_status : 7;
        }

        $this->force_to_checkoutprocess = true;
        $this->has_ipn = true;
        if ($this->mode == 'Test') {
            $this->form_action_url = 'http://dev.indomog.com/indomog2/old_core/index.php/paywall/main';
            $this->payment_checking_url = 'http://dev.indomog.com/h2h_paywall/index.php';
        } else {
            $this->form_action_url = 'https://www.indomog.com/indomog2/new_core/index.php/paywall/main';
            $this->payment_checking_url = 'https://mogpay.indomog.com/merchant/h2h_paywall/index.php';
        }

        $this->auto_cancel_period = 60; // In minutes
    }

    function javascript_validation() {
        return false;
    }

    function selection() {
        global $languages_id, $default_languages_id;

        $selection_array = array();

        $payment_methods_select_sql = "	SELECT payment_methods_id, payment_methods_code, payment_methods_types_id, payment_methods_logo, payment_methods_receive_status_mode 
                                        FROM " . TABLE_PAYMENT_METHODS . " 
                                        WHERE payment_methods_receive_status = 1 
                                                AND payment_methods_receive_status_mode <> 0 
                                                AND payment_methods_parent_id = '" . (int) $this->payment_methods_id . "' 
                                        ORDER BY payment_methods_sort_order";
        $payment_methods_result_sql = tep_db_query($payment_methods_select_sql);
        while ($payment_methods_row = tep_db_fetch_array($payment_methods_result_sql)) {
            $pm_desc_title_select_sql = "	SELECT payment_methods_description_title 
                                                FROM " . TABLE_PAYMENT_METHODS_DESCRIPTION . " 
                                                WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "' 
                                                        AND (IF (languages_id = '" . tep_db_input($languages_id) . "', 1, IF ((	SELECT COUNT(payment_methods_id) > 0 
																														FROM " . TABLE_PAYMENT_METHODS_DESCRIPTION . " 
																														WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "' 
																															AND languages_id = '" . (int) $languages_id . "'), 0, languages_id = 1)))";
            $pm_desc_title_result_sql = tep_db_query($pm_desc_title_select_sql);
            $pm_desc_title_row = tep_db_fetch_array($pm_desc_title_result_sql);

            $pm_array = $this->load_pm_setting(1, $payment_methods_row['payment_methods_id']);

            $selection_array[] = array('payment_gateway_code' => $this->code,
                'payment_methods_receive_status_mode' => $payment_methods_row['payment_methods_receive_status_mode'],
                'payment_methods_id' => $payment_methods_row['payment_methods_id'],
                'payment_methods_code' => $payment_methods_row['payment_methods_code'],
                'payment_methods_types_id' => $payment_methods_row['payment_methods_types_id'],
                'payment_methods_parent_id' => $this->payment_methods_id,
                'payment_methods_logo' => $payment_methods_row['payment_methods_logo'],
                'payment_methods_description_title' => $pm_desc_title_row['payment_methods_description_title'],
                'currency' => $this->get_support_currencies($payment_methods_row['payment_methods_id']),
                'show_billing_address' => $pm_array['MODULE_PAYMENT_INDOMOG_MANDATORY_ADDRESS_FIELD'],
                'confirm_complete_days' => $pm_array['MODULE_PAYMENT_INDOMOG_CONFIRM_COMPLETE'],
                'show_contact_number' => $pm_array['MODULE_PAYMENT_INDOMOG_MANDATORY_CONTACT_FIELD'],
                'show_ic' => $pm_array['MODULE_PAYMENT_INDOMOG_MANDATORY_IC_FIELD']
            );

            if ($payment_methods_row['payment_methods_receive_status_mode'] == '-1') {
                $pm_status_message_select = "	SELECT payment_methods_status_message 
                                                FROM " . TABLE_PAYMENT_METHODS_STATUS_DESCRIPTION . " 
                                                WHERE payment_methods_mode = 'RECEIVE' 
                                                        AND payment_methods_status = '-1' 
                                                        AND languages_id = '" . (int) $languages_id . "' 
                                                        AND payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "'";
                $pm_status_message_result = tep_db_query($pm_status_message_select);
                $pm_status_message_row = tep_db_fetch_array($pm_status_message_result);
                $selection_array[sizeof($selection_array) - 1]['payment_methods_status_message'] = $pm_status_message_row['payment_methods_status_message'];
            }
        }

        return $selection_array;
    }

    function set_support_currencies($currency_array) {
        $this->indomogCurrencies = $currency_array;
    }

    function get_support_currencies($pm_id, $by_zone = true) {
        global $zone_info_array;

        $default_currency = '';
        $support_currencies_array = array();

        $currency_code_select_sql = "	SELECT currency_code, payment_methods_instance_default 
                                        FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " 
                                        WHERE payment_methods_id = '" . (int) $pm_id . "' 
                                        ORDER BY currency_code";
        $currency_code_result_sql = tep_db_query($currency_code_select_sql);

        if (tep_db_num_rows($currency_code_result_sql) > 0) {
            while ($currency_code_row = tep_db_fetch_array($currency_code_result_sql)) {
                $support_currencies_array[] = $currency_code_row['currency_code'];

                if ($currency_code_row['payment_methods_instance_default'] == 1) {
                    $default_currency = $currency_code_row['currency_code'];
                }
            }
        } else {
            if ($this->payment_methods_parent_id > 0) {
                $payment_methods_parent_id = $this->payment_methods_parent_id;
            } else {
                $payment_methods_parent_id = $this->payment_methods_id;
            }

            $parent_currency_code_select_sql = "	SELECT currency_code, payment_methods_instance_default 
                                                        FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " 
                                                        WHERE payment_methods_id = '" . (int) $payment_methods_parent_id . "' 
                                                        ORDER BY currency_code";
            $parent_currency_code_result_sql = tep_db_query($parent_currency_code_select_sql);
            while ($parent_currency_code_row = tep_db_fetch_array($parent_currency_code_result_sql)) {
                $support_currencies_array[] = $parent_currency_code_row['currency_code'];

                if ($parent_currency_code_row['payment_methods_instance_default'] == 1) {
                    $default_currency = $parent_currency_code_row['currency_code'];
                }
            }
        }

        if (tep_not_null($default_currency)) {
            $this->defCurr = $default_currency;
        }

        if ($by_zone) {
            if (isset($zone_info_array)) {
                $support_currencies_array = array_intersect($zone_info_array[3]->zone_currency_id, $support_currencies_array);
                if (count($support_currencies_array)) {
                    array_multisort($support_currencies_array);
                } else {
                    $support_currencies_array = array($default_currency);
                }
            } else {
                $support_currencies_array = array($default_currency);
            }
        } else {
            $this->set_support_currencies($support_currencies_array);
            // All found currencies is supported. Normally this is used for backend script such as IPN call
        }

        return $support_currencies_array;
    }

    function get_require_address_information() {
        return $this->require_address_information;
    }

    function get_confirm_complete_days() {
        return $this->confirm_complete_days;
    }

    function get_pm_info() {
        return $this->message;
    }

    function get_email_pm_info() {
        return $this->email_message;
    }

    function draw_confirm_button() {
        return tep_image_button2('red', 'javascript:document.checkout_confirmation.submit()', MODULE_PAYMENT_INDOMOG_TEXT_CART, 200);
    }

    function get_confirm_button_caption() {
        return MODULE_PAYMENT_INDOMOG_TEXT_TITLE;
    }

    function pre_confirmation_check() {
        global $currency;

        if (in_array($currency, $this->indomogCurrencies)) {
            
        }

        return false;
    }

    function process_button() {
        global $currencies, $currency, $order;
        global $order_logged;

        if ($this->force_to_checkoutprocess && !tep_session_is_registered('order_logged'))
            return;

        $post_signature = '';
        $process_button_string = '';
        $indomogCurrency = $currency;

        if (!in_array($indomogCurrency, $this->indomogCurrencies)) {
            $indomogCurrency = in_array(DEFAULT_CURRENCY, $this->indomogCurrencies) ? DEFAULT_CURRENCY : $this->indomogCurrencies[0];
        }

        $this->get_merchant_account($indomogCurrency);
        $OrderAmt = number_format($order->info['total'] * $currencies->get_value($indomogCurrency), $currencies->get_decimal_places($indomogCurrency), '.', '');

        $hidden_field_array = array(
            'RMID' => $this->merchant_id,
            'QID' => $order_logged,
            'RC' => '4200',
            'Alg' => $this->alg,
            'AlgID' => $_SESSION['customer_id'],
            'Name' => $order->billing['firstname'] . ' ' . $order->billing['lastname'],
            'EmailHP' => $order->customer['email_address'],
            'IPD' => ' ', //tep_get_ip_address(),
            'Now' => date('Y-m-d H:i:s'),
            'QUrlBackground' => ' ', //background-color
            'QUrlImage' => ' ',
            'QUrlLogo' => DIR_WS_IMAGES . 'logo_pg.png',
            'QUrlSuccess' => tep_href_link(FILENAME_CHECKOUT_PROCESS, '', 'SSL'),
            'QUrlPending' => tep_href_link(FILENAME_CHECKOUT_PROCESS, '', 'SSL'),
            'QUrlCancel' => tep_href_link(FILENAME_CHECKOUT_PAYMENT, '', 'SSL'),
            'PurchaseAmt' => $OrderAmt,
            'PurchaseDesc' => 'Purchase from ' . STORE_NAME,
            'BMod' => $this->code
        );

        foreach ($hidden_field_array as $field => $value) {
            $post_signature .= $value;
            $process_button_string .= tep_draw_hidden_field($field, $value, '', true);
        }

        $process_button_string .= tep_draw_hidden_field('Signature', sha1($post_signature . $this->secret_word));

        return $process_button_string;
    }

    function before_process() {
        global $order_logged, $_POST, $currencies, $currency, $order;

        if (!tep_session_is_registered('order_logged')) {
            return;
        } else {
            $api_array = $this->call_api_verifying($order_logged);

            if (isset($api_array['data']['Response']['Data']['RspCode']) && in_array($api_array['data']['Response']['Data']['RspCode'], array('000', '001'))) {
                // success or pending success, do nothing
            } else {
                if (isset($api_array['data']['Response']['Data']['RspCode'])) {
                    $error_message = $api_array['data']['Response']['Data']['RspCode'] . ':' . $api_array['data']['Response']['Data']['RspDesc'];
                } else {
                    $error_message = 'UNKNOWN';
                }

                tep_redirect(tep_href_link(FILENAME_CHECKOUT_PAYMENT, 'error_message=' . urlencode($error_message), 'SSL', true, false));
            }
        }
    }

    function after_process() {
        return false;
    }

    function link_to_payment($new_order_id) {
        global $order_logged;

        if (tep_session_is_registered('order_logged'))
            tep_session_unregister('order_logged');

        tep_session_register('order_logged');
        $order_logged = $new_order_id;
        require(DIR_WS_INCLUDES . 'modules/payment/indomog/catalog/indomog_splash.php');
        return;
    }

    function call_api_verifying($order_id) {
        $debug_html = '';
        $error_code = true;
        $resp_array = array();

        $order_info_select_sql = "	SELECT o.payment_methods_id, o.currency, o.currency_value, o.customers_id, ot.value 
                                    FROM " . TABLE_ORDERS . " AS o 
                                    INNER JOIN " . TABLE_ORDERS_TOTAL . " AS ot 
                                            ON o.orders_id=ot.orders_id AND ot.class='ot_total' 
                                    WHERE o.orders_id = '" . tep_db_input($order_id) . "'";
        $order_info_result_sql = tep_db_query($order_info_select_sql);
        if ($order_info_row = tep_db_fetch_array($order_info_result_sql)) {
            if ($order_info_row['payment_methods_id'] != $this->payment_methods_id) {
                $error_code = 'PAYMENT_METHOD_MISMATCH';
            } else {
                $post_signature = '';
                $request_array = array();

                $customerID = $order_info_row['customers_id'];
                $this->get_merchant_account($order_info_row['currency']);

                // build URL to retrieve transaction result
                $form_data = array(
                    'RMID' => $this->merchant_id,
                    'QID' => $order_id,
                    'RC' => '4109',
                    'Alg' => $this->alg,
                    'AlgID' => $customerID,
                    'IPD' => ' ',
                    'Now' => gmdate('Y-m-d H:i:s', strtotime(" + 7 hours"))
                );

                foreach ($form_data as $field => $value) {
                    $post_signature .= $value;
                }

                $request_array = array(
                    'Request' => array('Data' => $form_data),
                    'Signature' => sha1($post_signature . $this->secret_word)
                );

                $json_data = json_encode($request_array);

                $ch = curl_init();
                curl_setopt($ch, CURLOPT_URL, $this->payment_checking_url);
                curl_setopt($ch, CURLOPT_VERBOSE, 1);
                curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 2);
                curl_setopt($ch, CURLOPT_POST, 1);
                curl_setopt($ch, CURLOPT_POSTFIELDS, $json_data);
                curl_setopt($ch, CURLOPT_TIMEOUT, 120);
                curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.0)');
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
                curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);  //Windows 2003 Compatibility
                curl_setopt($ch, CURLOPT_HTTPHEADER, array(
                    'Content-Type: application/json',
                    'Content-Length: ' . strlen($json_data))
                );

                // remove in staging/live
                //curl_setopt($ch, CURLOPT_PROXY, 'my-proxy.offgamers.lan:3128');
                // remove in staging/live

                $result = curl_exec($ch);

                ob_start();
                echo "========================REQUEST==========================<BR>";
                print_r($request_array);
                echo "========================================================<BR>";
                echo "========================RESPONSE=========================<BR>";
                print_r($result);
                echo "========================================================<BR>";
                if ($result === false) {
                    print_r(array(
                        'error_code' => curl_errno($ch),
                        'error_message' => curl_error($ch)
                    ));
                }
                $debug_html = ob_get_contents();
                ob_end_clean();
                curl_close($ch);

                if ($result !== false && tep_not_empty($result)) {
                    $resp_array = json_decode($result, true);

                    if (isset($resp_array['Response']['Data']['RspCode'])) {
                        $resp_signature = '';

                        foreach ($resp_array['Response']['Data'] as $value) {
                            $resp_signature .= $value;
                        }

                        $resp_array['Calculated_Signature'] = sha1($resp_signature . $this->secret_word);

                        if ($resp_array['Signature'] === $resp_array['Calculated_Signature']) {
                            $error_code = false;
                        } else {
                            $error_code = 'SIGNATURE_MISMATCH';
                        }
                    } else {
                        $error_code = 'UNKNOWN';
                    }
                } else {
                    $error_code = 'NO_RESPONSE';
                }
            }
        }

        return array('error' => $error_code, 'data' => $resp_array);
    }

    function check_trans_status($order_id) {
        global $currencies;

        $return_bool = false;
        $this->check_trans_status_flag = false;

        $path_info_array = pathinfo($_SERVER['SCRIPT_FILENAME']);
        $api_array = $this->call_api_verifying($order_id);

        if ($api_array['error'] === false) {
            $resp_array = $api_array['data'];
            $resp_data_array = $resp_array['Response']['Data'];

            if ($resp_data_array['RspCode'] == '000') {
                $this->check_trans_status_flag = true;

                $payment_history_data_array = array(
                    'indomog_orders_id' => $order_id,
                    'indomog_response_code' => $resp_data_array['RspCode'],
                    'indomog_response_description' => $resp_data_array['RspDesc'],
                    'indomog_status_datetime' => 'now()',
                    'indomog_changed_by' => (isset($path_info_array["basename"]) && $path_info_array["basename"] == 'cron_order_payment.php' ? 'cronjob' : (isset($_SESSION['login_email_address']) ? $_SESSION['login_email_address'] : 'system' ))
                );
                tep_db_perform(TABLE_INDOMOG_STATUS_HISTORY, $payment_history_data_array);

                $pg_data_array = array(
                    'indomog_orders_id' => $order_id,
                    'indomog_merchant_id' => tep_db_prepare_input($resp_data_array['RMID']),
                    'indomog_transaction_id' => tep_db_prepare_input($resp_data_array['TrxID']),
                    'indomog_bank_id' => tep_db_prepare_input($resp_data_array['BID']),
                    'indomog_transaction_status' => 'Success', //tep_db_prepare_input($resp_data_array['TrxStatus']),
                    'indomog_transaction_date' => tep_db_prepare_input($resp_data_array['TrxTime']),
                    'indomog_amount' => (double) tep_db_prepare_input($resp_data_array['TrxValue']),
                    'indomog_signature' => tep_db_prepare_input($resp_array['Signature']),
                    'indomog_certificate' => tep_db_prepare_input($resp_array['Response']['Certificate'])
                );
                tep_db_perform(TABLE_INDOMOG, $pg_data_array, 'update', "indomog_orders_id = '" . tep_db_input($order_id) . "'");

                $return_bool = true;
            } else {
                // save request for not fully success payment
                $payment_history_data_array = array(
                    'indomog_orders_id' => $order_id,
                    'indomog_response_code' => $resp_data_array['RspCode'],
                    'indomog_response_description' => 'Error: ' . $resp_data_array['RspDesc'],
                    'indomog_status_datetime' => 'now()',
                    'indomog_changed_by' => (isset($path_info_array["basename"]) && $path_info_array["basename"] == 'cron_order_payment.php' ? 'cronjob' : (isset($_SESSION['login_email_address']) ? $_SESSION['login_email_address'] : 'system' ))
                );
                tep_db_perform(TABLE_INDOMOG_STATUS_HISTORY, $payment_history_data_array);
            }
        } else {
            switch ($api_array['error']) {
                case 'PAYMENT_METHOD_MISMATCH':
                    // return false;
                    break;
                case 'SIGNATURE_MISMATCH':
                    $payment_history_data_array = array(
                        'indomog_orders_id' => $order_id,
                        'indomog_response_description' => 'Error: Check Status Signature not match ' . $api_array['data']['Signature'] . ' != ' . $api_array['data']['Calculated_Signature'],
                        'indomog_status_datetime' => 'now()',
                        'indomog_changed_by' => (isset($path_info_array["basename"]) && $path_info_array["basename"] == 'cron_order_payment.php' ? 'cronjob' : (isset($_SESSION['login_email_address']) ? $_SESSION['login_email_address'] : 'system' ))
                    );
                    tep_db_perform(TABLE_INDOMOG_STATUS_HISTORY, $payment_history_data_array);
                    break;
                case 'NO_RESPONSE':
                    $payment_history_data_array = array(
                        'indomog_orders_id' => $order_id,
                        'indomog_response_description' => 'Error: NO RESPONSE',
                        'indomog_status_datetime' => 'now()',
                        'indomog_changed_by' => (isset($path_info_array["basename"]) && $path_info_array["basename"] == 'cron_order_payment.php' ? 'cronjob' : (isset($_SESSION['login_email_address']) ? $_SESSION['login_email_address'] : 'system' ))
                    );
                    tep_db_perform(TABLE_INDOMOG_STATUS_HISTORY, $payment_history_data_array);
                    break;
                default:
                    $payment_history_data_array = array(
                        'indomog_orders_id' => $order_id,
                        'indomog_response_description' => 'Error: UNKNOWN',
                        'indomog_status_datetime' => 'now()',
                        'indomog_changed_by' => (isset($path_info_array["basename"]) && $path_info_array["basename"] == 'cron_order_payment.php' ? 'cronjob' : (isset($_SESSION['login_email_address']) ? $_SESSION['login_email_address'] : 'system' ))
                    );
                    tep_db_perform(TABLE_INDOMOG_STATUS_HISTORY, $payment_history_data_array);
                    break;
            }
        }

        return $return_bool;
    }

    function is_supported_currency($selected_currency) {
        return (in_array($selected_currency, $this->indomogCurrencies) ? true : false);
    }

    function output_error() {
        return false;
    }

    function check() {
        if (!isset($this->_check)) {
            $payment_methods_select_sql = "	SELECT payment_methods_id
                                            FROM " . TABLE_PAYMENT_METHODS . " as pm
                                            WHERE pm.payment_methods_id = '" . (int) $this->payment_methods_id . "'";
            $payment_methods_result_sql = tep_db_query($payment_methods_select_sql);
            $this->_check = tep_db_num_rows($payment_methods_result_sql);
        }
        return $this->_check;
    }

    function load_pm_setting($language_id = 1, $pm_id = '') {
        $pm_setting_array = array();
        //load value from DB

        if (tep_not_null($pm_id)) {
            $payment_method_id = $pm_id;

            $payment_methods_parent_id_select_sql = "	SELECT payment_methods_parent_id 
                                                        FROM " . TABLE_PAYMENT_METHODS . " 
                                                        WHERE payment_methods_id = '" . (int) $pm_id . "'";
            $payment_methods_parent_id_result_sql = tep_db_query($payment_methods_parent_id_select_sql);
            $payment_methods_parent_id_row = tep_db_fetch_array($payment_methods_parent_id_result_sql);

            $payment_gateway_id = $payment_methods_parent_id_row['payment_methods_parent_id'];
        } else {
            $payment_method_id = $this->payment_methods_id;
            $payment_gateway_id = $this->payment_methods_parent_id;
        }

        $payment_configuration_info_select_sql = "	SELECT pci.payment_configuration_info_key, pcid.payment_configuration_info_value
                                                        FROM " . TABLE_PAYMENT_CONFIGURATION_INFO . " as pci
                                                        LEFT JOIN " . TABLE_PAYMENT_CONFIGURATION_INFO_DESCRIPTION . " as pcid 
                                                                ON pci.payment_configuration_info_id = pcid.payment_configuration_info_id
                                                                        AND pcid.languages_id = '" . (int) $language_id . "'
                                                        WHERE pci.payment_methods_id = '" . (int) $payment_method_id . "'
                                                        ORDER BY pci.payment_configuration_info_sort_order";
        $payment_configuration_info_result_sql = tep_db_query($payment_configuration_info_select_sql);
        if (tep_db_num_rows($payment_configuration_info_result_sql)) {
            while ($payment_configuration_info_row = tep_db_fetch_array($payment_configuration_info_result_sql)) {
                $pm_setting_array[$payment_configuration_info_row['payment_configuration_info_key']] = $payment_configuration_info_row['payment_configuration_info_value'];
            }
        } else if ((int) $payment_gateway_id > 0) { //load value from PG
            $payment_configuration_info_select_sql = "	SELECT pci.payment_configuration_info_key, pcid.payment_configuration_info_value
                                                        FROM " . TABLE_PAYMENT_CONFIGURATION_INFO . " as pci
                                                        LEFT JOIN " . TABLE_PAYMENT_CONFIGURATION_INFO_DESCRIPTION . " as pcid 
                                                                ON pci.payment_configuration_info_id = pcid.payment_configuration_info_id
                                                                        AND pcid.languages_id = '" . (int) $language_id . "'
                                                        WHERE pci.payment_methods_id = '" . (int) $payment_gateway_id . "'
                                                        ORDER BY pci.payment_configuration_info_sort_order";
            $payment_configuration_info_result_sql = tep_db_query($payment_configuration_info_select_sql);
            while ($payment_configuration_info_row = tep_db_fetch_array($payment_configuration_info_result_sql)) {
                $pm_setting_array[$payment_configuration_info_row['payment_configuration_info_key']] = $payment_configuration_info_row['payment_configuration_info_value'];
            }
        }

        return $pm_setting_array;
    }

    function get_merchant_account($selected_currency) {
        $payment_methods_instance_id = 0;
        $payment_gateway_instance_select_sql = "SELECT pmi.payment_methods_instance_id, pmi.payment_methods_instance_follow_default, pmi.currency_code 
                                                FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " as pmi
                                                WHERE pmi.payment_methods_id = '" . (int) $this->payment_methods_id . "'
                                                LIMIT 1";
        $payment_gateway_instance_result_sql = tep_db_query($payment_gateway_instance_select_sql);
        if (tep_db_num_rows($payment_gateway_instance_result_sql)) {
            $payment_gateway_instance_select_sql = "SELECT pmi.payment_methods_instance_id, pmi.payment_methods_instance_follow_default 
                                                    FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " as pmi
                                                    WHERE pmi.currency_code = '" . tep_db_input($selected_currency) . "' 
                                                            AND pmi.payment_methods_id = '" . (int) $this->payment_methods_id . "'";
            $payment_gateway_instance_result_sql = tep_db_query($payment_gateway_instance_select_sql);
            if ($payment_gateway_instance_row = tep_db_fetch_array($payment_gateway_instance_result_sql)) {
                if ((int) $payment_gateway_instance_row['payment_methods_instance_follow_default'] > 0) { // Follow Default Currency Setting
                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_follow_default'];
                } else { // Has own setting
                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_id'];
                }
            }
        } else {
            $payment_gateway_instance_select_sql = "SELECT pmi.payment_methods_instance_id, pmi.payment_methods_instance_follow_default 
                                                    FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " as pmi
                                                    INNER JOIN " . TABLE_PAYMENT_METHODS . " as pm
                                                            ON pm.payment_methods_parent_id = pmi.payment_methods_id
                                                    WHERE pmi.currency_code = '" . tep_db_input($selected_currency) . "' 
                                                            AND pm.payment_methods_id = '" . (int) $this->payment_methods_id . "'";
            $payment_gateway_instance_result_sql = tep_db_query($payment_gateway_instance_select_sql);
            if ($payment_gateway_instance_row = tep_db_fetch_array($payment_gateway_instance_result_sql)) {
                if ((int) $payment_gateway_instance_row['payment_methods_instance_follow_default'] > 0) {
                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_follow_default'];
                } else {
                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_id'];
                }
            }
        }
        $payment_gateway_instance_setting_select_sql = "SELECT pmis.payment_methods_instance_setting_key, pmis.payment_methods_instance_setting_value 
                                                        FROM " . TABLE_PAYMENT_METHODS_INSTANCE_SETTING . " as pmis
                                                        WHERE pmis.payment_methods_instance_id = '" . (int) $payment_methods_instance_id . "'";
        $payment_gateway_instance_setting_result_sql = tep_db_query($payment_gateway_instance_setting_select_sql);
        while ($payment_gateway_instance_setting_row = tep_db_fetch_array($payment_gateway_instance_setting_result_sql)) {
            switch ($payment_gateway_instance_setting_row['payment_methods_instance_setting_key']) {
                case 'MODULE_PAYMENT_INDOMOG_ID':
                    $this->merchant_id = $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];
                    break;
                case 'MODULE_PAYMENT_INDOMOG_SECRET_WORD':
                    $this->secret_word = $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];
                    break;
                case 'MODULE_PAYMENT_INDOMOG_ALG':
                    $this->alg = $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];
                    break;
            }
        }
    }

    function install() {
        $install_configuration_flag = true;

        if ($this->payment_methods_id > 0) {
            if ($this->receive_status != 1) {
                $update_receive_status_sql_data = array("payment_methods_receive_status" => 1);
                tep_db_perform(TABLE_PAYMENT_METHODS, $update_receive_status_sql_data, 'update', "payment_methods_id = '" . (int) $this->payment_methods_id . "'");
            } else {
                $install_configuration_flag = false;
            }
        } else {
            $install_payment_gateway_data_sql = array(
                'payment_methods_code' => $this->code,
                'payment_methods_send_status' => 0,
                'payment_methods_title' => $this->code,
                'payment_methods_types_id' => 0,
                'payment_methods_parent_id' => 0,
                'payment_methods_legend_color' => '#6E920D',
                'payment_methods_sort_order' => 6,
                'payment_methods_filename' => $this->filename,
                'payment_methods_logo' => '',
                'date_added' => 'now()',
                'last_modified' => 'now()',
                'payment_methods_receive_status' => 1,
                'payment_methods_receive_status_mode' => 0);
            tep_db_perform(TABLE_PAYMENT_METHODS, $install_payment_gateway_data_sql);
            $this->payment_methods_id = tep_db_insert_id();
        }

        if ($install_configuration_flag) {
            $install_key_array = array();
            $install_key_array[] = array(
                'info' => array(
                    'payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Transaction Mode',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_INDOMOG_MODE',
                    'payment_configuration_info_description' => 'Transaction mode used for the indomog service',
                    'payment_configuration_info_sort_order' => '610',
                    'set_function' => 'tep_cfg_select_option(array(\'Test\', \'Production\'),',
                    'date_added' => 'now()'
                ),
                'desc' => array(
                    'payment_configuration_info_value' => 'Production',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array(
                'info' => array(
                    'payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Set Order Status',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_INDOMOG_ORDER_STATUS_ID',
                    'payment_configuration_info_description' => 'Set the status of orders made with this payment module to this value',
                    'payment_configuration_info_sort_order' => '620',
                    'set_function' => 'tep_cfg_pull_down_order_statuses(',
                    'use_function' => 'tep_get_order_status_name',
                    'date_added' => 'now()'
                ),
                'desc' => array(
                    'payment_configuration_info_value' => '1',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array(
                'info' => array(
                    'payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Set Order\'s "Processing" Status',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_INDOMOG_PROCESSING_STATUS_ID',
                    'payment_configuration_info_description' => 'Set the initial processing status of orders made with this payment module to this value',
                    'payment_configuration_info_sort_order' => '625',
                    'set_function' => 'tep_cfg_pull_down_order_statuses(',
                    'use_function' => 'tep_get_order_status_name',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => '7',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array(
                'info' => array(
                    'payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Payment Message',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_INDOMOG_MESSAGE',
                    'payment_configuration_info_description' => 'Payment message will show up during checkout process (indomog)',
                    'payment_configuration_info_sort_order' => '640',
                    'set_function' => 'tep_cfg_textarea(',
                    'date_added' => 'now()'
                ),
                'desc' => array(
                    'payment_configuration_info_value' => 'Thank you for shopping at Offgamers.com.',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array(
                'info' => array(
                    'payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Payment Email Message',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_INDOMOG_EMAIL_MESSAGE',
                    'payment_configuration_info_description' => 'This message will show up in order confirmation e-mail.',
                    'payment_configuration_info_sort_order' => '645',
                    'set_function' => 'tep_cfg_textarea(',
                    'date_added' => 'now()'
                ),
                'desc' => array(
                    'payment_configuration_info_value' => 'Thank you for shopping at Offgamers.com.',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array(
                'info' => array(
                    'payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Confirm Complete (in days)',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_INDOMOG_CONFIRM_COMPLETE',
                    'payment_configuration_info_description' => 'The number of days in which an order made by this payment method is confirm completed',
                    'payment_configuration_info_sort_order' => '655',
                    'set_function' => 'NULL',
                    'date_added' => 'now()'
                ),
                'desc' => array(
                    'payment_configuration_info_value' => '0',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array(
                'info' => array(
                    'payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Require Address Information',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_INDOMOG_MANDATORY_ADDRESS_FIELD',
                    'payment_configuration_info_description' => 'Set address field as required info during checkout.',
                    'payment_configuration_info_sort_order' => '660',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array(
                    'payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array(
                'info' => array(
                    'payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Customer Payment Info',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_INDOMOG_CUSTOMER_PAYMENT_INFO',
                    'payment_configuration_info_description' => 'Set to true if this Payment Method required customer input for Payment Information required customer payment info.',
                    'payment_configuration_info_sort_order' => '665',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array(
                    'payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array(
                'info' => array(
                    'payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Require IC Information',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_INDOMOG_MANDATORY_IC_FIELD',
                    'payment_configuration_info_description' => 'Set IC field as required info during checkout.',
                    'payment_configuration_info_sort_order' => '670',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array(
                'info' => array(
                    'payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Require Contact Information',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_INDOMOG_MANDATORY_CONTACT_FIELD',
                    'payment_configuration_info_description' => 'Set Contact field as required info during checkout.',
                    'payment_configuration_info_sort_order' => '675',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array(
                    'payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );
            foreach ($install_key_array as $data) {
                tep_db_perform(TABLE_PAYMENT_CONFIGURATION_INFO, $data['info']);
                $payment_conf_id = tep_db_insert_id();

                $data['desc']['payment_configuration_info_id'] = (int) $payment_conf_id;
                tep_db_perform(TABLE_PAYMENT_CONFIGURATION_INFO_DESCRIPTION, $data['desc']);
            }
        }
        return 1;
    }

    function remove() {
        tep_db_query("delete from " . TABLE_CONFIGURATION . " where configuration_key in ('" . implode("', '", $this->keys()) . "')");
    }

    function keys() {
        return array(
            'MODULE_PAYMENT_INDOMOG_STATUS', 'MODULE_PAYMENT_INDOMOG_ID', 'MODULE_PAYMENT_INDOMOG_MODE', 'MODULE_PAYMENT_INDOMOG_SORT_ORDER', 'MODULE_PAYMENT_INDOMOG_ORDER_STATUS_ID',
            'MODULE_PAYMENT_INDOMOG_PROCESSING_STATUS_ID', 'MODULE_PAYMENT_INDOMOG_SECRET_WORD', 'MODULE_PAYMENT_INDOMOG_ZONE', 'MODULE_PAYMENT_INDOMOG_MESSAGE',
            'MODULE_PAYMENT_INDOMOG_CONFIRM_COMPLETE', 'MODULE_PAYMENT_INDOMOG_EMAIL_MESSAGE', 'MODULE_PAYMENT_INDOMOG_LEGEND_COLOUR', 'MODULE_PAYMENT_INDOMOG_ALG'
        );
    }

    function configuration_information_keys() {
        return array(
            'MODULE_PAYMENT_INDOMOG_MODE',
            'MODULE_PAYMENT_INDOMOG_ORDER_STATUS_ID',
            'MODULE_PAYMENT_INDOMOG_PROCESSING_STATUS_ID',
            'MODULE_PAYMENT_INDOMOG_MESSAGE',
            'MODULE_PAYMENT_INDOMOG_EMAIL_MESSAGE',
            'MODULE_PAYMENT_INDOMOG_CONFIRM_COMPLETE',
            'MODULE_PAYMENT_INDOMOG_MANDATORY_ADDRESS_FIELD',
            'MODULE_PAYMENT_INDOMOG_CUSTOMER_PAYMENT_INFO',
            'MODULE_PAYMENT_INDOMOG_MANDATORY_IC_FIELD',
            'MODULE_PAYMENT_INDOMOG_MANDATORY_CONTACT_FIELD'
        );
    }

    function multi_lang_configuration_info_keys() {
        return array(
            'MODULE_PAYMENT_INDOMOG_MESSAGE',
            'MODULE_PAYMENT_INDOMOG_MANDATORY_IC_FIELD',
            'MODULE_PAYMENT_INDOMOG_EMAIL_MESSAGE'
        );
    }

    function merchant_information_keys() {
        include_once(DIR_WS_CLASSES . "payment_methods.php");

        return array(
            'MODULE_PAYMENT_INDOMOG_ID' => array("field" => 'text', 'mapping' => 'MODULE_PAYMENT_INDOMOG_LNG_ID'),
            'MODULE_PAYMENT_INDOMOG_SECRET_WORD' => array("field" => 'text', 'mapping' => 'MODULE_PAYMENT_INDOMOG_LNG_SECRET_WORD'),
            'MODULE_PAYMENT_INDOMOG_ALG' => array("field" => 'text', 'mapping' => 'MODULE_PAYMENT_INDOMOG_LNG_ALG'),
            'MODULE_PAYMENT_TAX' => array("field" => 'select', 'mapping' => "MODULE_PAYMENT_TAX", "select" => payment_methods::get_tax_option())
        );
    }

    function get_pg_id() {
        return $this->payment_methods_id;
    }

    function get_orders_payment_info_file() {
        return DIR_FS_CATALOG_MODULES . 'payment/indomog/admin/orders.inc.php';
    }

    function get_orders_list_payment_info_file() {
        return DIR_FS_CATALOG_MODULES . 'payment/indomog/admin/orders_list.inc.php';
    }

    function get_ipn_class_file() {
        return '';
    }

}

?>