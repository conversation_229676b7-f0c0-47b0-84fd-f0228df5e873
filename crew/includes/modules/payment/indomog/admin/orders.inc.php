<?php
require_once(DIR_FS_CATALOG_MODULES . 'payment/indomog.php');
include_once(DIR_FS_CATALOG_MODULES . 'payment/indomog/admin/languages/' . $language . '/indomog.lng.php');

$indomog_currency = 'IDR';

$trans_info_select_sql = "	SELECT * 
                            FROM " . TABLE_INDOMOG . " 
                            WHERE indomog_orders_id = '" . (int)$oID . "'";
$trans_info_result_sql= tep_db_query($trans_info_select_sql);

$trans_history_select_sql = "	SELECT * 
                                FROM " . TABLE_INDOMOG_STATUS_HISTORY . "
                                WHERE indomog_orders_id = '" . (int)$oID  . "' 
                                ORDER BY indomog_status_datetime";
$trans_history_result_sql = tep_db_query($trans_history_select_sql);
?>
<table border="0" width="100%" cellspacing="0" cellpadding="2">
<?php ob_start(); ?>
	<tr>
		<td class="main">
      		<table border="0" width="100%" cellspacing="0" cellpadding="2">
      			<tr>
      				<td class="main" width="10%" nowrap><b><?=ENTRY_PAYMENT_METHOD?></b></td>
      				<td class="main">
      					<div>
      						<?php if((int)$order->info['payment_methods_id']>0) { ?><div class="<?php echo ((int)$payment_info_array[$order->info['payment_methods_id']]->confirm_complete_days>0?'rp_icon':'nrp_icon'); ?>"><b><?php echo ((int)$payment_info_array[$order->info['payment_methods_id']]->confirm_complete_days>0?'RP':'NRP'); ?></b></div> <?php } ?>
      						<div style="width:30px; height:15px; background-color:<?=$payment_module_info['display_colour']?>; float:left; vertical-align:top; text-align:center;">&nbsp;</div>
      						<div style="vertical-align: top">
      							&nbsp;&nbsp;
<?php
	if ($view_payment_details_permission) {
		
		if (tep_not_null($last_payment_method) && $last_payment_method['payment_methods_id'] != $order->info['payment_methods_id']) {
			echo '<span class="redIndicator">'.(tep_not_null($payment_module_info['payment_methods_parent_title'])?$payment_module_info['payment_methods_parent_title'] . ' - ' :'') . $payment_module_info['payment_methods_title'].'</span>';
		} else {
			echo (tep_not_null($payment_module_info['payment_methods_parent_title'])?$payment_module_info['payment_methods_parent_title'] . ' - ' :'') . $payment_module_info['payment_methods_title'];
		}
		
		if (tep_not_null($last_payment_method) && $last_payment_method['payment_methods_id'] != $order->info['payment_methods_id']) {
			echo '&nbsp;&nbsp;<span class="redIndicator">(Last Payment Method: ' . (tep_not_null($last_payment_method['parent_payment_methods_title'])? $last_payment_method['parent_payment_methods_title'] . ' - ' : '' ) . $last_payment_method['payment_methods_title'] . ')</span>';
		}
	} else {
		echo (tep_not_null($payment_module_info['payment_methods_parent_title'])?$payment_module_info['payment_methods_parent_title'] . ' - ' :'') . $payment_module_info['payment_methods_title'];
	}
?>
							</div>
      					</div>
      				</td>
				</tr>
			</table>
		</td>
	</tr>
<?php
$payment_method_title_info = ob_get_contents();
ob_end_clean();

if (tep_db_num_rows($trans_info_result_sql) || tep_db_num_rows($trans_history_result_sql)) {
	$trans_info_row = tep_db_fetch_array($trans_info_result_sql);
	
	echo $payment_method_title_info;
	if (!$view_payment_details_permission) {
		;
	} else {
?>
  	<tr>
    	<td class="main">
      		<table border="0" width="100%" cellspacing="0" cellpadding="2">
      			<tr valign="top">
        			<td width="35%">
              			<table border="0" cellspacing="0" cellpadding="2">
              				<tr>
                				<td class="main" valign="top" nowrap><b><?php echo ENTRY_INDOMOG_TRANSACTION_ID; ?></b>&nbsp;</td>
                				<td class="main"><?php echo $trans_info_row['indomog_transaction_id']; ?></td>
              				</tr>
              				<tr>
                				<td class="main" valign="top" nowrap><b><?php echo ENTRY_INDOMOG_BANK_ID; ?></b>&nbsp;</td>
                				<td class="main" nowrap><?php echo $trans_info_row['indomog_bank_id']; ?></td>
              				</tr>
              				<tr>
                				<td class="main" valign="top" nowrap><b><?php echo ENTRY_INDOMOG_CURRENCY; ?></b>&nbsp;</td>
                				<td class="main" nowrap>
                                <?php
			    					if (strtolower($indomog_currency) == strtolower($order->info['currency'])) {
			    						echo $indomog_currency;
			    					} else {
			    						echo '<span class="redIndicator">' . $indomog_currency . '<br><span class="smallText">Does not match purchase currency.</span></span>';
			    					}
    								echo ' (' . TEXT_INDOMOG_CURRENCY_NOT_FROM_INDOMOG . ')';
    							?>
                                </td>
              				</tr>
              				<tr>
                				<td class="main" valign="top" nowrap><b><?php echo ENTRY_INDOMOG_AMT; ?></b>&nbsp;</td>
                				<td class="main" nowrap>
                                <?php
			    					$gross_display_text = number_format($trans_info_row["indomog_amount"], $currencies->currencies[$order->info["currency"]]['decimal_places'], $currencies->currencies[$order->info["currency"]]['decimal_point'], $currencies->currencies[$order->info["currency"]]['thousands_point']);
			    					if ($currencies->currencies[$order->info["currency"]]['symbol_left'].$gross_display_text.$currencies->currencies[$order->info["currency"]]['symbol_right'] != strip_tags($order->order_totals['ot_total']['text']) ) {
			    						$gross_display_text = '<span class="redIndicator">' . $gross_display_text . '<br><span class="smallText">Does not match purchase amount.</span></span>';
			    					}
			    					echo $gross_display_text;
			    				?>
                                </td>
              				</tr>
                            <tr>
                				<td class="main" valign="top" nowrap><b><?php echo ENTRY_INDOMOG_SIGNATURE; ?></b>&nbsp;</td>
                				<td class="main" nowrap><?php echo $trans_info_row['indomog_signature']; ?></td>
              				</tr>
              			</table>
        			</td>
        			<td>
              			<table border="0" cellspacing="0" cellpadding="2">
                            <tr>
                				<td class="main" valign="top" nowrap><b><?php echo ENTRY_INDOMOG_PAYMENT_STATUS; ?></b>&nbsp;</td>
                				<td class="main" nowrap>
	              				<?php
                					echo $trans_info_row['indomog_transaction_status'];
                					
                					if ($order->info['orders_status'] == '1' || $order->info['orders_status'] == '7' || $order->info['orders_status'] == '2') {
	                					echo "&nbsp;&nbsp;";
										echo tep_draw_form('indomog_status_check_form', FILENAME_ORDERS, tep_get_all_get_params(), 'post');
										echo tep_draw_hidden_field('subaction', 'payment_action');
										echo tep_draw_hidden_field('payment_action', 'check_trans_status');
										echo tep_submit_button('Check Payment Status', 'Check Payment Status', 'name="indomogCheckTransStatusBtn"', 'inputButton');
										echo "</form>";
									}
								?>
								</td>
							</tr>
              				<tr>
                				<td class="main" colspan="2" nowrap>
                					<table border="1" cellspacing="0" cellpadding="2">
        								<tr>
                							<td class="smallText" nowrap><b><?php echo TABLE_HEADING_INDOMOG_DATE; ?></b></td>
                							<td class="smallText" nowrap><b><?php echo TABLE_HEADING_INDOMOG_STATUS; ?></b></td>
                                            <td class="smallText" nowrap><b><?php echo TABLE_HEADING_INDOMOG_CHANGED_BY; ?></b></td>
                						</tr>
<?php
		while ($trans_history_row = tep_db_fetch_array($trans_history_result_sql)) {
        	echo ' 						<tr>
                							<td class="smallText" nowrap>' . $trans_history_row['indomog_status_datetime'] . '</td>
                							<td class="smallText" nowrap>' . (tep_not_null($trans_history_row['indomog_response_code']) ? $trans_history_row['indomog_response_code'] . ': ' : '') . $trans_history_row['indomog_response_description'] . '</td>
                                            <td class="smallText" nowrap>' . $trans_history_row['indomog_changed_by'] . '</td>
            							</tr>';
     	}
?>
                					</table>
                				</td>
                			</tr>
              			</table>
              		</td>
      			</tr>
      		</table>
   		</td>
  	</tr>
<?
	}
} else {
	echo $payment_method_title_info;
?>	
	<tr>
		<td class="main" nowrap>
		<?
			if ($order->info['orders_status'] == '1' || $order->info['orders_status'] == '7' || $order->info['orders_status'] == '2') {
				echo "&nbsp;&nbsp;";
				echo tep_draw_form('indomog_status_check_form', FILENAME_ORDERS, tep_get_all_get_params(), 'post');
				echo tep_draw_hidden_field('subaction', 'payment_action');
				echo tep_draw_hidden_field('payment_action', 'check_trans_status');
				echo tep_submit_button('Check Payment Status', 'Check Payment Status', 'name="indomogCheckTransStatusBtn"', 'inputButton');
				echo "</form>";
			}
		?>
		</td>
	</tr>
<?
}
?>
</table>