<?php
include_once(DIR_FS_CATALOG_MODULES . 'payment/indomog/admin/languages/' . $language . '/indomog.lng.php');

$indomog_currency = 'IDR';

$trans_info_select_sql = "	SELECT * 
                            FROM " . TABLE_INDOMOG . " 
                            WHERE indomog_orders_id = '" . $order_obj->orders_id . "'";
$trans_info_result_sql= tep_db_query($trans_info_select_sql);
?>
<table border="0" width="100%" cellspacing="0" cellpadding="2">
	<tr>
		<td class="subInvoiceBoxHeading">
			<div style="border-bottom: 1px solid #996600; border-top: 1px solid #996600; padding: 2px;">Payment Details</div>
		</td>
	</tr>
<?php
if ($trans_info_row = tep_db_fetch_array($trans_info_result_sql)) {
?>
	<tr>
    	<td class="main">
      		<table border="0" width="100%" cellspacing="0" cellpadding="2">
      			<tr valign="top">
        			<td width="50%">
              			<table border="0" cellspacing="0" cellpadding="2">
              				<tr>
                				<td class="invoiceRecords" valign="top" nowrap><?php echo ENTRY_INDOMOG_TRANSACTION_ID; ?>:&nbsp;</td>
                				<td class="invoiceRecords" nowrap><?php echo $trans_info_row['indomog_transaction_id']?></td>
              				</tr>
              				<tr>
                				<td class="invoiceRecords" valign="top" nowrap><?php echo ENTRY_INDOMOG_BANK_ID; ?>:&nbsp;</td>
                				<td class="invoiceRecords"><?php echo $trans_info_row['indomog_bank_id']; ?></td>
              				</tr>
              				<tr>
              					<td class="invoiceRecords" valign="top" nowrap><?php echo ENTRY_INDOMOG_CURRENCY; ?>:&nbsp;</td>
                				<td class="invoiceRecords"><?php echo $indomog_currency; ?></td>
              				</tr>
                            <tr>
                				<td class="invoiceRecords" valign="top" nowrap><?php echo ENTRY_INDOMOG_AMT; ?>:&nbsp;</td>
                				<td class="invoiceRecords" nowrap><?php echo $trans_info_row["indomog_amount"]; ?></td>
              				</tr>
                            <tr>
                				<td class="invoiceRecords" valign="top" nowrap><?php echo ENTRY_INDOMOG_SIGNATURE; ?>:&nbsp;</td>
                				<td class="invoiceRecords" nowrap><?php echo $trans_info_row['indomog_signature']; ?></td>
              				</tr>
              			</table>
        			</td>
        			<td valign="top">
              			<table border="0" cellspacing="0" cellpadding="2">
              				<tr>
                				<td class="invoiceRecords" valign="top" nowrap><?php echo ENTRY_INDOMOG_PAYMENT_STATUS; ?>:&nbsp;</td>
                				<td class="invoiceRecords"><?php echo $trans_info_row['indomog_transaction_status']; ?></td>
              				</tr>
              			</table>
              		</td>
      			</tr>
      		</table>
   		</td>
  	</tr>
<?
} else {
?>
	<tr>
		<td class="invoiceRecords">No further payment information is available.</td>
	</tr>
<?
}
?>