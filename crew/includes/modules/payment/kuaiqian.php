<?

/*
  $Id: kuaiqian.php, v1.0 2005/11/29 18:08:05
  Author : <PERSON><PERSON> (<EMAIL>)
  Title: KuaiQian Payment Module V1.0

  Revisions:
  Version 1.0 Initial Payment Module

  Copyright (c) 2005 SKC Venture

  Released under the GNU General Public License
 */

class kuaiqian {

    var $code, $title, $description, $enabled, $kuaiqianCurrencies;
    var $defCurr, $amount_decimal, $order_processing_status;

    function kuaiqian($pm_id = 0) {
        global $order, $languages_id, $default_languages_id;

        $this->payment_methods_id = 0;
        $this->payment_methods_parent_id = 0;

        $this->code = 'kuaiqian';
        $this->title = $this->code;

        $this->preferred = true;
        $this->filename = 'kuaiqian.php';

        $this->amount_decimal = 2;

        $payment_methods_select_sql = "	SELECT payment_methods_parent_id, payment_methods_code, 
												payment_methods_types_id, 
												payment_methods_receive_status_mode, payment_methods_id, 
												payment_methods_title, payment_methods_sort_order, 
												payment_methods_receive_status, payment_methods_legend_color, 
												payment_methods_logo 
										FROM " . TABLE_PAYMENT_METHODS . " 
										WHERE 1 ";
        if ((int) $pm_id > 0) {
            $payment_methods_select_sql .= "	AND payment_methods_id = '" . (int) $pm_id . "'";
        } else {
            $payment_methods_select_sql .= "	AND payment_methods_filename = '" . tep_db_input($this->filename) . "'";
        }

        $payment_methods_result_sql = tep_db_query($payment_methods_select_sql);
        if ($payment_methods_row = tep_db_fetch_array($payment_methods_result_sql)) {
            $pm_desc_title_select_sql = "	SELECT payment_methods_description_title 
											FROM " . TABLE_PAYMENT_METHODS_DESCRIPTION . " 
											WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "' 
												AND (IF (languages_id = '" . tep_db_input($languages_id) . "', 1, IF ((	SELECT COUNT(payment_methods_id) > 0 
																														FROM " . TABLE_PAYMENT_METHODS_DESCRIPTION . " 
																														WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "' 
																															AND languages_id = '" . (int) $languages_id . "'), 0, languages_id = 1)))";
            $pm_desc_title_result_sql = tep_db_query($pm_desc_title_select_sql);
            $pm_desc_title_row = tep_db_fetch_array($pm_desc_title_result_sql);

            $this->payment_methods_id = (int) $payment_methods_row['payment_methods_id'];
            $this->payment_methods_parent_id = (int) $payment_methods_row['payment_methods_parent_id'];
            $this->code = $payment_methods_row['payment_methods_code'];
            $this->title = $payment_methods_row['payment_methods_title']; //MODULE_PAYMENT_PAYPAL_TEXT_TITLE;
            $this->display_title = $pm_desc_title_row['payment_methods_description_title']; //MODULE_PAYMENT_PAYPAL_TEXT_DISPLAY_TITLE; 	// if title to be display for payment method is different from payment method value
            $this->sort_order = $payment_methods_row['payment_methods_sort_order']; //MODULE_PAYMENT_PAYPAL_SORT_ORDER;
            $this->enabled = $payment_methods_row['payment_methods_receive_status_mode'];
            $this->legend_display_colour = $payment_methods_row['payment_methods_legend_color'];
            $this->receive_status = $payment_methods_row['payment_methods_receive_status'];
            $this->description = $this->display_title;

            $this->kuaiqianCurrencies = $this->get_support_currencies($this->payment_methods_id);

            $configuration_setting_array = $this->load_pm_setting();

            $this->version = $configuration_setting_array['MODULE_PAYMENT_KUAIQIAN_VERSION'];
            $this->language = $configuration_setting_array['MODULE_PAYMENT_KUAIQIAN_LANGUAGE'];
            $this->signtype = $configuration_setting_array['MODULE_PAYMENT_KUAIQIAN_SIGNTYPE'];
            $this->bgurl = $configuration_setting_array['MODULE_PAYMENT_KUAIQIAN_BGURL'];
            $this->inputcharset = $configuration_setting_array['MODULE_PAYMENT_KUAIQIAN_INPUTCHARSET'];
            $this->paytype = $configuration_setting_array['MODULE_PAYMENT_KUAIQIAN_PAYTYPE'];
            $this->payerContactType = $configuration_setting_array['MODULE_PAYMENT_KUAIQIAN_PAYERCONTACTTYPE'];

            $this->order_status_id = $configuration_setting_array['MODULE_PAYMENT_KUAIQIAN_ORDER_STATUS_ID'];
            $this->processing_status_id = $configuration_setting_array['MODULE_PAYMENT_KUAIQIAN_PROCESSING_STATUS_ID'];
            $this->message = $configuration_setting_array['MODULE_PAYMENT_KUAIQIAN_MESSAGE'];
            $this->email_message = $configuration_setting_array['MODULE_PAYMENT_KUAIQIAN_EMAIL_MESSAGE'];
            $this->confirm_complete_days = $configuration_setting_array['MODULE_PAYMENT_KUAIQIAN_CONFIRM_COMPLETE'];
            $this->require_address_information = $configuration_setting_array['MODULE_PAYMENT_KUAIQIAN_MANDATORY_ADDRESS_FIELD'];
            $this->require_ic_information = $configuration_setting_array['MODULE_PAYMENT_KUAIQIAN_MANDATORY_IC_FIELD'];
            $this->require_contact_information = $configuration_setting_array['MODULE_PAYMENT_KUAIQIAN_MANDATORY_CONTACT_FIELD'];

            $this->customer_payment_info = $configuration_setting_array['MODULE_PAYMENT_KUAIQIAN_CUSTOMER_PAYMENT_INFO'];

            if ((int) $this->order_status_id > 0) {
                $this->order_status = $this->order_status_id;
            }

            $this->email_footer = str_replace(array("<br>", "<BR>", "\r\n"), array("\n", "\n", ''), $this->email_message);
            $this->order_processing_status = (int) $this->processing_status_id > 0 ? $this->processing_status_id : 7;
        }

        $this->force_to_checkoutprocess = true;
        $this->form_action_url = 'https://www.99bill.com/gateway/recvMerchantInfoAction.htm';
        $this->has_ipn = true;
        $this->auto_cancel_period = 60; // In minutes
    }

    function javascript_validation() {
        return false;
    }

    function selection() {
        global $languages_id, $default_languages_id;

        $selection_array = array();

        $payment_methods_select_sql = "	SELECT payment_methods_id, payment_methods_code, payment_methods_types_id, payment_methods_logo, payment_methods_receive_status_mode 
										FROM " . TABLE_PAYMENT_METHODS . " 
										WHERE payment_methods_receive_status = 1 
											AND payment_methods_receive_status_mode <> 0 
											AND payment_methods_parent_id = '" . (int) $this->payment_methods_id . "' 
										ORDER BY payment_methods_sort_order";
        $payment_methods_result_sql = tep_db_query($payment_methods_select_sql);
        while ($payment_methods_row = tep_db_fetch_array($payment_methods_result_sql)) {
            $pm_desc_title_select_sql = "	SELECT payment_methods_description_title 
											FROM " . TABLE_PAYMENT_METHODS_DESCRIPTION . " 
											WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "' 
												AND (IF (languages_id = '" . tep_db_input($languages_id) . "', 1, IF ((	SELECT COUNT(payment_methods_id) > 0 
																														FROM " . TABLE_PAYMENT_METHODS_DESCRIPTION . " 
																														WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "' 
																															AND languages_id = '" . (int) $languages_id . "'), 0, languages_id = 1)))";
            $pm_desc_title_result_sql = tep_db_query($pm_desc_title_select_sql);
            $pm_desc_title_row = tep_db_fetch_array($pm_desc_title_result_sql);

            $pm_array = $this->load_pm_setting(1, $payment_methods_row['payment_methods_id']);

            $selection_array[] = array('payment_gateway_code' => $this->code,
                'payment_methods_receive_status_mode' => $payment_methods_row['payment_methods_receive_status_mode'],
                'payment_methods_id' => $payment_methods_row['payment_methods_id'],
                'payment_methods_code' => $payment_methods_row['payment_methods_code'],
                'payment_methods_types_id' => $payment_methods_row['payment_methods_types_id'],
                'payment_methods_parent_id' => $this->payment_methods_id,
                'payment_methods_logo' => $payment_methods_row['payment_methods_logo'],
                'payment_methods_description_title' => $pm_desc_title_row['payment_methods_description_title'],
                'currency' => $this->get_support_currencies($payment_methods_row['payment_methods_id']),
                'show_billing_address' => $pm_array['MODULE_PAYMENT_KUAIQIAN_MANDATORY_ADDRESS_FIELD'],
                'confirm_complete_days' => $pm_array['MODULE_PAYMENT_KUAIQIAN_CONFIRM_COMPLETE'],
                'show_contact_number' => $pm_array['MODULE_PAYMENT_KUAIQIAN_MANDATORY_CONTACT_FIELD'],
                'show_ic' => $pm_array['MODULE_PAYMENT_KUAIQIAN_MANDATORY_IC_FIELD']
            );

            if ($payment_methods_row['payment_methods_receive_status_mode'] == '-1') { // Inactive
                $pm_status_message_select = "	SELECT payment_methods_status_message 
												FROM " . TABLE_PAYMENT_METHODS_STATUS_DESCRIPTION . " 
												WHERE payment_methods_mode = 'RECEIVE' 
													AND payment_methods_status = '-1' 
													AND languages_id = '" . (int) $languages_id . "' 
													AND payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "'";
                $pm_status_message_result = tep_db_query($pm_status_message_select);
                $pm_status_message_row = tep_db_fetch_array($pm_status_message_result);
                $selection_array[sizeof($selection_array) - 1]['payment_methods_status_message'] = $pm_status_message_row['payment_methods_status_message'];
            }
        }

        return $selection_array;
    }

    function get_support_currencies($pm_id, $by_zone = true) {
        global $zone_info_array;

        $default_currency = '';
        $support_currencies_array = array();

        $currency_code_select_sql = "	SELECT currency_code, payment_methods_instance_default 
    									FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " 
    									WHERE payment_methods_id = '" . (int) $pm_id . "' 
    									ORDER BY currency_code";
        $currency_code_result_sql = tep_db_query($currency_code_select_sql);

        if (tep_db_num_rows($currency_code_result_sql) > 0) {
            while ($currency_code_row = tep_db_fetch_array($currency_code_result_sql)) {
                $support_currencies_array[] = $currency_code_row['currency_code'];

                if ($currency_code_row['payment_methods_instance_default'] == 1) {
                    $default_currency = $currency_code_row['currency_code'];
                }
            }
        } else {
            if ($this->payment_methods_parent_id > 0) {
                $payment_methods_parent_id = $this->payment_methods_parent_id;
            } else {
                $payment_methods_parent_id = $this->payment_methods_id;
            }

            $parent_currency_code_select_sql = "	SELECT currency_code, payment_methods_instance_default 
			    									FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " 
			    									WHERE payment_methods_id = '" . (int) $payment_methods_parent_id . "' 
			    									ORDER BY currency_code";
            $parent_currency_code_result_sql = tep_db_query($parent_currency_code_select_sql);
            while ($parent_currency_code_row = tep_db_fetch_array($parent_currency_code_result_sql)) {
                $support_currencies_array[] = $parent_currency_code_row['currency_code'];

                if ($parent_currency_code_row['payment_methods_instance_default'] == 1) {
                    $default_currency = $parent_currency_code_row['currency_code'];
                }
            }
        }

        if (tep_not_null($default_currency)) {
            $this->defCurr = $default_currency;
        }

        if ($by_zone) {
            if (isset($zone_info_array)) {
                $support_currencies_array = array_intersect($zone_info_array[3]->zone_currency_id, $support_currencies_array);
                if (count($support_currencies_array)) {
                    array_multisort($support_currencies_array);
                } else {
                    $support_currencies_array = array($default_currency);
                }
            } else {
                $support_currencies_array = array($default_currency);
            }
        } else {
            $this->set_support_currencies($support_currencies_array);
            // All found currencies is supported. Normally this is used for backend script such as IPN call
        }

        return $support_currencies_array;
    }

    function get_require_address_information() {
        return $this->require_address_information;
    }

    function get_confirm_complete_days() {
        return $this->confirm_complete_days;
    }

    function get_pm_info() {
        return $this->message;
    }

    function get_email_pm_info() {
        return $this->email_message;
    }

    function draw_confirm_button() {
        return tep_image_button2('red', 'javascript:document.checkout_confirmation.submit()', MODULE_PAYMENT_KUAIQIAN_TEXT_CART, 200);
    }

    function get_confirm_button_caption() {
        return MODULE_PAYMENT_KUAIQIAN_TEXT_TITLE;
    }

    function pre_confirmation_check() {
        global $currency, $currencies, $messageStack;
        $supported_currency_str_array = array();

        if (!in_array($currency, $this->kuaiqianCurrencies)) {
            
        }
    }

    function confirmation() {
        global $HTTP_POST_VARS;

        return array('title' => $this->message);
    }

    function process_button() {
        global $HTTP_POST_VARS, $shipping_cost, $total_cost, $shipping_selected, $shipping_method;
        global $currencies, $currency, $order, $languages_id;
        global $order_logged;

        if ($this->force_to_checkoutprocess && !tep_session_is_registered('order_logged'))
            return;

        /*         * ****************************************************************************
          Multi Currency
          Convert to USD currency
         * **************************************************************************** */
        $kuaiQianCurrency = $currency;
        if (!in_array($kuaiQianCurrency, $this->kuaiqianCurrencies)) {
            $kuaiQianCurrency = in_array(DEFAULT_CURRENCY, $this->kuaiqianCurrencies) ? DEFAULT_CURRENCY : $this->kuaiqianCurrencies[0];
        }
        $this->get_merchant_account($kuaiQianCurrency);

        $purchase_date_select_sql = "	SELECT date_purchased  
      									FROM " . TABLE_ORDERS . "
      									WHERE orders_id = '" . (int) $order_logged . "'";
        $purchase_date_result_sql = tep_db_query($purchase_date_select_sql);
        $purchase_date_row = tep_db_fetch_array($purchase_date_result_sql);

        $pattern = array("/-/", "/:/", "/\s/");
        $replacement = array("", "", "");
        $orderTime = preg_replace($pattern, $replacement, $purchase_date_row['date_purchased']);

        $OrderAmt = number_format($order->info['total'] * $currencies->get_value($kuaiQianCurrency), $currencies->get_decimal_places($kuaiQianCurrency), '.', '');

        $kuaiqian_array = array();
        $kuaiqian_array['inputCharset'] = $this->inputcharset;
        $kuaiqian_array['bgUrl'] = $this->bgurl;
        $kuaiqian_array['version'] = $this->version;
        $kuaiqian_array['language'] = $this->language;
        $kuaiqian_array['signType'] = $this->signtype;
        $kuaiqian_array['merchantAcctId'] = $this->merchantAcctId . '01';
        $kuaiqian_array['payerName'] = $order->billing['firstname'] . ' ' . $order->billing['lastname'];
        $kuaiqian_array['payerContactType'] = $this->payerContactType;
        $kuaiqian_array['payerContact'] = $order->customer['email_address'];
        $kuaiqian_array['orderId'] = $order_logged;
        $kuaiqian_array['orderAmount'] = $OrderAmt * 100;
        $kuaiqian_array['orderTime'] = $orderTime;
        $kuaiqian_array['productName'] = 'Purchase from ' . STORE_NAME;
        $kuaiqian_array['productNum'] = count($order->products);
        $kuaiqian_array['productId'] = $this->productId;
        $kuaiqian_array['productDesc'] = $this->productDesc;
        $kuaiqian_array['ext1'] = $this->ext1;
        $kuaiqian_array['ext2'] = $this->ext2;
        $kuaiqian_array['payType'] = $this->paytype;
        if ($this->paytype == '10' || $this->paytype == '00') {
            $kuaiqian_array['bankId'] = $this->code;
        } else {
            $kuaiqian_array['bankId'] = '';
        }
        $kuaiqian_array['redoFlag'] = 1;
        $kuaiqian_array['pid'] = $this->pid;
        $kuaiqian_array['key'] = $this->key;

        $signMsgVal = '';
        foreach ($kuaiqian_array as $key => $data) {
            if ($data != '') {
                if ($signMsgVal != '') {
                    $signMsgVal .= '&';
                }
                $signMsgVal .= $key . "=" . $data;
            }
        }
        $signMsg = strtoupper(md5($signMsgVal));

        $process_button_string =
                tep_draw_hidden_field('inputCharset', $kuaiqian_array['inputCharset']) .
                tep_draw_hidden_field('bgUrl', $kuaiqian_array['bgUrl']) .
                tep_draw_hidden_field('version', $kuaiqian_array['version']) .
                tep_draw_hidden_field('language', $kuaiqian_array['language']) .
                tep_draw_hidden_field('signType', $kuaiqian_array['signType']) .
                tep_draw_hidden_field('signMsg', $signMsg) .
                tep_draw_hidden_field('merchantAcctId', $kuaiqian_array['merchantAcctId']) .
                tep_draw_hidden_field('payerName', $kuaiqian_array['payerName']) . // optional
                tep_draw_hidden_field('payerContactType', $kuaiqian_array['payerContactType']) . // optional, only can choose email
                tep_draw_hidden_field('payerContact', $kuaiqian_array['payerContact']) . // email <--
                tep_draw_hidden_field('orderId', $kuaiqian_array['orderId']) .
                tep_draw_hidden_field('orderAmount', $kuaiqian_array['orderAmount']) .
                tep_draw_hidden_field('orderTime', $kuaiqian_array['orderTime']) .
                tep_draw_hidden_field('productName', $kuaiqian_array['productName']) .
                tep_draw_hidden_field('productNum', $kuaiqian_array['productNum']) .
                tep_draw_hidden_field('productId', $kuaiqian_array['productId']) .
                tep_draw_hidden_field('productDesc', $kuaiqian_array['productDesc']) .
                tep_draw_hidden_field('ext1', $kuaiqian_array['ext1']) .
                tep_draw_hidden_field('ext2', $kuaiqian_array['ext2']) .
                tep_draw_hidden_field('payType', $kuaiqian_array['payType']) .
                tep_draw_hidden_field('bankId', $kuaiqian_array['bankId']) .
                tep_draw_hidden_field('redoFlag', $kuaiqian_array['redoFlag']) .
                tep_draw_hidden_field('pid', $kuaiqian_array['pid']);

        return $process_button_string;
    }

    function before_process() {
        global $HTTP_POST_VARS, $order_logged;

        if (tep_session_is_registered('order_logged')) {
            if ($_REQUEST['payResult'] != '10' && $_REQUEST['payResult'] != '00') {
                tep_redirect(tep_href_link(FILENAME_CHECKOUT_PAYMENT, 'error_message=' . urlencode(MODULE_PAYMENT_KUAIQIAN_TEXT_ERROR_MESSAGE), 'SSL', true, false));
            }
        }
    }

    function after_process() {
        return false;
    }

    function link_to_payment($new_order_id) {
        global $order_logged;

        if (tep_session_is_registered('order_logged'))
            tep_session_unregister('order_logged');

        tep_session_register('order_logged');
        $order_logged = $new_order_id;
        require(DIR_WS_INCLUDES . 'modules/payment/kuaiqian/catalog/kuaiqian_splash.php');
        return;
    }

    function is_supported_currency($selected_currency) {
        return (in_array($selected_currency, $this->kuaiqianCurrencies) ? true : false);
    }

    function output_error() {
        return false;
    }

    function check() {
        if (!isset($this->_check)) {
            $payment_methods_select_sql = "	SELECT payment_methods_id
											FROM " . TABLE_PAYMENT_METHODS . " as pm
											WHERE pm.payment_methods_id = '" . (int) $this->payment_methods_id . "'";
            $payment_methods_result_sql = tep_db_query($payment_methods_select_sql);
            $this->_check = tep_db_num_rows($payment_methods_result_sql);
        }
        return $this->_check;
    }

    function load_pm_setting($language_id = 1, $pm_id = '') {
        $pm_setting_array = array();
        //load value from DB

        if (tep_not_null($pm_id)) {
            $payment_method_id = $pm_id;

            $payment_methods_parent_id_select_sql = "	SELECT payment_methods_parent_id 
														FROM " . TABLE_PAYMENT_METHODS . " 
														WHERE payment_methods_id = '" . (int) $pm_id . "'";
            $payment_methods_parent_id_result_sql = tep_db_query($payment_methods_parent_id_select_sql);
            $payment_methods_parent_id_row = tep_db_fetch_array($payment_methods_parent_id_result_sql);

            $payment_gateway_id = $payment_methods_parent_id_row['payment_methods_parent_id'];
        } else {
            $payment_method_id = $this->payment_methods_id;
            $payment_gateway_id = $this->payment_methods_parent_id;
        }

        $payment_configuration_info_select_sql = "	SELECT pci.payment_configuration_info_key, pcid.payment_configuration_info_value
													FROM " . TABLE_PAYMENT_CONFIGURATION_INFO . " as pci
													LEFT JOIN " . TABLE_PAYMENT_CONFIGURATION_INFO_DESCRIPTION . " as pcid 
														ON pci.payment_configuration_info_id = pcid.payment_configuration_info_id
															AND pcid.languages_id = '" . (int) $language_id . "'
													WHERE pci.payment_methods_id = '" . (int) $payment_method_id . "'
													ORDER BY pci.payment_configuration_info_sort_order";
        $payment_configuration_info_result_sql = tep_db_query($payment_configuration_info_select_sql);
        if (tep_db_num_rows($payment_configuration_info_result_sql)) {
            while ($payment_configuration_info_row = tep_db_fetch_array($payment_configuration_info_result_sql)) {
                $pm_setting_array[$payment_configuration_info_row['payment_configuration_info_key']] = $payment_configuration_info_row['payment_configuration_info_value'];
            }
        } else if ((int) $payment_gateway_id > 0) { //load value from PG
            $payment_configuration_info_select_sql = "	SELECT pci.payment_configuration_info_key, pcid.payment_configuration_info_value
														FROM " . TABLE_PAYMENT_CONFIGURATION_INFO . " as pci
														LEFT JOIN " . TABLE_PAYMENT_CONFIGURATION_INFO_DESCRIPTION . " as pcid 
															ON pci.payment_configuration_info_id = pcid.payment_configuration_info_id
																AND pcid.languages_id = '" . (int) $language_id . "'
														WHERE pci.payment_methods_id = '" . (int) $payment_gateway_id . "'
														ORDER BY pci.payment_configuration_info_sort_order";
            $payment_configuration_info_result_sql = tep_db_query($payment_configuration_info_select_sql);
            while ($payment_configuration_info_row = tep_db_fetch_array($payment_configuration_info_result_sql)) {
                $pm_setting_array[$payment_configuration_info_row['payment_configuration_info_key']] = $payment_configuration_info_row['payment_configuration_info_value'];
            }
        }

        //static value
        $pm_setting_array['MODULE_PAYMENT_KUAIQIAN_VERSION'] = "v2.0";
        $pm_setting_array['MODULE_PAYMENT_KUAIQIAN_LANGUAGE'] = "1";
        $pm_setting_array['MODULE_PAYMENT_KUAIQIAN_SIGNTYPE'] = "1";
        $pm_setting_array['MODULE_PAYMENT_KUAIQIAN_BGURL'] = (defined(FILENAME_KUAIQIANCALLBACK) ? tep_href_link(FILENAME_KUAIQIANCALLBACK) : '');

        return $pm_setting_array;
    }

    function get_merchant_account($selected_currency) {
        $payment_methods_instance_id = 0;
        $payment_gateway_instance_select_sql = "SELECT pmi.payment_methods_instance_id, pmi.payment_methods_instance_follow_default, pmi.currency_code 
				 								FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " as pmi
				 								WHERE pmi.payment_methods_id = '" . (int) $this->payment_methods_id . "'
				 								LIMIT 1";
        $payment_gateway_instance_result_sql = tep_db_query($payment_gateway_instance_select_sql);
        if (tep_db_num_rows($payment_gateway_instance_result_sql)) {
            $payment_gateway_instance_select_sql = "SELECT pmi.payment_methods_instance_id, pmi.payment_methods_instance_follow_default 
					 								FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " as pmi
					 								WHERE pmi.currency_code = '" . tep_db_input($selected_currency) . "' 
					 									AND pmi.payment_methods_id = '" . (int) $this->payment_methods_id . "'";
            $payment_gateway_instance_result_sql = tep_db_query($payment_gateway_instance_select_sql);
            if ($payment_gateway_instance_row = tep_db_fetch_array($payment_gateway_instance_result_sql)) {
                if ((int) $payment_gateway_instance_row['payment_methods_instance_follow_default'] > 0) { // Follow Default Currency Setting
                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_follow_default'];
                } else { // Has own setting
                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_id'];
                }
            }
        } else {
            $payment_gateway_instance_select_sql = "SELECT pmi.payment_methods_instance_id, pmi.payment_methods_instance_follow_default 
					 								FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " as pmi
					 								INNER JOIN " . TABLE_PAYMENT_METHODS . " as pm
					 									ON pm.payment_methods_parent_id = pmi.payment_methods_id
					 								WHERE pmi.currency_code = '" . tep_db_input($selected_currency) . "' 
					 									AND pm.payment_methods_id = '" . (int) $this->payment_methods_id . "'";
            $payment_gateway_instance_result_sql = tep_db_query($payment_gateway_instance_select_sql);
            if ($payment_gateway_instance_row = tep_db_fetch_array($payment_gateway_instance_result_sql)) {
                if ((int) $payment_gateway_instance_row['payment_methods_instance_follow_default'] > 0) {
                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_follow_default'];
                } else {
                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_id'];
                }
            }
        }
        $payment_gateway_instance_setting_select_sql = "SELECT pmis.payment_methods_instance_setting_key, pmis.payment_methods_instance_setting_value 
						 								FROM " . TABLE_PAYMENT_METHODS_INSTANCE_SETTING . " as pmis
						 								WHERE pmis.payment_methods_instance_id = '" . (int) $payment_methods_instance_id . "'";
        $payment_gateway_instance_setting_result_sql = tep_db_query($payment_gateway_instance_setting_select_sql);
        while ($payment_gateway_instance_setting_row = tep_db_fetch_array($payment_gateway_instance_setting_result_sql)) {
            switch ($payment_gateway_instance_setting_row['payment_methods_instance_setting_key']) {
                case 'MODULE_PAYMENT_KUAIQIAN_MERCHANTACCTID':
                    $this->merchantAcctId = $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];
                    break;
                case 'MODULE_PAYMENT_KUAIQIAN_KEY':
                    $this->key = $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];
                    break;
            }
        }
    }

    function install() {
        $install_configuration_flag = true;

        if ($this->payment_methods_id > 0) {
            if ($this->receive_status != 1) {
                $update_receive_status_sql_data = array("payment_methods_receive_status" => 1);
                tep_db_perform(TABLE_PAYMENT_METHODS, $update_receive_status_sql_data, 'update', "payment_methods_id = '" . (int) $this->payment_methods_id . "'");
            } else {
                $install_configuration_flag = false;
            }
        } else {
            $install_payment_gateway_data_sql = array('payment_methods_code' => $this->code,
                'payment_methods_send_status' => 0,
                'payment_methods_title' => $this->code,
                'payment_methods_types_id' => 0,
                'payment_methods_parent_id' => 0,
                'payment_methods_legend_color' => '#6E920D',
                'payment_methods_sort_order' => 6,
                'payment_methods_filename' => $this->filename,
                'payment_methods_logo' => '',
                'date_added' => 'now()',
                'last_modified' => 'now()',
                'payment_methods_receive_status' => 1,
                'payment_methods_receive_status_mode' => 0);
            tep_db_perform(TABLE_PAYMENT_METHODS, $install_payment_gateway_data_sql);
            $this->payment_methods_id = tep_db_insert_id();
        }

        if ($install_configuration_flag) {
            $install_key_array = array();
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Input Charset',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_KUAIQIAN_INPUTCHARSET',
                    'payment_configuration_info_description' => 'To choose the input charset, UTF-8, GBK or GB2312',
                    'payment_configuration_info_sort_order' => '100',
                    'set_function' => 'tep_cfg_key_select_option(array(\'1\'=>\'UTF-8\', \'2\'=>\'GBK\', \'3\'=>\'GB2312\'),',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => '1',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Set Order Status',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_KUAIQIAN_ORDER_STATUS_ID',
                    'payment_configuration_info_description' => 'Set the status of orders made with this payment module to this value',
                    'payment_configuration_info_sort_order' => '200',
                    'set_function' => 'tep_cfg_pull_down_order_statuses(',
                    'use_function' => 'tep_get_order_status_name',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => '1',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Set Order\'s "Processing" Status',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_KUAIQIAN_PROCESSING_STATUS_ID',
                    'payment_configuration_info_description' => 'Set the initial processing status of orders made with this payment module to this value',
                    'payment_configuration_info_sort_order' => '300',
                    'set_function' => 'tep_cfg_pull_down_order_statuses(',
                    'use_function' => 'tep_get_order_status_name',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => '7',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Payment Message',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_KUAIQIAN_MESSAGE',
                    'payment_configuration_info_description' => 'Payment message will show up during checkout process (kuaiqian)',
                    'payment_configuration_info_sort_order' => '400',
                    'set_function' => 'tep_cfg_textarea(',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'Thank you for shopping at OffGamers.com.',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Payment Email Message',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_KUAIQIAN_EMAIL_MESSAGE',
                    'payment_configuration_info_description' => 'This message will show up in order confirmation e-mail.',
                    'payment_configuration_info_sort_order' => '500',
                    'set_function' => 'tep_cfg_textarea(',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'Thank you for shopping at OffGamers.com.',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Confirm Complete (in days)',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_KUAIQIAN_CONFIRM_COMPLETE',
                    'payment_configuration_info_description' => 'The number of days in which an order made by this payment method is confirm completed',
                    'payment_configuration_info_sort_order' => '600',
                    'set_function' => 'NULL',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => '0',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Pay Type',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_KUAIQIAN_PAYTYPE',
                    'payment_configuration_info_description' => '	Pay Types:',
                    'payment_configuration_info_sort_order' => '700',
                    'set_function' => 'tep_cfg_key_select_option(array(\'00\'=>\'All transactions\', \'10\'=>\'Bank transaction only\', \'11\'=>\'Phone call transaction only\', \'12\'=>\'KuaiQian account transaction only\', \'13\'=>\'Offline payment only\'), ',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => '00',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Payer Contact Type',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_KUAIQIAN_PAYERCONTACTTYPE',
                    'payment_configuration_info_description' => 'Payer Contact Type. currently only support email only.',
                    'payment_configuration_info_sort_order' => '700',
                    'set_function' => 'tep_cfg_key_select_option(array(\'1\'=>\'Email\'), ',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => '1',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Require Address Information',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_KUAIQIAN_MANDATORY_ADDRESS_FIELD',
                    'payment_configuration_info_description' => 'Set address field as required info during checkout.',
                    'payment_configuration_info_sort_order' => '1400',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Customer Payment Info',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_KUAIQIAN_CUSTOMER_PAYMENT_INFO',
                    'payment_configuration_info_description' => 'Set to true if this Payment Method required customer input for Payment Information required customer payment info.',
                    'payment_configuration_info_sort_order' => '1410',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Require IC Information',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_KUAIQIAN_MANDATORY_IC_FIELD',
                    'payment_configuration_info_description' => 'Set IC field as required info during checkout.',
                    'payment_configuration_info_sort_order' => '1420',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Require Contact Information',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_KUAIQIAN_MANDATORY_CONTACT_FIELD',
                    'payment_configuration_info_description' => 'Set Contact field as required info during checkout.',
                    'payment_configuration_info_sort_order' => '1430',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );
            foreach ($install_key_array as $data) {
                tep_db_perform(TABLE_PAYMENT_CONFIGURATION_INFO, $data['info']);
                $payment_conf_id = tep_db_insert_id();

                $data['desc']['payment_configuration_info_id'] = (int) $payment_conf_id;
                tep_db_perform(TABLE_PAYMENT_CONFIGURATION_INFO_DESCRIPTION, $data['desc']);
            }
        }
        return 1;
    }

    function remove() {
        tep_db_query("delete from " . TABLE_CONFIGURATION . " where configuration_key in ('" . implode("', '", $this->keys()) . "')");
    }

    function keys() {
        return array('MODULE_PAYMENT_KUAIQIAN_STATUS',
            'MODULE_PAYMENT_KUAIQIAN_ID',
            'MODULE_PAYMENT_KUAIQIAN_MODE',
            'MODULE_PAYMENT_KUAIQIAN_SORT_ORDER',
            'MODULE_PAYMENT_KUAIQIAN_ORDER_STATUS_ID',
            'MODULE_PAYMENT_KUAIQIAN_PROCESSING_STATUS_ID',
            'MODULE_PAYMENT_KUAIQIAN_SECRET_WORD',
            'MODULE_PAYMENT_KUAIQIAN_ZONE',
            'MODULE_PAYMENT_KUAIQIAN_MESSAGE',
            'MODULE_PAYMENT_KUAIQIAN_CONFIRM_COMPLETE',
            'MODULE_PAYMENT_KUAIQIAN_EMAIL_MESSAGE',
            'MODULE_PAYMENT_KUAIQIAN_LEGEND_COLOUR');
    }

    function configuration_information_keys() {
        return array('MODULE_PAYMENT_KUAIQIAN_MODE',
            'MODULE_PAYMENT_KUAIQIAN_ORDER_STATUS_ID',
            'MODULE_PAYMENT_KUAIQIAN_PROCESSING_STATUS_ID',
            'MODULE_PAYMENT_KUAIQIAN_MESSAGE',
            'MODULE_PAYMENT_KUAIQIAN_EMAIL_MESSAGE',
            'MODULE_PAYMENT_KUAIQIAN_CONFIRM_COMPLETE',
            'MODULE_PAYMENT_KUAIQIAN_CUSTOMER_PAYMENT_INFO',
            'MODULE_PAYMENT_KUAIQIAN_MANDATORY_IC_FIELD',
            'MODULE_PAYMENT_KUAIQIAN_MANDATORY_CONTACT_FIELD');
    }

    function multi_lang_configuration_info_keys() {
        return array('MODULE_PAYMENT_KUAIQIAN_MESSAGE',
            'MODULE_PAYMENT_KUAIQIAN_EMAIL_MESSAGE');
    }

    function merchant_information_keys() {
        include_once(DIR_WS_CLASSES . "payment_methods.php");

        return array(
            'MODULE_PAYMENT_KUAIQIAN_MERCHANTACCTID' => array('field' => 'text', 'mapping' => 'MODULE_PAYMENT_KUAIQIAN_LNG_MERCHANTACCTID'),
            'MODULE_PAYMENT_KUAIQIAN_KEY' => array('field' => 'text', 'mapping' => 'MODULE_PAYMENT_KUAIQIAN_LNG_KEY'),
            'MODULE_PAYMENT_TAX' => array("field" => 'select', 'mapping' => "MODULE_PAYMENT_TAX", "select" => payment_methods::get_tax_option())
        );
    }

    function get_pg_id() {
        return $this->payment_methods_id;
    }

    function get_orders_payment_info_file() {
        return DIR_FS_CATALOG_MODULES . 'payment/kuaiqian/admin/orders.inc.php';
    }

    function get_orders_list_payment_info_file() {
        return DIR_FS_CATALOG_MODULES . 'payment/kuaiqian/admin/orders_list.inc.php';
    }

    function get_ipn_class_file() {
        return DIR_FS_CATALOG_MODULES . 'payment/kuaiqian/classes/kuaiqian_ipn_class.php';
    }

    function kuaiqian_error_code($pass_code) {
        switch ($pass_code) {
            case '00000' : return TEXT_INFO_UNKNOWN_ERROR;
                break;
            case '10001' : return TEXT_INFO_NOT_SUPPORTED_ENCODING;
                break;
            case '10002' : return TEXT_INFO_NOT_SUPPORTED_RETURN_TYPE;
                break;
            case '10003' : return TEXT_INFO_INVALID_BGURL;
                break;
            case '10004' : return TEXT_INFO_INVALID_BGURL;
                break;
            case '10005' : return TEXT_INFO_NOT_SUPPORTED_VERSION;
                break;
            case '10006' : return TEXT_INFO_MERCHANT_ID_NOT_EXISTING;
                break;
            case '10007' : return TEXT_INFO_INVALID_PAYEE;
                break;
            case '10008' : return TEXT_INFO_NOT_SUPPORTED_CONTACT_TYPE;
                break;
            case '10009' : return TEXT_INFO_INVALID_CONTACT_CONTENT;
                break;
            case '10010' : return TEXT_INFO_INVALID_ORDER_ID;
                break;
            case '10011' : return TEXT_INFO_INVALID_ORDER_AMOUNT;
                break;
            case '10012' : return TEXT_INFO_INVALID_ORDER_TIME;
                break;
            case '10013' : return TEXT_INFO_INVALID_PRODUCT_NAME;
                break;
            case '10014' : return TEXT_INFO_UNKNOWN_INVALID_QUANTITY;
                break;
            case '10015' : return TEXT_INFO_INVALID_PRODUCT_ID;
                break;
            case '10016' : return TEXT_INFO_INVALID_PRODUCT_DESCRIPTION;
                break;
            case '10017' : return TEXT_INFO_INVALID_EXT1;
                break;
            case '10018' : return TEXT_INFO_INVALID_EXT2;
                break;
            case '10019' : return TEXT_INFO_INVALID_SELECTED_PAYMENT_METHOD;
                break;
            case '10020' : return TEXT_INFO_INVALID_PAYMENT_METHOD_CODE;
                break;
            case '10021' : return TEXT_INFO_INVALID_BANK_ID;
                break;
            case '10022' : return TEXT_INFO_NOT_SUPPORTED_LANGUAGE;
                break;
            case '10023' : return TEXT_INFO_NOT_SUPPORTED_SIGN_TYPE;
                break;
            case '10024' : return TEXT_INFO_PERMISSION_DENIED_ON_ONLINE_TRANSACTION;
                break;
            case '10025' : return TEXT_INFO_PERMISSION_DENIED_ON_INTERNATIONAL_TRANSACTION;
                break;
            case '10026' : return TEXT_INFO_PERMISSION_DENIED_ON_MOBILE_TRANSACTION;
                break;
            case '10027' : return TEXT_INFO_INVALID_PID;
                break;
            case '10028' : return TEXT_INFO_INVALID_INTERNATION_PAYMENT_DATA;
                break;
            case '10029' : return TEXT_INFO_INVALID_SHENGZUO_PAYMENT_DATA;
                break;
            case '10030' : return TEXT_INFO_INVALID_AGENT_ACCOUNT_CODE;
                break;
            case '10031' : return TEXT_INFO_MERCHANT_YET_TO_OPEN_AGENT_GATEWAY;
                break;
            case '10032' : return TEXT_INFO_ORIGINAL_TRADE_NOT_EXISTING;
                break;
            case '10033' : return TEXT_INFO_INVALID_ORDER_AMOUNT;
                break;
            case '10034' : return TEXT_INFO_INVALID_TRANSACTION_FEE;
                break;
            case '10035' : return TEXT_INFO_TRANSACTION_FEED_EQUAL_OR_GREATER_THAN_ORDER_AMOUNT;
                break;
            case '10036' : return TEXT_INFO_INVALID_REPEAT_ORDER_ID_FLAG;
                break;
            case '10037' : return TEXT_INFO_REPEATED_ORDER_NOT_ALLOWED;
                break;
            case '20001' : return TEXT_INFO_INVALID_SIGN_MESSAGE;
                break;
            case '20002' : return TEXT_INFO_MERCHANT_ACCOUNT_FROZEN;
                break;
            case '20003' : return TEXT_INFO_MERCHANT_TRANSACTION_AMOUNT_OVER_LIMIT;
                break;
            case '20004' : return TEXT_INFO_INVALID_MERCHANT_BANK_SETTING;
                break;
            case '20005' : return TEXT_INFO_VOUCHER_NOT_ALLOWED;
                break;
            case '20006' : return TEXT_INFO_DENIED_MERCHANT_ACCOUNT_RECEIVE_PAYMENT;
                break;
            case '20007' : return TEXT_INFO_ACCOUNT_CANCEL;
                break;
            case '20008' : return TEXT_INFO_ORDER_AMOUNT_SMALLER_THAN_CHARGES;
                break;
            case '20009' : return TEXT_INFO_MERCHANT_FAILED_TO_DEAL_DIRECTLY_WITH_BANK;
                break;
            case '20010' : return TEXT_INFO_OVER_KUAIQIAN_LIMITED_PAYMENT_AMOUNT;
                break;
            case '20011' : return TEXT_INFO_OVER_KUAIQIAN_LIMITED_PAYMENT_OR_ORDER_AMOUNT;
                break;
            case '20012' : return TEXT_INFO_OVER_DAILY_LIMIT;
                break;
            case '20013' : return TEXT_INFO_OVER_MONTHLY_LIMIT;
                break;
            case '30001' : return TEXT_INFO_NOT_SUPPORTED_BANK;
                break;
            case '30002' : return TEXT_INFO_NOT_SUPPORTED_OFFLINE;
                break;
            case '30003' : return TEXT_INFO_NOT_SUPPORTED_MOBILE_BANK;
                break;
            case '30004' : return TEXT_INFO_ORDER_PAYMENT_SUCCESS;
                break;
            case '30005' : return TEXT_INFO_FILL_EMAIL_ADDRESS;
                break;
            case '50001' : return TEXT_INFO_INVALID_VERIFICATION_CODE;
                break;
            case '50002' : return TEXT_INFO_ACCOUNT_NOT_EXISTING;
                break;
            case '50003' : return TEXT_INFO_ACCOUNT_FROZEN;
                break;
            case '50004' : return TEXT_INFO_ACCOUNT_LOCK_BY_MULTIPLE_LOGIN;
                break;
            case '50005' : return TEXT_INFO_SAME_PAYER_AND_PAYEE;
                break;
            case '50006' : return TEXT_INFO_OPERATION_EXPIRED;
                break;
            case '50007' : return TEXT_INFO_INVALID_ACCOUNT;
                break;
            case '50008' : return TEXT_INFO_INVALID_PASSWORD;
                break;
            case '50009' : return TEXT_INFO_NOT_ENOUGH_CREDIT;
                break;
            case '50010' : return TEXT_INFO_PAYMENT_PERMISSION_DENIED;
                break;
            case '50011' : return TEXT_INFO_PAYMENT_DENIED_TO_PERSONAL_MEMBER;
                break;
            case '50012' : return TEXT_INFO_INVALID_REEXAMINING;
                break;
            case '50013' : return TEXT_INFO_KUAIQIAN_CREDIT_REPORTING_LOST;
                break;
            case '50014' : return TEXT_INFO_INVALID_KUAIQIAN_CREDIT_DATA;
                break;
            case '50015' : return TEXT_INFO_CONTACT_SUPPORT_FOR_UNKNOWN_ERROR;
                break;
            case '50016' : return TEXT_INFO_PLEASE_USE_DEFAULT_LOGIN;
                break;
            case '50017' : return TEXT_INFO_LOGIN_FAILED_MORE_THAN_THREE_TIME;
                break;
            case '60001' : return TEXT_INFO_INVALID_CREDIT_TYPE;
                break;
            case '60002' : return TEXT_INFO_EMPTY_ORDER_ID;
                break;
            case '60003' : return TEXT_INFO_INVALID_ORDER_CHARACTER;
                break;
            case '60004' : return TEXT_INFO_TRASACTION_AMOUNT_NOT_MATCH;
                break;
            case '60005' : return TEXT_INFO_INVALID_TRANSACTION_AMOUNT_ON_INTEGER;
                break;
            case '70001' : return TEXT_INFO_VOUCHER_NOT_SUPPORTED_ON_ACCOUNT;
                break;
            case '70002' : return TEXT_INFO_INVALID_VOUCHER;
                break;
            case '70003' : return TEXT_INFO_CONTACT_INVALID_VERIFICATION_CODE;
                break;
            default: return TEXT_INFO_UNKNOWN_ERROR;
                break;
        }
    }

}

?>