<?php

define('TABLE_HEADING_KUAIQIAN_DATE', 'Date');
define('TABLE_HEADING_KUAIQIAN_DESCRIPTION', 'Description');

define('ENTRY_KUAIQIAN_BANID', 'Bank ID');
define('ENTRY_KUAIQIAN_BANKDEALID', 'Bank Deal ID');
define('ENTRY_KUAIQIAN_ORDERAMOUNT', 'Order Amount');
define('ENTRY_KUAIQIAN_PAYAMOUNT', 'Pay Amount');
define('ENTRY_KUAIQIAN_BANKDEALID', 'Bank Deal ID');
define('ENTRY_KUAIQIAN_FEE', 'Fee');
define('ENTRY_KUAIQIAN_PAYRESULT', 'Pay Result');
define('ENTRY_KUAIQIAN_ERRCODE', 'Error Code');
define('ENTRY_KUAIQIAN_VERSION', 'Version');
define('ENTRY_KUAIQIAN_PAY_TYPE', 'Pay Type');
define('ENTRY_KUAIQIAN_DEAL_ID', 'Deal ID');
define('ENTRY_KUAIQIAN_DEAL_TIME', 'Deal Time');
define('ENTRY_KUAIQIAN_ORDER_CURRENCY', 'Order Currency');
define('ENTRY_KUAIQIAN_PAY_CURRENCY', 'Pay Currency');

define('ENTRY_KUAIQIAN_NO_PAYMENT_INFO', 'No Payment Info');

//key langauge
define('MODULE_PAYMENT_KUAIQIAN_LNG_MERCHANTACCTID', 'Merchant Account ID');
define('MODULE_PAYMENT_KUAIQIAN_LNG_KEY', 'Key');
define('MODULE_PAYMENT_TAX', 'Tax');

define('TEXT_INFO_CURRENCY_NOT_FROM_KUAIQIAN', 'Currently not from KuaiQian');

define('TEXT_INFO_UNKNOWN_ERROR', '未知错误');
define('TEXT_INFO_NOT_SUPPORTED_ENCODING', '不支持的字符编码格式,系统支持的字符编码格式为 1.UTF-8,2.GBK,3.GB2312');
define('TEXT_INFO_NOT_SUPPORTED_RETURN_TYPE', '不支持的返回类型,系统支持的返回类型为 1.页面返回,2.后台返回,3.同时支持页面和后台返回');
define('TEXT_INFO_INVALID_BGURL', '页面返回地址和后台返回地址不能同时为空,请使用符合 URL 规则的 http 或者https地址'); // 
define('TEXT_INFO_NOT_SUPPORTED_VERSION', '不支持的网关接口版本号,目前系统支持的版本号为 v2.0');
define('TEXT_INFO_MERCHANT_ID_NOT_EXISTING', '商户号不存在');
define('TEXT_INFO_INVALID_PAYEE', '付款方用户名不正确');
define('TEXT_INFO_NOT_SUPPORTED_CONTACT_TYPE', '不支持的付款方联系方式,系统支持的联系方式为 1.电子邮件,2.电话.当联系内容不为空时联系方式不能为空.');
define('TEXT_INFO_INVALID_CONTACT_CONTENT', '付款方的联系内容不正确,请输入合法的联系地址');
define('TEXT_INFO_INVALID_ORDER_ID', '订单号不正确,系统只支持以字母,数字组合的订单号,最大长度不能超过30');
define('TEXT_INFO_INVALID_ORDER_AMOUNT', '订单金额不正确,请输入以分为单位的金额');
define('TEXT_INFO_INVALID_ORDER_TIME', '订单提交时间不正确,请输入以yyyyMMddhhmmss格式的时间字符串');
define('TEXT_INFO_INVALID_PRODUCT_NAME', '商品名称不正确');
define('TEXT_INFO_UNKNOWN_INVALID_QUANTITY', '商品数量不正确');
define('TEXT_INFO_INVALID_PRODUCT_ID', '商品 ID 不正确');
define('TEXT_INFO_INVALID_PRODUCT_DESCRIPTION', '商品的描述不正确');
define('TEXT_INFO_INVALID_EXT1', '扩展参数一不正确');
define('TEXT_INFO_INVALID_EXT2', '扩展参数二不正确');
define('TEXT_INFO_INVALID_SELECTED_PAYMENT_METHOD', '指定的支付方式不正确');
define('TEXT_INFO_INVALID_PAYMENT_METHOD_CODE', '指定的支付服务代码不正确');
define('TEXT_INFO_INVALID_BANK_ID', '指定的银行 ID不正确');
define('TEXT_INFO_NOT_SUPPORTED_LANGUAGE', '不支持的语言类型,系统支持的语言为 1.中文,2.英文');
define('TEXT_INFO_NOT_SUPPORTED_SIGN_TYPE', '不支持的签名类型,系统支持的签名类型为 1.MD5');
define('TEXT_INFO_PERMISSION_DENIED_ON_ONLINE_TRANSACTION', '商户未开通人民币网关');
define('TEXT_INFO_PERMISSION_DENIED_ON_INTERNATIONAL_TRANSACTION', '商户未开通国际卡人民币网关');
define('TEXT_INFO_PERMISSION_DENIED_ON_MOBILE_TRANSACTION', '商户未开通电话支付人民币网关');
define('TEXT_INFO_INVALID_PID', '不正确的 pid 值');
define('TEXT_INFO_INVALID_INTERNATION_PAYMENT_DATA', '不正确的国际卡支付参数,组合支付方式和支付方式必须为国际卡对应的参数');
define('TEXT_INFO_INVALID_SHENGZUO_PAYMENT_DATA', '不正确的神州行支付参数,组合支付方式和支付方式必须为神州行支付对应的参数');
define('TEXT_INFO_INVALID_AGENT_ACCOUNT_CODE', '不正确的代理商帐户代码');
define('TEXT_INFO_MERCHANT_YET_TO_OPEN_AGENT_GATEWAY', '商户未开通代理网关');
define('TEXT_INFO_ORIGINAL_TRADE_NOT_EXISTING', '原始交易不存在');
define('TEXT_INFO_INVALID_TRANSACTION_FEE', '手续费金额不正确,请输入以元为单位的金额,最多允许两位小数');
define('TEXT_INFO_TRANSACTION_FEED_EQUAL_OR_GREATER_THAN_ORDER_AMOUNT', '手续费总额大于或等于订单金额');
define('TEXT_INFO_INVALID_REPEAT_ORDER_ID_FLAG', '同一订单号禁止重复提交标志不正确');
define('TEXT_INFO_REPEATED_ORDER_NOT_ALLOWED', '对不起，该订单不允许重复提交，请重新下订单提交!');
define('TEXT_INFO_INVALID_SIGN_MESSAGE', '订单信息的签名内容不正确');
define('TEXT_INFO_MERCHANT_ACCOUNT_FROZEN', '商户账号已被冻结');
define('TEXT_INFO_MERCHANT_TRANSACTION_AMOUNT_OVER_LIMIT', '商户交易金额已超过限制');
define('TEXT_INFO_INVALID_MERCHANT_BANK_SETTING', '商户制定的银行直连参数不正确');
define('TEXT_INFO_VOUCHER_NOT_ALLOWED', '不能使用优惠券');
define('TEXT_INFO_DENIED_MERCHANT_ACCOUNT_RECEIVE_PAYMENT', '商户账户不允许收款');
define('TEXT_INFO_ACCOUNT_CANCEL', '账户已注销');
define('TEXT_INFO_ORDER_AMOUNT_SMALLER_THAN_CHARGES', '订单金额小于支付手续费,不能支付');
define('TEXT_INFO_MERCHANT_FAILED_TO_DEAL_DIRECTLY_WITH_BANK', '商户不允许银行直连,可能商户没有对网关定制或者定制中没有选择银行直连,请联系快钱客服');
define('TEXT_INFO_OVER_KUAIQIAN_LIMITED_PAYMENT_AMOUNT', '您通过快钱向此商户的支付金额超过支付限额,请联系快钱客服');
define('TEXT_INFO_OVER_KUAIQIAN_LIMITED_PAYMENT_OR_ORDER_AMOUNT', '您通过快钱向此商户的支付金额超过单笔订单的支付限额,请联系快钱客服');
define('TEXT_INFO_OVER_DAILY_LIMIT', '您通过快钱向此商户的支付金额超过单日的支付总限额,请联系快钱客服');
define('TEXT_INFO_OVER_MONTHLY_LIMIT', '您通过快钱向此商户的支付金额超过单月的支付总限额,请联系快钱客服');
define('TEXT_INFO_NOT_SUPPORTED_BANK', '银行不可用');
define('TEXT_INFO_NOT_SUPPORTED_OFFLINE', '线下支付不可用');
define('TEXT_INFO_NOT_SUPPORTED_MOBILE_BANK', '电话银行不可用');
define('TEXT_INFO_ORDER_PAYMENT_SUCCESS', '订单已支付成功,请勿重新支付');
define('TEXT_INFO_FILL_EMAIL_ADDRESS', '请填写您的电子邮箱');
define('TEXT_INFO_INVALID_VERIFICATION_CODE', '验证码不正确');
define('TEXT_INFO_ACCOUNT_NOT_EXISTING', '用户不存在');
define('TEXT_INFO_ACCOUNT_FROZEN', '用户被冻结');
define('TEXT_INFO_ACCOUNT_LOCK_BY_MULTIPLE_LOGIN', '登录次数过多,用户已被锁定');
define('TEXT_INFO_SAME_PAYER_AND_PAYEE', '付款人不能和收款人相同');
define('TEXT_INFO_OPERATION_EXPIRED', '你的操作已超时，请重新提交');
define('TEXT_INFO_INVALID_ACCOUNT', '账户不正确');
define('TEXT_INFO_INVALID_PASSWORD', '密码不正确');
define('TEXT_INFO_NOT_ENOUGH_CREDIT', '余额不足,用户已被锁定');
define('TEXT_INFO_PAYMENT_PERMISSION_DENIED', '没有付款权限');
define('TEXT_INFO_PAYMENT_DENIED_TO_PERSONAL_MEMBER', '不能向个人会员付款');
define('TEXT_INFO_INVALID_REEXAMINING', '复核错误');
define('TEXT_INFO_KUAIQIAN_CREDIT_REPORTING_LOST', '您的快钱盾已挂失,无法登录.您可以通过取消挂失或更换后再登录');
define('TEXT_INFO_INVALID_KUAIQIAN_CREDIT_DATA', '您输入的快钱盾数据不正确');
define('TEXT_INFO_CONTACT_SUPPORT_FOR_UNKNOWN_ERROR', '使用快钱盾遇到未知错误，请联系快钱客服');
define('TEXT_INFO_TRASACTION_AMOUNT_NOT_MATCH', '该笔交易金额格式不正确,必须为整数或者小数');
define('TEXT_INFO_INVALID_TRANSACTION_AMOUNT_ON_INTEGER', '该笔交易金额格式不正确,必须为整数');
define('TEXT_INFO_VOUCHER_NOT_SUPPORTED_ON_ACCOUNT', '您输入的用户不能使用此优惠券');
define('TEXT_INFO_INVALID_VOUCHER', '您尝试使用优惠错误次数超过 3次,请与快钱客服联系');
define('TEXT_INFO_CONTACT_INVALID_VERIFICATION_CODE', '您验证优惠券的用户错误次数超过 3次,请与快钱客服联系');
define('TEXT_INFO_PLEASE_USE_DEFAULT_LOGIN', '请使用您的默认用户名登录');
define('TEXT_INFO_LOGIN_FAILED_MORE_THAN_THREE_TIME', '复核重试次数超过 3 次,您不能重新复核');
define('TEXT_INFO_INVALID_CREDIT_TYPE', '货币种类不正确! 提示 1：人民币网关；3：预付费卡网关.');
define('TEXT_INFO_EMPTY_ORDER_ID', '定单号不能为空,不能超过 50 位长度');
define('TEXT_INFO_INVALID_ORDER_CHARACTER', '定单号只能包含数字或字母以及中划线和下划线');
?>