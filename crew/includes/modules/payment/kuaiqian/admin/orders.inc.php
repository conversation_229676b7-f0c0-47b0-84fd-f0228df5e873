﻿<?
/*
  	$Id: orders.inc.php,v 1.6 2009/02/16 11:12:06 boonhock Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2005 SKC Venture
  	
  	Released under the GNU General Public License
*/

$kuaiqian_payment_status_array = array(	'10' => 'Successful',
										'11' => 'Unsuccessful',
										'00' => 'Order\'s Application Successed',
										'01' => 'Order\'s Application Fail');

$kuaiqian_deal_type_array = array(	'00' => 'All payment Types',
									'10' => 'Banks Payment Types',
									'11' => 'Mobile/Phone Payment Types',
									'12' => 'KuaiQian Payment',
									'13' => 'Offline');

$kuaiqian_trans_info_select_sql = "	SELECT * 
									FROM " . TABLE_KUAIQIAN . "
									WHERE orders_id = '" . (int)$oID  . "'";
$kuaiqian_trans_info_result_sql = tep_db_query($kuaiqian_trans_info_select_sql);

$kuaiqian_trans_history_select_sql = "	SELECT * 
										FROM " . TABLE_KUAIQIAN_PAYMENT_STATUS_HISTORY . " 
										WHERE orders_id='" . (int)$oID . "'";
$kuaiqian_trans_history_result_sql= tep_db_query($kuaiqian_trans_history_select_sql);

include_once(DIR_FS_CATALOG_MODULES . 'payment/kuaiqian/admin/languages/'.$language.'/kuaiqian.lng.php');

?>
<table border="0" width="100%" cellspacing="0" cellpadding="2">
<? ob_start(); 

?>
	<tr>
		<td class="main">
      		<table border="0" width="100%" cellspacing="0" cellpadding="2">
      			<tr>
      				<td class="main" width="12%" nowrap><b><?=ENTRY_PAYMENT_METHOD?></b></td>
      				<td class="main">
      					<div>
      						<? if((int)$order->info['payment_methods_id']>0) { ?><div class="<?=((int)$payment_info_array[$order->info['payment_methods_id']]->confirm_complete_days>0?'rp_icon':'nrp_icon')?>"><b><?=((int)$payment_info_array[$order->info['payment_methods_id']]->confirm_complete_days>0?'RP':'NRP')?></b></div> <? } ?>
      						<div style="width:30px; height:15px; background-color:<?=$payment_module_info['display_colour']?>; float:left; vertical-align:top; text-align:center;"></div>
      						<div style="vertical-align: top">
      							&nbsp;&nbsp;
<?
	if ($view_payment_details_permission) {
		if (tep_not_null($last_payment_method) && $last_payment_method['payment_methods_id'] != $order->info['payment_methods_id']) {
			echo '<span class="redIndicator">'.(tep_not_null($payment_module_info['payment_methods_parent_title'])?$payment_module_info['payment_methods_parent_title'] . ' - ' :'') . $payment_module_info['payment_methods_title'].'</span>';
		} else {
			echo (tep_not_null($payment_module_info['payment_methods_parent_title'])?$payment_module_info['payment_methods_parent_title'] . ' - ' :'') . $payment_module_info['payment_methods_title'];
		}
		
		if (tep_not_null($last_payment_method) && $last_payment_method['payment_methods_id'] != $order->info['payment_methods_id']) {
			echo '&nbsp;&nbsp;<span class="redIndicator">(Last Payment Method: ' . (tep_not_null($last_payment_method['parent_payment_methods_title'])? $last_payment_method['parent_payment_methods_title'] . ' - ' : '' ) . $last_payment_method['payment_methods_title'] . ')</span>';
		}
	} else {
		echo (tep_not_null($payment_module_info['payment_methods_parent_title'])?$payment_module_info['payment_methods_parent_title'] . ' - ' :'') . $payment_module_info['payment_methods_title'];
	}
?>
							</div>
      					</div>
      				</td>
				</tr>
			</table>
		</td>
	</tr>
<?
$payment_method_title_info = ob_get_contents();
ob_end_clean();

if (tep_db_num_rows($kuaiqian_trans_info_result_sql) || tep_db_num_rows($kuaiqian_trans_history_result_sql)) {
	echo $payment_method_title_info;
	if (!$view_payment_details_permission) {
		;
	} else {
?>
  	<tr>
    	<td class="main">
      		<table border="0" width="100%" cellspacing="0" cellpadding="2">
      			<tr valign="top">
<?
		$kuaiqian_trans_found = false;
		if ($kuaiqian_trans_info_row = tep_db_fetch_array($kuaiqian_trans_info_result_sql)) {
			$kuaiqian_trans_found = true;
?>
        			<td width="25%">
              			<table border="0" cellspacing="0" cellpadding="2">
							<tr>
								<td class="main" valign="top" nowrap><b><?=ENTRY_KUAIQIAN_VERSION?></b>&nbsp;</td>
			                	<td class="main" valign="top" nowrap><?=$kuaiqian_trans_info_row["kuaiqian_version"]?></td>
							</tr>
							<tr>
								<td class="main" valign="top" nowrap><b><?=ENTRY_KUAIQIAN_PAY_TYPE?></b>&nbsp;</td>
			                	<td class="main" valign="top" nowrap><?=(isset($kuaiqian_deal_type_array[$kuaiqian_trans_info_row["kuaiqian_pay_type"]])?$kuaiqian_deal_type_array[$kuaiqian_trans_info_row["kuaiqian_pay_type"]]:'')?></td>
							</tr>
							<tr>
								<td class="main" valign="top" nowrap><b><?=ENTRY_KUAIQIAN_DEAL_ID?></b>&nbsp;</td>
			                	<td class="main" valign="top" nowrap><?=$kuaiqian_trans_info_row["kuaiqian_deal_id"]?></td>
							</tr>
							<tr>
								<td class="main" valign="top" nowrap><b><?=ENTRY_KUAIQIAN_DEAL_TIME?></b>&nbsp;</td>
			                	<td class="main" valign="top" nowrap><?=$kuaiqian_trans_info_row["kuaiqian_deal_time"]?></td>
							</tr>
							<tr>
								<td class="main" valign="top" nowrap><b><?=ENTRY_KUAIQIAN_BANID?></b>&nbsp;</td>
			                	<td class="main" valign="top" nowrap><?=$kuaiqian_trans_info_row["kuaiqian_bank_id"]?></td>
							</tr>
							<tr>
								<td class="main" valign="top" nowrap><b><?=ENTRY_KUAIQIAN_BANKDEALID?></b>&nbsp;</td>
			                	<td class="main" valign="top" nowrap><?=$kuaiqian_trans_info_row["kuaiqian_bank_deal_id"]?></td>
							</tr>
							<tr>
								<td class="main" valign="top" nowrap><b><?=ENTRY_KUAIQIAN_PAYRESULT?></b>&nbsp;</td>
			                	<td class="main" valign="top" nowrap><?=(isset($kuaiqian_payment_status_array[$kuaiqian_trans_info_row["kuaiqian_pay_result"]])?$kuaiqian_payment_status_array[$kuaiqian_trans_info_row["kuaiqian_pay_result"]]:'')?></td>
							</tr>
							<tr>
								<td class="main" valign="top" nowrap><b><?=ENTRY_KUAIQIAN_ERRCODE?></b>&nbsp;</td>
			                	<td class="main" valign="top" nowrap><?=(tep_not_null($kuaiqian_trans_info_row["kuaiqian_err_code"])?$payment_info_array[$order->info['payment_methods_id']]->pm_object->kuaiqian_error_code($kuaiqian_trans_info_row["kuaiqian_err_code"]):'')?></td>
							</tr>
              			</table>
        			</td>
<?
		}
?>
        			<td>
              			<table border="0" cellspacing="0" cellpadding="2">
              				<tr>
                				<td class="main" colspan="2" nowrap>
                					<table border="1" cellspacing="0" cellpadding="2">
        								<tr>
                							<td class="smallText" nowrap><b><?=TABLE_HEADING_KUAIQIAN_DATE?></b></td>
                							<td class="smallText" nowrap><b><?=TABLE_HEADING_KUAIQIAN_DESCRIPTION?></b></td>
                						</tr>
<?
		while ($kuaiqian_trans_history_row = tep_db_fetch_array($kuaiqian_trans_history_result_sql)) {
        	echo ' 						<tr>
                							<td class="smallText" nowrap>'.$kuaiqian_trans_history_row['kuaiqian_date'].'</td>
                							<td class="smallText" nowrap>';
            									echo $kuaiqian_trans_history_row['kuaiqian_description'];
            echo '							</td>
            							</tr>';
     	}
?>
                					</table>
                				</td>
                			</tr>
              			</table>
              		</td>
<?
		if ($kuaiqian_trans_found) {
?>
        			<td>
        				<table>
							<tr>
								<td class="main" valign="top" nowrap><b><?=ENTRY_KUAIQIAN_ORDER_CURRENCY?></b>&nbsp;</td>
			                	<td class="main" valign="top" nowrap>
<?
			    					if (strtolower($kuaiqian_trans_info_row["kuaiqian_order_currency"]) == strtolower($order->info['currency'])) {
			    						echo $kuaiqian_trans_info_row['kuaiqian_order_currency'];
			    					} else {
			    						echo '<span class="redIndicator">'.$kuaiqian_trans_info_row["kuaiqian_order_currency"].'<br><span class="smallText">Does not match purchase currency.</span></span>';
			    					}
									echo '('.TEXT_INFO_CURRENCY_NOT_FROM_KUAIQIAN.')';			                		
?>
			                	</td>
							</tr>
							<tr>
								<td class="main" valign="top" nowrap><b><?=ENTRY_KUAIQIAN_ORDERAMOUNT?></b>&nbsp;</td>
			                	<td class="main" valign="top" nowrap><?
			    					$gross_display_text = number_format($kuaiqian_trans_info_row["kuaiqian_order_amount"], $currencies->currencies[$order->info["currency"]]['decimal_places'], $currencies->currencies[$order->info["currency"]]['decimal_point'], $currencies->currencies[$order->info["currency"]]['thousands_point']);
			    					if ($currencies->currencies[$order->info["currency"]]['symbol_left'].$gross_display_text.$currencies->currencies[$order->info["currency"]]['symbol_right'] != strip_tags($order->order_totals['ot_total']['text']) ) {
			    						$gross_display_text = '<span class="redIndicator">'.$gross_display_text.'<br><span class="smallText">Does not match purchase amount.</span></span>';
			    					}
			    					echo $gross_display_text;
			    				?>
							</tr>
							<tr>
								<td class="main" valign="top" nowrap><b><?=ENTRY_KUAIQIAN_PAY_CURRENCY?></b>&nbsp;</td>
			                	<td class="main" valign="top" nowrap>
<?
			    					if (strtolower($kuaiqian_trans_info_row["kuaiqian_pay_currency"]) == strtolower($order->info['currency'])) {
			    						echo $kuaiqian_trans_info_row['kuaiqian_pay_currency'];
			    					} else {
			    						echo '<span class="redIndicator">'.$kuaiqian_trans_info_row["kuaiqian_pay_currency"].'<br><span class="smallText">Does not match purchase currency.</span></span>';
			    					}
									echo '('.TEXT_INFO_CURRENCY_NOT_FROM_KUAIQIAN.')';			                		
?>
			                	</td>
							</tr>
							<tr>
								<td class="main" valign="top" nowrap><b><?=ENTRY_KUAIQIAN_PAYAMOUNT?></b>&nbsp;</td>
			                	<td class="main" valign="top" nowrap><?
			    					$gross_display_text = number_format($kuaiqian_trans_info_row["kuaiqian_pay_amount"], $currencies->currencies[$order->info["currency"]]['decimal_places'], $currencies->currencies[$order->info["currency"]]['decimal_point'], $currencies->currencies[$order->info["currency"]]['thousands_point']);
			    					if ($currencies->currencies[$order->info["currency"]]['symbol_left'].$gross_display_text.$currencies->currencies[$order->info["currency"]]['symbol_right'] != strip_tags($order->order_totals['ot_total']['text']) ) {
			    						$gross_display_text = '<span class="redIndicator">'.$gross_display_text.'<br><span class="smallText">Does not match purchase amount.</span></span>';
			    					}
			    					echo $gross_display_text;
			    				?>
			    				</td>
							</tr>
							<tr>
								<td class="main" valign="top" nowrap><b><?=ENTRY_KUAIQIAN_FEE?></b>&nbsp;</td>
			                	<td class="main" valign="top" nowrap><?=$kuaiqian_trans_info_row["kuaiqian_fee"]?></td>
							</tr>	
        				</table>
        			</td>
<?
	}
?>
      			</tr>
      		</table>
   		</td>
  	</tr>
<?
	}
} else {
	echo $payment_method_title_info;
}
?>
</table>