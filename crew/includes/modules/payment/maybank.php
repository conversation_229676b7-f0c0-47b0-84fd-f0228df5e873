<?php

class maybank {

    var $code, $title, $description, $enabled, $sort_order;
    var $check_trans_status_flag, $preferred, $payee_code, $user_id, $user_password, $maybankCurrencies, $comment;

    function maybank($pm_id = '') {
        global $order, $languages_id, $default_languages_id, $currency, $defCurr;
        global $customer_country_id, $amount_decimal;

        $this->code = 'maybank';
        $this->filename = 'maybank.php';
        $this->title = $this->code;
        $this->preferred = true;
        $this->auto_cancel_period = 60; // In minutes
        $this->order_processing_status = 7;
        $this->connect_via_proxy = false;
        $this->force_to_checkoutprocess = true;
        $this->has_ipn = true;
        $this->amount_decimal = 2;

        $this->check_trans_status_flag = false;

        $payment_methods_select_sql = "	SELECT payment_methods_parent_id, payment_methods_code,
                                            payment_methods_types_id,
                                            payment_methods_receive_status_mode, payment_methods_id,
                                            payment_methods_title, payment_methods_sort_order,
                                            payment_methods_receive_status, payment_methods_legend_color,
                                            payment_methods_logo
                                        FROM " . TABLE_PAYMENT_METHODS . "
                                        WHERE 1 ";
        if ((int) $pm_id > 0) {
            $payment_methods_select_sql .= "	AND payment_methods_id = '" . (int) $pm_id . "'";
        } else {
            $payment_methods_select_sql .= "	AND payment_methods_filename = '" . tep_db_input($this->filename) . "'";
        }

        $payment_methods_result_sql = tep_db_query($payment_methods_select_sql);
        if ($payment_methods_row = tep_db_fetch_array($payment_methods_result_sql)) {
            $pm_desc_title_select_sql = "   SELECT payment_methods_description_title
                                            FROM " . TABLE_PAYMENT_METHODS_DESCRIPTION . "
                                            WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "'
                                                AND (IF (languages_id = '" . tep_db_input($languages_id) . "', 1, IF ((	SELECT COUNT(payment_methods_id) > 0
                                            FROM " . TABLE_PAYMENT_METHODS_DESCRIPTION . "
                                            WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "'
                                                AND languages_id = '" . (int) $languages_id . "'), 0, languages_id = 1)))";

            $pm_desc_title_result_sql = tep_db_query($pm_desc_title_select_sql);
            $pm_desc_title_row = tep_db_fetch_array($pm_desc_title_result_sql);

            $this->enabled = $payment_methods_row['payment_methods_receive_status_mode'];
            $this->payment_methods_id = (int) $payment_methods_row['payment_methods_id'];
            $this->payment_methods_parent_id = (int) $payment_methods_row['payment_methods_parent_id'];
            $this->code = $payment_methods_row['payment_methods_code'];
            $this->title = $payment_methods_row['payment_methods_title'];

            $this->display_title = $pm_desc_title_row['payment_methods_description_title'];
            $this->description = $this->display_title;
            $this->sort_order = $payment_methods_row['payment_methods_sort_order'];
            $this->legend_display_colour = $payment_methods_row['payment_methods_legend_color'];

            // Maybank only accept MYR currency
            $this->maybankCurrencies = $this->get_support_currencies($this->payment_methods_id);

            $configuration_setting_array = $this->load_pm_setting();
            $this->order_status_id = $configuration_setting_array['MODULE_PAYMENT_MAYBANK_ORDER_STATUS_ID'];
            $this->processing_status_id = $configuration_setting_array['MODULE_PAYMENT_MAYBANK_PROCESSING_STATUS_ID'];
            $this->confirm_complete_days = $configuration_setting_array['MODULE_PAYMENT_MAYBANK_CONFIRM_COMPLETE'];
            $this->require_address_information = $configuration_setting_array['MODULE_PAYMENT_MAYBANK_MANDATORY_ADDRESS_FIELD'];
            $this->require_ic_information = $configuration_setting_array['MODULE_PAYMENT_MAYBANK_MANDATORY_IC_FIELD'];
            $this->require_contact_information = $configuration_setting_array['MODULE_PAYMENT_MAYBANK_MANDATORY_CONTACT_FIELD'];

            $this->customer_payment_info = $configuration_setting_array['MODULE_PAYMENT_MAYBANK_CUSTOMER_PAYMENT_INFO'];

            $this->message = $configuration_setting_array['MODULE_PAYMENT_MAYBANK_MESSAGE'];
            $this->email_message = $configuration_setting_array['MODULE_PAYMENT_MAYBANK_EMAIL_MESSAGE'];
            $this->email_footer = str_replace(array("<br>", "<BR>", "\r\n"), array("\n", "\n", ''), $this->email_message);

            $this->order_processing_status = ((int) $this->processing_status_id > 0 ? $this->processing_status_id : 7 );
            $this->order_status = ((int) $this->order_status_id > 0 ? $this->order_status_id : DEFAULT_ORDERS_STATUS_ID );

            $this->form_action_url = 'https://www.maybank2u.com.my/mbb/m2u/m9006_enc/m2uMerchantLogin.do'; // Maybank ePayment Production v1.7 2015
//            $this->form_action_url = 'https://m2umobilesit.maybank.com.my/mbb/m2u/m9006_enc/m2uMerchantLogin.do'; // Maybank ePayment Test Mode v1.7 2015
            $this->form_status_action_url = 'https://www.maybank2u.com.my/eQuery/enoteservices/XmlQuery.jws?wsdl';
        }
    }

    function javascript_validation() {
        return false;
    }

    function selection() {
        global $languages_id, $default_languages_id;

        $selection_array = array();

        $payment_methods_select_sql = "	SELECT payment_methods_id, payment_methods_code, payment_methods_types_id, payment_methods_logo, payment_methods_receive_status_mode
                                        FROM " . TABLE_PAYMENT_METHODS . "
                                        WHERE payment_methods_receive_status = 1
                                            AND payment_methods_receive_status_mode <> 0
                                            AND payment_methods_parent_id = '" . (int) $this->payment_methods_id . "'
                                        ORDER BY payment_methods_sort_order";
        $payment_methods_result_sql = tep_db_query($payment_methods_select_sql);
        while ($payment_methods_row = tep_db_fetch_array($payment_methods_result_sql)) {
            $pm_desc_title_select_sql = "   SELECT payment_methods_description_title
                                            FROM " . TABLE_PAYMENT_METHODS_DESCRIPTION . "
                                            WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "'
                                                AND (IF (languages_id = '" . tep_db_input($languages_id) . "', 1, IF ((	SELECT COUNT(payment_methods_id) > 0
                                            FROM " . TABLE_PAYMENT_METHODS_DESCRIPTION . "
                                            WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "'
                                                AND languages_id = '" . (int) $languages_id . "'), 0, languages_id = 1)))";
            $pm_desc_title_result_sql = tep_db_query($pm_desc_title_select_sql);
            $pm_desc_title_row = tep_db_fetch_array($pm_desc_title_result_sql);

            $pm_array = $this->load_pm_setting(1, $payment_methods_row['payment_methods_id']);

            $selection_array[] = array('payment_gateway_code' => $this->code,
                'payment_methods_receive_status_mode' => $payment_methods_row['payment_methods_receive_status_mode'],
                'payment_methods_id' => $payment_methods_row['payment_methods_id'],
                'payment_methods_code' => $payment_methods_row['payment_methods_code'],
                'payment_methods_types_id' => $payment_methods_row['payment_methods_types_id'],
                'payment_methods_parent_id' => $this->payment_methods_id,
                'payment_methods_logo' => $payment_methods_row['payment_methods_logo'],
                'payment_methods_description_title' => $pm_desc_title_row['payment_methods_description_title'],
                'currency' => $this->get_support_currencies($payment_methods_row['payment_methods_id']),
                'show_billing_address' => $pm_array['MODULE_PAYMENT_MAYBANK_MANDATORY_ADDRESS_FIELD'],
                'confirm_complete_days' => $pm_array['MODULE_PAYMENT_MAYBANK_CONFIRM_COMPLETE'],
                'show_contact_number' => $pm_array['MODULE_PAYMENT_MAYBANK_MANDATORY_CONTACT_FIELD'],
                'show_ic' => $pm_array['MODULE_PAYMENT_MAYBANK_MANDATORY_IC_FIELD']
            );

            if ($payment_methods_row['payment_methods_receive_status_mode'] == '-1') { // Inactive
                $pm_status_message_select = "	SELECT payment_methods_status_message
                                                FROM " . TABLE_PAYMENT_METHODS_STATUS_DESCRIPTION . "
                                                WHERE payment_methods_mode = 'RECEIVE'
                                                    AND payment_methods_status = '-1'
                                                    AND languages_id = '" . (int) $languages_id . "'
                                                    AND payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "'";
                $pm_status_message_result = tep_db_query($pm_status_message_select);
                $pm_status_message_row = tep_db_fetch_array($pm_status_message_result);
                $selection_array[sizeof($selection_array) - 1]['payment_methods_status_message'] = $pm_status_message_row['payment_methods_status_message'];
            }
        }

        return $selection_array;
    }

    function get_require_address_information() {
        return $this->require_address_information;
    }

    function get_confirm_complete_days() {
        return $this->confirm_complete_days;
    }

    function get_pm_info() {
        return $this->message;
    }

    function get_email_pm_info() {
        return $this->email_message;
    }

    function draw_confirm_button() {
        return tep_image_button2('red', 'javascript:document.checkout_confirmation.submit()', MODULE_PAYMENT_MAYBANK_TEXT_CART, 200);
    }

    function get_confirm_button_caption() {
        return MODULE_PAYMENT_MAYBANK_TEXT_TITLE;
    }

    function pre_confirmation_check() {
        global $currency;

        if (in_array($currency, $this->maybankCurrencies)) {
            
        }

        return false;
    }

    function get_support_currencies($pm_id, $by_zone = true) {
        global $zone_info_array;

        $default_currency = '';
        $support_currencies_array = array();

        $currency_code_select_sql = "	SELECT currency_code, payment_methods_instance_default
                                        FROM " . TABLE_PAYMENT_METHODS_INSTANCE . "
                                        WHERE payment_methods_id = '" . (int) $pm_id . "'
                                        ORDER BY currency_code";
        $currency_code_result_sql = tep_db_query($currency_code_select_sql);

        if (tep_db_num_rows($currency_code_result_sql) > 0) {
            while ($currency_code_row = tep_db_fetch_array($currency_code_result_sql)) {
                $support_currencies_array[] = $currency_code_row['currency_code'];

                if ($currency_code_row['payment_methods_instance_default'] == 1) {
                    $default_currency = $currency_code_row['currency_code'];
                }
            }
        } else {
            if ($this->payment_methods_parent_id > 0) {
                $payment_methods_parent_id = $this->payment_methods_parent_id;
            } else {
                $payment_methods_parent_id = $this->payment_methods_id;
            }

            $parent_currency_code_select_sql = "SELECT currency_code, payment_methods_instance_default
                                                FROM " . TABLE_PAYMENT_METHODS_INSTANCE . "
                                                WHERE payment_methods_id = '" . (int) $payment_methods_parent_id . "'
                                                ORDER BY currency_code";
            $parent_currency_code_result_sql = tep_db_query($parent_currency_code_select_sql);
            while ($parent_currency_code_row = tep_db_fetch_array($parent_currency_code_result_sql)) {
                $support_currencies_array[] = $parent_currency_code_row['currency_code'];

                if ($parent_currency_code_row['payment_methods_instance_default'] == 1) {
                    $default_currency = $parent_currency_code_row['currency_code'];
                }
            }
        }

        if (tep_not_null($default_currency)) {
            $this->defCurr = $default_currency;
        }

        if ($by_zone) {
            if (isset($zone_info_array)) {
                $support_currencies_array = array_intersect($zone_info_array[3]->zone_currency_id, $support_currencies_array);
                if (count($support_currencies_array)) {
                    array_multisort($support_currencies_array);
                } else {
                    $support_currencies_array = array($default_currency);
                }
            } else {
                $support_currencies_array = array($default_currency);
            }
        } else {
            $this->set_support_currencies($support_currencies_array);
            // All found currencies is supported. Normally this is used for backend script such as IPN call
        }

        return $support_currencies_array;
    }

    function process_button() {
        global $HTTP_POST_VARS, $shipping_cost, $total_cost, $shipping_selected, $shipping_method, $order, $languages_id;
        global $order_logged, $OFFGAMERS, $currencies, $currency, $customer_id;

        if ($this->force_to_checkoutprocess && !tep_session_is_registered('order_logged')) {
            return;
        }

        // if the user currency is not supported by Maybank
        // fall back to the default currency set on the admin interface of the module
        $maybankCurrency = $currency;
        if (!in_array($currency, $this->maybankCurrencies)) {
            $maybankCurrency = $this->maybankCurrencies[0];
        }

        $this->get_merchant_account($maybankCurrency);

        $OrderAmt = number_format($order->info['total'] * $currencies->get_value($maybankCurrency), $currencies->get_decimal_places($maybankCurrency), '.', '');

        $this->form_action_url .= '?sendString=Login$' . urlencode($this->payee_code) . '$1$' . urlencode($OrderAmt) . '$1$' . urlencode($order_logged) . '$$$' . urlencode(tep_href_link(FILENAME_MAYBANKCALLBACK));

        //-- ******** : maybank webservice v1.7
        include_once(DIR_WS_FUNCTIONS . "AES.php");
        $m_sReqSendString = 'Login$' . urlencode($this->payee_code) . '$1$' . urlencode($OrderAmt) . '$1$' . urlencode($order_logged) . '$$$' . urlencode(tep_href_link(FILENAME_MAYBANKCALLBACK));
        $m_sSalt = "Maybank2u simple encryption"; // not to change
        $m_sIterations = 2; // not to change
        $m_sKey = "\x00\x01\x02\x03\x04\x05\x06\x07\x08\x09\x0a\x0b\x0c\x0d\x0e\x0f"; // not to change
        $block_size = mcrypt_get_block_size(MCRYPT_RIJNDAEL_128, MCRYPT_MODE_ECB);
        for ($i = 0; $i < $m_sIterations; $i++) {
            $padding = $block_size - (strlen($m_sSalt . $m_sReqSendString) % $block_size);
            $aes = new AES($m_sSalt . $m_sReqSendString . str_repeat(chr($padding), $padding), $m_sKey, 128);
            $m_sReqSendString = $aes->encrypt();
        }
        $m_sSendStringEncrypt = urlencode($m_sReqSendString);
        $hidden_field_array = array(
            "q" => $m_sSendStringEncrypt,
            "i" => "OT"
        );
        $process_button_string = "";
        foreach ($hidden_field_array as $field => $value) {
            $process_button_string .= tep_draw_hidden_field($field, $value, '', true);
        }
        return $process_button_string;
        //-- Eof ******** : maybank webservice v1.7
    }

    function before_process() {
        global $order_logged, $payment_status, $order, $currency;
        global $maybankReturnedStatus;

        $proceed = false;

        if (tep_session_is_registered('order_logged')) {
            $this->get_merchant_account($currency);
            $payee_code = trim($_REQUEST['payeeCode']);
            $payment_status = trim($_REQUEST['status']);
            $order_id = trim($_REQUEST['referenceNo']);

            if ($order_logged == $order_id) {
                $xml = '<?xml version="1.0" encoding="UTF-8"?>
                        <soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
                          <soap:Body>
                            <RequestPayeeInfo xmlns="http://www.openuri.org/">
                              <userid>' . $this->user_id . '</userid>
                              <password>' . $this->user_password . '</password>
                              <payeecode>' . $this->payee_code . '</payeecode>
                              <billNo>' . $order_logged . '</billNo>
                              <Amt>' . trim($_REQUEST['transAmount']) . '</Amt>
                            </RequestPayeeInfo>
                          </soap:Body>
                        </soap:Envelope>';
                $result = $this->curl_connect($this->form_status_action_url, $xml);
                $this->set_maybank_status_from_xml_content($result);

                if ($payee_code == $this->payee_code && $maybankReturnedStatus == '00') {
                    $proceed = true;
                }
            }

            if (!$proceed) {
                tep_redirect(tep_href_link(FILENAME_CHECKOUT_PAYMENT, 'error_message=' . urlencode(MODULE_PAYMENT_MAYBANK_TEXT_ERROR_MESSAGE), 'SSL', true, false));
            }
        }
    }

    function after_process() {
        return false;
    }

    function link_to_payment($new_order_id) {
        global $order_logged;

        if (tep_session_is_registered('order_logged'))
            tep_session_unregister('order_logged');

        tep_session_register('order_logged');
        $order_logged = $new_order_id;
        require(DIR_WS_INCLUDES . 'modules/payment/maybank/catalog/maybank_splash.php');
        return;
    }

    function is_supported_currency($selected_currency) {
        return (in_array($selected_currency, $this->maybankCurrencies) ? true : false);
    }

    function check_trans_status($order_id) {
        global $maybankReturnedStatus;
        $maybankReturnedStatus = '01';

        $this->check_trans_status_flag = false;

        $maybank_payment_status = array('00' => 'Successful',
            '01' => 'Unsuccessful',
            '20' => 'Unauthourised',
            '99' => 'Transaction Not Found');

        $order_info_select_sql = "  SELECT o.payment_methods_id, o.currency, o.currency_value, ot.value
                                    FROM " . TABLE_ORDERS . " AS o
                                    INNER JOIN " . TABLE_ORDERS_TOTAL . " AS ot
                                        ON o.orders_id=ot.orders_id AND ot.class='ot_total'
                                    WHERE o.orders_id = '" . tep_db_input($order_id) . "'";
        $order_info_result_sql = tep_db_query($order_info_select_sql);
        if ($order_info_row = tep_db_fetch_array($order_info_result_sql)) {
            if ($order_info_row['payment_methods_id'] != $this->payment_methods_id) {
                return;
            }

            $payment_amount = number_format($order_info_row['value'] * $order_info_row['currency_value'], $this->amount_decimal, '.', '');
            $payment_amount = $payment_amount * 100;

            $this->get_merchant_account($order_info_row['currency']);

            $this->xml = '
				<?xml version="1.0" encoding="UTF-8"?>
				<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
				  <soap:Body>
				    <RequestPayeeInfo xmlns="http://www.openuri.org/">
				      <userid>' . $this->user_id . '</userid>
				      <password>' . $this->user_password . '</password>
				      <payeecode>' . $this->payee_code . '</payeecode>
				      <billNo>' . $order_id . '</billNo>
				      <Amt>' . $payment_amount . '</Amt>
				    </RequestPayeeInfo>
				  </soap:Body>
				</soap:Envelope>';

            $result = $this->curl_connect($this->form_status_action_url, $this->xml);
            $this->set_maybank_status_from_xml_content($result);

            if ($maybankReturnedStatus == '00') {
                $this->check_trans_status_flag = true;
            }

            $maybank_payment_data_array = array('maybank_status' => (isset($maybankReturnedStatus) ? $maybankReturnedStatus : '01' ));

            $maybank_select_sql = " SELECT orders_id
                                    FROM " . TABLE_MAYBANK . "
                                    WHERE orders_id = '" . $order_id . "'";
            $maybank_result_sql = tep_db_query($maybank_select_sql);
            if (tep_db_num_rows($maybank_result_sql)) {
                tep_db_perform(TABLE_MAYBANK, $maybank_payment_data_array, 'update', ' orders_id="' . $order_id . '" ');
            } else {
                $maybank_payment_data_array['orders_id'] = (int) $order_id;
                tep_db_perform(TABLE_MAYBANK, $maybank_payment_data_array);
            }

            $maybank_payment_history_data_array = array('orders_id' => $order_id,
                'maybank_date' => 'now()',
                'maybank_status' => $maybankReturnedStatus,
                'maybank_description' => ($maybankReturnedStatus != '00' ? 'Error: Status = ' : '') . (isset($maybank_payment_status[$maybankReturnedStatus]) ? $maybank_payment_status[$maybankReturnedStatus] : 'unknown')
            );
            tep_db_perform(TABLE_MAYBANK_PAYMENT_STATUS_HISTORY, $maybank_payment_history_data_array);
        }
    }

    function get_payee_code() {
        return $this->payee_code;
    }

    function get_user_password() {
        return $this->user_password;
    }

    function output_error() {
        return false;
    }

    function check() {
        if (!isset($this->_check)) {
            $payment_methods_select_sql = " SELECT payment_methods_id
                                            FROM " . TABLE_PAYMENT_METHODS . " as pm
                                            WHERE pm.payment_methods_id = '" . (int) $this->payment_methods_id . "'";
            $payment_methods_result_sql = tep_db_query($payment_methods_select_sql);
            $this->_check = tep_db_num_rows($payment_methods_result_sql);
        }
        return $this->_check;
    }

    function load_pm_setting($language_id = 1, $pm_id = '') {
        $pm_setting_array = array();
        //load value from DB

        if (tep_not_null($pm_id)) {
            $payment_method_id = $pm_id;

            $payment_methods_parent_id_select_sql = "	SELECT payment_methods_parent_id
                                                        FROM " . TABLE_PAYMENT_METHODS . "
                                                        WHERE payment_methods_id = '" . (int) $pm_id . "'";
            $payment_methods_parent_id_result_sql = tep_db_query($payment_methods_parent_id_select_sql);
            $payment_methods_parent_id_row = tep_db_fetch_array($payment_methods_parent_id_result_sql);

            $payment_gateway_id = $payment_methods_parent_id_row['payment_methods_parent_id'];
        } else {
            $payment_method_id = $this->payment_methods_id;
            $payment_gateway_id = $this->payment_methods_parent_id;
        }

        $payment_configuration_info_select_sql = "  SELECT pci.payment_configuration_info_key, pcid.payment_configuration_info_value
                                                    FROM " . TABLE_PAYMENT_CONFIGURATION_INFO . " as pci
                                                    LEFT JOIN " . TABLE_PAYMENT_CONFIGURATION_INFO_DESCRIPTION . " as pcid
                                                        ON pci.payment_configuration_info_id = pcid.payment_configuration_info_id
                                                            AND pcid.languages_id = '" . (int) $language_id . "'
                                                    WHERE pci.payment_methods_id = '" . (int) $payment_method_id . "'
                                                    ORDER BY pci.payment_configuration_info_sort_order";
        $payment_configuration_info_result_sql = tep_db_query($payment_configuration_info_select_sql);
        if (tep_db_num_rows($payment_configuration_info_result_sql)) {
            while ($payment_configuration_info_row = tep_db_fetch_array($payment_configuration_info_result_sql)) {
                $pm_setting_array[$payment_configuration_info_row['payment_configuration_info_key']] = $payment_configuration_info_row['payment_configuration_info_value'];
            }
        } else if ((int) $payment_gateway_id > 0) { //load value from PG
            $payment_configuration_info_select_sql = "	SELECT pci.payment_configuration_info_key, pcid.payment_configuration_info_value
                                                        FROM " . TABLE_PAYMENT_CONFIGURATION_INFO . " as pci
                                                        LEFT JOIN " . TABLE_PAYMENT_CONFIGURATION_INFO_DESCRIPTION . " as pcid
                                                            ON pci.payment_configuration_info_id = pcid.payment_configuration_info_id
                                                                AND pcid.languages_id = '" . (int) $language_id . "'
                                                        WHERE pci.payment_methods_id = '" . (int) $payment_gateway_id . "'
                                                        ORDER BY pci.payment_configuration_info_sort_order";
            $payment_configuration_info_result_sql = tep_db_query($payment_configuration_info_select_sql);
            while ($payment_configuration_info_row = tep_db_fetch_array($payment_configuration_info_result_sql)) {
                $pm_setting_array[$payment_configuration_info_row['payment_configuration_info_key']] = $payment_configuration_info_row['payment_configuration_info_value'];
            }
        }

        return $pm_setting_array;
    }

    function get_merchant_account($selected_currency) {
        $payment_methods_instance_id = 0;
        $payment_gateway_instance_select_sql = "SELECT pmi.payment_methods_instance_id, pmi.payment_methods_instance_follow_default, pmi.currency_code
                                                FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " as pmi
                                                WHERE pmi.payment_methods_id = '" . (int) $this->payment_methods_id . "'
                                                LIMIT 1";
        $payment_gateway_instance_result_sql = tep_db_query($payment_gateway_instance_select_sql);
        if (tep_db_num_rows($payment_gateway_instance_result_sql)) {
            $payment_gateway_instance_select_sql = "SELECT pmi.payment_methods_instance_id, pmi.payment_methods_instance_follow_default
                                                    FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " as pmi
                                                    WHERE pmi.currency_code = '" . tep_db_input($selected_currency) . "'
                                                            AND pmi.payment_methods_id = '" . (int) $this->payment_methods_id . "'";
            $payment_gateway_instance_result_sql = tep_db_query($payment_gateway_instance_select_sql);
            if ($payment_gateway_instance_row = tep_db_fetch_array($payment_gateway_instance_result_sql)) {
                if ((int) $payment_gateway_instance_row['payment_methods_instance_follow_default'] > 0) { // Follow Default Currency Setting
                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_follow_default'];
                } else { // Has own setting
                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_id'];
                }
            }
        } else {
            $payment_gateway_instance_select_sql = "SELECT pmi.payment_methods_instance_id, pmi.payment_methods_instance_follow_default
                                                    FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " as pmi
                                                    INNER JOIN " . TABLE_PAYMENT_METHODS . " as pm
                                                        ON pm.payment_methods_parent_id = pmi.payment_methods_id
                                                    WHERE pmi.currency_code = '" . tep_db_input($selected_currency) . "'
                                                        AND pm.payment_methods_id = '" . (int) $this->payment_methods_id . "'";
            $payment_gateway_instance_result_sql = tep_db_query($payment_gateway_instance_select_sql);
            if ($payment_gateway_instance_row = tep_db_fetch_array($payment_gateway_instance_result_sql)) {
                if ((int) $payment_gateway_instance_row['payment_methods_instance_follow_default'] > 0) {
                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_follow_default'];
                } else {
                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_id'];
                }
            }
        }

        $payment_gateway_instance_setting_select_sql = "SELECT pmis.payment_methods_instance_setting_key, pmis.payment_methods_instance_setting_value
                                                        FROM " . TABLE_PAYMENT_METHODS_INSTANCE_SETTING . " as pmis
                                                        WHERE pmis.payment_methods_instance_id = '" . (int) $payment_methods_instance_id . "'";
        $payment_gateway_instance_setting_result_sql = tep_db_query($payment_gateway_instance_setting_select_sql);
        while ($payment_gateway_instance_setting_row = tep_db_fetch_array($payment_gateway_instance_setting_result_sql)) {
            switch ($payment_gateway_instance_setting_row['payment_methods_instance_setting_key']) {
                case 'MODULE_PAYMENT_MAYBANK_PAYEE_CODE':
                    $this->payee_code = $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];
                    break;
                case 'MODULE_PAYMENT_MAYBANK_USER_ID':
                    $this->user_id = $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];
                    break;
                case 'MODULE_PAYMENT_MAYBANK_USER_PASSWORD':
                    $this->user_password = $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];
                    break;
            }
        }
    }

    function get_pg_id() {
        return $this->payment_methods_id;
    }

    function install() {
        $install_configuration_flag = true;
        if ($this->payment_methods_id > 0) {
            if ($this->receive_status != 1) {
                $update_receive_status_sql_data = array("payment_methods_receive_status" => 1);
                tep_db_perform(TABLE_PAYMENT_METHODS, $update_receive_status_sql_data, 'update', "payment_methods_id = '" . (int) $this->payment_methods_id . "'");
            } else {
                $install_configuration_flag = false;
            }
        } else {

            $install_payment_gateway_data_sql = array('payment_methods_code' => $this->code,
                'payment_methods_send_status' => 0,
                'payment_methods_title' => $this->code,
                'payment_methods_types_id' => 0,
                'payment_methods_parent_id' => 0,
                'payment_methods_legend_color' => '#A68C12',
                'payment_methods_sort_order' => 5,
                'payment_methods_filename' => $this->filename,
                'payment_methods_logo' => '',
                'date_added' => 'now()',
                'last_modified' => 'now()',
                'payment_methods_receive_status' => 1,
                'payment_methods_receive_status_mode' => 0
            );
            tep_db_perform(TABLE_PAYMENT_METHODS, $install_payment_gateway_data_sql);
            $this->payment_methods_id = tep_db_insert_id();
        }
        if ($install_configuration_flag) {
            $install_key_array = array();
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Set Order Status',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_MAYBANK_ORDER_STATUS_ID',
                    'payment_configuration_info_description' => 'Set the status of orders made with this payment module to this value.',
                    'payment_configuration_info_sort_order' => '1335',
                    'set_function' => 'tep_cfg_pull_down_order_statuses(',
                    'use_function' => 'tep_get_order_status_name',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => '1',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Set Order\'s "Processing" Status',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_MAYBANK_PROCESSING_STATUS_ID',
                    'payment_configuration_info_description' => 'Set the initial processing status of orders made with this payment module to this value.',
                    'payment_configuration_info_sort_order' => '1340',
                    'set_function' => 'tep_cfg_pull_down_order_statuses(',
                    'use_function' => 'tep_get_order_status_name',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => '7',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Payment Message',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_MAYBANK_MESSAGE',
                    'payment_configuration_info_description' => 'Payment message will show up during checkout process (Maybank).',
                    'payment_configuration_info_sort_order' => '1350',
                    'set_function' => 'tep_cfg_textarea(',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'Thank you for shopping at OffGamers.com.',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Payment Email Message',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_MAYBANK_EMAIL_MESSAGE',
                    'payment_configuration_info_description' => 'This message will show up in order confirmation e-mail.',
                    'payment_configuration_info_sort_order' => '1360',
                    'set_function' => 'tep_cfg_textarea(',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'Thank you for shopping at OffGamers.com.',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Confirm Complete (in days)',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_MAYBANK_CONFIRM_COMPLETE',
                    'payment_configuration_info_description' => 'The number of days in which an order made by this payment method is confirm completed.',
                    'payment_configuration_info_sort_order' => '1265',
                    'set_function' => '',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => '0',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Require Address Information',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_MAYBANK_MANDATORY_ADDRESS_FIELD',
                    'payment_configuration_info_description' => 'Set address field as required info during checkout.',
                    'payment_configuration_info_sort_order' => '1370',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Customer Payment Info',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_MAYBANK_CUSTOMER_PAYMENT_INFO',
                    'payment_configuration_info_description' => 'Set to true if this Payment Method required customer input for Payment Information required customer payment info.',
                    'payment_configuration_info_sort_order' => '1380',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Require IC Information',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_MAYBANK_MANDATORY_IC_FIELD',
                    'payment_configuration_info_description' => 'Set IC field as required info during checkout.',
                    'payment_configuration_info_sort_order' => '1390',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Require Contact Information',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_MAYBANK_MANDATORY_CONTACT_FIELD',
                    'payment_configuration_info_description' => 'Set Contact field as required info during checkout.',
                    'payment_configuration_info_sort_order' => '1400',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );
            foreach ($install_key_array as $data) {
                tep_db_perform(TABLE_PAYMENT_CONFIGURATION_INFO, $data['info']);
                $payment_conf_id = tep_db_insert_id();

                $data['desc']['payment_configuration_info_id'] = (int) $payment_conf_id;
                tep_db_perform(TABLE_PAYMENT_CONFIGURATION_INFO_DESCRIPTION, $data['desc']);
            }
        }
        return 1;
    }

    function configuration_information_keys() {
        return array('MODULE_PAYMENT_MAYBANK_ORDER_STATUS_ID',
            'MODULE_PAYMENT_MAYBANK_PROCESSING_STATUS_ID',
            'MODULE_PAYMENT_MAYBANK_MESSAGE',
            'MODULE_PAYMENT_MAYBANK_EMAIL_MESSAGE',
            'MODULE_PAYMENT_MAYBANK_CONFIRM_COMPLETE',
            'MODULE_PAYMENT_MAYBANK_CUSTOMER_PAYMENT_INFO',
            'MODULE_PAYMENT_MAYBANK_MANDATORY_IC_FIELD',
            'MODULE_PAYMENT_MAYBANK_MANDATORY_CONTACT_FIELD');
    }

    function multi_lang_configuration_info_keys() {
        return array('MODULE_PAYMENT_MAYBANK_MESSAGE',
            'MODULE_PAYMENT_MAYBANK_EMAIL_MESSAGE');
    }

    function merchant_information_keys() {
        include_once(DIR_WS_CLASSES . "payment_methods.php");

        return array(
            'MODULE_PAYMENT_MAYBANK_PAYEE_CODE' => array("field" => 'text', "mapping" => 'MODULE_PAYMENT_MAYBANK_LNG_PAYEE_CODE'),
            'MODULE_PAYMENT_MAYBANK_USER_ID' => array("field" => 'text', "mapping" => 'MODULE_PAYMENT_MAYBANK_LNG_USER_ID'),
            'MODULE_PAYMENT_MAYBANK_USER_PASSWORD' => array("field" => 'text', "mapping" => 'MODULE_PAYMENT_MAYBANK_LNG_USER_PASSWORD'),
            'MODULE_PAYMENT_TAX' => array("field" => 'select', 'mapping' => "MODULE_PAYMENT_TAX", "select" => payment_methods::get_tax_option()));
    }

    function remove() {
        tep_db_query("DELETE FROM " . TABLE_CONFIGURATION . " WHERE configuration_key IN ('" . implode("', '", $this->keys()) . "')");
    }

    function keys() {
        return array('MODULE_PAYMENT_MAYBANK_STATUS',
            'MODULE_PAYMENT_MAYBANK_TEST_MODE',
            'MODULE_PAYMENT_MAYBANK_PAYEE_CODE',
            'MODULE_PAYMENT_MAYBANK_USER_ID',
            'MODULE_PAYMENT_MAYBANK_USER_PASSWORD',
            'MODULE_PAYMENT_MAYBANK_SORT_ORDER',
            'MODULE_PAYMENT_MAYBANK_ORDER_STATUS_ID',
            'MODULE_PAYMENT_MAYBANK_PROCESSING_STATUS_ID',
            'MODULE_PAYMENT_MAYBANK_MESSAGE',
            'MODULE_PAYMENT_MAYBANK_LEGEND_COLOUR',
            'MODULE_PAYMENT_MAYBANK_EMAIL_MESSAGE',
            'MODULE_PAYMENT_MAYBANK_CONFIRM_COMPLETE');
    }

    function curl_connect($url, $data) {

        $ch = curl_init($url);

        $agent = "Mozilla/5.0 (Windows; U; Windows NT 5.1; en-US) AppleWebKit/536.5 (KHTML, like Gecko) Chrome/19.0.1084.46 Safari/536.5";

        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_USERAGENT, $agent);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        curl_setopt($ch, CURLOPT_HTTPHEADER, Array("Content-Type: text/xml"));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_NOPROGRESS, 0);
        curl_setopt($ch, CURLOPT_VERBOSE, 1);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);  //Windows 2003 Compatibility
        curl_setopt($ch, CURLOPT_TIMEOUT, 120);

        if ($this->connect_via_proxy) {
            curl_setopt($ch, CURLOPT_PROXY, 'http://mvy-proxy:3128');
            curl_setopt($ch, CURLOPT_PROXYUSERPWD, 'devbackend:devonly');
        }

        $response = curl_exec($ch);
        curl_close($ch);

        return $response;
    }

    function set_maybank_xml_content($parser, $text) {
        global $currentTag;
        global $maybankReturnedStatus;
        if (strtoupper(trim($currentTag)) == 'NS:REQUESTPAYEEINFORESULT') {
            $maybankReturnedStatus = trim($text);
        }
    }

    function startElement($parser, $name, $attrs) {
        global $currentTag;
        global $maybankReturnedStatus;
        switch (strtoupper($name)) {
            case 'NS:REQUESTPAYEEINFORESULT':
                $currentTag = strtoupper($name);
                break;
            default:
                break;
        }
    }

    function endElement($parser, $name) {
        global $currentTag;
        global $maybankReturnedStatus;
        switch (strtoupper($name)) {
            case 'NS:REQUESTPAYEEINFORESULT':
                $currentTag = '';
                break;
            default:
                break;
        }
    }

    function set_maybank_status_from_xml_content($pass_xml) {
        global $currentTag;
        global $maybankReturnedStatus;
        $parser = xml_parser_create();
        xml_set_element_handler($parser, array($this, 'startElement'), array($this, 'endElement'));
        xml_set_character_data_handler($parser, array($this, "set_maybank_xml_content"));
        xml_parse($parser, $pass_xml);
        xml_parser_free($parser);
    }

    function get_orders_payment_info_file() {
        return DIR_FS_CATALOG_MODULES . 'payment/maybank/admin/orders.inc.php';
    }

    function get_orders_list_payment_info_file() {
        return DIR_FS_CATALOG_MODULES . 'payment/maybank/admin/orders_list.inc.php';
    }

    function get_ipn_class_file() {
        return DIR_FS_CATALOG_MODULES . 'payment/maybank/classes/maybank_ipn_class.php';
    }

}

?>