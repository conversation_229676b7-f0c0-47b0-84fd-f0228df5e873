<?
require_once(DIR_FS_CATALOG_MODULES . 'payment/maybank.php');
include_once(DIR_FS_CATALOG_MODULES . 'payment/maybank/admin/languages/' . $language . '/maybank.lng.php');

$maybank_payment_status = array('00' => 'Successful',
								'01' => 'Unsuccessful',
								'20' => 'Unauthourised',
                                '99' => 'Transaction Not Found'
                                );

$maybank_trans_info_select_sql = "	SELECT * 
									FROM " . TABLE_MAYBANK . " 
									WHERE orders_id = '" . (int)$oID . "'";
$maybank_trans_info_result_sql= tep_db_query($maybank_trans_info_select_sql);

$maybank_trans_history_select_sql = "	SELECT * 
										FROM " . TABLE_MAYBANK_PAYMENT_STATUS_HISTORY . "
										WHERE orders_id = '" . (int)$oID  . "' 
										ORDER BY maybank_date";
$maybank_trans_history_result_sql = tep_db_query($maybank_trans_history_select_sql);
?>
<table border="0" width="100%" cellspacing="0" cellpadding="2">
<? ob_start(); ?>
	<tr>
		<td class="main">
      		<table border="0" width="100%" cellspacing="0" cellpadding="2">
      			<tr>
      				<td class="main" width="10%" nowrap><b><?=ENTRY_PAYMENT_METHOD?></b></td>
      				<td class="main">
      					<div>
      						<? if((int)$order->info['payment_methods_id']>0) { ?><div class="<?=((int)$payment_info_array[$order->info['payment_methods_id']]->confirm_complete_days>0?'rp_icon':'nrp_icon')?>"><b><?=((int)$payment_info_array[$order->info['payment_methods_id']]->confirm_complete_days>0?'RP':'NRP')?></b></div> <? } ?>
      						<div style="width:30px; height:15px; background-color:<?=$payment_module_info['display_colour']?>; float:left; vertical-align:top; text-align:center;">&nbsp;</div>
      						<div style="vertical-align: top">
      							&nbsp;&nbsp;
<?
	if ($view_payment_details_permission) {
		
		if (tep_not_null($last_payment_method) && $last_payment_method['payment_methods_id'] != $order->info['payment_methods_id']) {
			echo '<span class="redIndicator">'.(tep_not_null($payment_module_info['payment_methods_parent_title'])?$payment_module_info['payment_methods_parent_title'] . ' - ' :'') . $payment_module_info['payment_methods_title'].'</span>';
		} else {
			echo (tep_not_null($payment_module_info['payment_methods_parent_title'])?$payment_module_info['payment_methods_parent_title'] . ' - ' :'') . $payment_module_info['payment_methods_title'];
		}
		
		if (tep_not_null($last_payment_method) && $last_payment_method['payment_methods_id'] != $order->info['payment_methods_id']) {
			echo '&nbsp;&nbsp;<span class="redIndicator">(Last Payment Method: ' . (tep_not_null($last_payment_method['parent_payment_methods_title'])? $last_payment_method['parent_payment_methods_title'] . ' - ' : '' ) . $last_payment_method['payment_methods_title'] . ')</span>';
		}
	} else {
		echo (tep_not_null($payment_module_info['payment_methods_parent_title'])?$payment_module_info['payment_methods_parent_title'] . ' - ' :'') . $payment_module_info['payment_methods_title'];
	}
?>
							</div>
      					</div>
      				</td>
				</tr>
			</table>
		</td>
	</tr>
<?
$payment_method_title_info = ob_get_contents();
ob_end_clean();

if (tep_db_num_rows($maybank_trans_info_result_sql) || tep_db_num_rows($maybank_trans_history_result_sql)) {
	$maybank_trans_info_row = tep_db_fetch_array($maybank_trans_info_result_sql);
	
	echo $payment_method_title_info;
	if (!$view_payment_details_permission) {
		;
	} else {
?>
  	<tr>
    	<td class="main">
      		<table border="0" width="100%" cellspacing="0" cellpadding="2">
      			<tr valign="top">
        			<td width="35%">
              			<table border="0" cellspacing="0" cellpadding="2">
              				<tr>
                				<td class="main" valign="top" nowrap><b><?=ENTRY_MAYBANK_REF_ID?></b>&nbsp;</td>
                				<td class="main"><?=$maybank_trans_info_row['maybank_reference_id']?></td>
              				</tr>
              				<tr>
                				<td class="main" valign="top" nowrap><b><?=ENTRY_MAYBANK_APPROVAL_CODE?></b>&nbsp;</td>
                				<td class="main" nowrap><?=$maybank_trans_info_row['maybank_bank_auth_code']?></td>
              				</tr>
              				<tr>
                				<td class="main" valign="top" nowrap><b><?=ENTRY_MAYBANK_CURRENCY?></b>&nbsp;</td>
                				<td class="main" nowrap><?
			    					if (strtolower($maybank_trans_info_row["maybank_currency"]) == strtolower($order->info['currency'])) {
			    						echo $maybank_trans_info_row['maybank_currency'];
			    					} else {
			    						echo '<span class="redIndicator">'.$maybank_trans_info_row["maybank_currency"].'<br><span class="smallText">Does not match purchase currency.</span></span>';
			    					}
    								echo '('.TEXT_INFO_CURRENCY_NOT_FROM_MAYBANK.')';
    							?></td>
              				</tr>
              				<tr>
                				<td class="main" valign="top" nowrap><b><?=ENTRY_MAYBANK_AMT?></b>&nbsp;</td>
                				<td class="main" nowrap><?
			    					$gross_display_text = number_format($maybank_trans_info_row["maybank_amount"], $currencies->currencies[$order->info["currency"]]['decimal_places'], $currencies->currencies[$order->info["currency"]]['decimal_point'], $currencies->currencies[$order->info["currency"]]['thousands_point']);
			    					if ($currencies->currencies[$order->info["currency"]]['symbol_left'].$gross_display_text.$currencies->currencies[$order->info["currency"]]['symbol_right'] != strip_tags($order->order_totals['ot_total']['text']) ) {
			    						$gross_display_text = '<span class="redIndicator">'.$gross_display_text.'<br><span class="smallText">Does not match purchase amount.</span></span>';
			    					}
			    					echo $gross_display_text;
			    				?></td>
              				</tr>
              				<tr>
                				<td class="main" valign="top" nowrap><b><?=ENTRY_MAYBANK_PAYMENT_STATUS?></b>&nbsp;</td>
                				<td class="main" nowrap>
	              				<?
                					echo isset($maybank_payment_status[$maybank_trans_info_row['maybank_status']]) ? $maybank_payment_status[$maybank_trans_info_row['maybank_status']] : TEXT_STATUS_UNKNOWN;
                					
                					if ($order->info['orders_status'] == '1' || $order->info['orders_status'] == '7' || $order->info['orders_status'] == '2') {
	                					echo "&nbsp;&nbsp;";
										echo tep_draw_form('maybank_status_check_form', FILENAME_ORDERS, tep_get_all_get_params(), 'post');
										echo tep_draw_hidden_field('subaction', 'payment_action');
										echo tep_draw_hidden_field('payment_action', 'check_trans_status');
										echo tep_submit_button('Check Payment Status', 'Check Payment Status', 'name="maybankCheckTransStatusBtn"', 'inputButton');
										echo "</form>";
									}
								?>
								</td>
							</tr>
              			</table>
        			</td>
        			<td>
              			<table border="0" cellspacing="0" cellpadding="2">
              				<tr>
                				<td class="main" colspan="2" nowrap>
                					<table border="1" cellspacing="0" cellpadding="2">
        								<tr>
                							<td class="smallText" nowrap><b><?=TABLE_HEADING_MAYBANK_DATE?></b></td>
                							<td class="smallText" nowrap><b><?=TABLE_HEADING_MAYBANK_STATUS?></b></td>
                							<td class="smallText" nowrap><b><?=TABLE_HEADING_MAYBANK_DESCRIPTION?></b></td>
                						</tr>
<?
		while ($maybank_trans_history_row = tep_db_fetch_array($maybank_trans_history_result_sql)) {
        	echo ' 						<tr>
                							<td class="smallText" nowrap>'.$maybank_trans_history_row['maybank_date'].'</td>
                							<td class="smallText" nowrap>'.(isset($maybank_payment_status[$maybank_trans_history_row['maybank_status']])?$maybank_payment_status[$maybank_trans_history_row['maybank_status']]:$maybank_trans_history_row['maybank_status']).'</td>
                							<td class="smallText" nowrap>'.(tep_not_null($maybank_trans_history_row['maybank_description'])?$maybank_trans_history_row['maybank_description']:'&nbsp;').'</td>
            							</tr>';
     	}
?>
                					</table>
                				</td>
                			</tr>
              			</table>
              		</td>
      			</tr>
      		</table>
   		</td>
  	</tr>
<?
	}
} else {
	echo $payment_method_title_info;
?>	
	<tr>
		<td class="main" nowrap>
		<?
			if ($order->info['orders_status'] == '1' || $order->info['orders_status'] == '7' || $order->info['orders_status'] == '2') {
				echo "&nbsp;&nbsp;";
				echo tep_draw_form('maybank_status_check_form', FILENAME_ORDERS, tep_get_all_get_params(), 'post');
				echo tep_draw_hidden_field('subaction', 'payment_action');
				echo tep_draw_hidden_field('payment_action', 'check_trans_status');
				echo tep_submit_button('Check Payment Status', 'Check Payment Status', 'name="maybankCheckTransStatusBtn"', 'inputButton');
				echo "</form>";
			}
		?>
		</td>
	</tr>
<?
}
?>
</table>