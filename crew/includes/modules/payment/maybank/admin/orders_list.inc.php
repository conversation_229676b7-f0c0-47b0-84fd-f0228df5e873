<?
include_once(DIR_FS_CATALOG_MODULES . 'payment/maybank/admin/languages/' . $language . '/maybank.lng.php');

$maybank_trans_info_select_sql = "	SELECT * 
									FROM " . TABLE_MAYBANK . " 
									WHERE orders_id = '" . $order_obj->orders_id . "'";
$maybank_trans_info_result_sql= tep_db_query($maybank_trans_info_select_sql);
?>
<table border="0" width="100%" cellspacing="0" cellpadding="2">
	<tr>
		<td class="subInvoiceBoxHeading">
			<div style="border-bottom: 1px solid #996600; border-top: 1px solid #996600; padding: 2px;">Payment Details</div>
		</td>
	</tr>
<?
if ($maybank_trans_info_row = tep_db_fetch_array($maybank_trans_info_result_sql)) {
?>
	<tr>
    	<td class="main">
      		<table border="0" width="100%" cellspacing="0" cellpadding="2">
      			<tr valign="top">
        			<td width="50%">
              			<table border="0" cellspacing="0" cellpadding="2">
              				<tr>
                				<td class="invoiceRecords" valign="top" nowrap><?=ENTRY_MAYBANK_CURRENCY?></td>
                				<td class="invoiceRecords" valign="top">:&nbsp;</td>
                				<td class="invoiceRecords" nowrap><?=$maybank_trans_info_row['maybank_currency']?></td>
              				</tr>
              				<tr>
                				<td class="invoiceRecords" valign="top" nowrap><?=ENTRY_MAYBANK_AMT?></td>
                				<td class="invoiceRecords" valign="top">:&nbsp;</td>
                				<td class="invoiceRecords"><?=$maybank_trans_info_row['maybank_amount']?></td>
              				</tr>
              				<tr>
              					<td class="invoiceRecords" valign="top" nowrap><?=ENTRY_MAYBANK_PAYMENT_STATUS?></td>
                				<td class="invoiceRecords" valign="top">:&nbsp;</td>
                				<td class="invoiceRecords"><?=(tep_not_null($maybank_trans_info_row['maybank_status']) ? $maybank_trans_info_row['maybank_status'] : TEXT_NOT_AVAILABLE)?></td>
              				</tr>
              			</table>
        			</td>
        			<td valign="top">
              			<table border="0" cellspacing="0" cellpadding="2">
              				<tr>
                				<td class="invoiceRecords" valign="top" nowrap><?=ENTRY_MAYBANK_REF_ID?></td>
                				<td class="invoiceRecords" valign="top">:&nbsp;</td>
                				<td class="invoiceRecords"><?=(tep_not_null($maybank_trans_info_row['maybank_reference_id']) ? $maybank_trans_info_row['maybank_reference_id'] : TEXT_NOT_AVAILABLE)?></td>
              				</tr>
              				<tr>
                				<td class="invoiceRecords" valign="top" nowrap><?=ENTRY_MAYBANK_APPROVAL_CODE?></td>
                				<td class="invoiceRecords" valign="top">:&nbsp;</td>
                				<td class="invoiceRecords"><?=(tep_not_null($maybank_trans_info_row['maybank_approval_code']) ? $maybank_trans_info_row['maybank_approval_code'] : TEXT_NOT_AVAILABLE)?></td>
              				</tr>
              			</table>
              		</td>
      			</tr>
      		</table>
   		</td>
  	</tr>
<?
} else {
?>
	<tr>
		<td class="invoiceRecords">No further payment information is available.</td>
	</tr>
<?
}
?>