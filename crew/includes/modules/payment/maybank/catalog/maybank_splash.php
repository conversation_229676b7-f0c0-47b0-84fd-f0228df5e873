<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?php echo HTML_PARAMS; ?> >
    <head>
        <title><?php echo STORE_NAME; ?></title>
        <meta http-equiv="Content-Type" content="text/html; charset=<?php echo CHARSET; ?>">
        <style type="text/css">
            body {background-color:#FFFFFF;}
            body, td, div {font-family: verdana, arial, sans-serif;}
        </style>
    </head>
    <body onload="paymentCall();">
        <script type="text/javascript">
        function paymentCall() {
            document.M2UPayment.target = "M2UPayment";
            window.open('about:blank', 'M2UPayment', 'width=400,height=350');
            document.M2UPayment.submit();
        }
        </script>
        <?php echo tep_draw_form('M2UPayment', $this->form_action_url, 'post', 'target="M2UPayment"'); ?>
        <table cellpadding="0" width="100%" height="100%" cellspacing="0" style="border:1px solid #003366;">
            <tr>
                <td align="middle" style="height:100%; vertical-align:middle;">
                    <div style="color:#003366">
                        <h4>
                            Please do not refresh or reload while payment is in progress!<br><br>
                            If you have a popup blocker enabled, please perform the following steps to avoid difficulties during payment process.
                        </h4>
                    </div>
                </td>
            </tr>
            <tr>
                <td align="center">
                    <div style="color:#003366">
                        <h4>
                            <ul>
                                <li>Go to Popop Blocker's Setting/Preference page.</li>
                                <li>Add the following sites to the allowed/safe/white list
                                    <ul>
                                        <li>*.offgamers.com</li>
                                        <li>*.maybank2u.com.my</li>
                                    </ul>
                                </li>
                            </ul>
                        </h4>
                    </div>
                </td>
            </tr>
        </table>
        <?php echo $this->process_button(); ?>
    </form>
</body>
</html>