<?
include_once(DIR_FS_CATALOG_MODULES . 'payment/mazooma/admin/languages/' . $language . '/mazooma.lng.php');

$mazooma_trans_info_select_sql = "SELECT * FROM " . TABLE_MAZOOMA . " WHERE orders_id='" . (int)$oID . "'";
$mazooma_trans_info_result_sql= tep_db_query($mazooma_trans_info_select_sql);

$mazooma_payment_status_history_info_select_sql = "SELECT * FROM " . TABLE_MAZOOMA_STATUS_HISTORY . " WHERE orders_id = '" . tep_db_input($oID) . "'";
$mazooma_payment_status_history_info_result_sql = tep_db_query($mazooma_payment_status_history_info_select_sql);

$mazooma_payment_status = array('S' => 'approved',
								'X' => 'declined',
								'R' => 'rejected',
								'I' => 'initiated');
?>
<table border="0" width="100%" cellspacing="0" cellpadding="2">
<? ob_start(); 

?>
	<tr>
		<td class="main">
      		<table border="0" width="100%" cellspacing="0" cellpadding="2">
      			<tr>
      				<td class="main" width="12%" nowrap><b><?=ENTRY_PAYMENT_METHOD?></b></td>
      				<td class="main">
      					<div>
      						<? if((int)$order->info['payment_methods_id']>0) { ?><div class="<?=((int)$payment_info_array[$order->info['payment_methods_id']]->confirm_complete_days>0?'rp_icon':'nrp_icon')?>"><b><?=((int)$payment_info_array[$order->info['payment_methods_id']]->confirm_complete_days>0?'RP':'NRP')?></b></div> <? } ?>
      						<div style="width:30px; height:15px; background-color:<?=$payment_module_info['display_colour']?>; float:left; vertical-align:top; text-align:center;">&nbsp;</div>
      						<div style="vertical-align: top">
      							&nbsp;&nbsp;
<?
	if ($view_payment_details_permission) {
		if (tep_not_null($last_payment_method) && $last_payment_method['payment_methods_id'] != $order->info['payment_methods_id']) {
			echo '<span class="redIndicator">'.(tep_not_null($payment_module_info['payment_methods_parent_title'])?$payment_module_info['payment_methods_parent_title'] . ' - ' :'') . $payment_module_info['payment_methods_title'].'</span>';
		} else {
			echo (tep_not_null($payment_module_info['payment_methods_parent_title'])?$payment_module_info['payment_methods_parent_title'] . ' - ' :'') . $payment_module_info['payment_methods_title'];
		}
		
		if (tep_not_null($last_payment_method) && $last_payment_method['payment_methods_id'] != $order->info['payment_methods_id']) {
			echo '&nbsp;&nbsp;<span class="redIndicator">(Last Payment Method: ' . (tep_not_null($last_payment_method['parent_payment_methods_title'])? $last_payment_method['parent_payment_methods_title'] . ' - ' : '' ) . $last_payment_method['payment_methods_title'] . ')</span>';
		}
	} else {
		echo (tep_not_null($payment_module_info['payment_methods_parent_title'])?$payment_module_info['payment_methods_parent_title'] . ' - ' :'') . $payment_module_info['payment_methods_title'];
	}
?>
							</div>
      					</div>
      				</td>
				</tr>
			</table>
		</td>
	</tr>
<?
$payment_method_title_info = ob_get_contents();
ob_end_clean();

if (tep_db_num_rows($mazooma_trans_info_result_sql) || tep_db_num_rows($mazooma_payment_status_history_info_result_sql)) {
	
	echo $payment_method_title_info;
	
	if (!$view_payment_details_permission) {
		;
	} else {
		
		$mazooma_trans_info_row = tep_db_fetch_array($mazooma_trans_info_result_sql);
?>
  	<tr>
    	<td class="main">
      		<table border="0" width="100%" cellspacing="0" cellpadding="2">
      			<tr valign="top">
        			<td width="39%" nowrap>
              			<table border="0" cellspacing="0" cellpadding="2">
              				<tr>
                				<td class="main" valign="top" nowrap><b><?=ENTRY_MAZOOMA_TRANSACTION_NUMBER?></b>&nbsp;</td>
                				<td class="main" nowrap><?=$mazooma_trans_info_row['mazooma_transaction_number']?></td>
              				</tr>
              				<tr>
                				<td class="main" valign="top" nowrap><b><?=ENTRY_MAZOOMA_STATUS?></b>&nbsp;</td>
                				<td class="main" nowrap><?=(isset($mazooma_payment_status[$mazooma_trans_info_row['mazooma_status']])?$mazooma_payment_status[$mazooma_trans_info_row['mazooma_status']]:$mazooma_trans_info_row['mazooma_status'])?></td>
              				</tr>
              				<tr>
                				<td class="main" valign="top" nowrap><b><?=ENTRY_MAZOOMA_ERROR_CODE?></b>&nbsp;</td>
                				<td class="main" nowrap><?=(tep_not_null($mazooma_trans_info_row['mazooma_error_code'])?$mazooma_trans_info_row['mazooma_error_code']:'')?></td>
              				</tr>
              				<tr>
                				<td class="main" valign="top" nowrap><b><?=ENTRY_MAZOOMA_USER_ID?></b>&nbsp;</td>
                				<td class="main" nowrap><?=$mazooma_trans_info_row['mazooma_user_id']?></td>
              				</tr>
              				<tr>
                				<td class="main" valign="top" nowrap><b><?=ENTRY_MAZOOMA_FEE?></b>&nbsp;</td>
                				<td class="main" nowrap><?=$mazooma_trans_info_row['mazooma_fee']?></td>
              				</tr>
              			</table>
        			</td>
        			<td>
        				<table border="0" cellspacing="0" cellpadding="1">
        					<tr>
        						<td colspan="2">
									<table border="1" cellspacing="0" cellpadding="2">
		        						<tr>
		        							<td class="smallText" nowrap><b><?=TABLE_HEADING_MAZOOMA_DATE?></b></td>
		        							<td class="smallText" nowrap><b><?=TABLE_HEADING_MAZOOMA_STATUS?></b></td>
		        							<td class="smallText" nowrap><b><?=TABLE_HEADING_MAZOOMA_DESCRIPTION?></b></td>
		        						</tr>
<?		while ($mazooma_payment_status_history_info_row = tep_db_fetch_array($mazooma_payment_status_history_info_result_sql)) {
        	echo ' 						<tr>
                							<td class="smallText" nowrap>'.$mazooma_payment_status_history_info_row['mazooma_status_date'].'</td>
                							<td class="smallText" nowrap>'.(isset($mazooma_payment_status[$mazooma_payment_status_history_info_row['mazooma_status']])?$mazooma_payment_status[$mazooma_payment_status_history_info_row['mazooma_status']]:'Unknown').'</td>
                							<td class="smallText" nowrap>'.($mazooma_payment_status_history_info_row['mazooma_status_description']).'</td>
                						</tr>';
     	}
?>
									</table>
								</td>
        					</tr>
        				</table>
        			</td>
        			<td>
        				<table border="0" cellspacing="0" cellpadding="1">
							<tr>
			    				<td class="main" valign="top" nowrap><b><?=ENTRY_MAZOOMA_CURRENCY?></b>&nbsp;</td>
			    				<td class="main" nowrap><?
			    					if (strtolower($mazooma_trans_info_row["mazooma_currency"]) == strtolower($order->info['currency'])) {
			    						echo $mazooma_trans_info_row['mazooma_currency'];
			    					} else {
			    						echo '<span class="redIndicator">'.$mazooma_trans_info_row["mazooma_currency"].'<br><span class="smallText">Does not match purchase currency.</span></span>';
			    					}
    							?></td>
			  				</tr>
              				<tr>
                				<td class="main" valign="top" nowrap><b><?=ENTRY_MAZOOMA_AMOUNT?></b>&nbsp;</td>
                				<td class="main" nowrap>
<?
									$gross_display_text = number_format($mazooma_trans_info_row["mazooma_amount"], $currencies->currencies[$order->info["currency"]]['decimal_places'], $currencies->currencies[$order->info["currency"]]['decimal_point'], $currencies->currencies[$order->info["currency"]]['thousands_point']);
			    					if ($currencies->currencies[$order->info["currency"]]['symbol_left'].$gross_display_text.$currencies->currencies[$order->info["currency"]]['symbol_right'] != strip_tags($order->order_totals['ot_total']['text']) ) {
			    						$gross_display_text = '<span class="redIndicator">'.$gross_display_text.'<br><span class="smallText">Does not match purchase amount.</span></span>';
			    					}
			    					echo $gross_display_text;
?>
                				</td>
              				</tr>
        				</table>
        			</td>
  				</tr>
      		</table>
   		</td>
  	</tr>
<?
	}
}/* else {
	echo $payment_method_title_info;
	*/
	//if ($order->info['orders_status'] == '1' || $order->info['orders_status'] == '7' || $order->info['orders_status'] == '2') {
		echo '<tr>
    			<td class="main">';
		echo tep_draw_form('bb_status_check_form', FILENAME_ORDERS, tep_get_all_get_params(), 'post');
		echo tep_draw_hidden_field('subaction', 'payment_action');
		echo tep_draw_hidden_field('payment_action', 'check_trans_status');
		echo '<input type="submit" name="BBCheckTransStatusBtn" value="Check Payment Status" title="Check Payment Status" class="inputButton">';
		echo '</form>
				</td>
			  </tr>';
	//}
//}
?>
</table>