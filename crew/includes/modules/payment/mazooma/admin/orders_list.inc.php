<?
include_once(DIR_FS_CATALOG_MODULES . 'payment/mazooma/admin/languages/' . $language . '/mazooma.lng.php');

$mazooma_trans_info_select_sql = "SELECT * FROM " . TABLE_MAZOOMA . " WHERE orders_id = '" . $order_obj->orders_id . "'";
$mazooma_trans_info_result_sql= tep_db_query($mazooma_trans_info_select_sql);
?>
<table border="0" width="100%" cellspacing="0" cellpadding="2">
	<tr>
		<td class="subInvoiceBoxHeading">
			<div style="border-bottom: 1px solid #996600; border-top: 1px solid #996600; padding: 2px;">Payment Details</div>
		</td>
	</tr>
<?
if ($mazooma_trans_info_row = tep_db_fetch_array($mazooma_trans_info_result_sql)) {
	$mazooma_payment_status = array('S' => 'approved',
									'X' => 'declined',
									'R' => 'rejected',
									'I' => 'initiated');
?>
	<tr>
    	<td class="main">
      		<table border="0" width="100%" cellspacing="0" cellpadding="2">
      			<tr valign="top">
        			<td width="50%">
              			<table border="0" cellspacing="0" cellpadding="2">
              				<tr>
                				<td class="invoiceRecords" valign="top" nowrap><?=ENTRY_MAZOOMA_TRANSACTION_NUMBER?></td>
                				<td class="invoiceRecords" valign="top">:&nbsp;</td>
                				<td class="invoiceRecords" nowrap><?=$mazooma_trans_info_row['mazooma_transaction_number']?></td>
              				</tr>
              				<tr>
                				<td class="invoiceRecords" valign="top" nowrap><?=ENTRY_MAZOOMA_STATUS?></td>
                				<td class="invoiceRecords" valign="top">:&nbsp;</td>
                				<td class="invoiceRecords" nowrap><?=(isset($mazooma_payment_status[$mazooma_trans_info_row['mazooma_status']])?$mazooma_payment_status[$mazooma_trans_info_row['mazooma_status']]:'Unknown')?></td>
              				</tr>
              			</table>
        			</td>
      			</tr>
      		</table>
   		</td>
  	</tr>
<?
} else {
?>
	<tr>
		<td class="invoiceRecords">No further payment information is available.</td>
	</tr>
<?
}
?>