<?
/*
  $Id: orders.inc.php,v 1.10 2014/10/15 06:59:49 weichen Exp $

  Developer: <PERSON> (c) 2006 SKC Venture

  Released under the GNU General Public License
 */

include_once(DIR_FS_CATALOG_MODULES . 'payment/mb/admin/languages/' . $language . '/mb.lng.php');

$mb_payment_status = array('2' => 'Processed',
    '1' => 'Scheduled',
    '0' => 'Pending',
    '-1' => 'Cancelled',
    '-2' => 'Failed');

$mb_trans_info_select_sql = "SELECT * FROM " . TABLE_PAYMENT_MONEYBOOKERS . " WHERE mb_order_id = '" . (int) $oID . "'";
$mb_trans_info_result_sql = tep_db_query($mb_trans_info_select_sql);
?>
<table border="0" width="100%" cellspacing="0" cellpadding="2">
    <? ob_start(); ?>
    <tr>
        <td class="main">
            <table border="0" width="100%" cellspacing="0" cellpadding="2">
                <tr>
                    <td class="main" width="12%" nowrap><b><?= ENTRY_PAYMENT_METHOD ?></b></td>
                    <td class="main">
                        <div>
                            <? if ((int) $order->info['payment_methods_id'] > 0) { ?><div class="<?= ((int) $payment_info_array[$order->info['payment_methods_id']]->confirm_complete_days > 0 ? 'rp_icon' : 'nrp_icon') ?>"><b><?= ((int) $payment_info_array[$order->info['payment_methods_id']]->confirm_complete_days > 0 ? 'RP' : 'NRP') ?></b></div> <? } ?>
                            <div style="width:30px; height:15px; background-color:<?= $payment_module_info['display_colour'] ?>; float:left; vertical-align:top; text-align:center;">&nbsp;</div>
                            <div style="vertical-align: top">
                                &nbsp;&nbsp;
                                <?
                                if ($view_payment_details_permission) {
                                    if (tep_not_null($last_payment_method) && $last_payment_method['payment_methods_id'] != $order->info['payment_methods_id']) {
                                        echo '<span class="redIndicator">' . (tep_not_null($payment_module_info['payment_methods_parent_title']) ? $payment_module_info['payment_methods_parent_title'] . ' - ' : '') . $payment_module_info['payment_methods_title'] . '</span>';
                                    } else {
                                        echo (tep_not_null($payment_module_info['payment_methods_parent_title']) ? $payment_module_info['payment_methods_parent_title'] . ' - ' : '') . $payment_module_info['payment_methods_title'];
                                    }

                                    if (tep_not_null($last_payment_method) && $last_payment_method['payment_methods_id'] != $order->info['payment_methods_id']) {
                                        echo '&nbsp;&nbsp;<span class="redIndicator">(Last Payment Method: ' . (tep_not_null($last_payment_method['parent_payment_methods_title']) ? $last_payment_method['parent_payment_methods_title'] . ' - ' : '' ) . $last_payment_method['payment_methods_title'] . ')</span>';
                                    }
                                } else {
                                    echo (tep_not_null($payment_module_info['payment_methods_parent_title']) ? $payment_module_info['payment_methods_parent_title'] . ' - ' : '') . $payment_module_info['payment_methods_title'];
                                }
                                ?>
                            </div>
                        </div>
                    </td>
                </tr>
            </table>
        </td>
    </tr>
    <?
    $payment_method_title_info = ob_get_contents();
    ob_end_clean();

    if ($mb_trans_info_row = tep_db_fetch_array($mb_trans_info_result_sql)) {
        $customer_share_mb_payer_email_array = array();
        if (tep_not_null($mb_trans_info_row["mb_payer_email"])) {
            $payer_email_select_sql = " SELECT DISTINCT customers_id 
                                        FROM " . TABLE_ORDERS . " AS o 
                                        INNER JOIN " . TABLE_PAYMENT_MONEYBOOKERS . " AS mb 
                                            ON (o.orders_id = mb.mb_order_id)
                                        WHERE mb.mb_payer_email = '" . $mb_trans_info_row["mb_payer_email"] . "'";
            $payer_email_result = tep_db_query($payer_email_select_sql);
            while ($payer_email_row = tep_db_fetch_array($payer_email_result)) {
                $customer_share_mb_payer_email_array[] = $payer_email_row["customers_id"];
            }
        }
        
        echo $payment_method_title_info;
        if (!$view_payment_details_permission) {
            ;
        } else {
            $mb_trans_history_select_sql = "SELECT mb_err_no, mb_err_txt, mb_date, mb_status 
										FROM " . TABLE_PAYMENT_MONEYBOOKERS_STATUS_HISTORY . "
										WHERE mb_trans_id = '" . tep_db_input($mb_trans_info_row['mb_trans_id']) . "' 
										ORDER BY mb_date";
            $mb_trans_history_result_sql = tep_db_query($mb_trans_history_select_sql);
            ?>
            <tr>
                <td class="main">
                    <table border="0" width="100%" cellspacing="0" cellpadding="2">
                        <tr valign="top">
                            <td width="35%">
                                <table border="0" cellspacing="0" cellpadding="2">
                                    <tr>
                                        <td class="main" valign="top" nowrap><b><?= ENTRY_MB_PAYER_EMAIL ?></b>&nbsp;</td>
                                        <td class="main" nowrap>
                                            <?
                                            if (tep_not_null($mb_trans_info_row["mb_payer_email"])) {
                                                if ($email_used_by_other == 1) {
                                                    $email_alert_reason .= tep_draw_form('cust_lists_share_email', FILENAME_CUSTOMERS, 'action=show_report&customer_lists_subaction=do_search', 'post', 'target="_blank"') . "\n" .
                                                            tep_draw_hidden_field('customer_share_id', tep_array_serialize($customer_with_this_email_array)) . "\n" .
                                                            '<span class="redIndicator">Email exists in <a href="javascript: document.cust_lists_share_email.submit();"><u>' . count($customer_with_this_email_array) . '</u></a> profiles.</span><br>' .
                                                            '</form>' . "\n";
                                                }

                                                $email_alert_reason .= ($total_email_used == 1) ? '<span class="redIndicator"><a href="' . tep_href_link(FILENAME_ORDERS, 'cID=' . $cid) . '" target="_blank"><u>' . count($total_name_used_in_mb) . '</u></a> email used in Moneybookers orders.</span><br>' : '';

                                                if (tep_not_null($email_alert_reason)) {
                                                    echo '<br><span class="smallText">' . $email_alert_reason . '</span>';
                                                }
                                                if (count($customer_share_mb_payer_email_array) > 1) {
                                                    echo tep_draw_form('cust_lists_share_payer_id', FILENAME_CUSTOMERS, tep_get_all_get_params(array('action')) . 'action=show_report&customer_lists_subaction=do_search', 'post', 'target="customerSharePayerIDWin"') . "\n" .
                                                    tep_draw_hidden_field('customer_share_id', tep_array_serialize($customer_share_mb_payer_email_array)) . "\n" .
                                                    '<span class="smallText"><span class="redIndicator">' . $mb_trans_info_row["mb_payer_email"] . '<br><a href="javascript: document.cust_lists_share_payer_id.submit();"><u>' . count($customer_share_mb_payer_email_array) . '</u></a> customers sharing this moneybookers account.</span></span>' .
                                                    '</form>' . "\n";
                                                } else {
                                                    echo $mb_trans_info_row["mb_payer_email"];
                                                }

                                                $payer_email_verified = tep_info_verified_check($cid, $mb_trans_info_row['mb_payer_email'], 'email');
                                                if ($payer_email_verified == 'A' || $payer_email_verified == 'M') {
                                                    echo tep_image(DIR_WS_ICONS . "ok.gif", "", "", "", 'align="top" onMouseover="ddrivetip(\'' . TEXT_IMAGE_MOUSEOVER_VERIFY . '\', \'\' , 180);" onMouseout="hideddrivetip();"');
                                                } else {
                                                    $confirm_send_profile_email_verification_url = tep_href_link(FILENAME_ORDERS, 'oID=' . $oID . '&action=edit&subaction=send_moneybooker_verify_email') . '&cid=' . $cid;
                                                    echo tep_image(DIR_WS_ICONS . "off.gif", "", "", "", 'align="top" onMouseover="ddrivetip(\'' . TEXT_IMAGE_MOUSEOVER_NOT_VERIFY . '\', \'\' , 201);" onMouseout="hideddrivetip();"') . '&nbsp;' . '<a href="" onclick="confirm_action(\'Are you sure you want to send verification e-mail?\', \'' . $confirm_send_profile_email_verification_url . '\'); return false;">' . EMAIL_MB_VERIFY_SEND_LINK . '</a>';
                                                }

                                                if (tep_not_null($mb_trans_info_row["mb_payer_email"])) {
                                                    $payment_check_info = array('email' => $mb_trans_info_row["mb_payer_email"],
                                                        'orders_id' => $order->order_id,
                                                        'date_purchased' => $order->info['date_purchased'],
                                                        'customers_id' => $order->customer['id']);
                                                    $payment_verified_date = tep_get_payment_info_verified_date($payment_check_info, 'moneybookers');
                                                    echo "<br><span class='redIndicator'>First successful verified date by this user: " . (tep_not_null($payment_verified_date) ? $payment_verified_date : "NEVER") . "</span>";
                                                }
                                            }
                                            ?>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="main" valign="top" nowrap><b><?= ENTRY_MB_TRANSACTION_ID ?></b>&nbsp;</td>
                                        <td class="main"><?= $mb_trans_info_row["mb_mb_trans_id"] != 0 ? $mb_trans_info_row["mb_mb_trans_id"] : '&nbsp;' ?></td>
                                    </tr>
                                    <tr>
                                        <td class="main" valign="top" nowrap><b><?= ENTRY_MB_REFERENCE_ID ?></b>&nbsp;</td>
                                        <td class="main" nowrap><?= $mb_trans_info_row["mb_trans_id"] ?></td>
                                    </tr>
                                    <tr>
                                        <td class="main" valign="top" nowrap><b><?= ENTRY_MB_PAYMENT_STATUS ?></b>&nbsp;</td>
                                        <td class="main" nowrap>
                                            <?
                                            echo isset($mb_payment_status[$mb_trans_info_row["mb_status"]]) ? $mb_payment_status[$mb_trans_info_row["mb_status"]] : TEXT_STATUS_UNKNOWN;

                                            if ($order->info['orders_status'] == '1' || $order->info['orders_status'] == '7' || $order->info['orders_status'] == '2') {
                                                echo "&nbsp;&nbsp;";
                                                echo tep_draw_form('mb_status_check_form', FILENAME_ORDERS, tep_get_all_get_params(), 'post');
                                                echo tep_draw_hidden_field('subaction', 'payment_action');
                                                echo tep_draw_hidden_field('payment_action', 'check_trans_status');
                                                echo '<input type="submit" name="MBCheckTransStatusBtn" value="Check Payment Status" title="Check Payment Status" class="inputButton">';
                                                echo "</form>";
                                            }
                                            ?>
                                        </td>
                                    <tr>
                                        <td></td>
                                        <td>
                                            <table border="0" cellspacing="0" cellpadding="2">
                                                <tr>
                                                    <td class="main" colspan="2" nowrap>
                                                        <table border="1" cellspacing="0" cellpadding="2">
                                                            <tr>
                                                                <td class="smallText" nowrap><b><?= TABLE_HEADING_MB_DATE ?></b></td>
                                                                <td class="smallText" nowrap><b><?= TABLE_HEADING_MB_STATUS ?></b></td>
                                                            </tr>
                                                            <?
                                                            while ($mb_trans_history_row = tep_db_fetch_array($mb_trans_history_result_sql)) {
                                                                echo ' 						<tr>
                                                            <td class="smallText" nowrap>' . $mb_trans_history_row['mb_date'] . '</td>
                                                            <td class="smallText" nowrap>' . ($mb_trans_history_row['mb_err_no'] == 200 ? $mb_trans_history_row['mb_err_txt'] . ' - ' . $mb_payment_status[$mb_trans_history_row["mb_status"]] : $mb_trans_history_row['mb_err_no'] . ' : ' . htmlentities($mb_trans_history_row['mb_err_txt'])) . '</td>
                                                        </tr>';
                                                            }
                                                            ?>
                                                        </table>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                        </tr>
                    </table>
                </td>
                <td width="35%">
                    <table border="0" cellspacing="0" cellpadding="2">
                        <tr>
                            <td class="main" valign="top" nowrap><b><?= ENTRY_MB_PAYMENT_CURRENCY ?></b>&nbsp;</td>
                            <td class="main" nowrap>
                                <?
                                if ($mb_trans_info_row['mb_paid_currency'] != $order->info["currency"]) {
                                    echo '<span class="redIndicator">' . $mb_trans_info_row['mb_paid_currency'] . '<br><span class="smallText">Does not match purchase currency.</span></span>';
                                } else {
                                    echo $mb_trans_info_row['mb_paid_currency'];
                                }
                                ?>
                            </td>
                        </tr>
                        <tr>
                            <td class="main" valign="top" nowrap><b><?= ENTRY_MB_PAYMENT_AMOUNT ?></b>&nbsp;</td>
                            <td class="main" nowrap>
                                <?
                                $mc_gross_display_text = $mb_trans_info_row["mb_paid_amount"];

                                $mb_gross_amt = number_format($mb_trans_info_row['mb_paid_amount'], $currencies->currencies[$order->info["currency"]]['decimal_places'], $currencies->currencies[$order->info["currency"]]['decimal_point'], $currencies->currencies[$order->info["currency"]]['thousands_point']);
                                if ($currencies->currencies[$order->info["currency"]]['symbol_left'] . $mb_gross_amt . $currencies->currencies[$order->info["currency"]]['symbol_right'] != strip_tags($order->order_totals['ot_total']['text'])) {
                                    $mc_gross_display_text = '<span class="redIndicator">' . $mb_trans_info_row["mb_paid_amount"] . '<br><span class="smallText">Does not match purchase amount.</span></span>';
                                }

                                echo $mc_gross_display_text;
                                ?>  
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
        </td>
        </tr>
        <?
    }
} else {
    echo $payment_method_title_info;
}
?>
</table>