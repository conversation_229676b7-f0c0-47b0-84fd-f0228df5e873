<?
/*
  	$Id: orders_list.inc.php,v 1.1 2007/02/13 05:47:22 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2007 SKC Venture
  	
  	Released under the GNU General Public License
*/

include_once(DIR_FS_CATALOG_MODULES . 'payment/mb/admin/languages/' . $language . '/mb.lng.php');

$mb_payment_status = array(	'2' => 'Processed',
							'1' => 'Scheduled',
							'0' => 'Pending',
							'-1' => 'Cancelled',
							'-2' => 'Failed');

$mb_trans_info_select_sql = "SELECT * FROM " . TABLE_PAYMENT_MONEYBOOKERS . " WHERE mb_order_id = '" . $order_obj->orders_id . "'";
$mb_trans_info_result_sql= tep_db_query($mb_trans_info_select_sql);
?>
<table border="0" width="100%" cellspacing="0" cellpadding="2">
	<tr>
		<td class="subInvoiceBoxHeading">
			<div style="border-bottom: 1px solid #996600; border-top: 1px solid #996600; padding: 2px;">Payment Details</div>
		</td>
	</tr>
<?
if ($mb_trans_info_row = tep_db_fetch_array($mb_trans_info_result_sql)) {
?>
  	<tr>
    	<td class="main">
      		<table border="0" width="100%" cellspacing="0" cellpadding="2">
      			<tr valign="top">
        			<td width="100%">
              			<table border="0" cellspacing="0" cellpadding="2">
              				<tr>
                				<td class="invoiceRecords" valign="top" nowrap><?=ENTRY_MB_PAYER_EMAIL?></td>
                				<td class="invoiceRecords" valign="top">:&nbsp;</td>
                				<td class="invoiceRecords" nowrap><?=$mb_trans_info_row["mb_payer_email"]?></td>
              				</tr>
              				<tr>
                				<td class="invoiceRecords" valign="top" nowrap><?=ENTRY_MB_TRANSACTION_ID?></td>
                				<td class="invoiceRecords" valign="top">:&nbsp;</td>
                				<td class="invoiceRecords" nowrap><?=$mb_trans_info_row["mb_mb_trans_id"] != 0 ? $mb_trans_info_row["mb_mb_trans_id"] : '&nbsp;'?></td>
              				</tr>
              				<tr>
                				<td class="invoiceRecords" valign="top" nowrap><?=ENTRY_MB_REFERENCE_ID?></td>
                				<td class="invoiceRecords" valign="top">:&nbsp;</td>
                				<td class="invoiceRecords"><?=$mb_trans_info_row["mb_trans_id"]?></td>
              				</tr>
              				<tr>
                				<td class="invoiceRecords" valign="top" nowrap><?=ENTRY_MB_PAYMENT_STATUS?></td>
                				<td class="invoiceRecords" valign="top">:&nbsp;</td>
                				<td class="invoiceRecords" nowrap><?=isset($mb_payment_status[$mb_trans_info_row["mb_status"]]) ? $mb_payment_status[$mb_trans_info_row["mb_status"]] : TEXT_STATUS_UNKNOWN?></td>
              				</tr>
              			</table>
        			</td>
      			</tr>
      		</table>
   		</td>
  	</tr>
<?
} else {
?>
	<tr>
		<td class="invoiceRecords">No further payment information is available.</td>
	</tr>
<?
}
?>
</table>