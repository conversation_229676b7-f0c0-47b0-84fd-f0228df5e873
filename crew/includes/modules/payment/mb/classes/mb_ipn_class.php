<?php
/*
  	$Id: mb_ipn_class.php, v1.0 2005/11/29 18:08:05
  	Author : <PERSON> (<EMAIL>)
  	Title: Moneybookers.com Payment Module V1.0
	
	Revisions:
		Version 1.0 Initial Payment Module
	
  	Copyright (c) 2006 SKC Venture
	
  	Released under the GNU General Public License
*/

class mb_ipn {
  	var $key, $ipn_response;
	
  	function mb_ipn($post_vars) {
    	$this->init($post_vars);
	}
	
  	function init($post_vars) {
    	$this->key = array();
    	$this->ipn_response = '';
    	reset($post_vars);
    	foreach ($post_vars as $var => $val) {
      		$val = tep_db_prepare_input(trim($val));
      		if ($var != '') {
        		$this->key[$var] = $val;
      		}
    	}
    	unset($post_vars);
  	}
	
  	//Test both receiver email address and Merchant ID
  	function validate_receiver_email($receiver_email, $merchant_id) {
  		if (!strcmp(strtolower($receiver_email), strtolower($this->key['pay_to_email'])) && !strcmp(strtolower($merchant_id), strtolower($this->key['merchant_id']))) {
        	return true;
      	} else {
        	return false;
      	}
	}
	
	function validate_transaction_data($order, $payment_method_obj) {
		global $currencies;
		
		$secret_word = $payment_method_obj->mb_secret_word;
		
		// Concatenation of merchant_id, transaction_id, uppercase md5 value of secret word (if empty ignore the whole secret word portion since md5 of empty value != ''), mb_amount, mb_currency and status
		$calculated_md5_str = md5($this->key['merchant_id'] . $this->key['transaction_id'] . (isset($secret_word) && tep_not_null($secret_word) ? strtoupper(md5(strtolower($secret_word))) : '') . $this->key['mb_amount'] . $this->key['mb_currency'] . $this->key['status']);
		$calculated_md5_str = strtoupper($calculated_md5_str);
		
		if ($calculated_md5_str == $this->key['md5sig']) {	// Match
			$mb_payment_data_array = array(	'mb_mb_trans_id' => $this->key['mb_transaction_id'],
											'mb_payer_email' => $this->key['pay_from_email'],
											'mb_paid_currency' => $this->key['currency'],
											'mb_paid_amount' => $this->key['amount']);
			tep_db_perform(TABLE_PAYMENT_MONEYBOOKERS, $mb_payment_data_array, 'update', "mb_trans_id = '" . $this->key['transaction_id'] . "'");
		  	
			$mbCurrency = $order->info['currency'];
	      	$payment_method_obj->get_support_currencies($payment_method_obj->payment_methods_id, false);	// No zone constraint
	      	
			if (!in_array($mbCurrency, $payment_method_obj->mbCurrencies)) {
	        	$mbCurrency = $payment_method_obj->defCurr;
	      	}
	      	
	      	//check the payment_amount
		    $mb_gross_amt = number_format($this->key['amount'], $currencies->currencies[$mbCurrency]['decimal_places'], $currencies->currencies[$mbCurrency]['decimal_point'], $currencies->currencies[$mbCurrency]['thousands_point']);
		    
		    if ( $currencies->currencies[$mbCurrency]['symbol_left'].$mb_gross_amt.$currencies->currencies[$mbCurrency]['symbol_right'] == $order->info['total'] 
		    	&& $mbCurrency == $this->key['currency']) {
				return true;
		    } else {
				$mb_payment_history_data_array = array(	'mb_trans_id' => $this->key['transaction_id'],
		                        						'mb_date' => 'now()');
			    if ( $currencies->currencies[$mbCurrency]['symbol_left'].$mb_gross_amt.$currencies->currencies[$mbCurrency]['symbol_right'] != $order->info['total']) {
			    	$mb_payment_history_data_array['mb_err_txt'] = tep_db_prepare_input('Error: Amount not matched,  ' . $currencies->currencies[$mbCurrency]['symbol_left'].$mb_gross_amt.$currencies->currencies[$mbCurrency]['symbol_right'] . ' != ' . $order->info['total']);
			    } else {
			    	$mb_payment_history_data_array['mb_err_txt'] = tep_db_prepare_input('Error: Currency not matched,  ' . $mbCurrency . ' != ' . $this->key['currency']);
			    }
				tep_db_perform(TABLE_PAYMENT_MONEYBOOKERS_STATUS_HISTORY, $mb_payment_history_data_array);
	        	return false;
	        }
		} else {
			$mb_payment_history_data_array = array(	'mb_trans_id' => $this->key['transaction_id'],
													'mb_date' => 'now()',
	                        						'mb_err_txt' => tep_db_prepare_input('Error: md5 str != ' . $this->key['md5sig'])
	                        						);
			tep_db_perform(TABLE_PAYMENT_MONEYBOOKERS_STATUS_HISTORY, $mb_payment_history_data_array);
			return false;
		}
	}
	
	function get_order_id() {
		$order_id_select_sql = "SELECT mb_order_id 
								FROM " . TABLE_PAYMENT_MONEYBOOKERS . " 
								WHERE mb_trans_id = '" . $this->key['transaction_id'] . "'";
		$order_id_result_sql = tep_db_query($order_id_select_sql);
		if ($order_id_row = tep_db_fetch_array($order_id_result_sql)) {
			return $order_id_row['mb_order_id'];
		}
		
		return false;
	}
	
	function authenticate($order_obj, $orders_id, $payment_method_obj) {
		global $currencies;
		
		$md5_pwd = $payment_method_obj->mb_md5_pwd;
      	
      	// build URL to retrieve transaction result
      	$data = '';
      	$form_data = array( 'email' => $payment_method_obj->mb_email_id,
							'password' => $md5_pwd,
							'action' => 'status_trn',
							'trn_id' => $this->key['transaction_id']
						 );
		// concatenate order information variables to $data
		while (list($key, $value) = each($form_data)) {
			$data .= $key . '=' . urlencode(ereg_replace_dep(',', '', $value)) . '&';
		}
		
		// take the last & out for the string
		$data = substr($data, 0, -1);
		unset($result);
		
      	$url = "https://www.moneybookers.com/app/query.pl";
		
		$agent = "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.0)";
		$ch = curl_init();
		curl_setopt($ch, CURLOPT_URL,$url);
		curl_setopt($ch, CURLOPT_VERBOSE, 1);
		curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 2);
		curl_setopt($ch, CURLOPT_POST, 1);
		curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
		curl_setopt($ch, CURLOPT_TIMEOUT, 120);
		curl_setopt($ch, CURLOPT_USERAGENT, $agent);
		curl_setopt($ch, CURLOPT_RETURNTRANSFER,1);
		curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);  //Windows 2003 Compatibility
		$result = curl_exec($ch);
		curl_close($ch);
		
		$result = urldecode($result);
		$this->ipn_response = $result;
		
		/********************************
		 	get the returned error code
	      	200 -- OK
	      	401 -- Login failed 
	      	402 -- Unknown action
	      	403 -- Transaction not found
	      	404 -- Missing parameter
	      	405 -- Illegal parameter value
		********************************/
		preg_match("/\d{3}/", $result, $return_code);
		
      	switch ($return_code[0]) {
        	// query was OK, data is sent back
        	case "200":
          		$result = strstr($result, "status");
          		$aResult = explode("&", $result);
              	
              	/***********************************************************
          			get the returned data
          			[status] -- (-2) => failed
                   				( 2) => processed
                   				( 1) => scheduled
                       			( 0) => Pending (eg. offline bank transfer)
                       			( -1) => Cancelled (manually cancelled or auto-cancel after 14 days if still in Pending)
          			[mb_transaction_id] -- transaction ID at moneybookers.com
          		/***********************************************************/
          		foreach ($aResult as $value) {
            		list($parameter, $pVal) = explode("=", $value);
            		$aFinal["$parameter"] = $pVal;
          		}
				
				$current_mb_status_select_sql = "	SELECT mb_status 
												 	FROM " . TABLE_PAYMENT_MONEYBOOKERS . "
												 	WHERE mb_trans_id = '" . $this->key['transaction_id'] . "'";
				$current_mb_status_result_sql = tep_db_query($current_mb_status_select_sql);
				$current_mb_status_row = tep_db_fetch_array($current_mb_status_result_sql);
				
				$mb_payment_data_array = array(	'mb_mb_trans_id' => $this->key['mb_transaction_id'],
												'mb_status' => $aFinal['status'],
												'mb_payer_email' => $this->key['pay_from_email'],
												'mb_paid_currency' => $this->key['currency'],
												'mb_paid_amount' => $this->key['amount']);
				tep_db_perform(TABLE_PAYMENT_MONEYBOOKERS, $mb_payment_data_array, 'update', "mb_trans_id = '" . $this->key['transaction_id'] . "'");
			  	
			  	if ($aFinal['status'] != $current_mb_status_row['mb_status']) {	// Only insert history record and perform stock related action if status been change
					$mb_payment_history_data_array = array(	'mb_trans_id' => $this->key['transaction_id'],
		                            						'mb_status' => $aFinal['status'],
		                            						'mb_date' => 'now()'
		                            						);
	          		if ($aFinal["status"] == -2) {
	            		// if there was an error, update the database according to it
	            		$mb_payment_history_data_array['mb_err_no'] = '999';
	            		$mb_payment_history_data_array['mb_err_txt'] = 'Transaction failed.';
						tep_db_perform(TABLE_PAYMENT_MONEYBOOKERS_STATUS_HISTORY, $mb_payment_history_data_array);
						
	            		return false;
	          		} else {
	            		// if everything went good, update the data in the database and in the order class
	            		if ($aFinal["status"] == 2)	{	// Money had reach our mb account
	            			$this->process_this_order($orders_id, $payment_method_obj, $payment_method_obj->order_processing_status);
	            		} else if ($aFinal["status"] == -1) {
	            			if ($this->process_this_order($orders_id, $payment_method_obj, 5)) {
	            				$sc_result_array = $order_obj->manage_order_sc($order_obj->info['orders_status_id'], 5);
				        		
				        		$sc_cancellation_comment = '';
				        		$sc_result_cnt_total = count($sc_result_array);
				        		for ($sc_cnt=0; $sc_cnt < $sc_result_cnt_total; $sc_cnt++) {
				        			if ($sc_cnt > 0)	$sc_cancellation_comment .= "\n";
				        			$sc_cancellation_comment .= sprintf(TEXT_ORDER_INFO_SC_TOTAL, $sc_result_array[$sc_cnt]['sc_type']) . ' ' . $currencies->format($sc_result_array[$sc_cnt]['sc_amount'], false, $sc_result_array[$sc_cnt]['sc_currency']) . '('.$sc_result_array[$sc_cnt]['sc_trans_id'].')';
				        			//$sc_cancellation_comment .= sprintf(TEXT_ORDER_INFO_SC_TOTAL, $sc_result_array[$sc_cnt]['sc_type']) . ' ' . $currencies->format($sc_result_array[$sc_cnt]['sc_amount'], true, $order->info['currency'], $order->info['currency_value']) . '('.$sc_result_array[$sc_cnt]['sc_trans_id'].')';
				        		}
				        		
				        		if ($sc_result_cnt_total > 0) {
					        		$orders_status_history_data_array = array(	'orders_id' => $orders_id,
			                            										'orders_status_id' => 0,
			                            										'date_added' => 'now()',
			                            										'customer_notified' => 0,
			                            										'comments' => $sc_cancellation_comment
			                            										);
					        		tep_db_perform(TABLE_ORDERS_STATUS_HISTORY, $orders_status_history_data_array);
				        		}
				        	}
	            		}
	            		
	            		$mb_payment_history_data_array['mb_err_no'] = '200';
	            		$mb_payment_history_data_array['mb_err_txt'] = 'OK';
						tep_db_perform(TABLE_PAYMENT_MONEYBOOKERS_STATUS_HISTORY, $mb_payment_history_data_array);
						
	            		return true;
	          		}
				}
				
          		break;
        	// error occured during query
        	// errors documented in the moneybookers doc
        	case "401":	// 	Authorization declined
        	case "402":	//	Unknown action
        	case "403": //	Transaction not found
        	case "404":	//	Missing parameter
        	case "405":	//	Illegal parameter value
        		
          		preg_match("/[^\d\t]+.*/i", $result, $return_array);
          		$mb_payment_history_data_array = array(	'mb_trans_id' => $this->key['transaction_id'],
	                            						'mb_date' => 'now()',
	                            						'mb_err_no' => $return_code[0],
	                            						'mb_err_txt' => $return_array[0]
	                            						);
          		tep_db_perform(TABLE_PAYMENT_MONEYBOOKERS_STATUS_HISTORY, $mb_payment_history_data_array);
				
				return false;
          		break;
        	// unknown error
        	default:
          		$mb_payment_history_data_array = array(	'mb_trans_id' => $this->key['transaction_id'],
          												'mb_status' => $this->key['status'],
	                            						'mb_date' => 'now()',
	                            						'mb_err_no' => $return_code[0],
	                            						'mb_err_txt' => $result
	                            						);
          		tep_db_perform(TABLE_PAYMENT_MONEYBOOKERS_STATUS_HISTORY, $mb_payment_history_data_array);
          		
        		return false;
        		
          		break;
		}
	}
	
	function process_this_order($orders_id, $payment_method_obj, $update_to_status) {
		global $currencies, $languages_id;
		
		$status_updated = false;
		
		$orders_status_array = array();
		$orders_status_query = tep_db_query("SELECT orders_status_id, orders_status_name from " . TABLE_ORDERS_STATUS . " WHERE language_id = '" . (int)$languages_id . "' ORDER BY orders_status_sort_order");
		while ($orders_status = tep_db_fetch_array($orders_status_query)) {
			$orders_status_array[$orders_status['orders_status_id']] = $orders_status['orders_status_name'];
		}
		
		// Call this function when money reach our account
		$cur_order = new order($orders_id);
		
		if ($update_to_status == 2 ||$update_to_status == 7) {
			$order_process_status = 1;
		} else {
			$order_process_status = -1;
		}
		
		if ($cur_order->info['orders_status_id'] == $payment_method_obj->order_status) {	// If the order still in initial status
			if ($order_process_status > 0) {
				$order_comment = $payment_method_obj->order_comment_1 . $payment_method_obj->order_comment_2;
				$deduct_stock_for_automated_payment = true;
			} else {
				$order_comment = '';
				$deduct_stock_for_automated_payment = false;
			}
			
			$customer_notification = (SEND_EMAILS == 'true') ? '1' : '0';
			
			$orders_status_history_perform_array = array(	'action' => 'insert',
															'data' => array('orders_id' => $orders_id,
																			'orders_status_id' => $update_to_status,
																			'date_added' => 'now()',
																			'customer_notified' => $customer_notification,
																			'comments' => $order_comment
																		)
														);
			
			$cur_order->update_order_status($update_to_status, $orders_status_history_perform_array, $deduct_stock_for_automated_payment);
			
			unset($orders_status_history_perform_array);
			
			//if ($order_process_status > 0) {
			//	tep_deduct_stock_for_automated_payment($orders_id, $cur_order->products, $cur_order->info['orders_status_id'], $update_to_status);
			//}
			
			$status_updated = true;
		}
		
		$customer_profile_select_sql = "SELECT customers_gender, customers_firstname, customers_lastname 
										FROM " . TABLE_CUSTOMERS . " 
										WHERE customers_id = '" . $cur_order->customer["id"] . "'";
		$customer_profile_result_sql = tep_db_query($customer_profile_select_sql);
		if ($customer_profile_row = tep_db_fetch_array($customer_profile_result_sql)) {
			$email_firstname = $customer_profile_row["customers_firstname"];
			$email_lastname = $customer_profile_row["customers_lastname"];
		} else {
			$email_firstname = $cur_order->customer['name'];
			$email_lastname = $cur_order->customer['name'];
		}
		
		$email_greeting = tep_get_email_greeting($email_firstname, $email_lastname, $customer_profile_row["customers_gender"]);
		
		if ($order_process_status > 0) {
			$email_order = $email_greeting . sprintf(EMAIL_TEXT_ORDER_THANK_YOU, $currencies->format($cur_order->info['total_value'], true, $cur_order->info['currency'])) . "\n\n";
			
			if (is_object($payment_method_obj)) {
				$email_order .= EMAIL_TEXT_PAYMENT_METHOD . "\n" .
			                    EMAIL_SEPARATOR . "\n";
			    $payment_class = $payment_method_obj;
			    $email_order .= strip_tags($payment_class->title) . "\n";
			    if ($payment_class->email_footer) {
			      	$email_order .= $payment_class->email_footer . "\n\n";
			    } else { $email_order .= "\n"; }
			}
			
			if ($cur_order->delivery !== false) {
				$order_shipping_address = tep_address_format($cur_order->delivery['format_id'], $cur_order->delivery, 0, '', "\n");
				$email_order .= EMAIL_TEXT_DELIVERY_ADDRESS . "\n" .
			                   	EMAIL_SEPARATOR . "\n" .
			                   	$order_shipping_address . "\n\n";
			}
			
			$email_order .= EMAIL_TEXT_BILLING_ADDRESS . "\n" .
							EMAIL_SEPARATOR . "\n" .
			                tep_address_format($cur_order->billing['format_id'], $cur_order->billing, 0, '', "\n") . "\n\n";
			
			$email_order .= EMAIL_TEXT_ORDER_SUMMARY . "\n" .
							EMAIL_SEPARATOR . "\n" .
							EMAIL_TEXT_ORDER_NUMBER . ' ' . $orders_id . "\n" .
							EMAIL_TEXT_DATE_ORDERED . ' ' . tep_date_long($cur_order->info['date_purchased']) . ".\n\n";
			
			$email_order .= EMAIL_TEXT_PRODUCTS . "\n" . 
			                EMAIL_SEPARATOR . "\n" . 
			                $this->get_products_ordered($cur_order) . 
			                EMAIL_SEPARATOR . "\n";
			
			for ($i=0, $n=sizeof($cur_order->totals); $i<$n; $i++) {
				$email_order .= strip_tags($cur_order->totals[$i]['title']) . ' ' . strip_tags($cur_order->totals[$i]['text']) . "\n";
			}
					
			$orders_history_query = tep_db_query("select comments from " . TABLE_ORDERS_STATUS_HISTORY . " where orders_id = '" . tep_db_input($orders_id) . "' order by date_added limit 1");
			if (tep_db_num_rows($orders_history_query)) {
				$orders_history = tep_db_fetch_array($orders_history_query);
				$cur_order->info['comments'] = $orders_history['comments'];
			}
			
			if ($order->info['comments']) {
			    $email_order .= "\n" . EMAIL_TEXT_EXTRA_INFO . "\n" . 
			    				tep_db_output(tep_db_prepare_input($cur_order->info['comments']));
			}
			
			$email_order .= "\n\n";
			
			$email_order = $email_order . EMAIL_TEXT_CLOSING . "\n\n" . EMAIL_FOOTER;
			
			tep_mail($cur_order->customer['name'], $cur_order->customer['email_address'], implode(' ', array(EMAIL_SUBJECT_PREFIX, sprintf(EMAIL_TEXT_SUBJECT, tep_db_input($orders_id)))), $email_order, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
			
			// send emails to other people
			if (tep_not_null(SEND_EXTRA_ORDER_EMAILS_TO)) {
				// A copy of email to admin when new order is made
				$extra_email_array = explode(',', SEND_EXTRA_ORDER_EMAILS_TO);
				$extra_email_email_pattern = "/([^<]*?)(?:<)([^>]+)(?:>)/is";
				for ($receiver_cnt=0; $receiver_cnt < count($extra_email_array); $receiver_cnt++) {
					if (preg_match($extra_email_email_pattern, $extra_email_array[$receiver_cnt], $regs)) {
						$receiver_name = trim($regs[1]);
						$receiver_email = trim($regs[2]);
						tep_mail($receiver_name, $receiver_email, implode(' ', array(EMAIL_SUBJECT_PREFIX, sprintf(EMAIL_TEXT_SUBJECT, tep_db_input($orders_id)))), $email_order, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
					}
				}
			}
			
			// Send order update 
			tep_status_update_notification('C', tep_db_input($orders_id), 'system', $cur_order->info['orders_status_id'], $update_to_status, 'A');
	        
			// Send affiliate notification e-mail
			tep_send_affiliate_notification($orders_id, $cur_order);
		} else {
			$email_order = $email_greeting . EMAIL_TEXT_STATUS_UPDATE_TITLE . EMAIL_TEXT_ORDER_NUMBER . ' ' . tep_db_input($orders_id) . "\n"
							. EMAIL_TEXT_DATE_ORDERED . ' ' . tep_date_long($cur_order->info['date_purchased']) . ".\n\n" ;
			
			$email_order .= sprintf(EMAIL_TEXT_UPDATED_STATUS, ((int)$update_to_status > 0 ? $orders_status_array[$cur_order->info['orders_status_id']] . ' -> ' . $orders_status_array[$update_to_status] : $orders_status_array[$cur_order->info['orders_status_id']]) ) 
							. EMAIL_TEXT_CLOSING . "\n\n" . EMAIL_FOOTER;
			
	    	tep_mail($cur_order->customer['name'], $cur_order->customer['email_address'], implode(' ', array(EMAIL_SUBJECT_PREFIX, sprintf(EMAIL_TEXT_ORDER_UPDATE_SUBJECT, tep_db_input($orders_id)))), $email_order, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
		}
		
		return $status_updated;
	}
	
	function get_products_ordered($order) {
		global $currencies, $languages_id;
		
		$products_ordered = '';
		for ($i=0, $n=sizeof($order->products); $i<$n; $i++) {
			$product_instance_id = tep_not_null($order->products[$i]['custom_content']['hla_account_id']) ? $order->products[$i]['custom_content']['hla_account_id'] : '';
	    	$currencies->product_instance_id = $product_instance_id;
	    	
			//------insert customer choosen option to order--------
		    $attributes_exist = '0';
		    $products_ordered_attributes = '';
		    if (isset($order->products[$i]['attributes'])) {
		      	$attributes_exist = '1';
		      	for ($j=0, $n2=sizeof($order->products[$i]['attributes']); $j<$n2; $j++) {
		        	if (DOWNLOAD_ENABLED == 'true') {
		          		$attributes_query = "	select popt.products_options_name, poval.products_options_values_name, pa.options_values_price, pa.price_prefix, pad.products_attributes_maxdays, pad.products_attributes_maxcount , pad.products_attributes_filename
		                               			from " . TABLE_PRODUCTS_OPTIONS . " popt, " . TABLE_PRODUCTS_OPTIONS_VALUES . " poval, " . TABLE_PRODUCTS_ATTRIBUTES . " pa
		                               			left join " . TABLE_PRODUCTS_ATTRIBUTES_DOWNLOAD . " pad
		                                			on pa.products_attributes_id=pad.products_attributes_id
		                               			where pa.products_id = '" . $order->products[$i]['id'] . "'
					                                and pa.options_id = '" . $order->products[$i]['attributes'][$j]['option_id'] . "'
					                                and pa.options_id = popt.products_options_id
					                                and pa.options_values_id = '" . $order->products[$i]['attributes'][$j]['value_id'] . "'
					                                and pa.options_values_id = poval.products_options_values_id
					                                and popt.language_id = '" . $languages_id . "'
					                                and poval.language_id = '" . $languages_id . "'";
		          		$attributes = tep_db_query($attributes_query);
		        	} else {
		          		$attributes = tep_db_query("select popt.products_options_name, poval.products_options_values_name, pa.options_values_price, pa.price_prefix from " . TABLE_PRODUCTS_OPTIONS . " popt, " . TABLE_PRODUCTS_OPTIONS_VALUES . " poval, " . TABLE_PRODUCTS_ATTRIBUTES . " pa where pa.products_id = '" . $order->products[$i]['id'] . "' and pa.options_id = '" . $order->products[$i]['attributes'][$j]['option_id'] . "' and pa.options_id = popt.products_options_id and pa.options_values_id = '" . $order->products[$i]['attributes'][$j]['value_id'] . "' and pa.options_values_id = poval.products_options_values_id and popt.language_id = '" . $languages_id . "' and poval.language_id = '" . $languages_id . "'");
		        	}
		        	$attributes_values = tep_db_fetch_array($attributes);
		        	if ((DOWNLOAD_ENABLED == 'true') && isset($attributes_values['products_attributes_filename']) && tep_not_null($attributes_values['products_attributes_filename']) ) {
		          		$sql_data_array = array('orders_id' => $orders_id,
		                                  		'orders_products_id' => $order->products[$i]['orders_products_id'],
		                                  		'orders_products_filename' => $attributes_values['products_attributes_filename'],
				                                'download_maxdays' => $attributes_values['products_attributes_maxdays'],
				                                'download_count' => $attributes_values['products_attributes_maxcount']);
		          		tep_db_perform(TABLE_ORDERS_PRODUCTS_DOWNLOAD, $sql_data_array);
		        	}
		        	$products_ordered_attributes .= "\n\t" . $attributes_values['products_options_name'] . ' ' . $attributes_values['products_options_values_name'];
		      	}
			}
			//------insert customer choosen option eof ----
		    $total_weight += ($order->products[$i]['qty'] * $order->products[$i]['weight']);
		    $total_tax += tep_calculate_tax($total_products_price, $products_tax) * $order->products[$i]['qty'];
		    $total_cost += $total_products_price;
			
			$cat_path = tep_output_generated_category_path($order->products[$i]['id'], 'product');
		  	$product_email_display_str = $cat_path . (tep_not_null($cat_path) ? ' > ' : '') . $order->products[$i]['name'] . (tep_not_null($order->products[$i]['model']) ? ' (' . $order->products[$i]['model'] . ')' : '');
		    
		    $products_ordered .= $order->products[$i]['qty'] . ' x ' . $product_email_display_str . ' = ' . $currencies->display_price($order->products[$i]['id'], $order->products[$i]['final_price'], $order->products[$i]['tax'], $order->products[$i]['qty']) . $products_ordered_attributes . "\n";
		}
		
		return $products_ordered;
	}
}//end class
?>