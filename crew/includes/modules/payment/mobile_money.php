<?php

/*
  $Id: mobile_money.php,v 1.10 2015/02/16 07:43:47 chingyen Exp $

  Developer: <PERSON>
  Copyright (c) 2007 SKC Ventrue

  Released under the GNU General Public License
 */

class mobile_money {

    var $code, $title, $description, $sort_order, $enabled = false;
    var $email_footer, $mm_currencies, $defCurr;
    var $amount_decimal, $ipay88_merchant_code, $ipay88_merchant_key, $ipay88_pm_ids;
    var $check_trans_status_flag;

    // class constructor
    function mobile_money($pm_id = '') {
        global $order, $languages_id, $default_languages_id;

        //Basic info
        $this->payment_methods_id = 0;
        $this->payment_methods_parent_id = 0;

        $this->code = 'mobile_money';
        $this->filename = 'mobile_money.php';

        $this->force_to_checkoutprocess = true;
        $this->auto_cancel_period = 60; // In minutes
        $this->connect_via_proxy = false;

        $this->has_ipn = true;

        $this->check_trans_status_flag = false;

        $payment_methods_select_sql = "	SELECT payment_methods_parent_id, payment_methods_code, 
												payment_methods_types_id, 
												payment_methods_receive_status_mode, payment_methods_id, 
												payment_methods_title, payment_methods_sort_order, 
												payment_methods_receive_status, payment_methods_legend_color, 
												payment_methods_logo 
										FROM " . TABLE_PAYMENT_METHODS . " 
										WHERE 1 ";
        if ((int) $pm_id > 0) {
            $payment_methods_select_sql .= "	AND payment_methods_id = '" . (int) $pm_id . "'";
        } else {
            $payment_methods_select_sql .= "	AND payment_methods_filename = '" . tep_db_input($this->filename) . "'";
        }

        $payment_methods_result_sql = tep_db_query($payment_methods_select_sql);
        if ($payment_methods_row = tep_db_fetch_array($payment_methods_result_sql)) {
            $pm_desc_title_select_sql = "	SELECT payment_methods_description_title 
											FROM " . TABLE_PAYMENT_METHODS_DESCRIPTION . " 
											WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "' 
												AND (IF (languages_id = '" . tep_db_input($languages_id) . "', 1, IF ((	SELECT COUNT(payment_methods_id) > 0 
																														FROM " . TABLE_PAYMENT_METHODS_DESCRIPTION . " 
																														WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "' 
																															AND languages_id = '" . (int) $languages_id . "'), 0, languages_id = '" . (int) $default_languages_id . "')))";
            $pm_desc_title_result_sql = tep_db_query($pm_desc_title_select_sql);
            $pm_desc_title_row = tep_db_fetch_array($pm_desc_title_result_sql);

            $this->payment_methods_id = (int) $payment_methods_row['payment_methods_id'];
            $this->payment_methods_parent_id = (int) $payment_methods_row['payment_methods_parent_id'];
            $this->code = $payment_methods_row['payment_methods_code'];
            $this->title = $payment_methods_row['payment_methods_title']; //MODULE_PAYMENT_PAYPAL_TEXT_TITLE;
            $this->display_title = $pm_desc_title_row['payment_methods_description_title']; //MODULE_PAYMENT_PAYPAL_TEXT_DISPLAY_TITLE; 	// if title to be display for payment method is different from payment method value
            $this->sort_order = $payment_methods_row['payment_methods_sort_order']; //MODULE_PAYMENT_PAYPAL_SORT_ORDER;
            $this->enabled = $payment_methods_row['payment_methods_receive_status_mode'];
            $this->legend_display_colour = $payment_methods_row['payment_methods_legend_color'];
            $this->receive_status = $payment_methods_row['payment_methods_receive_status'];
            $this->preferred = true;
            $this->description = $this->display_title;
            $this->mm_currencies = $this->get_support_currencies($this->payment_methods_id);

            $configuration_setting_array = $this->load_pm_setting();
            $this->mobile_money_trantype = $configuration_setting_array['MODULE_PAYMENT_MOBILE_MONEY_TRANTYPE'];
            $this->order_status_id = $configuration_setting_array['MODULE_PAYMENT_MOBILE_MONEY_ORDER_STATUS_ID'];
            $this->processing_status_id = $configuration_setting_array['MODULE_PAYMENT_MOBILE_MONEY_PROCESSING_STATUS_ID'];
            $this->message = $configuration_setting_array['MODULE_PAYMENT_MOBILE_MONEY_MESSAGE'];
            $this->email_message = $configuration_setting_array['MODULE_PAYMENT_MOBILE_MONEY_EMAIL_MESSAGE'];
            $this->confirm_complete_days = $configuration_setting_array['MODULE_PAYMENT_MOBILE_MONEY_CONFIRM_COMPLETE'];
            $this->customer_payment_info = $configuration_setting_array['MODULE_PAYMENT_MOBILE_MONEY_CUSTOMER_PAYMENT_INFO'];
            $this->require_address_information = $configuration_setting_array['MODULE_PAYMENT_MOBILE_MONEY_MANDATORY_ADDRESS_FIELD'];
            $this->require_ic_information = $configuration_setting_array['MODULE_PAYMENT_MOBILE_MONEY_MANDATORY_IC_FIELD'];
            $this->require_contact_information = $configuration_setting_array['MODULE_PAYMENT_MOBILE_MONEY_MANDATORY_CONTACT_FIELD'];

            if ((int) $this->order_status_id > 0) {
                $this->order_status = $this->order_status_id;
            } else {
                $this->order_status = DEFAULT_ORDERS_STATUS_ID;
            }

            $this->order_processing_status = $this->processing_status_id;
            $this->email_footer = str_replace(array("<br>", "<BR>", "\r\n"), array("\n", "\n", ''), $this->email_message);
            $this->force_to_checkoutprocess = true;

            $this->amount_decimal = 2;
            $this->auto_cancel_period = 60; // In minutes

            $this->form_action_url = 'https://mmweb.mobile-money.com/MM_WEBLINK/WEBLINK_Tran_Request.aspx';
            $this->form_status_action_url = 'https://mmweb.mobile-money.com/MM_WEBLINK/WEBLINK_Tran_Query.aspx';
        }
    }

    function javascript_validation() {
        return false;
    }

    function selection() {
        global $languages_id, $default_languages_id;

        $selection_array = array();

        $payment_methods_select_sql = "	SELECT payment_methods_id, payment_methods_code, payment_methods_types_id, payment_methods_logo, payment_methods_receive_status_mode 
										FROM " . TABLE_PAYMENT_METHODS . " 
										WHERE payment_methods_receive_status = 1 
											AND payment_methods_receive_status_mode <> 0 
											AND payment_methods_parent_id = '" . (int) $this->payment_methods_id . "' 
										ORDER BY payment_methods_sort_order";
        $payment_methods_result_sql = tep_db_query($payment_methods_select_sql);
        while ($payment_methods_row = tep_db_fetch_array($payment_methods_result_sql)) {
            $pm_desc_title_select_sql = "	SELECT payment_methods_description_title 
											FROM " . TABLE_PAYMENT_METHODS_DESCRIPTION . " 
											WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "' 
												AND (IF (languages_id = '" . tep_db_input($languages_id) . "', 1, IF ((	SELECT COUNT(payment_methods_id) > 0 
																														FROM " . TABLE_PAYMENT_METHODS_DESCRIPTION . " 
																														WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "' 
																															AND languages_id = '" . (int) $languages_id . "'), 0, languages_id = '" . (int) $default_languages_id . "')))";
            $pm_desc_title_result_sql = tep_db_query($pm_desc_title_select_sql);
            $pm_desc_title_row = tep_db_fetch_array($pm_desc_title_result_sql);

            $pm_array = $this->load_pm_setting(1, $payment_methods_row['payment_methods_id']);

            $selection_array[] = array('payment_gateway_code' => $this->code,
                'payment_methods_receive_status_mode' => $payment_methods_row['payment_methods_receive_status_mode'],
                'payment_methods_id' => $payment_methods_row['payment_methods_id'],
                'payment_methods_code' => $payment_methods_row['payment_methods_code'],
                'payment_methods_types_id' => $payment_methods_row['payment_methods_types_id'],
                'payment_methods_parent_id' => $this->payment_methods_id,
                'payment_methods_logo' => $payment_methods_row['payment_methods_logo'],
                'payment_methods_description_title' => $pm_desc_title_row['payment_methods_description_title'],
                'currency' => $this->get_support_currencies($payment_methods_row['payment_methods_id']),
                'show_billing_address' => $pm_array['MODULE_PAYMENT_MOBILE_MONEY_MANDATORY_ADDRESS_FIELD'],
                'confirm_complete_days' => $pm_array['MODULE_PAYMENT_MOBILE_MONEY_CONFIRM_COMPLETE'],
                'show_contact_number' => $pm_array['MODULE_PAYMENT_MOBILE_MONEY_MANDATORY_CONTACT_FIELD'],
                'show_ic' => $pm_array['MODULE_PAYMENT_MOBILE_MONEY_MANDATORY_IC_FIELD']
            );

            if ($payment_methods_row['payment_methods_receive_status_mode'] == '-1') {
                $pm_status_message_select = "	SELECT payment_methods_status_message 
												FROM " . TABLE_PAYMENT_METHODS_STATUS_DESCRIPTION . " 
												WHERE payment_methods_mode = 'RECEIVE' 
													AND payment_methods_status = '-1' 
													AND languages_id = '" . (int) $languages_id . "' 
													AND payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "'";
                $pm_status_message_result = tep_db_query($pm_status_message_select);
                $pm_status_message_row = tep_db_fetch_array($pm_status_message_result);
                $selection_array[sizeof($selection_array) - 1]['payment_methods_status_message'] = $pm_status_message_row['payment_methods_status_message'];
            }
        }

        return $selection_array;
    }

    function set_support_currencies($currency_array) {
        $this->mm_currencies = $currency_array;
    }

    function get_support_currencies($pm_id, $by_zone = true) {
        global $zone_info_array;

        $default_currency = '';
        $support_currencies_array = array();

        $currency_code_select_sql = "	SELECT currency_code, payment_methods_instance_default 
    									FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " 
    									WHERE payment_methods_id = '" . (int) $pm_id . "' 
    									ORDER BY currency_code";
        $currency_code_result_sql = tep_db_query($currency_code_select_sql);

        if (tep_db_num_rows($currency_code_result_sql) > 0) {
            while ($currency_code_row = tep_db_fetch_array($currency_code_result_sql)) {
                $support_currencies_array[] = $currency_code_row['currency_code'];

                if ($currency_code_row['payment_methods_instance_default'] == 1) {
                    $default_currency = $currency_code_row['currency_code'];
                }
            }
        } else {
            if ($this->payment_methods_parent_id > 0) {
                $payment_methods_parent_id = $this->payment_methods_parent_id;
            } else {
                $payment_methods_parent_id = $this->payment_methods_id;
            }

            $parent_currency_code_select_sql = "	SELECT currency_code, payment_methods_instance_default 
			    									FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " 
			    									WHERE payment_methods_id = '" . (int) $payment_methods_parent_id . "' 
			    									ORDER BY currency_code";
            $parent_currency_code_result_sql = tep_db_query($parent_currency_code_select_sql);
            while ($parent_currency_code_row = tep_db_fetch_array($parent_currency_code_result_sql)) {
                $support_currencies_array[] = $parent_currency_code_row['currency_code'];

                if ($parent_currency_code_row['payment_methods_instance_default'] == 1) {
                    $default_currency = $parent_currency_code_row['currency_code'];
                }
            }
        }

        if (tep_not_null($default_currency)) {
            $this->defCurr = $default_currency;
        }

        if ($by_zone) {
            if (isset($zone_info_array)) {
                $support_currencies_array = array_intersect($zone_info_array[3]->zone_currency_id, $support_currencies_array);
                if (count($support_currencies_array)) {
                    array_multisort($support_currencies_array);
                } else {
                    $support_currencies_array = array($default_currency);
                }
            } else {
                $support_currencies_array = array($default_currency);
            }
        } else {
            $this->set_support_currencies($support_currencies_array);
            // All found currencies is supported. Normally this is used for backend script such as IPN call
        }

        return $support_currencies_array;
    }

    function get_require_address_information() {
        return $this->require_address_information;
    }

    function get_confirm_complete_days() {
        return $this->confirm_complete_days;
    }

    function get_pm_info() {
        return $this->message;
    }

    function get_email_pm_info() {
        return $this->email_message;
    }

    function draw_confirm_button() {
        return tep_image_button2('red', 'javascript:document.checkout_confirmation.submit()', MODULE_PAYMENT_MOBILE_MONEY_TEXT_CART, 200);
    }

    function get_confirm_button_caption() {
        return MODULE_PAYMENT_MOBILE_MONEY_TEXT_TITLE;
    }

    function pre_confirmation_check() {
        /*
          global $currency;

          if (in_array($currency, $this->mm_currencies)) {

          }
         */
        return false;
    }

    function process_button() {
        global $_POST, $shipping_cost, $total_cost, $shipping_selected, $shipping_method, $currencies, $currency, $customer_id, $order, $languages_id;
        global $order_logged, $OFFGAMERS;

        if ($this->force_to_checkoutprocess && !tep_session_is_registered('order_logged'))
            return;

        $mm_currencies = $currency;
        if (!in_array($mm_currencies, $this->mm_currencies)) {
            $mm_currencies = $this->currency;
        }
        $this->get_merchant_account($mm_currencies);

        $payment_gateway_amount = number_format($order->info['total'] * $currencies->get_value($mm_currencies), $this->amount_decimal, '.', '');

        $customer_complete_phone_info_array = tep_format_telephone($customer_id);

        $process_button_string =
                tep_draw_hidden_field('mm_cmd', 'TRANX_REQ') .
                tep_draw_hidden_field('mmm_merchantepurseno', $this->mmm_merchantepurseno) .
                tep_draw_hidden_field('mmm_tran_id', $order_logged) .
                tep_draw_hidden_field('trantype', $this->mobile_money_trantype) .
                tep_draw_hidden_field('mmm_rev_tran_id', '') .
                tep_draw_hidden_field('ic', (isset($_SESSION['identify_number']) ? substr($_SESSION['identify_number'], strlen($_SESSION['identify_number']) - 4, 4) : '')) .
                tep_draw_hidden_field('amt', $payment_gateway_amount) .
                tep_draw_hidden_field('ecashno', ((isset($mobile_money_ecashno) && ($this->mobile_money_trantype == 3 || $this->mobile_money_trantype == 4)) ? $mobile_money_ecashno : '')) .
                tep_draw_hidden_field('mmm_desc', 'Purchase from ' . STORE_NAME);
        //tep_draw_hidden_field('mobileno', '+'.$customer_complete_phone_info_array['country_international_dialing_code'] . $_SESSION['customers_telephone']) .

        return $process_button_string;
    }

    // manage returning data from iPay88 (errors, failures, success etc.)
    function before_process() {
        global $order_logged, $_POST, $currencies, $currency, $order, $customer_id;

        if (!tep_session_is_registered('order_logged')) {
            if (!isset($_SESSION['identify_number']) || !tep_not_null($_SESSION['identify_number'])) {
                tep_redirect(tep_href_link(FILENAME_CHECKOUT_PAYMENT, 'error_message=' . urlencode(MODULE_PAYMENT_MOBILE_MONEY_TEXT_ERROR_INVALID_IDENTIFY_NUMBER), 'SSL'));
            }
            if (!isset($_SESSION['customers_telephone']) || !tep_not_null($_SESSION['customers_telephone'])) {
                tep_redirect(tep_href_link(FILENAME_CHECKOUT_PAYMENT, 'error_message=' . urlencode(MODULE_PAYMENT_MOBILE_MONEY_TEXT_ERROR_INVALID_TELEPHONE_NUMBER), 'SSL'));
            }
            return; // This line does not mean there is payment error. It means the first time order been created
        } else {
            ob_start();
            echo "=========================POST===========================<BR>";
            print_r($_POST);
            echo "========================================================<BR>";
            echo "=========================GET===========================<BR>";
            print_r($_GET);
            echo "========================================================<BR>";
            echo "========================REQUEST==========================<BR>";
            print_r($_REQUEST);
            echo "========================================================<BR>";
            echo "========================SESSION=========================<BR>";
            print_r($_SESSION);
            echo "========================================================<BR>";
            $debug_html = ob_get_contents();
            ob_end_clean();
            @tep_mail('<EMAIL>', '<EMAIL>', '[OFFGAMERS] Mobile Money BEFORE PROCESS DEBUG E-MAIL', $debug_html, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
        }
    }

    function after_process() {
        return false;
    }

    function link_to_payment($new_order_id) {
        global $order_logged;

        if (tep_session_is_registered('order_logged'))
            tep_session_unregister('order_logged');

        tep_session_register('order_logged');
        $order_logged = $new_order_id;
        require(DIR_WS_INCLUDES . 'modules/payment/mobile_money/catalog/mobile_money_splash.php');
        return;
    }

    function get_error() {
        $error = array('title' => MODULE_PAYMENT_MOBILE_MONEY_TEXT_ERROR,
            'error' => stripslashes(urldecode($_GET['error'])));
        return $error;
    }

    function check() {
        if (!isset($this->_check)) {
            $this->_check = defined('MODULE_PAYMENT_IPAY88_STATUS');
        }
        return $this->_check;
    }

    function is_processed_order($order_id) {
        /*         * *

          DO!

         * */
    }

    function is_supported_currency($selected_currency) {
        return (in_array($selected_currency, $this->mm_currencies) ? true : false);
    }

    function load_pm_setting($language_id = 1) {
        $pm_setting_array = array();
        //load value from DB
        $payment_configuration_info_select_sql = "	SELECT pci.payment_configuration_info_key, pcid.payment_configuration_info_value
													FROM " . TABLE_PAYMENT_CONFIGURATION_INFO . " as pci
													LEFT JOIN " . TABLE_PAYMENT_CONFIGURATION_INFO_DESCRIPTION . " as pcid 
														ON pci.payment_configuration_info_id = pcid.payment_configuration_info_id
															AND pcid.languages_id = '" . (int) $language_id . "'
													WHERE pci.payment_methods_id = '" . (int) $this->payment_methods_id . "'
													ORDER BY pci.payment_configuration_info_sort_order";
        $payment_configuration_info_result_sql = tep_db_query($payment_configuration_info_select_sql);
        if (tep_db_num_rows($payment_configuration_info_result_sql)) {
            while ($payment_configuration_info_row = tep_db_fetch_array($payment_configuration_info_result_sql)) {
                $pm_setting_array[$payment_configuration_info_row['payment_configuration_info_key']] = $payment_configuration_info_row['payment_configuration_info_value'];
            }
        } else if ((int) $this->payment_methods_parent_id > 0) { //load value from PG
            $payment_configuration_info_select_sql = "	SELECT pci.payment_configuration_info_key, pcid.payment_configuration_info_value
														FROM " . TABLE_PAYMENT_CONFIGURATION_INFO . " as pci
														LEFT JOIN " . TABLE_PAYMENT_CONFIGURATION_INFO_DESCRIPTION . " as pcid 
															ON pci.payment_configuration_info_id = pcid.payment_configuration_info_id
																AND pcid.languages_id = '" . (int) $language_id . "'
														WHERE pci.payment_methods_id = '" . (int) $this->payment_methods_parent_id . "'
														ORDER BY pci.payment_configuration_info_sort_order";
            $payment_configuration_info_result_sql = tep_db_query($payment_configuration_info_select_sql);
            while ($payment_configuration_info_row = tep_db_fetch_array($payment_configuration_info_result_sql)) {
                $pm_setting_array[$payment_configuration_info_row['payment_configuration_info_key']] = $payment_configuration_info_row['payment_configuration_info_value'];
            }
        }
        return $pm_setting_array;
    }

    function get_merchant_account($selected_currency) {
        $payment_methods_instance_id = 0;
        $payment_gateway_instance_select_sql = "SELECT pmi.payment_methods_instance_id, pmi.payment_methods_instance_follow_default, pmi.currency_code 
				 								FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " as pmi
				 								WHERE pmi.payment_methods_id = '" . (int) $this->payment_methods_id . "'
				 								LIMIT 1";
        $payment_gateway_instance_result_sql = tep_db_query($payment_gateway_instance_select_sql);
        if (tep_db_num_rows($payment_gateway_instance_result_sql)) {
            $payment_gateway_instance_select_sql = "SELECT pmi.payment_methods_instance_id, pmi.payment_methods_instance_follow_default 
					 								FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " as pmi
					 								WHERE pmi.currency_code = '" . tep_db_input($selected_currency) . "' 
					 									AND pmi.payment_methods_id = '" . (int) $this->payment_methods_id . "'";
            $payment_gateway_instance_result_sql = tep_db_query($payment_gateway_instance_select_sql);
            if ($payment_gateway_instance_row = tep_db_fetch_array($payment_gateway_instance_result_sql)) {
                if ((int) $payment_gateway_instance_row['payment_methods_instance_follow_default'] > 0) { // Follow Default Currency Setting
                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_follow_default'];
                } else { // Has own setting
                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_id'];
                }
            }
        } else {
            $payment_gateway_instance_select_sql = "SELECT pmi.payment_methods_instance_id, pmi.payment_methods_instance_follow_default 
					 								FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " as pmi
					 								INNER JOIN " . TABLE_PAYMENT_METHODS . " as pm
					 									ON pm.payment_methods_parent_id = pmi.payment_methods_id
					 								WHERE pmi.currency_code = '" . tep_db_input($selected_currency) . "' 
					 									AND pm.payment_methods_id = '" . (int) $this->payment_methods_id . "'";
            $payment_gateway_instance_result_sql = tep_db_query($payment_gateway_instance_select_sql);
            if ($payment_gateway_instance_row = tep_db_fetch_array($payment_gateway_instance_result_sql)) {
                if ((int) $payment_gateway_instance_row['payment_methods_instance_follow_default'] > 0) {
                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_follow_default'];
                } else {
                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_id'];
                }
            }
        }
        $payment_gateway_instance_setting_select_sql = "SELECT pmis.payment_methods_instance_setting_key, pmis.payment_methods_instance_setting_value 
						 								FROM " . TABLE_PAYMENT_METHODS_INSTANCE_SETTING . " as pmis
						 								WHERE pmis.payment_methods_instance_id = '" . (int) $payment_methods_instance_id . "'";
        $payment_gateway_instance_setting_result_sql = tep_db_query($payment_gateway_instance_setting_select_sql);
        while ($payment_gateway_instance_setting_row = tep_db_fetch_array($payment_gateway_instance_setting_result_sql)) {
            switch ($payment_gateway_instance_setting_row['payment_methods_instance_setting_key']) {
                case 'MODULE_PAYMENT_MOBILE_MONEY_MMM_MERCHANTEPURSENO':
                    $this->mmm_merchantepurseno = $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];
                    break;
            }
        }
    }

    function install() {
        $install_configuration_flag = true;

        if ($this->payment_methods_id > 0) {
            if ($this->receive_status != 1) {
                $update_receive_status_sql_data = array("payment_methods_receive_status" => 1);
                tep_db_perform(TABLE_PAYMENT_METHODS, $update_receive_status_sql_data, 'update', "payment_methods_id = '" . (int) $this->payment_methods_id . "'");
            } else {
                $install_configuration_flag = false;
            }
        } else {
            $install_payment_gateway_data_sql = array('payment_methods_code' => $this->code,
                'payment_methods_send_status' => 0,
                'payment_methods_title' => $this->code,
                'payment_methods_types_id' => 0,
                'payment_methods_parent_id' => 0,
                'payment_methods_legend_color' => '#950100',
                'payment_methods_sort_order' => 30,
                'payment_methods_filename' => $this->filename,
                'payment_methods_logo' => '',
                'date_added' => 'now()',
                'last_modified' => 'now()',
                'payment_methods_receive_status' => 1,
                'payment_methods_receive_status_mode' => 0
            );
            tep_db_perform(TABLE_PAYMENT_METHODS, $install_payment_gateway_data_sql);
            $this->payment_methods_id = tep_db_insert_id();
        }
        if ($install_configuration_flag) {
            $install_key_array = array();
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Set Order Status',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_MOBILE_MONEY_ORDER_STATUS_ID',
                    'payment_configuration_info_description' => 'Set the status of orders made with this payment module to this value.',
                    'payment_configuration_info_sort_order' => '1030',
                    'set_function' => 'tep_cfg_pull_down_order_statuses(',
                    'use_function' => 'tep_get_order_status_name',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => '1',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Set Order\'s "Processing" Status',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_MOBILE_MONEY_PROCESSING_STATUS_ID',
                    'payment_configuration_info_description' => 'Set the initial processing status of orders made with this payment module to this value.',
                    'payment_configuration_info_sort_order' => '1035',
                    'set_function' => 'tep_cfg_pull_down_order_statuses(',
                    'use_function' => 'tep_get_order_status_name',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => '7',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Payment Message',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_MOBILE_MONEY_MESSAGE',
                    'payment_configuration_info_description' => 'Payment message will show up during checkout process.',
                    'payment_configuration_info_sort_order' => '1045',
                    'set_function' => 'tep_cfg_textarea(',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'Thank you for shopping at OffGamers.com.',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Payment Email Message',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_MOBILE_MONEY_EMAIL_MESSAGE',
                    'payment_configuration_info_description' => 'This message will show up in order confirmation e-mail.',
                    'payment_configuration_info_sort_order' => '1045',
                    'set_function' => 'tep_cfg_textarea(',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'Thank you for shopping at OffGamers.com.',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Confirm Complete (in days)',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_MOBILE_MONEY_CONFIRM_COMPLETE',
                    'payment_configuration_info_description' => 'The number of days in which an order made by this payment method is confirm completed',
                    'payment_configuration_info_sort_order' => '1050',
                    'set_function' => '',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => '0',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Sale Transaction Type',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_MOBILE_MONEY_TRANTYPE',
                    'payment_configuration_info_description' => 'Sale Transaction Type.',
                    'payment_configuration_info_sort_order' => '1025',
                    'set_function' => 'tep_cfg_key_select_option(	array(\'1\'=>\'MobileNo and IC\', \'2\'=>\'MobileNo\', \'3\'=>\'ECashNo\', \'4\'=>\'ECashNo + MobileNo\', \'97\'=>\'Void the Sale Transaction\' , \'99\'=>\'Reversal\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => '1',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Require Address Information',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_MOBILE_MONEY_MANDATORY_ADDRESS_FIELD',
                    'payment_configuration_info_description' => 'Set address field as required info during checkout.',
                    'payment_configuration_info_sort_order' => '1100',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Customer Payment Info',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_MOBILE_MONEY_CUSTOMER_PAYMENT_INFO',
                    'payment_configuration_info_description' => 'Set to true if this Payment Method required customer input for Payment Information required customer payment info.',
                    'payment_configuration_info_sort_order' => '1105',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Require IC Information',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_MOBILE_MONEY_MANDATORY_IC_FIELD',
                    'payment_configuration_info_description' => 'Set IC field as required info during checkout.',
                    'payment_configuration_info_sort_order' => '1110',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Require Contact Information',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_MOBILE_MONEY_MANDATORY_CONTACT_FIELD',
                    'payment_configuration_info_description' => 'Set Contact field as required info during checkout.',
                    'payment_configuration_info_sort_order' => '1115',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );
            foreach ($install_key_array as $data) {
                tep_db_perform(TABLE_PAYMENT_CONFIGURATION_INFO, $data['info']);
                $payment_conf_id = tep_db_insert_id();

                $data['desc']['payment_configuration_info_id'] = (int) $payment_conf_id;
                tep_db_perform(TABLE_PAYMENT_CONFIGURATION_INFO_DESCRIPTION, $data['desc']);
            }
        }
        return 1;
    }

    function remove() {
        tep_db_query("delete from " . TABLE_CONFIGURATION . " where configuration_key in ('" . implode("', '", $this->keys()) . "')");
    }

    function keys() {
        return array('MODULE_PAYMENT_MOBILE_MONEY_ORDER_STATUS_ID',
            'MODULE_PAYMENT_MOBILE_MONEY_PROCESSING_STATUS_ID',
            'MODULE_PAYMENT_MOBILE_MONEY_MESSAGE',
            'MODULE_PAYMENT_MOBILE_MONEY_EMAIL_MESSAGE',
            'MODULE_PAYMENT_MOBILE_MONEY_CONFIRM_COMPLETE',
            'MODULE_PAYMENT_MOBILE_MONEY_TRANTYPE',
            'MODULE_PAYMENT_MOBILE_MONEY_MANDATORY_ADDRESS_FIELD');
    }

    function configuration_information_keys() {
        return array('MODULE_PAYMENT_MOBILE_MONEY_ORDER_STATUS_ID',
            'MODULE_PAYMENT_MOBILE_MONEY_PROCESSING_STATUS_ID',
            'MODULE_PAYMENT_MOBILE_MONEY_MESSAGE',
            'MODULE_PAYMENT_MOBILE_MONEY_EMAIL_MESSAGE',
            'MODULE_PAYMENT_MOBILE_MONEY_CONFIRM_COMPLETE',
            'MODULE_PAYMENT_MOBILE_MONEY_TRANTYPE',
            'MODULE_PAYMENT_MOBILE_MONEY_MANDATORY_ADDRESS_FIELD',
            'MODULE_PAYMENT_MOBILE_MONEY_CUSTOMER_PAYMENT_INFO',
            'MODULE_PAYMENT_MOBILE_MONEY_MANDATORY_IC_FIELD',
            'MODULE_PAYMENT_MOBILE_MONEY_MANDATORY_CONTACT_FIELD');
    }

    function multi_lang_configuration_info_keys() {
        return array('MODULE_PAYMENT_MOBILE_MONEY_MESSAGE',
            'MODULE_PAYMENT_MOBILE_MONEY_EMAIL_MESSAGE');
    }

    function merchant_information_keys() {
        include_once(DIR_WS_CLASSES . "payment_methods.php");

        return array(
            'MODULE_PAYMENT_MOBILE_MONEY_MMM_MERCHANTEPURSENO' => array("field" => 'text', "mapping" => 'MODULE_PAYMENT_MOBILE_MONEY_LNG_MMM_MERCHANTEPURSENO'),
            'MODULE_PAYMENT_TAX' => array("field" => 'select', 'mapping' => "MODULE_PAYMENT_TAX", "select" => payment_methods::get_tax_option())
        );
    }

    function get_pg_id() {
        return $this->payment_methods_id;
    }

    // Parse the predefinied array to be 'module install' friendly
    // as it is used for select in the module's install() function
    function show_array($aArray, $appendKey = false) {
        $aFormatted = "array(";
        foreach ($aArray as $key => $sVal) {
            if ($appendKey) {
                $aFormatted .= "\'$key\' => \'" . str_replace('\\\\\\\\"', '"', addslashes(addslashes(addslashes($sVal)))) . "\', ";
            } else {
                $aFormatted .= "\'$sVal\', ";
            }
        }
        $aFormatted = substr($aFormatted, 0, strlen($aFormatted) - 2);
        return $aFormatted;
    }

    function check_trans_status($order_id) {
        global $currencies;
        global $mmReturnedStatus;

        $order_info_select_sql = "	SELECT o.payment_methods_id, o.currency, o.currency_value, ot.value 
									FROM " . TABLE_ORDERS . " AS o 
									INNER JOIN " . TABLE_ORDERS_TOTAL . " AS ot 
										ON o.orders_id=ot.orders_id AND ot.class='ot_total' 
									WHERE o.orders_id = '" . tep_db_input($order_id) . "'";
        $order_info_result_sql = tep_db_query($order_info_select_sql);

        if ($order_info_row = tep_db_fetch_array($order_info_result_sql)) {
            if ($order_info_row['payment_methods_id'] != $this->payment_methods_id)
                return;
            $mm_currencies = $order_info_row['currency'];

            if (!in_array($mm_currencies, $this->mm_currencies)) {
                $mm_currencies = $this->currency;
            }
            $this->get_merchant_account($mm_currencies);

            // build URL to retrieve transaction result
            $data = '';
            $post_data = array('mm_cmd' => 'TRANX_QUERY',
                'mmm_merchantepurseno' => $this->mmm_merchantepurseno,
                'mmm_tran_id' => $order_id
            );

            $url = $this->form_status_action_url;

            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $post_data);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($ch, CURLOPT_VERBOSE, 1);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
            curl_setopt($ch, CURLOPT_TIMEOUT, 500);
            if ($this->connect_via_proxy) {
                curl_setopt($ch, CURLOPT_PROXY, 'http://*************:3128');
                curl_setopt($ch, CURLOPT_PROXYUSERPWD, 'devbackend:devonly');
            }

            $result = curl_exec($ch);
            curl_close($ch);

            if (preg_match('/^\d/', trim($result))) {
                preg_match_all("/^([0-9]+) : (.+)/", $result, $matched_result);
                $result_code = $matched_result[1][0];

                $mm_payment_history_data_array = array('orders_id' => (int) $order_id,
                    'money_money_request_date' => 'now()',
                    'mobile_money_tran_status' => '',
                    'mobile_money_tran_description' => $result_code . ' : ' . $this->get_mm_error_msg($result_code)
                );
                tep_db_perform(TABLE_MOBILE_MONEY_HISTORY, $mm_payment_history_data_array);
            } else {
                $this->set_mm_status_from_xml_content($result);

                if ($mmReturnedStatus['tran_status'] == '2') {
                    $this->check_trans_status_flag = true;
                }

                $mm_payment_history_data_array = array('orders_id' => (int) $order_id,
                    'money_money_request_date' => 'now()',
                    'mobile_money_tran_status' => $mmReturnedStatus['tran_status'],
                    'mobile_money_tran_description' => $mmReturnedStatus['tran_errcode'] . ' : ' . $this->get_mm_error_msg($mmReturnedStatus['tran_errcode'])
                );
                tep_db_perform(TABLE_MOBILE_MONEY_HISTORY, $mm_payment_history_data_array);

                $mm_payment_data_array = array('mobile_money_tran_id' => $mmReturnedStatus['tran_id'],
                    'mobile_money_tran_status' => $mmReturnedStatus['tran_status'],
                    'mobile_money_tran_errcode' => $mmReturnedStatus['tran_errcode'],
                    'mobile_money_amt' => $mmReturnedStatus['amt'],
                    'mobile_money_ecash_apprcode' => $mmReturnedStatus['ecash_apprcode'],
                    'mobile_money_tran_mmprocessdt' => $mmReturnedStatus['tran_mmprocessdt']
                );

                // check order exist
                $mm_check_exist_select_sql = "	SELECT orders_id 
												FROM " . TABLE_MOBILE_MONEY . " 
												WHERE orders_id = '" . (int) $order_id . "'";
                $mm_check_exist_result_sql = tep_db_query($mm_check_exist_select_sql);
                if (tep_db_num_rows($mm_check_exist_result_sql)) {
                    tep_db_perform(TABLE_MOBILE_MONEY, $mm_payment_data_array, 'update', ' orders_id = "' . (int) $order_id . '" ');
                } else {
                    $mm_payment_data_array['orders_id'] = (int) $order_id;
                    tep_db_perform(TABLE_MOBILE_MONEY, $mm_payment_data_array);
                }
            }
        }
    }

    function set_mm_xml_content($parser, $text) {
        global $currentTag;
        global $mmReturnedStatus;

        switch (strtolower($currentTag)) {
            case 'mm_cmd':
            case 'mmm_tran_id':
            case 'tran_id':
            case 'tran_status':
            case 'tran_errcode':
            case 'amt':
            case 'mmm_desc':
            case 'ecash_apprcode':
            case 'tran_mmprocessdt':
                $mmReturnedStatus[strtolower($currentTag)] = trim($text);
                break;
            default:
                break;
        }
    }

    function startElement($parser, $name, $attrs) {
        global $currentTag;
        switch (strtolower($name)) {
            case 'mm_cmd':
            case 'mmm_tran_id':
            case 'tran_id':
            case 'tran_status':
            case 'tran_errcode':
            case 'amt':
            case 'mmm_desc':
            case 'ecash_apprcode':
            case 'tran_mmprocessdt':
                $currentTag = strtolower($name);
                break;
            default:
                break;
        }
    }

    function endElement($parser, $name) {
        global $currentTag;
        switch (strtolower($name)) {
            case 'mm_cmd':
            case 'mmm_tran_id':
            case 'tran_id':
            case 'tran_status':
            case 'tran_errcode':
            case 'amt':
            case 'mmm_desc':
            case 'ecash_apprcode':
            case 'tran_mmprocessdt':
                $currentTag = '';
                break;
            default:
                break;
        }
    }

    function set_mm_status_from_xml_content($pass_xml) {
        global $currentTag;
        $parser = xml_parser_create();
        xml_set_element_handler($parser, array($this, 'startElement'), array($this, 'endElement'));
        xml_set_character_data_handler($parser, array($this, "set_mm_xml_content"));
        xml_parse($parser, $pass_xml);
        xml_parser_free($parser);
    }

    function hex2ascii($hex) {
        $ascii = '';
        $hex = str_replace(' ', '', $hex);
        for ($i = 0; $i < strlen($hex); $i = $i + 2) {
            $ascii.= chr(hexdec(substr($hex, $i, 2)));
        }
        return($ascii);
    }

    function get_orders_payment_info_file() {
        return DIR_FS_CATALOG_MODULES . 'payment/mobile_money/admin/orders.inc.php';
    }

    function get_orders_list_payment_info_file() {
        return DIR_FS_CATALOG_MODULES . 'payment/mobile_money/admin/orders_list.inc.php';
    }

    function get_ipn_class_file() {
        return DIR_FS_CATALOG_MODULES . 'payment/mobile_money/classes/mobile_money_ipn_class.php';
    }

    function get_mm_error_msg($pass_code) {
        switch ($pass_code) {
            case '0000':
            case '0': return 'Approved';
                break;
            case '5000': return 'Customer exceeded daily limit';
                break;
            case '5001': return 'Customer exceeded transaction limit';
                break;
            case '5002': return 'Customer not responded after 30 minutes';
                break;
            case '5003': return 'Customer declined to approve the payment';
                break;
            case '5004': return 'Customer account do not have sufficient fund';
                break;
            case '5006': return 'Transaction not approved by the bank';
                break;
            case '5007': return 'Transaction amount required approval from bank';
                break;
            case '5008': return 'Duplication of transmission';
                break;
            case '5009': return 'Account status currently inactive';
                break;
            case '5010': return 'Invalid Mobile Phone number used';
                break;
            case '5011': return 'Invalid Merchant ID submitted';
                break;
            case '5012': return 'Transaction not allowed for this merchant';
                break;
            case '5013': return 'Customer pay by invalid account code';
                break;
            case '5018': return 'Transaction voided by merchant';
                break;
            case '5019': return 'Ecashno not match with MobileNo.';
                break;
            case '5099': return 'Line Down or System Error';
                break;
            case '5100': return 'Invalid eCash Number';
                break;
            case '5101': return 'eCash Number Expired';
                break;
            case '5103': return 'This transaction is not found in the system';
                break;
            case '5104': return 'Not able to retrieve the detail of the transaction';
                break;
            case '5105': return 'Transaction Cannot be reversed';
                break;
            case '5106': return 'Transaction Cannot be refunded';
                break;
            case '5107': return 'Reversal on different amount not allowed';
                break;
            case '5108': return 'Refund Amount exceeded the transaction limit';
                break;
            case '5109': return 'ePurse of customer account invalid';
                break;
            case '5110': return 'Error detected';
                break;
            case '5111': return 'From Bank: Call Bank';
                break;
            case '5112': return 'From Bank: Call Bank to approve transaction';
                break;
            case '5114': return 'From Bank: Card used not honor';
                break;
            case '5115': return 'From Bank: Call IT Help Desk';
                break;
            case '5201': return 'Merchant account status is inactive';
                break;
            case '5202': return 'Customer account status is inactive';
                break;
            case '5203': return 'Transaction amount below the minimum amount set';
                break;
            case '5204': return 'Transaction failed after retry';
                break;
            case '5205': return 'Invalid transaction type';
                break;
            case '5207': return 'Reversal reference number blank';
                break;
            case '5208': return 'Reversal reference number not found';
                break;
            case '5209': return 'Internal Error';
                break;
            case '5210': return 'Value entered for phone number or i/c number is invalid';
                break;
            case '5211': return 'Value entered for phone number is invalid';
                break;
            case '5212': return 'Value entered for eCash number is invalid';
                break;
            case '5213': return 'Phone number or i/c number entered not found in system';
                break;
            case '5214': return 'System Out of Service';
                break;
            case '5231': return 'Internal Error';
                break;
            case '5232': return 'Invalid WebLink Version';
                break;
            case '5300': return 'Invalid Length [mm_cmd]';
                break;
            case '5301': return 'Invalid Length [mmm_merhcnatepurseno]';
                break;
            case '5302': return 'Invald Length [mmm_tran_id]';
                break;
            case '5303': return 'Invalid Length [trantype]';
                break;
            case '5304': return 'Invalid Length [mmm_rev_tran_id]';
                break;
            case '5305': return 'Invalid Length [mobileno]';
                break;
            case '5306': return 'Invalid Length [ic]';
                break;
            case '5307': return 'Invalid Length [ecashno]';
                break;
            case '5308': return 'Invalid Length [amt]';
                break;
            case '5309': return 'Invalid Length [mmm_desc]';
                break;
            case '5310': return 'Required Field [mm_cmd]';
                break;
            case '5311': return 'Required Field [mmm_merchantepurseno]';
                break;
            case '5312': return 'Required Field [mmm_tran_id]';
                break;
            case '5313': return 'Required Filed [trantype]';
                break;
            case '5314': return 'Required Field [mmm_mmm_rev_tran_id]';
                break;
            case '5315': return 'Required Field [mobileno]';
                break;
            case '5316': return 'Required Field [ic]';
                break;
            case '5317': return 'Required Field [ecashno]';
                break;
            case '5318': return 'Required Field [amt]';
                break;
            case '5319': return 'Required Field [mm_desc]';
                break;
            case '5320': return 'Invalid command [mm_cmd]';
                break;
            case '5321': return 'Duplicate Transaction';
                break;
            case '5322': return 'Invalid Value Associate With TranType';
                break;
            case '5323': return 'DB Exception Error';
                break;
            case '5324': return 'Invalid [mmm_verification_url]';
                break;
            case '5325': return 'Invalid [mmm_tran_response_url]';
                break;
            case '5326': return 'Invalid [mmm_query_response_url]';
                break;
            case '5327': return 'Error Queue Transaction';
                break;
            case '5328': return 'Unknown Merchant URL';
                break;
            case '5329': return 'Transaction rejected by merchant after verification';
                break;
            case '5330': return 'Transaction not verify by merchant';
                break;
            case '5331': return 'Communication Link Error:Merchant';
                break;
            case '5332': return 'Invalid Transaction Query Parameter';
                break;
            case '5333': return 'Invalid Transaction ID number used';
                break;
            case '5334': return 'Mandatory fields are not provided';
                break;
            case '5335': return 'Required Field [signature]';
                break;
            case '5336': return 'Invalid Signature';
                break;
            case '5337': return 'Transaction TimeOut';
                break;
            case '5338': return 'Required Filed [Mm_Topup_Tran_ID]';
                break;
            case '5339': return 'Invalid Length [InsTerm]';
                break;
            case '5340': return 'Invalid Installment Term';
                break;
            case '5341': return 'Invalid EPP Merchant';
                break;
            case '5342': return 'Below EPP Min Amount';
                break;
            case '5343': return 'Unknown EPP Error';
                break;
            case '5344': return 'Invalid eAccount';
                break;
            case '5345': return 'Insufficient balance';
                break;
            default: return $pass_code;
                break;
        }
    }

}

?>