<?
define('TABLE_HEADING_MM_DATE', 'History Date');
define('TABLE_HEADING_MM_STATUS', 'Status');
define('TABLE_HEADING_MM_DESCRIPTION', 'Description');

define('TEXT_STATUS_UNKNOWN', 'Unknown');

//key language
define('MODULE_PAYMENT_MOBILE_MONEY_LNG_MMM_MERCHANTEPURSENO', 'Merchant ID');
define('MODULE_PAYMENT_TAX', 'Tax');

define('ENTRY_MM_TRANSACTION_ID', 'Transaction ID:');
define('ENTRY_MM_ERR_CODE', 'Tran Err Code:');
define('ENTRY_MM_ECASH_APPR_CODE', 'Ecash Appr Code:');
define('ENTRY_MM_CURRENCY', 'Currency:');
define('ENTRY_MM_AMOUNT', 'Amount:');
define('ENTRY_MM_TRANSACTION_DATE', 'Transaction Date:');
define('ENTRY_MM_PAYMENT_STATUS', 'Payment Status:');

define('TEXT_INFO_CURRENCY_NOT_FROM_MM', 'Currency not returned by Mobile Money');
?>