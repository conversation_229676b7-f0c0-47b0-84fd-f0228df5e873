<?
include_once(DIR_FS_CATALOG_MODULES . 'payment/mobile_money/admin/languages/' . $language . '/mobile_money.lng.php');

$mm_trans_info_select_sql = "SELECT * FROM " . TABLE_MOBILE_MONEY . " WHERE orders_id='" . (int)$oID . "'";
$mm_trans_info_result_sql= tep_db_query($mm_trans_info_select_sql);

$mm_payment_status_history_info_select_sql = "SELECT * FROM " . TABLE_MOBILE_MONEY_HISTORY . " WHERE orders_id = '" . tep_db_input($oID) . "'";
$mm_payment_status_history_info_result_sql = tep_db_query($mm_payment_status_history_info_select_sql);
?>
<table border="0" width="100%" cellspacing="0" cellpadding="2">
<? ob_start(); 

?>
	<tr>
		<td class="main">
      		<table border="0" width="100%" cellspacing="0" cellpadding="2">
      			<tr>
      				<td class="main" width="12%" nowrap><b><?=ENTRY_PAYMENT_METHOD?></b></td>
      				<td class="main">
      					<div>
      						<? if((int)$order->info['payment_methods_id']>0) { ?><div class="<?=((int)$payment_info_array[$order->info['payment_methods_id']]->confirm_complete_days>0?'rp_icon':'nrp_icon')?>"><b><?=((int)$payment_info_array[$order->info['payment_methods_id']]->confirm_complete_days>0?'RP':'NRP')?></b></div> <? } ?>
      						<div style="width:30px; height:15px; background-color:<?=$payment_module_info['display_colour']?>; float:left; vertical-align:top; text-align:center;">&nbsp;</div>
      						<div style="vertical-align: top">
      							&nbsp;&nbsp;
<?
	if ($view_payment_details_permission) {
		if (tep_not_null($last_payment_method) && $last_payment_method['payment_methods_id'] != $order->info['payment_methods_id']) {
			echo '<span class="redIndicator">'.(tep_not_null($payment_module_info['payment_methods_parent_title'])?$payment_module_info['payment_methods_parent_title'] . ' - ' :'') . $payment_module_info['payment_methods_title'].'</span>';
		} else {
			echo (tep_not_null($payment_module_info['payment_methods_parent_title'])?$payment_module_info['payment_methods_parent_title'] . ' - ' :'') . $payment_module_info['payment_methods_title'];
		}
		
		if (tep_not_null($last_payment_method) && $last_payment_method['payment_methods_id'] != $order->info['payment_methods_id']) {
			echo '&nbsp;&nbsp;<span class="redIndicator">(Last Payment Method: ' . (tep_not_null($last_payment_method['parent_payment_methods_title'])? $last_payment_method['parent_payment_methods_title'] . ' - ' : '' ) . $last_payment_method['payment_methods_title'] . ')</span>';
		}
	} else {
		echo (tep_not_null($payment_module_info['payment_methods_parent_title'])?$payment_module_info['payment_methods_parent_title'] . ' - ' :'') . $payment_module_info['payment_methods_title'];
	}
?>
							</div>
      					</div>
      				</td>
				</tr>
			</table>
		</td>
	</tr>
<?
$payment_method_title_info = ob_get_contents();
ob_end_clean();

if (tep_db_num_rows($mm_trans_info_result_sql) || tep_db_num_rows($mm_payment_status_history_info_result_sql)) {
	
	echo $payment_method_title_info;
	
	if (!$view_payment_details_permission) {
		;
	} else {
		
		$mm_trans_info_row = tep_db_fetch_array($mm_trans_info_result_sql);
?>
  	<tr>
    	<td class="main">
      		<table border="0" width="100%" cellspacing="0" cellpadding="2">
      			<tr valign="top">
        			<td width="39%" nowrap>
              			<table border="0" cellspacing="0" cellpadding="2">
              				<tr>
                				<td class="main" valign="top" nowrap><b><?=ENTRY_MM_TRANSACTION_ID?></b>&nbsp;</td>
                				<td class="main" nowrap><?=$mm_trans_info_row['mobile_money_tran_id']?></td>
              				</tr>
              				<tr>
                				<td class="main" valign="top" nowrap><b><?=ENTRY_MM_ERR_CODE?></b>&nbsp;</td>
                				<td class="main" nowrap><?=$mm_trans_info_row['mobile_money_tran_errcode']?></td>
              				</tr>
							<tr>
			    				<td class="main" valign="top" nowrap><b><?=ENTRY_MM_CURRENCY?></b>&nbsp;</td>
			    				<td class="main" nowrap><?=$mm_trans_info_row["mobile_money_currency"] . ' ('.TEXT_INFO_CURRENCY_NOT_FROM_MM.')'?></td>
			  				</tr>
              				<tr>
                				<td class="main" valign="top" nowrap><b><?=ENTRY_MM_AMOUNT?></b>&nbsp;</td>
                				<td class="main" nowrap><?=$mm_trans_info_row['mobile_money_amt']?></td>
              				</tr>
              				<tr>
                				<td class="main" valign="top" nowrap><b><?=ENTRY_MM_ECASH_APPR_CODE?></b>&nbsp;</td>
                				<td class="main" nowrap><?=$mm_trans_info_row['mobile_money_ecash_apprcode']?></td>
              				</tr>
              				<tr>
                				<td class="main" valign="top" nowrap><b><?=ENTRY_MM_TRANSACTION_DATE?></b>&nbsp;</td>
                				<td class="main" nowrap><?=$mm_trans_info_row['mobile_money_tran_mmprocessdt']?></td>
              				</tr>
        					<tr>
                				<td class="main" valign="top" nowrap><b><?=ENTRY_MM_PAYMENT_STATUS?></b>&nbsp;</td>
                				<td class="main" nowrap>
                				<?
                					$mm_status_array = array(	'1'=>'Under Processing(Waiting For Customer Confirmation)',
                												'2'=>'Transaction Success',
                												'9'=>'Transaction Failed');
                					
                				
                					if (tep_not_null($mm_trans_info_row['mobile_money_tran_status'])) {
                					echo (isset($mm_status_array[$mm_trans_info_row['mobile_money_tran_status']])?$mm_status_array[$mm_trans_info_row['mobile_money_tran_status']]:'Unknown');
                					} else {
                						echo TEXT_STATUS_UNKNOWN;
                					}
									
                					if ($order->info['orders_status'] == '1' || $order->info['orders_status'] == '7' || $order->info['orders_status'] == '2') {
	                					echo "&nbsp;&nbsp;";
										echo tep_draw_form('mm_status_check_form', FILENAME_ORDERS, tep_get_all_get_params(), 'post');
										echo tep_draw_hidden_field('subaction', 'payment_action');
										echo tep_draw_hidden_field('payment_action', 'check_trans_status');
										echo '<input type="submit" name="MMCheckTransStatusBtn" value="Check Payment Status" title="Check Payment Status" class="inputButton">';
										echo "</form>";
									}
								?>
                				</td>
              				</tr>
              			</table>
        			</td>
        			<td>
        				<table border="0" cellspacing="0" cellpadding="1">
        					<tr>
        						<td colspan="2">
									<table border="1" cellspacing="0" cellpadding="2">
		        						<tr>
		        							<td class="smallText" nowrap><b><?=TABLE_HEADING_MM_DATE?></b></td>
		        							<td class="smallText" nowrap><b><?=TABLE_HEADING_MM_STATUS?></b></td>
		        							<td class="smallText" nowrap><b><?=TABLE_HEADING_MM_DESCRIPTION?></b></td>
		        						</tr>
<?		while ($mm_payment_status_history_info_row = tep_db_fetch_array($mm_payment_status_history_info_result_sql)) {
        	echo ' 						<tr>
                							<td class="smallText" nowrap>'.$mm_payment_status_history_info_row['money_money_request_date'].'</td>
                							<td class="smallText" nowrap>'.(isset($mm_status_array[$mm_payment_status_history_info_row['mobile_money_tran_status']])?$mm_status_array[$mm_payment_status_history_info_row['mobile_money_tran_status']]:'Unknown').'</td>
                							<td class="smallText" nowrap>'.htmlentities($mm_payment_status_history_info_row['mobile_money_tran_description']).'</td>
                						</tr>';
     	}
?>
									</table>
								</td>
        					</tr>
        				</table>
        			</td>
  				</tr>
      		</table>
   		</td>
  	</tr>
<?
	}
} else {
	echo $payment_method_title_info;
	
	if ($order->info['orders_status'] == '1' || $order->info['orders_status'] == '7' || $order->info['orders_status'] == '2') {
		echo '<tr>
    			<td class="main">';
		echo tep_draw_form('bb_status_check_form', FILENAME_ORDERS, tep_get_all_get_params(), 'post');
		echo tep_draw_hidden_field('subaction', 'payment_action');
		echo tep_draw_hidden_field('payment_action', 'check_trans_status');
		echo '<input type="submit" name="BBCheckTransStatusBtn" value="Check Payment Status" title="Check Payment Status" class="inputButton">';
		echo '</form>
				</td>
			  </tr>';
	}
}
?>
</table>