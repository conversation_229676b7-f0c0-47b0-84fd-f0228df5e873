<?
include_once(DIR_FS_CATALOG_MODULES . 'payment/mobile_money/admin/languages/' . $language . '/mobile_money.lng.php');

$mm_trans_info_select_sql = "SELECT * FROM " . TABLE_MOBILE_MONEY . " WHERE orders_id = '" . $order_obj->orders_id . "'";
$mm_trans_info_result_sql= tep_db_query($mm_trans_info_select_sql);
?>
<table border="0" width="100%" cellspacing="0" cellpadding="2">
	<tr>
		<td class="subInvoiceBoxHeading">
			<div style="border-bottom: 1px solid #996600; border-top: 1px solid #996600; padding: 2px;">Payment Details</div>
		</td>
	</tr>
<?
if ($mm_trans_info_row = tep_db_fetch_array($mm_trans_info_result_sql)) {
	$mm_status_array = array(	'1'=>'Under Processing(Waiting For Customer Confirmation)',
								'2'=>'Transaction Success',
								'9'=>'Transaction Failed');
?>
	<tr>
    	<td class="main">
      		<table border="0" width="100%" cellspacing="0" cellpadding="2">
      			<tr valign="top">
        			<td width="50%">
              			<table border="0" cellspacing="0" cellpadding="2">
              				<tr>
                				<td class="invoiceRecords" valign="top" nowrap><?=ENTRY_MM_TRANSACTION_ID?></td>
                				<td class="invoiceRecords" valign="top">:&nbsp;</td>
                				<td class="invoiceRecords" nowrap><?=$mm_trans_info_row['mobile_money_tran_id']?></td>
              				</tr>
              				<tr>
                				<td class="invoiceRecords" valign="top" nowrap><?=ENTRY_MM_PAYMENT_STATUS?></td>
                				<td class="invoiceRecords" valign="top">:&nbsp;</td>
                				<td class="invoiceRecords" nowrap><?=(isset($mm_status_array[$mm_trans_info_row['mobile_money_tran_status']])?$mm_status_array[$mm_trans_info_row['mobile_money_tran_status']]:'Unknown')?></td>
              				</tr>
              			</table>
        			</td>
      			</tr>
      		</table>
   		</td>
  	</tr>
<?
} else {
?>
	<tr>
		<td class="invoiceRecords">No further payment information is available.</td>
	</tr>
<?
}
?>