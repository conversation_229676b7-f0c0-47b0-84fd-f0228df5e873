<?php
/*
  	$Id: mobile_money_ipn_class.php, v1.0 2005/11/29 18:08:05
  	Author : <PERSON> (<EMAIL>)
  	Title: WebMoney Payment Module V1.0
	
	Revisions:
		Version 1.0 Initial Payment Module
	
  	Copyright (c) 2007 SKC Venture
	
  	Released under the GNU General Public License
*/

class mobile_money_ipn {
  	var $key, $ipn_response;
	
  	function mobile_money_ipn($post_vars) {
    	$this->init($post_vars);
	}
	
  	function init($post_vars) {
    	$this->key = array();
    	$this->ipn_response = '';
    	reset($post_vars);
    	foreach ($post_vars as $var => $val) {
      		$val = tep_db_prepare_input(trim($val));
      		if ($var != '') {
        		$this->key[$var] = $val;
      		}
    	}
    	unset($post_vars);
  	}
	
  	function validate_receiver_account($merchantepurseno) {
  		if (!strcmp(strtolower($merchantepurseno), strtolower(trim($this->key['mmm_merchantepurseno'])))) {
        	return true;
      	} else {
			$mm_payment_history_data_array = array(	'orders_id' => (int)$this->key['mmm_tran_id'], 
											  		'money_money_request_date' => 'now()',
											  		'mobile_money_tran_status' => '',
											  		'mobile_money_tran_description' => 'Error: Merchant Account ID not match'
						                      		);
			tep_db_perform(TABLE_MOBILE_MONEY_HISTORY, $mm_payment_history_data_array);	
        	return false;
      	}
	}
	
	function validate_transaction_data($payment_method_obj, $order, $pre_request=false) {
		$payment_record_select_sql = "	SELECT orders_id 
										FROM " . TABLE_MOBILE_MONEY . " 
										WHERE orders_id = '".tep_db_input($this->key['mmm_tran_id'])."'";
		$payment_record_result_sql = tep_db_query($payment_record_select_sql);
		if (tep_db_num_rows($payment_record_result_sql)) {
			return false;
		}
		return true;
	}
	
	function get_order_id() {
		return $this->key['mmm_tran_id'];
	}
	
	function authenticate($order, $orders_id, $payment_method_obj) {
		global $currencies, $mmReturnedStatus;
		
		$form_data = array( 'mm_cmd' => 'TRANX_QUERY',
							'mmm_merchantepurseno' => $payment_method_obj->mmm_merchantepurseno,
							'mmm_tran_id' => $orders_id
						 );
		$url = $payment_method_obj->form_status_action_url;
      	$agent = "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.0)";
		$ch = curl_init();
		curl_setopt($ch, CURLOPT_URL,$url);
		curl_setopt($ch, CURLOPT_VERBOSE, 1);
		curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 2);
		curl_setopt($ch, CURLOPT_POST, 1);
		curl_setopt($ch, CURLOPT_POSTFIELDS, $form_data);
		curl_setopt($ch, CURLOPT_TIMEOUT, 120);
		curl_setopt($ch, CURLOPT_USERAGENT, $agent);
		curl_setopt($ch, CURLOPT_RETURNTRANSFER,1);
		curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);  //Windows 2003 Compatibility
		if ($payment_method_obj->connect_via_proxy) {
			curl_setopt($ch, CURLOPT_PROXY, 'http://mvy-proxy:3128');
			curl_setopt($ch, CURLOPT_PROXYUSERPWD, 'devbackend:devonly');
		}
		$result = curl_exec($ch);
		curl_close($ch);
		
		if (preg_match('/^\d/',trim($result))) {
			preg_match_all("/^([0-9]+) : (.+)/", $result, $matched_result);
			$result_code = $matched_result[1][0];
			
			$mm_payment_history_data_array = array(	'orders_id' => (int)$orders_id, 
											  		'money_money_request_date' => 'now()',
											  		'mobile_money_tran_status' => '',
											  		'mobile_money_tran_description' => $result_code . ' : ' . $payment_method_obj->get_mm_error_msg($result_code)
						                      		);
			tep_db_perform(TABLE_MOBILE_MONEY_HISTORY, $mm_payment_history_data_array);
		} else {
			
			$payment_method_obj->set_mm_status_from_xml_content($result);
			
			$payment_amount = number_format($order->info['total_value'] * $order->info['currency_value'], $currencies->currencies[$order->info['currency']]['decimal_places']);
			if ($payment_amount != $mmReturnedStatus['amt']) {
				$mm_payment_history_data_array = array(	'orders_id' => (int)$orders_id, 
												  		'money_money_request_date' => 'now()',
												  		'mobile_money_tran_status' => '',
												  		'mobile_money_tran_description' => 'Error: Amount not match (' . $payment_amount . ' != '. $mmReturnedStatus['amt'] . ')'
							                      		);
				tep_db_perform(TABLE_MOBILE_MONEY_HISTORY, $mm_payment_history_data_array);
	      		return false;
			}
			
			$mm_payment_history_data_array = array(	'orders_id' => (int)$orders_id, 
											  		'money_money_request_date' => 'now()',
											  		'mobile_money_tran_status' => $mmReturnedStatus['tran_status'],
											  		'mobile_money_tran_description' => $mmReturnedStatus['tran_errcode'] . ' : ' . $payment_method_obj->get_mm_error_msg($mmReturnedStatus['tran_errcode'])
						                      		);
			tep_db_perform(TABLE_MOBILE_MONEY_HISTORY, $mm_payment_history_data_array);
			
			$mm_payment_data_array = array(	'mobile_money_tran_id' => $mmReturnedStatus['tran_id'],
									  		'mobile_money_tran_status' => $mmReturnedStatus['tran_status'],
									  		'mobile_money_tran_errcode' => $mmReturnedStatus['tran_errcode'],
									  		'mobile_money_amt' => $mmReturnedStatus['amt'],
									  		'mobile_money_currency' => $order->info['currency'],
									  		'mobile_money_ecash_apprcode' => $mmReturnedStatus['ecash_apprcode'],
									  		'mobile_money_tran_mmprocessdt' => $mmReturnedStatus['tran_mmprocessdt']
				                      		);
				                      		
			// check order exist
			$mm_check_exist_select_sql = "	SELECT orders_id 
											FROM " . TABLE_MOBILE_MONEY . " 
											WHERE orders_id = '".(int)$orders_id."'";
			$mm_check_exist_result_sql = tep_db_query($mm_check_exist_select_sql);
			if (tep_db_num_rows($mm_check_exist_result_sql)) {
				tep_db_perform(TABLE_MOBILE_MONEY, $mm_payment_data_array, 'update', ' orders_id = "'.(int)$orders_id.'" ');
			} else {
				$mm_payment_data_array['orders_id'] = (int)$orders_id;
				tep_db_perform(TABLE_MOBILE_MONEY, $mm_payment_data_array);
			}
			
			/*	Transaction Status / tran_status
				1 Under Processing(Waiting For Customer Confirmation)
				2 Transaction Success
				9 Transaction Failed	*/
			
			if ($mmReturnedStatus['tran_status'] == '2') {	// Only update order status if this is operative mode
				$this->process_this_order($orders_id, $payment_method_obj, $payment_method_obj->order_processing_status);
			}
		}
		return true;
	}
	
	function process_this_order($orders_id, $payment_method_obj, $update_to_status) {
		global $currencies, $languages_id;
		
		$orders_status_array = array();
		$orders_status_query = tep_db_query("SELECT orders_status_id, orders_status_name from " . TABLE_ORDERS_STATUS . " WHERE language_id = '" . (int)$languages_id . "' ORDER BY orders_status_sort_order");
		while ($orders_status = tep_db_fetch_array($orders_status_query)) {
			$orders_status_array[$orders_status['orders_status_id']] = $orders_status['orders_status_name'];
		}
		
		// Call this function when money reach our account
		$cur_order = new order($orders_id);
		
		if ($cur_order->info['orders_status_id'] == $payment_method_obj->order_status) {	// If the order still in initial status
			$order_comment = '';
			$customer_notification = (SEND_EMAILS == 'true') ? '1' : '0';
			
			//tep_db_query("insert into " . TABLE_ORDERS_STATUS_HISTORY . " (orders_id, orders_status_id, date_added, customer_notified, comments) values ('" . tep_db_input($orders_id) . "', '" . $update_to_status . "', now(), '" . $customer_notification . "', '" . tep_db_input($order_comment) . "')");
			
			$orders_status_history_perform_array = array(	'action' => 'insert',
															'data' => array('orders_id' => $orders_id,
																			'orders_status_id' => $update_to_status,
																			'date_added' => 'now()',
																			'customer_notified' => $customer_notification,
																			'comments' => $order_comment
																		)
														);
			
			$cur_order->update_order_status($update_to_status, $orders_status_history_perform_array, true);
			
			unset($orders_status_history_perform_array);
			
			//tep_deduct_stock_for_automated_payment($orders_id, $cur_order->products, $cur_order->info['orders_status_id'], $update_to_status);
			
			// E-mail section
			$customer_profile_select_sql = "SELECT customers_gender, customers_firstname, customers_lastname 
											FROM " . TABLE_CUSTOMERS . " 
											WHERE customers_id = '" . tep_db_input($cur_order->customer['id']) . "'";
			$customer_profile_result_sql = tep_db_query($customer_profile_select_sql);
			if ($customer_profile_row = tep_db_fetch_array($customer_profile_result_sql)) {
				$email_firstname = $customer_profile_row["customers_firstname"];
				$email_lastname = $customer_profile_row["customers_lastname"];
			} else {
				$email_firstname = $cur_order->customer['name'];
				$email_lastname = $cur_order->customer['name'];
			}
			
			$email_greeting = tep_get_email_greeting($email_firstname, $email_lastname, $customer_profile_row["customers_gender"]);
			
			$email_order = $email_greeting . sprintf(EMAIL_TEXT_ORDER_THANK_YOU, $currencies->format($cur_order->info['total_value'], true, $cur_order->info['currency'])) . "\n\n";
			
			if (is_object($payment_method_obj)) {
				$email_order .= EMAIL_TEXT_PAYMENT_METHOD . "\n" .
			                    EMAIL_SEPARATOR . "\n";
			    $payment_class = $payment_method_obj;
			    $email_order .= strip_tags($payment_class->title) . "\n";
			    if ($payment_class->email_footer) {
			      	$email_order .= $payment_class->email_footer . "\n\n";
			    } else { $email_order .= "\n"; }
			}
			
			if ($cur_order->delivery !== false) {
				$order_shipping_address = tep_address_format($cur_order->delivery['format_id'], $cur_order->delivery, 0, '', "\n");
				$email_order .= EMAIL_TEXT_DELIVERY_ADDRESS . "\n" .
			                   	EMAIL_SEPARATOR . "\n" .
			                   	$order_shipping_address . "\n\n";
			}
			
			$email_order .= EMAIL_TEXT_BILLING_ADDRESS . "\n" .
							EMAIL_SEPARATOR . "\n" .
			                tep_address_format($cur_order->billing['format_id'], $cur_order->billing, 0, '', "\n") . "\n\n";
			
			$email_order .= EMAIL_TEXT_ORDER_SUMMARY . "\n" .
							EMAIL_SEPARATOR . "\n" .
							EMAIL_TEXT_ORDER_NUMBER . ' ' . $orders_id . "\n" .
							EMAIL_TEXT_DATE_ORDERED . ' ' . tep_date_long($cur_order->info['date_purchased']) . ".\n\n";
			
			$email_order .= EMAIL_TEXT_PRODUCTS . "\n" . 
			                EMAIL_SEPARATOR . "\n" . 
			                $this->get_products_ordered($cur_order) . 
			                EMAIL_SEPARATOR . "\n";
			
			for ($i=0, $n=sizeof($cur_order->totals); $i<$n; $i++) {
				$email_order .= strip_tags($cur_order->totals[$i]['title']) . ' ' . strip_tags($cur_order->totals[$i]['text']) . "\n";
			}
			
			$orders_history_query = tep_db_query("select comments from " . TABLE_ORDERS_STATUS_HISTORY . " where orders_id = '" . tep_db_input($orders_id) . "' order by date_added limit 1");
			if (tep_db_num_rows($orders_history_query)) {
				$orders_history = tep_db_fetch_array($orders_history_query);
				$cur_order->info['comments'] = $orders_history['comments'];
			}
			
			if ($order->info['comments']) {
			    $email_order .= "\n" . EMAIL_TEXT_EXTRA_INFO . "\n" . 
			    				tep_db_output(tep_db_prepare_input($cur_order->info['comments']));
			}
			
			$email_order .= "\n\n";
			
			$email_order = $email_order . EMAIL_TEXT_CLOSING . "\n\n" . EMAIL_FOOTER;
			
			tep_mail($cur_order->customer['name'], $cur_order->customer['email_address'], implode(' ', array(EMAIL_SUBJECT_PREFIX, sprintf(EMAIL_TEXT_SUBJECT, tep_db_input($orders_id)))), $email_order, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
			
			// send emails to other people
			if (tep_not_null(SEND_EXTRA_ORDER_EMAILS_TO)) {
				// A copy of email to admin when new order is made
				$extra_email_array = explode(',', SEND_EXTRA_ORDER_EMAILS_TO);
				$extra_email_email_pattern = "/([^<]*?)(?:<)([^>]+)(?:>)/is";
				for ($receiver_cnt=0; $receiver_cnt < count($extra_email_array); $receiver_cnt++) {
					if (preg_match($extra_email_email_pattern, $extra_email_array[$receiver_cnt], $regs)) {
						$receiver_name = trim($regs[1]);
						$receiver_email = trim($regs[2]);
						tep_mail($receiver_name, $receiver_email, implode(' ', array(EMAIL_SUBJECT_PREFIX, sprintf(EMAIL_TEXT_SUBJECT, tep_db_input($orders_id)))), $email_order, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
					}
				}
			}
			
			// Send order update 
			tep_status_update_notification('C', tep_db_input($orders_id), 'system', $cur_order->info['orders_status_id'], $update_to_status, 'A');
	        
			// Send affiliate notification e-mail
			tep_send_affiliate_notification($orders_id, $cur_order);
		}
	}
	
	function get_products_ordered($order) {
		global $currencies, $languages_id;
		
		$products_ordered = '';
		for ($i=0, $n=sizeof($order->products); $i<$n; $i++) {
			$product_instance_id = tep_not_null($order->products[$i]['custom_content']['hla_account_id']) ? $order->products[$i]['custom_content']['hla_account_id'] : '';
	    	$currencies->product_instance_id = $product_instance_id;
	    	
			//------insert customer choosen option to order--------
		    $attributes_exist = '0';
		    $products_ordered_attributes = '';
		    if (isset($order->products[$i]['attributes'])) {
		      	$attributes_exist = '1';
		      	for ($j=0, $n2=sizeof($order->products[$i]['attributes']); $j<$n2; $j++) {
		        	if (DOWNLOAD_ENABLED == 'true') {
		          		$attributes_query = "	select popt.products_options_name, poval.products_options_values_name, pa.options_values_price, pa.price_prefix, pad.products_attributes_maxdays, pad.products_attributes_maxcount , pad.products_attributes_filename
		                               			from " . TABLE_PRODUCTS_OPTIONS . " popt, " . TABLE_PRODUCTS_OPTIONS_VALUES . " poval, " . TABLE_PRODUCTS_ATTRIBUTES . " pa
		                               			left join " . TABLE_PRODUCTS_ATTRIBUTES_DOWNLOAD . " pad
		                                			on pa.products_attributes_id=pad.products_attributes_id
		                               			where pa.products_id = '" . $order->products[$i]['id'] . "'
					                                and pa.options_id = '" . $order->products[$i]['attributes'][$j]['option_id'] . "'
					                                and pa.options_id = popt.products_options_id
					                                and pa.options_values_id = '" . $order->products[$i]['attributes'][$j]['value_id'] . "'
					                                and pa.options_values_id = poval.products_options_values_id
					                                and popt.language_id = '" . $languages_id . "'
					                                and poval.language_id = '" . $languages_id . "'";
		          		$attributes = tep_db_query($attributes_query);
		        	} else {
		          		$attributes = tep_db_query("select popt.products_options_name, poval.products_options_values_name, pa.options_values_price, pa.price_prefix from " . TABLE_PRODUCTS_OPTIONS . " popt, " . TABLE_PRODUCTS_OPTIONS_VALUES . " poval, " . TABLE_PRODUCTS_ATTRIBUTES . " pa where pa.products_id = '" . $order->products[$i]['id'] . "' and pa.options_id = '" . $order->products[$i]['attributes'][$j]['option_id'] . "' and pa.options_id = popt.products_options_id and pa.options_values_id = '" . $order->products[$i]['attributes'][$j]['value_id'] . "' and pa.options_values_id = poval.products_options_values_id and popt.language_id = '" . $languages_id . "' and poval.language_id = '" . $languages_id . "'");
		        	}
		        	$attributes_values = tep_db_fetch_array($attributes);
		        	if ((DOWNLOAD_ENABLED == 'true') && isset($attributes_values['products_attributes_filename']) && tep_not_null($attributes_values['products_attributes_filename']) ) {
		          		$sql_data_array = array('orders_id' => $orders_id,
		                                  		'orders_products_id' => $order->products[$i]['orders_products_id'],
		                                  		'orders_products_filename' => $attributes_values['products_attributes_filename'],
				                                'download_maxdays' => $attributes_values['products_attributes_maxdays'],
				                                'download_count' => $attributes_values['products_attributes_maxcount']);
		          		tep_db_perform(TABLE_ORDERS_PRODUCTS_DOWNLOAD, $sql_data_array);
		        	}
		        	$products_ordered_attributes .= "\n\t" . $attributes_values['products_options_name'] . ' ' . $attributes_values['products_options_values_name'];
		      	}
			}
			//------insert customer choosen option eof ----
		    $total_weight += ($order->products[$i]['qty'] * $order->products[$i]['weight']);
		    $total_tax += tep_calculate_tax($total_products_price, $products_tax) * $order->products[$i]['qty'];
		    $total_cost += $total_products_price;
			
			$cat_path = tep_output_generated_category_path($order->products[$i]['id'], 'product');
		  	$product_email_display_str = $cat_path . (tep_not_null($cat_path) ? ' > ' : '') . $order->products[$i]['name'] . (tep_not_null($order->products[$i]['model']) ? ' (' . $order->products[$i]['model'] . ')' : '');
		    
		    $products_ordered .= $order->products[$i]['qty'] . ' x ' . $product_email_display_str . ' = ' . $currencies->display_price($order->products[$i]['id'], $order->products[$i]['final_price'], $order->products[$i]['tax'], $order->products[$i]['qty']) . $products_ordered_attributes . "\n";
		}
		
		return $products_ordered;
	}
}//end class
?>