<?php

/*
  $Id: moneybookers.php,v 1.44 2015/02/16 07:43:46 chingyen Exp $

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2005 osCommerce

  Released under the GNU General Public License
 */

class moneybookers {

    var $code, $title, $description, $sort_order, $enabled = false, $form_action_url;
    var $email_footer, $auth_num, $transaction_id, $mbLanguages, $mbCurrencies, $aCurrencies, $defCurr, $defLang;
    var $mb_email_id, $mb_ref_id, $mb_md5_pwd, $mb_secret_word, $prepend_order_comment, $order_comment_1, $order_comment_2;
    var $check_trans_status_flag;

    // class constructor
    function moneybookers($pm_id = '') {
        global $order, $languages_id, $default_languages_id;

        //Basic info
        $this->payment_methods_id = 0;
        $this->payment_methods_parent_id = 0;

        $this->code = 'moneybookers';
        $this->title = $this->code;
        $this->connect_via_proxy = false;
        $this->has_ipn = true;

        $this->filename = 'moneybookers.php';

        $this->check_trans_status_flag = false;

        // languages moneybookers supports on its payment interface      
        $this->mbLanguages = array("EN", "DE", "ES", "FR", "IT", "PL");

        $currency_select_sql = "SELECT code FROM " . TABLE_CURRENCIES;
        $currency_result_sql = tep_db_query($currency_select_sql);
        while ($currency_row = tep_db_fetch_array($currency_result_sql)) {
            $this->aCurrencies[] = $currency_row['code'];
        }

        $payment_methods_select_sql = "	SELECT payment_methods_parent_id, payment_methods_code, 
												payment_methods_types_id, 
												payment_methods_receive_status_mode, payment_methods_id, 
												payment_methods_title, payment_methods_sort_order, 
												payment_methods_receive_status, payment_methods_legend_color, 
												payment_methods_logo 
										FROM " . TABLE_PAYMENT_METHODS . " 
										WHERE 1 ";
        if ((int) $pm_id > 0) {
            $payment_methods_select_sql .= "	AND payment_methods_id = '" . (int) $pm_id . "'";
        } else {
            $payment_methods_select_sql .= "	AND payment_methods_filename = '" . tep_db_input($this->filename) . "'";
        }

        $payment_methods_result_sql = tep_db_query($payment_methods_select_sql);
        if ($payment_methods_row = tep_db_fetch_array($payment_methods_result_sql)) {
            $pm_desc_title_select_sql = "	SELECT payment_methods_description_title 
											FROM " . TABLE_PAYMENT_METHODS_DESCRIPTION . " 
											WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "' 
												AND (IF (languages_id = '" . tep_db_input($languages_id) . "', 1, IF ((	SELECT COUNT(payment_methods_id) > 0 
																														FROM " . TABLE_PAYMENT_METHODS_DESCRIPTION . " 
																														WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "' 
																															AND languages_id = '" . (int) $languages_id . "'), 0, languages_id = 1)))";
            $pm_desc_title_result_sql = tep_db_query($pm_desc_title_select_sql);
            $pm_desc_title_row = tep_db_fetch_array($pm_desc_title_result_sql);

            $this->payment_methods_id = (int) $payment_methods_row['payment_methods_id'];
            $this->payment_methods_parent_id = (int) $payment_methods_row['payment_methods_parent_id'];
            $this->code = $payment_methods_row['payment_methods_code'];
            $this->title = $payment_methods_row['payment_methods_title']; //MODULE_PAYMENT_PAYPAL_TEXT_TITLE;
            $this->display_title = $pm_desc_title_row['payment_methods_description_title']; //MODULE_PAYMENT_PAYPAL_TEXT_DISPLAY_TITLE; 	// if title to be display for payment method is different from payment method value
            $this->sort_order = $payment_methods_row['payment_methods_sort_order']; //MODULE_PAYMENT_PAYPAL_SORT_ORDER;
            $this->enabled = $payment_methods_row['payment_methods_receive_status_mode'];
            $this->legend_display_colour = $payment_methods_row['payment_methods_legend_color'];
            $this->receive_status = $payment_methods_row['payment_methods_receive_status'];
            $this->description = $this->display_title;
            $this->preferred = true;
            $this->mbCurrencies = $this->get_support_currencies($this->payment_methods_id);

            //$this->pm_important_notice = MODULE_PAYMENT_MONEYBOOKERS_TEXT_IMPORTANT_NOTICE;	// comment by boonhock, couldn't found at others place
            // If the default language isn't supported by moneybookers.com fall back to English: "EN"
            $this->defLang = strtoupper($this->defLang);
            if (!in_array($this->defLang, $this->mbLanguages)) {
                $this->defLang = "EN";
            }

            // take the default currency and language to be used at moneybookers.com
            $this->defCurr = DEFAULT_CURRENCY;
            $this->defLang = DEFAULT_LANGUAGE;


            $configuration_setting_array = $this->load_pm_setting();
            //$this->currency = $configuration_setting_array['MODULE_PAYMENT_MONEYBOOKERS_CURRENCY'];
            $this->language = $configuration_setting_array['MODULE_PAYMENT_MONEYBOOKERS_LANGUAGE'];
            $this->zone = (isset($configuration_setting_array['MODULE_PAYMENT_MONEYBOOKERS_ZONE']) ? $configuration_setting_array['MODULE_PAYMENT_MONEYBOOKERS_ZONE'] : '');
            $this->order_status_id = $configuration_setting_array['MODULE_PAYMENT_MONEYBOOKERS_ORDER_STATUS_ID'];
            $this->order_processing_status = $this->processing_status_id = $configuration_setting_array['MODULE_PAYMENT_MONEYBOOKERS_PROCESSING_STATUS_ID'];
            $this->message = $configuration_setting_array['MODULE_PAYMENT_MONEYBOOKERS_MESSAGE'];
            $this->email_message = $configuration_setting_array['MODULE_PAYMENT_MONEYBOOKERS_EMAIL_MESSAGE'];
            $this->confirm_complete_days = $configuration_setting_array['MODULE_PAYMENT_MONEYBOOKERS_CONFIRM_COMPLETE'];
            $this->require_address_information = $configuration_setting_array['MODULE_PAYMENT_MONEYBOOKERS_MANDATORY_ADDRESS_FIELD'];
            $this->require_ic_information = $configuration_setting_array['MODULE_PAYMENT_MONEYBOOKERS_MANDATORY_IC_FIELD'];
            $this->require_contact_information = $configuration_setting_array['MODULE_PAYMENT_MONEYBOOKERS_MANDATORY_CONTACT_FIELD'];

            $this->customer_payment_info = $configuration_setting_array['MODULE_PAYMENT_MONEYBOOKERS_CUSTOMER_PAYMENT_INFO'];

            $this->email_footer = str_replace(array("<br>", "<BR>", "\r\n"), array("\n", "\n", ''), $this->email_message);
            $this->auto_cancel_period = 60; // In minutes
            $this->force_to_checkoutprocess = true;

            if ((int) $this->order_status_id > 0) {
                $this->order_status = $this->order_status_id;
            } else {
                $this->order_status = DEFAULT_ORDERS_STATUS_ID;
            }

            //if (is_object($order)) $this->update_status();
            $this->form_action_url = 'https://www.moneybookers.com/app/payment.pl';
            $this->order_comment_1 = (defined(MODULE_PAYMENT_MONEYBOOKERS_ORDER_COMMENT1) ? MODULE_PAYMENT_MONEYBOOKERS_ORDER_COMMENT1 : '');
            $this->order_comment_2 = (defined(MODULE_PAYMENT_MONEYBOOKERS_ORDER_COMMENT2) ? MODULE_PAYMENT_MONEYBOOKERS_ORDER_COMMENT2 : '');
        }
        /*
          $this->payment_pm_id = array(	'WLT' => 'Moneybookers Wallet',
          'VSA' => 'Visa',
          'MSC' => 'MasterCard',
          'VSE' => 'Visa Electron',
          'AMX' => 'American Express',
          'DIN' => 'Diners',
          'JCB' => 'JCB',
          'DIN' => 'Diners');
         */
        /*
          // text to include in the e-mail moneybookers.com sends during the transaction
          $this->mb_email_id = MODULE_PAYMENT_MONEYBOOKERS_EMAILID;
          $this->mb_ref_id = MODULE_PAYMENT_MONEYBOOKERS_REFID;
          $this->mb_md5_pwd = MODULE_PAYMENT_MONEYBOOKERS_PWD;
          $this->mb_secret_word = MODULE_PAYMENT_MONEYBOOKERS_SECRET_WORD;
         */
    }

    // Status update
    function update_status() {
        global $order;

        if (($this->enabled == true) && ((int) $this->zone > 0)) {
            $zone_check_select_sql = "SELECT zone_id FROM " . TABLE_ZONES_TO_GEO_ZONES . " WHERE geo_zone_id = '" . MODULE_PAYMENT_MONEYBOOKERS_ZONE . "' AND (zone_country_id = '" . $order->billing['country']['id'] . "' OR zone_country_id = 0) AND IF (zone_country_id = 0, 1, (zone_id LIKE '%," . $order->billing['zone_id'] . ",%' OR zone_id LIKE '%,0,%'))";
            $zone_check_result_sql = tep_db_query($zone_check_select_sql);
            $zone_check_row = tep_db_fetch_array($zone_check_result_sql);

            $check_flag = (tep_not_null($zone_check_row["zone_id"])) ? true : false;

            if ($check_flag == false) {
                //$this->enabled = false;
                $this->preferred = false;
            }
        }
    }

    // class methods
    function javascript_validation() {
        return false;
    }

    function selection() {
        global $languages_id, $default_languages_id;

        $selection_array = array();

        $payment_methods_select_sql = "	SELECT payment_methods_id, payment_methods_code, payment_methods_types_id, payment_methods_logo, payment_methods_receive_status_mode 
										FROM " . TABLE_PAYMENT_METHODS . " 
										WHERE payment_methods_receive_status = 1 
											AND payment_methods_receive_status_mode <> 0 
											AND payment_methods_parent_id = '" . (int) $this->payment_methods_id . "' 
										ORDER BY payment_methods_sort_order";
        $payment_methods_result_sql = tep_db_query($payment_methods_select_sql);
        while ($payment_methods_row = tep_db_fetch_array($payment_methods_result_sql)) {
            $pm_desc_title_select_sql = "	SELECT payment_methods_description_title 
											FROM " . TABLE_PAYMENT_METHODS_DESCRIPTION . " 
											WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "' 
												AND (IF (languages_id = '" . tep_db_input($languages_id) . "', 1, IF ((	SELECT COUNT(payment_methods_id) > 0 
																														FROM " . TABLE_PAYMENT_METHODS_DESCRIPTION . " 
																														WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "' 
																															AND languages_id = '" . (int) $languages_id . "'), 0, languages_id = 1)))";
            $pm_desc_title_result_sql = tep_db_query($pm_desc_title_select_sql);
            $pm_desc_title_row = tep_db_fetch_array($pm_desc_title_result_sql);

            $pm_array = $this->load_pm_setting(1, $payment_methods_row['payment_methods_id']);

            $selection_array[] = array('payment_gateway_code' => $this->code,
                'payment_methods_receive_status_mode' => $payment_methods_row['payment_methods_receive_status_mode'],
                'payment_methods_id' => $payment_methods_row['payment_methods_id'],
                'payment_methods_code' => $payment_methods_row['payment_methods_code'],
                'payment_methods_types_id' => $payment_methods_row['payment_methods_types_id'],
                'payment_methods_parent_id' => $this->payment_methods_id,
                'payment_methods_logo' => $payment_methods_row['payment_methods_logo'],
                'payment_methods_description_title' => $pm_desc_title_row['payment_methods_description_title'],
                'currency' => $this->get_support_currencies($payment_methods_row['payment_methods_id']),
                'confirm_complete_days' => $pm_array['MODULE_PAYMENT_MONEYBOOKERS_CONFIRM_COMPLETE'],
                'show_billing_address' => $pm_array['MODULE_PAYMENT_MONEYBOOKERS_MANDATORY_ADDRESS_FIELD'],
                'show_contact_number' => $pm_array['MODULE_PAYMENT_MONEYBOOKERS_MANDATORY_CONTACT_FIELD'],
                'show_ic' => $pm_array['MODULE_PAYMENT_MONEYBOOKERS_MANDATORY_IC_FIELD']
            );

            if ($payment_methods_row['payment_methods_receive_status_mode'] == '-1') {
                $pm_status_message_select = "	SELECT payment_methods_status_message 
												FROM " . TABLE_PAYMENT_METHODS_STATUS_DESCRIPTION . " 
												WHERE payment_methods_mode = 'RECEIVE' 
													AND payment_methods_status = '-1' 
													AND languages_id = '" . (int) $languages_id . "' 
													AND payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "'";
                $pm_status_message_result = tep_db_query($pm_status_message_select);
                $pm_status_message_row = tep_db_fetch_array($pm_status_message_result);
                $selection_array[sizeof($selection_array) - 1]['payment_methods_status_message'] = $pm_status_message_row['payment_methods_status_message'];
            }
        }

        return $selection_array;
    }

    function set_support_currencies($currency_array) {
        $this->mbCurrencies = $currency_array;
    }

    function get_support_currencies($pm_id, $by_zone = true) {
        global $zone_info_array;

        $default_currency = '';
        $support_currencies_array = array();

        $currency_code_select_sql = "	SELECT currency_code, payment_methods_instance_default 
    									FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " 
    									WHERE payment_methods_id = '" . (int) $pm_id . "' 
    									ORDER BY currency_code";
        $currency_code_result_sql = tep_db_query($currency_code_select_sql);

        if (tep_db_num_rows($currency_code_result_sql) > 0) {
            while ($currency_code_row = tep_db_fetch_array($currency_code_result_sql)) {
                $support_currencies_array[] = $currency_code_row['currency_code'];

                if ($currency_code_row['payment_methods_instance_default'] == 1) {
                    $default_currency = $currency_code_row['currency_code'];
                }
            }
        } else {
            if ($this->payment_methods_parent_id > 0) {
                $payment_methods_parent_id = $this->payment_methods_parent_id;
            } else {
                $payment_methods_parent_id = $this->payment_methods_id;
            }

            $parent_currency_code_select_sql = "	SELECT currency_code, payment_methods_instance_default 
			    									FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " 
			    									WHERE payment_methods_id = '" . (int) $payment_methods_parent_id . "' 
			    									ORDER BY currency_code";
            $parent_currency_code_result_sql = tep_db_query($parent_currency_code_select_sql);
            while ($parent_currency_code_row = tep_db_fetch_array($parent_currency_code_result_sql)) {
                $support_currencies_array[] = $parent_currency_code_row['currency_code'];

                if ($parent_currency_code_row['payment_methods_instance_default'] == 1) {
                    $default_currency = $parent_currency_code_row['currency_code'];
                }
            }
        }

        if (tep_not_null($default_currency)) {
            $this->defCurr = $default_currency;
        }

        if ($by_zone) {
            if (isset($zone_info_array)) {
                $support_currencies_array = array_intersect($zone_info_array[3]->zone_currency_id, $support_currencies_array);
                if (count($support_currencies_array)) {
                    array_multisort($support_currencies_array);
                } else {
                    $support_currencies_array = array($default_currency);
                }
            } else {
                $support_currencies_array = array($default_currency);
            }
        } else {
            $this->set_support_currencies($support_currencies_array);
            // All found currencies is supported. Normally this is used for backend script such as IPN call
        }

        return $support_currencies_array;
    }

    function get_require_address_information() {
        return $this->require_address_information;
    }

    function get_confirm_complete_days() {
        return $this->confirm_complete_days;
    }

    function get_pm_info() {
        return $this->message;
    }

    function get_email_pm_info() {
        return $this->email_message;
    }

    function draw_confirm_button() {
        return tep_image_button2('red', 'javascript:document.checkout_confirmation.submit()', MODULE_PAYMENT_MONEYBOOKERS_TEXT_CART, 220);
    }

    function get_confirm_button_caption() {
        return MODULE_PAYMENT_MONEYBOOKERS_TEXT_TITLE;
    }

    function pre_confirmation_check() {
        global $currency;

        if (in_array($currency, $this->mbCurrencies)) {
            
        }

        return false;
    }

    function process_button() {
        global $HTTP_POST_VARS, $shipping_cost, $total_cost, $shipping_selected, $shipping_method, $currencies, $currency, $customer_id, $order, $languages_id;
        global $order_logged, $OFFGAMERS, $country;

        if ($this->force_to_checkoutprocess && !tep_session_is_registered('order_logged'))
            return;

        $language_code_select_sql = "SELECT code FROM " . TABLE_LANGUAGES . " WHERE languages_id ='" . tep_db_input($languages_id) . "'";
        $language_code_result_sql = tep_db_query($language_code_select_sql);
        $language_code_row = tep_db_fetch_array($language_code_result_sql);

        $language_code = $language_code_row['code'];
        $mbLanguage = strtoupper($lang_code);

        /*         * *********************************************************************
          If you have "select default currency with language" set you can
          create US english, and UK english language sets separately in osC,
          and bind USD and GBP respectively to those language sets.
          (ie. If a user select UK english as his/her default language,
          the currency will also default to GBP.)
          If you do not use this "feature", you can delete the following
          3 lines.
          /********************************************************************* */

        // English is English, so set US english to "EN"
        if ($mbLanguage == 'US') {
            $mbLanguage = 'EN';
        }

        // if the user language is not supported by moneybookers.com 
        // fall back to the default language set on the admin interface of the module
        if (!in_array($mbLanguage, $this->mbLanguages)) {
            $mbLanguage = $this->language;
        }

        // if the user currency is not supported by moneybookers.com
        // fall back to the default currency set on the admin interface of the module
        $mbCurrency = $currency;
        if (!in_array($mbCurrency, $this->mbCurrencies)) {
            $mbCurrency = $this->mbCurrencies[0];
        }

        $country_select_sql = "	SELECT mb_cID 
								FROM " . TABLE_PAYMENT_MONEYBOOKERS_COUNTRIES . ", " . TABLE_COUNTRIES . " 
								WHERE osc_cID = countries_id
									AND countries_id = '" . tep_db_input($country) . "'";
        $country_result_sql = tep_db_query($country_select_sql);
        $country_row = tep_db_fetch_array($country_result_sql);

        $mbCountry = $country_row['mb_cID'];

        $this->get_merchant_account($mbCurrency);
        // generate a unique transaction ID for the process
        $this->transaction_id = $this->generate_trid();

        $mb_payment_data_array = array('mb_trans_id' => $this->transaction_id,
            'mb_order_id' => $order_logged
        );
        tep_db_perform(TABLE_PAYMENT_MONEYBOOKERS, $mb_payment_data_array);

        $process_button_string = tep_draw_hidden_field('pay_to_email', $this->mb_email_id) .
                tep_draw_hidden_field('transaction_id', $this->transaction_id) .
                tep_draw_hidden_field('return_url', tep_href_link(FILENAME_CHECKOUT_PROCESS, 'trid=' . $this->transaction_id)) .
                tep_draw_hidden_field('cancel_url', tep_href_link(FILENAME_CHECKOUT_PAYMENT, MODULE_PAYMENT_MONEYBOOKERS_ERRORTEXT1 . $this->code . MODULE_PAYMENT_MONEYBOOKERS_ERRORTEXT2)) .
                tep_draw_hidden_field('status_url', tep_href_link(FILENAME_MB_IPN, '', 'SSL', false)) .
                tep_draw_hidden_field('language', $mbLanguage) .
                tep_draw_hidden_field('hide_login', 1) .
                tep_draw_hidden_field('amount', number_format($order->info['total'] * $currencies->get_value($mbCurrency), $currencies->get_decimal_places($mbCurrency), '.', '')) .
                tep_draw_hidden_field('currency', $mbCurrency) .
                tep_draw_hidden_field('detail1_description', STORE_NAME) .
                tep_draw_hidden_field('detail1_text', MODULE_PAYMENT_MONEYBOOKERS_ORDER_TEXT . strftime(DATE_FORMAT_LONG)) .
                tep_draw_hidden_field('firstname', $order->billing['firstname']) .
                tep_draw_hidden_field('lastname', $order->billing['lastname']) .
                tep_draw_hidden_field('address', $order->billing['street_address']) .
                tep_draw_hidden_field('postal_code', $order->billing['postcode']) .
                tep_draw_hidden_field('city', $order->billing['city']) .
                tep_draw_hidden_field('state', $order->billing['state']) .
                tep_draw_hidden_field('logo_url', 'https://d130xiciw9h9wz.cloudfront.net/banners/1/logo_horizontal.png') .
                tep_draw_hidden_field('country', $mbCountry) .
                tep_draw_hidden_field('confirmation_note', $this->message);

        if ($this->code == 'BWI' || $this->code == 'WLT' || $this->code == 'WLTNRP') {
            $process_button_string .= tep_draw_hidden_field('pay_from_email', $order->customer['email_address']);
        } else {
            $process_button_string .= tep_draw_hidden_field('payment_methods', $this->code);
        }


        if (ereg_dep("[0-9]{6}", $this->mb_ref_id)) {
            $process_button_string .= tep_draw_hidden_field('rid', $this->mb_ref_id);
        }

        // moneyboocers.com payment gateway does not accept accented characters!
        // Please feel free to add any other accented characters to the list.
        return strtr($process_button_string, "������������������", "aeiooouuuAEIOOOUUU");
    }

    // manage returning data from moneybookers (errors, failures, success etc.)
    function before_process() {
        global $HTTP_POST_VARS, $order, $order_logged, $currency;

        // added by wei chen
        if (!tep_session_is_registered('order_logged'))
            return;
        // end added by wei chen
        // get the transaction ID
        if (isset($_GET['trid'])) {
            $this->transaction_id = $_GET["trid"];
        } else {
            $this->transaction_id = $_POST["transaction_id"];
        }

        if ($this->transaction_id != $order_logged) {
            tep_redirect(tep_href_link(FILENAME_CHECKOUT_PAYMENT, 'error_message=' . MODULE_PAYMENT_MONEYBOOKERS_TEXT_ERROR, 'SSL', true, false)); // Prevent URL injection
        }

        $mbCurrency = $currency;

        if (!in_array($currency, $this->mbCurrencies)) {
            $mbCurrency = $this->currency;
        }

        $this->get_merchant_account($mbCurrency);

        // Create password hash, no need md5 since the one entered from admin page should be md5
        //$md5_pwd = md5(MODULE_PAYMENT_MONEYBOOKERS_PWD);
        $md5_pwd = $this->mb_md5_pwd;

        // build URL to retrieve transaction result
        $data = '';
        $form_data = array('email' => $this->mb_email_id,
            'password' => $md5_pwd,
            'action' => 'status_trn',
            'trn_id' => $this->transaction_id
        );
        // concatenate order information variables to $data
        while (list($key, $value) = each($form_data)) {
            $data .= $key . '=' . urlencode(ereg_replace_dep(',', '', $value)) . '&';
        }

        // take the last & out for the string
        $data = substr($data, 0, -1);
        unset($result);

        $url = "https://www.moneybookers.com/app/query.pl";

        $agent = "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.0)";
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_VERBOSE, 1);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 2);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        curl_setopt($ch, CURLOPT_TIMEOUT, 120);
        curl_setopt($ch, CURLOPT_USERAGENT, $agent);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        if ($this->connect_via_proxy) {
            curl_setopt($ch, CURLOPT_PROXY, 'http://mvy-proxy:3128');
            curl_setopt($ch, CURLOPT_PROXYUSERPWD, 'devbackend:devonly');
        }
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);  //Windows 2003 Compatibility
        $result = curl_exec($ch);
        curl_close($ch);

        $result = urldecode($result);

        /*         * ******************************
          get the returned error code
          200 -- OK
          401 -- Login failed
          402 -- Unknown action
          403 -- Transaction not found
          404 -- Missing parameter
          405 -- Illegal parameter value
         * ****************************** */
        $this->order_processing_status = $this->order_status; // Only promoted to $this->processing_status_id when it is returned from moneybookers and has the status "processed" to ignore payment with upload funds

        preg_match("/\d{3}/", $result, $return_code);

        switch ($return_code[0]) {
            // query was OK, data is sent back
            case "200":
                // All the mb status update and history insertion will leave for mb_ipn.php

                $result = strstr($result, "status");
                $aResult = explode("&", $result);

                /*                 * *********************************************************
                  get the returned data
                  [status] -- (-2) => failed
                  ( 2) => processed
                  ( 1) => scheduled
                  ( 0) => Pending (eg. offline bank transfer)
                  [mb_transaction_id] -- transaction ID at moneybookers.com
                  /********************************************************** */
                foreach ($aResult as $value) {
                    list($parameter, $pVal) = explode("=", $value);
                    $aFinal["$parameter"] = $pVal;
                }

                $mb_payment_history_data_array = array('mb_trans_id' => $this->transaction_id,
                    'mb_status' => $aFinal['status'],
                    'mb_date' => 'now()'
                );
                if ($aFinal["status"] == -2) {
                    // if there was an error, update the database according to it
                    $mb_payment_history_data_array['mb_err_no'] = '999';
                    $mb_payment_history_data_array['mb_err_txt'] = 'Transaction failed.';
                    tep_db_perform(TABLE_PAYMENT_MONEYBOOKERS_STATUS_HISTORY, $mb_payment_history_data_array);

                    $payment_error_return = "ERROR-CODE:: -2, ERROR:: " . MODULE_PAYMENT_MONEYBOOKERS_TRANSACTION_FAILED_TEXT;

                    //$messageStack->add_session('checkout_payment', $payment_error_return);
                    tep_redirect(tep_href_link(FILENAME_CHECKOUT_PAYMENT, 'payment_error=' . $this->code . '&error=' . $payment_error_return, 'SSL', true, false));

                    return false;
                } else {
                    // if everything went good, update the data in the database and in the order class
                    if ($aFinal["status"] == 2) {
                        // No need to insert history for this case, it will handle by the notify IPN
                        // DO NOT DO ANYTHING HERE. ALL LEAVE TO IPN SCRIPT.
                    } else {
                        $mb_payment_history_data_array['mb_err_no'] = '200';
                        $mb_payment_history_data_array['mb_err_txt'] = 'OK';
                        tep_db_perform(TABLE_PAYMENT_MONEYBOOKERS_STATUS_HISTORY, $mb_payment_history_data_array);
                    }
                }

                break;
            // error occured during query
            // errors documented in the moneybookers doc
            case "401": // 	Authorization declined
            case "402": //	Unknown action
            case "403": //	Transaction not found
            case "404": //	Missing parameter
            case "405": //	Illegal parameter value
                preg_match("/[^\d\t]+.*/i", $result, $return_array);

                $mb_payment_history_data_array = array('mb_trans_id' => $this->transaction_id,
                    'mb_date' => 'now()',
                    'mb_err_no' => $return_code[0],
                    'mb_err_txt' => $return_array[0]
                );
                tep_db_perform(TABLE_PAYMENT_MONEYBOOKERS_STATUS_HISTORY, $mb_payment_history_data_array);

                $payment_error_return = "ERROR_CODE:: {$return_code[0]}, ERROR:: {$return_array[0]}";
                tep_redirect(tep_href_link(FILENAME_CHECKOUT_PAYMENT, 'payment_error=' . $this->code . '&error=' . $payment_error_return, 'SSL', true, false));

                break;
            // unknown error
            default:
                $payment_error_return = "ERROR_CODE:: 000, ERROR:: Unknown error!";
                tep_redirect(tep_href_link(FILENAME_CHECKOUT_PAYMENT, 'payment_error=' . $this->code . '&error=' . $payment_error_return, 'SSL', true, false));
                break;
        }
    }

    function after_process() {
        return false;
    }

    function link_to_payment($new_order_id) {
        global $order_logged;

        if (tep_session_is_registered('order_logged'))
            tep_session_unregister('order_logged');

        tep_session_register('order_logged');
        $order_logged = $new_order_id;
        require(DIR_WS_INCLUDES . 'modules/payment/mb/catalog/mb_splash.php');
        return;
    }

    function get_orders_with_payer_email($customers_id, $customer_email) {
        // Rerun genesis script for these Verifying status orders
        $orders_select_sql = "  SELECT o.orders_id  
                                FROM " . TABLE_PAYMENT_MONEYBOOKERS . " AS m 
                                INNER JOIN " . TABLE_ORDERS . " AS o 
                                    ON (m.mb_order_id = o.orders_id) 
                                WHERE m.mb_payer_email = '" . tep_db_input($customer_email) . "' 
                                    AND o.customers_id = '" . tep_db_input($customers_id) . "'
                                    AND o.orders_status = 7";
        $orders_result_sql = tep_db_query($orders_select_sql);

        while ($orders_row = tep_db_fetch_array($orders_result_sql)) {
            $genesis_order_data_array = array('orders_id' => $orders_row['orders_id'],
                'flag' => 0,
                're_run' => 1,
                'last_modified' => 'now()');
            tep_db_perform(TABLE_CRON_GENESIS_ORDERS, $genesis_order_data_array);
        }
    }

    function get_error() {
        $error = array('title' => MODULE_PAYMENT_MONEYBOOKERS_TEXT_ERROR,
            'error' => stripslashes(urldecode($_GET['error'])));
        return $error;
    }

    function check() {
        if (!isset($this->_check)) {
            $this->_check = defined('MODULE_PAYMENT_MONEYBOOKERS_STATUS');
        }

        return $this->_check;
    }

    function is_processed_order($order_id) {
        $payment_status_select_sql = "	SELECT mb_status 
										FROM " . TABLE_PAYMENT_MONEYBOOKERS . " 
										WHERE mb_order_id = '" . tep_db_input($order_id) . "'";
        $payment_status_result_sql = tep_db_query($payment_status_select_sql);
        $payment_status_row = tep_db_fetch_array($payment_status_result_sql);

        return $payment_status_row['mb_status'] == 2 ? true : false;
    }

    function is_supported_currency($selected_currency) {
        return (in_array($selected_currency, $this->mbCurrencies) ? true : false);
    }

    function load_pm_setting($language_id = 1, $pm_id = '') {
        $pm_setting_array = array();
        //load value from DB

        if (tep_not_null($pm_id)) {
            $payment_method_id = $pm_id;

            $payment_methods_parent_id_select_sql = "	SELECT payment_methods_parent_id 
														FROM " . TABLE_PAYMENT_METHODS . " 
														WHERE payment_methods_id = '" . (int) $pm_id . "'";
            $payment_methods_parent_id_result_sql = tep_db_query($payment_methods_parent_id_select_sql);
            $payment_methods_parent_id_row = tep_db_fetch_array($payment_methods_parent_id_result_sql);

            $payment_gateway_id = $payment_methods_parent_id_row['payment_methods_parent_id'];
        } else {
            $payment_method_id = $this->payment_methods_id;
            $payment_gateway_id = $this->payment_methods_parent_id;
        }

        $payment_configuration_info_select_sql = "	SELECT pci.payment_configuration_info_key, pcid.payment_configuration_info_value
													FROM " . TABLE_PAYMENT_CONFIGURATION_INFO . " as pci
													LEFT JOIN " . TABLE_PAYMENT_CONFIGURATION_INFO_DESCRIPTION . " as pcid 
														ON pci.payment_configuration_info_id = pcid.payment_configuration_info_id
															AND pcid.languages_id = '" . (int) $language_id . "'
													WHERE pci.payment_methods_id = '" . (int) $payment_method_id . "'
													ORDER BY pci.payment_configuration_info_sort_order";
        $payment_configuration_info_result_sql = tep_db_query($payment_configuration_info_select_sql);
        if (tep_db_num_rows($payment_configuration_info_result_sql)) {
            while ($payment_configuration_info_row = tep_db_fetch_array($payment_configuration_info_result_sql)) {
                $pm_setting_array[$payment_configuration_info_row['payment_configuration_info_key']] = $payment_configuration_info_row['payment_configuration_info_value'];
            }
        } else if ((int) $payment_gateway_id > 0) { //load value from PG
            $payment_configuration_info_select_sql = "	SELECT pci.payment_configuration_info_key, pcid.payment_configuration_info_value
														FROM " . TABLE_PAYMENT_CONFIGURATION_INFO . " as pci
														LEFT JOIN " . TABLE_PAYMENT_CONFIGURATION_INFO_DESCRIPTION . " as pcid 
															ON pci.payment_configuration_info_id = pcid.payment_configuration_info_id
																AND pcid.languages_id = '" . (int) $language_id . "'
														WHERE pci.payment_methods_id = '" . (int) $payment_gateway_id . "'
														ORDER BY pci.payment_configuration_info_sort_order";
            $payment_configuration_info_result_sql = tep_db_query($payment_configuration_info_select_sql);
            while ($payment_configuration_info_row = tep_db_fetch_array($payment_configuration_info_result_sql)) {
                $pm_setting_array[$payment_configuration_info_row['payment_configuration_info_key']] = $payment_configuration_info_row['payment_configuration_info_value'];
            }
        }

        return $pm_setting_array;
    }

    function get_merchant_account($selected_currency) {
        $payment_methods_instance_id = 0;
        $payment_gateway_instance_select_sql = "SELECT pmi.payment_methods_instance_id, pmi.payment_methods_instance_follow_default, pmi.currency_code 
				 								FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " as pmi
				 								WHERE pmi.payment_methods_id = '" . (int) $this->payment_methods_id . "'
				 								LIMIT 1";
        $payment_gateway_instance_result_sql = tep_db_query($payment_gateway_instance_select_sql);
        if (tep_db_num_rows($payment_gateway_instance_result_sql)) {
            $payment_gateway_instance_select_sql = "SELECT pmi.payment_methods_instance_id, pmi.payment_methods_instance_follow_default 
					 								FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " as pmi
					 								WHERE pmi.currency_code = '" . tep_db_input($selected_currency) . "' 
					 									AND pmi.payment_methods_id = '" . (int) $this->payment_methods_id . "'";
            $payment_gateway_instance_result_sql = tep_db_query($payment_gateway_instance_select_sql);
            if ($payment_gateway_instance_row = tep_db_fetch_array($payment_gateway_instance_result_sql)) {
                if ((int) $payment_gateway_instance_row['payment_methods_instance_follow_default'] > 0) { // Follow Default Currency Setting
                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_follow_default'];
                } else { // Has own setting
                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_id'];
                }
            }
        } else {
            $payment_gateway_instance_select_sql = "SELECT pmi.payment_methods_instance_id, pmi.payment_methods_instance_follow_default 
					 								FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " as pmi
					 								INNER JOIN " . TABLE_PAYMENT_METHODS . " as pm
					 									ON pm.payment_methods_parent_id = pmi.payment_methods_id
					 								WHERE pmi.currency_code = '" . tep_db_input($selected_currency) . "' 
					 									AND pm.payment_methods_id = '" . (int) $this->payment_methods_id . "'";
            $payment_gateway_instance_result_sql = tep_db_query($payment_gateway_instance_select_sql);
            if ($payment_gateway_instance_row = tep_db_fetch_array($payment_gateway_instance_result_sql)) {
                if ((int) $payment_gateway_instance_row['payment_methods_instance_follow_default'] > 0) {
                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_follow_default'];
                } else {
                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_id'];
                }
            }
        }

        $payment_gateway_instance_setting_select_sql = "SELECT pmis.payment_methods_instance_setting_key, pmis.payment_methods_instance_setting_value 
						 								FROM " . TABLE_PAYMENT_METHODS_INSTANCE_SETTING . " as pmis
						 								WHERE pmis.payment_methods_instance_id = '" . (int) $payment_methods_instance_id . "'";
        $payment_gateway_instance_setting_result_sql = tep_db_query($payment_gateway_instance_setting_select_sql);
        while ($payment_gateway_instance_setting_row = tep_db_fetch_array($payment_gateway_instance_setting_result_sql)) {
            switch ($payment_gateway_instance_setting_row['payment_methods_instance_setting_key']) {
                case 'MODULE_PAYMENT_MONEYBOOKERS_EMAILID':
                    $this->mb_email_id = $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];
                    break;
                case 'MODULE_PAYMENT_MONEYBOOKERS_PWD':
                    $this->mb_md5_pwd = $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];
                    break;
                case 'MODULE_PAYMENT_MONEYBOOKERS_REFID':
                    $this->mb_ref_id = $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];
                    break;
                case 'MODULE_PAYMENT_MONEYBOOKERS_SECRET_WORD':
                    $this->mb_secret_word = $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];
                    break;
            }
        }
    }

    function install() {

        $install_configuration_flag = true;
        if ($this->payment_methods_id > 0) {
            if ($this->receive_status != 1) {
                $update_receive_status_sql_data = array("payment_methods_receive_status" => 1);
                tep_db_perform(TABLE_PAYMENT_METHODS, $update_receive_status_sql_data, 'update', "payment_methods_id = '" . (int) $this->payment_methods_id . "'");
            } else {
                $install_configuration_flag = false;
            }
        } else {
            $install_payment_gateway_data_sql = array('payment_methods_code' => $this->code,
                'payment_methods_send_status' => 0,
                'payment_methods_title' => $this->code,
                'payment_methods_types_id' => 0,
                'payment_methods_parent_id' => 0,
                'payment_methods_legend_color' => '#D5CBC4',
                'payment_methods_sort_order' => 5,
                'payment_methods_filename' => $this->filename,
                'payment_methods_logo' => '',
                'date_added' => 'now()',
                'last_modified' => 'now()',
                'payment_methods_receive_status' => 1,
                'payment_methods_receive_status_mode' => 0
            );
            tep_db_perform(TABLE_PAYMENT_METHODS, $install_payment_gateway_data_sql);
            $this->payment_methods_id = tep_db_insert_id();
        }
        if ($install_configuration_flag) {
            $install_key_array = array();
            /* 			$install_key_array[] = array(	'info' => array (	'payment_methods_id'=>(int)$this->payment_methods_id,
              'payment_configuration_info_title'=>'Transaction Currency',
              'payment_configuration_info_key'=>'MODULE_PAYMENT_MONEYBOOKERS_CURRENCY',
              'payment_configuration_info_description'=>'Select the default currency for the payment transactions. If the user selected currency is not available at moneybookers.com, this currency will be the payment currency.',
              'payment_configuration_info_sort_order'=>'20',
              'set_function'=>'tep_cfg_select_option('.$this->show_array($this->aCurrencies).'), ',
              'use_function'=>'',
              'date_added'=>'now()'
              ),
              'desc' => array (	'payment_configuration_info_value'=>'Selected Currency',
              'languages_id' => 1
              )
              ); */
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Transaction Language',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_MONEYBOOKERS_LANGUAGE',
                    'payment_configuration_info_description' => 'Select the default language for the payment transactions. If the user selected language is not available at moneybookers.com, this currency will be the payment language.',
                    'payment_configuration_info_sort_order' => '25',
                    'set_function' => 'tep_cfg_select_option(' . $this->show_array($this->mbLanguages) . '), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => strtoupper(DEFAULT_LANGUAGE),
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Payment Zone',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_MONEYBOOKERS_ZONE',
                    'payment_configuration_info_description' => 'If a zone is selected, only enable this payment method for that zone.',
                    'payment_configuration_info_sort_order' => '27',
                    'set_function' => 'tep_cfg_pull_down_zone_classes(',
                    'use_function' => 'tep_get_zone_class_title',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => '0',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Set Order Status',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_MONEYBOOKERS_ORDER_STATUS_ID',
                    'payment_configuration_info_description' => 'Set the status of orders made with this payment module to this value',
                    'payment_configuration_info_sort_order' => '28',
                    'set_function' => 'tep_cfg_pull_down_order_statuses(',
                    'use_function' => 'tep_get_order_status_name',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => '1',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Set Order\'s "Processing" Status',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_MONEYBOOKERS_PROCESSING_STATUS_ID',
                    'payment_configuration_info_description' => 'Set the initial processing status of orders made with this payment module to this value.',
                    'payment_configuration_info_sort_order' => '29',
                    'set_function' => 'tep_cfg_pull_down_order_statuses(',
                    'use_function' => 'tep_get_order_status_name',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => '2',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Payment Message',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_MONEYBOOKERS_MESSAGE',
                    'payment_configuration_info_description' => 'Payment message will show up during checkout process (moneybookers.com)',
                    'payment_configuration_info_sort_order' => '30',
                    'set_function' => 'tep_cfg_textarea(',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'Thank you for shopping at SKC Store.',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Payment Email Message',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_MONEYBOOKERS_EMAIL_MESSAGE',
                    'payment_configuration_info_description' => 'This message will show up in order confirmation e-mail.',
                    'payment_configuration_info_sort_order' => '35',
                    'set_function' => 'tep_cfg_textarea(',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'Thank you for shopping at OffGamers.com.',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Confirm Complete (in days)',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_MONEYBOOKERS_CONFIRM_COMPLETE',
                    'payment_configuration_info_description' => 'The number of days in which an order made by this payment method is confirm completed.',
                    'payment_configuration_info_sort_order' => '40',
                    'set_function' => '',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => '0',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Require Address Information',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_MONEYBOOKERS_MANDATORY_ADDRESS_FIELD',
                    'payment_configuration_info_description' => 'Set address field as required info during checkout.',
                    'payment_configuration_info_sort_order' => '50',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Customer Payment Info',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_MONEYBOOKERS_CUSTOMER_PAYMENT_INFO',
                    'payment_configuration_info_description' => 'Set to true if this Payment Method required customer input for Payment Information required customer payment info.',
                    'payment_configuration_info_sort_order' => '60',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Require IC Information',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_MONEYBOOKERS_MANDATORY_IC_FIELD',
                    'payment_configuration_info_description' => 'Set IC field as required info during checkout.',
                    'payment_configuration_info_sort_order' => '70',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Require Contact Information',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_MONEYBOOKERS_MANDATORY_CONTACT_FIELD',
                    'payment_configuration_info_description' => 'Set Contact field as required info during checkout.',
                    'payment_configuration_info_sort_order' => '80',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );
            foreach ($install_key_array as $data) {
                tep_db_perform(TABLE_PAYMENT_CONFIGURATION_INFO, $data['info']);
                $payment_conf_id = tep_db_insert_id();

                $data['desc']['payment_configuration_info_id'] = (int) $payment_conf_id;
                tep_db_perform(TABLE_PAYMENT_CONFIGURATION_INFO_DESCRIPTION, $data['desc']);
            }
        }
        return 1;
    }

    function remove() {
        tep_db_query("delete from " . TABLE_CONFIGURATION . " where configuration_key in ('" . implode("', '", $this->keys()) . "')");
    }

    function keys() {
        return array('MODULE_PAYMENT_MONEYBOOKERS_STATUS',
            'MODULE_PAYMENT_MONEYBOOKERS_EMAILID',
            'MODULE_PAYMENT_MONEYBOOKERS_PWD',
            'MODULE_PAYMENT_MONEYBOOKERS_REFID',
            'MODULE_PAYMENT_MONEYBOOKERS_SECRET_WORD',
            'MODULE_PAYMENT_MONEYBOOKERS_LANGUAGE',
            'MODULE_PAYMENT_MONEYBOOKERS_CURRENCY',
            'MODULE_PAYMENT_MONEYBOOKERS_SORT_ORDER',
            'MODULE_PAYMENT_MONEYBOOKERS_ORDER_STATUS_ID',
            'MODULE_PAYMENT_MONEYBOOKERS_PROCESSING_STATUS_ID',
            'MODULE_PAYMENT_MONEYBOOKERS_ZONE',
            'MODULE_PAYMENT_MONEYBOOKERS_MESSAGE',
            'MODULE_PAYMENT_MONEYBOOKERS_EMAIL_MESSAGE',
            'MODULE_PAYMENT_MONEYBOOKERS_CONFIRM_COMPLETE',
            'MODULE_PAYMENT_MONEYBOOKERS_LEGEND_COLOUR');
    }

    function configuration_information_keys() {
        return array('MODULE_PAYMENT_MONEYBOOKERS_CURRENCY',
            'MODULE_PAYMENT_MONEYBOOKERS_LANGUAGE',
            'MODULE_PAYMENT_MONEYBOOKERS_ZONE',
            'MODULE_PAYMENT_MONEYBOOKERS_ORDER_STATUS_ID',
            'MODULE_PAYMENT_MONEYBOOKERS_PROCESSING_STATUS_ID',
            'MODULE_PAYMENT_MONEYBOOKERS_MESSAGE',
            'MODULE_PAYMENT_MONEYBOOKERS_EMAIL_MESSAGE',
            'MODULE_PAYMENT_MONEYBOOKERS_CONFIRM_COMPLETE',
            'MODULE_PAYMENT_MONEYBOOKERS_MANDATORY_ADDRESS_FIELD',
            'MODULE_PAYMENT_MONEYBOOKERS_CUSTOMER_PAYMENT_INFO',
            'MODULE_PAYMENT_MONEYBOOKERS_MANDATORY_IC_FIELD',
            'MODULE_PAYMENT_MONEYBOOKERS_MANDATORY_CONTACT_FIELD');
    }

    function multi_lang_configuration_info_keys() {
        return array('MODULE_PAYMENT_MONEYBOOKERS_MESSAGE',
            'MODULE_PAYMENT_MONEYBOOKERS_EMAIL_MESSAGE');
    }

    function merchant_information_keys() {
        include_once(DIR_WS_CLASSES . "payment_methods.php");

        return array(
            'MODULE_PAYMENT_MONEYBOOKERS_EMAILID' => array("field" => 'text', "mapping" => 'MODULE_PAYMENT_MONEYBOOKERS_LNG_EMAILID'),
            'MODULE_PAYMENT_MONEYBOOKERS_PWD' => array("field" => 'text', "mapping" => 'MODULE_PAYMENT_MONEYBOOKERS_LNG_PWD'),
            'MODULE_PAYMENT_MONEYBOOKERS_REFID' => array("field" => 'text', "mapping" => 'MODULE_PAYMENT_MONEYBOOKERS_LNG_REFID'),
            'MODULE_PAYMENT_MONEYBOOKERS_SECRET_WORD' => array("field" => 'text', "mapping" => 'MODULE_PAYMENT_MONEYBOOKERS_LNG_SECRET_WORD'),
            'MODULE_PAYMENT_TAX' => array("field" => 'select', 'mapping' => "MODULE_PAYMENT_TAX", "select" => payment_methods::get_tax_option())
        );
    }

    function get_pg_id() {
        return $this->payment_methods_id;
    }

    // If there is no moneybookers accepted currency configured with the shop
    // do not allow the moneybookers payment module installation
    function check_currency($availableCurr) {
        $foundCurr = false;
        foreach ($availableCurr as $currID) {
            if (in_array($currID, $this->mbCurrencies)) {
                $foundCurr = true;
            }
        }
        return $foundCurr;
    }

    // Parse the predefinied array to be 'module install' friendly
    // as it is used for select in the module's install() function
    function show_array($aArray) {
        $aFormatted = "array(";
        foreach ($aArray as $key => $sVal) {
            $aFormatted .= "\'$sVal\', ";
        }
        $aFormatted = substr($aFormatted, 0, strlen($aFormatted) - 2);
        return $aFormatted;
    }

    // generate a unique 16 characters long transaction ID and
    // check against the already existing IDs in the databse
    // loop it until it's really unique
    function generate_trid() {
        global $order_logged;

        return $order_logged; // Use order id as the transaction id to minimised reference id used in our system
        /*
          do {
          $trid = tep_create_random_value(16, "mixed");

          $transaction_select_sql = "SELECT mb_trans_id FROM " . TABLE_PAYMENT_MONEYBOOKERS . " WHERE mb_trans_id = '" . tep_db_input($trid) . "'";
          $transaction_result_sql = tep_db_query($transaction_select_sql);
          } while (tep_db_num_rows($transaction_result_sql));

          return $trid;
         */
    }

    function check_trans_status($order_id) {
        $trans_id_select_sql = "SELECT mb_trans_id FROM " . TABLE_PAYMENT_MONEYBOOKERS . " WHERE mb_order_id = '" . tep_db_input($order_id) . "'";
        $trans_id_result_sql = tep_db_query($trans_id_select_sql);

        if ($trans_id_row = tep_db_fetch_array($trans_id_result_sql)) {
            $order_info_select_sql = "	SELECT currency 
										FROM " . TABLE_ORDERS . " 
										WHERE orders_id = '" . tep_db_input($order_id) . "'";
            $order_info_result_sql = tep_db_query($order_info_select_sql);
            $order_info_row = tep_db_fetch_array($order_info_result_sql);
            $this->get_merchant_account($order_info_row['currency']);

            $md5_pwd = $this->mb_md5_pwd;

            // build URL to retrieve transaction result
            $data = '';
            $form_data = array('email' => $this->mb_email_id,
                'password' => $md5_pwd,
                'action' => 'status_trn',
                'trn_id' => $trans_id_row['mb_trans_id']
            );
            // concatenate order information variables to $data
            while (list($key, $value) = each($form_data)) {
                $data .= $key . '=' . urlencode(ereg_replace_dep(',', '', $value)) . '&';
            }

            // take the last & out for the string
            $data = substr($data, 0, -1);
            unset($result);

            $url = "https://www.moneybookers.com/app/query.pl";

            $agent = "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.0)";
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_VERBOSE, 1);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 2);
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
            curl_setopt($ch, CURLOPT_TIMEOUT, 120);
            curl_setopt($ch, CURLOPT_USERAGENT, $agent);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);  //Windows 2003 Compatibility
            if ($this->connect_via_proxy) {
                curl_setopt($ch, CURLOPT_PROXY, 'http://mvy-proxy:3128');
                curl_setopt($ch, CURLOPT_PROXYUSERPWD, 'devbackend:devonly');
            }
            $result = curl_exec($ch);
            curl_close($ch);

            $result = urldecode($result);

            /*             * ******************************
              get the returned error code
              200 -- OK
              401 -- Login failed
              402 -- Unknown action
              403 -- Transaction not found
              404 -- Missing parameter
              405 -- Illegal parameter value
             * ****************************** */
            preg_match("/\d{3}/", $result, $return_code);

            switch ($return_code[0]) {
                // query was OK, data is sent back
                case "200":
                    $result = strstr($result, "status");
                    $aResult = explode("&", $result);

                    /*                     * *********************************************************
                      get the returned data
                      [status] -- (-2) => failed
                      ( 2) => processed
                      ( 1) => scheduled
                      ( 0) => Pending (eg. offline bank transfer)
                      [mb_transaction_id] -- transaction ID at moneybookers.com
                      /********************************************************** */
                    foreach ($aResult as $value) {
                        list($parameter, $pVal) = explode("=", $value);
                        $aFinal["$parameter"] = $pVal;
                    }
                    /*
                      $current_mb_status_select_sql = "	SELECT mb_status
                      FROM " . TABLE_PAYMENT_MONEYBOOKERS . "
                      WHERE mb_trans_id = '" . $trans_id_row['mb_trans_id'] . "'";
                      $current_mb_status_result_sql = tep_db_query($current_mb_status_select_sql);
                      $current_mb_status_row = tep_db_fetch_array($current_mb_status_result_sql);
                     */
                    $mb_payment_data_array = array('mb_mb_trans_id' => $aFinal['mb_transaction_id'],
                        'mb_status' => $aFinal['status'],
                        'mb_payer_email' => $aFinal['pay_from_email']);
                    tep_db_perform(TABLE_PAYMENT_MONEYBOOKERS, $mb_payment_data_array, 'update', "mb_trans_id = '" . $trans_id_row['mb_trans_id'] . "'");

                    $mb_payment_history_data_array = array('mb_trans_id' => $trans_id_row['mb_trans_id'],
                        'mb_status' => $aFinal['status'],
                        'mb_date' => 'now()'
                    );
                    if ($aFinal["status"] == -2) {
                        // if there was an error, update the database according to it
                        $mb_payment_history_data_array['mb_err_no'] = '999';
                        $mb_payment_history_data_array['mb_err_txt'] = 'Transaction failed.';
                        tep_db_perform(TABLE_PAYMENT_MONEYBOOKERS_STATUS_HISTORY, $mb_payment_history_data_array);

                        return false;
                    } else {
                        $this->check_trans_status_flag = true;
                        $mb_payment_history_data_array['mb_err_no'] = '200';
                        $mb_payment_history_data_array['mb_err_txt'] = 'OK';
                        tep_db_perform(TABLE_PAYMENT_MONEYBOOKERS_STATUS_HISTORY, $mb_payment_history_data_array);

                        return true;
                    }

                    break;
                // error occured during query
                // errors documented in the moneybookers doc
                case "401": // 	Authorization declined
                case "402": //	Unknown action
                case "403": //	Transaction not found
                case "404": //	Missing parameter
                case "405": //	Illegal parameter value
                    preg_match("/[^\d\t]+.*/i", $result, $return_array);
                    $mb_payment_history_data_array = array('mb_trans_id' => $trans_id_row['mb_trans_id'],
                        'mb_date' => 'now()',
                        'mb_err_no' => $return_code[0],
                        'mb_err_txt' => $return_array[0]
                    );
                    tep_db_perform(TABLE_PAYMENT_MONEYBOOKERS_STATUS_HISTORY, $mb_payment_history_data_array);

                    return false;

                    break;
                // unknown error
                default:
                    return false;

                    break;
            }
        }
    }

    function get_orders_payment_info_file() {
        return DIR_FS_CATALOG_MODULES . 'payment/mb/admin/orders.inc.php';
    }

    function get_orders_list_payment_info_file() {
        return DIR_FS_CATALOG_MODULES . 'payment/mb/admin/orders_list.inc.php';
    }

    function get_ipn_class_file() {
        return DIR_FS_CATALOG_MODULES . 'payment/mb/classes/mb_ipn_class.php';
    }

}

?>