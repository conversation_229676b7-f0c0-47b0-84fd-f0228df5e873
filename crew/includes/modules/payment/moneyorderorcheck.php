<?

/*
  $Id: moneyorderorcheck.php, v1.0 2005/12/02 10:24:51
  Author : <PERSON> (<EMAIL>)
  Title: check / moneyorder Payment Module V1.0

  Copyright (c) 2005 SKC Venture

  Released under the GNU General Public License
 */

class moneyorderorcheck {

    var $code, $title, $description, $enabled;

    // class constructor
    function moneyorderorcheck($pm_id = '') {
        global $order, $currency, $languages_id;

        //Basic info
        $this->payment_methods_id = 0;
        $this->payment_methods_parent_id = 0;

        $this->code = 'moneyorderorcheck';
        $this->filename = 'moneyorderorcheck.php';

        $this->title = $this->code;
        $this->preferred = true;
        $this->auto_cancel_period = 17280; // In minutes

        $payment_methods_select_sql = "	SELECT pm.payment_methods_parent_id, pm.payment_methods_code, 
												pmd.payment_methods_description_title, 
												pm.payment_methods_receive_status_mode, pm.payment_methods_id, 
												pm.payment_methods_title, pm.payment_methods_sort_order, 
												pm.payment_methods_receive_status, pm.payment_methods_legend_color 
										FROM " . TABLE_PAYMENT_METHODS . " as pm
										LEFT JOIN " . TABLE_PAYMENT_METHODS_DESCRIPTION . " as pmd
											ON ( pmd.payment_methods_id = pm.payment_methods_id AND pmd.languages_id = '" . (int) $languages_id . "' )
										WHERE 1 ";
        if ((int) $pm_id > 0) {
            $payment_methods_select_sql .= "	AND pm.payment_methods_id = '" . (int) $pm_id . "'";
        } else {
            $payment_methods_select_sql .= "	AND pm.payment_methods_filename = '" . tep_db_input($this->filename) . "'";
        }

        $payment_methods_result_sql = tep_db_query($payment_methods_select_sql);

        if ($payment_methods_row = tep_db_fetch_array($payment_methods_result_sql)) {
            $this->payment_methods_id = (int) $payment_methods_row['payment_methods_id'];
            $this->payment_methods_parent_id = (int) $payment_methods_row['payment_methods_parent_id'];
            $this->code = $payment_methods_row['payment_methods_code'];
            $this->title = $payment_methods_row['payment_methods_title']; //MODULE_PAYMENT_PAYPAL_TEXT_TITLE;
            $this->display_title = $payment_methods_row['payment_methods_description_title']; //MODULE_PAYMENT_PAYPAL_TEXT_DISPLAY_TITLE; 	// if title to be display for payment method is different from payment method value
            $this->description = $this->display_title;
            $this->sort_order = $payment_methods_row['payment_methods_sort_order']; //MODULE_PAYMENT_PAYPAL_SORT_ORDER;
            $this->enabled = $payment_methods_row['payment_methods_receive_status_mode'];
            $this->legend_display_colour = $payment_methods_row['payment_methods_legend_color'];
            $this->receive_status = $payment_methods_row['payment_methods_receive_status'];
            $this->description = $this->display_title;
            $this->mocCurrencies = $this->get_support_currencies($this->payment_methods_id);

            $configuration_setting_array = $this->load_pm_setting();
            $this->notification_email = $configuration_setting_array['MODULE_PAYMENT_MOORCHECK_NOTIFICATION_EMAIL'];
            //$this->zone = $configuration_setting_array['MODULE_PAYMENT_MOORCHECK_ZONE'];
            $this->order_status_id = $configuration_setting_array['MODULE_PAYMENT_MOORCHECK_ORDER_STATUS_ID'];
            $this->processing_status_id = $configuration_setting_array['MODULE_PAYMENT_MOORCHECK_PROCESSING_STATUS_ID'];
            $this->confirm_complete_days = $configuration_setting_array['MODULE_PAYMENT_MOORCHECK_CONFIRM_COMPLETE'];
            $this->message = $configuration_setting_array['MODULE_PAYMENT_MOORCHECK_MESSAGE'];
            $this->email_message = $configuration_setting_array['MODULE_PAYMENT_MOORCHECK_EMAIL_MESSAGE'];
            $this->require_address_information = $configuration_setting_array['MODULE_PAYMENT_MOORCHECK_MANDATORY_ADDRESS_FIELD'];
            $this->require_ic_information = $configuration_setting_array['MODULE_PAYMENT_MOORCHECK_MANDATORY_IC_FIELD'];
            $this->require_contact_information = $configuration_setting_array['MODULE_PAYMENT_MOORCHECK_MANDATORY_CONTACT_FIELD'];

            $this->customer_payment_info = $configuration_setting_array['MODULE_PAYMENT_MOORCHECK_CUSTOMER_PAYMENT_INFO'];

            if ((int) $this->order_status_id > 0) {
                $this->order_status = $this->order_status_id;
            }
            //if (is_object($order)) $this->update_status();

            $this->email_footer = str_replace(array("<br>", "<BR>", "\r\n"), array("\n", "\n", ''), $this->email_message);
            $this->order_processing_status = (int) $this->processing_status_id > 0 ? $this->processing_status_id : 7;
            $this->processing_payment_notification_mail = $this->notification_email; // When to trigger is based on $this->order_processing_status.
        }
    }

    function javascript_validation() {
        return false;
    }

    function selection() {
        global $languages_id;

        $selection_array = array();

        $payment_methods_select_sql = "	SELECT pm.payment_methods_id, pm.payment_methods_code, pm.payment_methods_types_id, pm.payment_methods_logo, pmd.payment_methods_description_title 
										FROM " . TABLE_PAYMENT_METHODS . " AS pm 
										LEFT JOIN " . TABLE_PAYMENT_METHODS_DESCRIPTION . " AS pmd 
											ON (pm.payment_methods_id = pmd.payment_methods_id AND pmd.languages_id = '" . (int) $languages_id . "') 
										WHERE pm.payment_methods_receive_status = 1 
											AND pm.payment_methods_parent_id = '" . (int) $this->payment_methods_id . "'
										ORDER BY pm.payment_methods_sort_order";
        $payment_methods_result_sql = tep_db_query($payment_methods_select_sql);
        while ($payment_methods_row = tep_db_fetch_array($payment_methods_result_sql)) {
            $selection_array[] = array('payment_gateway_code' => $this->code,
                'payment_methods_id' => $payment_methods_row['payment_methods_id'],
                'payment_methods_code' => $payment_methods_row['payment_methods_code'],
                'payment_methods_types_id' => $payment_methods_row['payment_methods_types_id'],
                'payment_methods_parent_id' => $this->payment_methods_id,
                'payment_methods_logo' => $payment_methods_row['payment_methods_logo'],
                'payment_methods_description_title' => $payment_methods_row['payment_methods_description_title'],
                'currency' => $this->get_support_currencies($payment_methods_row['payment_methods_id']),
                'show_billing_address' => $pm_array['MODULE_PAYMENT_MOORCHECK_MANDATORY_ADDRESS_FIELD'],
                'confirm_complete_days' => $pm_array['MODULE_PAYMENT_MOORCHECK_CONFIRM_COMPLETE'],
                'show_contact_number' => $pm_array['MODULE_PAYMENT_MOORCHECK_MANDATORY_CONTACT_FIELD'],
                'show_ic' => $pm_array['MODULE_PAYMENT_MOORCHECK_MANDATORY_IC_FIELD']
            );
        }

        return $selection_array;
    }

    function get_support_currencies($pm_id) {
        $support_currencies_array = array();

        $currency_code_select_sql = "	SELECT currency_code 
    									FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " 
    									WHERE payment_methods_id = '" . (int) $pm_id . "' 
    									ORDER BY currency_code";
        $currency_code_result_sql = tep_db_query($currency_code_select_sql);

        if (tep_db_num_rows($currency_code_result_sql) > 0) {
            while ($currency_code_row = tep_db_fetch_array($currency_code_result_sql)) {
                $support_currencies_array[] = $currency_code_row['currency_code'];
            }
        } else {
            $parent_currency_code_select_sql = "	SELECT currency_code 
			    									FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " 
			    									WHERE payment_methods_id = '" . (int) $this->payment_methods_id . "' 
			    									ORDER BY currency_code";
            $parent_currency_code_result_sql = tep_db_query($parent_currency_code_select_sql);
            while ($parent_currency_code_row = tep_db_fetch_array($parent_currency_code_result_sql)) {
                $support_currencies_array[] = $parent_currency_code_row['currency_code'];
            }
        }

        return $support_currencies_array;
    }

    function pre_confirmation_check() {
        global $currency;

        if (in_array($currency, $this->mocCurrencies)) {
            
        }

        return false;
    }

    function process_button() {
        return false;
    }

    function before_process() {
        return false;
    }

    function after_process() {
        return false;
    }

    function get_error() {
        return false;
    }

    function check() {
        if (!isset($this->_check)) {
            $check_query = tep_db_query("select configuration_value from " . TABLE_CONFIGURATION . " where configuration_key = 'MODULE_PAYMENT_MOORCHECK_STATUS'");
            $this->_check = tep_db_num_rows($check_query);
        }
        return $this->_check;
    }

    function load_pm_setting($language_id = 1) {
        $pm_setting_array = array();
        //load value from DB
        $payment_configuration_info_select_sql = "	SELECT pci.payment_configuration_info_key, pcid.payment_configuration_info_value
													FROM " . TABLE_PAYMENT_CONFIGURATION_INFO . " as pci
													LEFT JOIN " . TABLE_PAYMENT_CONFIGURATION_INFO_DESCRIPTION . " as pcid 
														ON pci.payment_configuration_info_id = pcid.payment_configuration_info_id
															AND pcid.languages_id = '" . (int) $language_id . "'
													WHERE pci.payment_methods_id = '" . (int) $this->payment_methods_id . "'
													ORDER BY pci.payment_configuration_info_sort_order";
        $payment_configuration_info_result_sql = tep_db_query($payment_configuration_info_select_sql);
        if (tep_db_num_rows($payment_configuration_info_result_sql)) {
            while ($payment_configuration_info_row = tep_db_fetch_array($payment_configuration_info_result_sql)) {
                $pm_setting_array[$payment_configuration_info_row['payment_configuration_info_key']] = $payment_configuration_info_row['payment_configuration_info_value'];
            }
        } else if ((int) $this->payment_methods_parent_id > 0) { //load value from PG
            $payment_configuration_info_select_sql = "	SELECT pci.payment_configuration_info_key, pcid.payment_configuration_info_value
														FROM " . TABLE_PAYMENT_CONFIGURATION_INFO . " as pci
														LEFT JOIN " . TABLE_PAYMENT_CONFIGURATION_INFO_DESCRIPTION . " as pcid 
															ON pci.payment_configuration_info_id = pcid.payment_configuration_info_id
																AND pcid.languages_id = '" . (int) $language_id . "'
														WHERE pci.payment_methods_id = '" . (int) $this->payment_methods_parent_id . "'
														ORDER BY pci.payment_configuration_info_sort_order";
            $payment_configuration_info_result_sql = tep_db_query($payment_configuration_info_select_sql);
            while ($payment_configuration_info_row = tep_db_fetch_array($payment_configuration_info_result_sql)) {
                $pm_setting_array[$payment_configuration_info_row['payment_configuration_info_key']] = $payment_configuration_info_row['payment_configuration_info_value'];
            }
        }
        return $pm_setting_array;
    }

    function is_supported_currency($selected_currency) {
        return (in_array($selected_currency, $this->get_support_currencies()) ? true : false);
    }

    function get_merchant_account($selected_currency) {
        $payment_methods_instance_id = 0;
        $payment_gateway_instance_select_sql = "SELECT pmi.payment_methods_instance_id, pmi.payment_methods_instance_follow_default, pmi.currency_code 
				 								FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " as pmi
				 								WHERE pmi.payment_methods_id = '" . (int) $this->payment_methods_id . "'
				 								LIMIT 1";
        $payment_gateway_instance_result_sql = tep_db_query($payment_gateway_instance_select_sql);
        if (tep_db_num_rows($payment_gateway_instance_result_sql)) {
            $payment_gateway_instance_select_sql = "SELECT pmi.payment_methods_instance_id, pmi.payment_methods_instance_follow_default 
					 								FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " as pmi
					 								WHERE pmi.currency_code = '" . tep_db_input($selected_currency) . "' 
					 									AND pmi.payment_methods_id = '" . (int) $this->payment_methods_id . "'";
            $payment_gateway_instance_result_sql = tep_db_query($payment_gateway_instance_select_sql);
            if ($payment_gateway_instance_row = tep_db_fetch_array($payment_gateway_instance_result_sql)) {
                if ((int) $payment_gateway_instance_row['payment_methods_instance_follow_default'] > 0) { // Follow Default Currency Setting
                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_follow_default'];
                } else { // Has own setting
                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_id'];
                }
            }
        } else {
            $payment_gateway_instance_select_sql = "SELECT pmi.payment_methods_instance_id, pmi.payment_methods_instance_follow_default 
					 								FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " as pmi
					 								INNER JOIN " . TABLE_PAYMENT_METHODS . " as pm
					 									ON pm.payment_methods_parent_id = pmi.payment_methods_id
					 								WHERE pmi.currency_code = '" . tep_db_input($selected_currency) . "' 
					 									AND pm.payment_methods_id = '" . (int) $this->payment_methods_id . "'";
            $payment_gateway_instance_result_sql = tep_db_query($payment_gateway_instance_select_sql);
            if ($payment_gateway_instance_row = tep_db_fetch_array($payment_gateway_instance_result_sql)) {
                if ((int) $payment_gateway_instance_row['payment_methods_instance_follow_default'] > 0) {
                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_follow_default'];
                } else {
                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_id'];
                }
            }
        }
        $payment_gateway_instance_settinh_select_sql = "SELECT pmis.payment_gateway_instance_key, pmis.payment_gateway_instance_value 
						 								FROM " . TABLE_PAYMENT_METHODS_INSTANCE_SETTING . " as pmis
						 								WHERE pmis.payment_methods_instance_id = '" . (int) $payment_methods_instance_id . "'";
        $payment_gateway_instance_settinh_result_sql = tep_db_query($payment_gateway_instance_settinh_select_sql);
        while ($payment_gateway_instance_settinh_row = tep_db_fetch_array($payment_gateway_instance_settinh_result_sql)) {
            switch ($payment_gateway_instance_row['payment_gateway_instance_key']) {
                case 'MODULE_PAYMENT_MOORCHECK_PAYTO':
                    $this->payTo = $payment_gateway_instance_row['payment_gateway_instance_value'];
                    break;
            }
        }
    }

    function install() {
        $install_configuration_flag = true;

        if ($this->payment_methods_id > 0) {
            if ($this->receive_status != 1) {
                $update_receive_status_sql_data = array("payment_methods_receive_status" => 1);
                tep_db_perform(TABLE_PAYMENT_METHODS, $update_receive_status_sql_data, 'update', "payment_methods_id = '" . (int) $this->payment_methods_id . "'");
            } else {
                $install_configuration_flag = false;
            }
        } else {
            $install_payment_gateway_data_sql = array('payment_methods_code' => $this->code,
                'payment_methods_send_status' => 0,
                'payment_methods_title' => $this->code,
                'payment_methods_types_id' => 0,
                'payment_methods_parent_id' => 0,
                'payment_methods_legend_color' => '#660099',
                'payment_methods_sort_order' => 8,
                'payment_methods_filename' => $this->filename,
                'payment_methods_logo' => '',
                'date_added' => 'now()',
                'last_modified' => 'now()',
                'payment_methods_receive_status' => '1',
                'payment_methods_receive_status_mode' => 0
            );
            tep_db_perform(TABLE_PAYMENT_METHODS, $install_payment_gateway_data_sql);
            $this->payment_methods_id = tep_db_insert_id();
        }

        if ($install_configuration_flag) {
            $install_key_array = array();
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Payment Info Notification Email Address',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_MOORCHECK_NOTIFICATION_EMAIL',
                    'payment_configuration_info_description' => 'Email address to which the notification email will be send to when payment information is entered.<br>(In "Name &lt;Email&gt;" format. Use \',\' as delimiter for multiple recipient)',
                    'payment_configuration_info_sort_order' => '715',
                    'set_function' => '',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => '',
                    'languages_id' => 1
                )
            );
            /* 			$install_key_array[] = array(	'info' => array (	'payment_methods_id'=>(int)$this->payment_methods_id,
              'payment_configuration_info_title'=>'Payment Zone',
              'payment_configuration_info_key'=>'MODULE_PAYMENT_MOORCHECK_ZONE',
              'payment_configuration_info_description'=>'If a zone is selected, only enable this payment method for that zone.',
              'payment_configuration_info_sort_order'=>'720',
              'set_function'=>'tep_cfg_pull_down_zone_classes(',
              'use_function'=>'tep_get_zone_class_title',
              'date_added'=>'now()'
              ),
              'desc' => array (	'payment_configuration_info_value'=>'0',
              'languages_id' => 1
              )
              ); */
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Set Order Status',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_MOORCHECK_ORDER_STATUS_ID',
                    'payment_configuration_info_description' => 'Set the status of orders made with this payment module to this value',
                    'payment_configuration_info_sort_order' => '730',
                    'set_function' => 'tep_cfg_pull_down_order_statuses(',
                    'use_function' => 'tep_get_order_status_name',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => '0',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Set Order\'s "Processing" Status',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_MOORCHECK_PROCESSING_STATUS_ID',
                    'payment_configuration_info_description' => 'Set the initial processing status of orders made with this payment module to this value',
                    'payment_configuration_info_sort_order' => '735',
                    'set_function' => 'tep_cfg_pull_down_order_statuses(',
                    'use_function' => 'tep_get_order_status_name',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => '7',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Payment Message',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_MOORCHECK_MESSAGE',
                    'payment_configuration_info_description' => 'Payment message will show up during checkout process (Check/Money Order)',
                    'payment_configuration_info_sort_order' => '745',
                    'set_function' => 'tep_cfg_textarea(',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'Thank you for shopping at OffGamers.com.',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Payment Email Message',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_MOORCHECK_EMAIL_MESSAGE',
                    'payment_configuration_info_description' => 'This message will show up in order confirmation e-mail.',
                    'payment_configuration_info_sort_order' => '750',
                    'set_function' => 'tep_cfg_textarea(',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'Thank you for shopping at OffGamers.com.',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Confirm Complete (in days)',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_MOORCHECK_CONFIRM_COMPLETE',
                    'payment_configuration_info_description' => 'The number of days in which an order made by this payment method is confirm completeds',
                    'payment_configuration_info_sort_order' => '760',
                    'set_function' => '',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => '0',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Require Address Information',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_MOORCHECK_MANDATORY_ADDRESS_FIELD',
                    'payment_configuration_info_description' => 'Set address field as required info during checkout.',
                    'payment_configuration_info_sort_order' => '770',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Customer Payment Info',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_MOORCHECK_CUSTOMER_PAYMENT_INFO',
                    'payment_configuration_info_description' => 'Set to true if this Payment Method required customer input for Payment Information required customer payment info.',
                    'payment_configuration_info_sort_order' => '780',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Require IC Information',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_MOORCHECK_MANDATORY_IC_FIELD',
                    'payment_configuration_info_description' => 'Set IC field as required info during checkout.',
                    'payment_configuration_info_sort_order' => '790',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Require Contact Information',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_MOORCHECK_MANDATORY_CONTACT_FIELD',
                    'payment_configuration_info_description' => 'Set Contact field as required info during checkout.',
                    'payment_configuration_info_sort_order' => '800',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );
            foreach ($install_key_array as $data) {
                tep_db_perform(TABLE_PAYMENT_CONFIGURATION_INFO, $data['info']);
                $payment_conf_id = tep_db_insert_id();

                $data['desc']['payment_configuration_info_id'] = (int) $payment_conf_id;
                tep_db_perform(TABLE_PAYMENT_CONFIGURATION_INFO_DESCRIPTION, $data['desc']);
            }
        }
        return 1;
    }

    function remove() {
        tep_db_query("delete from " . TABLE_CONFIGURATION . " where configuration_key in ('" . implode("', '", $this->keys()) . "')");
    }

    function keys() {
        return array('MODULE_PAYMENT_MOORCHECK_STATUS',
            'MODULE_PAYMENT_MOORCHECK_NOTIFICATION_EMAIL',
            'MODULE_PAYMENT_MOORCHECK_ZONE',
            'MODULE_PAYMENT_MOORCHECK_SORT_ORDER',
            'MODULE_PAYMENT_MOORCHECK_ORDER_STATUS_ID',
            'MODULE_PAYMENT_MOORCHECK_PROCESSING_STATUS_ID',
            'MODULE_PAYMENT_MOORCHECK_PAYTO',
            'MODULE_PAYMENT_MOORCHECK_CONFIRM_COMPLETE',
            'MODULE_PAYMENT_MOORCHECK_MESSAGE',
            'MODULE_PAYMENT_MOORCHECK_EMAIL_MESSAGE',
            'MODULE_PAYMENT_MOORCHECK_LEGEND_COLOUR');
    }

    function configuration_information_keys() {
        return array('MODULE_PAYMENT_MOORCHECK_NOTIFICATION_EMAIL',
            //'MODULE_PAYMENT_MOORCHECK_ZONE', 
            'MODULE_PAYMENT_MOORCHECK_ORDER_STATUS_ID',
            'MODULE_PAYMENT_MOORCHECK_PROCESSING_STATUS_ID',
            'MODULE_PAYMENT_MOORCHECK_PAYTO',
            'MODULE_PAYMENT_MOORCHECK_CONFIRM_COMPLETE',
            'MODULE_PAYMENT_MOORCHECK_MESSAGE',
            'MODULE_PAYMENT_MOORCHECK_EMAIL_MESSAGE',
            'MODULE_PAYMENT_MOORCHECK_MANDATORY_ADDRESS_FIELD',
            'MODULE_PAYMENT_MOORCHECK_CUSTOMER_PAYMENT_INFO',
            'MODULE_PAYMENT_MOORCHECK_MANDATORY_IC_FIELD',
            'MODULE_PAYMENT_MOORCHECK_MANDATORY_CONTACT_FIELD');
    }

    function multi_lang_configuration_info_keys() {
        return array('MODULE_PAYMENT_MOORCHECK_MESSAGE',
            'MODULE_PAYMENT_MOORCHECK_EMAIL_MESSAGE');
    }

    function merchant_information_keys() {
        include_once(DIR_WS_CLASSES . "payment_methods.php");

        return array(
            'MODULE_PAYMENT_MOORCHECK_PAYTO' => array("field" => 'text', "mapping" => "MODULE_PAYMENT_MOORCHECK_LNG_PAYTO"),
            'MODULE_PAYMENT_TAX' => array("field" => 'select', 'mapping' => "MODULE_PAYMENT_TAX", "select" => payment_methods::get_tax_option())
        );
    }

    function get_pg_id() {
        return $this->payment_methods_id;
    }

    function get_orders_payment_info_file() {
        return DIR_FS_CATALOG_MODULES . 'payment/moneyordercheck/admin/orders.inc.php';
    }

    function get_orders_list_payment_info_file() {
        return DIR_FS_CATALOG_MODULES . 'payment/moneyordercheck/admin/orders_list.inc.php';
    }

    function get_ipn_class_file() {
        return '';
    }

}

?>