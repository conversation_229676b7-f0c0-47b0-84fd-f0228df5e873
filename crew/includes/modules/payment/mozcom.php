<?php

/*
  $Id: mozcom.php,v 1.7 2015/02/16 07:43:46 chingyen Exp $

  Developer: <PERSON>
  Copyright (c) 2007 SKC Ventrue

  Released under the GNU General Public License
 */

class mozcom {

    var $code, $title, $description, $sort_order, $enabled = false;
    var $email_footer, $mozcom_currencies, $defCurr;
    var $amount_decimal, $check_trans_status_flag;

    // class constructor
    function mozcom($pm_id = '') {
        global $order, $languages_id, $default_languages_id;

        //Basic info
        $this->payment_methods_id = 0;
        $this->payment_methods_parent_id = 0;

        $this->code = 'mozcom';
        $this->filename = 'mozcom.php';

        $this->force_to_checkoutprocess = true;
        $this->auto_cancel_period = 14400; // In minutes
        $this->connect_via_proxy = false;
        $this->amount_decimal = 2;

        //$this->has_ipn = false;

        $this->check_trans_status_flag = false;

        $payment_methods_select_sql = "	SELECT payment_methods_parent_id, payment_methods_code, 
												payment_methods_types_id, 
												payment_methods_receive_status_mode, payment_methods_id, 
												payment_methods_title, payment_methods_sort_order, 
												payment_methods_receive_status, payment_methods_legend_color, 
												payment_methods_logo 
										FROM " . TABLE_PAYMENT_METHODS . " 
										WHERE 1 ";
        if ((int) $pm_id > 0) {
            $payment_methods_select_sql .= "	AND payment_methods_id = '" . (int) $pm_id . "'";
        } else {
            $payment_methods_select_sql .= "	AND payment_methods_filename = '" . tep_db_input($this->filename) . "'";
        }

        $payment_methods_result_sql = tep_db_query($payment_methods_select_sql);
        if ($payment_methods_row = tep_db_fetch_array($payment_methods_result_sql)) {
            $pm_desc_title_select_sql = "	SELECT payment_methods_description_title 
											FROM " . TABLE_PAYMENT_METHODS_DESCRIPTION . " 
											WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "' 
												AND (IF (languages_id = '" . tep_db_input($languages_id) . "', 1, IF ((	SELECT COUNT(payment_methods_id) > 0 
																														FROM " . TABLE_PAYMENT_METHODS_DESCRIPTION . " 
																														WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "' 
																															AND languages_id = '" . (int) $languages_id . "'), 0, languages_id = '" . (int) $default_languages_id . "')))";
            $pm_desc_title_result_sql = tep_db_query($pm_desc_title_select_sql);
            $pm_desc_title_row = tep_db_fetch_array($pm_desc_title_result_sql);

            $this->payment_methods_id = (int) $payment_methods_row['payment_methods_id'];
            $this->payment_methods_parent_id = (int) $payment_methods_row['payment_methods_parent_id'];
            $this->code = $payment_methods_row['payment_methods_code'];
            $this->title = $payment_methods_row['payment_methods_title'];
            $this->display_title = $pm_desc_title_row['payment_methods_description_title'];
            $this->sort_order = $payment_methods_row['payment_methods_sort_order'];
            $this->enabled = $payment_methods_row['payment_methods_receive_status_mode'];
            $this->legend_display_colour = $payment_methods_row['payment_methods_legend_color'];
            $this->receive_status = $payment_methods_row['payment_methods_receive_status'];
            $this->preferred = true;
            $this->description = $this->display_title;
            $this->mozcom_currencies = $this->get_support_currencies($this->payment_methods_id);

            $configuration_setting_array = $this->load_pm_setting();
            $this->testing = ($configuration_setting_array['MODULE_PAYMENT_MOZCOM_TESTING'] == 'True' ? true : false);
            $this->order_status_id = $configuration_setting_array['MODULE_PAYMENT_MOZCOM_ORDER_STATUS_ID'];
            $this->processing_status_id = $configuration_setting_array['MODULE_PAYMENT_MOZCOM_PROCESSING_STATUS_ID'];
            $this->message = $configuration_setting_array['MODULE_PAYMENT_MOZCOM_MESSAGE'];
            $this->email_message = $configuration_setting_array['MODULE_PAYMENT_MOZCOM_EMAIL_MESSAGE'];
            $this->confirm_complete_days = $configuration_setting_array['MODULE_PAYMENT_MOZCOM_CONFIRM_COMPLETE'];

            $this->customer_payment_info = $configuration_setting_array['MODULE_PAYMENT_MOZCOM_CUSTOMER_PAYMENT_INFO'];
            $this->require_address_information = $configuration_setting_array['MODULE_PAYMENT_MOZCOM_MANDATORY_ADDRESS_FIELD'];
            $this->require_ic_information = $configuration_setting_array['MODULE_PAYMENT_MOZCOM_MANDATORY_IC_FIELD'];
            $this->require_contact_information = $configuration_setting_array['MODULE_PAYMENT_MOZCOM_MANDATORY_CONTACT_FIELD'];

            if ((int) $this->order_status_id > 0) {
                $this->order_status = $this->order_status_id;
            } else {
                $this->order_status = DEFAULT_ORDERS_STATUS_ID;
            }

            $this->order_processing_status = $this->processing_status_id;
            $this->email_footer = str_replace(array("<br>", "<BR>", "\r\n"), array("\n", "\n", ''), $this->email_message);

            $this->form_action_url = 'https://payeasy2.mozcom.com/v2/pay.aspx';
            if ($this->testing) {
                $this->form_action_url = 'http://payeasy2-uat.mozcom.com/v2/Pay.aspx';
            }
        }
    }

    function javascript_validation() {
        return false;
    }

    function selection() {
        global $languages_id, $default_languages_id;

        $selection_array = array();

        $payment_methods_select_sql = "	SELECT payment_methods_id, payment_methods_code, payment_methods_types_id, payment_methods_logo, payment_methods_receive_status_mode 
										FROM " . TABLE_PAYMENT_METHODS . " 
										WHERE payment_methods_receive_status = 1 
											AND payment_methods_receive_status_mode <> 0 
											AND payment_methods_parent_id = '" . (int) $this->payment_methods_id . "' 
										ORDER BY payment_methods_sort_order";
        $payment_methods_result_sql = tep_db_query($payment_methods_select_sql);
        while ($payment_methods_row = tep_db_fetch_array($payment_methods_result_sql)) {
            $pm_desc_title_select_sql = "	SELECT payment_methods_description_title 
											FROM " . TABLE_PAYMENT_METHODS_DESCRIPTION . " 
											WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "' 
												AND (IF (languages_id = '" . tep_db_input($languages_id) . "', 1, IF ((	SELECT COUNT(payment_methods_id) > 0 
																														FROM " . TABLE_PAYMENT_METHODS_DESCRIPTION . " 
																														WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "' 
																															AND languages_id = '" . (int) $languages_id . "'), 0, languages_id = '" . (int) $default_languages_id . "')))";
            $pm_desc_title_result_sql = tep_db_query($pm_desc_title_select_sql);
            $pm_desc_title_row = tep_db_fetch_array($pm_desc_title_result_sql);

            $pm_array = $this->load_pm_setting(1, $payment_methods_row['payment_methods_id']);

            $selection_array[] = array('payment_gateway_code' => $this->code,
                'payment_methods_receive_status_mode' => $payment_methods_row['payment_methods_receive_status_mode'],
                'payment_methods_id' => $payment_methods_row['payment_methods_id'],
                'payment_methods_code' => $payment_methods_row['payment_methods_code'],
                'payment_methods_types_id' => $payment_methods_row['payment_methods_types_id'],
                'payment_methods_parent_id' => $this->payment_methods_id,
                'payment_methods_logo' => $payment_methods_row['payment_methods_logo'],
                'payment_methods_description_title' => $pm_desc_title_row['payment_methods_description_title'],
                'currency' => $this->get_support_currencies($payment_methods_row['payment_methods_id']),
                'show_billing_address' => $pm_array['MODULE_PAYMENT_MOZCOM_MANDATORY_ADDRESS_FIELD'],
                'confirm_complete_days' => $pm_array['MODULE_PAYMENT_MOZCOM_CONFIRM_COMPLETE'],
                'show_contact_number' => $pm_array['MODULE_PAYMENT_MOZCOM_MANDATORY_CONTACT_FIELD'],
                'show_ic' => $pm_array['MODULE_PAYMENT_MOZCOM_MANDATORY_IC_FIELD']
            );

            if ($payment_methods_row['payment_methods_receive_status_mode'] == '-1') {
                $pm_status_message_select = "	SELECT payment_methods_status_message 
												FROM " . TABLE_PAYMENT_METHODS_STATUS_DESCRIPTION . " 
												WHERE payment_methods_mode = 'RECEIVE' 
													AND payment_methods_status = '-1' 
													AND languages_id = '" . (int) $languages_id . "' 
													AND payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "'";
                $pm_status_message_result = tep_db_query($pm_status_message_select);
                $pm_status_message_row = tep_db_fetch_array($pm_status_message_result);
                $selection_array[sizeof($selection_array) - 1]['payment_methods_status_message'] = $pm_status_message_row['payment_methods_status_message'];
            }
        }

        return $selection_array;
    }

    function set_support_currencies($currency_array) {
        $this->mozcom_currencies = $currency_array;
    }

    function get_support_currencies($pm_id, $by_zone = true) {
        global $zone_info_array;

        $default_currency = '';
        $support_currencies_array = array();

        $currency_code_select_sql = "	SELECT currency_code, payment_methods_instance_default 
    									FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " 
    									WHERE payment_methods_id = '" . (int) $pm_id . "' 
    									ORDER BY currency_code";
        $currency_code_result_sql = tep_db_query($currency_code_select_sql);
        if (tep_db_num_rows($currency_code_result_sql) > 0) {
            while ($currency_code_row = tep_db_fetch_array($currency_code_result_sql)) {
                $support_currencies_array[] = $currency_code_row['currency_code'];

                if ($currency_code_row['payment_methods_instance_default'] == 1) {
                    $default_currency = $currency_code_row['currency_code'];
                }
            }
        } else {
            if ($this->payment_methods_parent_id > 0) {
                $payment_methods_parent_id = $this->payment_methods_parent_id;
            } else {
                $payment_methods_parent_id = $this->payment_methods_id;
            }

            $parent_currency_code_select_sql = "	SELECT currency_code, payment_methods_instance_default 
			    									FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " 
			    									WHERE payment_methods_id = '" . (int) $payment_methods_parent_id . "' 
			    									ORDER BY currency_code";
            $parent_currency_code_result_sql = tep_db_query($parent_currency_code_select_sql);
            while ($parent_currency_code_row = tep_db_fetch_array($parent_currency_code_result_sql)) {
                $support_currencies_array[] = $parent_currency_code_row['currency_code'];

                if ($parent_currency_code_row['payment_methods_instance_default'] == 1) {
                    $default_currency = $parent_currency_code_row['currency_code'];
                }
            }
        }

        if (tep_not_null($default_currency)) {
            $this->defCurr = $default_currency;
        }

        if ($by_zone) {
            if (isset($zone_info_array)) {
                $support_currencies_array = array_intersect($zone_info_array[3]->zone_currency_id, $support_currencies_array);
                if (count($support_currencies_array)) {
                    array_multisort($support_currencies_array);
                } else {
                    $support_currencies_array = array($default_currency);
                }
            } else {
                $support_currencies_array = array($default_currency);
            }
        } else {
            $this->set_support_currencies($support_currencies_array);
            // All found currencies is supported. Normally this is used for backend script such as IPN call
        }

        return $support_currencies_array;
    }

    function get_confirm_complete_days() {
        return $this->confirm_complete_days;
    }

    function get_pm_info() {
        return $this->message;
    }

    function get_email_pm_info() {
        return $this->email_message;
    }

    function draw_confirm_button() {
        return tep_image_button2('red', 'javascript:document.checkout_confirmation.submit()', MODULE_PAYMENT_MOZCOM_TEXT_CART, 200);
    }

    function get_confirm_button_caption() {
        return MODULE_PAYMENT_MOZCOM_TEXT_TITLE;
    }

    function pre_confirmation_check() {
        /*
          global $currency;

          if (in_array($currency, $this->mozcom_currencies)) {

          }
         */
        return false;
    }

    function process_button() {
        global $_POST, $shipping_cost, $total_cost, $shipping_selected, $shipping_method, $currencies, $currency, $customer_id, $order, $languages_id;
        global $order_logged, $OFFGAMERS;

        if ($this->force_to_checkoutprocess && !tep_session_is_registered('order_logged'))
            return;

        $mozcom_currencies = $currency;
        if (!in_array($mozcom_currencies, $this->mozcom_currencies)) {
            $mozcom_currencies = $this->mozcom_currencies[0];
        }
        $this->get_merchant_account($mozcom_currencies);

        $payment_gateway_amount = number_format($order->info['total'] * $currencies->get_value($mozcom_currencies), $this->amount_decimal, '.', '');

        $digest_str = "$payment_gateway_amount:$order_logged:$this->merchantid:$this->password:$mozcom_currencies";
        $digest = sha1($digest_str);

        $process_button_string =
                tep_draw_hidden_field('merchantid', $this->merchantid) .
                tep_draw_hidden_field('txnid', $order_logged) .
                tep_draw_hidden_field('amount', $payment_gateway_amount) .
                tep_draw_hidden_field('description', 'Purchase from ' . STORE_NAME) .
                tep_draw_hidden_field('ccy', $mozcom_currencies) .
                tep_draw_hidden_field('digest', $digest);
        return $process_button_string;
    }

    // manage returning data from iPay88 (errors, failures, success etc.)
    function before_process() {
        global $order_logged, $_POST, $currencies, $currency, $order, $customer_id;

        $error_flag = true;

        if (!tep_session_is_registered('order_logged')) {
            return;
        } else {
            if (isset($_REQUEST['result']) && tep_not_null($_REQUEST['result'])) {

                $order_select_sql = "	SELECT o.currency, o.currency_value, ot.text, ot.value 
										FROM " . TABLE_ORDERS . " AS o
										INNER JOIN " . TABLE_ORDERS_TOTAL . " AS ot
											ON o.orders_id = ot.orders_id
										WHERE o.orders_id = '" . $order_logged . "'
											AND ot.class = 'ot_total'";
                $order_result_sql = tep_db_query($order_select_sql);
                if ($order_row = tep_db_fetch_array($order_result_sql)) {

                    $mozcom_data_sql = array('mozcom_status' => tep_db_prepare_input(strtolower($_REQUEST['result'])),
                        'mozcom_ref_num' => tep_db_prepare_input($_REQUEST['refnum']),
                        'mozcom_client_email' => tep_db_prepare_input($_REQUEST['clientemail']),
                        'mozcom_client_name' => tep_db_prepare_input($_REQUEST['clientname']),
                        'mozcom_client_phone' => tep_db_prepare_input($_REQUEST['clientphone']),
                        'mozcom_client_address' => tep_db_prepare_input($_REQUEST['clientaddress']),
                        'mozcom_digest2' => tep_db_prepare_input($_REQUEST['digest']),
                        'mozcom_reason' => (isset($_REQUEST['reason']) ? tep_db_prepare_input($_REQUEST['reason']) : '')
                    );

                    $mozcom_currencies = $order_row['currency'];
                    if (!in_array($mozcom_currencies, $this->mozcom_currencies)) {
                        $mozcom_currencies = $this->mozcom_currencies[0];
                    }
                    $this->get_merchant_account($mozcom_currencies);

                    $rdigest_str = $order_logged . ':' . $_REQUEST['result'] . ':' . $this->merchantid . ':' . $this->password;
                    $chk_rdigest = sha1($rdigest_str);
                    if ($_REQUEST['digest'] == $chk_rdigest) {
                        $error_flag = false;
                    } else if (strtolower($_REQUEST['result']) == 'success') {
                        $mozcom_data_sql['mozcom_status'] = 'Invalid';
                        $mozcom_data_sql['mozcom_reason'] = 'Invalid Digest Key, ' . $_REQUEST['digest'];
                    }

                    $mozcom_select_sql = "	SELECT orders_id
											FROM " . TABLE_MOZCOM . " AS o
											WHERE orders_id = '" . $order_logged . "'";
                    $mozcom_result_sql = tep_db_query($mozcom_select_sql);
                    if (tep_db_num_rows($mozcom_result_sql)) {
                        tep_db_perform(TABLE_MOZCOM, $mozcom_data_sql, 'update', " orders_id = '" . $order_logged . "' ");
                    } else {
                        $mozcom_data_sql['orders_id'] = $order_logged;
                        tep_db_perform(TABLE_MOZCOM, $mozcom_data_sql);
                    }
                }
            }
        }

        if ($error_flag || strtolower($_REQUEST['result']) != 'success') {
            tep_redirect(tep_href_link(FILENAME_CHECKOUT_PAYMENT, 'error_message=' . urlencode(MODULE_PAYMENT_MOZCOM_TEXT_ERROR_MESSAGE), 'SSL', true, false));
        }
    }

    function after_process() {
        return false;
    }

    function link_to_payment($new_order_id) {
        global $order_logged;

        if (tep_session_is_registered('order_logged'))
            tep_session_unregister('order_logged');

        tep_session_register('order_logged');
        $order_logged = $new_order_id;
        require(DIR_WS_INCLUDES . 'modules/payment/mozcom/catalog/mozcom_splash.php');
        return;
    }

    function get_error() {
        $error = array('title' => MODULE_PAYMENT_MOZCOM_TEXT_ERROR,
            'error' => stripslashes(urldecode($_GET['error'])));
        return $error;
    }

    function check() {
        if (!isset($this->_check)) {
            $this->_check = defined('MODULE_PAYMENT_IPAY88_STATUS');
        }
        return $this->_check;
    }

    function is_processed_order($order_id) {
        /*         * *

          DO!

         * */
    }

    function is_supported_currency($selected_currency) {
        return (in_array($selected_currency, $this->mozcom_currencies) ? true : false);
    }

    function load_pm_setting($language_id = 1) {
        $pm_setting_array = array();
        //load value from DB
        $payment_configuration_info_select_sql = "	SELECT pci.payment_configuration_info_key, pcid.payment_configuration_info_value
													FROM " . TABLE_PAYMENT_CONFIGURATION_INFO . " as pci
													LEFT JOIN " . TABLE_PAYMENT_CONFIGURATION_INFO_DESCRIPTION . " as pcid 
														ON pci.payment_configuration_info_id = pcid.payment_configuration_info_id
															AND pcid.languages_id = '" . (int) $language_id . "'
													WHERE pci.payment_methods_id = '" . (int) $this->payment_methods_id . "'
													ORDER BY pci.payment_configuration_info_sort_order";
        $payment_configuration_info_result_sql = tep_db_query($payment_configuration_info_select_sql);
        if (tep_db_num_rows($payment_configuration_info_result_sql)) {
            while ($payment_configuration_info_row = tep_db_fetch_array($payment_configuration_info_result_sql)) {
                $pm_setting_array[$payment_configuration_info_row['payment_configuration_info_key']] = $payment_configuration_info_row['payment_configuration_info_value'];
            }
        } else if ((int) $this->payment_methods_parent_id > 0) { //load value from PG
            $payment_configuration_info_select_sql = "	SELECT pci.payment_configuration_info_key, pcid.payment_configuration_info_value
														FROM " . TABLE_PAYMENT_CONFIGURATION_INFO . " as pci
														LEFT JOIN " . TABLE_PAYMENT_CONFIGURATION_INFO_DESCRIPTION . " as pcid 
															ON pci.payment_configuration_info_id = pcid.payment_configuration_info_id
																AND pcid.languages_id = '" . (int) $language_id . "'
														WHERE pci.payment_methods_id = '" . (int) $this->payment_methods_parent_id . "'
														ORDER BY pci.payment_configuration_info_sort_order";
            $payment_configuration_info_result_sql = tep_db_query($payment_configuration_info_select_sql);
            while ($payment_configuration_info_row = tep_db_fetch_array($payment_configuration_info_result_sql)) {
                $pm_setting_array[$payment_configuration_info_row['payment_configuration_info_key']] = $payment_configuration_info_row['payment_configuration_info_value'];
            }
        }
        return $pm_setting_array;
    }

    function get_merchant_account($selected_currency) {
        $payment_methods_instance_id = 0;
        $payment_gateway_instance_select_sql = "SELECT pmi.payment_methods_instance_id, pmi.payment_methods_instance_follow_default, pmi.currency_code 
				 								FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " as pmi
				 								WHERE pmi.payment_methods_id = '" . (int) $this->payment_methods_id . "'
				 								LIMIT 1";
        $payment_gateway_instance_result_sql = tep_db_query($payment_gateway_instance_select_sql);
        if (tep_db_num_rows($payment_gateway_instance_result_sql)) {
            $payment_gateway_instance_select_sql = "SELECT pmi.payment_methods_instance_id, pmi.payment_methods_instance_follow_default 
					 								FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " as pmi
					 								WHERE pmi.currency_code = '" . tep_db_input($selected_currency) . "' 
					 									AND pmi.payment_methods_id = '" . (int) $this->payment_methods_id . "'";
            $payment_gateway_instance_result_sql = tep_db_query($payment_gateway_instance_select_sql);
            if ($payment_gateway_instance_row = tep_db_fetch_array($payment_gateway_instance_result_sql)) {
                if ((int) $payment_gateway_instance_row['payment_methods_instance_follow_default'] > 0) { // Follow Default Currency Setting
                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_follow_default'];
                } else { // Has own setting
                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_id'];
                }
            }
        } else {
            $payment_gateway_instance_select_sql = "SELECT pmi.payment_methods_instance_id, pmi.payment_methods_instance_follow_default 
					 								FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " as pmi
					 								INNER JOIN " . TABLE_PAYMENT_METHODS . " as pm
					 									ON pm.payment_methods_parent_id = pmi.payment_methods_id
					 								WHERE pmi.currency_code = '" . tep_db_input($selected_currency) . "' 
					 									AND pm.payment_methods_id = '" . (int) $this->payment_methods_id . "'";
            $payment_gateway_instance_result_sql = tep_db_query($payment_gateway_instance_select_sql);
            if ($payment_gateway_instance_row = tep_db_fetch_array($payment_gateway_instance_result_sql)) {
                if ((int) $payment_gateway_instance_row['payment_methods_instance_follow_default'] > 0) {
                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_follow_default'];
                } else {
                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_id'];
                }
            }
        }
        $payment_gateway_instance_setting_select_sql = "SELECT pmis.payment_methods_instance_setting_key, pmis.payment_methods_instance_setting_value 
						 								FROM " . TABLE_PAYMENT_METHODS_INSTANCE_SETTING . " as pmis
						 								WHERE pmis.payment_methods_instance_id = '" . (int) $payment_methods_instance_id . "'";
        $payment_gateway_instance_setting_result_sql = tep_db_query($payment_gateway_instance_setting_select_sql);
        while ($payment_gateway_instance_setting_row = tep_db_fetch_array($payment_gateway_instance_setting_result_sql)) {
            switch ($payment_gateway_instance_setting_row['payment_methods_instance_setting_key']) {
                case 'MODULE_PAYMENT_MOZCOM_MERCHANTID':
                    $this->merchantid = $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];
                    break;
                case 'MODULE_PAYMENT_MOZCOM_PASSWORD':
                    $this->password = $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];
                    break;
            }
        }
    }

    function install() {
        $install_configuration_flag = true;

        if ($this->payment_methods_id > 0) {
            if ($this->receive_status != 1) {
                $update_receive_status_sql_data = array("payment_methods_receive_status" => 1);
                tep_db_perform(TABLE_PAYMENT_METHODS, $update_receive_status_sql_data, 'update', "payment_methods_id = '" . (int) $this->payment_methods_id . "'");
            } else {
                $install_configuration_flag = false;
            }
        } else {
            $install_payment_gateway_data_sql = array('payment_methods_code' => $this->code,
                'payment_methods_send_status' => 0,
                'payment_methods_title' => $this->code,
                'payment_methods_types_id' => 0,
                'payment_methods_parent_id' => 0,
                'payment_methods_legend_color' => '#950100',
                'payment_methods_sort_order' => 30,
                'payment_methods_filename' => $this->filename,
                'payment_methods_logo' => '',
                'date_added' => 'now()',
                'last_modified' => 'now()',
                'payment_methods_receive_status' => 1,
                'payment_methods_receive_status_mode' => 0
            );
            tep_db_perform(TABLE_PAYMENT_METHODS, $install_payment_gateway_data_sql);
            $this->payment_methods_id = tep_db_insert_id();
        }
        if ($install_configuration_flag) {
            $install_key_array = array();
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Set Order Status',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_MOZCOM_ORDER_STATUS_ID',
                    'payment_configuration_info_description' => 'Set the status of orders made with this payment module to this value.',
                    'payment_configuration_info_sort_order' => '1030',
                    'set_function' => 'tep_cfg_pull_down_order_statuses(',
                    'use_function' => 'tep_get_order_status_name',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => '1',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Set Order\'s "Processing" Status',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_MOZCOM_PROCESSING_STATUS_ID',
                    'payment_configuration_info_description' => 'Set the initial processing status of orders made with this payment module to this value.',
                    'payment_configuration_info_sort_order' => '1035',
                    'set_function' => 'tep_cfg_pull_down_order_statuses(',
                    'use_function' => 'tep_get_order_status_name',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => '7',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Testing Environment',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_MOZCOM_TESTING',
                    'payment_configuration_info_description' => 'Is under Testing Environment',
                    'payment_configuration_info_sort_order' => '0',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Payment Message',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_MOZCOM_MESSAGE',
                    'payment_configuration_info_description' => 'Payment message will show up during checkout process.',
                    'payment_configuration_info_sort_order' => '1045',
                    'set_function' => 'tep_cfg_textarea(',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'Thank you for shopping at OffGamers.com.',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Payment Email Message',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_MOZCOM_EMAIL_MESSAGE',
                    'payment_configuration_info_description' => 'This message will show up in order confirmation e-mail.',
                    'payment_configuration_info_sort_order' => '1045',
                    'set_function' => 'tep_cfg_textarea(',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'Thank you for shopping at OffGamers.com.',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Confirm Complete (in days)',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_MOZCOM_CONFIRM_COMPLETE',
                    'payment_configuration_info_description' => 'The number of days in which an order made by this payment method is confirm completed',
                    'payment_configuration_info_sort_order' => '1050',
                    'set_function' => '',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => '0',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Require Address Information',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_MOZCOM_MANDATORY_ADDRESS_FIELD',
                    'payment_configuration_info_description' => 'Set address field as required info during checkout.',
                    'payment_configuration_info_sort_order' => '1100',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Customer Payment Info',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_MOZCOM_CUSTOMER_PAYMENT_INFO',
                    'payment_configuration_info_description' => 'Set to true if this Payment Method required customer input for Payment Information required customer payment info.',
                    'payment_configuration_info_sort_order' => '1105',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Require IC Information',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_MOZCOM_MANDATORY_IC_FIELD',
                    'payment_configuration_info_description' => 'Set IC field as required info during checkout.',
                    'payment_configuration_info_sort_order' => '1110',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Require Contact Information',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_MOZCOM_MANDATORY_CONTACT_FIELD',
                    'payment_configuration_info_description' => 'Set Contact field as required info during checkout.',
                    'payment_configuration_info_sort_order' => '1115',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );
            foreach ($install_key_array as $data) {
                tep_db_perform(TABLE_PAYMENT_CONFIGURATION_INFO, $data['info']);
                $payment_conf_id = tep_db_insert_id();

                $data['desc']['payment_configuration_info_id'] = (int) $payment_conf_id;
                tep_db_perform(TABLE_PAYMENT_CONFIGURATION_INFO_DESCRIPTION, $data['desc']);
            }
        }
        return 1;
    }

    function remove() {
        tep_db_query("delete from " . TABLE_CONFIGURATION . " where configuration_key in ('" . implode("', '", $this->keys()) . "')");
    }

    function keys() {
        return array('MODULE_PAYMENT_MOZCOM_ORDER_STATUS_ID',
            'MODULE_PAYMENT_MOZCOM_PROCESSING_STATUS_ID',
            'MODULE_PAYMENT_MOZCOM_MESSAGE',
            'MODULE_PAYMENT_MOZCOM_EMAIL_MESSAGE',
            'MODULE_PAYMENT_MOZCOM_CONFIRM_COMPLETE',
            'MODULE_PAYMENT_MOZCOM_TRANTYPE',
            'MODULE_PAYMENT_MOZCOM_MANDATORY_ADDRESS_FIELD');
    }

    function configuration_information_keys() {
        return array('MODULE_PAYMENT_MOZCOM_ORDER_STATUS_ID',
            'MODULE_PAYMENT_MOZCOM_PROCESSING_STATUS_ID',
            'MODULE_PAYMENT_MOZCOM_MESSAGE',
            'MODULE_PAYMENT_MOZCOM_EMAIL_MESSAGE',
            'MODULE_PAYMENT_MOZCOM_CONFIRM_COMPLETE',
            'MODULE_PAYMENT_MOZCOM_MANDATORY_ADDRESS_FIELD',
            'MODULE_PAYMENT_MOZCOM_CUSTOMER_PAYMENT_INFO',
            'MODULE_PAYMENT_MOZCOM_MANDATORY_IC_FIELD',
            'MODULE_PAYMENT_MOZCOM_MANDATORY_CONTACT_FIELD');
    }

    function multi_lang_configuration_info_keys() {
        return array('MODULE_PAYMENT_MOZCOM_MESSAGE',
            'MODULE_PAYMENT_MOZCOM_EMAIL_MESSAGE');
    }

    function merchant_information_keys() {
        include_once(DIR_WS_CLASSES . "payment_methods.php");

        return array(
            'MODULE_PAYMENT_MOZCOM_MERCHANTID' => array("field" => 'text', "mapping" => 'MODULE_PAYMENT_MOZCOM_LNG_MERCHANTID'),
            'MODULE_PAYMENT_MOZCOM_PASSWORD' => array("field" => 'text', "mapping" => 'MODULE_PAYMENT_MOZCOM_LNG_PASSWORD'),
            'MODULE_PAYMENT_TAX' => array("field" => 'select', 'mapping' => "MODULE_PAYMENT_TAX", "select" => payment_methods::get_tax_option())
        );
    }

    function get_pg_id() {
        return $this->payment_methods_id;
    }

    function check_trans_status($order_id) {
        //
    }

    function get_orders_payment_info_file() {
        return DIR_FS_CATALOG_MODULES . 'payment/mozcom/admin/orders.inc.php';
    }

    function get_orders_list_payment_info_file() {
        return DIR_FS_CATALOG_MODULES . 'payment/mozcom/admin/orders_list.inc.php';
    }

    function get_ipn_class_file() {
        return '';
    }

    function clear_temp_process($match_case) {
        $delete_temp_process_sql = "DELETE FROM " . TABLE_TEMP_PROCESS . " 
									WHERE page_name = 'checkout_process.php' 
										AND match_case = '" . $match_case . "'";
        tep_db_query($delete_temp_process_sql);
    }

}

?>