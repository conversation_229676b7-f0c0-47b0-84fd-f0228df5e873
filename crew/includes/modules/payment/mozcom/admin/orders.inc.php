<?
include_once(DIR_FS_CATALOG_MODULES . 'payment/mozcom/admin/languages/' . $language . '/mozcom.lng.php');

$mozcom_trans_info_select_sql = "SELECT * FROM " . TABLE_MOZCOM . " WHERE orders_id='" . (int)$oID . "'";
$mozcom_trans_info_result_sql= tep_db_query($mozcom_trans_info_select_sql);

?>
<table border="0" width="100%" cellspacing="0" cellpadding="2">
<? ob_start(); 

?>
	<tr>
		<td class="main">
      		<table border="0" width="100%" cellspacing="0" cellpadding="2">
      			<tr>
      				<td class="main" width="12%" nowrap><b><?=ENTRY_PAYMENT_METHOD?></b></td>
      				<td class="main">
      					<div>
      						<? if((int)$order->info['payment_methods_id']>0) { ?><div class="<?=((int)$payment_info_array[$order->info['payment_methods_id']]->confirm_complete_days>0?'rp_icon':'nrp_icon')?>"><b><?=((int)$payment_info_array[$order->info['payment_methods_id']]->confirm_complete_days>0?'RP':'NRP')?></b></div> <? } ?>
      						<div style="width:30px; height:15px; background-color:<?=$payment_module_info['display_colour']?>; float:left; vertical-align:top; text-align:center;">&nbsp;</div>
      						<div style="vertical-align: top">
      							&nbsp;&nbsp;
<?
	if ($view_payment_details_permission) {
		if (tep_not_null($last_payment_method) && $last_payment_method['payment_methods_id'] != $order->info['payment_methods_id']) {
			echo '<span class="redIndicator">'.(tep_not_null($payment_module_info['payment_methods_parent_title'])?$payment_module_info['payment_methods_parent_title'] . ' - ' :'') . $payment_module_info['payment_methods_title'].'</span>';
		} else {
			echo (tep_not_null($payment_module_info['payment_methods_parent_title'])?$payment_module_info['payment_methods_parent_title'] . ' - ' :'') . $payment_module_info['payment_methods_title'];
		}
		
		if (tep_not_null($last_payment_method) && $last_payment_method['payment_methods_id'] != $order->info['payment_methods_id']) {
			echo '&nbsp;&nbsp;<span class="redIndicator">(Last Payment Method: ' . (tep_not_null($last_payment_method['parent_payment_methods_title'])? $last_payment_method['parent_payment_methods_title'] . ' - ' : '' ) . $last_payment_method['payment_methods_title'] . ')</span>';
		}
	} else {
		echo (tep_not_null($payment_module_info['payment_methods_parent_title'])?$payment_module_info['payment_methods_parent_title'] . ' - ' :'') . $payment_module_info['payment_methods_title'];
	}
?>
							</div>
      					</div>
      				</td>
				</tr>
			</table>
		</td>
	</tr>
<?
$payment_method_title_info = ob_get_contents();
ob_end_clean();

echo $payment_method_title_info;

if (tep_db_num_rows($mozcom_trans_info_result_sql)) {
	
	if (!$view_payment_details_permission) {
		;
	} else {
		
		$mozcom_trans_info_row = tep_db_fetch_array($mozcom_trans_info_result_sql);
?>
  	<tr>
    	<td class="main">
      		<table border="0" width="100%" cellspacing="0" cellpadding="2">
      			<tr valign="top">
        			<td width="39%" nowrap>
              			<table border="0" cellspacing="0" cellpadding="2">
              				<tr>
                				<td class="main" valign="top" nowrap><b><?=ENTRY_MOZCOM_STATUS?></b>&nbsp;</td>
                				<td class="main" nowrap><?=$mozcom_trans_info_row['mozcom_status']?></td>
              				</tr>
              				<tr>
                				<td class="main" valign="top" nowrap><b><?=ENTRY_MOZCOM_REFNUM?></b>&nbsp;</td>
                				<td class="main" nowrap><?=$mozcom_trans_info_row['mozcom_ref_num']?></td>
              				</tr>
							<tr>
			    				<td class="main" valign="top" nowrap><b><?=ENTRY_MOZCOM_CLIENTEMAIL?></b>&nbsp;</td>
			    				<td class="main" nowrap><?=$mozcom_trans_info_row["mozcom_client_email"]?></td>
			  				</tr>
              				<tr>
                				<td class="main" valign="top" nowrap><b><?=ENTRY_MOZCOM_CLIENTNAME?></b>&nbsp;</td>
                				<td class="main" nowrap><?=$mozcom_trans_info_row['mozcom_client_name']?></td>
              				</tr>
              				<tr>
                				<td class="main" valign="top" nowrap><b><?=ENTRY_MOZCOM_CLIENTPHONE?></b>&nbsp;</td>
                				<td class="main" nowrap><?=$mozcom_trans_info_row['mozcom_client_phone']?></td>
              				</tr>
              				<tr>
                				<td class="main" valign="top" nowrap><b><?=ENTRY_MOZCOM_CLIENTADDRESS?></b>&nbsp;</td>
                				<td class="main" nowrap><?=$mozcom_trans_info_row['mozcom_client_address']?></td>
              				</tr>
              				<tr>
                				<td class="main" valign="top" nowrap><b><?=ENTRY_MOZCOM_REASON?></b>&nbsp;</td>
                				<td class="main" nowrap><?=$mozcom_trans_info_row['mozcom_reason']?></td>
              				</tr>
              			</table>
        			</td>
  				</tr>
      		</table>
   		</td>
  	</tr>
<?
	}
}
?>
</table>