<?
include_once(DIR_FS_CATALOG_MODULES . 'payment/mozcom/admin/languages/' . $language . '/mozcom.lng.php');

$mozcom_trans_info_select_sql = "SELECT * FROM " . TABLE_MOZCOM . " WHERE orders_id = '" . $order_obj->orders_id . "'";
$mozcom_trans_info_result_sql= tep_db_query($mozcom_trans_info_select_sql);
?>
<table border="0" width="100%" cellspacing="0" cellpadding="2">
	<tr>
		<td class="subInvoiceBoxHeading">
			<div style="border-bottom: 1px solid #996600; border-top: 1px solid #996600; padding: 2px;">Payment Details</div>
		</td>
	</tr>
<?
if ($mozcom_trans_info_row = tep_db_fetch_array($mozcom_trans_info_result_sql)) {
?>
	<tr>
    	<td class="main">
      		<table border="0" width="100%" cellspacing="0" cellpadding="2">
      			<tr valign="top">
        			<td width="50%">
              			<table border="0" cellspacing="0" cellpadding="2">
              				<tr>
                				<td class="invoiceRecords" valign="top" nowrap><?=ENTRY_MOZCOM_REFNUM?></td>
                				<td class="invoiceRecords" valign="top">:&nbsp;</td>
                				<td class="invoiceRecords" nowrap><?=$mozcom_trans_info_row['mozcom_ref_num']?></td>
              				</tr>
              				<tr>
                				<td class="invoiceRecords" valign="top" nowrap><?=ENTRY_MOZCOM_STATUS?></td>
                				<td class="invoiceRecords" valign="top">:&nbsp;</td>
                				<td class="invoiceRecords" nowrap><?=$mozcom_trans_info_row['mozcom_status']?></td>
              				</tr>
              			</table>
        			</td>
      			</tr>
      		</table>
   		</td>
  	</tr>
<?
} else {
?>
	<tr>
		<td class="invoiceRecords">No further payment information is available.</td>
	</tr>
<?
}
?>