<?

/*
  Id: offline.php, v1.0 2006/04/10 12:43:17
  Author : <PERSON> (<EMAIL>)
  Title: Direct Deposit / Electronic Funds Transfer Payment Module V1.0

  Copyright (c) 2006 SKC Venture

  Released under the GNU General Public License
 */

class offline {

    var $code, $title, $description, $enabled, $offline_info;

    // class constructor
    function offline($pm_id = '') {
        global $order, $languages_id, $default_languages_id, $currency, $defCurr;

        $this->code = 'offline';
        $this->filename = 'offline.php';
        $this->title = $this->code;
        $this->preferred = true;
        $this->auto_cancel_period = 17280; // In minutes

        $payment_methods_select_sql = "	SELECT payment_methods_parent_id, payment_methods_code, 
												payment_methods_types_id, 
												payment_methods_receive_status_mode, payment_methods_id, 
												payment_methods_title, payment_methods_sort_order, 
												payment_methods_receive_status, payment_methods_legend_color, 
												payment_methods_logo 
										FROM " . TABLE_PAYMENT_METHODS . " 
										WHERE 1 ";
        if ((int) $pm_id > 0) {
            $payment_methods_select_sql .= "	AND payment_methods_id = '" . (int) $pm_id . "'";
        } else {
            $payment_methods_select_sql .= "	AND payment_methods_filename = '" . tep_db_input($this->filename) . "'";
        }

        $payment_methods_result_sql = tep_db_query($payment_methods_select_sql);
        if ($payment_methods_row = tep_db_fetch_array($payment_methods_result_sql)) {
            $pm_desc_title_select_sql = "	SELECT payment_methods_description_title 
											FROM " . TABLE_PAYMENT_METHODS_DESCRIPTION . " 
											WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "' 
												AND (IF (languages_id = '" . tep_db_input($languages_id) . "', 1, IF ((	SELECT COUNT(payment_methods_id) > 0 
																														FROM " . TABLE_PAYMENT_METHODS_DESCRIPTION . " 
																														WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "' 
																															AND languages_id = '" . (int) $languages_id . "'), 0, languages_id = 1)))";

            $pm_desc_title_result_sql = tep_db_query($pm_desc_title_select_sql);
            $pm_desc_title_row = tep_db_fetch_array($pm_desc_title_result_sql);

            $this->payment_methods_id = (int) $payment_methods_row['payment_methods_id'];
            $this->payment_methods_parent_id = (int) $payment_methods_row['payment_methods_parent_id'];
            $this->code = $payment_methods_row['payment_methods_code'];
            $this->title = $payment_methods_row['payment_methods_title']; //MODULE_PAYMENT_BIBIT_TEXT_TITLE;
            $this->display_title = $pm_desc_title_row['payment_methods_description_title']; //MODULE_PAYMENT_BIBIT_TEXT_DISPLAY_TITLE; 	// if title to be display for payment method is different from payment method value
            $this->sort_order = $payment_methods_row['payment_methods_sort_order']; //MODULE_PAYMENT_BIBIT_SORT_ORDER;
            $this->enabled = $payment_methods_row['payment_methods_receive_status_mode'];
            $this->legend_display_colour = $payment_methods_row['payment_methods_legend_color'];
            $this->description = $this->display_title;
            $this->offCurrencies = $this->get_support_currencies($this->payment_methods_id);
            $this->defCurr = DEFAULT_CURRENCY;

            $configuration_setting_array = $this->load_pm_setting();
            $this->processing_payment_notification_mail = $configuration_setting_array['MODULE_PAYMENT_OFFLINE_NOTIFICATION_EMAIL'];
            //$this->zone = $configuration_setting_array['MODULE_PAYMENT_OFFLINE_ZONE'];
            $this->message = $configuration_setting_array['MODULE_PAYMENT_OFFLINE_MESSAGE'];
            $this->order_status_id = $configuration_setting_array['MODULE_PAYMENT_OFFLINE_ORDER_STATUS_ID'];
            $this->processing_status_id = $configuration_setting_array['MODULE_PAYMENT_OFFLINE_PROCESSING_STATUS_ID'];
            $this->confirm_complete_days = $configuration_setting_array['MODULE_PAYMENT_OFFLINE_CONFIRM_COMPLETE'];
            $this->require_address_information = $configuration_setting_array['MODULE_PAYMENT_OFFLINE_MANDATORY_ADDRESS_FIELD'];
            $this->require_ic_information = $configuration_setting_array['MODULE_PAYMENT_OFFLINE_MANDATORY_IC_FIELD'];
            $this->require_contact_information = $configuration_setting_array['MODULE_PAYMENT_OFFLINE_MANDATORY_CONTACT_FIELD'];

            $this->customer_payment_info = $configuration_setting_array['MODULE_PAYMENT_OFFLINE_CUSTOMER_PAYMENT_INFO'];

            $offCurrencies = $currency;
            if (!in_array($offCurrencies, $this->offCurrencies)) {
                $offCurrencies = $this->offCurrencies[0];
            }
            $this->get_merchant_account($offCurrencies);

            $this->email_message = $this->offline_info;

            if ((int) $this->order_status_id > 0) {
                $this->order_status = $this->order_status_id;
            }

            $this->email_footer = str_replace(array("<br>", "<BR>", "\r\n"), array("\n", "\n", ''), $this->email_message);
            $this->order_processing_status = (int) $this->processing_status_id > 0 ? $this->processing_status_id : 7;
        }
    }

    function javascript_validation() {
        return false;
    }

    function selection() {
        global $languages_id, $default_languages_id;

        $selection_array = array();

        $payment_methods_select_sql = "	SELECT payment_methods_id, payment_methods_code, payment_methods_types_id, payment_methods_logo, payment_methods_receive_status_mode 
										FROM " . TABLE_PAYMENT_METHODS . " 
										WHERE payment_methods_receive_status = 1 
											AND payment_methods_receive_status_mode <> 0 
											AND payment_methods_parent_id = '" . (int) $this->payment_methods_id . "' 
										ORDER BY payment_methods_sort_order";
        $payment_methods_result_sql = tep_db_query($payment_methods_select_sql);
        while ($payment_methods_row = tep_db_fetch_array($payment_methods_result_sql)) {
            $pm_desc_title_select_sql = "	SELECT payment_methods_description_title 
											FROM " . TABLE_PAYMENT_METHODS_DESCRIPTION . " 
											WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "' 
												AND (IF (languages_id = '" . tep_db_input($languages_id) . "', 1, IF ((	SELECT COUNT(payment_methods_id) > 0 
																														FROM " . TABLE_PAYMENT_METHODS_DESCRIPTION . " 
																														WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "' 
																															AND languages_id = '" . (int) $languages_id . "'), 0, languages_id = 1)))";
            $pm_desc_title_result_sql = tep_db_query($pm_desc_title_select_sql);
            $pm_desc_title_row = tep_db_fetch_array($pm_desc_title_result_sql);

            $pm_array = $this->load_pm_setting(1, $payment_methods_row['payment_methods_id']);

            $selection_array[] = array('payment_gateway_code' => $this->code,
                'payment_methods_receive_status_mode' => $payment_methods_row['payment_methods_receive_status_mode'],
                'payment_methods_id' => $payment_methods_row['payment_methods_id'],
                'payment_methods_code' => $payment_methods_row['payment_methods_code'],
                'payment_methods_types_id' => $payment_methods_row['payment_methods_types_id'],
                'payment_methods_parent_id' => $this->payment_methods_id,
                'payment_methods_logo' => $payment_methods_row['payment_methods_logo'],
                'payment_methods_description_title' => $pm_desc_title_row['payment_methods_description_title'],
                'currency' => $this->get_support_currencies($payment_methods_row['payment_methods_id']),
                'show_billing_address' => $pm_array['MODULE_PAYMENT_OFFLINE_MANDATORY_ADDRESS_FIELD'],
                'confirm_complete_days' => $pm_array['MODULE_PAYMENT_OFFLINE_CONFIRM_COMPLETE'],
                'show_contact_number' => $pm_array['MODULE_PAYMENT_OFFLINE_MANDATORY_CONTACT_FIELD'],
                'show_ic' => $pm_array['MODULE_PAYMENT_OFFLINE_MANDATORY_IC_FIELD']
            );

            if ($payment_methods_row['payment_methods_receive_status_mode'] == '-1') {
                $pm_status_message_select = "	SELECT payment_methods_status_message 
												FROM " . TABLE_PAYMENT_METHODS_STATUS_DESCRIPTION . " 
												WHERE payment_methods_mode = 'RECEIVE' 
													AND payment_methods_status = '-1' 
													AND languages_id = '" . (int) $languages_id . "' 
													AND payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "'";
                $pm_status_message_result = tep_db_query($pm_status_message_select);
                $pm_status_message_row = tep_db_fetch_array($pm_status_message_result);
                $selection_array[sizeof($selection_array) - 1]['payment_methods_status_message'] = $pm_status_message_row['payment_methods_status_message'];
            }
        }

        return $selection_array;
    }

    function set_support_currencies($currency_array) {
        $this->offCurrencies = $currency_array;
    }

    function get_support_currencies($pm_id, $by_zone = true) {
        global $zone_info_array;

        $default_currency = '';
        $support_currencies_array = array();

        $currency_code_select_sql = "	SELECT currency_code, payment_methods_instance_default 
    									FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " 
    									WHERE payment_methods_id = '" . (int) $pm_id . "' 
    									ORDER BY currency_code";
        $currency_code_result_sql = tep_db_query($currency_code_select_sql);

        if (tep_db_num_rows($currency_code_result_sql) > 0) {
            while ($currency_code_row = tep_db_fetch_array($currency_code_result_sql)) {
                $support_currencies_array[] = $currency_code_row['currency_code'];

                if ($currency_code_row['payment_methods_instance_default'] == 1) {
                    $default_currency = $currency_code_row['currency_code'];
                }
            }
        } else {
            if ($this->payment_methods_parent_id > 0) {
                $payment_methods_parent_id = $this->payment_methods_parent_id;
            } else {
                $payment_methods_parent_id = $this->payment_methods_id;
            }

            $parent_currency_code_select_sql = "	SELECT currency_code, payment_methods_instance_default 
			    									FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " 
			    									WHERE payment_methods_id = '" . (int) $payment_methods_parent_id . "' 
			    									ORDER BY currency_code";
            $parent_currency_code_result_sql = tep_db_query($parent_currency_code_select_sql);
            while ($parent_currency_code_row = tep_db_fetch_array($parent_currency_code_result_sql)) {
                $support_currencies_array[] = $parent_currency_code_row['currency_code'];

                if ($parent_currency_code_row['payment_methods_instance_default'] == 1) {
                    $default_currency = $parent_currency_code_row['currency_code'];
                }
            }
        }

        if (tep_not_null($default_currency)) {
            $this->defCurr = $default_currency;
        }

        if ($by_zone) {
            if (isset($zone_info_array)) {
                $support_currencies_array = array_intersect($zone_info_array[3]->zone_currency_id, $support_currencies_array);
                if (count($support_currencies_array)) {
                    array_multisort($support_currencies_array);
                } else {
                    $support_currencies_array = array($default_currency);
                }
            } else {
                $support_currencies_array = array($default_currency);
            }
        } else {
            $this->set_support_currencies($support_currencies_array);
            // All found currencies is supported. Normally this is used for backend script such as IPN call
        }

        return $support_currencies_array;
    }

    function get_require_address_information() {
        return $this->require_address_information;
    }

    function get_confirm_complete_days() {
        return $this->confirm_complete_days;
    }

    function get_pm_info() {
        return $this->message;
    }

    function get_email_pm_info() {
        return $this->email_message;
    }

    function draw_confirm_button() {
        return tep_image_button2('red', 'javascript:document.checkout_confirmation.submit()', MODULE_PAYMENT_OFFLINE_TEXT_CART, 200);
    }

    function get_confirm_button_caption() {
        return MODULE_PAYMENT_OFFLINE_TITLE;
    }

    function pre_confirmation_check() {
        global $currency;

        if (!in_array($currency, $this->offCurrencies)) {
            
        }

        return false;
    }

    function confirmation() {
        return array('title' => $this->message);
    }

    function process_button() {
        return false;
    }

    function before_process() {
        return false;
    }

    function after_process() {
        return false;
    }

    function is_supported_currency($selected_currency) {
        return (in_array($selected_currency, $this->offCurrencies) ? true : false);
    }

    function get_error() {
        return false;
    }

    function check() {
        if (!isset($this->_check)) {
            $payment_methods_select_sql = "	SELECT payment_methods_id
											FROM " . TABLE_PAYMENT_METHODS . " as pm
											WHERE pm.payment_methods_id = '" . (int) $this->payment_methods_id . "'";
            $payment_methods_result_sql = tep_db_query($payment_methods_select_sql);
            $this->_check = tep_db_num_rows($payment_methods_result_sql);
        }
        return $this->_check;
    }

    function load_pm_setting($language_id = 1) {
        $pm_setting_array = array();
        //load value from DB
        $payment_configuration_info_select_sql = "	SELECT pci.payment_configuration_info_key, pcid.payment_configuration_info_value
													FROM " . TABLE_PAYMENT_CONFIGURATION_INFO . " as pci
													LEFT JOIN " . TABLE_PAYMENT_CONFIGURATION_INFO_DESCRIPTION . " as pcid 
														ON pci.payment_configuration_info_id = pcid.payment_configuration_info_id
															AND pcid.languages_id = '" . (int) $language_id . "' 
													WHERE pci.payment_methods_id = '" . (int) $this->payment_methods_id . "' 
													ORDER BY pci.payment_configuration_info_sort_order";
        $payment_configuration_info_result_sql = tep_db_query($payment_configuration_info_select_sql);
        if (tep_db_num_rows($payment_configuration_info_result_sql)) {
            while ($payment_configuration_info_row = tep_db_fetch_array($payment_configuration_info_result_sql)) {
                $pm_setting_array[$payment_configuration_info_row['payment_configuration_info_key']] = $payment_configuration_info_row['payment_configuration_info_value'];
            }
        } else if ((int) $this->payment_methods_parent_id > 0) { //load value from PG
            $payment_configuration_info_select_sql = "	SELECT pci.payment_configuration_info_key, pcid.payment_configuration_info_value
														FROM " . TABLE_PAYMENT_CONFIGURATION_INFO . " as pci
														LEFT JOIN " . TABLE_PAYMENT_CONFIGURATION_INFO_DESCRIPTION . " as pcid 
															ON pci.payment_configuration_info_id = pcid.payment_configuration_info_id
																AND pcid.languages_id = '" . (int) $language_id . "'
														WHERE pci.payment_methods_id = '" . (int) $this->payment_methods_parent_id . "'
														ORDER BY pci.payment_configuration_info_sort_order";
            $payment_configuration_info_result_sql = tep_db_query($payment_configuration_info_select_sql);
            while ($payment_configuration_info_row = tep_db_fetch_array($payment_configuration_info_result_sql)) {
                $pm_setting_array[$payment_configuration_info_row['payment_configuration_info_key']] = $payment_configuration_info_row['payment_configuration_info_value'];
            }
        }
        return $pm_setting_array;
    }

    function get_merchant_account($selected_currency) {
        $payment_methods_instance_id = 0;
        $payment_gateway_instance_select_sql = "SELECT pmi.payment_methods_instance_id, pmi.payment_methods_instance_follow_default, pmi.currency_code 
				 								FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " as pmi
				 								WHERE pmi.payment_methods_id = '" . (int) $this->payment_methods_id . "'
				 								LIMIT 1";
        $payment_gateway_instance_result_sql = tep_db_query($payment_gateway_instance_select_sql);
        if (tep_db_num_rows($payment_gateway_instance_result_sql)) {
            $payment_gateway_instance_select_sql = "SELECT pmi.payment_methods_instance_id, pmi.payment_methods_instance_follow_default 
					 								FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " as pmi
					 								WHERE pmi.currency_code = '" . tep_db_input($selected_currency) . "' 
					 									AND pmi.payment_methods_id = '" . (int) $this->payment_methods_id . "'";
            $payment_gateway_instance_result_sql = tep_db_query($payment_gateway_instance_select_sql);
            if ($payment_gateway_instance_row = tep_db_fetch_array($payment_gateway_instance_result_sql)) {
                if ((int) $payment_gateway_instance_row['payment_methods_instance_follow_default'] > 0) { // Follow Default Currency Setting
                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_follow_default'];
                } else { // Has own setting
                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_id'];
                }
            }
        } else {
            $payment_gateway_instance_select_sql = "SELECT pmi.payment_methods_instance_id, pmi.payment_methods_instance_follow_default 
					 								FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " as pmi
					 								INNER JOIN " . TABLE_PAYMENT_METHODS . " as pm
					 									ON pm.payment_methods_parent_id = pmi.payment_methods_id
					 								WHERE pmi.currency_code = '" . tep_db_input($selected_currency) . "' 
					 									AND pm.payment_methods_id = '" . (int) $this->payment_methods_id . "'";
            $payment_gateway_instance_result_sql = tep_db_query($payment_gateway_instance_select_sql);
            if ($payment_gateway_instance_row = tep_db_fetch_array($payment_gateway_instance_result_sql)) {
                if ((int) $payment_gateway_instance_row['payment_methods_instance_follow_default'] > 0) {
                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_follow_default'];
                } else {
                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_id'];
                }
            }
        }
        $payment_gateway_instance_setting_select_sql = "SELECT pmis.payment_methods_instance_setting_key, pmis.payment_methods_instance_setting_value 
						 								FROM " . TABLE_PAYMENT_METHODS_INSTANCE_SETTING . " as pmis
						 								WHERE pmis.payment_methods_instance_id = '" . (int) $payment_methods_instance_id . "'";
        $payment_gateway_instance_setting_result_sql = tep_db_query($payment_gateway_instance_setting_select_sql);
        while ($payment_gateway_instance_setting_row = tep_db_fetch_array($payment_gateway_instance_setting_result_sql)) {
            switch ($payment_gateway_instance_setting_row['payment_methods_instance_setting_key']) {
                case 'MODULE_PAYMENT_OFFLINE_INFO':
                    $this->offline_info = $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];
                    break;
            }
        }
    }

    function install() {
        $install_configuration_flag = true;
        $check_installed_select_sql = "	SELECT payment_methods_id, payment_methods_receive_status 
    									FROM " . TABLE_PAYMENT_METHODS . "
    									WHERE payment_methods_filename = '" . $this->filename . "'";
        $check_installed_result_sql = tep_db_query($check_installed_select_sql);
        if ($check_installed_row = tep_db_fetch_array($check_installed_result_sql)) {
            $this->payment_methods_id = $check_installed_row['payment_methods_id'];
            if ($check_installed_row['payment_methods_receive_status'] != 1) {
                $update_receive_status_sql_data = array("payment_methods_receive_status" => 1);
                tep_db_perform(TABLE_PAYMENT_METHODS, $update_receive_status_sql_data, 'update', "payment_methods_id = '" . (int) $this->payment_methods_id . "'");
            } else {
                $install_configuration_flag = false;
            }
        } else {
            $install_payment_gateway_data_sql = array('payment_methods_code' => $this->code,
                'payment_methods_send_status' => 0,
                'payment_methods_title' => $this->code,
                'payment_methods_types_id' => 0,
                'payment_methods_parent_id' => 0,
                'payment_methods_legend_color' => '',
                'payment_methods_sort_order' => 20,
                'payment_methods_filename' => $this->filename,
                'payment_methods_logo' => '',
                'date_added' => 'now()',
                'last_modified' => 'now()',
                'payment_methods_receive_status' => 1,
                'payment_methods_receive_status_mode' => 0
            );
            tep_db_perform(TABLE_PAYMENT_METHODS, $install_payment_gateway_data_sql);
            $this->payment_methods_id = mysql_insert_id();
        }
        if ($install_configuration_flag) {
            $install_key_array = array();
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Payment Info Notification Email Address',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_OFFLINE_NOTIFICATION_EMAIL',
                    'payment_configuration_info_description' => 'Email address to which the notification email will be send to when payment information is entered.<br>(In "Name &lt;Email&gt;" format. Use \',\' as delimiter for multiple recipient)',
                    'payment_configuration_info_sort_order' => '915',
                    'set_function' => '',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => '',
                    'languages_id' => 1
                )
            );
            /* 			$install_key_array[] = array(	'payment_methods_id'=>(int)$this->payment_methods_id,
              'payment_configuration_info_title'=>'Payment Zone',
              'payment_configuration_info_key'=>'MODULE_PAYMENT_OFFLINE_ZONE',
              'payment_configuration_info_value'=>'0',
              'payment_configuration_info_description'=>'If a zone is selected, only enable this payment method for that zone.',
              'payment_configuration_info_sort_order'=>'920',
              'set_function'=>'tep_cfg_pull_down_zone_classes(',
              'use_function'=>'tep_get_zone_class_title',
              'date_added'=>'now()'); */
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Set Order Status',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_OFFLINE_ORDER_STATUS_ID',
                    'payment_configuration_info_description' => 'Set the status of orders made with this payment module to this value',
                    'payment_configuration_info_sort_order' => '930',
                    'set_function' => 'tep_cfg_pull_down_order_statuses(',
                    'use_function' => 'tep_get_order_status_name',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => '0',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Set Order\'s "Processing" Status',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_OFFLINE_PROCESSING_STATUS_ID',
                    'payment_configuration_info_description' => 'Set the initial processing status of orders made with this payment module to this value',
                    'payment_configuration_info_sort_order' => '935',
                    'set_function' => 'tep_cfg_pull_down_order_statuses(',
                    'use_function' => 'tep_get_order_status_name',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => '7',
                    'languages_id' => 1
                )
            );
            /* $install_key_array[] = array(	'payment_methods_id'=>(int)$this->payment_methods_id,
              'payment_configuration_info_title'=>'Payment Message',
              'payment_configuration_info_key'=>'MODULE_PAYMENT_OFFLINE_MESSAGE',
              'payment_configuration_info_value'=>'Thank you for shopping at OffGamers.com.',
              'payment_configuration_info_description'=>'Payment message will show up during checkout process (Direct Deposit or Electronic Funds Transfer)',
              'payment_configuration_info_sort_order'=>'945',
              'set_function'=>'tep_cfg_textarea(',
              'use_function'=>'',
              'date_added'=>'now()');
              $install_key_array[] = array(	'payment_methods_id'=>(int)$this->payment_methods_id,
              'payment_configuration_info_title'=>'Payment Email Message',
              'payment_configuration_info_key'=>'MODULE_PAYMENT_OFFLINE_EMAIL_MESSAGE',
              'payment_configuration_info_value'=>'Thank you for shopping at OffGamers.com.',
              'payment_configuration_info_description'=>'This message will show up in order confirmation e-mail.',
              'payment_configuration_info_sort_order'=>'950',
              'set_function'=>'tep_cfg_textarea(',
              'use_function'=>'',
              'date_added'=>'now()'); */
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Confirm Complete (in days)',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_OFFLINE_CONFIRM_COMPLETE',
                    'payment_configuration_info_description' => 'The number of days in which an order made by this payment method is confirm completed.',
                    'payment_configuration_info_sort_order' => '960',
                    'set_function' => '',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => '0',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Require Address Information',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_OFFLINE_MANDATORY_ADDRESS_FIELD',
                    'payment_configuration_info_description' => 'Set address field as required info during checkout.',
                    'payment_configuration_info_sort_order' => '970',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Customer Payment Info',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_OFFLINE_CUSTOMER_PAYMENT_INFO',
                    'payment_configuration_info_description' => 'Set to true if this Payment Method required customer input for Payment Information required customer payment info.',
                    'payment_configuration_info_sort_order' => '980',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Require IC Information',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_OFFLINE_MANDATORY_IC_FIELD',
                    'payment_configuration_info_description' => 'Set IC field as required info during checkout.',
                    'payment_configuration_info_sort_order' => '990',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Require Contact Information',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_OFFLINE_MANDATORY_CONTACT_FIELD',
                    'payment_configuration_info_description' => 'Set Contact field as required info during checkout.',
                    'payment_configuration_info_sort_order' => '1000',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );
            foreach ($install_key_array as $data) {
                tep_db_perform(TABLE_PAYMENT_CONFIGURATION_INFO, $data['info']);
                $payment_conf_id = tep_db_insert_id();

                $data['desc']['payment_configuration_info_id'] = (int) $payment_conf_id;
                tep_db_perform(TABLE_PAYMENT_CONFIGURATION_INFO_DESCRIPTION, $data['desc']);
            }
            return 1;
        }
    }

    function remove() {
        tep_db_query("delete from " . TABLE_CONFIGURATION . " where configuration_key in ('" . implode("', '", $this->keys()) . "')");
    }

    function keys() {
        return array('MODULE_PAYMENT_OFFLINE_STATUS',
            'MODULE_PAYMENT_OFFLINE_NOTIFICATION_EMAIL',
            'MODULE_PAYMENT_OFFLINE_ZONE',
            'MODULE_PAYMENT_OFFLINE_SORT_ORDER',
            'MODULE_PAYMENT_OFFLINE_ORDER_STATUS_ID',
            'MODULE_PAYMENT_OFFLINE_PROCESSING_STATUS_ID',
            'MODULE_PAYMENT_OFFLINE_CONFIRM_COMPLETE',
            'MODULE_PAYMENT_OFFLINE_MESSAGE',
            'MODULE_PAYMENT_OFFLINE_EMAIL_MESSAGE',
            'MODULE_PAYMENT_OFFLINE_LEGEND_COLOUR');
    }

    function configuration_information_keys() {
        return array('MODULE_PAYMENT_OFFLINE_NOTIFICATION_EMAIL',
            //'MODULE_PAYMENT_OFFLINE_ZONE', 
            'MODULE_PAYMENT_OFFLINE_ORDER_STATUS_ID',
            'MODULE_PAYMENT_OFFLINE_PROCESSING_STATUS_ID',
            'MODULE_PAYMENT_OFFLINE_CONFIRM_COMPLETE',
            'MODULE_PAYMENT_OFFLINE_MANDATORY_ADDRESS_FIELD',
            'MODULE_PAYMENT_OFFLINE_CUSTOMER_PAYMENT_INFO',
            'MODULE_PAYMENT_OFFLINE_MANDATORY_IC_FIELD',
            'MODULE_PAYMENT_OFFLINE_MANDATORY_CONTACT_FIELD'/* , 
                  'MODULE_PAYMENT_OFFLINE_MESSAGE',
                  'MODULE_PAYMENT_OFFLINE_EMAIL_MESSAGE' */);
    }

    function multi_lang_configuration_info_keys() {
        return array(/* 'MODULE_PAYMENT_OFFLINE_MESSAGE',
                  'MODULE_PAYMENT_OFFLINE_EMAIL_MESSAGE' */);
    }

    function merchant_information_keys() {
        include_once(DIR_WS_CLASSES . "payment_methods.php");

        return array(
            'MODULE_PAYMENT_OFFLINE_INFO' => array("field" => 'textarea', "rows" => '7', "cols" => '50', "mapping" => "MODULE_PAYMENT_OFFLINE_LNG_INFO"),
            'MODULE_PAYMENT_TAX' => array("field" => 'select', 'mapping' => "MODULE_PAYMENT_TAX", "select" => payment_methods::get_tax_option())
        );
    }

    function get_pg_id() {
        return $this->payment_methods_id;
    }

    function get_orders_payment_info_file() {
        return DIR_FS_CATALOG_MODULES . 'payment/offline/admin/orders.inc.php';
    }

    function get_orders_list_payment_info_file() {
        return DIR_FS_CATALOG_MODULES . 'payment/offline/admin/orders_list.inc.php';
    }

    function get_ipn_class_file() {
        return '';
    }

}

?>