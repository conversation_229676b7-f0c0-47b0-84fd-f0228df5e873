<?
/*
  	$Id: orders.inc.php,v 1.4 2009/07/07 11:08:33 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2006 SKC Venture
  	
  	Released under the GNU General Public License
*/

include_once(DIR_FS_CATALOG_MODULES . 'payment/offline/admin/languages/'.$language.'/offline.lng.php');

$trans_info_select_sql = "	SELECT authorisation_result 
                                FROM " . TABLE_PAYMENT_EXTRA_INFO . " 
                                WHERE orders_id='" . tep_db_input($oID) . "'";
$trans_info_result_sql= tep_db_query($trans_info_select_sql);
$trans_info_row = tep_db_fetch_array($trans_info_result_sql);

$photo_array = array();

$trans_info_select_sql = "	SELECT orders_extra_info_key, orders_extra_info_value
                                FROM " . TABLE_ORDERS_EXTRA_INFO . " 
                                WHERE orders_id = '" . tep_db_input($oID) . "'
                                    AND orders_extra_info_key IN ('payment_images_1', 'payment_images_2', 'payment_images_3')";
$trans_info_result_sql= tep_db_query($trans_info_select_sql);
while ($trans_info_row2 = tep_db_fetch_array($trans_info_result_sql)) {
    $fpath = urlencode(str_replace('{order_id}', $oID, $trans_info_row2['orders_extra_info_value']));
    $photo_array['#'.$trans_info_row2['orders_extra_info_key']] = '<a id="' . $trans_info_row2['orders_extra_info_key'] . '" href="' . tep_href_link(FILENAME_ORDERS, 'subaction=preview_image&fpath=' . $fpath . '&site_id=' . $order_site_id) . '" target="_blank" title="' . $trans_info_row2['orders_extra_info_key'] . '">' . str_replace('_', ' ', $trans_info_row2['orders_extra_info_key']) . '</a>';
}
?>
<table border="0" width="100%" cellspacing="0" cellpadding="2">
	<tr>
		<td class="main">
      		<table border="0" width="100%" cellspacing="0" cellpadding="2">
      			<tr>
      				<td class="main" width="12%" nowrap><b><?=ENTRY_PAYMENT_METHOD?></b></td>
      				<td class="main">
      					<div>
      						<? if((int)$order->info['payment_methods_id']>0) { ?><div class="<?=((int)$payment_info_array[$order->info['payment_methods_id']]->confirm_complete_days>0?'rp_icon':'nrp_icon')?>"><b><?=((int)$payment_info_array[$order->info['payment_methods_id']]->confirm_complete_days>0?'RP':'NRP')?></b></div> <? } ?>
      						<div style="width:30px; height:15px; background-color:<?=$payment_module_info['display_colour']?>; float:left; vertical-align:top; text-align:center;"></div>
      						<div style="vertical-align: top">
      							&nbsp;&nbsp;
<?
	if ($view_payment_details_permission) {
		if (tep_not_null($last_payment_method) && $last_payment_method['payment_methods_id'] != $order->info['payment_methods_id']) {
			echo '<span class="redIndicator">'.(tep_not_null($payment_module_info['payment_methods_parent_title'])?$payment_module_info['payment_methods_parent_title'] . ' - ' :'') . $payment_module_info['payment_methods_title'].'</span>';
		} else {
			echo (tep_not_null($payment_module_info['payment_methods_parent_title'])?$payment_module_info['payment_methods_parent_title'] . ' - ' :'') . $payment_module_info['payment_methods_title'];
		}
		
		if (tep_not_null($last_payment_method) && $last_payment_method['payment_methods_id'] != $order->info['payment_methods_id']) {
			echo '&nbsp;&nbsp;<span class="redIndicator">(Last Payment Method: ' . (tep_not_null($last_payment_method['parent_payment_methods_title'])? $last_payment_method['parent_payment_methods_title'] . ' - ' : '' ) . $last_payment_method['payment_methods_title'] . ')</span>';
		}
	} else {
		echo (tep_not_null($payment_module_info['payment_methods_parent_title'])?$payment_module_info['payment_methods_parent_title'] . ' - ' :'') . $payment_module_info['payment_methods_title'];
	}
?>
							</div>
      					</div>
      				</td>
				</tr>
<?	if ($view_payment_details_permission) {
		echo '	<tr>';
		echo tep_draw_form('payment_info_form', FILENAME_ORDERS, tep_get_all_get_params(), 'post');
		echo tep_draw_hidden_field('subaction', 'update_payment_info');
?>
					<td class="main" valign="top" nowrap><b><?=ENTRY_OFFLINE_PAYMENT_INFO?></b>&nbsp;</td>
                	<td class="main" valign="top">
<? 		if ($order->info['orders_status'] == '1' || $order->info['orders_status'] == '7') { // Only editable when it is Pending or Verifying ?>
                		<table border="0" cellspacing="0" cellpadding="2">
                			<tr>
                				<td><?=tep_draw_textarea_field('payment_info', 'soft', '60', '5', $trans_info_row["authorisation_result"])?></td>
                				<td class="main" valign="bottom">
                					<?=tep_submit_button(IMAGE_UPDATE, IMAGE_UPDATE, 'name="updateBtn"', 'inputButton')?>
                				</td>
                			</tr>
                		</table>
<?		} else {
			echo nl2br($trans_info_row["authorisation_result"]);
		}
?>
					</td>
					</form>
				</tr>
                                <tr>
                                    <td class="main" valign="top" nowrap><b><?='Payment Made Images:'?></b>&nbsp;</td>
                                    <td class="main" valign="top">
<?php 
                                        if (tep_not_null($photo_array)) {
                                            echo implode('<br>', $photo_array);
?>
                                        <script>
                                        jQuery("<?= implode(',', array_keys($photo_array)) ?>").fancybox({
                                            'transitionIn': 'none',
                                            'transitionOut': 'none',
                                            'type': 'image',
                                            'centerOnScroll': true
                                        });
                                        </script>
<?
                                        }
?>
                                    </td>
                                </tr>
<?	} ?>
			</table>
		</td>
	</tr>
</table>