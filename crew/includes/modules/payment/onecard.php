<?php

/*
  $Id: onecard.php,v 1.10 2015/02/16 07:43:47 chingyen Exp $

  Developer: Ho <PERSON>
  Copyright (c) 2009 Dynamic Podium Sdn Bhd

  Released under the GNU General Public License
 */

class onecard {

    var $code, $title, $description, $sort_order, $enabled = false;
    var $email_footer, $onecard_currencies, $defCurr;
    var $amount_decimal, $check_trans_status_flag, $connect_via_proxy;
    var $product_code, $modulus;

    // class constructor
    function onecard($pm_id = '') {
        global $order, $languages_id, $default_languages_id;

        //Basic info
        $this->payment_methods_id = 0;
        $this->payment_methods_parent_id = 0;

        $this->code = 'onecard';
        $this->filename = 'onecard.php';

        $this->auto_cancel_period = 14400; // In minutes
        $this->amount_decimal = 2;

        $this->force_to_checkoutprocess = true;
        $this->has_ipn = false;
        $this->check_trans_status_flag = false;
        $this->form_action_url = 'https://www.onecard.net/customer/directVoucherPurchase.html';

        $this->connect_via_proxy = false;

        $payment_methods_select_sql = "	SELECT payment_methods_parent_id, payment_methods_code, 
												payment_methods_types_id, 
												payment_methods_receive_status_mode, payment_methods_id, 
												payment_methods_title, payment_methods_sort_order, 
												payment_methods_receive_status, payment_methods_legend_color, 
												payment_methods_logo 
										FROM " . TABLE_PAYMENT_METHODS . " 
										WHERE 1 ";
        if ((int) $pm_id > 0) {
            $payment_methods_select_sql .= "	AND payment_methods_id = '" . (int) $pm_id . "'";
        } else {
            $payment_methods_select_sql .= "	AND payment_methods_filename = '" . tep_db_input($this->filename) . "'";
        }

        $payment_methods_result_sql = tep_db_query($payment_methods_select_sql);
        if ($payment_methods_row = tep_db_fetch_array($payment_methods_result_sql)) {
            $pm_desc_title_select_sql = "	SELECT payment_methods_description_title 
											FROM " . TABLE_PAYMENT_METHODS_DESCRIPTION . " 
											WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "' 
												AND (IF (languages_id = '" . tep_db_input($languages_id) . "', 1, IF ((	SELECT COUNT(payment_methods_id) > 0 
																														FROM " . TABLE_PAYMENT_METHODS_DESCRIPTION . " 
																														WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "' 
																															AND languages_id = '" . (int) $languages_id . "'), 0, languages_id = '" . (int) $default_languages_id . "')))";
            $pm_desc_title_result_sql = tep_db_query($pm_desc_title_select_sql);
            $pm_desc_title_row = tep_db_fetch_array($pm_desc_title_result_sql);

            $this->payment_methods_id = (int) $payment_methods_row['payment_methods_id'];
            $this->payment_methods_parent_id = (int) $payment_methods_row['payment_methods_parent_id'];
            $this->code = $payment_methods_row['payment_methods_code'];
            $this->title = $payment_methods_row['payment_methods_title'];
            $this->display_title = $pm_desc_title_row['payment_methods_description_title'];
            $this->sort_order = $payment_methods_row['payment_methods_sort_order'];
            $this->enabled = $payment_methods_row['payment_methods_receive_status_mode'];
            $this->legend_display_colour = $payment_methods_row['payment_methods_legend_color'];
            $this->receive_status = $payment_methods_row['payment_methods_receive_status'];
            $this->preferred = true;
            $this->description = $this->display_title;
            $this->onecard_currencies = $this->get_support_currencies($this->payment_methods_id);

            $configuration_setting_array = $this->load_pm_setting();
            $this->testing = ($configuration_setting_array['MODULE_PAYMENT_ONECARD_TEST_MODE'] == 'True' ? true : false);
            $this->order_status_id = $configuration_setting_array['MODULE_PAYMENT_ONECARD_ORDER_STATUS_ID'];
            $this->processing_status_id = $configuration_setting_array['MODULE_PAYMENT_ONECARD_PROCESSING_STATUS_ID'];
            $this->message = $configuration_setting_array['MODULE_PAYMENT_ONECARD_MESSAGE'];
            $this->email_message = $configuration_setting_array['MODULE_PAYMENT_ONECARD_EMAIL_MESSAGE'];
            $this->confirm_complete_days = $configuration_setting_array['MODULE_PAYMENT_ONECARD_CONFIRM_COMPLETE'];

            $this->customer_payment_info = $configuration_setting_array['MODULE_PAYMENT_ONECARD_CUSTOMER_PAYMENT_INFO'];
            $this->require_address_information = $configuration_setting_array['MODULE_PAYMENT_ONECARD_MANDATORY_ADDRESS_FIELD'];
            $this->require_ic_information = $configuration_setting_array['MODULE_PAYMENT_ONECARD_MANDATORY_IC_FIELD'];
            $this->require_contact_information = $configuration_setting_array['MODULE_PAYMENT_ONECARD_MANDATORY_CONTACT_FIELD'];

            if ((int) $this->order_status_id > 0) {
                $this->order_status = $this->order_status_id;
            } else {
                $this->order_status = DEFAULT_ORDERS_STATUS_ID;
            }

            $this->order_processing_status = $this->processing_status_id;
            $this->email_footer = str_replace(array("<br>", "<BR>", "\r\n"), array("\n", "\n", ''), $this->email_message);
        }
    }

    function javascript_validation() {
        return false;
    }

    function selection() {
        global $languages_id, $default_languages_id;

        $selection_array = array();

        $payment_methods_select_sql = "	SELECT payment_methods_id, payment_methods_code, payment_methods_types_id, payment_methods_logo, payment_methods_receive_status_mode 
										FROM " . TABLE_PAYMENT_METHODS . " 
										WHERE payment_methods_receive_status = 1 
											AND payment_methods_receive_status_mode <> 0 
											AND payment_methods_parent_id = '" . (int) $this->payment_methods_id . "' 
										ORDER BY payment_methods_sort_order";
        $payment_methods_result_sql = tep_db_query($payment_methods_select_sql);
        while ($payment_methods_row = tep_db_fetch_array($payment_methods_result_sql)) {
            $pm_desc_title_select_sql = "	SELECT payment_methods_description_title 
											FROM " . TABLE_PAYMENT_METHODS_DESCRIPTION . " 
											WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "' 
												AND (IF (languages_id = '" . tep_db_input($languages_id) . "', 1, IF ((	SELECT COUNT(payment_methods_id) > 0 
																														FROM " . TABLE_PAYMENT_METHODS_DESCRIPTION . " 
																														WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "' 
																															AND languages_id = '" . (int) $languages_id . "'), 0, languages_id = '" . (int) $default_languages_id . "')))";
            $pm_desc_title_result_sql = tep_db_query($pm_desc_title_select_sql);
            $pm_desc_title_row = tep_db_fetch_array($pm_desc_title_result_sql);

            $pm_array = $this->load_pm_setting(1, $payment_methods_row['payment_methods_id']);

            $selection_array[] = array('payment_gateway_code' => $this->code,
                'payment_methods_receive_status_mode' => $payment_methods_row['payment_methods_receive_status_mode'],
                'payment_methods_id' => $payment_methods_row['payment_methods_id'],
                'payment_methods_code' => $payment_methods_row['payment_methods_code'],
                'payment_methods_types_id' => $payment_methods_row['payment_methods_types_id'],
                'payment_methods_parent_id' => $this->payment_methods_id,
                'payment_methods_logo' => $payment_methods_row['payment_methods_logo'],
                'payment_methods_description_title' => $pm_desc_title_row['payment_methods_description_title'],
                'currency' => $this->get_support_currencies($payment_methods_row['payment_methods_id']),
                'show_billing_address' => $pm_array['MODULE_PAYMENT_ONECARD_MANDATORY_ADDRESS_FIELD'],
                'confirm_complete_days' => $pm_array['MODULE_PAYMENT_ONECARD_CONFIRM_COMPLETE'],
                'show_contact_number' => $pm_array['MODULE_PAYMENT_ONECARD_MANDATORY_CONTACT_FIELD'],
                'show_ic' => $pm_array['MODULE_PAYMENT_ONECARD_MANDATORY_IC_FIELD']
            );

            if ($payment_methods_row['payment_methods_receive_status_mode'] == '-1') {
                $pm_status_message_select = "	SELECT payment_methods_status_message 
												FROM " . TABLE_PAYMENT_METHODS_STATUS_DESCRIPTION . " 
												WHERE payment_methods_mode = 'RECEIVE' 
													AND payment_methods_status = '-1' 
													AND languages_id = '" . (int) $languages_id . "' 
													AND payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "'";
                $pm_status_message_result = tep_db_query($pm_status_message_select);
                $pm_status_message_row = tep_db_fetch_array($pm_status_message_result);
                $selection_array[sizeof($selection_array) - 1]['payment_methods_status_message'] = $pm_status_message_row['payment_methods_status_message'];
            }
        }

        return $selection_array;
    }

    function set_support_currencies($currency_array) {
        $this->onecard_currencies = $currency_array;
    }

    function get_support_currencies($pm_id, $by_zone = true) {
        global $zone_info_array;

        $default_currency = '';
        $support_currencies_array = array();

        $currency_code_select_sql = "	SELECT currency_code, payment_methods_instance_default 
    									FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " 
    									WHERE payment_methods_id = '" . (int) $pm_id . "' 
    									ORDER BY currency_code";
        $currency_code_result_sql = tep_db_query($currency_code_select_sql);
        if (tep_db_num_rows($currency_code_result_sql) > 0) {
            while ($currency_code_row = tep_db_fetch_array($currency_code_result_sql)) {
                $support_currencies_array[] = $currency_code_row['currency_code'];

                if ($currency_code_row['payment_methods_instance_default'] == 1) {
                    $default_currency = $currency_code_row['currency_code'];
                }
            }
        } else {
            if ($this->payment_methods_parent_id > 0) {
                $payment_methods_parent_id = $this->payment_methods_parent_id;
            } else {
                $payment_methods_parent_id = $this->payment_methods_id;
            }

            $parent_currency_code_select_sql = "	SELECT currency_code, payment_methods_instance_default 
			    									FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " 
			    									WHERE payment_methods_id = '" . (int) $payment_methods_parent_id . "' 
			    									ORDER BY currency_code";
            $parent_currency_code_result_sql = tep_db_query($parent_currency_code_select_sql);
            while ($parent_currency_code_row = tep_db_fetch_array($parent_currency_code_result_sql)) {
                $support_currencies_array[] = $parent_currency_code_row['currency_code'];

                if ($parent_currency_code_row['payment_methods_instance_default'] == 1) {
                    $default_currency = $parent_currency_code_row['currency_code'];
                }
            }
        }

        if (tep_not_null($default_currency)) {
            $this->defCurr = $default_currency;
        }

        if ($by_zone) {
            if (isset($zone_info_array)) {
                $support_currencies_array = array_intersect($zone_info_array[3]->zone_currency_id, $support_currencies_array);
                if (count($support_currencies_array)) {
                    array_multisort($support_currencies_array);
                } else {
                    $support_currencies_array = array($default_currency);
                }
            } else {
                $support_currencies_array = array($default_currency);
            }
        } else {
            $this->set_support_currencies($support_currencies_array);
            // All found currencies is supported. Normally this is used for backend script such as IPN call
        }

        return $support_currencies_array;
    }

    function get_confirm_complete_days() {
        return $this->confirm_complete_days;
    }

    function get_pm_info() {
        return $this->message;
    }

    function get_email_pm_info() {
        return $this->email_message;
    }

    function draw_confirm_button() {
        return tep_image_button2('red', 'javascript:document.checkout_confirmation.submit()', MODULE_PAYMENT_ONECARD_TEXT_CART, 200);
    }

    function get_confirm_button_caption() {
        return MODULE_PAYMENT_ONECARD_TEXT_TITLE;
    }

    function pre_confirmation_check() {
        /*
          global $currency;

          if (in_array($currency, $this->onecard_currencies)) {

          }
         */
        return false;
    }

    function process_button() {
        global $_POST, $shipping_cost, $total_cost, $shipping_selected, $shipping_method, $currencies, $currency, $customer_id, $order, $languages_id;
        global $order_logged, $OFFGAMERS;

        if ($this->force_to_checkoutprocess && !tep_session_is_registered('order_logged')) {
            return;
        }

        $onecard_currencies = $currency;
        if (!in_array($onecard_currencies, $this->onecard_currencies)) {
            $onecard_currencies = $this->onecard_currencies[0];
        }
        $this->get_merchant_account($onecard_currencies);

        $payment_gateway_amount = number_format($order->info['total'] * $currencies->get_value($onecard_currencies), $this->amount_decimal, '.', '');

        $digest_str = md5($this->modulus . $this->product_code . $payment_gateway_amount);

        $process_button_string =
                tep_draw_hidden_field('productCode', $this->product_code) .
                tep_draw_hidden_field('trxRefNumber', $order_logged) .
                tep_draw_hidden_field('amount', $payment_gateway_amount) .
                tep_draw_hidden_field('description', 'Purchase from ' . STORE_NAME) .
                tep_draw_hidden_field('hashCode', $digest_str) .
                tep_draw_hidden_field('returnUrl', tep_href_link(FILENAME_ONECARD_CALLBACK));
        return $process_button_string;
    }

    function before_process() {
        global $order_logged, $_POST, $currencies, $currency, $order, $customer_id;

        $error_flag = true;

        if (!tep_session_is_registered('order_logged')) {
            return;
        } else {
            $returned_array = array();

            $error_flag = true;
            if (isset($_REQUEST['voucherCode']) && tep_not_null($_REQUEST['voucherCode'])) {

                require_once(DIR_FS_CATALOG . DIR_WS_INCLUDES . 'modules/payment/onecard/rsa.php');

                $order_info_select_sql = "	SELECT currency, payment_methods_id 
											FROM " . TABLE_ORDERS . "
											WHERE orders_id = '" . $order_logged . "'";
                $order_info_select_sql = tep_db_query($order_info_select_sql);
                $order_info_row = tep_db_fetch_array($order_info_select_sql);

                $this->payment_methods_id = $order_info_row['payment_methods_id']; // please ask weichen is there any issue if overwrite like this
                $this->get_merchant_account($order_info_row['currency']);

                $onecard_rsa_obj = new onecard_rsa();
                $returned_str = $onecard_rsa_obj->rsa_decrypt(base64_decode(str_replace(" ", "+", $_REQUEST['voucherCode'])), $this->exponent, $this->modulus, 2048);
                $voucher_code = $this->product_code . substr($returned_str, strlen(str_replace("-", "", $this->product_code)));

                $onecard_data_sql = array('onecard_voucher_code' => tep_db_prepare_input($voucher_code));
                $onecard_select_sql = "	SELECT onecard_orders_id
										FROM " . TABLE_ONECARD . "
										WHERE onecard_orders_id = '" . $order_logged . "' ";
                $onecard_result_sql = tep_db_query($onecard_select_sql);
                if (tep_db_num_rows($onecard_result_sql)) {
                    tep_db_perform(TABLE_ONECARD, $onecard_data_sql, 'update', " onecard_orders_id = '" . (int) $order_logged . "' ");
                } else {
                    $onecard_data_sql['onecard_orders_id'] = (int) $order_logged;
                    tep_db_perform(TABLE_ONECARD, $onecard_data_sql);
                }

                if ($this->check_trans_status((int) $order_logged)) {
                    $error_flag = false;
                }
            } else {
                //tep_redirect(tep_href_link(FILENAME_CUSTOMER_SUPPORT, 'SSL', true, false));
            }
        }

        if ($error_flag) {
            tep_redirect(tep_href_link(FILENAME_CHECKOUT_PAYMENT, 'error_message=' . urlencode(MODULE_PAYMENT_ONECARD_TEXT_ERROR_MESSAGE), 'SSL', true, false));
        }
    }

    function after_process() {
        return false;
    }

    function link_to_payment($new_order_id) {
        global $order_logged;

        if (tep_session_is_registered('order_logged'))
            tep_session_unregister('order_logged');

        tep_session_register('order_logged');
        $order_logged = $new_order_id;
        require_once(DIR_FS_CATALOG . DIR_WS_INCLUDES . 'modules/payment/onecard/catalog/onecard_splash.php');
        return;
    }

    function get_error() {
        $error = array('title' => MODULE_PAYMENT_ONECARD_TEXT_ERROR,
            'error' => stripslashes(urldecode($_GET['error'])));
        return $error;
    }

    function check() {
        if (!isset($this->_check)) {
            $this->_check = defined('MODULE_PAYMENT_ONECARD_STATUS');
        }
        return $this->_check;
    }

    function is_processed_order($order_id) {
        $payment_status_select_sql = "	SELECT onecard_order_voucher_redeem 
										FROM " . TABLE_ONECARD . " 
										WHERE onecard_orders_id = '" . tep_db_input($order_id) . "'";
        $payment_status_result_sql = tep_db_query($payment_status_select_sql);
        $payment_status_row = tep_db_fetch_array($payment_status_result_sql);

        if ($payment_status_row['onecard_order_voucher_redeem'] == 1) {
            return true;
        } else {
            return false;
        }
    }

    function is_supported_currency($selected_currency) {
        return (in_array($selected_currency, $this->onecard_currencies) ? true : false);
    }

    function load_pm_setting($language_id = 1) {
        $pm_setting_array = array();
        //load value from DB
        $payment_configuration_info_select_sql = "	SELECT pci.payment_configuration_info_key, pcid.payment_configuration_info_value
													FROM " . TABLE_PAYMENT_CONFIGURATION_INFO . " as pci
													LEFT JOIN " . TABLE_PAYMENT_CONFIGURATION_INFO_DESCRIPTION . " as pcid 
														ON pci.payment_configuration_info_id = pcid.payment_configuration_info_id
															AND pcid.languages_id = '" . (int) $language_id . "'
													WHERE pci.payment_methods_id = '" . (int) $this->payment_methods_id . "'
													ORDER BY pci.payment_configuration_info_sort_order";
        $payment_configuration_info_result_sql = tep_db_query($payment_configuration_info_select_sql);
        if (tep_db_num_rows($payment_configuration_info_result_sql)) {
            while ($payment_configuration_info_row = tep_db_fetch_array($payment_configuration_info_result_sql)) {
                $pm_setting_array[$payment_configuration_info_row['payment_configuration_info_key']] = $payment_configuration_info_row['payment_configuration_info_value'];
            }
        } else if ((int) $this->payment_methods_parent_id > 0) { //load value from PG
            $payment_configuration_info_select_sql = "	SELECT pci.payment_configuration_info_key, pcid.payment_configuration_info_value
														FROM " . TABLE_PAYMENT_CONFIGURATION_INFO . " as pci
														LEFT JOIN " . TABLE_PAYMENT_CONFIGURATION_INFO_DESCRIPTION . " as pcid 
															ON pci.payment_configuration_info_id = pcid.payment_configuration_info_id
																AND pcid.languages_id = '" . (int) $language_id . "'
														WHERE pci.payment_methods_id = '" . (int) $this->payment_methods_parent_id . "'
														ORDER BY pci.payment_configuration_info_sort_order";
            $payment_configuration_info_result_sql = tep_db_query($payment_configuration_info_select_sql);
            while ($payment_configuration_info_row = tep_db_fetch_array($payment_configuration_info_result_sql)) {
                $pm_setting_array[$payment_configuration_info_row['payment_configuration_info_key']] = $payment_configuration_info_row['payment_configuration_info_value'];
            }
        }
        return $pm_setting_array;
    }

    function get_merchant_account($selected_currency) {
        $payment_methods_instance_id = 0;
        $payment_gateway_instance_select_sql = "SELECT pmi.payment_methods_instance_id, pmi.payment_methods_instance_follow_default, pmi.currency_code 
				 								FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " as pmi
				 								WHERE pmi.payment_methods_id = '" . (int) $this->payment_methods_id . "'
				 								LIMIT 1";
        $payment_gateway_instance_result_sql = tep_db_query($payment_gateway_instance_select_sql);
        if (tep_db_num_rows($payment_gateway_instance_result_sql)) {
            $payment_gateway_instance_select_sql = "SELECT pmi.payment_methods_instance_id, pmi.payment_methods_instance_follow_default 
					 								FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " as pmi
					 								WHERE pmi.currency_code = '" . tep_db_input($selected_currency) . "' 
					 									AND pmi.payment_methods_id = '" . (int) $this->payment_methods_id . "'";
            $payment_gateway_instance_result_sql = tep_db_query($payment_gateway_instance_select_sql);
            if ($payment_gateway_instance_row = tep_db_fetch_array($payment_gateway_instance_result_sql)) {
                if ((int) $payment_gateway_instance_row['payment_methods_instance_follow_default'] > 0) { // Follow Default Currency Setting
                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_follow_default'];
                } else { // Has own setting
                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_id'];
                }
            }
        } else {
            $payment_gateway_instance_select_sql = "SELECT pmi.payment_methods_instance_id, pmi.payment_methods_instance_follow_default 
					 								FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " as pmi
					 								INNER JOIN " . TABLE_PAYMENT_METHODS . " as pm
					 									ON pm.payment_methods_parent_id = pmi.payment_methods_id
					 								WHERE pmi.currency_code = '" . tep_db_input($selected_currency) . "' 
					 									AND pm.payment_methods_id = '" . (int) $this->payment_methods_id . "'";
            $payment_gateway_instance_result_sql = tep_db_query($payment_gateway_instance_select_sql);
            if ($payment_gateway_instance_row = tep_db_fetch_array($payment_gateway_instance_result_sql)) {
                if ((int) $payment_gateway_instance_row['payment_methods_instance_follow_default'] > 0) {
                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_follow_default'];
                } else {
                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_id'];
                }
            }
        }
        $payment_gateway_instance_setting_select_sql = "SELECT pmis.payment_methods_instance_setting_key, pmis.payment_methods_instance_setting_value 
						 								FROM " . TABLE_PAYMENT_METHODS_INSTANCE_SETTING . " as pmis
						 								WHERE pmis.payment_methods_instance_id = '" . (int) $payment_methods_instance_id . "'";
        $payment_gateway_instance_setting_result_sql = tep_db_query($payment_gateway_instance_setting_select_sql);
        while ($payment_gateway_instance_setting_row = tep_db_fetch_array($payment_gateway_instance_setting_result_sql)) {
            switch ($payment_gateway_instance_setting_row['payment_methods_instance_setting_key']) {
                case 'MODULE_PAYMENT_ONECARD_MERCHANT_ID':
                    $this->merchant_id = $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];
                    break;
                case 'MODULE_PAYMENT_ONECARD_MODULUS':
                    $this->modulus = $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];
                    break;
                case 'MODULE_PAYMENT_ONECARD_PRODUCT_CODE':
                    $this->product_code = $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];
                    break;
                case 'MODULE_PAYMENT_ONECARD_EXPONENT':
                    $this->exponent = $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];
                    break;
            }
        }
    }

    function install() {
        $install_configuration_flag = true;

        if ($this->payment_methods_id > 0) {
            if ($this->receive_status != 1) {
                $update_receive_status_sql_data = array("payment_methods_receive_status" => 1);
                tep_db_perform(TABLE_PAYMENT_METHODS, $update_receive_status_sql_data, 'update', "payment_methods_id = '" . (int) $this->payment_methods_id . "'");
            } else {
                $install_configuration_flag = false;
            }
        } else {
            $install_payment_gateway_data_sql = array('payment_methods_code' => $this->code,
                'payment_methods_send_status' => 0,
                'payment_methods_title' => $this->code,
                'payment_methods_types_id' => 0,
                'payment_methods_parent_id' => 0,
                'payment_methods_legend_color' => '#950100',
                'payment_methods_sort_order' => 30,
                'payment_methods_filename' => $this->filename,
                'payment_methods_logo' => '',
                'date_added' => 'now()',
                'last_modified' => 'now()',
                'payment_methods_receive_status' => 1,
                'payment_methods_receive_status_mode' => 0
            );
            tep_db_perform(TABLE_PAYMENT_METHODS, $install_payment_gateway_data_sql);
            $this->payment_methods_id = tep_db_insert_id();
        }
        if ($install_configuration_flag) {
            $install_key_array = array();
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Set Order Status',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_ONECARD_ORDER_STATUS_ID',
                    'payment_configuration_info_description' => 'Set the status of orders made with this payment module to this value.',
                    'payment_configuration_info_sort_order' => '1030',
                    'set_function' => 'tep_cfg_pull_down_order_statuses(',
                    'use_function' => 'tep_get_order_status_name',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => '1',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Set Order\'s "Processing" Status',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_ONECARD_PROCESSING_STATUS_ID',
                    'payment_configuration_info_description' => 'Set the initial processing status of orders made with this payment module to this value.',
                    'payment_configuration_info_sort_order' => '1035',
                    'set_function' => 'tep_cfg_pull_down_order_statuses(',
                    'use_function' => 'tep_get_order_status_name',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => '7',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Payment Message',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_ONECARD_MESSAGE',
                    'payment_configuration_info_description' => 'Payment message will show up during checkout process.',
                    'payment_configuration_info_sort_order' => '1045',
                    'set_function' => 'tep_cfg_textarea(',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'Thank you for shopping at OffGamers.com.',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Payment Email Message',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_ONECARD_EMAIL_MESSAGE',
                    'payment_configuration_info_description' => 'This message will show up in order confirmation e-mail.',
                    'payment_configuration_info_sort_order' => '1045',
                    'set_function' => 'tep_cfg_textarea(',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'Thank you for shopping at OffGamers.com.',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Confirm Complete (in days)',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_ONECARD_CONFIRM_COMPLETE',
                    'payment_configuration_info_description' => 'The number of days in which an order made by this payment method is confirm completed',
                    'payment_configuration_info_sort_order' => '1050',
                    'set_function' => '',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => '0',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Test Mode',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_ONECARD_TEST_MODE',
                    'payment_configuration_info_description' => 'Testing Mode?',
                    'payment_configuration_info_sort_order' => '1000',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );

            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Require Address Information',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_ONECARD_MANDATORY_ADDRESS_FIELD',
                    'payment_configuration_info_description' => 'Set address field as required info during checkout.',
                    'payment_configuration_info_sort_order' => '1100',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Customer Payment Info',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_ONECARD_CUSTOMER_PAYMENT_INFO',
                    'payment_configuration_info_description' => 'Set to true if this Payment Method required customer input for Payment Information required customer payment info.',
                    'payment_configuration_info_sort_order' => '1105',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Require IC Information',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_ONECARD_MANDATORY_IC_FIELD',
                    'payment_configuration_info_description' => 'Set IC field as required info during checkout.',
                    'payment_configuration_info_sort_order' => '1110',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Require Contact Information',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_ONECARD_MANDATORY_CONTACT_FIELD',
                    'payment_configuration_info_description' => 'Set Contact field as required info during checkout.',
                    'payment_configuration_info_sort_order' => '1115',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );
            foreach ($install_key_array as $data) {
                tep_db_perform(TABLE_PAYMENT_CONFIGURATION_INFO, $data['info']);
                $payment_conf_id = tep_db_insert_id();

                $data['desc']['payment_configuration_info_id'] = (int) $payment_conf_id;
                tep_db_perform(TABLE_PAYMENT_CONFIGURATION_INFO_DESCRIPTION, $data['desc']);
            }
        }
        return 1;
    }

    function remove() {
        tep_db_query("delete from " . TABLE_CONFIGURATION . " where configuration_key in ('" . implode("', '", $this->keys()) . "')");
    }

    function keys() {
        return array('MODULE_PAYMENT_ONECARD_ORDER_STATUS_ID',
            'MODULE_PAYMENT_ONECARD_PROCESSING_STATUS_ID',
            'MODULE_PAYMENT_ONECARD_MESSAGE',
            'MODULE_PAYMENT_ONECARD_EMAIL_MESSAGE',
            'MODULE_PAYMENT_ONECARD_CONFIRM_COMPLETE',
            'MODULE_PAYMENT_ONECARD_TRANTYPE',
            'MODULE_PAYMENT_ONECARD_MANDATORY_ADDRESS_FIELD');
    }

    function configuration_information_keys() {
        return array('MODULE_PAYMENT_ONECARD_ORDER_STATUS_ID',
            'MODULE_PAYMENT_ONECARD_PROCESSING_STATUS_ID',
            'MODULE_PAYMENT_ONECARD_MESSAGE',
            'MODULE_PAYMENT_ONECARD_EMAIL_MESSAGE',
            'MODULE_PAYMENT_ONECARD_CONFIRM_COMPLETE',
            'MODULE_PAYMENT_ONECARD_MANDATORY_ADDRESS_FIELD',
            'MODULE_PAYMENT_ONECARD_CUSTOMER_PAYMENT_INFO',
            'MODULE_PAYMENT_ONECARD_MANDATORY_IC_FIELD',
            'MODULE_PAYMENT_ONECARD_MANDATORY_CONTACT_FIELD');
    }

    function multi_lang_configuration_info_keys() {
        return array('MODULE_PAYMENT_ONECARD_MESSAGE',
            'MODULE_PAYMENT_ONECARD_EMAIL_MESSAGE');
    }

    function merchant_information_keys() {
        include_once(DIR_WS_CLASSES . "payment_methods.php");

        return array(
            'MODULE_PAYMENT_ONECARD_MERCHANT_ID' => array("field" => 'text', "mapping" => 'MODULE_PAYMENT_ONECARD_LNG_MERCHANT_ID'),
            'MODULE_PAYMENT_ONECARD_MODULUS' => array("field" => 'text', "mapping" => 'MODULE_PAYMENT_ONECARD_LNG_MODULUS'),
            'MODULE_PAYMENT_ONECARD_PRODUCT_CODE' => array("field" => 'text', "mapping" => 'MODULE_PAYMENT_ONECARD_LNG_PRODUCT_CODE'),
            'MODULE_PAYMENT_ONECARD_EXPONENT' => array("field" => 'text', "mapping" => 'MODULE_PAYMENT_ONECARD_LNG_EXPONENT'),
            'MODULE_PAYMENT_TAX' => array("field" => 'select', 'mapping' => "MODULE_PAYMENT_TAX", "select" => payment_methods::get_tax_option())
        );
    }

    function get_pg_id() {
        return $this->payment_methods_id;
    }

    function get_ipn_flag() {
        return $this->has_ipn;
    }

    function check_trans_status($order_id) {
        global $currencies, $login_email_address;

        $return_flag = 0;

        $onecard_status_history_str = '';
        $order_info_select_sql = "	SELECT o.payment_methods_id, o.currency 
									FROM " . TABLE_ORDERS . " AS o 
									WHERE o.orders_id = '" . tep_db_input($order_id) . "'";
        $order_info_result_sql = tep_db_query($order_info_select_sql);
        if ($order_info_row = tep_db_fetch_array($order_info_result_sql)) {
            if ($order_info_row['payment_methods_id'] != $this->payment_methods_id) {
                return 0;
            }
            $this->get_merchant_account($order_info_row['currency']);

            require_once(DIR_FS_CATALOG . DIR_WS_INCLUDES . 'modules/payment/onecard/rsa.php');

            $voucher_code_select_sql = "SELECT onecard_voucher_code
										FROM " . TABLE_ONECARD . "
										WHERE onecard_orders_id = '" . (int) $order_id . "'";
            $voucher_code_result_sql = tep_db_query($voucher_code_select_sql);
            $voucher_code_row = tep_db_fetch_array($voucher_code_result_sql);

            $url = 'https://www.onecard.net/remote/voucher/getVoucherInfo?merchantId=' . $this->merchant_id . '&voucherCode=' . md5($voucher_code_row['onecard_voucher_code']);
            $ch = curl_init($url);
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($ch, CURLOPT_NOPROGRESS, 0);
            curl_setopt($ch, CURLOPT_VERBOSE, 1);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);  //Windows 2003 Compatibility
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
            curl_setopt($ch, CURLOPT_TIMEOUT, 120);
            if ($this->connect_via_proxy) {
                curl_setopt($ch, CURLOPT_PROXY, 'http://*************:3128');
                curl_setopt($ch, CURLOPT_PROXYUSERPWD, 'devbackend:devonly');
            }
            $response = curl_exec($ch);
            curl_close($ch);

            $orders_total_select_sql = "SELECT text 
										FROM " . TABLE_ORDERS_TOTAL . "
										WHERE orders_id = '" . (int) $order_id . "'
											AND class = 'ot_total'";
            $orders_total_result_sql = tep_db_query($orders_total_select_sql);
            $orders_total_row = tep_db_fetch_array($orders_total_result_sql);

            $onecard_status_history_str .= "<b><u>Get Voucher Information</u></b>\n";

            $returned_str = $response;
            if (!preg_match('/ACK\=error/i', $returned_str)) {
                $onecard_rsa_obj = new onecard_rsa();
                $returned_str = str_replace(" ", "+", $returned_str);
                $returned_str = $onecard_rsa_obj->rsa_decrypt(base64_decode($returned_str), $this->exponent, $this->modulus, 2048);
            }

            $returned_temp_array = array();
            $returned_temp_array = explode("&", $returned_str);

            foreach ($returned_temp_array as $returned_temp_data_loop) {
                $returned_temp_data_loop = explode("=", $returned_temp_data_loop);
                $returned_array[$returned_temp_data_loop[0]] = (isset($returned_temp_data_loop[1]) ? $returned_temp_data_loop[1] : '' );
                switch ($returned_temp_data_loop[0]) {
                    case 'PRODUCTCODE':
                        $onecard_status_history_str .= 'Product Code = ' . $returned_array[$returned_temp_data_loop[0]];
                        if (str_replace("-", "", $this->product_code) != $returned_array[$returned_temp_data_loop[0]]) {
                            $onecard_status_history_str .= '&nbsp;<span class="redIndicator">Product Code not match</span>';
                        }
                        $onecard_status_history_str .= "\n";
                        break;
                    case 'CREATED':
                        $onecard_status_history_str .= 'Voucher Created Datetime = ' . ($returned_array[$returned_temp_data_loop[0]] > 0 ? date("Y-m-d H:i:s", $returned_array[$returned_temp_data_loop[0]]) : $returned_array[$returned_temp_data_loop[0]]) . "\n";
                        break;
                    case 'EXPIRES':
                        $onecard_status_history_str .= 'Voucher Expiry Datetime = ' . ($returned_array[$returned_temp_data_loop[0]] > 0 ? date("Y-m-d H:i:s", $returned_array[$returned_temp_data_loop[0]]) : $returned_array[$returned_temp_data_loop[0]]) . "\n";
                        break;
                    case 'REDEEMED':
                        $onecard_status_history_str .= 'Redeemed = <span class="redIndicator">' . ($returned_array[$returned_temp_data_loop[0]] == 1 ? 'Yes' : ($returned_array[$returned_temp_data_loop[0]] == 0 ? 'No' : 'Unknown (' . $returned_array[$returned_temp_data_loop[0]] . ')')) . "</span>\n";
                        break;
                    case 'REDEEMEDTIME':
                        $onecard_status_history_str .= 'Voucher Redeemed Datetime = ' . ($returned_array[$returned_temp_data_loop[0]] > 0 ? date("Y-m-d H:i:s", $returned_array[$returned_temp_data_loop[0]]) : $returned_array[$returned_temp_data_loop[0]]) . "\n";
                        break;
                    case 'TRANSACTIONTIME':
                        //$onecard_status_history_str .= 'Transaction Datetime = ' . date("Y-m-d H:i:s", $returned_array[$returned_temp_data_loop[0]]) . "\n";
                        break;
                    case 'ERRORCODE':
                        $onecard_status_history_str .= 'Error Code = ' . $returned_array[$returned_temp_data_loop[0]];
                        $onecard_status_history_str .= '&nbsp;(' . $this->get_error_description($returned_array[$returned_temp_data_loop[0]]) . ')';
                        $onecard_status_history_str .= "\n";
                        break;
                    default:
                        if ($returned_temp_data_loop[0] != 'AMOUNT' && $returned_temp_data_loop[0] != 'CURRENCY') {
                            if ($returned_temp_data_loop[0] == 'TRXREFNUMBER') {
                                $onecard_status_history_str .= 'Transaction Ref Number = ' . $returned_array[$returned_temp_data_loop[0]];
                                if ($returned_array[$returned_temp_data_loop[0]] != $order_id) {
                                    $onecard_status_history_str .= "&nbsp;<span class='redIndicator'>Voucher's Order ID not matched</span>";
                                }
                            } else {
                                $onecard_status_history_str .= $returned_temp_data_loop[0] . ' = ' . $returned_array[$returned_temp_data_loop[0]];
                            }
                            $onecard_status_history_str .= "\n";
                        }
                        break;
                }
            }

            // Mapping
            if (isset($returned_array['CURRENCY'])) {
                if ($returned_array['CURRENCY'] == 'LE') {
                    $returned_array['CURRENCY'] = 'EGP';
                } else if ($returned_array['CURRENCY'] == 'SR') {
                    $returned_array['CURRENCY'] = 'SAR';
                }
            }
            $onecard_gross_amt = number_format($returned_array['AMOUNT'], $currencies->currencies[$returned_array['CURRENCY']]['decimal_places'], $currencies->currencies[$returned_array['CURRENCY']]['decimal_point'], $currencies->currencies[$returned_array['CURRENCY']]['thousands_point']);

            $onecard_status_history_str .= 'Amount = ' . $currencies->currencies[$returned_array['CURRENCY']]['symbol_left'] . $onecard_gross_amt . $currencies->currencies[$returned_array['CURRENCY']]['symbol_right'];

            if ($currencies->currencies[$returned_array['CURRENCY']]['symbol_left'] . $onecard_gross_amt . $currencies->currencies[$returned_array['CURRENCY']]['symbol_right'] != strip_tags($orders_total_row['text'])) {
                $onecard_status_history_str .= ' <span class="redIndicator">not matched with ' . strip_tags($orders_total_row['text']) . "</span>";
            }

            $orders_status_history_data_sql = array('onecard_orders_id' => (int) $order_id,
                'onecard_status_history_datetime' => 'now()',
                'onecard_status_history_voucher_code' => $voucher_code_row['onecard_voucher_code'],
                'onecard_status_history_description' => tep_db_prepare_input($onecard_status_history_str),
                'onecard_status_history_status' => tep_db_prepare_input($returned_array['ACK']),
                'onecard_changed_by' => tep_db_prepare_input((isset($login_email_address) && tep_not_null($login_email_address)) ? $login_email_address : 'system')
            );
            tep_db_perform(TABLE_ONECARD_STATUS_HISTORY, $orders_status_history_data_sql);

            $onecard_data_sql = array('onecard_orders_id' => (int) $order_id,
                'onecard_status' => tep_db_prepare_input((isset($returned_array['ACK']) ? $returned_array['ACK'] : '')),
                'onecard_date' => tep_db_prepare_input((isset($returned_array['CREATED']) ? date("Y-m-d H:i:s", $returned_array['CREATED']) : '')),
                'onecard_expired' => tep_db_prepare_input((isset($returned_array['EXPIRES']) ? date("Y-m-d H:i:s", $returned_array['EXPIRES']) : '')),
                'onecard_redeemed' => tep_db_prepare_input((isset($returned_array['REDEEMED']) ? $returned_array['REDEEMED'] : '')),
                'onecard_redeemed_time' => tep_db_prepare_input((isset($returned_array['REDEEMEDTIME']) ? date("Y-m-d H:i:s", $returned_array['REDEEMEDTIME']) : '')),
                //'onecard_transaction_time' => tep_db_prepare_input((isset($returned_array['TRANSACTIONTIME'])?date("Y-m-d H:i:s", $returned_array['TRANSACTIONTIME']):'')),
                'onecard_amount' => tep_db_prepare_input((isset($returned_array['AMOUNT']) ? $returned_array['AMOUNT'] : '')),
                'onecard_currency' => tep_db_prepare_input((isset($returned_array['CURRENCY']) ? $returned_array['CURRENCY'] : ''))
            );

            tep_db_perform(TABLE_ONECARD, $onecard_data_sql, 'update', " onecard_orders_id = '" . (int) $order_id . "' ");

            $onecard_status_history_str = "";
            if ($returned_array['TRXREFNUMBER'] == $order_id) {
                if ($returned_array['REDEEMED'] == 0) {
                    if (($returned_array['ACK'] == 'success' && !$this->testing) || ($this->testing && $returned_array['ACK'] == 'test')) {
                        $onecard_status_history_str .= "<b><u>Redeem Voucher</u></b>\n";

                        // go redeem
                        $url = 'https://www.onecard.net/remote/voucher/redeemVoucher?merchantId=' . $this->merchant_id . '&voucherCode=' . md5($voucher_code_row['onecard_voucher_code']) . '&redeemRefNumber=' . (int) $order_id;
                        $ch = curl_init($url);
                        curl_setopt($ch, CURLOPT_URL, $url);
                        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
                        curl_setopt($ch, CURLOPT_NOPROGRESS, 0);
                        curl_setopt($ch, CURLOPT_VERBOSE, 1);
                        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);  //Windows 2003 Compatibility
                        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
                        curl_setopt($ch, CURLOPT_TIMEOUT, 120);
                        if ($this->connect_via_proxy) {
                            curl_setopt($ch, CURLOPT_PROXY, 'http://*************:3128');
                            curl_setopt($ch, CURLOPT_PROXYUSERPWD, 'devbackend:devonly');
                        }
                        $response = curl_exec($ch);
                        curl_close($ch);

                        $redeem_returned_str = $onecard_rsa_obj->rsa_decrypt(base64_decode($response), $this->exponent, $this->modulus, 2048);

                        $redeem_returned_array = array();
                        $redeem_returned_temp_array = array();
                        $redeem_returned_temp_array = explode("&", $redeem_returned_str);
                        foreach ($redeem_returned_temp_array as $redeem_returned_temp_data_loop) {
                            $redeem_returned_temp_data_loop = explode("=", $redeem_returned_temp_data_loop);
                            $redeem_returned_array[$redeem_returned_temp_data_loop[0]] = (isset($redeem_returned_temp_data_loop[1]) ? $redeem_returned_temp_data_loop[1] : '' );
                        }

                        // Mapping
                        if (isset($redeem_returned_array['CURRENCY'])) {
                            if ($redeem_returned_array['CURRENCY'] == 'LE') {
                                $redeem_returned_array['CURRENCY'] = 'EGP';
                            } else if ($redeem_returned_array['CURRENCY'] == 'SR') {
                                $redeem_returned_array['CURRENCY'] = 'SAR';
                            }
                        }

                        if (($redeem_returned_array['ACK'] == 'success' && !$this->testing) || ($this->testing && $redeem_returned_array['ACK'] == 'test')) {
                            $onecard_status_history_str .= 'ACK = ' . $redeem_returned_array['ACK'] . "\n";

                            $onecard_gross_amt = number_format($redeem_returned_array['AMOUNT'], $currencies->currencies[$redeem_returned_array['CURRENCY']]['decimal_places'], $currencies->currencies[$redeem_returned_array['CURRENCY']]['decimal_point'], $currencies->currencies[$redeem_returned_array['CURRENCY']]['thousands_point']);
                            $onecard_status_history_str .= 'Amount = ' . $currencies->currencies[$redeem_returned_array['CURRENCY']]['symbol_left'] . $onecard_gross_amt . $currencies->currencies[$redeem_returned_array['CURRENCY']]['symbol_right'];
                            if ($currencies->currencies[$redeem_returned_array['CURRENCY']]['symbol_left'] . $onecard_gross_amt . $currencies->currencies[$redeem_returned_array['CURRENCY']]['symbol_right'] != strip_tags($orders_total_row['text'])) {
                                $onecard_status_history_str .= ' <span class="redIndicator">not matched with ' . strip_tags($orders_total_row['text']) . "</span>";
                            } else {
                                $return_flag = 1;
                            }
                            $onecard_status_history_str .= "\n";
                            $onecard_status_history_str .= 'Transaction Ref Number = ' . $redeem_returned_array['TRXREFNUMBER'] . "\n";
                        } else {
                            $onecard_status_history_str .= 'ACK = ' . $this->get_error_description((isset($redeem_returned_array['ERRORCODE']) ? $redeem_returned_array['ERRORCODE'] : '')) . "\n";
                        }
                        //$onecard_status_history_str .= 'Transaction Datetime = ' . date("Y-m-d H:i:s", $redeem_returned_array['TRANSACTIONTIME']) . "\n";
                        if ($return_flag) {
                            $onecard_status_history_str .= "<span class='redIndicator'><b>Voucher Redeem Successfully</b></span>\n";
                            $onecard_data_sql = array('onecard_redeemed' => '1',
                                'onecard_order_voucher_redeem' => '1',
                                'onecard_redeemed_time' => 'now()'
                            );

                            tep_db_perform(TABLE_ONECARD, $onecard_data_sql, 'update', " onecard_orders_id = '" . (int) $order_id . "' ");
                        }
                        $orders_status_history_data_sql = array('onecard_orders_id' => (int) $order_id,
                            'onecard_status_history_datetime' => 'now()',
                            'onecard_status_history_voucher_code' => $voucher_code_row['onecard_voucher_code'],
                            'onecard_status_history_description' => tep_db_prepare_input($onecard_status_history_str),
                            'onecard_status_history_status' => tep_db_prepare_input($returned_array['ACK']),
                            'onecard_changed_by' => tep_db_prepare_input((isset($login_email_address) && tep_not_null($login_email_address)) ? $login_email_address : 'system')
                        );
                        tep_db_perform(TABLE_ONECARD_STATUS_HISTORY, $orders_status_history_data_sql);
                    }
                }
            }
        }
        return $return_flag;
    }

    function get_orders_payment_info_file() {
        return DIR_FS_CATALOG_MODULES . 'payment/onecard/admin/orders.inc.php';
    }

    function get_orders_list_payment_info_file() {
        return DIR_FS_CATALOG_MODULES . 'payment/onecard/admin/orders_list.inc.php';
    }

    function get_ipn_class_file() {
        return '';
    }

    function clear_temp_process($match_case) {
        $delete_temp_process_sql = "DELETE FROM " . TABLE_TEMP_PROCESS . " 
									WHERE page_name = 'checkout_process.php' 
										AND match_case = '" . $match_case . "'";
        tep_db_query($delete_temp_process_sql);
    }

    function get_error_description($pass_error_code) {
        $onecard_description = $pass_error_code;
        switch ($pass_error_code) {
            case '101': $onecard_description = 'Missing Merchant ID'; //MISSING_MERCHANTID (Plain Text, i.e. Not Encrypted)
                break;
            case '102': $onecard_description = 'Missing Voucher Code'; //MISSING_VOUCHERCODE
                break;
            case '103': $onecard_description = 'Missing Redeem Ref Number'; //MISSING_REDEEM_REFNUMBER
                break;
            case '201': $onecard_description = 'Invalid Merchant ID'; //INVALID_MERCHANTID
                break;
            case '202': $onecard_description = 'Invalid Redeem Ref Number Lenght'; //INVALID_REDEEM_REFNUMBER_LENGTH
                break;
            case '301': $onecard_description = 'Voucher Code Not Match'; //VOUCHERCODE_NOTMATCH
                break;
            case '302': $onecard_description = 'Voucher Code Redeemed'; //VOUCHERCODE_REDEEMED
                break;
            case '303': $onecard_description = 'Voucher Code Expired'; //VOUCHERCODE_EXPIRED
                break;
            case '304': $onecard_description = 'Product Does Not Belong To Merchant'; //PRODUCT_DOESNOTBELONGTOMERCHANT
                break;
            case '500': $onecard_description = 'Missing Transaction Number'; //MISSING_TRXREFNUMBER
                break;
            case '501': $onecard_description = 'Internal Server Error'; //INTERNAL_SERVER_ERROR
                break;
            case '502': $onecard_description = 'Service Not Available'; //
                break;
        }
        return $onecard_description;
    }

}

?>