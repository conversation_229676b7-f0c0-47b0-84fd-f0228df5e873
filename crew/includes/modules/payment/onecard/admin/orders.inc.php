﻿<?
/*
  	$Id: orders.inc.php,v 1.2 2010/03/31 04:41:55 boonhock Exp $
	
  	Developer: Ho <PERSON>
  	Copyright (c) 2005 SKC Venture
  	
  	Released under the GNU General Public License
*/


$onecard_trans_info_select_sql = "	SELECT * 
									FROM " . TABLE_ONECARD . "
									WHERE onecard_orders_id = '" . (int)$oID  . "'";
$onecard_trans_info_result_sql = tep_db_query($onecard_trans_info_select_sql);

$onecard_trans_history_select_sql = "	SELECT * 
										FROM " . TABLE_ONECARD_STATUS_HISTORY . " 
										WHERE onecard_orders_id='" . (int)$oID . "'
										ORDER BY onecard_status_history_id";
$onecard_trans_history_result_sql= tep_db_query($onecard_trans_history_select_sql);

include_once(DIR_FS_CATALOG_MODULES . 'payment/alipay/admin/languages/'.$language.'/alipay.lng.php');

?>
<table border="0" width="100%" cellspacing="0" cellpadding="2">
<? ob_start(); 

?>
	<tr>
		<td class="main" colspan="3">
      		<table border="0" width="100%" cellspacing="0" cellpadding="2">
      			<tr>
      				<td class="main" width="12%" nowrap><b><?=ENTRY_PAYMENT_METHOD?></b></td>
      				<td class="main">
      					<div>
      						<? if((int)$order->info['payment_methods_id']>0) { ?><div class="<?=((int)$payment_info_array[$order->info['payment_methods_id']]->confirm_complete_days>0?'rp_icon':'nrp_icon')?>"><b><?=((int)$payment_info_array[$order->info['payment_methods_id']]->confirm_complete_days>0?'RP':'NRP')?></b></div> <? } ?>
      						<div style="width:30px; height:15px; background-color:<?=$payment_module_info['display_colour']?>; float:left; vertical-align:top; text-align:center;"></div>
      						<div style="vertical-align: top">
      							&nbsp;&nbsp;
<?
	if ($view_payment_details_permission) {
		if (tep_not_null($last_payment_method) && $last_payment_method['payment_methods_id'] != $order->info['payment_methods_id']) {
			echo '<span class="redIndicator">'.(tep_not_null($payment_module_info['payment_methods_parent_title'])?$payment_module_info['payment_methods_parent_title'] . ' - ' :'') . $payment_module_info['payment_methods_title'].'</span>';
		} else {
			echo (tep_not_null($payment_module_info['payment_methods_parent_title'])?$payment_module_info['payment_methods_parent_title'] . ' - ' :'') . $payment_module_info['payment_methods_title'];
		}
		
		if (tep_not_null($last_payment_method) && $last_payment_method['payment_methods_id'] != $order->info['payment_methods_id']) {
			echo '&nbsp;&nbsp;<span class="redIndicator">(Last Payment Method: ' . (tep_not_null($last_payment_method['parent_payment_methods_title'])? $last_payment_method['parent_payment_methods_title'] . ' - ' : '' ) . $last_payment_method['payment_methods_title'] . ')</span>';
		}
	} else {
		echo (tep_not_null($payment_module_info['payment_methods_parent_title'])?$payment_module_info['payment_methods_parent_title'] . ' - ' :'') . $payment_module_info['payment_methods_title'];
	}
?>
							</div>
      					</div>
      				</td>
				</tr>
			</table>
		</td>
	</tr>
<?

$payment_method_title_info = ob_get_contents();
ob_end_clean();
echo $payment_method_title_info;
?>
  	<tr>
    	<td class="main">
      		<table border="0" width="100%" cellspacing="0" cellpadding="2">
      			<tr valign="top">
<?


if (!$view_payment_details_permission) {
	//
} else { 
	if ($onecard_trans_info_row = tep_db_fetch_array($onecard_trans_info_result_sql)) {
		$onecard_trans_found = true;
	}
?>
        			<td width="35%">
              			<table border="0" cellspacing="0" cellpadding="2">
							<tr>
								<td class="main" valign="top" nowrap><b><?=ENTRY_ONECARD_VOUCHER_CODE?></b></td>
			                	<td class="main" valign="top" nowrap><?
			                		echo $onecard_trans_info_row["onecard_voucher_code"];
			                		if ($onecard_trans_info_row["onecard_order_voucher_redeem"] != '1') {
			                			echo '&nbsp;<a href="javascript:update_onecard_voucher_code()">Edit</a>';
			                		}
			                		?></td>
							</tr>
<?
	if ($onecard_trans_found) {
?>
							<tr>
								<td class="main" valign="top" nowrap><b><?=ENTRY_ONECARD_DATE?></b>&nbsp;</td>
			                	<td class="main" valign="top" nowrap><?=$onecard_trans_info_row["onecard_date"]?></td>
							</tr>
							<tr>
								<td class="main" valign="top" nowrap><b><?=ENTRY_ONECARD_EXPIRED?></b>&nbsp;</td>
			                	<td class="main" valign="top" nowrap><?=$onecard_trans_info_row["onecard_expired"]?></td>
							</tr>
							<tr>
								<td class="main" valign="top" nowrap><b><?=ENTRY_ONECARD_REDEEMED?></b>&nbsp;</td>
			                	<td class="main" valign="top" nowrap><?=($onecard_trans_info_row["onecard_redeemed"]=='1'?'Yes':'No')?></td>
							</tr>
							<tr>
								<td class="main" valign="top" nowrap><b><?=ENTRY_ONECARD_REDEEMED_TIME?></b>&nbsp;</td>
			                	<td class="main" valign="top" nowrap><?=$onecard_trans_info_row["onecard_redeemed_time"]?></td>
							</tr>
							<tr>
								<td class="main" valign="top" nowrap><b><?=ENTRY_ONECARD_AMOUNT?></b>&nbsp;</td>
			                	<td class="main" valign="top" nowrap><?=$onecard_trans_info_row["onecard_amount"]?></td>
							</tr>
							<tr>
								<td class="main" valign="top" nowrap><b><?=ENTRY_ONECARD_CURRENCY?></b>&nbsp;</td>
			                	<td class="main" valign="top" nowrap><?=$onecard_trans_info_row["onecard_currency"]?></td>
							</tr>
<?
	}
?>
              			</table>
						<script>
							function update_onecard_voucher_code() {
								var passVoucherCode;
								var display_html = 	'<table>'+
													'	<tr>'+
													'		<td width="150px">Voucher Code</td>'+
													'		<td width="*%"><input type="text" id="txt_voucher_code" name="txt_voucher_code" value="" size="50"></td>'+
													'	</tr>'+
													'	<tr>'+
													'		<td></td>'+
													'		<td><div id="div_onecard_voucher_message"></div></td>'+
													'	</tr>'+
													'</table>';
								jquery_confirm_box(display_html, 2, 0, 'Update Voucher Code');
								jQuery("#jconfirm_submit").click(function(){
									jquery_confirm_box('Loading...', 0, 0, 'Please wait');
									jQuery.ajax({
										type: 'GET',
										url: 'orders_xmlhttp.php?action=onecard&subaction=update_voucher_code&oID=<?=(int)$oID?>&voucher_code=' + jQuery("#txt_voucher_code").val(),
										dataType: "xml",
										success: function(xml) {
											if (jQuery(xml).find('success').text() == 1) {
												window.location.href = "<?=tep_href_link(FILENAME_ORDERS, 'oID='.(int)$oID.'&action=edit')?>";
											} else {
												if (jQuery(xml).find('message').text()!='') {
													jquery_confirm_box(jQuery(xml).find('message').text(), 1, 1, 'Warning');
												} else {
													jQuery.unblockUI();
												}
											}
										}
									});
								});
							}
						</script>
        			</td>
<?
}
?>
        			<td valign="top">
              			<table border="0" cellspacing="0" cellpadding="2">
        					<tr>
                				<td class="main" valign="top" nowrap colspan="2"><?
                					if ($order->info['orders_status'] == '1' || $order->info['orders_status'] == '7' || $order->info['orders_status'] == '2') {
	                					echo "&nbsp;&nbsp;";
										echo tep_draw_form('onecard_status_check_form', FILENAME_ORDERS, tep_get_all_get_params(), 'post');
										echo tep_draw_hidden_field('subaction', 'payment_action');
										echo tep_draw_hidden_field('payment_action', 'check_trans_status');
										echo '<input type="button" name="onecardCheckTransStatusBtn" value="Check Payment Status" title="Check Payment Status" class="inputButton" onclick="check_onecard_transaction_status('.$oID.')">';
										echo "</form>";
									}
								?></td>
              				</tr>
<?		if (tep_db_num_rows($onecard_trans_history_result_sql)) { ?>
              				<tr>
                				<td class="main" colspan="2" nowrap>
                					<table border="1" cellspacing="0" cellpadding="2">
        								<tr>
                							<td class="smallText" nowrap><b><?=TABLE_HEADING_ONECARD_DATE?></b></td>
                							<td class="smallText" nowrap><b><?=TABLE_HEADING_ONECARD_STATUS?></b></td>
                							<td class="smallText" nowrap><b><?=TABLE_HEADING_ONECARD_DESCRIPTION?></b></td>
                							<td class="smallText" nowrap><b><?=TABLE_HEADING_ONECARD_CHANGEDBY?></b></td>
                						</tr>
<?
			while ($onecard_trans_history_row = tep_db_fetch_array($onecard_trans_history_result_sql)) {
        	echo ' 						<tr>
                							<td class="smallText" nowrap valign="top">'.$onecard_trans_history_row['onecard_status_history_datetime'].'</td>
                							<td class="smallText" nowrap valign="top">'.(tep_not_null($onecard_trans_history_row['onecard_status_history_status'])?$onecard_trans_history_row['onecard_status_history_status']:'&nbsp;').'</td>
                							<td class="smallText" nowrap>';
            									echo nl2br($onecard_trans_history_row['onecard_status_history_description']);
            echo '							</td>
            								<td class="smallText" nowrap valign="top">'.$onecard_trans_history_row['onecard_changed_by'].'</td>
            							</tr>';
     		}
?>
                					</table>
                				</td>
                			</tr>
<?		}	?>
              			</table>
              		</td>
<?		if ($alipay_trans_found) {	?>
        			<td>
        				<table>
							<tr>
								<td class="main" valign="top" nowrap><b><?=ENTRY_ORDER_CURRENCY?></b>&nbsp;</td>
			                	<td class="main" valign="top" nowrap>
<?
			    					if (strtolower($onecard_trans_info_row["alipay_currency"]) == strtolower($order->info['currency'])) {
			    						echo $onecard_trans_info_row['alipay_currency'];
			    					} else {
			    						echo '<span class="redIndicator">'.$onecard_trans_info_row["alipay_currency"].'<br><span class="smallText">Does not match purchase currency.</span></span>';
			    					}
									echo '('.TEXT_INFO_CURRENCY_NOT_FROM_ALIPAY.')';			                		
?>
			                	</td>
							</tr>
							<tr>
								<td class="main" valign="top" nowrap><b><?=ENTRY_TOTAL_FEE?></b>&nbsp;</td>
			                	<td class="main" valign="top" nowrap><?
			    					$gross_display_text = number_format($onecard_trans_info_row["alipay_total_fee"], $currencies->currencies[$order->info["currency"]]['decimal_places'], $currencies->currencies[$order->info["currency"]]['decimal_point'], $currencies->currencies[$order->info["currency"]]['thousands_point']);
			    					if ($currencies->currencies[$order->info["currency"]]['symbol_left'].$gross_display_text.$currencies->currencies[$order->info["currency"]]['symbol_right'] != strip_tags($order->order_totals['ot_total']['text']) ) {
			    						$gross_display_text = '<span class="redIndicator">'.$gross_display_text.'<br><span class="smallText">Does not match purchase amount.</span></span>';
			    					}
			    					echo $gross_display_text;
			    				?>
							</tr>
        				</table>
        			</td>
<?
		}
?>
      			</tr>
      		</table>
   		</td>
  	</tr>
</table>