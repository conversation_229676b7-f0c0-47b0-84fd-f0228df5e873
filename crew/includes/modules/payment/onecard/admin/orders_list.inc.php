<?
/*
  	$Id: orders_list.inc.php,v 1.1 2010/02/22 09:12:25 boonhock Exp $
	
  	Developer: Ho <PERSON>
  	Copyright (c) 2005 SKC Venture
  	
  	Released under the GNU General Public License
*/
include_once(DIR_FS_CATALOG_MODULES . 'payment/onecard/admin/languages/'.$language.'/onecard.lng.php');

$onecard_trans_info_select_sql = "	SELECT * 
									FROM " . TABLE_ONECARD . "
									WHERE onecard_orders_id = '" . (int)$order_obj->orders_id  . "'";
$onecard_trans_info_result_sql = tep_db_query($onecard_trans_info_select_sql);
?>
<table border="0" width="100%" cellspacing="0" cellpadding="2">
	<tr>
		<td class="subInvoiceBoxHeading">
			<div style="border-bottom: 1px solid #996600; border-top: 1px solid #996600; padding: 2px;">Payment Details</div>
		</td>
	</tr>
<?
if ($onecard_trans_info_row = tep_db_fetch_array($onecard_trans_info_result_sql)) {
	

?>
  	<tr>
    	<td class="main">
      		<table border="0" width="100%" cellspacing="0" cellpadding="2">
      			<tr valign="top">
        			<td width="40%">
              			<table border="0" cellspacing="0" cellpadding="2">
							<tr>
								<td class="main" valign="top" nowrap><b><?=ENTRY_ONECARD_VOUCHER_CODE?></b>&nbsp;</td>
			                	<td class="main" valign="top"><?
			                		$onecard_voucher_code_length = strlen($onecard_trans_info_row["onecard_voucher_code"]);
			                		for ($count_word=0;$count_word<=$onecard_voucher_code_length;$count_word++) {
			                			echo $onecard_trans_info_row["onecard_voucher_code"]{$count_word};
			                			if ($count_word > 0 && $count_word%50==0) {
			                				echo "<br/>";
			                			}
			                		}
			                	?></td>
							</tr>
							<tr>
								<td class="main" valign="top" nowrap><b><?=ENTRY_ONECARD_DATE?></b>&nbsp;</td>
			                	<td class="main" valign="top" nowrap><?=$onecard_trans_info_row["onecard_date"]?></td>
							</tr>
							<tr>
								<td class="main" valign="top" nowrap><b><?=ENTRY_ONECARD_EXPIRED?></b>&nbsp;</td>
			                	<td class="main" valign="top" nowrap><?=$onecard_trans_info_row["onecard_expired"]?></td>
							</tr>
							<tr>
								<td class="main" valign="top" nowrap><b><?=ENTRY_ONECARD_REDEEMED?></b>&nbsp;</td>
			                	<td class="main" valign="top" nowrap><?=$onecard_trans_info_row["onecard_redeemed"]?></td>
							</tr>
							<tr>
								<td class="main" valign="top" nowrap><b><?=ENTRY_ONECARD_REDEEMED_TIME?></b>&nbsp;</td>
			                	<td class="main" valign="top" nowrap><?=$onecard_trans_info_row["onecard_redeemed_time"]?></td>
							</tr>
							<tr>
								<td class="main" valign="top" nowrap><b><?=ENTRY_ONECARD_AMOUNT?></b>&nbsp;</td>
			                	<td class="main" valign="top" nowrap><?=$onecard_trans_info_row["onecard_amount"]?></td>
							</tr>
							<tr>
								<td class="main" valign="top" nowrap><b><?=ENTRY_ONECARD_CURRENCY?></b>&nbsp;</td>
			                	<td class="main" valign="top" nowrap><?=$onecard_trans_info_row["onecard_currency"]?></td>
							</tr>
              			</table>
        			</td>
      			</tr>
      		</table>
   		</td>
  	</tr>
<?
} else {
?>
	<tr>
		<td class="invoiceRecords"><?=ENTRY_NO_PAYMENT_INFO?></td>
	</tr>
<?
}
?>
</table>