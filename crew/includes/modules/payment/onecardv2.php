<?php

require_once('payment_gateway.php');

class onecardv2 extends payment_gateway {

    var $code, $check_trans_status_flag, $onecardv2Currencies;

    function onecardv2($pm_id = '') {
        global $order, $languages_id, $default_languages_id, $currency;

        $this->code = 'onecardv2';
        $this->check_trans_status_flag = false;
        $this->title = $this->code;
        $this->preferred = true;
        $this->filename = 'onecardv2.php';
        $this->auto_cancel_period = 30; // In minutes
        $this->connect_via_proxy = false; // localhost test - set to true, server test - set to false //need proxy to connect outside
        $this->force_to_checkoutprocess = true;
        $this->has_ipn = true;
        $this->payment_methods_id = 0;
        $this->payment_methods_parent_id = 0;

        $payment_methods_select_sql = "	SELECT  payment_methods_parent_id, payment_methods_code, 
												payment_methods_types_id, 
												payment_methods_receive_status_mode, payment_methods_id, 
												payment_methods_title, payment_methods_sort_order, 
												payment_methods_receive_status, payment_methods_legend_color, 
												payment_methods_logo 
										FROM " . TABLE_PAYMENT_METHODS . "
										WHERE 1 ";
        if ((int) $pm_id > 0) {
            $payment_methods_select_sql .= "	AND payment_methods_id = '" . (int) $pm_id . "'";
        } else {
            $payment_methods_select_sql .= "	AND payment_methods_filename = '" . tep_db_input($this->filename) . "'";
        }

        $payment_methods_result_sql = tep_db_query($payment_methods_select_sql);
        if ($payment_methods_row = tep_db_fetch_array($payment_methods_result_sql)) {
            $pm_desc_title_select_sql = "	SELECT payment_methods_description_title 
											FROM " . TABLE_PAYMENT_METHODS_DESCRIPTION . " 
											WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "' 
												AND (IF (languages_id = '" . tep_db_input($languages_id) . "', 1, IF ((	SELECT COUNT(payment_methods_id) > 0 
																														FROM " . TABLE_PAYMENT_METHODS_DESCRIPTION . " 
																														WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "' 
																															AND languages_id = '" . (int) $languages_id . "'), 0, languages_id = '" . (int) $default_languages_id . "')))";
            $pm_desc_title_result_sql = tep_db_query($pm_desc_title_select_sql);
            $pm_desc_title_row = tep_db_fetch_array($pm_desc_title_result_sql);
            $this->display_title = $pm_desc_title_row['payment_methods_description_title'];
            $this->description = $this->display_title;
            $this->enabled = $payment_methods_row['payment_methods_receive_status_mode'];
            $this->payment_methods_id = (int) $payment_methods_row['payment_methods_id'];
            $this->payment_methods_parent_id = (int) $payment_methods_row['payment_methods_parent_id'];
            $this->code = $payment_methods_row['payment_methods_code'];
            $this->title = $payment_methods_row['payment_methods_title'];
            $this->sort_order = $payment_methods_row['payment_methods_sort_order'];
            $this->legend_display_colour = $payment_methods_row['payment_methods_legend_color'];
        }

        $configuration_setting_array = $this->load_pm_setting();
        $this->order_status_id = $configuration_setting_array['MODULE_PAYMENT_ONECARDV2_ORDER_STATUS_ID'];
        $this->processing_status_id = $configuration_setting_array['MODULE_PAYMENT_ONECARDV2_PROCESSING_STATUS_ID'];
        $this->confirm_complete_days = $configuration_setting_array['MODULE_PAYMENT_ONECARDV2_CONFIRM_COMPLETE'];
        $this->require_address_information = $configuration_setting_array['MODULE_PAYMENT_ONECARDV2_MANDATORY_ADDRESS_FIELD'];
        $this->test_mode = ($configuration_setting_array['MODULE_PAYMENT_ONECARDV2_TEST_MODE'] == 'True' ? true : false);
        $this->message = $configuration_setting_array['MODULE_PAYMENT_ONECARDV2_MESSAGE'];
        $this->email_message = $configuration_setting_array['MODULE_PAYMENT_ONECARDV2_EMAIL_MESSAGE'];
        $this->email_footer = str_replace(array("<br>", "<BR>", "\r\n"), array("\n", "\n", ''), $this->email_message);


        $this->onecardv2Currencies = $this->get_support_currencies($this->payment_methods_id);
        $this->defCurr = DEFAULT_CURRENCY;
        $this->order_processing_status = ((int) $this->processing_status_id > 0 ? $this->processing_status_id : 7 );
        $this->order_status = ( (int) $this->order_status_id > 0 ? $this->order_status_id : DEFAULT_ORDERS_STATUS_ID );


        if ($this->test_mode) {
            $this->form_action_url = 'http://onecard.n2vsb.com/customer/integratedPayment.html';
            $this->wap_form_action_url = 'http://onecard.n2vsb.com/mobile/integratedPayment.html';
            $this->status_query_url = 'http://onecard.n2vsb.com/remote/paymentIntegrationQuery.html';
        } else {
            $this->form_action_url = 'https://www.onecard.net/customer/integratedPayment.html';
            $this->wap_form_action_url = 'https://www.onecard.net/mobile/integratedPayment.html';
            $this->status_query_url = 'https://www.onecard.net/remote/paymentIntegrationQuery.html';
        }
    }

    function javascript_validation() {
        return false;
    }

    function selection() {
        global $languages_id, $default_languages_id;

        $selection_array = array();

        $payment_methods_select_sql = "	SELECT payment_methods_id, payment_methods_code, payment_methods_types_id, payment_methods_logo, payment_methods_receive_status_mode 
										FROM " . TABLE_PAYMENT_METHODS . " 
										WHERE payment_methods_receive_status = 1 
											AND payment_methods_receive_status_mode <> 0 
											AND payment_methods_parent_id = '" . (int) $this->payment_methods_id . "' 
										ORDER BY payment_methods_sort_order";
        $payment_methods_result_sql = tep_db_query($payment_methods_select_sql);
        while ($payment_methods_row = tep_db_fetch_array($payment_methods_result_sql)) {
            $pm_desc_title_select_sql = "	SELECT payment_methods_description_title 
											FROM " . TABLE_PAYMENT_METHODS_DESCRIPTION . " 
											WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "' 
												AND (IF (languages_id = '" . tep_db_input($languages_id) . "', 1, IF ((	SELECT COUNT(payment_methods_id) > 0 
																														FROM " . TABLE_PAYMENT_METHODS_DESCRIPTION . " 
																														WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "' 
																															AND languages_id = '" . (int) $languages_id . "'), 0, languages_id = 1)))";
            $pm_desc_title_result_sql = tep_db_query($pm_desc_title_select_sql);
            $pm_desc_title_row = tep_db_fetch_array($pm_desc_title_result_sql);

            $pm_array = $this->load_pm_setting(1, $payment_methods_row['payment_methods_id']);

            $selection_array[] = array('payment_gateway_code' => $this->code,
                'payment_methods_receive_status_mode' => $payment_methods_row['payment_methods_receive_status_mode'],
                'payment_methods_id' => $payment_methods_row['payment_methods_id'],
                'payment_methods_code' => $payment_methods_row['payment_methods_code'],
                'payment_methods_types_id' => $payment_methods_row['payment_methods_types_id'],
                'payment_methods_parent_id' => $this->payment_methods_id,
                'payment_methods_logo' => $payment_methods_row['payment_methods_logo'],
                'payment_methods_description_title' => $pm_desc_title_row['payment_methods_description_title'],
                'currency' => $this->get_support_currencies($payment_methods_row['payment_methods_id']),
                'confirm_complete_days' => $this->confirm_complete_days,
                'show_billing_address' => $this->require_address_information,
            );

            if ($payment_methods_row['payment_methods_receive_status_mode'] == '-1') {
                $pm_status_message_select = "	SELECT payment_methods_status_message 
												FROM " . TABLE_PAYMENT_METHODS_STATUS_DESCRIPTION . " 
												WHERE payment_methods_mode = 'RECEIVE' 
													AND payment_methods_status = '-1' 
													AND languages_id = '" . (int) $languages_id . "' 
													AND payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "'";
                $pm_status_message_result = tep_db_query($pm_status_message_select);
                $pm_status_message_row = tep_db_fetch_array($pm_status_message_result);
                $selection_array[sizeof($selection_array) - 1]['payment_methods_status_message'] = $pm_status_message_row['payment_methods_status_message'];
            }
        }

        return $selection_array;
    }

    function set_support_currencies($currency_array) {
        $this->onecardv2Currencies = $currency_array;
    }

    function get_require_address_information() {
        return $this->require_address_information;
    }

    function draw_confirm_button() {
        return tep_image_button2('red', 'javascript:document.checkout_confirmation.submit()', MODULE_PAYMENT_ONECARDV2_TEXT_CART, 220);
    }

    function get_confirm_button_caption() {
        return MODULE_PAYMENT_ONECARDV2_TEXT_TITLE;
    }

    function pre_confirmation_check() {
        return false;
    }

    function confirmation() {
        
    }

    function process_button() {
        global $currencies, $currency, $customer_id, $order, $languages_id, $order_logged;

        if ($this->force_to_checkoutprocess && !tep_session_is_registered('order_logged')) {
            return;
        }

        $onecardCurrency = $currency;
        if (!in_array($onecardCurrency, $this->onecardv2Currencies)) {
            $onecardCurrency = $this->onecardv2Currencies[0];
        }
        $this->get_merchant_account($onecardCurrency);
        $payment_gateway_amount = number_format($order->info['total'] * $currencies->get_value($onecardCurrency), $currencies->get_decimal_places($onecardCurrency), '.', '');
        $timeIn = round(microtime(true) * 1000); //in miliseconds timestamp
        $hashKey = md5($this->merchantId . $order_logged . $payment_gateway_amount . $onecardCurrency . $timeIn . $this->transkey);

        $process_button_string =
                tep_draw_hidden_field('OneCard_MerchID', $this->merchantId) .
                tep_draw_hidden_field('OneCard_TransID', $order_logged) .
                tep_draw_hidden_field('OneCard_Amount', $payment_gateway_amount) .
                tep_draw_hidden_field('OneCard_Currency', $onecardCurrency) .
                tep_draw_hidden_field('OneCard_Timein', $timeIn) .
                tep_draw_hidden_field('OneCard_MProd', 'Purchase from ' . STORE_NAME) .
                tep_draw_hidden_field('OneCard_ReturnURL', tep_href_link(FILENAME_CHECKOUT_PROCESS, '', 'SSL')) .
                tep_draw_hidden_field('OneCard_HashKey', $hashKey);

        return $process_button_string;
    }

    function before_process() {
        global $order, $order_logged, $currency, $log_object, $language;

        if (!tep_session_is_registered('order_logged'))
            return;

        if (isset($_POST['OneCard_Code']) && isset($_POST['OneCard_TransID'])) {
            if ($_SESSION['order_logged'] == $_POST['OneCard_TransID']) {
                include_once(DIR_WS_CLASSES . 'anti_fraud.php');
                include_once(DIR_WS_CLASSES . 'log.php');
                require_once(DIR_WS_MODULES . 'payment/onecardv2/classes/onecardv2_ipn_class.php');
                $log_object = new log_files('system');

                $onecardv2Ipn = new Onecardv2Ipn($_SESSION['order_logged']);
                $this->get_merchant_account($onecardv2Ipn->key['currency']);
                $order = new order($_SESSION['order_logged']);

                //order verification
                if ((isset($onecardv2Ipn->key['code']) && $onecardv2Ipn->key['code'] == '00') || $this->test_mode) {
                    if ($onecardv2Ipn->validateSignature($order, $this)) {
                        if ($onecardv2Ipn->authenticate($order)) {
                            if ($order->info['orders_status_id'] == $this->order_status) { // If the order still in initial status
                                if ($this->check_trans_status($_SESSION['order_logged'])) {
                                    $onecardv2Ipn->process_this_order($_SESSION['order_logged'], $this, $this->order_processing_status);
                                }
                            }
                        }
                    }
                }
            } else {
                tep_redirect(tep_href_link(FILENAME_CHECKOUT_PAYMENT, 'error_message=' . MODULE_PAYMENT_ONECARDV2_TEXT_ERROR_MESSAGE, 'SSL', true, false));
            }
        }
    }

    function after_process() {
        return false;
    }

    function link_to_payment($new_order_id) {
        global $order_logged;

        if (tep_session_is_registered('order_logged'))
            tep_session_unregister('order_logged');

        tep_session_register('order_logged');
        $order_logged = $new_order_id;
        require(DIR_WS_INCLUDES . 'modules/payment/onecardv2/catalog/onecardv2_splash.php');
        return;
    }

    function is_supported_currency($selected_currency) {
        return (in_array($selected_currency, $this->onecardv2Currencies) ? true : false);
    }

    function check_trans_status($order_id = '') {
        if (empty($order_id) && isset($_GET['oID']) && !empty($_GET['oID'])) {
            $order_id = $_GET['oID'];
        }
        $order_info_select_sql = "	SELECT payment_methods_id, currency
									FROM " . TABLE_ORDERS . "
									WHERE orders_id = '" . tep_db_input($order_id) . "'";
        $order_info_result_sql = tep_db_query($order_info_select_sql);
        if ($order_info_row = tep_db_fetch_array($order_info_result_sql)) {
            $onecardv2Currency = $order_info_row['currency'];

            $this->get_merchant_account($onecardv2Currency);
            // build URL to retrieve transaction result
            $data = '';
            $hashKey = md5($this->merchantId . $order_id . $this->transkey);
            $form_data = array('OneCard_MerchID' => $this->merchantId,
                'OneCard_TransID' => $order_id,
                'OneCard_HashKey' => $hashKey
            );
            // concatenate order information variables to $data
            $data = http_build_query($form_data, null, '&');

            $agent = "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.0)";
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $this->status_query_url);
            curl_setopt($ch, CURLOPT_VERBOSE, 1);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 2);
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
            curl_setopt($ch, CURLOPT_TIMEOUT, 120);
            curl_setopt($ch, CURLOPT_USERAGENT, $agent);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);  //Windows 2003 Compatibility
            if ($this->connect_via_proxy)
                curl_setopt($ch, CURLOPT_PROXY, 'my-proxy.offgamers.lan:3128');
            $curlResult = curl_exec($ch);
            curl_close($ch);
            parse_str(str_replace("|", "&", $curlResult), $result);
            
            if (isset($result['STATUS_CODE'])) {
                $proceedMoveOrder = false;
                if ($result['STATUS_CODE'] == '00') {
                    $statusMessage = 'Success';
                    $proceedMoveOrder = true;
                } else if ($result['STATUS_CODE'] == '18') {
                    $statusMessage = 'Test - Success';
                    if ($this->test_mode) {
                        $proceedMoveOrder = true;
                    }
                } else {
                    $statusMessage = 'Failed - ' . $result['STATUS_MESSAGE'];
                }
                $onecardv2_payment_history_data_array = array(
                    'order_id' => tep_db_prepare_input($order_id),
                    'status_code' => tep_db_prepare_input($result['STATUS_CODE']),
                    'status_message' => tep_db_prepare_input($statusMessage),
                    'hash_key' => tep_db_prepare_input($result['HASH_KEY']),
                    'last_modified' => 'now()'
                );
                $path_info_array = pathinfo($_SERVER['SCRIPT_FILENAME']);
                $onecardv2_payment_history_data_array['changed_by'] = (isset($path_info_array["basename"]) && $path_info_array["basename"] == 'cron_order_payment.php' ? 'cronjob' : (isset($_SESSION['login_email_address']) ? $_SESSION['login_email_address'] : 'system' ) );
                tep_db_perform(TABLE_ONECARDV2_STATUS_HISTORY, $onecardv2_payment_history_data_array);

                if ($proceedMoveOrder) {
                    return true;
                }
            } else {
                ob_start();
                echo "========================REQUEST==========================<BR>";
                print_r($form_data);
                echo "========================================================<BR>";
                echo "=========================RESPONSE==========================<BR>";
                print_r($result);
                echo "========================================================<BR>";
                echo "========================SESSION=========================<BR>";
                print_r($_SESSION);
                echo "========================================================<BR>";
                $debug_html = ob_get_contents();
                ob_end_clean();
                @tep_mail('OnecardV2 FAIL Report', '<EMAIL>', 'OnecardV2 CHECK TRANS STATUS DEBUG E-MAIL', $debug_html, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
            }
        }
        return false;
    }

    function output_error() {
        return false;
    }

    function check() {
        if (!isset($this->_check)) {
            $payment_methods_select_sql = "	SELECT payment_methods_id
											FROM " . TABLE_PAYMENT_METHODS . " as pm
											WHERE pm.payment_methods_id = '" . (int) $this->payment_methods_id . "'";
            $payment_methods_result_sql = tep_db_query($payment_methods_select_sql);
            $this->_check = tep_db_num_rows($payment_methods_result_sql);
        }
        return $this->_check;
    }

    function is_processed_order($order_id) {
        
    }

    function load_pm_setting($language_id = 1, $pm_id = '') {
        $pm_setting_array = array();
        //load value from DB

        if (tep_not_null($pm_id)) {
            $payment_method_id = $pm_id;

            $payment_methods_parent_id_select_sql = "	SELECT payment_methods_parent_id 
														FROM " . TABLE_PAYMENT_METHODS . " 
														WHERE payment_methods_id = '" . (int) $pm_id . "'";
            $payment_methods_parent_id_result_sql = tep_db_query($payment_methods_parent_id_select_sql);
            $payment_methods_parent_id_row = tep_db_fetch_array($payment_methods_parent_id_result_sql);

            $payment_gateway_id = $payment_methods_parent_id_row['payment_methods_parent_id'];
        } else {
            $payment_method_id = $this->payment_methods_id;
            $payment_gateway_id = $this->payment_methods_parent_id;
        }

        $payment_configuration_info_select_sql = "	SELECT pci.payment_configuration_info_key, pcid.payment_configuration_info_value
													FROM " . TABLE_PAYMENT_CONFIGURATION_INFO . " as pci
													LEFT JOIN " . TABLE_PAYMENT_CONFIGURATION_INFO_DESCRIPTION . " as pcid 
														ON pci.payment_configuration_info_id = pcid.payment_configuration_info_id
															AND pcid.languages_id = '" . (int) $language_id . "'
													WHERE pci.payment_methods_id = '" . (int) $payment_method_id . "'
													ORDER BY pci.payment_configuration_info_sort_order";
        $payment_configuration_info_result_sql = tep_db_query($payment_configuration_info_select_sql);
        if (tep_db_num_rows($payment_configuration_info_result_sql)) {
            while ($payment_configuration_info_row = tep_db_fetch_array($payment_configuration_info_result_sql)) {
                $pm_setting_array[$payment_configuration_info_row['payment_configuration_info_key']] = $payment_configuration_info_row['payment_configuration_info_value'];
            }
        } else if ((int) $payment_gateway_id > 0) { //load value from PG
            $payment_configuration_info_select_sql = "	SELECT pci.payment_configuration_info_key, pcid.payment_configuration_info_value
														FROM " . TABLE_PAYMENT_CONFIGURATION_INFO . " as pci
														LEFT JOIN " . TABLE_PAYMENT_CONFIGURATION_INFO_DESCRIPTION . " as pcid 
															ON pci.payment_configuration_info_id = pcid.payment_configuration_info_id
																AND pcid.languages_id = '" . (int) $language_id . "'
														WHERE pci.payment_methods_id = '" . (int) $payment_gateway_id . "'
														ORDER BY pci.payment_configuration_info_sort_order";
            $payment_configuration_info_result_sql = tep_db_query($payment_configuration_info_select_sql);
            while ($payment_configuration_info_row = tep_db_fetch_array($payment_configuration_info_result_sql)) {
                $pm_setting_array[$payment_configuration_info_row['payment_configuration_info_key']] = $payment_configuration_info_row['payment_configuration_info_value'];
            }
        }

        return $pm_setting_array;
    }

    function get_merchant_account($selected_currency) {
        $payment_methods_instance_id = 0;
        $payment_gateway_instance_select_sql = "SELECT pmi.payment_methods_instance_id, pmi.payment_methods_instance_follow_default, pmi.currency_code 
				 								FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " as pmi
				 								WHERE pmi.payment_methods_id = '" . (int) $this->payment_methods_id . "'
				 								LIMIT 1";
        $payment_gateway_instance_result_sql = tep_db_query($payment_gateway_instance_select_sql);
        if (tep_db_num_rows($payment_gateway_instance_result_sql)) {
            $payment_gateway_instance_select_sql = "SELECT pmi.payment_methods_instance_id, pmi.payment_methods_instance_follow_default 
					 								FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " as pmi
					 								WHERE pmi.currency_code = '" . tep_db_input($selected_currency) . "' 
					 									AND pmi.payment_methods_id = '" . (int) $this->payment_methods_id . "'";
            $payment_gateway_instance_result_sql = tep_db_query($payment_gateway_instance_select_sql);
            if ($payment_gateway_instance_row = tep_db_fetch_array($payment_gateway_instance_result_sql)) {
                if ((int) $payment_gateway_instance_row['payment_methods_instance_follow_default'] > 0) { // Follow Default Currency Setting
                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_follow_default'];
                } else { // Has own setting
                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_id'];
                }
            }
        } else {
            $payment_gateway_instance_select_sql = "SELECT pmi.payment_methods_instance_id, pmi.payment_methods_instance_follow_default 
					 								FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " as pmi
					 								INNER JOIN " . TABLE_PAYMENT_METHODS . " as pm
					 									ON pm.payment_methods_parent_id = pmi.payment_methods_id
					 								WHERE pmi.currency_code = '" . tep_db_input($selected_currency) . "' 
					 									AND pm.payment_methods_id = '" . (int) $this->payment_methods_id . "'";
            $payment_gateway_instance_result_sql = tep_db_query($payment_gateway_instance_select_sql);
            if ($payment_gateway_instance_row = tep_db_fetch_array($payment_gateway_instance_result_sql)) {
                if ((int) $payment_gateway_instance_row['payment_methods_instance_follow_default'] > 0) {
                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_follow_default'];
                } else {
                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_id'];
                }
            }
        }

        $payment_gateway_instance_setting_select_sql = "SELECT pmis.payment_methods_instance_setting_key, pmis.payment_methods_instance_setting_value 
						 								FROM " . TABLE_PAYMENT_METHODS_INSTANCE_SETTING . " as pmis
						 								WHERE pmis.payment_methods_instance_id = '" . (int) $payment_methods_instance_id . "'";
        $payment_gateway_instance_setting_result_sql = tep_db_query($payment_gateway_instance_setting_select_sql);
        while ($payment_gateway_instance_setting_row = tep_db_fetch_array($payment_gateway_instance_setting_result_sql)) {
            switch ($payment_gateway_instance_setting_row['payment_methods_instance_setting_key']) {
                case 'MODULE_PAYMENT_ONECARDV2_ID':
                    $this->merchantId = $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];
                    break;
                case 'MODULE_PAYMENT_ONECARDV2_SECRET':
                    $this->transkey = $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];
                    break;
                case 'MODULE_PAYMENT_ONECARDV2_KEYWORD':
                    $this->keyword = $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];
                    break;
            }
        }
    }

    function install() {
        $install_configuration_flag = true;
        if ($this->payment_methods_id > 0) {
            if ($this->receive_status != 1) {
                $update_receive_status_sql_data = array("payment_methods_receive_status" => 1);
                tep_db_perform(TABLE_PAYMENT_METHODS, $update_receive_status_sql_data, 'update', "payment_methods_id = '" . (int) $this->payment_methods_id . "'");
            } else {
                $install_configuration_flag = false;
            }
        } else {
            $install_payment_gateway_data_sql = array('payment_methods_code' => $this->code,
                'payment_methods_send_status' => 0,
                'payment_methods_title' => $this->code,
                'payment_methods_types_id' => 0,
                'payment_methods_parent_id' => 0,
                'payment_methods_legend_color' => '#950100',
                'payment_methods_sort_order' => 6,
                'payment_methods_filename' => $this->filename,
                'payment_methods_logo' => '',
                'date_added' => 'now()',
                'last_modified' => 'now()',
                'payment_methods_receive_status' => 1,
                'payment_methods_receive_status_mode' => 0
            );
            tep_db_perform(TABLE_PAYMENT_METHODS, $install_payment_gateway_data_sql);
            $this->payment_methods_id = tep_db_insert_id();
        }
        if ($install_configuration_flag) {
            $install_key_array = array();
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Set Order Status',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_ONECARDV2_ORDER_STATUS_ID',
                    'payment_configuration_info_description' => 'Set the status of orders made with this payment module to this value.',
                    'payment_configuration_info_sort_order' => '1335',
                    'set_function' => 'tep_cfg_pull_down_order_statuses(',
                    'use_function' => 'tep_get_order_status_name',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => '1',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Set Order\'s "Processing" Status',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_ONECARDV2_PROCESSING_STATUS_ID',
                    'payment_configuration_info_description' => 'Set the initial processing status of orders made with this payment module to this value.',
                    'payment_configuration_info_sort_order' => '1340',
                    'set_function' => 'tep_cfg_pull_down_order_statuses(',
                    'use_function' => 'tep_get_order_status_name',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => '7',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Payment Message',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_ONECARDV2_MESSAGE',
                    'payment_configuration_info_description' => 'Payment message will show up during checkout process (smart2pay).',
                    'payment_configuration_info_sort_order' => '1350',
                    'set_function' => 'tep_cfg_textarea(',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'Thank you for shopping at OffGamers.com.',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Payment Email Message',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_ONECARDV2_EMAIL_MESSAGE',
                    'payment_configuration_info_description' => 'This message will show up in order confirmation e-mail.',
                    'payment_configuration_info_sort_order' => '1360',
                    'set_function' => 'tep_cfg_textarea(',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'Thank you for shopping at OffGamers.com.',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Confirm Complete (in days)',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_ONECARDV2_CONFIRM_COMPLETE',
                    'payment_configuration_info_description' => 'The number of days in which an order made by this payment method is confirm completed.',
                    'payment_configuration_info_sort_order' => '1265',
                    'set_function' => '',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => '0',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Test Mode',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_ONECARDV2_TEST_MODE',
                    'payment_configuration_info_description' => 'Testing Mode?',
                    'payment_configuration_info_sort_order' => '100',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Require Address Information',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_ONECARDV2_MANDATORY_ADDRESS_FIELD',
                    'payment_configuration_info_description' => 'Set address field as required info during checkout.',
                    'payment_configuration_info_sort_order' => '1400',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );
            foreach ($install_key_array as $data) {
                tep_db_perform(TABLE_PAYMENT_CONFIGURATION_INFO, $data['info']);
                $payment_conf_id = tep_db_insert_id();

                $data['desc']['payment_configuration_info_id'] = (int) $payment_conf_id;
                tep_db_perform(TABLE_PAYMENT_CONFIGURATION_INFO_DESCRIPTION, $data['desc']);
            }
        }
        return 1;
    }

    function configuration_information_keys() {
        return array('MODULE_PAYMENT_ONECARDV2_ORDER_STATUS_ID',
            'MODULE_PAYMENT_ONECARDV2_PROCESSING_STATUS_ID',
            'MODULE_PAYMENT_ONECARDV2_CONFIRM_COMPLETE',
            'MODULE_PAYMENT_ONECARDV2_TEST_MODE',
            'MODULE_PAYMENT_ONECARDV2_MANDATORY_ADDRESS_FIELD');
    }

    function multi_lang_configuration_info_keys() {
        return array('MODULE_PAYMENT_ONECARDV2_MESSAGE',
            'MODULE_PAYMENT_ONECARDV2_EMAIL_MESSAGE');
    }

    function merchant_information_keys() {
        include_once(DIR_WS_CLASSES . "payment_methods.php");

        return array(
            'MODULE_PAYMENT_ONECARDV2_ID' => array('field' => 'text', 'mapping' => 'MODULE_PAYMENT_ONECARDV2_LNG_ID'),
            'MODULE_PAYMENT_ONECARDV2_SECRET' => array('field' => 'text', 'mapping' => 'MODULE_PAYMENT_ONECARDV2_LNG_SECRET'),
            'MODULE_PAYMENT_ONECARDV2_KEYWORD' => array('field' => 'text', 'mapping' => 'MODULE_PAYMENT_ONECARDV2_LNG_KEYWORD'),
            'MODULE_PAYMENT_TAX' => array("field" => 'select', 'mapping' => "MODULE_PAYMENT_TAX", "select" => payment_methods::get_tax_option())
        );
    }

    function get_orders_payment_info_file() {
        return DIR_FS_CATALOG_MODULES . 'payment/onecardv2/admin/orders.inc.php';
    }

    function get_orders_list_payment_info_file() {
        return DIR_FS_CATALOG_MODULES . 'payment/onecardv2/admin/orders_list.inc.php';
    }

}

?>