<?php
include_once(DIR_FS_CATALOG_MODULES . 'payment/onecardv2/admin/languages/' . $language . '/onecardv2.lng.php');

$onecardv2_trans_info_select_sql = "	SELECT code, amount, currency, date_added, hash_key
										FROM " . TABLE_ONECARDV2 . " 
										WHERE order_id = '" . $oID . "'";
$onecardv2_trans_info_result_sql= tep_db_query($onecardv2_trans_info_select_sql);

$onecardv2_trans_history_select_sql = "	SELECT status_code, status_message, last_modified, changed_by
										FROM " . TABLE_ONECARDV2_STATUS_HISTORY . "
										WHERE order_id = '" . $oID  . "' 
										ORDER BY last_modified";
$onecardv2_trans_history_result_sql = tep_db_query($onecardv2_trans_history_select_sql);
?>
<table border="0" width="100%" cellspacing="0" cellpadding="2">
<? ob_start(); ?>
	<tr>
		<td class="main">
      		<table border="0" width="100%" cellspacing="0" cellpadding="2">
      			<tr>
      				<td class="main" width="12%" nowrap><b><?=ENTRY_PAYMENT_METHOD?></b></td>
      				<td class="main">
      					<div>
      						<? if((int)$order->info['payment_methods_id']>0) { ?><div class="<?=((int)$payment_info_array[$order->info['payment_methods_id']]->confirm_complete_days>0?'rp_icon':'nrp_icon')?>"><b><?=((int)$payment_info_array[$order->info['payment_methods_id']]->confirm_complete_days>0?'RP':'NRP')?></b></div> <? } ?>
      						<div style="width:30px; height:15px; background-color:<?=$payment_module_info['display_colour']?>; float:left; vertical-align:top; text-align:center;">&nbsp;</div>
      						<div style="vertical-align: top">
      							&nbsp;&nbsp;
<?
	if ($view_payment_details_permission) {
		if (tep_not_null($last_payment_method) && $last_payment_method['payment_methods_id'] != $order->info['payment_methods_id']) {
			echo '<span class="redIndicator">'.(tep_not_null($payment_module_info['payment_methods_parent_title'])?$payment_module_info['payment_methods_parent_title'] . ' - ' :'') . $payment_module_info['payment_methods_title'].'</span>';
		} else {
			echo (tep_not_null($payment_module_info['payment_methods_parent_title'])?$payment_module_info['payment_methods_parent_title'] . ' - ' :'') . $payment_module_info['payment_methods_title'];
		}
		
		if (tep_not_null($last_payment_method) && $last_payment_method['payment_methods_id'] != $order->info['payment_methods_id']) {
			echo '&nbsp;&nbsp;<span class="redIndicator">(Last Payment Method: ' . (tep_not_null($last_payment_method['parent_payment_methods_title'])? $last_payment_method['parent_payment_methods_title'] . ' - ' : '' ) . $last_payment_method['payment_methods_title'] . ')</span>';
		}
	} else {
		echo (tep_not_null($payment_module_info['payment_methods_parent_title'])?$payment_module_info['payment_methods_parent_title'] . ' - ' :'') . $payment_module_info['payment_methods_title'];
	}
?>
							</div>
      					</div>
      				</td>
				</tr>
			</table>
		</td>
	</tr>
<?
$payment_method_title_info = ob_get_contents();
ob_end_clean();

if (tep_db_num_rows($onecardv2_trans_info_result_sql) || tep_db_num_rows($onecardv2_trans_history_result_sql)) {
	$onecardv2_trans_info_row = tep_db_fetch_array($onecardv2_trans_info_result_sql);
	
	echo $payment_method_title_info;
	if (!$view_payment_details_permission) {
		;
	} else {
?>
  	<tr>
    	<td class="main">
      		<table border="0" width="100%" cellspacing="0" cellpadding="2">
      			<tr valign="top">
        			<td width="35%">
              			<table border="0" cellspacing="0" cellpadding="2">
              				<tr>
                				<td class="main" valign="top" nowrap><b><?=ENTRY_ONECARDV2_STATUS_CODE?></b>&nbsp;</td>
                				<td class="main">
									<?php 
										if ($onecardv2_trans_info_row["code"] == '00' || $onecardv2_trans_info_row["code"] == '18') {
											echo 'SUCCESS';
										} else {
											echo 'ERROR';
										}
									?>
								</td>
              				</tr>
              				<tr>
                				<td class="main" valign="top" nowrap><b><?=ENTRY_ONECARDV2_AMOUNT?></b>&nbsp;</td>
                				<td class="main" nowrap><?=$onecardv2_trans_info_row["amount"]?></td>
              				</tr>
              				<tr>
                				<td class="main" valign="top" nowrap><b><?=ENTRY_ONECARDV2_CURRENCY?></b>&nbsp;</td>
                				<td class="main" nowrap><?=$onecardv2_trans_info_row["currency"]?></td>
              				</tr>
              				<tr>
                				<td class="main" valign="top" nowrap><b><?=ENTRY_ONECARDV2_CREATE_DATE?></b>&nbsp;</td>
                				<td class="main" nowrap><?=$onecardv2_trans_info_row["date_added"]?></td>
              				</tr>
              				<tr>
                				<td class="main" valign="top" nowrap><b><?=ENTRY_ONECARDV2_HASHKEY?></b>&nbsp;</td>
                				<td class="main" nowrap><?=$onecardv2_trans_info_row["hash_key"]?></td>
              				</tr>
              			</table>
        			</td>
        			<td>
              			<table border="0" cellspacing="0" cellpadding="2">
              				<tr>
                				<td class="main" colspan="2" nowrap>
                					<table border="1" cellspacing="0" cellpadding="2">
        								<tr>
                							<td class="smallText" nowrap><b><?=TABLE_HEADING_ONECARDV2_DATE?></b></td>
                							<td class="smallText" nowrap><b><?=TABLE_HEADING_ONECARDV2_STATUS?></b></td>
                							<td class="smallText" nowrap><b><?=TABLE_HEADING_ONECARDV2_DESCRIPTION?></b></td>
                							<td class="smallText" nowrap><b><?=TABLE_HEADING_ONECARDV2_CHANGED_BY?></b></td>
                						</tr>
<?		while ($onecardv2_trans_history_row = tep_db_fetch_array($onecardv2_trans_history_result_sql)) {
        	echo ' 						<tr>
                							<td class="smallText" nowrap>'.$onecardv2_trans_history_row['last_modified'].'</td>
                							<td class="smallText" nowrap>'.$onecardv2_trans_history_row['status_code'].'</td>
											<td class="smallText" nowrap>'.$onecardv2_trans_history_row['status_message'].'</td>
											<td class="smallText" nowrap>'.$onecardv2_trans_history_row['changed_by'].'</td>
                						</tr>';
     	}
?>
                					</table>
                				</td>
                			</tr>
              			</table>
              		</td>
      			</tr>
      		</table>
   		</td>
  	</tr>
<?
	}
} else {
	echo $payment_method_title_info;
}
?>
</table>
<?php 
if ($order->info['orders_status'] == '1' || $order->info['orders_status'] == '7' || $order->info['orders_status'] == '2') {
	echo "&nbsp;&nbsp;";
	echo tep_draw_form('onecardv2_status_check_form', FILENAME_ORDERS, tep_get_all_get_params(), 'post');
	echo tep_draw_hidden_field('subaction', 'payment_action');
	echo tep_draw_hidden_field('payment_action', 'check_trans_status');
	echo tep_submit_button('Check Payment Status', 'Check Payment Status', 'name="OnecardV2CheckTransStatusBtn"', 'inputButton');
	echo "</form>";
}
?>