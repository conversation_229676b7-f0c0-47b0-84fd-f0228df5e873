<?php
include_once(DIR_FS_CATALOG_MODULES . 'payment/onecardv2/admin/languages/' . $language . '/onecardv2.lng.php');

$onecardv2_trans_info_select_sql = "	SELECT code, amount, currency, date_added, hash_key
										FROM " . TABLE_ONECARDV2 . " 
										WHERE order_id = '" . $order_obj->orders_id  . "'";
$onecardv2_trans_info_result_sql= tep_db_query($onecardv2_trans_info_select_sql);

?>
<table border="0" width="100%" cellspacing="0" cellpadding="2">
	<tr>
		<td class="subInvoiceBoxHeading">
			<div style="border-bottom: 1px solid #996600; border-top: 1px solid #996600; padding: 2px;">Payment Details</div>
		</td>
	</tr>
<?
if ($onecardv2_trans_info_row = tep_db_fetch_array($onecardv2_trans_info_result_sql)) {
?>
  	<tr>
    	<td class="main">
      		<table border="0" width="100%" cellspacing="0" cellpadding="2">
      			<tr valign="top">
        			<td width="100%">
              			<table border="0" cellspacing="0" cellpadding="2">
              				<tr>
                				<td class="invoiceRecords" valign="top" nowrap><?=ENTRY_ONECARDV2_STATUS_CODE?></td>
                				<td class="invoiceRecords" valign="top">:&nbsp;</td>
                				<td class="invoiceRecords" nowrap>
									<?php 
										if ($onecardv2_trans_info_row["code"] == '00' || $onecardv2_trans_info_row["code"] == '18') {
											echo 'SUCCESS';
										} else {
											echo 'ERROR';
										}
									?>
								</td>
              				</tr>
              				<tr>
                				<td class="invoiceRecords" valign="top" nowrap><?=ENTRY_ONECARDV2_CREATE_DATE?></td>
                				<td class="invoiceRecords" valign="top">:&nbsp;</td>
                				<td class="invoiceRecords"><?=$onecardv2_trans_info_row["date_added"]?></td>
              				</tr>
              				<tr>
                				<td class="invoiceRecords" valign="top" nowrap><?=ENTRY_ONECARDV2_CURRENCY?></td>
                				<td class="invoiceRecords" valign="top">:&nbsp;</td>
                				<td class="invoiceRecords" nowrap><?=$onecardv2_trans_info_row["currency"]?></td>
              				</tr>
              				<tr>
                				<td class="invoiceRecords" valign="top" nowrap><?=ENTRY_ONECARDV2_AMOUNT?></td>
                				<td class="invoiceRecords" valign="top">:&nbsp;</td>
                				<td class="invoiceRecords" nowrap><?=$onecardv2_trans_info_row["amount"]?></td>
              				</tr>
              				<tr>
                				<td class="invoiceRecords" valign="top" nowrap><?=ENTRY_ONECARDV2_HASHKEY?></td>
                				<td class="invoiceRecords" valign="top">:&nbsp;</td>
                				<td class="invoiceRecords" nowrap><?=$onecardv2_trans_info_row["hash_key"]?></td>
              				</tr>
              			</table>
        			</td>
      			</tr>
      		</table>
   		</td>
  	</tr>
<?
} else {
?>
	<tr>
		<td class="invoiceRecords">No further payment information is available.</td>
	</tr>
<?
}
?>
</table>