<?php



require_once('payment_gateway.php');



class payU extends payment_gateway {



    var $code, $payUCurrencies, $merchant_id, $secret_word, $http_user, $http_password, $ip_list;



    function payU($pm_id = '') {

        global $order, $languages_id, $default_languages_id, $currency;



        $this->code = 'payU';

        $this->title = $this->code;

        $this->preferred = true;

        $this->filename = 'payU.php';

        $this->auto_cancel_period = 30; // In minutes

        $this->connect_via_proxy = false; // localhost test - set to true, server test - set to false //need proxy to connect outside

        $this->force_to_checkoutprocess = true;

        $this->has_ipn = true;

        $this->payment_methods_id = 0;

        $this->payment_methods_parent_id = 0;

        $this->check_trans_status_flag = false;



        $payment_methods_select_sql = "	SELECT  payment_methods_parent_id, payment_methods_code, 

												payment_methods_types_id, 

												payment_methods_receive_status_mode, payment_methods_id, 

												payment_methods_title, payment_methods_sort_order, 

												payment_methods_receive_status, payment_methods_legend_color, 

												payment_methods_logo 

										FROM " . TABLE_PAYMENT_METHODS . "

										WHERE 1 ";

        if ((int) $pm_id > 0) {

            $payment_methods_select_sql .= "	AND payment_methods_id = '" . (int) $pm_id . "'";

        } else {

            $payment_methods_select_sql .= "	AND payment_methods_filename = '" . tep_db_input($this->filename) . "'";

        }



        $payment_methods_result_sql = tep_db_query($payment_methods_select_sql);

        if ($payment_methods_row = tep_db_fetch_array($payment_methods_result_sql)) {

            $pm_desc_title_select_sql = "	SELECT payment_methods_description_title 

                                                FROM " . TABLE_PAYMENT_METHODS_DESCRIPTION . " 

                                                WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "' 

                                                        AND (IF (languages_id = '" . tep_db_input($languages_id) . "', 1, IF ((	SELECT COUNT(payment_methods_id) > 0 

                                                                                                                                FROM " . TABLE_PAYMENT_METHODS_DESCRIPTION . " 

                                                                                                                                WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "' 

                                                                                                                                        AND languages_id = '" . (int) $languages_id . "'), 0, languages_id = '" . (int) $default_languages_id . "')))";

            $pm_desc_title_result_sql = tep_db_query($pm_desc_title_select_sql);

            $pm_desc_title_row = tep_db_fetch_array($pm_desc_title_result_sql);

            $this->display_title = $pm_desc_title_row['payment_methods_description_title'];

            $this->description = $this->display_title;

            $this->enabled = $payment_methods_row['payment_methods_receive_status_mode'];

            $this->payment_methods_id = (int) $payment_methods_row['payment_methods_id'];

            $this->payment_methods_parent_id = (int) $payment_methods_row['payment_methods_parent_id'];

            $this->code = $payment_methods_row['payment_methods_code'];

            $this->title = $payment_methods_row['payment_methods_title'];

            $this->sort_order = $payment_methods_row['payment_methods_sort_order'];

            $this->legend_display_colour = $payment_methods_row['payment_methods_legend_color'];

            switch ($this->code) {

                //brazil CC

                case 'BR.ELO' :

                case 'BR.HIPERCARD' :

                    $this->is_credit_card = true;

                    break;

                default:

                    $this->is_credit_card = false;

            }

        }



        $configuration_setting_array = $this->load_pm_setting();

        $this->order_status_id = $configuration_setting_array['MODULE_PAYMENT_PAYU_ORDER_STATUS_ID'];

        $this->processing_status_id = $configuration_setting_array['MODULE_PAYMENT_PAYU_PROCESSING_STATUS_ID'];

        $this->confirm_complete_days = $configuration_setting_array['MODULE_PAYMENT_PAYU_CONFIRM_COMPLETE'];

        $this->require_address_information = $configuration_setting_array['MODULE_PAYMENT_PAYU_MANDATORY_ADDRESS_FIELD'];

        $this->test_mode = ($configuration_setting_array['MODULE_PAYMENT_PAYU_TEST_MODE'] == 'True' ? true : false);

        $this->message = $configuration_setting_array['MODULE_PAYMENT_PAYU_MESSAGE'];

        $this->email_message = $configuration_setting_array['MODULE_PAYMENT_PAYU_EMAIL_MESSAGE'];

        $this->email_footer = str_replace(array("<br>", "<BR>", "\r\n"), array("\n", "\n", ''), $this->email_message);



        $this->payUCurrencies = $this->get_support_currencies($this->payment_methods_id);

        $this->defCurr = DEFAULT_CURRENCY;

        $this->order_processing_status = ((int) $this->processing_status_id > 0 ? $this->processing_status_id : 7 );

        $this->order_status = ( (int) $this->order_status_id > 0 ? $this->order_status_id : DEFAULT_ORDERS_STATUS_ID );

        if ($this->test_mode) {

            $this->form_action_url = 'https://stg.api.payulatam.com/payments-api/4.0/service.cgi';

            $this->report_url = 'https://stg.api.payulatam.com/payments-api/4.0/service.cgi';

            $this->subscription_url = 'https://stg.api.payulatam.com/payments-api/4.0/service.cgi';

            $this->query_url = 'https://stg.api.payulatam.com/reports-api/4.0/service.cgi';

        } else {

            $this->form_action_url = 'https://api.payulatam.com/payments-api/4.0/service.cgi';

            $this->report_url = 'https://api.payulatam.com/reports-api/4.0/service.cgi';

            $this->subscription_url = 'https://api.payulatam.com/payments-api/rest/v4.3/';

            $this->query_url = 'https://api.payulatam.com/reports-api/4.0/service.cgi';

        }

    }



    function post_capture($order_id, $capture_by = '') {

        global $login_email_address;



        if (tep_not_null($capture_by)) {

            $changed_by = $capture_by;

            $comments_type = 0;

        } else {

            $changed_by = $login_email_address;

            $comments_type = 1;

        }



        $result = array();

        if ($this->is_credit_card && $this->code != 'BR.HIPERCARD') {

            $payu_payment_select_sql = "SELECT currency, state, payu_order_id, transaction_id

                                        FROM " . TABLE_PAYU . "

                                        WHERE order_id = '" . tep_db_input($order_id) . "'";

            $payu_payment_result_sql = tep_db_query($payu_payment_select_sql);

            if ($payu_payment_result_row = tep_db_fetch_array($payu_payment_result_sql)) {

                if (trim($payu_payment_result_row['state']) == 'AUTHORIZED' || trim($payu_payment_result_row['state']) == 'IN_PROGRESS') {

                    $this->get_merchant_account($payu_payment_result_row['currency']);

                    $captureArray = array(

                        'language' => 'en',

                        'command' => 'SUBMIT_TRANSACTION',

                        'merchant' =>

                        array(

                            'apiKey' => $this->apiKey,

                            'apiLogin' => $this->apiLogin,

                        ),

                        'transaction' =>

                        array(

                            'order' => array(

                                'id' => $payu_payment_result_row['payu_order_id']

                            ),

                            'type' => 'CAPTURE',

                            'parentTransactionId' => $payu_payment_result_row['transaction_id'],

                        ),

                        'test' => false,

                    );

                    $result = $this->curl_connect($this->form_action_url, $captureArray);

                    if ($result === false) {

                        ob_start();

                        echo "========================PAYU POST CAPTURE CURL FAILED=========================<BR>";

                        echo "================================REQUEST DATA=============================<BR>";

                        print_r($captureArray);

                        echo "============================================================================<BR>";

                        $debug_html = ob_get_contents();

                        ob_end_clean();

                        @tep_mail('PayU Report', '<EMAIL>', 'PAYU POST CAPTURE CURL ERROR', $debug_html, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
                    }





                    if (isset($result['code'])) {

                        $paymentArray = array(

                            'transaction_id' => isset($result['transactionResponse']['transactionId']) ? $result['transactionResponse']['transactionId'] : '',

                            'payu_order_id' => isset($result['transactionResponse']['orderId']) ? $result['transactionResponse']['orderId'] : '',

                            'code' => isset($result['code']) ? $result['code'] : '',

                            'state' => isset($result['transactionResponse']['state']) ? (($result['transactionResponse']['state'] == 'APPROVED') ? 'CAPTURED' : $result['transactionResponse']['state']) : '',

                            'response_code' => isset($result['transactionResponse']['responseCode']) ? $result['transactionResponse']['responseCode'] : '',

                            'operation_date' => isset($result['transactionResponse']['operationDate']) ? $result['transactionResponse']['operationDate'] : '',

                            'data' => json_encode($result),

                            'last_modified' => date('Y-m-d H:i:s'),

                        );

                        $this->saveTransactionData($order_id, $paymentArray);

                        if ($result['code'] == 'SUCCESS' && isset($result['transactionResponse']['state']) && $result['transactionResponse']['state'] == 'APPROVED') {

                            $payu_capture_update_sql = "UPDATE " . TABLE_PAYU . " 

                                                        SET capture_request = 1 

                                                        WHERE order_id = '" . tep_db_input($order_id) . "'";

                            tep_db_query($payu_capture_update_sql);

                            $result = array('code' => 'success',

                                'text' => SUCCESS_ORDER_POST_CAPTURED);

                        } else {

                            $result = array('code' => 'error',

                                'text' => isset($result['error']) && !empty($result['error']) ? $result['error'] : $result['responseCode']);

                        }

                    }



                    $order_history_data_array = array(

                        'orders_id' => $order_id,

                        'orders_status_id' => '',

                        'date_added' => 'now()',

                        'customer_notified' => 0,

                        'comments' => 'Remotely post captured this order',

                        'comments_type' => $comments_type,

                        'set_as_order_remarks' => '0',

                        'changed_by' => $changed_by

                    );

                    tep_db_perform(TABLE_ORDERS_STATUS_HISTORY, $order_history_data_array);

                    tep_update_orders_status_counter($order_history_data_array);

                } else if (trim($payu_payment_result_row['state']) == 'CAPTURED') {

                    $result = array('code' => 'success',

                        'text' => ERROR_ORDER_ALREADY_CAPTURED);

                } else {

                    $result = array('code' => 'error',

                        'text' => ERROR_ORDER_CANNOT_BE_CAPTURED);

                }

            } else {

                $result = array('code' => 'error',

                    'text' => ERROR_ORDER_DOES_NOT_EXIST);

            }

        } else {

            $result = array('code' => 'success',

                'text' => ERROR_ORDER_CANNOT_BE_CAPTURED);

        }



        return $result;

    }



    function check_trans_status($order_id = '') {

        $this->check_trans_status_flag = false;

        if ((isset($_GET['oID']) && !empty($_GET['oID'])) || (!empty($order_id))) {

            $order_id = $_GET['oID'];

            $payu_payment_select_sql = "	SELECT currency, payu_order_id, transaction_id

											FROM " . TABLE_PAYU . "

											WHERE order_id = '" . tep_db_input($order_id) . "'";

            $payu_payment_result_sql = tep_db_query($payu_payment_select_sql);

            if ($payu_payment_result_row = tep_db_fetch_array($payu_payment_result_sql)) {

                $this->get_merchant_account($payu_payment_result_row['currency']);

                $tranArray = array(

                    'language' => 'en',

                    'command' => 'ORDER_DETAIL',

                    'merchant' =>

                    array(

                        'apiKey' => $this->apiKey,

                        'apiLogin' => $this->apiLogin,

                    ),

                    'details' =>

                    array(

                        'orderId' => (int) $payu_payment_result_row['payu_order_id'],

                    ),

                    'test' => false,

                );

                $result = $this->curl_connect($this->query_url, $tranArray);

                if ($result === false) {

                    ob_start();

                    echo "========================PAYU CHECK TRANS STATUS CURL FAILED=========================<BR>";

                    echo "================================REQUEST DATA=============================<BR>";

                    print_r($request);

                    echo "============================================================================<BR>";

                    $debug_html = ob_get_contents();

                    ob_end_clean();

                    @tep_mail('PayU Report', '<EMAIL>', 'PAYU CHECK TRANS STATUS CURL ERROR', $debug_html, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);

                } else {
                    if (isset($result['code']) && $result['code'] == 'SUCCESS') {

                        $payUPaymentHistoryDataArray = array(

                            'order_id' => $order_id,

                            'payu_order_id' => $result['result']['payload']['id'],

                            'transaction_id' => $result['result']['payload']['transactions'][0]['id'],

                            'account_id' => $result['result']['payload']['accountId'],

                            'amount' => $result['result']['payload']['additionalValues']['TX_VALUE']['value'],

                            'currency' => $result['result']['payload']['additionalValues']['TX_VALUE']['currency'],

                            'code' => isset($result['code']) ? $result['code'] : '',

                            'state' => isset($result['result']['payload']['status']) ? $result['result']['payload']['status'] : '',

                            'response_code' => isset($result['result']['payload']['referenceCode']) ? $result['result']['payload']['referenceCode'] : '',

                            'payer_name' => isset($result['result']['payload']['transactions'][0]['creditCard']['name']) ? $result['result']['payload']['transactions'][0]['creditCard']['name'] : '',

                            'cc_number' => isset($result['result']['payload']['transactions'][0]['creditCard']['maskedNumber']) ? $result['result']['payload']['transactions'][0]['creditCard']['maskedNumber'] : '',

                            'operation_date' => isset($result['result']['payload']['transactions'][0]['transactionResponse']['operationDate']) ? $result['result']['payload']['transactions'][0]['transactionResponse']['operationDate'] : '',

                            'ip_address' => isset($result['result']['payload']['transactions'][0]['ipAddress']) ? $result['result']['payload']['transactions'][0]['ipAddress'] : '',

                            'data' => json_encode($result),

                            'last_modified' => date('Y-m-d H:i:s'),

                        );

                        $this->saveTransactionData($order_id, $payUPaymentHistoryDataArray);

                    } else if (isset($result['code']) && $result['code'] == 'ERROR') {

                        $payUPaymentHistoryDataArray = array(

                            'order_id' => $order_id,

                            'payu_order_id' => $payu_payment_result_row['payu_order_id'],

                            'transaction_id' => $payu_payment_result_row['transaction_id'],

                            'state' => $result['code'],

                            'response_code' => $result['error'],

                            'date' => date('Y-m-d H:i:s'),

                            'changed_by' => (isset($login_email_address) && !empty($login_email_address)) ? $login_email_address : 'System'

                        );

                        tep_db_perform(TABLE_PAYU_STATUS_HISTORY, $payUPaymentHistoryDataArray);

                    }

                }

            }

        }

        return (isset($result)) ? $result : array();

    }



    private function saveTransactionData($orderId, $data) {

        global $login_email_address;

        $payu_payment_select_sql = "	SELECT order_id

										FROM " . TABLE_PAYU . "

										WHERE order_id = '" . $orderId . "'";

        $payu_payment_result_sql = tep_db_query($payu_payment_select_sql);

        if (tep_db_num_rows($payu_payment_result_sql)) {

            tep_db_perform(TABLE_PAYU, $data, 'update', " order_id = '" . $orderId . "' ");

        } else {

            $data['order_id'] = $orderId;

            $data['date_added'] = date('Y-m-d H:i:s');

            tep_db_perform(TABLE_PAYU, $data);

        }

        $payUPaymentHistoryDataArray = array(

            'order_id' => $orderId,

            'payu_order_id' => $data['payu_order_id'],

            'transaction_id' => $data['transaction_id'],

            'state' => $data['state'],

            'response_code' => $data['response_code'],

            'date' => date('Y-m-d H:i:s'),

            'changed_by' => (isset($login_email_address) && !empty($login_email_address)) ? $login_email_address : 'System'

        );

        tep_db_perform(TABLE_PAYU_STATUS_HISTORY, $payUPaymentHistoryDataArray);

    }



    function curl_connect($url, $data) {

        $apiResult = '';

        $headers = array("Content-Type: application/json", "Accept: application/json");

        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, $url);

        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);

        curl_setopt($ch, CURLOPT_POST, 1);

        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);

        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));

        if ($this->connect_via_proxy)

            curl_setopt($ch, CURLOPT_PROXY, 'my-proxy.offgamers.lan:3128');



        $apiResult = curl_exec($ch);

        if (curl_errno($ch)) {

            @tep_mail('PayU Curl Report', '<EMAIL>', 'PayU Curl Error: ' . curl_errno($ch) . ' - ' . curl_error($ch), '', STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);

        } else {

            //closing the curl

            curl_close($ch);

        }



        return (!empty($apiResult)) ? json_decode($apiResult, 1) : array();

    }



    function get_merchant_account($selected_currency) {

        $payment_methods_instance_id = 0;

        $payment_gateway_instance_select_sql = "SELECT pmi.payment_methods_instance_id, pmi.payment_methods_instance_follow_default, pmi.currency_code 

				 								FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " as pmi

				 								WHERE pmi.payment_methods_id = '" . (int) $this->payment_methods_id . "'

				 								LIMIT 1";

        $payment_gateway_instance_result_sql = tep_db_query($payment_gateway_instance_select_sql);

        if (tep_db_num_rows($payment_gateway_instance_result_sql)) {

            $payment_gateway_instance_select_sql = "SELECT pmi.payment_methods_instance_id, pmi.payment_methods_instance_follow_default 

					 								FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " as pmi

					 								WHERE pmi.currency_code = '" . tep_db_input($selected_currency) . "' 

					 									AND pmi.payment_methods_id = '" . (int) $this->payment_methods_id . "'";

            $payment_gateway_instance_result_sql = tep_db_query($payment_gateway_instance_select_sql);

            if ($payment_gateway_instance_row = tep_db_fetch_array($payment_gateway_instance_result_sql)) {

                if ((int) $payment_gateway_instance_row['payment_methods_instance_follow_default'] > 0) { // Follow Default Currency Setting

                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_follow_default'];

                } else { // Has own setting

                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_id'];

                }

            }

        } else {

            $payment_gateway_instance_select_sql = "SELECT pmi.payment_methods_instance_id, pmi.payment_methods_instance_follow_default 

					 								FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " as pmi

					 								INNER JOIN " . TABLE_PAYMENT_METHODS . " as pm

					 									ON pm.payment_methods_parent_id = pmi.payment_methods_id

					 								WHERE pmi.currency_code = '" . tep_db_input($selected_currency) . "' 

					 									AND pm.payment_methods_id = '" . (int) $this->payment_methods_id . "'";

            $payment_gateway_instance_result_sql = tep_db_query($payment_gateway_instance_select_sql);

            if ($payment_gateway_instance_row = tep_db_fetch_array($payment_gateway_instance_result_sql)) {

                if ((int) $payment_gateway_instance_row['payment_methods_instance_follow_default'] > 0) {

                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_follow_default'];

                } else {

                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_id'];

                }

            }

        }



        $payment_gateway_instance_setting_select_sql = "SELECT pmis.payment_methods_instance_setting_key, pmis.payment_methods_instance_setting_value 

						 								FROM " . TABLE_PAYMENT_METHODS_INSTANCE_SETTING . " as pmis

						 								WHERE pmis.payment_methods_instance_id = '" . (int) $payment_methods_instance_id . "'";

        $payment_gateway_instance_setting_result_sql = tep_db_query($payment_gateway_instance_setting_select_sql);

        while ($payment_gateway_instance_setting_row = tep_db_fetch_array($payment_gateway_instance_setting_result_sql)) {

            switch ($payment_gateway_instance_setting_row['payment_methods_instance_setting_key']) {

                case 'MODULE_PAYMENT_PAYU_API_KEY':

                    $this->apiKey = $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];

                    break;

                case 'MODULE_PAYMENT_PAYU_API_LOGIN':

                    $this->apiLogin = $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];

                    break;

                case 'MODULE_PAYMENT_PAYU_ACCOUNT_ID':

                    $this->accountId = $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];

                    break;

                case 'MODULE_PAYMENT_PAYU_MERCHANT_ID':

                    $this->merchantId = $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];

                    break;

                case 'MODULE_PAYMENT_TAX':

                    $this->tax = $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];

                    break;

            }

        }

    }



    function load_pm_setting($language_id = 1, $pm_id = '') {

        $pm_setting_array = array();

        //load value from DB



        if (tep_not_null($pm_id)) {

            $payment_method_id = $pm_id;



            $payment_methods_parent_id_select_sql = "	SELECT payment_methods_parent_id 

														FROM " . TABLE_PAYMENT_METHODS . " 

														WHERE payment_methods_id = '" . (int) $pm_id . "'";

            $payment_methods_parent_id_result_sql = tep_db_query($payment_methods_parent_id_select_sql);

            $payment_methods_parent_id_row = tep_db_fetch_array($payment_methods_parent_id_result_sql);



            $payment_gateway_id = $payment_methods_parent_id_row['payment_methods_parent_id'];

        } else {

            $payment_method_id = $this->payment_methods_id;

            $payment_gateway_id = $this->payment_methods_parent_id;

        }



        $payment_configuration_info_select_sql = "	SELECT pci.payment_configuration_info_key, pcid.payment_configuration_info_value

													FROM " . TABLE_PAYMENT_CONFIGURATION_INFO . " as pci

													LEFT JOIN " . TABLE_PAYMENT_CONFIGURATION_INFO_DESCRIPTION . " as pcid 

														ON pci.payment_configuration_info_id = pcid.payment_configuration_info_id

															AND pcid.languages_id = '" . (int) $language_id . "'

													WHERE pci.payment_methods_id = '" . (int) $payment_method_id . "'

													ORDER BY pci.payment_configuration_info_sort_order";

        $payment_configuration_info_result_sql = tep_db_query($payment_configuration_info_select_sql);

        if (tep_db_num_rows($payment_configuration_info_result_sql)) {

            while ($payment_configuration_info_row = tep_db_fetch_array($payment_configuration_info_result_sql)) {

                $pm_setting_array[$payment_configuration_info_row['payment_configuration_info_key']] = $payment_configuration_info_row['payment_configuration_info_value'];

            }

        } else if ((int) $payment_gateway_id > 0) { //load value from PG

            $payment_configuration_info_select_sql = "	SELECT pci.payment_configuration_info_key, pcid.payment_configuration_info_value

														FROM " . TABLE_PAYMENT_CONFIGURATION_INFO . " as pci

														LEFT JOIN " . TABLE_PAYMENT_CONFIGURATION_INFO_DESCRIPTION . " as pcid 

															ON pci.payment_configuration_info_id = pcid.payment_configuration_info_id

																AND pcid.languages_id = '" . (int) $language_id . "'

														WHERE pci.payment_methods_id = '" . (int) $payment_gateway_id . "'

														ORDER BY pci.payment_configuration_info_sort_order";

            $payment_configuration_info_result_sql = tep_db_query($payment_configuration_info_select_sql);

            while ($payment_configuration_info_row = tep_db_fetch_array($payment_configuration_info_result_sql)) {

                $pm_setting_array[$payment_configuration_info_row['payment_configuration_info_key']] = $payment_configuration_info_row['payment_configuration_info_value'];

            }

        }



        return $pm_setting_array;

    }



    function check() {

        if (!isset($this->_check)) {

            $payment_methods_select_sql = "	SELECT payment_methods_id

											FROM " . TABLE_PAYMENT_METHODS . " as pm

											WHERE pm.payment_methods_id = '" . (int) $this->payment_methods_id . "'";

            $payment_methods_result_sql = tep_db_query($payment_methods_select_sql);

            $this->_check = tep_db_num_rows($payment_methods_result_sql);

        }

        return $this->_check;

    }



    function install() {

        $install_configuration_flag = true;

        if ($this->payment_methods_id > 0) {

            if ($this->receive_status != 1) {

                $update_receive_status_sql_data = array("payment_methods_receive_status" => 1);

                tep_db_perform(TABLE_PAYMENT_METHODS, $update_receive_status_sql_data, 'update', "payment_methods_id = '" . (int) $this->payment_methods_id . "'");

            } else {

                $install_configuration_flag = false;

            }

        } else {

            $install_payment_gateway_data_sql = array('payment_methods_code' => $this->code,

                'payment_methods_send_status' => 0,

                'payment_methods_title' => $this->code,

                'payment_methods_types_id' => 0,

                'payment_methods_parent_id' => 0,

                'payment_methods_legend_color' => '#4747A3',

                'payment_methods_sort_order' => 6,

                'payment_methods_filename' => $this->filename,

                'payment_methods_logo' => '',

                'date_added' => 'now()',

                'last_modified' => 'now()',

                'payment_methods_receive_status' => 1,

                'payment_methods_receive_status_mode' => 0

            );

            tep_db_perform(TABLE_PAYMENT_METHODS, $install_payment_gateway_data_sql);

            $this->payment_methods_id = tep_db_insert_id();

        }

        if ($install_configuration_flag) {

            $install_key_array = array();

            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,

                    'payment_configuration_info_title' => 'Set Order Status',

                    'payment_configuration_info_key' => 'MODULE_PAYMENT_PAYU_ORDER_STATUS_ID',

                    'payment_configuration_info_description' => 'Set the status of orders made with this payment module to this value.',

                    'payment_configuration_info_sort_order' => '1335',

                    'set_function' => 'tep_cfg_pull_down_order_statuses(',

                    'use_function' => 'tep_get_order_status_name',

                    'date_added' => 'now()'

                ),

                'desc' => array('payment_configuration_info_value' => '1',

                    'languages_id' => 1

                )

            );

            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,

                    'payment_configuration_info_title' => 'Set Order\'s "Processing" Status',

                    'payment_configuration_info_key' => 'MODULE_PAYMENT_PAYU_PROCESSING_STATUS_ID',

                    'payment_configuration_info_description' => 'Set the initial processing status of orders made with this payment module to this value.',

                    'payment_configuration_info_sort_order' => '1340',

                    'set_function' => 'tep_cfg_pull_down_order_statuses(',

                    'use_function' => 'tep_get_order_status_name',

                    'date_added' => 'now()'

                ),

                'desc' => array('payment_configuration_info_value' => '7',

                    'languages_id' => 1

                )

            );

            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,

                    'payment_configuration_info_title' => 'Payment Message',

                    'payment_configuration_info_key' => 'MODULE_PAYMENT_PAYU_MESSAGE',

                    'payment_configuration_info_description' => 'Payment message will show up during checkout process (smart2pay).',

                    'payment_configuration_info_sort_order' => '1350',

                    'set_function' => 'tep_cfg_textarea(',

                    'use_function' => '',

                    'date_added' => 'now()'

                ),

                'desc' => array('payment_configuration_info_value' => 'Thank you for shopping at OffGamers.com.',

                    'languages_id' => 1

                )

            );

            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,

                    'payment_configuration_info_title' => 'Payment Email Message',

                    'payment_configuration_info_key' => 'MODULE_PAYMENT_PAYU_EMAIL_MESSAGE',

                    'payment_configuration_info_description' => 'This message will show up in order confirmation e-mail.',

                    'payment_configuration_info_sort_order' => '1360',

                    'set_function' => 'tep_cfg_textarea(',

                    'use_function' => '',

                    'date_added' => 'now()'

                ),

                'desc' => array('payment_configuration_info_value' => 'Thank you for shopping at OffGamers.com.',

                    'languages_id' => 1

                )

            );

            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,

                    'payment_configuration_info_title' => 'Confirm Complete (in days)',

                    'payment_configuration_info_key' => 'MODULE_PAYMENT_PAYU_CONFIRM_COMPLETE',

                    'payment_configuration_info_description' => 'The number of days in which an order made by this payment method is confirm completed.',

                    'payment_configuration_info_sort_order' => '1265',

                    'set_function' => '',

                    'use_function' => '',

                    'date_added' => 'now()'

                ),

                'desc' => array('payment_configuration_info_value' => '0',

                    'languages_id' => 1

                )

            );

            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,

                    'payment_configuration_info_title' => 'Test Mode',

                    'payment_configuration_info_key' => 'MODULE_PAYMENT_PAYU_TEST_MODE',

                    'payment_configuration_info_description' => 'Testing Mode?',

                    'payment_configuration_info_sort_order' => '100',

                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',

                    'use_function' => '',

                    'date_added' => 'now()'

                ),

                'desc' => array('payment_configuration_info_value' => 'False',

                    'languages_id' => 1

                )

            );

            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,

                    'payment_configuration_info_title' => 'Require Address Information',

                    'payment_configuration_info_key' => 'MODULE_PAYMENT_PAYU_MANDATORY_ADDRESS_FIELD',

                    'payment_configuration_info_description' => 'Set address field as required info during checkout.',

                    'payment_configuration_info_sort_order' => '1400',

                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',

                    'use_function' => '',

                    'date_added' => 'now()'

                ),

                'desc' => array('payment_configuration_info_value' => 'False',

                    'languages_id' => 1

                )

            );

            foreach ($install_key_array as $data) {

                tep_db_perform(TABLE_PAYMENT_CONFIGURATION_INFO, $data['info']);

                $payment_conf_id = tep_db_insert_id();



                $data['desc']['payment_configuration_info_id'] = (int) $payment_conf_id;

                tep_db_perform(TABLE_PAYMENT_CONFIGURATION_INFO_DESCRIPTION, $data['desc']);

            }

        }

        return 1;

    }



    function merchant_information_keys() {

        include_once(DIR_WS_CLASSES . "payment_methods.php");



        return array(

            'MODULE_PAYMENT_PAYU_API_KEY' => array('field' => 'text', 'mapping' => 'MODULE_PAYMENT_PAYU_API_KEY'),

            'MODULE_PAYMENT_PAYU_API_LOGIN' => array('field' => 'text', 'mapping' => 'MODULE_PAYMENT_PAYU_API_LOGIN'),

            'MODULE_PAYMENT_PAYU_ACCOUNT_ID' => array('field' => 'text', 'mapping' => 'MODULE_PAYMENT_PAYU_ACCOUNT_ID'),

            'MODULE_PAYMENT_PAYU_MERCHANT_ID' => array('field' => 'text', 'mapping' => 'MODULE_PAYMENT_PAYU_MERCHANT_ID'),

            'MODULE_PAYMENT_TAX' => array("field" => 'select', 'mapping' => "MODULE_PAYMENT_TAX", "select" => payment_methods::get_tax_option())

        );

    }



    function get_orders_payment_info_file() {

        return DIR_FS_CATALOG_MODULES . 'payment/payU/admin/orders.inc.php';

    }



    function get_orders_list_payment_info_file() {

        return DIR_FS_CATALOG_MODULES . 'payment/payU/admin/orders_list.inc.php';

    }



}



?>