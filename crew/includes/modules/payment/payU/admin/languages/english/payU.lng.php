<?php



define('TABLE_HEADING_PAYU_DATE', 'History Date');

define('TABLE_HEADING_PAYU_CHANGED_BY', 'Changed By');



//Define Merchant Key Language

define('MODULE_PAYMENT_PAYU_API_KEY', 'API Key');

define('MODULE_PAYMENT_PAYU_API_LOGIN', 'API Login');

define('MODULE_PAYMENT_PAYU_ACCOUNT_ID', 'Account ID');

define('MODULE_PAYMENT_PAYU_MERCHANT_ID', 'Merchant ID');

define('MODULE_PAYMENT_TAX', 'Tax');

define('ENTRY_PAYU_PAYMENT_STATUS', 'Payment Status');

define('ENTRY_PAYU_TRANS_ID', 'Transaction Id');

define('ENTRY_PAYU_ORDER_ID', 'PayU Order Id');

define('ENTRY_PAYU_ACCOUNT_ID', 'Account Id');

define('ENTRY_PAYU_STATE', 'Status');

define('ENTRY_PAYU_CURRENCY', 'Payment Currency');

define('ENTRY_PAYU_AMOUNT', 'Payment Amount');

define('ENTRY_PAYU_RESPONSE_CODE', 'Response Code');

define('ENTRY_PAYU_OPERATION_DATE', 'Operation Date');

define('ENTRY_PAYU_DATE_ADDED', 'Added Date');

define('ENTRY_PAYU_MODIFICATION_DATE', 'Last Modified');

define('ENTRY_PAYU_ORDER_EXPIRATION', 'Order Expiration');

define('ENTRY_PAYU_CC_NO', 'Credit Card No');

define('ENTRY_PAYU_EXPIRY_DATE', 'Credit Card Expiry Date');

define('ENTRY_PAYU_PAYER_DNI', 'Payer Id');

define('ENTRY_PAYU_PAYER_NAME', 'Payer Name');

define('ENTRY_PAYU_PAYER_ADDRESS', 'Payer Address');

define('ENTRY_PAYU_PAYER_CITY', 'Payer City');

define('ENTRY_PAYU_PAYER_STATE', 'Payer State');

define('ENTRY_PAYU_PAYER_POSTCODE', 'Payer Postcode');

define('ENTRY_PAYU_PAYER_COUNTRY', 'Payer Country');

define('ENTRY_PAYU_CONTACT_PHONE', 'Payer Contact Phone');

define('ENTRY_PAYU_IP_ADDRESS', 'IP Address');

define('TEXT_STATUS_FAILED', 'Failed');

define('TEXT_STATUS_SUCCESS', 'Success');

define('TEXT_NOT_AVAILABLE', 'N/A');

?>