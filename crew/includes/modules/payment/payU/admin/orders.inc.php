<?php

include_once(DIR_FS_CATALOG_MODULES . 'payment/payU/admin/languages/' . $language . '/payU.lng.php');



$payU_trans_info_select_sql = "SELECT * FROM " . TABLE_PAYU . " WHERE order_id ='" . (int)$oID . "'";

$payU_trans_info_result_sql= tep_db_query($payU_trans_info_select_sql);



$payU_payment_status_history_info_select_sql = "SELECT * FROM " . TABLE_PAYU_STATUS_HISTORY . " WHERE order_id = '" . $oID . "'";

$payU_payment_status_history_info_result_sql = tep_db_query($payU_payment_status_history_info_select_sql);

?>

<table border="0" width="100%" cellspacing="0" cellpadding="2">

<?php ob_start(); 



?>

	<tr>

		<td class="main">

      		<table border="0" width="100%" cellspacing="0" cellpadding="2">

      			<tr>

      				<td class="main" width="12%" nowrap><b><?php echo ENTRY_PAYMENT_METHOD?></b></td>

      				<td class="main">

      					<div>

      						<?php if((int)$order->info['payment_methods_id']>0) { ?><div class="<?php echo ((int)$payment_info_array[$order->info['payment_methods_id']]->confirm_complete_days>0?'rp_icon':'nrp_icon')?>"><b><?php echo ((int)$payment_info_array[$order->info['payment_methods_id']]->confirm_complete_days>0?'RP':'NRP')?></b></div> <?php } ?>

      						<div style="width:30px; height:15px; background-color:<?php echo $payment_module_info['display_colour']?>; float:left; vertical-align:top; text-align:center;">&nbsp;</div>

      						<div style="vertical-align: top">

      							&nbsp;&nbsp;

<?php

	if ($view_payment_details_permission) {

		if (tep_not_null($last_payment_method) && $last_payment_method['payment_methods_id'] != $order->info['payment_methods_id']) {

			echo '<span class="redIndicator">'.(tep_not_null($payment_module_info['payment_methods_parent_title'])?$payment_module_info['payment_methods_parent_title'] . ' - ' :'') . $payment_module_info['payment_methods_title'].'</span>';

		} else {

			echo (tep_not_null($payment_module_info['payment_methods_parent_title'])?$payment_module_info['payment_methods_parent_title'] . ' - ' :'') . $payment_module_info['payment_methods_title'];

		}

		

		if (tep_not_null($last_payment_method) && $last_payment_method['payment_methods_id'] != $order->info['payment_methods_id']) {

			echo '&nbsp;&nbsp;<span class="redIndicator">(Last Payment Method: ' . (tep_not_null($last_payment_method['parent_payment_methods_title'])? $last_payment_method['parent_payment_methods_title'] . ' - ' : '' ) . $last_payment_method['payment_methods_title'] . ')</span>';

		}

	} else {

		echo (tep_not_null($payment_module_info['payment_methods_parent_title'])?$payment_module_info['payment_methods_parent_title'] . ' - ' :'') . $payment_module_info['payment_methods_title'];

	}

?>

							</div>

      					</div>

      				</td>

				</tr>

			</table>

		</td>

	</tr>

<?php

$payment_method_title_info = ob_get_contents();

ob_end_clean();



if (tep_db_num_rows($payU_trans_info_result_sql) || tep_db_num_rows($payU_payment_status_history_info_result_sql)) {

	$payU_trans_info_row = tep_db_fetch_array($payU_trans_info_result_sql);



	echo $payment_method_title_info;

	

	if (!$view_payment_details_permission) {

		;

	} else {

?>

  	<tr>

    	<td class="main">

      		<table border="0" width="100%" cellspacing="0" cellpadding="2">

      			<tr valign="top">

        			<td width="39%" nowrap>

              			<table border="0" cellspacing="0" cellpadding="2">

              				<tr>

                				<td class="main" valign="top" nowrap><b><?php echo ENTRY_PAYU_TRANS_ID?></b>&nbsp;</td>

                				<td class="main" nowrap><?php echo $payU_trans_info_row['transaction_id'] ?></td>

              				</tr>

             				<tr>

                				<td class="main" valign="top" nowrap><b><?php echo ENTRY_PAYU_ORDER_ID?></b>&nbsp;</td>

                				<td class="main" nowrap><?php echo $payU_trans_info_row['payu_order_id']?></td>

              				</tr>

              				<tr>

                				<td class="main" valign="top" nowrap><b><?php echo ENTRY_PAYU_ACCOUNT_ID?></b>&nbsp;</td>

                				<td class="main" nowrap><?php echo $payU_trans_info_row['account_id']?></td>

              				</tr>

              				<tr>

                				<td class="main" valign="top" nowrap><b><?php echo ENTRY_PAYU_STATE?></b>&nbsp;</td>

                				<td class="main" nowrap><?php echo $payU_trans_info_row['state']?></td>

              				</tr>

              				<tr>

                				<td class="main" valign="top" nowrap><b><?php echo ENTRY_PAYU_RESPONSE_CODE?></b>&nbsp;</td>

                				<td class="main" nowrap><?php echo $payU_trans_info_row['response_code']?></td>

              				</tr>

              				<tr>

                				<td class="main" valign="top" nowrap><b><?php echo ENTRY_PAYU_OPERATION_DATE?></b>&nbsp;</td>

                				<td class="main" nowrap><?php echo ((isset($payU_trans_info_row['operation_date']) && !empty($payU_trans_info_row['operation_date'])) ? date('Y-m-d H:i:s', $payU_trans_info_row['operation_date']/1000) : '')?></td>

              				</tr>

              				<tr>

                				<td class="main" valign="top" nowrap><b><?php echo ENTRY_PAYU_DATE_ADDED?></b>&nbsp;</td>

                				<td class="main" nowrap><?php echo $payU_trans_info_row['date_added']?></td>

              				</tr>

              				<tr>

                				<td class="main" valign="top" nowrap><b><?php echo ENTRY_PAYU_MODIFICATION_DATE?></b>&nbsp;</td>

                				<td class="main" nowrap><?php echo $payU_trans_info_row['last_modified']?></td>

              				</tr>

              				<tr>

                				<td class="main" valign="top" nowrap><b><?php echo ENTRY_PAYU_ORDER_EXPIRATION?></b>&nbsp;</td>

                				<td class="main" nowrap><?php echo $payU_trans_info_row['order_expiration']?></td>

              				</tr>

							<tr>

                				<td class="main" valign="top" nowrap><b><?php echo ENTRY_PAYU_PAYMENT_STATUS?></b>&nbsp;</td>

                				<td class="main" nowrap>

                				<?php

                					if (tep_not_null($payU_trans_info_row['code']) && $payU_trans_info_row['code']=='SUCCESS') {

                						echo TEXT_STATUS_SUCCESS;

                					} else {

                						echo TEXT_STATUS_FAILED;

                					}

								?>

                				</td>

							</tr>

							<tr>

								<td class="main" nowrap>

	              				<?php               					

                					if ($order->info['orders_status'] == '1' || $order->info['orders_status'] == '7' || $order->info['orders_status'] == '2') {

	                					echo "&nbsp;&nbsp;";

										echo tep_draw_form('payU_status_check_form', FILENAME_ORDERS, tep_get_all_get_params(), 'post');

										echo tep_draw_hidden_field('subaction', 'payment_action');

										echo tep_draw_hidden_field('payment_action', 'check_trans_status');

										echo tep_submit_button('Check Payment Status', 'Check Payment Status', 'name="payUCheckTransStatusBtn"', 'inputButton');

										echo "</form>";

									}

								?>

								</td>

							</tr>

              			</table>

        			</td>

        			<td>

        				<table border="0" cellspacing="0" cellpadding="1">

              				</tr>

        					<tr>

        						<td colspan="2">

									<table border="1" cellspacing="0" cellpadding="2">

		        						<tr>

		        							<td class="smallText" nowrap><b><?php echo TABLE_HEADING_PAYU_DATE?></b></td>

											<td class="smallText" nowrap><b><?php echo ENTRY_PAYU_STATE?></b></td>

		        							<td class="smallText" nowrap><b><?php echo ENTRY_PAYU_RESPONSE_CODE?></b></td>

		        							<td class="smallText" nowrap><b><?php echo TABLE_HEADING_PAYU_CHANGED_BY?></b></td>

		        						</tr>

<?php		while ($payu_payment_status_history_info_row = tep_db_fetch_array($payU_payment_status_history_info_result_sql)) {

        	echo ' 						<tr>

                							<td class="smallText" nowrap>'.$payu_payment_status_history_info_row['date'].'</td>

                							<td class="smallText" nowrap>'.$payu_payment_status_history_info_row['state'].'</td>

                							<td class="smallText" nowrap>'.$payu_payment_status_history_info_row['response_code'].'</td>

                							<td class="smallText" nowrap>'.(tep_not_null($payu_payment_status_history_info_row['changed_by'])?$payu_payment_status_history_info_row['changed_by']:'&nbsp;').'</td>

                						</tr>';

     	}

?>

									</table>

								</td>

        					</tr>

							<tr>

              					<td>

              						<table border="1" cellspacing="0" cellpadding="2">

										<tr>

											<td class="main" valign="top" nowrap><b><?php echo ENTRY_PAYU_CC_NO?></b>&nbsp;</td>

											<td class="main" nowrap>

<?php

											if (tep_not_null($payU_trans_info_row['cc_number'])) {

												$card_number = $payU_trans_info_row['cc_number'];

												echo $card_number;



												$check_credit_card_array = array();

												$check_credit_card_select_sql = "	SELECT o.customers_id

																					FROM " . TABLE_ORDERS . " AS o 

																					INNER JOIN " . TABLE_PAYU . " AS p 

																						ON o.orders_id = p.order_id

																					WHERE p.cc_number = '" . tep_db_input($payU_trans_info_row['cc_number']) . "'

																					GROUP BY o.customers_id";

												$check_credit_card_result_sql = tep_db_query($check_credit_card_select_sql);

												while ($check_credit_card_row = tep_db_fetch_array($check_credit_card_result_sql)) {

													$check_credit_card_array[] = $check_credit_card_row['customers_id'];

												}

												if (count($check_credit_card_array)>1) {

													echo 	tep_draw_form('cust_lists_share_cc', FILENAME_CUSTOMERS, 'action=show_report&customer_lists_subaction=do_search', 'post', 'target="_blank"') . "\n" . 

													tep_draw_hidden_field('customer_share_id', tep_array_serialize($check_credit_card_array)) . "\n" . 

															"<BR><span class='redIndicator'>Same credit card used in <a href='javascript:document.cust_lists_share_cc.submit();'>" . count($check_credit_card_array) . "</a> profiles.</span>" . 

															'</form>' . "\n";

												}

												$cc_check_info = array(	'card_number' => $card_number,

																		'date_purchased' => $order->info['date_purchased'],

																		'orders_id' => $order->order_id,

																		'customers_id' => $order->customer['id']);

												$cc_verified_date = tep_get_payment_info_verified_date($cc_check_info, 'credit_card');

												echo "<br><span class='redIndicator'>First successful verified date by this user: ".(tep_not_null($cc_verified_date)?$cc_verified_date:"NEVER")."</span><br>(Data can only traced back to 2009-12-09)</span>";

											} else {

												echo "N/A";

											}

?>

											</td>

										</tr>

										<tr>

											<td class="main" valign="top" nowrap><b><?php echo ENTRY_PAYU_EXPIRY_DATE?></b>&nbsp;</td>

											<td class="main" nowrap><?php echo $payU_trans_info_row['cc_expiration_date']?></td>

										</tr>

										<tr>

											<td class="main" valign="top" nowrap><b><?php echo ENTRY_PAYU_PAYER_DNI?></b>&nbsp;</td>

											<td class="main" nowrap><?php echo $payU_trans_info_row['buyer_dni']?></td>

										</tr>

										<tr>

											<td class="main" valign="top" nowrap><b><?php echo ENTRY_PAYU_PAYER_NAME?></b>&nbsp;</td>

											<td class="main" nowrap><?php echo $payU_trans_info_row['payer_name']?></td>

										</tr>

										<tr>

											<td class="main" valign="top" nowrap><b><?php echo ENTRY_PAYU_PAYER_ADDRESS?></b>&nbsp;</td>

											<td class="main" nowrap><?php echo $payU_trans_info_row['payer_address']?></td>

										</tr>

										<tr>

											<td class="main" valign="top" nowrap><b><?php echo ENTRY_PAYU_PAYER_CITY?></b>&nbsp;</td>

											<td class="main" nowrap><?php echo $payU_trans_info_row['payer_city']?></td>

										</tr>

										<tr>

											<td class="main" valign="top" nowrap><b><?php echo ENTRY_PAYU_PAYER_STATE?></b>&nbsp;</td>

											<td class="main" nowrap><?php echo $payU_trans_info_row['payer_state']?></td>

										</tr>

										<tr>

											<td class="main" valign="top" nowrap><b><?php echo ENTRY_PAYU_PAYER_POSTCODE?></b>&nbsp;</td>

											<td class="main" nowrap><?php echo $payU_trans_info_row['payer_postcode']?></td>

										</tr>

										<tr>

											<td class="main" valign="top" nowrap><b><?php echo ENTRY_PAYU_PAYER_COUNTRY?></b>&nbsp;</td>

											<td class="main" nowrap><?php echo $payU_trans_info_row['payer_country']?></td>

										</tr>

										<tr>

											<td class="main" valign="top" nowrap><b><?php echo ENTRY_PAYU_CONTACT_PHONE?></b>&nbsp;</td>

											<td class="main" nowrap><?php echo $payU_trans_info_row['payer_contact_phone']?></td>

										</tr>

										<tr>

											<td class="main" valign="top" nowrap><b><?php echo ENTRY_PAYU_IP_ADDRESS?></b>&nbsp;</td>

											<td class="main" nowrap><?php echo $payU_trans_info_row['ip_address']?></td>

										</tr>

              						</table>

              					</td>

              				</tr>

        				</table>

        			</td>

        			<td>

        				<table border="0" cellspacing="0" cellpadding="2">

        					<tr>

                				<td class="main" valign="top" nowrap><b><?php echo ENTRY_PAYU_CURRENCY?></b>&nbsp;</td>

                				<td class="main" nowrap>

                				<?php

                					if (strtolower($payU_trans_info_row['currency']) == strtolower($order->info['currency'])) {

                						echo $payU_trans_info_row['currency'];

                					} else {

                						echo '<span class="redIndicator">'.$payU_trans_info_row['currency'].'<br><span class="smallText">Does not match purchase currency.</span></span>';

                					}

                				?>

                				</td>

              				</tr>

              				<tr>

                				<td class="main" valign="top" nowrap><b><?php echo ENTRY_PAYU_AMOUNT?></b>&nbsp;</td>

                				<td class="main">

                				<?php

			

									$payUGrossAmt = number_format($payU_trans_info_row['amount'], $currencies->currencies[$payU_trans_info_row['currency']]['decimal_places'], $currencies->currencies[$payU_trans_info_row['currency']]['decimal_point'], $currencies->currencies[$payU_trans_info_row['currency']]['thousands_point']);

									$payUAmountFormatted = $currencies->currencies[$payU_trans_info_row['currency']]['symbol_left'] . $payUGrossAmt . $currencies->currencies[$payU_trans_info_row['currency']]['symbol_right'];



									if ($payUAmountFormatted != strip_tags($order->order_totals['ot_total']['text']) ) {

                						$mc_gross_display_text = '<span class="redIndicator">'.$payUAmountFormatted.'<br><span class="smallText">Does not match purchase amount.</span></span>';

                					} else {

										$mc_gross_display_text = $payUAmountFormatted;

									}

                					

                					echo $mc_gross_display_text;

                				?>

                				</td>

              				</tr>

              				<tr>

              					<td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10')?></td>

              				</tr>

              			</table>

        			</td>

      			</tr>

      			<tr>

    				<td class="main" nowrap colspan="2" valign="top">

    					<table border="0" width="100%" cellspacing="0" cellpadding="2">

    						<tr>

            				<?php	//only take care of showing Post-Captured button if it is in Verifying and was Pre-Captured

            					if ($order->info['orders_status'] == '7' && ($payU_trans_info_row['state'] == 'AUTHORIZED' || $payU_trans_info_row['state'] == 'IN_PROGRESS')) {

            						$button_displayed = false;

            						

            						$manual_authorise_permission = tep_admin_files_actions(FILENAME_ORDERS, 'ADM_WORLDPAY_MANUAL_AUTHORISATION');

            						echo '	<td width="140px" class="main">';

            						if ($manual_authorise_permission) {

                						echo tep_draw_form('manual_post_auth_order', FILENAME_ORDERS, tep_get_all_get_params(), 'post');

                						echo tep_draw_hidden_field('subaction', 'manual_post_authorisation');

                						echo '<input type="submit" name="ManualPostCaptureBtn" value="Manual Captured" title="Post Captured Manually" class="inputButton" onClick="if (confirm(\'Are you sure that you had post captured this order via the PayU Merchant Interface?\')) { openNewWin(\'https://secure.payulatam.com/login.zul\', \'PayU Admin\', \'location=1,status=1,menubar=1,resizable=1,directories=1,width=750,height=400\'); this.disabled=true;this.value=\'Please wait...\';this.form.submit(); } else { return false; }">';

                						echo '</form>';

                						$button_displayed = true;

                					}

            						echo '	</td>

											<td class="main">';

            						

        							$post_auth_btn_text = 'Post-Captured';

        							

        							if ($payU_trans_info_row['capture_request'] == 1) {

        								$post_auth_btn_text .= ' (Awaiting for response)';

        							}

        							

            						echo tep_draw_form('post_capture_order', FILENAME_ORDERS, tep_get_all_get_params(), 'post');

									echo tep_draw_hidden_field('subaction', 'post_authorisation');

									echo '<input type="submit" name="PostCaptureBtn" value="'.$post_auth_btn_text.'" title="Post Authorise" class="inputButton" onClick="if (confirm(\'Are you sure to post captured this order remotely?\')) { this.disabled=true;this.value=\'Please wait...\';this.form.submit(); } else { return false; }">';

									echo "</form>";

									$button_displayed = true;

            						

            						echo '</td>';

								}

            				?>

    						</tr>

    					</table>

    				</td>

  				</tr>

      		</table>

   		</td>

  	</tr>

<?php

	}

} else {

	echo $payment_method_title_info;

?>

	<tr>

		<td class="main" nowrap>

		<?

			if ($order->info['orders_status'] == '1' || $order->info['orders_status'] == '7' || $order->info['orders_status'] == '2') {

				echo "&nbsp;&nbsp;";

				echo tep_draw_form('payU_status_check_form', FILENAME_ORDERS, tep_get_all_get_params(), 'post');

				echo tep_draw_hidden_field('subaction', 'payment_action');

				echo tep_draw_hidden_field('payment_action', 'check_trans_status');

				echo tep_submit_button('Check Payment Status', 'Check Payment Status', 'name=payUCheckTransStatusBtn"', 'inputButton');

				echo "</form>";

			}

		?>

		</td>

	</tr>

<?php

}

?>

	

</table>