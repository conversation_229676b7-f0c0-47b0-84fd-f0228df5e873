<?

include_once(DIR_FS_CATALOG_MODULES . 'payment/payU/admin/languages/' . $language . '/payU.lng.php');



$payU_trans_info_select_sql = "SELECT * FROM " . TABLE_PAYU . " WHERE order_id = '" . $order_obj->orders_id . "'";

$payU_trans_info_result_sql = tep_db_query($payU_trans_info_select_sql);

?>

<table border="0" width="100%" cellspacing="0" cellpadding="2">

	<tr>

		<td class="subInvoiceBoxHeading">

			<div style="border-bottom: 1px solid #996600; border-top: 1px solid #996600; padding: 2px;">Payment Details</div>

		</td>

	</tr>

	<?

	if ($payU_trans_info_row = tep_db_fetch_array($payU_trans_info_result_sql)) {

		?>

		<tr>

			<td class="main">

				<table border="0" width="100%" cellspacing="0" cellpadding="2">

					<tr valign="top">

						<td width="50%" valign="top">

							<table border="0" cellspacing="0" cellpadding="2">

								

								<tr>

									<td class="invoiceRecords" valign="top" nowrap><?= ENTRY_PAYU_CURRENCY ?></td>

									<td class="invoiceRecords" valign="top">:&nbsp;</td>

									<td class="invoiceRecords" nowrap><?= $payU_trans_info_row['currency'] ?></td>

								</tr>

								<tr>

									<td class="invoiceRecords" valign="top" nowrap><?= ENTRY_PAYU_AMOUNT ?></td>

									<td class="invoiceRecords" valign="top">:&nbsp;</td>

									<td class="invoiceRecords">

										<?php

										$payUGrossAmt = number_format($payU_trans_info_row['amount'], $currencies->currencies[$payU_trans_info_row['currency']]['decimal_places'], $currencies->currencies[$payU_trans_info_row['currency']]['decimal_point'], $currencies->currencies[$payU_trans_info_row['currency']]['thousands_point']);

										$payUAmountFormatted = $currencies->currencies[$payU_trans_info_row['currency']]['symbol_left'] . $payUGrossAmt . $currencies->currencies[$payU_trans_info_row['currency']]['symbol_right'];



										echo $payUAmountFormatted;

										?>



									</td>

								</tr>

								<tr>

									<td class="invoiceRecords" valign="top" nowrap><?= ENTRY_PAYU_STATE ?></td>

									<td class="invoiceRecords" valign="top">:&nbsp;</td>

									<td class="invoiceRecords"><?= (tep_not_null($payU_trans_info_row['state']) ? $payU_trans_info_row['state'] : TEXT_NOT_AVAILABLE) ?></td>

								</tr>

							</table>

						</td>

						<td valign="top">

							<table border="0" cellspacing="0" cellpadding="2">

								<tr>

									<td class="invoiceRecords" valign="top" nowrap><?= ENTRY_PAYU_RESPONSE_CODE ?></td>

									<td class="invoiceRecords" valign="top">:&nbsp;</td>

									<td class="invoiceRecords"><?= (tep_not_null($payU_trans_info_row['response_code']) ? $payU_trans_info_row['response_code'] : TEXT_NOT_AVAILABLE) ?></td>

								</tr>

								<tr>

									<td class="invoiceRecords" valign="top" nowrap><?= ENTRY_PAYU_TRANS_ID ?></td>

									<td class="invoiceRecords" valign="top">:&nbsp;</td>

									<td class="invoiceRecords"><?= (tep_not_null($payU_trans_info_row['transaction_id']) ? $payU_trans_info_row['adyen_cc_fraud_score'] : TEXT_NOT_AVAILABLE) ?></td>

								</tr>

							</table>

						</td>

					</tr>

				</table>

			</td>

		</tr>

		<?

	} else {

		?>

		<tr>

			<td class="invoiceRecords">No further payment information is available.</td>

		</tr>

		<?

	}

	?>