<?php
define('TABLE_HEADING_PAYMASTER24_DATE', 'History Date');
define('ENTRY_PAYMASTER24_STATUS', 'Status');
define('ENTRY_PAYMASTER24_DESCRIPTION', 'Description');
define('TABLE_HEADING_PAYMASTER24_CHANGED_BY', 'Changed By');
//Define Merchant Key Language
define('MODULE_PAYMENT_PAYMASTER24_MERCHANT_KEY', 'Merchant ID');
define('MODULE_PAYMENT_PAYMASTER24_SECRET_KEY', 'Merchant Secret Key');
define('MODULE_PAYMENT_PAYMASTER24_LOGIN', 'API Username');
define('MODULE_PAYMENT_PAYMASTER24_PASSWORD', 'API Password');
define('MODULE_PAYMENT_TAX', 'Tax');

define('ENTRY_PAYMASTER24_PAYMENT_ID', 'Payment Id');
define('ENTRY_PAYMASTER24_PAYMENT_DATE', 'Payment Date');
define('ENTRY_PAYMASTER24_AMOUNT', 'Amount');
define('ENTRY_PAYMASTER24_CURRENCY', 'Currency');
define('ENTRY_PAYMASTER24_PAID_AMOUNT', 'Paid Amount');
define('ENTRY_PAYMASTER24_PAID_CURRENCY', 'Paid Currency');
define('ENTRY_PAYMASTER24_PAYMENT_SYSTEM', 'Payment System');
define('ENTRY_PAYMASTER24_PAYER_IDENTIFIER', 'Payer Identifier');
define('ENTRY_PAYMASTER24_PAYER_COUNTRY', 'Payer Country');
define('ENTRY_PAYMASTER24_PAYER_IP', 'Payer IP');
define('ENTRY_PAYMASTER24_CREATED_DATE', 'Created Date');
define('ENTRY_PAYMASTER24_LAST_MODIFIED', 'Last Modified');

?>