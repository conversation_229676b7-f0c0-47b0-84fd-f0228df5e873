<?php
include_once(DIR_FS_CATALOG_MODULES . 'payment/paymaster24/admin/languages/' . $language . '/paymaster24.lng.php');
$paymaster24_trans_info_select_sql = "SELECT * FROM " . TABLE_PAYMASTER24 . " WHERE order_id ='" . (int)$oID . "'";
$paymaster24_trans_info_result_sql= tep_db_query($paymaster24_trans_info_select_sql);
$paymaster24_payment_status_history_info_select_sql = "SELECT * FROM " . TABLE_PAYMASTER24_STATUS_HISTORY . " WHERE order_id = '" . $oID . "'";
$paymaster24_payment_status_history_info_result_sql = tep_db_query($paymaster24_payment_status_history_info_select_sql);
?>
<table border="0" width="100%" cellspacing="0" cellpadding="2">
	<?php ob_start();
	?>
	<tr>
		<td class="main">
			<table border="0" width="100%" cellspacing="0" cellpadding="2">
				<tr>
					<td class="main" width="12%" nowrap><b><?php echo ENTRY_PAYMENT_METHOD?></b></td>
					<td class="main">
						<div>
							<?php if((int)$order->info['payment_methods_id']>0) { ?><div class="<?php echo ((int)$payment_info_array[$order->info['payment_methods_id']]->confirm_complete_days>0?'rp_icon':'nrp_icon')?>"><b><?php echo ((int)$payment_info_array[$order->info['payment_methods_id']]->confirm_complete_days>0?'RP':'NRP')?></b></div> <?php } ?>
							<div style="width:30px; height:15px; background-color:<?php echo $payment_module_info['display_colour']?>; float:left; vertical-align:top; text-align:center;">&nbsp;</div>
							<div style="vertical-align: top">
								&nbsp;&nbsp;
								<?php
								if ($view_payment_details_permission) {
									if (tep_not_null($last_payment_method) && $last_payment_method['payment_methods_id'] != $order->info['payment_methods_id']) {
										echo '<span class="redIndicator">'.(tep_not_null($payment_module_info['payment_methods_parent_title'])?$payment_module_info['payment_methods_parent_title'] . ' - ' :'') . $payment_module_info['payment_methods_title'].'</span>';
									} else {
										echo (tep_not_null($payment_module_info['payment_methods_parent_title'])?$payment_module_info['payment_methods_parent_title'] . ' - ' :'') . $payment_module_info['payment_methods_title'];
									}

									if (tep_not_null($last_payment_method) && $last_payment_method['payment_methods_id'] != $order->info['payment_methods_id']) {
										echo '&nbsp;&nbsp;<span class="redIndicator">(Last Payment Method: ' . (tep_not_null($last_payment_method['parent_payment_methods_title'])? $last_payment_method['parent_payment_methods_title'] . ' - ' : '' ) . $last_payment_method['payment_methods_title'] . ')</span>';
									}
								} else {
									echo (tep_not_null($payment_module_info['payment_methods_parent_title'])?$payment_module_info['payment_methods_parent_title'] . ' - ' :'') . $payment_module_info['payment_methods_title'];
								}
								?>
							</div>
						</div>
					</td>
				</tr>
			</table>
		</td>
	</tr>
	<?php
	$payment_method_title_info = ob_get_contents();
	ob_end_clean();
	if (tep_db_num_rows($paymaster24_trans_info_result_sql) || tep_db_num_rows($paymaster24_payment_status_history_info_result_sql)) {
		$paymaster24_trans_info_row = tep_db_fetch_array($paymaster24_trans_info_result_sql);
		echo $payment_method_title_info;

		if (!$view_payment_details_permission) {
			;
		} else {
			?>
			<tr>
				<td class="main">
					<table border="0" width="100%" cellspacing="0" cellpadding="2">
						<tr valign="top">
							<td width="39%" nowrap>
								<table border="0" cellspacing="0" cellpadding="2">
									<tr>
										<td class="main" valign="top" nowrap><b><?php echo ENTRY_PAYMASTER24_PAYMENT_ID?></b>&nbsp;</td>
										<td class="main" nowrap><?php echo $paymaster24_trans_info_row['payment_id'] ?></td>
									</tr>
									<tr>
										<td class="main" valign="top" nowrap><b><?php echo ENTRY_PAYMASTER24_PAYMENT_DATE?></b>&nbsp;</td>
										<td class="main" nowrap><?php echo $paymaster24_trans_info_row['payment_date']?></td>
									</tr>
									<tr>
										<td class="main" valign="top" nowrap><b><?php echo ENTRY_PAYMASTER24_AMOUNT?></b>&nbsp;</td>
										<td class="main" nowrap><?php echo $paymaster24_trans_info_row['amount']?></td>
									</tr>
									<tr>
										<td class="main" valign="top" nowrap><b><?php echo ENTRY_PAYMASTER24_CURRENCY?></b>&nbsp;</td>
										<td class="main" nowrap><?php echo $paymaster24_trans_info_row['currency']?></td>
									</tr>
									<tr>
										<td class="main" valign="top" nowrap><b><?php echo ENTRY_PAYMASTER24_PAID_AMOUNT?></b>&nbsp;</td>
										<td class="main" nowrap><?php echo $paymaster24_trans_info_row['paid_amount']?></td>
									</tr>
									<tr>
										<td class="main" valign="top" nowrap><b><?php echo ENTRY_PAYMASTER24_PAID_CURRENCY?></b>&nbsp;</td>
										<td class="main" nowrap><?php echo $paymaster24_trans_info_row['paid_currency']?></td>
									</tr>
									<tr>
										<td class="main" valign="top" nowrap><b><?php echo ENTRY_PAYMASTER24_PAYMENT_SYSTEM?></b>&nbsp;</td>
										<td class="main" nowrap><?php echo $paymaster24_trans_info_row['payment_system']?></td>
									</tr>
									<tr>
										<td class="main" valign="top" nowrap><b><?php echo ENTRY_PAYMASTER24_PAYER_IDENTIFIER?></b>&nbsp;</td>
										<td class="main" nowrap><?php echo $paymaster24_trans_info_row['payer_identifier']?></td>
									</tr>
									<tr>
										<td class="main" valign="top" nowrap><b><?php echo ENTRY_PAYMASTER24_PAYER_COUNTRY?></b>&nbsp;</td>
										<td class="main" nowrap><?php echo $paymaster24_trans_info_row['payer_country']?></td>
									</tr>
									<tr>
										<td class="main" valign="top" nowrap><b><?php echo ENTRY_PAYMASTER24_PAYER_IP?></b>&nbsp;</td>
										<td class="main" nowrap><?php echo $paymaster24_trans_info_row['payer_ip']?></td>
									</tr>
									<tr>
										<td class="main" valign="top" nowrap><b><?php echo ENTRY_PAYMASTER24_CREATED_DATE?></b>&nbsp;</td>
										<td class="main" nowrap><?php echo $paymaster24_trans_info_row['date_added']?></td>
									</tr>
									<tr>
										<td class="main" valign="top" nowrap><b><?php echo ENTRY_PAYMASTER24_LAST_MODIFIED?></b>&nbsp;</td>
										<td class="main" nowrap><?php echo $paymaster24_trans_info_row['date_modified']?></td>
									</tr>
									<tr>
										<td class="main" nowrap>
											<?php
											if ($order->info['orders_status'] == '1' || $order->info['orders_status'] == '7' || $order->info['orders_status'] == '2') {
												echo "&nbsp;&nbsp;";
												echo tep_draw_form('paymaster24_status_check_form', FILENAME_ORDERS, tep_get_all_get_params(), 'post');
												echo tep_draw_hidden_field('subaction', 'payment_action');
												echo tep_draw_hidden_field('payment_action', 'check_trans_status');
												echo tep_submit_button('Check Payment Status', 'Check Payment Status', 'name="paymaster24CheckTransStatusBtn"', 'inputButton');
												echo "</form>";
											}
											?>
										</td>
									</tr>
								</table>
							</td>
							<td>
								<table border="0" cellspacing="0" cellpadding="1">
									</tr>
									<tr>
										<td colspan="2">
											<table border="1" cellspacing="0" cellpadding="2">
												<tr>
													<td class="smallText" nowrap><b><?php echo TABLE_HEADING_PAYMASTER24_DATE?></b></td>
													<td class="smallText" nowrap><b><?php echo ENTRY_PAYMASTER24_STATUS?></b></td>
													<td class="smallText" nowrap><b><?php echo ENTRY_PAYMASTER24_DESCRIPTION?></b></td>
													<td class="smallText" nowrap><b><?php echo TABLE_HEADING_PAYMASTER24_CHANGED_BY?></b></td>
												</tr>
												<?php		while ($paymaster24_payment_status_history_info_row = tep_db_fetch_array($paymaster24_payment_status_history_info_result_sql)) {
													echo ' 						<tr>
                							<td class="smallText" nowrap>'.$paymaster24_payment_status_history_info_row['date'].'</td>
                							<td class="smallText" nowrap>'.$paymaster24_payment_status_history_info_row['status'].'</td>
                							<td class="smallText" nowrap>'.$paymaster24_payment_status_history_info_row['reason'].'</td>
                							<td class="smallText" nowrap>'.(tep_not_null($paymaster24_payment_status_history_info_row['changed_by'])?$paymaster24_payment_status_history_info_row['changed_by']:'&nbsp;').'</td>
                						</tr>';
												}
												?>
											</table>
										</td>
									</tr>
								</table>
							</td>
							<td>
								<table border="0" cellspacing="0" cellpadding="2">
									<tr>
										<td class="main" valign="top" nowrap><b><?php echo ENTRY_PAYMASTER24_CURRENCY?></b>&nbsp;</td>
										<td class="main" nowrap>
											<?php
											if (strtolower($paymaster24_trans_info_row['currency']) == strtolower($order->info['currency'])) {
												echo $paymaster24_trans_info_row['currency'];
											} else {
												echo '<span class="redIndicator">'.$paymaster24_trans_info_row['currency'].'<br><span class="smallText">Does not match purchase currency.</span></span>';
											}
											?>
										</td>
									</tr>
									<tr>
										<td class="main" valign="top" nowrap><b><?php echo ENTRY_PAYMASTER24_PRICE?></b>&nbsp;</td>
										<td class="main">
											<?php

											$paymaster24GrossAmt = number_format($paymaster24_trans_info_row['amount'], $currencies->currencies[$paymaster24_trans_info_row['currency']]['decimal_places'], $currencies->currencies[$paymaster24_trans_info_row['currency']]['decimal_point'], $currencies->currencies[$paymaster24_trans_info_row['currency']]['thousands_point']);
											$paymaster24AmountFormatted = $currencies->currencies[$paymaster24_trans_info_row['currency']]['symbol_left'] . $paymaster24GrossAmt . $currencies->currencies[$paymaster24_trans_info_row['currency']]['symbol_right'];
											if ($paymaster24AmountFormatted != strip_tags($order->order_totals['ot_total']['text']) ) {
												$mc_gross_display_text = '<span class="redIndicator">'.$paymaster24AmountFormatted.'<br><span class="smallText">Does not match purchase amount.</span></span>';
											} else {
												$mc_gross_display_text = $paymaster24AmountFormatted;
											}

											echo $mc_gross_display_text;
											?>
										</td>
									</tr>
									<tr>
										<td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
									</tr>
								</table>
							</td>
						</tr>
					</table>
				</td>
			</tr>
			<?php
		}
	} else {
		echo $payment_method_title_info;
		?>
		<tr>
			<td class="main" nowrap>
				<?
				if ($order->info['orders_status'] == '1' || $order->info['orders_status'] == '7' || $order->info['orders_status'] == '2') {
					echo "&nbsp;&nbsp;";
					echo tep_draw_form('paymaster24_status_check_form', FILENAME_ORDERS, tep_get_all_get_params(), 'post');
					echo tep_draw_hidden_field('subaction', 'payment_action');
					echo tep_draw_hidden_field('payment_action', 'check_trans_status');
					echo tep_submit_button('Check Payment Status', 'Check Payment Status', 'name=paymaster24CheckTransStatusBtn"', 'inputButton');
					echo "</form>";
				}
				?>
			</td>
		</tr>
		<?php
	}
	?>

</table>