<?
include_once(DIR_FS_CATALOG_MODULES . 'payment/paymaster24/admin/languages/' . $language . '/paymaster24.lng.php');
$paymaster24_trans_info_select_sql = "SELECT * FROM " . TABLE_PAYMASTER24 . " WHERE order_id = '" . $order_obj->orders_id . "'";
$paymaster24_trans_info_result_sql = tep_db_query($paymaster24_trans_info_select_sql);
?>
<table border="0" width="100%" cellspacing="0" cellpadding="2">
	<tr>
		<td class="subInvoiceBoxHeading">
			<div style="border-bottom: 1px solid #996600; border-top: 1px solid #996600; padding: 2px;">Payment Details</div>
		</td>
	</tr>
	<?
	if ($paymaster24_trans_info_row = tep_db_fetch_array($paymaster24_trans_info_result_sql)) {
		?>
		<tr>
			<td class="main">
				<table border="0" width="100%" cellspacing="0" cellpadding="2">
					<tr valign="top">
						<td width="50%" valign="top">
							<table border="0" cellspacing="0" cellpadding="2">
								<tr>
									<td class="invoiceRecords" valign="top" nowrap><?= ENTRY_PAYMASTER24_CURRENCY ?></td>
									<td class="invoiceRecords" valign="top">:&nbsp;</td>
									<td class="invoiceRecords" nowrap><?= $paymaster24_trans_info_row['currency'] ?></td>
								</tr>
								<tr>
									<td class="invoiceRecords" valign="top" nowrap><?= ENTRY_PAYMASTER24_PRICE ?></td>
									<td class="invoiceRecords" valign="top">:&nbsp;</td>
									<td class="invoiceRecords">
										<?php
										$paymaster24GrossAmt = number_format($paymaster24_trans_info_row['amount'], $currencies->currencies[$paymaster24_trans_info_row['currency']]['decimal_places'], $currencies->currencies[$paymaster24_trans_info_row['currency']]['decimal_point'], $currencies->currencies[$paymaster24_trans_info_row['currency']]['thousands_point']);
										$paymaster24AmountFormatted = $currencies->currencies[$paymaster24_trans_info_row['currency']]['symbol_left'] . $paymaster24GrossAmt . $currencies->currencies[$paymaster24_trans_info_row['currency']]['symbol_right'];
										echo $paymaster24AmountFormatted;
										?>
									</td>
								</tr>
							</table>
						</td>
						<td valign="top">
							<table border="0" cellspacing="0" cellpadding="2">
								<tr>
									<td class="invoiceRecords" valign="top" nowrap><?= ENTRY_paymaster24_INVOICE_ID ?></td>
									<td class="invoiceRecords" valign="top">:&nbsp;</td>
									<td class="invoiceRecords"><?= (tep_not_null($paymaster24_trans_info_row['payment_id']) ? $paymaster24paymaster24_trans_info_row['payment_id'] : '') ?></td>
								</tr>
							</table>
						</td>
					</tr>
				</table>
			</td>
		</tr>
		<?
	} else {
		?>
		<tr>
			<td class="invoiceRecords">No further payment information is available.</td>
		</tr>
		<?
	}
	?>