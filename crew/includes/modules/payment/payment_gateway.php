<?
if (!class_exists('payment_gateway')) {

	class payment_gateway {
	    public function get_support_currencies($pm_id, $by_zone = true) {
	    	global $zone_info_array;
	    	
	    	$default_currency = '';
	    	$support_currencies_array = array();
	    	
	    	/*$currency_code_select_sql = "	SELECT currency_code, payment_methods_instance_default 
	    									FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " 
	    									WHERE payment_methods_id = '" . (int)$pm_id . "' 
	    										AND payment_methods_instance_status = '1' 
	    									ORDER BY currency_code";*/
	    	$currency_code_select_sql = "	SELECT currency_code, payment_methods_instance_default 
	    									FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " 
	    									WHERE payment_methods_id = '" . (int)$pm_id . "' 
	    									ORDER BY currency_code";
	    	$currency_code_result_sql = tep_db_query($currency_code_select_sql);
	    	
	    	if (tep_db_num_rows($currency_code_result_sql) > 0) {
	    		while ($currency_code_row = tep_db_fetch_array($currency_code_result_sql)) {
	    			$support_currencies_array[] = $currency_code_row['currency_code'];
	    			
	    			if ($currency_code_row['payment_methods_instance_default'] == 1) {
	    				$default_currency = $currency_code_row['currency_code'];
	    			}
	    		}
	    	} else {
	    		if ($this->payment_methods_parent_id > 0) {
	    			$payment_methods_parent_id = $this->payment_methods_parent_id;
	    		} else {
	    			$payment_methods_parent_id = $this->payment_methods_id;
	    		}
	    		/*
	    		$parent_currency_code_select_sql = "	SELECT currency_code, payment_methods_instance_default 
				    									FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " 
				    									WHERE payment_methods_id = '" . (int)$payment_methods_parent_id . "' 
				    										AND payment_methods_instance_status = '1' 
				    									ORDER BY currency_code";
	    		
	    		*/
	    		$parent_currency_code_select_sql = "	SELECT currency_code, payment_methods_instance_default 
				    									FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " 
				    									WHERE payment_methods_id = '" . (int)$payment_methods_parent_id . "' 
				    									ORDER BY currency_code";
	    		$parent_currency_code_result_sql = tep_db_query($parent_currency_code_select_sql);
	    		while ($parent_currency_code_row = tep_db_fetch_array($parent_currency_code_result_sql)) {
	    			$support_currencies_array[] = $parent_currency_code_row['currency_code'];
	    			
	    			if ($parent_currency_code_row['payment_methods_instance_default'] == 1) {
	    				$default_currency = $parent_currency_code_row['currency_code'];
	    			}
	    		}
	    	}
	    	
	    	if (tep_not_null($default_currency)) {
	    		$this->defCurr = $default_currency;
	    	}
	    	
	    	if ($by_zone) {
		    	if (isset($zone_info_array)) {
		    		$support_currencies_array = array_intersect($zone_info_array[3]->zone_currency_id, $support_currencies_array);
		    		if (count($support_currencies_array)) {
		    			array_multisort($support_currencies_array);
		    		} else {
		    			$support_currencies_array = array($default_currency);
		    		}
		    	} else {
		    		$support_currencies_array = array($default_currency);
		    	}
		    } else {
	    		$this->set_support_currencies($support_currencies_array);
	    		// All found currencies is supported. Normally this is used for backend script such as IPN call
	    	}
	    	
	    	return $support_currencies_array;
	    }
	    
	    public function get_confirm_complete_days() {
	    	return $this->confirm_complete_days;
	    }
	
		public function get_pm_info() {
			return $this->message;
		}
		
		public function get_email_pm_info() {
			return $this->email_message;
		}
		
	    protected function remove() {
	      	tep_db_query("delete from " . TABLE_CONFIGURATION . " where configuration_key in ('" . implode("', '", $this->keys()) . "')");
	    }
	    
		public function get_pg_id () {
			return $this->payment_methods_id;
		}
		
		public function get_ipn_flag() {
			return $this->has_ipn;
		}
		
	    public function get_pm_status() {
	    	$pm_status_select_sql = "	SELECT payment_methods_parent_id, payment_methods_receive_status_mode
	    								FROM " . TABLE_PAYMENT_METHODS . "
	    								WHERE 1 ";
			if ((int)$this->payment_methods_id>0) {
				$pm_status_select_sql .= " AND payment_methods_id = '".$this->payment_methods_id."'";
			} else {
				$pm_status_select_sql .= " AND payment_methods_filename = '".tep_db_input($this->filename)."'";
			}
			$pm_status_result_sql = tep_db_query($pm_status_select_sql);
			if ($pm_status_row = tep_db_fetch_array($pm_status_result_sql)) {
				if ((int)$pm_status_row['payment_methods_parent_id']==0) {
					return $pm_status_row['payment_methods_receive_status_mode'];
				} else {
			    	$pg_status_select_sql = "	SELECT payment_methods_receive_status_mode
			    								FROM " . TABLE_PAYMENT_METHODS . "
			    								WHERE payment_methods_id = '".$pm_status_row['payment_methods_parent_id']."'";
					$pg_status_result_sql = tep_db_query($pg_status_select_sql);
					if ($pg_status_row = tep_db_fetch_array($pg_status_result_sql)) {
						return ((int)$pg_status_row['payment_methods_receive_status_mode']==1 ? $pm_status_row['payment_methods_receive_status_mode'] : $pg_status_row['payment_methods_receive_status_mode']);
					}
				}
				
			}
			return 0;
	    }
	}
}
?>