<?

require_once('payment_gateway.php');

class paynearme extends payment_gateway {

    var $code, $title, $description, $enabled, $sort_order;
    var $preferred, $payee_code, $user_id, $paynearmeCurrencies, $comment;
    var $check_trans_status_flag;

    function paynearme($pm_id = '') {
        global $order, $languages_id, $default_languages_id, $currency, $defCurr;
        global $customer_country_id, $amount_decimal;

        $this->code = 'paynearme';
        $this->filename = 'paynearme.php';
        $this->title = $this->code;
        $this->preferred = true;
        $this->auto_cancel_period = 21600; // In minutes [15 days]
        $this->connect_via_proxy = false; // localhost test - set to true, server test - set to false //need proxy to connect outside
        $this->force_to_checkoutprocess = true;
        $this->has_ipn = true;
        $this->amount_decimal = 2;
        $this->payment_methods_id = 0;
        $this->payment_methods_parent_id = 0;
        $this->check_trans_status_flag = false;

        $this->version = '1.6';
        $this->order_duration = '15'; // Order will be invalid after 15 Days

        $payment_methods_select_sql = "	SELECT  payment_methods_parent_id, payment_methods_code,
												payment_methods_types_id,
												payment_methods_receive_status_mode, payment_methods_id,
												payment_methods_title, payment_methods_sort_order,
												payment_methods_receive_status, payment_methods_legend_color,
												payment_methods_logo
										FROM " . TABLE_PAYMENT_METHODS . "
										WHERE 1 ";
        if ((int) $pm_id > 0) {
            $payment_methods_select_sql .= "	AND payment_methods_id = '" . (int) $pm_id . "'";
        } else {
            $payment_methods_select_sql .= "	AND payment_methods_filename = '" . tep_db_input($this->filename) . "'";
        }

        $payment_methods_result_sql = tep_db_query($payment_methods_select_sql);
        if ($payment_methods_row = tep_db_fetch_array($payment_methods_result_sql)) {
            $pm_desc_title_select_sql = "	SELECT payment_methods_description_title
											FROM " . TABLE_PAYMENT_METHODS_DESCRIPTION . "
											WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "'
												AND (IF (languages_id = '" . tep_db_input($languages_id) . "', 1, IF ((	SELECT COUNT(payment_methods_id) > 0
																														FROM " . TABLE_PAYMENT_METHODS_DESCRIPTION . "
																														WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "'
																															AND languages_id = '" . (int) $languages_id . "'), 0, languages_id = '" . (int) $default_languages_id . "')))";

            $pm_desc_title_result_sql = tep_db_query($pm_desc_title_select_sql);
            $pm_desc_title_row = tep_db_fetch_array($pm_desc_title_result_sql);

            $this->enabled = $payment_methods_row['payment_methods_receive_status_mode'];
            $this->payment_methods_id = (int) $payment_methods_row['payment_methods_id'];
            $this->payment_methods_parent_id = (int) $payment_methods_row['payment_methods_parent_id'];
            $this->code = $payment_methods_row['payment_methods_code'];
            $this->title = $payment_methods_row['payment_methods_title'];

            $this->display_title = $pm_desc_title_row['payment_methods_description_title'];
            $this->description = $this->display_title;
            $this->sort_order = $payment_methods_row['payment_methods_sort_order'];
            $this->legend_display_colour = $payment_methods_row['payment_methods_legend_color'];
            $this->paynearmeCurrencies = $this->get_support_currencies($this->payment_methods_id);

            $configuration_setting_array = $this->load_pm_setting();
            $this->order_status_id = $configuration_setting_array['MODULE_PAYMENT_PAYNEARME_ORDER_STATUS_ID'];
            $this->processing_status_id = $configuration_setting_array['MODULE_PAYMENT_PAYNEARME_PROCESSING_STATUS_ID'];
            $this->confirm_complete_days = $configuration_setting_array['MODULE_PAYMENT_PAYNEARME_CONFIRM_COMPLETE'];
            $this->require_address_information = $configuration_setting_array['MODULE_PAYMENT_PAYNEARME_MANDATORY_ADDRESS_FIELD'];
            $this->require_ic_information = $configuration_setting_array['MODULE_PAYMENT_PAYNEARME_MANDATORY_IC_FIELD'];
            $this->require_contact_information = $configuration_setting_array['MODULE_PAYMENT_PAYNEARME_MANDATORY_CONTACT_FIELD'];
            $this->customer_payment_info = $configuration_setting_array['MODULE_PAYMENT_PAYNEARME_CUSTOMER_PAYMENT_INFO'];

            $this->test_mode = ($configuration_setting_array['MODULE_PAYMENT_PAYNEARME_TEST_MODE'] == 'True' ? true : false);

            $this->message = $configuration_setting_array['MODULE_PAYMENT_PAYNEARME_MESSAGE'];
            $this->email_message = $configuration_setting_array['MODULE_PAYMENT_PAYNEARME_EMAIL_MESSAGE'];
            $this->email_footer = str_replace(array("<br>", "<BR>", "\r\n"), array("\n", "\n", ''), $this->email_message);

            $this->order_processing_status = ((int) $this->processing_status_id > 0 ? $this->processing_status_id : 7 );
            $this->order_status = ( (int) $this->order_status_id > 0 ? $this->order_status_id : DEFAULT_ORDERS_STATUS_ID );

            if ($this->test_mode) {
                $this->form_action_url = 'https://sandbox.paynearme.com/api/buy_now'; // test url - create order
                $this->form_status_url = 'https://sandbox.paynearme.com/api/orders'; // test url - get order status
            } else {
                $this->form_action_url = 'https://www.paynearme.com/api/buy_now';  // live url
                $this->form_status_url = 'https://www.paynearme.com/api/orders'; // live url
            }
        }
    }

    function javascript_validation() {
        return false;
    }

    function selection() {
        global $languages_id, $default_languages_id;

        $selection_array = array();

        $payment_methods_select_sql = "	SELECT payment_methods_id, payment_methods_code, payment_methods_types_id, payment_methods_logo, payment_methods_receive_status_mode
										FROM " . TABLE_PAYMENT_METHODS . "
										WHERE payment_methods_receive_status = 1
											AND payment_methods_receive_status_mode <> 0
											AND payment_methods_parent_id = '" . (int) $this->payment_methods_id . "'
										ORDER BY payment_methods_sort_order";
        $payment_methods_result_sql = tep_db_query($payment_methods_select_sql);
        while ($payment_methods_row = tep_db_fetch_array($payment_methods_result_sql)) {
            $pm_desc_title_select_sql = "	SELECT payment_methods_description_title
											FROM " . TABLE_PAYMENT_METHODS_DESCRIPTION . "
											WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "'
												AND (IF (languages_id = '" . tep_db_input($languages_id) . "', 1, IF ((	SELECT COUNT(payment_methods_id) > 0
																														FROM " . TABLE_PAYMENT_METHODS_DESCRIPTION . "
																														WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "'
																															AND languages_id = '" . (int) $languages_id . "'), 0, languages_id = '" . (int) $default_languages_id . "')))";
            $pm_desc_title_result_sql = tep_db_query($pm_desc_title_select_sql);
            $pm_desc_title_row = tep_db_fetch_array($pm_desc_title_result_sql);

            $pm_array = $this->load_pm_setting(1, $payment_methods_row['payment_methods_id']);

            $selection_array[] = array('payment_gateway_code' => $this->code,
                'payment_methods_receive_status_mode' => $payment_methods_row['payment_methods_receive_status_mode'],
                'payment_methods_id' => $payment_methods_row['payment_methods_id'],
                'payment_methods_code' => $payment_methods_row['payment_methods_code'],
                'payment_methods_types_id' => $payment_methods_row['payment_methods_types_id'],
                'payment_methods_parent_id' => $this->payment_methods_id,
                'payment_methods_logo' => $payment_methods_row['payment_methods_logo'],
                'payment_methods_description_title' => $pm_desc_title_row['payment_methods_description_title'],
                'currency' => $this->get_support_currencies($payment_methods_row['payment_methods_id']),
                'show_billing_address' => $pm_array['MODULE_PAYMENT_PAYNEARME_MANDATORY_ADDRESS_FIELD'],
                'confirm_complete_days' => $pm_array['MODULE_PAYMENT_PAYNEARME_CONFIRM_COMPLETE'],
                'show_contact_number' => $pm_array['MODULE_PAYMENT_PAYNEARME_MANDATORY_CONTACT_FIELD'],
                'show_ic' => $pm_array['MODULE_PAYMENT_PAYNEARME_MANDATORY_IC_FIELD']
            );

            if ($payment_methods_row['payment_methods_receive_status_mode'] == '-1') { // Inactive
                $pm_status_message_select = "	SELECT payment_methods_status_message
												FROM " . TABLE_PAYMENT_METHODS_STATUS_DESCRIPTION . "
												WHERE payment_methods_mode = 'RECEIVE'
													AND payment_methods_status = '-1'
													AND languages_id = '" . (int) $languages_id . "'
													AND payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "'";
                $pm_status_message_result = tep_db_query($pm_status_message_select);
                $pm_status_message_row = tep_db_fetch_array($pm_status_message_result);
                $selection_array[sizeof($selection_array) - 1]['payment_methods_status_message'] = $pm_status_message_row['payment_methods_status_message'];
            }
        }

        return $selection_array;
    }

    function get_require_address_information() {
        return $this->require_address_information;
    }

    function draw_confirm_button() {
        return tep_image_button2('red', 'javascript:document.checkout_confirmation.submit()', MODULE_PAYMENT_PAYNEARME_TEXT_CART, 200);
    }

    function get_confirm_button_caption() {
        return MODULE_PAYMENT_PAYNEARME_TEXT_TITLE;
    }

    function pre_confirmation_check() {
        ; // Nothing to check for now
    }

    function confirmation() {
        return array('title' => $this->message);
    }

    function set_support_currencies($currency_array) {
        $this->paynearmeCurrencies = $currency_array;
    }

    function process_button() {
        global $shipping_cost, $total_cost, $shipping_selected, $shipping_method, $order;
        global $currencies, $currency;

        $timestamp = gmdate('U', time()); // Seconds since the Unix Epoch (January 1 1970 00:00:00 GMT)
        // Check Login
        if ($this->force_to_checkoutprocess && !tep_session_is_registered('order_logged'))
            return;

        $this->paynearmeCurrencies = $this->get_support_currencies($this->payment_methods_id, false);
        $paynearmeCurrencies = $currency;

        // Get default currency if user currency is empty
        if (!in_array($paynearmeCurrencies, $this->paynearmeCurrencies)) {
            $paynearmeCurrencies = $this->paynearmeCurrencies[0];
        }

        // Get Merchant Data
        $this->get_merchant_account($paynearmeCurrencies);

        // Get Total Amount - Convert other currency to USD
        $OrderAmt = $order->info['total'] * $currencies->get_value($paynearmeCurrencies);
        $formatted_order_amt = number_format($OrderAmt, 2, '.', '');

        // Get Signature
        $signature_str = "order_amount" . $formatted_order_amt . "order_currency" . $paynearmeCurrencies . "site_customer_identifier" . $_SESSION['customer_id'] . "site_identifier" . $this->paynearme_id . "site_order_identifier" . $_SESSION['order_logged'] . "timestamp" . $timestamp . "version" . $this->version . "order_duration" . $this->order_duration;
        $signature = bin2hex(md5($signature_str . $this->paynearme_key, true));

        // Prepare needed data to be send to PayNearMe
        $process_button_string =
                tep_draw_hidden_field('order_amount', $formatted_order_amt) .
                tep_draw_hidden_field('order_currency', $paynearmeCurrencies) .
                tep_draw_hidden_field('site_customer_identifier', $_SESSION['customer_id']) .
                tep_draw_hidden_field('site_identifier', $this->paynearme_id) .
                tep_draw_hidden_field('site_order_identifier', $_SESSION['order_logged']) .
                tep_draw_hidden_field('timestamp', $timestamp) .
                tep_draw_hidden_field('version', $this->version) .
                tep_draw_hidden_field('order_duration', $this->order_duration) .
                tep_draw_hidden_field('signature', $signature);

        return $process_button_string;
    }

    /* Call when:
      1. "Proceed to PayNearMe" button click
      2. Return from PayNearMe
     */

    function before_process() {
        if (!isset($_SESSION['order_logged']) || !tep_not_null($_SESSION['order_logged'])) {
            return;
        }

        // Validate Transaction
        $return_array = $this->check_trans_status($_SESSION['order_logged']);

        // Display error message if order id not found
        if (!isset($return_array['paynearme_order_status'])) {
            tep_redirect(tep_href_link(FILENAME_CHECKOUT_PAYMENT, 'error_message="Error"' . MODULE_PAYMENT_PAYNEARME_TEXT_ERROR_MESSAGE, 'SSL', true, false));
        }
    }

    function after_process() {
        return false;
    }

    function link_to_payment($new_order_id) {
        //if (tep_session_is_registered('order_logged')) tep_session_unregister('order_logged');
        if (isset($_SESSION['order_logged']))
            unset($_SESSION['order_logged']);

        $_SESSION['order_logged'] = $new_order_id;
        require(DIR_WS_INCLUDES . 'modules/payment/paynearme/catalog/paynearme_splash.php');

        return;
    }

    function is_supported_currency($selected_currency) {
        return (in_array($selected_currency, $this->paynearmeCurrencies) ? true : false);
    }

    function check_trans_status($order_id, $payment_status = '') {
        global $currencies;

        $this->check_trans_status_flag = false;

        $paynearme_order_data_array = array();
        $authentication_flag = false;

        // Get Order Info
        $order_info_select_sql = "	SELECT o.payment_methods_id, o.currency, o.currency_value, ot.value, ot.text
									FROM " . TABLE_ORDERS . " AS o
									INNER JOIN " . TABLE_ORDERS_TOTAL . " AS ot
										ON o.orders_id=ot.orders_id AND ot.class='ot_total'
									WHERE o.orders_id = '" . tep_db_input($order_id) . "'";
        $order_info_result_sql = tep_db_query($order_info_select_sql);

        // Check if Order Created - Yes after return from PayNearMe
        if ($order_info_row = tep_db_fetch_array($order_info_result_sql)) {

            $paynearmeCurrencies = $order_info_row['currency'];
            $this->get_merchant_account($paynearmeCurrencies);

            // Check if the selected payment method exist for the order made
            if ($order_info_row['payment_methods_id'] != $this->payment_methods_id)
                return;

            // Get order data
            require_once(DIR_WS_CLASSES . 'ogm_xml_to_ary.php');

            $timestamp = time();
            $str = "site_identifier" . $this->paynearme_id . "site_order_identifier" . $order_id . "timestamp" . $timestamp . "version" . $this->version;
            $signature = bin2hex(md5($str . $this->paynearme_key, true));
            $url = $this->form_status_url . "?site_identifier=" . $this->paynearme_id . "&site_order_identifier=" . $order_id . "&timestamp=" . $timestamp . "&version=" . $this->version . "&signature=" . $signature;

            $ogm_xml_to_ary_obj = new ogm_xml_to_ary($this->curl_connect($url), 'content');

            if (isset($ogm_xml_to_ary_obj->root['result'])) {
                if ($ogm_xml_to_ary_obj->root['result']['_a']['status'] == "ok") {

                    // Order Data
                    $paynearme_site_name = $ogm_xml_to_ary_obj->root['result']['_c']['orders']['_c']['order']['_a']['site_name'];
                    $paynearme_site_customer_id = $ogm_xml_to_ary_obj->root['result']['_c']['orders']['_c']['order']['_c']['customer']['_a']['site_customer_identifier'];
                    $paynearme_site_order_id = $ogm_xml_to_ary_obj->root['result']['_c']['orders']['_c']['order']['_a']['site_order_identifier'];
                    $paynearme_status = $ogm_xml_to_ary_obj->root['result']['_c']['orders']['_c']['order']['_a']['order_status'];
                    $paynearme_pnm_order_id = $ogm_xml_to_ary_obj->root['result']['_c']['orders']['_c']['order']['_a']['pnm_order_identifier'];
                    $paynearme_order_created = $ogm_xml_to_ary_obj->root['result']['_c']['orders']['_c']['order']['_a']['order_created'];
                    $paynearme_order_amount = $ogm_xml_to_ary_obj->root['result']['_c']['orders']['_c']['order']['_a']['order_amount'];
                    $paynearme_order_currency = $ogm_xml_to_ary_obj->root['result']['_c']['orders']['_c']['order']['_a']['order_currency'];
                    $paynearme_order_type = $ogm_xml_to_ary_obj->root['result']['_c']['orders']['_c']['order']['_a']['order_type'];
                    $paynearme_pnm_balance_due_amount = $ogm_xml_to_ary_obj->root['result']['_c']['orders']['_c']['order']['_a']['pnm_balance_due_amount'];
                    $paynearme_pnm_balance_due_currency = $ogm_xml_to_ary_obj->root['result']['_c']['orders']['_c']['order']['_a']['pnm_balance_due_currency'];
                    $paynearme_order_tracking_url = $ogm_xml_to_ary_obj->root['result']['_c']['orders']['_c']['order']['_a']['order_tracking_url'];
                    $paynearme_order_closed_reason = (isset($ogm_xml_to_ary_obj->root['result']['_c']['orders']['_c']['order']['_a']['order_closed_reason']) ? $ogm_xml_to_ary_obj->root['result']['_c']['orders']['_c']['order']['_a']['order_closed_reason'] : "");
                    $paynearme_order_closed_date = (isset($ogm_xml_to_ary_obj->root['result']['_c']['orders']['_c']['order']['_a']['order_closed_date']) ? $ogm_xml_to_ary_obj->root['result']['_c']['orders']['_c']['order']['_a']['order_closed_date'] : "");

                    // Payment Info Data
                    $paynearme_full_encoding = $ogm_xml_to_ary_obj->root['result']['_c']['orders']['_c']['order']['_c']['slip']['_a']['full_encoding'];
                    $paynearme_slip_pdf_url = $ogm_xml_to_ary_obj->root['result']['_c']['orders']['_c']['order']['_c']['slip']['_a']['slip_pdf_url'];
                    $paynearme_card_pdf_url = $ogm_xml_to_ary_obj->root['result']['_c']['orders']['_c']['order']['_c']['card_slip']['_a']['slip_pdf_url'];

                    // Customer Info
                    $paynearme_pnm_customer_phone = $ogm_xml_to_ary_obj->root['result']['_c']['orders']['_c']['order']['_c']['customer']['_a']['pnm_customer_phone'];
                    $paynearme_pnm_customer_id = $ogm_xml_to_ary_obj->root['result']['_c']['orders']['_c']['order']['_c']['customer']['_a']['pnm_customer_identifier'];
                    $paynearme_pnm_customer_email = $ogm_xml_to_ary_obj->root['result']['_c']['orders']['_c']['order']['_c']['customer']['_a']['pnm_customer_email'];
                    $paynearme_pnm_customer_city = $ogm_xml_to_ary_obj->root['result']['_c']['orders']['_c']['order']['_c']['customer']['_a']['pnm_customer_city'];
                    $paynearme_pnm_customer_state = $ogm_xml_to_ary_obj->root['result']['_c']['orders']['_c']['order']['_c']['customer']['_a']['pnm_customer_state'];
                    $paynearme_pnm_customer_postal_code = $ogm_xml_to_ary_obj->root['result']['_c']['orders']['_c']['order']['_c']['customer']['_a']['pnm_customer_postal_code'];

                    // Validate Order Amount
                    $paynearme_return_amt = (isset($ogm_xml_to_ary_obj->root['result']['_c']['orders']['_c']['order']['_a']['order_amount']) ? $ogm_xml_to_ary_obj->root['result']['_c']['orders']['_c']['order']['_a']['order_amount'] : 0);

                    // Set to log
                    $paynearme_status_history_data_sql = array('paynearme_order_id' => (int) $order_id,
                        'paynearme_date' => 'now()'
                    );

                    if (tep_not_null($payment_status)) {
                        $paynearme_status_history_data_sql['paynearme_status'] = tep_db_prepare_input($payment_status);
                    }

                    // Validation Checking
                    if (!isset($paynearme_site_name) || $paynearme_site_name != $this->paynearme_site_name) {
                        $paynearme_status_history_data_sql['paynearme_description'] = 'Error: Site name not matched (' . $paynearme_site_name . ' != ' . $this->paynearme_site_name . ')';
                    } else if (!isset($paynearme_site_order_id) || $paynearme_site_order_id != $order_id) {
                        $paynearme_status_history_data_sql['paynearme_description'] = 'Error: Order ID not matched (' . $paynearme_site_order_id . ' != ' . $order_id . ')';
                    } else if ($currencies->currencies[(isset($paynearme_order_currency) ? $paynearme_order_currency : '')]['symbol_left'] . $paynearme_return_amt . $currencies->currencies[(isset($paynearme_order_currency) ? $paynearme_order_currency : '')]['symbol_right'] != strip_tags($order_info_row['text'])) {
                        $paynearme_status_history_data_sql['paynearme_description'] = 'Error: Currency or Amount not matched (' . $currencies->currencies[(isset($paynearme_order_currency) ? $paynearme_order_currency : '')]['symbol_left'] . $paynearme_return_amt . $currencies->currencies[(isset($paynearme_order_currency) ? $paynearme_order_currency : '')]['symbol_right'] . ' != ' . strip_tags($order_info_row['text']) . ')';
                    } else {
                        $paynearme_status_history_data_sql['paynearme_description'] = 'Order Status: ' . $paynearme_status;
                        if ($paynearme_status == "closed") {
                            $paynearme_status_history_data_sql['paynearme_description'] .= '<br/>Closed Reason: ' . $paynearme_order_closed_reason . '<br/>Closed Date: ' . $paynearme_order_closed_date;
                        }

                        $authentication_flag = true;
                        if ($payment_status == 'payment') {
                            $this->check_trans_status_flag = true;
                        }
                    }

                    /* Save Return Value to DB */
                    // Order Data
                    $paynearme_order_data_array = array('paynearme_site_id' => tep_db_prepare_input($this->paynearme_id),
                        'paynearme_site_name' => tep_db_prepare_input($paynearme_site_name),
                        'paynearme_pnm_order_id' => (int) $paynearme_pnm_order_id,
                        'paynearme_order_created' => tep_db_prepare_input($paynearme_order_created),
                        'paynearme_order_status' => tep_db_prepare_input($paynearme_status),
                        'paynearme_order_amount' => tep_db_prepare_input($paynearme_order_amount),
                        'paynearme_order_currency' => tep_db_prepare_input($paynearme_order_currency),
                        'paynearme_order_type' => tep_db_prepare_input($paynearme_order_type),
                        'paynearme_pnm_balance_due_amount' => tep_db_prepare_input($paynearme_pnm_balance_due_amount),
                        'paynearme_pnm_balance_due_currency' => tep_db_prepare_input($paynearme_pnm_balance_due_currency),
                        'paynearme_order_tracking_url' => tep_db_prepare_input($paynearme_order_tracking_url),
                        'paynearme_site_customer_id' => tep_db_prepare_input($paynearme_site_customer_id)
                    );

                    $paynearme_check_exist_select_sql = "	SELECT paynearme_order_id
															FROM " . TABLE_PAYNEARME_ORDER . "
															WHERE paynearme_order_id = '" . (int) $order_id . "'";
                    $paynearme_check_exist_result_sql = tep_db_query($paynearme_check_exist_select_sql);
                    if ($paynearme_check_exist_row = tep_db_fetch_array($paynearme_check_exist_result_sql)) {
                        tep_db_perform(TABLE_PAYNEARME_ORDER, $paynearme_order_data_array, 'update', " paynearme_order_id = '" . (int) $order_id . "' ");
                    } else {
                        $paynearme_order_data_array['paynearme_order_id'] = (int) $order_id;
                        tep_db_perform(TABLE_PAYNEARME_ORDER, $paynearme_order_data_array);
                    }

                    // Payment Info Data
                    $paynearme_payment_data_array = array('paynearme_full_encoding' => tep_db_prepare_input($paynearme_full_encoding),
                        'paynearme_slip_pdf_url' => tep_db_prepare_input($paynearme_slip_pdf_url),
                        'paynearme_card_pdf_url' => tep_db_prepare_input($paynearme_card_pdf_url)
                    );
                    if (tep_not_null($payment_status)) {
                        $paynearme_payment_data_array['paynearme_payment_status'] = tep_db_prepare_input($payment_status);
                    }

                    $paynearme_check_exist_select_sql = "	SELECT paynearme_site_order_id
															FROM " . TABLE_PAYNEARME_PAYMENT_INFO . "
															WHERE paynearme_site_order_id = '" . (int) $order_id . "'";
                    $paynearme_check_exist_result_sql = tep_db_query($paynearme_check_exist_select_sql);
                    if ($paynearme_check_exist_row = tep_db_fetch_array($paynearme_check_exist_result_sql)) {
                        tep_db_perform(TABLE_PAYNEARME_PAYMENT_INFO, $paynearme_payment_data_array, 'update', " paynearme_site_order_id = '" . (int) $order_id . "' ");
                    } else {
                        $paynearme_payment_data_array['paynearme_site_order_id'] = (int) $order_id;
                        tep_db_perform(TABLE_PAYNEARME_PAYMENT_INFO, $paynearme_payment_data_array);
                    }

                    // Customer Data
                    $paynearme_customer_data_array = array('paynearme_site_order_id' => (int) $paynearme_site_order_id,
                        'paynearme_site_customer_id' => tep_db_prepare_input($paynearme_site_customer_id),
                        'paynearme_pnm_customer_id' => tep_db_prepare_input($paynearme_pnm_customer_id),
                        'paynearme_pnm_customer_email' => tep_db_prepare_input($paynearme_pnm_customer_email),
                        'paynearme_pnm_customer_phone' => tep_db_prepare_input($paynearme_pnm_customer_phone),
                        'paynearme_pnm_customer_city' => tep_db_prepare_input($paynearme_pnm_customer_city),
                        'paynearme_pnm_customer_state' => tep_db_prepare_input($paynearme_pnm_customer_state),
                        'paynearme_pnm_customer_postal_code' => tep_db_prepare_input($paynearme_pnm_customer_postal_code)
                    );

                    $paynearme_check_exist_select_sql = "	SELECT paynearme_site_order_id
															FROM " . TABLE_PAYNEARME_CUSTOMER . "
															WHERE paynearme_site_order_id = '" . (int) $order_id . "'";
                    $paynearme_check_exist_result_sql = tep_db_query($paynearme_check_exist_select_sql);
                    if ($paynearme_check_exist_row = tep_db_fetch_array($paynearme_check_exist_result_sql)) {
                        tep_db_perform(TABLE_PAYNEARME_CUSTOMER, $paynearme_customer_data_array, 'update', " paynearme_site_order_id = '" . (int) $order_id . "' ");
                    } else {
                        $paynearme_customer_data_array['paynearme_site_order_id'] = (int) $order_id;
                        tep_db_perform(TABLE_PAYNEARME_CUSTOMER, $paynearme_customer_data_array);
                    }

                    $paynearme_status_history_data_sql['paynearme_changed_by'] = (isset($path_info_array["basename"]) && $path_info_array["basename"] == 'cron_order_payment.php' ? 'cronjob' : (isset($_SESSION['login_email_address']) ? $_SESSION['login_email_address'] : 'system' ) );
                    if (tep_not_null($payment_status)) {
                        tep_db_perform(TABLE_PAYNEARME_STATUS_HISTORY, $paynearme_status_history_data_sql);
                    }
                } else {
                    $error_msg = $ogm_xml_to_ary_obj->root['result']['_c']['errors']['_c']['error']['_a']['description'];
                    $paynearme_status_history_data_sql['paynearme_description'] = "Error: " . $error_msg;
                    $paynearme_status_history_data_sql['paynearme_changed_by'] = (isset($path_info_array["basename"]) && $path_info_array["basename"] == 'cron_order_payment.php' ? 'cronjob' : (isset($_SESSION['login_email_address']) ? $_SESSION['login_email_address'] : 'system' ) );
                    tep_db_perform(TABLE_PAYNEARME_STATUS_HISTORY, $paynearme_status_history_data_sql);
                }
            } else {
                $paynearme_status_history_data_sql['paynearme_description'] = "Error: Return Empty Result";
                $paynearme_status_history_data_sql['paynearme_changed_by'] = (isset($path_info_array["basename"]) && $path_info_array["basename"] == 'cron_order_payment.php' ? 'cronjob' : (isset($_SESSION['login_email_address']) ? $_SESSION['login_email_address'] : 'system' ) );
                tep_db_perform(TABLE_PAYNEARME_STATUS_HISTORY, $paynearme_status_history_data_sql);
            }
        }

        $paynearme_order_data_array['check_authentication_flag'] = $authentication_flag;
        return $paynearme_order_data_array;
    }

    function output_error() {
        return false;
    }

    function check() {
        if (!isset($this->_check)) {
            $payment_methods_select_sql = "	SELECT payment_methods_id
											FROM " . TABLE_PAYMENT_METHODS . " as pm
											WHERE pm.payment_methods_id = '" . (int) $this->payment_methods_id . "'";
            $payment_methods_result_sql = tep_db_query($payment_methods_select_sql);
            $this->_check = tep_db_num_rows($payment_methods_result_sql);
        }
        return $this->_check;
    }

    function is_processed_order($order_id) {

        $payment_status_select_sql = "	SELECT paynearme_payment_status
										FROM " . TABLE_PAYNEARME_PAYMENT_INFO . "
										WHERE 	paynearme_site_order_id = '" . (int) tep_db_input($order_id) . "'";
        $payment_status_result_sql = tep_db_query($payment_status_select_sql);
        $payment_status_row = tep_db_fetch_array($payment_status_result_sql);

        if (tep_db_num_rows($payment_status_result_sql)) {
            if ($payment_status_row['paynearme_payment_status'] == 'payment') {
                return true;
            }
        }
        return false;
    }

    function load_pm_setting($language_id = 1, $pm_id = '') {
        $pm_setting_array = array();
        //load value from DB

        if (tep_not_null($pm_id)) {
            $payment_method_id = $pm_id;

            $payment_methods_parent_id_select_sql = "	SELECT payment_methods_parent_id
														FROM " . TABLE_PAYMENT_METHODS . "
														WHERE payment_methods_id = '" . (int) $pm_id . "'";
            $payment_methods_parent_id_result_sql = tep_db_query($payment_methods_parent_id_select_sql);
            $payment_methods_parent_id_row = tep_db_fetch_array($payment_methods_parent_id_result_sql);

            $payment_gateway_id = $payment_methods_parent_id_row['payment_methods_parent_id'];
        } else {
            $payment_method_id = $this->payment_methods_id;
            $payment_gateway_id = $this->payment_methods_parent_id;
        }

        $payment_configuration_info_select_sql = "	SELECT pci.payment_configuration_info_key, pcid.payment_configuration_info_value
													FROM " . TABLE_PAYMENT_CONFIGURATION_INFO . " as pci
													LEFT JOIN " . TABLE_PAYMENT_CONFIGURATION_INFO_DESCRIPTION . " as pcid
														ON pci.payment_configuration_info_id = pcid.payment_configuration_info_id
															AND pcid.languages_id = '" . (int) $language_id . "'
													WHERE pci.payment_methods_id = '" . (int) $payment_method_id . "'
													ORDER BY pci.payment_configuration_info_sort_order";
        $payment_configuration_info_result_sql = tep_db_query($payment_configuration_info_select_sql);
        if (tep_db_num_rows($payment_configuration_info_result_sql)) {
            while ($payment_configuration_info_row = tep_db_fetch_array($payment_configuration_info_result_sql)) {
                $pm_setting_array[$payment_configuration_info_row['payment_configuration_info_key']] = $payment_configuration_info_row['payment_configuration_info_value'];
            }
        } else if ((int) $payment_gateway_id > 0) { //load value from PG
            $payment_configuration_info_select_sql = "	SELECT pci.payment_configuration_info_key, pcid.payment_configuration_info_value
														FROM " . TABLE_PAYMENT_CONFIGURATION_INFO . " as pci
														LEFT JOIN " . TABLE_PAYMENT_CONFIGURATION_INFO_DESCRIPTION . " as pcid
															ON pci.payment_configuration_info_id = pcid.payment_configuration_info_id
																AND pcid.languages_id = '" . (int) $language_id . "'
														WHERE pci.payment_methods_id = '" . (int) $payment_gateway_id . "'
														ORDER BY pci.payment_configuration_info_sort_order";
            $payment_configuration_info_result_sql = tep_db_query($payment_configuration_info_select_sql);
            while ($payment_configuration_info_row = tep_db_fetch_array($payment_configuration_info_result_sql)) {
                $pm_setting_array[$payment_configuration_info_row['payment_configuration_info_key']] = $payment_configuration_info_row['payment_configuration_info_value'];
            }
        }
        return $pm_setting_array;
    }

    function get_merchant_account($selected_currency) {
        $payment_methods_instance_id = 0;
        $payment_gateway_instance_select_sql = "SELECT pmi.payment_methods_instance_id, pmi.payment_methods_instance_follow_default, pmi.currency_code
				 								FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " as pmi
				 								WHERE pmi.payment_methods_id = '" . (int) $this->payment_methods_id . "'
				 								LIMIT 1";
        $payment_gateway_instance_result_sql = tep_db_query($payment_gateway_instance_select_sql);
        if (tep_db_num_rows($payment_gateway_instance_result_sql)) {
            $payment_gateway_instance_select_sql = "SELECT pmi.payment_methods_instance_id, pmi.payment_methods_instance_follow_default
					 								FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " as pmi
					 								WHERE pmi.currency_code = '" . tep_db_input($selected_currency) . "'
					 									AND pmi.payment_methods_id = '" . (int) $this->payment_methods_id . "'";
            $payment_gateway_instance_result_sql = tep_db_query($payment_gateway_instance_select_sql);
            if ($payment_gateway_instance_row = tep_db_fetch_array($payment_gateway_instance_result_sql)) {
                if ((int) $payment_gateway_instance_row['payment_methods_instance_follow_default'] > 0) { // Follow Default Currency Setting
                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_follow_default'];
                } else { // Has own setting
                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_id'];
                }
            }
        } else {
            $payment_gateway_instance_select_sql = "SELECT pmi.payment_methods_instance_id, pmi.payment_methods_instance_follow_default
					 								FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " as pmi
					 								INNER JOIN " . TABLE_PAYMENT_METHODS . " as pm
					 									ON pm.payment_methods_parent_id = pmi.payment_methods_id
					 								WHERE pmi.currency_code = '" . tep_db_input($selected_currency) . "'
					 									AND pm.payment_methods_id = '" . (int) $this->payment_methods_id . "'";
            $payment_gateway_instance_result_sql = tep_db_query($payment_gateway_instance_select_sql);
            if ($payment_gateway_instance_row = tep_db_fetch_array($payment_gateway_instance_result_sql)) {
                if ((int) $payment_gateway_instance_row['payment_methods_instance_follow_default'] > 0) {
                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_follow_default'];
                } else {
                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_id'];
                }
            }
        }

        $payment_gateway_instance_setting_select_sql = "SELECT pmis.payment_methods_instance_setting_key, pmis.payment_methods_instance_setting_value
						 								FROM " . TABLE_PAYMENT_METHODS_INSTANCE_SETTING . " as pmis
						 								WHERE pmis.payment_methods_instance_id = '" . (int) $payment_methods_instance_id . "'";
        $payment_gateway_instance_setting_result_sql = tep_db_query($payment_gateway_instance_setting_select_sql);
        while ($payment_gateway_instance_setting_row = tep_db_fetch_array($payment_gateway_instance_setting_result_sql)) {
            switch ($payment_gateway_instance_setting_row['payment_methods_instance_setting_key']) {
                case 'MODULE_PAYMENT_PAYNEARME_ID': //PayNearMe ID
                    $this->paynearme_id = $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];
                    break;
                case 'MODULE_PAYMENT_PAYNEARME_KEY': //PayNearMe Key
                    $this->paynearme_key = $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];
                    break;
                case 'MODULE_PAYMENT_PAYNEARME_SITE_NAME': //PayNearMe Site Name
                    $this->paynearme_site_name = $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];
                    break;
            }
        }
    }

    function install() {
        $install_configuration_flag = true;
        if ($this->payment_methods_id > 0) {
            if ($this->receive_status != 1) {
                $update_receive_status_sql_data = array("payment_methods_receive_status" => 1);
                tep_db_perform(TABLE_PAYMENT_METHODS, $update_receive_status_sql_data, 'update', "payment_methods_id = '" . (int) $this->payment_methods_id . "'");
            } else {
                $install_configuration_flag = false;
            }
        } else {
            $install_payment_gateway_data_sql = array('payment_methods_code' => $this->code,
                'payment_methods_send_status' => 0,
                'payment_methods_title' => $this->code,
                'payment_methods_types_id' => 0,
                'payment_methods_parent_id' => 0,
                'payment_methods_legend_color' => '#FFCC44',
                'payment_methods_sort_order' => 5,
                'payment_methods_filename' => $this->filename,
                'payment_methods_logo' => '',
                'date_added' => 'now()',
                'last_modified' => 'now()',
                'payment_methods_receive_status' => 1,
                'payment_methods_receive_status_mode' => 0
            );
            tep_db_perform(TABLE_PAYMENT_METHODS, $install_payment_gateway_data_sql);
            $this->payment_methods_id = tep_db_insert_id();
        }
        if ($install_configuration_flag) {
            $install_key_array = array();
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Set Order Status',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_PAYNEARME_ORDER_STATUS_ID',
                    'payment_configuration_info_description' => 'Set the status of orders made with this payment module to this value.',
                    'payment_configuration_info_sort_order' => '1335',
                    'set_function' => 'tep_cfg_pull_down_order_statuses(',
                    'use_function' => 'tep_get_order_status_name',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => '1',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Set Order\'s "Processing" Status',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_PAYNEARME_PROCESSING_STATUS_ID',
                    'payment_configuration_info_description' => 'Set the initial processing status of orders made with this payment module to this value.',
                    'payment_configuration_info_sort_order' => '1340',
                    'set_function' => 'tep_cfg_pull_down_order_statuses(',
                    'use_function' => 'tep_get_order_status_name',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => '7',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Payment Message',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_PAYNEARME_MESSAGE',
                    'payment_configuration_info_description' => 'Payment message will show up during checkout process (paynearme).',
                    'payment_configuration_info_sort_order' => '1350',
                    'set_function' => 'tep_cfg_textarea(',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'Thank you for shopping at OffGamers.com.',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Payment Email Message',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_PAYNEARME_EMAIL_MESSAGE',
                    'payment_configuration_info_description' => 'This message will show up in order confirmation e-mail.',
                    'payment_configuration_info_sort_order' => '1360',
                    'set_function' => 'tep_cfg_textarea(',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'Thank you for shopping at OffGamers.com.',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Confirm Complete (in days)',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_PAYNEARME_CONFIRM_COMPLETE',
                    'payment_configuration_info_description' => 'The number of days in which an order made by this payment method is confirm completed.',
                    'payment_configuration_info_sort_order' => '1265',
                    'set_function' => '',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => '0',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Test Mode',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_PAYNEARME_TEST_MODE',
                    'payment_configuration_info_description' => 'Testing Mode?',
                    'payment_configuration_info_sort_order' => '100',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Require Address Information',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_PAYNEARME_MANDATORY_ADDRESS_FIELD',
                    'payment_configuration_info_description' => 'Set address field as required info during checkout.',
                    'payment_configuration_info_sort_order' => '1400',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Customer Payment Info',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_PAYNEARME_CUSTOMER_PAYMENT_INFO',
                    'payment_configuration_info_description' => 'Set to true if this Payment Method required customer input for Payment Information required customer payment info.',
                    'payment_configuration_info_sort_order' => '1410',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Require IC Information',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_PAYNEARME_MANDATORY_IC_FIELD',
                    'payment_configuration_info_description' => 'Set IC field as required info during checkout.',
                    'payment_configuration_info_sort_order' => '1420',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Require Contact Information',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_PAYNEARME_MANDATORY_CONTACT_FIELD',
                    'payment_configuration_info_description' => 'Set Contact field as required info during checkout.',
                    'payment_configuration_info_sort_order' => '1430',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );
            foreach ($install_key_array as $data) {
                tep_db_perform(TABLE_PAYMENT_CONFIGURATION_INFO, $data['info']);
                $payment_conf_id = tep_db_insert_id();

                $data['desc']['payment_configuration_info_id'] = (int) $payment_conf_id;
                tep_db_perform(TABLE_PAYMENT_CONFIGURATION_INFO_DESCRIPTION, $data['desc']);
            }
        }
        return 1;
    }

    function configuration_information_keys() {
        return array('MODULE_PAYMENT_PAYNEARME_ORDER_STATUS_ID',
            'MODULE_PAYMENT_PAYNEARME_PROCESSING_STATUS_ID',
            'MODULE_PAYMENT_PAYNEARME_CONFIRM_COMPLETE',
            'MODULE_PAYMENT_PAYNEARME_TEST_MODE',
            'MODULE_PAYMENT_PAYNEARME_MANDATORY_ADDRESS_FIELD',
            'MODULE_PAYMENT_PAYNEARME_CUSTOMER_PAYMENT_INFO',
            'MODULE_PAYMENT_PAYNEARME_MANDATORY_IC_FIELD',
            'MODULE_PAYMENT_PAYNEARME_MANDATORY_CONTACT_FIELD');
    }

    function multi_lang_configuration_info_keys() {
        return array('MODULE_PAYMENT_PAYNEARME_MESSAGE',
            'MODULE_PAYMENT_PAYNEARME_EMAIL_MESSAGE');
    }

    function merchant_information_keys() {
        include_once(DIR_WS_CLASSES . "payment_methods.php");

        return array(
            'MODULE_PAYMENT_PAYNEARME_ID' => array('field' => 'text', 'mapping' => 'MODULE_PAYMENT_PAYNEARME_LNG_ID'),
            'MODULE_PAYMENT_PAYNEARME_KEY' => array('field' => 'text', 'mapping' => 'MODULE_PAYMENT_PAYNEARME_LNG_KEY'),
            'MODULE_PAYMENT_PAYNEARME_SITE_NAME' => array('field' => 'text', 'mapping' => 'MODULE_PAYMENT_PAYNEARME_LNG_SITE_NAME'),
            'MODULE_PAYMENT_TAX' => array("field" => 'select', 'mapping' => "MODULE_PAYMENT_TAX", "select" => payment_methods::get_tax_option())
        );
    }

    function curl_connect($url, $data = '') {

        $ch = curl_init($url);

        $agent = "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.0)";

        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_VERBOSE, 1);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 2);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, trim($data));
        curl_setopt($ch, CURLOPT_TIMEOUT, 120);
        curl_setopt($ch, CURLOPT_USERAGENT, $agent);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);  //Windows 2003 Compatibility

        if ($this->connect_via_proxy) {
            curl_setopt($ch, CURLOPT_PROXY, 'http://*************:3128');
            curl_setopt($ch, CURLOPT_PROXYUSERPWD, 'devbackend:devonly');
        }

        $response = curl_exec($ch);
        return $response;
    }

    function get_orders_payment_info_file() {
        return DIR_FS_CATALOG_MODULES . 'payment/paynearme/admin/orders.inc.php';
    }

    function get_orders_list_payment_info_file() {
        return DIR_FS_CATALOG_MODULES . 'payment/paynearme/admin/orders_list.inc.php';
    }

    function get_ipn_class_file() {
        return DIR_FS_CATALOG_MODULES . 'payment/paynearme/classes/paynearme_ipn_class.php';
    }

}

?>