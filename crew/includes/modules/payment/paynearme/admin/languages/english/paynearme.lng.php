<?
define('TABLE_HEADING_PAYNEARME_DATE', 'History Date');
define('TABLE_HEADING_PAYNEARME_STATUS', 'Payment Status');
define('TABLE_HEADING_PAYNEARME_DESCRIPTION', 'Description');
define('TABLE_HEADING_PAYNEARME_CHANGED_BY', 'Changed By');

define('TEXT_STATUS_UNKNOWN', 'Unknown');

//Define Merchant Key Language
define('MODULE_PAYMENT_PAYNEARME_LNG_ID', 'PayNearMe ID');
define('MODULE_PAYMENT_PAYNEARME_LNG_KEY', 'PayNearMe Key');
define('MODULE_PAYMENT_PAYNEARME_LNG_SITE_NAME', 'PayNearMe Site Name');
define('MODULE_PAYMENT_PAYNEARME_ORDER_ID', 'PayNearMe Order ID');
define('MODULE_PAYMENT_PAYNEARME_ORDER_STATUS', 'PayNearMe Order Status');
define('MODULE_PAYMENT_TAX', 'Tax');

define('ENTRY_PAYNEARME_TRANSACTION_NUMBER', 'Transaction ID:');
define('ENTRY_PAYNEARME_CURRENCY', 'Currency:');
define('ENTRY_PAYNEARME_AMOUNT', 'Amount:');
define('ENTRY_PAYNEARME_PROCESSING_FEE_CURRENCY', 'Processing Fee Currency:');
define('ENTRY_PAYNEARME_PROCESSING_FEE_AMOUNT', 'Processing Fee Amount:');
define('ENTRY_PAYNEARME_SLIP', 'PayNearMe Slip:');
define('ENTRY_PAYNEARME_ORDER_TRACKING', 'Order Tracking:');
define('ENTRY_PAYNEARME_SLIP_INSTRUCTION', 'Slip Instructions:');
define('ENTRY_PAYNEARME_CARD_INSTRUCTION', 'Card Instructions:');
define('ENTRY_PAYNEARME_STATUS', 'Order Status:');
define('ENTRY_PAYNEARME_ERROR_CODE', 'Tran Err Code:');
define('ENTRY_PAYNEARME_USER_ID', 'User ID:');
define('ENTRY_PAYNEARME_FEE', 'Fee:');
define('ENTRY_PAYNEARME_PAYMENT_METHOD', 'PayNearMe Payment Method:');

define('ENTRY_PAYNEARME_PAYMENT_PRODUCTID','Payment Type');
define('ENTRY_PAYNEARME_PAYMENT_METHOD_ID','Payment Detail');
?>