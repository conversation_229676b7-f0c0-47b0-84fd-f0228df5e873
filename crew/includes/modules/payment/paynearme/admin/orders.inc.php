<?
/*
  	$Id: orders.inc.php,v 1.4 2011/09/29 02:42:29 weichen Exp $
	
  	Developer: <PERSON> (c) 2005 SKC Venture
  	
  	Released under the GNU General Public License
*/

include_once(DIR_FS_CATALOG_MODULES . 'payment/paynearme/admin/languages/' . $language . '/paynearme.lng.php');

$paynearme_trans_info_select_sql = "	SELECT * 
										FROM " . TABLE_PAYNEARME_ORDER . "
										WHERE paynearme_order_id = '" . (int)$oID  . "'";
$paynearme_trans_info_result_sql = tep_db_query($paynearme_trans_info_select_sql);

$paynearme_trans_payment_select_sql = "	SELECT * 
										FROM " . TABLE_PAYNEARME_PAYMENT_INFO . "
										WHERE paynearme_site_order_id = '" . (int)$oID  . "'";
$paynearme_trans_payment_result_sql = tep_db_query($paynearme_trans_payment_select_sql);
$paynearme_trans_payment_row = tep_db_fetch_array($paynearme_trans_payment_result_sql);
$payment_status = $paynearme_trans_payment_row['paynearme_payment_status'];

$paynearme_trans_history_select_sql = " SELECT * 
									    FROM " . TABLE_PAYNEARME_STATUS_HISTORY . " 
									    WHERE paynearme_order_id='" . (int)$oID . "'
									    ORDER BY paynearme_status_history_id";
$paynearme_trans_history_result_sql= tep_db_query($paynearme_trans_history_select_sql);

?>
<table border="0" width="100%" cellspacing="0" cellpadding="2">
<? ob_start(); 

?>
	<tr>
		<td class="main">
      		<table border="0" width="100%" cellspacing="0" cellpadding="2">
      			<tr>
      				<td class="main" width="12%" nowrap><b><?=ENTRY_PAYMENT_METHOD?></b></td>
      				<td class="main">
      					<div>
      						<? if((int)$order->info['payment_methods_id']>0) { ?><div class="<?=((int)$payment_info_array[$order->info['payment_methods_id']]->confirm_complete_days>0?'rp_icon':'nrp_icon')?>"><b><?=((int)$payment_info_array[$order->info['payment_methods_id']]->confirm_complete_days>0?'RP':'NRP')?></b></div> <? } ?>
      						<div style="width:30px; height:15px; background-color:<?=$payment_module_info['display_colour']?>; float:left; vertical-align:top; text-align:center;">&nbsp;</div>
      						<div style="vertical-align: top">
      							&nbsp;&nbsp;
<?
	if ($view_payment_details_permission) {
		if (tep_not_null($last_payment_method) && $last_payment_method['payment_methods_id'] != $order->info['payment_methods_id']) {
			echo '<span class="redIndicator">'.(tep_not_null($payment_module_info['payment_methods_parent_title'])?$payment_module_info['payment_methods_parent_title'] . ' - ' :'') . $payment_module_info['payment_methods_title'].'</span>';
		} else {
			echo (tep_not_null($payment_module_info['payment_methods_parent_title'])?$payment_module_info['payment_methods_parent_title'] . ' - ' :'') . $payment_module_info['payment_methods_title'];
		}
		
		if (tep_not_null($last_payment_method) && $last_payment_method['payment_methods_id'] != $order->info['payment_methods_id']) {
			echo '&nbsp;&nbsp;<span class="redIndicator">(Last Payment Method: ' . (tep_not_null($last_payment_method['parent_payment_methods_title'])? $last_payment_method['parent_payment_methods_title'] . ' - ' : '' ) . $last_payment_method['payment_methods_title'] . ')</span>';
		}
	} else {
		echo (tep_not_null($payment_module_info['payment_methods_parent_title'])?$payment_module_info['payment_methods_parent_title'] . ' - ' :'') . $payment_module_info['payment_methods_title'];
	}
?>
							</div>
      					</div>
      				</td>
				</tr>
			</table>
		</td>
	</tr>
<?
$payment_method_title_info = ob_get_contents();
ob_end_clean();

echo $payment_method_title_info;

if (tep_db_num_rows($paynearme_trans_info_result_sql) || tep_db_num_rows($paynearme_trans_history_result_sql)) {
	if (!$view_payment_details_permission) {
		;
	} else {
		$paynearme_trans_info_row = tep_db_fetch_array($paynearme_trans_info_result_sql);
?>
  	<tr>
    	<td class="main">
      		<table border="0" width="100%" cellspacing="0" cellpadding="2">
      			<tr valign="top">
        			<td width="39%" nowrap>
              			<table border="0" cellspacing="0" cellpadding="2">
              				<tr>
                				<td class="main" valign="top" nowrap><b><?=MODULE_PAYMENT_PAYNEARME_ORDER_ID?>:</b>&nbsp;</td>
                				<td class="main" nowrap><?=$paynearme_trans_info_row['paynearme_pnm_order_id']?></td>
              				</tr>
              				<tr>
                				<td class="main" valign="top" nowrap><b><?=ENTRY_PAYNEARME_USER_ID?></b>&nbsp;</td>
                				<td class="main" nowrap><?=$paynearme_trans_info_row['paynearme_site_customer_id']?></td>
              				</tr>
              				<tr>
			    				<td class="main" valign="top" nowrap><b><?=ENTRY_PAYNEARME_CURRENCY?></b>&nbsp;</td>
			    				<td class="main" nowrap>
			    				<?
			    					if (strtolower($paynearme_trans_info_row["paynearme_order_currency"]) == strtolower($order->info['currency'])) {
			    						echo $paynearme_trans_info_row['paynearme_order_currency'];
			    					} else {
			    						echo '<span class="redIndicator">'.$paynearme_trans_info_row["paynearme_order_currency"].'<br><span class="smallText">Does not match purchase currency.</span></span>';
			    					}
    							?>
    							</td>
			  				</tr>
              				<tr>
                				<td class="main" valign="top" nowrap><b><?=ENTRY_PAYNEARME_AMOUNT?></b>&nbsp;</td>
                				<td class="main" nowrap>
								<?
									$gross_display_text = number_format($paynearme_trans_info_row["paynearme_order_amount"], $currencies->currencies[$order->info["currency"]]['decimal_places'], $currencies->currencies[$order->info["currency"]]['decimal_point'], $currencies->currencies[$order->info["currency"]]['thousands_point']);
			    					if ($currencies->currencies[$order->info["currency"]]['symbol_left'].$gross_display_text.$currencies->currencies[$order->info["currency"]]['symbol_right'] != strip_tags($order->order_totals['ot_total']['text']) ) {
			    						$gross_display_text = '<span class="redIndicator">'.$gross_display_text.'<br><span class="smallText">Does not match purchase amount.</span></span>';
			    					}
			    					echo $gross_display_text;
								?>
                				</td>
              				</tr>
              				<tr>
			    				<td class="main" valign="top" nowrap><b><?=ENTRY_PAYNEARME_PROCESSING_FEE_CURRENCY?></b>&nbsp;</td>
			    				<td class="main" nowrap>
			    				<?
			    					if (tep_not_null($paynearme_trans_payment_row['paynearme_payment_processing_fee_currency'])) {
			    						echo $paynearme_trans_payment_row['paynearme_payment_processing_fee_currency'];
			    					} else if (tep_not_null($paynearme_trans_payment_row['paynearme_authorization_processing_fee_currency'])) {
			    						echo $paynearme_trans_payment_row['paynearme_authorization_processing_fee_currency'];
			    					} else {
            					   	    echo '-';
            					   	}
    							?>
    							</td>
			  				</tr>
              				<tr>
                				<td class="main" valign="top" nowrap><b><?=ENTRY_PAYNEARME_PROCESSING_FEE_AMOUNT?></b>&nbsp;</td>
                				<td class="main" nowrap>
								<?
									if (tep_not_null($paynearme_trans_payment_row['paynearme_payment_processing_fee_amount'])) {
			    						echo $paynearme_trans_payment_row['paynearme_payment_processing_fee_amount'];
			    					} else if (tep_not_null($paynearme_trans_payment_row['paynearme_authorization_processing_fee_amount'])) {
			    						echo $paynearme_trans_payment_row['paynearme_authorization_processing_fee_amount'];
			    					} else {
            					   	    echo '-';
            					   	}
								?>
                				</td>
              				</tr>
              			</table>
        			</td>
        			<td>
        				<table border="0" cellspacing="0" cellpadding="1">
        					<tr>
        						<td colspan="2">
									<table border="1" cellspacing="0" cellpadding="2">
		        						<tr>
		        							<td class="smallText" nowrap><b><?=TABLE_HEADING_PAYNEARME_DATE?></b></td>
		        							<td class="smallText" nowrap><b><?=TABLE_HEADING_PAYNEARME_STATUS?></b></td>
		        							<td class="smallText" nowrap><b><?=TABLE_HEADING_PAYNEARME_DESCRIPTION?></b></td>
		        							<td class="smallText" nowrap><b><?=TABLE_HEADING_PAYNEARME_CHANGED_BY?></b></td>
		        						</tr>
<?		while ($paynearme_payment_status_history_info_row = tep_db_fetch_array($paynearme_trans_history_result_sql)) {
        	echo ' 						<tr>
                							<td class="smallText" nowrap valign="top">'.$paynearme_payment_status_history_info_row['paynearme_date'].'</td>
                							<td class="smallText" nowrap valign="top">'.$paynearme_payment_status_history_info_row['paynearme_status'].'</td>
                							<td class="smallText" nowrap valign="top">'.$paynearme_payment_status_history_info_row['paynearme_description'].'</td>
                							<td class="smallText" nowrap valign="top">'.$paynearme_payment_status_history_info_row['paynearme_changed_by'].'</td>
                						</tr>';
     	}
?>
									</table>
								</td>
        					</tr>
        				</table>
        			</td>
        			<td>
        				<table border="0" cellspacing="0" cellpadding="1">
        					<tr>
                				<td class="main" valign="top" nowrap><b><?=TABLE_HEADING_PAYNEARME_STATUS?>:</b>&nbsp;</td>
                				<td class="main" nowrap><? if(tep_not_null($payment_status)){ echo $payment_status; } else { echo "-"; }?></td>
              				</tr>
              				<tr>
                				<td class="main" valign="top" nowrap><b><?=MODULE_PAYMENT_PAYNEARME_ORDER_STATUS?>:</b>&nbsp;</td>
                				<td class="main" nowrap><?=$paynearme_trans_info_row['paynearme_order_status']?></td>
              				</tr>
              				<tr>
                				<td class="main" valign="top" nowrap><b><?=ENTRY_PAYNEARME_ORDER_TRACKING?></b>&nbsp;</td>
                				<td class="main" nowrap>
                					<? 
                						if ($paynearme_trans_info_row['paynearme_order_tracking_url']) {
                						   echo '<a target="_new" href="'.	$paynearme_trans_info_row['paynearme_order_tracking_url'] . '">';
                						   echo "Order Tracking";
                						   echo '</a>';
                					   	}
                					?>
                				</td>
              				</tr>
<?		if ($paynearme_trans_info_row['paynearme_order_status'] == "open") {
					echo ' 	<tr>
    							<td class="main" valign="top" nowrap><b>'.ENTRY_PAYNEARME_SLIP_INSTRUCTION.'</b>&nbsp;</td>
    							<td class="main" nowrap>';
    								if (tep_not_null($paynearme_trans_payment_row['paynearme_slip_pdf_url'])) {
	        						   echo '<a target="_new" href="'.	$paynearme_trans_payment_row['paynearme_slip_pdf_url'] . '">';
	        						   echo "Slip Instructions PDF";
	        						   echo '</a>';
            					   	} else {
            					   	   echo '-';
            					   	}
    				echo 		'</td>
    						</tr>
              				<tr>
              					<td class="main" valign="top" nowrap><b>'.ENTRY_PAYNEARME_CARD_INSTRUCTION.'</b>&nbsp;</td>
    							<td class="main" nowrap>';
    								if (tep_not_null($paynearme_trans_payment_row['paynearme_card_pdf_url'])) {
            						   echo '<a target="_new" href="'.	$paynearme_trans_payment_row['paynearme_card_pdf_url'] . '">';
            						   echo "Card Instructions PDF";
            						   echo '</a>';
            					   	} else {
            					   	   echo '-';
            					   	}
    				echo 		'</td>
    						</tr>';
     	}
?>
        				</table>
        			</td>
  				</tr>
      		</table>
   		</td>
  	</tr>
<?
	}
}
?>
</table>