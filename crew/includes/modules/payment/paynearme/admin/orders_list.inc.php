<?
include_once(DIR_FS_CATALOG_MODULES . 'payment/paynearme/admin/languages/' . $language . '/paynearme.lng.php');

$paynearme_trans_select_sql = "SELECT paynearme_pnm_order_id FROM " . TABLE_PAYNEARME_ORDER . " WHERE paynearme_order_id = '" . $order_obj->orders_id . "'";
$paynearme_trans_result_sql= tep_db_query($paynearme_trans_select_sql);
$paynearme_trans_row = tep_db_fetch_array($paynearme_trans_result_sql);

$paynearme_trans_info_select_sql = "SELECT paynearme_payment_status FROM " . TABLE_PAYNEARME_PAYMENT_INFO . " WHERE paynearme_site_order_id = '" . $order_obj->orders_id . "'";
$paynearme_trans_info_result_sql= tep_db_query($paynearme_trans_info_select_sql);
?>
<table border="0" width="100%" cellspacing="0" cellpadding="2">
	<tr>
		<td class="subInvoiceBoxHeading">
			<div style="border-bottom: 1px solid #996600; border-top: 1px solid #996600; padding: 2px;">Payment Details</div>
		</td>
	</tr>
<?
if ($paynearme_trans_info_row = tep_db_fetch_array($paynearme_trans_info_result_sql)) {
	$paynearme_payment_status = array(	'decline' => 'Decline',
										'pending' => 'Pending',
										'authorization' => 'Authorization',
										'payment' => 'Payment');
?>
	<tr>
    	<td class="main">
      		<table border="0" width="100%" cellspacing="0" cellpadding="2">
      			<tr valign="top">
        			<td width="50%">
              			<table border="0" cellspacing="0" cellpadding="2">
              				<tr>
                				<td class="invoiceRecords" valign="top" nowrap><?=MODULE_PAYMENT_PAYNEARME_ORDER_ID?></td>
                				<td class="invoiceRecords" valign="top">:&nbsp;</td>
                				<td class="invoiceRecords" nowrap><?=$paynearme_trans_row['paynearme_pnm_order_id']?></td>
              				</tr>
              				<tr>
                				<td class="invoiceRecords" valign="top" nowrap><?=TABLE_HEADING_PAYNEARME_STATUS?></td>
                				<td class="invoiceRecords" valign="top">:&nbsp;</td>
                				<td class="invoiceRecords" nowrap><?=(isset($paynearme_payment_status[$paynearme_trans_info_row['paynearme_payment_status']]) ? $paynearme_payment_status[$paynearme_trans_info_row['paynearme_payment_status']] : 'Pending')?></td>
              				</tr>
              			</table>
        			</td>
      			</tr>
      		</table>
   		</td>
  	</tr>
<?
} else {
?>
	<tr>
		<td class="invoiceRecords">No further payment information is available.</td>
	</tr>
<?
}
?>