<?

/*
  $Id: paypal.php,v 1.61 2015/10/21 09:36:29 sionghuat.chng Exp $

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  DevosC, Developing open source Code
  http://www.devosc.com

  Copyright (c) 2003 osCommerce
  Copyright (c) 2004 DevosC.com

  Released under the GNU General Public License
 */

class paypal {

	var $code, $title, $description, $enabled;
	var $payment_methods_id, $filename;

	// class constructor
	function paypal($pm_id = '') {
		global $order, $languages_id, $default_languages_id, $defCurr;

		//Basic info
		$this->payment_methods_id = 0;
		$this->payment_methods_parent_id = 0;

		$this->code = 'paypal';
		$this->filename = 'paypal.php';
		$this->title = $this->code;

		$this->api_version = '3.0';
		$this->receive_status = 0;
		$this->preferred = true;
		$this->connect_via_proxy = false;

		$payment_methods_select_sql = "	SELECT payment_methods_parent_id, payment_methods_code, 
												payment_methods_types_id, 
												payment_methods_receive_status_mode, payment_methods_id, 
												payment_methods_title, payment_methods_sort_order, 
												payment_methods_receive_status, payment_methods_legend_color, 
												payment_methods_logo, payment_methods_send_status 
										FROM " . TABLE_PAYMENT_METHODS . " 
										WHERE 1 ";
		if ((int) $pm_id > 0) {
			$payment_methods_select_sql .= "	AND payment_methods_id = '" . (int) $pm_id . "'";
		} else {
			$payment_methods_select_sql .= "	AND payment_methods_filename = '" . tep_db_input($this->filename) . "'";
		}

		$payment_methods_result_sql = tep_db_query($payment_methods_select_sql);
		if ($payment_methods_row = tep_db_fetch_array($payment_methods_result_sql)) {
			$pm_desc_title_select_sql = "	SELECT payment_methods_description_title 
											FROM " . TABLE_PAYMENT_METHODS_DESCRIPTION . " 
											WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "' 
												AND (IF (languages_id = '" . tep_db_input($languages_id) . "', 1, IF ((	SELECT COUNT(payment_methods_id) > 0 
																														FROM " . TABLE_PAYMENT_METHODS_DESCRIPTION . " 
																														WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "' 
																															AND languages_id = '" . (int) $languages_id . "'), 0, languages_id = 1)))";
			$pm_desc_title_result_sql = tep_db_query($pm_desc_title_select_sql);
			$pm_desc_title_row = tep_db_fetch_array($pm_desc_title_result_sql);

			$this->payment_methods_id = (int) $payment_methods_row['payment_methods_id'];
			$this->payment_methods_parent_id = (int) $payment_methods_row['payment_methods_parent_id'];
			$this->code = $payment_methods_row['payment_methods_code'];
			$this->title = $payment_methods_row['payment_methods_title']; //MODULE_PAYMENT_PAYPAL_TEXT_TITLE;
			$this->display_title = $pm_desc_title_row['payment_methods_description_title']; //MODULE_PAYMENT_PAYPAL_TEXT_DISPLAY_TITLE; 	// if title to be display for payment method is different from payment method value
			$this->sort_order = $payment_methods_row['payment_methods_sort_order']; //MODULE_PAYMENT_PAYPAL_SORT_ORDER;
			$this->enabled = $payment_methods_row['payment_methods_receive_status_mode'];
			$this->legend_display_colour = $payment_methods_row['payment_methods_legend_color'];
			$this->receive_status = $payment_methods_row['payment_methods_receive_status'];
			$this->description = $this->display_title;
			$this->paypalCurrencies = $this->get_support_currencies($this->payment_methods_id);
			$this->defCurr = DEFAULT_CURRENCY;

			// SEND PAYMENT
			$this->send_enabled = $payment_methods_row['payment_methods_send_status'];

			//load 'TABLE_PAYMENT_CONFIGURATION_INFO'
			$configuration_setting_array = $this->load_pm_setting();

			$this->currency = $configuration_setting_array['MODULE_PAYMENT_PAYPAL_CURRENCY'];
			//$this->zone = $configuration_setting_array['MODULE_PAYMENT_PAYPAL_ZONE'];
			$this->processing_status_id = $configuration_setting_array['MODULE_PAYMENT_PAYPAL_PROCESSING_STATUS_ID'];
			$this->order_status_id = $configuration_setting_array['MODULE_PAYMENT_PAYPAL_ORDER_STATUS_ID'];
			$this->invoice_required = $configuration_setting_array['MODULE_PAYMENT_PAYPAL_INVOICE_REQUIRED'];
			$this->cs = $configuration_setting_array['MODULE_PAYMENT_PAYPAL_CS'];
			$this->processing_logo = $configuration_setting_array['MODULE_PAYMENT_PAYPAL_PROCESSING_LOGO'];
			$this->store_logo = $configuration_setting_array['MODULE_PAYMENT_PAYPAL_STORE_LOGO'];
			$this->page_style = $configuration_setting_array['MODULE_PAYMENT_PAYPAL_PAGE_STYLE'];
			$this->no_note = $configuration_setting_array['MODULE_PAYMENT_PAYPAL_NO_NOTE'];
			$this->method = $configuration_setting_array['MODULE_PAYMENT_PAYPAL_METHOD'];
			$this->shipping_allowed = $configuration_setting_array['MODULE_PAYMENT_PAYPAL_SHIPPING_ALLOWED'];
			$this->ipn_debug = $configuration_setting_array['MODULE_PAYMENT_PAYPAL_IPN_DEBUG'];
			$this->ipn_digest_key = $configuration_setting_array['MODULE_PAYMENT_PAYPAL_IPN_DIGEST_KEY'];
			$this->ipn_test_mode = $configuration_setting_array['MODULE_PAYMENT_PAYPAL_IPN_TEST_MODE'];
			$this->ipn_cart_test = $configuration_setting_array['MODULE_PAYMENT_PAYPAL_IPN_CART_TEST'];
			$this->ipn_debug_email = $configuration_setting_array['MODULE_PAYMENT_PAYPAL_IPN_DEBUG_EMAIL'];
			$this->domain = $configuration_setting_array['MODULE_PAYMENT_PAYPAL_DOMAIN'];
			$this->rm = $configuration_setting_array['MODULE_PAYMENT_PAYPAL_RM'];
			$this->message = $configuration_setting_array['MODULE_PAYMENT_PAYPAL_MESSAGE'];
			$this->email_message = $configuration_setting_array['MODULE_PAYMENT_PAYPAL_EMAIL_MESSAGE'];
			$this->confirm_complete_days = $configuration_setting_array['MODULE_PAYMENT_PAYPAL_CONFIRM_COMPLETE'];
			$this->verified_email_notification = $configuration_setting_array['MODULE_PAYMENT_PAYPAL_VERIFIED_EMAIL_NOTIFICATION'];
			$this->verified_email_notification_status = $configuration_setting_array['MODULE_PAYMENT_PAYPAL_VERIFIED_EMAIL_NOTIFICATION_STATUS'];
			$this->ewp = $configuration_setting_array['MODULE_PAYMENT_PAYPAL_EWP'];
			$this->require_address_information = $configuration_setting_array['MODULE_PAYMENT_PAYPAL_MANDATORY_ADDRESS_FIELD'];
			$this->require_ic_information = $configuration_setting_array['MODULE_PAYMENT_PAYPAL_MANDATORY_IC_FIELD'];
			$this->require_contact_information = $configuration_setting_array['MODULE_PAYMENT_PAYPAL_MANDATORY_CONTACT_FIELD'];

			$this->customer_payment_info = $configuration_setting_array['MODULE_PAYMENT_PAYPAL_CUSTOMER_PAYMENT_INFO'];

			$this->use_ewp_submission = (($this->ewp == 'Yes') ? true : false);

			if ((int) $this->order_status_id > 0) {
				$this->order_status = (int) $this->order_status_id;
			}
			//if (is_object($order)) $this->update_status();

			$this->form_paypal_url = 'https://' . $this->domain . '/cgi-bin/webscr';
			$this->cc_explain_url = tep_href_link('popup_paypal.php', '', 'SSL');

			if ($this->ipn_test_mode == 'On') {
				$this->api_endpoint_url = 'https://api-3t.sandbox.paypal.com/nvp';
				$this->rest_api_endpoint_url = 'https://api-m.sandbox.paypal.com/';
				$this->ipn_validate_url = 'https://www.sandbox.paypal.com/cgi-bin/webscr';
			} else {
				$this->api_endpoint_url = 'https://api-3t.paypal.com/nvp';
				$this->rest_api_endpoint_url = 'https://api-m.paypal.com/';
				$this->ipn_validate_url = 'https://ipnpb.paypal.com/cgi-bin/webscr';
			}

			/* # private key file to use
			  $this->private_key_file = DIR_WS_MODULES . 'payment/paypal/cert/paypal_private_97d5ac.pem';

			  # public certificate file to use
			  $this->public_key_file = DIR_WS_MODULES . 'payment/paypal/cert/paypal_public_de9707.pem';

			  # Paypal's public certificate, need to download from PayPal's Profile page
			  $this->paypal_public_key_file = DIR_WS_MODULES . 'payment/paypal/cert/paypal_cert.pem';
			 */

			# path to the openssl binary
			$this->openssl_path = "/usr/bin/openssl";

			$this->email_footer = str_replace(array("<br>", "<BR>", "\r\n"), array("\n", "\n", ''), $this->email_message);
			$this->order_processing_status = 7;
			$this->auto_cancel_period = 60; // In minutes

			if (isset($this->verified_email_notification_status) &&
					tep_not_null($this->verified_email_notification_status)) {
				$this->email_verification_notification_status = explode(',', $this->verified_email_notification_status);
			} else {
				$this->email_verification_notification_status = array();
			}
		}
	}

	function javascript_validation() {
		return false;
	}

	function selection() {
		global $languages_id, $default_languages_id;

		$selection_array = array();

		$payment_methods_select_sql = "	SELECT payment_methods_id, payment_methods_code, payment_methods_types_id, payment_methods_logo, payment_methods_receive_status_mode 
										FROM " . TABLE_PAYMENT_METHODS . " 
										WHERE payment_methods_receive_status = 1 
											AND payment_methods_receive_status_mode <> 0 
											AND payment_methods_parent_id = '" . (int) $this->payment_methods_id . "' 
										ORDER BY payment_methods_sort_order";
		$payment_methods_result_sql = tep_db_query($payment_methods_select_sql);
		while ($payment_methods_row = tep_db_fetch_array($payment_methods_result_sql)) {
			$pm_desc_title_select_sql = "	SELECT payment_methods_description_title 
											FROM " . TABLE_PAYMENT_METHODS_DESCRIPTION . " 
											WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "' 
												AND (IF (languages_id = '" . tep_db_input($languages_id) . "', 1, IF ((	SELECT COUNT(payment_methods_id) > 0 
																														FROM " . TABLE_PAYMENT_METHODS_DESCRIPTION . " 
																														WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "' 
																															AND languages_id = '" . (int) $languages_id . "'), 0, languages_id = 1)))";
			$pm_desc_title_result_sql = tep_db_query($pm_desc_title_select_sql);
			$pm_desc_title_row = tep_db_fetch_array($pm_desc_title_result_sql);

			$pm_array = $this->load_pm_setting(1, $payment_methods_row['payment_methods_id']);

			$selection_array[] = array('payment_gateway_code' => $this->code,
				'payment_methods_receive_status_mode' => $payment_methods_row['payment_methods_receive_status_mode'],
				'payment_methods_id' => $payment_methods_row['payment_methods_id'],
				'payment_methods_code' => $payment_methods_row['payment_methods_code'],
				'payment_methods_types_id' => $payment_methods_row['payment_methods_types_id'],
				'payment_methods_parent_id' => $this->payment_methods_id,
				'payment_methods_logo' => $payment_methods_row['payment_methods_logo'],
				'payment_methods_description_title' => $pm_desc_title_row['payment_methods_description_title'],
				'currency' => $this->get_support_currencies($payment_methods_row['payment_methods_id']),
				'show_billing_address' => $pm_array['MODULE_PAYMENT_PAYPAL_MANDATORY_ADDRESS_FIELD'],
				'confirm_complete_days' => $pm_array['MODULE_PAYMENT_PAYPAL_CONFIRM_COMPLETE'],
				'show_contact_number' => $pm_array['MODULE_PAYMENT_PAYPAL_MANDATORY_CONTACT_FIELD'],
				'show_ic' => $pm_array['MODULE_PAYMENT_PAYPAL_MANDATORY_IC_FIELD']
			);

			if ($payment_methods_row['payment_methods_receive_status_mode'] == '-1') {
				$pm_status_message_select = "	SELECT payment_methods_status_message 
												FROM " . TABLE_PAYMENT_METHODS_STATUS_DESCRIPTION . " 
												WHERE payment_methods_mode = 'RECEIVE' 
													AND payment_methods_status = '-1' 
													AND languages_id = '" . (int) $languages_id . "' 
													AND payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "'";
				$pm_status_message_result = tep_db_query($pm_status_message_select);
				$pm_status_message_row = tep_db_fetch_array($pm_status_message_result);
				$selection_array[sizeof($selection_array) - 1]['payment_methods_status_message'] = $pm_status_message_row['payment_methods_status_message'];
			}
		}

		return $selection_array;
	}

	function set_support_currencies($currency_array) {
		$this->paypalCurrencies = $currency_array;
	}

	function get_support_currencies($pm_id, $by_zone = true) {
		global $zone_info_array;

		$default_currency = '';
		$support_currencies_array = array();

		$currency_code_select_sql = "	SELECT currency_code, payment_methods_instance_default 
    									FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " 
    									WHERE payment_methods_id = '" . (int) $pm_id . "' 
    									ORDER BY currency_code";
		$currency_code_result_sql = tep_db_query($currency_code_select_sql);

		if (tep_db_num_rows($currency_code_result_sql) > 0) {
			while ($currency_code_row = tep_db_fetch_array($currency_code_result_sql)) {
				$support_currencies_array[] = $currency_code_row['currency_code'];

				if ($currency_code_row['payment_methods_instance_default'] == 1) {
					$default_currency = $currency_code_row['currency_code'];
				}
			}
		} else {
			if ($this->payment_methods_parent_id > 0) {
				$payment_methods_parent_id = $this->payment_methods_parent_id;
			} else {
				$payment_methods_parent_id = $this->payment_methods_id;
			}

			$parent_currency_code_select_sql = "	SELECT currency_code, payment_methods_instance_default 
			    									FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " 
			    									WHERE payment_methods_id = '" . (int) $payment_methods_parent_id . "' 
			    									ORDER BY currency_code";
			$parent_currency_code_result_sql = tep_db_query($parent_currency_code_select_sql);
			while ($parent_currency_code_row = tep_db_fetch_array($parent_currency_code_result_sql)) {
				$support_currencies_array[] = $parent_currency_code_row['currency_code'];

				if ($parent_currency_code_row['payment_methods_instance_default'] == 1) {
					$default_currency = $parent_currency_code_row['currency_code'];
				}
			}
		}

		if (tep_not_null($default_currency)) {
			$this->defCurr = $default_currency;
		}

		if ($by_zone) {
			if (isset($zone_info_array)) {
				$support_currencies_array = array_intersect($zone_info_array[3]->zone_currency_id, $support_currencies_array);
				if (count($support_currencies_array)) {
					array_multisort($support_currencies_array);
				} else {
					$support_currencies_array = array($default_currency);
				}
			} else {
				$support_currencies_array = array($default_currency);
			}
		} else {
			$this->set_support_currencies($support_currencies_array);
			// All found currencies is supported. Normally this is used for backend script such as IPN call
		}

		return $support_currencies_array;
	}

	function get_require_address_information() {
		return $this->require_address_information;
	}

	function get_confirm_complete_days() {
		return $this->confirm_complete_days;
	}

	function get_pm_info() {
		return $this->paypal_message;
	}

	function get_email_pm_info() {
		return $this->email_message;
	}

	function draw_confirm_button() {
		return tep_image_button2('red', 'javascript:document.checkout_confirmation.submit()', MODULE_PAYMENT_PAYPAL_TEXT_CART, 200);
	}

	function get_confirm_button_caption() {
		return MODULE_PAYMENT_PAYPAL_TEXT_TITLE;
	}

	function pre_confirmation_check() {
		global $currency, $currencies;
		$supported_currency_str_array = array();

		if (!in_array($currency, $this->paypalCurrencies)) {
			
		}

		return false;
	}

	function confirmation() {
		return array('title' => $this->paypal_message);
	}

	function currency() {
		global $currency;
		if (!isset($this->_currency)) {
			if ($this->currency == 'Selected Currency') {
				$this->_currency = $currency;
			} else {
				$this->_currency = substr($this->currency, 5);
			}

			if (!in_array($this->_currency, $this->paypalCurrencies)) {
				$this->_currency = 'USD';
			}
		}
		return $this->_currency;
	}

	function is_supported_currency($selected_currency) {
		return (in_array($selected_currency, $this->paypalCurrencies) ? true : false);
	}

	function process_button() {
		return false;
	}

	function before_process() {
		if (!class_exists('paypal_order'))
			include_once(DIR_WS_MODULES . 'payment/paypal/classes/paypal_order.class.php');
		if (paypal_order::check_order_status()) {
			tep_redirect(tep_href_link(FILENAME_SHOPPING_CART, '', 'SSL'));
		} else {
			include(DIR_WS_MODULES . 'payment/paypal/catalog/checkout_process.inc.php');
		}
		exit;
	}

	function after_process() {
		return false;
	}

	function output_error() {
		return false;
	}

	function check() {
		if (!isset($this->_check)) {
			$payment_methods_select_sql = "	SELECT payment_methods_id
											FROM " . TABLE_PAYMENT_METHODS . " as pm
											WHERE pm.payment_methods_id = '" . (int) $this->payment_methods_id . "'";
			$payment_methods_result_sql = tep_db_query($payment_methods_select_sql);
			$this->_check = tep_db_num_rows($payment_methods_result_sql);
		}
		return $this->_check;
	}

	function load_pm_setting($language_id = 1, $pm_id = '') {
		$pm_setting_array = array();
		//load value from DB

		if (tep_not_null($pm_id)) {
			$payment_method_id = $pm_id;

			$payment_methods_parent_id_select_sql = "	SELECT payment_methods_parent_id 
														FROM " . TABLE_PAYMENT_METHODS . " 
														WHERE payment_methods_id = '" . (int) $pm_id . "'";
			$payment_methods_parent_id_result_sql = tep_db_query($payment_methods_parent_id_select_sql);
			$payment_methods_parent_id_row = tep_db_fetch_array($payment_methods_parent_id_result_sql);

			$payment_gateway_id = $payment_methods_parent_id_row['payment_methods_parent_id'];
		} else {
			$payment_method_id = $this->payment_methods_id;
			$payment_gateway_id = $this->payment_methods_parent_id;
		}

		$payment_configuration_info_select_sql = "	SELECT pci.payment_configuration_info_key, pcid.payment_configuration_info_value
													FROM " . TABLE_PAYMENT_CONFIGURATION_INFO . " as pci
													LEFT JOIN " . TABLE_PAYMENT_CONFIGURATION_INFO_DESCRIPTION . " as pcid 
														ON pci.payment_configuration_info_id = pcid.payment_configuration_info_id
															AND pcid.languages_id = '" . (int) $language_id . "'
													WHERE pci.payment_methods_id = '" . (int) $payment_method_id . "'
													ORDER BY pci.payment_configuration_info_sort_order";
		$payment_configuration_info_result_sql = tep_db_query($payment_configuration_info_select_sql);
		if (tep_db_num_rows($payment_configuration_info_result_sql)) {
			while ($payment_configuration_info_row = tep_db_fetch_array($payment_configuration_info_result_sql)) {
				$pm_setting_array[$payment_configuration_info_row['payment_configuration_info_key']] = $payment_configuration_info_row['payment_configuration_info_value'];
			}
		} else if ((int) $payment_gateway_id > 0) { //load value from PG
			$payment_configuration_info_select_sql = "	SELECT pci.payment_configuration_info_key, pcid.payment_configuration_info_value
														FROM " . TABLE_PAYMENT_CONFIGURATION_INFO . " as pci
														LEFT JOIN " . TABLE_PAYMENT_CONFIGURATION_INFO_DESCRIPTION . " as pcid 
															ON pci.payment_configuration_info_id = pcid.payment_configuration_info_id
																AND pcid.languages_id = '" . (int) $language_id . "'
														WHERE pci.payment_methods_id = '" . (int) $payment_gateway_id . "'
														ORDER BY pci.payment_configuration_info_sort_order";
			$payment_configuration_info_result_sql = tep_db_query($payment_configuration_info_select_sql);
			while ($payment_configuration_info_row = tep_db_fetch_array($payment_configuration_info_result_sql)) {
				$pm_setting_array[$payment_configuration_info_row['payment_configuration_info_key']] = $payment_configuration_info_row['payment_configuration_info_value'];
			}
		}

		return $pm_setting_array;
	}

	function get_merchant_account($selected_currency) {
		$payment_methods_instance_id = 0;
		$payment_gateway_instance_select_sql = "SELECT pmi.payment_methods_instance_id, pmi.payment_methods_instance_follow_default, pmi.currency_code 
				 								FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " as pmi
				 								WHERE pmi.payment_methods_id = '" . (int) $this->payment_methods_id . "'
				 								LIMIT 1";
		$payment_gateway_instance_result_sql = tep_db_query($payment_gateway_instance_select_sql);
		if (tep_db_num_rows($payment_gateway_instance_result_sql)) {
			$payment_gateway_instance_select_sql = "SELECT pmi.payment_methods_instance_id, pmi.payment_methods_instance_follow_default 
					 								FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " as pmi
					 								WHERE pmi.currency_code = '" . tep_db_input($selected_currency) . "' 
					 									AND pmi.payment_methods_id = '" . (int) $this->payment_methods_id . "'";
			$payment_gateway_instance_result_sql = tep_db_query($payment_gateway_instance_select_sql);
			if ($payment_gateway_instance_row = tep_db_fetch_array($payment_gateway_instance_result_sql)) {
				if ((int) $payment_gateway_instance_row['payment_methods_instance_follow_default'] > 0) { // Follow Default Currency Setting
					$payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_follow_default'];
				} else { // Has own setting
					$payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_id'];
				}
			}
		} else {
			$payment_gateway_instance_select_sql = "SELECT pmi.payment_methods_instance_id, pmi.payment_methods_instance_follow_default 
					 								FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " as pmi
					 								INNER JOIN " . TABLE_PAYMENT_METHODS . " as pm
					 									ON pm.payment_methods_parent_id = pmi.payment_methods_id
					 								WHERE pmi.currency_code = '" . tep_db_input($selected_currency) . "' 
					 									AND pm.payment_methods_id = '" . (int) $this->payment_methods_id . "'";
			$payment_gateway_instance_result_sql = tep_db_query($payment_gateway_instance_select_sql);
			if ($payment_gateway_instance_row = tep_db_fetch_array($payment_gateway_instance_result_sql)) {
				if ((int) $payment_gateway_instance_row['payment_methods_instance_follow_default'] > 0) {
					$payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_follow_default'];
				} else {
					$payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_id'];
				}
			}
		}

		$payment_gateway_instance_setting_select_sql = "SELECT pmis.payment_methods_instance_setting_key, pmis.payment_methods_instance_setting_value 
						 								FROM " . TABLE_PAYMENT_METHODS_INSTANCE_SETTING . " as pmis
						 								WHERE pmis.payment_methods_instance_id = '" . (int) $payment_methods_instance_id . "'";
		$payment_gateway_instance_setting_result_sql = tep_db_query($payment_gateway_instance_setting_select_sql);
		while ($payment_gateway_instance_setting_row = tep_db_fetch_array($payment_gateway_instance_setting_result_sql)) {
			switch ($payment_gateway_instance_setting_row['payment_methods_instance_setting_key']) {
				case 'MODULE_PAYMENT_PAYPAL_ID':
					$this->paypal_id = $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];
					break;
				case 'MODULE_PAYMENT_PAYPAL_EWP_CERT_ID':
					$this->ewp_cert_id = $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];
					break;
				case 'MODULE_PAYMENT_PAYPAL_BUSINESS_ID':
					$this->business_id = $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];
					break;
				case 'MODULE_PAYMENT_PAYPAL_API_USERNAME':
					$this->api_username = $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];
					break;
				case 'MODULE_PAYMENT_PAYPAL_API_PASSWORD':
					$this->api_password = $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];
					break;
				case 'MODULE_PAYMENT_PAYPAL_API_SIGNATURE':
					$this->api_signature = $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];
					break;
				case 'MODULE_PAYMENT_PAYPAL_PRIVATE_KEY_FILE':
					$this->private_key_file = DIR_WS_MODULES . 'payment/paypal/cert/' . $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];
					break;
				case 'MODULE_PAYMENT_PAYPAL_PUBLIC_KEY_FILE':
					$this->public_key_file = DIR_WS_MODULES . 'payment/paypal/cert/' . $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];
					break;
				case 'MODULE_PAYMENT_PAYPAL_PAYPAL_PUBLIC_KEY_FILE':
					$this->paypal_public_key_file = DIR_WS_MODULES . 'payment/paypal/cert/' . $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];
					break;
			}
		}
	}

	function get_merchant_outgoing_account($selected_currency, $portal = '') {
		//temporary 'hack' solution for g2g separate account values
        if( $portal == 'G2G'){
            $this->outgoing_api_username = G2G_PAYPAL_MASS_PAY_USERNAME;
            $this->outgoing_api_password = G2G_PAYPAL_MASS_PAY_PASSWORD;
            $this->outgoing_api_signature = G2G_PAYPAL_MASS_PAY_SIGNATURE;
			return;
		}
		$payment_methods_outgoing_instance_id = 0;
		$payment_gateway_instance_select_sql = "SELECT pmi.payment_methods_outgoing_instance_id, pmi.payment_methods_outgoing_instance_follow_default, pmi.currency_code 
				 								FROM " . TABLE_PAYMENT_METHODS_OUTGOING_INSTANCE . " as pmi
				 								WHERE pmi.payment_methods_id = '" . (int) $this->payment_methods_id . "'
				 								LIMIT 1";
		$payment_gateway_instance_result_sql = tep_db_query($payment_gateway_instance_select_sql);
		if (tep_db_num_rows($payment_gateway_instance_result_sql)) {
			$payment_gateway_instance_select_sql = "SELECT pmi.payment_methods_outgoing_instance_id, pmi.payment_methods_outgoing_instance_follow_default 
					 								FROM " . TABLE_PAYMENT_METHODS_OUTGOING_INSTANCE . " as pmi
					 								WHERE pmi.currency_code = '" . tep_db_input($selected_currency) . "' 
					 									AND pmi.payment_methods_id = '" . (int) $this->payment_methods_id . "'";
			$payment_gateway_instance_result_sql = tep_db_query($payment_gateway_instance_select_sql);
			if ($payment_gateway_instance_row = tep_db_fetch_array($payment_gateway_instance_result_sql)) {
				if ((int) $payment_gateway_instance_row['payment_methods_outgoing_instance_follow_default'] > 0) { // Follow Default Currency Setting
					$payment_methods_outgoing_instance_id = (int) $payment_gateway_instance_row['payment_methods_outgoing_instance_follow_default'];
				} else { // Has own setting
					$payment_methods_outgoing_instance_id = (int) $payment_gateway_instance_row['payment_methods_outgoing_instance_id'];
				}
			}
		} else {
			$payment_gateway_instance_select_sql = "SELECT pmi.payment_methods_outgoing_instance_id, pmi.payment_methods_outgoing_instance_follow_default 
					 								FROM " . TABLE_PAYMENT_METHODS_OUTGOING_INSTANCE . " as pmi
					 								INNER JOIN " . TABLE_PAYMENT_METHODS . " as pm
					 									ON pm.payment_methods_parent_id = pmi.payment_methods_id
					 								WHERE pmi.currency_code = '" . tep_db_input($selected_currency) . "' 
					 									AND pm.payment_methods_id = '" . (int) $this->payment_methods_id . "'";
			$payment_gateway_instance_result_sql = tep_db_query($payment_gateway_instance_select_sql);
			if ($payment_gateway_instance_row = tep_db_fetch_array($payment_gateway_instance_result_sql)) {
				if ((int) $payment_gateway_instance_row['payment_methods_outgoing_instance_follow_default'] > 0) {
					$payment_methods_outgoing_instance_id = (int) $payment_gateway_instance_row['payment_methods_outgoing_instance_follow_default'];
				} else {
					$payment_methods_outgoing_instance_id = (int) $payment_gateway_instance_row['payment_methods_outgoing_instance_id'];
				}
			}
		}

		$payment_gateway_instance_setting_select_sql = "	SELECT pmis.payment_methods_outgoing_instance_setting_key, pmis.payment_methods_outgoing_instance_setting_value 
								 								FROM " . TABLE_PAYMENT_METHODS_OUTGOING_INSTANCE_SETTING . " as pmis
								 								WHERE pmis.payment_methods_outgoing_instance_id = '" . (int) $payment_methods_outgoing_instance_id . "'";
		$payment_gateway_instance_setting_result_sql = tep_db_query($payment_gateway_instance_setting_select_sql);
		while ($payment_gateway_instance_setting_row = tep_db_fetch_array($payment_gateway_instance_setting_result_sql)) {
			switch ($payment_gateway_instance_setting_row['payment_methods_outgoing_instance_setting_key']) {
				case 'MODULE_PAYMENT_OUTGOING_PAYPAL_API_USERNAME':
					$this->outgoing_api_username = $payment_gateway_instance_setting_row['payment_methods_outgoing_instance_setting_value'];
					break;
				case 'MODULE_PAYMENT_OUTGOING_PAYPAL_API_PASSWORD':
					$this->outgoing_api_password = $payment_gateway_instance_setting_row['payment_methods_outgoing_instance_setting_value'];
					break;
				case 'MODULE_PAYMENT_OUTGOING_PAYPAL_API_SIGNATURE':
					$this->outgoing_api_signature = $payment_gateway_instance_setting_row['payment_methods_outgoing_instance_setting_value'];
					break;
			}
		}
	}

	function install() {
		$install_configuration_flag = true;

		if ($this->payment_methods_id > 0) {
			if ($this->receive_status != 1) {
				$update_receive_status_sql_data = array("payment_methods_receive_status" => 1);
				tep_db_perform(TABLE_PAYMENT_METHODS, $update_receive_status_sql_data, 'update', "payment_methods_id = '" . (int) $this->payment_methods_id . "'");
			} else {
				$install_configuration_flag = false;
			}
		} else {
			$install_payment_gateway_data_sql = array('payment_methods_code' => $this->code,
				'payment_methods_send_status' => 0,
				'payment_methods_title' => $this->code,
				'payment_methods_types_id' => 0,
				'payment_methods_parent_id' => 0,
				'payment_methods_legend_color' => '#0066FF',
				'payment_methods_sort_order' => 1,
				'payment_methods_filename' => $this->filename,
				'payment_methods_logo' => '',
				'date_added' => 'now()',
				'last_modified' => 'now()',
				'payment_methods_receive_status' => '1',
				'payment_methods_receive_status_mode' => 0
			);
			tep_db_perform(TABLE_PAYMENT_METHODS, $install_payment_gateway_data_sql);
			$this->payment_methods_id = tep_db_insert_id();
		}

		if ($install_configuration_flag) {
			$install_key_array = array();
			$install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
					'payment_configuration_info_title' => 'Transaction Currency',
					'payment_configuration_info_key' => 'MODULE_PAYMENT_PAYPAL_CURRENCY',
					'payment_configuration_info_description' => 'The currency to use for credit card transactions.',
					'payment_configuration_info_sort_order' => '3',
					'set_function' => 'tep_cfg_select_option(array(\'Selected Currency\',\'Only USD\',\'Only CAD\',\'Only EUR\',\'Only GBP\',\'Only JPY\'), ',
					'use_function' => 'tep_get_zone_class_title',
					'date_added' => 'now()'
				),
				'desc' => array('payment_configuration_info_value' => 'Selected Currency',
					'languages_id' => 1
				)
			);
			$install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
					'payment_configuration_info_title' => 'Payment Zone',
					'payment_configuration_info_key' => 'MODULE_PAYMENT_PAYPAL_ZONE',
					'payment_configuration_info_description' => 'If a zone is selected, only enable this payment method for that zone.',
					'payment_configuration_info_sort_order' => '4',
					'set_function' => 'tep_cfg_pull_down_zone_classes(',
					'use_function' => '',
					'date_added' => 'now()'
				),
				'desc' => array('payment_configuration_info_value' => '0',
					'languages_id' => 1
				)
			);
			$install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
					'payment_configuration_info_title' => 'Set Pending Notification Status',
					'payment_configuration_info_key' => 'MODULE_PAYMENT_PAYPAL_PROCESSING_STATUS_ID',
					'payment_configuration_info_description' => 'Set the Pending Notification status of orders made with this payment module to this value (\'Processing\' recommended)',
					'payment_configuration_info_sort_order' => '5',
					'set_function' => 'tep_cfg_pull_down_order_statuses(',
					'use_function' => 'tep_get_order_status_name',
					'date_added' => 'now()'
				),
				'desc' => array('payment_configuration_info_value' => DEFAULT_ORDERS_STATUS_ID,
					'languages_id' => 1
				)
			);
			$install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
					'payment_configuration_info_title' => 'Set Order Status',
					'payment_configuration_info_key' => 'MODULE_PAYMENT_PAYPAL_ORDER_STATUS_ID',
					'payment_configuration_info_description' => 'Set the status of orders made with this payment module to this value<br>(\'Pending\' recommended)',
					'payment_configuration_info_sort_order' => '6',
					'set_function' => 'tep_cfg_pull_down_order_statuses(',
					'use_function' => 'tep_get_order_status_name',
					'date_added' => 'now()'
				),
				'desc' => array('payment_configuration_info_value' => DEFAULT_ORDERS_STATUS_ID,
					'languages_id' => 1
				)
			);
			$install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
					'payment_configuration_info_title' => 'Synchronize Invoice',
					'payment_configuration_info_key' => 'MODULE_PAYMENT_PAYPAL_INVOICE_REQUIRED',
					'payment_configuration_info_description' => 'Do you want to specify the order number as the PayPal invoice number?',
					'payment_configuration_info_sort_order' => '7',
					'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
					'use_function' => '',
					'date_added' => 'now()'
				),
				'desc' => array('payment_configuration_info_value' => False,
					'languages_id' => 1
				)
			);
			$install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
					'payment_configuration_info_title' => 'Background Color',
					'payment_configuration_info_key' => 'MODULE_PAYMENT_PAYPAL_CS',
					'payment_configuration_info_description' => 'Select the background color of PayPal\'s payment pages.',
					'payment_configuration_info_sort_order' => '9',
					'set_function' => 'tep_cfg_select_option(array(\'White\',\'Black\'), ',
					'use_function' => '',
					'date_added' => 'now()'
				),
				'desc' => array('payment_configuration_info_value' => 'White',
					'languages_id' => 1
				)
			);
			$install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
					'payment_configuration_info_title' => 'Processing logo',
					'payment_configuration_info_key' => 'MODULE_PAYMENT_PAYPAL_PROCESSING_LOGO',
					'payment_configuration_info_description' => 'The image file name to display the store\'s checkout process',
					'payment_configuration_info_sort_order' => '10',
					'set_function' => '',
					'use_function' => '',
					'date_added' => 'now()'
				),
				'desc' => array('payment_configuration_info_value' => 'oscommerce.gif',
					'languages_id' => 1
				)
			);
			$install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
					'payment_configuration_info_title' => 'Store logo',
					'payment_configuration_info_key' => 'MODULE_PAYMENT_PAYPAL_STORE_LOGO',
					'payment_configuration_info_description' => 'The image file name for PayPal to display (leave empty if your store does not have SSL)',
					'payment_configuration_info_sort_order' => '11',
					'set_function' => '',
					'use_function' => '',
					'date_added' => 'now()'
				),
				'desc' => array('payment_configuration_info_value' => '',
					'languages_id' => 1
				)
			);
			$install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
					'payment_configuration_info_title' => 'PayPal Page Style Name',
					'payment_configuration_info_key' => 'MODULE_PAYMENT_PAYPAL_PAGE_STYLE',
					'payment_configuration_info_description' => 'The name of the page style you have configured in your PayPal Account',
					'payment_configuration_info_sort_order' => '12',
					'set_function' => '',
					'use_function' => '',
					'date_added' => 'now()'
				),
				'desc' => array('payment_configuration_info_value' => 'default',
					'languages_id' => 1
				)
			);
			$install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
					'payment_configuration_info_title' => 'Include a note with payment',
					'payment_configuration_info_key' => 'MODULE_PAYMENT_PAYPAL_NO_NOTE',
					'payment_configuration_info_description' => 'Choose whether your customer should be prompted to include a note or not?',
					'payment_configuration_info_sort_order' => '13',
					'set_function' => 'tep_cfg_select_option(array(\'Yes\',\'No\'), ',
					'use_function' => '',
					'date_added' => 'now()'
				),
				'desc' => array('payment_configuration_info_value' => 'No',
					'languages_id' => 1
				)
			);
			$install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
					'payment_configuration_info_title' => 'Shopping Cart Method',
					'payment_configuration_info_key' => 'MODULE_PAYMENT_PAYPAL_METHOD',
					'payment_configuration_info_description' => "What type of shopping cart do you want to use?",
					'payment_configuration_info_sort_order' => '14',
					'set_function' => 'tep_cfg_select_option(array(\'Aggregate\',\'Itemized\'), ',
					'use_function' => '',
					'date_added' => 'now()'
				),
				'desc' => array('payment_configuration_info_value' => 'Aggregate',
					'languages_id' => 1
				)
			);
			$install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
					'payment_configuration_info_title' => 'Enable PayPal Shipping Address',
					'payment_configuration_info_key' => 'MODULE_PAYMENT_PAYPAL_SHIPPING_ALLOWED',
					'payment_configuration_info_description' => "Allow the customer to choose their own PayPal shipping address?",
					'payment_configuration_info_sort_order' => '15',
					'set_function' => 'tep_cfg_select_option(array(\'Yes\',\'No\'), ',
					'use_function' => '',
					'date_added' => 'now()'
				),
				'desc' => array('payment_configuration_info_value' => 'No',
					'languages_id' => 1
				)
			);
			$install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
					'payment_configuration_info_title' => 'Debug Email Notifications',
					'payment_configuration_info_key' => 'MODULE_PAYMENT_PAYPAL_IPN_DEBUG',
					'payment_configuration_info_description' => "Enable debug email notifications",
					'payment_configuration_info_sort_order' => '16',
					'set_function' => 'tep_cfg_select_option(array(\'Yes\',\'No\'), ',
					'use_function' => '',
					'date_added' => 'now()'
				),
				'desc' => array('payment_configuration_info_value' => 'Yes',
					'languages_id' => 1
				)
			);
			$install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
					'payment_configuration_info_title' => 'Digest Key',
					'payment_configuration_info_key' => 'MODULE_PAYMENT_PAYPAL_IPN_DIGEST_KEY',
					'payment_configuration_info_description' => "Key to use for the digest functionality",
					'payment_configuration_info_sort_order' => '17',
					'set_function' => '',
					'use_function' => '',
					'date_added' => 'now()'
				),
				'desc' => array('payment_configuration_info_value' => 'PayPal_Shopping_Cart_IPN',
					'languages_id' => 1
				)
			);
			$install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
					'payment_configuration_info_title' => 'Test Mode',
					'payment_configuration_info_key' => 'MODULE_PAYMENT_PAYPAL_IPN_TEST_MODE',
					'payment_configuration_info_description' => "Set test mode (<a href=\"" . tep_href_link(FILENAME_PAYPAL, tep_get_all_get_params(array('set', 'module', 'action')) . 'action=test') . "\" target=\"ipn\"><u>Launch Test Page</u></a>)",
					'payment_configuration_info_sort_order' => '18',
					'set_function' => 'tep_cfg_select_option(array(\'Off\',\'On\'), ',
					'use_function' => '',
					'date_added' => 'now()'
				),
				'desc' => array('payment_configuration_info_value' => 'Off',
					'languages_id' => 1
				)
			);
			$install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
					'payment_configuration_info_title' => 'Cart Test',
					'payment_configuration_info_key' => 'MODULE_PAYMENT_PAYPAL_IPN_CART_TEST',
					'payment_configuration_info_description' => 'Set cart test mode to verify the transaction amounts',
					'payment_configuration_info_sort_order' => '19',
					'set_function' => 'tep_cfg_select_option(array(\'Off\',\'On\'), ',
					'use_function' => '',
					'date_added' => 'now()'
				),
				'desc' => array('payment_configuration_info_value' => 'On',
					'languages_id' => 1
				)
			);
			$install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
					'payment_configuration_info_title' => 'Notification Address',
					'payment_configuration_info_key' => 'MODULE_PAYMENT_PAYPAL_IPN_DEBUG_EMAIL',
					'payment_configuration_info_description' => 'The e-mail address to send (level 1) notifications to',
					'payment_configuration_info_sort_order' => '20',
					'set_function' => '',
					'use_function' => '',
					'date_added' => 'now()'
				),
				'desc' => array('payment_configuration_info_value' => STORE_OWNER_EMAIL_ADDRESS,
					'languages_id' => 1
				)
			);
			$install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
					'payment_configuration_info_title' => 'PayPal Domain',
					'payment_configuration_info_key' => 'MODULE_PAYMENT_PAYPAL_DOMAIN',
					'payment_configuration_info_description' => 'Select which PayPal domain to use<br>(for live production select www.paypal.com)',
					'payment_configuration_info_sort_order' => '21',
					'set_function' => 'tep_cfg_select_option(array(\'www.paypal.com\',\'www.sandbox.paypal.com\'), ',
					'use_function' => '',
					'date_added' => 'now()'
				),
				'desc' => array('payment_configuration_info_value' => 'www.paypal.com',
					'languages_id' => 1
				)
			);
			$install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
					'payment_configuration_info_title' => 'Return URL behavior',
					'payment_configuration_info_key' => 'MODULE_PAYMENT_PAYPAL_RM',
					'payment_configuration_info_description' => 'How should the customer be sent back from PayPal to the specified URL?<br>0=No IPN, 1=GET, 2=POST',
					'payment_configuration_info_sort_order' => '22',
					'set_function' => 'tep_cfg_select_option(array(\'0\',\'1\',\'2\'), ',
					'use_function' => '',
					'date_added' => 'now()'
				),
				'desc' => array('payment_configuration_info_value' => '1',
					'languages_id' => 1
				)
			);
			$install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
					'payment_configuration_info_title' => 'Payment Message',
					'payment_configuration_info_key' => 'MODULE_PAYMENT_PAYPAL_MESSAGE',
					'payment_configuration_info_description' => 'Payment message will show up during checkout process (PayPal)',
					'payment_configuration_info_sort_order' => '24',
					'set_function' => 'tep_cfg_textarea(',
					'use_function' => '',
					'date_added' => 'now()'
				),
				'desc' => array('payment_configuration_info_value' => 'Thank you for shopping at SKC Store.',
					'languages_id' => 1
				)
			);
			$install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
					'payment_configuration_info_title' => 'Payment Email Message',
					'payment_configuration_info_key' => 'MODULE_PAYMENT_PAYPAL_EMAIL_MESSAGE',
					'payment_configuration_info_description' => 'This message will show up in order confirmation e-mail.',
					'payment_configuration_info_sort_order' => '200',
					'set_function' => 'tep_cfg_textarea(',
					'use_function' => '',
					'date_added' => 'now()'
				),
				'desc' => array('payment_configuration_info_value' => 'Thank you for shopping at OffGamers.com.',
					'languages_id' => 1
				)
			);
			$install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
					'payment_configuration_info_title' => 'Confirm Complete (in days)',
					'payment_configuration_info_key' => 'MODULE_PAYMENT_PAYPAL_CONFIRM_COMPLETE',
					'payment_configuration_info_description' => 'The number of days in which an order made by this payment method is confirm completed',
					'payment_configuration_info_sort_order' => '210',
					'set_function' => '',
					'use_function' => '',
					'date_added' => 'now()'
				),
				'desc' => array('payment_configuration_info_value' => '0',
					'languages_id' => 1
				)
			);
			$install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
					'payment_configuration_info_title' => 'Verified Payments E-mail Admin Notification',
					'payment_configuration_info_key' => 'MODULE_PAYMENT_PAYPAL_VERIFIED_EMAIL_NOTIFICATION',
					'payment_configuration_info_description' => 'E-mail address to which the verification of payment e-mail will be send to whenever customers verified their payment e-mail address.<br>(In "Name &lt;Email&gt;" format. Use \',\' as delimiter for multiple recipient)',
					'payment_configuration_info_sort_order' => '220',
					'set_function' => '',
					'use_function' => '',
					'date_added' => 'now()'
				),
				'desc' => array('payment_configuration_info_value' => '',
					'languages_id' => 1
				)
			);
			$install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
					'payment_configuration_info_title' => 'Verified Payments E-mail Admin Notification Status',
					'payment_configuration_info_key' => 'MODULE_PAYMENT_PAYPAL_VERIFIED_EMAIL_NOTIFICATION_STATUS',
					'payment_configuration_info_description' => 'The status for the orders to be included in the verified payment e-mail notification e-mail contents. Seperated by \',\' for multiple values.',
					'payment_configuration_info_sort_order' => '225',
					'set_function' => '',
					'use_function' => '',
					'date_added' => 'now()'
				),
				'desc' => array('payment_configuration_info_value' => '',
					'languages_id' => 1
				)
			);
			$install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
					'payment_configuration_info_title' => 'Encrypted Website Payments (EWP)',
					'payment_configuration_info_key' => 'MODULE_PAYMENT_PAYPAL_EWP',
					'payment_configuration_info_description' => 'Encrypt your PayPal checkout buttons dynamically when rendering your webpages to prevent and/or detect tampering with your buttons.',
					'payment_configuration_info_sort_order' => '230',
					'set_function' => 'tep_cfg_select_option(array(\'Yes\', \'No\'), ',
					'use_function' => '',
					'date_added' => 'now()'
				),
				'desc' => array('payment_configuration_info_value' => 'No',
					'languages_id' => 1
				)
			);
			$install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
					'payment_configuration_info_title' => 'Require Address Information',
					'payment_configuration_info_key' => 'MODULE_PAYMENT_PAYPAL_MANDATORY_ADDRESS_FIELD',
					'payment_configuration_info_description' => 'Set address field as required info during checkout.',
					'payment_configuration_info_sort_order' => '235',
					'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
					'use_function' => '',
					'date_added' => 'now()'
				),
				'desc' => array('payment_configuration_info_value' => 'False',
					'languages_id' => 1
				)
			);
			$install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
					'payment_configuration_info_title' => 'Customer Payment Info',
					'payment_configuration_info_key' => 'MODULE_PAYMENT_PAYPAL_CUSTOMER_PAYMENT_INFO',
					'payment_configuration_info_description' => 'Set to true if this Payment Method required customer input for Payment Information required customer payment info.',
					'payment_configuration_info_sort_order' => '240',
					'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
					'use_function' => '',
					'date_added' => 'now()'
				),
				'desc' => array('payment_configuration_info_value' => 'False',
					'languages_id' => 1
				)
			);
			$install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
					'payment_configuration_info_title' => 'Require IC Information',
					'payment_configuration_info_key' => 'MODULE_PAYMENT_PAYPAL_MANDATORY_IC_FIELD',
					'payment_configuration_info_description' => 'Set IC field as required info during checkout.',
					'payment_configuration_info_sort_order' => '245',
					'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
					'use_function' => '',
					'date_added' => 'now()'
				),
				'desc' => array('payment_configuration_info_value' => 'False',
					'languages_id' => 1
				)
			);
			$install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
					'payment_configuration_info_title' => 'Require Contact Information',
					'payment_configuration_info_key' => 'MODULE_PAYMENT_PAYPAL_MANDATORY_CONTACT_FIELD',
					'payment_configuration_info_description' => 'Set Contact field as required info during checkout.',
					'payment_configuration_info_sort_order' => '250',
					'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
					'use_function' => '',
					'date_added' => 'now()'
				),
				'desc' => array('payment_configuration_info_value' => 'False',
					'languages_id' => 1
				)
			);
			foreach ($install_key_array as $data) {
				tep_db_perform(TABLE_PAYMENT_CONFIGURATION_INFO, $data['info']);
				$payment_conf_id = tep_db_insert_id();

				$data['desc']['payment_configuration_info_id'] = (int) $payment_conf_id;
				tep_db_perform(TABLE_PAYMENT_CONFIGURATION_INFO_DESCRIPTION, $data['desc']);
			}

			$install_send_payment_key_array = array();
			$install_send_payment_key_array[] = array('payment_methods_fields_title' => 'PayPal Email',
				'payment_methods_fields_pre_info' => '',
				'payment_methods_fields_post_info' => '',
				'payment_methods_fields_required' => 1,
				'payment_methods_fields_type' => '1',
				'payment_methods_fields_system_type' => 'MODULE_PAYPAL_SEND_EMAIL',
				'payment_methods_fields_size' => '55',
				'payment_methods_fields_option' => 'NULL',
				'payment_methods_fields_options_title' => '0',
				'payment_methods_fields_sort_order' => 0
			);
			if (count($install_send_payment_key_array)) {
				foreach ($install_send_payment_key_array as $data) {
					$data['payment_methods_id'] = (int) $this->payment_methods_id;
					$data['payment_methods_mode'] = 'SEND';
					$data['payment_methods_fields_system_mandatory'] = 1;
					$data['payment_methods_fields_status'] = '1';

					tep_db_perform(TABLE_PAYMENT_METHODS_FIELDS, $data);
				}
			}
		}
		return 1;
	}

	function remove() {
		tep_db_query("delete from " . TABLE_CONFIGURATION . " where configuration_key in ('" . implode("', '", $this->keys()) . "')");
	}

	function keys() {
		return array('MODULE_PAYMENT_PAYPAL_STATUS',
			'MODULE_PAYMENT_PAYPAL_ID',
			'MODULE_PAYMENT_PAYPAL_BUSINESS_ID',
			'MODULE_PAYMENT_PAYPAL_CURRENCY',
			'MODULE_PAYMENT_PAYPAL_ZONE',
			'MODULE_PAYMENT_PAYPAL_PROCESSING_STATUS_ID',
			'MODULE_PAYMENT_PAYPAL_ORDER_STATUS_ID',
			'MODULE_PAYMENT_PAYPAL_INVOICE_REQUIRED',
			'MODULE_PAYMENT_PAYPAL_SORT_ORDER',
			'MODULE_PAYMENT_PAYPAL_CS',
			'MODULE_PAYMENT_PAYPAL_PROCESSING_LOGO',
			'MODULE_PAYMENT_PAYPAL_STORE_LOGO',
			'MODULE_PAYMENT_PAYPAL_PAGE_STYLE',
			'MODULE_PAYMENT_PAYPAL_EWP',
			'MODULE_PAYMENT_PAYPAL_EWP_CERT_ID',
			'MODULE_PAYMENT_PAYPAL_API_USERNAME',
			'MODULE_PAYMENT_PAYPAL_API_PASSWORD',
			'MODULE_PAYMENT_PAYPAL_API_SIGNATURE',
			'MODULE_PAYMENT_PAYPAL_NO_NOTE',
			'MODULE_PAYMENT_PAYPAL_METHOD',
			'MODULE_PAYMENT_PAYPAL_SHIPPING_ALLOWED',
			'MODULE_PAYMENT_PAYPAL_IPN_DEBUG',
			'MODULE_PAYMENT_PAYPAL_IPN_DIGEST_KEY',
			'MODULE_PAYMENT_PAYPAL_IPN_TEST_MODE',
			'MODULE_PAYMENT_PAYPAL_IPN_CART_TEST',
			'MODULE_PAYMENT_PAYPAL_IPN_DEBUG_EMAIL',
			'MODULE_PAYMENT_PAYPAL_DOMAIN',
			'MODULE_PAYMENT_PAYPAL_RM',
			'MODULE_PAYMENT_PAYPAL_CONFIRM_COMPLETE',
			'MODULE_PAYMENT_PAYPAL_VERIFIED_EMAIL_NOTIFICATION',
			'MODULE_PAYMENT_PAYPAL_VERIFIED_EMAIL_NOTIFICATION_STATUS',
			'MODULE_PAYMENT_PAYPAL_MESSAGE',
			'MODULE_PAYMENT_PAYPAL_EMAIL_MESSAGE',
			'MODULE_PAYMENT_PAYPAL_LEGEND_COLOUR');
	}

	function configuration_information_keys() {
		return array('MODULE_PAYMENT_PAYPAL_CURRENCY',
			'MODULE_PAYMENT_PAYPAL_ZONE',
			'MODULE_PAYMENT_PAYPAL_PROCESSING_STATUS_ID',
			'MODULE_PAYMENT_PAYPAL_ORDER_STATUS_ID',
			'MODULE_PAYMENT_PAYPAL_INVOICE_REQUIRED',
			'MODULE_PAYMENT_PAYPAL_CS',
			'MODULE_PAYMENT_PAYPAL_PROCESSING_LOGO',
			'MODULE_PAYMENT_PAYPAL_STORE_LOGO',
			'MODULE_PAYMENT_PAYPAL_PAGE_STYLE',
			'MODULE_PAYMENT_PAYPAL_NO_NOTE',
			'MODULE_PAYMENT_PAYPAL_METHOD',
			'MODULE_PAYMENT_PAYPAL_SHIPPING_ALLOWED',
			'MODULE_PAYMENT_PAYPAL_IPN_DEBUG',
			'MODULE_PAYMENT_PAYPAL_IPN_DIGEST_KEY',
			'MODULE_PAYMENT_PAYPAL_IPN_TEST_MODE',
			'MODULE_PAYMENT_PAYPAL_IPN_CART_TEST',
			'MODULE_PAYMENT_PAYPAL_IPN_DEBUG_EMAIL',
			'MODULE_PAYMENT_PAYPAL_DOMAIN',
			'MODULE_PAYMENT_PAYPAL_RM',
			'MODULE_PAYMENT_PAYPAL_MESSAGE',
			'MODULE_PAYMENT_PAYPAL_EMAIL_MESSAGE',
			'MODULE_PAYMENT_PAYPAL_CONFIRM_COMPLETE',
			'MODULE_PAYMENT_PAYPAL_VERIFIED_EMAIL_NOTIFICATION',
			'MODULE_PAYMENT_PAYPAL_VERIFIED_EMAIL_NOTIFICATION_STATUS',
			'MODULE_PAYMENT_PAYPAL_EWP',
			'MODULE_PAYMENT_PAYPAL_MANDATORY_ADDRESS_FIELD',
			'MODULE_PAYMENT_PAYPAL_CUSTOMER_PAYMENT_INFO',
			'MODULE_PAYMENT_PAYPAL_MANDATORY_IC_FIELD',
			'MODULE_PAYMENT_PAYPAL_MANDATORY_CONTACT_FIELD');
	}

	function multi_lang_configuration_info_keys() {
		return array(
			'MODULE_PAYMENT_PAYPAL_MESSAGE',
			'MODULE_PAYMENT_PAYPAL_EMAIL_MESSAGE',
		);
	}

	function merchant_information_keys() {
		include_once(DIR_WS_CLASSES . "payment_methods.php");

		return array(
			'MODULE_PAYMENT_PAYPAL_ID' => array("field" => 'text', 'mapping' => "MODULE_PAYMENT_PAYPAL_LNG_ID"),
			'MODULE_PAYMENT_PAYPAL_EWP_CERT_ID' => array("field" => 'text', 'mapping' => "MODULE_PAYMENT_PAYPAL_LNG_EWP_CERT_ID"),
			'MODULE_PAYMENT_PAYPAL_BUSINESS_ID' => array("field" => 'text', 'mapping' => "MODULE_PAYMENT_PAYPAL_LNG_BUSINESS_ID"),
			'MODULE_PAYMENT_PAYPAL_API_USERNAME' => array("field" => 'text', 'mapping' => "MODULE_PAYMENT_PAYPAL_LNG_API_USERNAME"),
			'MODULE_PAYMENT_PAYPAL_API_PASSWORD' => array("field" => 'text', 'mapping' => "MODULE_PAYMENT_PAYPAL_LNG_API_PASSWORD"),
			'MODULE_PAYMENT_PAYPAL_API_SIGNATURE' => array("field" => 'text', 'mapping' => "MODULE_PAYMENT_PAYPAL_LNG_API_SIGNATURE"),
			'MODULE_PAYMENT_PAYPAL_PRIVATE_KEY_FILE' => array("field" => 'text', 'mapping' => "MODULE_PAYMENT_PAYPAL_LNG_PRIVATE_KEY_FILE"),
			'MODULE_PAYMENT_PAYPAL_PUBLIC_KEY_FILE' => array("field" => 'text', 'mapping' => "MODULE_PAYMENT_PAYPAL_LNG_PUBLIC_KEY_FILE"),
			'MODULE_PAYMENT_PAYPAL_PAYPAL_PUBLIC_KEY_FILE' => array("field" => 'text', 'mapping' => "MODULE_PAYMENT_PAYPAL_LNG_PAYPAL_PUBLIC_KEY_FILE"),
			'MODULE_PAYMENT_TAX' => array("field" => 'select', 'mapping' => "MODULE_PAYMENT_TAX", "select" => payment_methods::get_tax_option())
		);
	}

	function outgoing_merchant_information_keys() {
		return array(
			'MODULE_PAYMENT_OUTGOING_PAYPAL_API_USERNAME' => array("field" => 'text', 'mapping' => "MODULE_PAYMENT_PAYPAL_LNG_API_USERNAME"),
			'MODULE_PAYMENT_OUTGOING_PAYPAL_API_PASSWORD' => array("field" => 'text', 'mapping' => "MODULE_PAYMENT_PAYPAL_LNG_API_PASSWORD"),
			'MODULE_PAYMENT_OUTGOING_PAYPAL_API_SIGNATURE' => array("field" => 'text', 'mapping' => "MODULE_PAYMENT_PAYPAL_LNG_API_SIGNATURE")
		);
	}

	function get_pg_id() {
		return $this->payment_methods_id;
	}

	function setTransactionID() {
		global $order, $currencies;
		$my_currency = $this->currency();
		$trans_id = STORE_NAME . date('Ymdhis') . $this->orders_id;
		$this->digest = md5($trans_id . number_format($order->info['total'] * $currencies->get_value($my_currency), $currencies->get_decimal_places($my_currency), '.', '') . $this->ipn_digest_key);
		return $this->digest;
	}

	function drawPayPalFields($txn_sign = '', $currency = '', $orders_id = '', $return_url = '', $cancel_url = '') {
		global $order, $currencies;

		$paypal_fields = array();
		$paypal_fields_html = '';

		$my_currency = (tep_not_null($currency)) ? $currency : $this->currency();

		$this->get_merchant_account($my_currency);

		$paypal_fields['cmd'] = '_ext-enter'; //allows the customer addr details to be passed
		$paypal_fields['business'] = $this->business_id;
		$paypal_fields['currency_code'] = $my_currency;

		if (tep_not_null(MODULE_PAYMENT_PAYPAL_STORE_LOGO))
			$paypal_fields['image_url'] = tep_href_link(DIR_WS_IMAGES . MODULE_PAYMENT_PAYPAL_STORE_LOGO, '', 'SSL');

		$return_href_link = tep_not_null($return_url) ? $return_url : tep_href_link(FILENAME_CHECKOUT_SUCCESS, 'action=success', 'SSL');
		$cancel_href_link = tep_not_null($cancel_url) ? $cancel_url : tep_href_link(FILENAME_CHECKOUT_PAYMENT, '', 'SSL');

		$paypal_fields['return'] = $return_href_link;
		$paypal_fields['cancel_return'] = $cancel_href_link;
		$paypal_fields['notify_url'] = tep_href_link('ipn.php', '', 'SSL', false);
		$paypal_fields['bn'] = 'osc-ipn-v1';
		$paypal_fields['mrb'] = 'R-5X478387L4986632G';
		$paypal_fields['pal'] = 'ZH3UM7MDRAMCG';
		if (MODULE_PAYMENT_PAYPAL_SHIPPING_ALLOWED == 'No')
			$paypal_fields['no_shipping'] = '1';

		if (MODULE_PAYMENT_PAYPAL_METHOD == 'Itemized') {
			$paypal_fields['upload'] = sizeof($order->products);
			$paypal_fields['redirect_cmd'] = '_cart';
			$paypal_fields['handling_cart'] = number_format($order->info['shipping_cost'] * $currencies->get_value($my_currency), $currencies->get_decimal_places($my_currency));
		} else {
			$paypal_fields['item_name'] = STORE_NAME;
			$paypal_fields['redirect_cmd'] = '_xclick';
			$paypal_fields['amount'] = number_format(($order->info['total'] - $order->info['shipping_cost']) * $currencies->get_value($my_currency), $currencies->get_decimal_places($my_currency), '.', '');
			$paypal_fields['shipping'] = number_format($order->info['shipping_cost'] * $currencies->get_value($my_currency), $currencies->get_decimal_places($my_currency), '.', '');
		}

		$paypal_fields['rm'] = MODULE_PAYMENT_PAYPAL_RM;

		$invoice_id = (tep_not_null($orders_id)) ? $orders_id : $this->orders_id;
		$signature = (tep_not_null($txn_sign)) ? $txn_sign : $this->digest;

		$paypal_fields['custom'] = $signature;

		if (MODULE_PAYMENT_PAYPAL_INVOICE_REQUIRED == 'True')
			$paypal_fields['invoice'] = $invoice_id;

		$paypal_fields = array_merge($paypal_fields, $this->_drawCustomerDetails($order), $this->_drawNoteOption(MODULE_PAYMENT_PAYPAL_NO_NOTE, MODULE_PAYMENT_PAYPAL_CUSTOMER_COMMENTS)); //Customer comment field

		$paypal_fields['cs'] = (MODULE_PAYMENT_PAYPAL_CS == 'White') ? '0' : '1'; //PayPal Background Color

		if (tep_not_null(MODULE_PAYMENT_PAYPAL_PAGE_STYLE))
			$paypal_fields['page_style'] = MODULE_PAYMENT_PAYPAL_PAGE_STYLE;

		$paypal_fields['item_number'] = 'Purchase from ' . STORE_NAME . ' #' . $invoice_id;

		if ($this->use_ewp_submission) {
			$paypal_fields['cert_id'] = $this->ewp_cert_id;
			$paypal_fields_html = $this->_paypal_encrypt($paypal_fields);
		} else {
			foreach ($paypal_fields as $field_name => $field_value) {
				$paypal_fields_html .= tep_draw_hidden_field($field_name, $field_value);
			}
		}

		return $paypal_fields_html;
	}

	function _paypal_encrypt($hash) {
		//Sample PayPal Button Encryption: Copyright 2006,2007 StellarWebSolutions.com
		//Not for resale - license agreement at
		//http://www.stellarwebsolutions.com/en/eula.php
		#Set home directory for OpenSSL
		//putenv("HOME=~");

		if (!file_exists($this->private_key_file)) {
			echo "ERROR: MY_KEY_FILE not found\n";
		}

		if (!file_exists($this->public_key_file)) {
			echo "ERROR: MY_CERT_FILE not found\n";
		}

		if (!file_exists($this->paypal_public_key_file)) {
			echo "ERROR: PAYPAL_CERT_FILE not found\n";
		}

		if (!file_exists($this->openssl_path)) {
			echo "ERROR: OPENSSL not found\n";
		}

		$openssl_cmd = "$this->openssl_path smime -sign -signer $this->public_key_file -inkey $this->private_key_file " .
				"-outform der -nodetach -binary | $this->openssl_path smime -encrypt " .
				"-des3 -binary -outform pem $this->paypal_public_key_file";

		$descriptors = array(0 => array("pipe", "r"),
			1 => array("pipe", "w")
		);

		$process = proc_open($openssl_cmd, $descriptors, $pipes);

		if (is_resource($process)) {
			foreach ($hash as $key => $value) {
				if ($value != "") {
					//echo "Adding to blob: $key=$value\n";
					fwrite($pipes[0], "$key=$value\n");
				}
			}
			fflush($pipes[0]);
			fclose($pipes[0]);

			$output = "";
			while (!feof($pipes[1])) {
				$output .= fgets($pipes[1]);
			}

			fclose($pipes[1]);
			$return_value = proc_close($process);

			return '<input type="hidden" name="cmd" value="_s-xclick">
					<input type="hidden" name="encrypted" value="' . $output . '">';
		}
		return "ERROR";
	}

	function _drawCustomerDetails(&$order) {
		$customer_fields = array();

		//Customer Details - for those who haven't signed up to PayPal
		$customer_fields['email'] = $order->customer['email_address'];
		$customer_fields['first_name'] = $order->customer['firstname'];
		$customer_fields['last_name'] = $order->customer['lastname'];
		$customer_fields['address1'] = $order->customer['street_address'];
		$customer_fields['address2'] = $order->customer['suburb'];
		$customer_fields['city'] = $order->customer['city'];
		$customer_fields['state'] = tep_get_zone_code($order->customer['country']['id'], $order->customer['zone_id'], $order->customer['zone_id']);
		$customer_fields['zip'] = $order->customer['postcode'];

		//User Country Preference
		//Note: Anguilla[AI], Dominican Republic[DO], The Netherlands[NL] have different codes to the iso codes in the osC db
		//$customer_fields['lc'] = $order->customer['country']['iso_code_2'];
		$customer_fields['lc'] = 'US';

		//Telephone is problematic.
		$telephone = preg_replace('/\D/', '', $order->customer['telephone']);
		$customer_fields['night_phone_a'] = substr($telephone, 0, 3);
		$customer_fields['night_phone_b'] = substr($telephone, 3, 3);
		$customer_fields['night_phone_c'] = substr($telephone, 6, 4);
		$customer_fields['day_phone_a'] = substr($telephone, 0, 3);
		$customer_fields['day_phone_b'] = substr($telephone, 3, 3);
		$customer_fields['day_phone_c'] = substr($telephone, 6, 4);

		return $customer_fields;
	}

	function _drawPayPalOptionSet($sub_index, $index, $option = ' ', $value = ' ') {
		return array('on' . $sub_index . '_' . $index => $option,
			'os' . $sub_index . '_' . $index => $value);
	}

	function _drawNoteOption($option = 'No', $msg = 'Add Comments About Your Order') {
		$note_fields = array();

		$option = ($option == 'Yes') ? '0' : '1';
		$note_fields['no_note'] = $option;

		if (!$option) {
			$note_fields['cn'] = $msg;
		}

		return $note_fields;
	}

	function drawSendMoneyFields(&$order, $currency, $orders_id) {
		include_once(DIR_WS_MODULES . 'payment/paypal/database_tables.inc.php');
		$orders_session_query = tep_db_query("select firstname, lastname, txn_signature from " . TABLE_ORDERS_SESSION_INFO . " where orders_id ='" . (int) $orders_id . "'");
		$orders_session_info = tep_db_fetch_array($orders_session_query);
		$order->customer['firstname'] = $orders_session_info['firstname'];
		$order->customer['lastname'] = $orders_session_info['lastname'];
		$order->info['total'] = $order->info['total_value'];
		$return_href_link = tep_href_link(FILENAME_ACCOUNT_HISTORY_INFO, 'order_id=' . $orders_id, 'SSL');
		$cancel_href_link = $return_href_link;
		return $this->drawPayPalFields($orders_session_info['txn_signature'], $currency, $orders_id, $return_href_link, $cancel_href_link);
	}

	function get_orders_with_payer_email($customers_id, $customer_email) {
		// Rerun genesis script for these Verifying status orders
		$orders_select_sql = "  SELECT o.orders_id  
                                FROM " . TABLE_PAYPAL . " AS p 
                                INNER JOIN " . TABLE_ORDERS . " AS o 
                                    ON (p.invoice = o.orders_id)
                                WHERE p.payer_email = '" . tep_db_input($customer_email) . "' 
                                    AND o.customers_id = '" . tep_db_input($customers_id) . "'
                                    AND o.orders_status = 7";
		$orders_result_sql = tep_db_query($orders_select_sql);

		while ($orders_row = tep_db_fetch_array($orders_result_sql)) {
			$genesis_order_data_array = array('orders_id' => $orders_row['orders_id'],
				'flag' => 0,
				're_run' => 1,
				'last_modified' => 'now()');
			tep_db_perform(TABLE_CRON_GENESIS_ORDERS, $genesis_order_data_array);
		}
	}

	function hash_call($methodName, $nvpStr, $paypal_currency) {
		$this->get_merchant_account($paypal_currency);

		//setting the curl parameters.
		$ch = curl_init();
		curl_setopt($ch, CURLOPT_URL, $this->api_endpoint_url);
		curl_setopt($ch, CURLOPT_VERBOSE, 1);

		//turning off the server and peer verification(TrustManager Concept).
		curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
		curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);

		curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
		curl_setopt($ch, CURLOPT_POST, 1);


		if ($this->connect_via_proxy) {
			curl_setopt($ch, CURLOPT_PROXY, 'http://*************:3128');
			curl_setopt($ch, CURLOPT_PROXYUSERPWD, 'devbackend:devonly');
		}

		//NVPRequest for submitting to server
		$nvpreq = 'METHOD=' . urlencode($methodName) . '&VERSION=' . urlencode($this->api_version) . '&PWD=' . urlencode($this->api_password) . '&USER=' . urlencode($this->api_username) . '&SIGNATURE=' . urlencode($this->api_signature) . $nvpStr;

		//setting the nvpreq as POST FIELD to curl
		curl_setopt($ch, CURLOPT_POSTFIELDS, $nvpreq);

		//getting response from server
		$response = curl_exec($ch);

		//convrting NVPResponse to an Associative Array
		$nvpResArray = $this->deformatNVP($response);

		curl_close($ch);

		return $nvpResArray;
	}

	function deformatNVP($nvpstr) {
		$intial = 0;
		$nvpArray = array();

		while (strlen($nvpstr)) {
			//postion of Key
			$keypos = strpos($nvpstr, '=');
			//position of value
			$valuepos = strpos($nvpstr, '&') ? strpos($nvpstr, '&') : strlen($nvpstr);

			/* getting the Key and Value values and storing in a Associative Array */
			$keyval = substr($nvpstr, $intial, $keypos);
			$valval = substr($nvpstr, $keypos + 1, $valuepos - $keypos - 1);
			//decoding the respose
			$nvpArray[urldecode($keyval)] = urldecode($valval);
			$nvpstr = substr($nvpstr, $valuepos + 1, strlen($nvpstr));
		}

		return $nvpArray;
	}

	function get_orders_payment_info_file() {
		return DIR_FS_CATALOG_MODULES . 'payment/paypal/admin/orders.inc.php';
	}

	function get_orders_list_payment_info_file() {
		return DIR_FS_CATALOG_MODULES . 'payment/paypal/admin/orders_list.inc.php';
	}

	function get_ipn_class_file() {
		return DIR_FS_CATALOG_MODULES . 'payment/paypal/classes/ipn.class.php';
	}

	function mass_pay($pass_id) {
		if (!tep_not_null($pass_id) || count($pass_id) == 0)
			return 0;

		$return_result_array = array();
		foreach ($pass_id as $store_payments_id_loop) {
			$return_result_array[$store_payments_id_loop] = 0;
		}

		$store_payments_array = array();
		$payments_methods_id_array = array();
		$store_payments_select_sql = "	SELECT sp.store_payments_id, sp.store_payments_request_currency, sp.store_payments_paid_currency, sp.store_payments_paid_currency_value, sp.store_payments_methods_id, sp.store_payments_after_fees_amount 
										FROM " . TABLE_STORE_PAYMENTS . " AS sp 
										WHERE sp.store_payments_id IN ('" . implode("','", $pass_id) . "')
											AND sp.store_payments_lock = '0'
											AND sp.store_payments_status = '2'";  // only those in processing status and not locked order
		$store_payments_result_sql = tep_db_query($store_payments_select_sql);
		while ($store_payments_row = tep_db_fetch_array($store_payments_result_sql)) {
			$store_payments_currency_array[$store_payments_row['store_payments_methods_id']][$store_payments_row['store_payments_paid_currency']][] = $store_payments_row['store_payments_id'];
			$actual_payout_amount = 0;
			if ($store_payments_row['store_payments_request_currency'] != $store_payments_row['store_payments_paid_currency']) {
				$latest_ex_rate = $store_payments_row['store_payments_paid_currency_value'];
				if (is_numeric($latest_ex_rate) && $latest_ex_rate > 0) {
					$latest_ex_rate = (double) $latest_ex_rate;
					$actual_payout_amount = $latest_ex_rate * $store_payments_row['store_payments_after_fees_amount'];
				} else {
					continue;
				}
			} else {
				$actual_payout_amount = $store_payments_row['store_payments_after_fees_amount'];
			}
			$store_payments_array[$store_payments_row['store_payments_methods_id']][$store_payments_row['store_payments_id']]['amount'] = number_format($actual_payout_amount, 2, ".", "");
		}

		$payments_methods_fields_array = array();
		if (count($store_payments_array)) {
			foreach ($store_payments_array as $store_payments_methods_id_loop => $store_payments_data_loop) {
				//$store_payments_methods_data_loop // store payments id
				$payment_methods_fields_select_sql = "	SELECT pmf.payment_methods_id, pmf.payment_methods_fields_title, pmf.payment_methods_fields_id, pmf.payment_methods_fields_system_type 
														FROM " . TABLE_PAYMENT_METHODS_FIELDS . " AS pmf
														WHERE pmf.payment_methods_id = '" . $store_payments_methods_id_loop . "'
															AND payment_methods_fields_system_type = 'MODULE_PAYPAL_SEND_EMAIL'";
				$payment_methods_fields_result_sql = tep_db_query($payment_methods_fields_select_sql);
				if (!tep_db_num_rows($payment_methods_fields_result_sql)) {
					$payment_methods_fields_select_sql = "	SELECT pmf.payment_methods_id, pmf.payment_methods_fields_title, pmf.payment_methods_fields_id, pmf.payment_methods_fields_system_type 
															FROM " . TABLE_PAYMENT_METHODS_FIELDS . " AS pmf
															LEFT JOIN " . TABLE_PAYMENT_METHODS . " AS pm
																ON pm.payment_methods_parent_id = pmf.payment_methods_id
															WHERE pm.payment_methods_id = '" . $store_payments_methods_id_loop . "'
																AND payment_methods_fields_system_type = 'MODULE_PAYPAL_SEND_EMAIL'";
					$payment_methods_fields_result_sql = tep_db_query($payment_methods_fields_select_sql);
				}

				if ($payment_methods_fields_row = tep_db_fetch_array($payment_methods_fields_result_sql)) {
					$payments_methods_id_array[$store_payments_methods_id_loop] = $payment_methods_fields_row['payment_methods_fields_id'];
				}
			}

			foreach ($store_payments_array as $store_payments_methods_id_loop => $store_payments_data_array_loop) {
				foreach ($store_payments_data_array_loop as $store_payments_id_loop => $store_payments_data_loop) {
					$store_payments_details_select_sql = "	SELECT payment_methods_fields_value
                                                            FROM " . TABLE_STORE_PAYMENTS_DETAILS . " AS spd 
															WHERE spd.store_payments_id = '" . (int) $store_payments_id_loop . "'
																AND spd.payment_methods_fields_id = '" . (int) $payments_methods_id_array[$store_payments_methods_id_loop] . "'";
					$store_payments_details_result_sql = tep_db_query($store_payments_details_select_sql);
					while ($store_payments_details_row = tep_db_fetch_array($store_payments_details_result_sql)) {
						$store_payments_array[$store_payments_methods_id_loop][$store_payments_id_loop]['email'] = $store_payments_details_row['payment_methods_fields_value'];
						//add g2g/og platform filter

						$g2g_store_payments_select_sql = "	SELECT sac.store_account_history_trans_id
															FROM ". TABLE_STORE_ACCOUNT_HISTORY ." as sac
															WHERE sac.store_account_history_trans_type = 'P' AND sac.store_account_history_account_type = 'WSC'
															AND sac.store_account_history_trans_id = '" . $store_payments_id_loop ."'";
						$g2g_store_payments_result_sql= tep_db_query($g2g_store_payments_select_sql);
						if ($store_payments_details_row = tep_db_fetch_array($g2g_store_payments_result_sql)) {
							$store_payments_array_portal['G2G'][$store_payments_methods_id_loop][$store_payments_id_loop] =  $store_payments_array[$store_payments_methods_id_loop][$store_payments_id_loop];
						} else {
							$store_payments_array_portal['OG'][$store_payments_methods_id_loop][$store_payments_id_loop] =  $store_payments_array[$store_payments_methods_id_loop][$store_payments_id_loop];
						}

					}
				}
			}
            foreach($store_payments_array_portal as $portal => $payment_details){
				foreach ($store_payments_currency_array as $payment_methods_id_loop => $store_payments_currency_array_loop) {
					$paypal_obj = new paypal($payment_methods_id_loop);

					foreach ($store_payments_currency_array_loop as $store_payments_currency_loop => $store_payments_data_array_loop) {
                        $paypal_obj->get_merchant_outgoing_account($store_payments_currency_loop,$portal);

						if (isset($store_payments_array_portal[$portal][$payment_methods_id_loop]) && count($store_payments_array_portal[$portal][$payment_methods_id_loop])) {
							$curl_submit_array = array();
							
							if ($portal == 'G2G' && G2G_PAYPAL_MASS_PAY_VERSION == 'REST V1') {
								$curl_submit_count = 0;
								$batch_count = 0;
								foreach ($store_payments_array_portal[$portal][$payment_methods_id_loop] as $store_payments_id_array => $store_payments_data_array) {
									if ($batch_count > 255) {
										$curl_submit_count++;
										$batch_count = 0;
									}
									$curl_data = [
										'recipient_type' => 'EMAIL',
									];
									foreach ($store_payments_data_array as $store_payments_key_loop => $store_payments_data_loop) {
										switch ($store_payments_key_loop) {
											case 'email':
												$curl_data['receiver'] = $store_payments_data_loop;
												$curl_submit_array[$curl_submit_count]['store_payment'][$store_payments_id_array]['email'] = $store_payments_data_loop;
												break;
											case 'amount':
												$curl_data['amount'] = [
													'value' => $store_payments_data_loop,
													'currency' => $store_payments_currency_loop,
												];
												$curl_submit_array[$curl_submit_count]['store_payment'][$store_payments_id_array]['amount'] = $store_payments_data_loop;
												break;
										}
										$curl_data["sender_item_id"] = $store_payments_id_array;
									}

									$curl_submit_array[$curl_submit_count]['data']['items'][] = $curl_data;
									$batch_count++;
								}

								foreach ($curl_submit_array as $curl_submit_count_loop => $curl_submit_data_loop) {
									$curl_data = $curl_submit_data_loop['data'];
									$payment_ids = array_keys($curl_submit_data_loop['store_payment']);
									sort($payment_ids);
									$curl_data['sender_batch_header']['sender_batch_id'] = 'payout_' . md5(implode('', $payment_ids));
									$url = $paypal_obj->rest_api_endpoint_url . 'v1/payments/payouts';
									$headers = [
										'Authorization: Basic ' . base64_encode($paypal_obj->outgoing_api_username . ":" . $paypal_obj->outgoing_api_password),
										'Content-Type: application/json',
									];
									$ch = curl_init();
									curl_setopt($ch, CURLOPT_URL, $url);
									curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
									curl_setopt($ch, CURLOPT_VERBOSE, 1);
									curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
									curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);
									curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
									curl_setopt($ch, CURLOPT_POST, 1);
									curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($curl_data));
									$response = curl_exec($ch);
									$json = json_decode($response, true);
									curl_close($ch);

									$returned_data = '';
									$batch_header = [];
									if ($json && !empty($json['batch_header'])) {
										$batch_header = $json['batch_header'];
										$returned_data .= 'TIMESTAMP: ' . gmdate('Y-m-d H:i:s', time()) . '</br>';
										$returned_data .= 'CORRELATIONID/BATCH_ID: ' . (!empty($batch_header['payout_batch_id']) ? $batch_header['payout_batch_id'] : '') . '</br>';
										$returned_data .= 'ACK: ' . (!empty($batch_header['batch_status']) && in_array($batch_header['batch_status'], ['PENDING', 'PROCESSING', 'SUCCESS']) ? 'Success' : '') . '</br>';
										$returned_data .= 'BATCH_STATUS: ' . (!empty($batch_header['batch_status']) ? $batch_header['batch_status'] : '') . '</br>';
										$returned_data .= 'VERSION: REST v1</br>';
									}
									
									foreach ($curl_submit_data_loop['store_payment'] as $store_payment_id_loop => $store_payment_data_loop) {
										$payment_update_sql_data_array = array();
										$payment_history_sql_data_array = array();
										$payment_history_sql_data_array['store_payments_id'] = tep_db_prepare_input($store_payment_id_loop);
										$payment_history_sql_data_array['date_added'] = 'now()';
										$payment_history_sql_data_array['payee_notified'] = '0';
										$payment_history_sql_data_array['changed_by'] = tep_db_prepare_input($_SESSION['login_email_address']);
										$payment_history_sql_data_array['changed_by_role'] = tep_db_prepare_input('admin');
										$payment_history_sql_data_array['store_payments_status'] = '0';
	
										if (isset($batch_header['batch_status']) && in_array($batch_header['batch_status'], ['PENDING', 'PROCESSING', 'SUCCESS'])) {
											$return_result_array[$store_payment_id_loop] = 1;
	
											$payment_update_sql_data_array = array('store_payments_reference' => 'Correlation ID: ' . (!empty($batch_header['payout_batch_id']) ? $batch_header['payout_batch_id'] : ''),
												'store_payments_last_modified' => 'now()');
										}
	
										$payment_update_sql_data_array['store_payments_lock'] = 1;
										tep_db_perform(TABLE_STORE_PAYMENTS, $payment_update_sql_data_array, 'update', "store_payments_id = '" . $store_payment_id_loop . "'");
	
										$log_message = 'Recepient: ' . $store_payment_data_loop['email'] . '</br>';
										$log_message .= 'Unique ID: ' . $store_payment_id_loop . '</br>';
										$log_message .= 'Amount: ' . number_format($store_payment_data_loop['amount'], 2) . '</br>';
										$log_message .= 'Currency: ' . $store_payments_currency_loop . '</br></br>';
	
										$log_message .= '<u>MassPay Response:</u> </br>';
										$log_message .= $returned_data;
	
										if (isset($returned_sp_nvp_array[$store_payment_id_loop])) {
											foreach ($returned_sp_nvp_array[$store_payment_id_loop] as $returned_sp_nvp_key_loop => $returned_sp_nvp_data_loop) {
												$log_message .= $returned_sp_nvp_key_loop . ': ' . $returned_sp_nvp_data_loop . '</br>';
											}
										}
	
										$payment_history_sql_data_array['comments'] = tep_db_prepare_input($log_message);
										tep_db_perform(TABLE_STORE_PAYMENTS_HISTORY, $payment_history_sql_data_array);
									}
								}
							} else {
								$curl_submit_count = 0;
								$batch_count = 0;
								$nvpreq = "METHOD=MassPay&VERSION=51.0&PWD=" . urlencode($paypal_obj->outgoing_api_password) . "&USER=" . $paypal_obj->outgoing_api_username . "&SIGNATURE=" . urlencode($paypal_obj->outgoing_api_signature) . "&CURRENCYCODE=" . urlencode($store_payments_currency_loop) . "&";
								$curl_submit_array[$curl_submit_count]['data'] = $nvpreq;
								
								$submitted_array = array();
								foreach ($store_payments_array_portal[$portal][$payment_methods_id_loop] as $store_payments_id_array => $store_payments_data_array) {
									if ($batch_count > 255) {
										$curl_submit_count++;
										$curl_submit_array[$curl_submit_count]['data'] = $nvpreq;
										$batch_count = 0;
									}
									$submitted_array[$curl_submit_count][$batch_count] = $store_payments_id_array;
									$curl_str = '';
									foreach ($store_payments_data_array as $store_payments_key_loop => $store_payments_data_loop) {
										switch ($store_payments_key_loop) {
											case 'email':
												$curl_submit_array[$curl_submit_count]['store_payment'][$store_payments_id_array]['email'] = $store_payments_data_loop;
												$curl_str .= "L_EMAIL$batch_count=" . urlencode($store_payments_data_loop) . "&";
												break;
											case 'amount':
												$curl_submit_array[$curl_submit_count]['store_payment'][$store_payments_id_array]['amount'] = $store_payments_data_loop;
												$curl_str .= "L_AMT$batch_count=" . urlencode($store_payments_data_loop) . "&";
												break;
										}
									}
									$curl_submit_array[$curl_submit_count]['data'] .= "L_UNIQUEID$batch_count=" . urlencode($store_payments_id_array) . "&" . $curl_str;
									$batch_count++;
								}

								foreach ($curl_submit_array as $curl_submit_count_loop => $curl_submit_data_loop) {
									$ch = curl_init();
									curl_setopt($ch, CURLOPT_URL, $paypal_obj->api_endpoint_url);
									curl_setopt($ch, CURLOPT_VERBOSE, 1);
									curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
									curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);
									curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);

									if ($this->connect_via_proxy) {
										curl_setopt($ch, CURLOPT_PROXY, 'http://*************:3128');
										curl_setopt($ch, CURLOPT_PROXYUSERPWD, 'devbackend:devonly');
									}

									curl_setopt($ch, CURLOPT_POST, 1);

									curl_setopt($ch, CURLOPT_POSTFIELDS, $curl_submit_data_loop['data']);
									$response = curl_exec($ch);
									$nvpResArray = $paypal_obj->deformatNVP($response);
									curl_close($ch);

									$returned_nvp_str = '';
									$returned_sp_nvp_array = array();
									if (count($nvpResArray)) {
										foreach ($nvpResArray as $nvp_res_key_loop => $nvp_res_data_loop) {
											switch ($nvp_res_key_loop) {
												case 'TIMESTAMP':
												case 'CORRELATIONID':
												case 'ACK':
												case 'VERSION':
												case 'BUILD':
													$returned_nvp_str .= $nvp_res_key_loop . ': ' . $nvp_res_data_loop . '</br>';
													break;
												default:
													if (preg_match("/L_([A-Z]+)([0-9]+)$/i", $nvp_res_key_loop, $matched_key) && count($matched_key) == 3) {
														$returned_sp_nvp_array[$submitted_array[$curl_submit_count_loop][$matched_key[2]]][$matched_key[1]] = $nvp_res_data_loop;
													} else {
														$returned_nvp_str .= $nvp_res_key_loop . ': ' . $nvp_res_data_loop . '</br>';
													}
													break;
											}
										}
									}
									
									foreach ($curl_submit_data_loop['store_payment'] as $store_payment_id_loop => $store_payment_data_loop) {
										$payment_update_sql_data_array = array();
										$payment_history_sql_data_array = array();
										$payment_history_sql_data_array['store_payments_id'] = tep_db_prepare_input($store_payment_id_loop);
										$payment_history_sql_data_array['date_added'] = 'now()';
										$payment_history_sql_data_array['payee_notified'] = '0';
										$payment_history_sql_data_array['changed_by'] = tep_db_prepare_input($_SESSION['login_email_address']);
										$payment_history_sql_data_array['changed_by_role'] = tep_db_prepare_input('admin');
										$payment_history_sql_data_array['store_payments_status'] = '0';

										if (isset($nvpResArray['ACK']) && strtolower($nvpResArray['ACK']) == 'success') {
											$return_result_array[$store_payment_id_loop] = 1;

											$payment_update_sql_data_array = array('store_payments_reference' => 'Correlation ID: ' . $nvpResArray['CORRELATIONID'],
												'store_payments_last_modified' => 'now()');
										}

										$payment_update_sql_data_array['store_payments_lock'] = 1;
										tep_db_perform(TABLE_STORE_PAYMENTS, $payment_update_sql_data_array, 'update', "store_payments_id = '" . $store_payment_id_loop . "'");

										$log_message = 'Recepient: ' . $store_payment_data_loop['email'] . '</br>';
										$log_message .= 'Unique ID: ' . $store_payment_id_loop . '</br>';
										$log_message .= 'Amount: ' . number_format($store_payment_data_loop['amount'], 2) . '</br>';
										$log_message .= 'Currency: ' . $store_payments_currency_loop . '</br></br>';

										$log_message .= '<u>MassPay Response:</u> </br>';
										$log_message .= $returned_nvp_str;

										if (isset($returned_sp_nvp_array[$store_payment_id_loop])) {
											foreach ($returned_sp_nvp_array[$store_payment_id_loop] as $returned_sp_nvp_key_loop => $returned_sp_nvp_data_loop) {
												$log_message .= $returned_sp_nvp_key_loop . ': ' . $returned_sp_nvp_data_loop . '</br>';
											}
										}

										$payment_history_sql_data_array['comments'] = tep_db_prepare_input($log_message);
										tep_db_perform(TABLE_STORE_PAYMENTS_HISTORY, $payment_history_sql_data_array);
									}
								}
							}
						}
					}
				}
			}
		}
		return $return_result_array;
	}

	function get_balance($nvpStr = '') {
		return $this->PPHttpPost('GetBalance', $nvpStr);
	}

	function PPHttpPost($methodName_, $nvpStr_) {
		global $environment;

		$API_UserName = urlencode($this->outgoing_api_username);
		$API_Password = urlencode($this->outgoing_api_password);
		$API_Signature = urlencode($this->outgoing_api_signature);

		if ($this->ipn_test_mode == 'On') {
			$this->api_endpoint_url = 'https://api-3t.sandbox.paypal.com/nvp';
		} else {
			$this->api_endpoint_url = 'https://api-3t.paypal.com/nvp';
		}

		$version = urlencode('51.0');

		// setting the curl parameters.
		$ch = curl_init();
		curl_setopt($ch, CURLOPT_URL, $this->api_endpoint_url);
		curl_setopt($ch, CURLOPT_VERBOSE, 1);

		if ($this->connect_via_proxy) {
			curl_setopt($ch, CURLOPT_PROXY, 'http://*************:3128');
			curl_setopt($ch, CURLOPT_PROXYUSERPWD, 'devbackend:devonly');
		}

		// turning off the server and peer verification(TrustManager Concept).
		curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
		curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);

		curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
		curl_setopt($ch, CURLOPT_POST, 1);

		// NVPRequest for submitting to server
		$nvpreq = "METHOD=$methodName_&VERSION=$version&PWD=$API_Password&USER=$API_UserName&SIGNATURE=$API_Signature" . (tep_not_null($nvpStr_) ? "&" . $nvpStr_ : '' );

		// setting the nvpreq as POST FIELD to curl
		curl_setopt($ch, CURLOPT_POSTFIELDS, $nvpreq);

		// getting response from server
		$httpResponse = curl_exec($ch);

		if (!$httpResponse) {
			return;
			//exit("$methodName_ failed: ".curl_error($ch).'('.curl_errno($ch).')');
		}

		// Extract the RefundTransaction response details
		$httpResponseAr = explode("&", $httpResponse);

		$httpParsedResponseAr = array();
		foreach ($httpResponseAr as $i => $value) {
			$tmpAr = explode("=", $value);
			if (sizeof($tmpAr) > 1) {
				$httpParsedResponseAr[$tmpAr[0]] = $tmpAr[1];
			}
		}
		return $httpParsedResponseAr;
	}

}

?>