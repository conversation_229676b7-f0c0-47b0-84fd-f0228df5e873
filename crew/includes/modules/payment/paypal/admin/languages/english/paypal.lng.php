<?php

/*
  $Id: paypal.lng.php,v 1.6 2015/02/16 07:43:48 chingyen Exp $

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  DevosC, Developing open source Code
  http://www.devosc.com

  Copyright (c) 2002 osCommerce
  Copyright (c) 2004 DevosC.com

  Released under the GNU General Public License
 */

//begin ADMIN text
define('HEADING_ADMIN_TITLE', 'PayPal Instant Payment Notifications');
define('HEADING_PAYMENT_STATUS', 'Payment Status');
define('TEXT_ALL_IPNS', 'All');

define('TABLE_HEADING_ORDER_NUMBER', 'Order No');
define('TABLE_HEADING_TXN_TYPE', 'Transaction Type');
define('TABLE_HEADING_PAYMENT_STATUS', 'Payment Status');
define('TABLE_HEADING_PAYMENT_AMOUNT', 'Amount');

if (!defined('TABLE_HEADING_ACTION'))
    define('TABLE_HEADING_ACTION', 'Action');
if (!defined('TABLE_HEADING_DATE_ADDED'))
    define('TABLE_HEADING_DATE_ADDED', 'Date Added');

define('TEXT_INFO_PAYPAL_IPN_HEADING', 'PayPal IPN');
define('TEXT_DISPLAY_NUMBER_OF_IPN_TRANSACTIONS', 'Displaying <b>%d</b> to <b>%d</b> (of <b>%d</b> IPN\'s)');

//Details section
define('HEADING_DEATILS_CUSTOMER_REGISTRATION_TITLE', 'PayPal Customer Registration Details');
define('HEADING_DETAILS_REGISTRATION_TITLE', 'PayPal Instant Payment Notification');
define('TEXT_INFO_ENTRY_ADDRESS', 'Address:');
define('TEXT_INFO_ORDER_NUMBER', 'Order Number');
define('TEXT_INFO_TXN_TYPE', 'Transaction Type');
define('TEXT_INFO_PAYMENT_STATUS', 'Payment Status:');
define('TEXT_INFO_PAYMENT_AMOUNT', 'Amount');
if (!defined('ENTRY_FIRST_NAME'))
    define('ENTRY_FIRST_NAME', 'First Name');
if (!defined('ENTRY_LAST_NAME'))
    define('ENTRY_LAST_NAME', 'Last Name');
define('ENTRY_BUSINESS_NAME', 'Business Name:');
define('ENTRY_ADDRESS', 'Address');
//EMAIL ALREADY DEFINED IN ORDERS
define('ENTRY_PAYER_ID', 'Payer ID:');
define('ENTRY_PAYER_STATUS', 'Payer Status:');
define('ENTRY_ADDRESS_STATUS', 'Address Status:');
define('ENTRY_PAYMENT_TYPE', 'Payment Type:');
define('TABLE_HEADING_ENTRY_PAYMENT_STATUS', 'Payment Status:');
define('TABLE_HEADING_PENDING_REASON', 'Pending Reason');
define('TABLE_HEADING_IPN_DATE', 'IPN Date');
define('ENTRY_INVOICE', 'Invoice:');
define('ENTRY_PAYPAL_IPN_TXN', 'Transaction ID:');
define('ENTRY_PAYMENT_DATE', 'Payment Date:');
define('ENTRY_PAYMENT_LAST_MODIFIED', 'Last modified:');
define('ENTRY_MC_CURRENCY', 'MC Currency:');
define('ENTRY_MC_GROSS', 'MC Gross:');
define('ENTRY_MC_FEE', 'MC Fee:');
define('ENTRY_PAYMENT_GROSS', 'Payment Gross');
define('ENTRY_PAYMENT_FEE', 'Payment Fee');
define('ENTRY_SETTLE_AMOUNT', 'Settle Amount:');
define('ENTRY_SETTLE_CURRENCY', 'Settle Currency:');
define('ENTRY_EXCHANGE_RATE', 'Exchange Rate:');
define('ENTRY_CART_ITEMS', 'No Of Cart Items:');
define('ENTRY_CUSTOMER_COMMENTS', 'Customer Comments:');
define('TEXT_NO_IPN_HISTORY', 'No IPN history available');
define('EMAIL_PAYPAL_VERIFY_SEND_LINK', 'Click here to send paypal verification e-mail');
define('TEXT_TXN_SIGNATURE', 'Transaction Signature');
//end ADMIN text
//key langauge
define('MODULE_PAYMENT_PAYPAL_LNG_ID', 'PayPal ID');
define('MODULE_PAYMENT_PAYPAL_LNG_EWP_CERT_ID', 'PayPal Cert ID');
define('MODULE_PAYMENT_PAYPAL_LNG_BUSINESS_ID', 'PayPal Business ID');
define('MODULE_PAYMENT_PAYPAL_LNG_API_USERNAME', 'PayPal Username');
define('MODULE_PAYMENT_PAYPAL_LNG_API_PASSWORD', 'PayPal Password');
define('MODULE_PAYMENT_PAYPAL_LNG_API_SIGNATURE', 'PayPal Signature');
define('MODULE_PAYMENT_PAYPAL_LNG_PRIVATE_KEY_FILE', 'Private Key File');
define('MODULE_PAYMENT_PAYPAL_LNG_PUBLIC_KEY_FILE', 'Public Key File');
define('MODULE_PAYMENT_PAYPAL_LNG_PAYPAL_PUBLIC_KEY_FILE', 'PayPal Public Key File');
define('MODULE_PAYMENT_TAX', 'Tax');
?>
