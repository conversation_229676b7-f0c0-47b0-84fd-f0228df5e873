<?
/*
  	$Id: orders.inc.php,v 1.31 2015/10/23 10:42:28 sionghuat.chng Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	DevosC, Developing open source Code
  	http://www.devosc.com
	
  	Copyright (c) 2003 osCommerce
  	Copyright (c) 2004 DevosC.com
	
  	Released under the GNU General Public License
*/

require_once(DIR_FS_CATALOG_MODULES . 'payment/paypal.php');
require_once(DIR_FS_CATALOG_MODULES . 'payment/paypal/functions/paypal.fnc.php');
paypal_include_lng(DIR_FS_CATALOG_MODULES . 'payment/paypal/admin/languages/', $language, 'paypal.lng.php');
require_once(DIR_FS_CATALOG_MODULES . 'payment/paypal/database_tables.inc.php');

include_once(DIR_FS_CATALOG_MODULES . 'payment/paypal/classes/ipn_query.class.php');
$paypal = new ipn_query($order->order_id);

$customer_share_paypal_payer_id_array = array();
$payer_id_select_sql = "SELECT DISTINCT customers_id 
						FROM " . TABLE_ORDERS . " AS o 
						INNER JOIN " . TABLE_PAYPAL . " AS paypal 
							ON (o.orders_id = paypal.invoice)
						WHERE paypal.payer_id = '" . tep_db_input($paypal->customer['payer_id']) ."'";
$payer_id_result = tep_db_query($payer_id_select_sql);
while ($payer_id_row = tep_db_fetch_array($payer_id_result)) {
	$customer_share_paypal_payer_id_array[] = $payer_id_row["customers_id"];
}
?>
<table border="0" width="100%" cellspacing="0" cellpadding="2">
	<tr>
		<td class="main" colspan="3">
      		<table border="0" width="100%" cellspacing="0" cellpadding="2">
      			<tr>
      				<td class="main" width="12%" nowrap><b><?=ENTRY_PAYMENT_METHOD?></b></td>
      				<td class="main">
      					<div>
      						<? if((int)$order->info['payment_methods_id']>0) { ?><div class="<?=((int)$payment_info_array[$order->info['payment_methods_id']]->confirm_complete_days>0?'rp_icon':'nrp_icon')?>"><b><?=((int)$payment_info_array[$order->info['payment_methods_id']]->confirm_complete_days>0?'RP':'NRP')?></b></div> <? } ?>
      						<div style="width:30px; height:15px; background-color: <?=$payment_module_info['display_colour']?>; float: left; vertical-align: top">&nbsp;</div>
      						<div style="float: left; vertical-align: top">
      							&nbsp;&nbsp;
<?
	if ($view_payment_details_permission) {
		if (tep_not_null($last_payment_method) && $last_payment_method['payment_methods_id'] != $order->info['payment_methods_id']) {
			echo '<span class="redIndicator">'.(tep_not_null($payment_module_info['payment_methods_parent_title'])?$payment_module_info['payment_methods_parent_title'] . ' - ' :'') . $payment_module_info['payment_methods_title'].'</span>';
		} else {
			echo (tep_not_null($payment_module_info['payment_methods_parent_title'])?$payment_module_info['payment_methods_parent_title'] . ' - ' :'') . $payment_module_info['payment_methods_title'];
		}
		
		if (tep_not_null($last_payment_method) && $last_payment_method['payment_methods_id'] != $order->info['payment_methods_id']) {
			echo '&nbsp;&nbsp;<span class="redIndicator">(Last Payment Method: ' . (tep_not_null($last_payment_method['parent_payment_methods_title'])? $last_payment_method['parent_payment_methods_title'] . ' - ' : '' ) . $last_payment_method['payment_methods_title'] . ')</span>';
		}
	} else {
		echo (tep_not_null($payment_module_info['payment_methods_parent_title'])?$payment_module_info['payment_methods_parent_title'] . ' - ' :'') . $payment_module_info['payment_methods_title'];
	}
?>
      						</div>
      					</div>
      				</td>
				</tr>
			</table>
		</td>
	</tr>
<?
if ($view_payment_details_permission) {
?>
  	<tr>
    	<td class="main" colspan="3">
      		<table border="0" width="100%" cellspacing="0" cellpadding="2">
      			<tr valign="top">
        			<td>
              			<table border="0" cellspacing="0" cellpadding="2">
              				<tr>
                				<td class="main" valign="top" nowrap><b><?=ENTRY_CUSTOMER?></b></td>
                				<td class="main">&nbsp;</td>
                				<td class="main" nowrap>
                				<?
                					$paypal_payer_name = $paypal->customer['first_name'] . ' ' . $paypal->customer['last_name'];
                					
                					$name_not_match_profile = (strtolower($paypal_payer_name) != strtolower($order->customer["name"])) ? 1 : 0 ;
                					
                					$customer_with_this_name_array = tep_check_duplicate_name(array($paypal->customer['first_name'], $paypal->customer['last_name']), 'paypal');	// including himself
                					$name_used_by_other = (count($customer_with_this_name_array) > 1) ? 1 : 0 ;
                					
                					$total_name_used_in_paypal = tep_get_distinct_name_used ($cid, $paypal_payer_name, 'paypal');
                					$total_name_used = (count($total_name_used_in_paypal) > 1) ? 1 : 0 ;
                					
                					echo '<span class="'.(($name_not_match_profile+$name_used_by_other+$total_name_used > 0) ? 'redIndicator' : 'blackIndicator').'">' . $paypal->customer['first_name'] . ' ' . $paypal->customer['last_name'] . '</span>';
                					
                					$name_alert_reason = '';
                					$name_alert_reason .= ($name_not_match_profile == 1) ? '<span class="redIndicator">Name does not match profile.</span><br>' : '';
                					
                					if ($name_used_by_other == 1) {
	                					$name_alert_reason .=	tep_draw_form('cust_lists_share_name', FILENAME_CUSTOMERS, 'action=show_report&customer_lists_subaction=do_search', 'post', 'target="_blank"') . "\n" . 
																tep_draw_hidden_field('customer_share_id', tep_array_serialize($customer_with_this_name_array)) . "\n" . 
																'<span class="redIndicator">Name exists in <a href="javascript: document.cust_lists_share_name.submit();"><u>' . count($customer_with_this_name_array) . '</u></a> profiles.</span><br>' . 
																'</form>' . "\n";
									}
                					$name_alert_reason .= ($total_name_used == 1) ? '<span class="redIndicator"><a href="'.tep_href_link(FILENAME_ORDERS, 'cID='.$cid).'" target="_blank"><u>' . count($total_name_used_in_paypal) . '</u></a> names used in PayPal orders.</span><br>' : '';
                					
                					if (tep_not_null($paypal_payer_name) && tep_not_null($name_alert_reason)) {
                						echo '<br><span class="smallText">' . $name_alert_reason . '</span>';
                					}
                				?>
                				</td>
              				</tr>
              				<tr>
                				<td class="main" nowrap><b><?=ENTRY_BUSINESS_NAME?></b></td>
                				<td class="main">&nbsp;</td>
                				<td class="main" nowrap><?php echo $paypal->customer['payer_business_name'];?></td>
              				</tr>
              				<tr>
                				<td class="main" valign="top" nowrap><b><?=ENTRY_EMAIL_ADDRESS?></b></td>
                				<td class="main">&nbsp;</td>
                				<td class="main" nowrap>
                				<?
                					if (strtolower($paypal->customer['payer_email']) != strtolower($order->customer['email_address'])) {
                						echo '<span class="redIndicator">'.$paypal->customer['payer_email'].'</span>&nbsp;&nbsp;';
                						if ($paypal->customer['payer_email_verified'] == 1) {
                							echo tep_image(DIR_WS_ICONS."ok.gif", "", "", "", 'align="top" onMouseover="ddrivetip(\'' . TEXT_IMAGE_MOUSEOVER_VERIFY. '\', \'\' , 180);" onMouseout="hideddrivetip();"');
                						} else {
                							$confirm_send_profile_email_verification_url = tep_href_link(FILENAME_ORDERS, 'oID=' . $oID . '&action=edit&subaction=send_paypal_verify_email') . '&cid=' . $cid;
                							echo tep_image(DIR_WS_ICONS."off.gif", "", "", "", 'align="top" onMouseover="ddrivetip(\'' . TEXT_IMAGE_MOUSEOVER_NOT_VERIFY . '\', \'\' , 201);" onMouseout="hideddrivetip();"') . '&nbsp;' . '<a href="" onclick="confirm_action(\'Are you sure you want to send verification e-mail?\', \'' . $confirm_send_profile_email_verification_url . '\'); return false;">'. EMAIL_PAYPAL_VERIFY_SEND_LINK .'</a>';
                						}
                						if (tep_not_null($paypal->customer['payer_email']))
                							echo '<br><span class="smallText"><span class="redIndicator">Email does not match profile.</span></span>';
                					} else {
                						echo $paypal->customer['payer_email'] . '&nbsp;&nbsp;';
                						
                						if ($paypal->customer['payer_email_verified'] == 1) {
                							echo tep_image(DIR_WS_ICONS."ok.gif", "", "", "", 'align="top" onMouseover="ddrivetip(\'' . TEXT_IMAGE_MOUSEOVER_VERIFY. '\', \'\' , 180);" onMouseout="hideddrivetip();"');
                						} else {
                							$confirm_send_profile_email_verification_url = tep_href_link(FILENAME_ORDERS, 'oID=' . $oID . '&action=edit&subaction=send_paypal_verify_email') . '&cid=' . $cid;
                							echo tep_image(DIR_WS_ICONS."off.gif", "", "", "", 'align="top" onMouseover="ddrivetip(\'' . TEXT_IMAGE_MOUSEOVER_NOT_VERIFY . '\', \'\' , 201);" onMouseout="hideddrivetip();"') . '&nbsp;' . '<a href="" onclick="confirm_action(\'Are you sure you want to send verification e-mail?\', \'' . $confirm_send_profile_email_verification_url . '\'); return false;">'. EMAIL_PAYPAL_VERIFY_SEND_LINK .'</a>';
                						}
                					}
                				?></td>
              				</tr>
              				<tr>
                				<td class="main" valign="top" nowrap><b><?=ENTRY_PAYER_ID?></b></td>
                				<td class="main">&nbsp;</td>
                				<td class="main" nowrap>
<?                					if (count($customer_share_paypal_payer_id_array) > 1) { 
										echo 	tep_draw_form('cust_lists_share_payer_id', FILENAME_CUSTOMERS, tep_get_all_get_params(array('action')) . 'action=show_report&customer_lists_subaction=do_search', 'post', 'target="customerSharePayerIDWin"') . "\n" .
												tep_draw_hidden_field('customer_share_id', tep_array_serialize($customer_share_paypal_payer_id_array)) . "\n" . 
												'<span class="smallText"><span class="redIndicator">' . $paypal->customer['payer_id'] . '<br><a href="javascript: document.cust_lists_share_payer_id.submit();"><u>' . count($customer_share_paypal_payer_id_array) . '</u></a> customers sharing the same Payer ID.</span></span>' . 
												'</form>' . "\n";
                					} else {
                						echo $paypal->customer['payer_id'];
                					}
                					if (tep_not_null($paypal->customer['payer_id'])) {
										$payment_check_info = array('payer_id' => $paypal->customer['payer_id'],
																	'orders_id' => $order->order_id,
																	'date_purchased' => $order->info['date_purchased'],
																	'customers_id' => $order->customer['id']);
										$payment_verified_date = tep_get_payment_info_verified_date($payment_check_info, 'paypal_payer_id');
	                					echo "<br><span class='redIndicator'>First successful verified date by this user: ".(tep_not_null($payment_verified_date) ? $payment_verified_date : "NEVER"). "</span>";
	                				}
?>
                				</td>
              				</tr>
              				<tr>
                				<td class="main" nowrap><b><?=ENTRY_PAYER_STATUS?></b></td>
                				<td class="main">&nbsp;</td>
                				<td class="main" nowrap><?php echo $paypal->customer['payer_status'];?></td>
              				</tr>
              			</table>
        			</td>
        			<td style="padding-left:50px;">
              			<table border="0" cellspacing="0" cellpadding="2">
              				<tr valign="top">
                				<td class="main" style="padding-right: 10px;" nowrap><?php echo '<b>' . ENTRY_ADDRESS . '</b><br>' . ($paypal->customer['address_status']=='unconfirmed' ? '<span class="redIndicator">'.$paypal->customer['address_status'].'</span>' : $paypal->customer['address_status']) ; ?></td>
                				<td class="main" nowrap><?php echo $paypal->customer['address_name'] . '<br>' . $paypal->customer['address_street'] . '<br>' . $paypal->customer['address_city'] . '<br>' . $paypal->customer['address_state'] . '<br>' . $paypal->customer['address_zip'] . '<br>' . $paypal->customer['address_country']; ?></td>
              				</tr>
              			</table>
        			</td>
        			<td style="padding-left:50px;">
              			<table border="0" cellspacing="0" cellpadding="2">
              				<tr>
                				<td class="main" nowrap><b><?=ENTRY_PAYMENT_TYPE?></b>&nbsp;</td><td class="main" nowrap><?php echo $paypal->info['payment_type'];?></td>
              				</tr>
              				<tr>
                				<td class="main" nowrap><b><?=ENTRY_CART_ITEMS?></b>&nbsp;</td><td class="main"><?php echo $paypal->txn['num_cart_items'];?></td>
              				</tr>
              				<tr>
                				<td class="main" nowrap><b><?=ENTRY_PAYPAL_IPN_TXN?></b>&nbsp;</td><td class="main" nowrap><?php echo $paypal->info['txn_id'];?></td>
              				</tr>
<?
		$txn_signature_query = tep_db_query("select txn_signature from " . TABLE_ORDERS_SESSION_INFO . " where orders_id = '" . (int)$oID . "' limit 1");
    	if (tep_db_num_rows($txn_signature_query)) {
     	 	$txn_signature = tep_db_fetch_array($txn_signature_query);
     	 	echo '			<tr>' .
       	      	 '  			<td class="main" nowrap><b>' . TEXT_TXN_SIGNATURE . '</b>&nbsp;</td><td class="main" nowrap>' . $txn_signature['txn_signature'] . '</td>' .
       	      	 '			</tr>';
		}
?>
              				<tr>
                				<td class="main" nowrap><b><?=ENTRY_INVOICE?></b>&nbsp;</td><td class="main" nowrap><?php echo $paypal->info['invoice'];?></td>
              				</tr>
              			</table>
        			</td>
      			</tr>
      		</table>
   		</td>
  	</tr>
  	<tr>
    	<td colspan="3"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
  	</tr>
  	<tr valign="top">
    	<td class="main" colspan="2">
      		<table border="0" cellspacing="0" cellpadding="2">
      			<tr valign="top">
        			<td>
              			<table border="0" cellspacing="0" cellpadding="2">
              				<tr>
                				<td class="main" valign="top" nowrap><b><?=ENTRY_MC_CURRENCY?></b></td>
                				<td class="main" valign="top" nowrap>
                				<?
                				if($paypal->info['mc_currency'] != $order->info["currency"]) {
                					echo '<span class="redIndicator">'.$paypal->info['mc_currency'].'<br><span class="smallText">Does not match purchase currency.</span></span>';
                				} else {
                					echo $paypal->info['mc_currency'];
                				}
                				?>
                				</td>
              				</tr>
              				<tr>
                				<td class="main" valign="top" nowrap><b><?=ENTRY_MC_GROSS?></b></td>
                				<td class="main" valign="top" nowrap>
                				<?
                				$mc_gross_display_text = $paypal->txn['mc_gross'];
                				/*
                				for ($i=0; $i < sizeof($order->totals); $i++) {
                					if ($order->totals[$i]['class'] == 'ot_total') {
                						if ($paypal->txn['mc_gross'] != preg_replace('/[^\d.]/', '', strip_tags($order->totals[$i]['text']))) {
                							$mc_gross_display_text = '<span class="redIndicator">'.$paypal->txn['mc_gross'].'<br><span class="smallText">Does not match purchase amount.</span></span>';
                						}
                						break;
                					}
								}
								*/
								//check the payment_amount
							    $paypal_gross_amt = number_format($paypal->txn['mc_gross'], $currencies->currencies[$order->info["currency"]]['decimal_places'], $currencies->currencies[$order->info["currency"]]['decimal_point'], $currencies->currencies[$order->info["currency"]]['thousands_point']);
							    if ( $currencies->currencies[$order->info["currency"]]['symbol_left'].$paypal_gross_amt.$currencies->currencies[$order->info["currency"]]['symbol_right'] != strip_tags($order->order_totals['ot_total']['text']) ) {
									$mc_gross_display_text = '<span class="redIndicator">'.$paypal->txn['mc_gross'].'<br><span class="smallText">Does not match purchase amount.</span></span>';
							    }
							    
								echo $mc_gross_display_text;
                				?>
                				</td>
              				</tr>
              				<tr>
                				<td class="main" valign="top" nowrap><b><?=ENTRY_MC_FEE?></b></td>
                				<td class="main" valign="top"><?php echo $paypal->txn['mc_fee'];?></td>
              				</tr>
              			</table>
     				</td>
     				<td class="main" style="padding-left:50px;">
              			<table border="0" cellspacing="0" cellpadding="2">
              				<tr>
                				<td class="main" valign="top" nowrap><b><?=ENTRY_SETTLE_AMOUNT?></b></td>
                				<td class="main" valign="top">&nbsp;<?php echo $paypal->txn['settle_amount'];?></td>
              				</tr>
              				<tr>
                				<td class="main" valign="top" nowrap><b><?=ENTRY_SETTLE_CURRENCY?></b></td>
                				<td class="main" valign="top">&nbsp;<?php echo $paypal->txn['settle_currency'];?></td>
              				</tr>
              				<tr>
                				<td class="main" valign="top" nowrap><b><?=ENTRY_EXCHANGE_RATE?></b></td>
                				<td class="main" valign="top">&nbsp;<?php echo $paypal->txn['exchange_rate'];?></td>
              				</tr>
              			</table>
     				</td>
      			</tr>
      		</table>
   		</td>
   		<td class="main" width="100%" height="100%" rowspan="2" style="padding-left:50px;">
			<table border="0" width="100%" height="100%" cellspacing="2" cellpadding="0">
            	<tr valign="top">
                	<td class="main" nowrap><?php echo '<b>' . ENTRY_CUSTOMER_COMMENTS . '</b>'; if (tep_not_null($paypal->customer['memo'])) echo ' (' . tep_datetime_short($paypal->info['payment_date'], PREFERRED_DATE_TIME_FORMAT) . ') '; ?></td>
              	</tr>
<?		if (tep_not_null($paypal->customer['memo'])) { ?>
              	<tr valign="top">
                	<td class="main" height="100%">
                  		<?php echo '<div style="border:0px solid gray; padding: 5px auto; width:100%;">' . nl2br(tep_db_output($paypal->customer['memo'])) . '</div>' ;?>
                	</td>
              	</tr>
<? 		} ?>
			</table>
   		</td>
  	</tr>
  	<tr valign="top">
    	<td class="main" colspan="3">
        	<table border="1" cellspacing="0" cellpadding="5">
          		<tr>
            		<td class="smallText" align="center" nowrap><b><?=TABLE_HEADING_IPN_DATE?></b></td>
            		<td class="smallText" align="center" nowrap><b><?=TABLE_HEADING_PAYMENT_STATUS?></b></td>
            		<td class="smallText" align="center" nowrap><b><?=TABLE_HEADING_PENDING_REASON?></b></td>
          		</tr>
<?
		$paypal_history_query =tep_db_query("	SELECT ppsh.payment_status, ppsh.pending_reason, ppsh.date_added 
												FROM " . TABLE_PAYPAL_PAYMENT_STATUS_HISTORY . " AS ppsh
												INNER JOIN " . TABLE_PAYPAL . " AS p
													ON (ppsh.paypal_ipn_id = p.paypal_ipn_id)
												WHERE p.invoice = '" . tep_db_input($order->order_id) ."'");
		if (tep_db_num_rows($paypal_history_query)) {
    		while ($paypal_history = tep_db_fetch_array($paypal_history_query)) {
       	 		echo '	<tr>' . "\n" .
             	 	 '		<td class="smallText" align="center" nowrap>' . tep_datetime_short($paypal_history['date_added'], PREFERRED_DATE_TIME_FORMAT) . '</td>' . "\n".
             	 	 '  	<td class="smallText" nowrap>' . $paypal_history['payment_status'] . '</td>' . "\n" .
             	 	 '  	<td class="smallText" align="center" nowrap>'. (tep_not_null($paypal_history['pending_reason']) ? $paypal_history['pending_reason'] : '&nbsp;') . '</td>' . "\n" .
             	 	 '	</tr>' . "\n";
      		}
		} else {
        	echo '	<tr>' . "\n" .
             	 '  	<td class="smallText" colspan="3" nowrap>' . TEXT_NO_IPN_HISTORY . '</td>' . "\n" .
             	 '	</tr>' . "\n";
    	}
?>
			</table>
   		</td>
	</tr>
<?
}
?>
</table>