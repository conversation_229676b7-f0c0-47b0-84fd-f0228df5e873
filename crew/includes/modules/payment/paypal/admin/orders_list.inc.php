<?
/*
  	$Id: orders_list.inc.php,v 1.3 2015/12/09 02:30:53 sionghuat.chng Exp $
	
	Developer: <PERSON>
  	Copyright (c) 2005 SKC Venture
  	
  	Released under the GNU General Public License
*/

require_once(DIR_FS_CATALOG_MODULES . 'payment/paypal.php');
require_once(DIR_FS_CATALOG_MODULES . 'payment/paypal/functions/paypal.fnc.php');
paypal_include_lng(DIR_FS_CATALOG_MODULES . 'payment/paypal/admin/languages/', $order_obj->language, 'paypal.lng.php');
require_once(DIR_FS_CATALOG_MODULES . 'payment/paypal/database_tables.inc.php');

include_once(DIR_FS_CATALOG_MODULES . 'payment/paypal/classes/ipn_query.class.php');
$paypal = new ipn_query($order_obj->orders_id);
?>
<table border="0" width="100%" cellspacing="0" cellpadding="2">
	<tr>
		<td class="subInvoiceBoxHeading">
			<div style="border-bottom: 1px solid #996600; border-top: 1px solid #996600; padding: 2px;">Payment Details</div>
		</td>
	</tr>
  	<tr>
    	<td>
      		<table border="0" width="100%" cellspacing="0" cellpadding="2">
      			<tr valign="top">
        			<td width="40%">
              			<table border="0" cellspacing="0" cellpadding="2">
              				<tr>
                				<td class="invoiceRecords" valign="top" nowrap><?=ENTRY_CUSTOMER?></td>
                				<td class="invoiceRecords">:&nbsp;</td>
                				<td class="invoiceRecords" nowrap><?=$paypal->customer['first_name'] . ' ' . $paypal->customer['last_name']?></td>
              				</tr>
              				<tr>
                				<td class="invoiceRecords" nowrap><?=ENTRY_BUSINESS_NAME?></td>
                				<td class="invoiceRecords">:&nbsp;</td>
                				<td class="invoiceRecords" nowrap><?=$paypal->customer['payer_business_name']?></td>
              				</tr>
              				<tr>
                				<td class="invoiceRecords" valign="top" nowrap><?=ENTRY_EMAIL_ADDRESS?></td>
                				<td class="invoiceRecords">:&nbsp;</td>
                				<td class="invoiceRecords" nowrap><?=$paypal->customer['payer_email']?></td>
              				</tr>
              				<tr>
                				<td class="invoiceRecords" nowrap><?=ENTRY_PAYER_ID?></td>
                				<td class="invoiceRecords">:&nbsp;</td>
                				<td class="invoiceRecords" nowrap><?=$paypal->customer['payer_id']?></td>
              				</tr>
              				<tr>
                				<td class="invoiceRecords" nowrap><?=ENTRY_PAYER_STATUS?></td>
                				<td class="invoiceRecords">:&nbsp;</td>
                				<td class="invoiceRecords" nowrap><?=$paypal->customer['payer_status']?></td>
              				</tr>
              			</table>
        			</td>
        			<td width="30%">
              			<table border="0" cellspacing="0" cellpadding="2">
              				<tr valign="top">
                				<td class="invoiceRecords" style="padding-right: 10px;" nowrap><?=ENTRY_ADDRESS . '<br>' . $paypal->customer['address_status']?></td>
                				<td class="invoiceRecords">:&nbsp;</td>
                				<td class="invoiceRecords" nowrap><?=$paypal->customer['address_name'] . '<br>' . $paypal->customer['address_street'] . '<br>' . $paypal->customer['address_city'] . '<br>' . $paypal->customer['address_state'] . '<br>' . $paypal->customer['address_zip'] . '<br>' . $paypal->customer['address_country']?></td>
              				</tr>
              			</table>
        			</td>
        			<td width="30%">
              			<table border="0" cellspacing="0" cellpadding="2">
              				<tr>
                				<td class="invoiceRecords" nowrap><?=ENTRY_PAYMENT_TYPE?></td>
                				<td class="invoiceRecords">:&nbsp;</td>
                				<td class="invoiceRecords" nowrap><?=$paypal->info['payment_type']?></td>
              				</tr>
              				<tr>
                				<td class="invoiceRecords" nowrap><?=ENTRY_CART_ITEMS?></td>
                				<td class="invoiceRecords">:&nbsp;</td>
                				<td class="invoiceRecords"><?=$paypal->txn['num_cart_items']?></td>
              				</tr>
              				<tr>
                				<td class="invoiceRecords" nowrap><?=ENTRY_PAYPAL_IPN_TXN?></td>
                				<td class="invoiceRecords">:&nbsp;</td>
                				<td class="invoiceRecords" nowrap><?=$paypal->info['txn_id']?></td>
              				</tr>
<?
	$txn_signature_query = tep_db_query("select txn_signature from " . TABLE_ORDERS_SESSION_INFO . " where orders_id = '" . (int)$order_obj->orders_id . "' limit 1");
    if (tep_db_num_rows($txn_signature_query)) {
      	$txn_signature = tep_db_fetch_array($txn_signature_query);
      	echo '				<tr>' .
             '  				<td class="invoiceRecords" nowrap>' . TEXT_TXN_SIGNATURE . '</td><td class="invoiceRecords">:&nbsp;</td><td class="invoiceRecords" nowrap>' . $txn_signature['txn_signature'] . '</td>' .
             '				</tr>';
	}
?>
              				<tr>
                				<td class="invoiceRecords" nowrap><?=ENTRY_INVOICE?></td>
                				<td class="invoiceRecords">:&nbsp;</td>
                				<td class="invoiceRecords" nowrap><?=$paypal->info['invoice']?></td>
              				</tr>
              			</table>
        			</td>
      			</tr>
      		</table>
   		</td>
  	</tr>
  	<tr>
    	<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
  	</tr>
  	<tr valign="top">
  		<td>
      		<table border="0" width="100%" cellspacing="0" cellpadding="2">
      			<tr valign="top">
			  		<td width="40%">
			  			<table border="0" cellspacing="0" cellpadding="2">
			  				<tr>
			    				<td class="invoiceRecords" nowrap><?=ENTRY_MC_CURRENCY?></td>
			    				<td class="invoiceRecords">:&nbsp;</td>
			    				<td class="invoiceRecords"><?=$paypal->info['mc_currency']?></td>
			  				</tr>
			  				<tr>
			    				<td class="invoiceRecords" nowrap><?=ENTRY_MC_GROSS?></td>
			    				<td class="invoiceRecords">:&nbsp;</td>
			    				<td class="invoiceRecords"><?=$paypal->txn['mc_gross']?></td>
			  				</tr>
			  				<tr>
			    				<td class="invoiceRecords" nowrap><?=ENTRY_MC_FEE?></td>
			    				<td class="invoiceRecords">:&nbsp;</td>
			    				<td class="invoiceRecords"><?=$paypal->txn['mc_fee']?></td>
			  				</tr>
			  			</table>
					</td>
					<td width="30%">
			  			<table border="0" cellspacing="0" cellpadding="2">
			  				<tr>
			    				<td class="invoiceRecords" nowrap><?=ENTRY_SETTLE_AMOUNT?></td>
			    				<td class="invoiceRecords">:&nbsp;</td>
			    				<td class="invoiceRecords"><?=$paypal->txn['settle_amount']?></td>
			  				</tr>
			  				<tr>
			    				<td class="invoiceRecords" nowrap><?=ENTRY_SETTLE_CURRENCY?></td>
			    				<td class="invoiceRecords">:&nbsp;</td>
			    				<td class="invoiceRecords"><?=$paypal->txn['settle_currency']?></td>
			  				</tr>
			  				<tr>
			    				<td class="invoiceRecords" nowrap><?=ENTRY_EXCHANGE_RATE?></td>
			    				<td class="invoiceRecords">:&nbsp;</td>
			    				<td class="invoiceRecords"><?=$paypal->txn['exchange_rate']?></td>
			  				</tr>
			  			</table>
					</td>
			   		<td class="main" width="30%" height="100%">
						<table border="0" height="100%" cellspacing="2" cellpadding="0">
			            	<tr valign="top">
			                	<td class="invoiceRecords" nowrap><?=ENTRY_CUSTOMER_COMMENTS?></td>
			                	<td class="invoiceRecords">:&nbsp;</td>
			                	<td class="invoiceRecords">
<?	                				if (tep_not_null($paypal->customer['memo'])) echo ' (' . tep_datetime_short($paypal->info['payment_date']) . ') '; ?>
								</td>
			              	</tr>
<?
	if (tep_not_null($paypal->customer['memo'])) { ?>
			              	<tr valign="top">
			                	<td colspan="3" class="invoiceRecords" height="100%">
			                  		<?php echo '<div style="border:0px solid gray; padding: 5px auto; width:100%;">' . nl2br(tep_db_output($paypal->customer['memo'])) . '</div>' ;?>
			                	</td>
			              	</tr>
<? 	} ?>
						</table>
			   		</td>
				</tr>
			</table>
		</td>
  	</tr>
</table>