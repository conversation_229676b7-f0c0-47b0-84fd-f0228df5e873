<?
/*
  	$Id: checkout_process.inc.php,v 1.78 2015/05/28 10:51:32 darren.ng Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	DevosC, Developing open source Code
  	http://www.devosc.com
	
  	Copyright (c) 2003 osCommerce
  	Copyright (c) 2004 DevosC.com
	
  	Released under the GNU General Public License
*/

global 	$payment_modules, $shipping_modules, $order, $currencies, $cart, $paypal_order, $customer_id,
		$sendto, $billto, $shipping, $payment, $language, $currency, $languages_id, $order, $order_totals, $$payment,
		$default_languages_id;
global $is_sc_checkout, $memcache_obj;

require_once(DIR_WS_INCLUDES . 'modules/payment/paypal/database_tables.inc.php');
include_once(DIR_WS_FUNCTIONS . 'customers_info_verification.php');
require_once(DIR_WS_CLASSES . 'anti_fraud.php');
require_once(DIR_WS_CLASSES . 'promo.php');

/*-- GST :: return to CHECKOUT PAYMENT page when country and currency changed --*/
localization::verify_gst_condition();

if (tep_not_null($_SESSION['RegionGST']) && (($_SESSION['RegionGST']['loc_country'] != $_SESSION['country']) || ($_SESSION['RegionGST']['currency'] != $_SESSION['currency']))) {
	tep_redirect(tep_href_link(FILENAME_CHECKOUT_PAYMENT, '', 'SSL'));
}
validate_max_pending_order($_SESSION['customer_id'], 'Paypal');
if(!class_exists('order_total')) {
	include_once(DIR_WS_CLASSES . 'order_total.php');
    $order_total_modules = new order_total;
    if (isset($is_sc_checkout) && $is_sc_checkout) {
    	$order_totals = $order_total_modules->process(true);
    } else {
    	$order_totals = $order_total_modules->process();
    }
}
include_once(DIR_WS_CLASSES . 'store_credit.php');

$payment_file = 'paypal.php';
$ip = tep_get_ip_address();

$payment_methods_id_select_sql = "	SELECT pm.payment_methods_parent_id, pm.payment_methods_id 
									FROM " . TABLE_PAYMENT_METHODS . " AS pm 
									WHERE pm.payment_methods_id = '" . (int)$$payment->payment_methods_id . "' 
									LIMIT 1";
$payment_methods_id_result_sql = tep_db_query($payment_methods_id_select_sql);
$payment_methods_id_row = tep_db_fetch_array($payment_methods_id_result_sql);

$payment_cfg_info_value_select_sql = "	SELECT pcid.payment_configuration_info_value 
										FROM " . TABLE_PAYMENT_CONFIGURATION_INFO . " AS pci 
										LEFT JOIN " . TABLE_PAYMENT_CONFIGURATION_INFO_DESCRIPTION . " as pcid 
											ON (pci.payment_configuration_info_id = pcid.payment_configuration_info_id) 
										WHERE pci.payment_methods_id = '" . (int)$payment_methods_id_row['payment_methods_id'] . "' 
											AND pci.payment_configuration_info_key = 'MODULE_PAYMENT_PAYPAL_PROCESSING_STATUS_ID' 
										LIMIT 1";
$payment_cfg_info_value_result_sql = tep_db_query($payment_cfg_info_value_select_sql);
if (tep_db_num_rows($payment_cfg_info_value_result_sql) > 0) {
	$payment_cfg_info_value_row = tep_db_fetch_array($payment_cfg_info_value_result_sql);
} else {
	$payment_cfg_info_value_select_sql = "	SELECT pcid.payment_configuration_info_value 
											FROM " . TABLE_PAYMENT_CONFIGURATION_INFO . " AS pci 
											LEFT JOIN " . TABLE_PAYMENT_CONFIGURATION_INFO_DESCRIPTION . " as pcid 
												ON (pci.payment_configuration_info_id = pcid.payment_configuration_info_id) 
											WHERE pci.payment_methods_id = '" . (int)$payment_methods_id_row['payment_methods_parent_id'] . "' 
												AND pci.payment_configuration_info_key = 'MODULE_PAYMENT_PAYPAL_PROCESSING_STATUS_ID' 
											LIMIT 1";
	$payment_cfg_info_value_result_sql = tep_db_query($payment_cfg_info_value_select_sql);
	$payment_cfg_info_value_row = tep_db_fetch_array($payment_cfg_info_value_result_sql);
}

$sql_data_array = array(	'customers_id' => $customer_id,
                          	'customers_name' => $order->customer['firstname'] . ' ' . $order->customer['lastname'],
                          	'customers_company' => $order->customer['company'],
                          	'customers_street_address' => $order->customer['street_address'],
                          	'customers_suburb' => $order->customer['suburb'],
                          	'customers_city' => $order->customer['city'],
                          	'customers_postcode' => $order->customer['postcode'],
                          	'customers_state' => $order->customer['state'],
                          	'customers_country' => $order->customer['country']['title'],
                          	'customers_telephone_country' => $order->customer['telephone_country_name'],
                          	'customers_country_international_dialing_code' => $order->customer['int_dialing_code'],
                          	'customers_telephone' => $order->customer['telephone'],
                          	'customers_email_address' => $order->customer['email_address'],
                          	'customers_address_format_id' => $order->customer['format_id'],
                          	'customers_groups_id' => $order->customer['customers_groups_id'],
                          	'delivery_name' => $order->delivery['firstname'] . ' ' . $order->delivery['lastname'],
                          	'delivery_company' => $order->delivery['company'],
                          	'delivery_street_address' => $order->delivery['street_address'],
                          	'delivery_suburb' => $order->delivery['suburb'],
                          	'delivery_city' => $order->delivery['city'],
                          	'delivery_postcode' => $order->delivery['postcode'],
                          	'delivery_state' => $order->delivery['state'],
                          	'delivery_country' => $order->delivery['country']['title'],
                          	'delivery_address_format_id' => $order->delivery['format_id'],
                          	'billing_name' => $order->billing['firstname'] . ' ' . $order->billing['lastname'],
                          	'billing_company' => $order->billing['company'],
                          	'billing_street_address' => $order->billing['street_address'],
                          	'billing_suburb' => $order->billing['suburb'],
                          	'billing_city' => $order->billing['city'],
                          	'billing_postcode' => $order->billing['postcode'],
                          	'billing_state' => $order->billing['state'],
                          	'billing_country' => $order->billing['country']['title'],
                          	'billing_address_format_id' => $order->billing['format_id'],
                          	'payment_method' => $order->info['payment_method'],
                          	'payment_methods_id' => $payment_methods_id_row['payment_methods_id'],
	                        'payment_methods_parent_id' => $payment_methods_id_row['payment_methods_parent_id'],
                          	'cc_type' => $order->info['cc_type'],
                          	'cc_owner' => $order->info['cc_owner'],
                          	'cc_number' => $order->info['cc_number'],
                          	'cc_expires' => $order->info['cc_expires'],
                          	'orders_status' => $payment_cfg_info_value_row['payment_configuration_info_value'],
                          	'last_modified' => 'now()',
                          	'currency' => $order->info['currency'],
                          	'remote_addr' => $ip, 
                          	'currency_value' => $order->info['currency_value']);
$session_exists = false;
/*	Commented By Wei Chen to ignore reuse of the PayPal Session
if (tep_session_is_registered('paypal_order')) {
	$orders_session_query = tep_db_query("select osi.orders_id, o.paypal_ipn_id from " . TABLE_ORDERS_SESSION_INFO . " osi left join " . TABLE_ORDERS . " o on osi.orders_id = o.orders_id where osi.txn_signature ='" . tep_db_input($paypal_order->txn_signature) . "'");
	$orders_check = tep_db_fetch_array($orders_session_query);
    //Now check to see whether order session info exists AND that this order
    //does not currently have an IPN.
    if ($orders_check['orders_id'] > 0 &&  $orders_check['paypal_ipn_id'] == '0' ) {
    	$session_exists = true;
       	$this->orders_id = $orders_check['orders_id'];
	}
}
*/

if($session_exists) {
	tep_db_perform(TABLE_ORDERS, $sql_data_array, 'update', "orders_id = '" . (int)$this->orders_id . "'");
} else {
	$sql_data_array['date_purchased'] = 'now()';
    tep_db_perform(TABLE_ORDERS, $sql_data_array);
    $this->orders_id = tep_db_insert_id();
    
    # insert notification
    $m_attr = array(
        'customers_id' => $customer_id,
        'orders_id' => $this->orders_id,
        'orders_type' => 'CO',
        'site_id' => SITE_ID
    );
    tep_db_perform(TABLE_ORDERS_NOTIFICATION, $m_attr);
    unset($m_attr);
    
    /*$aft_obj = new anti_fraud();
    $aft_module = $aft_obj->getAftModule();
    $aft_module->set_order_id($this->orders_id);
    $aft_module->set_transaction_type('CO');
    $aft_module->set_customers_id($customer_id);
    $aft_module->capture_query_call();
    unset($aft_obj, $aft_module);*/
    
    // Promo Log Scripts - Start
	$promo = new promo();
	if ($promo->is_promo_enabled() && $promo->promo_cookies_exist('promo_paypal')) {	// When promo still active[duration] and enabled + promo cookie exist
		$promo->update_order_success_log($this->orders_id);
	}
	unset($promo);
	// Promo Log Scripts - End
}

// Before delete need to add back those GV amount (whole amount) since if this order still have the credit been used it is deducted from the remaining
if ($session_exists) {
	tep_db_query("delete from " . TABLE_ORDERS_TOTAL . " where orders_id = '" . (int)$this->orders_id . "'");
}

# capture actual checkout country
$orders_extra_info = array ('orders_id' => $this->orders_id, 
							'orders_extra_info_key' => 'ip_country', 
							'orders_extra_info_value' => tep_get_ip_country_id() );
tep_db_perform(TABLE_ORDERS_EXTRA_INFO, $orders_extra_info);

for ($i=0, $n=sizeof($order_totals); $i<$n; $i++) {
	$sql_data_array = array('orders_id' => (int)$this->orders_id,
                            'title' => $order_totals[$i]['title'],
                            'text' => $order_totals[$i]['text'],
                            'value' => $order_totals[$i]['value'],
                            'class' => $order_totals[$i]['code'],
                            'sort_order' => $order_totals[$i]['sort_order']);
    tep_db_perform(TABLE_ORDERS_TOTAL, $sql_data_array);
	
	/*-- GST :: record GST percentage --*/
	if ($order_totals[$i]['code'] == 'ot_gst') {
		$orders_extra_info = array ('orders_id' => $this->orders_id, 
									'orders_extra_info_key' => 'gst_tax_percentage', 
									'orders_extra_info_value' => $ot_gst->gst_percentage );
		tep_db_perform(TABLE_ORDERS_EXTRA_INFO, $orders_extra_info);
	}
}

//////////////////////////////////////////////////////////////////////////////////////////
  
//////////////////////////////////////////////////////////////////////////////////////////
$sql_data_array = array(	'orders_status_id' => MODULE_PAYMENT_PAYPAL_PROCESSING_STATUS_ID,
                          	'date_added' => 'now()',
                          	'customer_notified' => 0,
                          	'comments' => tep_db_prepare_input($order->info['comments']));

if($session_exists) {
	tep_db_perform(TABLE_ORDERS_STATUS_HISTORY, $sql_data_array, 'update', "orders_id = '" . (int)$this->orders_id . "'");
    tep_db_query("delete from " . TABLE_ORDERS_PRODUCTS . " where orders_id = '" . (int)$this->orders_id . "'");
    tep_db_query("delete from " . TABLE_ORDERS_PRODUCTS_ATTRIBUTES . " where orders_id = '" . (int)$this->orders_id . "'");
    $sql_data_array['orders_id'] = $this->orders_id;
} else {
    $sql_data_array['orders_id'] = $this->orders_id;
    tep_db_perform(TABLE_ORDERS_STATUS_HISTORY, $sql_data_array);
}
tep_update_orders_status_counter($sql_data_array);
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

$products_ordered = '';
$subtotal = 0;
$total_tax = 0;
$custom_product_count = 1;

for ($i=0, $n=sizeof($order->products); $i<$n; $i++) {
	$product_instance_id = tep_not_null($order->products[$i]['custom_content']['hla_account_id']) ? $order->products[$i]['custom_content']['hla_account_id'] : '';
	$currencies->product_instance_id = $product_instance_id;
	
	// Update products_ordered (for bestsellers list) 
	tep_db_query("UPDATE " . TABLE_PRODUCTS . " SET products_ordered = products_ordered + " . sprintf('%d', $order->products[$i]['qty']) . " WHERE products_id = '" . tep_get_prid($order->products[$i]['id']) . "'");
	
	$currencies->display_price($order->products[$i]['id'], $order->products[$i]['normal_price'], $order->products[$i]['tax'], $order->products[$i]['qty'], false, '', $payment_methods_id_row['payment_methods_id']);
	$cust_group_extra_rebate = 0;
	if ($payment_methods_id_row['payment_methods_id'] > 0) {
		$cust_group_extra_rebate = $currencies->rebate_point_extra;
		if ($cust_group_extra_rebate > 0) {
			$surcharge_amt = 0;
			$gst_amt = 0;
			
			for ($count_order_totals=0; $count_order_totals<sizeof($order_totals); $count_order_totals++) {
				if ($order_totals[$count_order_totals]['code'] == 'ot_surcharge') {
					$surcharge_amt = $order_totals[$count_order_totals]['value'];
				} else if ($order_totals[$count_order_totals]['code'] == 'ot_gst') {
					$gst_amt = $order_totals[$count_order_totals]['value'];
				}
			}
			$cust_group_extra_rebate = floor($cust_group_extra_rebate * ( ($order->info['total']-$surcharge_amt - $gst_amt) / $order->info['subtotal']));
		}
	}
		
	if((int)$order->products[$i]['custom_products_type_id'] == 0 || (int)$order->products[$i]['custom_products_type_id'] == 2 || (int)$order->products[$i]['custom_products_type_id'] == 3) {
		if (isset($order->products[$i]['custom_content']['delivery_mode']) && $order->products[$i]['custom_content']['delivery_mode'] == '6') {
			$sql_data_array = array('orders_id' => $this->orders_id,
		                            'products_id' => tep_get_prid($order->products[$i]['id']),
		                            'products_model' => $order->products[$i]['model'],
		                            'products_name' => $order->products[$i]['name'],
		                            'orders_products_store_price' => $order->products[$i]['storage_price']['normal_price'],
		                            'products_price' => $order->products[$i]['storage_price']['price'],
		                            'final_price' => $order->products[$i]['storage_price']['final_price'],
		                            'products_tax' => $order->products[$i]['tax'],
		                            'products_quantity' => $order->products[$i]['qty'],
		                            'products_pre_order' => $order->products[$i]['pre_order'],
		                            'custom_products_type_id' => $order->products[$i]['custom_products_type_id'],
		                            'products_categories_id' => $order->products[$i]['products_categories_id'],
		                            'op_rebate' => $currencies->rebate_point + $cust_group_extra_rebate,
			                        'op_rebate_delivered' => 0);
			
		    tep_db_perform(TABLE_ORDERS_PRODUCTS, $sql_data_array);
		    $order_products_id = tep_db_insert_id();
		    
			if ($cust_group_extra_rebate > 0) {
				$order->insert_opders_products_extra_info($order_products_id, 'OP_EXTRA', $cust_group_extra_rebate);
				$currencies->rebate_point_formula = $currencies->rebate_point_formula . '<br><br>Extra OP: ' . $cust_group_extra_rebate;
			}
			
			$order->insert_opders_products_extra_info($order_products_id, 'OP_FORMULA', $currencies->rebate_point_formula);
			
			$product_query_raw = "	SELECT p.products_bundle, p.products_bundle_dynamic 
	                       			FROM " . TABLE_PRODUCTS . " AS p
	                      			LEFT JOIN " . TABLE_PRODUCTS_ATTRIBUTES . " AS pa
	                    	   			ON p.products_id=pa.products_id
	                	      		LEFT JOIN " . TABLE_PRODUCTS_ATTRIBUTES_DOWNLOAD . " AS pad
	            	           			ON pa.products_attributes_id=pad.products_attributes_id
	        	              		WHERE p.products_id = '" . tep_get_prid($order->products[$i]['id']) . "'";
			$product_query = tep_db_query($product_query_raw);
			$product_values = tep_db_fetch_array($product_query);
			
			if ($product_values['products_bundle_dynamic'] == 'yes') {
				for ($pbd_loop=0; $pbd_loop<count($order->products[$i]["bundle"]); $pbd_loop++) {
					if ($order->products[$i]["bundle"][$pbd_loop]["qty"]) {
						$dynamic_subproduct_select_sql = "	SELECT subproduct_qty 
															FROM ". TABLE_PRODUCTS_BUNDLES . "
															WHERE bundle_id = " . tep_get_prid($order->products[$i]["id"]) . "
																AND subproduct_id = " . tep_get_prid($order->products[$i]["bundle"][$pbd_loop]["id"]) ;
						$dynamic_subproduct_result_sql = tep_db_query($dynamic_subproduct_select_sql);
						if ($dynamic_subproduct_row = tep_db_fetch_array($dynamic_subproduct_result_sql)) {
							tep_db_query("UPDATE " . TABLE_PRODUCTS . " SET products_ordered = products_ordered + " . sprintf('%d', $order->products[$i]["bundle"][$pbd_loop]["qty"]*$dynamic_subproduct_row["subproduct_qty"]) . " WHERE products_id = '" . tep_get_prid($order->products[$i]["bundle"][$pbd_loop]["id"]) . "'");
							$sql_data_array = array('orders_id' => $this->orders_id, 
				                            		'products_id' => tep_get_prid($order->products[$i]["bundle"][$pbd_loop]["id"]),
				                            		'products_model' => $order->products[$i]["bundle"][$pbd_loop]["model"],
				                            		'products_name' => $order->products[$i]["bundle"][$pbd_loop]["name"],
				                            		'orders_products_store_price' => '0.00',
				                            		'products_price' => '0.01',
				                            		'final_price' => '0.00', 
				                            		'products_tax' => $order->products[$i]["tax"], 
				                            		'products_quantity' => $order->products[$i]["bundle"][$pbd_loop]["qty"]*$dynamic_subproduct_row["subproduct_qty"],
				                            		'products_bundle_id' => tep_db_input($order->products[$i]['id']),
				                            		'products_categories_id' => $order->products[$i]["bundle"][$pbd_loop]["products_categories_id"],
				                            		'op_rebate' => 0,
					                            	'op_rebate_delivered' => 0);
				    		tep_db_perform(TABLE_ORDERS_PRODUCTS, $sql_data_array);
				    		
				    		$sub_orders_products_id = tep_db_insert_id();
				    		
				    		if (isset($order->products[$i]['custom_content']['delivery_mode'])) {
				    			$order->insert_opders_products_extra_info($sub_orders_products_id, 'delivery_mode', $order->products[$i]['custom_content']['delivery_mode']);
				    		}
				    		
							if (count($order->products[$i]['custom_content']) && isset($order->products[$i]['custom_content']['delivery_mode']) && $order->products[$i]['custom_content']['delivery_mode'] == '6') {
								$top_up_info_array = array();
								$top_up_info_select_sql = "	SELECT top_up_info_id, top_up_info_key
															FROM " . TABLE_TOP_UP_INFO . "
															WHERE top_up_info_key IN ('".implode("','", array_keys($order->products[$i]['custom_content']))."')
																AND products_id = '".(int)tep_get_prid($order->products[$i]["bundle"][$pbd_loop]["id"])."'";
								$top_up_info_result_sql = tep_db_query($top_up_info_select_sql);
								while ($top_up_info_row = tep_db_fetch_array($top_up_info_result_sql)) {
									$top_up_info_array[$top_up_info_row['top_up_info_key']] = $top_up_info_row['top_up_info_id'];
								}
								
								if (count($top_up_info_array) && count($order->products[$i]['custom_content'])) {
									foreach ($order->products[$i]['custom_content'] as $extra_key => $extra_val) {
										if ($extra_key != 'delivery_mode' && trim($extra_val) != '') {
											$top_up_info_data_sql = array(	'orders_products_id' => $sub_orders_products_id,
																			'top_up_info_id' => $top_up_info_array[$extra_key],
																			'top_up_value' => (tep_not_null($extra_val) ? tep_db_prepare_input($extra_val) : ''));
											tep_db_perform(TABLE_CUSTOMERS_TOP_UP_INFO, $top_up_info_data_sql);
										}
									}
								}
							}
						}
					}
				}
			 } else if ($product_values["products_bundle"] == 'yes') {
		    	for ($static_loop = 0; $static_loop < count($order->products[$i]["static"]); $static_loop++) {
			    	if ($order->products[$i]["static"][$static_loop]["qty"]) {
			    		$this_package_total_qty = (int)$order->products[$i]['qty'];
			    		
			    		tep_db_query("UPDATE " . TABLE_PRODUCTS . " SET products_ordered = products_ordered + " . sprintf('%d', $order->products[$i]["static"][$static_loop]["qty"]*$order->products[$i]['qty']) . " WHERE products_id = '" . tep_get_prid($order->products[$i]["static"][$static_loop]["id"]) . "'");
			    		$sql_data_array = array('orders_id' => $this->orders_id, 
				                           		'products_id' => tep_get_prid($order->products[$i]["static"][$static_loop]["id"]),
				                           		'products_model' => $order->products[$i]["static"][$static_loop]["model"],
				                           		'products_name' => $order->products[$i]["static"][$static_loop]["name"],
				                           		'orders_products_store_price' => $order->products[$i]['static'][$static_loop]['storage_price']['normal_price'],
				                           		'products_price' => '0.00', 
				                           		'final_price' => '0.00', 
				                           		'products_tax' => $order->products[$i]["tax"], 
				                           		'products_quantity' => $order->products[$i]["static"][$static_loop]["qty"]*$this_package_total_qty,
				                           		'products_bundle_id' => tep_db_input($order->products[$i]['id']),
				                           		'parent_orders_products_id' => (int)$order_products_id,
				                           		'products_categories_id' => $order->products[$i]['static'][$static_loop]['products_categories_id'],
				                           		'op_rebate' => 0,
					                           	'op_rebate_delivered' => 0);
				    	tep_db_perform(TABLE_ORDERS_PRODUCTS, $sql_data_array);
					    
			    		$sub_orders_products_id = tep_db_insert_id();
			    		
			    		if (isset($order->products[$i]['custom_content']['delivery_mode'])) {
			    			$order->insert_opders_products_extra_info($sub_orders_products_id, 'delivery_mode', $order->products[$i]['custom_content']['delivery_mode']);
			    		}
						
						if (count($order->products[$i]['custom_content']) && isset($order->products[$i]['custom_content']['delivery_mode']) && $order->products[$i]['custom_content']['delivery_mode'] == '6') {
							$top_up_info_array = array();
							$top_up_info_select_sql = "	SELECT top_up_info_id, top_up_info_key
														FROM " . TABLE_TOP_UP_INFO . "
														WHERE top_up_info_key IN ('".implode("','", array_keys($order->products[$i]['custom_content']))."')
															AND products_id = '".(int)tep_get_prid($order->products[$i]["static"][$static_loop]["id"])."'";
							$top_up_info_result_sql = tep_db_query($top_up_info_select_sql);
							while ($top_up_info_row = tep_db_fetch_array($top_up_info_result_sql)) {
								$top_up_info_array[$top_up_info_row['top_up_info_key']] = $top_up_info_row['top_up_info_id'];
							}
							
							if (count($top_up_info_array) && count($order->products[$i]['custom_content'])) {
								foreach ($order->products[$i]['custom_content'] as $extra_key => $extra_val) {
									if ($extra_key != 'delivery_mode' && trim($extra_val) != '') {
										$top_up_info_data_sql = array(	'orders_products_id' => $sub_orders_products_id,
																		'top_up_info_id' => $top_up_info_array[$extra_key],
																		'top_up_value' => (tep_not_null($extra_val) ? tep_db_prepare_input($extra_val) : ''));
										tep_db_perform(TABLE_CUSTOMERS_TOP_UP_INFO, $top_up_info_data_sql);
									}
								}
							}
						}
			    	}
			    }
		    } else {
				if (isset($order->products[$i]['custom_content']['delivery_mode']) && $order->products[$i]['custom_content']['delivery_mode'] == '6') {
					$order->insert_opders_products_extra_info($order_products_id, 'delivery_mode', $order->products[$i]['custom_content']['delivery_mode']);
				}
				
				$top_up_info_array = array();
				$top_up_info_select_sql = "	SELECT top_up_info_id, top_up_info_key
											FROM " . TABLE_TOP_UP_INFO . "
											WHERE top_up_info_key IN ('".implode("','", array_keys($order->products[$i]['custom_content']))."')
												AND products_id = '".(int)$order->products[$i]['id']."'";
				$top_up_info_result_sql = tep_db_query($top_up_info_select_sql);
				while ($top_up_info_row = tep_db_fetch_array($top_up_info_result_sql)) {
					$top_up_info_array[$top_up_info_row['top_up_info_key']] = $top_up_info_row['top_up_info_id'];
				}
				
				if (count($top_up_info_array) && count($order->products[$i]['custom_content'])) {
					foreach ($order->products[$i]['custom_content'] as $extra_key => $extra_val) {
						if ($extra_key != 'delivery_mode' && trim($extra_val) != '') {
							$top_up_info_data_sql = array(	'orders_products_id' => $order_products_id,
															'top_up_info_id' => $top_up_info_array[$extra_key],
															'top_up_value' => (tep_not_null($extra_val) ? tep_db_prepare_input($extra_val) : ''));
							tep_db_perform(TABLE_CUSTOMERS_TOP_UP_INFO, $top_up_info_data_sql);
						}
					}
				}
			}
		} else {
			if (is_array($order->products[$i]['custom_content']) && sizeof($order->products[$i]['custom_content']) > 0) {
				foreach ($order->products[$i]['custom_content'] as $num => $extra_info_array) {
					$this_package_total_qty = (int)$order->products[$i]['custom_products_type_id'] == 2 ? (int)$order->products[$i]['qty'] : (int)$extra_info_array['qty'];
					$currencies->display_price($order->products[$i]['id'], $order->products[$i]['normal_price'], $order->products[$i]['tax'], $this_package_total_qty, false, '', $payment_methods_id_row['payment_methods_id']); // Need to use Qty per package instead of total qty of this product
					
					$cust_group_extra_rebate = 0;
					if ($payment_methods_id_row['payment_methods_id'] > 0) {
						$cust_group_extra_rebate = $currencies->rebate_point_extra;
						if ($cust_group_extra_rebate > 0) {
							$surcharge_amt = 0;
							for ($count_order_totals=0; $count_order_totals<sizeof($order_totals); $count_order_totals++) {
								if ($order_totals[$count_order_totals]['code'] == 'ot_surcharge') {
									$surcharge_amt = $order_totals[$count_order_totals]['value'];
								}
							}
							$cust_group_extra_rebate = floor($cust_group_extra_rebate * ( ($order->info['total']-$surcharge_amt) / $order->info['subtotal']));
						}
					}
					
					$sql_data_array = array('orders_id' => $this->orders_id,
				                            'products_id' => tep_get_prid($order->products[$i]['id']),
				                            'products_model' => $order->products[$i]['model'],
				                            'products_name' => $order->products[$i]['name'],
				                            'orders_products_store_price' => $order->products[$i]['storage_price']['normal_price'],
				                            'products_price' => $order->products[$i]['storage_price']['price'],
				                            'final_price' => $order->products[$i]['storage_price']['final_price'],
				                            'products_tax' => $order->products[$i]['tax'],
				                            'products_quantity' => $this_package_total_qty,
				                            'products_pre_order' => $order->products[$i]['pre_order'],
				                            'custom_products_type_id' => $order->products[$i]['custom_products_type_id'],
				                            'products_categories_id' => $order->products[$i]['products_categories_id'],
				                            'op_rebate' => $currencies->rebate_point + $cust_group_extra_rebate,
				                            'op_rebate_delivered' => 0);
					tep_db_perform(TABLE_ORDERS_PRODUCTS, $sql_data_array);
					$order_products_id = tep_db_insert_id();
	    			
					if ($cust_group_extra_rebate > 0) {
						$order->insert_opders_products_extra_info($order_products_id, 'OP_EXTRA', $cust_group_extra_rebate);
						$order->insert_opders_products_extra_info($order_products_id, 'OP_FORMULA', $currencies->rebate_point_formula . '<br><br>Extra OP: ' . $cust_group_extra_rebate);
					} else {
						$order->insert_opders_products_extra_info($order_products_id, 'OP_FORMULA', $currencies->rebate_point_formula);
					}
					
					if (isset($order->products[$i]['custom_content']['delivery_mode']) && $order->products[$i]['custom_content']['delivery_mode'] > 0) {
						$order->insert_opders_products_extra_info($order_products_id, 'delivery_mode', $order->products[$i]['custom_content']['delivery_mode']);
					}
					
					$product_query_raw = "	SELECT p.products_bundle, p.products_bundle_dynamic 
			                       			FROM " . TABLE_PRODUCTS . " AS p
			                      			LEFT JOIN " . TABLE_PRODUCTS_ATTRIBUTES . " AS pa
			                    	   			ON p.products_id=pa.products_id
			                	      		LEFT JOIN " . TABLE_PRODUCTS_ATTRIBUTES_DOWNLOAD . " AS pad
			            	           			ON pa.products_attributes_id=pad.products_attributes_id
			        	              		WHERE p.products_id = '" . tep_get_prid($order->products[$i]['id']) . "'";
					$product_query = tep_db_query($product_query_raw);
					$product_values = tep_db_fetch_array($product_query);
					
					if ($product_values["products_bundle_dynamic"] == 'yes') {
						for ($pbd_loop=0; $pbd_loop<count($order->products[$i]["bundle"]); $pbd_loop++) {
							if ($order->products[$i]["bundle"][$pbd_loop]["qty"]) {
								$dynamic_subproduct_select_sql = "	SELECT subproduct_qty 
																	FROM ". TABLE_PRODUCTS_BUNDLES . "
																	WHERE bundle_id = " . tep_get_prid($order->products[$i]["id"]) . "
																		AND subproduct_id = " . tep_get_prid($order->products[$i]["bundle"][$pbd_loop]["id"]) ;
								$dynamic_subproduct_result_sql = tep_db_query($dynamic_subproduct_select_sql);
								if ($dynamic_subproduct_row = tep_db_fetch_array($dynamic_subproduct_result_sql)) {
									tep_db_query("UPDATE " . TABLE_PRODUCTS . " SET products_ordered = products_ordered + " . sprintf('%d', $order->products[$i]["bundle"][$pbd_loop]["qty"]*$dynamic_subproduct_row["subproduct_qty"]) . " WHERE products_id = '" . tep_get_prid($order->products[$i]["bundle"][$pbd_loop]["id"]) . "'");
									$sql_data_array = array('orders_id' => $this->orders_id, 
						                            		'products_id' => tep_get_prid($order->products[$i]["bundle"][$pbd_loop]["id"]),
						                            		'products_model' => $order->products[$i]["bundle"][$pbd_loop]["model"],
						                            		'products_name' => $order->products[$i]["bundle"][$pbd_loop]["name"],
						                            		'orders_products_store_price' => '0.00',
						                            		'products_price' => '0.01',
						                            		'final_price' => '0.00', 
						                            		'products_tax' => $order->products[$i]["tax"], 
						                            		'products_quantity' => $order->products[$i]["bundle"][$pbd_loop]["qty"]*$dynamic_subproduct_row["subproduct_qty"],
						                            		'products_bundle_id' => tep_db_input($order->products[$i]['id']),
						                            		'products_categories_id' => $order->products[$i]["bundle"][$pbd_loop]["products_categories_id"],
						                            		'op_rebate' => 0,
							                            	'op_rebate_delivered' => 0);
						    		tep_db_perform(TABLE_ORDERS_PRODUCTS, $sql_data_array);
								}
							}
						}
				    } else if ($product_values["products_bundle"] == 'yes') {
				    	for ($static_loop = 0; $static_loop < count($order->products[$i]["static"]); $static_loop++) {
					    	if ($order->products[$i]["static"][$static_loop]["qty"]) {
					    		tep_db_query("UPDATE " . TABLE_PRODUCTS . " SET products_ordered = products_ordered + " . sprintf('%d', $order->products[$i]["static"][$static_loop]["qty"]*$order->products[$i]['qty']) . " WHERE products_id = '" . tep_get_prid($order->products[$i]["static"][$static_loop]["id"]) . "'");
					    		$sql_data_array = array('orders_id' => $this->orders_id, 
						                           		'products_id' => tep_get_prid($order->products[$i]["static"][$static_loop]["id"]),
						                           		'products_model' => $order->products[$i]["static"][$static_loop]["model"],
						                           		'products_name' => $order->products[$i]["static"][$static_loop]["name"],
						                           		'orders_products_store_price' => $order->products[$i]['static'][$static_loop]['storage_price']['normal_price'],
						                           		'products_price' => '0.00', 
						                           		'final_price' => '0.00', 
						                           		'products_tax' => $order->products[$i]["tax"], 
						                           		'products_quantity' => $order->products[$i]["static"][$static_loop]["qty"]*$this_package_total_qty,
						                           		'products_bundle_id' => tep_db_input($order->products[$i]['id']),
						                           		'parent_orders_products_id' => (int)$order_products_id,
						                           		'products_categories_id' => $order->products[$i]['static'][$static_loop]['products_categories_id'],
						                           		'op_rebate' => 0,
							                           	'op_rebate_delivered' => 0);
						    	tep_db_perform(TABLE_ORDERS_PRODUCTS, $sql_data_array);	
					    	}
					    }
				    }
					
					if (is_array($extra_info_array)) {
						foreach ($extra_info_array as $extra_key => $extra_val) {
							if ($extra_key != 'qty' && !is_array($extra_val) && trim($extra_val) != '') {
								$order->insert_opders_products_extra_info($order_products_id, $extra_key, $extra_val);
							}
						}
					}
				}
			} else {
				$sql_data_array = array('orders_id' => $this->orders_id,
			                            'products_id' => tep_get_prid($order->products[$i]['id']),
			                            'products_model' => $order->products[$i]['model'],
			                            'products_name' => $order->products[$i]['name'],
			                            'orders_products_store_price' => $order->products[$i]['storage_price']['normal_price'],
			                            'products_price' => $order->products[$i]['storage_price']['price'],
			                            'final_price' => $order->products[$i]['storage_price']['final_price'],
			                            'products_tax' => $order->products[$i]['tax'],
			                            'products_quantity' => $order->products[$i]['qty'],
			                            'products_pre_order' => $order->products[$i]['pre_order'],
			                            'custom_products_type_id' => $order->products[$i]['custom_products_type_id'],
			                            'products_categories_id' => $order->products[$i]['products_categories_id'],
			                            'op_rebate' => $currencies->rebate_point + $cust_group_extra_rebate,
				                        'op_rebate_delivered' => 0);
			    tep_db_perform(TABLE_ORDERS_PRODUCTS, $sql_data_array);
			    $order_products_id = tep_db_insert_id();
			    
				if ($cust_group_extra_rebate > 0) {
					$order->insert_opders_products_extra_info($order_products_id, 'OP_EXTRA', $cust_group_extra_rebate);
					$currencies->rebate_point_formula = $currencies->rebate_point_formula . '<br><br>Extra OP: ' . $cust_group_extra_rebate;
				}
			    
			    $order->insert_opders_products_extra_info($order_products_id, 'OP_FORMULA', $currencies->rebate_point_formula);
			    
				if (isset($order->products[$i]['custom_content']['delivery_mode']) && $order->products[$i]['custom_content']['delivery_mode'] > 0) {
					$order->insert_opders_products_extra_info($order_products_id, 'delivery_mode', $order->products[$i]['custom_content']['delivery_mode']);
				}
			}
		}
	} else {
		$sql_data_array = array('orders_id' => $this->orders_id,
	                            'products_id' => tep_get_prid($order->products[$i]['id']),
	                            'products_model' => $order->products[$i]['model'],
	                            'products_name' => $order->products[$i]['name'],
	                            'orders_products_store_price' => $order->products[$i]['storage_price']['normal_price'],
	                            'products_price' => $order->products[$i]['storage_price']['price'],
	                            'final_price' => $order->products[$i]['storage_price']['final_price'],
	                            'products_tax' => $order->products[$i]['tax'],
	                            'products_quantity' => $order->products[$i]['qty'],
	                            'products_pre_order' => $order->products[$i]['pre_order'],
	                            'custom_products_type_id' => $order->products[$i]['custom_products_type_id'],
	                            'products_categories_id' => $order->products[$i]['products_categories_id'],
	                            'op_rebate' => $currencies->rebate_point + $cust_group_extra_rebate,
		                        'op_rebate_delivered' => 0);
	    tep_db_perform(TABLE_ORDERS_PRODUCTS, $sql_data_array);
	    $order_products_id = tep_db_insert_id();
	    
		if ($cust_group_extra_rebate > 0) {
			$order->insert_opders_products_extra_info($order_products_id, 'OP_EXTRA', $cust_group_extra_rebate);
			$currencies->rebate_point_formula = $currencies->rebate_point_formula . '<br><br>Extra OP: ' . $cust_group_extra_rebate;
		}
		
	    $order->insert_opders_products_extra_info($order_products_id, 'OP_FORMULA', $currencies->rebate_point_formula);
		if (isset($order->products[$i]['custom_content']['delivery_mode']) && $order->products[$i]['custom_content']['delivery_mode'] > 0) {
			$order->insert_opders_products_extra_info($order_products_id, 'delivery_mode', $order->products[$i]['custom_content']['delivery_mode']);
		}
		
		/*-- HLA --*/
		if ((int)$order->products[$i]['custom_products_type_id'] == 4 && tep_not_null($product_instance_id)) {
			$products_hla_sql = "	SELECT products_hla_id, seller_id, products_account_id, products_original_price, products_base_currency 
									FROM " . TABLE_PRODUCTS_HLA . " 
									WHERE products_hla_id = '" . tep_db_input($product_instance_id) . "'";
			$products_hla_result = tep_db_query($products_hla_sql);
			if ($products_hla_row = tep_db_fetch_array($products_hla_result)) {
				$cron_hla_select_sql = "SELECT products_hla_id FROM " . TABLE_CRON_HLA . " WHERE products_hla_id = '" . $product_instance_id . "'";
				$cron_hla_result_sql = tep_db_query($cron_hla_select_sql);
				
				if (tep_db_num_rows($cron_hla_result_sql) == 0) { // no reservation request yet
					$reserve_insert_sql = "INSERT INTO " . TABLE_CRON_HLA . " (products_hla_id, created_date) VALUES ('" . $product_instance_id . "', NOW())";
					tep_db_query($reserve_insert_sql);
				}
				
				$order->insert_opders_products_extra_info($order_products_id, 'products_hla', tep_array_serialize($products_hla_row));
			}
			
			$hla_char_sql = "	SELECT phc.products_hla_characters_id, phc.products_ref_id, phd.products_hla_characters_name 
								FROM " . TABLE_PRODUCTS_HLA_CHARACTERS . " AS phc 
								LEFT JOIN " . TABLE_PRODUCTS_HLA_DESCRIPTION . " AS phd 
									ON phd.products_hla_characters_id = phc.products_hla_characters_id 
										AND phd.language_id = '" . (int)$default_languages_id . "' 
								WHERE phc.products_hla_id = '" . tep_db_input($product_instance_id) . "'";
			$hla_char_result = tep_db_query($hla_char_sql);
			while ($hla_char_row = tep_db_fetch_array($hla_char_result)) {
				$hla_char_data_array = array();
				$hla_char_attr_array = array();
				$hla_char_attr = array();
				
				$hla_char_attr_array['products_ref_id'] = $hla_char_row['products_ref_id'];
				
				$hla_char_attr_sql = "	SELECT phd.products_hla_characters_name, pha.language_id, pha.products_hla_attributes_type, pha.products_hla_value 
										FROM " . TABLE_PRODUCTS_HLA_DESCRIPTION . " AS phd 
										INNER JOIN " . TABLE_PRODUCTS_HLA_ATTRIBUTES . " AS pha 
											ON pha.products_hla_characters_id = phd.products_hla_characters_id 
												AND pha.language_id = phd.language_id 
										WHERE phd.products_hla_characters_id = '" . $hla_char_row['products_hla_characters_id'] . "'";
				$hla_char_attr_result = tep_db_query($hla_char_attr_sql);
				while ($hla_char_attr_row = tep_db_fetch_array($hla_char_attr_result)) {
					$hla_char_attr_array['language_id'][$hla_char_attr_row['language_id']][$hla_char_attr_row['products_hla_attributes_type']] = $hla_char_attr_row['products_hla_value'];
				}
				
				$hla_char_data_array = array (	'orders_products_id' => $order_products_id, 
												'products_hla_characters_id' => $hla_char_row['products_hla_characters_id'], 
												'products_hla_characters_name' => $hla_char_row['products_hla_characters_name'], 
												'orders_products_item_info' => tep_array_serialize($hla_char_attr_array));
				tep_db_perform(TABLE_ORDERS_PRODUCTS_ITEM, $hla_char_data_array);
			}
		}
	}
	
	// update Products Checkout Quantity Control memcache [START]
	$memcache_obj->delete(TABLE_PRODUCTS_CHECKOUT_SETTING . '/exceed_limit_info/array/customers_id/' . $_SESSION['customer_id'] . '/products_id/' . $order->products[$i]['id'], 0);
	// update Products Checkout Quantity Control memcache [END]
	
	/****************************************
		Custom Product
	****************************************/
	if((int)$order->products[$i]['custom_products_type_id'] == 1 && sizeof($order->products[$i]['custom_content']) > 0) {
		$custom_product_info = 'Order Number: ' . (int)$this->orders_id . "##1##" . "\n"; // Will get setting from admin page, in future
		$custom_product_info .= 'Price: ' . $currencies->format($order->products[$i]['final_price'] * $order->products[$i]['qty'], true, $order->info['currency'], $order->info['currency_value']) . "\n";
		
		$custom_content_array = $order->products[$i]['custom_content'];
		
		if (is_array($custom_content_array['db_info']) && count($custom_content_array['db_info'])) {
			foreach ($custom_content_array['db_info'] as $info_res) {
				if ($info_res['class'] == 'GENERAL' || !tep_not_null($info_res['class'])) {
					$custom_product_info .= $info_res['label'] . ': ' . $info_res['value'] . (($info_res['show_supplier'] == "1") ? "##1##" : "") . "\n";
				}
			}
		}
		
		if (tep_not_null($custom_content_array['calculated']['eta_text'])) {
			$custom_product_info .= 'ETA: ' . $custom_content_array['calculated']['eta_text'] . (($custom_content_array['calculated']['show_supplier'] == 1) ? "##1##" : "") . "\n";
		}
		
   		$orders_custom_products_data_array = array(	'products_id' => tep_get_prid($order->products[$i]['id']),
													'orders_products_id' => $order_products_id, 
													'orders_custom_products_key' => $order->products[$i]['custom_products_type_name'].'_info',
													'orders_custom_products_value' => $custom_product_info,
													'orders_custom_products_number' => $custom_product_count
													);
		tep_db_perform(TABLE_ORDERS_CUSTOM_PRODUCTS, $orders_custom_products_data_array);
		
		if (is_array($custom_content_array['db_info']) && count($custom_content_array['db_info'])) {
			foreach ($custom_content_array['db_info'] as $info_res) {
				switch($info_res['class']) {
					case "ACCOUNT USERNAME":
						$account_username_data_array = array(	'products_id' => tep_get_prid($order->products[$i]['id']),
																'orders_products_id' => $order_products_id, 
																'orders_custom_products_key' => 'power_leveling_account_account_username',
																'orders_custom_products_value' => tep_db_prepare_input($info_res['value']),
																'orders_custom_products_number' => (int)$custom_product_count
																);
						tep_db_perform(TABLE_ORDERS_CUSTOM_PRODUCTS, $account_username_data_array);
						
						break;
					
					case "ACCOUNT PASSWORD":
						$account_password_data_array = array(	'products_id' => tep_get_prid($order->products[$i]['id']),
																'orders_products_id' => $order_products_id, 
																'orders_custom_products_key' => 'power_leveling_account_account_password',
																'orders_custom_products_value' => tep_db_prepare_input($info_res['value']),
																'orders_custom_products_number' => (int)$custom_product_count
																);
						tep_db_perform(TABLE_ORDERS_CUSTOM_PRODUCTS, $account_password_data_array);
						
						break;
						
					case "CHARACTER NAME":
						$char_name_data_array = array(	'products_id' => tep_get_prid($order->products[$i]['id']),
														'orders_products_id' => $order_products_id, 
														'orders_custom_products_key' => 'power_leveling_account_character_name',
														'orders_custom_products_value' => tep_db_prepare_input($info_res['value']),
														'orders_custom_products_number' => (int)$custom_product_count
														);
						tep_db_perform(TABLE_ORDERS_CUSTOM_PRODUCTS, $char_name_data_array);
					
						break;
						
					case "REALM":
						$realm_data_array = array(	'products_id' => tep_get_prid($order->products[$i]['id']),
													'orders_products_id' => $order_products_id, 
													'orders_custom_products_key' => 'power_leveling_account_realm',
													'orders_custom_products_value' => $info_res['value'],
													'orders_custom_products_number' => (int)$custom_product_count
													);
						tep_db_perform(TABLE_ORDERS_CUSTOM_PRODUCTS, $realm_data_array);
						
						break;
						
					case "FACTION":
						$faction_data_array = array(	'products_id' => tep_get_prid($order->products[$i]['id']),
														'orders_products_id' => $order_products_id, 
														'orders_custom_products_key' => 'power_leveling_account_faction',
														'orders_custom_products_value' => $info_res['value'],
														'orders_custom_products_number' => (int)$custom_product_count
														);
						tep_db_perform(TABLE_ORDERS_CUSTOM_PRODUCTS, $faction_data_array);
						
						break;
				}
			}
		}
		
		$custom_product_count++;
		
		if (is_array($custom_content_array['db_bracket_info']) && count($custom_content_array['db_bracket_info'])) {
			foreach ($custom_content_array['db_bracket_info'] as $info_res1) {
				$custom_product_infos .= $info_res1['value'] . "\n";
			}
		}
			
		$orders_custom_products_data_array = array(	'products_id' => tep_get_prid($order->products[$i]['id']),
													'orders_products_id' => $order_products_id, 
													'orders_custom_products_key' => 'power_leveling_bracket_info',
													'orders_custom_products_value' => $custom_product_infos
													);
		tep_db_perform(TABLE_ORDERS_CUSTOM_PRODUCTS, $orders_custom_products_data_array);
		
	} else if ((int)$order->products[$i]['custom_products_type_id'] == 3) {
		$store_credit_currency_array = array(	'products_id' => tep_get_prid($order->products[$i]['id']),
												'orders_products_id' => $order_products_id, 
												'orders_custom_products_key' => 'store_credit_currency',
												'orders_custom_products_value' => tep_get_customer_store_credit_currency($customer_id),
												'orders_custom_products_number' => 0
												);
		tep_db_perform(TABLE_ORDERS_CUSTOM_PRODUCTS, $store_credit_currency_array);
		
		$sc_promotion_percentage = store_credit::get_sc_promotion_percentage();
		if ($sc_promotion_percentage > 0) {
            $store_credit_currency_array = array(	'products_id' => tep_get_prid($order->products[$i]['id']),
                                                    'orders_products_id' => $order_products_id, 
                                                    'orders_custom_products_key' => 'store_credit_promotion_percentage',
                                                    'orders_custom_products_value' => $sc_promotion_percentage,
                                                    'orders_custom_products_number' => 0
                                                    );
            tep_db_perform(TABLE_ORDERS_CUSTOM_PRODUCTS, $store_credit_currency_array);
		}
	}
	

//------insert customer choosen option to order--------
    $attributes_exist = '0';
    if (isset($order->products[$i]['attributes'])) {
      	$attributes_exist = '1';
      	for ($j=0, $n2=sizeof($order->products[$i]['attributes']); $j<$n2; $j++) {
        	if (DOWNLOAD_ENABLED == 'true') {
          		$attributes_query = "	select popt.products_options_name, poval.products_options_values_name, pa.options_values_price, pa.price_prefix, pad.products_attributes_maxdays, pad.products_attributes_maxcount , pad.products_attributes_filename
                               			from " . TABLE_PRODUCTS_OPTIONS . " popt, " . TABLE_PRODUCTS_OPTIONS_VALUES . " poval, " . TABLE_PRODUCTS_ATTRIBUTES . " pa
                               			left join " . TABLE_PRODUCTS_ATTRIBUTES_DOWNLOAD . " pad
                                			on pa.products_attributes_id=pad.products_attributes_id
                               			where pa.products_id = '" . $order->products[$i]['id'] . "'
                                			and pa.options_id = '" . $order->products[$i]['attributes'][$j]['option_id'] . "'
                                			and pa.options_id = popt.products_options_id
			                                and pa.options_values_id = '" . $order->products[$i]['attributes'][$j]['value_id'] . "'
			                                and pa.options_values_id = poval.products_options_values_id
			                                and popt.language_id = '" . $languages_id . "'
			                                and poval.language_id = '" . $languages_id . "'";
          		$attributes = tep_db_query($attributes_query);
        	} else {
          		$attributes = tep_db_query("select popt.products_options_name, poval.products_options_values_name, pa.options_values_price, pa.price_prefix from " . TABLE_PRODUCTS_OPTIONS . " popt, " . TABLE_PRODUCTS_OPTIONS_VALUES . " poval, " . TABLE_PRODUCTS_ATTRIBUTES . " pa where pa.products_id = '" . $order->products[$i]['id'] . "' and pa.options_id = '" . $order->products[$i]['attributes'][$j]['option_id'] . "' and pa.options_id = popt.products_options_id and pa.options_values_id = '" . $order->products[$i]['attributes'][$j]['value_id'] . "' and pa.options_values_id = poval.products_options_values_id and popt.language_id = '" . $languages_id . "' and poval.language_id = '" . $languages_id . "'");
        	}
        	$attributes_values = tep_db_fetch_array($attributes);
			
        	$sql_data_array = array('orders_id' => (int)$this->orders_id,
                                	'orders_products_id' => $order_products_id,
                                	'products_options_id' => $order->products[$i]['attributes'][$j]['option_id'],
                                	'products_options' => $attributes_values['products_options_name'],
                                	'products_options_values_id' => $order->products[$i]['attributes'][$j]['value_id'],
                                	'products_options_values' => $attributes_values['products_options_values_name'],
                                	'options_values_price' => $attributes_values['options_values_price'],
                                	'price_prefix' => $attributes_values['price_prefix']);
			
        	tep_db_perform(TABLE_ORDERS_PRODUCTS_ATTRIBUTES, $sql_data_array);
      	}
	}
 	
	////////////////////////////////////////////////////////////////////////////////dynamic package
	//------insert customer choosen option eof ----
	$total_weight += ($order->products[$i]['qty'] * $order->products[$i]['weight']);
	$total_tax += tep_calculate_tax($total_products_price, $products_tax) * $order->products[$i]['qty'];
	$total_cost += $total_products_price;
	
	//CGDiscountSpecials start
	$products_ordered .= $order->products[$i]['qty'] . ' x ' . $order->products[$i]['name'] . ' (' . $order->products[$i]['model'] . ') = ' . $currencies->display_price_nodiscount($order->products[$i]['id'], $order->products[$i]['final_price'], $order->products[$i]['tax'], $order->products[$i]['qty']) . $products_ordered_attributes . "\n";
	//CGDiscountSpecials end
	
	//CustomerDiscount end
}  // closing mark should put under here for retrieving the info... especially the orders->

$order_total_modules->apply_credit($this->orders_id);	// Applied to the whole order

//$customer_complete_phone_info = tep_format_telephone($customer_id);
//$complete_telephone_number = $customer_complete_phone_info['country_international_dialing_code'] . $customer_complete_phone_info['telephone_number'];

//if (tep_info_been_verify($customer_id, $complete_telephone_number, 'telephone') == true) {
//	tep_reset_try_turns($customer_id, $complete_telephone_number, 'telephone');
//}

if (!tep_session_is_registered('order_completed')) {
	tep_session_register('order_completed');
}

//////////////////////////////////////////////////////////////////////////////////////
// store the session info for notification update - gsb
$sql_data_array = array('sendto' => $sendto,
                       	'billto' => $billto,
                        'firstname' => $order->customer['firstname'],
                        'lastname' =>  $order->customer['lastname'],
                        //'shipping' => serialize($shipping),
                        //'payment' => $payment,
                        'language' => $language,
                        'currency' => $currency,
                        'content_type' => $order->content_type,
                        'txn_signature' => $this->setTransactionID());

if ($session_exists) {
	tep_db_perform(TABLE_ORDERS_SESSION_INFO, $sql_data_array, 'update', "orders_id = '" . (int)$this->orders_id . "'");
    $paypal_order->txn_signature = $this->digest;
} else {
    $sql_data_array['orders_id'] = (int)$this->orders_id;
    tep_db_perform(TABLE_ORDERS_SESSION_INFO, $sql_data_array);
    
    if (tep_session_is_registered('paypal_order'))	tep_session_unregister('paypal_order');
    $paypal_order = new paypal_order($this->orders_id,$this->digest);
    tep_session_register('paypal_order');
}

//require(DIR_WS_INCLUDES . 'affiliate_checkout_process1.php');
require(DIR_WS_INCLUDES . 'affiliate_checkout_process.php');
require(DIR_WS_INCLUDES . 'modules/payment/paypal/catalog/checkout_splash.inc.php');
?>