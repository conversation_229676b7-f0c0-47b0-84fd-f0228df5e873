<?
/*
  	$Id: checkout_update.inc.php,v 1.23 2010/06/21 10:13:50 boonhock Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	DevosC, Developing open source Code
  	http://www.devosc.com
	
  	Copyright (c) 2003 osCommerce
  	Copyright (c) 2004 DevosC.com
	
  	Released under the GNU General Public License
*/

// initialized for the email confirmation
$products_ordered = '';
$subtotal = 0;
$total_tax = 0;

for ($i=0, $n=sizeof($order->products); $i<$n; $i++)
{
	// Update products_ordered (for bestsellers list)
    tep_db_query("update " . TABLE_PRODUCTS . " set products_ordered = products_ordered + " . sprintf('%d', $order->products[$i]['qty']) . " where products_id = '" . tep_get_prid($order->products[$i]['id']) . "'");
	
	//------insert customer choosen option to order--------
    $attributes_exist = '0';
    $products_ordered_attributes = '';
    
    if (isset($order->products[$i]['attributes'])) {
      	$attributes_exist = '1';
      	for ($j=0, $n2=sizeof($order->products[$i]['attributes']); $j<$n2; $j++) {
        	if (DOWNLOAD_ENABLED == 'true') {
          		$attributes_query = "	select popt.products_options_name, poval.products_options_values_name, pa.options_values_price, pa.price_prefix, pad.products_attributes_maxdays, pad.products_attributes_maxcount , pad.products_attributes_filename
                               			from " . TABLE_PRODUCTS_OPTIONS . " popt, " . TABLE_PRODUCTS_OPTIONS_VALUES . " poval, " . TABLE_PRODUCTS_ATTRIBUTES . " pa
                               			left join " . TABLE_PRODUCTS_ATTRIBUTES_DOWNLOAD . " pad
                                			on pa.products_attributes_id=pad.products_attributes_id
                               			where pa.products_id = '" . $order->products[$i]['id'] . "'
			                                and pa.options_id = '" . $order->products[$i]['attributes'][$j]['option_id'] . "'
			                                and pa.options_id = popt.products_options_id
			                                and pa.options_values_id = '" . $order->products[$i]['attributes'][$j]['value_id'] . "'
			                                and pa.options_values_id = poval.products_options_values_id
			                                and popt.language_id = '" . $languages_id . "'
			                                and poval.language_id = '" . $languages_id . "'";
          		$attributes = tep_db_query($attributes_query);
        	} else {
          		$attributes = tep_db_query("select popt.products_options_name, poval.products_options_values_name, pa.options_values_price, pa.price_prefix from " . TABLE_PRODUCTS_OPTIONS . " popt, " . TABLE_PRODUCTS_OPTIONS_VALUES . " poval, " . TABLE_PRODUCTS_ATTRIBUTES . " pa where pa.products_id = '" . $order->products[$i]['id'] . "' and pa.options_id = '" . $order->products[$i]['attributes'][$j]['option_id'] . "' and pa.options_id = popt.products_options_id and pa.options_values_id = '" . $order->products[$i]['attributes'][$j]['value_id'] . "' and pa.options_values_id = poval.products_options_values_id and popt.language_id = '" . $languages_id . "' and poval.language_id = '" . $languages_id . "'");
        	}
        	$attributes_values = tep_db_fetch_array($attributes);
        	if ((DOWNLOAD_ENABLED == 'true') && isset($attributes_values['products_attributes_filename']) && tep_not_null($attributes_values['products_attributes_filename']) && $ipn->key['payment_status'] == 'Completed' ) {
          		$sql_data_array = array('orders_id' => $orders_id,
                                  		'orders_products_id' => $order->products[$i]['orders_products_id'],
                                  		'orders_products_filename' => $attributes_values['products_attributes_filename'],
		                                'download_maxdays' => $attributes_values['products_attributes_maxdays'],
		                                'download_count' => $attributes_values['products_attributes_maxcount']);
          		tep_db_perform(TABLE_ORDERS_PRODUCTS_DOWNLOAD, $sql_data_array);
        	}
        	$products_ordered_attributes .= "\n\t" . $attributes_values['products_options_name'] . ' ' . $attributes_values['products_options_values_name'];
      	}
	}
	//------insert customer choosen option eof ----
    $total_weight += ($order->products[$i]['qty'] * $order->products[$i]['weight']);
    $total_tax += tep_calculate_tax($total_products_price, $products_tax) * $order->products[$i]['qty'];
    $total_cost += $total_products_price;
	
	
	$cat_path = tep_output_generated_category_path($order->products[$i]['id'], 'product');
  	$product_email_display_str = $cat_path . (tep_not_null($cat_path) ? ' > ' : '') . $order->products[$i]['name'] . (tep_not_null($order->products[$i]['model']) ? ' (' . $order->products[$i]['model'] . ')' : '');
    
    $products_ordered .= $order->products[$i]['qty'] . ' x ' . $product_email_display_str . ' = ' . $currencies->format($order->products[$i]['final_price'] * $order->products[$i]['qty'], true, $order->info['currency'], $order->info['currency_value']) . $products_ordered_attributes . "\n";
}

$paypal_order_status_select_sql = " SELECT orders_status 
									FROM " . TABLE_ORDERS . " 
									WHERE orders_id = '" . (int)$orders_id . "'";
$paypal_order_status_result_sql = tep_db_query($paypal_order_status_select_sql);
if ($paypal_order_status_row = tep_db_fetch_array($paypal_order_status_result_sql)) {
	if ($paypal_order_status_row['orders_status'] == MODULE_PAYMENT_PAYPAL_PROCESSING_STATUS_ID) {
		// update the order's status only if current order status equal to MODULE_PAYMENT_PAYPAL_PROCESSING_STATUS_ID 
		// prevent the order status been updated wrongly in the case admin staff update before IPN comes in
		//tep_db_query("UPDATE " . TABLE_ORDERS . " SET orders_status = '" . MODULE_PAYMENT_PAYPAL_ORDER_STATUS_ID . "', last_modified = now() WHERE orders_id = '" . (int)$orders_id . "'");
		
		require_once(DIR_WS_CLASSES . 'log.php');
		$log_object = new log_files('system');
		
		$formatted_order_id = (int)$orders_id;
		$cur_order = new order($formatted_order_id);
		
		$customer_notification = (SEND_EMAILS == 'true') ? '1' : '0';
		
		$orders_status_history_perform_array = array(	'action' => 'insert',
														'data' => array('orders_id' => $orders_id,
																		'orders_status_id' => MODULE_PAYMENT_PAYPAL_ORDER_STATUS_ID,
																		'date_added' => 'now()',
																		'customer_notified' => $customer_notification
																		)
													);
		
		$order->update_order_status(MODULE_PAYMENT_PAYPAL_ORDER_STATUS_ID, $orders_status_history_perform_array, true);
		
		unset($orders_status_history_perform_array);
		
		// lets start with the email confirmation
		include(DIR_WS_LANGUAGES . $language . '/' . FILENAME_CHECKOUT_PROCESS);
		
		$customer_profile_select_sql = "SELECT customers_gender, customers_firstname, customers_lastname FROM " . TABLE_CUSTOMERS . " WHERE customers_id = '" . $order->customer["id"] . "'";
		$customer_profile_result_sql = tep_db_query($customer_profile_select_sql);
		if ($customer_profile_row = tep_db_fetch_array($customer_profile_result_sql)) {
			$email_firstname = $customer_profile_row["customers_firstname"];
			$email_lastname = $customer_profile_row["customers_lastname"];
		} else {
			$email_firstname = $order->customer['name'];
			$email_lastname = $order->customer['name'];
		}
		
		$email_greeting = tep_get_email_greeting($email_firstname, $email_lastname, $customer_profile_row["customers_gender"]);
		
		$email_order .= $email_greeting . sprintf(EMAIL_TEXT_ORDER_THANK_YOU, $currencies->format($order->info['total_value'], true, $order->info['currency'])) . "\n\n";
		
		if (is_object($$payment)) {
			$email_order .= EMAIL_TEXT_PAYMENT_METHOD . "\n" .
		                    EMAIL_SEPARATOR . "\n";
		    $payment_class = $$payment;
		    $email_order .= strip_tags($payment_class->title) . "\n";
		    if ($payment_class->email_footer) {
		      	$email_order .= $payment_class->email_footer . "\n\n";
		    } else { $email_order .= "\n"; }
		}
		
		if ($order->content_type != 'virtual') {
			if (isset($sendto) && tep_not_null($sendto) && $sendto > 0) {
				$email_order .= EMAIL_TEXT_DELIVERY_ADDRESS . "\n" .
			                   	EMAIL_SEPARATOR . "\n" .
			                   	tep_address_label($order->customer['id'], $sendto, 0, '', "\n") . "\n\n";
			}
		}
		
		$email_order .= EMAIL_TEXT_BILLING_ADDRESS . "\n" .
						EMAIL_SEPARATOR . "\n" .
		                tep_address_label($order->customer['id'], $billto, 0, '', "\n") . "\n\n";
		
		$email_order .= EMAIL_TEXT_ORDER_SUMMARY . "\n" .
						EMAIL_SEPARATOR . "\n" .
						EMAIL_TEXT_ORDER_NUMBER . ' ' . $orders_id . "\n" .
						EMAIL_TEXT_DATE_ORDERED . ' ' . strftime(DATE_FORMAT_LONG) . ".\n\n";
		
		$email_order .= EMAIL_TEXT_PRODUCTS . "\n" . 
		                EMAIL_SEPARATOR . "\n" . 
		                $products_ordered . 
		                EMAIL_SEPARATOR . "\n";
		for ($i=0, $n=sizeof($order->totals); $i<$n; $i++) {
			$email_order .= strip_tags($order->totals[$i]['title']) . ' ' . strip_tags($order->totals[$i]['text']) . "\n";
		}
		
		$orders_history_query = tep_db_query("select comments from " . TABLE_ORDERS_STATUS_HISTORY . " where orders_id = '" . (int)$orders_id . "' order by date_added limit 1");
		
		if (tep_db_num_rows($orders_history_query)) {
			$orders_history = tep_db_fetch_array($orders_history_query);
			$order->info['comments'] = $orders_history['comments'];
		}
		
		if ($order->info['comments']) {
		    $email_order .= "\n" . EMAIL_TEXT_EXTRA_INFO . "\n" . 
		    				tep_db_output(tep_db_prepare_input($order->info['comments']));
		}
		
		$email_order .= "\n\n";
		$email_order = $email_order . EMAIL_TEXT_CLOSING . "\n\n" . EMAIL_FOOTER;
		
		tep_mail($order->customer['name'], $order->customer['email_address'], implode(' ', array(EMAIL_SUBJECT_PREFIX, sprintf(EMAIL_TEXT_SUBJECT, (int)$orders_id))), $email_order, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
		
		// send emails to other people
		if (tep_not_null(SEND_EXTRA_ORDER_EMAILS_TO)) {
			// A copy of email to admin when new order is made
			$extra_email_array = explode(',', SEND_EXTRA_ORDER_EMAILS_TO);
			$extra_email_email_pattern = "/([^<]*?)(?:<)([^>]+)(?:>)/is";
			for ($receiver_cnt=0; $receiver_cnt < count($extra_email_array); $receiver_cnt++) {
				if (preg_match($extra_email_email_pattern, $extra_email_array[$receiver_cnt], $regs)) {
					$receiver_name = trim($regs[1]);
					$receiver_email = trim($regs[2]);
					tep_mail($receiver_name, $receiver_email, implode(' ', array(EMAIL_SUBJECT_PREFIX, sprintf(EMAIL_TEXT_SUBJECT, (int)$orders_id))), $email_order, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
				}
			}
		}
		
		// Send order update 
		tep_status_update_notification('C', (int)$orders_id, 'system', $paypal_order_status_row["orders_status"], MODULE_PAYMENT_PAYPAL_ORDER_STATUS_ID, 'A');
        
		// Send affiliate notification e-mail
		tep_send_affiliate_notification($formatted_order_id, $order);
	}
}

tep_db_query("DELETE FROM " . TABLE_ORDERS_SESSION_INFO . " WHERE orders_id = '" . (int)$orders_id . "'");
?>