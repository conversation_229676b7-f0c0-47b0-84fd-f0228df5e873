<?php
/*
  $Id: info_cc.inc.php,v 1.1 2004/09/23 05:36:09 stanley Exp $

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  DevosC, Developing open source Code
  http://www.devosc.com

  Copyright (c) 2003 osCommerce
  Copyright (c) 2004 DevosC.com

  Released under the GNU General Public License
*/
?>
<table class="pagebody" cellpadding="0" cellspacing="0" border="0" align="center">
	<tr>
	  <td class="ppheading">Make Shopping Easier for Customers with Website Payments</TD>
	</tr>
	<tr>
		<td bgcolor="#999999"><?php echo tep_image(DIR_WS_MODULES . 'payment/paypal/images/pixel.gif','','1','2'); ?></td>
	</tr>
	<tr>
		<td><?php echo tep_image(DIR_WS_MODULES . 'payment/paypal/images/pixel.gif','','1','10'); ?></td>
	</tr>
  <tr>
    <td class=".pptext"><?php echo tep_image(DIR_WS_MODULES . 'payment/paypal/images/hdr_ppGlobev4_160x76.gif',' PayPal ','','','align=right valign="top" style="margin: 10px;"'); ?>
PayPal has optimized their checkout experience by launching an exciting new improvement to their payment flow.
<br><br>For new buyers, signing up for a PayPal account is now optional. This means you can complete your payment first, and then decide whether to save your information for future purchases.
<p>To pay by credit card, look for this button:<br>
<center><?php echo tep_image(DIR_WS_MODULES . 'payment/paypal/images/PayPal-ContinueCheckout.gif','','',''); ?></center>
<br>
Or you may see this:<br>
<center><?php echo tep_image(DIR_WS_MODULES . 'payment/paypal/images/PayPal-no-account-Click-Here.gif','','',''); ?></center>
<br>
One of these options should appear on the first PayPal screen.<br>
<p>Note: if you are a PayPal member, you can either use your account,
or use a credit card that is not associated with a PayPal account.
In that case you'd also need to use an email address that's not associated with a PayPal account.
  </td>
</tr>
	<tr>
		<td><?php echo tep_image(DIR_WS_MODULES . 'payment/paypal/images/pixel.gif','','1','10'); ?></td>
	</tr>
	<tr>
		<td bgcolor="#999999"><?php echo tep_image(DIR_WS_MODULES . 'payment/paypal/images/pixel.gif','','1','2'); ?></td>
	</tr>
	<tr>
		<td align="right" style="padding-top: 5px; padding-right: 5px; padding-bottom: 10px;"><form><input type="button" value="Close Window" onclick="window.close();return(false);" class="ppbuttonsmall"></form></td>
	</tr>
</table>
