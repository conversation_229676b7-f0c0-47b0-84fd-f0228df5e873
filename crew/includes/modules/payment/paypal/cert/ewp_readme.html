<HTML>
<!-- B<PERSON><PERSON> HEAD INLUDE -->
<HEAD>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<meta name="description" content="Stellar Web Solutions is a leading provider of web applications development and computer network systems consulting. Perl CGI, SQL databases, custom web development." />
<meta name="keywords" content="web applications, perl cgi, mysql, sql database, designers, developers, consultants,web experts" />
<TITLE>stellarwebsolutions: How-To PayPal Button Encryption in PHP
</TITLE>
<LINK REL=stylesheet HREF="/styles/stellar.css" TYPE="text/css">
</HEAD>
<BODY >
<!-- BEGIN GOOGLE ANALYTICS -->
<script src="http://www.google-analytics.com/urchin.js" type="text/javascript">
</script>
<script type="text/javascript">
_uacct = "UA-1164777-1";
urchinTracker();
</script>
<!-- END GOOGLE ANALYTICS -->
<!-- consult -->
<P class="datebar" align=center>
<TABLE BORDER=0 WIDTH=674 CELLSPACING=0 CELLPADDING=0 >
<TR BGCOLOR=#FF6600><TD ALIGN=right colspan=2>
<!--Tuesday, February 19th 2008, 20:53:12EST-->&nbsp;
</TD></TR>
<TR><TD VALIGN=TOP ALIGN=center WIDTH=220>

<A HREF=/><IMG border=0 WIDTH=220 src=/images/stellar.png ALT="StellarWebSolutions.com - The Web Application Experts - Main Page"></A><BR>
<hr>
<!-- END CONTENT INLUDE -->

<!-- BEGIN SIDE INLUDE -->
<TABLE BORDER=0 HEIGHT=300 CELLSPACING=0 CELLPADDING=0 >
<TR><TD><A class="menu" HREF=/en/products.php>Products</A></TD>
</TD>
<!--<TR><TD><A class="menu" HREF=/en/consult.php>Consulting</A></TD></TR>-->
<TR><TD><A class="menu" HREF=/en/articles.php>Web How-To</A></TD>
</TR>
<TR><TD><A class="menu" HREF=/en/tools.php>Free Tools</A></TD>
</TR>
<TR><TD><A class="menu" HREF=/en/faq.php>FAQ</A></TD>
</TR>
<TR><TD><A class="menu" HREF=/en/contact.php>Contact Us</A></TD>
</TR>
<TR><TD><A class="menu" HREF=/en/clients.php>Our Clients</A></TD>
</TR>
</TABLE>

</TD><TD WIDTH=450 VALIGN=top>
<BR><BR>
<BR><BR><BR><BR>
<!-- END SIDE INLUDE -->
<!-- BEGIN CONTENT INLUDE -->
<P class="crumb">You are here: <A HREF=/>Home</A> >
<A HREF=/en/articles.php>Articles</A> > PayPal Buttons in PHP</P>

<H2>How-To: Build your own PayPal Encrypted Buttons</H2>

<P>Paypal uses the X.509 standard certificate format which was originally
developed for sending encrypted email messages. PayPal uses this to
encrypt the data so they can decrypt it, while signing it so that they
can ensure that you originated it.</P>

<P class="adbanner">
<DIV class="adbanner">
<TABLE border=0>
<TR><TD><DIV class="adbox">
<A href=http://www.stellarwebsolutions.com/en/products.php#donations>
<P class="adboxhead">PayPal Donations Tracker</P>
<P class="adboxtext">Track and Display donations live;<BR>
Increase donations now!</P></A>
</DIV></TD>
<TD><DIV class="adbox">
<A href=http://www.stellarwebsolutions.com/en/products.php#ecart>
<P class="adboxhead">Stellar eCart Shop</P>
<P class="adboxtext">Easy shopping cart for PayPal.<BR>
Use encryption, discounts, and coupons.</P></A>
</DIV></TD></TR>
<TR><TD><DIV class="adbox">
<A href=http://www.stellarwebsolutions.com/en/products.php#ipnrelay>
<P class="adboxhead">Integrate multiple IPNs</P>
<P class="adboxtext">Stellar PayPal IPN Relay - forward<BR>
IPN data to multiple applications.</P></A>
</DIV></TD>
<TD><DIV class="adbox">
<A href=http://www.stellarwebsolutions.com/en/products.php#ipn>
<P class="adboxhead">PayPal IPN System</P>
<P class="adboxtext">Store orders in your own<BR>
MySQL database.</P></A>
</DIV></TD></TR>
</TABLE>
</DIV>


<H3>How It Works</H3>
<P>You first create a private key and public signing certificate and download PayPal's public
key. You upload your public certificate to PayPal. PayPal generates a unique
ID to ensure a malicious user is not just using their own certificate.</P>
<P>Using something like open source tool OPENSSL, you can encrypt your form data
to be sent to PayPal. You can test all of this with PayPal's sandbox website.</P>

<H3>How-To: Do it yourself button encryption</H3>

<H4>Step 1: Generate your private key and public certificate.</H4>
<P>You can either generate your private key and public certificate on your
own server manually (step 1a and 1b), or <B>use our
<A href=/certificates/stellar_cert_builder.php target=_blank>Stellar
PayPal Certificate Wizard</A>.</B></P>


<H4>Step 1a: Manual Creation - Generate a Private Key 1024 bytes long</H4>
<DIV class="example">
<CODE>openssl genrsa -out my-prvkey.pem 1024</CODE>
</DIV>
<P>my-prvkey.pem is your private key.</P>

<H4>Step 1b: Manual Creation - Generate public certificate good for 1 year</H4>
<DIV class="example">
<CODE>openssl req -new -key my-prvkey.pem -x509 -days 365 -out my-pubcert.pem</CODE>
</DIV>
<P>my-pubcert.pem is your public signing certificate. Remember that your
certificate is only valid for 365 days with this command, you should recreate your
key and certificate every year.</P>



<H4>Step 2: Upload Your Public Certificate</H4>
<P>To upload your public certificates to PayPal:<BR>
1.   Log in to your Business or Premier account.<BR>
2.   Click the <B>Profile</B> subtab.  <BR>
3.   In the <B>Seller Preferences</B> column, click <B>Encrypted Payment Settings</B>.  <BR>
4.   Click <B>Add</B>.  <BR>
5.   Click <B>Browse</B>, and select your public certificate file "my-pubcert.pem".  <BR>
6.   When your public certificate is successfully uploaded, it appears on the next screen under Your Public Certificates.  <BR>
7.   Record the <B>Cert ID</B>, you'll need to include this in any encrypted data.<BR>
</P>


<H4>Step 3: Download the PayPal Public Certificate</H3>
<P>You use PayPal's public certificate to encrypt your button code. To download PayPal's public certificate:

1.   Log in to your Business or Premier account.<BR>
2.   Click the <B>Profile</B> subtab.  <BR>
3.   In the <B>Seller Preferences</B> column, click <B>Encrypted Payment Settings</B>.  <BR>
4.   Click <B>Download</B> in the PayPal Public Certificate area.  <BR>
</P>

<H4>Step 4: Block unencrypted payment buttons</H3>
<P>You can prevent malicious users from submitting made up unencrypted buttons by blocking
unencrypted payments. You should probably have everything working before you complete this
step or your current payment buttons may become broken.<BR>
1.   Log in to your Business or Premier account.<BR>
2.   Click the <B>Profile</B> subtab.  <BR>
3.   Click the <B>Website Payment Preferences</B> link in the right-hand menu.  <BR>
4.   Select On next to <B>Block Non-encrypted Website Payments</B>.  <BR>
5.   Click <B>Save</B>.  
</P>

<H4>Step 5: Generate your own payment buttons.</H3>
<P>You can use PHP or other languages such as PERL to implement PayPal encrypted button generation. </P>

<P>The following code is an example of how to implement
PayPal button encryption. 
This code uses proc_open to call OpenSSL and read the output - an encrypted blob. The function paypal_encrypt accepts a PHP associative array as input and returns the encrypted text. 

<DIV class="example">
<P align=right><A HREF=/en/articles/sample_button_code_for_php.txt>Download Example</A></P>
<PRE>
&lt;HTML&gt;
&lt;?php
//Sample PayPal Button Encryption: Copyright 2006,2007 StellarWebSolutions.com
//Not for resale  - license agreement at
//http://www.stellarwebsolutions.com/en/eula.php
//Updated: 2007 04 04

#Set home directory for OpenSSL
putenv("HOME=~");

# private key file to use
$MY_KEY_FILE = "/usr/home/<USER>/paypal/my-prvkey.pem";

# public certificate file to use
$MY_CERT_FILE = "/usr/home/<USER>/paypal/my-pubcert.pem";

# Paypal's public certificate
$PAYPAL_CERT_FILE = "/usr/home/<USER>/paypal/paypal_cert.pem";

# path to the openssl binary
$OPENSSL = "/usr/bin/openssl";


$form = array('cmd' =&gt; '_xclick',
        'business' =&gt; '<EMAIL>',
        'cert_id' =&gt; 'SD3DG5FFF1234',
        'lc' =&gt; 'US',
        'custom' =&gt; 'test',
        'invoice' =&gt; '',
        'currency_code' =&gt; 'USD',
        'no_shipping' =&gt; '1',
        'item_name' =&gt; 'Donation',
        'item_number' =&gt; '1',
	'amount' =&gt; '10'
	);


	$encrypted = paypal_encrypt($form);


function paypal_encrypt($hash)
{
	//Sample PayPal Button Encryption: Copyright 2006,2007 StellarWebSolutions.com
	//Not for resale - license agreement at
	//http://www.stellarwebsolutions.com/en/eula.php

	global $MY_KEY_FILE;
	global $MY_CERT_FILE;
	global $PAYPAL_CERT_FILE;
	global $OPENSSL;

	if (!file_exists($MY_KEY_FILE)) {
		echo "ERROR: MY_KEY_FILE $MY_KEY_FILE not found\n";
	}
	if (!file_exists($MY_CERT_FILE)) {
		echo "ERROR: MY_CERT_FILE $MY_CERT_FILE not found\n";
	}
	if (!file_exists($PAYPAL_CERT_FILE)) {
		echo "ERROR: PAYPAL_CERT_FILE $PAYPAL_CERT_FILE not found\n";
	}
	if (!file_exists($OPENSSL)) {
		echo "ERROR: OPENSSL $OPENSSL not found\n";
	}

	//Assign Build Notation for PayPal Support
	$hash['bn']= 'StellarWebSolutions.PHP_EWP';

	$openssl_cmd = "$OPENSSL smime -sign -signer $MY_CERT_FILE -inkey $MY_KEY_FILE " .
                "-outform der -nodetach -binary | $OPENSSL smime -encrypt " .
                "-des3 -binary -outform pem $PAYPAL_CERT_FILE";

	$descriptors = array(
        	0 =&gt; array("pipe", "r"),
		1 =&gt; array("pipe", "w"),
	);

	$process = proc_open($openssl_cmd, $descriptors, $pipes);
    
	if (is_resource($process)) {
		foreach ($hash as $key =&gt; $value) {
			if ($value != "") {
				//echo "Adding to blob: $key=$value\n";
				fwrite($pipes[0], "$key=$value\n");
			}
		}
		fflush($pipes[0]);
        	fclose($pipes[0]);

		$output = "";
		while (!feof($pipes[1])) {
			$output .= fgets($pipes[1]);
		}
		//echo $output;
		fclose($pipes[1]); 
		$return_value = proc_close($process);
		return $output;
	}
	return "ERROR";
};
?&gt; 
&lt;HEAD&gt;
&lt;LINK REL=stylesheet HREF="/styles/stellar.css" TYPE="text/css"&gt;
&lt;TITLE&gt;PHP Sample Donation using PayPal Encrypted Buttons&lt;/TITLE&gt;
&lt;/HEAD&gt;
&lt;BODY bgcolor=white&gt;
&lt;TABLE border=0&gt;
&lt;TR&gt;&lt;TD align=center&gt;
&lt;h1&gt;Sample Donation Page&lt;/h1&gt;
&lt;P&gt;This page uses encrypted PayPal buttons for your security.&lt;/P&gt;
&lt;form action="https://www.paypal.com/cgi-bin/webscr" method="post" target=_blank&gt;
&lt;input type="hidden" name="cmd" value="_s-xclick"&gt;
&lt;input type="hidden" name="encrypted" value="
&lt;?PHP echo $encrypted; ?&gt;"&gt;
&lt;input type="submit" value="Donate $10"&gt;
&lt;/form&gt;
&lt;P&gt;&lt;SMALL&gt;(PayPal will open in a new window for demonstration purposes.)&lt;/SMALL&gt;&lt;/P&gt;
&lt;/TD&gt;&lt;/TR&gt;&lt;/TABLE&gt;
&lt;/BODY&gt;

&lt;/HTML&gt;

</PRE>
</DIV>

<P>Here's an example of the above code in action using a simple donation. Right click in the white box and "View Source" to see
the underlying secure encrypted button blob.</P>
<DIV class="example">
<P align=right><A href=/en/articles/sample_button_code_for_php.txt>Download Example</A></P>

<iframe name="paypal_example1" src="/en/articles/example_button_encryption.php" align="top" height="270" width="340" hspace="10" vspace="10">
<A href=/en/articles/example_button_encryption.php>Click Here</a>
</IFRAME>
</DIV>

<!-- END CONTENT INLUDE -->
<!-- BEGIN FOOT INLUDE -->
</TD></TR><TR><TD align=center COLSPAN=2>

<HR>

<P class=bottommenu align=center><A HREF=/en/about.php>About&nbsp;Us</A> <font color=#FF6600>|</font>
<A HREF=/en/consult.php>Consulting&nbsp;Services</A> <font color=#FF6600>|</font>
<A HREF=/en/contact.php>Contact&nbsp;Us</A> <font color=#FF6600>|</font>
<A HREF=/en/partners.php>Partners</A> <font color=#FF6600>|</font>
<A HREF=/en/privacy.php>Privacy</A> <font color=#FF6600>|</font>
<A HREF=/en/termsofservice.php>User&nbsp;Agreement</A> <font color=#FF6600>|</font>
<A HREF=/en/copyright.php>Copyright</A></P>
<HR>
</TD></TR></TABLE></P>


</BODY>
<!-- END FOOT INLUDE -->
</HTML>
