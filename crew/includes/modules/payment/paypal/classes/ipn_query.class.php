<?php
/*
  	$Id: ipn_query.class.php,v 1.6 2015/09/18 09:07:40 sionghuat.chng Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	DevosC, Developing open source Code
  	http://www.devosc.com
	
  	Copyright (c) 2003 osCommerce
  	Copyright (c) 2004 DevosC.com
	
  	Released under the GNU General Public License
*/
class ipn_query {
    var $info, $txn, $customer;
	
    function ipn_query($order_id) {
      	$this->info = array();
      	$this->txn = array();
      	$this->customer = array();
      	$this->query($order_id);
    }
	
    function query($order_id) {
    	if ((int)$order_id > 0 ) {
    		$ipn_query = tep_db_query("select * from " . TABLE_PAYPAL . " where invoice = '" . (int)$order_id . "'");
	      	$ipn = tep_db_fetch_array($ipn_query);
	      	
	      	$paypal_verify_row = array('info_verified'=>0);
	      	$customer_select_sql = "SELECT o.customers_id 
  									FROM " . TABLE_ORDERS . " AS o 
  									WHERE o.orders_id = '" . (int)$ipn['invoice'] . "'";
			$customer_result_sql = tep_db_query($customer_select_sql);
			if ($customer_row = tep_db_fetch_array($customer_result_sql)) {
		      	$paypal_verify_select_sql = "	SELECT civ.info_verified 
		      									FROM " . TABLE_CUSTOMERS_INFO_VERIFICATION . " AS civ 
		      									WHERE civ.customers_id = '".$customer_row['customers_id']."'
		      										AND civ.customers_info_value = '" . tep_db_input($ipn['payer_email']) . "'
		      										AND civ.info_verification_type = 'email'";
		      	$paypal_verify_result_sql = tep_db_query($paypal_verify_select_sql);
		      	$paypal_verify_row = tep_db_fetch_array($paypal_verify_result_sql);

			}
	      	
	      	$this->info = array('txn_type'            => $ipn['txn_type'],
						        'reason_code'         => $ipn['reason_code'],
						        'payment_type'        => $ipn['payment_type'],
						        'payment_status'      => $ipn['payment_status'],
						        'pending_reason'      => $ipn['pending_reason'],
						        'invoice'             => $ipn['invoice'],
						        'mc_currency'         => $ipn['mc_currency'],
						        'payment_date'        => $ipn['payment_date'],
						        'business'            => $ipn['business'],
						        'receiver_email'      => $ipn['receiver_email'],
						        'receiver_id'         => $ipn['receiver_id'],
						        'txn_id'              => $ipn['txn_id'],
						        'parent_txn_id'       => $ipn['parent_txn_id'],
						        'notify_version'      => $ipn['notify_version'],
						        'verify_sign'         => $ipn['verify_sign'],
						        'last_modified'       => $ipn['last_modified'],
						        'date_added'          => $ipn['date_added']);
	
	      	$this->txn = array(	'num_cart_items'      => $ipn['num_cart_items'],
								'mc_gross'            => $ipn['mc_gross'],
						        'mc_fee'              => $ipn['mc_fee'],
						        'payment_gross'       => $ipn['payment_gross'],
						        'payment_fee'         => $ipn['payment_fee'],
						        'settle_amount'       => $ipn['settle_amount'],
						        'settle_currency'     => $ipn['settle_currency'],
						        'exchange_rate'       => $ipn['exchange_rate']);
			
	      	$this->customer = array('first_name'           => $ipn['first_name'],
							        'last_name'            => $ipn['last_name'],
							        'payer_business_name'  => $ipn['payer_business_name'],
							        'address_name'         => $ipn['address_name'],
							        'address_street'       => $ipn['address_street'],
							        'address_city'         => $ipn['address_city'],
							        'address_state'        => $ipn['address_state'],
							        'address_zip'          => $ipn['address_zip'],
							        'address_country'      => $ipn['address_country'],
							        'address_status'       => $ipn['address_status'],
							        'payer_email'          => $ipn['payer_email'],
							        'payer_email_verified' => $paypal_verify_row['info_verified'],
							        'payer_id'             => $ipn['payer_id'],
							        'payer_status'         => $ipn['payer_status'],
							        'memo'                 => $ipn['memo']);
		}
	}
}//end class
?>