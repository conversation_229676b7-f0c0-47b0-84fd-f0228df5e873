<?php

require_once('payment_gateway.php');

class paypalEC extends payment_gateway {

    var $code, $paypalECCurrencies, $apiUsername, $apiPassword, $apiSignature;

    function paypalEC($pm_id = '') {
        global $order, $languages_id, $default_languages_id, $currency;

        $this->code = 'paypalEC';
        $this->title = $this->code;
        $this->preferred = true;
        $this->filename = 'paypalEC.php';
        $this->auto_cancel_period = 30; // In minutes
        $this->connect_via_proxy = false; // localhost test - set to true, server test - set to false //need proxy to connect outside
        $this->force_to_checkoutprocess = true;
        $this->has_ipn = true;
        $this->payment_methods_id = 0;
        $this->payment_methods_parent_id = 0;

        $payment_methods_select_sql = "	SELECT  payment_methods_parent_id, payment_methods_code, 
												payment_methods_types_id, 
												payment_methods_receive_status_mode, payment_methods_id, 
												payment_methods_title, payment_methods_sort_order, 
												payment_methods_receive_status, payment_methods_legend_color, 
												payment_methods_logo 
										FROM " . TABLE_PAYMENT_METHODS . "
										WHERE 1 ";
        if ((int) $pm_id > 0) {
            $payment_methods_select_sql .= "	AND payment_methods_id = '" . (int) $pm_id . "'";
        } else {
            $payment_methods_select_sql .= "	AND payment_methods_filename = '" . tep_db_input($this->filename) . "'";
        }

        $payment_methods_result_sql = tep_db_query($payment_methods_select_sql);
        if ($payment_methods_row = tep_db_fetch_array($payment_methods_result_sql)) {
            $pm_desc_title_select_sql = "	SELECT payment_methods_description_title 
											FROM " . TABLE_PAYMENT_METHODS_DESCRIPTION . " 
											WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "' 
												AND (IF (languages_id = '" . tep_db_input($languages_id) . "', 1, IF ((	SELECT COUNT(payment_methods_id) > 0 
																														FROM " . TABLE_PAYMENT_METHODS_DESCRIPTION . " 
																														WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "' 
																															AND languages_id = '" . (int) $languages_id . "'), 0, languages_id = '" . (int) $default_languages_id . "')))";
            $pm_desc_title_result_sql = tep_db_query($pm_desc_title_select_sql);
            $pm_desc_title_row = tep_db_fetch_array($pm_desc_title_result_sql);
            $this->display_title = $pm_desc_title_row['payment_methods_description_title'];
            $this->description = $this->display_title;
            $this->enabled = $payment_methods_row['payment_methods_receive_status_mode'];
            $this->payment_methods_id = (int) $payment_methods_row['payment_methods_id'];
            $this->payment_methods_parent_id = (int) $payment_methods_row['payment_methods_parent_id'];
            $this->code = $payment_methods_row['payment_methods_code'];
            $this->title = $payment_methods_row['payment_methods_title'];
            $this->sort_order = $payment_methods_row['payment_methods_sort_order'];
            $this->legend_display_colour = $payment_methods_row['payment_methods_legend_color'];
        }

        $configuration_setting_array = $this->load_pm_setting();
        $this->order_status_id = $configuration_setting_array['MODULE_PAYMENT_PAYPALEC_ORDER_STATUS_ID'];
        $this->processing_status_id = $configuration_setting_array['MODULE_PAYMENT_PAYPALEC_PROCESSING_STATUS_ID'];
        $this->confirm_complete_days = $configuration_setting_array['MODULE_PAYMENT_PAYPALEC_CONFIRM_COMPLETE'];
        $this->require_address_information = $configuration_setting_array['MODULE_PAYMENT_PAYPALEC_MANDATORY_ADDRESS_FIELD'];
        $this->test_mode = ($configuration_setting_array['MODULE_PAYMENT_PAYPALEC_TEST_MODE'] == 'True' ? true : false);
        $this->message = $configuration_setting_array['MODULE_PAYMENT_PAYPALEC_MESSAGE'];
        $this->email_message = $configuration_setting_array['MODULE_PAYMENT_PAYPALEC_EMAIL_MESSAGE'];
        $this->email_footer = str_replace(array("<br>", "<BR>", "\r\n"), array("\n", "\n", ''), $this->email_message);

        $this->paypalECCurrencies = $this->get_support_currencies($this->payment_methods_id);
        $this->defCurr = DEFAULT_CURRENCY;
        $this->order_processing_status = ((int) $this->processing_status_id > 0 ? $this->processing_status_id : 7 );
        $this->order_status = ( (int) $this->order_status_id > 0 ? $this->order_status_id : DEFAULT_ORDERS_STATUS_ID );
        $this->version = '93';


        if ($this->test_mode) {
            $this->form_action_url = 'https://www.sandbox.paypal.com/webscr?cmd=_express-checkout&token=';
            $this->api_url = 'https://api-3t.sandbox.paypal.com/nvp';
            $this->ipn_validate_url = 'https://www.sandbox.paypal.com/cgi-bin/webscr';
        } else {
            $this->form_action_url = 'https://www.paypal.com/cgi-bin/webscr?cmd=_express-checkout&token=';
            $this->api_url = 'https://api-3t.paypal.com/nvp';
            $this->ipn_validate_url = 'https://www.paypal.com/cgi-bin/webscr';
        }
        $this->notify_url = tep_href_link('paypalEC_ipn.php');
//        $this->return_url = (defined(FILENAME_CHECKOUT_PROCESS) ? tep_href_link(FILENAME_CHECKOUT_PROCESS) : '');
//        $this->cancel_url = (defined(FILENAME_CHECKOUT_PROCESS) ? tep_href_link(FILENAME_CHECKOUT_PAYMENT, 'error_message=' . MODULE_PAYMENT_PAYPALEC_TEXT_ERROR_MESSAGE, 'SSL', true, false) : '');
    }

    function javascript_validation() {
        return false;
    }

    function selection() {
        global $languages_id, $default_languages_id;
        $selection_array = array();

        $payment_methods_select_sql = "	SELECT payment_methods_id, payment_methods_code, payment_methods_types_id, payment_methods_logo, payment_methods_receive_status_mode 
										FROM " . TABLE_PAYMENT_METHODS . " 
										WHERE payment_methods_receive_status = 1 
											AND payment_methods_receive_status_mode <> 0 
											AND payment_methods_parent_id = '" . (int) $this->payment_methods_id . "' 
										ORDER BY payment_methods_sort_order";
        $payment_methods_result_sql = tep_db_query($payment_methods_select_sql);
        while ($payment_methods_row = tep_db_fetch_array($payment_methods_result_sql)) {
            $pm_desc_title_select_sql = "	SELECT payment_methods_description_title 
											FROM " . TABLE_PAYMENT_METHODS_DESCRIPTION . " 
											WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "' 
												AND (IF (languages_id = '" . tep_db_input($languages_id) . "', 1, IF ((	SELECT COUNT(payment_methods_id) > 0 
																														FROM " . TABLE_PAYMENT_METHODS_DESCRIPTION . " 
																														WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "' 
																															AND languages_id = '" . (int) $languages_id . "'), 0, languages_id = 1)))";
            $pm_desc_title_result_sql = tep_db_query($pm_desc_title_select_sql);
            $pm_desc_title_row = tep_db_fetch_array($pm_desc_title_result_sql);

            $pm_array = $this->load_pm_setting(1, $payment_methods_row['payment_methods_id']);

            $selection_array[] = array('payment_gateway_code' => $this->code,
                'payment_methods_receive_status_mode' => $payment_methods_row['payment_methods_receive_status_mode'],
                'payment_methods_id' => $payment_methods_row['payment_methods_id'],
                'payment_methods_code' => $payment_methods_row['payment_methods_code'],
                'payment_methods_types_id' => $payment_methods_row['payment_methods_types_id'],
                'payment_methods_parent_id' => $this->payment_methods_id,
                'payment_methods_logo' => $payment_methods_row['payment_methods_logo'],
                'payment_methods_description_title' => $pm_desc_title_row['payment_methods_description_title'],
                'currency' => $this->get_support_currencies($payment_methods_row['payment_methods_id']),
                'confirm_complete_days' => $this->confirm_complete_days,
                'show_billing_address' => $this->require_address_information,
                'show_contact_number' => $pm_array['MODULE_PAYMENT_PAYPALEC_MANDATORY_CONTACT_FIELD'],
                'show_ic' => $pm_array['MODULE_PAYMENT_PAYPALEC_MANDATORY_IC_FIELD']
            );

            if ($payment_methods_row['payment_methods_receive_status_mode'] == '-1') {
                $pm_status_message_select = "	SELECT payment_methods_status_message 
												FROM " . TABLE_PAYMENT_METHODS_STATUS_DESCRIPTION . " 
												WHERE payment_methods_mode = 'RECEIVE' 
													AND payment_methods_status = '-1' 
													AND languages_id = '" . (int) $languages_id . "' 
													AND payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "'";
                $pm_status_message_result = tep_db_query($pm_status_message_select);
                $pm_status_message_row = tep_db_fetch_array($pm_status_message_result);
                $selection_array[sizeof($selection_array) - 1]['payment_methods_status_message'] = $pm_status_message_row['payment_methods_status_message'];
            }
        }

        return $selection_array;
    }

    function set_support_currencies($currency_array) {
        $this->paypalECCurrencies = $currency_array;
    }

    function get_require_address_information() {
        return $this->require_address_information;
    }

    function draw_confirm_button() {
        return tep_image_button2('red', 'javascript:document.checkout_confirmation.submit()', MODULE_PAYMENT_PAYPALEC_TEXT_CART, 220);
    }

    function get_confirm_button_caption() {
        return MODULE_PAYMENT_PAYPALEC_TEXT_TITLE;
    }

    function pre_confirmation_check() {
        global $currency;

        if (in_array($currency, $this->paypalECCurrencies)) {
            
        }

        return false;
    }

    function confirmation() {
        
    }

    function process_button() {
        global $HTTP_POST_VARS, $shipping_cost, $total_cost, $shipping_selected, $shipping_method, $currencies, $currency, $customer_id, $order, $languages_id;
        global $order_logged, $OFFGAMERS, $country, $payment;

        if ($this->force_to_checkoutprocess && !tep_session_is_registered('order_logged'))
            return;

        $default_currency = $this->defCurr;
        $paypalECCurrency = $currency;
        if (!in_array($paypalECCurrency, $this->paypalECCurrencies)) {
            $paypalECCurrency = in_array($default_currency, $this->paypalECCurrencies) ? $default_currency : $this->paypalECCurrencies[0];
        }
        $this->get_merchant_account($paypalECCurrency);
        $OrderAmt = number_format($order->info['total'] * $currencies->get_value($paypalECCurrency), $currencies->get_decimal_places($paypalECCurrency), '.', '');

        switch ($this->code) {
            case 'paypalEC':
            case 'paypalEC_creditCard':
            case 'PaypalECDigitalGoods':
                //'------------------------------------
                //' Calls the SetExpressCheckout API call
                //'
                //' The CallShortcutExpressCheckout function is defined in the file PayPalFunctions.php,
                //' it is included at the top of this file.
                //'-------------------------------------------------
                if ($this->code == 'paypalEC_creditCard') {
                    $resArray = $this->CallShortcutExpressCheckout($OrderAmt, $paypalECCurrency, $order_logged, 'Billing');
                } else {
                    $resArray = $this->CallShortcutExpressCheckout($OrderAmt, $paypalECCurrency, $order_logged);
                }
                $ack = strtoupper($resArray["ACK"]);
                if ($ack == "SUCCESS" || $ack == "SUCCESSWITHWARNING") {
                    $token = urldecode($resArray["TOKEN"]);
                    $this->RedirectToPayPal($token, true);
                } else {
                    if ($this->test_mode) {
                        //Display a user friendly Error on the page using any of the following error information returned by PayPal
                        $ErrorCode = urldecode($resArray["L_ERRORCODE0"]);
                        $ErrorShortMsg = urldecode($resArray["L_SHORTMESSAGE0"]);
                        $ErrorLongMsg = urldecode($resArray["L_LONGMESSAGE0"]);
                        $ErrorSeverityCode = urldecode($resArray["L_SEVERITYCODE0"]);

                        echo "SetExpressCheckout API call failed. ";
                        echo "Detailed Error Message: " . $ErrorLongMsg;
                        echo "Short Error Message: " . $ErrorShortMsg;
                        echo "Error Code: " . $ErrorCode;
                        echo "Error Severity Code: " . $ErrorSeverityCode;
                    } else {
                        $this->errorRedirect();
                    }
                }
                break;
            case 'referenceTransactionCC':
            case 'referenceTransaction':
                if (!isset($_SESSION['billlingID'])) {
                    $resArray = $this->CallReferenceTransactionCheckout($paypalECCurrency, $order_logged);
                    $ack = strtoupper($resArray["ACK"]);
                    if ($ack == "SUCCESS" || $ack == "SUCCESSWITHWARNING") {
                        $token = urldecode($resArray["TOKEN"]);
                        $this->RedirectToPayPal($token);
                    } else {
                        if ($this->test_mode) {
                            //Display a user friendly Error on the page using any of the following error information returned by PayPal
                            $ErrorCode = urldecode($resArray["L_ERRORCODE0"]);
                            $ErrorShortMsg = urldecode($resArray["L_SHORTMESSAGE0"]);
                            $ErrorLongMsg = urldecode($resArray["L_LONGMESSAGE0"]);
                            $ErrorSeverityCode = urldecode($resArray["L_SEVERITYCODE0"]);

                            echo "SetExpressCheckout API call failed. ";
                            echo "Detailed Error Message: " . $ErrorLongMsg;
                            echo "Short Error Message: " . $ErrorShortMsg;
                            echo "Error Code: " . $ErrorCode;
                            echo "Error Severity Code: " . $ErrorSeverityCode;
                        } else {
                            $this->errorRedirect();
                        }
                    }
                }
                break;
            case 'paypalPhysical':
                //'------------------------------------
                //' When you integrate this code 
                //' set the variables below with 
                //' shipping address details 
                //' entered by the user on the 
                //' Shipping page.
                //'------------------------------------
                $shipToName = $order->billing['lastname'] . ' ' . $order->billing['firstname'];
                $shipToStreet = $order->billing['street_address'];
                $shipToStreet2 = ''; //Leave it blank if there is no value
                $shipToCity = $order->billing['city'];
                $shipToState = $order->billing['state'];
                $shipToCountryCode = $order->billing['country']['iso_code_2']; // Please refer to the PayPal country codes in the API documentation
                $shipToZip = $order->billing['postcode'];
                $phoneNum = $order->customer['int_dialing_code'] . $order->customer['telephone'];

                //'------------------------------------
                //' Calls the SetExpressCheckout API call
                //'
                //' The CallMarkExpressCheckout function is defined in the file PayPalFunctions.php,
                //' it is included at the top of this file.
                //'-------------------------------------------------
                $resArray = $this->CallMarkExpressCheckout($paymentAmount, $currencyCodeType, $shipToName, $shipToStreet, $shipToCity, $shipToState, $shipToCountryCode, $shipToZip, $shipToStreet2, $phoneNum
                );

                $ack = strtoupper($resArray["ACK"]);
                if ($ack == "SUCCESS" || $ack == "SUCCESSWITHWARNING") {
                    $token = urldecode($resArray["TOKEN"]);
                    $this->RedirectToPayPal($token, true);
                } else {
                    if ($this->test_mode) {
                        //Display a user friendly Error on the page using any of the following error information returned by PayPal
                        $ErrorCode = urldecode($resArray["L_ERRORCODE0"]);
                        $ErrorShortMsg = urldecode($resArray["L_SHORTMESSAGE0"]);
                        $ErrorLongMsg = urldecode($resArray["L_LONGMESSAGE0"]);
                        $ErrorSeverityCode = urldecode($resArray["L_SEVERITYCODE0"]);

                        echo "SetExpressCheckout API call failed. ";
                        echo "Detailed Error Message: " . $ErrorLongMsg;
                        echo "Short Error Message: " . $ErrorShortMsg;
                        echo "Error Code: " . $ErrorCode;
                        echo "Error Severity Code: " . $ErrorSeverityCode;
                    } else {
                        $this->errorRedirect();
                    }
                }
                break;
            case 'Visa':
            case 'MasterCard':
            case 'Amex':
            case 'Discover':
            case 'DirectPayment':
//				echo 'NOT SUPPORTED YET';
//				//'------------------------------------
//				//' The paymentAmount is the total value of 
//				//' the shopping cart, that was set 
//				//' earlier in a session variable 
//				//' by the shopping cart page
//				//'------------------------------------
//					$paymentAmount = $_SESSION["Payment_Amount"];
//
//				//'------------------------------------
//				//' The currencyCodeType and paymentType 
//				//' are set to the selections made on the Integration Assistant 
//				//'------------------------------------
//				$currencyCodeType = "USD";
//				$paymentType = "Sale";
//
//				//' Set these values based on what was selected by the user on the Billing page Html form
//
//				$creditCardType = "<<Visa/MasterCard/Amex/Discover>>"; //' Set this to one of the acceptable values (Visa/MasterCard/Amex/Discover) match it to what was selected on your Billing page
//				$creditCardNumber = "<<CC number>>"; //' Set this to the string entered as the credit card number on the Billing page
//				$expDate = "<<Expiry Date>>"; //' Set this to the credit card expiry date entered on the Billing page
//				$cvv2 = "<<cvv2>>"; //' Set this to the CVV2 string entered on the Billing page 
//				$firstName = "<<firstName>>"; //' Set this to the customer's first name that was entered on the Billing page 
//				$lastName = "<<lastName>>"; //' Set this to the customer's last name that was entered on the Billing page 
//				$street = "<<street>>"; //' Set this to the customer's street address that was entered on the Billing page 
//				$city = "<<city>>"; //' Set this to the customer's city that was entered on the Billing page 
//				$state = "<<state>>"; //' Set this to the customer's state that was entered on the Billing page 
//				$zip = "<<zip>>"; //' Set this to the zip code of the customer's address that was entered on the Billing page 
//				$countryCode = "<<PayPal Country Code>>"; //' Set this to the PayPal code for the Country of the customer's address that was entered on the Billing page 
//				$currencyCode = "<<PayPal Currency Code>>"; //' Set this to the PayPal code for the Currency used by the customer 
//
//				/*
//				  '------------------------------------------------
//				  ' Calls the DoDirectPayment API call
//				  '
//				  ' The DirectPayment function is defined in PayPalFunctions.php included at the top of this file.
//				  '-------------------------------------------------
//				 */
//
//				$resArray = $this->DirectPayment($paymentAmount, $creditCardType, $creditCardNumber, $expDate, $cvv2, $firstName, $lastName, $street, $city, $state, $zip, $countryCode, $currencyCode);
//
//				$ack = strtoupper($resArray["ACK"]);
//				if ($ack == "SUCCESS" || $ack == "SUCCESSWITHWARNING") {
//					//Getting transaction ID from API responce. 
//					$TransactionID = urldecode($resArray["TRANSACTIONID"]);
//
//					echo "Your payment has been successfully processed";
//				} else {
//					//Display a user friendly Error on the page using any of the following error information returned by PayPal
//					$ErrorCode = urldecode($resArray["L_ERRORCODE0"]);
//					$ErrorShortMsg = urldecode($resArray["L_SHORTMESSAGE0"]);
//					$ErrorLongMsg = urldecode($resArray["L_LONGMESSAGE0"]);
//					$ErrorSeverityCode = urldecode($resArray["L_SEVERITYCODE0"]);
//
//					echo "Direct credit card payment API call failed. ";
//					echo "Detailed Error Message: " . $ErrorLongMsg;
//					echo "Short Error Message: " . $ErrorShortMsg;
//					echo "Error Code: " . $ErrorCode;
//					echo "Error Severity Code: " . $ErrorSeverityCode;
//				}
//				break;
        }
    }

    function postValidation($postData) {
        global $currencies;
    }

    function before_process() {
        global $order, $order_logged, $currency, $currencies, $OFFGAMERS, $customer_id;

        if (!tep_session_is_registered('order_logged')) {
            return;
        }
        if (isset($_GET['token']) && !empty($_GET['token'])) {
            $token = $_SESSION['paypalToken'] = $_GET['token'];
            $default_currency = $this->defCurr;
            $paypalECCurrency = $currency;
            if (!in_array($paypalECCurrency, $this->paypalECCurrencies)) {
                $paypalECCurrency = in_array($default_currency, $this->paypalECCurrencies) ? $default_currency : $this->paypalECCurrencies[0];
            }
            $this->get_merchant_account($paypalECCurrency);
            $OrderAmt = number_format($order->info['total'] * $currencies->get_value($paypalECCurrency), $currencies->get_decimal_places($paypalECCurrency), '.', '');

            switch ($this->code) {
                case 'paypalPhysical':
                case 'paypalEC':
                case 'paypalEC_creditCard':
                case 'PaypalECDigitalGoods':
                    /*
                      '------------------------------------
                      ' Calls the DoExpressCheckoutPayment API call
                      '-------------------------------------------------
                     */
                    if (isset($_GET['PayerID']) && !empty($_GET['PayerID'])) {
                        $_SESSION['PayerID'] = $_GET['PayerID'];
                    } else {
                        $getPayerIDArray = $this->GetShippingDetails($token);
                        $ack = (isset($getPayerIDArray["ACK"]) ? strtoupper($getPayerIDArray["ACK"]) : '');
                        if (($ack == "SUCCESS" || $ack == "SUCCESSWITHWARNING") && isset($getPayerIDArray['PAYERID'])) {
                            $_SESSION['PayerID'] = $getPayerIDArray['PAYERID'];
                        } else {
                            if ($this->test_mode) {
                                //Display a user friendly Error on the page using any of the following error information returned by PayPal
                                $ErrorCode = urldecode($resArray["L_ERRORCODE0"]);
                                $ErrorShortMsg = urldecode($resArray["L_SHORTMESSAGE0"]);
                                $ErrorLongMsg = urldecode($resArray["L_LONGMESSAGE0"]);
                                $ErrorSeverityCode = urldecode($resArray["L_SEVERITYCODE0"]);

                                echo "GetExpressCheckoutDetails API call failed. ";
                                echo "Detailed Error Message: " . $ErrorLongMsg;
                                echo "Short Error Message: " . $ErrorShortMsg;
                                echo "Error Code: " . $ErrorCode;
                                echo "Error Severity Code: " . $ErrorSeverityCode;
                            } else {
                                $this->errorRedirect();
                            }
                        }
                    }
                    $this->paymentConfirmation();  // Do not need to use payment confirmation page for paypal ec checkout
//					tep_redirect(tep_href_link(FILENAME_PAYMENT_CONFIRMATION, '', true, false));
                    exit;
                    break;
                case 'referenceTransactionCC':
                case 'referenceTransaction':
                    $billing_id = $this->CreateBillingAgreement($token, $customer_id, $order_logged);
                    if (!empty($billing_id)) {
                        $_SESSION['billlingID'] = $billing_id;
                        //add otp session and cookie to prevent security pin checking for after login user
                        $_SESSION['checkout_otp'] = '1';
                        $time = time();
                        $cookid = $time . "." . $_SESSION['customer_id'] . "." . md5($time . $_SESSION['customer_id'] . CHECKOUT_DEVICEPIN_SECRET);
                        $cookie_domain = (($request_type == 'NONSSL') ? HTTP_COOKIE_DOMAIN : HTTPS_COOKIE_DOMAIN);
                        $cookie_path = (($request_type == 'NONSSL') ? HTTP_COOKIE_PATH : HTTPS_COOKIE_PATH);
                        $cookieLifeTime = time() + 60 * 60 * 24 * 90;
                        tep_setcookie('ogm[cp]', $cookid, $cookieLifeTime, $cookie_path, $cookie_domain);
                        tep_redirect(tep_href_link(FILENAME_PAYMENT_CONFIRMATION, '', true, false));
                        exit;
                    } else {
                        $this->errorRedirect();
                    }

                    break;
            }
        } else if ($this->code == 'referenceTransaction' || $this->code == 'referenceTransactionCC') {
            
        }
    }

    public function paymentConfirmation() {
        global $order, $currency, $order_logged, $currencies, $OFFGAMERS;

        $default_currency = $this->defCurr;
        $paypalECCurrency = $order->info['currency'];
        $orderId = $order_logged;
        if (!in_array($paypalECCurrency, $this->paypalECCurrencies)) {
            $paypalECCurrency = in_array($default_currency, $this->paypalECCurrencies) ? $default_currency : $this->paypalECCurrencies[0];
        }
        //if current current not supported, redirect back to payment selection
        if (!in_array($currency, $this->paypalECCurrencies)) {
            $this->errorRedirect();
        }
        $this->get_merchant_account($paypalECCurrency);
        $amount = ($order->info['total'] > 0) ? $order->info['total'] : $order->info['total_value'];
        $OrderAmt = number_format($amount * $currencies->get_value($paypalECCurrency), $currencies->get_decimal_places($paypalECCurrency), '.', '');

        switch ($this->code) {
            case 'paypalPhysical':
            case 'paypalEC':
            case 'paypalEC_creditCard':
            case 'PaypalECDigitalGoods':
                if (isset($_SESSION['paypalToken']) && !empty($_SESSION['paypalToken'])) {
                    $token = $_SESSION['paypalToken'];
                } else {
                    $resArray = $this->CallShortcutExpressCheckout($OrderAmt, $paypalECCurrency, $orderId, 'Login');
                    $ack = strtoupper($resArray["ACK"]);
                    if ($ack == "SUCCESS" || $ack == "SUCCESSWITHWARNING") {
                        $token = urldecode($resArray["TOKEN"]);
                    }
                }
                $getPayerInfoArray = $this->GetShippingDetails($token);
                $paypalec_data_array = array(
                    'address_name' => isset($getPayerInfoArray['PAYMENTREQUEST_0_SHIPTONAME']) ? $getPayerInfoArray['PAYMENTREQUEST_0_SHIPTONAME'] : '',
                    'address_street' => isset($getPayerInfoArray['PAYMENTREQUEST_0_SHIPTOSTREET']) ? $getPayerInfoArray['PAYMENTREQUEST_0_SHIPTOSTREET'] : '',
                    'address_city' => isset($getPayerInfoArray['PAYMENTREQUEST_0_SHIPTOCITY']) ? $getPayerInfoArray['PAYMENTREQUEST_0_SHIPTOCITY'] : '',
                    'address_state' => isset($getPayerInfoArray['PAYMENTREQUEST_0_SHIPTOSTATE']) ? $getPayerInfoArray['PAYMENTREQUEST_0_SHIPTOSTATE'] : '',
                    'address_zip' => isset($getPayerInfoArray['PAYMENTREQUEST_0_SHIPTOZIP']) ? $getPayerInfoArray['PAYMENTREQUEST_0_SHIPTOZIP'] : '',
                    'address_country' => isset($getPayerInfoArray['PAYMENTREQUEST_0_SHIPTOCOUNTRYNAME']) ? $getPayerInfoArray['PAYMENTREQUEST_0_SHIPTOCOUNTRYNAME'] : '',
                    'address_status' => isset($getPayerInfoArray['PAYMENTREQUEST_0_ADDRESSSTATUS']) ? $getPayerInfoArray['PAYMENTREQUEST_0_ADDRESSSTATUS'] : '',
                );
                $paypalec_payment_select_sql = "	SELECT paypal_order_id
													FROM " . TABLE_PAYPALEC . "
													WHERE paypal_order_id = '" . $orderId . "'";
                $paypalec_payment_result_sql = tep_db_query($paypalec_payment_select_sql);
                if (tep_db_num_rows($paypalec_payment_result_sql)) {
                    tep_db_perform(TABLE_PAYPALEC, $paypalec_data_array, 'update', "paypal_order_id = '" . $orderId . "'");
                } else {
                    $paypalec_data_array['paypal_order_id'] = $orderId;
                    $paypalec_data_array['date_added'] = 'now()';
                    tep_db_perform(TABLE_PAYPALEC, $paypalec_data_array);
                }
                if ($this->ConfirmPayment($OrderAmt, $paypalECCurrency, $_SESSION['paypalToken'], $_SESSION['PayerID'])) {
                    $this->successRedirect();
                } else {
                    $this->errorRedirect();
                }
                break;
            case 'referenceTransactionCC':
            case 'referenceTransaction':
                if (!isset($_SESSION['billlingID'])) {
                    $_SESSION['billlingID'] = $this->IdentifyReferenceTransactionCustomer($customer_id);
                }
                $getPayerInfoArray = $this->BAUpdate($_SESSION['billlingID'], '', $orderId);
                if (!empty($getPayerInfoArray)) {
                    $paypalec_data_array = array(
                        'address_name' => isset($getPayerInfoArray['BILLINGNAME']) ? $getPayerInfoArray['BILLINGNAME'] : '',
                        'address_street' => isset($getPayerInfoArray['STREET']) ? $getPayerInfoArray['STREET'] : '',
                        'address_city' => isset($getPayerInfoArray['CITY']) ? $getPayerInfoArray['CITY'] : '',
                        'address_state' => isset($getPayerInfoArray['STATE']) ? $getPayerInfoArray['STATE'] : '',
                        'address_zip' => isset($getPayerInfoArray['ZIP']) ? $getPayerInfoArray['ZIP'] : '',
                        'address_country' => isset($getPayerInfoArray['COUNTRY']) ? $getPayerInfoArray['COUNTRY'] : '',
                        'address_status' => isset($getPayerInfoArray['ADDRESSSTATUS']) ? $getPayerInfoArray['ADDRESSSTATUS'] : '',
                    );
                    $paypalec_payment_select_sql = "	SELECT paypal_order_id
														FROM " . TABLE_PAYPALEC . "
														WHERE paypal_order_id = '" . (int) $orderId . "'";
                    $paypalec_payment_result_sql = tep_db_query($paypalec_payment_select_sql);
                    if (tep_db_num_rows($paypalec_payment_result_sql)) {
                        tep_db_perform(TABLE_PAYPALEC, $paypalec_data_array, 'update', "paypal_order_id = '" . $orderId . "'");
                    } else {
                        $paypalec_data_array['paypal_order_id'] = $orderId;
                        $paypalec_data_array['date_added'] = 'now()';
                        tep_db_perform(TABLE_PAYPALEC, $paypalec_data_array);
                    }
                }
                if ($this->DoReferenceTransaction($_SESSION['billlingID'], $OrderAmt, $paypalECCurrency, $OFFGAMERS, $orderId)) {
                    $this->successRedirect();
                } else {
                    $this->errorRedirect();
                }
                break;
        }
    }

    private function clearPaypalSession() {
        unset($_SESSION['paypalToken']);
        unset($_SESSION['PayerID']);
        unset($_SESSION['billlingID']);
        unset($_SESSION['confirmation_order_id']);
    }

    private function successRedirect() {
        $this->clearPaypalSession();
        tep_redirect($this->return_url);
        exit;
    }

    private function errorRedirect() {
        $this->clearPaypalSession();
        tep_redirect(tep_href_link(FILENAME_CHECKOUT_PAYMENT, 'error_message=' . MODULE_PAYMENT_PAYPALEC_TEXT_ERROR_MESSAGE, 'SSL', true, false));
        exit;
    }

    function after_process() {
        return false;
    }

    function link_to_payment($new_order_id) {
        global $order_logged, $customer_id;

        if (tep_session_is_registered('order_logged'))
            tep_session_unregister('order_logged');

        tep_session_register('order_logged');
        $order_logged = $new_order_id;
        $billingAgreement = '';
        if ($this->code == 'referenceTransaction' || $this->code == 'referenceTransactionCC') {
            $billingAgreement = $this->IdentifyReferenceTransactionCustomer($customer_id);
            if (!empty($billingAgreement)) {
                $_SESSION['billlingID'] = $billingAgreement;
            }
        }
        if (isset($_SESSION['billlingID']) && !empty($_SESSION['billlingID']) && ($this->code == 'referenceTransaction' || $this->code == 'referenceTransactionCC')) {
            tep_redirect(tep_href_link(FILENAME_PAYMENT_CONFIRMATION, '', true, false));
            exit;
        } else {
            require(DIR_WS_INCLUDES . 'modules/payment/paypalEC/catalog/paypalEC_splash.php');
        }
        return;
    }

    function is_supported_currency($selected_currency) {
        return (in_array($selected_currency, $this->paypalECCurrencies) ? true : false);
    }

    function output_error() {
        return false;
    }

    function IdentifyReferenceTransactionCustomer($customer_id) {
        $billingAgreement = '';
        $this->get_merchant_account($_SESSION['currency']);

        $billing_id_select = "	SELECT billing_id 
								FROM " . TABLE_PAYPALEC_REFERENCE_TRANSACTION . " 
								WHERE customer_id = '" . tep_db_input($customer_id) . "'
									AND merchant_email = '" . $this->receiverEmail . "'
								ORDER BY date desc
								LIMIT 1";
        $billing_id_result = tep_db_query($billing_id_select);
        if ($billing_id_row = tep_db_fetch_array($billing_id_result)) {
            $billingAgreement = $billing_id_row['billing_id'];
        }
        //query for customer with billing agreement
        return $billingAgreement;
    }

    public function refundAPI($type, $transId, $memo, $amount = 0, $currency = '') {
        $paypalec_trans_info_select_sql = "	SELECT receiver_email
                                            FROM " . TABLE_PAYPALEC . "
                                            WHERE txn_id ='" . $transId . "'";
        $paypalec_trans_info_result_sql = tep_db_query($paypalec_trans_info_select_sql);
        $paypalec_trans_info_row = tep_db_fetch_array($paypalec_trans_info_result_sql);
        if ($paypalec_trans_info_row['receiver_email'] == G2G_PAYPAL_RECEIVER_EMAIL) {
            $this->receiverEmail = G2G_PAYPAL_RECEIVER_EMAIL;
            $this->apiUsername = G2G_PAYPAL_USERNAME;
            $this->apiPassword = G2G_PAYPAL_API_PASSWORD;
            $this->apiSignature = G2G_PAYPAL_API_SIGNATURE;
        }
        $nvpStr = '&TRANSACTIONID=' . $transId . '&NOTE=' . $memo;
        if ($type == 'Partial') {
            $nvpStr .= '&AMT=' . $amount . '&CURRENCYCODE=' . $currency;
        }
        $nvpStr .= '&REFUNDTYPE=' . $type;
        $resArray = $this->hash_call("RefundTransaction", $nvpStr);
        $ack = strtoupper($resArray["ACK"]);
        if (empty($resArray) || $this->test_mode || $ack != 'SUCCESS') {
            ob_start();
            echo "=========================Request===========================<BR>";
            print_r($nvpStr);
            echo "========================================================<BR>";
            echo "=========================Response==========================<BR>";
            print_r($resArray);
            echo "========================================================<BR>";
            $debug_html = ob_get_contents();
            ob_end_clean();
            @tep_mail('PaypalEC RefundTransaction Report', '<EMAIL>', 'PaypalEC RefundTransaction', $debug_html, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
        }
        return $resArray;
    }

    private function BAUpdate($refId, $toStatus = '', $orderId = '') {
        $nvpStr = '&REFERENCEID=' . $refId;
        if (!empty($toStatus)) {
            $nvpStr .= '&BILLINGAGREEMENTSTATUS=' . $toStatus; //pass status = "Canceled" to terminate agreement
        } else if (!empty($orderId)) {
            $nvpStr .= '&L_BILLINGAGREEMENTDESCRIPTION0=' . 'Purchase from ' . STORE_NAME . ' #' . $orderId;
        }
        $resArray = $this->hash_call("BillAgreementUpdate", $nvpStr);
        $ack = strtoupper($resArray["ACK"]);
        if (empty($resArray) || $this->test_mode || $ack != 'SUCCESS') {
            ob_start();
            echo "=========================Request===========================<BR>";
            print_r($nvpStr);
            echo "========================================================<BR>";
            echo "=========================Response==========================<BR>";
            print_r($resArray);
            echo "========================================================<BR>";
            $debug_html = ob_get_contents();
            ob_end_clean();
            @tep_mail('PaypalEC BillAgreementUpdate Report', '<EMAIL>', 'PaypalEC BillAgreementUpdate', $debug_html, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
        }
        if ($ack == 'SUCCESS') {
            return $resArray;
        } else {
            return array();
        }
    }

    public function massPay($pass_id) {
        if (!tep_not_null($pass_id) || count($pass_id) == 0)
            return 0;

        $return_result_array = array();
        foreach ($pass_id as $store_payments_id_loop) {
            $return_result_array[$store_payments_id_loop] = 0;
        }

        $store_payments_array = array();
        $payments_methods_id_array = array();
        $store_payments_select_sql = "	SELECT sp.store_payments_id, sp.store_payments_request_currency, sp.store_payments_paid_currency, sp.store_payments_paid_currency_value, sp.store_payments_methods_id, sp.store_payments_after_fees_amount 
										FROM " . TABLE_STORE_PAYMENTS . " AS sp 
										WHERE sp.store_payments_id IN ('" . implode("','", $pass_id) . "')
											AND sp.store_payments_lock = '0'
											AND sp.store_payments_status = '2'";  // only those in processing status and not locked order
        $store_payments_result_sql = tep_db_query($store_payments_select_sql);
        while ($store_payments_row = tep_db_fetch_array($store_payments_result_sql)) {
            $store_payments_currency_array[$store_payments_row['store_payments_methods_id']][$store_payments_row['store_payments_paid_currency']][] = $store_payments_row['store_payments_id'];
            $actual_payout_amount = 0;
            if ($store_payments_row['store_payments_request_currency'] != $store_payments_row['store_payments_paid_currency']) {
                $latest_ex_rate = $store_payments_row['store_payments_paid_currency_value'];
                if (is_numeric($latest_ex_rate) && $latest_ex_rate > 0) {
                    $latest_ex_rate = (double) $latest_ex_rate;
                    $actual_payout_amount = $latest_ex_rate * $store_payments_row['store_payments_after_fees_amount'];
                } else {
                    continue;
                }
            } else {
                $actual_payout_amount = $store_payments_row['store_payments_after_fees_amount'];
            }
            $store_payments_array[$store_payments_row['store_payments_methods_id']][$store_payments_row['store_payments_id']]['amount'] = number_format($actual_payout_amount, 2, ".", "");
        }

        $payments_methods_fields_array = array();
        if (count($store_payments_array)) {
            foreach ($store_payments_array as $store_payments_methods_id_loop => $store_payments_data_loop) {
                //$store_payments_methods_data_loop // store payments id
                $payment_methods_fields_select_sql = "	SELECT pmf.payment_methods_id, pmf.payment_methods_fields_title, pmf.payment_methods_fields_id, pmf.payment_methods_fields_system_type 
														FROM " . TABLE_PAYMENT_METHODS_FIELDS . " AS pmf
														WHERE pmf.payment_methods_id = '" . $store_payments_methods_id_loop . "'
															AND payment_methods_fields_system_type = 'MODULE_PAYPALEC_SEND_EMAIL'";
                $payment_methods_fields_result_sql = tep_db_query($payment_methods_fields_select_sql);
                if (!tep_db_num_rows($payment_methods_fields_result_sql)) {
                    $payment_methods_fields_select_sql = "	SELECT pmf.payment_methods_id, pmf.payment_methods_fields_title, pmf.payment_methods_fields_id, pmf.payment_methods_fields_system_type 
															FROM " . TABLE_PAYMENT_METHODS_FIELDS . " AS pmf
															LEFT JOIN " . TABLE_PAYMENT_METHODS . " AS pm
																ON pm.payment_methods_parent_id = pmf.payment_methods_id
															WHERE pm.payment_methods_id = '" . $store_payments_methods_id_loop . "'
																AND payment_methods_fields_system_type = 'MODULE_PAYPALEC_SEND_EMAIL'";
                    $payment_methods_fields_result_sql = tep_db_query($payment_methods_fields_select_sql);
                }

                if ($payment_methods_fields_row = tep_db_fetch_array($payment_methods_fields_result_sql)) {
                    $payments_methods_id_array[$store_payments_methods_id_loop] = $payment_methods_fields_row['payment_methods_fields_id'];
                }
            }

            foreach ($store_payments_array as $store_payments_methods_id_loop => $store_payments_data_array_loop) {
                foreach ($store_payments_data_array_loop as $store_payments_id_loop => $store_payments_data_loop) {
                    $store_payments_details_select_sql = "	SELECT payment_methods_fields_value 
															FROM " . TABLE_STORE_PAYMENTS_DETAILS . " AS spd 
															WHERE spd.store_payments_id = '" . (int) $store_payments_id_loop . "'
																AND spd.payment_methods_fields_id = '" . (int) $payments_methods_id_array[$store_payments_methods_id_loop] . "'";
                    $store_payments_details_result_sql = tep_db_query($store_payments_details_select_sql);
                    while ($store_payments_details_row = tep_db_fetch_array($store_payments_details_result_sql)) {
                        $store_payments_array[$store_payments_methods_id_loop][$store_payments_id_loop]['email'] = $store_payments_details_row['payment_methods_fields_value'];
                    }
                }
            }
            foreach ($store_payments_currency_array as $payment_methods_id_loop => $store_payments_currency_array_loop) {
                foreach ($store_payments_currency_array_loop as $store_payments_currency_loop => $store_payments_data_array_loop) {
                    $this->get_merchant_account($store_payments_currency_loop);

                    if (isset($store_payments_array[$payment_methods_id_loop]) && count($store_payments_array[$payment_methods_id_loop])) {
                        $curl_submit_array = array();
                        $curl_submit_count = 0;
                        $batch_count = 0;
                        $submitted_array = array();
                        foreach ($store_payments_array[$payment_methods_id_loop] as $store_payments_id_array => $store_payments_data_array) {
                            if ($batch_count > 255) {
                                $curl_submit_count++;
                                $batch_count = 0;
                            }
                            $submitted_array[$curl_submit_count][$batch_count] = $store_payments_id_array;
                            $curl_str = '';
                            foreach ($store_payments_data_array as $store_payments_key_loop => $store_payments_data_loop) {
                                switch ($store_payments_key_loop) {
                                    case 'email':
                                        $curl_submit_array[$curl_submit_count]['store_payment'][$store_payments_id_array]['email'] = $store_payments_data_loop;
                                        $curl_str .= "L_EMAIL$batch_count=" . urlencode($store_payments_data_loop) . "&";
                                        break;
                                    case 'amount':
                                        $curl_submit_array[$curl_submit_count]['store_payment'][$store_payments_id_array]['amount'] = $store_payments_data_loop;
                                        $curl_str .= "L_AMT$batch_count=" . urlencode($store_payments_data_loop) . "&";
                                        break;
                                }
                            }
                            $curl_submit_array[$curl_submit_count]['data'] .= "L_UNIQUEID$batch_count=" . urlencode($store_payments_id_array) . "&" . $curl_str;
                            $batch_count++;
                        }
                        foreach ($curl_submit_array as $curl_submit_count_loop => $curl_submit_data_loop) {

                            $nvpResArray = $this->hash_call('MassPay', '&' . $curl_submit_data_loop['data'] . "CURRENCYCODE=" . urlencode($store_payments_currency_loop));

                            $returned_nvp_str = '';
                            $returned_sp_nvp_array = array();
                            if (count($nvpResArray)) {
                                foreach ($nvpResArray as $nvp_res_key_loop => $nvp_res_data_loop) {
                                    switch ($nvp_res_key_loop) {
                                        case 'TIMESTAMP':
                                        case 'CORRELATIONID':
                                        case 'ACK':
                                        case 'VERSION':
                                        case 'BUILD':
                                            $returned_nvp_str .= $nvp_res_key_loop . ': ' . $nvp_res_data_loop . '</br>';
                                            break;
                                        default:
                                            if (preg_match("/L_([A-Z]+)([0-9]+)$/i", $nvp_res_key_loop, $matched_key) && count($matched_key) == 3) {
                                                $returned_sp_nvp_array[$submitted_array[$curl_submit_count_loop][$matched_key[2]]][$matched_key[1]] = $nvp_res_data_loop;
                                            } else {
                                                $returned_nvp_str .= $nvp_res_key_loop . ': ' . $nvp_res_data_loop . '</br>';
                                            }
                                            break;
                                    }
                                }
                            }

                            foreach ($curl_submit_data_loop['store_payment'] as $store_payment_id_loop => $store_payment_data_loop) {
                                $payment_update_sql_data_array = array();
                                $payment_history_sql_data_array = array();
                                $payment_history_sql_data_array['store_payments_id'] = tep_db_prepare_input($store_payment_id_loop);
                                $payment_history_sql_data_array['date_added'] = 'now()';
                                $payment_history_sql_data_array['payee_notified'] = '0';
                                $payment_history_sql_data_array['changed_by'] = tep_db_prepare_input($_SESSION['login_email_address']);
                                $payment_history_sql_data_array['changed_by_role'] = tep_db_prepare_input('admin');
                                $payment_history_sql_data_array['store_payments_status'] = '0';

                                if (isset($nvpResArray['ACK']) && strtolower($nvpResArray['ACK']) == 'success') {
                                    $return_result_array[$store_payment_id_loop] = 1;

                                    $payment_update_sql_data_array = array('store_payments_reference' => 'Correlation ID: ' . $nvpResArray['CORRELATIONID'],
                                        'store_payments_last_modified' => 'now()');
                                }

                                $payment_update_sql_data_array['store_payments_lock'] = 1;
                                tep_db_perform(TABLE_STORE_PAYMENTS, $payment_update_sql_data_array, 'update', "store_payments_id = '" . $store_payment_id_loop . "'");

                                $log_message = 'Recepient: ' . $store_payment_data_loop['email'] . '</br>';
                                $log_message .= 'Unique ID: ' . $store_payment_id_loop . '</br>';
                                $log_message .= 'Amount: ' . number_format($store_payment_data_loop['amount'], 2) . '</br>';
                                $log_message .= 'Currency: ' . $store_payments_currency_loop . '</br></br>';

                                $log_message .= '<u>MassPay Response:</u> </br>';
                                $log_message .= $returned_nvp_str;

                                if (isset($returned_sp_nvp_array[$store_payment_id_loop])) {
                                    foreach ($returned_sp_nvp_array[$store_payment_id_loop] as $returned_sp_nvp_key_loop => $returned_sp_nvp_data_loop) {
                                        $log_message .= $returned_sp_nvp_key_loop . ': ' . $returned_sp_nvp_data_loop . '</br>';
                                    }
                                }

                                $payment_history_sql_data_array['comments'] = tep_db_prepare_input($log_message);
                                tep_db_perform(TABLE_STORE_PAYMENTS_HISTORY, $payment_history_sql_data_array);
                            }
                        }
                    }
                }
            }
        }
        return $return_result_array;
    }

    public function getBalance($nvpStr) {
        return $this->hash_call('GetBalance', $nvpStr);
    }

    private function CallReferenceTransactionCheckout($currencyCodeType, $orderId) {
        //------------------------------------------------------------------------------------------------------------------------------------
        // Construct the parameter string that describes the SetExpressCheckout API call in the shortcut implementation
//		$nvpstr = "&PAYMENTREQUEST_0_AMT=0";
//		$nvpstr = $nvpstr . "&PAYMENTREQUEST_0_CURRENCYCODE=" . $currencyCodeType;
        $nvpstr = "&PAYMENTREQUEST_0_PAYMENTACTION=Authorization";
        $nvpstr = $nvpstr . "&PAYMENTREQUEST_0_NOTIFYURL=" . $this->notify_url;
        $nvpstr = $nvpstr . "&L_PAYMENTTYPE0=Any";
        $nvpstr = $nvpstr . "&L_BILLINGTYPE0=MerchantInitiatedBilling";
        $nvpstr = $nvpstr . "&L_BILLINGAGREEMENTDESCRIPTION0=This agreement page will appear only once during your first time checkout for future express checkout purpose. Thank you.";
        $nvpstr = $nvpstr . "&RETURNURL=" . $this->return_url;
        $nvpstr = $nvpstr . "&CANCELURL=" . $this->cancel_url;
//		$nvpstr = $nvpstr . "&PAYMENTREQUEST_0_TRANSACTIONID=" . $orderId; //Order ID
//		$nvpstr = $nvpstr . "&PAYMENTREQUEST_0_ITEMAMT=" . $paymentAmount;
        $nvpstr = $nvpstr . "&L_PAYMENTREQUEST_0_NAME0=" . 'Purchase from ' . STORE_NAME . ' #' . $orderId;
        $nvpstr = $nvpstr . "&PAYMENTREQUEST_0_INVNUM=" . $orderId; //Order ID
//		$nvpstr = $nvpstr . "&L_PAYMENTREQUEST_0_AMT0=" . $paymentAmount;
        //'--------------------------------------------------------------------------------------------------------------- 
        //' Make the API call to PayPal
        //' If the API call succeded, then redirect the buyer to PayPal to begin to authorize payment.  
        //' If an error occured, show the resulting errors
        //'---------------------------------------------------------------------------------------------------------------
        $resArray = $this->hash_call("SetExpressCheckout", $nvpstr);
        $ack = strtoupper($resArray["ACK"]);
        if (!empty($resArray) || $this->test_mode || $ack != "SUCCESS") {
            ob_start();
            echo "=========================Request===========================<BR>";
            print_r($nvpstr);
            echo "========================================================<BR>";
            echo "=========================Response==========================<BR>";
            print_r($resArray);
            echo "========================================================<BR>";
            $debug_html = ob_get_contents();
            ob_end_clean();
            @tep_mail('PaypalEC CallReferenceTransactionCheckout Report', '<EMAIL>', 'PaypalEC CallReferenceTransactionCheckout', $debug_html, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
        }
        return $resArray;
    }

    /* An express checkout transaction starts with a token, that
      identifies to PayPal your transaction
      In this example, when the script sees a token, the script
      knows that the buyer has already authorized payment through
      paypal.  If no token was found, the action is to send the buyer
      to PayPal to first authorize payment
     */

    /*
      '-------------------------------------------------------------------------------------------------------------------------------------------
      ' Purpose: 	Prepares the parameters for the SetExpressCheckout API Call.
      ' Inputs:
      '		paymentAmount:  	Total value of the shopping cart
      '		currencyCodeType: 	Currency code value the PayPal API
      '		paymentType: 		paymentType has to be one of the following values: Sale or Order or Authorization
      '		returnURL:			the page where buyers return to after they are done with the payment review on PayPal
      '		cancelURL:			the page where buyers return to when they cancel the payment review on PayPal
      '--------------------------------------------------------------------------------------------------------------------------------------------
     */

    private function CallShortcutExpressCheckout($paymentAmount, $currencyCodeType, $orderId, $landingPage = 'Login') {
        //------------------------------------------------------------------------------------------------------------------------------------
        // Construct the parameter string that describes the SetExpressCheckout API call in the shortcut implementation

        $nvpstr = "&PAYMENTREQUEST_0_AMT=" . $paymentAmount;
        $nvpstr = $nvpstr . "&PAYMENTREQUEST_0_PAYMENTACTION=Sale";
        $nvpstr = $nvpstr . "&PAYMENTREQUEST_0_NOTIFYURL=" . $this->notify_url;
        $nvpstr = $nvpstr . "&RETURNURL=" . $this->return_url;
        $nvpstr = $nvpstr . "&CANCELURL=" . $this->cancel_url;
        $nvpstr = $nvpstr . "&PAYMENTREQUEST_0_CURRENCYCODE=" . $currencyCodeType;
        $nvpstr = $nvpstr . "&PAYMENTREQUEST_0_CUSTOM=" . $orderId; //Order ID
        $nvpstr = $nvpstr . "&PAYMENTREQUEST_0_INVNUM=" . $orderId; //Order ID
//		$nvpstr = $nvpstr . "&PAYMENTREQUEST_0_TRANSACTIONID=" . $orderId; //Order ID
        $nvpstr = $nvpstr . "&L_PAYMENTREQUEST_0_NAME0=" . 'Purchase from ' . STORE_NAME . ' #' . $orderId;
        $nvpstr = $nvpstr . "&L_PAYMENTREQUEST_0_AMT0=" . $paymentAmount;
        if ($landingPage == 'Login') {
            $nvpstr = $nvpstr . "&LANDINGPAGE=Login";
        } else if ($landingPage == 'Billing') {
            $nvpstr = $nvpstr . "&LANDINGPAGE=BILLING";
            $nvpstr = $nvpstr . "&SOLUTIONTYPE=SOLE";
        }
        if ($this->code == 'PaypalECDigitalGoods') {
            $nvpstr = $nvpstr . "&L_PAYMENTREQUEST_0_ITEMCATEGORY0=Digital";
            $nvpstr = $nvpstr . "&NOSHIPPING=1"; //For DG goods, no need shipping info
            $nvpstr = $nvpstr . "&REQCONFIRMSHIPPING=0";
            $nvpstr = $nvpstr . "&PAYMENTREQUEST_0_AMT=" . $paymentAmount;
        }
        //'--------------------------------------------------------------------------------------------------------------- 
        //' Make the API call to PayPal
        //' If the API call succeded, then redirect the buyer to PayPal to begin to authorize payment.  
        //' If an error occured, show the resulting errors
        //'---------------------------------------------------------------------------------------------------------------
        $resArray = $this->hash_call("SetExpressCheckout", $nvpstr);

        $ack = strtoupper($resArray["ACK"]);
        if (empty($resArray) || $this->test_mode || $ack != "SUCCESS") {
            ob_start();
            echo "=========================Request===========================<BR>";
            print_r($nvpstr);
            echo "========================================================<BR>";
            echo "=========================Response==========================<BR>";
            print_r($resArray);
            echo "========================================================<BR>";
            $debug_html = ob_get_contents();
            ob_end_clean();
            @tep_mail('PaypalEC SetExpressCheckout Report', '<EMAIL>', 'PaypalEC SetExpressCheckout', $debug_html, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
        }
        return $resArray;
    }

    /*
      '-------------------------------------------------------------------------------------------------------------------------------------------
      ' Purpose: 	Prepares the parameters for the SetExpressCheckout API Call.
      ' Inputs:
      '		paymentAmount:  	Total value of the shopping cart
      '		currencyCodeType: 	Currency code value the PayPal API
      '		paymentType: 		paymentType has to be one of the following values: Sale or Order or Authorization
      '		returnURL:			the page where buyers return to after they are done with the payment review on PayPal
      '		cancelURL:			the page where buyers return to when they cancel the payment review on PayPal
      '		shipToName:		the Ship to name entered on the merchant's site
      '		shipToStreet:		the Ship to Street entered on the merchant's site
      '		shipToCity:			the Ship to City entered on the merchant's site
      '		shipToState:		the Ship to State entered on the merchant's site
      '		shipToCountryCode:	the Code for Ship to Country entered on the merchant's site
      '		shipToZip:			the Ship to ZipCode entered on the merchant's site
      '		shipToStreet2:		the Ship to Street2 entered on the merchant's site
      '		phoneNum:			the phoneNum  entered on the merchant's site
      '--------------------------------------------------------------------------------------------------------------------------------------------
     */

    private function CallMarkExpressCheckout($paymentAmount, $currencyCodeType, $shipToName, $shipToStreet, $shipToCity, $shipToState, $shipToCountryCode, $shipToZip, $shipToStreet2, $phoneNum
    ) {
        //------------------------------------------------------------------------------------------------------------------------------------
        // Construct the parameter string that describes the SetExpressCheckout API call in the shortcut implementation

        $nvpstr = "&PAYMENTREQUEST_0_AMT=" . $paymentAmount;
        $nvpstr = $nvpstr . "&PAYMENTREQUEST_0_PAYMENTACTION=Sale";
        $nvpstr = $nvpstr . "&PAYMENTREQUEST_0_NOTIFYURL=" . $this->notify_url;
        $nvpstr = $nvpstr . "&RETURNURL=" . $this->return_url;
        $nvpstr = $nvpstr . "&CANCELURL=" . $this->cancel_url;
        $nvpstr = $nvpstr . "&PAYMENTREQUEST_0_CURRENCYCODE=" . $currencyCodeType;
        $nvpstr = $nvpstr . "&ADDROVERRIDE=1";
        $nvpstr = $nvpstr . "&PAYMENTREQUEST_0_SHIPTONAME=" . $shipToName;
        $nvpstr = $nvpstr . "&PAYMENTREQUEST_0_SHIPTOSTREET=" . $shipToStreet;
        $nvpstr = $nvpstr . "&PAYMENTREQUEST_0_SHIPTOSTREET2=" . $shipToStreet2;
        $nvpstr = $nvpstr . "&PAYMENTREQUEST_0_SHIPTOCITY=" . $shipToCity;
        $nvpstr = $nvpstr . "&PAYMENTREQUEST_0_SHIPTOSTATE=" . $shipToState;
        $nvpstr = $nvpstr . "&PAYMENTREQUEST_0_SHIPTOCOUNTRYCODE=" . $shipToCountryCode;
        $nvpstr = $nvpstr . "&PAYMENTREQUEST_0_SHIPTOZIP=" . $shipToZip;
        $nvpstr = $nvpstr . "&PAYMENTREQUEST_0_SHIPTOPHONENUM=" . $phoneNum;
        $nvpstr = $nvpstr . "&PAYMENTREQUEST_0_CUSTOM=" . $orderId; //Order ID
//		$nvpstr = $nvpstr . "&PAYMENTREQUEST_0_TRANSACTIONID=" . $orderId; //Order ID - returned after DoExpressCheckout
        $nvpstr = $nvpstr . "&PAYMENTREQUEST_0_INVNUM=" . $orderId; //Order ID
        $nvpstr = $nvpstr . "&PAYMENTREQUEST_0_DESC=" . 'Purchase from ' . STORE_NAME . ' #' . $orderId;
//		$nvpstr = $nvpstr . "&PAYMENTREQUEST_0_ITEMAMT=" . $paymentAmount;
        $nvpstr = $nvpstr . "&L_PAYMENTREQUEST_0_NAME0=" . 'Purchase from ' . STORE_NAME . ' #' . $orderId;
        $nvpstr = $nvpstr . "&L_PAYMENTREQUEST_0_AMT0=" . $paymentAmount;

        //'--------------------------------------------------------------------------------------------------------------- 
        //' Make the API call to PayPal
        //' If the API call succeded, then redirect the buyer to PayPal to begin to authorize payment.  
        //' If an error occured, show the resulting errors
        //'---------------------------------------------------------------------------------------------------------------
        $resArray = $this->hash_call("SetExpressCheckout", $nvpstr);

        return $resArray;
    }

    /*
      '-------------------------------------------------------------------------------------------
      ' Purpose: 	Prepares the parameters for the GetExpressCheckoutDetails API Call.
      '
      ' Inputs:
      '		None
      ' Returns:
      '		The NVP Collection object of the GetExpressCheckoutDetails Call Response.
      '-------------------------------------------------------------------------------------------
     */

    private function GetShippingDetails($token) {
        //'--------------------------------------------------------------
        //' At this point, the buyer has completed authorizing the payment
        //' at PayPal.  The function will call PayPal to obtain the details
        //' of the authorization, incuding any shipping information of the
        //' buyer.  Remember, the authorization is not a completed transaction
        //' at this state - the buyer still needs an additional step to finalize
        //' the transaction
        //'--------------------------------------------------------------
        //'---------------------------------------------------------------------------
        //' Build a second API request to PayPal, using the token as the
        //'  ID to get the details on the payment authorization
        //'---------------------------------------------------------------------------
        $nvpstr = "&TOKEN=" . $token;

        //'---------------------------------------------------------------------------
        //' Make the API call and store the results in an array.  
        //'	If the call was a success, show the authorization details, and provide
        //' 	an action to complete the payment.  
        //'	If failed, show the error
        //'---------------------------------------------------------------------------
        $resArray = $this->hash_call("GetExpressCheckoutDetails", $nvpstr);

        return $resArray;
    }

    private function CreateBillingAgreement($token, $customer_id, $order_id) {
        $nvpstr = "&TOKEN=" . $token;
        $resArray = $this->hash_call("CreateBillingAgreement", $nvpstr);
        $ack = strtoupper($resArray["ACK"]);
        if (empty($resArray) || $this->test_mode || $ack != 'SUCCESS') {
            ob_start();
            echo "=========================Request===========================<BR>";
            print_r($nvpstr);
            echo "========================================================<BR>";
            echo "=========================Response==========================<BR>";
            print_r($resArray);
            echo "========================================================<BR>";
            $debug_html = ob_get_contents();
            ob_end_clean();
            @tep_mail('PaypalEC CreateBillingAgreement Report', '<EMAIL>', 'PaypalEC CreateBillingAgreement', $debug_html, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
        }

        if (($ack == "SUCCESS" || $ack == "SUCCESSWITHWARNING") && isset($resArray["BILLINGAGREEMENTID"]) && !empty($resArray["BILLINGAGREEMENTID"])) {
            $billing_data_array = array(
                'billing_id' => tep_db_prepare_input($resArray["BILLINGAGREEMENTID"]),
                'date' => 'now()'
            );

            $billing_select_sql = "	SELECT customer_id
									FROM " . TABLE_PAYPALEC_REFERENCE_TRANSACTION . "
									WHERE billing_id = '" . tep_db_input($resArray["BILLINGAGREEMENTID"]) . "'
										AND customer_id = '" . tep_db_input($customer_id) . "'
										AND merchant_email = '" . $this->receiverEmail . "'";
            $billing_result_sql = tep_db_query($billing_select_sql);
            if (tep_db_num_rows($billing_result_sql)) {
                tep_db_perform(TABLE_PAYPALEC_REFERENCE_TRANSACTION, $billing_data_array, 'update', "customer_id = '" . tep_db_prepare_input($customer_id) . "' AND billing_id = '" . tep_db_prepare_input($resArray["BILLINGAGREEMENTID"]) . "'  AND merchant_email = '" . tep_db_prepare_input($this->receiverEmail) . "' ");
            } else {
                $billing_data_array['customer_id'] = tep_db_prepare_input($customer_id);
                $billing_data_array['merchant_email'] = $this->receiverEmail;
                tep_db_perform(TABLE_PAYPALEC_REFERENCE_TRANSACTION, $billing_data_array);
            }

            $reference_transaction_history_data_array = array('billing_id' => tep_db_prepare_input($resArray["BILLINGAGREEMENTID"]),
                'customer_id' => tep_db_prepare_input($customer_id),
                'order_id' => tep_db_prepare_input($order_id),
                'date' => 'now()',
                'status' => 'Billing Binding : ' . $customer_id . ' - ' . $resArray["BILLINGAGREEMENTID"],
                'data' => json_encode($resArray)
            );
            tep_db_perform(TABLE_PAYPALEC_REFERENCE_TRANSACTION_HISTORY, $reference_transaction_history_data_array);
            return $resArray["BILLINGAGREEMENTID"];
        }
        return false;
    }

    private function DoReferenceTransaction($billingAgreementID, $OrderAmt, $paypalECCurrency, $sessionID, $orderID) {
        //------------------------------------------------------------------------------------------------------------------------------------
        // Construct the parameter string that describes the DoExpressCheckout API call in the shortcut implementation
        $ip = tep_get_ip_address();
        if ($ip == 'unknown') {
            $ip = '';
        }
        $nvpstr = "&AMT=" . $OrderAmt;
        $nvpstr = $nvpstr . "&CURRENCYCODE=" . $paypalECCurrency;
        $nvpstr = $nvpstr . "&PAYMENTACTION=Sale";
        $nvpstr = $nvpstr . "&PAYMENTTYPE=Any";  //to support all type of funding source
        $nvpstr = $nvpstr . "&REFERENCEID=" . $billingAgreementID;
        $nvpstr = $nvpstr . "&IPADDRESS=" . $ip;
        $nvpstr = $nvpstr . "&INVNUM=" . $orderID;
        $nvpstr = $nvpstr . "&MERCHANTSESSIONID=" . $sessionID;
        $nvpstr = $nvpstr . "&CUSTOM=" . $orderID; //Order ID
        $nvpstr = $nvpstr . "&NOTIFYURL=" . $this->notify_url;
        $nvpstr = $nvpstr . "&DESC=" . 'Purchase from ' . STORE_NAME . ' #' . $orderID; //Order ID
//		$nvpstr = $nvpstr . "&TRANSACTIONID=" . $orderID;
//		$nvpstr = $nvpstr . "&RETURNFMFDETAILS=1";
        //'--------------------------------------------------------------------------------------------------------------- 
        //' Make the API call to PayPal
        //' If the API call succeded, then redirect the buyer to PayPal to begin to authorize payment.  
        //' If an error occured, show the resulting errors
        //'---------------------------------------------------------------------------------------------------------------
        $resArray = $this->hash_call("DoReferenceTransaction", $nvpstr);
        $ack = strtoupper($resArray["ACK"]);
        if (empty($resArray) || $this->test_mode || $ack != "SUCCESS") {
            ob_start();
            echo "=========================Request===========================<BR>";
            print_r($nvpstr);
            echo "========================================================<BR>";
            echo "=========================Response==========================<BR>";
            print_r($resArray);
            echo "========================================================<BR>";
            $debug_html = ob_get_contents();
            ob_end_clean();
            @tep_mail('PaypalEC DoReferenceTransaction Report', '<EMAIL>', 'PaypalEC DoReferenceTransaction', $debug_html, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
        }

        if ($ack == "SUCCESS" || $ack == "SUCCESSWITHWARNING") {
            return true;
        } else if ($resArray['PAYMENTSTATUS'] == 'Expired' || ($ack == "FAILURE" && $resArray['L_ERRORCODE0'] == '10201')) {
            $resArray = $this->CallReferenceTransactionCheckout($paypalECCurrency, $orderID);
            $ack = strtoupper($resArray["ACK"]);
            if ($ack == "SUCCESS" || $ack == "SUCCESSWITHWARNING") {
                $token = urldecode($resArray["TOKEN"]);
                echo 'Please wait...';
                $this->RedirectToPayPal($token);
            } else {
                if ($this->test_mode) {
                    //Display a user friendly Error on the page using any of the following error information returned by PayPal
                    $ErrorCode = urldecode($resArray["L_ERRORCODE0"]);
                    $ErrorShortMsg = urldecode($resArray["L_SHORTMESSAGE0"]);
                    $ErrorLongMsg = urldecode($resArray["L_LONGMESSAGE0"]);
                    $ErrorSeverityCode = urldecode($resArray["L_SEVERITYCODE0"]);

                    echo "SetExpressCheckout API call failed. ";
                    echo "Detailed Error Message: " . $ErrorLongMsg;
                    echo "Short Error Message: " . $ErrorShortMsg;
                    echo "Error Code: " . $ErrorCode;
                    echo "Error Severity Code: " . $ErrorSeverityCode;
                } else {
                    $this->errorRedirect();
                }
            }
        } else {
            //Display a user friendly Error on the page using any of the following error information returned by PayPal
            $ErrorCode = urldecode($resArray["L_ERRORCODE0"]);
            $ErrorShortMsg = urldecode($resArray["L_SHORTMESSAGE0"]);
            $ErrorLongMsg = urldecode($resArray["L_LONGMESSAGE0"]);
            $ErrorSeverityCode = urldecode($resArray["L_SEVERITYCODE0"]);

            echo "GetExpressCheckoutDetails API call failed. ";
            echo "Detailed Error Message: " . $ErrorLongMsg;
            echo "Short Error Message: " . $ErrorShortMsg;
            echo "Error Code: " . $ErrorCode;
            echo "Error Severity Code: " . $ErrorSeverityCode;
            return false;
        }
    }

    /*
      '-------------------------------------------------------------------------------------------------------------------------------------------
      ' Purpose: 	Prepares the parameters for the GetExpressCheckoutDetails API Call.
      '
      ' Inputs:
      '		sBNCode:	The BN code used by PayPal to track the transactions from a given shopping cart.
      ' Returns:
      '		The NVP Collection object of the GetExpressCheckoutDetails Call Response.
      '--------------------------------------------------------------------------------------------------------------------------------------------
     */

    private function ConfirmPayment($OrderAmt, $paypalECCurrency, $token, $payerID) {
        //Format the other parameters that were stored in the session from the previous calls	
        $nvpstr = '&TOKEN=' . $token . '&PAYERID=' . $payerID . '&PAYMENTREQUEST_0_PAYMENTACTION=Sale&PAYMENTREQUEST_0_AMT=' . $OrderAmt;
        $nvpstr .= '&PAYMENTREQUEST_0_CURRENCYCODE=' . $paypalECCurrency . '&PAYMENTREQUEST_0_NOTIFYURL=' . $this->notify_url;

        /* Make the call to PayPal to finalize payment
          If an error occured, show the resulting errors
         */
        $resArray = $this->hash_call("DoExpressCheckoutPayment", $nvpstr);
        $ack = strtoupper($resArray["ACK"]);
        if (empty($resArray) || $this->test_mode || $ack != "SUCCESS") {
            ob_start();
            echo "=========================Request===========================<BR>";
            print_r($nvpstr);
            echo "========================================================<BR>";
            echo "=========================Response==========================<BR>";
            print_r($resArray);
            echo "========================================================<BR>";
            $debug_html = ob_get_contents();
            ob_end_clean();
            @tep_mail('PaypalEC DoExpressCheckoutPayment Report', '<EMAIL>', 'PaypalEC DoExpressCheckoutPayment', $debug_html, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
        }
        $ack = strtoupper($resArray["ACK"]);

        if ($ack == "SUCCESS" || $ack == "SUCCESSWITHWARNING") {
//			$successPayment = array(
//				'token' => $resArray['TOKEN'],
//				'success_page_redirect' => $resArray['SUCCESSPAGEREDIRECTREQUESTED'],
//				'timestamp' => $resArray['TIMESTAMP'],
//				'correlation_id' => $resArray['CORRELATIONID'],
//				'ack' => $resArray['ACK'],
//				'version' => $resArray['VERSION'],
//				'build' => $resArray['BUILD'],
//				'insurance_option_selected' => $resArray['INSURANCEOPTIONSELECTED'],
//				'shipping_option_is_default' => $resArray['SHIPPINGOPTIONISDEFAULT'],
//				'transaction_id' => $resArray['PAYMENTINFO_0_TRANSACTIONID'],
//				'transaction_type' => $resArray['PAYMENTINFO_0_TRANSACTIONTYPE'],
//				'payment_type' => $resArray['PAYMENTINFO_0_PAYMENTTYPE'],
//				'order_time' => $resArray['PAYMENTINFO_0_ORDERTIME'],
//				'amount' => $resArray['PAYMENTINFO_0_AMT'],
//				'fee_amount' => $resArray['PAYMENTINFO_0_FEEAMT'],
//				'tax_amount' => $resArray['PAYMENTINFO_0_TAXAMT'],
//				'currency_code' => $resArray['PAYMENTINFO_0_CURRENCYCODE'],
//				'payment_status' => $resArray['PAYMENTINFO_0_PAYMENTSTATUS'],
//				'pending_reason' => $resArray['PAYMENTINFO_0_PENDINGREASON'],
//				'reason_code' => $resArray['PAYMENTINFO_0_REASONCODE'],
//				'protection_eligibility' => $resArray['PAYMENTINFO_0_PROTECTIONELIGIBILITY'],
//				'protection_eligibility_type' => $resArray['PAYMENTINFO_0_PROTECTIONELIGIBILITYTYPE'],
//				'merchant_account_id' => $resArray['PAYMENTINFO_0_SECUREMERCHANTACCOUNTID'],
//				'payment_info_error_code' => $resArray['PAYMENTINFO_0_ERRORCODE'],
//				'payment_info_ack' => $resArray['PAYMENTINFO_0_ACK'],
//			);
            return true;
        } else {
            //Display a user friendly Error on the page using any of the following error information returned by PayPal
            $ErrorCode = urldecode($resArray["L_ERRORCODE0"]);
            $ErrorShortMsg = urldecode($resArray["L_SHORTMESSAGE0"]);
            $ErrorLongMsg = urldecode($resArray["L_LONGMESSAGE0"]);
            $ErrorSeverityCode = urldecode($resArray["L_SEVERITYCODE0"]);

            echo "GetExpressCheckoutDetails API call failed. ";
            echo "Detailed Error Message: " . $ErrorLongMsg;
            echo "Short Error Message: " . $ErrorShortMsg;
            echo "Error Code: " . $ErrorCode;
            echo "Error Severity Code: " . $ErrorSeverityCode;
            return false;
        }
    }

    /*
      '-------------------------------------------------------------------------------------------------------------------------------------------
      ' Purpose: 	This function makes a DoDirectPayment API call
      '
      ' Inputs:
      '		paymentType:		paymentType has to be one of the following values: Sale or Order or Authorization
      '		paymentAmount:  	total value of the shopping cart
      '		currencyCode:	 	currency code value the PayPal API
      '		firstName:			first name as it appears on credit card
      '		lastName:			last name as it appears on credit card
      '		street:				buyer's street address line as it appears on credit card
      '		city:				buyer's city
      '		state:				buyer's state
      '		countryCode:		buyer's country code
      '		zip:				buyer's zip
      '		creditCardType:		buyer's credit card type (i.e. Visa, MasterCard ... )
      '		creditCardNumber:	buyers credit card number without any spaces, dashes or any other characters
      '		expDate:			credit card expiration date
      '		cvv2:				Card Verification Value
      '
      '-------------------------------------------------------------------------------------------
      '
      ' Returns:
      '		The NVP Collection object of the DoDirectPayment Call Response.
      '--------------------------------------------------------------------------------------------------------------------------------------------
     */

//	private function DirectPayment($paymentAmount, $creditCardType, $creditCardNumber, $expDate, $cvv2, $firstName, $lastName, $street, $city, $state, $zip, $countryCode, $currencyCode) {
//		//Construct the parameter string that describes DoDirectPayment
//		$nvpstr = "&AMT=" . $paymentAmount;
//		$nvpstr = $nvpstr . "&CURRENCYCODE=" . $currencyCode;
//		$nvpstr = $nvpstr . "&PAYMENTACTION=Sale";
//		$nvpstr = $nvpstr . "&CREDITCARDTYPE=" . $creditCardType;
//		$nvpstr = $nvpstr . "&ACCT=" . $creditCardNumber;
//		$nvpstr = $nvpstr . "&EXPDATE=" . $expDate;
//		$nvpstr = $nvpstr . "&CVV2=" . $cvv2;
//		$nvpstr = $nvpstr . "&FIRSTNAME=" . $firstName;
//		$nvpstr = $nvpstr . "&LASTNAME=" . $lastName;
//		$nvpstr = $nvpstr . "&STREET=" . $street;
//		$nvpstr = $nvpstr . "&CITY=" . $city;
//		$nvpstr = $nvpstr . "&STATE=" . $state;
//		$nvpstr = $nvpstr . "&COUNTRYCODE=" . $countryCode;
//		$nvpstr = $nvpstr . "&IPADDRESS=" . $_SERVER['REMOTE_ADDR'];
//
//		$resArray = $this->hash_call("DoDirectPayment", $nvpstr);
//
//		return $resArray;
//	}

    /**
      '-------------------------------------------------------------------------------------------------------------------------------------------
     * hash_call: Function to perform the API call to PayPal using API signature
     * @methodName is name of API  method.
     * @nvpStr is nvp string.
     * returns an associtive array containing the response from the server.
      '-------------------------------------------------------------------------------------------------------------------------------------------
     */
    private function hash_call($methodName, $nvpStr) {
        //setting the curl parameters.
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $this->api_url);
        curl_setopt($ch, CURLOPT_VERBOSE, 1);

        //turning off the server and peer verification(TrustManager Concept).
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);

        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_POST, 1);

        //if USE_PROXY constant set to TRUE in Constants.php, then only proxy will be enabled.
        //Set proxy name to PROXY_HOST and port number to PROXY_PORT in constants.php 
        if ($this->connect_via_proxy)
            curl_setopt($ch, CURLOPT_PROXY, 'my-proxy.offgamers.lan:3128');

        //NVPRequest for submitting to server
        $nvpreq = "METHOD=" . urlencode($methodName) . "&VERSION=" . urlencode($this->version) . "&PWD=" . urlencode($this->apiPassword) . "&USER=" . urlencode($this->apiUsername) . "&SIGNATURE=" . urlencode($this->apiSignature) . $nvpStr;

        //setting the nvpreq as POST FIELD to curl
        curl_setopt($ch, CURLOPT_POSTFIELDS, $nvpreq);

        //getting response from server
        $response = curl_exec($ch);

        //convrting NVPResponse to an Associative Array
        $nvpResArray = $this->deformatNVP($response);
//		$nvpReqArray = $this->deformatNVP($nvpreq);
        if (curl_errno($ch)) {
            @tep_mail('PaypalEC Curl Report', '<EMAIL>', 'PaypalEC Curl Error: ' . curl_errno($ch) . ' - ' . curl_error($ch), '', STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
        } else {
            //closing the curl
            curl_close($ch);
        }

        return $nvpResArray;
    }

    /* '----------------------------------------------------------------------------------
      Purpose: Redirects to PayPal.com site.
      Inputs:  NVP string.
      Returns:
      ----------------------------------------------------------------------------------
     */

    private function RedirectToPayPal($token, $commit = false) {
        // Redirect to paypal.com here
        $payPalURL = $this->form_action_url . $token;
        if ($commit) {
            $payPalURL .= '&useraction=commit';
        }
        header("Refresh:1;" . $payPalURL);
        exit;
    }

    public function get_orders_with_payer_email($customers_id, $customer_email) {
        // Rerun genesis script for these Verifying status orders
        $orders_select_sql = "  SELECT o.orders_id  
                                FROM " . TABLE_PAYPALEC . " AS p 
                                INNER JOIN " . TABLE_ORDERS . " AS o 
                                    ON (p.paypal_order_id = o.orders_id) 
                                WHERE p.payer_email = '" . tep_db_input($customer_email) . "' 
                                    AND o.customers_id = '" . tep_db_input($customers_id) . "'
                                    AND o.orders_status = 7";
        $orders_result_sql = tep_db_query($orders_select_sql);

        while ($orders_row = tep_db_fetch_array($orders_result_sql)) {
            $genesis_order_data_array = array('orders_id' => $orders_row['orders_id'],
                'flag' => 0,
                're_run' => 1,
                'last_modified' => 'now()');
            tep_db_perform(TABLE_CRON_GENESIS_ORDERS, $genesis_order_data_array);
        }
    }

    /* '----------------------------------------------------------------------------------
     * This function will take NVPString and convert it to an Associative Array and it will decode the response.
     * It is usefull to search for a particular key and displaying arrays.
     * @nvpstr is NVPString.
     * @nvpArray is Associative Array.
      ----------------------------------------------------------------------------------
     */

    private function deformatNVP($nvpstr) {
        $intial = 0;
        $nvpArray = array();

        while (strlen($nvpstr)) {
            //postion of Key
            $keypos = strpos($nvpstr, '=');
            //position of value
            $valuepos = strpos($nvpstr, '&') ? strpos($nvpstr, '&') : strlen($nvpstr);

            /* getting the Key and Value values and storing in a Associative Array */
            $keyval = substr($nvpstr, $intial, $keypos);
            $valval = substr($nvpstr, $keypos + 1, $valuepos - $keypos - 1);
            //decoding the respose
            $nvpArray[urldecode($keyval)] = urldecode($valval);
            $nvpstr = substr($nvpstr, $valuepos + 1, strlen($nvpstr));
        }
        return $nvpArray;
    }

    function check() {
        if (!isset($this->_check)) {
            $payment_methods_select_sql = "	SELECT payment_methods_id
											FROM " . TABLE_PAYMENT_METHODS . " as pm
											WHERE pm.payment_methods_id = '" . (int) $this->payment_methods_id . "'";
            $payment_methods_result_sql = tep_db_query($payment_methods_select_sql);
            $this->_check = tep_db_num_rows($payment_methods_result_sql);
        }
        return $this->_check;
    }

    function check_trans_status() {
        
    }

    function is_processed_order($order_id) {
        return true;
    }

    function load_pm_setting($language_id = 1, $pm_id = '') {
        $pm_setting_array = array();
        //load value from DB

        if (tep_not_null($pm_id)) {
            $payment_method_id = $pm_id;

            $payment_methods_parent_id_select_sql = "	SELECT payment_methods_parent_id 
														FROM " . TABLE_PAYMENT_METHODS . " 
														WHERE payment_methods_id = '" . (int) $pm_id . "'";
            $payment_methods_parent_id_result_sql = tep_db_query($payment_methods_parent_id_select_sql);
            $payment_methods_parent_id_row = tep_db_fetch_array($payment_methods_parent_id_result_sql);

            $payment_gateway_id = $payment_methods_parent_id_row['payment_methods_parent_id'];
        } else {
            $payment_method_id = $this->payment_methods_id;
            $payment_gateway_id = $this->payment_methods_parent_id;
        }

        $payment_configuration_info_select_sql = "	SELECT pci.payment_configuration_info_key, pcid.payment_configuration_info_value
													FROM " . TABLE_PAYMENT_CONFIGURATION_INFO . " as pci
													LEFT JOIN " . TABLE_PAYMENT_CONFIGURATION_INFO_DESCRIPTION . " as pcid 
														ON pci.payment_configuration_info_id = pcid.payment_configuration_info_id
															AND pcid.languages_id = '" . (int) $language_id . "'
													WHERE pci.payment_methods_id = '" . (int) $payment_method_id . "'
													ORDER BY pci.payment_configuration_info_sort_order";
        $payment_configuration_info_result_sql = tep_db_query($payment_configuration_info_select_sql);
        if (tep_db_num_rows($payment_configuration_info_result_sql)) {
            while ($payment_configuration_info_row = tep_db_fetch_array($payment_configuration_info_result_sql)) {
                $pm_setting_array[$payment_configuration_info_row['payment_configuration_info_key']] = $payment_configuration_info_row['payment_configuration_info_value'];
            }
        } else if ((int) $payment_gateway_id > 0) { //load value from PG
            $payment_configuration_info_select_sql = "	SELECT pci.payment_configuration_info_key, pcid.payment_configuration_info_value
														FROM " . TABLE_PAYMENT_CONFIGURATION_INFO . " as pci
														LEFT JOIN " . TABLE_PAYMENT_CONFIGURATION_INFO_DESCRIPTION . " as pcid 
															ON pci.payment_configuration_info_id = pcid.payment_configuration_info_id
																AND pcid.languages_id = '" . (int) $language_id . "'
														WHERE pci.payment_methods_id = '" . (int) $payment_gateway_id . "'
														ORDER BY pci.payment_configuration_info_sort_order";
            $payment_configuration_info_result_sql = tep_db_query($payment_configuration_info_select_sql);
            while ($payment_configuration_info_row = tep_db_fetch_array($payment_configuration_info_result_sql)) {
                $pm_setting_array[$payment_configuration_info_row['payment_configuration_info_key']] = $payment_configuration_info_row['payment_configuration_info_value'];
            }
        }

        return $pm_setting_array;
    }

    function get_merchant_account($selected_currency) {
        $payment_methods_instance_id = 0;
        $payment_gateway_instance_select_sql = "SELECT pmi.payment_methods_instance_id, pmi.payment_methods_instance_follow_default, pmi.currency_code 
				 								FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " as pmi
				 								WHERE pmi.payment_methods_id = '" . (int) $this->payment_methods_id . "'
				 								LIMIT 1";
        $payment_gateway_instance_result_sql = tep_db_query($payment_gateway_instance_select_sql);
        if (tep_db_num_rows($payment_gateway_instance_result_sql)) {
            $payment_gateway_instance_select_sql = "SELECT pmi.payment_methods_instance_id, pmi.payment_methods_instance_follow_default 
					 								FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " as pmi
					 								WHERE pmi.currency_code = '" . tep_db_input($selected_currency) . "' 
					 									AND pmi.payment_methods_id = '" . (int) $this->payment_methods_id . "'";
            $payment_gateway_instance_result_sql = tep_db_query($payment_gateway_instance_select_sql);
            if ($payment_gateway_instance_row = tep_db_fetch_array($payment_gateway_instance_result_sql)) {
                if ((int) $payment_gateway_instance_row['payment_methods_instance_follow_default'] > 0) { // Follow Default Currency Setting
                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_follow_default'];
                } else { // Has own setting
                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_id'];
                }
            }
        } else {
            $payment_gateway_instance_select_sql = "SELECT pmi.payment_methods_instance_id, pmi.payment_methods_instance_follow_default 
					 								FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " as pmi
					 								INNER JOIN " . TABLE_PAYMENT_METHODS . " as pm
					 									ON pm.payment_methods_parent_id = pmi.payment_methods_id
					 								WHERE pmi.currency_code = '" . tep_db_input($selected_currency) . "' 
					 									AND pm.payment_methods_id = '" . (int) $this->payment_methods_id . "'";
            $payment_gateway_instance_result_sql = tep_db_query($payment_gateway_instance_select_sql);
            if ($payment_gateway_instance_row = tep_db_fetch_array($payment_gateway_instance_result_sql)) {
                if ((int) $payment_gateway_instance_row['payment_methods_instance_follow_default'] > 0) {
                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_follow_default'];
                } else {
                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_id'];
                }
            }
        }

        $payment_gateway_instance_setting_select_sql = "SELECT pmis.payment_methods_instance_setting_key, pmis.payment_methods_instance_setting_value 
						 								FROM " . TABLE_PAYMENT_METHODS_INSTANCE_SETTING . " as pmis
						 								WHERE pmis.payment_methods_instance_id = '" . (int) $payment_methods_instance_id . "'";
        $payment_gateway_instance_setting_result_sql = tep_db_query($payment_gateway_instance_setting_select_sql);
        while ($payment_gateway_instance_setting_row = tep_db_fetch_array($payment_gateway_instance_setting_result_sql)) {
            switch ($payment_gateway_instance_setting_row['payment_methods_instance_setting_key']) {
                case 'MODULE_PAYMENT_PAYPALEC_RECEIVER_EMAIL':
                    $this->receiverEmail = $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];
                    break;
                case 'MODULE_PAYMENT_PAYPALEC_USERNAME':
                    $this->apiUsername = $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];
                    break;
                case 'MODULE_PAYMENT_PAYPALEC_PASSWORD':
                    $this->apiPassword = $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];
                    break;
                case 'MODULE_PAYMENT_PAYPALEC_SIGNATURE':
                    $this->apiSignature = $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];
                    break;
            }
        }
    }

    function install() {
        $install_configuration_flag = true;
        if ($this->payment_methods_id > 0) {
            if ($this->receive_status != 1) {
                $update_receive_status_sql_data = array("payment_methods_receive_status" => 1);
                tep_db_perform(TABLE_PAYMENT_METHODS, $update_receive_status_sql_data, 'update', "payment_methods_id = '" . (int) $this->payment_methods_id . "'");
            } else {
                $install_configuration_flag = false;
            }
        } else {
            $install_payment_gateway_data_sql = array('payment_methods_code' => $this->code,
                'payment_methods_send_status' => 0,
                'payment_methods_title' => $this->code,
                'payment_methods_types_id' => 0,
                'payment_methods_parent_id' => 0,
                'payment_methods_legend_color' => '#A3B9A3',
                'payment_methods_sort_order' => 6,
                'payment_methods_filename' => $this->filename,
                'payment_methods_logo' => '',
                'date_added' => 'now()',
                'last_modified' => 'now()',
                'payment_methods_receive_status' => 1,
                'payment_methods_receive_status_mode' => 0
            );
            tep_db_perform(TABLE_PAYMENT_METHODS, $install_payment_gateway_data_sql);
            $this->payment_methods_id = tep_db_insert_id();
        }
        if ($install_configuration_flag) {
            $install_key_array = array();
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Set Order Status',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_PAYPALEC_ORDER_STATUS_ID',
                    'payment_configuration_info_description' => 'Set the status of orders made with this payment module to this value.',
                    'payment_configuration_info_sort_order' => '1335',
                    'set_function' => 'tep_cfg_pull_down_order_statuses(',
                    'use_function' => 'tep_get_order_status_name',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => '1',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Set Order\'s "Processing" Status',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_PAYPALEC_PROCESSING_STATUS_ID',
                    'payment_configuration_info_description' => 'Set the initial processing status of orders made with this payment module to this value.',
                    'payment_configuration_info_sort_order' => '1340',
                    'set_function' => 'tep_cfg_pull_down_order_statuses(',
                    'use_function' => 'tep_get_order_status_name',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => '7',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Payment Message',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_PAYPALEC_MESSAGE',
                    'payment_configuration_info_description' => 'Payment message will show up during checkout process (smart2pay).',
                    'payment_configuration_info_sort_order' => '1350',
                    'set_function' => 'tep_cfg_textarea(',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'Thank you for shopping at OffGamers.com.',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Payment Email Message',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_PAYPALEC_EMAIL_MESSAGE',
                    'payment_configuration_info_description' => 'This message will show up in order confirmation e-mail.',
                    'payment_configuration_info_sort_order' => '1360',
                    'set_function' => 'tep_cfg_textarea(',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'Thank you for shopping at OffGamers.com.',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Confirm Complete (in days)',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_PAYPALEC_CONFIRM_COMPLETE',
                    'payment_configuration_info_description' => 'The number of days in which an order made by this payment method is confirm completed.',
                    'payment_configuration_info_sort_order' => '1265',
                    'set_function' => '',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => '0',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Test Mode',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_PAYPALEC_TEST_MODE',
                    'payment_configuration_info_description' => 'Testing Mode?',
                    'payment_configuration_info_sort_order' => '100',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Require Address Information',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_PAYPALEC_MANDATORY_ADDRESS_FIELD',
                    'payment_configuration_info_description' => 'Set address field as required info during checkout.',
                    'payment_configuration_info_sort_order' => '1400',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );
            foreach ($install_key_array as $data) {
                tep_db_perform(TABLE_PAYMENT_CONFIGURATION_INFO, $data['info']);
                $payment_conf_id = tep_db_insert_id();

                $data['desc']['payment_configuration_info_id'] = (int) $payment_conf_id;
                tep_db_perform(TABLE_PAYMENT_CONFIGURATION_INFO_DESCRIPTION, $data['desc']);
            }
        }
        $install_send_payment_key_array = array();
        $install_send_payment_key_array[] = array('payment_methods_fields_title' => 'PayPalEC Email',
            'payment_methods_fields_pre_info' => '',
            'payment_methods_fields_post_info' => '',
            'payment_methods_fields_required' => 1,
            'payment_methods_fields_type' => '1',
            'payment_methods_fields_system_type' => 'MODULE_PAYPALEC_SEND_EMAIL',
            'payment_methods_fields_size' => '55',
            'payment_methods_fields_option' => 'NULL',
            'payment_methods_fields_options_title' => '0',
            'payment_methods_fields_sort_order' => 0
        );
        if (count($install_send_payment_key_array)) {
            foreach ($install_send_payment_key_array as $data) {
                $data['payment_methods_id'] = (int) $this->payment_methods_id;
                $data['payment_methods_mode'] = 'SEND';
                $data['payment_methods_fields_system_mandatory'] = 1;
                $data['payment_methods_fields_status'] = '1';

                tep_db_perform(TABLE_PAYMENT_METHODS_FIELDS, $data);
            }
        }
        return 1;
    }

    function merchant_information_keys() {
        include_once(DIR_WS_CLASSES . "payment_methods.php");

        return array(
            'MODULE_PAYMENT_PAYPALEC_RECEIVER_EMAIL' => array('field' => 'text', 'mapping' => 'MODULE_PAYMENT_PAYPALEC_RECEIVER_EMAIL'),
            'MODULE_PAYMENT_PAYPALEC_USERNAME' => array('field' => 'text', 'mapping' => 'MODULE_PAYMENT_PAYPALEC_USERNAME'),
            'MODULE_PAYMENT_PAYPALEC_PASSWORD' => array('field' => 'text', 'mapping' => 'MODULE_PAYMENT_PAYPALEC_PASSWORD'),
            'MODULE_PAYMENT_PAYPALEC_SIGNATURE' => array('field' => 'text', 'mapping' => 'MODULE_PAYMENT_PAYPALEC_SIGNATURE'),
            'MODULE_PAYMENT_TAX' => array("field" => 'select', 'mapping' => "MODULE_PAYMENT_TAX", "select" => payment_methods::get_tax_option())
        );
    }

    function get_orders_payment_info_file() {
        return DIR_FS_CATALOG_MODULES . 'payment/paypalEC/admin/orders.inc.php';
    }

    function get_orders_list_payment_info_file() {
        return DIR_FS_CATALOG_MODULES . 'payment/paypalEC/admin/orders_list.inc.php';
    }

}

?>