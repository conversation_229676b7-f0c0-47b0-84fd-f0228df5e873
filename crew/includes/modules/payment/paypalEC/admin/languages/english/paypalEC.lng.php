<?php

define('TABLE_HEADING_TXN_TYPE', 'Transaction Type');
define('TABLE_HEADING_PAYMENT_STATUS', 'Payment Status');

if (!defined('TABLE_HEADING_ACTION'))
	define('TABLE_HEADING_ACTION', 'Action');
if (!defined('TABLE_HEADING_DATE_ADDED'))
	define('TABLE_HEADING_DATE_ADDED', 'Date Added');

define('TEXT_INFO_PAYPAL_IPN_HEADING', 'PayPal IPN');
define('TEXT_DISPLAY_NUMBER_OF_IPN_TRANSACTIONS', 'Displaying <b>%d</b> to <b>%d</b> (of <b>%d</b> IPN\'s)');

//Details section
define('HEADING_DEATILS_CUSTOMER_REGISTRATION_TITLE', 'PayPal Customer Registration Details');
define('HEADING_DETAILS_REGISTRATION_TITLE', 'PayPal Instant Payment Notification');
define('TEXT_INFO_ENTRY_ADDRESS', 'Address:');
define('TEXT_INFO_ORDER_NUMBER', 'Order Number');
define('TEXT_INFO_TXN_TYPE', 'Transaction Type');
define('TEXT_INFO_PAYMENT_STATUS', 'Payment Status:');
define('TEXT_INFO_PAYMENT_AMOUNT', 'Amount');
if (!defined('ENTRY_FIRST_NAME'))
	define('ENTRY_FIRST_NAME', 'First Name');
if (!defined('ENTRY_LAST_NAME'))
	define('ENTRY_LAST_NAME', 'Last Name');
define('ENTRY_BUSINESS_NAME', 'Business Name:');
define('ENTRY_ADDRESS', 'Address');
//EMAIL ALREADY DEFINED IN ORDERS
define('ENTRY_PAYER_ID', 'Payer ID:');
define('ENTRY_PAYER_STATUS', 'Payer Status:');
define('ENTRY_ADDRESS_STATUS', 'Address Status:');
define('ENTRY_PAYMENT_TYPE', 'Payment Type:');
define('TABLE_HEADING_ENTRY_PAYMENT_STATUS', 'Payment Status:');
define('TABLE_HEADING_PENDING_REASON', 'Pending Reason');
define('TABLE_HEADING_IPN_DATE', 'IPN Date');
define('ENTRY_INVOICE', 'Invoice:');
define('ENTRY_PAYPAL_IPN_TXN', 'Transaction ID:');
define('ENTRY_PAYMENT_DATE', 'Payment Date:');
define('ENTRY_PAYMENT_LAST_MODIFIED', 'Last modified:');
define('ENTRY_MC_CURRENCY', 'MC Currency:');
define('ENTRY_MC_GROSS', 'MC Gross:');
define('ENTRY_MC_FEE', 'MC Fee:');
define('ENTRY_PAYMENT_GROSS', 'Payment Gross');
define('ENTRY_PAYMENT_FEE', 'Payment Fee');
define('ENTRY_RESIDENCE_COUNTRY', 'Residence Country:');
define('ENTRY_PROTECTION_ELIGIBILITY', 'Protection Eligibility:');
define('ENTRY_PAYMENT_RECEIVER_EMAIL', 'Receiver Email:');
define('TEXT_NO_IPN_HISTORY', 'No IPN history available');
define('EMAIL_PAYPAL_VERIFY_SEND_LINK', 'Click here to send paypal verification e-mail');
define('TABLE_HEADING_CHANGE_BY', 'Changed By');

//Define Merchant Key Language
define('MODULE_PAYMENT_PAYPALEC_RECEIVER_EMAIL', 'PayPal ID');
define('MODULE_PAYMENT_PAYPALEC_USERNAME', 'Username');
define('MODULE_PAYMENT_PAYPALEC_PASSWORD', 'Password');
define('MODULE_PAYMENT_PAYPALEC_SIGNATURE', 'Signature');
define('MODULE_PAYMENT_TAX', 'Tax');

?>