<?php
include_once(DIR_FS_CATALOG_MODULES . 'payment/paypalEC/admin/languages/' . $language . '/paypalEC.lng.php');

$paypalec_trans_info_select_sql = "	SELECT first_name, last_name, payer_email, payer_id, payer_status, address_status, address_name, 
									address_street, address_city, address_state, address_zip, address_country, payment_type, txn_id,
									paypal_order_id, mc_currency, mc_gross, mc_fee, residence_country, protection_eligibility, date_added, 
									last_modified, receiver_email
									FROM " . TABLE_PAYPALEC . " 
									WHERE paypal_order_id ='" . $oID . "'";
$paypalec_trans_info_result_sql = tep_db_query($paypalec_trans_info_select_sql);
?>
<table border="0" width="100%" cellspacing="0" cellpadding="2">
	<tr>
		<td class="main" colspan="3">
			<table border="0" width="100%" cellspacing="0" cellpadding="2">
				<tr>
					<td class="main" width="12%" nowrap><b><?= ENTRY_PAYMENT_METHOD ?></b></td>
					<td class="main">
						<div>
							<?php if ((int) $order->info['payment_methods_id'] > 0) { ?><div class="<?= ((int) $payment_info_array[$order->info['payment_methods_id']]->confirm_complete_days > 0 ? 'rp_icon' : 'nrp_icon') ?>"><b><?= ((int) $payment_info_array[$order->info['payment_methods_id']]->confirm_complete_days > 0 ? 'RP' : 'NRP') ?></b></div> <? } ?>
							<div style="width:30px; height:15px; background-color: <?= $payment_module_info['display_colour'] ?>; float: left; vertical-align: top">&nbsp;</div>
							<div style="float: left; vertical-align: top">
								&nbsp;&nbsp;
								<?php
								if ($view_payment_details_permission) {
									if (tep_not_null($last_payment_method) && $last_payment_method['payment_methods_id'] != $order->info['payment_methods_id']) {
										echo '<span class="redIndicator">' . (tep_not_null($payment_module_info['payment_methods_parent_title']) ? $payment_module_info['payment_methods_parent_title'] . ' - ' : '') . $payment_module_info['payment_methods_title'] . '</span>';
									} else {
										echo (tep_not_null($payment_module_info['payment_methods_parent_title']) ? $payment_module_info['payment_methods_parent_title'] . ' - ' : '') . $payment_module_info['payment_methods_title'];
									}

									if (tep_not_null($last_payment_method) && $last_payment_method['payment_methods_id'] != $order->info['payment_methods_id']) {
										echo '&nbsp;&nbsp;<span class="redIndicator">(Last Payment Method: ' . (tep_not_null($last_payment_method['parent_payment_methods_title']) ? $last_payment_method['parent_payment_methods_title'] . ' - ' : '' ) . $last_payment_method['payment_methods_title'] . ')</span>';
									}
								} else {
									echo (tep_not_null($payment_module_info['payment_methods_parent_title']) ? $payment_module_info['payment_methods_parent_title'] . ' - ' : '') . $payment_module_info['payment_methods_title'];
								}
								?>
							</div>
						</div>
					</td>
				</tr>
			</table>
		</td>
	</tr>
	<?php
	if (tep_db_num_rows($paypalec_trans_info_result_sql)) {
		if ($view_payment_details_permission) {
			$paypalec_trans_info_row = tep_db_fetch_array($paypalec_trans_info_result_sql);
			?>
			<tr>
				<td class="main" colspan="3">
					<table border="0" width="100%" cellspacing="0" cellpadding="2">
						<tr valign="top">
							<td>
								<table border="0" cellspacing="0" cellpadding="2">
									<tr>
										<td class="main" valign="top" nowrap><b><?= ENTRY_CUSTOMER ?></b></td>
										<td class="main">&nbsp;</td>
										<td class="main" nowrap>
											<?php
											$paypal_payer_name = $paypalec_trans_info_row['first_name'] . ' ' . $paypalec_trans_info_row['last_name'];

											$name_not_match_profile = (strtolower($paypal_payer_name) != strtolower($order->customer["name"])) ? 1 : 0;

											$customer_with_this_name_array = tep_check_duplicate_name(array($paypalec_trans_info_row['first_name'], $paypalec_trans_info_row['last_name']), 'paypalEC'); // including himself
											$name_used_by_other = (count($customer_with_this_name_array) > 1) ? 1 : 0;

											$total_name_used_in_paypal = tep_get_distinct_name_used($cid, $paypal_payer_name, 'paypalEC');
											$total_name_used = (count($total_name_used_in_paypal) > 1) ? 1 : 0;

											echo '<span class="' . (($name_not_match_profile + $name_used_by_other + $total_name_used > 0) ? 'redIndicator' : 'blackIndicator') . '">' . $paypalec_trans_info_row['first_name'] . ' ' . $paypalec_trans_info_row['last_name'] . '</span>';

											$name_alert_reason = '';
											$name_alert_reason .= ($name_not_match_profile == 1) ? '<span class="redIndicator">Name does not match profile.</span><br>' : '';

											if ($name_used_by_other == 1) {
												$name_alert_reason .= tep_draw_form('cust_lists_share_name', FILENAME_CUSTOMERS, 'action=show_report&customer_lists_subaction=do_search', 'post', 'target="_blank"') . "\n" .
													tep_draw_hidden_field('customer_share_id', tep_array_serialize($customer_with_this_name_array)) . "\n" .
													'<span class="redIndicator">Name exists in <a href="javascript: document.cust_lists_share_name.submit();"><u>' . count($customer_with_this_name_array) . '</u></a> profiles.</span><br>' .
													'</form>' . "\n";
											}
											$name_alert_reason .= ($total_name_used == 1) ? '<span class="redIndicator"><a href="' . tep_href_link(FILENAME_ORDERS, 'cID=' . $cid) . '" target="_blank"><u>' . count($total_name_used_in_paypal) . '</u></a> names used in PayPal orders.</span><br>' : '';

											if (tep_not_null($paypal_payer_name) && tep_not_null($name_alert_reason)) {
												echo '<br><span class="smallText">' . $name_alert_reason . '</span>';
											}
											?>
										</td>
									</tr>
									<tr>
										<td class="main" valign="top" nowrap><b><?= ENTRY_EMAIL_ADDRESS ?></b></td>
										<td class="main">&nbsp;</td>
										<td class="main" nowrap>
											<?php
											$paypal_verify_select_sql = "	SELECT civ.info_verified 
																			FROM " . TABLE_CUSTOMERS_INFO_VERIFICATION . " AS civ 
																			WHERE civ.customers_id = '" . $cid . "'
																				AND civ.customers_info_value = '" . tep_db_input($paypalec_trans_info_row['payer_email']) . "'
																				AND civ.info_verification_type = 'email'";
											$paypal_verify_result_sql = tep_db_query($paypal_verify_select_sql);
											$paypal_verify_row = tep_db_fetch_array($paypal_verify_result_sql);
											if (strtolower($paypalec_trans_info_row['payer_email']) != strtolower($order->customer['email_address'])) {
												echo '<span class="redIndicator">' . $paypalec_trans_info_row['payer_email'] . '</span>&nbsp;&nbsp;';
												if ($paypal_verify_row['info_verified'] == 1) {
													echo tep_image(DIR_WS_ICONS . "ok.gif", "", "", "", 'align="top" onMouseover="ddrivetip(\'' . TEXT_IMAGE_MOUSEOVER_VERIFY . '\', \'\' , 180);" onMouseout="hideddrivetip();"');
												} else {
													$confirm_send_profile_email_verification_url = tep_href_link(FILENAME_ORDERS, 'oID=' . $oID . '&action=edit&subaction=send_paypal_verify_email&payment=paypalec') . '&cid=' . $cid;
													echo tep_image(DIR_WS_ICONS . "off.gif", "", "", "", 'align="top" onMouseover="ddrivetip(\'' . TEXT_IMAGE_MOUSEOVER_NOT_VERIFY . '\', \'\' , 201);" onMouseout="hideddrivetip();"') . '&nbsp;' . '<a href="" onclick="confirm_action(\'Are you sure you want to send verification e-mail?\', \'' . $confirm_send_profile_email_verification_url . '\'); return false;">' . EMAIL_PAYPAL_VERIFY_SEND_LINK . '</a>';
												}
												if (tep_not_null($paypalec_trans_info_row['payer_email']))
													echo '<br><span class="smallText"><span class="redIndicator">Email does not match profile.</span></span>';
											} else {
												echo $paypalec_trans_info_row['payer_email'] . '&nbsp;&nbsp;';

												if ($paypal_verify_row['info_verified'] == 1) {
													echo tep_image(DIR_WS_ICONS . "ok.gif", "", "", "", 'align="top" onMouseover="ddrivetip(\'' . TEXT_IMAGE_MOUSEOVER_VERIFY . '\', \'\' , 180);" onMouseout="hideddrivetip();"');
												} else {
													$confirm_send_profile_email_verification_url = tep_href_link(FILENAME_ORDERS, 'oID=' . $oID . '&action=edit&subaction=send_paypal_verify_email&payment=paypalec') . '&cid=' . $cid;
													echo tep_image(DIR_WS_ICONS . "off.gif", "", "", "", 'align="top" onMouseover="ddrivetip(\'' . TEXT_IMAGE_MOUSEOVER_NOT_VERIFY . '\', \'\' , 201);" onMouseout="hideddrivetip();"') . '&nbsp;' . '<a href="" onclick="confirm_action(\'Are you sure you want to send verification e-mail?\', \'' . $confirm_send_profile_email_verification_url . '\'); return false;">' . EMAIL_PAYPAL_VERIFY_SEND_LINK . '</a>';
												}
											}
											?></td>
									</tr>
									<tr>
										<td class="main" valign="top" nowrap><b><?= ENTRY_PAYER_ID ?></b></td>
										<td class="main">&nbsp;</td>
										<td class="main" nowrap>
											<?php
											$customer_share_paypal_payer_id_array = array();
											$payer_id_select_sql = "SELECT DISTINCT customers_id 
																	FROM " . TABLE_ORDERS . " AS o 
																	INNER JOIN " . TABLE_PAYPAL . " AS paypal 
																		ON (o.orders_id = paypal.invoice)
																	WHERE paypal.payer_id = '" . tep_db_input($paypalec_trans_info_row['payer_id']) . "'
																	UNION
																	SELECT DISTINCT customers_id 
																	FROM " . TABLE_ORDERS . " AS o 
																	INNER JOIN " . TABLE_PAYPALEC . " AS paypalec
																		ON (o.orders_id = paypalec.paypal_order_id)
																	WHERE paypalec.payer_id = '" . tep_db_input($paypalec_trans_info_row['payer_id']) . "'";
											$payer_id_result = tep_db_query($payer_id_select_sql);
											while ($payer_id_row = tep_db_fetch_array($payer_id_result)) {
												$customer_share_paypal_payer_id_array[] = $payer_id_row["customers_id"];
											}
											if (count($customer_share_paypal_payer_id_array) > 1) {
												echo tep_draw_form('cust_lists_share_payer_id', FILENAME_CUSTOMERS, tep_get_all_get_params(array('action')) . 'action=show_report&customer_lists_subaction=do_search', 'post', 'target="customerSharePayerIDWin"') . "\n" .
												tep_draw_hidden_field('customer_share_id', tep_array_serialize($customer_share_paypal_payer_id_array)) . "\n" .
												'<span class="smallText"><span class="redIndicator">' . $paypalec_trans_info_row['payer_id'] . '<br><a href="javascript: document.cust_lists_share_payer_id.submit();"><u>' . count($customer_share_paypal_payer_id_array) . '</u></a> customers sharing the same Payer ID.</span></span>' .
												'</form>' . "\n";
											} else {
												echo $paypalec_trans_info_row['payer_id'];
											}
											if (tep_not_null($paypalec_trans_info_row['payer_id'])) {
												$payment_check_info = array(
													'payer_id' => $paypalec_trans_info_row['payer_id'],
													'orders_id' => $order->order_id,
													'date_purchased' => $order->info['date_purchased'],
													'customers_id' => $order->customer['id']
												);
												$payment_verified_date = tep_get_payment_info_verified_date($payment_check_info, 'paypal_payer_id');
												echo "<br><span class='redIndicator'>First successful verified date by this user: " . (tep_not_null($payment_verified_date) ? $payment_verified_date : "NEVER") . "</span>";
											}
											?>
										</td>
									</tr>
									<tr>
										<td class="main" nowrap><b><?= ENTRY_PAYER_STATUS ?></b></td>
										<td class="main">&nbsp;</td>
										<td class="main" nowrap><?php echo $paypalec_trans_info_row['payer_status']; ?></td>
									</tr>
								</table>
							</td>
							<td style="padding-left:50px;">
								<table border="0" cellspacing="0" cellpadding="2">
									<tr valign="top">
										<td class="main" style="padding-right: 10px;" nowrap><?php echo '<b>' . ENTRY_ADDRESS . '</b><br>' . ($paypalec_trans_info_row['address_status'] == 'unconfirmed' ? '<span class="redIndicator">' . $paypalec_trans_info_row['address_status'] . '</span>' : $paypalec_trans_info_row['address_status']); ?></td>
										<td class="main" nowrap><?php echo $paypalec_trans_info_row['address_name'] . '<br>' . $paypalec_trans_info_row['address_street'] . '<br>' . $paypalec_trans_info_row['address_city'] . '<br>' . $paypalec_trans_info_row['address_state'] . '<br>' . $paypalec_trans_info_row['address_zip'] . '<br>' . $paypalec_trans_info_row['address_country']; ?></td>
									</tr>
								</table>
							</td>
							<td style="padding-left:50px;">
								<table border="0" cellspacing="0" cellpadding="2">
									<tr>
										<td class="main" nowrap><b><?= ENTRY_PAYMENT_TYPE ?></b>&nbsp;</td><td class="main" nowrap><?php echo $paypalec_trans_info_row['payment_type']; ?></td>
									</tr>
									<tr>
										<td class="main" nowrap><b><?= ENTRY_PAYPAL_IPN_TXN ?></b>&nbsp;</td><td class="main" nowrap><?php echo $paypalec_trans_info_row['txn_id']; ?></td>
									</tr>
									<tr>
										<td class="main" nowrap><b><?= ENTRY_INVOICE ?></b>&nbsp;</td><td class="main" nowrap><?php echo $paypalec_trans_info_row['paypal_order_id']; ?></td>
									</tr>
								</table>
							</td>
						</tr>
					</table>
				</td>
			</tr>
			<tr>
				<td colspan="3"><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td>
			</tr>
			<tr valign="top">
				<td class="main" colspan="2">
					<table border="0" cellspacing="0" cellpadding="2">
						<tr valign="top">
							<td>
								<table border="0" cellspacing="0" cellpadding="2">
									<tr>
										<td class="main" valign="top" nowrap><b><?= ENTRY_MC_CURRENCY ?></b></td>
										<td class="main" valign="top" nowrap>
											<?php
											if ($paypalec_trans_info_row['mc_currency'] != $order->info["currency"]) {
												echo '<span class="redIndicator">' . $paypalec_trans_info_row['mc_currency'] . '<br><span class="smallText">Does not match purchase currency.</span></span>';
											} else {
												echo $paypalec_trans_info_row['mc_currency'];
											}
											?>
										</td>
									</tr>
									<tr>
										<td class="main" valign="top" nowrap><b><?= ENTRY_MC_GROSS ?></b></td>
										<td class="main" valign="top" nowrap>
											<?php
											$mc_gross_display_text = $paypalec_trans_info_row['mc_gross'];
											//check the payment_amount
											$paypal_gross_amt = number_format($paypalec_trans_info_row['mc_gross'], $currencies->currencies[$order->info["currency"]]['decimal_places'], $currencies->currencies[$order->info["currency"]]['decimal_point'], $currencies->currencies[$order->info["currency"]]['thousands_point']);
											if ($currencies->currencies[$order->info["currency"]]['symbol_left'] . $paypal_gross_amt . $currencies->currencies[$order->info["currency"]]['symbol_right'] != strip_tags($order->order_totals['ot_total']['text'])) {
												$mc_gross_display_text = '<span class="redIndicator">' . $paypalec_trans_info_row['mc_gross'] . '<br><span class="smallText">Does not match purchase amount.</span></span>';
											}

											echo $mc_gross_display_text;
											?>
										</td>
									</tr>
									<tr>
										<td class="main" valign="top" nowrap><b><?= ENTRY_MC_FEE ?></b></td>
										<td class="main" valign="top"><?php echo $paypalec_trans_info_row['mc_fee']; ?></td>
									</tr>
								</table>
							</td>
							<td class="main" style="padding-left:50px;">
								<table border="0" cellspacing="0" cellpadding="2">
									<tr>
										<td class="main" valign="top" nowrap><b><?= ENTRY_RESIDENCE_COUNTRY ?></b></td>
										<td class="main" valign="top">&nbsp;<?php echo $paypalec_trans_info_row['residence_country']; ?></td>
									</tr>
									<tr>
										<td class="main" valign="top" nowrap><b><?= ENTRY_PROTECTION_ELIGIBILITY ?></b></td>
										<td class="main" valign="top">&nbsp;<?php echo $paypalec_trans_info_row['protection_eligibility']; ?></td>
									</tr>
									<tr>
										<td class="main" valign="top" nowrap><b><?= ENTRY_PAYMENT_DATE ?></b></td>
										<td class="main" valign="top">&nbsp;<?php echo $paypalec_trans_info_row['date_added']; ?></td>
									</tr>
									<tr>
										<td class="main" valign="top" nowrap><b><?= ENTRY_PAYMENT_LAST_MODIFIED ?></b></td>
										<td class="main" valign="top">&nbsp;<?php echo $paypalec_trans_info_row['last_modified']; ?></td>
									</tr>
									<tr>
										<td class="main" valign="top" nowrap><b><?= ENTRY_PAYMENT_RECEIVER_EMAIL ?></b></td>
										<td class="main" valign="top">&nbsp;<?php echo $paypalec_trans_info_row['receiver_email']; ?></td>
									</tr>
								</table>
							</td>
						</tr>
					</table>
				</td>
			</tr>
			<tr valign="top">
				<td class="main" colspan="3">
					<table border="1" cellspacing="0" cellpadding="5">
						<tr>
							<td class="smallText" align="center" nowrap><b><?= TABLE_HEADING_IPN_DATE ?></b></td>
							<td class="smallText" align="center" nowrap><b><?= TABLE_HEADING_PAYMENT_STATUS ?></b></td>
							<td class="smallText" align="center" nowrap><b><?= TABLE_HEADING_PENDING_REASON ?></b></td>
							<td class="smallText" align="center" nowrap><b><?= TABLE_HEADING_CHANGE_BY ?></b></td>
						</tr>
						<?
						$paypalec_payment_status_history_info_select_sql = "SELECT payment_status, reason, date, changed_by 
																			FROM " . TABLE_PAYPALEC_STATUS_HISTORY . " 
																			WHERE paypal_order_id = '" . tep_db_input($oID) . "'";
						$paypalec_payment_status_history_info_result_sql = tep_db_query($paypalec_payment_status_history_info_select_sql);
						if (tep_db_num_rows($paypalec_payment_status_history_info_result_sql)) {
							while ($paypal_history = tep_db_fetch_array($paypalec_payment_status_history_info_result_sql)) {
								echo '	<tr>' . "\n" .
								'		<td class="smallText" align="center" nowrap>' . tep_datetime_short($paypal_history['date'], PREFERRED_DATE_TIME_FORMAT) . '</td>' . "\n" .
								'  	<td class="smallText" nowrap>' . $paypal_history['payment_status'] . '</td>' . "\n" .
								'  	<td class="smallText" align="center" nowrap>' . (tep_not_null($paypal_history['reason']) ? $paypal_history['reason'] : '&nbsp;') . '</td>' . "\n" .
								'  	<td class="smallText" align="center" nowrap>' . (tep_not_null($paypal_history['changed_by']) ? $paypal_history['changed_by'] : '&nbsp;') . '</td>' . "\n" .
								'	</tr>' . "\n";
							}
						} else {
							echo '	<tr>' . "\n" .
							'  	<td class="smallText" colspan="3" nowrap>' . TEXT_NO_IPN_HISTORY . '</td>' . "\n" .
							'	</tr>' . "\n";
						}
						?>
					</table>
				</td>
			</tr>
			<?php
		}
	} else {
		echo $payment_method_title_info;
	}
	?>
</table>