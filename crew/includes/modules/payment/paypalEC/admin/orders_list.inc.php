<?php
include_once(DIR_FS_CATALOG_MODULES . 'payment/paypalEC/admin/languages/' . $language . '/paypalEC.lng.php');
$paypalec_trans_info_select_sql = "	SELECT first_name, last_name, payer_email, payer_id, payer_status, address_status, address_name, 
									address_street, address_city, address_state, address_zip, address_country, payment_type, txn_id,
									paypal_order_id, mc_currency, mc_gross, mc_fee, residence_country, protection_eligibility, date_added, 
									last_modified, receiver_email
									FROM " . TABLE_PAYPALEC . " 
									WHERE paypal_order_id ='" . $order_obj->orders_id . "'";
$paypalec_trans_info_result_sql = tep_db_query($paypalec_trans_info_select_sql);
?>
<table border="0" width="100%" cellspacing="0" cellpadding="2">
	<tr>
		<td class="subInvoiceBoxHeading">
			<div style="border-bottom: 1px solid #996600; border-top: 1px solid #996600; padding: 2px;">Payment Details</div>
		</td>
	</tr>
	<?
	if ($paypalec_trans_info_row = tep_db_fetch_array($paypalec_trans_info_result_sql)) {
		?>
		<tr>
			<td>
				<table border="0" width="100%" cellspacing="0" cellpadding="2">
					<tr valign="top">
						<td width="40%">
							<table border="0" cellspacing="0" cellpadding="2">
								<tr>
									<td class="invoiceRecords" valign="top" nowrap><?= ENTRY_CUSTOMER ?></td>
									<td class="invoiceRecords">:&nbsp;</td>
									<td class="invoiceRecords" nowrap><?= $paypalec_trans_info_row['first_name'] . ' ' . $paypalec_trans_info_row['last_name'] ?></td>
								</tr>
								<tr>
									<td class="invoiceRecords" valign="top" nowrap><?= ENTRY_EMAIL_ADDRESS ?></td>
									<td class="invoiceRecords">:&nbsp;</td>
									<td class="invoiceRecords" nowrap><?= $paypalec_trans_info_row['payer_email'] ?></td>
								</tr>
								<tr>
									<td class="invoiceRecords" nowrap><?= ENTRY_PAYER_ID ?></td>
									<td class="invoiceRecords">:&nbsp;</td>
									<td class="invoiceRecords" nowrap><?= $paypalec_trans_info_row['payer_id'] ?></td>
								</tr>
								<tr>
									<td class="invoiceRecords" nowrap><?= ENTRY_PAYER_STATUS ?></td>
									<td class="invoiceRecords">:&nbsp;</td>
									<td class="invoiceRecords" nowrap><?= $paypalec_trans_info_row['payer_status'] ?></td>
								</tr>
							</table>
						</td>
						<td width="30%">
							<table border="0" cellspacing="0" cellpadding="2">
								<tr valign="top">
									<td class="invoiceRecords" style="padding-right: 10px;" nowrap><?= ENTRY_ADDRESS . '<br>' . $paypalec_trans_info_row['address_status'] ?></td>
									<td class="invoiceRecords">:&nbsp;</td>
									<td class="invoiceRecords" nowrap><?= $paypalec_trans_info_row['address_name'] . '<br>' . $paypalec_trans_info_row['address_street'] . '<br>' . $paypalec_trans_info_row['address_city'] . '<br>' . $paypalec_trans_info_row['address_state'] . '<br>' . $paypalec_trans_info_row['address_zip'] . '<br>' . $paypalec_trans_info_row['address_country'] ?></td>
								</tr>
							</table>
						</td>
						<td width="30%">
							<table border="0" cellspacing="0" cellpadding="2">
								<tr>
									<td class="invoiceRecords" nowrap><?= ENTRY_PAYMENT_TYPE ?></td>
									<td class="invoiceRecords">:&nbsp;</td>
									<td class="invoiceRecords" nowrap><?= $paypalec_trans_info_row['payment_type'] ?></td>
								</tr>
								<tr>
									<td class="invoiceRecords" nowrap><?= ENTRY_PAYPAL_IPN_TXN ?></td>
									<td class="invoiceRecords">:&nbsp;</td>
									<td class="invoiceRecords" nowrap><?= $paypalec_trans_info_row['txn_id'] ?></td>
								</tr>
								<tr>
									<td class="invoiceRecords" nowrap><?= ENTRY_INVOICE ?></td>
									<td class="invoiceRecords">:&nbsp;</td>
									<td class="invoiceRecords" nowrap><?= $paypalec_trans_info_row['paypal_order_id'] ?></td>
								</tr>
							</table>
						</td>
					</tr>
				</table>
			</td>
		</tr>
		<tr>
			<td><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td>
		</tr>
		<tr valign="top">
			<td>
				<table border="0" width="100%" cellspacing="0" cellpadding="2">
					<tr valign="top">
						<td width="40%">
							<table border="0" cellspacing="0" cellpadding="2">
								<tr>
									<td class="invoiceRecords" nowrap><?= ENTRY_MC_CURRENCY ?></td>
									<td class="invoiceRecords">:&nbsp;</td>
									<td class="invoiceRecords"><?= $paypalec_trans_info_row['mc_currency'] ?></td>
								</tr>
								<tr>
									<td class="invoiceRecords" nowrap><?= ENTRY_MC_GROSS ?></td>
									<td class="invoiceRecords">:&nbsp;</td>
									<td class="invoiceRecords"><?= $paypalec_trans_info_row['mc_gross'] ?></td>
								</tr>
								<tr>
									<td class="invoiceRecords" nowrap><?= ENTRY_MC_FEE ?></td>
									<td class="invoiceRecords">:&nbsp;</td>
									<td class="invoiceRecords"><?= $paypalec_trans_info_row['mc_fee'] ?></td>
								</tr>
							</table>
						</td>
						<td width="30%">
							<table border="0" cellspacing="0" cellpadding="2">
								<tr>
									<td class="invoiceRecords" nowrap><?= ENTRY_RESIDENCE_COUNTRY ?></td>
									<td class="invoiceRecords">:&nbsp;</td>
									<td class="invoiceRecords"><?= $paypalec_trans_info_row['residence_country'] ?></td>
								</tr>
								<tr>
									<td class="invoiceRecords" nowrap><?= ENTRY_PROTECTION_ELIGIBILITY ?></td>
									<td class="invoiceRecords">:&nbsp;</td>
									<td class="invoiceRecords"><?= $paypalec_trans_info_row['protection_eligibility'] ?></td>
								</tr>
								<tr>
									<td class="invoiceRecords" nowrap><?= ENTRY_PAYMENT_DATE ?></td>
									<td class="invoiceRecords">:&nbsp;</td>
									<td class="invoiceRecords"><?= $paypalec_trans_info_row['date_added'] ?></td>
								</tr>
								<tr>
									<td class="invoiceRecords" nowrap><?= ENTRY_PAYMENT_LAST_MODIFIED ?></td>
									<td class="invoiceRecords">:&nbsp;</td>
									<td class="invoiceRecords"><?= $paypalec_trans_info_row['last_modified'] ?></td>
								</tr>
								<tr>
									<td class="invoiceRecords" nowrap><?= ENTRY_PAYMENT_RECEIVER_EMAIL ?></td>
									<td class="invoiceRecords">:&nbsp;</td>
									<td class="invoiceRecords"><?= $paypalec_trans_info_row['receiver_email'] ?></td>
								</tr>
							</table>
						</td>
					</tr>
				</table>
			</td>
		</tr>
	</table>
<?php
} else {
	?>
	<tr>
		<td class="invoiceRecords">No further payment information is available.</td>
	</tr>
	<?
}
?>