<?php
  define('UNKNOWN_TXN_TYPE', 'Unknown Transaction Type');
  define('UNKNOWN_TXN_TYPE_MSG', 'An unknown transaction (%s) occurred from ' . tep_get_ip_address() . "\nAre you running any tests?\n\n");
  define('UNKNOWN_POST', 'Unknown Post');
  define('UNKNOWN_POST_MSG', "An unknown POST from %s was received.\nAre you running any tests?\n\n");
  define('EMAIL_SEPARATOR', "------------------------------------------------------");
  define('RESPONSE_VERIFIED', 'Verified');
  define('RESPONSE_MSG', "Connection Type\n".EMAIL_SEPARATOR."\ncurl= %s, socket= %s, domain= %s, port= %s \n\nPayPal Response\n".EMAIL_SEPARATOR."\n%s \n\n");
  define('RESPONSE_INVALID', 'Invalid PayPal Response');
  define('RESPONSE_UNKNOWN', 'Unknown Verfication');
  define('EMAIL_RECEIVER', 'Email and Business ID config');
  define('EMAIL_RECEIVER_MSG', "Store Configuration Settings\nPrimary PayPal Email Address: %s\nBusiness ID: %s\n".EMAIL_SEPARATOR."\nPayPal Configuration Settings\nPrimary PayPal Email Address: %s\nBusiness ID: %s\n\n");
  define('EMAIL_RECEIVER_ERROR_MSG', "Store Configuration Settings\nPrimary PayPal Email Address: %s\nBusiness ID: %s\n".EMAIL_SEPARATOR."\nPayPal Configuration Settings\nPrimary PayPal Email Address: %s\nBusiness ID: %s\n\nPayPal Transaction ID: %s\n\n");
  define('TXN_DUPLICATE', 'Duplicate Transaction');
  define('TXN_DUPLICATE_MSG', "A duplicate IPN transaction (%s) has been received.\nPlease check your PayPal Account\n\n");
  define('IPN_TXN_INSERT', "IPN INSERTED");
  define('IPN_TXN_INSERT_MSG', "IPN %s has been inserted\n\n");
  define('CHECK_CURRENCY', 'Validate Currency');
  define('CHECK_CURRENCY_MSG', "Incorrect Currency\nPayPal: %s\nosC: %s\n\n");
  define('CHECK_TXN_SIGNATURE', 'Validate PayPal_Shopping_Cart Transaction Signature');
  define('CHECK_TXN_SIGNATURE_MSG', "Incorrect Signature\nPayPal: %s\nosC: %s\n\n");
  define('CHECK_TOTAL', 'Validate Total Transaction Amount');
  define('CHECK_TOTAL_MSG', "Incorrect Total\nPayPal: %s\nSession: %s\n\n");
  define('DEBUG', 'Debug');
  define('DEBUG_MSG', "\nOriginal Post\n".EMAIL_SEPARATOR."\n%s\n\n\nReconstructed Post\n".EMAIL_SEPARATOR."\n%s\n\n");
  define('PAYMENT_SEND_MASS_PAYMENT_NOTIFICATION', 'Send Mass Payment Notification Receive');
  define('PAYMENT_SEND_MONEY_DESCRIPTION', 'Money Received');
  define('PAYMENT_SEND_MONEY_DESCRIPTION_MSG', "You have received a payment of %s %s \n".EMAIL_SEPARATOR."\nThis payment was sent by someone from the PayPal website, using the Send Money tab\n\n");
  define('TEST_INCOMPLETE', 'Invalid Test');
  define('TEST_INCOMPLETE_MSG', "An error has occured, mostly likely because the Custom field in the IPN Test Panel did not have a valid transaction id.\n\n\n");
  define('HTTP_ERROR', 'HTTP Error');
  define('HTTP_ERROR_MSG', "An HTTP Error occured during authentication\n".EMAIL_SEPARATOR."\ncurl= %s, socket= %s, domain= %s, port= %s\n\n");
  
  // Paypal Account Verification E-mail
  define('TEXT_PAYPAL_EMAIL_VERIFY_INSTRUCTION_CONTENT', "\n\n\n" . 'Please kindly verify this e-mail account by clicking on the following link or copy and paste the link to your browser.  This link will confirm that you are the owner of this e-mail address and we will not ask for any PayPal  information in the page.  Please understand that this step is taken to protect PayPal account owners from unauthorized charges and this is a one-time process unless you change your PayPal e-mail.' . "\n\n");
  define('TEXT_PAYPAL_PAYMENT_MAKE_EMAIL_CONTENT1', 'You have made a PayPal payment of ');
  define('TEXT_PAYPAL_PAYMENT_MAKE_EMAIL_CONTENT2', ' to OffGamers using the following account:' . "\n");
  define('TEXT_PAYPAL_EMAIL_VERIFY_ENDING_CONTENT', "\n\n\n" . 'If you have not made any purchase at OffGamers, please report this fraudulent <NAME_EMAIL> immediately and we also advise you to report the case to PayPal at their website and change your PayPal password immediately.  Thank you for shopping at ' . STORE_NAME . '.');
  define('TEXT_PAYPAL_VERIFICATION_TITLE', 'PayPal Account Verification');
  define('TEXT_NOTICE_OF_PAYPAL_VERIFICATION_SEND_TITLE', 'PayPal Account Verification Notice');
  define('TEXT_PAYPAL_EMAIL_VERIFICATION_NOTICE_HEADING_CONTENT', "\n\n" . 'In order to protect the owner of the PayPal account from fraudulent use, a verification e-mail with "Subject: ');
  define('TEXT_PAYPAL_EMAIL_VERIFICATION_NOTICE_ENDING_CONTENT', '" has been sent to the e-mail address shown above.  Please kindly check the e-mail account and follow the instructions stated to verify your PayPal e-mail account.  Thank you for helping us to serve you better.');
  define('EMAIL_FOOTER', STORE_EMAIL_SIGNATURE);
  define('EMAIL_CONTACT', 'For any enquiries or assistance, please use our Online Live Support service or e-mail your enquiries to ' . EMAIL_TO . '. Thank you for shopping at ' . STORE_NAME . ".\n\n\n");
  define('REMARK_PAYPAL_VERIFICATION_EMAIL_SENT', 'Paypal verification e-mail sent.');
  define('REMARK_PAYPAL_VERIFICATION_EMAIL_NOTICE_SENT', 'Paypal verification e-mail notice sent.');
  
  define('EMAIL_PAYPAL_PAYMENT_REVIEW_SUBJECT', 'Paypal Payment Review');
  define('EMAIL_PAYPAL_PAYMENT_REVIEW', 'Thank you for shopping at ' . STORE_NAME . '. It has come to our attention that Paypal has frozen payment for this order pending Paypal Review. Please understand that this verification phase is beyond our control. Normally, Paypal Payment Review takes 24 hours or less to clear, but more often, within a few hours. We apologize for the delay, as this is part of Paypal\'s new policy directed toward transactions made for virtual goods.' . "\n\n" . 'For more information, you may contact <a href="https://www.paypal.com/cgi-bin/webscr?cmd=_contact-phone">Paypal</a> to verify this process.' . "\n\n" . 'Alternatively, our Customer Support Agents are available 24/7 to assist you with any queries, via <a href="' . tep_href_link(FILENAME_CUSTOMER_SUPPORT) . '">' . tep_href_link(FILENAME_DEFAULT) . '</a>');
  
  define('TEXT_PAYMENT_ESTIMATE_PAYMENT_RECEIVED', 'Please be informed that your payment has been successfully processed by our Payment Institution service provider, please allow 1-7 business working days for the funds to show up in your account.<br>Kindly contact <a href="mailto:<EMAIL>"><EMAIL></a> if you require further assistance. Thank you.');
  define('EMAIL_PAYMENT_PAYMENT_UPDATE_SUBJECT', implode(' ', array(EMAIL_SUBJECT_PREFIX, "Payment Update #%s")));
  define('TEXT_PAYMENT_ESTIMATE_PAYMENT_RECEIVED_G2G', 'If you do not receive this payment by %s you must contact <a href="mailto:<EMAIL>"><EMAIL></a> by %s or we will assume that the full payment has been received by the intended beneficiary and we will not hold any further responsibility and/or liability.');

?>