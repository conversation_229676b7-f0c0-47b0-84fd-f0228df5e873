<?php
define('TABLE_HEADING_PBBANK_DATE', 'History Date');
define('ENTRY_PBBANK_RETURNED_CODE', 'Returned Code');
define('ENTRY_PBBANK_RETURNED_MESSAGE', 'Returned Message');
define('TABLE_HEADING_PBBANK_CHANGED_BY', 'Changed By');
//Define Merchant Key Language
define('MODULE_PAYMENT_PBBANK_RETAILER_ID', 'Retailer ID');
define('MODULE_PAYMENT_PBBANK_SECRET_KEY', 'Secret Key');
define('MODULE_PAYMENT_TAX', 'Tax');

define('ENTRY_PBBANK_TRANSACTION_RECEIVED_DATE', 'Transaction receive date');
define('ENTRY_PBBANK_TRANSACTION_RETURNED_DATE', 'Transaction return date');
define('ENTRY_PBBANK_AMOUNT', 'Amount');
define('ENTRY_PBBANK_CURRENCY', 'Currency');
define('ENTRY_PBBANK_CONFIRMATION_NUMBER', 'Confirmation Number');
define('ENTRY_PBBANK_ACTIVE', 'From Public Bank actively');
define('ENTRY_PBBANK_CREATED_DATE', 'Created Date');
define('ENTRY_PBBANK_LAST_MODIFIED', 'Last Modified');

?>