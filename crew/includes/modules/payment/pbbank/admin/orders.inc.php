<?php
include_once(DIR_FS_CATALOG_MODULES . 'payment/pbbank/admin/languages/' . $language . '/pbbank.lng.php');
$pbbank_trans_info_select_sql = "SELECT * FROM " . TABLE_PUBLIC_BANK . " WHERE order_id ='" . (int)$oID . "'";
$pbbank_trans_info_result_sql= tep_db_query($pbbank_trans_info_select_sql);
$pbbank_payment_status_history_info_select_sql = "SELECT * FROM " . TABLE_PUBLIC_BANK_STATUS_HISTORY . " WHERE order_id = '" . $oID . "'";
$pbbank_payment_status_history_info_result_sql = tep_db_query($pbbank_payment_status_history_info_select_sql);
?>
<table border="0" width="100%" cellspacing="0" cellpadding="2">
	<?php ob_start();
	?>
	<tr>
		<td class="main">
			<table border="0" width="100%" cellspacing="0" cellpadding="2">
				<tr>
					<td class="main" width="12%" nowrap><b><?php echo ENTRY_PAYMENT_METHOD?></b></td>
					<td class="main">
						<div>
							<?php if((int)$order->info['payment_methods_id']>0) { ?><div class="<?php echo ((int)$payment_info_array[$order->info['payment_methods_id']]->confirm_complete_days>0?'rp_icon':'nrp_icon')?>"><b><?php echo ((int)$payment_info_array[$order->info['payment_methods_id']]->confirm_complete_days>0?'RP':'NRP')?></b></div> <?php } ?>
							<div style="width:30px; height:15px; background-color:<?php echo $payment_module_info['display_colour']?>; float:left; vertical-align:top; text-align:center;">&nbsp;</div>
							<div style="vertical-align: top">
								&nbsp;&nbsp;
								<?php
								if ($view_payment_details_permission) {
									if (tep_not_null($last_payment_method) && $last_payment_method['payment_methods_id'] != $order->info['payment_methods_id']) {
										echo '<span class="redIndicator">'.(tep_not_null($payment_module_info['payment_methods_parent_title'])?$payment_module_info['payment_methods_parent_title'] . ' - ' :'') . $payment_module_info['payment_methods_title'].'</span>';
									} else {
										echo (tep_not_null($payment_module_info['payment_methods_parent_title'])?$payment_module_info['payment_methods_parent_title'] . ' - ' :'') . $payment_module_info['payment_methods_title'];
									}

									if (tep_not_null($last_payment_method) && $last_payment_method['payment_methods_id'] != $order->info['payment_methods_id']) {
										echo '&nbsp;&nbsp;<span class="redIndicator">(Last Payment Method: ' . (tep_not_null($last_payment_method['parent_payment_methods_title'])? $last_payment_method['parent_payment_methods_title'] . ' - ' : '' ) . $last_payment_method['payment_methods_title'] . ')</span>';
									}
								} else {
									echo (tep_not_null($payment_module_info['payment_methods_parent_title'])?$payment_module_info['payment_methods_parent_title'] . ' - ' :'') . $payment_module_info['payment_methods_title'];
								}
								?>
							</div>
						</div>
					</td>
				</tr>
			</table>
		</td>
	</tr>
	<?php
	$payment_method_title_info = ob_get_contents();
	ob_end_clean();
	if (tep_db_num_rows($pbbank_trans_info_result_sql) || tep_db_num_rows($pbbank_payment_status_history_info_result_sql)) {
		$pbbank_trans_info_row = tep_db_fetch_array($pbbank_trans_info_result_sql);
		echo $payment_method_title_info;

		if (!$view_payment_details_permission) {
			;
		} else {
			?>
			<tr>
				<td class="main">
					<table border="0" width="100%" cellspacing="0" cellpadding="2">
						<tr valign="top">
							<td width="39%" nowrap>
								<table border="0" cellspacing="0" cellpadding="2">
									<tr>
										<td class="main" valign="top" nowrap><b><?php echo ENTRY_PBBANK_CONFIRMATION_NUMBER?></b>&nbsp;</td>
										<td class="main" nowrap><?php echo $pbbank_trans_info_row['confirmation_num'] ?></td>
									</tr>
									<tr>
										<td class="main" valign="top" nowrap><b><?php echo ENTRY_PBBANK_AMOUNT?></b>&nbsp;</td>
										<td class="main" nowrap><?php echo $pbbank_trans_info_row['amount']?></td>
									</tr>
									<tr>
										<td class="main" valign="top" nowrap><b><?php echo ENTRY_PBBANK_CURRENCY?></b>&nbsp;</td>
										<td class="main" nowrap><?php echo $pbbank_trans_info_row['currency']?></td>
									</tr>
									<tr>
										<td class="main" valign="top" nowrap><b><?php echo ENTRY_PBBANK_ACTIVE?></b>&nbsp;</td>
										<td class="main" nowrap><?php echo (tep_not_null($pbbank_trans_info_row['active']) ? 'true' : 'false')?></td>
									</tr>
									<tr>
										<td class="main" valign="top" nowrap><b><?php echo ENTRY_PBBANK_TRANSACTION_RECEIVED_DATE?></b>&nbsp;</td>
										<td class="main" nowrap><?php echo $pbbank_trans_info_row['transaction_date_received']?></td>
									</tr>
									<tr>
										<td class="main" valign="top" nowrap><b><?php echo ENTRY_PBBANK_TRANSACTION_RETURNED_DATE?></b>&nbsp;</td>
										<td class="main" nowrap><?php echo $pbbank_trans_info_row['transaction_date_returned']?></td>
									</tr>
									<tr>
										<td class="main" valign="top" nowrap><b><?php echo ENTRY_PBBANK_CREATED_DATE?></b>&nbsp;</td>
										<td class="main" nowrap><?php echo $pbbank_trans_info_row['date_added']?></td>
									</tr>
									<tr>
										<td class="main" valign="top" nowrap><b><?php echo ENTRY_PBBANK_LAST_MODIFIED?></b>&nbsp;</td>
										<td class="main" nowrap><?php echo $pbbank_trans_info_row['date_modified']?></td>
									</tr>
									<tr>
										<td class="main" nowrap>
											<?php
											//HIDE this section due to pbbank exercise 1 IP policy
//											if ($order->info['orders_status'] == '1' || $order->info['orders_status'] == '7' || $order->info['orders_status'] == '2') {
//												echo "&nbsp;&nbsp;";
//												echo tep_draw_form('pbbank_status_check_form', FILENAME_ORDERS, tep_get_all_get_params(), 'post');
//												echo tep_draw_hidden_field('subaction', 'payment_action');
//												echo tep_draw_hidden_field('payment_action', 'check_trans_status');
//												echo tep_submit_button('Check Payment Status', 'Check Payment Status', 'name="pbbankCheckTransStatusBtn"', 'inputButton');
//												echo "</form>";
//											}
											?>
										</td>
									</tr>
								</table>
							</td>
							<td>
								<table border="0" cellspacing="0" cellpadding="1">
									</tr>
									<tr>
										<td colspan="2">
											<table border="1" cellspacing="0" cellpadding="2">
												<tr>
													<td class="smallText" nowrap><b><?php echo TABLE_HEADING_PBBANK_DATE?></b></td>
													<td class="smallText" nowrap><b><?php echo ENTRY_PBBANK_RETURNED_CODE?></b></td>
													<td class="smallText" nowrap><b><?php echo ENTRY_PBBANK_RETURNED_MESSAGE?></b></td>
													<td class="smallText" nowrap><b><?php echo TABLE_HEADING_PBBANK_CHANGED_BY?></b></td>
												</tr>
												<?php		while ($pbbank_payment_status_history_info_row = tep_db_fetch_array($pbbank_payment_status_history_info_result_sql)) {
													echo ' 						<tr>
                							<td class="smallText" nowrap>'.$pbbank_payment_status_history_info_row['date'].'</td>
                							<td class="smallText" nowrap>'.$pbbank_payment_status_history_info_row['return_code'].'</td>
                							<td class="smallText" nowrap>'.$pbbank_payment_status_history_info_row['return_message'].'</td>
                							<td class="smallText" nowrap>'.(tep_not_null($pbbank_payment_status_history_info_row['changed_by'])?$pbbank_payment_status_history_info_row['changed_by']:'&nbsp;').'</td>
                						</tr>';
												}
												?>
											</table>
										</td>
									</tr>
								</table>
							</td>
							<td>
								<table border="0" cellspacing="0" cellpadding="2">
									<tr>
										<td class="main" valign="top" nowrap><b><?php echo ENTRY_PBBANK_CURRENCY?></b>&nbsp;</td>
										<td class="main" nowrap>
											<?php
											if (strtolower($pbbank_trans_info_row['currency']) == strtolower($order->info['currency'])) {
												echo $pbbank_trans_info_row['currency'];
											} else {
												echo '<span class="redIndicator">'.$pbbank_trans_info_row['currency'].'<br><span class="smallText">Does not match purchase currency.</span></span>';
											}
											?>
										</td>
									</tr>
									<tr>
										<td class="main" valign="top" nowrap><b><?php echo ENTRY_PBBANK_AMOUNT?></b>&nbsp;</td>
										<td class="main">
											<?php

											$pbbankGrossAmt = number_format($pbbank_trans_info_row['amount'], $currencies->currencies[$pbbank_trans_info_row['currency']]['decimal_places'], $currencies->currencies[$pbbank_trans_info_row['currency']]['decimal_point'], $currencies->currencies[$pbbank_trans_info_row['currency']]['thousands_point']);
											$pbbankAmountFormatted = $currencies->currencies[$pbbank_trans_info_row['currency']]['symbol_left'] . $pbbankGrossAmt . $currencies->currencies[$pbbank_trans_info_row['currency']]['symbol_right'];
											if ($pbbankAmountFormatted != strip_tags($order->order_totals['ot_total']['text']) ) {
												$mc_gross_display_text = '<span class="redIndicator">'.$pbbankAmountFormatted.'<br><span class="smallText">Does not match purchase amount.</span></span>';
											} else {
												$mc_gross_display_text = $pbbankAmountFormatted;
											}

											echo $mc_gross_display_text;
											?>
										</td>
									</tr>
									<tr>
										<td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
									</tr>
								</table>
							</td>
						</tr>
					</table>
				</td>
			</tr>
			<?php
		}
	} else {
		echo $payment_method_title_info;
		?>
		<tr>
			<td class="main" nowrap>
				<?
				//HIDE this section due to pbbank exercise 1 IP policy
//				if ($order->info['orders_status'] == '1' || $order->info['orders_status'] == '7' || $order->info['orders_status'] == '2') {
//					echo "&nbsp;&nbsp;";
//					echo tep_draw_form('pbbank_status_check_form', FILENAME_ORDERS, tep_get_all_get_params(), 'post');
//					echo tep_draw_hidden_field('subaction', 'payment_action');
//					echo tep_draw_hidden_field('payment_action', 'check_trans_status');
//					echo tep_submit_button('Check Payment Status', 'Check Payment Status', 'name=pbbankCheckTransStatusBtn"', 'inputButton');
//					echo "</form>";
//				}
				?>
			</td>
		</tr>
		<?php
	}
	?>

</table>