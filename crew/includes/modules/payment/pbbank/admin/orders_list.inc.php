<?
include_once(DIR_FS_CATALOG_MODULES . 'payment/pbbank/admin/languages/' . $language . '/pbbank.lng.php');
$pbbank_trans_info_select_sql = "SELECT * FROM " . TABLE_PUBLIC_BANK . " WHERE order_id = '" . $order_obj->orders_id . "'";
$pbbank_trans_info_result_sql = tep_db_query($pbbank_trans_info_select_sql);
?>
<table border="0" width="100%" cellspacing="0" cellpadding="2">
	<tr>
		<td class="subInvoiceBoxHeading">
			<div style="border-bottom: 1px solid #996600; border-top: 1px solid #996600; padding: 2px;">Payment Details</div>
		</td>
	</tr>
	<?
	if ($pbbank_trans_info_row = tep_db_fetch_array($pbbank_trans_info_result_sql)) {
		?>
		<tr>
			<td class="main">
				<table border="0" width="100%" cellspacing="0" cellpadding="2">
					<tr valign="top">
						<td width="50%" valign="top">
							<table border="0" cellspacing="0" cellpadding="2">
								<tr>
									<td class="invoiceRecords" valign="top" nowrap><?= ENTRY_PBBANK_CURRENCY ?></td>
									<td class="invoiceRecords" valign="top">:&nbsp;</td>
									<td class="invoiceRecords" nowrap><?= $pbbank_trans_info_row['currency'] ?></td>
								</tr>
								<tr>
									<td class="invoiceRecords" valign="top" nowrap><?= ENTRY_PBBANK_AMOUNT ?></td>
									<td class="invoiceRecords" valign="top">:&nbsp;</td>
									<td class="invoiceRecords">
										<?php
										$pbbankGrossAmt = number_format($pbbank_trans_info_row['amount'], $currencies->currencies[$pbbank_trans_info_row['currency']]['decimal_places'], $currencies->currencies[$pbbank_trans_info_row['currency']]['decimal_point'], $currencies->currencies[$pbbank_trans_info_row['currency']]['thousands_point']);
										$pbbankAmountFormatted = $currencies->currencies[$pbbank_trans_info_row['currency']]['symbol_left'] . $pbbankGrossAmt . $currencies->currencies[$pbbank_trans_info_row['currency']]['symbol_right'];
										echo $pbbankAmountFormatted;
										?>
									</td>
								</tr>
							</table>
						</td>
						<td valign="top">
							<table border="0" cellspacing="0" cellpadding="2">
								<tr>
									<td class="invoiceRecords" valign="top" nowrap><?= ENTRY_PBBANK_CONFIRMATION_NUMBER ?></td>
									<td class="invoiceRecords" valign="top">:&nbsp;</td>
									<td class="invoiceRecords"><?= (tep_not_null($pbbank_trans_info_row['confirmation_num']) ? $pbbankpbbank_trans_info_row['confirmation_num'] : '') ?></td>
								</tr>
							</table>
						</td>
					</tr>
				</table>
			</td>
		</tr>
		<?
	} else {
		?>
		<tr>
			<td class="invoiceRecords">No further payment information is available.</td>
		</tr>
		<?
	}
	?>