<?php
define('TABLE_HEADING_PIPWAVE_DATE', 'History Date');
define('ENTRY_PIPWAVE_STATUS', 'Status');
define('ENTRY_PIPWAVE_MESSAGE', 'Message');
define('TABLE_HEADING_PIPWAVE_CHANGED_BY', 'Changed By');

define('ENTRY_PIPWAVE_NOTIFICATION_ID', 'Notification ID');
define('ENTRY_PIPWAVE_NOTIFICATION_DATE', 'Notification date');
define('ENTRY_PIPWAVE_AMOUNT', 'Amount');
define('ENTRY_PIPWAVE_CURRENCY', 'Currency');
define('ENTRY_PIPWAVE_PW_ID', 'pipwave ID');
define('ENTRY_PIPWAVE_TAX_EXEMPTED_AMOUNT', 'Tax exempted amount');
define('ENTRY_PIPWAVE_SURCHARGE_AMOUNT', 'Surcharge amount');
define('ENTRY_PIPWAVE_TAX_AMOUNT', 'Tax amount');
define('ENTRY_PIPWAVE_TOTAL_AMOUNT', 'Total amount');
define('ENTRY_PIPWAVE_FINAL_AMOUNT', 'Final amount');
define('ENTRY_PIPWAVE_TRANSACTION_STATUS', 'Transaction status');
define('ENTRY_PIPWAVE_TRANSACTION_SUB_STATUS', 'Transaction sub status');
define('ENTRY_PIPWAVE_TYPE', 'Type');
define('ENTRY_PIPWAVE_PAYMENT_METHOD_CODE', 'Payment method code');
define('ENTRY_PIPWAVE_REQUIRED_CAPTURE', 'Required capture');
define('ENTRY_PIPWAVE_REVERSIBLE_PAYMENT', 'RP');
define('ENTRY_PIPWAVE_MOBILE_NUMBER', 'Mobile number');
define('ENTRY_PIPWAVE_MOBILE_NUMBER_VERIFICATION', 'Mobile verified');
define('ENTRY_PIPWAVE_PG_STATUS', 'PG status');
define('ENTRY_PIPWAVE_PG_REASON', 'PG reason');
define('ENTRY_PIPWAVE_PG_DATE', 'PG date');
define('ENTRY_PIPWAVE_AFT_SCORE', 'pipwave Score');
define('ENTRY_PIPWAVE_AFT_STATUS', 'Rule Action');
define('ENTRY_PIPWAVE_RISK_SERVICE_TYPE', 'Risk Service Type');
define('ENTRY_PIPWAVE_PG_TXN_ID', 'Payment Transaction ID');
define('ENTRY_CC_NUMBER', 'Credit Card Number');
define('ENTRY_CUSTOMER', 'Customer');
define('ENTRY_PAYPAL_EMAIL_ADDRESS', 'Paypal Email');
define('ENTRY_PAYER_ID', 'Payer ID');
define('ENTRY_PAYER_STATUS', 'Payer Status');
define('ENTRY_PAYPAL_IPN_TXN', 'Paypal Txn ID');
define('ENTRY_RESIDENCE_COUNTRY', 'Residence Country');
define('ENTRY_PAYPAL_ADDRESS', 'Paypal Address');
define('ENTRY_PAYPAL_PENDING_REASON', 'Pending Reason');
define('ENTRY_PAYPAL_PROTECTION_ELIGIBLE', 'Protection Eligibility');
define('ENTRY_WEBMONEY_TXN_ID', 'Webmoney Transaction ID');
define('ENTRY_CC_ISSUER_COUNTRY', 'Credit Card Issuer Country');
define('ENTRY_CC_EXPIRY_DATE', 'Credit Card Expiry Date');
define('ENTRY_CC_AUTHENTICATION_RESULT', '3D Authenticated');
define('ENTRY_CC_AUTHENTICATION_OFFERED', '3D Offered');
define('ENTRY_CC_AUTHENTICATION_SUCCESS', 'Successfully authenticated');
define('ENTRY_CC_AUTHENTICATION_FAILED', 'Failed authenticated');
define('ENTRY_CC_AUTHENTICATION_NORECORD', 'No Record');
define('ENTRY_CC_AUTHENTICATION_NOT_ENROLLED', 'Bank/card not enrolled');
define('ENTRY_MB_TRANSACTION_ID', 'Skrill Transaction ID');
define('ENTRY_MB_PAYER_EMAIL', 'Skrill Payer Email');
define('TABLE_HEADING_RISK_TYPE', 'Risk Type');
define('TABLE_HEADING_RISK_VALUE', 'Risk Value');
define('TABLE_HEADING_RULES_TITLE', 'Rules Title');
define('TABLE_HEADING_RULES_RESULT', 'Rules Result');
define('TEXT_PG_COUNTRY_NOT_MATCH_PHONE_COUNTRY', 'Telephone country(%s) does not match PG country.');
define('EMAIL_MB_VERIFY_SEND_LINK', 'Click here to send MoneyBookers verification e-mail');

?>