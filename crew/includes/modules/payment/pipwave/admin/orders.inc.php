<?php
include_once(DIR_FS_CATALOG_MODULES . 'payment/pipwave/admin/languages/' . $language . '/pipwave.lng.php');
include_once(DIR_WS_CLASSES . 'pipwave.php');
include_once(DIR_WS_CLASSES . 'g2g_serverless.php');
$pipwave_trans_info_select_sql = "SELECT * FROM " . TABLE_PIPWAVE_PAYMENT . " WHERE orders_id ='" . (int)$oID . "'";
$pipwave_trans_info_result_sql= tep_db_query($pipwave_trans_info_select_sql);

$pipwave_payment_status_history_info_select_sql = " SELECT notification_date, status, message, changed_by
                                                    FROM " . TABLE_PIPWAVE_PAYMENT_HISTORY . " WHERE orders_id = '" . (int)$oID . "'";
$pipwave_payment_status_history_info_result_sql = tep_db_query($pipwave_payment_status_history_info_select_sql);

$pipwave = new pipwave($oID);
?>
<table border="0" width="100%" cellspacing="0" cellpadding="2">
    <?php ob_start();
    ?>
    <tr>
        <td class="main">
            <table border="0" width="100%" cellspacing="0" cellpadding="2">
                <tr>
                    <td class="main" width="12%" nowrap><b><?php echo ENTRY_PAYMENT_METHOD?></b></td>
                    <td class="main">
                        <div>
                            <?php
                                if((int)$order->info['payment_methods_id']>0) {
                                    $pmInfo = $pipwave->pipwavePaymentMapper('pm_id',$order->info['payment_methods_id']);
                            ?>
                                    <div class="<?php echo ((int)$pmInfo['is_rp'] > 0?'rp_icon':'nrp_icon')?>"><b>
                            <?php
                                if(isset($pmInfo['is_rp'])) {
                                    switch ($pmInfo['is_rp'] ) {
                                        case 0:
                                            echo 'NRP';
                                            break;
                                        case 1:
                                            echo 'RP';
                                            break;
                                        case 2:
                                            echo 'Semi-RP';
                                            break;
                                    }
                                }
                            ?>
                                    </b></div>
                            <?php } ?>
                            <div style="width:30px; height:15px; background-color:<?php echo $payment_module_info['display_colour']?>; float:left; vertical-align:top; text-align:center;">&nbsp;</div>
                            <div style="vertical-align: top">
                                &nbsp;&nbsp;
                                <?php
                                if ($view_payment_details_permission) {
                                    if (tep_not_null($last_payment_method) && $last_payment_method['payment_methods_id'] != $order->info['payment_methods_id']) {
                                        echo '<span class="redIndicator">'.(tep_not_null($payment_module_info['payment_methods_parent_title'])?$payment_module_info['payment_methods_parent_title'] . ' - ' :'') . $payment_module_info['payment_methods_title'].'</span>';
                                    } else {
                                        echo (tep_not_null($payment_module_info['payment_methods_parent_title'])?$payment_module_info['payment_methods_parent_title'] . ' - ' :'') . $payment_module_info['payment_methods_title'];
                                    }

                                    if (tep_not_null($last_payment_method) && $last_payment_method['payment_methods_id'] != $order->info['payment_methods_id']) {
                                        echo '&nbsp;&nbsp;<span class="redIndicator">(Last Payment Method: ' . (tep_not_null($last_payment_method['parent_payment_methods_title'])? $last_payment_method['parent_payment_methods_title'] . ' - ' : '' ) . $last_payment_method['payment_methods_title'] . ')</span>';
                                    }
                                } else {
                                    echo (tep_not_null($payment_module_info['payment_methods_parent_title'])?$payment_module_info['payment_methods_parent_title'] . ' - ' :'') . $payment_module_info['payment_methods_title'];
                                }
                                ?>
                            </div>
                        </div>
                    </td>
                </tr>
            </table>
        </td>
    </tr>
    <?php
    $payment_method_title_info = ob_get_contents();
    ob_end_clean();
    if (tep_db_num_rows($pipwave_trans_info_result_sql) || tep_db_num_rows($pipwave_payment_status_history_info_result_sql)) {
        $pipwave_trans_info_row = tep_db_fetch_array($pipwave_trans_info_result_sql);
        if(!is_array($pmInfo)) {
            if(isset($pipwave_trans_info_row['payment_method_code'])) {
                $pmInfo = $pipwave->pipwavePaymentMapper('pipwave',$pipwave_trans_info_row['payment_method_code']);
            }
        }
        $pgRawData = array();
        if(isset($pipwave_trans_info_row['pg_raw_data'])) {
            $pgRawData = json_decode($pipwave_trans_info_row['pg_raw_data'],1);
        }
        echo $payment_method_title_info;

        if (!$view_payment_details_permission) {
            ;
        } else {
            ?>
            <tr>
                <td class="main">
                    <table border="0" width="100%" cellspacing="0" cellpadding="2">
                        <tr valign="top">
                            <td width="35%" nowrap>
                                <table border="0" cellspacing="0" cellpadding="2">
                                    <tr>
                                        <td class="main" valign="top" nowrap><b><?php echo ENTRY_PIPWAVE_STATUS?></b>&nbsp;</td>
                                        <td class="main" nowrap><?php echo $pipwave_trans_info_row['status'] ?></td>
                                    </tr>
<!--                                    <tr>-->
<!--                                        <td class="main" valign="top" nowrap><b>--><?php //echo ENTRY_PIPWAVE_NOTIFICATION_ID?><!--</b>&nbsp;</td>-->
<!--                                        <td class="main" nowrap>--><?php //echo $pipwave_trans_info_row['notification_id']?><!--</td>-->
<!--                                    </tr>-->
                                    <tr>
                                        <td class="main" valign="top" nowrap><b><?php echo ENTRY_PIPWAVE_NOTIFICATION_DATE?></b>&nbsp;</td>
                                        <td class="main" nowrap><?php echo $pipwave_trans_info_row['notification_date']?></td>
                                    </tr>
                                    <tr>
                                        <td class="main" valign="top" nowrap><b><?php echo ENTRY_PIPWAVE_PW_ID?></b>&nbsp;</td>
                                        <td class="main" nowrap><?php echo $pipwave_trans_info_row['pw_id']?></td>
                                    </tr>
<!--                                    <tr>-->
<!--                                        <td class="main" valign="top" nowrap><b>--><?php //echo ENTRY_PIPWAVE_AMOUNT?><!--</b>&nbsp;</td>-->
<!--                                        <td class="main" nowrap>--><?php //echo $pipwave_trans_info_row['amount']?><!--</td>-->
<!--                                    </tr>-->
                                    <tr>
                                        <td class="main" valign="top" nowrap><b><?php echo ENTRY_PIPWAVE_TAX_EXEMPTED_AMOUNT?></b>&nbsp;</td>
                                        <td class="main" nowrap><?php echo $pipwave_trans_info_row['tax_exempted_amount']?></td>
                                    </tr>
<!--                                    <tr>-->
<!--                                        <td class="main" valign="top" nowrap><b>--><?php //echo ENTRY_PIPWAVE_SURCHARGE_AMOUNT?><!--</b>&nbsp;</td>-->
<!--                                        <td class="main" nowrap>--><?php //echo $pipwave_trans_info_row['surcharge_amount']?><!--</td>-->
<!--                                    </tr>-->
<!--                                    <tr>-->
<!--                                        <td class="main" valign="top" nowrap><b>--><?php //echo ENTRY_PIPWAVE_TAX_AMOUNT?><!--</b>&nbsp;</td>-->
<!--                                        <td class="main" nowrap>--><?php //echo $pipwave_trans_info_row['tax_amount']?><!--</td>-->
<!--                                    </tr>-->
<!--                                    <tr>-->
<!--                                        <td class="main" valign="top" nowrap><b>--><?php //echo ENTRY_PIPWAVE_TOTAL_AMOUNT?><!--</b>&nbsp;</td>-->
<!--                                        <td class="main" nowrap>--><?php //echo $pipwave_trans_info_row['total_amount']?><!--</td>-->
<!--                                    </tr>-->
<!--                                    <tr>-->
<!--                                        <td class="main" valign="top" nowrap><b>--><?php //echo ENTRY_PIPWAVE_FINAL_AMOUNT?><!--</b>&nbsp;</td>-->
<!--                                        <td class="main" nowrap>--><?php //echo $pipwave_trans_info_row['final_amount']?><!--</td>-->
<!--                                    </tr>-->
<!--                                    <tr>-->
<!--                                        <td class="main" valign="top" nowrap><b>--><?php //echo ENTRY_PIPWAVE_CURRENCY?><!--</b>&nbsp;</td>-->
<!--                                        <td class="main" nowrap>--><?php //echo $pipwave_trans_info_row['currency_code']?><!--</td>-->
<!--                                    </tr>-->
                                    <?php
                                    $txnStatus = ($pipwave_trans_info_row['transaction_status']) ? $pipwave->paymentStatusMapper($pipwave_trans_info_row['transaction_status']) : '';
                                    switch($txnStatus) {
                                        case 'Failed': //failed
                                        case 'Cancelled': //cancelled
                                        $txnStatus = '<span style="background-color:red;color:white;padding:2px;">' . $txnStatus . '</span>';
                                            break;
                                    }
                                    ?>
                                    <tr>
                                        <td class="main" valign="top" nowrap><b><?php echo ENTRY_PIPWAVE_TRANSACTION_STATUS?></b>&nbsp;</td>
                                        <td class="main" nowrap><?php echo $txnStatus?></td>
                                    </tr>
                                    <tr>
                                        <td class="main" valign="top" nowrap><b><?php echo ENTRY_PIPWAVE_TRANSACTION_SUB_STATUS?></b>&nbsp;</td>
                                        <td class="main" nowrap><b><?php echo $pipwave->paymentSubStatusMapper($pipwave_trans_info_row['txn_sub_status'])?></b></td>
                                    </tr>
<!--                                    <tr>-->
<!--                                        <td class="main" valign="top" nowrap><b>--><?php //echo ENTRY_PIPWAVE_TYPE?><!--</b>&nbsp;</td>-->
<!--                                        <td class="main" nowrap>--><?php //echo $pipwave_trans_info_row['type']?><!--</td>-->
<!--                                    </tr>-->
<!--                                    <tr>-->
<!--                                        <td class="main" valign="top" nowrap><b>--><?php //echo ENTRY_PIPWAVE_PAYMENT_METHOD_CODE?><!--</b>&nbsp;</td>-->
<!--                                        <td class="main" nowrap>--><?php //echo $pipwave_trans_info_row['payment_method_code']?><!--</td>-->
<!--                                    </tr>-->
<!--                                    <tr>-->
<!--                                        <td class="main" valign="top" nowrap><b>--><?php //echo ENTRY_PIPWAVE_REQUIRED_CAPTURE?><!--</b>&nbsp;</td>-->
<!--                                        <td class="main" nowrap>--><?php //echo ($pipwave_trans_info_row['require_capture'] == 1) ? 'TRUE' : 'FALSE'?><!--</td>-->
<!--                                    </tr>-->
<!--                                    <tr>-->
<!--                                        <td class="main" valign="top" nowrap><b>--><?php //echo ENTRY_PIPWAVE_REVERSIBLE_PAYMENT?><!--</b>&nbsp;</td>-->
<!--                                        <td class="main" nowrap>--><?php //echo ($pipwave_trans_info_row['reversible_payment'] == 1) ? 'TRUE' : 'FALSE'?><!--</td>-->
<!--                                    </tr>-->
                                    <tr>
                                        <td class="main" valign="top" nowrap><b><?php echo ENTRY_PIPWAVE_MOBILE_NUMBER?></b>&nbsp;</td>
                                        <td class="main" nowrap><?php echo $pipwave_trans_info_row['mobile_number']?></td>
                                    </tr>
                                    <tr>
                                        <td class="main" valign="top" nowrap><b><?php echo ENTRY_PIPWAVE_MOBILE_NUMBER_VERIFICATION?></b>&nbsp;</td>
                                        <td class="main" nowrap>
                                            <?php
                                                if(isset($pipwave_trans_info_row['mobile_number_verification']) && !empty($pipwave_trans_info_row['mobile_number_verification'])) {
                                                    switch($pipwave_trans_info_row['mobile_number_verification']) {
                                                        case 0:
                                                            echo 'skipped';
                                                            break;
                                                        case 1:
                                                            echo 'verified';
                                                            break;
                                                        case 2:
                                                            echo 'verified previously';
                                                            break;
                                                        case 3:
                                                            echo 'unverified';
                                                            break;
                                                    }
                                                }
                                            ?>
                                        </td>
                                    </tr>
<!--                                    <tr>-->
<!--                                        <td class="main" valign="top" nowrap><b>--><?php //echo ENTRY_PIPWAVE_PG_STATUS?><!--</b>&nbsp;</td>-->
<!--                                        <td class="main" nowrap>--><?php //echo $pipwave_trans_info_row['pg_status']?><!--</td>-->
<!--                                    </tr>-->
<!--                                    <tr>-->
<!--                                        <td class="main" valign="top" nowrap><b>--><?php //echo ENTRY_PIPWAVE_PG_REASON?><!--</b>&nbsp;</td>-->
<!--                                        <td class="main" nowrap>--><?php //echo $pipwave_trans_info_row['pg_reason']?><!--</td>-->
<!--                                    </tr>-->
<!--                                    <tr>-->
<!--                                        <td class="main" valign="top" nowrap><b>--><?php //echo ENTRY_PIPWAVE_PG_DATE?><!--</b>&nbsp;</td>-->
<!--                                        <td class="main" nowrap>--><?php //echo $pipwave_trans_info_row['pg_date']?><!--</td>-->
<!--                                    </tr>-->
                                    <?php
                                        $score = ($pipwave_trans_info_row['pipwave_score']) ? (int)$pipwave_trans_info_row['pipwave_score'] : 'n/a';
                                        if(is_int($score)) {
                                            if($score < 40) {
                                                $score = '<span style="color:green;font-weight: bold">' . $score . '</span>';
                                            } else if ($score >= 40 && $score <= 70) {
                                                $score = '<span style="color:orange;font-weight: bold">' . $score . '</span>';
                                            } else if ($score > 70) {
                                                $score = '<span style="color:red;font-weight: bold">' . $score . '</span>';
                                            }
                                        }
                                    ?>
                                    <tr>
                                        <td class="main" valign="top" nowrap><b><?php echo ENTRY_PIPWAVE_AFT_SCORE?></b>&nbsp;</td>
                                        <td class="main" nowrap><?php echo $score?></td>
                                    </tr>
                                    <tr>
                                        <td class="main" valign="top" nowrap><b><?php echo ENTRY_PIPWAVE_AFT_STATUS?></b>&nbsp;</td>
                                        <td class="main" nowrap><?php echo $pipwave_trans_info_row['rules_action']?></td>
                                    </tr>
                                    <tr>
                                        <td class="main" valign="top" nowrap><b><?php echo ENTRY_PIPWAVE_RISK_SERVICE_TYPE?></b>&nbsp;</td>
                                        <td class="main" nowrap><?php echo $pipwave_trans_info_row['risk_service_type']?></td>
                                    </tr>
                                    <tr>
                                        <td class="main" valign="top" nowrap><b><?php echo ENTRY_PIPWAVE_PG_TXN_ID?></b>&nbsp;</td>
                                        <td class="main" nowrap><?php echo $pipwave_trans_info_row['pg_txn_id']?></td>
                                    </tr>
                                    <tr>
                                        <td class="main" nowrap>
                                            <?php
                                            if ($order->info['orders_status'] == '1' || $order->info['orders_status'] == '7' || $order->info['orders_status'] == '2') {
                                                echo "&nbsp;&nbsp;";
                                                echo tep_draw_form('pipwave_status_check_form', FILENAME_ORDERS, tep_get_all_get_params(), 'post');
                                                echo tep_draw_hidden_field('subaction', 'payment_action');
                                                echo tep_draw_hidden_field('payment_action', 'check_trans_status');
                                                echo tep_submit_button('Check Payment Status', 'Check Payment Status', 'name="pipwaveCheckTransStatusBtn"', 'inputButton');
                                                echo "</form>";
                                            }
                                            if ($order->info['site_id'] == 0 && $order->info['orders_status'] == '1' && empty($order->info['payment_methods_id'])) {
                                                echo "&nbsp;&nbsp;";
                                                echo tep_draw_form('pipwave_status_check_form', FILENAME_ORDERS, tep_get_all_get_params(), 'post');
                                                echo tep_draw_hidden_field('subaction', 'check_payment_method');
                                                echo tep_submit_button('Check Offline Payment', 'Offline Payment', 'name="checkoutCheckPaymentBtn"', 'inputButton');
                                                echo "</form>";
                                            }
                                            //only take care of showing Post-Captured button if it is in Verifying and was Pre-Captured
                                            if ($order->info['orders_status'] == '7' && $pipwave_trans_info_row['require_capture'] == 1) {
                                                $manual_authorise_permission = tep_admin_files_actions(FILENAME_ORDERS, 'ADM_WORLDPAY_MANUAL_AUTHORISATION');
                                                echo '	<td class="main">';
                                                if ($manual_authorise_permission) {
                                                    $pwUrl = PIPWAVE_PAYMENT_TRANSACTION_REPORT_URL . '?pw_id=' . $pipwave_trans_info_row['pw_id'];
                                                    echo tep_draw_form('manual_post_auth_order', FILENAME_ORDERS, tep_get_all_get_params(), 'post');
                                                    echo tep_draw_hidden_field('subaction', 'manual_post_authorisation');
                                                    echo '<input type="submit" name="ManualPostCaptureBtn" value="Manual Captured" title="Post Captured Manually" class="inputButton" onClick="if (confirm(\'Are you sure that you had post captured this order via the pipwave Merchant Interface?\')) { openNewWin(\''.$pwUrl.'\', \'pipwave Admin\', \'location=1,scrollbars=1,status=1,menubar=1,resizable=1,directories=1,width=1024,height=768\'); this.disabled=true;this.value=\'Please wait...\';this.form.submit(); } else { return false; }">';
                                                    echo '</form>';
                                                }
                                                echo '</td>';
											    echo '<td class="main">';
                                                echo tep_draw_form('post_capture_order', FILENAME_ORDERS, tep_get_all_get_params(), 'post');
                                                echo tep_draw_hidden_field('subaction', 'post_authorisation');
                                                echo '<input type="submit" name="PostCaptureBtn" value="Post Capture & Process" title="Post Authorise" class="inputButton" onClick="if (confirm(\'Are you sure to post captured this order remotely?\')) { this.disabled=true;this.value=\'Please wait...\';this.form.submit(); } else { return false; }">';
                                                echo "</form>";
                                                echo '</td>';
                                            }
                                            ?>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                            <td>
                                <table border="0" cellspacing="0" cellpadding="1">
                                    </tr>
                                    <tr>
                                        <td colspan="2">
                                            <table border="1" cellspacing="0" cellpadding="2">
                                                <tr>
                                                    <td class="smallText" nowrap><b><?php echo TABLE_HEADING_PIPWAVE_DATE?></b></td>
                                                    <td class="smallText" nowrap><b><?php echo ENTRY_PIPWAVE_STATUS?></b></td>
                                                    <td class="smallText" nowrap><b><?php echo ENTRY_PIPWAVE_MESSAGE?></b></td>
                                                    <td class="smallText" nowrap><b><?php echo TABLE_HEADING_PIPWAVE_CHANGED_BY?></b></td>
                                                </tr>
                                                <?php		while ($pipwave_payment_status_history_info_row = tep_db_fetch_array($pipwave_payment_status_history_info_result_sql)) {
                                                    echo ' 						<tr>
                							<td class="smallText" nowrap>'.$pipwave_payment_status_history_info_row['notification_date'].'</td>
                							<td class="smallText" nowrap>'.$pipwave_payment_status_history_info_row['status'].'</td>
                							<td class="smallText" nowrap>'.$pipwave_payment_status_history_info_row['message'].'</td>
                							<td class="smallText" nowrap>'.(tep_not_null($pipwave_payment_status_history_info_row['changed_by'])?$pipwave_payment_status_history_info_row['changed_by']:'&nbsp;').'</td>
                						</tr>';
                                                }
                                                ?>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <?php
                                        if(isset($pmInfo['pg_code'])) {
                                            switch($pmInfo['pg_code']) {
                                                case 'pipwavePG':
                                                    if(isset($pmInfo['pg_display_name']) && !empty($pmInfo['pg_display_name'])) {
                                                        switch ($pmInfo['pg_display_name']) {
                                                            case 'SafeCharge':
                                                                $cc_select_sql = "	SELECT *
                                                                        FROM " . TABLE_ANALYSIS_CREDIT_CARD_INFO . "
                                                                        WHERE orders_id = '" . (int)$order->order_id . "'";
                                                                $cc_result_sql = tep_db_query($cc_select_sql);
                                                                $cc_row = tep_db_fetch_array($cc_result_sql);
                                                                if (tep_not_null($cc_row['bin_number']) && tep_not_null($cc_row['card_summary'])) {
                                                                    $card_number = $cc_row['bin_number'] . '******' . $cc_row['card_summary'];
                                                                    echo '<tr><td class="main" valign="top" nowrap><b>'. ENTRY_CC_NUMBER .'</b>&nbsp;</td>';
                                                                    echo '<td class="main" nowrap>';
                                                                    echo $card_number;
                                                                    $check_credit_card_array = array();
                                                                    $check_credit_card_select_sql = "
	                                                                    SELECT DISTINCT o.customers_id
																		FROM " . TABLE_ORDERS . " AS o
																		INNER JOIN " . TABLE_ADYEN . " AS a
																			ON o.orders_id = a.adyen_order_id
																		WHERE a.adyen_cc_card_bin = '" . tep_db_input($cc_row['bin_number']) . "'
																			AND a.adyen_cc_card_summary = '" . tep_db_input($cc_row['card_summary']) . "'
																		UNION
																		SELECT DISTINCT o.customers_id
																		FROM " . TABLE_ORDERS . " AS o
																		INNER JOIN " . TABLE_ANALYSIS_CREDIT_CARD_INFO . " AS a
																			ON o.orders_id = a.orders_id
																		WHERE a.bin_number = '" . tep_db_input($cc_row['bin_number']) . "'
																			AND a.card_summary = '" . tep_db_input($cc_row['card_summary']) . "'
																		";
                                                                    $check_credit_card_result_sql = tep_db_query($check_credit_card_select_sql);
                                                                    while ($check_credit_card_row = tep_db_fetch_array($check_credit_card_result_sql)) {
                                                                        $check_credit_card_array[] = $check_credit_card_row['customers_id'];
                                                                    }
                                                                    if (count($check_credit_card_array)>1) {
                                                                        echo 	tep_draw_form('cust_lists_share_cc', FILENAME_CUSTOMERS, 'action=show_report&customer_lists_subaction=do_search', 'post', 'target="_blank"') . "\n" .
                                                                            tep_draw_hidden_field('customer_share_id', tep_array_serialize($check_credit_card_array)) . "\n" .
                                                                            "<BR><span class='redIndicator'>Same credit card used in <a href='javascript:document.cust_lists_share_cc.submit();'>" . count($check_credit_card_array) . "</a> profiles.</span>" .
                                                                            '</form>' . "\n";
                                                                    }
                                                                    $cc_check_info = array(	'card_number' => $card_number,
                                                                        'date_purchased' => $order->info['date_purchased'],
                                                                        'orders_id' => $order->order_id,
                                                                        'customers_id' => $order->customer['id']);
                                                                    $cc_verified_date = tep_get_payment_info_verified_date($cc_check_info, 'credit_card');
                                                                    echo "<br><span class='redIndicator'>First successful verified date by this user: ".(tep_not_null($cc_verified_date)?$cc_verified_date:"NEVER")."</span><br>(Data can only traced back to 2009-12-09)</span></td></tr>";
                                                                }
                                                                if(isset($cc_row['expiry'])) {
                                                                    echo '<tr><td class="main" valign="top" nowrap><b>'.ENTRY_CC_EXPIRY_DATE.'</b>&nbsp;</td>';
                                                                    echo '<td class="main" nowrap>'.$cc_row['expiry'].'</td></tr>';
                                                                }
                                                                if(isset($cc_row['issuer_country'])) {
                                                                    echo '<tr><td class="main" valign="top" nowrap><b>'.ENTRY_CC_ISSUER_COUNTRY.'</b>&nbsp;</td>';
                                                                    $country_info = tep_get_countries_info($cc_row['issuer_country'], 'countries_iso_code_2');
                                                                    if (isset($order->customer['telephone_country']) && !empty($order->customer['telephone_country']) && $order->customer['telephone_country'] != $country_info['countries_name']) {
                                                                        echo '<td class="main" nowrap>'.$cc_row['issuer_country']. ' <span class="redIndicator"> (' . sprintf(TEXT_PG_COUNTRY_NOT_MATCH_PHONE_COUNTRY, $order->customer['telephone_country']) . ')</span></td>';
                                                                    } else {
                                                                        echo '<td class="main" nowrap>'.$cc_row['issuer_country'].'</td>';
                                                                    }
                                                                    echo '</tr>';
                                                                }
                                                                if(isset($cc_row['three_d_result'])) {
                                                                    echo '<tr><td class="main" valign="top" nowrap><b>'.ENTRY_CC_AUTHENTICATION_RESULT.'</b>&nbsp;</td>';
                                                                    echo '<td class="main" nowrap>'. ($cc_row['three_d_result'] == 'true' ? 'Yes' : 'No').'</td></tr>';
                                                                }
                                                                if(isset($cc_row['three_d_offered'])) {
                                                                    echo '<tr><td class="main" valign="top" nowrap><b>'.ENTRY_CC_AUTHENTICATION_OFFERED.'</b>&nbsp;</td>';
                                                                    echo '<td class="main" nowrap>'.($cc_row['three_d_offered'] == 'true' ? 'Yes' : 'No').'</td></tr>';
                                                                }
                                                                break;
                                                            default:
                                                                $cc_select_sql = "  SELECT *
                                                                        FROM " . TABLE_ANALYSIS_CREDIT_CARD_INFO . "
                                                                        WHERE orders_id = '" . (int)$order->order_id . "'";
                                                                $cc_result_sql = tep_db_query($cc_select_sql);
                                                                $cc_row = tep_db_fetch_array($cc_result_sql);
                                                                if (tep_not_null($cc_row['bin_number']) && tep_not_null($cc_row['card_summary'])) {
                                                                    $card_number = $cc_row['bin_number'] . '******' . $cc_row['card_summary'];
                                                                    echo '<tr><td class="main" valign="top" nowrap><b>'. ENTRY_CC_NUMBER .'</b>&nbsp;</td>';
                                                                    echo '<td class="main" nowrap>';
                                                                    echo $card_number;
                                                                    $check_credit_card_array = array();
                                                                    $check_credit_card_select_sql = "
                                                                        SELECT DISTINCT o.customers_id
                                                                        FROM " . TABLE_ORDERS . " AS o
                                                                        INNER JOIN " . TABLE_ADYEN . " AS a
                                                                            ON o.orders_id = a.adyen_order_id
                                                                        WHERE a.adyen_cc_card_bin = '" . tep_db_input($cc_row['bin_number']) . "'
                                                                            AND a.adyen_cc_card_summary = '" . tep_db_input($cc_row['card_summary']) . "'
                                                                        UNION
                                                                        SELECT DISTINCT o.customers_id
                                                                        FROM " . TABLE_ORDERS . " AS o
                                                                        INNER JOIN " . TABLE_ANALYSIS_CREDIT_CARD_INFO . " AS a
                                                                            ON o.orders_id = a.orders_id
                                                                        WHERE a.bin_number = '" . tep_db_input($cc_row['bin_number']) . "'
                                                                            AND a.card_summary = '" . tep_db_input($cc_row['card_summary']) . "'
                                                                        ";
                                                                    $check_credit_card_result_sql = tep_db_query($check_credit_card_select_sql);
                                                                    while ($check_credit_card_row = tep_db_fetch_array($check_credit_card_result_sql)) {
                                                                        $check_credit_card_array[] = $check_credit_card_row['customers_id'];
                                                                    }
                                                                    if (count($check_credit_card_array)>1) {
                                                                        echo    tep_draw_form('cust_lists_share_cc', FILENAME_CUSTOMERS, 'action=show_report&customer_lists_subaction=do_search', 'post', 'target="_blank"') . "\n" .
                                                                            tep_draw_hidden_field('customer_share_id', tep_array_serialize($check_credit_card_array)) . "\n" .
                                                                            "<BR><span class='redIndicator'>Same credit card used in <a href='javascript:document.cust_lists_share_cc.submit();'>" . count($check_credit_card_array) . "</a> profiles.</span>" .
                                                                            '</form>' . "\n";
                                                                    }
                                                                    $cc_check_info = array( 'card_number' => $card_number,
                                                                        'date_purchased' => $order->info['date_purchased'],
                                                                        'orders_id' => $order->order_id,
                                                                        'customers_id' => $order->customer['id']);
                                                                    $cc_verified_date = tep_get_payment_info_verified_date($cc_check_info, 'credit_card');
                                                                    echo "<br><span class='redIndicator'>First successful verified date by this user: ".(tep_not_null($cc_verified_date)?$cc_verified_date:"NEVER")."</span><br>(Data can only traced back to 2009-12-09)</span></td></tr>";
                                                                }
                                                                if(isset($cc_row['expiry'])) {
                                                                    echo '<tr><td class="main" valign="top" nowrap><b>'.ENTRY_CC_EXPIRY_DATE.'</b>&nbsp;</td>';
                                                                    echo '<td class="main" nowrap>'.$cc_row['expiry'].'</td></tr>';
                                                                }
                                                                if(isset($cc_row['issuer_country'])) {
                                                                    echo '<tr><td class="main" valign="top" nowrap><b>'.ENTRY_CC_ISSUER_COUNTRY.'</b>&nbsp;</td>';
                                                                    $country_info = tep_get_countries_info($cc_row['issuer_country'], 'countries_iso_code_2');
                                                                    if (isset($order->customer['telephone_country']) && !empty($order->customer['telephone_country']) && $order->customer['telephone_country'] != $country_info['countries_name']) {
                                                                        echo '<td class="main" nowrap>'.$cc_row['issuer_country']. ' <span class="redIndicator"> (' . sprintf(TEXT_PG_COUNTRY_NOT_MATCH_PHONE_COUNTRY, $order->customer['telephone_country']) . ')</span></td>';
                                                                    } else {
                                                                        echo '<td class="main" nowrap>'.$cc_row['issuer_country'].'</td>';
                                                                    }
                                                                    echo '</tr>';
                                                                }
                                                                if(isset($cc_row['three_d_result']) && isset($cc_row['three_d_offered'])) {
                                                                    if ($cc_row['three_d_result'] == 'true' && $cc_row['three_d_offered'] == 'true') {
                                                                        $ccResultText = ENTRY_CC_AUTHENTICATION_SUCCESS;
                                                                    } elseif ($cc_row['three_d_result'] == 'false' && $cc_row['three_d_offered'] == 'true') {
                                                                        $ccResultText = ENTRY_CC_AUTHENTICATION_FAILED;
                                                                    } elseif ($cc_row['three_d_offered'] == 'false') {
                                                                        $ccResultText = ENTRY_CC_AUTHENTICATION_NOT_ENROLLED;
                                                                    } else {
                                                                        $ccResultText = ENTRY_CC_AUTHENTICATION_NORECORD;
                                                                    }
                                                                    echo '<tr><td class="main" valign="top" nowrap><b>'.ENTRY_CC_AUTHENTICATION_RESULT.'</b>&nbsp;</td>';
                                                                    echo '<td class="main" nowrap>'. $ccResultText .'</td></tr>';
                                                                }
                                                                break;
                                                        }
                                                    }
                                                    break;
                                                case 'adyen':
                                                    $cc_select_sql = "	SELECT *
                                                                        FROM " . TABLE_ANALYSIS_CREDIT_CARD_INFO . "
                                                                        WHERE orders_id = '" . (int)$order->order_id . "'";
                                                    $cc_result_sql = tep_db_query($cc_select_sql);
                                                    $cc_row = tep_db_fetch_array($cc_result_sql);
                                                    if (tep_not_null($cc_row['bin_number']) && tep_not_null($cc_row['card_summary'])) {
                                                        $card_number = $cc_row['bin_number'] . '******' . $cc_row['card_summary'];
                                                        echo '<tr><td class="main" valign="top" nowrap><b>'. ENTRY_CC_NUMBER .'</b>&nbsp;</td>';
                                                        echo '<td class="main" nowrap>';
                                                        echo $card_number;
                                                        $check_credit_card_array = array();
                                                        $check_credit_card_select_sql = "
	                                                                    SELECT DISTINCT o.customers_id
																		FROM " . TABLE_ORDERS . " AS o
																		INNER JOIN " . TABLE_ADYEN . " AS a
																			ON o.orders_id = a.adyen_order_id
																		WHERE a.adyen_cc_card_bin = '" . tep_db_input($cc_row['bin_number']) . "'
																			AND a.adyen_cc_card_summary = '" . tep_db_input($cc_row['card_summary']) . "'
																		UNION
																		SELECT DISTINCT o.customers_id
																		FROM " . TABLE_ORDERS . " AS o
																		INNER JOIN " . TABLE_ANALYSIS_CREDIT_CARD_INFO . " AS a
																			ON o.orders_id = a.orders_id
																		WHERE a.bin_number = '" . tep_db_input($cc_row['bin_number']) . "'
																			AND a.card_summary = '" . tep_db_input($cc_row['card_summary']) . "'
																		";
                                                        $check_credit_card_result_sql = tep_db_query($check_credit_card_select_sql);
                                                        while ($check_credit_card_row = tep_db_fetch_array($check_credit_card_result_sql)) {
                                                            $check_credit_card_array[] = $check_credit_card_row['customers_id'];
                                                        }
                                                        if (count($check_credit_card_array)>1) {
                                                            echo 	tep_draw_form('cust_lists_share_cc', FILENAME_CUSTOMERS, 'action=show_report&customer_lists_subaction=do_search', 'post', 'target="_blank"') . "\n" .
                                                                tep_draw_hidden_field('customer_share_id', tep_array_serialize($check_credit_card_array)) . "\n" .
                                                                "<BR><span class='redIndicator'>Same credit card used in <a href='javascript:document.cust_lists_share_cc.submit();'>" . count($check_credit_card_array) . "</a> profiles.</span>" .
                                                                '</form>' . "\n";
                                                        }
                                                        $cc_check_info = array(	'card_number' => $card_number,
                                                            'date_purchased' => $order->info['date_purchased'],
                                                            'orders_id' => $order->order_id,
                                                            'customers_id' => $order->customer['id']);
                                                        $cc_verified_date = tep_get_payment_info_verified_date($cc_check_info, 'credit_card');
                                                        echo "<br><span class='redIndicator'>First successful verified date by this user: ".(tep_not_null($cc_verified_date)?$cc_verified_date:"NEVER")."</span><br>(Data can only traced back to 2009-12-09)</span></td></tr>";
                                                    }
                                                    if(isset($cc_row['expiry'])) {
                                                        echo '<tr><td class="main" valign="top" nowrap><b>'.ENTRY_CC_EXPIRY_DATE.'</b>&nbsp;</td>';
                                                        echo '<td class="main" nowrap>'.$cc_row['expiry'].'</td></tr>';
                                                    }
                                                    if(isset($cc_row['issuer_country'])) {
                                                        echo '<tr><td class="main" valign="top" nowrap><b>'.ENTRY_CC_ISSUER_COUNTRY.'</b>&nbsp;</td>';
                                                        $country_info = tep_get_countries_info($cc_row['issuer_country'], 'countries_iso_code_2');
                                                        if (isset($order->customer['telephone_country']) && !empty($order->customer['telephone_country']) && $order->customer['telephone_country'] != $country_info['countries_name']) {
                                                            echo '<td class="main" nowrap>'.$cc_row['issuer_country']. ' <span class="redIndicator"> (' . sprintf(TEXT_PG_COUNTRY_NOT_MATCH_PHONE_COUNTRY, $order->customer['telephone_country']) . ')</span></td>';
                                                        } else {
                                                            echo '<td class="main" nowrap>'.$cc_row['issuer_country'].'</td>';
                                                        }
                                                        echo '</tr>';
                                                    }
                                                    if(isset($cc_row['three_d_result'])) {
                                                        echo '<tr><td class="main" valign="top" nowrap><b>'.ENTRY_CC_AUTHENTICATION_RESULT.'</b>&nbsp;</td>';
                                                        echo '<td class="main" nowrap>'. ($cc_row['three_d_result'] == 'true' ? 'Yes' : 'No').'</td></tr>';
                                                    }
                                                    if(isset($cc_row['three_d_offered'])) {
                                                        echo '<tr><td class="main" valign="top" nowrap><b>'.ENTRY_CC_AUTHENTICATION_OFFERED.'</b>&nbsp;</td>';
                                                        echo '<td class="main" nowrap>'.($cc_row['three_d_offered'] == 'true' ? 'Yes' : 'No').'</td></tr>';
                                                    }
                                                    break;
                                                case 'paypalEC':
                                                    if(isset($pgRawData['intent']) || !empty($pgRawData['intent'])){
                                                        $pgRawData = pipwave::get_paypal_pg_raw_data_capture($pgRawData);
                                                    }else if(isset($pgRawData['requery'])){
                                                        $pgRawData = pipwave::get_paypal_pg_raw_data_requery($pgRawData);
                                                    }else if(isset($pgRawData['mc_gross'])){
                                                        $pgRawData = pipwave::get_paypal_pg_raw_data_old($pgRawData);
                                                    }else{
                                                        $pgRawData = pipwave::get_paypal_pg_raw_data_legacy($pgRawData);
                                                    }


                                                    $paypalTxnId = '';
                                                    if (isset($pgRawData['txn_id'])) {
                                                        if(isset($pgRawData['parent_txn_id']) && !empty($pgRawData['parent_txn_id'])) {
                                                            $paypalTxnId = $pgRawData['parent_txn_id'];
                                                        } else {
                                                            $paypalTxnId = $pgRawData['txn_id'];
                                                        }
                                                    } else {
                                                        $paypalTxnId = $pipwave_trans_info_row['pg_txn_id'];
                                                    }
                                                    echo '<tr><td class="main" nowrap><b>'.ENTRY_PAYPAL_IPN_TXN.'</b>&nbsp;</td>';
                                                    echo '<td class="main" nowrap>'.$paypalTxnId.'</td></tr>';
                                                    if (isset($pgRawData['first_name']) && $pgRawData['first_name']) {
                                                        echo '<tr><td class="main" valign="top" nowrap><b>'.ENTRY_CUSTOMER.'</b></td>';
                                                        echo '<td class="main" nowrap>';
                                                        $paypal_payer_name = $pgRawData['first_name'] . ' ' . $pgRawData['last_name'];

                                                        $name_not_match_profile = (strtolower($paypal_payer_name) != strtolower($order->customer["name"])) ? 1 : 0;

                                                        $customer_with_this_name_array = tep_check_duplicate_name(array($pgRawData['first_name'], $pgRawData['last_name']), 'paypalEC'); // including himself
                                                        $name_used_by_other = (count($customer_with_this_name_array) > 1) ? 1 : 0;

                                                        $total_name_used_in_paypal = tep_get_distinct_name_used($cid, $paypal_payer_name, 'paypalEC');
                                                        $total_name_used = (count($total_name_used_in_paypal) > 1) ? 1 : 0;

                                                        echo '<span class="' . (($name_not_match_profile + $name_used_by_other + $total_name_used > 0) ? 'redIndicator' : 'blackIndicator') . '">' . $pgRawData['first_name'] . ' ' . $pgRawData['last_name'] . '</span>';

                                                        $name_alert_reason = '';
                                                        $name_alert_reason .= ($name_not_match_profile == 1) ? '<span class="redIndicator">Name does not match profile.</span><br>' : '';

                                                        if ($name_used_by_other == 1) {
                                                            $name_alert_reason .= tep_draw_form('cust_lists_share_name', FILENAME_CUSTOMERS, 'action=show_report&customer_lists_subaction=do_search', 'post', 'target="_blank"') . "\n" .
                                                                tep_draw_hidden_field('customer_share_id', tep_array_serialize($customer_with_this_name_array)) . "\n" .
                                                                '<span class="redIndicator">Name exists in <a href="javascript: document.cust_lists_share_name.submit();"><u>' . count($customer_with_this_name_array) . '</u></a> profiles.</span><br>' .
                                                                '</form>' . "\n";
                                                        }
                                                        $name_alert_reason .= ($total_name_used == 1) ? '<span class="redIndicator"><a href="' . tep_href_link(FILENAME_ORDERS, 'cID=' . $cid) . '" target="_blank"><u>' . count($total_name_used_in_paypal) . '</u></a> names used in PayPal orders.</span><br>' : '';

                                                        if (tep_not_null($paypal_payer_name) && tep_not_null($name_alert_reason)) {
                                                            echo '<br><span class="smallText">' . $name_alert_reason . '</span>';
                                                        }
                                                        echo '</td></tr>';
                                                    }
                                                    if (isset($pgRawData['residence_country'])) {
                                                        echo '<tr><td class="main" valign="top" nowrap><b>'.ENTRY_RESIDENCE_COUNTRY.'</b></td>';
                                                        echo '<td class="main" valign="top">&nbsp;'.$pgRawData['residence_country'];
                                                        $country_info = tep_get_countries_info($pgRawData['residence_country'], 'countries_iso_code_2');
                                                        if (isset($order->customer['telephone_country']) && !empty($order->customer['telephone_country']) && $order->customer['telephone_country'] != $country_info['countries_name']) {
                                                            echo '<br><span class="smallText redIndicator">' . sprintf(TEXT_PG_COUNTRY_NOT_MATCH_PHONE_COUNTRY, $order->customer['telephone_country']) . '</span>';
                                                        }
                                                        echo '</td></tr>';
                                                    }
                                                    $payerEmail='';
                                                    $pg_email_select_sql = "	SELECT api.info_value, o.customers_id
                                                                                FROM " . TABLE_ORDERS . " AS o
                                                                                INNER JOIN " . TABLE_ANALYSIS_PG_INFO . " AS api
                                                                                    ON (o.orders_id = api.orders_id)
                                                                                WHERE o.orders_id = '" . (int) $order->order_id . "'
                                                                                    AND api.info_key = 'payer_email'";
                                                    $pg_email_result_sql = tep_db_query($pg_email_select_sql);
                                                    if ($pg_email_row = tep_db_fetch_array($pg_email_result_sql)) {
                                                        $payerEmail = (isset($pg_email_row['info_value'])?$pg_email_row['info_value']:'');
                                                    }
                                                    if(isset($pgRawData['payer_email']) && empty($payerEmail)) {
                                                        $payerEmail = $pgRawData['payer_email'];
                                                    }
                                                    if($payerEmail) {
                                                        echo '<tr><td class="main" valign="top" nowrap><b>'.ENTRY_PAYPAL_EMAIL_ADDRESS .'</b></td>';
                                                        echo '<td class="main" nowrap>';
                                                        $paypal_verify_select_sql = "	SELECT civ.info_verified
                                                                                        FROM " . TABLE_CUSTOMERS_INFO_VERIFICATION . " AS civ
                                                                                        WHERE civ.customers_id = '" . $cid . "'
                                                                                            AND civ.customers_info_value = '" . tep_db_input($payerEmail) . "'
                                                                                            AND civ.info_verification_type = 'email'";
                                                        $paypal_verify_result_sql = tep_db_query($paypal_verify_select_sql);
                                                        $paypal_verify_row = tep_db_fetch_array($paypal_verify_result_sql);
                                                        if (strtolower($payerEmail) != strtolower($order->customer['email_address'])) {
                                                            echo '<span class="redIndicator">' . $payerEmail . '</span>&nbsp;&nbsp;';
                                                            if ($paypal_verify_row['info_verified'] == 1) {
                                                                echo tep_image(DIR_WS_ICONS . "ok.gif", "", "", "", 'align="top" onMouseover="ddrivetip(\'' . TEXT_IMAGE_MOUSEOVER_VERIFY . '\', \'\' , 180);" onMouseout="hideddrivetip();"');
                                                            } else {
                                                                $confirm_send_profile_email_verification_url = tep_href_link(FILENAME_ORDERS, 'oID=' . $oID . '&action=edit&subaction=send_paypal_verify_email&payment=paypalec') . '&cid=' . $cid;
                                                                echo tep_image(DIR_WS_ICONS . "off.gif", "", "", "", 'align="top" onMouseover="ddrivetip(\'' . TEXT_IMAGE_MOUSEOVER_NOT_VERIFY . '\', \'\' , 201);" onMouseout="hideddrivetip();"') . '&nbsp;' . '<a href="" onclick="confirm_action(\'Are you sure you want to send verification e-mail?\', \'' . $confirm_send_profile_email_verification_url . '\'); return false;">' . EMAIL_PAYPAL_VERIFY_SEND_LINK . '</a>';
                                                            }
                                                            if (tep_not_null($payerEmail))
                                                                echo '<br><span class="smallText"><span class="redIndicator">Email does not match profile.</span></span>';
                                                        } else {
                                                            echo $payerEmail . '&nbsp;&nbsp;';

                                                            if ($paypal_verify_row['info_verified'] == 1) {
                                                                echo tep_image(DIR_WS_ICONS . "ok.gif", "", "", "", 'align="top" onMouseover="ddrivetip(\'' . TEXT_IMAGE_MOUSEOVER_VERIFY . '\', \'\' , 180);" onMouseout="hideddrivetip();"');
                                                            } else {
                                                                $confirm_send_profile_email_verification_url = tep_href_link(FILENAME_ORDERS, 'oID=' . $oID . '&action=edit&subaction=send_paypal_verify_email&payment=paypalec') . '&cid=' . $cid;
                                                                echo tep_image(DIR_WS_ICONS . "off.gif", "", "", "", 'align="top" onMouseover="ddrivetip(\'' . TEXT_IMAGE_MOUSEOVER_NOT_VERIFY . '\', \'\' , 201);" onMouseout="hideddrivetip();"') . '&nbsp;' . '<a href="" onclick="confirm_action(\'Are you sure you want to send verification e-mail?\', \'' . $confirm_send_profile_email_verification_url . '\'); return false;">' . EMAIL_PAYPAL_VERIFY_SEND_LINK . '</a>';
                                                            }
                                                        }
                                                        echo '</td></tr>';
                                                    }
                                                    if(isset($pgRawData['payer_id'])) {
                                                        echo '<tr><td class="main" valign="top" nowrap><b>'.ENTRY_PAYER_ID.'</b></td>';
                                                        echo '<td class="main" nowrap>';
                                                        $customer_share_paypal_payer_id_array = array();
                                                        $payer_id_select_sql = "SELECT DISTINCT customers_id
                                                                                FROM " . TABLE_ORDERS . " AS o
                                                                                INNER JOIN " . TABLE_PAYPAL . " AS paypal
                                                                                    ON (o.orders_id = paypal.invoice)
                                                                                WHERE paypal.payer_id = '" . tep_db_input($pgRawData['payer_id']) . "'
                                                                                UNION
                                                                                SELECT DISTINCT customers_id
                                                                                FROM " . TABLE_ORDERS . " AS o
                                                                                INNER JOIN " . TABLE_PAYPALEC . " AS paypalec
                                                                                    ON (o.orders_id = paypalec.paypal_order_id)
                                                                                WHERE paypalec.payer_id = '" . tep_db_input($pgRawData['payer_id']) . "'
                                                                                UNION
                                                                                SELECT DISTINCT customers_id
                                                                                FROM " . TABLE_ORDERS . " AS o
                                                                                INNER JOIN " . TABLE_ANALYSIS_PG_INFO . " AS pginfo
                                                                                    ON (o.orders_id = pginfo.orders_id AND pginfo.info_key = 'payer_id')
                                                                                WHERE pginfo.info_value = '" . tep_db_input($pgRawData['payer_id']) . "'
                                                                                ";
                                                        $payer_id_result = tep_db_query($payer_id_select_sql);
                                                        while ($payer_id_row = tep_db_fetch_array($payer_id_result)) {
                                                            $customer_share_paypal_payer_id_array[] = $payer_id_row["customers_id"];
                                                        }
                                                        if (count($customer_share_paypal_payer_id_array) > 1) {
                                                            echo tep_draw_form('cust_lists_share_payer_id', FILENAME_CUSTOMERS, tep_get_all_get_params(array('action')) . 'action=show_report&customer_lists_subaction=do_search', 'post', 'target="customerSharePayerIDWin"') . "\n" .
                                                                tep_draw_hidden_field('customer_share_id', tep_array_serialize($customer_share_paypal_payer_id_array)) . "\n" .
                                                                '<span class="smallText"><span class="redIndicator">' . $pgRawData['payer_id'] . '<br><a href="javascript: document.cust_lists_share_payer_id.submit();"><u>' . count($customer_share_paypal_payer_id_array) . '</u></a> customers sharing the same Payer ID.</span></span>' .
                                                                '</form>' . "\n";
                                                        } else {
                                                            echo $pgRawData['payer_id'];
                                                        }
                                                        if (tep_not_null($pgRawData['payer_id'])) {
                                                            $payment_check_info = array(
                                                                'payer_id' => $pgRawData['payer_id'],
                                                                'orders_id' => $order->order_id,
                                                                'date_purchased' => $order->info['date_purchased'],
                                                                'customers_id' => $order->customer['id']
                                                            );
                                                            $payment_verified_date = tep_get_payment_info_verified_date($payment_check_info, 'paypal_payer_id');
                                                            echo "<br><span class='redIndicator'>First successful verified date by this user: " . (tep_not_null($payment_verified_date) ? $payment_verified_date : "NEVER") . "</span>";
                                                        }
                                                    }
                                                    if(isset($pgRawData['address_name'])) {
                                                        echo '<tr valign="top">';
                                                        echo '<td class="main" style="padding-right: 10px;" nowrap>';
                                                        echo '<b>' . ENTRY_PAYPAL_ADDRESS . '</b><br>' . ($pgRawData['address_status'] == 'unconfirmed' ? '<span class="redIndicator">' . $pgRawData['address_status'] . '</span>' : $pgRawData['address_status']); '.</td>.';
                                                        echo '<td class="main" nowrap>'.$pgRawData['address_name'] . '<br>' . $pgRawData['address_street'] . '<br>' . $pgRawData['address_city'] . '<br>' . $pgRawData['address_state'] . '<br>' . $pgRawData['address_zip'] . '<br>' . $pgRawData['address_country'].'</td></tr>';
                                                    }
                                                    if (isset($pgRawData['payer_status'])) {
                                                        echo '<tr><td class="main" nowrap><b>'.ENTRY_PAYER_STATUS.'</b></td>';
                                                        echo '<td class="main" nowrap>'.$pgRawData['payer_status'].'</td></tr>';
                                                    }
                                                    if (isset($pgRawData['pending_reason'])) {
                                                        echo '<tr><td class="main" nowrap><b>'.ENTRY_PAYPAL_PENDING_REASON.'</b></td>';
                                                        echo '<td class="main" nowrap>'.$pgRawData['pending_reason'].'</td></tr>';
                                                    }
                                                    if (isset($pgRawData['protection_eligibility'])) {
                                                        echo '<tr><td class="main" nowrap><b>'.ENTRY_PAYPAL_PROTECTION_ELIGIBLE.'</b></td>';
                                                        echo '<td class="main" nowrap>'.$pgRawData['protection_eligibility'].'</td></tr>';
                                                    }
                                                    break;
                                                case 'moneybookers':
                                                    if (isset($pgRawData['mb_transaction_id'])) {
                                                        echo '<tr><td class="main" nowrap><b>'.ENTRY_MB_TRANSACTION_ID.'</b></td>';
                                                        echo '<td class="main" nowrap>'.($pgRawData["mb_transaction_id"] != 0 ? $pgRawData["mb_transaction_id"] : '&nbsp;').'</td></tr>';
                                                    }
                                                    $moneybookerEmail = '';
                                                    if (isset($pgRawData['pay_from_email'])) {
                                                        $moneybookerEmail = $pgRawData['pay_from_email'];
                                                    } else {
                                                        $pg_email_select_sql = "	SELECT info_value
                                                                                    FROM " . TABLE_ANALYSIS_PG_INFO . "
                                                                                    WHERE orders_id = '" . (int) $order->order_id . "'
                                                                                        AND info_key = 'moneybooker_email'";
                                                        $pg_email_result_sql = tep_db_query($pg_email_select_sql);
                                                        if ($pg_email_row = tep_db_fetch_array($pg_email_result_sql)) {
                                                            $moneybookerEmail = (isset($pg_email_row['info_value'])?$pg_email_row['info_value']:'');
                                                        }
                                                    }
                                                    if(!empty($moneybookerEmail)) {
                                                        $payer_email_select_sql = " SELECT DISTINCT customers_id
                                                                                    FROM " . TABLE_ORDERS . " AS o
                                                                                    INNER JOIN " . TABLE_PAYMENT_MONEYBOOKERS . " AS mb
                                                                                        ON (o.orders_id = mb.mb_order_id)
                                                                                    WHERE mb.mb_payer_email = '" . $moneybookerEmail . "'
                                                                                    UNION
                                                                                    SELECT DISTINCT customers_id
                                                                                    FROM " . TABLE_ORDERS . " AS o
                                                                                    INNER JOIN " . TABLE_ANALYSIS_PG_INFO . " AS pginfo
                                                                                        ON (o.orders_id = pginfo.orders_id AND pginfo.info_key = 'moneybooker_email')
                                                                                    WHERE pginfo.info_value = '" . $moneybookerEmail . "'
                                                                                    ";
                                                        $payer_email_result = tep_db_query($payer_email_select_sql);
                                                        while ($payer_email_row = tep_db_fetch_array($payer_email_result)) {
                                                            $customer_share_mb_payer_email_array[] = $payer_email_row["customers_id"];
                                                        }

                                                        echo '<td class="main" valign="top" nowrap><b>'.ENTRY_MB_PAYER_EMAIL.'</b>&nbsp;</td>';
                                                        echo '<td class="main" nowrap>';
                                                        if ($email_used_by_other == 1) {
                                                            $email_alert_reason .= tep_draw_form('cust_lists_share_email', FILENAME_CUSTOMERS, 'action=show_report&customer_lists_subaction=do_search', 'post', 'target="_blank"') . "\n" .
                                                                tep_draw_hidden_field('customer_share_id', tep_array_serialize($customer_with_this_email_array)) . "\n" .
                                                                '<span class="redIndicator">Email exists in <a href="javascript: document.cust_lists_share_email.submit();"><u>' . count($customer_with_this_email_array) . '</u></a> profiles.</span><br>' .
                                                                '</form>' . "\n";
                                                        }

                                                        $email_alert_reason .= ($total_email_used == 1) ? '<span class="redIndicator"><a href="' . tep_href_link(FILENAME_ORDERS, 'cID=' . $cid) . '" target="_blank"><u>' . count($total_name_used_in_mb) . '</u></a> email used in Moneybookers orders.</span><br>' : '';

                                                        if (tep_not_null($email_alert_reason)) {
                                                            echo '<br><span class="smallText">' . $email_alert_reason . '</span>';
                                                        }
                                                        if (count($customer_share_mb_payer_email_array) > 1) {
                                                            echo tep_draw_form('cust_lists_share_payer_id', FILENAME_CUSTOMERS, tep_get_all_get_params(array('action')) . 'action=show_report&customer_lists_subaction=do_search', 'post', 'target="customerSharePayerIDWin"') . "\n" .
                                                                tep_draw_hidden_field('customer_share_id', tep_array_serialize($customer_share_mb_payer_email_array)) . "\n" .
                                                                '<span class="smallText"><span class="redIndicator">' . $pgRawData["pay_from_email"] . '<br><a href="javascript: document.cust_lists_share_payer_id.submit();"><u>' . count($customer_share_mb_payer_email_array) . '</u></a> customers sharing this moneybookers account.</span></span>' .
                                                                '</form>' . "\n";
                                                        } else {
                                                            echo $pgRawData["pay_from_email"];
                                                        }

                                                        $payer_email_verified = tep_info_verified_check($cid, $moneybookerEmail, 'email');
                                                        if ($payer_email_verified == 'A' || $payer_email_verified == 'M') {
                                                            echo tep_image(DIR_WS_ICONS . "ok.gif", "", "", "", 'align="top" onMouseover="ddrivetip(\'' . TEXT_IMAGE_MOUSEOVER_VERIFY . '\', \'\' , 180);" onMouseout="hideddrivetip();"');
                                                        } else {
                                                            $confirm_send_profile_email_verification_url = tep_href_link(FILENAME_ORDERS, 'oID=' . $oID . '&action=edit&subaction=send_moneybooker_verify_email') . '&cid=' . $cid;
                                                            echo tep_image(DIR_WS_ICONS . "off.gif", "", "", "", 'align="top" onMouseover="ddrivetip(\'' . TEXT_IMAGE_MOUSEOVER_NOT_VERIFY . '\', \'\' , 201);" onMouseout="hideddrivetip();"') . '&nbsp;' . '<a href="" onclick="confirm_action(\'Are you sure you want to send verification e-mail?\', \'' . $confirm_send_profile_email_verification_url . '\'); return false;">' . EMAIL_MB_VERIFY_SEND_LINK . '</a>';
                                                        }

                                                        if (tep_not_null($pgRawData["pay_from_email"])) {
                                                            $payment_check_info = array('email' => $pgRawData["pay_from_email"],
                                                                'orders_id' => $order->order_id,
                                                                'date_purchased' => $order->info['date_purchased'],
                                                                'customers_id' => $order->customer['id']);
                                                            $payment_verified_date = tep_get_payment_info_verified_date($payment_check_info, 'moneybookers');
                                                            echo "<br><span class='redIndicator'>First successful verified date by this user: " . (tep_not_null($payment_verified_date) ? $payment_verified_date : "NEVER") . "</span>";
                                                        }
                                                        echo '</td></tr>';
                                                    }
                                                    break;
                                                case 'webmoney':
                                                    if (isset($pgRawData['LMI_SYS_TRANS_NO']) || isset($pgRawData['PW_TXN_ID'])) {
                                                        echo '<tr><td class="main" nowrap><b>'.ENTRY_WEBMONEY_TXN_ID.'</b></td>';
                                                        echo '<td class="main" nowrap>'.(isset($pgRawData['LMI_SYS_TRANS_NO']) ? $pgRawData['LMI_SYS_TRANS_NO'] : (isset($pgRawData['PW_TXN_ID']) ? $pgRawData['PW_TXN_ID'] : $pipwave_trans_info_row['pg_txn_id'])).'</td></tr>';
                                                    }
                                                    break;
                                                case 'paymaster24':
                                                    if (isset($pgRawData['LMI_SYS_PAYMENT_ID']) || isset($pgRawData['PW_TXN_ID'])) {
                                                        echo '<tr><td class="main" nowrap><b>'.ENTRY_WEBMONEY_TXN_ID.'</b></td>';
                                                        echo '<td class="main" nowrap>'.(isset($pgRawData['LMI_SYS_PAYMENT_ID']) ? $pgRawData['LMI_SYS_PAYMENT_ID'] : (isset($pgRawData['PW_TXN_ID']) ? $pgRawData['PW_TXN_ID'] : $pipwave_trans_info_row['pg_txn_id'])).'</td></tr>';
                                                    }
                                                    break;
                                                case 'offline':
                                                    $trans_info_select_sql = "	SELECT authorisation_result
                                                                                FROM " . TABLE_PAYMENT_EXTRA_INFO . "
                                                                                WHERE orders_id='" . tep_db_input($oID) . "'";
                                                    $trans_info_result_sql= tep_db_query($trans_info_select_sql);
                                                    $trans_info_row = tep_db_fetch_array($trans_info_result_sql);

                                                    if(isset($trans_info_row['authorisation_result'])) {
                                                        echo '	<tr>';
                                                        echo tep_draw_form('payment_info_form', FILENAME_ORDERS, tep_get_all_get_params(), 'post');
                                                        echo tep_draw_hidden_field('subaction', 'update_payment_info');
                                                        ?>
                                                        <td class="main" valign="top" nowrap><b>Payment Info:</b>&nbsp;</td>
                                                        <td class="main" valign="top">
                                                        <?php
                                                        if ($order->info['orders_status'] == '1' || $order->info['orders_status'] == '7') { // Only editable when it is Pending or Verifying ?>
                                                            <table border="0" cellspacing="0" cellpadding="2">
                                                                <tr>
                                                                    <td><?=tep_draw_textarea_field('payment_info', 'soft', '60', '5', $trans_info_row["authorisation_result"])?></td>
                                                                    <td class="main" valign="bottom">
                                                                        <?=tep_submit_button(IMAGE_UPDATE, IMAGE_UPDATE, 'name="updateBtn"', 'inputButton')?>
                                                                    </td>
                                                                </tr>
                                                            </table>
                                                        <?php
                                                        } else {
                                                            echo nl2br($trans_info_row["authorisation_result"]);
                                                        }
                                                        echo '</form>';
                                                    }
                                                    break;
                                            }

                                            // Start Image Upload checking
                                            $photo_array = array();
                                            $photo_img_array = array();
                                            $photo_pdf_array = array();

                                            $trans_info_select_sql = "  SELECT orders_extra_info_key, orders_extra_info_value
                                                                        FROM " . TABLE_ORDERS_EXTRA_INFO . "
                                                                        WHERE orders_id = '" . tep_db_input($oID) . "'
                                                                            AND orders_extra_info_key IN ('payment_images_1', 'payment_images_2', 'payment_images_3')";
                                            $trans_info_result_sql= tep_db_query($trans_info_select_sql);
                                            while ($trans_info_row2 = tep_db_fetch_array($trans_info_result_sql)) {
                                                $fpath = urlencode(str_replace('{order_id}', $oID, $trans_info_row2['orders_extra_info_value']));
                                                // set file_ext in array
                                                $filename = urldecode($fpath);
                                                $path_parts = pathinfo($filename);
                                                $file_ext = $path_parts['extension'] === 'jpg' ? 'jpeg' : $path_parts['extension'];

                                                // img/pdf url
                                                $photo_array['#'.$trans_info_row2['orders_extra_info_key']] = '<a id="' . $trans_info_row2['orders_extra_info_key'] . '" href="' . tep_href_link(FILENAME_ORDERS, 'subaction=preview_image&fpath=' . $fpath . '&site_id=' . $order_site_id) . '" target="_blank" title="' . $trans_info_row2['orders_extra_info_key'] . '">' . str_replace('_', ' ', $trans_info_row2['orders_extra_info_key']) . '</a>';

                                                if ($file_ext === 'pdf') {
                                                    $photo_pdf_array['#'.$trans_info_row2['orders_extra_info_key']] = '<a id="' . $trans_info_row2['orders_extra_info_key'] . '" href="' . tep_href_link(FILENAME_ORDERS, 'subaction=preview_image&fpath=' . $fpath . '&site_id=' . $order_site_id) . '" target="_blank" title="' . $trans_info_row2['orders_extra_info_key'] . '">' . str_replace('_', ' ', $trans_info_row2['orders_extra_info_key']) . '</a>';
                                                } else {
                                                    $photo_img_array['#'.$trans_info_row2['orders_extra_info_key']] = '<a id="' . $trans_info_row2['orders_extra_info_key'] . '" href="' . tep_href_link(FILENAME_ORDERS, 'subaction=preview_image&fpath=' . $fpath . '&site_id=' . $order_site_id) . '" target="_blank" title="' . $trans_info_row2['orders_extra_info_key'] . '">' . str_replace('_', ' ', $trans_info_row2['orders_extra_info_key']) . '</a>';
                                                }
                                            }

                                            if (tep_not_null($photo_array)) {
                                                ?>
                                                <tr>
                                                    <td class="main" valign="top" nowrap><b><?='Payment Made Images:'?></b>&nbsp;</td>
                                                    <td class="main" valign="top">
                                                        <?php
                                                        echo implode('<br>', $photo_array);
                                                        ?>
                                                        <script>
                                                            <?php if ($photo_img_array) { ?>
                                                                jQuery("<?= implode(',', array_keys($photo_img_array)) ?>").fancybox({
                                                                    'transitionIn': 'none',
                                                                    'transitionOut': 'none',
                                                                    'type': 'image',
                                                                    'centerOnScroll': true
                                                                });
                                                            <?php } ?>

                                                            <?php if ($photo_pdf_array) { ?>
                                                                jQuery("<?= implode(',', array_keys($photo_pdf_array)) ?>").fancybox({
                                                                    width  : 800,
                                                                    height : 600,
                                                                    type   :'iframe'
                                                                });
                                                            <?php } ?>
                                                        </script>
                                                    </td>
                                                </tr>
                                                <?php
                                            }
                                            // End Image Upload checking
                                        }
                                        ?>
                                    </tr>
                                </table>
                            </td>
                            <td>
                                <table border="0" cellspacing="0" cellpadding="2">
                                    <tr>
                                        <td class="main" valign="top" nowrap><b><?php echo ENTRY_PIPWAVE_CURRENCY?></b>&nbsp;</td>
                                        <td class="main" nowrap>
                                            <?php
                                            if (strtolower($pipwave_trans_info_row['currency_code']) == strtolower($order->info['currency'])) {
                                                echo $pipwave_trans_info_row['currency_code'];
                                            } else {
                                                echo '<span class="redIndicator">'.$pipwave_trans_info_row['currency_code'].'<br><span class="smallText">Does not match purchase currency.</span></span>';
                                            }
                                            ?>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="main" valign="top" nowrap><b><?php echo ENTRY_PIPWAVE_AMOUNT?></b>&nbsp;</td>
                                        <td class="main">
                                            <?php
                                            $pipwaveGrossAmt = number_format($pipwave_trans_info_row['total_amount'], $currencies->currencies[$pipwave_trans_info_row['currency_code']]['decimal_places'], $currencies->currencies[$pipwave_trans_info_row['currency_code']]['decimal_point'], $currencies->currencies[$pipwave_trans_info_row['currency_code']]['thousands_point']);
                                            $pipwaveAmountFormatted = $currencies->currencies[$pipwave_trans_info_row['currency_code']]['symbol_left'] . $pipwaveGrossAmt . $currencies->currencies[$pipwave_trans_info_row['currency_code']]['symbol_right'];
                                            if ($pipwaveAmountFormatted != strip_tags($order->order_totals['ot_total']['text']) ) {
                                                $mc_gross_display_text = '<span class="redIndicator">'.$pipwaveAmountFormatted.'<br><span class="smallText">Does not match purchase amount.</span></span>';
                                            } else {
                                                $mc_gross_display_text = $pipwaveAmountFormatted;
                                            }

                                            echo $mc_gross_display_text;
                                            ?>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
                                    </tr>
                                </table>
                                <?php
                                $riskData = array();
                                if(isset($pipwave_trans_info_row['risk_management_data']) && !empty($pipwave_trans_info_row['risk_management_data'])) {
                                    $riskData = json_decode($pipwave_trans_info_row['risk_management_data'],1);
                                    if (!empty($riskData)) {
                                ?>
                                    <table border="1" cellspacing="0" cellpadding="2">
                                        <tr>
                                            <td class="smallText" nowrap><b><?php echo TABLE_HEADING_RISK_TYPE?></b></td>
                                            <td class="smallText" nowrap><b><?php echo TABLE_HEADING_RISK_VALUE?></b></td>
                                        </tr>
                                        <?php
                                        foreach ($riskData as $riskTitle => $riskValue) {
                                            if($riskTitle == 'proxy_ip_country' || $riskTitle == 'device_setting_country') {
                                                $countries_code_select_sql = "SELECT countries_name FROM " . TABLE_COUNTRIES . " WHERE countries_iso_code_2 = '" . tep_db_prepare_input($riskValue) . "'";
                                                $countries_code_result_sql = tep_db_query($countries_code_select_sql);
                                                $countries_code_row = tep_db_fetch_array($countries_code_result_sql);
                                                if(isset($countries_code_row['countries_name']) && !empty($countries_code_row['countries_name'])) {
                                                    $riskValue = $riskValue . ' - ' . $countries_code_row['countries_name'];
                                                }
                                            }
                                            echo '<tr>
                                                    <td class="smallText" nowrap>'.$riskTitle.'</td>
                                                    <td class="smallText" nowrap>'.$riskValue.'</td>
                                                  </tr>';
                                        }
                                        ?>
                                    </table><br />
                                <?php
                                    }
                                }
                                $rulesData = array();
                                if(isset($pipwave_trans_info_row['matched_rules']) && !empty($pipwave_trans_info_row['matched_rules'])) {
                                    $rulesData = json_decode($pipwave_trans_info_row['matched_rules'], 1);
                                    if (!empty($rulesData)) {
                                        ?>
                                        <table border="1" cellspacing="0" cellpadding="2">
                                            <tr>
                                                <td class="smallText" nowrap><b><?php echo TABLE_HEADING_RULES_TITLE ?></b></td>
                                                <td class="smallText" nowrap><b><?php echo TABLE_HEADING_RULES_RESULT ?></b></td>
                                            </tr>
                                            <?php
                                            foreach ($rulesData as $rules) {
                                                $color = '';
                                                if(isset($rules['result'])) {
                                                    if($rules['result'] == 'review') {
                                                        $color = 'color:orange';
                                                    } else if ($rules['result'] == 'decline') {
                                                        $color = 'color:red';
                                                    }
                                                }
                                                $ruleUrl = $pipwave->generateRuleResultUrl($rules['id']);
                                                echo '<tr>
                                                        <td class="smallText" nowrap><a href="'.$ruleUrl.'" target="_blank">' . htmlentities($rules['title']) . '</a></td>
                                                        <td class="smallText" nowrap><span style="'.$color.'">' . $rules['result'] . '</span>' . (!isset($rules['what_to_do']) || empty($rules['what_to_do']) ? '<br />' : '<br />'.htmlentities($rules['what_to_do'])) . '</td>
                                                      </tr>';
                                            }
                                            ?>
                                        </table>
                                        <?php
                                    }
                                }
                                ?>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <?php
        }
    } else {
        echo $payment_method_title_info;
        ?>
        <tr>
            <td class="main" nowrap>
                <?
                if ($order->info['orders_status'] == '1' || $order->info['orders_status'] == '7' || $order->info['orders_status'] == '2') {
                    echo "&nbsp;&nbsp;";
                    echo tep_draw_form('pipwave_status_check_form', FILENAME_ORDERS, tep_get_all_get_params(), 'post');
                    echo tep_draw_hidden_field('subaction', 'payment_action');
                    echo tep_draw_hidden_field('payment_action', 'check_trans_status');
                    echo tep_submit_button('Check Payment Status', 'Check Payment Status', 'name="pipwaveCheckTransStatusBtn"', 'inputButton');
                    echo "</form>";
                }
                if ($order->info['site_id'] == 0 && $order->info['orders_status'] == '1' && empty($order->info['payment_methods_id'])) {
                    echo "&nbsp;&nbsp;";
                    echo tep_draw_form('pipwave_status_check_form', FILENAME_ORDERS, tep_get_all_get_params(), 'post');
                    echo tep_draw_hidden_field('subaction', 'check_payment_method');
                    echo tep_submit_button('Check Offline Payment', 'Offline Payment', 'name="checkoutCheckPaymentBtn"', 'inputButton');
                    echo "</form>";
                }
                ?>
            </td>
        </tr>
        <?php
    }
    ?>

</table><?php
    function get_paypal_pg_raw_data_capture($pgRawData){

        if(isset($pgRawData['id'])) {
            $pgRawData['txn_id'] = $pgRawData['id'];
        }
        if(isset($pgRawData['PaymentInfo']['ParentTransactionID'])) { // no data yet
            $pgRawData['parent_txn_id'] = $pgRawData['PaymentInfo']['ParentTransactionID'];
        }
        if(isset($pgRawData['payer']['name']['given_name'])) {
            $pgRawData['first_name'] = $pgRawData['payer']['name']['given_name'];
        }
        if(isset($pgRawData['payer']['name']['surname'])) {
            $pgRawData['last_name'] = $pgRawData['payer']['name']['surname'];
        }
        if(isset($pgRawData['payer']['address']['country_code'])) {
            $pgRawData['residence_country'] = $pgRawData['payer']['address']['country_code'];
        }
        if(isset($pgRawData['payer']['email_address'])) {
            $pgRawData['payer_email'] = $pgRawData['payer']['email_address'];
        }
        if(isset($pgRawData['payer']['payer_id'])) {
            $pgRawData['payer_id'] = $pgRawData['payer']['payer_id'];
        }
        if(isset($pgRawData['PayerInfo']['Address']['Name'])) { // no data yet
            $pgRawData['address_name'] = $pgRawData['PayerInfo']['Address']['Name'];
        }else{
            if(isset($pgRawData['payer']['name']['given_name']) && isset($pgRawData['payer']['name']['surname']) ){
                $pgRawData['address_name'] = $pgRawData['payer']['name']['given_name']." ".$pgRawData['payer']['name']['surname'];
            }
        }

        if(isset($pgRawData['PayerInfo']['Address']['AddressStatus'])) { // no data yet
            $pgRawData['address_status'] = $pgRawData['PayerInfo']['Address']['AddressStatus'];
        }
        if(isset($pgRawData['payer']['address']['address_line_1']) || isset($pgRawData['purchase_units'][0]['shipping']['address']['address_line_1'])) {
            $pgRawData['address_street'] = (isset($pgRawData['payer']['address']['address_line_1'])) ? $pgRawData['payer']['address']['address_line_1'] : $pgRawData['purchase_units'][0]['shipping']['address']['address_line_1'];
        }
        if(isset($pgRawData['payer']['address']['admin_area_1']) || isset($pgRawData['purchase_units'][0]['shipping']['address']['admin_area_1'])) {
            $pgRawData['address_city'] = (isset($pgRawData['payer']['address']['admin_area_1'])) ? $pgRawData['payer']['address']['admin_area_1'] : $pgRawData['purchase_units'][0]['shipping']['address']['admin_area_1'];
        }
        if(isset($pgRawData['payer']['address']['admin_area_2']) || isset($pgRawData['purchase_units'][0]['shipping']['address']['admin_area_2']) ) {
            $pgRawData['address_state'] = (isset($pgRawData['payer']['address']['admin_area_2'])) ? $pgRawData['payer']['address']['admin_area_2'] : $pgRawData['purchase_units'][0]['shipping']['address']['admin_area_2'];
        }
        if(isset($pgRawData['payer']['address']['postal_code']) || isset($pgRawData['purchase_units'][0]['shipping']['address']['postal_code']) ) {
            $pgRawData['address_zip'] = (isset($pgRawData['payer']['address']['postal_code']) ? $pgRawData['payer']['address']['postal_code'] : $pgRawData['purchase_units'][0]['shipping']['address']['postal_code'] );
        }
        if(isset($pgRawData['payer']['address']['country_code']) || isset($pgRawData['purchase_units'][0]['shipping']['address']['country_code']) ) {
            $pgRawData['address_country'] = (isset($pgRawData['payer']['address']['country_code'])) ? $pgRawData['payer']['address']['country_code'] : $pgRawData['purchase_units'][0]['shipping']['address']['country_code'] ;
        }
        if(isset($pgRawData['PayerInfo']['PayerStatus'])) { // no data
            $pgRawData['payer_status'] = $pgRawData['PayerInfo']['PayerStatus'];
        }
        if(isset($pgRawData['purchase_units'][0]['payments']['captures'][0]['status_details']['reason'])) {
            $pgRawData['pending_reason'] = $pgRawData['purchase_units'][0]['payments']['captures'][0]['status_details']['reason'];
        }
        if(isset($pgRawData['purchase_units'][0]['payments']['captures'][0]['seller_protection']['status'])) {
            $pgRawData['protection_eligibility'] = $pgRawData['purchase_units'][0]['payments']['captures'][0]['seller_protection']['status'];
        }

        return $pgRawData;

    }

    function get_paypal_pg_raw_data_requery($pgRawData){

        $requery = array();

        if(isset($pgRawData['requery'])){
            $requery = $pgRawData['requery'];
        }

        if(isset($requery['id'])) {
            $pgRawData['txn_id'] = $requery['id'];
        }
        if(isset($requery['PaymentInfo']['ParentTransactionID'])) { // no data yet
            $pgRawData['parent_txn_id'] = $requery['PaymentInfo']['ParentTransactionID'];
        }
        if(isset($requery['payer']['name']['given_name'])) {
            $pgRawData['first_name'] = $requery['payer']['name']['given_name'];
        }
        if(isset($requery['payer']['name']['surname'])) {
            $pgRawData['last_name'] = $requery['payer']['name']['surname'];
        }
        if(isset($requery['payer']['address']['country_code'])) {
            $pgRawData['residence_country'] = $requery['payer']['address']['country_code'];
        }
        if(isset($requery['payer']['email_address'])) {
            $pgRawData['payer_email'] = $requery['payer']['email_address'];
        }
        if(isset($requery['payer']['payer_id'])) {
            $pgRawData['payer_id'] = $requery['payer']['payer_id'];
        }
        if(isset($requery['PayerInfo']['Address']['Name'])) { // no data yet
            $pgRawData['address_name'] = $requery['PayerInfo']['Address']['Name'];
        }else{
            if(isset($requery['payer']['name']['given_name']) && isset($requery['payer']['name']['surname']) ){
                $pgRawData['address_name'] = $requery['payer']['name']['given_name']." ".$requery['payer']['name']['surname'];
            }
        }

        if(isset($requery['PayerInfo']['Address']['AddressStatus'])) { // no data yet
            $pgRawData['address_status'] = $requery['PayerInfo']['Address']['AddressStatus'];
        }
        if(isset($requery['payer']['address']['address_line_1']) || isset($requery['purchase_units'][0]['shipping']['address']['address_line_1'])) {
            $pgRawData['address_street'] = (isset($requery['payer']['address']['address_line_1'])) ? $requery['payer']['address']['address_line_1'] : $requery['purchase_units'][0]['shipping']['address']['address_line_1'];
        }
        if(isset($requery['payer']['address']['admin_area_1']) || isset($requery['purchase_units'][0]['shipping']['address']['admin_area_1'])) {
            $pgRawData['address_city'] = (isset($requery['payer']['address']['admin_area_1'])) ? $requery['payer']['address']['admin_area_1'] : $requery['purchase_units'][0]['shipping']['address']['admin_area_1'];
        }
        if(isset($requery['payer']['address']['admin_area_2']) || isset($requery['purchase_units'][0]['shipping']['address']['admin_area_2']) ) {
            $pgRawData['address_state'] = (isset($requery['payer']['address']['admin_area_2'])) ? $requery['payer']['address']['admin_area_2'] : $requery['purchase_units'][0]['shipping']['address']['admin_area_2'];
        }
        if(isset($requery['payer']['address']['postal_code']) || isset($requery['purchase_units'][0]['shipping']['address']['postal_code']) ) {
            $pgRawData['address_zip'] = (isset($requery['payer']['address']['postal_code']) ? $requery['payer']['address']['postal_code'] : $requery['purchase_units'][0]['shipping']['address']['postal_code'] );
        }
        if(isset($requery['payer']['address']['country_code']) || isset($requery['purchase_units'][0]['shipping']['address']['country_code']) ) {
            $pgRawData['address_country'] = (isset($requery['payer']['address']['country_code'])) ? $requery['payer']['address']['country_code'] : $requery['purchase_units'][0]['shipping']['address']['country_code'] ;
        }
        if(isset($requery['PayerInfo']['PayerStatus'])) { // no data
            $pgRawData['payer_status'] = $requery['PayerInfo']['PayerStatus'];
        }
        if(isset($requery['purchase_units'][0]['payments']['captures'][0]['status_details']['reason'])) {
            $pgRawData['pending_reason'] = $requery['purchase_units'][0]['payments']['captures'][0]['status_details']['reason'];
        }
        if(isset($requery['purchase_units'][0]['payments']['captures'][0]['seller_protection']['status'])) {
            $pgRawData['protection_eligibility'] = $requery['purchase_units'][0]['payments']['captures'][0]['seller_protection']['status'];
        }

        return $pgRawData;

    }

    function get_paypal_pg_raw_data_old($pgRawData){
        return $pgRawData;
    }

    function get_paypal_pg_raw_data_legacy($pgRawData){

        if(isset($pgRawData['PaymentInfo']['TransactionID'])) {
            $pgRawData['txn_id'] = $pgRawData['PaymentInfo']['TransactionID'];
        }
        if(isset($pgRawData['PaymentInfo']['ParentTransactionID'])) {
            $pgRawData['parent_txn_id'] = $pgRawData['PaymentInfo']['ParentTransactionID'];
        }
        if(isset($pgRawData['PayerInfo']['PayerName']['FirstName'])) {
            $pgRawData['first_name'] = $pgRawData['PayerInfo']['PayerName']['FirstName'];
        }
        if(isset($pgRawData['PayerInfo']['PayerName']['LastName'])) {
            $pgRawData['last_name'] = $pgRawData['PayerInfo']['PayerName']['LastName'];
        }
        if(isset($pgRawData['PayerInfo']['PayerCountry'])) {
            $pgRawData['residence_country'] = $pgRawData['PayerInfo']['PayerCountry'];
        }
        if(isset($pgRawData['PayerInfo']['Payer'])) {
            $pgRawData['payer_email'] = $pgRawData['PayerInfo']['Payer'];
        }
        if(isset($pgRawData['PayerInfo']['PayerID'])) {
            $pgRawData['payer_id'] = $pgRawData['PayerInfo']['PayerID'];
        }
        if(isset($pgRawData['PayerInfo']['Address']['Name'])) {
            $pgRawData['address_name'] = $pgRawData['PayerInfo']['Address']['Name'];
        }
        if(isset($pgRawData['PayerInfo']['Address']['AddressStatus'])) {
            $pgRawData['address_status'] = $pgRawData['PayerInfo']['Address']['AddressStatus'];
        }
        if(isset($pgRawData['PayerInfo']['Address']['Street1'])) {
            $pgRawData['address_street'] = $pgRawData['PayerInfo']['Address']['Street1'];
        }
        if(isset($pgRawData['PayerInfo']['Address']['CityName'])) {
            $pgRawData['address_city'] = $pgRawData['PayerInfo']['Address']['CityName'];
        }
        if(isset($pgRawData['PayerInfo']['Address']['StateOrProvince'])) {
            $pgRawData['address_state'] = $pgRawData['PayerInfo']['Address']['StateOrProvince'];
        }
        if(isset($pgRawData['PayerInfo']['Address']['PostalCode'])) {
            $pgRawData['address_zip'] = $pgRawData['PayerInfo']['Address']['PostalCode'];
        }
        if(isset($pgRawData['PayerInfo']['Address']['CountryName'])) {
            $pgRawData['address_country'] = $pgRawData['PayerInfo']['Address']['CountryName'];
        }
        if(isset($pgRawData['PayerInfo']['PayerStatus'])) {
            $pgRawData['payer_status'] = $pgRawData['PayerInfo']['PayerStatus'];
        }
        if(isset($pgRawData['PaymentInfo']['PendingReason'])) {
            $pgRawData['pending_reason'] = $pgRawData['PaymentInfo']['PendingReason'];
        }
        if(isset($pgRawData['PaymentInfo']['ProtectionEligibility'])) {
            $pgRawData['protection_eligibility'] = $pgRawData['PaymentInfo']['ProtectionEligibility'];
        }

        return $pgRawData;
    }
?>
