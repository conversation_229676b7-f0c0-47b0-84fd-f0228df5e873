<?php
require_once('payment_gateway.php');
include_once(DIR_WS_CLASSES . 'pipwave.php');

class pipwavePG extends payment_gateway
{
    var $code, $pipwaveCurrencies;
    var $outgoing_api_merchant, $outgoing_api_username, $outgoing_api_password, $outgoing_api_extra, $outgoing_merchant_code = '';
    function pipwavePG($pm_id = '')
    {
        global $languages_id, $default_languages_id;
        $this->code = 'pipwavePG';
        $this->title = $this->code;
        $this->preferred = true;
        $this->filename = 'pipwavePG.php';
        $this->auto_cancel_period = 30; // In minutes
        $this->connect_via_proxy = false; // localhost test - set to true, server test - set to false //need proxy to connect outside
        $this->force_to_checkoutprocess = true;
        $this->has_ipn = true;
        $this->payment_methods_id = 0;
        $this->payment_methods_parent_id = 0;
        $this->check_trans_status_flag = false;
        $payment_methods_select_sql = "	SELECT  payment_methods_parent_id, payment_methods_code, 
												payment_methods_types_id, 
												payment_methods_receive_status_mode, payment_methods_id, 
												payment_methods_title, payment_methods_sort_order, 
												payment_methods_receive_status, payment_methods_legend_color, 
												payment_methods_logo 
										FROM " . TABLE_PAYMENT_METHODS . "
										WHERE 1 ";
        if ((int) $pm_id > 0) {
            $payment_methods_select_sql .= "	AND payment_methods_id = '" . (int) $pm_id . "'";
        } else {
            $payment_methods_select_sql .= "	AND payment_methods_filename = '" . tep_db_input($this->filename) . "'";
        }
        $payment_methods_result_sql = tep_db_query($payment_methods_select_sql);
        if ($payment_methods_row = tep_db_fetch_array($payment_methods_result_sql)) {
            $pm_desc_title_select_sql = "	SELECT payment_methods_description_title 
                                                FROM " . TABLE_PAYMENT_METHODS_DESCRIPTION . " 
                                                WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "' 
                                                        AND (IF (languages_id = '" . tep_db_input($languages_id) . "', 1, IF ((	SELECT COUNT(payment_methods_id) > 0 
                                                                                                                                FROM " . TABLE_PAYMENT_METHODS_DESCRIPTION . " 
                                                                                                                                WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "' 
                                                                                                                                        AND languages_id = '" . (int) $languages_id . "'), 0, languages_id = '" . (int) $default_languages_id . "')))";
            $pm_desc_title_result_sql = tep_db_query($pm_desc_title_select_sql);
            $pm_desc_title_row = tep_db_fetch_array($pm_desc_title_result_sql);
            $this->display_title = $pm_desc_title_row['payment_methods_description_title'];
            $this->description = $this->display_title;
            $this->enabled = $payment_methods_row['payment_methods_receive_status_mode'];
            $this->payment_methods_id = (int) $payment_methods_row['payment_methods_id'];
            $this->payment_methods_parent_id = (int) $payment_methods_row['payment_methods_parent_id'];
            $this->code = $payment_methods_row['payment_methods_code'];
            $this->title = $payment_methods_row['payment_methods_title'];
            $this->sort_order = $payment_methods_row['payment_methods_sort_order'];
            $this->legend_display_colour = $payment_methods_row['payment_methods_legend_color'];
        }
        $configuration_setting_array = $this->load_pm_setting();
        $this->order_status_id = $configuration_setting_array['MODULE_PAYMENT_pipwave_ORDER_STATUS_ID'];
        $this->processing_status_id = $configuration_setting_array['MODULE_PAYMENT_pipwave_PROCESSING_STATUS_ID'];
        $this->confirm_complete_days = $configuration_setting_array['MODULE_PAYMENT_pipwave_CONFIRM_COMPLETE'];
        $this->require_address_information = $configuration_setting_array['MODULE_PAYMENT_pipwave_MANDATORY_ADDRESS_FIELD'];
        $this->test_mode = ($configuration_setting_array['MODULE_PAYMENT_pipwave_TEST_MODE'] == 'True' ? true : false);
        $this->message = $configuration_setting_array['MODULE_PAYMENT_pipwave_MESSAGE'];
        $this->email_message = $configuration_setting_array['MODULE_PAYMENT_pipwave_EMAIL_MESSAGE'];
        $this->email_footer = str_replace(array("<br>", "<BR>", "\r\n"), array("\n", "\n", ''), $this->email_message);
        $this->pipwaveCurrencies = $this->get_support_currencies($this->payment_methods_id);
        $this->defCurr = DEFAULT_CURRENCY;
        $this->order_processing_status = ((int) $this->processing_status_id > 0 ? $this->processing_status_id : 7);
        $this->order_status = ((int) $this->order_status_id > 0 ? $this->order_status_id : DEFAULT_ORDERS_STATUS_ID);
    }

    function check_trans_status($order_id)
    {
        $this->check_trans_status_flag = false;
        if ((isset($_GET['oID']) && !empty($_GET['oID']))) {
            $order_id = $_GET['oID'];
        }
        $pw = new pipwave($order_id);
        $pwStatus = $pw->requeryAPI();
        if (isset($pwStatus['status']) && $pwStatus['status'] == 200) {
            if (isset($pwStatus['transaction_status']) && $pwStatus['transaction_status'] == 10) {
                $this->check_trans_status_flag = true;
                return true;
            }
        }
        return false;
    }

    function get_merchant_account($selected_currency)
    {
        //all config done under include->classes->pipwave
    }
    function load_pm_setting($language_id = 1, $pm_id = '')
    {
        $pm_setting_array = array();
        //load value from DB
        if (tep_not_null($pm_id)) {
            $payment_method_id = $pm_id;
            $payment_methods_parent_id_select_sql = "	SELECT payment_methods_parent_id 
														FROM " . TABLE_PAYMENT_METHODS . " 
														WHERE payment_methods_id = '" . (int) $pm_id . "'";
            $payment_methods_parent_id_result_sql = tep_db_query($payment_methods_parent_id_select_sql);
            $payment_methods_parent_id_row = tep_db_fetch_array($payment_methods_parent_id_result_sql);
            $payment_gateway_id = $payment_methods_parent_id_row['payment_methods_parent_id'];
        } else {
            $payment_method_id = $this->payment_methods_id;
            $payment_gateway_id = $this->payment_methods_parent_id;
        }
        $payment_configuration_info_select_sql = "	SELECT pci.payment_configuration_info_key, pcid.payment_configuration_info_value
													FROM " . TABLE_PAYMENT_CONFIGURATION_INFO . " as pci
													LEFT JOIN " . TABLE_PAYMENT_CONFIGURATION_INFO_DESCRIPTION . " as pcid 
														ON pci.payment_configuration_info_id = pcid.payment_configuration_info_id
															AND pcid.languages_id = '" . (int) $language_id . "'
													WHERE pci.payment_methods_id = '" . (int) $payment_method_id . "'
													ORDER BY pci.payment_configuration_info_sort_order";
        $payment_configuration_info_result_sql = tep_db_query($payment_configuration_info_select_sql);
        if (tep_db_num_rows($payment_configuration_info_result_sql)) {
            while ($payment_configuration_info_row = tep_db_fetch_array($payment_configuration_info_result_sql)) {
                $pm_setting_array[$payment_configuration_info_row['payment_configuration_info_key']] = $payment_configuration_info_row['payment_configuration_info_value'];
            }
        } else if ((int) $payment_gateway_id > 0) { //load value from PG
            $payment_configuration_info_select_sql = "	SELECT pci.payment_configuration_info_key, pcid.payment_configuration_info_value
														FROM " . TABLE_PAYMENT_CONFIGURATION_INFO . " as pci
														LEFT JOIN " . TABLE_PAYMENT_CONFIGURATION_INFO_DESCRIPTION . " as pcid 
															ON pci.payment_configuration_info_id = pcid.payment_configuration_info_id
																AND pcid.languages_id = '" . (int) $language_id . "'
														WHERE pci.payment_methods_id = '" . (int) $payment_gateway_id . "'
														ORDER BY pci.payment_configuration_info_sort_order";
            $payment_configuration_info_result_sql = tep_db_query($payment_configuration_info_select_sql);
            while ($payment_configuration_info_row = tep_db_fetch_array($payment_configuration_info_result_sql)) {
                $pm_setting_array[$payment_configuration_info_row['payment_configuration_info_key']] = $payment_configuration_info_row['payment_configuration_info_value'];
            }
        }
        return $pm_setting_array;
    }
    function check()
    {
        if (!isset($this->_check)) {
            $payment_methods_select_sql = "	SELECT payment_methods_id
											FROM " . TABLE_PAYMENT_METHODS . " as pm
											WHERE pm.payment_methods_id = '" . (int) $this->payment_methods_id . "'";
            $payment_methods_result_sql = tep_db_query($payment_methods_select_sql);
            $this->_check = tep_db_num_rows($payment_methods_result_sql);
        }
        return $this->_check;
    }

    public function default_form($payment_methods_send_code)
    {
        switch ($payment_methods_send_code) {
            case 'doku_wallet':
            case 'doku_bank':
                $install_send_payment_key_array = array(
                    array(
                        'payment_methods_fields_title' => 'Account First Name',
                        'payment_methods_fields_pre_info' => '',
                        'payment_methods_fields_post_info' => '',
                        'payment_methods_fields_required' => 1,
                        'payment_methods_fields_type' => '1',
                        'payment_methods_fields_system_type' => 'MODULE_DOKU_ACCOUNT_FNAME',
                        'payment_methods_fields_size' => '128',
                        'payment_methods_fields_option' => 'NULL',
                        'payment_methods_fields_options_title' => '0',
                        'payment_methods_fields_sort_order' => 10000
                    ),
                    array(
                        'payment_methods_fields_title' => 'Account Last Name',
                        'payment_methods_fields_pre_info' => '',
                        'payment_methods_fields_post_info' => '',
                        'payment_methods_fields_required' => 0,
                        'payment_methods_fields_type' => '1',
                        'payment_methods_fields_system_type' => 'MODULE_DOKU_ACCOUNT_LNAME',
                        'payment_methods_fields_size' => '128',
                        'payment_methods_fields_option' => 'NULL',
                        'payment_methods_fields_options_title' => '0',
                        'payment_methods_fields_sort_order' => 10000
                    ),
                );

                switch ($payment_methods_send_code) {
                    case "doku_wallet":
                        $install_send_payment_key_array[] = array(
                            'payment_methods_fields_title' => 'DOKU Wallet ID',
                            'payment_methods_fields_pre_info' => '',
                            'payment_methods_fields_post_info' => '',
                            'payment_methods_fields_required' => 1,
                            'payment_methods_fields_type' => '1',
                            'payment_methods_fields_system_type' => 'MODULE_DOKUWALLET_ID',
                            'payment_methods_fields_size' => '128',
                            'payment_methods_fields_option' => 'NULL',
                            'payment_methods_fields_options_title' => '0',
                            'payment_methods_fields_sort_order' => 40000
                        );
                        if (count($install_send_payment_key_array)) {
                            foreach ($install_send_payment_key_array as $data) {
                                $data['payment_methods_id'] = (int) $this->payment_methods_id;
                                $data['payment_methods_mode'] = 'SEND';
                                $data['payment_methods_fields_system_mandatory'] = 1;
                                $data['payment_methods_fields_status'] = '1';

                                tep_db_perform(TABLE_PAYMENT_METHODS_FIELDS, $data);
                            }
                        }
                        break;
                    case "doku_bank":
                        $bank_list = "BANK PANIN (_019_PINBIDJA_):~:Bank Jatim (_114_PDJTIDJ1_):~:BANK ARTA GRAHA (_037_ARTGIDJA_):~:Bank BTPN (_213_TAPEIDJ1_):~:Bank BJB (_110_PDJBIDJA_):~:BANK MAYAPADA INTERNATIONAL (_097_MAYAIDJA_):~:BANK PERMATA (_013_BBBAIDJA_):~:Bank BCA (_014_CENAIDJA_):~:BANK DANAMON INDONESIA (_011_BDINIDJA_):~:BANK HANA (_484_HNBNIDJA_):~:BII Maybank (_016_IBBKIDJA_):~:BANK SINARMAS (_153_SBJKIDJA_):~:BANK SYARIAH MANDIRI (_451_SYMDIDJ1_):~:CITIBANK (_031_CITIIDJX_):~:BANK BCA SYARIAH (_536_SYCAIDJ1_):~:Bank NTT (_130_PDNTIDJ1_):~:BANK SYARIAH MEGA (_506_BUTGIDJ1_):~:BPD NUSA TENGGARA BARAT (_128_PDNBIDJ1_):~:BANK MANDIRI (_008_BMRIIDJA_):~:BANK MUAMALAT INDONESIA (_147_MUABIDJA_):~:BANK BNI 46 (_009_BNINIDJA_):~:Bank BTN (_200_BTANIDJA_):~:BANK DBS INDONESIA (_046_DBSBIDJA_):~:BANK CIMB NIAGA (_022_BNIAIDJA_):~:BANK COMMONWEALTH (_950_BICNIDJA_):~:BANK UOB BUANA INDONESIA (_023_BBIJIDJA_):~:BPD BALI (_129_PDBAIDJ1_):~:BANK OCBC NISP (_028_NISPIDJA_):~:Bank BRI (_002_BRINIDJA_):~:BANK MEGA (_426_MEGAIDJA_):~:DOKU (_899_899_):~:BANK BUKOPIN (_441_BBUKIDJA_):~:BANK HSBC (_041_HSBCIDJA_):~:BANK BRI SYARIAH (_422_DJARIDJ1_):~:BANK JABAR BANTEN SYARIAH (_425_SYJBIDJ1_):~:Bank BNI Syariah (_427_SYNIIDJA_):~:Bank Chinatrust Indonesia (_949_CTCBIDJA_):~:Bank Mayora (_553_MAYOIDJA_):~:Bank Mutiara (_095_CICTIDJA_):~:Bank Nagari (BPD Sumbar) (_118_PDSBIDSP_):~:Bank National Nobu (_503_NOBUIDJA_):~:Bank Saudara (_212_HVBKIDJA_):~:Bank Standard Chartered (_050_SCBLIDJX_):~:Bank Sumut (_117_PDSUIDSA_):~:Bank Windu Kencana (_036_BWKIIDJA_):~:BPD Aceh (_116_PDACIDJ1_):~:BPD Jawa Tengah (_113_PDJGIDJA_):~:BPD Kalimantan Barat (_123_PDKBIDJ1_):~:BPD Kalimantan Selatan (_122_PDKSIDJ1_):~:BPD Kalimantan Timur (_124_PDKTIDJA_):~:BPD Lampung (_121_PDLPIDJ1_):~:BPD Riau (_119_PDRIIDJA_):~:BPD Sulawesi Selatan dan Barat (_126_PDWSIDJ1_):~:BPD Sulawesi Utara (_127_PDWUIDJ1_):~:BPD Sulawesi Utara (_127_PDWUIDJ1_):~:BPD Sumatera Selatan (_120_BSSPIDSP_):~:BPD Yogyakarta (_112_PDYKIDJ1_):~:BTN SYARIAH (_785_BTANIDJA_):~:DANAMON SYARIAH (_771_BDINIDJA_):~:Deutsche Bank AG (_067_DEUTIDJA_):~:ICB Bumiputera (_485_BUMIIDJA_):~:MAYBANK INDONESIA SYARIAH (_777_MBBEIDJA_):~:Maybank Syariah (_947_MBBEIDJA_):~:OCBC NISP SYARIAH (_799_NISPIDJA_):~:PERMATA SYARIAH (_784_BBBAIDJA_):~:QNB Kesawan (_167_AWANIDJA_):~:Rabobank (_089_RABOIDJA_):~:SINARMAS SYARIAH (_798_SBJKIDJA_)";
                        $install_send_payment_key_array[] = array(
                            'payment_methods_fields_title' => 'Bank',
                            'payment_methods_fields_pre_info' => '',
                            'payment_methods_fields_post_info' => '',
                            'payment_methods_fields_required' => 1,
                            'payment_methods_fields_type' => '3',
                            'payment_methods_fields_system_type' => 'MODULE_DOKUBANK_CODE',
                            'payment_methods_fields_size' => 'NULL',
                            'payment_methods_fields_option' => $bank_list,
                            'payment_methods_fields_options_title' => '1000',
                            'payment_methods_fields_sort_order' => 40000
                        );
                        $install_send_payment_key_array[] = array(
                            'payment_methods_fields_title' => 'Account Number',
                            'payment_methods_fields_pre_info' => '',
                            'payment_methods_fields_post_info' => '',
                            'payment_methods_fields_required' => 1,
                            'payment_methods_fields_type' => '1',
                            'payment_methods_fields_system_type' => 'MODULE_DOKUBANK_ACCOUNT',
                            'payment_methods_fields_size' => '55',
                            'payment_methods_fields_option' => 'NULL',
                            'payment_methods_fields_options_title' => '0',
                            'payment_methods_fields_sort_order' => 50000
                        );
                        $install_send_payment_key_array[] = array(
                            'payment_methods_fields_title' => 'Address',
                            'payment_methods_fields_pre_info' => '',
                            'payment_methods_fields_post_info' => '',
                            'payment_methods_fields_required' => 1,
                            'payment_methods_fields_type' => '1',
                            'payment_methods_fields_system_type' => 'MODULE_DOKUBANK_ADDRESS',
                            'payment_methods_fields_size' => '256',
                            'payment_methods_fields_option' => 'NULL',
                            'payment_methods_fields_options_title' => '0',
                            'payment_methods_fields_sort_order' => 20000
                        );
                        $install_send_payment_key_array[] = array(
                            'payment_methods_fields_title' => 'City',
                            'payment_methods_fields_pre_info' => '',
                            'payment_methods_fields_post_info' => '',
                            'payment_methods_fields_required' => 1,
                            'payment_methods_fields_type' => '1',
                            'payment_methods_fields_system_type' => 'MODULE_DOKUBANK_CITY',
                            'payment_methods_fields_size' => '64',
                            'payment_methods_fields_option' => 'NULL',
                            'payment_methods_fields_options_title' => '0',
                            'payment_methods_fields_sort_order' => 30000
                        );

                        if (count($install_send_payment_key_array)) {
                            foreach ($install_send_payment_key_array as $data) {
                                $data['payment_methods_id'] = (int) $this->payment_methods_id;
                                $data['payment_methods_mode'] = 'SEND';
                                $data['payment_methods_fields_system_mandatory'] = 1;
                                $data['payment_methods_fields_status'] = '1';

                                tep_db_perform(TABLE_PAYMENT_METHODS_FIELDS, $data);
                            }
                        }
                        break;
                }
                break;
            case 'hyperwallet_bank':
                $install_send_payment_key_array = array(
                    array(
                        'payment_methods_fields_title' => 'Transfer Method',
                        'payment_methods_fields_pre_info' => '',
                        'payment_methods_fields_post_info' => '',
                        'payment_methods_fields_required' => 1,
                        'payment_methods_fields_type' => '8',
                        'payment_methods_fields_system_type' => 'MODULE_HW_TRANSFER_METHOD',
                        'payment_methods_fields_size' => '256',
                        'payment_methods_fields_option' => 'NULL',
                        'payment_methods_fields_options_title' => '0',
                        'payment_methods_fields_sort_order' => 10000
                    ),
                );
                if (count($install_send_payment_key_array)) {
                    foreach ($install_send_payment_key_array as $data) {
                        $data['payment_methods_id'] = (int) $this->payment_methods_id;
                        $data['payment_methods_mode'] = 'SEND';
                        $data['payment_methods_fields_system_mandatory'] = 1;
                        $data['payment_methods_fields_status'] = '1';

                        tep_db_perform(TABLE_PAYMENT_METHODS_FIELDS, $data);
                    }
                }
                break;
        }
    }

    function install()
    {
        $install_configuration_flag = true;
        if ($this->payment_methods_id > 0) {
            if ($this->receive_status != 1) {
                $update_receive_status_sql_data = array("payment_methods_receive_status" => 1);
                tep_db_perform(TABLE_PAYMENT_METHODS, $update_receive_status_sql_data, 'update', "payment_methods_id = '" . (int) $this->payment_methods_id . "'");
            } else {
                $install_configuration_flag = false;
            }
        } else {
            $install_payment_gateway_data_sql = array(
                'payment_methods_code' => $this->code,
                'payment_methods_send_status' => 0,
                'payment_methods_title' => $this->code,
                'payment_methods_types_id' => 0,
                'payment_methods_parent_id' => 0,
                'payment_methods_legend_color' => '#460e56',
                'payment_methods_sort_order' => 6,
                'payment_methods_filename' => $this->filename,
                'payment_methods_logo' => '',
                'date_added' => 'now()',
                'last_modified' => 'now()',
                'payment_methods_receive_status' => 1,
                'payment_methods_receive_status_mode' => 0
            );
            tep_db_perform(TABLE_PAYMENT_METHODS, $install_payment_gateway_data_sql);
            $this->payment_methods_id = tep_db_insert_id();
        }
        if ($install_configuration_flag) {
            $install_key_array = array();
            $install_key_array[] = array(
                'info' => array(
                    'payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Set Order Status',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_pipwave_ORDER_STATUS_ID',
                    'payment_configuration_info_description' => 'Set the status of orders made with this payment module to this value.',
                    'payment_configuration_info_sort_order' => '1335',
                    'set_function' => 'tep_cfg_pull_down_order_statuses(',
                    'use_function' => 'tep_get_order_status_name',
                    'date_added' => 'now()'
                ),
                'desc' => array(
                    'payment_configuration_info_value' => '1',
                    'languages_id' => 1
                )
            );

            $install_key_array[] = array(
                'info' => array(
                    'payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Set Order\'s "Processing" Status',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_pipwave_PROCESSING_STATUS_ID',
                    'payment_configuration_info_description' => 'Set the initial processing status of orders made with this payment module to this value.',
                    'payment_configuration_info_sort_order' => '1340',
                    'set_function' => 'tep_cfg_pull_down_order_statuses(',
                    'use_function' => 'tep_get_order_status_name',
                    'date_added' => 'now()'
                ),
                'desc' => array(
                    'payment_configuration_info_value' => '7',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array(
                'info' => array(
                    'payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Payment Message',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_pipwave_MESSAGE',
                    'payment_configuration_info_description' => 'Payment message will show up during checkout process (pipwave).',
                    'payment_configuration_info_sort_order' => '1350',
                    'set_function' => 'tep_cfg_textarea(',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array(
                    'payment_configuration_info_value' => 'Thank you for shopping at OffGamers.com.',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array(
                'info' => array(
                    'payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Payment Email Message',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_pipwave_EMAIL_MESSAGE',
                    'payment_configuration_info_description' => 'This message will show up in order confirmation e-mail.',
                    'payment_configuration_info_sort_order' => '1360',
                    'set_function' => 'tep_cfg_textarea(',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array(
                    'payment_configuration_info_value' => 'Thank you for shopping at OffGamers.com.',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array(
                'info' => array(
                    'payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Confirm Complete (in days)',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_pipwave_CONFIRM_COMPLETE',
                    'payment_configuration_info_description' => 'The number of days in which an order made by this payment method is confirm completed.',
                    'payment_configuration_info_sort_order' => '1265',
                    'set_function' => '',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array(
                    'payment_configuration_info_value' => '0',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array(
                'info' => array(
                    'payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Test Mode',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_pipwave_TEST_MODE',
                    'payment_configuration_info_description' => 'Testing Mode?',
                    'payment_configuration_info_sort_order' => '100',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array(
                    'payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array(
                'info' => array(
                    'payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Require Address Information',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_pipwave_MANDATORY_ADDRESS_FIELD',
                    'payment_configuration_info_description' => 'Set address field as required info during checkout.',
                    'payment_configuration_info_sort_order' => '1400',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array(
                    'payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );
            foreach ($install_key_array as $data) {
                tep_db_perform(TABLE_PAYMENT_CONFIGURATION_INFO, $data['info']);
                $payment_conf_id = tep_db_insert_id();
                $data['desc']['payment_configuration_info_id'] = (int) $payment_conf_id;
                tep_db_perform(TABLE_PAYMENT_CONFIGURATION_INFO_DESCRIPTION, $data['desc']);
            }
        }
        return 1;
    }
    function merchant_information_keys()
    {
        include_once(DIR_WS_CLASSES . "payment_methods.php");
        return array();
    }
    function get_orders_payment_info_file()
    {
        return DIR_FS_CATALOG_MODULES . 'payment/pipwave/admin/orders.inc.php';
    }
    function get_orders_list_payment_info_file()
    {
        return DIR_FS_CATALOG_MODULES . 'payment/pipwave/admin/orders_list.inc.php';
    }


    function outgoing_merchant_information_keys()
    {
        return array(
            'MODULE_PAYMENT_OUTGOING_API_MERCHANT' => array("field" => 'text', 'mapping' => "MODULE_PAYMENT_OUTGOING_API_MERCHANT"),
            'MODULE_PAYMENT_OUTGOING_API_USERNAME' => array("field" => 'text', 'mapping' => "MODULE_PAYMENT_OUTGOING_API_USERNAME"),
            'MODULE_PAYMENT_OUTGOING_API_PASSWORD' => array("field" => 'text', 'mapping' => "MODULE_PAYMENT_OUTGOING_API_PASSWORD"),
            'MODULE_PAYMENT_OUTGOING_API_EXTRA' => array("field" => 'text', 'mapping' => "MODULE_PAYMENT_OUTGOING_API_EXTRA"),
        );
    }

    function get_merchant_outgoing_account($selected_currency)
    {
        $payment_methods_outgoing_instance_id = 0;
        $payment_gateway_instance_select_sql = "SELECT pmi.payment_methods_outgoing_instance_id, pmi.payment_methods_outgoing_instance_follow_default, pmi.currency_code 
                                                                                            FROM " . TABLE_PAYMENT_METHODS_OUTGOING_INSTANCE . " as pmi
                                                                                            WHERE pmi.payment_methods_id = '" . (int) $this->payment_methods_id . "'
                                                                                            LIMIT 1";
        $payment_gateway_instance_result_sql = tep_db_query($payment_gateway_instance_select_sql);
        if (tep_db_num_rows($payment_gateway_instance_result_sql)) {
            $payment_gateway_instance_select_sql = "SELECT pmi.payment_methods_outgoing_instance_id, pmi.payment_methods_outgoing_instance_follow_default 
                                                                                                    FROM " . TABLE_PAYMENT_METHODS_OUTGOING_INSTANCE . " as pmi
                                                                                                    WHERE pmi.currency_code = '" . tep_db_input($selected_currency) . "' 
                                                                                                            AND pmi.payment_methods_id = '" . (int) $this->payment_methods_id . "'";
            $payment_gateway_instance_result_sql = tep_db_query($payment_gateway_instance_select_sql);
            if ($payment_gateway_instance_row = tep_db_fetch_array($payment_gateway_instance_result_sql)) {
                if ((int) $payment_gateway_instance_row['payment_methods_outgoing_instance_follow_default'] > 0) { // Follow Default Currency Setting
                    $payment_methods_outgoing_instance_id = (int) $payment_gateway_instance_row['payment_methods_outgoing_instance_follow_default'];
                } else { // Has own setting
                    $payment_methods_outgoing_instance_id = (int) $payment_gateway_instance_row['payment_methods_outgoing_instance_id'];
                }
            }
        } else {
            $payment_gateway_instance_select_sql = "SELECT pmi.payment_methods_outgoing_instance_id, pmi.payment_methods_outgoing_instance_follow_default 
                                                                                                    FROM " . TABLE_PAYMENT_METHODS_OUTGOING_INSTANCE . " as pmi
                                                                                                    INNER JOIN " . TABLE_PAYMENT_METHODS . " as pm
                                                                                                            ON pm.payment_methods_parent_id = pmi.payment_methods_id
                                                                                                    WHERE pmi.currency_code = '" . tep_db_input($selected_currency) . "' 
                                                                                                            AND pm.payment_methods_id = '" . (int) $this->payment_methods_id . "'";
            $payment_gateway_instance_result_sql = tep_db_query($payment_gateway_instance_select_sql);
            if ($payment_gateway_instance_row = tep_db_fetch_array($payment_gateway_instance_result_sql)) {
                if ((int) $payment_gateway_instance_row['payment_methods_outgoing_instance_follow_default'] > 0) {
                    $payment_methods_outgoing_instance_id = (int) $payment_gateway_instance_row['payment_methods_outgoing_instance_follow_default'];
                } else {
                    $payment_methods_outgoing_instance_id = (int) $payment_gateway_instance_row['payment_methods_outgoing_instance_id'];
                }
            }
        }

        $payment_gateway_instance_setting_select_sql = "	SELECT pmis.payment_methods_outgoing_instance_setting_key, pmis.payment_methods_outgoing_instance_setting_value 
                                                                                                                            FROM " . TABLE_PAYMENT_METHODS_OUTGOING_INSTANCE_SETTING . " as pmis
                                                                                                                            WHERE pmis.payment_methods_outgoing_instance_id = '" . (int) $payment_methods_outgoing_instance_id . "'";
        $payment_gateway_instance_setting_result_sql = tep_db_query($payment_gateway_instance_setting_select_sql);
        while ($payment_gateway_instance_setting_row = tep_db_fetch_array($payment_gateway_instance_setting_result_sql)) {
            switch ($payment_gateway_instance_setting_row['payment_methods_outgoing_instance_setting_key']) {
                case 'MODULE_PAYMENT_OUTGOING_API_MERCHANT':
                    $this->outgoing_api_merchant = $payment_gateway_instance_setting_row['payment_methods_outgoing_instance_setting_value'];
                    break;
                case 'MODULE_PAYMENT_OUTGOING_API_USERNAME':
                    $this->outgoing_api_username = $payment_gateway_instance_setting_row['payment_methods_outgoing_instance_setting_value'];
                    break;
                case 'MODULE_PAYMENT_OUTGOING_API_PASSWORD':
                    $this->outgoing_api_password = $payment_gateway_instance_setting_row['payment_methods_outgoing_instance_setting_value'];
                    break;
                case 'MODULE_PAYMENT_OUTGOING_API_EXTRA':
                    $this->outgoing_api_extra = $payment_gateway_instance_setting_row['payment_methods_outgoing_instance_setting_value'];
                    break;
            }
        }

        $pm_status_message_select = "	SELECT payment_methods_status_message 
                                                                                            FROM " . TABLE_PAYMENT_METHODS_STATUS_DESCRIPTION . " 
                                                                                            WHERE payment_methods_mode = 'SEND_CODE' 
                                                                                                    AND payment_methods_status = '-1' 
                                                                                                    AND languages_id = '1' 
                                                                                                    AND payment_methods_id = '" . (int) $this->payment_methods_id . "'";
        $pm_status_message_result = tep_db_query($pm_status_message_select);
        if ($pm_status_message_row = tep_db_fetch_array($pm_status_message_result)) {
            $this->outgoing_merchant_code = $pm_status_message_row['payment_methods_status_message'];
        }
    }

    public function getBalance($selected_currency = "")
    {
        $error = '';
        $this->_getApiUrl($this->outgoing_merchant_code);
        switch ($this->outgoing_merchant_code) {
            case 'payoneer':
            case 'payoneer_gbt':
                $base = $this->outgoing_api_username . ':' . $this->outgoing_api_password;
                $auth = base64_encode($base);
                $header = [
                    'authorization' => 'Basic ' . $auth,
                    "Accept" => "application/json",
                    "Content-Type" => "application/json",
                ];
                $url = $this->api_url . "/" . $this->outgoing_api_merchant . "/balance";
                $result = $this->restapi($url, 'GET', "", $header);

                if (isset($result['currency']) && isset($result['balance'])) {
                    return $result['currency'] . " " . $result['balance'];
                } else if (isset($result['description'])) {
                    $error = ": " . $result['description'];
                }
                break;
            case 'doku_wallet':
            case 'doku_bank':
                $requestId = time();
                $signature = openssl_encrypt($this->outgoing_api_username . $requestId, 'AES-128-ECB', $this->outgoing_api_password);
                $header = [
                    "Content-Type" => "application/json",
                ];
                $data = array("agentKey" => $this->outgoing_api_username, 'requestId' => $requestId, 'signature' => $signature);
                $url = $this->api_url . "/checkbalance";
                $result = $this->restapi($url, 'POST', json_encode($data), $header);
                if (isset($result['balance']) && isset($result['balance']['creditLastBalance'])) {
                    return "IDR " . $result['balance']['creditLastBalance'];
                } else if (isset($result['message'])) {
                    $error = ": " . $result['message'];
                }
                break;
            case 'hyperwallet_bank':
                $header = [
                    "Accept" => "application/json",
                    "Content-Type" => "application/json",
                ];

                $url = $this->api_url . "/programs/" . $this->outgoing_api_merchant . "/accounts/" . $this->outgoing_api_extra . "/balances?currency=" . $selected_currency;
                $result = $this->restapi($url, "GET", "", $header, 'body', $this->outgoing_api_password);

                if (isset($result['data'])) {
                    $balance = "";
                    foreach ($result['data'] as $data) {
                        if (isset($data['amount']) && $data['amount'] > 0) {
                            $balance .= $data['currency'] . " " . $data['amount'] . ',';
                        }
                    }
                    return rtrim($balance, ',');
                }

                break;
        }

        return 'Invalid data' . $error;
    }

    function massPay($pass_id)
    {
        if (!tep_not_null($pass_id) || count($pass_id) == 0)
            return 0;

        $return_result_array = array();
        foreach ($pass_id as $store_payments_id_loop) {
            $return_result_array[$store_payments_id_loop] = 0;
        }

        $store_payments_array = array();
        $payments_methods_id_array = array();
        $store_payments_select_sql = "	SELECT sp.user_id, sp.store_payments_id, sp.store_payments_request_currency, sp.store_payments_paid_currency, sp.store_payments_paid_currency_value, sp.store_payments_methods_id, sp.store_payments_after_fees_amount 
										FROM " . TABLE_STORE_PAYMENTS . " AS sp 
										WHERE sp.store_payments_id IN ('" . implode("','", $pass_id) . "')
											AND sp.store_payments_lock = '0'
											AND sp.store_payments_status = '2'";  // only those in processing status and not locked order
        $store_payments_result_sql = tep_db_query($store_payments_select_sql);
        while ($store_payments_row = tep_db_fetch_array($store_payments_result_sql)) {
            $store_payments_currency_array[$store_payments_row['store_payments_methods_id']][$store_payments_row['store_payments_paid_currency']][] = $store_payments_row['store_payments_id'];
            $actual_payout_amount = 0;
            if ($store_payments_row['store_payments_request_currency'] != $store_payments_row['store_payments_paid_currency']) {
                $latest_ex_rate = $store_payments_row['store_payments_paid_currency_value'];
                if (is_numeric($latest_ex_rate) && $latest_ex_rate > 0) {
                    $latest_ex_rate = (float) $latest_ex_rate;
                    $actual_payout_amount = $latest_ex_rate * $store_payments_row['store_payments_after_fees_amount'];
                } else {
                    continue;
                }
            } else {
                $actual_payout_amount = $store_payments_row['store_payments_after_fees_amount'];
            }
            $store_payments_array[$store_payments_row['store_payments_methods_id']][$store_payments_row['store_payments_id']]['amount'] = number_format($actual_payout_amount, 2, ".", "");
            $store_payments_array[$store_payments_row['store_payments_methods_id']][$store_payments_row['store_payments_id']]['user'] = $store_payments_row['user_id'];
        }

        $payments_methods_fields_array = array();
        if (count($store_payments_array)) {
            $admin_id = isset($_SESSION['login_id']) ? $_SESSION['login_id'] : '';
            $xmlhttp_payments_object = new payments($admin_id, $_SESSION['login_email_address']);
            foreach ($store_payments_array as $store_payments_methods_id_loop => $store_payments_data_loop) {
                //$store_payments_methods_data_loop // store payments id
                $payment_methods_fields_select_sql = "	SELECT pmf.payment_methods_id, pmf.payment_methods_fields_title, pmf.payment_methods_fields_id, pmf.payment_methods_fields_system_type 
														FROM " . TABLE_PAYMENT_METHODS_FIELDS . " AS pmf
														WHERE pmf.payment_methods_id = '" . $store_payments_methods_id_loop . "'
															AND payment_methods_fields_system_type != ''";
                $payment_methods_fields_result_sql = tep_db_query($payment_methods_fields_select_sql);
                if (!tep_db_num_rows($payment_methods_fields_result_sql)) {
                    $payment_methods_fields_select_sql = "	SELECT pmf.payment_methods_id, pmf.payment_methods_fields_title, pmf.payment_methods_fields_id, pmf.payment_methods_fields_system_type 
															FROM " . TABLE_PAYMENT_METHODS_FIELDS . " AS pmf
															LEFT JOIN " . TABLE_PAYMENT_METHODS . " AS pm
																ON pm.payment_methods_parent_id = pmf.payment_methods_id
															WHERE pm.payment_methods_id = '" . $store_payments_methods_id_loop . "'
																AND payment_methods_fields_system_type != ''";
                    $payment_methods_fields_result_sql = tep_db_query($payment_methods_fields_select_sql);
                }

                while ($payment_methods_fields_row = tep_db_fetch_array($payment_methods_fields_result_sql)) {
                    // if ($payment_methods_fields_row['payment_methods_fields_system_type'] != "") {
                    $payments_methods_id_array[$store_payments_methods_id_loop][] = array(
                        'payment_methods_fields_system_type' => $payment_methods_fields_row['payment_methods_fields_system_type'],
                        'payment_methods_fields_id' => $payment_methods_fields_row['payment_methods_fields_id']
                    );
                    // }
                }
            }

            foreach ($store_payments_array as $store_payments_methods_id_loop => $store_payments_data_array_loop) {
                foreach ($store_payments_data_array_loop as $store_payments_id_loop => $store_payments_data_loop) {
                    if (isset($payments_methods_id_array[$store_payments_methods_id_loop])) {
                        foreach ($payments_methods_id_array[$store_payments_methods_id_loop] as $payments_methods_id_array_value) {
                            $store_payments_details_select_sql = "	SELECT payment_methods_fields_value 
															FROM " . TABLE_STORE_PAYMENTS_DETAILS . " AS spd 
															WHERE spd.store_payments_id = '" . (int) $store_payments_id_loop . "'
																AND spd.payment_methods_fields_id = '" . (int) $payments_methods_id_array_value['payment_methods_fields_id'] . "'";
                            $store_payments_details_result_sql = tep_db_query($store_payments_details_select_sql);
                            while ($store_payments_details_row = tep_db_fetch_array($store_payments_details_result_sql)) {
                                $store_payments_array[$store_payments_methods_id_loop][$store_payments_id_loop][$payments_methods_id_array_value['payment_methods_fields_system_type']] = $store_payments_details_row['payment_methods_fields_value'];
                            }
                        }
                    }
                }
            }

            foreach ($store_payments_currency_array as $payment_methods_id_loop => $store_payments_currency_array_loop) {
                foreach ($store_payments_currency_array_loop as $store_payments_currency_loop => $store_payments_data_array_loop) {
                    $this->get_merchant_outgoing_account($store_payments_currency_loop);

                    if (isset($store_payments_array[$payment_methods_id_loop]) && count($store_payments_array[$payment_methods_id_loop])) {
                        $curl_submit_array = array();
                        $curl_submit_count = 0;
                        $batch_count = 0;

                        foreach ($store_payments_array[$payment_methods_id_loop] as $store_payments_id_array => $store_payments_data_array) {
                            if ($batch_count > 255) {
                                $curl_submit_count++;
                                $batch_count = 0;
                            }

                            foreach ($store_payments_data_array as $store_payments_key_loop => $store_payments_data_loop) {
                                switch ($store_payments_key_loop) {
                                    case 'email':
                                        $curl_submit_array[$curl_submit_count]['store_payment'][$store_payments_id_array]['email'] = $store_payments_data_loop;
                                        break;
                                    case 'amount':
                                        $curl_submit_array[$curl_submit_count]['store_payment'][$store_payments_id_array]['amount'] = $store_payments_data_loop;
                                        break;
                                    case 'user':
                                        $curl_submit_array[$curl_submit_count]['store_payment'][$store_payments_id_array]['user'] = $store_payments_data_loop;
                                        break;
                                    default:
                                        $curl_submit_array[$curl_submit_count]['store_payment'][$store_payments_id_array][$store_payments_key_loop] = $store_payments_data_loop;
                                }
                            }
                            $batch_count++;
                        }

                        foreach ($curl_submit_array as $curl_submit_count_loop => $curl_submit_data_loop) {
                            foreach ($curl_submit_data_loop['store_payment'] as $store_payment_id_loop => $store_payment_data_loop) {
                                $apiResponse = array();
                                $apiResponseStr = "";
                                $process_proceed = true;
                                $referrenceId = "";
                                $payment_status = '0';
                                $this->_getApiUrl($this->outgoing_merchant_code);
                                switch ($this->outgoing_merchant_code) {
                                    case 'payoneer':
                                    case 'payoneer_gbt':
                                        $base = $this->outgoing_api_username . ':' . $this->outgoing_api_password;
                                        $auth = base64_encode($base);
                                        $header = [
                                            'authorization' => 'Basic ' . $auth,
                                            "Accept" => "application/json",
                                            "Content-Type" => "application/json",
                                        ];


                                        $url = $this->api_url . "/" . $this->outgoing_api_merchant . "/payees/" . $store_payment_data_loop['user'] . "/details";
                                        $apiResponse = $this->restapi($url, 'GET', "", $header);
                                        foreach ($apiResponse as $apiResponseKey => $apiResponseValue) {
                                            $apiResponseStr .= $apiResponseKey . ': ' . $apiResponseValue . '</br>';
                                        }
                                        if (isset($apiResponse['code']) && $apiResponse['code'] == "0" && isset($apiResponse['status'])) {
                                            $apiResponseStr = '';
                                            if ($apiResponse['status'] == "ACTIVE") {
                                                if ($this->outgoing_merchant_code == 'payoneer_gbt') {
                                                    if (empty($apiResponse['payout_method']['type']) || empty($apiResponse['payout_method']['currency'])) {
                                                        $process_proceed = false;
                                                        $return_result_array[$store_payment_id_loop] = "Unexpected Payoneer response";
                                                    } else if ($apiResponse['payout_method']['type'] != 'BANK') {
                                                        $process_proceed = false;
                                                        $return_result_array[$store_payment_id_loop] = "Payoneer account payout method is not BANK";
                                                    } else if ($apiResponse['payout_method']['currency'] != $store_payments_currency_loop) {
                                                        $process_proceed = false;
                                                        $return_result_array[$store_payment_id_loop] = "Payoneer account payout currency is not ";
                                                    }
                                                }
                                                if ($process_proceed) {
                                                    $customers_select_sql = "	SELECT customers_firstname, customers_lastname
                                                                                    FROM " . TABLE_CUSTOMERS . "
                                                                                    WHERE customers_id = '" . $store_payment_data_loop['user'] . "'";
                                                    $customers_result_sql = tep_db_query($customers_select_sql);
                                                    if ($customers_result_row = tep_db_fetch_array($customers_result_sql)) {
                                                        // $shasso_first_name = strtolower(str_replace(' ', '', $customers_result_row['customers_firstname'] . $customers_result_row['customers_lastname']));
                                                        // $shasso_last_name = strtolower(str_replace(' ', '', $customers_result_row['customers_lastname'] . $customers_result_row['customers_firstname']));
                                                        // $client_name = strtolower(str_replace(' ', '', $apiResponse['contact']['first_name'] . $apiResponse['contact']['last_name']));
                                                        // if (($shasso_first_name == $client_name) || ($shasso_last_name == $client_name)) {
                                                            $url = $this->api_url . "/" . $this->outgoing_api_merchant . "/payouts";
                                                            $data = json_encode(array("payee_id" => $store_payment_data_loop['user'], 'amount' => $store_payment_data_loop['amount'], 'client_reference_id' => $store_payment_id_loop, 'description' => 'Payout', 'currency' => $store_payments_currency_loop));
                                                            $apiResponse = $this->restapi($url, 'POST', $data, $header);

                                                            foreach ($apiResponse as $apiResponseKey => $apiResponseValue) {
                                                                $apiResponseStr .= $apiResponseKey . ': ' . $apiResponseValue . '</br>';
                                                            }

                                                            if (isset($apiResponse['code']) && $apiResponse['code'] == "0" && isset($apiResponse['payout_id'])) {
                                                                $referrenceId = "Payoneer Payment ID: " . $apiResponse['payout_id'];
                                                                // $payment_status = '3'; //temporary set to not auto complete until clarify with Payoneer how to handle pending case
                                                            } else if (isset($apiResponse['description'])) {
                                                                $return_result_array[$store_payment_id_loop] = $apiResponse['description'];
                                                            } else {
                                                                $return_result_array[$store_payment_id_loop] = "Payoneer payout API failed, please try again";
                                                            }
                                                        // } else {
                                                        //     $process_proceed = false;
                                                        //     $return_result_array[$store_payment_id_loop] = "Payoneer Payee Name (" . $apiResponse['contact']['last_name'] . " " . $apiResponse['contact']['first_name'] . ") does NOT match with Shasso Customer Name (" . $customers_result_row['customers_lastname'] . " " . $customers_result_row['customers_firstname'] . ")";
                                                        // }
                                                    } else {
                                                        $process_proceed = false;
                                                        $return_result_array[$store_payment_id_loop] = "Failed to retrieved Shasso customer profile, please try again";
                                                    }
                                                }
                                            } else {
                                                $process_proceed = false;
                                                $return_result_array[$store_payment_id_loop] = "Payoneer Payee account INACTIVE";
                                            }
                                        } else if (isset($apiResponse['description'])) {
                                            $return_result_array[$store_payment_id_loop] = $apiResponse['description'] . " (" . $apiResponse['code'] . ")";
                                        } else {
                                            $return_result_array[$store_payment_id_loop] = "Payoneer get details API failed, please try again";
                                        }
                                        break;
                                    case 'doku_wallet':
                                        if (isset($store_payment_data_loop['MODULE_DOKUWALLET_ID'])) {
                                            $customers_select_sql = "	SELECT customers_firstname, customers_lastname, customers_country_dialing_code_id, customers_telephone
                                                                        FROM " . TABLE_CUSTOMERS . "
                                                                        WHERE customers_id = '" . $store_payment_data_loop['user'] . "'";
                                            $customers_result_sql = tep_db_query($customers_select_sql);
                                            if ($customers_result_row = tep_db_fetch_array($customers_result_sql)) {
                                                // $shasso_first_name = strtolower(str_replace(' ', '', $customers_result_row['customers_firstname'] . $customers_result_row['customers_lastname']));
                                                // $shasso_last_name = strtolower(str_replace(' ', '', $customers_result_row['customers_lastname'] . $customers_result_row['customers_firstname']));
                                                $signature = openssl_encrypt($this->outgoing_api_username . $store_payment_id_loop, 'AES-128-ECB', $this->outgoing_api_password);
                                                $header = [
                                                    "Content-Type" => "application/json",
                                                    "agentKey" => $this->outgoing_api_username,
                                                    'requestId' => $store_payment_id_loop,
                                                    'signature' => $signature,
                                                ];
                                                //inquiry
                                                $data = array(
                                                    // "agentKey" => $this->outgoing_api_username,
                                                    // 'requestId' => $store_payment_id_loop,
                                                    // 'signature' => $signature,
                                                    'beneficiaryCountry' => array(
                                                        'code' => 'ID'
                                                    ),
                                                    'beneficiaryCurrency' => array(
                                                        'code' => 'IDR'
                                                    ),
                                                    'channel' => array(
                                                        'code' => '04'
                                                    ),
                                                    'senderCountry' => array(
                                                        'code' => 'ID'
                                                    ),
                                                    'senderCurrency' => array(
                                                        'code' => 'IDR'
                                                    ),
                                                    // "beneficiaryAccount" => array(
                                                    //     "address" => $store_payment_data_loop['MODULE_DOKU_ADDRESS'],
                                                    //     "city" => $store_payment_data_loop['MODULE_DOKU_CITY'],
                                                    //     "name" => $store_payment_data_loop['MODULE_DOKU_ACCOUNT_NAME'],
                                                    // ),
                                                    // 'senderNote' => 'Payment ID: ' . $store_payment_id_loop,
                                                    'beneficiaryWalletId' => $store_payment_data_loop['MODULE_DOKUWALLET_ID'],
                                                    'senderAmount' => $store_payment_data_loop['amount'],
                                                );
                                                $url = $this->api_url . "/cashin/inquiry";
                                                $apiResponse = $this->restapi($url, 'POST', json_encode($data), $header);
                                                $apiResponseStr = "Inquiry: <br />";
                                                foreach ($apiResponse as $apiResponseKey => $apiResponseValue) {
                                                    switch ($apiResponseKey) {
                                                        case 'status':
                                                        case 'message':
                                                            $apiResponseStr .= $apiResponseKey . ': ' . $apiResponseValue . '</br>';
                                                            break;
                                                        default:
                                                            $apiResponseStr .= $apiResponseKey . ': ' . json_encode($apiResponseValue) . '</br>';
                                                            break;
                                                    }
                                                }
                                                // $apiResponseStr .= json_encode($data);


                                                if (isset($apiResponse['status']) && $apiResponse['status'] == "0" && isset($apiResponse['inquiry']['idToken'])) {
                                                    $client_name = isset($apiResponse['inquiry']['beneficiaryWalletName']) ? $apiResponse['inquiry']['beneficiaryWalletName'] : "";
                                                    // $shasso_first_name = "BENEFICIARY NAME"; //dummy
                                                    if ($client_name) {
                                                        // $client_name_check = strtolower(str_replace(' ', '', $client_name));
                                                        // if (($shasso_first_name == $client_name_check) || ($shasso_last_name == $client_name_check)) {
                                                            $mobile_number = 0;
                                                            //end get customer mobile number
                                                            $countries_select_sql = "	SELECT countries_international_dialing_code
                                                                                        FROM " . TABLE_COUNTRIES . "
                                                                                        WHERE countries_id = '" . $customers_result_row['customers_country_dialing_code_id'] . "'";
                                                            $ccountries_result_sql = tep_db_query($countries_select_sql);
                                                            if ($countries_result_row = tep_db_fetch_array($ccountries_result_sql)) {
                                                                $mobile_number = $countries_result_row['countries_international_dialing_code'] . $customers_result_row['customers_telephone'];
                                                            }
                                                            //end get customer mobile number

                                                            $referrenceId = "Inquiry ID Token: " . $apiResponse['inquiry']['idToken'];
                                                            //remit
                                                            $data = array(
                                                                // "agentKey" => $this->outgoing_api_username,
                                                                // 'requestId' => $store_payment_id_loop,
                                                                // 'signature' => $signature,
                                                                'inquiry' => array(
                                                                    'idToken' => $apiResponse['inquiry']['idToken']
                                                                ),
                                                                'beneficiaryCountry' => array(
                                                                    'code' => 'ID'
                                                                ),
                                                                'beneficiaryCurrency' => array(
                                                                    'code' => 'IDR'
                                                                ),
                                                                'channel' => array(
                                                                    'code' => '04'
                                                                ),
                                                                'senderCountry' => array(
                                                                    'code' => 'ID'
                                                                ),
                                                                // 'senderNote' => 'Payment ID: ' . $store_payment_id_loop,
                                                                'senderAmount' => $store_payment_data_loop['amount'],
                                                                'senderCurrency' => array(
                                                                    'code' => 'IDR'
                                                                ),
                                                                'sendTrxId' => $store_payment_id_loop,
                                                                'sender' => array(
                                                                    'birthDate' => '2000-10-16',
                                                                    'country' => array(
                                                                        'code' => 'SG'
                                                                    ),
                                                                    'firstName' => 'GLOBAL PTE. LTD.',
                                                                    'lastName' => 'GAMER2GAMER',
                                                                    'personalId' => '201537924K',
                                                                    'personalIdType' => 'Business No',
                                                                    'phoneNumber' => '6531583145',
                                                                    'personalIdCountry' => array(
                                                                        'code' => 'SG'
                                                                    ),
                                                                ),
                                                                'beneficiary' => array(
                                                                    'country' => array(
                                                                        'code' => 'ID'
                                                                    ),
                                                                    'firstName' => $store_payment_data_loop['MODULE_DOKU_ACCOUNT_FNAME'],
                                                                    'lastName' => empty($store_payment_data_loop['MODULE_DOKU_ACCOUNT_LNAME']) ? $store_payment_data_loop['MODULE_DOKU_ACCOUNT_FNAME'] : $store_payment_data_loop['MODULE_DOKU_ACCOUNT_LNAME'],
                                                                    'phoneNumber' => $mobile_number,
                                                                ),
                                                                // "beneficiaryAccount" => array(
                                                                //     "address" => $store_payment_data_loop['MODULE_DOKU_ADDRESS'],
                                                                //     "city" => $store_payment_data_loop['MODULE_DOKU_CITY'],
                                                                //     "name" => $store_payment_data_loop['MODULE_DOKU_ACCOUNT_NAME'],
                                                                // ),
                                                                'beneficiaryWalletId' => $store_payment_data_loop['MODULE_DOKUWALLET_ID'],
                                                            );

                                                            $url = $this->api_url . "/cashin/remit";
                                                            $apiResponse = $this->restapi($url, 'POST', json_encode($data), $header);
                                                            $apiResponseStr .= "<br />Remit: <br />";
                                                            foreach ($apiResponse as $apiResponseKey => $apiResponseValue) {
                                                                switch ($apiResponseKey) {
                                                                    case 'status':
                                                                    case 'message':
                                                                        $apiResponseStr .= $apiResponseKey . ': ' . $apiResponseValue . '</br>';
                                                                        break;
                                                                    default:
                                                                        $apiResponseStr .= $apiResponseKey . ': ' . json_encode($apiResponseValue) . '</br>';
                                                                        break;
                                                                }
                                                            }
                                                            // $apiResponseStr .= json_encode($data);

                                                            if (isset($apiResponse['status']) && $apiResponse['status'] == "0" && isset($apiResponse['remit']['transactionId'])) {
                                                                if (isset($apiResponse['remit']['transactionStatus']) && $apiResponse['remit']['transactionStatus'] == "50") {
                                                                    $payment_status = '3';
                                                                    $store_payments_paid_amount = $store_payment_data_loop['amount'];
                                                                } else if (isset($apiResponse['remit']['transactionStatus']) && $apiResponse['remit']['transactionStatus'] == "20") {
                                                                    # insert cronjob
                                                                    $extra_info_array = array(
                                                                        'portal' => 'doku_wallet',
                                                                        'action' => 'pending_transaction',
                                                                        'transaction_id' => $apiResponse['remit']['transactionId'],
                                                                        'payment_id' => $store_payment_id_loop,
                                                                    );
                                                                    $cron_attr = array(
                                                                        'extra_info' => json_encode($extra_info_array),
                                                                        'type' => 'pending_payment',
                                                                        'flag' => 0,
                                                                        'response' => 0,
                                                                        'created_at' => time(),
                                                                        'updated_at' => time(),
                                                                    );
                                                                    tep_db_perform(TABLE_G2G_CRON_PROCESS_QUEUE, $cron_attr);
                                                                }
                                                                $referrenceId = "Remit Transaction ID: " . $apiResponse['remit']['transactionId'];
                                                            } else if (isset($apiResponse['message'])) {
                                                                $return_result_array[$store_payment_id_loop] = "DOKU Remit API failed: " . $apiResponse['message'];
                                                            } else {
                                                                $return_result_array[$store_payment_id_loop] = "DOKU Remit API failed, please try again";
                                                            }
                                                        // } else {
                                                        //     $apiResponseStr .= "<br /><br /><u>Extra Remarks:</u><br />";
                                                        //     $apiResponseStr .= $return_result_array[$store_payment_id_loop] = "DOKU Wallet Name (" . $client_name . ") does NOT match with Shasso Customer Name (" . $customers_result_row['customers_lastname'] . " " . $customers_result_row['customers_firstname'] . "). \n\nPlease cancel other DOKU wallet payment from the same user.\nSystem will delete this user DOKU wallet disbursement account";
                                                        //     $store_payment_select_sql = "	SELECT store_payment_account_book_id
                                                        //                                     FROM " . TABLE_STORE_PAYMENTS . "
                                                        //                                     WHERE store_payments_id = '" . $store_payment_id_loop . "'";
                                                        //     $store_payment_result_sql = tep_db_query($store_payment_select_sql);
                                                        //     if ($store_payment_result_row = tep_db_fetch_array($store_payment_result_sql)) {
                                                        //         $delete_store_payment_account_book_sql = "	DELETE FROM " . TABLE_STORE_PAYMENT_ACCOUNT_BOOK . "
                                                        //                                                 WHERE store_payment_account_book_id = '" . $store_payment_result_row['store_payment_account_book_id'] . "'";
                                                        //         tep_db_query($delete_store_payment_account_book_sql);
                                                        //         $delete_store_payment_account_book_details_sql = "	DELETE FROM " . TABLE_STORE_PAYMENT_ACCOUNT_BOOK_DETAILS . "
                                                        //                                                 WHERE store_payment_account_book_id = '" . $store_payment_result_row['store_payment_account_book_id'] . "'";
                                                        //         tep_db_query($delete_store_payment_account_book_details_sql);
                                                        //     }
                                                        // }
                                                    } else {
                                                        $return_result_array[$store_payment_id_loop] = "DOKU Inquiry API failed: not able to get DOKU wallet account name";
                                                    }
                                                } else if (isset($apiResponse['message'])) {
                                                    $return_result_array[$store_payment_id_loop] = "DOKU Inquiry API failed: " . $apiResponse['message'];
                                                } else {
                                                    $return_result_array[$store_payment_id_loop] = "DOKU Inquiry API failed, please try again";
                                                }
                                            } else {
                                                $process_proceed = false;
                                                $return_result_array[$store_payment_id_loop] = "Failed to retrieved Shasso customer profile, please try again";
                                            }
                                        } else {
                                            $process_proceed = false;
                                            $return_result_array[$store_payment_id_loop] = "Customer DOKU Wallet ID NOT FOUND";
                                        }
                                        break;
                                    case 'doku_bank':
                                        if (isset($store_payment_data_loop['MODULE_DOKUBANK_ACCOUNT']) && isset($store_payment_data_loop['MODULE_DOKUBANK_CODE'])) {
                                            $customers_select_sql = "	SELECT customers_firstname, customers_lastname, customers_country_dialing_code_id, customers_telephone
                                                                        FROM " . TABLE_CUSTOMERS . "
                                                                        WHERE customers_id = '" . $store_payment_data_loop['user'] . "'";
                                            $customers_result_sql = tep_db_query($customers_select_sql);
                                            if ($customers_result_row = tep_db_fetch_array($customers_result_sql)) {
                                                // $shasso_first_name = strtolower(str_replace(' ', '', $customers_result_row['customers_firstname'] . $customers_result_row['customers_lastname']));
                                                // $shasso_last_name = strtolower(str_replace(' ', '', $customers_result_row['customers_lastname'] . $customers_result_row['customers_firstname']));

                                                $fullBankData = $store_payment_data_loop['MODULE_DOKUBANK_CODE'];
                                                preg_match('#\(\_(.*?)\_\)#', $fullBankData, $matches);
                                                if (count($matches) >= 2) {
                                                    $bankName = trim(str_replace($matches[0], "", $fullBankData));
                                                    $bankDetails = explode("_", $matches[1]);
                                                    if (count($bankDetails) >= 2) {
                                                        $bankId = $bankDetails[0];
                                                        $bankSwiftCode = $bankDetails[1];
                                                    }
                                                }

                                                $signature = openssl_encrypt($this->outgoing_api_username . $store_payment_id_loop, 'AES-128-ECB', $this->outgoing_api_password);
                                                $header = [
                                                    "Content-Type" => "application/json",
                                                    "agentKey" => $this->outgoing_api_username,
                                                    'requestId' => $store_payment_id_loop,
                                                    'signature' => $signature,
                                                ];
                                                //inquiry
                                                $data = array(
                                                    // "agentKey" => $this->outgoing_api_username,
                                                    // 'requestId' => $store_payment_id_loop,
                                                    // 'signature' => $signature,
                                                    'beneficiaryCountry' => array(
                                                        'code' => 'ID'
                                                    ),
                                                    'beneficiaryCurrency' => array(
                                                        'code' => 'IDR'
                                                    ),
                                                    'channel' => array(
                                                        'code' => '07'
                                                    ),
                                                    'senderCountry' => array(
                                                        'code' => 'ID'
                                                    ),
                                                    'senderCurrency' => array(
                                                        'code' => 'IDR'
                                                    ),
                                                    // "beneficiaryAccount" => array(
                                                    //     "address" => $store_payment_data_loop['MODULE_DOKU_ADDRESS'],
                                                    //     "city" => $store_payment_data_loop['MODULE_DOKU_CITY'],
                                                    //     "name" => $store_payment_data_loop['MODULE_DOKU_ACCOUNT_NAME'],
                                                    // ),
                                                    // 'senderNote' => 'Payment ID: ' . $store_payment_id_loop,
                                                    // 'beneficiaryWalletId' => $store_payment_data_loop['MODULE_DOKUWALLET_ID'],
                                                    'senderAmount' => $store_payment_data_loop['amount'],
                                                    'beneficiaryAccount' => array(
                                                        'address' => $store_payment_data_loop['MODULE_DOKUBANK_ACCOUNT'],
                                                        'bank' => array(
                                                            'code' => $bankSwiftCode,
                                                            'countryCode' => 'ID',
                                                            'id' => $bankId,
                                                            'name' => $bankName,
                                                        ),
                                                        'city' => $store_payment_data_loop['MODULE_DOKUBANK_CITY'],
                                                        'name' => $store_payment_data_loop['MODULE_DOKU_ACCOUNT_FNAME'] . " " . $store_payment_data_loop['MODULE_DOKU_ACCOUNT_LNAME'],
                                                        'number' => $store_payment_data_loop['MODULE_DOKUBANK_ACCOUNT'],
                                                    )
                                                );
                                                $url = $this->api_url . "/cashin/inquiry";
                                                $apiResponse = $this->restapi($url, 'POST', json_encode($data), $header);
                                                $apiResponseStr = "Inquiry: <br />";
                                                foreach ($apiResponse as $apiResponseKey => $apiResponseValue) {
                                                    switch ($apiResponseKey) {
                                                        case 'status':
                                                        case 'message':
                                                            $apiResponseStr .= $apiResponseKey . ': ' . $apiResponseValue . '</br>';
                                                            break;
                                                        default:
                                                            $apiResponseStr .= $apiResponseKey . ': ' . json_encode($apiResponseValue) . '</br>';
                                                            break;
                                                    }
                                                }
                                                // $apiResponseStr .= json_encode($data);


                                                if (isset($apiResponse['status']) && $apiResponse['status'] == "0" && isset($apiResponse['inquiry']['idToken'])) {
                                                    $client_name = isset($apiResponse['inquiry']['beneficiaryAccount']['name']) ? $apiResponse['inquiry']['beneficiaryAccount']['name'] : "";
                                                    // $shasso_first_name = "Taufik Ismail"; //dummy
                                                    if ($client_name) {
                                                        //G2G API update disbursement extra info
                                                        $g2gApiUpdate = array(
                                                            'merchant' => G2G_API_OGCREW_MERCHANT,
                                                            'signature' => md5($store_payment_id_loop . "|" . G2G_API_OGCREW_SECRET),
                                                            'tid' => $store_payment_id_loop,
                                                            'payment_code' => $this->outgoing_merchant_code,
                                                            'unique_key' => $fullBankData . "_" . $store_payment_data_loop['MODULE_DOKUBANK_ACCOUNT'],
                                                            'value' => $client_name,
                                                        );
                                                        // $client_name_check = strtolower(str_replace(' ', '', $client_name));
                                                        // if (($shasso_first_name == $client_name_check) || ($shasso_last_name == $client_name_check)) {
                                                            $mobile_number = 0;
                                                            //end get customer mobile number
                                                            $countries_select_sql = "	SELECT countries_international_dialing_code
                                                                                        FROM " . TABLE_COUNTRIES . "
                                                                                        WHERE countries_id = '" . $customers_result_row['customers_country_dialing_code_id'] . "'";
                                                            $ccountries_result_sql = tep_db_query($countries_select_sql);
                                                            if ($countries_result_row = tep_db_fetch_array($ccountries_result_sql)) {
                                                                $mobile_number = $countries_result_row['countries_international_dialing_code'] . $customers_result_row['customers_telephone'];
                                                            }
                                                            //end get customer mobile number

                                                            $referrenceId = "Inquiry ID Token: " . $apiResponse['inquiry']['idToken'];
                                                            //remit
                                                            $data = array(
                                                                // "agentKey" => $this->outgoing_api_username,
                                                                // 'requestId' => $store_payment_id_loop,
                                                                // 'signature' => $signature,
                                                                'inquiry' => array(
                                                                    'idToken' => $apiResponse['inquiry']['idToken']
                                                                ),
                                                                'beneficiaryCountry' => array(
                                                                    'code' => 'ID'
                                                                ),
                                                                'beneficiaryCurrency' => array(
                                                                    'code' => 'IDR'
                                                                ),
                                                                'channel' => array(
                                                                    'code' => '07'
                                                                ),
                                                                'senderCountry' => array(
                                                                    'code' => 'ID'
                                                                ),
                                                                // 'senderNote' => 'Payment ID: ' . $store_payment_id_loop,
                                                                'senderAmount' => $store_payment_data_loop['amount'],
                                                                'senderCurrency' => array(
                                                                    'code' => 'IDR'
                                                                ),
                                                                'sendTrxId' => $store_payment_id_loop,
                                                                'sender' => array(
                                                                    'birthDate' => '2000-10-16',
                                                                    'country' => array(
                                                                        'code' => 'SG'
                                                                    ),
                                                                    'firstName' => 'GLOBAL PTE. LTD.',
                                                                    'lastName' => 'GAMER2GAMER',
                                                                    'personalId' => '201537924K',
                                                                    'personalIdType' => 'Business No',
                                                                    'phoneNumber' => '6531583145',
                                                                    'personalIdCountry' => array(
                                                                        'code' => 'SG'
                                                                    ),
                                                                ),
                                                                'beneficiary' => array(
                                                                    'country' => array(
                                                                        'code' => 'ID'
                                                                    ),
                                                                    'firstName' => $store_payment_data_loop['MODULE_DOKU_ACCOUNT_FNAME'],
                                                                    'lastName' => empty($store_payment_data_loop['MODULE_DOKU_ACCOUNT_LNAME']) ? $store_payment_data_loop['MODULE_DOKU_ACCOUNT_FNAME'] : $store_payment_data_loop['MODULE_DOKU_ACCOUNT_LNAME'],
                                                                    'phoneNumber' => $mobile_number,
                                                                ),
                                                                'beneficiaryAccount' => array(
                                                                    'address' => $store_payment_data_loop['MODULE_DOKUBANK_ACCOUNT'],
                                                                    'bank' => array(
                                                                        'code' => $bankSwiftCode,
                                                                        'countryCode' => 'ID',
                                                                        'id' => $bankId,
                                                                        'name' => $bankName,
                                                                    ),
                                                                    'city' => $store_payment_data_loop['MODULE_DOKUBANK_CITY'],
                                                                    'name' => $store_payment_data_loop['MODULE_DOKU_ACCOUNT_FNAME'] . " " . $store_payment_data_loop['MODULE_DOKU_ACCOUNT_LNAME'],
                                                                    'number' => $store_payment_data_loop['MODULE_DOKUBANK_ACCOUNT'],
                                                                )
                                                                // "beneficiaryAccount" => array(
                                                                //     "address" => $store_payment_data_loop['MODULE_DOKU_ADDRESS'],
                                                                //     "city" => $store_payment_data_loop['MODULE_DOKU_CITY'],
                                                                //     "name" => $store_payment_data_loop['MODULE_DOKU_ACCOUNT_NAME'],
                                                                // ),
                                                                // 'beneficiaryWalletId' => $store_payment_data_loop['MODULE_DOKUWALLET_ID'],
                                                            );

                                                            $url = $this->api_url . "/cashin/remit";
                                                            $apiResponse = $this->restapi($url, 'POST', json_encode($data), $header);
                                                            $apiResponseStr .= "<br />Remit: <br />";
                                                            foreach ($apiResponse as $apiResponseKey => $apiResponseValue) {
                                                                switch ($apiResponseKey) {
                                                                    case 'status':
                                                                    case 'message':
                                                                        $apiResponseStr .= $apiResponseKey . ': ' . $apiResponseValue . '</br>';
                                                                        break;
                                                                    default:
                                                                        $apiResponseStr .= $apiResponseKey . ': ' . json_encode($apiResponseValue) . '</br>';
                                                                        break;
                                                                }
                                                            }
                                                            // $apiResponseStr .= json_encode($data);

                                                            if (isset($apiResponse['status']) && $apiResponse['status'] == "0" && isset($apiResponse['remit']['transactionId'])) {
                                                                if (isset($apiResponse['remit']['transactionStatus']) && $apiResponse['remit']['transactionStatus'] == "50") {
                                                                    $payment_status = '3';
                                                                    $store_payments_paid_amount = $store_payment_data_loop['amount'];
                                                                } else if (isset($apiResponse['remit']['transactionStatus']) && $apiResponse['remit']['transactionStatus'] == "20") {
                                                                    # insert cronjob
                                                                    $extra_info_array = array(
                                                                        'portal' => 'doku_bank',
                                                                        'action' => 'pending_transaction',
                                                                        'transaction_id' => $apiResponse['remit']['transactionId'],
                                                                        'payment_id' => $store_payment_id_loop,
                                                                    );
                                                                    $cron_attr = array(
                                                                        'extra_info' => json_encode($extra_info_array),
                                                                        'type' => 'pending_payment',
                                                                        'flag' => 0,
                                                                        'response' => 0,
                                                                        'created_at' => time(),
                                                                        'updated_at' => time(),
                                                                    );
                                                                    tep_db_perform(TABLE_G2G_CRON_PROCESS_QUEUE, $cron_attr);
                                                                }
                                                                $referrenceId = "Remit Transaction ID: " . $apiResponse['remit']['transactionId'];
                                                            } else if (isset($apiResponse['message'])) {
                                                                $return_result_array[$store_payment_id_loop] = "DOKU Remit API failed: " . $apiResponse['message'];
                                                            } else {
                                                                $return_result_array[$store_payment_id_loop] = "DOKU Remit API failed, please try again";
                                                            }
                                                        // } else {
                                                        //     $apiResponseStr .= "<br /><br /><u>Extra Remarks:</u><br />";
                                                        //     $apiResponseStr .= $return_result_array[$store_payment_id_loop] = "DOKU Bank Name (" . $client_name . ") does NOT match with Shasso Customer Name (" . $customers_result_row['customers_lastname'] . " " . $customers_result_row['customers_firstname'] . "). \n\nPlease cancel other DOKU bank payment from the same user.\nSystem will delete this user DOKU bank disbursement account";
                                                        //     $store_payment_select_sql = "	SELECT store_payment_account_book_id
                                                        //                                     FROM " . TABLE_STORE_PAYMENTS . "
                                                        //                                     WHERE store_payments_id = '" . $store_payment_id_loop . "'";
                                                        //     $store_payment_result_sql = tep_db_query($store_payment_select_sql);
                                                        //     if ($store_payment_result_row = tep_db_fetch_array($store_payment_result_sql)) {
                                                        //         $delete_store_payment_account_book_sql = "	DELETE FROM " . TABLE_STORE_PAYMENT_ACCOUNT_BOOK . "
                                                        //                                                 WHERE store_payment_account_book_id = '" . $store_payment_result_row['store_payment_account_book_id'] . "'";
                                                        //         tep_db_query($delete_store_payment_account_book_sql);
                                                        //         $delete_store_payment_account_book_details_sql = "	DELETE FROM " . TABLE_STORE_PAYMENT_ACCOUNT_BOOK_DETAILS . "
                                                        //                                                 WHERE store_payment_account_book_id = '" . $store_payment_result_row['store_payment_account_book_id'] . "'";
                                                        //         tep_db_query($delete_store_payment_account_book_details_sql);
                                                        //     }
                                                        // }
                                                    } else {
                                                        $return_result_array[$store_payment_id_loop] = "DOKU Inquiry API failed: not able to get DOKU bank account name";
                                                    }
                                                } else if (isset($apiResponse['message'])) {
                                                    $return_result_array[$store_payment_id_loop] = "DOKU Inquiry API failed: " . $apiResponse['message'];
                                                } else {
                                                    $return_result_array[$store_payment_id_loop] = "DOKU Inquiry API failed, please try again";
                                                }
                                            } else {
                                                $process_proceed = false;
                                                $return_result_array[$store_payment_id_loop] = "Failed to retrieved Shasso customer profile, please try again";
                                            }
                                        } else {
                                            $process_proceed = false;
                                            $return_result_array[$store_payment_id_loop] = "Customer DOKU Bank account NOT FOUND";
                                        }
                                        break;
                                    case 'hyperwallet_bank':
                                        $customer_setting_select_sql = "	SELECT `value`
                                                                    FROM c2c_customer_setting
                                                                    WHERE `key` = 'hyperwallet-usr-token' AND customer_id = '" . $store_payment_data_loop['user'] . "'";
                                        $customer_setting_result_sql = tep_db_query($customer_setting_select_sql);
                                        if ($customer_setting_result_row = tep_db_fetch_array($customer_setting_result_sql)) {
                                            if (isset($store_payment_data_loop['MODULE_HW_TRANSFER_METHOD']) && isset($store_payment_data_loop['MODULE_HW_TRANSFER_METHOD'])) {
                                                $customers_select_sql = "	SELECT customers_firstname, customers_lastname, customers_country_dialing_code_id, customers_telephone
                                                                            FROM " . TABLE_CUSTOMERS . "
                                                                            WHERE customers_id = '" . $store_payment_data_loop['user'] . "'";
                                                $customers_result_sql = tep_db_query($customers_select_sql);
                                                if ($customers_result_row = tep_db_fetch_array($customers_result_sql)) {

                                                    //move from fronted
                                                    $usr_token = $customer_setting_result_row['value'];
                                                    $header = [
                                                        "Accept" => "application/json",
                                                        "Content-Type" => "application/json",
                                                    ];

                                                    $url = $this->api_url . "/users/" . $usr_token;
                                                    $user_data = $this->restapi($url, "GET", "", $header, 'body', $this->outgoing_api_password);
                                                    $apiResponseStr = "<br />Hyperwallet User: <br />";
                                                    foreach ($user_data as $apiResponseKey => $apiResponseValue) {
                                                        switch ($apiResponseKey) {
                                                            case 'status':
                                                            case 'verificationStatus':
                                                            case 'clientUserId':
                                                            case 'profileType':
                                                            case 'firstName':
                                                            case 'lastName':
                                                                $apiResponseStr .= $apiResponseKey . ': ' . $apiResponseValue . '</br>';
                                                                break;
                                                            default:
                                                                $apiResponseStr .= $apiResponseKey . ': ' . json_encode($apiResponseValue) . '</br>';
                                                                break;
                                                        }
                                                    }

                                                    if (isset($user_data['token'])) {
                                                        // $m_cust['customers_firstname'] = "fff2222";
                                                        // $shasso_first_name = strtolower(str_replace(' ', '', $customers_result_row['customers_firstname'] . $customers_result_row['customers_lastname']));
                                                        // $shasso_last_name = strtolower(str_replace(' ', '', $customers_result_row['customers_lastname'] . $customers_result_row['customers_firstname']));
                                                        $client_name = strtolower(str_replace(' ', '', $user_data['firstName'] . $user_data['lastName']));
                                                        // if (($shasso_first_name == $client_name) || ($shasso_last_name == $client_name)) {
                                                            switch ($user_data['status']) {
                                                                case "PRE_ACTIVATED":
                                                                    switch ($user_data['verificationStatus']) {
                                                                        case "NOT_REQUIRED":
                                                                        case "VERIFIED":
                                                                            $trm_token = $store_payment_data_loop['MODULE_HW_TRANSFER_METHOD'];
                                                                            $url = $this->api_url . "/users/" . $usr_token . "/transfer-methods/" . $trm_token;
                                                                            $trm_result = $this->restapi($url, "GET", '', $header, 'body', $this->outgoing_api_password);
                                                                            $apiResponseStr .= "<br /><br />Hyperwallet Transfer method: <br />";
                                                                            foreach ($trm_result as $apiResponseKey => $apiResponseValue) {
                                                                                switch ($apiResponseKey) {
                                                                                    case 'token':
                                                                                    case 'type':
                                                                                    case 'status':
                                                                                    case 'verificationStatus':
                                                                                    case 'transferMethodCountry':
                                                                                    case 'transferMethodCurrency':
                                                                                    case 'profileType':
                                                                                    case 'firstName':
                                                                                    case 'lastName':
                                                                                        $apiResponseStr .= $apiResponseKey . ': ' . $apiResponseValue . '</br>';
                                                                                        break;
                                                                                    default:
                                                                                        $apiResponseStr .= $apiResponseKey . ': ' . json_encode($apiResponseValue) . '</br>';
                                                                                        break;
                                                                                }
                                                                            }

                                                                            if (isset($trm_result['token'])) {
                                                                                switch ($trm_result['status']) {
                                                                                    case "ACTIVATED":
                                                                                    case "VERIFIED":
                                                                                        //$this->outgoing_api_merchant 
                                                                                        $url = $this->api_url . "/payments";

                                                                                        $data = array(
                                                                                            "amount" => $store_payment_data_loop['amount'],
                                                                                            "clientPaymentId" => $store_payment_id_loop,
                                                                                            "currency" => $store_payments_currency_loop,
                                                                                            "destinationToken" => $trm_token,
                                                                                            "programToken" => $this->outgoing_api_merchant,
                                                                                            "purpose" => "OTHER"
                                                                                        );
                                                                                        $transfer_result = $this->restapi($url, "POST", json_encode($data), $header, 'body', $this->outgoing_api_password);
                                                                                        $apiResponseStr .= "<br /><br />Hyperwallet payment: <br />";
                                                                                        foreach ($transfer_result as $apiResponseKey => $apiResponseValue) {
                                                                                            switch ($apiResponseKey) {
                                                                                                case 'status':
                                                                                                    $apiResponseStr .= $apiResponseKey . ': ' . $apiResponseValue . '</br>';
                                                                                                    break;
                                                                                                case 'programToken':
                                                                                                    //remove program token from remarks
                                                                                                    break;
                                                                                                default:
                                                                                                    $apiResponseStr .= $apiResponseKey . ': ' . json_encode($apiResponseValue) . '</br>';
                                                                                                    break;
                                                                                            }
                                                                                        }
                                                                                        if (isset($transfer_result['errors'])) {
                                                                                            $return_result_array[$store_payment_id_loop] = "Failed to transfer Hyperwallet payment, please check the error message from payment remarks";
                                                                                        } else if (isset($transfer_result['token']) && isset($transfer_result['status'])) {
                                                                                            $referrenceId = $transfer_result['token'];
                                                                                            switch ($transfer_result['status']) {
                                                                                                case "CREATED":
                                                                                                case "IN_PROGRESS":
                                                                                                    //wait for webhook update to complete status
                                                                                                    break;
                                                                                                case "COMPLETED":
                                                                                                    $payment_status = '3';
                                                                                                    $store_payments_paid_amount = $transfer_result['amount'];
                                                                                                    //move to complete
                                                                                                    break;
                                                                                                case "RETURNED":
                                                                                                    $return_result_array[$store_payment_id_loop] = "Please cancel this paymet\n\nThe payment has been returned to G2G Hyperwallet account by the recipient's institution, typically due to incorrect account details.";
                                                                                                    break;
                                                                                                case "FAILED":
                                                                                                    $return_result_array[$store_payment_id_loop] = "Please cancel this paymet\n\nThe payment has failed, typically due to insufficient funds in the source account.";
                                                                                                    break;
                                                                                                case "EXPIRED":
                                                                                                    $return_result_array[$store_payment_id_loop] = "Please cancel this paymet\n\nThe payment has expired";
                                                                                                    break;
                                                                                                case "CANCELLED":
                                                                                                    $return_result_array[$store_payment_id_loop] = "Please cancel this paymet\n\nThe payment has been cancelled.";
                                                                                                    break;
                                                                                                    // case "RECALLED":
                                                                                                    // case "SCHEDULED":
                                                                                                    // case "PENDING_ACCOUNT_ACTIVATION":
                                                                                                    // case "PENDING_ID_VERIFICATION":
                                                                                                    // case "PENDING_TAX_VERIFICATION":
                                                                                                    // case "PENDING_TRANSFER_METHOD_ACTION":
                                                                                                    // case "PENDING_TRANSACTION_VERIFICATION":
                                                                                                default:
                                                                                                    $return_result_array[$store_payment_id_loop] = "Failed to transfer Hyperwallet payment (" . $transfer_result['status'] . ")";
                                                                                            }
                                                                                        } else {
                                                                                            $return_result_array[$store_payment_id_loop] = "Failed to transfer Hyperwallet payment, please check the error message from payment remarks";
                                                                                        }
                                                                                        break;
                                                                                        // case "INVALID":
                                                                                        // case "DE_ACTIVATED":
                                                                                    default:
                                                                                        //slack out the status $trm_result['status']
                                                                                        $return_result_array[$store_payment_id_loop] = "Hyperwallet transfer method NOT ACTIVE (" . $trm_result['status'] . ")";
                                                                                }
                                                                            } else {
                                                                                $return_result_array[$store_payment_id_loop] = "Failed to retreived Hyperwallet transfer method";
                                                                            }
                                                                            break;
                                                                            // case "REQUIRED":
                                                                            // case "UNDER_REVIEW":
                                                                        default:
                                                                            $return_result_array[$store_payment_id_loop] = "Hyperwallet KYC is under review or required (" . $user_data['verificationStatus'] . "). Please cancel this payment and get customer withdraw again (will go through KYC again during withdrawal)";
                                                                    }

                                                                    break;
                                                                    // case "LOCKED":
                                                                    // case "FROZEN":
                                                                    // case "DE_ACTIVATED":
                                                                default:
                                                                    $return_result_array[$store_payment_id_loop] = "Hyperwallet user NOT ACTIVE (" . $user_data['status'] . ")";
                                                            }
                                                        // } else {
                                                        //     $apiResponseStr .= "<br /><br /><u>Extra Remarks:</u><br />";
                                                        //     $apiResponseStr .= $return_result_array[$store_payment_id_loop] = "Hyperwallet payee name (" . $user_data['firstName'] . " " . $user_data['lastName'] . ") does NOT match with Shasso Customer Name (" . $customers_result_row['customers_lastname'] . " " . $customers_result_row['customers_firstname'] . "). \n\nPlease cancel other Hyperwallet bank payment from the same user.";
                                                            // $apiResponseStr .= $return_result_array[$store_payment_id_loop] = "Hyperwallet payee name (" . $client_name . ") does NOT match with Shasso Customer Name (" . $customers_result_row['customers_lastname'] . " " . $customers_result_row['customers_firstname'] . "). \n\nPlease cancel others Hyperwallet bank payment from the same user.\nSystem will delete this user Hyperwallet bank disbursement account";
                                                            // $store_payment_select_sql = "	SELECT store_payment_account_book_id
                                                            //                                 FROM " . TABLE_STORE_PAYMENTS . "
                                                            //                                 WHERE store_payments_id = '" . $store_payment_id_loop . "'";
                                                            // $store_payment_result_sql = tep_db_query($store_payment_select_sql);
                                                            // if ($store_payment_result_row = tep_db_fetch_array($store_payment_result_sql)) {
                                                            //     $delete_store_payment_account_book_sql = "	DELETE FROM " . TABLE_STORE_PAYMENT_ACCOUNT_BOOK . "
                                                            //                                             WHERE store_payment_account_book_id = '" . $store_payment_result_row['store_payment_account_book_id'] . "'";
                                                            //     tep_db_query($delete_store_payment_account_book_sql);
                                                            //     $delete_store_payment_account_book_details_sql = "	DELETE FROM " . TABLE_STORE_PAYMENT_ACCOUNT_BOOK_DETAILS . "
                                                            //                                             WHERE store_payment_account_book_id = '" . $store_payment_result_row['store_payment_account_book_id'] . "'";
                                                            //     tep_db_query($delete_store_payment_account_book_details_sql);
                                                            // }
                                                        // }
                                                    } else {
                                                        $process_proceed = false;
                                                        $return_result_array[$store_payment_id_loop] = "Failed to retrieved Hyperwallet payee profile, please try again";
                                                    }
                                                    //end
                                                } else {
                                                    $process_proceed = false;
                                                    $return_result_array[$store_payment_id_loop] = "Failed to retrieved Shasso customer profile, please try again";
                                                }
                                            } else {
                                                $process_proceed = false;
                                                $return_result_array[$store_payment_id_loop] = "Hyperwallet transfer method NOT FOUND";
                                            }
                                        } else {
                                            $process_proceed = false;
                                            $return_result_array[$store_payment_id_loop] = "Hyperwallet payee profile NOT FOUND";
                                        }


                                        break;
                                }

                                if ($process_proceed == true) {
                                    $payment_update_sql_data_array = array();
                                    $payment_history_sql_data_array = array();
                                    $payment_history_sql_data_array['store_payments_id'] = tep_db_prepare_input($store_payment_id_loop);
                                    $payment_history_sql_data_array['date_added'] = 'now()';
                                    $payment_history_sql_data_array['payee_notified'] = '0';
                                    $payment_history_sql_data_array['changed_by'] = tep_db_prepare_input($_SESSION['login_email_address']);
                                    $payment_history_sql_data_array['changed_by_role'] = tep_db_prepare_input('admin');
                                    $payment_history_sql_data_array['store_payments_status'] = '0';

                                    if (isset($referrenceId) && !empty($referrenceId)) {
                                        if (!$return_result_array[$store_payment_id_loop]) {
                                            $return_result_array[$store_payment_id_loop] = 1;
                                        }

                                        $payment_update_sql_data_array = array(
                                            'store_payments_reference' => $referrenceId,
                                            'store_payments_last_modified' => 'now()'
                                        );
                                    }

                                    $payment_update_sql_data_array['store_payments_lock'] = 1;
                                    tep_db_perform(TABLE_STORE_PAYMENTS, $payment_update_sql_data_array, 'update', "store_payments_id = '" . $store_payment_id_loop . "'");

                                    $log_message = "";
                                    if (isset($store_payment_data_loop['email'])) {
                                        $log_message .= 'Recipient: ' . $store_payment_data_loop['email'] . '</br>';
                                    }
                                    $log_message .= 'Unique ID: ' . $store_payment_id_loop . '</br>';
                                    $log_message .= 'Amount: ' . number_format($store_payment_data_loop['amount'], 2) . '</br>';
                                    $log_message .= 'Currency: ' . $store_payments_currency_loop . '</br></br>';

                                    $log_message .= '<u>MassPay Response:</u> </br>';
                                    $log_message .= $apiResponseStr;

                                    $payment_history_sql_data_array['comments'] = tep_db_prepare_input($log_message);
                                    tep_db_perform(TABLE_STORE_PAYMENTS_HISTORY, $payment_history_sql_data_array);

                                    if ($payment_status == '3' && isset($store_payments_paid_amount)) {
                                        $payment_update_sql_data_array = array(
                                            'store_payments_status' => 3,
                                            'store_payments_last_modified' => 'now()',
                                            'store_payments_paid_amount' => $store_payments_paid_amount
                                        );
                                        tep_db_perform(TABLE_STORE_PAYMENTS, $payment_update_sql_data_array, 'update', "store_payments_id = '" . $store_payment_id_loop . "'");

                                        $payment_received_by_str = '';

                                        $estimated_payment_receive_time_select_sql = "	SELECT payment_methods_estimated_receive_period 
                                                                                         FROM " . TABLE_PAYMENT_METHODS . "
                                                                                         WHERE payment_methods_id = '" . $payment_methods_id_loop . "'";
                                        $estimated_payment_receive_time_result_sql = tep_db_query($estimated_payment_receive_time_select_sql);

                                        if ($estimated_payment_receive_time_row = tep_db_fetch_array($estimated_payment_receive_time_result_sql)) {
                                            $payment_received_by_timestamp = mktime(date("H"), (int) date("i") + (int) $estimated_payment_receive_time_row['payment_methods_estimated_receive_period'], (int) date("s"), date("m"), date("d"), date("Y"));
                                            $payment_notice_due_timestamp = mktime(date("H") + 24, (int) date("i") + (int) $estimated_payment_receive_time_row['payment_methods_estimated_receive_period'], (int) date("s"), date("m"), date("d"), date("Y")); // Hard code 24 for now..Need global configuration

                                            $payment_received_by_date = date("Y-m-d H:i:s T", $payment_received_by_timestamp);
                                            $payment_notice_due_date = date("Y-m-d H:i:s T", $payment_notice_due_timestamp);

                                            $is_g2g = c2c_invoice::check_g2g_withdraw($store_payment_id_loop);
                                            if ($is_g2g == true) {
                                                $payment_received_by_str = sprintf(TEXT_PAYMENT_ESTIMATE_PAYMENT_RECEIVED_G2G, $payment_received_by_date, $payment_notice_due_date);
                                            } else {
                                                $payment_received_by_str = sprintf(TEXT_PAYMENT_ESTIMATE_PAYMENT_RECEIVED, $payment_received_by_date, $payment_notice_due_date);
                                            }
                                        }

                                        // Insert payment history
                                        $payment_history_sql_data_array = array(
                                            'store_payments_id' => $store_payment_id_loop,
                                            'store_payments_status' => 3,
                                            'date_added' => 'now()',
                                            'payee_notified' => '1',
                                            'comments' => $payment_received_by_str,
                                            'changed_by' => tep_db_prepare_input($_SESSION['login_email_address']),
                                            'changed_by_role' => tep_db_prepare_input('admin')
                                        );
                                        tep_db_perform(TABLE_STORE_PAYMENTS_HISTORY, $payment_history_sql_data_array);
                                        $xmlhttp_payments_object->send_payment_status_email($store_payment_id_loop, $payment_received_by_str);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        return $return_result_array;
    }

    private function restapi($url, $type = "GET", $data = "", $header = null, $return = 'body', $userpwd = '')
    {
        $ch = curl_init($url);
        $opts = array(
            CURLOPT_CONNECTTIMEOUT => 10,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POST => true,
            CURLOPT_HEADER => true,
            CURLOPT_TIMEOUT => 60,
            CURLOPT_SSL_VERIFYPEER => true
        );
        if ($this->connect_via_proxy) {
            curl_setopt($ch, CURLOPT_PROXY, 'my-proxy.offgamers.lan:3128');
        }
        curl_setopt_array($ch, $opts);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $type);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_NOPROGRESS, 0);
        curl_setopt($ch, CURLOPT_VERBOSE, 1);

        // $agent = "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.0)";
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 2);
        curl_setopt($ch, CURLOPT_TIMEOUT, 120);
        // curl_setopt($ch, CURLOPT_USERAGENT, $agent);
        curl_setopt($ch, CURLOPT_HEADER, 0);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        //        curl_setopt($ch, CURLOPT_HEADER, true);

        if ($userpwd) {
            curl_setopt($ch, CURLOPT_USERPWD, $userpwd);
        }

        if ($header) {
            $httpHeader = [];
            foreach ($header as $key => $value) {
                $httpHeader[] = $key . ': ' . $value;
            }
            if (!empty($httpHeader)) {
                curl_setopt($ch, CURLOPT_HTTPHEADER, $httpHeader);
            }
        }

        $result = curl_exec($ch);

        if ($return == 'header') {
            return curl_getinfo($ch, CURLINFO_HTTP_CODE);
        }

        if ($this->isJson($result)) {
            return json_decode($result, true);
        } else {
            return $result;
        }
    }

    private function isJson($params)
    {
        return (@json_decode($params) !== NULL) ? true : false;
    }

    private function _getApiUrl($code)
    {
        switch ($code) {
            case 'payoneer':
            case 'payoneer_gbt':
                if ($this->test_mode) {
                    $this->api_url = 'https://api.sandbox.payoneer.com/v2/programs';
                } else {
                    $this->api_url = 'https://api.payoneer.com/v2/programs';
                }
                break;
            case 'doku_wallet':
            case 'doku_bank':
                if ($this->test_mode) {
                    $this->api_url = 'https://staging.doku.com/apikirimdoku';
                } else {
                    $this->api_url = 'https://www.kirimdoku.com/v2/api';
                }
                break;
            case 'hyperwallet_bank':
                if ($this->test_mode) {
                    $this->api_url = 'https://uat-api.paylution.com/rest/v3';
                } else {
                    $this->api_url = 'https://api.paylution.com/rest/v3';
                }
                break;
        }
    }
}
