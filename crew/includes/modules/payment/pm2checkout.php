<?

/*
  $Id: pm2checkout.php,v 1.27 2015/02/16 07:43:45 chingyen Exp $

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2003 osCommerce

  Released under the GNU General Public License
 */

class pm2checkout {

    var $code, $title, $description, $enabled;

    // class constructor
    function pm2checkout($pm_id = '') {
        global $order, $currency, $customer_country_id, $languages_id;

        //Basic info
        $this->payment_methods_id = 0;
        $this->payment_methods_parent_id = 0;

        $this->code = 'pm2checkout';
        $this->filename = 'pm2checkout.php';

        $this->title = $this->code;

        $this->form_action_url = 'https://www.2checkout.com/cgi-bin/Abuyers/purchase.2c';
        $this->order_processing_status = 7;
        $this->auto_cancel_period = 60; // In minutes

        $payment_methods_select_sql = "	SELECT pm.payment_methods_parent_id, pm.payment_methods_code, 
												pmd.payment_methods_description_title, 
												pm.payment_methods_receive_status_mode, pm.payment_methods_id, 
												pm.payment_methods_title, pm.payment_methods_sort_order, 
												pm.payment_methods_receive_status
										FROM " . TABLE_PAYMENT_METHODS . " as pm
										LEFT JOIN " . TABLE_PAYMENT_METHODS_DESCRIPTION . " as pmd
											ON ( pmd.payment_methods_id = pm.payment_methods_id AND pmd.languages_id = '" . (int) $languages_id . "' )
										WHERE 1 ";
        if ((int) $pm_id > 0) {
            $payment_methods_select_sql .= "	AND pm.payment_methods_id = '" . (int) $pm_id . "'";
        } else {
            $payment_methods_select_sql .= "	AND pm.payment_methods_filename = '" . tep_db_input($this->filename) . "'";
        }

        $payment_methods_result_sql = tep_db_query($payment_methods_select_sql);

        if ($payment_methods_row = tep_db_fetch_array($payment_methods_result_sql)) {
            $this->payment_methods_id = (int) $payment_methods_row['payment_methods_id'];
            $this->payment_methods_parent_id = (int) $payment_methods_row['payment_methods_parent_id'];
            $this->code = $payment_methods_row['payment_methods_code'];
            $this->title = $payment_methods_row['payment_methods_title']; //MODULE_PAYMENT_PAYPAL_TEXT_TITLE;
            $this->display_title = $payment_methods_row['payment_methods_description_title']; //MODULE_PAYMENT_PAYPAL_TEXT_DISPLAY_TITLE; 	// if title to be display for payment method is different from payment method value
            $this->description = $this->display_title;
            $this->sort_order = $payment_methods_row['payment_methods_sort_order']; //MODULE_PAYMENT_PAYPAL_SORT_ORDER;
            $this->enabled = $payment_methods_row['payment_methods_receive_status_mode'];
            $this->legend_display_colour = (isset($payment_methods_row['payment_methods_legend_color']) ? $payment_methods_row['payment_methods_legend_color'] : '');
            $this->receive_status = $payment_methods_row['payment_methods_receive_status'];
            $this->description = $this->display_title;

            $configuration_setting_array = $this->load_pm_setting();
            $this->testmode = $configuration_setting_array['MODULE_PAYMENT_2CHECKOUT_TESTMODE'];
            $this->email_merchant = $configuration_setting_array['MODULE_PAYMENT_2CHECKOUT_EMAIL_MERCHANT'];
            //$this->zone = $configuration_setting_array['MODULE_PAYMENT_2CHECKOUT_ZONE'];
            $this->order_status_id = $configuration_setting_array['MODULE_PAYMENT_2CHECKOUT_ORDER_STATUS_ID'];
            $this->check_hash = (($configuration_setting_array['MODULE_PAYMENT_2CHECKOUT_CHECK_HASH'] == 'True') ? true : false);
            $this->card_owner = $configuration_setting_array['MODULE_PAYMENT_2CHECKOUT_CARD_OWNER'];
            $this->billing_address = $configuration_setting_array['MODULE_PAYMENT_2CHECKOUT_BILLING_ADDRESS'];
            $this->confirm_complete_days = $configuration_setting_array['MODULE_PAYMENT_2CHECKOUT_CONFIRM_COMPLETE'];
            $this->message = $configuration_setting_array['MODULE_PAYMENT_2CHECKOUT_MESSAGE'];
            $this->require_address_information = $configuration_setting_array['MODULE_PAYMENT_2CHECKOUT_MANDATORY_ADDRESS_FIELD'];
            $this->require_ic_information = $configuration_setting_array['MODULE_PAYMENT_2CHECKOUT_MANDATORY_IC_FIELD'];
            $this->require_contact_information = $configuration_setting_array['MODULE_PAYMENT_2CHECKOUT_MANDATORY_CONTACT_FIELD'];

            $this->customer_payment_info = $configuration_setting_array['MODULE_PAYMENT_2CHECKOUT_CUSTOMER_PAYMENT_INFO'];

            if ((int) $this->order_status_id > 0) {
                $this->order_status = $this->order_status_id;
            }
            $this->force_to_checkoutprocess = true;
            //if (is_object($order)) $this->update_status();
        }
    }

    /* // class methods
      function update_status()
      {
      global $order;

      if ( ($this->enabled == true) && ((int)MODULE_PAYMENT_2CHECKOUT_ZONE > 0) )
      {
      $check_flag = false;
      $check_query = tep_db_query("select zone_id from " . TABLE_ZONES_TO_GEO_ZONES . " where geo_zone_id = '" . MODULE_PAYMENT_2CHECKOUT_ZONE . "' and zone_country_id = '" . $order->billing['country']['id'] . "' order by zone_id");
      while ($check = tep_db_fetch_array($check_query)) {
      if ($check['zone_id'] < 1) {
      $check_flag = true;
      break;
      } elseif ($check['zone_id'] == $order->billing['zone_id']) {
      $check_flag = true;
      break;
      }
      }

      if ($check_flag == false)	$this->enabled = false;
      }
      } */

    function javascript_validation() {
        return false;
    }

    function selection() {
        global $order, $pm_2CO_cc_owner;

        $selection = array('id' => $this->code,
            'module' => $this->title,
            'display_module' => $this->display_title,
            'fields' => array()
        );

        if (tep_not_null($pm_2CO_cc_owner["firstname"]) && tep_not_null($pm_2CO_cc_owner["lastname"])) {
            $firstname = $pm_2CO_cc_owner["firstname"];
            $lastname = $pm_2CO_cc_owner["lastname"];
        } else {
            $firstname = $order->billing["firstname"];
            $lastname = $order->billing["lastname"];
        }

        if ($this->card_owner == "True") {
            $selection[fields] = array(array('title' => "<span class='inputLabel'>" . MODULE_PAYMENT_2CHECKOUT_TEXT_CREDIT_CARD_OWNER_FIRST_NAME . "</span>",
                    'field' => tep_draw_input_field('pm_2checkout_cc_owner_firstname', $firstname)),
                array('title' => "<span class='inputLabel'>" . MODULE_PAYMENT_2CHECKOUT_TEXT_CREDIT_CARD_OWNER_LAST_NAME . "</span>",
                    'field' => tep_draw_input_field('pm_2checkout_cc_owner_lastname', $lastname))
            );
        }

        if ($this->billing_address == "True") {
            $selection[fields][] = array('title' => "MOD_SHOW_BILLING_ADDRESS", 'field' => 1);
        }
        return $selection;
    }

    function pre_confirmation_check() {
        return false;
    }

    function confirmation() {
        global $HTTP_POST_VARS, $order, $pm_2CO_cc_owner;

        if (tep_session_is_registered('pm_2CO_cc_owner')) {
            $pm_2CO_cc_owner["firstname"] = $HTTP_POST_VARS['pm_2checkout_cc_owner_firstname'];
            $pm_2CO_cc_owner["lastname"] = $HTTP_POST_VARS['pm_2checkout_cc_owner_lastname'];
        }

        if (tep_not_null($pm_2CO_cc_owner["firstname"]) && tep_not_null($pm_2CO_cc_owner["lastname"])) {
            $firstname = $pm_2CO_cc_owner["firstname"];
            $lastname = $pm_2CO_cc_owner["lastname"];
        } else {
            $firstname = $order->billing["firstname"];
            $lastname = $order->billing["lastname"];
            $pm_2CO_cc_owner["firstname"] = $firstname;
            $pm_2CO_cc_owner["lastname"] = $lastname;
        }
        /*
          $confirmation = array(	'title' => $this->title . ': ' . $this->cc_card_type,
          'fields' => array(array('title' => MODULE_PAYMENT_2CHECKOUT_TEXT_CREDIT_CARD_OWNER,
          'field' => $HTTP_POST_VARS['pm_2checkout_cc_owner_firstname'] . ' ' . $HTTP_POST_VARS['pm_2checkout_cc_owner_lastname']),
          array('title' => MODULE_PAYMENT_2CHECKOUT_TEXT_CREDIT_CARD_NUMBER,
          'field' => substr($this->cc_card_number, 0, 4) . str_repeat('X', (strlen($this->cc_card_number) - 8)) . substr($this->cc_card_number, -4)),
          array('title' => MODULE_PAYMENT_2CHECKOUT_TEXT_CREDIT_CARD_EXPIRES,
          'field' => strftime('%B, %Y', mktime(0,0,0,$HTTP_POST_VARS['pm_2checkout_cc_expires_month'], 1, '20' . $HTTP_POST_VARS['pm_2checkout_cc_expires_year'])))));

          $confirmation = array(	'title' => $this->title . ': ' . $this->cc_card_type,
          'fields' => array(array('title' => MODULE_PAYMENT_2CHECKOUT_TEXT_CREDIT_CARD_OWNER,
          'field' => $HTTP_POST_VARS['pm_2checkout_cc_owner_firstname'] . ' ' . $HTTP_POST_VARS['pm_2checkout_cc_owner_lastname']),
          array('title' => MODULE_PAYMENT_2CHECKOUT_TEXT_CONFIRMATION,
          'field' => '')));
          return $confirmation;
         */
        return array('title' => MODULE_PAYMENT_2CHECKOUT_TEXT_CREDIT_CARD_OWNER . ' <b>' .
            $firstname . ' ' . $lastname . '</b><br><br>' .
            MODULE_PAYMENT_2CHECKOUT_TEXT_CONFIRMATION);
    }

    function process_button() {
        global $HTTP_POST_VARS, $order, $currency, $currencies, $order_logged, $pm_2CO_cc_owner;

        // added by wei chen
        if (!tep_session_is_registered('order_logged'))
            return;
        // end added by wei chen

        if (DEFAULT_CURRENCY <> 'USD')
            $cOrderTotal = $currencies->get_value("USD") * $order->info['total'];
        else
            $cOrderTotal = $order->info['total'];

        if (tep_not_null($pm_2CO_cc_owner["firstname"]) && tep_not_null($pm_2CO_cc_owner["lastname"])) {
            $firstname = $pm_2CO_cc_owner["firstname"];
            $lastname = $pm_2CO_cc_owner["lastname"];
        } else {
            $firstname = $order->billing["firstname"];
            $lastname = $order->billing["lastname"];
        }
        $this->get_merchant_account($currency);
        $process_button_string = tep_draw_hidden_field('x_login', $this->login_id) .
                tep_draw_hidden_field('x_amount', number_format($cOrderTotal, 2)) .
                tep_draw_hidden_field('x_invoice_num', date('YmdHis')) .
                tep_draw_hidden_field('x_order_id', $order_logged) .
                tep_draw_hidden_field('x_test_request', (($this->testmode == 'Test') ? 'Y' : 'N')) .
                tep_draw_hidden_field('x_first_name', $firstname) .
                tep_draw_hidden_field('x_last_name', $lastname) .
                tep_draw_hidden_field('x_address', $order->billing['street_address']) .
                tep_draw_hidden_field('x_city', $order->billing['city']) .
                tep_draw_hidden_field('x_state', $order->billing['state']) .
                tep_draw_hidden_field('x_zip', $order->billing['postcode']) .
                tep_draw_hidden_field('x_country', $order->billing['country']['title']) .
                tep_draw_hidden_field('x_email', $order->customer['email_address']) .
                tep_draw_hidden_field('x_phone', $order->customer['telephone']) .
                tep_draw_hidden_field('x_ship_to_first_name', $order->delivery['firstname']) .
                tep_draw_hidden_field('x_ship_to_last_name', $order->delivery['lastname']) .
                tep_draw_hidden_field('x_ship_to_address', $order->delivery['street_address']) .
                tep_draw_hidden_field('x_ship_to_city', $order->delivery['city']) .
                tep_draw_hidden_field('x_ship_to_state', $order->delivery['state']) .
                tep_draw_hidden_field('x_ship_to_zip', $order->delivery['postcode']) .
                tep_draw_hidden_field('x_ship_to_country', $order->delivery['country']['title']) .
                tep_draw_hidden_field('x_receipt_link_url', tep_href_link(FILENAME_CHECKOUT_PROCESS, '', 'SSL')) .
                tep_draw_hidden_field('x_email_merchant', (($this->email_merchant == 'True') ? 'TRUE' : 'FALSE'));

        if (tep_session_is_registered('pm_2CO_cc_owner'))
            tep_session_unregister('pm_2CO_cc_owner');
        return $process_button_string;
    }

    function before_process() {
        global $HTTP_POST_VARS, $currency;
        // added by wei chen
        if (!tep_session_is_registered('order_logged'))
            return;
        // end added by wei chen

        if ($HTTP_POST_VARS['x_response_code'] != '1') {
            tep_redirect(tep_href_link(FILENAME_CHECKOUT_PAYMENT, 'error_message=' . urlencode(MODULE_PAYMENT_2CHECKOUT_TEXT_ERROR_MESSAGE), 'SSL', true, false));
        }
        $this->get_merchant_account($currency);
        // check the md4 hash
        if ($this->check_hash == true) {
            $compare_string = $this->secret_word . $this->login_id . $HTTP_POST_VARS['x_trans_id'] . $HTTP_POST_VARS['x_amount'];
            // make it md5
            $compare_hash1 = md5($compare_string);
            // make all upper
            $compare_hash1 = strtoupper($compare_hash1);
            $compare_hash2 = $HTTP_POST_VARS['x_MD5_Hash'];
            if ($compare_hash1 != $compare_hash2) {
                tep_redirect(tep_href_link(FILENAME_CHECKOUT_PAYMENT, 'error_message=' . urlencode(MODULE_PAYMENT_2CHECKOUT_TEXT_ERROR_HASH_MESSAGE), 'SSL', true, false));
            }
        }
    }

    function after_process() {
        return false;
    }

    function link_to_payment($new_order_id) {
        global $order_logged;

        if (tep_session_is_registered('order_logged'))
            tep_session_unregister('order_logged');

        tep_session_register('order_logged');
        $order_logged = $new_order_id;
        require(DIR_WS_INCLUDES . 'modules/payment/pm2checkout/catalog/pm2checkout_splash.php');
        return;
    }

    function get_error() {
        global $HTTP_GET_VARS;

        $error = array('title' => MODULE_PAYMENT_2CHECKOUT_TEXT_ERROR,
            'error' => stripslashes(urldecode($HTTP_GET_VARS['error'])));

        return $error;
    }

    function check() {
        if (!isset($this->_check)) {
            $check_query = tep_db_query("select configuration_value from " . TABLE_CONFIGURATION . " where configuration_key = 'MODULE_PAYMENT_2CHECKOUT_STATUS'");
            $this->_check = tep_db_num_rows($check_query);
        }
        return $this->_check;
    }

    function load_pm_setting($language_id = 1) {
        $pm_setting_array = array();
        //load value from DB
        $payment_configuration_info_select_sql = "	SELECT pci.payment_configuration_info_key, pcid.payment_configuration_info_value
													FROM " . TABLE_PAYMENT_CONFIGURATION_INFO . " as pci
													LEFT JOIN " . TABLE_PAYMENT_CONFIGURATION_INFO_DESCRIPTION . " as pcid 
														ON pci.payment_configuration_info_id = pcid.payment_configuration_info_id
															AND pcid.languages_id = '" . (int) $language_id . "'
													WHERE pci.payment_methods_id = '" . (int) $this->payment_methods_id . "'
													ORDER BY pci.payment_configuration_info_sort_order";
        $payment_configuration_info_result_sql = tep_db_query($payment_configuration_info_select_sql);
        if (tep_db_num_rows($payment_configuration_info_result_sql)) {
            while ($payment_configuration_info_row = tep_db_fetch_array($payment_configuration_info_result_sql)) {
                $pm_setting_array[$payment_configuration_info_row['payment_configuration_info_key']] = $payment_configuration_info_row['payment_configuration_info_value'];
            }
        } else if ((int) $this->payment_methods_parent_id > 0) { //load value from PG
            $payment_configuration_info_select_sql = "	SELECT pci.payment_configuration_info_key, pcid.payment_configuration_info_value
														FROM " . TABLE_PAYMENT_CONFIGURATION_INFO . " as pci
														LEFT JOIN " . TABLE_PAYMENT_CONFIGURATION_INFO_DESCRIPTION . " as pcid 
															ON pci.payment_configuration_info_id = pcid.payment_configuration_info_id
																AND pcid.languages_id = '" . (int) $language_id . "'
														WHERE pci.payment_methods_id = '" . (int) $this->payment_methods_parent_id . "'
														ORDER BY pci.payment_configuration_info_sort_order";
            $payment_configuration_info_result_sql = tep_db_query($payment_configuration_info_select_sql);
            while ($payment_configuration_info_row = tep_db_fetch_array($payment_configuration_info_result_sql)) {
                $pm_setting_array[$payment_configuration_info_row['payment_configuration_info_key']] = $payment_configuration_info_row['payment_configuration_info_value'];
            }
        }
        return $pm_setting_array;
    }

    function get_merchant_account($selected_currency) {
        $payment_methods_instance_id = 0;
        $payment_gateway_instance_select_sql = "SELECT pmi.payment_methods_instance_id, pmi.payment_methods_instance_follow_default, pmi.currency_code 
				 								FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " as pmi
				 								WHERE pmi.payment_methods_id = '" . (int) $this->payment_methods_id . "'
				 								LIMIT 1";
        $payment_gateway_instance_result_sql = tep_db_query($payment_gateway_instance_select_sql);
        if (tep_db_num_rows($payment_gateway_instance_result_sql)) {
            $payment_gateway_instance_select_sql = "SELECT pmi.payment_methods_instance_id, pmi.payment_methods_instance_follow_default 
					 								FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " as pmi
					 								WHERE pmi.currency_code = '" . tep_db_input($selected_currency) . "' 
					 									AND pmi.payment_methods_id = '" . (int) $this->payment_methods_id . "'";
            $payment_gateway_instance_result_sql = tep_db_query($payment_gateway_instance_select_sql);
            if ($payment_gateway_instance_row = tep_db_fetch_array($payment_gateway_instance_result_sql)) {
                if ((int) $payment_gateway_instance_row['payment_methods_instance_follow_default'] > 0) { // Follow Default Currency Setting
                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_follow_default'];
                } else { // Has own setting
                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_id'];
                }
            }
        } else {
            $payment_gateway_instance_select_sql = "SELECT pmi.payment_methods_instance_id, pmi.payment_methods_instance_follow_default 
					 								FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " as pmi
					 								INNER JOIN " . TABLE_PAYMENT_METHODS . " as pm
					 									ON pm.payment_methods_parent_id = pmi.payment_methods_id
					 								WHERE pmi.currency_code = '" . tep_db_input($selected_currency) . "' 
					 									AND pm.payment_methods_id = '" . (int) $this->payment_methods_id . "'";
            $payment_gateway_instance_result_sql = tep_db_query($payment_gateway_instance_select_sql);
            if ($payment_gateway_instance_row = tep_db_fetch_array($payment_gateway_instance_result_sql)) {
                if ((int) $payment_gateway_instance_row['payment_methods_instance_follow_default'] > 0) {
                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_follow_default'];
                } else {
                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_id'];
                }
            }
        }
        $payment_gateway_instance_setting_select_sql = "SELECT pmis.payment_methods_instance_setting_key, pmis.payment_methods_instance_setting_value 
						 								FROM " . TABLE_PAYMENT_METHODS_INSTANCE_SETTING . " as pmis
						 								WHERE pmis.payment_methods_instance_id = '" . (int) $payment_methods_instance_id . "'";
        $payment_gateway_instance_setting_result_sql = tep_db_query($payment_gateway_instance_setting_select_sql);
        while ($payment_gateway_instance_setting_row = tep_db_fetch_array($payment_gateway_instance_setting_result_sql)) {
            switch ($payment_gateway_instance_setting_row['payment_methods_instance_setting_key']) {
                case 'MODULE_PAYMENT_2CHECKOUT_LOGIN':
                    $this->login_id = $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];
                    break;
                case 'MODULE_PAYMENT_2CHECKOUT_SECRET_WORD':
                    $this->secret_word = $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];
                    break;
            }
        }
    }

    function install() {
        $install_configuration_flag = true;

        if ($this->payment_methods_id > 0) {
            if ($this->receive_status != 1) {
                $update_receive_status_sql_data = array("payment_methods_receive_status" => 1);
                tep_db_perform(TABLE_PAYMENT_METHODS, $update_receive_status_sql_data, 'update', "payment_methods_id = '" . (int) $this->payment_methods_id . "'");
            } else {
                $install_configuration_flag = false;
            }
        } else {
            $install_payment_gateway_data_sql = array('payment_methods_code' => $this->code,
                'payment_methods_send_status' => 0,
                'payment_methods_title' => $this->code,
                'payment_methods_types_id' => 0,
                'payment_methods_parent_id' => 0,
                'payment_methods_legend_color' => '#66FFCC',
                'payment_methods_sort_order' => 3,
                'payment_methods_filename' => $this->filename,
                'payment_methods_logo' => '',
                'date_added' => 'now()',
                'last_modified' => 'now()',
                'payment_methods_receive_status' => '1',
                'payment_methods_receive_status_mode' => 0
            );
            tep_db_perform(TABLE_PAYMENT_METHODS, $install_payment_gateway_data_sql);
            $this->payment_methods_id = tep_db_insert_id();
        }

        if ($install_configuration_flag) {
            $install_key_array = array();
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Transaction Mode',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_2CHECKOUT_TESTMODE',
                    'payment_configuration_info_description' => 'Transaction mode used for the 2Checkout service',
                    'payment_configuration_info_sort_order' => '3',
                    'set_function' => 'tep_cfg_select_option(array(\'Test\', \'Production\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'Test',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Merchant Notifications',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_2CHECKOUT_EMAIL_MERCHANT',
                    'payment_configuration_info_description' => 'Should 2CheckOut e-mail a receipt to the store owner?',
                    'payment_configuration_info_sort_order' => '4',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'True',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Set Order Status',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_2CHECKOUT_ORDER_STATUS_ID',
                    'payment_configuration_info_description' => 'Set the status of orders made with this payment module to this value',
                    'payment_configuration_info_sort_order' => '7',
                    'set_function' => 'tep_cfg_pull_down_order_statuses(',
                    'use_function' => 'tep_get_order_status_name',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => '0',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Check MD5 hash',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_2CHECKOUT_CHECK_HASH',
                    'payment_configuration_info_description' => 'Should the 2CheckOut MD5 hash facilty to be checked?',
                    'payment_configuration_info_sort_order' => '8',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => 'tep_get_order_status_name',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Payment Message',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_2CHECKOUT_MESSAGE',
                    'payment_configuration_info_description' => 'Payment message will show up during checkout process (2CheckOut)',
                    'payment_configuration_info_sort_order' => '13',
                    'set_function' => 'tep_cfg_textarea(',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'Thank you for shopping at SKC Store.',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Credit card owner',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_2CHECKOUT_CARD_OWNER',
                    'payment_configuration_info_description' => 'Should the credit card owner name to be changed at checkout payment?',
                    'payment_configuration_info_sort_order' => '10',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'True',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Credit card billing address',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_2CHECKOUT_BILLING_ADDRESS',
                    'payment_configuration_info_description' => 'Should the credit card billing address to be changed at checkout payment?',
                    'payment_configuration_info_sort_order' => '11',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'True',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Confirm Complete (in days)',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_2CHECKOUT_CONFIRM_COMPLETE',
                    'payment_configuration_info_description' => 'The number of days in which an order made by this payment method is confirm completed',
                    'payment_configuration_info_sort_order' => '210',
                    'set_function' => '',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => '0',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Require Address Information',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_2CHECKOUT_MANDATORY_ADDRESS_FIELD',
                    'payment_configuration_info_description' => 'Set address field as required info during checkout.',
                    'payment_configuration_info_sort_order' => '2000',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Customer Payment Info',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_2CHECKOUT_CUSTOMER_PAYMENT_INFO',
                    'payment_configuration_info_description' => 'Set to true if this Payment Method required customer input for Payment Information required customer payment info.',
                    'payment_configuration_info_sort_order' => '1230',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );
            foreach ($install_key_array as $data) {
                tep_db_perform(TABLE_PAYMENT_CONFIGURATION_INFO, $data['info']);
                $payment_conf_id = tep_db_insert_id();

                $data['desc']['payment_configuration_info_id'] = (int) $payment_conf_id;
                tep_db_perform(TABLE_PAYMENT_CONFIGURATION_INFO_DESCRIPTION, $data['desc']);
            }
        }
        return 1;
    }

    function get_pg_id() {
        return $this->payment_methods_id;
    }

    function remove() {
        tep_db_query("delete from " . TABLE_CONFIGURATION . " where configuration_key in ('" . implode("', '", $this->keys()) . "')");
    }

    function keys() {
        return array('MODULE_PAYMENT_2CHECKOUT_STATUS',
            'MODULE_PAYMENT_2CHECKOUT_LOGIN',
            'MODULE_PAYMENT_2CHECKOUT_TESTMODE',
            'MODULE_PAYMENT_2CHECKOUT_EMAIL_MERCHANT',
            //'MODULE_PAYMENT_2CHECKOUT_ZONE', 
            'MODULE_PAYMENT_2CHECKOUT_ORDER_STATUS_ID',
            'MODULE_PAYMENT_2CHECKOUT_SORT_ORDER',
            'MODULE_PAYMENT_2CHECKOUT_CHECK_HASH',
            'MODULE_PAYMENT_2CHECKOUT_SECRET_WORD',
            'MODULE_PAYMENT_2CHECKOUT_CARD_OWNER',
            'MODULE_PAYMENT_2CHECKOUT_BILLING_ADDRESS',
            'MODULE_PAYMENT_2CHECKOUT_CONFIRM_COMPLETE',
            'MODULE_PAYMENT_2CHECKOUT_MESSAGE',
            'MODULE_PAYMENT_2CHECKOUT_LEGEND_COLOUR');
    }

    function configuration_information_keys() {
        return array('MODULE_PAYMENT_2CHECKOUT_TESTMODE',
            'MODULE_PAYMENT_2CHECKOUT_EMAIL_MERCHANT',
            'MODULE_PAYMENT_2CHECKOUT_ZONE',
            'MODULE_PAYMENT_2CHECKOUT_ORDER_STATUS_ID',
            'MODULE_PAYMENT_2CHECKOUT_CHECK_HASH',
            'MODULE_PAYMENT_2CHECKOUT_CARD_OWNER',
            'MODULE_PAYMENT_2CHECKOUT_BILLING_ADDRESS',
            'MODULE_PAYMENT_2CHECKOUT_CONFIRM_COMPLETE',
            'MODULE_PAYMENT_2CHECKOUT_MESSAGE',
            'MODULE_PAYMENT_2CHECKOUT_MANDATORY_ADDRESS_FIELD',
            'MODULE_PAYMENT_2CHECKOUT_CUSTOMER_PAYMENT_INFO');
    }

    function multi_lang_configuration_info_keys() {
        return array('MODULE_PAYMENT_2CHECKOUT_MESSAGE');
    }

    function merchant_information_keys() {
        include_once(DIR_WS_CLASSES . "payment_methods.php");

        return array(
            'MODULE_PAYMENT_2CHECKOUT_LOGIN' => array("field" => 'text', "mapping" => "MODULE_PAYMENT_2CHECKOUT_LNG_LOGIN"),
            'MODULE_PAYMENT_2CHECKOUT_SECRET_WORD' => array("field" => 'text', "mapping" => "MODULE_PAYMENT_2CHECKOUT_LNG_SECRET_WORD"),
            'MODULE_PAYMENT_TAX' => array("field" => 'select', 'mapping' => "MODULE_PAYMENT_TAX", "select" => payment_methods::get_tax_option())
        );
    }

    function get_orders_payment_info_file() {
        return DIR_FS_CATALOG_MODULES . 'payment/pm2checkout/admin/orders.inc.php';
    }

    function get_orders_list_payment_info_file() {
        return DIR_FS_CATALOG_MODULES . 'payment/pm2checkout/admin/orders_list.inc.php';
    }

    function get_ipn_class_file() {
        return '';
    }

}

?>