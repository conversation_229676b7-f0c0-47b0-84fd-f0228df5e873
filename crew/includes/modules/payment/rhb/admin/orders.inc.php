﻿<?
/*
  	$Id: orders.inc.php,v 1.1 2010/06/25 09:42:39 boonhock Exp $
	
  	Developer: Ho <PERSON>
  	Copyright (c) 2005 SKC Venture
  	
  	Released under the GNU General Public License
*/

$rhb_trans_info_select_sql = "	SELECT * 
								FROM " . TABLE_RHB . "
								WHERE rhb_orders_id = '" . (int)$oID  . "'";
$rhb_trans_info_result_sql = tep_db_query($rhb_trans_info_select_sql);

$rhb_trans_history_select_sql = "	SELECT * 
									FROM " . TABLE_RHB_PAYMENT_STATUS_HISTORY . " 
									WHERE rhb_orders_id = '" . (int)$oID . "'
									ORDER BY rhb_payment_status_history_id";
$rhb_trans_history_result_sql= tep_db_query($rhb_trans_history_select_sql);

include_once(DIR_FS_CATALOG_MODULES . 'payment/rhb/admin/languages/'.$language.'/rhb.lng.php');

?>
<table border="0" width="100%" cellspacing="0" cellpadding="2">
<? ob_start(); 

?>
	<tr>
		<td class="main" colspan="3">
      		<table border="0" width="100%" cellspacing="0" cellpadding="2">
      			<tr>
      				<td class="main" width="12%" nowrap><b><?=ENTRY_PAYMENT_METHOD?></b></td>
      				<td class="main">
      					<div>
      						<? if((int)$order->info['payment_methods_id']>0) { ?><div class="<?=((int)$payment_info_array[$order->info['payment_methods_id']]->confirm_complete_days>0?'rp_icon':'nrp_icon')?>"><b><?=((int)$payment_info_array[$order->info['payment_methods_id']]->confirm_complete_days>0?'RP':'NRP')?></b></div> <? } ?>
      						<div style="width:30px; height:15px; background-color:<?=$payment_module_info['display_colour']?>; float:left; vertical-align:top; text-align:center;"></div>
      						<div style="vertical-align: top">
      							&nbsp;&nbsp;
<?
	if ($view_payment_details_permission) {
		if (tep_not_null($last_payment_method) && $last_payment_method['payment_methods_id'] != $order->info['payment_methods_id']) {
			echo '<span class="redIndicator">'.(tep_not_null($payment_module_info['payment_methods_parent_title'])?$payment_module_info['payment_methods_parent_title'] . ' - ' :'') . $payment_module_info['payment_methods_title'].'</span>';
		} else {
			echo (tep_not_null($payment_module_info['payment_methods_parent_title'])?$payment_module_info['payment_methods_parent_title'] . ' - ' :'') . $payment_module_info['payment_methods_title'];
		}
		
		if (tep_not_null($last_payment_method) && $last_payment_method['payment_methods_id'] != $order->info['payment_methods_id']) {
			echo '&nbsp;&nbsp;<span class="redIndicator">(Last Payment Method: ' . (tep_not_null($last_payment_method['parent_payment_methods_title'])? $last_payment_method['parent_payment_methods_title'] . ' - ' : '' ) . $last_payment_method['payment_methods_title'] . ')</span>';
		}
	} else {
		echo (tep_not_null($payment_module_info['payment_methods_parent_title'])?$payment_module_info['payment_methods_parent_title'] . ' - ' :'') . $payment_module_info['payment_methods_title'];
	}
?>
							</div>
      					</div>
      				</td>
				</tr>
			</table>
		</td>
	</tr>
<?

$rhb_return_code_array = array(	'0' => 'Payment Is Successful',
								'1' => 'Payment Is Unsuccessful',
								'2' => 'Payment Is Not Found');

$payment_method_title_info = ob_get_contents();
ob_end_clean();
echo $payment_method_title_info;
?>
  	<tr>
    	<td class="main">
<?
if (!$view_payment_details_permission) {
	//
} else { ?>
      		<table border="0" width="100%" cellspacing="0" cellpadding="2">
      			<tr valign="top">
      				<td width="35%">
<?	if ($rhb_trans_info_row = tep_db_fetch_array($rhb_trans_info_result_sql)) { ?>		
              			<table border="0" cellspacing="0" cellpadding="2">
							<tr>
								<td class="main" valign="top" nowrap><b><?=ENTRY_RHB_AMOUNT?></b>&nbsp;</td>
			                	<td class="main" valign="top" nowrap><?=$rhb_trans_info_row["rhb_amount"]?></td>
							</tr>
							<tr>
								<td class="main" valign="top" nowrap><b><?=ENTRY_RHB_CURRENCY?></b>&nbsp;</td>
			                	<td class="main" valign="top" nowrap><?=$rhb_trans_info_row["rhb_currency"]?></td>
							</tr>
							<tr>
								<td class="main" valign="top" nowrap><b><?=ENTRY_RHB_RETURN_CODE?></b>&nbsp;</td>
			                	<td class="main" valign="top" nowrap><?=(isset($rhb_return_code_array[$rhb_trans_info_row["rhb_return_code"]])?$rhb_return_code_array[$rhb_trans_info_row["rhb_return_code"]]:'Unknown('.$rhb_trans_info_row["rhb_return_code"].')')?></td>
							</tr>
              			</table>
<?	} ?>
					</td>
        			<td valign="top">
              			<table border="0" cellspacing="0" cellpadding="2">
              				<tr>
                				<td class="main" valign="top" nowrap>
<?		if (tep_db_num_rows($rhb_trans_info_result_sql)) { ?>
                					<b><?=ENTRY_RHB_STATUS?></b>&nbsp;<?=$rhb_trans_info_row['rhb_status']?>
<?		} ?>
                				</td>
                				<td class="main" nowrap><?
                					if ($order->info['orders_status'] == '1' || $order->info['orders_status'] == '7' || $order->info['orders_status'] == '2') {
	                					echo "&nbsp;&nbsp;";
										echo tep_draw_form('rhb_status_check_form', FILENAME_ORDERS, tep_get_all_get_params(), 'post');
										echo tep_draw_hidden_field('subaction', 'payment_action');
										echo tep_draw_hidden_field('payment_action', 'check_trans_status');
										echo '<input type="submit" name="rhbCheckTransStatusBtn" value="Check Payment Status" title="Check Payment Status" class="inputButton">';
										echo "</form>";
									}
								?></td>
							</tr>
        					<tr>
                				<td class="main" valign="top" nowrap colspan="2"></td>
              				</tr>
<?		if (tep_db_num_rows($rhb_trans_history_result_sql)) { ?>
              				<tr>
                				<td class="main" colspan="2" nowrap>
                					<table border="1" cellspacing="0" cellpadding="2">
        								<tr>
                							<td class="smallText" nowrap><b><?=TABLE_HEADING_RHB_DATE?></b></td>
                							<td class="smallText" nowrap><b><?=TABLE_HEADING_RHB_STATUS?></b></td>
                							<td class="smallText" nowrap><b><?=TABLE_HEADING_RHB_DESCRIPTION?></b></td>
                							<td class="smallText" nowrap><b><?=TABLE_HEADING_RHB_CHANGEDBY?></b></td>
                						</tr>
<?
			while ($rhb_trans_history_row = tep_db_fetch_array($rhb_trans_history_result_sql)) {
        	echo ' 						<tr>
                							<td class="smallText" nowrap valign="top">'.$rhb_trans_history_row['rhb_date'].'</td>
                							<td class="smallText" nowrap valign="top">'.$rhb_trans_history_row['rhb_status'].'</td>
                							<td class="smallText" nowrap>';
            									echo nl2br($rhb_trans_history_row['rhb_description']);
            echo '							</td>
            								<td class="smallText" nowrap valign="top">'.$rhb_trans_history_row['rhb_changed_by'].'</td>
            							</tr>';
     		}
?>
                					</table>
                				</td>
                			</tr>
<?		}	?>
              			</table>
              		</td>
      			</tr>
      		</table>
<?	} ?>
      		
   		</td>
  	</tr>
</table>