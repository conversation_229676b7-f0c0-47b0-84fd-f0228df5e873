<?
/*
  	$Id: orders_list.inc.php,v 1.1 2010/06/25 09:42:39 boonhock Exp $
	
  	Developer: Ho <PERSON>
  	Copyright (c) 2005 SKC Venture
  	
  	Released under the GNU General Public License
*/
include_once(DIR_FS_CATALOG_MODULES . 'payment/rhb/admin/languages/'.$language.'/rhb.lng.php');

$rhb_return_code_array = array(	'0' => 'Payment Is Successful',
								'1' => 'Payment Is Unsuccessful',
								'2' => 'Payment Is Not Found');

$rhb_trans_info_select_sql = "	SELECT * 
								FROM " . TABLE_RHB . "
								WHERE rhb_orders_id = '" . (int)$order_obj->orders_id  . "'";
$rhb_trans_info_result_sql = tep_db_query($rhb_trans_info_select_sql);
?>
<table border="0" width="100%" cellspacing="0" cellpadding="2">
	<tr>
		<td class="subInvoiceBoxHeading">
			<div style="border-bottom: 1px solid #996600; border-top: 1px solid #996600; padding: 2px;">Payment Details</div>
		</td>
	</tr>
<? if ($rhb_trans_info_row = tep_db_fetch_array($rhb_trans_info_result_sql)) { ?>
  	<tr>
    	<td class="main">
      		<table border="0" width="100%" cellspacing="0" cellpadding="2">
      			<tr valign="top">
        			<td width="40%">
              			<table border="0" cellspacing="0" cellpadding="2">
							<tr>
								<td class="main" valign="top" nowrap><b><?=ENTRY_RHB_STATUS?></b>&nbsp;</td>
			                	<td class="main" valign="top" nowrap><?=$rhb_trans_info_row["rhb_status"]?></td>
							</tr>
							<tr>
								<td class="main" valign="top" nowrap><b><?=ENTRY_RHB_AMOUNT?></b>&nbsp;</td>
			                	<td class="main" valign="top" nowrap><?=$rhb_trans_info_row["rhb_amount"]?></td>
							</tr>
							<tr>
								<td class="main" valign="top" nowrap><b><?=ENTRY_RHB_CURRENCY?></b>&nbsp;</td>
			                	<td class="main" valign="top" nowrap><?=$rhb_trans_info_row["rhb_currency"]?></td>
							</tr>
							<tr>
								<td class="main" valign="top" nowrap><b><?=ENTRY_RHB_RETURN_CODE?></b>&nbsp;</td>
			                	<td class="main" valign="top" nowrap><?=(isset($rhb_return_code_array[$rhb_trans_info_row["rhb_return_code"]])?$rhb_return_code_array[$rhb_trans_info_row["rhb_return_code"]]:'Unknown('.$rhb_trans_info_row["rhb_return_code"].')')?></td>
							</tr>
              			</table>
        			</td>
      			</tr>
      		</table>
   		</td>
  	</tr>
<? } else { ?>
	<tr>
		<td class="invoiceRecords"><?=ENTRY_NO_PAYMENT_INFO?></td>
	</tr>
<? } ?>
</table>