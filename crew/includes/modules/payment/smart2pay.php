<?

require_once('payment_gateway.php');

class smart2pay extends payment_gateway {

    var $code, $check_trans_status_flag, $merchant_server_ip_address, $smart2payCurrencies;

    function smart2pay($pm_id = '') {
        global $order, $languages_id, $default_languages_id, $currency;

        $this->code = 'smart2pay';
        $this->check_trans_status_flag = false;
        $this->merchant_server_ip_address = (isset($_SERVER['SERVER_ADDR']) ? $_SERVER['SERVER_ADDR'] : '');  //IP address of merchant server
        $this->title = $this->code;
        $this->preferred = true;
        $this->filename = 'smart2pay.php';
        $this->auto_cancel_period = 30; // In minutes
        $this->connect_via_proxy = false; // localhost test - set to true, server test - set to false //need proxy to connect outside
        $this->force_to_checkoutprocess = true;
        $this->has_ipn = true;
        $this->payment_methods_id = 0;
        $this->payment_methods_parent_id = 0;

        $payment_methods_select_sql = "	SELECT  payment_methods_parent_id, payment_methods_code, 
												payment_methods_types_id, 
												payment_methods_receive_status_mode, payment_methods_id, 
												payment_methods_title, payment_methods_sort_order, 
												payment_methods_receive_status, payment_methods_legend_color, 
												payment_methods_logo 
										FROM " . TABLE_PAYMENT_METHODS . "
										WHERE 1 ";
        if ((int) $pm_id > 0) {
            $payment_methods_select_sql .= "	AND payment_methods_id = '" . (int) $pm_id . "'";
        } else {
            $payment_methods_select_sql .= "	AND payment_methods_filename = '" . tep_db_input($this->filename) . "'";
        }

        $payment_methods_result_sql = tep_db_query($payment_methods_select_sql);
        if ($payment_methods_row = tep_db_fetch_array($payment_methods_result_sql)) {
            $pm_desc_title_select_sql = "	SELECT payment_methods_description_title 
											FROM " . TABLE_PAYMENT_METHODS_DESCRIPTION . " 
											WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "' 
												AND (IF (languages_id = '" . tep_db_input($languages_id) . "', 1, IF ((	SELECT COUNT(payment_methods_id) > 0 
																														FROM " . TABLE_PAYMENT_METHODS_DESCRIPTION . " 
																														WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "' 
																															AND languages_id = '" . (int) $languages_id . "'), 0, languages_id = '" . (int) $default_languages_id . "')))";

            $pm_desc_title_result_sql = tep_db_query($pm_desc_title_select_sql);
            $pm_desc_title_row = tep_db_fetch_array($pm_desc_title_result_sql);

            $this->enabled = $payment_methods_row['payment_methods_receive_status_mode'];
            $this->payment_methods_id = (int) $payment_methods_row['payment_methods_id'];
            $this->payment_methods_parent_id = (int) $payment_methods_row['payment_methods_parent_id'];
            $this->code = $payment_methods_row['payment_methods_code'];
            $this->title = $payment_methods_row['payment_methods_title'];

            $this->display_title = $pm_desc_title_row['payment_methods_description_title'];
            $this->description = $this->display_title;
            $this->sort_order = $payment_methods_row['payment_methods_sort_order'];
            $this->legend_display_colour = $payment_methods_row['payment_methods_legend_color'];
            $this->smart2payCurrencies = $this->get_support_currencies($this->payment_methods_id);

            $configuration_setting_array = $this->load_pm_setting();
            $this->order_status_id = $configuration_setting_array['MODULE_PAYMENT_SMART2PAY_ORDER_STATUS_ID'];
            $this->processing_status_id = $configuration_setting_array['MODULE_PAYMENT_SMART2PAY_PROCESSING_STATUS_ID'];
            $this->confirm_complete_days = $configuration_setting_array['MODULE_PAYMENT_SMART2PAY_CONFIRM_COMPLETE'];
            $this->require_address_information = $configuration_setting_array['MODULE_PAYMENT_SMART2PAY_MANDATORY_ADDRESS_FIELD'];
            $this->test_mode = ($configuration_setting_array['MODULE_PAYMENT_SMART2PAY_TEST_MODE'] == 'True' ? true : false);
            $this->message = $configuration_setting_array['MODULE_PAYMENT_SMART2PAY_MESSAGE'];
            $this->email_message = $configuration_setting_array['MODULE_PAYMENT_SMART2PAY_EMAIL_MESSAGE'];
            $this->email_footer = str_replace(array("<br>", "<BR>", "\r\n"), array("\n", "\n", ''), $this->email_message);

            $this->order_processing_status = ((int) $this->processing_status_id > 0 ? $this->processing_status_id : 7 );
            $this->order_status = ( (int) $this->order_status_id > 0 ? $this->order_status_id : DEFAULT_ORDERS_STATUS_ID );

            if ($this->test_mode) {
                $this->form_action_url = "https://europaytest.smart2pay.com/Process.asmx"; // test url - get transaction id
                $this->form_merchant_url = "https://europaytest.smart2pay.com/MerchantService.asmx";
                $this->form_bank_url = "https://europaytest.smart2pay.com/BankTransfer.asmx";
                $this->form_submit_url = "https://europaytest.smart2pay.com/Landing.aspx"; // test url - create order
            } else {
                $this->form_action_url = "https://europay.smart2pay.com/Process.asmx";
                $this->form_submit_url = "https://europay.smart2pay.com/Landing.aspx";
                $this->form_bank_url = "https://europay.smart2pay.com/BankTransfer.asmx";
                $this->form_merchant_url = "https://europay.smart2pay.com/MerchantService.asmx";
            }
        }
    }

    function javascript_validation() {
        return false;
    }

    function selection() {
        global $languages_id, $default_languages_id;

        $selection_array = array();

        $payment_methods_select_sql = "	SELECT payment_methods_id, payment_methods_code, payment_methods_types_id, payment_methods_logo, payment_methods_receive_status_mode 
										FROM " . TABLE_PAYMENT_METHODS . " 
										WHERE payment_methods_receive_status = 1 
											AND payment_methods_receive_status_mode <> 0 
											AND payment_methods_parent_id = '" . (int) $this->payment_methods_id . "'
										ORDER BY payment_methods_sort_order";
        $payment_methods_result_sql = tep_db_query($payment_methods_select_sql);
        while ($payment_methods_row = tep_db_fetch_array($payment_methods_result_sql)) {
            $pm_desc_title_select_sql = "	SELECT payment_methods_description_title 
											FROM " . TABLE_PAYMENT_METHODS_DESCRIPTION . " 
											WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "' 
												AND (IF (languages_id = '" . tep_db_input($languages_id) . "', 1, IF ((	SELECT COUNT(payment_methods_id) > 0 
																														FROM " . TABLE_PAYMENT_METHODS_DESCRIPTION . " 
																														WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "' 
																															AND languages_id = '" . (int) $languages_id . "'), 0, languages_id = '" . (int) $default_languages_id . "')))";
            $pm_desc_title_result_sql = tep_db_query($pm_desc_title_select_sql);
            $pm_desc_title_row = tep_db_fetch_array($pm_desc_title_result_sql);

            $pm_array = $this->load_pm_setting(1, $payment_methods_row['payment_methods_id']);

            $selection_array[] = array('payment_gateway_code' => $this->code,
                'payment_methods_receive_status_mode' => $payment_methods_row['payment_methods_receive_status_mode'],
                'payment_methods_id' => $payment_methods_row['payment_methods_id'],
                'payment_methods_code' => $payment_methods_row['payment_methods_code'],
                'payment_methods_types_id' => $payment_methods_row['payment_methods_types_id'],
                'payment_methods_parent_id' => $this->payment_methods_id,
                'payment_methods_logo' => $payment_methods_row['payment_methods_logo'],
                'payment_methods_description_title' => $pm_desc_title_row['payment_methods_description_title'],
                'currency' => $this->get_support_currencies($payment_methods_row['payment_methods_id']),
                'show_billing_address' => $pm_array['MODULE_PAYMENT_SMART2PAY_MANDATORY_ADDRESS_FIELD'],
                'confirm_complete_days' => $pm_array['MODULE_PAYMENT_SMART2PAY_CONFIRM_COMPLETE']
            );

            if ($payment_methods_row['payment_methods_receive_status_mode'] == '-1') { // Inactive
                $pm_status_message_select = "	SELECT payment_methods_status_message 
												FROM " . TABLE_PAYMENT_METHODS_STATUS_DESCRIPTION . " 
												WHERE payment_methods_mode = 'RECEIVE' 
													AND payment_methods_status = '-1' 
													AND languages_id = '" . (int) $languages_id . "'
													AND payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "'";
                $pm_status_message_result = tep_db_query($pm_status_message_select);
                $pm_status_message_row = tep_db_fetch_array($pm_status_message_result);
                $selection_array[sizeof($selection_array) - 1]['payment_methods_status_message'] = $pm_status_message_row['payment_methods_status_message'];
            }
        }

        return $selection_array;
    }

    function get_require_address_information() {
        return $this->require_address_information;
    }

    function draw_confirm_button() {
        return tep_image_button2('red', 'javascript:document.checkout_confirmation.submit()', MODULE_PAYMENT_SMART2PAY_TEXT_CART, 200);
    }

    function get_confirm_button_caption() {
        return MODULE_PAYMENT_SMART2PAY_TEXT_DISPLAY_TITLE;
    }

    function pre_confirmation_check() {
        ; // Nothing to check for now
    }

    function confirmation() {
        return array('title' => $this->message);
    }

    function set_support_currencies($currency_array) {
        $this->smart2payCurrencies = $currency_array;
    }

    function process_button() {
        global $order, $currency;

        $validation_flag = false;
        $currencies = new currencies();

        // Check Login
        if ($this->force_to_checkoutprocess && (!isset($_SESSION['order_logged']) || !tep_not_null($_SESSION['order_logged'])))
            return;

        $this->smart2payCurrencies = $this->get_support_currencies($this->payment_methods_id, false);
        $smart2payCurrencies = $currency;

        // Get default currency if user currency is empty
        if (!in_array($smart2payCurrencies, $this->smart2payCurrencies)) {
            $smart2payCurrencies = $this->smart2payCurrencies[0];
        }

        // Get Merchant Data
        $this->get_merchant_account($smart2payCurrencies);

        // Get Total Amount - Convert other currency to USD
        $OrderAmt = $order->info['total'] * $currencies->get_value($smart2payCurrencies);

        $exponent = (int) (tep_not_null($this->exponent) ? $this->exponent : 2);

        $formatted_order_amt = number_format($OrderAmt, $currencies->currencies[$smart2payCurrencies]['decimal_places'], $currencies->currencies[$smart2payCurrencies]['decimal_point'], '');
        if ($exponent > 0) {
            $formatted_order_amt = $formatted_order_amt * pow(10, $exponent);
        }

        // Set to log
        $path_info_array = pathinfo($_SERVER['SCRIPT_FILENAME']);
        $smart2pay_status_history_data_sql = array('smart2pay_order_id' => (int) $_SESSION['order_logged'],
            'smart2pay_date' => 'now()',
            'smart2pay_status_id' => 1,
            'smart2pay_changed_by' => (isset($path_info_array["basename"]) && $path_info_array["basename"] == 'cron_order_payment.php' ? 'cronjob' : (isset($_SESSION['login_email_address']) ? $_SESSION['login_email_address'] : 'system' ) )
        );

        switch (strtolower($this->code)) {
            case "wallie":
                $submit_xml = '<?xml version="1.0" encoding="utf-8"?>
								<InitiatePaymentRequest xmlns="http://europay.smart2pay.com/message" version="1.0">
									<MID>' . $this->smart2pay_id . '</MID>
									<PaymentMethod>' . $this->code . '</PaymentMethod>
									<Details>
                            			<Payment>
                                			<Amount>' . $formatted_order_amt . '</Amount>
                                			<Currency>' . $smart2payCurrencies . '</Currency>
                                			<MTID>' . $_SESSION["order_logged"] . '</MTID>
                							<SuccessURL>' . tep_href_link(FILENAME_CHECKOUT_PROCESS) . '</SuccessURL>
                                			<FailureURL>' . tep_href_link(FILENAME_CHECKOUT_PROCESS) . '</FailureURL>
               							</Payment>
                       			 	</Details>
                    			</InitiatePaymentRequest>';

                $submit_xml = str_replace('<', '&lt;', $submit_xml);
                $submit_xml = str_replace('>', '&gt;', $submit_xml);

                $XMLRequest = '<?xml version="1.0" encoding="utf-8"?>';
                $XMLRequest .= '<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">';
                $XMLRequest .= '<soap:Body>';
                $XMLRequest .= '<InitiatePayment xmlns="http://europay.smart2pay.com">';
                $XMLRequest .= '<XMLPaymentRequest>' . $submit_xml . '</XMLPaymentRequest>';
                $XMLRequest .= '</InitiatePayment>';
                $XMLRequest .= '</soap:Body>';
                $XMLRequest .= '</soap:Envelope>';

                $data_string = $XMLRequest;
                $return_result = $this->curl_connect($data_string, "InitiatePayment", $this->form_action_url);

                require_once(DIR_WS_CLASSES . 'ogm_xml_to_ary.php');
                $this->xml_array_obj = new ogm_xml_to_ary($return_result, 'content');
                $xml_array = $this->xml_array_obj->get_ogm_xml_to_ary();

                $return_err_code = (isset($xml_array['soap:Envelope']['_c']['soap:Body']['_c']['InitiatePaymentResponse']['_c']['InitiatePaymentResult']['_c']['InitiatePaymentResponse']['_c']['ErrorCode']['_v']) ? $xml_array['soap:Envelope']['_c']['soap:Body']['_c']['InitiatePaymentResponse']['_c']['InitiatePaymentResult']['_c']['InitiatePaymentResponse']['_c']['ErrorCode']['_v'] : "");
                $return_err_msg = (isset($xml_array['soap:Envelope']['_c']['soap:Body']['_c']['InitiatePaymentResponse']['_c']['InitiatePaymentResult']['_c']['InitiatePaymentResponse']['_c']['ErrorMessage']['_v']) ? $xml_array['soap:Envelope']['_c']['soap:Body']['_c']['InitiatePaymentResponse']['_c']['InitiatePaymentResult']['_c']['InitiatePaymentResponse']['_c']['ErrorMessage']['_v'] : "");
                $return_trxid = (isset($xml_array['soap:Envelope']['_c']['soap:Body']['_c']['InitiatePaymentResponse']['_c']['InitiatePaymentResult']['_c']['InitiatePaymentResponse']['_c']['TrxID']['_v']) ? $xml_array['soap:Envelope']['_c']['soap:Body']['_c']['InitiatePaymentResponse']['_c']['InitiatePaymentResult']['_c']['InitiatePaymentResponse']['_c']['TrxID']['_v'] : "");
                $return_amount = (isset($xml_array['soap:Envelope']['_c']['soap:Body']['_c']['InitiatePaymentResponse']['_c']['InitiatePaymentResult']['_c']['InitiatePaymentResponse']['_c']['Amount']['_v']) ? $xml_array['soap:Envelope']['_c']['soap:Body']['_c']['InitiatePaymentResponse']['_c']['InitiatePaymentResult']['_c']['InitiatePaymentResponse']['_c']['Amount']['_v'] : 0);
                $return_mid = (isset($xml_array['soap:Envelope']['_c']['soap:Body']['_c']['InitiatePaymentResponse']['_c']['InitiatePaymentResult']['_c']['InitiatePaymentResponse']['_c']['MID']['_v']) ? $xml_array['soap:Envelope']['_c']['soap:Body']['_c']['InitiatePaymentResponse']['_c']['InitiatePaymentResult']['_c']['InitiatePaymentResponse']['_c']['MID']['_v'] : '');
                $return_mtid = (isset($xml_array['soap:Envelope']['_c']['soap:Body']['_c']['InitiatePaymentResponse']['_c']['InitiatePaymentResult']['_c']['InitiatePaymentResponse']['_c']['MTID']['_v']) ? $xml_array['soap:Envelope']['_c']['soap:Body']['_c']['InitiatePaymentResponse']['_c']['InitiatePaymentResult']['_c']['InitiatePaymentResponse']['_c']['MTID']['_v'] : '');
                $return_currency = (isset($xml_array['soap:Envelope']['_c']['soap:Body']['_c']['InitiatePaymentResponse']['_c']['InitiatePaymentResult']['_c']['InitiatePaymentResponse']['_c']['Currency']['_v']) ? $xml_array['soap:Envelope']['_c']['soap:Body']['_c']['InitiatePaymentResponse']['_c']['InitiatePaymentResult']['_c']['InitiatePaymentResponse']['_c']['Currency']['_v'] : '');

                if ($exponent > 0) {
                    $return_amount = $return_amount / pow(10, $exponent);
                }

                $return_amount = number_format($return_amount, $currencies->currencies[$return_currency]['decimal_places'], $currencies->currencies[$return_currency]['decimal_point'], $currencies->currencies[$return_currency]['thousands_point']);

                if (tep_not_null($return_err_code)) {
                    $smart2pay_status_history_data_sql['smart2pay_description'] = tep_db_prepare_input('Error Code - ' . $return_err_code . "\n" . $return_err_msg);
                    tep_db_perform(TABLE_SMART2PAY_STATUS_HISTORY, $smart2pay_status_history_data_sql);
                    tep_redirect(tep_href_link(FILENAME_CHECKOUT_PAYMENT, 'error_message=' . urlencode(MODULE_PAYMENT_SMART2PAY_TEXT_ERROR_MESSAGE), 'SSL', true, false));
                }
                break;

            case 'paysafecard':
            case 'ukash':
            case 'mercadopago':
            case 'gluepay':
                // Populate INSERT_PAYIN Request Hash
                if (strtolower($this->code) == 'gluepay') {
                    $str = strtolower("insert_payin" . $this->smart2pay_id . $this->code . $this->merchant_server_ip_address . $_SESSION['order_logged'] . $formatted_order_amt . $smart2payCurrencies . 'SEsv-SE' . $this->smart2pay_key);
                    $hash = bin2hex(md5($str, true));

                    $submit_xml = '<?xml version="1.0" encoding="utf-8"?>
                                    <Request version="1.0" xmlns="http://europay.smart2pay.com/message">
                                        <Action>INSERT_PAYIN</Action>
                                        <MID>' . $this->smart2pay_id . '</MID>
                                        <PaymentMethod>' . $this->code . '</PaymentMethod>
                                        <IPAddress>' . $this->merchant_server_ip_address . '</IPAddress>
                                        <Hash>' . $hash . '</Hash>
                                        <Details>
                                            <MTID>' . $_SESSION["order_logged"] . '</MTID>
                                            <Amount>' . $formatted_order_amt . '</Amount> 
                                            <Currency>' . $smart2payCurrencies . '</Currency>
                                            <Country>SE</Country>
                                            <Language>sv-SE</Language>
                                            <CustomerName>' . $_SESSION["customer_first_name"] . '</CustomerName>
                                            <CustomerEmail><EMAIL></CustomerEmail>
                                        </Details>
                                    </Request>
                                    ';
                } else {
                    if (strtolower($this->code) == 'mercadopago' && $smart2payCurrencies == 'BRL') {
                        $str = strtolower("insert_payin" . $this->smart2pay_id . $this->code . $this->merchant_server_ip_address . $_SESSION['order_logged'] . $formatted_order_amt . 'REA' . $this->smart2pay_key);
                    } else {
                        $str = strtolower("insert_payin" . $this->smart2pay_id . $this->code . $this->merchant_server_ip_address . $_SESSION['order_logged'] . $formatted_order_amt . $smart2payCurrencies . $this->smart2pay_key);
                    }

                    $hash = bin2hex(md5($str, true));

                    $submit_xml = '<?xml version="1.0" encoding="utf-8"?>
                                    <Request version="1.0" xmlns="http://europay.smart2pay.com/message">
                                        <Action>INSERT_PAYIN</Action>
                                        <MID>' . $this->smart2pay_id . '</MID>
                                        <PaymentMethod>' . $this->code . '</PaymentMethod>
                                        <IPAddress>' . $this->merchant_server_ip_address . '</IPAddress>
                                        <Hash>' . $hash . '</Hash>
                                        <Details>
                                            <MTID>' . $_SESSION["order_logged"] . '</MTID>
                                            <Amount>' . $formatted_order_amt . '</Amount> 
                                            <Currency>' . ((strtolower($this->code) == 'mercadopago' && $smart2payCurrencies == 'BRL') ? 'REA' : $smart2payCurrencies) . '</Currency>
                                            <SuccessURL>' . tep_href_link(FILENAME_CHECKOUT_PROCESS) . '</SuccessURL>
                                            <FailureURL>' . tep_href_link(FILENAME_CHECKOUT_PROCESS) . '</FailureURL>
                                    ';

                    if (in_array(strtolower($this->code), array('paysafecard', 'mercadopago'))) {
                        $submit_xml .= '<CancelURL>' . tep_href_link(FILENAME_CHECKOUT_PROCESS) . '</CancelURL>
                                        <ProcessingURL>' . tep_href_link(FILENAME_CHECKOUT_PROCESS) . '</ProcessingURL>
                                       ';
                    } else if (strtolower($this->code) == "ukash") {
                        $submit_xml .= '<PendingURL>' . tep_href_link(FILENAME_CHECKOUT_PROCESS) . '</PendingURL>';
                    }

                    if (strtolower($this->code) == 'mercadopago') {
                        $submit_xml .= '<CustomerName FirstName="' . $_SESSION["customer_first_name"] . '" LastName="' . $_SESSION["customer_last_name"] . '" />
                                        ';
                    } else {
                        $submit_xml .= '<Language>en-US</Language>
                                        <Description>payment</Description>
                                        <CustomerName>' . $_SESSION["customer_first_name"] . '</CustomerName>
                                        ';
                    }

                    $submit_xml .= '		<CustomerEmail><EMAIL></CustomerEmail>
                                        </Details>
                                    </Request>';
                }

                $submit_xml = str_replace('<', '&lt;', $submit_xml);
                $submit_xml = str_replace('>', '&gt;', $submit_xml);

                $XMLRequest = '<?xml version="1.0" encoding="utf-8"?>';
                $XMLRequest .= '<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	        					xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">';
                $XMLRequest .= '<soap:Body>';
                $XMLRequest .= '<SubmitRequest xmlns="http://europay.smart2pay.com">';
                $XMLRequest .= '<request>' . $submit_xml . '</request>';
                $XMLRequest .= '</SubmitRequest>';
                $XMLRequest .= '</soap:Body>';
                $XMLRequest .= '</soap:Envelope>';

                $data_string = $XMLRequest;

                $return_result = $this->curl_connect($data_string, "SubmitRequest", $this->form_merchant_url);

                require_once(DIR_WS_CLASSES . 'ogm_xml_to_ary.php');
                $this->xml_array_obj = new ogm_xml_to_ary($return_result, 'content');
                $xml_array = $this->xml_array_obj->get_ogm_xml_to_ary();

                $return_err_code = (isset($xml_array['soap:Envelope']['_c']['soap:Body']['_c']['SubmitRequestResponse']['_c']['SubmitRequestResult']['_c']['Response']['_c']['Details']['_c']['ReturnCode']['_v']) ? $xml_array['soap:Envelope']['_c']['soap:Body']['_c']['SubmitRequestResponse']['_c']['SubmitRequestResult']['_c']['Response']['_c']['Details']['_c']['ReturnCode']['_v'] : "");
                $return_trxid = (isset($xml_array['soap:Envelope']['_c']['soap:Body']['_c']['SubmitRequestResponse']['_c']['SubmitRequestResult']['_c']['Response']['_c']['Details']['_c']['TRXID']['_v']) ? $xml_array['soap:Envelope']['_c']['soap:Body']['_c']['SubmitRequestResponse']['_c']['SubmitRequestResult']['_c']['Response']['_c']['Details']['_c']['TRXID']['_v'] : "");
                $return_amount = (isset($xml_array['soap:Envelope']['_c']['soap:Body']['_c']['SubmitRequestResponse']['_c']['SubmitRequestResult']['_c']['Response']['_c']['Details']['_c']['Amount']['_v']) ? $xml_array['soap:Envelope']['_c']['soap:Body']['_c']['SubmitRequestResponse']['_c']['SubmitRequestResult']['_c']['Response']['_c']['Details']['_c']['Amount']['_v'] : 0);
                $return_mid = (isset($xml_array['soap:Envelope']['_c']['soap:Body']['_c']['SubmitRequestResponse']['_c']['SubmitRequestResult']['_c']['Response']['_c']['MID']['_v']) ? $xml_array['soap:Envelope']['_c']['soap:Body']['_c']['SubmitRequestResponse']['_c']['SubmitRequestResult']['_c']['Response']['_c']['MID']['_v'] : '');
                $return_hash = (isset($xml_array['soap:Envelope']['_c']['soap:Body']['_c']['SubmitRequestResponse']['_c']['SubmitRequestResult']['_c']['Response']['_c']['Hash']['_v']) ? $xml_array['soap:Envelope']['_c']['soap:Body']['_c']['SubmitRequestResponse']['_c']['SubmitRequestResult']['_c']['Response']['_c']['Hash']['_v'] : '');
                $return_mtid = (isset($xml_array['soap:Envelope']['_c']['soap:Body']['_c']['SubmitRequestResponse']['_c']['SubmitRequestResult']['_c']['Response']['_c']['Details']['_c']['MTID']['_v']) ? $xml_array['soap:Envelope']['_c']['soap:Body']['_c']['SubmitRequestResponse']['_c']['SubmitRequestResult']['_c']['Response']['_c']['Details']['_c']['MTID']['_v'] : '');
                $return_currency = (isset($xml_array['soap:Envelope']['_c']['soap:Body']['_c']['SubmitRequestResponse']['_c']['SubmitRequestResult']['_c']['Response']['_c']['Details']['_c']['Currency']['_v']) ? $xml_array['soap:Envelope']['_c']['soap:Body']['_c']['SubmitRequestResponse']['_c']['SubmitRequestResult']['_c']['Response']['_c']['Details']['_c']['Currency']['_v'] : '');

                // Populate INSERT_PAYIN Response Hash
                if (tep_not_null($return_err_code)) {
                    $str = strtolower("insert_payin" . $this->smart2pay_id . $this->code . $this->merchant_server_ip_address . $return_err_code . $this->smart2pay_key);
                    $response_hash = bin2hex(md5($str, true));
                } else {
                    $str = strtolower("insert_payin" . $this->smart2pay_id . $this->code . $this->merchant_server_ip_address . $_SESSION['order_logged'] . $return_trxid . $return_amount . $return_currency . $this->smart2pay_key);
                    $response_hash = bin2hex(md5($str, true));
                }

                // This line has to be AFTER hash string generation because hash is need to based on the Currency Code used by Merchant which is REA
                if (strtolower($this->code) == 'mercadopago' && $return_currency == 'REA')
                    $return_currency = 'BRL';

                if ($exponent > 0) {
                    $return_amount = $return_amount / pow(10, $exponent);
                }

                $return_amount = number_format($return_amount, $currencies->currencies[$return_currency]['decimal_places'], $currencies->currencies[$return_currency]['decimal_point'], $currencies->currencies[$return_currency]['thousands_point']);

                if ($response_hash !== strtolower($return_hash)) {
                    $smart2pay_status_history_data_sql['smart2pay_description'] = tep_db_prepare_input('Error: Hash Not Match (' . $response_hash . ' != ' . $return_hash . ')');
                    tep_db_perform(TABLE_SMART2PAY_STATUS_HISTORY, $smart2pay_status_history_data_sql);
                    tep_redirect(tep_href_link(FILENAME_CHECKOUT_PAYMENT, 'error_message=' . urlencode(MODULE_PAYMENT_SMART2PAY_TEXT_ERROR_MESSAGE), 'SSL', true, false));
                }

                if (tep_not_null($return_err_code)) {
                    $err_msg = $this->get_error_message($return_err_code);
                    $smart2pay_status_history_data_sql['smart2pay_description'] = tep_db_prepare_input('Error Code - ' . $return_err_code . "\n" . $err_msg);
                    tep_db_perform(TABLE_SMART2PAY_STATUS_HISTORY, $smart2pay_status_history_data_sql);
                    tep_redirect(tep_href_link(FILENAME_CHECKOUT_PAYMENT, 'error_message=' . urlencode(MODULE_PAYMENT_SMART2PAY_TEXT_ERROR_MESSAGE), 'SSL', true, false));
                }

                break;
        }

        $smart2pay_status_history_data_sql = array('smart2pay_order_id' => (int) $_SESSION['order_logged'],
            'smart2pay_date' => 'now()',
            'smart2pay_status_id' => 1,
            'smart2pay_changed_by' => (isset($path_info_array["basename"]) && $path_info_array["basename"] == 'cron_order_payment.php' ? 'cronjob' : (isset($_SESSION['login_email_address']) ? $_SESSION['login_email_address'] : 'system' ) )
        );

        // Get Order Info
        $order_info_select_sql = "	SELECT o.payment_methods_id, o.currency, o.currency_value, ot.value, ot.text
									FROM " . TABLE_ORDERS . " AS o
									INNER JOIN " . TABLE_ORDERS_TOTAL . " AS ot
										ON o.orders_id=ot.orders_id AND ot.class='ot_total'
									WHERE o.orders_id = '" . tep_db_input($_SESSION['order_logged']) . "'";

        $order_info_result_sql = tep_db_query($order_info_select_sql);

        if ($order_info_row = tep_db_fetch_array($order_info_result_sql)) {
            if (tep_not_null($return_trxid)) {
                // Return Data Checking
                if (!tep_not_null($return_mid) || $return_mid != $this->smart2pay_id) {
                    $smart2pay_status_history_data_sql['smart2pay_description'] = 'Error: Merchant id not matched (' . $return_mid . ' != ' . $this->smart2pay_id . ')';
                } else if (!tep_not_null($return_mtid) || $return_mtid != $_SESSION['order_logged']) {
                    $smart2pay_status_history_data_sql['smart2pay_description'] = 'Error: Order id not matched (' . $return_mtid . ' != ' . $_SESSION['order_logged'] . ')';
                } else if (!tep_not_null($return_currency) || $currencies->currencies[$return_currency]['symbol_left'] . $return_amount . $currencies->currencies[$return_currency]['symbol_right'] != strip_tags($order_info_row['text'])) {
                    $smart2pay_status_history_data_sql['smart2pay_description'] = 'Error: Currency or Amount not matched (' . $currencies->currencies[$return_currency]['symbol_left'] . $return_amount . $currencies->currencies[$return_currency]['symbol_right'] . ' != ' . strip_tags($order_info_row['text']) . ')';
                } else {
                    $smart2pay_order_data_sql = array('smart2pay_merchant_id' => tep_db_prepare_input($return_mid),
                        'smart2pay_payment_method' => tep_db_prepare_input($this->code),
                        'smart2pay_order_id' => (int) $return_mtid,
                        'smart2pay_transaction_id' => tep_db_prepare_input($return_trxid),
                        'smart2pay_amount' => tep_db_prepare_input(preg_replace('/[^\d.]/', '', $return_amount)),
                        'smart2pay_currency' => tep_db_prepare_input($return_currency),
                        'smart2pay_status_id' => 1
                    );
                    tep_db_perform(TABLE_SMART2PAY, $smart2pay_order_data_sql);

                    $smart2pay_status_history_data_sql['smart2pay_description'] = tep_db_prepare_input("Transaction: OK");
                    tep_db_perform(TABLE_SMART2PAY_STATUS_HISTORY, $smart2pay_status_history_data_sql);
                    $validation_flag = true;
                }
            } else {
                $smart2pay_status_history_data_sql['smart2pay_description'] = tep_db_prepare_input("Error: Return Empty Value");
            }

            if ($validation_flag == false) {
                tep_db_perform(TABLE_SMART2PAY_STATUS_HISTORY, $smart2pay_status_history_data_sql);
                tep_redirect(tep_href_link(FILENAME_CHECKOUT_PAYMENT, 'error_message=' . urlencode(MODULE_PAYMENT_SMART2PAY_TEXT_ERROR_MESSAGE), 'SSL', true, false));
            }
        } else {
            $smart2pay_status_history_data_sql['smart2pay_description'] = tep_db_prepare_input("Error: Order ID Not Created");
            tep_db_perform(TABLE_SMART2PAY_STATUS_HISTORY, $smart2pay_status_history_data_sql);
            tep_redirect(tep_href_link(FILENAME_CHECKOUT_PAYMENT, 'error_message=' . urlencode(MODULE_PAYMENT_SMART2PAY_TEXT_ERROR_MESSAGE), 'SSL', true, false));
        }
        return tep_draw_hidden_field('TRXID', $return_trxid);
    }

    /* Call when:
      1. "Proceed to Smart2Pay" button click
      2. Return from Smart2Pay
     */

    function before_process() {
        if (!isset($_SESSION['order_logged']) || !tep_not_null($_SESSION['order_logged'])) {
            return;
        }

        // Validate Transaction
        $return_array = $this->check_trans_status($_SESSION['order_logged']);

        if (!isset($return_array['smart2pay_status_id']) || !in_array(strtolower($return_array['smart2pay_status_id']), array('2', '1'))) {
            tep_redirect(tep_href_link(FILENAME_CHECKOUT_PAYMENT, 'error_message=' . urlencode(MODULE_PAYMENT_SMART2PAY_TEXT_ERROR_MESSAGE), 'SSL', true, false));
        }
    }

    function after_process() {
        return false;
    }

    function link_to_payment($new_order_id) {
        if (isset($_SESSION['order_logged']))
            unset($_SESSION['order_logged']);

        $_SESSION['order_logged'] = $new_order_id;
        require(DIR_WS_INCLUDES . 'modules/payment/smart2pay/catalog/smart2pay_splash.php');

        return;
    }

    function is_supported_currency($selected_currency) {
        return (in_array($selected_currency, $this->smart2payCurrencies) ? true : false);
    }

    function check_trans_status($order_id, $ipn_call = 0) {
        $authentication_flag = false;

        $this->check_trans_status_flag = false;

        // Set to log
        $path_info_array = pathinfo($_SERVER['SCRIPT_FILENAME']);
        $smart2pay_status_history_data_sql = array('smart2pay_order_id' => (int) $order_id,
            'smart2pay_date' => 'now()',
            'smart2pay_status_id' => 1,
            'smart2pay_changed_by' => (isset($path_info_array["basename"]) && $path_info_array["basename"] == 'cron_order_payment.php' ? 'cronjob' : (isset($_SESSION['login_email_address']) ? $_SESSION['login_email_address'] : 'system' ) )
        );

        // Get Order Info
        $order_info_select_sql = "	SELECT o.payment_methods_id, o.currency, o.currency_value, ot.value, ot.text
									FROM " . TABLE_ORDERS . " AS o 
									INNER JOIN " . TABLE_ORDERS_TOTAL . " AS ot 
										ON o.orders_id=ot.orders_id AND ot.class='ot_total'
									WHERE o.orders_id = '" . tep_db_input($order_id) . "'";
        $order_info_result_sql = tep_db_query($order_info_select_sql);
        if ($order_info_row = tep_db_fetch_array($order_info_result_sql)) { // Check if Order Created - after return from Smart2Pay
            // Check if the selected payment method exist for the order made
            if ($order_info_row['payment_methods_id'] != $this->payment_methods_id)
                return;

            $smart2pay_order_info_select_sql = " SELECT smart2pay_payment_method, smart2pay_transaction_id 
												 FROM " . TABLE_SMART2PAY . "
												 WHERE smart2pay_order_id = '" . (int) $order_id . "'";
            $smart2pay_order_info_result_sql = tep_db_query($smart2pay_order_info_select_sql);
            if ($smart2pay_order_info_row = tep_db_fetch_array($smart2pay_order_info_result_sql)) {

                $smart2payCurrencies = $order_info_row['currency'];
                $this->get_merchant_account($smart2payCurrencies);

                switch (strtolower($smart2pay_order_info_row['smart2pay_payment_method'])) {
                    case "wallie":
                        $return_status_id = "";
                        $submit_xml = '<?xml version="1.0" encoding="utf-8"?>
										<TransactionStatusRequest xmlns="http://europay.smart2pay.com/message" version="1.0">
											<MID>' . $this->smart2pay_id . '</MID>
											<Transaction>
		    									<TrxID>' . $smart2pay_order_info_row["smart2pay_transaction_id"] . '</TrxID>
		  									</Transaction>
		                    		    </TransactionStatusRequest>';
                        $submit_xml = str_replace('<', '&lt;', $submit_xml);
                        $submit_xml = str_replace('>', '&gt;', $submit_xml);

                        $XMLRequest = '<?xml version="1.0" encoding="utf-8"?>';
                        $XMLRequest .= '<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
			        					xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">';
                        $XMLRequest .= '<soap:Body>';
                        $XMLRequest .= '<TransactionStatus xmlns="http://europay.smart2pay.com">';
                        $XMLRequest .= '<XMLStatusRequest>' . $submit_xml . '</XMLStatusRequest>';
                        $XMLRequest .= '</TransactionStatus>';
                        $XMLRequest .= '</soap:Body>';
                        $XMLRequest .= '</soap:Envelope>';

                        $data_string = $XMLRequest;
                        $action_url = $this->form_action_url;
                        $return_result = $this->curl_connect($data_string, "TransactionStatus", $action_url);

                        require_once(DIR_WS_CLASSES . 'ogm_xml_to_ary.php');
                        $this->xml_array_obj = new ogm_xml_to_ary($return_result, 'content');

                        $xml_array = $this->xml_array_obj->get_ogm_xml_to_ary();

                        $return_error_code = (isset($xml_array['soap:Envelope']['_c']['soap:Body']['_c']['TransactionStatusResponse']['_c']['TransactionStatusResult']['_c']['TransactionStatusResponse']['_c']['ErrorCode']['_v']) ? $xml_array['soap:Envelope']['_c']['soap:Body']['_c']['TransactionStatusResponse']['_c']['TransactionStatusResult']['_c']['TransactionStatusResponse']['_c']['ErrorCode']['_v'] : "");
                        $return_error_message = (isset($xml_array['soap:Envelope']['_c']['soap:Body']['_c']['TransactionStatusResponse']['_c']['TransactionStatusResult']['_c']['TransactionStatusResponse']['_c']['ErrorMessage']['_v']) ? $xml_array['soap:Envelope']['_c']['soap:Body']['_c']['TransactionStatusResponse']['_c']['TransactionStatusResult']['_c']['TransactionStatusResponse']['_c']['ErrorMessage']['_v'] : "");
                        $return_trxid = (isset($xml_array['soap:Envelope']['_c']['soap:Body']['_c']['TransactionStatusResponse']['_c']['TransactionStatusResult']['_c']['TransactionStatusResponse']['_c']['Transaction']['_c']['TrxID']['_v']) ? $xml_array['soap:Envelope']['_c']['soap:Body']['_c']['TransactionStatusResponse']['_c']['TransactionStatusResult']['_c']['TransactionStatusResponse']['_c']['Transaction']['_c']['TrxID']['_v'] : "");
                        $return_mid = (isset($xml_array['soap:Envelope']['_c']['soap:Body']['_c']['TransactionStatusResponse']['_c']['TransactionStatusResult']['_c']['TransactionStatusResponse']['_c']['MID']['_v']) ? $xml_array['soap:Envelope']['_c']['soap:Body']['_c']['TransactionStatusResponse']['_c']['TransactionStatusResult']['_c']['TransactionStatusResponse']['_c']['MID']['_v'] : "");
                        $return_status = (isset($xml_array['soap:Envelope']['_c']['soap:Body']['_c']['TransactionStatusResponse']['_c']['TransactionStatusResult']['_c']['TransactionStatusResponse']['_c']['Transaction']['_c']['Status']['_v']) ? $xml_array['soap:Envelope']['_c']['soap:Body']['_c']['TransactionStatusResponse']['_c']['TransactionStatusResult']['_c']['TransactionStatusResponse']['_c']['Transaction']['_c']['Status']['_v'] : "");

                        if (tep_not_null($return_error_code)) {
                            $smart2pay_status_history_data_sql['smart2pay_description'] = tep_db_prepare_input('Error Code - ' . $return_error_code . "\n" . $return_error_message);
                            tep_db_perform(TABLE_SMART2PAY_STATUS_HISTORY, $smart2pay_status_history_data_sql);
                            return;
                        }
                        break;

                    case "paysafecard":
                    case "ukash":
                    case 'mercadopago':
                    case "gluepay":
                        // Populate GET_STATUS Request Hash
                        $str = strtolower("get_status" . $this->smart2pay_id . $this->merchant_server_ip_address . $smart2pay_order_info_row['smart2pay_transaction_id'] . $this->smart2pay_key);
                        $hash = bin2hex(md5($str, true));

                        $submit_xml = '<?xml version="1.0" encoding="utf-8"?>
										<Request version="1.0" xmlns="http://europay.smart2pay.com/message">
										    <Action>GET_STATUS</Action>
										    <MID>' . $this->smart2pay_id . '</MID>
										    <PaymentMethod/>
										    <IPAddress>' . $this->merchant_server_ip_address . '</IPAddress>
										    <Hash>' . $hash . '</Hash>
										    <Details>
										        <TRXID>' . $smart2pay_order_info_row["smart2pay_transaction_id"] . '</TRXID>
										    </Details>
										</Request>';

                        $submit_xml = str_replace('<', '&lt;', $submit_xml);
                        $submit_xml = str_replace('>', '&gt;', $submit_xml);

                        $XMLRequest = '<?xml version="1.0" encoding="utf-8"?>';
                        $XMLRequest .= '<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
			        					xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">';
                        $XMLRequest .= '<soap:Body>';
                        $XMLRequest .= '<SubmitRequest xmlns="http://europay.smart2pay.com">';
                        $XMLRequest .= '<request>' . $submit_xml . '</request>';
                        $XMLRequest .= '</SubmitRequest>';
                        $XMLRequest .= '</soap:Body>';
                        $XMLRequest .= '</soap:Envelope>';

                        $data_string = $XMLRequest;
                        $return_result = $this->curl_connect($data_string, "SubmitRequest", $this->form_merchant_url);

                        require_once(DIR_WS_CLASSES . 'ogm_xml_to_ary.php');
                        $this->xml_array_obj = new ogm_xml_to_ary($return_result, 'content');

                        $xml_array = $this->xml_array_obj->get_ogm_xml_to_ary();

                        $return_error_code = (isset($xml_array['soap:Envelope']['_c']['soap:Body']['_c']['SubmitRequestResponse']['_c']['SubmitRequestResult']['_c']['Response']['_c']['Details']['_c']['ReturnCode']['_v']) ? $xml_array['soap:Envelope']['_c']['soap:Body']['_c']['SubmitRequestResponse']['_c']['SubmitRequestResult']['_c']['Response']['_c']['Details']['_c']['ReturnCode']['_v'] : "");
                        $return_trxid = (isset($xml_array['soap:Envelope']['_c']['soap:Body']['_c']['SubmitRequestResponse']['_c']['SubmitRequestResult']['_c']['Response']['_c']['Details']['_c']['TRXID']['_v']) ? $xml_array['soap:Envelope']['_c']['soap:Body']['_c']['SubmitRequestResponse']['_c']['SubmitRequestResult']['_c']['Response']['_c']['Details']['_c']['TRXID']['_v'] : "");
                        $return_mid = (isset($xml_array['soap:Envelope']['_c']['soap:Body']['_c']['SubmitRequestResponse']['_c']['SubmitRequestResult']['_c']['Response']['_c']['MID']['_v']) ? $xml_array['soap:Envelope']['_c']['soap:Body']['_c']['SubmitRequestResponse']['_c']['SubmitRequestResult']['_c']['Response']['_c']['MID']['_v'] : "");
                        $return_status_id = (isset($xml_array['soap:Envelope']['_c']['soap:Body']['_c']['SubmitRequestResponse']['_c']['SubmitRequestResult']['_c']['Response']['_c']['Details']['_c']['StatusID']['_v']) ? $xml_array['soap:Envelope']['_c']['soap:Body']['_c']['SubmitRequestResponse']['_c']['SubmitRequestResult']['_c']['Response']['_c']['Details']['_c']['StatusID']['_v'] : "");
                        $return_hash = (isset($xml_array['soap:Envelope']['_c']['soap:Body']['_c']['SubmitRequestResponse']['_c']['SubmitRequestResult']['_c']['Response']['_c']['Hash']['_v']) ? $xml_array['soap:Envelope']['_c']['soap:Body']['_c']['SubmitRequestResponse']['_c']['SubmitRequestResult']['_c']['Response']['_c']['Hash']['_v'] : "");

                        // Populate GET_STATUS Response Hash
                        if (tep_not_null($return_error_code)) {
                            $err_msg = $this->get_error_message($return_error_code);
                            $smart2pay_status_history_data_sql['smart2pay_description'] = tep_db_prepare_input('Error Code - ' . $return_error_code . "\n" . $err_msg);
                            tep_db_perform(TABLE_SMART2PAY_STATUS_HISTORY, $smart2pay_status_history_data_sql);
                            return;
                        } else {
                            $str = strtolower("get_status" . $this->smart2pay_id . $this->merchant_server_ip_address . $return_trxid . $return_status_id . $this->smart2pay_key);
                            $response_hash = bin2hex(md5($str, true));

                            if ($response_hash !== strtolower($return_hash)) {
                                $smart2pay_status_history_data_sql['smart2pay_description'] = tep_db_prepare_input('Error: Hash Not Match (' . $response_hash . ' != ' . $return_hash . ')');
                                tep_db_perform(TABLE_SMART2PAY_STATUS_HISTORY, $smart2pay_status_history_data_sql);
                                return;
                            }
                        }
                        break;
                }
            }

            if (tep_not_null($return_mid)) {
                $smart2pay_check_exist_select_sql = "	SELECT smart2pay_order_id
														FROM " . TABLE_SMART2PAY . " 
														WHERE smart2pay_transaction_id = '" . tep_db_input($return_trxid) . "'";
                $smart2pay_check_exist_result_sql = tep_db_query($smart2pay_check_exist_select_sql);
                if ($smart2pay_check_exist_row = tep_db_fetch_array($smart2pay_check_exist_result_sql)) {
                    if ($return_mid != $this->smart2pay_id) {
                        $smart2pay_status_history_data_sql['smart2pay_description'] = tep_db_prepare_input('Error: Merchant ID not matched (' . $return_mid . ' != ' . $this->smart2pay_id . ')');
                    } else {
                        $authentication_flag = true;
                    }
                } else {
                    $smart2pay_status_history_data_sql['smart2pay_description'] = tep_db_prepare_input('Error: Transaction ID not found (' . $return_trxid . ')');
                }
            } else {
                $smart2pay_status_history_data_sql['smart2pay_description'] = tep_db_prepare_input("Error: Return Empty Value");
            }

            if ($authentication_flag) {
                // Set to log
                if (isset($return_status) && $return_status != "") {
                    $return_status_id = $this->get_status_id($return_status);
                }

                if ($return_status_id == '2') {
                    $this->check_trans_status_flag = true;
                }

                $smart2pay_status_history_data_sql['smart2pay_status_id'] = (int) $return_status_id;
                $smart2pay_status_history_data_sql['smart2pay_description'] = tep_db_prepare_input("Transaction: OK");

                $smart2pay_order_data_array = array('smart2pay_merchant_id' => tep_db_prepare_input($return_mid),
                    'smart2pay_order_id' => (int) $order_id,
                    'smart2pay_transaction_id' => tep_db_prepare_input($return_trxid),
                    'smart2pay_status_id' => (int) $return_status_id
                );

                tep_db_perform(TABLE_SMART2PAY, $smart2pay_order_data_array, 'update', " smart2pay_order_id = '" . $order_id . "' ");
                $smart2pay_order_data_array['check_authentication_flag'] = 1;
            } else {
                $smart2pay_order_data_array['check_authentication_flag'] = 0;
            }
            tep_db_perform(TABLE_SMART2PAY_STATUS_HISTORY, $smart2pay_status_history_data_sql);
        } else {
            $smart2pay_status_history_data_sql['smart2pay_description'] = tep_db_prepare_input("Error: Transaction ID not Found");
            tep_db_perform(TABLE_SMART2PAY_STATUS_HISTORY, $smart2pay_status_history_data_sql);
        }

        return $smart2pay_order_data_array;
    }

    function output_error() {
        return false;
    }

    function check() {
        if (!isset($this->_check)) {
            $payment_methods_select_sql = "	SELECT smart2pay_keys_id
											FROM " . TABLE_PAYMENT_METHODS . " as pm
											WHERE pm.payment_methods_id = '" . (int) $this->payment_methods_id . "'";
            $payment_methods_result_sql = tep_db_query($payment_methods_select_sql);
            $this->_check = tep_db_num_rows($payment_methods_result_sql);
        }
        return $this->_check;
    }

    function is_processed_order($order_id) {
        $payment_status_select_sql = "	SELECT smart2pay_status_id
										FROM " . TABLE_SMART2PAY . " 
										WHERE smart2pay_order_id = '" . (int) $order_id . "'";
        $payment_status_result_sql = tep_db_query($payment_status_select_sql);

        if ($payment_status_row = tep_db_fetch_array($payment_status_result_sql)) {
            if ($payment_status_row['smart2pay_status_id'] == 2) {
                return true;
            }
        }
        return false;
    }

    function load_pm_setting($language_id = 1, $pm_id = '') {
        $pm_setting_array = array();
        //load value from DB

        if (tep_not_null($pm_id)) {
            $payment_method_id = $pm_id;

            $payment_methods_parent_id_select_sql = "	SELECT payment_methods_parent_id 
														FROM " . TABLE_PAYMENT_METHODS . " 
														WHERE payment_methods_id = '" . (int) $pm_id . "'";
            $payment_methods_parent_id_result_sql = tep_db_query($payment_methods_parent_id_select_sql);
            $payment_methods_parent_id_row = tep_db_fetch_array($payment_methods_parent_id_result_sql);

            $payment_gateway_id = $payment_methods_parent_id_row['payment_methods_parent_id'];
        } else {
            $payment_method_id = $this->payment_methods_id;
            $payment_gateway_id = $this->payment_methods_parent_id;
        }

        $payment_configuration_info_select_sql = "	SELECT pci.payment_configuration_info_key, pcid.payment_configuration_info_value
													FROM " . TABLE_PAYMENT_CONFIGURATION_INFO . " as pci
													LEFT JOIN " . TABLE_PAYMENT_CONFIGURATION_INFO_DESCRIPTION . " as pcid 
														ON pci.payment_configuration_info_id = pcid.payment_configuration_info_id
															AND pcid.languages_id = '" . (int) $language_id . "'
													WHERE pci.payment_methods_id = '" . (int) $payment_method_id . "'
													ORDER BY pci.payment_configuration_info_sort_order";
        $payment_configuration_info_result_sql = tep_db_query($payment_configuration_info_select_sql);
        if (tep_db_num_rows($payment_configuration_info_result_sql)) {
            while ($payment_configuration_info_row = tep_db_fetch_array($payment_configuration_info_result_sql)) {
                $pm_setting_array[$payment_configuration_info_row['payment_configuration_info_key']] = $payment_configuration_info_row['payment_configuration_info_value'];
            }
        } else if ((int) $payment_gateway_id > 0) { //load value from PG
            $payment_configuration_info_select_sql = "	SELECT pci.payment_configuration_info_key, pcid.payment_configuration_info_value
														FROM " . TABLE_PAYMENT_CONFIGURATION_INFO . " as pci
														LEFT JOIN " . TABLE_PAYMENT_CONFIGURATION_INFO_DESCRIPTION . " as pcid 
															ON pci.payment_configuration_info_id = pcid.payment_configuration_info_id
																AND pcid.languages_id = '" . (int) $language_id . "'
														WHERE pci.payment_methods_id = '" . (int) $payment_gateway_id . "'
														ORDER BY pci.payment_configuration_info_sort_order";
            $payment_configuration_info_result_sql = tep_db_query($payment_configuration_info_select_sql);
            while ($payment_configuration_info_row = tep_db_fetch_array($payment_configuration_info_result_sql)) {
                $pm_setting_array[$payment_configuration_info_row['payment_configuration_info_key']] = $payment_configuration_info_row['payment_configuration_info_value'];
            }
        }
        return $pm_setting_array;
    }

    function get_merchant_account($selected_currency) {
        $payment_methods_instance_id = 0;
        $payment_gateway_instance_select_sql = "SELECT pmi.payment_methods_instance_id, pmi.payment_methods_instance_follow_default, pmi.currency_code 
				 								FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " as pmi
				 								WHERE pmi.payment_methods_id = '" . (int) $this->payment_methods_id . "'
				 								LIMIT 1";
        $payment_gateway_instance_result_sql = tep_db_query($payment_gateway_instance_select_sql);
        if (tep_db_num_rows($payment_gateway_instance_result_sql)) {
            $payment_gateway_instance_select_sql = "SELECT pmi.payment_methods_instance_id, pmi.payment_methods_instance_follow_default
					 								FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " as pmi
					 								WHERE pmi.currency_code = '" . tep_db_input($selected_currency) . "' 
					 									AND pmi.payment_methods_id = '" . (int) $this->payment_methods_id . "'";
            $payment_gateway_instance_result_sql = tep_db_query($payment_gateway_instance_select_sql);
            if ($payment_gateway_instance_row = tep_db_fetch_array($payment_gateway_instance_result_sql)) {
                if ((int) $payment_gateway_instance_row['payment_methods_instance_follow_default'] > 0) { // Follow Default Currency Setting
                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_follow_default'];
                } else { // Has own setting
                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_id'];
                }
            }
        } else {
            $payment_gateway_instance_select_sql = "SELECT pmi.payment_methods_instance_id, pmi.payment_methods_instance_follow_default
					 								FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " as pmi
					 								INNER JOIN " . TABLE_PAYMENT_METHODS . " as pm
					 									ON pm.payment_methods_parent_id = pmi.payment_methods_id
					 								WHERE pmi.currency_code = '" . tep_db_input($selected_currency) . "' 
					 									AND pm.payment_methods_id = '" . (int) $this->payment_methods_id . "'";
            $payment_gateway_instance_result_sql = tep_db_query($payment_gateway_instance_select_sql);
            if ($payment_gateway_instance_row = tep_db_fetch_array($payment_gateway_instance_result_sql)) {
                if ((int) $payment_gateway_instance_row['payment_methods_instance_follow_default'] > 0) {
                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_follow_default'];
                } else {
                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_id'];
                }
            }
        }

        $payment_gateway_instance_setting_select_sql = "SELECT pmis.payment_methods_instance_setting_key, pmis.payment_methods_instance_setting_value 
						 								FROM " . TABLE_PAYMENT_METHODS_INSTANCE_SETTING . " as pmis
						 								WHERE pmis.payment_methods_instance_id = '" . (int) $payment_methods_instance_id . "'";
        $payment_gateway_instance_setting_result_sql = tep_db_query($payment_gateway_instance_setting_select_sql);
        while ($payment_gateway_instance_setting_row = tep_db_fetch_array($payment_gateway_instance_setting_result_sql)) {
            switch ($payment_gateway_instance_setting_row['payment_methods_instance_setting_key']) {
                case 'MODULE_PAYMENT_SMART2PAY_ID': //Smart2Pay ID
                    $this->smart2pay_id = $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];
                    break;
                case 'MODULE_PAYMENT_SMART2PAY_KEY': //Smart2Pay Key
                    $this->smart2pay_key = $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];
                    break;
                case 'MODULE_PAYMENT_SMART2PAY_EXPONENT':
                    $this->exponent = $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];
                    break;
            }
        }
    }

    function install() {
        $install_configuration_flag = true;
        if ($this->payment_methods_id > 0) {
            if ($this->receive_status != 1) {
                $update_receive_status_sql_data = array("payment_methods_receive_status" => 1);
                tep_db_perform(TABLE_PAYMENT_METHODS, $update_receive_status_sql_data, 'update', "payment_methods_id = '" . (int) $this->payment_methods_id . "'");
            } else {
                $install_configuration_flag = false;
            }
        } else {
            $install_payment_gateway_data_sql = array('payment_methods_code' => $this->code,
                'payment_methods_send_status' => 0,
                'payment_methods_title' => $this->code,
                'payment_methods_types_id' => 0,
                'payment_methods_parent_id' => 0,
                'payment_methods_legend_color' => '#FFCC44',
                'payment_methods_sort_order' => 5,
                'payment_methods_filename' => $this->filename,
                'payment_methods_logo' => '',
                'date_added' => 'now()',
                'last_modified' => 'now()',
                'payment_methods_receive_status' => 1,
                'payment_methods_receive_status_mode' => 0
            );
            tep_db_perform(TABLE_PAYMENT_METHODS, $install_payment_gateway_data_sql);
            $this->payment_methods_id = tep_db_insert_id();
        }
        if ($install_configuration_flag) {
            $install_key_array = array();
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Set Order Status',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_SMART2PAY_ORDER_STATUS_ID',
                    'payment_configuration_info_description' => 'Set the status of orders made with this payment module to this value.',
                    'payment_configuration_info_sort_order' => '1335',
                    'set_function' => 'tep_cfg_pull_down_order_statuses(',
                    'use_function' => 'tep_get_order_status_name',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => '1',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Set Order\'s "Processing" Status',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_SMART2PAY_PROCESSING_STATUS_ID',
                    'payment_configuration_info_description' => 'Set the initial processing status of orders made with this payment module to this value.',
                    'payment_configuration_info_sort_order' => '1340',
                    'set_function' => 'tep_cfg_pull_down_order_statuses(',
                    'use_function' => 'tep_get_order_status_name',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => '7',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Payment Message',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_SMART2PAY_MESSAGE',
                    'payment_configuration_info_description' => 'Payment message will show up during checkout process (smart2pay).',
                    'payment_configuration_info_sort_order' => '1350',
                    'set_function' => 'tep_cfg_textarea(',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'Thank you for shopping at OffGamers.com.',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Payment Email Message',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_SMART2PAY_EMAIL_MESSAGE',
                    'payment_configuration_info_description' => 'This message will show up in order confirmation e-mail.',
                    'payment_configuration_info_sort_order' => '1360',
                    'set_function' => 'tep_cfg_textarea(',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'Thank you for shopping at OffGamers.com.',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Confirm Complete (in days)',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_SMART2PAY_CONFIRM_COMPLETE',
                    'payment_configuration_info_description' => 'The number of days in which an order made by this payment method is confirm completed.',
                    'payment_configuration_info_sort_order' => '1265',
                    'set_function' => '',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => '0',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Test Mode',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_SMART2PAY_TEST_MODE',
                    'payment_configuration_info_description' => 'Testing Mode?',
                    'payment_configuration_info_sort_order' => '100',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Require Address Information',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_SMART2PAY_MANDATORY_ADDRESS_FIELD',
                    'payment_configuration_info_description' => 'Set address field as required info during checkout.',
                    'payment_configuration_info_sort_order' => '1400',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );
            foreach ($install_key_array as $data) {
                tep_db_perform(TABLE_PAYMENT_CONFIGURATION_INFO, $data['info']);
                $payment_conf_id = tep_db_insert_id();

                $data['desc']['payment_configuration_info_id'] = (int) $payment_conf_id;
                tep_db_perform(TABLE_PAYMENT_CONFIGURATION_INFO_DESCRIPTION, $data['desc']);
            }
        }
        return 1;
    }

    function configuration_information_keys() {
        return array('MODULE_PAYMENT_SMART2PAY_ORDER_STATUS_ID',
            'MODULE_PAYMENT_SMART2PAY_PROCESSING_STATUS_ID',
            'MODULE_PAYMENT_SMART2PAY_CONFIRM_COMPLETE',
            'MODULE_PAYMENT_SMART2PAY_TEST_MODE',
            'MODULE_PAYMENT_SMART2PAY_MANDATORY_ADDRESS_FIELD');
    }

    function multi_lang_configuration_info_keys() {
        return array('MODULE_PAYMENT_SMART2PAY_MESSAGE',
            'MODULE_PAYMENT_SMART2PAY_EMAIL_MESSAGE');
    }

    function merchant_information_keys() {
        include_once(DIR_WS_CLASSES . "payment_methods.php");

        return array(
            'MODULE_PAYMENT_SMART2PAY_ID' => array('field' => 'text', 'mapping' => 'MODULE_PAYMENT_SMART2PAY_LNG_ID'),
            'MODULE_PAYMENT_SMART2PAY_KEY' => array('field' => 'text', 'mapping' => 'MODULE_PAYMENT_SMART2PAY_LNG_KEY'),
            'MODULE_PAYMENT_SMART2PAY_EXPONENT' => array('field' => 'text', 'mapping' => 'MODULE_PAYMENT_SMART2PAY_LNG_EXPONENT', 'default' => '2'),
            'MODULE_PAYMENT_TAX' => array("field" => 'select', 'mapping' => "MODULE_PAYMENT_TAX", "select" => payment_methods::get_tax_option())
        );
    }

    function curl_connect($data_string, $api_method, $action_url) {

        $curl_handle = curl_init();

        curl_setopt($curl_handle, CURLOPT_URL, $action_url);
        curl_setopt($curl_handle, CURLOPT_HEADER, 0);
        curl_setopt($curl_handle, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($curl_handle, CURLOPT_POST, 1);
        curl_setopt($curl_handle, CURLOPT_POSTFIELDS, $data_string);

        curl_setopt($curl_handle, CURLOPT_SSL_VERIFYPEER, FALSE);
        curl_setopt($curl_handle, CURLOPT_SSL_VERIFYHOST, 1);

        if ($this->connect_via_proxy) {
            curl_setopt($curl_handle, CURLOPT_PROXY, 'http://*************:3128');
            curl_setopt($curl_handle, CURLOPT_PROXYUSERPWD, 'devbackend:devonly');
        }

        $header[] = "POST /Process.asmx HTTP/1.1";
        $header[] = "Host: europay.smart2pay.com";
        $header[] = "Content-Type: text/xml; charset=utf-8";
        $header[] = "Content-Length: " . strlen($data_string); //."\r\n";
        $header[] = 'SOAPAction: "http://europay.smart2pay.com/' . $api_method . '"';

        curl_setopt($curl_handle, CURLOPT_HTTPHEADER, $header);
        $result = curl_exec($curl_handle);
        curl_close($curl_handle);

        return $result;
    }

    function get_orders_payment_info_file() {
        return DIR_FS_CATALOG_MODULES . 'payment/smart2pay/admin/orders.inc.php';
    }

    function get_orders_list_payment_info_file() {
        return DIR_FS_CATALOG_MODULES . 'payment/smart2pay/admin/orders_list.inc.php';
    }

    function get_ipn_class_file() {
        return DIR_FS_CATALOG_MODULES . 'payment/smart2pay/classes/smart2pay_ipn_class.php';
    }

    function get_status_id($status) {
        switch (strtolower($status)) {
            case 'open' : return '1';
                break;
            case 'success' : return '2';
                break;
            case 'cancelled' : return '3';
                break;
            case 'failure' : return '4';
                break;
            case 'expired' : return '5';
                break;
            case 'exception' : return '14';
                break;
            default :
                return $status;
                break;
        }
    }

    function get_status($status_id) {
        switch ($status_id) {
            case '0' : return 'Unknown';
                break;
            case '1' : return 'Open';
                break;
            case '2' : return 'Success';
                break;
            case '3' : return 'Cancelled';
                break;
            case '4' : return 'Failed';
                break;
            case '5' : return 'Expired';
                break;
            case '14' : return 'Exception';
                break;
            default :
                return $status_id;
                break;
        }
    }

    function get_error_message($error_code) {
        switch ($error_code) {
            case '1' : return 'Xml has an invalid general format (e.g. IPAddress element missing)';
                break;
            case '2' : return 'Xml is not well formed';
                break;
            case '4' : return 'Merchant ID does not exist';
                break;
            case '6' : return 'The merchant is locked in Europay system. Please contact the technical support operators.';
                break;
            case '7' : return 'The transaction could not be created in Europay system';
                break;
            case '9' : return 'Invalid transaction ID (does not exist in Europay system)';
                break;
            case '13' : return 'The merchant is locked for the given payment method';
                break;
            case '14' : return 'An IP error has occurred (the IP from the merchant request does not match the IP taken from Request)';
                break;
            case '15' : return 'The IP from the merchant request is not in the merchant’s white list of IPs.';
                break;
            case '16' : return 'The hash check has failed – the received hash and the calculated hash do not match';
                break;
            case '19' : return 'A general error has occurred. Please contact the technical support operators.';
                break;
            default :
                return $error_code . ' (unknown)';
                break;
        }
    }

}

?>