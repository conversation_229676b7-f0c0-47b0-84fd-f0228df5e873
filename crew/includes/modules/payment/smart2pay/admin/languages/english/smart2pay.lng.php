<?
define('TABLE_HEADING_SMART2PAY_DATE', 'History Date');
define('TABLE_HEADING_SMART2PAY_STATUS', 'Status');
define('TABLE_HEADING_SMART2PAY_DESCRIPTION', 'Description');
define('TABLE_HEADING_SMART2PAY_CHANGED_BY', 'Changed By');

//Define Merchant Key Language
define('MODULE_PAYMENT_SMART2PAY_LNG_ID', 'Smart2Pay ID');
define('MODULE_PAYMENT_SMART2PAY_LNG_KEY', 'Smart2Pay Key');
define('MODULE_PAYMENT_SMART2PAY_LNG_EXPONENT', 'Smart2Pay Exponent');
define('MODULE_PAYMENT_TAX', 'Tax');

define('ENTRY_SMART2PAY_TRANSACTION_ID', 'Transaction ID');
define('ENTRY_SMART2PAY_CURRENCY', 'Currency');
define('ENTRY_SMART2PAY_AMOUNT', 'Amount');
define('ENTRY_SMART2PAY_STATUS', 'Payment Status');
define('ENTRY_SMART2PAY_PAYMENT_METHOD','Payment Method');
?>