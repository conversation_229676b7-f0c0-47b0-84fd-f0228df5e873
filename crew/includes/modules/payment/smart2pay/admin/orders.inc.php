<?
/*
  	$Id: orders.inc.php,v 1.1 2011/05/25 09:50:11 dennis.chang Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2005 SKC Venture
  	
  	Released under the GNU General Public License
*/

include_once(DIR_FS_CATALOG_MODULES . 'payment/smart2pay/admin/languages/' . $language . '/smart2pay.lng.php');

$order_smart2pay_cancel_payment_permission = tep_admin_files_actions(FILENAME_ORDERS, 'ORDER_SMART2PAY_CANCEL_PAYMENT');
$order_smart2pay_check_payment_permission = tep_admin_files_actions(FILENAME_ORDERS, 'ORDER_SMART2PAY_PAYMENT');

$smart2pay_trans_info_select_sql = "	SELECT * 
										FROM " . TABLE_SMART2PAY . "
										WHERE smart2pay_order_id = '" . (int)$oID  . "'";
$smart2pay_trans_info_result_sql = tep_db_query($smart2pay_trans_info_select_sql);

$smart2pay_trans_history_select_sql = " SELECT * 
									    FROM " . TABLE_SMART2PAY_STATUS_HISTORY . " 
									    WHERE smart2pay_order_id='" . (int)$oID . "'
									    ORDER BY smart2pay_status_history_id";
$smart2pay_trans_history_result_sql= tep_db_query($smart2pay_trans_history_select_sql);

?>
<table border="0" width="100%" cellspacing="0" cellpadding="2">
<? ob_start(); 

?>
	<tr>
		<td class="main">
      		<table border="0" width="100%" cellspacing="0" cellpadding="2">
      			<tr>
      				<td class="main" width="12%" nowrap><b><?=ENTRY_PAYMENT_METHOD?></b></td>
      				<td class="main">
      					<div>
      						<? if((int)$order->info['payment_methods_id']>0) { ?><div class="<?=((int)$payment_info_array[$order->info['payment_methods_id']]->confirm_complete_days>0?'rp_icon':'nrp_icon')?>"><b><?=((int)$payment_info_array[$order->info['payment_methods_id']]->confirm_complete_days>0?'RP':'NRP')?></b></div> <? } ?>
      						<div style="width:30px; height:15px; background-color:<?=$payment_module_info['display_colour']?>; float:left; vertical-align:top; text-align:center;">&nbsp;</div>
      						<div style="vertical-align: top">
      							&nbsp;&nbsp;
<?
	if ($view_payment_details_permission) {
		if (tep_not_null($last_payment_method) && $last_payment_method['payment_methods_id'] != $order->info['payment_methods_id']) {
			echo '<span class="redIndicator">'.(tep_not_null($payment_module_info['payment_methods_parent_title'])?$payment_module_info['payment_methods_parent_title'] . ' - ' :'') . $payment_module_info['payment_methods_title'].'</span>';
		} else {
			echo (tep_not_null($payment_module_info['payment_methods_parent_title'])?$payment_module_info['payment_methods_parent_title'] . ' - ' :'') . $payment_module_info['payment_methods_title'];
		}
		
		if (tep_not_null($last_payment_method) && $last_payment_method['payment_methods_id'] != $order->info['payment_methods_id']) {
			echo '&nbsp;&nbsp;<span class="redIndicator">(Last Payment Method: ' . (tep_not_null($last_payment_method['parent_payment_methods_title'])? $last_payment_method['parent_payment_methods_title'] . ' - ' : '' ) . $last_payment_method['payment_methods_title'] . ')</span>';
		}
	} else {
		echo (tep_not_null($payment_module_info['payment_methods_parent_title'])?$payment_module_info['payment_methods_parent_title'] . ' - ' :'') . $payment_module_info['payment_methods_title'];
	}
?>
							</div>
      					</div>
      				</td>
				</tr>
			</table>
		</td>
	</tr>
<?
$payment_method_title_info = ob_get_contents();
ob_end_clean();

if (tep_db_num_rows($smart2pay_trans_info_result_sql) || tep_db_num_rows($smart2pay_trans_history_result_sql)) {
	
	echo $payment_method_title_info;
	
	if (!$view_payment_details_permission) {
		;
	} else {
		
		$smart2pay_trans_info_row = tep_db_fetch_array($smart2pay_trans_info_result_sql);

?>
  	<tr>
    	<td class="main">
      		<table border="0" width="100%" cellspacing="0" cellpadding="2">
      			<tr valign="top">
        			<td width="39%" nowrap>
              			<table border="0" cellspacing="0" cellpadding="2">
              				<tr>
                				<td class="main" valign="top" nowrap><b><?=ENTRY_SMART2PAY_TRANSACTION_ID?></b>:&nbsp;</td>
                				<td class="main" nowrap><?=$smart2pay_trans_info_row['smart2pay_transaction_id']?></td>
              				</tr>
              				<tr>
                				<td class="main" valign="top" nowrap><b><?=ENTRY_SMART2PAY_PAYMENT_METHOD?></b>:&nbsp;</td>
                				<td class="main" nowrap><?=$smart2pay_trans_info_row['smart2pay_payment_method']?></td>
              				</tr>
              				<tr>
                				<td class="main" valign="top" nowrap><b><?=ENTRY_SMART2PAY_STATUS?></b>:&nbsp;</td>
                				<td class="main" nowrap><?=$payment_info_array[$order->info['payment_methods_id']]->pm_object->get_status($smart2pay_trans_info_row["smart2pay_status_id"])?></td>
              				</tr>
              				<tr>
			    				<td class="main" valign="top" nowrap><b><?=ENTRY_SMART2PAY_CURRENCY?></b>:&nbsp;</td>
			    				<td class="main" nowrap>
			    				<?
			    					if (strtolower($smart2pay_trans_info_row["smart2pay_currency"]) == strtolower($order->info['currency'])) {
			    						echo $smart2pay_trans_info_row['smart2pay_currency'];
			    					} else {
			    						echo '<span class="redIndicator">'.$smart2pay_trans_info_row["smart2pay_currency"].'<br><span class="smallText">Does not match purchase currency.</span></span>';
			    					}
    							?>
    							</td>
			  				</tr>
              				<tr>
                				<td class="main" valign="top" nowrap><b><?=ENTRY_SMART2PAY_AMOUNT?></b>:&nbsp;</td>
                				<td class="main" nowrap>
								<?
									$gross_display_text = number_format($smart2pay_trans_info_row["smart2pay_amount"], $currencies->currencies[$order->info["currency"]]['decimal_places'], $currencies->currencies[$order->info["currency"]]['decimal_point'], $currencies->currencies[$order->info["currency"]]['thousands_point']);
			    					if ($currencies->currencies[$order->info["currency"]]['symbol_left'].$gross_display_text.$currencies->currencies[$order->info["currency"]]['symbol_right'] != strip_tags($order->order_totals['ot_total']['text']) ) {
			    						$gross_display_text = '<span class="redIndicator">'.$gross_display_text.'<br><span class="smallText">Does not match purchase amount.</span></span>';
			    					}
			    					echo $gross_display_text;
								?>
                				</td>
              				</tr>
              			</table>
        			</td>
        			<td>
        				<table border="0" cellspacing="0" cellpadding="1">
        					<tr>
        						<td colspan="2">
									<table border="1" cellspacing="0" cellpadding="2">
		        						<tr>
		        							<td class="smallText" nowrap><b><?=TABLE_HEADING_SMART2PAY_DATE?></b></td>
		        							<td class="smallText" nowrap><b><?=TABLE_HEADING_SMART2PAY_STATUS?></b></td>
		        							<td class="smallText" nowrap><b><?=TABLE_HEADING_SMART2PAY_DESCRIPTION?></b></td>
		        							<td class="smallText" nowrap><b><?=TABLE_HEADING_SMART2PAY_CHANGED_BY?></b></td>
		        						</tr>
<?		while ($smart2pay_payment_status_history_info_row = tep_db_fetch_array($smart2pay_trans_history_result_sql)) {
        	echo ' 						<tr>
                							<td class="smallText" nowrap>'.$smart2pay_payment_status_history_info_row['smart2pay_date'].'</td>
                							<td class="smallText" nowrap>'.$payment_info_array[$order->info['payment_methods_id']]->pm_object->get_status($smart2pay_payment_status_history_info_row["smart2pay_status_id"]).'</td>
                							<td class="smallText" nowrap>'.($smart2pay_payment_status_history_info_row['smart2pay_description']).'</td>
                							<td class="smallText" nowrap>'.($smart2pay_payment_status_history_info_row['smart2pay_changed_by']).'</td>
                						</tr>';
     	}
?>
									</table>
								</td>
        					</tr>
        				</table>
        			</td>
  				</tr>
      		</table>
   		</td>
  	</tr>
<?
	}
}

echo '<tr>
		<td class="main">';
echo tep_draw_form('bb_status_check_form', FILENAME_ORDERS, tep_get_all_get_params(), 'post');
echo tep_draw_hidden_field('subaction', 'payment_action');
echo tep_draw_hidden_field('payment_action', 'check_trans_status');
echo '<input type="submit" name="BBCheckTransStatusBtn" value="Check Payment Status" title="Check Payment Status" class="inputButton">';
echo '</form>
		</td>
	  </tr>';
?>
</table>