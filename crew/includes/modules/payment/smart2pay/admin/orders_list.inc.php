<?
include_once(DIR_FS_CATALOG_MODULES . 'payment/smart2pay/admin/languages/' . $language . '/smart2pay.lng.php');

$smart2pay_trans_info_select_sql = "SELECT * FROM " . TABLE_SMART2PAY . " WHERE smart2pay_order_id = '" . tep_db_input($order_obj->orders_id) . "'";
$smart2pay_trans_info_result_sql= tep_db_query($smart2pay_trans_info_select_sql);
?>
<table border="0" width="100%" cellspacing="0" cellpadding="2">
	<tr>
		<td class="subInvoiceBoxHeading">
			<div style="border-bottom: 1px solid #996600; border-top: 1px solid #996600; padding: 2px;">Payment Details</div>
		</td>
	</tr>
<?
if ($smart2pay_trans_info_row = tep_db_fetch_array($smart2pay_trans_info_result_sql)) {
?>
	<tr>
    	<td class="main">
      		<table border="0" width="100%" cellspacing="0" cellpadding="0">
      			<tr valign="top">
        			<td width="50%">
              			<table width="100%" cellspacing="0" cellpadding="0">
              				<tr>
              					<td width="50%">
              						<table>
              							<tr>
              								<td class="invoiceRecords" valign="top" nowrap><?=ENTRY_SMART2PAY_TRANSACTION_ID?></td>
              								<td class="invoiceRecords" valign="top">:&nbsp;</td>
                							<td class="invoiceRecords" nowrap><?=$smart2pay_trans_info_row['smart2pay_transaction_id']?></td>
              							</tr>
              						</table>
              					</td>
              					<td>
              						<table>
              							<tr>
			                				<td class="invoiceRecords" valign="top" nowrap><?=ENTRY_SMART2PAY_PAYMENT_METHOD?></td>
			                				<td class="invoiceRecords" valign="top">:&nbsp;</td>
			                				<td class="invoiceRecords" nowrap><?=$smart2pay_trans_info_row['smart2pay_payment_method']?></td>
              							</tr>
              						</table>
              					</td>
              				</tr>
              				<tr>
              					<td width="50%">
              						<table>
              							<tr>
			                				<td class="invoiceRecords" valign="top" nowrap><?=ENTRY_SMART2PAY_STATUS?></td>
			                				<td class="invoiceRecords" valign="top">:&nbsp;</td>
			                				<td class="invoiceRecords" nowrap><?=$payment_methods_obj->payment_gateways_array[$order_obj->payment_methods_parent_id]->get_status($smart2pay_trans_info_row["smart2pay_status_id"])?></td>
              							</tr>
              						</table>
              					</td>
              				</tr>
              			</table>
        			</td>
      			</tr>
      		</table>
   		</td>
  	</tr>
<?
} else {
?>
	<tr>
		<td class="invoiceRecords">No further payment information is available.</td>
	</tr>
<?
}
?>