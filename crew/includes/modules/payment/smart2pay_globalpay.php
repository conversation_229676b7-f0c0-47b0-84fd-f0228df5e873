<?php

require_once('payment_gateway.php');
include_once(DIR_WS_CLASSES . 'curl.php');

class smart2pay_globalpay extends payment_gateway {

    public $code = 'smart2pay_globalpay', // will be overwritten by DB
            $check_trans_status_flag = false,
            $title = '',
            $filename = 'smart2pay_globalpay.php',
            $auto_cancel_period = 30, // In minutes
            $force_to_checkoutprocess = TRUE, // false : will force to post info to PG if has session order_logged

            $connect_via_proxy = FALSE,
            $has_ipn = TRUE,
            $payment_methods_id = 0,
            $payment_methods_parent_id = 0,
            $supportCurrencies;
	
    function smart2pay_globalpay($pm_id = '') {
        global $order, $languages_id, $default_languages_id;

        $this->title = $this->code;

        $payment_methods_select_sql = "	SELECT  payment_methods_parent_id, payment_methods_code, 
												payment_methods_types_id, 
												payment_methods_receive_status_mode, payment_methods_id, 
												payment_methods_title, payment_methods_sort_order, 
												payment_methods_receive_status, payment_methods_legend_color, 
												payment_methods_logo 
										FROM " . TABLE_PAYMENT_METHODS . "
										WHERE 1 ";
        if ((int) $pm_id > 0) {
            $payment_methods_select_sql .= "	AND payment_methods_id = '" . (int) $pm_id . "'";
        } else {
            $payment_methods_select_sql .= "	AND payment_methods_filename = '" . tep_db_input($this->filename) . "'";
        }

        $payment_methods_result_sql = tep_db_query($payment_methods_select_sql);
        if ($payment_methods_row = tep_db_fetch_array($payment_methods_result_sql)) {
            $pm_desc_title_select_sql = "	SELECT payment_methods_description_title 
											FROM " . TABLE_PAYMENT_METHODS_DESCRIPTION . " 
											WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "' 
												AND (IF (languages_id = '" . tep_db_input($languages_id) . "', 1, IF ((	SELECT COUNT(payment_methods_id) > 0 
																														FROM " . TABLE_PAYMENT_METHODS_DESCRIPTION . " 
																														WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "' 
																															AND languages_id = '" . (int) $languages_id . "'), 0, languages_id = '" . (int) $default_languages_id . "')))";
            $pm_desc_title_result_sql = tep_db_query($pm_desc_title_select_sql);
            $pm_desc_title_row = tep_db_fetch_array($pm_desc_title_result_sql);
            $this->display_title = $pm_desc_title_row['payment_methods_description_title'];
            $this->description = $this->display_title;


            $this->enabled = $payment_methods_row['payment_methods_receive_status_mode'];
            $this->payment_methods_id = (int) $payment_methods_row['payment_methods_id'];
            $this->payment_methods_parent_id = (int) $payment_methods_row['payment_methods_parent_id'];
            $this->code = $payment_methods_row['payment_methods_code'];
            $this->title = $payment_methods_row['payment_methods_title'];
            $this->sort_order = $payment_methods_row['payment_methods_sort_order'];
            $this->legend_display_colour = $payment_methods_row['payment_methods_legend_color'];


            $configuration_setting_array = $this->load_pm_setting();
            $this->order_status_id = $configuration_setting_array['MODULE_PAYMENT_SMART2PAY_GLOBALPAY_ORDER_STATUS_ID'];
            $this->processing_status_id = $configuration_setting_array['MODULE_PAYMENT_SMART2PAY_GLOBALPAY_PROCESSING_STATUS_ID'];
            $this->confirm_complete_days = $configuration_setting_array['MODULE_PAYMENT_SMART2PAY_GLOBALPAY_CONFIRM_COMPLETE'];
            $this->require_address_information = $configuration_setting_array['MODULE_PAYMENT_SMART2PAY_GLOBALPAY_MANDATORY_ADDRESS_FIELD'];
            $this->test_mode = ($configuration_setting_array['MODULE_PAYMENT_SMART2PAY_GLOBALPAY_TEST_MODE'] == 'True' ? true : false);
            $this->message = $configuration_setting_array['MODULE_PAYMENT_SMART2PAY_GLOBALPAY_MESSAGE'];
            $this->email_message = $configuration_setting_array['MODULE_PAYMENT_SMART2PAY_GLOBALPAY_EMAIL_MESSAGE'];
            $this->email_footer = str_replace(array("<br>", "<BR>", "\r\n"), array("\n", "\n", ''), $this->email_message);


            $this->supportCurrencies = $this->get_support_currencies($this->payment_methods_id);
            $this->order_processing_status = ((int) $this->processing_status_id > 0 ? $this->processing_status_id : 7 );
            $this->order_status = ( (int) $this->order_status_id > 0 ? $this->order_status_id : DEFAULT_ORDERS_STATUS_ID );


            if ($this->test_mode) {
                $this->form_action_url = "https://apitest.smart2pay.com"; // test url - get transaction id
                $this->checking_url = "https://apitest.smart2pay.com/GetStatus/KeyValue";
            } else {
                $this->form_action_url = "https://api.smart2pay.com";
                $this->checking_url = "https://api.smart2pay.com/GetStatus/KeyValue";
            }

            $this->return_url = (defined(FILENAME_CHECKOUT_PROCESS) ? tep_href_link(FILENAME_CHECKOUT_PROCESS, '', 'SSL') : '');
        }
    }

    function javascript_validation() {
        return FALSE;
    }

    function selection() {
        global $languages_id, $default_languages_id;

        $selection_array = array();

        $payment_methods_select_sql = "	SELECT payment_methods_id, payment_methods_code, payment_methods_types_id, payment_methods_logo, payment_methods_receive_status_mode 
                                        FROM " . TABLE_PAYMENT_METHODS . " 
                                        WHERE payment_methods_receive_status = 1 
                                                AND payment_methods_receive_status_mode <> 0 
                                                AND payment_methods_parent_id = '" . (int) $this->payment_methods_id . "' 
                                        ORDER BY payment_methods_sort_order";
        $payment_methods_result_sql = tep_db_query($payment_methods_select_sql);
        while ($payment_methods_row = tep_db_fetch_array($payment_methods_result_sql)) {
            $pm_desc_title_select_sql = "	SELECT payment_methods_description_title 
                                                FROM " . TABLE_PAYMENT_METHODS_DESCRIPTION . " 
                                                WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "' 
                                                        AND (IF (languages_id = '" . tep_db_input($languages_id) . "', 1, IF ((	SELECT COUNT(payment_methods_id) > 0 
																														FROM " . TABLE_PAYMENT_METHODS_DESCRIPTION . " 
																														WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "' 
																															AND languages_id = '" . (int) $languages_id . "'), 0, languages_id = 1)))";
            $pm_desc_title_result_sql = tep_db_query($pm_desc_title_select_sql);
            $pm_desc_title_row = tep_db_fetch_array($pm_desc_title_result_sql);

            $pm_array = $this->load_pm_setting(1, $payment_methods_row['payment_methods_id']);

            $selection_array[] = array('payment_gateway_code' => $this->code,
                'payment_methods_receive_status_mode' => $payment_methods_row['payment_methods_receive_status_mode'],
                'payment_methods_id' => $payment_methods_row['payment_methods_id'],
                'payment_methods_code' => $payment_methods_row['payment_methods_code'],
                'payment_methods_types_id' => $payment_methods_row['payment_methods_types_id'],
                'payment_methods_parent_id' => $this->payment_methods_id,
                'payment_methods_logo' => $payment_methods_row['payment_methods_logo'],
                'payment_methods_description_title' => $pm_desc_title_row['payment_methods_description_title'],
                'currency' => $this->get_support_currencies($payment_methods_row['payment_methods_id']),
                'show_billing_address' => $pm_array['MODULE_PAYMENT_SMART2PAY_GLOBALPAY_MANDATORY_ADDRESS_FIELD'],
                'confirm_complete_days' => $pm_array['MODULE_PAYMENT_SMART2PAY_GLOBALPAY_CONFIRM_COMPLETE']
            );

            if ($payment_methods_row['payment_methods_receive_status_mode'] == '-1') {
                $pm_status_message_select = "	SELECT payment_methods_status_message 
                                                FROM " . TABLE_PAYMENT_METHODS_STATUS_DESCRIPTION . " 
                                                WHERE payment_methods_mode = 'RECEIVE' 
                                                        AND payment_methods_status = '-1' 
                                                        AND languages_id = '" . (int) $languages_id . "' 
                                                        AND payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "'";
                $pm_status_message_result = tep_db_query($pm_status_message_select);
                $pm_status_message_row = tep_db_fetch_array($pm_status_message_result);
                $selection_array[sizeof($selection_array) - 1]['payment_methods_status_message'] = $pm_status_message_row['payment_methods_status_message'];
            }
        }

        return $selection_array;
    }

    public function get_default_supported_currency() {
        return isset($this->supportCurrencies[0]) ? $this->supportCurrencies[0] : '';
    }

    function get_require_address_information() {
        return $this->require_address_information;
    }

    function draw_confirm_button() {
        return tep_image_button2('red', 'javascript:document.checkout_confirmation.submit()', MODULE_PAYMENT_SMART2PAY_GLOBALPAY_TEXT_CART, 200);
    }

    function get_confirm_button_caption() {
        return MODULE_PAYMENT_SMART2PAY_GLOBALPAY_TEXT_DISPLAY_TITLE;
    }

    function pre_confirmation_check() {
        ; // Nothing to check for now
    }

    function confirmation() {
        
    }

    function set_support_currencies($currency_array) {
        
    }

    /*
     * render the splash page and post data to PG
     * ensure order has been created
     * checkout currency support by PG
     */

    function process_button() {
        global $currencies, $currency, $order, $country;

        if ($this->force_to_checkoutprocess && !isset($_SESSION['order_logged'])) {
            return;
        }

        $process_button_string = '';
        $pg_currency = $currency;

        // if the user currency is not supported by smart2pay globalpay
        // fall back to the default currency set on the admin interface of the module
        if (!$this->is_supported_currency($pg_currency)) {
            $pg_currency = $this->get_default_supported_currency();
        }

        $country_array = tep_get_countries_with_iso_codes($country);
        $payment_gateway_amount = number_format($order->info['total'] * $currencies->get_value($pg_currency), $currencies->get_decimal_places($pg_currency), '.', '');
        $this->get_merchant_account($pg_currency);

        $hidden_field_array = array(
            'MerchantID' => $this->globalpay_id,
            'MerchantTransactionID' => $_SESSION['order_logged'],
            'Amount' => $this->convert_purchase_amount($payment_gateway_amount, 'send'),
            'Currency' => $pg_currency,
            'ReturnURL' => $this->return_url,
            'Description' => 'Purchase from ' . STORE_NAME,
            'Language' => 'en-GB',
            'CustomerName' => mb_convert_case($order->billing['firstname'] . ' ' . $order->billing['lastname'], MB_CASE_LOWER, "UTF-8"),
            'CustomerEmail' => $order->customer['email_address'],
            'CustomerPhone' => '+' . $order->customer['int_dialing_code'] . $order->customer['telephone'],
            'Country' => $country_array['countries_iso_code_2'],
            'MethodID' => $this->code,
            'Guaranteed' => 1,
            'CustomerID' => $_SESSION['customer_id'],
            'SiteID' => '2',
            'SkipHPP' => 1
        );

        $hidden_field_array['Hash'] = $this->generate_signature($hidden_field_array);

        foreach ($hidden_field_array as $key => $value) {
            $process_button_string .= tep_draw_hidden_field($key, $value);
        }

        return $process_button_string;
    }

    /*
     * data: status
     * 2 - Success
     * 3 - Cancelled
     * 4 - Failed
     * 7 - Processing
     * MerchantTransactionID: Order ID
     */

    function before_process() {
        global $_POST, $currencies, $currency, $order;

        if (!isset($_SESSION['order_logged'])) {
            // order has not been created
            return;
        } else {
            $status = isset($_GET['data']) ? (int) $_GET['data'] : 0;
            // order has been created

            if (in_array($status, array(2, 7))) {
//               // success or pending success, do nothing
            } else {
                switch ($status) {
                    case 3:
                        $error_message = 'Cancelled';
                        break;
                    case 4:
                        $error_message = 'Failed';
                        break;
                    default:
                        $error_message = 'UNKNOWN';
                        break;
                }

                tep_redirect(tep_href_link(FILENAME_CHECKOUT_PAYMENT, 'error_message=' . urlencode($error_message), 'SSL', TRUE, FALSE));
            }
        }
    }

    function call_api_verifying($order_id) {
        $status = FALSE;
        $error_code = TRUE;
        $result_array = array();

        $order_info_select_sql = "	SELECT o.payment_methods_id, o.currency, o.currency_value, o.customers_id, ot.value 
                                    FROM " . TABLE_ORDERS . " AS o 
                                    INNER JOIN " . TABLE_ORDERS_TOTAL . " AS ot 
                                            ON o.orders_id=ot.orders_id AND ot.class='ot_total' 
                                    WHERE o.orders_id = '" . tep_db_input($order_id) . "'";
        $order_info_result_sql = tep_db_query($order_info_select_sql);
        if ($order_info_row = tep_db_fetch_array($order_info_result_sql)) {
            if ($order_info_row['payment_methods_id'] != $this->payment_methods_id) {
                $error_code = 'PAYMENT_METHOD_MISMATCH';
            } else {
                $customerID = $order_info_row['customers_id'];
                $this->get_merchant_account($order_info_row['currency']);

                // build URL to retrieve transaction result
                $request_array = array(
                    'MerchantID' => $this->globalpay_id,
                    'MerchantTransactionID' => $order_id
                );
                $request_array['Hash'] = $this->generate_signature($request_array);

                $result = $this->request_cURL($this->checking_url, $request_array, $order_id, 'CHECK TRANSACTION');

                if ($result !== false && tep_not_empty($result)) {
                    $result_array = $this->split_query_to_array($result);

                    if (isset($result_array['Error'])) {
                        $error_code = $result_array['Error'];
                    } else {
                        $calc_signature = $this->is_valid_signature($result_array);

                        if ($calc_signature !== TRUE) {
                            $error_code = 'SIGNATURE_MISMATCH';
                            $result_array['calculated_hash'] = $calc_signature;
                        } else if ($result_array['MerchantTransactionID'] != $order_id) {
                            $error_code = 'ORDER_ID_MISMATCHED';
                        } else if (isset($result_array['StatusID']) && !empty($result_array['StatusID'])) {
                            $error_code = FALSE;
                            $status = (int) $result_array['StatusID'];
                        } else {
                            $error_code = 'EMPTY_RESPONSE';
                        }
                    }
                } else {
                    $error_code = 'NO_RESPONSE';
                }
            }
        } else {
            $error_code = 'ORDER_ID_NOT_FOUND';
        }

        return array('status' => $status, 'error' => $error_code, 'data' => $result_array);
    }

    function after_process() {
        return FALSE;
    }

    /*
     * process after order created but before send email to purchaser
     * this method will call process_button()
     */

    function link_to_payment($new_order_id) {
        if (isset($_SESSION['order_logged'])) {
            unset($_SESSION['order_logged']);
        }

        $_SESSION['order_logged'] = $new_order_id;
        require(DIR_WS_INCLUDES . 'modules/payment/smart2pay_globalpay/catalog/smart2pay_globalpay_splash.php');

        return;
    }

    public function is_supported_currency($selected_currency) {
        return in_array($selected_currency, $this->supportCurrencies);
    }

    function check_trans_status($order_id) {
        global $currencies;

        $description = '';
        $status_id = 0;
        $this->check_trans_status_flag = FALSE;

        $api_array = $this->call_api_verifying($order_id);
		$order = new order($order_id);
		$pg_currency = $api_array['data']['Currency'];
		$pg_amount = $this->convert_purchase_amount($api_array['data']['Amount']);
		$pg_amount = number_format($pg_amount, $currencies->currencies[$pg_currency]['decimal_places'], $currencies->currencies[$pg_currency]['decimal_point'], $currencies->currencies[$pg_currency]['thousands_point']);
		$amount = $currencies->currencies[$pg_currency]['symbol_left'] . $pg_amount . $currencies->currencies[$pg_currency]['symbol_right'];

		if ($amount != strip_tags($order->order_totals['ot_total']['text'])) {
			$api_array['error'] = 'AMOUNT_MISMATCH';
		}
        switch ($api_array['error']) {
            case 'SIGNATURE_MISMATCH':
                $description = 'Error : Check Status Signature not match ' . $api_array['data']['Hash'] . ' != ' . $api_array['data']['calculated_hash'];
                break;
            case 'AMOUNT_MISMATCH':
                $description = 'Error : Amount not match ' . $amount . ' != ' . strip_tags($order->order_totals['ot_total']['text']);
                break;
            case 'PAYMENT_METHOD_MISMATCH':
                $description = 'Error : PAYMENT METHOD MISMATCH';
                break;
            case 'ORDER_ID_MISMATCHED':
                $description = 'Error : Order ID mismatched != ' . $api_array['data']['MerchantTransactionID'];
                break;
            case 'ORDER_ID_NOT_FOUND':
                $description = "Error: Order not Found != " . $order_id;
                break;
            case 'EMPTY_RESPONSE':
                $description = 'Error : Return Empty Value';
                break;
            case 'NO_RESPONSE':
                $description = 'Error : NO RESPONSE';
                break;
            default:
                if ($api_array['error'] === FALSE) {
                    $status_id = (int) $api_array['data']['StatusID'];
                    $description = 'Payment Status : ' . self::get_status_description($status_id);

                    if ($status_id === 2) {
                        $this->check_trans_status_flag = TRUE;
                    }
                } else {
                    $description = 'Error : ' . $api_array['error'];
                }
                break;
        }

        $this->save_history_data($order_id, $description, $status_id);

        if ($api_array['data'] !== array()) {
            $smart2payGlobalPayOrderDataArray = array(
                'smart2pay_globalpay_merchant_id' => $this->globalpay_id,
                'smart2pay_globalpay_method_id_returned' => $api_array['data']['MethodID'],
                'smart2pay_globalpay_transaction_id' => $api_array['data']['PaymentID'],
                'smart2pay_globalpay_amount' => $this->convert_purchase_amount($api_array['data']['Amount']),
                'smart2pay_globalpay_currency' => $api_array['data']['Currency'],
                'smart2pay_globalpay_status_id' => $api_array['data']['StatusID'],
                'smart2pay_globalpay_return_info' => $api_array['data']['ReturnInfo'],
                'smart2pay_globalpay_hash' => $api_array['data']['Hash']
            );

            $this->save_data($order_id, $smart2payGlobalPayOrderDataArray);
        }

        return $this->check_trans_status_flag;
    }

    function output_error() {
        return false;
    }

    public function convert_purchase_amount($amount, $convert_type = 'receive') {
        $return_amount = $amount;
        $exponent = !empty($this->exponent) ? (int) $this->exponent : 2;

        if ($exponent > 0 && $return_amount > 0) {
            if ($convert_type == 'receive') {
                $return_amount = $return_amount / pow(10, $exponent);
            } else if ($convert_type == 'send') {
                $return_amount = $return_amount * pow(10, $exponent);
            }
        }

        return $return_amount;
    }

    function check() {
        if (!isset($this->_check)) {
            $payment_methods_select_sql = "	SELECT payment_methods_id
											FROM " . TABLE_PAYMENT_METHODS . " as pm
											WHERE pm.payment_methods_id = '" . (int) $this->payment_methods_id . "'";
            $payment_methods_result_sql = tep_db_query($payment_methods_select_sql);
            $this->_check = tep_db_num_rows($payment_methods_result_sql);
        }

        return $this->_check;
    }

    public function is_data_exist($order_id) {
        $return_bool = FALSE;

        $payment_methods_select_sql = "	SELECT smart2pay_globalpay_order_id
                                        FROM " . TABLE_SMART2PAY_GLOBALPAY . "
                                        WHERE smart2pay_globalpay_order_id = '" . $order_id . "'";
        $payment_methods_result_sql = tep_db_query($payment_methods_select_sql);
        if (tep_db_num_rows($payment_methods_result_sql)) {
            $return_bool = TRUE;
        }

        return $return_bool;
    }

    function is_processed_order($order_id) {
        $return_bool = FALSE;

        $payment_status_select_sql = "	SELECT smart2pay_globalpay_status_id
										FROM " . TABLE_SMART2PAY_GLOBALPAY . " 
										WHERE smart2pay_globalpay_order_id = '" . (int) $order_id . "'";
        $payment_status_result_sql = tep_db_query($payment_status_select_sql);
        if ($payment_status_row = tep_db_fetch_array($payment_status_result_sql)) {
            if ($payment_status_row['smart2pay_globalpay_status_id'] == 2) {
                $return_bool = TRUE;
            }
        }

        return $return_bool;
    }

    private function is_valid_signature($data_array) {
        $return_str = '';
        $hash = $data_array['Hash'];
        $calc_hash = $this->generate_signature($data_array);

        if ($calc_hash === $hash) {
            $return_str = TRUE;
        } else {
            $return_str = $calc_hash;
        }

        return $return_str;
    }

    function load_pm_setting($language_id = 1, $pm_id = '') {
        $pm_setting_array = array();
        //load value from DB

        if (tep_not_null($pm_id)) {
            $payment_method_id = $pm_id;

            $payment_methods_parent_id_select_sql = "	SELECT payment_methods_parent_id 
                                                        FROM " . TABLE_PAYMENT_METHODS . " 
                                                        WHERE payment_methods_id = '" . (int) $pm_id . "'";
            $payment_methods_parent_id_result_sql = tep_db_query($payment_methods_parent_id_select_sql);
            $payment_methods_parent_id_row = tep_db_fetch_array($payment_methods_parent_id_result_sql);

            $payment_gateway_id = $payment_methods_parent_id_row['payment_methods_parent_id'];
        } else {
            $payment_method_id = $this->payment_methods_id;
            $payment_gateway_id = $this->payment_methods_parent_id;
        }

        $payment_configuration_info_select_sql = "	SELECT pci.payment_configuration_info_key, pcid.payment_configuration_info_value
                                                        FROM " . TABLE_PAYMENT_CONFIGURATION_INFO . " as pci
                                                        LEFT JOIN " . TABLE_PAYMENT_CONFIGURATION_INFO_DESCRIPTION . " as pcid 
                                                                ON pci.payment_configuration_info_id = pcid.payment_configuration_info_id
                                                                        AND pcid.languages_id = '" . (int) $language_id . "'
                                                        WHERE pci.payment_methods_id = '" . (int) $payment_method_id . "'
                                                        ORDER BY pci.payment_configuration_info_sort_order";
        $payment_configuration_info_result_sql = tep_db_query($payment_configuration_info_select_sql);
        if (tep_db_num_rows($payment_configuration_info_result_sql)) {
            while ($payment_configuration_info_row = tep_db_fetch_array($payment_configuration_info_result_sql)) {
                $pm_setting_array[$payment_configuration_info_row['payment_configuration_info_key']] = $payment_configuration_info_row['payment_configuration_info_value'];
            }
        } else if ((int) $payment_gateway_id > 0) { //load value from PG
            $payment_configuration_info_select_sql = "	SELECT pci.payment_configuration_info_key, pcid.payment_configuration_info_value
                                                        FROM " . TABLE_PAYMENT_CONFIGURATION_INFO . " as pci
                                                        LEFT JOIN " . TABLE_PAYMENT_CONFIGURATION_INFO_DESCRIPTION . " as pcid 
                                                                ON pci.payment_configuration_info_id = pcid.payment_configuration_info_id
                                                                        AND pcid.languages_id = '" . (int) $language_id . "'
                                                        WHERE pci.payment_methods_id = '" . (int) $payment_gateway_id . "'
                                                        ORDER BY pci.payment_configuration_info_sort_order";
            $payment_configuration_info_result_sql = tep_db_query($payment_configuration_info_select_sql);
            while ($payment_configuration_info_row = tep_db_fetch_array($payment_configuration_info_result_sql)) {
                $pm_setting_array[$payment_configuration_info_row['payment_configuration_info_key']] = $payment_configuration_info_row['payment_configuration_info_value'];
            }
        }

        return $pm_setting_array;
    }

    public function generate_signature($data_array) {
        $return_str = '';

        foreach ($data_array as $key => $val) {
            if ($key == 'Hash')
                continue;
            $return_str .= $key . $val;
        }

        return hash('sha256', strtolower($return_str . $this->globalpay_key));
    }

    public function save_data($order_id, $data_array) {
        if ($this->is_data_exist($order_id) !== FALSE) {
            tep_db_perform(TABLE_SMART2PAY_GLOBALPAY, $data_array, 'update', "smart2pay_globalpay_order_id = '" . $order_id . "'");
        } else {
            $data_array['smart2pay_globalpay_order_id'] = $order_id;
            tep_db_perform(TABLE_SMART2PAY_GLOBALPAY, $data_array);

            $this->save_history_data($order_id, self::get_status_description($data_array['smart2pay_globalpay_status_id']), $data_array['smart2pay_globalpay_status_id']);
        }
    }

    public function save_history_data($order_id, $description, $status_id = 0) {
        $path_info_array = pathinfo($_SERVER['SCRIPT_FILENAME']);

        $insert_history_array = array(
            'smart2pay_globalpay_order_id' => $order_id,
            'smart2pay_globalpay_description' => $description,
            'smart2pay_globalpay_status_id' => $status_id,
            'smart2pay_globalpay_date' => date("Y-m-d H:i:s"),
            'smart2pay_globalpay_changed_by' => (isset($path_info_array["basename"]) && $path_info_array["basename"] == 'cron_order_payment.php' ? 'cronjob' : (isset($_SESSION['login_email_address']) ? $_SESSION['login_email_address'] : 'system' ))
        );

        tep_db_perform(TABLE_SMART2PAY_GLOBALPAY_STATUS_HISTORY, $insert_history_array);
    }

    private function split_query_to_array($data_str) {
        $return_array = array();

        if (!empty($data_str)) {
            $pairs = explode("&", $data_str);

            foreach ($pairs as $pair) {
                list($key, $val) = explode("=", $pair);
                $return_array[$key] = $val;
            }
        }

        return $return_array;
    }

    public static function get_status_description($statusID) {
        switch ($statusID) {
            case '1' : return 'Open';
                break;
            case '2' : return 'Success';
                break;
            case '3' : return 'Cancelled';
                break;
            case '4' : return 'Failed';
                break;
            case '5' : return 'Expired';
                break;
            default :
                return 'Unknown';
                break;
        }
    }

    private function request_cURL($url, $request_array, $order_id, $call_action = '') {
        $curl_obj = new curl();
        $result = $curl_obj->curl_post($url, http_build_query($request_array, null, '&'));

        if ($result === FALSE) {
            ob_start();
            echo "========================REQUEST==========================<BR>";
            print_r($request_array);
            echo "========================================================<BR>";
            echo "========================RESPONSE=========================<BR>";
            print_r($result);
            echo "========================================================<BR>";
            print_r($curl_obj->get_error());
            $debug_html = ob_get_contents();
            ob_end_clean();
            // send debug content through email
            @tep_mail('<EMAIL>', '<EMAIL>', $order_id . ' - Smart2pay Globalpay ' . $call_action . ' DEBUG E-MAIL ' . date('Y-m-d H:i:s'), $debug_html, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
        }

        unset($curl_obj);

        return $result;
    }

    function get_merchant_account($selected_currency) {
        $payment_methods_instance_id = 0;
        $payment_gateway_instance_select_sql = "SELECT pmi.payment_methods_instance_id, pmi.payment_methods_instance_follow_default, pmi.currency_code 
				 								FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " as pmi
				 								WHERE pmi.payment_methods_id = '" . (int) $this->payment_methods_id . "'
				 								LIMIT 1";
        $payment_gateway_instance_result_sql = tep_db_query($payment_gateway_instance_select_sql);
        if (tep_db_num_rows($payment_gateway_instance_result_sql)) {
            $payment_gateway_instance_select_sql = "SELECT pmi.payment_methods_instance_id, pmi.payment_methods_instance_follow_default
					 								FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " as pmi
					 								WHERE pmi.currency_code = '" . tep_db_input($selected_currency) . "' 
					 									AND pmi.payment_methods_id = '" . (int) $this->payment_methods_id . "'";
            $payment_gateway_instance_result_sql = tep_db_query($payment_gateway_instance_select_sql);
            if ($payment_gateway_instance_row = tep_db_fetch_array($payment_gateway_instance_result_sql)) {
                if ((int) $payment_gateway_instance_row['payment_methods_instance_follow_default'] > 0) { // Follow Default Currency Setting
                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_follow_default'];
                } else { // Has own setting
                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_id'];
                }
            }
        } else {
            $payment_gateway_instance_select_sql = "SELECT pmi.payment_methods_instance_id, pmi.payment_methods_instance_follow_default
					 								FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " as pmi
					 								INNER JOIN " . TABLE_PAYMENT_METHODS . " as pm
					 									ON pm.payment_methods_parent_id = pmi.payment_methods_id
					 								WHERE pmi.currency_code = '" . tep_db_input($selected_currency) . "' 
					 									AND pm.payment_methods_id = '" . (int) $this->payment_methods_id . "'";
            $payment_gateway_instance_result_sql = tep_db_query($payment_gateway_instance_select_sql);
            if ($payment_gateway_instance_row = tep_db_fetch_array($payment_gateway_instance_result_sql)) {
                if ((int) $payment_gateway_instance_row['payment_methods_instance_follow_default'] > 0) {
                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_follow_default'];
                } else {
                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_id'];
                }
            }
        }

        $payment_gateway_instance_setting_select_sql = "SELECT pmis.payment_methods_instance_setting_key, pmis.payment_methods_instance_setting_value 
						 								FROM " . TABLE_PAYMENT_METHODS_INSTANCE_SETTING . " as pmis
						 								WHERE pmis.payment_methods_instance_id = '" . (int) $payment_methods_instance_id . "'";
        $payment_gateway_instance_setting_result_sql = tep_db_query($payment_gateway_instance_setting_select_sql);
        while ($payment_gateway_instance_setting_row = tep_db_fetch_array($payment_gateway_instance_setting_result_sql)) {
            switch ($payment_gateway_instance_setting_row['payment_methods_instance_setting_key']) {
                case 'MODULE_PAYMENT_SMART2PAY_GLOBALPAY_ID': //Smart2Pay ID
                    $this->globalpay_id = $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];
                    break;
                case 'MODULE_PAYMENT_SMART2PAY_GLOBALPAY_SIGNATURE': //Smart2Pay Key
                    $this->globalpay_key = $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];
                    break;
                case 'MODULE_PAYMENT_SMART2PAY_GLOBALPAY_EXPONENT':
                    $this->exponent = $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];
                    break;
				case 'MODULE_PAYMENT_SMART2PAY_GLOBALPAY_PRICE_RANGE_MIN':
                    $this->min_price = $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];
                    break;
                case 'MODULE_PAYMENT_SMART2PAY_GLOBALPAY_PRICE_RANGE_MAX':
                    $this->max_price = $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];
                    break;
				case 'MODULE_PAYMENT_TAX':
					$this->tax = $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];
					break;
            }
        }
    }

    function install() {
        $install_configuration_flag = TRUE;

        if ($this->payment_methods_id > 0) {
            if ($this->receive_status != 1) {
                $update_receive_status_sql_data = array("payment_methods_receive_status" => 1);
                tep_db_perform(TABLE_PAYMENT_METHODS, $update_receive_status_sql_data, 'update', "payment_methods_id = '" . (int) $this->payment_methods_id . "'");
            } else {
                $install_configuration_flag = FALSE;
            }
        } else {
            $install_payment_gateway_data_sql = array(
                'payment_methods_code' => $this->code,
                'payment_methods_send_status' => 0,
                'payment_methods_title' => $this->code,
                'payment_methods_types_id' => 0,
                'payment_methods_parent_id' => 0,
                'payment_methods_legend_color' => '#C3B99E',
                'payment_methods_sort_order' => 6,
                'payment_methods_filename' => $this->filename,
                'payment_methods_logo' => '',
                'date_added' => 'now()',
                'last_modified' => 'now()',
                'payment_methods_receive_status' => 1,
                'payment_methods_receive_status_mode' => 0
            );
            tep_db_perform(TABLE_PAYMENT_METHODS, $install_payment_gateway_data_sql);
            $this->payment_methods_id = tep_db_insert_id();
        }

        if ($install_configuration_flag) {
            $install_key_array = array();

            $install_key_array[] = array(
                'info' => array(
                    'payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Set Order Status',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_SMART2PAY_GLOBALPAY_ORDER_STATUS_ID',
                    'payment_configuration_info_description' => 'Set the status of orders made with this payment module to this value.',
                    'payment_configuration_info_sort_order' => '1335',
                    'set_function' => 'tep_cfg_pull_down_order_statuses(',
                    'use_function' => 'tep_get_order_status_name',
                    'date_added' => 'now()'
                ),
                'desc' => array(
                    'payment_configuration_info_value' => '1',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array(
                'info' => array(
                    'payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Set Order\'s "Processing" Status',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_SMART2PAY_GLOBALPAY_PROCESSING_STATUS_ID',
                    'payment_configuration_info_description' => 'Set the initial processing status of orders made with this payment module to this value.',
                    'payment_configuration_info_sort_order' => '1340',
                    'set_function' => 'tep_cfg_pull_down_order_statuses(',
                    'use_function' => 'tep_get_order_status_name',
                    'date_added' => 'now()'
                ),
                'desc' => array(
                    'payment_configuration_info_value' => '7',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array(
                'info' => array(
                    'payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Payment Message',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_SMART2PAY_GLOBALPAY_MESSAGE',
                    'payment_configuration_info_description' => 'Payment message will show up during checkout process (smart2pay).',
                    'payment_configuration_info_sort_order' => '1350',
                    'set_function' => 'tep_cfg_textarea(',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array(
                    'payment_configuration_info_value' => 'Thank you for shopping at OffGamers.com.',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array(
                'info' => array(
                    'payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Payment Email Message',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_SMART2PAY_GLOBALPAY_EMAIL_MESSAGE',
                    'payment_configuration_info_description' => 'This message will show up in order confirmation e-mail.',
                    'payment_configuration_info_sort_order' => '1360',
                    'set_function' => 'tep_cfg_textarea(',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array(
                    'payment_configuration_info_value' => 'Thank you for shopping at OffGamers.com.',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array(
                'info' => array(
                    'payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Confirm Complete (in days)',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_SMART2PAY_GLOBALPAY_CONFIRM_COMPLETE',
                    'payment_configuration_info_description' => 'The number of days in which an order made by this payment method is confirm completed.',
                    'payment_configuration_info_sort_order' => '1265',
                    'set_function' => '',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array(
                    'payment_configuration_info_value' => '0',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array(
                'info' => array(
                    'payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Test Mode',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_SMART2PAY_GLOBALPAY_TEST_MODE',
                    'payment_configuration_info_description' => 'Testing Mode?',
                    'payment_configuration_info_sort_order' => '100',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array(
                    'payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array(
                'info' => array(
                    'payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Require Address Information',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_SMART2PAY_GLOBALPAY_MANDATORY_ADDRESS_FIELD',
                    'payment_configuration_info_description' => 'Set address field as required info during checkout.',
                    'payment_configuration_info_sort_order' => '1400',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array(
                    'payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );

            foreach ($install_key_array as $data) {
                tep_db_perform(TABLE_PAYMENT_CONFIGURATION_INFO, $data['info']);
                $payment_conf_id = tep_db_insert_id();

                $data['desc']['payment_configuration_info_id'] = (int) $payment_conf_id;
                tep_db_perform(TABLE_PAYMENT_CONFIGURATION_INFO_DESCRIPTION, $data['desc']);
            }
        }

        return 1;
    }

    function keys() {
        return array();
    }

    function configuration_information_keys() {
        return array(
            'MODULE_PAYMENT_SMART2PAY_GLOBALPAY_ORDER_STATUS_ID',
            'MODULE_PAYMENT_SMART2PAY_GLOBALPAY_PROCESSING_STATUS_ID',
            'MODULE_PAYMENT_SMART2PAY_GLOBALPAY_CONFIRM_COMPLETE',
            'MODULE_PAYMENT_SMART2PAY_GLOBALPAY_TEST_MODE',
            'MODULE_PAYMENT_SMART2PAY_GLOBALPAY_MANDATORY_ADDRESS_FIELD'
        );
    }

    function multi_lang_configuration_info_keys() {
        return array(
            'MODULE_PAYMENT_SMART2PAY_GLOBALPAY_MESSAGE',
            'MODULE_PAYMENT_SMART2PAY_GLOBALPAY_EMAIL_MESSAGE'
        );
    }

    function merchant_information_keys() {
        include_once(DIR_WS_CLASSES . "payment_methods.php");

        return array(
            'MODULE_PAYMENT_SMART2PAY_GLOBALPAY_ID' => array('field' => 'text', 'mapping' => 'MODULE_PAYMENT_SMART2PAY_GLOBALPAY_LNG_ID'),
            'MODULE_PAYMENT_SMART2PAY_GLOBALPAY_SIGNATURE' => array('field' => 'text', 'mapping' => 'MODULE_PAYMENT_SMART2PAY_GLOBALPAY_LNG_SIGNATURE'),
            'MODULE_PAYMENT_SMART2PAY_GLOBALPAY_EXPONENT' => array('field' => 'text', 'mapping' => 'MODULE_PAYMENT_SMART2PAY_GLOBALPAY_LNG_EXPONENT', 'default' => '2'),
			'MODULE_PAYMENT_SMART2PAY_GLOBALPAY_PRICE_RANGE_MIN' => array('field' => 'text', 'mapping' => 'MODULE_PAYMENT_SMART2PAY_GLOBALPAY_LNG_PRICE_RANGE_MIN'),
            'MODULE_PAYMENT_SMART2PAY_GLOBALPAY_PRICE_RANGE_MAX' => array('field' => 'text', 'mapping' => 'MODULE_PAYMENT_SMART2PAY_GLOBALPAY_LNG_PRICE_RANGE_MAX'),
            'MODULE_PAYMENT_TAX' => array("field" => 'select', 'mapping' => "MODULE_PAYMENT_TAX", "select" => payment_methods::get_tax_option())
        );
    }

    function get_orders_payment_info_file() {
        return DIR_FS_CATALOG_MODULES . 'payment/smart2pay_globalpay/admin/orders.inc.php';
    }

    function get_orders_list_payment_info_file() {
        return DIR_FS_CATALOG_MODULES . 'payment/smart2pay_globalpay/admin/orders_list.inc.php';
    }

    function get_ipn_class_file() {
       return DIR_FS_CATALOG_MODULES . 'payment/smart2pay_globalpay/classes/smart2pay_globalpay_ipn_class.php';
    }

}

?>