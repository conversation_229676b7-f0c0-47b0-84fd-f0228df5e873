<?php
define('TABLE_SMART2PAY_GLO<PERSON>LPAY_PAYMENT_DETAILS', 'Payment Details');
define('TABLE_HEADING_SMART2PAY_GLOBALPAY_DATE', 'History Date');
define('TABLE_HEADING_SMART2PAY_GLOBALPAY_STATUS', 'Payment Status');
define('TABLE_HEADING_SMART2PAY_GLOBALPAY_CHANGED_BY', 'Changed By');

define('ENTRY_SMART2PAY_GLOBALPAY_AMT', 'Amount');
define('ENTRY_SMART2PAY_GLOBALPAY_PAYMENT_ID', 'Payment ID');
define('ENTRY_SMART2PAY_GLOBALPAY_CURRENCY', 'Currency');
define('ENTRY_SMART2PAY_GLOBALPAY_PAYMENT_STATUS', 'Payment Status');
define('ENTRY_SMART2PAY_GLOBALPAY_TRANSACTION_ID', 'Transaction ID');
define('ENTRY_SMART2PAY_GLOBALPAY_SIGNATURE', 'Signature');
define('MODULE_PAYMENT_SMART2PAY_GLOBALPAY_LNG_PRICE_RANGE_MIN', 'Price Range Min Amount');
define('MODULE_PAYMENT_SMART2PAY_GLOBALPAY_LNG_PRICE_RANGE_MAX', 'Price Range Max Amount');

define('TEXT_SMART2PAY_GLOBALPAY_NO_INFO', 'No further payment information is available.');
//key langauge
define('MODULE_PAYMENT_SMART2PAY_GLOBALPAY_LNG_ID', 'Smart2pay GlobalPay ID');
define('MODULE_PAYMENT_SMART2PAY_GLOBALPAY_LNG_SIGNATURE', 'Smart2Pay GlobalPay Secret Word');
define('MODULE_PAYMENT_SMART2PAY_GLOBALPAY_LNG_EXPONENT', 'Smart2Pay GlobalPay Exponent');
define('MODULE_PAYMENT_TAX', 'Tax');
?>