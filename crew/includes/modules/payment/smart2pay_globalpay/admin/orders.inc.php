<?php
include_once(DIR_FS_CATALOG_MODULES . 'payment/smart2pay_globalpay.php');
include_once(DIR_FS_CATALOG_MODULES . 'payment/smart2pay_globalpay/admin/languages/' . $language . '/smart2pay_globalpay.lng.php');

$trans_info_select_sql = "	SELECT * 
                            FROM " . TABLE_SMART2PAY_GLOBALPAY . " 
                            WHERE smart2pay_globalpay_order_id = '" . (int)$oID . "'";
$trans_info_result_sql= tep_db_query($trans_info_select_sql);

$trans_history_select_sql = "	SELECT * 
                                FROM " . TABLE_SMART2PAY_GLOBALPAY_STATUS_HISTORY . "
                                WHERE smart2pay_globalpay_order_id = '" . (int)$oID  . "' 
                                ORDER BY smart2pay_globalpay_date";
$trans_history_result_sql = tep_db_query($trans_history_select_sql);
?>
<table border="0" width="100%" cellspacing="0" cellpadding="2">
<?php ob_start(); ?>
	<tr>
		<td class="main">
      		<table border="0" width="100%" cellspacing="0" cellpadding="2">
      			<tr>
      				<td class="main" width="10%" nowrap><b><?=ENTRY_PAYMENT_METHOD?></b></td>
      				<td class="main">
      					<div>
      						<?php if((int)$order->info['payment_methods_id']>0) { ?><div class="<?php echo ((int)$payment_info_array[$order->info['payment_methods_id']]->confirm_complete_days>0?'rp_icon':'nrp_icon'); ?>"><b><?php echo ((int)$payment_info_array[$order->info['payment_methods_id']]->confirm_complete_days>0?'RP':'NRP'); ?></b></div> <?php } ?>
      						<div style="width:30px; height:15px; background-color:<?=$payment_module_info['display_colour']?>; float:left; vertical-align:top; text-align:center;">&nbsp;</div>
      						<div style="vertical-align: top">
      							&nbsp;&nbsp;
<?php
	if ($view_payment_details_permission) {
		
		if (tep_not_null($last_payment_method) && $last_payment_method['payment_methods_id'] != $order->info['payment_methods_id']) {
			echo '<span class="redIndicator">'.(tep_not_null($payment_module_info['payment_methods_parent_title'])?$payment_module_info['payment_methods_parent_title'] . ' - ' :'') . $payment_module_info['payment_methods_title'].'</span>';
		} else {
			echo (tep_not_null($payment_module_info['payment_methods_parent_title'])?$payment_module_info['payment_methods_parent_title'] . ' - ' :'') . $payment_module_info['payment_methods_title'];
		}
		
		if (tep_not_null($last_payment_method) && $last_payment_method['payment_methods_id'] != $order->info['payment_methods_id']) {
			echo '&nbsp;&nbsp;<span class="redIndicator">(Last Payment Method: ' . (tep_not_null($last_payment_method['parent_payment_methods_title'])? $last_payment_method['parent_payment_methods_title'] . ' - ' : '' ) . $last_payment_method['payment_methods_title'] . ')</span>';
		}
	} else {
		echo (tep_not_null($payment_module_info['payment_methods_parent_title'])?$payment_module_info['payment_methods_parent_title'] . ' - ' :'') . $payment_module_info['payment_methods_title'];
	}
?>
							</div>
      					</div>
      				</td>
				</tr>
			</table>
		</td>
	</tr>
<?php
$payment_method_title_info = ob_get_contents();
ob_end_clean();

if (tep_db_num_rows($trans_info_result_sql) || tep_db_num_rows($trans_history_result_sql)) {
	$trans_info_row = tep_db_fetch_array($trans_info_result_sql);
	
	echo $payment_method_title_info;
	if (!$view_payment_details_permission) {
		;
	} else {
?>
  	<tr>
    	<td class="main">
      		<table border="0" width="100%" cellspacing="0" cellpadding="2">
      			<tr valign="top">
        			<td width="35%">
              			<table border="0" cellspacing="0" cellpadding="2">
              				<tr>
                				<td class="main" valign="top" nowrap><b><?php echo ENTRY_SMART2PAY_GLOBALPAY_TRANSACTION_ID; ?></b>&nbsp;</td>
                				<td class="main"><?php echo $trans_info_row['smart2pay_globalpay_order_id']; ?></td>
              				</tr>
              				<tr>
                				<td class="main" valign="top" nowrap><b><?php echo ENTRY_SMART2PAY_GLOBALPAY_PAYMENT_ID; ?></b>&nbsp;</td>
                				<td class="main" nowrap><?php echo $trans_info_row['smart2pay_globalpay_transaction_id']; ?></td>
              				</tr>
              				<tr>
                				<td class="main" valign="top" nowrap><b><?php echo ENTRY_SMART2PAY_GLOBALPAY_CURRENCY; ?></b>&nbsp;</td>
                				<td class="main" nowrap>
                                <?php
			    					if (strtolower($trans_info_row['smart2pay_globalpay_currency']) == strtolower($order->info['currency'])) {
			    						echo $trans_info_row['smart2pay_globalpay_currency'];
			    					} else {
			    						echo '<span class="redIndicator">' . $trans_info_row['smart2pay_globalpay_currency'] . '<br><span class="smallText">Does not match purchase currency.</span></span>';
			    					}
    							?>
                                </td>
              				</tr>
              				<tr>
                				<td class="main" valign="top" nowrap><b><?php echo ENTRY_SMART2PAY_GLOBALPAY_AMT; ?></b>&nbsp;</td>
                				<td class="main" nowrap>
                                <?php
			    					$gross_display_text = number_format($trans_info_row["smart2pay_globalpay_amount"], $currencies->currencies[$order->info["currency"]]['decimal_places'], $currencies->currencies[$order->info["currency"]]['decimal_point'], $currencies->currencies[$order->info["currency"]]['thousands_point']);
			    					if ($currencies->currencies[$order->info["currency"]]['symbol_left'].$gross_display_text.$currencies->currencies[$order->info["currency"]]['symbol_right'] != strip_tags($order->order_totals['ot_total']['text']) ) {
			    						$gross_display_text = '<span class="redIndicator">' . $gross_display_text . '<br><span class="smallText">Does not match purchase amount.</span></span>';
			    					}
			    					echo $gross_display_text;
			    				?>
                                </td>
              				</tr>
                            <tr>
                				<td class="main" valign="top" nowrap><b><?php echo ENTRY_SMART2PAY_GLOBALPAY_SIGNATURE; ?></b>&nbsp;</td>
                				<td class="main" nowrap><?php echo $trans_info_row['smart2pay_globalpay_hash']; ?></td>
              				</tr>
              			</table>
        			</td>
        			<td>
              			<table border="0" cellspacing="0" cellpadding="2">
                            <tr>
                				<td class="main" valign="top" nowrap><b><?php echo ENTRY_SMART2PAY_GLOBALPAY_PAYMENT_STATUS; ?></b>&nbsp;</td>
                				<td class="main" nowrap>
	              				<?php
                					echo smart2pay_globalpay::get_status_description($trans_info_row['smart2pay_globalpay_status_id']);
                					
                					if ($order->info['orders_status'] == '1' || $order->info['orders_status'] == '7' || $order->info['orders_status'] == '2') {
	                					echo "&nbsp;&nbsp;";
										echo tep_draw_form('smart2pay_globalpay_status_check_form', FILENAME_ORDERS, tep_get_all_get_params(), 'post');
										echo tep_draw_hidden_field('subaction', 'payment_action');
										echo tep_draw_hidden_field('payment_action', 'check_trans_status');
										echo tep_submit_button('Check Payment Status', 'Check Payment Status', 'name="smart2payGlobalpayCheckTransStatusBtn"', 'inputButton');
										echo "</form>";
									}
								?>
								</td>
							</tr>
              				<tr>
                				<td class="main" colspan="2" nowrap>
                					<table border="1" cellspacing="0" cellpadding="2">
        								<tr>
                							<td class="smallText" nowrap><b><?php echo TABLE_HEADING_SMART2PAY_GLOBALPAY_DATE; ?></b></td>
                							<td class="smallText" nowrap><b><?php echo TABLE_HEADING_SMART2PAY_GLOBALPAY_STATUS; ?></b></td>
                                            <td class="smallText" nowrap><b><?php echo TABLE_HEADING_SMART2PAY_GLOBALPAY_CHANGED_BY; ?></b></td>
                						</tr>
<?php
		while ($trans_history_row = tep_db_fetch_array($trans_history_result_sql)) {
        	echo ' 						<tr>
                							<td class="smallText" nowrap>' . $trans_history_row['smart2pay_globalpay_date'] . '</td>
                							<td class="smallText" nowrap>' . $trans_history_row['smart2pay_globalpay_description'] . '</td>
                                            <td class="smallText" nowrap>' . $trans_history_row['smart2pay_globalpay_changed_by'] . '</td>
            							</tr>';
     	}
?>
                					</table>
                				</td>
                			</tr>
              			</table>
              		</td>
      			</tr>
      		</table>
   		</td>
  	</tr>
<?
	}
} else {
	echo $payment_method_title_info;
?>	
	<tr>
		<td class="main" nowrap>
		<?
			if ($order->info['orders_status'] == '1' || $order->info['orders_status'] == '7' || $order->info['orders_status'] == '2') {
				echo "&nbsp;&nbsp;";
				echo tep_draw_form('smart2pay_globalpay_status_check_form', FILENAME_ORDERS, tep_get_all_get_params(), 'post');
				echo tep_draw_hidden_field('subaction', 'payment_action');
				echo tep_draw_hidden_field('payment_action', 'check_trans_status');
				echo tep_submit_button('Check Payment Status', 'Check Payment Status', 'name="smart2payGlobalpayCheckTransStatusBtn"', 'inputButton');
				echo "</form>";
			}
		?>
		</td>
	</tr>
<?
}
?>
</table>