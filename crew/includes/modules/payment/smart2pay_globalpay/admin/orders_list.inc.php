<?php
include_once(DIR_FS_CATALOG_MODULES . 'payment/smart2pay_globalpay.php');
include_once(DIR_FS_CATALOG_MODULES . 'payment/smart2pay_globalpay/admin/languages/' . $language . '/smart2pay_globalpay.lng.php');

$trans_info_select_sql = "	SELECT * 
                            FROM " . TABLE_SMART2PAY_GLOBALPAY . " 
                            WHERE smart2pay_globalpay_order_id = '" . $order_obj->orders_id . "'";
$trans_info_result_sql= tep_db_query($trans_info_select_sql);
?>
<table border="0" width="100%" cellspacing="0" cellpadding="2">
	<tr>
		<td class="subInvoiceBoxHeading">
			<div style="border-bottom: 1px solid #996600; border-top: 1px solid #996600; padding: 2px;"><?php echo TABLE_SMART2PAY_GLOBALPAY_PAYMENT_DETAILS; ?></div>
		</td>
	</tr>
<?php
if ($trans_info_row = tep_db_fetch_array($trans_info_result_sql)) {
?>
	<tr>
    	<td class="main">
      		<table border="0" width="100%" cellspacing="0" cellpadding="2">
      			<tr valign="top">
        			<td width="50%">
              			<table border="0" cellspacing="0" cellpadding="2">
              				<tr>
                				<td class="invoiceRecords" valign="top" nowrap><?php echo ENTRY_SMART2PAY_GLOBALPAY_TRANSACTION_ID; ?>:&nbsp;</td>
                				<td class="invoiceRecords" nowrap><?php echo $trans_info_row['smart2pay_globalpay_order_id']?></td>
              				</tr>
              				<tr>
                				<td class="invoiceRecords" valign="top" nowrap><?php echo ENTRY_SMART2PAY_GLOBALPAY_PAYMENT_ID; ?>:&nbsp;</td>
                				<td class="invoiceRecords"><?php echo $trans_info_row['smart2pay_globalpay_transaction_id']; ?></td>
              				</tr>
              				<tr>
              					<td class="invoiceRecords" valign="top" nowrap><?php echo ENTRY_SMART2PAY_GLOBALPAY_CURRENCY; ?>:&nbsp;</td>
                				<td class="invoiceRecords"><?php echo $trans_info_row['smart2pay_globalpay_currency']; ?></td>
              				</tr>
                            <tr>
                				<td class="invoiceRecords" valign="top" nowrap><?php echo ENTRY_SMART2PAY_GLOBALPAY_AMT; ?>:&nbsp;</td>
                				<td class="invoiceRecords" nowrap><?php echo $trans_info_row["smart2pay_globalpay_amount"]; ?></td>
              				</tr>
                            <tr>
                				<td class="invoiceRecords" valign="top" nowrap><?php echo ENTRY_SMART2PAY_GLOBALPAY_SIGNATURE; ?>:&nbsp;</td>
                				<td class="invoiceRecords" nowrap><?php echo $trans_info_row['smart2pay_globalpay_hash']; ?></td>
              				</tr>
              			</table>
        			</td>
        			<td valign="top">
              			<table border="0" cellspacing="0" cellpadding="2">
              				<tr>
                				<td class="invoiceRecords" valign="top" nowrap><?php echo TABLE_HEADING_SMART2PAY_GLOBALPAY_STATUS; ?>:&nbsp;</td>
                				<td class="invoiceRecords"><?php echo smart2pay_globalpay::get_status_description($trans_info_row['smart2pay_globalpay_status_id']); ?></td>
              				</tr>
              			</table>
              		</td>
      			</tr>
      		</table>
   		</td>
  	</tr>
<?php
} else {
?>
	<tr>
		<td class="invoiceRecords"><?PHP echo TEXT_SMART2PAY_GLOBALPAY_NO_INFO; ?></td>
	</tr>
<?
}
?>