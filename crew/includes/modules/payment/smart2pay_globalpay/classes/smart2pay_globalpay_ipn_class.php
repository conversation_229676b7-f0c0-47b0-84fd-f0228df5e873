<?php

class smart2pay_globalpay_ipn {
    private $data_array,
            $pg_obj;
    
  	public function __construct($ipn_data = '') {
    	$this->data_array = $this->split_query_to_array($ipn_data);
	}
    
    public function acknowledge_ipn() {
        $return_str = '';
        
        if (is_object($this->pg_obj)) {
            $ipn_response_array =  array(
                'NotificationType' => 'Payment',
                'PaymentID' => $this->get_data('PaymentID')
            );
            
            $ipn_response_array['Hash'] = $this->pg_obj->generate_signature($ipn_response_array);
            $return_str = http_build_query($ipn_response_array, null, '&');
        }
        
        return $return_str;
    }
    
	public function authenticate($order_obj) {
        global $currencies;
		
        if (is_object($this->pg_obj)) {
            $order_id = $this->get_order_id();
            $pg_currency = $this->get_data('Currency', '');
            $pg_amount = $this->get_data('Amount', '');
            $exponent = (int)(tep_not_null($this->pg_obj->exponent) ? $this->pg_obj->exponent : 2);
            
            if (!$this->pg_obj->is_supported_currency($pg_currency)) {
                $pg_currency = $this->pg_obj->get_default_supported_currency();
            }
            
            if ($exponent > 0) {
                $pg_amount = $pg_amount / pow(10, $exponent);
            }

            $pg_amount = number_format($pg_amount, $currencies->currencies[$pg_currency]['decimal_places'], $currencies->currencies[$pg_currency]['decimal_point'], $currencies->currencies[$pg_currency]['thousands_point']);
            $amount = $currencies->currencies[$pg_currency]['symbol_left'] . $pg_amount . $currencies->currencies[$pg_currency]['symbol_right'];
            
            if ($amount == $order_obj->info['total']) {
                $payment_sucess = $this->pg_obj->check_trans_status($order_id);

                if ($payment_sucess) {
                    $this->process_this_order($order_id, $this->pg_obj, $this->pg_obj->order_processing_status);
                }
            } else {
                $this->pg_obj->save_history_data($order_id, 'Error : Amount not match != ' . $amount, 0);
            }
        }
	}
	
    private function get_data($field, $default = false) {
        return isset($this->data_array[$field]) ? $this->data_array[$field] : $default;
    }
	
    public function get_notification_type() {
        return $this->get_data('NotificationType');
    }
    
    public function get_order_id() {
        return $this->get_data('MerchantTransactionID');
    }
    
	function get_products_ordered($order) {
		global $currencies, $languages_id;
		
		$products_ordered = '';
		for ($i=0, $n=sizeof($order->products); $i<$n; $i++) {
			$product_instance_id = tep_not_null($order->products[$i]['custom_content']['hla_account_id']) ? $order->products[$i]['custom_content']['hla_account_id'] : '';
	    	$currencies->product_instance_id = $product_instance_id;
	    	
			//------insert customer choosen option to order--------
		    $attributes_exist = '0';
		    $products_ordered_attributes = '';
		    if (isset($order->products[$i]['attributes'])) {
		      	$attributes_exist = '1';
		      	for ($j=0, $n2=sizeof($order->products[$i]['attributes']); $j<$n2; $j++) {
		        	if (DOWNLOAD_ENABLED == 'true') {
		          		$attributes_query = "	select popt.products_options_name, poval.products_options_values_name, pa.options_values_price, pa.price_prefix, pad.products_attributes_maxdays, pad.products_attributes_maxcount , pad.products_attributes_filename
		                               			from " . TABLE_PRODUCTS_OPTIONS . " popt, " . TABLE_PRODUCTS_OPTIONS_VALUES . " poval, " . TABLE_PRODUCTS_ATTRIBUTES . " pa
		                               			left join " . TABLE_PRODUCTS_ATTRIBUTES_DOWNLOAD . " pad
		                                			on pa.products_attributes_id=pad.products_attributes_id
		                               			where pa.products_id = '" . $order->products[$i]['id'] . "'
					                                and pa.options_id = '" . $order->products[$i]['attributes'][$j]['option_id'] . "'
					                                and pa.options_id = popt.products_options_id
					                                and pa.options_values_id = '" . $order->products[$i]['attributes'][$j]['value_id'] . "'
					                                and pa.options_values_id = poval.products_options_values_id
					                                and popt.language_id = '" . $languages_id . "'
					                                and poval.language_id = '" . $languages_id . "'";
		          		$attributes = tep_db_query($attributes_query);
		        	} else {
		          		$attributes = tep_db_query("select popt.products_options_name, poval.products_options_values_name, pa.options_values_price, pa.price_prefix from " . TABLE_PRODUCTS_OPTIONS . " popt, " . TABLE_PRODUCTS_OPTIONS_VALUES . " poval, " . TABLE_PRODUCTS_ATTRIBUTES . " pa where pa.products_id = '" . $order->products[$i]['id'] . "' and pa.options_id = '" . $order->products[$i]['attributes'][$j]['option_id'] . "' and pa.options_id = popt.products_options_id and pa.options_values_id = '" . $order->products[$i]['attributes'][$j]['value_id'] . "' and pa.options_values_id = poval.products_options_values_id and popt.language_id = '" . $languages_id . "' and poval.language_id = '" . $languages_id . "'");
		        	}
		        	$attributes_values = tep_db_fetch_array($attributes);
		        	if ((DOWNLOAD_ENABLED == 'true') && isset($attributes_values['products_attributes_filename']) && tep_not_null($attributes_values['products_attributes_filename']) ) {
		          		$sql_data_array = array('orders_id' => $orders_id,
		                                  		'orders_products_id' => $order->products[$i]['orders_products_id'],
		                                  		'orders_products_filename' => $attributes_values['products_attributes_filename'],
				                                'download_maxdays' => $attributes_values['products_attributes_maxdays'],
				                                'download_count' => $attributes_values['products_attributes_maxcount']);
		          		tep_db_perform(TABLE_ORDERS_PRODUCTS_DOWNLOAD, $sql_data_array);
		        	}
		        	$products_ordered_attributes .= "\n\t" . $attributes_values['products_options_name'] . ' ' . $attributes_values['products_options_values_name'];
		      	}
			}
			//------insert customer choosen option eof ----
		    $total_weight += ($order->products[$i]['qty'] * $order->products[$i]['weight']);
		    $total_tax += tep_calculate_tax($total_products_price, $products_tax) * $order->products[$i]['qty'];
		    $total_cost += $total_products_price;
			
			$cat_path = tep_output_generated_category_path($order->products[$i]['id'], 'product');
		  	$product_email_display_str = $cat_path . (tep_not_null($cat_path) ? ' > ' : '') . $order->products[$i]['name'] . (tep_not_null($order->products[$i]['model']) ? ' (' . $order->products[$i]['model'] . ')' : '');
		    
		    $products_ordered .= $order->products[$i]['qty'] . ' x ' . $product_email_display_str . ' = ' . $currencies->display_price($order->products[$i]['id'], $order->products[$i]['final_price'], $order->products[$i]['tax'], $order->products[$i]['qty']) . $products_ordered_attributes . "\n";
		}
		
		return $products_ordered;
	}
	
    public function get_status() {
        return $this->get_data('StatusID');
    }
    
    public function is_ipn_data_exist() {
        $return_bool = false;
        
        if ($this->data_array !== array() && count($this->data_array)) {
            $return_bool = true;
        }
        
        return $return_bool;
    }
    
    public function is_valid_signature() {
        $return_str = '';
        $hash = isset($this->data_array['Hash']) ? $this->data_array['Hash'] : '';
        
        if (is_object($this->pg_obj)) {
            $calc_hash = $this->pg_obj->generate_signature($this->data_array);
        
            if ($calc_hash === $hash) {
                $return_str = TRUE;
            } else {
                $return_str = $calc_hash;
            }
        }
        
        return $return_str;
    }
    
    public function load_pg_main_obj($obj) {
        $this->pg_obj = $obj;
    }
    
	function process_this_order($orders_id, $payment_method_obj, $update_to_status) {
		global $currencies, $languages_id;
		
		$orders_status_array = array();
		$orders_status_query = tep_db_query("SELECT orders_status_id, orders_status_name from " . TABLE_ORDERS_STATUS . " WHERE language_id = '" . (int)$languages_id . "' ORDER BY orders_status_sort_order");
		while ($orders_status = tep_db_fetch_array($orders_status_query)) {
			$orders_status_array[$orders_status['orders_status_id']] = $orders_status['orders_status_name'];
		}
		
		// Call this function when money reach our account
		$cur_order = new order($orders_id);
		
		if ($cur_order->info['orders_status_id'] == $payment_method_obj->order_status) {	// If the order still in initial status
			$order_comment = '';
			$customer_notification = (SEND_EMAILS == 'true') ? '1' : '0';
			
			$orders_status_history_perform_array = array(	'action' => 'insert',
															'data' => array('orders_id' => $orders_id,
																			'orders_status_id' => $update_to_status,
																			'date_added' => 'now()',
																			'customer_notified' => $customer_notification,
																			'comments' => $order_comment
																		)
														);
			
			$cur_order->update_order_status($update_to_status, $orders_status_history_perform_array, true);
			unset($orders_status_history_perform_array);
			
			// E-mail section
			$customer_profile_select_sql = "SELECT customers_gender, customers_firstname, customers_lastname 
											FROM " . TABLE_CUSTOMERS . " 
											WHERE customers_id = '" . $cur_order->customer["id"] . "'";
			$customer_profile_result_sql = tep_db_query($customer_profile_select_sql);
			if ($customer_profile_row = tep_db_fetch_array($customer_profile_result_sql)) {
				$email_firstname = $customer_profile_row["customers_firstname"];
				$email_lastname = $customer_profile_row["customers_lastname"];
			} else {
				$email_firstname = $cur_order->customer['name'];
				$email_lastname = $cur_order->customer['name'];
			}
			
			$email_greeting = tep_get_email_greeting($email_firstname, $email_lastname, $customer_profile_row["customers_gender"]);
			
			$email_order = $email_greeting . sprintf(EMAIL_TEXT_ORDER_THANK_YOU, $currencies->format($cur_order->info['total_value'], true, $cur_order->info['currency'])) . "\n\n";
			
			if (is_object($payment_method_obj)) {
				$email_order .= EMAIL_TEXT_PAYMENT_METHOD . "\n" .
			                    EMAIL_SEPARATOR . "\n";
			    $payment_class = $payment_method_obj;
			    $email_order .= strip_tags($payment_class->title) . "\n";
			    if ($payment_class->email_footer) {
			      	$email_order .= $payment_class->email_footer . "\n\n";
			    } else { $email_order .= "\n"; }
			}
			
			if ($cur_order->delivery !== false) {
				$order_shipping_address = tep_address_format($cur_order->delivery['format_id'], $cur_order->delivery, 0, '', "\n");
				$email_order .= EMAIL_TEXT_DELIVERY_ADDRESS . "\n" .
			                   	EMAIL_SEPARATOR . "\n" .
			                   	$order_shipping_address . "\n\n";
			}
			
			$email_order .= EMAIL_TEXT_BILLING_ADDRESS . "\n" .
							EMAIL_SEPARATOR . "\n" .
			                tep_address_format($cur_order->billing['format_id'], $cur_order->billing, 0, '', "\n") . "\n\n";
			
			$email_order .= EMAIL_TEXT_ORDER_SUMMARY . "\n" .
							EMAIL_SEPARATOR . "\n" .
							EMAIL_TEXT_ORDER_NUMBER . ' ' . $orders_id . "\n" .
							EMAIL_TEXT_DATE_ORDERED . ' ' . tep_date_long($cur_order->info['date_purchased']) . ".\n\n";
			
			$email_order .= EMAIL_TEXT_PRODUCTS . "\n" . 
			                EMAIL_SEPARATOR . "\n" . 
			                $this->get_products_ordered($cur_order) . 
			                EMAIL_SEPARATOR . "\n";
			
			for ($i=0, $n=sizeof($cur_order->totals); $i<$n; $i++) {
				$email_order .= strip_tags($cur_order->totals[$i]['title']) . ' ' . strip_tags($cur_order->totals[$i]['text']) . "\n";
			}
			
			$orders_history_query = tep_db_query("select comments from " . TABLE_ORDERS_STATUS_HISTORY . " where orders_id = '" . tep_db_input($orders_id) . "' order by date_added limit 1");
			if (tep_db_num_rows($orders_history_query)) {
				$orders_history = tep_db_fetch_array($orders_history_query);
				$cur_order->info['comments'] = $orders_history['comments'];
			}
			
			if ($order->info['comments']) {
			    $email_order .= "\n" . EMAIL_TEXT_EXTRA_INFO . "\n" . 
			    				tep_db_output(tep_db_prepare_input($cur_order->info['comments']));
			}
			
			$email_order .= "\n\n";
			
			$email_order = $email_order . EMAIL_TEXT_CLOSING . "\n\n" . EMAIL_FOOTER;
			
			tep_mail($cur_order->customer['name'], $cur_order->customer['email_address'], implode(' ', array(EMAIL_SUBJECT_PREFIX, sprintf(EMAIL_TEXT_SUBJECT, tep_db_input($orders_id)))), $email_order, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
			
			// send emails to other people
			if (tep_not_null(SEND_EXTRA_ORDER_EMAILS_TO)) {
				// A copy of email to admin when new order is made
				$extra_email_array = explode(',', SEND_EXTRA_ORDER_EMAILS_TO);
				$extra_email_email_pattern = "/([^<]*?)(?:<)([^>]+)(?:>)/is";
				for ($receiver_cnt=0; $receiver_cnt < count($extra_email_array); $receiver_cnt++) {
					if (preg_match($extra_email_email_pattern, $extra_email_array[$receiver_cnt], $regs)) {
						$receiver_name = trim($regs[1]);
						$receiver_email = trim($regs[2]);
						tep_mail($receiver_name, $receiver_email, implode(' ', array(EMAIL_SUBJECT_PREFIX, sprintf(EMAIL_TEXT_SUBJECT, tep_db_input($orders_id)))), $email_order, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
					}
				}
			}
			
			// Send order update 
			tep_status_update_notification('C', tep_db_input($orders_id), 'system', $cur_order->info['orders_status_id'], $update_to_status, 'A');
	        
			// Send affiliate notification e-mail
			tep_send_affiliate_notification($orders_id, $cur_order);
		}
	}
    
    public function save_data($order_id) {
        if (is_object($this->pg_obj) && $order_id > 0) {    
            $smart2payGlobalPayOrderDataArray = array(  
                'smart2pay_globalpay_merchant_id' => $this->get_data('MerchantID', ''),
                'smart2pay_globalpay_method_id_returned' => $this->get_data('MethodID', ''),
                'smart2pay_globalpay_transaction_id' => $this->get_data('PaymentID', ''), 
                'smart2pay_globalpay_amount' => $this->pg_obj->convert_purchase_amount($this->get_data('Amount', 0)), 
                'smart2pay_globalpay_currency' => $this->get_data('Currency', ''), 
                'smart2pay_globalpay_status_id' => $this->get_data('StatusID', ''), 
                'smart2pay_globalpay_return_info' => $this->get_data('ReturnInfo', ''), 
                'smart2pay_globalpay_hash' => $this->get_data('Hash', '')
            );
            
            $this->pg_obj->save_data($order_id, $smart2payGlobalPayOrderDataArray);
        }
    }
	
    public function send_log_by_email($order_id, $raw_data) {
        //
    }
    
    public function split_query_to_array($data_str) {
        $return_array = array();
        
        if(!empty($data_str)){
            $pairs = explode("&", $data_str);
            
            foreach ($pairs as $pair) {
                list($key, $val) = explode("=", $pair);
                $return_array[$key] = $val;
            }
        }
        
        return $return_array;
    }

    public function validate_notification_type() {
        $return_bool = false;
        
        if (is_object($this->pg_obj)) {
            if ($this->get_notification_type() == 'Payment') {
                $return_bool = true;
            } else {
                $this->pg_obj->save_history_data($order_id, 'Error : ' . $this->get_data('ReturnCodeID') . ' : ' . $this->get_data('ReturnInfo'), 0);
            }
        }
        
        return $return_bool;
    }
    
    public function validate_save_data_exist($order_id) {
        $return_bool = false;
        
        // Check if this order already exists
        if (is_object($this->pg_obj)) {
            $return_bool = $this->pg_obj->is_data_exist($order_id);
        }
        
        return $return_bool;
    }

	public function validate_transaction_data() {
        $return_bool = false;
        
        if (is_object($this->pg_obj)) {
            $order_id = $this->get_order_id();
            
            if ($calculated_signature = $this->is_valid_signature() === TRUE) {
                return true;
            } else {
                $this->pg_obj->save_history_data($order_id, 'Error : Signature not match != ' . $calculated_signature, 0);
                return false;
            }
        }
	}
}//end class
?>