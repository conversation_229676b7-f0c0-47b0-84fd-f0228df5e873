<?php
require_once('payment_gateway.php');
include_once(DIR_WS_CLASSES . 'curl.php');

class vtc extends payment_gateway {
    var $code, $vtcCurrencies, $vtc_website_id, $vtc_receiver_acc, $vtc_secret_key;
    function vtc($pm_id = '') {
        global $languages_id, $default_languages_id;
        $this->code = 'vtc';
        $this->title = $this->code;
        $this->preferred = true;
        $this->filename = 'vtc.php';
        $this->auto_cancel_period = 30; // In minutes
        $this->connect_via_proxy = false; // localhost test - set to true, server test - set to false //need proxy to connect outside
        $this->force_to_checkoutprocess = true;
        $this->has_ipn = true;
        $this->payment_methods_id = 0;
        $this->payment_methods_parent_id = 0;
        $this->check_trans_status_flag = false;
        $payment_methods_select_sql = "	SELECT  payment_methods_parent_id, payment_methods_code, 
												payment_methods_types_id, 
												payment_methods_receive_status_mode, payment_methods_id, 
												payment_methods_title, payment_methods_sort_order, 
												payment_methods_receive_status, payment_methods_legend_color, 
												payment_methods_logo 
										FROM " . TABLE_PAYMENT_METHODS . "
										WHERE 1 ";
        if ((int) $pm_id > 0) {
            $payment_methods_select_sql .= "	AND payment_methods_id = '" . (int) $pm_id . "'";
        } else {
            $payment_methods_select_sql .= "	AND payment_methods_filename = '" . tep_db_input($this->filename) . "'";
        }
        $payment_methods_result_sql = tep_db_query($payment_methods_select_sql);
        if ($payment_methods_row = tep_db_fetch_array($payment_methods_result_sql)) {
            $pm_desc_title_select_sql = "	SELECT payment_methods_description_title 
                                                FROM " . TABLE_PAYMENT_METHODS_DESCRIPTION . " 
                                                WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "' 
                                                        AND (IF (languages_id = '" . tep_db_input($languages_id) . "', 1, IF ((	SELECT COUNT(payment_methods_id) > 0 
                                                                                                                                FROM " . TABLE_PAYMENT_METHODS_DESCRIPTION . " 
                                                                                                                                WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "' 
                                                                                                                                        AND languages_id = '" . (int) $languages_id . "'), 0, languages_id = '" . (int) $default_languages_id . "')))";
            $pm_desc_title_result_sql = tep_db_query($pm_desc_title_select_sql);
            $pm_desc_title_row = tep_db_fetch_array($pm_desc_title_result_sql);
            $this->display_title = $pm_desc_title_row['payment_methods_description_title'];
            $this->description = $this->display_title;
            $this->enabled = $payment_methods_row['payment_methods_receive_status_mode'];
            $this->payment_methods_id = (int) $payment_methods_row['payment_methods_id'];
            $this->payment_methods_parent_id = (int) $payment_methods_row['payment_methods_parent_id'];
            $this->code = $payment_methods_row['payment_methods_code'];
            $this->title = $payment_methods_row['payment_methods_title'];
            $this->sort_order = $payment_methods_row['payment_methods_sort_order'];
            $this->legend_display_colour = $payment_methods_row['payment_methods_legend_color'];
        }
        $configuration_setting_array = $this->load_pm_setting();
        $this->order_status_id = $configuration_setting_array['MODULE_PAYMENT_VTC_ORDER_STATUS_ID'];
        $this->processing_status_id = $configuration_setting_array['MODULE_PAYMENT_VTC_PROCESSING_STATUS_ID'];
        $this->confirm_complete_days = $configuration_setting_array['MODULE_PAYMENT_VTC_CONFIRM_COMPLETE'];
        $this->require_address_information = $configuration_setting_array['MODULE_PAYMENT_VTC_MANDATORY_ADDRESS_FIELD'];
        $this->test_mode = ($configuration_setting_array['MODULE_PAYMENT_VTC_TEST_MODE'] == 'True' ? true : false);
        $this->message = $configuration_setting_array['MODULE_PAYMENT_VTC_MESSAGE'];
        $this->email_message = $configuration_setting_array['MODULE_PAYMENT_VTC_EMAIL_MESSAGE'];
        $this->email_footer = str_replace(array("<br>", "<BR>", "\r\n"), array("\n", "\n", ''), $this->email_message);
        $this->vtcCurrencies = $this->get_support_currencies($this->payment_methods_id);
        $this->defCurr = DEFAULT_CURRENCY;
        $this->order_processing_status = ((int) $this->processing_status_id > 0 ? $this->processing_status_id : 7 );
        $this->order_status = ( (int) $this->order_status_id > 0 ? $this->order_status_id : DEFAULT_ORDERS_STATUS_ID );
        if ($this->test_mode) {
            $this->form_action_url = 'http://sandbox1.vtcebank.vn/pay.vtc.vn/cong-thanh-toan/checkout.html';	// VTC ePayment
            $this->payment_checking_url = 'http://sandbox1.vtcebank.vn/pay.vtc.vn/cong-thanh-toan/WSCheckTrans.asmx';	// re-query payment
        } else {
            $this->form_action_url = 'https://pay.vtc.vn/cong-thanh-toan/checkout.html';	// VTC ePayment
            $this->payment_checking_url = 'https://pay.vtc.vn/cong-thanh-toan/WSCheckTrans.asmx';	// re-query payment
        }
    }

    function check_trans_status($order_id) {
        global $login_email_address;
        $this->check_trans_status_flag = false;
        if ((isset($_GET['oID']) && !empty($_GET['oID']))) {
            $order_id = $_GET['oID'];
        }
        $vtc_payment_select_sql = "	SELECT currency, amount
                                    FROM " . TABLE_VTC . "
                                    WHERE order_id = '" . tep_db_input($order_id) . "'";
        $vtc_payment_result_sql = tep_db_query($vtc_payment_select_sql);
        if ($vtc_payment_result_row = tep_db_fetch_array($vtc_payment_result_sql)) {
            $this->get_merchant_account($vtc_payment_result_row['currency']);
            $string = $this->vtc_website_id . '-' . $order_id . '-' . $this->vtc_receiver_acc . '-' . $this->vtc_secret_key;
            $sign = strtoupper(hash('sha256', $string));
            $xml_post_string = '<?xml version="1.0" encoding="utf-8"?>
                                <soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
                                  <soap:Body>
                                    <CheckPartnerTransation xmlns="http://tempuri.org/">
                                      <website_id>'.$this->vtc_website_id.'</website_id>
                                      <order_code>'.$order_id.'</order_code>
                                      <receiver_acc>'.$this->vtc_receiver_acc.'</receiver_acc>
                                      <sign>'.$sign.'</sign>
                                    </CheckPartnerTransation>
                                  </soap:Body>
                                </soap:Envelope>';   // data from the form, e.g. some ID number

            $headers = array(
                "Content-type: text/xml;charset=\"utf-8\"",
                "Accept: text/xml",
                "Cache-Control: no-cache",
                "Pragma: no-cache",
                "SOAPAction: http://tempuri.org/CheckPartnerTransation",
                "Content-length: ".strlen($xml_post_string),
            ); //SOAPAction: your op URL


            // PHP cURL  for https connection with auth
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $this->payment_checking_url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 60);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $xml_post_string); // the SOAP request
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
//            curl_setopt($ch, CURLOPT_PROXY, 'my-proxy.offgamers.lan:3128');
            // converting
            $response = curl_exec($ch);
            curl_close($ch);
            // converting
            $response1 = str_replace("<soap:Body>","",$response);
            $response2 = str_replace("</soap:Body>","",$response1);

            // convertingc to XML
            $parser = simplexml_load_string($response2);

            $checkTransResult = '';
            if(isset($parser->CheckPartnerTransationResponse->CheckPartnerTransationResult)) {
                $checkTransResult = explode('|',$parser->CheckPartnerTransationResponse->CheckPartnerTransationResult);
            }
            $status = '';
            $reason = '';
            if(!is_array($checkTransResult))
            {
                $status = 'ERR';
                $reason = "***invalid response returned from gateway***";
            } else {
                if (isset($checkTransResult[0]) &&  ($checkTransResult[0] == '1' || $checkTransResult[0] == '2')) {
                    if (isset($checkTransResult[1]) && $order_id == $checkTransResult[1]) {
                        if (isset($checkTransResult[2]) && (int)$vtc_payment_result_row['amount'] == $checkTransResult[2]) {
                            $this->check_trans_status_flag = true;
                            $status = $checkTransResult[0];
                            $reason = $this->reasonStatusCodeParser($status);
                        } else {
                            $status = 'ERR';
                            $reason = 'Amount mismatched.';
                        }
                    } else {
                        $status = 'ERR';
                        $reason = 'Order ID mismatched.';
                    }
                } else {
                    $status = $checkTransResult[0];
                    $reason = $this->reasonStatusCodeParser($status);
                }
            }

            $vtcPaymentHistoryDataArray = array(
                'order_id' => $order_id,
                'status_code' => $status,
                'status_message' => $reason,
                'date' => date('Y-m-d H:i:s'),
                'changed_by' => (isset($login_email_address) && !empty($login_email_address)) ? $login_email_address : 'System'
            );
            tep_db_perform(TABLE_VTC_STATUS_HISTORY, $vtcPaymentHistoryDataArray);
            if ($status == '1' || $status == '2') {
                return true;
            } else {
                return false;
            }
        }
    }

    function get_merchant_account($selected_currency) {
        $payment_methods_instance_id = 0;
        $payment_gateway_instance_select_sql = "SELECT pmi.payment_methods_instance_id, pmi.payment_methods_instance_follow_default, pmi.currency_code 
				 								FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " as pmi
				 								WHERE pmi.payment_methods_id = '" . (int) $this->payment_methods_id . "'
				 								LIMIT 1";
        $payment_gateway_instance_result_sql = tep_db_query($payment_gateway_instance_select_sql);
        if (tep_db_num_rows($payment_gateway_instance_result_sql)) {
            $payment_gateway_instance_select_sql = "SELECT pmi.payment_methods_instance_id, pmi.payment_methods_instance_follow_default 
					 								FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " as pmi
					 								WHERE pmi.currency_code = '" . tep_db_input($selected_currency) . "' 
					 									AND pmi.payment_methods_id = '" . (int) $this->payment_methods_id . "'";
            $payment_gateway_instance_result_sql = tep_db_query($payment_gateway_instance_select_sql);
            if ($payment_gateway_instance_row = tep_db_fetch_array($payment_gateway_instance_result_sql)) {
                if ((int) $payment_gateway_instance_row['payment_methods_instance_follow_default'] > 0) { // Follow Default Currency Setting
                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_follow_default'];
                } else { // Has own setting
                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_id'];
                }
            }
        } else {
            $payment_gateway_instance_select_sql = "SELECT pmi.payment_methods_instance_id, pmi.payment_methods_instance_follow_default 
					 								FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " as pmi
					 								INNER JOIN " . TABLE_PAYMENT_METHODS . " as pm
					 									ON pm.payment_methods_parent_id = pmi.payment_methods_id
					 								WHERE pmi.currency_code = '" . tep_db_input($selected_currency) . "' 
					 									AND pm.payment_methods_id = '" . (int) $this->payment_methods_id . "'";
            $payment_gateway_instance_result_sql = tep_db_query($payment_gateway_instance_select_sql);
            if ($payment_gateway_instance_row = tep_db_fetch_array($payment_gateway_instance_result_sql)) {
                if ((int) $payment_gateway_instance_row['payment_methods_instance_follow_default'] > 0) {
                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_follow_default'];
                } else {
                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_id'];
                }
            }
        }
        $payment_gateway_instance_setting_select_sql = "SELECT pmis.payment_methods_instance_setting_key, pmis.payment_methods_instance_setting_value 
						 								FROM " . TABLE_PAYMENT_METHODS_INSTANCE_SETTING . " as pmis
						 								WHERE pmis.payment_methods_instance_id = '" . (int) $payment_methods_instance_id . "'";
        $payment_gateway_instance_setting_result_sql = tep_db_query($payment_gateway_instance_setting_select_sql);
        while ($payment_gateway_instance_setting_row = tep_db_fetch_array($payment_gateway_instance_setting_result_sql)) {
            switch ($payment_gateway_instance_setting_row['payment_methods_instance_setting_key']) {
                case 'MODULE_PAYMENT_VTC_WEBSITE_ID':
                    $this->vtc_website_id = $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];
                    break;
                case 'MODULE_PAYMENT_VTC_RECEIVER_ACC':
                    $this->vtc_receiver_acc = $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];
                    break;
                case 'MODULE_PAYMENT_VTC_SECRET_KEY':
                    $this->vtc_secret_key = $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];
                    break;
                case 'MODULE_PAYMENT_TAX':
                    $this->tax = $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];
                    break;
            }
        }
    }
    function load_pm_setting($language_id = 1, $pm_id = '') {
        $pm_setting_array = array();
        //load value from DB
        if (tep_not_null($pm_id)) {
            $payment_method_id = $pm_id;
            $payment_methods_parent_id_select_sql = "	SELECT payment_methods_parent_id 
														FROM " . TABLE_PAYMENT_METHODS . " 
														WHERE payment_methods_id = '" . (int) $pm_id . "'";
            $payment_methods_parent_id_result_sql = tep_db_query($payment_methods_parent_id_select_sql);
            $payment_methods_parent_id_row = tep_db_fetch_array($payment_methods_parent_id_result_sql);
            $payment_gateway_id = $payment_methods_parent_id_row['payment_methods_parent_id'];
        } else {
            $payment_method_id = $this->payment_methods_id;
            $payment_gateway_id = $this->payment_methods_parent_id;
        }
        $payment_configuration_info_select_sql = "	SELECT pci.payment_configuration_info_key, pcid.payment_configuration_info_value
													FROM " . TABLE_PAYMENT_CONFIGURATION_INFO . " as pci
													LEFT JOIN " . TABLE_PAYMENT_CONFIGURATION_INFO_DESCRIPTION . " as pcid 
														ON pci.payment_configuration_info_id = pcid.payment_configuration_info_id
															AND pcid.languages_id = '" . (int) $language_id . "'
													WHERE pci.payment_methods_id = '" . (int) $payment_method_id . "'
													ORDER BY pci.payment_configuration_info_sort_order";
        $payment_configuration_info_result_sql = tep_db_query($payment_configuration_info_select_sql);
        if (tep_db_num_rows($payment_configuration_info_result_sql)) {
            while ($payment_configuration_info_row = tep_db_fetch_array($payment_configuration_info_result_sql)) {
                $pm_setting_array[$payment_configuration_info_row['payment_configuration_info_key']] = $payment_configuration_info_row['payment_configuration_info_value'];
            }
        } else if ((int) $payment_gateway_id > 0) { //load value from PG
            $payment_configuration_info_select_sql = "	SELECT pci.payment_configuration_info_key, pcid.payment_configuration_info_value
														FROM " . TABLE_PAYMENT_CONFIGURATION_INFO . " as pci
														LEFT JOIN " . TABLE_PAYMENT_CONFIGURATION_INFO_DESCRIPTION . " as pcid 
															ON pci.payment_configuration_info_id = pcid.payment_configuration_info_id
																AND pcid.languages_id = '" . (int) $language_id . "'
														WHERE pci.payment_methods_id = '" . (int) $payment_gateway_id . "'
														ORDER BY pci.payment_configuration_info_sort_order";
            $payment_configuration_info_result_sql = tep_db_query($payment_configuration_info_select_sql);
            while ($payment_configuration_info_row = tep_db_fetch_array($payment_configuration_info_result_sql)) {
                $pm_setting_array[$payment_configuration_info_row['payment_configuration_info_key']] = $payment_configuration_info_row['payment_configuration_info_value'];
            }
        }
        return $pm_setting_array;
    }
    function check() {
        if (!isset($this->_check)) {
            $payment_methods_select_sql = "	SELECT payment_methods_id
											FROM " . TABLE_PAYMENT_METHODS . " as pm
											WHERE pm.payment_methods_id = '" . (int) $this->payment_methods_id . "'";
            $payment_methods_result_sql = tep_db_query($payment_methods_select_sql);
            $this->_check = tep_db_num_rows($payment_methods_result_sql);
        }
        return $this->_check;
    }
    function install() {
        $install_configuration_flag = true;
        if ($this->payment_methods_id > 0) {
            if ($this->receive_status != 1) {
                $update_receive_status_sql_data = array("payment_methods_receive_status" => 1);
                tep_db_perform(TABLE_PAYMENT_METHODS, $update_receive_status_sql_data, 'update', "payment_methods_id = '" . (int) $this->payment_methods_id . "'");
            } else {
                $install_configuration_flag = false;
            }
        } else {
            $install_payment_gateway_data_sql = array('payment_methods_code' => $this->code,
                'payment_methods_send_status' => 0,
                'payment_methods_title' => $this->code,
                'payment_methods_types_id' => 0,
                'payment_methods_parent_id' => 0,
                'payment_methods_legend_color' => '#610B21',
                'payment_methods_sort_order' => 6,
                'payment_methods_filename' => $this->filename,
                'payment_methods_logo' => '',
                'date_added' => 'now()',
                'last_modified' => 'now()',
                'payment_methods_receive_status' => 1,
                'payment_methods_receive_status_mode' => 0
            );
            tep_db_perform(TABLE_PAYMENT_METHODS, $install_payment_gateway_data_sql);
            $this->payment_methods_id = tep_db_insert_id();
        }
        if ($install_configuration_flag) {
            $install_key_array = array();
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                'payment_configuration_info_title' => 'Set Order Status',
                'payment_configuration_info_key' => 'MODULE_PAYMENT_VTC_ORDER_STATUS_ID',
                'payment_configuration_info_description' => 'Set the status of orders made with this payment module to this value.',
                'payment_configuration_info_sort_order' => '1335',
                'set_function' => 'tep_cfg_pull_down_order_statuses(',
                'use_function' => 'tep_get_order_status_name',
                'date_added' => 'now()'
            ),
                'desc' => array('payment_configuration_info_value' => '1',
                    'languages_id' => 1
                )
            );

            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                'payment_configuration_info_title' => 'Set Order\'s "Processing" Status',
                'payment_configuration_info_key' => 'MODULE_PAYMENT_VTC_PROCESSING_STATUS_ID',
                'payment_configuration_info_description' => 'Set the initial processing status of orders made with this payment module to this value.',
                'payment_configuration_info_sort_order' => '1340',
                'set_function' => 'tep_cfg_pull_down_order_statuses(',
                'use_function' => 'tep_get_order_status_name',
                'date_added' => 'now()'
            ),
                'desc' => array('payment_configuration_info_value' => '7',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                'payment_configuration_info_title' => 'Payment Message',
                'payment_configuration_info_key' => 'MODULE_PAYMENT_VTC_MESSAGE',
                'payment_configuration_info_description' => 'Payment message will show up during checkout process (VTC).',
                'payment_configuration_info_sort_order' => '1350',
                'set_function' => 'tep_cfg_textarea(',
                'use_function' => '',
                'date_added' => 'now()'
            ),
                'desc' => array('payment_configuration_info_value' => 'Thank you for shopping at OffGamers.com.',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                'payment_configuration_info_title' => 'Payment Email Message',
                'payment_configuration_info_key' => 'MODULE_PAYMENT_VTC_EMAIL_MESSAGE',
                'payment_configuration_info_description' => 'This message will show up in order confirmation e-mail.',
                'payment_configuration_info_sort_order' => '1360',
                'set_function' => 'tep_cfg_textarea(',
                'use_function' => '',
                'date_added' => 'now()'
            ),
                'desc' => array('payment_configuration_info_value' => 'Thank you for shopping at OffGamers.com.',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                'payment_configuration_info_title' => 'Confirm Complete (in days)',
                'payment_configuration_info_key' => 'MODULE_PAYMENT_VTC_CONFIRM_COMPLETE',
                'payment_configuration_info_description' => 'The number of days in which an order made by this payment method is confirm completed.',
                'payment_configuration_info_sort_order' => '1265',
                'set_function' => '',
                'use_function' => '',
                'date_added' => 'now()'
            ),
                'desc' => array('payment_configuration_info_value' => '0',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                'payment_configuration_info_title' => 'Test Mode',
                'payment_configuration_info_key' => 'MODULE_PAYMENT_VTC_TEST_MODE',
                'payment_configuration_info_description' => 'Testing Mode?',
                'payment_configuration_info_sort_order' => '100',
                'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                'use_function' => '',
                'date_added' => 'now()'
            ),
                'desc' => array('payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                'payment_configuration_info_title' => 'Require Address Information',
                'payment_configuration_info_key' => 'MODULE_PAYMENT_VTC_MANDATORY_ADDRESS_FIELD',
                'payment_configuration_info_description' => 'Set address field as required info during checkout.',
                'payment_configuration_info_sort_order' => '1400',
                'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                'use_function' => '',
                'date_added' => 'now()'
            ),
                'desc' => array('payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );
            foreach ($install_key_array as $data) {
                tep_db_perform(TABLE_PAYMENT_CONFIGURATION_INFO, $data['info']);
                $payment_conf_id = tep_db_insert_id();
                $data['desc']['payment_configuration_info_id'] = (int) $payment_conf_id;
                tep_db_perform(TABLE_PAYMENT_CONFIGURATION_INFO_DESCRIPTION, $data['desc']);
            }
        }
        return 1;
    }
    function merchant_information_keys() {
        include_once(DIR_WS_CLASSES . "payment_methods.php");
        return array(
            'MODULE_PAYMENT_VTC_WEBSITE_ID' => array('field' => 'text', 'mapping' => 'MODULE_PAYMENT_VTC_WEBSITE_ID'),
            'MODULE_PAYMENT_VTC_RECEIVER_ACC' => array('field' => 'text', 'mapping' => 'MODULE_PAYMENT_VTC_RECEIVER_ACC'),
            'MODULE_PAYMENT_VTC_SECRET_KEY' => array('field' => 'text', 'mapping' => 'MODULE_PAYMENT_VTC_SECRET_KEY'),
            'MODULE_PAYMENT_TAX' => array("field" => 'select', 'mapping' => "MODULE_PAYMENT_TAX", "select" => payment_methods::get_tax_option())
        );
    }
    function get_orders_payment_info_file() {
        return DIR_FS_CATALOG_MODULES . 'payment/vtc/admin/orders.inc.php';
    }
    function get_orders_list_payment_info_file() {
        return DIR_FS_CATALOG_MODULES . 'payment/vtc/admin/orders_list.inc.php';
    }
    function reasonStatusCodeParser($code) {
        $reason = '';
        switch($code) {
            case 1:
                $reason = 'Transaction successful (instant payment)';
                break;
            case 2:
                $reason = 'Transaction successful (Custodian Payment)';
                break;
            case 402:
                $reason = 'website_id is not correct';
                break;
            case 403:
                $reason = 'transferred data is not correct';
                break;
            case 404:
                $reason = 'wrong signature';
                break;
            case 620:
                $reason = 'transaction is not exist';
                break;
            case 0:
                $reason = 'Initial transaction';
                break;
            case 625:
                $reason = 'Failure';
                break;
            case 699:
                $reason = 'Suspicious, waiting to process';
                break;
        }
        return $reason;
    }
}
?>