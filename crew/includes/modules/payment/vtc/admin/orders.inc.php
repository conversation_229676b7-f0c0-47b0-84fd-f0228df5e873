<?php
include_once(DIR_FS_CATALOG_MODULES . 'payment/vtc/admin/languages/' . $language . '/vtc.lng.php');
$vtc_trans_info_select_sql = "SELECT * FROM " . TABLE_VTC . " WHERE order_id ='" . (int)$oID . "'";
$vtc_trans_info_result_sql= tep_db_query($vtc_trans_info_select_sql);
$vtc_payment_status_history_info_select_sql = "SELECT * FROM " . TABLE_VTC_STATUS_HISTORY . " WHERE order_id = '" . $oID . "'";
$vtc_payment_status_history_info_result_sql = tep_db_query($vtc_payment_status_history_info_select_sql);
?>
<table border="0" width="100%" cellspacing="0" cellpadding="2">
	<?php ob_start();
	?>
	<tr>
		<td class="main">
			<table border="0" width="100%" cellspacing="0" cellpadding="2">
				<tr>
					<td class="main" width="12%" nowrap><b><?php echo ENTRY_PAYMENT_METHOD?></b></td>
					<td class="main">
						<div>
							<?php if((int)$order->info['payment_methods_id']>0) { ?><div class="<?php echo ((int)$payment_info_array[$order->info['payment_methods_id']]->confirm_complete_days>0?'rp_icon':'nrp_icon')?>"><b><?php echo ((int)$payment_info_array[$order->info['payment_methods_id']]->confirm_complete_days>0?'RP':'NRP')?></b></div> <?php } ?>
							<div style="width:30px; height:15px; background-color:<?php echo $payment_module_info['display_colour']?>; float:left; vertical-align:top; text-align:center;">&nbsp;</div>
							<div style="vertical-align: top">
								&nbsp;&nbsp;
								<?php
								if ($view_payment_details_permission) {
									if (tep_not_null($last_payment_method) && $last_payment_method['payment_methods_id'] != $order->info['payment_methods_id']) {
										echo '<span class="redIndicator">'.(tep_not_null($payment_module_info['payment_methods_parent_title'])?$payment_module_info['payment_methods_parent_title'] . ' - ' :'') . $payment_module_info['payment_methods_title'].'</span>';
									} else {
										echo (tep_not_null($payment_module_info['payment_methods_parent_title'])?$payment_module_info['payment_methods_parent_title'] . ' - ' :'') . $payment_module_info['payment_methods_title'];
									}

									if (tep_not_null($last_payment_method) && $last_payment_method['payment_methods_id'] != $order->info['payment_methods_id']) {
										echo '&nbsp;&nbsp;<span class="redIndicator">(Last Payment Method: ' . (tep_not_null($last_payment_method['parent_payment_methods_title'])? $last_payment_method['parent_payment_methods_title'] . ' - ' : '' ) . $last_payment_method['payment_methods_title'] . ')</span>';
									}
								} else {
									echo (tep_not_null($payment_module_info['payment_methods_parent_title'])?$payment_module_info['payment_methods_parent_title'] . ' - ' :'') . $payment_module_info['payment_methods_title'];
								}
								?>
							</div>
						</div>
					</td>
				</tr>
			</table>
		</td>
	</tr>
	<?php
	$payment_method_title_info = ob_get_contents();
	ob_end_clean();
	if (tep_db_num_rows($vtc_trans_info_result_sql) || tep_db_num_rows($vtc_payment_status_history_info_result_sql)) {
		$vtc_trans_info_row = tep_db_fetch_array($vtc_trans_info_result_sql);
		echo $payment_method_title_info;

		if (!$view_payment_details_permission) {
			;
		} else {
			?>
			<tr>
				<td class="main">
					<table border="0" width="100%" cellspacing="0" cellpadding="2">
						<tr valign="top">
							<td width="39%" nowrap>
								<table border="0" cellspacing="0" cellpadding="2">
									<tr>
										<td class="main" valign="top" nowrap><b><?php echo ENTRY_VTC_WEBSITE_ID?></b>&nbsp;</td>
										<td class="main" nowrap><?php echo $vtc_trans_info_row['website_id'] ?></td>
									</tr>
									<tr>
										<td class="main" valign="top" nowrap><b><?php echo ENTRY_VTC_AMOUNT?></b>&nbsp;</td>
										<td class="main" nowrap><?php echo $vtc_trans_info_row['amount']?></td>
									</tr>
									<tr>
										<td class="main" valign="top" nowrap><b><?php echo ENTRY_VTC_CURRENCY?></b>&nbsp;</td>
										<td class="main" nowrap><?php echo $vtc_trans_info_row['currency']?></td>
									</tr>
									<tr>
										<td class="main" valign="top" nowrap><b><?php echo ENTRY_VTC_STATUS?></b>&nbsp;</td>
										<td class="main" nowrap><?php echo $vtc_trans_info_row['status']?></td>
									</tr>
									<tr>
										<td class="main" valign="top" nowrap><b><?php echo ENTRY_VTC_TRAN_REF_NO?></b>&nbsp;</td>
										<td class="main" nowrap><?php echo $vtc_trans_info_row['tran_ref_no']?></td>
									</tr>
									<tr>
										<td class="main" valign="top" nowrap><b><?php echo ENTRY_VTC_METHOD?></b>&nbsp;</td>
										<td class="main" nowrap><?php echo $vtc_trans_info_row['method']?></td>
									</tr>
									<tr>
										<td class="main" valign="top" nowrap><b><?php echo ENTRY_VTC_MESSAGE?></b>&nbsp;</td>
										<td class="main" nowrap><?php echo $vtc_trans_info_row['message']?></td>
									</tr>
									<tr>
										<td class="main" valign="top" nowrap><b><?php echo ENTRY_VTC_RECEIVER_ACC?></b>&nbsp;</td>
										<td class="main" nowrap><?php echo $vtc_trans_info_row['receiver_acc']?></td>
									</tr>
									<tr>
										<td class="main" valign="top" nowrap><b><?php echo ENTRY_VTC_SIGN?></b>&nbsp;</td>
										<td class="main" nowrap><?php echo $vtc_trans_info_row['sign']?></td>
									</tr>
									<tr>
										<td class="main" valign="top" nowrap><b><?php echo ENTRY_VTC_CREATED_DATE?></b>&nbsp;</td>
										<td class="main" nowrap><?php echo $vtc_trans_info_row['date_added']?></td>
									</tr>
									<tr>
										<td class="main" valign="top" nowrap><b><?php echo ENTRY_VTC_LAST_MODIFIED?></b>&nbsp;</td>
										<td class="main" nowrap><?php echo $vtc_trans_info_row['date_modified']?></td>
									</tr>
									<tr>
										<td class="main" nowrap>
											<?php
											if ($order->info['orders_status'] == '1' || $order->info['orders_status'] == '7' || $order->info['orders_status'] == '2') {
												echo "&nbsp;&nbsp;";
												echo tep_draw_form('vtc_status_check_form', FILENAME_ORDERS, tep_get_all_get_params(), 'post');
												echo tep_draw_hidden_field('subaction', 'payment_action');
												echo tep_draw_hidden_field('payment_action', 'check_trans_status');
												echo tep_submit_button('Check Payment Status', 'Check Payment Status', 'name="vtcCheckTransStatusBtn"', 'inputButton');
												echo "</form>";
											}
											?>
										</td>
									</tr>
								</table>
							</td>
							<td>
								<table border="0" cellspacing="0" cellpadding="1">
									</tr>
									<tr>
										<td colspan="2">
											<table border="1" cellspacing="0" cellpadding="2">
												<tr>
													<td class="smallText" nowrap><b><?php echo TABLE_HEADING_VTC_DATE?></b></td>
													<td class="smallText" nowrap><b><?php echo ENTRY_VTC_STATUS_CODE?></b></td>
													<td class="smallText" nowrap><b><?php echo ENTRY_VTC_STATUS_MESSAGE?></b></td>
													<td class="smallText" nowrap><b><?php echo TABLE_HEADING_VTC_CHANGED_BY?></b></td>
												</tr>
												<?php		while ($vtc_payment_status_history_info_row = tep_db_fetch_array($vtc_payment_status_history_info_result_sql)) {
													echo ' 						<tr>
                							<td class="smallText" nowrap>'.$vtc_payment_status_history_info_row['date'].'</td>
                							<td class="smallText" nowrap>'.$vtc_payment_status_history_info_row['status_code'].'</td>
                							<td class="smallText" nowrap>'.$vtc_payment_status_history_info_row['status_message'].'</td>
                							<td class="smallText" nowrap>'.(tep_not_null($vtc_payment_status_history_info_row['changed_by'])?$vtc_payment_status_history_info_row['changed_by']:'&nbsp;').'</td>
                						</tr>';
												}
												?>
											</table>
										</td>
									</tr>
								</table>
							</td>
							<td>
								<table border="0" cellspacing="0" cellpadding="2">
									<tr>
										<td class="main" valign="top" nowrap><b><?php echo ENTRY_VTC_CURRENCY?></b>&nbsp;</td>
										<td class="main" nowrap>
											<?php
											if (strtolower($vtc_trans_info_row['currency']) == strtolower($order->info['currency'])) {
												echo $vtc_trans_info_row['currency'];
											} else {
												echo '<span class="redIndicator">'.$vtc_trans_info_row['currency'].'<br><span class="smallText">Does not match purchase currency.</span></span>';
											}
											?>
										</td>
									</tr>
									<tr>
										<td class="main" valign="top" nowrap><b><?php echo ENTRY_VTC_AMOUNT?></b>&nbsp;</td>
										<td class="main">
											<?php

											$vtcGrossAmt = number_format($vtc_trans_info_row['amount'], $currencies->currencies[$vtc_trans_info_row['currency']]['decimal_places'], $currencies->currencies[$vtc_trans_info_row['currency']]['decimal_point'], $currencies->currencies[$vtc_trans_info_row['currency']]['thousands_point']);
											$vtcAmountFormatted = $currencies->currencies[$vtc_trans_info_row['currency']]['symbol_left'] . $vtcGrossAmt . $currencies->currencies[$vtc_trans_info_row['currency']]['symbol_right'];
											if ($vtcAmountFormatted != strip_tags($order->order_totals['ot_total']['text']) ) {
												$mc_gross_display_text = '<span class="redIndicator">'.$vtcAmountFormatted.'<br><span class="smallText">Does not match purchase amount.</span></span>';
											} else {
												$mc_gross_display_text = $vtcAmountFormatted;
											}

											echo $mc_gross_display_text;
											?>
										</td>
									</tr>
									<tr>
										<td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
									</tr>
								</table>
							</td>
						</tr>
					</table>
				</td>
			</tr>
			<?php
		}
	} else {
		echo $payment_method_title_info;
		?>
		<tr>
			<td class="main" nowrap>
				<?
				if ($order->info['orders_status'] == '1' || $order->info['orders_status'] == '7' || $order->info['orders_status'] == '2') {
					echo "&nbsp;&nbsp;";
					echo tep_draw_form('vtc_status_check_form', FILENAME_ORDERS, tep_get_all_get_params(), 'post');
					echo tep_draw_hidden_field('subaction', 'payment_action');
					echo tep_draw_hidden_field('payment_action', 'check_trans_status');
					echo tep_submit_button('Check Payment Status', 'Check Payment Status', 'name=vtcCheckTransStatusBtn"', 'inputButton');
					echo "</form>";
				}
				?>
			</td>
		</tr>
		<?php
	}
	?>

</table>