<?
include_once(DIR_FS_CATALOG_MODULES . 'payment/vtc/admin/languages/' . $language . '/vtc.lng.php');
$vtc_trans_info_select_sql = "SELECT * FROM " . TABLE_VTC . " WHERE order_id = '" . $order_obj->orders_id . "'";
$vtc_trans_info_result_sql = tep_db_query($vtc_trans_info_select_sql);
?>
<table border="0" width="100%" cellspacing="0" cellpadding="2">
	<tr>
		<td class="subInvoiceBoxHeading">
			<div style="border-bottom: 1px solid #996600; border-top: 1px solid #996600; padding: 2px;">Payment Details</div>
		</td>
	</tr>
	<?
	if ($vtc_trans_info_row = tep_db_fetch_array($vtc_trans_info_result_sql)) {
		?>
		<tr>
			<td class="main">
				<table border="0" width="100%" cellspacing="0" cellpadding="2">
					<tr valign="top">
						<td width="50%" valign="top">
							<table border="0" cellspacing="0" cellpadding="2">
								<tr>
									<td class="invoiceRecords" valign="top" nowrap><?= ENTRY_VTC_CURRENCY ?></td>
									<td class="invoiceRecords" valign="top">:&nbsp;</td>
									<td class="invoiceRecords" nowrap><?= $vtc_trans_info_row['currency'] ?></td>
								</tr>
								<tr>
									<td class="invoiceRecords" valign="top" nowrap><?= ENTRY_VTC_AMOUNT ?></td>
									<td class="invoiceRecords" valign="top">:&nbsp;</td>
									<td class="invoiceRecords">
										<?php
										$vtcGrossAmt = number_format($vtc_trans_info_row['amount'], $currencies->currencies[$vtc_trans_info_row['currency']]['decimal_places'], $currencies->currencies[$vtc_trans_info_row['currency']]['decimal_point'], $currencies->currencies[$vtc_trans_info_row['currency']]['thousands_point']);
										$vtcAmountFormatted = $currencies->currencies[$vtc_trans_info_row['currency']]['symbol_left'] . $vtcGrossAmt . $currencies->currencies[$vtc_trans_info_row['currency']]['symbol_right'];
										echo $vtcAmountFormatted;
										?>
									</td>
								</tr>
							</table>
						</td>
						<td valign="top">
							<table border="0" cellspacing="0" cellpadding="2">
								<tr>
									<td class="invoiceRecords" valign="top" nowrap><?= ENTRY_VTC_WEBSITE_ID ?></td>
									<td class="invoiceRecords" valign="top">:&nbsp;</td>
									<td class="invoiceRecords"><?= (tep_not_null($vtc_trans_info_row['website_id']) ? $vtc_trans_info_row['website_id'] : '') ?></td>
								</tr>
								<tr>
									<td class="invoiceRecords" valign="top" nowrap><?= ENTRY_VTC_STATUS ?></td>
									<td class="invoiceRecords" valign="top">:&nbsp;</td>
									<td class="invoiceRecords"><?=$vtc_trans_info_row['status'] ?></td>
								</tr>
								<tr>
									<td class="invoiceRecords" valign="top" nowrap><?= ENTRY_VTC_RECEIVER_ACC ?></td>
									<td class="invoiceRecords" valign="top">:&nbsp;</td>
									<td class="invoiceRecords"><?= (tep_not_null($vtc_trans_info_row['receiver_acc']) ? $vtc_trans_info_row['receiver_acc'] : '') ?></td>
								</tr>
							</table>
						</td>
					</tr>
				</table>
			</td>
		</tr>
		<?
	} else {
		?>
		<tr>
			<td class="invoiceRecords">No further payment information is available.</td>
		</tr>
		<?
	}
	?>