<?
/*
  	$Id: wire_transfers.php, v1.0 2005/12/02 10:24:51
  	Author : <PERSON> (<EMAIL>)
  	Title: Wire Transfer Payment Module V1.0
	
  	Copyright (c) 2005 SKC Venture
	
  	Released under the GNU General Public License
*/

class wire_transfers
{
	var $code, $title, $description, $enabled;

	// class constructor
    function wire_transfers()
    {
      	global $order;
		
      	$this->code = 'wire_transfers';
      	$this->title = MODULE_PAYMENT_WIRE_TRANSFERS_TEXT_TITLE;
      	$this->display_title = MODULE_PAYMENT_WIRE_TRANSFERS_TEXT_DISPLAY_TITLE; 	// if title to be display for payment method is different from payment method value
      	$this->description = MODULE_PAYMENT_WIRE_TRANSFERS_TEXT_DESCRIPTION;
      	$this->sort_order = MODULE_PAYMENT_WIRE_TRANSFERS_SORT_ORDER;
      	$this->enabled = ((MODULE_PAYMENT_WIRE_TRANSFERS_STATUS == 'True') ? true : false);
		$this->preferred = true;
		
		if ((int)MODULE_PAYMENT_WIRE_TRANSFERS_ORDER_STATUS_ID > 0) {
        	$this->order_status = MODULE_PAYMENT_WIRE_TRANSFERS_ORDER_STATUS_ID;
      	}
      	
      	if (is_object($order)) $this->update_status();
    	
      	$this->email_footer = MODULE_PAYMENT_WIRE_TRANSFERS_TEXT_EMAIL_FOOTER;
      	
      	$this->wireCurrencies = array('USD', 'GBP', 'EUR', 'AUD', 'CAD', 'HKD', 'JPY', 'NZD', 'SGD', 'CHF', 'THB');
      	
      	$this->legend_display_colour = MODULE_PAYMENT_WIRE_TRANSFERS_LEGEND_COLOUR;
      	$this->order_processing_status = (int)MODULE_PAYMENT_WIRE_TRANSFERS_PROCESSING_STATUS_ID > 0 ? MODULE_PAYMENT_WIRE_TRANSFERS_PROCESSING_STATUS_ID : 7;
      	$this->processing_payment_notification_mail = MODULE_PAYMENT_WIRE_TRANSFERS_NOTIFICATION_EMAIL;	// When to trigger is based on $this->order_processing_status.
      	
      	$this->confirm_complete_days = MODULE_PAYMENT_WIRE_TRANSFERS_CONFIRM_COMPLETE;
      	$this->auto_cancel_period = 17280;	// In minutes
	}
	
	// class methods
    function update_status()
    {
      	global $order;
		
      	if ( ($this->enabled == true) && ((int)MODULE_PAYMENT_WIRE_TRANSFERS_ZONE > 0) ) {
        	$zone_check_select_sql = "SELECT zone_id FROM " . TABLE_ZONES_TO_GEO_ZONES . " WHERE geo_zone_id = '" . MODULE_PAYMENT_WIRE_TRANSFERS_ZONE . "' AND (zone_country_id = '" . $order->billing['country']['id'] . "' OR zone_country_id = 0) AND IF (zone_country_id = 0, 1, (zone_id LIKE '%,".$order->billing['zone_id'].",%' OR zone_id LIKE '%,0,%'))";
        	
        	$zone_check_result_sql = tep_db_query($zone_check_select_sql);
        	$zone_check_row = tep_db_fetch_array($zone_check_result_sql);
        	
        	$check_flag = (tep_not_null($zone_check_row["zone_id"])) ? true : false;
        	
        	if ($check_flag == false) {
          		//$this->enabled = false;
          		$this->preferred = false;
        	}
      	}
	}
	
    function javascript_validation()
    {
      	return false;
    }
	
    function selection()
    {
    	$pm_logo = tep_image(DIR_WS_MODULES .'payment/wire_transfers/images/wire_transfers_logo.gif', ' '.$this->display_title.' ','','','align="absmiddle"');
    	$box_logo = tep_image(DIR_WS_MODULES .'payment/wire_transfers/images/box_wiretransfer.gif', ' '.$this->display_title.' ','','','align="absmiddle"');

      	return array(	'id' => $this->code,
      			   		'display_module' => $this->display_title,
      			   		'logo' => $pm_logo,
      			   		'box_logo' => $box_logo,
                   		'module' => $this->title,
                   		'currency' => $this->wireCurrencies);
    }
	
	function get_pm_info() {
		return MODULE_PAYMENT_WIRE_TRANSFERS_TEXT_CONFIRMATION;
	}
	
	function get_email_pm_info() {
		return MODULE_PAYMENT_WIRE_TRANSFERS_EMAIL_MESSAGE;
	}
	
	function draw_confirm_button() {
		return tep_image_button2('red', 'javascript:document.checkout_confirmation.submit()', MODULE_PAYMENT_WIRE_TRANSFERS_TEXT_CART, 200);
	}
	
	function get_confirm_button_caption() {
		return MODULE_PAYMENT_WIRE_TRANSFERS_TEXT_TITLE;
	}
	
    function pre_confirmation_check()
    {
      	global $currency, $currencies, $messageStack;
    	$supported_currency_str_array = array();
    	
    	if (!in_array($currency, $this->wireCurrencies)) {
    		for ($cur_cnt=0; $cur_cnt < count($this->wireCurrencies); $cur_cnt++) {
    			if (isset($currencies->currencies[$this->wireCurrencies[$cur_cnt]])) {
    				$supported_currency_str_array[] = $currencies->currencies[$this->wireCurrencies[$cur_cnt]]['title'];
    			}
    		}
    		
      		$messageStack->add_session('add_to_cart', sprintf(ERROR_PAYMENT_CURRENCY_NOT_SUPPORTED, $this->title, implode(', ', $supported_currency_str_array)), 'error');
        	tep_redirect(tep_href_link(FILENAME_SHOPPING_CART));
      	}
    }
    
    function confirmation()
    {
      	return array('title' => MODULE_PAYMENT_WIRE_TRANSFERS_TEXT_CONFIRMATION);
    }
	
    function process_button()
    {
      	return false;
    }
	
    function before_process()
    {
      	return false;
    }
	
    function after_process()
    {
      	return false;
    }
    
	function is_supported_currency($selected_currency) {
		return (in_array($selected_currency, $this->wireCurrencies) ? true : false);
    }
    
    function get_error()
    {
      	return false;
    }
	
    function check()
    {
      	if (!isset($this->_check)) {
        	$check_query = tep_db_query("select configuration_value from " . TABLE_CONFIGURATION . " where configuration_key = 'MODULE_PAYMENT_WIRE_TRANSFERS_STATUS'");
        	$this->_check = tep_db_num_rows($check_query);
      	}
      	return $this->_check;
    }
	
    function install()
    {
      	tep_db_query("insert into " . TABLE_CONFIGURATION . " (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, set_function, date_added) values ('Enable Wire Transfers Module', 'MODULE_PAYMENT_WIRE_TRANSFERS_STATUS', 'False', 'Do you want to accept Wire Transfers payments?', '6', '800', 'tep_cfg_select_option(array(\'True\', \'False\'), ', now());");
      	tep_db_query("insert into " . TABLE_CONFIGURATION . " (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, date_added) values ('Payment Info Notification Email Address', 'MODULE_PAYMENT_WIRE_TRANSFERS_NOTIFICATION_EMAIL', '', 'Email address to which the notification email will be send to when payment information is entered.<br>(In \"Name &lt;Email&gt;\" format. Use \',\' as delimiter for multiple recipient)', '6', '815', now());");
      	tep_db_query("insert into " . TABLE_CONFIGURATION . " (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, use_function, set_function, date_added) values ('Payment Zone', 'MODULE_PAYMENT_WIRE_TRANSFERS_ZONE', '0', 'If a zone is selected, only enable this payment method for that zone.', '6', '820', 'tep_get_zone_class_title', 'tep_cfg_pull_down_zone_classes(', now())");
      	tep_db_query("insert into " . TABLE_CONFIGURATION . " (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, date_added) values ('Sort order of display.', 'MODULE_PAYMENT_WIRE_TRANSFERS_SORT_ORDER', '15', 'Sort order of display. Lowest is displayed first.', '6', '825', now())");
      	tep_db_query("insert into " . TABLE_CONFIGURATION . " (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, set_function, use_function, date_added) values ('Set Order Status', 'MODULE_PAYMENT_WIRE_TRANSFERS_ORDER_STATUS_ID', '0', 'Set the status of orders made with this payment module to this value', '6', '830', 'tep_cfg_pull_down_order_statuses(', 'tep_get_order_status_name', now())");
      	tep_db_query("insert into " . TABLE_CONFIGURATION . " (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, use_function, set_function, date_added) values ('Set Order\'s \"Processing\" Status', 'MODULE_PAYMENT_WIRE_TRANSFERS_PROCESSING_STATUS_ID', '7', 'Set the initial processing status of orders made with this payment module to this value', '6', '835', 'tep_get_order_status_name', 'tep_cfg_pull_down_order_statuses(', now())");
      	
      	tep_db_query("insert into " . TABLE_CONFIGURATION . " (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, set_function, date_added) values ('Payment Message', 'MODULE_PAYMENT_WIRE_TRANSFERS_MESSAGE', 'Thank you for shopping at OffGamers.com.', 'Payment message will show up during checkout process (Wire Transfers)', '6', '845', 'tep_cfg_textarea(', now())");
      	tep_db_query("insert into " . TABLE_CONFIGURATION . " (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, use_function, set_function, date_added) values ('Payment Email Message', 'MODULE_PAYMENT_WIRE_TRANSFERS_EMAIL_MESSAGE', 'Thank you for shopping at OffGamers.com.', 'This message will show up in order confirmation e-mail.', '6', '850', NULL, 'tep_cfg_textarea(', now())");
      	
      	tep_db_query("insert into " . TABLE_CONFIGURATION . " (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, set_function, date_added) values ('Legend Display Colour', 'MODULE_PAYMENT_WIRE_TRANSFERS_LEGEND_COLOUR', '#FF6600', 'Colour to indicate Wire Transfers payment method (default is grey)', '6', '855', 'tep_cfg_colour_palette(', now())");
      	
      	$select_sql = "	SELECT configuration_id 
						FROM " . TABLE_CONFIGURATION . "
						WHERE configuration_key='MODULE_PAYMENT_WIRE_TRANSFERS_CONFIRM_COMPLETE' " ;
		$result_sql = tep_db_query($select_sql);
		if ($row_sql = tep_db_fetch_array($result_sql)) {
			tep_db_query("UPDATE " . TABLE_CONFIGURATION . " SET configuration_value='0' WHERE configuration_key='MODULE_PAYMENT_WIRE_TRANSFERS_CONFIRM_COMPLETE'");
		} else {
      		tep_db_query("insert into " . TABLE_CONFIGURATION . " (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, set_function, date_added) values ('Confirm Complete (in days)', 'MODULE_PAYMENT_WIRE_TRANSFERS_CONFIRM_COMPLETE', '0', 'The number of days in which an order made by this payment method is confirm completed', '6', '860', NULL, now())");
      	}
    }
	
    function remove()
    {
      	tep_db_query("delete from " . TABLE_CONFIGURATION . " where configuration_key in ('" . implode("', '", $this->keys()) . "')");
    }
	
    function keys()
    {
      	return array(	'MODULE_PAYMENT_WIRE_TRANSFERS_STATUS',
      					'MODULE_PAYMENT_WIRE_TRANSFERS_NOTIFICATION_EMAIL', 
      					'MODULE_PAYMENT_WIRE_TRANSFERS_ZONE', 
      					'MODULE_PAYMENT_WIRE_TRANSFERS_SORT_ORDER',
      				 	'MODULE_PAYMENT_WIRE_TRANSFERS_ORDER_STATUS_ID', 
      				 	'MODULE_PAYMENT_WIRE_TRANSFERS_PROCESSING_STATUS_ID', 
      				 	'MODULE_PAYMENT_WIRE_TRANSFERS_CONFIRM_COMPLETE', 
      				 	'MODULE_PAYMENT_WIRE_TRANSFERS_MESSAGE', 
      				 	'MODULE_PAYMENT_WIRE_TRANSFERS_EMAIL_MESSAGE', 
      				 	'MODULE_PAYMENT_WIRE_TRANSFERS_LEGEND_COLOUR');
    }
}
?>