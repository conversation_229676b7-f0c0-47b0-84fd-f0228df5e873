<?
/*
  	$Id: orders_list.inc.php,v 1.2 2007/02/13 07:36:00 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2005 SKC Venture
  	
  	Released under the GNU General Public License
*/
include_once(DIR_FS_CATALOG_MODULES . 'payment/wire_transfers/admin/languages/'.$language.'/wire_transfers.lng.php');

$trans_info_select_sql = "SELECT authorisation_result FROM " . TABLE_PAYMENT_EXTRA_INFO . " WHERE orders_id='" . tep_db_input($order_obj->orders_id) . "'";
$trans_info_result_sql= tep_db_query($trans_info_select_sql);
?>
<table border="0" width="100%" cellspacing="0" cellpadding="2">
	<tr>
		<td class="subInvoiceBoxHeading">
			<div style="border-bottom: 1px solid #996600; border-top: 1px solid #996600; padding: 2px;">Payment Details</div>
		</td>
	</tr>
<?
if ($trans_info_row = tep_db_fetch_array($trans_info_result_sql)) {
?>
  	<tr>
    	<td class="main">
      		<table border="0" width="100%" cellspacing="0" cellpadding="2">
      			<tr valign="top">
        			<td width="40%">
              			<table border="0" cellspacing="0" cellpadding="2">
              				<tr>
                				<td class="invoiceRecords" valign="top" nowrap><?=ENTRY_WIRE_TRANSFERS_PAYMENT_INFO?></td>
                				<td class="invoiceRecords" valign="top">:&nbsp;</td>
                				<td class="invoiceRecords" nowrap><?=nl2br($trans_info_row["authorisation_result"])?></td>
              				</tr>
              			</table>
        			</td>
      			</tr>
      		</table>
   		</td>
  	</tr>
<?
} else {
?>
	<tr>
		<td class="invoiceRecords"><?=ENTRY_WIRE_TRANSFERS_NO_PAYMENT_INFO?></td>
	</tr>
<?
}
?>
</table>