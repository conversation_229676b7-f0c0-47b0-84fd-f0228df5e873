<?
/*
  	$Id: orders.inc.php,v 1.4 2009/02/23 04:38:11 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2007 SKC Venture
  	
  	Released under the GNU General Public License
*/

include_once(DIR_FS_CATALOG_MODULES . 'payment/wm/admin/languages/' . $language . '/wm.lng.php');

$payment_mode_status = array(	'0' => 'Live',
								'1' => '<span class="redIndicator">Test</span>'
								);

$wm_trans_info_select_sql = "SELECT * FROM " . TABLE_PAYMENT_WEBMONEY . " WHERE webmoney_payment_no = '" . (int)$oID . "'";
$wm_trans_info_result_sql= tep_db_query($wm_trans_info_select_sql);
?>

<table border="0" width="100%" cellspacing="0" cellpadding="2">
<? ob_start(); ?>
	<tr>
		<td class="main">
      		<table border="0" width="100%" cellspacing="0" cellpadding="2">
      			<tr>
      				<td class="main" width="10%" nowrap><b><?=ENTRY_PAYMENT_METHOD?></b></td>
      				<td class="main">
      					<div>
      						<? if((int)$order->info['payment_methods_id']>0) { ?><div class="<?=((int)$payment_info_array[$order->info['payment_methods_id']]->confirm_complete_days>0?'rp_icon':'nrp_icon')?>"><b><?=((int)$payment_info_array[$order->info['payment_methods_id']]->confirm_complete_days>0?'RP':'NRP')?></b></div> <? } ?>
      						<div style="width:30px; height:15px; background-color:<?=$payment_module_info['display_colour']?>; float:left; vertical-align:top; text-align:center;">&nbsp;</div>
      						<div style="vertical-align: top">
      							&nbsp;&nbsp;
<?
	if ($view_payment_details_permission) {
		
		if (tep_not_null($last_payment_method) && $last_payment_method['payment_methods_id'] != $order->info['payment_methods_id']) {
			echo '<span class="redIndicator">'.(tep_not_null($payment_module_info['payment_methods_parent_title'])?$payment_module_info['payment_methods_parent_title'] . ' - ' :'') . $payment_module_info['payment_methods_title'].'</span>';
		} else {
			echo (tep_not_null($payment_module_info['payment_methods_parent_title'])?$payment_module_info['payment_methods_parent_title'] . ' - ' :'') . $payment_module_info['payment_methods_title'];
		}
		
		if (tep_not_null($last_payment_method) && $last_payment_method['payment_methods_id'] != $order->info['payment_methods_id']) {
			echo '&nbsp;&nbsp;<span class="redIndicator">(Last Payment Method: ' . (tep_not_null($last_payment_method['parent_payment_methods_title'])? $last_payment_method['parent_payment_methods_title'] . ' - ' : '' ) . $last_payment_method['payment_methods_title'] . ')</span>';
		}
	} else {
		echo (tep_not_null($payment_module_info['payment_methods_parent_title'])?$payment_module_info['payment_methods_parent_title'] . ' - ' :'') . $payment_module_info['payment_methods_title'];
	}
?>
							</div>
      					</div>
      				</td>
				</tr>
			</table>
		</td>
	</tr>
<?
$payment_method_title_info = ob_get_contents();
ob_end_clean();

if ($wm_trans_info_row = tep_db_fetch_array($wm_trans_info_result_sql)) {
	echo $payment_method_title_info;
	if (!$view_payment_details_permission) {
		;
	} else {
?>
  	<tr>
    	<td class="main">
      		<table border="0" width="100%" cellspacing="0" cellpadding="2">
      			<tr valign="top">
        			<td width="35%">
              			<table border="0" cellspacing="0" cellpadding="2">
              				<tr>
                				<td class="main" valign="top" nowrap><b><?=ENTRY_WM_PAYER_WM?></b>&nbsp;</td>
                				<td class="main" nowrap>
                				<?
                				if (tep_not_null($wm_trans_info_row["webmoney_payer_wm"])) {
                					$customer_with_this_id_array = tep_check_duplicate_payment_email($wm_trans_info_row["webmoney_payer_wm"], 'webmoney');	// including himself
                					$id_used_by_other = (count($customer_with_this_id_array) > 1) ? 1 : 0 ;
                					
                					$total_id_used_array = tep_get_distinct_payment_email_used ($cid, 'webmoney');
                					$total_id_used = (count($total_id_used_array) > 1) ? 1 : 0 ;
                					
	                				echo '<span class="'.(($id_used_by_other+$total_id_used > 0) ? 'redIndicator' : 'blackIndicator').'">' . $wm_trans_info_row["webmoney_payer_wm"] . '</span>';
	                				
	                				$id_alert_reason = '';
                					if ($id_used_by_other == 1) {
	                					$id_alert_reason .=	tep_draw_form('cust_lists_share_email', FILENAME_CUSTOMERS, 'action=show_report&customer_lists_subaction=do_search', 'post', 'target="_blank"') . "\n" . 
																tep_draw_hidden_field('customer_share_id', tep_array_serialize($customer_with_this_id_array)) . "\n" . 
																'<span class="redIndicator">WM ID exists in <a href="javascript: document.cust_lists_share_email.submit();"><u>' . count($customer_with_this_id_array) . '</u></a> profiles.</span><br>' . 
																'</form>' . "\n";
									}
									
									$id_alert_reason .= ($total_id_used == 1) ? '<span class="redIndicator"><a href="'.tep_href_link(FILENAME_ORDERS, 'cID='.$cid).'" target="_blank"><u>' . count($total_id_used_array) . '</u></a> WM ID used in WebMoney orders.</span><br>' : '';
	                				
	                				if (tep_not_null($id_alert_reason)) {
                						echo '<br><span class="smallText">' . $id_alert_reason . '</span>';
                					}
	                			}
                				?>
                				</td>
              				</tr>
              				<tr>
                				<td class="main" valign="top" nowrap><b><?=ENTRY_WM_PAYER_PURSE?></b>&nbsp;</td>
                				<td class="main"><?=$wm_trans_info_row["webmoney_payer_purse"]?></td>
              				</tr>
              				<tr>
                				<td class="main" valign="top" nowrap><b><?=ENTRY_WM_PAYEE_PURSE?></b>&nbsp;</td>
                				<td class="main" nowrap><?=$wm_trans_info_row["webmoney_payee_purse"]?></td>
              				</tr>
              				<tr>
                				<td class="main" valign="top" nowrap><b><?=ENTRY_WM_PAYMENT_MODE?></b>&nbsp;</td>
                				<td class="main"><?=$payment_mode_status[$wm_trans_info_row["webmoney_operative_mode"]]?></td>
              				</tr>
              			</table>
        			</td>
        			<td>
              			<table border="0" cellspacing="0" cellpadding="2">
              				<tr>
                				<td class="main" valign="top" nowrap><b><?=ENTRY_WM_INVOICE_ID?></b>&nbsp;</td>
                				<td class="main"><?=$wm_trans_info_row["webmoney_invoices_no"]?></td>
              				</tr>
              				<tr>
                				<td class="main" valign="top" nowrap><b><?=ENTRY_WM_TRANSACTION_ID?></b>&nbsp;</td>
                				<td class="main"><?=$wm_trans_info_row["webmoney_transaction_no"]?></td>
              				</tr>
              				<tr>
                				<td class="main" valign="top" nowrap><b><?=ENTRY_WM_REFERENCE_ID?></b>&nbsp;</td>
                				<td class="main"><?=$wm_trans_info_row["webmoney_payment_no"]?></td>
              				</tr>
              				<tr>
                				<td class="main" valign="top" nowrap><b><?=ENTRY_WM_AMOUNT?></b>&nbsp;</td>
                				<td class="main"><?=$wm_trans_info_row["webmoney_payment_amount"]?></td>
              				</tr>
              			</table>
              		</td>
      			</tr>
      		</table>
   		</td>
  	</tr>
<?
	}
} else {
	echo $payment_method_title_info;
}
?>
</table>