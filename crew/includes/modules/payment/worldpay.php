<?

/*
  $Id: worldpay.php,v4.1 Beta 2003/01/12 22:00:00
  Author : <PERSON> (<EMAIL>)/<PERSON> (<EMAIL>)
  Title: WorldPay Payment Module V4.0 Beta

  Revisions:
  Version 1.7 Minor changes to enable control of 'Payment Zone' added function update_status
  Version 1.6 Sorts out duplicate session creation on re-entry to host server.
  Version 1.5 Cleaned up code, moved static English to language file to allow for bi-lingual use,
  Now posting language code to WP, Redirect on failure now to Checkout Payment,
  Reduced re-direct time to 8 seconds, added MD5, made callback dynamic
  NOTE: YOU MUST CHANGE THE CALLBACK URL IN WP ADMIN TO <wpdisplay item="MC_callback">
  Version 1.4 Removes boxes to prevent users from clicking away before update,
  Fixes currency for Yen,
  Redirects to Checkout_Process after 10 seconds or click by user
  Version 1.3 Fixes problem with Multi Currency
  Version 1.2 Added Sort Order and Default order status to work with snapshots after 14 Jan 2003
  Version 1.1 Added Worldpay Pre-Authorisation ability
  Version 1.0 Initial Payment Module

  NOTE: IF UPGRADING YOU MUST CHANGE THE CALLBACK URL IN WP ADMIN TO:
  <wpdisplay item="MC_callback">

  PLUS add: define('FILENAME_WPCALLBACK', 'wpcallback.php'); To catalog/includes/filenames.php
  PLUS add replace all files including the new wpcallback.php language file.

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2002 osCommerce

  Released under the GNU General Public License
 */

class worldpay {

    var $code, $title, $description, $enabled;
    var $payment_methods_id, $filename;

    // class constructor
    function worldpay($pm_id = '') {
        global $order, $languages_id, $default_languages_id, $defCurr;

        //Basic info
        $this->payment_methods_id = 0;
        $this->payment_methods_parent_id = 0;

        $this->code = 'worldpay';
        $this->filename = 'worldpay.php';
        $this->title = $this->code;

        $this->receive_status = 0;
        $this->preferred = true;

        $payment_methods_select_sql = "	SELECT payment_methods_parent_id, payment_methods_code, 
												payment_methods_types_id, 
												payment_methods_receive_status_mode, payment_methods_id, 
												payment_methods_title, payment_methods_sort_order, 
												payment_methods_receive_status, payment_methods_legend_color, 
												payment_methods_logo 
										FROM " . TABLE_PAYMENT_METHODS . " 
										WHERE 1 ";
        if ((int) $pm_id > 0) {
            $payment_methods_select_sql .= "	AND payment_methods_id = '" . (int) $pm_id . "'";
        } else {
            $payment_methods_select_sql .= "	AND payment_methods_filename = '" . tep_db_input($this->filename) . "'";
        }

        $payment_methods_result_sql = tep_db_query($payment_methods_select_sql);
        if ($payment_methods_row = tep_db_fetch_array($payment_methods_result_sql)) {
            $pm_desc_title_select_sql = "	SELECT payment_methods_description_title 
											FROM " . TABLE_PAYMENT_METHODS_DESCRIPTION . " 
											WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "' 
												AND (IF (languages_id = '" . tep_db_input($languages_id) . "', 1, IF ((	SELECT COUNT(payment_methods_id) > 0 
																														FROM " . TABLE_PAYMENT_METHODS_DESCRIPTION . " 
																														WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "' 
																															AND languages_id = '" . (int) $languages_id . "'), 0, languages_id = 1)))";
            $pm_desc_title_result_sql = tep_db_query($pm_desc_title_select_sql);
            $pm_desc_title_row = tep_db_fetch_array($pm_desc_title_result_sql);

            $this->payment_methods_id = (int) $payment_methods_row['payment_methods_id'];
            $this->payment_methods_parent_id = (int) $payment_methods_row['payment_methods_parent_id'];
            $this->code = $payment_methods_row['payment_methods_code'];
            $this->title = $payment_methods_row['payment_methods_title']; //MODULE_PAYMENT_PAYPAL_TEXT_TITLE;
            $this->display_title = $pm_desc_title_row['payment_methods_description_title']; //MODULE_PAYMENT_PAYPAL_TEXT_DISPLAY_TITLE; 	// if title to be display for payment method is different from payment method value
            $this->sort_order = $payment_methods_row['payment_methods_sort_order']; //MODULE_PAYMENT_PAYPAL_SORT_ORDER;
            $this->enabled = $payment_methods_row['payment_methods_receive_status_mode'];
            $this->legend_display_colour = $payment_methods_row['payment_methods_legend_color'];
            $this->receive_status = $payment_methods_row['payment_methods_receive_status'];
            $this->description = $this->display_title;
            $this->wpCurrencies = $this->get_support_currencies($this->payment_methods_id);
            $this->defCurr = DEFAULT_CURRENCY;

            $configuration_setting_array = $this->load_pm_setting();

            $this->manual_auth_notification_email = $configuration_setting_array['MODULE_PAYMENT_WORLDPAY_MANUAL_AUTH_NOTIFICATION_EMAIL'];
            $this->card_types = $configuration_setting_array['MODULE_PAYMENT_WORLDPAY_CARD_TYPES'];
            $this->confirm_complete_days = $configuration_setting_array['MODULE_PAYMENT_WORLDPAY_CONFIRM_COMPLETE'];
            $this->show_card_types = $configuration_setting_array['MODULE_PAYMENT_WORLDPAY_SHOW_CARD_TYPES'];
            $this->email_message = $configuration_setting_array['MODULE_PAYMENT_WORLDPAY_EMAIL_MESSAGE'];
            $this->post_auth_time_out = $configuration_setting_array['MODULE_PAYMENT_WORLDPAY_POST_AUTH_TIME_OUT'];
            $this->message = $configuration_setting_array['MODULE_PAYMENT_WORLDPAY_MESSAGE'];
            $this->billing_address = $configuration_setting_array['MODULE_PAYMENT_WORLDPAY_BILLING_ADDRESS'];
            $this->card_owner = $configuration_setting_array['MODULE_PAYMENT_WORLDPAY_CARD_OWNER'];
            $this->mode = $configuration_setting_array['MODULE_PAYMENT_WORLDPAY_MODE'];
            $this->usepreauth = $configuration_setting_array['MODULE_PAYMENT_WORLDPAY_USEPREAUTH'];
            $this->preauth = $configuration_setting_array['MODULE_PAYMENT_WORLDPAY_PREAUTH'];
            $this->order_status_id = $configuration_setting_array['MODULE_PAYMENT_WORLDPAY_ORDER_STATUS_ID'];
            $this->require_address_information = $configuration_setting_array['MODULE_PAYMENT_WORLDPAY_MANDATORY_ADDRESS_FIELD'];
            $this->require_ic_information = $configuration_setting_array['MODULE_PAYMENT_WORLDPAY_MANDATORY_IC_FIELD'];
            $this->require_contact_information = $configuration_setting_array['MODULE_PAYMENT_WORLDPAY_MANDATORY_CONTACT_FIELD'];

            $this->customer_payment_info = $configuration_setting_array['MODULE_PAYMENT_WORLDPAY_CUSTOMER_PAYMENT_INFO'];
            $this->usemd5 = $configuration_setting_array['MODULE_PAYMENT_WORLDPAY_USEMD5'];
            //$this->zone = $configuration_setting_array['MODULE_PAYMENT_WORLDPAY_ZONE'];

            if ((int) $this->order_status_id > 0) {
                $this->order_status = $this->order_status_id;
            }

            //if (is_object($order)) $this->update_status();

            $this->email_footer = str_replace(array("<br>", "<BR>", "\r\n"), array("\n", "\n", ''), $this->message);
            $this->order_processing_status = 7;
            $this->post_auth_time_out = (int) $this->post_auth_time_out;
            $this->auto_cancel_period = 60; // In minutes
        }

        $this->force_to_checkoutprocess = true;
        $this->form_action_url = 'https://select.worldpay.com/wcc/purchase';
    }

    function javascript_validation() {
        return false;
    }

    function selection() {
        global $languages_id, $default_languages_id;

        $selection_array = array();

        $payment_methods_select_sql = "	SELECT payment_methods_id, payment_methods_code, payment_methods_types_id, payment_methods_logo, payment_methods_receive_status_mode 
										FROM " . TABLE_PAYMENT_METHODS . " 
										WHERE payment_methods_receive_status = 1 
											AND payment_methods_receive_status_mode <> 0 
											AND payment_methods_parent_id = '" . (int) $this->payment_methods_id . "' 
										ORDER BY payment_methods_sort_order";
        $payment_methods_result_sql = tep_db_query($payment_methods_select_sql);
        while ($payment_methods_row = tep_db_fetch_array($payment_methods_result_sql)) {
            $pm_desc_title_select_sql = "	SELECT payment_methods_description_title 
											FROM " . TABLE_PAYMENT_METHODS_DESCRIPTION . " 
											WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "' 
												AND (IF (languages_id = '" . tep_db_input($languages_id) . "', 1, IF ((	SELECT COUNT(payment_methods_id) > 0 
																														FROM " . TABLE_PAYMENT_METHODS_DESCRIPTION . " 
																														WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "' 
																															AND languages_id = '" . (int) $languages_id . "'), 0, languages_id = 1)))";
            $pm_desc_title_result_sql = tep_db_query($pm_desc_title_select_sql);
            $pm_desc_title_row = tep_db_fetch_array($pm_desc_title_result_sql);

            $pm_array = $this->load_pm_setting(1, $payment_methods_row['payment_methods_id']);

            $selection_array[] = array('payment_gateway_code' => $this->code,
                'payment_methods_receive_status_mode' => $payment_methods_row['payment_methods_receive_status_mode'],
                'payment_methods_id' => $payment_methods_row['payment_methods_id'],
                'payment_methods_code' => $payment_methods_row['payment_methods_code'],
                'payment_methods_types_id' => $payment_methods_row['payment_methods_types_id'],
                'payment_methods_parent_id' => $this->payment_methods_id,
                'payment_methods_logo' => $payment_methods_row['payment_methods_logo'],
                'payment_methods_description_title' => $pm_desc_title_row['payment_methods_description_title'],
                'currency' => $this->get_support_currencies($payment_methods_row['payment_methods_id']),
                'confirm_complete_days' => $pm_array['MODULE_PAYMENT_WORLDPAY_CONFIRM_COMPLETE'],
                'show_billing_address' => $pm_array['MODULE_PAYMENT_WORLDPAY_MANDATORY_ADDRESS_FIELD'],
                'show_contact_number' => $pm_array['MODULE_PAYMENT_WORLDPAY_MANDATORY_CONTACT_FIELD'],
                'show_ic' => $pm_array['MODULE_PAYMENT_WORLDPAY_MANDATORY_IC_FIELD']
            );

            if ($payment_methods_row['payment_methods_receive_status_mode'] == '-1') {
                $pm_status_message_select = "	SELECT payment_methods_status_message 
												FROM " . TABLE_PAYMENT_METHODS_STATUS_DESCRIPTION . " 
												WHERE payment_methods_mode = 'RECEIVE' 
													AND payment_methods_status = '-1' 
													AND languages_id = '" . (int) $languages_id . "' 
													AND payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "'";
                $pm_status_message_result = tep_db_query($pm_status_message_select);
                $pm_status_message_row = tep_db_fetch_array($pm_status_message_result);
                $selection_array[sizeof($selection_array) - 1]['payment_methods_status_message'] = $pm_status_message_row['payment_methods_status_message'];
            }
        }

        return $selection_array;
    }

    function set_support_currencies($currency_array) {
        $this->wpCurrencies = $currency_array;
    }

    function get_support_currencies($pm_id, $by_zone = true) {
        global $zone_info_array;

        $default_currency = '';
        $support_currencies_array = array();

        $currency_code_select_sql = "	SELECT currency_code, payment_methods_instance_default 
    									FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " 
    									WHERE payment_methods_id = '" . (int) $pm_id . "' 
    									ORDER BY currency_code";
        $currency_code_result_sql = tep_db_query($currency_code_select_sql);

        if (tep_db_num_rows($currency_code_result_sql) > 0) {
            while ($currency_code_row = tep_db_fetch_array($currency_code_result_sql)) {
                $support_currencies_array[] = $currency_code_row['currency_code'];

                if ($currency_code_row['payment_methods_instance_default'] == 1) {
                    $default_currency = $currency_code_row['currency_code'];
                }
            }
        } else {
            if ($this->payment_methods_parent_id > 0) {
                $payment_methods_parent_id = $this->payment_methods_parent_id;
            } else {
                $payment_methods_parent_id = $this->payment_methods_id;
            }

            $parent_currency_code_select_sql = "	SELECT currency_code, payment_methods_instance_default 
			    									FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " 
			    									WHERE payment_methods_id = '" . (int) $payment_methods_parent_id . "' 
			    									ORDER BY currency_code";
            $parent_currency_code_result_sql = tep_db_query($parent_currency_code_select_sql);
            while ($parent_currency_code_row = tep_db_fetch_array($parent_currency_code_result_sql)) {
                $support_currencies_array[] = $parent_currency_code_row['currency_code'];

                if ($parent_currency_code_row['payment_methods_instance_default'] == 1) {
                    $default_currency = $parent_currency_code_row['currency_code'];
                }
            }
        }

        if (tep_not_null($default_currency)) {
            $this->defCurr = $default_currency;
        }

        if ($by_zone) {
            if (isset($zone_info_array)) {
                $support_currencies_array = array_intersect($zone_info_array[3]->zone_currency_id, $support_currencies_array);
                if (count($support_currencies_array)) {
                    array_multisort($support_currencies_array);
                } else {
                    $support_currencies_array = array($default_currency);
                }
            } else {
                $support_currencies_array = array($default_currency);
            }
        } else {
            $this->set_support_currencies($support_currencies_array);
            // All found currencies is supported. Normally this is used for backend script such as IPN call
        }

        return $support_currencies_array;
    }

    function get_require_address_information() {
        return $this->require_address_information;
    }

    function get_confirm_complete_days() {
        return $this->confirm_complete_days;
    }

    function get_pm_info() {
        return $this->message;
    }

    function get_email_pm_info() {
        return $this->email_message;
    }

    function draw_confirm_button() {
        return tep_image_button2('red', 'javascript:document.checkout_confirmation.submit()', MODULE_PAYMENT_WORLDPAY_TEXT_CART, 200);
    }

    function get_confirm_button_caption() {
        return MODULE_PAYMENT_WORLDPAY_TEXT_TITLE;
    }

    function pre_confirmation_check() {
        global $currency;

        if (!in_array($currency, $this->wpCurrencies)) {
            
        }

        return false;
    }

    function process_button() {
        global $HTTP_POST_VARS, $shipping_cost, $total_cost, $shipping_selected, $shipping_method, $currencies, $currency, $customer_id, $order, $languages_id;
        global $order_logged, $OFFGAMERS;

        // added by wei chen
        if ($this->force_to_checkoutprocess && !tep_session_is_registered('order_logged'))
            return;
        // end added by wei chen
        // change this to order id
        //$worldpay_url = tep_session_name() . '=' . tep_session_id() ;
        $worldpay_url = $order_logged;

        $wpCurrency = $currency;
        if (!in_array($wpCurrency, $this->wpCurrencies)) {
            $wpCurrency = in_array(DEFAULT_CURRENCY, $this->wpCurrencies) ? DEFAULT_CURRENCY : $this->wpCurrencies[0];
        }
        $this->get_merchant_account($wpCurrency);
        // Multi Currency 
        $OrderAmt = number_format($order->info['total'] * $currencies->get_value($wpCurrency), $currencies->get_decimal_places($wpCurrency), '.', '');
        // Multi Currency 
        //$cart->cartID
        $process_button_string =
                tep_draw_hidden_field('instId', $this->worldpay_id) .
                tep_draw_hidden_field('currency', $wpCurrency) .
                tep_draw_hidden_field('hideCurrency', '1') .
                tep_draw_hidden_field('desc', 'Purchase from ' . STORE_NAME) .
                tep_draw_hidden_field('cartId', $worldpay_url) .
                tep_draw_hidden_field('amount', $OrderAmt) .
                tep_draw_hidden_field('paymentType', $this->code);

        if (MODULE_PAYMENT_WORLDPAY_USEPREAUTH == 'True')
            $process_button_string .= tep_draw_hidden_field('authMode', MODULE_PAYMENT_WORLDPAY_PREAUTH);

        $firstname = $order->billing['firstname'];
        $lastname = $order->billing['lastname'];

        if (ENABLE_SSL) {
            if (HTTP_SERVER != HTTPS_SERVER) {
                // if servers are different use the https one to create the callback url
                $callback_url = tep_href_link(FILENAME_WPCALLBACK, 'OFFGAMERS=' . $OFFGAMERS, 'SSL', false);
                $worldpay_callback = explode('https://', $callback_url);
            } else {
                // otherwise use the http
                $callback_url = tep_href_link(FILENAME_WPCALLBACK, 'OFFGAMERS=' . $OFFGAMERS);
                $worldpay_callback = explode('http://', $callback_url);
            }
        } else {
            $callback_url = tep_href_link(FILENAME_WPCALLBACK, 'OFFGAMERS=' . $OFFGAMERS);
            $worldpay_callback = explode('http://', $callback_url);
        }

        $language_code_raw = tep_db_query("select code from " . TABLE_LANGUAGES . " where languages_id ='$languages_id'");
        $language_code_array = tep_db_fetch_array($language_code_raw);
        $language_code = $language_code_array['code'];

        $address = htmlspecialchars($order->billing['street_address'] . "\n" . $order->billing['suburb'] . "\n" . $order->billing['city'] . "\n" . $order->billing['state'], ENT_QUOTES);
        $process_button_string .=
                tep_draw_hidden_field('testMode', $this->mode) .
                tep_draw_hidden_field('name', $firstname . ' ' . $lastname) .
                tep_draw_hidden_field('address', $address) .
                tep_draw_hidden_field('postcode', $order->billing['postcode']) .
                tep_draw_hidden_field('country', $order->billing['country']['iso_code_2']) .
                tep_draw_hidden_field('tel', "+" . $order->customer['int_dialing_code'] . ' ' . $order->customer['telephone']) .
                tep_draw_hidden_field('fax', $order->customer['fax']) .
                tep_draw_hidden_field('email', $order->customer['email_address']) .
                //Added dynamic callback and languages link
                tep_draw_hidden_field('lang', $language_code) .
                tep_draw_hidden_field('MC_callback', $callback_url) .
                tep_draw_hidden_field('MC_oGM', $OFFGAMERS);

        // Added MD5
        if ($this->usemd5 == '1') {
            $md5_signature_fields = 'amount:currency:cartId';
            $md5_signature = $this->md5key . ':' . $OrderAmt . ':' . $wpCurrency . ':' . $worldpay_url;
            $md5_signature_md5 = md5($md5_signature);

            $process_button_string .= tep_draw_hidden_field('signatureFields', $md5_signature_fields) .
                    tep_draw_hidden_field('signature', $md5_signature_md5);
        }

        if (tep_session_is_registered('credit_card_owner'))
            tep_session_unregister('credit_card_owner');
        return $process_button_string;
    }

    function before_process() {
        global $HTTP_POST_VARS, $order_logged;

        $proceed = true;

        // added by wei chen
        if (!tep_session_is_registered('order_logged'))
            return;
        // end added by wei chen

        if (!tep_session_is_registered('wp_order_logged'))
            $proceed = false;

        tep_session_unregister('wp_order_logged');

        if ($HTTP_POST_VARS['transStatus'] != 'Y')
            $proceed = false;

        if ($proceed) {
            $wp_payment_info_select_sql = "SELECT orders_id FROM " . TABLE_PAYMENT_EXTRA_INFO . " WHERE orders_id = '" . tep_db_input($order_logged) . "'";
            $wp_payment_info_result_sql = tep_db_query($wp_payment_info_select_sql);
            if (!tep_db_num_rows($wp_payment_info_result_sql))
                $proceed = false;
        }

        if (!$proceed) {
            tep_redirect(tep_href_link(FILENAME_CHECKOUT_PAYMENT, 'error_message=' . urlencode(MODULE_PAYMENT_WORLDPAY_TEXT_ERROR_MESSAGE), 'SSL', true, false));
        }
    }

    function after_process() {
        return false;
    }

    function link_to_payment($new_order_id) {
        global $order_logged;

        if (tep_session_is_registered('order_logged'))
            tep_session_unregister('order_logged');

        tep_session_register('order_logged');
        $order_logged = $new_order_id;
        require(DIR_WS_INCLUDES . 'modules/payment/worldpay/catalog/worldpay_splash.php');
        return;
    }

    function is_supported_currency($selected_currency) {
        return (in_array($selected_currency, $this->wpCurrencies) ? true : false);
    }

    function post_authorisation($transID) {
        $transition_date = '2007-12-10 15:00:00';
        $org_trans_id = $transID;

        $transID = (substr($transID, -1) == '*') ? substr($transID, 0, -1) : $transID;
        //if (tep_not_null($this->remote_admin_id) && tep_not_null($this->remote_admin_password)) {
        /*
          WP Account Transition period
         */
        $wp_account_transition_select_sql = "	SELECT orders_id
													FROM payment_extra_info 
													WHERE transaction_id = '" . tep_db_input($org_trans_id) . "'";
        $wp_account_transition_result_sql = tep_db_query($wp_account_transition_select_sql);
        $wp_account_transition_row = tep_db_fetch_array($wp_account_transition_result_sql);
        /*
          $order_date_status_select_sql = "	SELECT (date_purchased < '" . tep_db_input($transition_date) . "') AS old_order
          FROM " . TABLE_ORDERS . "
          WHERE orders_id = '" . tep_db_input($wp_account_transition_row['orders_id']) . "'";
          $order_date_status_result_sql = tep_db_query($order_date_status_select_sql);
          $order_date_status_row = tep_db_fetch_array($order_date_status_result_sql);
         */

        $order_info_select_sql = "	SELECT currency 
										FROM " . TABLE_ORDERS . " 
										WHERE orders_id = '" . tep_db_input($wp_account_transition_row['orders_id']) . "'";
        $order_info_result_sql = tep_db_query($order_info_select_sql);
        $order_info_row = tep_db_fetch_array($order_info_result_sql);

        $this->get_merchant_account($order_info_row['currency']);

        $wp_instId = $this->remote_admin_id;
        $wp_authPW = $this->remote_admin_password;
        /*
          if ($order_date_status_row['old_order'] == '1') {
          $wp_instId = '124610';
          $wp_authPW = 'E98hffs42';
          }
         */
        $form_data = array(instId => $wp_instId,
            authPW => $wp_authPW,
            op => 'postAuth-full',
            authMode => 'O',
            transId => $transID
        );

        // concatenate order information variables to $data
        while (list($key, $value) = each($form_data)) {
            $data .= $key . '=' . urlencode(ereg_replace_dep(',', '', $value)) . '&';
        }

        // take the last & out for the string
        $data = substr($data, 0, -1);
        unset($response);

        $url = "https://select.worldpay.com/wcc/itransaction";

        $agent = "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.0)";
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_VERBOSE, 1);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 2);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        curl_setopt($ch, CURLOPT_TIMEOUT, 120);
        curl_setopt($ch, CURLOPT_USERAGENT, $agent);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);  //Windows 2003 Compatibility
        $response = curl_exec($ch);
        curl_close($ch);

        return $response;
        //}
    }

    function output_error() {
        return false;
    }

    function check() {
        if (!isset($this->_check)) {
            $payment_methods_select_sql = "	SELECT payment_methods_id
											FROM " . TABLE_PAYMENT_METHODS . " as pm
											WHERE pm.payment_methods_id = '" . (int) $this->payment_methods_id . "'";
            $payment_methods_result_sql = tep_db_query($payment_methods_select_sql);
            $this->_check = tep_db_num_rows($payment_methods_result_sql);
        }
        return $this->_check;
    }

    function load_pm_setting($language_id = 1, $pm_id = '') {
        $pm_setting_array = array();
        //load value from DB

        if (tep_not_null($pm_id)) {
            $payment_method_id = $pm_id;

            $payment_methods_parent_id_select_sql = "	SELECT payment_methods_parent_id 
														FROM " . TABLE_PAYMENT_METHODS . " 
														WHERE payment_methods_id = '" . (int) $pm_id . "'";
            $payment_methods_parent_id_result_sql = tep_db_query($payment_methods_parent_id_select_sql);
            $payment_methods_parent_id_row = tep_db_fetch_array($payment_methods_parent_id_result_sql);

            $payment_gateway_id = $payment_methods_parent_id_row['payment_methods_parent_id'];
        } else {
            $payment_method_id = $this->payment_methods_id;
            $payment_gateway_id = $this->payment_methods_parent_id;
        }

        $payment_configuration_info_select_sql = "	SELECT pci.payment_configuration_info_key, pcid.payment_configuration_info_value
													FROM " . TABLE_PAYMENT_CONFIGURATION_INFO . " as pci
													LEFT JOIN " . TABLE_PAYMENT_CONFIGURATION_INFO_DESCRIPTION . " as pcid 
														ON pci.payment_configuration_info_id = pcid.payment_configuration_info_id
															AND pcid.languages_id = '" . (int) $language_id . "'
													WHERE pci.payment_methods_id = '" . (int) $payment_method_id . "'
													ORDER BY pci.payment_configuration_info_sort_order";
        $payment_configuration_info_result_sql = tep_db_query($payment_configuration_info_select_sql);
        if (tep_db_num_rows($payment_configuration_info_result_sql)) {
            while ($payment_configuration_info_row = tep_db_fetch_array($payment_configuration_info_result_sql)) {
                $pm_setting_array[$payment_configuration_info_row['payment_configuration_info_key']] = $payment_configuration_info_row['payment_configuration_info_value'];
            }
        } else if ((int) $payment_gateway_id > 0) { //load value from PG
            $payment_configuration_info_select_sql = "	SELECT pci.payment_configuration_info_key, pcid.payment_configuration_info_value
														FROM " . TABLE_PAYMENT_CONFIGURATION_INFO . " as pci
														LEFT JOIN " . TABLE_PAYMENT_CONFIGURATION_INFO_DESCRIPTION . " as pcid 
															ON pci.payment_configuration_info_id = pcid.payment_configuration_info_id
																AND pcid.languages_id = '" . (int) $language_id . "'
														WHERE pci.payment_methods_id = '" . (int) $payment_gateway_id . "'
														ORDER BY pci.payment_configuration_info_sort_order";
            $payment_configuration_info_result_sql = tep_db_query($payment_configuration_info_select_sql);
            while ($payment_configuration_info_row = tep_db_fetch_array($payment_configuration_info_result_sql)) {
                $pm_setting_array[$payment_configuration_info_row['payment_configuration_info_key']] = $payment_configuration_info_row['payment_configuration_info_value'];
            }
        }

        //static value
        $pm_setting_array['MODULE_PAYMENT_WORLDPAY_USEMD5'] = 1;

        return $pm_setting_array;
    }

    function get_merchant_account($selected_currency) {
        $payment_methods_instance_id = 0;
        $payment_gateway_instance_select_sql = "SELECT pmi.payment_methods_instance_id, pmi.payment_methods_instance_follow_default, pmi.currency_code 
				 								FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " as pmi
				 								WHERE pmi.payment_methods_id = '" . (int) $this->payment_methods_id . "'
				 								LIMIT 1";
        $payment_gateway_instance_result_sql = tep_db_query($payment_gateway_instance_select_sql);
        if (tep_db_num_rows($payment_gateway_instance_result_sql)) {
            $payment_gateway_instance_select_sql = "SELECT pmi.payment_methods_instance_id, pmi.payment_methods_instance_follow_default 
					 								FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " as pmi
					 								WHERE pmi.currency_code = '" . tep_db_input($selected_currency) . "' 
					 									AND pmi.payment_methods_id = '" . (int) $this->payment_methods_id . "'";
            $payment_gateway_instance_result_sql = tep_db_query($payment_gateway_instance_select_sql);
            if ($payment_gateway_instance_row = tep_db_fetch_array($payment_gateway_instance_result_sql)) {
                if ((int) $payment_gateway_instance_row['payment_methods_instance_follow_default'] > 0) { // Follow Default Currency Setting
                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_follow_default'];
                } else { // Has own setting
                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_id'];
                }
            }
        } else {
            $payment_gateway_instance_select_sql = "SELECT pmi.payment_methods_instance_id, pmi.payment_methods_instance_follow_default 
					 								FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " as pmi
					 								INNER JOIN " . TABLE_PAYMENT_METHODS . " as pm
					 									ON pm.payment_methods_parent_id = pmi.payment_methods_id
					 								WHERE pmi.currency_code = '" . tep_db_input($selected_currency) . "' 
					 									AND pm.payment_methods_id = '" . (int) $this->payment_methods_id . "'";
            $payment_gateway_instance_result_sql = tep_db_query($payment_gateway_instance_select_sql);
            if ($payment_gateway_instance_row = tep_db_fetch_array($payment_gateway_instance_result_sql)) {
                if ((int) $payment_gateway_instance_row['payment_methods_instance_follow_default'] > 0) {
                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_follow_default'];
                } else {
                    $payment_methods_instance_id = (int) $payment_gateway_instance_row['payment_methods_instance_id'];
                }
            }
        }
        $payment_gateway_instance_setting_select_sql = "SELECT pmis.payment_methods_instance_setting_key, pmis.payment_methods_instance_setting_value 
						 								FROM " . TABLE_PAYMENT_METHODS_INSTANCE_SETTING . " as pmis
						 								WHERE pmis.payment_methods_instance_id = '" . (int) $payment_methods_instance_id . "'";
        $payment_gateway_instance_setting_result_sql = tep_db_query($payment_gateway_instance_setting_select_sql);
        while ($payment_gateway_instance_setting_row = tep_db_fetch_array($payment_gateway_instance_setting_result_sql)) {
            switch ($payment_gateway_instance_setting_row['payment_methods_instance_setting_key']) {
                case 'MODULE_PAYMENT_WORLDPAY_ID':
                    $this->worldpay_id = $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];
                    break;
                case 'MODULE_PAYMENT_WORLDPAY_MD5KEY':
                    $this->md5key = $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];
                    break;
                case 'MODULE_PAYMENT_WORLDPAY_REMOTE_ADMIN_ID':
                    $this->remote_admin_id = $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];
                    break;
                case 'MODULE_PAYMENT_WORLDPAY_REMOTE_ADMIN_PASSWORD':
                    $this->remote_admin_password = $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];
                    break;
                case 'MODULE_PAYMENT_WORLDPAY_CALLBACK_PASSWORD':
                    $this->callback_password = $payment_gateway_instance_setting_row['payment_methods_instance_setting_value'];
                    break;
            }
        }
    }

    function install() {
        $install_configuration_flag = true;

        if ($this->payment_methods_id > 0) {
            if ($this->receive_status != 1) {
                $update_receive_status_sql_data = array("payment_methods_receive_status" => 1);
                tep_db_perform(TABLE_PAYMENT_METHODS, $update_receive_status_sql_data, 'update', "payment_methods_id = '" . (int) $this->payment_methods_id . "'");
            } else {
                $install_configuration_flag = false;
            }
        } else {
            $install_payment_gateway_data_sql = array('payment_methods_code' => $this->code,
                'payment_methods_send_status' => 0,
                'payment_methods_title' => $this->code,
                'payment_methods_types_id' => 0,
                'payment_methods_parent_id' => 0,
                'payment_methods_legend_color' => '#FFFF00',
                'payment_methods_sort_order' => 4,
                'payment_methods_filename' => $this->filename,
                'payment_methods_logo' => '',
                'date_added' => 'now()',
                'last_modified' => 'now()',
                'payment_methods_receive_status' => 1,
                'payment_methods_receive_status_mode' => 0
            );
            tep_db_perform(TABLE_PAYMENT_METHODS, $install_payment_gateway_data_sql);
            $this->payment_methods_id = tep_db_insert_id();
        }

        if ($install_configuration_flag) {
            $install_key_array = array();
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Manual Post Auth Notification',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_WORLDPAY_MANUAL_AUTH_NOTIFICATION_EMAIL',
                    'payment_configuration_info_description' => 'Email address to which the notification email will be send to when someone manually authorise WorldPay payment.<br>(In "Name &lt;Email&gt;" format. Use \',\' as delimiter for multiple recipient)',
                    'payment_configuration_info_sort_order' => '220',
                    'set_function' => '',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => '',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Credit Card Types',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_WORLDPAY_CARD_TYPES',
                    'payment_configuration_info_description' => 'Select which credit card types are accepted in this payment method. Those checked types will be available for select in checkout page.',
                    'payment_configuration_info_sort_order' => '16',
                    'set_function' => 'tep_cfg_key_select_multioption(array(\'Amex\'=>\'Amex\',\'VISA\'=>\'Visa\',\'VISP\'=>\'Visa Purchasing\',\'MSCD\'=>\'Master Card\',\'SWIT\'=>\'Switch\',\'SOLO\'=>\'Solo\',\'VISD\'=>\'Visa Delta\',\'VIED\'=>\'Visa Electron\',\'JCB\'=>\'JCB\'),',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => '',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Confirm Complete (in days)',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_WORLDPAY_CONFIRM_COMPLETE',
                    'payment_configuration_info_description' => 'The number of days in which an order made by this payment method is confirm completed',
                    'payment_configuration_info_sort_order' => '210',
                    'set_function' => '',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => '0',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Show credit card types selection',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_WORLDPAY_SHOW_CARD_TYPES',
                    'payment_configuration_info_description' => 'Do you want to let customers select their credit card type at payment method selection page?',
                    'payment_configuration_info_sort_order' => '15',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'),',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Payment Email Message',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_WORLDPAY_EMAIL_MESSAGE',
                    'payment_configuration_info_description' => 'This message will show up in order confirmation e-mail.',
                    'payment_configuration_info_sort_order' => '200',
                    'set_function' => 'tep_cfg_textarea(',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'Thank you for shopping at OffGamers.com.',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Post Authorisation Time Out',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_WORLDPAY_POST_AUTH_TIME_OUT',
                    'payment_configuration_info_description' => 'Set the time out period(days) for WorldPay\'s Pre-Authorisation transaction.',
                    'payment_configuration_info_sort_order' => '6',
                    'set_function' => '',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => '5',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Payment Message',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_WORLDPAY_MESSAGE',
                    'payment_configuration_info_description' => 'Payment message will show up during checkout process (WorldPay)',
                    'payment_configuration_info_sort_order' => '16',
                    'set_function' => 'tep_cfg_textarea(',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'Thank you for shopping at SKC Store.',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Credit card billing address',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_WORLDPAY_BILLING_ADDRESS',
                    'payment_configuration_info_description' => 'Should the credit card billing address to be changed at WorldPay payment?',
                    'payment_configuration_info_sort_order' => '14',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'True',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Credit card owner',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_WORLDPAY_CARD_OWNER',
                    'payment_configuration_info_description' => 'Should the credit card owner name to be changed at WorldPay payment?',
                    'payment_configuration_info_sort_order' => '12',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'True',
                    'languages_id' => 1
                )
            );

            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Mode',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_WORLDPAY_MODE',
                    'payment_configuration_info_description' => 'The mode you are working in (100 = Test Mode Accept, 101 = Test Mode Decline, 0 = Live.',
                    'payment_configuration_info_sort_order' => '3',
                    'set_function' => '',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => '100',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Use Pre-Authorisation?',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_WORLDPAY_USEPREAUTH',
                    'payment_configuration_info_description' => 'Do you want to pre-authorise payments? Default=False. You need to request this from WorldPay before using it.',
                    'payment_configuration_info_sort_order' => '4',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Pre-Auth',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_WORLDPAY_PREAUTH',
                    'payment_configuration_info_description' => 'The mode you are working in (A = Pay Now, E = Pre Auth). Ignored if Use PreAuth is False.',
                    'payment_configuration_info_sort_order' => '5',
                    'set_function' => '',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'A',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Set Order Status',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_WORLDPAY_ORDER_STATUS_ID',
                    'payment_configuration_info_description' => 'Set the status of orders made with this payment module to this value.',
                    'payment_configuration_info_sort_order' => '7',
                    'set_function' => 'tep_cfg_pull_down_order_statuses(',
                    'use_function' => 'tep_get_order_status_name',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => '0',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Require Address Information',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_WORLDPAY_MANDATORY_ADDRESS_FIELD',
                    'payment_configuration_info_description' => 'Set address field as required info during checkout.',
                    'payment_configuration_info_sort_order' => '8',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Customer Payment Info',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_WORLDPAY_CUSTOMER_PAYMENT_INFO',
                    'payment_configuration_info_description' => 'Set to true if this Payment Method required customer input for Payment Information required customer payment info.',
                    'payment_configuration_info_sort_order' => '9',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Require IC Information',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_WORLDPAY_MANDATORY_IC_FIELD',
                    'payment_configuration_info_description' => 'Set IC field as required info during checkout.',
                    'payment_configuration_info_sort_order' => '10',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );
            $install_key_array[] = array('info' => array('payment_methods_id' => (int) $this->payment_methods_id,
                    'payment_configuration_info_title' => 'Require Contact Information',
                    'payment_configuration_info_key' => 'MODULE_PAYMENT_WORLDPAY_MANDATORY_CONTACT_FIELD',
                    'payment_configuration_info_description' => 'Set Contact field as required info during checkout.',
                    'payment_configuration_info_sort_order' => '11',
                    'set_function' => 'tep_cfg_select_option(array(\'True\', \'False\'), ',
                    'use_function' => '',
                    'date_added' => 'now()'
                ),
                'desc' => array('payment_configuration_info_value' => 'False',
                    'languages_id' => 1
                )
            );
            foreach ($install_key_array as $data) {
                tep_db_perform(TABLE_PAYMENT_CONFIGURATION_INFO, $data['info']);
                $payment_conf_id = tep_db_insert_id();

                $data['desc']['payment_configuration_info_id'] = (int) $payment_conf_id;
                tep_db_perform(TABLE_PAYMENT_CONFIGURATION_INFO_DESCRIPTION, $data['desc']);
            }
        }
        return 1;
    }

    function configuration_information_keys() {
        return array('MODULE_PAYMENT_WORLDPAY_MANUAL_AUTH_NOTIFICATION_EMAIL',
            'MODULE_PAYMENT_WORLDPAY_CARD_TYPES',
            'MODULE_PAYMENT_WORLDPAY_CONFIRM_COMPLETE',
            'MODULE_PAYMENT_WORLDPAY_SHOW_CARD_TYPES',
            'MODULE_PAYMENT_WORLDPAY_EMAIL_MESSAGE',
            'MODULE_PAYMENT_WORLDPAY_POST_AUTH_TIME_OUT',
            'MODULE_PAYMENT_WORLDPAY_MESSAGE',
            'MODULE_PAYMENT_WORLDPAY_BILLING_ADDRESS',
            'MODULE_PAYMENT_WORLDPAY_CARD_OWNER',
            'MODULE_PAYMENT_WORLDPAY_MODE',
            'MODULE_PAYMENT_WORLDPAY_USEPREAUTH',
            'MODULE_PAYMENT_WORLDPAY_PREAUTH',
            'MODULE_PAYMENT_WORLDPAY_ORDER_STATUS_ID',
            'MODULE_PAYMENT_WORLDPAY_MANDATORY_ADDRESS_FIELD',
            'MODULE_PAYMENT_WORLDPAY_CUSTOMER_PAYMENT_INFO',
            'MODULE_PAYMENT_WORLDPAY_MANDATORY_IC_FIELD',
            'MODULE_PAYMENT_WORLDPAY_MANDATORY_CONTACT_FIELD'
                //'MODULE_PAYMENT_WORLDPAY_ZONE'
        );
    }

    function multi_lang_configuration_info_keys() {
        return array('MODULE_PAYMENT_WORLDPAY_MANUAL_AUTH_NOTIFICATION_EMAIL',
            'MODULE_PAYMENT_WORLDPAY_EMAIL_MESSAGE',
        );
    }

    function merchant_information_keys() {
        include_once(DIR_WS_CLASSES . "payment_methods.php");

        return array(
            'MODULE_PAYMENT_WORLDPAY_ID' => array("field" => 'text', 'mapping' => 'MODULE_PAYMENT_WORLDPAY_LNG_ID'),
            'MODULE_PAYMENT_WORLDPAY_MD5KEY' => array("field" => 'text', 'mapping' => 'MODULE_PAYMENT_WORLDPAY_LNG_MD5KEY'),
            'MODULE_PAYMENT_WORLDPAY_REMOTE_ADMIN_ID' => array("field" => 'text', 'mapping' => 'MODULE_PAYMENT_WORLDPAY_LNG_REMOTE_ADMIN_ID'),
            'MODULE_PAYMENT_WORLDPAY_REMOTE_ADMIN_PASSWORD' => array("field" => 'text', 'mapping' => 'MODULE_PAYMENT_WORLDPAY_LNG_REMOTE_ADMIN_PASSWORD'),
            'MODULE_PAYMENT_WORLDPAY_CALLBACK_PASSWORD' => array("field" => 'text', 'mapping' => 'MODULE_PAYMENT_WORLDPAY_LNG_CALLBACK_PASSWORD'),
            'MODULE_PAYMENT_TAX' => array("field" => 'select', 'mapping' => "MODULE_PAYMENT_TAX", "select" => payment_methods::get_tax_option())
        );
    }

    function get_pg_id() {
        return $this->payment_methods_id;
    }

    function remove() {
        tep_db_query("delete from " . TABLE_CONFIGURATION . " where configuration_key in ('" . implode("', '", $this->keys()) . "')");
    }

    function keys() {
        return array('MODULE_PAYMENT_WORLDPAY_STATUS', 'MODULE_PAYMENT_WORLDPAY_ID', 'MODULE_PAYMENT_WORLDPAY_MODE', 'MODULE_PAYMENT_WORLDPAY_USEPREAUTH', 'MODULE_PAYMENT_WORLDPAY_PREAUTH', 'MODULE_PAYMENT_WORLDPAY_REMOTE_ADMIN_ID', 'MODULE_PAYMENT_WORLDPAY_REMOTE_ADMIN_PASSWORD',
            'MODULE_PAYMENT_WORLDPAY_POST_AUTH_TIME_OUT', 'MODULE_PAYMENT_WORLDPAY_MANUAL_AUTH_NOTIFICATION_EMAIL', 'MODULE_PAYMENT_WORLDPAY_ZONE', 'MODULE_PAYMENT_WORLDPAY_SORT_ORDER', 'MODULE_PAYMENT_WORLDPAY_ORDER_STATUS_ID', 'MODULE_PAYMENT_WORLDPAY_USEMD5', 'MODULE_PAYMENT_WORLDPAY_MD5KEY', 'MODULE_PAYMENT_WORLDPAY_CALLBACK_PASSWORD',
            'MODULE_PAYMENT_WORLDPAY_SHOW_CARD_TYPES', 'MODULE_PAYMENT_WORLDPAY_CARD_TYPES', 'MODULE_PAYMENT_WORLDPAY_CARD_OWNER', 'MODULE_PAYMENT_WORLDPAY_BILLING_ADDRESS', 'MODULE_PAYMENT_WORLDPAY_CONFIRM_COMPLETE',
            'MODULE_PAYMENT_WORLDPAY_MESSAGE', 'MODULE_PAYMENT_WORLDPAY_EMAIL_MESSAGE', 'MODULE_PAYMENT_WORLDPAY_LEGEND_COLOUR');
    }

    function get_orders_payment_info_file() {
        return DIR_FS_CATALOG_MODULES . 'payment/worldpay/admin/orders.inc.php';
    }

    function get_orders_list_payment_info_file() {
        return DIR_FS_CATALOG_MODULES . 'payment/worldpay/admin/orders_list.inc.php';
    }

    function get_ipn_class_file() {
        return '';
    }

}

?>