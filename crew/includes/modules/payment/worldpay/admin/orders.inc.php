<?
/*
  	$Id: orders.inc.php,v 1.22 2009/02/03 03:25:21 boonhock Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2005 SKC Venture
  	
  	Released under the GNU General Public License
*/

$authorisation_array = array('A' => 'Fully Authorised', 'E' => 'Pre-Authorisation', 'M' => 'Manual Authorisation');
$current_date_time = date('Y-m-d H:i:s');

$wp_trans_info_select_sql = "SELECT * FROM " . TABLE_PAYMENT_EXTRA_INFO . " WHERE orders_id='" . (int)$oID . "'";
$wp_trans_info_result_sql= tep_db_query($wp_trans_info_select_sql);
?>
<table border="0" width="100%" cellspacing="0" cellpadding="2">
<? ob_start(); ?>
	<tr>
		<td class="main">
      		<table border="0" width="100%" cellspacing="0" cellpadding="2">
      			<tr>
      				<td class="main" width="12%" nowrap><b><?=ENTRY_PAYMENT_METHOD?></b></td>
      				<td class="main">
      					<div>
      						<? if((int)$order->info['payment_methods_id']>0) { ?><div class="<?=((int)$payment_info_array[$order->info['payment_methods_id']]->confirm_complete_days>0?'rp_icon':'nrp_icon')?>"><b><?=((int)$payment_info_array[$order->info['payment_methods_id']]->confirm_complete_days>0?'RP':'NRP')?></b></div> <? } ?>
      						<div style="width:30px; height:15px; background-color:<?=$payment_module_info['display_colour']?>; float:left; vertical-align:top; text-align:center;">##DAYS_LAPSE##</div>
      						<div style="vertical-align: top">
      							&nbsp;&nbsp;
<?
	if ($view_payment_details_permission) {
		if (tep_not_null($last_payment_method) && $last_payment_method['payment_methods_id'] != $order->info['payment_methods_id']) {
			echo '<span class="redIndicator">'.(tep_not_null($payment_module_info['payment_methods_parent_title'])?$payment_module_info['payment_methods_parent_title'] . ' - ' :'') . $payment_module_info['payment_methods_title'].'</span>';
		} else {
			echo (tep_not_null($payment_module_info['payment_methods_parent_title'])?$payment_module_info['payment_methods_parent_title'] . ' - ' :'') . $payment_module_info['payment_methods_title'];
		}
		
		if (tep_not_null($last_payment_method) && $last_payment_method['payment_methods_id'] != $order->info['payment_methods_id']) {
			echo '&nbsp;&nbsp;<span class="redIndicator">(Last Payment Method: ' . (tep_not_null($last_payment_method['parent_payment_methods_title'])? $last_payment_method['parent_payment_methods_title'] . ' - ' : '' ) . $last_payment_method['payment_methods_title'] . ')</span>';
		}
	} else {
		echo (tep_not_null($payment_module_info['payment_methods_parent_title'])?$payment_module_info['payment_methods_parent_title'] . ' - ' :'') . $payment_module_info['payment_methods_title'];
	}
?>
							</div>
      					</div>
      				</td>
				</tr>
			</table>
		</td>
	</tr>
<?
$payment_method_title_info = ob_get_contents();
ob_end_clean();

if ($wp_trans_info_row = tep_db_fetch_array($wp_trans_info_result_sql)) {
	if (!$view_payment_details_permission) {
		echo str_replace('##DAYS_LAPSE##', '&nbsp;', $payment_method_title_info);
	} else {
		if ($order->info['orders_status'] == '7' && $wp_trans_info_row["authorisation_mode"] == 'E') {
			if ( ($days_lapse = tep_day_diff($order->info['date_purchased'], $current_date_time)) != FALSE) {
				$days_count = (int)$days_lapse;
				$days_count = $days_count > $payment_module_info['post_auth_time_out'] ? '<span class="redIndicator"><b>E</b></span>' : $days_count;
				echo str_replace('##DAYS_LAPSE##', $days_count, $payment_method_title_info);
			} else {
				echo str_replace('##DAYS_LAPSE##', '&nbsp;', $payment_method_title_info);
			}
		} else {
			echo str_replace('##DAYS_LAPSE##', '&nbsp;', $payment_method_title_info);
		}
		
		$cardholder_auth_desc_array = array (	0 => "Cardholder authenticated", 
												1 => "Cardholder/Issuing Bank not enrolled for authentication", 
												7 => "Cardholder did not complete authentication", 
												6 => "Cardholder authentication not available");
		
		$AVS_desc_array = array (0 => "Not supported", 1 => "Not checked", 2 => "Matched", 4 => "Not match", 8 => "Partially match");
		
		if (tep_not_null($wp_trans_info_row["check_result"])) {
			$AVS_array = preg_split('//', $wp_trans_info_row["check_result"], -1, PREG_SPLIT_NO_EMPTY);
		} else {
			$AVS_array = array();
		}
?>
  	<tr>
    	<td class="main">
      		<table border="0" width="100%" cellspacing="0" cellpadding="2">
      			<tr valign="top">
        			<td>
              			<table border="0" cellspacing="0" cellpadding="2">
              				<tr>
                				<td class="main" valign="top" nowrap><b><?="Card Owner"?></b>&nbsp;</td>
                				<td class="main" nowrap>
                				<?
                				$wp_payer_name = $wp_trans_info_row["credit_card_owner"];
                				if (tep_not_null($wp_payer_name)) {
                					$name_not_match_profile = (strtolower($wp_payer_name) != strtolower($order->customer["name"])) ? 1 : 0 ;
                					
                					$customer_with_this_name_array = tep_check_duplicate_name($wp_payer_name, 'worldpay');	// including himself
                					$name_used_by_other = (count($customer_with_this_name_array) > 1) ? 1 : 0 ;
                					
                					$total_name_used_in_wp = tep_get_distinct_name_used ($cid, $wp_payer_name, 'worldpay');
                					$total_name_used = (count($total_name_used_in_wp) > 1) ? 1 : 0 ;
                					
                					echo '<span class="'.(($name_not_match_profile+$name_used_by_other+$total_name_used > 0) ? 'redIndicator' : 'blackIndicator').'">' . $wp_payer_name . '</span>';
                					
                					$name_alert_reason = '';
                					$name_alert_reason .= ($name_not_match_profile == 1) ? '<span class="redIndicator">Name does not match profile.</span><br>' : '';
                					
                					if ($name_used_by_other == 1) {
	                					$name_alert_reason .=	tep_draw_form('cust_lists_share_name', FILENAME_CUSTOMERS, 'action=show_report&customer_lists_subaction=do_search', 'post', 'target="_blank"') . "\n" . 
																tep_draw_hidden_field('customer_share_id', tep_array_serialize($customer_with_this_name_array)) . "\n" . 
																'<span class="redIndicator">Name exists in <a href="javascript: document.cust_lists_share_name.submit();"><u>' . count($customer_with_this_name_array) . '</u></a> profiles.</span><br>' . 
																'</form>' . "\n";
									}
                					$name_alert_reason .= ($total_name_used == 1) ? '<span class="redIndicator"><a href="'.tep_href_link(FILENAME_ORDERS, 'cID='.$cid).'" target="_blank"><u>' . count($total_name_used_in_wp) . '</u></a> names used in WorldPay orders.</span><br>' : '';
                					
                					if (tep_not_null($name_alert_reason)) {
                						echo '<br><span class="smallText">' . $name_alert_reason . '</span>';
                					}
                				}
                				?>
                				</td>
              				</tr>
              				<tr>
                				<td class="main" valign="top" nowrap><b><?="E-Mail Address"?></b>&nbsp;</td>
                				<td class="main" nowrap>
                				<?
                				if (tep_not_null($wp_trans_info_row["email_address"])) {
	                				if (strtolower($wp_trans_info_row["email_address"]) != strtolower($order->customer['email_address'])) {
	                					echo '<span class="redIndicator">'.$wp_trans_info_row["email_address"].'</span>';
	                					echo '<br><span class="smallText"><span class="redIndicator">Email does not match profile.</span></span>';
	                				} else {
	                					echo $wp_trans_info_row["email_address"];
	                				}
	                			}
                				?>
                				</td>
              				</tr>
              				<tr>
                				<td class="main" valign="top" nowrap><b><?="Telephone Number"?></b>&nbsp;</td>
                				<td class="main">
                				<?
                				if (tep_not_null($wp_trans_info_row["tel"])) {
	                				if (preg_replace('/[^\d]/', '', $wp_trans_info_row["tel"]) != preg_replace('/[^\d]/', '', $order->customer['order_country_code'] . $order->customer['telephone'])) {
	                					echo '<span class="redIndicator">'.$wp_trans_info_row["tel"].'</span>';
	                					echo '<br><span class="smallText"><span class="redIndicator">Telephone number does not match profile.</span></span>';
	                				} else {
	                					echo $wp_trans_info_row["tel"];
	                				}
	                			}
                				?>
                				</td>
              				</tr>
              				<tr>
                				<td class="main" valign="top" nowrap><b><?="Fax Number"?></b>&nbsp;</td>
                				<td class="main" nowrap>
                				<?
                				if (tep_not_null($wp_trans_info_row["fax"])) {
	                				if ($wp_trans_info_row["fax"] != $the_customers_fax) {
	                					echo '<span class="redIndicator">'.$wp_trans_info_row["fax"].'</span>';
	                					echo '<br><span class="smallText"><span class="redIndicator">Fax number does not match profile.</span></span>';
	                				} else {
	                					echo $wp_trans_info_row["fax"];
	                				}
	                			}
                				?>
                				</td>
              				</tr>
              			</table>
        			</td>
        			<td>
              			<table border="0 cellspacing="0" cellpadding="2">
              				<tr valign="top">
                				<td class="main" nowrap valign="top"><b><?="Billing Address"?></b>&nbsp;</td>
                				<td class="main"><?=$wp_trans_info_row["billing_address"]?></td>
              				</tr>
              				<tr valign="top">
                				<td class="main" nowrap valign="top"><b><?="Billing Country"?></b>&nbsp;</td>
                				<td class="main"><?=$wp_trans_info_row["country"]?></td>
              				</tr>
              			</table>
        			</td>
        			<td>
              			<table border="0" cellspacing="0" cellpadding="2">
              				<tr>
                				<td class="main" valign="top" nowrap><b><?="Transaction ID"?></b>&nbsp;</td>
                				<td class="main" nowrap>
                					<span id="transIDValue"><?=$wp_trans_info_row["transaction_id"]?></span>
                				<?	if ($order->info['orders_status'] == '7') {
                						$edit_transaction_id_permission = tep_admin_files_actions(FILENAME_ORDERS, 'ADM_EDIT_WORLDPAY_TRANSACTION_ID');
                						if ( $edit_transaction_id_permission && (substr($wp_trans_info_row["transaction_id"], -1) == '*' || !tep_not_null($wp_trans_info_row["transaction_id"])) ) {	// manually entered ?>
                							<a href="javascript:;" onClick="editTransId('<?=(int)$oID?>', 'transIDValue', '<?=$login_email_address?>');"><u>Edit</u></a>
                				<?		}
                					}
                					
                					if (substr($wp_trans_info_row["transaction_id"], -1) == '*') {
                						echo '<br><span class="redIndicator"><small>* denotes manually entered transaction id</small></span>';
                					}
                				?>
                				</td>
              				</tr>
              				<tr>
                				<td class="main" nowrap><b><?="Transaction Status"?></b>&nbsp;</td><td class="main" nowrap><?=($wp_trans_info_row["transaction_status"] =="1" ? "Success" : "Failed")?></td>
              				</tr>
              				<tr>
                				<td class="main" nowrap><b><?="Transaction Amount"?></b>&nbsp;</td><td class="main" nowrap><?=$wp_trans_info_row["transaction_text_amount"]?></td>
              				</tr>
              				
              				<tr>
                				<td class="main" nowrap><b><?="Card/Wallet Type:"?></b>&nbsp;</td><td class="main" nowrap><?=$wp_trans_info_row["credit_card_type"]?></td>
              				</tr>
              			</table>
              		</td>
      			</tr>
      			<tr valign="top">
      				<td>
        				<table border="0" cellspacing="0" cellpadding="2">
        					<tr>
                				<td class="main" nowrap><b><?="Cardholder Authentication Result"?></b>&nbsp;</td>
                				<td class="main" nowrap>
                				<?
                					if (tep_not_null($wp_trans_info_row["cardholder_aut_result"])) {
                						$cardholder_auth_result_no = substr(trim($wp_trans_info_row["cardholder_aut_result"]), -1);
                						if ($cardholder_auth_result_no == '6' ||
                							$cardholder_auth_result_no == '7'
                							) {
                							echo '<span class="redIndicator">'.$cardholder_auth_result_no.' - '.$cardholder_auth_desc_array[$cardholder_auth_result_no].'</span>';
                						} else if ($cardholder_auth_result_no == '0') {
                							echo '<b>'.$cardholder_auth_result_no.' - '.$cardholder_auth_desc_array[$cardholder_auth_result_no].'</b>';
                						} else {
                							echo $cardholder_auth_result_no.' - '.$cardholder_auth_desc_array[$cardholder_auth_result_no];
                						}
                					} else {
                						echo TEXT_OPTION_NOT_APPLICABLE;
                					}
                				?>
                				</td>
                			</tr>
        					<tr>
                				<td class="main" nowrap><b><?="WorldAlert Results"?></b>&nbsp;</td><td class="main" nowrap><?=tep_not_null($wp_trans_info_row["alert_message"]) ? '<span class="redIndicator">'.$wp_trans_info_row["alert_message"].'</span>' : 'No Alert Received'?></td>
                			</tr>
                			<tr>
                				<td class="main" nowrap><b><?="Authorisation Mode"?></b>&nbsp;</td>
                				<td class="main" nowrap><?=$authorisation_array[$wp_trans_info_row["authorisation_mode"]]?></td>
              				</tr>
              				<tr>
                				<td class="main" nowrap><b><?="Authorisation Result"?></b>&nbsp;</td><td class="main" nowrap><?=$wp_trans_info_row["authorisation_result"]?></td>
              				</tr>
              				<tr>
                				<td class="main" nowrap colspan="2">
                					<table border="0" width="100%" cellspacing="0" cellpadding="2">
                						<tr>
                				<?	//only take care of showing Post-Authorisation button if it is in Verifying and was Pre-Authorised
                					if ($order->info['orders_status'] == '7' && $wp_trans_info_row["authorisation_mode"] == 'E') {
                						$button_state = ($AVS_array[0] == "4" ? 'disabled' : '');	// Disabled button when CVV = 4 (Not match)
                						$button_displayed = false;
                						
                						$manual_authorise_permission = tep_admin_files_actions(FILENAME_ORDERS, 'ADM_WORLDPAY_MANUAL_AUTHORISATION');
                						echo '	<td width="140px" class="main">';
                						if ($manual_authorise_permission) {
	                						echo tep_draw_form('manual_post_auth_order', FILENAME_ORDERS, tep_get_all_get_params(), 'post');
	                						echo tep_draw_hidden_field('subaction', 'manual_post_authorisation');
	                						echo tep_draw_hidden_field('transId', $wp_trans_info_row["transaction_id"]);
	                						echo '<input type="submit" name="ManualPostAuthBtn" value="Manual Authorisation" '.$button_state.' title="Post Authorise Manually" class="inputButton" onClick="if (confirm(\'Are you sure that you had post authorised this order via the WorldPay Customer Management System?\')) { openNewWin(\'https://secure.ims.worldpay.com/sso/public/auth/login.html?serviceIdentifier=merchantadmin\', \'WP Admin\', \'location=1,status=1,menubar=1,resizable=1,directories=1,width=750,height=400\'); this.disabled=true;this.value=\'Please wait...\';this.form.submit(); } else { return false; }">';
	                						echo '</form>';
	                						$button_displayed = true;
	                					}
                						echo '	</td>
												<td class="main">';
                						
                						if ( ($days_lapse = tep_day_diff($order->info['date_purchased'], $current_date_time)) != FALSE) {
                							if (isset($payment_module_info['post_auth_time_out']) && $payment_module_info['post_auth_time_out'] !== '') {
	                							$post_auth_btn_text = "Post-Authorise" . ($days_lapse <= $payment_module_info['post_auth_time_out'] ? '' : " (".(int)$days_lapse." days lapse)");
		                						echo tep_draw_form('post_auth_order', FILENAME_ORDERS, tep_get_all_get_params(), 'post');
												echo tep_draw_hidden_field('subaction', 'post_authorisation');
												echo '<input type="submit" name="PostAuthBtn" value="'.$post_auth_btn_text.'" '.$button_state.' title="Post Authorise" class="inputButton" onClick="if (confirm(\'Are you sure to post authorise this order remotely?\')) { this.disabled=true;this.value=\'Please wait...\';this.form.submit(); } else { return false; }">';
												echo "</form>";
												$button_displayed = true;
											} else { echo '<span class="redIndicator">Post-Authorisation time out period is not set!</span>'; }
                						} else {
                							echo '<span class="redIndicator">Dates comparison failed!</span>';
                						}
                						
                						echo '</td>';
                						if ($AVS_array[0] == "4" && $button_displayed == true) {
                							echo '</tr><tr><td class="main" colspan="2"><span class="redIndicator">You cannot perform Post-Authorisation due to CVV not matching!</span></td>';
                						}
                						/*
                						echo '<br><br>'.tep_draw_form('manual_post_auth_order', FILENAME_ORDERS, tep_get_all_get_params(), 'post');
										echo tep_draw_hidden_field('subaction', 'manual_post_authorisation');
										echo tep_draw_hidden_field('transId', $wp_trans_info_row["transaction_id"]);
										echo '<input type="submit" name="ManualPostAuthBtn" value="Manual Post Authorisation" title="Post Authorise Manually" class="inputButton" onClick="if (confirm(\'Are you sure that you had post authorised this order via the WorldPay Customer Management System?\')) { this.disabled=true;this.value=\'Please wait...\';this.form.submit(); } else { return false; }">';
										echo "</form>";*/
									}
                				?>
                						</tr>
                					</table>
                				</td>
              				</tr>
        				</table>
        			</td>
        			<td colspan="2">
        				<table border="0" cellspacing="0" cellpadding="2">
<?		if (count($AVS_array)) { ?>
        					<tr>
                				<td class="main" colspan="2" nowrap><b>AVS (internal fraud-related checks)</b></td>
                			</tr>
                			<tr>
                				<td class="main" colspan="2" nowrap>
                					<table border="1" cellspacing="0" cellpadding="5">
        								<tr>
                							<td class="main" nowrap>Card Verification Value check (CVV)</td>
                							<td class="main" nowrap><?=($AVS_array[0] == "2" ? $AVS_array[0] . ' - ' . $AVS_desc_array[$AVS_array[0]] : '<span class="redIndicator">' . $AVS_array[0] . ' - ' . $AVS_desc_array[$AVS_array[0]] . '</span>')?></td>
                						</tr>
                						<tr>
                							<td class="main" nowrap>Postcode AVS check</td>
                							<td class="main" nowrap><?=($AVS_array[1] == 2 ? $AVS_array[1] . ' - ' . $AVS_desc_array[$AVS_array[1]] : '<span class="redIndicator">' . $AVS_array[1] . ' - ' . $AVS_desc_array[$AVS_array[1]] . '</span>')?></td>
                						</tr>
                						<tr>
                							<td class="main" nowrap>Address AVS check</td>
                							<td class="main" nowrap><?=($AVS_array[2] == 2 ? $AVS_array[2] . ' - ' . $AVS_desc_array[$AVS_array[2]] : '<span class="redIndicator">' . $AVS_array[2] . ' - ' . $AVS_desc_array[$AVS_array[2]] . '</span>')?></td>
                						</tr>
                						<tr>
                							<td class="main" nowrap>Country comparison check</td>
                							<td class="main" nowrap><?=($AVS_array[3] == 2 ? $AVS_array[3] . ' - ' . $AVS_desc_array[$AVS_array[3]] : '<span class="redIndicator">' . $AVS_array[3] . ' - ' . $AVS_desc_array[$AVS_array[3]] . '</span>')?></td>
                						</tr>
                					</table>
                				</td>
                			</tr>
<?		} ?>
        				</table>
        			</td>
        		</tr>
      		</table>
   		</td>
  	</tr>
<?
	}
} else if ($order->info['orders_status'] == '7') {
	echo str_replace('##DAYS_LAPSE##', '&nbsp;', $payment_method_title_info);
	
	if ($view_payment_details_permission) {
		echo '<tr>
	    		<td class="main">';
		echo tep_draw_form('manual_insert_id_form', FILENAME_ORDERS, tep_get_all_get_params(), 'post');
		echo tep_draw_hidden_field('subaction', 'manual_insert_trans_id');
		echo tep_draw_hidden_field('transId', '');
		echo '<input type="submit" name="ManualInsertTransIDBtn" value="Insert Transaction ID" title="Manually Insert Transaction ID" class="inputButton" onClick="return confirmManualInsert(this);">';
		echo "</form>
				</td>
			</tr>";
?>
	<script>
	<!--
		function confirmManualInsert(btnObj) {
			var transID = prompt("Please enter WorldPay transaction ID that correspond to this order:","");
			// check to see if anything was entered
	        // null == pressing cancel
	        // ""   == entered nothing and pressing ok
			while (transID != null && (trim_str(transID) == '' || !validateInteger(trim_str(transID)) || transID.length > 12) ) {
				transID = prompt("Invalid Transaction ID!\nPlease enter the \"Transaction ID\" provided by WorldPay for this order:", "");
			}
			if (transID == null) {
				return false;
			} else {
				document.manual_insert_id_form.transId.value = transID;
				btnObj.disabled=true;
				btnObj.value='Please wait...';
				btnObj.form.submit();
			}
		}
	//-->
	</script>
<?
	}
} else {
	echo str_replace('##DAYS_LAPSE##', '&nbsp;', $payment_method_title_info);
}
?>
</table>