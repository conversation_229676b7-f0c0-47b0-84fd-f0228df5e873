<?
/*
  	$Id: orders_list.inc.php,v 1.3 2006/08/24 07:35:03 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2005 SKC Venture
  	
  	Released under the GNU General Public License
*/

$wp_trans_info_select_sql = "SELECT * FROM " . TABLE_PAYMENT_EXTRA_INFO . " WHERE orders_id='" . $order_obj->orders_id . "'";
$wp_trans_info_result_sql= tep_db_query($wp_trans_info_select_sql);
?>
<table border="0" width="100%" cellspacing="0" cellpadding="2">
	<tr>
		<td class="subInvoiceBoxHeading">
			<div style="border-bottom: 1px solid #996600; border-top: 1px solid #996600; padding: 2px;">Payment Details</div>
		</td>
	</tr>
<?
if ($wp_trans_info_row = tep_db_fetch_array($wp_trans_info_result_sql)) {
?>
  	<tr>
    	<td class="main">
      		<table border="0" width="100%" cellspacing="0" cellpadding="2">
      			<tr valign="top">
        			<td width="40%">
              			<table border="0" cellspacing="0" cellpadding="2">
              				<tr>
                				<td class="invoiceRecords" valign="top" nowrap><?="Card Owner"?></td>
                				<td class="invoiceRecords" valign="top">:&nbsp;</td>
                				<td class="invoiceRecords" nowrap><?=$wp_trans_info_row["credit_card_owner"]?></td>
              				</tr>
              				<tr>
                				<td class="invoiceRecords" valign="top" nowrap><?="E-Mail Address"?></td>
                				<td class="invoiceRecords" valign="top">:&nbsp;</td>
                				<td class="invoiceRecords" nowrap><?=$wp_trans_info_row["email_address"]?></td>
              				</tr>
              				<tr>
                				<td class="invoiceRecords" valign="top" nowrap><?="Telephone Number"?></td>
                				<td class="invoiceRecords" valign="top">:&nbsp;</td>
                				<td class="invoiceRecords"><?=$wp_trans_info_row["tel"]?></td>
              				</tr>
              				<tr>
                				<td class="invoiceRecords" valign="top" nowrap><?="Fax Number"?></td>
                				<td class="invoiceRecords" valign="top">:&nbsp;</td>
                				<td class="invoiceRecords" nowrap><?=$wp_trans_info_row["fax"]?></td>
              				</tr>
              			</table>
        			</td>
        			<td width="30%">
              			<table border="0 cellspacing="0" cellpadding="2">
              				<tr valign="top">
                				<td class="invoiceRecords" nowrap valign="top"><?="Billing Address"?></td>
                				<td class="invoiceRecords" valign="top">:&nbsp;</td>
                				<td class="invoiceRecords"><?=$wp_trans_info_row["billing_address"]?></td>
              				</tr>
              			</table>
        			</td>
        			<td width="30%">
              			<table border="0" cellspacing="0" cellpadding="2">
              				<tr>
                				<td class="invoiceRecords" nowrap><?="Transaction ID"?></b></td>
                				<td class="invoiceRecords" valign="top">:&nbsp;</td>
                				<td class="invoiceRecords" nowrap><?=$wp_trans_info_row["transaction_id"]?></td>
              				</tr>
              				<tr>
                				<td class="invoiceRecords" nowrap><?="Transaction Status"?></b></td>
                				<td class="invoiceRecords" valign="top">:&nbsp;</td>
                				<td class="invoiceRecords" nowrap><?=($wp_trans_info_row["transaction_status"] =="1" ? "Success" : "Failed")?></td>
              				</tr>
              				<tr>
                				<td class="invoiceRecords" nowrap><?="Transaction Amount"?></b></td>
                				<td class="invoiceRecords" valign="top">:&nbsp;</td>
                				<td class="invoiceRecords" nowrap><?=$wp_trans_info_row["transaction_text_amount"]?></td>
              				</tr>
              				
              				<tr>
                				<td class="invoiceRecords" nowrap><?="Card/Wallet Type"?></b></td>
                				<td class="invoiceRecords" valign="top">:&nbsp;</td>
                				<td class="invoiceRecords" nowrap><?=$wp_trans_info_row["credit_card_type"]?></td>
              				</tr>
              			</table>
              		</td>
      			</tr>
      		</table>
   		</td>
  	</tr>
<?
} else {
?>
	<tr>
		<td class="invoiceRecords">No further payment information is available.</td>
	</tr>
<?
}
?>
</table>