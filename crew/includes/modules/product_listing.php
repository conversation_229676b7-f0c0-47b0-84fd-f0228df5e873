<?
if ((int)$HTTP_GET_VARS['item_per_page'] == 0) {
	$itemperpage = (int)MAX_DISPLAY_SEARCH_RESULTS;
} else {
	$itemperpage = (int)$HTTP_GET_VARS['item_per_page'];
}

$listing_split = new splitPageResults($listing_sql, $itemperpage, 'p.products_id', 'page', (isset($$listing_db_link) && $$listing_db_link) ? $listing_db_link : '');

if ( ($listing_split->number_of_rows > 0) && ( (PREV_NEXT_BAR_LOCATION == '1') || (PREV_NEXT_BAR_LOCATION == '3') ) ) {
?>
	<table border="0" width="100%" cellspacing="0" cellpadding="2">
  		<tr>
    		<td class="pageResultsText"><? echo $listing_split->display_count(TEXT_DISPLAY_NUMBER_OF_PRODUCTS); ?></td>
    		<td class="pageResultsText" align="right"><? echo TEXT_RESULT_PAGE . ' ' . $listing_split->display_links(MAX_DISPLAY_PAGE_LINKS, tep_get_all_get_params(array('page', 'info', 'x', 'y'))); ?></td>
  		</tr>
	</table>
<?
}

if ( ($listing_split->number_of_rows > 0) && ((PREV_NEXT_BAR_LOCATION == '2') || (PREV_NEXT_BAR_LOCATION == '3')) ) {
?>
	<table border="0" width="100%" cellspacing="0" cellpadding="2">
  		<tr>
    		<td class="pageResultsText"><? echo $listing_split->display_count(TEXT_DISPLAY_NUMBER_OF_PRODUCTS); ?></td>
    		<td class="pageResultsText" align="right"><? echo TEXT_RESULT_PAGE . ' ' . $listing_split->display_links(MAX_DISPLAY_PAGE_LINKS, tep_get_all_get_params(array('page', 'info', 'x', 'y'))); ?></td>
  		</tr>
	</table>
<?
}

$list_box_contents = array();

for ($col=0, $n=sizeof($column_list); $col<$n; $col++) {
	switch ($column_list[$col]) {
    	case 'PRODUCT_LIST_MODEL':
        	$lc_text = TABLE_HEADING_MODEL;
        	$lc_align = '';
        	break;
      	case 'PRODUCT_LIST_NAME':
        	$lc_text = TABLE_HEADING_PRODUCTS;
        	$lc_align = '';
        	break;
      	case 'PRODUCT_LIST_MANUFACTURER':
        	$lc_text = TABLE_HEADING_MANUFACTURER;
        	$lc_align = '';
        	break;
      	case 'PRODUCT_LIST_PRICE':
        	$lc_text = TABLE_HEADING_PRICE;
        	$lc_align = 'right';
        	break;
      	case 'PRODUCT_LIST_QUANTITY':
        	$lc_text = TABLE_HEADING_QUANTITY;
        	$lc_align = 'right';
        	break;
      	case 'PRODUCT_LIST_WEIGHT':
        	$lc_text = TABLE_HEADING_WEIGHT;
        	$lc_align = 'right';
        	break;
      	case 'PRODUCT_LIST_IMAGE':
        	$lc_text = TABLE_HEADING_IMAGE;
        	$lc_align = 'center';
        	break;
      	case 'PRODUCT_LIST_BUY_NOW':
	        $lc_text = TABLE_HEADING_BUY_NOW;
    	    $lc_align = 'center';
        	break;
      	case 'PRODUCT_SORT_ORDER':
			$lc_text = TABLE_HEADING_PRODUCT_SORT;
			$lc_align = 'center';
			break;        
    }
	
    if ( ($column_list[$col] != 'PRODUCT_LIST_BUY_NOW') && ($column_list[$col] != 'PRODUCT_LIST_IMAGE') ) {
		$lc_text = tep_create_sort_heading($HTTP_GET_VARS['sort'], $col+1, $lc_text, 'class="searchResultsHeading"');
    }
    
    $list_box_contents[0][] = array('align' => $lc_align,
                                    'params' => 'class="searchResultsBoxHeading"',
                                    'text' => '&nbsp;' . $lc_text . '&nbsp;');
}

if ($listing_split->number_of_rows > 0)
{
	$gst_popup = '/popup/gst.html';
	if ($_SESSION['languages_id'] == 2 || $_SESSION['languages_id'] == 3) {
		$gst_popup = '/popup/gst_cn.html';
	}
	
	$rows = 0;
    $listing_query = tep_db_query($listing_split->sql_query);
    while ($listing = tep_db_fetch_array($listing_query)) {
	
      	$rows++;
		//***** Begin Separate Price per Customer Mod *****
      	$listing['specials_new_products_price']=tep_get_products_special_price($listing['products_id']);
		//***** End Separate Price per Customer Mod *****
      	if (($rows/2) == floor($rows/2)) {
        	$list_box_contents[] = array('params' => 'class="searchListingEven"');
      	} else {
        	$list_box_contents[] = array('params' => 'class="searchListingOdd"');
      	}
		
      	$cur_row = sizeof($list_box_contents) - 1;
      	
		$normal_price = $listing['products_price'];
		$special_price = $listing['specials_new_products_price'];
		$pid = $listing['products_id'];
		
		$cat_cfg_array = tep_get_cfg_setting($pid, 'product');
		
		$cpt_id = tep_get_custom_product_type($pid);
		
		if ($listing['products_bundle'] == 'yes') {
  			$status_info = tep_product_add_to_cart_permission($listing['products_id'], 'products_bundle');
		} else if ($listing['products_bundle_dynamic'] == 'yes') {
			$status_info = tep_product_add_to_cart_permission($listing['products_id'], 'products_bundle_dynamic');
		} else {
			$status_info = tep_product_add_to_cart_permission($listing['products_id'], '');
		}
		
		if ((int)$cpt_id > 0) {
			$linkFileName = FILENAME_CUSTOM_PRODUCT_INFO;
			
			$custom_product_info = tep_get_custom_product_listing_info($listing['products_id']);
		} else {
			$linkFileName = FILENAME_PRODUCT_INFO;
			$custom_product_info = array();
		}
		
		/****************************************************
			$show_it = 0 : Out of Stock!
			$show_it = 1 : Available in stock!
			$show_it = 2 : Not enough stock! But pre-order available
		****************************************************/
		$show_it = $status_info["show"];
		$product_catalog_info_query = tep_db_query("select products_quantity, products_date_added 
													from " . TABLE_PRODUCTS . "
				                                    where products_id = '" . $pid . "'");
		$product_catalog_info_row = tep_db_fetch_array($product_catalog_info_query);
        $qty = $product_catalog_info_row["products_quantity"];
		$dty = $product_catalog_info_row["products_date_added"];

      	for ($col=0, $n=sizeof($column_list); $col<$n; $col++) {
        	$lc_align = '';
			
        	switch ($column_list[$col]) {
          		case 'PRODUCT_LIST_MODEL':
            		$lc_align = '';
            		if ($cat_cfg_array['PRODUCT_LIST_MODEL']) {
            			$lc_text = $lc_text = '&nbsp;' . $listing['products_model'] . '&nbsp;';
            		} else {
            			$lc_text = '&nbsp;';
            		}
            		break;
          		case 'PRODUCT_LIST_NAME':
		            $lc_align = '';
		            if ($cat_cfg_array['PRODUCT_LIST_NAME']) {
			            if ((int)$cpt_id > 0 && tep_not_null($custom_product_info['level_text'])) {
	        				$listing['products_name'] .= '<br>' . $custom_product_info['level_text'];
	        			}
	        			
	        			$cat_name_select_sql = "SELECT categories_id 
	        									FROM " . TABLE_PRODUCTS_TO_CATEGORIES . " 
	        									WHERE products_id = " . $listing['products_id'] . " 
	        									ORDER BY products_id";
			            $cat_name_query = tep_db_query($cat_name_select_sql);
				  	    $cat_name = tep_db_fetch_array($cat_name_query);
			            $cat_path = tep_output_generated_category_path_sq($cat_name['categories_id']);
	            		
              			$lc_text = '<table border="0" cellspacing="0" cellpadding="0" width="100%">
              							<tr>
              								<td class="main">' . $listing['products_name'] . '</td>
              							</tr>
              							<tr>
              								<td class="main">'.($listing['custom_products_type_id'] != 1 ? TEXT_SELLING_DELIVERY_TIME . ': ' . (tep_not_null(tep_get_eta_string($status_info['pre_order_time'])) ? tep_get_eta_string($status_info['pre_order_time']) : '-') : '').'</td>
              							</tr>
              							<tr>
              								<td class="main">' . $cat_path . '</td>
              							</tr>
              						</table>';
	            	} else {
	            		$lc_text = '&nbsp;';
	            	}
            		break;
          		case 'PRODUCT_LIST_MANUFACTURER':
		            $lc_align = '';
		            $lc_text = '&nbsp;<a href="' . tep_href_link(FILENAME_DEFAULT, 'manufacturers_id=' . $listing['manufacturers_id']) . '" class="productNavigation">' . $listing['manufacturers_name'] . '</a>&nbsp;';
		            break;
          		case 'PRODUCT_LIST_PRICE':
		            $lc_align = 'right';
		            if ($cat_cfg_array['PRODUCT_LIST_PRICE']) {
			            if (tep_not_null($listing['specials_new_products_price'])) {
			            	if ((int)$cpt_id == 0) {
				            	$lc_text = '<table border="0" cellspacing="0" cellpadding="0">
		            							<tr>
		            								<td class="finalPrice" align="left" nowrap>' . $currencies->format($special_price) . '</td>
		            							</tr>
		            						</table>';
				            } else {
				            	$lc_text = '<table border="0" cellspacing="0" cellpadding="0">
					            				<tr>
					            					<td class="price" align="left">?</td>
					            				</tr>
				            				</table>';
		            		}
			            } else {
							$cust_select_sql = "	SELECT customers_groups_id, customers_discount 
				        							FROM " . TABLE_CUSTOMERS . " 
				        							WHERE customers_id ='" . $customer_id . "'";
	        				$cust_result_sql = tep_db_query($cust_select_sql);
							$cust_row_sql = tep_db_fetch_array($cust_result_sql);
							
							$cust_group_select_sql = "SELECT customers_groups_name FROM " . TABLE_CUSTOMERS_GROUPS . " WHERE customers_groups_id ='" . $cust_row_sql['customers_groups_id'] . "'";
							$cust_group_result_sql = tep_db_query($cust_group_select_sql);
							$cust_group_row_sql = tep_db_fetch_array($cust_group_result_sql);
							
							$customers_groups_info_array = tep_get_customer_group_discount($customer_id, $pid, 'product');
							$customers_groups_discount = $customers_groups_info_array['cust_group_discount'];
							
							$total_customer_discount = $cust_row_sql["customers_discount"] + $customers_groups_discount;
							$ind_cust_discount_asterisk = (abs($cust_row_sql["customers_discount"]) > 0) ? '<span class="requiredInfo">*</span>' : '';
							
							if ((int)$cpt_id != 1) {
								if ($show_it == 2 && abs($cat_cfg_array['PRE_ORDER_DISCOUNT'])) {
									if ($cust_group_row_sql['customers_groups_name'] && abs($total_customer_discount)) {
										$lc_text = '<table border="0" cellspacing="0" cellpadding="0">
														<tr>
															<td class="finalPrice" align="left" nowrap>' . $currencies->display_price($pid, $listing['products_price'], tep_get_tax_rate($listing['products_tax_class_id']), 1, true, $cat_cfg_array['PRE_ORDER_DISCOUNT']) . '</td>
														</tr>
													</table>';
									} else {
										$lc_text = '<table border="0" cellspacing="0" cellpadding="0">
														<tr>
															<td class="finalPriceLabel" align="right" nowrap>Pre-Order:&nbsp;</td><td class="finalPrice" align="left" nowrap>'.$currencies->display_preorder_price($listing['products_price'], tep_get_tax_rate($listing['products_tax_class_id']), 1, $cat_cfg_array['PRE_ORDER_DISCOUNT']). '</td>
														</tr>
													</table>';
									}
								} else {
									if ($cust_group_row_sql['customers_groups_name'] && abs($total_customer_discount)) {
										$lc_text = '<table border="0" cellspacing="0" cellpadding="0">
														<tr>
															<td class="finalPrice" align="left" nowrap>' . $currencies->display_price($pid, $listing['products_price'], tep_get_tax_rate($listing['products_tax_class_id']), 1, false). '</td>
														</tr>
													</table>';
									} else {
										$lc_text = '<table border="0" cellspacing="0" cellpadding="0">
														<tr>
															<td class="finalPrice" align="left" nowrap>' . $currencies->display_price($pid, $normal_price, tep_get_tax_rate($listing['products_tax_class_id'])) . '</td>
														</tr>
													</table>';
									}
								}
							} else {	// Custom product
								if ($custom_product_info["configured"] == '1') {
									if ($cust_group_row_sql['customers_groups_name'] && abs($total_customer_discount)) {
										$lc_text = '<table border="0" cellspacing="0" cellpadding="0">
														<tr>
															<td class="price" align="right">'.$custom_product_info['price']['label'].'&nbsp;</td><td class="price" align="left"><s>' . $currencies->format($custom_product_info['price']['value']) . '</s></td>
														</tr>
														<tr>
															<td class="finalPriceLabel" align="right">'.$cust_group_row_sql['customers_groups_name'].$ind_cust_discount_asterisk.':&nbsp;</td><td class="finalPrice" align="left">' . $currencies->display_price($pid, $custom_product_info['price']['value'], tep_get_tax_rate($listing['products_tax_class_id']), 1, false).'</td>
														</tr>
													</table>';
									} else {
										$lc_text = '<table border="0" cellspacing="0" cellpadding="0">
														<tr>
															<td class="finalPriceLabel" align="right">'.$custom_product_info['price']['label'].'&nbsp;</td><td class="finalPrice" align="left">' . $currencies->display_price($pid, $custom_product_info['price']['value'], tep_get_tax_rate($listing['products_tax_class_id'])) . '</td>
														</tr>
													</table>';
									}
								} else {
									$lc_text = '	<table border="0" cellspacing="0" cellpadding="0">
														<tr>
															<td class="finalPriceLabel" align="right">Regular:&nbsp;</td><td class="finalPrice" align="left">?</td>
														</tr>
													</table>';
								}
							}
	            		}
	            	} else {
            			$lc_text = '&nbsp;';
            		}
            		
					# display GST description
					if (tep_not_null($_SESSION['RegionGST']['tax_title'])) {
						$lc_text .= '<a href="javascript:;" onclick="window.open(\'' . tep_href_link($gst_popup) . '\',\'mywindow\',\'width=400,height=180,scrollbars=yes\');" class="productNavigation">' . sprintf(WARNING_PRODUCT_PRICE_WITHOUT_GST, $_SESSION['RegionGST']['tax_title']) . '</a>';
					}
            		break;
          		case 'PRODUCT_LIST_QUANTITY':
		            $lc_align = 'right';
		            $lc_text = '';
		            //$lc_text = '&nbsp;' . $listing['products_quantity'] . '&nbsp;';
		            break;
          		case 'PRODUCT_LIST_WEIGHT':
		            $lc_align = 'right';
		            if ($cat_cfg_array['PRODUCT_LIST_WEIGHT']) {
		            	$lc_text = '&nbsp;' . ($listing['products_weight'] > 0 ? $listing['products_weight'] . '&nbsp;' . SHIPPING_WEIGHT_UNIT : '') . '&nbsp;';
		            } else {
	            		$lc_text = '&nbsp;';
	            	}
		            break;
          		case 'PRODUCT_LIST_IMAGE':
            		$pro_img = tep_get_products_info($listing['products_id'], 'products_image', $languages_id, $default_languages_id );
            		$aws_obj = new ogm_amazon_ws();
					$aws_obj->set_bucket_key('BUCKET_STATIC');
					$aws_obj->set_filepath('images/products/');
            		$products_image_info_array = $aws_obj->get_image_info($pro_img);
					unset($aws_obj);
					
					if (tep_not_null($products_image_info_array)) {
						$pro_img = $products_image_info_array['src'];
					} else if (tep_not_null($pro_img) && file_exists(DIR_FS_IMAGES . 'products/' . $pro_img)) {
 			            $pro_img = THEMA_IMAGES . 'products/' . $pro_img;
 		            } else {
 		            	$pro_img = THEMA_IMAGES . 'no_product_image.gif';	
 		            }
					
					$pro_img_w = $pro_img_h = '';
            		list($pro_img_w, $pro_img_h) = getimagesize($pro_img);
						
					if ($pro_img_w > PRODUCT_IMAGE_WIDTH) {
						$pro_img_w = PRODUCT_IMAGE_WIDTH;
					}
					if ($pro_img_h > PRODUCT_IMAGE_HEIGHT) {
						$pro_img_h = PRODUCT_IMAGE_HEIGHT;
					}
					
					$display_image = tep_image($pro_img, tep_get_products_info($listing['products_id'], 'products_image_title', $languages_id, $default_languages_id), $pro_img_w, $pro_img_h);
					
            		$lc_align = 'center';
					
            		if ((int)$cpt_id == 2) {
	            		if (isset($HTTP_GET_VARS['manufacturers_id'])) {
	              			$lc_text = '<table border="0" cellSpacing="0" cellPadding="0" width="100%">
	              							<tr>
	              								<tdalign="center">
	              									<a href="' . tep_href_link($linkFileName, 'manufacturers_id=' . $_GET['manufacturers_id'] . '&products_id=' . $listing['products_id']) . '" class="productNavigation">' . $display_image . '</a>
	              									</td>
	              							</tr>
	              						</table>';
	            		} else {
	              			$lc_text = '<table border="0" cellSpacing="0" cellPadding="0" width="100%">
	              							<tr>
	              								<tdalign="center">
	              									<a href="' . tep_href_link($linkFileName, ($cPath ? 'cPath=' . $cPath . '&' : '') . 'products_id=' . $listing['products_id']) . '" class="productNavigation">' . $display_image . '</a>
	              									</td>
	              							</tr>
	              						</table>';
	            		}
	            	} else {
						$lc_text = '	<table border="0" cellSpacing="0" cellPadding="0" width="100%">
											<tr>
												<td align="center">
	            									<a href="' . tep_href_link($linkFileName, ($cPath ? 'cPath=' . $cPath . '&' : '') . 'products_id=' . $listing['products_id']) . '" class="productNavigation">' . $display_image . '</a>
	            								</td>
	            							</tr>
	            						</table>';
	            	}
            		break;
          		case 'PRODUCT_LIST_BUY_NOW':
            		$lc_align = 'center';
		  			$lc_text = '';
		  			
		  			if ($cat_cfg_array['PRODUCT_LIST_BUY_NOW']) {
		  				$delivery_mode_select_sql = "	SELECT products_delivery_mode_id 
												FROM " . TABLE_PRODUCTS_DELIVERY_INFO . " 
												WHERE products_id = '".(int)$listing['products_id']."'";
						$delivery_mode_result_sql = tep_db_query($delivery_mode_select_sql);
						$delivery_mode_num = tep_db_num_rows($delivery_mode_result_sql);
						if ($delivery_mode_num) {
							
							if (file_exists('checkout_xmlhttp.js.php')) { include_once ('checkout_xmlhttp.js.php'); }
							
							$lc_text = '<table cellSpacing="0" height="120" cellPadding="0" width="100%">
		          	    					<tr valign="right" height="25%">
		          	    						<td align="center" width="75%" valign="middle">
												  	<table cellspacing="0" cellpadding="0" border="0"><tr><td></td></tr></table>
												  </td>
		          	    					</tr>
		          	    				</table>';	
						} else {
							if (!$show_it) {
			          	    	$lc_text = '<table cellSpacing="0" height="120" cellPadding="0" width="100%">
			          	    					<tr valign="right" height="25%">
			          	    						<td align="center" width="75%" valign="middle">
													  	<table cellspacing="0" cellpadding="0" border="0"><tr><td>'.
			          	    							tep_div_button(2, IMAGE_BUTTON_OUT_OF_STOCK,'javascript:;', '', 'lightgray_button_fix_width') . '
			          	    							</td></tr></table>
													  </td>
			          	    					</tr>
			          	    				</table>';	
			          	 	} else {
			          	 		if ($listing['products_bundle_dynamic']) {
			          	 			$lc_text .= '<table border="0" cellSpacing="0" height="120" cellPadding="0" width="100%">
		                    						<tr>
		                    							<td>
		                    								<table cellSpacing="0" cellPadding="2" width="100%">';
		                   			$lc_text .= '				<tr>
				                    								<td align="right" valign="middle" width="25%">&nbsp;</td>
				                    								<td align="center" width="75%" valign="middle" class="productSmallText">'.($show_it == 2 ? 'ETA: ' . tep_get_eta_string($status_info["pre_order_time"]) : TEXT_INSTANT_DELIVERY).'</td>
				                    							</tr>
				                    							<tr>
					          	 									<td align="right" valign="middle" width="25%">'.
																		($cat_cfg_array['SHOW_BUY_QTY_BOX'] == 'true' ? tep_draw_input_field('buyqty', '1', 'size="4" maxlength="4" class="qtyInput"') : tep_draw_hidden_field("buyqty", '1') ) . '
																	</td>
																	<td align="center" width="75%" valign="middle" class="productSmallText">
																	'. tep_draw_separator('pixel_trans.gif', '115', '1').'<br>';
									
									if ($show_it == 1) {
										$lc_text .=	'					<table cellpadding=0 cellspacing=0 border=0><tr><td>'. tep_div_button(2, IMAGE_BUTTON_IN_CART,'javascript:showMe(\'' . $listing['products_id'] . '\');', '', 'green_button_fix_width').'</td></tr></table>';
									} else {
										if ($status_info["is_future_product"] == 1) {
											$lc_text .=	'					<table cellpadding=0 cellspacing=0 border=0><tr><td>'.tep_div_button(1, IMAGE_BUTTON_PRE_ORDER, 'javascript:showMe(\'' . $listing['products_id'] . '\');', '', 'yellow_button_fix_width').'</td></tr></table>';
										} else {
											$lc_text .=	'					<table cellpadding=0 cellspacing=0 border=0><tr><td>'.tep_div_button(2, IMAGE_BUTTON_IN_CART,'javascript:showMe(\'' . $listing['products_id'] . '\');', '', 'green_button_fix_width').'</td></tr></table>';
										}
									}
									
									$lc_text .= '					</td>
																</tr>
															</table>
														</td>
				                    				</tr>
				                    			</table>';
			          	 		} else {
		                    		$lc_text .= '<table border="0" cellSpacing="0" height="120" cellPadding="0" width="100%"> 
		                    						<tr>
		                    							<td>
		                    								<table border="0" cellSpacing="0" cellPadding="2" width="100%">
		                    									<tr>';
		                    		
				                    if ((int)$cpt_id == 0) {
				                    	$lc_text .= '				<td align="center" valign="middle" class="productSmallText">' . 'ETA: ' . (tep_not_null(tep_get_eta_string($status_info["pre_order_time"])) ? tep_get_eta_string($status_info["pre_order_time"]) : '-') . '</td>';
				                    }
				                    
				                    $lc_text .=	'				</tr>
				                    							<tr>
					                    							<td align="center" width="75%" valign="middle" class="productSmallText">
																	'. tep_draw_separator('pixel_trans.gif', '115', '1').'<br>';
									
									if ((int)$cpt_id == 0) {
										if ($show_it == 1) {
											$lc_text .=	'					<table cellpadding=0 cellspacing=0 border=0><tr><td>'.tep_div_button(2, IMAGE_BUTTON_IN_CART,'javascript:showMe(\'' . $listing['products_id'] . '\');', '', 'green_button_fix_width').'</td></tr></table>';
										} else {
											if ($status_info["is_future_product"] == 1) {
												$lc_text .=	'					<table cellpadding=0 cellspacing=0 border=0><tr><td>'.tep_div_button(2, IMAGE_BUTTON_PRE_ORDER,'javascript:showMe(\'' . $listing['products_id'] . '\');', '', 'yellow_button_fix_width').'</td></tr></table>';
											} else {
												$lc_text .=	'					<table cellpadding=0 cellspacing=0 border=0><tr><td>'.tep_div_button(2, IMAGE_BUTTON_IN_CART,'javascript:showMe(\'' . $listing['products_id'] . '\');', '', 'green_button_fix_width').'</td></tr></table>';
											}
										}
									} else {
										$buynowcounter++;
										$lc_text .= 					tep_draw_form('buy_now_'.$buynowcounter, tep_href_link(basename($PHP_SELF), tep_get_all_get_params(array('action', 'tpl')) . 'action=buy_now&products_id='. $listing['products_id']."&custom_product=".(int)$cpt_id), 'POST', '') . tep_draw_hidden_field("buyqty", '1');
										
										if ($show_it == 2 && $status_info["is_future_product"] == 1) {
											$lc_text .= '<table cellpadding=0 cellspacing=0 border=0><tr><td>'.tep_div_button(2, IMAGE_BUTTON_PRE_ORDER,'javascript:add_to_shopping_cart(\''.tep_href_link(basename($PHP_SELF), tep_get_all_get_params(array('action', 'tpl')) . 'action=buy_now&products_id='. $listing['products_id']."&custom_product=".(int)$cpt_id).'\' , \'1\', \''.$listing['products_bundle'].'\' ,\''.((int)$cpt_id != 1 ? $listing['products_quantity'] : 1).'\')', '', 'yellow_button_fix_width').'</td></tr></table>';
										} else {
											$lc_text .= '<table cellpadding=0 cellspacing=0 border=0><tr><td>'.tep_div_button(2, IMAGE_BUTTON_IN_CART,'javascript:add_to_shopping_cart(\''.tep_href_link(basename($PHP_SELF), tep_get_all_get_params(array('action', 'tpl')) . 'action=buy_now&products_id='. $listing['products_id']."&custom_product=".(int)$cpt_id).'\' , \'1\', \''.$listing['products_bundle'].'\' ,\''.((int)$cpt_id != 1 ? $listing['products_quantity'] : 1).'\')', '', 'green_button_fix_width').'</td></tr></table>';
										}
										$lc_text .= '						<input type="hidden" name="buy_now_qty" value="'.((int)$cpt_id != 1 ? $listing['products_quantity'] : 1).'">
																			<input type="hidden" name="products_bundle" value="'.$listing['products_bundle'].'">
																		</form>';
									}
											                    		
				                    $lc_text .=	'					</td>
				                    							</tr>
				                    						</table>
				                    					</td>
				                    				</tr>
				                    			</table>';
			          	 		}
			          		}
			          	}
		          	} else {
		          		$lc_text = '&nbsp;';
		          	}
					
            		break;
          		case 'PRODUCT_SORT_ORDER';
            		$lc_align = 'center';
            		$lc_text = '&nbsp;' . $listing['products_sort_order'] . '&nbsp;';
            		break;
        	}
        	$list_box_contents[$cur_row][] = array(	'align' => $lc_align,
             	                                  	'params' => 'class="searchResults"',
                	                               	'text'  => $lc_text);
		}
	}
	
    new productListingBox($list_box_contents, 'class="searchResultsBox"');
} else {
    $list_box_contents = array();

    $list_box_contents[0] = array('params' => 'class="messageRow"');
    $list_box_contents[0][] = array('params' => 'class="messageData"',
                                   'text' => TEXT_NO_PRODUCTS);
	
    new productListingBox($list_box_contents, 'class="messageBox"');
}

if ( ($listing_split->number_of_rows > 0) && ((PREV_NEXT_BAR_LOCATION == '2') || (PREV_NEXT_BAR_LOCATION == '3')) ) {
?>
	<table border="0" width="100%" cellspacing="0" cellpadding="2">
  		<tr>
    		<td class="pageResultsText"><?=$listing_split->display_count(TEXT_DISPLAY_NUMBER_OF_PRODUCTS)?></td>
    		<td class="pageResultsText" align="right"><?=TEXT_RESULT_PAGE . ' ' . $listing_split->display_links(MAX_DISPLAY_PAGE_LINKS, tep_get_all_get_params(array('page', 'info', 'x', 'y')))?></td>
  		</tr>
	</table>
<?
}
?>