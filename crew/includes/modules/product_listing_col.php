<?
$listing_split = new splitPageResults($listing_sql, MAX_DISPLAY_SEARCH_RESULTS, 'p.products_id');
if ( ($listing_split->number_of_rows > 0) && ( (PREV_NEXT_BAR_LOCATION == '1') || (PREV_NEXT_BAR_LOCATION == '3') ) ) {
?>
	<table border="0" width="100%" cellspacing="0" cellpadding="2">
		<tr>
			<td class="pageResultsText"><?=$listing_split->display_count(TEXT_DISPLAY_NUMBER_OF_PRODUCTS)?></td>
	    	<td class="pageResultsText" align="right"><? echo TEXT_RESULT_PAGE . ' ' . $listing_split->display_links(MAX_DISPLAY_PAGE_LINKS, tep_get_all_get_params(array('page', 'info', 'x', 'y'))); ?></td>
		</tr>
	</table>
<?
}

if ( ($listing_split->number_of_rows > 0) && ((PREV_NEXT_BAR_LOCATION == '2') || (PREV_NEXT_BAR_LOCATION == '3')) ) {
?>
	<table border="0" width="100%" cellspacing="0" cellpadding="2">
		<tr>
	    	<td class="pageResultsText"><?=$listing_split->display_count(TEXT_DISPLAY_NUMBER_OF_PRODUCTS)?></td>
	    	<td class="pageResultsText" align="right"><? echo TEXT_RESULT_PAGE . ' ' . $listing_split->display_links(MAX_DISPLAY_PAGE_LINKS, tep_get_all_get_params(array('page', 'info', 'x', 'y'))); ?></td>
		</tr>
	</table>
<?
}

$product_frame_img = THEMA_IMAGES . 'box_products.gif';
if (file_exists($product_frame_img)) {
	list($frame_w, $frame_h) = getimagesize($product_frame_img);
} else {
	$frame_h = '248';
}

$column_display = array();
$column_display['PRODUCT_LIST_MODEL']['param'] = array ('valign' => 'top');
$column_display['PRODUCT_LIST_NAME']['param'] = array ('valign' => 'top');
$column_display['PRODUCT_LIST_MANUFACTURER']['param'] = array ('valign' => 'top');
$column_display['PRODUCT_LIST_PRICE']['param'] = array ('valign' => 'top');
$column_display['PRODUCT_LIST_QUANTITY']['param'] = array ('valign' => 'top');
$column_display['PRODUCT_LIST_WEIGHT']['param'] = array ('valign' => 'top');
$column_display['PRODUCT_LIST_IMAGE']['param'] = array ('valign' => 'top');
$column_display['PRODUCT_LIST_BUY_NOW']['param'] = array ('valign' => 'bottom');

$list_box_contents = array();
if ($listing_split->number_of_rows > 0)
{
	$row = 0;
    $column = 0;
    
    $listing_query = tep_db_query($listing_split->sql_query);
    $this_loop_total = tep_db_num_rows($listing_query);		// get the maximum product count for this page
    
	while ($listing = tep_db_fetch_array($listing_query))
	{
		//***** Begin Separate Price per Customer Mod *****
   		$listing['specials_new_products_price']=tep_get_products_special_price($listing['products_id']);
	 	//***** End Separate Price per Customer Mod *****
		
	  	$normal_price = $listing['products_price'];
	  	$special_price = $listing['specials_new_products_price'];
	  	$pid = $listing['products_id'];
	  	
	  	if ($listing['products_bundle'] == "yes") {
			$status_info = tep_product_add_to_cart_permission($listing['products_id'], "products_bundle");
		} else if ($listing['products_bundle_dynamic'] == "yes") {
			$status_info = tep_product_add_to_cart_permission($listing['products_id'], "products_bundle_dynamic");
		} else {
			$status_info = tep_product_add_to_cart_permission($listing['products_id'], "");
		}
		
		if ((int)$listing['custom_products_type_id'] > 0) {
			$linkFileName = FILENAME_CUSTOM_PRODUCT_INFO;
			
			$custom_product_info = tep_get_custom_product_listing_info($listing['products_id']);
		} else {
			$linkFileName = FILENAME_PRODUCT_INFO;
			$custom_product_info = array();
		}
		
	  	/****************************************************
			$show_it = 0 : Out of Stock!
			$show_it = 1 : Available in stock!
			$show_it = 2 : Not enough stock! But pre-order available
		****************************************************/
		$show_it = $status_info["show"];
		
	  	$w=0;
 		
	  	if ( $column % MAX_DISPLAY_CATEGORIES_PER_ROW == 0) {
	  		$product_display = array();
	  	}
	  	
  		for ($col=0, $n=sizeof($column_list); $col<$n; $col++)
  		{
    		$lc_align = '';
			
    		switch ($column_list[$col]) {
      			case 'PRODUCT_LIST_MODEL':
        			$lc_align = '';
        			//$lc_text = '&nbsp;' . $listing['products_model'] . '&nbsp;';
        			$lc_text = (tep_not_null($listing['products_model'])) ? '<table cellspacing="0" cellpadding="0"><tr><td class="productSmallText" align="center">&nbsp;' . $listing['products_model'] . '&nbsp;</td></tr></table>' : '';
        			$product_display[$col][] = $lc_text;
        			break;
      			case 'PRODUCT_LIST_NAME':
        			$lc_align = '';
        			if ((int)$listing['custom_products_type_id'] > 0 && tep_not_null($custom_product_info['level_text'])) {
        				$listing['products_name'] .= '<br>' . $custom_product_info['level_text'];
        			}
        			if (isset($HTTP_GET_VARS['manufacturers_id'])) {
          				$lc_text = '<table border="0" width="90%" cellspacing="0" cellpadding="0"><tr valign="top" height="50"><td class="productSmallText" align="center"><a href="' . tep_href_link($linkFileName, 'manufacturers_id=' . $HTTP_GET_VARS['manufacturers_id'] . '&products_id=' . $listing['products_id']) . '" class="productNavigation">' . $listing['products_name'] . '</a></td></tr></table>';
        			} else {
          				$lc_text = '<table border="0" width="90%" cellspacing="0" cellpadding="0"><tr valign="top"><td class="productSmallText" align="center"><a href="' . tep_href_link($linkFileName, ($cPath ? 'cPath=' . $cPath . '&' : '') . 'products_id=' . $listing['products_id']) . '" class="productNavigation">' . $listing['products_name'] . '</a></td></tr></table>';
        			}
        			$product_display[$col][] = $lc_text;
        			break;
      			case 'PRODUCT_LIST_MANUFACTURER':
        			$lc_align = '';
        			$lc_text = '&nbsp;<a href="' . tep_href_link(FILENAME_DEFAULT, 'manufacturers_id=' . $listing['manufacturers_id']) . '" class="productNavigation">' . $listing['manufacturers_name'] . '</a>&nbsp;';
        			break;
      			case 'PRODUCT_LIST_PRICE':
       				$lc_align = 'right';
       			 	
					if (tep_not_null($listing['specials_new_products_price'])) {
						if ((int)$listing['custom_products_type_id'] == 0) {
			            	$lc_text = '	<table border="0" cellspacing="0" cellpadding="0">
				            					<tr>
				            						<td class="priceLabel" align="right">Regular:&nbsp;</td><td class="price" align="left"><s>' .  $currencies->format($normal_price) . '</s></td>
				            					</tr>
				            					<tr>
				            						<td class="finalPriceLabel" align="right">Special:&nbsp;</td><td class="finalPrice" align="left">' . $currencies->format($special_price) . '</td>
				            					</tr>
			            					</table>';
			            } else {
			            	$lc_text = '	<table border="0" cellspacing="0" cellpadding="0">
				            					<tr>
				            						<td class="priceLabel" align="right">Regular:&nbsp;</td><td class="price" align="left">?</td>
				            					</tr>
			            					</table>';
			            }
	        		} else {
	        			$cust_select_sql = "	SELECT customers_groups_id, customers_discount 
				       							FROM " . TABLE_CUSTOMERS . " 
				       							WHERE customers_id ='" . $customer_id . "'";
	        			$cust_result_sql = tep_db_query($cust_select_sql);
						$cust_row_sql = tep_db_fetch_array($cust_result_sql);
						
						$cust_group_select_sql = "SELECT customers_groups_name FROM " . TABLE_CUSTOMERS_GROUPS . " WHERE customers_groups_id ='" . $cust_row_sql['customers_groups_id'] . "'";
						$cust_group_result_sql = tep_db_query($cust_group_select_sql);
						$cust_group_row_sql = tep_db_fetch_array($cust_group_result_sql);
						
						$customers_groups_info_array = tep_get_customer_group_discount($customer_id, $pid, 'product');
						$customers_groups_discount = $customers_groups_info_array['cust_group_discount'];
						
						$total_customer_discount = $cust_row_sql["customers_discount"] + $customers_groups_discount;
						$ind_cust_discount_asterisk = (abs($cust_row_sql["customers_discount"]) > 0) ? '<span class="requiredInfo">*</span>' : '';
						
						if ((int)$listing['custom_products_type_id'] != 1) {
							if ($show_it == 2 && abs(PRE_ORDER_DISCOUNT)) {
								if ($cust_group_row_sql['customers_groups_name'] && abs($total_customer_discount)) {
									$lc_text = '<table border="0" cellspacing="0" cellpadding="0">
													<tr>
														<td class="priceLabel" align="right">Regular:&nbsp;</td><td class="price" align="left"><s>' . $currencies->format($normal_price) . '</s></td>
													</tr>
													<tr>
														<td class="priceLabel" align="right">Pre-Order:&nbsp;</td><td class="price" align="left"><s>'.$currencies->display_preorder_price($pid, $listing['products_price'], tep_get_tax_rate($listing['products_tax_class_id']), 1).'</s></td>
													</tr>
													<tr>
														<td class="finalPriceLabel" align="right">'.$cust_group_row_sql['customers_groups_name'].$ind_cust_discount_asterisk.':&nbsp;</td><td class="finalPrice" align="left">' . $currencies->display_price($pid, $listing['products_price'], tep_get_tax_rate($listing['products_tax_class_id']), 1, true).'</td>
													</tr>
												</table>';
								} else {
									$lc_text = '<table border="0" cellspacing="0" cellpadding="0">
													<tr>
														<td class="price" align="right">Regular:&nbsp;</td><td class="price" align="left"><s>' . $currencies->format($normal_price) . '</s></td>
													</tr>
													<tr>
														<td class="finalPriceLabel" align="right">Pre-Order:&nbsp;</td><td class="finalPrice" align="left">'.$currencies->display_preorder_price($pid, $listing['products_price'], tep_get_tax_rate($listing['products_tax_class_id']), 1).'</td>
													</tr>
												</table>';
								}
							} else {
								if ($cust_group_row_sql['customers_groups_name'] && abs($total_customer_discount)) {
									$lc_text = '<table border="0" cellspacing="0" cellpadding="0">
													<tr>
														<td class="price" align="right">Regular:&nbsp;</td><td class="price" align="left"><s>' . $currencies->display_price_nodiscount($pid, $normal_price, tep_get_tax_rate($listing['products_tax_class_id'])) . '</s></td>
													</tr>
													<tr>
														<td class="finalPriceLabel" align="right">'.$cust_group_row_sql['customers_groups_name'].$ind_cust_discount_asterisk.':&nbsp;</td><td class="finalPrice" align="left">' . $currencies->display_price($pid, $listing['products_price'], tep_get_tax_rate($listing['products_tax_class_id']), 1, false).'</td>
													</tr>
												</table>';
								} else {
									$lc_text = '<table border="0" cellspacing="0" cellpadding="0">
													<tr>
														<td class="finalPriceLabel" align="right">Regular:&nbsp;</td><td class="finalPrice" align="left">' . $currencies->display_price($pid, $normal_price, tep_get_tax_rate($listing['products_tax_class_id'])) . '</td>
													</tr>
												</table>';
								}
							}
						} else {
							if ($custom_product_info["configured"] == '1') {
								if ($cust_group_row_sql['customers_groups_name'] && abs($total_customer_discount)) {
									$lc_text = '<table border="0" cellspacing="0" cellpadding="0">
													<tr>
														<td class="price" align="right">'.$custom_product_info['price']['label'].'&nbsp;</td><td class="price" align="left"><s>' . $currencies->format($custom_product_info['price']['value']) . '</s></td>
													</tr>
													<tr>
														<td class="finalPriceLabel" align="right">'.$cust_group_row_sql['customers_groups_name'].$ind_cust_discount_asterisk.':&nbsp;</td><td class="finalPrice" align="left">' . $currencies->display_price($pid, $custom_product_info['price']['value'], tep_get_tax_rate($listing['products_tax_class_id']), 1, false).'</td>
													</tr>
												</table>';
								} else {
									$lc_text = '<table border="0" cellspacing="0" cellpadding="0">
													<tr>
														<td class="finalPriceLabel" align="right">'.$custom_product_info['price']['label'].'&nbsp;</td><td class="finalPrice" align="left">' . $currencies->display_price($pid, $custom_product_info['price']['value'], tep_get_tax_rate($listing['products_tax_class_id'])) . '</td>
													</tr>
												</table>';
								}
							} else {
								$lc_text = '	<table border="0" cellspacing="0" cellpadding="0">
													<tr>
														<td class="finalPriceLabel" align="right">Regular:&nbsp;</td><td class="finalPrice" align="left">?</td>
													</tr>
												</table>';
							}
						}
	        		}
	        		$product_display[$col][] = $lc_text;
	        		break;
      			case 'PRODUCT_LIST_QUANTITY':
       				$lc_align = 'center';
       				$lc_text = '';
       				//'<table cellspacing="0" cellpadding="0"><tr><td class="productSmallText" align="center">Quantity:&nbsp;' . $listing['products_quantity'] . '</td></tr></table>';
        			//$lc_text = '&nbsp;' . $listing['products_quantity'] . '&nbsp;';
        			//$lc_text = '&nbsp;<font class="messageStockWarning">' . stripslashes(WARNING_PRODUCTS_LOW_QUANTITY) . $listing['products_quantity']. ' item/s in our stock. </font>&nbsp;';
		  			//$lc_text = '&nbsp;<tr><td class="messageStockWarning" align="center">' . stripslashes(WARNING_PRODUCTS_LOW_QUANTITY) . '</td></tr>&nbsp;';
		  	 		
		  	 		$product_display[$col][] = $lc_text;
        			break;
      			case 'PRODUCT_LIST_WEIGHT':
        			$lc_align = 'right';
        			$lc_text = ($listing['products_weight'] > 0) ? '<table cellspacing="0" cellpadding="0"><tr><td class="productSmallText" align="center">Weight:&nbsp;' . $listing['products_weight'] . '&nbsp;' . SHIPPING_WEIGHT_UNIT . '</td></tr></table>' : '';
        			$product_display[$col][] = $lc_text;
        			break;
      			case 'PRODUCT_LIST_IMAGE':
					$aws_obj = new ogm_amazon_ws();
					$aws_obj->set_bucket_key('BUCKET_STATIC');
					$aws_obj->set_filepath('images/products/');
					
					$pro_img_w = $pro_img_h = '';
        			$lc_align = 'center';
        			
        			$products_image_info_array = $aws_obj->get_image_info($listing['products_image']);
					
					if (tep_not_null($products_image_info_array)) {
						$pro_img = $products_image_info_array['src'];
        			} else if (tep_not_null($listing['products_image']) && file_exists(DIR_FS_IMAGES . "products/" . $listing['products_image'])) {
						$pro_img = THEMA_IMAGES . "products/" . $listing['products_image'];
					} else {
						$pro_img = THEMA_IMAGES . "products/" . "label_nopic.gif";
					}
					
					list($pro_img_w, $pro_img_h) = getimagesize($pro_img);
					
					if ($pro_img_w > PRODUCT_IMAGE_WIDTH) {
						$pro_img_w = PRODUCT_IMAGE_WIDTH;
					}
					
					if ($pro_img_h > PRODUCT_IMAGE_HEIGHT) {
						$pro_img_h = PRODUCT_IMAGE_HEIGHT;
					}
					
					$max_flag_img_h = 0;
					$max_flag_img_w = 0;
					if ($listing['products_flag_id']) {
    					$flag = explode(",", $listing['products_flag_id']);
    					$aws_obj->set_filepath('images/');
    					
	    				for ($w=0, $o=sizeof($flag); $w < $o; $w++) {
	    					$flag_query = tep_db_query("select products_flag_id, products_flag_name, products_flag_image from products_flag where products_flag_id = '".$flag[$w]."' order by products_flag_id");
		    				
	    					while ($products_flag = tep_db_fetch_array($flag_query)) {
	    						$products_image_info_array = $aws_obj->get_image_info($products_flag['products_flag_image']);
					
								if (tep_not_null($products_image_info_array)) {
	    							list($flag_img_w, $flag_img_h) = getimagesize($products_image_info_array['src']);
									if ($flag_img_h > $max_flag_img_h) 	$max_flag_img_h = $flag_img_h;
									$max_flag_img_w += $flag_img_h;
	    						} else if (file_exists(DIR_FS_IMAGES . $products_flag['products_flag_image'])) {
									list($flag_img_w, $flag_img_h) = getimagesize(DIR_WS_IMAGES . $products_flag['products_flag_image']);
									if ($flag_img_h > $max_flag_img_h) 	$max_flag_img_h = $flag_img_h;
									$max_flag_img_w += $flag_img_h;
								}
								
								unset($products_image_info_array);
		    				}
	    				}
    				} 
    				
    				unset($aws_obj);
    				
        			if (isset($HTTP_GET_VARS['manufacturers_id'])) {
          				$lc_text = '<table width="100%" border="0" cellspacing="0" cellpadding="0"><tr><td align="center" class="productListing"><a href="' . tep_href_link($linkFileName, 'manufacturers_id=' . $HTTP_GET_VARS['manufacturers_id'] . '&products_id=' . $listing['products_id']) . '" class="productNavigation">' . tep_image($pro_img, $listing['products_name'], SMALL_IMAGE_WIDTH, SMALL_IMAGE_HEIGHT) . '</a>&nbsp;</td></tr></table>';
        			} else {
          				if (PRODUCTS_FLAG == 'true') {
          					$lc_text = '<table width="'.$frame_w.'" border="0" cellspacing="0" cellpadding="1">
          									<tr height="'.$frame_h.'">
          										<td align="center" valign="center" style="background:url(\''.$product_frame_img.'\'); background-repeat:no-repeat; background-position:center center;">
          											<a href="' . tep_href_link($linkFileName, ($cPath ? 'cPath=' . $cPath . '&' : '') . 'products_id=' . $listing['products_id']) . '" class="productNavigation">' . tep_image($pro_img, $listing['products_name'], $pro_img_w, $pro_img_h) . '</a>
          										</td>
          									</tr>';
          					if ($listing['products_flag_id']) {
          						$lc_text .= '<tr height="'.$max_flag_img_h.'">
          										<td>';
		    					$flag = explode(",", $listing['products_flag_id']);
			    				for ($w=0, $o=sizeof($flag); $w<$o; $w++) {
			    					$flag_query = tep_db_query("select products_flag_id, products_flag_name, products_flag_image from products_flag where products_flag_id = '".$flag[$w]."' order by products_flag_id");
				    				
			    					while ($products_flag = tep_db_fetch_array($flag_query)) {
				        				$lc_text .= '&nbsp;'.tep_image(DIR_WS_IMAGES . $products_flag['products_flag_image'], $products_flag['products_flag_name']);
				    				}
			    				}
			    				$lc_text .= '</td></tr>';
		    				} 
		    				
		    				$lc_text .= '</table>';
          				} else {
          					$lc_text = '<table width="'.$frame_w.'" height="'.$frame_h.'" border="0" cellspacing="0" cellpadding="0"><tr><td height="168" align="center" valign="center" style="background:url(\''.$product_frame_img.'\'); background-repeat:no-repeat; background-position:center center;"><a href="' . tep_href_link($linkFileName, ($cPath ? 'cPath=' . $cPath . '&' : '') . 'products_id=' . $listing['products_id']) . '" class="productNavigation">' . tep_image($pro_img, $listing['products_name'], SMALL_IMAGE_WIDTH, SMALL_IMAGE_HEIGHT) . '</a></td></tr></table>';
          				}
        			}
        			
        			$product_display[$col][] = $lc_text;
        			break;
      			case 'PRODUCT_LIST_BUY_NOW':
        			$lc_align = 'center';
        			
          			if (!$show_it) {
          				$lc_text = '<table>
          								<tr valign="bottom">
          									<td align="center" class="productSmallText"><table cellspacing="0" cellpadding="0" border="0"><tr><td>'.
											  tep_div_button(2, IMAGE_BUTTON_OUT_OF_STOCK,'javascript:;', '', 'lightgray_button_fix_width').'
          									</td></tr></table></td>
          								</tr>
          							</table>';
          			} else {
          	 			if($listing['products_bundle_dynamic']) {
          	 				$lc_text = '<table><tr valign="bottom" height="30" ><td align="center" class="productSmallText">'.tep_draw_separator('pixel_trans.gif', '115', '1').'<br>' . 
          	 				($show_it == 2 ? 'ETA: '.tep_get_eta_string($status_info["pre_order_time"]).'<br>' : TEXT_INSTANT_DELIVERY.'<br>') .
          	 				tep_draw_form('buy_dynamic_prod', tep_href_link($linkFileName, tep_get_all_get_params(array('action', 'err', 'products_id')) .'products_id='. $listing['products_id']), 'POST', '') . 
          	 								(SHOW_BUY_QTY_BOX == 'true' ? 'Qty: ' . tep_draw_input_field('buyqty', '1', 'size="4" maxlength="4" class="qtyInput"') : tep_draw_hidden_field("buyqty", '1') ) . ' ' .
											($show_it == 1 ? tep_div_button(1, IMAGE_BUTTON_IN_CART,'buy_dynamic_prod', '', 'green_button_fix_width') : tep_div_button(1, IMAGE_BUTTON_PRE_ORDER,'buy_dynamic_prod', '', 'yellow_button_fix_width')) . 
											'</form></td></tr></table>';
          	 			} else {
							$lc_text = '<table><tr valign="bottom"><td align="center" class="productSmallText">'.tep_draw_separator('pixel_trans.gif', '115', '1').'<br>' . 
							($show_it == 2 ? 'ETA: '.tep_get_eta_string($status_info["pre_order_time"]).'<br>' : TEXT_INSTANT_DELIVERY.'<br>') .
							tep_draw_form('buy_now', tep_href_link(basename($PHP_SELF), tep_get_all_get_params(array('action')) .'action=buy_now&products_id='. $listing['products_id']."&custom_product=".(int)$listing['custom_products_type_id']), 'POST', '') . 
											((int)$listing['custom_products_type_id'] != 1 
												? (SHOW_BUY_QTY_BOX == 'true' ? 'Qty: ' . tep_draw_input_field('buyqty', '1', 'size="4" maxlength="4" class="qtyInput"') : tep_draw_hidden_field("buyqty", '1')) 
												: (SHOW_BUY_QTY_BOX == 'true' ? 'Qty: 1' : '') . tep_draw_hidden_field("buyqty", '1') ) . '
											<INPUT type="hidden" name="buy_now_qty" value="'.((int)$listing['custom_products_type_id'] != 1 ? $listing['products_quantity'] : 1).'">
											<INPUT type="hidden" name="products_bundle" value="'.$listing['products_bundle'].'">' . 
											($show_it == 1 ? tep_div_button(1, IMAGE_BUTTON_IN_CART,'buy_now', '', 'green_button_fix_width'): tep_div_button(1, IMAGE_BUTTON_PRE_ORDER,'buy_now', '', 'yellow_button_fix_width')) . 
											'</form></td></tr></table>';
          	 			}
          			}
          			$product_display[$col][] = $lc_text;
        			break;
    		}
    		//$product_contents[] = $lc_text;
  		}
  		
  		$column ++;
  		
  		$displayed_product_count = $row*MAX_DISPLAY_CATEGORIES_PER_ROW + $column;
  		
  		if ( $column % MAX_DISPLAY_CATEGORIES_PER_ROW == 0 || $displayed_product_count==$this_loop_total) {
  			$formated_lc_text = '';
  			foreach ($product_display as $display_order => $cell_array) {
  				$formated_lc_text .= '<tr>';
  				for ($col_count=0; $col_count < count($cell_array); $col_count++) {
  					$formated_lc_text .= '<td align="center" valign="'.$column_display[$column_list[$display_order]]['param']['valign'].'" width="'.(int)(100/MAX_DISPLAY_CATEGORIES_PER_ROW).'%">'.(tep_not_null($cell_array[$col_count]) ? $cell_array[$col_count] : '&nbsp;').'</td>';
  				}
  				// append empty box
  				if ($displayed_product_count==$this_loop_total && count($cell_array) < MAX_DISPLAY_CATEGORIES_PER_ROW) {
  					for ($extra_col=count($cell_array); $extra_col < MAX_DISPLAY_CATEGORIES_PER_ROW; $extra_col++) {
  						$formated_lc_text .= '<td align="center" width="'.(int)(100/MAX_DISPLAY_CATEGORIES_PER_ROW).'%" class="productSmallText">&nbsp;</td>';
  					}
  				}
  				$formated_lc_text .= '</tr>';
  			}
  			
  			if (tep_not_null($formated_lc_text)) {
  				if ($row == "0") {
		  			$formated_lc_text = '<tr><td height="5" colspan="'.MAX_DISPLAY_CATEGORIES_PER_ROW.'" class="productSmallText">&nbsp;</td></tr>' . $formated_lc_text;
  				}
  				$formated_lc_text .= '<tr><td height="5" colspan="'.MAX_DISPLAY_CATEGORIES_PER_ROW.'" class="productSmallText">&nbsp;</td></tr>' ;
  			}
  			
  			$formated_lc_text = '<table border="0" width="100%">'.$formated_lc_text.'</table>';
	  		$list_box_contents[$row][] = array(	'align' => 'center',
	  											'valign' => 'top',
	                                       		'params' => '',
	                                       		'text'  => $formated_lc_text);
	  	}
  		if ($column >= MAX_DISPLAY_CATEGORIES_PER_ROW) {
    		$row ++;
    		$column = 0;
  		}
	}
	
    new productListingBox($list_box_contents);
} else {
	$list_box_contents = array();
	
	$list_box_contents[0] = array('params' => 'class="messageRow"');
	$list_box_contents[0][] = array( 'params' => 'class="messageData"',
    	                           	 'text' => TEXT_NO_PRODUCTS);
	
	new productListingBox($list_box_contents, 'class="messageBox"');
}

if ( ($listing_split->number_of_rows > 0) && ((PREV_NEXT_BAR_LOCATION == '2') || (PREV_NEXT_BAR_LOCATION == '3')) )
{
?>
	<table border="0" width="100%" cellspacing="0" cellpadding="2">
		<tr>
	    	<td class="pageResultsText"><?php echo $listing_split->display_count(TEXT_DISPLAY_NUMBER_OF_PRODUCTS); ?></td>
	    	<td class="pageResultsText" align="right"><?php echo TEXT_RESULT_PAGE . ' ' . $listing_split->display_links(MAX_DISPLAY_PAGE_LINKS, tep_get_all_get_params(array('page', 'info', 'x', 'y'))); ?></td>
	  	</tr>
	</table>
<?
}
?>