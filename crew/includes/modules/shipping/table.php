<?
/*
  	$Id: table.php,v 1.5 2005/05/18 06:32:24 weichen Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2003 osCommerce
	
  	Released under the GNU General Public License
*/

class table
{
	var $code, $title, $description, $icon, $enabled;
	
	// class constructor
    function table()
    {
      	global $order;
		
		$this->code = 'table';
		$this->title = MODULE_SHIPPING_TABLE_TEXT_TITLE;
		$this->description = MODULE_SHIPPING_TABLE_TEXT_DESCRIPTION;
		$this->sort_order = MODULE_SHIPPING_TABLE_SORT_ORDER;
		$this->icon = '';
		$this->tax_class = MODULE_SHIPPING_TABLE_TAX_CLASS;
		$this->enabled = ((MODULE_SHIPPING_TABLE_STATUS == 'True') ? true : false);
		$this->extra_info = true;
		
      	if ( ($this->enabled == true) && ((int)MODULE_SHIPPING_TABLE_ZONE > 0) ) {
        	$check_flag = false;
        	$check_query = tep_db_query("select zone_id from " . TABLE_ZONES_TO_GEO_ZONES . " where geo_zone_id = '" . MODULE_SHIPPING_TABLE_ZONE . "' and zone_country_id = '" . $order->delivery['country']['id'] . "' order by zone_id");
        	while ($check = tep_db_fetch_array($check_query)) {
          		if ($check['zone_id'] < 1) {
            		$check_flag = true;
            		break;
          		} else if ($check['zone_id'] == $order->delivery['zone_id']) {
            		$check_flag = true;
            		break;
          		}
        	}
			
        	if ($check_flag == false) {
          		$this->enabled = false;
        	}
      	}
	}
	
	// class methods
    function quote($method = '')
    {
		global $order, $cart, $shipping_num_boxes, $total_weight;
		global $shipping_weight, $padding_weight;
		
		$shipping_module_sub_title = '';
      	if (MODULE_SHIPPING_TABLE_MODE == 'price') {
        	$order_total = $cart->show_total();
        	//$shipping_module_sub_title = MODULE_SHIPPING_TABLE_TEXT_WEIGHT . ': ' . ($total_weight + $padding_weight) . ' ' . SHIPPING_WEIGHT_UNIT;
      	} else {
      		$order_total = $shipping_weight;
      		/*
      		$shipping_module_sub_title = MODULE_SHIPPING_TABLE_TEXT_WEIGHT . ': ' . ($total_weight + $padding_weight) . ' ' . SHIPPING_WEIGHT_UNIT;
      		if ($shipping_num_boxes > 1) {
             	$shipping_module_sub_title .= '&nbsp;[' . $shipping_num_boxes . ' x ' . number_format($order_total, 4) . '&nbsp;' . SHIPPING_WEIGHT_UNIT . ']';
           	}*/
	        //$order_total = $total_weight;
      	}
		
		$dest_country = $order->delivery["country_id"];
	    $dest_zone = 0;
	    $error = false;
	    
	    $modules_classes_select_sql = "SELECT shipping_classes_id, shipping_classes_name FROM " . TABLE_SHIPPING_CLASSES . " WHERE shipping_module = 'table'";
		$modules_classes_result_sql = tep_db_query($modules_classes_select_sql);
		
		$class_key_value = array();
		
		while ($modules_classes_row = tep_db_fetch_array($modules_classes_result_sql)) {
			$class_values_select_sql = "SELECT shipping_classes_key, shipping_classes_values FROM " . TABLE_SHIPPING_CLASSES_VALUES . " WHERE shipping_classes_id = '" . $modules_classes_row["shipping_classes_id"] . "'";
			$class_values_result_sql = tep_db_query($class_values_select_sql);
			
			$class_key_value[$modules_classes_row["shipping_classes_id"]]["title"] = $modules_classes_row["shipping_classes_name"];
			
			while ($class_values_row = tep_db_fetch_array($class_values_result_sql)) {
				$class_key_value[$modules_classes_row["shipping_classes_id"]][$class_values_row["shipping_classes_key"]] = $class_values_row["shipping_classes_values"];
			}
		}
		
		$valid_class = array();
		foreach ($class_key_value as $method_id => $method_setting) {
			$shipping_zone_select_sql = "SELECT zone_country_id FROM " . TABLE_ZONES_TO_GEO_ZONES . " WHERE geo_zone_id = '" . $method_setting["shipping_zone"] . "' AND (zone_country_id = '" . $dest_country . "' OR zone_country_id = 0) AND IF (zone_country_id = 0, 1, (zone_id LIKE '%,".$order->delivery['zone_id'].",%' OR zone_id LIKE '%,0,%'))";
			$shipping_zone_result_sql = tep_db_query($shipping_zone_select_sql);
			if ($shipping_zone_row = tep_db_fetch_array($shipping_zone_result_sql)) {
				$valid_class[] = $method_id;
			}
		}
		
		$this->quotes = array(	'id' => $this->code,
                            	'module' => MODULE_SHIPPING_TABLE_TEXT_TITLE . (tep_not_null($shipping_module_sub_title) ? '&nbsp;&nbsp;<span class="spacingInfo">'.$shipping_module_sub_title.'</span>' : ''),
                            	'methods' => array() );
		
		if (count($valid_class)) {
			if (tep_not_null($method)) {
				if (in_array($method, $valid_class)) {
					$table_cost = split_dep("[:,]" , $class_key_value[$method]["shipping_rate"]);
			      	$found = false;
					
			      	for ($i=0; $i < count($table_cost); $i+=2) {
			        	if ($order_total <= $table_cost[$i]) {
			          		$shipping = $table_cost[$i+1];
			          		$found = true;
			          		break;
			        	}
			      	}
			      	
			      	if (!$found) {
			      		$max_table_rate_weight = (count($table_cost) >= 2) ? $table_cost[count($table_cost)-2] : 0;
			      		$max_table_rate_charge = (count($table_cost) >= 2) ? $table_cost[count($table_cost)-1] : 0;
			      		
			      		$additional_weight = $order_total - $max_table_rate_weight;
			      		$additional_charge_ratio = tep_not_null($class_key_value[$method]["over_max_unit"]) ? ceil($additional_weight/$class_key_value[$method]["over_max_unit"]) : ceil($additional_weight);
			      		
			      		$shipping = $max_table_rate_charge + $class_key_value[$method]["over_max_charge"] * $additional_charge_ratio;
			      	}
			      	
			      	if (MODULE_SHIPPING_TABLE_MODE == 'price') {
			      		$shipping_method_title = $class_key_value[$method]["title"] . '<br>' . MODULE_SHIPPING_TABLE_TEXT_WEIGHT . ': ' . ($total_weight + $padding_weight) . ' ' . SHIPPING_WEIGHT_UNIT;
			      	} else {
			      		$shipping_method_title = $class_key_value[$method]["title"] . '<br>' . MODULE_SHIPPING_TABLE_TEXT_WEIGHT . ': ' . ($total_weight + $padding_weight) . ' ' . SHIPPING_WEIGHT_UNIT;
			      		if ($shipping_num_boxes > 1) {
			             	$shipping_method_title .= '&nbsp;[' . $shipping_num_boxes . ' x ' . number_format($order_total, 4) . '&nbsp;' . SHIPPING_WEIGHT_UNIT . ']';
			         		$shipping = $shipping * $shipping_num_boxes;
			           	}
			      	}
			      	
			      	$this->quotes["methods"][] = array(	'id' => $method,
                                                 		'title' => $shipping_method_title,
                                                 		'cost' => $shipping + $class_key_value[$method]["handling_fee"]);
				} else {
					$error = true;
				}
			} else {
				foreach ($valid_class as $valid_class_id) {
					$table_cost = split_dep("[:,]" , $class_key_value[$valid_class_id]["shipping_rate"]);
			      	$found = false;
			      	for ($i=0; $i < count($table_cost); $i+=2) {
			        	if ($order_total <= $table_cost[$i]) {
			          		$shipping = $table_cost[$i+1];
			          		$found = true;
			          		break;
			        	}
			      	}
			      	
			      	if (!$found) {
			      		$max_table_rate_weight = (count($table_cost) >= 2) ? $table_cost[count($table_cost)-2] : 0;
			      		$max_table_rate_charge = (count($table_cost) >= 2) ? $table_cost[count($table_cost)-1] : 0;
			      		
			      		$additional_weight = $order_total - $max_table_rate_weight;
			      		$additional_charge_ratio = tep_not_null($class_key_value[$valid_class_id]["over_max_unit"]) ? ceil($additional_weight/$class_key_value[$valid_class_id]["over_max_unit"]) : ceil($additional_weight);
			      		
			      		$shipping = $max_table_rate_charge + $class_key_value[$valid_class_id]["over_max_charge"] * $additional_charge_ratio;
			      	}
			      	
			      	if (MODULE_SHIPPING_TABLE_MODE == 'price') {
			      		$shipping_method_title = $class_key_value[$valid_class_id]["title"] . '<br>' . MODULE_SHIPPING_TABLE_TEXT_WEIGHT . ': ' . ($total_weight + $padding_weight) . ' ' . SHIPPING_WEIGHT_UNIT;
			      	} else {
			      		$shipping_method_title = $class_key_value[$valid_class_id]["title"] . '<br>' . MODULE_SHIPPING_TABLE_TEXT_WEIGHT . ': ' . ($total_weight + $padding_weight) . ' ' . SHIPPING_WEIGHT_UNIT;
			      		if ($shipping_num_boxes > 1) {
			             	$shipping_method_title .= '&nbsp;[' . $shipping_num_boxes . ' x ' . number_format($order_total, 4) . '&nbsp;' . SHIPPING_WEIGHT_UNIT . ']';
			         		$shipping = $shipping * $shipping_num_boxes;
			           	}
			      	}
           			
			      	$this->quotes["methods"][] = array(	'id' => $valid_class_id,
                                                 		'title' => $shipping_method_title,
                                                 		'cost' => $shipping + $class_key_value[$valid_class_id]["handling_fee"]);
				}
			}
		} else {
			$error = true;
		}
		
		if ($this->tax_class > 0) {
        	$this->quotes['tax'] = tep_get_tax_rate($this->tax_class, $order->delivery['country']['id'], $order->delivery['zone_id']);
      	}
		
      	if (tep_not_null($this->icon)) $this->quotes['icon'] = tep_image($this->icon, $this->title);
      	
      	if ($error == true) {
      		$this->quotes['error'] = sprintf(MODULE_SHIPPING_TABLE_INVALID_ZONE, $order->delivery["state"], $order->delivery["country"]["title"]);
      	}
		
      	return $this->quotes;
	}
	
    function check()
    {
      	if (!isset($this->_check)) {
        	$check_query = tep_db_query("select configuration_value from " . TABLE_CONFIGURATION . " where configuration_key = 'MODULE_SHIPPING_TABLE_STATUS'");
        	$this->_check = tep_db_num_rows($check_query);
      	}
      	return $this->_check;
    }
	
	function load_classes()
	{
		$class_table = '<tr>
							<td>
								<table>
									<tr>
										<td class="subRecordsBoxHeading">[<a href="'.tep_href_link(FILENAME_MODULES_CLASSES, 'action=new_class&set=shipping&module=table').'">Add New</a>]</td>
									</tr>
						';
		
		$modules_classes_select_sql = "SELECT * FROM " . TABLE_SHIPPING_CLASSES . " WHERE shipping_module = 'table'";
		$modules_classes_result_sql = tep_db_query($modules_classes_select_sql);
		
		while ($modules_classes_row = tep_db_fetch_array($modules_classes_result_sql)) {
			$class_values_select_sql = "SELECT shipping_classes_key, shipping_classes_values FROM " . TABLE_SHIPPING_CLASSES_VALUES . " WHERE shipping_classes_id = '" . $modules_classes_row["shipping_classes_id"] . "'";
			$class_values_result_sql = tep_db_query($class_values_select_sql);
			
			$class_key_value = array();
			
			while ($class_values_row = tep_db_fetch_array($class_values_result_sql)) {
				$class_key_value[$class_values_row["shipping_classes_key"]] = $class_values_row["shipping_classes_values"];
			}
			
			$class_table .= '		<tr>
										<td class="reportRecords"><a href="'.tep_href_link(FILENAME_MODULES_CLASSES, 'action=edit_class&cID='.$modules_classes_row["shipping_classes_id"].'&set=shipping&module=table').'">'.$modules_classes_row["shipping_classes_name"].'</a></td>
										<td class="reportRecords">&nbsp;['.$class_key_value["shipping_rate"].']</td>
										<td class="reportRecords">&nbsp;&nbsp;[<a href="javascript:void(confirm_delete(\'\', \'this shipping rate\', \''.tep_href_link(FILENAME_MODULES_CLASSES, 'action=delete_class&cID='.$modules_classes_row["shipping_classes_id"].'&set=shipping&module=table').'\'))">Delete</a>]</td>
									</tr>
									';
		}
		
		$class_table .= '			</table>
								</td>
							</tr>';
		return $class_table;
	}
	
    function install()
    {
      	tep_db_query("insert into " . TABLE_CONFIGURATION . " (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, set_function, date_added) VALUES ('Enable Table Method', 'MODULE_SHIPPING_TABLE_STATUS', 'True', 'Do you want to offer table rate shipping?', '6', '1', 'tep_cfg_select_option(array(\'True\', \'False\'), ', now())");
      	//tep_db_query("insert into " . TABLE_CONFIGURATION . " (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, date_added) values ('Shipping Table', 'MODULE_SHIPPING_TABLE_COST', '25:8.50,50:5.50,10000:0.00', 'The shipping cost is based on the total cost or weight of items. Example: 25:8.50,50:5.50,etc.. Up to 25 charge 8.50, from there to 50 charge 5.50, etc', '6', '0', now())");
      	tep_db_query("insert into " . TABLE_CONFIGURATION . " (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, set_function, date_added) values ('Table Method', 'MODULE_SHIPPING_TABLE_MODE', 'weight', 'The shipping cost is based on the order total or the total weight of the items ordered.', '6', '2', 'tep_cfg_select_option(array(\'weight\', \'price\'), ', now())");
      	//tep_db_query("insert into " . TABLE_CONFIGURATION . " (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, date_added) values ('Handling Fee', 'MODULE_SHIPPING_TABLE_HANDLING', '0', 'Handling fee for this shipping method.', '6', '0', now())");
      	//tep_db_query("insert into " . TABLE_CONFIGURATION . " (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, use_function, set_function, date_added) values ('Tax Class', 'MODULE_SHIPPING_TABLE_TAX_CLASS', '0', 'Use the following tax class on the shipping fee.', '6', '0', 'tep_get_tax_class_title', 'tep_cfg_pull_down_tax_classes(', now())");
      	//tep_db_query("insert into " . TABLE_CONFIGURATION . " (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, use_function, set_function, date_added) values ('Shipping Zone', 'MODULE_SHIPPING_TABLE_ZONE', '0', 'If a zone is selected, only enable this shipping method for that zone.', '6', '0', 'tep_get_zone_class_title', 'tep_cfg_pull_down_zone_classes(', now())");
      	tep_db_query("insert into " . TABLE_CONFIGURATION . " (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, date_added) values ('Sort Order', 'MODULE_SHIPPING_TABLE_SORT_ORDER', '5', 'Sort order of display.', '6', '3', now())");
	}
	
    function remove()
    {
      	tep_db_query("delete from " . TABLE_CONFIGURATION . " where configuration_key in ('" . implode("', '", $this->keys()) . "')");
      	
      	// delete all the shipping class defined under this table module.
      	$modules_classes_select_sql = "SELECT shipping_classes_id FROM " . TABLE_SHIPPING_CLASSES . " WHERE shipping_module = 'table'";
		$modules_classes_result_sql = tep_db_query($modules_classes_select_sql);
		
		while ($modules_classes_row = tep_db_fetch_array($modules_classes_result_sql)) {
			$class_values_delete_sql = "DELETE FROM " . TABLE_SHIPPING_CLASSES_VALUES . " WHERE shipping_classes_id = '" . $modules_classes_row["shipping_classes_id"] . "'";
			tep_db_query($class_values_delete_sql);
		}
		
		$modules_classes_delete_sql = "DELETE FROM " . TABLE_SHIPPING_CLASSES . " WHERE shipping_module = 'table'";
		tep_db_query($modules_classes_delete_sql);
    }
	
    function keys()
    {
    	return array('MODULE_SHIPPING_TABLE_STATUS', 'MODULE_SHIPPING_TABLE_MODE', 'MODULE_SHIPPING_TABLE_SORT_ORDER');
      	//return array('MODULE_SHIPPING_TABLE_STATUS', 'MODULE_SHIPPING_TABLE_COST', 'MODULE_SHIPPING_TABLE_MODE', 'MODULE_SHIPPING_TABLE_HANDLING', 'MODULE_SHIPPING_TABLE_TAX_CLASS', 'MODULE_SHIPPING_TABLE_ZONE', 'MODULE_SHIPPING_TABLE_SORT_ORDER');
    }
}
?>