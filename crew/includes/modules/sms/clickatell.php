<?php

class clickatell {
	private $send_url;
    private $api_credential;
    private $unicode_msg;
    
	function  __construct() {
        $this->send_url = "http://api.clickatell.com/http/sendmsg";
        
        $this->api_credential = array(  'global' => array('api_id' => '3384472', 'send_id' => 'offgamers', 'send_pwd' => 'eCRDEYVSeOcIDF'),
                                        'us' => array('api_id' => '3400975', 'send_id' => 'us.offgamers', 'send_pwd' => 'pEo1tr6o2ra0', 'extra' => array('from' => '***********', 'mo' => '1') )
                                    );
        $this->unicode_msg = FALSE;
    }
    
    public function send($mobile_no, $text) {
        $account = 'global';
        
        if (strpos($mobile_no, '1') === 0 || strpos($mobile_no, '52') === 0) {  // Use US Long Number Account for US, Canada and Mexico
            $account = 'us';
        }
        
        $post_array = array('api_id' => $this->api_credential[$account]['api_id'],
                            'user' => $this->api_credential[$account]['send_id'],
                            'password' => $this->api_credential[$account]['send_pwd'],
                            'text' => $text,
                            'to' => $mobile_no
                            );
        
        if (isset($this->api_credential[$account]['extra']) && is_array($this->api_credential[$account]['extra'])) {
            $post_array = array_merge($post_array, $this->api_credential[$account]['extra']);
        }
        
        if ($this->unicode_msg === TRUE) {
            $post_array['unicode'] = '1';
        }
        $curl_obj = new curl();
        $result = $curl_obj->curl_post($this->send_url, $post_array);
        return $result;
    }
}
?>