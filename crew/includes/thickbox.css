P#thickboxPara {font-family: Arial, Verdana, Helvetica, sans-serif; font-size: 12px; color: #000000;}
.titleThickbox {font-family: Arial, Verdana, Helvetica, sans-serif; font-size: 16px; font-weight: bold; color: #333333;}
.smallThickbox {font-family: Arial, Verdana, Helvetica, sans-serif; font-size: 10px; color: #ffffff;}

a.thickbox {font-family: Arial, Verdana, Helvetica, sans-serif; font-size: 16px; font-weight: bold; text-decoration: none;}
a.thickbox:link {color: #ffffff; font-weight: bold; text-decoration: none;}
a.thickbox:visited {color: #ffffff; font-weight: bold; text-decoration: none;}
a.thickbox:hover {color: #FF6400; font-weight: bold; text-decoration: none;}

/* ----------------------------------------------------------------------------------------------------------------*/
/* ---------->>> global settings needed for thickbox <<<-----------------------------------------------------------*/
/* ----------------------------------------------------------------------------------------------------------------*/

html, body {
min-height: 100%;
height: auto !important;
height: 100%
}

/* ----------------------------------------------------------------------------------------------------------------*/
/* ---------->>> thickbox specific link and font settings <<<------------------------------------------------------*/
/* ----------------------------------------------------------------------------------------------------------------*/
#TB_window {
	font: 14px Arial, Helvetica, sans-serif;
	color: #333333;
}

#TB_secondLine {
	font: 10px Arial, Helvetica, sans-serif;
	color:#666666;
}

#TB_window a:link {color: #666666;}
#TB_window a:visited {color: #666666;}
#TB_window a:hover {color: #000;}
#TB_window a:active {color: #666666;}
#TB_window a:focus{color: #666666;}

/* ----------------------------------------------------------------------------------------------------------------*/
/* ---------->>> thickbox settings <<<-----------------------------------------------------------------------------*/
/* ----------------------------------------------------------------------------------------------------------------*/
#TB_overlay {
	position: absolute;
	z-index:100;
	top: 0px;
	left: 0px;
	background-color:#000;
	filter:alpha(opacity=60);
	-moz-opacity: 0.6;
	opacity: 0.6;
}

#TB_window {
	position: absolute;
	background: #ffffff;
	z-index: 102;
	color:#000000;
	display:none;
	border: 4px solid #525252;
	text-align:left;
}

#TB_window img {
	display:block;
	margin: 15px 0 0 15px;
	border-right: 1px solid #ccc;
	border-bottom: 1px solid #ccc;
	border-top: 1px solid #666;
	border-left: 1px solid #666;
}

#TB_caption{
	height:25px;
	padding:7px 30px 10px 25px;
	float:left;
}

#TB_closeWindow{
	height:25px;
	padding:11px 25px 10px 0;
	float:right;
}

#TB_closeAjaxWindow{
	padding:5px 10px 7px 0;
	margin-bottom:1px;
	text-align:right;
	float:right;
}

#TB_ajaxWindowTitle{
	float:left;
	padding:7px 0 5px 10px;
	margin-bottom:1px;
}

#TB_title{
	background-color:#e8e8e8;
	height:27px;
}

#TB_ajaxContent{
	clear:both;
	padding:2px 15px 15px 15px;
	overflow:auto;
	text-align:left;
	line-height:1.4em;
}

#TB_ajaxContent p{
	padding:5px 0px 5px 0px;
}

#TB_load{
	position: absolute;
	display:none;
	height:100px;
	width:100px;
	z-index:101;
}

#TB_HideSelect{
	z-index:99;
	position:absolute;
	top: 0;
	left: 0;
	background-color:#fff;
	border:none;
	filter:alpha(opacity=0);
	-moz-opacity: 0;
	opacity: 0;
}

#TB_iframeContent{
	clear:both;
	border:none;
	margin-bottom:-1px;
	margin-top:1px;
	_margin-bottom:1px;
}
