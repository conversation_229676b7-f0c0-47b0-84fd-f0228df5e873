<?

// check if the 'install' directory exists, and warn of its existence
if (WARN_INSTALL_EXISTENCE == 'true') {
	if (file_exists(dirname($_SERVER['SCRIPT_FILENAME']) . '/install')) {
		$messageStack->add('header', WARNING_INSTALL_DIRECTORY_EXISTS, 'warning');
    }
}

// check if the configure.php file is writeable
if (WARN_CONFIG_WRITEABLE == 'true') {
	if ( (file_exists(dirname($_SERVER['SCRIPT_FILENAME']) . '/includes/configure.php')) && (is_writeable(dirname($_SERVER['SCRIPT_FILENAME']) . '/includes/configure.php')) ) {
		$messageStack->add('header', WARNING_CONFIG_FILE_WRITEABLE, 'warning');
    }
}

// check if the session folder is writeable
if (WARN_SESSION_DIRECTORY_NOT_WRITEABLE == 'true') {
	if (STORE_SESSIONS == '') {
		if (!is_dir(tep_session_save_path())) {
			$messageStack->add('header', WARNING_SESSION_DIRECTORY_NON_EXISTENT, 'warning');
      	} else if (!is_writeable(tep_session_save_path())) {
        	$messageStack->add('header', WARNING_SESSION_DIRECTORY_NOT_WRITEABLE, 'warning');
      	}
	}
}

// check session.auto_start is disabled
if ( (function_exists('ini_get')) && (WARN_SESSION_AUTO_START == 'true') ) {
	if (ini_get('session.auto_start') == '1') {
		$messageStack->add('header', WARNING_SESSION_AUTO_START, 'warning');
	}
}

if ( (WARN_DOWNLOAD_DIRECTORY_NOT_READABLE == 'true') && (DOWNLOAD_ENABLED == 'true') ) {
	if (!is_dir(DIR_FS_DOWNLOAD)) {
		$messageStack->add('header', WARNING_DOWNLOAD_DIRECTORY_NON_EXISTENT, 'warning');
    }
}

if ($messageStack->size('header') > 0) {
	echo $messageStack->output('header');
}

if (isset($_GET['error_message']) && tep_not_null($_GET['error_message'])) {
?>
	<table border="0" width="100%" cellspacing="0" cellpadding="2">
  		<tr class="headerError">
    		<td class="headerError"><?=htmlspecialchars(urldecode($_GET['error_message']))?></td>
  		</tr>
	</table>
<?
}

if (isset($_GET['info_message']) && tep_not_null($_GET['info_message'])) {
?>
	<table border="0" width="100%" cellspacing="0" cellpadding="2">
  		<tr class="headerInfo">
    		<td class="headerInfo"><?=htmlspecialchars($_GET['info_message'])?></td>
  		</tr>
	</table>
<?
}
?>