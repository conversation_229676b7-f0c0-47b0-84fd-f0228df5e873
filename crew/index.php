<?
/*
  	$Id: index.php,v 1.43 2012/02/27 11:32:09 weesiong Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2003 osCommerce
	
  	Released under the GNU General Public License
*/

require('includes/application_top.php');

// Set Localization variable session using ajax and exit
if ($_GET['action'] == "ajax") {echo "1";exit;}

require(DIR_WS_LANGUAGES . $language . '/' . FILENAME_DEFAULT);
require(DIR_WS_LANGUAGES . $language . '/' . FILENAME_CUSTOM_PRODUCT_INFO);
require_once(DIR_WS_FUNCTIONS . 'user_agent.php');

$user_os = tep_get_os($HTTP_USER_AGENT);
$checkout_mode = ($user_os == "Windows" || $user_os == "Linux") ? 'xmlhttp_mode' : 'refresh_mode';

//store customer last path to visit

$cat_grp_id = '1';
if (tep_session_is_registered('customer_id')) {
	if (isset($cPath) && tep_not_null($cPath)) {
		if (tep_session_is_registered('customer_first_name') && tep_session_is_registered('customer_last_name') ) {
			$lc_full_name = $customer_first_name . ' ' . $customer_last_name;
		} else {
			$customer_query = tep_db_query("select customers_firstname, customers_lastname from " . TABLE_CUSTOMERS . " where customers_id = '" . (int)$customer_id . "'");
	      	$customer = tep_db_fetch_array($customer_query);
	      	$lc_full_name = $customer['customers_firstname'] . ' ' . $customer['customers_lastname'];
		}
		
      	$lc_session_id = tep_session_id();
	  	$lc_ip_address = tep_get_ip_address();
	  	$lc_last_page_url = getenv('REQUEST_URI');
	  	$current_time = time();
	  	
	  	$stored_customer_query = tep_db_query("select customers_id from " . TABLE_CUSTOMERS_LAST_CPATH . " where customers_id = '" . (int)$customer_id . "'");
      	if (tep_db_num_rows($stored_customer_query)) {
			tep_db_query("update " . TABLE_CUSTOMERS_LAST_CPATH . " set session_id = '" . tep_db_input($lc_session_id) . "', full_name = '" . tep_db_input($lc_full_name) . "', ip_address = '" . tep_db_input($lc_ip_address) . "', time_entry = '" . tep_db_input($current_time) . "', last_page_url = '" . tep_db_input($lc_last_page_url) . "', last_cpath = '" . $cPath . "' where customers_id = '" . (int)$customer_id . "'");
		} else {
			tep_db_query("insert into " . TABLE_CUSTOMERS_LAST_CPATH . " (customers_id, full_name, session_id, ip_address, time_entry, last_page_url, last_cpath) values ('" . (int)$customer_id . "', '" . tep_db_input($lc_full_name) . "', '" . tep_db_input($lc_session_id) . "', '" . tep_db_input($lc_ip_address) . "', '" . tep_db_input($current_time) . "', '" . tep_db_input($lc_last_page_url) . "', '" . $cPath . "')");
		}
	}
	
	$customer_group_query = tep_db_query("select customers_groups_id from " . TABLE_CUSTOMERS . " where customers_id =  '" . $customer_id . "'");
	$customer_group = tep_db_fetch_array($customer_group_query);

	if ($customer_group['customers_groups_id']){
		$cat_grp_id = $customer_group['customers_groups_id'];	
	}
}

$group_raw1 = tep_db_query("select parent_id, categories_status from categories where categories_id = '".(int)$current_category_id ."'");
$groupsid1 =  tep_db_fetch_array($group_raw1);
if ((int)$current_category_id > 0 && $groupsid1['categories_status']!=1 && tep_not_null($games_categories_path)) {
    tep_redirect(tep_href_link(FILENAME_DEFAULT, tep_get_all_get_params(array('cPath')) . 'cPath=' . $games_categories_path));
}

$group_raw = tep_db_query("select * from categories_groups where categories_id = '".$current_category_id ."' and ((groups_id= '".$cat_grp_id."') or (groups_id=0))");
if ($groupsid =  tep_db_fetch_array($group_raw)) {
	// the following cPath references come from application_top.php
	$category_depth = 'top';
	
	if (isset($cPath) && tep_not_null($cPath)) {
		$categories_products_query = tep_db_query("select count(*) as total from " . TABLE_PRODUCTS_TO_CATEGORIES . " where categories_id = '" . (int)$current_category_id . "'");
		$cateqories_products = tep_db_fetch_array($categories_products_query);
		
		if ($cateqories_products['total'] > 0) {
  			$category_depth = 'products'; // display products
		} else {
  			$category_parent_query = tep_db_query("select count(*) as total from " . TABLE_CATEGORIES . " where parent_id = '" . (int)$current_category_id . "'");
  			$category_parent = tep_db_fetch_array($category_parent_query);
  			if ($category_parent['total'] > 0) {
    			$category_depth = 'nested'; // navigate through the categories
  			} else {
    			$category_depth = 'products'; // category has no products, but display the 'no products' message
  			}
		}
	}
} else { }
// create column list
$define_list = array(	'PRODUCT_LIST_MODEL' => PRODUCT_LIST_MODEL,
	                 	'PRODUCT_LIST_NAME' => PRODUCT_LIST_NAME,
    	             	'PRODUCT_LIST_MANUFACTURER' => PRODUCT_LIST_MANUFACTURER,
        	         	'PRODUCT_LIST_PRICE' => PRODUCT_LIST_PRICE,
            	     	'PRODUCT_LIST_QUANTITY' => PRODUCT_LIST_QUANTITY,
                	 	'PRODUCT_LIST_WEIGHT' => PRODUCT_LIST_WEIGHT,
                 		'PRODUCT_LIST_IMAGE' => PRODUCT_LIST_IMAGE,
                 		'PRODUCT_SORT_ORDER' => PRODUCT_SORT_ORDER,
                 		'PRODUCT_LIST_BUY_NOW' => PRODUCT_LIST_BUY_NOW);

asort($define_list);
$column_list = array();
reset($define_list);
while (list($key, $value) = each($define_list)) {
	if ($value > 0) $column_list[] = $key;
}

$select_column_list = ' p.products_quantity, p.products_bundle, p.products_bundle_dynamic, ';

for ($i=0, $n=sizeof($column_list); $i<$n; $i++) {
	switch ($column_list[$i]) {
		case 'PRODUCT_LIST_MODEL':
  			$select_column_list .= 'p.products_model, ';
  			break;
		case 'PRODUCT_LIST_NAME':
  			$select_column_list .= 'pd.products_name, pd.products_description, ';
  			break;
		case 'PRODUCT_LIST_MANUFACTURER':
  			$select_column_list .= 'm.manufacturers_name, ';
  			break;
		case 'PRODUCT_LIST_QUANTITY':
  			//$select_column_list .= 'p.products_quantity, ';	// default
  			break;
		case 'PRODUCT_LIST_IMAGE':
  			$select_column_list .= 'pd.products_image, pd.products_description_image, p.products_flag_id, ';
  			break;
		case 'PRODUCT_LIST_WEIGHT':
  			$select_column_list .= " replace(rtrim(replace(replace(rtrim(replace(p.products_weight,'0',' ')),' ','0'),'.',' ')),' ','.') as products_weight, ";
  			break;
		case 'PRODUCT_SORT_ORDER':
  			$select_column_list .= 'p.products_sort_order, ';
  			break;
	}
}

//CGDiscountSpecials start
// show the products of a specified manufacturer
if (isset($HTTP_GET_VARS['manufacturers_id'])) {
	if (isset($HTTP_GET_VARS['filter_id']) && tep_not_null($HTTP_GET_VARS['filter_id'])) {
		// We are asked to show only a specific category
		//$listing_sql = "select " . $select_column_list . " p.products_id, p.products_flag_id, p.manufacturers_id, p.products_price, p.products_tax_class_id from " . TABLE_PRODUCTS . " p, " . TABLE_PRODUCTS_DESCRIPTION . " pd, " . TABLE_MANUFACTURERS . " m, " . TABLE_PRODUCTS_TO_CATEGORIES . " p2c where p.products_status = '1' and p.manufacturers_id = m.manufacturers_id and m.manufacturers_id = '" . (int)$HTTP_GET_VARS['manufacturers_id'] . "' and p.products_id = p2c.products_id and pd.products_id = p2c.products_id and pd.language_id = '" . (int)$languages_id . "' and p2c.categories_id = '" . (int)$HTTP_GET_VARS['filter_id'] . "'";
  		$listing_sql = "select " . $select_column_list . " p.custom_products_type_id, p.products_id, p.products_flag_id, p.manufacturers_id, p.products_price, p.products_tax_class_id, p.products_sort_order, IF(s.status, s.specials_new_products_price, NULL) as specials_new_products_price, IF(s.status, s.specials_new_products_price, p.products_price) as final_price from " . TABLE_PRODUCTS . " p, " . TABLE_PRODUCTS_DESCRIPTION . " pd, " . TABLE_MANUFACTURERS . " m, " . TABLE_PRODUCTS_TO_CATEGORIES . " p2c left join " . TABLE_SPECIALS . " s on p.products_id = s.products_id where p.products_status = '1' and p.manufacturers_id = m.manufacturers_id and m.manufacturers_id = '" . (int)$HTTP_GET_VARS['manufacturers_id'] . "' and p.products_id = p2c.products_id and pd.products_id = p2c.products_id and pd.language_id = '" . (int)$languages_id . "' and p2c.categories_id = '" . (int)$HTTP_GET_VARS['filter_id'] . "'";
	} else {
		// We show them all
		//$listing_sql = "select " . $select_column_list . " p.products_id, p.products_flag_id, p.manufacturers_id, p.products_price, p.products_tax_class_id from " . TABLE_PRODUCTS . " p, " . TABLE_PRODUCTS_DESCRIPTION . " pd, " . TABLE_MANUFACTURERS . " m where p.products_status = '1' and pd.products_id = p.products_id and pd.language_id = '" . (int)$languages_id . "' and p.manufacturers_id = m.manufacturers_id and m.manufacturers_id = '" . (int)$HTTP_GET_VARS['manufacturers_id'] . "'";
  		$listing_sql = "select " . $select_column_list . " p.custom_products_type_id, p.products_id, p.products_flag_id, p.manufacturers_id, p.products_price, p.products_tax_class_id, p.products_sort_order, IF(s.status, s.specials_new_products_price, NULL) as specials_new_products_price, IF(s.status, s.specials_new_products_price, p.products_price) as final_price from " . TABLE_PRODUCTS . " p, " . TABLE_PRODUCTS_DESCRIPTION . " pd, " . TABLE_MANUFACTURERS . " m left join " . TABLE_SPECIALS . " s on p.products_id = s.products_id where p.products_status = '1' and pd.products_id = p.products_id and pd.language_id = '" . (int)$languages_id . "' and p.manufacturers_id = m.manufacturers_id and m.manufacturers_id = '" . (int)$HTTP_GET_VARS['manufacturers_id'] . "'";
	}
} else {
	// show the products in a given categorie
	if (isset($HTTP_GET_VARS['filter_id']) && tep_not_null($HTTP_GET_VARS['filter_id'])) {
		// We are asked to show only specific catgeory
		//$listing_sql = "select " . $select_column_list . " p.products_id, p.products_flag_id, p.manufacturers_id, p.products_price, p.products_tax_class_id from " . TABLE_PRODUCTS . " p, " . TABLE_PRODUCTS_DESCRIPTION . " pd, " . TABLE_MANUFACTURERS . " m, " . TABLE_PRODUCTS_TO_CATEGORIES . " p2c where p.products_status = '1' and p.manufacturers_id = m.manufacturers_id and m.manufacturers_id = '" . (int)$HTTP_GET_VARS['filter_id'] . "' and p.products_id = p2c.products_id and pd.products_id = p2c.products_id and pd.language_id = '" . (int)$languages_id . "' and p2c.categories_id = '" . (int)$current_category_id . "'";
  		$listing_sql = 	"select " . $select_column_list . " p.custom_products_type_id, p.products_id, p.products_flag_id, p.manufacturers_id, p.products_price, p.products_tax_class_id, p.products_sort_order, IF(s.status, s.specials_new_products_price, NULL) as specials_new_products_price, IF(s.status, s.specials_new_products_price, p.products_price) as final_price " . 
  						"from " . TABLE_PRODUCTS . " AS p, " . TABLE_PRODUCTS_DESCRIPTION . " AS pd, " . TABLE_MANUFACTURERS . " AS m, " . TABLE_PRODUCTS_TO_CATEGORIES . " AS p2c left join " . TABLE_SPECIALS . " AS s on p.products_id=s.products_id 
  						where p.products_status='1' and ( p.products_bundle='yes' OR p.products_bundle_dynamic='yes' OR (p.products_bundle<>'yes' AND p.products_bundle_dynamic<>'yes' AND p.products_display='1')) and p.manufacturers_id=m.manufacturers_id and m.manufacturers_id='" . (int)$HTTP_GET_VARS['filter_id'] . "' and p.products_id=p2c.products_id and pd.products_id=p2c.products_id and pd.language_id='" . (int)$languages_id . "' and p2c.categories_id='" . (int)$current_category_id . "'"; 
	} else {
		// We show them all
		//$listing_sql = "select " . $select_column_list . " p.products_id, p.products_flag_id, p.manufacturers_id, p.products_price, p.products_tax_class_id from " . TABLE_PRODUCTS_DESCRIPTION . " pd, " . TABLE_PRODUCTS . " p left join " . TABLE_MANUFACTURERS . " m on p.manufacturers_id = m.manufacturers_id, " . TABLE_PRODUCTS_TO_CATEGORIES . " p2c where p.products_status = '1' and p.products_id = p2c.products_id and pd.products_id = p2c.products_id and pd.language_id = '" . (int)$languages_id . "' and p2c.categories_id = '" . (int)$current_category_id . "'";
  		$listing_sql = 	"select " . $select_column_list . " p.custom_products_type_id, p.products_id, p.manufacturers_id, p.manufacturers_id, p.products_price, p.products_tax_class_id, p.products_sort_order, IF(s.status, s.specials_new_products_price, NULL) as specials_new_products_price, IF(s.status, s.specials_new_products_price, p.products_price) as final_price " .
  					   	"from " . TABLE_PRODUCTS_DESCRIPTION . " AS pd, " . TABLE_PRODUCTS . " AS p left join " . TABLE_MANUFACTURERS . " AS m on p.manufacturers_id=m.manufacturers_id, " . TABLE_PRODUCTS_TO_CATEGORIES . " AS p2c left join " . TABLE_SPECIALS . " AS s on p.products_id=s.products_id 
  					   	where p.products_status='1' and ( p.products_bundle='yes' OR p.products_bundle_dynamic='yes' OR (p.products_bundle<>'yes' AND p.products_bundle_dynamic<>'yes' AND p.products_display='1')) and p.products_id=p2c.products_id and pd.products_id=p2c.products_id and pd.language_id='" . (int)$languages_id . "' and p2c.categories_id='" . (int)$current_category_id . "'";
	}
}

if ( (!isset($HTTP_GET_VARS['sort'])) || (!ereg_dep('[1-8][ad]', $HTTP_GET_VARS['sort'])) || (substr($HTTP_GET_VARS['sort'], 0, 1) > sizeof($column_list)) ) {
	for ($i=0, $n=sizeof($column_list); $i<$n; $i++) {
		if ($column_list[$i] == 'PRODUCT_LIST_NAME') {
  			//$HTTP_GET_VARS['sort'] = 'products_sort_order';
  			$listing_sql .= " order by p.products_sort_order, pd.products_name asc";
  			break;
		}
	}
} else {
	$sort_col = substr($HTTP_GET_VARS['sort'], 0 , 1);
	$sort_order = substr($HTTP_GET_VARS['sort'], 1);
	$listing_sql .= ' order by ';
	switch ($column_list[$sort_col-1]) {
		case 'PRODUCT_LIST_MODEL':
  			$listing_sql .= "p.products_model " . ($sort_order == 'd' ? 'desc' : '') . ", pd.products_name";
  			break;
		case 'PRODUCT_LIST_NAME':
  			$listing_sql .= "pd.products_name " . ($sort_order == 'd' ? 'desc' : '');
  			break;
		case 'PRODUCT_LIST_MANUFACTURER':
  			$listing_sql .= "m.manufacturers_name " . ($sort_order == 'd' ? 'desc' : '') . ", pd.products_name";
  			break;
		case 'PRODUCT_LIST_QUANTITY':
  			$listing_sql .= "p.products_quantity " . ($sort_order == 'd' ? 'desc' : '') . ", pd.products_name";
  			break;
		case 'PRODUCT_LIST_IMAGE':
  			$listing_sql .= "pd.products_name";
  			break;
		case 'PRODUCT_LIST_WEIGHT':
  			$listing_sql .= "p.products_weight " . ($sort_order == 'd' ? 'desc' : '') . ", pd.products_name";
  			break;
		case 'PRODUCT_LIST_PRICE':
  			$listing_sql .= "final_price " . ($sort_order == 'd' ? 'desc' : '') . ", pd.products_name";
  			break;
		case 'PRODUCT_SORT_ORDER':
  			$listing_sql .= "p.products_sort_order" . ($sort_order == 'd' ? "desc" : '') . ", pd.products_name";
  			break;
	}
}

if ($const_i_am_allowed_here) {
	if ($page_info->tpl == 3) {
		tep_redirect(tep_href_link(FILENAME_ACCOUNT_STORE_CREDIT_TOPUP, '', 'SSL'));
	} else if ($page_info->main_game_id > 0) {
        if (!$localization->is_supported($page_info->main_game_id, 1)) {
 			$content = CONTENT_ERROR_REGION;
 		} else {
            if (!$current_game_category_id) { // current game does not belongs to this customer group
                tep_redirect(tep_href_link(FILENAME_SEARCH_ALL_GAMES, '', 'SSL'));
            } else {
                $print_listing_flag = true;

                if ($page_info->custom_products_type_id_cat > 0) {
                    $content = $page_info->get_listing_page();
                } else if ($page_info->custom_products_type_id_cat == 0) {
                    tep_redirect(tep_href_link(FILENAME_SEARCH_ALL_GAMES, '', 'SSL'));
                }
            }
        }
	} else {
		if (!$localization->is_supported($page_info->categories_id, 1)) {	// This checking has to come before product type checking
			$content = CONTENT_ERROR_REGION;
		} else if ($page_info->custom_products_type_id_cat > 0) {
			$content = CONTENT_INDEX_PRODUCT_TYPE;
		} else {
			if (isset($_REQUEST['pagetype']) && $_REQUEST['pagetype'] == 'q') {
				$content = CONTENT_INDEX_PRODUCT_TYPE;
			} else {
				tep_redirect(tep_href_link(FILENAME_SEARCH_ALL_GAMES, '', 'SSL'));
			}
		}
	}
} else {
	if ($page_info->tpl == 3) {
		tep_redirect(tep_href_link(FILENAME_ACCOUNT_STORE_CREDIT_TOPUP, '', 'SSL'));
	} else if (isset($_REQUEST['pagetype']) && $_REQUEST['pagetype'] == 'q') {
		$content = CONTENT_INDEX_PRODUCT_TYPE;
	} else {
		if ($page_info->main_game_id > 0 || $page_info->custom_products_type_id_cat > 0) {
			tep_redirect(tep_href_link(FILENAME_SEARCH_ALL_GAMES, '', 'SSL'));
		} else {
			$content = CONTENT_INDEX_DEFAULT;
		}
	}
}
if (!tep_session_is_registered('customer_id')) 	$navigation->set_snapshot();

require(DIR_WS_TEMPLATES . TEMPLATENAME_MAIN_PAGE);

require(DIR_WS_INCLUDES . 'application_bottom.php');
?>