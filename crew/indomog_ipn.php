<?php
require_once('includes/application_top.php');
require_once(DIR_WS_MODULES . 'payment/indomog/classes/indomog_ipn_class.php');
include(DIR_WS_LANGUAGES . $language . '/' . FILENAME_CHECKOUT_PROCESS);
//Process payment

$ipn_content = file_get_contents('php://input');    // '{"Response":{"Data":{"RMID": "0910403545","QID": "1151384","RspCode": "000","RspDesc": "Successful Transaction Complete","TrxID": "2222","TrxStatus": "Success","BID": "2222","TrxRC": "4200","TrxTime": "2013-02-28 15:30:00","TrxValue": "449077"}},"Signature": "956eb64f0b4dd2c416bc2cfcfdb01c308076fa1f","Certificate": ""}';//

if (tep_not_empty($ipn_content)) {
    include_once(DIR_WS_FUNCTIONS . 'custom_product_info.php');
    include_once(DIR_WS_CLASSES . 'order.php');
    include_once(DIR_WS_CLASSES . 'anti_fraud.php');
    include_once(DIR_WS_CLASSES . 'log.php');
    include_once(DIR_WS_CLASSES . 'payment.php');
	include_once(DIR_WS_CLASSES . 'pgs.php');
    
    /**********************************************************
      (1) Check this order is pay to us
      (2) This is Pending order
     **********************************************************/
    $payment = 'indomog';
    
    $ipn_array = json_decode($ipn_content, true);
    
    $indomog_ipn = new indomog_ipn($ipn_array);
    $orders_id = $indomog_ipn->get_order_id();
	if (pgs::getOrderSiteId($orders_id) == '5') {
		$ipnData['data'] = $ipn_content;
		pgs::repostToPGS('indomog', $ipnData);
	} else {
		$order = new order($orders_id);
		$payment_modules = new payment($payment);
		$$payment = new $payment($order->info['payment_methods_id']);
		$$payment->get_merchant_account($order->info['currency']);
		$log_object = new log_files('system');

		if ($orders_id > 0 && isset($indomog_ipn->key['RspCode']) && in_array($indomog_ipn->key['RspCode'], array('000', '001'))) {
			$pg_data_array = array(
				'indomog_orders_id' => $orders_id,
				'indomog_merchant_id' => tep_db_prepare_input($indomog_ipn->key['RMID']),
				'indomog_transaction_id' => tep_db_prepare_input($indomog_ipn->key['TrxID']),
				'indomog_bank_id' => tep_db_prepare_input($indomog_ipn->key['BID']),
				'indomog_transaction_status' => tep_db_prepare_input($indomog_ipn->key['TrxStatus']),
				'indomog_transaction_date' => tep_db_prepare_input($indomog_ipn->key['TrxTime']),
				'indomog_amount' => (double) tep_db_prepare_input($indomog_ipn->key['TrxValue']),
				'indomog_signature' => tep_db_prepare_input($indomog_ipn->key['Signature']),
				'indomog_certificate' => tep_db_prepare_input($indomog_ipn->key['Certificate'])
			);

			$indomog_payment_select_sql = "	SELECT *
											FROM " . TABLE_INDOMOG . "
											WHERE indomog_orders_id = '" . $orders_id . "'";
			$indomog_payment_result_sql = tep_db_query($indomog_payment_select_sql);
			if (tep_db_num_rows($indomog_payment_result_sql)) {
				tep_db_perform(TABLE_INDOMOG, $pg_data_array, 'update', " indomog_orders_id = '" . $orders_id . "' ");
			} else {
				tep_db_perform(TABLE_INDOMOG, $pg_data_array);
			}
		}

		if (isset($indomog_ipn->key['RspCode']) && $indomog_ipn->key['RspCode'] == '000') {
			if ($indomog_ipn->validate_receiver_account($$payment->merchant_id)) {
				if ($indomog_ipn->validate_transaction_data($$payment)) { // To ensure the integrity of the data posted back to merchant's server
					if (tep_not_null($orders_id)) {
						if ($order->info['orders_status_id'] == $$payment->order_status) { // If the order still in initial status
							$indomog_ipn->authenticate($order, $orders_id, $$payment);
							exit;
						}
					}
				}
			}
		}
	}
}
?>