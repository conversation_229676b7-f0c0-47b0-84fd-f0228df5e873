<?php
require('includes/application_top.php');

if (!tep_session_is_registered('customer_id')) {
	tep_redirect(tep_href_link(FILENAME_LOGIN, '', 'SSL'));
}

require(DIR_WS_LANGUAGES . $language . '/' . FILENAME_INVITER);
require(DIR_WS_INCLUDES.'addon/ogmOpenInviterAPI/openinviter.php');

# Start Open Inviter
$inviter = new OpenInviter();
$oi_services = $inviter->getPlugins();
$plugType = '';

if (isset($_REQUEST['vi_provider'])) {
	if ( isset($oi_services['email'][$_REQUEST['vi_provider']]) ) {
		$plugType='email';
	}
}

if (isset($oi_services)) {
	array_multisort($oi_services['email']);	
}

$step = $_REQUEST['step'];
$import_ok = false;
$done = false;
$add_visible = 'block';
$visible = 'none';

if ($_SERVER['REQUEST_METHOD'] == 'POST')	{
	switch ($step) {
		case 'get_contacts':
			$error = false;
			
			if ($plugType == 'email') {
				if (!preg_match("/^[_a-z0-9-]+(\.[_a-z0-9-]+)*@[a-z0-9-]+(\.[a-z0-9-]+)*(\.[a-z]{2,4})$/i", $_REQUEST['vi_email'])) {
					$messageStack->add('inviter', ERROR_ENTER_USERNAME);
					$error = true;
				}
			}
			
			if (empty($_REQUEST['vi_email'])) {
				$messageStack->add('inviter', ERROR_EMAIL_MISSING);
				$error = true;
			}
				
			if (empty($_REQUEST['vi_password'])) {
				$messageStack->add('inviter', ERROR_PASSWORD_MISSING);
				$error = true;
			}
			
			if (empty($_REQUEST['vi_provider'])) {
				$messageStack->add('inviter', ERROR_PROVIDER_MISSING);
				$error = true;
			}
			
			if ($error === false) {
				$inviter->startPlugin($_REQUEST['vi_provider']);
				$internal = $inviter->getInternalError();
	
				if ($internal) {
					$messageStack->add('inviter', $internal);
				}
				elseif (!$inviter->login($_REQUEST['vi_email'], $_REQUEST['vi_password'])) {
					$internal = $inviter->getInternalError();
					$messageStack->add('inviter', ($internal ? $internal : ERROR_LOGIN));
				}
				elseif (false === $contacts=$inviter->getMyContacts()) {
					$messageStack->add('inviter', ERROR_UNABLE_GET_CONTACT);
				}
				else {
					$import_ok=true;
					$step='send_invites';
					$visible='block';
					$add_visible='none';
					$_REQUEST['vi_session_id']=$inviter->plugin->getSessionID();
					
					// check contacts that can be listed out
					if ($inviter->showContacts()) {
						$vi_contacts = array();
						foreach($contacts as $contact_email => $contact_name) {
							/*
							if (tep_not_null($contact_email) && preg_match("/^[_a-z0-9-]+(\.[_a-z0-9-]+)*@[a-z0-9-]+(\.[a-z0-9-]+)*(\.[a-z]{2,4})$/i", $contact_email)) { 
								
								// filter out existing customer contacts
								$check_customer_sql = "	SELECT customers_email_address 
								                      	FROM ". TABLE_CUSTOMERS ." 
								                      	WHERE customers_email_address = '".tep_db_input($contact_email)."'";
								$check_customer_result_sql = tep_db_query($check_customer_sql);
								if (tep_db_num_rows($check_customer_result_sql) == 0) {
									
									// filter out existing ignore list contacts
									$check_ignore_sql = "	SELECT inviter_ignore_contact 
															FROM ". TABLE_INVITER_IGNORE_LIST ." 
															WHERE inviter_ignore_contact = '".tep_db_input($contact_email)."'";
									$check_ignore_result_sql = tep_db_query($check_ignore_sql);
									if (tep_db_num_rows($check_ignore_result_sql) == 0) {
										
										// filter out existing invitee contacts
										$check_contacts_sql = "	SELECT inviter_contacts_contact_email 
																FROM ". TABLE_INVITER_CONTACTS ." 
																WHERE inviter_contacts_contact_email = '".tep_db_input($contact_email)."'";
										$check_contacts_result_sql = tep_db_query($check_contacts_sql);
										if (tep_db_num_rows($check_contacts_result_sql) == 0) {
							*/
											$vi_list[] = $contact_email;
											$vi_contacts[$contact_email] = $contact_name;
							/*
										}
									} // fi
								} // fi
							} // fi
							*/
						} // foreach
					} // fi
					
				} // else
			} // fi
			break;
			
			
		case 'send_invites':
			$error = false;
			if (empty($_REQUEST['vi_provider'])) {
				$messageStack->add('inviter', ERROR_PROVIDER_MISSING);
				$error = true;
			}
			else {
				$inviter->startPlugin($_REQUEST['vi_provider']);
				$internal = $inviter->getInternalError();
				
				if ($internal) {
					$messageStack->add('inviter', $internal);
				}
				else {
					if ($plugType == 'email') {
						if (!preg_match("/^[_a-z0-9-]+(\.[_a-z0-9-]+)*@[a-z0-9-]+(\.[a-z0-9-]+)*(\.[a-z]{2,4})$/i", $_REQUEST['vi_email'])) {
							$messageStack->add('inviter', ERROR_ENTER_USERNAME);
							$error = true;
						}
					}
					
					if (empty($_REQUEST['vi_email'])) {
						$messageStack->add('inviter', ERROR_INVITER_INFOR_MISSING);
						$error = true;
					}
					
					if (empty($_REQUEST['vi_session_id'])) {
						$messageStack->add('inviter', ERROR_NO_ACTIVE_SESSION);
						$error = true;
					}
						
					$selected_vi_contacts = array();				
					if ($inviter->showContacts())	{
						foreach ($_REQUEST as $key => $val) {
							if (strpos($key,'check_') !== false) {
								$check_num = substr($key,strlen("check_"));
								if (tep_not_null($_REQUEST['inv_contact_'.$check_num]) && preg_match("/^[_a-z0-9-]+(\.[_a-z0-9-]+)*@[a-z0-9-]+(\.[a-z0-9-]+)*(\.[a-z]{2,4})$/i", $_REQUEST['inv_contact_'.$check_num])) {
									$selected_vi_contacts[$_REQUEST['inv_contact_'.$check_num]] = $_REQUEST['inv_name_'.$check_num];
								}
							}
						}
	
						if (count($selected_vi_contacts) == 0) {
							$messageStack->add('inviter', ERROR_SELECT_CONTACTS);
							$error = true;
						}
					}
				}
			}
			
			if ($error === false)
			{
				$insert_contacts = false;
				$vi_provider = tep_db_prepare_input($_REQUEST['vi_provider']);
				$vi_message = tep_db_prepare_input($_REQUEST['vi_message']);
				$vi_remoteip = tep_get_ip_address();
				
				// Insert records into tables
				foreach ($selected_vi_contacts as $vi_contact_email => $vi_contact_name) {	

					if (tep_not_null($vi_contact_email) && preg_match("/^[_a-z0-9-]+(\.[_a-z0-9-]+)*@[a-z0-9-]+(\.[a-z0-9-]+)*(\.[a-z]{2,4})$/i", $vi_contact_email)) { 

						// filter out existing ignore list contacts
						$check_ignore_sql = "	SELECT inviter_ignore_contact 
												FROM ". TABLE_INVITER_IGNORE_LIST ." 
												WHERE inviter_ignore_contact = '".tep_db_input($vi_contact_email)."'";
						$check_ignore_result_sql = tep_db_query($check_ignore_sql);
						if (tep_db_num_rows($check_ignore_result_sql) == 0) {
							
							// filter out existing invitee contacts
							$check_contacts_sql = "	SELECT inviter_contacts_contact_email 
													FROM ". TABLE_INVITER_CONTACTS ." 
													WHERE inviter_contacts_contact_email = '".tep_db_input($vi_contact_email)."'";
							$check_contacts_result_sql = tep_db_query($check_contacts_sql);
							if (tep_db_num_rows($check_contacts_result_sql) == 0) {
											
								$vi_contact_email = tep_db_prepare_input($vi_contact_email);
								$vi_contact_name = tep_db_prepare_input($vi_contact_name);
								$temp_contact[] = $vi_contact_email;
								$vi_code = tep_create_random_value(8);
								
								$inviter_imports_array = array('customer_id' => $customer_id,
															  'inviter_imports_service' => $vi_provider,
															  'inviter_imports_contact_email' => $vi_contact_email,
															  'inviter_imports_contact_name' => $vi_contact_name,
															  'inviter_imports_code' => $vi_code,
															  'inviter_imports_insert_datetime' => 'now()',
															  'inviter_imports_insert_ip' => $vi_remoteip );
								if (tep_db_perform(TABLE_INVITER_IMPORTS, $inviter_imports_array)) {
									$inviter_imports_insert_id = tep_db_insert_id();
									
									// store links + message
									$join_link = "?a_aid=".$customer_id."&code=".$vi_code."&rec=".$inviter_imports_insert_id;
									$optout_link = "?a_aid=".$customer_id."&code=".$vi_code."&rec=".$inviter_imports_insert_id."&email=".$vi_contact_email;
									$vi_message  = $join_link.'#~#'.$optout_link.'#~#'.htmlentities($vi_message);
									
									$inviter_messages_array = array('inviter_imports_id' => $inviter_imports_insert_id, 
																	'inviter_messages_language_id' => $languages_id,
																	'message' => $vi_message);
									$insert_messages = tep_db_perform(TABLE_INVITER_MESSAGES, $inviter_messages_array);
									
									$inviter_contacts_array = array('inviter_contacts_contact_email' => $vi_contact_email,
																	'inviter_contacts_service' => $vi_provider,
																	'inviter_contacts_insert_datetime' => 'now()');
									if (tep_db_perform(TABLE_INVITER_CONTACTS, $inviter_contacts_array)) {
										$insert_contacts = true;
									}
								} 
								else {
									$vi_contacts_invalid_list[] = $vi_contact_email;
								}
							} // fi
							else {
								$vi_contacts_unable_deliver_list[] = $vi_contact_email; //$vi_contacts_exist_list[] = $vi_contact_email;
							}
						} // fi
						else {
							$vi_contacts_unable_deliver_list[] = $vi_contact_email; //$vi_contacts_ignore_list[] = $vi_contact_email;
						}
					} // fi
					else {
						$vi_contacts_invalid_list[] = $vi_contact_email;	
					}
				} // foreach
				
				if ($insert_contacts === true) {
					$messageStack->add_session('inviter', SUCCESS_SENT_OUT_INVITATION, 'success');
					/*
					if (count($vi_contacts_invalid_list) > 0) {
						$messageStack->add_session('inviter', sprintf(ERROR_SENT_OUT_INVITATION, implode(', ', $vi_contacts_invalid_list)));
					}
					if (count($vi_contacts_unable_deliver_list) > 0) {
						$messageStack->add_session('inviter', sprintf(ERROR_EMAIL_UNABLE_DELIVER, implode(', ', $vi_contacts_unable_deliver_list)));
					}
					*/
					tep_redirect(tep_href_link(FILENAME_INVITER, 'action=success_invite', 'SSL'));
				}
				else {
					/*
					if (count($vi_contacts_invalid_list) > 0) {
						$messageStack->add('inviter', sprintf(ERROR_SENT_OUT_INVITATION, implode(', ', $vi_contacts_invalid_list)));
					}
					if (count($vi_contacts_unable_deliver_list) > 0) {
						$messageStack->add('inviter', sprintf(ERROR_EMAIL_UNABLE_DELIVER, implode(', ', $vi_contacts_unable_deliver_list)));
					}
					*/
				}
				//$done=true;
			} // fi
			break;
			
			
		case 'add_manually':
			$mi_message = tep_not_null($_REQUEST['mi_message'])? tep_db_prepare_input($_REQUEST['mi_message']) : '';
			$arr_mi_email = tep_not_null($_REQUEST['mi_to'])? explode(',', $_REQUEST['mi_to']): array();
			
			$error = false;
			if (empty($_REQUEST['mi_to'])) {
				$messageStack->add('inviter', ERROR_INVITEE_EMAIL_MISSING);
				$error = true;
			}
			
			if (strlen($_REQUEST['mi_message']) > 0 && strlen($_REQUEST['mi_message']) > 100) {
				$messageStack->add('inviter', MESSAGE_LENGTH_EXCEEDED . (strlen($_REQUEST['mi_message']) - 100));
				$error = true;	
			}
			
			if ($error === false) {
				$mi_insert_contacts = false;
				$mi_provider = 'sent_mannually';
				$mi_email = array_unique($arr_mi_email);
				$mi_remoteip = tep_get_ip_address();
				
				$mi_contact_list = array();
				$mi_contact_exist_list = array();
				$mi_contact_ignore_list = array();
				
				for($mi=0; $mi < count($mi_email); $mi++) {
					$mi_contact_email = trim($mi_email[$mi]);
					$mi_code = tep_create_random_value(8);
					
					if (tep_not_null($mi_contact_email) && preg_match("/^[_a-z0-9-]+(\.[_a-z0-9-]+)*@[a-z0-9-]+(\.[a-z0-9-]+)*(\.[a-z]{2,4})$/i", $mi_contact_email)) {

						$check_ignore_sql = "SELECT * FROM ". TABLE_INVITER_IGNORE_LIST ." WHERE inviter_ignore_contact = '".$mi_contact_email."'";
						$check_ignore_result_sql = tep_db_query($check_ignore_sql);
						if (tep_db_num_rows($check_ignore_result_sql) == 0) {
							
							$check_contacts_sql = "SELECT * FROM ". TABLE_INVITER_CONTACTS ." WHERE inviter_contacts_contact_email = '".$mi_contact_email."'"; //AND inviter_contacts_insert_datetime + INTERVAL ". NUMBER_OF_LAPSE_DAY ." DAY) < now()";
							$check_contacts_result_sql = tep_db_query($check_contacts_sql);
							if (tep_db_num_rows($check_contacts_result_sql) == 0) {
								
								$inviter_imports_array= array('customer_id' => $customer_id,
													  'inviter_imports_service' => $mi_provider,
													  'inviter_imports_contact_email' => $mi_contact_email,
													  'inviter_imports_contact_name' => $mi_contact_email,
													  'inviter_imports_code' => $mi_code,
													  'inviter_imports_insert_datetime' => 'now()',
													  'inviter_imports_insert_ip' => $mi_remoteip );
													  
								if (tep_db_perform(TABLE_INVITER_IMPORTS, $inviter_imports_array)){
									$inviter_imports_insert_id = tep_db_insert_id();
									
									// store links + message
									$join_link = "?a_aid=".$customer_id."&code=".$mi_code."&rec=".$inviter_imports_insert_id;
									$optout_link = "?a_aid=".$customer_id."&code=".$mi_code."&rec=".$inviter_imports_insert_id."&email=".$mi_contact_email;
									$mi_message  = $join_link.'#~#'.$optout_link.'#~#'.htmlentities($mi_message);
									
									$inviter_messages_array = array('inviter_imports_id' => $inviter_imports_insert_id,
																	'inviter_messages_language_id' => $languages_id,
																	'message' => $mi_message );
									$insert_messages = tep_db_perform(TABLE_INVITER_MESSAGES, $inviter_messages_array);
									
									$inviter_contacts_array = array('inviter_contacts_contact_email' => $mi_contact_email,
																	'inviter_contacts_service' => $mi_provider,
																	'inviter_contacts_insert_datetime' => 'now()' );
									if (tep_db_perform(TABLE_INVITER_CONTACTS, $inviter_contacts_array)) {
										$mi_insert_contacts = true;
									} // fi
								} // fi
								else {
									$mi_contact_invalid_list[] = $mi_contact_email; //$mi_contact_list[] = $mi_contact_email;
								}
							} // fi
							else {
								$mi_contact_unable_deliver_list[] = $mi_contact_email; //$mi_contact_exist_list[] = $mi_contact_email;
							}
						} // fi
						else {
							$mi_contact_unable_deliver_list[] = $mi_contact_email; //$mi_contact_ignore_list[] = $mi_contact_email;	
						}
					} // fi
					else {
						$mi_contact_invalid_list[] = $mi_contact_email;
					}
				} // for
	
				if ($mi_insert_contacts === true) {
					$messageStack->add_session('inviter', SUCCESS_SENT_OUT_INVITATION, 'success');
					/*
					if (count($mi_contact_invalid_list) > 0) {
						$messageStack->add_session('inviter', sprintf(ERROR_SENT_OUT_INVITATION, implode(', ', $mi_contact_invalid_list)));
					}
					if (count($mi_contact_unable_deliver_list) > 0) {
						$messageStack->add_session('inviter', sprintf(ERROR_EMAIL_UNABLE_DELIVER, implode(', ',$mi_contact_unable_deliver_list)));
					}
					*/
					tep_redirect(tep_href_link(FILENAME_INVITER, 'action=success_invite', 'SSL'));
				}
				else {
					/*
					if (count($mi_contact_invalid_list) > 0) {
						$messageStack->add('inviter', sprintf(ERROR_SENT_OUT_INVITATION, implode(', ', $mi_contact_invalid_list)));
					}
					if (count($mi_contact_unable_deliver_list) > 0) {
						$messageStack->add('inviter', sprintf(ERROR_EMAIL_UNABLE_DELIVER, implode(', ',$mi_contact_unable_deliver_list)));
					}
					*/
				}
			} // fi
			break;
			
			
		case 'delete_selected':
			$error = false;
			$delete_contacts = false;
			$cant_delete_contacts = array();
			foreach ($_REQUEST as $key => $val) {
				if (strpos($key,'rec_') !== false) {
					$check_num = substr($key, strlen('rec_'));
					$contact_tobe_removed = tep_db_prepare_input($_REQUEST['rm_contact_'.$check_num]);
					$removed_contact_list[] = tep_db_prepare_input($_REQUEST['rm_contact_'.$check_num]);
					
					$delete_invitees_message_sql = "DELETE FROM ". TABLE_INVITER_MESSAGES ." WHERE inviter_imports_id='".(int) $check_num."' ";
					if (tep_db_query($delete_invitees_message_sql)) {
						$delete_invitees_sql = "DELETE FROM ". TABLE_INVITER_IMPORTS ." WHERE customer_id='".(int) $customer_id."' AND inviter_imports_contact_email='".$contact_tobe_removed."' ";
						if (tep_db_query($delete_invitees_sql)) {
							$delete_contacts = true;
						}
						else {
							$cant_delete_contacts[] = $contact_tobe_removed;
						}
					}
				}
			} // foreach
			
			if (count($removed_contact_list)==0) {
				$messageStack->add('inviter', ERROR_DELETE_SELECTED);
			}
			
			if ($delete_contacts === true) {
				$messageStack->add_session('inviter', SUCCESS_DELETE_INVITED_CONTACT, 'success');
				tep_redirect(tep_href_link(FILENAME_INVITER, '', 'SSL'));
			}
			else {
				$messageStack->add('inviter', sprintf(ERROR_DELETE_SELECTED_CONTACT, implode(', ', $cant_delete_contacts)));
			}
			break;
			
			
		case 'send_reminder':
			$error = false;
			$reminder_sent_lately = array();
			$reminder_in_ignore_list = array();
			foreach ($_REQUEST as $key => $val) {
				if (strpos($key,'rec_') !== false) {
					$check_num = substr($key,strlen("rec_"));
					
					$check_ignore_sql = "SELECT * FROM ". TABLE_INVITER_IGNORE_LIST ." WHERE inviter_ignore_contact = '". $_REQUEST['rm_contact_'.$check_num] ."'";
					$check_ignore_result_sql = tep_db_query($check_ignore_sql);
					if (tep_db_num_rows($check_ignore_result_sql) == 0) {
						
						$check_contacts_sql = "SELECT * FROM ". TABLE_INVITER_CONTACTS ." WHERE inviter_contacts_contact_email = '". $_REQUEST['rm_contact_'.$check_num] ."' "; //AND inviter_contacts_insert_datetime + INTERVAL ". NUMBER_OF_LAPSE_DAY ." DAY) < now()";
						$check_contacts_result_sql = tep_db_query($check_contacts_sql);
						if ($cont_rows = tep_db_fetch_array($check_contacts_result_sql) ) {
							
							$cr_date = explode('-',substr($cont_rows['inviter_contacts_insert_datetime'],0,strpos(' ',$cont_rows['inviter_contacts_insert_datetime'])-1));

							if (mktime(0,0,0,date('m'),date('d'),date('Y')) >= mktime(0,0,0,$cr_date[1],$cr_date[2] + NUMBER_OF_LAPSE_DAY,$cr_date[0])) {
								$reminder_list[$val]['service'] = $_REQUEST['rm_service_'.$check_num];
								$reminder_list[$val]['email'] = $_REQUEST['rm_contact_'.$check_num];
								$reminder_list[$val]['name'] = $_REQUEST['rm_name_'.$check_num];
								$reminder_list[$val]['counter'] = $_REQUEST['rm_counter_'.$check_num];
								$reminder_list[$val]['datetime'] = $_REQUEST['rm_datetime_'.$check_num];
							}
							else {
								$reminder_sent_lately[] = $_REQUEST['rm_contact_'.$check_num];
							}
						} // fi
					} // fi
					else {
						$reminder_in_ignore_list[] = $_REQUEST['rm_contact_'.$check_num];
					}
				} // fi
			} // foreach
			
			if (count($reminder_list) == 0 && count($reminder_sent_lately) == 0 && count($reminder_in_ignore_list) == 0) {
				$messageStack->add('inviter', ERROR_SELECT_REMINDER_CONTACTS);
				$error = true;
			}
			
			if ($error === false)
			{	
				$insert_contacts = false;
				
				// Insert records into tables
				if (!empty($reminder_list)) {
					$reminder_cant_send = array();
					foreach ($reminder_list as $list_id => $list_value) {
						$list_service = tep_db_prepare_input($list_value['service']);
						$list_email = tep_db_prepare_input($list_value['email']);
						$list_counter = $list_value['counter'] + 1;
						$list_remoteip = tep_get_ip_address();
						$list_code = tep_create_random_value(8);
						
						// store links + message
						$join_link = "?a_aid=".$customer_id."&code=".$list_code."&rec=".$list_id;
						$optout_link = "?a_aid=".$customer_id."&code=".$list_code."&rec=".$list_id."&email=".$list_email;
						$list_message  = tep_db_prepare_input($join_link.'#~#'.$optout_link.'#~#'.MESSAGE_REMINDER);
						
						$inviter_imports_array = array('inviter_imports_insert_counter' => $list_counter,
													  'inviter_imports_insert_datetime' => 'now()',
													  'inviter_imports_insert_ip' => $list_remoteip);
						if (tep_db_perform(TABLE_INVITER_IMPORTS, $inviter_imports_array, "update", "inviter_imports_id=".(int) $list_id." AND customer_id='".(int) $customer_id."' ")){
							$inviter_messages_array = array('inviter_imports_id' => $list_id, 'inviter_messages_language_id' => $languages_id, 'message' => $list_message);
							$insert_messages = tep_db_perform(TABLE_INVITER_MESSAGES, $inviter_messages_array);
							
							$inviter_contacts_array = array('inviter_contacts_insert_datetime' => 'now()');
							if (tep_db_perform(TABLE_INVITER_CONTACTS, $inviter_contacts_array, "update", "inviter_contacts_contact_email='".$list_email."' AND inviter_contacts_service='".$list_service."' ")) {
								$insert_contacts = true;
							} // fi
						} // fi
						else {
							$reminder_cant_send[] = $list_email;
						}
					} // foreach
				} // fi
	
				if ($insert_contacts === true) {
					$messageStack->add_session('inviter', SUCCESS_SENT_OUT_REMINDER, 'success');
					/*
					if (count($reminder_cant_send) > 0) {
						$messageStack->add_session('inviter', sprintf(ERROR_SENT_OUT_REMINDER, implode(', ', $cant_send_reminder)));
					}
					if (count($reminder_sent_lately) > 0) {
						$messageStack->add_session('inviter', sprintf(ERROR_WITHIN_LAPSE_PERIOD, implode(', ', $reminder_sent_lately)));
					}
					if (count($reminder_in_ignore_list) > 0) {
						$messageStack->add_session('inviter', sprintf(ERROR_EMAIL_UNABLE_DELIVER, implode(', ', $reminder_in_ignore_list)));
					}
					*/
					tep_redirect(tep_href_link(FILENAME_INVITER, 'action=success_invite', 'SSL'));
				}
				else {
					/*
					if (count($reminder_cant_send) > 0) {
						$messageStack->add('inviter', sprintf(ERROR_SENT_OUT_REMINDER, implode(', ', $cant_send_reminder)));
					}
					if (count($reminder_sent_lately) > 0) {
						$messageStack->add('inviter', sprintf(ERROR_WITHIN_LAPSE_PERIOD, implode(', ', $reminder_sent_lately)));
					}
					if (count($reminder_in_ignore_list) > 0) {
						$messageStack->add('inviter', sprintf(ERROR_EMAIL_UNABLE_DELIVER, implode(', ', $reminder_in_ignore_list)));
					}
					*/
				}
				//$done=true;
			} // fi
			break;
			
		default:
			// Reset variables
			$_REQUEST['vi_email']='';
			$_REQUEST['vi_password']='';
			$_REQUEST['vi_provider']='';
			//$_REQUEST['vi_message']='';
			
			$_REQUEST['mi_to']='';
			$_REQUEST['mi_message']='';
			break;	
	}
} // fi
else { // Reset variables
	$_REQUEST['vi_email']='';
	$_REQUEST['vi_password']='';
	$_REQUEST['vi_provider']='';
		
	$_REQUEST['mi_to']='';
	$_REQUEST['mi_message']='';
}
# End


function list_invitation($sortby='') {
	global $customer_id;
	
	switch ($sortby) {
		case 'joined':
			$contacts_select_sql = "SELECT im.inviter_imports_id, im.inviter_imports_service, im.inviter_imports_contact_email, 
			                               im.inviter_imports_contact_name, im.inviter_imports_contact_joined, 
			                               im.inviter_imports_insert_datetime, im.inviter_imports_insert_counter, 
			                               il.inviter_ignore_contact AS inviter_imports_opt_out 
			                        FROM ". TABLE_INVITER_IMPORTS ." AS im 
	                            LEFT JOIN ". TABLE_INVITER_IGNORE_LIST ." AS il 
	                              ON ( im.inviter_imports_contact_email=il.inviter_ignore_contact 
	                                   AND im.inviter_imports_service=il.inviter_ignore_service ) 
	                            WHERE im.customer_id='".(int) $customer_id."'  AND im.inviter_imports_contact_joined='1'";
			break;

		case 'notjoined':
			$contacts_select_sql = "SELECT im.inviter_imports_id, im.inviter_imports_service, im.inviter_imports_contact_email, im.inviter_imports_contact_name, im.inviter_imports_contact_joined, im.inviter_imports_insert_datetime, im.inviter_imports_insert_counter, 
			                               il.inviter_ignore_contact AS inviter_imports_opt_out 
			                       FROM ". TABLE_INVITER_IMPORTS ." AS im 
			                       LEFT JOIN ". TABLE_INVITER_IGNORE_LIST ." AS il 
			                         ON ( im.inviter_imports_contact_email=il.inviter_ignore_contact 
			                              AND im.inviter_imports_service=il.inviter_ignore_service ) 
			                       WHERE im.customer_id='".(int) $customer_id."'  AND im.inviter_imports_contact_joined='0'";
			break;

		case 'optout':
			$contacts_select_sql = "SELECT * FROM ". TABLE_INVITER_IMPORTS ." AS im 
			                       WHERE EXISTS ( SELECT * FROM inviter_ignore_list ign 
			                                          WHERE ign.inviter_ignore_contact=im.inviter_imports_contact_email AND 
			                                                ign.inviter_ignore_service = inviter_imports_service) 
			                       AND im.customer_id='".(int) $customer_id."'";
			break;
									
		default:
			$contacts_select_sql = "SELECT im.inviter_imports_id, im.inviter_imports_service, im.inviter_imports_contact_email, 
			                               im.inviter_imports_contact_name, im.inviter_imports_contact_joined, im.inviter_imports_insert_datetime, 
			                               im.inviter_imports_insert_counter, il.inviter_ignore_contact AS inviter_imports_opt_out 
			                        FROM ". TABLE_INVITER_IMPORTS ." AS im 
			                        LEFT JOIN ". TABLE_INVITER_IGNORE_LIST ." AS il 
			                          ON ( im.inviter_imports_contact_email=il.inviter_ignore_contact AND im.inviter_imports_service=il.inviter_ignore_service ) 
			                        WHERE im.customer_id='".(int) $customer_id."'";
			break;
	}

	return strtolower($contacts_select_sql);
}


$content = CONTENT_INVITER;

require(DIR_WS_TEMPLATES . TEMPLATENAME_MAIN_PAGE);

require(DIR_WS_INCLUDES . 'application_bottom.php');

?>