﻿<?php
require('includes/application_top.php');

require(DIR_WS_LANGUAGES . $language . '/' . FILENAME_INVITER_X);

$action = $_REQUEST['email_optout'];

switch($action) {
	case 'success_optout':
		$messageStack->add('inviter_x', SUCCESS_OPT_OUT, 'success');
		
		break;
	case 'optout':
	default:
		$random_code = tep_not_null($_REQUEST['code'])? tep_db_prepare_input($_REQUEST['code']): '';
		$recid = tep_not_null($_REQUEST['rec'])? tep_db_prepare_input($_REQUEST['rec']): '';
		
		if (tep_not_null($random_code) && tep_not_null($recid)) {
			$get_invitee_email_sql = "	SELECT inviter_imports_contact_email, inviter_imports_service 
										FROM ". TABLE_INVITER_IMPORTS ." 
										WHERE inviter_imports_id= '". (int)$recid ."' 
											AND inviter_imports_code = '". $random_code ."' ";
			$get_invitee_email_result_sql = tep_db_query($get_invitee_email_sql);
		
			if (tep_db_num_rows($get_invitee_email_result_sql) > 0) {
				$inv_email_row = tep_db_fetch_array($get_invitee_email_result_sql);
				$invitee_email = $inv_email_row['inviter_imports_contact_email'];
				$invitee_email_provider = $inv_email_row['inviter_imports_service'];
				
				if ($action == 'optout') {
					if (tep_inviter_ignore($recid, $random_code) === true) {	// Success opt out
						tep_redirect(tep_href_link(FILENAME_INVITER_X, 'email_optout=success_optout', 'SSL'));
					} else { // Check if already opt out
						$invitee_in_ignore_list_sql = "	SELECT inviter_ignore_contact 
														FROM ". TABLE_INVITER_IGNORE_LIST ." 
														WHERE inviter_ignore_contact= '". $invitee_email ."' 
															AND inviter_ignore_service = '". $invitee_email_provider ."' ";
						$invitee_in_ignore_list_result_sql = tep_db_query($invitee_in_ignore_list_sql);
						if (tep_db_num_rows($invitee_in_ignore_list_result_sql) > 0) {
							$messageStack->add('inviter_x', ERROR_ALREADY_OPTOUT);
						}
					}
				} else if (isset($_REQUEST['confirm_submit']) && $_REQUEST['confirm_submit']=='btn_confirm') {
					$messageStack->add('inviter_x', ERROR_EMPTY_OPTION);
				}
			} else {
				$messageStack->add('inviter_x', ERROR_OPT_OUT);
			}
		} else {
			$messageStack->add('inviter_x', ERROR_OPT_OUT);
		}
		
		break;
}

$content = CONTENT_INVITER_X;

require(DIR_WS_TEMPLATES . TEMPLATENAME_MAIN_PAGE);

require(DIR_WS_INCLUDES . 'application_bottom.php');

?>