<?
/*
  	$Id: ipay_ipn.php, v1.0 2005/11/29 18:08:05
  	Author : <PERSON> (<EMAIL>)
  	Title: iPay88.com Payment Module V1.0
	
	Revisions:
		Version 1.0 Initial Payment Module
	
  	Copyright (c) 2007 SKC Venture
	
  	Released under the GNU General Public License
*/
require_once('includes/application_top.php');

require_once(DIR_WS_MODULES . 'payment/iPay88/classes/iPay88_ipn_class.php');
include(DIR_WS_LANGUAGES . $language . '/' . FILENAME_CHECKOUT_PROCESS);

//Process payment
if (isset($_POST) && count($_POST)) {
	echo 'RECEIVEOK';
	/**********************************************************
		(1) Check this order is pay to us
		(2) This is Pending order
	**********************************************************/
	$payment = 'iPay88';
    
    // load selected payment module
    include_once(DIR_WS_CLASSES . 'payment.php');
    $payment_modules = new payment($payment);
     
    $payment_methods_id_select_sql = "	SELECT pm.payment_methods_id 
										FROM " . TABLE_PAYMENT_METHODS . " AS pm
										INNER JOIN " . TABLE_PAYMENT_METHODS . " AS pg 
											ON (pm.payment_methods_parent_id = pg.payment_methods_id AND pg.payment_methods_filename = '" . tep_db_input($payment . '.php') . "') 
										WHERE pm.payment_methods_code = '" . tep_db_input(trim($_POST['PaymentId'])) . "' 
										LIMIT 1";
	$payment_methods_id_result_sql = tep_db_query($payment_methods_id_select_sql);
	if ($payment_methods_id_row = tep_db_fetch_array($payment_methods_id_result_sql)) {
		$$payment = new $payment($payment_methods_id_row['payment_methods_id']);
		$$payment->get_merchant_account($_POST['Currency']);
	}
    
    $iPay88_ipn = new iPay88_ipn($_POST);
    
	//transaction_id
	include_once(DIR_WS_FUNCTIONS . 'custom_product_info.php');
    include_once(DIR_WS_CLASSES . 'order.php');
    include_once(DIR_WS_CLASSES . 'anti_fraud.php');
    include_once(DIR_WS_CLASSES . 'log.php');
    
	$log_object = new log_files('system');
	
	if (isset($iPay88_ipn->key['RefNo']) && (int)$iPay88_ipn->key['RefNo']>0 ) {
		$iPay88_payment_data_array = array(	'ipay88_merchant_code' => $iPay88_ipn->key['MerchantCode'],
											'ipay88_payment_method' => $iPay88_ipn->key['PaymentId'],
											'ipay88_payment_amount' => preg_replace('/[^\d.]/', '', preg_quote($iPay88_ipn->key['Amount'])),
											'ipay88_currency' => $iPay88_ipn->key['Currency'],
											'ipay88_remark' => $iPay88_ipn->key['Remark'],
											'ipay88_trans_id' => $iPay88_ipn->key['TransId'],
											'ipay88_bank_auth_code' => $iPay88_ipn->key['AuthCode'],
											'ipay88_status' => $iPay88_ipn->key['Status'],
											'ipay88_sha1_signature' => $iPay88_ipn->key['Signature']
											);
		$iPay88_payment_select_sql = "	SELECT ipay88_orders_id
										FROM " . TABLE_PAYMENT_IPAY88 . "
										WHERE ipay88_orders_id = '".(int)$iPay88_ipn->key['RefNo']."'";
		$iPay88_payment_result_sql = tep_db_query($iPay88_payment_select_sql);
		if (tep_db_num_rows($iPay88_payment_result_sql)) {
			tep_db_perform(TABLE_PAYMENT_IPAY88, $iPay88_payment_data_array, 'update', " ipay88_orders_id = '".(int)$iPay88_ipn->key['RefNo']."' ");
		} else {
			$iPay88_payment_data_array['ipay88_orders_id'] = $iPay88_ipn->key['RefNo'];
			tep_db_perform(TABLE_PAYMENT_IPAY88, $iPay88_payment_data_array);
		}
	}
	
	if ($iPay88_ipn->validate_receiver_account($$payment->ipay88_merchant_code)) {
		if ($iPay88_ipn->validate_transaction_data($$payment)) {	// To ensure the integrity of the data posted back to merchant's server
			$orders_id = $iPay88_ipn->get_order_id();
			
			if (tep_not_null($orders_id)) {
				$order = new order($orders_id);
				
				if ($order->info['orders_status_id'] == $$payment->order_status) {	// If the order still in initial status
					$iPay88_ipn->authenticate($order, $orders_id, $$payment);
				}
			}
		}
	}
}
?>