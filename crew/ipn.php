<?
/*
  	$Id: ipn.php,v 1.35 2015/10/23 10:42:56 sionghuat.chng Exp $

  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com

  	DevosC, Developing open source Code
  	http://www.devosc.com

  	Copyright (c) 2003 osCommerce
  	Copyright (c) 2004 DevosC.com

  	Released under the GNU General Public License
*/

require_once('includes/modules/payment/paypal/application_top.inc.php');
require_once(DIR_WS_MODULES . 'payment/paypal.php');
require_once(DIR_WS_MODULES . 'payment/paypal/classes/ipn.class.php');
require_once(DIR_WS_MODULES . 'payment/paypal/classes/debug.class.php');
require_once(DIR_WS_MODULES . 'payment/paypal/functions/paypal.fnc.php');

include_once(DIR_WS_FUNCTIONS . 'custom_product_info.php');
include_once(DIR_WS_CLASSES . 'anti_fraud.php');

include_once(DIR_FS_ADMIN . 'includes/classes/c2c_invoice.php');

if (isset($_POST['invoice']) && tep_not_null($_POST['invoice'])) {
	$orders_select_sql = "	SELECT payment_methods_id
							FROM " . TABLE_ORDERS . "
							WHERE orders_id = '".(int)$_POST['invoice']."'";
	$orders_result_sql = tep_db_query($orders_select_sql);
	if ($orders_row = tep_db_fetch_array($orders_result_sql)) {
		$paypal_obj = new paypal($orders_row['payment_methods_id']);
	} else {
		$paypal_obj = new paypal();
	}
} else {
	$paypal_obj = new paypal();
}

paypal_include_lng(DIR_WS_MODULES . 'payment/paypal/languages/', $language, 'ipn.lng.php');
$debug = new debug($paypal_obj->ipn_debug_email, $paypal_obj->ipn_debug);
$ipn = new ipn($_POST);

if ($_POST['txn_type'] == 'new_case') {
    $cb_email_content = 'Order ID: ' . $_POST['invoice'] . '<br>Case ID:' . $_POST['case_id'] . '<br>Case Type:' . $_POST['case_type'] . '<br>Reason Code:' . $_POST['reason_code'];

    @tep_mail('<EMAIL>', '<EMAIL>', '[OffGamers] PayPal Dispute/Chargeback Notification', $cb_email_content, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
}

unset($_POST);

//post back to PayPal system to validate	
if(!$ipn->authenticate($paypal_obj->domain, $paypal_obj->ipn_validate_url) && $paypal_obj->ipn_test_mode == 'Off') $ipn->dienice();

if (isset($ipn->key['mc_currency']) && tep_not_null($ipn->key['mc_currency'])) {
	$paypal_obj->get_merchant_account($ipn->key['mc_currency']);
}

//Check both the receiver_email and business ID fields match
if (!$ipn->validate_receiver_email($paypal_obj->paypal_id,$paypal_obj->business_id)) $ipn->dienice();

//Process payment
if ($ipn->txn_type() == 'web_accept' || $ipn->txn_type() == 'cart' && tep_not_null($orders_session->orders_id)) {
	// reload global osCommerce vars, see application_top for $orders_session
    $orders_id = $orders_session->orders_id;
    $sendto = $orders_session->sendto;
    $billto = $orders_session->billto;

    $is_sc_flag = false;

    //$payment = $orders_session->payment;
    //$shipping = unserialize($orders_session->shipping);

    include_once(DIR_WS_CLASSES . 'order.php');
    $order = new order($orders_id);
    $order->content_type = $orders_session->content_type;

    //Check that txn_id has not been previously processed
    if ($ipn->unique_txn_id()) {
    	$ipn->insert_ipn_txn();

      	// call AFT Module query.
      	/*$aft_module = new anti_fraud();
   		$aft_module->getAftModule()->execute_stored_query_call($orders_id, $order->customer['id']);*/

      	$is_sc_checkout_select_sql = "	SELECT op.custom_products_type_id
      									FROM " . TABLE_ORDERS . " AS o
      									INNER JOIN " . TABLE_ORDERS_PRODUCTS . " AS op
      										ON (o.orders_id = op.orders_id)
      									WHERE o.orders_id = '" . (int)$orders_id . "'
      										AND op.custom_products_type_id = '3'";
		$is_sc_checkout_result_sql = tep_db_query($is_sc_checkout_select_sql);
		if (tep_db_num_rows($is_sc_checkout_result_sql)) {
			$is_sc_flag = true;
		}

      	tep_db_query("UPDATE " . TABLE_ORDERS . " SET paypal_ipn_id = '" . (int)$ipn->id() . "' WHERE orders_id = '" . (int)$orders_id . "'");

      	$customer_paypal_info_sql = "	SELECT c.customers_gender, c.customers_firstname, c.customers_lastname, c.customers_email_address, ord.customers_id, ord.customers_name, ord.currency, pay.payer_email, ot.text
      									FROM " . TABLE_ORDERS . " AS ord
      									INNER JOIN " . TABLE_ORDERS_TOTAL . " AS ot
      										ON (ot.orders_id = ord.orders_id and ot.class = 'ot_total')
      									INNER JOIN " . TABLE_PAYPAL . " AS pay
      										ON (pay.invoice = ord.orders_id)
      									LEFT JOIN " . TABLE_CUSTOMERS . " As c
      										ON (c.customers_id = ord.customers_id)
      									WHERE ord.orders_id ='" . (int)$orders_id . "'";
		$customer_paypal_info_result_sql = tep_db_query($customer_paypal_info_sql);
		$customer_paypal_info_row = tep_db_fetch_array($customer_paypal_info_result_sql);

		$customer_paypal_verify_sql = "select serial_number, info_verified from " . TABLE_CUSTOMERS_INFO_VERIFICATION . " where customers_info_value = '" . $customer_paypal_info_row['payer_email'] . "' and customers_id = '" . (int)$customer_paypal_info_row['customers_id'] . "' and info_verification_type = 'email'";
		$customer_paypal_verify_result_sql = tep_db_query($customer_paypal_verify_sql);
		$customer_paypal_verify_row = tep_db_fetch_array($customer_paypal_verify_result_sql);

		$send_verification_mail = false;
		if (tep_db_num_rows($customer_paypal_verify_result_sql) > 0) {
			if ($customer_paypal_verify_row['info_verified'] == 0) {
				if (tep_not_null($customer_paypal_verify_row['serial_number'])) {
					$serial_number = $customer_paypal_verify_row['serial_number'];
				} else {
					$serial_number = tep_gen_random_serial($customer_paypal_info_row['payer_email'], TABLE_CUSTOMERS_INFO_VERIFICATION, 'serial_number', '', 12);
					tep_db_query("update " . TABLE_CUSTOMERS_INFO_VERIFICATION . " set serial_number = '" . $serial_number . "' where customers_info_value = '" . tep_db_input($customer_paypal_info_row['payer_email']) . "' and customers_id ='" . (int)$customer_paypal_info_row['customers_id'] . "'");
				}
				$send_verification_mail = true;
			}
		} else if (tep_db_num_rows($customer_paypal_verify_result_sql) < 1) {
			$serial_number = substr(tep_gen_random_serial($customer_paypal_info_row['payer_email'], TABLE_CUSTOMERS, "serial_number"), 0, 12);

			$sql_data_array = array('customers_id ' => (int)$customer_paypal_info_row['customers_id'],
	                              	'customers_info_value' => tep_db_input($customer_paypal_info_row['payer_email']),
	                              	'serial_number' => $serial_number,
	                              	'info_verified' => 0,
	                              	'info_verification_type' => 'email');

			tep_db_perform(TABLE_CUSTOMERS_INFO_VERIFICATION, $sql_data_array);
			$send_verification_mail = true;
		}

		if ($is_sc_flag) {
		  	tep_db_query("DELETE FROM " . TABLE_CUSTOMERS_SC_CART . " WHERE customers_id = '" . (int)$order->customer['id'] . "'");
	    } else {
	      	$basket_id_select_sql = "SELECT customers_basket_id from " . TABLE_CUSTOMERS_BASKET . " WHERE customers_id = '" . (int)$order->customer['id'] . "'";
	      	$basket_id_result_sql = tep_db_query($basket_id_select_sql);
			while ($basket_id_row = tep_db_fetch_array($basket_id_result_sql)) {
				tep_db_query("DELETE FROM " . TABLE_CUSTOMERS_BASKET_CUSTOM . " WHERE customers_basket_id = '" . (int)$basket_id_row['customers_basket_id'] . "'");
			}

	      	tep_db_query("DELETE FROM " . TABLE_CUSTOMERS_BASKET . " WHERE customers_id = '" . (int)$order->customer['id'] . "'");
	      	tep_db_query("DELETE FROM " . TABLE_CUSTOMERS_BASKET_ATTRIBUTES . " WHERE customers_id = '" . (int)$order->customer['id'] . "'");
	      	tep_db_query("DELETE FROM " . TABLE_CUSTOMERS_BASKET_BUNDLE . " WHERE customers_id = '" . (int)$order->customer['id'] . "'");
      	}

      	if ($ipn->key['payment_status'] == 'Completed' && $ipn->valid_payment() ) {
			upgradePromoCustGroup($order->customer['id']);
			//move customer to bronze if they belonged to PromoCustomerGroup
        	include(DIR_WS_MODULES . 'payment/paypal/catalog/checkout_update.inc.php');
      	} else if ($ipn->key['payment_status'] == 'Pending' && $ipn->key['pending_reason'] == 'paymentreview') {
    		$customer_profile_select_sql = "SELECT c.customers_gender, c.customers_firstname, c.customers_lastname, ord.customers_name, pay.payer_email
	      									FROM " . TABLE_ORDERS . " AS ord
	      									INNER JOIN " . TABLE_PAYPAL . " AS pay
	      										ON (pay.invoice = ord.orders_id)
	      									LEFT JOIN " . TABLE_CUSTOMERS . " AS c
	      										ON (c.customers_id = ord.customers_id)
	      									WHERE ord.orders_id = '" . (int)$orders_id . "'";
			$customer_profile_result_sql = tep_db_query($customer_profile_select_sql);
			if ($customer_profile_row = tep_db_fetch_array($customer_profile_result_sql)) {
				$email_firstname = $customer_profile_row['customers_firstname'];
				$email_lastname = $customer_profile_row['customers_lastname'];
			} else {
				$email_firstname = $customer_paypal_info_row['customers_name'];
				$email_lastname = '';
			}

			$paypal_email_greeting = tep_get_email_greeting($email_firstname, $email_lastname, $customer_profile_result_sql['customers_gender']);
			$paypal_email_text = $paypal_email_greeting . "\n\n" . EMAIL_PAYPAL_PAYMENT_REVIEW . "\n\n\n" . EMAIL_FOOTER;

			$orders_status_history_data_array = array(	'orders_id' => $orders_id,
														'orders_status_id' => 0,
														'date_added' => 'now()',
														'customer_notified' => 1,
														'comments' => 'Payment review notification email sent',
														'changed_by' => 'system'
													);
			tep_db_perform(TABLE_ORDERS_STATUS_HISTORY, $orders_status_history_data_array);

			@tep_mail($email_firstname . ' ' . $email_lastname, $customer_profile_row['payer_email'], implode(' ', array(EMAIL_SUBJECT_PREFIX, EMAIL_PAYPAL_PAYMENT_REVIEW_SUBJECT)), $paypal_email_text, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
    	}

      	if ($send_verification_mail == true) {
      		$activate_address = tep_href_link(FILENAME_ACCOUNT_ACTIVATE, 'serial=' . $serial_number . '&email=' . urlencode($customer_paypal_info_row['payer_email']) . '&action=verify_email');
			$link = '<a href="' . $activate_address . '" target="_blank">' . $activate_address . '</a><br>';

			$customer_profile_select_sql = "SELECT customers_gender, customers_firstname, customers_lastname FROM " . TABLE_CUSTOMERS . " WHERE customers_id = '" . (int)$order->customer['id'] . "'";
			$customer_profile_result_sql = tep_db_query($customer_profile_select_sql);
			if ($customer_profile_row = tep_db_fetch_array($customer_profile_result_sql)) {
				$email_firstname = $customer_profile_row['customers_firstname'];
				$email_lastname = $customer_profile_row['customers_lastname'];
			} else {
				$email_firstname = $customer_paypal_info_row['customers_name'];
				$email_lastname = '';
			}

			$email_greeting = tep_get_email_greeting($email_firstname, $email_lastname, $customer_profile_result_sql['customers_gender']);

			$email_text = 	$email_greeting .
							TEXT_PAYPAL_PAYMENT_MAKE_EMAIL_CONTENT1 .
							$customer_paypal_info_row['text'] .
							TEXT_PAYPAL_PAYMENT_MAKE_EMAIL_CONTENT2 .
							'  ' . $customer_paypal_info_row['payer_email'] .
							TEXT_PAYPAL_EMAIL_VERIFY_INSTRUCTION_CONTENT .
							$link .
							TEXT_PAYPAL_EMAIL_VERIFY_ENDING_CONTENT . "\n\n\n" . EMAIL_CONTACT . EMAIL_FOOTER;

			@tep_mail($customer_paypal_info_row['customers_name'], $customer_paypal_info_row['payer_email'], implode(' ', array(EMAIL_SUBJECT_PREFIX, TEXT_PAYPAL_VERIFICATION_TITLE)), $email_text, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);

			if ($customer_paypal_info_row['payer_email'] != $customer_paypal_info_row['customers_email_address']) {
				$profile_email_text = 	$email_greeting .
										TEXT_PAYPAL_PAYMENT_MAKE_EMAIL_CONTENT1 .
										$customer_paypal_verify_row['currency'] . $customer_paypal_verify_row['text'] .
										TEXT_PAYPAL_PAYMENT_MAKE_EMAIL_CONTENT2 .
										'  ' . $customer_paypal_info_row['payer_email'] .
										TEXT_PAYPAL_EMAIL_VERIFICATION_NOTICE_HEADING_CONTENT .
										EMAIL_SUBJECT_PREFIX . TEXT_PAYPAL_VERIFICATION_TITLE .
										TEXT_PAYPAL_EMAIL_VERIFICATION_NOTICE_ENDING_CONTENT .
										"\n\n\n" . EMAIL_CONTACT . EMAIL_FOOTER;

				$name = $customer_paypal_verify_row['customers_firstname'] . ' ' . $customer_paypal_verify_row['customers_lastname'];
				@tep_mail($name, $customer_paypal_info_row['customers_email_address'], implode(' ', array(EMAIL_SUBJECT_PREFIX, TEXT_NOTICE_OF_PAYPAL_VERIFICATION_SEND_TITLE)), $profile_email_text, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
			}

			$sql_data_array = array('orders_id' => (int)$orders_id,
									'orders_status_id' => 0,
	                              	'date_added' => 'now()',
	                              	'customer_notified' => 1,
	                              	'comments' => REMARK_PAYPAL_VERIFICATION_EMAIL_SENT . ($customer_paypal_info_row['payer_email'] != $customer_paypal_info_row['customers_email_address'] ? ' ' . REMARK_PAYPAL_VERIFICATION_EMAIL_NOTICE_SENT : '')
	                              	);

			tep_db_perform(TABLE_ORDERS_STATUS_HISTORY, $sql_data_array);
		}
	} else { // not a unique transaction
		$ipn_select_sql = "	SELECT psh.invoice, psh.payment_status
							FROM " . TABLE_PAYPAL_PAYMENT_STATUS_HISTORY . " psh
							LEFT JOIN " . TABLE_PAYPAL . " p
								ON (p.paypal_ipn_id = psh.paypal_ipn_id)
							WHERE p.txn_id = '" . tep_db_input($ipn->key['txn_id']) . "'
							ORDER BY psh.date_added
							LIMIT 1";
      	$ipn_query = tep_db_query($ipn_select_sql);
      	//Assumes there is only one previous IPN transaction
      	$ipn_query_result = tep_db_fetch_array($ipn_query);
      	if ($ipn_query_result['payment_status'] == 'Pending') {
        	$ipn->update_status($ipn_query_result['invoice'], $ipn->key['payment_status'], $ipn->key['pending_reason']);
        	if ($ipn->key['payment_status'] == 'Completed' && $ipn->valid_payment()) {
				//move customer to bronze if they belonged to PromoCustomerGroup
				upgradePromoCustGroup($order->customer['id']);
          		include(DIR_WS_MODULES . 'payment/paypal/catalog/checkout_update.inc.php');
        	}
      	}
	}
} else if ($ipn->txn_type() == 'send_money') {
	if ($debug->enabled) $debug->add(PAYMENT_SEND_MONEY_DESCRIPTION, sprintf(PAYMENT_SEND_MONEY_DESCRIPTION_MSG, number_format($ipn->key['mc_gross'],2),$ipn->key['mc_currency']));
} else if ($ipn->txn_type() == 'masspay') {
	if ($debug->enabled) $debug->add(PAYMENT_SEND_MASS_PAYMENT_NOTIFICATION);
	$num_of_order = 0;

	foreach ($ipn->key as $ipn_key_loop => $ipn_key_data) {
		if (preg_match("/status_[0-9]+$/i", $ipn_key_loop)) $num_of_order++;
	}

	for ($count_order=1;$count_order<=$num_of_order;$count_order++) {
		$returned_masspay_txn_id = (isset($ipn->key['masspay_txn_id_'.$count_order])?$ipn->key['masspay_txn_id_'.$count_order]:'');
		$returned_mc_currency = (isset($ipn->key['mc_currency_'.$count_order])?$ipn->key['mc_currency_'.$count_order]:'');
		$returned_mc_gross = (isset($ipn->key['mc_gross_'.$count_order])?$ipn->key['mc_gross_'.$count_order]:'');
		$returned_mc_fee = (isset($ipn->key['mc_fee_'.$count_order])?$ipn->key['mc_fee_'.$count_order]:'');
		$returned_mc_handling = (isset($ipn->key['mc_handling'.$count_order])?$ipn->key['mc_handling'.$count_order]:'');
		$returned_receiver_email = (isset($ipn->key['receiver_email_'.$count_order])?$ipn->key['receiver_email_'.$count_order]:'');
		$returned_status = (isset($ipn->key['status_'.$count_order])?$ipn->key['status_'.$count_order]:'');
		$returned_unique_id = (isset($ipn->key['unique_id_'.$count_order])?$ipn->key['unique_id_'.$count_order]:'');

		$comments_log_str = "<u>MassPay IPN</u>\n";
		$comments_log_str .= "MassPay Transaction ID: ".$returned_masspay_txn_id."\n";
		$comments_log_str .= "Currency: ".$returned_mc_currency."\n";
		$comments_log_str .= "Gross: ".$returned_mc_gross."\n";
		$comments_log_str .= "Fee: ".$returned_mc_fee."\n";
		$comments_log_str .= "Handling: ".$returned_mc_handling."\n";
		$comments_log_str .= "Receiver Email: ".$returned_receiver_email."\n";
		$comments_log_str .= "Status: ".$returned_status."\n\n";

		$store_payments_select_sql = "	SELECT store_payments_id, store_payments_request_amount, store_payments_methods_id,
											store_payments_fees, store_payments_request_currency,
											store_payments_status, user_firstname, user_lastname, user_email_address
										FROM " . TABLE_STORE_PAYMENTS . "
										WHERE store_payments_id = '".tep_db_input($returned_unique_id)."'";
		$store_payments_result_sql = tep_db_query($store_payments_select_sql);
		if ($store_payments_row = tep_db_fetch_array($store_payments_result_sql)) {
			$return_paypal_gross_amt = number_format($returned_mc_gross, $currencies->currencies[$returned_mc_currency]['decimal_places'], $currencies->currencies[$returned_mc_currency]['decimal_point'], $currencies->currencies[$returned_mc_currency]['thousands_point']);
			$paypal_gross_amt = number_format($store_payments_row['store_payments_request_amount'], $currencies->currencies[$store_payments_row['store_payments_request_currency']]['decimal_places'], $currencies->currencies[$store_payments_row['store_payments_request_currency']]['decimal_point'], $currencies->currencies[$store_payments_row['store_payments_request_currency']]['thousands_point']);

			if ( $currencies->currencies[$returned_mc_currency]['symbol_left'].$return_paypal_gross_amt.$currencies->currencies[$returned_mc_currency]['symbol_right'] != $currencies->currencies[$paypal_gross_amt]['symbol_left'].$paypal_gross_amt.$currencies->currencies[$paypal_gross_amt]['symbol_right']) {
				$store_payments_array = array();
		    	$store_payments_details_select_sql = "	SELECT spd.payment_methods_fields_value, pmf.payment_methods_fields_system_type
														FROM " . TABLE_PAYMENT_METHODS_FIELDS . " as pmf
														INNER JOIN " . TABLE_STORE_PAYMENTS_DETAILS . " as spd
															ON pmf.payment_methods_fields_id = spd.payment_methods_fields_id
														WHERE store_payments_id = '".$store_payments_row['store_payments_id']."'";
		    	$store_payments_details_result_sql = tep_db_query($store_payments_details_select_sql);
		    	while ($store_payments_details_row = tep_db_fetch_array($store_payments_details_result_sql)) {
		    		$store_payments_array[$store_payments_details_row['payment_methods_fields_system_type']] = $store_payments_details_row['payment_methods_fields_value'];
		    	}

		    	if ($store_payments_array['MODULE_PAYPAL_SEND_EMAIL'] == $returned_receiver_email) {
			    	if (strtolower($returned_status) == 'completed') {
						if ($store_payments_row['store_payments_status']!=3) {
			    		$comments_log_str .= "<u>Result</u>\n";
	    				$comments_log_str .= "Send Mass Payment Completed\nStatus: Completed.\nReference: " . $returned_masspay_txn_id;

			    		$store_payments_data_sql = array( 	'store_payments_reference' => tep_db_prepare_input("Reference: " . $returned_masspay_txn_id),
			    											'store_payments_last_modified' => 'now()',
			    											'store_payments_paid_currency' => tep_db_prepare_input($returned_mc_currency),
			    											'store_payments_paid_amount' => tep_db_prepare_input($returned_mc_gross),
			    											'store_payments_status' => 3);
			    		tep_db_perform(TABLE_STORE_PAYMENTS, $store_payments_data_sql, 'update', " store_payments_id = '" . $store_payments_row['store_payments_id'] . "' ");

			    		$store_payments_history_data_sql = array( 	'store_payments_id' => $store_payments_row['store_payments_id'],
			    													'store_payments_status' => '3',
																	'date_added' => 'now()',
																	'payee_notified' => 0,
																	'comments' => tep_db_prepare_input($comments_log_str),
																	'changed_by' => tep_db_prepare_input('system'),
																	'changed_by_role' => tep_db_prepare_input('system'));
			    		tep_db_perform(TABLE_STORE_PAYMENTS_HISTORY, $store_payments_history_data_sql);

			    		$payment_received_by_str = '';
			    		$estimated_payment_receive_time_select_sql = "	SELECT payment_methods_estimated_receive_period
	          															FROM " . TABLE_PAYMENT_METHODS . "
	          															WHERE payment_methods_id = '".tep_db_input($store_payments_row['store_payments_methods_id'])."'";
	          			$estimated_payment_receive_time_result_sql = tep_db_query($estimated_payment_receive_time_select_sql);
						if ($estimated_payment_receive_time_row = tep_db_fetch_array($estimated_payment_receive_time_result_sql)) {
							$payment_received_by_timestamp = mktime(date("H"), (int)date("i")+(int)$estimated_payment_receive_time_row['payment_methods_estimated_receive_period'], (int)date("s"), date("m"), date("d"), date("Y"));
							$payment_notice_due_timestamp = mktime(date("H")+24, (int)date("i")+(int)$estimated_payment_receive_time_row['payment_methods_estimated_receive_period'], (int)date("s"), date("m"), date("d"), date("Y"));	// Hard code 24 for now..Need global configuration

							$payment_received_by_date = date("Y-m-d H:i:s T", $payment_received_by_timestamp);
							$payment_notice_due_date = date("Y-m-d H:i:s T", $payment_notice_due_timestamp);
                                                        
                                                        $is_g2g = c2c_invoice::check_g2g_withdraw($store_payments_row['store_payments_id']);
                                                        if($is_g2g == false){
                                                            $payment_received_by_str = "\n\n" . sprintf(TEXT_PAYMENT_ESTIMATE_PAYMENT_RECEIVED_G2G, $payment_received_by_date, $payment_notice_due_date);                                        
                                                        }
                                                        else{
                                                            $payment_received_by_str = "\n\n" . sprintf(TEXT_PAYMENT_ESTIMATE_PAYMENT_RECEIVED, $payment_received_by_date, $payment_notice_due_date);                                        
                                                        }
							//$payment_received_by_str = sprintf(TEXT_PAYMENT_ESTIMATE_PAYMENT_RECEIVED, $payment_received_by_date, $payment_notice_due_date);
						}

	          			// Insert payment history
	          			$payment_history_sql_data_array = array('store_payments_id' => $store_payments_row['store_payments_id'],
			    		           								'store_payments_status' => '3',
				    	 	          							'date_added' => 'now()',
				    	 	          							'payee_notified' => '1',
				    	 	          							'comments' => $payment_received_by_str,
				    	 	          							'changed_by_role' => 'system'
			        	    	       							);
						tep_db_perform(TABLE_STORE_PAYMENTS_HISTORY, $payment_history_sql_data_array);
                                                    
                                                if($is_g2g == true){
                                                    $withdraw_datetime = c2c_invoice::get_store_payment_datetime($store_payments_row['store_payments_id']); 
                                                }
                                            
						// Email to beneficiary
						@tep_mail($store_payments_row['user_firstname'].' '.$store_payments_row['user_lastname'], $store_payments_row['user_email_address'], sprintf(EMAIL_PAYMENT_PAYMENT_UPDATE_SUBJECT, $store_payments_row['store_payments_id']), $store_payments_row['user_firstname'].' '.$store_payments_row['user_lastname'] . "\n\n" . $payment_received_by_str . "\n\n\n" . EMAIL_CONTACT . EMAIL_FOOTER, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
                            
                                                if(isset($withdraw_datetime) && ($withdraw_datetime != false)){
                                                    c2c_invoice::create_c2c_invoice_queue($store_payments_row['store_payments_id'], 'withdrawal', $withdraw_datetime);
                                                }
                                        }
			    	} else if (strtolower($returned_status) == 'processed') { // status processed
			    		$comments_log_str .= "<u>Result</u>\n";
	    				$comments_log_str .= "Send Mass Payment Processed\nStatus: Processed.\nReference: " . $masspay_txn_id;

			    		$store_payments_data_sql = array( 	'store_payments_reference' => tep_db_prepare_input($masspay_txn_id),
			    											'store_payments_last_modified' => 'now()',
			    											'store_payments_status' => 2);
			    		tep_db_perform(TABLE_STORE_PAYMENTS, $store_payments_data_sql, 'update', " store_payments_id = '" . $store_payments_row['store_payments_id'] . "' ");

			    		$store_payments_history_data_sql = array( 	'store_payments_id' => $store_payments_row['store_payments_id'],
			    													'store_payments_status' => '2',
																	'date_added' => 'now()',
																	'payee_notified' => 0,
																	'comments' => tep_db_prepare_input($comments_log_str),
																	'changed_by' => tep_db_prepare_input('system'),
																	'changed_by_role' => tep_db_prepare_input('system'));
			    		tep_db_perform(TABLE_STORE_PAYMENTS_HISTORY, $store_payments_history_data_sql);
			    	} else if (strtolower($returned_status) == 'denied' || strtolower($returned_status) == 'unclaimed') { // status denied
			    		$comments_log_str .= "<u>Result</u>\n";
	    				$comments_log_str .= "Send Mass Payment rejected\nStatus: ".strtolower($returned_status).".\nReference: " . $masspay_txn_id;

			    		$store_payments_data_sql = array( 	'store_payments_reference' => tep_db_prepare_input($masspay_txn_id),
			    											'store_payments_last_modified' => 'now()',
			    											'store_payments_status' => 2);
			    		tep_db_perform(TABLE_STORE_PAYMENTS, $store_payments_data_sql, 'update', " store_payments_id = '" . $store_payments_row['store_payments_id'] . "' ");

			    		$store_payments_history_data_sql = array( 	'store_payments_id' => $store_payments_row['store_payments_id'],
			    													'store_payments_status' => '2',
																	'date_added' => 'now()',
																	'payee_notified' => 0,
																	'comments' => tep_db_prepare_input($comments_log_str),
																	'changed_by' => tep_db_prepare_input('system'),
																	'changed_by_role' => tep_db_prepare_input('system'));
			    		tep_db_perform(TABLE_STORE_PAYMENTS_HISTORY, $store_payments_history_data_sql);
			    	} else {
			    		$comments_log_str .= "<u>Result</u>\n";
	    				$comments_log_str .= "Send Mass Payment On Hold\nStatus: Unknown.\nReference: " . $masspay_txn_id;

			    		$store_payments_history_data_sql = array( 	'store_payments_id' => $store_payments_row['store_payments_id'],
			    													'store_payments_status' => 0,
																	'date_added' => 'now()',
																	'payee_notified' => 0,
																	'comments' => tep_db_prepare_input($comments_log_str),
																	'changed_by' => tep_db_prepare_input('system'),
																	'changed_by_role' => tep_db_prepare_input('system'));
			    		tep_db_perform(TABLE_STORE_PAYMENTS_HISTORY, $store_payments_history_data_sql);
			    	}
		    	} else { // receiver email not match
					$comments_log_str .= "<u>Result</u>\n";
		    		$comments_log_str .= "Receiver Email Not Match, " .$store_payments_array['MODULE_PAYPAL_SEND_EMAIL'] .'!='. $returned_receiver_email."\n\n";

		    		$store_payments_history_data_sql = array( 	'store_payments_id' => $store_payments_row['store_payments_id'],
		    													'store_payments_status' => 0,
																'date_added' => 'now()',
																'payee_notified' => 0,
																'comments' => tep_db_prepare_input($comments_log_str),
																'changed_by' => tep_db_prepare_input('system'),
																'changed_by_role' => tep_db_prepare_input('system'));
		    		tep_db_perform(TABLE_STORE_PAYMENTS_HISTORY, $store_payments_history_data_sql);
		    	}
		    } else { // amount or currency not match
	    		$comments_log_str .= "<u>Result</u>\n";
	    		$comments_log_str .= "Amount not matched, " . $currencies->currencies[$returned_mc_currency]['symbol_left'].$return_paypal_gross_amt.$currencies->currencies[$returned_mc_currency]['symbol_right'] .'!='. $currencies->currencies[$paypal_gross_amt]['symbol_left'].$paypal_gross_amt.$currencies->currencies[$paypal_gross_amt]['symbol_right'] ."\n\n";

	    		$store_payments_history_data_sql = array( 	'store_payments_id' => $store_payments_row['store_payments_id'],
	    													'store_payments_status' => 0,
															'date_added' => 'now()',
															'payee_notified' => 0,
															'comments' => tep_db_prepare_input($comments_log_str),
															'changed_by' => tep_db_prepare_input('system'),
															'changed_by_role' => tep_db_prepare_input('system'));
	    		tep_db_perform(TABLE_STORE_PAYMENTS_HISTORY, $store_payments_history_data_sql);
		    }
		}
	}
} else if ($ipn->txn_type() == 'new_case') {
    $cb_email_content = 'Order ID: ' . $ipn->key['invoice'] . '<br>Case ID:' . $ipn->key['case_id'] . '<br>Case Type:' . $ipn->key['case_type'] . '<br>Reason Code:' . $ipn->key['reason_code'];

    @tep_mail('<EMAIL>', '<EMAIL>', '[OffGamers] PayPal Dispute/Chargeback Notification', $cb_email_content, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
} else if ($debug->enabled && $paypal_obj->ipn_test_mode == 'On') {
	$debug->raise_error(TEST_INCOMPLETE,sprintf(TEST_INCOMPLETE_MSG),true);
}

if ($debug->enabled) $debug->send_email();
if ($paypal_obj->ipn_test_mode == 'On') {
	include(DIR_WS_MODULES . 'payment/paypal/admin/results.inc.php');
}
require(DIR_WS_MODULES . 'payment/paypal/application_bottom.inc.php');

function upgradePromoCustGroup($customerId) {
	$customer_group_select_sql = "	SELECT customers_groups_id
									FROM " . TABLE_CUSTOMERS . "
									WHERE customers_id = '".tep_db_input($customerId)."'
										AND customers_groups_id = 19";
	$customer_group_result_sql = tep_db_query($customer_group_select_sql);
	if (tep_db_num_rows($customer_group_result_sql)) {
		$customers_groups_id_update_sql = "	UPDATE " . TABLE_CUSTOMERS . " 
											SET customers_groups_id = '12' 
											WHERE customers_id = '" . tep_db_input($customerId) . "'";
		tep_db_query($customers_groups_id_update_sql);
		
		$sql_data_array = array('customers_id' => $customerId,
								'date_remarks_added' => 'now()',
								'remarks' => 'Upgraded to BRONZE by system',
								'remarks_added_by' => 'system');
		tep_db_perform(TABLE_CUSTOMERS_REMARKS_HISTORY, $sql_data_array);
	}
}
?>