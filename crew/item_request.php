<?php
  require('includes/application_top.php');

  require(DIR_WS_LANGUAGES . $language . '/' . FILENAME_ITEM_REQUEST);

  $error = false;
  if (isset($HTTP_GET_VARS['action']) && ($HTTP_GET_VARS['action'] == 'send')) {
    $name = tep_db_prepare_input($HTTP_POST_VARS['name']);
    $email_address = tep_db_prepare_input($HTTP_POST_VARS['email']);
    $item = tep_db_prepare_input($HTTP_POST_VARS['item']);
    $qty = tep_db_prepare_input($HTTP_POST_VARS['qty']);
    $price = tep_db_prepare_input($HTTP_POST_VARS['price']);
    $cat_id = tep_db_prepare_input($HTTP_POST_VARS['categories_id']);
    $ladder = tep_db_prepare_input($HTTP_POST_VARS['ladder']);
    $hardcore = tep_db_prepare_input($HTTP_POST_VARS['hardcore']);
    $comments = tep_db_prepare_input($HTTP_POST_VARS['comments']);
    
    if($ladder){
    	$ladder = "Yes";	
    }else{
    	$ladder = "No";	
    }
    
    if($hardcore){
    	$hardcore = "Yes";	
    }else{
    	$hardcore = "No";	
    }
    
    if (!$name) {
    	$error = true;
        $messageStack->add('contact', 'Please enter your full name!');
    }
    
    //STORE_OWNER_EMAIL_ADDRESS
    if (tep_validate_email($email_address)) {
      //tep_mail($to_name, $to_email_address, $email_subject, $email_text, $from_email_name, $from_email_address)
      $email_text = "Item Request\n\n".$name." has request <b>".$item."</b> for quantity <b>".$qty."</b>\nPrice willing to pay (USD): ". $price ."\nEmail: ".$email_address."\nCategaries: ".$cat_id."\nLadder: ".$ladder."\nHardcore: ".$hardcore."\nComments:\n".$comments;
      tep_mail(STORE_OWNER, "<EMAIL>", EMAIL_SUBJECT, $email_text, $name, $email_address);

      tep_redirect(tep_href_link(FILENAME_ITEM_REQUEST, 'action=success'));
    } else {
      $error = true;

      $messageStack->add('contact', ENTRY_EMAIL_ADDRESS_CHECK_ERROR);
    }
    
      if (!$cat_id) {
    	$error = true;
        $messageStack->add('contact', 'Please Select Reamls!');
	  }
	  if (!$item) {
	    	$error = true;
	        $messageStack->add('contact', 'Item Name can not be blank!');
	  }
	
	  if (!$qty) {
	    	$error = true;
	        $messageStack->add('contact', 'Please enter your item quantity!');
	  }
  
  }
  

   
  $breadcrumb->add(NAVBAR_TITLE, tep_href_link(FILENAME_ITEM_REQUEST));

  $content = CONTENT_ITEM_REQUEST;

  require(DIR_WS_TEMPLATES . TEMPLATENAME_MAIN_PAGE);

  require(DIR_WS_INCLUDES . 'application_bottom.php');
?>
