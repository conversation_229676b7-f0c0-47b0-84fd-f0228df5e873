<div id='slide_holder_inner'>
	<div id='row_of_slides'>
<?
	$counter = 1;
	$images_data_array = array();
	$images_data_array['1'] = array(	array(	"background_image"=>"http://image.offgamers.com/banners/1/mp_jcarousel_2011041801.jpg",
												"thumnail_image"=>"http://image.offgamers.com/banners/1/mp_jcarousel_small_2011041801.jpg",
												"caption"=>"",
												"link"=>"http://space.offgamers.com/en/blogs/official-blogs/367-july-2010/3755-offgamers-express-checkout-buying-has-never-been-easier"
										),
										array(	"background_image"=>"http://image.offgamers.com/banners/1/mp_jcarousel_2011050302.jpg",
												"thumnail_image"=>"http://image.offgamers.com/banners/1/mp_jcarousel_small_2011050302.jpg",
												"caption"=>"",
												"link"=>"http://www.offgamers.com/purchase-with-onecard-and-receive-a-multitude-of-rewards-ln-2617.ogm?news_type=8_10_7_9_2&tag=0&released_date=1&sort_by=1"
										),
										
										array(	"background_image"=>"http://image.offgamers.com/banners/1/mp_jcarousel_2011051003.jpg",
												"thumnail_image"=>"http://image.offgamers.com/banners/1/mp_jcarousel_small_2011051003.jpg",
												"caption"=>"",
												"link"=>"http://www.offgamers.com/forsaken-world/forsaken-world-my-sg/forsaken-world-cubicard-c-5916-7479-7480.ogm"
										),
										array(	"background_image"=>"http://image.offgamers.com/banners/1/mp_jcarousel_2011050304.jpg",
												"thumnail_image"=>"http://image.offgamers.com/banners/1/mp_jcarousel_small_2011050304.jpg",
												"caption"=>"",
												"link"=>"http://www.offgamers.com/dragonica/dragonica-sea/dragonica-8cash-c-4269-4270-4271.ogm"
										),
										array(	"background_image"=>"http://image.offgamers.com/banners/1/mp_jcarousel_2011040105.jpg",
												"thumnail_image"=>"http://image.offgamers.com/banners/1/mp_jcarousel_small_2011040105.jpg",
												"caption"=>"",
												"link"=>"http://www.offgamers.com/ran-online-ph-exclusive-e-points-promotion-ln-1954.ogm?news_type=8_10_7_9_2&tag=0&released_date=1&sort_by=1"
										),
										array(	"background_image"=>"http://image.offgamers.com/banners/1/mp_jcarousel_2011051006.jpg",
												"thumnail_image"=>"http://image.offgamers.com/banners/1/mp_jcarousel_small_2011051006.jpg",
												"caption"=>"",
												"link"=>"http://www.offgamers.com/offgamers-direct-top-up-feature-now-available-for-all-cubizone-ph-games-ln-3178.ogm?news_type=7&tag=0&released_date=1&sort_by=1"
										)
									);
	$images_data_array['2'] = array(	array(	"background_image"=>"http://image.offgamers.com/img/main_page/2/mp_jcarousel_2010120301.jpg",
												"thumnail_image"=>"http://image.offgamers.com/img/main_page/2/mp_jcarousel_small_2010120301.jpg",
												"caption"=>"",
												"link" => "http://www.offgamers.com/facebook-connect-cn-i-821.ogm?baseURL=/inviter.php"
										),
										array(	"background_image"=>"http://image.offgamers.com/banners/2/mp_jcarousel_2011050402.jpg",
												"thumnail_image"=>"http://image.offgamers.com/banners/2/mp_jcarousel_small_2011050402.jpg",
												"caption"=>"",
												"link" => "http://www.offgamers.com/zh-CN/saga-of-hero/saga-of-hero-my-sg/saga-of-hero-upo-card-c-8002-8003-8004.ogm"
										),
										array(	"background_image"=>"http://image.offgamers.com/banners/2/mp_jcarousel_2011051003.jpg",
												"thumnail_image"=>"http://image.offgamers.com/banners/2/mp_jcarousel_small_2011051003.jpg",
												"caption"=>"",
												"link" => "http://www.offgamers.com/tx-online/tx-online-my/tx-online-ballun-points-c-6002-6003-6004.ogm"
										),
										array(	"background_image"=>"http://image.offgamers.com/banners/2/mp_jcarousel_2011050304.jpg",
												"thumnail_image"=>"http://image.offgamers.com/banners/2/mp_jcarousel_small_2011050304.jpg",
												"caption"=>"",
												"link" => "http://www.offgamers.com/zh-CN/dragonica/dragonica-sea/dragonica-8cash-c-4269-4270-4271.ogm"
										),
										array(	"background_image"=>"http://image.offgamers.com/banners/2/mp_jcarousel_2011041305.jpg",
												"thumnail_image"=>"http://image.offgamers.com/banners/2/mp_jcarousel_small_2011041305.jpg",
												"caption"=>"",
												"link" => "http://www.offgamers.com/rms-direct-top-up-feature-ln-1895.ogm?news_type=8_10_7_9_2&tag=0&released_date=1&sort_by=1"
										),
										array(	"background_image"=>"http://image.offgamers.com/banners/2/mp_jcarousel_2011050406.jpg",
												"thumnail_image"=>"http://image.offgamers.com/banners/2/mp_jcarousel_small_2011050406.jpg",
												"caption"=>"",
												"link" => "http://www.offgamers.com/zh-CN/fun-city-onweb/fun-city-onweb-my/fun-city-onweb-upo-card-c-4934-4935-4936.ogm"
										)
									);
	$images_data_array['3'] = array(	array(	"background_image"=>"http://image.offgamers.com/img/main_page/3/mp_jcarousel_2009062601.jpg",
												"thumnail_image"=>"http://image.offgamers.com/img/main_page/3/mp_jcarousel_small_2009062601.jpg",
												"caption"=>"",
												"link" => "dragonica-sea-free-items-cntd-n-1084-3.ogm"
										),
										array(	"background_image"=>"http://image.offgamers.com/img/main_page/3/mp_jcarousel_2009063002.jpg",
												"thumnail_image"=>"http://image.offgamers.com/img/main_page/3/mp_jcarousel_small_2009063002.jpg",
												"caption"=>"",
												"link" => "jxworld-my-limited-edition-kash-card-cntd-n-1047-3.ogm"
										),
										array(	"background_image"=>"http://image.offgamers.com/img/main_page/3/mp_jcarousel_2009061901.jpg",
												"thumnail_image"=>"http://image.offgamers.com/img/main_page/3/mp_jcarousel_small_2009061901.jpg",
												"caption"=>"",
												"link" => "dragonica-sea-balloons-summer-cntd-n-1092-3.ogm"
										),
										array(	"background_image"=>"http://image.offgamers.com/img/main_page/3/mp_jcarousel_2009063004.jpg",
												"thumnail_image"=>"http://image.offgamers.com/img/main_page/3/mp_jcarousel_small_2009063004.jpg",
												"caption"=>"",
												"link" => "cib-mall-reload-500cp-extra-cntd-n-577-3.ogm"
										),
										array(	"background_image"=>"http://image.offgamers.com/img/main_page/3/mp_jcarousel_2009070305.jpg",
												"thumnail_image"=>"http://image.offgamers.com/img/main_page/3/mp_jcarousel_small_2009070305.jpg",
												"caption"=>"",
												"link" => "what-our-users-have-to-say-cntd-n-1345-3.ogm"
										),
										array(	"background_image"=>"http://image.offgamers.com/img/main_page/3/mp_jcarousel_2009070306.jpg",
												"thumnail_image"=>"http://image.offgamers.com/img/main_page/3/mp_jcarousel_small_2009070306.jpg",
												"caption"=>"",
												"link" => "enemy-at-the-gates-i-wanna-go-home-cntd-n-940-3.ogm"
										)
									);
	$selected_language_id = 1;
	if (isset($_COOKIE['RegionalSet']['language']) && $_COOKIE['RegionalSet']['language'] == 'zh-CN') {
		$selected_language_id = 2;
	}
	
	foreach ($images_data_array[$selected_language_id] as $data) {
	    if (preg_match('/^.*\.jpe?g$|^.*\.gif$|^.*\.png$/i', $data['background_image'])) {
	        $slide_type = 'image_slide';
	    } else if (preg_match('/^.*\.htm$|^.*\.html$/i', $data['background_image'])) {
	        $slide_type = 'html_slide';
	    }else if (preg_match('/^.*\.swf$/i', $data['background_image'])) {
	        $slide_type = 'swf_slide';
	    }
		switch ($slide_type) {
			case 'image_slide':
				$curr_index = $counter - 1;
				echo "	<div class='slide' id='slide_".$counter."'>";
				
				if($data['link'] != null) echo "<a href='".$data['link']."'>";
				
				echo "		<img src='".$data['background_image']."'>";
				
				if($data['link'] != null) echo "</a>\n";

				echo "	</div>\r";
				$counter++;
			break;
			default:
			break;
		}    
	}
?>
	</div>
	<div id='carousel_controls'>
		<ul id='slide_buttons'>
<?
	$button_number = 1;
	foreach($images_data_array[$selected_language_id] as $data) {
?>
			<li class='slide_<?=$button_number?>'>
				<div>
					<img class='reflex itiltnone idistance2 iopacity70 iheight40 iborder1 icolorFFFFFF' src='<?=$data['thumnail_image']?>' <?=($data['link'] != null ? ' onclick="location.href=\'' . $data['link'] . '\'"': '')?>>
				</div>
			</li>
<?
		$button_number++;
	}
?>
		</ul>
		
<?
	$caption_number = 1;
	foreach($images_data_array[$selected_language_id] as $caption) {
?>
		<div id='slide_captions_<?=$caption_number?>' class='slide_captions'><span><?=$caption['caption']?></span></div>
<?
		$caption_number++;
	}
?>
	</div>
</div>


<?
	$link_number = 1;
	foreach($images_data_array[$selected_language_id] as $link) {
?>
<div id='slide_link_<?=$link_number?>' style='display:none;'><?=$link['link']?></div>
<?
		$link_number++;
	}
?>