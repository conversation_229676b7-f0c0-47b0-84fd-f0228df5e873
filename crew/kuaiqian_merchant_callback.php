<?
	include_once('includes/application_top.php');
	require_once(DIR_WS_LANGUAGES . $language . '/' . FILENAME_KUAIQIANCALLBACK);
	
	if (isset($_SESSION['customer_id'])) {
		if ( (isset($_REQUEST['payResult']) && ($_REQUEST['payResult'] == '10' || $_REQUEST['payResult'] == '00')) &&
			 (isset($_REQUEST['result']) && $_REQUEST['result'] == 1)
			) {
			tep_redirect(tep_href_link(FILENAME_CHECKOUT_PROCESS, 'payResult='.$_REQUEST['payResult'], 'SSL'));
		} else {
			tep_redirect(tep_href_link(FILENAME_CHECKOUT_PAYMENT, 'error_message=' . urlencode(MODULE_PAYMENT_KUAIQIAN_TEXT_ERROR_MESSAGE), 'SSL', true, false));
		}
	} else {
		if (isset($_GET['orderId'])) {
			echo "Kuai<PERSON>ian's order (".$_GET['orderId'].") updated.";
		}		
	}
?>
