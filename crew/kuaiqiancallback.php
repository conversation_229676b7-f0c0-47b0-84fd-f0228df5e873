<?
/*
header("Cache-Control: no-cache");
header("Cache-Control: post-check=0,pre-check=0");
header("Cache-Control: max-age=0");
header("Pragma: no-cache");
header('Content-Type: text/xml');*/

// Must set content-type to xml, else <PERSON><PERSON><PERSON><PERSON> will not able to read and keep call this script.

require('includes/application_top.php');
include_once(DIR_WS_LANGUAGES . $language . '/' . FILENAME_CHECKOUT_PROCESS);

$result_flag = 0;
if (isset($_REQUEST) && count($_REQUEST)) {
	include_once(DIR_WS_FUNCTIONS . 'custom_product_info.php');
	include_once(DIR_WS_CLASSES . 'anti_fraud.php');
	include_once(DIR_WS_CLASSES . 'order.php');
	include_once(DIR_WS_CLASSES . 'payment.php');
	include_once(DIR_WS_CLASSES . 'log.php');
	require_once(DIR_WS_MODULES . 'payment/kuaiqian/classes/kuaiqian_ipn_class.php');

	$payment = 'kuaiqian';

	$kuaiqian_ipn = new kuaiqian_ipn($_REQUEST);
	$orders_id = $kuaiqian_ipn->get_order_id();
	if (tep_not_null($orders_id)) {
		$payment_modules = new payment($payment);
		$log_object = new log_files('system');
		$order = new order($orders_id);
		$$payment = new $payment($order->info['payment_methods_id']);

		$kuaiqianCurrencies = $order->info['currency'];
		if (!in_array($kuaiqianCurrencies, $$payment->kuaiqianCurrencies)) {
			$kuaiqianCurrencies = $$payment->defCurr;
		}
		$$payment->get_merchant_account($kuaiqianCurrencies);

		if ($order->info['orders_status_id'] == $$payment->order_status) {	// If the order still in initial status
			if ($kuaiqian_ipn->validate_receiver_account($$payment->merchantAcctId)) {
				if ($kuaiqian_ipn->validate_transaction_data($$payment)) {	// To ensure the integrity of the data posted back to merchant's server
					$payment_success = $kuaiqian_ipn->authenticate($order, $orders_id, $$payment);
					if ($payment_success) {
						if ($_REQUEST['payResult'] == '10') {
							$kuaiqian_ipn->process_this_order($orders_id, $$payment, $$payment->order_processing_status);
						}
						$result_flag = 1;
					}
				}
			}
		}
		$kq_data_array = array(	'kuaiqian_order_amount' => ($_GET['orderAmount']/pow(10,2)),
						  		'kuaiqian_order_currency' => 'CNY',
								'kuaiqian_merchant_acct_id' => $_GET['merchantAcctId'],
								'kuaiqian_version' => trim($_GET['version']),
								'kuaiqian_pay_type' => trim($_GET['payType']),
								'kuaiqian_bank_id' => trim($_GET['bankId']),
								'kuaiqian_deal_id' => trim($_GET['dealId']),
								'kuaiqian_bank_deal_id' => trim($_GET['bankDealId']),
								'kuaiqian_deal_time' => trim($_GET['dealTime']),
								'kuaiqian_pay_amount' => trim($_GET['payAmount']/pow(10,2)),
								'kuaiqian_pay_currency' => 'CNY',
								'kuaiqian_fee' => trim($_GET['fee']/pow(10,2)),
								'kuaiqian_pay_result' => trim($_GET['payResult']),
								'kuaiqian_err_code' => trim($_GET['errCode']),
								'kuaiqian_signMsg' => trim($_GET['signMsg'])
		                  		);
		$check_kq_select_sql = "SELECT orders_id
								FROM " . TABLE_KUAIQIAN . "
								WHERE orders_id = '".$_GET['orderId']."'";
		$check_kq_result_sql = tep_db_query($check_kq_select_sql);
		if (tep_db_num_rows($check_kq_result_sql)) {
			tep_db_perform(TABLE_KUAIQIAN, $kq_data_array, 'update', ' orders_id="'.(int)$orders_id.'"');
		} else {
			$kq_data_array['orders_id'] = (int)$orders_id;
			tep_db_perform(TABLE_KUAIQIAN, $kq_data_array);
		}
	}
}
echo "<result>1</result><redirecturl>".tep_href_link(FILENAME_KUAIQIAN_MERCHANT_CALLBACK, 'orderId=' . ( isset($_REQUEST['orderId']) ? (int)$_REQUEST['orderId'] : 'Unknown' ) . '&result=' . $result_flag, 'SSL')."</redirecturl>";
?>