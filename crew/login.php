<?php

/*
  $Id: login.php,v 1.105 2014/12/29 07:53:35 weesiong Exp $

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2003 osCommerce

  Released under the GNU General Public License
 */

require('includes/application_top.php');
require(DIR_WS_LANGUAGES . $language . '/' . FILENAME_LOGIN);
require(DIR_WS_CLASSES . 'recaptcha.php');

if (isset($_SESSION['post_data_holder'])) {
    foreach ($_SESSION['post_data_holder'] as $field_name => $field_value) {
        $HTTP_POST_VARS[$field_name] = $_REQUEST[$field_name] = $_POST[$field_name] = $field_value;
    }

    unset($_SESSION['post_data_holder']);
}

$action = isset($_REQUEST['action']) ? $_REQUEST['action'] : '';
$baseURL = isset($_REQUEST['baseURL']) ? '&baseURL=' . urlencode($_REQUEST['baseURL']) : '';
$email_address = isset($_POST['email_address']) ? tep_db_prepare_input($_POST['email_address']) : '';
$password = isset($_POST['password']) ? tep_db_prepare_input($_POST['password']) : '';

// redirect the customer to a friendly cookie-must-be-enabled page if cookies are disabled (or the session has not started)
if ($session_started == false) {
    tep_redirect(tep_href_link(FILENAME_COOKIE_USAGE));
}

//$oauth_login = oauth::is_oauth_login();

if (tep_session_is_registered('customer_id')) {
    if (tep_not_null($baseURL)) {
        $path = str_replace(":", "", $_REQUEST['baseURL']);
    } else {
        $path = tep_href_link(FILENAME_DEFAULT, '', 'SSL');
    }
//    if (tep_not_null($oauth_login)) {
//        switch ($oauth_login) {
//            case 'oauth':
//                oauth::Oauth_login_success(true);
//                break;
//            case 'redirect_url':
//                if (isset($_COOKIE['ogm']) && !empty($_COOKIE['ogm']['si']) && !empty($_COOKIE['ogm']['st'])) {
//                    oauth::resetSSOToken($_COOKIE['ogm']['st'], $_COOKIE['ogm']['si']);
//                }
//                $redirect_url = $_SESSION['login_redirect_url'];
//                unset($_SESSION['login_redirect_url']);
//                tep_redirect($redirect_url);
//                break;
//        }
//    } else {
        tep_redirect($path);
//    }
}

tep_redirect($shasso_obj->getLoginURL());

$error = false;
$login_error_str = '';
$authorized_access = FALSE;
$cookie_remember_me = false;
$enable_captcha = TRUE;
$sso_status = false;
if (tep_not_null($last_page)) {
    $_SESSION['trace'] = $last_page;
}

if (tep_not_null($action)) {
    switch ($action) {
        case 'process':
            //auto login from affiliate system (PAP)
            if (isset($_POST['client_id']) && $_POST['client_id'] == 'qmr' && isset($_POST['type']) && $_POST['type'] == 'launcher') {
                $enable_captcha = FALSE;
                $redirect_with_type = $_POST['type'];
            }
            if (tep_not_null($_REQUEST['param'])) {
                $param = tep_array_unserialize($_REQUEST['param']);
                $catelog_domain = ((ENABLE_SSL_AFFILIATE == 'true') ? HTTPS_AFFILIATE_SERVER : HTTP_AFFILIATE_SERVER);

                if ($param['aff_domain'] == $catelog_domain) { //check correct domain
                    $email_address = $param['aff_u'];
                    $password = $param['aff_p'];
                }
            } else if (isset($_COOKIE['ogm']) && ((isset($_COOKIE['ogm']['st']) && isset($_COOKIE['ogm']['si'])) || (isset($_COOKIE['ogm']['un']) && isset($_COOKIE['ogm']['uc'])) ) && !tep_not_null($email_address) && !tep_not_null($password)) {
                if (isset($_COOKIE['ogm']['st']) && tep_not_null($_COOKIE['ogm']['st']) && isset($_COOKIE['ogm']['si']) && tep_not_null($_COOKIE['ogm']['si'])) {
                    # Single Sign-On
                    $sso_status = oauth::getSSOTokenStatus($_COOKIE['ogm']['st'], $_COOKIE['ogm']['si']);

                    if ($sso_status) {
                        $customer_query = tep_db_query("SELECT customers_email_address
													FROM " . TABLE_CUSTOMERS . "
													WHERE customers_id = '" . tep_db_input($_COOKIE['ogm']['si']) . "'");
                        if ($customer_row = tep_db_fetch_array($customer_query)) {
                            $email_address = $customer_row['customers_email_address'];
                            $authorized_access = TRUE;
                        }
                    }
                }
                if (!$authorized_access && isset($_COOKIE['ogm']['un']) && tep_not_null($_COOKIE['ogm']['un']) && isset($_COOKIE['ogm']['uc']) && tep_not_null($_COOKIE['ogm']['uc'])) {
                    $email_address = $_COOKIE['ogm']['un'];
                    $password = $_COOKIE['ogm']['uc'];

                    $cookie_remember_me = true;
                }

                if (!tep_not_null($email_address)) {
                    authenticate::logoff();
                    if (isset($_SESSION['login_redirect_url'])) {
                        $redirect_url = $_SESSION['login_redirect_url'];
                        unset($_SESSION['login_redirect_url']);
                        tep_redirect($redirect_url);
                    }
                }
            } else {
                if (tep_not_null($email_address) && tep_not_null($password) && $enable_captcha) {
                    $validate_ip_address = isset($_POST['ext_ip']) ? $_POST['ext_ip'] : tep_get_ip_address();
                    if (recaptcha::is_captcha_require($email_address, $validate_ip_address)) {
                        if (!tep_not_null($_POST['recaptcha_challenge_field']) || !tep_not_null($_POST['recaptcha_response_field'])) {
                            $messageStack->add_session('captcha', TEXT_CAPTCHA_MISSING_ERROR);
                            if (tep_not_null($oauth_login) && $oauth_login == 'oauth') {
                                tep_redirect(tep_href_link(FILENAME_LOGIN_POPUP, 'action=login_failed&client_id=' . $_POST['client_id'] . '&request_id=' . $_POST['request_id']));
                            } else {
                                tep_redirect(tep_href_link(FILENAME_LOGIN, 'action=login_failed'));
                            }
                        } else if (recaptcha::captcha_validation($_POST['recaptcha_challenge_field'], $_POST['recaptcha_response_field']) === false) {
                            $messageStack->add_session('captcha', TEXT_CAPTCHA_ERROR);
                            if (tep_not_null($oauth_login) && $oauth_login == 'oauth') {
                                tep_redirect(tep_href_link(FILENAME_LOGIN_POPUP, 'action=login_failed&client_id=' . $_POST['client_id'] . '&request_id=' . $_POST['request_id']));
                            } else {
                                tep_redirect(tep_href_link(FILENAME_LOGIN, 'action=login_failed'));
                            }
                        }
                    }

                    if (tep_not_null($_COOKIE['ogm']['un']) && tep_not_null($_COOKIE['ogm']['uc'])) {
                        $expire = time() - 3600;
                        tep_setcookie('ogm[un]', '', $expire, $cookie_path, $cookie_domain);
                        tep_setcookie('ogm[uc]', '', $expire, $cookie_path, $cookie_domain);
                    }
                } else {
                    require_once(DIR_WS_CLASSES . 'access_token.php');
                    $otp = isset($_GET['otp']) ? tep_db_prepare_input(strip_tags($_GET['otp'])) : '';

                    if (tep_not_null($otp)) {
                        $access_token_obj = new access_token();
                        $cid = $access_token_obj->getCustomerIdFromAccessToken($otp);
                        $access_token_obj->removeAccessToken($otp);

                        $customer_query = tep_db_query("    SELECT customers_email_address
                                                            FROM " . TABLE_CUSTOMERS . "
                                                            WHERE customers_id = '" . tep_db_input($cid) . "'");
                        if ($customer_row = tep_db_fetch_array($customer_query)) {
                            $email_address = $customer_row['customers_email_address'];
                            $authorized_access = TRUE;
                        }
                    }
                }
            }

            // Check if email exists
            if ($error == false && tep_not_null($email_address)) {
                $check_customer_query = tep_db_query("select customers_id, customers_firstname, customers_lastname, account_activated, customers_password, customers_email_address, customers_default_address_id, customers_status, customers_groups_id, customers_login_sites, customers_dob, customers_country_dialing_code_id, customers_telephone
														from " . TABLE_CUSTOMERS . " where customers_status = '1'
															and customers_email_address = '" . tep_db_input($email_address) . "'
															and FIND_IN_SET( '0', customers_login_sites)");
                if (!tep_db_num_rows($check_customer_query)) {
                    $error = true;
                } else {
                    $check_customer = tep_db_fetch_array($check_customer_query);

                    if (isset($_SESSION['fb_uid'])) {
                        $fb_connect_select_sql = "	SELECT provider_uid
			    									FROM " . TABLE_CUSTOMERS_CONNECTION . "
			     									WHERE customers_id = '" . $check_customer['customers_id'] . "' AND provider='Facebook'";
                        $fb_connect_result_sql = tep_db_query($fb_connect_select_sql);
                        if ($fb_connect_row = tep_db_fetch_array($fb_connect_result_sql)) {
                            if ($fb_connect_row['provider_uid'] != $_SESSION['fb_uid']) {
                                $error = true;
                            }
                        } else {
                            $error = true;
                        }
                    } else if ($cookie_remember_me == true) {
                        if (md5($check_customer['customers_email_address'] . ':' . $check_customer['customers_password']) != $password) {
                            $error = true;

                            authenticate::logoff();
                            if (isset($_SESSION['login_redirect_url'])) {
                                $redirect_url = $_SESSION['login_redirect_url'];
                                unset($_SESSION['login_redirect_url']);
                                tep_redirect($redirect_url);
                            }
                        }
                    } else if ($authorized_access) {
                        // pass through password validation
                    } else if (!tep_validate_password($password, $check_customer['customers_password'])) {
                        $error = true;
                    }

                    if (!$error) {
                        if (SESSION_RECREATE == 'True') {
                            tep_session_recreate();
                        }

                        $check_country_query = tep_db_query("select entry_country_id, entry_zone_id from " . TABLE_ADDRESS_BOOK . " where customers_id = '" . (int) $check_customer['customers_id'] . "' and address_book_id = '" . (int) $check_customer['customers_default_address_id'] . "'");
                        $check_country = tep_db_fetch_array($check_country_query);

                        $_SESSION['customers_groups_id'] = $customers_groups_id = $check_customer['customers_groups_id'];

                        $customer_id = $check_customer['customers_id'];

                        $_SESSION['customer_default_address_id'] = $check_customer['customers_default_address_id'];
                        $customer_first_name = $check_customer['customers_firstname'];
                        $customer_last_name = $check_customer['customers_lastname'];

                        $customer_country_id = $check_country['entry_country_id'];
                        $customer_zone_id = $check_country['entry_zone_id'];
                        $phone_country_id = $check_customer['customers_country_dialing_code_id'];
                        $telephone_number = $check_customer['customers_telephone'];

                        tep_session_register('customer_id');
                        tep_session_register('customer_country_id');

                        // comment: uncomment below 2 for future use address book
                        //tep_session_register('phone_country_id');
                        //tep_session_register('customer_zone_id');

                        tep_session_register('customer_first_name');
                        tep_session_register('customer_last_name');

                        $buyback_vip_select_sql = "	SELECT vip_supplier_groups_id
													FROM " . TABLE_CUSTOMERS_VIP . "
													WHERE customers_id='" . $customer_id . "'";
                        $buyback_vip_result_sql = tep_db_query($buyback_vip_select_sql);
                        if ($buyback_vip_row = tep_db_fetch_array($buyback_vip_result_sql)) {
                            $vip_supplier_groups_id = $buyback_vip_row['vip_supplier_groups_id'];
                        } else {
                            $vip_supplier_groups_id = 1; //default set to member if success login
                        }
                        tep_session_register('vip_supplier_groups_id');

                        $diff_day = 0;
                        $customer_last_logon_select_sql = " SELECT customers_info_date_of_last_logon, customers_info_number_of_logons
                                                            FROM " . TABLE_CUSTOMERS_INFO . "
                                                            WHERE customers_info_id='" . $customer_id . "'";
                        $customer_last_logon_result_sql = tep_db_query($customer_last_logon_select_sql);
                        if ($customer_last_logon_row = tep_db_fetch_array($customer_last_logon_result_sql)) {
                            $diff_seconds = time() - strtotime($customer_last_logon_row['customers_info_date_of_last_logon']);
                            $diff_day = $diff_seconds / 86400;
                        }

                        $customer_info_data_array = array('customers_info_date_of_last_logon' => 'now()',
                            'customers_info_number_of_logons' => $customer_last_logon_row['customers_info_number_of_logons'] + 1,
                            'customer_info_selected_country' => $_SESSION['country']
                        );

                        if ($diff_day > 90) { //No login for 90days will be set as dormant account
                            $customer_info_data_array['customer_info_account_dormant'] = 1;
                        }

                        tep_db_perform(TABLE_CUSTOMERS_INFO, $customer_info_data_array, 'update', 'customers_info_id = "' . $_SESSION['customer_id'] . '"');

                        // Prevent multiple insert for same Primary Key data
                        $customer_login_insert_sql = "INSERT IGNORE INTO " . TABLE_CUSTOMERS_LOGIN_IP_HISTORY . " (customers_id, customers_login_date, customers_login_ua_info, customers_login_ip) VALUES (" . tep_db_input($_SESSION['customer_id']) . ", now(), '" . tep_db_input(isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : getenv('HTTP_USER_AGENT')) . "', '" . tep_db_input(tep_get_ip_address()) . "');";
                        tep_db_query($customer_login_insert_sql);

                        // Remember Me feature
                        if (isset($_REQUEST['remember_me']) || $cookie_remember_me) {
                            $expire = time() + 60 * 60 * 24 * 90; // 90 days expiry date;
                            tep_setcookie('ogm[un]', $email_address, $expire, $cookie_path, $cookie_domain);
                            tep_setcookie('ogm[uc]', md5($check_customer['customers_email_address'] . ':' . $check_customer['customers_password']), $expire, $cookie_path, $cookie_domain);
                        }

                        // Check SSO token table before creating new one
                        $sso_token = '';
                        $sso_row = oauth::getValidSSOToken($customer_id);
                        if (!empty($sso_row)) {
                            $sso_token = $sso_row['sso_token'];
                            $sso_status = oauth::getSSOTokenStatus($sso_token, $customer_id);
                        }
                        if (!$sso_status) {
                            $sso_token = oauth::addSSOToken($customer_id);
                        }
                        if (isset($sso_token) && $sso_token != "") {
                            $cookieLifeTime = 0; //time() + 86400
                            tep_setcookie('ogm[si]', $customer_id, $cookieLifeTime, $cookie_path, $cookie_domain);
                            tep_setcookie('ogm[st]', $sso_token, $cookieLifeTime, $cookie_path, $cookie_domain);
                            $_SESSION['sso_token'] = $sso_token;
                        } else {
                            $_SESSION['sso_token'] = $_COOKIE['ogm']['st'];
                        }

                        $_SESSION['customers_login_ip'] = $customer_login_sql_data_array['customers_login_ip'];
                        $_SESSION['customers_login_timestamp'] = time();
                        $_SESSION['login_trial'] = 0;
                        // restore cart contents
                        $cart->restore_contents();

                        $stored_customer_query = tep_db_query("select last_page_url, last_cpath from " . TABLE_CUSTOMERS_LAST_CPATH . " where customers_id = '" . (int) $customer_id . "'");
                        $stored_customer = tep_db_fetch_array($stored_customer_query);

                        //redirect back to where it's is came from
                        //if (isset($param['aff_cust_id']) && tep_not_null($param['aff_cust_id'])) {
                        //	tep_redirect(tep_affiliate_href_link(DIR_WS_AFFILIATE_AFFILIATES . $param['path'], ''));
                        //}
                        // Check contact no. required to reset by user
//                        if (tep_is_x_sharing_mobile_n_verified($customer_id, $telephone_number, $phone_country_id, 'id') !== TRUE) {
//                            $_SESSION['RESET_PHONE_NO'] = 1;
//                        }

                        update_lifetime_cookies();

                        if (isset($_SESSION['trace'])) {
                            if ($_SESSION['trace'] == 'chkout') {
                                $path = tep_href_link(FILENAME_CHECKOUT_SHIPPING);
                            } else if ($_SESSION['trace'] == 'bb_chkout') {
                                $path = tep_href_link(FILENAME_BUYBACK, 'action=update2');
                            }
                        } else {
                            if (tep_not_null($_REQUEST['baseURL'])) {
                                if (preg_match('/community/', $_REQUEST['baseURL']) || preg_match('/space.offgamers.com/', $_REQUEST['baseURL'])) {
                                    $path = $_REQUEST['baseURL'];
                                } else {
                                    $path = str_replace(":", "", $_REQUEST['baseURL']);
                                }
                            } else if (sizeof($navigation->snapshot) > 0) {
                                $origin_href = tep_href_link($navigation->snapshot['page'], tep_array_to_string($navigation->snapshot['get'], array(tep_session_name())), $navigation->snapshot['mode']);
                                $navigation->clear_snapshot();
                                $path = $origin_href;
                            } else {
                                if ($stored_customer['last_cpath']) {
                                    $path = tep_href_link(FILENAME_DEFAULT, 'cPath=' . $stored_customer['last_cpath']);
                                } else {
                                    $path = tep_href_link(FILENAME_DEFAULT);
                                }
                            }
                        }

                        if (tep_not_null($oauth_login)) {
                            switch ($oauth_login) {
                                case 'oauth':
                                    oauth::Oauth_login_success();
                                    break;
                                case 'redirect_url':
                                    $redirect_url = $_SESSION['login_redirect_url'];
                                    unset($_SESSION['login_redirect_url']);
                                    tep_redirect($redirect_url);
                                    break;
                            }
                        } else {
                            tep_redirect($path);
                        }
                    }
                }
            } else {
                $error = true;
            }

            if ($error == true) {
                $_SESSION['login_trial']++;
                if (tep_not_null($oauth_login) && $oauth_login == 'oauth') {
                    $login_error_str = sprintf(ERROR_UNRECOGNISE_USER_OR_PASSWORD, '<a href="' . tep_href_link(FILENAME_LOGIN_POPUP, 'action=forget_password', 'SSL') . '">' . TEXT_RESET_PASSWORD . '</a>');
                    $messageStack->add_session('login', $login_error_str);
                    $redirect_extra_param = '&client_id=' . $_POST['client_id'] . '&request_id=' . $_POST['request_id'];
                    if (isset($redirect_with_type))
                        $redirect_extra_param .= '&type=' . $redirect_with_type;
                    tep_redirect(tep_href_link(FILENAME_LOGIN_POPUP, 'action=login_failed' . $redirect_extra_param));
                } else {
                    $login_error_str = sprintf(TEXT_LOGIN_ERROR, '<a href="' . tep_href_link(FILENAME_LOGIN, 'action=forget_password', 'SSL') . '">' . TEXT_RESET_PASSWORD . '</a>');
                    $messageStack->add_session('login', $login_error_str);
                    tep_redirect(tep_href_link(FILENAME_LOGIN, 'action=login_failed'));
                }
            }

            break;

        case 'send_password':
            $email_address = tep_not_null($HTTP_POST_VARS['forgotten_email_address']) ? tep_db_prepare_input($HTTP_POST_VARS['forgotten_email_address']) : "";
            if (tep_not_null($email_address)) {

                $check_customer_select_sql = "	SELECT customers_firstname, customers_lastname, customers_password, customers_id, customers_gender
                                                FROM " . TABLE_CUSTOMERS . " WHERE customers_email_address = '" . tep_db_input($email_address) . "'";
                $check_customer_query = tep_db_query($check_customer_select_sql);
                if (tep_db_num_rows($check_customer_query)) {
                    $check_customer_row = tep_db_fetch_array($check_customer_query);
                    $new_password = tep_create_random_value(ENTRY_PASSWORD_MIN_LENGTH);
                    $crypted_password = tep_encrypt_password($new_password);

                    tep_db_query("update " . TABLE_CUSTOMERS . " set customers_password = '" . tep_db_input($crypted_password) . "' where customers_id = '" . (int) $check_customer_row['customers_id'] . "'");

                    $email_greeting = tep_get_email_greeting($check_customer_row["customers_firstname"], $check_customer_row["customers_lastname"], $check_customer_row["customers_gender"]);
                    $email_text2 = $email_greeting . EMAIL_PASSWORD_REMINDER_BODY . "\n\n" . EMAIL_FOOTER;
                    tep_mail($check_customer_row['customers_firstname'] . ' ' . $check_customer_row['customers_lastname'], $email_address, implode(' ', array(EMAIL_SUBJECT_PREFIX, EMAIL_PASSWORD_REMINDER_SUBJECT)), sprintf($email_text2, $email_address, $new_password), STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
                    if (tep_not_null($oauth_login) && $oauth_login == 'oauth') {
                        $messageStack->add_session('send_password', SUCCESS_PASSWORD_SENT, 'success');
                        tep_redirect(tep_href_link(FILENAME_LOGIN_POPUP, 'action=send_password_success&client_id=' . $_POST['client_id'] . '&request_id=' . $_POST['request_id']));
                    } else {
                        $messageStack->add_session('send_password', SUCCESS_PASSWORD_SENT, 'success');
                        tep_redirect(tep_href_link(FILENAME_LOGIN, 'action=send_password_success'));
                    }
                } else {
                    if (tep_not_null($oauth_login) && $oauth_login == 'oauth') {
                        $messageStack->add_session('send_password', SUCCESS_PASSWORD_SENT, 'success');
                        tep_redirect(tep_href_link(FILENAME_LOGIN_POPUP, 'action=send_password_success&client_id=' . $_POST['client_id'] . '&request_id=' . $_POST['request_id']));
                    } else {
                        $messageStack->add_session('send_password', SUCCESS_PASSWORD_SENT, 'success');
                        tep_redirect(tep_href_link(FILENAME_LOGIN, 'action=send_password_success'));
                    }
                }
            } else {
                if (tep_not_null($oauth_login) && $oauth_login == 'oauth') {
                    $messageStack->add_session('send_password', TEXT_SEND_PASSWORD_MSG);
                    tep_redirect(tep_href_link(FILENAME_LOGIN_POPUP, 'action=send_password_failed&client_id=' . $_POST['client_id'] . '&request_id=' . $_POST['request_id']));
                } else {
                    $login_error_str = TEXT_SEND_PASSWORD_MSG;
                    $messageStack->add_session('send_password', $login_error_str);
                    tep_redirect(tep_href_link(FILENAME_LOGIN, 'action=send_password_failed'));
                }
            }
            break;

        case 'create_new':
            tep_redirect(tep_href_link(FILENAME_CREATE_ACCOUNT));
            break;
    }
}

$content = CONTENT_LOGIN;
$javascript = $content . '.js';

require(DIR_WS_TEMPLATES . TEMPLATENAME_MAIN_PAGE);
require(DIR_WS_INCLUDES . 'application_bottom.php');
?>