<?
/*
  	$Id: logoff.php,v 1.23 2014/12/29 12:31:36 weesiong Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2003 osCommerce
	
  	Released under the GNU General Public License
*/

require('includes/application_top.php');
require(DIR_WS_LANGUAGES . $language . '/' . FILENAME_LOGOFF);

// remove Remember Me
if (isset($_COOKIE['ogm'])) {
	$expire = time() - 3600;
	
	tep_setcookie('ogm[un]', '', $expire, $cookie_path, $cookie_domain);
	tep_setcookie('ogm[uc]', '', $expire, $cookie_path, $cookie_domain);
}

$breadcrumb->add(NAVBAR_TITLE);

$shasso_obj->input = array();
$shasso_obj->requestClearAllCookie();
//oauth::delSSOToken($cookie_path, $cookie_domain);

tep_session_unregister('customer_id');
unset($customer_id);

tep_session_unregister('customers_groups_id');
unset($customers_groups_id);

unset($_SESSION['customer_default_address_id']);
tep_session_unregister('customer_first_name');
tep_session_unregister('customer_country_id');
tep_session_unregister('customer_zone_id');
tep_session_unregister('comments');
tep_session_unregister('custom_comments');
tep_session_unregister('buyback_comment');
tep_session_unregister('buyback_agree');

//ICW - logout -> unregister GIFT VOUCHER sessions - Thanks Fredrik
tep_session_unregister('gv_id');
tep_session_unregister('cc_id');
//ICW - logout -> unregister GIFT VOUCHER sessions  - Thanks Fredrik

//Facebook Connect
if (isset($_SESSION['fb_uid'])) {
    if (isset($_REQUEST['redirect_url'])) $logout_fb = true;
	unset($_SESSION['fb_uid']);
}

if (isset($_SESSION['FB_prelogin_switch'])) {
	unset($_SESSION['FB_prelogin_switch']);
}
//Facebook Connect

if (isset($_SESSION['need_sc_usage_qna'])) {
    unset($_SESSION['need_sc_usage_qna']);
}

// Used as a secret key for lifetime password verifying process
if (isset($_SESSION['lifetime_secret'])) {
    unset($_SESSION['lifetime_secret']);
}

$cart->reset();

# remove Single Sign-On
authenticate::logoff();

if (isset($_GET['nextURL'])) {
    tep_redirect(tep_href_link($_GET['nextURL'], '', 'SSL'));
}

if (tep_not_null($_REQUEST['redirect_url'])) {
    if (isset($logout_fb) && $logout_fb == true) {
        tep_redirect($ogm_fb_obj->getLogoutUrl(array('next'=>$_REQUEST['redirect_url'])));
    } else {
        tep_redirect($_REQUEST['redirect_url']);
    }
}
$content = CONTENT_LOGOFF;

require(DIR_WS_TEMPLATES . TEMPLATENAME_MAIN_PAGE);
require(DIR_WS_INCLUDES . 'application_bottom.php');
?>
