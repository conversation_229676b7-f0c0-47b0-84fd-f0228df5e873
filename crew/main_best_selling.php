<?php
/*
  $Id: main_best_selling.php,v 1.16 2015/01/23 09:40:02 weesiong Exp $

  Developer: Ching <PERSON>n
  Copyright (c) 2005 SKC Ventrue

  Released under the GNU General Public License
 */

if (tep_not_empty($main_content['main_best_selling'])) {
    $main_best_selling_banner = array_slice($main_content['main_best_selling'], 0, 1);
?>
    <table border="0" cellpadding="0" cellspacing="0" style="border-collapse: collapse;" align="center">
        <tr>
            <?php
            $banner_cnt = count($main_best_selling_banner);
            $banner_width = 972 / $banner_cnt;

            for ($i = 0; $banner_cnt > $i; $i++) {
                ?>
                <td width="<?php echo $banner_width; ?>px" height="125px" valign="top">
                    <div id="banner<?php echo $i; ?>" style="text-align: center;">
                        <?php echo $main_best_selling_banner[$i]; ?>
                    </div>
                    <style type="text/css">
                        #img_2 {position:absolute;top:0px;display:none;z-index:0;}
                    </style>
                    <script type="text/javascript">
                        jQuery("#banner<?php echo $i; ?>").hover(function() {
                            jQuery("#img_2", this).fadeIn('300');
                            jQuery("#img_2", this).css({display: "block", left: jQuery('#img_1', this).position().left, top: $('#img_1', this).position().top});
                        }, function() {
                            jQuery("#img_2", this).fadeOut('300');
                        });
                    </script>
                </td>
            <?php } ?>
        </tr>
    </table>
<?php } ?>