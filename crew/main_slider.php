<?php
/*
  	$Id: main_slider.php,v 1.2 2011/05/12 07:02:16 weichen Exp $
	
	Developer: Ching Yen
  	Copyright (c) 2005 SKC Ventrue
	
  	Released under the GNU General Public License
*/

if (tep_not_empty($main_content['main_slider'])) {
	$init_caption = '';
	$nav_btn_div = '';
	$slider_div = '';
	
	$rev_cnt = count($main_content['main_slider']);
	
	foreach ($main_content['main_slider'] as $num => $val) {
		$btn_active = '';
		$cnt = $num + 1;
		
		if ($cnt == 1) {
			$btn_active = ' active';
			$init_caption = $val['slider_caption'];
		}
		
		$nav_btn_div .= '<div class="os_nav_btn' . $btn_active . '" id="os_nav_' . $cnt . '"></div>';
		$slider_div .= '<div class="os" id="os_' . $cnt . '" alt="' . $val['slider_caption'] . '" style="background-image: url(' . $val['slider_background_image'] . '); z-index: ' . $rev_cnt . ';" onsubmit="window.top.location=\'' . $val['slider_image_url'] . '\'"></div>';
		
		$rev_cnt--;
	}
?>
	<link rel="stylesheet" type="text/css" href="<?=DIR_WS_IMAGES;?>css/ogm_slider.css">
	<script src="<?=DIR_WS_JAVASCRIPT;?>jcarousel/ogm_slider.js"></script>
	<div class="os_container">
		<!-- Slider background image and click action -->
		<div class="os_click" id="os_click"></div>
		<?=$slider_div;?>
		<!-- Slider navigation button -->
		<div class="os_nav" style="display: block;">
			<?=$nav_btn_div;?>
			<div class="os_nav_info" id="os_nav_title"><?=$init_caption;?></div>
		</div>
	</div>
<?php
}
?>