<?php
/*
  	$Id: main_tab.php,v 1.8 2013/02/27 07:19:19 weichen Exp $
	
	Developer: Ching Yen
  	Copyright (c) 2005 SKC Ventrue
	
  	Released under the GNU General Public License
*/

$tab_content = array();
$platform_genre_max_length = 40;

$hot_direct_top_up_tab = '';
$best_selling_tab = '';

# HOT DIRECT TOP UP
ob_start();

		if (tep_not_empty($main_content['main_tab']['dtu_game'])) {
			for ($i=0, $max = count($main_content['main_tab']['dtu_game']); $max > $i; $i++) {
				$platform = '-';
				$genre = '-';
				
				if (tep_not_empty($main_content['main_tab']['dtu_game'][$i]['platform'])) {
					$platform = implode('/', $main_content['main_tab']['dtu_game'][$i]['platform']);
				}
				
				if (tep_not_empty($main_content['main_tab']['dtu_game'][$i]['genre'])) {
					$genre = implode('/', $main_content['main_tab']['dtu_game'][$i]['genre']);
				}
				
				if (tep_not_empty($main_content['main_tab']['dtu_game'][$i]['cat_path'])) {
					$url_link = tep_href_link(FILENAME_DEFAULT, 'cPath=' . $main_content['main_tab']['dtu_game'][$i]['cat_path']);
				} else {
					$url_link = tep_href_link(FILENAME_SEARCH_ALL_GAMES, 'gid=' . $main_content['main_tab']['dtu_game'][$i]['cat_id']);
				}
	?>
			<div class="tab_row t1">
				<div class="lfloat">
						<a class="hd1" href="<?=$url_link;?>"><?=$main_content['main_tab']['dtu_game'][$i]['cat_name'];?></a>
						<div style="ctn">
							<div style="float: left; display: inline;"><?php echo TAB_TEXT_PLATFORM . $platform; ?></div>
							<div style="width: 10px; float: left; display: inline;">&nbsp;</div>
							<div style="float: left; display: inline;"><?=TAB_TEXT_GENRE . $genre;?></div>
						</div>	
				</div>
				
				<div class="rfloat">
					<?=tep_image_button2('green', $url_link, TAB_BUTTON_BUY_NOW, 105);?>
				</div>
				<div class="clrFx"></div>
			</div>
			
	<?php
				if ($max > ($i + 1)) {
					echo '<div style="" class="dotborderBtm"></div>';
				}
			}
		} else {
	?>
			<div class="tab_row t1">
				<div class="lfloat"><?=ERROR_HOT_DIRECT_TOP_UP;?></div>
 				<div class="rfloat"></div>
 				<div class="clrFx"></div>
			</div>
	<?php
		}
	?>
			<div class="tab_row dotborder" style="padding:20px 0px 0px 160px">
				<?=tep_image_button2('gray_short',tep_href_link(FILENAME_SEARCH_ALL_GAMES),BUTTON_BROWSE_IN_STORE,150);?>
			</div>

<?php
$hot_direct_top_up_tab = ob_get_contents();
ob_end_clean();

# End of HOT DIRECT TOP UP


# BEST SELLING
ob_start();

		if (tep_not_empty($main_content['main_tab']['best_selling'])) {
			for ($i=0, $max = count($main_content['main_tab']['best_selling']); $max > $i; $i++) {
				$platform = '-';
				$genre = '-';
				
				if (tep_not_empty($main_content['main_tab']['best_selling'][$i]['platform'])) {
					$platform = implode('/', $main_content['main_tab']['best_selling'][$i]['platform']);
				}
				
				if (tep_not_empty($main_content['main_tab']['best_selling'][$i]['genre'])) {
					$genre = implode('/', $main_content['main_tab']['best_selling'][$i]['genre']);
				}
				
				if (tep_not_empty($main_content['main_tab']['best_selling'][$i]['cat_path'])) {
					$url_link = tep_href_link(FILENAME_DEFAULT, 'cPath=' . $main_content['main_tab']['best_selling'][$i]['cat_path']);
				} else {
					$url_link = tep_href_link(FILENAME_SEARCH_ALL_GAMES, 'gid=' . $main_content['main_tab']['best_selling'][$i]['cat_id']);
				}
	?>
				<div class="tab_row t1">
					<div class="lfloat">
						<a class="hd1" href="<?=$url_link;?>"><?=$main_content['main_tab']['best_selling'][$i]['cat_name'];?></a>
						<div style="ctn">
                            <div style="float: left; display: inline;"><?=TAB_TEXT_PLATFORM . $platform;?></div>
							<div style="width: 10px; float: left; display: inline;">&nbsp;</div>
							<div style="float: left; display: inline;"><?=TAB_TEXT_GENRE . $genre;?></div>
						</div>
					</div>
					
					<div class="rfloat">
						<?=tep_image_button2('green', $url_link, TAB_BUTTON_BUY_NOW, 105);?>
					</div>
					<div class="clrFx"></div>
				</div>
						
	<?php
				if ($max > ($i + 1)) {
					echo '<div style="" class="dotborderBtm"></div>';
				}
			}
		} else {
	?>
			<div class="tab_row t1">
				<div class="lfloat"><?=ERROR_BEST_SELLING;?></div>
 				<div class="rfloat"></div>
 				<div class="clrFx"></div>
			</div>
	<?php
		}
	?>
			<div class="tab_row dotborder" style="padding:20px 0px 0px 160px">
				<?=tep_image_button2('gray_short',tep_href_link(FILENAME_SEARCH_ALL_GAMES),BUTTON_BROWSE_IN_STORE,150);?>
			</div>

<?php
$best_selling_tab = ob_get_contents();
ob_end_clean();
# End of BEST SELLING


$tab_content[] = array( 'label' => TAB_HEADER_BEST_SELLING, 
						'content' => $best_selling_tab );
$tab_content[] = array( 'label' => TAB_HEADER_HOT_DIRECT_TOP_UP, 
						'content' => $hot_direct_top_up_tab );

echo $page_obj->get_html_tab($tab_content, 2);
?>