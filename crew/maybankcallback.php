<?
require('includes/application_top.php');

global $maybankReturnedStatus;

if (isset($_GET) && count($_GET)) {
	include_once(DIR_WS_FUNCTIONS . 'custom_product_info.php');
	include_once(DIR_WS_CLASSES . 'anti_fraud.php');
	include_once(DIR_WS_CLASSES . 'order.php');
	include_once(DIR_WS_CLASSES . 'payment.php');
	include_once(DIR_WS_CLASSES . 'log.php');
	
	include_once(DIR_WS_LANGUAGES . $language . '/' . FILENAME_CHECKOUT_PROCESS);
	
	$maybankReturnedStatus = '';
	if (isset($_REQUEST['status'])) {
		$maybankReturnedStatus = trim($_REQUEST['status']);
	}
	
	/********************************************************
		NOTE: authMode = E (for pre-authorisation)
						 A (for immediate authorisation)
	********************************************************/
	
	$payment = 'maybank';
	$payment_modules = new payment($payment);
	
	if (isset($_GET['referenceNo']) && tep_not_null($_GET['referenceNo'])) {

		require_once(DIR_WS_MODULES . 'payment/maybank/classes/maybank_ipn_class.php');
		
		$orders_id = (int)$_GET['referenceNo'];

		$maybank_ipn = new maybank_ipn($_GET);
		$log_object = new log_files('system');	
		$order = new order($orders_id);
		if (isset($order->info['currency'])) $currency = $order->info['currency'];
		
		$$payment = new $payment($order->info['payment_methods_id']);
		
		$maybankCurrencies = $order->info['currency'];
      	if (!in_array($maybankCurrencies, $$payment->maybankCurrencies)) {
        	$maybankCurrencies = $$payment->defCurr;
      	}
      	
		$$payment->get_merchant_account($maybankCurrencies);
		if ($maybank_ipn->validate_receiver_account($$payment->payee_code)) {
			if ($maybank_ipn->validate_transaction_data($$payment, $orders_id)) {
				if ($maybank_ipn->authenticate($order, $orders_id, $$payment)) {
					// Order updated to Verifying status
				}
			}
		}
		
		// Capture this no matter success or not
		$maybank_payment_data_array = array('maybank_status' => (isset($maybankReturnedStatus)?$maybankReturnedStatus:'01'),
											'maybank_corporate' => $_GET['corpName'],
											'maybank_account' => $_GET['accountNo'],
											'maybank_currency' => (isset($maybankCurrencies)?$maybankCurrencies:''),
											'maybank_amount' => $_GET['transAmount']/100,
											'maybank_approval_code' => $_GET['approvalCode'],
											'maybank_reference_id' => $_GET['bankRefNo']);
		
		$maybank_select_sql = "	SELECT orders_id
								FROM " . TABLE_MAYBANK . " 
								WHERE orders_id = '".$orders_id."'";
		$maybank_result_sql = tep_db_query($maybank_select_sql);
		if (tep_db_num_rows($maybank_result_sql)) {
			tep_db_perform(TABLE_MAYBANK, $maybank_payment_data_array, 'update', ' orders_id = "'.(int)$orders_id.'"');
		} else {
			$maybank_payment_data_array['orders_id'] = (int)$orders_id;
			tep_db_perform(TABLE_MAYBANK, $maybank_payment_data_array);
		}
	}
}
?>
<html>
	<head></head>
	<body onload="return document.maybank_payment_success.submit();">
<?
		$url = tep_href_link(FILENAME_CHECKOUT_PROCESS);
		echo "\n".tep_draw_form('maybank_payment_success', $url, 'post');
		
		reset($_GET);
		while (list($key, $value) = each($_GET)) {
			if (!is_array($_GET[$key])) {
				echo tep_draw_hidden_field($key, htmlspecialchars(stripslashes($value)));
			}
		}
?>
		</form>
	</body>
</html>