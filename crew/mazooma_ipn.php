<?
/*
  	$Id: mazooma_ipn.php, v1.0 2005/11/29 18:08:05
  	Author : <PERSON> (<EMAIL>)
  	Title: kuaiqian.com Payment Module V1.0
	
	Revisions:
		Version 1.0 Initial Payment Module
	
  	Copyright (c) 2007 SKC Venture
	
  	Released under the GNU General Public License
*/

require_once('includes/application_top.php');
include_once(DIR_WS_LANGUAGES . $language . '/' . FILENAME_CHECKOUT_PROCESS);

if (isset($_REQUEST) && count($_REQUEST)) {
	$payment = 'mazooma';
    
    require_once(DIR_WS_MODULES . 'payment/mazooma/classes/mazooma_ipn_class.php');
    $mazooma_ipn = new mazooma_ipn($_REQUEST);
    $orders_id = $mazooma_ipn->get_order_id();
    
    include_once(DIR_WS_CLASSES . 'anti_fraud.php');
	include_once(DIR_WS_CLASSES . 'log.php');
    include_once(DIR_WS_CLASSES . 'order.php');
    include_once(DIR_WS_CLASSES . 'payment.php');
    
    $log_object = new log_files('system');
	$order = new order($orders_id);
	
	$payment_modules = new payment($payment);
    $$payment = new $payment($order->info['payment_methods_id']);
    
	$mazooma_get_supported_currencies = $$payment->get_support_currencies($$payment->payment_methods_id, false);
	$mazooma_currency = $order->info['currency'];
  	if (!in_array($mazooma_currency, $mazooma_get_supported_currencies)) {
    	$mazooma_currency = $mazooma_get_supported_currencies[0];
  	}
  	
  	$mazooma_protocal_authenticate_str = '';
  	foreach ($mazooma_ipn->key as $mazomma_ipn_key => $mazomma_ipn_value) {
  		if ($mazooma_protocal_authenticate_str!='') {
  			$mazooma_protocal_authenticate_str .= '&';
  		}
  		$mazooma_protocal_authenticate_str .= $mazomma_ipn_key . '=' . $mazomma_ipn_value;
  	}
  	
  	$vp = curl_init();
	curl_setopt($vp, CURLOPT_URL, 'https://www.mazooma.com/service/servlet/ConfirmTrans');
	curl_setopt($vp, CURLOPT_POST, 1);
	curl_setopt($vp, CURLOPT_POSTFIELDS, $mazooma_protocal_authenticate_str);
	curl_setopt($vp, CURLOPT_RETURNTRANSFER, 1);
	curl_setopt($vp, CURLOPT_SSL_VERIFYPEER, 0);
	$verification_rsp = curl_exec($vp);
  	$comm_error_code = curl_errno($vp);
	$rsp_http_code = curl_getinfo($vp, CURLINFO_HTTP_CODE);
	curl_close($vp);

	ob_start();
	echo '$comm_error_code = ' . $comm_error_code . "<BR>";
	echo '$rsp_http_code = ' . $rsp_http_code . "<BR>";
	echo '$verification_rsp = ' . $verification_rsp . "<BR>";
	echo '$mazooma_protocal_authenticate_str = ' . $mazooma_protocal_authenticate_str . "<BR>";
	echo '$mazooma_currency = ' . $mazooma_currency . "<BR>";
	echo '<pre>';
	print_r($$payment);
	echo '</pre>';
	$aaa = ob_get_contents();
	ob_end_clean();
	
	if($comm_error_code != CURLE_OK or $rsp_http_code != 200) { // check communication errors
		@tep_mail('<EMAIL>', '<EMAIL>', $orders_id . ' - Mazooma Protocol Verify - comm_error_code || rsp_http_code', $aaa, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
	} elseif(trim($verification_rsp) != "verification_code=0") { // check verification code
		@tep_mail('<EMAIL>', '<EMAIL>', $orders_id . ' - Mazooma Protocol Verify - verification_rsp || verification_code', $aaa, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
	} else {
	  	$$payment->get_merchant_account($mazooma_currency);
	  	if ($mazooma_ipn->validate_receiver_account($$payment->mazooma_id)) {
			if ($mazooma_ipn->validate_transaction_data($$payment, $orders_id)) {
				if ($mazooma_ipn->authenticate($order, $orders_id, $$payment)) {
					// Order updated to Verifying status
				}
			}
		}
	}
	
	$mazooma_payment_data_array = array('mazooma_transaction_number' => tep_db_prepare_input($mazooma_ipn->key['txn_num']),
								  		'mazooma_user_id' => tep_db_prepare_input($mazooma_ipn->key['merchant_user_id']),
								  		'mazooma_fee' => tep_db_prepare_input($mazooma_ipn->key['txn_fee']),
								  		'mazooma_amount' => tep_db_prepare_input($mazooma_ipn->key['txn_amount']),
								  		'mazooma_currency' => tep_db_prepare_input($mazooma_ipn->key['txn_currency']),
								  		'mazooma_status' => tep_db_prepare_input($mazooma_ipn->key['txn_status']),
								  		'mazooma_error_code' => tep_db_prepare_input($mazooma_ipn->key['error_code'])
		                      		);
	// check order exist
	$mazooma_check_exist_select_sql = "	SELECT orders_id 
										FROM " . TABLE_MAZOOMA . " 
										WHERE orders_id = '".(int)$orders_id."'";
	$mazooma_check_exist_result_sql = tep_db_query($mazooma_check_exist_select_sql);
	if (tep_db_num_rows($mazooma_check_exist_result_sql)) {
		tep_db_perform(TABLE_MAZOOMA, $mazooma_payment_data_array, 'update', ' orders_id = "'.(int)$orders_id.'" ');
	} else {
		$mazooma_payment_data_array['orders_id'] = (int)$orders_id;
		tep_db_perform(TABLE_MAZOOMA, $mazooma_payment_data_array);
	}
}
?>
