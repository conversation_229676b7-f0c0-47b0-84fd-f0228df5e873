<?
/*
  	$Id: mb_ipn.php, v1.0 2005/11/29 18:08:05
  	Author : <PERSON> (<EMAIL>)
  	Title: Moneybookers.com Payment Module V1.0
	
	Revisions:
		Version 1.0 Initial Payment Module
	
  	Copyright (c) 2006 SKC Venture
	
  	Released under the GNU General Public License
*/

require_once('includes/application_top.php');
require_once(DIR_WS_MODULES . 'payment/mb/classes/mb_ipn_class.php');
include(DIR_WS_LANGUAGES . $language . '/' . FILENAME_CHECKOUT_PROCESS);
include_once(DIR_WS_LANGUAGES . $language . '/modules/payment/moneybookers.php');
include_once(DIR_WS_CLASSES . 'order.php');
//Process payment
if (isset($_POST) && count($_POST)) {
	/**********************************************************
		(1) Check this order is pay to us
		(2) This is Pending order
	**********************************************************/
	$payment = 'moneybookers';
    
    // load selected payment module
    include_once(DIR_WS_CLASSES . 'payment.php');    
    $mb_ipn = new mb_ipn($_POST);
    $orders_id = $mb_ipn->get_order_id();
    $order = new order($orders_id);

    $payment_modules = new payment($payment);
    $$payment = new $payment($order->info['payment_methods_id']);
    $$payment->get_merchant_account($mb_ipn->key['currency']);

	//transaction_id
	include_once(DIR_WS_FUNCTIONS . 'custom_product_info.php');
	include_once(DIR_WS_CLASSES . 'anti_fraud.php');
	include_once(DIR_WS_CLASSES . 'store_credit.php');
	include_once(DIR_WS_CLASSES . 'log.php');
    
	$log_object = new log_files('system');
	
	if ($mb_ipn->validate_receiver_email($$payment->mb_email_id, $$payment->mb_ref_id)) {
		$orders_id = $mb_ipn->get_order_id();
		
		if (tep_not_null($orders_id)) {
			$order = new order($orders_id);
            $mb_email = tep_db_prepare_input($mb_ipn->key['pay_from_email']);
			$customer_mb_verify_sql = " select serial_number, info_verified 
                                            from " . TABLE_CUSTOMERS_INFO_VERIFICATION . " 
                                            where customers_info_value = '" . $mb_email . "' 
                                                and customers_id = '" . (int)$order->customer['id'] . "' 
                                                and info_verification_type = 'email'";
            $customer_mb_verify_result_sql = tep_db_query($customer_mb_verify_sql);
            $customer_mb_verify_row = tep_db_fetch_array($customer_mb_verify_result_sql);
            $send_verification_mail = false;
            if (tep_db_num_rows($customer_mb_verify_result_sql) > 0) {
                if ($customer_mb_verify_row['info_verified'] == 0) {
                    if (tep_not_null($customer_mb_verify_row['serial_number'])) {
                        $serial_number = $customer_mb_verify_row['serial_number'];
                    } else {
                        $serial_number = tep_gen_random_serial($mb_email, TABLE_CUSTOMERS_INFO_VERIFICATION, 'serial_number', '', 12);
                        tep_db_query("  update " . TABLE_CUSTOMERS_INFO_VERIFICATION . " 
                                        set serial_number = '" . $serial_number . "' 
                                        where customers_info_value = '" . $mb_email . "' 
                                            and customers_id ='" . (int)$order->customer['id'] . "'");
                    }
                    $send_verification_mail = true;
                }
            } else if (tep_db_num_rows($customer_mb_verify_result_sql) < 1) {
                $serial_number = substr(tep_gen_random_serial($mb_email, TABLE_CUSTOMERS, "serial_number"), 0, 12);

                $sql_data_array = array('customers_id ' => (int)$order->customer['id'],
                                        'customers_info_value' => $mb_email,
                                        'serial_number' => $serial_number,
                                        'info_verified' => 0,
                                        'info_verification_type' => 'email');

                tep_db_perform(TABLE_CUSTOMERS_INFO_VERIFICATION, $sql_data_array);
                $send_verification_mail = true;
            }
            if ($send_verification_mail == true) {
                $activate_address = tep_href_link(FILENAME_ACCOUNT_ACTIVATE, 'serial=' . $serial_number . '&email=' . urlencode($mb_email) . '&action=verify_email');
                $link = '<a href="' . $activate_address . '" target="_blank">' . $activate_address . '</a><br>';
                $customer_profile_select_sql = "SELECT customers_gender, customers_firstname, customers_lastname, customers_email_address 
                                                FROM " . TABLE_CUSTOMERS . " 
                                                WHERE customers_id = '" . (int)$order->customer['id'] . "'";
                $customer_profile_result_sql = tep_db_query($customer_profile_select_sql);
                if ($customer_profile_row = tep_db_fetch_array($customer_profile_result_sql)) {
                    $email_firstname = $customer_profile_row['customers_firstname'];
                    $email_lastname = $customer_profile_row['customers_lastname'];
                    $customer_email = $customer_profile_row['customers_email_address'];
                }                
                $email_greeting = tep_get_email_greeting($email_firstname, $email_lastname, $customer_profile_result_sql['customers_gender']);
                $email_text = 	$email_greeting . 
                                TEXT_MB_PAYMENT_MAKE_EMAIL_CONTENT1 . 
                                $_POST['currency'] . $_POST['amount'] . 
                                TEXT_MB_PAYMENT_MAKE_EMAIL_CONTENT2 . 
                                '  ' . $mb_email .
                                TEXT_MB_EMAIL_VERIFY_INSTRUCTION_CONTENT .
                                $link .
                                TEXT_MB_EMAIL_VERIFY_ENDING_CONTENT . "\n\n\n" . EMAIL_CONTACT . EMAIL_FOOTER;
                tep_mail($email_firstname, $mb_email, implode(' ', array(EMAIL_SUBJECT_PREFIX, TEXT_MB_VERIFICATION_TITLE)), $email_text, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
                if ($mb_email != $customer_email) {
                    $mb_info_select_sql = " SELECT mb_paid_currency, mb_paid_amount
                                            FROM " . TABLE_PAYMENT_MONEYBOOKERS . " 
                                            WHERE mb_order_id = '" . (int)$orders_id . "'";
                    $mb_info_result_sql = tep_db_query($mb_info_select_sql);
                    $mb_info_row = tep_db_fetch_array($mb_info_result_sql);
                    $profile_email_text = 	$email_greeting . 
                                            TEXT_MB_PAYMENT_MAKE_EMAIL_CONTENT1 .
                                            $mb_info_row['mb_paid_currency'] . $mb_info_row['mb_paid_amount'] .
                                            TEXT_MB_PAYMENT_MAKE_EMAIL_CONTENT2 .
                                            '  ' . $mb_email .
                                            TEXT_MB_EMAIL_VERIFICATION_NOTICE_HEADING_CONTENT .
                                            EMAIL_SUBJECT_PREFIX . TEXT_MB_VERIFICATION_TITLE .
                                            TEXT_MB_EMAIL_VERIFICATION_NOTICE_ENDING_CONTENT .
                                            "\n\n\n" . EMAIL_CONTACT . EMAIL_FOOTER;
                    $name = $email_firstname . ' ' . $email_lastname;
                    tep_mail($name, $customer_email, implode(' ', array(EMAIL_SUBJECT_PREFIX, TEXT_NOTICE_OF_MB_VERIFICATION_SEND_TITLE)), $profile_email_text, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
                }
                $sql_data_array = array('orders_id' => (int)$orders_id,
                                        'orders_status_id' => 0,
                                        'date_added' => 'now()',
                                        'customer_notified' => 1,
                                        'comments' => REMARK_MB_VERIFICATION_EMAIL_SENT . ($mb_email != $customer_email ? ' ' . REMARK_MB_VERIFICATION_EMAIL_NOTICE_SENT : '')
                                        );

                tep_db_perform(TABLE_ORDERS_STATUS_HISTORY, $sql_data_array);
            }
			if ($mb_ipn->validate_transaction_data($order, $$payment)) {	// To ensure the integrity of the data posted back to merchant's server
				if ($order->info['orders_status_id'] == $$payment->order_status) {	// If the order still in initial status
					$mb_ipn->authenticate($order, $orders_id, $$payment);
				}
			}
		}
	}
}
?>