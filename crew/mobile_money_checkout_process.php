<?

	require('includes/application_top.php');
	include_once(DIR_WS_FUNCTIONS . 'customers_info_verification.php');
	
	$post_data = array();
	foreach ($_POST as $post_name=>$post_value) {
		$post_data[$post_name] = $post_value;
	}
	
	if (count($post_data) && isset($_SESSION['order_logged']) && tep_not_null($_SESSION['order_logged'])) {
		
		$customer_complete_phone_info_array = tep_format_telephone($customer_id);
		$post_data['mobileno'] = '+'.$customer_complete_phone_info_array['country_international_dialing_code'] . $_SESSION['customers_telephone'];
		
		include_once(DIR_WS_CLASSES . 'order.php');
		include_once(DIR_WS_CLASSES . 'payment.php');
		
		$order = new order($_SESSION['order_logged']);
		
		$payment = 'mobile_money';
		$payment_modules = new payment($payment);
		$mobile_money_module = new $payment($order->info['payment_methods_id']);
		
		$ch = curl_init();
		curl_setopt($ch, CURLOPT_URL,$mobile_money_module->form_action_url);
		curl_setopt($ch, CURLOPT_POST, 1);
		curl_setopt($ch, CURLOPT_POSTFIELDS,$post_data);
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
		curl_setopt($ch, CURLOPT_VERBOSE, 1);
		curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
		curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0); 
		curl_setopt($ch, CURLOPT_TIMEOUT, 500);
		if ($mobile_money_module->connect_via_proxy) {
			curl_setopt($ch, CURLOPT_PROXY, 'http://mvy-proxy:3128');
			curl_setopt($ch, CURLOPT_PROXYUSERPWD, 'devbackend:devonly');
		}
		$result = curl_exec($ch);
		preg_match_all("/^([0-9]+) : (.+)/", $result, $matched_result);
		
		$result_code = $matched_result[1][0];
		if ($result_code == '0000' || $result_code == '0') {
			tep_redirect(tep_href_link(FILENAME_CHECKOUT_PROCESS, '', 'SSL'));
		} else {
			$mm_payment_history_data_array = array(	'orders_id' => (int)$_SESSION['order_logged'], 
											  		'money_money_request_date' => 'now()',
											  		'mobile_money_tran_status' => '9',
											  		'mobile_money_tran_description' => trim($result)
						                      		);
			tep_db_perform(TABLE_MOBILE_MONEY_HISTORY, $mm_payment_history_data_array);
			
			tep_redirect(tep_href_link(FILENAME_CHECKOUT_PAYMENT, 'error_message=' . urlencode($matched_result[2][0]), 'SSL', true, false));
		}
	}
?>