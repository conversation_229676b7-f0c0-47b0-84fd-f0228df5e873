<?

/*
  $Id: mobile_money_ipn.php, v1.0 2005/11/29 18:08:05
  Author : <PERSON> (<EMAIL>)
  Title: WebMoney Payment Module V1.0

  Revisions:
  Version 1.0 Initial Payment Module

  Copyright (c) 2007 SKC Venture

  Released under the GNU General Public License
 */
header("status: 200");

require_once('includes/application_top.php');

require_once(DIR_WS_MODULES . 'payment/mobile_money/classes/mobile_money_ipn_class.php');
include_once(DIR_WS_LANGUAGES . $language . '/' . FILENAME_CHECKOUT_PROCESS);

//Process payment
if (isset($_POST) && count($_POST)) {
    $payment = 'mobile_money';

    $mobile_money_ipn = new mobile_money_ipn($_POST);
    $orders_id = $mobile_money_ipn->get_order_id();

    include_once(DIR_WS_FUNCTIONS . 'custom_product_info.php');
    include_once(DIR_WS_CLASSES . 'anti_fraud.php');
    include_once(DIR_WS_CLASSES . 'log.php');
    include_once(DIR_WS_CLASSES . 'order.php');
    include_once(DIR_WS_CLASSES . 'payment.php');

    $log_object = new log_files('system');
    $order = new order($orders_id);

    $payment_modules = new payment($payment);
    $$payment = new $payment($order->info['payment_methods_id']);

    if (in_array($order->info['currency'], $$payment->mm_currencies)) {
        $$payment->get_merchant_account($order->info['currency']);
    } else {
        $$payment->get_merchant_account($$payment->currency);
    }

    if (isset($_POST['mm_cmd'])) {
        switch ($_POST['mm_cmd']) {
            case 'TRANX_REQ':
                $email_debug = "TRANX_REQ";
                if ((int) $orders_id > 0) {
                    $email_debug .= '<BR>(int)$orders_id > 0';
                    $verified = false;
                    if ($mobile_money_ipn->validate_receiver_account($$payment->mmm_merchantepurseno)) {
                        $email_debug .= '<BR>validate_receiver_account';
                        if ($mobile_money_ipn->validate_transaction_data($$payment, $order, false)) {
                            $email_debug .= '<BR>validate_transaction_data';
                            $verified = true;
                        }
                    }
                    $email_debug .= '<BR>verified = ' . $verified;
                    if ($verified) {
                        $mm_payment_history_data_array = array('orders_id' => (int) $orders_id,
                            'money_money_request_date' => 'now()',
                            'mobile_money_tran_status' => '',
                            'mobile_money_tran_description' => 'Merchant Verification: VERIFIED');
                        tep_db_perform(TABLE_MOBILE_MONEY_HISTORY, $mm_payment_history_data_array);
                        echo "VERIFIED";
                    } else {
                        echo "INVALID";
                    }
                }
                break;

            case 'TRANX_RESP':
                if ($mobile_money_ipn->validate_receiver_account($$payment->mmm_merchantepurseno)) {
                    if ($mobile_money_ipn->validate_transaction_data($$payment, $order, false)) {
                        if ($order->info['orders_status_id'] == $$payment->order_status) { // If the order still in initial status
                            $payment_success = $mobile_money_ipn->authenticate($order, $orders_id, $$payment);
                        }
                    }
                }
                break;
        }
    }
}
?>