<?
/*
	$Id: mozcom_callback.php,v 1.1 2009/10/13 07:02:38 boonhock Exp $
  	Author : 	<PERSON> (<EMAIL>)
  	
	Copyright (c) 2009
  	Released under the GNU General Public License
*/

require('includes/application_top.php');

$form_data = array();
$data = '';
if (isset($_REQUEST) && count($_REQUEST)) {
	foreach($_REQUEST as $key => $value) {
		$form_data[$key] = $value;
	}
}

require_once(DIR_WS_MODULES . 'payment/mozcom.php');
$mozcom_obj = new mozcom();
$mozcom_obj->clear_temp_process($_SESSION['customer_id']);

?>
<html>
	<head></head>
	<body>
<?
		echo "\n".tep_draw_form('mozcom_payment_success', tep_href_link(FILENAME_CHECKOUT_PROCESS), 'post');
		reset($form_data);
		while (list($key, $value) = each($form_data)) {
			if (!is_array($_REQUEST[$key])) {
				echo tep_draw_hidden_field($key, htmlspecialchars(stripslashes($value)));
			}
		}
?>
		</form>
		<script type="text/javascript" language="JavaScript">
			document.mozcom_payment_success.submit();
		</script>
	</body>
</html>