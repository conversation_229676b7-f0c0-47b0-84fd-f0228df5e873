<?php
require('includes/application_top.php');
require(DIR_WS_LANGUAGES . $language . '/' . FILENAME_MY_AFFILIATE_OVERVIEW);

if (!tep_session_is_registered('customer_id')) {
	$navigation->set_snapshot();
    tep_redirect(tep_href_link(FILENAME_LOGIN, '', 'SSL'));
}

// grap affiliate's latest news
$aff_latest_news_select_qry = "	SELECT lnd.headline, lnd.latest_news_summary, lnd.content 
							   	FROM ". TABLE_LATEST_NEWS ." AS ln
							   	INNER JOIN ".TABLE_LATEST_NEWS_DESCRIPTION." AS lnd
							   		ON ln.news_id = lnd.news_id
							   	WHERE ln.status='1' 
							   		AND ln.news_groups_id='3' 
							   		AND ln.news_display_sites='2' 
							   		AND lnd.language_id ='".(int)$default_languages_id."'
							   	ORDER BY ln.date_added ASC ";
$aff_latest_news_result = tep_db_query($aff_latest_news_select_qry);

$news_cnt = 0;
while($aff_latest_news_array = tep_db_fetch_array($aff_latest_news_result)) {
	$aff_latest_news[$news_cnt]['headline'] = $aff_latest_news_array['headline'];
	$aff_latest_news[$news_cnt]['latest_news_summary'] = $aff_latest_news_array['latest_news_summary'];
	$aff_latest_news[$news_cnt]['content'] = $aff_latest_news_array['content'];
	$news_cnt++;
}

$customers_login_sites_select_sql = "   SELECT customers_login_sites
                                        FROM " . TABLE_CUSTOMERS . "
                                        WHERE customers_id = '" . (int)$customer_id . "'";
$customers_login_sites_result_sql = tep_db_query($customers_login_sites_select_sql);
$customers_login_sites_row = tep_db_fetch_array($customers_login_sites_result_sql);

if (!in_array(2, explode(',', $customers_login_sites_row['customers_login_sites']))) {
        $messageStack->add('aff_overview', TEXT_ACCOUNT_UPGRADE_AFFILIATE, 'success');
}

$inner_content = tep_affiliate_href_link('affiliates/'.FILENAME_MY_AFFILIATE_PANEL.'#Home');

$content = CONTENT_MY_AFFILIATE_OVERVIEW;

$breadcrumb->add(NAVBAR_TITLE, tep_href_link(FILENAME_MY_AFFILIATE_OVERVIEW, '', 'SSL'));

require(DIR_WS_TEMPLATES . TEMPLATENAME_MAIN_PAGE);

require(DIR_WS_INCLUDES . 'application_bottom.php');

?>