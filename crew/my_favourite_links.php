<?php

require('includes/application_top.php');
tep_redirect(tep_href_link(FILENAME_ACCOUNT, '', 'SSL'));
require(DIR_WS_LANGUAGES . $language . '/' . FILENAME_MY_FAVOURITE_LINKS);
require_once(DIR_WS_FUNCTIONS . 'supplier.php');
require_once(DIR_WS_CLASSES . 'buyback_supplier.php');
include_once(DIR_WS_CLASSES . 'vip_order.php');

define('DISPLAY_PRICE_DECIMAL', 2);
define('BUYBACK_PRICE_DEFAULT_CURRENCY', DEFAULT_CURRENCY);

if (!tep_session_is_registered('customer_id')) {
    $navigation->set_snapshot();
    tep_redirect(tep_href_link(FILENAME_LOGIN, '', 'SSL'));
}

include_once(DIR_WS_CLASSES . 'buyback.php');

//Define content that gets included in the center column.
//Must match $content . ".tpl.php" in templates/content/
$content = CONTENT_MY_FAVOURITE_LINKS;
//Define the javascript file

//Creating session key based on filename so we know where it got its values.
$form_session_name = FILENAME_MY_FAVOURITE_LINKS;
if (!tep_session_is_registered($form_session_name)) {
    //tep_session_unregister($form_session_name);
    $$form_session_name = array();
    tep_session_register($form_session_name);
}

$form_values_arr = $_SESSION[$form_session_name];

if (isset($_REQUEST['action'])) {
    $action = $_REQUEST['action'];
} else {
    $action = 'select';
}

/**
 * Validate the post
 */
$errorCount = 0;
switch ($action) {
    case 'add': // Also for adding from different page (i.e. i want to sell.)
        if ($products_id = (int) $_POST['fvl_input_product_select']) {
            $errorCount++;

            //Don't insert if already exists
            $check_favourite_exists_select_sql = "  SELECT user_id
                                                        FROM " . TABLE_USER_FAVOURITE_PRODUCTS . "
                                                        WHERE user_id = '" . $_SESSION['customer_id'] . "'
                                                            AND products_id='" . $products_id . "'";
            $check_favourite_exists_result_sql = tep_db_query($check_favourite_exists_select_sql);
            if (!tep_db_num_rows($check_favourite_exists_result_sql)) {
                //Insert now
                $favourite_product_insert_sql = "INSERT IGNORE INTO " . TABLE_USER_FAVOURITE_PRODUCTS . " (user_id, products_id) VALUES (" . tep_db_input($_SESSION['customer_id']) . ", $products_id);";
                tep_db_query($favourite_product_insert_sql);
                $errorCount--;
//					$insert_favourite_product_array = array('products_id' => $products_id, 'user_id' => $_SESSION['customer_id']);
//					$insert_success = tep_db_perform(TABLE_USER_FAVOURITE_PRODUCTS, $insert_favourite_product_array);
//					if ($insert_success) {
//						$errorCount--;
//					}
            } else {
                $messageStack->add_session($content, TEXT_FAVOURITE_EXISTS);
            }
        }
        if ($errorCount > 0) {
            $messageStack->add_session($content, TEXT_ERROR_TRYAGAIN);
        } else {
            $messageStack->add_session($content, SUCCESS_ADD_FAV, 'success');
        }
        $_SESSION[$form_session_name]['fvl_input_game_select'] = $_POST['fvl_input_game_select'];
        tep_redirect(tep_href_link(FILENAME_MY_FAVOURITE_LINKS, tep_get_all_get_params(array('action'))));

        break;
    case 'batch_action':
        if (isset($_REQUEST['server_batch']) && count($_REQUEST['server_batch'])) {
            switch ($_REQUEST['batch_action']) {
                case 'AddPresale':
                    for ($batchCnt = 0; $batchCnt < count($_REQUEST['server_batch']); $batchCnt++) {
                        $presale_update_sql = "	UPDATE " . TABLE_USER_FAVOURITE_PRODUCTS . "
													SET favourite_products_presale_notice = '1'
													WHERE products_id = '" . tep_db_input($_REQUEST['server_batch'][$batchCnt]) . "'
														AND user_id = '" . tep_db_input($_SESSION['customer_id']) . "'";
                        tep_db_query($presale_update_sql);
                    }
                    $messageStack->add_session($content, SUCCESS_ADD_PRESALE, 'success');

                    break;
                case 'RemovePresale':
                    for ($batchCnt = 0; $batchCnt < count($_REQUEST['server_batch']); $batchCnt++) {
                        $presale_update_sql = "	UPDATE " . TABLE_USER_FAVOURITE_PRODUCTS . "
													SET favourite_products_presale_notice = '0'
													WHERE products_id = '" . tep_db_input($_REQUEST['server_batch'][$batchCnt]) . "'
														AND user_id = '" . tep_db_input($_SESSION['customer_id']) . "'";
                        tep_db_query($presale_update_sql);
                    }
                    $messageStack->add_session($content, SUCCESS_REMOVE_PRESALE, 'success');

                    break;
                case 'RemoveFav':
                    for ($batchCnt = 0; $batchCnt < count($_REQUEST['server_batch']); $batchCnt++) {
                        $favourite_delete_sql = "	DELETE FROM " . TABLE_USER_FAVOURITE_PRODUCTS . "
														WHERE products_id = '" . tep_db_input($_REQUEST['server_batch'][$batchCnt]) . "'
															AND user_id = '" . tep_db_input($_SESSION['customer_id']) . "'";
                        tep_db_query($favourite_delete_sql);
                    }
                    $messageStack->add_session($content, SUCCESS_REMOVE_FAV, 'success');

                    break;
                default:
                    $messageStack->add_session($content, TEXT_ERROR_TRYAGAIN, 'error');

                    break;
            }
        }

        tep_redirect(tep_href_link(FILENAME_MY_FAVOURITE_LINKS));

        break;
    case 'select':
        $favourite_games_arr = array();
        $filter_by_game_arr = array();

        $favourite_games_select_sql = "	SELECT DISTINCT categories_id
										  	FROM " . TABLE_PRODUCTS_TO_CATEGORIES . " AS p2c
										  	INNER JOIN " . TABLE_USER_FAVOURITE_PRODUCTS . " AS f
										  		ON (p2c.products_id = f.products_id AND p2c.products_is_link=0)
										  	WHERE f.user_id = '" . tep_db_input($_SESSION['customer_id']) . "'";
        $favourite_games_result_sql = tep_db_query($favourite_games_select_sql);

        while ($favourite_games_row = tep_db_fetch_array($favourite_games_result_sql)) {
            $buyback_cat_name = tep_get_buyback_main_cat_info($favourite_games_row['categories_id']);

            if (!isset($favourite_games_arr[$buyback_cat_name['id']])) {
                $favourite_games_arr[$buyback_cat_name['id']] = $buyback_cat_name['path'];
                $filter_by_game_arr[] = array('id' => $buyback_cat_name['id'], 'text' => (defined("DISPLAY_NAME_CAT_ID_" . $buyback_cat_name['id']) ? constant("DISPLAY_NAME_CAT_ID_" . $buyback_cat_name['id']) : $buyback_cat_name['text']));
            }
        }

        if (!isset($_SESSION[$form_session_name]['fvl_input_filter_game_select'])) {
            //Always filter by the first game in the list of favourite games.
            reset($favourite_games_arr);
            $_SESSION[$form_session_name]['fvl_input_filter_game_select'] = key($favourite_games_arr);
            $form_values_arr = $_SESSION[$form_session_name];
        }

        $subcategories_array = array((isset($favourite_games_arr[$form_values_arr['fvl_input_filter_game_select']]) ? $form_values_arr['fvl_input_filter_game_select'] : (count($filter_by_game_arr) ? $filter_by_game_arr[0]['id'] : 0)));
        tep_get_subcategories($subcategories_array, (isset($favourite_games_arr[$form_values_arr['fvl_input_filter_game_select']]) ? $form_values_arr['fvl_input_filter_game_select'] : (count($filter_by_game_arr) ? $filter_by_game_arr[0]['id'] : 0)));
        $favourite_products_select_sql = "	SELECT f.products_id, f.favourite_products_presale_notice, p2c.categories_id
											  	FROM " . TABLE_USER_FAVOURITE_PRODUCTS . " AS f
											  	INNER JOIN " . TABLE_PRODUCTS . " AS p
											  		ON f.products_id=p.products_id
											  	INNER JOIN " . TABLE_PRODUCTS_TO_CATEGORIES . " AS p2c
											 		ON (f.products_id=p2c.products_id AND p2c.products_is_link=0)
											  	WHERE f.user_id = '" . tep_db_input($_SESSION['customer_id']) . "'
											  		AND p2c.categories_id IN ('" . implode("', '", $subcategories_array) . "')
											  	ORDER BY p.products_cat_path";

        break;
    case 'select_filter':
        $_SESSION[$form_session_name]['fvl_input_game_select'] = $_SESSION[$form_session_name]['fvl_input_filter_game_select'] = tep_db_prepare_input($_POST['fvl_input_filter_game_select']);
        tep_redirect(tep_href_link(FILENAME_MY_FAVOURITE_LINKS, tep_get_all_get_params(array('action'))));
        break;
}
/**
 * Start preparing add fav form. Save doing this if redirecting on error.
 */
//Game listing
$cat_cfg_array = tep_get_cfg_setting($_SESSION[$form_session_name]['fvl_input_filter_game_select'], 'categories', 'BUYBACK_QUANTITY_UNIT', 'configuration_key');

$col_titles = array();
$col_titles[1] = array('width' => "28%", 'title' => TABLE_HEADING_SERVER);
$col_titles[2] = array('width' => "15%", 'title' => TEXT_QUANTITY . (tep_not_null(trim($cat_cfg_array['BUYBACK_QUANTITY_UNIT'])) ? " <font class='title-text'>(" . $cat_cfg_array['BUYBACK_QUANTITY_UNIT'] . ")</font>" : ""));
$col_titles[3] = array('width' => "10%", 'title' => TABLE_HEADING_STATUS);
$col_titles[4] = array('width' => "11%", 'title' => TEXT_UNIT_PRICE);
$col_titles[5] = array('width' => "11%", 'title' => TABLE_HEADING_ACTION);
$col_titles[6] = array('width' => "7%", 'title' => '&nbsp;');
$col_titles[7] = array('width' => "11%", 'title' => TABLE_HEADING_PRESALE_NOTIFY);

$filter_by_game_id = 0;
$row_count = 0;

if (isset($form_values_arr['fvl_input_filter_game_select'])) {
    $filter_by_game_id = (int) $form_values_arr['fvl_input_filter_game_select'];
}

/**
 * Prepare for display results in div
 */
$favourite_products_result_sql = tep_db_query($favourite_products_select_sql);
$fav_links_num_rows = tep_db_num_rows($favourite_products_result_sql);
if ($fav_links_num_rows) {
    $favLinksHTML = '<table border="0" cellpadding="0" cellspacing="0" width="100%">';
    while ($favourite_products_row = tep_db_fetch_array($favourite_products_result_sql)) {
        $button_linkHTML = '';

        //$row_style = ($row_count%2) ? 'searchListingEven' : 'searchListingOdd';

        $products_id = $favourite_products_row['products_id'];
        $server_cat_id = $favourite_products_row['categories_id'];

        $buyback_cat_name = tep_get_buyback_main_cat_info($favourite_products_row['categories_id']);
        $game_cat_id = $buyback_cat_name['id'];

        $game_name = $favourite_games_arr[$game_cat_id];

        $buybackSupplierObj = new buyback_supplier($game_cat_id, $products_id, true);
        $currencies->set_decimal_places($buybackSupplierObj->_DECIMAL_PLACES_DISPLAY);
        $buybackSupplierObj->calculate_offer_price();

        $product_display_name = tep_display_category_path($buybackSupplierObj->products_arr[$products_id]['cat_path'], $server_cat_id, 'catalog', false);

        $is_buyback = $buybackSupplierObj->products_arr[$products_id]['is_buyback'];
        $avg_offer_price = $buybackSupplierObj->products_arr[$products_id]['avg_offer_price'];
        $buyback_list_status = $buybackSupplierObj->buyback_supplier_settings['ofp_buyback_list_controller_status'];

        if ($buyback_list_status == '0' || (int) $is_buyback) {
            if ($buyback_list_status == '0' || (float) $avg_offer_price <= 0) {
                $button_linkHTML .= '<span class="menu_link"><small>' . TEXT_SELL_THIS_LINK . '</small></span>';
                $status_text = '<span style="color:#999999;">' . TEXT_LIST_CLOSE . '</span>';
            } else if ($is_buyback == '1') { // Game buyback list is closed
                $button_linkHTML .= '<a href="' . tep_href_link(FILENAME_BUYBACK, tep_get_all_get_params(array('action')) . "action=favourites_link&gcat=$game_cat_id&pid=$products_id") . '">' . TEXT_SELL_THIS_LINK . '</a>';
                $status_text = '<span class="navyBlueIndicator">' . TEXT_NORMAL . '</span>';
            } else {
                $button_linkHTML .= '<span class="menu_link"><small>' . TEXT_SELL_THIS_LINK . '</small></span>';
                $status_text = '<span class="redIndicator">' . TEXT_FULL . '</span>';
            }
        } else {
            if ($is_buyback == '1') {
                $button_linkHTML .= '<a href="' . tep_href_link(FILENAME_BUYBACK, tep_get_all_get_params(array('action')) . "action=favourites_link&gcat=$game_cat_id&pid=$products_id") . '">' . TEXT_SELL_THIS_LINK . '</a>';
                $status_text = '<span class="navyBlueIndicator">' . TEXT_NORMAL . '</span>';
            } else {
                $button_linkHTML .= '<span class="menu_link"><small>' . TEXT_SELL_THIS_LINK . '</small></span>';
                $status_text = '<span class="redIndicator">' . TEXT_FULL . '</span>';
            }
        }

        $favLinksHTML .= '<tr height="25">
							<td class="ordersRecords" align="center" valign="top" width="20">' . tep_draw_checkbox_field('server_batch[]', $products_id, false) . '</td>
							<td class="ordersRecords" align="left" width="' . $col_titles[1]['width'] . '">' . $product_display_name . '</td>
							<td class="ordersRecords" align="center" width="' . $col_titles[7]['width'] . '">' . ($favourite_products_row['favourite_products_presale_notice'] == '1' ? tep_image(DIR_WS_ICONS . 'icon_status_green.gif') : tep_image(DIR_WS_ICONS . 'icon_status_red.gif')) . '</td>
							<td class="ordersRecords" align="center" width="' . $col_titles[2]['width'] . '">' . ($buybackSupplierObj->products_arr[$products_id]['is_buyback'] ? $buybackSupplierObj->products_arr[$products_id]['min_qty'] . '-' . $buybackSupplierObj->products_arr[$products_id]['max_qty'] : '') . '</td>
							<td class="ordersRecords" align="center" width="' . $col_titles[3]['width'] . '">' . $status_text . '</td>
							<td class="ordersRecords" align="center" width="' . $col_titles[4]['width'] . '">' . $currencies->format($buybackSupplierObj->products_arr[$products_id]['avg_offer_price'], true, BUYBACK_PRICE_DEFAULT_CURRENCY) . '</td>
							<td class="ordersRecords" align="center" width="' . $col_titles[5]['width'] . '" nowrap>' . $button_linkHTML . '</td>
							<td class="ordersRecords" align="center" width="' . $col_titles[6]['width'] . '">&nbsp;</td>
						  </tr>';
        $row_count++;
    }
    $favLinksHTML .= '</table>';

    if ($row_count < 9) {
        $favLinksHTML = str_replace("##DYNAMIC_WIDTH##", "5%", $favLinksHTML);
    } else {
        $favLinksHTML = str_replace("##DYNAMIC_WIDTH##", "3%", $favLinksHTML);
    }
}

$form_values_arr = $_SESSION[$form_session_name];

$breadcrumb->add(NAVBAR_TITLE, tep_href_link(FILENAME_MY_FAVOURITE_LINKS, '', 'SSL'));

require(DIR_WS_TEMPLATES . TEMPLATENAME_MAIN_PAGE);

require(DIR_WS_INCLUDES . 'application_bottom.php');
?>