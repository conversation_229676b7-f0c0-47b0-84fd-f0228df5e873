<?
/*
  	$Id: my_order_history.php,v 1.39 2014/12/29 08:30:58 weesiong Exp $

	Developer: <PERSON>
  	Copyright (c) 2007 SKC Ventrue

  	Released under the GNU General Public License
*/

require('includes/application_top.php');
tep_redirect(HTTP_SHASSO_PORTAL . '/index.php/site/index/c/MyStoreBuyback/a/orders');
require(DIR_WS_LANGUAGES . $language . '/' . FILENAME_MY_ORDER_HISTORY);
require(DIR_WS_CLASSES . 'log.php');
include_once(DIR_WS_FUNCTIONS . 'supplier.php');

if (!tep_session_is_registered('customer_id')) {
	//$navigation->set_snapshot();
    tep_redirect(tep_href_link(FILENAME_LOGIN, '', 'SSL'));
}

/******************************************************************
	Buyback Order Expiration Cancellation Task
******************************************************************/
include_once(DIR_WS_CLASSES . 'vip_order.php');
include_once(DIR_WS_CLASSES . 'vip_groups.php');
require_once(DIR_WS_FUNCTIONS . 'supplier.php');

define('CNBB_REMARK_MAX_QUEUE_NO', 3);
//define('CNBB_LOGIN_GAME_QUEUE_REMARK_ID', '135');

define('BUYBACK_SCREENSHOT_MAX_SIZE', '512000');

function getStatusContents($isTop=false, $min = 0) {
	global $languages_id;
	$arrayStack = array();

	$temp = array();
	$buyback_status_select_sql = "	SELECT buyback_status_id, buyback_status_name
									FROM ".TABLE_BUYBACK_STATUS."
									WHERE buyback_status_id >='" . (int)$min . "' AND language_id ='" . (int)$languages_id . "'";
	$buyback_status_result_sql = tep_db_query($buyback_status_select_sql);
	while ($buyback_status_row = tep_db_fetch_array($buyback_status_result_sql)) {
		$temp['id'] 	= $buyback_status_row['buyback_status_id'];
		$temp['text']   = $buyback_status_row['buyback_status_name'];
		array_push($arrayStack, $temp);
		$temp = array();
	}

	return $arrayStack;
}

//Define content that gets included in the center column.
//Must match $content . ".tpl.php" in templates/content/
$content = CONTENT_MY_ORDER_HISTORY;
//Define the javascript file
$javascript = 'supplier_xmlhttp.js.php';

//Creating session key based on filename so we know where it got its values.
$form_session_name = FILENAME_MY_ORDER_HISTORY;
$new_session = false;

if (!tep_session_is_registered($form_session_name)) {
	$$form_session_name = array();
	tep_session_register($form_session_name);
	$new_session = true;
}

$form_values_arr = $_SESSION[$form_session_name];

if (isset($_REQUEST['action'])) {
	$action = $_REQUEST['action'];
} else {
	$action = '';
}

$allow_search_num_days_ago = 100;

//$odh_input_order_status_select = '1';
$odh_input_order_status_select = '0';
$odh_input_product_type_select = 'game_currency';
$odh_input_order_no = '';
$odh_input_game_select = 0;

$start_date = $odh_input_start_date = '';

if (isset($_REQUEST['odh_input_start_date']) && tep_not_null($_REQUEST['odh_input_start_date'])) {
	$_REQUEST['odh_input_start_date'] = $start_date = $odh_input_start_date = strstr($odh_input_start_date, ':') ? $_REQUEST['odh_input_start_date'].' 00:00:00' : $_REQUEST['odh_input_start_date'];
} else if (isset($_GET['startdate']) && tep_not_null($_GET['startdate'])) {
	$_REQUEST['odh_input_start_date'] = $start_date = $odh_input_start_date = strstr($odh_input_start_date, ':') ? $_GET['startdate'].' 00:00:00' : $_GET['startdate'];
}

//$end_date = $odh_input_end_date = date('Y-m-d') .' 23:59';
$end_date = $odh_input_end_date = '';

if (isset($_GET['order_status']) && tep_not_null($_GET['order_status'])) {
	$odh_input_order_status_select = $_GET['order_status'];
}

/**
 * Validate the post
**/
$errorCount = 0;
switch ($action) {
	case 'reset_session':
	    unset($_SESSION[$form_session_name]);
	    tep_redirect(tep_href_link(FILENAME_MY_ORDER_HISTORY, tep_get_all_get_params(array('action')) . 'action=search'));

	    break;
	case 'confirm_sent':
		//if (isset($_POST['btn_cancel_'.$buyback_request_group_id])) {	// Cancel this order

		if (isset($_POST['btn_action']) && $_POST['btn_action'] == 'cancel') {
			$buyback_request_group_id = isset($_POST['request_group_id']) ? (int)$_POST['request_group_id'] : 0;
			$buyback_quantity_confirmed = isset($_POST['sent_qty'][$buyback_request_group_id]) ? (int)$_POST['sent_qty'][$buyback_request_group_id] : 0;
			$buyback_request_id = isset($_POST['request_id']) ? (int)$_POST['request_id'] : 0;
			$buyback_comments = isset($_POST['cancel_comment_'.$buyback_request_group_id]) ? htmlspecialchars($_POST['cancel_comment_'.$buyback_request_group_id]) : '';
			if (tep_cancel_buyback_order($buyback_request_group_id, $buyback_comments)) {
				$messageStack->add_session($content, SUCCESS_ORDER_HISTORY_ORDER_CANCEL_SUCCESS, 'success');
			} else {
				$messageStack->add_session($content, ERROR_ORDER_HISTORY_ORDER_CANCEL_FAILED, 'error');
			}
		}

		tep_redirect(tep_href_link(FILENAME_MY_ORDER_HISTORY, tep_get_all_get_params(array('action')) . 'action=search'));

		break;
	case 'search':
	default:
		$action = 'search';
		//Display pending orders on initial load (no post)
		//Assigned here so we know what to render in the switch() below.

		if (isset($_POST['odh_input_order_status_select']) && trim($_POST['odh_input_order_status_select'])) {
			$odh_input_order_status_select = tep_db_prepare_input($_POST['odh_input_order_status_select']);
		}

		if (isset($_REQUEST['odh_input_product_type_select'])) $odh_input_product_type_select = tep_db_prepare_input($_REQUEST['odh_input_product_type_select']);
		if (isset($_REQUEST['odh_input_order_no'])) $odh_input_order_no = tep_db_prepare_input($_REQUEST['odh_input_order_no']);
		if (isset($_REQUEST['odh_input_game_select'])) $odh_input_game_select = tep_db_prepare_input($_REQUEST['odh_input_game_select']);
		$odh_input_start_date = isset($_POST['odh_input_start_date']) ? tep_db_prepare_input($_POST['odh_input_start_date']) : (isset($_GET['odh_input_start_date']) ? tep_db_prepare_input($_GET['odh_input_start_date']) : date('Y-m-d'));
		$odh_input_end_date = isset($_POST['odh_input_end_date']) ? $_POST['odh_input_end_date'] : (isset($_GET['odh_input_end_date']) ? $_GET['odh_input_end_date'] : date('Y-m-d'));

		if (tep_not_null($_GET['odh_input_product_type_select']) && tep_not_null($_SESSION[$form_session_name])) {
			foreach($_SESSION[$form_session_name] as $key => $val) {
				$$key = $val;
			}
		}

		if (tep_not_null($odh_input_start_date)) {
			if (strstr($odh_input_start_date, ':')) {
				//user selected time as well.
				if (!strstr($odh_input_start_date, '00:00:00')) {
					$vipodh_input_start_date = $odh_input_start_date.':00';
				}
			} else {
				//user did not select time
				$odh_input_start_date = $odh_input_start_date.' 00:00:00';
			}
		}

		if (tep_not_null($odh_input_end_date)) {
			if (strstr($odh_input_end_date, ':')) {
				//user selected time as well.
				if (!strstr($odh_input_end_date, '23:59:59')) {
					$odh_input_end_date = $odh_input_end_date.':59';
				}
			} else {
				//user did not select time
				$odh_input_end_date = $odh_input_end_date.' 23:59:59';
			}
		}

		$date_range = 0;
		$start_date_where_sql = $end_date_where_sql = ' 1 ';

		if (tep_not_null($odh_input_start_date) && tep_not_null($odh_input_end_date)) {

			$date_range = tep_day_diff($odh_input_start_date, $odh_input_end_date);

			$start_date_where_sql = " brg.buyback_request_group_date >= '" . tep_db_input($odh_input_start_date) . "'";

			if ($date_range > $allow_search_num_days_ago) {
				$end_date_where_sql =  " brg.buyback_request_group_date <= DATE_ADD('".$odh_input_start_date."', INTERVAL ".$allow_search_num_days_ago." DAY)";
				$odh_input_end_date = $odh_input_end_date = date("Y-m-d 23:59:59", strtotime($odh_input_start_date." +".$allow_search_num_days_ago." day"));
			} else {
				$end_date_where_sql =  " brg.buyback_request_group_date <= '" . tep_db_input($odh_input_end_date) . "'";
			}
		} else {
			if (isset($_REQUEST['odh_input_start_date']) && tep_not_null($_REQUEST['odh_input_start_date'])) {
				$date_range = tep_day_diff($_REQUEST['odh_input_start_date'], date("Y-m-d 23:59:59"));
				$start_date_where_sql = " brg.buyback_request_group_date >= '" . tep_db_input($odh_input_start_date) . "'";

				if ($date_range > $allow_search_num_days_ago) {
					$end_date_where_sql =  " brg.buyback_request_group_date <= DATE_ADD('".$odh_input_start_date."', INTERVAL ".$allow_search_num_days_ago." DAY)";
					$odh_input_end_date = date("Y-m-d 23:59:59", strtotime($odh_input_start_date." +".$allow_search_num_days_ago." day"));
				} else {
					$end_date_where_sql =  " brg.buyback_request_group_date <= '" . date("Y-m-d 23:59:59") . "'";
				}
			} elseif (isset($_REQUEST['odh_input_end_date']) && tep_not_null($_REQUEST['odh_input_end_date'])) {
				$start_date_where_sql = " brg.buyback_request_group_date >= DATE_ADD('".$odh_input_end_date."', INTERVAL -".$allow_search_num_days_ago." DAY)";
				$end_date_where_sql =  " brg.buyback_request_group_date <= '" . tep_db_input($odh_input_end_date) . "'";

				$odh_input_start_date = date("Y-m-d 23:59:59", strtotime($odh_input_end_date." -".$allow_search_num_days_ago." day"));
			}
		}

		break;
}

/**
 * Save form vars to session
 */
if($new_session || $action == 'search'){
	$_SESSION[$form_session_name]['odh_input_order_status_select'] = $odh_input_order_status_select;
	$_SESSION[$form_session_name]['odh_input_product_type_select'] = $odh_input_product_type_select;
	$_SESSION[$form_session_name]['odh_input_order_no'] = $odh_input_order_no;
	$_SESSION[$form_session_name]['odh_input_game_select'] = $odh_input_game_select;
	$_SESSION[$form_session_name]['odh_input_start_date'] = $odh_input_start_date;
	$_SESSION[$form_session_name]['odh_input_end_date'] = $odh_input_end_date;
} else {
	$odh_input_order_status_select = $_SESSION[$form_session_name]['odh_input_order_status_select'];
	$odh_input_product_type_select = $_SESSION[$form_session_name]['odh_input_product_type_select'];
	$odh_input_order_no = $_SESSION[$form_session_name]['odh_input_order_no'];
	$odh_input_game_select = $_SESSION[$form_session_name]['odh_input_game_select'];
	$odh_input_start_date = $_SESSION[$form_session_name]['odh_input_start_date'];
	$odh_input_end_date = $_SESSION[$form_session_name]['odh_input_end_date'];
	$start_date = $odh_input_start_date;
	$end_date = $odh_input_end_date;
}

if ($errorCount > 0) {
	$messageStack->add_session($content, TEXT_ERROR_TRYAGAIN);
	tep_redirect(tep_href_link(FILENAME_MY_ORDER_HISTORY) );
}

/**
 * Start preparing search form. Save doing this if redirecting on error.
 */

$product_type_arr = array(
							array('id' => 'game_currency', 'text' => TEXT_GAME_CURRENCY),
							array('id' => 'hla_product', 'text' => TEXT_HIGH_LEVEL_ACCOUNT));

//Order statuses. Reflects the processing cycle.
$order_status_arr = getStatusContents(true);

//All order status
$default_order_status_array = array(array('id' => '0', 'text' => TEXT_ALL_ORDER_STATUS));
$order_status_arr = array_merge($default_order_status_array, $order_status_arr);

//Game listing
$wbb_game_list_arr = tep_get_game_list_arr(array(array('id' => '0', 'text' => TEXT_ALL_GAMES)));

/**
 * Prepare for display results in div
 */
//Default
$odh_auto_refresh_results_bool = 1;

switch ($action) {
	case 'search':
		//Do the search
		$buyback_request_select_sql = "	SELECT brg.buyback_request_group_id, brg.buyback_request_group_date, brg.buyback_request_group_expiry_date, brg.currency, brg.currency_value, brg.show_restock, brg.buyback_status_id,
											br.orders_products_id, br.buyback_request_id, br.products_id, br.buyback_amount, br.buyback_request_quantity, bs.buyback_status_name, br.buyback_quantity_confirmed, br.restock_character AS saved_restock_character,
											br.buyback_dealing_type, brg.buyback_request_group_served, br.buyback_request_screenshot_before_name, br.buyback_request_screenshot_after_name, p.products_cat_id_path
										FROM ". TABLE_BUYBACK_REQUEST_GROUP . " AS brg
										LEFT JOIN " . TABLE_BUYBACK_REQUEST . " AS br
											ON (brg.buyback_request_group_id = br.buyback_request_group_id)
										INNER JOIN " . TABLE_PRODUCTS . " AS p
											ON (br.products_id = p.products_id)
										INNER JOIN " . TABLE_BUYBACK_STATUS . " AS bs
											ON ( brg.buyback_status_id = bs.buyback_status_id AND bs.language_id = '" . tep_db_input($languages_id) . "')
										WHERE brg.customers_id = '" . tep_db_input($_SESSION['customer_id']) . "'
											AND brg.buyback_request_order_type = '0'
											AND " . $start_date_where_sql . "
											AND " . $end_date_where_sql;

		if ((int)$odh_input_order_status_select != 0) {
		    $buyback_request_select_sql .= " AND brg.buyback_status_id = '" . tep_db_input($odh_input_order_status_select) . "' ";
		}
		if ($odh_input_order_no) {
			$buyback_request_select_sql .= " AND brg.buyback_request_group_id = '" . tep_db_input($odh_input_order_no) . "' ";
		}

		$cat_parent_path = tep_get_categories_parent_path($odh_input_game_select);
		if (tep_not_null($cat_parent_path)) {
			$cat_parent_array = explode("_",$cat_parent_path);
			$top_parent_id = $cat_parent_array[1];

			$cat_parent_path .= $odh_input_game_select."_";
		} else {
			$cat_parent_path = "_".$odh_input_game_select."_";
			$top_parent_id = (int)$odh_input_game_select;
		}

		if ((int)$odh_input_game_select>0) {
			$cat_parent_path = str_replace('_', '\_', $cat_parent_path);
			$buyback_request_select_sql .= " AND p.products_cat_id_path LIKE '".$cat_parent_path."%'";
		}

		$buyback_request_select_sql .= "  GROUP BY brg.buyback_request_group_id
										  ORDER BY brg.buyback_status_id asc, brg.buyback_request_group_date desc";
		$buyback_request_result_sql = tep_db_query($buyback_request_select_sql);
		$buyback_request_num_rows = tep_db_num_rows($buyback_request_result_sql);

		$searchResultsHTML = '';

		$col_titles = array();
		$col_titles[0] = array('width' => 55, 'align' => 'center', 'title' => TABLE_HEADING_ORDER_NUMBER);
		$col_titles[1] = array('width' => 55, 'align' => 'center', 'title' => TABLE_HEADING_DELIVERY_METHOD);
		$col_titles[2] = array('width' => 100, 'align' => 'center', 'title' => TABLE_HEADING_GAME);
		$col_titles[3] = array('width' => 100, 'align' => 'center', 'title' => TABLE_HEADING_SERVER);
		$col_titles[4] = array('width' => 80, 'align' => 'center', 'title' => TABLE_HEADING_RECEIVER_CHAR_NAME);
		$col_titles[5] = array('width' => 50, 'align' => 'center', 'title' => TABLE_HEADING_REQUEST_QTY);
		$col_titles[6] = array('width' => 50, 'align' => 'center', 'title' => TABLE_HEADING_CONFIRM_QUANTITY);
		$col_titles[7] = array('width' => 70, 'align' => 'center', 'title' => TABLE_HEADING_AMOUNT);
		$col_titles[8] = array('width' => 50, 'align' => 'center', 'title' => TABLE_HEADING_STATUS);
		$col_titles[9] = array('width' => 100, 'align' => 'center', 'title' => TABLE_HEADING_ACTION);

		$col_hla_titles = array();
		$col_hla_titles[0] = array('width' => 85, 'align' => 'center', 'title' => TABLE_HEADING_ORDER_NUMBER);
		$col_hla_titles[1] = array('width' => 110, 'align' => 'center', 'title' => TABLE_HEADING_GAME);
		$col_hla_titles[2] = array('width' => 100, 'align' => 'center', 'title' => TABLE_HLA_HEADING_REFERRENCE);
		$col_hla_titles[3] = array('width' => 30, 'align' => 'center', 'title' => TABLE_HLA_HEADING_LEVEL);
		$col_hla_titles[4] = array('width' => 200, 'align' => 'center', 'title' => TABLE_HLA_HEADING_RACE.' / '.TABLE_HLA_HEADING_CLASS);
		//$col_hla_titles[5] = array('width' => 100, 'align' => 'center', 'title' => 'Submited Qty.');
		//$col_hla_titles[6] = array('width' => 80, 'align' => 'center', 'title' => 'Delivered Qty.');
		$col_hla_titles[7] = array('width' => 70, 'align' => 'center', 'title' => TABLE_HEADING_AMOUNT);
		$col_hla_titles[8] = array('width' => 70, 'align' => 'center', 'title' => TABLE_HEADING_STATUS);
		$col_hla_titles[9] = array('width' => 120, 'align' => 'center', 'title' => TABLE_HEADING_ACTION);



		break;
	default:
		break;
}

$num_titles = count($col_titles);

$form_values_arr = $_SESSION[$form_session_name];

$breadcrumb->add(NAVBAR_TITLE, tep_href_link(FILENAME_MY_ORDER_HISTORY, '', 'SSL'));

require(DIR_WS_TEMPLATES . TEMPLATENAME_MAIN_PAGE);

require(DIR_WS_INCLUDES . 'application_bottom.php');
?>