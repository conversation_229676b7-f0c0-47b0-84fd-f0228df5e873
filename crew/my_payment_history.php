<?php
/*
  	$Id: my_payment_history.php,v 1.8 2014/12/29 08:31:27 weesiong Exp $

	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com

  	Copyright (c) 2002 osCommerce

	Released under the GNU General Public License
*/

require('includes/application_top.php');

# redirect to MY ACCOUNT > Seller Payment > Request Payment
tep_redirect(HTTP_SHASSO_PORTAL . '/index.php/site/index/c/WithdrawMoney/a/index');

require(DIR_WS_LANGUAGES . $language . '/' . FILENAME_MY_PAYMENT_HISTORY);

if (!tep_session_is_registered('customer_id')) {
	$navigation->set_snapshot();
    tep_redirect(tep_href_link(FILENAME_LOGIN, '', 'SSL'));
}

require(DIR_WS_CLASSES . 'payment_module_info.php');

$pm_object = new payment_module_info($customer_id, 'customers');


/******************************************************************
	Buyback Order Expiration Cancellation Task
******************************************************************/
include_once(DIR_WS_CLASSES . 'vip_order.php');
include_once(DIR_WS_CLASSES . 'vip_groups.php');
require_once(DIR_WS_FUNCTIONS . 'supplier.php');
//Define content that gets included in the center column.
//Must match $content . ".tpl.php" in templates/content/
$content = CONTENT_MY_PAYMENT_HISTORY;
//Define the javascript file
$javascript = 'supplier_xmlhttp.js.php';

//Creating session key based on filename so we know where it got its values.
$form_session_name = FILENAME_MY_PAYMENT_HISTORY;
if (!tep_session_is_registered($form_session_name)) {
	//tep_session_unregister($form_session_name);
	$$form_session_name = array();
	tep_session_register($form_session_name);
}

$form_values_arr = $_SESSION[$form_session_name];

if (isset($_REQUEST['action'])) {
	$action = $_REQUEST['action'];
} else {
	$action = 'show_report';
}

/**
 * Validate the post
 */
$errorCount = 0;

switch ($action) {
	case 'reset_session':
		//Drop the post vars
		tep_redirect(tep_href_link(FILENAME_MY_PAYMENT_HISTORY, tep_get_all_get_params(array('action')) . 'action=show_report'));
		break;
	case 'show_report':
		$form_submit_arr = (isset($_POST) && $_POST ? $_POST : array('start_date' => date("Y-m-d"), 'end_date' => '', 'payment_id' => '', 'order_id' => '', 'show_records' => '', 'payment_status_id' => ''));
		break;
	case 'show_balance':
		$user_credit_balance = tep_user_balance($_SESSION['customer_id'], 'customers');

		if (count($user_credit_balance) > 0) {
			$form_content .= '<br>';
			$form_content .= '<table border="0" cellpadding="0" cellspacing="0" width="350px">';

			foreach ($user_credit_balance as $bal_currency => $bal_amt) {
				$form_content .= '<tr valign="top" style="height:20px">';
				$form_content .= '<td style="width:50px;text-indent:10px;">'.$currencies->currencies[$bal_currency]['symbol_left'].'&nbsp;</td>';
				$form_content .= '<td style="width:70px">'.number_format($bal_amt, 2, '.', ',').$currencies->currencies[$bal_currency]['symbol_right'].'</td>';
				$form_content .= '<td style="width:100px;text-align:right;"><a href="' . tep_href_link(FILENAME_MY_PAYMENT_HISTORY, tep_get_all_get_params(array('action', 'cur')) . 'action=show_request&cur='.$bal_currency, 'SSL') . '" >'. MY_ACCOUNT_WITHDRAW_MONEY_LINK . '</a></td>';
				$form_content .= '<td style="width:20px;text-align:center;">|</td>';
				$form_content .= '<td style="width:110px;"><a href="' . tep_href_link(FILENAME_MY_PAYMENT_HISTORY, tep_get_all_get_params(array('action', 'cur')) . 'action=show_report', 'SSL') . '" >'.MY_ACCOUNT_WITHDRAW_STATEMENT_LINK.'</a></td>';
				$form_content .= '</tr>';
			}
			$form_content .= '</table><br>';
		}
		break;
	case 'show_request':
		$form_content = $pm_object->show_withdraw_form(FILENAME_MY_PAYMENT_HISTORY, $_REQUEST['cur']);
		break;

	case "confirm_withdraw":
		$action_res_array = $pm_object->confirm_withdraw_form(FILENAME_MY_PAYMENT_HISTORY, $_REQUEST['cur'], $HTTP_POST_VARS, 'display', $messageStack, $content);

		if ($action_res_array['code'] == '-1') {
			tep_redirect(tep_href_link(FILENAME_MY_PAYMENT_HISTORY, tep_get_all_get_params(array('action', 'subaction')). 'action=show_request'));
		} else {
			$form_content = $action_res_array['html'] ;
		}

    	break;
    case "submit_withdraw":
		$action_res_array = $pm_object->confirm_withdraw_form(FILENAME_MY_PAYMENT_HISTORY, $_REQUEST['cur'], $HTTP_POST_VARS, 'submit', $messageStack, $content);

		if ($action_res_array['code'] == '-1') {
			tep_redirect(tep_href_link(FILENAME_MY_PAYMENT_HISTORY, tep_get_all_get_params(array('action', 'subaction')) . 'action=show_request'));
		} else {
			tep_redirect(tep_href_link(FILENAME_MY_PAYMENT_HISTORY, 'action=show_report&pyh_input_start_date='.(date('Y-m-d', mktime(0, 0, 0, date("m")  , date("d")-7, date("Y")))), 'SSL'));
		}

		break;
}
/**
 * Save form vars to session
 */

if ($errorCount > 0) {
	$messageStack->add_session($content, TEXT_ERROR_TRYAGAIN);
	tep_redirect(tep_href_link(FILENAME_MY_PAYMENT_HISTORY) );
}

$form_values_arr = $_SESSION[$form_session_name];

/**
 * Start preparing search form. Save doing this if redirecting on error.
 */
//Payment statuses. Reflects the processing cycle.
//$payment_status_arr = getStatusContents(true);
//$payment_history_num_rows = 0;

/**
 * Prepare for display results in div
 */
switch ($action) {
	case 'show_report':
		$form_content = $pm_object->show_acc_statement(FILENAME_MY_PAYMENT_HISTORY, $form_session_name, $form_submit_arr, $messageStack);
		break;
	default:
		break;
}

$form_values_arr = $_SESSION[$form_session_name];

$breadcrumb->add(NAVBAR_TITLE, tep_href_link(FILENAME_MY_PAYMENT_HISTORY, '', 'SSL'));

require(DIR_WS_TEMPLATES . TEMPLATENAME_MAIN_PAGE);

require(DIR_WS_INCLUDES . 'application_bottom.php');
?>