<?
/*
	$Id: onecard_callback.php,v 1.4 2012/06/28 12:36:29 weichen Exp $
  	Author : 	<PERSON><PERSON> (<EMAIL>)
  	
	Copyright (c) 2005
  	Released under the GNU General Public License
*/

require('includes/application_top.php');

if (!(isset($_REQUEST['voucherCode']) && tep_not_null($_REQUEST['voucherCode']))) {
	tep_redirect(tep_href_link(FILENAME_CUSTOMER_SUPPORT, 'SSL', true, false));
}

$form_data = array();
$data = '';

if (isset($_REQUEST) && count($_REQUEST)) {
	foreach($_REQUEST as $key => $value) {
		$form_data[$key] = $value;
	}
}
$url = tep_href_link(FILENAME_CHECKOUT_PROCESS); 
?>
<html>
	<head></head>
	<body>
<?
		echo "\n".tep_draw_form('onecard_payment_success', $url, 'get');
		
		reset($form_data);
		while (list($key, $value) = each($form_data)) {
			if (!is_array($_REQUEST[$key])) {
				echo tep_draw_hidden_field($key, $value);
			}
		}
?>
		</form>
		<script type="text/javascript" language="JavaScript">
			document.onecard_payment_success.submit();
		</script>
	</body>
</html>