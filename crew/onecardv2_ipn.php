<?php
require_once('includes/application_top.php');

if (isset($_REQUEST) && count($_REQUEST)) {
	include_once(DIR_WS_CLASSES . 'pgs.php');
	include(DIR_WS_LANGUAGES . $language . '/modules/payment/onecardv2.php');
	require_once(DIR_WS_LANGUAGES . $language . '/' . FILENAME_CHECKOUT_PROCESS);
	
	if (isset($_POST['errorCode']) && isset($_POST['errorMessage'])) {
		$onecardv2_payment_history_data_array = array(
			'order_id' => tep_db_prepare_input($_POST['oneCardTransID']),
			'status_code' => tep_db_prepare_input($_POST['errorCode']),
			'status_message' => tep_db_prepare_input($_POST['errorMessage']),
			'hash_key' => tep_db_prepare_input($_POST['hashKey']),
			'last_modified' => 'now()',
			'changed_by' => 'system'
		);
		tep_db_perform(TABLE_ONECARDV2_STATUS_HISTORY, $onecardv2_payment_history_data_array);
		if (pgs::getOrderSiteId($_POST['oneCardTransID']) == '5') {
			tep_redirect(HTTP_PGS_SERVER . '/Payment/index?error_message=' . MODULE_PAYMENT_ONECARDV2_TEXT_ERROR_MESSAGE);
		} else {
			tep_redirect(tep_href_link(FILENAME_CHECKOUT_PAYMENT,'error_message=' . MODULE_PAYMENT_ONECARDV2_TEXT_ERROR_MESSAGE ,'SSL', true, false));
		}
		exit;
	}
	
	if (isset($_POST['OneCard_Code']) && isset($_POST['OneCard_TransID'])) {
		include_once(DIR_WS_CLASSES . 'order.php');
		include_once(DIR_WS_CLASSES . 'payment.php');
		$payment = 'onecardv2';
		$orders_id = tep_db_prepare_input($_POST['OneCard_TransID']);
		$order = new order($orders_id);
		$payment_modules = new payment($payment);
		if (isset($order->info['payment_methods_id']) && !empty($order->info['payment_methods_id'])) {
			$$payment = new $payment($order->info['payment_methods_id']);
		} else {
			$$payment = new $payment();
		}
		$onecardv2Currency = (isset($_POST['currency']) ? $_POST['currency'] : $order->info['currency']);
		$$payment->get_merchant_account($onecardv2Currency);
		$hashkey = md5($$payment->merchantId . $orders_id . $_POST['OneCard_Amount'] . $onecardv2Currency . $_POST['OneCard_RTime'] . $$payment->keyword . $_POST['OneCard_Code']);
		if ($hashkey == $_POST['OneCard_RHashKey']) {
			echo md5($$payment->merchantId . $_POST['OneCard_Code'] . $orders_id . $_POST['OneCard_Amount'] . $_POST['OneCard_Currency'] . $_POST['OneCard_RTime'] . $$payment->transkey);
			if (pgs::getOrderSiteId($orders_id) == '5') {
				pgs::repostToPGS('onecardv2', $_POST);
			} else {
				$onecardv2_data_array = array(
					'code' => isset($_POST['OneCard_Code']) ? tep_db_prepare_input($_POST['OneCard_Code']) : '',
					'amount' => isset($_POST['OneCard_Amount']) ? tep_db_prepare_input($_POST['OneCard_Amount']) : '',
					'currency' => isset($_POST['OneCard_Currency']) ? tep_db_prepare_input($_POST['OneCard_Currency']) : '',
					'last_modified' => isset($_POST['OneCard_RTime']) ? tep_db_prepare_input($_POST['OneCard_RTime']) : '',
					'hash_key' => isset($_POST['OneCard_RHashKey']) ? tep_db_prepare_input($_POST['OneCard_RHashKey']) : '',
					'data' => json_encode($_POST)
				);

				$onecardv2_payment_select_sql = "	SELECT order_id
													FROM " . TABLE_ONECARDV2 . "
													WHERE order_id = '" . $orders_id . "'";
				$onecardv2_payment_result_sql = tep_db_query($onecardv2_payment_select_sql);
				if (tep_db_num_rows($onecardv2_payment_result_sql)) {
					tep_db_perform(TABLE_ONECARDV2, $onecardv2_data_array, 'update', "order_id = '" . $orders_id . "'");
				} else {
					$onecardv2_data_array['order_id'] = $orders_id;
					$onecardv2_data_array['date_added'] = 'now()';
					tep_db_perform(TABLE_ONECARDV2, $onecardv2_data_array);
				}

				$onecardv2_payment_history_data_array = array(
					'order_id' => tep_db_prepare_input($orders_id),
					'status_code' => tep_db_prepare_input($_POST['OneCard_Code']),
					'status_message' => tep_db_prepare_input($_POST['OneCard_Description']),
					'hash_key' => tep_db_prepare_input($_POST['OneCard_RHashKey']),
					'last_modified' => 'now()',
					'changed_by' => 'system'
				);
				tep_db_perform(TABLE_ONECARDV2_STATUS_HISTORY, $onecardv2_payment_history_data_array);
			}
		} else {
			ob_start();
			echo "========================POST==========================<BR>";
			print_r($_POST);
			echo "========================================================<BR>";
			echo "=========================GET==========================<BR>";
			print_r($_GET);
			echo "========================================================<BR>";
			echo "========================SESSION=========================<BR>";
			print_r($_SESSION);
			echo "========================================================<BR>";
			$debug_html = ob_get_contents();
			ob_end_clean();
			@tep_mail('OnecardV2 IPN Report', '<EMAIL>', 'ONECARDV2 IPN INVALID HASHKEY DEBUG E-MAIL', $debug_html, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
		}
	}
}

?>
