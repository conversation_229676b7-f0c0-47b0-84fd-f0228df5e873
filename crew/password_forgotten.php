<?
/*
  	$Id: password_forgotten.php,v 1.8 2009/12/14 02:53:53 weichen Exp $
  	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
  	
  	Copyright (c) 2003 osCommerce
  	
  	Released under the GNU General Public License
*/

require('includes/application_top.php');

if (tep_session_is_registered('customer_id')) {
	tep_redirect(tep_href_link(FILENAME_DEFAULT, '', 'SSL'));
} else {
	tep_redirect(tep_href_link(FILENAME_LOGIN, '', 'SSL'));
}
exit;

require(DIR_WS_LANGUAGES . $language . '/' . FILENAME_PASSWORD_FORGOTTEN);

if (isset($HTTP_GET_VARS['action']) && ($HTTP_GET_VARS['action'] == 'process')) {
	$email_address = tep_db_prepare_input($HTTP_POST_VARS['email_address']);
    
    $check_customer_select_sql = "	SELECT customers_firstname, customers_lastname, customers_password, customers_id, customers_gender, customers_status 
    								FROM " . TABLE_CUSTOMERS . " 
    								WHERE customers_email_address = '" . tep_db_input($email_address) . "'";
    $check_customer_query = tep_db_query($check_customer_select_sql);
    if (tep_db_num_rows($check_customer_query)) {
    	$check_customer_row = tep_db_fetch_array($check_customer_query);
    	
    	if ($check_customer_row['customers_status'] == 1) {
	      	$new_password = tep_create_random_value(ENTRY_PASSWORD_MIN_LENGTH);
	      	$crypted_password = tep_encrypt_password($new_password);
	      	
	      	tep_db_query("update " . TABLE_CUSTOMERS . " set customers_password = '" . tep_db_input($crypted_password) . "' where customers_id = '" . (int)$check_customer_row['customers_id'] . "'");
	      	
	      	$email_greeting = tep_get_email_greeting($check_customer_row["customers_firstname"], $check_customer_row["customers_lastname"], $check_customer_row["customers_gender"]);
	      	$email_text2 = $email_greeting . EMAIL_PASSWORD_REMINDER_BODY . "\n\n" . EMAIL_FOOTER;
		  	tep_mail($check_customer_row['customers_firstname'] . ' ' . $check_customer_row['customers_lastname'], $email_address, implode(' ', array(EMAIL_SUBJECT_PREFIX, EMAIL_PASSWORD_REMINDER_SUBJECT)), sprintf($email_text2, $email_address, $new_password), STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
			
	      	$messageStack->add_session('login', SUCCESS_PASSWORD_SENT, 'success');
			
	      	tep_redirect(tep_href_link(FILENAME_LOGIN, '', 'SSL'));
	    } else {
	    	$messageStack->add('password_forgotten', ERROR_ACCOUNT_INACTIVE);
	    }
    } else {
      	$messageStack->add('password_forgotten', TEXT_NO_EMAIL_ADDRESS_FOUND);
    }
}

$breadcrumb->add(NAVBAR_TITLE_1, tep_href_link(FILENAME_LOGIN, '', 'SSL'));
$breadcrumb->add(NAVBAR_TITLE_2, tep_href_link(FILENAME_PASSWORD_FORGOTTEN, '', 'SSL'));

$content = CONTENT_PASSWORD_FORGOTTEN;

require(DIR_WS_TEMPLATES . TEMPLATENAME_MAIN_PAGE);

require(DIR_WS_INCLUDES . 'application_bottom.php');
?>
