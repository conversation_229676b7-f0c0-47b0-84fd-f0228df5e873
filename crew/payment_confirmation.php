<?php

require("includes/application_top.php");

require(DIR_WS_LANGUAGES . $language . '/' . FILENAME_PAYMENT_CONFIRMATION);
$_SESSION['confirmation_order_id'] = $_SESSION['order_logged'];

if (isset($_SESSION['confirmation_order_id'])) {
if (!tep_session_is_registered('customer_id'))
	$navigation->set_snapshot();

$breadcrumb->add(NAVBAR_TITLE, tep_href_link(FILENAME_PAYMENT_CONFIRMATION));

require(DIR_WS_CLASSES . 'order.php');
$order = new order($_SESSION['confirmation_order_id']);

require(DIR_WS_CLASSES . 'order_total.php'); //ICW ADDED FOR CREDIT CLASS SYSTEM
$order_total_modules = new order_total(); //ICW ADDED FOR CREDIT CLASS SYSTEM

// load all enabled payment modules
require(DIR_WS_CLASSES . 'payment.php');
$payment_methods_select_sql = "	SELECT payment_methods_id
								FROM " . TABLE_ORDERS ."
								WHERE orders_id = '".$_SESSION['confirmation_order_id']."'";
$payment_methods_result_sql = tep_db_query($payment_methods_select_sql);
if ($payment_methods_row = tep_db_fetch_array($payment_methods_result_sql)) {
	$payment_modules = new payment('pm_'.$payment_methods_row['payment_methods_id'].':~:'.$payment.':~:'.$payment_methods_row['payment_methods_id']);
	list($$payment->display_title, $noUse) = explode('<br>', $$payment->display_title);
}

if (isset($_SESSION['customer_id']) && !empty($_SESSION['customer_id'])) {
	if (isset($_COOKIE['ogm']['cp']) && $_COOKIE['ogm']['cp'] != NULL) {
		$reset_cookie = true;
		$key = explode(".", $_COOKIE['ogm']['cp']);
		if (isset($key[1]) && ($key[1] == $_SESSION['customer_id'])) {
			$val = md5($key[0] . $_SESSION['customer_id'] . CHECKOUT_DEVICEPIN_SECRET);
			if ($_COOKIE['ogm']['cp'] == ($key[0] . "." .  $_SESSION['customer_id'] . "." . $val)) {
				$_SESSION['checkout_otp'] = '1';
				$reset_cookie = false;
			} else {
				$_SESSION['checkout_otp'] = '0';
			}
		}
		unset($key);

		$cookie_domain = (($request_type == 'NONSSL') ? HTTP_COOKIE_DOMAIN : HTTPS_COOKIE_DOMAIN);
		$cookie_path = (($request_type == 'NONSSL') ? HTTP_COOKIE_PATH : HTTPS_COOKIE_PATH);
		$cookieLifeTime = time() + (($reset_cookie) ? -1 : (60 * 60 * 24 * 90));
		tep_setcookie('ogm[cp]', $_COOKIE['ogm']['cp'], $cookieLifeTime, $cookie_path, $cookie_domain);
	} else {
		tep_db_query("DELETE FROM " . TABLE_CUSTOMERS_OTP . " WHERE customers_otp_type = 'checkout_device_pin' AND customers_otp_request_date < NOW()");
		$checkout_otp_select_sql = "SELECT customers_otp_digit
									FROM " . TABLE_CUSTOMERS_OTP . "
									WHERE customers_id = " . (int)$_SESSION['customer_id'] . "
									AND customers_otp_type = 'checkout_device_pin'";
		$checkout_otp_result_sql = tep_db_query($checkout_otp_select_sql);
		if ($checkout_otp_row = tep_db_fetch_array($checkout_otp_result_sql)) {
			$pin = $checkout_otp_row['customers_otp_digit'];
		} else {
			$time = time() + 24 * 60 * 60; // valid 24hr
			$pin = sprintf('%06X', mt_rand(0, 16777215));
			$otp_sql_data_array = array(	'customers_id ' => (int)$_SESSION['customer_id'],
											'customers_otp_type' => 'checkout_device_pin',
											'customers_otp_digit' => $pin,
											'customers_otp_request_date' => date("Y-m-d H:i:s", $time));
			tep_db_perform(TABLE_CUSTOMERS_OTP, $otp_sql_data_array);
		}

		$customer_email_select_sql = "	SELECT customers_firstname, customers_lastname, customers_email_address
										FROM " . TABLE_CUSTOMERS . "
										WHERE customers_id = '" . $_SESSION['customer_id'] . "'";
		$customer_email_result_sql = tep_db_query($customer_email_select_sql);
		if ($customer_email_row = tep_db_fetch_array($customer_email_result_sql)) {
			// device code mail
			ob_start();

			echo '<div style="width: 660px;">';
			echo '<div style="border: 1px solid #cecece; border-radius: 5px; padding: 40px; background-color: white; position: relative;">';
			echo sprintf(EMAIL_DEVICE_HEADER, $customer_email_row['customers_firstname'] . ' ' . $customer_email_row['customers_lastname']) . '<br><br>';
			echo sprintf(EMAIL_DEVICE_CONTENT_1, $$payment->display_title) . '<br>';
			echo '<h2>' . $pin . '</h2>';
			echo EMAIL_DEVICE_CONTENT_2 . '<br><br>';
			echo EMAIL_DEVICE_CONTENT_3 . '<br><br>';
			echo EMAIL_FOOTER;
			echo '</div><br><br></div>';
			$mail_html = ob_get_contents();
			ob_end_clean();

			tep_mail($customer_email_row['customers_firstname'] . ' ' . $customer_email_row['customers_lastname'], $customer_email_row['customers_email_address'], implode(' ', array(EMAIL_SUBJECT_PREFIX, EMAIL_DEVICE_TITLE)), $mail_html, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
		}
		$_SESSION['checkout_otp'] = '0';
	}
} else {
	tep_redirect(FILENAME_DEFAULT);
}
$content = CONTENT_PAYMENT_CONFIRMATION;

require(DIR_WS_TEMPLATES . TEMPLATENAME_MAIN_PAGE);
} else {
	tep_redirect(FILENAME_DEFAULT);
}
require(DIR_WS_INCLUDES . 'application_bottom.php');
?>