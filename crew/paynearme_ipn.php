<?

/*
  	$Id: paynearme_ipn.php, v1.0 2010/11/09 13:12:05 dennis <PERSON>p
  	Author : <PERSON> (<EMAIL>)
  	Title: paynearme Payment Module V1.0
	
	Revisions:
		Version 1.0 Initial Payment Module
	
  	Copyright (c) 2007 SKC Venture
	
  	Released under the GNU General Public License
*/

require_once('includes/application_top.php');
include_once(DIR_WS_CLASSES . 'payment.php');
include_once(DIR_WS_CLASSES . 'order.php');
include_once(DIR_WS_CLASSES . 'log.php');
include_once(DIR_WS_LANGUAGES . $language . '/' . FILENAME_CHECKOUT_PROCESS);

if (isset($_REQUEST) && count($_REQUEST)) {
	include_once(DIR_WS_MODULES . 'payment/paynearme/classes/paynearme_ipn_class.php');
	include_once(DIR_WS_LANGUAGES . $language . '/' . FILENAME_CHECKOUT_PROCESS);
	include_once(DIR_WS_CLASSES . 'anti_fraud.php');
	
	$signature_flag = false;
	
	$paynearme_ipn = new paynearme_ipn($_REQUEST);
    
    $orders_id = $paynearme_ipn->get_order_id();
    
    $log_object = new log_files('system');
	
	$payment = 'paynearme';
    $payment_modules = new payment($payment);
	
	$txn_date = '';
	$$payment = new $payment($order->info['payment_methods_id']);
	$check_trans_status_flag = 0;
    
	$paynearme_get_supported_currencies = $$payment->get_support_currencies($$payment->payment_methods_id, false);
	$paynearme_currency = $order->info['currency'];
  	if (!in_array($paynearme_currency, $paynearme_get_supported_currencies)) {
    	$paynearme_currency = $paynearme_get_supported_currencies[0];
  	}
	
	if (tep_not_null($orders_id)) {
		$order = new order($orders_id);
		$payment_modules = new payment($payment);
	    $$payment = new $payment($order->info['payment_methods_id']);
	    $log_object = new log_files('system');
	    $processing_fee = '';
	    $receipt_array = array();
	    
		// Convert Date Format
		if (isset($_REQUEST['timestamp'])) {
			$txn_date = date("Y-m-d H:i:s", $_REQUEST['timestamp']);
		}
		
		if (isset($_REQUEST['payment_date'])) {
			$payment_date = date("Y-m-d H:i:s", $_REQUEST['payment_date']);
		}
		
		if (isset($_REQUEST['pnm_processing_fee'])) {
			$processing_fee = $_REQUEST['pnm_processing_fee'];
		}
		
		$form_status_action_url = $$payment->form_action_url;
		$$payment->get_merchant_account($paynearme_currency);
		
		if (isset($_REQUEST['status'])) {
			$payment_status = $_REQUEST['status'];
			
			if ($payment_status == "pending") { // Pending CallBack ~ Call when customer creates a slip
 				$paynearme_payment_data_array = array('paynearme_pending_amount' => tep_db_prepare_input($_REQUEST['pending_amount']),
													  'paynearme_pending_currency' => tep_db_prepare_input($_REQUEST['pending_currency']),
													  'paynearme_site_order_id' => (int)$_REQUEST['site_order_identifier'],
													  'paynearme_pending_timestamp' => tep_db_prepare_input($txn_date),
													  'paynearme_payment_status' => tep_db_prepare_input($payment_status)
										 		     );
				
				// Validate Signature
   				$pending_request = "pending_amount".$_REQUEST['pending_amount']."pending_currency".$_REQUEST['pending_currency']."pnm_order_identifier".$_REQUEST['pnm_order_identifier']."site_customer_identifier".$_REQUEST['site_customer_identifier']."site_identifier".$_REQUEST['site_identifier']."site_order_identifier".$_REQUEST['site_order_identifier']."status".$payment_status."timestamp".$_REQUEST['timestamp']."version".$_REQUEST['version'];
   				$signature = bin2hex(md5($pending_request.$$payment->paynearme_key, true));
   				
   				if ($signature == $_REQUEST['signature']) {
   					$signature_flag = true;
   				}
   				
			} else { // Confirmation CallBack ~ Call when payment make in 7-eleven
				$paynearme_payment_data_array = array('paynearme_payment_due_to_site_amount' => tep_db_prepare_input($_REQUEST['due_to_site_amount']),
													  'paynearme_payment_due_to_site_currency' => tep_db_prepare_input($_REQUEST['due_to_site_currency']),
													  'paynearme_payment_net_payment_amount' => tep_db_prepare_input($_REQUEST['net_payment_amount']),
											   		  'paynearme_payment_net_payment_currency' => tep_db_prepare_input($_REQUEST['net_payment_currency']),
													  'paynearme_payment_payment_amount' => tep_db_prepare_input($_REQUEST['payment_amount']),
											   		  'paynearme_payment_payment_currency' => tep_db_prepare_input($_REQUEST['payment_currency']),
													  'paynearme_payment_pnm_payment_id' => (int)$_REQUEST['pnm_payment_identifier'],
													  'paynearme_payment_pnm_withheld_amount' => tep_db_prepare_input($_REQUEST['pnm_withheld_amount']),
											   		  'paynearme_payment_pnm_withheld_currency' => $_REQUEST['pnm_withheld_currency'],
											   		  'paynearme_payment_processing_fee_amount' => tep_db_prepare_input($processing_fee),
													  'paynearme_payment_processing_fee_currency' => tep_db_prepare_input($_REQUEST['pnm_processing_fee_currency']),
												      'paynearme_site_order_id' => (int)$_REQUEST['site_order_identifier'],
													  'paynearme_payment_timestamp' => tep_db_prepare_input($txn_date),
													  'paynearme_payment_status' => tep_db_prepare_input($payment_status)
										 		   );
				
				// Validate Signature
				$confirm_request = "due_to_site_amount".$_REQUEST['due_to_site_amount']."due_to_site_currency".$_REQUEST['due_to_site_currency']."net_payment_amount".$_REQUEST['net_payment_amount']."net_payment_currency".$_REQUEST['net_payment_currency']."payment_amount".$_REQUEST['payment_amount']."payment_currency".$_REQUEST['payment_currency']."pnm_order_identifier".$_REQUEST['pnm_order_identifier']."pnm_payment_identifier".$_REQUEST['pnm_payment_identifier']."pnm_processing_fee".$processing_fee."pnm_processing_fee_currency".$_REQUEST['pnm_processing_fee_currency']."pnm_withheld_amount".$_REQUEST['pnm_withheld_amount']."pnm_withheld_currency".$_REQUEST['pnm_withheld_currency']."site_customer_identifier".$_REQUEST['site_customer_identifier']."site_identifier".$_REQUEST['site_identifier']."site_order_identifier".$_REQUEST['site_order_identifier']."status".$payment_status."timestamp".$_REQUEST['timestamp']."version".$_REQUEST['version'];
   				$signature = bin2hex(md5($confirm_request.$$payment->paynearme_key, true));
   				
   				if ($signature == $_REQUEST['signature']){
   					$signature_flag = true;
   				}
			}
		} else { // Authentication CallBack ~ Call when bar code is scanned 
			$payment_status = 'authorization';
			
			$paynearme_payment_data_array = array(	'paynearme_authorization_due_to_site_amount' => tep_db_prepare_input($_REQUEST['due_to_site_amount']),
													'paynearme_authorization_due_to_site_currency' => tep_db_prepare_input($_REQUEST['due_to_site_currency']),
													'paynearme_authorization_net_payment_amount' => tep_db_prepare_input($_REQUEST['net_payment_amount']),
													'paynearme_authorization_net_payment_currency' => tep_db_prepare_input($_REQUEST['net_payment_currency']),
													'paynearme_authorization_payment_amount' => tep_db_prepare_input($_REQUEST['payment_amount']),
													'paynearme_authorization_payment_currency' => tep_db_prepare_input($_REQUEST['payment_currency']),
													'paynearme_authorization_payment_date' => tep_db_prepare_input($payment_date),
													'paynearme_authorization_pnm_payment_id' => (int)$_REQUEST['pnm_payment_identifier'],
													'paynearme_authorization_pnm_withheld_amount' => tep_db_prepare_input($_REQUEST['pnm_withheld_amount']),
													'paynearme_authorization_pnm_withheld_currency' => tep_db_prepare_input($_REQUEST['pnm_withheld_currency']),
													'paynearme_authorization_processing_fee_amount' => tep_db_prepare_input($processing_fee),
													'paynearme_authorization_processing_fee_currency' => tep_db_prepare_input($_REQUEST['pnm_processing_fee_currency']),
													'paynearme_site_order_id' => (int)$_REQUEST['site_order_identifier'],
													'paynearme_authorization_timestamp' => tep_db_prepare_input($txn_date),
													'paynearme_payment_status' => tep_db_prepare_input($payment_status)
												 );
			
			// Validate Signature
			$authentication_request = "due_to_site_amount".$_REQUEST['due_to_site_amount']."due_to_site_currency".$_REQUEST['due_to_site_currency']."net_payment_amount".$_REQUEST['net_payment_amount']."net_payment_currency".$_REQUEST['net_payment_currency']."payment_amount".$_REQUEST['payment_amount']."payment_currency".$_REQUEST['payment_currency']."payment_date".$_REQUEST['payment_date']."pnm_order_identifier".$_REQUEST['pnm_order_identifier']."pnm_payment_identifier".$_REQUEST['pnm_payment_identifier']."pnm_processing_fee".$processing_fee."pnm_processing_fee_currency".$_REQUEST['pnm_processing_fee_currency']."pnm_withheld_amount".$_REQUEST['pnm_withheld_amount']."pnm_withheld_currency".$_REQUEST['pnm_withheld_currency']."site_customer_identifier".$_REQUEST['site_customer_identifier']."site_identifier".$_REQUEST['site_identifier']."site_order_identifier".$_REQUEST['site_order_identifier']."timestamp".$_REQUEST['timestamp']."version".$_REQUEST['version'];
   			$signature = bin2hex(md5($authentication_request.$$payment->paynearme_key, true));

   			if ($signature == $_REQUEST['signature']){
   				$signature_flag = true;
   			}
   			
   			// Get Data Required for Receipt Text
   			$receipt_array = array(	'pnm_payment_identifier' => $_REQUEST['pnm_payment_identifier'],
   									'due_to_site_amount' => $_REQUEST['due_to_site_amount'],
   									'pnm_processing_fee' => $processing_fee,
   									'payment_amount' => $_REQUEST['payment_amount']);
		}
		
		if ($signature_flag) {
			// Merchant ID Checking
		  	if ($paynearme_ipn->validate_receiver_account($$payment->paynearme_id, $payment_status)) {
		  		// Currency & Amount Checking
				if ($paynearme_ipn->validate_transaction_data($$payment, $orders_id, $payment_status)) {
					// Checking with PayNearMe Order
					if ($paynearme_ipn->authenticate($order, $orders_id, $$payment, $payment_status)) {
						// Payment Status
						$paynearme_ipn->xml_response($payment_status, $_REQUEST['pnm_order_identifier'], "Checking_Success", $orders_id, $$payment->version, $receipt_array);
						
						// check order exist
						$paynearme_check_exist_select_sql = " SELECT paynearme_order_id, paynearme_order_status
														 	  FROM " . TABLE_PAYNEARME_ORDER . " 
														 	  WHERE paynearme_order_id  = '".(int)$_REQUEST['site_order_identifier']."'";
						$paynearme_check_exist_result_sql = tep_db_query($paynearme_check_exist_select_sql);
						
						if (tep_db_num_rows($paynearme_check_exist_result_sql)) {
							$paynearme_check_exist_row = tep_db_fetch_array($paynearme_check_exist_result_sql);
							if ($paynearme_check_exist_row['paynearme_order_status'] != $payment_status) {
								$paynearme_order_data_array = array('paynearme_pnm_order_id' => tep_db_prepare_input($_REQUEST['pnm_order_identifier']),
																	'paynearme_site_customer_id' => (int)$_REQUEST['site_customer_identifier'],
																	'paynearme_site_id' => tep_db_prepare_input($_REQUEST['site_identifier']),
																	'paynearme_order_id' => (int)$_REQUEST['site_order_identifier']
														 		  );
								tep_db_perform(TABLE_PAYNEARME_ORDER, $paynearme_order_data_array, 'update', ' paynearme_order_id = "'.(int)$_REQUEST['site_order_identifier'].'" ');
								tep_db_perform(TABLE_PAYNEARME_PAYMENT_INFO, $paynearme_payment_data_array, 'update', ' paynearme_site_order_id = "'.(int)$_REQUEST['site_order_identifier'].'" ');
							}
						} else { // Order Not Exist
							$paynearme_payment_history_data_array = array(	'paynearme_order_id' => (int)$_REQUEST['site_order_identifier'], 
																	  		'paynearme_date' => 'now()',
																	  		'paynearme_status' => tep_db_prepare_input($payment_status),
																 			'paynearme_description' => tep_db_prepare_input('IPN Error: Order ID ('.trim($_REQUEST['site_order_identifier']).') not found.'),
															  				'paynearme_changed_by' => 'system'
											                   			);			                   			
							tep_db_perform(TABLE_PAYNEARME_STATUS_HISTORY, $paynearme_payment_history_data_array);
						}
					} else { // Not Payment Status
						$paynearme_ipn->xml_response($payment_status, $_REQUEST['pnm_order_identifier'], "Checking_Fail", $orders_id, $$payment->version, $receipt_array);
					}
				} else { // Invalid Currency or Amount
					$paynearme_ipn->xml_response($payment_status, $_REQUEST['pnm_order_identifier'], "Invalid_Currency_Amount", $orders_id, $$payment->version, $receipt_array);
				}
		  	} else { // Invalid Merchant ID
		  		$paynearme_ipn->xml_response($payment_status, $_REQUEST['pnm_order_identifier'], "Invalid_Merchant_ID", $orders_id, $$payment->version, $receipt_array);
			}
		} else {
			$paynearme_payment_history_data_array = array(	'paynearme_order_id' => (int)$_REQUEST['site_order_identifier'],
												  			'paynearme_date' => 'now()',
												  			'paynearme_status' => tep_db_prepare_input($payment_status),
											 				'paynearme_description' => tep_db_prepare_input('Error: Signature Not Match (' . $signature  . ' != ' . trim($_REQUEST['signature']).')'),
										  					'paynearme_changed_by' => 'system'
						                   				);	      
			tep_db_perform(TABLE_PAYNEARME_STATUS_HISTORY, $paynearme_payment_history_data_array);
		}
	}
}
?>