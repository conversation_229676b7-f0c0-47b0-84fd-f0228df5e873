<?php

require_once('includes/application_top.php');

require_once(DIR_WS_MODULES . 'payment/paypalEC/classes/paypalECIpnClass.php');
require_once(DIR_WS_LANGUAGES . $language . '/' . FILENAME_CHECKOUT_PROCESS);
require_once(DIR_WS_MODULES . 'payment/paypalEC/languages/english/paypalec_ipn_lng.php');

include_once(DIR_FS_ADMIN . 'includes/classes/c2c_invoice.php');

if (isset($_REQUEST) && count($_REQUEST)) {
	include_once(DIR_WS_CLASSES . 'order.php');
	include_once(DIR_WS_CLASSES . 'anti_fraud.php');
	include_once(DIR_WS_CLASSES . 'log.php');

	$payment = 'paypalEC';
	include_once(DIR_WS_CLASSES . 'payment.php');

	$paypalIpn = new paypalECIpnClass($_REQUEST);
	$orders_id = $paypalIpn->get_order_id();
	$order = new order($orders_id);
	$payment_modules = new payment($payment);
	if (isset($order->info['payment_methods_id']) && !empty($order->info['payment_methods_id'])) {
		$$payment = new $payment($order->info['payment_methods_id']);
	} else {
		$$payment = new $payment();
	}
	$paypalCurrency = (isset($paypalIpn->key['mc_currency']) ? $paypalIpn->key['mc_currency'] : $paypalIpn->key['mp_currency']);
	$$payment->get_merchant_account($paypalCurrency);
	$log_object = new log_files('system');
	
	if (isset($paypalIpn->key['txn_type'])) {
		switch ($paypalIpn->key['txn_type']) {
			//paypal ec checkout
			case 'express_checkout':
			//reference transaction checkout
			case 'merch_pmt':
				$paypalec_data_array = array(
					'txn_type' => isset($paypalIpn->key['txn_type']) ? $paypalIpn->key['txn_type'] : '',
					'payment_type' => isset($paypalIpn->key['payment_type']) ? $paypalIpn->key['payment_type'] : '',
					'payment_status' => isset($paypalIpn->key['payment_status']) ? $paypalIpn->key['payment_status'] : '',
					'pending_reason' => isset($paypalIpn->key['pending_reason']) ? $paypalIpn->key['pending_reason'] : '',
					'mc_currency' => isset($paypalIpn->key['mc_currency']) ? $paypalIpn->key['mc_currency'] : '',
					'first_name' => isset($paypalIpn->key['first_name']) ? $paypalIpn->key['first_name'] : '',
					'last_name' => isset($paypalIpn->key['last_name']) ? $paypalIpn->key['last_name'] : '',
//					'address_name' => isset($paypalIpn->key['address_name']) ? $paypalIpn->key['address_name'] : '',
//					'address_street' => isset($paypalIpn->key['address_street']) ? $paypalIpn->key['address_street'] : '',
//					'address_city' => isset($paypalIpn->key['address_city']) ? $paypalIpn->key['address_city'] : '',
//					'address_state' => isset($paypalIpn->key['address_state']) ? $paypalIpn->key['address_state'] : '',
//					'address_zip' => isset($paypalIpn->key['address_zip']) ? $paypalIpn->key['address_zip'] : '',
//					'address_country' => isset($paypalIpn->key['address_country']) ? $paypalIpn->key['address_country'] : '',
//					'address_status' => isset($paypalIpn->key['address_status']) ? $paypalIpn->key['address_status'] : '',
					'payer_email' => isset($paypalIpn->key['payer_email']) ? $paypalIpn->key['payer_email'] : '',
					'payer_id' => isset($paypalIpn->key['payer_id']) ? $paypalIpn->key['payer_id'] : '',
					'payer_status' => isset($paypalIpn->key['payer_status']) ? $paypalIpn->key['payer_status'] : '',
					'payment_date' => isset($paypalIpn->key['payment_date']) ? $paypalIpn->key['payment_date'] : '',
					'business' => isset($paypalIpn->key['business']) ? $paypalIpn->key['business'] : '',
					'receiver_email' => isset($paypalIpn->key['receiver_email']) ? $paypalIpn->key['receiver_email'] : '',
					'receiver_id' => isset($paypalIpn->key['receiver_id']) ? $paypalIpn->key['receiver_id'] : '',
					'mc_gross' => isset($paypalIpn->key['mc_gross']) ? $paypalIpn->key['mc_gross'] : '',
					'mc_fee' => isset($paypalIpn->key['mc_fee']) ? $paypalIpn->key['mc_fee'] : '',
					'payment_gross' => isset($paypalIpn->key['payment_gross']) ? $paypalIpn->key['payment_gross'] : '',
					'payment_fee' => isset($paypalIpn->key['payment_fee']) ? $paypalIpn->key['payment_fee'] : '',
					'notify_version' => isset($paypalIpn->key['notify_version']) ? $paypalIpn->key['notify_version'] : '',
					'verify_sign' => isset($paypalIpn->key['verify_sign']) ? $paypalIpn->key['verify_sign'] : '',
					'residence_country' => isset($paypalIpn->key['residence_country']) ? $paypalIpn->key['residence_country'] : '',
					'protection_eligibility' => isset($paypalIpn->key['protection_eligibility']) ? $paypalIpn->key['protection_eligibility'] : '',
					'txn_id' => isset($paypalIpn->key['txn_id']) ? $paypalIpn->key['txn_id'] : '',
					'last_modified' => 'now()',
					'data' => json_encode($paypalIpn->key)
				);

				$paypalec_payment_select_sql = "	SELECT paypal_order_id
													FROM " . TABLE_PAYPALEC . "
													WHERE paypal_order_id = '" . (int) $orders_id . "'";
				$paypalec_payment_result_sql = tep_db_query($paypalec_payment_select_sql);
				if (tep_db_num_rows($paypalec_payment_result_sql)) {
					tep_db_perform(TABLE_PAYPALEC, $paypalec_data_array, 'update', "paypal_order_id = '" . (int) $orders_id . "'");
				} else {
					$paypalec_data_array['paypal_order_id'] = $orders_id;
					$paypalec_data_array['date_added'] = 'now()';
					tep_db_perform(TABLE_PAYPALEC, $paypalec_data_array);
				}

				//Paypal Email verify
				$customer_paypal_info_sql = "	SELECT c.customers_gender, c.customers_firstname, c.customers_lastname, c.customers_email_address, ord.customers_id, ord.customers_name, ord.currency, p.payer_email, ot.text, ord.orders_id
												FROM " . TABLE_ORDERS . " AS ord
												INNER JOIN " . TABLE_ORDERS_TOTAL . " AS ot
													ON (ot.orders_id = ord.orders_id AND ot.class = 'ot_total')
												INNER JOIN " . TABLE_PAYPALEC . " AS p
													ON (p.paypal_order_id = ord.orders_id)
												LEFT JOIN " . TABLE_CUSTOMERS . " As c
													ON (c.customers_id = ord.customers_id)
												WHERE ord.orders_id ='" . (int) $orders_id . "'";
				$customer_paypal_info_result_sql = tep_db_query($customer_paypal_info_sql);
				$customer_paypal_info_row = tep_db_fetch_array($customer_paypal_info_result_sql);

				$customer_paypal_verify_sql = "	SELECT serial_number, info_verified 
												FROM " . TABLE_CUSTOMERS_INFO_VERIFICATION . " 
												WHERE customers_info_value = '" . $customer_paypal_info_row['payer_email'] . "' 
													AND customers_id = '" . (int) $customer_paypal_info_row['customers_id'] . "' 
													AND info_verification_type = 'email'";
				$customer_paypal_verify_result_sql = tep_db_query($customer_paypal_verify_sql);
				$customer_paypal_verify_row = tep_db_fetch_array($customer_paypal_verify_result_sql);

				$send_verification_mail = false;
				if (tep_db_num_rows($customer_paypal_verify_result_sql) > 0) {
					if ($customer_paypal_verify_row['info_verified'] == 0) {
						if (tep_not_null($customer_paypal_verify_row['serial_number'])) {
							$serial_number = $customer_paypal_verify_row['serial_number'];
						} else {
							$serial_number = tep_gen_random_serial($customer_paypal_info_row['payer_email'], TABLE_CUSTOMERS_INFO_VERIFICATION, 'serial_number', '', 12);
							tep_db_query("update " . TABLE_CUSTOMERS_INFO_VERIFICATION . " set serial_number = '" . $serial_number . "' where customers_info_value = '" . tep_db_input($customer_paypal_info_row['payer_email']) . "' and customers_id ='" . (int) $customer_paypal_info_row['customers_id'] . "'");
						}
						$send_verification_mail = true;
					}
				} else if (tep_db_num_rows($customer_paypal_verify_result_sql) < 1) {
					$serial_number = substr(tep_gen_random_serial($customer_paypal_info_row['payer_email'], TABLE_CUSTOMERS, "serial_number"), 0, 12);

					$sql_data_array = array('customers_id ' => (int) $customer_paypal_info_row['customers_id'],
						'customers_info_value' => tep_db_input($customer_paypal_info_row['payer_email']),
						'serial_number' => $serial_number,
						'info_verified' => 0,
						'info_verification_type' => 'email');

					tep_db_perform(TABLE_CUSTOMERS_INFO_VERIFICATION, $sql_data_array);
					$send_verification_mail = true;
				}
				if ($send_verification_mail == true) {
					$activate_address = tep_href_link(FILENAME_ACCOUNT_ACTIVATE, 'serial=' . $serial_number . '&email=' . urlencode($customer_paypal_info_row['payer_email']) . '&action=verify_email');
					$link = '<a href="' . $activate_address . '" target="_blank">' . $activate_address . '</a><br>';

					$customer_profile_select_sql = "SELECT customers_gender, customers_firstname, customers_lastname 
													FROM " . TABLE_CUSTOMERS . " 
													WHERE customers_id = '" . (int) $order->customer['id'] . "'";
					$customer_profile_result_sql = tep_db_query($customer_profile_select_sql);
					if ($customer_profile_row = tep_db_fetch_array($customer_profile_result_sql)) {
						$email_firstname = $customer_profile_row['customers_firstname'];
						$email_lastname = $customer_profile_row['customers_lastname'];
					} else {
						$email_firstname = $customer_paypal_info_row['customers_name'];
						$email_lastname = '';
					}

					$email_greeting = tep_get_email_greeting($email_firstname, $email_lastname, $customer_profile_result_sql['customers_gender']);

					$email_text = $email_greeting .
						TEXT_PAYPAL_PAYMENT_MAKE_EMAIL_CONTENT1 .
						$customer_paypal_info_row['text'] .
						TEXT_PAYPAL_PAYMENT_MAKE_EMAIL_CONTENT2 .
						'  ' . $customer_paypal_info_row['payer_email'] .
						TEXT_PAYPAL_EMAIL_VERIFY_INSTRUCTION_CONTENT .
						$link .
						TEXT_PAYPAL_EMAIL_VERIFY_ENDING_CONTENT . "\n\n\n" . EMAIL_CONTACT . EMAIL_FOOTER;

					@tep_mail($customer_paypal_info_row['customers_name'], $customer_paypal_info_row['payer_email'], implode(' ', array(EMAIL_SUBJECT_PREFIX, TEXT_PAYPAL_VERIFICATION_TITLE)), $email_text, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);

					if ($customer_paypal_info_row['payer_email'] != $customer_paypal_info_row['customers_email_address']) {
						$profile_email_text = $email_greeting .
							TEXT_PAYPAL_PAYMENT_MAKE_EMAIL_CONTENT1 .
							$customer_paypal_verify_row['currency'] . $customer_paypal_verify_row['text'] .
							TEXT_PAYPAL_PAYMENT_MAKE_EMAIL_CONTENT2 .
							'  ' . $customer_paypal_info_row['payer_email'] .
							TEXT_PAYPAL_EMAIL_VERIFICATION_NOTICE_HEADING_CONTENT .
							EMAIL_SUBJECT_PREFIX . TEXT_PAYPAL_VERIFICATION_TITLE .
							TEXT_PAYPAL_EMAIL_VERIFICATION_NOTICE_ENDING_CONTENT .
							"\n\n\n" . EMAIL_CONTACT . EMAIL_FOOTER;

						$name = $customer_paypal_verify_row['customers_firstname'] . ' ' . $customer_paypal_verify_row['customers_lastname'];
						@tep_mail($name, $customer_paypal_info_row['customers_email_address'], implode(' ', array(EMAIL_SUBJECT_PREFIX, TEXT_NOTICE_OF_PAYPAL_VERIFICATION_SEND_TITLE)), $profile_email_text, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
					}

					$sql_data_array = array('orders_id' => (int) $orders_id,
						'orders_status_id' => 0,
						'date_added' => 'now()',
						'customer_notified' => 1,
						'comments' => REMARK_PAYPAL_VERIFICATION_EMAIL_SENT . ($customer_paypal_info_row['payer_email'] != $customer_paypal_info_row['customers_email_address'] ? ' ' . REMARK_PAYPAL_VERIFICATION_EMAIL_NOTICE_SENT : '')
					);

					tep_db_perform(TABLE_ORDERS_STATUS_HISTORY, $sql_data_array);
				}

				$paypalec_payment_history_data_array = array(
					'paypal_order_id' => $paypalIpn->get_order_id(),
					'payment_status' => $paypalIpn->key['payment_status'],
					'reason' => isset($paypalIpn->key['reason_code']) ? $paypalIpn->key['reason_code'] : '',
					'date' => 'now()',
					'changed_by' => 'system'
				);
				tep_db_perform(TABLE_PAYPALEC_STATUS_HISTORY, $paypalec_payment_history_data_array);

				//order verification
				if ((int) $orders_id > 0) {
					if ((!isset($paypalIpn->key['test_ipn']) || $paypalIpn->key['test_ipn'] != '1') || $$payment->test_mode == 'True') {
						if ($paypalIpn->validate_receiver_account($$payment->receiverEmail)) {
							if ($paypalIpn->authenticate($$payment->ipn_validate_url)) {
								if (!is_object($order))
									$order = new order($orders_id);
								if ($paypalIpn->validate_payment_amount($order)) {
									if ($paypalIpn->key['payment_status'] == 'Completed') {
										if ($order->info['orders_status_id'] == $$payment->order_status) { // If the order still in initial status
											if (isset($paypalIpn->key['mp_id']) && !empty($paypalIpn->key['mp_id'])) {
												$rederence_transaction_history_data_array = array(
													'billing_id' => $paypalIpn->key['mp_id'],
													'customer_id' => $order->customer['id'],
													'order_id' => $orders_id,
													'date' => 'now()',
													'status' => 'Purchase Success',
													'data' => json_encode($paypalIpn->key)
												);
												tep_db_perform(TABLE_PAYPALEC_REFERENCE_TRANSACTION_HISTORY, $rederence_transaction_history_data_array);
											}
											if (!isset($paypalIpn->key['mp_status']) || $paypalIpn->key['mp_status'] == 0) {
												$paypalIpn->process_this_order($orders_id, $$payment, $$payment->order_processing_status);
											}
										}
									} else if ($paypalIpn->key['payment_status'] == 'Pending' && isset($paypalIpn->key['pending_reason']) && $paypalIpn->key['pending_reason'] == 'paymentreview') {
										$customer_profile_select_sql = "SELECT c.customers_gender, c.customers_firstname, c.customers_lastname, ord.customers_name, pay.payer_email
																		FROM " . TABLE_ORDERS . " AS ord
																		INNER JOIN " . TABLE_PAYPALEC . " AS pay
																			ON (p.paypal_order_id = ord.orders_id)
																		LEFT JOIN " . TABLE_CUSTOMERS . " AS c
																			ON (c.customers_id = ord.customers_id)
																		WHERE ord.orders_id = '" . (int) $orders_id . "'";
										$customer_profile_result_sql = tep_db_query($customer_profile_select_sql);
										if ($customer_profile_row = tep_db_fetch_array($customer_profile_result_sql)) {
											$email_firstname = $customer_profile_row['customers_firstname'];
											$email_lastname = $customer_profile_row['customers_lastname'];
										} else {
											$email_firstname = $customer_paypal_info_row['customers_name'];
											$email_lastname = '';
										}

										$paypal_email_greeting = tep_get_email_greeting($email_firstname, $email_lastname, $customer_profile_result_sql['customers_gender']);
										$paypal_email_text = $paypal_email_greeting . "\n\n" . EMAIL_PAYPAL_PAYMENT_REVIEW . "\n\n\n" . EMAIL_FOOTER;

										$orders_status_history_data_array = array('orders_id' => $orders_id,
											'orders_status_id' => 0,
											'date_added' => 'now()',
											'customer_notified' => 1,
											'comments' => 'Payment review notification email sent',
											'changed_by' => 'system'
										);
										tep_db_perform(TABLE_ORDERS_STATUS_HISTORY, $orders_status_history_data_array);

										@tep_mail($email_firstname . ' ' . $email_lastname, $customer_profile_row['payer_email'], implode(' ', array(EMAIL_SUBJECT_PREFIX, EMAIL_PAYPAL_PAYMENT_REVIEW_SUBJECT)), $paypal_email_text, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
									}
								}
							}
						} 
					}
				}
				break;
			//reference transaction signup
			case 'mp_signup':
				if ($paypalIpn->key['mp_status'] == 0) {
					$customer_id = '';
					$customer_id_select_sql = "	SELECT customer_id 
												FROM " . TABLE_PAYPALEC_REFERENCE_TRANSACTION . " 
												WHERE billing_id = '" . $paypalIpn->key['mp_id'] . "'
													AND merchant_email = '". $$payment->receiverEmail ."'";
					$customer_id_result_sql = tep_db_query($customer_id_select_sql);
					if ($customer_id_row = tep_db_fetch_array($customer_id_result_sql)) {
						$customer_id = $customer_id_row['customer_id'];
					} 
					$rederence_transaction_history_data_array = array(
						'billing_id' => $paypalIpn->key['mp_id'],
						'customer_id' => $customer_id,
						'order_id' => $orders_id,
						'date' => 'now()',
						'status' => 'Agreement SignUp - Reason Code : ' . $paypalIpn->key['reason_code'],
						'data' => json_encode($paypalIpn->key)
					);
					tep_db_perform(TABLE_PAYPALEC_REFERENCE_TRANSACTION_HISTORY, $rederence_transaction_history_data_array);
				}
				break;
			//reference transaction agreement termination
			case 'mp_cancel':
				if ($paypalIpn->key['mp_status'] == 1) {
					$customer_id = '';
					$customer_id_select_sql = "	SELECT customer_id 
												FROM " . TABLE_PAYPALEC_REFERENCE_TRANSACTION . " 
												WHERE billing_id = '" . $paypalIpn->key['mp_id'] . "'
													AND merchant_email = '". $$payment->receiverEmail ."'";
					$customer_id_result_sql = tep_db_query($customer_id_select_sql);
					if ($customer_id_row = tep_db_fetch_array($customer_id_result_sql)) {
						$customer_id = $customer_id_row['customer_id'];
					} 
					tep_db_query("DELETE FROM " . TABLE_PAYPALEC_REFERENCE_TRANSACTION . " WHERE billing_id = '" . $paypalIpn->key['mp_id'] . "' AND customer_id = '" . $customer_id . "' AND merchant_email = '" . $$payment->receiverEmail . "'");
					$rederence_transaction_history_data_array = array(
						'billing_id' => $paypalIpn->key['mp_id'],
						'customer_id' => $customer_id,
						'order_id' => $orders_id,
						'date' => 'now()',
						'status' => 'Agreement Terminated - Reason Code : ' . $paypalIpn->key['reason_code'],
						'data' => json_encode($paypalIpn->key)
					);
					tep_db_perform(TABLE_PAYPALEC_REFERENCE_TRANSACTION_HISTORY, $rederence_transaction_history_data_array);
				}
				break;
			//Payment sent using Mass Pay
			case 'masspay':
				$currencies = new currencies();
				$num_of_order = 0;

				foreach ($paypalIpn->key as $ipn_key_loop => $ipn_key_data) {
					if (preg_match("/status_[0-9]+$/i", $ipn_key_loop)) $num_of_order++;
				}

				for ($count_order=1;$count_order<=$num_of_order;$count_order++) {
					$returned_masspay_txn_id = (isset($paypalIpn->key['masspay_txn_id_'.$count_order])?$paypalIpn->key['masspay_txn_id_'.$count_order]:'');
					$returned_mc_currency = (isset($paypalIpn->key['mc_currency_'.$count_order])?$paypalIpn->key['mc_currency_'.$count_order]:'');
					$returned_mc_gross = (isset($paypalIpn->key['mc_gross_'.$count_order])?$paypalIpn->key['mc_gross_'.$count_order]:'');
					$returned_mc_fee = (isset($paypalIpn->key['mc_fee_'.$count_order])?$paypalIpn->key['mc_fee_'.$count_order]:'');
					$returned_mc_handling = (isset($paypalIpn->key['mc_handling'.$count_order])?$paypalIpn->key['mc_handling'.$count_order]:'');
					$returned_receiver_email = (isset($paypalIpn->key['receiver_email_'.$count_order])?$paypalIpn->key['receiver_email_'.$count_order]:'');
					$returned_status = (isset($paypalIpn->key['status_'.$count_order])?$paypalIpn->key['status_'.$count_order]:'');
					$returned_unique_id = (isset($paypalIpn->key['unique_id_'.$count_order])?$paypalIpn->key['unique_id_'.$count_order]:'');

					$comments_log_str = "<u>MassPay IPN</u>\n";
					$comments_log_str .= "MassPay Transaction ID: ".$returned_masspay_txn_id."\n";
					$comments_log_str .= "Currency: ".$returned_mc_currency."\n";
					$comments_log_str .= "Gross: ".$returned_mc_gross."\n";
					$comments_log_str .= "Fee: ".$returned_mc_fee."\n";
					$comments_log_str .= "Handling: ".$returned_mc_handling."\n";
					$comments_log_str .= "Receiver Email: ".$returned_receiver_email."\n";
					$comments_log_str .= "Status: ".$returned_status."\n\n";

					$store_payments_select_sql = "	SELECT store_payments_id, store_payments_request_amount, store_payments_methods_id,
														store_payments_fees, store_payments_request_currency,
														store_payments_status, user_firstname, user_lastname, user_email_address
													FROM " . TABLE_STORE_PAYMENTS . "
													WHERE store_payments_id = '".tep_db_input($returned_unique_id)."'";
					$store_payments_result_sql = tep_db_query($store_payments_select_sql);
					if ($store_payments_row = tep_db_fetch_array($store_payments_result_sql)) {
						$return_paypal_gross_amt = number_format($returned_mc_gross, $currencies->currencies[$returned_mc_currency]['decimal_places'], $currencies->currencies[$returned_mc_currency]['decimal_point'], $currencies->currencies[$returned_mc_currency]['thousands_point']);
						$paypal_gross_amt = number_format($store_payments_row['store_payments_request_amount'], $currencies->currencies[$store_payments_row['store_payments_request_currency']]['decimal_places'], $currencies->currencies[$store_payments_row['store_payments_request_currency']]['decimal_point'], $currencies->currencies[$store_payments_row['store_payments_request_currency']]['thousands_point']);

						if ( $currencies->currencies[$returned_mc_currency]['symbol_left'].$return_paypal_gross_amt.$currencies->currencies[$returned_mc_currency]['symbol_right'] != $currencies->currencies[$paypal_gross_amt]['symbol_left'].$paypal_gross_amt.$currencies->currencies[$paypal_gross_amt]['symbol_right']) {
							$store_payments_array = array();
							$store_payments_details_select_sql = "	SELECT spd.payment_methods_fields_value, pmf.payment_methods_fields_system_type
																	FROM " . TABLE_PAYMENT_METHODS_FIELDS . " as pmf
																	INNER JOIN " . TABLE_STORE_PAYMENTS_DETAILS . " as spd
																		ON pmf.payment_methods_fields_id = spd.payment_methods_fields_id
																	WHERE store_payments_id = '".$store_payments_row['store_payments_id']."'";
							$store_payments_details_result_sql = tep_db_query($store_payments_details_select_sql);
							while ($store_payments_details_row = tep_db_fetch_array($store_payments_details_result_sql)) {
								$store_payments_array[$store_payments_details_row['payment_methods_fields_system_type']] = $store_payments_details_row['payment_methods_fields_value'];
							}

							if ($store_payments_array['MODULE_PAYPALEC_SEND_EMAIL'] == $returned_receiver_email) {
								if (strtolower($returned_status) == 'completed') {
									if ($store_payments_row['store_payments_status']!=3) {
									$comments_log_str .= "<u>Result</u>\n";
									$comments_log_str .= "Send Mass Payment Completed\nStatus: Completed.\nReference: " . $returned_masspay_txn_id;

									$store_payments_data_sql = array( 	'store_payments_reference' => tep_db_prepare_input("Reference: " . $returned_masspay_txn_id),
																		'store_payments_last_modified' => 'now()',
																		'store_payments_paid_currency' => tep_db_prepare_input($returned_mc_currency),
																		'store_payments_paid_amount' => tep_db_prepare_input($returned_mc_gross),
																		'store_payments_status' => 3);
									tep_db_perform(TABLE_STORE_PAYMENTS, $store_payments_data_sql, 'update', " store_payments_id = '" . $store_payments_row['store_payments_id'] . "' ");

									$store_payments_history_data_sql = array( 	'store_payments_id' => $store_payments_row['store_payments_id'],
																				'store_payments_status' => '3',
																				'date_added' => 'now()',
																				'payee_notified' => 0,
																				'comments' => tep_db_prepare_input($comments_log_str),
																				'changed_by' => tep_db_prepare_input('system'),
																				'changed_by_role' => tep_db_prepare_input('system'));
									tep_db_perform(TABLE_STORE_PAYMENTS_HISTORY, $store_payments_history_data_sql);

									$payment_received_by_str = '';
									$estimated_payment_receive_time_select_sql = "	SELECT payment_methods_estimated_receive_period
																					FROM " . TABLE_PAYMENT_METHODS . "
																					WHERE payment_methods_id = '".tep_db_input($store_payments_row['store_payments_methods_id'])."'";
									$estimated_payment_receive_time_result_sql = tep_db_query($estimated_payment_receive_time_select_sql);
									if ($estimated_payment_receive_time_row = tep_db_fetch_array($estimated_payment_receive_time_result_sql)) {
										$payment_received_by_timestamp = mktime(date("H"), (int)date("i")+(int)$estimated_payment_receive_time_row['payment_methods_estimated_receive_period'], (int)date("s"), date("m"), date("d"), date("Y"));
										$payment_notice_due_timestamp = mktime(date("H")+24, (int)date("i")+(int)$estimated_payment_receive_time_row['payment_methods_estimated_receive_period'], (int)date("s"), date("m"), date("d"), date("Y"));	// Hard code 24 for now..Need global configuration

										$payment_received_by_date = date("Y-m-d H:i:s T", $payment_received_by_timestamp);
										$payment_notice_due_date = date("Y-m-d H:i:s T", $payment_notice_due_timestamp);
                                                                                
                                                                                $is_g2g = c2c_invoice::check_g2g_withdraw($store_payments_row['store_payments_id']);
                                                                                if($is_g2g == true){
                                                                                    $payment_received_by_str = "\n\n" . sprintf(TEXT_PAYMENT_ESTIMATE_PAYMENT_RECEIVED_G2G, $payment_received_by_date, $payment_notice_due_date);                                        
                                                                                }
                                                                                else{
                                                                                    $payment_received_by_str = "\n\n" . sprintf(TEXT_PAYMENT_ESTIMATE_PAYMENT_RECEIVED, $payment_received_by_date, $payment_notice_due_date);                                        
                                                                                }
                                                        
//										$payment_received_by_str = sprintf(TEXT_PAYMENT_ESTIMATE_PAYMENT_RECEIVED, $payment_received_by_date, $payment_notice_due_date);
									}

									// Insert payment history
									$payment_history_sql_data_array = array('store_payments_id' => $store_payments_row['store_payments_id'],
																			'store_payments_status' => '3',
																			'date_added' => 'now()',
																			'payee_notified' => '1',
																			'comments' => $payment_received_by_str,
																			'changed_by_role' => 'system'
																			);
									tep_db_perform(TABLE_STORE_PAYMENTS_HISTORY, $payment_history_sql_data_array);
                                                                        
                                                                        if($is_g2g == true){
                                                                            $withdraw_datetime = c2c_invoice::get_store_payment_datetime($store_payments_row['store_payments_id']); 
                                                                        }
                                            
									// Email to beneficiary
									@tep_mail($store_payments_row['user_firstname'].' '.$store_payments_row['user_lastname'], $store_payments_row['user_email_address'], sprintf(EMAIL_PAYMENT_PAYMENT_UPDATE_SUBJECT, $store_payments_row['store_payments_id']), $store_payments_row['user_firstname'].' '.$store_payments_row['user_lastname'] . "\n\n" . $payment_received_by_str . "\n\n\n" . EMAIL_CONTACT . EMAIL_FOOTER, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
                                                            
                                                                        if(isset($withdraw_datetime) && ($withdraw_datetime != false)){
                                                                            c2c_invoice::create_c2c_invoice_queue($store_payments_row['store_payments_id'], 'withdrawal', $withdraw_datetime);
                                                                        }
                                                
									}
								} else if (strtolower($returned_status) == 'processed') { // status processed
									$comments_log_str .= "<u>Result</u>\n";
									$comments_log_str .= "Send Mass Payment Processed\nStatus: Processed.\nReference: " . $masspay_txn_id;

									$store_payments_data_sql = array( 	'store_payments_reference' => tep_db_prepare_input($masspay_txn_id),
																		'store_payments_last_modified' => 'now()',
																		'store_payments_status' => 2);
									tep_db_perform(TABLE_STORE_PAYMENTS, $store_payments_data_sql, 'update', " store_payments_id = '" . $store_payments_row['store_payments_id'] . "' ");

									$store_payments_history_data_sql = array( 	'store_payments_id' => $store_payments_row['store_payments_id'],
																				'store_payments_status' => '2',
																				'date_added' => 'now()',
																				'payee_notified' => 0,
																				'comments' => tep_db_prepare_input($comments_log_str),
																				'changed_by' => tep_db_prepare_input('system'),
																				'changed_by_role' => tep_db_prepare_input('system'));
									tep_db_perform(TABLE_STORE_PAYMENTS_HISTORY, $store_payments_history_data_sql);
								} else if (strtolower($returned_status) == 'denied' || strtolower($returned_status) == 'unclaimed') { // status denied
									$comments_log_str .= "<u>Result</u>\n";
									$comments_log_str .= "Send Mass Payment rejected\nStatus: ".strtolower($returned_status).".\nReference: " . $masspay_txn_id;

									$store_payments_data_sql = array( 	'store_payments_reference' => tep_db_prepare_input($masspay_txn_id),
																		'store_payments_last_modified' => 'now()',
																		'store_payments_status' => 2);
									tep_db_perform(TABLE_STORE_PAYMENTS, $store_payments_data_sql, 'update', " store_payments_id = '" . $store_payments_row['store_payments_id'] . "' ");

									$store_payments_history_data_sql = array( 	'store_payments_id' => $store_payments_row['store_payments_id'],
																				'store_payments_status' => '2',
																				'date_added' => 'now()',
																				'payee_notified' => 0,
																				'comments' => tep_db_prepare_input($comments_log_str),
																				'changed_by' => tep_db_prepare_input('system'),
																				'changed_by_role' => tep_db_prepare_input('system'));
									tep_db_perform(TABLE_STORE_PAYMENTS_HISTORY, $store_payments_history_data_sql);
								} else {
									$comments_log_str .= "<u>Result</u>\n";
									$comments_log_str .= "Send Mass Payment On Hold\nStatus: Unknown.\nReference: " . $masspay_txn_id;

									$store_payments_history_data_sql = array( 	'store_payments_id' => $store_payments_row['store_payments_id'],
																				'store_payments_status' => 0,
																				'date_added' => 'now()',
																				'payee_notified' => 0,
																				'comments' => tep_db_prepare_input($comments_log_str),
																				'changed_by' => tep_db_prepare_input('system'),
																				'changed_by_role' => tep_db_prepare_input('system'));
									tep_db_perform(TABLE_STORE_PAYMENTS_HISTORY, $store_payments_history_data_sql);
								}
							} else { // receiver email not match
								$comments_log_str .= "<u>Result</u>\n";
								$comments_log_str .= "Receiver Email Not Match, " .$store_payments_array['MODULE_PAYPAL_SEND_EMAIL'] .'!='. $returned_receiver_email."\n\n";

								$store_payments_history_data_sql = array( 	'store_payments_id' => $store_payments_row['store_payments_id'],
																			'store_payments_status' => 0,
																			'date_added' => 'now()',
																			'payee_notified' => 0,
																			'comments' => tep_db_prepare_input($comments_log_str),
																			'changed_by' => tep_db_prepare_input('system'),
																			'changed_by_role' => tep_db_prepare_input('system'));
								tep_db_perform(TABLE_STORE_PAYMENTS_HISTORY, $store_payments_history_data_sql);
							}
						} else { // amount or currency not match
							$comments_log_str .= "<u>Result</u>\n";
							$comments_log_str .= "Amount not matched, " . $currencies->currencies[$returned_mc_currency]['symbol_left'].$return_paypal_gross_amt.$currencies->currencies[$returned_mc_currency]['symbol_right'] .'!='. $currencies->currencies[$paypal_gross_amt]['symbol_left'].$paypal_gross_amt.$currencies->currencies[$paypal_gross_amt]['symbol_right'] ."\n\n";

							$store_payments_history_data_sql = array( 	'store_payments_id' => $store_payments_row['store_payments_id'],
																		'store_payments_status' => 0,
																		'date_added' => 'now()',
																		'payee_notified' => 0,
																		'comments' => tep_db_prepare_input($comments_log_str),
																		'changed_by' => tep_db_prepare_input('system'),
																		'changed_by_role' => tep_db_prepare_input('system'));
							tep_db_perform(TABLE_STORE_PAYMENTS_HISTORY, $store_payments_history_data_sql);
						}
					}
				}
				break;
			//A new dispute was filed
			case 'new_case':
				$cb_email_content = 'Order ID: ' . $paypalIpn->key['custom'] . '<br>Case ID:' . $paypalIpn->key['case_id'] . '<br>Case Type:' . $paypalIpn->key['case_type'] . '<br>Reason Code:' . $paypalIpn->key['reason_code'];
				@tep_mail('<EMAIL>', '<EMAIL>', '[OffGamers] PayPal Dispute/Chargeback Notification', $cb_email_content, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
				break;
		}
	}
}

?>