<?php
require('includes/application_top.php');
require_once(DIR_WS_CLASSES . 'payment_methods.php');

function tep_order_notify($orders_status_id, $customer_id, $customer_email) {
    $orders_id_select_sql = "	SELECT orders_id 
								FROM " . TABLE_ORDERS . " 
								WHERE customers_id = '" . (int) $customer_id . "' 
									AND orders_status = '" . (int) $orders_status_id . "' 
									AND FIND_IN_SET('170', orders_tag_ids)";
    $orders_id_result_sql = tep_db_query($orders_id_select_sql);

    if (tep_db_num_rows($orders_id_result_sql) > 0) {
        $order_cnt = 1;
        $email_subject = 'Customer ID: ' . $customer_id . ' - Email Verified';
        $email_text = 'E-mail: ' . $customer_email . "\n" . 'ID: ' . $customer_id . "\n\n" . 'Order/s awaiting email verification:' . "\n";

        while ($orders_id_row = tep_db_fetch_array($orders_id_result_sql)) {
            $email_text .= $order_cnt . '. ' . $orders_id_row['orders_id'] . "\n";
            $order_cnt++;
        }

        tep_mail(STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS, implode(' ', array(EMAIL_SUBJECT_PREFIX, $email_subject)), $email_text, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
    }
}

$email = (isset($_REQUEST['email']) ? $_REQUEST['email'] : '');
$customerID = (isset($_REQUEST['customer_id']) ? $_REQUEST['customer_id'] : '');

if (!empty($customerID) && !empty($email)) {
    // Rerun Genesis script for all verifying orders
    $order_info_select_sql = "	SELECT DISTINCT o.payment_methods_parent_id 
								FROM " . TABLE_ORDERS . " AS o 
								WHERE o.customers_id ='" . tep_db_input($customerID) . "'
									AND o.orders_status = 7";
    $order_info_result_sql = tep_db_query($order_info_select_sql);

    while ($order_info_row = tep_db_fetch_array($order_info_result_sql)) {
        $cust_payment_method_obj = new payment_methods($order_info_row['payment_methods_parent_id']);
        $cust_payment_method_obj = $cust_payment_method_obj->payment_method_array;

        if (method_exists($cust_payment_method_obj, 'get_orders_with_payer_email')) {
            $cust_payment_method_obj->get_orders_with_payer_email($customerID, $email);
        }
    }

//    tep_order_notify(7, $customerID, $email);
}
?>