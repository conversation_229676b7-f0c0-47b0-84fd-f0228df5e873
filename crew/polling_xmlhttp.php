<?php
header("Cache-Control: no-cache");
header("Cache-Control: post-check=0,pre-check=0");
header("Cache-Control: max-age=0");
header("Pragma: no-cache");

header('Content-Type: text/xml');

require('includes/application_top.php');

if (file_exists(DIR_WS_LANGUAGES . $_SESSION['language'] . '/'. FILENAME_POLLING)) {
	include_once(DIR_WS_LANGUAGES . $_SESSION['language'] . '/' . FILENAME_POLLING);
}

require_once(DIR_WS_CLASSES . 'polling.php');

$aa = (isset($_REQUEST['aa']) ? $_REQUEST['aa'] : '');
$uid = (isset($_REQUEST['uid']) ? $_REQUEST['uid'] : '');
$pid = (isset($_REQUEST['pid']) ? $_REQUEST['pid'] : '');
$oid = (isset($_REQUEST['oid']) ? $_REQUEST['oid'] : '');
$comment = (isset($_REQUEST['comment']) ? $_REQUEST['comment'] : '');
$email = (isset($_REQUEST['email']) ? $_REQUEST['email'] : '');
$action = (isset($_REQUEST['action']) ? $_REQUEST['action'] : '');

if (isset($_REQUEST['tpl']) && $action == 'show_polls_questions') {
	$poll_object = new polling($uid, $_REQUEST['tpl']);
} else {
	$poll_object = new polling($uid);
}

if (tep_not_null($action)) {
	switch($action) {
		case 'vote':
			$error = false;
			if (tep_not_null($oid)) {
				if ($poll_object->is_valid_poll($pid, $oid)) {
					if($aa!='1'&&$uid=='0'){
						$messageStack->add('polling', stripslashes(WARNING_POLLING_NOT_PUBLIC), 'error');
					} else {
						if ($poll_object->submit_vote($pid, $oid)) {
							$messageStack->add('polling', stripslashes(SUCCESS_POLLING_VOTED), 'success');
						} else {
							$messageStack->add('polling', stripslashes(WARNING_POLLING_EXTRA_SUBMISSION), 'warning');
						}
					}
				} else {
					$error = true;
					$messageStack->add('polling', stripslashes(ERROR_POLLING_INVALID_QUESTION), 'error');
				}
			} else {
				$error = true;
				$messageStack->add('polling', stripslashes(ERROR_POLLING_NO_SELECTION), 'error');
			}
			unset($_REQUEST['action']);
			break;
		case 'comment':
			$error = false;
			
			if ($poll_object->submit_comment($uid, $pid, $comment, $email)) {
				$messageStack->add('polling', SUCCESS_POLLING_COMMENT, 'success');
			} else {
				$messageStack->add('polling', ERROR_POLLING_COMMENT_FAILED, 'error');
			}
			
			unset($_REQUEST['action']);
			
			break;
		case 'show_polls_result':
			break;
		case 'show_polls_questions':
			break;
		case 'show_comment_form':
			break;
		default:
			break;
	}
}
echo '<response>';
if (($uid!=0)&&(!tep_session_is_registered('customer_id'))) {
	echo '<error>';
	echo tep_href_link(FILENAME_LOGIN, '', 'SSL');
	echo '</error>';
} else if ($action=='show_polls_questions'){
	echo '<polls>';
	$total_poll = count($poll_object->active_polls);
	for ($poll_cnt=0; $poll_cnt < $total_poll; $poll_cnt++) {
		echo '<questions id=\''.$poll_object->active_polls[$poll_cnt]['id'].'\'><![CDATA[';
		echo  '<div id="poll_vote_'.$poll_object->active_polls[$poll_cnt]['id'].'_div">'.
					tep_draw_form('poll_vote_'.$poll_object->active_polls[$poll_cnt]['id'].'_form', tep_href_link(FILENAME_POLLING, 'action=vote&pollid='.$poll_object->active_polls[$poll_cnt]['id']), 'post', 'onSubmit="return poll_submit_form_checking(\''.$poll_object->active_polls[$poll_cnt]['id'].'\', \''.$poll_object->active_polls[$poll_cnt]['allow_anonymous'].'\', \'poll_vote_'.$poll_object->active_polls[$poll_cnt]['id'].'_div\', \'poll_vote_'.$poll_object->active_polls[$poll_cnt]['id'].'_form\', \'poll_'.$poll_object->active_polls[$poll_cnt]['id'].'\')"') . '
					<table border="0" width="90%" cellspacing="0" cellpadding="4">';
		if ($poll_cnt > 0) {
			echo '		<tr>
							<td class="systemBoxText" colspan="2"><div class="row_separator">&nbsp;</div></td>
						</tr>';
		}
		
		echo '			<tr>
							<td align="left" colspan="2" style="color:#000000;font-size:11px;font-weight:bold">'.tep_draw_hidden_field('poll_vote_'.$poll_object->active_polls[$poll_cnt]['id'].'_hidden', $poll_object->active_polls[$poll_cnt]['id']).$poll_object->active_polls[$poll_cnt]['question'].'<br /></td>
						</tr>';
		
		$total_option = count($poll_object->active_polls[$poll_cnt]['option']);
		for ($opt_cnt=0; $opt_cnt < $total_option; $opt_cnt++) {
			echo '		<tr>
							<td width="20" valign="top">'.tep_draw_radio_field("poll_".$poll_object->active_polls[$poll_cnt]['id'], $poll_object->active_polls[$poll_cnt]['option'][$opt_cnt]['id'], false, 'id="poll_'.$poll_object->active_polls[$poll_cnt]['id'].'_'.$opt_cnt.'"').'</td><td width="150" style="color:#000000;font-size:11px;text-align:left;"><label for=\"poll_'.$poll_object->active_polls[$poll_cnt]['id']."_".$opt_cnt."\">".$poll_object->active_polls[$poll_cnt]['option'][$opt_cnt]['value'].'</label></td>
						</tr>';
		}
		echo  '		</table>
					<div class="dottedLine"><!-- --></div>
					<div style="text-align:center">
						<div style="float:left;">'. 
							tep_div_button(2, BUTTON_VOTE, 'javascript:update_poll(\''.$poll_object->active_polls[$poll_cnt]['id'].'\', \''.$poll_object->active_polls[$poll_cnt]['allow_anonymous'].'\', \'poll_vote_'.$poll_object->active_polls[$poll_cnt]['id'].'_div\', \'poll_vote_'.$poll_object->active_polls[$poll_cnt]['id'].'_form\', \'poll_'.$poll_object->active_polls[$poll_cnt]['id'].'\')', 'style="float:left;padding:0 0 0 7px;"', 'gray_button') . 
						'</div>
						<div style="float:left">' . 
							tep_div_button(2, BUTTON_VIEW_RESULT, 'javascript:;' , 'onclick="showPollResult('.$poll_object->active_polls[$poll_cnt]['id'].', '.$poll_object->identity.', \'' . $poll_object->type . '\');" style="padding:0 0 0 7px;"', 'gray_button') . 
						'</div>
					</div>
					</form>
				</div>';
		echo ']]></questions>';
	}
	echo '</polls>';
} else if ($action=='show_comment_form') {
	echo '<comment><![CDATA[
			<div id="poll_comment_div">
			<table border="0" width="100%" cellspacing="0" cellpadding="0">
				<tr>
					<td align="center">
						<form name="poll_comment" action="index.php" method="post">
							<table border="0" width="90%" cellspacing="0" cellpadding="4">
								<tr>
									<td colspan="2" style="color:#000000;font-size:11px;">
										<b>Leave your comment:</b><br/>
										<textarea id="poll_comment_message" name="poll_comment_message" rows="3" cols="30" style="background-color:#FFFFFF; width:165px;"></textarea><br/>
									</td>
								</tr>';
	if($uid==0){
		echo '					<tr>
									<td style="font-size:11px;" colspan="2">Email Address:<BR></td>
								</tr>
								<tr>
									<td colspan="2">' . tep_draw_input_field("poll_comment_email", '', 'id="poll_comment_email"') . '</td>
								</tr>';
	} else {
		echo '					<tr>
									<td style="font-size:11px;" colspan="2">' . tep_draw_hidden_field('poll_comment_email', '<EMAIL>', 'id="poll_comment_email"') . '</td>
								</tr>';
	}
	
	echo '					</table>
							<div class="dottedLine"><!-- --></div>
							<div style="text-align:center;width:100%;">
								<div style="float:left;">'. 
									tep_div_button(2, BUTTON_SUBMIT, 'javascript:poll_comment_form_checking('.$uid.', '.$pid.', \'poll_votes\', \'poll_comment\', \'poll_comment_message\', \'poll_comment_email\');window.exit;' , 'style="float:left;padding:0 0 0 7px;"', 'gray_button') . 
								'</div>
								<div style="float:left">' . 
									tep_div_button(2, BUTTON_BACK, 'javascript:;' , 'onclick="showPollResult('.$pid.', '.$uid.', '.$tpl.');return false;"', 'gray_button') . 
								'</div>
							</div>
						</form>
					</td>
				</tr>
			</table></div>';
	echo ']]></comment>';
} else {
	echo '<result><![CDATA[
			<table border="0" width="100%" cellspacing="0" cellpadding="0">
				<tr>
					<td align="center">
						<table border="0" width="90%" cellspacing="0" cellpadding="2">';
	if ($messageStack->size('polling') > 0) {
		echo '<tr>';
		echo '<td class="systemBoxText" style="font-weight:bold;">';
		echo $messageStack->output('polling');
		echo '</td>';
		echo '</tr>';
	}
	echo '					<tr>
								<td class="systemBoxText">';
	echo $poll_object->show_poll_result($pid, $uid, $tpl);
	echo '						</td>
							</tr>
						</table>
					</td>
				</tr>
			</table>';
	echo ']]></result>';
}
echo '</response>';
?>