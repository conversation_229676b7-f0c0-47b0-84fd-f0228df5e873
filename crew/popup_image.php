<?php
/*
  $Id: popup_image.php,v 1.3 2010/02/05 10:15:26 henry.chow Exp $

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2003 osCommerce

  Released under the GNU General Public License
*/

  require('includes/application_top.php');

  $navigation->remove_current_page();
  $products_query = tep_db_query("	SELECT pd.products_name, pd.products_image 
  									FROM " . TABLE_PRODUCTS . " p left join " . TABLE_PRODUCTS_DESCRIPTION . " pd 
  										ON p.products_id = pd.products_id 
  									WHERE p.products_status = '1' 
  										AND p.products_id = '" . (int)$_GET['pID'] . "' 
  										AND pd.language_id = '" . (int)$languages_id . "'");
  $products = tep_db_fetch_array($products_query);

  $content = CONTENT_POPUP_IMAGE;
  $javascript = $content . '.js';
  $body_attributes = ' onload="resize();"';

  require(DIR_WS_TEMPLATES . TEMPLATENAME_POPUP);

  require('includes/application_bottom.php');
?>