<?php
/*
  $Id: popup_paypal.php,v 1.1 2004/09/23 05:36:08 stanley Exp $

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  DevosC, Developing open source Code
  http://www.devosc.com

  Copyright (c) 2003 osCommerce
  Copyright (c) 2004 DevosC.com

  Released under the GNU General Public License
*/

  require("includes/application_top.php");
  $navigation->remove_current_page();
?>
<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?php echo HTML_PARAMS; ?>>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=<?php echo CHARSET; ?>">
<title><?php echo TITLE; ?></title>
<base href="<?php echo (($request_type == 'SSL') ? HTTPS_SERVER : HTTP_SERVER) . DIR_WS_CATALOG; ?>">
<link rel="stylesheet" type="text/css" href="stylesheet.css">
<style type="text/css">
.pagebody {width: 600px; margin:10px; background-color: #FFFFFF;}
.ppheading {font-size: 17px; font-family: verdana,arial,helvetica,sans-serif; font-weight: 700; color: #003366;}
.ppheading {padding-bottom:10px;}
.pptext, td {font-size: 13px; font-family: verdana,arial,helvetica,sans-serif; font-weight: 400;}
.ppbuttonsmall {font-size: 11px; font-family: verdana,arial,helvetica,sans-serif; font-weight: 400; border-style:outset; color:#000000;}
</style>
</head>
<body>
<?php
  require_once(DIR_WS_MODULES . 'payment/paypal/functions/paypal.fnc.php');
  paypal_include_lng(DIR_WS_MODULES . 'payment/paypal/catalog/languages/', $language, 'info_cc.inc.php');
?>
</body>
</html>
<?php
  require("includes/counter.php");
  require(DIR_WS_INCLUDES . 'application_bottom.php');
?>
