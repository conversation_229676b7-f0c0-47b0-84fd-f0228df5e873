<?php
	include_once('includes/application_top.php');
	
	if (isset($_REQUEST['regional_action'])) {
		switch ($_REQUEST['regional_action']) {
			case 'preload' : 
				$country_data = '';
				$country_arr = array('country' => array(), 'gst' => '');
				
				if (tep_not_null($_SESSION['RegionGST']['loc_country'])) { // GST valid, country name show as text
					$country_arr['gst'] = '<input type="hidden" id="id_country" name="RegionalSet[loc_country]" value="' . $_SESSION['RegionGST']['loc_country'] . '">' . htmlentities(tep_get_country_name($_SESSION['RegionGST']['loc_country']));
				} else {
					require_once(DIR_WS_CLASSES . 'ogm_xml_to_ary.php');
					
					$xml_array_obj = new ogm_xml_to_ary(realpath('cache/' . FILENAME_COUNTRY_LIST_XML));
					$xml_array = $xml_array_obj->get_ogm_xml_to_ary();
					
					if (count($xml_array)) {
						foreach ($xml_array['countries']['_c']['country'] as $xml_countries_num => $xml_countries_data) {
							$country_id = $xml_countries_data['_c']['countries_id']['_v'];
							$country_name = $xml_countries_data['_c']['countries_name']['_v'];
							
							$country_arr['country'][] = array( 'id' => $country_id,
													'text' => $country_name);
						}
					}
				}
				
				$country_data = json_encode($country_arr);
				
				echo $country_data;
				break;
		}
	}
?>