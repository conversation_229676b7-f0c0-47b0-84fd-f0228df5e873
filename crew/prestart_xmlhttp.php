<?php
	header("Cache-Control: no-cache");
	header("Cache-Control: post-check=0,pre-check=0");
	header("Cache-Control: max-age=0");
	header("Pragma: no-cache");
	header('Content-Type: text/xml');
	
	include_once('includes/application_top.php');
	
	$languages_array = array();
	
	echo '<response>';
	
	switch ($_REQUEST['regional_action']) {
		case 'getRegionalContent' :
			$country = isset($_REQUEST['country_ajx']) ? $_REQUEST['country_ajx'] : $country;
			
			//Pre-load Data into array
			$language_select_sql = "	SELECT name, code 
										FROM " . TABLE_LANGUAGES;
			$language_result = tep_db_query($language_select_sql);
			while($language_row = tep_db_fetch_array($language_result)){
				$languages_array[$language_row['code']] = $language_row['name'];
			}
			
			//Get Geo Zone ID
			$localization_obj = new localization($country);
			
			$geo_zone_id = $localization_obj->get_zone_id(3);
			$currency_info_obj = $localization_obj->get_zone_info($geo_zone_id);
			
			$geo_zone_id = $localization_obj->get_zone_id(2);
			$language_info_obj = $localization_obj->get_zone_info($geo_zone_id);
			
			$selected_currency = ($cookies_started && tep_not_null($currency_info_obj->zone_currency_id) && in_array($currency, $currency_info_obj->zone_currency_id)) ? $currency : $currency_info_obj->zone_default_currency_id;
			$selected_language = ($cookies_started && tep_not_null($language_info_obj->zone_languages_id) && in_array($language_code, $language_info_obj->zone_languages_id)) ? $language_code : $language_info_obj->zone_default_languages_id;
			
			echo "<currencies>";
			//Get Currency Data
			if (tep_not_null($currency_info_obj->zone_currency_id)) {
				if (tep_not_null($_SESSION['RegionGST']['currency']) && tep_not_null($currencies->currencies[$_SESSION['RegionGST']['currency']])) {
					echo "<region_currency id='" . $_SESSION['RegionGST']['currency'] . "'><![CDATA[".$currencies->currencies[$_SESSION['RegionGST']['currency']]['title'].' ('.$currencies->currencies[$_SESSION['RegionGST']['currency']]['symbol_left'].$currencies->currencies[$_SESSION['RegionGST']['currency']]['symbol_right'].")]]></region_currency>";
				} else {
					foreach ($currency_info_obj->zone_currency_id AS $cy_code) {
						if (isset($currencies->currencies[$cy_code])) {
							echo "<currency id='".$cy_code."' selected='".($selected_currency == $cy_code ? "selected=\"\"" : "")."'><![CDATA[".$currencies->currencies[$cy_code]['title'].' ('.$currencies->currencies[$cy_code]['symbol_left'].$currencies->currencies[$cy_code]['symbol_right'].")]]></currency>";
						}
					}
				}
			}
			echo "</currencies>";
			echo "<languages>";
			//Get Language Data
			if (tep_not_null($language_info_obj->zone_languages_id)) {
				foreach ($language_info_obj->zone_languages_id AS $ln_code) {
					echo "<language id='".$ln_code."' selected='".($selected_language == $ln_code ? "checked=\"\"" : "")."'><![CDATA[".$languages_array[$ln_code]."]]></language>";
				}
			}
			echo "</languages>";
			
			unset($languages_array, $localization_obj, $currency_info_obj, $language_info_obj);
			break;
	}
	echo '</response>';
?>