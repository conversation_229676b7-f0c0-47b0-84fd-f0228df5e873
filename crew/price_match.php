<?
/*
  	$Id: price_match.php,v 1.2 2005/01/06 08:44:02 weichen Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2003 osCommerce
	
  	Released under the GNU General Public License
*/

require('includes/application_top.php');
require(DIR_WS_LANGUAGES . $language . '/' . FILENAME_PRICE_MATCH);

$error = false;
if (isset($HTTP_GET_VARS['action']) && ($HTTP_GET_VARS['action'] == 'send')) {
	$name = tep_db_prepare_input($HTTP_POST_VARS['name']);
    $email_address = tep_db_prepare_input($HTTP_POST_VARS['email']);
    $item = tep_db_prepare_input($HTTP_POST_VARS['item']);
    $url = tep_db_prepare_input($HTTP_POST_VARS['url']);
    $price = tep_db_prepare_input($HTTP_POST_VARS['price']);
    $cat_id = tep_db_prepare_input($HTTP_POST_VARS['categories_id']);
    $ladder = tep_db_prepare_input($HTTP_POST_VARS['ladder']);
    $hardcore = tep_db_prepare_input($HTTP_POST_VARS['hardcore']);
    $comments = tep_db_prepare_input($HTTP_POST_VARS['comments']);
    
    if($ladder){
    	$ladder = "Yes";	
    }else{
    	$ladder = "No";	
    }
    
    if($hardcore){
    	$hardcore = "Yes";	
    }else{
    	$hardcore = "No";	
    }
    /*
    if($cat_id){
	    $sql=mysql_query('SELECT * FROM categories_description where categories_id='.$cat_id)
				    or die(mysql_error());
				$row=mysql_fetch_array($sql);
	    //echo $row['categories_name'];
    }
    */
    //STORE_OWNER_EMAIL_ADDRESS
    if (!$name) {
    	$error = true;
        $messageStack->add('contact', 'Please enter your full name!');
    }
    if (tep_validate_email($email_address)) {
      	//tep_mail($to_name, $to_email_address, $email_subject, $email_text, $from_email_name, $from_email_address)
      	$email_text = "Price Match\n\nFrom: ".$name."\nEmail: ".$email_address."\nPrice: ".$price."\nURL: ".$url."\nCategaries: ".$cat_id."\nLadder: ".$ladder."\nHardcore: ".$hardcore."\nComments:\n".$comments;
      	tep_mail(STORE_OWNER, "<EMAIL>", EMAIL_SUBJECT, $email_text, $name, $email_address);
		
      	tep_redirect(tep_href_link(FILENAME_PRICE_MATCH, 'action=success'));
    } else {
      	$error = true;
		$messageStack->add('contact', ENTRY_EMAIL_ADDRESS_CHECK_ERROR);
    }
    
    if (!$cat_id) {
    	$error = true;
        $messageStack->add('contact', 'Please Select Reamls!');
	}
	if (!$item) {
	    $error = true;
	    $messageStack->add('contact', 'Item Name can not be blank!');
	}
	
	if (!$price) {
	   	$error = true;
	    $messageStack->add('contact', 'Please enter your item price!');
	}
}

$breadcrumb->add(NAVBAR_TITLE, tep_href_link(FILENAME_PRICE_MATCH));
$content = CONTENT_PRICE_MATCH;

require(DIR_WS_TEMPLATES . TEMPLATENAME_MAIN_PAGE);

require(DIR_WS_INCLUDES . 'application_bottom.php');
?>
