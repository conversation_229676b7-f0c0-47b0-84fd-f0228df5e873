<?
/*
  	$Id: product_info.php,v 1.14 2008/06/24 07:35:02 edwin.wang Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2003 osCommerce
	
  	Released under the GNU General Public License
*/

require('includes/application_top.php');

// Redirect since we do not want this page anymore.
tep_redirect(tep_href_link(FILENAME_DEFAULT, '', 'SSL'));

/*
if (isset($HTTP_POST_VARS['add_bundle_dynamic'])) {
	$Varray = _unserialize($bundleV);
	//echo "|".count($Varray);
	
	$cn = count($Varray);
	for ($u=0; $u<$cn; $u++) {
		$cart->add_cart($Varray[$u]['id'], $cart->get_quantity(tep_get_uprid($Varray[$u]['id'], $HTTP_POST_VARS['id']))+(int)$HTTP_POST_VARS['q_'.$Varray[$u]['id']], $Varray[$u]['id']);
	}
}	 
*/
require(DIR_WS_LANGUAGES . $language . '/' . FILENAME_PRODUCT_INFO);

if ($const_i_am_allowed_here) {
	require(DIR_WS_LANGUAGES . $language . '/' . FILENAME_PRODUCT_REVIEWS);
  	require(DIR_WS_LANGUAGES . $language . '/' . FILENAME_PRODUCT_REVIEWS_WRITE);
  	
  	$customer_query = tep_db_query("select customers_firstname, customers_lastname from " . TABLE_CUSTOMERS . " where customers_id = '" . (int)$customer_id . "'");
  	$customer = tep_db_fetch_array($customer_query);
  	
  	$all_cat_active = true;
  	if (is_array($cPath_array) && count($cPath_array)) {
	  	$inactive_cat_select_sql = "SELECT COUNT(categories_id) as inactive_cat 
	  								FROM " . TABLE_CATEGORIES . "
	  								WHERE categories_id IN ('" . implode("', '", $cPath_array) . "') 
	  									AND categories_status = 0";
	  	$inactive_cat_result_sql = tep_db_query($inactive_cat_select_sql);
  		$inactive_cat_row = tep_db_fetch_array($inactive_cat_result_sql);
	  	
	  	if ($inactive_cat_row['inactive_cat'] > 0)	$all_cat_active = false;
	}
	
	if ($all_cat_active) {
	  	$prod_select_sql = "SELECT COUNT(*) AS total 
	  						FROM " . TABLE_PRODUCTS . " p, " . TABLE_PRODUCTS_DESCRIPTION . " pd 
	  						WHERE p.products_status = '1' 
	  							AND ( p.products_bundle='yes' OR p.products_bundle_dynamic='yes' OR (p.products_bundle<>'yes' AND p.products_bundle_dynamic<>'yes' AND p.products_display='1')) 
	  							AND p.products_id = '" . (int)$HTTP_GET_VARS['products_id'] . "' 
	  							AND pd.products_id = p.products_id 
	  							AND pd.language_id = '" . (int)$languages_id . "'";
	  	$prod_result_sql = tep_db_query($prod_select_sql);
	  	$product_check = tep_db_fetch_array($prod_result_sql);
	} else {
		$product_check = array('total' => 0);
	}
}

if (!tep_session_is_registered('customer_id')) 	$navigation->set_snapshot();

$content = CONTENT_PRODUCT_REVIEWS_WRITE;
$javascript = $content . '.js';
$content = CONTENT_PRODUCT_INFO;
//$javascript = 'popup_window.js';
//echo "<script language=\"javascript\" src=\"includes/general.js\"></script>";

require(DIR_WS_TEMPLATES . TEMPLATENAME_MAIN_PAGE);

require(DIR_WS_INCLUDES . 'application_bottom.php');
?>