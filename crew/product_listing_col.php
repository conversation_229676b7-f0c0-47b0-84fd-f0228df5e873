<?php
  $listing_split = new splitPageResults($listing_sql, MAX_DISPLAY_SEARCH_RESULTS, 'p.products_id');

  if ( ($listing_split->number_of_rows > 0) && ( (PREV_NEXT_BAR_LOCATION == '1') || (PREV_NEXT_BAR_LOCATION == '3') ) ) {
?>
<table border="0" width="100%" cellspacing="0" cellpadding="2">
  <tr>
    <td class="pageResultsText"><?php echo $listing_split->display_count(TEXT_DISPLAY_NUMBER_OF_PRODUCTS); ?></td>
    <td class="pageResultsText" align="right"><?php echo TEXT_RESULT_PAGE . ' ' . $listing_split->display_links(MAX_DISPLAY_PAGE_LINKS, tep_get_all_get_params(array('page', 'info', 'x', 'y'))); ?></td>
  </tr>
</table>

<?php
  }
if ( ($listing_split->number_of_rows > 0) && ((PREV_NEXT_BAR_LOCATION == '2') || (PREV_NEXT_BAR_LOCATION == '3')) ) {
?>
<table border="0" width="100%" cellspacing="0" cellpadding="2">
  <tr>
    <td class="pageResultsText"><?php echo $listing_split->display_count(TEXT_DISPLAY_NUMBER_OF_PRODUCTS); ?></td>
    <td class="pageResultsText" align="right"><?php echo TEXT_RESULT_PAGE . ' ' . $listing_split->display_links(MAX_DISPLAY_PAGE_LINKS, tep_get_all_get_params(array('page', 'info', 'x', 'y'))); ?></td>
  </tr>
</table>
<?php
  }
?>
<?
  $list_box_contents = array();

  if ($listing_split->number_of_rows > 0) {
    $listing_query = tep_db_query($listing_split->sql_query);

    $row = 0;
    $column = 0;
    while ($listing = tep_db_fetch_array($listing_query)) {

      $product_contents = array();
	  //***** Begin Separate Price per Customer Mod *****
      $listing['specials_new_products_price']=tep_get_products_special_price($listing['products_id']);
      
	  //***** End Separate Price per Customer Mod *****
      $product_contents = array();
	  $normal_price = $listing['products_price'];
	  $special_price = $listing['specials_new_products_price'];
	  $pid = $listing['products_id'];
	  $w=0;
	  if($pid == $products_id){
	  	$yes = true;
	  }else{
	  	$yes = false;
	  }
      for ($col=0, $n=sizeof($column_list); $col<$n; $col++) {
        $lc_align = '';

        switch ($column_list[$col]) {
          case 'PRODUCT_LIST_MODEL':
            $lc_align = '';
            $lc_text = '&nbsp;' . $listing['products_model'] . '&nbsp;';
            break;
          case 'PRODUCT_LIST_NAME':
            $lc_align = '';
            if (isset($HTTP_GET_VARS['manufacturers_id'])) {
              $lc_text = '<tr valign="top" height="50"><td class="smallText" align="center"><a href="' . tep_href_link(FILENAME_PRODUCT_INFO, 'manufacturers_id=' . $HTTP_GET_VARS['manufacturers_id'] . '&products_id=' . $listing['products_id']) . '">' . $listing['products_name'] . '</a></td></tr>';
            } else {
              $lc_text = '<tr valign="top" height="50"><td class="smallText" align="center"><a href="' . tep_href_link(FILENAME_PRODUCT_INFO, ($cPath ? 'cPath=' . $cPath . '&' : '') . 'products_id=' . $listing['products_id']) . '">' . $listing['products_name'] . '</a></td></tr>';
            }
            break;
          case 'PRODUCT_LIST_MANUFACTURER':
            $lc_align = '';
            $lc_text = '&nbsp;<a href="' . tep_href_link(FILENAME_DEFAULT, 'manufacturers_id=' . $listing['manufacturers_id']) . '">' . $listing['manufacturers_name'] . '</a>&nbsp;';
            break;
          case 'PRODUCT_LIST_PRICE':
            $lc_align = 'right';
            
			if (tep_not_null($listing['specials_new_products_price'])) {
                $lc_text = '&nbsp;<tr><td class="price" align="center">Price:<br><s>' .  $currencies->format($normal_price) . '</s>&nbsp;' . $currencies->format($special_price) . '&nbsp;</td></tr>';                
            } else {
            	$customer_group_query = tep_db_query("select customers_groups_id from " . TABLE_CUSTOMERS . " where customers_id =  '" . $customer_id . "'");
				$customer_group = tep_db_fetch_array($customer_group_query);
				
				$customer_group_name_query = tep_db_query("select customers_groups_name from " . TABLE_CUSTOMERS_GROUPS . " where customers_groups_id =  '" . $customer_group['customers_groups_id'] . "'");
				$customer_group_name = tep_db_fetch_array($customer_group_name_query);
				
            	if($customer_group_name['customers_groups_id']){
		            $lc_text = '&nbsp;<tr><td class="price" align="center">Price:&nbsp;<s>'.$currencies->format($normal_price).'</s><br>'.$customer_group_name['customers_groups_name'].' Price:&nbsp;' . $currencies->display_price($pid, $listing['products_price'], tep_get_tax_rate($listing['products_tax_class_id'])).'&nbsp;</td></tr>';
            	}else{
            		$lc_text = '&nbsp;<tr><td class="price" align="center">Price:&nbsp;' . $currencies->display_price($pid, $listing['products_price'], tep_get_tax_rate($listing['products_tax_class_id'])) . '&nbsp;</td></tr>';
            	}      
            }
            break;
          case 'PRODUCT_LIST_QUANTITY':
           $lc_align = 'center';
            //$lc_text = '&nbsp;' . $listing['products_quantity'] . '&nbsp;';
            //echo $yes;
            if($yes){
            	if($err=="SL"){
			  		//$lc_text = '&nbsp;<font class="messageStockWarning">' . stripslashes(WARNING_PRODUCTS_LOW_QUANTITY) . $listing['products_quantity']. ' item/s in our stock. </font>&nbsp;';
			  		$lc_text = '&nbsp;<font class="messageStockWarning">' . stripslashes(WARNING_PRODUCTS_LOW_QUANTITY) . '</font>&nbsp;';
			  	 }
            }else{
            	$lc_text = '';
            }
            break;
          case 'PRODUCT_LIST_WEIGHT':
            $lc_align = 'right';
            $lc_text = '&nbsp;' . $listing['products_weight'] . '&nbsp;';
            break;
          case 'PRODUCT_LIST_IMAGE':
            $lc_align = 'center';
            if ($listing['products_image']){
            	$pro_img = $listing['products_image'];
            }else{
            	$pro_img = "label_nopic.gif";	
            }
            if (isset($HTTP_GET_VARS['manufacturers_id'])) {
              $lc_text = '<table width="100%" border="0" cellspacing="0" cellpadding="0"><tr><td align="center" class="productListing"><a href="' . tep_href_link(FILENAME_PRODUCT_INFO, 'manufacturers_id=' . $HTTP_GET_VARS['manufacturers_id'] . '&products_id=' . $listing['products_id']) . '">' . tep_image(THEMA_IMAGES."products/" . $pro_img, $listing['products_name'], SMALL_IMAGE_WIDTH, SMALL_IMAGE_HEIGHT) . '</a>&nbsp;</td></tr><tr><td height="22">&nbsp;</td></tr>';
            } else {
              if(PRODUCTS_FLAG == 'true'){
              	$lc_text = '&nbsp;<table width="130" height="248" border="0" cellspacing="0" cellpadding="0"><tr><td height="168" align="center" valign="center" background="'.THEMA_IMAGES.'box_products.gif"><a href="' . tep_href_link(FILENAME_PRODUCT_INFO, ($cPath ? 'cPath=' . $cPath . '&' : '') . 'products_id=' . $listing['products_id']) . '">' . tep_image(THEMA_IMAGES."products/" . $pro_img, $listing['products_name'], '', '') . '</a>&nbsp;</td></tr><tr height="25"><td align="right">';
              	if($listing['products_flag_id']){
			    $flag = explode(",", $listing['products_flag_id']);
				    for ($w=0, $o=sizeof($flag); $w<$o; $w++) {
				    	$flag_query = tep_db_query("select products_flag_id, products_flag_name, products_flag_image from products_flag where products_flag_id = '".$flag[$w]."' order by products_flag_id");
					    
				    	while ($products_flag = tep_db_fetch_array($flag_query)) {
					        $lc_text .= '&nbsp;'.tep_image(DIR_WS_IMAGES . $products_flag['products_flag_image'], $products_flag['products_flag_name']);
					    }
				    }	
			    }else{
			    	$lc_text .= tep_image(DIR_WS_IMAGES . 'pixel_trans.gif', '', '', '22');
			    }
			    $lc_text .= '</td></tr>';
              }else{
              	$lc_text = '&nbsp;<table width="130" height="248" border="0" cellspacing="0" cellpadding="0"><tr><td height="168" align="center" valign="center" background="'.THEMA_IMAGES.'"images/box_products.gif"><a href="' . tep_href_link(FILENAME_PRODUCT_INFO, ($cPath ? 'cPath=' . $cPath . '&' : '') . 'products_id=' . $listing['products_id']) . '">' . tep_image(THEMA_IMAGES."products/" . $pro_img, $listing['products_name'], SMALL_IMAGE_WIDTH, SMALL_IMAGE_HEIGHT) . '</a></td></tr><tr><td height="22">&nbsp;</td></tr>';
              }
            }
            break;
          case 'PRODUCT_LIST_BUY_NOW':
            $lc_align = 'center';
            $quantity = tep_db_query("select products_quantity
                                      from " . TABLE_PRODUCTS . "
                                      where products_id = '" . $pid . "'");
	          $quantity_values = tep_db_fetch_array($quantity);
	          $qty = $quantity_values['products_quantity'];
			  
	          if($listing['products_bundle']=="yes"){
		  		$bundle_query = tep_db_query("	SELECT pd.products_name, pb.*, p.products_bundle, p.products_id, p.products_price
											FROM products p 
											INNER JOIN products_description pd
											  ON p.products_id=pd.products_id
											INNER JOIN products_bundles pb
											  ON pb.subproduct_id=pd.products_id 
											WHERE pb.bundle_id = " . tep_get_prid($listing['products_id']) . " and language_id = '" . (int)$languages_id . "'");
			    while ($bundle_data = tep_db_fetch_array($bundle_query)) {
			    	      $quantity1 = tep_db_query("select products_quantity
                                      from " . TABLE_PRODUCTS . "
                                      where products_id = '" . $bundle_data['products_id'] . "'");
				          $quantity_values1 = tep_db_fetch_array($quantity1);
				          
			    	      if($quantity_values1['products_quantity']=="0"){
					          	$qty = 0;
					          	//echo "qty:".$quantity_values1['products_quantity']."<br>";
					      }
			    	if ($bundle_data['products_bundle'] == "yes") {
			    		//echo $bundle_data['products_name'];
						$bundle_query_nested = tep_db_query("	SELECT pd.products_name, pb.*, p.products_bundle, p.products_id, p.products_price
												FROM products p 
												INNER JOIN products_description pd
												  ON p.products_id=pd.products_id
												INNER JOIN products_bundles pb
												  ON pb.subproduct_id=pd.products_id 
												WHERE pb.bundle_id = " . $bundle_data['products_id'] . " and language_id = '" . (int)$languages_id . "'");
						while ($bundle_data_nested = tep_db_fetch_array($bundle_query_nested)) {
							$quantity2 = tep_db_query("select products_quantity
                                      from " . TABLE_PRODUCTS . "
                                      where products_id = '" . $bundle_data_nested['products_id'] . "'");
					          $quantity_values2 = tep_db_fetch_array($quantity2);
					          
					          if($quantity_values2['products_quantity']=="0"){
					          		$qty = 0;
					          		//echo "qty:".$quantity_values2['products_quantity']."<br>";
					          }
						}
					} 
			    }
		  }
	          
	          if($qty < 1){
	          	 	//$lc_text = '<br><div align="center"><a href="' . tep_href_link(FILENAME_PRODUCT_INFO, ($cPath ? 'cPath=' . $cPath . '&' : '') . 'products_id=' . $listing['products_id']) . '">'.tep_image(DIR_WS_IMAGES . 'button_detail.jpg', 'Details!').'</a></div>&nbsp;<br><table border="0" align="center" cellspacing="0" cellpadding="0"><tr><td>................................................................</td></tr></table>';
	          	 	//$lc_text = '<tr><td height="50" width="160" class="productsDescription" align="center"><br>'.stripslashes($listing['products_description']).'<br>&nbsp;</td></tr><tr><td align="center" width="100%" height="50">'.tep_image(THEMA_IMAGES . 'button_out_of_stock.gif', 'Out of Stock!').'</td></tr>';
	          	 	$lc_text = '<tr valign="bottom" height="30"><td align="center"><table cellspacing="0" cellpadding="0" border="0"><tr><td>'.tep_div_button(2, IMAGE_BUTTON_OUT_OF_STOCK,'javascript:;', '', 'lightgray_button_fix_width').'</td></tr></table></td></tr>';
	          }else{
	          	 if($listing['products_bundle_dynamic']){
	          	 	//$lc_text = '<tr><td height="50" width="160" class="productsDescription" align="center" valign="middle"><br>'.stripslashes($listing['products_description']).'</br>&nbsp;</td></tr><tr><td align="center" class="main" height="50"><a href="' . tep_href_link(FILENAME_PRODUCT_INFO, 'manufacturers_id=' . $HTTP_GET_VARS['manufacturers_id'] . '&products_id=' . $listing['products_id']) . '">'.tep_image(THEMA. 'cart.gif', 'Buy Now!').'</a></td></tr>';
	          	 	$lc_text = '<tr valign="bottom" height="30" ><td align="center" class="main"><a href="' . tep_href_link(FILENAME_PRODUCT_INFO, 'manufacturers_id=' . $HTTP_GET_VARS['manufacturers_id'] . '&products_id=' . $listing['products_id']) . '">'.tep_image(THEMA. 'cart.gif', 'Buy Now!').'</a></td></tr>';
	          	 }else{
					$lc_text = '<tr valign="bottom" height="30"><td align="center" class="main">'.
					tep_draw_form('buy_now', tep_href_link(basename($PHP_SELF), tep_get_all_get_params(array('action')) . 'action=buy_now&products_id='. $listing['products_id']), 'POST', '') . 
					'Qty: ' . tep_draw_input_field('buyqty', '1', 'size=3 maxlength=3') . 
					'
					<INPUT type="hidden" name="buy_now_qty" value="'.$listing['products_quantity'].'">'.
					tep_div_button(1, IMAGE_BUTTON_IN_CART,'buy_now', '', 'green_button_fix_width').'
					</form></td></tr>';
	          	 }
	          }
	          $lc_text .= '</table>';
            break;
        }
        $product_contents[] = $lc_text;

      }
      $lc_text = implode('', $product_contents);
      $list_box_contents[$row][$column] = array('align' => 'center',
      											'valign' => 'top',
                                                'params' => '',
                                                'text'  => $lc_text);
      $column ++;
      if ($column >= MAX_DISPLAY_CATEGORIES_PER_ROW) {
        $row ++;
        $column = 0;
      }
    }

    new productListingBox($list_box_contents);
  } else {
    $list_box_contents = array();

    $list_box_contents[0] = array('params' => 'class="messageRow"');
    $list_box_contents[0][] = array('params' => 'class="messageData"',
                                   'text' => TEXT_NO_PRODUCTS);

    new productListingBox($list_box_contents, 'class="messageBox"');
  }

  if ( ($listing_split->number_of_rows > 0) && ((PREV_NEXT_BAR_LOCATION == '2') || (PREV_NEXT_BAR_LOCATION == '3')) ) {
?>
<table border="0" width="100%" cellspacing="0" cellpadding="2">
  <tr>
    <td class="pageResultsText"><?php echo $listing_split->display_count(TEXT_DISPLAY_NUMBER_OF_PRODUCTS); ?></td>
    <td class="pageResultsText" align="right"><?php echo TEXT_RESULT_PAGE . ' ' . $listing_split->display_links(MAX_DISPLAY_PAGE_LINKS, tep_get_all_get_params(array('page', 'info', 'x', 'y'))); ?></td>
  </tr>
</table>
<?php
  }
?>