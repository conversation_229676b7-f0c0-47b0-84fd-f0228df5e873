<?php
/*
  $Id: product_reviews.php,v 1.4 2010/02/05 10:18:22 henry.chow Exp $

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2003 osCommerce

  Released under the GNU General Public License
*/

  require('includes/application_top.php');
  $product_info_query = tep_db_query("	SELECT p.products_id, p.products_model, pd.products_image, p.products_price, p.products_tax_class_id, pd.products_name 
  										FROM " . TABLE_PRODUCTS . " p, " . TABLE_PRODUCTS_DESCRIPTION . " pd 
  										WHERE p.products_id = '" . (int)$_GET['products_id'] . "' 
  											AND p.products_status = '1' 
  											AND p.products_id = pd.products_id 
  											AND pd.language_id = '" . (int)$languages_id . "'");
  if (!tep_db_num_rows($product_info_query)) {
    tep_redirect(tep_href_link(FILENAME_REVIEWS));
  } else {
    $product_info = tep_db_fetch_array($product_info_query);
  }
	$normal_price = $product_info['products_price'];
	echo $normal_price;
  if ($new_price = tep_get_products_special_price($product_info['products_id'])) {
    //CGDiscountSpecials start
    //$products_price = '<s>' . $currencies->format($normal_price) . '</s> <span class="productSpecialPrice">' . $currencies->display_price_nodiscount($new_price, tep_get_tax_rate($product_info['products_tax_class_id'])) . '</span>';
    //CGDiscountSpecials end
    
  	
  	//$products_price = '<s>' . $currencies->display_price($product_info['products_id'], $product_info['products_price'], tep_get_tax_rate($product_info['products_tax_class_id'])) . '</s> <span class="productSpecialPrice">' . $currencies->display_price($product_info['products_id'], $new_price, tep_get_tax_rate($product_info['products_tax_class_id'])) . '</span>';
    //CustomerDiscount start
    //$products_price = '<s>' . $currencies->display_price($product_info['products_id'], $product_info['products_price'], tep_get_tax_rate($product_info['products_tax_class_id'])) . '</s> <span class="productSpecialPrice">' . $currencies->display_price_nodiscount($new_price, tep_get_tax_rate($product_info['products_tax_class_id'])) . '</span>';
    //CustomerDiscount end
  } else {
    //$products_price = $currencies->display_price($product_info['products_id'], $product_info['products_price'], tep_get_tax_rate($product_info['products_tax_class_id']));
  }

  if (tep_not_null($product_info['products_model'])) {
    $products_name = $product_info['products_name'] . '<br><span class="smallText">[' . $product_info['products_model'] . ']</span>';
  } else {
    $products_name = $product_info['products_name'];
  }

  //require(DIR_WS_LANGUAGES . $language . '/' . FILENAME_PRODUCT_REVIEWS);
  
  require(DIR_WS_TEMPLATES . TEMPLATENAME_MAIN_PAGE);
  require(DIR_WS_LANGUAGES . $language . '/' . FILENAME_PRODUCT_INFO);
  require(DIR_WS_LANGUAGES . $language . '/' . FILENAME_PRODUCT_REVIEWS);
  require(DIR_WS_LANGUAGES . $language . '/' . FILENAME_PRODUCT_REVIEWS_WRITE);
  
  $customer_query = tep_db_query("select customers_firstname, customers_lastname from " . TABLE_CUSTOMERS . " where customers_id = '" . (int)$customer_id . "'");
  $customer = tep_db_fetch_array($customer_query);
  $product_check_query = tep_db_query("select count(*) as total from " . TABLE_PRODUCTS . " p, " . TABLE_PRODUCTS_DESCRIPTION . " pd where p.products_status = '1' and p.products_id = '" . (int)$HTTP_GET_VARS['products_id'] . "' and pd.products_id = p.products_id and pd.language_id = '" . (int)$languages_id . "'");
  $product_check = tep_db_fetch_array($product_check_query);
  
  $breadcrumb->add(NAVBAR_TITLE, tep_href_link(FILENAME_PRODUCT_REVIEWS, tep_get_all_get_params()));
  $content = CONTENT_PRODUCT_INFO;
   
  $content = CONTENT_PRODUCT_REVIEWS;
  $javascript = 'popup_window.js';

  require(DIR_WS_INCLUDES . 'application_bottom.php');
?>
