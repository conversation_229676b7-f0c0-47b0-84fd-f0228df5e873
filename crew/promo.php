<?php
/*
  	$Id: promo.php,v 1.1 2011/03/25 08:14:13 weesiong Exp $
*/

require('includes/application_top.php');
require(DIR_WS_CLASSES . 'promo.php');
require(DIR_WS_LANGUAGES . $language . '/' . FILENAME_SEARCH_LATEST_NEWS);

$redirect_to_mainpage = false;
$promo = new promo();

if ($promo->is_promo_enabled()) {
	$promo->set_cookie_name('promo_paypal');
	
	if (!$promo->promo_cookies_exist()) {
		$promo->create_cookie();
	}
} else {
	$redirect_to_mainpage = true;
}

if ($redirect_to_mainpage) {
	tep_redirect(tep_href_link('/'));
}

$content = CONTENT_PROMO;

require(DIR_WS_TEMPLATES . TEMPLATENAME_MAIN_PAGE);

require(DIR_WS_INCLUDES . 'application_bottom.php');
?>