<?
require('includes/application_top.php');

tep_redirect(HTTP_SHASSO_PORTAL . '/wor/index');

include_once(DIR_WS_CLASSES . 'store_point.php');

if (!tep_session_is_registered('customer_id')) {
	$navigation->set_snapshot();
    tep_redirect(tep_href_link(FILENAME_LOGIN, '', 'SSL'));
}

$sp_object = new store_point($customer_id, 'customer');

require(DIR_WS_LANGUAGES . $language . '/' . FILENAME_REDEEM_POINT);

$action = isset($_REQUEST['action']) ? $_REQUEST['action'] : '';

if (tep_not_null($action)) {
	switch($action) {
		case 'redeem_point':
			$store_points_redeem_id = $sp_object->redeem_store_point();
			if ($store_points_redeem_id > 0) {
				$store_points_redeem_amount_select_sql = "	SELECT store_points_redeem_amount 
															FROM " . TABLE_STORE_POINTS_REDEEM . " 
															WHERE store_points_redeem_id = '" . (int)$store_points_redeem_id . "' 
																AND user_id = '" . (int)$customer_id . "' 
																AND user_role = 'customer'";
				$store_points_redeem_amount_result_sql = tep_db_query($store_points_redeem_amount_select_sql);
				if (tep_db_num_rows($store_points_redeem_amount_result_sql) > 0) {
					tep_redirect(tep_href_link(FILENAME_REDEEM_POINT, 'redeem=' . $store_points_redeem_id, 'SSL'));
				} else {
					tep_redirect(tep_href_link(FILENAME_REDEEM_POINT, '', 'SSL'));
				}
			} else {
				tep_redirect(tep_href_link(FILENAME_REDEEM_POINT, '', 'SSL'));
			}
			
			break;
	}
}

$breadcrumb->add(NAVBAR_TITLE_1, tep_href_link(FILENAME_ACCOUNT, '', 'SSL'));
$breadcrumb->add(NAVBAR_TITLE_2, tep_href_link(FILENAME_REDEEM_POINT, '', 'SSL'));

$content = CONTENT_REDEEM_POINT;

require(DIR_WS_TEMPLATES . TEMPLATENAME_MAIN_PAGE);
require(DIR_WS_INCLUDES . 'application_bottom.php');
?>