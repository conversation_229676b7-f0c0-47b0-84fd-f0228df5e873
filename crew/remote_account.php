<?
/*
  	$Id: remote_account.php,v 1.3 2009/09/28 07:09:22 chan Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2003 osCommerce
	
  	Released under the GNU General Public License
*/

require('includes/application_top.php');
require_once(DIR_WS_MODULES . 'anti_fraud/maxmind/db/geoip.inc');

if (isset($_POST['action']) && JOOMLA_REMOTE_ACCOUNT_KEY == $_POST['key']) {
	if ($_POST['action'] == 'login') {
		$ch = curl_init($_SERVER['SERVER_NAME'] . '/login.php');
		
		curl_setopt($ch, CURLOPT_POST          ,1);
		curl_setopt($ch, CURLOPT_POSTFIELDS        ,"action=process&email_address=".$_POST['email_address']."&password=".$_POST['password']."&OFFGAMERS=".$_POST['OFFGAMERS']);
		curl_setopt($ch, CURLOPT_COOKIEJAR, "/");
		curl_setopt($ch, CURLOPT_COOKIEFILE, "/");
		curl_setopt($ch, CURLOPT_FOLLOWLOCATION    ,1);
		curl_setopt($ch, CURLOPT_HEADER            ,0);    // DO NOT RETURN HTTP HEADERS
		curl_setopt($ch, CURLOPT_RETURNTRANSFER    ,1);    // RETURN THE CONTENTS OF THE CALL
		$curl_result = curl_exec($ch);
	}  elseif ($_POST['action'] == 'register') {
		if (tep_not_null($_POST['name']) && tep_not_null($_POST['email_address']) && tep_not_null($_POST['password'])) {
			$name = tep_not_null($_POST['name']) ? tep_db_prepare_input(strip_tags($_POST['name'])) : "";
			$email_address = tep_not_null($_POST['email_address']) ? tep_db_prepare_input(strip_tags($_POST['email_address'])) : "";
			$password = tep_not_null($_POST['password']) ? tep_db_prepare_input(strip_tags($_POST['password'])) : "";
			$create_acc_ip = tep_not_null($_POST['ip']) ? tep_db_prepare_input(strip_tags($_POST['ip'])) : '';
			$selected_country_id = 0;
			
			$insert_customer_query = "INSERT INTO customers (customers_firstname, customers_email_address, customers_password) VALUES ('".tep_db_input($name)."', '".tep_db_input($email_address)."', '".tep_encrypt_password($password)."')";
			tep_db_query($insert_customer_query);
			echo mysql_insert_id();
			
			$customers_id = tep_db_insert_id();
			
			$gi = geoip_open(DIR_WS_MODULES . 'anti_fraud/maxmind/db/GeoIP.dat', GEOIP_STANDARD);
			$countries_iso_code_2 = geoip_country_code_by_addr($gi, $create_acc_ip);
			
			$country_info_array = tep_get_countries_info($countries_iso_code_2, 'countries_iso_code_2');
			
			if (isset($country_info_array['id']) && tep_not_null($country_info_array['id'])) {
				$selected_country_id = $country_info_array['id'];
			}
			
			$customer_info_data_array = array(	'customers_info_id' => $customers_id,
												'customers_info_date_of_last_logon' => 'now()',
												'customers_info_number_of_logons' => 1,
												'customers_info_date_account_created' => 'now()',
												'customers_info_account_created_ip' => $create_acc_ip,
												'customers_info_account_created_from' => 0,
												'customer_info_selected_country' => $selected_country_id
											);
			
			tep_db_perform(TABLE_CUSTOMERS_INFO, $customer_info_data_array);
		}
	}
}
?>