<?php
require('includes/application_top.php');
if (tep_not_null($_REQUEST['action']) && (strpos($_SERVER['HTTP_REFERER'], 'upload.php') === 0)) {
	switch ($_REQUEST['action']) {
		case 'checking':
			if (isset($_REQUEST['bb_req_grp_id']) && isset($_REQUEST['customer_id'])) {
				$check_uploaded_select_sql = "SELECT br.buyback_request_screenshot_before_name ,br.buyback_request_screenshot_after_name 
												FROM " . TABLE_BUYBACK_REQUEST_GROUP . " AS brg 
												INNER JOIN ".TABLE_BUYBACK_REQUEST." AS br 
													ON (brg.buyback_request_group_id = br.buyback_request_group_id)
												WHERE br.buyback_request_group_id = '".tep_db_input($_REQUEST['bb_req_grp_id'])."' 
													AND brg.buyback_status_id <> '4' 
													AND brg.customers_id = '".$_REQUEST['customer_id']."'";
				$check_uploaded_result_sql = tep_db_query($check_uploaded_select_sql);
				if ($check_uploaded_row = tep_db_fetch_array($check_uploaded_result_sql)) {
					if (!tep_not_null($check_uploaded_row['buyback_request_screenshot_before_name']) && !tep_not_null($check_uploaded_row['buyback_request_screenshot_after_name'])) {
						echo 'Approved';
						return true;
					}
				}
			}
			
			echo 'Denied';
		break;
		case 'update_buyback_ss':
			$comments = '';
			$last_update_age = 0;
			$approved = false;
			
			if (isset($_REQUEST['bb_req_grp_id']) && isset($_REQUEST['customer_id'])) {
				$check_uploaded_select_sql = "SELECT br.buyback_request_screenshot_before_name ,br.buyback_request_screenshot_after_name 
												FROM " . TABLE_BUYBACK_REQUEST_GROUP . " AS brg 
												INNER JOIN ".TABLE_BUYBACK_REQUEST." AS br 
													ON (brg.buyback_request_group_id = br.buyback_request_group_id)
												WHERE br.buyback_request_group_id = '".tep_db_input($_REQUEST['bb_req_grp_id'])."' 
													AND brg.customers_id = '".$_REQUEST['customer_id']."'";
				$check_uploaded_result_sql = tep_db_query($check_uploaded_select_sql);
				if ($check_uploaded_row = tep_db_fetch_array($check_uploaded_result_sql)) {
					if (!tep_not_null($check_uploaded_row['buyback_request_screenshot_before_name']) && !tep_not_null($check_uploaded_row['buyback_request_screenshot_after_name'])) {
						$approved = true;
					}
				}
				
				if ($approved) {
					$get_date_added_processing_select_sql = "	SELECT bsh.date_added 
																FROM " . TABLE_BUYBACK_STATUS_HISTORY . " AS bsh 
																WHERE bsh.buyback_request_group_id = '".tep_db_input($_REQUEST['bb_req_grp_id'])."' 
																AND bsh.buyback_status_id = '2' ORDER BY bsh.date_added DESC LIMIT 1
																";
					$get_date_added_processing_result_sql = tep_db_query($get_date_added_processing_select_sql);
					if ($get_date_added_processing_row = tep_db_fetch_array($get_date_added_processing_result_sql)) {
						$last_update_age = tep_calculate_age($get_date_added_processing_row['date_added'], '', 1, true);
						
						$update_bb_ss_array = array('buyback_request_screenshot_before_name' => 'delivery_'.tep_db_input($_REQUEST['bb_req_grp_id']).'_1.jpg', 'buyback_request_screenshot_after_name' => 'delivery_'.tep_db_input($_REQUEST['bb_req_grp_id']).'_2.jpg');
						tep_db_perform(TABLE_BUYBACK_REQUEST, $update_bb_ss_array, 'update', "buyback_request_group_id='".tep_db_input($_REQUEST['bb_req_grp_id'])."'");
						
						$comments = 'Screenshot uploaded after Processing '.$last_update_age;
						
						$buyback_history_data_array = array('buyback_request_group_id' => $_REQUEST['bb_req_grp_id'],
									                        'buyback_status_id' => '',
															'date_added' => 'now()',
															'customer_notified' => '0',
															'comments' => $comments,
															'changed_by' => 'system'
															);
						tep_db_perform(TABLE_BUYBACK_STATUS_HISTORY, $buyback_history_data_array);
						echo 'success';
					}
				}
			}
			
			echo 'failed';
		break;
	}
}



?>