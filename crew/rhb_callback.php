<?
/*
	$Id: rhb_callback.php,v 1.2 2015/04/01 03:20:38 sionghuat.chng Exp $
  	Author : 	<PERSON> (<EMAIL>)
  	
	Copyright (c) 2005
  	Released under the GNU General Public License
*/

require('includes/application_top.php');

$form_data = array();
$data = '';
if (isset($_REQUEST) && count($_REQUEST)) {
	foreach($_REQUEST as $key => $value) {
		$form_data[$key] = $value;
	}
}

$url = 'https://www.offgamers.com/pgs/index.php/payment/CheckoutSuccess';

?>
<html>
	<head></head>
	<body onload="return document.rhb_payment_success.submit();">
<?
		echo "\n".tep_draw_form('rhb_payment_success', $url, 'get');
		
		reset($form_data);
		foreach ($form_data as $form_key_loop => $form_data_loop) {
			echo tep_draw_hidden_field($form_key_loop, htmlspecialchars(stripslashes($form_data_loop)));
		}
?>
		</form>
	</body>
</html>