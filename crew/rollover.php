<SCRIPT>
function preload(imgObj,imgSrc) {
	if (document.images) {
		eval(imgObj+' = new Image()')
		eval(imgObj+'.src = "'+imgSrc+'"')
	}
}
function changeImage(layer,imgName,imgObj) {
	if (document.images) {
		if (document.layers && layer!=null) eval('document.layers["'+layer+'"].document.images["'+imgName+'"].src = '+imgObj+'.src')
		else document.images[imgName].src = eval(imgObj+".src")
	}
}
</SCRIPT>
<?PHP
if( !isset( $rollover2_class_loaded))
{
    $rollover2_class_loaded = 1; 
 
    class rollover2 
    {
      var $images = array(); // Array for all images
      var $imagedir = './images/'; // path to image files
      var $jsincludepath = './'; // path to javascript image.js  './' means actual directory
      var $window_defaultstatus = ''; // default status for status bar message
      
      function addimage( $imgname, $baseimg, $selimg, $downimg='', $alt='', $link='#', $target='', $layer='', $map='' )
      {
        $this->images[$imgname] = 
          array( 'layer' => $layer,     // the layer name if it is on a named layer
                 'baseimg' => $baseimg, // unselected image file
                 'selimg' => $selimg,   // selected image file
                 'downimg' => $downimg, // if $PHP_SELF is the link, show this image
                 'alt' => $alt,         // mouseover hint text
                 'link' => $link,       // url to document
                 'target' => $target,   // target window for new document
                 'map' => $map );       // client image map name <map name="my_name"><area ... /area></map>
      }
      
      function preload()
      {
        # MUST BE CALLED WITHIN <HEAD></HEAD> SECTION
        # includes images.js and defines preload script section
          
        //echo '<script type="text/javascript" language="javascript" src="'. 
          //$this->jsincludepath.'images.js"></script>'.
          echo '<script type="text/javascript" language="javascript">'."\n".
          # set window.defaultstatus for onmouseout=..
          "//<!--\nwindow.defaultStatus='".$this->window_defaultstatus."';\n";
        
        while( list( $key, $val ) = each( $this->images ) )
        {
          if( $val['downimg'] > '' && $this->i_am_this_url( $val['link'] ))
          {
            echo '// no preloads for '.$key.': url is '.$val['link']."\n";
          }
          else
          {
            echo "preload('$key"."bas','".$this->imagedir.$val['baseimg']."');\n" ;
            echo "preload('$key"."sel','".$this->imagedir.$val['selimg'] ."');\n" ;
          }
        }
        
        echo '//-->'."\n".'</script>'."\n";
      }
      
      function i_am_this_url( $url )
      {
        # global $PHP_SELF; changed 2003-01-24
				global $REQUEST_URI;
				$urll = strtolower( $url );
				$phps = strtolower( $REQUEST_URI );
				
        $shortest = strlen( $urll );
        $shortest = strlen( $phps ) < $shortest ? strlen( $phps ) : $shortest;
        return( substr($urll, -$shortest) == substr( $phps, -$shortest ) );
      }
      
      function image( $imgname )
      {
        $size = GetImageSize($this->imagedir . $this->images[$imgname]['baseimg'] ); 
        $l = $this->images[$imgname]['layer'] == '' ? 'null' : "'".$this->images[$imgname]['layer']."'";
        $t = $this->images[$imgname]['target'] == '' ? ' ' : ' target="'.$this->images[$imgname]['target'].'" ';
        $map = $this->images[$imgname]['map'] == '' ? '' : ' ISMAP USEMAP="#'.$this->images[$imgname]['map'].'" ';
    
        if( ($this->images[$imgname]['downimg'] > '') && 
            ($this->i_am_this_url( $this->images[$imgname]['link'] ) ) )
        {
            # show state 3: downimage ($this->link == reached)
            echo  "<img name='". 
            $imgname . "' border=0 width=" . $size[0] . 
            " height=" . $size[1] .$map. " src='" . $this->imagedir .  
            $this->images[$imgname]['downimg'] . 
            "' alt='" . $this->images[$imgname]['alt'] . 
            "'>" ;
        }
        else
        {
            # show normal rollover image 
            echo '<a href="' . $this->images[$imgname]['link'].'"'.$t. 
            "onMouseOver=\"javascript:changeImage( $l ,'" . 
            $imgname ."','".$imgname . 
            "sel');".
            "window.status='".$this->images[$imgname]['alt']."';return true;\" ".
            "onMouseOut=\"javascript:changeImage( $l,'".
            $imgname ."','".$imgname . "bas');window.status='".
            $this->window_defaultstatus."';return true;\"><img name='" . 
            $imgname . "' border=0 width=" . $size[0] . 
            " height=" . $size[1] . $map. " src='" . $this->imagedir .  
            $this->images[$imgname]['baseimg'] . 
            "' alt='" . $this->images[$imgname]['alt'] . 
            "'></a>" ;
        } // normal rollover image
      } // end function image()
    } // end class rollover2
} // if( !isset( $rollover2_class_loaded))
?>