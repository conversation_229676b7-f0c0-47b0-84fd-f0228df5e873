<?
/*
	$Id: sc_checkout_payment.php,v 1.9 2015/05/28 10:51:32 darren.ng Exp $

  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com

  	Copyright (c) 2003 osCommerce

  	Released under the GNU General Public License
*/

require('includes/application_top.php');

require_once(DIR_WS_CLASSES . 'sc_shopping_cart.php');
require_once(DIR_WS_CLASSES . 'store_credit.php');

// if the customer is not logged on, redirect them to the login page
if (!tep_session_is_registered('customer_id')) {
	$navigation->set_snapshot();
    tep_redirect(tep_href_link(FILENAME_LOGIN, '', 'SSL'));
}

$action = '';
if (isset($_REQUEST['action'])) $action = $_REQUEST['action'];

switch ($action) {
	case 'buy_now':
		if (isset($_REQUEST['products_id']) && (int)$_REQUEST['products_id'] > 0) {
			$products_select_sql = "	SELECT p.products_id, p.custom_products_type_id 
										FROM " . TABLE_PRODUCTS . " AS p 
										WHERE p.products_id = '".(int)$_REQUEST['products_id']."'
											AND p.custom_products_type_id = '3' 
											AND p.products_status = 1 
											AND p.products_display = 1 ";
			$products_result_sql = tep_db_query($products_select_sql);
			if ($products_row = tep_db_fetch_array($products_result_sql)) {
				tep_db_query("DELETE FROM " . TABLE_CUSTOMERS_SC_CART . " WHERE customers_id = '" . (int)$_SESSION['customer_id'] . "'");
				if (isset($_REQUEST['customqty']) && (int)$_REQUEST['customqty']) {
					$customers_sc_cart_array = array(	'customers_id' => (int)$_SESSION['customer_id'],
														'products_id' => $products_row['products_id'],
														'customers_sc_cart_quantity' => (int)$_REQUEST['customqty'],
														'customers_sc_cart_date_added' => 'now()');
					tep_db_perform(TABLE_CUSTOMERS_SC_CART, $customers_sc_cart_array);
				} else {
					tep_redirect(tep_href_link(FILENAME_ACCOUNT_STORE_CREDIT_TOPUP, tep_get_all_get_params(array('action', 'cPath')), 'SSL'), 'post');
				}
			} else {
				tep_redirect(tep_href_link(FILENAME_ACCOUNT_STORE_CREDIT_TOPUP, tep_get_all_get_params(array('action', 'cPath')), 'SSL'), 'post');
			}
		}
		break;
}

$customers_ordered_sc = 0;
$customers_sc_select_sql = "SELECT cscq.customers_sc_cart_quantity, p.custom_products_type_id, p.products_price 
							FROM " . TABLE_CUSTOMERS_SC_CART . " AS cscq 
							INNER JOIN " . TABLE_PRODUCTS . " AS p 
								ON p.products_id = cscq.products_id 
							WHERE cscq.customers_id = '" . (int)$_SESSION['customer_id'] . "'";
$customers_sc_result_sql = tep_db_query($customers_sc_select_sql);
if (!$customers_sc_row = tep_db_fetch_array($customers_sc_result_sql)) {
	tep_redirect(tep_href_link(FILENAME_ACCOUNT_STORE_CREDIT_TOPUP, tep_get_all_get_params(array('action', 'cPath')), 'SSL'), 'post');
}

$currency_currency_code = tep_get_customer_store_credit_currency($_SESSION['customer_id'], false);	// SC Product currency always follow Customer SC Balance Currency
$minimum_sc = store_credit::get_minimum_store_credit($currency_currency_code);

if ($customers_sc_row['custom_products_type_id'] == 3) {
	$customers_ordered_sc = $customers_sc_row['customers_sc_cart_quantity'];
	
	if ($customers_sc_row['customers_sc_cart_quantity'] < $minimum_sc) {
		tep_redirect(tep_href_link(FILENAME_ACCOUNT_STORE_CREDIT_TOPUP, tep_get_all_get_params(array('action', 'cPath')), 'SSL'), 'post');
	}
} else {
	$customers_ordered_sc = $customers_sc_row['products_price'];
}
$customers_sc_currency = $currency;

$sc_currencies_select_sql = "	SELECT c.code 
								FROM " . TABLE_COUPON_GV_CUSTOMER . " AS cgc
								INNER JOIN " . TABLE_CURRENCIES . " AS c
									ON cgc.sc_currency_id = c.currencies_id
								WHERE cgc.customer_id = '" . (int)$_SESSION['customer_id'] . "'";
$sc_currencies_result_sql = tep_db_query($sc_currencies_select_sql);
if ($sc_currencies_row = tep_db_fetch_array($sc_currencies_result_sql)) {
	$customers_sc_currency = $sc_currencies_row['code'];
}

// if no billing destination address was selected, use the customers own address as default
if (!tep_session_is_registered('billto')) {
	tep_session_register('billto');
    $billto = $_SESSION['customer_default_address_id'];
} else {
	// verify the selected billing address
    $check_address_query = tep_db_query("select count(address_book_id) as total from " . TABLE_ADDRESS_BOOK . " where customers_id = '" . (int)$_SESSION['customer_id'] . "' and address_book_id = '" . (int)$billto . "'");
    $check_address = tep_db_fetch_array($check_address_query);

    if ($check_address['total'] != '1') {
    	$billto = $_SESSION['customer_default_address_id'];
      	if (tep_session_is_registered('payment')) tep_session_unregister('payment');
	}
}

// a session array storing firstname and lastname for 2CO credit card payment
if (!tep_session_is_registered('pm_2CO_cc_owner')) tep_session_register('pm_2CO_cc_owner');
if (!tep_session_is_registered('credit_card_owner')) tep_session_register('credit_card_owner');
if (!tep_session_is_registered('payment_inputs_array')) tep_session_register('payment_inputs_array');

require(DIR_WS_CLASSES . 'order.php');
$order = new order;

if ($order->info['subtotal'] > 0) {
	;	// These ordered products has price value.
} else {
	$payment = '';
	/*	Allow 0.00 Checkout (we have some free gift keys)
	$messageStack->add_session('add_to_cart', WARNING_ORDER_AMOUNT_ZERO, 'error');
	tep_redirect(tep_href_link(FILENAME_ACCOUNT_STORE_CREDIT_TOPUP));
	break;
	*/
}

$_SESSION['sc_gst'] = 0; // ot_gst charge
$_SESSION['sc_gst_checked'] = false;

require(DIR_WS_CLASSES . 'order_total.php');//ICW ADDED FOR CREDIT CLASS SYSTEM
$order_total_modules = new order_total;//ICW ADDED FOR CREDIT CLASS SYSTEM
$ot_surcharge->fees_type = 'sc_topup';  // SC Top Up has own Surcharge setting
$order_total_modules->pre_confirmation_check();	// Need it here to decide show/hide payment method options

/*-- GST :: check status and calculate GST amount for page init load --*/
$gst_amt = 0;
localization::verify_gst_condition();

if (tep_not_null($_SESSION['RegionGST']) && (($_SESSION['RegionGST']['loc_country'] != $country) || ($_SESSION['RegionGST']['currency'] != $currency))) {
	$country = $_SESSION['RegionGST']['loc_country'];
	$currency = $_SESSION['RegionGST']['currency'];
	$_SESSION['currency'] = $_SESSION['RegionGST']['currency'];
	tep_setcookie('RegionalSet[loc_country]', $country, $cookies_expired, $cookie_path, $cookie_domain);
	tep_setcookie('RegionalSet[currency]', $currency, $cookies_expired, $cookie_path, $cookie_domain);
}

if (tep_not_null($_SESSION['RegionGST']['tax_title'])) {
	$ot_gst->sc_process();
	$gst_amt = tep_not_null($ot_gst->output[0]['value']) ? $currencies->advance_currency_conversion($ot_gst->output[0]['value'], DEFAULT_CURRENCY, $_SESSION['currency']) : 0;
}

$total_weight = $cart->show_weight();
$total_count = $cart->count_contents();
$total_count = $cart->count_contents_virtual(); //ICW ADDED FOR CREDIT CLASS SYSTEM

if (isset($HTTP_GET_VARS['payment'])) {
	if (!tep_session_is_registered('payment')) tep_session_register('payment');
	$payment = $HTTP_GET_VARS['payment'];
}

$orig_cart = $cart;
$cart = new sc_shopping_cart;
$cart->restore_contents();

// load all enabled payment modules
require(DIR_WS_CLASSES . 'payment.php');
$payment_modules = new payment;

$order = new order('', true);
$opContent = $cart->calculateRebatePoint();

$cart = $orig_cart;
$order = new order();

require(DIR_WS_LANGUAGES . $language . '/' . FILENAME_SC_CHECKOUT_PAYMENT);

$breadcrumb->add(NAVBAR_TITLE_1, tep_href_link(FILENAME_ACCOUNT, '', 'SSL'));	// remember change this link
$breadcrumb->add(NAVBAR_TITLE_2, tep_href_link(FILENAME_SC_CHECKOUT_PAYMENT, '', 'SSL'));	// remember change this link

$content = CONTENT_SC_CHECKOUT_PAYMENT;
$javascript = 'checkout_payment.js.php';

require(DIR_WS_TEMPLATES . TEMPLATENAME_MAIN_PAGE);

// restore shopping cart
$cart = new shoppingCart;
$cart->restore_contents();

require(DIR_WS_INCLUDES . 'application_bottom.php');
?>