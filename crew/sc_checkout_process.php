<?
/*
  	$Id: sc_checkout_process.php,v 1.20 2015/05/28 10:51:32 darren.ng Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2003 osCommerce
	
  	Released under the GNU General Public License
*/

include('includes/application_top.php');
require_once(DIR_WS_CLASSES . 'payment_methods.php');

require_once(DIR_WS_CLASSES . 'sc_shopping_cart.php');
require_once(DIR_WS_CLASSES . 'store_credit.php');
require_once(DIR_WS_CLASSES . 'anti_fraud.php');
require_once(DIR_WS_CLASSES . 'user.php');

// if the customer is not logged on, redirect them to the login page
if (!tep_session_is_registered('customer_id')) {
	$navigation->set_snapshot(array('mode' => 'SSL', 'page' => FILENAME_SC_CHECKOUT_PAYMENT));
    tep_redirect(tep_href_link(FILENAME_LOGIN, '', 'SSL'));
}

$payment_methods_obj = new payment_methods('count_payment_methods');
if ( ((int)$payment_methods_obj->payment_methods_size > 0) && (!tep_session_is_registered('payment')) ) {
	tep_redirect(tep_href_link(FILENAME_SC_CHECKOUT_PAYMENT, '', 'SSL'));
}

$customers_sc_currency = $currency;
$sc_currencies_select_sql = "	SELECT c.code 
								FROM " . TABLE_COUPON_GV_CUSTOMER . " AS cgc
								INNER JOIN " . TABLE_CURRENCIES . " AS c
									ON cgc.sc_currency_id = c.currencies_id
								WHERE cgc.customer_id = '" . (int)$_SESSION['customer_id'] . "'";
$sc_currencies_result_sql = tep_db_query($sc_currencies_select_sql);
if ($sc_currencies_row = tep_db_fetch_array($sc_currencies_result_sql)) {
	$customers_sc_currency = $sc_currencies_row['code'];
}

require_once(DIR_WS_CLASSES . 'sc_shopping_cart.php');
$cart = new sc_shopping_cart;
$cart->restore_contents();

// if the quantity of any products in cart exceed the current stock amount, redirect customer 
// back to view cart page to modify the quantity
if (!$cart->checkout_permission()) {
	$_SESSION['cart_update_error'] = TEXT_STOCK_NOT_AVAILABLE;
	
	$store_credit_cPath = '';
	$store_credit_select_sql = "	SELECT DISTINCT c.categories_id, c.parent_id, c.categories_parent_path 
									FROM " . TABLE_CATEGORIES . " AS c 
									INNER JOIN " . TABLE_CATEGORIES_GROUPS . " AS cg 
										ON (c.categories_id = cg.categories_id) 
									WHERE c.categories_status = 1 
										AND c.custom_products_type_id = '3'
										AND ((cg.groups_id = '".$customers_groups_id. "') OR (cg.groups_id = 0)) 
									ORDER BY c.categories_id";
	$store_credit_result_sql = tep_db_query($store_credit_select_sql);
	if ($store_credit_row = tep_db_fetch_array($store_credit_result_sql)) {
		if ($store_credit_row['parent_id'] > 0) {
			$store_credit_cPath = substr($store_credit_row['categories_parent_path'], 1).$store_credit_row['categories_id'];
		} else {
			$store_credit_cPath = $store_credit_row['categories_id'];
		}
	}
	tep_redirect(tep_href_link(FILENAME_ACCOUNT_STORE_CREDIT_TOPUP, tep_get_all_get_params(array('action', 'cPath')) . 'cPath='.$store_credit_cPath, 'SSL'), 'post');
}

include(DIR_WS_LANGUAGES . $language . '/' . FILENAME_SC_CHECKOUT_PROCESS);

require(DIR_WS_CLASSES . 'cart_comments.php');
$cart_comments_obj = new cart_comments('');

$missing_payment = false;

// load selected payment module
require(DIR_WS_CLASSES . 'payment.php');

/*if ($credit_covers && $_SESSION['cot_gv']) {	// Must include $_SESSION['cot_gv'] checking to prevent customer go back to checkout_payment.php and directly go to checkout_process.php which result in missing payment method
	$payment=''; //ICW added for CREDIT CLASS
} else {*/
	if ($payment == '') {
		$missing_payment = true;
	}
//}

if (!tep_session_is_registered('order_logged')) {
	if (tep_not_null($payment) && !preg_match('/^(pm_)(\d+)(:~:)(\S+)(:~:)(\d+)$/', $payment)) {
		tep_redirect(tep_href_link(FILENAME_SC_CHECKOUT_PAYMENT, 'payment_error=ccerr&error_message=' . urlencode(ERROR_NO_PAYMENT_MODULE_SELECTED), 'SSL'));
	} else {
        $_SESSION['selected_payment_info'] = $payment;
    }
}

$payment_modules = new payment($payment);
if (isset($$payment) && method_exists($$payment, 'get_pm_status') && (int)$$payment->get_pm_status()!=1) {
	tep_redirect(tep_href_link(FILENAME_SC_CHECKOUT_PAYMENT, 'payment_error=ccerr&error_message=' . urlencode(ERROR_NO_PAYMENT_MODULE_SELECTED), 'SSL'));
}
if (!isset($_SESSION['order_logged'])) {
	if (isset($$payment->filename) && in_array($$payment->filename, array('paypal.php', 'paypalEC.php', 'adyen.php'))) {
		$is_customer_require_qna = validate_customer_domant_account($_SESSION['customer_id']);
		if ($is_customer_require_qna === true) {
			$_SESSION['need_sc_usage_qna'] = true;
			$_SESSION['rp_qna_flag'] = true;
			tep_redirect(tep_href_link(FILENAME_SC_CHECKOUT_PAYMENT, '', 'SSL'));
			exit;
		}
	}
}
if ($payment != '' && $payment_modules->get_confirm_complete_days() > 0) {
    // check is customer billing info / mobile number are updated and verified.
    $user_obj = new user();
    $is_billing_info_complete = $user_obj->is_billing_info_completed();
    unset($user_obj);
    
    if ($is_billing_info_complete !== TRUE) {
        tep_redirect(tep_href_link(FILENAME_SC_CHECKOUT_PAYMENT, 'payment_error=ccerr&error_message=' . urlencode(ERROR_CHECKOUT_MISSING_BILLING_INFO), 'SSL'));
    }
}

// load the selected shipping module
require(DIR_WS_CLASSES . 'shipping.php');
$shipping_modules = new shipping($shipping);

require(DIR_WS_CLASSES . 'order.php');
$order = new order('', true);

if ($order->info['subtotal'] > 0) {
	;	// These ordered products has price value.
} else {
	$payment = '';
	$missing_payment = false;
}

if (!((isset($$payment->form_action_url) && !tep_session_is_registered('order_logged')) || !isset($$payment->form_action_url))) {
	if (isset($order_logged) && (int)$order_logged>0) {
		$payment_methods_select_sql = "	SELECT payment_methods_id 
										FROM " . TABLE_ORDERS ." 
										WHERE orders_id = '".$order_logged."'";
		$payment_methods_result_sql = tep_db_query($payment_methods_select_sql);
		if ($payment_methods_row = tep_db_fetch_array($payment_methods_result_sql)) {
			$payment_modules = new payment('pm_'.$payment_methods_row['payment_methods_id'].':~:'.$payment.':~:'.$payment_methods_row['payment_methods_id']);
		}
	}
}

if ($missing_payment || (tep_not_null($payment) && !$payment_modules->is_supported_currency($order->info['currency']))) {
	tep_redirect(tep_href_link(FILENAME_SC_CHECKOUT_PAYMENT, 'payment_error=ccerr&error_message=' . urlencode(ERROR_NO_PAYMENT_MODULE_SELECTED), 'SSL'));
}

//************************************************************
// Authorizenet ADC Direct Connection
// Make sure the /catalog/includes/class/order.php is included
// and $order object is created before this!!!
if (MODULE_PAYMENT_AUTHORIZENET_STATUS == 'True') {
	include(DIR_WS_MODULES . 'authorizenet_direct.php');
}
//************************************************************

// load the before_process function from the payment modules
$is_sc_checkout = true;
$payment_modules->before_process();

require(DIR_WS_CLASSES . 'order_total.php');
$order_total_modules = new order_total;
$ot_surcharge->fees_type = 'sc_topup';  // SC Top Up has own Surcharge setting

/*-- GST :: return to SC CHECKOUT PAYMENT page when country and currency changed --*/
localization::verify_gst_condition();

if (tep_not_null($_SESSION['RegionGST']) && (($_SESSION['RegionGST']['loc_country'] != $country) || ($_SESSION['RegionGST']['currency'] != $currency))) {
	tep_redirect(tep_href_link(FILENAME_SC_CHECKOUT_PAYMENT, '', 'SSL'));
}

$temp_process_select_sql = "SELECT temp_id, DATE_ADD(created_date, INTERVAL 300 SECOND) > now() as check_time
							FROM " . TABLE_TEMP_PROCESS . "
							WHERE page_name = 'checkout_process.php' 
								AND match_case = '".$_SESSION['customer_id']."'";
$temp_process_result_sql = tep_db_query($temp_process_select_sql);
if ($temp_process_row = tep_db_fetch_array($temp_process_result_sql)) {	
	if ((int)$temp_process_row['check_time']) {
		//tep_redirect(tep_href_link(FILENAME_SC_CHECKOUT_PAYMENT, 'payment_error=ccerr&error_message=' . urlencode(ERROR_ORDERS_IN_PROGRESS), 'SSL'));
	} else {
		$temp_process_data_sql = array(	'created_date' => 'now()');
		tep_db_perform(TABLE_TEMP_PROCESS, $temp_process_data_sql, 'update', " temp_id = '".(int)$temp_process_row['temp_id']."' ");
	}
} else {
	$temp_process_data_sql = array(	'page_name' => 'checkout_process.php' ,
									'match_case' => $_SESSION['customer_id'],
									'created_date' => 'now()');
	tep_db_perform(TABLE_TEMP_PROCESS, $temp_process_data_sql);
}

//ICW ADDED FOR CREDIT CLASS SYSTEM
$order_total_modules->pre_confirmation_check();

// Remove in next rollout
$full_store_credit = $payment_modules->check_credit_covers();
//$_SESSION['full_store_credit_checkout'] = $full_store_credit;  // For PG Surcharge, may need to remove

if ($order->info['subtotal'] > 0) {
	if ((!$full_store_credit) && (!isset($$payment) || !tep_not_null($payment))) {
		tep_redirect(tep_href_link(FILENAME_SC_CHECKOUT_PAYMENT, 'payment_error=ccerr&error_message=' . urlencode(ERROR_NO_PAYMENT_MODULE_SELECTED), 'SSL'));
	} else if ($full_store_credit && (isset($$payment) || tep_not_null($payment))) {
		tep_redirect(tep_href_link(FILENAME_SC_CHECKOUT_PAYMENT, 'payment_error=ccerr&error_message=' . urlencode(ERROR_NO_PAYMENT_MODULE_NEEDED), 'SSL'));
	}
}

if ( (isset($$payment->form_action_url) && !tep_session_is_registered('order_logged')) || !isset($$payment->form_action_url)) {
	validate_max_pending_order($_SESSION['customer_id'], 'SC');
	$order_totals = $order_total_modules->process(true);
	// BOF: WebMakers.com Added: Downloads Controller
	$sql_data_array = array('customers_id' => $_SESSION['customer_id'],
	                        'customers_name' => $order->customer['firstname'] . ' ' . $order->customer['lastname'],
	                        'customers_company' => $order->customer['company'],
	                        'customers_street_address' => $order->customer['street_address'],
	                        'customers_suburb' => $order->customer['suburb'],
	                        'customers_city' => $order->customer['city'],
	                        'customers_postcode' => $order->customer['postcode'],
	                        'customers_state' => $order->customer['state'],
	                        'customers_country' => $order->customer['country']['title'],
	                        'customers_telephone_country' => $order->customer['telephone_country_name'],
	                        'customers_country_international_dialing_code' => $order->customer['int_dialing_code'],
	                        'customers_telephone' => $order->customer['telephone'],
	                        'customers_email_address' => $order->customer['email_address'],
	                        'customers_address_format_id' => $order->customer['format_id'],
	                        'customers_groups_id' => $order->customer['customers_groups_id'],
	                        'delivery_name' => $order->delivery['firstname'] . ' ' . $order->delivery['lastname'],
	                        'delivery_company' => $order->delivery['company'],
	                        'delivery_street_address' => $order->delivery['street_address'],
	                        'delivery_suburb' => $order->delivery['suburb'],
	                        'delivery_city' => $order->delivery['city'],
	                        'delivery_postcode' => $order->delivery['postcode'],
	                        'delivery_state' => $order->delivery['state'],
	                        'delivery_country' => $order->delivery['country']['title'],
	                        'delivery_address_format_id' => $order->delivery['format_id'],
	                        'billing_name' => $order->billing['firstname'] . ' ' . $order->billing['lastname'],
	                        'billing_company' => $order->billing['company'],
	                        'billing_street_address' => $order->billing['street_address'],
	                        'billing_suburb' => $order->billing['suburb'],
	                        'billing_city' => $order->billing['city'],
	                        'billing_postcode' => $order->billing['postcode'],
	                        'billing_state' => $order->billing['state'],
	                        'billing_country' => $order->billing['country']['title'], 
	                        'billing_address_format_id' => $order->billing['format_id'], 
	                        'payment_method' => $order->info['payment_method'], 
	                        'payment_methods_id' => $$payment->payment_methods_id,
	                        'payment_methods_parent_id' => $$payment->payment_methods_parent_id,
	                        'cc_type' => $order->info['cc_type'], 
	                        'cc_owner' => $order->info['cc_owner'], 
	                        'cc_number' => $order->info['cc_number'], 
	                        'cc_expires' => $order->info['cc_expires'], 
	                        'date_purchased' => 'now()',
	                        'last_modified' => 'now()',
	                        'orders_status' => $order->info['order_status'],
	                        'currency' => $order->info['currency'],
	                        'remote_addr' => tep_get_ip_address(),
	                        'currency_value' => $order->info['currency_value'],
	                        'pm_2CO_cc_owner_firstname' => $order->info['pm_2CO_cc_owner_firstname'],
	                        'pm_2CO_cc_owner_lastname' => $order->info['pm_2CO_cc_owner_lastname']);
	// EOF: WebMakers.com Added: Downloads Controller
	tep_db_perform(TABLE_ORDERS, $sql_data_array);
	$insert_id = tep_db_insert_id();
	
	// ------------------------------------------- AFT Module Script START -------------------------------------------
	/*$aft_obj = new anti_fraud();
	
	if($aft_obj->RP_payment_type_exist($$payment->payment_methods_id, $$payment->payment_methods_parent_id)) {
		$aft_module = $aft_obj->getAftModule();
		$aft_module->set_order_id($insert_id);
		$aft_module->set_transaction_type('CO');
		$aft_module->set_customers_id($customer_id);
		$aft_module->capture_query_call();
	}
	unset($aft_obj, $aft_module);*/
	// ------------------------------------------- AFT Module Script END -------------------------------------------
	
	$cron_orders_payment_status_data_sql = array(	'orders_id' => $insert_id, 
													'counter' => 0, 
													'check_date' => 'now()');
	tep_db_perform(TABLE_CRON_ORDERS_PAYMENT_STATUS, $cron_orders_payment_status_data_sql);
	
	# capture actual checkout country
	$orders_extra_info = array ('orders_id' => $insert_id, 
								'orders_extra_info_key' => 'ip_country', 
								'orders_extra_info_value' => tep_get_ip_country_id() );
	tep_db_perform(TABLE_ORDERS_EXTRA_INFO, $orders_extra_info);
	
	for ($i=0, $n=sizeof($order_totals); $i<$n; $i++) {
		$sql_data_array = array('orders_id' => $insert_id,
	                            'title' => $order_totals[$i]['title'],
	                            'text' => $order_totals[$i]['text'],
	                            'value' => $order_totals[$i]['value'], 
	                            'class' => $order_totals[$i]['code'], 
	                            'sort_order' => $order_totals[$i]['sort_order']);
	    tep_db_perform(TABLE_ORDERS_TOTAL, $sql_data_array);
		
		/*-- GST :: record GST percentage --*/
		if ($order_totals[$i]['code'] == 'ot_gst') {
			$orders_extra_info = array ('orders_id' => $insert_id, 
										'orders_extra_info_key' => 'gst_tax_percentage', 
										'orders_extra_info_value' => $ot_gst->gst_percentage );
			tep_db_perform(TABLE_ORDERS_EXTRA_INFO, $orders_extra_info);
		}
	}
	
	$individual_comments = $order->info['comments_array'];
	
	for ($count = 0; $count < sizeof($individual_comments); $count++) {
		$sql_data_array = array('user_comments_id' => tep_db_prepare_input($individual_comments[$count]['id']),
		      					'user_id' => $_SESSION['customer_id'],
		      					'user_comments' => tep_db_prepare_input($individual_comments[$count]['text']),
		                     	'user_role' => "customer");
		tep_db_perform(TABLE_USER_COMMENTS, $sql_data_array);
	}
	
	// for pm2checkout, do not set notification here yet since they havent made the payment
	$customer_notification = (SEND_EMAILS == 'true' && !(isset($$payment->form_action_url) && !tep_session_is_registered('order_logged')) ) ? '1' : '0';
	$sql_data_array = array('orders_id' => $insert_id, 
	                        'orders_status_id' => $order->info['order_status'], 
	                        'date_added' => 'now()', 
	                        'customer_notified' => $customer_notification,
	                        'comments' => tep_db_prepare_input($order->info['comments']));
	tep_db_perform(TABLE_ORDERS_STATUS_HISTORY, $sql_data_array);
	tep_update_orders_status_counter($sql_data_array);
	
	// initialized for the email confirmation
	$products_ordered = '';
	$subtotal = 0;
	$total_tax = 0;
	$custom_product_count = 1;
	
	for ($i=0, $n=sizeof($order->products); $i<$n; $i++) {
		// Stock Update - Joao Correia
	    if (STOCK_LIMITED == 'true') {
	    	if (DOWNLOAD_ENABLED == 'true') {
	        	$stock_query_raw = "SELECT products_quantity, products_bundle, products_bundle_dynamic, products_quantity_order, pad.products_attributes_filename 
	            	                FROM " . TABLE_PRODUCTS . " p
	                	            LEFT JOIN " . TABLE_PRODUCTS_ATTRIBUTES . " pa
										ON p.products_id=pa.products_id
	                            	LEFT JOIN " . TABLE_PRODUCTS_ATTRIBUTES_DOWNLOAD . " pad
	                             		ON pa.products_attributes_id=pad.products_attributes_id
	                            	WHERE p.products_id = '" . tep_get_prid($order->products[$i]['id']) . "'";
		
				// Will work with only one option for downloadable products
				// otherwise, we have to build the query dynamically with a loop
	        	$products_attributes = $order->products[$i]['attributes'];
	      		if (is_array($products_attributes)) {
	          		$stock_query_raw .= " AND pa.options_id = '" . $products_attributes[0]['option_id'] . "' AND pa.options_values_id = '" . $products_attributes[0]['value_id'] . "'";
	        	}
	        	$stock_query = tep_db_query($stock_query_raw);
	      	} else {
	        	$stock_query = tep_db_query("select products_quantity, products_quantity_order, products_bundle, products_bundle_dynamic from " . TABLE_PRODUCTS . " where products_id = '" . tep_get_prid($order->products[$i]['id']) . "'");
	      	}
	      	
	      	if (tep_db_num_rows($stock_query) > 0) {
		        $stock_values = tep_db_fetch_array($stock_query);
		        
		        if ($stock_values['products_bundle'] == 'yes') {
					// order item is a bundle and must be separated
					$report_text .= "Bundle found in order : " . tep_get_prid($order->products[$i]['id']) . "<br>\n";
				  	$bundle_query = tep_db_query("	select pb.subproduct_id, pb.subproduct_qty, p.products_model, p.products_quantity, p.products_bundle 
													from " . TABLE_PRODUCTS_BUNDLES . " pb 
													LEFT JOIN " . TABLE_PRODUCTS . " p 
														ON p.products_id=pb.subproduct_id 
													where pb.bundle_id = '" . tep_get_prid($order->products[$i]['id']) . "'");
		
					while ($bundle_data = tep_db_fetch_array($bundle_query)) {
						if ($bundle_data['products_bundle'] == "yes") {
							$report_text .= "<br>level 2 bundle found in order :  " . $bundle_data['products_model'] . "<br>";
				  			$bundle_query_nested = tep_db_query("select pb.subproduct_id, pb.subproduct_qty, p.products_model, p.products_quantity, p.products_bundle 
																from " . TABLE_PRODUCTS_BUNDLES . " pb 
																LEFT JOIN " . TABLE_PRODUCTS . " p 
																	ON p.products_id=pb.subproduct_id 
																where pb.bundle_id = '" . $bundle_data['subproduct_id'] . "'");
							while ($bundle_data_nested = tep_db_fetch_array($bundle_query_nested)) {
								$stock_left = $bundle_data_nested['products_quantity'] - $bundle_data_nested['subproduct_qty'] * $order->products[$i]['qty'];
								$report_text .= "updating level 2 item " . $bundle_data_nested['products_model'] . " : was " . $bundle_data_nested['products_quantity'] . " and number ordered is " . ($bundle_data_nested['subproduct_qty'] * $order->products[$i]['qty']) . " <br>\n";
								//tep_db_query("update " . TABLE_PRODUCTS . " set products_quantity = '" . $stock_left . "' where products_id = '" . $bundle_data_nested['subproduct_id'] . "'");
							}
						} else {
							$stock_left = $bundle_data['products_quantity'] - $bundle_data['subproduct_qty'] * $order->products[$i]['qty'];
							$report_text .= "updating level 1 item " . $bundle_data['products_model'] . " : was " . $bundle_data['products_quantity'] . " and number ordered is " . ($bundle_data['subproduct_qty'] * $order->products[$i]['qty']) . " <br>\n";
							//tep_db_query("update " . TABLE_PRODUCTS . " set products_quantity = '" . $stock_left . "' where products_id = '" . $bundle_data['subproduct_id'] . "'");
						}
					}
				} else {
					// order item is normal and should be treated as such
					$report_text .= "Normal product found in order : " . tep_get_prid($order->products[$i]['id']) . "\n";
					// order item is normal and should be treated as such
					$report_text .= "Normal product found in order : " . tep_get_prid($order->products[$i]['id']) . "\n";
					// do not decrement quantities if products_attributes_filename exists
			        if ((DOWNLOAD_ENABLED != 'true') || (!$stock_values['products_attributes_filename'])) {
			        	$stock_left = $stock_values['products_quantity'] - $order->products[$i]['qty'];
			          	$stock_reorder = $stock_values['products_quantity_order'];
			        } else {
			          	$stock_left = $stock_values['products_quantity'];
			        }
				}
	      	}
		}
		
		// Update products_ordered (for bestsellers list)
		tep_db_query("UPDATE " . TABLE_PRODUCTS . " SET products_ordered = products_ordered + " . sprintf('%d', $order->products[$i]['qty']) . " WHERE products_id = '" . tep_get_prid($order->products[$i]['id']) . "'");

		$customers_groups_info_array = tep_get_customer_group_discount($_SESSION['customer_id'], tep_get_prid($order->products[$i]['id']), 'product');
		$currencies->display_price($order->products[$i]['id'], $order->products[$i]['normal_price'], $order->products[$i]['tax'], $order->products[$i]['qty'], false, '', $$payment->payment_methods_id);
		//$sc_storage_normal_price = $currencies->display_noformat_price_nodiscount($order->products[$i]['id'], $order->products[$i]['normal_price']) / $currencies->get_value($currency, 'sell');
		$sc_storage_normal_price = $order->products[$i]['normal_price'] / $currencies->get_value($currency, 'sell');
		
		//$sc_storage_price = $currencies->display_noformat_price_nodiscount($order->products[$i]['id'], $order->products[$i]['price'], $order->products[$i]['tax'], 1, $order->products[$i]['discounts']) / $currencies->get_value($currency, 'sell');
		$sc_storage_price = ($order->products[$i]['price']) / $currencies->get_value($currency, 'sell');
		$cust_group_extra_rebate = 0;
        if ($$payment->payment_methods_id > 0) {
            $cust_group_extra_rebate = $currencies->rebate_point_extra;

            if ($cust_group_extra_rebate > 0) {
                $surcharge_amt = 0;
                $gst_amt = 0;
                for ($count_order_totals=0; $count_order_totals<sizeof($order_totals); $count_order_totals++) {
                    if ($order_totals[$count_order_totals]['code'] == 'ot_surcharge') {
                        $surcharge_amt = $order_totals[$count_order_totals]['value'];
                    } else if ($order_totals[$count_order_totals]['code'] == 'ot_gst') {
                        $gst_amt = $order_totals[$count_order_totals]['value'];
                    }
                }
                $cust_group_extra_rebate = floor($cust_group_extra_rebate * ( ($order->info['total']-$surcharge_amt-$gst_amt) / $order->info['subtotal']));
            }
        }
        
		if (is_array($order->products[$i]['custom_content']) && sizeof($order->products[$i]['custom_content']) > 0) {
			foreach ($order->products[$i]['custom_content'] as $num => $extra_info_array) {
				$this_package_total_qty = (int)$extra_info_array['qty'];
				$sql_data_array = array('orders_id' => $insert_id,
			                            'products_id' => tep_get_prid($order->products[$i]['id']),
			                            'products_model' => $order->products[$i]['model'],
			                            'products_name' => $order->products[$i]['name'],
			                            'orders_products_store_price' => $sc_storage_normal_price,
			                            'products_price' => $sc_storage_normal_price,
			                            'final_price' => $sc_storage_price,
			                            'products_tax' => $order->products[$i]['tax'],
			                            'products_quantity' => $this_package_total_qty,
			                            'products_pre_order' => $order->products[$i]['pre_order'],
			                            'custom_products_type_id' => $order->products[$i]['custom_products_type_id'],
			                            'products_categories_id' => $order->products[$i]['products_categories_id'],
			                            'op_rebate' => ($currencies->rebate_point + $cust_group_extra_rebate),
			                            'op_rebate_delivered' => 0);
				tep_db_perform(TABLE_ORDERS_PRODUCTS, $sql_data_array);
				$order_products_id = tep_db_insert_id();
				
				$product_query_raw = "	SELECT p.products_bundle, p.products_bundle_dynamic 
		                       			FROM " . TABLE_PRODUCTS . " AS p
		                      			LEFT JOIN " . TABLE_PRODUCTS_ATTRIBUTES . " AS pa
		                    	   			ON p.products_id=pa.products_id
		                	      		LEFT JOIN " . TABLE_PRODUCTS_ATTRIBUTES_DOWNLOAD . " AS pad
		            	           			ON pa.products_attributes_id=pad.products_attributes_id
		        	              		WHERE p.products_id = '" . tep_get_prid($order->products[$i]['id']) . "'";
				$product_query = tep_db_query($product_query_raw);
				$product_values = tep_db_fetch_array($product_query);
				
                if ($cust_group_extra_rebate > 0) {
                    $order->insert_opders_products_extra_info($order_products_id, 'OP_EXTRA', $cust_group_extra_rebate);
                    $order->insert_opders_products_extra_info($order_products_id, 'OP_FORMULA', $currencies->rebate_point_formula . '<br><br>Extra OP: ' . $cust_group_extra_rebate);
                } else {
                    $order->insert_opders_products_extra_info($order_products_id, 'OP_FORMULA', $currencies->rebate_point_formula);
                }
				
				if ($product_values['products_bundle_dynamic'] == 'yes') {
					for ($pbd_loop=0; $pbd_loop<count($order->products[$i]["bundle"]); $pbd_loop++) {
						if ($order->products[$i]["bundle"][$pbd_loop]["qty"]) {
							$dynamic_subproduct_select_sql = "	SELECT subproduct_qty 
																FROM ". TABLE_PRODUCTS_BUNDLES . "
																WHERE bundle_id = " . tep_get_prid($order->products[$i]["id"]) . "
																	AND subproduct_id = " . tep_get_prid($order->products[$i]["bundle"][$pbd_loop]["id"]) ;
							$dynamic_subproduct_result_sql = tep_db_query($dynamic_subproduct_select_sql);
							if ($dynamic_subproduct_row = tep_db_fetch_array($dynamic_subproduct_result_sql)) {
								tep_db_query("UPDATE " . TABLE_PRODUCTS . " SET products_ordered = products_ordered + " . sprintf('%d', $order->products[$i]["bundle"][$pbd_loop]["qty"]*$dynamic_subproduct_row["subproduct_qty"]) . " WHERE products_id = '" . tep_get_prid($order->products[$i]["bundle"][$pbd_loop]["id"]) . "'");
								$sql_data_array = array('orders_id' => $insert_id, 
					                            		'products_id' => tep_get_prid($order->products[$i]["bundle"][$pbd_loop]["id"]),
					                            		'products_model' => $order->products[$i]["model"],
					                            		'products_name' => $order->products[$i]["bundle"][$pbd_loop]["name"],
					                            		'orders_products_store_price' => '0.00',
					                            		'products_price' => '0.01',
					                            		'final_price' => '0.00', 
					                            		'products_tax' => $order->products[$i]["tax"], 
					                            		'products_quantity' => $order->products[$i]["bundle"][$pbd_loop]["qty"]*$dynamic_subproduct_row["subproduct_qty"],
					                            		'products_bundle_id' => tep_db_input($order->products[$i]['id']),
					                            		'products_categories_id' => $order->products[$i]["bundle"][$pbd_loop]["products_categories_id"],
					                            		'op_rebate' => 0,
					                            		'op_rebate_delivered' => 0);
					    		tep_db_perform(TABLE_ORDERS_PRODUCTS, $sql_data_array);
							}
						}
					}
			    } else if ($product_values['products_bundle'] == 'yes') {
			    	for ($static_loop = 0; $static_loop < count($order->products[$i]["static"]); $static_loop++) {
				    	if ($order->products[$i]["static"][$static_loop]["qty"]) {
				    		tep_db_query("UPDATE " . TABLE_PRODUCTS . " SET products_ordered = products_ordered + " . sprintf('%d', $order->products[$i]["static"][$static_loop]["qty"]*$order->products[$i]['qty']) . " WHERE products_id = '" . tep_get_prid($order->products[$i]["static"][$static_loop]["id"]) . "'");
				    		$sql_data_array = array('orders_id' => $insert_id, 
					                           		'products_id' => tep_get_prid($order->products[$i]["static"][$static_loop]["id"]),
					                           		'products_model' => $order->products[$i]["model"],
					                           		'products_name' => $order->products[$i]["static"][$static_loop]["name"],
					                           		'orders_products_store_price' => $sc_storage_normal_price,
					                           		'products_price' => '0.00', 
					                           		'final_price' => '0.00', 
					                           		'products_tax' => $order->products[$i]["tax"], 
					                           		'products_quantity' => $order->products[$i]["static"][$static_loop]["qty"]*$this_package_total_qty,
					                           		'products_bundle_id' => tep_db_input($order->products[$i]['id']),
					                           		'parent_orders_products_id' => (int)$order_products_id,
					                           		'products_categories_id' => $order->products[$i]["static"][$static_loop]["products_categories_id"],
					                           		'op_rebate' => 0,
					                           		'op_rebate_delivered' => 0);
					    	tep_db_perform(TABLE_ORDERS_PRODUCTS, $sql_data_array);	
				    	}
				    }
			    }
				
				if (is_array($extra_info_array)) {
					foreach ($extra_info_array as $extra_key => $extra_val) {
						if ($extra_key != 'qty' && trim($extra_val) != '') {
							$order->insert_opders_products_extra_info($order_products_id, $extra_key, $extra_val);
						}
					}
				}
			}
		} else {
			$sql_data_array = array('orders_id' => $insert_id,
		                            'products_id' => tep_get_prid($order->products[$i]['id']),
		                            'products_model' => $order->products[$i]['model'],
		                            'products_name' => $order->products[$i]['name'],
		                            'orders_products_store_price' => $sc_storage_normal_price,
		                            'products_price' => $sc_storage_normal_price,
		                            'final_price' => $sc_storage_price,
		                            'products_tax' => $order->products[$i]['tax'],
		                            'products_quantity' => $order->products[$i]['qty'],
		                            'products_pre_order' => $order->products[$i]['pre_order'],
		                            'custom_products_type_id' => $order->products[$i]['custom_products_type_id'],
		                            'products_categories_id' => $order->products[$i]['products_categories_id'],
		                            'op_rebate' => ($currencies->rebate_point + $cust_group_extra_rebate), //
		                            'op_rebate_delivered' => 0);
		    tep_db_perform(TABLE_ORDERS_PRODUCTS, $sql_data_array);
		    $order_products_id = tep_db_insert_id();
		    if ($cust_group_extra_rebate > 0) {
                $order->insert_opders_products_extra_info($order_products_id, 'OP_EXTRA', $cust_group_extra_rebate);
                $currencies->rebate_point_formula = $currencies->rebate_point_formula . '<br><br>Extra OP: ' . $cust_group_extra_rebate;
            }
			$order->insert_opders_products_extra_info($order_products_id, 'OP_FORMULA', $currencies->rebate_point_formula);
		}

		$order_total_modules->update_credit_account($i);//ICW ADDED FOR CREDIT CLASS SYSTEM
		
		/****************************************
	    	Custom Product
	    ****************************************/
		$store_credit_currency_array = array(	'products_id' => tep_get_prid($order->products[$i]['id']),
												'orders_products_id' => $order_products_id, 
												'orders_custom_products_key' => 'store_credit_currency',
												'orders_custom_products_value' => tep_get_customer_store_credit_currency($_SESSION['customer_id']),
												'orders_custom_products_number' => 0
												);
		tep_db_perform(TABLE_ORDERS_CUSTOM_PRODUCTS, $store_credit_currency_array);
		
		$sc_promotion_percentage = store_credit::get_sc_promotion_percentage();
		if ($sc_promotion_percentage > 0) {
            $store_credit_currency_array = array(	'products_id' => tep_get_prid($order->products[$i]['id']),
                                                    'orders_products_id' => $order_products_id, 
                                                    'orders_custom_products_key' => 'store_credit_promotion_percentage',
                                                    'orders_custom_products_value' => $sc_promotion_percentage,
                                                    'orders_custom_products_number' => 0
                                                    );
            tep_db_perform(TABLE_ORDERS_CUSTOM_PRODUCTS, $store_credit_currency_array);
		}
		
		//------insert customer choosen option to order--------
	    $attributes_exist = '0';
	    $products_ordered_attributes = '';
	    
	    if (isset($order->products[$i]['attributes'])) {
	    	$attributes_exist = '1';
	      	
	      	for ($j=0, $n2=sizeof($order->products[$i]['attributes']); $j<$n2; $j++) {
	        	if (DOWNLOAD_ENABLED == 'true') {
	          		$attributes_query = "	select popt.products_options_name, poval.products_options_values_name, pa.options_values_price, pa.price_prefix, pad.products_attributes_maxdays, pad.products_attributes_maxcount , pad.products_attributes_filename 
											from " . TABLE_PRODUCTS_OPTIONS . " popt, " . TABLE_PRODUCTS_OPTIONS_VALUES . " poval, " . TABLE_PRODUCTS_ATTRIBUTES . " pa 
	                               			left join " . TABLE_PRODUCTS_ATTRIBUTES_DOWNLOAD . " pad
	                                			on pa.products_attributes_id=pad.products_attributes_id
	                               			where pa.products_id = '" . $order->products[$i]['id'] . "' 
	                                			and pa.options_id = '" . $order->products[$i]['attributes'][$j]['option_id'] . "' 
	                                			and pa.options_id = popt.products_options_id 
	                                			and pa.options_values_id = '" . $order->products[$i]['attributes'][$j]['value_id'] . "' 
	                                			and pa.options_values_id = poval.products_options_values_id 
	                                			and popt.language_id = '" . $languages_id . "' 
	                                			and poval.language_id = '" . $languages_id . "'";
	          		$attributes = tep_db_query($attributes_query);
	        	} else {
	          		$attributes = tep_db_query("select popt.products_options_name, poval.products_options_values_name, pa.options_values_price, pa.price_prefix from " . TABLE_PRODUCTS_OPTIONS . " popt, " . TABLE_PRODUCTS_OPTIONS_VALUES . " poval, " . TABLE_PRODUCTS_ATTRIBUTES . " pa where pa.products_id = '" . $order->products[$i]['id'] . "' and pa.options_id = '" . $order->products[$i]['attributes'][$j]['option_id'] . "' and pa.options_id = popt.products_options_id and pa.options_values_id = '" . $order->products[$i]['attributes'][$j]['value_id'] . "' and pa.options_values_id = poval.products_options_values_id and popt.language_id = '" . $languages_id . "' and poval.language_id = '" . $languages_id . "'");
	        	}
	        	$attributes_values = tep_db_fetch_array($attributes);
				
	        	$sql_data_array = array('orders_id' => $insert_id, 
	            	                    'orders_products_id' => $order_products_id, 
	                	                'products_options' => $attributes_values['products_options_name'],
	                    	            'products_options_values' => $attributes_values['products_options_values_name'], 
	                        	        'options_values_price' => $attributes_values['options_values_price'], 
	                            	    'price_prefix' => $attributes_values['price_prefix']);
	        	tep_db_perform(TABLE_ORDERS_PRODUCTS_ATTRIBUTES, $sql_data_array);
				
	        	if ((DOWNLOAD_ENABLED == 'true') && isset($attributes_values['products_attributes_filename']) && tep_not_null($attributes_values['products_attributes_filename'])) {
	          		$sql_data_array = array('orders_id' => $insert_id, 
											'orders_products_id' => $order_products_id, 
	                                  		'orders_products_filename' => $attributes_values['products_attributes_filename'], 
	                                  		'download_maxdays' => $attributes_values['products_attributes_maxdays'], 
	                                  		'download_count' => $attributes_values['products_attributes_maxcount']);
	          		tep_db_perform(TABLE_ORDERS_PRODUCTS_DOWNLOAD, $sql_data_array);
	        	}
	        	$products_ordered_attributes .= "\n\t" . $attributes_values['products_options_name'] . ' ' . $attributes_values['products_options_values_name'];
	      	}
	    }
		//------insert customer choosen option eof ----
	    $total_weight += ($order->products[$i]['qty'] * $order->products[$i]['weight']);
	    $total_tax += tep_calculate_tax($total_products_price, $products_tax) * $order->products[$i]['qty'];
	    $total_cost += $total_products_price;
		
		$cat_path = tep_output_generated_category_path($order->products[$i]['id'], 'product');
      	$product_email_display_str = $order->products[$i]['name'] . (tep_not_null($order->products[$i]['model']) ? ' (' . $order->products[$i]['model'] . ')' : '');
	    $products_ordered .= $order->products[$i]['qty'] . ' x ' . $product_email_display_str . ' = ' . $currencies->display_price_nodiscount($order->products[$i]['id'], $order->products[$i]['final_price'], $order->products[$i]['tax'], $order->products[$i]['qty']) . $products_ordered_attributes . "\n";
	    
	    $product_query_raw = "	SELECT p.products_bundle, p.products_bundle_dynamic 
                       			FROM " . TABLE_PRODUCTS . " AS p
                      			LEFT JOIN " . TABLE_PRODUCTS_ATTRIBUTES . " AS pa
                    	   			ON p.products_id=pa.products_id
                	      		LEFT JOIN " . TABLE_PRODUCTS_ATTRIBUTES_DOWNLOAD . " AS pad
            	           			ON pa.products_attributes_id=pad.products_attributes_id
        	              		WHERE p.products_id = '" . tep_get_prid($order->products[$i]['id']) . "'";
		$product_query = tep_db_query($product_query_raw);
		$product_values = tep_db_fetch_array($product_query);
	}
	$order_total_modules->apply_credit($insert_id);//ICW ADDED FOR CREDIT CLASS SYSTEM
	
	// Only do the deduction if status been change from Pending
	if ($order->info['order_status'] == '7') {
		require_once(DIR_WS_CLASSES . 'log.php');
		$log_object = new log_files('system');
		
		$cur_order = new order($insert_id);
		tep_deduct_stock_for_automated_payment($insert_id, $cur_order->products, 1, 7);
	}
	
	// register order ID with sc
	if (!isset($_SESSION['sc_order'])) {
		$_SESSION['sc_order'] = array();
	}
	$_SESSION['sc_order'][] = $insert_id;
	
} else if ( tep_session_is_registered('order_logged') ) {
	if (isset($$payment->has_ipn) && $$payment->has_ipn) {
		// If this payment method has it own IPN handling
	} else {
		// for pm2checkout and WorldPay, only set status to processing and email notification to 1 when customer return from 2CO website after made payment
		$order_select_sql = "	SELECT orders_id, orders_status 
								FROM " . TABLE_ORDERS . "
							 	WHERE orders_id = " . $order_logged ;
		$order_result_sql = tep_db_query($order_select_sql);
		if ($order_row_sql = tep_db_fetch_array($order_result_sql)) {
			if ($order_row_sql["orders_status"] == 1) {
				include_once(DIR_WS_FUNCTIONS . 'custom_product_info.php');
				include_once(DIR_WS_CLASSES . 'anti_fraud.php');
				require_once(DIR_WS_CLASSES . 'log.php');
				
				$log_object = new log_files('system');
				$cur_order = new order($order_row_sql["orders_id"]);
				
				$customer_notified = (SEND_EMAILS == 'true') ? '1' : '0';
				
				$orders_status_history_perform_array = array(	'action' => 'insert',
																'data' => array('orders_id' => (int)$order_logged,
																				'orders_status_id' => $$payment->order_processing_status,
																				'date_added' => 'now()',
																				'changed_by' => 'system',
																				'customer_notified' => $customer_notified
																				)
															);
				
				if ($$payment->order_processing_status != $order_row_sql["orders_status"] && $$payment->order_processing_status > 1) {
					$cur_order->update_order_status($$payment->order_processing_status, $orders_status_history_perform_array, true);
				}
				unset($orders_status_history_perform_array);
				
				$products_ordered = '';
				reset($cur_order->products);
				
				for ($i = 0; $i < count($cur_order->products); $i++) {
					$cat_path = tep_output_generated_category_path($order->products[$i]['id'], 'product');
			      	$product_email_display_str = $order->products[$i]['name'] . (tep_not_null($order->products[$i]['model']) ? ' (' . $order->products[$i]['model'] . ')' : '');
					$products_ordered .= $cur_order->products[$i]['qty'] . ' x ' . $product_email_display_str . ' = ' . $currencies->display_price_nodiscount($cur_order->products[$i]['id'], $cur_order->products[$i]['final_price'], $cur_order->products[$i]['tax'], $cur_order->products[$i]['qty']) . "\n";
				}
				
				if (isset($$payment->prepend_order_comment) && tep_not_null($$payment->prepend_order_comment)) {
					$prepend_order_comment_update_sql = "	UPDATE " . TABLE_ORDERS_STATUS_HISTORY . "
															SET comments = CONCAT('".tep_db_input($$payment->prepend_order_comment)."', comments)
															WHERE orders_id = '" . $order_row_sql["orders_id"] . "'";
					tep_db_query($prepend_order_comment_update_sql);
				}
				
				// Send order update 
				tep_status_update_notification('C', $order_row_sql["orders_id"], 'system', $order_row_sql["orders_status"], $$payment->order_processing_status, 'A');
		        
				// Send affiliate notification e-mail
				tep_send_affiliate_notification($order_logged, $cur_order);
			}
		}
	}
}

// Lets start with the email confirmation
$process_email = false;

/*
	Confirm with Weichen abt add clear_temp_process here? 
*/
$cart = new shoppingCart;
$cart->restore_contents();

$delete_temp_process_sql = "DELETE FROM " . TABLE_TEMP_PROCESS . " 
							WHERE page_name = 'checkout_process.php' 
								AND match_case = '".$_SESSION['customer_id']."'";
tep_db_query($delete_temp_process_sql);

if (isset($$payment->form_action_url)) {
	if ($$payment->force_to_checkoutprocess && !tep_session_is_registered('order_logged')) {
		// redirect customer to payment website when they select to pay with 2CO
		$$payment->link_to_payment($insert_id);
	} else {
		$process_email = true;
	}
} else {
	$process_email = true;
}

if ($process_email) {
	if (isset($$payment->has_ipn) && $$payment->has_ipn) {
		// E-mail handle by IPN script
	} else {
		$email_greeting = tep_get_email_greeting($order->customer['firstname'], $order->customer['lastname'], $order->customer['gender']);
		$email_order = $email_greeting . sprintf(EMAIL_TEXT_ORDER_THANK_YOU, $currencies->format($order->info['total'], true, $order->info['currency'])) . "\n\n";
		if (is_object($$payment)) {
			$email_order .= EMAIL_TEXT_PAYMENT_METHOD . "\n" . 
		                    EMAIL_SEPARATOR . "\n";
		    $payment_class = $$payment;
		    $email_order .= strip_tags($payment_class->title) . "\n";
		    if ($payment_class->email_footer) { 
		      $email_order .= $payment_class->email_footer . "\n\n";
		    } else { $email_order .= "\n"; }
		}
		
		if ($order->content_type != 'virtual') {
			if (isset($sendto) && tep_not_null($sendto)) {
				$email_order .= EMAIL_TEXT_DELIVERY_ADDRESS . "\n" . 
		    		            EMAIL_SEPARATOR . "\n" .
		            	        tep_address_label($_SESSION['customer_id'], $sendto, 0, '', "\n") . "\n\n";
			}
		}
		
		$email_order .= EMAIL_TEXT_BILLING_ADDRESS . "\n" .
		                EMAIL_SEPARATOR . "\n" .
		                tep_address_label($_SESSION['customer_id'], $billto, 0, '', "\n") . "\n\n";
		
		$email_order .= EMAIL_TEXT_ORDER_SUMMARY . "\n" .
						EMAIL_SEPARATOR . "\n" .
						EMAIL_TEXT_ORDER_NUMBER . ' ' . (tep_session_is_registered('order_logged') ? $order_logged : $insert_id) . "\n" .
						EMAIL_TEXT_DATE_ORDERED . ' ' . strftime(DATE_FORMAT_LONG) . ".\n\n";
		
		$email_order .= EMAIL_TEXT_PRODUCTS . "\n" . 
		                EMAIL_SEPARATOR . "\n" . 
		                $products_ordered . 
		                EMAIL_SEPARATOR . "\n";
		for ($i=0, $n=sizeof($order_totals); $i<$n; $i++) {
			$email_order .= strip_tags($order_totals[$i]['title']) . ' ' . strip_tags($order_totals[$i]['text']) . "\n";
		}
		
		if ($order->info['comments']) {
		    $email_order .= "\n" . EMAIL_TEXT_EXTRA_INFO . "\n" . 
		    				tep_db_output(tep_db_prepare_input($order->info['comments']));
		}
		
		$email_order .= "\n\n";
		
		$email_text3 = $email_order . EMAIL_TEXT_CLOSING . "\n\n" . EMAIL_FOOTER;
		
		tep_mail($order->customer['firstname'] . ' ' . $order->customer['lastname'], $order->customer['email_address'], implode(' ', array(EMAIL_SUBJECT_PREFIX, sprintf(EMAIL_TEXT_SUBJECT, (tep_session_is_registered('order_logged') ? $order_logged : $insert_id)))), $email_text3, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
		
		if (tep_not_null(SEND_EXTRA_ORDER_EMAILS_TO)) {
			// A copy of email to admin when new order is made
			$extra_email_array = explode(',', SEND_EXTRA_ORDER_EMAILS_TO);
			$extra_email_email_pattern = "/([^<]*?)(?:<)([^>]+)(?:>)/is";
			for ($receiver_cnt=0; $receiver_cnt < count($extra_email_array); $receiver_cnt++) {
				if (preg_match($extra_email_email_pattern, $extra_email_array[$receiver_cnt], $regs)) {
					$receiver_name = trim($regs[1]);
					$receiver_email = trim($regs[2]);
					tep_mail($receiver_name, $receiver_email, implode(' ', array(EMAIL_SUBJECT_PREFIX, sprintf(EMAIL_TEXT_SUBJECT, (tep_session_is_registered('order_logged') ? $order_logged : $insert_id)))), $email_text3, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
				}
			}
		}
	}
	
	// Include OSC-AFFILIATE 
	require(DIR_WS_INCLUDES . 'affiliate_checkout_process.php');
	
	// load the after_process function from the payment modules
	$payment_modules->after_process();
	
	$cart = new sc_shopping_cart;
	$cart->reset(true);
	
	// restore back to normal cart
	$cart = new shoppingCart;
	$cart->restore_contents();
	
	unset($_SESSION['sc_order']);
	
	// unregister session variables used during checkout
	tep_session_unregister('sendto');
	tep_session_unregister('billto');
	tep_session_unregister('shipping');
	tep_session_unregister('payment');
	tep_session_unregister('comments');
	tep_session_unregister('custom_comments');
	if (tep_session_is_registered('credit_covers')) tep_session_unregister('credit_covers');
	if (tep_session_is_registered('pm_2CO_cc_owner')) tep_session_unregister('pm_2CO_cc_owner');
	if (tep_session_is_registered('credit_card_owner')) tep_session_unregister('credit_card_owner');
	if (tep_session_is_registered('payment_inputs_array')) tep_session_unregister('payment_inputs_array');
	if (tep_session_is_registered('order_logged')) tep_session_unregister('order_logged');
	$order_total_modules->clear_posts();//ICW ADDED FOR CREDIT CLASS SYSTEM
	
//	$customer_complete_phone_info = tep_format_telephone($_SESSION['customer_id']);
//	$complete_telephone_number = $customer_complete_phone_info['country_international_dialing_code'] . $customer_complete_phone_info['telephone_number'];
	
//	if (tep_info_been_verify($_SESSION['customer_id'], $complete_telephone_number, 'telephone') == true) {
//		tep_reset_try_turns($_SESSION['customer_id'], $complete_telephone_number, 'telephone');
//	}
	
	if (!tep_session_is_registered('order_completed')) {
		tep_session_register('order_completed');
	}
	
	tep_redirect(tep_href_link(FILENAME_CHECKOUT_SUCCESS, '', 'SSL'));
}

require(DIR_WS_INCLUDES . 'application_bottom.php');
?>