<?
/*
  	$Id: search_all_games.php,v 1.3 2011/05/14 11:17:51 weesiong Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2003 osCommerce
	
  	Released under the GNU General Public License
*/

require('includes/application_top.php');
require(DIR_WS_LANGUAGES . $language . '/' . FILENAME_SEARCH_ALL_GAMES);
require_once(DIR_WS_CLASSES . FILENAME_SEARCH_ALL_GAMES);

if (!isset($_SESSION['customer_id'])) {
    $navigation->set_snapshot();
}

$search_all_games_obj = new search_all_games ();
$search_all_games_obj->generate_game_array();
$filtering_bar_object_array = $search_all_games_obj->get_filtering_bar_object();

$content = CONTENT_SEARCH_ALL_GAMES;

require(DIR_WS_TEMPLATES . TEMPLATENAME_MAIN_PAGE);

require(DIR_WS_INCLUDES . 'application_bottom.php');
?>