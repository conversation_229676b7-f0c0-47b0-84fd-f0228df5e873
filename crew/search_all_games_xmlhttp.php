<?
error_reporting(null);
error_reporting(E_ERROR);
header("Cache-Control: no-cache");
header("Cache-Control: post-check=0,pre-check=0");
header("Cache-Control: max-age=0");
header("Pragma: no-cache");

header('Content-Type: text/xml');

include_once('includes/application_top.php');
require_once(DIR_WS_LANGUAGES . $language . '/' . FILENAME_SEARCH_ALL_GAMES);
require_once(DIR_WS_CLASSES . FILENAME_SEARCH_ALL_GAMES);
			
$action = $_REQUEST['action'];

echo '<response>';

if (tep_not_null($action)) {
	switch($action) {
		case 'get_games_list':
			$search_all_games_obj = new search_all_games ();
			$search_all_games_obj->update_cookie();
			$search_all_games_obj->generate_game_array();
			$return_result = $search_all_games_obj->generate_html_body_content();
			
			if (tep_not_null($return_result)) {
				echo "<error>0</error>";
				echo "<result><![CDATA[" . $return_result . "]]></result>";
			} else {
				echo "<error>1</error>";
				echo "<result><![CDATA[<div style='padding:10px;text-align:center'>" . MESSAGE_SEARCH_HAS_NO_RESULTS . "</div>]]></result>";
			}
			
			unset($return_result);
			break;
		case 'get_new_games_list':
			$search_all_games_obj = new search_all_games ();
			$search_all_games_obj->update_cookie();
			$search_all_games_obj->generate_game_array();
			$return_result = $search_all_games_obj->generate_html_body_content();
			
			if (tep_not_null($return_result)) {
				echo "<error>0</error>";
				echo "<records>1</records>";
				echo "<result><![CDATA[" . $return_result . "]]></result>";
			} else {
				echo "<error>1</error>";
				echo "<records>0</records>";
				echo "<result><![CDATA[]]></result>";
			}
			
			unset($return_result);
			break;
		case 'update_fav':
			$search_all_games_obj = new search_all_games ();
			$return_result = $search_all_games_obj->update_favorite();
			
			echo "<result><![CDATA[" . $return_result['status'] . "]]></result>";
			
			switch ($return_result['status']) {
				case 2 :
					echo "<message><![CDATA[" . sprintf(MESSAGE_FAVORITE_REMOVED, $return_result['balance']) . "]]></message>";
					break;
				case 1 :
					echo "<message><![CDATA[" . sprintf(MESSAGE_FAVORITE_ADDED, $return_result['balance']) . "]]></message>";
					break;
				case -1 :
					// No login session
				case -2 :
					echo "<message><![CDATA[" . MESSAGE_FAVORITE_FULL . "]]></message>";
					break;
				case -3 :
					echo "<message><![CDATA[<h1>Error: Invalid ID...</h1>]]></message>";
					break;
			}
			
			unset($return_result);
			break;
		default:
			echo "<error>1</error>";
			echo "<result>" . MESSAGE_SEARCH_HAS_NO_RESULTS . "</result>";
			break;
	}
}

echo '</response>';
?>