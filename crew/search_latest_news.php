<?php
require('includes/application_top.php');

require(DIR_WS_LANGUAGES . $language . '/' . FILENAME_SEARCH_LATEST_NEWS);

$breadcrumb->add(NAVBAR_TITLE, tep_href_link(FILENAME_SEARCH_LATEST_NEWS, '', 'NONSSL'));

$news_id = (int)$_GET['news_id'];
$tag = (int)$_GET['tag'];

if ($news_id > 0) {
	$this_site_id = SITE_ID;
	
	$news_display_sites_where_str = "FIND_IN_SET( '" . $this_site_id . "', news_display_sites)";
	
	$page_sql = "	SELECT ln.news_id, ln.date_added, ln.url, lnd.headline, lnd.content 
					FROM " . TABLE_LATEST_NEWS . " AS ln 
					INNER JOIN " . TABLE_LATEST_NEWS_DESCRIPTION . " AS lnd 
						ON (ln.news_id=lnd .news_id) 
					WHERE ln.news_id = '" . tep_db_input($news_id) . "'
						AND ln.status = '1' 
						AND " . $news_display_sites_where_str . "
				    	AND ( IF(lnd.language_id = '". $languages_id. "' && lnd.headline <> '', 1, 
						  if ((select count(lnd.news_id) > 0 from  " . TABLE_LATEST_NEWS_DESCRIPTION . " as lnd
								where " . $news_display_sites_where_str . " 
								and lnd.news_id ='".$news_id."' 
								and lnd.language_id ='". $languages_id. "'
								and lnd.headline <> ''), 
								0,
		 						lnd.language_id = '".$default_languages_id."')
								)
							 )";
	
	$page_query = tep_db_query($page_sql);
	$page = tep_db_fetch_array($page_query);

	$breadcrumb->add($page['headline'], '');
} else if ($tag) {
	$tag_content_select_sql = "	SELECT tag_name 
								FROM ".TABLE_LATEST_NEWS_TAG_CATEGORIES."   
								WHERE tag_id = '" . tep_db_input($tag) . "'";
	$tag_content_result_sql = tep_db_query($tag_content_select_sql);
	$tag_content_row = tep_db_fetch_array($tag_content_result_sql);
	
	$breadcrumb->add($tag_content_row['tag_name'], '');
}

$content = CONTENT_SEARCH_LATEST_NEWS;

if ($_GET['ajax'] == "1" || $_GET['rss'] == "1") {
	require(DIR_WS_CONTENT . $content . '.tpl.php');
} else {
	require(DIR_WS_TEMPLATES . TEMPLATENAME_MAIN_PAGE);
}

require(DIR_WS_INCLUDES . 'application_bottom.php'); 
?>