<?
/*
  	$Id: set_secret_qna.php,v 1.7 2012/07/19 09:41:41 weichen Exp $

	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com

  	Copyright (c) 2002 osCommerce

	Released under the GNU General Public License
*/

require('includes/application_top.php');

tep_redirect(tep_href_link(FILENAME_DEFAULT, '', 'SSL'));   // No longer in use

if (!tep_session_is_registered('customer_id')) {
	$navigation->set_snapshot();
    tep_redirect(tep_href_link(FILENAME_LOGIN, '', 'SSL'));
}

if ($session_started == false) {
	tep_redirect(tep_href_link(FILENAME_COOKIE_USAGE));
}

// Mantis # 0000024 @ 200810291822 - if key in from URL then redirect to index page
if (tep_not_null($_SERVER['QUERY_STRING']) && !tep_not_null($action)) {
	tep_redirect(tep_href_link(FILENAME_DEFAULT));
}
else
{	
	// define page heading title & navigation bar title
	define('HEADING_TITLE', HEADER_TITLE_SECRET_QUESTION);
	define('NAVBAR_TITLE', HEADER_TITLE_SECRET_QUESTION);
	
	// english version of notice message
	include_once(DIR_WS_LANGUAGES . $_SESSION['language'] . '/set_secret_qna.php');
	
	
	$last_page = (isset($_REQUEST['trace']) ? $_REQUEST['trace'] : '');
	$action = (isset($_REQUEST['action']) ? $_REQUEST['action'] : '');
	
	if (tep_not_null($last_page)) {
		$_SESSION['trace'] = $last_page;
	}
	
	$error = false;
	
	
	// if $action is set
	if (tep_not_null($action))
	{
		$secretquestion = tep_not_null($HTTP_POST_VARS['secretquestion']) ? tep_db_prepare_input(strip_tags($HTTP_POST_VARS['secretquestion'])) : "";
		$answer = tep_not_null($HTTP_POST_VARS['answer']) ? tep_db_prepare_input(strip_tags($HTTP_POST_VARS['answer'])) : "";
		
		// Secret Question & Answer error messages
		if (is_numeric($secretquestion) == false) {
			$error = true;
			$messageStack->add('set_secret_qna', ENTRY_SECRET_QUESTION_ERROR);
		} 
		
		if (strlen($answer) < ENTRY_SECRET_ANSWER_MIN_LENGTH) {
			$error = true;
			$messageStack->add('set_secret_qna', sprintf(ENTRY_ANSWER_ERROR, ENTRY_SECRET_ANSWER_MIN_LENGTH));
		}
		
		if ($error == false)
		{
			// Check if record exists in table customers_security @ 200810311100
			$result_record_exists = $customers_security_obj->check_secret_question_isset();

			if ($result_record_exists) {
				$question_array = $customers_security_obj->get_customer_security_questions_list($secretquestion);
				
				$sql_data_array = array('customers_security_question_1' => $question_array[0]['text'],	// update entire questions instead of question_id @ 200811031658
								      	'customers_security_answer_1' => $answer,
								      	'customers_security_question_ask' => 'NULL',	// Update customers_security_question_ask to NULL
								      	'customers_security_update_time' => date("Y-m-d H:i:s"));	// Update customers_security_update_time as well @ 200811031907
				$param = "customers_id = '" . tep_db_input($customer_id) . "'";
				$update_qry1_done = tep_db_perform(TABLE_CUSTOMERS_SECURITY, $sql_data_array, 'update', $param);

				if ($update_qry1_done) 
				{
					if ($customers_security_obj->remove_customers_security_start_time()) {
						$messageStack->add('index', ENTRY_UPDATE_DONE);
						tep_redirect(tep_href_link(FILENAME_DEFAULT));
					}
				}
			}
			
			else {
				$question_array = $customers_security_obj->get_customer_security_questions_list($secretquestion);
				
				// Insert into database table customers_security
				$sql_data_array = array('customers_id' => $customer_id,
								      	'customers_security_question_1' => $question_array[0]['text'],	// insert entire questions instead of question_id @ 200811031658
								      	'customers_security_answer_1' => $answer,
								      	'customers_security_question_ask' => 'NULL', 	// Insert customers_security_question_ask as NULL
								      	'customers_security_update_time' => date("Y-m-d H:i:s"));	// Insert customers_security_update_time as well @ 200811031907
				$save_qry_done = tep_db_perform(TABLE_CUSTOMERS_SECURITY, $sql_data_array);
				
				if ($save_qry_done)
				{
					if ($customers_security_obj->remove_customers_security_start_time()) {
						$messageStack->add('index', ENTRY_SAVE_DONE);
		      			tep_redirect(tep_href_link(FILENAME_DEFAULT));
		      		}
				}
			} // end else
		}		
	}
	
	//get the expired day for changing the Q&A
	$day_diff = $customers_security_obj->get_convert_pin_expired_date();
	
	//Define content that gets included in the center column.
	$content = CONTENT_SET_SECRET_QNA;
	
	//Define the javascript file
	//$javascript = 'form_check.js.php';
	
	$breadcrumb->add(NAVBAR_TITLE);
	require(DIR_WS_TEMPLATES . TEMPLATENAME_MAIN_PAGE);
	require(DIR_WS_INCLUDES . 'application_bottom.php');
}
?>