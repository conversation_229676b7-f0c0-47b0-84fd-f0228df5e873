<?php
require('includes/application_top.php');

$action = isset($_GET['action']) ? tep_db_prepare_input(strip_tags($_GET['action'])) : '';

if ($action) {
    switch ($action) {
        case 'setID':
            // this set input has been done in app top
            $shasso_obj->requestClearAllCookie();
            
            if ($shasso_obj->input['S3ID']) {
                $shasso_obj->requestSetID();
            }
            
            if ($shasso_obj->input['S3RM']) {
                $shasso_obj->requestSetRME();
            }
            
            break;
        case 'deleteID':
            $shasso_obj->input = array();
            $shasso_obj->requestClearAllCookie();
            authenticate::logoff();
            break;
        case 'kickCustomer':
            $_data = ($_SERVER['REQUEST_METHOD'] == 'POST') ? $_POST : "";

            if (!empty($_data) && isset($_data["user_id"])) {
                if ($_data["signature"] == md5($_data["user_id"] . "|" . SHASSO_CLIENT_SECRET)) {
                    $active_session_select_sql = "	SELECT sesskey 
                                                    FROM " . TABLE_SESSIONS . " 
                                                    WHERE expiry > '" . time() . "' 
                                                    AND (value REGEXP \"(customer_id)[[.|.]][a-z][[.:.]]" . $_data["user_id"] . "\" OR value REGEXP \"(customer_id)[[.|.]][a-z][[.:.]][0-9][[.:.]][[.quotation-mark.]]" . $_data["user_id"] . "[[.quotation-mark.]]\")
                                                    ";
                    $active_session_result_sql = tep_db_query($active_session_select_sql);
                    while ($active_session_row = tep_db_fetch_array($active_session_result_sql)) {
                        tep_db_query("delete from " . TABLE_SESSIONS . " where sesskey = '" . tep_db_input($active_session_row['sesskey']) . "'");
                    }
                }
            }
            
            break;
        case 'getCustomerScWor':
            $responseArray = array(
                'sc' => array(
                    'val' => '-',
                    'error' => 1,
                ),
                'wor' => array(
                    'val' => '-',
                    'error' => 1,
                )
            );
            
            if (isset($_SESSION['customer_id']) && $_SESSION['customer_id']) {
                $customer_sc_array = store_credit::get_current_credits_balance($customer_id);

                if (count($customer_sc_array) > 0) {
                    $total_store_credit = ($customer_sc_array['sc_reverse'] + $customer_sc_array['sc_irreverse']) - ($customer_sc_array['sc_reverse_reserve_amt'] + $customer_sc_array['sc_irreverse_reserve_amt']);
                    $sc_currency_code = $currencies->get_code_by_id($customer_sc_array['sc_currency_id']);
                }

                $responseArray['sc'] = array(
                    'val' => (count($customer_sc_array) > 0) ? $currencies->format($total_store_credit, true, $sc_currency_code, 1) : $currencies->format('0.00', false, '', 1),
                    'error' => 0,
                );
                $responseArray['wor'] = array(
                    'val' => (int) store_point::get_current_points_balance($_SESSION['customer_id']),
                    'error' => 0,
                );
            }

            echo json_encode($responseArray);
            break;
        default:
            //validateID
    }
}