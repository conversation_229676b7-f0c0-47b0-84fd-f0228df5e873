<?php
require_once('includes/application_top.php');
include_once(DIR_WS_CLASSES . 'payment.php');
include_once(DIR_WS_CLASSES . 'order.php');
include_once(DIR_WS_CLASSES . 'log.php');

$acknowledgement = '';
$payment = 'smart2pay_globalpay';
$form_data = file_get_contents("php://input");

if (tep_not_null($form_data)) {
	include_once(DIR_WS_MODULES . 'payment/' . $payment . '/classes/' . $payment . '_ipn_class.php');
	include_once(DIR_WS_LANGUAGES . $language . '/' . FILENAME_CHECKOUT_PROCESS);
	include_once(DIR_WS_CLASSES . 'anti_fraud.php');
	include_once(DIR_WS_CLASSES . 'pgs.php');
    $ipn_obj = new smart2pay_globalpay_ipn($form_data);
    
    // check IPN data is exist
    if ($ipn_obj->is_ipn_data_exist()) {
        $order_id = $ipn_obj->get_order_id();

        // proceed only when transaction is success
        if ($order_id !== false && $order_id > 0) {
			$order = new order($order_id);
			$payment_modules = new payment($payment);
			$$payment = new $payment($order->info['payment_methods_id']);   // smart2pay_globalpay class
			$$payment->get_merchant_account($order->info['currency']);
			$log_object = new log_files('system');
			$ipn_obj->load_pg_main_obj($$payment);
			if (pgs::getOrderSiteId($order_id) == '5') {
				$acknowledgement = $ipn_obj->acknowledge_ipn(); // it might be success or fail
				pgs::repostToPGS('smart2pay_globalpay', $form_data);
			} else {
				// proceed on signature check, if success it will means from the valid PG and merchant account (validate_receiver_account) as well.
				if ($ipn_obj->validate_transaction_data()) {
					$acknowledgement = $ipn_obj->acknowledge_ipn(); // it might be success or fail

					if ($ipn_obj->validate_notification_type()) {
						$ipn_obj->save_data($order_id);

						if ($ipn_obj->get_status() == 2 && $ipn_obj->validate_save_data_exist($order_id)) {
							if ($order->info['orders_status_id'] == $$payment->order_status) { // If the order still in initial status
								$ipn_obj->authenticate($order);
							}
						}
					}
				}
			}
        }
        
        $ipn_obj->send_log_by_email($order_id, $form_data);
    }
    
    unset($ipn_obj);
}
echo $acknowledgement;
?>