<?
/*
  	$Id: smart2pay_ipn.php, v1.0 2010/11/09 13:12:05 dennis <PERSON>p
  	Author : <PERSON> (<EMAIL>)
  	Title: smart2pay Payment Module V1.0
	
	Revisions:
		Version 1.0 Initial Payment Module
  		Copyright (c) 2007 SKC Venture
	
  	Released under the GNU General Public License
*/

require_once('includes/application_top.php');
include_once(DIR_WS_CLASSES . 'payment.php');
include_once(DIR_WS_CLASSES . 'order.php');
include_once(DIR_WS_CLASSES . 'log.php');
include_once(DIR_WS_LANGUAGES . $language . '/' . FILENAME_CHECKOUT_PROCESS);

$xmlstr = file_get_contents("php://input");

if (tep_not_null($xmlstr)) {
	include_once(DIR_WS_MODULES . 'payment/smart2pay/classes/smart2pay_ipn_class.php');
	include_once(DIR_WS_LANGUAGES . $language . '/' . FILENAME_CHECKOUT_PROCESS);
	include_once(DIR_WS_CLASSES . 'anti_fraud.php');
	
	require_once(DIR_WS_CLASSES . 'ogm_xml_to_ary.php');
	$xml_array_obj = new ogm_xml_to_ary($xmlstr, 'content');
	$xml_array = $xml_array_obj->get_ogm_xml_to_ary();
	
	$count_payment = 1;
	$smart2pay_ipn = new smart2pay_ipn($xmlstr);
	$signature_flag = false;
	$payment_method = '';
	$payment = 'smart2pay';
    $payment_modules = new payment($payment);
    $log_object = new log_files('system');
	
	$currency = array();
	$$payment = array(); 
	$status_id = array();
	$date = array();
	$orders_id = array();
	
	if (isset($xml_array['PaymentConfirmation']['_c']['PaymentMethod']['_v'])) {  // Wallie IPN
		$payment_method = $xml_array['PaymentConfirmation']['_c']['PaymentMethod']['_v'];
	} else if (isset($xml_array['PaymentNotification']['_c']['PaymentMethod']['_v'])) {  // PaySafeCard, Ukash IPN
		$payment_method = $xml_array['PaymentNotification']['_c']['PaymentMethod']['_v'];
	}
	
	switch (strtolower($payment_method)) {
	 	case 'wallie':  // data won't return in batch
            $hash = (isset($xml_array['PaymentConfirmation']['_c']['Payments']['_c']['Payment']['_c']['Hash']['_v']) ? $xml_array['PaymentConfirmation']['_c']['Payments']['_c']['Payment']['_c']['Hash']['_v'] : "");
            $payment_id[0] = (isset($xml_array['PaymentConfirmation']['_c']['Payments']['_c']['Payment']['_c']['PaymentID']['_v']) ? $xml_array['PaymentConfirmation']['_c']['Payments']['_c']['Payment']['_c']['PaymentID']['_v'] : "");
            $amount[0] = (isset($xml_array['PaymentConfirmation']['_c']['Payments']['_c']['Payment']['_c']['Amount']['_v']) ? $xml_array['PaymentConfirmation']['_c']['Payments']['_c']['Payment']['_c']['Amount']['_v'] : 0);
            $currency[0] = (isset($xml_array['PaymentConfirmation']['_c']['Payments']['_c']['Payment']['_c']['Currency']['_v']) ? $xml_array['PaymentConfirmation']['_c']['Payments']['_c']['Payment']['_c']['Currency']['_v'] : "");
            $date[0] = (isset($xml_array['PaymentConfirmation']['_c']['Payments']['_c']['Payment']['_c']['Date']['_v']) ? $xml_array['PaymentConfirmation']['_c']['Payments']['_c']['Payment']['_c']['Date']['_v'] : "");
            $status[0] = (isset($xml_array['PaymentConfirmation']['_c']['Payments']['_c']['Payment']['_c']['Status']['_v']) ? $xml_array['PaymentConfirmation']['_c']['Payments']['_c']['Payment']['_c']['Status']['_v'] : "");
			break;
		
		case 'paysafecard':  // data might return in batch
		case 'ukash':
        case 'mercadopago':
        case 'gluepay':
            $count_payment = (isset($xml_array['PaymentNotification']['_c']['Payments']['_c']['Payment']) ? count($xml_array['PaymentNotification']['_c']['Payments']['_c']['Payment']) : 0);
            $hash = (isset($xml_array['PaymentNotification']['_c']['Hash']['_v']) ? $xml_array['PaymentNotification']['_c']['Hash']['_v'] : "");
            
            if ($count_payment > 1) {
                for ($i = 0; $i < $count_payment; $i++){
                    $payment_id[$i] = (isset($xml_array['PaymentNotification']['_c']['Payments']['_c']['Payment'][$i]['_c']['PaymentID']['_v']) ? $xml_array['PaymentNotification']['_c']['Payments']['_c']['Payment'][$i]['_c']['PaymentID']['_v'] : "");
                    $amount[$i] = (isset($xml_array['PaymentNotification']['_c']['Payments']['_c']['Payment'][$i]['_c']['Amount']['_v']) ? $xml_array['PaymentNotification']['_c']['Payments']['_c']['Payment'][$i]['_c']['Amount']['_v'] : "");
                    $currency[$i] = (isset($xml_array['PaymentNotification']['_c']['Payments']['_c']['Payment'][$i]['_c']['Currency']['_v']) ? $xml_array['PaymentNotification']['_c']['Payments']['_c']['Payment'][$i]['_c']['Currency']['_v'] : "");
                    $date[$i] = (isset($xml_array['PaymentNotification']['_c']['Payments']['_c']['Payment'][$i]['_c']['Date']['_v']) ? $xml_array['PaymentNotification']['_c']['Payments']['_c']['Payment'][$i]['_c']['Date']['_v'] : "");
                    $status_id[$i] = (isset($xml_array['PaymentNotification']['_c']['Payments']['_c']['Payment'][$i]['_c']['StatusID']['_v']) ? $xml_array['PaymentNotification']['_c']['Payments']['_c']['Payment'][$i]['_c']['StatusID']['_v'] : "");
                }
            } else {
                $payment_id[0] = (isset($xml_array['PaymentNotification']['_c']['Payments']['_c']['Payment']['_c']['PaymentID']['_v']) ? $xml_array['PaymentNotification']['_c']['Payments']['_c']['Payment']['_c']['PaymentID']['_v'] : "");
                $amount[0] = (isset($xml_array['PaymentNotification']['_c']['Payments']['_c']['Payment']['_c']['Amount']['_v']) ? $xml_array['PaymentNotification']['_c']['Payments']['_c']['Payment']['_c']['Amount']['_v'] : 0);
                $currency[0] = (isset($xml_array['PaymentNotification']['_c']['Payments']['_c']['Payment']['_c']['Currency']['_v']) ? $xml_array['PaymentNotification']['_c']['Payments']['_c']['Payment']['_c']['Currency']['_v'] : "");
                $date[0] = (isset($xml_array['PaymentNotification']['_c']['Payments']['_c']['Payment']['_c']['Date']['_v']) ? $xml_array['PaymentNotification']['_c']['Payments']['_c']['Payment']['_c']['Date']['_v'] : "");
                $status_id[0] = (isset($xml_array['PaymentNotification']['_c']['Payments']['_c']['Payment']['_c']['StatusID']['_v']) ? $xml_array['PaymentNotification']['_c']['Payments']['_c']['Payment']['_c']['StatusID']['_v'] : "");
            }
			break;
	}
	
    for ($i = 0; $i < $count_payment; $i++){
	    $orders_id[$i] = $smart2pay_ipn->get_order_id($payment_id[$i]);
	    
	    if (tep_not_null($orders_id[$i])) {
			$order = new order($orders_id[$i]);
			$$payment[$i] = new $payment($order->info['payment_methods_id']);
			$smart2pay_get_supported_currencies = $$payment[$i]->get_support_currencies($$payment[$i]->payment_methods_id, false);
			$smart2pay_currency = $order->info['currency'];
			$$payment[$i]->get_merchant_account($smart2pay_currency);
		}
		
		if (isset($status[$i]) && tep_not_null($status[$i])) {
			$status_id[$i] = $$payment[$i]->get_status_id($status[$i]);
		}
	}
	
	// Create IPN Receive Hash
	switch (strtolower($payment_method)) {
		case 'wallie':
            $payment_string = $payment_id[0] . $amount[0] . $currency[0] . $payment_method . $status[0];
            $return_hash = bin2hex(md5($payment_string.$$payment[0]->smart2pay_key, true));
			break;
		case 'paysafecard':
		case 'ukash':
        case 'mercadopago':
        case 'gluepay':
            if ($count_payment > 1) {
                for ($i = 0; $i < $count_payment; $i++){
                    $payment_string = $payment_id[$i] . $amount[$i] . $currency[$i] . $status_id[$i];
                    $hash_string .= $payment_string;
                }
                $return_hash = bin2hex(md5(strtolower($payment_method.$hash_string.$$payment[0]->smart2pay_key), true));
            } else {
                $hash_string = $payment_method . $payment_id[0] . $amount[0] . $currency[0] . $status_id[0];
                $return_hash = bin2hex(md5(strtolower($hash_string.$$payment[0]->smart2pay_key), true));
            }
			break;
	}
	
	if (strtolower($return_hash) == strtolower($hash)) {
		$signature_flag = true;
	}
    
	if (count($orders_id) > 0) {
		for ($i = 0; $i < count($orders_id); $i++) {
			// Set to log
			$smart2pay_status_history_data_sql = array(	'smart2pay_order_id' => (int)$orders_id[$i],
														'smart2pay_date' => 'now()',
									   					'smart2pay_status_id' => (int)$status_id[$i],
									   					'smart2pay_changed_by' => 'system'
									  					);
			if ($signature_flag) {
				$pg_returned_param = array("payment_id" => $payment_id[$i], "amount" => $amount[$i], "currency" => $currency[$i], "date" => $date[$i], "status_id" => $status_id[$i]);
				// Payment ID Checking
			  	if ($smart2pay_ipn->validate_payment_method($payment_method, $orders_id[$i], $pg_returned_param)) {
			  		// Currency & Amount Checking 
					if ($smart2pay_ipn->validate_transaction_data($$payment[$i], $orders_id[$i], $payment_method, $pg_returned_param)) {
						// Checking with Smart2Pay Order
						if ($smart2pay_ipn->authenticate($orders_id[$i], $$payment[$i])) {
							
							// check order exist
							$smart2pay_check_exist_select_sql = " SELECT smart2pay_order_id
															 	  FROM " . TABLE_SMART2PAY . " 
															 	  WHERE smart2pay_transaction_id  = '".tep_db_input($payment_id[$i])."'";
							$smart2pay_check_exist_result_sql = tep_db_query($smart2pay_check_exist_select_sql);
							if (tep_db_num_rows($smart2pay_check_exist_result_sql)) {
								$smart2pay_status_history_data_sql['smart2pay_description'] = 'IPN Transaction: OK';
								$smart2pay_order_data_array = array( 'smart2pay_payment_date' => tep_db_prepare_input($date[$i]),
															   	 	 'smart2pay_status_id' => (int)$status_id[$i]
															     	 );
								tep_db_perform(TABLE_SMART2PAY, $smart2pay_order_data_array, 'update', " smart2pay_order_id = '".(int)$orders_id[$i]."' ");
							} else {
								$smart2pay_status_history_data_sql['smart2pay_description'] = tep_db_prepare_input('IPN Error: Transaction ID ('.trim($payment_id[$i]).') not found.');
							}
							tep_db_perform(TABLE_SMART2PAY_STATUS_HISTORY, $smart2pay_status_history_data_sql);
						}
					}
			  	}
			} else {
				// Set to log
			    $smart2pay_status_history_data_sql['smart2pay_description'] = tep_db_prepare_input('IPN Error: Signature Not Match ('. strtolower($return_hash)  . ' != ' . strtolower(trim($hash)).')');
			    tep_db_perform(TABLE_SMART2PAY_STATUS_HISTORY, $smart2pay_status_history_data_sql);
			}
		}
		
		$smart2pay_ipn->xml_response($payment_method, $payment_id, $$payment->smart2pay_key);
	}
}
?>