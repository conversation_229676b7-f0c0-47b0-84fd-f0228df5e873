<?php
/*
  $Id: specials.php,v 1.5 2010/02/05 10:23:12 henry.chow Exp $

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2003 osCommerce

  Released under the GNU General Public License


  require('includes/application_top.php');

  require(DIR_WS_LANGUAGES . $language . '/' . FILENAME_SPECIALS);

  $breadcrumb->add(NAVBAR_TITLE, tep_href_link(FILENAME_SPECIALS));

  $content = CONTENT_SPECIALS;

  require(DIR_WS_TEMPLATES . TEMPLATENAME_MAIN_PAGE);

  require(DIR_WS_INCLUDES . 'application_bottom.php');
  */
?>
<?php
   //CGDiscountSpecials start
   if (!isset($customer_id)) $customer_id = 0;
   $customer_group = tep_get_customers_groups_id();
	if ($random_product = tep_random_select("	SELECT p.products_id, pd.products_name, p.products_price, p.products_tax_class_id, pd.products_image, s.specials_new_products_price 
												FROM " . TABLE_PRODUCTS . " p, " . TABLE_PRODUCTS_DESCRIPTION . " pd, " . TABLE_SPECIALS . " s 
												WHERE p.products_status = '1' 
													AND p.products_id = s.products_id 
													AND pd.products_id = s.products_id 
													AND pd.language_id = '" . (int)$languages_id . "' 
													AND s.status = '1' 
													AND ((s.customers_id = '" . $customer_id . "' 
													AND s.customers_groups_id = '0') or (s.customers_id = '0' 
													AND s.customers_groups_id = '" . $customer_group . "') or (s.customers_id = '0' 
													AND s.customers_groups_id = '0')) 
													ORDER BY s.specials_date_added DESC LIMIT " . MAX_RANDOM_SELECT_SPECIALS)) {	
   //CGDiscountSpecials end
?>
<!-- specials //-->
          <tr>
            <td>
<?php
    $info_box_contents = array();
    $info_box_contents[] = array('text' => BOX_HEADING_SPECIALS);

    new infoBoxHeading($info_box_contents, false, false, tep_href_link(FILENAME_SPECIALS));

    $info_box_contents = array();

	//CGDiscountSpecials start
	$random_product['specials_new_products_price'] = tep_get_products_special_price($random_product['products_id']);
    $info_box_contents[] = array('align' => 'center',
                                 'text' => '<a href="' . tep_href_link(FILENAME_PRODUCT_INFO, 'products_id=' . $random_product["products_id"]) . '">' . tep_image(DIR_WS_IMAGES . $random_product['products_image'], $random_product['products_name'], SMALL_IMAGE_WIDTH, SMALL_IMAGE_HEIGHT) . '</a><br><a href="' . tep_href_link(FILENAME_PRODUCT_INFO, 'products_id=' . $random_product['products_id']) . '">' . $random_product['products_name'] . '</a><br><s>' . $currencies->display_price($random_product['products_id'], $random_product['products_price'], tep_get_tax_rate($random_product['products_tax_class_id'])) . '</s><br><span class="productSpecialPrice">' . $currencies->display_price_nodiscount($random_product["products_id"], $random_product['specials_new_products_price'], tep_get_tax_rate($random_product['products_tax_class_id'])) . '</span>');
    //CGDiscountSpecials end

    new infoBox($info_box_contents);
?>
            </td>
          </tr>
<!-- specials_eof //-->
<?php
  }
?>