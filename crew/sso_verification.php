<?php
error_reporting(null);
error_reporting(E_ERROR);
header("Cache-Control: no-cache");
header("Cache-Control: post-check=0,pre-check=0");
header("Cache-Control: max-age=0");
header("Pragma: no-cache");

header('Content-Type: application/json');

include_once('includes/application_top.php');
			
$action = $_REQUEST['action'];

if (tep_not_null($action)) {
	switch($action) {
		case 'verify_sso_token':
            $customer_id = $_REQUEST['customer_id'];
            $sso_token = $_REQUEST['sso_token'];
            $offgamers_id = isset($_REQUEST['offgamers_id']) ? $_REQUEST['offgamers_id'] : '';
			$check_sso_sql = "  SELECT customers_id, UNIX_TIMESTAMP(datetime) as timestamp, login_method
                                FROM " . TABLE_SSO_TOKEN . " 
                                WHERE sso_token  = '" . tep_db_prepare_input($sso_token) . "'";
            $check_sso_query = tep_db_query($check_sso_sql);
            $check_sso_result = tep_db_fetch_array($check_sso_query); 
            if (tep_not_null($check_sso_result['customers_id'])) {
                if ($check_sso_result['timestamp'] > time()) {
                    tep_db_perform(TABLE_SSO_TOKEN, array('datetime' => date("Y-m-d H:i:s", strtotime("+2 HOUR"))), 'update', 'sso_token = "'.$sso_token.'"');
                    tep_db_perform(TABLE_SESSIONS, array('expiry' => time() + 7200), 'update', 'sesskey = "'.tep_db_prepare_input($offgamers_id).'"');
                    $return_array = array ( 'customer_id' => $check_sso_result['customers_id'], 
                                            'login_method' => $check_sso_result['login_method'], 
                                            'result' => 'Success'
                                            );
                    echo json_encode($return_array);
                } else {
                    error_report('1001', 'Token Expired');
                }
            } else {
                error_report('1000', 'Invalid Token');
            }
			break;
    }
}    
    function error_report($code, $message) {
        $error_message = array ('code' => $code,
                                'message' => $message
                                );
        echo json_encode($error_message);
    }
    
?>
