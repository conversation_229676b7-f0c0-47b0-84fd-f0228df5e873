<?php 

require('includes/application_top.php');

if (!tep_session_is_registered('customer_id')) {
	$navigation->set_snapshot();
    tep_redirect(tep_href_link(FILENAME_LOGIN, '', 'SSL'));
}

$order_id = isset($_REQUEST['order_id']) && is_numeric($_REQUEST['order_id']) ? tep_db_prepare_input($_REQUEST['order_id']) : '';
if (!tep_not_null($order_id)) {
	tep_redirect(tep_href_link(FILENAME_ACCOUNT, '', 'SSL'));	
}

require(DIR_WS_LANGUAGES . $language . '/' . FILENAME_RECEIVE_PAYMENT_INFO);
require(DIR_WS_CLASSES . 'order.php');
$order = new order($order_id);

if ($order->customer['id'] != $customer_id) {
	$messageStack->add_session('account_history', MESSAGE_UNAUTHORISED_SENT_OFFLINE_PAYMENT_INFO);
	tep_redirect(tep_href_link(FILENAME_ACCOUNT_HISTORY, 'orders_type=current', 'SSL'));
}

require_once(DIR_WS_CLASSES . 'payment_methods.php');
$pm_obj = new payment_methods($order->info['payment_methods_id']);
$payment_method = $pm_obj->payment_methods_description_title;

// get payment method's confirm completed days (larger than 0 is RP else is NRP)
$payment_confirm_complete_days = $pm_obj->payment_method_array->confirm_complete_days;

$action = isset($_REQUEST['action']) ? tep_db_prepare_input($_REQUEST['action']) : '';
if ($action == 'process') {	
	$offline_payment_info		= tep_not_null($_REQUEST['offline_payment_info']) ? tep_db_prepare_input($_REQUEST['offline_payment_info']) : '';
	$payment_info_update_array 	= array ('authorisation_result' => $offline_payment_info);
	$fill_payment_info = false;
	
	// check if record exists in payment_extra_info table
	$orders_authorised_result_sql = "	SELECT authorisation_result FROM ". TABLE_PAYMENT_EXTRA_INFO ." WHERE orders_id = '" . (int)$order_id . "'";
	$orders_authorised_result_res = tep_db_query($orders_authorised_result_sql);
	if (tep_db_num_rows($orders_authorised_result_res) > 0) {
		if (tep_db_perform(TABLE_PAYMENT_EXTRA_INFO, $payment_info_update_array, 'update', 'orders_id="' . (int)$order_id . '"')) {
			$fill_payment_info = true;
		}
	} else {
		$payment_info_update_array['orders_id'] = $order_id;
		if (tep_db_perform(TABLE_PAYMENT_EXTRA_INFO, $payment_info_update_array)) {
			$fill_payment_info = true;
		}
	}
	

	// update offline payment info to payment_extra_info table
	if ($fill_payment_info) {
		if (sizeof($order->totals) && is_array($order->totals)) {
			for ($r=0, $t=count($order->totals); $r < $t; $r++) {
				$totals_array[$order->totals[$r]['class']] = array('title'	=> $order->totals[$r]['title'],
																   'text'	=> $order->totals[$r]['text'],
																   'value'	=> $order->totals[$r]['value']
															 );
			}
		}
		
		$email_subject = tep_mb_convert_encoding(tep_mb_convert_encoding(sprintf(EMAIL_SUBJECT, $order_id), EMAIL_CHARSET, CHARSET), CHARSET, EMAIL_CHARSET);
		$email_text = sprintf(EMAIL_TEMPLATE_OF_OFFLINE_PAYMENT_INFO_SUBMITTED, $order->customer['name'], $order->info['date_purchased'], $order_id, $totals_array['ot_subtotal']['text'], $order->info['orders_status'], $payment_method, $order->customer['name'], $offline_payment_info);
		
		// send email
		tep_mail(STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS, $email_subject, $email_text, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
		tep_mail($order->customer['name'], $order->customer['email_address'], $email_subject, $email_text, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
		tep_redirect(tep_href_link(FILENAME_ACCOUNT, '', 'SSL'));
	} else {
		$messageStack->add('offline_payment_info', MESSAGE_OFFLINE_PAYMENT_INFO_SENT_FAIL);
	}
}

$breadcrumb->add(NAVBAR_TITLE_1, tep_href_link(FILENAME_ACCOUNT, '', 'SSL'));
$breadcrumb->add(NAVBAR_TITLE_2, tep_href_link(FILENAME_ACCOUNT_HISTORY, '', 'SSL'));
$breadcrumb->add(NAVBAR_TITLE_3, tep_href_link(FILENAME_ACCOUNT_HISTORY_INFO, 'order_id='.$order_id, 'SSL'));
$breadcrumb->add(NAVBAR_TITLE_4, tep_href_link(FILENAME_RECEIVE_PAYMENT_INFO, 'order_id='.$order_id, 'SSL'));


$offline_payment_info_select_query = "	SELECT authorisation_result 
										FROM " . TABLE_PAYMENT_EXTRA_INFO . " 
										WHERE orders_id='" . (int)$order_id . "' 
											AND authorisation_result <> '' 
											AND authorisation_result IS NOT NULL ";
if ($offline_payment_info_select_result = tep_db_query($offline_payment_info_select_query)) {
	$offline_payment_info_row = tep_db_fetch_array($offline_payment_info_select_result);
}


$content = CONTENT_RECEIVE_PAYMENT_INFO;

require(DIR_WS_TEMPLATES . TEMPLATENAME_MAIN_PAGE);
require(DIR_WS_INCLUDES . 'application_bottom.php');

?>