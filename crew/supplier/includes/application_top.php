<?
define('PAGE_PARSE_START_TIME', microtime());

// Set the level of error reporting
error_reporting(E_ALL & ~E_NOTICE);

$HTTP_SERVER_VARS = $_SERVER;
$HTTP_POST_VARS = $_POST;
$HTTP_GET_VARS = $_GET;
$HTTP_COOKIE_VARS = $_COOKIE;

// Check if register_globals is enabled.
// Since this is a temporary measure this message is hardcoded. The requirement will be removed before 2.2 is finalized.
if (function_exists('ini_get')) {
	ini_get('register_globals') or exit('FATAL ERROR: register_globals is disabled in php.ini, please enable it!');
}

// Set the local configuration parameters - mainly for developers
if (file_exists('includes/local/configure.php')) 
	include('includes/local/configure.php');

// Include application configuration parameters
require('includes/configure.php');

// Define the project version
define('PROJECT_VERSION', 'osCommerce 2.2-MS2');

// set the type of request (secure or not)
$request_type = (getenv('HTTPS') == 'on') ? 'SSL' : 'NONSSL';

// set php_self in the local scope
//$PHP_SELF = (isset($HTTP_SERVER_VARS['PHP_SELF']) ? $HTTP_SERVER_VARS['PHP_SELF'] : $HTTP_SERVER_VARS['SCRIPT_NAME']);
$PHP_SELF = $HTTP_SERVER_VARS['SCRIPT_NAME'];

$Sep = (strstr(DIR_FS_DOCUMENT_ROOT, ":/")?";":":") ;
$GLOBALS["_PEAR_Path_"] = DIR_FS_DOCUMENT_ROOT . "pear" ;
ini_set("include_path", $GLOBALS["_PEAR_Path_"]. $Sep.".".$Sep .$_SERVER['DOCUMENT_ROOT']) ;

// Used in the "Backup Manager" to compress backups
define('LOCAL_EXE_GZIP', '/usr/bin/gzip');
define('LOCAL_EXE_GUNZIP', '/usr/bin/gunzip');
define('LOCAL_EXE_ZIP', '/usr/local/bin/zip');
define('LOCAL_EXE_UNZIP', '/usr/local/bin/unzip');

// include the list of project filenames
require(DIR_WS_INCLUDES . 'filenames.php');

// include the list of project database tables
require(DIR_WS_INCLUDES . 'database_tables.php');

// customization for the design layout
define('BOX_WIDTH', 160); // how wide the boxes should be in pixels (default: 125)

// Define how do we update currency exchange rates
// Possible values are 'oanda' 'xe' or ''
define('CURRENCY_SERVER_PRIMARY', 'oanda');
define('CURRENCY_SERVER_BACKUP', 'xe');

define('DISPLAY_PRICE_DECIMAL', 4);

// include the database functions
require(DIR_WS_FUNCTIONS . 'database.php');

// make a connection to the database... now
tep_db_connect() or die('Unable to connect to database server!');

// set application wide parameters
$configuration_query = tep_db_query('select configuration_key as cfgKey, configuration_value as cfgValue from ' . TABLE_CONFIGURATION);
while ($configuration = tep_db_fetch_array($configuration_query)) {
	define($configuration['cfgKey'], $configuration['cfgValue']);
}

// define our general functions used application-wide
require(DIR_WS_FUNCTIONS . 'general.php');
require(DIR_WS_FUNCTIONS . 'html_output.php');
//Admin begin
require(DIR_WS_FUNCTIONS . 'password_funcs.php');
//Admin end

// Get particular categories configuration setting(s)
require(DIR_WS_FUNCTIONS . 'configuration.php');

// set the cookie domain
$cookie_domain = (($request_type == 'NONSSL') ? HTTP_COOKIE_DOMAIN : HTTPS_COOKIE_DOMAIN);
$cookie_path = (($request_type == 'NONSSL') ? HTTP_COOKIE_PATH : HTTPS_COOKIE_PATH);

// initialize the logger class
require(DIR_WS_CLASSES . 'logger.php');

// some code to solve compatibility issues
require(DIR_WS_FUNCTIONS . 'compatibility.php');

// check to see if php implemented session management functions - if not, include php3/php4 compatible session class
if (!function_exists('session_start')) {
	define('PHP_SESSION_NAME', 'osCSupID');
    define('PHP_SESSION_PATH', '/');
    define('PHP_SESSION_SAVE_PATH', SESSION_WRITE_DIRECTORY);
	
    include(DIR_WS_CLASSES . 'sessions.php');
}

// define how the session functions will be used
require(DIR_WS_FUNCTIONS . 'sessions.php');

// set the session name and save path
tep_session_name('osCSupID');
tep_session_save_path(SESSION_WRITE_DIRECTORY);

// set the session cookie parameters
if (function_exists('session_set_cookie_params')) {
	//session_set_cookie_params(0, DIR_WS_SUPPLIER);
	session_set_cookie_params(0, $cookie_path, $cookie_domain);
} elseif (function_exists('ini_set')) {
	ini_set('session.cookie_lifetime', '0');
    //ini_set('session.cookie_path', DIR_WS_SUPPLIER);
    ini_set('session.cookie_path', $cookie_path);
    ini_set('session.cookie_domain', $cookie_domain);
}
/*
// set the session ID if it exists
if (isset($HTTP_POST_VARS[tep_session_name()])) {
	tep_session_id($HTTP_POST_VARS[tep_session_name()]);
} else if ( isset($HTTP_GET_VARS[tep_session_name()]) ) {
	tep_session_id($HTTP_GET_VARS[tep_session_name()]);
}
*/
// lets start our session
tep_session_start();

if (tep_session_is_registered('supplier_id')) {
	$supplier_pref_setting = tep_get_supplier_pref_setting($supplier_id);
}

// set the language
if (!tep_session_is_registered('language') || isset($HTTP_GET_VARS['language'])) {
	if (!tep_session_is_registered('language')) {
    	tep_session_register('language');
      	tep_session_register('languages_id');
	}
	
    include(DIR_WS_CLASSES . 'language.php');
    $lng = new language();
	
    if (isset($HTTP_GET_VARS['language']) && tep_not_null($HTTP_GET_VARS['language'])) {
    	$lng->set_language($HTTP_GET_VARS['language']);
    } else {
      	$lng->get_browser_language();
    }
	
    $language = $lng->language['directory'];
    $languages_id = $lng->language['id'];
}

if (!tep_session_is_registered('sup_language') || isset($HTTP_GET_VARS['sup_language'])) {
	if (!tep_session_is_registered('sup_language')) {
    	tep_session_register('sup_language');
      	tep_session_register('sup_languages_id');
	}
	
    include_once(DIR_WS_CLASSES . 'language.php');
    $sup_lng = new language('', TABLE_SUPPLIER_LANGUAGES);
	
    if (isset($HTTP_GET_VARS['sup_language']) && tep_not_null($HTTP_GET_VARS['sup_language'])) {
    	$sup_lng->set_language($HTTP_GET_VARS['sup_language']);
    } else {
    	if (tep_session_is_registered('supplier_id')) {
    		$lang_code_select_sql = "SELECT code FROM " . TABLE_SUPPLIER_LANGUAGES . " WHERE languages_id = '". $supplier_pref_setting[KEY_SP_LANG] . "'";
    		$lang_code_result_sql = tep_db_query($lang_code_select_sql);
			if ($lang_code_row = tep_db_fetch_array($lang_code_result_sql)) {
				$sup_lng->set_language($lang_code_row['code']);
			} else {
				$sup_lng->get_browser_language();
			}
    	} else {
    		$sup_lng->get_browser_language();
		}
    }
	
    $sup_language = $sup_lng->language['directory'];
    $sup_languages_id = $sup_lng->language['id'];
}

// include the language translations
require(DIR_WS_LANGUAGES . $sup_language . '.php');
$current_page = basename($PHP_SELF);
if (file_exists(DIR_WS_LANGUAGES . $sup_language . '/' . $current_page)) {
	include(DIR_WS_LANGUAGES . $sup_language . '/' . $current_page);
}

// currency
if (!tep_session_is_registered('currency') || isset($HTTP_GET_VARS['currency']) || ( (USE_DEFAULT_LANGUAGE_CURRENCY == 'true') && (LANGUAGE_CURRENCY != $currency) ) ) {
	if (!tep_session_is_registered('currency')) tep_session_register('currency');

    if (isset($HTTP_GET_VARS['currency'])) {
    	if (!$currency = tep_currency_exists($HTTP_GET_VARS['currency'])) $currency = (USE_DEFAULT_LANGUAGE_CURRENCY == 'true') ? LANGUAGE_CURRENCY : DEFAULT_CURRENCY;
    } else {
      	$currency = (USE_DEFAULT_LANGUAGE_CURRENCY == 'true') ? LANGUAGE_CURRENCY : DEFAULT_CURRENCY;
    }
}

// define our localization functions
require(DIR_WS_FUNCTIONS . 'localization.php');

// Include validation functions (right now only email address)
require(DIR_WS_FUNCTIONS . 'validations.php');

// setup our boxes
require(DIR_WS_CLASSES . 'table_block.php');
require(DIR_WS_CLASSES . 'box.php');

// initialize the message stack for output messages
require(DIR_WS_CLASSES . 'message_stack.php');
$messageStack = new messageStack;

// split-page-results
require(DIR_WS_CLASSES . 'split_page_results.php');

// entry/item info classes
require(DIR_WS_CLASSES . 'object_info.php');

// email classes
require(DIR_WS_CLASSES . 'mime.php');
require(DIR_WS_CLASSES . 'email.php');

// file uploading class
require(DIR_WS_CLASSES . 'upload.php');

require(DIR_WS_CLASSES . 'currencies.php');
$currencies = new currencies();

// Time Zone class
require_once ('Date.php');

if (basename($PHP_SELF) != FILENAME_LOGIN && basename($PHP_SELF) != FILENAME_SUPPLIER_ACTIVATE && basename($PHP_SELF) != FILENAME_PASSWORD_FORGOTTEN && basename($PHP_SELF) != FILENAME_LOGOFF && basename($PHP_SELF) != FILENAME_SUPPLIER_SIGNUP) {
	tep_admin_check_login(); 
	
	$list_resources_array = array();
	
	$list_select_sql = "SELECT ppl.products_purchases_lists_id, ppl.products_purchases_lists_name 
						FROM " . TABLE_SUPPLIER_LIST_TIME_SETTING . " AS slts 
						INNER JOIN " . TABLE_PRODUCTS_PURCHASES_LISTS . " AS ppl 
							ON slts.products_purchases_lists_id=ppl.products_purchases_lists_id 
						WHERE slts.supplier_groups_id = '" . $supplier_groups_id . "' 
						ORDER BY ppl.products_purchases_lists_sort_order, ppl.products_purchases_lists_name";
	$list_result_sql = tep_db_query($list_select_sql);
	while ($list_row = tep_db_fetch_array($list_result_sql)) {
		// replace these variables later
		$time = tep_get_supplier_list_timer($supplier_id, $list_row["products_purchases_lists_id"]);
		
		$list_resources_array[$list_row["products_purchases_lists_id"]] = $time;
		$list_resources_array[$list_row["products_purchases_lists_id"]]['list_name'] = $list_row["products_purchases_lists_name"];
		
		$current_list_status = $time['status'];
		$current_status = $time['current_status'];
		$auto_off_check = (int)$time['auto_off'];
		$auto_on_check = (int)$time['auto_on'];
		$today = time();
		
		$list_type = 0;
		$confirmation_list_global_info = array();
		$entry_allowed = false;
		$enable_counter = false;
		
		$draft_or_pending_order_select_sql = "	SELECT supplier_order_lists_id 
												FROM " . TABLE_SUPPLIER_ORDER_LISTS . " 
												WHERE suppliers_id = '" . (int)$supplier_id . "' AND supplier_order_lists_status IN (1, 5) AND products_purchases_lists_id = '" . $list_row["products_purchases_lists_id"] . "' 
												ORDER BY supplier_order_lists_id DESC 
												LIMIT 1";
		$draft_or_pending_order_result_sql = tep_db_query($draft_or_pending_order_select_sql);
		if ($draft_or_pending_order_row = tep_db_fetch_array($draft_or_pending_order_result_sql)) {
			$entry_allowed = false;
		} else {
		 	if ($current_list_status == "STATUS_ON") {
		 		$entry_allowed = true;
		 		$list_type = 1;
		 		
				tep_db_query("UPDATE " . TABLE_SUPPLIER_LIST_TIME_SETTING . " SET current_status='STATUS_ON' WHERE supplier_groups_id = '" . $supplier_groups_id . "' AND products_purchases_lists_id ='".$list_row["products_purchases_lists_id"]."';");
			} else if ($current_list_status == "STATUS_AUTO") {
				$enable_counter = true;
				
				if (tep_time_check($time['first_start_time'], $time['first_end_time'])) {	// If satisfied first list time then allowed to submit first list
					$list_type = 1;
					$start_time = $time['first_start_timestamp'];
					$end_time = $time['first_end_timestamp'];
				} else if (	tep_time_check($time['second_start_time'], $time['second_end_time']) ) {
					$list_type = 1;
					$start_time = $time['second_start_timestamp'];
					$end_time = $time['second_end_timestamp'];
				}
				
				if ($current_status == 'STATUS_OFF') {
					if ($list_type && $auto_on_check) {
						$entry_allowed = true;
						
						tep_db_query("UPDATE " . TABLE_SUPPLIER_LIST_TIME_SETTING . " SET current_status='STATUS_ON' WHERE supplier_groups_id = '" . $supplier_groups_id . "' AND products_purchases_lists_id ='".$list_row["products_purchases_lists_id"]."';");
					} else {
						$entry_allowed = false;
					}
				} else if ($current_status == 'STATUS_ON') {
					if (!$list_type && $auto_off_check) {
						$entry_allowed = false;
						
						tep_db_query("UPDATE " . TABLE_SUPPLIER_LIST_TIME_SETTING . " SET current_status='STATUS_OFF' WHERE supplier_groups_id = '" . $supplier_groups_id . "' AND products_purchases_lists_id ='".$list_row["products_purchases_lists_id"]."';");
					} else {
						$entry_allowed = true;
					}
				}
			}
		}
		
		$list_resources_array[$list_row["products_purchases_lists_id"]]['entry_allowed'] = $entry_allowed ? 1 : 0;
		$list_resources_array[$list_row["products_purchases_lists_id"]]['current_list_status'] = $current_list_status;
		$list_resources_array[$list_row["products_purchases_lists_id"]]['list_type'] = $list_type;
		if (!$entry_allowed && tep_not_null($draft_or_pending_order_row["supplier_order_lists_id"])) { // first list can't be shown
			$list_resources_array[$list_row["products_purchases_lists_id"]]['second_list_order_id'] = $draft_or_pending_order_row["supplier_order_lists_id"];
		}
		$list_resources_array[$list_row["products_purchases_lists_id"]]['start_time'] = $start_time;
		$list_resources_array[$list_row["products_purchases_lists_id"]]['end_time'] = $end_time;
		$list_resources_array[$list_row["products_purchases_lists_id"]]['enable_counter'] = $enable_counter ? 1 : 0;
	}
}

// BROWSER AND VERSION
// (must check everything else before Mozilla)
if (preg_match('@Opera(/| )([0-9].[0-9]{1,2})@', $HTTP_USER_AGENT, $log_version)) {
    define('PMA_USR_BROWSER_VER', $log_version[2]);
    define('PMA_USR_BROWSER_AGENT', 'OPERA');
} else if (preg_match('@MSIE ([0-9].[0-9]{1,2})@', $HTTP_USER_AGENT, $log_version)) {
    define('PMA_USR_BROWSER_VER', $log_version[1]);
    define('PMA_USR_BROWSER_AGENT', 'IE');
} else if (preg_match('@OmniWeb/([0-9].[0-9]{1,2})@', $HTTP_USER_AGENT, $log_version)) {
    define('PMA_USR_BROWSER_VER', $log_version[1]);
    define('PMA_USR_BROWSER_AGENT', 'OMNIWEB');
//} else if (ereg('Konqueror/([0-9].[0-9]{1,2})', $HTTP_USER_AGENT, $log_version)) {
// Konqueror 2.2.2 says Konqueror/2.2.2
// Konqueror 3.0.3 says Konqueror/3
} else if (preg_match('@(Konqueror/)(.*)(;)@', $HTTP_USER_AGENT, $log_version)) {
    define('PMA_USR_BROWSER_VER', $log_version[2]);
    define('PMA_USR_BROWSER_AGENT', 'KONQUEROR');
} else if (preg_match('@Mozilla/([0-9].[0-9]{1,2})@', $HTTP_USER_AGENT, $log_version)
           && preg_match('@Safari/([0-9]*)@', $HTTP_USER_AGENT, $log_version2)) {
    define('PMA_USR_BROWSER_VER', $log_version[1] . '.' . $log_version2[1]);
    define('PMA_USR_BROWSER_AGENT', 'SAFARI');
} else if (preg_match('@Mozilla/([0-9].[0-9]{1,2})@', $HTTP_USER_AGENT, $log_version)) {
    define('PMA_USR_BROWSER_VER', $log_version[1]);
    define('PMA_USR_BROWSER_AGENT', 'MOZILLA');
} else {
    define('PMA_USR_BROWSER_VER', 0);
    define('PMA_USR_BROWSER_AGENT', 'OTHER');
}
?>