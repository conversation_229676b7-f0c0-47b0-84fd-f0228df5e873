<?
/*
  	$Id: box.php,v 1.1 2005/09/05 07:28:34 subrat Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2003 osCommerce
	
  	Released under the GNU General Public License
	
  	Example usage:
  	$heading = array();
  	$heading[] = array(	'params' => 'class="menuBoxHeading"',
                     	'text'  => BOX_HEADING_TOOLS,
                     	'link'  => tep_href_link(basename($PHP_SELF), tep_get_all_get_params(array('selected_box')) . 'selected_box=tools'));
  	
  	$contents = array();
  	$contents[] = array('text'  => SOME_TEXT);
	
  	$box = new box;
  	echo $box->infoBox($heading, $contents);
*/

class box extends tableBlock {
    function box() {
      	$this->heading = array();
      	$this->contents = array();
    }
	
    function infoBox($heading, $contents) {
      	$this->table_row_parameters = 'class="infoBoxHeading"';
      	$this->table_data_parameters = 'class="infoBoxHeading"';
      	$this->heading = $this->tableBlock($heading);
		
      	$this->table_row_parameters = '';
      	$this->table_data_parameters = 'class="infoBoxContent"';
      	$this->contents = $this->tableBlock($contents);
		
      	return $this->heading . $this->contents;
	}
	
    function menuBox($heading, $contents) {
      	$this->table_data_parameters = 'class="menuBoxHeading"';
      	if (isset($heading[0]['link'])) {
        	$this->table_data_parameters .= ' onmouseover="this.style.cursor=\'hand\'" onclick="document.location.href=\'' . $heading[0]['link'] . '\'"';
        	$heading[0]['text'] = '&nbsp;<a href="' . $heading[0]['link'] . '" '.(tep_not_null($heading[0]['link_class']) ? $heading[0]['link_class'] : 'class="menuBoxHeadingLink"').'>' . $heading[0]['text'] . '</a>&nbsp;';
      	} else {
        	$heading[0]['text'] = '&nbsp;' . $heading[0]['text'] . '&nbsp;';
      	}
      	$this->heading = $this->tableBlock($heading);
		
      	$this->table_data_parameters = 'class="menuBoxContent"';
      	$this->contents = $this->tableBlock($contents);
		
      	return $this->heading . $this->contents;
	}
	
	function setCellPadding($intPadding) {
		$this->table_cellpadding = (int)$intPadding;
	}
}
?>