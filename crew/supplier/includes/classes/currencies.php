<?
/*
  	$Id: currencies.php,v 1.9 2007/10/18 03:58:02 weichen Exp $
  	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
  	
  	Copyright (c) 2003 osCommerce
  	
  	Released under the GNU General Public License
*/
////
// Class to handle currencies
// TABLES: currencies
class currencies
{
	var $currencies;
	var $internal_currencies;
	var $decimal_places;
	
	// class constructor
	function currencies()
	{
  		$this->decimal_places = null;
		$this->currencies = array();
		$this->internal_currencies = array();
		$currencies_query = tep_db_query("select currencies_id, code, title, symbol_left, symbol_right, decimal_point, thousands_point, decimal_places, value, buy_value, sell_value from " . TABLE_CURRENCIES);
  		while ($currencies = tep_db_fetch_array($currencies_query)) {
    		$this->currencies[$currencies['code']] = array(	'title' => $currencies['title'],
            		                                       	'symbol_left' => $currencies['symbol_left'],
                    		                               	'symbol_right' => $currencies['symbol_right'],
                            		                       	'decimal_point' => $currencies['decimal_point'],
                                    		               	'thousands_point' => $currencies['thousands_point'],
                                            		       	'decimal_places' => $currencies['decimal_places'],
															'value' => $currencies['value'],
                                	                       	'buy_value' => $currencies['buy_value'],
                                	                       	'sell_value' => $currencies['sell_value']
															);
			
			$this->internal_currencies[$currencies['currencies_id']] = $currencies['code'];
  		}
	}
	
	// class methods
	function format($number, $calculate_currency_value = true, $currency_type = DEFAULT_CURRENCY, $currency_value = '', $type='buy')
	{
  		if ($calculate_currency_value) {
    		$rate = ($currency_value) ? $currency_value : $this->get_value($currency_type, $type);
    		
    		// added by subrat
        	if (is_null($this->decimal_places))
    			$format_string = $this->currencies[$currency_type]['symbol_left'] . number_format($number * $rate, $this->currencies[$currency_type]['decimal_places'], $this->currencies[$currency_type]['decimal_point'], $this->currencies[$currency_type]['thousands_point']) . $this->currencies[$currency_type]['symbol_right'];
			else
				$format_string = $this->currencies[$currency_type]['symbol_left'] . number_format(tep_round($number * $rate, $this->decimal_places), $this->decimal_places, $this->currencies[$currency_type]['decimal_point'], $this->currencies[$currency_type]['thousands_point']) . $this->currencies[$currency_type]['symbol_right'];
    		// if the selected currency is in the european euro-conversion and the default currency is euro,
			// the currency will displayed in the national currency and euro currency
    		if ( (DEFAULT_CURRENCY == 'EUR') && ($currency_type == 'DEM' || $currency_type == 'BEF' || $currency_type == 'LUF' || $currency_type == 'ESP' || $currency_type == 'FRF' || $currency_type == 'IEP' || $currency_type == 'ITL' || $currency_type == 'NLG' || $currency_type == 'ATS' || $currency_type == 'PTE' || $currency_type == 'FIM' || $currency_type == 'GRD') ) {
      			$format_string .= ' <small>[' . $this->format($number, true, 'EUR') . ']</small>';
    		}
  		} else {
  			if (is_null($this->decimal_places))  
    			$format_string = $this->currencies[$currency_type]['symbol_left'] . number_format($number, $this->currencies[$currency_type]['decimal_places'], $this->currencies[$currency_type]['decimal_point'], $this->currencies[$currency_type]['thousands_point']) . $this->currencies[$currency_type]['symbol_right'];
  			else
  				$format_string = $this->currencies[$currency_type]['symbol_left'] . number_format(tep_round($number, $this->decimal_places), $this->decimal_places, $this->currencies[$currency_type]['decimal_point'], $this->currencies[$currency_type]['thousands_point']) . $this->currencies[$currency_type]['symbol_right'];
  		}
 		return $format_string;
	}
	
	// class methods
	function format_round_down($number, $calculate_currency_value = true, $currency_type = DEFAULT_CURRENCY, $currency_value = '', $type='buy')
	{
  		if ($calculate_currency_value) {
    		$rate = ($currency_value) ? $currency_value : $this->get_value($currency_type, $type);
    		
    		// added by subrat
        	if (is_null($this->decimal_places))
    			$format_string = $this->currencies[$currency_type]['symbol_left'] . number_format($this->_currency_round_down($number*$rate, pow(10, (int)$this->currencies[$currency_type]['decimal_places'])), $this->currencies[$currency_type]['decimal_places'], $this->currencies[$currency_type]['decimal_point'], $this->currencies[$currency_type]['thousands_point']) . $this->currencies[$currency_type]['symbol_right'];
			else
				$format_string = $this->currencies[$currency_type]['symbol_left'] . number_format($this->_currency_round_down($number*$rate, pow(10, (int)$this->currencies[$currency_type]['decimal_places'])), $this->decimal_places, $this->currencies[$currency_type]['decimal_point'], $this->currencies[$currency_type]['thousands_point']) . $this->currencies[$currency_type]['symbol_right'];
    		// if the selected currency is in the european euro-conversion and the default currency is euro,
			// the currency will displayed in the national currency and euro currency
    		if ( (DEFAULT_CURRENCY == 'EUR') && ($currency_type == 'DEM' || $currency_type == 'BEF' || $currency_type == 'LUF' || $currency_type == 'ESP' || $currency_type == 'FRF' || $currency_type == 'IEP' || $currency_type == 'ITL' || $currency_type == 'NLG' || $currency_type == 'ATS' || $currency_type == 'PTE' || $currency_type == 'FIM' || $currency_type == 'GRD') ) {
      			$format_string .= ' <small>[' . $this->format_round_down($number, true, 'EUR') . ']</small>';
    		}
  		} else {
  			if (is_null($this->decimal_places))  
    			$format_string = $this->currencies[$currency_type]['symbol_left'] . number_format($this->_currency_round_down($number, pow(10, (int)$this->currencies[$currency_type]['decimal_places'])), $this->currencies[$currency_type]['decimal_places'], $this->currencies[$currency_type]['decimal_point'], $this->currencies[$currency_type]['thousands_point']) . $this->currencies[$currency_type]['symbol_right'];
  			else
  				$format_string = $this->currencies[$currency_type]['symbol_left'] . number_format($this->_currency_round_down($number, pow(10, (int)$this->decimal_places)), $this->decimal_places, $this->currencies[$currency_type]['decimal_point'], $this->currencies[$currency_type]['thousands_point']) . $this->currencies[$currency_type]['symbol_right'];
  		}
 		return $format_string;
	}
	
	//Simulate $currencies->format() but don't append currency symbols.
	function do_raw_conversion($number, $calculate_currency_value = true, $currency_type = DEFAULT_CURRENCY, $currency_value = '', $round_rules='', $type='buy') {
		$raw_number = $number;
		if ($calculate_currency_value) {
			$rate = ($currency_value) ? $currency_value : $this->get_value($currency_type, $type);
			$raw_number = $number * $rate;
		}
		
		if ($round_rules == 'UP') {
			return tep_round($raw_number, $this->currencies[$currency_type]['decimal_places']);
		} else if ($round_rules == 'DOWN') {
			return $this->_currency_round_down($raw_number, pow(10, (int)$this->currencies[$currency_type]['decimal_places']));
		} else {
			return $raw_number;
		}
	}
	
	function _currency_round_down($val, $rounder) {
		if ($rounder != 0) {
			return floor($val*$rounder) /$rounder;
		} else {
			return $val;
		}
	}
	
	function apply_currency_exchange($number, $currency_type = DEFAULT_CURRENCY, $currency_value = '', $type='buy') {
		$rate = ($currency_value) ? $currency_value : $this->get_value($currency_type, $type);
    	
    	return tep_round($number * $rate, $this->currencies[$currency_type]['decimal_places']);
	}
	
	function advanced_apply_currency_exchange($number, $from_currency_type=DEFAULT_CURRENCY, $to_currency_type=DEFAULT_CURRENCY, $type='buy') {
		$rate = $this->advance_currency_conversion_rate($from_currency_type, $to_currency_type, $type);
		
		$number = tep_round($number * $rate, is_null($this->decimal_places) ? $this->currencies[$to_currency_type]['decimal_places'] : $this->decimal_places);
		
		return $number;
	}
	
	function advance_currency_conversion_rate($from_currency_type=DEFAULT_CURRENCY, $to_currency_type=DEFAULT_CURRENCY, $type='buy') {
		if ($from_currency_type == $to_currency_type) {
			return tep_round(1, 8);
		}
		
		if ($type == 'sell') {
			if ($to_currency_type == DEFAULT_CURRENCY) {
				$rate = ($this->currencies[$from_currency_type]['sell_value'] > 0) ? (double) ($this->currencies[$to_currency_type]['buy_value'] / $this->currencies[$from_currency_type]['sell_value']) : 0;
			} else {
				$rate = ($this->currencies[$from_currency_type]['value'] > 0) ? (double) ($this->currencies[$to_currency_type]['buy_value'] / $this->currencies[$from_currency_type]['value']) : 0;
			}
        } elseif($type == 'spot') {
            $rate = ($this->currencies[$from_currency_type]['value'] > 0) ? (double) ($this->currencies[$to_currency_type]['value'] / $this->currencies[$from_currency_type]['value']) : 0;
        } else {
			if ($to_currency_type == DEFAULT_CURRENCY) {
				$rate = ($this->currencies[$from_currency_type]['buy_value'] > 0) ? (double) ($this->currencies[$to_currency_type]['sell_value'] / $this->currencies[$from_currency_type]['buy_value']) : 0;
			} else {
				$rate = ($this->currencies[$from_currency_type]['value'] > 0) ? (double) ($this->currencies[$to_currency_type]['sell_value'] / $this->currencies[$from_currency_type]['value']) : 0;
			}
		}
		
    	return tep_round($rate, 8);
	}
	
	function get_value($code, $type='buy')
    {
    	if (tep_not_null($type)) {
    		return $this->currencies[$code][$type.'_value'];
    	} else {
    		return $this->currencies[$code]['value'];
    	}
    }
    
	function get_decimal_places($code='')
	{
		if (!tep_not_null($code))	$code = DEFAULT_CURRENCY;
  		return is_null($this->decimal_places) ? $this->currencies[$code]['decimal_places'] : $this->decimal_places;
	}
	
	function get_code_by_id($cur_id)
	{
		return $this->internal_currencies[$cur_id];
	}
	
	function display_price($products_price, $products_tax, $quantity = 1)
	{
  		return $this->format(tep_add_tax($products_price, $products_tax) * $quantity);
	}
	
    function set_decimal_places($decimal_place)
    {
    	$decimal_place = (int)$decimal_place;
    	
    	if ($decimal_place<=0)
    		$this->decimal_places = null;
    	else
    		$this->decimal_places = $decimal_place;
    }	
}
?>