<?
class custom_product
{
	var $info, $totals, $products, $supplier, $payment, $custom_product_id;
	
    function custom_product($custom_product_id)
    {
      	$this->info = array();
      	$this->totals = array();
      	$this->products = array();
      	$this->supplier = array();
		$this->payment = array();
		
		$this->custom_product_id = $custom_product_id;
		
      	$this->query($custom_product_id);
    }
    
    function query($custom_product_id) {
    	$custom_product_select_sql = "SELECT op.*, sta,* FROM " . TABLE_SUPPLIER_TASKS_ALLOCATION . " AS sta INNER JOIN " . TABLE_ORDERS_PRODUCTS . " AS op ON (op.orders_products_id = sta.orders_products_id) WHERE sta.orders_products_id='" . (int)$custom_product_id . "'" ;
    	$custom_product_result_sql = tep_db_query($custom_product_select_sql);
    	$custom_product_row = tep_db_fetch_array($custom_product_result_sql);
    	
    	$supplier_personal_select_sql = "	SELECT s.supplier_code, s.supplier_gender, s.supplier_dob, s.supplier_date_account_created AS date_created, s.supplier_fax, 
												s.supplier_payment_paypal, s.supplier_payment_bank_name, s.supplier_payment_bank_swift_code, s.supplier_payment_bank_address, s.supplier_payment_bank_telephone, s.supplier_payment_bank_account_name, s.supplier_payment_bank_account_number, sg.supplier_groups_name 
											FROM " . TABLE_SUPPLIER . " AS s 
											LEFT JOIN " . TABLE_SUPPLIER_GROUPS . " AS sg 
												ON s.supplier_groups_id = sg.supplier_groups_id 
											WHERE s.supplier_id = '" . $custom_product_row["suppliers_id"] . "'";
		$supplier_personal_result_sql = tep_db_query($supplier_personal_select_sql);
		$supplier_personal_row = tep_db_fetch_array($supplier_personal_result_sql);
    	
    	$this->info =     array('currency_value' => $custom_product_row["final_price"],
    							'product_name' => $custom_product_row["product_name"],
    							'custom_products_type_id' => $custom_product_row["custom_products_type_id"],
    							'products_categories_id' => $custom_product_row["products_categories_id"]);
    	
    	$this->supplier = array('id' => $order['suppliers_id'],
      							'name' => $order['suppliers_name'],
                              	'street_address' => $order['suppliers_street_address'],
                              	'suburb' => $order['suppliers_suburb'],
                              	'city' => $order['suppliers_city'],
                              	'postcode' => $order['suppliers_postcode'],
                              	'state' => $order['suppliers_state'],
                              	'country' => $order['suppliers_country'],
                              	'format_id' => 1,
                              	'telephone' => $order['suppliers_telephone'],
                              	'fax' => $supplier_personal_row['supplier_fax'],
                              	'email_address' => $order['suppliers_email_address'],
                              	'gender' => $supplier_personal_row['supplier_gender'],
                              	'dob' => $supplier_personal_row['supplier_dob'],
                              	'date_account_created' => $supplier_personal_row['date_created'],
                              	'supplier_group' => $supplier_personal_row['supplier_groups_name'],
                              	'code' => $supplier_personal_row['supplier_code']);
		
		$this->payment = array(	'paypal_email' => $supplier_personal_row['supplier_payment_paypal'],
                              	'bank_name' => $supplier_personal_row['supplier_payment_bank_name'],
                              	'bank_swift_code' => $supplier_personal_row['supplier_payment_bank_swift_code'],
                              	'bank_address' => $supplier_personal_row['supplier_payment_bank_address'],
                              	'bank_telephone' => $supplier_personal_row['supplier_payment_bank_telephone'],
                              	'bank_account_name' => $supplier_personal_row['supplier_payment_bank_account_name'],
                              	'bank_account_number' => $supplier_personal_row['supplier_payment_bank_account_number']);
	}

	function get_order_product_total_payable_amount($custom_product_id='') {
		$payable_price = 0;
		
		if (!tep_not_null($custom_product_id)) {
			$custom_product_id = $this->custom_product_id;
		}
		
		$custom_product_info_select_sql = "SELECT IF(op.orders_products_store_price IS NULL, (((op.final_price * sta.supplier_tasks_allocation_ratio_adjust) / 100) + sta.supplier_payable_adjust), (((op.orders_products_store_price * sta.supplier_tasks_allocation_ratio_adjust) / 100) + sta.supplier_payable_adjust)) AS payable_price FROM " . TABLE_ORDERS_PRODUCTS . " AS op INNER JOIN " . TABLE_SUPPLIER_TASKS_ALLOCATION . " AS sta ON (op.orders_products_id = sta.orders_products_id) WHERE op.orders_products_id ='" . (int)$custom_product_id . "'";
		$custom_product_info_result_sql = tep_db_query($custom_product_info_select_sql);
		$custom_product_info_row = tep_db_fetch_array($custom_product_info_result_sql);
		
		$payable_price = $custom_product_info_row["payable_price"];
		
		return $payable_price;
	}
}
?>