<?
class custom_product_payment
{
	var $info, $payment_info, $orders, $supplier;
	var $payment_id;
	
    function custom_product_payment($payment_id='')
    {
      	$this->info = array();
      	$this->orders = array();
      	$this->supplier = array();
		$this->payment_info = array();
					
		if (tep_not_null($payment_id)) {
			$this->payment_id = $payment_id;
      		$this->query($payment_id);
      	}
    }
	
    function query($payment_id)
    {
    	$payment_info_select_sql = "SELECT * FROM " . TABLE_SUPPLIER_CP_PAYMENTS . " WHERE supplier_cp_payments_id = '" . tep_db_input($payment_id) . "'";
		$payment_info_result_sql = tep_db_query($payment_info_select_sql);
      	$payment_info_row = tep_db_fetch_array($payment_info_result_sql);
		
		$supplier_personal_select_sql = "	SELECT s.supplier_code, s.supplier_payment_paypal, s.supplier_payment_bank_name, s.supplier_payment_bank_swift_code, s.supplier_payment_bank_address, s.supplier_payment_bank_telephone, s.supplier_payment_bank_account_name, s.supplier_payment_bank_account_number, sg.supplier_groups_name 
											FROM " . TABLE_SUPPLIER . " AS s 
											LEFT JOIN " . TABLE_SUPPLIER_GROUPS . " AS sg 
												ON s.supplier_groups_id = sg.supplier_groups_id 
											WHERE s.supplier_id = '" . $payment_info_row['suppliers_id'] . "'";
		$supplier_personal_result_sql = tep_db_query($supplier_personal_select_sql);
		$supplier_personal_row = tep_db_fetch_array($supplier_personal_result_sql);
		
      	$this->info = array('currency' => $payment_info_row['currency'],
                          	'currency_value' => $payment_info_row['currency_value'],
                          	'payments_amount' => $payment_info_row['supplier_cp_payments_amount'],
                          	'payments_tax' => $payment_info_row['supplier_cp_payments_tax'],
                          	'payments_total' => $payment_info_row['supplier_cp_payments_total'],
                          	'payments_date' => $payment_info_row['supplier_cp_payments_date'],
                          	'last_modified' => $payment_info_row['supplier_cp_payments_last_modified'],
                          	'payments_status' => $payment_info_row['supplier_cp_payments_status']
                          	);
		
      	$this->supplier = array('id' => $payment_info_row['suppliers_id'],
      							'firstname' => $payment_info_row['suppliers_firstname'],
                              	'lastname' => $payment_info_row['suppliers_lastname'],
                              	'street_address' => $payment_info_row['suppliers_street_address'],
                              	'suburb' => $payment_info_row['suppliers_suburb'],
                              	'city' => $payment_info_row['suppliers_city'],
                              	'postcode' => $payment_info_row['suppliers_postcode'],
                              	'state' => $payment_info_row['suppliers_state'],
                              	'country' => $payment_info_row['suppliers_country'],
                              	'format_id' => 1,
                              	'email_address' => $payment_info_row['suppliers_email_address'],
                              	'supplier_group' => $supplier_personal_row['supplier_groups_name'],
                              	'code' => $supplier_personal_row['supplier_code']);
		
		$this->payment_info = array('paypal_email' => $supplier_personal_row['supplier_payment_paypal'],
                              		'bank_name' => $supplier_personal_row['supplier_payment_bank_name'],
	                              	'bank_swift_code' => $supplier_personal_row['supplier_payment_bank_swift_code'],
	                              	'bank_address' => $supplier_personal_row['supplier_payment_bank_address'],
	                              	'bank_telephone' => $supplier_personal_row['supplier_payment_bank_telephone'],
	                              	'bank_account_name' => $supplier_personal_row['supplier_payment_bank_account_name'],
	                              	'bank_account_number' => $supplier_personal_row['supplier_payment_bank_account_number']);
        
  		$index = 0;
  		
  		$payment_orders_select_sql = "	SELECT scpp.orders_products_id, scpp.supplier_cp_payments_products_paid_amount, scpp.supplier_cp_payments_type, op.orders_products_id, op.orders_id, op.products_name FROM " . TABLE_SUPPLIER_CP_PAYMENTS_PRODUCTS . " AS scpp INNER JOIN " . TABLE_ORDERS_PRODUCTS . " AS op ON (op.orders_products_id = scpp.orders_products_id) WHERE supplier_cp_payments_id ='" . tep_db_input($payment_id) . "'";
  		$payment_orders_result_sql = tep_db_query($payment_orders_select_sql);
  		while ($payment_orders_row = tep_db_fetch_array($payment_orders_result_sql)) {
  			$this->orders[$index] = array(  'id' => $payment_orders_row["orders_id"],
  											'orders_products_id' => $payment_orders_row["orders_products_id"],
  											'product_name' => $payment_orders_row["products_name"],
  											'paid_amount' => $payment_orders_row["supplier_cp_payments_products_paid_amount"],
  											'payments_type' => $payment_orders_row["supplier_payments_type"]
  										);
  										
  			$index++;
  			//echo $payment_orders_row["supplier_cp_payments_products_paid_amount"];
  		}
	}

	function get_order_paid_history($orders_products_id, $all=true)
	{
		$payment_history_array = '';
		$order_product_payments_select_sql = "	SELECT scpp.supplier_cp_payments_id, scpp.supplier_cp_payments_products_paid_amount, scpp.supplier_cp_payments_type, scp.currency, scp.currency_value 
										FROM " . TABLE_SUPPLIER_CP_PAYMENTS_PRODUCTS . " AS scpp 
										INNER JOIN " . TABLE_SUPPLIER_CP_PAYMENTS . " AS scp 
											ON (scpp.supplier_cp_payments_id=scp.supplier_cp_payments_id) 
										WHERE scpp.orders_products_id = '" . tep_db_input($orders_products_id) . "' " . (!$all ? "AND scp.supplier_cp_payments_status IN (1, 2) " : '') . " 
										ORDER BY scp.supplier_cp_payments_date";
		
		$order_product_payments_result_sql = tep_db_query($order_product_payments_select_sql);
		
		while ($order_product_payments_row = tep_db_fetch_array($order_product_payments_result_sql)) {
			$payment_history_array = array(	'payment_id' => $order_product_payments_row['supplier_cp_payments_id'],
											'paid_amount' => $order_product_payments_row['supplier_cp_payments_products_paid_amount'],
											'paid_currency' => $order_product_payments_row['currency'],
											'paid_currency_value' => $order_product_payments_row['currency_value'],
											'payment_type' => $order_product_payments_row['supplier_cp_payments_type']
											);
		}
		
		return $payment_history_array;
	}
}
?>