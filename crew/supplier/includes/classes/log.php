<?
/*
  	$Id: log.php,v 1.1 2005/09/05 07:28:34 subrat Exp $
	
  	Developer: <PERSON> (c) 2005 SKC Ventrue
	
  	Released under the GNU General Public License
*/

class log_files {
	var $identity;
	var $table = TABLE_LOG_TABLE;
	
	function log_files($identity)
	{
		$this->identity = $identity;
	}
	
	function insert_log($prod_id, $field_name, $from_val='', $to_val='', $admin_msg='', $user_msg='')
	{
		$sql = sprintf(	'	INSERT INTO %s (log_admin_id, log_ip, log_time, log_products_id, log_system_messages, log_user_messages, log_field_name, log_from_value, log_to_value) ' . '
							VALUES ("%s", "%s", NOW(), %d, "%s", "%s", "%s", "%s", "%s")',
                     		$this->table, $this->identity, tep_db_input(getenv("REMOTE_ADDR")), $prod_id, tep_db_input($admin_msg),
                     		tep_db_input($user_msg), tep_db_input($field_name), tep_db_input($from_val), tep_db_input($to_val));
		tep_db_query($sql);
	}
	
	function insert_orders_log($order_id, $orders_log_system_msg='')
	{
		$sql = sprintf(	'	INSERT INTO %s (orders_log_admin_id, orders_log_ip, orders_log_time, orders_log_orders_id, orders_log_system_messages) ' . '
							VALUES ("%s", "%s", NOW(), "%s", "%s")',
                     		$this->table, $this->identity, tep_db_input(getenv("REMOTE_ADDR")), $order_id, tep_db_input($orders_log_system_msg));
		tep_db_query($sql);
	}
	
	function set_log_table($table_name) {
		if (tep_not_null($table_name)) {
			$this->table = $table_name;
		}
	}
	
	function draw_inputs($form_name="log_form", $input_array=array())
	{
		echo '	<tr>
        			<td>
        				<table border="0" width="100%" cellspacing="2" cellpadding="0">';
		foreach ($input_array as $key => $resources) {
			$input_str = "";
			switch ($resources["type"]) {
				case "date":
					$input_str = '	<script language="javascript"><!--
  										var ' . "date_$key" . ' = new ctlSpiffyCalendarBox("' . "date_$key" . '", "' . $form_name . '", "' . $key . '", "' . "btnDate_$key". '", "", scBTNMODE_CUSTOMBLUE);
										//--></script>';
					$input_str .= '	<tr>
            							<td class="main" width="10%">' . $resources["title"] . '<br><small>(' . strtoupper($resources["format"]) . ')</small></td>
            							<td class="main" align="laft"><script language="javascript">' . "date_$key" . '.writeControl(); ' . "date_$key" . '.dateFormat="' . $resources["format"] . '"; document.getElementById(\''.$key.'\').value=\''.$resources["default_value"].'\';</script>' .
            							($resources["required"] ? '<span class="fieldRequired">*</span>' : '') . $resources["extra_msg"].'</td>
          							</tr>';
          			$input_str .= '	<tr>
            							<td colspan="2">' . tep_draw_separator('pixel_trans.gif', '1', '10') . '</td>
          							</tr>';
					break;
				case "text":
					$input_str = '	<tr>
										<td class="main">' . $resources["title"] . '</td>
										<td class="main">' . tep_draw_input_field("$key", "$resources[default_value]", "$resources[params]") . ($resources["required"] ? '<sup><span style="color:red;">*</span></sup>' : '');
					
				 	if (isset($resources["lookup"])) {
				 		$input_str .= '&nbsp;<a href="javascript:openNewWin(\''. tep_href_link($resources["lookup"]["file"], $resources["lookup"]["params"]) . '\', \'product_list\', \'WIDTH=600,HEIGHT=250\');">' . $resources["lookup"]["link"] . '</a>';
				 	}
					$input_str .= '		</td>
									</tr>';
					$input_str .= '	<tr>
            							<td colspan="2">' . tep_draw_separator('pixel_trans.gif', '1', '10') . '</td>
          							</tr>';
					break;
				case "select":
					$input_str = '	<tr >
										<td class="main">' . $resources["title"] . '</td>
							            <td class="main">' . tep_draw_pull_down_menu("$key", $resources["source"], $resources["default_value"], '') . 
							            ($resources["required"] ? '<span class="fieldRequired">*</span>' : '') . '</td>
									</tr>';
					$input_str .= '	<tr>
            							<td colspan="2">' . tep_draw_separator('pixel_trans.gif', '1', '10') . '</td>
          							</tr>';
					break;
				case "multi_select":
				 //size=10
					$input_str = '	<tr >
										<td class="main">' . $resources["title"] . '</td>
							            <td class="main">' . tep_draw_pull_down_menu("$key", $resources["source"], $resources["default_value"], 'multiple="multiple" size="' . $resources["size"].'" ' . $resources["params"]) . 
							            ($resources["required"] ? '<span class="fieldRequired">*</span>' : '') . '</td>';
					if (tep_not_null($resources["js"])) {
						$input_str .= "\n" . $resources["js"] . "\n";
					}
					$input_str .='	</tr>';
					$input_str .= '	<tr>
            							<td colspan="2">' . tep_draw_separator('pixel_trans.gif', '1', '10') . '</td>
          							</tr>';
					break;
				case "checkbox":
					$input_str = '	<tr >
										<td class="main">' . $resources["title"] . '</td>';
					if ($resources["format"] == "horizontal") {
						$input_str .= ' <td class="main">';
						foreach ($resources["source"] as $ind_checkbox) {
							$input_str .= tep_draw_checkbox_field($ind_checkbox["id"], (isset($ind_checkbox["value"]) ? $ind_checkbox["value"] : $ind_checkbox["text"]), ( $ind_checkbox["checked"] ? true : false), "", $ind_checkbox["params"]) . '&nbsp;' . $ind_checkbox["text"] . '&nbsp;&nbsp;';
						}
						$input_str .= ($resources["required"] ? '<span class="fieldRequired">*</span>' : '') . '</td>';
					} else if ($resources["format"] == "vertical") {
						$input_str .= ' <td class="main">';
						foreach ($resources["source"] as $ind_checkbox) {
							$input_str .= (tep_not_null($resources["spacer"]) ? tep_draw_separator('pixel_trans.gif', $resources["spacer"], '1') : '') . tep_draw_checkbox_field($resources["use_key"] ? $key : $ind_checkbox["id"], (isset($ind_checkbox["value"]) ? $ind_checkbox["value"] : $ind_checkbox["text"]), ( $ind_checkbox["checked"] ? true : false), "", $ind_checkbox["params"]) . '&nbsp;' . $ind_checkbox["text"] . '<br>';
						}
						$input_str .= ($resources["required"] ? '<span class="fieldRequired">*</span>' : '') . '</td>';
					}
					
					if (tep_not_null($resources["js"])) {
						$input_str .= "\n" . $resources["js"] . "\n";
					}
					$input_str .= '	</tr>';
					$input_str .= '	<tr>
            							<td colspan="2">' . tep_draw_separator('pixel_trans.gif', '1', '10') . '</td>
          							</tr>';
					break;
				case "radio":
					$input_str = '	<tr >
										<td class="main">' . $resources["title"] . '</td>';
					if ($resources["format"] == "horizontal") {
						$input_str .= ' <td class="main">';
						foreach ($resources["source"] as $ind_radio) {
							$input_str .= tep_draw_radio_field($ind_radio["name"], (isset($ind_radio["value"]) ? $ind_radio["value"] : $ind_radio["text"]), ( $ind_radio["checked"] ? true : false), "", $ind_radio["params"]) . '&nbsp;' . $ind_radio["text"] . '&nbsp;&nbsp;';
						}
						$input_str .= ($resources["required"] ? '<span class="fieldRequired">*</span>' : '') . '</td>';
					}
					if (tep_not_null($resources["js"])) {
						$input_str .= "\n" . $resources["js"] . "\n";
					}
					$input_str .= '	</tr>';
					$input_str .= '	<tr>
            							<td colspan="2">' . tep_draw_separator('pixel_trans.gif', '1', '10') . '</td>
          							</tr>';
					break;
			}
			echo $input_str;
		}
		echo '			</table>
					</td>
				</tr>';
	}
}
?>