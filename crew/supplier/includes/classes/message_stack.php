<?
/*
  	$Id: message_stack.php,v 1.2 2006/01/16 09:35:41 weichen Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2003 osCommerce
	
  	Released under the GNU General Public License
	
  	Example usage:
	
  	$messageStack = new messageStack();
  	$messageStack->add('Error: Error 1', 'error');
  	$messageStack->add('Error: Error 2', 'warning');
  	if ($messageStack->size > 0) echo $messageStack->output();
*/

class messageStack extends tableBlock {
	var $size = 0;
	
    function messageStack() {
      	global $messageToStack;
		
      	$this->errors = array();
		
      	if (tep_session_is_registered('messageToStack')) {
        	for ($i = 0, $n = sizeof($messageToStack); $i < $n; $i++) {
          		$this->add($messageToStack[$i]['text'], $messageToStack[$i]['type']);
        	}
        	tep_session_unregister('messageToStack');
		}
	}
	
    function add($message, $type = 'error') {
      	if ($type == 'error') {
        	$this->errors[] = array('params' => 'class="messageStackError"', 'text' => tep_image(DIR_WS_ICONS . 'error.gif', ICON_ERROR) . '&nbsp;' . $message, 'type' => $type, 'org_text' => $message);
      	} else if ($type == 'warning') {
        	$this->errors[] = array('params' => 'class="messageStackWarning"', 'text' => tep_image(DIR_WS_ICONS . 'warning.gif', ICON_WARNING) . '&nbsp;' . $message, 'type' => $type, 'org_text' => $message);
      	} else if ($type == 'success') {
        	$this->errors[] = array('params' => 'class="messageStackSuccess"', 'text' => tep_image(DIR_WS_ICONS . 'success.gif', ICON_SUCCESS) . '&nbsp;' . $message, 'type' => $type, 'org_text' => $message);
      	} else {
        	$this->errors[] = array('params' => 'class="messageStackError"', 'text' => $message, 'type' => $type, 'org_text' => $message);
      	}
		
      	$this->size++;
	}
	
    function add_session($message, $type = 'error') {
      	global $messageToStack;
		
      	if (!tep_session_is_registered('messageToStack')) {
        	tep_session_register('messageToStack');
        	$messageToStack = array();
      	}
		
      	$messageToStack[] = array('text' => $message, 'type' => $type);
	}
	
    function reset() {
      	$this->errors = array();
      	$this->size = 0;
    }
	
    function output() {
      	$this->table_data_parameters = 'class="messageBox"';
      	return $this->tableBlock($this->errors);
	}
	
	function redirect_message() {
		for ($i=0; $i < count($this->errors); $i++) {
      		$this->add_session($this->errors[$i]['org_text'], $this->errors[$i]['type']);
    	}
	}
}
?>