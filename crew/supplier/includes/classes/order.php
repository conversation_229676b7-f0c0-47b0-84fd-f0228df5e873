<?
/*
	$Id: order.php,v 1.19 2007/05/02 08:41:23 sunny Exp $
	
  	Developer: <PERSON> (c) 2006 SKC Ventrue
	
  	Released under the GNU General Public License
*/

class order
{
	var $info, $products, $supplier, $order_id;
	
    function order($order_id='')
    {
      	$this->info = array();
      	$this->products = array();
      	$this->supplier = array();
		
		if (tep_not_null($order_id)) {
			$this->order_id = $order_id;
        	$this->query($order_id);
      	} else {
        	$this->cart();
      	}
    }
	
	function query($order_id)
    {
    	global $supplier_id;
    	
		$order_query = tep_db_query("SELECT * FROM " . TABLE_SUPPLIER_ORDER_LISTS . " WHERE supplier_order_lists_id = '" . tep_db_input($order_id) . "' AND suppliers_id = '" . tep_db_input($supplier_id) . "'");
      	$order = tep_db_fetch_array($order_query);
		
		$supplier_personal_select_sql = "	SELECT s.supplier_code, s.supplier_gender, s.supplier_dob, s.supplier_qq, s.supplier_msn, s.supplier_date_account_created AS date_created, s.supplier_fax, sg.supplier_groups_name 
											FROM " . TABLE_SUPPLIER . " AS s 
											LEFT JOIN " . TABLE_SUPPLIER_GROUPS . " AS sg 
												ON s.supplier_groups_id = sg.supplier_groups_id 
											WHERE s.supplier_id = '" . $order["suppliers_id"] . "'";
		$supplier_personal_result_sql = tep_db_query($supplier_personal_select_sql);
		$supplier_personal_row = tep_db_fetch_array($supplier_personal_result_sql);
		
      	$this->info = array('currency' => $order['currency'],
                          	'currency_value' => $order['currency_value'],
                          	'date_submitted' => $order['supplier_order_lists_date'],
                          	'orders_status' => $order['supplier_order_lists_status'],
                          	'list_name' => $order['products_purchases_lists_name'],
                          	'remote_addr' => $order['remote_addr'],
                          	'last_modified' => $order['supplier_order_lists_last_modified']);
		
      	$this->supplier = array('id' => $order['suppliers_id'],
      							'name' => $order['suppliers_name'],
                              	'street_address' => $order['suppliers_street_address'],
                              	'suburb' => $order['suppliers_suburb'],
                              	'city' => $order['suppliers_city'],
                              	'postcode' => $order['suppliers_postcode'],
                              	'state' => $order['suppliers_state'],
                              	'country' => $order['suppliers_country'],
                              	'format_id' => 1,
                              	'telephone' => $order['suppliers_telephone'],
                              	'fax' => $supplier_personal_row['supplier_fax'],
                              	'qq' => $supplier_personal_row['supplier_qq'],
                              	'msn' => $supplier_personal_row['supplier_msn'],
                              	'email_address' => $order['suppliers_email_address'],
                              	'gender' => $supplier_personal_row['supplier_gender'],
                              	'dob' => $supplier_personal_row['supplier_dob'],
                              	'date_account_created' => $supplier_personal_row['date_created'],
                              	'supplier_group' => $supplier_personal_row['supplier_groups_name'],
                              	'code' => $supplier_personal_row['supplier_code']);
		
		// Grab the first list products
      	$index = 0;
      	$unique_product_select_sql = "SELECT DISTINCT products_id AS pid FROM " . TABLE_SUPPLIER_ORDER_LISTS_PRODUCTS . " WHERE supplier_order_lists_id = '" . tep_db_input($order_id) . "'";
      	$unique_product_result_sql = tep_db_query($unique_product_select_sql);
      	while ($unique_product_row = tep_db_fetch_array($unique_product_result_sql)) {
      		$first_orders_products_query = tep_db_query("SELECT * FROM " . TABLE_SUPPLIER_ORDER_LISTS_PRODUCTS . " WHERE supplier_order_lists_id = '" . tep_db_input($order_id) . "' AND products_id = '" . tep_db_input($unique_product_row["pid"]) . "' AND supplier_order_lists_type=1");
        	$first_orders_row = tep_db_fetch_array($first_orders_products_query);
        	
        	$confirmed_orders_products_query = tep_db_query("SELECT * FROM " . TABLE_SUPPLIER_ORDER_LISTS_PRODUCTS . " WHERE supplier_order_lists_id = '" . tep_db_input($order_id) . "' AND products_id = '" . tep_db_input($unique_product_row["pid"]) . "' AND supplier_order_lists_type=2");
        	$confirmed_orders_row = tep_db_fetch_array($confirmed_orders_products_query);
        	
        	$payable_amount = 0;
        	if (tep_db_num_rows($confirmed_orders_products_query)) {
        		$payable_amount = $this->get_payable_amount($confirmed_orders_row["products_received_quantity"], $confirmed_orders_row["first_max_quantity"], $confirmed_orders_row["first_max_unit_price"], $confirmed_orders_row["second_max_quantity"], $confirmed_orders_row["second_max_unit_price"]);
        	}
        	
        	$this->products[$index] = array('id' => $unique_product_row['pid'],
											'first_list' => $first_orders_row,
	                                       	'confirm_list' => $confirmed_orders_row,
	                                       	'payable_amount' => $payable_amount
	                                       	);
			
        	$index++;
      	}
	}
	
	function get_order_total_payable_amount($order_id='')
	{
		if (!tep_not_null($order_id)) {
			$order_id = $this->order_id;
		}
		
		$total_payable_amount = 0;
		
		$distinct_product_select_sql = "SELECT DISTINCT solp.products_id 
										FROM " . TABLE_SUPPLIER_ORDER_LISTS_PRODUCTS . " AS solp 
										WHERE solp.supplier_order_lists_id = '" . tep_db_input($order_id) . "' 
											AND solp.supplier_order_lists_type=2";
		$distinct_product_result_sql = tep_db_query($distinct_product_select_sql);
		
		while ($distinct_product_row = tep_db_fetch_array($distinct_product_result_sql)) {
			$payable_amount_select_sql = "	SELECT (IF(solp.products_received_quantity > solp.first_max_quantity, IF(solp.products_received_quantity > solp.first_max_quantity+solp.second_max_quantity, solp.first_max_quantity*solp.first_max_unit_price + solp.second_max_quantity*solp.second_max_unit_price, solp.first_max_quantity*solp.first_max_unit_price + (solp.products_received_quantity-solp.first_max_quantity)*solp.second_max_unit_price), solp.products_received_quantity*solp.first_max_unit_price)) AS total_amount 
											FROM " . TABLE_SUPPLIER_ORDER_LISTS_PRODUCTS . " AS solp 
											WHERE solp.supplier_order_lists_id = '" . tep_db_input($order_id) . "' 
												AND solp.supplier_order_lists_type=2 
												AND solp.products_id = '" . tep_db_input($distinct_product_row['products_id']) . "' 
											GROUP BY solp.products_id 
											LIMIT 1	";
			$payable_amount_result_sql = tep_db_query($payable_amount_select_sql);
			
			while ($payable_amount_row = tep_db_fetch_array($payable_amount_result_sql)) {
				$total_payable_amount += $payable_amount_row["total_amount"];
			}
		}
		
		return $total_payable_amount;
	}
	
	function get_payable_amount($received_qty, $first_max, $first_max_unit_price, $second_max, $second_max_unit_price) {
		$payable_amount = 0;
		$received_qty = (int)$received_qty;
		
		if ($received_qty > $first_max) {
			if ($received_qty > $first_max + $second_max) {
				$payable_amount = $first_max * $first_max_unit_price + $second_max * $second_max_unit_price;
			} else {
				$payable_amount = $first_max * $first_max_unit_price + ($received_qty - $first_max) * $second_max_unit_price;
			}
		} else {
			$payable_amount = $received_qty * $first_max_unit_price;
		}
		
		return $payable_amount;
	}
	
	function cart()
    {
    	global $supplier_id, $supplier_groups_id, $languages_id, $currency, $currencies, $list_type, $list_id;
		
		$supplier_pricing_setting_array = tep_get_supplier_pricing_setting($supplier_groups_id, $list_id);
		$total_active_supplier = tep_get_active_suppliers_count($supplier_groups_id);
		
		$supplier_address_select_sql = "SELECT s.*, z.zone_name, co.countries_id, co.countries_name, co.countries_iso_code_2, co.countries_iso_code_3, co.address_format_id  
										FROM " . TABLE_SUPPLIER . " AS s
										LEFT JOIN " . TABLE_ZONES . " AS z 
											ON (s.supplier_zone_id = z.zone_id) 
										LEFT JOIN " . TABLE_COUNTRIES . " AS co 
											ON (s.supplier_country_id = co.countries_id) 
										WHERE s.supplier_id = '" . (int)$supplier_id . "'";
		
      	$supplier_address_result_sql = tep_db_query($supplier_address_select_sql);
      	$supplier_address_row = tep_db_fetch_array($supplier_address_result_sql);
		
		$list_info_select_sql = "SELECT products_purchases_lists_name, products_purchases_lists_qty_round_up FROM " . TABLE_PRODUCTS_PURCHASES_LISTS . " WHERE products_purchases_lists_id = '" . (int)$list_id . "'";
		$list_info_result_sql = tep_db_query($list_info_select_sql);
      	$list_info_row = tep_db_fetch_array($list_info_result_sql);
      	$list_qty_round_up = (int)$list_info_row["products_purchases_lists_qty_round_up"] > 0 ? (int)$list_info_row["products_purchases_lists_qty_round_up"] : 1;
      	
      	$this->info = array('order_status' => $list_type == '2' ? 1 : 5,	// draft or pending
      						'list_id' => $list_id,
      						'list_name' => $list_info_row["products_purchases_lists_name"],
                          	'currency' => $currency,
                          	'currency_value' => $currencies->currencies[$currency]['value']);
		
      	$this->supplier = array('firstname' => $supplier_address_row['supplier_firstname'],
                              	'lastname' => $supplier_address_row['supplier_lastname'],
                              	'gender' => $supplier_address_row['supplier_gender'],
                              	'street_address' => $supplier_address_row['supplier_street_address'],
                              	'suburb' => $supplier_address_row['supplier_suburb'],
                              	'city' => $supplier_address_row['supplier_city'],
                              	'postcode' => $supplier_address_row['supplier_postcode'],
                              	'state' => ((tep_not_null($supplier_address_row['supplier_state'])) ? $supplier_address_row['supplier_state'] : $supplier_address_row['zone_name']),
                              	'zone_id' => $supplier_address_row['supplier_zone_id'],
                              	'country' => array('id' => $supplier_address_row['countries_id'], 'title' => $supplier_address_row['countries_name'], 'iso_code_2' => $supplier_address_row['countries_iso_code_2'], 'iso_code_3' => $supplier_address_row['countries_iso_code_3']),
                              	'telephone' => $supplier_address_row['supplier_telephone'],
                              	'email_address' => $supplier_address_row['supplier_email_address']);
		
		$supplier_cart_select_sql = "SELECT products_id, buyback_basket_quantity, buyback_basket_amount, buyback_basket_comment FROM " . TABLE_BUYBACK_BASKET . " WHERE customers_id ='" . (int)$supplier_id . "' AND buyback_basket_user_type='1' AND products_purchases_lists_id = '".(int)$list_id."' ORDER BY buyback_basket_id;";
		$supplier_cart_result_sql = tep_db_query($supplier_cart_select_sql);
		
		$index = 0;
		while ($supplier_cart_row = tep_db_fetch_array($supplier_cart_result_sql)) {
			$products_list_select_sql = "	SELECT p.products_quantity as inventory_qty, pd.products_name, p.products_cat_path, sp.*, pp.products_purchases_selling_quantity AS selling_qty, rci.restock_character 
											FROM " . TABLE_PRODUCTS . " AS p 
											INNER JOIN " . TABLE_PRODUCTS_DESCRIPTION . " AS pd 
												ON p.products_id=pd.products_id 
											INNER JOIN " . TABLE_SUPPLIER_PRICING . " AS sp 
												ON (p.products_id=sp.products_id AND sp.supplier_groups_id='" . $supplier_groups_id . "') 
											INNER JOIN " . TABLE_PRODUCTS_PURCHASES . " as pp 
												ON (sp.products_purchases_lists_id=pp.products_purchases_lists_id AND pp.products_purchases_disabled=0 AND p.products_id=pp.products_id) 
											LEFT JOIN " . TABLE_RESTOCK_CHARACTER_INFO . " AS rci 
												ON (p.products_id=rci.products_id AND rci.restock_character_sets_id='" . tep_db_input($supplier_pricing_setting_array[KEY_SPS_RSTK_CHAR_SET]) . "') 
											WHERE sp.products_purchases_lists_id = '" . $list_id . "' AND p.products_id='" . $supplier_cart_row["products_id"]."' AND p.products_bundle='' AND p.products_bundle_dynamic='' AND p.custom_products_type_id=0";
			$products_list_result_sql = tep_db_query($products_list_select_sql);
			if ($products_list_row = tep_db_fetch_array($products_list_result_sql)) {
				$product_display_name = tep_display_category_path($products_list_row['products_cat_path'] . " > " . $products_list_row['products_name'], $supplier_cart_row["products_id"], 'product');
				
				if ($total_active_supplier > 0) {
					if (tep_not_null($products_list_row['supplier_pricing_max_quantity']) && is_numeric($products_list_row['supplier_pricing_max_quantity'])) {	// If overwrite maximum qty is set
						$group_max_qty = (int)$products_list_row['supplier_pricing_max_quantity'];
					} else {
						if ($supplier_pricing_setting_array[KEY_SPS_USE_MFC_SUPPLY] == '1') {
							$group_max_qty = (int)$supplier_pricing_setting_array[KEY_SPS_MFC_SUPPLY_MAX_QTY];
						} else {
							$group_max_qty = ceil(((int)$products_list_row['selling_qty'] * (double)$supplier_pricing_setting_array[KEY_SPS_QUANTITY_RATIO]) / 100);
						}
					}
					$offset_amount = tep_get_previously_restocked_qty($supplier_cart_row["products_id"], $supplier_groups_id, $list_id);
					$group_max_qty -= (int)$offset_amount;
					
					$this_supplier_max_quantity = (int)$group_max_qty / $total_active_supplier;
					
					$this_supplier_max_quantity = tep_round_up_to($this_supplier_max_quantity, $list_qty_round_up);
				} else {
					$this_supplier_max_quantity = 0;	// no active supplier means no one can submit
				}
				
				/*
				if ($this_supplier_max_quantity > 0) {
					$this_supplier_over_limit_max_quantity = ((int)$supplier_pricing_setting_array["sps_max_limit"] - ((int)$products_list_row['inventory_qty'] + (int)$products_list_row['supplier_pricing_max_quantity'])) / $total_active_supplier;
					$this_supplier_over_limit_max_quantity = tep_round_up_to($this_supplier_over_limit_max_quantity, 100);
				} else {
					$this_supplier_over_limit_max_quantity = 0;	// no active supplier means no one can submit
				}
				*/
				$this_supplier_over_limit_max_quantity = 0;
				
				$normal_unit_price = (double)$products_list_row['supplier_pricing_unit_price'];
				//$over_limit_unit_price = $normal_unit_price * ( (100 - (double)$supplier_pricing_setting_array["sps_over_limit_discount"]) / 100);
				$over_limit_unit_price = 0;
				
				$product_amount = tep_calculate_amount($supplier_cart_row["buyback_basket_quantity"], $this_supplier_max_quantity, $normal_unit_price, $this_supplier_over_limit_max_quantity, $supplier_pricing_setting_array["sps_over_limit_discount"]);
				
				$this->products[$index] = array('id' => $supplier_cart_row["products_id"],
												'qty' => $supplier_cart_row["buyback_basket_quantity"],
                                        		'name' => $products_list_row["products_name"],
                                        		'display_name' => $product_display_name,
                                        		'purchase_status' => $products_list_row["supplier_pricing_product_status"],
                                        		'min_qty' => (int)$supplier_pricing_setting_array["sps_min_quantity"],
                                        		'first_max' => $this_supplier_max_quantity,
                                        		'first_max_unit_price' => $normal_unit_price,
                                        		'second_max' => $this_supplier_over_limit_max_quantity,
                                        		'second_max_unit_price' => $over_limit_unit_price,
                                        		'amount' => $product_amount,
                                        		'restock_comment' => $products_list_row["restock_character"],
                                        		'comment' => $supplier_cart_row["buyback_basket_comment"]
                                        		);
				$index++;
			}
		}
		
		return $cart_arr;
	}
	
	function get_products_ordered($list_type=2, $admin_report=false, $show_price_info=true)
	{
		global $currencies, $list_id;
		
		if ($list_type == 1) {
			$main_cat_id_select_sql = "	SELECT products_purchases_lists_cat_id AS cat_id 
										FROM " . TABLE_PRODUCTS_PURCHASES_LISTS . " 
										WHERE products_purchases_lists_id = '" . tep_db_input($list_id) . "'";
			$main_cat_id_result_sql = tep_db_query($main_cat_id_select_sql);
			$main_cat_id_row = tep_db_fetch_array($main_cat_id_result_sql);
			
			$margin_cfg_array = tep_get_cfg_setting($main_cat_id_row['cat_id'], 'catalog', 'BUYBACK_PROFIT_MARGIN');
			
			$products_ordered = '<table border="0" cellspacing="2" cellpadding="2">'.
							  	'	<tr>'.
							  	'		<td style="background-color:#FFD073; color:#000099; font-size:11px">Product</td>';
			
			if ($admin_report) {
				$products_ordered .='	<td style="background-color:#FFD073; color:#000099; font-size:11px" align="center">Available Qty</td>'.
									'	<td style="background-color:#FFD073; color:#000099; font-size:11px" align="center">Actual Qty</td>'.
							  		'	<td style="background-color:#FFD073; color:#000099; font-size:11px" align="center">Unit Price (USD)</td>' .
							  		'	<td style="background-color:#FFD073; color:#000099; font-size:11px" align="center">Unit Selling Price (USD)</td>'.
							  		'	<td style="background-color:#FFD073; color:#000099; font-size:11px" align="center">Profit Margin</td>';
			}
			$products_ordered .= '		<td style="background-color:#FFD073; color:#000099; font-size:11px">First List Selling Quantity</td>';
			if ($show_price_info) {
				$products_ordered .= '	<td style="background-color:#FFD073; color:#000099; font-size:11px" align="center">Amount</td>';
			}
			$products_ordered .= '	</tr>';
			for ($i=0; $i < count($this->products); $i++) {
				if ($admin_report) {
					$product_info_select_sql = "SELECT products_quantity, products_actual_quantity FROM " . TABLE_PRODUCTS . " WHERE products_id = '" . tep_db_input($this->products[$i]['id']) . "'";
					$product_info_result_sql = tep_db_query($product_info_select_sql);
					$product_info_row = tep_db_fetch_array($product_info_result_sql);
					
					$profit_margin = $markup_margin = $unit_selling_price = '--';
					$cell_span_style = '';
					
					$largest_package_select_sql = "	SELECT pb.subproduct_qty, p.products_price, COUNT(pb2.bundle_id) AS total_subproduct 
													FROM " . TABLE_PRODUCTS_BUNDLES . " AS pb 
													INNER JOIN " . TABLE_PRODUCTS_BUNDLES . " AS pb2 
														ON (pb.subproduct_id='" . tep_db_input($this->products[$i]['id']) . "' AND pb.bundle_id=pb2.bundle_id) 
													INNER JOIN " . TABLE_PRODUCTS . " AS p 
														ON pb.bundle_id = p.products_id 
													WHERE p.products_status=1 
													AND ( p.products_bundle = 'yes' OR p.products_bundle_dynamic = 'yes' )
													GROUP BY pb2.bundle_id
													HAVING total_subproduct =1 
													ORDER BY pb.subproduct_qty DESC LIMIT 1";
					$largest_package_result_sql = tep_db_query($largest_package_select_sql);
					if ($largest_package_row = tep_db_fetch_array($largest_package_result_sql)) {
						if ($largest_package_row['subproduct_qty'] > 0) {
							$unit_selling_price = $largest_package_row['products_price'] / $largest_package_row['subproduct_qty'];
							$unit_selling_price = number_format($unit_selling_price, DISPLAY_PRICE_DECIMAL, '.', '');
							
							if ($this->products[$i]['first_list']['first_max_unit_price'] > 0) {
								$buyback_price = number_format($this->products[$i]['first_list']['first_max_unit_price'], DISPLAY_PRICE_DECIMAL, '.', '');
								
								$markup_margin = ( ($unit_selling_price - $buyback_price) / $buyback_price ) * 100 ;
								$markup_margin = sprintf('%.3f', $markup_margin);
								
								$profit_margin = ( ($unit_selling_price - $buyback_price) / $unit_selling_price ) * 100 ;
								$profit_margin = sprintf('%.3f', $profit_margin);
								
								if ($profit_margin < (double)$margin_cfg_array['BUYBACK_PROFIT_MARGIN']) {
									$cell_span_style = ' style="color:#ff0000;" ';
								}
								
								$markup_margin = $markup_margin . '%';
								$profit_margin = $profit_margin . '%';
							}
						}
					}
				}
				
				$suggested_quantity = (int)$this->products[$i]['first_list']['products_quantity'];
				
			    $products_ordered .='	<tr>'.
							  		'		<td style="background-color:#DFDFDF; color:#000; font-size:11px"><span '.$cell_span_style.'>'.$this->products[$i]['first_list']['products_display_name'].'</span></td>';
				if ($admin_report) {
					$products_ordered .='	<td style="background-color:#DFDFDF; color:#000; font-size:11px" align="center"><span '.$cell_span_style.'>'.$product_info_row['products_quantity'].'</span></td>'.
										'	<td style="background-color:#DFDFDF; color:#000; font-size:11px" align="center"><span '.$cell_span_style.'>'.$product_info_row['products_actual_quantity'].'</span></td>'.
								  		'	<td style="background-color:#DFDFDF; color:#000; font-size:11px" align="center"><span '.$cell_span_style.'>'.number_format($this->products[$i]['first_list']['first_max_unit_price'], DISPLAY_PRICE_DECIMAL, '.', '').'</span></td>' .
								  		'	<td style="background-color:#DFDFDF; color:#000; font-size:11px" align="center"><span '.$cell_span_style.'>'.$unit_selling_price.'</span></td>' .
								  		'	<td style="background-color:#DFDFDF; color:#000; font-size:11px" align="center"><span '.$cell_span_style.'>'.$profit_margin.'</span></td>';
				}
				
				$products_ordered .=	'	<td style="background-color:#DFDFDF; color:#000; font-size:11px" align="center"><span '.$cell_span_style.'>'.$suggested_quantity.'</span></td>';
				if ($show_price_info) {
					$products_ordered .= '	<td style="background-color:#DFDFDF; color:#000; font-size:11px" align="right"><span '.$cell_span_style.'>'.$currencies->format($this->products[$i]['first_list']['products_provision_amount']).'</span></td>';
				}
				$products_ordered .= '	</tr>';
			}
			$products_ordered .= '</table>';
		} else if ($list_type == 2) {
			$products_ordered = '<table border="0" cellspacing="2" cellpadding="2">'.
							  	'	<tr>'.
							  	'		<td style="background-color:#FFD073; color:#000099; font-size:11px;">Product</td>'.
							  	'		<td style="background-color:#FFD073; color:#000099; font-size:11px;">First List Selling Quantity</td>'.
							  	'		<td style="background-color:#FFD073; color:#000099; font-size:11px;">Confirmation List Selling Quantity</td>'.
							  	'		<td style="background-color:#FFD073; color:#000099; font-size:11px;" align="center">Amount</td>'.
							  	'	</tr>';
			for ($i=0; $i < count($this->products); $i++) {
				if (is_array($this->products[$i]['confirm_list']) && count($this->products[$i]['confirm_list'])) {
					$suggested_quantity = $this->products[$i]['first_list']['products_quantity'];
					$selling_quantity = $this->products[$i]['confirm_list']['products_quantity'];
					$received_quantity = $this->products[$i]['confirm_list']['products_received_quantity'];
					$alert_style = $received_quantity < $selling_quantity ? '#ff0000' : '#000000';
					
				    $products_ordered .='	<tr>'.
								  		'		<td style="background-color:#DFDFDF; color:#000; font-size:11px;">'.$this->products[$i]['confirm_list']['products_display_name'].'</td>'.
								  		'		<td style="background-color:#DFDFDF; color:#000; font-size:11px;" align="center">'.$suggested_quantity.'</td>'.
								  		'		<td style="background-color:#DFDFDF; color:#000; font-size:11px;" align="center">'.$selling_quantity.'</td>'.
								  		'		<td style="background-color:#DFDFDF; color:#000; font-size:11px;" align="right">'.$currencies->format($this->products[$i]['confirm_list']['products_provision_amount']).'</td>'.
								  		'	</tr>';
				}
			}
			$products_ordered .= '</table>';
		}
		return $products_ordered;
	}
}
?>