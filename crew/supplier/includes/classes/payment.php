<?
/*
	$Id: payment.php,v 1.1 2006/03/10 02:21:38 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2006 SKC Ventrue
	
  	Released under the GNU General Public License
*/

/**************************************************************************
	NOTE: require(DIR_WS_CLASSES . 'order.php');
**************************************************************************/
class payment
{
	var $info, $payment_info, $orders, $supplier;
	var $payment_id;
	
    function payment($payment_id='')
    {
      	$this->info = array();
      	$this->orders = array();
      	$this->supplier = array();
		$this->payment_info = array();
		
		if (tep_not_null($payment_id)) {
			$this->payment_id = $payment_id;
      		$this->query($payment_id);
      	}
    }
	
    function query($payment_id)
    {
    	$payment_info_select_sql = "SELECT * FROM " . TABLE_SUPPLIER_PAYMENTS . " WHERE supplier_payments_id = '" . tep_db_input($payment_id) . "'";
		$payment_info_result_sql = tep_db_query($payment_info_select_sql);
      	$payment_info_row = tep_db_fetch_array($payment_info_result_sql);
		
		$supplier_personal_select_sql = "	SELECT s.supplier_code, s.supplier_payment_paypal, s.supplier_payment_bank_name, s.supplier_payment_bank_swift_code, s.supplier_payment_bank_address, s.supplier_payment_bank_telephone, s.supplier_payment_bank_account_name, s.supplier_payment_bank_account_number, sg.supplier_groups_name 
											FROM " . TABLE_SUPPLIER . " AS s 
											LEFT JOIN " . TABLE_SUPPLIER_GROUPS . " AS sg 
												ON s.supplier_groups_id = sg.supplier_groups_id 
											WHERE s.supplier_id = '" . $payment_info_row['suppliers_id'] . "'";
		$supplier_personal_result_sql = tep_db_query($supplier_personal_select_sql);
		$supplier_personal_row = tep_db_fetch_array($supplier_personal_result_sql);
		
      	$this->info = array('currency' => $payment_info_row['currency'],
                          	'currency_value' => $payment_info_row['currency_value'],
                          	'payments_amount' => $payment_info_row['supplier_payments_amount'],
                          	'payments_tax' => $payment_info_row['supplier_payments_tax'],
                          	'payments_total' => $payment_info_row['supplier_payments_total'],
                          	'payments_date' => $payment_info_row['supplier_payments_date'],
                          	'last_modified' => $payment_info_row['supplier_payments_last_modified'],
                          	'payments_status' => $payment_info_row['supplier_payments_status']
                          	);
		
      	$this->supplier = array('id' => $payment_info_row['suppliers_id'],
      							'firstname' => $payment_info_row['suppliers_firstname'],
                              	'lastname' => $payment_info_row['suppliers_lastname'],
                              	'street_address' => $payment_info_row['suppliers_street_address'],
                              	'suburb' => $payment_info_row['suppliers_suburb'],
                              	'city' => $payment_info_row['suppliers_city'],
                              	'postcode' => $payment_info_row['suppliers_postcode'],
                              	'state' => $payment_info_row['suppliers_state'],
                              	'country' => $payment_info_row['suppliers_country'],
                              	'format_id' => 1,
                              	'email_address' => $payment_info_row['suppliers_email_address'],
                              	'supplier_group' => $supplier_personal_row['supplier_groups_name'],
                              	'code' => $supplier_personal_row['supplier_code']);
		
		$this->payment_info = array('paypal_email' => $supplier_personal_row['supplier_payment_paypal'],
                              		'bank_name' => $supplier_personal_row['supplier_payment_bank_name'],
	                              	'bank_swift_code' => $supplier_personal_row['supplier_payment_bank_swift_code'],
	                              	'bank_address' => $supplier_personal_row['supplier_payment_bank_address'],
	                              	'bank_telephone' => $supplier_personal_row['supplier_payment_bank_telephone'],
	                              	'bank_account_name' => $supplier_personal_row['supplier_payment_bank_account_name'],
	                              	'bank_account_number' => $supplier_personal_row['supplier_payment_bank_account_number']);
        
		// Grab the payment's orders
      	$index = 0;
      	
      	$payment_orders_select_sql = "	SELECT spo.supplier_order_lists_id, spo.supplier_payments_orders_paid_amount, spo.supplier_payments_type, sol.currency, sol.currency_value 
    									FROM " . TABLE_SUPPLIER_PAYMENTS_ORDERS . " AS spo 
    									INNER JOIN " . TABLE_SUPPLIER_ORDER_LISTS . " AS sol 
    										ON spo.supplier_order_lists_id=sol.supplier_order_lists_id 
    									WHERE spo.supplier_payments_id = '" . tep_db_input($payment_id) . "'
    									ORDER BY spo.supplier_order_lists_id";
    	$payment_orders_result_sql = tep_db_query($payment_orders_select_sql);
    	
		while ($payment_orders_row = tep_db_fetch_array($payment_orders_result_sql)) {
			$order_payable_amount = supplier_order::get_order_total_payable_amount($payment_orders_row['supplier_order_lists_id']);
			
			$this->orders[$index] = array(	'id' => $payment_orders_row['supplier_order_lists_id'],
											'currency' => $payment_orders_row['currency'],
                          					'currency_value' => $payment_orders_row['currency_value'],
											'payable_amount' => $order_payable_amount,
											'paid_amount' => $payment_orders_row['supplier_payments_orders_paid_amount'],
											'paid_currency' => $payment_info_row['currency'],
                          					'paid_currency_value' => $payment_info_row['currency_value'],
                          					'payments_type' => $payment_orders_row['supplier_payments_type']
	                                       	);
			
        	$index++;
  		}
	}
	
	function get_order_paid_amount($order_id)
	{
		$paid_amount_select_sql = "	SELECT SUM(spo.supplier_payments_orders_paid_amount) AS total_paid 
									FROM " . TABLE_SUPPLIER_PAYMENTS_ORDERS . " AS spo 
									INNER JOIN " . TABLE_SUPPLIER_PAYMENTS . " AS sp 
										ON (spo.supplier_payments_id=sp.supplier_payments_id AND sp.supplier_payments_status IN (1, 2)) 
									WHERE spo.supplier_order_lists_id = '" . tep_db_input($order_id) . "' ";
		$paid_amount_result_sql = tep_db_query($paid_amount_select_sql);
		$paid_amount_row = tep_db_fetch_array($paid_amount_result_sql);
		
		return $paid_amount_row["total_paid"];
	}
	
	function get_order_paid_history($order_id, $all=true)
	{
		$payment_history_array = array();
		$order_payments_select_sql = "	SELECT spo.supplier_payments_id, spo.supplier_payments_orders_paid_amount, spo.supplier_payments_type, sp.currency, sp.currency_value 
										FROM " . TABLE_SUPPLIER_PAYMENTS_ORDERS . " AS spo 
										INNER JOIN " . TABLE_SUPPLIER_PAYMENTS . " AS sp 
											ON (spo.supplier_payments_id=sp.supplier_payments_id) 
										WHERE spo.supplier_order_lists_id = '" . tep_db_input($order_id) . "' " . (!$all ? "AND sp.supplier_payments_status IN (1, 2) " : '') . " 
										ORDER BY sp.supplier_payments_date";
		$order_payments_result_sql = tep_db_query($order_payments_select_sql);
		
		while ($order_payments_row = tep_db_fetch_array($order_payments_result_sql)) {
			$payment_history_array[] = array(	'payment_id' => $order_payments_row['supplier_payments_id'],
												'paid_amount' => $order_payments_row['supplier_payments_orders_paid_amount'],
												'paid_currency' => $order_payments_row['currency'],
												'paid_currency_value' => $order_payments_row['currency_value'],
												'payment_type' => $order_payments_row['supplier_payments_type']
												);
		}
		
		return $payment_history_array;
	}
}
?>