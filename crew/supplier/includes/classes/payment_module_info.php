<?php

class payment_module_info {
    var $payment_accounts, $user_id, $user_role, $max_entry;
	var $info, $beneficiary, $payment_info;
	
	// class constructor
    function payment_module_info($user_id, $user_role) {
      	$this->payment_accounts = array();
		
		$this->_get_current_payment_accounts($user_id, $user_role);
		
		$this->user_id = $user_id;
		$this->user_role = $user_role;
		
		if ($this->user_role == 'supplier') {
			$this->max_entry = MAX_SUPPLIER_PAYMENT_BOOK_ENTRIES;
		} else {
			$this->max_entry = -1;	// Unlimited
		}
	}
	
	function _get_current_payment_accounts($user_id, $user_role) {
		$payment_acc_select_sql = "	SELECT pab.*, pm.payment_methods_send_mode_name 
									FROM " . TABLE_STORE_PAYMENT_ACCOUNT_BOOK . " AS pab 
									INNER JOIN " . TABLE_PAYMENT_METHODS . " AS pm 
										ON pab.payment_methods_id=pm.payment_methods_id 
									WHERE pab.user_id = '" . tep_db_input($user_id) . "'
										AND pab.user_role = '" . tep_db_input($user_role) . "' 
										AND pm.payment_methods_send_status = '1'
										AND pm.payment_methods_send_status_mode = '1'
									ORDER BY pab.payment_methods_alias";
		$payment_acc_result_sql = tep_db_query($payment_acc_select_sql);
		
		while ($payment_acc_row = tep_db_fetch_array($payment_acc_result_sql)) {
			$this->payment_accounts[$payment_acc_row['store_payment_account_book_id']] = array(	'pm_id' => $payment_acc_row['payment_methods_id'],
																								'pm_alias' => $payment_acc_row['payment_methods_alias'],
																								'pm_name' => $payment_acc_row['payment_methods_send_mode_name']);
			
			$payment_acc_field_select_sql = "	SELECT pabd.payment_methods_fields_id, pabd.payment_methods_fields_value 
												FROM " . TABLE_STORE_PAYMENT_ACCOUNT_BOOK_DETAILS . " AS pabd 
												INNER JOIN " . TABLE_PAYMENT_METHODS_FIELDS . " AS pmf 
													ON pabd.payment_methods_fields_id=pmf.payment_methods_fields_id 
												WHERE pabd.store_payment_account_book_id = '" . tep_db_input($payment_acc_row['store_payment_account_book_id']) . "' 
												ORDER BY pmf.payment_methods_fields_sort_order";
			$payment_acc_field_result_sql = tep_db_query($payment_acc_field_select_sql);
			
			while ($payment_acc_field_row = tep_db_fetch_array($payment_acc_field_result_sql)) {
				$this->payment_accounts[$payment_acc_row['store_payment_account_book_id']]['field'][] = $payment_acc_field_row;
			}
		}
	}
	
	function _get_all_active_send_payment_methods($include_option_title=false) {
		$pm_array = array();
		if ($include_option_title)	$pm_array[] = array('id' => '', 'text' => PULL_DOWN_DEFAULT);
		
		$payment_method_select_sql = "	SELECT payment_methods_id, payment_methods_send_mode_name
										FROM " . TABLE_PAYMENT_METHODS . "
										WHERE payment_methods_send_status = 1
											AND payment_methods_send_status_mode = 1 
											AND payment_methods_parent_id <> '0' 
										ORDER BY payment_methods_sort_order";
		$payment_method_result_sql = tep_db_query($payment_method_select_sql);
		while ($payment_method_row = tep_db_fetch_array($payment_method_result_sql)) {
			$pm_array[] = array('id' => $payment_method_row['payment_methods_id'], 'text' => $payment_method_row['payment_methods_send_mode_name']);
		}
		
		return $pm_array;
	}
	
	function _get_existing_payment_account_selection($include_option_title=false) {
		$pm_array = array();
		
		if ($include_option_title)	$pm_array[] = array('id' => '', 'text' => PULL_DOWN_DEFAULT);
		
		if (isset($this->payment_accounts) && count($this->payment_accounts)) {
			foreach ($this->payment_accounts as $pm_book_id => $pm_book_info) {
				$pm_array[] = array('id' => $pm_book_id, 'text' => $pm_book_info['pm_alias']);
			}
		}
		
		return $pm_array;
	}
	
	function _get_user_particulars() {
		if ($this->user_role == 'supplier') {
			$user_info_select_sql = "	SELECT supplier_firstname AS fname, supplier_lastname AS lname, supplier_email_address AS email, supplier_telephone AS phone, supplier_disable_withdrawal as disable_withdrawal
										FROM " . TABLE_SUPPLIER . " 
										WHERE supplier_id = '" . tep_db_input($this->user_id) . "'";
		} else {
			$user_info_select_sql = "	SELECT cust.customers_firstname AS fname, cust.customers_lastname AS lname, cust.customers_email_address AS email, cust.customers_telephone AS phone, cust.customers_mobile AS mobile, c.countries_international_dialing_code AS dialing_code, customers_disable_withdrawal as disable_withdrawal 
										FROM " . TABLE_CUSTOMERS . " AS cust 
										LEFT JOIN " . TABLE_COUNTRIES . " AS c 
											ON (cust.customers_country_dialing_code_id = c.countries_id) 
										WHERE cust.customers_id = '" . tep_db_input($this->user_id) . "'";
		}
		
		$user_info_result_sql = tep_db_query($user_info_select_sql);
		$user_info_row = tep_db_fetch_array($user_info_result_sql);
		
		return $user_info_row;
	}
	
	function list_payment_account_book($filename) {
		$payment_list_content = '';
		
		if (count($this->payment_accounts)) {
			$payment_list_content = '<table border="0" width="100%" cellspacing="0" cellpadding="2">
										<tr><td class="reportBoxHeading">'.TABLE_HEADING_PM_PAYMENT_ACCOUNT.'</td><td width="10%" class="reportBoxHeading">'.TABLE_HEADING_ACTION.'</td></tr>';
			
			$css_cnt = 0;
			foreach ($this->payment_accounts as $book_id => $book_info) {
				$row_style = ( ($css_cnt++) % 2 ) ? 'reportListingEven' : 'reportListingOdd';
				
				$safe_pm_title = htmlspecialchars(addslashes($book_info['pm_alias']), ENT_QUOTES);
				
				$payment_list_content .=' <tr class="'.$row_style.'" id="'.$book_id.'" onMouseOver="rowOverEffect(this, \'reportListingRowOver\')" onMouseOut="rowOutEffect(this, \''.$row_style.'\')" onClick="rowClicked(this, \''.$row_style.'\')">
											<td class="reportRecords">'.$book_info['pm_name'].' ['.$book_info['pm_alias'].']'.'</td>
											<td class="reportRecords">
												<a href="'.tep_href_link($filename, 'action=edit_pm&book_id='.$book_id) . '">'.tep_image(DIR_WS_ICONS."edit.gif", LINK_PM_EDIT_PM_ACCOUNT, "", "", 'align="top"').'</a>&nbsp;
												<a href="javascript:void(confirm_delete(\''.$safe_pm_title.'\', \''.TABLE_HEADING_PM_PAYMENT_ACCOUNT.'\', \''.tep_href_link($filename, 'action=delete_pm&subaction=confirm_delete_pm&book_id='.$book_id).'\'))">'.tep_image(DIR_WS_ICONS."delete.gif", LINK_PM_DELETE_PM_ACCOUNT, "", "", 'align="top"').'</a>
											</td>
										  </tr>';
			}
			$payment_list_content .= '</table>';
		} else {
			$payment_list_content = TEXT_PM_NO_EXISTING_PAYMENT_ACCOUNT;
		}
		
		$payment_list_html = '<table width="600" border="0" align="center" cellpadding="1" cellspacing="0">
           						<tr bgcolor="#000000">
              						<td>
              							<table border="0" width="600" cellspacing="0" cellpadding="0">
                  							<tr bgcolor="#ffffff">
                    							<td align="left" valign="top">
                    								<table border="0" cellpadding="2" cellspacing="1" width="100%">
                      									<tbody>
                        									<tr class="reportRecords"><td>'.$payment_list_content.'</td></tr>
				                      					</tbody>
                    								</table>
                    							</td>
                  							</tr>
              							</table>
              							</form>
              						</td>
            					</tr>
            					<tr>
						        	<td>'.tep_draw_separator('pixel_trans.gif', '100%', '10').'</td>
						      	</tr>';
		
		if ($this->max_entry < 0 || count($this->payment_accounts) < $this->max_entry) {
            $payment_list_html .= '<tr>
                  					<td>
	          							<table class="buttonBox2" border="0" cellpadding="2" cellspacing="1" width="100%">
	              						<tbody>
	                						<tr class="buttonBoxContents2">
			                                  	<td align="right">'.
				                                  	tep_draw_form('add_pm_form', $filename, tep_get_all_get_params(array('subaction', 'action')) . 'action=add_pm', 'POST', '').
				                                  	tep_submit_button(BUTTON_ADD_PM, ALT_BUTTON_ADD_PM, '', 'inputButton').'
				                                  	</form>
			                                  	</td>
	                						</tr>
	              						</tbody>
	          							</table>
	          						</td>
	        					</tr>';
     	}
     	
     	$payment_list_html .= '</table>';
     	
		return $payment_list_html;
	}
	
	function add_payment_account($filename, $book_id='') {
		$this_action = isset($book_id) && tep_not_null($book_id) ? 'edit' : 'add';
		
		$payment_list_html = '';
		
		if ($this->max_entry > 0) {
			if ($this_action == 'add' && count($this->payment_accounts) >= $this->max_entry) {
				$payment_list_html = '<table width="600" border="0" align="center" cellpadding="1" cellspacing="0" class="smallText" valign="middle">
	           							<tr>
	              							<td class="messageStackError">'.ERROR_PM_ACCOUNT_BOOK_FULL.'</td>
	              						</tr>
	              						<tr>
		                  					<td>
			          							<table class="buttonBox2" border="0" cellpadding="2" cellspacing="1" width="100%">
			              						<tbody>
			                						<tr class="buttonBoxContents2">
			                							<td align="left">'.
						                                  	tep_button(BUTTON_BACK, ALT_BUTTON_BACK, tep_href_link($filename, tep_get_all_get_params(array('action', 'subaction'))), '', 'inputButton').'
					                                  	</td>
			                						</tr>
			              						</tbody>
			          							</table>
			          						</td>
			        					</tr>
	              					  </table>';
			}
		}
		
		if (!tep_not_null($payment_list_html)) {
			$payment_list_html = '<table width="600" border="0" align="center" cellpadding="1" cellspacing="0" class="smallText" valign="middle">
	           						<tr bgcolor="#000000">
	              						<td>'.
	              							tep_draw_form('add_pm_form', $filename, tep_get_all_get_params(array('subaction', 'action')) . ($this_action == 'edit' ? 'action=edit_pm&subaction=update_pm' : 'action=add_pm&subaction=insert_pm'), 'POST', '').
	              							($this_action == 'edit' ? tep_draw_hidden_field("book_id", $_REQUEST["book_id"]) : '') . '
	              							<table border="0" width="600" cellspacing="0" cellpadding="2">
	                  							<tr bgcolor="#ffffff">
	                    							<td align="left" valign="top" bgcolor="#F0F0FF">
	                    								<table class="inputBox" border="0" cellpadding="2" cellspacing="1" width="100%">
	                    									<tr class="inputBoxContents">
	                    										<td width="35%" class="inputLabel">'.ENTRY_FORM_PM_SELECT_PM.'</td>
	                    										<td class="inputField">'.tep_draw_pull_down_menu('pm_id', $this->_get_all_active_send_payment_methods(true), $this->payment_accounts[$book_id]['pm_id'], 'id="pm_id" onChange="getPMOptions(this, \'pm_field_div\', \''.$book_id.'\');"').'</td>
	                    									</tr>
	                    									<tr class="inputBoxContents">
	                    										<td class="inputLabel">'.ENTRY_FORM_PM_ALIAS.'</td>
	                    										<td class="inputField">'.tep_draw_input_field('pm_alias', $this->payment_accounts[$book_id]['pm_alias'], 'size="30"').'</td>
	                    									</tr>
	                    									<tr>
	                    										<td class="inputLabel" colspan="2">
	                    											<div id="pm_field_div" style="padding-top:0.2em; display: block;"></div>
	                    										</td>
	                    									</tr>
							            					<tr>
							                  					<td colspan="2">
								          							<table class="buttonBox2" border="0" cellpadding="2" cellspacing="1" width="100%">
								              						<tbody>
								                						<tr class="buttonBoxContents2">
								                							<td align="left">'.
											                                  	tep_button(BUTTON_BACK, ALT_BUTTON_BACK, tep_href_link($filename, tep_get_all_get_params(array('action', 'subaction'))), '', 'inputButton').'
										                                  	</td>
										                                  	<td align="right">'.
											                                  	tep_submit_button(BUTTON_SUBMIT, ALT_BUTTON_SUBMIT, '', 'inputButton').'
										                                  	</td>
								                						</tr>
								              						</tbody>
								          							</table>
								          						</td>
								        					</tr>
	                    								</table>
	                    							</td>
	                  							</tr>
	              							</table>
	              							</form>
	              						</td>
	            					</tr>
	            					<script language="javascript">getPMOptions(document.getElementById(\'pm_id\'), \'pm_field_div\', \''.$book_id.'\');</script>
	          					</table>';
	  	}
	  	
		return $payment_list_html;
	}
	
	function insert_payment_account($input_array, &$messageStack) {
		if ($this->max_entry > 0 && count($this->payment_accounts) >= $this->max_entry) {	// Exceed maximum allowed payment account entry
			$messageStack->add_session(ERROR_PM_ACCOUNT_BOOK_FULL, 'error');
			return true;
		}
		
		$error = false;
		
		$account_book_data_array = array();
		$account_book_details_data_array = array();
		
		$payment_method_select_sql = "	SELECT payment_methods_id 
										FROM " . TABLE_PAYMENT_METHODS . " 
										WHERE payment_methods_id = '".tep_db_input($input_array['pm_id'])."' 
											AND payment_methods_send_status = 1 ";
		$payment_method_result_sql = tep_db_query($payment_method_select_sql);
		
		if ($payment_method_row = tep_db_fetch_array($payment_method_result_sql)) {
			if (!tep_not_null($input_array['pm_alias'])) {
				$error = true;
				$messageStack->add(ERROR_EMPTY_PM_ALIAS, 'error');
			}
			
			$account_book_data_array = array(	'user_id' => $this->user_id,
												'user_role' => $this->user_role,
						                        'payment_methods_id' => $input_array['pm_id'],
						                      	'payment_methods_alias' => tep_db_prepare_input($input_array['pm_alias'])
						                  	);
		    
			$payment_fields_select_sql = "	SELECT payment_methods_fields_id, payment_methods_fields_title, payment_methods_fields_required 
											FROM " . TABLE_PAYMENT_METHODS_FIELDS . " 
											WHERE payment_methods_id = '" . tep_db_input($input_array['pm_id']) . "' 
												AND payment_methods_mode = 'SEND' 
												AND payment_methods_fields_status = 1";
			$payment_fields_result_sql = tep_db_query($payment_fields_select_sql);
			
			while ($payment_fields_row = tep_db_fetch_array($payment_fields_result_sql)) {
				if (!tep_not_null($input_array['pm_field'][$payment_fields_row['payment_methods_fields_id']]) && $payment_fields_row['payment_methods_fields_required'] == '1') {
					$error = true;
					$messageStack->add(sprintf(ERROR_PM_FIELD_INFO_REQUIRED, $payment_fields_row['payment_methods_fields_title']), 'error');
				}
				
				$account_book_details_data_array[] = array(	'payment_methods_fields_id' => $payment_fields_row['payment_methods_fields_id'],
															'payment_methods_fields_value' => tep_db_prepare_input($input_array['pm_field'][$payment_fields_row['payment_methods_fields_id']])
									                  	);
			}
		} else {
			$error = true;
			$messageStack->add(ERROR_INVALID_PM_SELECTION, 'error');
		}
		
		if (!$error) {
			if (count($account_book_data_array)) {
				tep_db_perform(TABLE_STORE_PAYMENT_ACCOUNT_BOOK, $account_book_data_array);
				$payment_account_book_id = tep_db_insert_id();
				
				for ($acc_book_detail_cnt=0; $acc_book_detail_cnt < count($account_book_details_data_array); $acc_book_detail_cnt++) {
					$account_book_details_data_array[$acc_book_detail_cnt]['store_payment_account_book_id'] = $payment_account_book_id;
					tep_db_perform(TABLE_STORE_PAYMENT_ACCOUNT_BOOK_DETAILS, $account_book_details_data_array[$acc_book_detail_cnt]);
				}
			}
			return true;
		} else {
			return false;
		}
	}
	
	function update_payment_account($input_array, &$messageStack) {
		$error = false;
		
		$account_book_data_array = array();
		$account_book_details_data_array = array();
		
		if (isset($input_array['book_id'])) {
			$payment_method_select_sql = "	SELECT payment_methods_id 
											FROM " . TABLE_PAYMENT_METHODS . " 
											WHERE payment_methods_id = '".tep_db_input($input_array['pm_id'])."' 
												AND payment_methods_send_status = 1 ";
			$payment_method_result_sql = tep_db_query($payment_method_select_sql);
			
			if ($payment_method_row = tep_db_fetch_array($payment_method_result_sql)) {
				if (!tep_not_null($input_array['pm_alias'])) {
					$error = true;
					$messageStack->add(ERROR_EMPTY_PM_ALIAS, 'error');
				}
				
				$account_book_data_array = array(	'payment_methods_id' => $input_array['pm_id'],
							                      	'payment_methods_alias' => tep_db_prepare_input($input_array['pm_alias'])
							                  	);
			    
				$payment_fields_select_sql = "	SELECT payment_methods_fields_id, payment_methods_fields_title, payment_methods_fields_required 
												FROM " . TABLE_PAYMENT_METHODS_FIELDS . " 
												WHERE payment_methods_id = '" . tep_db_input($input_array['pm_id']) . "' 
													AND payment_methods_mode = 'SEND' 
													AND payment_methods_fields_status = 1";
				$payment_fields_result_sql = tep_db_query($payment_fields_select_sql);
				
				while ($payment_fields_row = tep_db_fetch_array($payment_fields_result_sql)) {
					if (!tep_not_null($input_array['pm_field'][$payment_fields_row['payment_methods_fields_id']]) && $payment_fields_row['payment_methods_fields_required'] == '1') {
						$error = true;
						$messageStack->add(sprintf(ERROR_PM_FIELD_INFO_REQUIRED, $payment_fields_row['payment_methods_fields_title']), 'error');
					}
					
					$account_book_details_data_array[] = array(	'payment_methods_fields_id' => $payment_fields_row['payment_methods_fields_id'],
																'payment_methods_fields_value' => tep_db_prepare_input($input_array['pm_field'][$payment_fields_row['payment_methods_fields_id']])
										                  	);
				}
			} else {
				$error = true;
				$messageStack->add(ERROR_INVALID_PM_SELECTION, 'error');
			}
		} else {
			$error = true;
			$messageStack->add(ERROR_INVALID_PM_BOOK_SELECTION, 'error');
		}
		
		if (!$error) {
			if (count($account_book_data_array)) {
				$update_res = tep_db_perform(TABLE_STORE_PAYMENT_ACCOUNT_BOOK, $account_book_data_array, 'update', "store_payment_account_book_id = '" . tep_db_input($input_array['book_id']) . "'");
				
				if ($update_res) {
					$account_book_field_delete_sql = "DELETE FROM " . TABLE_STORE_PAYMENT_ACCOUNT_BOOK_DETAILS . " WHERE store_payment_account_book_id = '" . tep_db_input($input_array['book_id']) . "'";
					tep_db_query($account_book_field_delete_sql);
					
					for ($acc_book_detail_cnt=0; $acc_book_detail_cnt < count($account_book_details_data_array); $acc_book_detail_cnt++) {
						$account_book_details_data_array[$acc_book_detail_cnt]['store_payment_account_book_id'] = $input_array['book_id'];
						tep_db_perform(TABLE_STORE_PAYMENT_ACCOUNT_BOOK_DETAILS, $account_book_details_data_array[$acc_book_detail_cnt]);
					}
				}
			}
			return true;
		} else {
			return false;
		}
	}
	
	function delete_payment_account($book_id, &$messageStack) {
		$payment_account_delete_sql = "	DELETE " . TABLE_STORE_PAYMENT_ACCOUNT_BOOK . ", " . TABLE_STORE_PAYMENT_ACCOUNT_BOOK_DETAILS . " 
										FROM " . TABLE_STORE_PAYMENT_ACCOUNT_BOOK . "
										INNER JOIN " . TABLE_STORE_PAYMENT_ACCOUNT_BOOK_DETAILS . "
											ON ( ".TABLE_STORE_PAYMENT_ACCOUNT_BOOK.".store_payment_account_book_id=".TABLE_STORE_PAYMENT_ACCOUNT_BOOK_DETAILS.".store_payment_account_book_id)
										WHERE ".TABLE_STORE_PAYMENT_ACCOUNT_BOOK.".store_payment_account_book_id = '" . tep_db_input($book_id) . "' 
											AND ".TABLE_STORE_PAYMENT_ACCOUNT_BOOK.".user_id = '" . tep_db_input($this->user_id) . "' 
											AND ".TABLE_STORE_PAYMENT_ACCOUNT_BOOK.".user_role = '" . tep_db_input($this->user_role) . "'";
		tep_db_query($payment_account_delete_sql);
		
		if (tep_db_affected_rows()) {
			$messageStack->add_session(SUCCESS_PM_DELETE_PM_ACCOUNT, 'success');
		}
	}
	
	function show_withdraw_form($filename, $credit_currency) {
		global $currencies;
		
		$user_credit_info_select_sql = "SELECT store_account_balance_amount, store_account_reserve_amount 
										FROM " . TABLE_STORE_ACCOUNT_BALANCE . "
										WHERE user_id = '".tep_db_input($this->user_id)."' 
											AND user_role = '".tep_db_input($this->user_role)."' 
											AND store_account_balance_currency = '".tep_db_input($credit_currency)."'";
		$user_credit_info_result_sql = tep_db_query($user_credit_info_select_sql);

		$user_info_array = $this->_get_user_particulars();
		
		if ($user_info_array['disable_withdrawal'] == '0' && $user_credit_info_row = tep_db_fetch_array($user_credit_info_result_sql)) {
			$general_reserve_amount = $this->_get_general_reserve_amount();
			
			$converted_general_reserve_amount = $currencies->apply_currency_exchange($general_reserve_amount, $credit_currency, '', 'sell');	// Hold the most as possible
			$other_eligible_balance = $this->_get_other_currency_acc_eligible_balance($credit_currency);
			
			if ($other_eligible_balance >= $converted_general_reserve_amount) {
				$converted_general_reserve_amount = 0;
			} else {
				$converted_general_reserve_amount = $converted_general_reserve_amount - $other_eligible_balance;
			}
			
			$total_reserve_amount = $converted_general_reserve_amount + (double)$user_credit_info_row['store_account_reserve_amount'];
			
			$available_balance = $user_credit_info_row['store_account_balance_amount'] - $total_reserve_amount;
			
			if ($available_balance <  0)	$available_balance = 0;
			
			$rounded_down_available_balance = $currencies->do_raw_conversion($available_balance, false, $credit_currency, '', 'DOWN');
			
			$withdraw_form_html = '<table width="700" border="0" align="center" cellpadding="1" cellspacing="0" class="smallText" valign="middle">
	           						<tr bgcolor="#000000">
	              						<td>'.
	              							tep_draw_form('withdraw_form', $filename, tep_get_all_get_params(array('subaction', 'action')) . 'action=confirm_withdraw', 'POST', '') . '
	              							<table border="0" width="700" cellspacing="0" cellpadding="2">
	                  							<tr bgcolor="#ffffff">
	                    							<td align="left" valign="top" bgcolor="#F0F0FF">
	                    								<table class="inputBox" border="0" cellpadding="2" cellspacing="1" width="100%">
	                    									<tr class="inputBoxContents">
	                    										<td width="30%" class="inputLabel">'.ENTRY_WITHDRAW_CURRENT_BALANCE.'</td>
	                    										<td class="inputField">'.
	                    											$currencies->format($user_credit_info_row['store_account_balance_amount'], false, $credit_currency).'&nbsp;&nbsp;'.
	                    											sprintf(LINK_WITHDRAW_ACCOUNT_STATEMENT, tep_href_link(FILENAME_SUPPLIER_ACCOUNT_STATEMENT, 'action=show_report&start_date='.(date('Y-m-d', mktime(0, 0, 0, date("m")  , date("d")-7, date("Y")))), 'SSL')).'
	                    										</td>
	                    									</tr>
	                    									<tr class="inputBoxContents">
	                    										<td class="inputLabel">'.ENTRY_WITHDRAW_RESERVE_AMOUNT.'</td>
	                    										<td class="inputField">'.$currencies->format($total_reserve_amount, false, $credit_currency).'</td>
	                    									</tr>
	                    									<tr class="inputBoxContents">
	                    										<td class="inputLabel">'.ENTRY_WITHDRAW_AVAILABLE_BALANCE.'</td>
	                    										<td class="inputField">'.
	                    											$currencies->format_round_down($available_balance, false, $credit_currency) . '&nbsp;&nbsp;'.
	                    											($rounded_down_available_balance > 0 ? sprintf(LINK_WITHDRAW_ALL_AMOUNT, "javascript:;", "document.withdraw_form.withdraw_amount.value='".$rounded_down_available_balance."'; getPMInfo(document.getElementById('pb_id'), '".$_REQUEST["cur"]."', 'withdraw_amount', 'pm_field_div');") : '').'
	                    										</td>
	                    									</tr>
	                    									<tr class="inputBoxContents">
	                    										<td class="inputLabel">'.ENTRY_WITHDRAW_AMOUNT.'</td>
	                    										<td class="inputField">'.
	                    											$currencies->currencies[$credit_currency]['symbol_left'] . tep_draw_input_field('withdraw_amount', '', 'id="withdraw_amount" onkeyup="if (trim_str(this.value) != \'\' && !currencyValidation(trim_str(this.value))) { this.value = \'\'; }" onChange="getPMInfo(document.getElementById(\'pb_id\'), \''.$_REQUEST["cur"].'\', \'withdraw_amount\', \'pm_field_div\');"').$currencies->currencies[$credit_currency]['symbol_right'] . '&nbsp;&nbsp;'.
	                    											sprintf(LINK_WITHDRAW_REFRESH_DATA, "javascript:;", "getPMInfo(document.getElementById('pb_id'), '".$_REQUEST["cur"]."', 'withdraw_amount', 'pm_field_div');") . '
	                    										</td>
	                    									</tr>
	                    									<tr class="inputBoxContents">
	                    										<td class="inputLabel">'.ENTRY_WITHDRAW_PAYMENT_ACCOUNT.'</td>
	                    										<td class="inputField">'.
	                    											tep_draw_pull_down_menu('pb_id', $this->_get_existing_payment_account_selection(true), '', 'id="pb_id" onChange="getPMInfo(this, \''.$_REQUEST["cur"].'\', \'withdraw_amount\', \'pm_field_div\');"').'&nbsp;&nbsp;'.
	                    											sprintf(LINK_PM_ACC_BOOK, tep_href_link(FILENAME_SUPPLIER_PAYMENT_BOOK, '', 'SSL')).'
	                    										</td>
	                    									</tr>
	                    									<tr>
	                    										<td class="inputLabel">&nbsp;</td>
	                    										<td class="inputLabel">
	                    											<div id="pm_field_div" style="padding-top:0.2em; display: block;"></div>
	                    										</td>
	                    									</tr>
							            					<tr>
							                  					<td colspan="2">
								          							<table class="buttonBox2" border="0" cellpadding="2" cellspacing="1" width="100%">
								              						<tbody>
								                						<tr class="buttonBoxContents2">
								                							<td align="right">'.
											                                  	tep_submit_button(BUTTON_CONTINUE, ALT_BUTTON_CONTINUE, ' onClick="this.disabled=true; this.form.submit();"', 'inputButton').'
										                                  	</td>
								                						</tr>
								              						</tbody>
								          							</table>
								          						</td>
								        					</tr>
	                    								</table>
	                    							</td>
	                  							</tr>
	              							</table>
	              							</form>
	              						</td>
	            					</tr>
	            					<script language="javascript">getPMInfo(document.getElementById(\'pb_id\'), \''.$_REQUEST["cur"].'\', \'withdraw_amount\', \'pm_field_div\');</script>
	          					</table>';
		} else {
			if ($user_info_array['disable_withdrawal'] == '1') {
				$withdraw_form_html = '	<table width="600" border="0" align="center" cellpadding="1" cellspacing="0" class="smallText" valign="middle">
										<tr>
											<td class="messageStackError">'.nl2br(ERROR_WITHDRAW_DISABLE_WITHDRAWAL).'</td>
										</tr>
										</table>';
			} else {
				$withdraw_form_html = '	<table width="600" border="0" align="center" cellpadding="1" cellspacing="0" class="smallText" valign="middle">
										<tr>
											<td class="messageStackError">'.nl2br(ERROR_WITHDRAW_NO_CURRENCY_ACCOUNT_INFO).'</td>
										</tr>
										</table>';
			}
		}
		
		return $withdraw_form_html;
	}
	
	function confirm_withdraw_form($filename, $credit_currency, $input_array, $action, &$messageStack) {
		global $currencies;
		
		$withdraw_form_html = $pm_sec_html = '';
		$withdraw_failed = false;
		$display_trans_fee_array = array();
		$total_fees_amount = 0;
		$withdraw_fees_waived = false;
		$action_res_array = array('code' => '', 'html' => '');
		
		$user_credit_info_select_sql = "SELECT store_account_balance_amount, store_account_reserve_amount 
										FROM " . TABLE_STORE_ACCOUNT_BALANCE . "
										WHERE user_id = '".tep_db_input($this->user_id)."' 
											AND user_role = '".tep_db_input($this->user_role)."' 
											AND store_account_balance_currency = '".tep_db_input($credit_currency)."'";
		$user_credit_info_result_sql = tep_db_query($user_credit_info_select_sql);
		
		if ($user_credit_info_row = tep_db_fetch_array($user_credit_info_result_sql)) {
			$general_reserve_amount = $this->_get_general_reserve_amount();
			$converted_general_reserve_amount = $currencies->apply_currency_exchange($general_reserve_amount, $credit_currency, '', 'sell');	// Hold the most as possible
			
			$other_eligible_balance = $this->_get_other_currency_acc_eligible_balance($credit_currency);
			
			if ($other_eligible_balance >= $converted_general_reserve_amount) {
				$converted_general_reserve_amount = 0;
			} else {
				$converted_general_reserve_amount = $converted_general_reserve_amount - $other_eligible_balance;
			}
			
			$total_reserve_amount = $converted_general_reserve_amount + (double)$user_credit_info_row['store_account_reserve_amount'];
			
			$available_balance = $user_credit_info_row['store_account_balance_amount'] - $total_reserve_amount;
			
			if ($available_balance <  0)	$available_balance = 0;
			
			$payment_book_id = isset($input_array['pb_id']) ? trim($input_array['pb_id']) : '';
			
			// check if any incomplete mandatory data
			$mandatory_data_completed_flag = true;
			$check_incomplete_field_select_sql = "	SELECT pmf.payment_methods_fields_id, spabd.payment_methods_fields_value 
													FROM " . TABLE_PAYMENT_METHODS_FIELDS . " AS pmf 
													INNER JOIN " . TABLE_STORE_PAYMENT_ACCOUNT_BOOK . " AS spab
														ON pmf.payment_methods_id = spab.payment_methods_id
													LEFT JOIN " . TABLE_STORE_PAYMENT_ACCOUNT_BOOK_DETAILS . " spabd 
														ON spabd.store_payment_account_book_id = spab.store_payment_account_book_id
															AND spabd.payment_methods_fields_id = pmf.payment_methods_fields_id
													WHERE spab.store_payment_account_book_id = '".tep_db_input($payment_book_id)."'
														AND pmf.payment_methods_fields_required = '1'
														AND pmf.payment_methods_fields_status = '1'";
			$check_incomplete_field_result_sql = tep_db_query($check_incomplete_field_select_sql);
			while ($check_incomplete_field_row = tep_db_fetch_array($check_incomplete_field_result_sql)) {
				if (!tep_not_null($check_incomplete_field_row['payment_methods_fields_value'])) {
					$mandatory_data_completed_flag = false;
				}
			}
			
			if (!$mandatory_data_completed_flag) {
				tep_redirect(tep_href_link(FILENAME_SUPPLIER_PAYMENT_BOOK, tep_get_all_get_params(array('action', 'subaction', 'book_id')). 'book_id='.$payment_book_id.'&action=edit_pm', 'SSL'));
			}
			
			if (isset($input_array['withdraw_amount'])) {
				$withdraw_amount = preg_replace('/[^\d\.]/', '', $input_array['withdraw_amount']);
			} else {
				$withdraw_amount = 0;
			}
			//$withdraw_amount = isset($input_array['withdraw_amount']) ? (double)$input_array['withdraw_amount'] : 0;
			
			if ($available_balance > 0 && $withdraw_amount <= $available_balance) {
				if ($withdraw_amount > 0) {
					$payment_book_select_sql = "	SELECT pm.payment_methods_id, pm.payment_methods_send_mode_name, pm.payment_methods_send_currency, spab.payment_methods_alias, pm.payment_methods_types_id, pm.payment_methods_parent_id  
													FROM " . TABLE_STORE_PAYMENT_ACCOUNT_BOOK . " AS spab 
													INNER JOIN " . TABLE_PAYMENT_METHODS . " AS pm 
														ON spab.payment_methods_id=pm.payment_methods_id 
													WHERE spab.store_payment_account_book_id = '".tep_db_input($payment_book_id)."' 
														AND spab.user_id = '".tep_db_input($this->user_id)."' 
														AND spab.user_role = '".tep_db_input($this->user_role)."' 
														AND pm.payment_methods_send_status = 1 
														AND pm.payment_methods_send_status_mode = 1 ";
					$payment_book_result_sql = tep_db_query($payment_book_select_sql);
					
					if ($payment_book_row = tep_db_fetch_array($payment_book_result_sql)) {
						if ($payment_book_row['payment_methods_send_currency'] == '0') {
							$pmt_row = tep_db_fetch_array(tep_db_query("SELECT payment_methods_types_system_define FROM " . TABLE_PAYMENT_METHODS_TYPES . " WHERE payment_methods_types_id='".$payment_book_row['payment_methods_types_id']."'"));
							if ($pmt_row['payment_methods_types_system_define'] == '1') {
								$payment_currency = DEFAULT_CURRENCY;
								$payment_book_row['payment_methods_send_currency'] = array_search($payment_currency, $currencies->internal_currencies);
							} else {
								$withdraw_failed = true;
								$messageStack->add_session(ERROR_INVALID_PM_SELECTION, 'error');
							}
						} else {
							$payment_currency = $currencies->get_code_by_id($payment_book_row['payment_methods_send_currency']);
						}
						
						$trans_fee_array = $this->_calculate_fees($payment_book_row['payment_methods_id'], $credit_currency, $withdraw_amount);
						
						if ($trans_fee_array['cost'] > 0) {
							$display_trans_fee_array[] = $currencies->format($trans_fee_array['cost']);
							$total_fees_amount += $currencies->apply_currency_exchange($trans_fee_array['cost'], $credit_currency, '', 'sell');
						}
						
						if ($trans_fee_array['percent'] > 0) {
							$display_trans_fee_array[] = $trans_fee_array['percent'] . '%';
							//$total_fees_amount += ($withdraw_amount * $trans_fee_array['percent']) / 100;
							$total_fees_amount += (($withdraw_amount / (1 + ($trans_fee_array['percent'] / 100))) * ($trans_fee_array['percent'] / 100));
						}
						
						$payment_fee_select_sql = "	SELECT * 
													FROM " . TABLE_PAYMENT_FEES . "	
													WHERE payment_methods_id = '".tep_db_input($payment_book_row['payment_methods_id'])."' 
														AND payment_methods_mode = 'SEND'";
						$payment_fee_result_sql = tep_db_query($payment_fee_select_sql);
						if (!tep_db_num_rows($payment_fee_result_sql) && $payment_book_row['payment_methods_parent_id']>0) {
							$payment_fee_select_sql = "	SELECT * 
														FROM " . TABLE_PAYMENT_FEES . "	
														WHERE payment_methods_id = '".tep_db_input($payment_book_row['payment_methods_parent_id'])."' 
															AND payment_methods_mode = 'SEND'";
							$payment_fee_result_sql = tep_db_query($payment_fee_select_sql);
						}
						$payment_fee_row = tep_db_fetch_array($payment_fee_result_sql);
						
						$converted_min_withdraw  = $currencies->apply_currency_exchange($payment_fee_row['payment_fees_min'], $credit_currency, '', 'sell');
						if ($converted_min_withdraw > 0 && $withdraw_amount < $converted_min_withdraw) {	// Below minimum withdraw amount
							if ($payment_fee_row['payment_fees_below_min'] == 'beneficiary') {
								if (count($display_trans_fee_array)) {
									$withdraw_fees = implode(' + ', $display_trans_fee_array);
								} else {
									$withdraw_fees = TEXT_WITHDRAW_FEES_FREE;
									$withdraw_fees_waived = true;
									//$withdraw_fees = 'DO_NOT_SHOW_FEES';
								}
							} else if ($payment_fee_row['payment_fees_below_min'] == 'payer') {
								$withdraw_fees = TEXT_WITHDRAW_FEES_FREE;
								$withdraw_fees_waived = true;
								//$withdraw_fees = 'DO_NOT_SHOW_FEES';
							} else {
								$withdraw_failed = true;
								$messageStack->add_session(sprintf(WARNING_WITHDRAW_NOT_ALLOWED_PAYMENT_METHOD, $payment_book_row['payment_methods_alias']), 'error');
							}
						} else {
							if ($payment_fee_row['payment_fees_bear_by'] == 'beneficiary') {
								if (count($display_trans_fee_array)) {
									$withdraw_fees = implode(' + ', $display_trans_fee_array);
								} else {
									$withdraw_fees = TEXT_WITHDRAW_FEES_FREE;
									$withdraw_fees_waived = true;
									//$withdraw_fees = 'DO_NOT_SHOW_FEES';
								}
							} else {
								$withdraw_fees = TEXT_WITHDRAW_FEES_FREE;
								$withdraw_fees_waived = true;
								//$withdraw_fees = 'DO_NOT_SHOW_FEES';
							}
						}
						
						if ($withdraw_fees_waived) { // Reset total fees to 0 if not applicable
							$total_fees_amount = 0;
						}
						
						$converted_max_withdraw = $currencies->apply_currency_exchange($payment_fee_row['payment_fees_max'], $credit_currency, '', 'sell');
						
						if ($converted_max_withdraw > 0 && $withdraw_amount > $converted_max_withdraw) {	// Over limit
							$withdraw_failed = true;
							$messageStack->add_session(sprintf(WARNING_WITHDRAW_NOT_ALLOWED_PAYMENT_METHOD, $payment_book_row['payment_methods_alias']), 'error');
						}
						
						// Fees is higher than withdraw amount
						if ($withdraw_amount <= $total_fees_amount) {
							$withdraw_failed = true;
							$messageStack->add_session(WARNING_WITHDRAW_FEES_HIGHER_THAN_WITHDRAW_AMOUNT, 'error');
						}
						
						if ($withdraw_failed) {
							$action_res_array['code'] = '-1';
						} else {
							if ($action == 'submit') {	// Actual insert
								$user_info_array = $this->_get_user_particulars();
								
								$after_fees_amount = $withdraw_amount - $total_fees_amount;
								
								/**********************************************************************
									Capture the withdraw calculation and store it for reference
								**********************************************************************/
								$fees_calculation_text .= 	ENTRY_WITHDRAW_CURRENT_BALANCE . ' ' . $currencies->format($user_credit_info_row['store_account_balance_amount'], false, $credit_currency) . "\n" .
															ENTRY_WITHDRAW_RESERVE_AMOUNT . ' ' . $currencies->format($total_reserve_amount, false, $credit_currency) . "\n" . 
															ENTRY_WITHDRAW_AVAILABLE_BALANCE . ' ' . $currencies->format_round_down($available_balance, false, $credit_currency) . "\n" .
						                    				ENTRY_WITHDRAW_AMOUNT . ' ' . $currencies->currencies[$credit_currency]['symbol_left'] . $withdraw_amount .$currencies->currencies[$credit_currency]['symbol_right'] . "\n" .
						                    				ENTRY_WITHDRAW_PAYMENT_ACCOUNT . ' ' . $payment_book_row['payment_methods_alias'] . "\n\n";
								
								$fees_calculation_text .= 	ENTRY_WITHDRAW_MIN_AMT . ' ' . ($payment_fee_row['payment_fees_min'] > 0 ? $currencies->format($payment_fee_row['payment_fees_min']) : TEXT_NO_WITHDRAW_LIMIT) . "\n" .
											    		 	ENTRY_WITHDRAW_MAX_AMT . ' ' . ($payment_fee_row['payment_fees_max'] > 0 ? $currencies->format($payment_fee_row['payment_fees_max']) : TEXT_NO_WITHDRAW_LIMIT) . "\n";
								
								if ($withdraw_fees != 'DO_NOT_SHOW_FEES') {
									$fees_calculation_text .= ENTRY_WITHDRAW_FEES . ' ' . $withdraw_fees . "\n\n";
								}
								
								$fees_calculation_text .= ENTRY_WITHDRAW_FINAL_AMOUNT . ' ' . $currencies->format($after_fees_amount, true, $payment_currency) . "\n\n";
								/**********************************************************************
									End of capture the withdraw calculation and store it for reference
								**********************************************************************/
								
								$payment_fields_select_sql = "	SELECT pmf.payment_methods_fields_id, pmf.payment_methods_fields_title, pmf.payment_methods_fields_type, pmf.payment_methods_fields_option, pmf.payment_methods_fields_sort_order, spabd.payment_methods_fields_value 
																FROM " . TABLE_PAYMENT_METHODS_FIELDS . " AS pmf 
																LEFT JOIN " . TABLE_STORE_PAYMENT_ACCOUNT_BOOK_DETAILS . " spabd 
																	ON (pmf.payment_methods_fields_id=spabd.payment_methods_fields_id AND spabd.store_payment_account_book_id = '".tep_db_input($payment_book_id)."')
																WHERE pmf.payment_methods_id = '" . tep_db_input($payment_book_row['payment_methods_id']) . "' 
																	AND pmf.payment_methods_mode = 'SEND' 
																	AND pmf.payment_methods_fields_status = 1 
																ORDER BY pmf.payment_methods_fields_sort_order";
								$payment_fields_result_sql = tep_db_query($payment_fields_select_sql);
								
								$payment_sql_data_array = array(	'user_id' => tep_db_prepare_input($this->user_id),
					    		           							'user_role' => tep_db_prepare_input($this->user_role),
					    	 	          							'user_firstname' => $user_info_array['fname'],
					    	 	          							'user_lastname' => $user_info_array['lname'],
					    	 	          							'user_email_address' => $user_info_array['email'],
					    	 	          							'user_country_international_dialing_code' => $user_info_array['dialing_code'],
					    	 	          							'user_telephone' => $user_info_array['phone'],
					    	 	          							'user_mobile' => $user_info_array['mobile'],
					    	 	          							'store_payments_date' => 'now()',
					    	 	          							'store_payments_status' => '1',
					    	  	         							'store_payments_request_currency' => tep_db_prepare_input($credit_currency),
					    	  	         							'store_payments_request_amount' => (double)$withdraw_amount,
					    	  	         							'store_payments_fees' => tep_db_prepare_input($total_fees_amount),
					    	  	         							'store_payments_after_fees_amount' => $after_fees_amount,
					    	  	         							'store_payments_paid_currency' => tep_db_prepare_input($currencies->get_code_by_id($payment_book_row['payment_methods_send_currency'])),
					    	  	         							'store_payments_methods_id' => $payment_book_row['payment_methods_id'],
					    	  	         							'store_payments_methods_name' => tep_db_prepare_input($payment_book_row['payment_methods_send_mode_name']),
					    	  	         							'store_payment_account_book_id' => $payment_book_id,
					    	  	         							'user_payment_methods_alias' => tep_db_prepare_input($payment_book_row['payment_methods_alias'])
																);
					        	
					        	if ($credit_currency != $currencies->get_code_by_id($payment_book_row['payment_methods_send_currency'])) {
									$payment_sql_data_array['store_payments_paid_currency_value'] = $currencies->advance_currency_conversion_rate($credit_currency, $currencies->get_code_by_id($payment_book_row['payment_methods_send_currency']), 'sell');
								}
					        	
					        	tep_db_perform(TABLE_STORE_PAYMENTS, $payment_sql_data_array);
								$insert_payment_id = tep_db_insert_id();
								
								if ($insert_payment_id > 0) {
									// Update live credit balance
									$update_info = array(	array(	'field_name'=> 'store_account_balance_amount',
																	'operator'=> '-', 
																	'value'=> $withdraw_amount)
														);
									
									$new_store_acc_balance = $this->_set_store_acc_balance($credit_currency, $update_info);
									
									// Insert account statement history
									$account_balance_history_data_array = array('user_id' => tep_db_prepare_input($this->user_id),
																				'user_role' => tep_db_prepare_input($this->user_role),
														                        'store_account_history_date' => 'now()',
														                        'store_account_history_currency' => tep_db_prepare_input($credit_currency),
														                        'store_account_history_debit_amount' => (double)$withdraw_amount,
														                        'store_account_history_credit_amount' => 'NULL',
														                        'store_account_history_after_balance' => (double)$new_store_acc_balance,
														                        'store_account_history_trans_type' => 'P',
														                        'store_account_history_trans_id' => $insert_payment_id,
														                        'store_account_history_added_by' => $user_info_array['email'],
														                        'store_account_history_added_by_role' => tep_db_prepare_input($this->user_role)
														                       );
									tep_db_perform(TABLE_STORE_ACCOUNT_HISTORY, $account_balance_history_data_array);
									
									// Insert payment details
									while ($payment_fields_row = tep_db_fetch_array($payment_fields_result_sql)) {
										$pm_fields_value_str = ($payment_fields_row['payment_methods_fields_type']=='6' || $payment_fields_row['payment_methods_fields_type']=='7') ? str_replace(':~:', "\r\n", $payment_fields_row['payment_methods_fields_option']) : $payment_fields_row['payment_methods_fields_value'];
										$payment_details_sql_data_array = array('store_payments_id' => $insert_payment_id,
							    		           								'payment_methods_fields_id' => $payment_fields_row['payment_methods_fields_id'],
								    	 	          							'payment_methods_fields_title' => $payment_fields_row['payment_methods_fields_title'],
								    	 	          							'payment_methods_fields_value' => $pm_fields_value_str,
								    	 	          							'payment_methods_fields_sort_order' => $payment_fields_row['payment_methods_fields_sort_order']
							        	    	       							);
										tep_db_perform(TABLE_STORE_PAYMENTS_DETAILS, $payment_details_sql_data_array);
										
										$fees_calculation_text .= '<b>'.$payment_fields_row['payment_methods_fields_title'] . '</b> ' . $pm_fields_value_str . "\n";
									}
									
									// Insert payment history
									$payment_history_sql_data_array = array('store_payments_id' => $insert_payment_id,
						    		           								'store_payments_status' => '1',
							    	 	          							'date_added' => 'now()',
							    	 	          							'payee_notified' => '1',
							    	 	          							'comments' => tep_db_prepare_input(sprintf(COMMENT_WITHDRAW_FUNDS_REQUESTED, $payment_book_row['payment_methods_alias'], $payment_book_row['payment_methods_send_mode_name'])) . "\n\n" . tep_db_prepare_input($fees_calculation_text),
							    	 	          							'changed_by' => $user_info_array['email'],
							    	 	          							'changed_by_role' => tep_db_prepare_input($this->user_role)
						        	    	       							);
									tep_db_perform(TABLE_STORE_PAYMENTS_HISTORY, $payment_history_sql_data_array);
									
									// Update withdraw fees calculation history
									$payment_update_array = array('store_payments_fees_calculation' => tep_db_prepare_input($fees_calculation_text));
									tep_db_perform(TABLE_STORE_PAYMENTS, $payment_update_array, 'update', 'store_payments_id="'.tep_db_input($insert_payment_id).'"');
									
									// E-mail the beneficiary
									$this->send_payment_status_email($insert_payment_id);
									
									$messageStack->add_session(SUCCESS_WITHDRAW_SUCCESS, 'success');
									$action_res_array['code'] = '1';
								} else {
									// Withdraw failed
									$messageStack->add_session(ERROR_WITHDRAW_NOT_SUCCESS, 'error');
									$action_res_array['code'] = '-1';
								}
							} else {
								$pm_sec_html .= '<tr class="inputBoxContents">
													<td width="35%" class="inputLabel" valign="top">'.ENTRY_WITHDRAW_MIN_AMT.'</td>
													<td class="inputField" valign="top">'.($payment_fee_row['payment_fees_min'] > 0 ? $currencies->format($payment_fee_row['payment_fees_min']) : TEXT_NO_WITHDRAW_LIMIT).'</td>
												 </tr>
												 <tr class="inputBoxContents">
													<td class="inputLabel" valign="top">'.ENTRY_WITHDRAW_MAX_AMT.'</td>
													<td class="inputField" valign="top">'.($payment_fee_row['payment_fees_max'] > 0 ? $currencies->format($payment_fee_row['payment_fees_max']) : TEXT_NO_WITHDRAW_LIMIT).'</td>
												 </tr>';
								
								if ($withdraw_fees != 'DO_NOT_SHOW_FEES') {
									$pm_sec_html .= '<tr class="inputBoxContents">
														<td class="inputLabel" valign="top">'.ENTRY_WITHDRAW_FEES.'</td>
														<td class="inputField" valign="top"><b>'.$withdraw_fees.'</b></td>
													 </tr>';
								}
								
								$pm_sec_html .= '<tr class="inputBoxContents">
													<td class="inputLabel" valign="top"><b>'.ENTRY_WITHDRAW_FINAL_AMOUNT.'</b></td>
													<td class="inputField" valign="top"><b>'.$currencies->format($withdraw_amount-$total_fees_amount, true, $payment_currency).'</b></td>
												 </tr>
												 <tr><td colspan="2" height="10px"></td></tr>';
								
								$payment_fields_select_sql = "	SELECT pmf.*, spabd.payment_methods_fields_value 
																FROM " . TABLE_PAYMENT_METHODS_FIELDS . " AS pmf 
																LEFT JOIN " . TABLE_STORE_PAYMENT_ACCOUNT_BOOK_DETAILS . " spabd 
																	ON (pmf.payment_methods_fields_id=spabd.payment_methods_fields_id AND spabd.store_payment_account_book_id = '".tep_db_input($payment_book_id)."')
																WHERE pmf.payment_methods_id = '" . tep_db_input($payment_book_row['payment_methods_id']) . "' 
																	AND pmf.payment_methods_mode = 'SEND' 
																	AND pmf.payment_methods_fields_status = 1 
																ORDER BY pmf.payment_methods_fields_sort_order";
								$payment_fields_result_sql = tep_db_query($payment_fields_select_sql);
								
								while ($payment_fields_row = tep_db_fetch_array($payment_fields_result_sql)) {
									$pm_fields_value_str = ($payment_fields_row['payment_methods_fields_type']=='6' || $payment_fields_row['payment_methods_fields_type']=='7') ? str_replace(':~:', "\r\n", $payment_fields_row['payment_methods_fields_option']) : $payment_fields_row['payment_methods_fields_value'];
									$pm_sec_html .= '<tr class="inputBoxContents">
														<td class="inputLabel" valign="top" nowrap><i>'.$payment_fields_row['payment_methods_fields_title'].'</i></td>
														<td class="inputField" valign="top">'.nl2br($pm_fields_value_str).'</td>
												 	 </tr>';
								}
								
								$withdraw_form_html = '<table width="700" border="0" align="center" cellpadding="1" cellspacing="0" class="smallText" valign="middle">
						           						<tr bgcolor="#000000">
						              						<td>'.
						              							tep_draw_form('withdraw_form', $filename, tep_get_all_get_params(array('subaction', 'action')) . 'action=submit_withdraw', 'POST', '') . 
						              							tep_draw_hidden_field("pb_id", $payment_book_id) . 
						              							tep_draw_hidden_field("withdraw_amount", $withdraw_amount) . '
						              							<table border="0" width="700" cellspacing="0" cellpadding="2">
						                  							<tr bgcolor="#ffffff">
						                    							<td align="left" valign="top" bgcolor="#F0F0FF">
						                    								<table class="inputBox" border="0" cellpadding="2" cellspacing="1" width="100%">
						                    									<tr class="inputBoxContents">
						                    										<td width="30%" class="inputLabel">'.ENTRY_WITHDRAW_CURRENT_BALANCE.'</td>
						                    										<td class="inputField">'.
						                    											$currencies->format($user_credit_info_row['store_account_balance_amount'], false, $credit_currency).'&nbsp;&nbsp;' . '
						                    										</td>
						                    									</tr>
						                    									<tr class="inputBoxContents">
						                    										<td class="inputLabel">'.ENTRY_WITHDRAW_RESERVE_AMOUNT.'</td>
						                    										<td class="inputField">'.$currencies->format($total_reserve_amount, false, $credit_currency).'</td>
						                    									</tr>
						                    									<tr class="inputBoxContents">
						                    										<td class="inputLabel">'.ENTRY_WITHDRAW_AVAILABLE_BALANCE.'</td>
						                    										<td class="inputField">'.$currencies->format_round_down($available_balance, false, $credit_currency).'</td>
						                    									</tr>
						                    									<tr class="inputBoxContents">
						                    										<td class="inputLabel">'.ENTRY_WITHDRAW_AMOUNT.'</td>
						                    										<td class="inputField"><b>'.$currencies->currencies[$credit_currency]['symbol_left'].$withdraw_amount .$currencies->currencies[$credit_currency]['symbol_right'].'</b></td>
						                    									</tr>
						                    									<tr class="inputBoxContents">
						                    										<td class="inputLabel">'.ENTRY_WITHDRAW_PAYMENT_ACCOUNT.'</td>
						                    										<td class="inputField">'.$payment_book_row['payment_methods_alias'].'</td>
						                    									</tr>
						                    									<tr>
						                    										<td class="inputLabel">&nbsp;</td>
						                    										<td class="inputLabel">
						                    											<table border="0" width="100%" cellspacing="0" cellpadding="2">'.$pm_sec_html.'</table>
						                    										</td>
						                    									</tr>
												            					<tr>
												                  					<td colspan="2">
													          							<table class="buttonBox2" border="0" cellpadding="2" cellspacing="1" width="100%">
													              						<tbody>
													                						<tr class="buttonBoxContents2">
													                							<td align="left">'.
																                                  	tep_button(BUTTON_BACK, ALT_BUTTON_BACK, tep_href_link($filename, tep_get_all_get_params(array('action', 'subaction'))), '', 'inputButton').'
															                                  	</td>
															                                  	<td align="right">'.
																                                  	tep_submit_button(BUTTON_CONFIRM, ALT_BUTTON_CONFIRM, ' onClick="this.disabled=true; this.form.submit();"', 'inputButton').'
															                                  	</td>
													                						</tr>
													              						</tbody>
													          							</table>
													          						</td>
													        					</tr>
						                    								</table>
						                    							</td>
						                  							</tr>
						              							</table>
						              							</form>
						              						</td>
						            					</tr>
						          					</table>';
						      	$action_res_array['html'] = $withdraw_form_html;
							}
						}
					} else {
						$messageStack->add_session(ERROR_WITHDRAW_NO_PAYMENT_ACCOUNT_BOOK, 'error');
						$action_res_array['code'] = '-1';
					}
				} else {
					$messageStack->add_session(ERROR_WITHDRAW_NO_WITHDRAW_AMOUNT, 'error');
					$action_res_array['code'] = '-1';
				}
			} else {
				$messageStack->add_session(ERROR_WITHDRAW_NO_AVAILABLE_FUNDS, 'error');
				$action_res_array['code'] = '-1';
			}
		} else {
			$messageStack->add_session(ERROR_WITHDRAW_NO_CURRENCY_ACCOUNT_INFO, 'error');
			$action_res_array['code'] = '-1';
		}
		
		return $action_res_array;
	}
	
	function send_payment_status_email($payment_id, $comment='') {
		global $currencies;
		
		$this->_get_payment_info($payment_id);
		
		$user_particulars_array = $this->_get_user_particulars();
		
		// Send email to payee
		$email = 	sprintf(EMAIL_PAYMENT_TEXT_TITLE, $currencies->format($this->payment_info["request_amount"], false, $this->payment_info["request_currency"]), $this->payment_info['payment_methods_name']) . "\n\n" .
					EMAIL_PAYMENT_TEXT_SUMMARY_TITLE . "\n" . EMAIL_SEPARATOR . "\n" .
				 	sprintf(EMAIL_PAYMENT_TEXT_PAYMENT_NUMBER, $payment_id) . "\n" .
				 	sprintf(EMAIL_PAYMENT_TEXT_PAYMENT_DATE, tep_date_long($this->info["payments_date"])) . "\n\n" .
				 	sprintf(EMAIL_PAYMENT_TEXT_STATUS_UPDATE, $this->info['status_name']) . "\n\n" . 
				 	//sprintf(EMAIL_PAYMENT_TEXT_COMMENTS, tep_db_prepare_input($comment)) . "\n\n" .
				 	EMAIL_TEXT_CLOSING . "\n\n" .
				 	EMAIL_FOOTER;
		
		$email_greeting = tep_get_email_greeting($user_particulars_array['fname'], $user_particulars_array['lname'], $user_particulars_array['gender']);
		
		$email = $email_greeting . $email;
		@tep_mail($user_particulars_array['fname'].' '.$user_particulars_array['lname'], $this->beneficiary['email_address'], sprintf(EMAIL_PAYMENT_PAYMENT_UPDATE_SUBJECT, $payment_id), $email, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
	}
	
	function _get_general_reserve_amount() {
		switch($this->user_role) {
			case "supplier":
				$reserve_amt_select_sql = "	SELECT supplier_reserve_amount 
											FROM " . TABLE_SUPPLIER . " 
											WHERE supplier_id = '".tep_db_input($this->user_id)."' ";
				$reserve_amt_result_sql = tep_db_query($reserve_amt_select_sql);
				$reserve_amt_row = tep_db_fetch_array($reserve_amt_result_sql);
				
				return $reserve_amt_row['supplier_reserve_amount'];
				
				break;
			default:
				return 0;
				
				break;
		}
	}
	
	function _get_other_currency_acc_eligible_balance($withdraw_currency) {
		global $currencies;
		
		$eligible_balance = 0;
		
		$other_balance_select_sql = "	SELECT store_account_balance_currency, store_account_balance_amount - store_account_reserve_amount AS balance 
										FROM " . TABLE_STORE_ACCOUNT_BALANCE . " 
										WHERE user_id = '" . tep_db_input($this->user_id) . "' 
											AND user_role = '" . tep_db_input($this->user_role) . "' 
											AND store_account_balance_currency <> '" . tep_db_input($withdraw_currency) . "'";
		$other_balance_result_sql = tep_db_query($other_balance_select_sql);
		
		while ($other_balance_row = tep_db_fetch_array($other_balance_result_sql)) {
			if ($other_balance_row['balance'] > 0) {
				$eligible_balance += $currencies->advanced_apply_currency_exchange($other_balance_row['balance'], $other_balance_row['store_account_balance_currency'], $withdraw_currency, 'sell');
			}
		}
		
		return $eligible_balance;
	}
	
	function _calculate_fees($payment_method_id, $withdraw_currency, $withdraw_amount) {
		global $currencies;
		
		$trans_fee_array = array('cost' => 0, 'percent' => 0);
		
		$payment_fee_select_sql = "	SELECT * 
									FROM " . TABLE_PAYMENT_FEES . "	
									WHERE payment_methods_id = '".tep_db_input($payment_method_id)."' 
										AND payment_methods_mode = 'SEND'";
		$payment_fee_result_sql = tep_db_query($payment_fee_select_sql);
		if (!tep_db_num_rows($payment_fee_result_sql)) {
			$payment_fee_select_sql = "	SELECT pf.* 
										FROM " . TABLE_PAYMENT_FEES . "	AS pf 
										INNER JOIN " . TABLE_PAYMENT_METHODS. " AS pm 
											ON pf.payment_methods_id = pm.payment_methods_parent_id
										WHERE pm.payment_methods_id = '".tep_db_input($payment_method_id)."' 
											AND pf.payment_methods_mode = 'SEND'
											AND pf.payment_methods_currency_code = 'USD'";
			$payment_fee_result_sql = tep_db_query($payment_fee_select_sql);
		}
		$payment_fee_row = tep_db_fetch_array($payment_fee_result_sql);
		
		if ($payment_fee_row['payment_fees_cost_value'] > 0) 	$trans_fee_array['cost'] = $payment_fee_row['payment_fees_cost_value'];
		
		if ($payment_fee_row['payment_fees_cost_percent'] > 0) {
			$percent_fees_not_in_range = false;
			
			$percent_fees = ($withdraw_amount * $payment_fee_row['payment_fees_cost_percent']) / 100;
			
			if ($payment_fee_row['payment_fees_cost_percent_min'] > 0) {
				$w_currency_min_fee = $currencies->apply_currency_exchange($payment_fee_row['payment_fees_cost_percent_min'], $withdraw_currency, '', 'sell');
				if ($percent_fees < $w_currency_min_fee) {
					$percent_fees = $payment_fee_row['payment_fees_cost_percent_min'];
					$percent_fees_not_in_range = true;
				}
			}
			
			if ($payment_fee_row['payment_fees_cost_percent_max'] > 0)	{
				$w_currency_max_fee = $currencies->apply_currency_exchange($payment_fee_row['payment_fees_cost_percent_max'], $withdraw_currency, '', 'sell');
				
				if ($percent_fees > $w_currency_max_fee) {
					$percent_fees = $payment_fee_row['payment_fees_cost_percent_max'];
					$percent_fees_not_in_range = true;
				}
			}
			
			if ($percent_fees_not_in_range) {
				$trans_fee_array['cost'] += $percent_fees;
			} else {
				$trans_fee_array['percent'] += $payment_fee_row['payment_fees_cost_percent'];
			}
		}
		
		return $trans_fee_array;
	}
	
	function _set_store_acc_balance($credit_currency, $update_info) {
		/*******************************************************************
			operator = +, -, and =
		*******************************************************************/
		$sql_update_array = array();
		
		// Generate the update sql
		for ($update_cnt=0; $update_cnt < count($update_info); $update_cnt++) {
			if ( ($update_info[$update_cnt]['field_name'] == 'store_account_balance_amount' || $update_info[$update_cnt]['field_name'] == 'store_account_reserve_amount') ) {
				$update_info[$update_cnt]['operator'] = trim($update_info[$update_cnt]['operator']);
				switch($update_info[$update_cnt]['operator']) {
					case '+':
						$sql_update_array[] = $update_info[$update_cnt]['field_name'] . ' = ' . $update_info[$update_cnt]['field_name'] . ' + ' . tep_db_input($update_info[$update_cnt]['value']);
						break;
					case '-':
						$sql_update_array[] = $update_info[$update_cnt]['field_name'] . ' = ' . $update_info[$update_cnt]['field_name'] . ' - ' . tep_db_input($update_info[$update_cnt]['value']);
						break;
					case '=':
						$sql_update_array[] = $update_info[$update_cnt]['field_name'] . ' = ' . tep_db_input($update_info[$update_cnt]['value']);
						break;
					default:
						break;
				}
			}
		}
		
		if (count($sql_update_array)) {
			$sql_update_array[] = ' store_account_last_modified = now() ';
			
			$update_sql_str = " SET " . implode(', ', $sql_update_array);
	    	
	    	/*************************************************************************
			 	Lock the TABLE_STORE_ACCOUNT_BALANCE 
			 	REMEMBER: Need to lock all the tables involved in this block.
			*************************************************************************/
	    	tep_db_query("LOCK TABLES " . TABLE_STORE_ACCOUNT_BALANCE . " WRITE;");
	    	
	    	$store_acc_balance_update_sql = "	UPDATE " . TABLE_STORE_ACCOUNT_BALANCE . 
	    										$update_sql_str . " 
												WHERE user_id = '" . tep_db_input($this->user_id) . "' 
													AND user_role = '" . tep_db_input($this->user_role) . "' 
													AND store_account_balance_currency = '" . tep_db_input($credit_currency) . "'";
			tep_db_query($store_acc_balance_update_sql);
			
			$new_balance_select_sql = "	SELECT store_account_balance_amount 
										FROM " . TABLE_STORE_ACCOUNT_BALANCE . " 
										WHERE user_id = '" . tep_db_input($this->user_id) . "' 
											AND user_role = '" . tep_db_input($this->user_role) . "' 
											AND store_account_balance_currency = '" . tep_db_input($credit_currency) . "'";
			$new_balance_result_sql = tep_db_query($new_balance_select_sql);
			$new_balance_row = tep_db_fetch_array($new_balance_result_sql);
			
			tep_db_query("UNLOCK TABLES;");
			/********************************************************************
			 	End of locking the TABLE_STORE_ACCOUNT_BALANCE table.
			********************************************************************/
			
			return $new_balance_row['store_account_balance_amount'];
		}
		
		return false;
	}
	
	function search_acc_statement($filename, $session_name) {
		$acc_statement_html = '';
		
	  	$show_options = array 	(	array ('id' => '10', "text" => "10"),
									array ('id' => '20', "text" => "20"),
									array ('id' => '50', "text" => "50"),
									array ('id' => 'ALL', "text" => TEXT_ALL_PAGES)
								);
		
		ob_start();
?>
	  	<table width="100%" border="0" align="center" cellpadding="1" cellspacing="0" class="smallText" valign="middle">
			<tr>
				<td>
					<table border="0" width="100%" cellspacing="0" cellpadding="0">
						<tr>
							<td width="20%">&nbsp;</td>
        					<td>
        						<?=tep_draw_form('payments_criteria', $filename, tep_get_all_get_params(array('action', 'cont')) . 'action=show_report', 'post', '')?>
        						<table border="0" cellspacing="2" cellpadding="0">
									<tr>
										<td class="main" ><?=ENTRY_ORDER_START_DATE?></td>
										<td class="main" valign="top" nowrap>
    										<script language="javascript"><!--
  												var date_start_date = new ctlSpiffyCalendarBox("date_start_date", "payments_criteria", "start_date", "btnDate_start_date", "", scBTNMODE_CUSTOMBLUE);
  												date_start_date.writeControl(); date_start_date.dateFormat="yyyy-MM-dd"; document.getElementById('start_date').value='<?=$_SESSION[$session_name]["start_date"]?>';
											//--></script>
    									</td>
    									<td class="main" width="10%">&nbsp;</td>
    									<td class="main" valign="top" nowrap><?=ENTRY_ORDER_END_DATE?></td>
    									<td class="main" >&nbsp;</td>
    									<td class="main"  valign="top" nowrap>
			    							<script language="javascript"><!--
			  									var date_end_date = new ctlSpiffyCalendarBox("date_end_date", "payments_criteria", "end_date", "btnDate_end_date", "", scBTNMODE_CUSTOMBLUE);
			  									date_end_date.writeControl(); date_end_date.dateFormat="yyyy-MM-dd"; document.getElementById('end_date').value='<?=$_SESSION[$session_name]["end_date"]?>';
											//--></script>
			    						</td>
									</tr>
									<tr>
	            						<td colspan="6"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
	          						</tr>
	          						<tr>
	          							<td class="main"><?=ENTRY_PAYMENT_ID?></td>
	          							<td class="main" ><?=tep_draw_input_field('payment_id', $_SESSION[$session_name]["payment_id"], ' id="payment_id" size="15" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) ) { this.value = \'\'; } else { resetControls(this); }" onBlur="if (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value)) ) { this.value = \'\'; } else { resetControls(this); }" onKeyPress="return noEnterKey(event)"')?></td>
			    						<td class="main" >&nbsp;</td>
			    						<td class="main"><?=ENTRY_ORDER_ID?></td>
			    						<td class="main" >&nbsp;</td>
			    						<td class="main" ><?=tep_draw_input_field('order_id', $_SESSION[$session_name]["order_id"], ' id="order_id" size="15" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) ) { this.value = \'\'; } else { resetControls(this); }" onBlur="if (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value)) ) { this.value = \'\'; } else { resetControls(this); }" onKeyPress="return noEnterKey(event)"')?></td>
									</tr>
									<tr>
	            						<td colspan="6"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
	          						</tr>
	          						<tr>
										<td class="main"><?=ENTRY_RECORDS_PER_PAGE?></td>
						    			<td class="main"><?=tep_draw_pull_down_menu("show_records", $show_options, tep_not_null($_SESSION[$session_name]["show_records"]) ? $_SESSION[$session_name]["show_records"] : '', '') . TEXT_LANG_RECORDS_PER_PAGE?></td>
						    			<td class="main" >&nbsp;</td>
			    						<td colspan="3">
		  									<?=tep_submit_button(BUTTON_SEARCH, ALT_BUTTON_SEARCH, 'onClick="return form_checking(this.form, \'do_search\');"', 'inputButton')?>
		  									<?=tep_button(BUTTON_RESET, ALT_BUTTON_RESET, tep_href_link($filename, 'action=reset_session'), '', 'inputButton')?>
		  								</td>
									</tr>
	        					</table>
	        					</form>
	        				</td>
	        			</tr>
	        		</table>
	        	</td>
	        </tr>
	    </table>
			<script language="javascript"><!--
				function form_checking(form_obj, action) {
				    //form_obj.submit();
					return true;
	    		}
	    		
				function resetControls(controlObj) {
					if (trim_str(controlObj.value) != '') {
						if (controlObj.id == 'payment_id') {
							document.payments_criteria.order_id.value = '';
						} else {
							document.payments_criteria.payment_id.value = '';
						}
						document.payments_criteria.start_date.value = '';
						document.payments_criteria.end_date.value = '';
						document.payments_criteria.show_records.selectedIndex = 0;
		    		} else {
		    			controlObj.value = '';
		    		}
				}
	    	//-->
			</script>
<?
		$acc_statement_html = ob_get_contents();
		ob_end_clean() ;
		
		return $acc_statement_html;
	}
	
	function show_acc_statement($filename, $session_name, $input_array, &$messageStack) {
		global $currencies, $languages_id;
		
		if (!$_REQUEST['cont']) {
			$_SESSION[$session_name]["start_date"] = $input_array["start_date"];
			$_SESSION[$session_name]["end_date"] = $input_array["end_date"];
			$_SESSION[$session_name]["payment_id"] = $input_array["payment_id"];
			$_SESSION[$session_name]["order_id"] = $input_array["order_id"];
			$_SESSION[$session_name]["show_records"] = $input_array["show_records"];
	  	}
	  	
	  	if (tep_not_null($_SESSION[$session_name]["start_date"])) {
			if (strpos($_SESSION[$session_name]["start_date"], ':') !== false) {
				$startDateObj = explode(' ', trim($_SESSION[$session_name]["start_date"]));
				list($yr, $mth, $day) = explode('-', $startDateObj[0]);
				list($hr, $min) = explode(':', $startDateObj[1]);
				$start_date_str = " ( sah.store_account_history_date >= '".date("Y-m-d H:i:s", mktime((int)$hr, (int)$min, 0, $mth, $day, $yr))."' )";
			} else {
				list($yr, $mth, $day) = explode('-', trim($_SESSION[$session_name]["start_date"]));
				$start_date_str = " ( DATE_FORMAT(sah.store_account_history_date, '%Y-%m-%d') >= DATE_FORMAT('".date("Y-m-d", mktime(0,0,0,$mth,$day,$yr))."','%Y-%m-%d') )";
			}
		} else {
			$start_date_str = " 1 ";
		}
		
		if (tep_not_null($_SESSION[$session_name]["end_date"])) {
			if (strpos($_SESSION[$session_name]["end_date"], ':') !== false) {
				$endDateObj = explode(' ', trim($_SESSION[$session_name]["end_date"]));
				list($yr, $mth, $day) = explode('-', $endDateObj[0]);
				list($hr, $min) = explode(':', $endDateObj[1]);
				$end_date_str = " ( sah.store_account_history_date <= '".date("Y-m-d H:i:s", mktime((int)$hr, (int)$min, 0, $mth, $day, $yr))."' )";
			} else {
				list($yr, $mth, $day) = explode('-', trim($_SESSION[$session_name]["end_date"]));
				$end_date_str = " ( DATE_FORMAT(sah.store_account_history_date, '%Y-%m-%d') <= DATE_FORMAT('".date("Y-m-d", mktime(0,0,0,$mth,$day,$yr))."','%Y-%m-%d') )";
			}
		} else {
			$end_date_str = " 1 ";
		}
		
	  	$payment_id_str = (isset($_SESSION[$session_name]["payment_id"]) && tep_not_null($_SESSION[$session_name]["payment_id"])) ? " sah.store_account_history_trans_id='" . $_SESSION[$session_name]["payment_id"] . "' AND sah.store_account_history_trans_type='P'" : "1";
		$order_id_str = (isset($_SESSION[$session_name]["order_id"]) && tep_not_null($_SESSION[$session_name]["order_id"])) ? " sah.store_account_history_trans_id = '".$_SESSION[$session_name]["order_id"]."' AND sah.store_account_history_trans_type<>'P'" : "1";
	  	
	  	$statement_user_str = " sah.user_id='" . tep_db_input($this->user_id) . "' and sah.user_role='".tep_db_input($this->user_role)."' ";
	  	
	  	$result_display_text = TEXT_DISPLAY_NUMBER_OF_RECORDS;
	  	
	  	// Need store_account_history_id in ORDER BY to maintain the ordering for those records on the same date and time
	  	$acc_statement_select_sql = "	SELECT sah.store_account_history_id, DATE_FORMAT(sah.store_account_history_date, '%Y-%m-%d %H:%i') AS activity_date, sah.store_account_history_trans_type AS trans_type, sah.store_account_history_trans_id AS trans_id, 
			  								sah.store_account_history_activity_title AS activity_title, sah.store_account_history_activity_desc AS activity_desc, sah.store_account_transaction_reserved AS trans_reserved, sah.store_account_history_activity_desc_show, 
			  								sah.store_account_history_currency AS currency, sah.store_account_history_debit_amount AS debit_amount, sah.store_account_history_credit_amount AS credit_amount, sah.store_account_history_after_balance AS after_balance 
										FROM " . TABLE_STORE_ACCOUNT_HISTORY . " AS sah 
										WHERE " . $order_id_str . " 
											AND " . $payment_id_str . " 
											AND " . $start_date_str . " 
											AND " . $end_date_str . "
											AND " . $statement_user_str . "
										ORDER BY sah.store_account_history_date desc, sah.store_account_history_id DESC";
		
		$show_records = $_SESSION[$session_name]["show_records"];
		
		if ($show_records != "ALL") {
			$acc_statement_split_object = new splitPageResults($_REQUEST['page'], ($show_records=='' || $show_records=='DEFAULT' ? MAX_DISPLAY_SEARCH_RESULTS : (int) $show_records), $acc_statement_select_sql, $acc_statement_select_sql_numrows, true);
		}
		$acc_statement_result_sql = tep_db_query($acc_statement_select_sql);
		
		ob_start();
?>
			<table width="100%" border="0" cellspacing="2" cellpadding="2">
				<tr>
					<td>&nbsp;</td>
				</tr>
				<tr>
					<td>
						<table width="90%" border="0" align="center" cellpadding="1" cellspacing="1" class="smallText" valign="middle">
							<tr>
								<td width="12%" class="reportBoxHeading"><?=TABLE_HEADING_ACC_STAT_DATETIME?></td>
								<td class="reportBoxHeading"><?=TABLE_HEADING_ACC_STAT_ACTIVITY?></td>
								<td width="15%" align="center" class="reportBoxHeading"><?=TABLE_HEADING_ACC_STAT_PAYMENT_STATUS?></td>
							    <td width="15%" class="reportBoxHeading" align="right"><?=TABLE_HEADING_ACC_STAT_DEBIT?></td>
							    <td width="15%" class="reportBoxHeading" align="right"><?=TABLE_HEADING_ACC_STAT_CREDIT?></td>
							    <td width="15%" class="reportBoxHeading" align="right"><?=TABLE_HEADING_ACC_STAT_BALANCE?></td>
							</tr>
<?
		$row_count = 0;
		while ($acc_statement_row = tep_db_fetch_array($acc_statement_result_sql)) {
			$row_style = ($row_count%2) ? 'reportListingEven' : 'reportListingOdd' ;
	  		
	  		$acc_history_id_str = $activity_title = '';
	  		$trans_details = '';
	  		
	  		if (tep_not_null($acc_statement_row['activity_title'])) {
	  			if (strtolower(trim($acc_statement_row['activity_title'])) == 'manual deduction' ||
	  				strtolower(trim($acc_statement_row['activity_title'])) == 'manual addition') {
	  				$acc_history_id_str = sprintf(TEXT_ACC_STAT_TRANS_ID, $acc_statement_row['store_account_history_id']);
	  			}
	  			$activity_title = $acc_statement_row['activity_title'] . (tep_not_null($acc_history_id_str) ? '<br>'.$acc_history_id_str : '') . (tep_not_null($acc_statement_row['activity_desc']) ? (($acc_statement_row['store_account_history_activity_desc_show']) ? '<br><div class="paymentRemarkSelectedRow">'.TEXT_ACC_STAT_ADMIN_COMMENT.'<br>' . nl2br($acc_statement_row['activity_desc']) : '') : '') . '</div>';
	  		} else {
	  			switch($acc_statement_row['trans_type']) {
	  				case 'P':
	  					$activity_title = sprintf(TITLE_TRANS_PAYMENT, $acc_statement_row['trans_id']);
	  					break;
	  				case 'S':
	  					$activity_title = sprintf(TITLE_TRANS_SUPPLIER_ORDER, $acc_statement_row['trans_id']);
	  					break;
	  				case 'PWL':
	  					$activity_title = sprintf(TITLE_TRANS_PWL_ORDER, $acc_statement_row['trans_id']);
	  					break;
	  				case 'B':
	  					$activity_title = sprintf(TITLE_TRANS_BUYBACK_ORDER, $acc_statement_row['trans_id']);
	  					break;
	  				default:
	  					$activity_title = $acc_statement_row['trans_type'] . ' ' . $acc_statement_row['trans_id'];
	  					break;
	  			}
	  		}
	  		
	  		if ($acc_statement_row['trans_type'] == 'P') {
	  			$payment_info_select_sql = "SELECT sp.store_payments_status, sp.store_payments_fees_calculation, sps.store_payments_status_name 
	  										FROM " . TABLE_STORE_PAYMENTS . " AS sp 
	  										INNER JOIN " . TABLE_STORE_PAYMENTS_STATUS . " sps 
	  											ON (sp.store_payments_status=sps.store_payments_status_id AND sps.language_id = '".$languages_id."') 
	  										WHERE sp.store_payments_id='".tep_db_input($acc_statement_row['trans_id'])."'";
	  			$payment_info_result_sql = tep_db_query($payment_info_select_sql);
	  			$payment_info_row = tep_db_fetch_array($payment_info_result_sql);
	  			
	  			if ($payment_info_row['store_payments_status'] == '3' || $payment_info_row['store_payments_status'] == '4') {
	  				$status_update_date_select_sql = "	SELECT date_added 
	  													FROM " . TABLE_STORE_PAYMENTS_HISTORY . " 
	  													WHERE store_payments_id = '".tep_db_input($acc_statement_row['trans_id'])."' 
	  														AND store_payments_status = '".$payment_info_row['store_payments_status']."' 
	  													ORDER BY date_added DESC 
	  													LIMIT 1";
	  				$status_update_date_result_sql = tep_db_query($status_update_date_select_sql);
		  			$status_update_date_row = tep_db_fetch_array($status_update_date_result_sql);
		  			
	  				$payment_status = sprintf(TEXT_ACC_STAT_PAYMENT_STATUS_UPDATE_DATE, $payment_info_row['store_payments_status_name'], $status_update_date_row['date_added']);
	  			} else {
	  				$payment_status = $payment_info_row['store_payments_status_name'];
	  			}
	  			
	  			if (tep_not_null($payment_info_row['store_payments_fees_calculation']))	$trans_details = '&nbsp;<a href="javascript:;" onmouseover="ddrivetip(\''.str_replace(array("\r\n", "\n"), array('', '<br>'), htmlspecialchars(addslashes($payment_info_row['store_payments_fees_calculation']), ENT_QUOTES)).'\', \'\' , 500);" onmouseout="hideddrivetip();">[Details]</a>';
	  		} else {
	  			$payment_status = TEXT_ACC_STAT_NOT_APPLICABLE;
	  		}
	  		
	  		echo '			<tr height="20" class="'.$row_style.'" onmouseover="rowOverEffect(this, \'reportListingRowOver\')" onmouseout="rowOutEffect(this, \''.$row_style.'\')" onclick="rowClicked(this, \''.$row_style.'\')">
								<td valign="top" class="reportRecords" nowrap>'.$acc_statement_row['activity_date'].'</td>
								<td valign="top" class="reportRecords">'.$activity_title.$trans_details;

			$comments_select_sql = "SELECT store_account_comments_date_added, store_account_comments, store_account_comments_notified
		                            FROM ". TABLE_STORE_ACCOUNT_COMMENTS ." 
		                            WHERE store_account_history_id='". $acc_statement_row['store_account_history_id'] ."'
		                                AND store_account_comments_notified='1'
		                            ORDER BY store_account_comments_id";
            $comments_result_sql = tep_db_query($comments_select_sql);

            if (tep_db_num_rows($comments_result_sql) > 0) {
                    while ($comments_row = tep_db_fetch_array($comments_result_sql)) {
    					    echo '<hr>'. $comments_row['store_account_comments_date_added'] . '<br><div class="paymentRemarkSelectedRow">Comment:<br>'. nl2br($comments_row['store_account_comments']) . '</div>';
					}
                }

            echo '				</td>
								<td align="center" valign="top" class="reportRecords">'.$payment_status.'</td>
								<td align="right" valign="top" class="reportRecords">'.(tep_not_null($acc_statement_row['debit_amount']) ? $currencies->format($acc_statement_row['debit_amount'], false, $acc_statement_row['currency']) : TEXT_ACC_STAT_NOT_APPLICABLE).'</td>
								<td align="right" valign="top" class="reportRecords">'.(tep_not_null($acc_statement_row['credit_amount']) ? $currencies->format($acc_statement_row['credit_amount'], false, $acc_statement_row['currency']) : TEXT_ACC_STAT_NOT_APPLICABLE).'</td>
								<td align="right" valign="top" class="reportRecords">'.$currencies->format($acc_statement_row['after_balance'], false, $acc_statement_row['currency']).'</td>
							  </tr>';
	  		$row_count++;
	  	}
?>
						</table>
					</td>
				</tr>
				<tr>
        			<td>
        				<table width="90%" border="0" align="center" cellpadding="1" cellspacing="0" class="smallText" valign="middle">
          					<tr>
            					<td class="smallText" valign="top"><?=$show_records == "ALL" ? sprintf($result_display_text, tep_db_num_rows($acc_statement_result_sql) > 0 ? "1" : "0", tep_db_num_rows($acc_statement_result_sql), tep_db_num_rows($acc_statement_result_sql)) : $acc_statement_split_object->display_count($acc_statement_select_sql_numrows, ($show_records=='' || $show_records=='DEFAULT' ? MAX_DISPLAY_SEARCH_RESULTS : (int)$show_records), $_REQUEST['page'], $result_display_text)?></td>
            					<td class="smallText" align="right"><?=$show_records == "ALL" ? sprintf(TEXT_RESULT_PAGE, '1', '1') : $acc_statement_split_object->display_links($acc_statement_select_sql_numrows, ($show_records=='' || $show_records=='DEFAULT' ? MAX_DISPLAY_SEARCH_RESULTS : (int) $show_records), MAX_DISPLAY_PAGE_LINKS, $_REQUEST['page'], tep_get_all_get_params(array('page', 'cont'))."cont=1", 'page')?></td>
          					</tr>
        				</table>
        			</td>
				</tr>
			</table>
<?
		$report_section_html = ob_get_contents();
		ob_end_clean() ;
		
		// This should be here to let all the session get updated value
		$search_section_html = $this->search_acc_statement($filename, $session_name);
		
		return $search_section_html . "\n" . $report_section_html;
	}
	
	function _get_payment_info($payment_id) {
		global $sup_languages_id;
		
		$payment_info_select_sql = "SELECT * 
									FROM " . TABLE_STORE_PAYMENTS . "
									WHERE store_payments_id = '" . tep_db_input($payment_id) . "'";
		$payment_info_result_sql = tep_db_query($payment_info_select_sql);
		
		if ($payment_info_row = tep_db_fetch_array($payment_info_result_sql)) {
			$payment_status_select_sql = "	SELECT store_payments_status_name 
	  										FROM " . TABLE_STORE_PAYMENTS_STATUS . " 
	  										WHERE store_payments_status_id = '" . tep_db_input($payment_info_row['store_payments_status']) . "' 
	  											AND language_id='" . $sup_languages_id  . "'";
	  		$payment_status_result_sql = tep_db_query($payment_status_select_sql);
	  		$payment_status_row = tep_db_fetch_array($payment_status_result_sql);
	  		
			$this->info = array('pay_id' => $payment_id,
								'payments_date' => $payment_info_row['store_payments_date'],
								'status' => $payment_info_row['store_payments_status'],
								'status_name' => $payment_status_row['store_payments_status_name'],
								'last_modified' => $payment_info_row['store_payments_last_modified']
								);
			
			$this->beneficiary = array(	'id' => $payment_info_row['user_id'],
	      								'role' => $payment_info_row['user_role'],
	      								'firstname' => $payment_info_row['user_firstname'],
	      								'lastname' => $payment_info_row['user_lastname'],
	      								'email_address' => $payment_info_row['user_email_address']
	      								);
	      	
	      	$this->payment_info = array('request_currency' => $payment_info_row['store_payments_request_currency'],
	      								'request_amount' => $payment_info_row['store_payments_request_amount'],
	      								'fees' => $payment_info_row['store_payments_fees'],
	      								'after_fees_amount' => $payment_info_row['store_payments_after_fees_amount'],
	      								'paid_currency' => $payment_info_row['store_payments_paid_currency'],
	      								'exchange_rate' => $payment_info_row['store_payments_paid_currency_value'],
	      								'paid_amount' => $payment_info_row['store_payments_paid_amount'],
	      								'reference' => $payment_info_row['store_payments_reference'],
	      								'payment_methods_id' => $payment_info_row['store_payments_methods_id'],
	      								'payment_methods_name' => $payment_info_row['store_payments_methods_name'],
	      								'account_book_id' => $payment_info_row['store_payment_account_book_id'],
	      								'payment_methods_alias' => $payment_info_row['user_payment_methods_alias'],
	      								'fees_calculation' => $payment_info_row['store_payments_fees_calculation']
	      								);
		}
	}
}
?>