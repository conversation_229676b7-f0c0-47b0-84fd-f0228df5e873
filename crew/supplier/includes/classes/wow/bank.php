<?php
include_once "item.php";
include_once "char.php";

class bank extends item {
	var $contents;

	function bank($data){
		parent::item($data);

		$this->contents = item_get_many($this->data['id']);
	}
  

	function out() {

		echo '<div class="bank">';

		$offset = -1 * (28 % 7);
		for($slot = $offset, $idx = 28 - $offset; $slot < 28; $slot++, $idx--) {

			if( $idx % 7 == 0 ) {
				echo '<div class="bankLine">';
			}

			if($slot < 0) {
				echo '<div class="bankNoSlot"></div>';
			} else {
				echo '<div class="bankSlot">';
				$item = $this->contents[$slot+1];

				if(isset($item)) {
					$item->out();
				}
				
				echo '</div>';
			}

			if($idx % 7 == 1) {
				echo "</div>\n";
			}
		}
		
		$bank_bag_slot = bank_bag_num($this->data["game_char_id"], $this->data['game_char_history_id']);
		echo '<div class="bankLine"><div class="bankNoSlot"></div></div>';
		
		$default_bank_bag_slot = 7;
		
		if ($bank_bag_slot > $default_bank_bag_slot) {
			$default_bank_bag_slot = $bank_bag_slot;
		}
		echo '<div class="bankLine">';
			
		for ($bank_bank_count=1; $bank_bank_count <= $default_bank_bag_slot; $bank_bank_count++) {
			echo '<div class="bankSlot">';
			$bank_bag = item_get_one($this->data['game_char_id'], $slot, $this->data['game_char_history_id'], 'bankbag'.$bank_bank_count);

			if (isset($bank_bag)) {
				$bank_bag->out();
			}
			echo "</div>";
		}
		echo "</div>\n";

	echo '</div>';

	}
}

function bank_get($id, $slot, $date, $parent) {
	$item = item_get_one($id, '', $date, $parent);
	
	if($item) {
		return new bank($item->data);
	} else {
		return Null;
	}
}

function bank_bag_num ($game_char_id, $game_char_history_id) {
	$char_item_parent_select_sql = "SELECT count(char_item_parent) AS bank_bag_slots   
									FROM " . TABLE_CHAR_ITEM_HISTORY . " 
									WHERE game_char_id ='" . tep_db_input($game_char_id) . "' 
										AND game_char_history_id='" . tep_db_input($game_char_history_id) . "' 
										AND char_item_parent LIKE 'bankBag%'";
	
	$char_item_parent_result_sql = tep_db_query($char_item_parent_select_sql);
	$char_item_parent_row = tep_db_fetch_array($char_item_parent_result_sql);
	
	return $char_item_parent_row["bank_bag_slots"];
}
?>