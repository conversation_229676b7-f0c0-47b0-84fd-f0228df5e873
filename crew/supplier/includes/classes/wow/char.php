<?php

include_once "item.php";
include_once "skill.php";
include_once "quest.php";
include_once "bank.php";
include_once "honor.php";
include_once "reputation.php";
include_once "pet.php";

class char {
	var $data;
	var $skilltypes = array( 1 => 'Class Skills',
							 2 => 'Professions',
							 3 => 'Secondary Skills',
							 4 => 'Weapon Skills',
							 5 => 'Armor Proficiencies',
							 6 => 'Languages' );
			  
	function char($data) {
		$this->data = $data;
	}
  
	function get($field) {
		return $this->data[$field];
	}

	function printStat($statname) {
		list($base, $current, $mod) = explode( ":", $this->data[$statname] );
		
		if($mod == 0) {
			echo '<span class="white">';
		} else if($mod < 0) {
			echo '<span class="purple">';
		} else {
			echo '<span class="green">';
		}
		
		echo $current + $base . "</span>";
	}

	function printEquip($slot, $date) {
		$item = item_get_one($this->data['game_char_id'], $slot, $date, 'equip');
	
		if(isset($item)){
			$item->out();
		} else {
			echo '<div class="icon"></div>';
		}
	}

	function printAtk($type, $stat) {
		$atk = str_replace(':','-',$this->data[$type . '_' . $stat]);
		echo '<span class="white">';
		echo "$atk";
		echo "</span>";
	}
  
	function printTab($name, $div, $enabled = False) {
		if($enabled) {
			echo "<div class=\"tab\"><font id=\"tabfont$div\" class=\"white\">";
		} else {
			echo "<div class=\"tab\"><font id=\"tabfont$div\" class=\"yellow\">";
		}
		echo "<span onClick=\"doTab('$div')\" style=\"cursor: pointer;\">$name</span></font></div>";
	}

	function printSkills() {
		list($major, $minor) = explode( '.', $this->data['version'] );
	
		for($i=1; $i<7; $i++) {
			if(($major == 0) && ($minor < 96)) {
				$skills = skill_get_many_by_type($this->data['game_char_id'], $this->skilltypes[$i], $this->data['game_char_history_id']);
			} else {
				$skills = skill_get_many_by_order($this->data['game_char_id'], $i, $this->data['game_char_history_id']);
			}
			
			if(isset($skills[0])) {
				$skills[0]->outHeader();
				foreach ($skills as $skill) {
					$skill->out();
				}
			}
		}
	}

	function printQuests() {
		$quests = get_quests($this->data['game_char_id'], $this->data['game_char_history_id']);
		$cur_location = '';
		$subOpen = false;
		echo '<ul>';
		
		foreach ($quests as $location) {
			if ($cur_location != $location[0]) {
				$cur_location=$location[0];
				$location[0]->outHeader($subOpen);
				$subOpen = true;
			}
	
		foreach ($location as $quest) {
			$quest->out();
		}
	}
	
	if ($subOpen) {
		echo '</ul></li>';
	}
	echo '</ul>';
	}

	function printTalents() {
		list($major, $minor) = explode( '.', $this->data['version'] );
	
		for($i=1 ; $i<7; $i++) {
			if( ($major == 0) && ($minor < 96) ) {
				$skills = skill_get_many_by_type( $this->data['name'], $this->data['server'], $this->skilltypes[$i]);
			} else {
				$skills = skill_get_many_by_order( $this->data['name'], $this->data['server'], $i);
			}
			
			if( isset($skills[0])) {
				$skills[0]->outHeader();
				
				foreach ($skills as $skill) {
					$skill->out();
				}
			}
		}
	}
	
	function printHonor() {
		$honor = get_honor($this->data['game_char_id'], $this->data['game_char_history_id']);
		
		if (isset($honor)) {
			$honor->out();
		}
	}
	
	function printReputation() {
		$reputation = get_reputation($this->data['game_char_id'], $this->data['game_char_history_id']);
		
		if (isset($reputation)) {
			$reputation->out();
		}
	}
	
	function printPet() {
		$pet = get_pet($this->data['game_char_id'], $this->data['game_char_history_id']);
		
		if (isset($pet)) {
			$pet->out();
		}
	}
	
	function printResist($resist_name) {
		$resist_value = explode(':', $this->data[$resist_name]);
		
		echo $resist_value[0];
	}
	
	function out() {
		echo '<script><!--
		
				var tabs = new Array()
				var tab_count = 0

				function addTab(name) {
					tabs[tab_count] = name;
					tab_count++;
				}

				function doTab(div) {
					for(i=0; i<tab_count; i++) {
						obj = document.getElementById(tabs[i]);
						fontobj = document.getElementById("tabfont"+tabs[i]);
						
						if(tabs[i] == div) {
							obj.style.display="block";
							fontobj.style.color="#ffffff";
						} else {
							obj.style.display="none";
							fontobj.style.color="#aa9900";
						}
					}
				}

				addTab(\'page1\')
				addTab(\'page2\')
				addTab(\'page3\')
				addTab(\'page4\')
				addTab(\'page5\')
				addTab(\'page6\')

				--></script>

			<div class="char" id="char">
				<div class="main">
					<div class="top" id="top">
						<h1>' . $this->data['name'] . ' <small>(' . $this->data['game_char_history_date'] . ') <small>' . '</h1>
						<h2>Level ' . $this->data['level'] . ' ' . $this->data['race'].' '.$this->data['class'].'</h2>';
	
						if(isset($this->data['guild_name'])) {
							if (tep_not_null($this->data['guild_name'])) {
								echo '<h2>'.$this->data['guild_title'].' of '.$this->data['guild_name'].'</h2>';
							}
						}
						$exp_array = explode(':', $this->data['exp']);
						echo '<h2>Experience: ' . $exp_array[0] . ' / ' . $exp_array[1] . '</h2>'.
					'</div>
					<div class="page1" id="page1">
						<div class="left">
							<div class="equip">', $this->printEquip("Head", $this->data['game_char_history_id']); echo '</div>
							<div class="equip">', $this->printEquip("Neck", $this->data['game_char_history_id']); echo '</div>
							<div class="equip">', $this->printEquip("Shoulder", $this->data['game_char_history_id']); echo '</div>
							<div class="equip">', $this->printEquip("Back", $this->data['game_char_history_id']); echo '</div>
							<div class="equip">', $this->printEquip("Chest", $this->data['game_char_history_id']); echo '</div>
							<div class="equip">', $this->printEquip("Shirt", $this->data['game_char_history_id']); echo '</div>
							<div class="equip">', $this->printEquip("Tabard", $this->data['game_char_history_id']); echo '</div>
							<div class="equip">', $this->printEquip("Wrist", $this->data['game_char_history_id']); echo '</div>
						</div> <!-- left -->
						<div class="middle">
							<div class="portrait">
								<table border="0" width="12%" cellspacing="0" cellpadding="0" class="resistTable" align="right">
									<tr>
										<td height="29px" align="center" valign="bottom">
											<span class="white">'; $this->printResist("res_fire"); echo '</span>
										</td>
									</tr>
									<tr>
										<td height="29px" align="center" valign="bottom">
											<span class="white">'; $this->printResist("res_nature"); echo '</span>
										</td>
									</tr>
									<tr>
										<td height="29px" align="center" valign="bottom">
											<span class="white">'; $this->printResist("res_arcane"); echo '</span>
										</td>
									</tr>
									<tr>
										<td height="29px" align="center" valign="bottom">
											<span class="white">'; $this->printResist("res_frost"); echo '</span>
										</td>
									</tr>
									<tr>
										<td height="29px" align="center" valign="bottom">
											<span class="white">'; $this->printResist("res_shadow"); echo '</span>
										</td>
									</tr>
								</table>
							</div>
							<div class="bottom">
								<div class="padding">
									<ul class="stats">
										<li>Strength: ';  $this->printStat("stat_str"); echo '</li>
										<li>Agility: ';   $this->printStat("stat_agl"); echo '</li>
										<li>Stamina: ';   $this->printStat("stat_sta"); echo '</li>
										<li>Intellect: '; $this->printStat("stat_int"); echo '</li>
										<li>Spirit: ';    $this->printStat("stat_spr"); echo '</li>
										<li>Armor: ';     $this->printStat("armor"); echo '</li>
									</ul>
									<ul class="stats">
										<li>Melee Attack '; $this->printAtk("melee","rating"); echo '
											<ul>
												<li>Power: '; $this->printAtk("melee","power"); echo '</li>
												<li>Damage: '; $this->printAtk("melee","range"); echo '</li>
											</ul>
										</li>
										<!--li style="line-height:0.1em;">&nbsp;</li-->
										<li>Ranged Attack '; $this->printAtk("ranged","rating"); echo '
											<ul>
												<li>Power: '; $this->printAtk("ranged","power"); echo '</li>
												<li>Damage: '; $this->printAtk("ranged","range"); echo '</li>
											</ul>
										</li>
									</ul>
								</div> <!-- padding -->
								<div class="hands">
									<div class="weapon0">'; $this->printEquip("MainHand", $this->data['game_char_history_id']); echo '</div>
									<div class="weapon1">'; $this->printEquip("SecondaryHand", $this->data['game_char_history_id']); echo '</div>
									<div class="weapon2">'; $this->printEquip("Ranged", $this->data['game_char_history_id']); echo '</div>
								</div><!-- hands -->
							</div> <!-- bottom -->
						</div> <!-- middle -->
						<div class="right">
							<div class="equip">', $this->printEquip("Hands", $this->data['game_char_history_id']); echo '</div>
							<div class="equip">', $this->printEquip("Waist", $this->data['game_char_history_id']); echo '</div>
							<div class="equip">', $this->printEquip("Legs", $this->data['game_char_history_id']); echo '</div>
							<div class="equip">', $this->printEquip("Feet", $this->data['game_char_history_id']); echo '</div>
							<div class="equip">', $this->printEquip("Finger0", $this->data['game_char_history_id']); echo '</div>
							<div class="equip">', $this->printEquip("Finger1", $this->data['game_char_history_id']); echo '</div>
							<div class="equip">', $this->printEquip("Trinket0", $this->data['game_char_history_id']); echo '</div>
							<div class="equip">', $this->printEquip("Trinket1", $this->data['game_char_history_id']); echo '</div>
						</div> <!-- right -->
					</div><!-- page1 -->
					<div class="page2" id="page2">
						<div class="left"></div>
						<div class="skills">'; $this->printSkills(); echo '</div>
						<div class="right"></div>
					</div>
					<div class="page2" id="page3">
						<div class="left"></div>
						<div class="skills">'; $this->printQuests(); echo '</div>
						<div class="right"></div>
					</div>
					<div class="page4" id="page4">
						<div class="left"></div>
						<div class="honor">'; $this->printHonor();  echo '</div>
						<div class="right"></div>
					</div>
					<div class="page2" id="page5">
						<div class="left"></div>
						<div class="rep">'; $this->printReputation();  echo '</div>
						<div class="right"></div>
					</div>
					<div class="page6" id="page6">
						<div class="left"></div>
						<div class="pet">'; $this->printPet(); echo '</div>
						<div class="right"></div>
					</div>
				</div><!-- main -->
				<div class="bottomBorder"></div>
				<div class="tabs">';
					$this->printTab( "Stats", "page1", True );
					$this->printTab( "Skills", "page2" );
					$this->printTab( "Quests", "page3" );
					$this->printTab( "PvP", "page4" );
					$this->printTab( "Rep", "page5" );
					$this->printTab( "Pet", "page6" );
					
		echo '
			  	</div>
			</div> <!-- char -->';
	}
}

function char_get_one($id, $date) 
{
	$char_info_select_sql = "	SELECT stat_int, stat_agl, stat_sta, stat_str, stat_spr, guild_name, guild_title, race, res_holy, res_frost, res_arcane, res_fire, armor, level, defense, talent_points, money_c, money_s, money_g, exp, class, health, melee_power, melee_rating, melee_range, melee_range_tooltip, melee_power_tooltip, ranged_power, ranged_rating, ranged_range, ranged_range_tooltip, ranged_power_tooltip, version, game_char_history_date 
								FROM " . TABLE_GAME_CHAR_HISTORY . " 
								WHERE game_char_id = '" . (int)$id . "' 
									AND game_char_history_id ='" . tep_db_input($date) . "'";
								
	$char_info_result_sql = tep_db_query($char_info_select_sql);
	$char_info_row = tep_db_fetch_array($char_info_result_sql);
	
	return new char($char_info_row);
}
?>