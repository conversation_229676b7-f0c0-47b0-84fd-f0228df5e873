<?php
	class honor {
		var $data;
		
		function honor ($data) {
			$this->data = $data;
		}
		
		function out () {
			$current_honor = (isset($this->data['current_honor']) ? $this->data['current_honor'] : 0);
			$session_honorable_kill = (isset($this->data['session_honorable_kill']) ? $this->data['session_honorable_kill'] : 0);
			$session_honor = (isset($this->data['session_honor']) ? $this->data['session_honor'] : 0);
			$yesterday_honorable_kill = (isset($this->data['yesterday_honorable_kill']) ? $this->data['yesterday_honorable_kill'] : 0);
			$yesterday_honor = (isset($this->data['yesterday_honor']) ? $this->data['yesterday_honor'] : 0);
			$life_time_honorable_kill = (isset($this->data['life_time_honorable_kill']) ? $this->data['life_time_honorable_kill'] : 0);	
?>
					<table border="0" cellspacing="0" cellpadding="0" width="100%">
						<tr>
							<td><?=tep_draw_separator('pixel_trans.gif', '1', '23')?></td>
						</tr>
						<tr>
							<td>
								<table border="0" cellspacing="0" cellpadding="0" width="100%">
									<tr>
										<td width="100%">
											<table border="0" cellspacing="0" cellpadding="0" width="100%">
												<tr>
													<td width="48%">&nbsp;</td>
													<td><span class="yellow"><?=$current_honor?></span></td>
												</tr>
											</table>
										</td>
									</tr>
									<tr>
										<td height="30px" width="100%">&nbsp;</td>
									</tr>
									<tr>
										<td width="100%">
											<table border="0" cellspacing="0" cellpadding="0" width="100%">
												<tr height="18px">
													<td width="28%" align="center">&nbsp;</td>
													<td width="15%" align="center"><span class="white"><?=$session_honorable_kill?></span></td>
													<td width="17%" align="center"><span class="white"><?=$yesterday_honorable_kill?></span></td>
													<td width="16%" align="center"><span class="white"><?=$life_time_honorable_kill?></span></td>
													<td align="center">&nbsp;</td>
												</tr>
												<tr height="18px">
													<td width="28%" align="center">&nbsp;</td>
													<td width="15%" align="center"><span class="white"><?=$session_honor?></span></td>
													<td width="17%" align="center"><span class="white"><?=$yesterday_honor?></span></td>
													<td width="16%" align="center">&nbsp;</td>
													<td align="center">&nbsp;</td>
												</tr>
											</table>
										</td>
									</tr>
								</table>
							</td>
						</tr>
					</table>
<?
		}
	}
		
	function get_honor ($id, $game_char_history_id) {
		$honor_info_select_sql = "	SELECT current_honor, yesterday_honorable_kill, yesterday_honor, session_honorable_kill, session_honor 
									FROM " . TABLE_CHAR_HONOR_HISTORY . " 
									WHERE game_char_id ='" . tep_db_input($id) . "' 
										AND game_char_history_id ='" . tep_db_input($game_char_history_id) . "'";
		$honor_info_result_sql = tep_db_query($honor_info_select_sql);
		$honor_info_row = tep_db_fetch_array($honor_info_result_sql);
		
		$honor = new honor ($honor_info_row);
		
		return $honor;
	}
?>