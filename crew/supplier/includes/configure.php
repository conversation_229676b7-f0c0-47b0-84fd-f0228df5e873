<?php
/*
  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2003 osCommerce

  Released under the GNU General Public License
*/

/*******************************************************************
	Define the webserver and path parameters
	DIR_FS_* = Filesystem directories (local/physical)
	
	DIR_WS_* = Webserver directories (virtual/URL)
		Note: Should not has leading slash and must has ending slash
********************************************************************/
  define('HTTP_SERVER', 'http://carerra.localhost/'); // eg, http://localhost - should not be empty for productive servers
  define('HTTPS_SERVER', 'https://carerra.localhost/');
  define('HTTP_CATALOG_SERVER', 'http://carerra.localhost/');
  define('HTTPS_CATALOG_SERVER', 'https://carerra.localhost/');
  define('HTTP_ADMIN_SERVER', 'http://carerra.localhost/');
  define('HTTPS_ADMIN_SERVER', 'https://carerra.localhost/');
  define('ENABLE_SSL', 'false'); // secure webserver for supplier module
  define('ENABLE_SSL_ADMIN', 'false'); // secure webserver for admin module
  define('ENABLE_SSL_CATALOG', 'false'); // secure webserver for catalog module
  define('HTTP_COOKIE_DOMAIN', 'carerra.localhost');
  define('HTTPS_COOKIE_DOMAIN', 'carerra.localhost');
  define('HTTP_COOKIE_PATH', '/supplier/');
  define('HTTPS_COOKIE_PATH', '/supplier/');
  define('DIR_FS_DOCUMENT_ROOT', 'C:/Apache2/vhosts/carerra.localhost/httpdocs/supplier/'); // where the pages are located on the server
  define('DIR_WS_ADMIN', 'admin/'); // absolute path required
  define('DIR_WS_SUPPLIER', 'supplier/'); // absolute path required
  define('DIR_FS_SUPPLIER', 'C:/Apache2/vhosts/carerra.localhost/httpdocs/supplier/'); // absolute path required
  define('DIR_WS_CATALOG', ''); // absolute path required
  define('DIR_FS_CATALOG', 'C:/Apache2/vhosts/carerra.localhost/httpdocs/'); // absolute path required
  define('DIR_WS_IMAGES', 'images/');
  define('DIR_WS_ICONS', DIR_WS_IMAGES . 'icons/');
  define('DIR_WS_INTERFACE', DIR_WS_IMAGES . 'Interface/');
  define('DIR_WS_CATALOG_IMAGES', DIR_WS_CATALOG . 'images/');
  define('DIR_WS_INCLUDES', 'includes/');
  define('DIR_WS_BOXES', DIR_WS_INCLUDES . 'boxes/');
  define('DIR_WS_FUNCTIONS', DIR_WS_INCLUDES . 'functions/');
  define('DIR_WS_CLASSES', DIR_WS_INCLUDES . 'classes/');
  define('DIR_WS_MODULES', DIR_WS_INCLUDES . 'modules/');
  define('DIR_WS_LANGUAGES', DIR_WS_INCLUDES . 'languages/');
  define('DIR_WS_CATALOG_LANGUAGES', DIR_WS_CATALOG . 'includes/languages/');
  define('DIR_FS_CATALOG_LANGUAGES', DIR_FS_CATALOG . 'includes/languages/');
  define('DIR_FS_CATALOG_IMAGES', DIR_FS_CATALOG . 'images/');
  define('DIR_FS_CATALOG_MODULES', DIR_FS_CATALOG . 'includes/modules/');
  define('DIR_FS_BACKUP', DIR_FS_SUPPLIER . 'backups/');
  define('DIR_FS_UPGRADE', DIR_FS_SUPPLIER . 'upgrade/');
  define('DIR_FS_REDJACK_LOG', 'C:/Apache2/vhosts/carerra.localhost/httpdocs/supplier/log/'); // Store RedJack Log File
  
// define our database connection
  define('DB_SERVER', 'localhost'); // eg, localhost - should not be empty for productive servers
  define('DB_SERVER_USERNAME', 'skc_user');
  define('DB_SERVER_PASSWORD', 'skc_password');
  define('DB_DATABASE', 'carerra');
  define('USE_PCONNECT', 'false'); // use persisstent connections?
  define('STORE_SESSIONS', 'mysql'); // leave empty '' for default handler or set to 'mysql'
?>