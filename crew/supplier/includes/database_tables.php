<?php
/*
  $Id: database_tables.php,v 1.22 2014/07/24 06:45:19 chingyen Exp $

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2003 osCommerce

  Released under the GNU General Public License
*/

//Admin begin
define('TABLE_ADMIN', 'admin');
define('TABLE_ADMIN_FILES', 'admin_files');
define('TABLE_ADMIN_FILES_ACTIONS', 'admin_files_actions');
define('TABLE_ADMIN_GROUPS', 'admin_groups');

// Products_flag
define ('TABLE_PRODUCTS_FLAG' , 'products_flag');

// Latest news
define('TABLE_LATEST_NEWS', 'latest_news');
define('TABLE_LATEST_NEWS_DESCRIPTION', 'latest_news_description');
define('TABLE_LATEST_NEWS_GROUPS', 'latest_news_groups');
  
// Database tables
define('TABLE_ADDRESS_FORMAT', 'address_format');
define('TABLE_BANNERS', 'banners');
define('TABLE_BANNERS_HISTORY', 'banners_history');
define('TABLE_CART_COMMENTS', 'cart_comments');
define('TABLE_CATEGORIES', 'categories');
define('TABLE_CATEGORIES_CONFIGURATION', 'categories_configuration');
define('TABLE_CATEGORIES_DESCRIPTION', 'categories_description');
define('TABLE_CATEGORIES_GROUPS', 'categories_groups');   
define('TABLE_CONFIGURATION', 'configuration');
define('TABLE_CONFIGURATION_GROUP', 'configuration_group');
define('TABLE_COUNTRIES', 'countries');
define('TABLE_CURRENCIES', 'currencies');
define('TABLE_CUSTOMERS_BASKET', 'customers_basket');
define('TABLE_CUSTOMERS_BASKET_ATTRIBUTES', 'customers_basket_attributes');
define('TABLE_CUSTOMERS_BASKET_BUNDLE', 'customers_basket_bundle');
define('TABLE_CUSTOMERS_INFO', 'customers_info');
define('TABLE_CUSTOMERS_LAST_CPATH', 'customers_last_cpath');
define('TABLE_CUSTOMERS_REMARKS_HISTORY', 'customers_remarks_history');
define('TABLE_LANGUAGES', 'languages');
define('TABLE_LOG_FILES', 'log_files');
define('TABLE_LOG_TABLE', 'log_table');
define('TABLE_MANUFACTURERS', 'manufacturers');
define('TABLE_MANUFACTURERS_INFO', 'manufacturers_info');
define('TABLE_NEWSLETTERS', 'newsletters');
define('TABLE_ORDERS', 'orders');
define('TABLE_ORDERS_COMMENTS', 'orders_comments');
define('TABLE_ORDERS_LOG_TABLE', 'orders_log_table');
define('TABLE_ORDERS_PRODUCTS', 'orders_products');
define('TABLE_ORDERS_PRODUCTS_ATTRIBUTES', 'orders_products_attributes');
define('TABLE_ORDERS_PRODUCTS_DOWNLOAD', 'orders_products_download');
define('TABLE_ORDERS_REMARKS_HISTORY', 'orders_remarks_history');
define('TABLE_ORDERS_STATUS', 'orders_status');
define('TABLE_ORDERS_STATUS_HISTORY', 'orders_status_history');
define('TABLE_ORDERS_TOTAL', 'orders_total');
define('TABLE_PAYMENT_EXTRA_INFO', 'payment_extra_info');
define('TABLE_PAYMENT_HISTORY', 'payment_history');
define('TABLE_PAYMENT_METHODS_TYPES', 'payment_methods_types');
define('TABLE_PRICE_GROUPS', 'price_groups');
define('TABLE_PRICE_GROUPS_TO_CATEGORIES', 'price_groups_to_categories');
define('TABLE_PRICE_TAGS', 'price_tags');
define('TABLE_PRODUCTS_BUNDLES', 'products_bundles');
define('TABLE_PRODUCTS', 'products');
define('TABLE_PRODUCTS_ATTRIBUTES', 'products_attributes');
define('TABLE_PRODUCTS_ATTRIBUTES_DOWNLOAD', 'products_attributes_download');
define('TABLE_PRODUCTS_DESCRIPTION', 'products_description');
define('TABLE_PRODUCTS_GROUPS', 'products_groups'); //Separate price per customer mod
define('TABLE_PRODUCTS_NOTIFICATIONS', 'products_notifications');
define('TABLE_PRODUCTS_OPTIONS', 'products_options');
define('TABLE_PRODUCTS_OPTIONS_VALUES', 'products_options_values');
define('TABLE_PRODUCTS_OPTIONS_VALUES_TO_PRODUCTS_OPTIONS', 'products_options_values_to_products_options');
define('TABLE_PRODUCTS_PURCHASES', 'products_purchases');
define('TABLE_PRODUCTS_PURCHASES_LISTS', 'products_purchases_lists');
define('TABLE_PRODUCTS_TO_CATEGORIES', 'products_to_categories');
define('TABLE_PROMOTIONS', 'promotions');
define('TABLE_REVIEWS', 'reviews');
define('TABLE_REVIEWS_DESCRIPTION', 'reviews_description');
define('TABLE_SEARCH_CRITERIA', 'search_criteria');
define('TABLE_SESSIONS', 'sessions');
define('TABLE_SPECIALS', 'specials');
define('TABLE_SUPPLIER_LOGIN_HISTORY', 'supplier_login_history');
define('TABLE_TAX_CLASS', 'tax_class');
define('TABLE_TAX_RATES', 'tax_rates');
define('TABLE_GEO_ZONES', 'geo_zones');
define('TABLE_ZONES_TO_GEO_ZONES', 'zones_to_geo_zones');
define('TABLE_WHOS_ONLINE', 'whos_online');
define('TABLE_ZONES', 'zones');
define('TABLE_PAYPAL','paypal');
define('TABLE_PAYPALIPN_TXN', 'paypalipn_txn'); // PAYPALIPN
define('TABLE_PRODUCTS_XSELL', 'products_xsell');
define('TABLE_SUPPLIER_TASKS_ALLOCATION_HISTORY', 'supplier_tasks_allocation_history');
define('TABLE_SUPPLIER_CP_PAYMENTS_PRODUCTS', 'supplier_cp_payments_products');
define('TABLE_SUPPLIER_CP_PAYMENTS', 'supplier_cp_payments');
define('TABLE_SUPPLIER_CP_PAYMENTS_HISTORY', 'supplier_cp_payments_history');
define('TABLE_ORDERS_TAG', 'orders_tag');
define('TABLE_DEFINED_IP_ZONES', 'defined_ip_zones');
define('TABLE_IP_TO_DEFINED_IP_ZONES', 'ip_to_defined_ip_zones');
 
// CGDiscountSpecials
define('TABLE_CUSTOMERS_GROUPS', 'customers_groups');

// Custom Product
define('TABLE_BRACKETS_GROUPS', 'brackets_groups');
define('TABLE_DATA_POOL', 'data_pool');
define('TABLE_DATA_POOL_LEVEL', 'data_pool_level');
define('TABLE_DATA_POOL_REF', 'data_pool_ref');
define('TABLE_DATA_POOL_TEMPLATE', 'data_pool_template');
define('TABLE_DATA_POOL_LEVEL_TAGS', 'data_pool_level_tags');
define('TABLE_BRACKETS_TAGS', 'brackets_tags');
define('TABLE_BRACKETS', 'brackets');
define('TABLE_BRACKETS_GROUPS_TO_LEVEL_TAGS', 'brackets_groups_to_level_tags');
define('TABLE_CUSTOM_PRODUCTS_TYPE', 'custom_products_type');
define('TABLE_POOL_TEMPLATE_T0_CATEGORIES', 'data_pool_template_to_categories');
define('TABLE_ORDERS_CUSTOM_PRODUCTS', 'orders_custom_products');
define('TABLE_SUPPLIER_TASKS_ALLOCATION', 'supplier_tasks_allocation');
define('TABLE_SUPPLIER_TASKS_SETTING', 'supplier_tasks_setting');
define('TABLE_SUPPLIER_TASKS_STATUS', 'supplier_tasks_status');

// Buyback
define('TABLE_BUYBACK_BASKET', 'buyback_basket');
define('TABLE_BUYBACK_GROUPS', 'buyback_groups');
define('TABLE_BUYBACK_REQUEST', 'buyback_request');
define('TABLE_BUYBACK_REQUEST_GROUP', 'buyback_request_group');
define('TABLE_BUYBACK_STATUS', 'buyback_status');

// Supplier
define('TABLE_CUSTOMERS', 'customers');
define('TABLE_PAYMENT_FEES', 'payment_fees');
define('TABLE_PAYMENT_METHODS', 'payment_methods');
define('TABLE_PAYMENT_METHODS_FIELDS', 'payment_methods_fields');
define('TABLE_RESTOCK_CHARACTER_INFO', 'restock_character_info');
define('TABLE_RESTOCK_CHARACTER_SETS', 'restock_character_sets');
define('TABLE_STATUS_CONFIGURATION', 'status_configuration');
define('TABLE_STORE_ACCOUNT_BALANCE', 'store_account_balance');
define('TABLE_STORE_ACCOUNT_COMMENTS', 'store_account_comments');
define('TABLE_STORE_ACCOUNT_HISTORY', 'store_account_history');
define('TABLE_STORE_PAYMENT_ACCOUNT_BOOK', 'store_payment_account_book');
define('TABLE_STORE_PAYMENT_ACCOUNT_BOOK_DETAILS', 'store_payment_account_book_details');
define('TABLE_STORE_PAYMENTS', 'store_payments');
define('TABLE_STORE_PAYMENTS_DETAILS', 'store_payments_details');
define('TABLE_STORE_PAYMENTS_HISTORY', 'store_payments_history');
define('TABLE_STORE_PAYMENTS_STATUS', 'store_payments_status');
define('TABLE_SUPPLIER', 'supplier');
define('TABLE_SUPPLIER_GROUPS', 'supplier_groups');
define('TABLE_SUPPLIER_HISTORY', 'supplier_history');
define('TABLE_SUPPLIER_HISTORY_GROUP', 'supplier_history_group');
define('TABLE_SUPPLIER_LANGUAGES', 'supplier_languages');
define('TABLE_SUPPLIER_LIST_TIME_SETTING', 'supplier_list_time_setting');
define('TABLE_SUPPLIER_LIST_STATUS', 'supplier_list_status');
define('TABLE_SUPPLIER_ORDER_LISTS', 'supplier_order_lists');
define('TABLE_SUPPLIER_ORDER_LISTS_HISTORY', 'supplier_order_lists_history');
define('TABLE_SUPPLIER_ORDER_LISTS_PRODUCTS', 'supplier_order_lists_products');
define('TABLE_SUPPLIER_PAYMENTS', 'supplier_payments');
define('TABLE_SUPPLIER_PAYMENTS_HISTORY', 'supplier_payments_history');
define('TABLE_SUPPLIER_PAYMENTS_ORDERS', 'supplier_payments_orders');
define('TABLE_SUPPLIER_PAYMENTS_STATUS', 'supplier_payments_status');
define('TABLE_SUPPLIER_PREFERENCES', 'supplier_preferences');
define('TABLE_SUPPLIER_PRICING', 'supplier_pricing');
define('TABLE_SUPPLIER_PRICING_SETTING', 'supplier_pricing_setting');
define('TABLE_SUPPLIER_PURCHASE_MODES', 'supplier_purchase_modes');

// Supplier Crew
define('TABLE_SUPPLIER_CREW', 'supplier_crew');
define('TABLE_SUPPLIER_CREW_MAC_ADDRESS', 'supplier_crew_mac_address');

// Mail and Trade Log
define('TABLE_GAME_ITEM_INFO', 'game_item_info');
define('TABLE_GAME_CHAR_LOG', 'game_char_log');

//UI
define('TABLE_GAME_CHAR', 'game_char');
define('TABLE_GAME_CHAR_HISTORY', 'game_char_history');
define('TABLE_CHAR_ITEM_HISTORY', 'char_item_history');
define('TABLE_CHAR_SKILL_HISTORY', 'char_skill_history');
define('TABLE_CHAR_QUEST_HISTORY', 'char_quest_history');
define('TABLE_CHAR_HONOR_HISTORY', 'char_honor_history');
define('TABLE_CHAR_ITEM_STORAGE', 'char_item_storage');
define('TABLE_CHAR_REPUTATION_HISTORY', 'char_reputation_history');
define('TABLE_CHAR_REPUTATION_DETAIL', 'char_reputation_detail');
define('TABLE_CHAR_PET', 'char_pet');
define('TABLE_CHAR_PET_HISTORY', 'char_pet_history');
?>