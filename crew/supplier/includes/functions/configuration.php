<?
/*
  	$Id: configuration.php,v 1.4 2007/01/26 08:17:27 chan Exp $
	
  	Developer: <PERSON> (c) 2006 SKC Venture
	
  	Released under the GNU General Public License
*/

function tep_get_cfg_setting($id, $id_type='catalog', $cfg_key='', $key_type='configuration_key') {
	$cat_cfg_array = array();
	$cid = $id;
	
	if ($id_type == 'product') 	$cid = tep_get_actual_product_cat_id($id);
	
	$cat_path = tep_get_particular_cat_path($cid);
	
	if (tep_not_null($cfg_key)) {
		$cat_path_array = explode('_', $cat_path);
		$cat_path_array = array_merge(array(0), $cat_path_array);
		
		if ($key_type == 'configuration_key') {
			for ($i=count($cat_path_array)-1; $i >= 0; $i--) {
				$cfg_value_select_sql = "	SELECT categories_configuration_value AS cfgValue 
											FROM " . TABLE_CATEGORIES_CONFIGURATION . " 
											WHERE categories_id ='" . $cat_path_array[$i] . "' 
												AND categories_configuration_key = '" . tep_db_input($cfg_key) . "'; ";
				$cfg_value_result_sql = tep_db_query($cfg_value_select_sql);
				if ($cfg_value_row = tep_db_fetch_array($cfg_value_result_sql)) {
					$cat_cfg_array[$cfg_key] = $cfg_value_row['cfgValue'];
					break;
				}
			}
		} else if ($key_type == 'group_id') {
			for ($i=0; $i < count($cat_path_array); $i++) {
				$cfg_value_select_sql = "	SELECT categories_configuration_key AS cfgKey, categories_configuration_value AS cfgValue 
											FROM " . TABLE_CATEGORIES_CONFIGURATION . " 
											WHERE categories_id ='" . $cat_path_array[$i] . "' 
												AND categories_configuration_group_id = '" . tep_db_input($cfg_key) . "'";
				$cfg_value_result_sql = tep_db_query($cfg_value_select_sql);
				while ($cfg_value_row = tep_db_fetch_array($cfg_value_result_sql)) {
					$cat_cfg_array[$cfg_value_row['cfgKey']] = $cfg_value_row['cfgValue'];
				}
			}
		}
	} else {
		$cat_cfg_select_sql = "	SELECT categories_configuration_key AS cfgKey, categories_configuration_value AS cfgValue 
								FROM " . TABLE_CATEGORIES_CONFIGURATION . " 
								WHERE categories_id=0; ";
		$cat_cfg_result_sql = tep_db_query($cat_cfg_select_sql);
		while ($cat_cfg_row = tep_db_fetch_array($cat_cfg_result_sql)) {
			$cat_cfg_array[$cat_cfg_row['cfgKey']] = $cat_cfg_row['cfgValue'];
		}
		
		if (tep_not_null($cat_path)) {
			$cat_path_array = explode('_', $cat_path);
			for ($i=0; $i < count($cat_path_array); $i++) {
				$cfg_value_select_sql = "	SELECT categories_configuration_key AS cfgKey, categories_configuration_value AS cfgValue 
											FROM " . TABLE_CATEGORIES_CONFIGURATION . " 
											WHERE categories_id ='" . $cat_path_array[$i] . "' ";
				$cfg_value_result_sql = tep_db_query($cfg_value_select_sql);
				while ($cfg_value_row = tep_db_fetch_array($cfg_value_result_sql)) {
					$cat_cfg_array[$cfg_value_row['cfgKey']] = $cfg_value_row['cfgValue'];
				}
			}
		}
	}
	
	return $cat_cfg_array;
}

// Transaction status configuration related functions
function tep_status_update_notification($trans_type, $trans_id, $user_email_address, $from_status, $to_status, $mode='M', $comments='') {
	global $languages_id, $currencies;
	$readable_trans_id = '';
	
	$notification_field = $mode == 'M' ? 'status_configuration_manual_notification' : 'status_configuration_auto_notification';
	
	$notification_select_sql = "SELECT " . $notification_field . "
								FROM " . TABLE_STATUS_CONFIGURATION . " 
								WHERE status_configuration_trans_type = '".tep_db_input($trans_type)."' 
									AND status_configuration_source_status_id = '".tep_db_input($from_status)."' 
									AND status_configuration_destination_status_id = '".tep_db_input($to_status)."'";
	$notification_result_sql = tep_db_query($notification_select_sql);
	
	if ($notification_row = tep_db_fetch_array($notification_result_sql)) {
		$notification_email_subject = '';
		
		$email_to_array = tep_parse_email_string($notification_row[$notification_field]);
		
		if (count($email_to_array)) {
			if ($trans_type == 'S') {
				$notification_email_subject = EMAIL_SUPPLIER_ORDER_UPDATE_NOTIFICATION_SUBJECT;
				
				$sup_order_status_select_sql = "SELECT supplier_list_status_id, supplier_list_status_name FROM " . TABLE_SUPPLIER_LIST_STATUS . " WHERE language_id='" . (int)$languages_id  . "' ORDER BY supplier_list_status_sort_order";
				$sup_order_status_result_sql = tep_db_query($sup_order_status_select_sql);
				while ($sup_order_status_row = tep_db_fetch_array($sup_order_status_result_sql)) {
					$orders_status_array[$sup_order_status_row["supplier_list_status_id"]] = $sup_order_status_row["supplier_list_status_name"];
				}
				
				$order_info_select_sql = "	SELECT supplier_order_lists_date, products_purchases_lists_name, currency, currency_value
											FROM " . TABLE_SUPPLIER_ORDER_LISTS  . " 
											WHERE supplier_order_lists_id = '" . tep_db_input($trans_id) . "'";
				$order_info_result_sql = tep_db_query($order_info_select_sql);
				$order_info_row = tep_db_fetch_array($order_info_result_sql);
				
				$payable_amount = order::get_order_total_payable_amount($trans_id);
				$payable_amount = $currencies->format($payable_amount, true, $order_info_row['currency'], $order_info_row['currency_value']);
				
				$trans_status_update_notification_email = sprintf(EMAIL_SUPPLIER_ORDER_UPDATE_NOTIFICATION_CONTENT, $trans_id, $order_info_row['supplier_order_lists_date'], $payable_amount, $order_info_row['products_purchases_lists_name'], ($mode == 'M' ? EMAIL_TEXT_TRANS_UPDATE_MANUAL_NOTIFICATION : EMAIL_TEXT_TRANS_UPDATE_AUTO_NOTIFICATION), $orders_status_array[$from_status], $orders_status_array[$to_status], date("Y-m-d H:i:s"), getenv("REMOTE_ADDR"), $user_email_address, $comments);
			} else if ($trans_type == 'PWL') {
				$notification_email_subject = EMAIL_PWL_ORDER_UPDATE_NOTIFICATION_SUBJECT;
				
				$pwl_order_status_select_sql = "SELECT supplier_tasks_status_id, supplier_tasks_status_name FROM " . TABLE_SUPPLIER_TASKS_STATUS . " WHERE language_id='" . (int)$languages_id  . "' ORDER BY supplier_tasks_status_sort_order";
				$pwl_order_status_result_sql = tep_db_query($pwl_order_status_select_sql);
				while ($pwl_order_status_row = tep_db_fetch_array($pwl_order_status_result_sql)) {
					$orders_status_array[$pwl_order_status_row["supplier_tasks_status_id"]] = $pwl_order_status_row["supplier_tasks_status_name"];
				}
				
				$orders_products_select_sql = "	SELECT IF(op.orders_products_store_price IS NULL, (((op.final_price * sta.supplier_tasks_allocation_ratio_adjust) / 100) + sta.supplier_payable_adjust), (((op.orders_products_store_price * sta.supplier_tasks_allocation_ratio_adjust) / 100) + sta.supplier_payable_adjust)) AS payable_price, 
													ocp.orders_custom_products_number, o.orders_id, o.date_purchased, op.products_name, op.products_id, o.currency, o.currency_value 
												FROM " . TABLE_ORDERS . " AS o 
												INNER JOIN " . TABLE_ORDERS_PRODUCTS . " AS op 
													ON (o.orders_id=op.orders_id AND op.custom_products_type_id = 1) 
												INNER JOIN " . TABLE_ORDERS_CUSTOM_PRODUCTS . " AS ocp 
													ON (op.orders_products_id=ocp.orders_products_id AND ocp.orders_custom_products_key='power_leveling_info') 
												INNER JOIN " . TABLE_SUPPLIER_TASKS_ALLOCATION . " AS sta 
													ON (op.orders_products_id=sta.orders_products_id) 
												WHERE sta.orders_products_id='" . (int)$trans_id . "'";
				$orders_products_result_sql = tep_db_query($orders_products_select_sql);
				$orders_products_row = tep_db_fetch_array($orders_products_result_sql);
				
				$readable_trans_id = $orders_products_row['orders_id'] . '-' . $orders_products_row['orders_custom_products_number'];
				$payable_amount = $currencies->format($orders_products_row['payable_price'], true, $orders_products_row['currency'], $orders_products_row['currency_value']);
				
				$trans_status_update_notification_email = sprintf(EMAIL_PWL_ORDER_UPDATE_NOTIFICATION_CONTENT, $readable_trans_id, $orders_products_row['date_purchased'], $payable_amount, ($mode == 'M' ? EMAIL_TEXT_TRANS_UPDATE_MANUAL_NOTIFICATION : EMAIL_TEXT_TRANS_UPDATE_AUTO_NOTIFICATION), $orders_status_array[$from_status], $orders_status_array[$to_status], date("Y-m-d H:i:s"), getenv("REMOTE_ADDR"), $user_email_address, $comments);
			}
		}
		
		if (tep_not_null($notification_email_subject)) {
			for ($email_to_cnt=0; $email_to_cnt < count($email_to_array); $email_to_cnt++) {
				tep_mail($email_to_array[$email_to_cnt]['name'], $email_to_array[$email_to_cnt]['email'], implode(' ', array(EMAIL_SUBJECT_PREFIX, sprintf($notification_email_subject, (tep_not_null($readable_trans_id) ? $readable_trans_id : $trans_id) ))), $trans_status_update_notification_email, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
			}
		}
	}
}
?>