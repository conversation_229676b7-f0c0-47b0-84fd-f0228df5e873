<?
/*
  	$Id: connection_function.php,v 1.1 2006/02/09 07:13:27 weichen Exp $
	
  	Developer: <PERSON> (c) 2005 SKC Venture
	
  	Released under the GNU General Public License
*/

function tep_validate_connection_call($context) {
	if (!tep_not_null($context)) return false;
	
	$pattern_array = Array(	"/a href[\s]*=[\s]*[\"\']([a-z0-9=:;&_\/ ?.-]+)[\"\']/i", 
							"/href[\s]*=[\s]*[\"\']([a-z0-9=:;&_\/ ?.-]+)[\"\']/i",
				 			"/src[\s]*=[\s]*[\"\']([a-z0-9=:;&_\/ ?.-]+)[\"\']/i"
				 		);
	
	$good_condition = true;
	
	foreach ($pattern_array as $pattern) {
		while (preg_match($pattern, $context, $regs)) {
			$url_info = parse_url($regs[1]);
			
			if ($url_info["scheme"] == "http" || $url_info["scheme"] == "https") {
				tep_set_time_limit(20);
				$fp = @fsockopen($url_info["host"], 80);
				if (!$fp) {
					$good_condition = false;
					break 2;
				} else {
					if (@file_get_contents($regs[1])) {
						;
					} else {
						$good_condition = false;
						fclose($fp);
						break 2;
					}
				    fclose($fp);
				}
			}
			
			$context = str_replace("$regs[1]", "##template##".$regs[1], $context) ;
		}
	}
	
	return $good_condition;
}

?>