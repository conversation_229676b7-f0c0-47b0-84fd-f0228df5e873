<?

/*
  $Id: general.php,v 1.25 2011/01/19 04:17:59 weesiong Exp $

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2003 osCommerce
  Released under the GNU General Public License
 */

function tep_get_categories($categories_array = '', $parent_id = '0', $indent = '', $showsubcat = false) {
    global $languages_id;

    if (!is_array($categories_array))
        $categories_array = array();

    $categories_query = tep_db_query("select c.categories_id, cd.categories_name from " . TABLE_CATEGORIES . " c, " . TABLE_CATEGORIES_DESCRIPTION . " cd where parent_id = '" . (int) $parent_id . "' and c.categories_id = cd.categories_id and cd.language_id = '" . (int) $languages_id . "' order by sort_order, cd.categories_name");
    while ($categories = tep_db_fetch_array($categories_query)) {
        $categories_array[] = array('id' => $categories['categories_id'],
            'text' => $indent . $categories['categories_name']);

        if ($categories['categories_id'] != $parent_id) {
            $categories_array = tep_get_categories($categories_array, $categories['categories_id'], $indent . '__');
        }
    }

    return $categories_array;
}

function tep_get_subcategories(&$subcategories_array, $parent_id = 0) {
    $subcategories_query = tep_db_query("select categories_id from " . TABLE_CATEGORIES . " where parent_id = '" . (int) $parent_id . "'");
    while ($subcategories = tep_db_fetch_array($subcategories_query)) {
        $subcategories_array[sizeof($subcategories_array)] = $subcategories['categories_id'];
        if ($subcategories['categories_id'] != $parent_id) {
            tep_get_subcategories($subcategories_array, $subcategories['categories_id']);
        }
    }
}

//Admin begin
////
//Check login and file access
function tep_admin_check_login() {
    global $PHP_SELF, $supplier_groups_id, $supplier_id;
    if (!tep_session_is_registered('supplier_id')) {
        tep_redirect(tep_href_link(FILENAME_LOGIN, '', 'SSL'));
    } else {
        $supplier_group_select_sql = "SELECT supplier_groups_id FROM " . TABLE_SUPPLIER . " WHERE supplier_id = '" . tep_db_input($supplier_id) . "'";
        $supplier_group_result_sql = tep_db_query($supplier_group_select_sql);
        $supplier_group_row = tep_db_fetch_array($supplier_group_result_sql);

        if ($supplier_groups_id != $supplier_group_row["supplier_groups_id"]) { // The supplier group been changed
            $supplier_groups_id = $supplier_group_row["supplier_groups_id"];
        }
    }
}

////
//Return 'true' or 'false' value to display boxes and files in index.php and column_left.php
function tep_admin_check_boxes($filename, $boxes = '') {
    global $supplier_groups_id;

    $is_boxes = 1;
    if ($boxes == 'sub_boxes') {
        $is_boxes = 0;
    }
    $dbquery = tep_db_query("select admin_files_id from " . TABLE_ADMIN_FILES . " where FIND_IN_SET( '" . $supplier_groups_id . "', admin_groups_id) and admin_files_is_boxes = '" . $is_boxes . "' and admin_files_name = '" . $filename . "'");

    $return_value = false;
    if (tep_db_num_rows($dbquery)) {
        $return_value = true;
    }
    return $return_value;
}

////
//Return files stored in box that can be accessed by user
function tep_admin_files_boxes($filename, $sub_box_name, $parameters = '') {
    global $supplier_groups_id;
    $sub_boxes = '';

    $dbquery = tep_db_query("select admin_files_name from " . TABLE_ADMIN_FILES . " where FIND_IN_SET( '" . $supplier_groups_id . "', admin_groups_id) and admin_files_is_boxes = '0' and admin_files_name = '" . $filename . "'");
    if (tep_db_num_rows($dbquery)) {
        $sub_boxes = '<a href="' . tep_href_link($filename, $parameters) . '" class="menuBoxContentLink">' . $sub_box_name . '</a><br>';
    }
    return $sub_boxes;
}

////
//Return files stored in box that can be accessed by user
function tep_admin_check_files($files_array) {
    $allowed_files = array();

    for ($i = 0; $i < count($files_array); $i++) {
        if (tep_admin_check_boxes($files_array[$i], 'sub_boxes')) {
            $allowed_files[] = $files_array[$i];
        }
    }

    return $allowed_files;
}

////
//Check whether a particular action of a file can be accessed by user
function tep_admin_files_actions($filename, $action_name) {
    global $supplier_groups_id;

    $files_actions_select_sql = "SELECT afa.admin_files_actions_id FROM " . TABLE_ADMIN_FILES_ACTIONS . " AS afa INNER JOIN " . TABLE_ADMIN_FILES . " AS af ON afa.admin_files_id=af.admin_files_id WHERE af.admin_files_name='" . $filename . "' AND af.admin_files_is_boxes=0 AND afa.admin_files_actions_key='" . $action_name . "' AND FIND_IN_SET( '" . $supplier_groups_id . "', afa.admin_groups_id)";
    $files_actions_result_sql = tep_db_query($files_actions_select_sql);
    if (tep_db_num_rows($files_actions_result_sql)) {
        return true;
    }
    return false;
}

////
//Get selected file for index.php
function tep_selected_file($filename) {
    global $supplier_groups_id;
    $randomize = FILENAME_ADMIN_ACCOUNT;

    $dbquery = tep_db_query("select admin_files_id as boxes_id from " . TABLE_ADMIN_FILES . " where FIND_IN_SET( '" . $supplier_groups_id . "', admin_groups_id) and admin_files_is_boxes = '1' and admin_files_name = '" . $filename . "'");
    if (tep_db_num_rows($dbquery)) {
        $boxes_id = tep_db_fetch_array($dbquery);
        $randomize_query = tep_db_query("select admin_files_name from " . TABLE_ADMIN_FILES . " where FIND_IN_SET( '" . $supplier_groups_id . "', admin_groups_id) and admin_files_is_boxes = '0' and admin_files_to_boxes = '" . $boxes_id['boxes_id'] . "'");
        if (tep_db_num_rows($randomize_query)) {
            $file_selected = tep_db_fetch_array($randomize_query);
            $randomize = $file_selected['admin_files_name'];
        }
    }
    return $randomize;
}

//Admin end
////
// Redirect to another page or site
function tep_redirect($url) {
    global $logger;

    header('Location: ' . $url);

    if (STORE_PAGE_PARSE_TIME == 'true') {
        if (!is_object($logger))
            $logger = new logger;
        $logger->timer_stop();
    }
    exit;
}

////
// Parse the data used in the html tags to ensure the tags will not break
function tep_parse_input_field_data($data, $parse) {
    return strtr(trim($data), $parse);
}

function tep_output_string($string, $translate = false, $protected = false) {
    if ($protected == true) {
        return htmlspecialchars($string);
    } else {
        if ($translate == false) {
            return tep_parse_input_field_data($string, array('"' => '&quot;'));
        } else {
            return tep_parse_input_field_data($string, $translate);
        }
    }
}

function tep_output_string_protected($string) {
    return tep_output_string($string, false, true);
}

function tep_sanitize_string($string) {
    $string = ereg_replace_dep(' +', ' ', $string);
    return preg_replace("/[<>]/", '_', $string);
}

function tep_get_path($current_category_id = '', $value_pair = true) {
    global $cPath_array;

    if ($current_category_id == '') {
        $path_array = is_array($cPath_array[0]) ? $cPath_array[0] : $cPath_array;
        if (is_array($path_array)) {
            $cPath_new = implode('_', array_reverse($path_array));
        }
    } else {
        if (sizeof($cPath_array) == 0) {
            $path_array = array($current_category_id);
            $current_category_query = tep_db_query("select parent_id from " . TABLE_CATEGORIES . " where categories_id = '" . (int) $current_category_id . "' AND parent_id<>0");
            while ($current_category = tep_db_fetch_array($current_category_query)) {
                $path_array[] = $current_category["parent_id"];
                $current_category_query = tep_db_query("select parent_id from " . TABLE_CATEGORIES . " where categories_id = '" . (int) $current_category["parent_id"] . "' AND parent_id<>0");
            }
            $cPath_new = implode('_', array_reverse($path_array));
        } else {
            $cPath_new = '';
            $last_category_query = tep_db_query("select parent_id from " . TABLE_CATEGORIES . " where categories_id = '" . (int) $cPath_array[(sizeof($cPath_array) - 1)] . "'");
            $last_category = tep_db_fetch_array($last_category_query);

            $current_category_query = tep_db_query("select parent_id from " . TABLE_CATEGORIES . " where categories_id = '" . (int) $current_category_id . "'");
            $current_category = tep_db_fetch_array($current_category_query);

            if ($last_category['parent_id'] == $current_category['parent_id']) {
                for ($i = 0, $n = sizeof($cPath_array) - 1; $i < $n; $i++) {
                    $cPath_new .= '_' . $cPath_array[$i];
                }
            } else {
                for ($i = 0, $n = sizeof($cPath_array); $i < $n; $i++) {
                    $cPath_new .= '_' . $cPath_array[$i];
                }
            }

            $cPath_new .= '_' . $current_category_id;
            if (substr($cPath_new, 0, 1) == '_') {
                $cPath_new = substr($cPath_new, 1);
            }
        }
    }
    if ($value_pair)
        return 'cPath=' . $cPath_new;
    else
        return $cPath_new;
}

function tep_get_product_cPath($products_id, $show_all = false) {
    $cPath = '';

    if ($show_all)
        $category_query = tep_db_query("select p2c.categories_id from " . TABLE_PRODUCTS . " p, " . TABLE_PRODUCTS_TO_CATEGORIES . " p2c where p.products_id = '" . (int) $products_id . "' and p.products_id = p2c.products_id limit 1");
    else
        $category_query = tep_db_query("select p2c.categories_id from " . TABLE_PRODUCTS . " p, " . TABLE_PRODUCTS_TO_CATEGORIES . " p2c where p.products_id = '" . (int) $products_id . "' and p.products_status = '1' and p.products_id = p2c.products_id limit 1");

    if (tep_db_num_rows($category_query)) {
        $category = tep_db_fetch_array($category_query);

        $categories = array();
        tep_get_parent_categories($categories, $category['categories_id']);

        $categories = array_reverse($categories);

        $cPath = implode('_', $categories);

        if (tep_not_null($cPath))
            $cPath .= '_';
        $cPath .= $category['categories_id'];
    }
    return $cPath;
}

////
// Recursively go through the categories and retreive all parent categories IDs
// TABLES: categories
function tep_get_parent_categories(&$categories, $categories_id) {
    $parent_categories_query = tep_db_query("select parent_id from " . TABLE_CATEGORIES . " where categories_id = '" . (int) $categories_id . "'");
    while ($parent_categories = tep_db_fetch_array($parent_categories_query)) {
        if ($parent_categories['parent_id'] == 0)
            return true;
        $categories[sizeof($categories)] = $parent_categories['parent_id'];
        if ($parent_categories['parent_id'] != $categories_id) {
            tep_get_parent_categories($categories, $parent_categories['parent_id']);
        }
    }
}

function tep_get_all_get_params($exclude_array = '') {
    global $HTTP_GET_VARS;

    if ($exclude_array == '')
        $exclude_array = array();

    $get_url = '';

    reset($HTTP_GET_VARS);
    while (list($key, $value) = each($HTTP_GET_VARS)) {
        if (($key != tep_session_name()) && ($key != 'error') && (!in_array($key, $exclude_array)))
            $get_url .= $key . '=' . $value . '&';
    }

    return $get_url;
}

function tep_date_long($raw_date) {
    if (($raw_date == '0000-00-00 00:00:00') || ($raw_date == ''))
        return false;

    $year = (int) substr($raw_date, 0, 4);
    $month = (int) substr($raw_date, 5, 2);
    $day = (int) substr($raw_date, 8, 2);
    $hour = (int) substr($raw_date, 11, 2);
    $minute = (int) substr($raw_date, 14, 2);
    $second = (int) substr($raw_date, 17, 2);

    return strftime(DATE_FORMAT_LONG, mktime($hour, $minute, $second, $month, $day, $year));
}

////
// Output a raw date string in the selected locale date format
// $raw_date needs to be in this format: YYYY-MM-DD HH:MM:SS
// NOTE: Includes a workaround for dates before 01/01/1970 that fail on windows servers
function tep_date_short($raw_date) {
    if (($raw_date == '0000-00-00 00:00:00') || ($raw_date == ''))
        return false;

    $year = substr($raw_date, 0, 4);
    $month = (int) substr($raw_date, 5, 2);
    $day = (int) substr($raw_date, 8, 2);
    $hour = (int) substr($raw_date, 11, 2);
    $minute = (int) substr($raw_date, 14, 2);
    $second = (int) substr($raw_date, 17, 2);

    if (@date('Y', mktime($hour, $minute, $second, $month, $day, $year)) == $year) {
        return date(DATE_FORMAT, mktime($hour, $minute, $second, $month, $day, $year));
    } else {
        return ereg_replace_dep('2037' . '$', $year, date(DATE_FORMAT, mktime($hour, $minute, $second, $month, $day, 2037)));
    }
}

function tep_datetime_short($raw_datetime) {
    if (($raw_datetime == '0000-00-00 00:00:00') || ($raw_datetime == ''))
        return false;

    $year = (int) substr($raw_datetime, 0, 4);
    $month = (int) substr($raw_datetime, 5, 2);
    $day = (int) substr($raw_datetime, 8, 2);
    $hour = (int) substr($raw_datetime, 11, 2);
    $minute = (int) substr($raw_datetime, 14, 2);
    $second = (int) substr($raw_datetime, 17, 2);

    return strftime(DATE_TIME_FORMAT, mktime($hour, $minute, $second, $month, $day, $year));
}

function tep_date_delta($ts_start_date, $ts_end_date) {
    /*     * ****************************************************************
      Both date parameter is an array in the form:
      (hour, minute, second, month, day, year)
     * **************************************************************** */
    $secs_in_day = 86400;

    $i_years = $ts_end_date["Y"] - $ts_start_date["Y"];
    $i_months = $ts_end_date["m"] - $ts_start_date["m"];
    $i_days = $ts_end_date["d"] - $ts_start_date["d"];
    if ($i_days < 0)
        $i_months--;
    if ($i_months < 0) {
        $i_years--;
        $i_months += 12;
    }
    if ($i_days < 0) {
        $i_days = date('t', mktime(0, 0, 0, $ts_start_date["m"], 1, date('Y'))) - $ts_start_date["d"];
        $i_days += $ts_end_date["d"];
    }

    /*
      # calculate HMS delta
      $f_delta = $ts_end_date - $ts_start_date;
      $f_secs = $f_delta % $secs_in_day;
      $f_secs -= ($i_secs = $f_secs % 60);
      $i_mins = intval($f_secs/60)%60;
      $f_secs -= $i_mins * 60;
      $i_hours = intval($f_secs/3600);
     */
    //return array($i_years, $i_months, $i_days, $i_hours, $i_mins, $i_secs);
    return array($i_years, $i_months, $i_days, 0, 0, 0);
}

function tep_calculate_age($s_start_date, $s_end_date = '', $b_show_days = 0, $b_show_time = false) {
    if ($b_show_time == true) {
        $b_show_time = strlen($s_start_date > 10) ? true : false;
    }

    $ts_start_date = array('H' => substr($s_start_date, 11, 2),
        'i' => substr($s_start_date, 14, 2),
        's' => substr($s_start_date, 17, 2),
        'm' => substr($s_start_date, 5, 2),
        'd' => substr($s_start_date, 8, 2),
        'Y' => substr($s_start_date, 0, 4));
    if ($s_end_date) {
        $ts_end_date = array('H' => substr($s_end_date, 11, 2),
            'i' => substr($s_end_date, 14, 2),
            's' => substr($s_end_date, 17, 2),
            'm' => substr($s_end_date, 5, 2),
            'd' => substr($s_end_date, 8, 2),
            'Y' => substr($s_end_date, 0, 4));
    } else {
        $ts_end_date = array('H' => date('H'),
            'i' => date('i'),
            's' => date('s'),
            'm' => date('m'),
            'd' => date('d'),
            'Y' => date('Y'));
    }

    if (checkdate($ts_start_date['m'], $ts_start_date['d'], $ts_start_date['Y']) == false || checkdate($ts_end_date['m'], $ts_end_date['d'], $ts_end_date['Y']) == false) {
        return 'Invalid Date';
    }

    list ($i_age_years, $i_age_months, $i_age_days, $i_age_hours, $i_age_mins, $i_age_secs) = tep_date_delta($ts_start_date, $ts_end_date);

    // output
    $s_age = '';
    if ($i_age_years)
        $s_age .= "$i_age_years yr";
    if ($i_age_months)
        $s_age .= ($s_age ? ', ' : '') . "$i_age_months mth";
    if ($b_show_days && $i_age_days)
        $s_age .= ($s_age ? ', ' : '') . "$i_age_days day" . (abs($i_age_days) > 1 ? 's' : '');

    if ($b_show_time && $i_age_hours)
        $s_age .= ($s_age ? ', ' : '') . "$i_age_hours hour" . (abs($i_age_hours) > 1 ? 's' : '');
    if ($b_show_time && $i_age_mins)
        $s_age .= ($s_age ? ', ' : '') . "$i_age_mins minute" . (abs($i_age_mins) > 1 ? 's' : '');
    if ($b_show_time && $i_age_secs)
        $s_age .= ($s_age ? ', ' : '') . "$i_age_secs second" . (abs($i_age_secs) > 1 ? 's' : '');
    return $s_age;
}

function tep_day_diff($start_date_time, $end_date_time) {
    list($s_date_str, $s_time_str) = explode(' ', $start_date_time);
    list($s_yr, $s_mth, $s_day) = explode('-', $s_date_str);
    list($s_hr, $s_min, $s_sec) = explode(':', $s_time_str);

    list($e_date_str, $e_time_str) = explode(' ', $end_date_time);
    list($e_yr, $e_mth, $e_day) = explode('-', $e_date_str);
    list($e_hr, $e_min, $e_sec) = explode(':', $e_time_str);

    if (checkdate($s_mth, $s_day, $s_yr) == false || checkdate($e_mth, $e_day, $e_yr) == false) {
        return false;
    }

    $start_time_secs = mktime($s_hr, $s_min, $s_sec, $s_mth, $s_day, $s_yr);
    $end_time_secs = mktime($e_hr, $e_min, $e_sec, $e_mth, $e_day, $e_yr);
    $daydiff = $end_time_secs - $start_time_secs;
    if ($daydiff < 0) {
        return false;
    }
    return $daydiff / 86400;
}

function tep_parse_email_string($email_string) {
    $email_array = tep_not_null($email_string) ? explode(',', $email_string) : array();
    $email_pattern = "/([^<]*?)(?:<)([^>]+)(?:>)/is";
    $email_receivers = array();

    for ($receiver_cnt = 0; $receiver_cnt < count($email_array); $receiver_cnt++) {
        if (preg_match($email_pattern, $email_array[$receiver_cnt], $regs)) {
            $receiver_name = trim($regs[1]);
            $receiver_email = trim($regs[2]);

            $email_receivers[] = array('name' => $receiver_name, 'email' => $receiver_email);
        }
    }

    return $email_receivers;
}

function tep_get_email_greeting($firstname, $lastname, $gender = '') {
    if (ACCOUNT_GENDER == 'true') {
        if ($gender == 'm') {
            $email_greeting = sprintf(EMAIL_GREET_MR, $lastname);
        } else if ($gender == 'f') {
            $email_greeting = sprintf(EMAIL_GREET_MS, $lastname);
        } else {
            $email_greeting = sprintf(EMAIL_GREET_NONE, $firstname);
        }
    } else {
        $email_greeting = sprintf(EMAIL_GREET_NONE, $firstname);
    }

    return $email_greeting;
}

function tep_get_category_buyback_tree($parent_id = '0', $spacing = '', $exclude = '', $category_tree_array = '', $include_itself = false, $level = 0) {
    global $languages_id;

    if (!is_array($category_tree_array))
        $category_tree_array = array();
    if ((sizeof($category_tree_array) < 1) && ($exclude != '0'))
        $category_tree_array[] = array('id' => '0', 'text' => TEXT_TOP);

    if ($include_itself) {
        $category_query = tep_db_query("SELECT cd.categories_name FROM " . TABLE_CATEGORIES_DESCRIPTION . " AS cd WHERE cd.language_id = '" . (int) $languages_id . "' AND cd.categories_id = '" . (int) $parent_id . "'");
        $category = tep_db_fetch_array($category_query);
        $category_tree_array[] = array('id' => $parent_id, 'text' => str_repeat($spacing, $level) . strip_tags($category['categories_name']));
        $skip = array($parent_id);
    } else {
        $skip = array();
    }

    tep_get_sub_cat_buyback_tree($parent_id, $category_tree_array, $level, $spacing, $skip, false);

    return $category_tree_array;
}

function tep_get_sub_cat_buyback_tree($category_id, &$cat_id_array, $level, $spacing = '', $skip = '', $by_level = true) {
    global $languages_id;

    if ($by_level) {
        $sub_category_select_sql = "SELECT c.categories_id, cd.categories_name
									FROM " . TABLE_CATEGORIES . " AS c 
									LEFT JOIN " . TABLE_CATEGORIES_DESCRIPTION . " AS cd 
										ON c.categories_id=cd.categories_id 
									WHERE c.parent_id = '" . $category_id . "' AND cd.language_id = '" . (int) $languages_id . "'";
        $sub_category_query = tep_db_query($sub_category_select_sql);

        while ($sub_category = tep_db_fetch_array($sub_category_query)) {
            tep_get_sub_cat_buyback_tree($sub_category["categories_id"], $cat_id_array, $level + 1, $spacing);
            $cat_id_array[$level][] = array("id" => $sub_category["categories_id"], 'text' => strip_tags($sub_category["categories_name"]));
        }
    } else {
        if (is_array($skip) && !in_array($category_id, $skip)) {
            $cat_select_sql = "	SELECT c.categories_id, cd.categories_name
								FROM " . TABLE_CATEGORIES . " AS c 
								LEFT JOIN " . TABLE_CATEGORIES_DESCRIPTION . " AS cd 
									ON c.categories_id=cd.categories_id 
								WHERE c.categories_id = '" . $category_id . "' AND cd.language_id = '" . (int) $languages_id . "'";
            $cat_result_sql = tep_db_query($cat_select_sql);
            if ($cat_row = tep_db_fetch_array($cat_result_sql)) {
                $cat_id_array[] = array("id" => $category_id, 'text' => ($level > 1 ? str_repeat($spacing, $level - 1) . strip_tags($cat_row["categories_name"]) : strip_tags($cat_row["categories_name"])));
            }
        }
        $sub_category_select_sql = "SELECT c.categories_id, cd.categories_name
									FROM " . TABLE_CATEGORIES . " AS c 
									LEFT JOIN " . TABLE_CATEGORIES_DESCRIPTION . " AS cd 
										ON c.categories_id = cd.categories_id 
									WHERE c.parent_id = '" . $category_id . "' AND cd.language_id = '" . (int) $languages_id . "'
									ORDER BY c.sort_order, cd.categories_name";
        $sub_category_result_sql = tep_db_query($sub_category_select_sql);

        while ($sub_category = tep_db_fetch_array($sub_category_result_sql)) {
            tep_get_sub_cat_buyback_tree($sub_category["categories_id"], $cat_id_array, $level + 1, $spacing, $skip, false);
        }
    }
    return true;
}

function tep_get_category_tree($parent_id = '0', $spacing = '', $exclude = '', $category_tree_array = '', $include_itself = false, $level = 0) {
    global $languages_id;

    if (!is_array($category_tree_array))
        $category_tree_array = array();
    if ((sizeof($category_tree_array) < 1) && ($exclude != '0'))
        $category_tree_array[] = array('id' => '0', 'text' => TEXT_TOP);

    if ($include_itself) {
        $category_query = tep_db_query("SELECT cd.categories_name FROM " . TABLE_CATEGORIES_DESCRIPTION . " AS cd WHERE cd.language_id = '" . (int) $languages_id . "' AND cd.categories_id = '" . (int) $parent_id . "'");
        $category = tep_db_fetch_array($category_query);
        $category_tree_array[] = array('id' => $parent_id, 'text' => str_repeat($spacing, $level) . strip_tags($category['categories_name']));
        $skip = array($parent_id);
    } else {
        $skip = array();
    }

    tep_get_sub_cat_tree($parent_id, $category_tree_array, $level, $spacing, $skip, false);

    return $category_tree_array;
}

function tep_draw_products_pull_down($name, $parameters = '', $exclude = '') {
    global $currencies, $languages_id;
    if ($exclude == '') {
        $exclude = array();
    }

    $select_string = '<select name="' . $name . '"';

    if ($parameters) {
        $select_string .= ' ' . $parameters;
    }

    $select_string .= '>';
    //Begin SPC
    $all_groups = array();
    $customers_groups_query = tep_db_query("select distinct customers_group_name, customers_group_id from " . TABLE_CUSTOMERS . " order by customers_group_id ");
    while ($existing_groups = tep_db_fetch_array($customers_groups_query)) {
        $all_groups[$existing_groups['customers_group_id']] = $existing_groups['customers_group_name'];
    }
    //End SPC

    $products_query = tep_db_query("select p.products_id, pd.products_name, p.products_price from " . TABLE_PRODUCTS . " p, " . TABLE_PRODUCTS_DESCRIPTION . " pd where p.products_id = pd.products_id and pd.language_id = '" . (int) $languages_id . "' order by products_name");
    while ($products = tep_db_fetch_array($products_query)) {
        //Begin SPC
        //      if (!in_array($products['products_id'], $exclude)) {
        //        $select_string .= '<option value="' . $products['products_id'] . '">' . $products['products_name'] . ' (' . $currencies->format($products['products_price']) . ')</option>';
        //      }
        if (!in_array($products['products_id'], $exclude)) {
            $price_query = tep_db_query("select customers_group_price, customers_group_id from " . TABLE_PRODUCTS_GROUPS . " where products_id = " . $products['products_id']);
            $product_prices = array();
            while ($prices_array = tep_db_fetch_array($price_query)) {
                $product_prices[$prices_array['customers_group_id']] = $prices_array['customers_group_price'];
            }
            reset($all_groups);
            $price_string = "";
            $sde = 0;
            while (list($sdek, $sdev) = each($all_groups)) {
                if (!in_array((int) $products['products_id'] . ":" . (int) $sdek, $exclude)) {
                    if ($sde)
                        $price_string .= ", ";
                    $price_string .= $sdev . ": " . $currencies->format(isset($product_prices[$sdek]) ? $product_prices[$sdek] : $products['products_price']);
                    $sde = 1;
                }
            }
            $select_string .= '<option value="' . $products['products_id'] . '">' . $products['products_name'] . ' (' . $price_string . ')</option>\n';
        }
        //End SPC
    }
    $select_string .= '</select>';

    return $select_string;
}

function tep_options_name($options_id) {
    global $languages_id;

    $options = tep_db_query("select products_options_name from " . TABLE_PRODUCTS_OPTIONS . " where products_options_id = '" . (int) $options_id . "' and language_id = '" . (int) $languages_id . "'");
    $options_values = tep_db_fetch_array($options);

    return $options_values['products_options_name'];
}

function tep_values_name($values_id) {
    global $languages_id;

    $values = tep_db_query("select products_options_values_name from " . TABLE_PRODUCTS_OPTIONS_VALUES . " where products_options_values_id = '" . (int) $values_id . "' and language_id = '" . (int) $languages_id . "'");
    $values_values = tep_db_fetch_array($values);

    return $values_values['products_options_values_name'];
}

function tep_info_image($image, $alt, $width = '', $height = '') {
    if (tep_not_null($image) && (file_exists(DIR_FS_CATALOG_IMAGES . $image))) {
        $image = tep_image(DIR_WS_CATALOG_IMAGES . $image, $alt, $width, $height);
    } else {
        $image = TEXT_IMAGE_NONEXISTENT;
    }

    return $image;
}

function tep_break_string($string, $len, $break_char = '-') {
    $l = 0;
    $output = '';
    for ($i = 0, $n = strlen($string); $i < $n; $i++) {
        $char = substr($string, $i, 1);
        if ($char != ' ') {
            $l++;
        } else {
            $l = 0;
        }
        if ($l > $len) {
            $l = 1;
            $output .= $break_char;
        }
        $output .= $char;
    }

    return $output;
}

function tep_get_country_name($country_id) {
    $country_query = tep_db_query("select countries_name from " . TABLE_COUNTRIES . " where countries_id = '" . (int) $country_id . "'");

    if (!tep_db_num_rows($country_query)) {
        return $country_id;
    } else {
        $country = tep_db_fetch_array($country_query);
        return $country['countries_name'];
    }
}

function tep_get_zone_name($country_id, $zone_id, $default_zone) {
    $zone_query = tep_db_query("select zone_name from " . TABLE_ZONES . " where zone_country_id = '" . (int) $country_id . "' and zone_id = '" . (int) $zone_id . "'");
    if (tep_db_num_rows($zone_query)) {
        $zone = tep_db_fetch_array($zone_query);
        return $zone['zone_name'];
    } else {
        return $default_zone;
    }
}

function tep_not_null($value) {
    if (is_array($value)) {
        if (sizeof($value) > 0) {
            return true;
        } else {
            return false;
        }
    } else {
        if ((is_string($value) || is_int($value)) && ($value !== '') && ($value !== 'NULL') && (strlen(trim($value)) > 0)) {
            return true;
        } else {
            return false;
        }
    }
}

function tep_browser_detect($component) {
    global $HTTP_USER_AGENT;
    return stristr($HTTP_USER_AGENT, $component);
}

function tep_tax_classes_pull_down($name, $tax_class_id = '', $parameters = '') {
    $tax_class_array = array(array('id' => '0', 'text' => TEXT_NONE));
    $tax_class_query = tep_db_query("select tax_class_id, tax_class_title from " . TABLE_TAX_CLASS . " order by tax_class_title");
    while ($tax_class = tep_db_fetch_array($tax_class_query)) {
        $tax_class_array[] = array('id' => $tax_class['tax_class_id'],
            'text' => $tax_class['tax_class_title']);
    }

    return tep_draw_pull_down_menu($name, $tax_class_array, $tax_class_id, $parameters);
}

function tep_geo_zones_pull_down($name, $zone_class_id = '', $parameters = '') {
    $zone_class_array = array(array('id' => '0', 'text' => TEXT_NONE));
    $zone_class_query = tep_db_query("select geo_zone_id, geo_zone_name from " . TABLE_GEO_ZONES . " order by geo_zone_name");
    while ($zone_class = tep_db_fetch_array($zone_class_query)) {
        $zone_class_array[] = array('id' => $zone_class['geo_zone_id'],
            'text' => $zone_class['geo_zone_name']);
    }

    return tep_draw_pull_down_menu($name, $zone_class_array, $zone_class_id, $parameters);
}

function tep_get_geo_zone_name($geo_zone_id) {
    $zones_query = tep_db_query("select geo_zone_name from " . TABLE_GEO_ZONES . " where geo_zone_id = '" . (int) $geo_zone_id . "'");

    if (!tep_db_num_rows($zones_query)) {
        $geo_zone_name = $geo_zone_id;
    } else {
        $zones = tep_db_fetch_array($zones_query);
        $geo_zone_name = $zones['geo_zone_name'];
    }

    return $geo_zone_name;
}

function tep_address_format($address_format_id, $address, $html, $boln, $eoln, $name_style = '', $display_name = true) {
    $address_format_query = tep_db_query("select address_format as format from " . TABLE_ADDRESS_FORMAT . " where address_format_id = '" . (int) $address_format_id . "'");
    $address_format = tep_db_fetch_array($address_format_query);

    $company = tep_output_string_protected($address['company']);
    if ($display_name) {
        if (isset($address['firstname']) && tep_not_null($address['firstname'])) {
            $firstname = tep_output_string_protected($address['firstname']);
            $lastname = tep_output_string_protected($address['lastname']);
        } elseif (isset($address['name']) && tep_not_null($address['name'])) {
            $firstname = tep_output_string_protected($address['name']);
            $lastname = '';
        } else {
            $firstname = '';
            $lastname = '';
        }
        $firstname = '<span class="' . $name_style . '">' . $firstname . '</span>';
        $lastname = '<span class="' . $name_style . '">' . $lastname . '</span>';
    }

    $street = tep_output_string_protected($address['street_address']);
    $suburb = tep_output_string_protected($address['suburb']);
    $city = tep_output_string_protected($address['city']);
    $state = tep_output_string_protected($address['state']);
    if (isset($address['country_id']) && tep_not_null($address['country_id'])) {
        $country = tep_get_country_name($address['country_id']);
        if (isset($address['zone_id']) && tep_not_null($address['zone_id'])) {
            $state = tep_get_zone_code($address['country_id'], $address['zone_id'], $state);
        }
    } elseif (isset($address['country']) && tep_not_null($address['country'])) {
        $country = tep_output_string_protected($address['country']);
    } else {
        $country = '';
    }
    $postcode = tep_output_string_protected($address['postcode']);
    $zip = $postcode;

    if ($html) {
        // HTML Mode
        $HR = '<hr>';
        $hr = '<hr>';
        if (($boln == '') && ($eoln == "\n")) { // Values not specified, use rational defaults
            $CR = '<br>';
            $cr = '<br>';
            $eoln = $cr;
        } else { // Use values supplied
            $CR = $eoln . $boln;
            $cr = $CR;
        }
    } else {
        // Text Mode
        $CR = $eoln;
        $cr = $CR;
        $HR = '----------------------------------------';
        $hr = '----------------------------------------';
    }

    $statecomma = '';
    $streets = $street;
    if ($suburb != '')
        $streets = $street . $cr . $suburb;
    if ($country == '')
        $country = tep_output_string_protected($address['country']);
    if ($state != '')
        $statecomma = $state . ', ';

    $fmt = $address_format['format'];
    eval("\$address = \"$fmt\";");

    if ((ACCOUNT_COMPANY == 'true') && (tep_not_null($company))) {
        $address = $company . $cr . $address;
    }

    $address = trim($address);

    if (strpos($address, $cr) === 0) {
        $address = substr($address, strlen($cr));
    }
    return $address;
}

////////////////////////////////////////////////////////////////////////////////////////////////
//
// Function    : tep_get_zone_code
//
// Arguments   : country           country code string
//               zone              state/province zone_id
//               def_state         default string if zone==0
//
// Return      : state_prov_code   state/province code
//
// Description : Function to retrieve the state/province code (as in FL for Florida etc)
//
////////////////////////////////////////////////////////////////////////////////////////////////
function tep_get_zone_code($country, $zone, $def_state) {
    $state_prov_query = tep_db_query("select zone_code from " . TABLE_ZONES . " where zone_country_id = '" . (int) $country . "' and zone_id = '" . (int) $zone . "'");
    if (!tep_db_num_rows($state_prov_query)) {
        $state_prov_code = $def_state;
    } else {
        $state_prov_values = tep_db_fetch_array($state_prov_query);
        $state_prov_code = $state_prov_values['zone_code'];
    }

    return $state_prov_code;
}

function tep_get_uprid($prid, $params) {
    $uprid = $prid;
    if ((is_array($params)) && (!strstr($prid, '{'))) {
        while (list($option, $value) = each($params)) {
            $uprid = $uprid . '{' . $option . '}' . $value;
        }
    }

    return $uprid;
}

function tep_get_prid($uprid) {
    $pieces = explode('{', $uprid);
    return $pieces[0];
}

function tep_get_languages() {
    $languages_query = tep_db_query("select languages_id, name, code, image, directory from " . TABLE_LANGUAGES . " order by sort_order");
    while ($languages = tep_db_fetch_array($languages_query)) {
        $languages_array[] = array('id' => $languages['languages_id'],
            'name' => $languages['name'],
            'code' => $languages['code'],
            'image' => $languages['image'],
            'directory' => $languages['directory']);
    }

    return $languages_array;
}

function tep_get_category_name($category_id, $language_id) {
    $category_query = tep_db_query("select categories_name from " . TABLE_CATEGORIES_DESCRIPTION . " where categories_id = '" . (int) $category_id . "' and language_id = '" . (int) $language_id . "'");
    $category = tep_db_fetch_array($category_query);
    return $category['categories_name'];
}

function tep_get_orders_status_name($orders_status_id, $language_id = '') {
    global $languages_id;

    if (!$language_id)
        $language_id = $languages_id;
    $orders_status_query = tep_db_query("select orders_status_name from " . TABLE_ORDERS_STATUS . " where orders_status_id = '" . (int) $orders_status_id . "' and language_id = '" . (int) $language_id . "'");
    $orders_status = tep_db_fetch_array($orders_status_query);

    return $orders_status['orders_status_name'];
}

function tep_get_orders_status() {
    global $languages_id;

    $orders_status_array = array();
    $orders_status_query = tep_db_query("select orders_status_id, orders_status_name from " . TABLE_ORDERS_STATUS . " where language_id = '" . (int) $languages_id . "' order by orders_status_sort_order");
    while ($orders_status = tep_db_fetch_array($orders_status_query)) {
        $orders_status_array[] = array('id' => $orders_status['orders_status_id'],
            'text' => $orders_status['orders_status_name']);
    }

    return $orders_status_array;
}

function tep_get_products_name($product_id, $language_id = 0) {
    global $languages_id;

    if ($language_id == 0)
        $language_id = $languages_id;
    $product_query = tep_db_query("select products_name from " . TABLE_PRODUCTS_DESCRIPTION . " where products_id = '" . (int) $product_id . "' and language_id = '" . (int) $language_id . "'");
    $product = tep_db_fetch_array($product_query);

    return $product['products_name'];
}

function tep_get_products_description($product_id, $language_id) {
    $product_query = tep_db_query("select products_description from " . TABLE_PRODUCTS_DESCRIPTION . " where products_id = '" . (int) $product_id . "' and language_id = '" . (int) $language_id . "'");
    $product = tep_db_fetch_array($product_query);

    return $product['products_description'];
}

function tep_get_products_url($product_id, $language_id) {
    $product_query = tep_db_query("select products_url from " . TABLE_PRODUCTS_DESCRIPTION . " where products_id = '" . (int) $product_id . "' and language_id = '" . (int) $language_id . "'");
    $product = tep_db_fetch_array($product_query);

    return $product['products_url'];
}

function tep_get_products_location($product_id, $language_id) {
    $product_query = tep_db_query("select products_location from " . TABLE_PRODUCTS_DESCRIPTION . " where products_id = '" . (int) $product_id . "' and language_id = '" . (int) $language_id . "'");
    $product = tep_db_fetch_array($product_query);

    return $product['products_location'];
}

////
// Return the manufacturers URL in the needed language
// TABLES: manufacturers_info
function tep_get_manufacturer_url($manufacturer_id, $language_id) {
    $manufacturer_query = tep_db_query("select manufacturers_url from " . TABLE_MANUFACTURERS_INFO . " where manufacturers_id = '" . (int) $manufacturer_id . "' and languages_id = '" . (int) $language_id . "'");
    $manufacturer = tep_db_fetch_array($manufacturer_query);

    return $manufacturer['manufacturers_url'];
}

////
// Wrapper for class_exists() function
// This function is not available in all PHP versions so we test it before using it.
function tep_class_exists($class_name) {
    if (function_exists('class_exists')) {
        return class_exists($class_name);
    } else {
        return true;
    }
}

////
// Count how many products exist in a category
// TABLES: products, products_to_categories, categories
function tep_products_in_category_count($categories_id, $include_deactivated = false) {
    $products_count = 0;

    if ($include_deactivated) {
        $products_query = tep_db_query("select count(*) as total from " . TABLE_PRODUCTS . " p, " . TABLE_PRODUCTS_TO_CATEGORIES . " p2c where p.products_id = p2c.products_id and p2c.categories_id = '" . (int) $categories_id . "'");
    } else {
        $products_query = tep_db_query("select count(*) as total from " . TABLE_PRODUCTS . " p, " . TABLE_PRODUCTS_TO_CATEGORIES . " p2c where p.products_id = p2c.products_id and p.products_status = '1' and p2c.categories_id = '" . (int) $categories_id . "'");
    }

    $products = tep_db_fetch_array($products_query);
    $products_count += $products['total'];
    $childs_query = tep_db_query("select categories_id from " . TABLE_CATEGORIES . " where parent_id = '" . (int) $categories_id . "'");

    if (tep_db_num_rows($childs_query)) {
        while ($childs = tep_db_fetch_array($childs_query)) {
            $products_count += tep_products_in_category_count($childs['categories_id'], $include_deactivated);
        }
    }
    return $products_count;
}

////
// Count how many subcategories exist in a category
// TABLES: categories
function tep_childs_in_category_count($categories_id) {
    $categories_count = 0;
    $categories_query = tep_db_query("select categories_id from " . TABLE_CATEGORIES . " where parent_id = '" . (int) $categories_id . "'");
    while ($categories = tep_db_fetch_array($categories_query)) {
        $categories_count++;
        $categories_count += tep_childs_in_category_count($categories['categories_id']);
    }

    return $categories_count;
}

////
// Returns an array with countries
// TABLES: countries
function tep_get_countries($default = '') {
    $countries_array = array();
    if ($default) {
        $countries_array[] = array('id' => '',
            'text' => $default);
    }

    $countries_sql = "	SELECT countries_id, countries_name
    					FROM " . TABLE_COUNTRIES . " 
    					WHERE countries_display = 1 
    					ORDER BY countries_name";
    $countries_query = tep_db_query($countries_sql);
    while ($countries = tep_db_fetch_array($countries_query)) {
        $countries_array[] = array('id' => $countries['countries_id'],
            'text' => $countries['countries_name']);
    }

    return $countries_array;
}

////
// return an array with country zones
function tep_get_country_zones($country_id) {
    $zones_array = array();
    $zones_query = tep_db_query("select zone_id, zone_name from " . TABLE_ZONES . " where zone_country_id = '" . (int) $country_id . "' order by zone_name");
    while ($zones = tep_db_fetch_array($zones_query)) {
        $zones_array[] = array('id' => $zones['zone_id'],
            'text' => $zones['zone_name']);
    }

    return $zones_array;
}

function tep_prepare_country_zones_pull_down($country_id = '') {
    // preset the width of the drop-down for Netscape
    $pre = '';
    if ((!tep_browser_detect('MSIE')) && (tep_browser_detect('Mozilla/4'))) {
        for ($i = 0; $i < 45; $i++)
            $pre .= '&nbsp;';
    }

    $zones = tep_get_country_zones($country_id);

    if (sizeof($zones) > 0) {
        $zones_select = array(array('id' => '0', 'text' => PLEASE_SELECT));
        $zones = array_merge($zones_select, $zones);
    } else {
        $zones = array(array('id' => '0', 'text' => TYPE_BELOW));
        // create dummy options for Netscape to preset the height of the drop-down
        if ((!tep_browser_detect('MSIE')) && (tep_browser_detect('Mozilla/4'))) {
            for ($i = 0; $i < 9; $i++) {
                $zones[] = array('id' => '', 'text' => $pre);
            }
        }
    }

    return $zones;
}

////
// Get list of address_format_id's
function tep_get_address_formats() {
    $address_format_query = tep_db_query("select address_format_id from " . TABLE_ADDRESS_FORMAT . " order by address_format_id");
    $address_format_array = array();
    while ($address_format_values = tep_db_fetch_array($address_format_query)) {
        $address_format_array[] = array('id' => $address_format_values['address_format_id'],
            'text' => $address_format_values['address_format_id']);
    }
    return $address_format_array;
}

////
// Alias function for Store configuration values in the Administration Tool
function tep_cfg_pull_down_country_list($country_id) {
    return tep_draw_pull_down_menu('configuration_value', tep_get_countries(), $country_id);
}

function tep_cfg_pull_down_zone_list($zone_id) {
    return tep_draw_pull_down_menu('configuration_value', tep_get_country_zones(STORE_COUNTRY), $zone_id);
}

function tep_cfg_pull_down_tax_classes($tax_class_id, $key = '') {
    $name = (($key) ? 'configuration[' . $key . ']' : 'configuration_value');

    $tax_class_array = array(array('id' => '0', 'text' => TEXT_NONE));
    $tax_class_query = tep_db_query("select tax_class_id, tax_class_title from " . TABLE_TAX_CLASS . " order by tax_class_title");
    while ($tax_class = tep_db_fetch_array($tax_class_query)) {
        $tax_class_array[] = array('id' => $tax_class['tax_class_id'],
            'text' => $tax_class['tax_class_title']);
    }

    return tep_draw_pull_down_menu($name, $tax_class_array, $tax_class_id);
}

////
// Function to read in text area in admin
function tep_cfg_textarea($text, $key = '') {
    $name = ((tep_not_null($key)) ? 'configuration[' . $key . ']' : 'configuration_value');
    $text = tep_db_prepare_input($text);
    return tep_draw_textarea_field($name, false, 35, 5, $text);
}

function tep_cfg_get_zone_name($zone_id) {
    $zone_query = tep_db_query("select zone_name from " . TABLE_ZONES . " where zone_id = '" . (int) $zone_id . "'");

    if (!tep_db_num_rows($zone_query)) {
        return $zone_id;
    } else {
        $zone = tep_db_fetch_array($zone_query);
        return $zone['zone_name'];
    }
}

////
// Sets the status of a banner
function tep_set_banner_status($banners_id, $status) {
    if ($status == '1') {
        return tep_db_query("update " . TABLE_BANNERS . " set status = '1', expires_impressions = NULL, expires_date = NULL, date_status_change = NULL where banners_id = '" . $banners_id . "'");
    } elseif ($status == '0') {
        return tep_db_query("update " . TABLE_BANNERS . " set status = '0', date_status_change = now() where banners_id = '" . $banners_id . "'");
    } else {
        return -1;
    }
}

////
// Sets the status of a product
function tep_set_product_status($products_id, $status) {
    if ($status == '1') {
        return tep_db_query("update " . TABLE_PRODUCTS . " set products_status = '1', products_last_modified = now() where products_id = '" . (int) $products_id . "'");
    } elseif ($status == '0') {
        return tep_db_query("update " . TABLE_PRODUCTS . " set products_status = '0', products_last_modified = now() where products_id = '" . (int) $products_id . "'");
    } else {
        return -1;
    }
}

////
// Sets the hidden status of a product
function tep_set_product_display($products_id, $display) {
    if ($display == '1') {
        return tep_db_query("UPDATE " . TABLE_PRODUCTS . " SET products_display='1', products_last_modified=now() WHERE products_id='" . (int) $products_id . "'");
    } elseif ($display == '0') {
        return tep_db_query("UPDATE " . TABLE_PRODUCTS . " SET products_display='0', products_last_modified = now() WHERE products_id='" . (int) $products_id . "'");
    } else {
        return -1;
    }
}

////
// Sets the status of a product on special
function tep_set_specials_status($specials_id, $status) {
    if ($status == '1') {
        return tep_db_query("update " . TABLE_SPECIALS . " set status = '1', expires_date = NULL, date_status_change = NULL where specials_id = '" . (int) $specials_id . "'");
    } elseif ($status == '0') {
        return tep_db_query("update " . TABLE_SPECIALS . " set status = '0', date_status_change = now() where specials_id = '" . (int) $specials_id . "'");
    } else {
        return -1;
    }
}

// Sets the status of a promotion
function tep_set_promotions_status($promotions_id, $promotion) {
    if ($promotion == '1') {
        return tep_db_query("update " . TABLE_PROMOTIONS . " set promotions_status = '1' where promotions_id = '" . (int) $promotions_id . "'");
    } elseif ($promotion == '0') {
        return tep_db_query("update " . TABLE_PROMOTIONS . " set promotions_status = '0' where promotions_id = '" . (int) $promotions_id . "'");
    } else {
        return -1;
    }
}

// Get products_payment_mature_period
function tep_get_products_payment_mature_period($products_id) {
    $mature_period = 0;

    $product_mature_period_select_sql = "	SELECT products_payment_mature_period
										 	FROM " . TABLE_PRODUCTS . " 
										 	WHERE products_id = '" . tep_db_input($products_id) . "'";
    $product_mature_period_result_sql = tep_db_query($product_mature_period_select_sql);

    if ($product_mature_period_row = tep_db_fetch_array($product_mature_period_result_sql)) {
        $mature_period = $product_mature_period_row['products_payment_mature_period'];
    }

    return $mature_period;
}

// Sets timeout for the current script.
// Cant be used in safe mode.
function tep_set_time_limit($limit) {
    if (!get_cfg_var('safe_mode')) {
        set_time_limit($limit);
    }
}

////
// Alias function for Store configuration values in the Administration Tool
function tep_cfg_select_option($select_array, $key_value, $key = '') {
    $string = '';

    for ($i = 0, $n = sizeof($select_array); $i < $n; $i++) {
        $name = ((tep_not_null($key)) ? 'configuration[' . $key . ']' : 'configuration_value');
        $string .= '<br><input type="radio" name="' . $name . '" value="' . $select_array[$i] . '"';

        if ($key_value == $select_array[$i])
            $string .= ' CHECKED';

        $string .= '> ' . $select_array[$i];
    }

    return $string;
}

////
// Alias function for Shipping configuration values in the Administration Tool
function tep_cfg_pull_down_option($select_array, $key_value, $key = '') {
    $name = ((tep_not_null($key)) ? 'configuration[' . $key . ']' : 'configuration_value');

    $resource_array = array();
    for ($i = 0; $i < count($select_array); $i++) {
        $resource_array[] = array('id' => $select_array[$i],
            'text' => $select_array[$i]);
    }

    return tep_draw_pull_down_menu($name, $resource_array, $key_value);
}

////
// Alias function for Payment configuration values in the Administration Tool
function tep_cfg_colour_palette($key_value, $key = '') {
    $name = ((tep_not_null($key)) ? 'configuration[' . $key . ']' : 'configuration_value');

    $string = tep_draw_input_field($name, $key_value, 'id="' . $name . '" size="9" onfocus="select() "');
    $string .= "&nbsp;<a href=\"javascript:;\" onClick=\"popUpColorLab('" . $name . "');\">" . tep_image(IMAGE_LEGEND_COLOUR_SELECTION, '', '', '', " border=0 unselectable=on ") . "</a>";

    return $string;
}

////
// Alias function for module configuration keys
function tep_mod_select_option($select_array, $key_name, $key_value) {
    reset($select_array);
    while (list($key, $value) = each($select_array)) {
        if (is_int($key))
            $key = $value;
        $string .= '<br><input type="radio" name="configuration[' . $key_name . ']" value="' . $key . '"';
        if ($key_value == $key)
            $string .= ' CHECKED';
        $string .= '> ' . $value;
    }

    return $string;
}

////
// Retreive server information
function tep_get_system_information() {
    global $HTTP_SERVER_VARS;

    $db_query = tep_db_query("select now() as datetime");
    $db = tep_db_fetch_array($db_query);

    list($system, $host, $kernel) = preg_split('/[\s,]+/', @exec('uname -a'), 5);

    return array('date' => tep_datetime_short(date('Y-m-d H:i:s')),
        'system' => $system,
        'kernel' => $kernel,
        'host' => $host,
        'ip' => gethostbyname($host),
        'uptime' => @exec('uptime'),
        'http_server' => $HTTP_SERVER_VARS['SERVER_SOFTWARE'],
        'php' => PHP_VERSION,
        'zend' => (function_exists('zend_version') ? zend_version() : ''),
        'db_server' => DB_SERVER,
        'db_ip' => gethostbyname(DB_SERVER),
        'db_version' => 'MySQL ' . (function_exists('mysql_get_server_info') ? mysql_get_server_info() : ''),
        'db_date' => tep_datetime_short($db['datetime']));
}

function tep_generate_category_path($id, $from = 'category', $categories_array = '', $index = 0) {
    global $languages_id;

    if (!is_array($categories_array))
        $categories_array = array();

    if ($from == 'product') {
        $categories_query = tep_db_query("select categories_id from " . TABLE_PRODUCTS_TO_CATEGORIES . " where products_id = '" . (int) $id . "'");
        while ($categories = tep_db_fetch_array($categories_query)) {
            if ($categories['categories_id'] == '0') {
                $categories_array[$index][] = array('id' => '0', 'text' => TEXT_TOP);
            } else {
                $category_query = tep_db_query("select cd.categories_name, c.parent_id from " . TABLE_CATEGORIES . " c, " . TABLE_CATEGORIES_DESCRIPTION . " cd where c.categories_id = '" . (int) $categories['categories_id'] . "' and c.categories_id = cd.categories_id and cd.language_id = '" . (int) $languages_id . "'");
                $category = tep_db_fetch_array($category_query);
                $categories_array[$index][] = array('id' => $categories['categories_id'], 'text' => strip_tags($category['categories_name']));
                if ((tep_not_null($category['parent_id'])) && ($category['parent_id'] != '0'))
                    $categories_array = tep_generate_category_path($category['parent_id'], 'category', $categories_array, $index);
                $categories_array[$index] = array_reverse($categories_array[$index]);
            }
            $index++;
        }
    } elseif ($from == 'category') {
        $category_query = tep_db_query("select cd.categories_name, c.parent_id from " . TABLE_CATEGORIES . " c, " . TABLE_CATEGORIES_DESCRIPTION . " cd where c.categories_id = '" . (int) $id . "' and c.categories_id = cd.categories_id and cd.language_id = '" . (int) $languages_id . "'");
        $category = tep_db_fetch_array($category_query);
        $categories_array[$index][] = array('id' => $id, 'text' => strip_tags($category['categories_name']));
        if ((tep_not_null($category['parent_id'])) && ($category['parent_id'] != '0'))
            $categories_array = tep_generate_category_path($category['parent_id'], 'category', $categories_array, $index);
    }

    return $categories_array;
}

//Kenson 27/08/2004 :: Top Heading Path for Product List
function tep_output_generated_category_path_sq($id, $from = 'category') {
    $calculated_category_path_string = '';
    $calculated_category_path = tep_generate_category_path($id, $from);
    for ($i = 0, $n = sizeof($calculated_category_path); $i < $n; $i++) {
        for ($j = 0, $k = sizeof($calculated_category_path[$i]); $j < $k; $j++) {
            if ($ts) {
                $ts = $calculated_category_path[$i][$j]['text'] . " > " . $ts;
            } else {
                $ts = $calculated_category_path[$i][$j]['text'];
            }
        }
    }
    return $ts;
}

//Wei Chen 18/01/2005 :: Top Heading Navigation Path for Category / Product Edit Page
function tep_output_category_nav_path($category_id, $filename = '', $params = '', $safe_quote = true, $display = '') {
    global $languages_id;

    $cat_id_array = array($category_id);
    $category_query = tep_db_query("SELECT parent_id FROM " . TABLE_CATEGORIES . " WHERE categories_id = '" . (int) $category_id . "' AND parent_id<>0");
    while ($category = tep_db_fetch_array($category_query)) {
        $cat_id_array[] = $category["parent_id"];
        $category_query = tep_db_query("SELECT parent_id FROM " . TABLE_CATEGORIES . " WHERE categories_id = '" . $category["parent_id"] . "' AND parent_id<>0");
    }

    $complete_cat_array = $cat_id_array = array_reverse($cat_id_array);

    $cat_nav_array = array();
    $count = 0;
    while (count($cat_id_array)) {
        $cur_cat = end($cat_id_array);
        $extra_param = "";

        $category_name_query = tep_db_query("SELECT categories_name FROM " . TABLE_CATEGORIES_DESCRIPTION . " WHERE categories_id = '" . $cur_cat . "' AND language_id = '" . (int) $languages_id . "'");
        $category_name = tep_db_fetch_array($category_name_query);
        if ($filename) {
            if ($display == 'cat') {
                $extra_param = "cID=" . (!$count ? $_REQUEST["cID"] : $complete_cat_array[count($cat_id_array)]);
            } else if ($display == 'product') {
                $extra_param = (!$count ? "pID=" . $_REQUEST["pID"] : "cID=" . $complete_cat_array[count($cat_id_array)]);
            }
            $link = '<a href="' . tep_href_link($filename, 'cPath=' . implode("_", $cat_id_array) . ($extra_param != '' ? "&$extra_param" : "")) . '">' . strip_tags($category_name["categories_name"]) . '</a>';
        } else {
            $link = strip_tags($category_name["categories_name"]);
        }

        $cat_nav_array[] = $link;
        array_pop($cat_id_array);
        $count++;
    }

    return (count($cat_nav_array) ? implode(( $safe_quote ? "&nbsp;&gt;&nbsp;" : " > "), array_reverse($cat_nav_array)) : TEXT_TOP );
}

function tep_get_particular_cat_path($category_id) {
    $this_cat_path = '';
    if (tep_not_null($category_id)) {
        $cat_id_array = array($category_id);
        $category_query = tep_db_query("SELECT parent_id FROM " . TABLE_CATEGORIES . " WHERE categories_id = '" . (int) $category_id . "' AND parent_id<>0");
        while ($category = tep_db_fetch_array($category_query)) {
            $cat_id_array[] = $category["parent_id"];
            $category_query = tep_db_query("SELECT parent_id FROM " . TABLE_CATEGORIES . " WHERE categories_id = '" . $category["parent_id"] . "' AND parent_id<>0");
        }

        $cat_id_array = array_reverse($cat_id_array);
        $this_cat_path = count($cat_id_array) ? implode('_', $cat_id_array) : '';
    }

    return $this_cat_path;
}

function tep_update_cat_path($cat_id) {
    if (!$cat_id)
        return;
    $cat_path = tep_output_category_nav_path($cat_id, '', '', false);

    $p2c_select_sql = " SELECT products_id
						FROM " . TABLE_PRODUCTS_TO_CATEGORIES . " 
						WHERE categories_id ='" . $cat_id . "' AND products_is_link=0 
						ORDER BY products_id ";
    $p2c_result_sql = tep_db_query($p2c_select_sql);
    while ($p2c_row_sql = tep_db_fetch_array($p2c_result_sql)) {
        $prod_update_sql = " UPDATE " . TABLE_PRODUCTS . " SET products_cat_path='" . tep_db_input($cat_path) . "' WHERE products_id='" . $p2c_row_sql['products_id'] . "'";
        tep_db_query($prod_update_sql);
    }
    $cat_select_sql = " SELECT categories_id
						FROM " . TABLE_CATEGORIES . " 
						WHERE parent_id ='" . $cat_id . "' 
						ORDER BY sort_order ";
    $cat_result_sql = tep_db_query($cat_select_sql);
    while ($cat_row_sql = tep_db_fetch_array($cat_result_sql)) {
        tep_update_cat_path($cat_row_sql["categories_id"]);
    }
}

function tep_get_actual_product_cat_id($prod_id) {
    $category_select_sql = "SELECT categories_id FROM " . TABLE_PRODUCTS_TO_CATEGORIES . " WHERE products_id = '" . $prod_id . "' AND products_is_link=0";
    $category_result_sql = tep_db_query($category_select_sql);
    $category_id = tep_db_fetch_array($category_result_sql);

    return $category_id["categories_id"];
}

function tep_output_generated_category_path($id, $from = 'category') {
    $calculated_category_path_string = '';
    $calculated_category_path = tep_generate_category_path($id, $from);

    for ($i = 0, $n = sizeof($calculated_category_path); $i < $n; $i++) {
        for ($j = 0, $k = sizeof($calculated_category_path[$i]); $j < $k; $j++) {
            if ($ts) {
                $ts = $calculated_category_path[$i][$j]['text'] . " > " . $ts;
            } else {
                $ts = $calculated_category_path[$i][$j]['text'];
            }
            $calculated_category_path_string .= $calculated_category_path[$i][$j]['text'] . '&nbsp;&gt;&nbsp;';
        }
        $calculated_category_path_string = substr($calculated_category_path_string, 0, -16) . '<br>';
    }
    $calculated_category_path_string = substr($calculated_category_path_string, 0, -4);

    if (strlen($calculated_category_path_string) < 1)
        $calculated_category_path_string = TEXT_TOP;

    return $calculated_category_path_string;
}

function tep_get_generated_category_path_ids($id, $from = 'category') {
    $calculated_category_path_string = '';
    $calculated_category_path = tep_generate_category_path($id, $from);
    for ($i = 0, $n = sizeof($calculated_category_path); $i < $n; $i++) {
        for ($j = 0, $k = sizeof($calculated_category_path[$i]); $j < $k; $j++) {
            $calculated_category_path_string .= $calculated_category_path[$i][$j]['id'] . '_';
        }
        $calculated_category_path_string = substr($calculated_category_path_string, 0, -1) . '<br>';
    }
    $calculated_category_path_string = substr($calculated_category_path_string, 0, -4);

    if (strlen($calculated_category_path_string) < 1)
        $calculated_category_path_string = TEXT_TOP;

    return $calculated_category_path_string;
}

function tep_remove_category($category_id) {
    include_once(DIR_WS_CLASSES . 'ogm_amazon_ws.php');
    $aws_obj = new ogm_amazon_ws();
    /* 	$category_image_query = tep_db_query("select categories_image from " . TABLE_CATEGORIES . " where categories_id = '" . (int)$category_id . "'");
      $category_image = tep_db_fetch_array($category_image_query);

      $duplicate_image_query = tep_db_query("select count(*) as total from " . TABLE_CATEGORIES . " where categories_image = '" . tep_db_input($category_image['categories_image']) . "'");
      $duplicate_image = tep_db_fetch_array($duplicate_image_query);
     */
    $category_image_query = tep_db_query("	SELECT categories_image
											FROM " . TABLE_CATEGORIES_DESCRIPTION . " 
											WHERE categories_id = '" . (int) $category_id . "'");
    $category_image = tep_db_fetch_array($category_image_query);

    $duplicate_image_query = tep_db_query("	SELECT count(*) as total
    										FROM " . TABLE_CATEGORIES_DESCRIPTION . " 
    										WHERE categories_image = '" . tep_db_input($category_image['categories_image']) . "'");
    $duplicate_image = tep_db_fetch_array($duplicate_image_query);

    if ($duplicate_image['total'] < 2) {
        if ($aws_obj->is_image_exists($category_image['categories_image'], 'images/category/', 'BUCKET_STATIC')) {
            $aws_obj->delete_file();
        } else if (file_exists(DIR_FS_CATALOG_IMAGES . $_SESSION['cPath'] . "/category/" . $category_image['categories_image'])) {
            @unlink(DIR_FS_CATALOG_IMAGES . $_SESSION['cPath'] . "/category/" . $category_image['categories_image']);
        }
    }
    unset($aws_obj);

    tep_db_query("DELETE FROM " . TABLE_CATEGORIES . " WHERE categories_id = '" . (int) $category_id . "'");
    tep_db_query("DELETE FROM " . TABLE_CATEGORIES_DESCRIPTION . " WHERE categories_id = '" . (int) $category_id . "'");
    tep_db_query("DELETE FROM " . TABLE_PRODUCTS_TO_CATEGORIES . " WHERE categories_id = '" . (int) $category_id . "'");

    if (USE_CACHE == 'true') {
        tep_reset_cache_block('categories');
        tep_reset_cache_block('also_purchased');
    }
}

////
// Check if this product has the right to be checkout even if low qty or negative qty
// TABLES: products
function tep_check_product_skip_inventory($products_id) {
    $products_id = tep_get_prid($products_id);
    $prod_permission_query = tep_db_query("SELECT products_skip_inventory FROM " . TABLE_PRODUCTS . " WHERE products_id = '" . (int) $products_id . "'");
    $prod_permission = tep_db_fetch_array($prod_permission_query);

    return ($prod_permission['products_skip_inventory']);
}

function tep_admin_group_unlock_permission() {
    $admin_group_array = array();
    $unlock_admin_group_select_sql = "SELECT ag.admin_groups_id, ag.admin_groups_name FROM " . TABLE_ADMIN_GROUPS . " AS ag, " . TABLE_ADMIN_FILES_ACTIONS . " AS afa WHERE afa.admin_files_actions_key='UNLOCK_OTHERS_ORDERS' AND FIND_IN_SET(ag.admin_groups_id, afa.admin_groups_id)";
    $unlock_admin_group_result_sql = tep_db_query($unlock_admin_group_select_sql);
    while ($unlock_admin_group_row = tep_db_fetch_array($unlock_admin_group_result_sql)) {
        $admin_group_array[$unlock_admin_group_row["admin_groups_id"]] = $unlock_admin_group_row["admin_groups_name"];
    }

    return $admin_group_array;
}

// release those orders locked by this admin id when logoff
function tep_release_locked_order($admin_id, $inclusion = true) {
    if (is_array($admin_id) && count($admin_id)) { // batch release for a group of inactive admin staff
        if ($inclusion == true) {
            $unlock_orders_update_sql = "UPDATE " . TABLE_ORDERS . " SET orders_locked_by = NULL, orders_locked_from_ip = NULL, orders_locked_datetime = NULL WHERE orders_locked_by IS NOT NULL AND orders_locked_by IN (" . implode(', ', $admin_id) . ")";
            tep_db_query($unlock_orders_update_sql);
        } else {
            $unlock_orders_update_sql = "UPDATE " . TABLE_ORDERS . " SET orders_locked_by = NULL, orders_locked_from_ip = NULL, orders_locked_datetime = NULL WHERE orders_locked_by IS NOT NULL AND orders_locked_by NOT IN (" . implode(', ', $admin_id) . ")";
            tep_db_query($unlock_orders_update_sql);
        }
    } else { // for individual admin
        $unlock_orders_update_sql = "UPDATE " . TABLE_ORDERS . " SET orders_locked_by = NULL, orders_locked_from_ip = NULL, orders_locked_datetime = NULL WHERE orders_locked_by = '" . (int) $admin_id . "'";
        tep_db_query($unlock_orders_update_sql);
    }
}

function tep_check_duplicate_name($original_customer, $payment_method) {
    $customer_id_array = array();
    $duplicate_name_select_sql = '';
    if ($payment_method == 'paypal') {
        $duplicate_name_select_sql = "SELECT DISTINCT(o.customers_id) AS cust_id FROM " . TABLE_ORDERS . " AS o INNER JOIN " . TABLE_PAYPAL . " AS p ON o.paypal_ipn_id = p.paypal_ipn_id WHERE CONCAT(p.first_name, ' ', p.last_name) = '" . tep_db_input($original_customer) . "'";
    } else if ($payment_method == 'worldpay') {
        $duplicate_name_select_sql = "SELECT DISTINCT(o.customers_id) AS cust_id FROM " . TABLE_ORDERS . " AS o INNER JOIN " . TABLE_PAYMENT_EXTRA_INFO . " AS p ON o.orders_id = p.orders_id WHERE p.credit_card_owner = '" . tep_db_input($original_customer) . "'";
    }

    if (tep_not_null($duplicate_name_select_sql)) {
        $duplicate_name_result_sql = tep_db_query($duplicate_name_select_sql);

        while ($duplicate_name_row = tep_db_fetch_array($duplicate_name_result_sql)) {
            $customer_id_array[] = $duplicate_name_row["cust_id"];
        }
    }

    return $customer_id_array;
}

function tep_get_distinct_name_used($customer_id, $current_name, $payment_method) {
    $customer_name_array = array();
    $name_count_select_sql = '';
    if ($payment_method == 'paypal') {
        $name_count_select_sql = "SELECT DISTINCT(concat(p.first_name, ' ', p.last_name)) cust_name FROM " . TABLE_ORDERS . " AS o INNER JOIN " . TABLE_PAYPAL . " AS p ON o.paypal_ipn_id = p.paypal_ipn_id WHERE o.customers_id = '" . (int) $customer_id . "'";
    } else if ($payment_method == 'worldpay') {
        $name_count_select_sql = "SELECT DISTINCT(p.credit_card_owner) cust_name FROM " . TABLE_ORDERS . " AS o INNER JOIN " . TABLE_PAYMENT_EXTRA_INFO . " AS p ON o.orders_id = p.orders_id WHERE o.customers_id = '" . (int) $customer_id . "'";
    }

    if (tep_not_null($name_count_select_sql)) {
        $name_count_result_sql = tep_db_query($name_count_select_sql);

        while ($name_count_row = tep_db_fetch_array($name_count_result_sql)) {
            $customer_name_array[] = $name_count_row["cust_name"];
        }
    }

    return $customer_name_array;
}

function tep_get_latest_payment_method($customer_id, $order_id) {
    // Look for those in "Completed", "Refunded" and "Reversed" orders
    $payment_method_select_sql = "SELECT payment_method FROM " . TABLE_ORDERS . " WHERE customers_id = '" . (int) $customer_id . "' AND orders_status IN (3, 4, 6) AND orders_id <> '" . (int) $order_id . "' ORDER BY date_purchased DESC LIMIT 1";
    $payment_method_result_sql = tep_db_query($payment_method_select_sql);
    $payment_method_row = tep_db_fetch_array($payment_method_result_sql);

    return $payment_method_row["payment_method"];
}

function tep_get_customer_share_ip($ip_add) {
    // Look for number of customer sharing the same ip address
    $customer_id_array = array();
    $share_ip_select_sql = "SELECT DISTINCT(customers_id) AS cust_id FROM " . TABLE_ORDERS . " WHERE remote_addr = '" . tep_db_input($ip_add) . "'";
    $share_ip_result_sql = tep_db_query($share_ip_select_sql);

    while ($share_ip_row = tep_db_fetch_array($share_ip_result_sql)) {
        $customer_id_array[] = $share_ip_row["cust_id"];
    }

    return $customer_id_array;
}

function tep_reset_cache_block($cache_block) {
    global $cache_blocks;

    for ($i = 0, $n = sizeof($cache_blocks); $i < $n; $i++) {
        if ($cache_blocks[$i]['code'] == $cache_block) {
            if ($cache_blocks[$i]['multiple']) {
                if ($dir = @opendir(DIR_FS_CACHE)) {
                    while ($cache_file = readdir($dir)) {
                        $cached_file = $cache_blocks[$i]['file'];
                        $languages = tep_get_languages();
                        for ($j = 0, $k = sizeof($languages); $j < $k; $j++) {
                            $cached_file_unlink = ereg_replace_dep('-language', '-' . $languages[$j]['directory'], $cached_file);
                            if (ereg_dep('^' . $cached_file_unlink, $cache_file)) {
                                @unlink(DIR_FS_CACHE . $cache_file);
                            }
                        }
                    }
                    closedir($dir);
                }
            } else {
                $cached_file = $cache_blocks[$i]['file'];
                $languages = tep_get_languages();
                for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
                    $cached_file = ereg_replace_dep('-language', '-' . $languages[$i]['directory'], $cached_file);
                    @unlink(DIR_FS_CACHE . $cached_file);
                }
            }
            break;
        }
    }
}

function tep_get_file_permissions($mode) {
    // determine type
    if (($mode & 0xC000) == 0xC000) { // unix domain socket
        $type = 's';
    } elseif (($mode & 0x4000) == 0x4000) { // directory
        $type = 'd';
    } elseif (($mode & 0xA000) == 0xA000) { // symbolic link
        $type = 'l';
    } elseif (($mode & 0x8000) == 0x8000) { // regular file
        $type = '-';
    } elseif (($mode & 0x6000) == 0x6000) { //bBlock special file
        $type = 'b';
    } elseif (($mode & 0x2000) == 0x2000) { // character special file
        $type = 'c';
    } elseif (($mode & 0x1000) == 0x1000) { // named pipe
        $type = 'p';
    } else { // unknown
        $type = '?';
    }

    // determine permissions
    $owner['read'] = ($mode & 00400) ? 'r' : '-';
    $owner['write'] = ($mode & 00200) ? 'w' : '-';
    $owner['execute'] = ($mode & 00100) ? 'x' : '-';
    $group['read'] = ($mode & 00040) ? 'r' : '-';
    $group['write'] = ($mode & 00020) ? 'w' : '-';
    $group['execute'] = ($mode & 00010) ? 'x' : '-';
    $world['read'] = ($mode & 00004) ? 'r' : '-';
    $world['write'] = ($mode & 00002) ? 'w' : '-';
    $world['execute'] = ($mode & 00001) ? 'x' : '-';

    // adjust for SUID, SGID and sticky bit
    if ($mode & 0x800)
        $owner['execute'] = ($owner['execute'] == 'x') ? 's' : 'S';
    if ($mode & 0x400)
        $group['execute'] = ($group['execute'] == 'x') ? 's' : 'S';
    if ($mode & 0x200)
        $world['execute'] = ($world['execute'] == 'x') ? 't' : 'T';

    return $type .
            $owner['read'] . $owner['write'] . $owner['execute'] .
            $group['read'] . $group['write'] . $group['execute'] .
            $world['read'] . $world['write'] . $world['execute'];
}

function tep_remove($source) {
    global $messageStack, $tep_remove_error;

    if (isset($tep_remove_error))
        $tep_remove_error = false;

    if (is_dir($source)) {
        $dir = dir($source);
        while ($file = $dir->read()) {
            if (($file != '.') && ($file != '..')) {
                if (is_writeable($source . '/' . $file)) {
                    tep_remove($source . '/' . $file);
                } else {
                    $messageStack->add(sprintf(ERROR_FILE_NOT_REMOVEABLE, $source . '/' . $file), 'error');
                    $tep_remove_error = true;
                }
            }
        }
        $dir->close();

        if (is_writeable($source)) {
            rmdir($source);
        } else {
            $messageStack->add(sprintf(ERROR_DIRECTORY_NOT_REMOVEABLE, $source), 'error');
            $tep_remove_error = true;
        }
    } else {
        if (is_writeable($source)) {
            unlink($source);
        } else {
            $messageStack->add(sprintf(ERROR_FILE_NOT_REMOVEABLE, $source), 'error');
            $tep_remove_error = true;
        }
    }
}

////
// Output the tax percentage with optional padded decimals
function tep_display_tax_value($value, $padding = TAX_DECIMAL_PLACES) {
    if (strpos($value, '.')) {
        $loop = true;
        while ($loop) {
            if (substr($value, -1) == '0') {
                $value = substr($value, 0, -1);
            } else {
                $loop = false;
                if (substr($value, -1) == '.') {
                    $value = substr($value, 0, -1);
                }
            }
        }
    }

    if ($padding > 0) {
        if ($decimal_pos = strpos($value, '.')) {
            $decimals = strlen(substr($value, ($decimal_pos + 1)));
            for ($i = $decimals; $i < $padding; $i++) {
                $value .= '0';
            }
        } else {
            $value .= '.';
            for ($i = 0; $i < $padding; $i++) {
                $value .= '0';
            }
        }
    }

    return $value;
}

function tep_mail($to_name, $to_email_address, $email_subject, $email_text, $from_email_name, $from_email_address) {
    if (SEND_EMAILS != 'true')
        return false;

    // Instantiate a new mail object
    $message = new email(array('X-Mailer: php',
        'Reply-To: ' . $from_email_address
    ));

    // Build the text version
    $text = strip_tags($email_text);
    if (EMAIL_USE_HTML == 'true') {
        $message->add_html($email_text, $text);
    } else {
        $message->add_text($text);
    }

    // Send message
    $message->build_message();
    $message->send($to_name, $to_email_address, $from_email_name, $from_email_address, $email_subject);
}

function tep_get_tax_class_title($tax_class_id) {
    if ($tax_class_id == '0') {
        return TEXT_NONE;
    } else {
        $classes_query = tep_db_query("select tax_class_title from " . TABLE_TAX_CLASS . " where tax_class_id = '" . (int) $tax_class_id . "'");
        $classes = tep_db_fetch_array($classes_query);

        return $classes['tax_class_title'];
    }
}

function tep_banner_image_extension() {
    if (function_exists('imagetypes')) {
        if (imagetypes() & IMG_PNG) {
            return 'png';
        } elseif (imagetypes() & IMG_JPG) {
            return 'jpg';
        } elseif (imagetypes() & IMG_GIF) {
            return 'gif';
        }
    } elseif (function_exists('imagecreatefrompng') && function_exists('imagepng')) {
        return 'png';
    } elseif (function_exists('imagecreatefromjpeg') && function_exists('imagejpeg')) {
        return 'jpg';
    } elseif (function_exists('imagecreatefromgif') && function_exists('imagegif')) {
        return 'gif';
    }

    return false;
}

////
// Wrapper function for round() for php3 compatibility
function tep_round($value, $precision) {
    if (PHP_VERSION < 4) {
        $exp = pow(10, $precision);
        return round($value * $exp) / $exp;
    } else {
        return round($value, $precision);
    }
}

function tep_round_up_to($value, $unit) {
    $result = 0;
    if ($value > 0 && $unit > 0) {
        $integral_multiple = ceil($value / $unit);

        $result = $integral_multiple * $unit;
    }

    return $result;
}

////
// Add tax to a products price
function tep_add_tax($price, $tax) {
    global $currencies;

    if (DISPLAY_PRICE_WITH_TAX == 'true') {
        return tep_round($price, $currencies->currencies[DEFAULT_CURRENCY]['decimal_places']) + tep_calculate_tax($price, $tax);
    } else {
        return tep_round($price, $currencies->currencies[DEFAULT_CURRENCY]['decimal_places']);
    }
}

// Calculates Tax rounding the result
function tep_calculate_tax($price, $tax) {
    global $currencies;

    return tep_round($price * $tax / 100, $currencies->currencies[DEFAULT_CURRENCY]['decimal_places']);
}

////
// Returns the tax rate for a zone / class
// TABLES: tax_rates, zones_to_geo_zones
function tep_get_tax_rate($class_id, $country_id = -1, $zone_id = -1) {
    global $customer_zone_id, $customer_country_id;

    if (($country_id == -1) && ($zone_id == -1)) {
        if (!tep_session_is_registered('customer_id')) {
            $country_id = STORE_COUNTRY;
            $zone_id = STORE_ZONE;
        } else {
            $country_id = $customer_country_id;
            $zone_id = $customer_zone_id;
        }
    }

    $tax_query = tep_db_query("select SUM(tax_rate) as tax_rate from " . TABLE_TAX_RATES . " tr left join " . TABLE_ZONES_TO_GEO_ZONES . " za ON tr.tax_zone_id = za.geo_zone_id left join " . TABLE_GEO_ZONES . " tz ON tz.geo_zone_id = tr.tax_zone_id WHERE (za.zone_country_id IS NULL OR za.zone_country_id = '0' OR za.zone_country_id = '" . (int) $country_id . "') AND (za.zone_id IS NULL OR za.zone_id = '0' OR za.zone_id = '" . (int) $zone_id . "') AND tr.tax_class_id = '" . (int) $class_id . "' GROUP BY tr.tax_priority");
    if (tep_db_num_rows($tax_query)) {
        $tax_multiplier = 0;
        while ($tax = tep_db_fetch_array($tax_query)) {
            $tax_multiplier += $tax['tax_rate'];
        }
        return $tax_multiplier;
    } else {
        return 0;
    }
}

// Returns the tax rate for a tax class
// TABLES: tax_rates
function tep_get_tax_rate_value($class_id) {
    $tax_query = tep_db_query("select SUM(tax_rate) as tax_rate from " . TABLE_TAX_RATES . " where tax_class_id = '" . (int) $class_id . "' group by tax_priority");
    if (tep_db_num_rows($tax_query)) {
        $tax_multiplier = 0;
        while ($tax = tep_db_fetch_array($tax_query)) {
            $tax_multiplier += $tax['tax_rate'];
        }
        return $tax_multiplier;
    } else {
        return 0;
    }
}

function tep_call_function($function, $parameter, $object = '') {
    if ($object == '') {
        return call_user_func($function, $parameter);
    } elseif (PHP_VERSION < 4) {
        return call_user_method($function, $object, $parameter);
    } else {
        return call_user_func(array($object, $function), $parameter);
    }
}

function tep_get_zone_class_title($zone_class_id) {
    if ($zone_class_id == '0') {
        return TEXT_NONE;
    } else {
        $classes_query = tep_db_query("select geo_zone_name from " . TABLE_GEO_ZONES . " where geo_zone_id = '" . (int) $zone_class_id . "'");
        $classes = tep_db_fetch_array($classes_query);

        return $classes['geo_zone_name'];
    }
}

function tep_cfg_pull_down_zone_classes($zone_class_id, $key = '') {
    $name = (($key) ? 'configuration[' . $key . ']' : 'configuration_value');

    $zone_class_array = array(array('id' => '0', 'text' => TEXT_NONE));
    $zone_class_query = tep_db_query("select geo_zone_id, geo_zone_name from " . TABLE_GEO_ZONES . " order by geo_zone_name");
    while ($zone_class = tep_db_fetch_array($zone_class_query)) {
        $zone_class_array[] = array('id' => $zone_class['geo_zone_id'],
            'text' => $zone_class['geo_zone_name']);
    }

    return tep_draw_pull_down_menu($name, $zone_class_array, $zone_class_id);
}

function tep_cfg_pull_down_order_statuses($order_status_id, $key = '') {
    global $languages_id;

    $name = (($key) ? 'configuration[' . $key . ']' : 'configuration_value');

    $statuses_array = array(array('id' => '0', 'text' => TEXT_DEFAULT));
    $statuses_query = tep_db_query("select orders_status_id, orders_status_name from " . TABLE_ORDERS_STATUS . " where language_id = '" . (int) $languages_id . "' order by orders_status_sort_order");
    while ($statuses = tep_db_fetch_array($statuses_query)) {
        $statuses_array[] = array('id' => $statuses['orders_status_id'],
            'text' => $statuses['orders_status_name']);
    }

    return tep_draw_pull_down_menu($name, $statuses_array, $order_status_id);
}

/* To be removed from next rollout
  function tep_get_order_status_name($order_status_id, $language_id = '') {
  global $languages_id;

  if ($order_status_id < 1) return TEXT_DEFAULT;

  if (!is_numeric($language_id)) $language_id = $languages_id;

  $status_query = tep_db_query("select orders_status_name from " . TABLE_ORDERS_STATUS . " where orders_status_id = '" . (int)$order_status_id . "' and language_id = '" . (int)$language_id . "'");
  $status = tep_db_fetch_array($status_query);

  return $status['orders_status_name'];
  }
 */

////
// Return a random value
function tep_rand($min = null, $max = null) {
    static $seeded;

    if (!$seeded) {
        mt_srand((double) microtime() * 1000000);
        $seeded = true;
    }

    if (isset($min) && isset($max)) {
        if ($min >= $max) {
            return $min;
        } else {
            return mt_rand($min, $max);
        }
    } else {
        return mt_rand();
    }
}

// nl2br() prior PHP 4.2.0 did not convert linefeeds on all OSs (it only converted \n)
function tep_convert_linefeeds($from, $to, $string) {
    if ((PHP_VERSION < "4.0.5") && is_array($from)) {
        return ereg_replace_dep('(' . implode('|', $from) . ')', $to, $string);
    } else {
        return str_replace($from, $to, $string);
    }
}

function tep_string_to_int($string) {
    return (int) $string;
}

////
// Parse and secure the cPath parameter values
function tep_parse_category_path($cPath) {
    // make sure the category IDs are integers
    $cPath_array = array_map('tep_string_to_int', explode('_', $cPath));

    // make sure no duplicate category IDs exist which could lock the server in a loop
    $tmp_array = array();
    $n = sizeof($cPath_array);
    for ($i = 0; $i < $n; $i++) {
        if (!in_array($cPath_array[$i], $tmp_array)) {
            $tmp_array[] = $cPath_array[$i];
        }
    }

    return $tmp_array;
}

function tep_gen_random_key($prefix = "", $searchTable = "", $searchField = "") {
    if (trim($searchTable) == "" || trim($searchField) == "") {
        $value = tep_rand(10000, 99999);
    } else {
        $again = true;
        while ($again) {
            $value = $prefix . tep_rand(1, 99999);
            $sql = "select * from $searchTable where $searchField='$value';";
            $result = tep_db_query($sql);

            if (tep_db_num_rows($result)) {
                $again = true;
            } else {
                $again = false;
            }
        }
    }
    return $value;
}

// Alias function for array of configuration values in the Administration Tool
function tep_cfg_select_multioption($select_array, $key_value, $key = '') {
    for ($i = 0; $i < sizeof($select_array); $i++) {
        $name = (($key) ? 'configuration[' . $key . '][]' : 'configuration_value');
        $string .= '<br><input type="checkbox" name="' . $name . '" value="' . $select_array[$i] . '"';
        $key_values = explode(", ", $key_value);
        if (in_array($select_array[$i], $key_values))
            $string .= 'CHECKED';
        $string .= '> ' . $select_array[$i];
    }
    return $string;
}

//
function tep_insert_log_files($log_userid = '', $log_time = '', $log_ip = '', $log_details = '', $log_categories = '') {
    $sql_data_array = array('log_userid' => $log_userid,
        'log_ip' => $log_ip,
        'log_details' => $log_details,
        'log_categories' => $log_categories
    );
    $insert_sql_data = array('log_time' => 'now()');
    $sql_data_array = array_merge($sql_data_array, $insert_sql_data);
    tep_db_perform(TABLE_LOG_FILES, $sql_data_array);
    //$log_id = tep_db_insert_id();
}

// BOF Enable - Disable Categories Contribution--------------------------------------
// Sets the status of a category and all nested categories and products whithin.
function tep_set_categories_status($category_id, $status) {
    if ($status == '1') {
        tep_db_query("UPDATE " . TABLE_CATEGORIES . " SET categories_status = '1', last_modified = now() WHERE categories_id = '" . $category_id . "'");

        $tree = tep_get_cat_tree($category_id, "root");
        for ($i = 0; $i < count($tree); $i++) {
            if ($tree[$i]["id"] != $category_id) {
                tep_db_query("UPDATE " . TABLE_CATEGORIES . " SET categories_status = '1', last_modified = now() WHERE categories_id = '" . $tree[$i]["id"] . "'");
            }
        }

        $tree = tep_get_cat_tree($category_id, "base");

        for ($i = 0; $i < count($tree); $i++) {
            for ($j = 0; $j < count($tree[$i]); $j++) {
                if ($tree[$i][$j]["id"] != $category_id) {
                    tep_db_query("UPDATE " . TABLE_CATEGORIES . " SET categories_status = '1', last_modified = now() WHERE categories_id = '" . $tree[$i][$j]["id"] . "'");
                }
            }
        }
    } elseif ($status == '0') {
        tep_db_query("UPDATE " . TABLE_CATEGORIES . " SET categories_status = '0', last_modified = now() WHERE categories_id = '" . $category_id . "'");

        $tree = tep_get_cat_tree($category_id, "base");

        for ($i = 0; $i < count($tree); $i++) {
            for ($j = 0; $j < count($tree[$i]); $j++) {
                if ($tree[$i][$j]["id"] != $category_id) {
                    tep_db_query("UPDATE " . TABLE_CATEGORIES . " SET categories_status = '0', last_modified = now() WHERE categories_id = '" . $tree[$i][$j]["id"] . "'");
                }
            }
        }
    }
}

//Wei Chen 24/02/2005 :: Category tree array
function tep_get_cat_tree($category_id, $direction = "") {
    global $languages_id;

    $cat_id_array = array();

    if ($direction == "root") {
        $cat_id_array = array(array("id" => $category_id));
        $parent_category_query = tep_db_query("SELECT parent_id FROM " . TABLE_CATEGORIES . " WHERE categories_id = '" . (int) $category_id . "' AND parent_id<>0");
        while ($parent_category = tep_db_fetch_array($parent_category_query)) {
            $cat_id_array[]["id"] = $parent_category["parent_id"];
            $parent_category_query = tep_db_query("SELECT parent_id FROM " . TABLE_CATEGORIES . " WHERE categories_id = '" . $parent_category["parent_id"] . "' AND parent_id<>0");
        }
        $cat_id_array = array_reverse($cat_id_array);
    } else if ($direction == "base") {
        $category_name_query = tep_db_query("SELECT cd.categories_name FROM " . TABLE_CATEGORIES_DESCRIPTION . " AS cd WHERE cd.language_id = '" . (int) $languages_id . "' AND cd.categories_id = '" . $category_id . "'");
        $category_name = tep_db_fetch_array($category_name_query);

        $cat_id_array["0"] = array(array("id" => $category_id, "text" => strip_tags($category_name["categories_name"])));
        tep_get_sub_cat_tree($category_id, $cat_id_array, 1, '___');
        //ksort($cat_id_array);
    }

    return $cat_id_array;
}

function tep_get_sub_cat_tree($category_id, &$cat_id_array, $level, $spacing = '', $skip = '', $by_level = true) {
    global $languages_id;

    if ($by_level) {
        $sub_category_select_sql = "SELECT c.categories_id, cd.categories_name
									FROM " . TABLE_CATEGORIES . " AS c 
									LEFT JOIN " . TABLE_CATEGORIES_DESCRIPTION . " AS cd 
										ON c.categories_id=cd.categories_id 
									WHERE c.parent_id = '" . $category_id . "' AND cd.language_id = '" . (int) $languages_id . "'";
        $sub_category_query = tep_db_query($sub_category_select_sql);

        while ($sub_category = tep_db_fetch_array($sub_category_query)) {
            tep_get_sub_cat_tree($sub_category["categories_id"], $cat_id_array, $level + 1, $spacing);
            $cat_id_array[$level][] = array("id" => $sub_category["categories_id"], 'text' => strip_tags($sub_category["categories_name"]));
        }
    } else {
        if (is_array($skip) && !in_array($category_id, $skip)) {
            $cat_select_sql = "	SELECT c.categories_id, cd.categories_name
								FROM " . TABLE_CATEGORIES . " AS c 
								LEFT JOIN " . TABLE_CATEGORIES_DESCRIPTION . " AS cd 
									ON c.categories_id=cd.categories_id 
								WHERE c.categories_id = '" . $category_id . "' AND cd.language_id = '" . (int) $languages_id . "'";
            $cat_result_sql = tep_db_query($cat_select_sql);
            if ($cat_row = tep_db_fetch_array($cat_result_sql)) {
                $cat_id_array[] = array("id" => $category_id, 'text' => ($level > 1 ? str_repeat($spacing, $level - 1) . strip_tags($cat_row["categories_name"]) : strip_tags($cat_row["categories_name"])));
            }
        }
        $sub_category_select_sql = "SELECT c.categories_id, cd.categories_name
									FROM " . TABLE_CATEGORIES . " AS c 
									LEFT JOIN " . TABLE_CATEGORIES_DESCRIPTION . " AS cd 
										ON c.categories_id = cd.categories_id 
									WHERE c.parent_id = '" . $category_id . "' AND cd.language_id = '" . (int) $languages_id . "'
									ORDER BY c.sort_order, cd.categories_name";
        $sub_category_result_sql = tep_db_query($sub_category_select_sql);

        while ($sub_category = tep_db_fetch_array($sub_category_result_sql)) {
            tep_get_sub_cat_tree($sub_category["categories_id"], $cat_id_array, $level + 1, $spacing, $skip, false);
        }
    }
    return true;
}

function tep_get_order_comment($id) {
    $id = (int) $id;

    if ($id == 0) {
        return "";
    } else {
        $result = tep_db_query("select orders_comments_text from orders_comments where orders_comments_id='$id';");

        if ($row = tep_db_fetch_array($result)) {
            return $row['orders_comments_text'];
        } else {
            return "";
        }
    }
}

// by subrat, clean the data 
function tep_filtersqldata(&$data) {
    $data = mysql_escape_string($data);
    $data = htmlentities($data, ENT_QUOTES);
}

function tep_clean_data($var, $filter = false) {
    if (!is_array($var)) {
        if (isset($var)) {
            if ($filter)
                tep_filtersqldata($var);
            return $var;
        } else {
            return '';
        }
    } else {
        for ($i = 0; $i < sizeof($var);  ++$i) {
            if (isset($var[$i])) {
                if ($filter)
                    tep_filtersqldata($var[$i]);
            } else {
                $var[$i] = '';
            }
        }
        return $var;
    }
}

function tep_array_serialize($arr) {
    return urlencode(serialize($arr));
}

function tep_array_unserialize($val) {
    return unserialize(urldecode(stripslashes($val)));
}

function tep_calculate_amount($qty, $max1, $price1, $max2, $discount_rate) {
    $qty = (int) $qty;
    $max1 = (int) $max1;
    $price1 = (double) $price1;
    $max2 = (int) $max2;
    $discount_rate = (double) $discount_rate;

    $price2 = $price1 * ( (100 - $discount_rate) / 100 );
    $price2 = (double) $price2;

    $amount = 0;

    if ($qty <= $max1 + $max2) {
        if ($qty > $max1) {
            $amount = ( $max1 * $price1 ) + ( ($qty - $max1) * $price2 );
        } else {
            $amount = $qty * $price1;
        }
    } else {
        $amount = ( $max1 * $price1 ) + ( $max2 * $price2 );
    }

    return $amount;
}

function tep_show_purchase_status($sup_grp_id) {
    $show_purchase_status_select_sql = "SELECT show_products_purchase_demand_status as show_status
										FROM " . TABLE_SUPPLIER_GROUPS . " 
										WHERE supplier_groups_id = '" . (int) $sup_grp_id . "'";
    $show_purchase_status_result_sql = tep_db_query($show_purchase_status_select_sql);
    $show_purchase_status_row = tep_db_fetch_array($show_purchase_status_result_sql);

    return ((int) $show_purchase_status_row["show_status"] == 1);
}

function tep_get_active_suppliers_count($sup_grp_id) {
    $active_supplier_count_sql = "	SELECT COUNT(supplier_id) AS active_supplier
									FROM " . TABLE_SUPPLIER . " 
									WHERE supplier_groups_id = '" . (int) $sup_grp_id . "' AND supplier_status=1";
    $active_supplier_result_sql = tep_db_query($active_supplier_count_sql);
    $active_supplier_row = tep_db_fetch_array($active_supplier_result_sql);

    return (int) $active_supplier_row["active_supplier"];
}

function tep_get_supplier_list_timer($sup_id, $list_id) {
    $time_res = array();

    $supplier_timer_select_sql = "	SELECT s.supplier_groups_id, spm.supplier_purchase_mode
									FROM " . TABLE_SUPPLIER . " AS s 
									INNER JOIN " . TABLE_SUPPLIER_PURCHASE_MODES . " spm 
										ON (s.supplier_id=spm.supplier_id AND spm.products_purchases_lists_id='" . (int) $list_id . "')
									WHERE s.supplier_id = '" . (int) $sup_id . "'";
    $supplier_timer_result_sql = tep_db_query($supplier_timer_select_sql);

    if ($supplier_timer_row = tep_db_fetch_array($supplier_timer_result_sql)) {
        if ($supplier_timer_row["supplier_purchase_mode"] != 'STATUS_ON' && $supplier_timer_row["supplier_purchase_mode"] != 'STATUS_OFF') {
            // This section implies this supplier purchase mode is following the supplier group setting

            $supplier_group_timer_select_sql = "SELECT * FROM " . TABLE_SUPPLIER_LIST_TIME_SETTING . " WHERE supplier_groups_id = '" . (int) $supplier_timer_row["supplier_groups_id"] . "' AND products_purchases_lists_id = '" . (int) $list_id . "'";
            $supplier_group_timer_result_sql = tep_db_query($supplier_group_timer_select_sql);
            if ($supplier_group_timer_row = tep_db_fetch_array($supplier_group_timer_result_sql)) {
                $time_res['status'] = $supplier_group_timer_row['normal_status'];

                $time_res['first_list_start_time'] = explode(":", $supplier_group_timer_row['first_list_start_time']);
                $time_res['first_list_end_time'] = explode(":", $supplier_group_timer_row['first_list_end_time']);
                $time_res['second_list_start_time'] = explode(":", $supplier_group_timer_row['second_list_start_time']);
                $time_res['second_list_end_time'] = explode(":", $supplier_group_timer_row['second_list_end_time']);

                if (tep_not_null($supplier_group_timer_row['first_list_edit_time'])) {
                    $time_res['first_list_edit_time'] = explode(':', $supplier_group_timer_row['first_list_edit_time']);
                    $time_res['first_edit_time'] = $supplier_group_timer_row['first_list_edit_time'];
                    $time_res['first_list_edit_anytime'] = 0;
                } else {
                    $time_res['first_list_edit_anytime'] = 1;
                }

                $time_res['auto_on'] = (int) $supplier_group_timer_row['auto_on'];
                $time_res['auto_off'] = (int) $supplier_group_timer_row['auto_off'];
                $time_res['current_status'] = $supplier_group_timer_row['current_status'];

                $time_res['first_start_timestamp'] = mktime((int) $time_res['first_list_start_time'][0], (int) $time_res['first_list_start_time'][1], (int) $time_res['first_list_start_time'][2]);
                $time_res['first_end_timestamp'] = mktime((int) $time_res['first_list_end_time'][0], (int) $time_res['first_list_end_time'][1], (int) $time_res['first_list_end_time'][2]);
                $time_res['first_start_time'] = $supplier_group_timer_row['first_list_start_time'];
                $time_res['first_end_time'] = $supplier_group_timer_row['first_list_end_time'];

                $time_res['second_start_timestamp'] = mktime((int) $time_res['second_list_start_time'][0], (int) $time_res['second_list_start_time'][1], (int) $time_res['second_list_start_time'][2]);
                $time_res['second_end_timestamp'] = mktime((int) $time_res['second_list_end_time'][0], (int) $time_res['second_list_end_time'][1], (int) $time_res['second_list_end_time'][2]);
                $time_res['second_start_time'] = $supplier_group_timer_row['second_list_start_time'];
                $time_res['second_end_time'] = $supplier_group_timer_row['second_list_end_time'];
            }
        } else {
            $time_res['status'] = $supplier_timer_row['supplier_purchase_mode'];
        }
    }

    return $time_res;
}

function tep_time_check($start_time, $end_time, $use_time = '') {
    if (!tep_not_null($start_time) || !tep_not_null($end_time)) {
        return false;
    }

    $start_time_array = explode(':', $start_time);
    $end_time_array = explode(':', $end_time);

    $start_time = ltrim($start_time_array[0], '0') . $start_time_array[1];
    $end_time = ltrim($end_time_array[0], '0') . $end_time_array[1];

    $start_time = (int) $start_time;
    $end_time = (int) $end_time;

    if (tep_not_null($use_time)) {
        $t_t_check = ltrim($use_time, '0');
        $t_t_check = (int) $t_t_check;
    } else {
        $t_t_check = date('H') . date('i');
        $t_t_check = ltrim($t_t_check, '0');
        $t_t_check = (int) $t_t_check;
    }

    if ($end_time > $start_time) {
        return ($t_t_check >= $start_time) && ($end_time >= $t_t_check);
    } else {
        return ($t_t_check >= $start_time) || ($t_t_check <= $end_time);
    }
}

function tep_verify_lists_time_status($start_time, $end_time, $date_time) {
    if (!tep_not_null($date_time))
        return false;

    list($date_str, $time_str) = explode(' ', $date_time);
    list($hr, $min, $sec) = explode(':', $time_str);

    $use_time = $hr . $min;

    if (tep_time_check($start_time, $end_time, $use_time)) {
        if (($days_lapse = tep_day_diff($date_time, date('Y-m-d H:i:s'))) != FALSE) {
            if ($days_lapse < 1) {
                return true;
            }
        }
    }

    return false;
}

function tep_get_supplier_pricing_setting($sup_grp_id, $list_id) {
    $setting_array = array();

    $pricing_setting_select_sql = "SELECT supplier_pricing_setting_key, supplier_pricing_setting_value FROM " . TABLE_SUPPLIER_PRICING_SETTING . " WHERE supplier_groups_id = '" . (int) $sup_grp_id . "' AND products_purchases_lists_id = '" . (int) $list_id . "'";
    $pricing_setting_result_sql = tep_db_query($pricing_setting_select_sql);

    while ($pricing_setting_row = tep_db_fetch_array($pricing_setting_result_sql)) {
        $setting_array[$pricing_setting_row["supplier_pricing_setting_key"]] = $pricing_setting_row["supplier_pricing_setting_value"];
    }

    return $setting_array;
}

function tep_get_supplier_pref_setting($sup_id) {
    $setting_array = array();

    $pref_setting_select_sql = "SELECT supplier_preferences_key, supplier_preferences_value FROM " . TABLE_SUPPLIER_PREFERENCES . " WHERE suppliers_id = '" . (int) $sup_id . "'";
    $pref_setting_result_sql = tep_db_query($pref_setting_select_sql);

    while ($pref_setting_row = tep_db_fetch_array($pref_setting_result_sql)) {
        $setting_array[$pref_setting_row["supplier_preferences_key"]] = $pref_setting_row["supplier_preferences_value"];
    }

    return $setting_array;
}

function tep_supplier_check_url($url) {
    return eregi_dep("^https?://[a-z0-9]([-_.]?[a-z0-9])+[.][a-z0-9][a-z0-9/=?.&\~_-]+$", $url);
}

function tep_get_previously_restocked_qty($product_id, $sup_grp_id, $list_id) {
    $list_info_select_sql = "SELECT products_purchases_lists_reference_date FROM " . TABLE_PRODUCTS_PURCHASES_LISTS . " WHERE products_purchases_lists_id = '" . (int) $list_id . "'";
    $list_info_result_sql = tep_db_query($list_info_select_sql);
    $list_info_row = tep_db_fetch_array($list_info_result_sql);

    /*     * ****************************************************************************
      List based deduction. Do not take into account those restock quantities
      for the same product in other lists.
     * **************************************************************************** */
    $restock_qty_select_sql = "	SELECT SUM(solp.products_received_quantity) AS restock_qty
								FROM " . TABLE_SUPPLIER_ORDER_LISTS . " AS sol 
								INNER JOIN " . TABLE_SUPPLIER . " AS s 
									ON (sol.suppliers_id=s.supplier_id AND s.supplier_groups_id='" . (int) $sup_grp_id . "')
								INNER JOIN " . TABLE_SUPPLIER_ORDER_LISTS_PRODUCTS . " AS solp 
									ON (sol.supplier_order_lists_id=solp.supplier_order_lists_id AND solp.supplier_order_lists_type=2 AND solp.products_id='" . (int) $product_id . "')
								WHERE sol.supplier_order_lists_date >= '" . (tep_not_null($list_info_row["products_purchases_lists_reference_date"]) ? trim($list_info_row["products_purchases_lists_reference_date"]) : date('Y-m-d H:i:s')) . "'
									AND sol.supplier_order_lists_status IN (2, 3) 
									AND sol.products_purchases_lists_id='" . tep_db_input($list_id) . "'";
    $restock_qty_result_sql = tep_db_query($restock_qty_select_sql);
    $restock_qty_row = tep_db_fetch_array($restock_qty_result_sql);

    return (int) $restock_qty_row["restock_qty"];
}

function tep_update_record_tags($filename, $rec_id, $status_id, $extra_tag) {
    if ($filename == FILENAME_SUPPLIER_PROGRESS_REPORT) {
        $orders_tag_select_sql = "SELECT orders_tag_ids FROM " . TABLE_SUPPLIER_TASKS_ALLOCATION . " WHERE orders_products_id = '" . tep_db_input($rec_id) . "'";
        $orders_tag_result_sql = tep_db_query($orders_tag_select_sql);
        $orders_tag_row = tep_db_fetch_array($orders_tag_result_sql);
        if (tep_not_null($orders_tag_row["orders_tag_ids"])) {
            $new_order_tags_array = array();
            $applicable_orders_tag_select_sql = "SELECT orders_tag_id FROM " . TABLE_ORDERS_TAG . " WHERE FIND_IN_SET(orders_tag_id, '" . $orders_tag_row["orders_tag_ids"] . "') AND FIND_IN_SET('" . (int) $status_id . "', orders_tag_status_ids) AND filename='" . tep_db_input(FILENAME_PROGRESS_REPORT) . "'";
            $applicable_orders_tag_result_sql = tep_db_query($applicable_orders_tag_select_sql);
            while ($applicable_orders_tag_row = tep_db_fetch_array($applicable_orders_tag_result_sql)) {
                $new_order_tags_array[] = $applicable_orders_tag_row["orders_tag_id"];
            }
            $new_tag_string = count($new_order_tags_array) > 0 ? implode(',', $new_order_tags_array) : '';
            tep_db_query("UPDATE " . TABLE_SUPPLIER_TASKS_ALLOCATION . " SET orders_tag_ids='" . $new_tag_string . "' WHERE orders_products_id='" . tep_db_input($rec_id) . "'");
        } else {
            if (tep_not_null($extra_tag)) {
                tep_db_query("UPDATE " . TABLE_SUPPLIER_TASKS_ALLOCATION . " SET orders_tag_ids='" . $new_status_tag . "' WHERE orders_products_id='" . tep_db_input($rec_id) . "'");
            }
        }
    }
}

function tep_check_category_ip_access($orders_products_id, $ip_address) {
    // default to allow
    $verify_ip_array = array('res' => '1');

    $pwl_ip_zone_select_sql = "	SELECT orders_custom_products_value
								FROM " . TABLE_ORDERS_CUSTOM_PRODUCTS . " 
								WHERE orders_custom_products_key = 'power_leveling_ip_zones' 
									AND orders_products_id ='" . tep_db_input($orders_products_id) . "'";
    $pwl_ip_zone_result_sql = tep_db_query($pwl_ip_zone_select_sql);
    $pwl_ip_zone_row = tep_db_fetch_array($pwl_ip_zone_result_sql);

    if (tep_not_null($pwl_ip_zone_row['orders_custom_products_value'])) {
        $ip_to_defined_ip_zones_id_select_sql = "	SELECT ip_to_defined_ip_zones_id
													FROM " . TABLE_IP_TO_DEFINED_IP_ZONES . " 
													WHERE defined_ip_zones_id IN (" . $pwl_ip_zone_row['orders_custom_products_value'] . ")";
        $ip_to_defined_ip_zones_id_result_sql = tep_db_query($ip_to_defined_ip_zones_id_select_sql);

        if (tep_db_num_rows($ip_to_defined_ip_zones_id_result_sql) > 0) {
            if (tep_valid_ip_zone($pwl_ip_zone_row['orders_custom_products_value'], $ip_address)) {
                ; // Use back default result
            } else {
                $zone_name_str = '';
                $zone_info_select_sql = "	SELECT defined_ip_zones_name
											FROM " . TABLE_DEFINED_IP_ZONES . " 
											WHERE defined_ip_zones_id IN (" . (tep_not_null($pwl_ip_zone_row['orders_custom_products_value']) ? $pwl_ip_zone_row['orders_custom_products_value'] : '') . ")";

                $zone_info_result_sql = tep_db_query($zone_info_select_sql);
                while ($zone_info_row = tep_db_fetch_array($zone_info_result_sql)) {
                    $zone_name_str .= $zone_info_row['defined_ip_zones_name'] . ', ';
                }
                $zone_name_str = trim($zone_name_str);
                if (substr($zone_name_str, -1) == ',')
                    $zone_name_str = substr($zone_name_str, 0, -1);
                $verify_ip_array = array('res' => '-1',
                    'msg' => sprintf(MSG_GAME_IP_ZONES, $zone_name_str));
            }
        } else {
            $verify_ip_array = array('res' => '-1',
                'msg' => ERROR_IP_NOT_ASSIGN_TO_ZONE);
        }
    } else {
        //$verify_ip_array = array(	'res' => '-1',
        //	'msg' => ERROR_IP_ZONE_ASSIGN);
        ; // Use back default result
    }

    return $verify_ip_array;
}

function tep_valid_ip_zone($pwl_ip_zones, $ip_address) {
    $user_ip_in_binary = tep_ip_in_binary_form($ip_address);

    $ip_lists_select_sql = "SELECT ip_address, subnet
							FROM " . TABLE_IP_TO_DEFINED_IP_ZONES . " 
							WHERE defined_ip_zones_id IN (" . (tep_not_null($pwl_ip_zones) ? $pwl_ip_zones : '') . ")";
    $ip_lists_result_sql = tep_db_query($ip_lists_select_sql);
    while ($ip_lists_row = tep_db_fetch_array($ip_lists_result_sql)) {
        $current_ip_in_binary = tep_ip_in_binary_form($ip_lists_row['ip_address']);

        $test_user_ip_str = substr($user_ip_in_binary, 0, ($ip_lists_row['subnet'] < 0 || $ip_lists_row['subnet'] > 32 ? 32 : $ip_lists_row['subnet']));
        if (strpos($current_ip_in_binary, $test_user_ip_str) === 0) { // Access from valid IP address
            return true;
            break;
        }
    }

    return false;
}

function tep_get_ip_address() {
    if (defined('SUPPLIER_USE_PROXY_SERVER') && SUPPLIER_USE_PROXY_SERVER == 'true') {
        if (isset($_SERVER)) {
            if (isset($_SERVER['HTTP_TRUE_CLIENT_IP'])) {
                $ip = $_SERVER['HTTP_TRUE_CLIENT_IP'];
            } else if (isset($_SERVER['HTTP_X_FORWARDED_FOR']) && tep_not_null($_SERVER['HTTP_X_FORWARDED_FOR'])) {
                $ip_array = explode(',', $_SERVER['HTTP_X_FORWARDED_FOR']);
                $ip = trim(end($ip_array));
            } else {
                $ip = $_SERVER['REMOTE_ADDR'];
            }
        } else {
            if (getenv('HTTP_TRUE_CLIENT_IP')) {
                $ip = getenv('HTTP_TRUE_CLIENT_IP');
            } else {
                $ip = getenv('REMOTE_ADDR');
            }
        }
    } else {
        if (getenv('HTTP_TRUE_CLIENT_IP')) {
            $ip = getenv('HTTP_TRUE_CLIENT_IP');
        } else {
            $ip = getenv('REMOTE_ADDR');
        }
    }
    return $ip;
}

function tep_ip_in_binary_form($ip) {
    $ip_bin_str = '';

    $ip_array = explode('.', $ip);

    for ($i = 0; $i < count($ip_array); $i++) {
        $this_ip_value = ((int) $ip_array[$i] < 0 || (int) $ip_array[$i] > 255) ? 0 : (int) $ip_array[$i];

        $ip_bin_str .= sprintf("%08s", decbin($this_ip_value));
    }

    return $ip_bin_str;
}

function tep_generate_game_currencies($money = 0, $currency_array, $format = '') {
    $currencies_generated_array = array();

    switch ($format) {
        case 'all':
            for ($currency_count = 0; $currency_count < sizeof($currency_array); $currency_count++) {
                $current_currency = (int) ($money / $currency_array[$currency_count]['value']);
                $currencies_generated_array[] = array($currency_array[$currency_count]['currency'] => $current_currency);
                $money -= ($current_currency * $currency_array[$currency_count]['value']);
            }
            break;
        default:
            for ($currency_count = 0; $currency_count < sizeof($currency_array); $currency_count++) {
                if ($currency_array[$currency_count]['currency'] == $format) {
                    //echo $money;
                    $currencies_generated_array[] = array($format => number_format($money / $currency_array[$currency_count]['value'], 4));
                }
            }
            break;
    }

    return $currencies_generated_array;
}

//Customers Status
// BOF: WebMakers.com Added: Downloads Controller
require(DIR_WS_FUNCTIONS . 'downloads_controller.php');
// EOF: WebMakers.com Added: Downloads Controller
// BOF IndvShip
//require(DIR_WS_FUNCTIONS . 'indvship_status.php');
// EOF
?>