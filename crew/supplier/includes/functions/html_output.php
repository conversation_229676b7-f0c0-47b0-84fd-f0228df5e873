<?
/*
  	$Id: html_output.php,v 1.8 2007/01/25 10:24:19 weichen Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2003 osCommerce
	
  	Released under the GNU General Public License
*/

////
// The HTML href link wrapper function
function tep_href_link($page = '', $parameters = '', $connection = 'NONSSL', $add_session_id = true) {
	if ($page == '') {
      	die('</td></tr></table></td></tr></table><br><br><font color="#ff0000"><b>Error!</b></font><br><br><b>Unable to determine the page link!<br><br>Function used:<br><br>tep_href_link(\'' . $page . '\', \'' . $parameters . '\', \'' . $connection . '\')</b>');
    }
    
    if ($connection == 'NONSSL') {
      	$link = HTTP_SERVER . DIR_WS_SUPPLIER;
    } else if ($connection == 'SSL') {
      	if (ENABLE_SSL == 'true') {
        	$link = HTTPS_SERVER . DIR_WS_SUPPLIER;
      	} else {
        	$link = HTTP_SERVER . DIR_WS_SUPPLIER;
      	}
    } else {
      	die('</td></tr></table></td></tr></table><br><br><font color="#ff0000"><b>Error!</b></font><br><br><b>Unable to determine connection method on a link!<br><br>Known methods: NONSSL SSL<br><br>Function used:<br><br>tep_href_link(\'' . $page . '\', \'' . $parameters . '\', \'' . $connection . '\')</b>');
    }
	
    if (tep_not_null($parameters)) {	      
		$link = $link . $page . '?' . $parameters . ($add_session_id ? (substr($parameters,-1)=='&' ? '' : '&') . SID : '');
    } else {
		$link = $link . $page . ($add_session_id ? '?' . SID : '');
    }
	
    while ( (substr($link, -1) == '&') || (substr($link, -1) == '?') ) $link = substr($link, 0, -1);
	
    return $link;
}

function tep_catalog_href_link($page = '', $parameters = '', $connection = 'NONSSL') {
	if ($connection == 'NONSSL') {
		$link = HTTP_CATALOG_SERVER . DIR_WS_CATALOG;
    } else if ($connection == 'SSL') {
      	if (ENABLE_SSL_CATALOG == 'true') {
        	$link = HTTP_CATALOG_SERVER . DIR_WS_CATALOG;
        	//$link = HTTPS_CATALOG_SERVER . DIR_WS_CATALOG;
      	} else {
        	$link = HTTP_CATALOG_SERVER . DIR_WS_CATALOG;
      	}
	} else {
		die('</td></tr></table></td></tr></table><br><br><font color="#ff0000"><b>Error!</b></font><br><br><b>Unable to determine connection method on a link!<br><br>Known methods: NONSSL SSL<br><br>Function used:<br><br>tep_href_link(\'' . $page . '\', \'' . $parameters . '\', \'' . $connection . '\')</b>');
    }
    
    if ($parameters == '') {
      	$link .= $page;
    } else {
      	$link .= $page . '?' . $parameters;
    }
	
    while ( (substr($link, -1) == '&') || (substr($link, -1) == '?') ) $link = substr($link, 0, -1);
	
    return $link;
}

function tep_admin_href_link($page = '', $parameters = '', $connection = 'NONSSL') {
	if ($connection == 'NONSSL') {
		$link = HTTP_ADMIN_SERVER . DIR_WS_ADMIN;
    } else if ($connection == 'SSL') {
      	if (ENABLE_SSL_ADMIN == 'true') {
        	$link = HTTPS_ADMIN_SERVER . DIR_WS_ADMIN;
      	} else {
        	$link = HTTP_ADMIN_SERVER . DIR_WS_ADMIN;
      	}
	} else {
		die('</td></tr></table></td></tr></table><br><br><font color="#ff0000"><b>Error!</b></font><br><br><b>Unable to determine connection method on a link!<br><br>Known methods: NONSSL SSL<br><br>Function used:<br><br>tep_href_link(\'' . $page . '\', \'' . $parameters . '\', \'' . $connection . '\')</b>');
    }
    
    if ($parameters == '') {
      	$link .= $page;
    } else {
      	$link .= $page . '?' . $parameters;
    }
	
    while ( (substr($link, -1) == '&') || (substr($link, -1) == '?') ) $link = substr($link, 0, -1);
	
    return $link;
}

////
// The HTML image wrapper function
function tep_image($src, $alt = '', $width = '', $height = '', $params = '') {
	$image = '<img src="' . $src . '" border="0" alt="' . $alt . '"';
    
    if ($alt) {
      	$image .= ' title=" ' . $alt . ' "';
    }
    if ($width) {
      	$image .= ' width="' . $width . '"';
    }
    if ($height) {
      	$image .= ' height="' . $height . '"';
    }
    if ($params) {
      	$image .= ' ' . $params;
    }
    $image .= '>';
	
    return $image;
}

////
// The HTML form submit button wrapper function
// Outputs a button in the selected language
function tep_image_submit($image, $alt = '', $parameters = '', $own_path='') {
	global $language;
	
    $image_submit = '<input type="image" src="' . tep_output_string(($own_path) ? $own_path.$image : DIR_WS_LANGUAGES . $language . '/images/buttons/' . $image) . '" border="0" alt="' . tep_output_string($alt) . '"';
	
    if (tep_not_null($alt)) $image_submit .= ' title=" ' . tep_output_string($alt) . ' "';
	
	if (tep_not_null($parameters)) {
		$image_submit .= ' ' . $parameters;
		
		if (strpos($parameters, 'onClick') === false) {
			//$image_submit .= " onClick=\"this.disabled=true; this.form.submit();\" ";
		}
	} else {
		//$image_submit .= " onClick=\"this.disabled=true; this.form.submit();\" ";
	}
	
    $image_submit .= '>';
	
    return $image_submit;
}

////
// Draw a 1 pixel black line
function tep_black_line() {
    return tep_image(DIR_WS_IMAGES . 'pixel_black.gif', '', '100%', '1');
}

////
// Output a separator either through whitespace, or with an image
function tep_draw_separator($image = 'pixel_black.gif', $width = '100%', $height = '1') {
	return tep_image(DIR_WS_IMAGES . $image, '', $width, $height);
}

////
// Output a function button in the selected language
function tep_image_button($image, $alt = '', $params = '') {
	global $language;
	$params .= ' class="inputButton"';
    return tep_image(DIR_WS_LANGUAGES . $language . '/images/buttons/' . $image, $alt, '', '', $params);
}

function tep_submit_button($name='', $alt = '', $parameters = '', $css_class = 'generalBtn', $btn_active = true) {
   	$submit_html = '<input type="SUBMIT" value="'.$name.'" class="'.$css_class.'" ';
	
    if (tep_not_null($alt)) $submit_html .= ' title=" ' . tep_output_string($alt) . ' "';
	
    if (tep_not_null($parameters)) $submit_html .= ' ' . $parameters;
    
	$submit_html .= ($btn_active == true ? ' onMouseOver="this.className=\''.$css_class.'Over'.'\'"' : '') .' onMouseOut="this.className=\''.$css_class.'\'" ';
    
    $submit_html .= '>';
	
    return $submit_html;
}

function tep_button($name, $alt = '', $url='', $parameters = '', $css_class = 'generalBtn', $btn_active = true) {
   	$button_html = '<input type="BUTTON" value="'.$name.'" class="'.$css_class.'" ';
	
    if (tep_not_null($alt)) $button_html .= ' title=" ' . tep_output_string($alt) . ' "';
	
    if (tep_not_null($parameters)) $button_html .= ' ' . $parameters;
	
	$button_html .= ($btn_active == true ? ' onMouseOver="this.className=\''.$css_class.'Over'.'\'"' : '') .' onMouseOut="this.className=\''.$css_class.'\'" ';
    
    if (tep_not_null($url))	$button_html .= 'onClick="window.location=\''.$url.'\'"';
    
    $button_html .= '>';
	
    return $button_html;
}

function tep_get_country_list($name, $selected = '', $parameters = '') {
	$countries_array = array(array('id' => '', 'text' => PULL_DOWN_DEFAULT));
    $countries = tep_get_countries();
	
    for ($i=0, $n=sizeof($countries); $i<$n; $i++) {
      	$countries_array[] = array('id' => $countries[$i]['id'], 'text' => $countries[$i]['text']);
    }
	
    return tep_draw_pull_down_menu($name, $countries_array, $selected, $parameters);
}

function tep_js_zone_list($country, $form, $field, $selection_type='') {
	$countries_query = tep_db_query("select distinct zone_country_id from " . TABLE_ZONES . " order by zone_country_id");
    $num_country = 1;
    $output_string = '';
    while ($countries = tep_db_fetch_array($countries_query)) {
      	if ($num_country == 1) {
        	$output_string .= '  if (' . $country . ' == "' . $countries['zone_country_id'] . '") {' . "\n";
      	} else {
        	$output_string .= '  } else if (' . $country . ' == "' . $countries['zone_country_id'] . '") {' . "\n";
      	}
		
      	$states_query = tep_db_query("select zone_name, zone_id from " . TABLE_ZONES . " where zone_country_id = '" . $countries['zone_country_id'] . "' order by zone_name");
		
      	$num_state = 1;
      	while ($states = tep_db_fetch_array($states_query)) {
        	if ($num_state == '1') {
        		if ($selection_type == 'multiple')
        			$output_string .= '    ' . $form . '.elements[\'' . $field . '\'].options[0] = new Option("' . PLEASE_SELECT . '", "0");' . "\n";
        		else
        			$output_string .= '    ' . $form . '.' . $field . '.options[0] = new Option("' . PLEASE_SELECT . '", "0");' . "\n";
        	}
        	if ($selection_type == 'multiple')
        		$output_string .= '    ' . $form . '.elements[\'' . $field . '\'].options[' . $num_state . '] = new Option("' . $states['zone_name'] . '", "' . $states['zone_id'] . '");' . "\n";
        	else
        		$output_string .= '    ' . $form . '.' . $field . '.options[' . $num_state . '] = new Option("' . $states['zone_name'] . '", "' . $states['zone_id'] . '");' . "\n";
        	
        	$num_state++;
      	}
      	$num_country++;
	}
    $output_string .= '  } else {' . "\n" .
                      ($selection_type == 'multiple' ? ('    ' . $form . '.elements[\'' . $field . '\'].options[0] = new Option("' . TYPE_BELOW . '", "0");' . "\n") : ('    ' . $form . '.' . $field . '.options[0] = new Option("' . TYPE_BELOW . '", "0");' . "\n") ).
                      '  }' . "\n";
	
    return $output_string;
}

////
// Output a form
function tep_draw_form($name, $action, $parameters = '', $method = 'post', $params = '') {
	$form = '<form name="' . tep_output_string($name) . '" action="';
    if (tep_not_null($parameters)) {
      	$form .= tep_href_link($action, $parameters);
    } else if (tep_not_null($action)) {
      	$form .= tep_href_link($action);
    }
    $form .= '" method="' . tep_output_string($method) . '"';
    if (tep_not_null($params)) {
      	$form .= ' ' . $params;
    }
    $form .= '>';
	
	if (SID && strtolower($method) == 'get') $form .= tep_draw_hidden_field(tep_session_name(), tep_session_id());
	
    return $form;
}

////
// Output a form input field
function tep_draw_input_field($name, $value = '', $parameters = '', $required = false, $type = 'text', $reinsert_value = true) {
    $field = '<input type="' . tep_output_string($type) . '" name="' . tep_output_string($name) . '"';
	
    if (isset($GLOBALS[$name]) && ($reinsert_value == true) && is_string($GLOBALS[$name])) {
      	$field .= ' value="' . tep_output_string(stripslashes($GLOBALS[$name])) . '"';
    } else if (tep_not_null($value)) {
      	$field .= ' value="' . tep_output_string($value) . '"';
    }
    
    if (tep_not_null($parameters)) $field .= ' ' . $parameters;
	
    $field .= '>';
	
    if ($required == true) $field .= TEXT_FIELD_REQUIRED;
	
    return $field;
}

////
// Output a form password field
function tep_draw_password_field($name, $value = '', $required = false, $parameters = '') {
	$field = tep_draw_input_field($name, $value, $parameters . ' maxlength="40"', $required, 'password', false);
	
    return $field;
}

////
// Output a form filefield
function tep_draw_file_field($name, $parameters = '', $required = false) {
    $field = tep_draw_input_field($name, '', $parameters, $required, 'file');
	
    return $field;
}

// Output a selection field - alias function for tep_draw_checkbox_field() and tep_draw_radio_field()
function tep_draw_selection_field($name, $type, $value = '', $checked = false, $compare = '', $parameter = '') {
    $selection = '<input type="' . $type . '" name="' . $name . '"';
    if ($value != '') {
      	$selection .= ' value="' . $value . '"';
    }
    if ( ($checked == true) || ($GLOBALS[$name] == 'on') || ($value && ($GLOBALS[$name] == $value)) || ($value && ($value == $compare)) ) {
      	$selection .= ' CHECKED';
    }
    if ($parameter != '') {
      	$selection .= ' ' . $parameter;
    }   
    $selection .= '>';
	
    return $selection;
}

////
// Output a form checkbox field
function tep_draw_checkbox_field($name, $value = '', $checked = false, $compare = '', $parameter = '') {
	return tep_draw_selection_field($name, 'checkbox', $value, $checked, $compare, $parameter);
}

////
// Output a form radio field
function tep_draw_radio_field($name, $value = '', $checked = false, $compare = '', $parameter = '') {
    return tep_draw_selection_field($name, 'radio', $value, $checked, $compare, $parameter);
}
//Admin end

////
// Output a form textarea field
function tep_draw_textarea_field($name, $wrap, $width, $height, $text = '', $parameters = '', $reinsert_value = true) {
	$field = '<textarea name="' . tep_output_string($name) . '" wrap="' . tep_output_string($wrap) . '" cols="' . tep_output_string($width) . '" rows="' . tep_output_string($height) . '"';
	
    if (tep_not_null($parameters)) $field .= ' ' . $parameters;
	
    $field .= '>';
	
    if ( (isset($GLOBALS[$name])) && ($reinsert_value == true) ) {
      	$field .= stripslashes($GLOBALS[$name]);
    } else if (tep_not_null($text)) {
      	$field .= $text;
    }
	
    $field .= '</textarea>';
	
    return $field;
}

////
// Output a form hidden field
function tep_draw_hidden_field($name, $value = '', $parameters = '') {
    $field = '<input type="hidden" name="' . tep_output_string($name) . '"';
	
    if (tep_not_null($value)) {
      	$field .= ' value="' . tep_output_string($value) . '"';
    } else if (isset($GLOBALS[$name]) && is_string($GLOBALS[$name])) {
      	$field .= ' value="' . tep_output_string(stripslashes($GLOBALS[$name])) . '"';
    }
	
    if (tep_not_null($parameters)) $field .= ' ' . $parameters;
	
    $field .= '>';
	
    return $field;
}

////
// Output a form pull down menu
function tep_draw_pull_down_menu($name, $values, $default = '', $parameters = '', $required = false) {
	$field = '<select name="' . tep_output_string($name) . '"';
	
    if (tep_not_null($parameters)) $field .= ' ' . $parameters;
	
    $field .= '>';
	
    if (empty($default) && isset($GLOBALS[$name])) $default = stripslashes($GLOBALS[$name]);
	
    for ($i=0, $n=sizeof($values); $i<$n; $i++) {
      	$field .= '<option value="' . tep_output_string($values[$i]['id']) . '"';
      	if ($default == $values[$i]['id']) {
        	$field .= ' SELECTED';
      	}
		
      	$field .= '>' . tep_output_string($values[$i]['text'], array('"' => '&quot;', '\'' => '&#039;', '<' => '&lt;', '>' => '&gt;')) . '</option>';
    }
    $field .= '</select>';
	
    if ($required == true) $field .= TEXT_FIELD_REQUIRED;
	
    return $field;
}

function tep_draw_comment_select($field_name="commentID") {
	$result = tep_db_query("SELECT orders_comments_id, orders_comments_title FROM " . TABLE_ORDERS_COMMENTS . " WHERE orders_comments_status='1' ORDER BY orders_comments_sort_order;");
	
	echo '<select name="'.$field_name.'" onchange="setOrderComment(this)">';
	echo "<option value='0' selected>" . TEXT_SELECT_PREDEFINED_COMMENT . "</option>";
	
	while ($row = tep_db_fetch_array($result)) {
		echo "<option value='$row[orders_comments_id]'>" .  $row['orders_comments_title']  . "</option>";	
	}	
	echo "</select>";
}

function tep_draw_date_box($name, $from_date, $period, $default='') {
	$date_array = array();
	list($y, $m, $d) = explode('-', $from_date);
	$m = (int)$m;
	$d = (int)$d;
	
	$month_day = date('t', mktime(0, 0, 0, $m, $d, $y));
	
	$days_this_month = ($month_day - $d) + 1;
	$days_next_month = $period - $days_this_month;
	
	$current_month_days_loop = $d + ($period > $days_this_month ? $days_this_month : $period);
	for ($day_cnt=(int)$d; $day_cnt <= $current_month_days_loop; $day_cnt++) {
		$date_array[] = array('id' => $y . '-' . ((int)$m < 10 ? '0'.(int)$m : (int)$m) . '-' . ($day_cnt < 10 ? '0'.$day_cnt : $day_cnt), 'text' => $y . '-' . ((int)$m < 10 ? '0'.(int)$m : (int)$m) . '-' . ($day_cnt < 10 ? '0'.$day_cnt : $day_cnt));
	}
	
	while ($days_next_month > 0) {
		$m++;
		if ($m > 12) {
			$m = 1;
			$y++;
		}
		
		$month_day = date('t', mktime(0, 0, 0, $m, $d, $y));
		
		$month_total_days = ($days_next_month > $month_day) ? $month_day : $days_next_month;
		for ($day_cnt=1; $day_cnt <= $month_total_days; $day_cnt++) {
			$date_array[] = array('id' => $y . '-' . ((int)$m < 10 ? '0'.(int)$m : (int)$m) . '-' . ($day_cnt < 10 ? '0'.$day_cnt : $day_cnt), 'text' => $y . '-' . ((int)$m < 10 ? '0'.(int)$m : (int)$m) . '-' . ($day_cnt < 10 ? '0'.$day_cnt : $day_cnt));
		}
		
		$days_next_month -= $month_day;
	}
	
	return tep_draw_pull_down_menu($name, $date_array, $default, 'onChange=""');
}

function tep_switch_image($status,$onURL,$offURL) {
	$status = (bool)$status;
	
	if ($status) {
		echo tep_image(DIR_WS_IMAGES . 'icon_status_green.gif', IMAGE_ICON_STATUS_GREEN, 10, 10) . '&nbsp;&nbsp;<a href="'.$offURL.'">' . tep_image(DIR_WS_IMAGES . 'icon_status_red_light.gif', IMAGE_ICON_STATUS_RED_LIGHT, 10, 10) . '</a>';
	} else {
		echo '<a href="'.$onURL.'">'.tep_image(DIR_WS_IMAGES . 'icon_status_green_light.gif', IMAGE_ICON_STATUS_GREEN, 10, 10) . '</a>&nbsp;&nbsp;' . tep_image(DIR_WS_IMAGES . 'icon_status_red.gif', IMAGE_ICON_STATUS_RED_LIGHT, 10, 10);
	}
}

function tep_display_category_path($path, $id, $id_type='catalog', $safe_quote=true) {
	$cat_display_level_setting = '';
	
	$cat_cfg_array = tep_get_cfg_setting($id, $id_type, 'BUYBACK_SUPPLIER_DISPLAY_LEVELS');
	$cat_display_level_setting = $cat_cfg_array['BUYBACK_SUPPLIER_DISPLAY_LEVELS'];
	
	if (!tep_not_null($cat_display_level_setting))	return $path;
	
	$level_array = array();
	
	$level_array = explode(',', $cat_display_level_setting);
	$path_array = explode('>', $path);
	$ret = array();
	
	for ($i=0; $i < count($level_array); $i++) {
		$level_array[$i] = trim($level_array[$i]);
		$level_array[$i] = (int)$level_array[$i];
		if ($level_array[$i] > 0) {
			$index = $level_array[$i]-1;
			if (trim($path_array[$index]) != '')	$ret[] = trim($path_array[$index]);
		}
	}
	
	return $safe_quote ? implode(" &gt; ",$ret) : implode(" > ",$ret);
}
?>