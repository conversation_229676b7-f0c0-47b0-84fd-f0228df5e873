<?php
/*
  $Id: localization.php,v 1.2 2007/01/25 10:24:49 weichen Exp $

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2003 osCommerce

  Released under the GNU General Public License
*/

function tep_get_currencies_array($omit_currency_array='') {
	$currency_array = array();
	
	if (!is_array($omit_currency_array))	$omit_currency_array = array();
	
	$currency_select_sql = "SELECT currencies_id, code, title
							FROM " . TABLE_CURRENCIES . " 
							WHERE code NOT IN ('".implode("', '", $omit_currency_array)."') 
							ORDER BY code";
	$currency_result_sql = tep_db_query($currency_select_sql);
	
	while ($currency_row = tep_db_fetch_array($currency_result_sql)) {
		$currency_array[] = array('id' => $currency_row['currencies_id'], 'text' => $currency_row['code'].' - '.$currency_row['title']);
	}
	
	return $currency_array;
}

function tep_user_balance($user_id, $user_role) {
	$user_balance_array = array();
	
	$store_account_balance_select_sql = "	SELECT store_account_balance_currency, store_account_balance_amount 
											FROM " . TABLE_STORE_ACCOUNT_BALANCE . " 
											WHERE user_id = '" . tep_db_input($user_id) . "' 
												AND user_role = '" . tep_db_input($user_role) . "' 
											ORDER BY store_account_balance_currency";
	$store_account_balance_result_sql = tep_db_query($store_account_balance_select_sql);
	
    while ($store_account_balance_row = tep_db_fetch_array($store_account_balance_result_sql)) {
    	$user_balance_array[$store_account_balance_row['store_account_balance_currency']] = $store_account_balance_row['store_account_balance_amount'];
    }
    
    return $user_balance_array;
}
?>
