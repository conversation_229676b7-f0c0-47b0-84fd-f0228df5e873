<?php

function lua_parse($lua_content) {
	// Parse the contents of the array
	$stack = array( array( '',  array() ) );
	$stack_pos = 0;
	$last_line = '';
	
	foreach( $lua_content as $line ) {
		// join lines ending in \\ together
		if (substr($line, -2, 1) == '\\') {
			$last_line .= substr($line, 0, -2) . "\n";
			continue;
		}
		
		if ($last_line!='') {
			$line = trim($last_line . $line);
			$last_line = '';
		} else {
			$line = trim($line);
		}
		
		// Look for end of an array
		if (isset($line[0]) && $line[0] == '}' ) {
			$hash = $stack[$stack_pos];
			unset($stack[$stack_pos]);
			$stack_pos--;
			$stack[$stack_pos][1][$hash[0]] = $hash[1];
			unset($hash);
		} else { // Handle other cases
			// Check if the key is given
			if( strpos($line, '=')) {
				list($name, $value) = explode('=', $line, 2);
				$name = trim($name);
				$value = trim($value,', ');
				
				if($name[0] == '[') {
					$name = trim($name, '[]"');
				}
			} else { // Otherwise we'll have to make one up for ourselves
				$value = $line;
				
				if( empty($stack[$stack_pos][1]) ) {
					$name = 1;
				} else {
					$name = max(array_keys($stack[$stack_pos][1]))+1;
				}
				
				if( strpos($line,'-- [') ) {
					$value = explode('-- [', $value);
					array_pop($value);
					$value = implode('-- [', $value);
				}
				$value = trim($value,', ');
			}
			
			if ($value == '{' ) {
				$stack_pos++;
				$stack[$stack_pos] = array($name, array());
			} else {
				if (isset($value[0]) && $value[0] == '"') {
					$value = substr($value, 1, -1);
				} else if($value == 'true') {
					$value = true;
				} else if($value == 'false') {
					$value = false;
				} else if($value == 'nil') {
					$value = NULL;
				}
				$stack[$stack_pos][1][$name] = $value;
			}
		}
	}
	return($stack[0][1]);
}

function update_char($name, $data, $orders_products_id, $lua_contents) {
	
	$char_name = tep_db_prepare_input($name);
	$server = tep_db_prepare_input($data['Server']);
	
	$game_char_id_select_sql = "	SELECT game_char_id 
									FROM " . TABLE_GAME_CHAR . " 
									WHERE name = '" . tep_db_input($char_name) . "' 
										AND server = '" . tep_db_input($server) . "'
										AND orders_products_id ='" . tep_db_input($orders_products_id) . "'";
									
	$game_char_id_result_sql = tep_db_query($game_char_id_select_sql);
	$game_char_id_row = tep_db_fetch_array($game_char_id_result_sql);
	
	if (tep_not_null($game_char_id_row['game_char_id'])) {
		$game_char_id = $game_char_id_row['game_char_id'];
	} else {
		$sql_data_array = array('name' => $char_name,
								'server' => $server,
								'orders_products_id' => $orders_products_id
								);
								
		tep_db_perform(TABLE_GAME_CHAR, $sql_data_array);
		$game_char_id = tep_db_insert_id();
	}
	
	$sql_data_array = array('game_char_id' => $game_char_id,
							'stat_int' => (isset($data['Attributes']['Stats']['Intellect']) ? $data['Attributes']['Stats']['Intellect'] : ''),
							'stat_agl' => (isset($data['Attributes']['Stats']['Agility']) ? $data['Attributes']['Stats']['Agility'] : ''),
							'stat_sta' => (isset($data['Attributes']['Stats']['Stamina']) ? $data['Attributes']['Stats']['Stamina'] : ''),
							'stat_str' => (isset($data['Attributes']['Stats']['Strength']) ? $data['Attributes']['Stats']['Strength'] : ''),
							'stat_spr' => (isset($data['Attributes']['Stats']['Spirit']) ? $data['Attributes']['Stats']['Spirit'] : ''),
							'guild_name' => (isset($data['Guild']['GuildName']) ? $data['Guild']['GuildName'] : ''),
							'guild_title' => (isset($data['Guild']['Title']) ? $data['Guild']['Title'] : ''),
							'guild_rank' => (isset($data['Guild']['Rank']) ? $data['Guild']['Rank'] : ''),
							'race' => (isset($data['Race']) ? $data['Race'] : ''),
							'res_holy' => (isset($data["Attributes"]['Resists']['Holy']) ? $data["Attributes"]['Resists']['Holy'] : ''),
							'res_frost' => (isset($data["Attributes"]['Resists']['Frost']) ? $data["Attributes"]['Resists']['Frost'] : ''),
							'res_arcane' => (isset($data["Attributes"]['Resists']['Arcane']) ? $data["Attributes"]['Resists']['Arcane'] : ''),
							'res_fire' => (isset($data["Attributes"]['Resists']['Fire']) ? $data["Attributes"]['Resists']['Fire'] : ''),
							'res_shadow' => (isset($data["Attributes"]['Resists']['Shadow']) ? $data["Attributes"]['Resists']['Shadow'] : ''),
							'res_nature' => (isset($data["Attributes"]['Resists']['Nature']) ? $data["Attributes"]['Resists']['Nature'] : ''),
							'armor' => (isset($data["Defense"]['Armor']) ? $data["Defense"]['Armor'] : ''),
							'level' => (isset($data['Level']) ? $data['Level'] : ''),
							'defense' => (isset($data["Defense"]['Defense']) ? $data["Defense"]['Defense'] : ''),
							'talent_points' => (isset($data['TalentPoints']) ? $data['TalentPoints'] : ''),
							'money_c' => (isset($data['Money']['Copper']) ? $data['Money']['Copper'] : ''),
							'money_s' => (isset($data['Money']['Silver']) ? $data['Money']['Silver'] : ''),
							'money_g' => (isset($data['Money']['Gold']) ? $data['Money']['Gold'] : ''),
							'exp' => (isset($data['Experience']) ? $data['Experience'] : ''),
							'class' => (isset($data['Class']) ? $data['Class'] : ''),
							'health' => (isset($data['Health']) ? $data['Health'] : ''),
							'melee_power' => (isset($data['Attributes']["Melee"]['AttackPower']) ? $data['Attributes']["Melee"]['AttackPower'] : ''),
							'melee_rating' => (isset($data['Attributes']["Melee"]["MainHand"]["AttackSkill"]) ? $data['Attributes']["Melee"]["MainHand"]["AttackSkill"] : ''),
							'melee_range' => (isset($data['Attributes']["Melee"]["MainHand"]['DamageRange']) ? $data['Attributes']["Melee"]["MainHand"]['DamageRange'] : ''),
							'melee_range_tooltip' => (isset($data['Attributes']["Melee"]['DamageRangeTooltip']) ? tooltip($data['Attributes']["Melee"]['DamageRangeTooltip']) : ''),
							'melee_power_tooltip' => (isset($data['Attributes']["Melee"]['AttackPowerTooltip']) ? tooltip($data['Attributes']["Melee"]['AttackPowerTooltip']) : ''),
							'ranged_power' => (isset($data['Attributes']["Ranged"]['AttackPower']) ? $data['Attributes']["Ranged"]['AttackPower'] : ''),
							'ranged_rating' => (isset($data['Attributes']["Ranged"]['AttackRating']) ? $data['Attributes']["Ranged"]['AttackRating'] : ''),
							'ranged_range' => (isset($data['Attributes']["Ranged"]['DamageRange']) ? $data['Attributes']["Ranged"]['DamageRange'] : ''),
							'ranged_range_tooltip' => (isset($data['Attributes']["Ranged"]['DamageRangeTooltip']) ? tooltip($data['Attributes']["Ranged"]['DamageRangeTooltip']) : ''),
							'ranged_power_tooltip' => (isset($data['Attributes']["Ranged"]['AttackPowerTooltip']) ? tooltip($data['Attributes']["Ranged"]['AttackPowerTooltip']) : ''),
							'version' => (isset($data['CPversion']) ? $data['CPversion'] : ''),
							'game_char_history_date' => 'now()'
							);
	
							
	tep_db_perform(TABLE_GAME_CHAR_HISTORY, $sql_data_array);
	
	$game_char_history_id = tep_db_insert_id();
	
	// Start Equipments
	$sql_data_array = array('game_char_id' => $game_char_id,
							'game_char_history_id' => $game_char_history_id,
							'char_item_parent' => 'equip'
							);
	
	tep_db_perform(TABLE_CHAR_ITEM_HISTORY, $sql_data_array);
	
	$item_char_item_history_id = tep_db_insert_id();
	
	if (isset($data['Equipment'])) {
		$equip = $data['Equipment'];
		foreach(array_keys($equip) as $slot_name) {
			$sql_data_array = array('char_item_history_id' => $item_char_item_history_id,
									'char_item_storage_name' => (isset($equip[$slot_name]['Name']) ? $equip[$slot_name]['Name'] : ''),
									'char_item_storage_slot' => (isset($slot_name) ? $slot_name : ''),
									'char_item_storage_color' => (isset($equip[$slot_name]['Color']) ? $equip[$slot_name]['Color'] : ''),
									'char_item_storage' => (isset($equip[$slot_name]['Item']) ? $equip[$slot_name]['Item'] : ''),
									'char_item_storage_texture' => (isset($equip[$slot_name]['Icon']) ? 'Interface\\Icons\\' . $equip[$slot_name]['Icon'] : ''),
									'char_item_storage_tooltip' => (isset($equip[$slot_name]['Tooltip']) ? tooltip($equip[$slot_name]['Tooltip']) : '')
									);
			
			tep_db_perform(TABLE_CHAR_ITEM_STORAGE, $sql_data_array);
		}
	} // End Equipments
	
	if (isset($data['Bank'])) { // Start Bank
		$bank = $data['Bank'];
		
		foreach(array_keys($bank) as $bank_bag_name) {
			if ($bank_bag_name != 'Contents' && substr($bank_bag_name, 0, 3) == 'Bag') {
				$bank_bag = $bank[$bank_bag_name];
				
				$sql_data_array = array('game_char_id' => $game_char_id,
										'game_char_history_id' => $game_char_history_id,
										'char_item_name' => (isset($bank[$bank_bag_name]['Name']) ? $bank[$bank_bag_name]['Name'] : ''),
										'char_item_parent' => (($bank_bag_name == 'Bag0') ? 'bank' : 'bank'.$bank_bag_name),
										'char_item_slot_available' => (isset($bank[$bank_bag_name]['Slots']) ? $bank[$bank_bag_name]['Slots'] : ''),
										'char_item_color' => (isset($bank[$bank_bag_name]['Color']) ? $bank[$bank_bag_name]['Color'] : ''),
										'char_item' => (isset($bank[$bank_bag_name]['Item']) ? $bank[$bank_bag_name]['Item'] : ''),
										'char_item_texture' => (isset($bank[$bank_bag_name]['Icon']) ? 'Interface\\Icons\\' . $bank[$bank_bag_name]['Icon'] : ''),
										'char_item_tooltip' => (isset($bank[$bank_bag_name]['Tooltip']) ? tooltip($bank[$bank_bag_name]['Tooltip']) : '')
										);
										
				tep_db_perform(TABLE_CHAR_ITEM_HISTORY, $sql_data_array);
				$char_item_history_id_bank_bag = tep_db_insert_id();
				
				if (isset($bank_bag['Contents'])) {
					foreach(array_keys($bank_bag['Contents']) as $slot_num) {
						$slot = $bank_bag['Contents'][$slot_num];
						
						$sql_data_array = array('char_item_history_id' => $char_item_history_id_bank_bag,
												'char_item_storage_name' => (isset($slot['Name']) ? $slot['Name'] : ''),
												'char_item_storage_slot' => $slot_num,
												'char_item_storage_color' => (isset($slot['Color']) ? $slot['Color'] : ''),
												'char_item_storage' => (isset($slot['Item']) ? $slot['Item'] : ''),
												'char_item_storage_texture' => (isset($slot['Icon']) ? 'Interface\\Icons\\' . $slot['Icon'] : ''),
												'char_item_storage_quantity' => (isset($slot['Quantity']) ? $slot['Quantity'] : ''),
												'char_item_storage_tooltip' => (isset($slot['Tooltip']) ? tooltip($slot['Tooltip']) : '')
												);
												
						tep_db_perform(TABLE_CHAR_ITEM_STORAGE, $sql_data_array);
					}
				}
			}
		}
	}
	
	if (isset($data['Inventory'])) { // Start Inventory
		$inv = $data['Inventory'];
		foreach(array_keys($inv) as $bag_name) {
			$bag = $inv[$bag_name];
			
			$sql_data_array = array('game_char_id' => $game_char_id,
									'game_char_history_id' => $game_char_history_id,
									'char_item_name' => (isset($inv[$bag_name]['Name']) ? $inv[$bag_name]['Name'] : ''),
									'char_item_parent' => $bag_name,
									'char_item_slot_available' => (isset($inv[$bag_name]['Slots']) ? $inv[$bag_name]['Slots'] : ''),
									'char_item_color' => (isset($inv[$bag_name]['Color']) ? $inv[$bag_name]['Color'] : ''),
									'char_item' => (isset($inv[$bag_name]['Item']) ? $inv[$bag_name]['Item'] : ''),
									'char_item_texture' => (isset($inv[$bag_name]['Icon']) ? 'Interface\\Icons\\' . $inv[$bag_name]['Icon'] : ''),
									'char_item_tooltip' => (isset($inv[$bag_name]['Tooltip']) ? tooltip($inv[$bag_name]['Tooltip']) : '')
									);
									
			tep_db_perform(TABLE_CHAR_ITEM_HISTORY, $sql_data_array);
			$char_item_history_id = tep_db_insert_id();
			
			if (isset($bag['Contents'])) { // Start In
				foreach(array_keys($bag['Contents']) as $slot_name) {
					$slot = $bag['Contents'][$slot_name];
					
					$sql_data_array = array('char_item_history_id' => $char_item_history_id,
											'char_item_storage_name' => (isset($slot['Name']) ? $slot['Name'] : ''),
											'char_item_storage_slot' => $slot_name,
											'char_item_storage_color' => (isset($slot['Color']) ? $slot['Color'] : ''),
											'char_item_storage' => (isset($slot['Item']) ? $slot['Item'] : ''),
											'char_item_storage_texture' => (isset($slot['Icon']) ? 'Interface\\Icons\\' . $slot['Icon'] : ''),
											'char_item_storage_quantity' => (isset($slot['Quantity']) ? $slot['Quantity'] : ''),
											'char_item_storage_tooltip' => (isset($slot['Tooltip']) ? tooltip($slot['Tooltip']) : '')
											);
											
					tep_db_perform(TABLE_CHAR_ITEM_STORAGE, $sql_data_array);
				}
			}
		}
	} // End Inventory
	
	// Start Skill
	if (isset($data['Skills'])) {
		foreach (array_keys($data['Skills']) as $skill_type) {
			$sub_skill = $data['Skills'][$skill_type];
			$order = $sub_skill['Order'];
			
			foreach (array_keys($sub_skill) as $skill_name) {
				if($skill_name != 'Order') {
					$sql_data_array = array('game_char_id' => $game_char_id,
											'game_char_history_id' => $game_char_history_id,
											'char_skill_type' => $skill_type,
											'char_skill_name' => $skill_name,
											'char_skill_order' => $order,
											'char_skill_level' => $sub_skill[$skill_name]
											);
											
					tep_db_perform(TABLE_CHAR_SKILL_HISTORY, $sql_data_array);
				}
			}
		}
	} // End Skill
	
	// Start Quest
	if (isset($data['Quests'])) {
		foreach (array_keys($data['Quests']) as $quest_location) {
		 	$quest_header = $data['Quests'][$quest_location];
		 	$orders = isset($quest_header['Order']) ? $quest_header['Order'] : '';
		 	
		 	foreach (array_keys($quest_header) as $order) {
		 		if ($order != 'Order') {
		 			$sql_data_array = array('game_char_id' => $game_char_id,
		 									'game_char_history_id' => $game_char_history_id,
											'char_quest_location' => $quest_location,
											'char_quest_order' => $orders,
											'char_quest_level' => (isset($data['Quests'][$quest_location][$order]['Level']) ? $data['Quests'][$quest_location][$order]['Level'] : ''),
											'char_quest_title' => (isset($data['Quests'][$quest_location][$order]['Title']) ? $data['Quests'][$quest_location][$order]['Title'] : '')
											);
											
					tep_db_perform(TABLE_CHAR_QUEST_HISTORY, $sql_data_array);
		 		}
			}
		}
	} // End Quest
	
	// Start Honor
	if (isset($data['Honor'])) {
		$sql_data_array = array('game_char_id' => $game_char_id,
								'game_char_history_id' => $game_char_history_id,
								'current_honor_description' => (isset($data['Honor']['Current']['Rank']) ? '(Rank '. $data['Honor']['Current']['Rank'] . ')' : ''),
								'current_honor_progress' => (isset($data['Honor']['Current']['Progress']) ? $data['Honor']['Current']['Progress'] : ''),
								'current_honor' => (isset($data['Honor']['Current']['HonorPoints']) ? $data['Honor']['Current']['HonorPoints'] : 0),
								'current_honor_arena' => (isset($data['Honor']['Current']['ArenaPoints']) ? $data['Honor']['Current']['ArenaPoints'] : 0),
								'current_honor_texture' => (isset($data['Honor']['Current']['Icon']) ? 'Interface\\Icons\\' . $data['Honor']['Current']['Icon'] : ''),
								'current_honor_rank' => (isset($data['Honor']['Current']['Name']) ? $data['Honor']['Current']['Name'] : ''),
								'yesterday_honorable_kill' => (isset($data['Honor']['Yesterday']['HK']) ? $data['Honor']['Yesterday']['HK'] : ''),
								'yesterday_honor' => (isset($data['Honor']['Yesterday']['CP']) ? $data['Honor']['Yesterday']['CP'] : ''),
								'life_time_honor_name' => (isset($data['Honor']['Lifetime']['Name']) ? $data['Honor']['Lifetime']['Name'] : ''),
								'life_time_honorable_kill' => (isset($data['Honor']['Lifetime']['HK']) ? $data['Honor']['Lifetime']['HK'] : ''),
								'life_time_rank' => (isset($data['Honor']['Lifetime']['Rank']) ? $data['Honor']['Lifetime']['Rank'] : ''),
								'session_honorable_kill' => (isset($data['Honor']['Session']['HK']) ? $data['Honor']['Session']['HK'] : ''),
								'session_honor' => (isset($data['Honor']['Session']['CP']) ? $data['Honor']['Session']['CP'] : '')
								);
							
		tep_db_perform(TABLE_CHAR_HONOR_HISTORY, $sql_data_array);
	} // End Honor
	
	// Start Reputation
	if (isset($data['Reputation'])) {
		$sql_data_array = array('game_char_id' => $game_char_id,
								'game_char_history_id' => $game_char_history_id
								);
								
		tep_db_perform(TABLE_CHAR_REPUTATION_HISTORY, $sql_data_array);
		
		$char_reputation_history_id = tep_db_insert_id();
		
		foreach ($data['Reputation'] as $char_reputation_detail_category => $char_reputation_detail_name_value_array) {
			if ($char_reputation_detail_category != 'Count') {
				foreach ($char_reputation_detail_name_value_array as $char_reputation_detail_name => $char_reputation_detail_name_value_array) {
					
					$sql_data_array = array('char_reputation_history_id' => $char_reputation_history_id,
											'char_reputation_detail_category' => $char_reputation_detail_category,
											'char_reputation_detail_name' => $char_reputation_detail_name,
											'char_reputation_detail_standing' => (isset($char_reputation_detail_name_value_array['Standing']) ? $char_reputation_detail_name_value_array['Standing'] : ''),
											'char_reputation_detail_value' => (isset($char_reputation_detail_name_value_array['Value']) ? $char_reputation_detail_name_value_array['Value'] : ''),
											'char_reputation_detail_at_war' => (isset($char_reputation_detail_name_value_array['AtWar']) ? $char_reputation_detail_name_value_array['AtWar'] : '')
											);
					
					tep_db_perform(TABLE_CHAR_REPUTATION_DETAIL, $sql_data_array);
				}
			}
		}
	}
	
	// Start Pet
	if (isset($data['Pets'])) {
		foreach ($data['Pets'] as $pet_name => $pet_info_array) {
			$pet_food_types_list = "";
			
			$char_pet_id_select_sql = "	SELECT char_pet_id FROM " . TABLE_CHAR_PET . " WHERE char_pet_name='" . tep_db_input($pet_name) . "' AND game_char_id ='" . tep_db_input($game_char_id) . "'";
			$char_pet_id_result_sql = tep_db_query($char_pet_id_select_sql);
			$char_pet_id_row = tep_db_fetch_array($char_pet_id_result_sql);
			
			if (tep_not_null($char_pet_id_row['char_pet_id'])) {
				$char_pet_id = $char_pet_id_row['char_pet_id'];
			} else {
				$sql_data_array = array('game_char_id' => $game_char_id,
										'char_pet_name' => $pet_name
										);
										
				tep_db_perform(TABLE_CHAR_PET, $sql_data_array);
				
				$char_pet_id = tep_db_insert_id();
			}
			
			if (isset($data['Pets'][$pet_name]['PetFoodTypes'])) {
				foreach ($data['Pets'][$pet_name]['PetFoodTypes'] as $pet_food_type_list => $pet_food_type) {
					$pet_food_types_list .= $pet_food_type . ':';
				}
			}
			
			if (tep_not_null($pet_food_types_list)) {
				if (substr($pet_food_types_list, -1) == ':') {
					$pet_food_types_list = substr($pet_food_types_list, 0, -1);
				}
			}
			
			$sql_data_array = array('char_pet_id' => $char_pet_id,
									'game_char_history_id' => $game_char_history_id,
									'char_pet_stat_int' => (isset($data['Pets'][$pet_name]['Attributes']["Stats"]['Intellect']) ? $data['Pets'][$pet_name]['Attributes']["Stats"]['Intellect'] : ''),
									'char_pet_stat_agl' => (isset($data['Pets'][$pet_name]['Attributes']["Stats"]['Agility']) ? $data['Pets'][$pet_name]['Attributes']["Stats"]['Agility'] : ''),
									'char_pet_stat_sta' => (isset($data['Pets'][$pet_name]['Attributes']["Stats"]['Stamina']) ? $data['Pets'][$pet_name]['Attributes']["Stats"]['Stamina'] : ''),
									'char_pet_stat_str' => (isset($data['Pets'][$pet_name]['Attributes']["Stats"]['Strength']) ? $data['Pets'][$pet_name]['Attributes']["Stats"]['Strength'] : ''),
									'char_pet_stat_armor' => (isset($data['Pets'][$pet_name]['Attributes']["Stats"]['Armor']) ? $data['Pets'][$pet_name]['Attributes']["Stats"]['Armor'] : ''),
									'char_pet_stat_spr' => (isset($data['Pets'][$pet_name]['Attributes']["Stats"]['Spirit']) ? $data['Pets'][$pet_name]['Attributes']["Stats"]['Spirit'] : ''),
									'char_pet_melee_power' => (isset($data['Pets'][$pet_name]['Attributes']["Melee"]['MainHand']['AttackPower']) ? $data['Pets'][$pet_name]['Attributes']['Melee']['MainHand']['AttackPower'] : ''),
									'char_pet_melee_rating' => (isset($data['Pets'][$pet_name]['Attributes']["Melee"]['MainHand']['AttackRating']) ? $data['Pets'][$pet_name]['Attributes']['Melee']['MainHand']['AttackRating'] : ''),
									'char_pet_melee_range' => (isset($data['Pets'][$pet_name]['Attributes']["Melee"]['MainHand']['DamageRange']) ? $data['Pets'][$pet_name]['Attributes']['Melee']['MainHand']['DamageRange'] : ''),
									'char_pet_melee_range_tooltip' => (isset($data['Pets'][$pet_name]['Attributes']['Melee']['DamageRangeTooltip']) ? $data['Pets'][$pet_name]['Attributes']['Melee']['DamageRangeTooltip'] : ''),
									'char_pet_melee_power_tooltip' => (isset($data['Pets'][$pet_name]['Attributes']['Melee']['AttackPowerTooltip']) ? $data['Pets'][$pet_name]['Attributes']['Melee']['AttackPowerTooltip'] : ''),
									'char_pet_armor' => (isset($data['Pets'][$pet_name]['Attributes']['Defense']['Armor']) ? $data['Pets'][$pet_name]['Attributes']['Defense']['Armor'] : ''),
									'char_pet_level' => (isset($data['Pets'][$pet_name]['Level']) ? $data['Pets'][$pet_name]['Level'] : ''),
									'char_pet_loyalty' => (isset($data['Pets'][$pet_name]['Loyalty']) ? $data['Pets'][$pet_name]['Loyalty'] : ''),
									'char_pet_defense' => (isset($data['Pets'][$pet_name]['Defense']) ? $data['Pets'][$pet_name]['Defense'] : ''),
									'char_pet_talent_points' => (isset($data['Pets'][$pet_name]['TalentPoints']) ? $data['Pets'][$pet_name]['TalentPoints'] : ''),
									'char_pet_food_types' => $pet_food_types_list,
									'char_pet_power' => (isset($data['Pets'][$pet_name]['Power']) ? $data['Pets'][$pet_name]['Power'] : ''),
									'char_pet_type' => (isset($data['Pets'][$pet_name]['Type']) ? $data['Pets'][$pet_name]['Type'] : ''),
									'char_pet_health' => (isset($data['Pets'][$pet_name]['Health']) ? $data['Pets'][$pet_name]['Health'] : ''),
									'char_pet_exp' => (isset($data['Pets'][$pet_name]['Experience']) ? $data['Pets'][$pet_name]['Experience'] : ''),
									'char_pet_res_holy' => (isset($data['Pets'][$pet_name]['Attributes']['Resists']['Holy']) ? $data['Pets'][$pet_name]['Attributes']['Resists']['Holy'] : ''),
									'char_pet_res_arcane' => (isset($data['Pets'][$pet_name]['Attributes']['Resists']['Arcane']) ? $data['Pets'][$pet_name]['Attributes']['Resists']['Arcane'] : ''),
									'char_pet_res_shadow' => (isset($data['Pets'][$pet_name]['Attributes']['Resists']['Shadow']) ? $data['Pets'][$pet_name]['Attributes']['Resists']['Shadow'] : ''),
									'char_pet_res_fire' => (isset($data['Pets'][$pet_name]['Attributes']['Resists']['Fire']) ? $data['Pets'][$pet_name]['Attributes']['Resists']['Fire'] : ''),
									'char_pet_res_frost' => (isset($data['Pets'][$pet_name]['Attributes']['Resists']['Frost']) ? $data['Pets'][$pet_name]['Attributes']['Resists']['Frost'] : ''),
									'char_pet_res_nature' => (isset($data['Pets'][$pet_name]['Attributes']['Resists']['Nature']) ? $data['Pets'][$pet_name]['Attributes']['Resists']['Nature'] : '')
									);
									
			tep_db_perform(TABLE_CHAR_PET_HISTORY, $sql_data_array);
		}
	} // End Pet
}

function tooltip($tipdata) {
	$tooltip = "";
	if( !is_array( $tipdata ) ) {
		$tipdata = explode( "<br>", $tipdata );
	}

	foreach( $tipdata as $tip ) {
		$tooltip .= $tip . "\n";
	}
	
	return $tooltip;
}

?>