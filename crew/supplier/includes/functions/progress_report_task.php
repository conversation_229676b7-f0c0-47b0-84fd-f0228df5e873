<?
function tep_get_time_diff($orders_products_id) {
	$time_diff_select_sql = "SELECT SEC_TO_TIME( (TO_DAYS(now())*24*3600+TIME_TO_SEC(now())) - (TO_DAYS(supplier_tasks_time_reference)*24*3600+TIME_TO_SEC(supplier_tasks_time_reference)) ) AS time FROM " . TABLE_SUPPLIER_TASKS_ALLOCATION . " WHERE orders_products_id ='" . (int)$orders_products_id . "'";
	$time_diff_result_sql = tep_db_query($time_diff_select_sql);
	$time_diff_row = tep_db_fetch_array($time_diff_result_sql);
	
	$time_array = explode(':', $time_diff_row['time']);
	
	$hour = (int)$time_array[0];
	$min = ((int)$time_array[0] * 60) + $time_array[1];
	
	return $time_diff_array = array('hour_format' => $hour, 'minute_format' => $min);
}

function local_get_pwl_key(&$var, $key, $prefix) {
	$var = $prefix . $var;
}

function tep_get_custom_product_info_option_class ($order_products_id) {
	$custom_product_info_option_class_array = array();
	$orders_custom_products_key_array = array();
	
	$custom_product_info_option_class_type = array('account_username' => ENTRY_ACCOUNT_USERNAME, 'account_password' => ENTRY_ACCOUNT_PASSWORD, 'character_name' => ENTRY_CHAR_NAME, 'realm' => ENTRY_REALM, 'faction' => ENTRY_FACTION, 'game' => ENTRY_GAME);
	
	$pwl_key = array_keys($custom_product_info_option_class_type);
	array_walk($pwl_key, 'local_get_pwl_key', 'power_leveling_account_');
		
	$account_info_select_sql = "	SELECT orders_custom_products_value, orders_custom_products_key
									FROM " . TABLE_ORDERS_CUSTOM_PRODUCTS . " 
									WHERE orders_products_id ='" . (int)$order_products_id . "' 
										AND orders_custom_products_key IN ('" . implode("', '", $pwl_key) . "')";
	
	$account_info_result_sql = tep_db_query($account_info_select_sql);
	
	while ($account_info_row = tep_db_fetch_array($account_info_result_sql)) {
		$keys_array = array();
		for ($array_count = 0; $array_count < sizeof($custom_product_info_option_class_type); $array_count++) {
			$keys_array = array_keys($custom_product_info_option_class_type);
			
			if (trim($account_info_row['orders_custom_products_key']) == trim('power_leveling_account_' . $keys_array[$array_count])) {
				$custom_product_info_option_class_array[$keys_array[$array_count]] = array('label' => $custom_product_info_option_class_type[$keys_array[$array_count]], 'value' => $account_info_row['orders_custom_products_value']);
			}
		}
	}
	
	return $custom_product_info_option_class_array;
}

function tep_first_version_is_greater($first_version, $second_version) {
	if ($first_version == $second_version)
		return false;
	
	$first_version_id_array = explode(".", $first_version);
	$second_version_id_array = explode(".", $second_version);
	
	$greater = false;
	if (count($first_version_id_array) < count($second_version_id_array)) {
		for ($i=0; $i < count($first_version_id_array); $i++) {
			if ($first_version_id_array[$i] > $second_version_id_array[$i]) {
				$greater = true;
				break;
			}
		}
	} else {
		for ($i=0; $i < count($first_version_id_array); $i++) {
			if ($i < count($second_version_id_array)) {
				if ($first_version_id_array[$i] > $second_version_id_array[$i]) {
					$greater = true;
					break;
				} else if ($first_version_id_array[$i] < $second_version_id_array[$i]) {
					$greater = false;
					break;
				}
			} else {	
				$greater = true;
				break;
			}
		}
	}
	return $greater;
}

function tep_StringEncryptRC4($passText, $passKey) {
	$en_key_len = strlen($passKey);
	$rc4_b = 0;
	$rc4_i = 0;
	$rc4_j = 0;
	$rc4_cipher = '';
	
	for($rc4_a = 0; $rc4_a <= 255; $rc4_a++) {
		$rc4_key[$rc4_a] = ord(substr($passKey, fmod($rc4_a,$en_key_len), 1));
		$rc4_box[$rc4_a] = $rc4_a;
	}
	
	for($rc4_a = 0; $rc4_a <= 255; $rc4_a++) {
		$rc4_b = fmod(($rc4_b + $rc4_box[$rc4_a] + $rc4_key[$rc4_a] ), 256);
		$en_temp_swap = $rc4_box[$rc4_a];
		$rc4_box[$rc4_a] = $rc4_box[$rc4_b];
		$rc4_box[$rc4_b] = $en_temp_swap;
	}
	
	for($rc4_a = 0; $rc4_a < strlen($passText); $rc4_a++ ) {
		$rc4_i = fmod(($rc4_i + 1), 256);
		$rc4_j = fmod(($rc4_j + $rc4_box[$rc4_i]), 256);
		$rc4_k = $rc4_box[fmod(($rc4_box[$rc4_i] + $rc4_box[$rc4_j]), 256)];
		$rc4_cipherby = ord(substr($passText, $rc4_a, 1)) ^ $rc4_k;
		if (strlen(base_convert($rc4_cipherby, 10, 16)) < 2) {
			$rc4_cipher .= "0" . substr(base_convert($rc4_cipherby, 10, 16), strlen(base_convert($rc4_cipherby, 10, 16)) - 1, 2);
		} else {
			$rc4_cipher .= substr(base_convert($rc4_cipherby, 10, 16), strlen(base_convert($rc4_cipherby, 10, 16)) - 2, 2);
		}
	}
	return strtoupper($rc4_cipher);
}

function tep_StringDecryptRC4($passText, $passKey) {
	$de_key_len = strlen($passKey);
	$rc4_i = "";
	$rc4_b = 0;
	$rc4_cipher = '';
	
	for($rc4_a = 0; $rc4_a <= 255; $rc4_a++) {
		$rc4_key[$rc4_a] = ord(substr($passKey, fmod($rc4_a, $de_key_len), 1));
		$rc4_box[$rc4_a] = $rc4_a;
	}
	
	for($rc4_a = 0; $rc4_a <= 255; $rc4_a++) {
		$rc4_b = fmod( ($rc4_b + $rc4_box[$rc4_a] + $rc4_key[$rc4_a] ), 256);
		$de_temp_swap = $rc4_box[$rc4_a];
		$rc4_box[$rc4_a] = $rc4_box[$rc4_b];
		$rc4_box[$rc4_b] = $de_temp_swap;
	}
	
	for($rc4_a = 0; $rc4_a < strlen($passText); $rc4_a = $rc4_a + 2) {
		$rc4_i = fmod(($rc4_i + 1), 256);
		$rc4_j = fmod(($rc4_j + $rc4_box[$rc4_i]), 256);
		$rc4_k = $rc4_box[fmod(($rc4_box[$rc4_i] + $rc4_box[$rc4_j]), 256)];
		$rc4_cipherby = hexdec(substr($passText, $rc4_a, 2)) ^ $rc4_k;
		$rc4_cipher .= chr($rc4_cipherby);
	}
	return $rc4_cipher;
}

function tep_get_profiler_link ($order_products_id) {
	$profiler_url = '';
	
	$game_char_id_select_sql = "SELECT game_char_id FROM " . TABLE_GAME_CHAR . " WHERE orders_products_id ='" . (int)$order_products_id . "'";
	$game_char_id_result_sql = tep_db_query($game_char_id_select_sql);
	$game_char_id_row = tep_db_fetch_array($game_char_id_result_sql);
	
	if (tep_not_null($game_char_id_row['game_char_id_row']) || $game_char_id_row['game_char_id'] != 0) {
		$profiler_url = '<a href="' . tep_href_link(FILENAME_SUPPLIER_PROGRESS_REPORT, 'action=char_info&game_char_id=' . $game_char_id_row['game_char_id']) . '">' . LINK_PROFILER . '</a>';
	}
	
	return $profiler_url;
}
?>