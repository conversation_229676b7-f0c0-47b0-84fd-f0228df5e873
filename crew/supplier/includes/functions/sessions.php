<?
/*
  	$Id: sessions.php,v 1.2 2006/01/16 09:37:01 weichen Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2003 osCommerce
	
  	Released under the GNU General Public License
*/

if (STORE_SESSIONS == 'mysql') {
	if (!$SESS_LIFE = get_cfg_var('session.gc_maxlifetime')) {
    	//$SESS_LIFE = 1440;
      	//$SESS_LIFE = 3600; //1 hours
      	$SESS_LIFE = 2700; //45 minutes
	}
    
    function _sess_open($save_path, $session_name) {
    	return true;
    }
	
    function _sess_close() {
      	return true;
    }
	
    function _sess_read($key) {
    	$qid = tep_db_query("select value from " . TABLE_SESSIONS . " where sesskey = '" . tep_db_input($key) . "' and expiry > '" . time() . "'");
		
      	$value = tep_db_fetch_array($qid);
      	if ($value['value']) {
        	return $value['value'];
      	}
		
      	return false;
    }
	
    function _sess_write($key, $val) {
    	global $SESS_LIFE;
		
      	$expiry = time() + $SESS_LIFE;
      	$value = $val;
		
      	$qid = tep_db_query("select count(*) as total from " . TABLE_SESSIONS . " where sesskey = '" . tep_db_input($key) . "'");
      	$total = tep_db_fetch_array($qid);
		
      	if ($total['total'] > 0) {
        	return tep_db_query("update " . TABLE_SESSIONS . " set expiry = '" . tep_db_input($expiry) . "', value = '" . tep_db_input($value) . "' where sesskey = '" . tep_db_input($key) . "'");
      	} else {
        	return tep_db_query("insert into " . TABLE_SESSIONS . " values ('" . tep_db_input($key) . "', '" . tep_db_input($expiry) . "', '" . tep_db_input($value) . "')");
      	}
    }
	
    function _sess_destroy($key) {
      	return tep_db_query("delete from " . TABLE_SESSIONS . " where sesskey = '" . tep_db_input($key) . "'");
    }
	
    function _sess_gc($maxlifetime) {
      	tep_db_query("delete from " . TABLE_SESSIONS . " where expiry < '" . time() . "'");
      	return true;
    }
	
    session_set_save_handler('_sess_open', '_sess_close', '_sess_read', '_sess_write', '_sess_destroy', '_sess_gc');
}

function tep_session_start() {
	return session_start();
}

function tep_session_register($variable) {
	return session_register($variable);
}

function tep_session_is_registered($variable) {
    return session_is_registered($variable);
}

/*	This function does not unset the corresponding global variable for $variable, 
	it only prevents the variable from being saved as part of the session. 
	You must call unset() to remove the corresponding global variable.
*/
function tep_session_unregister($variable) {
	return session_unregister($variable);
}

function tep_session_id($sessid = '') {
    if ($sessid != '') {
      	return session_id($sessid);
    } else {
      	return session_id();
    }
}

function tep_session_name($name = '') {
	if ($name != '') {
    	return session_name($name);
    } else {
      	return session_name();
    }
}

function tep_session_close() {
	if (function_exists('session_close')) {
    	return session_close();
    }
}

function tep_session_destroy() {
    return session_destroy();
}

function tep_session_save_path($path = '') {
    if ($path != '') {
      	return session_save_path($path);
    } else {
      	return session_save_path();
    }
}
?>