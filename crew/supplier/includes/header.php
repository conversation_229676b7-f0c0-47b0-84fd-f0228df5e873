<?
/*
  	$Id: header.php,v 1.12 2007/04/09 07:22:39 weichen Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2002 osCommerce
	
  	Released under the GNU General Public License
*/

require_once(DIR_WS_FUNCTIONS . 'connection_function.php');

if ($messageStack->size > 0) {
	echo $messageStack->output();
}

if (!isset($sup_lng) || (isset($sup_lng) && !is_object($sup_lng))) {
	include_once(DIR_WS_CLASSES . 'language.php');
    $sup_lng = new language('', TABLE_SUPPLIER_LANGUAGES);
}

$languages_selection_array = array();
$selected_language = '';

while (list($key, $value) = each($sup_lng->catalog_languages)) {
	$languages_selection_array[] = array( 'id' => $key, 'text' => $value['name']);
	if (!tep_not_null($selected_language) && $value['directory'] == $sup_language)
		$selected_language = $key;
}

$hidden_get_variables = '';
reset($HTTP_GET_VARS);
while (list($key, $value) = each($HTTP_GET_VARS)) {
  	if ( ($key != 'sup_language') && ($key != tep_session_name()) && ($key != 'x') && ($key != 'y') ) {
    	$hidden_get_variables .= tep_draw_hidden_field($key, $value);
  	}
}
// To maintain report continuous
if ($_REQUEST['action'] == "show_report") {
	$hidden_get_variables .= tep_draw_hidden_field('cont', '1');
}

$langBoxContent = tep_draw_form('language_form', basename($PHP_SELF), '', 'get');
$langBoxContent .= $hidden_get_variables;
$langBoxContent .= tep_draw_pull_down_menu('sup_language', $languages_selection_array, $selected_language, 'onChange="this.form.submit();"');
$langBoxContent .= '</form>';

if (PMA_USR_BROWSER_AGENT == 'IE') { ?>
	<STYLE TYPE="text/css" MEDIA="screen, projection">
	<!--
	  @import url(includes/ie_style.css);
	-->
	</STYLE>
<?
}
?>
<table border="0" width="100%" cellspacing="0" cellpadding="0">
  	<tr class="headerBar">
    	<td width="40%" class="headerBarContent" align="left"><?=date("r")?></td>
    	<td width="10%" class="headerBarContent" align="center" nowrap><?=tep_session_is_registered('supplier_id') ? ENTRY_PREF_TIME_ZONE . $GLOBALS['_DATE_TIMEZONE_DATA'][$supplier_pref_setting[KEY_SP_TIME_ZONE]]['shortname'] : ''?></td>
    	<td width="50%" class="headerBarContent" align="right">&nbsp;
<?
//	Supplier Main Menu

	$supplier_tasks_select_sql = "SELECT orders_products_id FROM " . TABLE_SUPPLIER_TASKS_ALLOCATION . " WHERE suppliers_id ='" . (int)$supplier_id . "'";
	$supplier_tasks_result_sql = tep_db_query($supplier_tasks_select_sql);
	$supplier_tasks_row = tep_db_fetch_array($supplier_tasks_result_sql);

	echo '<a href="' . tep_href_link(FILENAME_DEFAULT, '', 'NONSSL') . '" class="headerLink">' . HEADER_TITLE_HOME . '</a>';
  	if (tep_session_is_registered('supplier_id')) {
    	echo ' | <a href="' . tep_href_link(FILENAME_SUPPLIER_ACCOUNT, '', 'SSL') . '" class="headerLink">' . HEADER_TITLE_ACCOUNT . '</a> | <a href="' . tep_href_link(FILENAME_SUPPLIER_HISTORY, '', 'NONSSL') . '" class="headerLink">' . HEADER_TITLE_ORDER_HISTORY . '</a> | <a href="' . tep_href_link(FILENAME_SUPPLIER_PAYMENT, '', 'NONSSL') . '" class="headerLink">' . HEADER_TITLE_PAYMENT_HISTORY . '</a>' . (tep_not_null($supplier_tasks_row["orders_products_id"]) ? ' | <a href="' . tep_href_link(FILENAME_SUPPLIER_PROGRESS_REPORT, '', 'NONSSL') . '" class="headerLink">' . HEADER_TITLE_PROGRESS_REPORT . '</a>' : '');
  	}
  	
  	echo ' | ' . $langBoxContent;
  	
  	if (tep_session_is_registered('supplier_id')) {
  		echo ' | <a href="' . tep_href_link(FILENAME_LOGOFF, '', 'NONSSL') . '" class="headerLink">' . HEADER_TITLE_LOGOFF . '</a>';
  	} else {
  		echo ' | <a href="' . tep_href_link(FILENAME_LOGIN, '', 'NONSSL') . '" class="headerLink">' . HEADER_TITLE_LOGIN . '</a>';
  	}
?>&nbsp;&nbsp;&nbsp;
		</td>
  	</tr>
  	<tr>
  		<td align="left" valign="top" class="headerBarContent">
<?
	$user_credit_balance = tep_user_balance($supplier_id, 'supplier');
	
	if (count($user_credit_balance)) {
		echo '<table border="0" cellspacing="0" cellpadding="2">
  				<tr><td align="center" class="invoiceBoxHeading" colspan="4">'.HEADER_TITLE_ACCOUNT_BALANCE.'</td></tr>';
		foreach ($user_credit_balance as $bal_currency => $bal_amt) {
			echo '<tr>
					<td class="invoiceRecords">'.$currencies->currencies[$bal_currency]['title'].'</td>
					<td class="invoiceRecords">&nbsp;</td>
					<td class="invoiceRecords">'.number_format($bal_amt, 2, '.', ',').'</td>
					<td class="invoiceRecords"><a href="'.tep_href_link(FILENAME_WITHDRAW, 'cur='.$bal_currency, 'SSL').'">'.LINK_WITHDRAW_FUND.'</a></td>
				  </tr>';
		}
		echo '</table>';
	} else {
		;
	}
?>
  		</td>
  	</tr>
</table>