<SCRIPT language="JavaScript" type="text/javascript">
<!--
var xmlhttp=loadXMLHTTP();
var stateSelectionTitle = Array();
stateSelectionTitle['id'] = '';
if (typeof(pls_select) != 'undefined' && pls_select != null) {
	stateSelectionTitle['text'] = pls_select;
} else {
	stateSelectionTitle['text'] = 'Please Select';
}

function loadXMLHTTP() {
	try { 
		varXMLHTTP = new ActiveXObject("Msxml2.XMLHTTP"); 
	}
	
	catch (e) { 
		try { 
			varXMLHTTP = new ActiveXObject("Microsoft.XMLHTTP"); 
		}
		catch (E) { 
			varXMLHTTP = false; 
		}
	}
	
	if (!xmlhttp && typeof XMLHttpRequest!='undefined') { 
		try {
			varXMLHTTP = new XMLHttpRequest(); 
		}
		catch (ex) {
			varXMLHTTP = false;		
		}
	}
	
	return varXMLHTTP;
}

function DOMCall(name) {
	if (document.getElementById)
    	return document.getElementById(name);
	else if (document.all)
		return document.all[name];
	else if (document.layers)
		return document.layers[name];
}

function initInfoCaptions() {
   	// create a dynamic layer and attach it to the HTML document.
	objBody = document.getElementsByTagName("body").item(0);
    objContainer = document.createElement('div');
    objContainer.setAttribute('id', 'infocaption');
    
    objBody.appendChild(objContainer);
}

function getPMOptions (pm_sel, opt_div, b_id) {
	if (xmlhttp.readyState > 0 && xmlhttp.readyState < 4) return ;
	
	var obj_ref = pm_sel;
	var div_option_obj = DOMCall(opt_div);
	var res;
    var server_action = (b_id != null && b_id != '') ? 'edit_pm_fields' : 'add_pm_fields';
    
    div_option_obj.innerHTML = 'Loading ...';
    obj_ref.disabled = true;
	
	var ref_url = "payment_xmlhttp.php?action="+server_action+"&b_id="+b_id+"&pm_id="+obj_ref.value;
	xmlhttp.open("GET", ref_url); 
    xmlhttp.onreadystatechange = function() { 
      	if (xmlhttp.readyState == 4 && xmlhttp.status == 200) { 
      		//res = xmlhttp.responseText;
      		
      		if (typeof (xmlhttp.responseXML.getElementsByTagName("option_selection")[0]) != "undefined" && xmlhttp.responseXML.getElementsByTagName("option_selection")[0] != null) {
				div_option_obj.innerHTML = xmlhttp.responseXML.getElementsByTagName("option_selection")[0].firstChild.data
			} else {
      			div_option_obj.innerHTML = '';
      		}
      		
      		obj_ref.disabled = false;
      	}
    }
    
    xmlhttp.send(null);
}

function getPMInfo (pb_sel, bal_cur, bal_txt, opt_div) {
	if (xmlhttp.readyState > 0 && xmlhttp.readyState < 4) return ;
	
	var obj_ref = pb_sel;
	var bal_obj = DOMCall(bal_txt);
	var div_option_obj = DOMCall(opt_div);
	var res;
    var server_action = 'withdraw_pm_info';
    var js_error = false;
    
    if (obj_ref.value != '') {
    	bal_obj.value = bal_obj.value.replace(/[^\d\.]/g, '');
    	
	    if (trim_str(bal_obj.value) == '') {
	    	alert('<?=JS_ERROR_NO_WITHDRAW_AMOUNT?>');
	    	js_error = true;
	    } else if (!currencyValidation(trim_str(bal_obj.value))) {
	   		alert('<?=JS_ERROR_INVALID_WITHDRAW_AMOUNT?>');
	   		js_error = true;
	    }
    }
    
    if (js_error) {
    	obj_ref.selectedIndex = 0;
    	return false;
	} else {
	    div_option_obj.innerHTML = 'Loading ...';
	    obj_ref.disabled = true;
		
		var ref_url = "payment_xmlhttp.php?action="+server_action+"&b_id="+obj_ref.value+"&w_cur="+bal_cur+"&w_amt="+bal_obj.value;
		xmlhttp.open("GET", ref_url);
	    xmlhttp.onreadystatechange = function() { 
	      	if (xmlhttp.readyState == 4 && xmlhttp.status == 200) { 
	      		//res = xmlhttp.responseText;
	      		//alert(res);
      			if (typeof (xmlhttp.responseXML.getElementsByTagName("withdraw_info")[0]) != "undefined" && xmlhttp.responseXML.getElementsByTagName("withdraw_info")[0] != null) {
					div_option_obj.innerHTML = xmlhttp.responseXML.getElementsByTagName("withdraw_info")[0].firstChild.data
				} else {
	      			div_option_obj.innerHTML = '';
	      		}
	      		
	      		obj_ref.disabled = false;
	      	}
	    }
	    
	    xmlhttp.send(null);
	}
}

// add item to select element the less elegant, but compatible way.
function appendToSelect(select, value, content) {
	var opt = document.createElement('option');
	opt.value = value;
	opt.text = content;
	select.options.add(opt);
}

function clearOptionList(obj) {
	while (obj.options.length > 0) {
		obj.remove(0);
	}
}

function showMainInfo(DisplayMsg) {
	hideMainInfo();
	if (typeof DisplayMsg == 'undefined') {
		return;
	}
	var objInfo = DOMCall('infocaption');
	var scroll = getScroll();
	
	objInfo.innerHTML = '<div class="xmlHttpMainInfo">' + DisplayMsg + '</div>';
	objInfo.style.visibility = 'visible';
	
	objInfo.style.position = "absolute";
	
	objInfo.style.top = 5 + scroll.y;
	
	var msg_left_pos = getWindowWidth() - objInfo.offsetWidth;
	objInfo.style.left = msg_left_pos - 20;
}

function hideMainInfo() {
	var objInfo = DOMCall('infocaption');
	objInfo.style.visibility = 'hidden';
	objInfo.innerHTML = '';
}

function getWindowWidth() {
	var windowWidth = 0;
	if (typeof(window.innerWidth) == 'number') {
		windowWidth = window.innerWidth;
	} else {
		if (document.documentElement && document.documentElement.clientWidth) {
			windowWidth = document.documentElement.clientWidth;
		} else {
			if (document.body && document.body.clientWidth) {
				windowWidth = document.body.clientWidth;
			}
		}
	}
	return windowWidth;
}

function getScroll() {
    if (document.body.scrollTop != undefined) { // IE model
        var ieBox = document.compatMode != "CSS1Compat";
        var cont = ieBox ? document.body : document.documentElement;
        return {x : cont.scrollLeft, y : cont.scrollTop};
    } else {
        return {x : window.pageXOffset, y : window.pageYOffset};
    }
}

window.onload = function() {
	initInfoCaptions();
}
//--></SCRIPT>