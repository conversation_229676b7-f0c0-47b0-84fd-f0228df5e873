var xmlhttp=loadXMLHTTP();
var stateSelectionTitle = Array();
stateSelectionTitle['id'] = '';
if (typeof(pls_select) != 'undefined' && pls_select != null) {
	stateSelectionTitle['text'] = pls_select;
} else {
	stateSelectionTitle['text'] = 'Please Select';
}

function loadXMLHTTP() {
	try { 
		varXMLHTTP = new ActiveXObject("Msxml2.XMLHTTP"); 
	}
	
	catch (e) { 
		try { 
			varXMLHTTP = new ActiveXObject("Microsoft.XMLHTTP"); 
		}
		catch (E) { 
			varXMLHTTP = false; 
		}
	}
	
	if (!xmlhttp && typeof XMLHttpRequest!='undefined') { 
		try {
			varXMLHTTP = new XMLHttpRequest(); 
		}
		catch (ex) {
			varXMLHTTP = false;		
		}
	}
	
	return varXMLHTTP;
}

function DOMCall(name) {
	if (document.getElementById)
    	return document.getElementById(name);
	else if (document.all)
		return document.all[name];
	else if (document.layers)
		return document.layers[name];
}

function initInfoCaptions() {
   	// create a dynamic layer and attach it to the HTML document.
	objBody = document.getElementsByTagName("body").item(0);
    objContainer = document.createElement('div');
    objContainer.setAttribute('id', 'infocaption');
    
    objBody.appendChild(objContainer);
}

function refreshDynamicSelectOptions (sel1, sel2, sel3, _lang, show_title) {
	var div_objRef = DOMCall(sel2);
	var objRef = DOMCall(sel3);
	var res;
    var server_action = 'state_list';
    var selection_available = false;
    
    if (objRef.type == 'text') {
    	objRef = document.createElement("SELECT");
    	objRef.name = sel3;
    	objRef.id = sel3;
    } else {
    	clearOptionList(objRef);
    }
    
    div_objRef.innerHTML = 'Loading ...';
    sel1.disabled = true;
	
	var ref_url = "supplier_xmlhttp.php?action="+server_action+"&country_id="+sel1.value+"&lang="+_lang;
	xmlhttp.open("GET", ref_url); 
    xmlhttp.onreadystatechange = function() { 
      	if (xmlhttp.readyState == 4 && xmlhttp.status == 200) { 
      		//res = xmlhttp.responseText;
      		
      		clearOptionList(objRef);
      		var selection = xmlhttp.responseXML.getElementsByTagName("selection")[0];
      		
      		if (typeof (selection) != 'undefined' && selection != null) {
      			if (show_title == true) {
      				appendToSelect(objRef, stateSelectionTitle['id'], stateSelectionTitle['text']);
      			}
      			
      			var option_sel = '';
	      		for (var i=0; i < selection.childNodes.length; i++) {
	      			option_sel = selection.getElementsByTagName("option")[i];
	      			appendToSelect(objRef, option_sel.getAttribute("index"), option_sel.firstChild.nodeValue);
			    }
      		}
      		
      		if ((show_title == true && objRef.options.length > 1) || (show_title == false && objRef.options.length > 0)) {
      			selection_available = true;
      		} else {
      			selection_available = false;
      		}
      		
      		if (!selection_available) {
      			objRef = document.createElement("INPUT");
		    	objRef.name = sel3;
		    	objRef.id = sel3;
      		}
      		
      		div_objRef.innerHTML = '';
      		div_objRef.appendChild(objRef);
      		sel1.disabled = false;
      	}
    }
    
    xmlhttp.send(null);
}

// add item to select element the less elegant, but compatible way.
function appendToSelect(select, value, content) {
	var opt = document.createElement('option');
	opt.value = value;
	opt.text = content;
	select.options.add(opt);
}

function clearOptionList(obj) {
	while (obj.options.length > 0) {
		obj.remove(0);
	}
}

function showMainInfo(DisplayMsg) {
	hideMainInfo();
	if (typeof DisplayMsg == 'undefined') {
		return;
	}
	var objInfo = DOMCall('infocaption');
	var scroll = getScroll();
	
	objInfo.innerHTML = '<div class="xmlHttpMainInfo">' + DisplayMsg + '</div>';
	objInfo.style.visibility = 'visible';
	
	objInfo.style.position = "absolute";
	
	objInfo.style.top = 5 + scroll.y;
	
	var msg_left_pos = getWindowWidth() - objInfo.offsetWidth;
	objInfo.style.left = msg_left_pos - 20;
}

function hideMainInfo() {
	var objInfo = DOMCall('infocaption');
	objInfo.style.visibility = 'hidden';
	objInfo.innerHTML = '';
}

function getWindowWidth() {
	var windowWidth = 0;
	if (typeof(window.innerWidth) == 'number') {
		windowWidth = window.innerWidth;
	} else {
		if (document.documentElement && document.documentElement.clientWidth) {
			windowWidth = document.documentElement.clientWidth;
		} else {
			if (document.body && document.body.clientWidth) {
				windowWidth = document.body.clientWidth;
			}
		}
	}
	return windowWidth;
}

function getScroll() {
    if (document.body.scrollTop != undefined) { // IE model
        var ieBox = document.compatMode != "CSS1Compat";
        var cont = ieBox ? document.body : document.documentElement;
        return {x : cont.scrollLeft, y : cont.scrollTop};
    } else {
        return {x : window.pageXOffset, y : window.pageYOffset};
    }
}

window.onload = function() {
	initInfoCaptions();
}