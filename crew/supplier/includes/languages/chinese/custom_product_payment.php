<?
define('HEADING_TITLE', '代练付款记录');
define('HEADING_INPUT_TITLE', '查询付款');

define('TABLE_HEADING_TRANS_NO', '付款编号');
define('TABLE_HEADING_PAYMENT_NO', '付款编号');
define('TABLE_HEADING_TRANS_DATE', '付款交易日期');
define('TABLE_HEADING_PAYMENT_DATE', '付款日期');
define('TABLE_HEADING_REMARK', '备注');
define('TABLE_HEADING_TRANS_AMOUNT', '金额');

define('TABLE_HEADING_ORDER_NO', '交易编号');
//define('TABLE_HEADING_ORDER_DATE', '日期');
 
define('TABLE_HEADING_SUBMITED_DATE', '提交');
define('TABLE_HEADING_ORDER_AMOUNT', '可付总金额');
define('TABLE_HEADING_PAID_AMOUNT', '支付金额');

define('ENTRY_ORDER_START_DATE', '起始日期<br><small>(YYYY-MM-DD)</small>');
define('ENTRY_ORDER_END_DATE', '结束日期<br><small>(YYYY-MM-DD)</small>');
define('ENTRY_PAYMENT_ID', '付款编号');
define('ENTRY_ORDER_ID', '交易编号');
define('ENTRY_RECORDS_PER_PAGE', '每页显示');
define('TEXT_LANG_RECORDS_PER_PAGE', '&nbsp;项搜索结果');

define('TEXT_SEARCH_ORDER_START_DATE', '<b>起始日期:</b> ');
define('TEXT_SEARCH_ORDER_END_DATE', '<b>结束日期:</b> ');
define('TEXT_SEARCH_PAYMENT_ID', '<b>付款编号:</b> ');
define('TEXT_SEARCH_ORDER_ID', '<b>交易编号:</b> ');

define('TEXT_MODE_T', '付款交易记录');
define('TEXT_PARTIAL_PAID_ORDER', '[分批]');
 
?>