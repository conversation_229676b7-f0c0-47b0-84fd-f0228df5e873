<?
define('HEADING_TITLE','供应商清单 (%s%s)');
define('TABLE_HEADING_FIRST_SELLING_QTY', '草单数额');
define('TABLE_HEADING_PRODUCT_ID','Product ID');
define('TABLE_HEADING_PRODUCT', '产品名称');
define('TABLE_HEADING_STATUS', '服务器状态');
define('TABLE_HEADING_MIN_MAX_QTY','最高数额');
define('TABLE_HEADING_SELLING_QTY', '销售数额');
define('TABLE_HEADING_OVER_LIMIT_MAX_QTY','最高超限数额');
define('TABLE_HEADING_UNIT_PRICE','单位价格 (美金)');
define('TABLE_HEADING_UNIT_OVER_LIMIT_PRICE','单位超限价格 (美金)');
define('TABLE_HEADING_AMOUNT','金额 (美金)');
define('TABLE_HEADING_ADMIN_COMMENT', '进货角色');
define('TABLE_HEADING_SUPPLIER_COMMENT', '意见');
define('TEXT_MIN_QTY', '最少数额: %d');
define('TEXT_OVER_LIMIT_DISCOUNT', '超限折扣: %s%%');
define('TEXT_CURRENTLY_NOT_BUYING', '服务器已满');
define('TEXT_ADDITIONAL_COMMENTS', '意见');
define('TEXT_TO_DEACTIVATION', ' 提交时限');

define('TABLE_CSV_HEADING_PRODUCT_ID','Product ID');
define('TABLE_CSV_HEADING_PRODUCT','Product Name');
define('TABLE_CSV_HEADING_ADMIN_COMMENT', 'Restock Character');
define('TABLE_CSV_HEADING_STATUS', 'Server Status');
define('TABLE_CSV_HEADING_MIN_MAX_QTY','Max Quantity');
define('TABLE_CSV_HEADING_OVER_LIMIT_MAX_QTY','Max Over Limit Quantity');
define('TABLE_CSV_HEADING_UNIT_PRICE','Unit Price (USD)');
define('TABLE_CSV_HEADING_UNIT_OVER_LIMIT_PRICE','Unit Over Limit Price (USD)');
define('TABLE_CSV_HEADING_FIRST_SELLING_QTY','First List Quantity');
define('TABLE_CSV_HEADING_SELLING_QTY','Selling Quantity');
define('TABLE_CSV_HEADING_SUPPLIER_COMMENT','Comments');

define('TEXT_RESTOCK_ACCOUNT_NOT_AVAILABLE', '&nbsp;');
define('TEXT_RESTOCK_DONE', '&nbsp;');
define('TEXT_SUBMIT_MODE', '提交');
define('TEXT_UPDATE_MODE', '更新');

define('EMAIL_SEPARATOR', '------------------------------------------------------');
define('EMAIL_TEXT_SUBJECT', implode(' ', array(EMAIL_SUBJECT_PREFIX, "New Supplier Order #%s (%s - %s)")));
define('EMAIL_ORDER_UPDATE_SUBJECT', implode(' ', array(EMAIL_SUBJECT_PREFIX, "Supplier Order Update #%s (%s - %s)")));
define('EMAIL_TEXT_BODY','Thank you for supplying to '.STORE_NAME.'.

Supplier Order Summary:
'.EMAIL_SEPARATOR."\n");
define('EMAIL_ADMIN_TEXT_SUBMIT_CONFIRM_LIST', 'The supplier (%s) has successfully submitted the Confirmation List.' . "\n\n" . 'Supplier Order Summary:' . "\n" . EMAIL_SEPARATOR . "\n");
define('EMAIL_TEXT_ORDER_NUMBER', 'Supplier Order Number: %d');
define('EMAIL_TEXT_ORDER_TITLE', 'Supplier Order Title: %s');
define('EMAIL_TEXT_DATE_ORDERED', 'Supplier Order Date: %s');
define('EMAIL_TEXT_PRODUCTS', "\n\n".'%s');
define('EMAIL_TEXT_FIRST_LIST_PRODUCTS', "Your first list products");
define('EMAIL_TEXT_CONFIRMATION_LIST_PRODUCTS', "Your submitted confirmation list products");
define('EMAIL_TEXT_UPDATED_CONFIRMATION_LIST_PRODUCTS', "Your updated confirmation list products");

define('EMAIL_TEXT_BUYBACK_TOTAL', 'Total: ');
define('EMAIL_TEXT_EXTRA','Extra Information:');
define('EMAIL_TEXT_CLOSING', 'Thank you for supporting '.STORE_NAME.'.');
define('EMAIL_FOOTER', "\n\n".STORE_EMAIL_SIGNATURE);

define('WARNING_NOTHING_TO_EXPORT', '注意：没有任何资料被输出');
define('SUCCESS_SUBMIT_LIST', '您的草单已成功提交');
define('ERROR_NOT_EDITABLE_FIRST_LIST', '您还不获准提交草单');
define('SUCCESS_SUBMIT_CONFIRM_LIST', '您的结单已成功提交');
define('SUCCESS_UPDATE_CONFIRM_LIST', '您的结单已成功更新');
define('ERROR_UNKNOW_LIST', '提交无效');
define('WARNING_EMPTY_CART', '您还未准确列出您的产品');
define('WARNING_UPDATE_CONFIRMATION_LIST_INVALID_TIME', '您不获准在结单时限以外更新结单 #%d');
define('WARNING_SUBMIT_CONFIRMATION_LIST_INVALID_TIME', '您不获准在结单时限以外提交结单 #%d');
define('ERROR_EDIT_SUPPLIER_ORDER', '您不获准修改清单 #%d.');
define('ERROR_IMPORT_NOT_SUCCESS', '错误: 输入不成功');
define('ERROR_IMPORTED_PRODUCT_NOT_MATCH', '错误: 产品(编号 #%d)与供应商的清单上的任何产品都不匹配');
define('ERROR_INVALID_SELLING_QUANTITY', '错误: %s 的销售数额不是数字!');
define('WARNING_SERVER_FULL', '注意: %s 服务器已满!');
define('WARNING_AUTO_RESET_MIN', '注意: %s 的销售数额被设定为 %d 因为低于最少数额!');
define('WARNING_AUTO_RESET_MAX', '注意: %s 的销售数额被设定为 %d 因为超过最高数额!');

define('JS_ALERT_NOT_VALID_QTY', '请输入有效的销售数额');
define('JS_ALERT_POSITIVE_QTY', '售卖数额必须大于零');
define('JS_ALERT_LESS_THAN_MIN_QTY', '售卖数额最低值 ');
define('JS_ALERT_GREATER_THAN_MAX_QTY', '售卖数额最高值 ');
define('JS_CONFIRM_SELLING_QTY', '您确定输入的销售数额正确吗？\n您将无法更改所确定的销售数额。');

define('CUST_ALT_BUTTON_EXPORT', '输出清单成csv文件');
define('CUST_ALT_BUTTON_IMPORT', '输入csv文件');
?>