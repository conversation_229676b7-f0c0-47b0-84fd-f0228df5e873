<?php
/*
  	$Id: supplier_pm_book.php,v 1.1 2007/01/25 10:45:11 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2006 SKC Ventrue
	
  	Released under the GNU General Public License
*/

define('HEADER_FORM_PM_BOOK_TITLE', '付款账户');
define('HEADER_FORM_PM_NEW_PM_TITLE', '增加收款账户');
define('HEADER_FORM_PM_EDIT_PM_TITLE', '更改付款账号');

define('ENTRY_FORM_PM_SELECT_PM', '付款方式:');
define('ENTRY_FORM_PM_ALIAS', '付款项目:');

define('SECTION_TITLE_FORM_PM_REQUIRED_INFO', '必要资料:');

define('TABLE_HEADING_PM_PAYMENT_ACCOUNT', '付款账号');

define('TEXT_PM_NO_EXISTING_PAYMENT_ACCOUNT', '目前还没有付款账号的记录.');

define('LINK_PM_EDIT_PM_ACCOUNT', '更改付款账号');
define('LINK_PM_DELETE_PM_ACCOUNT', '删除付款账号');

define('SUCCESS_PM_DELETE_PM_ACCOUNT', '成功: 付款账号已经成功删除.');

define('ERROR_INVALID_PM_BOOK_SELECTION', '错误: 您正在填写的付款账号有错误.');
define('ERROR_INVALID_PM_SELECTION', '错误: 请选择正确的付款方式.');
define('ERROR_EMPTY_PM_ALIAS', '错误: 付款项目必须填写.');
define('ERROR_PM_FIELD_INFO_REQUIRED', '错误: 请填写 "%s" 资料!');
define('ERROR_PM_ACCOUNT_BOOK_FULL', '您的付款账号已经满了。请删除不需要的账号以重新储存一个新的.');
define('ERROR_INVALID_PIN', 'PIN 代码无效，更新失败');
?>