<?
 
define('HEADING_INPUT_TITLE', '进度报告%s');
define('HEADING_LIST_CRITERIA', '查询'); 
define('HEADING_PROGRESS_REPORT', '');
 

define('TEXT_HOURS_PASS', '总过小时: ');
define('TEXT_CURRENT_LEVEL', '现在等级: ');
define('TEXT_HOURS', '小时'); 
define('TEXT_ORDER_ID', 'Order ID');
define('TEXT_PROGRESS_BAR_NOT_AVAILABLE', '进度表无法操作'); 
define('TEXT_CHAR_NAME', 'Character Name: '); 
define('TEXT_FACTION', 'Faction: ');
define('TEXT_REALM', 'Realm: ');
 
define('TEXT_BILLED', '已付');
 
define('TEXT_NOT_BILLED', '等待付款');
 
define('TEXT_INFO_NOT_AVAILABLE', '无法提供讯息');

define('ENTRY_CHARACTER', '角色');
define('ENTRY_HEADING_ORDER_ID', 'Order ID: ');
 
define('ENTRY_TOTAL', '总金额') ;
define('ENTRY_ORDER_PRODUCT_START_DATE', '起始日期<br><small>(YYYY-MM-DD)</small>');
define('ENTRY_ORDER_PRODUCT_END_DATE', '结束日期<br><small>(YYYY-MM-DD)</small>');
define('ENTRY_ORDER_ID', '交易编号'); 
define('ENTRY_PRODUCT_NAME', '产品名称');
 
define('ENTRY_ACCOUNT_USERNAME', 'Account Username');
define('ENTRY_ACCOUNT_PASSWORD', 'Account Password');
define('ENTRY_NEW_ACCOUNT_PASSWORD', 'New Account Password');
define('ENTRY_CHAR_NAME', 'Character Name');
define('ENTRY_REALM', 'Realm');
define('ENTRY_FACTION', 'Faction');
define('ENTRY_GAME', 'Game');

define('SUCCESS_INFORMATION_UPDATED', '资料已更新和保存');
define('ERROR_INFORMATION_NOT_UPDATED', '无法更新和保存资料'); 

define('TEXT_I_AM_DONE', '我已完成');
define('TABLE_HEADING_PROGRESS_REPORT_STATISTIC', '进度报告统计');
 
define('TEXT_CHANGE_STATUS', '更换状态');
 
define('TEXT_NOT_SELECTED', '未选');
 
define('TEXT_GEAR', '装备');
 
define('TEXT_BAGS_AND_BANK', '包包以及银行');
 
define('TEXT_CHARACTER_INFO', '<b>角色资料</b>');
define('TEXT_REDJACK_DOWNLOAD_NOTICE_ENGLISH', 'Please be informed that Redjack will scan for malicious software on your PC, for security and integrity purposes, before access can be granted to customers\' accounts');
define('TEXT_REDJACK_DOWNLOAD_NOTICE_CHINESE', '为了安全起见和该软体完整执行任务，登入顾客帐号前，请先使用Redjack ，因为 它可以扫描您电脑内的恶意软体');

define('LINK_PROFILER', '截图数据');
define('TABLE_HEADING_NUMBER', '第.');
define('TABLE_HEADING_SUBMITTED_DATE', '提交日期');
define('TABLE_HEADING_TOTAL', '总值'); 
define('TABLE_HEADING_PROGRESS_STATUS', '总过小时/等级');

define('TABLE_HEADING_LAST_REPLY', '回复');
define('TABLE_HEADING_PRODUCT_NAME', '产品名称');
 
define('TABLE_HEADING_AMOUNT_ADJUST', '调整总金额'); 
define('TABLE_HEADING_TOTAL_PAYABLE', '可付总金额');
define('TABLE_HEADING_RATIO', '比率');
define('TABLE_HEADING_DATE_ADDED', '日期记录');
define('TABLE_HEADING_STATUS', '状态');
define('TABLE_HEADING_COMMENTS', '意见');
define('TABLE_HEADING_MODIFIED_BY', '修改者');
define('TABLE_HEADING_CP_BILLING_STATUS', '付款状态');

define('LINK_ADVANCED_SEARCH', '高级搜索');
define('LINK_DOWNLOAD_REDJACK_EN', '下载 Redjack (英文版)');
define('LINK_DOWNLOAD_REDJACK_CN', '下载 Redjack (中文版)');
define('LINK_DOWNLOAD_WOW_GUIDE', '下载魔兽世界指南');
 

define('TABLE_HEADING_ASSIGNED_TO', '分配');
define('TABLE_HEADING_ORDER_CODE', '单子码');
?>