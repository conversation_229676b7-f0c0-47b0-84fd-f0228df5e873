<?php
/*
  	$Id: withdraw.php,v 1.2 2007/01/29 02:56:05 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2006 SKC Ventrue
	
  	Released under the GNU General Public License
*/

define('HEADER_FORM_WITHDRAW_TITLE', '提款');
define('HEADER_FORM_WITHDRAW_CONFIRM_TITLE', '提款 - 确认');
define('HEADER_FORM_WITHDRAW_SUCCESS_TITLE', '提款 - 成功');

define('ENTRY_WITHDRAW_CURRENT_BALANCE', '目前余额:');
define('ENTRY_WITHDRAW_RESERVE_AMOUNT', '保留:');
define('ENTRY_WITHDRAW_AVAILABLE_BALANCE', '可提款数额:');
define('ENTRY_WITHDRAW_AMOUNT', '提款数额:');
define('ENTRY_WITHDRAW_PAYMENT_ACCOUNT', '结款致:');
define('ENTRY_WITHDRAW_FINAL_AMOUNT', '可接收数额:');

define('ENTRY_WITHDRAW_PAYMENT_METHOD', '付款方式:');
define('ENTRY_WITHDRAW_MIN_AMT', '&nbsp;- 最低数额:');
define('ENTRY_WITHDRAW_MAX_AMT', '&nbsp;- 最高数额:');
define('ENTRY_WITHDRAW_FEES', '&nbsp;- 结款费:');

define('COMMENT_WITHDRAW_FUNDS_REQUESTED', '要求款额结款致 %s(%s)');

define('TEXT_NO_WITHDRAW_LIMIT', '任何');
define('TEXT_WITHDRAW_FEES_FREE', '无结款费');

define('LINK_WITHDRAW_ACCOUNT_STATEMENT', '[<a href="%s">账单</a>]');
define('LINK_PM_ACC_BOOK', '[<a href="%s">付款账户</a>]');
define('LINK_WITHDRAW_ALL_AMOUNT', '[<a href="%s" onClick="%s">提款所有金额</a>]');
define('LINK_WITHDRAW_REFRESH_DATA', '[<a href="%s" onClick="%s">刷新</a>]');

define('ERROR_WITHDRAW_NO_CURRENCY_ACCOUNT_INFO', '请选择钱币账号来提款。');
define('ERROR_WITHDRAW_NO_AVAILABLE_FUNDS', '您目前没有余额结款，或是您的结款数额已超过了可利用资金。');
define('ERROR_WITHDRAW_NO_PAYMENT_ACCOUNT_BOOK', '请选择接收结款的账号。');
define('ERROR_WITHDRAW_NO_WITHDRAW_AMOUNT', '请输入结款数额。');
define('ERROR_WITHDRAW_NOT_SUCCESS', '您输入的结款数额不被接收。');
define('ERROR_WITHDRAW_DISABLE_WITHDRAWAL', '您的提款功能失效，您的户口现在无法正常提款，我们已经冻结了您的帐户，具体原因请联系我们的在线客服');
define('WARNING_WITHDRAW_FEES_HIGHER_THAN_WITHDRAW_AMOUNT', '结款费高于结款数额。');
define('WARNING_WITHDRAW_NOT_ALLOWED_PAYMENT_METHOD', '%s 不接受这个结款数额。');
define('SUCCESS_WITHDRAW_SUCCESS', '您已经成功提款。');
define('ERROR_INVALID_PM_SELECTION', '错误：请选择有效的付款方法.');

define('CONTENTS_WARNING_WITHDRAW_NOT_ALLOWED_PAYMENT_METHOD', '<span class="redIndicator">%s 不接受这个结款数额</span>');

define('JS_ERROR_NO_WITHDRAW_AMOUNT', '请输入您的结款数额');
define('JS_ERROR_INVALID_WITHDRAW_AMOUNT', '结款数额无效');

// E-mail
define('EMAIL_SEPARATOR', '------------------------------------------------------');
define('EMAIL_PAYMENT_PAYMENT_UPDATE_SUBJECT', implode(' ', array(EMAIL_SUBJECT_PREFIX, "Payment Update #%s")));

define('EMAIL_PAYMENT_TEXT_TITLE', 'You request to withdraw %s via %s from your OffGamers Store Balance account.');
define('EMAIL_PAYMENT_TEXT_SUMMARY_TITLE', 'Payment Summary:');
define('EMAIL_PAYMENT_TEXT_PAYMENT_NUMBER', 'Payment Number: %s');
define('EMAIL_PAYMENT_TEXT_PAYMENT_DATE', 'Payment Date: %s');
define('EMAIL_PAYMENT_TEXT_COMMENTS', 'Comment:' . "\n%s\n");
define('EMAIL_PAYMENT_TEXT_STATUS_UPDATE', "Status: %s");

define('EMAIL_TEXT_CLOSING', 'Thank you for supporting ' . STORE_NAME . '.');
define('EMAIL_FOOTER', STORE_EMAIL_SIGNATURE);
?>