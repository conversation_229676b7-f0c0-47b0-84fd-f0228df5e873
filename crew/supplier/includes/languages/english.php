<?
/*
  	$Id: english.php,v 1.23 2007/11/30 08:27:29 chan Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2003 osCommerce
	
  	Released under the GNU General Public License
*/

//	Supplier Main Menu
// 	header text in includes/header.php
define('HEADER_TITLE_ACCOUNT', 'My Account');
define('HEADER_TITLE_ACCOUNT_STATEMENT', 'Account Statement');
define('HEADER_TITLE_HOME', 'Home');
define('HEADER_TITLE_LOGOFF', 'Sign Out');
define('HEADER_TITLE_LOGIN', 'Sign In');
define('HEADER_TITLE_ORDER_HISTORY', 'Order History');
define('HEADER_TITLE_PAYMENT_BOOK', 'Manage Payment Accounts');
define('HEADER_TITLE_PAYMENT_HISTORY', 'Payment History');
define('HEADER_TITLE_SUB_USER', 'Sub User');
define('HEADER_TITLE_MAC_ACTIVATION', 'Mac Activation');
define('HEADER_TITLE_PROGRESS_REPORT', 'Progress Report');
define('HEADER_TITLE_CUSTOM_PRODUCT_PAYMENT', 'Custom Product Payment');
define('HEADER_TITLE_ACCOUNT_BALANCE', 'Account Balance');

define('HEADER_FORM_SIGN_UP', 'Supplier Sign Up');
define('HEADER_FORM_UPDATE_PROFILE', 'Update Profile');

// images
define('IMAGE_FILE_PERMISSION', 'File Permission');
define('IMAGE_GROUPS', 'Groups List');
define('IMAGE_INSERT_FILE', 'Insert File');
define('IMAGE_MEMBERS', 'Members List');
define('IMAGE_NEW_GROUP', 'New Group');
define('IMAGE_NEW_MEMBER', 'New Member');
define('IMAGE_NEXT', 'Next');

// Supplier List
define('TEXT_FIRST_LIST', 'First List');
define('TEXT_CONFIRMATION_LIST', 'Confirmation List');

// look in your $PATH_LOCALE/locale directory for available locales..
// on RedHat6.0 I used 'en_US'
// on FreeBSD 4.0 I use 'en_US.ISO_8859-1'
// this may not work under win32 environments..
setlocale(LC_TIME, 'en_US.ISO_8859-1');
define('DATE_FORMAT_SHORT', '%m/%d/%Y');  // this is used for strftime()
define('DATE_FORMAT_LONG', '%A %d %B, %Y'); // this is used for strftime()
define('DATE_FORMAT', 'm/d/Y'); // this is used for date()
define('PHP_DATE_TIME_FORMAT', 'm/d/Y H:i:s'); // this is used for date()
define('DATE_TIME_FORMAT', DATE_FORMAT_SHORT . ' %H:%M:%S');

// Return date in raw format
// $date should be in format mm/dd/yyyy
// raw date is in format YYYYMMDD, or DDMMYYYY
function tep_date_raw($date, $reverse = false) {
	if ($reverse) {
    	return substr($date, 3, 2) . substr($date, 0, 2) . substr($date, 6, 4);
  	} else {
    	return substr($date, 6, 4) . substr($date, 0, 2) . substr($date, 3, 2);
  	}
}

define('LANGUAGE_CURRENCY', 'USD');

// Global entries for the <html> tag
define('HTML_PARAMS','dir="ltr" lang="en"');

// charset for web pages and emails
//define('CHARSET', 'iso-8859-1');
define('CHARSET', 'utf-8');

// page title
define('TITLE', STORE_NAME);

// e-mail greeting
define('EMAIL_GREET_MR', 'Dear Mr. %s,' . "\n\n");
define('EMAIL_GREET_MS', 'Dear Ms. %s,' . "\n\n");
define('EMAIL_GREET_NONE', 'Dear %s,' . "\n\n");

// text for gender
define('MALE', 'Male');
define('FEMALE', 'Female');

// text for date of birth example
define('DOB_FORMAT_STRING', 'mm/dd/yyyy');

define('BUTTON_ADD_PM', 'Add Payment Account');
define('BUTTON_BACK', 'Back');
define('BUTTON_CONFIRM', 'Confirm');
define('BUTTON_CONTINUE', 'Continue');
define('BUTTON_EXPORT_TEMPLATE', 'Export Template');
define('BUTTON_IMPORT', 'Import');
define('BUTTON_RESET', 'Reset');
define('BUTTON_SEARCH', 'Search');
define('BUTTON_SHOW_ALL_PRODUCTS', 'Show All Servers');
define('BUTTON_SHOW_SELLING_PRODUCTS', 'Show Selling Servers');
define('BUTTON_SUBMIT', 'Submit');
define('BUTTON_SIGN_IN', 'Sign In');
define('BUTTON_SIGN_UP', 'Sign Up');

define('ALT_BUTTON_ADD_PM', 'Add Payment Account');
define('ALT_BUTTON_BACK', 'Back to previous page');
define('ALT_BUTTON_CONFIRM', 'Confirm');
define('ALT_BUTTON_CONTINUE', 'Continue');
define('ALT_BUTTON_RESET', 'Reset');
define('ALT_BUTTON_SEARCH', 'Search');
define('ALT_BUTTON_SHOW_ALL_PRODUCTS', 'Click here to show all servers.');
define('ALT_BUTTON_SHOW_SELLING_PRODUCTS', 'Click here to show your selling servers only.');
define('ALT_BUTTON_SUBMIT', 'Submit form');
define('ALT_BUTTON_SIGN_IN', 'Sign In');
define('ALT_BUTTON_SIGN_UP', 'Sign Up');

define('TABLE_HEADING_ACTION', 'Action');

define('TEXT_NOT_AVAILABLE', 'n/a');
define('TEXT_PAGE_GENERATION', 'Page was generated in %0.3f seconds');

$server_status_trans = array('urgent'=>'Urgent', 'important'=>'Important', 'normal'=>'Normal');

define('KEY_SP_TIME_ZONE', "sp_time_zone");
define('KEY_SP_LANG', "sp_lang");
define('KEY_SPS_QUANTITY_RATIO', "sps_quantity_ratio");
define('KEY_SPS_USE_MFC_SUPPLY', "sps_use_mfc_supply");
define('KEY_SPS_MFC_SUPPLY_MAX_QTY', "sps_mfc_supply_max_qty");
define('KEY_SPS_RSTK_CHAR_SET', "rstk_char_set");

// taxes box text in includes/boxes/taxes.php
define('BOX_HEADING_LOCATION_AND_TAXES', 'Taxes');
define('BOX_TAXES_COUNTRIES', 'Countries');
define('BOX_TAXES_ZONES', 'Zones');
define('BOX_TAXES_GEO_ZONES', 'Defined Zones');
define('BOX_TAXES_TAX_CLASSES', 'Tax Classes');
define('BOX_TAXES_TAX_RATES', 'Tax Rates');

// reports box text in includes/boxes/reports.php
define('BOX_HEADING_REPORTS', 'Reports');
define('BOX_REPORTS_PRODUCTS_VIEWED', 'Products - Best Viewed');
define('BOX_REPORTS_PRODUCTS_PURCHASED', 'Product Sales Report');
define('BOX_REPORTS_STOCK', 'Stock Report');
define('BOX_REPORTS_SALES', 'Sales Report');
define('BOX_REPORTS_SALES_NEW', 'Sales Report 3');
define('BOX_REPORTS_ORDERS_TOTAL', 'Total Purchased');

// tools text in includes/boxes/tools.php
define('BOX_HEADING_TOOLS', 'Tools');
define('BOX_TOOLS_UPGRADE', 'Version Upgrade');		// for version upgrading
define('BOX_TOOLS_CSS_GENERATOR', 'CSS Generator');	// for css generator
define('BOX_TOOLS_BACKUP', 'Database Backup');
define('BOX_TOOLS_BANNER_MANAGER', 'Banner Manager');
define('BOX_TOOLS_CACHE', 'Cache Control');
define('BOX_TOOLS_DEFINE_LANGUAGE', 'Define Languages');
define('BOX_TOOLS_FILE_MANAGER', 'File Manager');
define('BOX_TOOLS_MAIL', 'E-mail Customer');
define('BOX_TOOLS_NEWSLETTER_MANAGER', 'Newsletter Manager');
define('BOX_TOOLS_SERVER_INFO', 'Server Info');
define('BOX_TOOLS_WHOS_ONLINE', 'Who\'s Online');

// localizaion box text in includes/boxes/localization.php
define('BOX_HEADING_LOCALIZATION', 'Localization');
define('BOX_LOCALIZATION_CURRENCIES', 'Currencies');
define('BOX_LOCALIZATION_LANGUAGES', 'Languages');
define('BOX_LOCALIZATION_ORDERS_STATUS', 'Orders Status');

// javascript messages
define('JS_ERROR', 'Errors have occured during the process of your form!\nPlease make the following corrections:\n\n');

define('JS_OPTIONS_VALUE_PRICE', '* The new product atribute needs a price value\n');
define('JS_OPTIONS_VALUE_PRICE_PREFIX', '* The new product atribute needs a price prefix\n');

define('JS_PRODUCTS_NAME', '* The new product needs a name\n');
define('JS_PRODUCTS_DESCRIPTION', '* The new product needs a description\n');
define('JS_PRODUCTS_PRICE', '* The new product needs a price value\n');
define('JS_PRODUCTS_WEIGHT', '* The new product needs a weight value\n');
define('JS_PRODUCTS_QUANTITY', '* The new product needs a quantity value\n');
define('JS_PRODUCTS_MODEL', '* The new product needs a model value\n');
define('JS_PRODUCTS_IMAGE', '* The new product needs an image value\n');

define('JS_SPECIALS_PRODUCTS_PRICE', '* A new price for this product needs to be set\n');

define('JS_GENDER', '* The \'Gender\' value must be chosen.\n');
define('JS_FIRST_NAME', '* The \'First Name\' entry must have at least ' . ENTRY_FIRST_NAME_MIN_LENGTH . ' characters.\n');
define('JS_LAST_NAME', '* The \'Last Name\' entry must have at least ' . ENTRY_LAST_NAME_MIN_LENGTH . ' characters.\n');
define('JS_DOB', '* The \'Date of Birth\' entry must be in the format: xx/xx/xxxx (month/date/year).\n');
define('JS_EMAIL_ADDRESS', '* The \'E-Mail Address\' entry must have at least ' . ENTRY_EMAIL_ADDRESS_MIN_LENGTH . ' characters.\n');
define('JS_ADDRESS', '* The \'Street Address\' entry must have at least ' . ENTRY_STREET_ADDRESS_MIN_LENGTH . ' characters.\n');
define('JS_POST_CODE', '* The \'Post Code\' entry must have at least ' . ENTRY_POSTCODE_MIN_LENGTH . ' characters.\n');
define('JS_CITY', '* The \'City\' entry must have at least ' . ENTRY_CITY_MIN_LENGTH . ' characters.\n');
define('JS_STATE', '* The \'State\' entry is must be selected.\n');
define('JS_STATE_SELECT', '-- Select Above --');
define('JS_ZONE', '* The \'State\' entry must be selected from the list for this country.');
define('JS_COUNTRY', '* The \'Country\' value must be chosen.\n');
define('JS_TELEPHONE', '* The \'Telephone Number\' entry must have at least ' . ENTRY_TELEPHONE_MIN_LENGTH . ' characters.\n');
define('JS_PASSWORD', '* The \'Password\' amd \'Confirmation\' entries must match amd have at least ' . ENTRY_PASSWORD_MIN_LENGTH . ' characters.\n');

define('JS_ORDER_DOES_NOT_EXIST', 'Order Number %s does not exist!');

define('CATEGORY_PAYMENT', 'Payment Info');
define('CATEGORY_PERSONAL', 'Personal Details');
define('CATEGORY_ADDRESS', 'Address');
define('CATEGORY_CONTACT', 'Contact Information');
define('CATEGORY_PREFERENCES', 'Preferences');
define('CATEGORY_PASSWORD', 'Password');

define('ENTRY_GENDER', 'Gender:');
define('ENTRY_GENDER_ERROR', '&nbsp;<span class="errorText">The gender value must be chosen</span>');
define('ENTRY_FIRST_NAME', 'First Name:');
define('ENTRY_FIRST_NAME_ERROR', '&nbsp;<span class="errorText">Firstname should be min ' . ENTRY_FIRST_NAME_MIN_LENGTH . ' chars</span>');
define('ENTRY_LAST_NAME', 'Last Name:');
define('ENTRY_LAST_NAME_ERROR', '&nbsp;<span class="errorText">Last name should be min ' . ENTRY_LAST_NAME_MIN_LENGTH . ' chars</span>');
define('ENTRY_DATE_OF_BIRTH', 'Date of Birth:');
define('ENTRY_DATE_OF_BIRTH_ERROR', '&nbsp;<span class="errorText">Invalid date of birth format</span>');
define('ENTRY_EMAIL_ADDRESS', 'E-Mail Address:');
define('ENTRY_EMAIL_ADDRESS_ERROR', '&nbsp;<span class="errorText">Email address must be min ' . ENTRY_EMAIL_ADDRESS_MIN_LENGTH . ' chars</span>');
define('ENTRY_EMAIL_ADDRESS_CHECK_ERROR', '&nbsp;<span class="errorText">The email address doesn\'t appear to be valid</span>');
define('ENTRY_EMAIL_ADDRESS_ERROR_EXISTS', '&nbsp;<span class="errorText">This email address already exists</span>');
define('ENTRY_PASSWORD', 'Password');
define('ENTRY_PASSWORD_CONFIRMATION', 'Confirm Password:');
define('ENTRY_COMPANY', 'Company name:');
define('ENTRY_COMPANY_ERROR', '');
define('ENTRY_STREET_ADDRESS', 'Street Address:');
define('ENTRY_STREET_ADDRESS_ERROR', '&nbsp;<span class="errorText">The street address must be min ' . ENTRY_STREET_ADDRESS_MIN_LENGTH . ' chars</span>');
define('ENTRY_SUBURB', 'Suburb:');
define('ENTRY_SUBURB_ERROR', '');
define('ENTRY_POST_CODE', 'Post Code:');
define('ENTRY_POST_CODE_ERROR', '&nbsp;<span class="errorText">The postcode length must be min ' . ENTRY_POSTCODE_MIN_LENGTH . ' chars</span>');
define('ENTRY_CITY', 'City:');
define('ENTRY_CITY_ERROR', '&nbsp;<span class="errorText">The city length must be min ' . ENTRY_CITY_MIN_LENGTH . ' chars</span>');
define('ENTRY_STATE', 'State:');
define('ENTRY_STATE_ERROR', '&nbsp;<span class="errorText">Valid state is required</span>');
define('ENTRY_COUNTRY', 'Country:');
define('ENTRY_COUNTRY_ERROR', '&nbsp;<span class="errorText">Country is required</span>');
define('ENTRY_TELEPHONE_NUMBER', 'Telephone Number:');
define('ENTRY_TELEPHONE_NUMBER_ERROR', '&nbsp;<span class="errorText">The telephone number must be min ' . ENTRY_TELEPHONE_MIN_LENGTH . ' chars</span>');
define('ENTRY_FAX_NUMBER', 'Fax Number:');
define('ENTRY_FAX_NUMBER_ERROR', '&nbsp;Invalid FAX number');
define('ENTRY_MSN_ADDRESS', 'MSN Address:');
define('ENTRY_MSN_ADDRESS_ERROR', '&nbsp;<span class="errorText">The MSN doesn\'t appear to be valid</span>');
define('ENTRY_QQ_NUMBER', 'QQ Number:');
define('ENTRY_QQ_NUMBER_ERROR', '&nbsp;<span class="errorText">Accept number only</span>');
define('ENTRY_NEWSLETTER', 'Newsletter:');
define('ENTRY_NEWSLETTER_YES', 'Subscribed');
define('ENTRY_NEWSLETTER_NO', 'Unsubscribed');
define('ENTRY_NEWSLETTER_ERROR', 'Newsletter error');
define('ENTRY_SUPPLIER_HOMEPAGE_ERROR','The homepage format is invalid!');
define('ENTRY_PASSWORD_ERROR','&nbsp;<span class="errorText">Password must be min ' . ENTRY_PASSWORD_MIN_LENGTH . ' chars</span>');
define('ENTRY_PASSWORD_ERROR_NOT_MATCHING', '&nbsp;<span class="errorText">Password Confirmation must match Password.</span>');
define('ENTRY_PREF_LANGUAGE', 'Language:');
define('ENTRY_PREF_TIME_ZONE', 'Time Zone: ');
define('ENTRY_STATUS_ERROR', '&nbsp;<span class="errorText">The status value must be chosen</span>');
define('ENTRY_USER_NAME', 'User Name');
define('ENTRY_USER_NAME_MIN_LENGTH', '6');
define('ENTRY_USER_NAME_ERROR', '&nbsp;<span class="errorText">The username should be min ' . ENTRY_USER_NAME_MIN_LENGTH . ' chars and without space</span>');

define('ENTRY_SUPPLIER_PAYMENT_CHECK', 'Check Payee Name:');
define('ENTRY_SUPPLIER_PAYMENT_PAYPAL', 'PayPal Account E-mail:');
define('ENTRY_SUPPLIER_PAYMENT_BANK_NAME', 'Bank Name:');
define('ENTRY_SUPPLIER_PAYMENT_BANK_ADDRESS', 'Bank Address:');
define('ENTRY_SUPPLIER_PAYMENT_BANK_PHONE_NUMBER', 'Bank Phone Number:');
define('ENTRY_SUPPLIER_PAYMENT_BANK_SWIFT_CODE', 'Bank SWIFT Code:');
define('ENTRY_SUPPLIER_PAYMENT_BANK_ACCOUNT_NAME', 'Beneficiary Name:');
define('ENTRY_SUPPLIER_PAYMENT_BANK_ACCOUNT_NUMBER', 'Beneficiary Account Number:');
define('ENTRY_SUPPLIER_PAYMENT_BANK_BRANCH_NUMBER', 'ABA/BSB number (branch number):');

// images
define('IMAGE_ANI_SEND_EMAIL', 'Sending E-Mail');
define('IMAGE_BACK', 'Back');
define('IMAGE_BACKUP', 'Backup');
define('IMAGE_CANCEL', 'Cancel');
define('IMAGE_CONFIRM', 'Confirm');
define('IMAGE_COPY', 'Copy');
define('IMAGE_COPY_TO', 'Copy To');
define('IMAGE_DETAILS', 'Details');
define('IMAGE_DELETE', 'Delete');
define('IMAGE_EDIT', 'Edit');
define('IMAGE_EMAIL', 'Email');
define('IMAGE_FILE_MANAGER', 'File Manager');
define('IMAGE_ICON_STATUS_GREEN', 'Active');
define('IMAGE_ICON_STATUS_GREEN_LIGHT', 'Set Active');
define('IMAGE_ICON_STATUS_RED', 'Inactive');
define('IMAGE_ICON_STATUS_RED_LIGHT', 'Set Inactive');
//Added for product hidden
define('IMAGE_ICON_HIDDEN_GREEN', 'Show');
define('IMAGE_ICON_HIDDEN_GREEN_LIGHT', 'Show It');
define('IMAGE_ICON_HIDDEN_RED', 'Hidden');
define('IMAGE_ICON_HIDDEN_RED_LIGHT', 'Hide It');
//End of added for product hidden
define('IMAGE_ICON_INFO', 'Info');
define('IMAGE_INSERT', 'Insert');
define('IMAGE_LOCK', 'Lock');
define('IMAGE_MODULE_INSTALL', 'Install Module');
define('IMAGE_MODULE_REMOVE', 'Remove Module');
define('IMAGE_MOVE', 'Move');
define('IMAGE_NEW_BANNER', 'New Banner');
define('IMAGE_NEW_CATEGORY', 'New Category');
define('IMAGE_NEW_COUNTRY', 'New Country');
define('IMAGE_NEW_CURRENCY', 'New Currency');
define('IMAGE_NEW_FILE', 'New File');
define('IMAGE_NEW_FOLDER', 'New Folder');
define('IMAGE_NEW_LANGUAGE', 'New Language');
define('IMAGE_NEW_NEWSLETTER', 'New Newsletter');
define('IMAGE_NEW_PRODUCT', 'New Product');
define('IMAGE_NEW_TAX_CLASS', 'New Tax Class');
define('IMAGE_NEW_TAX_RATE', 'New Tax Rate');
define('IMAGE_NEW_TAX_ZONE', 'New Tax Zone');
define('IMAGE_NEW_ZONE', 'New Zone');
define('IMAGE_ORDERS', 'Orders');
define('IMAGE_ORDERS_INVOICE', 'Invoice');
define('IMAGE_ORDERS_PACKINGSLIP', 'Packing Slip');
define('IMAGE_PREVIEW', 'Preview');
define('IMAGE_REPORT', 'View Report');
define('IMAGE_RESET', 'Reset Form');
define('IMAGE_RESTORE', 'Restore');
//define('IMAGE_RESET', 'Reset');
define('IMAGE_SAVE', 'Save');
define('IMAGE_SEARCH', 'Search');
define('IMAGE_SELECT', 'Select');
define('IMAGE_SEND', 'Send');
define('IMAGE_SEND_EMAIL', 'Send Email');
define('IMAGE_UNLOCK', 'Unlock');
define('IMAGE_UPDATE', 'Update');
define('IMAGE_UPDATE_CURRENCIES', 'Update Exchange Rate');
define('IMAGE_UPLOAD', 'Upload');

define('ICON_CROSS', 'False');
define('ICON_CURRENT_FOLDER', 'Current Folder');
define('ICON_DELETE', 'Delete');
define('ICON_ERROR', 'Error');
define('ICON_FILE', 'File');
define('ICON_FILE_DOWNLOAD', 'Download');
define('ICON_FOLDER', 'Folder');
define('ICON_LOCKED', 'Locked');
define('ICON_PREVIOUS_LEVEL', 'Previous Level');
define('ICON_PREVIEW', 'Preview');
define('ICON_STATISTICS', 'Statistics');
define('ICON_SUCCESS', 'Success');
define('ICON_TICK', 'True');
define('ICON_UNLOCKED', 'Unlocked');
define('ICON_WARNING', 'Warning');

// constants for use in tep_prev_next_display function
define('TEXT_RESULT_PAGE', 'Page %s of %s');
define('TEXT_DISPLAY_NUMBER_OF_BANNERS', 'Displaying <b>%d</b> to <b>%d</b> (of <b>%d</b> banners)');
define('TEXT_DISPLAY_NUMBER_OF_COUNTRIES', 'Displaying <b>%d</b> to <b>%d</b> (of <b>%d</b> countries)');
define('TEXT_DISPLAY_NUMBER_OF_CUSTOMERS', 'Displaying <b>%d</b> to <b>%d</b> (of <b>%d</b> customers)');
define('TEXT_DISPLAY_NUMBER_OF_LOGS', 'Displaying <b>%d</b> to <b>%d</b> (of <b>%d</b> logs)');
define('TEXT_DISPLAY_NUMBER_OF_CURRENCIES', 'Displaying <b>%d</b> to <b>%d</b> (of <b>%d</b> currencies)');
define('TEXT_DISPLAY_NUMBER_OF_LANGUAGES', 'Displaying <b>%d</b> to <b>%d</b> (of <b>%d</b> languages)');
define('TEXT_DISPLAY_NUMBER_OF_MANUFACTURERS', 'Displaying <b>%d</b> to <b>%d</b> (of <b>%d</b> manufacturers)');
define('TEXT_DISPLAY_NUMBER_OF_NEWSLETTERS', 'Displaying <b>%d</b> to <b>%d</b> (of <b>%d</b> newsletters)');
define('TEXT_DISPLAY_NUMBER_OF_ORDERS', 'Displaying <b>%d</b> to <b>%d</b> (of <b>%d</b> orders)');
define('TEXT_DISPLAY_NUMBER_OF_ORDERS_STATUS', 'Displaying <b>%d</b> to <b>%d</b> (of <b>%d</b> orders status)');
define('TEXT_DISPLAY_NUMBER_OF_PAYMENTS_TRANSACTIONS', 'Displaying <b>%d</b> to <b>%d</b> (of <b>%d</b> payments transactions)');
define('TEXT_DISPLAY_NUMBER_OF_PAYPALIPN_TRANSACTIONS', 'Displaying <b>%d</b> to <b>%d</b> (of <b>%d</b> transactions)'); // PAYPALIPN
define('TEXT_DISPLAY_NUMBER_OF_PRODUCTS', 'Displaying <b>%d</b> to <b>%d</b> (of <b>%d</b> products)');
define('TEXT_DISPLAY_NUMBER_OF_PRODUCTS_EXPECTED', 'Displaying <b>%d</b> to <b>%d</b> (of <b>%d</b> products expected)');
define('TEXT_DISPLAY_NUMBER_OF_RECORDS', 'Displaying <b>%d</b> to <b>%d</b> (of <b>%d</b> records)');
define('TEXT_DISPLAY_NUMBER_OF_REVIEWS', 'Displaying <b>%d</b> to <b>%d</b> (of <b>%d</b> product reviews)');
define('TEXT_DISPLAY_NUMBER_OF_SPECIALS', 'Displaying <b>%d</b> to <b>%d</b> (of <b>%d</b> products on special)');
define('TEXT_DISPLAY_NUMBER_OF_TAX_CLASSES', 'Displaying <b>%d</b> to <b>%d</b> (of <b>%d</b> tax classes)');
define('TEXT_DISPLAY_NUMBER_OF_TAX_ZONES', 'Displaying <b>%d</b> to <b>%d</b> (of <b>%d</b> tax zones)');
define('TEXT_DISPLAY_NUMBER_OF_TAX_RATES', 'Displaying <b>%d</b> to <b>%d</b> (of <b>%d</b> tax rates)');
define('TEXT_DISPLAY_NUMBER_OF_ZONES', 'Displaying <b>%d</b> to <b>%d</b> (of <b>%d</b> zones)');
define('TEXT_DISPLAY_NUMBER_OF_SUB_USERS', 'Displaying <b>%d</b> to <b>%d</b> (of <b>%d</b> Sub Users)');
define('TEXT_EDIT_CATEGORIES_HEADING_TITLE', 'Category Heading Title:');
define('TEXT_EDIT_CATEGORIES_DESCRIPTION', 'Category Description:');

define('PREVNEXT_BUTTON_PREV', '&lt;&lt;');
define('PREVNEXT_BUTTON_NEXT', '&gt;&gt;');

define('LINK_WITHDRAW_FUND', 'Withdraw');
define('LINK_PROFILER', 'Profiler');

define('TEXT_ALL_PAGES', 'All');
define('TEXT_DEFAULT', 'default');
define('TEXT_SET_DEFAULT', 'Set as default');
define('FORM_REQUIRED_INFORMATION', '* Required information');
define('TEXT_FIELD_REQUIRED', '&nbsp;<span class="fieldRequired">* Required</span>');
define('PULL_DOWN_DEFAULT','Please Select');

define('ERROR_NO_DEFAULT_CURRENCY_DEFINED', 'Error: There is currently no default currency set. Please set one at: Administration Tool->Localization->Currencies');

define('TEXT_CACHE_CATEGORIES', 'Categories Box');
define('TEXT_CACHE_MANUFACTURERS', 'Manufacturers Box');
define('TEXT_CACHE_ALSO_PURCHASED', 'Also Purchased Module');

define('TEXT_NONE', '--none--');
define('TEXT_TOP', 'Top');
define('TEXT_ACTIVE', 'Active');
define('TEXT_NOT_ACTIVE', 'Not Active');

define('TITLE_TRANS_PAYMENT', 'Payment %s');
define('TITLE_TRANS_SUPPLIER_ORDER', 'Supplier Order %s');
define('TITLE_TRANS_PWL_ORDER', 'PWL Order %s');
define('TITLE_TRANS_BUYBACK_ORDER', 'Buyback Order %s');

define('ERROR_DESTINATION_DOES_NOT_EXIST', 'Error: Destination does not exist.');
define('ERROR_DESTINATION_NOT_WRITEABLE', 'Error: Destination not writeable.');
define('ERROR_FILE_NOT_SAVED', 'Error: File upload not saved.');
define('ERROR_FILETYPE_NOT_ALLOWED', 'Error: File upload type not allowed.');
define('SUCCESS_FILE_SAVED_SUCCESSFULLY', 'Success: File upload saved successfully.');
define('WARNING_NO_FILE_UPLOADED', 'Warning: No file uploaded.');
define('WARNING_FILE_UPLOADS_DISABLED', 'Warning: File uploads are disabled in the php.ini configuration file.');

define('BOX_HEADING_PAYPALIPN_ADMIN', 'Paypal IPN'); // PAYPALIPN
define('BOX_PAYPALIPN_ADMIN_TRANSACTIONS', 'Paypal IPN Transactions'); // PAYPALIPN
define('BOX_PAYPALIPN_ADMIN_TESTS', 'Paypal IPN Tests'); // PAYPALIPN
define('BOX_CATALOG_XSELL_PRODUCTS', 'Cross Selling'); // X-Sell
REQUIRE(DIR_WS_LANGUAGES . 'add_ccgvdc_english.php');

// system log comment
define('LOG_QTY_ADJUST', "Quantity Adjustment \n%s");
define('LOG_SALES_ORDER', '##%d##: ##%d## ##%d##');
define('LOG_SALES_COMPLETED', '##%d##: ##%d## ##%d##');
define('LOG_SALES_CANCELLED', '##%d##: ##%d## ##%d##');
define('LOG_SALES_REFUNDED', '##%d##: ##%d## ##%d##');
define('LOG_PARTIAL_DELIVERY_SALES', '##%d##: Partial Delivery Sales');
define('LOG_REVERSED_FROM_VERIFYING_2_PENDING', '##%d##: ##%d## ##%d##');
define('LOG_REVERSED_FROM_PROCESSING_2_PENDING', '##%d##: ##%d## ##%d##');
define('LOG_REVERSED_FROM_COMPLETE_2_PROCESSING', '##%d##: ##%d## ##%d##');
define('LOG_REVERSED_FROM_CANCEL_2_PENDING', '##%d##: ##%d## ##%d##');
define('LOG_REVERSED_FROM_CANCEL_2_PROCESSING', '##%d##: ##%d## ##%d##');
define('LOG_REVERSED_FROM_REFUND_2_VERIFYING', '##%d##: ##%d## ##%d##');
define('LOG_REVERSED_FROM_REFUND_2_PROCESSING', '##%d##: ##%d## ##%d##');
define('LOG_NEW_PRODUCT', "Product Added \n%s");
define('LOG_DELETE_PRODUCT', 'Product Deleted');
define('LOG_PRICE_ADJUST', 'Price Adjustment');
define('LOG_NAME_ADJUST', 'Name Adjustment');
define('LOG_STATUS_ADJUST', 'Status Adjustment');
define('LOG_LOCATION_ADJUST', 'Location Adjustment');
define('LOG_REORDER_ADJUST', "Reorder Level Adjustment \n%s");
define('LOG_DISPLAY_ADJUST', 'Display Adjustment');

define('EMAIL_SUPPLIER_ORDER_UPDATE_NOTIFICATION_SUBJECT', 'Supplier Order Update Notification #%d');
define('EMAIL_SUPPLIER_ORDER_UPDATE_NOTIFICATION_CONTENT', 'Supplier Order ID: %s'."\n".'Supplier Order Date: %s'."\n".'Supplier Order Payable Amount: %s'."\n".'Supplier Order Title: %s'."\n\n".'Update Type: %s'."\n".'Status Update: %s -> %s'."\n".'Update Date: %s'."\n".'Update IP: %s'."\n".'Update User: %s'."\n\n".'Update Comment:'."\n".'%s');
define('EMAIL_PWL_ORDER_UPDATE_NOTIFICATION_SUBJECT', 'Powerleveling Order Update Notification #%s');
define('EMAIL_PWL_ORDER_UPDATE_NOTIFICATION_CONTENT', 'Powerleveling Order ID: %s'."\n".'Powerleveling Order Date: %s'."\n".'Powerleveling Order Payable Amount: %s'."\n\n".'Update Type: %s'."\n".'Status Update: %s -> %s'."\n".'Update Date: %s'."\n".'Update IP: %s'."\n".'Update User: %s'."\n\n".'Update Comment:'."\n".'%s');
define('EMAIL_TEXT_TRANS_UPDATE_MANUAL_NOTIFICATION', 'Manual');
define('EMAIL_TEXT_TRANS_UPDATE_AUTO_NOTIFICATION', 'Automatic');

// text for supplier timing messages
define('TEXT_STATUS_MESSAGE_OFF','<br><br><b><font class="heading"><div  align="center">Supplier Page is currently disabled.<br>Logging you out in 5 seconds...</div></font></b>');
define('TEXT_STATUS_MESSAGE_AUTO','<br><br><b><font class = "heading" align="center"><div  align="center">Supplier Page is only active from %s hrs to %s hrs everyday.<br>Logging you out in 5 seconds...</div></font></b>');

define('TEXT_MSG_STAFF_NOT_EXIST', 'Staff not exist');
?>