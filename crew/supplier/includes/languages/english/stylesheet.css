/*
  	$Id: stylesheet.css,v 1.7 2007/01/25 10:26:48 weichen Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2002 osCommerce
	
  	Released under the GNU General Public License
*/

/* links */
a:link { font-family: Verdana, Arial, sans-serif; font-size: 10px; color: #000000; font-weight: normal; text-decoration: underline; }
a:visited { font-family: Verdana, Arial, sans-serif; font-size: 10px; color: #000000; font-weight: normal; text-decoration: underline; }
a:active { font-family: Verdana, Arial, sans-serif; font-size: 10px; color: #000000; font-weight: normal; text-decoration: underline; }
a:hover { font-family: Verdana, Arial, sans-serif; font-size: 10px; color: #000000; font-weight: normal; text-decoration: none; }

a.headerLink:link { font-family: Verdana, Arial, sans-serif; font-size: 10px; color: #000000; font-weight: bold; text-decoration: none; }
a.headerLink:visited { font-family: <PERSON>erdana, Arial, sans-serif; font-size: 10px; color: #000000; font-weight: bold; text-decoration: none; }
a.headerLink:active { font-family: Verdana, Arial, sans-serif; font-size: 10px; color: #000000; font-weight: bold; text-decoration: none; }
a.headerLink:hover { font-family: Verdana, Arial, sans-serif; font-size: 10px; color: #000000; font-weight: bold; text-decoration: underline; }

a.menuBoxHeadingLink:link { font-size: 10px; color: #616060; font-weight: bold; text-decoration: none; }
a.menuBoxHeadingLink:visited { font-size: 10px; color: #616060; font-weight: bold; text-decoration: none; }
a.menuBoxHeadingLink:active { font-size: 10px; color: #616060; font-weight: bold; text-decoration: none; }
a.menuBoxHeadingLink:hover { font-size: 10px; color: #616060; font-weight: bold; text-decoration: none; }

a.menuBoxContentLink:link { font-family: Verdana, Arial, sans-serif; font-size: 10px; color: #616060; font-weight: normal; text-decoration: none; }
a.menuBoxContentLink:visited { font-family: Verdana, Arial, sans-serif; font-size: 10px; color: #616060; font-weight: normal; text-decoration: none; }
a.menuBoxContentLink:active { font-family: Verdana, Arial, sans-serif; font-size: 10px; color: #616060; font-weight: normal; text-decoration: none; }
a.menuBoxContentLink:hover { font-family: Verdana, Arial, sans-serif; font-size: 10px; color: #616060; font-weight: normal; text-decoration: underline; }

a.splitPageLink:link { font-family: Verdana, Arial, sans-serif; font-size: 10px; color: #0000FF; font-weight: normal; text-decoration: none; }
a.splitPageLink:visited { font-family: Verdana, Arial, sans-serif; font-size: 10px; color: #0000FF; font-weight: normal; text-decoration: none; }
a.splitPageLink:active { font-family: Verdana, Arial, sans-serif; font-size: 10px; color: #0000FF; font-weight: normal; text-decoration: none; }
a.splitPageLink:hover { font-family: Verdana, Arial, sans-serif; font-size: 10px; color: #0000FF; font-weight: normal; text-decoration: underline; background-color: #FFFF33; }

a.highlightLink:link { font-family: Verdana, Arial, sans-serif; font-size: 10px; color: red; font-weight: bold; text-decoration: none; }
a.highlightLink:visited { font-family: Verdana, Arial, sans-serif; font-size: 10px; color: red; font-weight: bold; text-decoration: none }
a.highlightLink:active { font-family: Verdana, Arial, sans-serif; font-size: 10px; color: red; font-weight: bold; text-decoration: none; }
a.highlightLink:hover { font-family: Verdana, Arial, sans-serif; font-size: 10px; color: red; font-weight: bold; text-decoration: underline; }

a.textLink:link { font-family: Verdana, Arial, sans-serif; font-size: 10px; color: blue; text-decoration: none; }
a.textLink:visited { font-family: Verdana, Arial, sans-serif; font-size: 10px; color: blue; text-decoration: none; }
a.textLink:active { font-family: Verdana, Arial, sans-serif; font-size: 10px; color: blue; text-decoration: none; }
a.textLink:hover { font-family: Verdana, Arial, sans-serif; font-size: 10px; color: blue; text-decoration: underline; }

/* menu box */
.menuBoxHeading { font-family: Verdana, Arial, sans-serif; font-size: 10px; color: #616060; background-color: #ffffff; }
.menuBoxContent { font-family: Verdana, Arial, sans-serif; font-size: 10px; color: #616060; }

/* page */
body { background-color: #ffffff; color: #000000; margin: 0px; }
.headerBar {
	background-color: #C3D9FF;
}
.headerBarContent {
	font-family: Verdana, Arial, sans-serif;
	font-size: 10px;
	color: #000000;
	font-weight: bold;
	padding: 2px;
}
.columnLeft { background-color: #F0F1F1; border-color: #999999; border-width: 1px; border-style: solid; padding: 2px; }
.pageHeading { font-family: Verdana, Arial, sans-serif; font-size: 18px; color: #727272; font-weight: bold; }

/* data table */
.dataTableHeadingRow { background-color: #C9C9C9; }
.dataTableHeadingContent { font-family: Verdana, Arial, sans-serif; font-size: 10px; color: #ffffff; font-weight: bold; }
.dataTableRow { background-color: #F0F1F1; }
.dataTableRowSelected { background-color: #DEE4E8; }
.dataTableRowOver { background-color: #FFFFFF; cursor: pointer; cursor: hand; }
.dataTableContent { font-family: Verdana, Arial, sans-serif; font-size: 10px; color: #000000; }

/* info box */
.infoBoxHeading { font-family: Verdana, Arial, sans-serif; font-size: 10px; color: #ffffff; background-color: #B3BAC5; }
.infoBoxContent { font-family: Verdana, Arial, sans-serif; font-size: 10px; color: #000000; background-color: #DEE4E8; }

/* message box */

.messageBox { font-family: Verdana, Arial, sans-serif; font-size: 10px; }
.messageStackError, .messageStackWarning { font-family: Verdana, Arial, sans-serif; font-size: 10px; background-color: #ffb3b5; }
.messageStackSuccess { font-family: Verdana, Arial, sans-serif; font-size: 10px; background-color: #99ff00; }

/* forms */
FORM { display: inline; }

/* account */
.formArea { background-color: #f1f9fe; border-color: #7b9ebd; border-style: solid; border-width: 1px; }
.formAreaTitle { font-family: Tahoma, Verdana, Arial, sans-serif; font-size: 12px; font-weight: bold; }

/* attributes */
.attributes-odd { background-color: #f4f7fd; }
.attributes-even { background-color: #ffffff; }

/* miscellaneous */
.specialPrice { color: #ff0000; }
.oldPrice { text-decoration: line-through; }
.fieldRequired { font-family: Verdana, Arial, sans-serif; font-size: 10px; color: #ff0000; }
.smallText { font-family: Verdana, Arial, sans-serif; font-size: 10px; }
.main { font-family: Verdana, Arial, sans-serif; font-size: 12px; }
.titleField { font-family: Verdana, Arial, sans-serif; font-size: 11px; }
.errorText { font-family: Verdana, Arial, sans-serif; font-size: 10px; color: #ff0000; }

/* Orders stock */
span.sufficientStock {
	font-weight:bold;
	color:#007700;
}

span.lowStock {
	font-weight:bold;
	color:#ff0000;
}

.preOrderText {
	font-family: Verdana, Arial, sans-serif;
	font-size: 10px;
	font-weight: bold;
	font-style: italic;
	color: #ff0000;
}

/* Button Effect */
.inputButton, .inputButtonOver {
	cursor: pointer;
	cursor: hand;
}

.infoLabel, td.inputLabel, p.inputLabel, div.inputLabel, span.inputLabel {
  	font-family: Verdana, Arial, sans-serif;
  	font-size: 11px;
  	line-height: 1.5;
  	color: #000000; 
}

td.inputField, p.inputField, div.inputField, span.inputField {
  	font-family: Verdana, Arial, sans-serif;
  	font-size: 11px;
  	line-height: 1.5;
  	color: #000000; 
}

/* Rollover Effect */
.moduleRow { }

.moduleRowOver {
	background-color: #336699; 
	//cursor: pointer;
	//cursor: hand;
}

.moduleRowSelected {
	background-color: #336699; 
}

.invoiceBoxHeading, .ordersBoxHeading, .reportBoxHeading {
  	font-family: Verdana, Arial, sans-serif;
  	font-size: 10px;
  	font-weight: bold;
  	color: #000; 
  	background: #D3DCE3;
}

.subInvoiceBoxHeading, .subRecordsBoxHeading {
  	font-family: Verdana, Arial, sans-serif;
  	font-size: 10px;
  	color: #000; 
}

td.invoiceRecords, td.ordersRecords, td.reportRecords {
	font-family: Verdana, Arial, sans-serif;
  	font-size: 10px;
  	color : #000; 
}

td.invoiceRecordsRightBorder {
	font-family: Verdana, Arial, sans-serif;
  	font-size: 10px;
  	color : #000; 
  	border-right: white 1px solid;
}

tr.invoiceListingEven, tr.ordersListingEven, tr.reportListingEven {
	background: #E5E5E5; 
}

tr.invoiceListingOdd, tr.ordersListingOdd, tr.reportListingOdd {
	background: #D5D5D5; 
}

tr.invoiceListingRowOver, tr.ordersListingRowOver, tr.reportListingRowOver {
	background: #CCFFCC;
}

tr.rowSelected {
	background: #FFCC99;
}

td.bracketListingOdd {
	background: #D7D5D0;
}

td.bracketListingEven {
	background: #FFFFCC;
}

span.categoryPath {
	color: blue;
}

span.redIndicator {
	color: red;
}

span.greenIndicator {
	color: green;
}

span.blackIndicator {
	color: black;
}

.boldText {
	font-family: Verdana, Arial, sans-serif;
  	font-size: 10px;
  	font-weight: bold;
  	color : #000;
}

/***************************************
	Table Style
***************************************/
tbody.show {
	display: block;
	display: table-row-group;
}

tbody.hide {
	display: none;
}

span.show, div.show, div.show_row {
	display: inline;
	display: table-cell-group;
}

span.hide, div.hide, div.hide_row {
	display: none;
}

table.spacing {
 	border           : 0px solid #fb9500;
   	width            : 100%;
   	margin           : auto;
   	border-spacing	 : 1px;
}

ul.extraInfo {
	text-align: left;
	margin: 0 0 0 2em;
	padding: 1;
	position: relative;
}

ul.extraInfo li {
    padding: 0px;
    margin-left: -10px;
    margin: 0;
}

.orderRemarkSelectedRow, .paymentRemarkSelectedRow { background-color: #FFFFCC; }

/***************************************
	xmlhttp								
***************************************/
.xmlHttpMainInfo {
	font-family: Verdana, Arial, sans-serif;
  	font-size: 14px;
  	font-weight: bold;
  	color : #000000;
  	background-color: #ffb3b5;
}

#dhtmlTooltip {
	position: absolute;
	width: 150px;
	border: 1px solid black;
	padding: 2px;
	background-color: lightyellow;
	visibility: hidden;
	z-index: 100;
	/*Remove below line to remove shadow. Below line should always appear last within this CSS*/
	filter: progid:DXImageTransform.Microsoft.Shadow(color=gray,direction=135);
}

div.bar_time {
	background-color: #7187BB;
	width: 6px;
}

div.bar_time_empty {
	background-color: #00000;
	width: 6px;
}

div.bar {
	background-color: #00F000;
	width: 6px;
}

div.bar_alert_red {
	background-color: #F44676;
	width: 6px;
}

table.bar_border {
	border: 1;
}