<?php
/*
  	$Id: supplier_account_statement.php,v 1.2 2007/02/02 10:07:15 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2006 SKC Ventrue
	
  	Released under the GNU General Public License
*/

define('HEADER_FORM_ACC_STAT_TITLE', 'Account Statement');

define('ENTRY_ORDER_START_DATE', 'Start Date<br><small>(YYYY-MM-DD)</small>');
define('ENTRY_ORDER_END_DATE', 'End Date<br><small>(YYYY-MM-DD)</small>');
define('ENTRY_PAYMENT_ID', 'Payment ID');
define('ENTRY_ORDER_ID', 'Order ID');
define('ENTRY_RECORDS_PER_PAGE', 'Records per page');
define('TEXT_LANG_RECORDS_PER_PAGE', '');

define('TABLE_HEADING_ACC_STAT_DATETIME', 'Date/Time');
define('TABLE_HEADING_ACC_STAT_ACTIVITY', 'Activity');
define('TABLE_HEADING_ACC_STAT_PAYMENT_STATUS', 'Payment Status');
define('TABLE_HEADING_ACC_STAT_DEBIT', 'Debit');
define('TABLE_HEADING_ACC_STAT_CREDIT', 'Credit');
define('TABLE_HEADING_ACC_STAT_BALANCE', 'Balance');

define('TEXT_ACC_STAT_NOT_APPLICABLE', '-');
define('TEXT_ACC_STAT_PAYMENT_STATUS_UPDATE_DATE', '%s on %s');
define('TEXT_ACC_STAT_ADMIN_COMMENT', 'Comment:');
define('TEXT_ACC_STAT_TRANS_ID', '(Transaction ID: %s)');

define('SECTION_TITLE_FORM_PM_REQUIRED_INFO', 'Required Information:');

define('TEXT_PM_NO_EXISTING_PAYMENT_ACCOUNT', 'There is no any payment account records yet.');

define('LINK_PM_EDIT_PM_ACCOUNT', 'Edit payment account');
define('LINK_PM_DELETE_PM_ACCOUNT', 'Delete payment account');

define('SUCCESS_PM_DELETE_PM_ACCOUNT', 'Success: Payment account has been successfully deleted.');

define('ERROR_INVALID_PM_BOOK_SELECTION', 'Error: You are editing invalid payment account.');
define('ERROR_INVALID_PM_SELECTION', 'Error: Please select valid payment method.');
define('ERROR_EMPTY_PM_ALIAS', 'Error: Payment title is required.');
define('ERROR_PM_FIELD_INFO_REQUIRED', 'Error: Please provide "%s" information!');
define('ERROR_PM_ACCOUNT_BOOK_FULL', 'Your payment account book is full. Please delete an unneeded account to save a new one.');
?>