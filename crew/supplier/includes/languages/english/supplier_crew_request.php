<?
define('LOGIN_ERROR_ORDER_ID_INVALID', 'Error 201 : Invalid Order ID.');
define('LOGIN_ERROR_SUPPLIER_CREW_NOT_EXIST', 'Error 202 : Username not exist.');
define('LOGIN_ERROR_PASSWD_NOT_MATCH', 'Error 203 : Password not match.');
define('LOGIN_ERROR_MAC_INACTIVE', 'Error 204 : MAC address inactive.');
define('LOGIN_ERROR_INFO', 'Error 205 : Information not available.');
define('LOGIN_ERROR_INPUT', 'Error 206 : Invalid Input.');
define('LOGIN_ERROR_VERSION', 'Error 207 : Invalid RedJack\'s version.');
define('LOGIN_ERROR_SUPPLIER_NOT_EXIST', 'Error 208 : Supplier not exist.');
define('LOGIN_ERROR_OVER_SLOT_LIMIT', 'Error 209 : Slot over limit.');

define('LOGIN_ADMIN_ERROR_INPUT', 'Error 401 : Invalid Input.');
define('LOGIN_ADMIN_ERROR_ORDER_ID_INVALID', 'Error 402 : Invalid Order ID.');
define('LOGIN_ADMIN_ERROR_ADMIN', 'Error 403 : Wrong username or password.');
define('LOGIN_ADMIN_ERROR_VERSION', 'Error 404 : Invalid RedJack\'s version.');

define('TEXT_PENDING_ACTIVATE', 'Pending activation');

define('SUCCESS_PASSWD_CHANGED', 'Success: Password has been successfully change.');
define('ERROR_SUPPLIER_CREW_NOT_EXIST', 'Error 301 : Username not exist.');
define('ERROR_PASSWD_NOT_MATCH', 'Password not match.');
define('ERROR_SUPPLIER_NOT_EXIST', 'Supplier not exist.');
define('ERROR_VERSION', 'Invalid RedJack\'s version.');
define('ERROR_INPUT', 'Invalid Input.');

define('ERROR_MAINTAINANCE', 'Error 100 : Server down for maintenance.');

define('ERROR_IP_ZONE_ASSIGN', 'Error 500 : No IP assigned to this powerleveling.');
define('ERROR_IP_NOT_ASSIGN_TO_ZONE', 'Error 501 : No IP assigned to IP Zone.');

define('MSG_GAME_IP_ZONES', 'Error 502 : Please ensure you are using the IP from %s.');
?>