<?
define('HEADING_TITLE','Supplier Order History');

define('TABLE_HEADING_SUPPLIERS_INFO', 'Supplier Information');

define('TABLE_HEADING_ORDER_ID', 'Order #');
define('TABLE_HEADING_ORDER_LIST_NAME', 'Order List Title');
define('TABLE_HEADING_FIRST_SUBMISSION_TIME', 'First List Submission Date');
define('TABLE_HEADING_TOTAL_SELLING_QUANTITY', 'Total Selling Quantity');
define('TABLE_HEADING_TOTAL_RECEIVED_QUANTITY', 'Total Received Quantity');
define('TABLE_HEADING_TOTAL_PAYABLE', 'Total Payable Amount');

define('TABLE_HEADING_PRODUCT_NAME', 'Product Name');
define('TABLE_HEADING_PRODUCT_DEMAND_STATUS', 'Server Status');
define('TABLE_HEADING_PRODUCT_ADMIN_COMMENT', 'Restock Account');
define('TABLE_HEADING_PRODUCT_SUPPLIER_COMMENT', 'Comments');
define('TABLE_HEADING_MIN_QTY','Min Qty');
define('TABLE_HEADING_MAX_QTY','Max Qty');
define('TABLE_HEADING_OVER_LIMIT_MAX_QTY','Max Over Limit Qty');
define('TABLE_HEADING_UNIT_PRICE','Unit Price (USD)');
define('TABLE_HEADING_UNIT_OVER_LIMIT_PRICE','Unit Over Limit Price (USD)');
define('TABLE_HEADING_SUGGESTED_SELLING_QUANTITY', '<span title="First List Selling Quantity">1st List Qty</span>');
define('TABLE_HEADING_SELLING_QUANTITY', '<span title="Confirmation List Selling Quantity">2nd List Qty</span>');
define('TABLE_HEADING_PRODUCT_PREVIOUS_RECEIVED', 'Received Qty');
define('TABLE_HEADING_PRODUCT_PAYABLE_AMOUNT', 'Amount Payable');

define('TABLE_HEADING_DATE_ADDED', 'Date Added');
define('TABLE_HEADING_STATUS', 'Status');
define('TABLE_HEADING_COMMENTS', 'Comments');

define('ENTRY_SUPPLIER_ORDER_ID', 'Order Number:');
define('ENTRY_ORDER_LIST_NAME', 'Order List Title:');
define('ENTRY_ORDER_STATUS', 'Order Status:');
define('ENTRY_ORDER_DATE_TIME', 'First List Submitted On:');
define('ENTRY_DATE_LAST_MODIFIED', 'Last Modified On:');

define('ENTRY_SUPPLIER', 'Supplier:');
define('ENTRY_SUPPLIER_CODE', 'Supplier Code:');
define('ENTRY_SUPPLIER_SIGNUP_DATE', 'Sign Up Date:');
define('ENTRY_SUPPLIER_GENDER', 'Gender:');
define('ENTRY_SUPPLIER_DOB', 'Date of Birth:');
define('ENTRY_TELEPHONE_NUMBER', 'Telephone Number:');
define('ENTRY_FAX_NUMBER', 'Fax Number:');
define('ENTRY_EMAIL_ADDRESS', 'E-Mail Address:');
define('ENTRY_ORDERING_IP', 'Ordering IP:');

define('ERROR_ORDER_DOES_NOT_EXIST', 'Error: Order does not exist.');

define('TEXT_TOTAL_AMOUNT', 'Total: %s');
define('TEXT_BALANCE_AMOUNT', 'Balance: %s');
define('TEXT_PAYMENT_RECORDS', '%s Payment #%s: %s');
define('TEXT_PARTIAL_PAYMENT', 'Partial');
define('TEXT_FULL_PAYMENT', 'Full');
define('TEXT_NO_ORDER_HISTORY', 'You have not placed any orders yet.');

define('LINK_EDIT_ORDER', "Edit order");
define('LINK_VIEW_ORDER', "View order details");
?>