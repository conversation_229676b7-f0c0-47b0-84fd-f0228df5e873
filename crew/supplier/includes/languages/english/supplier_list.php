<?

define('HEADING_TITLE','Supplier Order List (%s%s)');
define('TABLE_HEADING_FIRST_SELLING_QTY','First List Quantity');
define('TABLE_HEADING_PRODUCT_ID','Product ID');
define('TABLE_HEADING_PRODUCT','Product Name');
define('TABLE_HEADING_STATUS', 'Server Status');
define('TABLE_HEADING_MIN_MAX_QTY','Max Quantity');
define('TABLE_HEADING_SELLING_QTY','Selling Quantity');
define('TABLE_HEADING_OVER_LIMIT_MAX_QTY','Max Over Limit Quantity');
define('TABLE_HEADING_UNIT_PRICE','Unit Price (USD)');
define('TABLE_HEADING_UNIT_OVER_LIMIT_PRICE','Unit Over Limit Price (USD)');
define('TABLE_HEADING_AMOUNT','Amount(USD)');
define('TABLE_HEADING_ADMIN_COMMENT', 'Restock Character');
define('TABLE_HEADING_SUPPLIER_COMMENT', 'Comments');
define('TEXT_MIN_QTY', 'Minimum Quantity: %d');
define('TEXT_OVER_LIMIT_DISCOUNT', 'Over Limit Discount: %s%%');
define('TEXT_CURRENTLY_NOT_BUYING', 'Server Full');
define('TEXT_ADDITIONAL_COMMENTS', 'Additional Comments');
define('TEXT_TO_DEACTIVATION', ' to deactivation');

define('TABLE_CSV_HEADING_PRODUCT_ID','Product ID');
define('TABLE_CSV_HEADING_PRODUCT','Product Name');
define('TABLE_CSV_HEADING_ADMIN_COMMENT', 'Restock Character');
define('TABLE_CSV_HEADING_STATUS', 'Server Status');
define('TABLE_CSV_HEADING_MIN_MAX_QTY','Max Quantity');
define('TABLE_CSV_HEADING_OVER_LIMIT_MAX_QTY','Max Over Limit Quantity');
define('TABLE_CSV_HEADING_UNIT_PRICE','Unit Price (USD)');
define('TABLE_CSV_HEADING_UNIT_OVER_LIMIT_PRICE','Unit Over Limit Price (USD)');
define('TABLE_CSV_HEADING_FIRST_SELLING_QTY','First List Quantity');
define('TABLE_CSV_HEADING_SELLING_QTY','Selling Quantity');
define('TABLE_CSV_HEADING_SUPPLIER_COMMENT','Comments');

define('TEXT_RESTOCK_ACCOUNT_NOT_AVAILABLE', '&nbsp;');
define('TEXT_RESTOCK_DONE', '&nbsp;');
define('TEXT_SUBMIT_MODE', 'Submit');
define('TEXT_UPDATE_MODE', 'Update');

define('EMAIL_SEPARATOR', '------------------------------------------------------');
define('EMAIL_TEXT_SUBJECT', implode(' ', array(EMAIL_SUBJECT_PREFIX, "New Supplier Order #%s (%s - %s)")));
define('EMAIL_ORDER_UPDATE_SUBJECT', implode(' ', array(EMAIL_SUBJECT_PREFIX, "Supplier Order Update #%s (%s - %s)")));
define('EMAIL_TEXT_BODY','Thank you for supplying to '.STORE_NAME.'.

Supplier Order Summary:
'.EMAIL_SEPARATOR."\n");
define('EMAIL_ADMIN_TEXT_SUBMIT_CONFIRM_LIST', 'The supplier (%s) has successfully submitted the Confirmation List.' . "\n\n" . 'Supplier Order Summary:' . "\n" . EMAIL_SEPARATOR . "\n");
define('EMAIL_TEXT_ORDER_NUMBER', 'Supplier Order Number: %d');
define('EMAIL_TEXT_ORDER_TITLE', 'Supplier Order Title: %s');
define('EMAIL_TEXT_DATE_ORDERED', 'Supplier Order Date: %s');
define('EMAIL_TEXT_PRODUCTS', "\n\n".'%s');
define('EMAIL_TEXT_FIRST_LIST_PRODUCTS', "Your first list products");
define('EMAIL_TEXT_CONFIRMATION_LIST_PRODUCTS', "Your submitted confirmation list products");
define('EMAIL_TEXT_UPDATED_CONFIRMATION_LIST_PRODUCTS', "Your updated confirmation list products");

define('EMAIL_TEXT_BUYBACK_TOTAL', 'Total: ');
define('EMAIL_TEXT_EXTRA','Extra Information:');
define('EMAIL_TEXT_CLOSING', 'Thank you for supporting '.STORE_NAME.'.');
define('EMAIL_FOOTER', "\n\n".STORE_EMAIL_SIGNATURE);

define('WARNING_NOTHING_TO_EXPORT', 'Warning: There is no any data to be exported.');
define('SUCCESS_SUBMIT_LIST', 'Your first list has been successfully submitted.');
define('ERROR_NOT_EDITABLE_FIRST_LIST', 'You are not allowed to submit your first list yet.');
define('SUCCESS_SUBMIT_CONFIRM_LIST', 'Your confirmation list has been successfully submitted.');
define('SUCCESS_UPDATE_CONFIRM_LIST', 'Your confirmation list has been successfully updated.');
define('ERROR_UNKNOW_LIST', 'Invalid submission list.');
define('WARNING_EMPTY_CART', 'You have not specify any selling products yet.');
define('WARNING_UPDATE_CONFIRMATION_LIST_INVALID_TIME', 'You are not allowed to update confirmation list for Order #%d beyond the Confirmation List Time.');
define('WARNING_SUBMIT_CONFIRMATION_LIST_INVALID_TIME', 'You are not allowed to submit confirmation list for Order #%d beyond the Confirmation List Time.');
define('ERROR_EDIT_SUPPLIER_ORDER', 'You are not allowed to edit Order #%d.');
define('ERROR_IMPORT_NOT_SUCCESS', 'Error: Import does not success.');
define('ERROR_IMPORTED_PRODUCT_NOT_MATCH', 'Error: Product #%s does not match any of the Supplier Order List\'s products!');
define('ERROR_INVALID_SELLING_QUANTITY', 'Error: Selling Quantity for %s must be an integer value!');
define('WARNING_SERVER_FULL', 'Warning: %s currently is full!');
define('WARNING_AUTO_RESET_MIN', 'Warning: Selling Quantity for %s is set to %d since it is less than minimum quantity!');
define('WARNING_AUTO_RESET_MAX', 'Warning: Selling Quantity for %s is set to %d since it is greater than maximum quantity!');

define('JS_ALERT_NOT_VALID_QTY', 'Please enter a valid selling quantity.');
define('JS_ALERT_POSITIVE_QTY', 'Selling quantity value must greater than zero.');
define('JS_ALERT_LESS_THAN_MIN_QTY', 'This selling quantity must be at least ');
define('JS_ALERT_GREATER_THAN_MAX_QTY', 'This selling quantity cannot be more than ');
define('JS_CONFIRM_SELLING_QTY', 'Are you confirm the Selling Quantity is correct?\nYou will not allowed to update it once you confirm.');

define('CUST_ALT_BUTTON_EXPORT', 'Export order list template as csv file');
define('CUST_ALT_BUTTON_IMPORT', 'Import csv file');
?>