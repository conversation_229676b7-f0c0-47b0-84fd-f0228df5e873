<?
define('HEADING_TITLE', 'Payment History');
define('HEADING_INPUT_TITLE', 'Payments Criteria');

define('TABLE_HEADING_TRANS_NO', 'Payment ID');
define('TABLE_HEADING_PAYMENT_NO', 'Payment ID');
define('TABLE_HEADING_TRANS_DATE', 'Transaction Date');
define('TABLE_HEADING_PAYMENT_DATE', 'Payment Date');
define('TABLE_HEADING_REMARK', 'Remarks');
define('TABLE_HEADING_TRANS_AMOUNT', 'Payment Amount');

define('TABLE_HEADING_ORDER_NO', 'Order No.');
define('TABLE_HEADING_ORDER_DATE', 'Date');
define('TABLE_HEADING_ORDER_AMOUNT', 'Amount');
define('TABLE_HEADING_PAID_AMOUNT', 'Paid Amount');

define('ENTRY_ORDER_START_DATE', 'Start Date<br><small>(YYYY-MM-DD)</small>');
define('ENTRY_ORDER_END_DATE', 'End Date<br><small>(YYYY-MM-DD)</small>');
define('ENTRY_PAYMENT_ID', 'Payment ID');
define('ENTRY_ORDER_ID', 'Order ID');
define('ENTRY_RECORDS_PER_PAGE', 'Records per page');
define('TEXT_LANG_RECORDS_PER_PAGE', '');

define('TEXT_SEARCH_ORDER_START_DATE', '<b>Start Date:</b> ');
define('TEXT_SEARCH_ORDER_END_DATE', '<b>End Date:</b> ');
define('TEXT_SEARCH_PAYMENT_ID', '<b>Payment ID:</b> ');
define('TEXT_SEARCH_ORDER_ID', '<b>Order ID:</b> ');

define('TEXT_MODE_T', 'Transaction History');
define('TEXT_PARTIAL_PAID_ORDER', '[partial]');
?>