<?php
/*
  	$Id: supplier_pm_book.php,v 1.1 2007/01/25 10:45:11 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2006 SKC Ventrue
	
  	Released under the GNU General Public License
*/

define('HEADER_FORM_PM_BOOK_TITLE', 'Payment Accounts');
define('HEADER_FORM_PM_NEW_PM_TITLE', 'New Payment Account Entry');
define('HEADER_FORM_PM_EDIT_PM_TITLE', 'Edit Payment Account Entry');

define('ENTRY_FORM_PM_SELECT_PM', 'Payment Method:');
define('ENTRY_FORM_PM_ALIAS', 'Payment Title:');

define('SECTION_TITLE_FORM_PM_REQUIRED_INFO', 'Required Information:');

define('TABLE_HEADING_PM_PAYMENT_ACCOUNT', 'Payment Account');

define('TEXT_PM_NO_EXISTING_PAYMENT_ACCOUNT', 'There is no any payment account records yet.');

define('LINK_PM_EDIT_PM_ACCOUNT', 'Edit payment account');
define('LINK_PM_DELETE_PM_ACCOUNT', 'Delete payment account');

define('SUCCESS_PM_DELETE_PM_ACCOUNT', 'Success: Payment account has been successfully deleted.');

define('ERROR_INVALID_PM_BOOK_SELECTION', 'Error: You are editing invalid payment account.');
define('ERROR_INVALID_PM_SELECTION', 'Error: Please select valid payment method.');
define('ERROR_EMPTY_PM_ALIAS', 'Error: Payment title is required.');
define('ERROR_PM_FIELD_INFO_REQUIRED', 'Error: Please provide "%s" information!');
define('ERROR_PM_ACCOUNT_BOOK_FULL', 'Your payment account entry is full. Please delete an unneeded account to save a new one.');
?>