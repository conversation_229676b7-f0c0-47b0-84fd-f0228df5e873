<?
define('HEADING_INPUT_TITLE', 'Progress Report %s');
define('HEADING_LIST_CRITERIA', 'List Criteria');
define('HEADING_PROGRESS_REPORT', '');

define('TABLE_HEADING_PROGRESS_REPORT_STATISTIC', 'Progress Report Statistic');
define('TABLE_HEADING_AMOUNT_ADJUST', 'Amount Adjust');
define('TABLE_HEADING_TOTAL_PAYABLE', 'Total Payable');

define('TEXT_HOURS_PASS', 'Hours passed: ');
define('TEXT_CURRENT_LEVEL', 'Current level: ');
define('TEXT_HOURS', 'hours');
define('TEXT_DISPLAY_TO_SUPPLIER_SIGN', '<span class="redIndicator">*</span>');
define('TEXT_PROGRESS_BAR_NOT_AVAILABLE', 'progress bar n/a');
define('TEXT_I_AM_DONE', 'I\'m Done');
define('TEXT_CHANGE_STATUS', 'Change Status');
define('TEXT_NOT_SELECTED', 'Not Selected');
define('TEXT_ACC_USERNAME', 'Account Username: '); 
define('TEXT_ACC_PASSWORD', 'Account Password: ');
define('TEXT_CHAR_NAME', 'Character Name: '); 
define('TEXT_FACTION', 'Faction: ');
define('TEXT_REALM', 'Realm: ');
define('TEXT_BILLED', 'Billed');
define('TEXT_NOT_BILLED', 'Not Billed');
define('TEXT_INFO_NOT_AVAILABLE', 'Information Not Available');
define('TEXT_GEAR', 'Gear');
define('TEXT_BAGS_AND_BANK', 'Bags and Bank');
define('TEXT_CHARACTER_INFO', "<b>Character Information: </b>");
define('TEXT_REDJACK_DOWNLOAD_NOTICE_ENGLISH', 'Please be informed that Redjack will scan for malicious software on your PC, for security and integrity purposes, before access can be granted to customers\' accounts');
define('TEXT_REDJACK_DOWNLOAD_NOTICE_CHINESE', '为了安全起见和该软体完整执行任务，登入顾客帐号前，请先使用Redjack ，因为 它可以扫描您电脑内的恶意软体');

define('TABLE_HEADING_LAST_REPLY', 'Last Reply');
define('TABLE_HEADING_CUSTOMERS_EMAIL', 'Customer E-Mail');
define('TABLE_HEADING_CUSTOMER_GROUP', 'Member Group');
define('TABLE_HEADING_PRODUCT_NAME', 'Product Name');
define('TABLE_HEADING_TOTAL', 'Total');
define('TABLE_HEADING_STATUS', 'Status');
define('TABLE_HEADING_NUMBER', 'No.');
define('TABLE_HEADING_SUBMITTED_DATE', 'Submitted Date');
define('TABLE_HEADING_SUPPLIER_CODE', 'Supplier Code');
define('TABLE_HEADING_DATE_ADDED', 'Date Added');
define('TABLE_HEADING_COMMENTS', 'Comments'); 
define('TABLE_HEADING_MODIFIED_BY', 'Changed By'); 
define('TABLE_HEADING_PROGRESS_STATUS', 'Hours Passed/Progress');
define('TABLE_HEADING_RATIO', 'Ratio');
define('TABLE_HEADING_ADJUST', 'Adjust');
define('TABLE_HEADING_ASSIGNED_TO', 'Asssigned To');
define('TABLE_HEADING_ORDER_CODE', 'Order Code');
define('TABLE_HEADING_CP_BILLING_STATUS', 'Billing Status');

define('ENTRY_HEADING_ORDER_ID', 'Order ID: ');
define('ENTRY_TOTAL', 'Total:');
define('ENTRY_ORDER_PRODUCT_START_DATE', 'Start Date<br><small>(YYYY-MM-DD)</small>');
define('ENTRY_ORDER_PRODUCT_END_DATE', 'End Date<br><small>(YYYY-MM-DD)</small>');
define('ENTRY_ORDER_ID', 'Order ID');
define('ENTRY_PRODUCT_NAME', 'Product Name');
define('ENTRY_ACCOUNT_USERNAME', 'Account Username');
define('ENTRY_ACCOUNT_PASSWORD', 'Account Password');
define('ENTRY_NEW_ACCOUNT_PASSWORD', 'New Account Password');
define('ENTRY_CHAR_NAME', 'Character Name');
define('ENTRY_REALM', 'Realm');
define('ENTRY_FACTION', 'Faction');
define('ENTRY_GAME', 'Game');
define('ENTRY_CHARACTER', 'Character');

define('LINK_ADVANCED_SEARCH', 'Advanced Search');
define('LINK_DOWNLOAD_REDJACK_EN', 'Download Redjack (English Version)');
define('LINK_DOWNLOAD_REDJACK_CN', 'Download Redjack (Chinese Version)');
define('LINK_DOWNLOAD_WOW_GUIDE', 'Download WOW Guide');
?>