<?php
/*
  	$Id: withdraw.php,v 1.6 2010/01/11 04:33:10 wilson.sun Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2006 SKC Ventrue
	
  	Released under the GNU General Public License
*/

define('HEADER_FORM_WITHDRAW_TITLE', 'Withdraw Funds');
define('HEADER_FORM_WITHDRAW_CONFIRM_TITLE', 'Withdraw Funds - Confirmation');
define('HEADER_FORM_WITHDRAW_SUCCESS_TITLE', 'Withdraw Funds - Success');

define('ENTRY_WITHDRAW_CURRENT_BALANCE', 'Current Balance:');
define('ENTRY_WITHDRAW_RESERVE_AMOUNT', 'Reserve:');
define('ENTRY_WITHDRAW_AVAILABLE_BALANCE', 'Available for Withdrawal:');
define('ENTRY_WITHDRAW_AMOUNT', 'Withdrawal Amount:');
define('ENTRY_WITHDRAW_PAYMENT_ACCOUNT', 'Withdraw To:');
define('ENTRY_WITHDRAW_FINAL_AMOUNT', 'Receivable Amount:');

define('ENTRY_WITHDRAW_PAYMENT_METHOD', 'Payment Method:');
define('ENTRY_WITHDRAW_MIN_AMT', '&nbsp;- Minimum Amount:');
define('ENTRY_WITHDRAW_MAX_AMT', '&nbsp;- Maximum Amount:');
define('ENTRY_WITHDRAW_FEES', '&nbsp;- Withdrawal Fees:');

define('COMMENT_WITHDRAW_FUNDS_REQUESTED', 'Request funds withdraw to %s(%s)');

define('TEXT_NO_WITHDRAW_LIMIT', 'Any');
define('TEXT_WITHDRAW_FEES_FREE', 'No Fees');

define('LINK_WITHDRAW_ACCOUNT_STATEMENT', '[<a href="%s">Account Statement</a>]');
define('LINK_PM_ACC_BOOK', '[<a href="%s">Manage Payment Accounts</a>]');
define('LINK_WITHDRAW_ALL_AMOUNT', '[<a href="%s" onClick="%s">Withdraw All</a>]');
define('LINK_WITHDRAW_REFRESH_DATA', '[<a href="%s" onClick="%s">Refresh</a>]');

define('ERROR_WITHDRAW_NO_CURRENCY_ACCOUNT_INFO', 'Please select the currency account to withdraw.');
define('ERROR_WITHDRAW_NO_AVAILABLE_FUNDS', 'You have no available funds to withdraw or your withdraw amount exceed the available funds.');
define('ERROR_WITHDRAW_NO_PAYMENT_ACCOUNT_BOOK', 'Please select which account to receive the funds.');
define('ERROR_WITHDRAW_NO_WITHDRAW_AMOUNT', 'Please enter your withdraw amount.');
define('ERROR_WITHDRAW_NOT_SUCCESS', 'Your funds withdraw is failed.');
define('ERROR_WITHDRAW_DISABLE_WITHDRAWAL', "Your payment withdrawal capability has been disabled.\n\rPlease contact <a href='mailto:<EMAIL>'>Customer Support</a> for detail.");
define('WARNING_WITHDRAW_FEES_HIGHER_THAN_WITHDRAW_AMOUNT', 'Withdraw fees is higher than withdraw amount.');
define('WARNING_WITHDRAW_NOT_ALLOWED_PAYMENT_METHOD', '%s is not available for this withdraw amount.');
define('SUCCESS_WITHDRAW_SUCCESS', 'Your funds has been successfully withdraw.');
define('ERROR_INVALID_PM_SELECTION', 'Error: Please select valid payment method.');

define('CONTENTS_WARNING_WITHDRAW_NOT_ALLOWED_PAYMENT_METHOD', '<span class="redIndicator">%s is not available for this withdraw amount</span>');

define('JS_ERROR_NO_WITHDRAW_AMOUNT', 'Please enter your withdraw amount');
define('JS_ERROR_INVALID_WITHDRAW_AMOUNT', 'Invalid withdraw amount');

// E-mail
define('EMAIL_SEPARATOR', '------------------------------------------------------');
define('EMAIL_PAYMENT_PAYMENT_UPDATE_SUBJECT', implode(' ', array(EMAIL_SUBJECT_PREFIX, "Payment Update #%s")));

define('EMAIL_PAYMENT_TEXT_TITLE', 'You request to withdraw %s via %s from your OffGamers Store Balance account.');
define('EMAIL_PAYMENT_TEXT_SUMMARY_TITLE', 'Payment Summary:');
define('EMAIL_PAYMENT_TEXT_PAYMENT_NUMBER', 'Payment Number: %s');
define('EMAIL_PAYMENT_TEXT_PAYMENT_DATE', 'Payment Date: %s');
define('EMAIL_PAYMENT_TEXT_COMMENTS', 'Comment:' . "\n%s\n");
define('EMAIL_PAYMENT_TEXT_STATUS_UPDATE', "Status: %s");

define('EMAIL_TEXT_CLOSING', 'Thank you for supporting ' . STORE_NAME . '.');
define('EMAIL_FOOTER', STORE_EMAIL_SIGNATURE);
?>