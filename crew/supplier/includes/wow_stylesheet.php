<?
header("Content-type: text/css");
//include_once('../includes/configure.php');
$path = isset($_REQUEST['path']) ? $_REQUEST['path'] : '';

$wow_style_string = "
* { 
  font: 12px verdana, arial, sans-serif;
}

h1 { 
  margin: 0;
  padding: 5px 0 5px 0;
  font: 13px georgia, tahoma, sans-serif;
  color: #ffffff;
  font-weight: bold;
}

h2 { 
  margin: 0;
  padding: 0;
  font: 11px georgia, tahoma, sans-serif;
  color: #dfb901;
  font-weight: bold;
}

.char { 
  width: 405px;
  height: 560px;
  padding: 0 0 0 0;
}

.char .main { 
  width: 405px;
  height: 500px;
  background: url('" . $path . "img/borderBottomSides.gif') no-repeat;
  background-position: center bottom;
}

.char .main .top { 
  float: left;
  width: 100%;
  height: 70px;
  text-align: center;
  background: url('" . $path . "img/bgTop.gif') no-repeat;
}
.char .main .page1 { 
  float: left;
}

.char .main .page1 .left { 
  float: left;
  width: 62px;
  height: 430px;
  background: url('" . $path . "img/borderLeft.gif') repeat-y;
  background-position: left top;
  clear: none;
}
.char .main .page1 .right { 
  float: left;
  width: 62px;
  height: 430px;
  background: url('" . $path . "img/borderRight.gif') repeat-y;
  background-position: right top;
}
.char .main .page1 .left .equip { 
  float: left;
  width: 52px;
  height: 43px;
  padding: 3px 0 3px 10px;
  background: url('" . $path . "img/bgItem.gif') no-repeat;
  background-position: 3px 0;
}
.char .main .page1 .right .equip { 
  float: left;
  width: 51px;
  height: 43px;
  padding: 3px 0 3px 7px;
  background: url('" . $path . "img/bgItem.gif') no-repeat;
}
.char .main .page1 .middle { 
  float: left;
  width: 280px;
  height: 430px;
}
.char .main .page1 .middle .portrait { 
  float: left;
  width: 100%;
  height: 260px;
  background-color: #000000;
  background: url('" . $path . "img/bgChar.gif') no-repeat;
}

.char .main .page1 .middle .bottom { 
  float: left;
  width: 100%;
  height: 170px;
}

.char .main .page1 .middle .bottom .padding { 
  height: 100px;
  padding: 0 2px 0 2px;
  background: url('" . $path . "img/bgStats.gif') no-repeat;
  background-position: 0 0px;
}

.char .main .page1 .middle .bottom .padding .stats { 
  float: left;
  width: 50%;
  height: 100%;
  padding: 0;
  margin: 0;
  list-style-type: none;
  color: #DFB801;
}

.char .main .page1 .middle .bottom .padding .stats li { 
  list-style-type: none;
  padding: 1px 0 0 6px;
  text-align: left;
  font: 11px georgia, tahoma, sans-serif;
  font-weight: bold;
}

.char .main .page1 .middle .bottom .padding .stats li ul {  
  list-style-type: none;
  padding: 0;
  margin: 0;
}

.char .main .page1 .middle .bottom .padding .stats li span, .stats ul li span { 
  float: right;
  font-family: georgia, tahoma, sans-serif;
  padding: 0 6px 0 0;
  margin-top: -15px;
}

.char .main .page1 .middle .bottom .padding .stats ul li ul li span { 
  color: #ffffff;
}

.char .main .page1 .middle .bottom .hands { 
  height: 70px;
  width: 280px;
  background: url('" . $path . "img/bgHands.gif') no-repeat;
}

.char .main .page1 .middle .bottom .hands .weapon0 { 
  padding: 16px 0 0 69px;
  float: left;
}

.char .main .page1 .middle .bottom .hands .weapon1 { 
  padding: 16px 0 0 11px;
  float: left;
}

.char .main .page1 .middle .bottom .hands .weapon2 { 
  padding: 16px 0 0 10px;
  float: left;
}

.char .main .page2 { 
  float: left;
  display: none;
}
.char .main .page2 .left { 
  float: left;
  width: 4px;
  height: 430px;
  background: url('" . $path . "img/borderLeft.gif') repeat-y;
  background-position: left top;
}

.char .main .page2 .right { 
  float: left;
  width: 4px;
  height: 430px;
  background: url('" . $path . "img/borderRight.gif') repeat-y;
  background-position: right top;
}

.char .main .page2 .skills { 
  float: left;
  position: relative;
  height: 430px;
  width: 397px;
  overflow: auto;
  background-color: #000000;
}

.char .main .page3 { 
  float: left;
  display: none;
}
.char .main .page3 .left { 
  float: left;
  width: 4px;
  height: 430px;
  background: url('" . $path . "img/borderLeft.gif') repeat-y;
  background-position: left top;
}
.char .main .page3 .right { 
  float: left;
  width: 4px;
  height: 430px;
  background: url('" . $path . "img/borderRight.gif') repeat-y;
  background-position: right top;
}

.char .main .page3 .talents { 
  float: left;
  position: relative;
  height: 430px;
  width: 397px;
  overflow: auto;
  background-color: #000000;
}

.char .main .page4 { 
  float: left;
  display: none;
}
.char .main .page4 .left { 
  float: left;
  width: 4px;
  height: 430px;
  background: url('" . $path . "img/borderLeft.gif') repeat-y;
  background-position: left top;
}
.char .main .page4 .right { 
  float: left;
  width: 4px;
  height: 430px;
  background: url('" . $path . "img/borderRight.gif') repeat-y;
  background-position: right top;
}

.char .main .page4 .honor { 
  float: left;
  position: relative;
  height: 430px;
  width: 397px;
  overflow: auto;
  background: url('" . $path . "img/honor_back.jpg') no-repeat;
  background-position: 32px 20px;
  background-color: #000000;
}

.char .main .page6 { 
  float: left;
  display: none;
}
.char .main .page6 .left { 
  float: left;
  width: 4px;
  height: 430px;
  background: url('" . $path . "img/borderLeft.gif') repeat-y;
  background-position: left top;
}
.char .main .page6 .right { 
  float: left;
  width: 4px;
  height: 430px;
  background: url('" . $path . "img/borderRight.gif') repeat-y;
  background-position: right top;
}

.char .main .page6 .pet { 
  float: left;
  position: relative;
  height: 430px;
  width: 397px;
  overflow: auto;
  background: url('" . $path . "img/bgPet.gif') no-repeat;
  background-position: 50% 80%;
  background-color: #000000;
}

.resistTable{
  background: url('" . $path . "img/Interface/Icons/ResistanceIcons.gif') no-repeat;
}

.char .main .page2 .rep { 
  float: left;
  position: relative;
  height: 430px;
  width: 397px;
  overflow: auto;
  background-color: #000000;
}

.char .bottomBorder { 
  float: left;
  width: 405px;
  height: 10px;
  background: url('" . $path . "img/borderBottom.gif');
  background-position: left top;
  font-size: 0px;
}

.char .tabs { 
  width: 405px;
  height: 20px;
  float: left;
  padding: 0 0 0 5px ;
}
.char .tabs .tab { 
  float: left;
  width: 56px;
  height: 15px;
  background: url('" . $path . "img/tab.gif');
  background-position: left top;
  font: 11px georgia, tahoma, sans-serif;
  font-weight: bold;
  padding: 5px 5px 5px 5px;
  text-align: center;
}

.bag { 
  float: left;
  width: 212px;
  margin: 10px 0 10px;
  color: #ffffff;
  background: url('" . $path . "img/bagBg2.gif') repeat-y;
  background-position: 0 3px;
}

.bag .bagTop { 
  float: left;
  width: 100%;
  background: url('" . $path . "img/bagTopBig.gif') no-repeat;
  height: 52px;
}

.bag .bagTop .bagIcon { 
  float: left;
  width: 50px;
  height: 50px;
  padding: 5px 5px 5px 5px;
} 

.bag .bagTop .bagName { 
  float: left;
  vertical-align: center;
  height: 50px;
  margin: 0;
  padding: 15px 0 10px 0;
  font: 13px georgia, tahoma, sans-serif;
  font-weight: bold;
}

.bag .bagLine { 
  float: left;
  width: 100%;
  padding: 0 0 0 4px;
}

.bag .bagLine .bagNoSlot { 
  float: left;
  width: 40px;
  height: 40px;
  padding: 4px 5px 5px 6px;
}

.bag .bagLine .bagSlot { 
  float: left;
  width: 40px;
  height: 40px;
  padding: 4px 5px 5px 6px;
  background-image: url('" . $path . "img/bagSlot.gif');

}

.bag .bagBottomLine { 
  float: left;
  width: 100%;
  padding: 2px 0 0 4px;
}

.bag .bagBottomLine .bagSlot { 
  float: left;
  width: 40px;
  height: 40px;
  padding: 4px 5px 5px 6px;
  background-image: url('" . $path . "img/bagBottomBox.gif');
  background-position: left top;
}

.bag .bagMoneyBottom { 
  float: left;
  width: 100%;
  background-image: url('" . $path . "img/bagMoneyBottom.gif');
}

.bag .bagMoneyBottom .money { 
  float: right;
  padding: 4px 15px 7px 0;
}

.bag .bagBottom { 
  float: left;
  height: 6px;
  width: 100%;
  background-image: url('" . $path . "img/bagBottom.gif') ;
}
/*
.honors_cat {
	padding: 9px 0px 0px 0px;
}

.honors {
	font-weight: bold;
	font-size: 11;
	padding: 35px 0px 0px 328px;
}
*/

.reputation {
	font-weight: bold;
	color: white;
}

.pet_info_title {
	color: #9b9444;
	font-size: 13px;
}

.pet_info_info {
	color: white;
	font-size: 12px;
}

.skilltype { 
  float: left
  height: 12px;
  padding: 10px 2px 2px 10px;
  font-family: 12px verdana, arial, sans-serif;
  font-weight: bold;
  color: #ffffff;
}

.skill { 
  float: left
  height: 20px;
  width: 360px;
}

.skill .skillbox { 
  position: relative;
  left: 0;
  top: 0;
  width: 360px;
  height: 20px;

}

.skill .skillbox .bg { 
  position: absolute;
  left: 5px;
  top: 0;
  width: 360px;
  height: 20px

}

.skill .skillbox .bit { 
  position: absolute;
  left: 5px;
  top: 1px;
  left: 8px;
  height: 18px;
}

.skill .skillbox .name { 
  position: absolute;
  left: 5px;
  top: 0;
  height: 15px;
  padding: 3px 0px 5px 10px;
  font: 11px georgia, tahoma, sans-serif;
  font-weight: bold;
  color: #aa9900;
  

}

.skill .skillbox .level { 
  position: absolute;
  left: 5px;
  top: 0;
  height: 15px;
  width: 100%;
  text-align: center;
  padding: 3px 0px 5px 10px;
  font: 11px georgia, tahoma, sans-serif;
  color: #ffffff;
  font-weight: bold;
}

.item { 
  position: relative;
  width: 40px;
  height: 40px; 
}

.item .quant {
  position: absolute;
  bottom: 0px;
  right: 0px;
  font: 11px georgia, tahoma, sans-serif;
  background-color: #000000;
  color: #ffffff;
}
.icon { 
  float: left;
  width: 40px;
  height: 40px;
  padding: 1px 0 0 0;
}

.tooltip {

}

.tooltip .tip{
  position: absolute;
  top: 0px;
  left: 45px;
  width: 200px;
  border: 2px solid #aa9900;
  background-color: #000000;
  text-decoration: none;
  font-size: 12px;
  display: none;
  z-index: 100;

}

.tooltip .tip .tooltipline { 
float: left;
 width: 100%;
}

.tooltip .tip .tooltipheader { 
	float: left;
 width: 100%;
  font-weight: bold;
}

img, p { 
  padding: 0;
  margin: 0;
  border: 0;
}

a{
	color: #F3C861;
}

a.charlink{
	font-weight: bold;
	color: #22ff22;
}

em{
	color: #F3C861;
}


a.title {
	color: #000000;
}

td.char{
	background-color: #000000;
	color: #ffffff
}
table.subchar{
	background-color: #000000;
}
td.subchar{
	background-color: #000000;
}

table.bag{
	background-color: #000000;
}

table.invis{
	background-color: #000000;
}
table.tooltip{
	background-color: #000000;
}

font.white{
	font-family: sans-serif;
	font-weight: bold;
	font-size: 10pt;
	color: #ffffff
}

font.yellow{
	font-family: sans-serif;
	font-weight: bold;
	font-size: 10pt;
	color: #aa9900;
}
font.green{
	font-family: sans-serif;
	font-weight: bold;
	font-size: 10pt;
	color: #22ff22;
}
font.red{
	font-family: sans-serif;
	font-weight: bold;
	font-size: 10pt;
	color: #ff2222;
}

.grey {
  color: #808080;
}

.yellow {
  color: #cc9900;
}

.green {
  color: #00bb00;
}

.blue {
  color: #0068ff;
}

.purple {
  color: #8000ff;
}

.white {
  color: #ffffff;
}

.quest {
	color: #dfb901;
}

.questlocation {
	color: #dfb901;
}

.bank { 
  float: left;
  width: 362px;
  color: #ffffff;
  background: url('" . $path . "img/bankBg3.gif') repeat-y;
  background-position: 0 3px;
}

.bank .bankTop { 
  float: left;
  width: 100%;
  background: url('" . $path . "img/bagTopBig.gif') no-repeat;
  height: 52px;
}

.bank .bankTop .bankIcon { 
  float: left;
  width: 50px;
  height: 50px;
  padding: 5px 5px 5px 5px;
} 

.bank .bankTop .bankName { 
  float: left;
  vertical-align: center;
  height: 50px;
  margin: 0;
  padding: 15px 0 10px 0;
  font: 13px georgia, tahoma, sans-serif;
  font-weight: bold;
}

.bank .bankLine { 
  float: left;
  width: 100%;
  padding: 0 0 0 4px;
}

.bank .bankLine .bankNoSlot { 
  float: left;
  width: 40px;
  height: 40px;
  padding: 4px 5px 5px 6px;
}

.bank .bankLine .bankSlot { 
  float: left;
  width: 40px;
  height: 40px;
  padding: 4px 5px 5px 6px;
  background-image: url('" . $path . "img/bankSlot.gif');

}

.bank .bankBottomLine { 
  float: left;
  width: 100%;
  padding: 2px 0 0 4px;
}

.bank .bankBottomLine .bankSlot { 
  float: left;
  width: 40px;
  height: 40px;
  padding: 4px 5px 5px 6px;
  background-image: url('" . $path . "img/bankBottomBox.gif');
  background-position: left top;
}

.bank .bankBottom { 
  float: left;
  height: 6px;
  width: 100%;
  background-image: url('" . $path . "img/bagBottom.gif') ;
}

.m0l0iout {
	font-family: sans-serif, Tahoma, Verdana, Geneva, Arial, Helvetica;
	font-size: 12px;
	text-decoration: none;
	margin: 4px 0 0 8px;
	color: #FFFFFF;
}
.m0l0iover {
	font-family: sans-serif, Tahoma, Verdana, Geneva, Arial, Helvetica;
	font-size: 12px;
	text-decoration: underline;
	margin: 5px 0 0 8px;
	color: #FFFFFF;
}

/* level 0 outer */
.m0l0oout {
	text-decoration : none;
	background-image: url(img/lev0_bg1.gif);
	border: 1px solid #336699;
	background-color: #2C5F93;
}
.m0l0oover {
	text-decoration : none;
	background-image: url(img/lev0_bg2.gif);
	background-color: #2C5F93;
	border: 1px solid #336699;
}

/* level 1 inner */
.m0l1iout {
	font-family: sans-serif, Tahoma, Verdana, Geneva, Arial, Helvetica;
	font-size: 12px;
	margin: 4px 0 0 17px;
	color: #FFFFFF;
}
.m0l1iover {
	font-family: sans-serif, Tahoma, Verdana, Geneva, Arial, Helvetica;
	font-size: 12px;
	margin: 4px 0 0 17px;
	color: #FFFFFF;
}

/* level 1 outer */
.m0l1oout {
	text-decoration : none;
	background-color: #5286BB;
	border: 1px solid #336699;
	padding: 0;
}
.m0l1oover {
	text-decoration : none;
	background-color: #1A4D81;
	background-image: url(img/lev1_arrow.gif);
	background-repeat: no-repeat;
	border: 1px solid #336699;
	padding: 0;
}
";
echo $wow_style_string;
?>