<?php
/*
  	$Id: index.php,v 1.14 2007/01/25 10:13:05 weichen Exp $
	
	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
  	
  	Copyright (c) 2002 osCommerce
	
	Released under the GNU General Public License
*/

require('includes/application_top.php');

$list_time_heading_text = '	<table border="0" cellspacing="0" cellpadding="1">
								<tr>
									<td class="smallText" colspan="2"><a href="javascript:;" onClick="DOMCall(\'purchase_list_section\').className=\'show\'">'.TEXT_TODAY_LIST.'</a></td>
								</tr>
								<tbody id="purchase_list_section" class="hide">';

if (count($list_resources_array) > 0) {
	$offset_str = '';
	$offset = date('Z');
	if ($offset > 0) {
		$hr = (int)$offset/3600;
		$min = (int)($offset - ($hr * 3600)) / 60;
		$offset_str = '+' . sprintf('%02d', $hr) . ':' . sprintf('%02d', $min);
	} else if ($offset < 0) {
		$offset = abs($offset);
		$hr = (int)$offset/3600;
		$min = (int)($offset - ($hr * 3600)) / 60;
		$offset_str = '-' . sprintf('%02d', $hr) . ':' . sprintf('%02d', $min);
	}
	
	foreach ($list_resources_array as $list_id => $list_res) {
		$please_submit_row_html = '';
		if ($list_res["entry_allowed"] == '1') {	// First list
			$txt_list_title = sprintf(TEXT_ACTIVE_LIST, $list_res["list_name"]);
			
			$txt_submit_list_title = sprintf(TEXT_CURRENT_LIST_TYPE, '<a href="'.tep_href_link(FILENAME_SUPPLIER_LIST, 'lid='.$list_id).'" class="textLink">'.TEXT_FIRST_LIST.'</a>');
			if ($list_res["status"] == 'STATUS_AUTO') {
				if ($list_res['first_list_edit_anytime'] != 1 && isset($list_res['first_list_edit_time'])) {
					if (!tep_time_check($list_res['first_start_time'], $list_res['first_end_time'], $list_res['first_list_edit_time'][0].$list_res['first_list_edit_time'][1])
						|| !tep_time_check($list_res['first_edit_time'], $list_res['second_end_time'])) {
						$first_edit_date_obj = new Date(date("Y-m-d " . $list_res['first_edit_time'] . $offset_str));
						$tmp = new Date($first_edit_date_obj);
						$tmp2 = new Date($first_edit_date_obj);
						$tmp->setTZbyID($supplier_pref_setting[KEY_SP_TIME_ZONE]);
						$tmp2->convertTZ($tmp->tz);
						$local_first_edit_time = $tmp2->format("%H:%M:%S");
						
						$txt_submit_list_title = sprintf(TEXT_READ_ONLY_LIST, '<a href="'.tep_href_link(FILENAME_SUPPLIER_LIST, 'lid='.$list_id).'" class="textLink">'.TEXT_FIRST_LIST.'</a>', $local_first_edit_time);
					}
				}
			}
			$please_submit_row_html = '	<tr>
											<td class="smallText" colspan="2">&nbsp;&nbsp;&nbsp;' . $txt_submit_list_title . '</td>
										</tr>';
		} else {
			$show_confirm_list_link = false;
			if (isset($list_res['second_list_order_id'])) {
				if ($list_res['status'] == "STATUS_ON") {
					$show_confirm_list_link = true;
				} else if ($list_res['status'] == "STATUS_AUTO") {
					if (tep_time_check($list_res['second_start_time'], $list_res['second_end_time'])) { // Only allow confirmation list if it is in the confirmation list time range
						$show_confirm_list_link = true;
					}
				}
			}
			
			if ($show_confirm_list_link) {
				$txt_list_title = sprintf(TEXT_ACTIVE_LIST, $list_res["list_name"]);
				$please_submit_row_html = '	<tr>
												<td class="smallText" colspan="2">&nbsp;&nbsp;&nbsp;' . sprintf(TEXT_CURRENT_LIST_TYPE, '<a href="'.tep_href_link(FILENAME_SUPPLIER_LIST, 'oID='.$list_res['second_list_order_id'].'&action=edit', 'NONSSL').'" class="textLink">'.TEXT_CONFIRMATION_LIST.sprintf(TEXT_ORDER_ID, $list_res['second_list_order_id']).'</a>') . '</td>
											</tr>';
			} else {
				$txt_list_title = sprintf(TEXT_INACTIVE_LIST, $list_res["list_name"]);
			}
		}
		$list_time_heading_text .= '<tr>
										<td class="smallText" colspan="2">' . $txt_list_title . '</td>
									</tr>' . $please_submit_row_html;
		
		if ($list_res["status"] == 'STATUS_AUTO') {
			$first_start_date_obj = new Date(date("Y-m-d " . $list_res['first_start_time'] . $offset_str));
			
			$tmp = new Date($first_start_date_obj);
			$tmp2 = new Date($first_start_date_obj);
			$tmp->setTZbyID($supplier_pref_setting[KEY_SP_TIME_ZONE]);
			$tmp2->convertTZ($tmp->tz);
			
			$local_first_start_time = $tmp2->format("%H:%M:%S");
			
			$first_end_date_obj = new Date(date("Y-m-d " . $list_res['first_end_time'] . $offset_str));
			$tmp = new Date($first_end_date_obj);
			$tmp2 = new Date($first_end_date_obj);
			$tmp->setTZbyID($supplier_pref_setting[KEY_SP_TIME_ZONE]);
			$tmp2->convertTZ($tmp->tz);
			
			$local_first_end_time = $tmp2->format("%H:%M:%S");
			
			
			$second_start_date_obj = new Date(date("Y-m-d " . $list_res['second_start_time'] . $offset_str));
			$tmp = new Date($second_start_date_obj);
			$tmp2 = new Date($second_start_date_obj);
			$tmp->setTZbyID($supplier_pref_setting[KEY_SP_TIME_ZONE]);
			$tmp2->convertTZ($tmp->tz);
			
			$local_second_start_time = $tmp2->format("%H:%M:%S");
			
			$second_end_date_obj = new Date(date("Y-m-d " . $list_res['second_end_time'] . $offset_str));
			$tmp = new Date($second_end_date_obj);
			$tmp2 = new Date($second_end_date_obj);
			$tmp->setTZbyID($supplier_pref_setting[KEY_SP_TIME_ZONE]);
			$tmp2->convertTZ($tmp->tz);
			
			$local_second_end_time = $tmp2->format("%H:%M:%S");
			
			$tmp2->convertTZ($first_start_date_obj->tz);
			
			$list_time_heading_text .= sprintf(TEXT_LIST_TIME_CONSTRAINT_TYPE, $local_first_start_time, $local_first_end_time, $local_second_start_time, $local_second_end_time);
		}
	}
}

$list_time_heading_text .= '	</tbody>
							</table>';
?>
<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?=HTML_PARAMS?> >
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=<?php echo CHARSET; ?>">
	<title><?=TITLE?></title>
<?
if (file_exists(DIR_WS_LANGUAGES . $sup_language . '/stylesheet.css')) {
	echo '<link rel="stylesheet" type="text/css" href="'. DIR_WS_LANGUAGES . $sup_language . '/stylesheet.css">';
} else {
	echo '<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">';
}
?>
	<script language="javascript" src="includes/general.js"></script>
</head>
<body bgcolor="#FFFFFF">
	<? require(DIR_WS_INCLUDES . 'header.php'); ?>
	<table border="0" cellspacing="2" cellpadding="2" align="center" class="main" width="100%">
		<tr>
			<td height="100px">&nbsp;</td>
		</tr>
		<tr>
			<td valign="middle">
				<table border="0" cellspacing="0" cellpadding="4" align="center" class="main" width="40%">
					<tr valign="middle">
						<td colspan="2" class="ordersBoxHeading"><?=TEXT_WELCOME." ".$login_first_name."!"?></td>
					</tr>
					<tr class="ordersListingEven">
						<td width="1%" class="smallText" valign="top">1.</td>
						<td width="99%" class="smallText"><?=$list_time_heading_text?></td>
					</tr>
					<tr class="ordersListingOdd">
						<td width="1%" class="smallText">2.</td>
						<td width="99%"><a href="<?=tep_href_link(FILENAME_SUPPLIER_HISTORY)?>"><?=HEADER_TITLE_ORDER_HISTORY?></a></td>
					</tr>
					<tr class="ordersListingEven">
						<td width="1%" class="smallText">3.</td>
						<td width="99%"><a href="<?=tep_href_link(FILENAME_SUPPLIER_PAYMENT)?>"><?=HEADER_TITLE_PAYMENT_HISTORY?></a></td>
					</tr>
					<tr class="ordersListingOdd">
						<td width="1%" class="smallText" valign="top">4.</td>
						<td width="99%">
							<a href="<?=tep_href_link(FILENAME_SUPPLIER_ACCOUNT)?>"><?=HEADER_TITLE_ACCOUNT?></a>
							<br>
							<ul class="extraInfo">
								<li><a href="<?=tep_href_link(FILENAME_SUPPLIER_PAYMENT_BOOK)?>"><?=HEADER_TITLE_PAYMENT_BOOK?></a></li>
								<li><a href="<?=tep_href_link(FILENAME_SUPPLIER_ACCOUNT_STATEMENT)?>"><?=HEADER_TITLE_ACCOUNT_STATEMENT?></a></li>
							</ul>
						</td>
					</tr>
<?
	$menu_count = 0;
	
	$suppliers_id_select_sql = "SELECT suppliers_id FROM " . TABLE_SUPPLIER_TASKS_SETTING . " WHERE suppliers_id ='" . (int)$supplier_id . "' AND custom_products_type_id = 1";
	$suppliers_id_result_sql = tep_db_query($suppliers_id_select_sql);
	
	if (tep_db_num_rows($suppliers_id_result_sql)) {
		$menu_count = $menu_count+1;
?>
					<tr class="ordersListingEven">
						<td width="1%" class="smallText">5.</td>
						<td width="99%"><a href="<?=tep_href_link(FILENAME_SUPPLIER_STAFF)?>"><?=HEADER_TITLE_SUB_USER?></a></td>
					</tr>
					
					<tr class="ordersListingOdd">
						<td width="1%" class="smallText">6.</td>
						<td width="99%"><a href="<?=tep_href_link(FILENAME_SUPPLIER_MAC_ACTIVATION)?>"><?=HEADER_TITLE_MAC_ACTIVATION?></a></td>
					</tr>
<?	}
	
	$supplier_tasks_select_sql = "SELECT orders_products_id FROM " . TABLE_SUPPLIER_TASKS_ALLOCATION . " WHERE suppliers_id ='" . (int)$supplier_id . "'";
	$supplier_tasks_result_sql = tep_db_query($supplier_tasks_select_sql);
	$supplier_tasks_row = tep_db_fetch_array($supplier_tasks_result_sql);
	
	if (tep_not_null($supplier_tasks_row)) {
		$menu_count = $menu_count+1;
?>
					<tr class="<?=(($menu_count % 2 == 0) ? 'ordersListingEven' : 'ordersListingOdd')?>" class="smallText">
						<td width="1%" class="smallText"><?=5 + $menu_count?>.</td>
						<td width="99%" ><a href="<?=tep_href_link(FILENAME_SUPPLIER_PROGRESS_REPORT)?>"><?=HEADER_TITLE_PROGRESS_REPORT?></a></td>
					</tr>
<?	} ?>
					<tr class="<?=(($menu_count % 2 == 0) ? 'ordersListingOdd' : 'ordersListingEven')?>" class="smallText">
						<td width="1%" class="smallText"><?=6 + $menu_count?>.</td>
						<td width="99%" ><a href="<?=tep_href_link(FILENAME_CUSTOM_PRODUCT_PAYMENT)?>"><?=HEADER_TITLE_CUSTOM_PRODUCT_PAYMENT?></a></td>
					</tr>
					<tr class="<?=(($menu_count % 2 == 0) ? 'ordersListingEven' : 'ordersListingOdd')?>" class="smallText">
						<td width="1%" class="smallText"><?=7 + $menu_count?>.</td>
						<td width="99%" ><a href="<?=tep_href_link(FILENAME_LOGOFF)?>"><?=HEADER_TITLE_LOGOFF?></a></td>
					</tr>					
				</table>
			</td>
		</tr>
	</table>
	<? require(DIR_WS_INCLUDES . 'footer.php'); ?>
</body>
</html>