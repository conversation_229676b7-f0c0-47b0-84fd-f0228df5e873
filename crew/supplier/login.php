<?
/*
  $Id: login.php,v 1.6 2014/07/24 06:45:51 chingyen Exp $

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2002 osCommerce

  Released under the GNU General Public License
 */

require('includes/application_top.php');

if (isset($HTTP_GET_VARS['action']) && ($HTTP_GET_VARS['action'] == 'process')) {
    $email_address = tep_db_prepare_input($HTTP_POST_VARS['email_address']);
    $password = tep_db_prepare_input($HTTP_POST_VARS['password']);
    // Check if email exists
    $check_supplier_query = tep_db_query("select supplier_id as login_id, supplier_groups_id as login_groups_id, supplier_firstname as login_firstname, supplier_email_address as login_email_address, supplier_password as login_password, supplier_date_of_last_logon as login_logdate, supplier_number_of_logons as login_lognum from " . TABLE_SUPPLIER . " where supplier_email_address = '" . tep_db_input($email_address) . "' AND supplier_status=1;");
    if (!tep_db_num_rows($check_supplier_query)) {
        $HTTP_GET_VARS['login'] = 'fail';
    } else {
        $check_supplier = tep_db_fetch_array($check_supplier_query);
        // Check that password is good
        if (!tep_validate_password($password, $check_supplier['login_password'])) {
            $HTTP_GET_VARS['login'] = 'fail';
        } else {
            if (tep_session_is_registered('password_forgotten')) {
                tep_session_unregister('password_forgotten');
            }

            $supplier_id = $check_supplier['login_id'];
            $supplier_groups_id = $check_supplier['login_groups_id'];
            $login_first_name = $check_supplier['login_firstname'];
            $login_email_address = $check_supplier['login_email_address'];
            $login_logdate = $check_supplier['login_logdate'];
            $login_lognum = $check_supplier['login_lognum'];

            tep_session_register('supplier_id');
            tep_session_register('supplier_groups_id');
            tep_session_register('login_first_name');
            tep_session_register('login_email_address');

            tep_db_query("update " . TABLE_SUPPLIER . " set supplier_date_of_last_logon = now(), supplier_number_of_logons = supplier_number_of_logons+1 where supplier_id = '" . $supplier_id . "'");

            $pref_array = tep_get_supplier_pref_setting($supplier_id);
            $lang_code_select_sql = "SELECT directory FROM " . TABLE_SUPPLIER_LANGUAGES . " WHERE languages_id = '" . $pref_array[KEY_SP_LANG] . "'";
            $lang_code_result_sql = tep_db_query($lang_code_select_sql);
            if ($lang_code_row = tep_db_fetch_array($lang_code_result_sql)) {
                $sup_language = $lang_code_row['directory'];
                $sup_languages_id = $pref_array[KEY_SP_LANG];
            }

            // capture supplier login history
            $_sql_data = array(
                'supplier_id' => $supplier_id,
                'email_address' => $email_address,
                'login_date' => date('Y-m-d H:i:s'),
                'login_ip' => tep_get_ip_address(),
                'ua_info' => (isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : getenv('HTTP_USER_AGENT')),
                'session_key' => session_id(),
                'session' => json_encode($_SESSION),
            );
            tep_db_perform(TABLE_SUPPLIER_LOGIN_HISTORY, $_sql_data);

            if (($login_lognum == 0) || !($login_logdate) || ($login_email_address == 'admin@localhost') || ($login_modified == '0000-00-00 00:00:00')) {
                tep_redirect(tep_href_link(FILENAME_DEFAULT));
            } else {
                tep_redirect(tep_href_link(FILENAME_DEFAULT));
            }
        }
    }
}

//require(DIR_WS_LANGUAGES . $language . '/' . FILENAME_LOGIN);
?>
<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?= HTML_PARAMS ?>>
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=<?php echo CHARSET; ?>">
        <title><?= TITLE ?></title>
        <?
        if (file_exists(DIR_WS_LANGUAGES . $sup_language . '/stylesheet.css')) {
            echo '<link rel="stylesheet" type="text/css" href="' . DIR_WS_LANGUAGES . $sup_language . '/stylesheet.css">';
        } else {
            echo '<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">';
        }
        ?>
        <style type="text/css"><!--
            a { color:#080381; text-decoration:none; }
            a:hover { color:#aabbdd; text-decoration:underline; }
            a.text:link, a.text:visited { color: #ffffff; text-decoration: none; }
            a:text:hover { color: #000000; text-decoration: underline; }
            a.sub:link, a.sub:visited { color: #080381; text-decoration: none; }
            A.sub:hover { color: #080381; text-decoration: underline; }
            .sub { font-family: Verdana, Arial, Helvetica, sans-serif; font-size: 10px; font-weight: bold; line-height: 1.5; color: #dddddd; }
            .text { font-family: Verdana, Arial, Helvetica, sans-serif; font-size: 11px; font-weight: bold; color: #000000; }
            .smallText { font-family: Verdana, Arial, sans-serif; font-size: 10px; }
            .login_heading { font-family: Verdana, Arial, sans-serif; font-size: 12px; color: #080381;}
            .login { font-family: Verdana, Arial, sans-serif; font-size: 12px; color: #000000;}
            //-->
        </style>
    </head>
    <body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">
        <!-- header //-->
        <? require(DIR_WS_INCLUDES . 'header.php'); ?>
        <!-- header_eof //-->
        <table border="0" width="600" height="100%" cellspacing="0" cellpadding="0" align="center" valign="middle">
            <tr>
                <td>
                    <table border="0" width="600" height="440" cellspacing="0" cellpadding="1" align="center" valign="middle">
                        <tr bgcolor="#000000">
                            <td>
                                <table border="0" width="600" height="440" cellspacing="0" cellpadding="0">
                                    <tr bgcolor="#ffffff" height="50">
                                        <td align="left">&nbsp;</td>            						
                                    </tr>
                                    <tr bgcolor="#ffffff">
                                        <td align="center" valign="middle">
                                            <? echo tep_draw_form('login', FILENAME_LOGIN, 'action=process'); ?>
                                            <table width="280" border="0" cellspacing="0" cellpadding="2">
                                                <tr>
                                                    <td class="login_heading" valign="top">&nbsp;<b><?= HEADING_RETURNING_SUPPLIER ?></b></td>
                                                </tr>
                                                <tr>
                                                    <td height="100%" valign="top" align="center">
                                                        <table border="0" width="100%" height="100%" cellspacing="0" cellpadding="1" bgcolor="#666666">
                                                            <tr>
                                                                <td>
                                                                    <table border="0" width="100%" height="100%" cellspacing="3" cellpadding="2" bgcolor="#F0F0FF">
                                                                        <?
                                                                        if ($HTTP_GET_VARS['login'] == 'fail') {
                                                                            $info_message = TEXT_LOGIN_ERROR;
                                                                        }
                                                                        if (isset($info_message)) {
                                                                            ?>
                                                                            <tr>
                                                                                <td colspan="2" class="smallText" align="center"><?= $info_message ?></td>
                                                                            </tr>
                                                                        <? } else { ?>
                                                                            <tr>
                                                                                <td colspan="2" class="smallText">&nbsp;</td>
                                                                            </tr>
                                                                        <? } ?>                                    
                                                                        <tr>
                                                                            <td class="login" nowrap><?= ENTRY_EMAIL_ADDRESS ?></td>
                                                                            <td class="login"><?= tep_draw_input_field('email_address', '', 'size="30"') ?></td>
                                                                        </tr>
                                                                        <tr>
                                                                            <td class="login"><?= ENTRY_PASSWORD ?></td>
                                                                            <td class="login"><?= tep_draw_password_field('password', '', '', 'size="30"') ?></td>
                                                                        </tr>
                                                                        <tr>
                                                                            <td colspan="2" align="right" valign="top">
                                                                                <?= tep_submit_button(BUTTON_SIGN_IN, ALT_BUTTON_SIGN_IN, '', 'inputButton') ?>
                                                                            </td>
                                                                        </tr>
                                                                    </table>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td valign="top" align="right"><? echo '<a class="sub" href="' . tep_href_link(FILENAME_SUPPLIER_SIGNUP, '', 'SSL') . '">' . TEXT_SIGNUP . '</a><span class="sub">&nbsp;</span>'; ?><br><? echo '<a class="sub" href="' . tep_href_link(FILENAME_PASSWORD_FORGOTTEN, '', 'SSL') . '">' . TEXT_PASSWORD_FORGOTTEN . '</a><span class="sub">&nbsp;</span>'; ?></td>
                                                </tr>
                                            </table>
                                            </form>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td><? require(DIR_WS_INCLUDES . 'footer.php'); ?></td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
        <? if (tep_not_null(GOOGLE_ANALYTICS_ACCOUNT_ID)) { // Google Analytics Tracking ?>
            <script type="text/javascript">
                var gaJsHost = (("https:" == document.location.protocol) ? "https://ssl." : "http://www.");
                document.write(unescape("%3Cscript src='" + gaJsHost + "google-analytics.com/ga.js' type='text/javascript'%3E%3C/script%3E"));
            </script>
            <script type="text/javascript">
                var pageTracker = _gat._getTracker("UA-318255-17");
                pageTracker._initData();
                pageTracker._trackPageview();
            </script>
            <?
            if ($content == CONTENT_CHECKOUT_SUCCESS) {
                if (file_exists(DIR_WS_JAVASCRIPT . 'google_analytics.js.php')) {
                    include_once (DIR_WS_JAVASCRIPT . 'google_analytics.js.php');
                }
            }
        }
        ?>
    </body>
</html>