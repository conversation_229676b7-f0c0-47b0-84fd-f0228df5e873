<?
/*
  	$Id: password_forgotten.php,v 1.3 2006/01/18 02:27:16 weichen Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2002 osCommerce
	
  	Released under the GNU General Public License
*/

require('includes/application_top.php');

if (isset($HTTP_GET_VARS['action']) && ($HTTP_GET_VARS['action'] == 'process')) {
	$email_address = tep_db_prepare_input($HTTP_POST_VARS['email_address']);
    $firstname = tep_db_prepare_input($HTTP_POST_VARS['firstname']);
    $log_times = $HTTP_POST_VARS['log_times']+1;
    if ($log_times >= 4) {
      	tep_session_register('password_forgotten');
    }
    
	// Check if email exists
    $check_supplier_query = tep_db_query("select supplier_id as check_id, supplier_firstname as check_firstname, supplier_lastname as check_lastname, supplier_gender as check_gender, supplier_email_address as check_email_address from " . TABLE_SUPPLIER . " where supplier_email_address = '" . tep_db_input($email_address) . "'");
    if (!tep_db_num_rows($check_supplier_query)) {
      	$HTTP_GET_VARS['login'] = 'fail';
    } else {
      	$check_supplier = tep_db_fetch_array($check_supplier_query);
      	if ($check_supplier['check_firstname'] != $firstname) {
        	$HTTP_GET_VARS['login'] = 'fail';
      	} else {
        	$HTTP_GET_VARS['login'] = 'success';
			
			function randomize() {
          		$salt = "ABCDEFGHIJKLMNOPQRSTUVWXWZabchefghjkmnpqrstuvwxyz0123456789";
          		srand((double)microtime()*1000000); 
          		$i = 0;
    			
          		while ($i <= 7) {
            		$num = rand() % 33;
    	    		$tmp = substr($salt, $num, 1);
    	    		$pass = $pass . $tmp;
    	    		$i++;
  	  			}
  	  			return $pass;
        	}
        	$makePassword = randomize();
      		
        	tep_mail($check_supplier['check_firstname'] . ' ' . $check_supplier['supplier_lastname'], $check_supplier['check_email_address'], SUPPLIER_EMAIL_SUBJECT, tep_get_email_greeting($check_supplier['check_firstname'], $check_supplier['check_lastname'], $check_supplier['check_gender']) . sprintf(SUPPLIER_EMAIL_TEXT, tep_href_link(FILENAME_LOGIN, '', 'SSL', false), $check_supplier['check_email_address'], $makePassword) . SUPPLIER_EMAIL_FOOTER, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
        	tep_db_query("UPDATE " . TABLE_SUPPLIER . " set supplier_password = '" . tep_encrypt_password($makePassword) . "' where supplier_id = '" . $check_supplier['check_id'] . "'");
		}
	}
}

if ($HTTP_GET_VARS['login'] == 'success') {
	$success_message = TEXT_FORGOTTEN_SUCCESS;
} else if ($HTTP_GET_VARS['login'] == 'fail') {
	$info_message = TEXT_FORGOTTEN_ERROR;
}
?>
<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?php echo HTML_PARAMS; ?>>
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=<?php echo CHARSET; ?>">
	<title><?php echo TITLE; ?></title>
<?
if (file_exists(DIR_WS_LANGUAGES . $sup_language . '/stylesheet.css')) {
	echo '<link rel="stylesheet" type="text/css" href="'. DIR_WS_LANGUAGES . $sup_language . '/stylesheet.css">';
} else {
	echo '<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">';
}
?>
	<style type="text/css"><!--
	a { color:#080381; text-decoration:none; }
	a:hover { color:#aabbdd; text-decoration:underline; }
	a.text:link, a.text:visited { color: #ffffff; text-decoration: none; }
	a:text:hover { color: #000000; text-decoration: underline; }
	a.sub:link, a.sub:visited { color: #dddddd; text-decoration: none; }
	A.sub:hover { color: #dddddd; text-decoration: underline; }
	.sub { font-family: Verdana, Arial, Helvetica, sans-serif; font-size: 10px; font-weight: bold; line-height: 1.5; color: #dddddd; }
	.text { font-family: Verdana, Arial, Helvetica, sans-serif; font-size: 11px; font-weight: bold; color: #000000; }
	.login_heading { font-family: Verdana, Arial, sans-serif; font-size: 12px; color: #080381;}
	.login { font-family: Verdana, Arial, sans-serif; font-size: 12px; color: #000000;}
	//--></style>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">
<!-- header //-->
<? require(DIR_WS_INCLUDES . 'header.php'); ?>
<!-- header_eof //-->
	<table border="0" width="600" height="100%" cellspacing="0" cellpadding="0" align="center" valign="middle">
  		<tr>
    		<td>
    			<table border="0" width="600" height="440" cellspacing="0" cellpadding="1" align="center" valign="middle">
      				<tr bgcolor="#000000">
        				<td>
        					<table border="0" width="600" height="440" cellspacing="0" cellpadding="0">
          						<tr bgcolor="#ffffff" height="50">
          							<td align="left">&nbsp;</td>
          						</tr>
          						<tr bgcolor="#ffffff">
            						<td align="center" valign="middle">
                            			<table width="330" border="0" cellspacing="0" cellpadding="2">
                              				<tr>
                                				<td class="login_heading" valign="top">&nbsp;<b><?=HEADING_PASSWORD_FORGOTTEN?></b></td>
                              				</tr>
                              				<tr>
                                				<td height="100%" width="100%" valign="top" align="center">
                                					<table border="0" height="100%" width="100%" cellspacing="0" cellpadding="1" bgcolor="#666666">
                                  						<tr>
                                  							<td>
                                  								<table border="0" width="100%" height="100%" cellspacing="3" cellpadding="2" bgcolor="#F0F0FF">
<?	if (tep_session_is_registered('password_forgotten')) { ?>
								                                    <tr>
								                                      	<td class="smallText"><?=TEXT_FORGOTTEN_FAIL?></td>
								                                    </tr>
								                                    <tr>
								                                      	<td align="center" valign="top"><?=tep_button(BUTTON_BACK, ALT_BUTTON_BACK, tep_href_link(FILENAME_LOGIN, '' , 'SSL'), '', 'inputButton')?></td>
								                                    </tr>
<?	} else if (isset($success_message)) { ?>
								                                    <tr>
								                                      	<td class="smallText"><?=$success_message?></td>
								                                    </tr>
								                                    <tr>
								                                      	<td align="center" valign="top"><?=tep_button(BUTTON_BACK, ALT_BUTTON_BACK, tep_href_link(FILENAME_LOGIN, '' , 'SSL'), '', 'inputButton')?></td>
								                                    </tr>
<?	} else {
		echo tep_draw_form('login', 'password_forgotten.php', 'action=process', 'post');
		if (isset($info_message)) {
?>
                                    								<tr>
                                      									<td colspan="2" class="smallText" align="center"><?=$info_message?><?=tep_draw_hidden_field('log_times', $log_times)?></td>
                                    								</tr>
<?		} else { ?>
								                                    <tr>
								                                      	<td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '100%', '10'); ?><?php echo tep_draw_hidden_field('log_times', '0'); ?></td>
								                                    </tr>
<?		} ?>
                                    								<tr>
								                                      	<td class="login"><?=ENTRY_FIRST_NAME?></td>
								                                      	<td class="login"><?=tep_draw_input_field('firstname', '', 'size="30"')?></td>
								                                    </tr>
								                                    <tr>
								                                      	<td class="login"><?=ENTRY_EMAIL_ADDRESS?></td>
								                                      	<td class="login"><?=tep_draw_input_field('email_address', '', 'size="30"')?></td>
								                                    </tr>
								                                    <tr>
								                                      	<td colspan="2" align="right" valign="top"><? echo tep_submit_button(BUTTON_SUBMIT, ALT_BUTTON_SUBMIT, '', 'inputButton') . '&nbsp;' . tep_button(BUTTON_BACK, ALT_BUTTON_BACK, tep_href_link(FILENAME_LOGIN, '' , 'SSL'), '', 'inputButton'); ?>&nbsp;</td>
								                                    </tr>
								                                    </form>
<?	} ?>   
                                  								</table>
                                  							</td>
                                  						</tr>
                                					</table>
                                				</td>
                              				</tr>
                            			</table>
	            					</td>
          						</tr>
        					</table>
        				</td>
      				</tr>
      				<tr>
        				<td><? require(DIR_WS_INCLUDES . 'footer.php'); ?></td>
      				</tr>
    			</table>
    		</td>
  		</tr>
	</table>
</body>
</html>