<?
header("Cache-Control: no-cache");
header("Cache-Control: post-check=0,pre-check=0");
header("Cache-Control: max-age=0");
header("Pragma: no-cache");

header('Content-Type: text/xml');

include_once('includes/application_top.php');

$action = isset($HTTP_GET_VARS['action']) ? $HTTP_GET_VARS['action'] : '';

if (isset($_SESSION['sup_language']) && tep_not_null($_SESSION['sup_language'])) {
	if (file_exists(DIR_WS_LANGUAGES . $_SESSION['sup_language'] . '.php')) {
		include_once(DIR_WS_LANGUAGES . $_SESSION['sup_language'] . '.php');
	}
	
	if (file_exists(DIR_WS_LANGUAGES . $_SESSION['sup_language'] . '/supplier_pm_book.php')) {
		include_once(DIR_WS_LANGUAGES . $_SESSION['sup_language'] . '/supplier_pm_book.php');
	}
}

echo '<response>';
if (tep_not_null($action)) {
	switch($action) {
		case "add_pm_fields":
		case "edit_pm_fields":
			$res_html = '';
			
			echo "<option_selection><![CDATA[";
			
			$payment_method_id = isset($HTTP_GET_VARS['pm_id']) ? $HTTP_GET_VARS['pm_id'] : '';
			$payment_book_id = isset($HTTP_GET_VARS['b_id']) ? $HTTP_GET_VARS['b_id'] : '';
			
			$payment_fields_select_sql = "	SELECT pmf.*, spabd.payment_methods_fields_value 
											FROM " . TABLE_PAYMENT_METHODS_FIELDS . " AS pmf 
											LEFT JOIN " . TABLE_STORE_PAYMENT_ACCOUNT_BOOK_DETAILS . " spabd 
												ON (pmf.payment_methods_fields_id=spabd.payment_methods_fields_id AND spabd.store_payment_account_book_id = '".tep_db_input($payment_book_id)."')
											WHERE pmf.payment_methods_id = '" . tep_db_input($payment_method_id) . "' 
												AND payment_methods_mode = 'SEND'
												AND payment_methods_fields_status = 1 
											ORDER BY payment_methods_fields_sort_order";
			$payment_fields_result_sql = tep_db_query($payment_fields_select_sql);
//			if (!tep_db_num_rows($payment_fields_result_sql)) { 
//				$payment_fields_select_sql = "	SELECT pmf.*, spabd.payment_methods_fields_value, pm.payment_methods_send_status_mode 
//												FROM " . TABLE_PAYMENT_METHODS_FIELDS . " AS pmf 
//												INNER JOIN " . TABLE_PAYMENT_METHODS . " AS pm 
//													ON pm.payment_methods_parent_id = pmf.payment_methods_id 
//												LEFT JOIN " . TABLE_STORE_PAYMENT_ACCOUNT_BOOK_DETAILS . " spabd 
//													ON (pmf.payment_methods_fields_id=spabd.payment_methods_fields_id AND spabd.store_payment_account_book_id = '".tep_db_input($payment_book_id)."')
//												WHERE pm.payment_methods_id = '" . tep_db_input($payment_method_id) . "' 
//													AND payment_methods_mode = 'SEND' 
//													AND payment_methods_fields_status = 1 
//													AND payment_methods_send_status_mode = 1
//												ORDER BY payment_methods_fields_sort_order";
//				$payment_fields_result_sql = tep_db_query($payment_fields_select_sql);
//			}
			
			while ($payment_fields_row = tep_db_fetch_array($payment_fields_result_sql)) {
				if (!$payment_fields_row['payment_methods_fields_status']) continue;
				$input_section = tep_xmlhttp_draw_payment_fields($payment_fields_row, $payment_fields_row['payment_methods_fields_value']);
				
				$res_html .= '<tr class="inputBoxContents">
								<td width="35%" class="inputLabel" valign="top">'.$payment_fields_row['payment_methods_fields_title'].'</td>
								<td class="inputField" valign="top">'.$input_section['field'].'</td>
							  </tr>';
			}
			
			if (tep_not_null($res_html)) {
				$res_html = '<tr class="inputBoxContents">
								<td width="30%" class="formAreaTitle" colspan="2">' . SECTION_TITLE_FORM_PM_REQUIRED_INFO . '</td>
							 </tr>' . $res_html;
			}
			
			echo '<table border="0" width="100%" cellspacing="0" cellpadding="2">'.$res_html.'</table>';
			echo "]]></option_selection>";
			
			break;
		case "withdraw_pm_info":
			if (file_exists(DIR_WS_LANGUAGES . $_SESSION['sup_language'] . '/withdraw.php')) {
				include_once(DIR_WS_LANGUAGES . $_SESSION['sup_language'] . '/withdraw.php');
			}
			
			$res_html = '';
			$display_trans_fee_array = array();
			$total_fees_amount = 0;
			$allowed_pm = true;
			$withdraw_fees_waived = false;
			$withdraw_error = false;
			
			if (isset($_SESSION['supplier_id'])) {
				$user_id = $_SESSION['supplier_id'];
				$user_role = 'supplier';
			} else {
				$user_id = $_SESSION['customer_id'];
				$user_role = 'customers';
			}
			
			$payment_book_id = isset($HTTP_GET_VARS['b_id']) ? trim($HTTP_GET_VARS['b_id']) : '';
			$withdraw_currency = isset($HTTP_GET_VARS['w_cur']) ? trim($HTTP_GET_VARS['w_cur']) : 0;
			$withdraw_amount = isset($HTTP_GET_VARS['w_amt']) ? (double)$HTTP_GET_VARS['w_amt'] : 0;
			
			$payment_book_select_sql = "	SELECT pm.payment_methods_id, pm.payment_methods_send_mode_name, pm.payment_methods_send_currency, spab.payment_methods_alias, 
													pm.payment_methods_types_id, pm.payment_methods_parent_id 
											FROM " . TABLE_STORE_PAYMENT_ACCOUNT_BOOK . " AS spab 
											INNER JOIN " . TABLE_PAYMENT_METHODS . " AS pm 
												ON spab.payment_methods_id=pm.payment_methods_id 
											WHERE spab.store_payment_account_book_id = '".tep_db_input($payment_book_id)."' 
												AND spab.user_id = '".tep_db_input($user_id)."' 
												AND spab.user_role = '".tep_db_input($user_role)."' 
												AND pm.payment_methods_send_status = 1 ";
			$payment_book_result_sql = tep_db_query($payment_book_select_sql);
			
			if ($payment_book_row = tep_db_fetch_array($payment_book_result_sql)) {
				if ($payment_book_row['payment_methods_send_currency'] == '0') {
					$pm_type_select_sql = "	SELECT payment_methods_types_system_define 
											FROM " . TABLE_PAYMENT_METHODS_TYPES . " 
											WHERE payment_methods_types_id = '" . $payment_book_row['payment_methods_types_id'] . "'";
					$pm_type_result_sql = tep_db_query($pm_type_select_sql);
					$pm_type_row = tep_db_fetch_array($pm_type_result_sql);
					
					if ($pm_type_row['payment_methods_types_system_define'] == '1') {
						$payment_currency = DEFAULT_CURRENCY;
					} else {
						$withdraw_error = true;
						$res_html = '<tr class="inputBoxContents">
										<td width="35%" class="inputLabel" valign="top"></td>
										<td class="inputField" valign="top"><span class="errorText">'.ERROR_INVALID_PM_SELECTION.'</span></td>
									 </tr>';
					}
				} else {
					$payment_currency = $currencies->get_code_by_id($payment_book_row['payment_methods_send_currency']);
				}
				
				if (!$withdraw_error) {
					$res_html =	'<tr class="inputBoxContents">
									<td width="35%" class="inputLabel" valign="top">'.ENTRY_WITHDRAW_PAYMENT_METHOD.'</td>
									<td class="inputField" valign="top">'.$payment_book_row['payment_methods_send_mode_name'].'</td>
								 </tr>';
					
					$trans_fee_array = tep_xmlhttp_calculate_fees($payment_book_row['payment_methods_id'], $withdraw_currency, $withdraw_amount);
					
					if ($trans_fee_array['cost'] > 0) {
						$display_trans_fee_array[] = $currencies->format($trans_fee_array['cost']);
						$total_fees_amount += $currencies->apply_currency_exchange($trans_fee_array['cost'], $withdraw_currency, '', 'sell');
					}
					
					if ($trans_fee_array['percent'] > 0) {
						$display_trans_fee_array[] = $trans_fee_array['percent'] . '%';
						//$total_fees_amount += ($withdraw_amount * $trans_fee_array['percent']) / 100;
						$total_fees_amount += (($withdraw_amount / (1 + ($trans_fee_array['percent'] / 100))) * ($trans_fee_array['percent'] / 100));
					}
					
					$payment_fee_select_sql = "	SELECT * 
												FROM " . TABLE_PAYMENT_FEES . "	
												WHERE payment_methods_id = '".tep_db_input($payment_book_row['payment_methods_id'])."' 
													AND payment_methods_mode = 'SEND'";
					$payment_fee_result_sql = tep_db_query($payment_fee_select_sql);
					if (!tep_db_num_rows($payment_fee_result_sql) && $payment_book_row['payment_methods_parent_id']>0) {
						$payment_fee_select_sql = "	SELECT * 
													FROM " . TABLE_PAYMENT_FEES . "	
													WHERE payment_methods_id = '".tep_db_input($payment_book_row['payment_methods_parent_id'])."' 
														AND payment_methods_mode = 'SEND'";
						$payment_fee_result_sql = tep_db_query($payment_fee_select_sql);
					}
					$payment_fee_row = tep_db_fetch_array($payment_fee_result_sql);
					
					// Need to convert
					$converted_min_withdraw  = $currencies->apply_currency_exchange($payment_fee_row['payment_fees_min'], $withdraw_currency, '', 'sell');
					if ($converted_min_withdraw > 0 && $withdraw_amount < $converted_min_withdraw) {	// Below minimum withdraw amount
						if ($payment_fee_row['payment_fees_below_min'] == 'beneficiary') {
							if (count($display_trans_fee_array)) {
								$withdraw_fees = implode(' + ', $display_trans_fee_array);
							} else {
								$withdraw_fees = TEXT_WITHDRAW_FEES_FREE;
								$withdraw_fees_waived = true;
								//$withdraw_fees = 'DO_NOT_SHOW_FEES';
							}
						} else if ($payment_fee_row['payment_fees_below_min'] == 'payer') {
							$withdraw_fees = TEXT_WITHDRAW_FEES_FREE;
							$withdraw_fees_waived = true;
							//$withdraw_fees = 'DO_NOT_SHOW_FEES';
						} else {
							$withdraw_fees = sprintf(CONTENTS_WARNING_WITHDRAW_NOT_ALLOWED_PAYMENT_METHOD, $payment_book_row['payment_methods_alias']);
						}
					} else {
						if ($payment_fee_row['payment_fees_bear_by'] == 'beneficiary') {
							if (count($display_trans_fee_array)) {
								$withdraw_fees = implode(' + ', $display_trans_fee_array);
							} else {
								$withdraw_fees = TEXT_WITHDRAW_FEES_FREE;
								$withdraw_fees_waived = true;
								//$withdraw_fees = 'DO_NOT_SHOW_FEES';
							}
						} else {
							$withdraw_fees = TEXT_WITHDRAW_FEES_FREE;
							$withdraw_fees_waived = true;
							//$withdraw_fees = 'DO_NOT_SHOW_FEES';
						}
					}
					
					if ($withdraw_fees_waived) { // Reset total fees to 0 if not applicable
						$total_fees_amount = 0;
					}
					
					$converted_max_withdraw  = $currencies->apply_currency_exchange($payment_fee_row['payment_fees_max'], $withdraw_currency, '', 'sell');
					if ($converted_max_withdraw > 0 && $withdraw_amount > $converted_max_withdraw) {	// Over limit
						$withdraw_fees = sprintf(CONTENTS_WARNING_WITHDRAW_NOT_ALLOWED_PAYMENT_METHOD, $payment_book_row['payment_methods_alias']);
					}
					
					$res_html .= '<tr class="inputBoxContents">
									<td width="35%" class="inputLabel" valign="top">'.ENTRY_WITHDRAW_MIN_AMT.'</td>
									<td class="inputField" valign="top">'.($payment_fee_row['payment_fees_min'] > 0 ? $currencies->format($payment_fee_row['payment_fees_min']) : TEXT_NO_WITHDRAW_LIMIT).'</td>
								  </tr>
								  <tr class="inputBoxContents">
									<td class="inputLabel" valign="top">'.ENTRY_WITHDRAW_MAX_AMT.'</td>
									<td class="inputField" valign="top">'.($payment_fee_row['payment_fees_max'] > 0 ? $currencies->format($payment_fee_row['payment_fees_max']) : TEXT_NO_WITHDRAW_LIMIT).'</td>
								  </tr>';
					
					if ($withdraw_fees != 'DO_NOT_SHOW_FEES') {
						$res_html .= '<tr class="inputBoxContents">
										<td class="inputLabel" valign="top">'.ENTRY_WITHDRAW_FEES.'</td>
										<td class="inputField" valign="top">'.$withdraw_fees.'</td>
								  	  </tr>';
					}
					
					$res_html .= '<tr><td colspan="2" height="10px"></td></tr>
								  <tr class="inputBoxContents">
									<td class="inputLabel" valign="top">'.ENTRY_WITHDRAW_FINAL_AMOUNT.'</td>
									<td class="inputField" valign="top">'.$currencies->format($withdraw_amount < $total_fees_amount ? 0 : $withdraw_amount-$total_fees_amount, true, $payment_currency, '' , 'buy').'</td>
								  </tr>
								  <tr><td colspan="2" height="10px"></td></tr>';
					
					$payment_fields_select_sql = "	SELECT pmf.*, spabd.payment_methods_fields_value 
													FROM " . TABLE_PAYMENT_METHODS_FIELDS . " AS pmf 
													LEFT JOIN " . TABLE_STORE_PAYMENT_ACCOUNT_BOOK_DETAILS . " spabd 
														ON (pmf.payment_methods_fields_id=spabd.payment_methods_fields_id AND spabd.store_payment_account_book_id = '".tep_db_input($payment_book_id)."')
													WHERE pmf.payment_methods_id = '" . tep_db_input($payment_book_row['payment_methods_id']) . "' 
														AND pmf.payment_methods_mode = 'SEND' 
														AND pmf.payment_methods_fields_status = 1 
													ORDER BY pmf.payment_methods_fields_sort_order";
					$payment_fields_result_sql = tep_db_query($payment_fields_select_sql);
					
					while ($payment_fields_row = tep_db_fetch_array($payment_fields_result_sql)) {
						$pm_fields_value_str = ($payment_fields_row['payment_methods_fields_type']=='6' || $payment_fields_row['payment_methods_fields_type']=='7') ? str_replace(':~:', "\r\n", $payment_fields_row['payment_methods_fields_option']) : $payment_fields_row['payment_methods_fields_value'];
						$res_html .= '<tr class="inputBoxContents">
										<td class="inputLabel" valign="top" nowrap><i>'.$payment_fields_row['payment_methods_fields_title'].'</i></td>
										<td class="inputField" valign="top">'.nl2br($pm_fields_value_str).'</td>
									  </tr>';
					}
				}
				
				echo "<withdraw_info><![CDATA[";
				echo '<table border="0" width="100%" cellspacing="0" cellpadding="2">'.$res_html.'</table>';
				echo "]]></withdraw_info>";
			}
			
			break;
		default:
			echo "<result>Unknown request!</result>";
			
			break;
	}
}

echo '</response>';

function tep_xmlhttp_draw_payment_fields($field_info_array, $default='') {
	$field_name = 'pm_field['.$field_info_array['payment_methods_fields_id'].']';
	$field_id = 'pm_field'.$field_info_array['payment_methods_fields_id'];
	$required = $field_info_array["payment_methods_fields_required"] == 1 ? true : false ;
	$js_html = '';
	
	switch($field_info_array["payment_methods_fields_type"]) {
		case "1":	// Text Box
			if (tep_not_null($field_info_array["payment_methods_fields_size"])) {
				list($size, $max_len) = explode(',', $field_info_array["payment_methods_fields_size"]);
			}
			
			$param = ' id="'.$field_id.'" SIZE=' . ($size > 0 ? $size : '20') . (isset($max_len) && $max_len > 0 ? " MAXLENGTH=".$max_len : '');
			
			return array ('field' => tep_draw_input_field($field_name, $default, $param) . ($required ? TEXT_FIELD_REQUIRED : ''), 'js' => $js_html);
			
			break;
		case "2":	// Text Area
			if (tep_not_null($field_info_array["payment_methods_fields_size"])) {
				list($row_val, $col_val) = explode(',', $field_info_array["payment_methods_fields_size"]);
			}
			
			$param = ' id="'.$field_id.'" ';
			
			$field_html =	'<div style="float: left;">'.tep_draw_textarea_field($field_name, "soft", $col_val, $row_val, $default, $param, false).'</div>' . 
							'<div>'.($required ? TEXT_FIELD_REQUIRED : '&nbsp;').'</div>';
			
			return array ('field' => $field_html, 'js' => $js_html);
			
			break;
		case "3":	// Dropdown Menu
			$selection_array = array();
			if (tep_not_null($field_info_array["payment_methods_fields_option"])) {
				$selection_list = explode(':~:', $field_info_array["payment_methods_fields_option"]);
				foreach ($selection_list as $val) {
					$selection_array[] = array('id' => tep_db_input($val), 'text' => $val);
				}
			}
			
			$param = ' id="'.$field_id.'" ';
			
			return array ('field' => tep_draw_pull_down_menu($field_name, $selection_array, $default, $param) . ($required ? TEXT_FIELD_REQUIRED : ''), 'js' => $js_html);
			
			break;
		case "4":	// Radio Button
			$js_html = '';
			$update_on_change = false;
			$field_html = '';
			
			$selection_list = explode(':~:', $field_info_array["payment_methods_fields_option"]);
			foreach ($selection_list as $val) {
				$field_html .= tep_draw_radio_field($field_name, tep_db_input($val), ($val == $default ? true : false), '') . '&nbsp;' . $val . '<br>';
			}
			
			if ($required)	$field_html .= TEXT_FIELD_REQUIRED;
			
			return array ('field' => $field_html, 'js' => $js_html);
			
			break;
		case "5":	// Date Selection
			if (tep_not_null($field_info_array["payment_methods_fields_size"])) {
				list($from_date, $period) = explode(',', $field_info_array["payment_methods_fields_size"]);
				if ($from_date == 'TODAY') {
					$from_date = date('Y-m-d');
				}
				return array ('field' => tep_draw_date_box($field_name, $from_date, $period, $default) . ($required ? TEXT_FIELD_REQUIRED : ''));
			}
			
			break;
		case "7":
			$field_info_array["payment_methods_fields_option"] = str_replace(':~:', "\r\n", $field_info_array["payment_methods_fields_option"]);
			
			$hidden_value = tep_draw_hidden_field($field_name, $field_info_array["payment_methods_fields_option"]);	// Use hidden field to pass info of this type. need it to be saved in database for shown in "Edit Order" page as well.
			return array ('field' => $field_info_array["payment_methods_fields_option"] . $hidden_value, 'js' => '');
			
			break;
	}
}

function tep_xmlhttp_calculate_fees($payment_method_id, $withdraw_currency, $withdraw_amount) {
	global $currencies;
	
	$trans_fee_array = array('cost' => 0, 'percent' => 0);
	
	$payment_fee_select_sql = "	SELECT * 
								FROM " . TABLE_PAYMENT_FEES . "	
								WHERE payment_methods_id = '".tep_db_input($payment_method_id)."' 
									AND payment_methods_mode = 'SEND'";
	$payment_fee_result_sql = tep_db_query($payment_fee_select_sql);
	if (!tep_db_num_rows($payment_fee_result_sql)) {
		$payment_fee_select_sql = "	SELECT pf.* 
									FROM " . TABLE_PAYMENT_FEES . "	AS pf 
									INNER JOIN " . TABLE_PAYMENT_METHODS. " AS pm 
										ON pf.payment_methods_id = pm.payment_methods_parent_id
									WHERE pm.payment_methods_id = '".tep_db_input($payment_method_id)."' 
										AND pf.payment_methods_mode = 'SEND'";
		$payment_fee_result_sql = tep_db_query($payment_fee_select_sql);
	}
	$payment_fee_row = tep_db_fetch_array($payment_fee_result_sql);
	
	if ($payment_fee_row['payment_fees_cost_value'] > 0) 	$trans_fee_array['cost'] = $payment_fee_row['payment_fees_cost_value'];
	
	if ($payment_fee_row['payment_fees_cost_percent'] > 0) {
		$percent_fees_not_in_range = false;
		
		$percent_fees = ($withdraw_amount * $payment_fee_row['payment_fees_cost_percent']) / 100;
		
		if ($payment_fee_row['payment_fees_cost_percent_min'] > 0) {
			$w_currency_min_fee = $currencies->apply_currency_exchange($payment_fee_row['payment_fees_cost_percent_min'], $withdraw_currency, '', 'sell');
			if ($percent_fees < $w_currency_min_fee) {
				$percent_fees = $payment_fee_row['payment_fees_cost_percent_min'];
				$percent_fees_not_in_range = true;
			}
		}
		
		if ($payment_fee_row['payment_fees_cost_percent_max'] > 0)	{
			$w_currency_max_fee = $currencies->apply_currency_exchange($payment_fee_row['payment_fees_cost_percent_max'], $withdraw_currency, '', 'sell');
			
			if ($percent_fees > $w_currency_max_fee) {
				$percent_fees = $payment_fee_row['payment_fees_cost_percent_max'];
				$percent_fees_not_in_range = true;
			}
		}
		
		if ($percent_fees_not_in_range) {
			$trans_fee_array['cost'] += $percent_fees;
		} else {
			$trans_fee_array['percent'] += $payment_fee_row['payment_fees_cost_percent'];
		}
	}
	
	return $trans_fee_array;
}
?>