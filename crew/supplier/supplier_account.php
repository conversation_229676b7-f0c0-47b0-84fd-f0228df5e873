<?
/*
  	$Id: supplier_account.php,v 1.9 2007/05/02 08:40:28 sunny Exp $
	
	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
  	
  	Copyright (c) 2002 osCommerce
	
	Released under the GNU General Public License
*/

require('includes/application_top.php');

$process = false;
if (strtolower($_SERVER['REQUEST_METHOD']) == 'post') {
	$process = true;
	$a_gender = tep_db_prepare_input($HTTP_POST_VARS['a_gender']);
    $a_firstname = tep_db_prepare_input($HTTP_POST_VARS['a_firstname']);
    $a_lastname = tep_db_prepare_input($HTTP_POST_VARS['a_lastname']);
    $a_dob = tep_db_prepare_input($HTTP_POST_VARS['a_dob']);
    $a_company = tep_db_prepare_input($HTTP_POST_VARS['a_company']);
    $a_company_taxid = tep_db_prepare_input($HTTP_POST_VARS['a_company_taxid']);
    //$a_payment_check = tep_db_prepare_input($HTTP_POST_VARS['a_payment_check']);
    
    $a_payment_paypal = tep_db_prepare_input($HTTP_POST_VARS['a_payment_paypal']);
    $a_payment_bank_name = tep_db_prepare_input($HTTP_POST_VARS['a_payment_bank_name']);
    //$a_payment_bank_branch_number = tep_db_prepare_input($HTTP_POST_VARS['a_payment_bank_branch_number']);
    $a_payment_bank_swift_code = tep_db_prepare_input($HTTP_POST_VARS['a_payment_bank_swift_code']);
    $payment_bank_address = tep_db_prepare_input($HTTP_POST_VARS['payment_bank_address']);
    $payment_bank_phone = tep_db_prepare_input($HTTP_POST_VARS['payment_bank_phone']);
    $a_payment_bank_account_name = tep_db_prepare_input($HTTP_POST_VARS['a_payment_bank_account_name']);
    $a_payment_bank_account_number = tep_db_prepare_input($HTTP_POST_VARS['a_payment_bank_account_number']);
    $a_street_address = tep_db_prepare_input($HTTP_POST_VARS['a_street_address']);
    $a_suburb = tep_db_prepare_input($HTTP_POST_VARS['a_suburb']);
    $a_postcode = tep_db_prepare_input($HTTP_POST_VARS['a_postcode']);
    $a_city = tep_db_prepare_input($HTTP_POST_VARS['a_city']);
    $a_country=tep_db_prepare_input($HTTP_POST_VARS['a_country']);
    $a_zone_id = tep_db_prepare_input($HTTP_POST_VARS['a_zone_id']);
    $a_state = tep_db_prepare_input($HTTP_POST_VARS['a_state']);
    $a_telephone = tep_db_prepare_input($HTTP_POST_VARS['a_telephone']);
    $a_fax = tep_db_prepare_input($HTTP_POST_VARS['a_fax']);
    $a_password = tep_db_prepare_input($HTTP_POST_VARS['a_password']);
    $a_qq = tep_db_prepare_input($HTTP_POST_VARS['a_qq']);
    $a_msn = tep_db_prepare_input($HTTP_POST_VARS['a_msn']);
    
	
	$error = false; // reset error flag
	
    if (ACCOUNT_GENDER == 'true') {
    	if ( ($a_gender != 'm') && ($a_gender != 'f') ) {
        	$error = true;
        	$entry_gender_error = true;
      	} else
      		$entry_gender_error = false;
    }
	
	if(strtolower($a_gender) == 'm')
		$malechecked = true;
	else
		$femalechecked = true;
	
    if (strlen($a_firstname) < ENTRY_FIRST_NAME_MIN_LENGTH) {
    	$error = true;
      	$entry_firstname_error = true;
    } else
    	$entry_firstname_error = false;
	
    if (strlen($a_lastname) < ENTRY_LAST_NAME_MIN_LENGTH) {
      	$error = true;
      	$entry_lastname_error = true;
    } else
    	$entry_lastname_error = false;
	
    if (ACCOUNT_DOB == 'true') {
      	if (@checkdate(substr(tep_date_raw($a_dob), 4, 2), substr(tep_date_raw($a_dob), 6, 2), substr(tep_date_raw($a_dob), 0, 4))) {
    		$entry_date_of_birth_error = false;
    	} else {
        	$error = true;
        	$entry_date_of_birth_error = true;
      	}
    }
	
    /*if (strlen($a_email_address) < ENTRY_EMAIL_ADDRESS_MIN_LENGTH) {
    	$error = true;
    	$messageStack->add( ENTRY_EMAIL_ADDRESS_ERROR);
      	$entry_email_address_error = true;
    }
	
    if (!tep_validate_email($a_email_address)) {
      	$error = true;
      	$messageStack->add( ENTRY_EMAIL_ADDRESS_CHECK_ERROR);
      	$entry_email_address_check_error = true;
    }
    */
	
    if (strlen($a_street_address) < ENTRY_STREET_ADDRESS_MIN_LENGTH) {
      	$error = true;
      	$entry_street_address_error = true;
    }
	
    if (strlen($a_postcode) < ENTRY_POSTCODE_MIN_LENGTH) {
     	$error = true;
      	$entry_post_code_error = true;
    }
	
    if (strlen($a_city) < ENTRY_CITY_MIN_LENGTH) {
    	$error = true;
      	$entry_city_error = true;
    }
	
    if (!$a_country) {
      	$error = true;
      	$entry_country_error = true;
    }
    
    if (ACCOUNT_STATE == 'true') {
    	if ($entry_country_error) {
    		$error = true;
        	$entry_state_error = true;
      	} else {
	        $entry_state_error = false;
	        $a_zone_id = 0;
	      	$check_query = tep_db_query("select count(*) as total from " . TABLE_ZONES . " where zone_country_id = '" . (int)$a_country . "'");
	      	$check = tep_db_fetch_array($check_query);
	      	$entry_state_has_zones = ($check['total'] > 0);
	      	if ($entry_state_has_zones == true) {
	        	$zone_query = tep_db_query("SELECT zone_id FROM " . TABLE_ZONES . " WHERE zone_country_id = '" . (int)$a_country . "' AND zone_id = '" . (int)$a_state . "'");
	        	if (tep_db_num_rows($zone_query) == 1) {
	          		$zone = tep_db_fetch_array($zone_query);
	          		$a_zone_id = $zone['zone_id'];
	        	} else {
	          		$error = true;
	          		$entry_state_error = true;
	        	}
	      	} else {
	        	if (strlen($a_state) < ENTRY_STATE_MIN_LENGTH) {
	          		$error = true;
	          		$entry_state_error = true;
	        	}
	      	}
      	}
	}
    
    if (strlen($a_telephone) < ENTRY_TELEPHONE_MIN_LENGTH) {
      	$error = true;
      	$entry_telephone_error = true;
    } else {
      	$entry_telephone_error = false;
    }
	
	if (tep_not_null($a_msn) && !tep_validate_email($a_msn)) {
      	$error = true;
      	$entry_msn_error = true;
    } else {
        $entry_msn_error = false;
    }
	
	if (tep_not_null($a_qq) && !is_numeric($a_qq)) {
        $error = true;
      	$entry_qq_error = true;
    } else {
        $entry_qq_error = false;
    }
    
    if ($a_password!="") {
	    $passlen = strlen($a_password);
    	if ($passlen < ENTRY_PASSWORD_MIN_LENGTH) {
	      	$error = true;
	      	$entry_password_error = true;
	    } else {
	      	$entry_password_error = false;
	    }
    }
	
    if ($a_password != $a_confirmation) {
      	$error = true;
      	$entry_password_error_not_match = true;
    } else {
      	$entry_password_error_not_match = false;
    }
	
    /*
    $check_email = tep_db_query("select supplier_email_address from " . TABLE_SUPPLIER . " where supplier_email_address = '" . tep_db_input($a_email_address) . "'");
    if (tep_db_num_rows($check_email)) {
      	$error = true;
      	$messageStack->add( ENTRY_EMAIL_ADDRESS_ERROR_EXISTS);
      	$entry_email_address_exists = true;
    } else {
      	$entry_email_address_exists = false;
    }
    */
	
    // Check Suburb
    $entry_suburb_error = false;
    // Check Fax
    $entry_fax_error = false;
	
    // Check Company 
    $entry_company_error = false;
    $entry_company_taxid_error = false;
	
    // Check Payment
    $entry_payment_check_error = false;
    $entry_payment_paypal_error = false;
    $entry_payment_bank_name_error = false;
    $entry_payment_bank_branch_number_error = false;
    $entry_payment_bank_swift_code_error = false;
    $entry_payment_bank_account_name_error = false;
    $entry_payment_bank_account_number_error = false;
    
    if (!$error) {
		$activation_code = sha1($a_password.$a_firstname.time().tep_rand());
		$sql_data_array = array('supplier_firstname' => $a_firstname,
                              	'supplier_lastname' => $a_lastname,
                              	/*'supplier_email_address' => $a_email_address,*/
                              	/*'supplier_payment_check' => $a_payment_check,*/
                              	'supplier_payment_paypal' => $a_payment_paypal,
                              	'supplier_payment_bank_name' => $a_payment_bank_name,
                              	/*'supplier_payment_bank_branch_number' => $a_payment_bank_branch_number,*/
                              	'supplier_payment_bank_swift_code' => $a_payment_bank_swift_code,
                              	'supplier_payment_bank_address' => $payment_bank_address,
                              	'supplier_payment_bank_telephone' => $payment_bank_phone,
                              	'supplier_payment_bank_account_name' => $a_payment_bank_account_name,
                              	'supplier_payment_bank_account_number' => $a_payment_bank_account_number,
                              	'supplier_street_address' => $a_street_address,
                              	'supplier_postcode' => $a_postcode,
                              	'supplier_city' => $a_city,
                              	'supplier_country_id' => $a_country,
                              	'supplier_telephone' => $a_telephone,
                              	'supplier_fax' => $a_fax,
                              	'supplier_qq' => $a_qq,
                              	'supplier_msn' => $a_msn,
                              	'supplier_date_account_created' => 'now()',
								'supplier_activation_code' => $activation_code);
	
		if (ACCOUNT_GENDER == 'true') $sql_data_array['supplier_gender'] = $a_gender;
	    
	    if (ACCOUNT_DOB == 'true') $sql_data_array['supplier_dob'] = tep_date_raw($a_dob);
		
	    if (ACCOUNT_COMPANY == 'true') {
			$sql_data_array['supplier_company'] = $a_company;
	        $sql_data_array['supplier_company_taxid'] = $a_company_taxid;
		}
		
		if ($a_password!='')	$sql_data_array['supplier_password'] = tep_encrypt_password($a_password);
	    
	    if (ACCOUNT_SUBURB == 'true') $sql_data_array['supplier_suburb'] = $a_suburb;
      	if (ACCOUNT_STATE == 'true') {
        	if ($a_zone_id > 0) {
	        	$sql_data_array['supplier_zone_id'] = $a_zone_id;
 		        $sql_data_array['supplier_state'] = $a_state;
        	} else {
          		$sql_data_array['supplier_zone_id'] = '0';
          		$sql_data_array['supplier_state'] = $a_state;
        	}
      	}
		
		tep_db_perform(TABLE_SUPPLIER, $sql_data_array, 'update', " supplier_id='".$supplier_id."'");		
    	
    	// save supplier preferences
    	$preferences_setting_array = array(KEY_SP_TIME_ZONE, KEY_SP_LANG);
		tep_db_query("DELETE FROM " . TABLE_SUPPLIER_PREFERENCES . " WHERE suppliers_id = '" . tep_db_input($supplier_id) . "' AND supplier_preferences_key IN ('".implode("', '", $preferences_setting_array)."')");
		
		foreach ($preferences_setting_array as $pref_key) {
			$preferences_setting_data_array = array('suppliers_id' => $supplier_id,
													'supplier_preferences_key' => $pref_key,
												 	'supplier_preferences_value' => tep_db_prepare_input($_POST["$pref_key"])
												 );
			tep_db_perform(TABLE_SUPPLIER_PREFERENCES, $preferences_setting_data_array);
			
			if ($pref_key == KEY_SP_LANG && tep_not_null($_POST["$pref_key"])) {
				$lang_code_select_sql = "SELECT directory FROM " . TABLE_SUPPLIER_LANGUAGES . " WHERE languages_id = '". (int)$_POST["$pref_key"] . "'";
	    		$lang_code_result_sql = tep_db_query($lang_code_select_sql);
				if ($lang_code_row = tep_db_fetch_array($lang_code_result_sql)) {
					$sup_language = $lang_code_row['directory'];
	    			$sup_languages_id = (int)$_POST["$pref_key"];
				}
			}
		}
		
		$messageStack->add_session(TEXT_MSG_SUCCESS, 'success');
      	tep_redirect(tep_href_link(FILENAME_SUPPLIER_ACCOUNT));
	}
}

$sql_select = "SELECT * FROM " . TABLE_SUPPLIER . " WHERE supplier_id='".$supplier_id."'";
$select_result = tep_db_query($sql_select);
if ($select_row = tep_db_fetch_array($select_result)) {
	$dob = tep_date_short($select_row['supplier_dob']);	
	$a_gender = $select_row['supplier_gender'];
    $a_firstname = $select_row['supplier_firstname'];
    $a_lastname = $select_row['supplier_lastname'];
    $a_dob = $dob;
    $a_email_address = $select_row['supplier_email_address'];
    $a_company = $select_row['supplier_company'];
    $a_company_taxid = $select_row['supplier_company_taxid'];
    /*$a_payment_check = $select_row['supplier_payment_check'];*/
    $a_payment_paypal = $select_row['supplier_payment_paypal'];
    $a_payment_bank_name = $select_row['supplier_payment_bank_name'];
    //$a_payment_bank_branch_number = $select_row['supplier_payment_bank_branch_number'];
    $a_payment_bank_swift_code = $select_row['supplier_payment_bank_swift_code'];
    $payment_bank_address = $select_row['supplier_payment_bank_address'];
    $payment_bank_phone = $select_row['supplier_payment_bank_telephone'];
    $a_payment_bank_account_name = $select_row['supplier_payment_bank_account_name'];
    $a_payment_bank_account_number = $select_row['supplier_payment_bank_account_number'];
    $a_street_address = $select_row['supplier_street_address'];
    $a_suburb = $select_row['supplier_suburb'];
    $a_postcode = $select_row['supplier_postcode'];
    $a_city = $select_row['supplier_city'];
    $a_country=$select_row['supplier_country_id'];
    $a_zone_id = $select_row['supplier_zone_id'];
    $a_state = $select_row['supplier_state'];
    $a_telephone = $select_row['supplier_telephone'];
    $a_fax = $select_row['supplier_fax'];
    $a_qq = $select_row['supplier_qq'];
    $a_msn = $select_row['supplier_msn'];
    $a_password = '';
   	
    if (strtolower($a_gender) == 'm') {
		$malechecked = true;
	} else {
		$femalechecked = true;
	}
	
    $check_query = tep_db_query("select count(*) as total from " . TABLE_ZONES . " where zone_country_id = '" . tep_db_input($a_country) . "'");
    $check_value = tep_db_fetch_array($check_query);
    $entry_state_has_zones = ($check_value['total'] > 0);	
}

$time_zone_array = array();
foreach($GLOBALS['_DATE_TIMEZONE_DATA'] as $tid => $tres) {
	if (preg_match('/(?:GMT)[+-]?(\d*?)$/i', $tid, $regs)) {
		if ($regs[1] > 0 || $tid == 'GMT') {
			$time_zone_array[] = array('id' => $tid, 'text' => $tres["shortname"]);
		}
	}
}

$pref_language_array = array();
$languages_query = tep_db_query("select languages_id, name from " . TABLE_SUPPLIER_LANGUAGES . " order by sort_order");
while ($languages = tep_db_fetch_array($languages_query)) {
	$pref_language_array[] = array(	'id' => $languages['languages_id'],
									'text' => $languages['name']);
}

//$supplier_pref_setting = tep_get_supplier_pref_setting($supplier_id);

?>

<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?=HTML_PARAMS?>>
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=<?php echo CHARSET; ?>">
	<title><?=TITLE?></title>
<?
if (file_exists(DIR_WS_LANGUAGES . $sup_language . '/stylesheet.css')) {
	echo '<link rel="stylesheet" type="text/css" href="'. DIR_WS_LANGUAGES . $sup_language . '/stylesheet.css">';
} else {
	echo '<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">';
}
?>
	<script language="javascript">
	<!--
		var pls_select = '<?=PULL_DOWN_DEFAULT?>';
	//-->
	</script>
	<script language="javascript" src="includes/javascript/supplier_xmlhttp.js"></script>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">
<!-- header //-->
<? require(DIR_WS_INCLUDES . 'header.php'); ?>
<!-- header_eof //-->
<form action="<?=tep_href_link(FILENAME_SUPPLIER_ACCOUNT)?>" method="post">
	<table width="733" height="100%" align="center" cellpadding="0" cellspacing="2" valign="middle">
  		<tr>
    		<td width="62"><p>&nbsp;</p></td>
  		    <td width="556" class="pageHeading"><b><?=HEADER_FORM_UPDATE_PROFILE?></b></td>
  		    <td width="40" valign="bottom"></td>
  		    <td width="63" class="login_heading">&nbsp;</td>
  		</tr>
  		<tr>
  		  	<td colspan="4">
  		  		<table width="600" height="440" border="0" align="center" cellpadding="1" cellspacing="0" class="smallText" valign="middle">
           			<tr bgcolor="#000000">
              			<td>
              				<table border="0" width="600" height="440" cellspacing="0" cellpadding="0">
                  				<tr bgcolor="#ffffff" height="50">
                    				<td align="left" valign="top" bgcolor="#F0F0FF">
                    					<table class="inputBox" border="0" cellpadding="2" cellspacing="1" width="100%">
                      					<tbody>
                        					<tr class="inputBoxContents">
                          						<td>
                          							<table width="100%" border="0" cellpadding="2" cellspacing="0" class="title">
                              						<tbody>
                                						<tr>
                                  							<td class="formAreaTitle"><?=CATEGORY_PERSONAL?></td>
                                  							<td class="fieldRequired" align="right"><?=FORM_REQUIRED_INFORMATION?></td>
                                						</tr>
                              						</tbody>
                            						</table>
                              						<table border="0" cellpadding="2" cellspacing="0" width="100%">
                                					<tbody>
                                  						<tr>
                                    						<td>
                                    							<table class="inputNestedBox" border="0" cellpadding="2" cellspacing="1" width="100%">
                                        						<tbody>
                                          							<tr class="inputNestedBoxContents">
                                            							<td>
                                            								<table width="100%" border="0" cellpadding="2" cellspacing="2" class="login">
                                                							<tbody>
                                                  								<tr>
                                                    								<td class="inputLabel" width="30%"><?=ENTRY_GENDER?></td>
                                                    								<td class="inputField">
                                                    									<?=tep_draw_radio_field('a_gender', 'm', $malechecked) . MALE?>&nbsp;&nbsp;
                                														<?=tep_draw_radio_field('a_gender', 'f', $femalechecked) . FEMALE?>
                                														<?
		                                                    								if ($entry_gender_error) {
		                                                    									echo ENTRY_GENDER_ERROR;
		                                                    								} else {
		                                                    									echo '&nbsp;<span class="fieldRequired">*</span>';
		                                                    								}
		                                                    							?>
                                													</td>
                                                  								</tr>
                                                  								<tr>
                                                    								<td class="inputLabel"><?=ENTRY_FIRST_NAME?></td>
                                                    								<td class="inputField">
                                                    									<?=tep_draw_input_field('a_firstname',$a_firstname)?>
                                                										<?
		                                                    								if ($entry_firstname_error) {
		                                                    									echo ENTRY_FIRST_NAME_ERROR;
		                                                    								} else {
		                                                    									echo '&nbsp;<span class="fieldRequired">*</span>';
		                                                    								}
		                                                    							?>
	                                                    							</td>
                                                  								</tr>
                                                  								<tr>
                                                    								<td class="inputLabel"><?=ENTRY_LAST_NAME?></td>
                                                    								<td class="inputField">
                                                    									<?=tep_draw_input_field('a_lastname',$a_lastname)?>
                                                    									<?
		                                                    								if ($entry_lastname_error) {
		                                                    									echo ENTRY_FIRST_NAME_ERROR;
		                                                    								} else {
		                                                    									echo '&nbsp;<span class="fieldRequired">*</span>';
		                                                    								}
		                                                    							?>
                                                    								</td>	
                                                  								</tr>
                                                  								<tr>
                                                    								<td class="inputLabel"><?=ENTRY_DATE_OF_BIRTH?></td>
                                                    								<td class="inputField">
                                                    									<?=tep_draw_input_field('a_dob', tep_date_short($a_dob))?>
                                                    									<?
		                                                    								if ($entry_date_of_birth_error) {
		                                                    									echo ENTRY_DATE_OF_BIRTH_ERROR;
		                                                    								} else {
		                                                    									echo '&nbsp;<span class="fieldRequired">* (eg. 05/21/1970)</span>';
		                                                    								}
		                                                    							?>
                                                    								</td>
                                                  								</tr>
                                                  								<tr>
                                                    								<td class="inputLabel"><?=ENTRY_EMAIL_ADDRESS?></td>
                                                    								<td class="inputField"><?=$a_email_address?></td>
                                                  								</tr>
                                                							</tbody>
                                            								</table>
                                            							</td>
                                          							</tr>
                                        						</tbody>
                                    							</table>
                                    						</td>
                                  						</tr>
                                  						<tr>
                                    						<td><?=tep_draw_separator('pixel_trans.gif')?></td>
                                  						</tr>
                                  						<tr>
                                    						<td class="formAreaTitle"><?=CATEGORY_PASSWORD?></td>
                                  						</tr>
                                  						<tr>
                                    						<td>
                                    							<table class="inputNestedBox" border="0" cellpadding="2" cellspacing="1" width="100%">
                                        						<tbody>
                                          							<tr class="inputNestedBoxContents">
                                            							<td>
                                            								<table width="100%" border="0" cellpadding="2" cellspacing="2" class="login">
                                                            				<tbody>
                                                  								<tr>
								                                                    <td class="inputLabel" width="30%"><?=ENTRY_PASSWORD?></td>
								                                                    <td class="inputField">
								                                                    	<?=tep_draw_input_field('a_password',$a_password,'','','password')?>
								                                                    	<?
																						  	if ($entry_password_error == true) {
																						   		echo ENTRY_PASSWORD_ERROR;
																						    } else {
																						    	echo '&nbsp;<span class="fieldRequired">*</span>';
																						  	}
		                                                    							?>
								                                                    </td>
                                                  								</tr>
                                                  								<tr>
                                                    								<td class="inputLabel"><?=ENTRY_PASSWORD_CONFIRMATION?></td>
                                                    								<td class="inputField">
                                                    									<?=tep_draw_input_field('a_confirmation',$a_confirmation,'','','password')?>
                                                    									<?
																						  	if (!$entry_password_error && $entry_password_error_not_match == true) {
																						   		echo ENTRY_PASSWORD_ERROR_NOT_MATCHING;
																						    } else {
																						    	echo '&nbsp;<span class="fieldRequired">*</span>';
																						  	}
		                                                    							?>
                                                    								</td>
                                                  								</tr>
                                                							</tbody>
                                            								</table>
                                            							</td>
                                          							</tr>
                                        						</tbody>
                                    							</table>
                                    						</td>
                                  						</tr>
                                  						<tr>
                                    						<td><?=tep_draw_separator('pixel_trans.gif')?></td>
                                  						</tr>
                                  						<tr>
                                    						<td class="formAreaTitle"><?=CATEGORY_ADDRESS?></td>
                                  						</tr>
                                  						<tr>
                                    						<td>
                                    							<table class="inputNestedBox" border="0" cellpadding="2" cellspacing="1" width="100%">
                                        						<tbody>
                                          							<tr class="inputNestedBoxContents">
                                            							<td>
                                            								<table width="100%" border="0" cellpadding="2" cellspacing="2" class="login">
                                                							<tbody>
                                                  								<tr>
                                                    								<td class="inputLabel" width="30%"><?=ENTRY_STREET_ADDRESS?></td>
                                                    								<td class="inputField">
                                                    									<?=tep_draw_input_field('a_street_address',$a_street_address)?>
                                                    									<?
																						  	if ($entry_street_address_error == true) {
																						   		echo ENTRY_STREET_ADDRESS_ERROR;
																						    } else {
																						    	echo '&nbsp;<span class="fieldRequired">*</span>';
																						  	}
		                                                    							?>
                                                    								</td>
                                                  								</tr>
                                                  								<tr>
                                                    								<td class="inputLabel"><?=ENTRY_SUBURB?></td>
                                                    								<td class="inputField">
                                                    									<?=tep_draw_input_field('a_suburb',$a_suburb)?>
                                                    								</td>
                                                  								</tr>
                                                  								<tr>
                                                    								<td class="inputLabel"><?=ENTRY_POST_CODE?></td>
                                                    								<td class="inputField">
                                                    									<?=tep_draw_input_field('a_postcode',$a_postcode)?>
                                                    									<?
																						  	if ($entry_post_code_error == true) {
																						   		echo ENTRY_POST_CODE_ERROR;
																						    } else {
																						    	echo '&nbsp;<span class="fieldRequired">*</span>';
																						  	}
		                                                    							?>
                                                    								</td>
                                                  								</tr>
                                                  								<tr>
                                                    								<td class="inputLabel"><?=ENTRY_CITY?></td>
                                                    								<td class="inputField">
                                                    									<?=tep_draw_input_field('a_city',$a_city)?>
                                                    									<?
																						  	if ($entry_city_error == true) {
																						   		echo ENTRY_CITY_ERROR;
																						    } else {
																						    	echo '&nbsp;<span class="fieldRequired">*</span>';
																						  	}
		                                                    							?>
                                                    								</td>
                                                  								</tr>
												  								<tr>
					                												<td class="inputLabel"><?=ENTRY_COUNTRY?></td>
					                												<td class="inputField">
					                													<?=tep_get_country_list('a_country', $a_country, 'onChange="refreshDynamicSelectOptions(this, \'state_div\', \'a_state\', \''.(int)$languages_id.'\', true);"'); ?>
					                													<?
																						  	if ($entry_country_error == true) {
																						   		echo ENTRY_COUNTRY_ERROR;
																						    } else {
																						    	echo '&nbsp;<span class="fieldRequired">*</span>';
																						  	}
		                                                    							?>
					                												</td>
					              				  								</tr>
					              				  								<tr>
					                												<td class="inputLabel"><?=ENTRY_STATE?></td>
					                												<td class="inputField">
					                													<div id="state_div" style="float:left;">
<?	if ($process == true) {
	   	if ($entry_state_has_zones == true) {
	       	$zones_array = array( array('id' => '', 'text' => PULL_DOWN_DEFAULT) );
	       	$zones_select_sql = "SELECT zone_id, zone_name FROM " . TABLE_ZONES . " WHERE zone_country_id = '" . (int)$a_country . "' ORDER BY zone_name";
	       	$zones_result_sql = tep_db_query($zones_select_sql);
	       	while ($zones_row = tep_db_fetch_array($zones_result_sql)) {
	       		$zones_array[] = array('id' => $zones_row['zone_id'], 'text' => $zones_row['zone_name']);
	       	}
	       	echo tep_draw_pull_down_menu('a_state', $zones_array, $a_zone_id, 'id="a_state"');
	   	} else {
	       	echo tep_draw_input_field('a_state', $a_state, 'id="a_state"');
	   	}
	} else {
	   	if ($entry_state_has_zones == true) {
	   		$zones_array = array( array('id' => '', 'text' => PULL_DOWN_DEFAULT) );
	       	$zones_select_sql = "SELECT zone_id, zone_name FROM " . TABLE_ZONES . " WHERE zone_country_id = '" . (int)$a_country . "' ORDER BY zone_name";
	       	$zones_result_sql = tep_db_query($zones_select_sql);
	       	while ($zones_row = tep_db_fetch_array($zones_result_sql)) {
	       		$zones_array[] = array('id' => $zones_row['zone_id'], 'text' => $zones_row['zone_name']);
	       	}
			
			echo tep_draw_pull_down_menu('a_state', $zones_array, $a_zone_id, 'id="a_state"');
		} else {
			echo tep_draw_input_field('a_state', $a_state, 'id="a_state"');
		}
	}
	echo '									</div>';
?>
																						<?
																						  	if ($entry_state_error == true) {
																						   		echo ENTRY_STATE_ERROR;
																						    } else {
																						    	echo '&nbsp;<span class="fieldRequired">*</span>';
																						  	}
		                                                    							?>
					                												</td>
					              												</tr>
                                                							</tbody>
                                            								</table>
                                            							</td>
                                          							</tr>
                                        						</tbody>
                                    							</table>
                                    						</td>
                                  						</tr>
                                  						<tr>
                                    						<td><?=tep_draw_separator('pixel_trans.gif')?></td>
                                  						</tr>
                                  						<tr>
                                    						<td class="formAreaTitle"><?=CATEGORY_CONTACT?></td>
                                  						</tr>
                                  						<tr>
                                    						<td>
                                    							<table class="inputNestedBox" border="0" cellpadding="2" cellspacing="1" width="100%">
                                        						<tbody>
                                          							<tr class="inputNestedBoxContents">
                                            							<td>
                                            								<table width="100%" border="0" cellpadding="2" cellspacing="2" class="login">
                                                							<tbody>
                                                  								<tr>
                                                    								<td class="inputLabel" width="30%"><?=ENTRY_TELEPHONE_NUMBER?></td>
                                                    								<td class="inputField">
                                                    									<?=tep_draw_input_field('a_telephone', $a_telephone)?>
                                                    									<?
																						  	if ($entry_telephone_error == true) {
																						   		echo ENTRY_TELEPHONE_NUMBER_ERROR;
																						    } else {
																						    	echo '&nbsp;<span class="fieldRequired">*</span>';
																						  	}
		                                                    							?>
                                                    								</td>
                                                  								</tr>
                                                  								<tr>
                                                    								<td class="inputLabel"><?=ENTRY_FAX_NUMBER?></td>
                                                    								<td class="inputField"><?=tep_draw_input_field('a_fax',$a_fax)?>&nbsp;<span class="fieldRequired"></span> </td>
                                                  								</tr>
                                                  								<tr>
                                                    								<td class="inputLabel"><?=ENTRY_QQ_NUMBER?></td>
                                                    								<td class="inputField"><?=tep_draw_input_field('a_qq',$a_qq)?>&nbsp;<span class="fieldRequired"></span> 
                                                    								    <?
                                                    								        if ($entry_qq_error == true) {
																						   		echo ENTRY_QQ_NUMBER_ERROR;
																						    }
                                                    								    ?>
                                                    								</td>
                                                  								</tr>
                                                  								<tr>
                                                    								<td class="inputLabel"><?=ENTRY_MSN_ADDRESS?></td>
                                                    								<td class="inputField"><?=tep_draw_input_field('a_msn',$a_msn)?>&nbsp;<span class="fieldRequired"></span> 
                                                        								<?
                                                        								    if ($entry_msn_error == true) {
																						   		echo ENTRY_MSN_ADDRESS_ERROR;
																						    }
                                                        								?>
                                                    								</td>
                                                  								</tr>
                                                							</tbody>
                                            								</table>
                                            							</td>
                                          							</tr>
                                        						</tbody>
                                    							</table>
                                    						</td>
                                  						</tr>
                                  						<tr>
			                                    			<td><?=tep_draw_separator('pixel_trans.gif')?></td>
			                                  			</tr>
			                                  			<tr>
			                                    			<td class="formAreaTitle"><?=CATEGORY_PAYMENT?></td>
			                                  			</tr>
			                                  			<tr>
			                                    			<td>
			                                    				<table class="inputNestedBox" border="0" cellpadding="2" cellspacing="1" width="100%">
			                                        			<tbody>
			                                          				<tr>
			                                            				<td>
			                                            					<table width="100%" border="0" cellpadding="2" cellspacing="2" class="login">
			                                            					<tbody>
			                                                  					<!--tr>
			                                                    					<td class="inputLabel" width="30%"><?=ENTRY_SUPPLIER_PAYMENT_CHECK?></td>
			                                                    					<td class="inputField"><?=tep_draw_input_field('a_payment_check',$a_payment_check)?>&nbsp;<span class="fieldRequired"></span> </td>
			                                                  					</tr-->
			                                                  					<tr>
			                                                    					<td class="inputLabel" width="30%"><?=ENTRY_SUPPLIER_PAYMENT_PAYPAL?></td>
			                                                    					<td class="inputField"><?=tep_draw_input_field('a_payment_paypal', $a_payment_paypal, 'size="50"')?>&nbsp;<span class="fieldRequired"></span> </td>
			                                                  					</tr>
			                                                  					<tr>
									                                    			<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '15')?></td>
									                                  			</tr>
			                                                  					<tr>
			                                                    					<td class="inputLabel"><?=ENTRY_SUPPLIER_PAYMENT_BANK_NAME?></td>
			                                                    					<td class="inputField"><?=tep_draw_input_field('a_payment_bank_name',$a_payment_bank_name, 'size="50"')?>&nbsp;<span class="fieldRequired"></span> </td>
			                                                  					</tr>
			                                                  					<!--tr>
			                                                    					<td class="inputLabel"><?=ENTRY_SUPPLIER_PAYMENT_BANK_BRANCH_NUMBER?></td>
			                                                    					<td class="inputField"><?=tep_draw_input_field('a_payment_bank_branch_number',$a_payment_bank_branch_number)?>&nbsp;<span class="fieldRequired"></span> </td>
			                                                  					</tr-->
			                                                  					<tr>
			                                                    					<td class="inputLabel"><?=ENTRY_SUPPLIER_PAYMENT_BANK_SWIFT_CODE?></td>
			                                                    					<td class="inputField"><?=tep_draw_input_field('a_payment_bank_swift_code',$a_payment_bank_swift_code, 'size="50"')?>&nbsp;<span class="fieldRequired"></span> </td>
			                                                  					</tr>
			                                                  					<tr>
			                                                    					<td class="inputLabel" valign="top"><?=ENTRY_SUPPLIER_PAYMENT_BANK_ADDRESS?></td>
			                                                    					<td class="inputField">
			                                                    						<?=tep_draw_textarea_field('payment_bank_address', 'soft', '45', '3', $payment_bank_address)?>
			                                                    					</td>
			                                                  					</tr>
			                                                  					<tr>
			                                                    					<td class="inputLabel"><?=ENTRY_SUPPLIER_PAYMENT_BANK_PHONE_NUMBER?></td>
			                                                    					<td class="inputField"><?=tep_draw_input_field('payment_bank_phone', $payment_bank_phone, 'size="50"')?>&nbsp;<span class="fieldRequired"></span> </td>
			                                                  					</tr>
			                                                  					<tr>
			                                                    					<td class="inputLabel"><?=ENTRY_SUPPLIER_PAYMENT_BANK_ACCOUNT_NAME?></td>
			                                                    					<td class="inputField"><?=tep_draw_input_field('a_payment_bank_account_name', $a_payment_bank_account_name, 'size="50"')?>&nbsp;<span class="fieldRequired"></span> </td>
			                                                  					</tr>
			                                                  					<tr>
			                                                    					<td class="inputLabel"><?=ENTRY_SUPPLIER_PAYMENT_BANK_ACCOUNT_NUMBER?></td>
			                                                    					<td class="inputField"><?=tep_draw_input_field('a_payment_bank_account_number',$a_payment_bank_account_number, 'size="50"')?>&nbsp;<span class="fieldRequired"></span> </td>
			                                                  					</tr>
			                                                				</tbody>
			                                            					</table>
			                                            				</td>
			                                          				</tr>
			                                        			</tbody>
			                                    				</table>
			                                    			</td>
			                                  			</tr>
			                                  			<tr>
			                                    			<td><?=tep_draw_separator('pixel_trans.gif')?></td>
			                                  			</tr>
			                                  			<tr>
			                                    			<td class="formAreaTitle"><?=CATEGORY_PREFERENCES?></td>
			                                  			</tr>
			                                  			<tr>
			                                    			<td>
			                                    				<table class="inputNestedBox" border="0" cellpadding="2" cellspacing="1" width="100%">
			                                        			<tbody>
			                                          				<tr>
			                                            				<td>
			                                            					<table width="100%" border="0" cellpadding="2" cellspacing="2" class="login">
			                                            					<tbody>
			                                                  					<tr>
								                                                    <td class="inputLabel" width="30%"><?=ENTRY_PREF_TIME_ZONE?></td>
								                                                    <td class="inputField"><?=tep_draw_pull_down_menu(KEY_SP_TIME_ZONE, $time_zone_array, $supplier_pref_setting[KEY_SP_TIME_ZONE], '')?></td>
			                                                  					</tr>
			                                                  					<tr>
								                                                    <td class="inputLabel"><?=ENTRY_PREF_LANGUAGE?></td>
								                                                    <td class="inputField"><?=tep_draw_pull_down_menu(KEY_SP_LANG, $pref_language_array, $supplier_pref_setting[KEY_SP_LANG], '')?></td>
			                                                  					</tr>
			                                                				</tbody>
			                                            					</table>
			                                            				</td>
			                                          				</tr>
			                                        			</tbody>
			                                    				</table>
			                                    			</td>
			                                  			</tr>
			                                  			<tr>
			                                    			<td><?=tep_draw_separator('pixel_trans.gif')?></td>
			                                  			</tr>
                                					</tbody>
                            						</table>
                            					</td>
                        					</tr>
                        					<tr>
			                      				<td>
			              							<table class="buttonBox2" border="0" cellpadding="2" cellspacing="1" width="100%">
			                  						<tbody>
			                    						<tr class="buttonBoxContents2">
						                                  	<td width="10">&nbsp;</td>
						                                  	<td align="right">
						                                  		<?=tep_submit_button(IMAGE_UPDATE, IMAGE_UPDATE, '', 'inputButton')?>
						                                  	</td>
						                                  	<td width="10">&nbsp;</td>
			                    						</tr>
			                  						</tbody>
			              							</table>
			              						</td>
			            					</tr>
                      					</tbody>
                    					</table>
                    				</td>
                  				</tr>
              				</table>
              			</td>
            		</tr>
            		<tr>
              			<td><? require(DIR_WS_INCLUDES . 'footer.php'); ?></td>
            		</tr>
          		</table>
          	</td>
	  </tr>
</table>
</form>
</body>
</html>