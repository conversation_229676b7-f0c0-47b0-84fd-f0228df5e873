<?
/*
  	$Id: supplier_account_statement.php,v 1.2 2007/01/29 07:33:01 weichen Exp $
	
	Developer: <PERSON>
  	Copyright (c) 2007 SKC Ventrue
	
  	Released under the GNU General Public License
*/

require('includes/application_top.php');
require(DIR_WS_CLASSES . 'payment_module_info.php');

$pm_object = new payment_module_info($supplier_id, 'supplier');

$action = (isset($_REQUEST['action']) ? $_REQUEST['action'] : '');

switch($action) {
	case "show_report":
		$header_title = HEADER_FORM_ACC_STAT_TITLE;
		$form_content = $pm_object->show_acc_statement(FILENAME_SUPPLIER_ACCOUNT_STATEMENT, 'acc_statement_inputs', $_REQUEST, $messageStack);
		
		break;
	case "reset_session":
    	unset($_SESSION['acc_statement_inputs']);
    	tep_redirect(tep_href_link(FILENAME_SUPPLIER_ACCOUNT_STATEMENT));
    	
    	break;
	default:
		$header_title = HEADER_FORM_ACC_STAT_TITLE;
		$form_content = $pm_object->search_acc_statement(FILENAME_SUPPLIER_ACCOUNT_STATEMENT, 'acc_statement_inputs');
		
		break;
}
?>
<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?=HTML_PARAMS?>>
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=<?php echo CHARSET; ?>">
	<title><?=TITLE?></title>
<?
if (file_exists(DIR_WS_LANGUAGES . $sup_language . '/stylesheet.css')) {
	echo '<link rel="stylesheet" type="text/css" href="'. DIR_WS_LANGUAGES . $sup_language . '/stylesheet.css">';
} else {
	echo '<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">';
}
?>
	<link rel="stylesheet" type="text/css" href="includes/javascript/spiffyCal/spiffyCal_v2_1.css">
	<script language="JavaScript" src="includes/javascript/spiffyCal/spiffyCal_v2_1.js"></script>
	<script language="javascript" src="<?=DIR_WS_INCLUDES?>general.js"></script>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">
<div id="spiffycalendar" class="text"></div>
<!-- Include div for Action button instruction //-->
<div id="dhtmlTooltip"></div>
<script language="javascript" src="<?=DIR_WS_INCLUDES?>javascript/dhtml_tooltip.js"></script>
<!-- End of include div for Action button instruction //-->

<!-- header //-->
<? require(DIR_WS_INCLUDES . 'header.php'); ?>
<!-- header_eof //-->
	<table border="0" width="100%" height="95%" cellspacing="0" cellpadding="2">
  		<tr>
  			<td valign="top" height="30px">
  		  		<table width="100%" border="0" align="center" cellspacing="0" cellpadding="2">
           			<tr>
			    		<td width="10">&nbsp;</td>
			  		    <td class="pageHeading"><b><?=$header_title?></b></td>
			  		    <td width="10">&nbsp;</td>
			  		</tr>
			  	</table>
			</td>
  		</tr>
  		<tr>
  		  	<td valign="top"><?=$form_content?></td>
		</tr>
		<tr>
			<td valign="bottom" height="120px"><? require(DIR_WS_INCLUDES . 'footer.php'); ?></td>
		</tr>
	</table>
</body>
</html>