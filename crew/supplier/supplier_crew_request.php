<?
header("Cache-Control: no-cache");
header("Cache-Control: post-check=0,pre-check=0");
header("Cache-Control: max-age=0");
header("Pragma: no-cache");
header('Content-Type: text/html');

include_once('includes/configure.php');
include_once(DIR_WS_INCLUDES . 'database_tables.php');
include_once(DIR_WS_INCLUDES . 'filenames.php');
include_once(DIR_WS_FUNCTIONS . 'database.php');
include_once(DIR_WS_FUNCTIONS . 'general.php');
include_once(DIR_WS_FUNCTIONS . 'html_output.php');
include_once(DIR_WS_LANGUAGES . '/english/' . FILENAME_SUPPLIER_PROGRESS_REPORT);
include_once(DIR_WS_FUNCTIONS . 'password_funcs.php');
include_once(DIR_WS_FUNCTIONS . 'progress_report_task.php');

// include the mail classes
include_once(DIR_WS_CLASSES . 'mime.php');
include_once(DIR_WS_CLASSES . 'email.php');

tep_db_connect() or die('Unable to connect to database server!');

// set application wide parameters
$configuration_query = tep_db_query('select configuration_key as cfgKey, configuration_value as cfgValue from ' . TABLE_CONFIGURATION);
while ($configuration = tep_db_fetch_array($configuration_query)) {
	define($configuration['cfgKey'], $configuration['cfgValue']);
}

if (file_exists(DIR_WS_LANGUAGES . '/english.php')) {
	include_once(DIR_WS_LANGUAGES . '/english.php');
}

if (file_exists(DIR_WS_LANGUAGES . '/english/supplier_crew_request.php')) {
	include_once(DIR_WS_LANGUAGES . '/english/' . FILENAME_SUPPLIER_CREW_REQUEST);
}

$response_str = '';
$version = isset($_REQUEST["version"]) ? trim(tep_db_prepare_input($_REQUEST["version"])) : '';

if (tep_check_version($version)) {
	$action = isset($_REQUEST["action"]) ? $_REQUEST["action"] : '';
	$username = isset($_REQUEST["username"]) ?  trim(tep_db_prepare_input($_REQUEST["username"])) : '';
	$password = isset($_REQUEST["password"]) ? trim(tep_db_prepare_input($_REQUEST["password"])) : '';
	$new_password = isset($_REQUEST["newpassword"]) ? trim($_REQUEST["newpassword"]) : '';
	$mac_add = isset($_REQUEST["macaddress"]) ? trim($_REQUEST["macaddress"]) : '';
	$reset_password = isset($_REQUEST["reset_pwd"]) ? trim(tep_db_prepare_input($_REQUEST["reset_pwd"])) : '';
	$adminemail = isset($_REQUEST["adminemail"]) ? trim(tep_db_prepare_input($_REQUEST["adminemail"])) : '';
	$supemail = isset($_REQUEST["supemail"]) ? trim(tep_db_prepare_input($_REQUEST["supemail"])) : '';
	$order_reference = isset($_REQUEST["orderid"]) ? trim(tep_db_prepare_input($_REQUEST["orderid"])) : '';
	$skip_authentication_time = isset($_REQUEST["request_time"]) ? (int)$_REQUEST["request_time"] : '';
	
	$ip = tep_get_ip_address();
} else {
	$response_str .= 'error=' . urlencode(LOGIN_ADMIN_ERROR_VERSION) . '&error_code=' . urlencode('OUTDATED_VER') . '&latest_ver=' . urlencode(REMOTE_REDJACK_LEAST_SUPPORT_VERSION);
	$action = '';
}

if (DOWN_FOR_MAINTENANCE == 'true') {
	$response['error'] = ERROR_MAINTAINANCE;
	
	$value_pair_array = array();
	foreach ($response as $key => $val) {
		$value_pair_array[] = "$key=".rawurlencode($val);
	}
	
	echo implode('&', $value_pair_array);
	exit;	// end here
}

switch($action) {
	case "admin_user_auth": // admin login
		$login = false;
		
		/*******************************************************************************************
		$password, $version, order_reference number, admin e-mail address & password is null or not
		********************************************************************************************/
		if (tep_not_null($password) && tep_not_null($adminemail) && tep_not_null($order_reference)) {
			$order_reference_array = explode('-', $order_reference);
			
			if (sizeof($order_reference_array) == 2) {
				if (isset($order_reference_array[0]) && isset($order_reference_array[1])) {
					if (is_numeric($order_reference_array[0]) && is_numeric($order_reference_array[1])) {
						$order_id = $order_reference_array[0];
						$orders_custom_products_number = $order_reference_array[1];
						
						// Check whether admin exist or not
						if (tep_admin_login_check($adminemail, $password)) {
							$login = true;
						} else {
							$response_str .= 'error=' . urlencode(LOGIN_ADMIN_ERROR_ADMIN);
						}
					} else {
						$response_str .= 'error=' . urlencode(LOGIN_ADMIN_ERROR_ORDER_ID_INVALID);
					}
				} else {
					$response_str .= 'error=' . urlencode(LOGIN_ADMIN_ERROR_ORDER_ID_INVALID);
				}
			} else {
				$response_str .= 'error=' . urlencode(LOGIN_ADMIN_ERROR_ORDER_ID_INVALID);
			}
		} else {
			$response_str .= 'error=' . urlencode(LOGIN_ADMIN_ERROR_INPUT);
		}
		
		if ($login) {
			$orders_products_id_select_sql = "	SELECT ocp.orders_products_id 
												FROM " . TABLE_ORDERS_PRODUCTS . " AS op 
												INNER JOIN " . TABLE_ORDERS_CUSTOM_PRODUCTS . " AS ocp 
													ON (ocp.orders_products_id = op.orders_products_id 
														AND ocp.orders_custom_products_key = 'power_leveling_info' 
														AND ocp.orders_custom_products_number='" . tep_db_input($orders_custom_products_number) . "') 
												WHERE orders_id='" . tep_db_input($order_id) . "' 
													AND op.custom_products_type_id='1'";
			
			$orders_products_id_result_sql = tep_db_query($orders_products_id_select_sql);
			$orders_products_id_row = tep_db_fetch_array($orders_products_id_result_sql);
			
			$ip_verification_res = tep_check_category_ip_access($orders_products_id_row["orders_products_id"], $ip);
			
			if ($ip_verification_res['res'] == '1') {
				$response_str .= tep_get_game_info ($order_id, $orders_custom_products_number, 'admin');
			} else {
				$response_str .= 'error=' . urlencode($ip_verification_res['msg']);
			}
		}
		break;
	case "user_auth": // login
		$login = false;
		$supplier_crew_id = '';
		
		/*****************************************************************************************************************************
			Check User Name, $mac_add, $password, $version, order_reference number, supplier e-mail address & password is null or not
		******************************************************************************************************************************/
		if (tep_not_null($username) && tep_not_null($mac_add) && tep_not_null($password) && tep_not_null($order_reference) && tep_not_null($supemail) && strlen($password) > 5) {
			$order_reference_array = explode('-', $order_reference);
				
			if (sizeof($order_reference_array) == 2) {
				if (isset($order_reference_array[0]) && isset($order_reference_array[1])) {
					if (is_numeric($order_reference_array[0]) && is_numeric($order_reference_array[1])) {
						$order_id = $order_reference_array[0];
						$orders_custom_products_number = $order_reference_array[1];
						
						// Check whether supplier exist or not
						if (tep_not_null(tep_supplier_exist($supemail))) {
							$supplier_id = tep_supplier_exist($supemail);
							
							// Check whether supplier staff exist or not
							$check_admin_select_sql = "	SELECT supplier_crew_id, supplier_crew_password 
														FROM " . TABLE_SUPPLIER_CREW . " 
														WHERE supplier_crew_username ='" . tep_db_input($username) . "' AND supplier_id ='" . tep_db_input($supplier_id) . "'";
							
							$check_admin_result_sql = tep_db_query($check_admin_select_sql);
							
							if (tep_db_num_rows($check_admin_result_sql) > 0) {
								$check_admin_row = tep_db_fetch_array($check_admin_result_sql);
								$supplier_crew_id = $check_admin_row['supplier_crew_id'];
								
								if (tep_validate_password($password, $check_admin_row['supplier_crew_password'])) {
									$check_mac_address_select_sql = "	SELECT supplier_crew_mac_address_id, supplier_crew_mac_address_status 
																		FROM " . TABLE_SUPPLIER_CREW_MAC_ADDRESS . " 
																		WHERE supplier_id='" . tep_db_input($supplier_id) . "' 
																			AND supplier_crew_mac_address='" . tep_db_input($mac_add) . "'";
									
									$check_mac_address_result_sql = tep_db_query($check_mac_address_select_sql);
									
									if (tep_db_num_rows($check_mac_address_result_sql) > 0) {
										$check_mac_address_row = tep_db_fetch_array($check_mac_address_result_sql);
										
										if ($check_mac_address_row['supplier_crew_mac_address_status'] == '1') {
											$login = true;
										} else {
											$response_str .= 'error=' . urlencode(LOGIN_ERROR_MAC_INACTIVE);
										}
									} else {
										// new mac_address
										$supplier_tasks_select_sql = "	SELECT sts.supplier_tasks_allocation_physical_slots, count(scma.supplier_crew_mac_address_id) AS total_mac_address 
																		FROM " . TABLE_SUPPLIER_TASKS_SETTING . " AS sts 
																		LEFT JOIN " . TABLE_SUPPLIER_CREW_MAC_ADDRESS . " AS scma 
																			ON (sts.suppliers_id=scma.supplier_id) 
																		WHERE sts.suppliers_id ='" . (int)$supplier_id . "' GROUP BY scma.supplier_crew_mac_address_id";
										$supplier_tasks_result_sql = tep_db_query($supplier_tasks_select_sql);
										$supplier_tasks_row = tep_db_fetch_array($supplier_tasks_result_sql);
										
										if ($supplier_tasks_row['total_mac_address'] < $supplier_tasks_row['supplier_tasks_allocation_physical_slots']) {
											$mac_data_array = array('supplier_crew_mac_address' => tep_db_prepare_input($mac_add),
																	'supplier_crew_mac_address_status' => 2,
																	'request_by_supplier_crew_id' => $check_admin_row['supplier_crew_id'],
																	'request_date' => 'now()',
																	'request_ip' => tep_db_prepare_input($ip),
																	'supplier_crew_mac_comment' => TEXT_PENDING_ACTIVATE,
																	'supplier_id' => (int)$supplier_id
																	);
							          		
											tep_db_perform(TABLE_SUPPLIER_CREW_MAC_ADDRESS, $mac_data_array);
											
											$response_str .= 'error=' . urlencode(LOGIN_ERROR_MAC_INACTIVE);
										} else {
											$response_str .= 'error=' . urlencode(LOGIN_ERROR_OVER_SLOT_LIMIT);
										}
									}
								} else { // Wrong Password
									$response_str .= 'error=' . urlencode(LOGIN_ERROR_PASSWD_NOT_MATCH);
								}
							} else { // Supplier Staff Not Exist
								$response_str .= 'error=' . urlencode(LOGIN_ERROR_SUPPLIER_CREW_NOT_EXIST);
							}
						} else { // Supplier Not Exist
							$response_str .= 'error=' . urlencode(LOGIN_ERROR_SUPPLIER_NOT_EXIST);
						}
					} else { // Invalid Order Id format
						$response_str .= 'error=' . urlencode(LOGIN_ERROR_ORDER_ID_INVALID);
					}
				} else { // Invalid Order Id format
					$response_str .= 'error=' . urlencode(LOGIN_ERROR_ORDER_ID_INVALID);
				}
			} else { // Invalid Order Id format
				$response_str .= 'error=' . urlencode(LOGIN_ERROR_ORDER_ID_INVALID);
			}
		} else {
			$response_str .= 'error=' . urlencode(LOGIN_ERROR_INPUT);
		}
		
		if ($login) {
			$orders_products_id_select_sql = "	SELECT ocp.orders_products_id 
												FROM " . TABLE_ORDERS_PRODUCTS . " AS op 
												INNER JOIN " . TABLE_ORDERS_CUSTOM_PRODUCTS . " AS ocp 
													ON (ocp.orders_products_id = op.orders_products_id 
														AND ocp.orders_custom_products_key = 'power_leveling_info' 
														AND ocp.orders_custom_products_number='" . tep_db_input($orders_custom_products_number) . "') 
												WHERE orders_id='" . tep_db_input($order_id) . "' 
													AND op.custom_products_type_id='1'";
			
			$orders_products_id_result_sql = tep_db_query($orders_products_id_select_sql);
			$orders_products_id_row = tep_db_fetch_array($orders_products_id_result_sql);
			
			$ip_verification_res = tep_check_category_ip_access($orders_products_id_row["orders_products_id"], $ip);
			
			if ($ip_verification_res['res'] == '1') {
				$response_str .= tep_get_game_info ($order_id, $orders_custom_products_number, $supplier_crew_id, $supplier_id);
			} else {
				$response_str .= 'error=' . urlencode($ip_verification_res['msg']);
			}
		}
		break;
	case 'change_pwd': // Change password
		if (tep_not_null($supemail) && tep_not_null($username) && tep_not_null($new_password) && tep_not_null($mac_add) && tep_not_null($version)) {	
			// Check whether supplier exist or not
			if (tep_not_null(tep_supplier_exist($supemail))) {
				$supplier_id = tep_supplier_exist($supemail);
				
				// Check whether supplier staff exist or not
				$check_admin_select_sql = "	SELECT supplier_crew_id, supplier_crew_password 
											FROM " . TABLE_SUPPLIER_CREW . " 
											WHERE supplier_crew_username ='" . tep_db_input($username) . "' AND supplier_id ='" . tep_db_input($supplier_id) . "'";
				
				$check_admin_result_sql = tep_db_query($check_admin_select_sql);
				
				if (tep_db_num_rows($check_admin_result_sql) > 0) {
					$check_admin_row = tep_db_fetch_array($check_admin_result_sql);
					
					if (tep_validate_password($password, $check_admin_row['supplier_crew_password'])) {
						$sql_data_array['supplier_crew_password'] = tep_encrypt_password($new_password);
						tep_db_perform(TABLE_SUPPLIER_CREW, $sql_data_array, 'update', " supplier_crew_id='".tep_db_input($check_admin_row['supplier_crew_id'])."'");
					
						$response_str .= 'success=' . urlencode(SUCCESS_PASSWD_CHANGED);
					} else {
						$response_str .= 'error=' . urlencode(ERROR_PASSWD_NOT_MATCH);
					}
				} else {
					$response_str .= 'error=' . urlencode(ERROR_SUPPLIER_CREW_NOT_EXIST);
				}
			} else {
				$response_str .= 'error=' . urlencode(ERROR_SUPPLIER_NOT_EXIST);
			}
		} else {
			$response_str .= 'error=' . urlencode(ERROR_INPUT);
		}
		
		break;
	case 'retrieve_account_username':
		if (tep_not_null($order_reference)) {
			list($order_id, $orders_custom_products_number) = explode('-', $order_reference);
			
			if (tep_not_null($order_id) && tep_not_null($orders_custom_products_number)) {
				$retrieve_game_id = '';
				
				$orders_custom_products_value_select_sql = "	SELECT ocpUserName.orders_custom_products_value AS account_username, ocpUserName.orders_products_id, ocpUserName.products_id, ocpGameID.orders_custom_products_value AS game_id, s.supplier_email_address 
																FROM " . TABLE_ORDERS_PRODUCTS . " AS op 
																INNER JOIN " . TABLE_ORDERS_CUSTOM_PRODUCTS . " AS ocpUserName 
																	ON (op.orders_products_id = ocpUserName.orders_products_id 
																		AND ocpUserName.orders_custom_products_key = 'power_leveling_account_account_username' 
																		AND ocpUserName.orders_custom_products_number = '" . (int)$orders_custom_products_number . "') 
																INNER JOIN " . TABLE_SUPPLIER_TASKS_ALLOCATION . " AS sta 
																	ON (op.orders_products_id = sta.orders_products_id) 
																INNER JOIN " . TABLE_SUPPLIER . " AS s 
																	ON (sta.suppliers_id = s.supplier_id) 
																LEFT JOIN " . TABLE_ORDERS_CUSTOM_PRODUCTS . " AS ocpGameID 
																	ON (op.orders_products_id = ocpGameID.orders_products_id 
																		AND ocpGameID.orders_custom_products_key = 'power_leveling_account_game' 
																		AND ocpGameID.orders_custom_products_number = '" . (int)$orders_custom_products_number . "') 
																WHERE op.orders_id ='" . (int)$order_id . "'";
				
				$orders_custom_products_value_result_sql = tep_db_query($orders_custom_products_value_select_sql);
				$orders_custom_products_value_row = tep_db_fetch_array($orders_custom_products_value_result_sql);
				
				if (tep_not_null($orders_custom_products_value_row['account_username']) && tep_not_null($orders_custom_products_value_row['supplier_email_address'])) {
					if (tep_not_null($orders_custom_products_value_row['game_id'])) {
						$retrieve_game_id = $orders_custom_products_value_row['game_id'];
					} else {
						$prod_cat_path = tep_get_product_cPath($orders_custom_products_value_row['products_id'], true);
						
						if (tep_not_null($prod_cat_path)) {
							$prod_cat_path_array = explode('_', $prod_cat_path);
							$retrieve_game_id = $prod_cat_path_array[0];
						} else {
							$retrieve_game_id = 0;
						}	
					}
					$response_str .= 'account_username=' . urlencode($orders_custom_products_value_row['account_username']) . '&orders_products_id=' . urlencode($orders_custom_products_value_row['orders_products_id']) . '&game_id=' . urlencode($retrieve_game_id) . '&variant=' . urlencode(REMOTE_REDJACK_PIXEL_VARIANCE) . '&return_error=' . urlencode(REMOTE_REDJACK_SEND_ERROR_REPORT) . '&chance=' . urlencode(REMOTE_REDJACK_LEAST_MATCH_PIXEL) . '&login_user_email=' . urlencode($orders_custom_products_value_row['supplier_email_address']);
				} else {
					$response_str .= 'error=' . urlencode(LOGIN_ERROR_INFO);
				}
			} else {
				$response_str .= 'error=' . urlencode(LOGIN_ADMIN_ERROR_ORDER_ID_INVALID);
			}
		}
		break;
}

function tep_admin_login_check ($admin_email, $admin_password_plain) {
	$admin_info_select_sql = "	SELECT admin_id, admin_groups_id, admin_password 
								FROM " . TABLE_ADMIN . " 
								WHERE admin_email_address = '" . tep_db_input($admin_email) . "'";
	
	$admin_info_result_sql = tep_db_query($admin_info_select_sql);
	if ($admin_info_row = tep_db_fetch_array($admin_info_result_sql)) {
		$files_actions_select_sql = "	SELECT afa.admin_files_actions_id 
	  									FROM " . TABLE_ADMIN_FILES_ACTIONS . " AS afa 
	  									INNER JOIN " . TABLE_ADMIN_FILES . " AS af 
	  										ON afa.admin_files_id=af.admin_files_id 
	  									WHERE af.admin_files_name='" . FILENAME_PROGRESS_REPORT . "' 
	  										AND af.admin_files_is_boxes=0 
	  										AND afa.admin_files_actions_key='GAME_REMOTE_ACCOUNT_LOGIN_ACCESS' 
	  										AND FIND_IN_SET( '" . $admin_info_row['admin_groups_id'] . "', afa.admin_groups_id)";
	  	$files_actions_result_sql = tep_db_query($files_actions_select_sql);
	  	
	  	if (tep_db_num_rows($files_actions_result_sql)) {
	  		if (tep_validate_password($admin_password_plain, $admin_info_row['admin_password'])) {
				return true;
			} else {
				return false;
			}
	  	} else {
	  		return false;
	  	}
	} else {
		return false;
	}
}

function tep_supplier_exist ($supplier_email) {
	$supplier_id_select_sql = "SELECT supplier_id FROM " . TABLE_SUPPLIER . " WHERE supplier_email_address='" . tep_db_input($supplier_email) . "'";
	$supplier_id_result_sql = tep_db_query($supplier_id_select_sql);
	$supplier_id_row = tep_db_fetch_array($supplier_id_result_sql);
	
	return $supplier_id_row['supplier_id'];
}

function tep_check_version ($version) {
	if ($version == REMOTE_REDJACK_LEAST_SUPPORT_VERSION || tep_first_version_is_greater($version, REMOTE_REDJACK_LEAST_SUPPORT_VERSION)) {
		return true;
	} else {
		return false;
	}
}

function tep_get_game_info ($order_id, $orders_custom_products_number, $supplier_crew_id, $supplier_id=0) {
	$result_str = '';
	
	if (tep_not_null($supplier_crew_id)) {
		$supplier_tasks_allocation_table_str = '';
		
		$order_custom_product_info_select_sql_str = "	SELECT ocp.orders_custom_products_value AS game_info, ocp.orders_products_id, op.products_categories_id, op.products_id 
														FROM " . TABLE_ORDERS_PRODUCTS . " AS op 
														INNER JOIN " . TABLE_ORDERS_CUSTOM_PRODUCTS . " AS ocp 
															ON (op.orders_products_id = ocp.orders_products_id 
																AND ocp.orders_custom_products_key = 'power_leveling_info' 
																AND ocp.orders_custom_products_number = '".(int)$orders_custom_products_number."') ";
		
		if (is_numeric($supplier_crew_id)) {
			$supplier_tasks_allocation_table_str = "	INNER JOIN " . TABLE_SUPPLIER_TASKS_ALLOCATION . " AS sta 
															ON (op.orders_products_id = sta.orders_products_id 
																AND sta.suppliers_id = '".(int)$supplier_id."'
																AND sta.supplier_tasks_status NOT IN (4, 3) ) ";
			$order_custom_product_where_str = "	WHERE op.orders_id ='" . (int)$order_id . "' AND op.custom_products_type_id = 1 AND sta.supplier_crew_id ='" . (int)$supplier_crew_id . "'";
		} else {
			$order_custom_product_where_str = "	WHERE op.orders_id ='" . (int)$order_id . "' AND op.custom_products_type_id = 1";
		}
		
		$order_custom_product_info_select_sql = $order_custom_product_info_select_sql_str . $supplier_tasks_allocation_table_str . $order_custom_product_where_str;
		$order_custom_product_info_result_sql = tep_db_query($order_custom_product_info_select_sql);
		$order_custom_product_info_row = tep_db_fetch_array($order_custom_product_info_result_sql);
		
		if (tep_not_null($order_custom_product_info_row['game_info'])) {
			$game_id = '';
			$account_non_general_info_array = tep_get_custom_product_info_option_class($order_custom_product_info_row['orders_products_id']);
			
			foreach ($account_non_general_info_array as $key => $info_array) {
				switch($key) {
					case 'account_username':
						$account_name = tep_db_prepare_input($info_array['value']);
						break;
					case 'account_password':
						$password = tep_StringEncryptRC4(tep_db_prepare_input($info_array['value']), 'ab$82@64e#ca2');
						break;
					case 'character_name':
						$character = tep_db_prepare_input($info_array['value']);
						break;
					case 'realm':
						$realm = tep_db_prepare_input($info_array['value']);
						break;
					case 'faction':
						$faction = tep_db_prepare_input($info_array['value']);
						break;
					case 'game':
						$game_id = tep_db_prepare_input($info_array['value']);
						break;
				}
			}
			
			if (!tep_not_null($game_id)) {
				$prod_cat_path = tep_get_product_cPath($order_custom_product_info_row['products_id'], true);
				
				if (tep_not_null($prod_cat_path)) {
					$prod_cat_path_array = explode('_', $prod_cat_path);
					$game_id = $prod_cat_path_array[1];
				} else {
					$game_id = 0;
				}
			}
			
			if (tep_not_null($game_id) && tep_not_null($account_name) && tep_not_null($password)) {
				$result_str .= 'gameid=' . urlencode($game_id) . '&realm=' . urlencode($realm) . '&character=' . urlencode($character) . '&account=' . urlencode($account_name) . '&password=' . urlencode($password) . '&faction=' . urlencode($faction) . '&error_report=' . urlencode(REMOTE_REDJACK_SEND_ERROR_REPORT) . '&variant=' . urlencode(REMOTE_REDJACK_PIXEL_VARIANCE) . '&chance=' . urlencode(REMOTE_REDJACK_LEAST_MATCH_PIXEL);
			} else {
				$result_str .= 'error=' . urlencode(LOGIN_ERROR_INFO);
			}
		} else {
			$result_str .= 'error=' . urlencode(LOGIN_ERROR_INFO);
		}
	} else {
		$result_str .= 'error=' . urlencode(LOGIN_ERROR_INFO);
	}
	
	return $result_str;
}

echo $response_str;
?>