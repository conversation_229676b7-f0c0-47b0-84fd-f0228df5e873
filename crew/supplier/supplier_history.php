<?
require('includes/application_top.php');
require_once(DIR_WS_CLASSES . 'order.php');
require_once(DIR_WS_CLASSES . 'payment.php');

$currencies->set_decimal_places(DISPLAY_PRICE_DECIMAL);

$action = (isset($_REQUEST['action']) ? $_REQUEST['action'] : '');

$status_options = array();
$status_options['0'] = "--";
$order_status_select_sql = "SELECT supplier_list_status_id, supplier_list_status_name FROM " . TABLE_SUPPLIER_LIST_STATUS . " WHERE language_id='". ((int)$sup_languages_id > 0 ? (int)$sup_languages_id : $languages_id) . "' ORDER BY supplier_list_status_sort_order";
$order_status_result_sql = tep_db_query($order_status_select_sql);
while ($order_status_row = tep_db_fetch_array($order_status_result_sql)) {
	$status_options[$order_status_row["supplier_list_status_id"]] = $order_status_row["supplier_list_status_name"];
	if ($order_status_row["supplier_list_status_id"] == 5) $status_options[$order_status_row["supplier_list_status_id"]] .= ' (' . TEXT_FIRST_LIST . ')';
	if ($order_status_row["supplier_list_status_id"] == 1) $status_options[$order_status_row["supplier_list_status_id"]] .= ' (' . TEXT_CONFIRMATION_LIST .')';
}

$server_status_array = array(	'urgent' => array('name' => 'icon_status_urgent'),
								'important' => array('name' => 'icon_status_important'),
								'normal' => array('name' => 'icon_status_normal')
							);

switch($action) {
	case 'view':
		$oID = tep_db_prepare_input($_REQUEST['oID']);
		
		$order_exists = false;
		$current_order_status_select_sql = "SELECT supplier_order_lists_status FROM " . TABLE_SUPPLIER_ORDER_LISTS . " WHERE supplier_order_lists_id = '" . tep_db_input($oID) . "' AND suppliers_id = '" . tep_db_input($supplier_id) . "'";
		$current_order_status_result_sql = tep_db_query($current_order_status_select_sql);
		if ($current_order_status_row = tep_db_fetch_array($current_order_status_result_sql)) {
		    $order_exists = true;
		} else {
			$messageStack->add_session(ERROR_ORDER_DOES_NOT_EXIST, 'error');
			tep_redirect(tep_href_link(FILENAME_SUPPLIER_HISTORY));
		}
		
		break;
	default:
		$orders_select_str = "select sol.supplier_order_lists_id, sol.products_purchases_lists_id, sol.products_purchases_lists_name, sol.suppliers_name, sol.suppliers_id, sol.suppliers_email_address, DATE_FORMAT(sol.supplier_order_lists_date, '%Y-%m-%d %H:%i:%s') AS date_submitted, sol.supplier_order_lists_last_modified AS last_modified, sol.currency, sol.currency_value, sls.supplier_list_status_name from " . TABLE_SUPPLIER_ORDER_LISTS . " as sol inner join " . TABLE_SUPPLIER_LIST_STATUS . " as sls on sol.supplier_order_lists_status = sls.supplier_list_status_id left join " . TABLE_SUPPLIER . " as s on sol.suppliers_id=s.supplier_id ";
		
		$orders_status_select_str = "select distinct(sol.supplier_order_lists_status) as status_id from " . TABLE_SUPPLIER_ORDER_LISTS . " as sol inner join " . TABLE_SUPPLIER_LIST_STATUS . " as sls on sol.supplier_order_lists_status = sls.supplier_list_status_id left join " . TABLE_SUPPLIER . " as s on sol.suppliers_id=s.supplier_id ";
		$orders_where_str = " where sls.language_id = '1' and sol.suppliers_id = '" . tep_db_input($supplier_id) . "'";
		$orders_group_by_str = " group by sol.supplier_order_lists_id ";
		$orders_order_by_str = " order by sol.supplier_order_lists_id DESC ";
		
		$auto_get_status_select_sql = $orders_status_select_str . $orders_where_str . " group by sol.supplier_order_lists_id order by sls.supplier_list_status_sort_order ASC ";
		$auto_get_status_result_sql = tep_db_query($auto_get_status_select_sql);
		while($auto_get_status_row = tep_db_fetch_array($auto_get_status_result_sql)) {
			$show_order_status[] = $auto_get_status_row["status_id"];
		}
		
		break;
}
?>
<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?=HTML_PARAMS?> >
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=<?=CHARSET?>">
	<title><?=TITLE?></title>
<?
if (file_exists(DIR_WS_LANGUAGES . $sup_language . '/stylesheet.css')) {
	echo '<link rel="stylesheet" type="text/css" href="'. DIR_WS_LANGUAGES . $sup_language . '/stylesheet.css">';
} else {
	echo '<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">';
}
?>
	<script language="javascript" src="includes/general.js"></script>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">
<!-- header //-->
<? require(DIR_WS_INCLUDES . 'header.php'); ?>
<!-- header_eof //-->

	<table border="0" width="100%" cellspacing="0" cellpadding="2">
  		<tr>
    		<td width="100%" valign="top">
				<table border="0" width="100%" cellspacing="0" cellpadding="2" align="center" bgcolor="#ffffff">
					<tr>
            			<td valign="top" class="pageHeading"><?=HEADING_TITLE?></td>
					</tr>
					<tr>
          				<td>
<?
if ($action == 'view') {
	$supplier_order = new order($oID);
?>
							<table border="0" width="100%" cellspacing="0" cellpadding="2" style="border-collapse: collapse;">
			      				<tr>
							  		<td align="right">
							  		<?
							  			echo tep_draw_form('back_form', FILENAME_SUPPLIER_HISTORY, tep_get_all_get_params(array('oID', 'action')), 'GET', '');
							  			echo tep_submit_button(BUTTON_BACK, ALT_BUTTON_BACK, '', 'inputButton');
							  			echo '</form>';
							  		?>
				 					</td>
				 				</tr>
								<tr>
			        				<td width="100%">
			        					<table border="0" width="100%" cellspacing="0" cellpadding="0">
			        						<tr>
			            						<td valign="top" width="80%">
			            							<table width="100%" border="0" cellspacing="0" cellpadding="2">
														<tr>
															<td class="main" width="15%"><b><?=ENTRY_SUPPLIER_ORDER_ID?></b></td>
															<td class="main"><b><?=$oID?></b></td>
														</tr>
														<tr>
															<td class="main"><b><?=ENTRY_ORDER_STATUS?></b></td>
															<td class="main"><?=$status_options[$supplier_order->info["orders_status"]]?></td>
														</tr>
														<tr>
															<td class="main"><b><?=ENTRY_ORDER_LIST_NAME?></b></td>
															<td class="main"><?=$supplier_order->info["list_name"]?></td>
														</tr>
														<tr>
															<td class="main"><b><?=ENTRY_ORDER_DATE_TIME?></b></td>
															<td class="main"><?=tep_datetime_short($supplier_order->info["date_submitted"])?></td>
														</tr>
														<tr>
															<td class="main"><b><?=ENTRY_DATE_LAST_MODIFIED?></b></td>
															<td class="main"><?=tep_datetime_short($supplier_order->info["last_modified"])?></td>
														</tr>
													</table>
												</td>
											</tr>
										</table>
			        				</td>
			        			</tr>
			        			<tr>
			        				<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			      				</tr>
			      				<tr>
			        				<td>
			        					<table width="100%" border="0" cellspacing="0" cellpadding="2">
											<tr>
			            						<td><?=tep_draw_separator()?></td>
			          						</tr>
			          						<tr>
			                					<td class="pageHeading" valign="top"><b><?=TABLE_HEADING_SUPPLIERS_INFO?></b></td>
			              					</tr>
			              					<tr>
			            						<td valign="top">
			            							<table width="100%" border="0" cellspacing="0" cellpadding="2">
			            								<tr>
			            									<td valign="top">
			            										<table width="100%" border="0" cellspacing="0" cellpadding="2">
						              								<tr>
						              									<td width="15%" class="main" valign="top" nowrap><b><?=ENTRY_SUPPLIER?></b></td>
			                											<td class="main">
			                											<?
			                												echo tep_output_string_protected($supplier_order->supplier['name']);
			                												echo '<br>' . tep_address_format($supplier_order->supplier['format_id'], $supplier_order->supplier, 1, '', '<br>', '', false);
			                											?>
			                											</td>
						              								</tr>
						              							</table>
						              						</td>
						              					</tr>
						              					<tr>
			            									<td valign="top">
			            										<table width="100%" border="0" cellspacing="0" cellpadding="2">
						              								<tr>
						                								<td width="15%" class="main" valign="top"><b><?=ENTRY_SUPPLIER_SIGNUP_DATE?></b></td>
						                								<td class="main">
						                								<?	echo tep_date_short($supplier_order->supplier["date_account_created"]);?>
						                								</td>
						              								</tr>
						              								<tr>
						                								<td class="main" valign="top"><b><?=ENTRY_SUPPLIER_GENDER?></b></td>
						                								<td class="main"><?=($supplier_order->supplier["gender"]=='m' ? 'Male' : ($supplier_order->supplier["gender"]=='f' ? 'Female' : '&nbsp;'))?></td>
						              								</tr>
						              								<tr>
						                								<td class="main" valign="top"><b><?=ENTRY_SUPPLIER_DOB?></b></td>
						                								<td class="main">
						                								<?	echo tep_date_short($supplier_order->supplier["dob"]);	?>
						                								</td>
						              								</tr>
						              								<tr>
						                								<td class="main"><b><?=ENTRY_TELEPHONE_NUMBER?></b></td>
						                								<td class="main"><?=$supplier_order->supplier['telephone']?></td>
						              								</tr>
						              								<tr>
														                <td class="main"><b><?=ENTRY_FAX_NUMBER?></b></td>
														                <td class="main"><?=$supplier_order->supplier['fax']?></td>
						              								</tr>
						              								<tr>
										                				<td class="main"><b><?=ENTRY_EMAIL_ADDRESS?></b></td>
										                				<td class="main"><? echo $supplier_order->supplier['email_address']; ?></td>
						              								</tr>
						              								<tr>
										                				<td class="main"><b><?=ENTRY_QQ_NUMBER?></b></td>
										                				<td class="main"><? echo (($supplier_order->supplier['qq']) ? $supplier_order->supplier['qq'] : TEXT_NOT_AVAILABLE); ?></td>
						              								</tr>
						              								<tr>
										                				<td class="main"><b><?=ENTRY_MSN_ADDRESS?></b></td>
										                				<td class="main"><? echo (($supplier_order->supplier['msn']) ? $supplier_order->supplier['msn'] : TEXT_NOT_AVAILABLE); ?></td>
						              								</tr>
						            							</table>
						            						</td>
						            					</tr>
						            				</table>
						            			</td>
						            		</tr>
						            		<tr>
						        				<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
						      				</tr>
						      				<tr>
						            			<td><?=tep_draw_separator()?></td>
						          			</tr>
						          			<tr>
						        				<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
						      				</tr>
										  	<tr>
										  		<td>
<?
	$use_second_list = false;
	$product_submission = array();
	
	// check whether this order need to show purchase status or not
	$show_purchase_status = false;
	for ($i=0; $i < count($supplier_order->products); $i++) {
		if (is_array($supplier_order->products[$i]['confirm_list']) && count($supplier_order->products[$i]['confirm_list']) > 0) {
			$use_second_list = true;
			break;
		}
	}
	
	$list_ref = $use_second_list ? 'confirm_list' : 'first_list';
	
	for ($i=0; $i < count($supplier_order->products); $i++) {
		$product_submission[$supplier_order->products[$i]["id"]] = $supplier_order->products[$i];
		if (tep_not_null($supplier_order->products[$i][$list_ref]['products_purchase_status'])) {
			$show_purchase_status = true;
		}
	}
	
	$total_colspan = 10;
	if ($show_purchase_status) {
		$total_colspan ++;
	}
?>
													<table width="100%" border="0" cellspacing="1" cellpadding="2">
												  		<tr>
														  	<td class="ordersBoxHeading" nowrap><?=TABLE_HEADING_PRODUCT_NAME?></td>
<?	if ($show_purchase_status) {
		echo '  											<td width="5%" align="center" class="ordersBoxHeading">'.TABLE_HEADING_PRODUCT_DEMAND_STATUS.'</td>';
	}
	
	echo '													<td width="8%" align="center" class="ordersBoxHeading" nowrap>'.TABLE_HEADING_PRODUCT_ADMIN_COMMENT.'</td>';
?>
															<td width="12%" class="ordersBoxHeading"><?=TABLE_HEADING_PRODUCT_SUPPLIER_COMMENT?></td>
															<td width="6%" align="center" class="ordersBoxHeading"><?=TABLE_HEADING_MIN_QTY?></td>
															<td width="6%" align="center" class="ordersBoxHeading"><?=TABLE_HEADING_MAX_QTY?></td>
															<td width="6%" align="center" class="ordersBoxHeading"><?=TABLE_HEADING_UNIT_PRICE?></td>
															<!--td width="6%" align="center" class="ordersBoxHeading"><?=TABLE_HEADING_OVER_LIMIT_MAX_QTY?></td>
															<td width="6%" align="center" class="ordersBoxHeading"><?=TABLE_HEADING_UNIT_OVER_LIMIT_PRICE?></td-->
														  	<td width="5%" align="center" class="ordersBoxHeading"><?=TABLE_HEADING_SUGGESTED_SELLING_QUANTITY?></td>
														  	<td width="5%" align="center" class="ordersBoxHeading"><?=TABLE_HEADING_SELLING_QUANTITY?></td>
														  	<td width="5%" align="center" class="ordersBoxHeading"><?=TABLE_HEADING_PRODUCT_PREVIOUS_RECEIVED?></td>
													  		<td width="8%" align="right" class="ordersBoxHeading"><?=TABLE_HEADING_PRODUCT_PAYABLE_AMOUNT?></td>
												  		</tr>
<?
	$payable_total = 0;
	
	$i=0;
	foreach ($product_submission as $this_pid => $this_order_res) {
		if (is_array($this_order_res['confirm_list']) && count($this_order_res['confirm_list']) > 0) {
			$list_ref = 'confirm_list';
		} else {
			$list_ref = 'first_list';
		}
		
		$unique_row_reference = $this_pid;
		
		$row_style = ($i%2) ? 'invoiceListingEven' : 'invoiceListingOdd';
?>
														<tr class="<?=$row_style?>" id="<?=$unique_row_reference?>" onMouseOver="showOverEffect(this, 'invoiceListingRowOver')" onMouseOut="showOutEffect(this, '<?=$row_style?>')" onClick="showClicked(this, '<?=$row_style?>')">
															<td valign="top" class="ordersRecords">
																<?=$this_order_res[$list_ref]['products_display_name']; ?>
															</td>
<?
		$suggested_quantity = (int)$this_order_res['first_list']['products_quantity'];
		$selling_quantity = $this_order_res['confirm_list']['products_quantity'];
		$received_quantity = $this_order_res['confirm_list']['products_received_quantity'];	// Do not cast to int since NULL implie not yet restock
		
		$recv_qty_style = tep_not_null($received_quantity) && is_numeric($received_quantity) && $received_quantity < $selling_quantity ? 'class="redIndicator"' : '';
		$payable_total += (double)$this_order_res['payable_amount'];
		
		if ($show_purchase_status) {
			echo '											<td align="center" valign="top" class="ordersRecords">';
			$server_status_index = trim($this_order_res[$list_ref]['products_purchase_status']);
			if (isset($server_status_array[$server_status_index])) {
				echo tep_image(DIR_WS_IMAGES . $server_status_array[$server_status_index]['name'] . '.gif', $server_status_trans[$this_order_res[$list_ref]['products_purchase_status']]);
			} else { echo '&nbsp;'; }
			echo '											</td>';		
		}
		
		echo '												<td valign="top" class="ordersRecords">'.$this_order_res[$list_ref]['products_restock_comment'].'</td>';
		echo '												<td valign="top" class="ordersRecords">'.$this_order_res[$list_ref]['supplier_order_lists_products_comment'].'</td>';
		echo '												<td align="center" valign="top" class="ordersRecords">'.$this_order_res[$list_ref]['min_quantity'].'</td>';
		echo '												<td align="center" valign="top" class="ordersRecords">'.$this_order_res[$list_ref]['first_max_quantity'].'</td>';
		echo '												<td align="right" valign="top" class="ordersRecords">'.$this_order_res[$list_ref]['first_max_unit_price'].'</td>';
		//echo '												<td align="center" valign="top" class="ordersRecords">'.$this_order_res[$list_ref]['second_max_quantity'].'</td>';
		//echo '												<td align="right" valign="top" class="ordersRecords">'.$this_order_res[$list_ref]['second_max_unit_price'].'</td>';
?>
															<td align="center" valign="top" class="ordersRecords"><?=$suggested_quantity?></td>
															<td align="center" valign="top" class="ordersRecords"><?=$selling_quantity?></td>
															<td align="center" valign="top" class="ordersRecords"><span "<?=$recv_qty_style?>"><?=$received_quantity?></span></td>
<?		echo '												<td align="right" valign="top" class="ordersRecords" nowrap>'.$currencies->format($this_order_res['payable_amount'], true, $supplier_order->info['currency'], $supplier_order->info['currency_value']).'</td>';
		
		$i++;
	}
?>
														</tr>
														<tr>
															<td colspan="<?=($total_colspan-3)?>" class="smallText">
<?	if ($show_purchase_status && count($server_status_array)) { ?>
																<table border="0" cellspacing="0" cellpadding="0">
																	<tr>
																	<?
																	foreach ($server_status_array as $ss_name => $ss_res) {
																		echo '	<td>'.tep_image(DIR_WS_IMAGES . $ss_res['name'] . '.gif', $server_status_trans[$ss_name]).'</td>
																				<td class="smallText" NOWRAP>&nbsp;'.$server_status_trans[$ss_name].'&nbsp;&nbsp;</td>';
																	}
																	?>
																	</tr>
																</table>
<?	} ?>
															</td>
          													<td align="right" colspan="3" class="infoLabel">
															<?
																echo sprintf(TEXT_TOTAL_AMOUNT, $currencies->format($payable_total, true, $supplier_order->info['currency'], $supplier_order->info['currency_value']));
																
																$order_payments_array = payment::get_order_paid_history($oID, false);
																
																$outstanding_amount = $payable_total;
																for ($op_cnt=0; $op_cnt < count($order_payments_array); $op_cnt++) {
																	$outstanding_amount -= $order_payments_array[$op_cnt]['paid_amount'];
																	echo '<br>' . sprintf(TEXT_PAYMENT_RECORDS, $order_payments_array[$op_cnt]['payment_type']=='1' ? TEXT_PARTIAL_PAYMENT : TEXT_FULL_PAYMENT, $order_payments_array[$op_cnt]['payment_id'], $currencies->format($order_payments_array[$op_cnt]['paid_amount'], true, $order_payments_array[$op_cnt]['paid_currency'], $order_payments_array[$op_cnt]['paid_currency_value']));
																}
																
																if ($op_cnt > 0) {
																	echo '<br>' . sprintf(TEXT_BALANCE_AMOUNT, $currencies->format($outstanding_amount, true, $supplier_order->info['currency'], $supplier_order->info['currency_value']));
																}
															?>
															</td>
														</tr>
												  	</table>
										  		</td>
										  	</tr>
			          					</table>
			          				</td>
			          			</tr>
			          			<tr>
			        				<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			      				</tr>
			      				<tr>
			            			<td colspan="3"><?=tep_draw_separator()?></td>
			          			</tr>
			          			<tr>
			        				<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			      				</tr>
							  	<tr>
							  		<td class="main">
			        					<table border="1" cellspacing="0" cellpadding="5">
			          						<tr>
			            						<td class="titleField" align="center"><b><?=TABLE_HEADING_DATE_ADDED?></b></td>
			            						<td class="titleField" align="center"><b><?=TABLE_HEADING_STATUS?></b></td>
			            						<td class="titleField" align="center"><b><?=TABLE_HEADING_COMMENTS?></b></td>
			          						</tr>
<?
	$order_list_history_select_sql = "	SELECT supplier_order_lists_status, date_added, comments 
										FROM " . TABLE_SUPPLIER_ORDER_LISTS_HISTORY . " 
										WHERE supplier_order_lists_id = '" . tep_db_input($oID) . "' AND supplier_notified = 1 
										ORDER BY date_added";
	$order_list_history_result_sql = tep_db_query($order_list_history_select_sql);
	while ($order_list_history_row = tep_db_fetch_array($order_list_history_result_sql)) {
		$formatted_date_comment_added = tep_datetime_short($order_list_history_row["date_added"]);
?>
											<tr <?=$comment_row_style?>>
			            						<td class="smallText" align="center"><?=(tep_not_null($formatted_date_comment_added) ? $formatted_date_comment_added : '--')?></td>
			            						<td class="smallText" align="center"><?=$status_options[$order_list_history_row['supplier_order_lists_status']]?></td>
			            						<td class="smallText"><?=nl2br(str_replace("\t", "&nbsp;&nbsp;&nbsp;&nbsp;", $order_list_history_row['comments']))?>&nbsp;</td>
			          						</tr>
<?	} ?>
										</table>
			          				</td>
							  	</tr>
							  	<tr>
							  		<td align="right">
							  		<?
							  			echo tep_draw_form('back_form', FILENAME_SUPPLIER_HISTORY, tep_get_all_get_params(array('oID', 'action')), 'GET', '');
							  			echo tep_submit_button(BUTTON_BACK, ALT_BUTTON_BACK, '', 'inputButton');
							  			echo '</form>';
							  		?>
				 					</td>
				 				</tr>
			        		</table>
<?
} else {
	$total_history = 0;
	for ($status_count=0; $status_count < count($show_order_status); $status_count++) {
		$order_status_id = $show_order_status[$status_count];
		$status = $status_options[$order_status_id];
		
		$status_total_payable_amount = 0;
?>
							<table border="0" width="100%" cellspacing="0" cellpadding="2" style="border-collapse: collapse;">
  								<tr>
    								<td colspan="8">
    									<span class="pageHeading"><?=$status?></span>
    								</td>
			  					</tr>
								<tr>
									<td width="8%" class="ordersBoxHeading"><?=TABLE_HEADING_ORDER_ID?></td>
									<td width="12%" class="ordersBoxHeading"><?=TABLE_HEADING_ORDER_LIST_NAME?></td>
								    <td width="13%" class="ordersBoxHeading"><?=TABLE_HEADING_FIRST_SUBMISSION_TIME?></td>
								    <td align="center" width="15%" class="ordersBoxHeading"><?=TABLE_HEADING_TOTAL_SELLING_QUANTITY?></td>
								    <td align="center" width="15%" class="ordersBoxHeading"><?=TABLE_HEADING_TOTAL_RECEIVED_QUANTITY?></td>
								    <td align="right" class="ordersBoxHeading"><?=TABLE_HEADING_TOTAL_PAYABLE?></td>
								    <td align="center" width="6%" class="ordersBoxHeading"><?=TABLE_HEADING_ACTION?></td>
								</tr>
<?
		$orders_select_sql = $orders_select_str . $orders_where_str . ' and sol.supplier_order_lists_status = ' . $show_order_status[$status_count] . $orders_group_by_str . $orders_order_by_str;
		
		$orders_split_object = new splitPageResults($HTTP_GET_VARS['page'.$order_status_id], MAX_DISPLAY_SEARCH_RESULTS, $orders_select_sql, $orders_select_sql_numrows, true);
		$orders_result_sql = tep_db_query($orders_select_sql);
		
		$row_count = 0;
		$list_colspan_count = 8;
		
		while ($row = tep_db_fetch_array($orders_result_sql)) {
			$ordernummer = $row['supplier_order_lists_id'];
			$orderdate = tep_datetime_short($row['date_submitted']);
			
			$payable_amount = $total_selling_qty = $total_received_qty = 0;
			
			// Prevent the bug where same server been submitted twice
			$distinct_product_select_sql = "SELECT DISTINCT solp.products_id 
											FROM " . TABLE_SUPPLIER_ORDER_LISTS_PRODUCTS . " AS solp 
											WHERE solp.supplier_order_lists_id = '" . tep_db_input($ordernummer) . "' 
												AND solp.supplier_order_lists_type=2";
			$distinct_product_result_sql = tep_db_query($distinct_product_select_sql);
			
			while ($distinct_product_row = tep_db_fetch_array($distinct_product_result_sql)) {
				$payable_amount_select_sql = "	SELECT (IF(solp.products_received_quantity > solp.first_max_quantity, IF(solp.products_received_quantity > solp.first_max_quantity+solp.second_max_quantity, solp.first_max_quantity*solp.first_max_unit_price + solp.second_max_quantity*solp.second_max_unit_price, solp.first_max_quantity*solp.first_max_unit_price + (solp.products_received_quantity-solp.first_max_quantity)*solp.second_max_unit_price), solp.products_received_quantity*solp.first_max_unit_price)) AS total_amount,
													products_received_quantity AS total_received,
													products_quantity AS total_selling 
												FROM " . TABLE_SUPPLIER_ORDER_LISTS_PRODUCTS . " AS solp 
												WHERE solp.supplier_order_lists_id = '" . tep_db_input($ordernummer) . "' 
													AND solp.supplier_order_lists_type=2 
													AND solp.products_id = '" . tep_db_input($distinct_product_row['products_id']) . "' 
												GROUP BY solp.products_id 
												LIMIT 1	";
				$payable_amount_result_sql = tep_db_query($payable_amount_select_sql);
				
				while ($payable_amount_row = tep_db_fetch_array($payable_amount_result_sql)) {
					$payable_amount += $payable_amount_row["total_amount"];
					$total_selling_qty += (int)$payable_amount_row["total_selling"];
					$total_received_qty += (int)$payable_amount_row["total_received"];
				}
			}
			
			$status_total_payable_amount += $payable_amount;
			
			$row_style = ($row_count%2) ? 'ordersListingEven' : 'ordersListingOdd' ;
?>
								<tr id="<?=$status.'_main_'.$row_count?>" class="<?=$row_style?>" onmouseover="showOverEffect(this, 'ordersListingRowOver', '<?=$status.'_sub_'.$row_count?>##<?=$status.'_sub2_'.$row_count?>')" onmouseout="showOutEffect(this, '<?=$row_style?>', '<?=$status.'_sub_'.$row_count?>##<?=$status.'_sub2_'.$row_count?>')" onclick="showClicked(this, '<?=$row_style?>', '<?=$status.'_sub_'.$row_count?>##<?=$status.'_sub2_'.$row_count?>')">
									<td class="ordersRecords" nowrap><?=$ordernummer?></td>
									<td class="ordersRecords"><?=$row['products_purchases_lists_name']?></td>
							      	<td class="ordersRecords"><?=$orderdate?></td>
							      	<td align="center" class="ordersRecords"><?=$total_selling_qty?></td>
							      	<td align="center" class="ordersRecords"><?=$total_received_qty?></td>
			        				<td align="right" class="ordersRecords" align="right"><?=$currencies->format($payable_amount, true, $row['currency'], $row['currency_value'])?></td>
			        				<td class="ordersRecords" align="center">
			  						<?
			  						if ($order_status_id == 1) {	// No view details in Pending status
			  							echo tep_draw_separator('pixel_trans.gif', '16', '1');
			  						} else {
			  							echo '<a href="' . tep_href_link(FILENAME_SUPPLIER_HISTORY, 'oID='.$ordernummer.'&action=view', 'NONSSL') . '">'.tep_image(DIR_WS_ICONS."view.gif", LINK_VIEW_ORDER, "", "", 'align="top"').'</a>&nbsp;';
			  						}
			  						
			  						if ($order_status_id == 5 || $order_status_id == 1) {
			  							echo '<a href="' . tep_href_link(FILENAME_SUPPLIER_LIST, 'oID='.$ordernummer.'&action=edit', 'NONSSL') . '">'.tep_image(DIR_WS_ICONS."edit.gif", LINK_EDIT_ORDER, "", "", 'align="top"').'</a>';
			  						} else {
			  							echo tep_draw_separator('pixel_trans.gif', '16', '1');
			  						}
			  						?>
			  						</td>
			        			</tr>
<?			$row_count++;
		}
		
		if ($order_status_id != 4 && $order_status_id != 5) {
			echo '				<tr>
									<td align="right" class="ordersRecords" colspan="6"><b>'.$currencies->format($status_total_payable_amount).'</b></td>
									<td class="ordersRecords" colspan="'.($list_colspan_count-6).'">&nbsp;</td>
								</tr>';
		}
		
		$total_history += $row_count;
?>
								<tr>
			            			<td colspan="<?=$list_colspan_count?>">
			            				<table border="0" width="100%" cellspacing="0" cellpadding="2">
			              					<tr>
			                					<td class="smallText" valign="top"><?=$orders_split_object->display_count($orders_select_sql_numrows, MAX_DISPLAY_SEARCH_RESULTS, $HTTP_GET_VARS['page'.$order_status_id], TEXT_DISPLAY_NUMBER_OF_ORDERS)?></td>
			                					<td class="smallText" align="right"><?=$orders_split_object->display_links($orders_select_sql_numrows, MAX_DISPLAY_SEARCH_RESULTS, MAX_DISPLAY_PAGE_LINKS, $HTTP_GET_VARS['page'.$order_status_id], tep_get_all_get_params(array('page'.$order_status_id, 'cont'))."cont=1", 'page'.$order_status_id)?></td>
			              					</tr>
			            				</table>
			            			</td>
          						</tr>
							</table>
<?
	}
	
	if (!$total_history) {
		echo '				<table border="0" width="100%" cellspacing="0" cellpadding="2" class="formArea">
          						<tr>
            						<td class="formAreaTitle" valign="top">'.TEXT_NO_ORDER_HISTORY.'</td>
	          					</tr>
	        				</table>';
	}
}
?>
						</td>
					</tr>
	          		<script language="javascript">
					<!--
						function hideShow(groupName, styleClass) {
							var row_count = eval(groupName+"_count");
							for (var i=0; i<row_count; i++) {
								document.getElementById(groupName+"_"+i).className = styleClass;
							}
							
							if (styleClass == "show") {
								document.getElementById(groupName+'_nav').innerHTML = "<a href=\"javascript:;\" onClick=\"hideShow('"+groupName+"', 'hide')\">Hide Ordered Details</a>";
								SetCookie(groupName, '1');
							} else {
								document.getElementById(groupName+'_nav').innerHTML = "<a href=\"javascript:;\" onClick=\"hideShow('"+groupName+"', 'show')\">Show Ordered Details</a>";
								SetCookie(groupName, '0');
							}
						}
						
						function showOverEffect(object, class_name, extra_row) {
							rowOverEffect(object, class_name);
							if (extra_row != null) {
								var rowObjArray = extra_row.split('##');
		  						for (var i = 0; i < rowObjArray.length; i++) {
		  							if (typeof (document.getElementById(rowObjArray[i])) != 'undefined' && document.getElementById(rowObjArray[i]) != null) {
		  								rowOverEffect(document.getElementById(rowObjArray[i]), class_name);
		  							}
		  						}
		  					}
						}
						
						function showOutEffect(object, class_name, extra_row) {
							rowOutEffect(object, class_name);
							if (extra_row != null) {
								var rowObjArray = extra_row.split('##');
						  		for (var i = 0; i < rowObjArray.length; i++) {
						  			if (typeof (document.getElementById(rowObjArray[i])) != 'undefined' && document.getElementById(rowObjArray[i]) != null) {
						  				rowOutEffect(document.getElementById(rowObjArray[i]), class_name);
						  			}
						  		}
						  	}
						}
						
						function showClicked(object, class_name, extra_row) {
							rowClicked(object, class_name);
							if (extra_row != null) {
								var rowObjArray = extra_row.split('##');
						  		for (var i = 0; i < rowObjArray.length; i++) {
						  			if (typeof (document.getElementById(rowObjArray[i])) != 'undefined' && document.getElementById(rowObjArray[i]) != null) {
						  				rowClicked(document.getElementById(rowObjArray[i]), class_name);
						  			}
		  						}
		  					}
						}
					//-->
					</script>
				</table>
<!-- body_text_eof //-->
  			</td>
  		</tr>
	</table>
<!-- body_eof //-->
<br>
<!-- footer //-->
<? require(DIR_WS_INCLUDES . 'footer.php'); ?>
<!-- footer_eof //-->
<? require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>
</body>
</html>