<?
require('includes/application_top.php');

require(DIR_WS_CLASSES . 'order.php');

$action = (isset($_REQUEST['action']) ? $_REQUEST['action'] : '');
$subaction = (isset($_REQUEST['subaction']) ? $_REQUEST['subaction'] : '');
$c_mode = (isset($_REQUEST['c_mode']) ? $_REQUEST['c_mode'] : '');
$list_id = $_REQUEST['lid'];

$entry_allowed = false;

$start_time = $list_resources_array[$list_id]['start_time'];
$end_time = $list_resources_array[$list_id]['end_time'];

switch($action) {
	case "edit":
		$editable_list = true;
		
		$list_type = 0;
		$confirmation_list_global_info = array();
		$entry_allowed = false;
		$enable_counter = false;
		$potential_error_message = '';
		
		$oID = tep_db_prepare_input($_REQUEST['oID']);
		
		$order_info_select_sql = "	SELECT supplier_order_lists_status, products_purchases_lists_id
									FROM " . TABLE_SUPPLIER_ORDER_LISTS . " 
									WHERE suppliers_id = '" . (int)$supplier_id . "' AND supplier_order_lists_id = '" . tep_db_input($oID) . "' ";
		$order_info_result_sql = tep_db_query($order_info_select_sql);
		if ($order_info_row = tep_db_fetch_array($order_info_result_sql)) {
			$list_id = $order_info_row["products_purchases_lists_id"];
			$current_list_status = $list_resources_array[$list_id]['status'];
			
			if ($order_info_row["supplier_order_lists_status"] == 5) {	// This is draft list, not yet submit confirmation list
				$confirmation_list_global_info['order_list_id'] = $oID;
				$confirmation_list_global_info["submit_mode"] = 'insert';
				$list_type = 2;
				
				$entry_allowed = true;
				
				$potential_error_message = sprintf(WARNING_SUBMIT_CONFIRMATION_LIST_INVALID_TIME, $oID);
			} else if ($order_info_row["supplier_order_lists_status"] == 1) {	// Already in pending
				$confirmation_list_global_info['order_list_id'] = $oID;
				$confirmation_list_global_info["submit_mode"] = 'update';
				$list_type = 2;
				
				$entry_allowed = true;
				
				$potential_error_message = sprintf(WARNING_UPDATE_CONFIRMATION_LIST_INVALID_TIME, $oID);
			} else {
				$potential_error_message = sprintf(ERROR_EDIT_SUPPLIER_ORDER, $oID);
			}
			
			if ($current_list_status == "STATUS_OFF") {
				$entry_allowed = false;
				tep_db_query("UPDATE " . TABLE_SUPPLIER_LIST_TIME_SETTING . " SET current_status='STATUS_OFF' WHERE supplier_groups_id = '" . $supplier_groups_id . "' AND products_purchases_lists_id ='".$list_id."';");
			} else if ($current_list_status == "STATUS_ON") {
				$entry_allowed = true;
				tep_db_query("UPDATE " . TABLE_SUPPLIER_LIST_TIME_SETTING . " SET current_status='STATUS_ON' WHERE supplier_groups_id = '" . $supplier_groups_id . "'  AND products_purchases_lists_id ='".$list_id."';");
			} else if ($current_list_status == "STATUS_AUTO") {
				if (tep_time_check($list_resources_array[$list_id]['second_start_time'], $list_resources_array[$list_id]['second_end_time'])) { // Only allow confirmation list if it is in the confirmation list time range
					$enable_counter = true;
					
					$start_time = $list_resources_array[$list_id]['second_start_timestamp'];
					$end_time = $list_resources_array[$list_id]['second_end_timestamp'];
				} else {
					$entry_allowed = false;
					$list_type = 0;
				}
			}
		}
		break;
	default:
		if ($list_resources_array[$list_id]['entry_allowed'] == '1') {
			$entry_allowed = true;
			$editable_list = true;
			
			$list_type = $list_resources_array[$list_id]['list_type'];
			$current_list_status = $list_resources_array[$list_id]['status'];
			$enable_counter = $list_resources_array[$list_id]['enable_counter'];
			
			if ($current_list_status == 'STATUS_AUTO') {
				if ($list_resources_array[$list_id]['first_list_edit_anytime'] != 1 && isset($list_resources_array[$list_id]['first_list_edit_time'])) {
					if (!tep_time_check($list_resources_array[$list_id]['first_start_time'], $list_resources_array[$list_id]['first_end_time'], $list_resources_array[$list_id]['first_list_edit_time'][0].$list_resources_array[$list_id]['first_list_edit_time'][1])
						|| !tep_time_check($list_resources_array[$list_id]['first_edit_time'], $list_resources_array[$list_id]['second_end_time'])) {
						$editable_list = false;
					}
				}
			}
		}
		break;
}

if (!$entry_allowed) {
	if ($action == 'edit') {
		if (tep_not_null($potential_error_message))		$messageStack->add_session($potential_error_message, 'warning');
		tep_redirect(tep_href_link(FILENAME_SUPPLIER_HISTORY));
	} else {
		//$messageStack->redirect_message();
		tep_redirect(tep_href_link(FILENAME_DEFAULT));
	}
} else {
	$list_info_select_sql = "SELECT products_purchases_lists_name, products_purchases_lists_qty_round_up, products_purchases_lists_cat_id FROM " . TABLE_PRODUCTS_PURCHASES_LISTS . " WHERE products_purchases_lists_id = '" . (int)$list_id . "'";
	$list_info_result_sql = tep_db_query($list_info_select_sql);
	$list_info_row = tep_db_fetch_array($list_info_result_sql);
	$list_name = $list_info_row["products_purchases_lists_name"];
	$list_qty_round_up = (int)$list_info_row["products_purchases_lists_qty_round_up"] > 0 ? (int)$list_info_row["products_purchases_lists_qty_round_up"] : 1;
	
	$cat_cfg_array = tep_get_cfg_setting($list_info_row['products_purchases_lists_cat_id']);
	
	if (is_array($cat_cfg_array)) {
		foreach ($cat_cfg_array as $cfgKey => $cfgValue) {
			define($cfgKey, $cfgValue);
		}
	}
}

function filter_empty_val($var) {
	return  tep_not_null($var);
}

function tep_set_supplier_cart($cart_arr, $list_id) {
	// if the supplier is already logged in, add to database
	if (tep_session_is_registered('supplier_id')) {
		// delete all the cart for this bugger
		tep_clear_supplier_cart($list_id);
		
		if (sizeof($cart_arr) > 0) {
			foreach ($cart_arr as $list_res) {
				tep_db_perform(TABLE_BUYBACK_BASKET, $list_res);
			}
		}
	}
}

function tep_clear_supplier_cart($list_id) {
	global $supplier_id;
	
	$clear_cart_sql = "DELETE FROM " . TABLE_BUYBACK_BASKET . " WHERE customers_id ='" . (int)$supplier_id . "' AND buyback_basket_user_type='1' AND products_purchases_lists_id = '".$list_id."';";
	tep_db_query($clear_cart_sql);
}

function tep_get_supplier_cart($list_id) {
	global $supplier_id;
	
	$cart_arr = array();
	
	if (tep_session_is_registered('supplier_id')) {
		$supplier_cart_select_sql = "SELECT * FROM " . TABLE_BUYBACK_BASKET . " WHERE customers_id ='" . (int)$supplier_id . "' AND buyback_basket_user_type='1' AND products_purchases_lists_id = '".$list_id."';";
		$supplier_cart_result_sql = tep_db_query($supplier_cart_select_sql);
		
		while ($supplier_cart_row = tep_db_fetch_array($supplier_cart_result_sql)) {
			$cart_arr[(int)$supplier_cart_row['products_id']] = $supplier_cart_row;
		}
	}
	
	return $cart_arr;
}

function tep_get_product_path($id) {
	$result = tep_db_query("SELECT p.products_cat_path, pd.products_name from " . TABLE_PRODUCTS . " p, " . TABLE_PRODUCTS_DESCRIPTION . " pd where pd.products_id=p.products_id and p.products_id=$id;");
	
	if ($row=tep_db_fetch_array($result)) {
		return $row['products_cat_path']." > ".$row['products_name'];
	} else {
		return "";
	}
}

$currencies->set_decimal_places(DISPLAY_PRICE_DECIMAL);

$diff = $end_time - $today;
$actual_hrs = $hrs = (int)($diff/3600);
if ($hrs < 0)	$hrs = abs(abs($hrs) - 24);
$diff = $diff - ($actual_hrs * 3600);
$mins = (int)($diff/60);
$diff = $diff - ($mins * 60);
$secs = $diff;
$mins = abs($mins);
$secs = abs($secs);

if ($list_type == 1) {
	$text_list_title = TEXT_FIRST_LIST;
	$text_list_submit_mode = '';
} else {
	$text_list_title = TEXT_CONFIRMATION_LIST;
	if (tep_not_null($confirmation_list_global_info["submit_mode"])) {
		$text_list_submit_mode = ' - ' . ($confirmation_list_global_info["submit_mode"]=='insert' ? TEXT_SUBMIT_MODE : TEXT_UPDATE_MODE);
	}
}

$supplier_pricing_setting_array = tep_get_supplier_pricing_setting($supplier_groups_id, $list_id);
$total_active_supplier = tep_get_active_suppliers_count($supplier_groups_id);
$show_purchase_status = tep_show_purchase_status($supplier_groups_id);

if (strtolower($_SERVER['REQUEST_METHOD'])=='post') {
	switch($subaction) {
		case 'update':
			if ($HTTP_POST_VARS['btn_csv_import']) {
				if ( tep_not_null($_FILES['csv_import']['tmp_name']) && ($_FILES['csv_import']['tmp_name'] != 'none') && is_uploaded_file($_FILES['csv_import']['tmp_name']) ) {
					if ($_FILES['csv_import']["size"] > 0) {
						if (!isset($_SESSION['order_lists_import_sql'])) {
							$messageStack->add_session(ERROR_IMPORT_NOT_SUCCESS, 'error');
							tep_redirect(tep_href_link(FILENAME_SUPPLIER_LIST));
						}
						
						$import_error = false;
						$filename = ($_FILES['csv_import']['tmp_name']);  
					    $handle = fopen($filename, 'r+');
					    
					    $must_have_field = array(TABLE_HEADING_PRODUCT_ID => 0, TABLE_CSV_HEADING_SELLING_QTY => 0, TABLE_CSV_HEADING_SUPPLIER_COMMENT => 0);
					    if (($data = fgetcsv($handle, 1024, ',', '"')) !== FALSE) {
					    	$header_exists_count = 0;
					    	for ($i=0; $i < count($data); $i++) {
					    		if (in_array(trim($data[$i]), array_keys($must_have_field))) {
					    			$must_have_field[trim($data[$i])] = $i;
					    			$header_exists_count++;
					    		}
					    	}
					    	
					    	if ($header_exists_count != count($must_have_field)) {
					    		$messageStack->add("Some required fields does not exists. Please ensure your imported csv file contains " . implode(", ", array_keys($must_have_field)) . ".", 'error');
					    		$import_error = true;
					    	}
					    }
					    
					    if (!$import_error) {
					    	$exist_product_id_array = array();
					    	$imported_products_array = array();
					    	$active_products_select_sql = $_SESSION['order_lists_import_sql'];
					    	
							$active_products_result_sql = tep_db_query($active_products_select_sql);
					  		while ($active_products_row = tep_db_fetch_array($active_products_result_sql)) {
					  			$exist_product_id_array[$active_products_row["prd_id"]] = $active_products_row;
							}
							
					    	while (($data = fgetcsv($handle, 1024, ',', '"')) !== FALSE) {
					    		if (trim($data[0]) == '') {	// Assume this row is useless
				    				continue;
				    			}
				    			
					    		$product_id_str = $data[$must_have_field[TABLE_CSV_HEADING_PRODUCT_ID]];
								$selling_qty_str = $data[$must_have_field[TABLE_CSV_HEADING_SELLING_QTY]];
								$supplier_comment_str = $data[$must_have_field[TABLE_CSV_HEADING_SUPPLIER_COMMENT]];
								
					    		if ((int)$product_id_str > 0) {
						    		$imported_products_array[(int)$product_id_str] = array(	'prd_id' => (int)$product_id_str,
											    											'selling_quantity' => (int)$selling_qty_str,
											    											'supplier_comment' => $supplier_comment_str
									    													);
					    		}
					    		
					    		if (!in_array((int)$product_id_str, array_keys($exist_product_id_array))) {					    			
					    			$messageStack->add(sprintf(ERROR_IMPORTED_PRODUCT_NOT_MATCH, $product_id_str), 'error');
					    			$import_error = true;
					    		} else {
					    			$product_name = tep_display_category_path($exist_product_id_array[(int)$product_id_str]['products_cat_path']." > ".$exist_product_id_array[(int)$product_id_str]['products_name'], $exist_product_id_array[(int)$product_id_str]['categories_id'], 'catalog');
					    			
					    			if (tep_not_null($selling_qty_str)) {
						    			if (!preg_match('/[1-9][0-9]*/is', $selling_qty_str) && $selling_qty_str != '0') {
							    			$messageStack->add(sprintf(ERROR_INVALID_SELLING_QUANTITY, $product_name), 'error');
							    			$import_error = true;
							    		}
							    		
						    			if ($exist_product_id_array[(int)$product_id_str]["supplier_pricing_server_full"] == 0) {
						    				if (is_numeric($selling_qty_str) && $selling_qty_str > 0) {
						    					if ($total_active_supplier > 0) {
						    						if (tep_not_null($exist_product_id_array[(int)$product_id_str]['supplier_pricing_max_quantity']) && is_numeric($exist_product_id_array[(int)$product_id_str]['supplier_pricing_max_quantity'])) {	// If overwrite maximum qty is set
														$group_max_qty = (int)$exist_product_id_array[(int)$product_id_str]['supplier_pricing_max_quantity'];
													} else {
							    						if ($supplier_pricing_setting_array[KEY_SPS_USE_MFC_SUPPLY] == '1') {
															$group_max_qty = (int)$supplier_pricing_setting_array[KEY_SPS_MFC_SUPPLY_MAX_QTY];
														} else {
															$group_max_qty = ceil(((int)$exist_product_id_array[(int)$product_id_str]['selling_qty'] * (double)$supplier_pricing_setting_array[KEY_SPS_QUANTITY_RATIO]) / 100);
														}
						    						}
						    						$offset_amount = tep_get_previously_restocked_qty((int)$product_id_str, $supplier_groups_id, $list_id);
													$group_max_qty -= (int)$offset_amount;
													
													$this_supplier_max_quantity = (int)$group_max_qty / $total_active_supplier;
													
													$this_supplier_max_quantity = tep_round_up_to($this_supplier_max_quantity, $list_qty_round_up);
												} else {
													$this_supplier_max_quantity = 0;	// no active supplier means no one can submit
												}
												
												/*
												if ($this_supplier_max_quantity > 0) {
													$this_supplier_over_limit_max_quantity = ((int)$supplier_pricing_setting_array["sps_max_limit"] - ((int)$exist_product_id_array[(int)$product_id_str]['inventory_qty'] + (int)$exist_product_id_array[(int)$product_id_str]['supplier_pricing_max_quantity'])) / $total_active_supplier;
													$this_supplier_over_limit_max_quantity = tep_round_up_to($this_supplier_over_limit_max_quantity, 100);
												} else {
													$this_supplier_over_limit_max_quantity = 0;	// no active supplier means no one can submit
												}
												*/
												$this_supplier_over_limit_max_quantity = 0;
												
												$total_max_qty = $this_supplier_max_quantity + $this_supplier_over_limit_max_quantity;
												
												if ((int)$this_supplier_max_quantity > 0 && (int)$supplier_pricing_setting_array["sps_min_quantity"] <= (int)$this_supplier_max_quantity) {
													if ($imported_products_array[(int)$product_id_str]["selling_quantity"] < (int)$supplier_pricing_setting_array["sps_min_quantity"]) {
							    						$imported_products_array[(int)$product_id_str]["selling_quantity"] = (int)$supplier_pricing_setting_array["sps_min_quantity"];
							    						$messageStack->add(sprintf(WARNING_AUTO_RESET_MIN, $product_name, (int)$supplier_pricing_setting_array["sps_min_quantity"]), 'warning');
							    					} else if ($imported_products_array[(int)$product_id_str]["selling_quantity"] > (int)$total_max_qty) {
							    						$imported_products_array[(int)$product_id_str]["selling_quantity"] = (int)$total_max_qty;
							    						$messageStack->add(sprintf(WARNING_AUTO_RESET_MAX, $product_name, (int)$total_max_qty), 'warning');
							    					}
												} else {
							    					$messageStack->add(sprintf(WARNING_SERVER_FULL, $product_name) , 'warning');
							    					$import_error = true;
							    				}
						    				}
						    			} else {
						    				if ((int)$selling_qty_str > 0) {
						    					$messageStack->add(sprintf(WARNING_SERVER_FULL, $product_name) , 'warning');
							    				$import_error = true;
						    				}
							    		}
							    	}
						    	}
					    	}
					    	
					    	fclose($handle);
					    } else {
					    	fclose($handle);
					    }
					} else {
						$messageStack->add_session(ERROR_NO_UPLOAD_FILE, 'error');
						tep_redirect(tep_href_link(FILENAME_SUPPLIER_LIST, tep_get_all_get_params(array('subaction'))));
					}
				} else {
					$messageStack->add_session(WARNING_NO_FILE_UPLOADED, 'warning');
					tep_redirect(tep_href_link(FILENAME_SUPPLIER_LIST, tep_get_all_get_params(array('subaction'))));
				}
			} else if ($HTTP_POST_VARS['btn_csv_export']) {
				$export_csv_array = tep_array_unserialize($HTTP_POST_VARS["serialized_export_csv_array"]);
				$export_csv_data = '';
				
				if (count($export_csv_array)) {
					foreach ($export_csv_array as $pid => $res) {
						$tmp_cvs_data_array = array();
						for ($i=0; $i < count($res); $i++) {
							$tmp_cvs_data_array[] = '"' . str_replace(array('"', '<br>', '<BR>'), array('""', ''), $res[$i]) . '"';
						}
						$export_csv_data .= implode(',', $tmp_cvs_data_array) . "\n";
					}
				}
				
				if (tep_not_null($export_csv_data)) {
					$filename = 'order_list_'.date('YmdHis').'.csv';
					$mime_type = 'text/x-csv';
					// Download
			        header('Content-Type: ' . $mime_type);
			        header('Expires: ' . gmdate('D, d M Y H:i:s') . ' GMT');
			        // IE need specific headers
			        if (PMA_USR_BROWSER_AGENT == 'IE') {
			            header('Content-Disposition: inline; filename="' . $filename . '"');
			            header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
			            header('Pragma: public');
			        } else {
			            header('Content-Disposition: attachment; filename="' . $filename . '"');
			            header('Pragma: no-cache');
			        }
					echo $export_csv_data;
					exit();
				} else {
					$messageStack->add_session(WARNING_NOTHING_TO_EXPORT, 'warning');
					tep_redirect(tep_href_link(FILENAME_SUPPLIER_LIST, tep_get_all_get_params(array('subaction'))));
				}
			} else {
				if ($list_type == 1 && !$editable_list) {
					$messageStack->add_session(ERROR_NOT_EDITABLE_FIRST_LIST, 'error');
					tep_redirect(tep_href_link(FILENAME_SUPPLIER_LIST, tep_get_all_get_params(array('subaction'))));
				}
				
				$sup_order_cart = array();
				$products_info_array = array();
				if (is_array($_POST["cart_quantity"]) && count($_POST["cart_quantity"])) {
					$cart_quantity = array_filter($_POST["cart_quantity"], "filter_empty_val");
				} else {
					$cart_quantity = array();
				}
				
				$cart_comment = $_POST['cart_comment'];
				
				if (count($cart_quantity) > 0) {
					$products_list_select_sql = "	SELECT p2c.categories_id, p.products_id as prd_id, p.products_quantity as inventory_qty, p.products_cat_path, pd.products_name, sp.supplier_pricing_max_quantity, sp.supplier_pricing_unit_price, sp.supplier_pricing_disabled, sp.supplier_pricing_server_full, sp.supplier_pricing_product_status, pp.products_purchases_selling_quantity AS selling_qty, rci.restock_character 
													FROM " . TABLE_PRODUCTS_TO_CATEGORIES . " AS p2c 
													INNER JOIN " . TABLE_PRODUCTS . " AS p 
														ON (p2c.products_id=p.products_id AND p.products_bundle='' AND p.products_bundle_dynamic='' AND p.custom_products_type_id=0)
													INNER JOIN " . TABLE_PRODUCTS_DESCRIPTION . " AS pd 
														ON p2c.products_id=pd.products_id 
													INNER JOIN " . TABLE_SUPPLIER_PRICING . " as sp 
														ON (p2c.products_id=sp.products_id AND sp.supplier_groups_id='" . $supplier_groups_id . "') 
													INNER JOIN " . TABLE_PRODUCTS_PURCHASES . " as pp 
														ON (sp.products_purchases_lists_id=pp.products_purchases_lists_id AND pp.products_purchases_disabled=0 AND p2c.products_id=pp.products_id) 
													LEFT JOIN " . TABLE_RESTOCK_CHARACTER_INFO . " AS rci 
														ON (p2c.products_id=rci.products_id AND rci.restock_character_sets_id='" . tep_db_input($supplier_pricing_setting_array[KEY_SPS_RSTK_CHAR_SET]) . "') 
													WHERE sp.products_purchases_lists_id = '" . $list_id . "' AND p2c.products_id IN ('" . implode("', '", array_keys($cart_quantity)) . "') AND p2c.products_is_link=0 AND pd.language_id = '" . $languages_id . "' 
													ORDER BY p.products_cat_path, pd.products_name, p.products_sort_order";
					
					$products_list_result_sql = tep_db_query($products_list_select_sql);
					while($products_list_row = tep_db_fetch_array($products_list_result_sql)) {
						$products_info_array[$products_list_row["prd_id"]] = $products_list_row;
						$products_info_array[$products_list_row["prd_id"]]["min_quantity"] = (int)$supplier_pricing_setting_array["sps_min_quantity"];	// For future if different product can has different min qty
					}
					
					foreach ($cart_quantity as $pid => $qty) {
						if (!isset($products_info_array[$pid]) || ($products_info_array[$pid]["supplier_pricing_disabled"] == '1' || $products_info_array[$pid]["supplier_pricing_server_full"] == '1') ) {
							continue;
						}
						
						$qty = (int)$qty;
						$min_qty = (int)$products_info_array[$pid]["min_quantity"];
						
						if ($total_active_supplier > 0) {
							if (tep_not_null($products_info_array[$pid]['supplier_pricing_max_quantity']) && is_numeric($products_info_array[$pid]['supplier_pricing_max_quantity'])) {	// If overwrite maximum qty is set
								$group_max_qty = (int)$products_info_array[$pid]['supplier_pricing_max_quantity'];
							} else {
								if ($supplier_pricing_setting_array[KEY_SPS_USE_MFC_SUPPLY] == '1') {
									$group_max_qty = (int)$supplier_pricing_setting_array[KEY_SPS_MFC_SUPPLY_MAX_QTY];
								} else {
									$group_max_qty = ceil(((int)$products_info_array[$pid]['selling_qty'] * (double)$supplier_pricing_setting_array[KEY_SPS_QUANTITY_RATIO]) / 100);
								}
							}
							
							$offset_amount = tep_get_previously_restocked_qty($pid, $supplier_groups_id, $list_id);
							$group_max_qty -= (int)$offset_amount;
							
							$this_supplier_max_quantity = (int)$group_max_qty / $total_active_supplier;
							
							$this_supplier_max_quantity = tep_round_up_to($this_supplier_max_quantity, $list_qty_round_up);
						} else {
							$this_supplier_max_quantity = 0;	// no active supplier means no one can submit
						}
						/*
						if ($this_supplier_max_quantity > 0) {
							$this_supplier_over_limit_max_quantity = ((int)$supplier_pricing_setting_array["sps_max_limit"] - ((int)$products_info_array[$pid]['inventory_qty'] + (int)$products_info_array[$pid]['supplier_pricing_max_quantity'])) / $total_active_supplier;
							$this_supplier_over_limit_max_quantity = tep_round_up_to($this_supplier_over_limit_max_quantity, 100);
						} else {
							$this_supplier_over_limit_max_quantity = 0;	// no active supplier means no one can submit
						}
						*/
						
						$this_supplier_over_limit_max_quantity = 0;
						$total_max_qty = $this_supplier_max_quantity + $this_supplier_over_limit_max_quantity;
						
						if ($list_type == 2) {
							$supplier_order_lists_id = $confirmation_list_global_info['order_list_id'];
							$submit_mode = $confirmation_list_global_info["submit_mode"];
							$supplier_product_list = '';
							
							if (!isset($HTTP_POST_VARS["StockConfirmBtn_" . $pid])) {
								continue;
							} else {
								$allow_to_confirm_qty = false;
								if ($qty == 0) {	// For the case supplier has nothing to send
						    		$supplier_order_lists_id = $confirmation_list_global_info['order_list_id'];
						    		$first_list_product_select_sql = "	SELECT supplier_order_lists_products_id  
						    											FROM " . TABLE_SUPPLIER_ORDER_LISTS_PRODUCTS . "
						    											WHERE supplier_order_lists_id = '" . tep_db_input($supplier_order_lists_id) . "'
						    												AND supplier_order_lists_type = '1'
						    												AND products_id = '" . tep_db_input($pid) . "'";
							    	$first_list_product_result_sql = tep_db_query($first_list_product_select_sql);
									if (tep_db_num_rows($first_list_product_result_sql)) {
										$allow_to_confirm_qty = true;
										$amount = 0;
									}
						    	} else if ($qty > 0) {
									if ($qty >= $min_qty) {
										$allow_to_confirm_qty = true;
										$amount = tep_calculate_amount($qty, $this_supplier_max_quantity, $products_info_array[$pid]["supplier_pricing_unit_price"], $this_supplier_over_limit_max_quantity, $supplier_pricing_setting_array["sps_over_limit_discount"]);
									}
								}
								
								if ($allow_to_confirm_qty) {
									$confirm_qty_updated = false;
									$total_order_list_amount = 0;
									
									$product_display_name = tep_display_category_path($products_info_array[$pid]['products_cat_path'] . " > " . $products_info_array[$pid]['products_name'], $products_info_array[$pid]['categories_id'], 'catalog');
									
									/********************************************************************
									 	Lock the TABLE_SUPPLIER_ORDER_LISTS_PRODUCTS to prevent double 
									 	insertion of supplier order product.
									 	REMEMBER: Need to lock all the tables involved in this block.
									********************************************************************/
									tep_db_query("LOCK TABLES " . TABLE_SUPPLIER_ORDER_LISTS_PRODUCTS . " WRITE;");
									
							    	$check_order_list_product_exists_sql = "SELECT supplier_order_lists_products_id, products_received_quantity 
							    											FROM " . TABLE_SUPPLIER_ORDER_LISTS_PRODUCTS . "
							    											WHERE supplier_order_lists_id = '" . tep_db_input($supplier_order_lists_id) . "'
							    												AND supplier_order_lists_type = '" . tep_db_input($list_type) . "'
							    												AND products_id = '" . tep_db_input($pid) . "'";
							    	$check_order_list_product_exists_result_sql = tep_db_query($check_order_list_product_exists_sql);
									if ($check_order_list_product_exists_row = tep_db_fetch_array($check_order_list_product_exists_result_sql)) {
										; // No more updating once confirm the selling qty. Cannot use continue since we need to unlock table at below lines
									} else {	// not exists yet
										$order_list_product_data_array = array(	'supplier_order_lists_id' => $supplier_order_lists_id,
																				'supplier_order_lists_type' => $list_type,
													                            'products_id' => $pid,
													                            'products_name' => $products_info_array[$pid]['products_name'],
													                            'products_display_name' => $product_display_name,
													                            'products_purchase_status' => $products_info_array[$pid]['supplier_pricing_product_status'],
													                            'products_restock_comment' => $products_info_array[$pid]['restock_character'],
													                            'min_quantity' => $min_qty,
													                            'first_max_quantity' => $this_supplier_max_quantity,
																				'first_max_unit_price' => $products_info_array[$pid]["supplier_pricing_unit_price"],
																				'second_max_quantity' => $this_supplier_over_limit_max_quantity,
																				'second_max_unit_price' => 0,
													                            'products_provision_amount' => $amount,
													                            'products_quantity' => $qty,
													                            'supplier_order_lists_products_comment' => tep_db_prepare_input($cart_comment[$pid])
													                           );
									    tep_db_perform(TABLE_SUPPLIER_ORDER_LISTS_PRODUCTS, $order_list_product_data_array);
									    
									    $confirm_qty_updated = true;
									}
								    
								    tep_db_query("UNLOCK TABLES;");
								    /********************************************************************
									 	End of locking the TABLE_SUPPLIER_ORDER_LISTS_PRODUCTS table.
									********************************************************************/
									
								    if ($confirm_qty_updated) {
									    $total_order_list_amount += (double)$amount;
									    
									    $cat_path = $product_display_name;
										$product_email_display_str = $cat_path . (tep_not_null($cat_path) ? ' > ' : '') . $products_info_array[$pid]['products_name'];
										$supplier_product_list .= $qty . ' x ' . $product_email_display_str . ' = ' . $currencies->format((double)$amount) . "\n";
										
										// Supplier Start Sending Items
										if ($submit_mode == 'insert') {
											$supplier_list_status_update_sql = "UPDATE " . TABLE_SUPPLIER_ORDER_LISTS . " 
																				SET supplier_order_lists_status = '1', products_purchases_lists_name='".tep_db_input($list_name)."' 
																				WHERE supplier_order_lists_id = '" . $supplier_order_lists_id . "'";
											tep_db_query($supplier_list_status_update_sql);
											
											$order_list_history_data_array = array(	'supplier_order_lists_id' => $supplier_order_lists_id,
															                        'supplier_order_lists_status' => '1',
															                        'date_added' => 'now()',
															                        'supplier_notified' => '0',
															                        'comments' => ''
															                       );
											tep_db_perform(TABLE_SUPPLIER_ORDER_LISTS_HISTORY, $order_list_history_data_array);
											
											
											/*
											$supplier_info_select_sql = "	SELECT supplier_firstname, supplier_lastname  
																			FROM " . TABLE_SUPPLIER . " 
																			WHERE supplier_id = '" . (int)$supplier_id . "'";
									      	$supplier_info_result_sql = tep_db_query($supplier_info_select_sql);
									      	$supplier_info_row = tep_db_fetch_array($supplier_info_result_sql);
									      	
									      	$order_list_info_select_sql = "SELECT products_purchases_lists_name FROM " . TABLE_SUPPLIER_ORDER_LISTS . " WHERE supplier_order_lists_id = '" . $supplier_order_lists_id . "'";
									      	$order_list_info_result_sql = tep_db_query($order_list_info_select_sql);
									      	$order_list_info_row = tep_db_fetch_array($order_list_info_result_sql);
									      	
											$email_content = 	'Dear Purchase Team,' . "\n\n" . 
																sprintf(EMAIL_ADMIN_TEXT_SUBMIT_CONFIRM_LIST, $supplier_info_row['supplier_firstname'] . ' ' . $supplier_info_row['supplier_lastname']) . 
																sprintf(EMAIL_TEXT_ORDER_NUMBER, $supplier_order_lists_id) . "\n" .
																sprintf(EMAIL_TEXT_ORDER_TITLE, $list_name) . 
																"\n\n" . EMAIL_FOOTER;			
											
											// Send a copy to purchase team members
											$email_supplier_name_subject = $supplier_info_row['supplier_firstname'] . ' ' . $supplier_info_row['supplier_lastname'];
											$email_to_array = tep_parse_email_string(PURCHASE_TEAM_EMAIL);
							        		for ($i=0; $i < count($email_to_array); $i++) {
							        			tep_mail($email_to_array[$i]['name'], $email_to_array[$i]['email'], sprintf($list_type==1 ? EMAIL_TEXT_SUBJECT : EMAIL_ORDER_UPDATE_SUBJECT, $supplier_order_lists_id, $order_list_info_row['products_purchases_lists_name'], $email_supplier_name_subject), $email_content, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
							        		}
							        		*/
							        		
							        		// Send order update notification email
											tep_status_update_notification('S', $supplier_order_lists_id, 'system', 5, 1, 'A');
										}
										
										if ($submit_mode == 'insert') {
											$messageStack->add_session(SUCCESS_SUBMIT_CONFIRM_LIST, 'success');
										} else {
											$messageStack->add_session(SUCCESS_UPDATE_CONFIRM_LIST, 'success');
										}
									}
								} else {
									break;
								}
							}
						} else {
							if ($qty > 0) {
								if ($qty >= $min_qty) {
									$amount = tep_calculate_amount($qty, $this_supplier_max_quantity, $products_info_array[$pid]["supplier_pricing_unit_price"], $this_supplier_over_limit_max_quantity, $supplier_pricing_setting_array["sps_over_limit_discount"]);
									
									// put contents into the array
									$sup_order_cart[$pid] = array(	'customers_id' => (int)$supplier_id,
																	'products_id' => $pid,
																	'buyback_basket_quantity' => $qty,
																	'buyback_basket_amount' => $amount,
																	'buyback_basket_comment' => tep_db_prepare_input($cart_comment[$pid]),
																	'buyback_basket_user_type' => '1',
																	'buyback_basket_list_type' => $list_type,
																	'products_purchases_lists_id' => $list_id
																);
								}
							}
						}
					}
				}
				
				if ($list_type == 2) {
					tep_redirect(tep_href_link(FILENAME_SUPPLIER_LIST, tep_get_all_get_params(array('subaction'))));
				} else {
					tep_set_supplier_cart($sup_order_cart, $list_id);
					
					if ($_GET['subaction']=='update' && sizeof($sup_order_cart) > 0) {
						tep_redirect(tep_href_link(FILENAME_SUPPLIER_LIST, tep_get_all_get_params(array('subaction')).'subaction=final'));
					} else {
						$messageStack->add_session(WARNING_EMPTY_CART, 'warning');
						tep_redirect(tep_href_link(FILENAME_SUPPLIER_LIST, tep_get_all_get_params(array('subaction'))));
					}
				}
			}
			
			break;
		case 'final':
			$supplier_comment = tep_db_prepare_input($_POST['supplier_comment']);
			$supplier_product_list = '';
			$supplier_order = new order;
			
			if (count($supplier_order->products) > 0) {
				//if ($list_type == 1 || $list_type == 2) {
				if ($list_type == 1) {
					$submit_mode = 'insert';
					$supplier_order_lists_id = 0;
					
					if ($list_type == 1) {
						$sql_data_array = array('suppliers_id' => (int)$supplier_id,
												'products_purchases_lists_id' => $supplier_order->info['list_id'],
												'products_purchases_lists_name' => $supplier_order->info['list_name'],
						                        'suppliers_name' => $supplier_order->supplier['firstname'] . ' ' . $supplier_order->supplier['lastname'],
						                        'suppliers_street_address' => $supplier_order->supplier['street_address'],
						                        'suppliers_suburb' => $supplier_order->supplier['suburb'],
						                        'suppliers_city' => $supplier_order->supplier['city'],
						                        'suppliers_postcode' => $supplier_order->supplier['postcode'], 
						                        'suppliers_state' => $supplier_order->supplier['state'], 
						                        'suppliers_country' => $supplier_order->supplier['country']['title'], 
						                        'suppliers_telephone' => $supplier_order->supplier['telephone'], 
						                        'suppliers_email_address' => $supplier_order->supplier['email_address'],
						                        'supplier_order_lists_date' => 'now()', 
						                        'supplier_order_lists_last_modified' => 'now()',
						                        'supplier_order_lists_status' => $supplier_order->info['order_status'], 
						                        'currency' => $supplier_order->info['currency'], 
						                        'remote_addr' => $_SERVER['REMOTE_ADDR'],
						                        'currency_value' => $supplier_order->info['currency_value']);
						tep_db_perform(TABLE_SUPPLIER_ORDER_LISTS, $sql_data_array);
						$supplier_order_lists_id = tep_db_insert_id();
					} else {
						$supplier_order_lists_id = $confirmation_list_global_info['order_list_id'];
						$submit_mode = $confirmation_list_global_info["submit_mode"];
						
						if ($confirmation_list_global_info["submit_mode"] == 'insert') {
							$supplier_list_status_update_sql = "UPDATE " . TABLE_SUPPLIER_ORDER_LISTS . " SET supplier_order_lists_status = '1', products_purchases_lists_name='".tep_db_input($supplier_order->info['list_name'])."' WHERE supplier_order_lists_id = '" . $supplier_order_lists_id . "'";
							tep_db_query($supplier_list_status_update_sql);
						}
					}
					
					if ($supplier_order_lists_id > 0) {
						$order_lists_history_status = ($submit_mode == 'insert') ? $supplier_order->info['order_status'] : 0;
						$order_list_history_data_array = array(	'supplier_order_lists_id' => $supplier_order_lists_id,
										                        'supplier_order_lists_status' => $order_lists_history_status,
										                        'date_added' => 'now()',
										                        'supplier_notified' => '0',
										                        'comments' => $supplier_comment
										                       );
						tep_db_perform(TABLE_SUPPLIER_ORDER_LISTS_HISTORY, $order_list_history_data_array);
						
						$total_order_list_amount = 0;
						
						for ($i=0; $i < count($supplier_order->products); $i++) {
							if ($submit_mode == 'insert') {
								$order_list_product_data_array = array(	'supplier_order_lists_id' => $supplier_order_lists_id,
																		'supplier_order_lists_type' => $list_type,
											                            'products_id' => $supplier_order->products[$i]['id'],
											                            'products_name' => $supplier_order->products[$i]['name'],
											                            'products_display_name' => $supplier_order->products[$i]['display_name'],
											                            'products_purchase_status' => $supplier_order->products[$i]['purchase_status'],
											                            'products_restock_comment' => $list_type == 1 ? '' : $supplier_order->products[$i]['restock_comment'],
											                            'min_quantity' => $supplier_order->products[$i]['min_qty'],
											                            'first_max_quantity' => $supplier_order->products[$i]['first_max'],
																		'first_max_unit_price' => $supplier_order->products[$i]['first_max_unit_price'],
																		'second_max_quantity' => $supplier_order->products[$i]['second_max'],
																		'second_max_unit_price' => $supplier_order->products[$i]['second_max_unit_price'],
											                            'products_provision_amount' => $supplier_order->products[$i]['amount'],
											                            'products_quantity' => $supplier_order->products[$i]['qty'],
											                            'supplier_order_lists_products_comment' => $supplier_order->products[$i]['comment']
											                           );
							    tep_db_perform(TABLE_SUPPLIER_ORDER_LISTS_PRODUCTS, $order_list_product_data_array);
						    } else {
						    	$check_order_list_product_exists_sql = "SELECT supplier_order_lists_products_id, products_received_quantity 
						    											FROM " . TABLE_SUPPLIER_ORDER_LISTS_PRODUCTS . "
						    											WHERE supplier_order_lists_id = '" . tep_db_input($supplier_order_lists_id) . "'
						    												AND supplier_order_lists_type = '" . tep_db_input($list_type) . "'
						    												AND products_id = '" . tep_db_input($supplier_order->products[$i]['id']) . "'";
						    	$check_order_list_product_exists_result_sql = tep_db_query($check_order_list_product_exists_sql);
								if ($check_order_list_product_exists_row = tep_db_fetch_array($check_order_list_product_exists_result_sql)) {
									if (tep_not_null($check_order_list_product_exists_row["products_received_quantity"])) {
										continue;
									}
									// Only products_received_quantity = NULL can be updated
									$order_list_product_data_array = array(	'products_name' => $supplier_order->products[$i]['name'],
												                            'products_display_name' => $supplier_order->products[$i]['display_name'],
												                            'products_purchase_status' => $supplier_order->products[$i]['purchase_status'],
												                            'products_restock_comment' => $list_type == 1 ? '' : $supplier_order->products[$i]['restock_comment'],
												                            'min_quantity' => $supplier_order->products[$i]['min_qty'],
												                            'first_max_quantity' => $supplier_order->products[$i]['first_max'],
																			'first_max_unit_price' => $supplier_order->products[$i]['first_max_unit_price'],
																			'second_max_quantity' => $supplier_order->products[$i]['second_max'],
																			'second_max_unit_price' => $supplier_order->products[$i]['second_max_unit_price'],
												                            'products_provision_amount' => $supplier_order->products[$i]['amount'],
												                            'products_quantity' => $supplier_order->products[$i]['qty'],
												                            'supplier_order_lists_products_comment' => $supplier_order->products[$i]['comment']
												                           );
								    tep_db_perform(TABLE_SUPPLIER_ORDER_LISTS_PRODUCTS, $order_list_product_data_array, 'update', "supplier_order_lists_products_id = '" . $check_order_list_product_exists_row["supplier_order_lists_products_id"] . "'");
								} else {	// not exists yet
									$order_list_product_data_array = array(	'supplier_order_lists_id' => $supplier_order_lists_id,
																			'supplier_order_lists_type' => $list_type,
												                            'products_id' => $supplier_order->products[$i]['id'],
												                            'products_name' => $supplier_order->products[$i]['name'],
												                            'products_display_name' => $supplier_order->products[$i]['display_name'],
												                            'products_purchase_status' => $supplier_order->products[$i]['purchase_status'],
												                            'products_restock_comment' => $list_type == 1 ? '' : $supplier_order->products[$i]['restock_comment'],
												                            'min_quantity' => $supplier_order->products[$i]['min_qty'],
												                            'first_max_quantity' => $supplier_order->products[$i]['first_max'],
																			'first_max_unit_price' => $supplier_order->products[$i]['first_max_unit_price'],
																			'second_max_quantity' => $supplier_order->products[$i]['second_max'],
																			'second_max_unit_price' => $supplier_order->products[$i]['second_max_unit_price'],
												                            'products_provision_amount' => $supplier_order->products[$i]['amount'],
												                            'products_quantity' => $supplier_order->products[$i]['qty'],
												                            'supplier_order_lists_products_comment' => $supplier_order->products[$i]['comment']
												                           );
								    tep_db_perform(TABLE_SUPPLIER_ORDER_LISTS_PRODUCTS, $order_list_product_data_array);	
								}
						    }
						    
						    $total_order_list_amount += (double)$supplier_order->products[$i]['amount'];
						    
						    $cat_path = tep_output_generated_category_path($supplier_order->products[$i]['id'], 'product');
							$product_email_display_str = $cat_path . (tep_not_null($cat_path) ? ' > ' : '') . $supplier_order->products[$i]['name'];
							$supplier_product_list .= $supplier_order->products[$i]['qty'] . ' x ' . $product_email_display_str . ' = ' . $currencies->format((double)$supplier_order->products[$i]['amount']) . "\n";
						}
						
						$latest_supplier_order = new order($supplier_order_lists_id);
						
						if ($list_type == 1) {
							$email_prod_title = EMAIL_TEXT_FIRST_LIST_PRODUCTS;
						} else {
							if ($submit_mode == 'insert') {
								$email_prod_title = EMAIL_TEXT_CONFIRMATION_LIST_PRODUCTS;
							} else {
								$email_prod_title = EMAIL_TEXT_UPDATED_CONFIRMATION_LIST_PRODUCTS;
							}
						}
						
						$email_content = tep_get_email_greeting($supplier_order->supplier['firstname'], $supplier_order->supplier['lastname'], $supplier_order->supplier['gender']) . 
										EMAIL_TEXT_BODY	. 
										sprintf(EMAIL_TEXT_ORDER_NUMBER, $supplier_order_lists_id) . "\n" .
										sprintf(EMAIL_TEXT_ORDER_TITLE, $supplier_order->info['list_name']) . "\n" .
										sprintf(EMAIL_TEXT_DATE_ORDERED, strftime(DATE_FORMAT_LONG)) .
										sprintf(EMAIL_TEXT_PRODUCTS, $email_prod_title) . "\n" . EMAIL_SEPARATOR .
										"\n" . $latest_supplier_order->get_products_ordered($list_type, true) . EMAIL_SEPARATOR . "\n" . EMAIL_TEXT_BUYBACK_TOTAL . $currencies->format($total_order_list_amount) .
										"\n\n" . EMAIL_TEXT_EXTRA . "\n" . $supplier_comment . "\n\n" . EMAIL_TEXT_CLOSING . EMAIL_FOOTER;
						
						// Send an e-mail to supplier
						/*
						if (eregi('^[a-zA-Z0-9._-]+@[a-zA-Z0-9-]+\.[a-zA-Z.]{2,5}$', $supplier_order->supplier['email_address'])) {
							tep_mail($supplier_order->supplier['firstname'] . ' ' . $supplier_order->supplier['lastname'], $supplier_order->supplier['email_address'], sprintf($list_type==1 ? EMAIL_TEXT_SUBJECT : EMAIL_ORDER_UPDATE_SUBJECT, $supplier_order_lists_id), $email_content, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
						}
						*/
						// Send a copy to purchase team members
						$email_supplier_name_subject = $supplier_order->supplier['firstname'] . ' ' . $supplier_order->supplier['lastname'];
						
						$email_to_array = tep_parse_email_string(PURCHASE_TEAM_EMAIL);
		        		for ($i=0; $i < count($email_to_array); $i++) {
		        			tep_mail($email_to_array[$i]['name'], $email_to_array[$i]['email'], sprintf($list_type==1 ? EMAIL_TEXT_SUBJECT : EMAIL_ORDER_UPDATE_SUBJECT, $supplier_order_lists_id, $supplier_order->info['list_name'], $email_supplier_name_subject), $email_content, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
		        		}
		        		
		        		$passive_email_content = tep_get_email_greeting($supplier_order->supplier['firstname'], $supplier_order->supplier['lastname'], $supplier_order->supplier['gender']) . 
												EMAIL_TEXT_BODY	. 
												sprintf(EMAIL_TEXT_ORDER_NUMBER, $supplier_order_lists_id) . "\n" .
												sprintf(EMAIL_TEXT_ORDER_TITLE, $supplier_order->info['list_name']) . "\n" .
												sprintf(EMAIL_TEXT_DATE_ORDERED, strftime(DATE_FORMAT_LONG)) .
												sprintf(EMAIL_TEXT_PRODUCTS, $email_prod_title) . "\n" . EMAIL_SEPARATOR .
												"\n" . $latest_supplier_order->get_products_ordered($list_type, false, false) . EMAIL_SEPARATOR . 
												"\n\n" . EMAIL_TEXT_EXTRA . "\n" . $supplier_comment . "\n\n" . EMAIL_TEXT_CLOSING . EMAIL_FOOTER;
						
						$email_to_array = tep_parse_email_string(JUNIOR_PURCHASE_TEAM_EMAIL);
		        		for ($i=0; $i < count($email_to_array); $i++) {
		        			tep_mail($email_to_array[$i]['name'], $email_to_array[$i]['email'], sprintf($list_type==1 ? EMAIL_TEXT_SUBJECT : EMAIL_ORDER_UPDATE_SUBJECT, $supplier_order_lists_id, $supplier_order->info['list_name'], $email_supplier_name_subject), $passive_email_content, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
		        		}
		        						
						if ($list_type == 1) {
							$messageStack->add_session(SUCCESS_SUBMIT_LIST, 'success');
						} else if ($list_type == 2) {
							if ($submit_mode == 'insert') {
								$messageStack->add_session(SUCCESS_SUBMIT_CONFIRM_LIST, 'success');
							} else {
								$messageStack->add_session(SUCCESS_UPDATE_CONFIRM_LIST, 'success');
							}
						}
					}
					
					// Clear this supplier cart now
					tep_clear_supplier_cart($list_id);
					if ($list_type == 1) {
						tep_redirect(tep_href_link(FILENAME_DEFAULT));
						exit;
					}
				} else {
					$messageStack->add_session(ERROR_UNKNOW_LIST, 'error');
				}
			} else {
				$messageStack->add_session(WARNING_EMPTY_CART, 'warning');
			}
			tep_redirect(tep_href_link(FILENAME_SUPPLIER_LIST, tep_get_all_get_params(array('subaction'))));
			
			break;
	}
}

if ($subaction == 'final') {
	$isLastStep = true;
	$subaction = "final";
} else {
	$isLastStep = false;
	$subaction = "update";
}

$confirmation_list_products_array = array();

if ($list_type == '2') { // this is the confirmation list
	if ($c_mode == 'show') {
		// The only different is using LEFT join
		$products_list_select_product = "	SELECT p2c.categories_id, p.products_id as prd_id, p.products_quantity as inventory_qty, pd.products_name, p.products_cat_path, sp.*, pp.products_purchases_selling_quantity AS selling_qty, solp.products_quantity AS first_list_qty, solp.supplier_order_lists_products_comment AS first_list_comment, rci.restock_character_sets_id, rci.restock_character 
											FROM " . TABLE_SUPPLIER_PRICING . " as sp 
											INNER JOIN " . TABLE_PRODUCTS_TO_CATEGORIES . " AS p2c 
												ON (sp.supplier_pricing_disabled=0 AND p2c.products_id=sp.products_id AND sp.supplier_groups_id='" . $supplier_groups_id . "')
											INNER JOIN " . TABLE_PRODUCTS_PURCHASES . " as pp 
												ON (sp.products_purchases_lists_id=pp.products_purchases_lists_id AND pp.products_purchases_disabled=0 AND p2c.products_id=pp.products_id) 
											INNER JOIN " . TABLE_PRODUCTS . " AS p 
												ON (p2c.products_id=p.products_id AND p.products_bundle='' AND p.products_bundle_dynamic='' AND p.custom_products_type_id=0)
											INNER JOIN " . TABLE_PRODUCTS_DESCRIPTION . " AS pd 
												ON p2c.products_id=pd.products_id 
											LEFT JOIN " . TABLE_RESTOCK_CHARACTER_INFO . " AS rci 
												ON (p.products_id=rci.products_id AND rci.restock_character_sets_id='" . tep_db_input($supplier_pricing_setting_array[KEY_SPS_RSTK_CHAR_SET]) . "') 
											LEFT JOIN " . TABLE_SUPPLIER_ORDER_LISTS_PRODUCTS . " as solp 
												ON (solp.supplier_order_lists_id='".$confirmation_list_global_info['order_list_id']."' AND solp.supplier_order_lists_type=1 AND p2c.products_id=solp.products_id)
											WHERE sp.products_purchases_lists_id = '" . $list_id . "' AND p2c.products_is_link=0 AND pd.language_id = '" . $languages_id . "' 
											ORDER BY p.products_cat_path, pd.products_name, p.products_sort_order";
	} else {
		$all_submitted_products = array();
		
		$unique_product_select_sql = "SELECT DISTINCT products_id AS pid FROM " . TABLE_SUPPLIER_ORDER_LISTS_PRODUCTS . " WHERE supplier_order_lists_id = '" . tep_db_input($confirmation_list_global_info['order_list_id']) . "'";
      	$unique_product_result_sql = tep_db_query($unique_product_select_sql);
      	while ($unique_product_row = tep_db_fetch_array($unique_product_result_sql)) {
      		$all_submitted_products[] = $unique_product_row["pid"];
      	}
		$products_list_select_product = "	SELECT p2c.categories_id, p.products_id as prd_id, p.products_quantity as inventory_qty, pd.products_name, p.products_cat_path, sp.*, pp.products_purchases_selling_quantity AS selling_qty, solp.products_quantity AS first_list_qty, solp.supplier_order_lists_products_comment AS first_list_comment, rci.restock_character_sets_id, rci.restock_character 
											FROM " . TABLE_SUPPLIER_PRICING . " as sp 
											INNER JOIN " . TABLE_PRODUCTS_TO_CATEGORIES . " AS p2c 
												ON (sp.supplier_pricing_disabled=0 AND p2c.products_id=sp.products_id AND sp.supplier_groups_id='" . $supplier_groups_id . "')
											INNER JOIN " . TABLE_PRODUCTS_PURCHASES . " as pp 
												ON (sp.products_purchases_lists_id=pp.products_purchases_lists_id AND pp.products_purchases_disabled=0 AND p2c.products_id=pp.products_id) 
											INNER JOIN " . TABLE_PRODUCTS . " AS p 
												ON (p2c.products_id=p.products_id AND p.products_bundle='' AND p.products_bundle_dynamic='' AND p.custom_products_type_id=0)
											INNER JOIN " . TABLE_PRODUCTS_DESCRIPTION . " AS pd 
												ON p2c.products_id=pd.products_id 
											LEFT JOIN " . TABLE_RESTOCK_CHARACTER_INFO . " AS rci 
												ON (p.products_id=rci.products_id AND rci.restock_character_sets_id='" . tep_db_input($supplier_pricing_setting_array[KEY_SPS_RSTK_CHAR_SET]) . "') 
											LEFT JOIN " . TABLE_SUPPLIER_ORDER_LISTS_PRODUCTS . " as solp 
												ON (solp.supplier_order_lists_id='".$confirmation_list_global_info['order_list_id']."' AND solp.supplier_order_lists_type=1 AND p2c.products_id=solp.products_id)
											WHERE sp.products_purchases_lists_id = '" . $list_id . "' AND p2c.products_id IN ('".implode("', '", $all_submitted_products)."') AND p2c.products_is_link=0 AND pd.language_id = '" . $languages_id . "' 
											ORDER BY p.products_cat_path, pd.products_name, p.products_sort_order";
	}
	
	if ($confirmation_list_global_info["submit_mode"] == 'update') {
		$confirmation_list_select_sql = "	SELECT products_id, products_quantity, products_received_quantity, supplier_order_lists_products_comment FROM " . TABLE_SUPPLIER_ORDER_LISTS_PRODUCTS . " 
											WHERE supplier_order_lists_id='" . $confirmation_list_global_info['order_list_id'] . "' 
												AND supplier_order_lists_type=2";
		$confirmation_list_result_sql = tep_db_query($confirmation_list_select_sql);
		while ($confirmation_list_row = tep_db_fetch_array($confirmation_list_result_sql)) {
			$confirmation_list_products_array[$confirmation_list_row["products_id"]] = $confirmation_list_row;
		}
	}
} else {
	$products_list_select_product = "	SELECT p2c.categories_id, p.products_id as prd_id, p.products_quantity as inventory_qty, pd.products_name, p.products_cat_path, sp.*, pp.products_purchases_selling_quantity AS selling_qty 
										FROM " . TABLE_SUPPLIER_PRICING . " as sp 
										INNER JOIN " . TABLE_PRODUCTS_TO_CATEGORIES . " AS p2c 
											ON (sp.supplier_pricing_disabled=0 AND p2c.products_id=sp.products_id AND sp.supplier_groups_id='" . $supplier_groups_id . "')
										INNER JOIN " . TABLE_PRODUCTS_PURCHASES . " as pp 
											ON (sp.products_purchases_lists_id=pp.products_purchases_lists_id AND pp.products_purchases_disabled=0 AND p2c.products_id=pp.products_id) 
										INNER JOIN " . TABLE_PRODUCTS . " AS p 
											ON (p2c.products_id=p.products_id AND p.products_bundle='' AND p.products_bundle_dynamic='' AND p.custom_products_type_id=0)
										INNER JOIN " . TABLE_PRODUCTS_DESCRIPTION . " AS pd 
											ON p2c.products_id=pd.products_id 
										WHERE sp.products_purchases_lists_id = '" . $list_id . "' AND p2c.products_is_link=0 AND pd.language_id = '" . $languages_id . "' 
										ORDER BY p.products_cat_path, pd.products_name, p.products_sort_order";
}

// stored this sql for import csv file product checking. Do not use hidden field to store it since people can explore our sql and hence the tables fields
$_SESSION['order_lists_import_sql'] = $products_list_select_product;

$result_select_product = tep_db_query($products_list_select_product);

$cart_data = tep_get_supplier_cart($list_id);

$server_status_array = array(	'urgent' => array('name' => 'icon_status_urgent'), 
								'important' => array('name' => 'icon_status_important'), 
								'normal' => array('name' => 'icon_status_normal')
							);

$export_csv_array = array();
?>

<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?=HTML_PARAMS?> >
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=<?=CHARSET?>">
	<title><?=TITLE?></title>
<?
if (file_exists(DIR_WS_LANGUAGES . $sup_language . '/stylesheet.css')) {
	echo '<link rel="stylesheet" type="text/css" href="'. DIR_WS_LANGUAGES . $sup_language . '/stylesheet.css">';
} else {
	echo '<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">';
}
?>
	<script language="javascript" src="includes/general.js"></script>	
	<script>
		var cartQtyElements =  new Array();
		var cartAmountElements = new Array();
		var products_ids = new Array();
		var hr,min,sec,timeoutid;
		var time_expired = false;
		
		var selling_qty_timer = null;
		
		hr = <?=$hrs?>;
		min = <?=$mins?>;
		sec = <?=$secs?>;
		
		function showOverEffect(object, class_name, extra_row) {
			rowOverEffect(object, class_name);
			if (extra_row=="")	return;
			
			var rowObjArray = extra_row.split('##');
			for (var i = 0; i < rowObjArray.length; i++) {
				rowOverEffect(document.getElementById(rowObjArray[i]), class_name);
			}
		}
		
		function showOutEffect(object, class_name, extra_row) {
			rowOutEffect(object, class_name);
			if (extra_row=="")	return;
			var rowObjArray = extra_row.split('##');
			for (var i = 0; i < rowObjArray.length; i++) {
				rowOutEffect(document.getElementById(rowObjArray[i]), class_name);
			}
		}
		
		function showClicked(object, class_name, extra_row) {
			rowClicked(object, class_name);
			if (extra_row=="")	return;
			var rowObjArray = extra_row.split('##');
			for (var i = 0; i < rowObjArray.length; i++) {
				rowClicked(document.getElementById(rowObjArray[i]), class_name);
			}
		}
		
		function getPriceFormula(pid, val_min, val_max1, val_price1, val_max2, val_price2) {
			var selling_obj = DOMCall('cart_quantity_' + pid);
			
			if (selling_obj != null && selling_obj.disabled != true) {
				var valSelling = parseInt(selling_obj.value);
				var valMax1 = parseInt(val_max1);
				var valPrice1 = parseFloat(val_price1);
				var valMax2 = parseInt(val_max2);
				var valPrice2 = parseFloat(val_price2);
				var formula = '';
				var amt = 0;
				
				if (!validateInteger(valSelling))	valSelling = 0;
				
				if (valSelling > 0) {
					if (valSelling <= valMax1 + valMax2) {
						if (valSelling > valMax1) {
							amt = ( valMax1 * valPrice1 ) + ( (valSelling - valMax1) * valPrice2 );
							formula = '(' + valMax1 + ' x ' + valPrice1 + ') + (' + (valSelling - valMax1) + ' x ' + valPrice2 + ')<br>= ' + currency(amt);
						} else {
							amt = valSelling * valPrice1;
							formula = valSelling + ' x ' + valPrice1 + '<br>= ' + currency(amt);
						}
					} else {
						amt = valMax1 * valPrice1;
						formula = valMax1 + ' x ' + valPrice1 + '<br>= ' + currency(amt);
						/*
						amt = ( valMax1 * valPrice1 ) + ( valMax2 * valPrice2 );
						formula = '(' + valMax1 + ' x ' + valPrice1 + ') + (' + valMax2 + ' x ' + valPrice2 + ')<br>= ' + currency(amt);
						*/
					}
					
					ddrivetip(formula, '', 200);
				}
			}
		}
		
		function checkMaxMinQty(selling_obj, val_min, val_max1, val_price1, val_max2, val_price2, allow_zero) {
			if (selling_obj == null) {
				return;
			}
			
			var ret = true;
			var valSelling = parseInt(selling_obj.value);
			var valMin = parseInt(val_min);
			var valMax1 = parseInt(val_max1);
			var valPrice1 = parseFloat(val_price1);
			var valMax2 = parseInt(val_max2);
			var valPrice2 = parseFloat(val_price2);
			
			var valTotalMax = valMax1 + valMax2;
			
			if (validateSignInteger(selling_obj.value)) {
				if (valSelling > 0) {
					if (valSelling < valMin) {
						alert('<?=JS_ALERT_LESS_THAN_MIN_QTY?>' + valMin + '.');
						selling_obj.value = valMin;
						ret = false;
					} else if (valSelling > valTotalMax) {
						alert('<?=JS_ALERT_GREATER_THAN_MAX_QTY?>' + valTotalMax + '.');
						selling_obj.value = valTotalMax;
						ret = false;
					}
				} else {
					if (allow_zero == true) {
						;
					} else {
						alert('<?=JS_ALERT_POSITIVE_QTY?>');
						selling_obj.value = '';
						ret = false;
					}
				}
			} else {
				if (trim_str(selling_obj.value) != '') {
					alert('<?=JS_ALERT_NOT_VALID_QTY?>');
					selling_obj.value = '';
					ret = false;
				} else {
					valSelling = '';
				}
			}
			
			if (ret) {
				selling_obj.value = valSelling;
			} else {
				selling_qty_timer = setTimeout("performTimingEvent('"+selling_obj.id+"')", 1);
			}
			
			update_amount(selling_obj, val_max1, val_price1, val_max2, val_price2);
			
			return ret;
		}
		
		function performTimingEvent(id) {
			document.getElementById(id).focus();
			
			if (selling_qty_timer != null) {
				window.clearTimeout(selling_qty_timer);
			}
		}
		
		function currency(anynum) {
			var cur_symbol_left = '<?=$currencies->currencies[$currency]["symbol_left"]?>';
			var cur_symbol_right = '<?=$currencies->currencies[$currency]["symbol_right"]?>';
			
			var retval = currency_display(anynum, <?=DISPLAY_PRICE_DECIMAL?>);
			
			if (cur_symbol_left != '') {
				return cur_symbol_left + retval;
			} else {
				return retval + cur_symbol_right;
			}
		}
		
		function update_amount(selling_obj, val_max1, val_price1, val_max2, val_price2) {
			if (selling_obj == null) {
				return;
			}
			
			var valSelling = parseInt(selling_obj.value);
			var valMax1 = parseInt(val_max1);
			var valPrice1 = parseFloat(val_price1);
			var valMax2 = parseInt(val_max2);
			var valPrice2 = parseFloat(val_price2);
			var amt = 0;
			
			var pid = replace(selling_obj.id, 'cart_quantity_', '');
			
			var amount_display_obj = DOMCall('cart_amount_' + pid);
			
			if (!validateInteger(valSelling)) {
				valSelling = 0;
			}
			
			if (valSelling <= valMax1 + valMax2) {
				if (valSelling > valMax1) {
					amt = ( valMax1 * valPrice1 ) + ( (valSelling - valMax1) * valPrice2 );
				} else {
					amt = valSelling * valPrice1;
				}
			} else {
				amt = ( valMax1 * valPrice1 ) + ( valMax2 * valPrice2 );
			}
			
			if(amount_display_obj != null) {
				amount_display_obj.innerHTML = currency(amt);
			}
			
			if (trim_str(pid) != '') {
				cartAmountElements[pid] = currency_display(amt, <?=DISPLAY_PRICE_DECIMAL?>);
				cartQtyElements[pid] = valSelling;
			}
			
			update_all_total();
		}
		
		function update_all_total() {
			var total_amt = 0;
			var total_qty = 0;
			var qty, amount = 0;
			
			var total_size = products_ids.length;
			
			for (var i=0; i<total_size; i++) {
				if (cartAmountElements[products_ids[i]] != null) {
					amount = parseFloat(cartAmountElements[products_ids[i]]);
				} else {
					amount = 0;
				}
				
				if (cartQtyElements[products_ids[i]] != null) {
					qty = parseInt(cartQtyElements[products_ids[i]]);
				} else {
					qty = 0;
				}
				
				if (validateInteger(qty) && currencyValidation(amount)) {
					total_amt += amount;
					total_qty += qty;
				}
			}
			
			document.getElementById('total_qty').innerHTML = total_qty;
			document.getElementById('total_amt').innerHTML = currency(total_amt) + '&nbsp;';
		}
		
		function tick_time() {
<?		if ($current_list_status != "STATUS_AUTO") { ?>
			return;
<?		} ?>
			
			if (time_expired)	return;
			
			var ele = document.getElementById('ticker');
			var time_string;
			var hr_pad = '';
			var min_pad = '';
			var sec_pad = '';
			
			sec--;
			if (sec < 0) {
				sec = 59;
				min--;	
				
				if (min < 0) {
					min = 59;
					hr--;
					
					if (hr < 0) {
						hr = 0;
						min = 0;
						sec = 0;
						
						// time's up
						clearTimeout(timeoutid);
						time_expired = true;
						alert("Time Up! Logging you out!");
						document.location.href = '<?=tep_href_link(FILENAME_LOGOFF)?>';
					}
				}
			}
			
			if (sec < 10)	sec_pad = '0';
			if(hr < 10)		hr_pad = '0';
			if(min < 10)	min_pad = '0';		
			
			time_string = '<font color="red">' + hr_pad + hr + ":" + min_pad + min + ":" + sec_pad + sec+'</font>';
			
			ele.innerHTML = time_string;
		}
	</script>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">
<!-- header //-->
<? require(DIR_WS_INCLUDES . 'header.php'); ?>
<!-- header_eof //-->
	<!-- Include div for price calculation formula //-->
	<div id="dhtmlTooltip"></div>
	<script language="javascript" src="<?=DIR_WS_INCLUDES?>javascript/dhtml_tooltip.js"></script>
	<!-- End of include div for price calculation formula //-->
	
	<table border="0" width="100%" height="100%" cellspacing="0" cellpadding="2" align="center" valign="middle">
  		<tr>
    		<td width="100%" valign="top">
				<table border="0" width="100%" cellspacing="0" cellpadding="2" align="center" bgcolor="#ffffff">
					<tr>
						<td valign="top" class="pageHeading"><?=sprintf(HEADING_TITLE, $text_list_title, $text_list_submit_mode) . '&nbsp;-&nbsp;' . $list_name?></td>
						<td align="right" class="pageHeading">
<? 	if ($editable_list && $entry_allowed && $enable_counter) { ?>
							<div id="ticker"><font color="red">00:00:00</font></div> <?=TEXT_TO_DEACTIVATION?>
<?	} else {
		echo '&nbsp;';
	} ?>
						</td>
					</tr>
					<tr>
						<td colspan="2" valign="top"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
					</tr>
					<tr>
						<td align="left">
							<table border="0" cellspacing="0" cellpadding="2">
								<tr>
									<td class="main"><?=sprintf(TEXT_MIN_QTY, (int)$supplier_pricing_setting_array["sps_min_quantity"])?></td>
								</tr>
							</table>
						</td>
						<td align="right">
<?	if ($list_type == '2' && !$isLastStep) {
		$new_cmode = ($c_mode == 'show') ? 'hide' : 'show';
		
		echo tep_draw_form('show_mode_form', FILENAME_SUPPLIER_LIST, tep_get_all_get_params(array('subaction', 'c_mode')) . 'c_mode='.$new_cmode, 'POST', '');
		if ($new_cmode == 'hide') {
			echo tep_submit_button(BUTTON_SHOW_SELLING_PRODUCTS, ALT_BUTTON_SHOW_SELLING_PRODUCTS, '', 'inputButton');
		} else {
			echo tep_submit_button(BUTTON_SHOW_ALL_PRODUCTS, ALT_BUTTON_SHOW_ALL_PRODUCTS, '', 'inputButton');
		}
		echo '</form>';
	}
?>
						</td>
					</tr>
					<tr>
						<td colspan="2">
<?
	echo tep_draw_form('order_list_form', FILENAME_SUPPLIER_LIST, tep_get_all_get_params(array('subaction')) . 'subaction=' . $subaction, 'post', 'enctype="multipart/form-data"');
	$export_csv_array['HEADER'] = array(TABLE_CSV_HEADING_PRODUCT_ID, TABLE_CSV_HEADING_PRODUCT);
?>
							<table width="100%" border="0" cellspacing="0" cellpadding="2">
								<tr>
									<td class="ordersBoxHeading">&nbsp;<?=TABLE_HEADING_PRODUCT?></td>
<?	if ($show_purchase_status) {
		echo '						<td align="center" class="ordersBoxHeading" width="4%">'.TABLE_HEADING_STATUS.'</td>';
		$export_csv_array['HEADER'][] = TABLE_CSV_HEADING_STATUS;
	}
	
	if ($list_type == '2') {
		echo '						<td class="ordersBoxHeading" width="15%">'.TABLE_HEADING_ADMIN_COMMENT.'</td>';
		$export_csv_array['HEADER'][] = TABLE_CSV_HEADING_ADMIN_COMMENT;
	}
	
	$export_csv_array['HEADER'][] = TABLE_CSV_HEADING_MIN_MAX_QTY;
	$export_csv_array['HEADER'][] = TABLE_CSV_HEADING_UNIT_PRICE;
	//$export_csv_array['HEADER'][] = TABLE_CSV_HEADING_OVER_LIMIT_MAX_QTY;
	//$export_csv_array['HEADER'][] = TABLE_CSV_HEADING_UNIT_OVER_LIMIT_PRICE;
?>
									<td class="ordersBoxHeading" width="6%" align="center"><?=TABLE_HEADING_MIN_MAX_QTY?></td>
									<td class="ordersBoxHeading" width="6%" align="center"><?=TABLE_HEADING_UNIT_PRICE?></td>
									<!--td class="ordersBoxHeading" width="6%" align="center"><?=TABLE_HEADING_OVER_LIMIT_MAX_QTY?></td>
									<td class="ordersBoxHeading" width="6%" align="center"><?=TABLE_HEADING_UNIT_OVER_LIMIT_PRICE?></td-->
<?
	if ($list_type == '2') {
		echo '						<td class="ordersBoxHeading" width="6%" align="center">'.TABLE_HEADING_FIRST_SELLING_QTY.'</td>';
		$export_csv_array['HEADER'][] = TABLE_CSV_HEADING_FIRST_SELLING_QTY;
	}
	
	$export_csv_array['HEADER'][] = TABLE_CSV_HEADING_SELLING_QTY;
	$export_csv_array['HEADER'][] = TABLE_CSV_HEADING_SUPPLIER_COMMENT;
?>
									<td class="ordersBoxHeading" width="5%" align="center"><?=TABLE_HEADING_SELLING_QTY?></td>
<?
	if ($list_type == '2') {
		echo '						<td class="ordersBoxHeading" width="5%">&nbsp;</td>';
	}
?>
									<td class="ordersBoxHeading" width="9%" align="right"><?=TABLE_HEADING_AMOUNT?>&nbsp;</td>
									<td class="ordersBoxHeading" width="15%"><?=TABLE_HEADING_SUPPLIER_COMMENT?></td>
								</tr>
<?
	$i = 0;
	$js = "\n";
	
	$list_colspan_count = 6;
	if ($list_type == '2') $list_colspan_count += 3;	// For restock account column
	if ($show_purchase_status) $list_colspan_count++;
	
	$form_can_be_process = false;
	while ($row = tep_db_fetch_array($result_select_product)) {
		if ((int)$row['supplier_pricing_disabled'] == 0) {
			$showed_column = 0;
			$pid = $row['prd_id'];
			
			if ($total_active_supplier > 0) {
				if (tep_not_null($row['supplier_pricing_max_quantity']) && is_numeric($row['supplier_pricing_max_quantity'])) {	// If overwrite maximum qty is set
					$group_max_qty = (int)$row['supplier_pricing_max_quantity'];
				} else {
					if ($supplier_pricing_setting_array[KEY_SPS_USE_MFC_SUPPLY] == '1') {
						$group_max_qty = (int)$supplier_pricing_setting_array[KEY_SPS_MFC_SUPPLY_MAX_QTY];
					} else {
						$group_max_qty = ceil(((int)$row['selling_qty'] * (double)$supplier_pricing_setting_array[KEY_SPS_QUANTITY_RATIO]) / 100);
					}
				}
				$offset_amount = tep_get_previously_restocked_qty($pid, $supplier_groups_id, $list_id);
				$group_max_qty -= (int)$offset_amount;
				
				$this_supplier_max_quantity = (int)$group_max_qty / $total_active_supplier;
				$this_supplier_max_quantity = tep_round_up_to($this_supplier_max_quantity, $list_qty_round_up);
			} else {
				$this_supplier_max_quantity = 0;	// no active supplier means no one can submit
			}
			
			/*
			if ($this_supplier_max_quantity > 0) {
				$this_supplier_over_limit_max_quantity = ((int)$supplier_pricing_setting_array["sps_max_limit"] - ((int)$row['inventory_qty'] + (int)$row['supplier_pricing_max_quantity'])) / $total_active_supplier;
				$this_supplier_over_limit_max_quantity = tep_round_up_to($this_supplier_over_limit_max_quantity, 100);
			} else {
				$this_supplier_over_limit_max_quantity = 0;	// no active supplier means no one can submit
			}
			*/
			$this_supplier_over_limit_max_quantity = 0;	// no active supplier means no one can submit
			
			$allow_zero_quantity = false;
			if ($list_type == '2' && is_numeric($row['first_list_qty'])) {
				$allow_zero_quantity = true;	// For in case they has nothing to submit
			}
			
			if (!$isLastStep || $isLastStep && ((int)$cart_data[$pid]['buyback_basket_quantity'] > 0 || $allow_zero_quantity)) {
				$class = ($i%2) ? 'ordersListingEven' : 'ordersListingOdd' ;
				
				$product_display_name = tep_display_category_path($row['products_cat_path']." > ".$row['products_name'], $row['categories_id'], 'catalog', false);
				$export_csv_array[$pid] = array($pid, $product_display_name);
?>
								<tr height="26" class="<?=$class?>" id="<? echo "custRow_".$i;?>" onClick="showClicked(this,'<?=$class?>', '')" onMouseOver="showOverEffect(this,'ordersListingRowOver','')" onMouseOut="showOutEffect(this,'<?=$class?>','')">
									<td class="dataTableContent" valign="top">&nbsp;<?=$product_display_name?></td>
<?				$showed_column++;
				if ($show_purchase_status) {
					echo '			<td align="center" valign="top" class="dataTableContent">';
					$server_status_index = trim($row['supplier_pricing_product_status']);
					if (isset($server_status_array[$server_status_index])) {
						echo tep_image(DIR_WS_IMAGES . $server_status_array[$server_status_index]['name'] . '.gif', $server_status_trans[$row['supplier_pricing_product_status']]);
					} else { echo '&nbsp;'; }
					echo '			</td>';
					
					$export_csv_array[$pid][] = $row['supplier_pricing_product_status'];
					$showed_column++;
				}
				
				$can_update_quantity = true;
				$text_cannot_update_reason = '';
				if ($list_type == '2') {
					echo '			<td class="dataTableContent" valign="top">'.(tep_not_null($row['restock_character']) && (int)$row['supplier_pricing_show_comment']==1 ? $row['restock_character'] : '&nbsp').'</td>';
					if (tep_not_null($confirmation_list_products_array[$pid]['products_quantity'])) {
						$can_update_quantity = false;	// Already restocked
						$text_cannot_update_reason = TEXT_RESTOCK_DONE;
					} else if ((int)$row['supplier_pricing_show_comment'] != 1 || is_null($row['restock_character_sets_id'])) {
						$can_update_quantity = false;	// No restock account entered yet
						$text_cannot_update_reason = TEXT_RESTOCK_ACCOUNT_NOT_AVAILABLE;
					}
					
					$export_csv_array[$pid][] = (tep_not_null($row['restock_character']) && (int)$row['supplier_pricing_show_comment']==1 ? $row['restock_character'] : '');
					$showed_column++;
				} else if ($list_type == '1') {
					if (!$editable_list) $can_update_quantity = false;	// Not the time to submit the first list yet
				}
				
				if ((int)$row['supplier_pricing_server_full'] == 0 && (int)$this_supplier_max_quantity > 0 && (int)$supplier_pricing_setting_array["sps_min_quantity"] <= (int)$this_supplier_max_quantity) {
					// Accept selling quantity for this product
					$normal_unit_price = (double)$row['supplier_pricing_unit_price'];
					//$over_limit_unit_price = $normal_unit_price * ( (100 - (double)$supplier_pricing_setting_array["sps_over_limit_discount"]) / 100);
					$over_limit_unit_price = 0;
					
					if ($can_update_quantity) {
						if ($list_type == '2' && $cart_data[$pid]['buyback_basket_list_type'] != '2') {
							if ($confirmation_list_global_info["submit_mode"] == 'update') {
								if (isset($confirmation_list_products_array[$pid])) {
									$cart_data[$pid]['buyback_basket_quantity'] = (int)$confirmation_list_products_array[$pid]['products_quantity'];
									$cart_data[$pid]['buyback_basket_comment'] = $confirmation_list_products_array[$pid]['supplier_order_lists_products_comment'];
								}
							} else {
								$cart_data[$pid]['buyback_basket_quantity'] = $row['first_list_qty'];
								$cart_data[$pid]['buyback_basket_comment'] = $row['first_list_comment'];
							}
						}
						
						$form_can_be_process = true;
					} else {
						$cart_data[$pid]['buyback_basket_quantity'] = 0;
					}
					
					$supplier_total_quantity = (int)$supplier_total_quantity + (int)$cart_data[$pid]['buyback_basket_quantity'];
					$cart_data[$pid]['buyback_basket_amount'] = tep_calculate_amount($cart_data[$pid]['buyback_basket_quantity'], $this_supplier_max_quantity, $normal_unit_price, $this_supplier_over_limit_max_quantity, $supplier_pricing_setting_array["sps_over_limit_discount"]);
					
					$supplier_total_amount = (double)$supplier_total_amount + (double)$cart_data[$pid]['buyback_basket_amount'];
					
					echo '			<td class="dataTableContent" align="center" valign="top">'.$this_supplier_max_quantity.'</td>';
					echo '			<td class="dataTableContent" align="right" valign="top" nowrap>'.$currencies->format($normal_unit_price).'</td>';
	  				//echo '			<td class="dataTableContent" align="center" valign="top">'.$this_supplier_over_limit_max_quantity.'</td>';
	  				//echo '			<td class="dataTableContent" align="right" valign="top" nowrap>'.$currencies->format($over_limit_unit_price).'</td>';
					
					$export_csv_array[$pid][] = $this_supplier_max_quantity;
					$export_csv_array[$pid][] = number_format(tep_round($normal_unit_price, $currencies->get_decimal_places()), $currencies->get_decimal_places(), $currencies->currencies[$currency]['decimal_point'], $currencies->currencies[$currency]['thousands_point']);
					//$export_csv_array[$pid][] = $this_supplier_over_limit_max_quantity;
					//$export_csv_array[$pid][] = number_format(tep_round($over_limit_unit_price, $currencies->get_decimal_places), $currencies->get_decimal_places, $currencies->currencies[$currency]['decimal_point'], $currencies->currencies[$currency]['thousands_point']);
					
					if ($list_type == '2') {
						echo '			<td class="dataTableContent" align="center" valign="top">'.$row['first_list_qty'].'</td>';
						$export_csv_array[$pid][] = (int)$row['first_list_qty'];
					}
					
	  				if (!$isLastStep) {
						echo '		<td class="dataTableContent" align="center" valign="top">';
						
						if ($can_update_quantity) {
							$disabled = '';
							$show_input_qty = (isset($HTTP_POST_VARS['btn_csv_import']) && tep_not_null($HTTP_POST_VARS['btn_csv_import']) ? $imported_products_array[$pid]["selling_quantity"] : $cart_data[$pid]['buyback_basket_quantity']);
						} else {
							$disabled = 'DISABLED';
							$show_input_qty = $confirmation_list_products_array[$pid]['products_quantity'];
						}
						
						echo tep_draw_input_field('cart_quantity['.$pid.']', $show_input_qty, 'size="6" id="cart_quantity_'.$pid.'" onBlur="checkMaxMinQty(this, \''.(int)$supplier_pricing_setting_array["sps_min_quantity"].'\', \''.$this_supplier_max_quantity.'\', \''.$normal_unit_price.'\', \''.$this_supplier_over_limit_max_quantity.'\', \''.$over_limit_unit_price.'\', '.($allow_zero_quantity ? 'true' : 'false').');" onKeyPress="return noEnterKey(event)" ' . $disabled);
						
						echo '		</td>';
						
						if ($list_type == '2') {
							echo '	<td class="dataTableContent" align="left" valign="top">';
							if ($can_update_quantity) {
								echo tep_submit_button(BUTTON_CONFIRM, ALT_BUTTON_CONFIRM, 'name="StockConfirmBtn_'.$pid.'" onClick="if (confirm_action(\''.JS_CONFIRM_SELLING_QTY.'\')) { return checkMaxMinQty(document.getElementById(\'cart_quantity_'.$pid.'\'), \''.(int)$supplier_pricing_setting_array["sps_min_quantity"].'\', \''.$this_supplier_max_quantity.'\', \''.$normal_unit_price.'\', \''.$this_supplier_over_limit_max_quantity.'\', \''.$over_limit_unit_price.'\', '.($allow_zero_quantity ? 'true' : 'false').'); } else { return false; }"', 'inputButton');
							} else {
								echo '&nbsp;';
							}
							echo '	</td>';
						}
						
						$export_csv_array[$pid][] = (int)$show_input_qty;
					} else {
						echo '		<td class="dataTableContent" align="center" valign="top">';
						if ($can_update_quantity) {
							echo $cart_data[$pid]['buyback_basket_quantity'];
						} else {
							echo $text_cannot_update_reason;	// Show supplier the reason
						}
						echo '		</td>';
					}
					
					echo '<td class="dataTableContent" align="right" valign="top" id="" nowrap><b><span id="cart_amount_'.$pid.'" onMouseover="getPriceFormula(\''.$pid.'\', \''.(int)$supplier_pricing_setting_array["sps_min_quantity"].'\', \''.$this_supplier_max_quantity.'\', \''.$normal_unit_price.'\', \''.$this_supplier_over_limit_max_quantity.'\', \''.$over_limit_unit_price.'\');" onMouseout="hideddrivetip();">'.$currencies->format($cart_data[$pid]['buyback_basket_amount']).'</span></b>&nbsp;</td>';
					
					if (!$isLastStep) {
						echo '		<td class="dataTableContent" valign="top">';
						if ($can_update_quantity) {
							echo tep_draw_input_field("cart_comment[".$pid."]", (isset($HTTP_POST_VARS['btn_csv_import']) && tep_not_null($HTTP_POST_VARS['btn_csv_import']) ? $imported_products_array[$pid]["supplier_comment"] : $cart_data[$pid]['buyback_basket_comment']), 'size="35" onKeyPress="return noEnterKey(event)" ');
						} else {
							echo '&nbsp;';
						}
						echo '</td>';
						
						$export_csv_array[$pid][] = (isset($HTTP_POST_VARS['btn_csv_import']) && tep_not_null($HTTP_POST_VARS['btn_csv_import']) ? $imported_products_array[$pid]["supplier_comment"] : $cart_data[$pid]['buyback_basket_comment']);
					} else {
						echo '		<td class="dataTableContent" valign="top">';
						if ($can_update_quantity) {
							if (tep_not_null($cart_data[$pid]['buyback_basket_comment'])) {
								echo $cart_data[$pid]['buyback_basket_comment'];
							} else {
								echo '&nbsp;';
							}
						} else {
							echo '&nbsp;';
						}
						echo '</td>';
					}
				} else {
					echo '			<td class="dataTableContent" align="center" colspan="'.($list_colspan_count-$showed_column).'">'.TEXT_CURRENTLY_NOT_BUYING.'</td>';
					for ($csv_cnt=0; $csv_cnt < ($list_colspan_count-$showed_column-1); $csv_cnt++) {
						if ($csv_cnt == ($list_colspan_count-$showed_column-1)-1) {
							$export_csv_array[$pid][] = '';
						} else {
							$export_csv_array[$pid][] = 0;
						}
					}
 				}
?>
								</tr>
<?
				$js .= "products_ids.push(".$pid.");\n";
				$js.= "cartQtyElements['".$pid."'] = ".(isset($cart_data[$pid]['buyback_basket_quantity']) && (int)$cart_data[$pid]['buyback_basket_quantity'] > 0 ? (int)$cart_data[$pid]['buyback_basket_quantity'] : 0).";\n";
				$js.= "cartAmountElements['".$pid."'] = ".(isset($cart_data[$pid]['buyback_basket_amount']) && (double)$cart_data[$pid]['buyback_basket_amount'] > 0 ? (double)$cart_data[$pid]['buyback_basket_amount'] : 0).";\n";
				
				$i++;
			}
		}
	}
?>
								<script><?=$js?></script>
								<tr>
									<td colspan="<?=$list_colspan_count?>" valign="top"><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
								</tr>					
								<tr>
									<td class="dataTableContent" colspan="<?=($list_type == '2' ? $list_colspan_count-4 : $list_colspan_count-3)?>">
<?	if ($show_purchase_status && count($server_status_array)) { ?>
										<table border="0" cellspacing="0" cellpadding="0">
											<tr>
											<?
											foreach ($server_status_array as $ss_name => $ss_res) {
												echo '	<td>'.tep_image(DIR_WS_IMAGES . $ss_res['name'] . '.gif', $server_status_trans[$ss_name]).'</td>
														<td class="smallText" NOWRAP>&nbsp;'.$server_status_trans[$ss_name].'&nbsp;&nbsp;</td>';
											}
											?>
											</tr>
										</table>
<?	} ?>
									</td>
									<td class="dataTableContent" align="center"><b><div id="total_qty"><?=$supplier_total_quantity?></div></b></td>
<?	if ($list_type == '2') echo '	<td class="dataTableContent">&nbsp;</td>'; ?>
									<td class="dataTableContent" align="right"><b><div id="total_amt"><?=$currencies->format($supplier_total_amount).'&nbsp;'?></div></b></td>
									<td class="dataTableContent">&nbsp;</td>
								</tr>				
								<tr>
									<td colspan="<?=$list_colspan_count?>" valign="top"><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
								</tr>
<?	if ($isLastStep && sizeof($cart_data)>0) {	//show comment box ?>
								<tr>
									<td class="main" align="left" valign="top" colspan="<?=$list_colspan_count?>">
										<?=TEXT_ADDITIONAL_COMMENTS?><br>
										<?=tep_draw_textarea_field('supplier_comment', 'virtual', 120, 5, '', ' style="width: 100%"')?>
									</td>
								</tr>
								<tr>
									<td width="10" colspan="<?=$list_colspan_count?>"><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
								</tr>
<?	}
	
	if ($list_type == '1') {
?>
								<tr>
									<td colspan="<?=$list_colspan_count?>">
										<table width="100%" border="0" cellspacing="0" cellpadding="2">
											<tr>
<?		if ($isLastStep) {
			if ($form_can_be_process) {
?>
												<td align="left">
													<?=tep_button(BUTTON_BACK, ALT_BUTTON_BACK, tep_href_link(FILENAME_SUPPLIER_LIST, tep_get_all_get_params(array('subaction'))), '', 'inputButton')?>
												</td>
												<td align="right"><?=tep_draw_input_field("submit", BUTTON_CONFIRM, 'title="'.ALT_BUTTON_CONFIRM.'" class="inputButton"', false, 'submit')?></td>
<? 			}
		} else { ?>
												<td align="left">
												<?
												if ($form_can_be_process) {
													echo tep_draw_file_field('csv_import', 'size="50"') . '&nbsp;' . tep_draw_input_field("btn_csv_import", BUTTON_IMPORT, 'title="'.CUST_ALT_BUTTON_IMPORT.'" class="inputButton"', false, 'submit');
												}
												?>
												</td>
												<td align="right">
												<?
													echo tep_draw_input_field("btn_csv_export", BUTTON_EXPORT_TEMPLATE, 'title="'.CUST_ALT_BUTTON_EXPORT.'" class="inputButton"', false, 'submit');
													if ($form_can_be_process) {
														echo '&nbsp;' . tep_draw_input_field("submit_list", BUTTON_SUBMIT, 'title="'.ALT_BUTTON_SUBMIT.'" class="inputButton"', false, 'submit');
													}
												?>
												</td>
<?			echo tep_draw_hidden_field("serialized_export_csv_array", tep_array_serialize($export_csv_array)) . "\n";
		} ?>
											</tr>
										</table>
									</td>
								</tr>
<?	} ?>
							</table>
							</form>
						</td>
					</tr>
					<tr>
        				<td colspan="2">
        					<? if ($editable_list && $entry_allowed && $enable_counter) { ?>
        							<script>setInterval('tick_time()',1000);</script>
        					<? }?>
        				</td>
      				</tr>
				</table>
<!-- body_text_eof //-->
  			</td>
  		</tr>
	</table>
<!-- body_eof //-->
<br>
<!-- footer //-->
<? require(DIR_WS_INCLUDES . 'footer.php'); ?>
<!-- footer_eof //-->
<? require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>
</body>
</html>