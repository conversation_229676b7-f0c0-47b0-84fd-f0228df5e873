<?

require('includes/application_top.php');

$action = $_REQUEST["action"];
$subaction = $_REQUEST["subaction"];

if (tep_not_null($action)) {
	switch ($action) {
		case "save_comment":
			while (list($key, $value) = each($HTTP_POST_VARS['mac_comment'])) {
          		$mac_comment = tep_db_prepare_input($value);
          		$mac_comment_update_sql = "	UPDATE " . TABLE_SUPPLIER_CREW_MAC_ADDRESS . " 
          									SET supplier_crew_mac_comment = '" . tep_db_input($mac_comment) . "' 
          									WHERE supplier_crew_mac_address_id = '" . $key . "'
          										AND supplier_crew_mac_comment <> '" . tep_db_input($mac_comment) . "'";
          		tep_db_query($mac_comment_update_sql);
        	}
        	
        	$messageStack->add_session('MAC comments has been successfully updated!', 'success');
        	tep_redirect(tep_href_link(FILENAME_SUPPLIER_MAC_ACTIVATION));
        	
			break;
		case "set_mac_status":
			if ( ($_REQUEST['flag'] == '0') || ($_REQUEST['flag'] == '1') || ($_REQUEST['flag'] == '2')) {
          		if (isset($_REQUEST['macID'])) {
          			if ($_REQUEST['flag'] == '1') {
          				$supplier_tasks_allocation_slots_select_sql = "	SELECT supplier_tasks_allocation_slots 
          																FROM " . TABLE_SUPPLIER_TASKS_SETTING . " 
          																WHERE suppliers_id ='" . tep_db_input($supplier_id) . "'";
          				$supplier_tasks_allocation_slots_result_sql = tep_db_query($supplier_tasks_allocation_slots_select_sql);
          				$supplier_tasks_allocation_slots_row = tep_db_fetch_array($supplier_tasks_allocation_slots_result_sql);
          				
          				$supplier_crew_mac_address_status_count_select_sql = "	SELECT count(scma.supplier_crew_mac_address_status) AS total_active 
          																		FROM " . TABLE_SUPPLIER_CREW_MAC_ADDRESS . " AS scma 
          																		INNER JOIN " . TABLE_SUPPLIER_CREW . " AS sc 
          																			ON (scma.request_by_supplier_crew_id = sc.supplier_crew_id) 
          																		INNER JOIN " . TABLE_SUPPLIER . " AS s 
          																			ON (s.supplier_id = sc.supplier_id) 
          																		WHERE s.supplier_id='" . tep_db_input($supplier_id) . "' AND scma.supplier_crew_mac_address_status = 1";
          				$supplier_crew_mac_address_status_count_result_sql = tep_db_query($supplier_crew_mac_address_status_count_select_sql);
          				$supplier_crew_mac_address_status_count_row = tep_db_fetch_array($supplier_crew_mac_address_status_count_result_sql);
          				
          				if ($supplier_crew_mac_address_status_count_row['total_active'] < $supplier_tasks_allocation_slots_row['supplier_tasks_allocation_slots']) {
	          				$mac_status_update_sql = "UPDATE " . TABLE_SUPPLIER_CREW_MAC_ADDRESS . " SET supplier_crew_mac_address_status = '".(int)$_REQUEST['flag']."' WHERE supplier_crew_mac_address_id='" . (int)$_REQUEST["macID"] . "'";
							tep_db_query($mac_status_update_sql);
						} else {
							$messageStack->add_session(ERROR_LIMIT_MESSAGE . $supplier_crew_mac_address_status_count_row['total_active']);
        					tep_redirect(tep_href_link(FILENAME_SUPPLIER_MAC_ACTIVATION));
						}
          			} else {
	          			$mac_status_update_sql = "UPDATE " . TABLE_SUPPLIER_CREW_MAC_ADDRESS . " SET supplier_crew_mac_address_status = '".(int)$_REQUEST['flag']."' WHERE supplier_crew_mac_address_id='" . (int)$_REQUEST["macID"] . "'";
						tep_db_query($mac_status_update_sql);
					}
          		}
        	}
        	
        	tep_redirect(tep_href_link(FILENAME_SUPPLIER_MAC_ACTIVATION));
        	
			break;
	}
}

if (tep_not_null($subaction)) {
	switch ($subaction) {
		case "confirm_delete_mac":
			$mac_delete_sql = "DELETE FROM " . TABLE_SUPPLIER_CREW_MAC_ADDRESS . " WHERE supplier_crew_mac_address_id ='" . (int)$_REQUEST["macID"] . "'";
			tep_db_query($mac_delete_sql);
			
			$messageStack->add_session(SUCCESS_MAC_DELETE, 'success');
			tep_redirect(tep_href_link(FILENAME_SUPPLIER_MAC_ACTIVATION));
			
			break;
	}
}

$mac_status_array = array(	'1'=> array('name' => 'icon_status_green', 'alt' => IMAGE_ICON_STATUS_GREEN, 'alt2' => IMAGE_ICON_STATUS_GREEN_LIGHT),
							'2'=>  array('name' => 'icon_status_yellow', 'alt' => IMAGE_ICON_STATUS_YELLOW, 'alt2' => IMAGE_ICON_STATUS_YELLOW_LIGHT),
							'0'=> array('name' => 'icon_status_red', 'alt' => IMAGE_ICON_STATUS_RED, 'alt2' => IMAGE_ICON_STATUS_RED_LIGHT)
						);
?>

<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?=HTML_PARAMS?>>
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=<?php echo CHARSET; ?>">
	<title><?=TITLE?></title>
<?
if (file_exists(DIR_WS_LANGUAGES . $sup_language . '/stylesheet.css')) {
	echo '<link rel="stylesheet" type="text/css" href="'. DIR_WS_LANGUAGES . $sup_language . '/stylesheet.css">';
} else {
	echo '<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">';
}
?>
<script language="javascript" src="includes/general.js"></script>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">
<!-- header //-->
<? require(DIR_WS_INCLUDES . 'header.php'); ?>
<!-- header_eof //-->
<!-- body //-->
	<table border="0" width="100%" cellspacing="2" cellpadding="2">
  		<tr>
			<!-- body_text //-->    
    		<td width="100%" valign="top">
    			<table border="0" width="100%" cellspacing="0" cellpadding="2">
<!-- body_text //-->
					<tr>
            			<td class="pageHeading" valign="top"><?=HEADING_TITLE?></td>
          			</tr>
					<tr>
						<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
					</tr>
<?
echo tep_draw_form('mac_form', FILENAME_SUPPLIER_MAC_ACTIVATION, 'action=save_comment');

$mac_select_sql = " SELECT * FROM " . TABLE_SUPPLIER_CREW_MAC_ADDRESS . " WHERE supplier_id ='" . (int)$supplier_id . "' ORDER BY request_date DESC";
$mac_result_sql = tep_db_query($mac_select_sql);
if (tep_db_num_rows($mac_result_sql)) {
?>
					<tr>
        				<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
      				</tr>
					<tr>
            			<td valign="top">
            				<table border="0" width="75%" align="center" cellspacing="1" cellpadding="2">
			   					<tr>
								    <td class="reportBoxHeading"><?=TABLE_HEADING_MAC_ADDRESS?></td>
								    <td class="reportBoxHeading"><?=TABLE_HEADING_REQUEST_DATE?></td>
								    <td class="reportBoxHeading"><?=TABLE_HEADING_REQUEST_IP?></td>
								    <td class="reportBoxHeading" align="center"><?=TABLE_HEADING_MAC_STATUS?></td>
								    <td class="reportBoxHeading"><?=TABLE_HEADING_MAC_COMMENT?></td>
								    <td width="5%" align="center" class="reportBoxHeading"><?=TABLE_HEADING_ACTION?></td>
								</tr>
<?
	$row_count = 0;
	while ($mac_row = tep_db_fetch_array($mac_result_sql)) {
		$row_style = ($row_count%2) ? 'reportListingEven' : 'reportListingOdd' ;
?>
								<tr class="<?=$row_style?>" onmouseover="rowOverEffect(this, 'reportListingRowOver')" onmouseout="rowOutEffect(this, '<?=$row_style?>')" onclick="rowClicked(this, '<?=$row_style?>')">
									<td class="reportRecords" valign="top" nowrap><?=$mac_row["supplier_crew_mac_address"]?></td>
									<td class="reportRecords" valign="top"><?=$mac_row["request_date"]?></td>
									<td class="reportRecords" valign="top"><?=$mac_row["request_ip"]?></td>
									<td class="reportRecords" align="center" valign="top">
									<?
									foreach ($mac_status_array as $status_id => $img_res) {
										if ((int)$mac_row["supplier_crew_mac_address_status"] == (int)$status_id) {
											echo tep_image(DIR_WS_IMAGES . $img_res['name'] . '.gif', $img_res['alt'], 10, 10) . '&nbsp;&nbsp;';
										} else {
											echo '<a href="' . tep_href_link(FILENAME_SUPPLIER_MAC_ACTIVATION, 'action=set_mac_status&flag='.(int)$status_id.'&macID=' . $mac_row['supplier_crew_mac_address_id']) . '">' . tep_image(DIR_WS_IMAGES . $img_res['name'] . '_light.gif', $img_res['alt2'], 10, 10) . '</a>&nbsp;&nbsp;';
										}
									}
									?>
									</td>
									<td width="30%" valign="top" class="reportRecords">
										<?=tep_draw_input_field("mac_comment[".$mac_row['supplier_crew_mac_address_id']."]", $mac_row["supplier_crew_mac_comment"], 'size="50"')?>
									</td>
									<td align="center" class="reportRecords" nowrap>
										<a href="javascript:void(confirm_delete('<?='MAC Address: '.$mac_row["supplier_crew_mac_address"]?>', '', '<?=tep_href_link(FILENAME_SUPPLIER_MAC_ACTIVATION, 'action=delete_mac&subaction=confirm_delete_mac&macID='.$mac_row['supplier_crew_mac_address_id'])?>'))"><?=tep_image(DIR_WS_ICONS."delete.gif", "Delete mac address", "", "", 'align="top"')?></a>
									</td>
								</tr>
<?		$row_count++;
	}
	
	echo '						<tr>
									<td colspan="7" align="right"><br>'.tep_submit_button(IMAGE_UPDATE, IMAGE_UPDATE, 'name="submitBtn"').'</td>
								</tr>';
?>
							</table>
			   			</td>
			   		</tr>
<?
}
?>
					</form>
          		</table>
          	</td>
		</tr>
	</table>
</body>
</html>