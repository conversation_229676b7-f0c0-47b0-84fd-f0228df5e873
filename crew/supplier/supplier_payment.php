<?
require('includes/application_top.php');
//require(DIR_WS_CLASSES . 'supplier_payment.php');
require(DIR_WS_CLASSES . 'order.php');

$currencies->set_decimal_places(DISPLAY_PRICE_DECIMAL);

$action = (isset($_REQUEST["action"]) ? $_REQUEST["action"] : '');

if (tep_not_null($action)) {
	switch ($action) {
		case "show_report":
			if (!$_REQUEST['cont']) {
				$_SESSION['payment_inputs']["start_date"] = $_REQUEST["start_date"];
				$_SESSION['payment_inputs']["end_date"] = $_REQUEST["end_date"];
				$_SESSION['payment_inputs']["payment_id"] = $_REQUEST["payment_id"];
				$_SESSION['payment_inputs']["order_id"] = $_REQUEST["order_id"];
				$_SESSION['payment_inputs']["show_records"] = $_REQUEST["show_records"];
		  	}
		  	
		  	$payment_id_str = (isset($_SESSION['payment_inputs']["payment_id"]) && tep_not_null($_SESSION['payment_inputs']["payment_id"])) ? " sp.supplier_payments_id='" . $_SESSION['payment_inputs']["payment_id"] . "'" : "1";
			$order_id_str = (isset($_SESSION['payment_inputs']["order_id"]) && tep_not_null($_SESSION['payment_inputs']["order_id"])) ? " spo.supplier_order_lists_id = '".$_SESSION['payment_inputs']["order_id"]."'" : "1";
		  	$supplier_str = " sp.suppliers_id='" . tep_db_input($supplier_id) . "'";
		  	
		  	$comparing_date_field = 'sph.date_added';
		  	$result_display_text = TEXT_DISPLAY_NUMBER_OF_PAYMENTS_TRANSACTIONS;
		  	
		  	if (tep_not_null($_SESSION['payment_inputs']["start_date"])) {
				if (strpos($_SESSION['payment_inputs']["start_date"], ':') !== false) {
					$startDateObj = explode(' ', trim($_SESSION['payment_inputs']["start_date"]));
					list($yr, $mth, $day) = explode('-', $startDateObj[0]);
					list($hr, $min) = explode(':', $startDateObj[1]);
					$start_date_str = " ( $comparing_date_field >= '".date("Y-m-d H:i:s", mktime((int)$hr, (int)$min, 0, $mth, $day, $yr))."' )";
				} else {
					list($yr, $mth, $day) = explode('-', trim($_SESSION['payment_inputs']["start_date"]));
					$start_date_str = " ( DATE_FORMAT($comparing_date_field, '%Y-%m-%d') >= DATE_FORMAT('".date("Y-m-d", mktime(0,0,0,$mth,$day,$yr))."','%Y-%m-%d') )";
				}
			} else {
				$start_date_str = " 1 ";
			}
			
			if (tep_not_null($_SESSION['payment_inputs']["end_date"])) {
				if (strpos($_SESSION['payment_inputs']["end_date"], ':') !== false) {
					$endDateObj = explode(' ', trim($_SESSION['payment_inputs']["end_date"]));
					list($yr, $mth, $day) = explode('-', $endDateObj[0]);
					list($hr, $min) = explode(':', $endDateObj[1]);
					$end_date_str = " ( $comparing_date_field <= '".date("Y-m-d H:i:s", mktime((int)$hr, (int)$min, 0, $mth, $day, $yr))."' )";
				} else {
					list($yr, $mth, $day) = explode('-', trim($_SESSION['payment_inputs']["end_date"]));
					$end_date_str = " ( DATE_FORMAT($comparing_date_field, '%Y-%m-%d') <= DATE_FORMAT('".date("Y-m-d", mktime(0,0,0,$mth,$day,$yr))."','%Y-%m-%d') )";
				}
			} else {
				$end_date_str = " 1 ";
			}
			
			$payments_select_str = "select sp.supplier_payments_id, sp.supplier_payments_amount, sp.supplier_payments_total, DATE_FORMAT(sp.supplier_payments_date, '%Y-%m-%d %H:%i:%s') AS payment_date, sp.currency, sp.currency_value, sp.supplier_payments_status as cur_status, sph.supplier_payments_history_id as trans_id, DATE_FORMAT(sph.date_added, '%Y-%m-%d %H:%i:%s') AS trans_date, sph.supplier_notified, sph.comments, sph.supplier_payments_status as p_status from " . TABLE_SUPPLIER_PAYMENTS . " as sp inner join " . TABLE_SUPPLIER_PAYMENTS_HISTORY . " as sph on (sp.supplier_payments_id=sph.supplier_payments_id and sph.supplier_payments_status>0) " . (tep_not_null($_SESSION['payment_inputs']["order_id"]) ? "inner join " . TABLE_SUPPLIER_PAYMENTS_ORDERS . " as spo on (sp. supplier_payments_id=spo.supplier_payments_id)" : '') . " left join " . TABLE_SUPPLIER . " as s on sp.suppliers_id=s.supplier_id ";
	  		$payments_order_by_str = " order by sph.date_added ";
	  		
	  		$history_title = TEXT_MODE_T;
			
		  	$payments_where_str = " where $supplier_str ";
		  	$payments_where_str .= " and $payment_id_str and $order_id_str and $start_date_str and $end_date_str ";
		  	
		  	$payments_group_by_str = " ";
		  	
		  	$show_records = $_SESSION['payment_inputs']["show_records"];
		  	
			break;
        case "reset_session":
        	unset($_SESSION['payment_inputs']);
        	tep_redirect(tep_href_link(FILENAME_SUPPLIER_PAYMENT));
        	break;
	}
}

$payment_statuses = array();
$payment_status_array = array();
$payment_status_select_sql = "SELECT supplier_payments_status_id, supplier_payments_status_name FROM " . TABLE_SUPPLIER_PAYMENTS_STATUS . " WHERE language_id = '" . (int)$languages_id . "' ORDER BY supplier_payments_status_sort_order";
$payment_status_result_sql = tep_db_query($payment_status_select_sql);
while ($payment_status_row = tep_db_fetch_array($payment_status_result_sql)) {
	$payment_statuses[] = array('id' => $payment_status_row['supplier_payments_status_id'],
                              	'text' => $payment_status_row['supplier_payments_status_name']);
    $payment_status_array[$payment_status_row['supplier_payments_status_id']] = $payment_status_row['supplier_payments_status_name'];                       
}
?>
<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?=HTML_PARAMS?> >
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=<?=CHARSET?>">
	<title><?=TITLE?></title>
	<?
	if (file_exists(DIR_WS_LANGUAGES . $sup_language . '/stylesheet.css')) {
		echo '<link rel="stylesheet" type="text/css" href="'. DIR_WS_LANGUAGES . $sup_language . '/stylesheet.css">';
	} else {
		echo '<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">';
	}
	?>
	<link rel="stylesheet" type="text/css" href="includes/javascript/spiffyCal/spiffyCal_v2_1.css">
	<script language="javascript" src="includes/general.js"></script>
	<script language="JavaScript" src="includes/javascript/spiffyCal/spiffyCal_v2_1.js"></script>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">
<div id="spiffycalendar" class="text"></div>
<!-- header //-->
<? require(DIR_WS_INCLUDES . 'header.php'); ?>
<!-- header_eof //-->

<!-- body_text //-->
	<table border="0" width="100%" cellspacing="0" cellpadding="2">
<?
if ($_REQUEST['action'] == 'show_report') {
	$total_payment_amt = 0;
	$list_colspan_count = 4;
?>
					<tr>
            			<td valign="top" class="pageHeading"><?=HEADING_TITLE?></td>
            			<td class="smallText" align="right" valign="top">
            			<?
				  			echo tep_draw_form('back_form', FILENAME_SUPPLIER_PAYMENT, '', 'GET', '');
				  			echo tep_submit_button(BUTTON_BACK, ALT_BUTTON_BACK, '', 'inputButton');
				  			echo '</form>';
				  		?>
            			</td>
          			</tr>
          			<tr>
          				<td colspan="2">
							<table border="0" width="100%" cellspacing="0" cellpadding="2" style="border-collapse: collapse;">
  								<tr>
    								<td colspan="<?=$list_colspan_count?>">
    									<!--span class="pageHeading"><?=$history_title?></span-->
									</td>
			  					</tr>
<?	if (isset($_SESSION['payment_inputs'])) { ?>
								<tr>
									<td colspan="<?=$list_colspan_count?>">
										<table border="0" cellspacing="2" cellpadding="2">
											<tr>
												<td class="main"><?=TEXT_SEARCH_ORDER_START_DATE . $_SESSION['payment_inputs']["start_date"]?></td>
												<td width="5px">&nbsp;</td>
												<td class="main"><?=TEXT_SEARCH_ORDER_END_DATE . $_SESSION['payment_inputs']["end_date"]?></td>
											</tr>
											<tr>
												<td class="main"><?=TEXT_SEARCH_PAYMENT_ID . $_SESSION['payment_inputs']["payment_id"]?></td>
												<td width="5px">&nbsp;</td>
												<td class="main"><?=TEXT_SEARCH_ORDER_ID . $_SESSION['payment_inputs']["order_id"]?></td>
											</tr>
										</table>
									</td>
								</tr>
<?	} ?>
								<tr>
									<td width="10%" class="ordersBoxHeading"><?=TABLE_HEADING_PAYMENT_NO?></td>
									<td width="15%" class="ordersBoxHeading"><?=TABLE_HEADING_TRANS_DATE?></td>
									<td class="ordersBoxHeading"><?=TABLE_HEADING_REMARK?></td>
								    <td width="15%" class="ordersBoxHeading" align="right"><?=TABLE_HEADING_TRANS_AMOUNT?></td>
								</tr>
<?
	$payments_select_sql = $payments_select_str . $payments_where_str . $payments_group_by_str . $payments_order_by_str;
	
	if ($show_records != "ALL") {
		$payments_split_object = new splitPageResults($HTTP_GET_VARS['page'], ($show_records=='' || $show_records=='DEFAULT' ? MAX_DISPLAY_SEARCH_RESULTS : (int) $show_records), $payments_select_sql, $payments_select_sql_numrows, true);
	}
	$payments_result_sql = tep_db_query($payments_select_sql);
	
	$row_count = 0;
	
	while ($row = tep_db_fetch_array($payments_result_sql)) {
		$payment_number = $row['supplier_payments_id'];
		
		$unique_product_reference = $row_count . '_' . $payment_number;
		
		$payment_date = $row['trans_date'];
		$payment_amt = $row['supplier_payments_total'];
		
		$row_style = ($row_count%2) ? 'ordersListingEven' : 'ordersListingOdd' ;
		
		$is_credit_amount = $row['p_status'] == '3' ? true : false;
		
		if ($is_credit_amount) {
			$total_payment_amt -= $payment_amt;
		} else {
			$total_payment_amt += $payment_amt;
		}
		
		$payment_orders_select_sql = "	SELECT spo.supplier_order_lists_id, spo.supplier_payments_orders_paid_amount, spo.supplier_payments_type, sol.supplier_order_lists_date, sol.currency, sol.currency_value 
    									FROM " . TABLE_SUPPLIER_PAYMENTS_ORDERS . " AS spo 
    									INNER JOIN " . TABLE_SUPPLIER_ORDER_LISTS . " AS sol 
    										ON spo.supplier_order_lists_id=sol.supplier_order_lists_id 
    									WHERE spo.supplier_payments_id = '" . tep_db_input($payment_number) . "'
    									ORDER BY spo.supplier_order_lists_id";
    	$payment_orders_result_sql = tep_db_query($payment_orders_select_sql);
?>
								<tr height="20" id="<?='main_'.$row_count?>" class="<?=$row_style?>" onmouseover="showOverEffect(this, 'ordersListingRowOver', '<?='sub_'.$row_count?>##<?='sub2_'.$row_count?>')" onmouseout="showOutEffect(this, '<?=$row_style?>', '<?='sub_'.$row_count?>##<?='sub2_'.$row_count?>')" onclick="showClicked(this, '<?=$row_style?>', '<?='sub_'.$row_count?>##<?='sub2_'.$row_count?>')">
									<td class="ordersRecords" nowrap><a href="javascript:;" onClick="paymentOrderInfo('<?=$unique_product_reference?>');"><?=$payment_number?></a></td>
							      	<td class="ordersRecords" nowrap><?=$payment_date?></td>
									<td class="ordersRecords"><?=$row['supplier_notified']=='1' ? $row['comments'] : '&nbsp;'?></td>
							      	<td class="ordersRecords" align="right">
							      		<a href="javascript:;" onClick="paymentOrderInfo('<?=$unique_product_reference?>');"><?=sprintf($is_credit_amount ? '(%s)' : '%s', $currencies->format($payment_amt, true, $row['currency'], $row['currency_value']))?></a>
									</td>
								</tr>
								<tbody id="<?=$unique_product_reference.'_order_sec'?>" class="hide">
                    				<tr id="<?='sub_'.$row_count?>" class="<?=$row_style?>" onmouseover="showOverEffect(this, 'ordersListingRowOver', '<?='main_'.$row_count?>##<?='sub2_'.$row_count?>')" onmouseout="showOutEffect(this, '<?=$row_style?>', '<?='main_'.$row_count?>##<?='sub2_'.$row_count?>')" onclick="showClicked(this, '<?=$row_style?>', '<?='main_'.$row_count?>##<?='sub2_'.$row_count?>')">
                    					<td colspan="<?=($list_colspan_count-2)?>">&nbsp;</td>
			  							<td align="right" colspan="2">
			  								<div id="<?=$unique_product_reference.'_order'?>">
			  									<table border="0" width="50%" cellspacing="1" cellpadding="0">
													<tr>
								          				<td colspan="4"><div style="border-bottom: 1px solid #996600; font-size: 1px; height: 2px; padding-top: 3px;"></td>
								          			</tr>
								          			<tr>
								        				<td width="25%" align="left" class="subInvoiceBoxHeading"><?=TABLE_HEADING_ORDER_NO?></td>
								        				<td width="25%" align="left" class="subInvoiceBoxHeading"><?=TABLE_HEADING_ORDER_DATE?></td>
														<td width="25%" align="right" class="subInvoiceBoxHeading"><?=TABLE_HEADING_ORDER_AMOUNT?></td>
														<td width="25%" align="right" class="subInvoiceBoxHeading"><?=TABLE_HEADING_PAID_AMOUNT?></td>
													</tr>
													<tr>
								          				<td colspan="4"><div style="border-bottom: 1px solid #996600; "></td>
								      				</tr>
								      				<tr><td colspan="4"></td></tr>
<?
		while ($payment_orders_row = tep_db_fetch_array($payment_orders_result_sql)) {
	  		$order_payable_amount = order::get_order_total_payable_amount($payment_orders_row['supplier_order_lists_id']);
	  		
	  		echo '									<tr>
	  													<td align="left" valign="top" class="invoiceRecords">
	  														<a href="' . tep_href_link(FILENAME_SUPPLIER_HISTORY, 'oID='.$payment_orders_row['supplier_order_lists_id'].'&action=view', 'NONSSL') . '" target="_blank">'.$payment_orders_row['supplier_order_lists_id'].'</a>' . ($payment_orders_row['supplier_payments_type']=='1' ? '&nbsp;' . TEXT_PARTIAL_PAID_ORDER : '') . '
	  													</td>
	  													<td align="left" valign="top" class="invoiceRecords" nowrap>' . $payment_orders_row['supplier_order_lists_date'] . '</td>
								  						<td align="right" valign="top" class="invoiceRecords">' . $currencies->format($order_payable_amount, true, $payment_orders_row['currency'], $payment_orders_row['currency_value']) . '</td>
									  					<td align="right" valign="top" class="invoiceRecords">' . $currencies->format($payment_orders_row['supplier_payments_orders_paid_amount'], true, $row['currency'], $row['currency_value']) . '</td>
									  				</tr>';
	  	}
?>
												</table>
			  								</div>
			  							</td>
			  						</tr>
				               	</tbody>
<?
		$row_count++;
	}
	/*
	if ($row_count > 0) {
		echo '					<tbody>
									<tr>
										<td class="ordersRecords" colspan="'.($list_colspan_count-1).'">&nbsp;</td>
										<td align="right" class="ordersRecords"><b>'.$currencies->format($total_payment_amt).'</b></td>
									</tr>
								</tbody>';
	}
	*/
?>
							</table>
						</form>
						</td>
					</tr>
					<tr>
            			<td colspan="2">
            				<table border="0" width="100%" cellspacing="0" cellpadding="2">
              					<tr>
                					<td class="smallText" valign="top"><?=$show_records == "ALL" ? sprintf($result_display_text, tep_db_num_rows($payments_result_sql) > 0 ? "1" : "0", tep_db_num_rows($payments_result_sql), tep_db_num_rows($payments_result_sql)) : $payments_split_object->display_count($payments_select_sql_numrows, ($show_records=='' || $show_records=='DEFAULT' ? MAX_DISPLAY_SEARCH_RESULTS : (int)$show_records), $HTTP_GET_VARS['page'], $result_display_text)?></td>
                					<td class="smallText" align="right"><?=$show_records == "ALL" ? sprintf(TEXT_RESULT_PAGE, '1', '1') : $payments_split_object->display_links($payments_select_sql_numrows, ($show_records=='' || $show_records=='DEFAULT' ? MAX_DISPLAY_SEARCH_RESULTS : (int) $show_records), MAX_DISPLAY_PAGE_LINKS, $HTTP_GET_VARS['page'], tep_get_all_get_params(array('page', 'cont'))."cont=1", 'page')?></td>
              					</tr>
            				</table>
            			</td>
					</tr>
					<tr>
				  		<td align="right" colspan="2"><br>
				  		<?
				  			echo tep_draw_form('back_form', FILENAME_SUPPLIER_PAYMENT, '', 'GET', '');
				  			echo tep_submit_button(BUTTON_BACK, ALT_BUTTON_BACK, '', 'inputButton');
				  			echo '</form>';
				  		?>
	 					</td>
	 				</tr>
	          		<script language="javascript">
					<!--
						function paymentOrderInfo(sec_id) {
							var sec_obj = DOMCall(sec_id + '_order_sec');
							
							if (sec_obj != null) {
								if (sec_obj.className == 'show') {
									sec_obj.className = 'hide';
								} else {
									sec_obj.className = 'show'
								}
							}
						}
					
						function hideShow(groupName, styleClass) {
							var row_count = eval(groupName+"_count");
							for (var i=0; i<row_count; i++) {
								document.getElementById(groupName+"_"+i).className = styleClass;
							}
							
							if (styleClass == "show") {
								document.getElementById(groupName+'_nav').innerHTML = "<a href=\"javascript:;\" onClick=\"hideShow('"+groupName+"', 'hide')\">Hide Ordered Details</a>";
								SetCookie(groupName, '1');
							} else {
								document.getElementById(groupName+'_nav').innerHTML = "<a href=\"javascript:;\" onClick=\"hideShow('"+groupName+"', 'show')\">Show Ordered Details</a>";
								SetCookie(groupName, '0');
							}
						}
					//-->
					</script>
<?
} else {
?>
					<tr>
						<td>
							<table border="0" width="100%" cellspacing="0" cellpadding="0">
								<tr>
									<td class="pageHeading" valign="top"><?=HEADING_INPUT_TITLE?></td>
								</tr>
							</table>
						</td>
					</tr>
					<tr>
        				<td>
        					<table border="0" width="100%" cellspacing="0" cellpadding="0">
        						<tr>
        							<td width="20%">&nbsp;</td>
        							<td>
<?	echo tep_draw_form('payments_criteria', FILENAME_SUPPLIER_PAYMENT, tep_get_all_get_params(array('action')) . 'action=show_report', 'post', ''); ?>
        								<table border="0" width="100%" cellspacing="2" cellpadding="0">
<?
	$show_options = array 	(	array ('id' => '10', "text" => "10"),
								array ('id' => '20', "text" => "20"),
								array ('id' => '50', "text" => "50"),
								array ('id' => 'ALL', "text" => TEXT_ALL_PAGES)
							);
?>
											<tr>
												<td class="main" width="12%"><?=ENTRY_ORDER_START_DATE?></td>
								    			<td class="main">
								    				<table border="0" cellspacing="2" cellpadding="0">
								    					<tr>
								    						<td class="main" valign="top" nowrap>
								    							<script language="javascript"><!--
								  									var date_start_date = new ctlSpiffyCalendarBox("date_start_date", "payments_criteria", "start_date", "btnDate_start_date", "", scBTNMODE_CUSTOMBLUE);
								  									date_start_date.writeControl(); date_start_date.dateFormat="yyyy-MM-dd"; document.getElementById('start_date').value='<?=$_SESSION['payment_inputs']["start_date"]?>';
																//--></script>
								    						</td>
								    						<td class="main" width="5%">&nbsp;</td>
								    						<td class="main" valign="top" nowrap><?=ENTRY_ORDER_END_DATE?></td>
								    						<td class="main" width="1%">&nbsp;</td>
								    						<td class="main" valign="top" nowrap>
								    							<script language="javascript"><!--
								  									var date_end_date = new ctlSpiffyCalendarBox("date_end_date", "payments_criteria", "end_date", "btnDate_end_date", "", scBTNMODE_CUSTOMBLUE);
								  									date_end_date.writeControl(); date_end_date.dateFormat="yyyy-MM-dd"; document.getElementById('end_date').value='<?=$_SESSION['payment_inputs']["end_date"]?>';
																//--></script>
								    						</td>
								    					</tr>
								    				</table>
								    			</td>
											</tr>
											<tr>
			            						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			          						</tr>
			          						<tr>
												<td class="main"><?=ENTRY_PAYMENT_ID?></td>
								    			<td class="main"><?=tep_draw_input_field('payment_id', $_SESSION['payment_inputs']["payment_id"], ' id="payment_id" size="15" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) ) { this.value = \'\'; } else { resetControls(this); }" onBlur="if (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value)) ) { this.value = \'\'; } else { resetControls(this); }" onKeyPress="return noEnterKey(event)"')?></td>
											</tr>
											<tr>
			            						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			          						</tr>
											<tr>
												<td class="main"><?=ENTRY_ORDER_ID?></td>
								    			<td class="main"><?=tep_draw_input_field('order_id', $_SESSION['payment_inputs']["order_id"], ' id="order_id" size="15" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) ) { this.value = \'\'; } else { resetControls(this); }" onBlur="if (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value)) ) { this.value = \'\'; } else { resetControls(this); }" onKeyPress="return noEnterKey(event)"')?></td>
											</tr>
											<tr>
			            						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			          						</tr>
			          						<tr>
												<td class="main"><?=ENTRY_RECORDS_PER_PAGE?></td>
								    			<td class="main">
								    				<?=tep_draw_pull_down_menu("show_records", $show_options, tep_not_null($_SESSION['payment_inputs']["show_records"]) ? $_SESSION['payment_inputs']["show_records"] : '', '') . TEXT_LANG_RECORDS_PER_PAGE?>
								    			</td>
											</tr>
											<tr>
			            						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '15')?></td>
			          						</tr>
			          						<tr>
			          							<td>&nbsp;</td>
				  								<td>
				  									<?=tep_submit_button(BUTTON_SEARCH, ALT_BUTTON_SEARCH, 'onClick="return form_checking(this.form, \'do_search\');"', 'inputButton')?>
				  									<?=tep_button(BUTTON_RESET, ALT_BUTTON_RESET, tep_href_link(FILENAME_SUPPLIER_PAYMENT, 'action=reset_session'), '', 'inputButton')?>
				  								</td>
				  							</tr>
			        					</table>
			        					</form>
			        				</td>
			        			</tr>
			        		</table>
			        	</td>
			        </tr>
					<script language="javascript"><!--
						function form_checking(form_obj, action) {
						    //form_obj.submit();
							return true;
			    		}
			    		
						function resetControls(controlObj) {
							if (trim_str(controlObj.value) != '') {
								if (controlObj.id == 'payment_id') {
									document.payments_criteria.order_id.value = '';
								} else {
									document.payments_criteria.payment_id.value = '';
								}
								document.payments_criteria.start_date.value = '';
								document.payments_criteria.end_date.value = '';
								document.payments_criteria.show_records.selectedIndex = 0;
				    		} else {
				    			controlObj.value = '';
				    		}
						}
			    	//-->
					</script>
<?
}
?>
					<script language="javascript">
					<!--
						function showOverEffect(object, class_name, extra_row) {
							rowOverEffect(object, class_name);
							var rowObjArray = extra_row.split('##');
	  						for (var i = 0; i < rowObjArray.length; i++) {
	  							if (typeof (document.getElementById(rowObjArray[i])) != 'undefined' && document.getElementById(rowObjArray[i]) != null) {
	  								rowOverEffect(document.getElementById(rowObjArray[i]), class_name);
	  							}
	  						}
						}
						
						function showOutEffect(object, class_name, extra_row) {
							rowOutEffect(object, class_name);
							var rowObjArray = extra_row.split('##');
					  		for (var i = 0; i < rowObjArray.length; i++) {
					  			if (typeof (document.getElementById(rowObjArray[i])) != 'undefined' && document.getElementById(rowObjArray[i]) != null) {
					  				rowOutEffect(document.getElementById(rowObjArray[i]), class_name);
					  			}
					  		}
						}
						
						function showClicked(object, class_name, extra_row) {
							rowClicked(object, class_name);
							
							var rowObjArray = extra_row.split('##');
					  		for (var i = 0; i < rowObjArray.length; i++) {
					  			if (typeof (document.getElementById(rowObjArray[i])) != 'undefined' && document.getElementById(rowObjArray[i]) != null) {
					  				rowClicked(document.getElementById(rowObjArray[i]), class_name);
					  			}
	  						}
						}
					//-->
					</script>
				</table>
<!-- body_text_eof //-->
  			</td>
  		</tr>
	</table>
<!-- body_eof //-->
<br>
<!-- footer //-->
<? require(DIR_WS_INCLUDES . 'footer.php'); ?>
<!-- footer_eof //-->
<? require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>
</body>
</html>