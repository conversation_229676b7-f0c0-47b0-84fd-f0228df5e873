<?
/*
  	$Id: supplier_pm_book.php,v 1.1 2007/01/25 10:44:45 weichen Exp $
	
	Developer: <PERSON>
  	Copyright (c) 2007 SKC Ventrue
	
  	Released under the GNU General Public License
*/

require('includes/application_top.php');
require(DIR_WS_CLASSES . 'payment_module_info.php');

$pm_object = new payment_module_info($supplier_id, 'supplier');

$action = (isset($_REQUEST['action']) ? $_REQUEST['action'] : '');
$subaction = (isset($_REQUEST['subaction']) ? $_REQUEST['subaction'] : '');


switch($subaction) {
	case "insert_pm":
		$subaction_res = $pm_object->insert_payment_account($HTTP_POST_VARS, $messageStack);
		
		if ($subaction_res) {
			tep_redirect(tep_href_link(FILENAME_SUPPLIER_PAYMENT_BOOK, tep_get_all_get_params(array('action', 'subaction')) . 'page=' . $_GET['page']));
		} else {
			;
		}
		break;
	case "update_pm":
		$subaction_res = $pm_object->update_payment_account($HTTP_POST_VARS, $messageStack);
		
		if ($subaction_res) {
			tep_redirect(tep_href_link(FILENAME_SUPPLIER_PAYMENT_BOOK, tep_get_all_get_params(array('action', 'subaction')) . 'page=' . $_GET['page']));
		} else {
			;
		}
		break;
	case "confirm_delete_pm":
		$pm_object->delete_payment_account($HTTP_GET_VARS['book_id'], $messageStack);
		
		tep_redirect(tep_href_link(FILENAME_SUPPLIER_PAYMENT_BOOK, tep_get_all_get_params(array('action', 'subaction', 'book_id')) . 'page=' . $_GET['page']));
		
		break;
	default:
		break;
}

switch($action) {
	case "add_pm":
		$header_title = HEADER_FORM_PM_NEW_PM_TITLE;
		$form_content = $pm_object->add_payment_account(FILENAME_SUPPLIER_PAYMENT_BOOK);
		
		break;
	case "edit_pm":
		$header_title = HEADER_FORM_PM_EDIT_PM_TITLE;
		$form_content = $pm_object->add_payment_account(FILENAME_SUPPLIER_PAYMENT_BOOK, $HTTP_GET_VARS['book_id']);
		
		break;
	default:
		$header_title = HEADER_FORM_PM_BOOK_TITLE;
		$form_content = $pm_object->list_payment_account_book(FILENAME_SUPPLIER_PAYMENT_BOOK);
		
		break;
}
?>
<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?=HTML_PARAMS?>>
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=<?php echo CHARSET; ?>">
	<title><?=TITLE?></title>
<?
if (file_exists(DIR_WS_LANGUAGES . $sup_language . '/stylesheet.css')) {
	echo '<link rel="stylesheet" type="text/css" href="'. DIR_WS_LANGUAGES . $sup_language . '/stylesheet.css">';
} else {
	echo '<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">';
}

if (file_exists(DIR_WS_INCLUDES . 'javascript/payment_xmlhttp.js.php')) { include_once (DIR_WS_INCLUDES . 'javascript/payment_xmlhttp.js.php'); }
?>
	<script language="javascript" src="<?=DIR_WS_INCLUDES?>general.js"></script>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">
<!-- header //-->
<? require(DIR_WS_INCLUDES . 'header.php'); ?>
<!-- header_eof //-->
	<table border="0" width="733" height="100%" align="center" cellpadding="0" cellspacing="2" valign="middle">
  		<tr>
  			<td valign="top" height="30px">
  		  		<table width="100%" border="0" align="center" cellspacing="0" cellpadding="2">
           			<tr>
			    		<td width="62"><p>&nbsp;</p></td>
			  		    <td width="556" class="pageHeading"><b><?=$header_title?></b></td>
			  		    <td width="40" valign="bottom"></td>
			  		    <td width="63" class="login_heading">&nbsp;</td>
			  		</tr>
			  	</table>
			</td>
  		</tr>
  		<tr>
  		  	<td valign="top"><?=$form_content?></td>
		</tr>
		<tr>
			<td valign="bottom" height="120px"><? require(DIR_WS_INCLUDES . 'footer.php'); ?></td>
		</tr>
	</table>
</body>
</html>