<?
/*
  	$Id: supplier_progress_report.php,v 1.47 2008/05/09 06:55:41 weichen Exp $
	
	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
  	
  	Copyright (c) 2002 osCommerce
	
	Released under the GNU General Public License
*/

require('includes/application_top.php');
require('includes/functions/progress_report_task.php');
require(DIR_WS_CLASSES . 'custom_product_payment.php');


define('FILE_REDJACK_EN', 'RedJack_en_v.zip');
define('FILE_REDJACK_CN', 'RedJack_cn_v.zip');
define('FILE_WOW_GUIDE', 'wow_guide.zip');

$currencies = new currencies();
$currencies->set_decimal_places(3);

$orders_product_id = $_REQUEST["orders_product_id"];
$action = (isset($HTTP_GET_VARS['action']) ? $HTTP_GET_VARS['action'] : '');

$status_name = array();

$supplier_tasks_status_select_sql = "SELECT supplier_tasks_status_id, supplier_tasks_status_name FROM " . TABLE_SUPPLIER_TASKS_STATUS . " WHERE language_id='" . (int)$sup_languages_id . "' ORDER BY supplier_tasks_status_sort_order";
$supplier_tasks_status_result_sql = tep_db_query($supplier_tasks_status_select_sql);
while ($supplier_tasks_status_row = tep_db_fetch_array($supplier_tasks_status_result_sql)) {
	$status_name[$supplier_tasks_status_row['supplier_tasks_status_id']] = $supplier_tasks_status_row['supplier_tasks_status_name'];
}

if (tep_not_null($action)) {
	if (isset($HTTP_POST_VARS["search"])) {
		$_SESSION['custom_product_param']["start_date"] = tep_db_prepare_input($HTTP_POST_VARS["start_date"]);
		$_SESSION['custom_product_param']["end_date"] = tep_db_prepare_input($HTTP_POST_VARS["end_date"]);
		$_SESSION['custom_product_param']["custom_product_order_id"] = tep_db_prepare_input($HTTP_POST_VARS["custom_product_order_id"]);
		$_SESSION['custom_product_param']["custom_product_product_name"] = tep_db_prepare_input($HTTP_POST_VARS["custom_product_product_name"]);
	
	} else if (isset($HTTP_POST_VARS["reset"])) {
		unset($_SESSION['custom_product_param']);
        tep_redirect(tep_href_link(FILENAME_SUPPLIER_PROGRESS_REPORT, 'action=advanced_search'));
        break;
	} else if (isset($HTTP_POST_VARS["post_comment"])) {
		foreach($HTTP_POST_VARS["post_comment"] as $key => $value) {
			$status_update = false;
			if (isset($HTTP_POST_VARS["current_level"])) {
				$supplier_tasks_allocation_progress_select_sql = "SELECT supplier_tasks_allocation_progress FROM " . TABLE_SUPPLIER_TASKS_ALLOCATION . " WHERE orders_products_id ='" . tep_db_input($key) . "'";
				$supplier_tasks_allocation_progress_result_sql = tep_db_query($supplier_tasks_allocation_progress_select_sql);
				$supplier_tasks_allocation_progress_row = tep_db_fetch_array($supplier_tasks_allocation_progress_result_sql);
				
				if ($HTTP_POST_VARS["current_level"][$key] != $supplier_tasks_allocation_progress_row["supplier_tasks_allocation_progress"]) {
					$update_current_level = "UPDATE " . TABLE_SUPPLIER_TASKS_ALLOCATION . " SET supplier_tasks_allocation_progress = '" . tep_db_input($HTTP_POST_VARS["current_level"][$key]) . "' WHERE orders_products_id ='" . (int)$key . "'";
					tep_db_query($update_current_level);
					$comment = $supplier_tasks_allocation_progress_row["supplier_tasks_allocation_progress"] . ' --> ' . $HTTP_POST_VARS["current_level"][$key];
				}
			}
			
			$supplier_tasks_status_select_sql = "SELECT supplier_tasks_status FROM " . TABLE_SUPPLIER_TASKS_ALLOCATION . " WHERE orders_products_id ='" . (int)$key . "'";
			$supplier_tasks_status_result_sql = tep_db_query($supplier_tasks_status_select_sql);
			$supplier_tasks_status_row = tep_db_fetch_array($supplier_tasks_status_result_sql);
			
			if ($HTTP_POST_VARS["task_status"][$key] != 0) {
				if ($HTTP_POST_VARS["task_status"][$key] != $supplier_tasks_status_row['supplier_tasks_status']) {
					$status = $HTTP_POST_VARS["task_status"][$key];
					$update_current_status_sql = "UPDATE " . TABLE_SUPPLIER_TASKS_ALLOCATION . " SET supplier_tasks_status ='" . tep_db_input($status) . "' WHERE orders_products_id ='" . (int)$key . "'";
					tep_db_query($update_current_status_sql);
					
					if ($HTTP_POST_VARS["task_status"][$key] == 2 || $HTTP_POST_VARS["task_status"][$key] == 3 && $supplier_tasks_status_row["supplier_tasks_status"] != $HTTP_POST_VARS["task_status"][$key]) {
						$time_diff_array = tep_get_time_diff($key);
						$time_diff_min = $time_diff_array['minute_format'];
						$tasks_time_taken = $HTTP_POST_VARS["tasks_time_taken"][$key] + $time_diff_min;
						
						$update_supplier_tasks_time_taken_sql = "UPDATE " . TABLE_SUPPLIER_TASKS_ALLOCATION . " SET supplier_tasks_time_taken ='" . tep_db_input($tasks_time_taken) . "' WHERE orders_products_id ='" . (int)$key . "'";
						tep_db_query($update_supplier_tasks_time_taken_sql);
						$status_update = true;
					} else if ($HTTP_POST_VARS["task_status"][$key] == 1 && $supplier_tasks_status_row["supplier_tasks_status"] != $HTTP_POST_VARS["task_status"][$key]) {
						$update_supplier_tasks_time_reference_sql = "UPDATE " . TABLE_SUPPLIER_TASKS_ALLOCATION . " SET supplier_tasks_time_reference = now() WHERE orders_products_id ='" . (int)$key . "'";
						tep_db_query($update_supplier_tasks_time_reference_sql);
						$status_update = true;
					}
					
					tep_update_record_tags(FILENAME_SUPPLIER_PROGRESS_REPORT, $key, $HTTP_POST_VARS["task_status"][$key], "");
				}
			}
			
			if (isset($HTTP_POST_VARS["status_done"])) {
				$status_update = true;
				$status = 3;
				
				$time_diff_array = tep_get_time_diff($key);
				$time_diff_min = $time_diff_array['minute_format'];
				$tasks_time_taken = $HTTP_POST_VARS["tasks_time_taken"][$key] + $time_diff_min;
				$update_task = "UPDATE " . TABLE_SUPPLIER_TASKS_ALLOCATION . " SET supplier_tasks_status = 3, supplier_tasks_time_taken = '" . tep_db_prepare_input($tasks_time_taken) . "' WHERE orders_products_id ='" . (int)$key . "'";
				tep_db_query($update_task);
				
				tep_update_record_tags(FILENAME_SUPPLIER_PROGRESS_REPORT, $key, $status, "");
			}
			
			if (tep_not_null($comment)) {
				$comment .= "\n\n" . $HTTP_POST_VARS["comments"][$key];
			} else {
				$comment = $HTTP_POST_VARS["comments"][$key];
			}
			
			if ($status_update == true || tep_not_null($comment)) {
				$sql_data_array = array('orders_products_id' => tep_db_prepare_input($key),
										'supplier_tasks_status' => ($status_update == true ? tep_db_input($status) : 'NULL'),
										'date_added' => 'now()',
										'comments' => (tep_not_null($comment) ? tep_db_prepare_input($comment) : 'NULL'),
										'changed_by' => tep_db_prepare_input($login_email_address),
										'user_role' => 'supplier',
										'notify_recipient' => (tep_not_null($HTTP_POST_VARS["comments"][$key]) ? 1 : 0)
										);
				
				tep_db_perform(TABLE_SUPPLIER_TASKS_ALLOCATION_HISTORY, $sql_data_array);
			}
			
			if ($status_update)	tep_status_update_notification('PWL', $key, $login_email_address, $supplier_tasks_status_row['supplier_tasks_status'], $HTTP_POST_VARS["task_status"][$key], 'A', tep_db_prepare_input($comment));
		}
		
		tep_redirect(tep_href_link(FILENAME_SUPPLIER_PROGRESS_REPORT, tep_get_all_get_params(array('action')).'action=report'));
		break;
	} else if (isset($HTTP_POST_VARS["batch_update"])) {
		if (isset($HTTP_POST_VARS["current_level"])) {
			foreach($HTTP_POST_VARS["current_level"] as $key => $value) {
				$supplier_tasks_allocation_progress_select_sql = "SELECT supplier_tasks_allocation_progress FROM " . TABLE_SUPPLIER_TASKS_ALLOCATION . " WHERE orders_products_id ='" . tep_db_input($key) . "'";
				$supplier_tasks_allocation_progress_result_sql = tep_db_query($supplier_tasks_allocation_progress_select_sql);
				$supplier_tasks_allocation_progress_row = tep_db_fetch_array($supplier_tasks_allocation_progress_result_sql);
				
				if ($HTTP_POST_VARS["current_level"][$key] != $supplier_tasks_allocation_progress_row["supplier_tasks_allocation_progress"]) {
					$update_current_level = "UPDATE " . TABLE_SUPPLIER_TASKS_ALLOCATION . " SET supplier_tasks_allocation_progress = '" . tep_db_input($HTTP_POST_VARS["current_level"][$key]) . "' WHERE orders_products_id ='" . (int)$key . "'";
					tep_db_query($update_current_level);
					$comment = $supplier_tasks_allocation_progress_row["supplier_tasks_allocation_progress"] . ' --> ' . $HTTP_POST_VARS["current_level"][$key];
				
					$sql_data_array = array('orders_products_id' => tep_db_prepare_input($key),
											'supplier_tasks_status' => 'NULL',
											'date_added' => 'now()',
											'comments' => (tep_not_null($comment) ? tep_db_prepare_input($comment) : 'NULL'),
											'changed_by' => tep_db_prepare_input($login_email_address),
											'user_role' => 'supplier',
											'notify_recipient' => 0
											);
			
					tep_db_perform(TABLE_SUPPLIER_TASKS_ALLOCATION_HISTORY, $sql_data_array);
				}
			}
		}
		
		if (isset($HTTP_POST_VARS["assign_to"])) {
			foreach($HTTP_POST_VARS["assign_to"] as $key => $value) {
				$update_supplier_crew_id_sql = "UPDATE " . TABLE_SUPPLIER_TASKS_ALLOCATION . " SET supplier_crew_id ='" . tep_db_input($HTTP_POST_VARS["assign_to"][$key]) . "' WHERE orders_products_id ='" . tep_db_input($key) . "'";
				tep_db_query($update_supplier_crew_id_sql);
			}
		}
		tep_redirect(tep_href_link(FILENAME_SUPPLIER_PROGRESS_REPORT, tep_get_all_get_params(array('action')).'action=search'));
	}
}
?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html <?=HTML_PARAMS?>>
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=<?php echo CHARSET; ?>">
	<title><?=TITLE?></title>

<?
if (file_exists(DIR_WS_LANGUAGES . $sup_language . '/stylesheet.css')) {
	echo '<link rel="stylesheet" type="text/css" href="'. DIR_WS_LANGUAGES . $sup_language . '/stylesheet.css">';
} else {
	echo '<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">';
}
?>
	<link rel="stylesheet" type="text/css" href="includes/javascript/spiffyCal/spiffyCal_v2_1.css">
<?
	if ($_REQUEST['action'] == 'char_info') {
		if (ENABLE_SSL == 'true') {
        	$path = HTTPS_CATALOG_SERVER . DIR_WS_CATALOG . DIR_WS_INTERFACE;
      	} else {
        	$path = HTTP_CATALOG_SERVER . DIR_WS_CATALOG . DIR_WS_INTERFACE;
      	}
?>
	<link rel="Stylesheet" href="includes/wow_stylesheet.php?path=<?=$path?>" type="text/css" media="Screen" />
<? } ?>
	<script language="javascript" src="includes/general.js"></script>
	<script language="JavaScript" src="includes/javascript/spiffyCal/spiffyCal_v2_1.js"></script>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">
<div id="spiffycalendar" class="text"></div>
<div id="dhtmlTooltip"></div>
		<script language="javascript" src="<?=DIR_WS_INCLUDES?>javascript/dhtml_tooltip.js"></script>
<!-- header //-->
<? require(DIR_WS_INCLUDES . 'header.php'); ?>
<!-- header_eof //-->
<table width="100%" border="0" cellspacing="0" cellpadding="2">
	<tr>
		<td>
<?	if ($_REQUEST['action'] == 'search') { ?>
			<table border="0" width="82%" cellspacing="0" cellpadding="2" style="border-collapse: collapse;" align="center">
				<tr>
					<td><span class="pageHeading"><?=sprintf(HEADING_INPUT_TITLE, HEADING_LIST_CRITERIA)?></span></td>
				</tr>
<?
		if (tep_not_null($_REQUEST['searchby'])) {
			$show_order_status[] = $_REQUEST['searchby'];
			
			$order_id_where = " 1 ";
			$product_name_where = " 1 ";
			$start_date_str = " 1 ";
			$end_date_str = " 1 ";
		} else {
			if (tep_not_null($_SESSION['custom_product_param']["custom_product_order_id"])) {
				$order_id_where = " o.orders_id ='" . tep_db_input($_SESSION['custom_product_param']["custom_product_order_id"]) . "'";
			} else {
				$order_id_where = " 1 ";
			}
			
			if (tep_not_null($_SESSION['custom_product_param']["custom_product_product_name"])) {
				$product_name_where = " op.products_name LIKE '%" . tep_db_input($_SESSION['custom_product_param']["custom_product_product_name"]) . "%'";
			} else {
				$product_name_where = " 1 ";
			}
			
			if (tep_not_null($_SESSION['custom_product_param']["start_date"])) {
				if (strpos($_SESSION['custom_product_param']["start_date"], ':') !== false) {
					list($yr, $mth, $day) = explode('-', $startDateObj[0]);
					list($hr, $min) = explode(':', $startDateObj[1]);
					$start_date_str = " ( DATE_FORMAT(sta.supplier_tasks_start_time, '%Y-%m-%d %H:%i:%s') >= DATE_FORMAT('".date("Y-m-d H:i:s", mktime((int)$hr, (int)$min, 0, $mth, $day, $yr))."', '%Y-%m-%d %H:%i:%s') )";
				} else {
					list($yr, $mth, $day) = explode('-', trim($_SESSION['custom_product_param']["start_date"]));
					$start_date_str = " ( DATE_FORMAT(sta.supplier_tasks_start_time, '%Y-%m-%d') >= DATE_FORMAT('".date("Y-m-d", mktime(0,0,0,$mth,$day,$yr))."','%Y-%m-%d') )";
				}
			} else {
				$start_date_str = " 1 ";
			}
			
			if ($_SESSION['custom_product_param']["custom_product_end_date"]) {
				if (strpos($_SESSION['custom_product_param']["end_date"], ':') !== false) {
					$endDateObj = explode(' ', trim($_SESSION['custom_product_param']["end_date"]));
					list($yr, $mth, $day) = explode('-', $endDateObj[0]);
					list($hr, $min) = explode(':', $endDateObj[1]);
					$end_date_str = " ( DATE_FORMAT(sta.supplier_tasks_start_time, '%Y-%m-%d %H:%i:%s') <= DATE_FORMAT('".date("Y-m-d H:i:s", mktime((int)$hr, (int)$min, 0, $mth, $day, $yr))."', '%Y-%m-%d %H:%i:%s') )";
				} else {
					list($yr, $mth, $day) = explode('-', trim($_SESSION['custom_product_param']["end_date"]));
					$end_date_str = " ( DATE_FORMAT(sta.supplier_tasks_start_time, '%Y-%m-%d') <= DATE_FORMAT('".date("Y-m-d", mktime(0,0,0,$mth,$day,$yr))."','%Y-%m-%d') )";
				}
			} else {
				$end_date_str = " 1 ";
			}
			
			$auto_orders_products_id_select_sql = " SELECT DISTINCT(sta.supplier_tasks_status) 
													FROM " . TABLE_ORDERS . " AS o 
													INNER JOIN " . TABLE_ORDERS_PRODUCTS . " AS op 
														ON (o.orders_id=op.orders_id AND op.custom_products_type_id='1') 
													INNER JOIN " . TABLE_ORDERS_CUSTOM_PRODUCTS . " AS ocp 
														ON (op.orders_products_id=ocp.orders_products_id AND ocp.orders_custom_products_key='power_leveling_info') 
													INNER JOIN " . TABLE_SUPPLIER_TASKS_ALLOCATION . " AS sta 
														ON (op.orders_products_id=sta.orders_products_id AND sta.suppliers_id !=0) 
													LEFT JOIN " . TABLE_ORDERS_CUSTOM_PRODUCTS . " AS ocpB 
														ON (sta.orders_products_id=ocpB.orders_products_id AND ocpB.orders_custom_products_key='power_leveling_bracket_info') 
													WHERE 1 
														AND sta.suppliers_id='" . (int)$supplier_id . "' 
														AND $start_date_str 
														AND $end_date_str 
														AND $order_id_where 
														AND $product_name_where 
													ORDER BY supplier_tasks_status ";
		
			$auto_orders_products_id_result_sql = tep_db_query($auto_orders_products_id_select_sql);
			
			while ($auto_orders_products_id_row = tep_db_fetch_array($auto_orders_products_id_result_sql)) {
				$show_order_status[] = $auto_orders_products_id_row["supplier_tasks_status"];
			}
		}
		
		for ($status = 0; $status < count($show_order_status); $status++) {
			$form_name = 'progress_report_'.$show_order_status[$status].'_lists_form';
			
			$orders_orders_id_select_sql = "	SELECT distinct(o.orders_id) 
												FROM " . TABLE_ORDERS . " AS o 
												INNER JOIN " . TABLE_ORDERS_PRODUCTS . " AS op 
													ON (o.orders_id=op.orders_id AND op.custom_products_type_id='1') 
												INNER JOIN " . TABLE_ORDERS_CUSTOM_PRODUCTS . " AS ocp 
													ON (op.orders_products_id=ocp.orders_products_id AND ocp.orders_custom_products_key='power_leveling_info') 
												INNER JOIN " . TABLE_SUPPLIER_TASKS_ALLOCATION . " AS sta 
													ON (op.orders_products_id=sta.orders_products_id AND sta.suppliers_id !=0) 
												LEFT JOIN " . TABLE_ORDERS_CUSTOM_PRODUCTS . " AS ocpB 
													ON (sta.orders_products_id=ocpB.orders_products_id AND ocpB.orders_custom_products_key='power_leveling_bracket_info') 
												WHERE 1 
													AND sta.supplier_tasks_status ='" . (int)$show_order_status[$status] . "' 
													AND sta.suppliers_id='" . (int)$supplier_id . "' 
													AND $start_date_str 
													AND $end_date_str 
													AND $order_id_where 
													AND $product_name_where ";
			
			if ($show_order_status[$status] == 4 && tep_not_null($_REQUEST['bill'])) {
				$orders_orders_id_select_sql .= " AND supplier_tasks_billing_status ='" . (int)$_REQUEST['bill'] . "'";
			}
			
			$orders_orders_id_select_sql .= "ORDER BY o.orders_id DESC";
			
			if ($show_records != "ALL") {
				$orders_split_object = new splitPageResults($HTTP_GET_VARS['page'.$show_order_status[$status]], ($show_records=='' || $show_records=='DEFAULT' ? MAX_DISPLAY_SEARCH_RESULTS : (int) $show_records), $orders_orders_id_select_sql, $orders_select_sql_numrows, true);
			}
			
			$orders_orders_id_result_sql = tep_db_query($orders_orders_id_select_sql);
?>
				<tr>
					<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
				</tr>
				<tr>
					<td><span class="pageHeading"><?=$status_name[$show_order_status[$status]]?></span></td>
				</tr>
				<tr>
					<td>
<?						echo tep_draw_form($form_name, FILENAME_SUPPLIER_PROGRESS_REPORT, tep_get_all_get_params(array('action')) . 'action=batch_action', 'post');?>
							<table border="0" width="100%" cellspacing="1" cellpadding="2">
								<tr>
									<td class="ordersBoxHeading" width="8%"><?=ENTRY_HEADING_ORDER_ID?></td>
									<td class="ordersBoxHeading">
										<table border="0" width="100%" cellspacing="0" cellpadding="0" style="border-collapse: collapse;">
											<tr>
												<td width="21px" class="ordersBoxHeading"><?=TABLE_HEADING_NUMBER?></td>
												<td width="130px" class="ordersBoxHeading"><?=TABLE_HEADING_SUBMITTED_DATE?></td>
												<td width="140px" class="ordersBoxHeading"><?=TABLE_HEADING_PRODUCT_NAME?></td>
												<td width="71px" class="ordersBoxHeading" align="right"><?=TABLE_HEADING_TOTAL?></td>
<?							if ($show_order_status[$status] == 4) { ?>
												<td width="90px" class="ordersBoxHeading" align="center"><?=TABLE_HEADING_CP_BILLING_STATUS?></td>
<?							} ?>
												<td width="122px" class="ordersBoxHeading" align="center"><?=TABLE_HEADING_PROGRESS_STATUS?></td>
<?							if ($show_order_status[$status] != 4) { ?>
												<td width="65px" class="ordersBoxHeading" align="center"><?=TABLE_HEADING_LAST_REPLY?></td>
<?								if ($show_order_status[$status] == 1) { ?>
												<td width="68px" class="ordersBoxHeading" align="left"><?=TEXT_CURRENT_LEVEL?></td>
<?								} ?>
												
<?							} else { ?>
												<td width="71px" class="ordersBoxHeading" align="right"><?=TABLE_HEADING_AMOUNT_ADJUST?></td>
												<td width="71px" class="ordersBoxHeading" align="right"><?=TABLE_HEADING_TOTAL_PAYABLE?></td>
<?							} ?>
												<td width="125px" class="ordersBoxHeading" align="center"><?=TABLE_HEADING_ASSIGNED_TO?></td>
												<td width="80px" class="ordersBoxHeading" align="center"><?=TABLE_HEADING_ORDER_CODE?></td>
												<td width="40px" class="ordersBoxHeading" align="center"><?=TABLE_HEADING_ACTION?></td>
											</tr>
										</table>
									</td>
								</tr>
<?
							while ($orders_orders_id_row = tep_db_fetch_array($orders_orders_id_result_sql)) {
								if (tep_not_null($orders_orders_id_row["orders_id"])) {
									$orders_id_where = " o.orders_id ='" . (int)$orders_orders_id_row["orders_id"] . "'";
								} else {
									$orders_id_where = " 1 ";
								}
								
								$orders_orders_info_select_sql = "	SELECT IF(op.orders_products_store_price IS NULL, (((op.final_price * sta.supplier_tasks_allocation_ratio_adjust) / 100) + sta.supplier_payable_adjust), (((op.orders_products_store_price * sta.supplier_tasks_allocation_ratio_adjust) / 100) + sta.supplier_payable_adjust)) AS payable_price, sta.suppliers_id, SEC_TO_TIME( (TO_DAYS(now())*24*3600+TIME_TO_SEC(now())) - (TO_DAYS(sta.supplier_tasks_time_reference)*24*3600+TIME_TO_SEC(sta.supplier_tasks_time_reference)) ) AS hours, sta.orders_products_id, sta.supplier_tasks_start_time, sta.supplier_tasks_time_reference, sta.supplier_tasks_time_taken, sta.orders_tag_ids, sta.supplier_tasks_billing_status, s.supplier_code, op.final_price, op.orders_products_store_price, ocpB.orders_custom_products_value AS bracket_info, ocp.orders_custom_products_value AS task_info, ocp.orders_custom_products_number, sta.supplier_tasks_start_time, sta.supplier_tasks_allocation_info, sta.supplier_tasks_allocation_progress, sta.supplier_tasks_status, sta.supplier_payable_adjust, sta.supplier_crew_id, o.orders_id, o.customers_id, o.customers_email_address, o.currency, o.currency_value, op.products_name, op.products_id, sc.supplier_crew_firstname, sc.supplier_crew_lastname 
																	FROM " . TABLE_ORDERS . " AS o 
																	INNER JOIN " . TABLE_ORDERS_STATUS . " AS os 
																		ON (o.orders_status=os.orders_status_id) 
																	INNER JOIN " . TABLE_ORDERS_PRODUCTS . " AS op 
																		ON (o.orders_id=op.orders_id AND op.custom_products_type_id = 1) 
																	INNER JOIN " . TABLE_ORDERS_CUSTOM_PRODUCTS . " AS ocp 
																		ON (op.orders_products_id=ocp.orders_products_id AND ocp.orders_custom_products_key='power_leveling_info') 
																	INNER JOIN " . TABLE_SUPPLIER_TASKS_ALLOCATION . " AS sta 
																		ON (op.orders_products_id=sta.orders_products_id) 
																	LEFT JOIN " . TABLE_SUPPLIER . " AS s 
																		ON (sta.suppliers_id=s.supplier_id) 
																	LEFT JOIN " . TABLE_ORDERS_CUSTOM_PRODUCTS . " AS ocpB 
																		ON (sta.orders_products_id=ocpB.orders_products_id AND ocpB.orders_custom_products_key='power_leveling_bracket_info') 
																	LEFT JOIN " . TABLE_SUPPLIER_CREW . " AS sc 
																		ON (sta.supplier_crew_id = sc.supplier_crew_id) 
																	WHERE sta.suppliers_id ='" . (int)$supplier_id . "' AND sta.supplier_tasks_status ='" . (int)$show_order_status[$status] . "' AND $orders_id_where ORDER BY orders_custom_products_number ";
																
								$orders_orders_info_result_sql = tep_db_query($orders_orders_info_select_sql);

?>
								<tr class="ordersListingEven">
									<td class="ordersRecords" valign="top"><?=$orders_orders_id_row["orders_id"]?></td>
									<td class="ordersRecords" valign="top">
										<table border="0" width="100%" cellspacing="0" cellpadding="0" style="border-collapse: collapse;">
<?							
								while ($orders_orders_info_row = tep_db_fetch_array($orders_orders_info_result_sql)) {
									$time_pass = 0;
									$time_array = explode(':', $orders_orders_info_row['hours']);
									
									if (tep_not_null($orders_orders_info_row['bracket_info'])) {
										$orders_bracket_info_array = unserialize($orders_orders_info_row['bracket_info']);
									}
											
									if ($orders_orders_info_row['supplier_tasks_status'] == 2 || $orders_orders_info_row['supplier_tasks_status'] == 3 || $orders_orders_info_row['supplier_tasks_status'] == 4) {
										$time_pass = (int)((int)$orders_orders_info_row['supplier_tasks_time_taken'] / 60);
									} else if ($orders_orders_info_row['supplier_tasks_status'] == 1) {
										$time_pass = (int)((int)$time_array[0] + ((int)$orders_orders_info_row['supplier_tasks_time_taken'] / 60));
									}
									
									$user_role_select_sql = "SELECT user_role FROM " . TABLE_SUPPLIER_TASKS_ALLOCATION_HISTORY . " WHERE orders_products_id = '" . tep_db_input($orders_orders_info_row["orders_products_id"]) . "' AND notify_recipient = 1 ORDER BY date_added desc LIMIT 1";
									$user_role_result_sql = tep_db_query($user_role_select_sql);
									$user_role_row = tep_db_fetch_array($user_role_result_sql);
									
									$row_style = ($row_count%2) ? 'ordersListingEven' : 'ordersListingOdd';
?>
											<tr id="<?=$status.'_main_'.$row_count?>" class="<?=$row_style?>">
												<td width="21px" class="ordersRecords"><?=$orders_orders_info_row['orders_custom_products_number']?></td>
												<td width="130px" class="ordersRecords"><?=$orders_orders_info_row['supplier_tasks_start_time']?></td>
												<td width="140px" class="ordersRecords"><?=$orders_orders_info_row['products_name']?></td>
												<td width="71px" class="ordersRecords" align="right"><?=$currencies->format(tep_not_null($orders_orders_info_row['orders_products_store_price']) ? $orders_orders_info_row['orders_products_store_price'] : $orders_orders_info_row['final_price'], true, $orders_orders_info_row['currency'], $orders_orders_info_row['currency_value'])?></td>
<?									if ($show_order_status[$status] == 4) { ?>
												<td width="90px" class="ordersRecords" align="center">
													<?
														if ($orders_orders_info_row['supplier_tasks_billing_status'] == '0') {
															echo TEXT_NOT_BILLED;
															echo ' ' .tep_image(DIR_WS_IMAGES . 'icon_status_red.gif', TEXT_NOT_BILLED, 10, 10);
														} else {
															echo TEXT_BILLED;
															echo ' ' .tep_image(DIR_WS_IMAGES . 'icon_status_green.gif', TEXT_BILLED, 10, 10);
														}
													?>
												</td>
<?									} ?>
												<td width="122px" class="ordersRecords" align="center"><?=$time_pass . '/' . $orders_orders_info_row['supplier_tasks_allocation_progress']?></td>
<?									if ($show_order_status[$status] != 4) { ?>
												<td width="65px" class="ordersRecords" align="center">
													<?
														if ($user_role_row["user_role"] == 'supplier') {
															echo tep_image(DIR_WS_ICONS."green_arrow.gif");
														} else if ($user_role_row["user_role"] == 'admin') {
															echo tep_image(DIR_WS_ICONS."red_arrow.gif");
														} else if (!tep_not_null($user_role_row["user_role"])) {
															echo '&nbsp;';
														}
													?>
												</td>
<?										if ($show_order_status[$status] == 1) { ?>
												<td width="68px" class="ordersRecords" align="left">
													<?
													if (tep_not_null($orders_orders_info_row['bracket_info'])) {
														if ($orders_bracket_info_array['mode'] == 'continuos') {
															echo tep_draw_input_field('current_level['. $orders_orders_info_row['orders_products_id'] .']', $orders_orders_info_row['supplier_tasks_allocation_progress'], 'size="2"');
														} else if ($orders_bracket_info_array['mode'] == 'discrete') {
															$orders_all_options_array = array();
															for ($orders_all_options = 0; $orders_all_options < sizeof($orders_bracket_info_array['range']) ; $orders_all_options++) {
																if (tep_not_null($orders_bracket_info_array['range'][$orders_all_options]['alias'])) {
																	$orders_all_options_array[] = array('id' => $orders_bracket_info_array['range'][$orders_all_options]['alias'], 'text' => $orders_bracket_info_array['range'][$orders_all_options]['alias']);
																} else {
																	$orders_all_options_array[] = array('id' => $orders_bracket_info_array['range'][$orders_all_options]['level'], 'text' => $orders_bracket_info_array['range'][$orders_all_options]['level']);
																}
															}
															echo tep_draw_pull_down_menu('current_level['.$orders_orders_info_row['orders_products_id'].']', $orders_all_options_array, $orders_orders_info_row['supplier_tasks_allocation_progress']);
														}
													} else {
														echo tep_draw_input_field('current_level['. $orders_orders_info_row['orders_products_id'] .']', $orders_orders_info_row['supplier_tasks_allocation_progress'], 'size="2"');
													}
													?>
												</td>
<?										}
									} else {
										$total_price_pay_for_supplier = $orders_orders_info_row['payable_price'];
										
										$price_adjust = (substr($orders_orders_info_row['supplier_payable_adjust'], 0, 1) == '-' ? substr($orders_orders_info_row['supplier_payable_adjust'], 1) : $orders_orders_info_row['supplier_payable_adjust']);
?>
												<td width="71px" class="ordersRecords" align="right"><?=(substr($orders_orders_info_row['supplier_payable_adjust'], 0, 1) == '-' ? '-' : '') . $currencies->format($price_adjust)?></td>
												<td width="71px" class="ordersRecords" align="right"><?=$currencies->format($total_price_pay_for_supplier)?></td>
<?
									}
									if ($show_order_status[$status] != 0 && $show_order_status[$status] != 1 && $show_order_status[$status] != 2) {
?>
												<td width="125px" class="ordersRecords" align="center"><?=$orders_orders_info_row['supplier_crew_firstname'] . $orders_orders_info_row['supplier_crew_lastname']?></td>
<?
									} else {
										$staff_array = array();
										
										$staff_array[] = array('id' => '', 'text' => TEXT_NOT_SELECTED);
										
										$supplier_crew_info_select_sql = "	SELECT supplier_crew_id, supplier_crew_firstname, supplier_crew_lastname 
																			FROM " . TABLE_SUPPLIER_CREW . " 
																			WHERE supplier_crew_status = 1 AND supplier_id ='" . (int)$supplier_id . "'";
																			
										$supplier_crew_info_result_sql = tep_db_query($supplier_crew_info_select_sql);
										while ($supplier_crew_info_row = tep_db_fetch_array($supplier_crew_info_result_sql)) {
											$staff_array[] = array ('id' => $supplier_crew_info_row['supplier_crew_id'], 'text' => $supplier_crew_info_row['supplier_crew_firstname'] . ' ' .  $supplier_crew_info_row['supplier_crew_lastname']);
										}
?>
												<td width="125px" class="ordersRecords" align="center"><?=tep_draw_pull_down_menu('assign_to['.$orders_orders_info_row['orders_products_id'].']', $staff_array, $orders_orders_info_row["supplier_crew_id"])?></td>
<?									} ?>
												<td width="80px" class="ordersRecords" align="center"><?=$orders_orders_id_row["orders_id"] . '-' . $orders_orders_info_row['orders_custom_products_number']?></td>
												<td width="40px" class="ordersRecords" align="center"><a href="<?=tep_href_link(FILENAME_SUPPLIER_PROGRESS_REPORT, 'orders_product_id=' . $orders_orders_info_row['orders_products_id'] . '&action=report')?>"><?=tep_image(DIR_WS_ICONS."edit.gif", "Edit", "14", "13", 'align="top"')?></a></td>
											</tr>
<?
									$row_count++;
								}
?>
										</table>
									</td>
								</tr>
<?							}
							if ($show_order_status[$status] == 1 || $show_order_status[$status] == 0 || $show_order_status[$status] == 2) { ?>
								<tr>
									<td colspan="2" align="right"><?=tep_submit_button(IMAGE_UPDATE, IMAGE_UPDATE, 'name="batch_update['.$orders_orders_info_row['orders_products_id'].']"', 'inputButton')?></td>
								</tr>
<?							} ?>
							</table>
						</form>
					</td>
				</tr>
				<tr>
					<td colspan="2">
        				<table border="0" width="100%" cellspacing="0" cellpadding="2">
          					<tr>
            					<td class="smallText" valign="top"><?=$show_records == "ALL" ? sprintf(TEXT_DISPLAY_NUMBER_OF_ORDERS, tep_db_num_rows($orders_orders_id_select_sql) > 0 ? "1" : "0", tep_db_num_rows($orders_orders_id_select_sql), tep_db_num_rows($orders_orders_id_select_sql)) : $orders_split_object->display_count($orders_select_sql_numrows, ($show_records=='' || $show_records=='DEFAULT' ? MAX_DISPLAY_SEARCH_RESULTS : (int) $show_records), $HTTP_GET_VARS['page'.$show_order_status[$status]], TEXT_DISPLAY_NUMBER_OF_ORDERS)?></td>
            					<td class="smallText" align="right"><?=$show_records == "ALL" ? "Page 1 of 1" : $orders_split_object->display_links($orders_select_sql_numrows, ($show_records=='' || $show_records=='DEFAULT' ? MAX_DISPLAY_SEARCH_RESULTS : (int) $show_records), MAX_DISPLAY_PAGE_LINKS, $HTTP_GET_VARS['page'.$show_order_status[$status]], tep_get_all_get_params(array('page'.$show_order_status[$status], 'cont', 'criteria_id'))."cont=1", 'page'.$show_order_status[$status])?></td>
          					</tr>
        				</table>
        			</td>
        		</tr>
<?		}	?>	

<?
	} else if ($_REQUEST['action'] == 'report') {
			echo tep_draw_form('update_task_info', FILENAME_SUPPLIER_PROGRESS_REPORT, tep_get_all_get_params(array('action')) . 'action=search', 'post');
?>
				<table border="0" cellspacing="0" cellpadding="2" align="center" width="52%">
<?				
				$orders_products_id_select_sql = "  	SELECT sta.supplier_payable_adjust, SEC_TO_TIME( (TO_DAYS(now())*24*3600+TIME_TO_SEC(now())) - (TO_DAYS(sta.supplier_tasks_time_reference)*24*3600+TIME_TO_SEC(sta.supplier_tasks_time_reference)) ) AS hours, sta.supplier_tasks_time_reference, sta.supplier_tasks_time_taken, sta.supplier_firstname, sta.supplier_lastname, s.supplier_code, ocpB.orders_custom_products_value AS bracket_info, ocp.orders_products_id, ocp.orders_custom_products_value AS task_info, sta.supplier_tasks_start_time, sta.orders_products_id, sta.supplier_tasks_allocation_info, sta.supplier_tasks_allocation_progress, sta.supplier_tasks_status, o.orders_id, op.products_id, op.products_name, op.final_price, op.orders_products_store_price, o.currency, o.currency_value 
														FROM " . TABLE_ORDERS . " AS o 
														INNER JOIN " . TABLE_ORDERS_STATUS . " AS os 
															ON (o.orders_status = os.orders_status_id) 
														INNER JOIN " . TABLE_ORDERS_PRODUCTS . " AS op 
															ON (o.orders_id = op.orders_id AND op.custom_products_type_id = 1) 
														INNER JOIN " . TABLE_ORDERS_CUSTOM_PRODUCTS . " AS ocp 
															ON (op.orders_products_id = ocp.orders_products_id AND ocp.orders_custom_products_key = 'power_leveling_info') 
														INNER JOIN " . TABLE_SUPPLIER_TASKS_ALLOCATION . " AS sta 
															ON (op.orders_products_id = sta.orders_products_id) 
														LEFT JOIN " . TABLE_SUPPLIER . " AS s 
															ON (sta.suppliers_id = s.supplier_id) 
														LEFT JOIN " . TABLE_ORDERS_CUSTOM_PRODUCTS . " AS ocpB 
															ON (sta.orders_products_id = ocpB.orders_products_id AND ocpB.orders_custom_products_key = 'power_leveling_bracket_info') 
														WHERE sta.suppliers_id ='" . (int)$supplier_id . "' 
															AND sta.orders_products_id ='" . (int)$orders_product_id . "'";
				$orders_products_id_result_sql = tep_db_query($orders_products_id_select_sql);
				
				while ($orders_products_id_row = tep_db_fetch_array($orders_products_id_result_sql)) {
					preg_match('/(?:ETA:)(?:.*?)(\d+)( day)(##)?/', $orders_products_id_row['task_info'], $eta_days_array);
					preg_match('/(?:ETA:)(?:.*?)(\d+)( hour)(##)?/', $orders_products_id_row['task_info'], $eta_hours_array);
					
					$time_array = explode(':', $orders_products_id_row['hours']);
						
					if ($orders_products_id_row['supplier_tasks_status'] == 2 || $orders_products_id_row['supplier_tasks_status'] == 3 || $orders_products_id_row['supplier_tasks_status'] == 4) {
						$time_pass = (int)((int)$orders_products_id_row['supplier_tasks_time_taken'] / 60);
					} else if ($orders_products_id_row['supplier_tasks_status'] == 1) {
						$time_pass = (int)((int)$time_array[0] + ((int)$orders_products_id_row['supplier_tasks_time_taken'] / 60));
					}
					
					if (tep_not_null($orders_products_id_row['bracket_info'])) {
						$tot_time = 0;
						$bracket_info_array = unserialize($orders_products_id_row['bracket_info']);
						
						preg_match('/(Current '.$bracket_info_array['bracket_cfg']['pl_level_label'].': )([0-9a-zA-Z]+)(##)?/', $orders_products_id_row['task_info'], $current_level_array);
						preg_match('/(Desired '.$bracket_info_array['bracket_cfg']['pl_level_label'].': )([0-9a-zA-Z]+)(##)?/', $orders_products_id_row['task_info'], $desired_level_array);
						
						if (count($eta_days_array) > 0) {
							$tot_time = $eta_days_array[1] * 24;
						}
						
						$tot_time += $eta_hours_array[1];
						
						if ($bracket_info_array['mode'] == 'continuos') {
							$current_level = $current_level_array[2];
							$desired_level = $desired_level_array[2];
								
							for ($i = 0; $i < sizeof($bracket_info_array['range']) ; $i++) {
												
								if ((int)$bracket_info_array['range'][$i]['level'] <= (int)$desired_level) {
									if ((int)$bracket_info_array['range'][$i]['level'] > (int)$current_level) {
										$tot_time_need = $tot_time_need + (double)$bracket_info_array['range'][$i]['interval'];
									}
								}
							}
						} else if ($bracket_info_array['mode'] == 'discrete') {
							$current_alias = $current_level_array[2];
							$desired_alias = $desired_level_array[2];
							
							for ($track = 0; $track < sizeof($bracket_info_array['range']) ; $track++) {
								if (tep_not_null($bracket_info_array['range'][$track]['alias'])) {
									if ($current_alias == $bracket_info_array['range'][$track]['alias']) {
										$start = $track;
									} else if ($desired_alias == $bracket_info_array['range'][$track]['alias']) {
										$end = $track;
									}
								} else {
									if ($current_alias == $bracket_info_array['range'][$track]['level']) {
										$start = $track;
									} else if ($desired_alias == $bracket_info_array['range'][$track]['level']) {
										$end = $track;
									}
								}
							}
							
							$current_level = $start;
							$desired_level = $end;
							
							for ($i = 0; $i < sizeof($bracket_info_array['range']) ; $i++) {
								if ((int)$bracket_info_array['range'][$i]['level'] <= (int)$desired_level) {
									if ((int)$bracket_info_array['range'][$i]['level'] > (int)$current_level) {
										$tot_time_need = $tot_time_need + (double)$bracket_info_array['range'][$i]['interval'];
									}
								}
							}
						}
					}
?>	
					<tr>
					    <td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
					</tr>
<?
					$profiler_url = tep_get_profiler_link($orders_products_id_row['orders_products_id']);
					
					if (tep_not_null($profiler_url)) {
?>
					<tr>
						<td>
							<table border="0" cellspacing="0" cellpadding="0" width="30%">
								<tr>
									<td class="main"><?=TEXT_CHARACTER_INFO?></td>
									<td class="main"><?=$profiler_url?></td>
								</tr>
							</table>
						</td>
					</tr>
					<tr>
					    <td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
					</tr>
<?					} ?>
					<tr>
						<td>
							<table border="0" cellspacing="0" cellpadding="3" bgcolor="#F0F0FF" bordercolor="#7187BB" width="100%">
								<tr bgcolor="#7187BB" rowspan="2">
									<td colspan="3" class="invoiceRecordsRightBorder">
										<b><?=ENTRY_HEADING_ORDER_ID . $orders_products_id_row['orders_id'] .'&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' . TABLE_HEADING_PRODUCT_NAME . ': ' . $orders_products_id_row['products_name']?></b>
									</td>
								</tr>
								<tr>
									<td width="50%" valign="top">
										<table border="0" cellspacing="0" cellpadding="0" align="center" width="100%">
											<tr>
												<td colspan="3" class="invoiceRecordsRightBorder" align="center">
													<?=TEXT_HOURS_PASS .(int)$time_pass?>
												</td>
											</tr>
											<tr>
<?					if (tep_not_null($orders_products_id_row['bracket_info']) && $tot_time != 0) { ?>
												<td align="right" class="invoiceRecordsRightBorder" nowrap>
													<?=tep_draw_separator('pixel_trans.gif', '61', '1') . '<br>'?>
													<?='0 ' . TEXT_HOURS?>
												</td>
												<td>
													<table border="1" cellspacing="0" cellpadding="0" align="center" bordercolor="black" class="bar_border" width="96%">
														<tr>
															<td bgcolor="white">
																<table border="0" cellspacing="1" cellpadding="1" align="left">
																	<tr>
																		<?
																		$time_bar_unit = 20 / $tot_time;
																		$total_bar_unit = ((int)$time_pass * $time_bar_unit);
																		
																		for ($unit_count=0; $unit_count < 20; $unit_count++) {
																			echo '<td class="main" valign="middle">';
																			echo '<div class="'.($unit_count < $total_bar_unit ? 'bar_time' : 'bar_time_empty').'">'.'&nbsp;'.'</div>';
																			echo '</td>';
																		}
																		?>
																	</tr>
																</table>
															</td>
														</tr>
													</table>
												</td>
												<td align="left" class="invoiceRecordsRightBorder" nowrap>
													<?=tep_draw_separator('pixel_trans.gif', '61', '1') . '<br>'?>
													<?=$tot_time . ' ' . TEXT_HOURS;?>
												</td>
<?					} else { ?>
												<td align="center" class="invoiceRecordsRightBorder" nowrap>
													<?=TEXT_PROGRESS_BAR_NOT_AVAILABLE?><br>
													<?=tep_draw_separator('pixel_trans.gif', '320', '1') . '<br>'?>
												</td>
<?					} ?>
											</tr>
											<tr>
												<td colspan="3"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
											</tr>
											<tr>
												<td colspan="3" class="invoiceRecordsRightBorder" align="center"><?=TEXT_CURRENT_LEVEL . $orders_products_id_row['supplier_tasks_allocation_progress'] . '&nbsp;'?></td>
											</tr>
											<tr>
<?					if (tep_not_null($orders_products_id_row['bracket_info']) && tep_not_null($desired_level)) { ?>
												<td align="right" class="invoiceRecordsRightBorder"><?=$current_level_array[2] . ' ' . $bracket_info_array['bracket_cfg']['pl_level_label']?></td>
												<td>
													<table border="1" cellspacing="0" cellpadding="0" align="center" bordercolor="black" class="bar_border" width="96%">
														<tr>
															<td bgcolor="white">
																<table border="0" cellspacing="1" cellpadding="1" align="left">
																	<tr>
																		<?
																			$level_bar_unit = 20 / ((int)$desired_level - $current_level);
																			
																			if ($bracket_info_array['mode'] == 'continuos') {
																				$total_bar_unit = ((int)$orders_products_id_row['supplier_tasks_allocation_progress'] - (int)$current_level)*$level_bar_unit;
																			} else if ($bracket_info_array['mode'] == 'discrete') {
																				for ($track_bar = 0; $track_bar < sizeof($bracket_info_array['range']) ; $track_bar++) {
																					if (tep_not_null($bracket_info_array['range'][$track_bar]['alias'])) {
																						if ($orders_products_id_row['supplier_tasks_allocation_progress'] == $bracket_info_array['range'][$track_bar]['alias']) {
																							$total_bar_unit = ($track_bar - (int)$current_level) * $level_bar_unit;
																						}
																					} else {
																						if ($orders_products_id_row['supplier_tasks_allocation_progress'] == $bracket_info_array['range'][$track_bar]['level']) {
																							$total_bar_unit = ($track_bar - (int)$current_level) * $level_bar_unit;
																						}
																					}
																				}
																			}
																					
																			for ($unit_count=0; $unit_count < 20; $unit_count++) {
																				echo '<td class="main" valign="middle">';
																				echo '<div class="'.($unit_count < $total_bar_unit ? 'bar_alert_red' : 'bar_time_empty').'">'.'&nbsp;'.'</div>';
																				echo '</td>';
																			}
																		?>
																	</tr>
																</table>
															</td>
														</tr>
													</table>
												</td>
												<td align="left" class="invoiceRecordsRightBorder" nowrap><?=$desired_level_array[2] . ' ' . $bracket_info_array['bracket_cfg']['pl_level_label']?></td>
<?					} else { ?>
												<td align="center" class="invoiceRecordsRightBorder" colspan="3" nowrap><?=TEXT_PROGRESS_BAR_NOT_AVAILABLE?></td>
<?					} ?>
											</tr>
										</table>
									</td>
									<td width="45%" valign="top">
										<table border="0" cellspacing="0" cellpadding="0" align="center" width="100%">
											<tr>
												<td width="100%" class="invoiceRecordsRightBorder" nowrap>
												<?
													if ($orders_products_id_row['supplier_tasks_status'] == 4) {
														echo TEXT_INFO_NOT_AVAILABLE;
													} else {
														$customer_product_info_array = explode("\n", $orders_products_id_row['task_info']);
														$info_display = "";
														$info_display_array = array();
														$info_display_count = 0;
														
														foreach ($customer_product_info_array as $content => $content_value) {
															$content_value = trim($content_value);
															
															$pattern = '/(##1##)$/i';
															$replacement = '';
															if (preg_match($pattern, $content_value)) {
																$customer_product_info_array[$content] = preg_replace($pattern, $replacement, $content_value) . "\n";
																$info_display = $customer_product_info_array[$content];
																$info_display_array[$info_display_count][] = $info_display;
															}
															if (preg_match('/\d{4}\-\d{2}\-\d{2}(\s*?)\d{2}\:\d{2}\:\d{2} \(by/', $content_value) && !preg_match('/(---------)/', $content_value) && tep_not_null($content_value)) {
																if (tep_not_null($info_display)) {
																	$info_display_count++;
																	$info_display = "";
																}
															}
														}
														
														if (tep_not_null($orders_products_id_row['supplier_tasks_allocation_info'])) {
															echo '----------------------' . '<br>';
															echo nl2br($orders_products_id_row['supplier_tasks_allocation_info']);
															echo '<br>' . '----------------------' . '<br><br>';
														}
														
														for ($count=0; $count < count($info_display_array); $count++) {
															$info_display_final = implode("<br>", $info_display_array[$count]);
															echo ($count == (count($info_display_array)-1) ? "<b>" : "") . $info_display_final . ($count == (count($info_display_array)-1) ? "</b>" : "");
															echo "<br>";
														}
														
														// Display character name, faction & realm if exist where it is not in completed & paid
														$account_non_general_info_array = tep_get_custom_product_info_option_class($orders_products_id_row['orders_products_id']);
														
														foreach ($account_non_general_info_array as $key => $info_array) {
															switch($key) {
																case 'account_password':
																	if ($orders_products_id_row['supplier_tasks_status'] != 4 && $orders_products_id_row['supplier_tasks_status'] != 5) {
																		$cat_cfg_array = tep_get_cfg_setting($orders_products_id_row['products_id'], 'product');
																		
																		if ($cat_cfg_array['GAME_REMOTE_ACCOUNT_LOGIN_API'] == 'true') {
																			$use_remote_account_login_api_select_sql = "SELECT use_remote_account_login_api FROM " . TABLE_SUPPLIER_TASKS_SETTING . " WHERE suppliers_id = '" . (int)$supplier_id . "' AND  custom_products_type_id = '1'";
																			$use_remote_account_login_api_result_sql = tep_db_query($use_remote_account_login_api_select_sql);
																			$use_remote_account_login_api_row = tep_db_fetch_array($use_remote_account_login_api_result_sql);
																			
																			if ($use_remote_account_login_api_row['use_remote_account_login_api'] == 0) {
																				echo $info_array['label'] . ': ' . $info_array['value'] . '<br>';
																			}
																		} else {
																			echo $info_array['label'] . ': ' . $info_array['value'] . '<br>';
																		}
																	}
																	break;
																case 'game':
																		// WOW game list
																		$wow_game_list_array[''] = 'Not Selected';
																		$wow_game_list_array[2299] = 'World of Warcraft US';
																		$wow_game_list_array[2522] = 'World of Warcraft EU';
																		
																		echo $info_array['label'] . ': ' . $wow_game_list_array[$info_array['value']] . '<br>';
																		break;
																default:
																	echo $info_array['label'] . ': ' . $info_array['value'] . '<br>';
																	break;
															}
														}
													}
												?>
												</td>
											</tr>
										</table>
									</td>
									<td width="10%">
										<table border="0" cellspacing="0" cellpadding="0" align="center">
											<tr>
												<td class="invoiceRecordsRightBorder" align="center" nowrap>
												<?
													if ($orders_products_id_row['supplier_tasks_status'] == 0) {
														$task_status_array = array();
														$task_status_array[] = array('id' => '', 'text' => TEXT_CHANGE_STATUS);
														$task_status_array[] = array('id' => 1, 'text' => $status_name[1]);
														
														echo	'<table border="0" cellspacing="0" cellpadding="0" align="center" width="100%">' .
																	'<tr>' .
																		'<td>' .
																			tep_draw_pull_down_menu('task_status['.$orders_products_id_row['orders_products_id'].']', $task_status_array, $orders_products_id_row['supplier_tasks_status']) .
																		'</td>' .
																	'</tr>' .
																'</table>';
													} else if ($orders_products_id_row['supplier_tasks_status'] == 1) {
														echo '<table border="0" cellspacing="0" cellpadding="0" align="center" width="100%">';
														if (tep_not_null($orders_products_id_row['bracket_info'])) {
															if ($bracket_info_array['mode'] == 'continuos') {
																$task_status_array = array();
																$task_status_array[] = array('id' => '', 'text' => TEXT_CHANGE_STATUS);
																$task_status_array[] = array('id' => 1, 'text' => $status_name[1]);
																$task_status_array[] = array('id' => 2, 'text' => $status_name[2]);
																
																echo	'<tr>' .
																			'<td>' .
																				tep_draw_pull_down_menu('task_status['.$orders_products_id_row['orders_products_id'].']', $task_status_array, $orders_products_id_row['supplier_tasks_status']) .
																	 			tep_draw_hidden_field('tasks_time_taken['.$orders_products_id_row['orders_products_id'].']', $orders_products_id_row['supplier_tasks_time_taken']) .
																			'</td>' .
																		'</tr>' .
																		'<tr>' .
																			'<td>' . tep_draw_separator('pixel_trans.gif', '1', '10') . '</td>' .
																		'</tr>' .
																		'<tr>' .
																			'<td class="invoiceRecordsRightBorder">' .
																				TEXT_CURRENT_LEVEL . tep_draw_input_field('current_level['. $orders_products_id_row['orders_products_id'] .']', $orders_products_id_row['supplier_tasks_allocation_progress'], 'size="2"') .
																				'<br>' . tep_draw_checkbox_field('status_done['.$orders_products_id_row['orders_products_id'].']', 3) . TEXT_I_AM_DONE .
																			'</td>' .
																		'</tr>';
															} else if ($bracket_info_array['mode'] == 'discrete') {
																$task_status_array = array();
																$task_status_array[] = array('id' => '', 'text' => TEXT_CHANGE_STATUS);
																$task_status_array[] = array('id' => 1, 'text' => $status_name[1]);
																$task_status_array[] = array('id' => 2, 'text' => $status_name[2]);
																
																$all_options_array = array();
																
																for ($all_options = 0; $all_options < sizeof($bracket_info_array['range']) ; $all_options++) {
																	if (tep_not_null($bracket_info_array['range'][$all_options]['alias'])) {
																		$all_options_array[] = array('id' => $bracket_info_array['range'][$all_options]['alias'], 'text' => $bracket_info_array['range'][$all_options]['alias']);
																	} else {
																		$all_options_array[] = array('id' => $bracket_info_array['range'][$all_options]['level'], 'text' => $bracket_info_array['range'][$all_options]['level']);
																	}
																}
																echo	'<tr>' .												
																			'<td>' .
																				tep_draw_pull_down_menu('task_status['.$orders_products_id_row['orders_products_id'].']', $task_status_array, $orders_products_id_row['supplier_tasks_status']) .
																	 			tep_draw_hidden_field('tasks_time_taken['.$orders_products_id_row['orders_products_id'].']', $orders_products_id_row['supplier_tasks_time_taken']) .
																			'</td>' .
																		'</tr>' .
																		'<tr>' .
																			'<td>' . tep_draw_separator('pixel_trans.gif', '1', '10') . '</td>' .
																		'</tr>' .
																		'<tr>' .
																			'<td class="invoiceRecordsRightBorder">' .
																				TEXT_CURRENT_LEVEL . tep_draw_pull_down_menu('current_level['.$orders_products_id_row['orders_products_id'].']', $all_options_array, $orders_products_id_row['supplier_tasks_allocation_progress']) .
																				'<br>' . tep_draw_checkbox_field('status_done['.$orders_products_id_row['orders_products_id'].']', 3) . TEXT_I_AM_DONE .
																			'</td>' .
																		'</tr>';
															}
														} else {
															$task_status_array = array();
															$task_status_array[] = array('id' => '', 'text' => TEXT_CHANGE_STATUS);
															$task_status_array[] = array('id' => 1, 'text' => $status_name[1]);
															$task_status_array[] = array('id' => 2, 'text' => $status_name[2]);
															echo	'<tr>' .											
																		'<td>' .
																			tep_draw_pull_down_menu('task_status['.$orders_products_id_row['orders_products_id'].']', $task_status_array, $orders_products_id_row['supplier_tasks_status']) .
																 			tep_draw_hidden_field('tasks_time_taken['.$orders_products_id_row['orders_products_id'].']', $orders_products_id_row['supplier_tasks_time_taken']) .
																		'</td>' .
																	'</tr>' .
																	'<tr>' .
																		'<td>' . tep_draw_separator('pixel_trans.gif', '1', '10') . '</td>' .
																	'</tr>' .
																	'<tr>' .
																		'<td class="invoiceRecordsRightBorder">' .
																			TEXT_CURRENT_LEVEL . tep_draw_input_field('current_level['. $orders_products_id_row['orders_products_id'] .']', $orders_products_id_row['supplier_tasks_allocation_progress'], 'size="2"') .
																			'<br>' . tep_draw_checkbox_field('status_done['.$orders_products_id_row['orders_products_id'].']', 3) . TEXT_I_AM_DONE .
																		'</td>' .
																	'</tr>';
														}
														echo '</table>';
													} else if ($orders_products_id_row['supplier_tasks_status'] == 2) {
														$task_status_array = array();
														$task_status_array[] = array('id' => '', 'text' => TEXT_CHANGE_STATUS);
														$task_status_array[] = array('id' => 1, 'text' => $status_name[1]);
														$task_status_array[] = array('id' => 2, 'text' => $status_name[2]);
														
														echo	'<table border="0" cellspacing="0" cellpadding="0" align="center" width="100%">' .
																	'<tr>' .
																		'<td>' .
																			tep_draw_pull_down_menu('task_status['.$orders_products_id_row['orders_products_id'].']', $task_status_array, $orders_products_id_row['supplier_tasks_status']) .
																 			tep_draw_hidden_field('tasks_time_taken['.$orders_products_id_row['orders_products_id'].']', $orders_products_id_row['supplier_tasks_time_taken']) .
																		'</td>' .
																	'</tr>' .
																	'<tr>' .
																		'<td>' . tep_draw_separator('pixel_trans.gif', '1', '10') . '</td>' .
																	'</tr>' .
																	'<tr>' .
																		'<td class="invoiceRecordsRightBorder">' .
																			TEXT_CURRENT_LEVEL . ' ' . $orders_products_id_row['supplier_tasks_allocation_progress'] .
																		'</td>' .
																	'</tr>' .
																'</table>';
													} else if ($orders_products_id_row['supplier_tasks_status'] == 3) {
														$task_status_array = array();
														$task_status_array[] = array('id' => '', 'text' => TEXT_CHANGE_STATUS);
														$task_status_array[] = array('id' => 1, 'text' => $status_name[1]);
														$task_status_array[] = array('id' => 2, 'text' => $status_name[2]);
														
														echo	'<table border="0" cellspacing="0" cellpadding="0" align="center" width="100%">' .
																	'<tr>' .
																		'<td>' .
																			tep_draw_pull_down_menu('task_status['.$orders_products_id_row['orders_products_id'].']', $task_status_array, $orders_products_id_row['supplier_tasks_status']) .
																		'</td>' .
																	'</tr>' .
																'</table>';
													} else if ($orders_products_id_row['supplier_tasks_status'] == 4) {
														echo $status_name[4];
													}
												?>
												</td>
											</tr>
										</table>
									</td>
								</tr>
							</table>
						</td>
					</tr>
					<tr>
						<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
					</tr>
	
<?
				if ($orders_products_id_row['supplier_tasks_status'] == 5) { 
					$custom_product_payments_array = custom_product_payment::get_order_paid_history($orders_products_id_row['orders_products_id'], false);	
?>
					<tr>
						<td>
							<table width="65%" border="0" cellspacing="1" cellpadding="2" class="printViewTable">
								<tr>
									<td class="ordersBoxHeading" align="right" nowrap="nowrap" width="12%"><?=TABLE_HEADING_TOTAL?></td>
									<!--td class="ordersBoxHeading" align="center" nowrap="nowrap" width="12%"><?=TABLE_HEADING_RATIO?></td-->
									<td class="ordersBoxHeading" align="right" nowrap="nowrap" width="14%"><?=TABLE_HEADING_AMOUNT_ADJUST?></td>
									<td class="ordersBoxHeading" align="right" nowrap="nowrap" width="12%"><?=TABLE_HEADING_TOTAL_PAYABLE?></td>
								</tr>
								<tr class="ordersListingOdd">
									<td class="ordersRecords" align="right"><?=$currencies->format(tep_not_null($orders_products_id_row['orders_products_store_price']) ? $orders_products_id_row['orders_products_store_price'] : $orders_products_id_row['final_price'], true, $orders_products_id_row['currency'], $orders_products_id_row['currency_value'])?></td>
									<!--td class="ordersRecords" align="center"><?=$orders_products_id_row['supplier_tasks_ratio'] . '%'?></td-->
									<td class="ordersRecords" align="right"><?=$orders_products_id_row['supplier_payable_adjust']?></td>
									<td class="ordersRecords" align="right"><?=$currencies->format($custom_product_payments_array['paid_amount'], true, $custom_product_payments_array['paid_currency'], $custom_product_payments_array['paid_currency_value'])?></td>
								</tr>
								<tr>
									<td class="ordersRecords" align="right" colspan="4"><?=ENTRY_TOTAL . ' ' . $currencies->format($custom_product_payments_array['paid_amount'], true, $custom_product_payments_array['paid_currency'], $custom_product_payments_array['paid_currency_value'])?></td>
								</tr>
								<tr>
									<td class="ordersRecords" align="right" colspan="4"><?='#'.$custom_product_payments_array["payment_id"].': ' . $currencies->format($custom_product_payments_array['paid_amount'], true, $custom_product_payments_array['paid_currency'], $custom_product_payments_array['paid_currency_value'])?></td>
								</tr>
							</table>
						</td>
					</tr>
					<tr>
						<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
					</tr>
<?				} ?>
					<tr>
						<td align="left">
<?
							$supplier_tasks_allocation_history_select_sql = "SELECT supplier_tasks_allocation_history_id, supplier_tasks_status, date_added, comments, user_role, changed_by, supplier_tasks_allocation_history_show, supplier_code FROM " . TABLE_SUPPLIER_TASKS_ALLOCATION_HISTORY . " AS stah LEFT JOIN " . TABLE_SUPPLIER . " AS s ON (stah.changed_by = s.supplier_email_address AND stah.user_role = 'supplier') WHERE orders_products_id='" . (int)$orders_product_id . "' AND supplier_tasks_allocation_history_show = 1";
							$supplier_tasks_allocation_history_result_sql = tep_db_query($supplier_tasks_allocation_history_select_sql);
?>
							<table border="1" cellspacing="0" cellpadding="5" width="100%">
								<tr>
									<td class="smallText" width="16%"><b><?=TABLE_HEADING_DATE_ADDED?></b></td>
									<td class="smallText" align="center" width="13%"><b><?=TABLE_HEADING_STATUS?></b></td>
									<td class="smallText" align="center"><b><?=TABLE_HEADING_COMMENTS?></b></td>
									<td class="smallText" align="center" width="19%"><b><?=TABLE_HEADING_MODIFIED_BY?></b></td>
								</tr>
<?
								while ($supplier_tasks_allocation_history_row = tep_db_fetch_array($supplier_tasks_allocation_history_result_sql)) {
									if ($supplier_tasks_allocation_history_row["user_role"] == 'supplier') {
										preg_match('/([0-9a-zA-Z]+)(@)([0-9a-zA-Z]+)(.com)?/', $supplier_tasks_allocation_history_row["changed_by"], $email_array);
										$modifled_by = $email_array[1];
									} else if ($supplier_tasks_allocation_history_row["user_role"] == 'admin') {
										$modifled_by = $supplier_tasks_allocation_history_row["changed_by"];
									}
?>
									<tr>
										<td class="smallText"><?=$supplier_tasks_allocation_history_row["date_added"]?></td>
										<td class="smallText" align="center"><?=(tep_not_null($supplier_tasks_allocation_history_row["supplier_tasks_status"]) ? $status_name[$supplier_tasks_allocation_history_row["supplier_tasks_status"]] : '--')?></td>
										<td class="smallText">
<?
										if (tep_not_null($supplier_tasks_allocation_history_row["comments"])) {
											$comments_array = explode ("\n", $supplier_tasks_allocation_history_row["comments"]);
											
											for ($comments_array_count = 0; $comments_array_count < sizeof($comments_array); $comments_array_count++) {
												$pattern = '/^(?:#sys#)(Account\s)?(Password:\s)((.*)+)/';
												
												if ($cat_cfg_array['GAME_REMOTE_ACCOUNT_LOGIN_API'] == 'true') {
													$replacement = '$1$2xxxxxx --> xxxxxx';
												} else {
													$replacement = '$1$2$3';
												}
												
												echo preg_replace($pattern, $replacement, $comments_array[$comments_array_count]) . '<br>';
											}
										} else {
											echo '&nbsp;';	
										}
?>
										</td>
										<td class="smallText" align="center"><?=(tep_not_null($supplier_tasks_allocation_history_row["supplier_code"]) ? $supplier_tasks_allocation_history_row["supplier_code"] : $modifled_by)?></td>
									</tr>
<?								}	?>
							</table>
						</td>
					</tr>
					<tr>
						<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
					</tr>
					<tr>
						<td align="left">
							<table border="0" cellspacing="0" cellpadding="0">
								<tr>
									<td><?=tep_draw_textarea_field('comments['.$orders_products_id_row['orders_products_id'].']', 'soft', '60', '5', '')?></td>
								</tr>
								<tr>
									<td align="right"><?=tep_submit_button(IMAGE_UPDATE, IMAGE_UPDATE, 'name="post_comment['.$orders_products_id_row['orders_products_id'].']"', 'inputButton')?></td>
								</tr>
							</table>
						</td>
					</tr>
<?				} ?>
				</table>
			</form>
			
<?
	} else if ($_REQUEST['action'] == 'char_info') {
		require_once(DIR_WS_CLASSES . 'wow/bag.php');
		require_once(DIR_WS_CLASSES . 'wow/char.php');
		require_once(DIR_WS_CLASSES . 'wow/item.php');
		require_once(DIR_WS_CLASSES . 'wow/skill.php');
		require_once(DIR_WS_CLASSES . 'wow/quest.php');
		require_once(DIR_WS_CLASSES . 'wow/bank.php');
		
		$game_char_id = tep_db_prepare_input($_REQUEST['game_char_id']);
		$orders_products_id = 0;
		$character_array = array();
		
		$char_info_select_str = "	SELECT gc.*, gch.game_char_history_id, gch.game_char_id, gch.stat_int, gch.stat_agl, gch.stat_sta, gch.stat_str, gch.stat_spr, gch.guild_name, gch.guild_title, gch.guild_rank, gch.race, gch.res_holy, gch.res_frost, gch.res_arcane, gch.res_fire, gch.res_shadow, gch.res_nature, gch.armor, gch.level, gch.defense, gch.talent_points, gch.money_c, gch.money_s, gch.money_g, gch.exp, gch.class, gch.health, gch.melee_power, gch.melee_rating, gch.melee_range, gch.melee_range_tooltip, gch.melee_power_tooltip, gch.ranged_power, gch.ranged_rating, gch.ranged_range, gch.ranged_range_tooltip, gch.ranged_power_tooltip, gch.version, gch.game_char_history_date 
									FROM " . TABLE_GAME_CHAR . " AS gc 
									INNER JOIN " . TABLE_GAME_CHAR_HISTORY . " AS gch 
										ON (gc.game_char_id = gch.game_char_id) 
									INNER JOIN " . TABLE_SUPPLIER_TASKS_ALLOCATION . " AS sta 
										ON (sta.orders_products_id = gc.orders_products_id) ";
		$char_info_where_str .= " WHERE gc.game_char_id = '" . tep_db_input($game_char_id) . "' AND sta.suppliers_id ='" . (int)$supplier_id . "' ORDER BY gch.game_char_history_date DESC LIMIT 1";
		
		$char_info_select_sql = $char_info_select_str . $char_info_where_str;
		
		$char_info_result_sql = tep_db_query($char_info_select_sql);
		$char_info_row = tep_db_fetch_array($char_info_result_sql);
		
		$char_info = new char($char_info_row);
		
		$game_char_id_select_sql = "SELECT game_char_id, name FROM " . TABLE_GAME_CHAR . " WHERE orders_products_id ='" . (int)$char_info_row['orders_products_id'] . "'";
		$game_char_id_result_sql = tep_db_query($game_char_id_select_sql);
		if (tep_db_num_rows($game_char_id_result_sql) > 0) {
			while ($game_char_id_row = tep_db_fetch_array($game_char_id_result_sql)) {
				$character_array[] = array('id' => $game_char_id_row['game_char_id'], 'text' => $game_char_id_row['name']);
			}
		}
?>
			<script language="JavaScript" src="includes/javascript/menu.js"></script>
			<table border="0" width="100%" cellspacing="0" cellpadding="0">
				<tr>
					<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '20')?></td>
				</tr>
				<tr>
					<td width="20"></td>
					<td>
<?						echo tep_draw_form('compareDate', FILENAME_PROGRESS_REPORT, tep_get_all_get_params(), 'post', ''); ?>
							<table border="0" cellspacing="0" cellpadding="0" width="30%">
								<tr>
									<td class="main"><?=ENTRY_CHARACTER?></td>
									<td><?=tep_draw_pull_down_menu('character', $character_array, '', ' id="character" onChange="view_character(this);"')?></td>
								</tr>
							</table>
						</form>
					</td>
				</tr>
				<tr>
					<td></td>
					<td>
						<table border="0" width="100%" cellspacing="0" cellpadding="0">
							<tr>
								<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '50')?></td>
							</tr>
							<tr>
								<td>
<?		if ($_REQUEST['view_info'] == 'bag_bank') { ?>
									<table border="0" cellspacing="2" cellpadding="0" width="100%">
										<tr>
											<td valign="top" width="35%">
<?
												$bank = bank_get($char_info_row['game_char_id'], '', $char_info_row["game_char_history_id"], "bank");
												
												if (isset($bank)) {
													$bank->out();
												}
?>
											</td>
											<td valign="top">
<?
												$default_bank_bag_slot = 6;
												$bank_bag_slot = bank_bag_num($char_info_row['game_char_id'], $char_info_row["game_char_history_id"]);
												
												if ($bank_bag_slot > $default_bank_bag_slot) {
													$default_bank_bag_slot = $bank_bag_slot;
												}
												
												for ($bank_bag_count = 1; $bank_bag_count <= $default_bank_bag_slot; $bank_bag_count++) {
													
													$bankbag = bag_get($char_info_row['game_char_id'], "", $char_info_row["game_char_history_id"], "bankBag".$bank_bag_count);
													
													if (isset($bankbag)) {
														$bankbag_div = "bankBag" . $bank_bag_count . "_div_id";
?>
														<div id="<?=$bankbag_div?>" class="hide"><?=$bankbag->out()?></div>
<?
										}
									}
?>
												</div>
											</td>
										</tr>
									</table>
									<table border="0" cellspacing="2" cellpadding="0">
										<tr>
											<td valign="top">
<?
												$bag0 = bag_get($char_info_row['game_char_id'], "", $char_info_row["game_char_history_id"], "Bag0");
												if (isset($bag0)) {
													$bag0->out();
												}
?>
											</td>
											<td valign="top">
<?
												$bag1 = bag_get($char_info_row['game_char_id'], "", $char_info_row["game_char_history_id"], "Bag1");
												if (isset($bag1)) {
													$bag1->out();
												}
?>
											</td>
											<td valign="top">
<?
												$bag2 = bag_get($char_info_row['game_char_id'], "", $char_info_row["game_char_history_id"], "Bag2");
												if (isset($bag2)) {
													$bag2->out();
												}
?>
											</td>
											<td valign="top">
<?
												$bag3 = bag_get($char_info_row['game_char_id'], "", $char_info_row["game_char_history_id"], "Bag3");
												if (isset($bag3)) {
													$bag3->out();
												}
?>
											</td>
											<td valign="top">
<?
												$bag4 = bag_get($char_info_row['game_char_id'], "", $char_info_row["game_char_history_id"], "Bag4");
												if (isset($bag4)) {
													$bag4->out();
												}
?>
											</td>
										</tr>
									</table>
<?		} else { ?>
									<table border="0" cellspacing="2" cellpadding="0" width="100%">
										<tr>
											<td valign="top" width="38%">
												<?
												if (isset($char_info)) {
													echo $char_info->out();
												}
												?>
											</td>
										</tr>
									</table>
<?		} ?>
								</td>
							</tr>
						</table>
					</td>
				</tr>
				<tr>
					<td></td><td class="main"><a href="<?=tep_href_link(FILENAME_SUPPLIER_PROGRESS_REPORT, 'orders_product_id=' . (int)$char_info_row['orders_products_id'] . '&action=report')?>"><?=BUTTON_BACK?></a></td>
				</tr>
			</table>
			<script language="javascript"><!--
				function view_character (sel) {
					window.location.href = '<?=tep_href_link(FILENAME_SUPPLIER_PROGRESS_REPORT, "game_char_id='+sel.value+'&action=char_info")?>';
				}
				
				function hidebag(id) {
					if (DOMCall(id+"_div_id") != null) {
						if (document.getElementById(id+"_div_id").className == "hide") {
							document.getElementById(id+"_div_id").className = "show";
						} else if (document.getElementById(id+"_div_id").className == "show") {
							document.getElementById(id+"_div_id").className = "hide";
						}
					}
				}
				
				var MENU_ITEMS = [
					['<?=TEXT_GEAR?>', '<?=tep_href_link(FILENAME_SUPPLIER_PROGRESS_REPORT, tep_get_all_get_params(array("view_info")))?>', null, null],
					['<?=TEXT_BAGS_AND_BANK?>', '<?=tep_href_link(FILENAME_SUPPLIER_PROGRESS_REPORT, tep_get_all_get_params()."view_info=bag_bank")?>', null, null],
				];

				var MENU_POS = [{
					// item sizes
					'height': 24,
					'width': 130,
					// menu block offset from the origin:
					//	for root level origin is upper left corner of the page
					//	for other levels origin is upper left corner of parent item
					'block_top': 128,
					'block_left': 25,
					// offsets between items of the same level
					'top': 0,
					'left': 131,
					// time in milliseconds before menu is hidden after cursor has gone out
					// of any items
					'hide_delay': 200,
					'expd_delay': 200,
					'css' : {
						'outer' : ['m0l0oout', 'm0l0oover'],
						'inner' : ['m0l0iout', 'm0l0iover']
					}
				},{
					'height': 24,
					'width': 170,
					'block_top': 25,
					'block_left': 0,
					'top': 23,
					'left': 0,
					'css' : {
						'outer' : ['m0l1oout', 'm0l1oover'],
						'inner' : ['m0l1iout', 'm0l1iover']
					}
				},{
					'block_top': 5,
					'block_left': 160
				}
				]

				new menu (MENU_ITEMS, MENU_POS);
			//--></script>
<?
	} else if ($_REQUEST['action'] == 'advanced_search') {
		echo tep_draw_form('custom_product_criteria', FILENAME_SUPPLIER_PROGRESS_REPORT, 'action=search', 'post', '');
?>
			<table border="0" width="60%" cellspacing="0" cellpadding="0" align="center">
				<tr>
					<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '20')?></td>
				</tr>
				<tr>
					<td class="main" width="12%"><?=ENTRY_ORDER_PRODUCT_START_DATE?></td>
					<td class="main">
						<table border="0" cellspacing="2" cellpadding="0">
							<tr>
								<td class="main" valign="top" nowrap>
									<script language="javascript"><!--
								  		var date_start_date = new ctlSpiffyCalendarBox("date_start_date", "custom_product_criteria", "start_date", "btnDate_start_date", "", scBTNMODE_CUSTOMBLUE);
								  		date_start_date.writeControl(); date_start_date.dateFormat="yyyy-MM-dd"; document.getElementById('start_date').value='<?=$_SESSION['custom_product_inputs']["start_date"]?>';
									//--></script>
								</td>
								<td class="main" width="20%">&nbsp;</td>
								<td class="main" valign="top" nowrap><?=ENTRY_ORDER_PRODUCT_END_DATE?></td>
								<td class="main" valign="top" nowrap>
	    							<script language="javascript"><!--
	  									var date_end_date = new ctlSpiffyCalendarBox("date_end_date", "custom_product_criteria", "end_date", "btnDate_end_date", "", scBTNMODE_CUSTOMBLUE);
	  									date_end_date.writeControl(); date_end_date.dateFormat="yyyy-MM-dd"; document.getElementById('end_date').value='<?=$_SESSION['custom_product_inputs']["end_date"]?>';
									//--></script>
	    						</td>
							</tr>
						</table>
					</td>
				</tr>
				<tr>
					<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
				</tr>
				<tr>
					<td class="main"><?=ENTRY_ORDER_ID?></td>
					<td class="main">
						<?=tep_draw_input_field('custom_product_order_id', $_SESSION['custom_product_param']["custom_product_order_id"], ' id="custom_product_order_id" size="15" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) ) { this.value = \'\'; } else { resetControls(this); }" onBlur="if (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value)) ) { this.value = \'\'; } else { resetControls(this); }" onKeyPress="return noEnterKey(event)"');?>
					</td>
				</tr>
				<tr>
					<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
				</tr>
				<tr>
					<td class="main"><?=ENTRY_PRODUCT_NAME?></td>
					<td class="main">
						<?=tep_draw_input_field('custom_product_product_name', $_SESSION['custom_product_param']["custom_product_product_name"], ' id="custom_product_product_name" size="26" onKeyUP="if (trim_str(this.value)!=\'\') { resetControls(this); }" onBlur="if (trim_str(this.value)!=\'\') { resetControls(this); }" onKeyPress="return noEnterKey(event)"');?>
					</td>
				</tr>
				<tr>
					<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
				</tr>
				<tr>
					<td class="main">&nbsp;</td>
					<td class="main" align="right">
						<?=tep_submit_button(BUTTON_SEARCH, ALT_BUTTON_SEARCH, 'onClick="return form_checking(this.form, \'do_search\');" name="search"', 'inputButton')?>
					  	<?=tep_submit_button(BUTTON_RESET, ALT_BUTTON_RESET, 'name="reset"', 'inputButton')?>
					</td>
				</tr>
			</table>
		</form>
		
<?	} else { ?>
			<table border="0" width="41%" cellspacing="0" cellpadding="3" align="center">
				<tr>
					<td><?=tep_draw_separator('pixel_trans.gif', '1', '20')?></td>
				</tr>
				<tr>
					<td align="center"><a href="http://image.offgamers.com/download/<?=FILE_REDJACK_EN?>"><?=LINK_DOWNLOAD_REDJACK_EN?></a></td>
				</tr>
				<tr>
					<td class="ordersRecords" align="center"><?=TEXT_REDJACK_DOWNLOAD_NOTICE_ENGLISH?></td>
				</tr>
				<tr>
					<td align="center"><a href="http://image.offgamers.com/download/<?=FILE_REDJACK_CN?>"><?=LINK_DOWNLOAD_REDJACK_CN?></a></td>
				</tr>
				<tr>
					<td class="ordersRecords" align="center"><?=TEXT_REDJACK_DOWNLOAD_NOTICE_CHINESE?></td>
				</tr>
				<tr>
					<td align="center"><a href="http://image.offgamers.com/download/<?=FILE_WOW_GUIDE?>"><?=LINK_DOWNLOAD_WOW_GUIDE?></a></td>
				</tr>
				<tr>
					<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
				</tr>
				<tr>
					<td>
						<table border="0" width="65%" cellspacing="0" cellpadding="2" style="border-collapse: collapse;" align="center">
							<tr>
								<td class="ordersBoxHeading" align="center" colspan="2"><?=TABLE_HEADING_PROGRESS_REPORT_STATISTIC?></td>
							</tr>
<?
			for ($count_status = 0; $count_status < 4; $count_status++) {
				$row_style = ($row_count%2) ? 'ordersListingEven' : 'ordersListingOdd';
				
				$supplier_tasks_status_select_sql = "	SELECT COUNT(supplier_tasks_status) AS total_status 
														FROM " . TABLE_SUPPLIER_TASKS_ALLOCATION . " 
														WHERE supplier_tasks_status ='" . (int)$count_status . "' 
															AND suppliers_id ='" . (int)$supplier_id . "'";
				$supplier_tasks_status_result_sql = tep_db_query($supplier_tasks_status_select_sql);
				$supplier_tasks_status_row = tep_db_fetch_array($supplier_tasks_status_result_sql);
?>
							<tr id="<?=$status.'_main_'.$row_count?>" class="<?=$row_style?>">
								<td class="ordersRecords" align="left" width="75%">
									<?
									$search_by_url = $count_status;
									echo $status_name[$count_status];
									?>
								</td>
								<td class="ordersRecords" align="left"><a href="<?=tep_href_link(FILENAME_SUPPLIER_PROGRESS_REPORT, 'action=search&searchby='.$search_by_url)?>"><?=$supplier_tasks_status_row["total_status"]?></a></td>
							</tr>
<?
				$row_count++;
			}

			$bill_select_sql = "	SELECT count(supplier_tasks_status) AS bill 
									FROM " . TABLE_SUPPLIER_TASKS_ALLOCATION . " 
									WHERE suppliers_id ='" . (int)$supplier_id . "' 
										AND supplier_tasks_billing_status = '1'
										AND supplier_tasks_status = '4'";
			$bill_result_sql = tep_db_query($bill_select_sql);
			$bill_row = tep_db_fetch_array($bill_result_sql);
			
			$not_bill_select_sql = "	SELECT count(supplier_tasks_status) AS not_bill 
										FROM " . TABLE_SUPPLIER_TASKS_ALLOCATION . " 
										WHERE suppliers_id ='" . (int)$supplier_id . "' 
											AND supplier_tasks_billing_status = '0'
											AND supplier_tasks_status = '4'";
			$not_bill_result_sql = tep_db_query($not_bill_select_sql);
			$not_bill_row = tep_db_fetch_array($not_bill_result_sql);
?>
							<tr class="ordersListingOdd">
								<td class="ordersRecords" align="left" width="75%"><?=$status_name[4] . ' (' . TEXT_NOT_BILLED . ' / ' . TEXT_BILLED . ')'?></td>
								<td class="ordersRecords" align="left">
								<?
									echo '<a href="' . tep_href_link(FILENAME_SUPPLIER_PROGRESS_REPORT, 'action=search&searchby=4&bill=0') . '">' . $not_bill_row['not_bill'] . '</a>' . ' / ';
									echo '<a href="' . tep_href_link(FILENAME_SUPPLIER_PROGRESS_REPORT, 'action=search&searchby=4&bill=1') . '">' . $bill_row['bill'] . '</a>';
								?>
								</td>
							</tr>
						</table>
					</td>
				</tr>
				<tr>
					<td class="ordersRecords" align="center" colspan="2">
						<a href="<?=tep_href_link(FILENAME_SUPPLIER_PROGRESS_REPORT, 'action=advanced_search')?>"><?=LINK_ADVANCED_SEARCH?></a>
					</td>
				</tr>
			</table>
<?	} ?>
		</td>
	</tr>
</table>
</body>
</html>

<script language="javascript"><!--
	function resetControls(controlObj) {
		if (controlObj.id == 'custom_product_product_name') {
			document.custom_product_criteria.custom_product_order_id.value = '';
		} else if (controlObj.id == 'custom_product_order_id') {
			document.custom_product_criteria.custom_product_product_name.value = '';
			document.custom_product_criteria.start_date.value = '';
			document.custom_product_criteria.end_date.value = '';
		}
	}
	
	function form_checking(form_obj, action) {
		//form_obj.submit();
		return true;
	}
--></script>