<?
require('includes/application_top.php');

$action = (isset($HTTP_GET_VARS['action']) ? $HTTP_GET_VARS['action'] : '');

$process = false;

if (tep_not_null($action)) {
	switch ($action) {
		case 'new':
		case 'edit':
			if (strtolower($_SERVER['REQUEST_METHOD']) == 'post') {
				$process = true;
				$supplier_crew_id = tep_db_prepare_input($HTTP_POST_VARS['supplier_crew_id']);
				$sc_status = tep_db_prepare_input($HTTP_POST_VARS['sc_status']);
			    $sc_firstname = tep_db_prepare_input($HTTP_POST_VARS['sc_firstname']);
			    $sc_lastname = tep_db_prepare_input($HTTP_POST_VARS['sc_lastname']);
			    $sc_username = tep_db_prepare_input($HTTP_POST_VARS['sc_username']);
			    $sc_password = tep_db_prepare_input($HTTP_POST_VARS['sc_password']);
			    
				$error = false; // reset error flag
				
				if ($sc_status != '1' && $sc_status != '2') {
					$error = true;
					$entry_status_error = true;
				} else {
					$entry_status_error = false;
				}
				
				if ($sc_status == '1') {
					$activechecked = true;
				} else if ($sc_status == '2') {
					$inactivechecked = true;
				}
				
			    if (strlen($sc_firstname) < ENTRY_FIRST_NAME_MIN_LENGTH) {
			    	$error = true;
			      	$entry_firstname_error = true;
			    } else {
			    	$entry_firstname_error = false;
			    }
				
			    if (strlen($sc_lastname) < ENTRY_LAST_NAME_MIN_LENGTH) {
			      	$error = true;
			      	$entry_lastname_error = true;
			    } else {
			    	$entry_lastname_error = false;
			    }
			    
			    if (strlen($sc_username) < ENTRY_USER_NAME_MIN_LENGTH) {
			    	$error = true;
			    	$entry_username_error = true;
			    } else {
			    	if (preg_match('/\s/', $sc_username)) {
			    		$error = true;
			    		$entry_username_error = true;
			    	} else {
			    		$supplier_crew_id_select_sql = "SELECT supplier_crew_id FROM " . TABLE_SUPPLIER_CREW . " WHERE supplier_crew_username = '" . tep_db_input($sc_username) . "' AND supplier_crew_id != '" . tep_db_input($supplier_crew_id) . "' AND supplier_id ='" . tep_db_input($supplier_id) . "'";
			    		$supplier_crew_id_result_sql = tep_db_query($supplier_crew_id_select_sql);
			    		
			    		if (tep_db_num_rows($supplier_crew_id_result_sql) > 0) {
			    			$error = true;
			    			$messageStack->add_session(TEXT_MSG_USER_NAME_EXISTED);
			    			
			    			$parameter = 'action='.$action;
			    			
			    			if ($action == 'edit') {
			    				$parameter .= '&scID='.$supplier_crew_id;
			    			}
			    			
			    			tep_redirect(tep_href_link(FILENAME_SUPPLIER_STAFF, $parameter));
			    		} else {
			    			$entry_username_error = false;
			    		}
			    	}
			    }
				
				if (($sc_password!='' && $action == 'edit') || $action == 'new') {
					if (strlen($sc_password) < ENTRY_PASSWORD_MIN_LENGTH) {
						$error = true;
						$entry_password_error = true;
					}
					
				    if ($sc_password != $sc_confirmation) {
				      	$error = true;
				      	$entry_password_error_not_match = true;
				    } else {
				      	$entry_password_error_not_match = false;
				    }
				}
			    
			    if (!$error) {
					$sql_data_array = array('supplier_crew_firstname' => $sc_firstname,
			                              	'supplier_crew_lastname' => $sc_lastname,
			                              	'supplier_crew_username' => $sc_username,
			                              	'supplier_crew_status' => (($sc_status == '1') ? 1 : 0)
			                              	);
					
					if ($sc_password!='') {
						$sql_data_array['supplier_crew_password'] = tep_encrypt_password($sc_password);
					}
					
					if ($_REQUEST['action'] == 'edit') {
						$sql_data_array['supplier_crew_date_account_last_modified'] = 'now()';
						tep_db_perform(TABLE_SUPPLIER_CREW, $sql_data_array, 'update', " supplier_crew_id='".tep_db_input($supplier_crew_id)."'");
			    	} else if ($_REQUEST['action'] == 'new') {
			    		$sql_data_array['supplier_crew_date_account_created'] = 'now()';
			    		$sql_data_array['supplier_id'] = $supplier_id;
			    		tep_db_perform(TABLE_SUPPLIER_CREW, $sql_data_array);
			    	}
			    	
					$messageStack->add_session(TEXT_MSG_UPDATE_SUCCESS, 'success');
			      	tep_redirect(tep_href_link(FILENAME_SUPPLIER_STAFF));
			      	break;
				}
			}
			break;
		case 'deleteconfirm':
			$supplier_crew_name_select_sql = "SELECT supplier_crew_firstname, supplier_crew_lastname FROM " . TABLE_SUPPLIER_CREW . " WHERE supplier_crew_id='" . tep_db_input($HTTP_GET_VARS['scID']) . "'";
			$supplier_crew_name_result_sql = tep_db_query($supplier_crew_name_select_sql);
			$supplier_crew_name_row = tep_db_fetch_array($supplier_crew_name_result_sql);
			
			$supplier_id_replacement = sprintf(TEXT_INFO_UPDATE_LOG_SUPPLIER_STAFF_DELETE, $supplier_crew_name_row["supplier_crew_firstname"] . ' ' . $supplier_crew_name_row["supplier_crew_lastname"], date("Y-m-d"));
			
			$update_mac_add_table_sql = "UPDATE " . TABLE_SUPPLIER_CREW_MAC_ADDRESS . " SET request_by_supplier_crew_id='" . tep_db_input($supplier_id_replacement) . "' WHERE request_by_supplier_crew_id='" . (int)$HTTP_GET_VARS['scID'] . "'";
        	tep_db_query($update_mac_add_table_sql);
			
			$delete_supplier_crew_sql = "DELETE FROM " . TABLE_SUPPLIER_CREW . " WHERE supplier_crew_id = '" . (int)$HTTP_GET_VARS['scID'] . "'";
			tep_db_query($delete_supplier_crew_sql);
			$messageStack->add_session(TEXT_MSG_DELETE_SUCCESS, 'success');
			tep_redirect(tep_href_link(FILENAME_SUPPLIER_STAFF));
			break;
	}
}

if ($action == 'edit') {
	$supplier_crew_id = (int)$_REQUEST["scID"];
	
	$supplier_crew_select_sql = "SELECT * FROM " . TABLE_SUPPLIER_CREW . " WHERE supplier_crew_id ='" . tep_db_input($supplier_crew_id) . "' AND supplier_id ='" . tep_db_input($supplier_id) . "'";
	$supplier_crew_result_sql = tep_db_query($supplier_crew_select_sql);
	if ($supplier_crew_row = tep_db_fetch_array($supplier_crew_result_sql)) {
		$sc_status = $supplier_crew_row['supplier_crew_status'];
		$sc_firstname = $supplier_crew_row['supplier_crew_firstname'];
		$sc_lastname = $supplier_crew_row['supplier_crew_lastname'];
		$sc_username = $supplier_crew_row['supplier_crew_username'];
		$sc_password = '';
	} else {
		$messageStack->add_session(TEXT_MSG_STAFF_NOT_EXIST);
		//tep_redirect(tep_href_link(FILENAME_SUPPLIER_STAFF));
	}
	
	if ($sc_status == '1') {
		$activechecked = true;
	} else {
		$inactivechecked = true;
	}
}
?>

<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=<?php echo CHARSET; ?>">
	<title><?=TITLE?></title>
<?
if (file_exists(DIR_WS_LANGUAGES . $sup_language . '/stylesheet.css')) {
	echo '<link rel="stylesheet" type="text/css" href="'. DIR_WS_LANGUAGES . $sup_language . '/stylesheet.css">';
} else {
	echo '<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">';
}
?>
<script language="javascript" src="includes/general.js"></script>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">
<!-- header //-->
<? require(DIR_WS_INCLUDES . 'header.php'); ?>
<!-- header_eof //-->
<?
if ($action == 'edit' || $action == 'new') {
?>
	<form action="<?=tep_href_link(FILENAME_SUPPLIER_STAFF, 'action='.$action . '&scID='.$scID)?>" method="post">
		<?=tep_draw_hidden_field('supplier_crew_id', $supplier_crew_id)?>
		<table width="733" border="0" height="100%" align="center" cellpadding="0" cellspacing="2" valign="top">
	  		<tr>
	    		<td width="62"><p>&nbsp;</p></td>
	  		    <td width="556" class="pageHeading"><b><?=HEADING_TITLE_EDIT_SUB_USER_PROFILE?></b></td>
	  		    <td width="40" valign="bottom">&nbsp;</td>
	  		    <td width="63" class="login_heading">&nbsp;</td>
	  		</tr>
	  		<tr>
	  		  	<td colspan="4" valign="top">
	  		  		<table width="600" height="340" border="0" align="center" cellpadding="1" cellspacing="0" class="smallText" valign="middle">
	           			<tr bgcolor="#000000">
	              			<td>
	              				<table border="0" width="600" height="340" cellspacing="0" cellpadding="0">
	                  				<tr bgcolor="#ffffff" height="50">
	                    				<td align="left" valign="middle" bgcolor="#F0F0FF">
	                    					<table class="inputBox" border="0" cellpadding="2" cellspacing="1" width="100%">
		                      					<tbody>
		                        					<tr class="inputBoxContents">
		                          						<td>
		                          							<table width="100%" border="0" cellpadding="2" cellspacing="0" class="title">
			                              						<tbody>
			                                						<tr>
			                                  							<td class="formAreaTitle"><?=CATEGORY_PERSONAL?></td>
			                                  							<td class="fieldRequired" align="right"><?=FORM_REQUIRED_INFORMATION?></td>
			                                						</tr>
			                              						</tbody>
		                            						</table>
		                              						<table border="0" cellpadding="2" cellspacing="0" width="100%">
			                                					<tbody>
			                                  						<tr>
			                                    						<td>
			                                    							<table class="inputNestedBox" border="0" cellpadding="2" cellspacing="1" width="100%">
				                                        						<tbody>
				                                          							<tr class="inputNestedBoxContents">
				                                            							<td>
				                                            								<table width="100%" border="0" cellpadding="2" cellspacing="2" class="login">
					                                                							<tbody>
					                                                  								<tr>
					                                                    								<td class="inputLabel" width="30%"><?=ENTRY_STATUS?></td>
					                                                    								<td class="inputField">
					                                                    									<?
						                                                    									if ($_REQUEST['action'] == 'edit') {
						                                                    										echo tep_draw_radio_field('sc_status', '1', $activechecked) . TEXT_ACTIVE . "&nbsp;&nbsp";
						                                                    										echo tep_draw_radio_field('sc_status', '2', $inactivechecked) . TEXT_NOT_ACTIVE;
						                                                    									} else if ($_REQUEST['action'] == 'new') {
						                                                    										echo tep_draw_radio_field('sc_status', '1') . TEXT_ACTIVE . "&nbsp;&nbsp";
						                                                    										echo tep_draw_radio_field('sc_status', '2') . TEXT_NOT_ACTIVE;
						                                                    									}
					                                                    									
							                                                    								if ($entry_status_error) {
							                                                    									echo ENTRY_STATUS_ERROR;
							                                                    								} else {
							                                                    									echo '&nbsp;&nbsp;&nbsp;<span class="fieldRequired">*</span>';
							                                                    								}
							                                                    							?>
					                                													</td>
					                                                  								</tr>
					                                                  								<tr>
					                                                    								<td class="inputLabel"><?=ENTRY_FIRST_NAME?></td>
					                                                    								<td class="inputField">
					                                                    									<?
					                                                    										if ($_REQUEST['action'] == 'edit') {
					                                                    											echo tep_draw_input_field('sc_firstname',$sc_firstname);
					                                                    										} else if ($_REQUEST['action'] == 'new') {
					                                                    											echo tep_draw_input_field('sc_firstname');
					                                                    										}
					                                                    										
							                                                    								if ($entry_firstname_error) {
							                                                    									echo ENTRY_FIRST_NAME_ERROR;
							                                                    								} else {
							                                                    									echo '&nbsp;&nbsp;<span class="fieldRequired">*</span>';
							                                                    								}
							                                                    							?>
						                                                    							</td>
					                                                  								</tr>
					                                                  								<tr>
					                                                    								<td class="inputLabel"><?=ENTRY_LAST_NAME?></td>
					                                                    								<td class="inputField">
					                                                    									<?
					                                                    										if ($_REQUEST['action'] == 'edit') {
					                                                    											echo tep_draw_input_field('sc_lastname',$sc_lastname);
					                                                    										} else if ($_REQUEST['action'] == 'new') {
					                                                    											echo tep_draw_input_field('sc_lastname');
					                                                    										}
					                                                    										
							                                                    								if ($entry_lastname_error) {
							                                                    									echo ENTRY_LAST_NAME_ERROR;
							                                                    								} else {
							                                                    									echo '&nbsp;&nbsp;<span class="fieldRequired">*</span>';
							                                                    								}
							                                                    							?>
					                                                    								</td>	
					                                                  								</tr>
					                                                  								<tr>
					                                                  									<td class="inputLabel"><?=ENTRY_USER_NAME?></td>
					                                                    								<td class="inputField">
					                                                    									<?
					                                                    										if ($_REQUEST['action'] == 'edit') {
					                                                    											echo tep_draw_input_field('sc_username',$sc_username);
					                                                    										} else if ($_REQUEST['action'] == 'new') {
					                                                    											echo tep_draw_input_field('sc_username');
					                                                    										}
					                                                    										
							                                                    								if ($entry_username_error) {
							                                                    									echo ENTRY_USER_NAME_ERROR;
							                                                    								} else {
							                                                    									echo '&nbsp;&nbsp;<span class="fieldRequired">*</span>';
							                                                    								}
							                                                    							?>
					                                                    								</td>	
					                                                  								</tr>
					                                                							</tbody>
				                                            								</table>
				                                            							</td>
				                                          							</tr>
				                                        						</tbody>
			                                    							</table>
			                                    						</td>
			                                  						</tr>
			                                  						<tr>
			                                    						<td><?=tep_draw_separator('pixel_trans.gif')?></td>
			                                  						</tr>
			                                  						<tr>
			                                    						<td class="formAreaTitle"><?=CATEGORY_PASSWORD?></td>
			                                  						</tr>
			                                  						<tr>
			                                    						<td>
			                                    							<table class="inputNestedBox" border="0" cellpadding="2" cellspacing="1" width="100%">
				                                        						<tbody>
				                                          							<tr class="inputNestedBoxContents">
				                                            							<td>
				                                            								<table width="100%" border="0" cellpadding="2" cellspacing="2" class="login">
					                                                            				<tbody>
					                                                  								<tr>
													                                                    <td class="inputLabel" width="30%"><?=ENTRY_PASSWORD?></td>
													                                                    <td class="inputField">
													                                                    	<?=tep_draw_input_field('sc_password',$sc_password, '', '', 'password')?>
													                                                    	<?
																											  	if ($entry_password_error == true) {
																											   		echo ENTRY_PASSWORD_ERROR;
																											    } else {
																											    	echo '&nbsp;<span class="fieldRequired">*</span>';
																											  	}
							                                                    							?>
													                                                    </td>
					                                                  								</tr>
					                                                  								<tr>
					                                                    								<td class="inputLabel"><?=ENTRY_PASSWORD_CONFIRMATION?></td>
					                                                    								<td class="inputField">
					                                                    									<?=tep_draw_input_field('sc_confirmation', $sc_confirmation, '', '', 'password')?>
					                                                    									<?
																											  	if (!$entry_password_error && $entry_password_error_not_match == true) {
																											   		echo ENTRY_PASSWORD_ERROR_NOT_MATCHING;
																											    } else {
																											    	echo '&nbsp;<span class="fieldRequired">*</span>';
																											  	}
							                                                    							?>
					                                                    								</td>
					                                                  								</tr>
					                                                							</tbody>
				                                            								</table>
				                                            							</td>
				                                          							</tr>
				                                        						</tbody>
			                                    							</table>
			                                    						</td>
			                                  						</tr>
			                                  						<tr>
			                                    						<td><?=tep_draw_separator('pixel_trans.gif')?></td>
			                                  						</tr>
			                                					</tbody>
		                            						</table>
		                            					</td>
		                        					</tr>
		                        					<tr>
					                      				<td>
					              							<table class="buttonBox2" border="0" cellpadding="2" cellspacing="1" width="100%">
						                  						<tbody>
						                    						<tr class="buttonBoxContents2">
									                                  	<td width="10">&nbsp;</td>
									                                  	<td align="right">
																		<?
																			if ($_REQUEST['action'] == 'edit') {
									                                  			echo tep_submit_button(IMAGE_UPDATE, IMAGE_UPDATE, '', 'inputButton');
																			} else if ($_REQUEST['action'] == 'new') {
																				echo tep_submit_button(IMAGE_SAVE, IMAGE_SAVE, '', 'inputButton');
																			}
																		?>
									                                  	</td>
									                                  	<td width="10">&nbsp;</td>
						                    						</tr>
						                  						</tbody>
					              							</table>
					              						</td>
					            					</tr>
		                      					</tbody>
	                    					</table>
	                    				</td>
	                  				</tr>
	              				</table>
	              			</td>
	            		</tr>
	            		<tr>
	              			<td><? require(DIR_WS_INCLUDES . 'footer.php'); ?></td>
	            		</tr>
	          		</table>
	          	</td>
		  	</tr>
		</table>
	</form>
<?
} else {
?>
	<table border="0" width="35%" cellspacing="1" cellpadding="2" align="center">
		<tr>
			<td colspan="5"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
		</tr>
		<tr>
			<td colspan="5">
				<a href="<?=tep_href_link(FILENAME_SUPPLIER_STAFF, 'action=new')?>"><?=LINK_ADD_SUB_USER?></a>
			</td>
		</tr>
		<tr>
			<td colspan="5"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
		</tr>
		<tr>
			<td class="ordersBoxHeading"><?=TABLE_HEADING_FIRST_NAME?></td>
			<td class="ordersBoxHeading"><?=TABLE_HEADING_LAST_NAME?></td>
			<td class="ordersBoxHeading"><?=TABLE_HEADING_USER_NAME?></td>
			<td class="ordersBoxHeading"><?=TABLE_HEADING_ACC_CREATED_DATE?></td>
			<td class="ordersBoxHeading" align="center"><?=TABLE_HEADING_STATUS?></td>
			<td class="ordersBoxHeading" align="center"><?=TABLE_HEADING_ACTION?></td>
		</tr>
<?
	$supplier_staff_list_select_sql = "SELECT * FROM " . TABLE_SUPPLIER_CREW . " WHERE supplier_id ='" . (int)$supplier_id . "'";
	
	if ($show_records != "ALL") {
		$orders_split_object = new splitPageResults($HTTP_GET_VARS['page'], ($show_records=='' || $show_records=='DEFAULT' ? MAX_DISPLAY_SEARCH_RESULTS : (int) $show_records), $supplier_staff_list_select_sql, $supplier_staff_list_select_sql_numrows, true);
	}
	
	$supplier_staff_list_result_sql = tep_db_query($supplier_staff_list_select_sql);
	while ($supplier_staff_list_row = tep_db_fetch_array($supplier_staff_list_result_sql)) {
		$row_style = ($row_count%2) ? 'ordersListingEven' : 'ordersListingOdd';
?>
		<tr id="<?=$status.'_main_'.$row_count?>" class="<?=$row_style?>">
			<td class="ordersRecords"><?=$supplier_staff_list_row['supplier_crew_firstname']?></td>
			<td class="ordersRecords"><?=$supplier_staff_list_row['supplier_crew_lastname']?></td>
			<td class="ordersRecords"><?=$supplier_staff_list_row['supplier_crew_username']?></td>
			<td class="ordersRecords"><?=$supplier_staff_list_row['supplier_crew_date_account_created']?></td>
			<td class="ordersRecords" align="center">
				<?
				if ($supplier_staff_list_row['supplier_crew_status'] == '1') {
					echo tep_image(DIR_WS_IMAGES . 'icon_status_green.gif', IMAGE_ICON_STATUS_GREEN, 10, 10);
				} else {
					echo tep_image(DIR_WS_IMAGES . 'icon_status_red.gif', IMAGE_ICON_STATUS_RED, 10, 10);
				}
				?>
			</td>
			<!--td class="ordersRecords"><?=$supplier_staff_list_row['supplier_crew_date_account_last_modified']?></td-->
			<td class="ordersRecords" align="center">
				<a href="<?=tep_href_link(FILENAME_SUPPLIER_STAFF, 'scID=' . $supplier_staff_list_row['supplier_crew_id'] . '&action=edit')?>"><?=tep_image(DIR_WS_ICONS."edit.gif", "Edit", "", "", 'align="top"')?></a>
				<?echo '<a href="javascript:void(confirm_delete(\'\', \'this staff details\', \''.tep_href_link(FILENAME_SUPPLIER_STAFF, tep_get_all_get_params(array('scID', 'action')) . 'scID=' . $supplier_staff_list_row['supplier_crew_id'] . '&action=deleteconfirm').'\'))">'.tep_image(DIR_WS_ICONS."delete.gif", IMAGE_DELETE).'</a>&nbsp;';?>
			</td>
		</tr>
<?
		$row_count++;
	}
?>
		<tr>
			<td colspan="5">
				<table border="0" width="100%" cellspacing="0" cellpadding="2">
					<tr>
						<td class="smallText" valign="top"><?=$show_records == "ALL" ? sprintf(TEXT_DISPLAY_NUMBER_OF_SUB_USERS, tep_db_num_rows($supplier_staff_list_select_sql) > 0 ? "1" : "0", tep_db_num_rows($supplier_staff_list_select_sql), tep_db_num_rows($supplier_staff_list_select_sql)) : $orders_split_object->display_count($supplier_staff_list_select_sql_numrows, ($show_records=='' || $show_records=='DEFAULT' ? MAX_DISPLAY_SEARCH_RESULTS : (int) $show_records), $HTTP_GET_VARS['page'], TEXT_DISPLAY_NUMBER_OF_SUB_USERS)?></td>
						<td class="smallText" align="right"><?=$show_records == "ALL" ? "Page 1 of 1" : $orders_split_object->display_links($supplier_staff_list_select_sql_numrows, ($show_records=='' || $show_records=='DEFAULT' ? MAX_DISPLAY_SEARCH_RESULTS : (int) $show_records), MAX_DISPLAY_PAGE_LINKS, $HTTP_GET_VARS['page'], tep_get_all_get_params(array('page', 'cont', 'criteria_id'))."cont=1", 'page')?></td>
					</tr>
				</table>
			</td>
		</tr>
<?
}
?>
</body>
</html>