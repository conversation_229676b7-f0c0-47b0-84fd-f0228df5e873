<?
// PHP5 Compatible
$HTTP_SERVER_VARS = $_SERVER;
$HTTP_POST_VARS = $_POST;
$HTTP_GET_VARS = $_GET;
$HTTP_COOKIE_VARS = $_COOKIE;

include_once('includes/configure.php');
include_once(DIR_WS_FUNCTIONS . 'general.php');
include_once(DIR_WS_FUNCTIONS . 'html_output.php');
include_once(DIR_WS_INCLUDES . 'filenames.php');
include_once(DIR_WS_FUNCTIONS . 'database.php');
include_once(DIR_WS_INCLUDES . 'database_tables.php');
require_once(DIR_WS_FUNCTIONS . 'luaparser.php');
include_once(DIR_WS_FUNCTIONS . 'progress_report_task.php');
include_once(DIR_WS_FUNCTIONS . 'configuration.php');
require_once(DIR_WS_CLASSES . 'email.php');
require_once(DIR_WS_CLASSES . 'mime.php');

define('TEXT_SUPPLIER', 'supplier');

tep_db_connect() or die('Unable to connect to database server!');

// set application wide parameters
$configuration_query = tep_db_query('select configuration_key as cfgKey, configuration_value as cfgValue from ' . TABLE_CONFIGURATION);
while ($configuration = tep_db_fetch_array($configuration_query)) {
	define($configuration['cfgKey'], $configuration['cfgValue']);
}

if (file_exists(DIR_WS_LANGUAGES . '/english.php')) {
	include_once(DIR_WS_LANGUAGES . '/english.php');
}

if (file_exists(DIR_WS_LANGUAGES . '/english/' . FILENAME_SUPPLIER_UI_SUBMIT)) {
	include_once(DIR_WS_LANGUAGES . '/english/' . FILENAME_SUPPLIER_UI_SUBMIT);
}

$action = (isset($HTTP_POST_VARS['action']) ? tep_db_prepare_input($HTTP_POST_VARS['action']) : '');
$version = (isset($HTTP_POST_VARS['versions']) ? tep_db_prepare_input($HTTP_POST_VARS['versions']) : '');

if (tep_not_null($action)) {
	$game_char_log_login_user = trim($HTTP_POST_VARS['login_user']);
	
	if (trim($HTTP_POST_VARS['user_role']) == 'supplier') {
		$supplier_id_select_sql = "	SELECT supplier_id FROM " . TABLE_SUPPLIER . " WHERE supplier_email_address = '" . tep_db_input(trim($HTTP_POST_VARS['login_user'])) . "'";
		$supplier_id_result_sql = tep_db_query($supplier_id_select_sql);
		if ($supplier_id_row = tep_db_fetch_array($supplier_id_result_sql)) {
			$game_char_log_login_user = $supplier_id_row['supplier_id'];
		}
		
		$id = $supplier_id_row['supplier_id'];
	} else {
		$admin_id_select_sql = "	SELECT admin_id FROM " . TABLE_ADMIN . " WHERE admin_email_address = '" . tep_db_input(trim($HTTP_POST_VARS['login_user'])) . "'";
		$admin_id_result_sql = tep_db_query($admin_id_select_sql);
		if ($admin_id_row = tep_db_fetch_array($admin_id_result_sql)) {
			$game_char_log_login_user = $admin_id_row['admin_id'];
		}
	}
	
	switch($action) {
		case 'post_contents':
			if (tep_not_null($HTTP_POST_VARS['lua_logs_contents'])) {
				$outgoing_currency = 0;
				$data = lua_parse(explode("\n", tep_db_prepare_input(trim($HTTP_POST_VARS['lua_logs_contents']))));
				$lua_contents = tep_db_prepare_input($HTTP_POST_VARS['lua_logs_contents']);
				$lua_contents = preg_replace('/\t/', '', $lua_contents);
				$order_id = (isset($HTTP_POST_VARS['order_id']) ? tep_db_prepare_input($HTTP_POST_VARS['order_id']) : '');
				$orders_products_id = tep_db_prepare_input($HTTP_POST_VARS['orders_products_id']);
				
				$profiler_info = false;
				$mail_trade_info = false;
				
				if (isset($data['rpgoCPpref'])) {
					if (tep_not_null($orders_products_id)) {
						$profiler_info = true;
					}
				}
				
				if (isset($data['MailLog'])) {
					if (tep_not_null($orders_products_id)) {
						$mail_trade_info = true;
					}
				}
				
				if (isset($data['myProfile'])) {
					if (tep_not_null($orders_products_id)) {
						$myProfile = $data['myProfile'];
						foreach(array_keys($myProfile) as $realm_name) {
							$realm = $myProfile[$realm_name];
							
							foreach(array_keys($realm) as $char_name) {
								$char = $realm[$char_name];
								foreach (array_keys($char) as $character_name) {
									$profiler = $char[$character_name];
									update_char($character_name, $profiler, $orders_products_id, $lua_contents);
								}
							}
						}
					}
				}
				
				if (isset($data['MailLogSetting'])) {
					$game_realm = "";
					$game_char_name = "";
					$game_char_race = "";
					$game_item_info_id_mail_send_list = "";
					$game_item_info_id_mail_receive_list = "";
					
					foreach ($data['MailLogSetting'] as $server => $realm_info_array) {
						if (isset($realm_info_array)) {
							foreach ($realm_info_array as $realm => $char_info_array) {
								$game_realm = $realm;
								
								if (isset($char_info_array)) {
									foreach ($char_info_array as $char_name => $char_info_detail_array) {
										$game_char_name = $char_name;
										$game_char_race = $char_info_detail_array['race'];
										$game_item_info_id_mail_send_list = '';
										
										if (isset($char_info_detail_array['Mail'])) {
											foreach ($char_info_detail_array['Mail'] as $mail_count => $mail_detail_array) {
												$outgoing_currency += (int)$mail_detail_array['Money'];
												
												if (isset($mail_detail_array['ItemName'])) {
													$game_item_info_id_select_sql = "SELECT game_item_info_id FROM " . TABLE_GAME_ITEM_INFO . " WHERE game_item_info_name ='" . tep_db_input($mail_detail_array['ItemName']) . "'";
													$game_item_info_id_result_sql = tep_db_query($game_item_info_id_select_sql);
													$game_item_info_id_row = tep_db_fetch_array($game_item_info_id_result_sql);
													
													if (tep_not_null($game_item_info_id_row['game_item_info_id'])) {
														$game_item_send_info_id = $game_item_info_id_row['game_item_info_id'];
													} else {
														$sql_data_array = array('game_item_info_name' => $mail_detail_array['ItemName'],
																				'game_item_info_texture' => $mail_detail_array['ItemTexture']);
														
														tep_db_perform(TABLE_GAME_ITEM_INFO, $sql_data_array);
														
														$game_item_send_info_id = tep_db_insert_id();
													}
													$game_item_info_id_mail_send_list .= 's_' . $game_item_send_info_id . '_' . $mail_detail_array['ItemCount'] . ",";
													
													if ($game_item_info_id_mail_send_list{strlen($game_item_info_id_mail_send_list)-1} == ",") {
														$game_item_info_id_mail_send_list = substr($game_item_info_id_mail_send_list, 0, strlen($game_item_info_id_mail_send_list)-1);
													}
												}
												
												$sql_data_array = array('game_char_log_time' => 'now()',
										                              	'game_char_log_account_name' => tep_db_prepare_input($HTTP_POST_VARS['account_name']),
										                              	'game_char_log_server' => tep_db_prepare_input($HTTP_POST_VARS['server']),
										                              	'game_char_log_realm' => $game_realm,
																		'game_char_log_race' => $game_char_race,
										                              	'game_char_log_sender' => $game_char_name,
										                               	'game_char_log_receiver' => $mail_detail_array['Receiver'],
										                               	'game_char_log_subject' => $mail_detail_array['Subject'],
										                               	'game_char_log_messages' => $mail_detail_array['Message'],
										                               	'game_char_log_system_messages' => ((int)$mail_detail_array['COD'] > 0) ? "COD: " . $mail_detail_array['COD'] : "",
										                              	'game_char_log_balance_before' => $mail_detail_array['BalanceBefore'],
																		'game_char_log_balance_after' => $mail_detail_array['BalanceAfter'],
																		'game_char_log_send' => $mail_detail_array['Money'] . ':~:' . $game_item_info_id_mail_send_list,
										                              	'game_char_log_type' => 'mail',
										                              	'game_char_log_login_as' => $game_char_name,
																		'game_char_log_computer_name' => tep_db_prepare_input($HTTP_POST_VARS['computer_name']),
																		'game_char_log_login_user' => $game_char_log_login_user,
																		'game_char_log_user_role' => tep_db_prepare_input($HTTP_POST_VARS['user_role']));
												
												if (tep_not_null($order_id) && $order_id != '0') {
													$sql_data_array['game_char_log_orders_id'] = $order_id;
												}
												
										        tep_db_perform(TABLE_GAME_CHAR_LOG, $sql_data_array);
										        
										        $game_item_info_id_mail_send_list = '';
											}
										}
										
										if (isset($char_info_detail_array['Inbox'])) {
											foreach ($char_info_detail_array['Inbox'] as $inbox_mail_count => $inbox_mail_detail_array) {
												
												if (isset($inbox_mail_detail_array['ItemName'])) {
													$game_item_info_id_select_sql = "SELECT game_item_info_id FROM " . TABLE_GAME_ITEM_INFO . " WHERE game_item_info_name ='" . tep_db_input($inbox_mail_detail_array['ItemName']) . "'";
													$game_item_info_id_result_sql = tep_db_query($game_item_info_id_select_sql);
													$game_item_info_id_row = tep_db_fetch_array($game_item_info_id_result_sql);
												
													if (tep_not_null($game_item_info_id_row['game_item_info_id'])) {
														$game_item_receive_info_id = $game_item_info_id_row['game_item_info_id'];
													} else {
														$sql_data_array = array('game_item_info_name' => $inbox_mail_detail_array['ItemName'],
																				'game_item_info_texture' => $inbox_mail_detail_array['ItemTexture']);
														
														tep_db_perform(TABLE_GAME_ITEM_INFO, $sql_data_array);
														
														$game_item_receive_info_id = tep_db_insert_id();
													}
													$game_item_info_id_mail_receive_list .= 'r_' . $game_item_receive_info_id . "_" . $inbox_mail_detail_array['ItemCount'] . ",";
													
													if ($game_item_info_id_mail_receive_list{strlen($game_item_info_id_mail_receive_list)-1} == ",") {
														$game_item_info_id_mail_receive_list = substr($game_item_info_id_mail_receive_list, 0, strlen($game_item_info_id_mail_receive_list)-1);
													}
												}
												
												$sql_data_array = array('game_char_log_time' => 'now()',
										                              	'game_char_log_account_name' => tep_db_prepare_input($HTTP_POST_VARS['account_name']),
										                              	'game_char_log_server' => tep_db_prepare_input($HTTP_POST_VARS['server']),
										                              	'game_char_log_realm' => $game_realm,
																		'game_char_log_race' => $game_char_race,
										                              	'game_char_log_sender' => $inbox_mail_detail_array['Sender'],
										                               	'game_char_log_receiver' => $game_char_name,
										                               	'game_char_log_subject' => $inbox_mail_detail_array['Subject'],
										                               	'game_char_log_messages' => (isset($inbox_mail_detail_array['Message']) ? $inbox_mail_detail_array['Message'] : ''),
										                               	'game_char_log_system_messages' => ((int)$inbox_mail_detail_array['COD'] > 0) ? "COD: " . $inbox_mail_detail_array['COD'] : "",
										                              	'game_char_log_balance_before' => $inbox_mail_detail_array['BalanceBefore'],
																		'game_char_log_balance_after' => $inbox_mail_detail_array['BalanceAfter'],
																		'game_char_log_receive' => $inbox_mail_detail_array['Money'] . ':~:' . $game_item_info_id_mail_receive_list,
										                              	'game_char_log_type' => 'mail',
										                              	'game_char_log_login_as' => $game_char_name,
																		'game_char_log_computer_name' => tep_db_prepare_input($HTTP_POST_VARS['computer_name']),
																		'game_char_log_login_user' => $game_char_log_login_user,
																		'game_char_log_user_role' => tep_db_prepare_input($HTTP_POST_VARS['user_role']));
																		
												if (tep_not_null($order_id) && $order_id != '0') {
													$sql_data_array['game_char_log_orders_id'] = $order_id;
												}
										        
										        tep_db_perform(TABLE_GAME_CHAR_LOG, $sql_data_array);
										        
										        $game_item_info_id_mail_receive_list = '';
											}
										}
									}
								}
							}
						}
					}
				}
					
				if (isset($data['TradeLogSetting'])) {
					$game_server = "";
					$game_realm = "";
					$game_char_name = "";
					$game_char_race = "";
					$game_item_info_id_send_list = "";
					$game_item_info_id_receive_list = "";
					
					foreach ($data['TradeLogSetting'] as $server => $realm_info_array) {
						$game_server = $server;
						
						if (isset($realm_info_array)) {
							foreach ($realm_info_array as $realm => $char_info_array) {
								$game_realm = $realm;
								
								if (isset($char_info_array)) {
									foreach ($char_info_array as $char_name => $char_info_detail_array) {
										$game_char_name = $char_name;
										$game_char_race = $char_info_detail_array['race'];
										
										if (isset($char_info_detail_array['Trade'])) {
											foreach ($char_info_detail_array['Trade'] as $trade_count => $trade_detail_array) {
												if (isset($trade_detail_array['player_item_trade'])) {
													foreach ($trade_detail_array['player_item_trade'] as $trade_slot => $item_send_info_array) {
														
														$game_item_info_id_select_sql = "SELECT game_item_info_id FROM " . TABLE_GAME_ITEM_INFO . " WHERE game_item_info_name ='" . tep_db_input($item_send_info_array['Name']) . "'";
														$game_item_info_id_result_sql = tep_db_query($game_item_info_id_select_sql);
														$game_item_info_id_row = tep_db_fetch_array($game_item_info_id_result_sql);
														
														if (tep_not_null($game_item_info_id_row['game_item_info_id'])) {
															$game_item_send_info_id = $game_item_info_id_row['game_item_info_id'];
														} else {
															$sql_data_array = array('game_item_info_name' => $item_send_info_array['Name'],
																					'game_item_info_texture' => $item_send_info_array['Texture']);
																					
															tep_db_perform(TABLE_GAME_ITEM_INFO, $sql_data_array);
															
															$game_item_send_info_id = tep_db_insert_id();
														}
														$game_item_info_id_send_list .= 's_' . $game_item_send_info_id . "_" . $item_send_info_array['Numitems'] . ",";
													}
												}
												
												if (isset($trade_detail_array['target_item_trade'])) {
													foreach ($trade_detail_array['target_item_trade'] as $trade_slot => $item_receive_info_array) {
														
														$game_item_info_id_select_sql = "SELECT game_item_info_id FROM " . TABLE_GAME_ITEM_INFO . " WHERE game_item_info_name ='" . tep_db_input($item_receive_info_array['Name']) . "'";
														$game_item_info_id_result_sql = tep_db_query($game_item_info_id_select_sql);
														$game_item_info_id_row = tep_db_fetch_array($game_item_info_id_result_sql);
														
														if (tep_not_null($game_item_info_id_row['game_item_info_id'])) {
															$game_item_receive_info_id = $game_item_info_id_row['game_item_info_id'];
														} else {
															$sql_data_array = array('game_item_info_name' => $item_receive_info_array['Name'],
																					'game_item_info_texture' => $item_receive_info_array['Texture']);
																					
															tep_db_perform(TABLE_GAME_ITEM_INFO, $sql_data_array);
															
															$game_item_receive_info_id = tep_db_insert_id();
														}
														
														$game_item_info_id_receive_list .= 'r_' . $game_item_receive_info_id . "_" . $item_receive_info_array['Numitems'] . ",";
													}
												}
												
												if (tep_not_null($game_item_info_id_send_list) && $game_item_info_id_send_list{strlen($game_item_info_id_send_list)-1} == ",") {
													$game_item_info_id_send_list = substr($game_item_info_id_send_list, 0, strlen($game_item_info_id_send_list)-1);
												}
												
												if (tep_not_null($game_item_info_id_receive_list) && $game_item_info_id_receive_list{strlen($game_item_info_id_receive_list)-1} == ",") {
													$game_item_info_id_receive_list = substr($game_item_info_id_receive_list, 0, strlen($game_item_info_id_receive_list)-1);
												}
												
												$outgoing_currency += (int)$trade_detail_array['player_money_trade'];
												
												$sql_data_array = array('game_char_log_time' => 'now()',
										                              	'game_char_log_account_name' => $HTTP_POST_VARS['account_name'],
										                              	'game_char_log_server' => $HTTP_POST_VARS['server'],
										                              	'game_char_log_realm' => $game_realm,
																		'game_char_log_race' => $game_char_race,
										                              	'game_char_log_sender' => $trade_detail_array['player'],
										                               	'game_char_log_receiver' => $trade_detail_array['target'],
																		'game_char_log_balance_before' => $trade_detail_array['BalanceBefore'],
																		'game_char_log_balance_after' => $trade_detail_array['BalanceAfter'],
										                              	'game_char_log_send' => $trade_detail_array['player_money_trade'] . ':~:' . $game_item_info_id_send_list,
										                              	'game_char_log_receive' => $trade_detail_array['target_money_trade'] . ':~:' . $game_item_info_id_receive_list,
										                              	'game_char_log_type' => 'trade',
										                              	'game_char_log_login_as' => $trade_detail_array['player'],
																		'game_char_log_computer_name' => $HTTP_POST_VARS['computer_name'],
																		'game_char_log_login_user' => $game_char_log_login_user,
																		'game_char_log_user_role' => $HTTP_POST_VARS['user_role']);
																		
												if (tep_not_null($order_id) && $order_id != '0') {
													$sql_data_array['game_char_log_orders_id'] = $order_id;
												}
										        
										        tep_db_perform(TABLE_GAME_CHAR_LOG, $sql_data_array);
										        
										        $game_item_info_id_send_list = "";
												$game_item_info_id_receive_list = "";
											}
										}
									}
								}
							}
						}
					}
				}
				echo 'success=updated';
				
				if ($HTTP_POST_VARS['user_role'] == 'supplier' && tep_not_null($order_id)) {
					$currency_array = array();
					
					$currency_array[] = array('currency' => 'Golds', 'value' => '10000');
					$currency_array[] = array('currency' => 'Silvers', 'value' => '100');
					$currency_array[] = array('currency' => 'Coppers', 'value' => '1');
					
					$cat_cfg_array = tep_get_cfg_setting($orders_products_id, 'product');
					$email_to_array = tep_parse_email_string($cat_cfg_array['GAME_ADDON_NOT_SELECTED_NOTIFICATION']);
					$addon_notification_content = "";
					$outgoing_notification_content = "";
					
					if ($profiler_info == true && $mail_trade_info == false) {
						$addon_notification_content .= "Order ID: " . $order_id . "\n";
						$addon_notification_content .= "Mail & Trade Log Add-On Not Selected";
					} else if ($profiler_info == false && $mail_trade_info == true) {
						$addon_notification_content .= "Order ID: " . $order_id . "\n";
						$addon_notification_content .= "Profiler Add-On Not Selected";
					}
					
					if ($outgoing_currency > 0) {
						$outging_currency_array = tep_generate_game_currencies($outgoing_currency, $currency_array, "Golds");
						
						$outgoing_notification_content .= "Order ID: " . $order_id . "\n";
						
						for ($count = 0; $count <sizeof($outging_currency_array); $count++) {
							foreach ($outging_currency_array[$count] as $currency_name => $value) {
								$outgoing_notification_content .= $value . " " . $currency_name . " has been sent out." . "\n";
							}
						}
						
						for ($email_to_cnt = 0; $email_to_cnt < count($email_to_array); $email_to_cnt++) {
							tep_mail($email_to_array[$email_to_cnt]['name'], $email_to_array[$email_to_cnt]['email'], 'Outgoing Currencies Notification', $outgoing_notification_content, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
						}
					}
					
					if (tep_not_null($addon_notification_content)) {
						for ($email_to_cnt = 0; $email_to_cnt < count($email_to_array); $email_to_cnt++) {
		        			tep_mail($email_to_array[$email_to_cnt]['name'], $email_to_array[$email_to_cnt]['email'], 'Add-On Notification', $addon_notification_content, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
		        		}
					}
				}
			}
			break;
		case 'error_report':
			$order_id = (isset($_POST['order_id']) ? tep_db_prepare_input(preg_replace('/[^a-zA-Z0-9]/', '', $_POST['order_id'])) : '');
			$mail_title = (isset($_POST['mail_title']) ? tep_db_prepare_input(preg_replace('/[^a-zA-Z0-9]/', '', $_POST['mail_title'])) : '');
			
			if (tep_not_null($_POST['lua_logs_contents'])) {
				$filename = date("YmdHis").'_'.$mail_title.'_'.$order_id;
				$file_location = DIR_FS_REDJACK_LOG . $filename;
				
				if (($rj_fp = @fopen($file_location, 'w')) !== false) {
					fwrite($rj_fp, $_POST['lua_logs_contents']);
					fclose($rj_fp);
				}
				echo 'success=updated';
			}
			break;
	}
}
?>
<form action="<?=FILENAME_SUPPLIER_UI_SUBMIT?>" method="post" name="mail_trade_log">

<?
echo tep_draw_hidden_field('action', 'post_contents');
echo tep_draw_hidden_field('versions');
echo tep_draw_textarea_field('lua_logs_contents', 'soft', '60', '15');
echo tep_draw_input_field('account_name');
echo tep_draw_input_field('computer_name');
echo tep_draw_input_field('server');
echo tep_draw_input_field('login_user');
echo tep_draw_input_field('user_role');
echo tep_draw_input_field('order_id');
echo tep_draw_input_field('orders_products_id');
echo tep_draw_input_field('mail_title', 'Redjack Error Report');
?>
</form>