<?
header("Cache-Control: no-cache");
header("Cache-Control: post-check=0,pre-check=0");
header("Cache-Control: max-age=0");
header("Pragma: no-cache");

header('Content-Type: text/xml');

$HTTP_SERVER_VARS = $_SERVER;
$HTTP_POST_VARS = $_POST;
$HTTP_GET_VARS = $_GET;
$HTTP_COOKIE_VARS = $_COOKIE;

include_once('includes/configure.php');
include_once(DIR_WS_INCLUDES . 'database_tables.php');
include_once(DIR_WS_FUNCTIONS . 'database.php');
include_once(DIR_WS_FUNCTIONS . 'general.php');

tep_db_connect() or die('Unable to connect to database server!');

$action = $HTTP_GET_VARS['action'];
$country_id = (int)$HTTP_GET_VARS['country_id'];
$language_id = (int)$HTTP_GET_VARS['lang'];

if (isset($language_id) && tep_not_null($language_id)) {
	$language_dir_select_sql = "SELECT directory FROM " . TABLE_LANGUAGES . " WHERE  languages_id = '" . $language_id . "'";
	$language_dir_result_sql = tep_db_query($language_dir_select_sql);
	$language_dir_row = tep_db_fetch_array($language_dir_result_sql);
	
	if (file_exists(DIR_WS_LANGUAGES . $language_dir_row["directory"] . '/' . "orders_xmlhttp.php")) {
		include_once(DIR_WS_LANGUAGES . $language_dir_row["directory"] . '/' . "orders_xmlhttp.php");
	}
}

echo '<response>';

if (tep_not_null($action)) {
	switch($action) {
		case "state_list":
			$zones_select_sql = "SELECT zone_id, zone_name FROM " . TABLE_ZONES . " WHERE zone_country_id = '" . $country_id . "' ORDER BY zone_name";
			$zones_result_sql = tep_db_query($zones_select_sql);
        	echo "<selection>";
        	while ($zones_row = tep_db_fetch_array($zones_result_sql)) {
        		echo "<option index='".$zones_row['zone_id']."'><![CDATA[".$zones_row['zone_name']."]]></option>";
        	}
			echo "</selection>";
			break;
		default:
			echo "<result>Unknown request!</result>";
			break;
	}
}

echo '</response>';
?>