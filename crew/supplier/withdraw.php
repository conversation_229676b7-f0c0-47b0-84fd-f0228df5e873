<?
/*
  	$Id: withdraw.php,v 1.1 2007/01/25 10:44:45 weichen Exp $
	
	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
  	
  	Copyright (c) 2002 osCommerce
	
	Released under the GNU General Public License
*/

require('includes/application_top.php');
require(DIR_WS_CLASSES . 'payment_module_info.php');

$pm_object = new payment_module_info($supplier_id, 'supplier');

$action = (isset($_REQUEST['action']) ? $_REQUEST['action'] : '');

switch($action) {
	case "submit_withdraw":
		$header_title = HEADER_FORM_WITHDRAW_SUCCESS_TITLE;
		$action_res_array = $pm_object->confirm_withdraw_form(FILENAME_WITHDRAW, $_REQUEST['cur'], $HTTP_POST_VARS, 'submit', $messageStack);
		
		if ($action_res_array['code'] == '-1') {
			tep_redirect(tep_href_link(FILENAME_WITHDRAW, tep_get_all_get_params(array('action', 'subaction'))));
		} else {
			tep_redirect(tep_href_link(FILENAME_SUPPLIER_ACCOUNT_STATEMENT, 'action=show_report&start_date='.(date('Y-m-d', mktime(0, 0, 0, date("m")  , date("d")-7, date("Y")))), 'SSL'));
		}
		
		break;
    case "confirm_withdraw":
    	$header_title = HEADER_FORM_WITHDRAW_CONFIRM_TITLE;
		$action_res_array = $pm_object->confirm_withdraw_form(FILENAME_WITHDRAW, $_REQUEST['cur'], $HTTP_POST_VARS, 'display', $messageStack);
		
		if ($action_res_array['code'] == '-1') {
			tep_redirect(tep_href_link(FILENAME_WITHDRAW, tep_get_all_get_params(array('action', 'subaction'))));
		} else {
			$form_content = $action_res_array['html'] ;
		}
		
    	break;
	default:
		$header_title = HEADER_FORM_WITHDRAW_TITLE;
		$form_content = $pm_object->show_withdraw_form(FILENAME_WITHDRAW, $_REQUEST['cur']);
		
		break;
}
?>
<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?=HTML_PARAMS?>>
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=<?php echo CHARSET; ?>">
	<title><?=TITLE?></title>
<?
if (file_exists(DIR_WS_LANGUAGES . $sup_language . '/stylesheet.css')) {
	echo '<link rel="stylesheet" type="text/css" href="'. DIR_WS_LANGUAGES . $sup_language . '/stylesheet.css">';
} else {
	echo '<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">';
}

if (file_exists(DIR_WS_INCLUDES . 'javascript/payment_xmlhttp.js.php')) { include_once (DIR_WS_INCLUDES . 'javascript/payment_xmlhttp.js.php'); }
?>
	<link rel="stylesheet" type="text/css" href="includes/javascript/spiffyCal/spiffyCal_v2_1.css">
	<script language="JavaScript" src="includes/javascript/spiffyCal/spiffyCal_v2_1.js"></script>
	<script language="javascript" src="<?=DIR_WS_INCLUDES?>general.js"></script>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">
<div id="spiffycalendar" class="text"></div>
<!-- header //-->
<? require(DIR_WS_INCLUDES . 'header.php'); ?>
<!-- header_eof //-->
	<table border="0" width="100%" height="95%" cellspacing="0" cellpadding="2">
  		<tr>
  			<td valign="top" height="30px">
  		  		<table width="100%" border="0" align="center" cellspacing="0" cellpadding="2">
           			<tr>
			    		<td width="10">&nbsp;</td>
			  		    <td class="pageHeading"><b><?=$header_title?></b></td>
			  		    <td width="10">&nbsp;</td>
			  		</tr>
			  	</table>
			</td>
  		</tr>
  		<tr>
  		  	<td valign="top"><?=$form_content?></td>
		</tr>
		<tr>
			<td valign="bottom" height="120px"><? require(DIR_WS_INCLUDES . 'footer.php'); ?></td>
		</tr>
	</table>
</body>
</html>