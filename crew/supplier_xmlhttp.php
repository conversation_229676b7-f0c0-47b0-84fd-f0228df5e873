<?php
error_reporting(null);
error_reporting(E_ERROR);
header("Cache-Control: no-cache");
header("Cache-Control: post-check=0,pre-check=0");
header("Cache-Control: max-age=0");
header("Pragma: no-cache");

header('Content-Type: text/xml');

include_once('includes/application_top.php');
require_once(DIR_WS_FUNCTIONS . 'supplier.php');
require_once(DIR_WS_FUNCTIONS . 'general.php');
include_once(DIR_WS_CLASSES . 'buyback_supplier.php');
include_once(DIR_WS_CLASSES . 'vip_order.php');
require_once(DIR_WS_CLASSES . 'log.php');
require_once(DIR_WS_CLASSES . 'order.php');
require_once(DIR_WS_CLASSES . 'edit_order.php');
require_once(DIR_WS_CLASSES . 'affiliate.php');
require_once(DIR_WS_CLASSES . 'concurrent_process.php');

define('CNBB_REMARK_MAX_QUEUE_NO', 3);
define('CNBB_LOGIN_GAME_QUEUE_REMARK_ID', '135');
define('BUYBACK_PRICE_DEFAULT_CURRENCY', $_SESSION['currency']);

$action = $_GET['action'];

$currencies->set_decimal_places(DISPLAY_PRICE_DECIMAL);

// Internal Language
$languages_id = (isset($_SESSION['languages_id'])) ? $_SESSION['languages_id'] : '';
$default_languages_id = (isset($_SESSION['default_languages_id'])) ? $_SESSION['default_languages_id'] : '';

// Webpage Language
$sup_languages_id = (isset($_SESSION['sup_languages_id'])) ? $_SESSION['sup_languages_id'] : '';
$sup_language = (isset($_SESSION['sup_language'])) ? $_SESSION['sup_language'] : '';

$country_id = (isset($_GET['country_id'])) ? (int)$_GET['country_id'] : 0;
$buyback_cat_id = isset($_GET['buyback_cat_id']) ? (int)$_GET['buyback_cat_id'] : '';
$buyback_parent_cat_id = isset($_GET['buyback_parent_cat_id']) ? (int)$_GET['buyback_parent_cat_id'] : '';
$buyback_qty = isset($_GET['buyback_qty']) ? (int)$_GET['buyback_qty'] : '';
$buyback_products_id = isset($_GET['buyback_products_id']) ? (int)$_GET['buyback_products_id'] : '';
$buyback_req_grp_id = isset($_GET['buyback_req_grp_id']) ? (int)$_GET['buyback_req_grp_id'] : '';
$payments_id = isset($_GET['payment_id']) ? (int)$_GET['payment_id'] : 0;

$order_status_id = isset($_GET['order_status_id']) ? (int)$_GET['order_status_id'] : '';
$product_type = isset($_GET['product_type']) ? $_GET['product_type'] : '';
$order_no = isset($_GET['order_no']) ? (int)$_GET['order_no'] : '';
$game_cat_id = isset($_GET['game_cat_id']) ? (int)$_GET['game_cat_id'] : '';
$start_date = isset($_GET['start_date']) ? urldecode($_GET['start_date']) : '';
$end_date = isset($_GET['end_date']) ? urldecode($_GET['end_date']) : '';

$mode = isset($_GET['mode']) ? (int)$_GET['mode'] : '0';

if (file_exists(DIR_WS_LANGUAGES . $language . '/' . "buyback.php")) {
	include_once(DIR_WS_LANGUAGES . $language . '/' . "buyback.php");
}

$current_page = basename($PHP_SELF);
if (file_exists(DIR_WS_LANGUAGES . $language . '/' . $current_page)) {
	include_once(DIR_WS_LANGUAGES . $language . '/' . $current_page);
}

echo '<response>';

if (tep_not_null($action)) {
	if ( !isset($_SERVER['HTTP_REFERER']) ||
		(strstr($_SERVER['HTTP_REFERER'], HTTP_SERVER) === FALSE && strstr($_SERVER['HTTP_REFERER'], HTTPS_SERVER) === FALSE)
		) {
		echo "You are not allowed to access from outside";
		echo '</response>';
		exit;
	}
	
	switch($action) {
		case 'verify_submit_hla_info':
			$xml_str = '';
			
			if ($buyback_req_grp_id > 0) {
				$verify_bo_select_sql = "	SELECT buyback_request_group_id 
											FROM " . TABLE_BUYBACK_REQUEST_GROUP . " 
											WHERE buyback_request_group_id = '".tep_db_input($buyback_req_grp_id)."' 
											AND buyback_status_id NOT IN (1,2)";
				$verify_bo_result_sql = tep_db_query($verify_bo_select_sql);
				if (tep_db_num_rows($verify_bo_result_sql)) {	// Only can submit info when BO is Pending or Processing
					echo "<result><![CDATA[0]]></result>";
				} else {
					echo "<result><![CDATA[1]]></result>";
				}
			}
			
			break;
		case 'get_server_list':
            $xml_str = "<servers>\n";
            if ($buyback_cat_id > 0) {
                /******************************************************************************
                	Cannot only get direct child instead look for ALL subcategories(all levels)
                	of this main category which has PRODUCTS
                ******************************************************************************/
                tep_get_subcategories($subcat_array, $buyback_cat_id);
				if ($subcat_array) {
				    $categories_select_sql = "	SELECT p2c.categories_id, p.products_id
				            					FROM " . TABLE_PRODUCTS_TO_CATEGORIES . " AS p2c
				            					INNER JOIN " . TABLE_PRODUCTS . " AS p
				            						ON (p2c.products_id = p.products_id)
				            					INNER JOIN " . TABLE_BUYBACK_PRODUCTS . " AS bp
													ON p2c.products_id = bp.products_id
				            					WHERE p2c.categories_id IN ('" . implode("', '", $subcat_array) . "')
				            						AND p2c.products_is_link = 0
				            						AND p.custom_products_type_id = 0
				            						AND p.products_bundle = ''
													AND p.products_bundle_dynamic = ''";
				    $categories_result_sql = tep_db_query($categories_select_sql);
				    while ($categories_row = tep_db_fetch_array($categories_result_sql)) {
				    	$categories_name = tep_get_categories_name($categories_row['categories_id'], $languages_id);
				    	
						$xml_str .= "<server>";
						$xml_str .= "<id><![CDATA[".$categories_row['categories_id']."]]></id>";
						$xml_str .= "<name><![CDATA[".strip_tags($categories_name)."]]></name>";
						$xml_str .= "</server>\n";
					}
				}
            }
            
            $xml_str .= "</servers>\n";
            if (tep_db_num_rows($categories_result_sql) > 0) {
                $xml_str .= "<error>0</error>";
            } else {
                $xml_str .= "<error>3</error>";
            }
            echo $xml_str;
			break;

		case 'get_backorder_buyback_server_list':
			$active_server_xml_str = $inactive_server_xml_str = '';
			
            if ($buyback_cat_id > 0) {
				$buybackSupplierObj = new buyback_supplier($buyback_cat_id);
				$currencies->set_decimal_places($buybackSupplierObj->_DECIMAL_PLACES_DISPLAY);
				$buybackSupplierObj->calculate_offer_price();
				$buyback_list_status = $buybackSupplierObj->buyback_supplier_settings['ofp_buyback_list_controller_status'];
				
				foreach ($buybackSupplierObj->products_arr as $products_id => $products_arr) {
					$product_display_name = $products_arr['categories_name'];
					
					//backorder may be flagged as server full (is_buyback == 0) if errors detected
					if ($buyback_list_status == '0' || (int)$products_arr['is_buyback']) {	// If list is closed, no point to distinguish active / inactive
						$active_server_xml_str .= "<server>";
						$active_server_xml_str .= "<id><![CDATA[".$products_id."]]></id>";
						$active_server_xml_str .= "<name><![CDATA[".$product_display_name."]]></name>";
						$active_server_xml_str .= "<unit_price><![CDATA[".$currencies->format($products_arr['avg_offer_price'], true, BUYBACK_PRICE_DEFAULT_CURRENCY, '', 'buy')."]]></unit_price>";
						$active_server_xml_str .= "<min_qty><![CDATA[".(int)$products_arr['min_qty']."]]></min_qty>";
						$active_server_xml_str .= "<max_qty><![CDATA[".(int)$products_arr['max_qty']."]]></max_qty>";
						$active_server_xml_str .= "<is_buyback><![CDATA[".($buyback_list_status == '0' || (float)$products_arr['avg_offer_price'] <= 0 ? '-1' : (int)$products_arr['is_buyback'])."]]></is_buyback>";
						$active_server_xml_str .= "</server>\n";
					} else {
						$inactive_server_xml_str .= "<server>";
						$inactive_server_xml_str .= "<id><![CDATA[".$products_id."]]></id>";
						$inactive_server_xml_str .= "<name><![CDATA[".$product_display_name."]]></name>";
						$inactive_server_xml_str .= "<unit_price><![CDATA[".$currencies->format($products_arr['avg_offer_price'], true, BUYBACK_PRICE_DEFAULT_CURRENCY, '', 'buy')."]]></unit_price>";
						$inactive_server_xml_str .= "<min_qty><![CDATA[".(int)$products_arr['min_qty']."]]></min_qty>";
						$inactive_server_xml_str .= "<max_qty><![CDATA[".(int)$products_arr['max_qty']."]]></max_qty>";
						$inactive_server_xml_str .= "<is_buyback><![CDATA[".(int)$products_arr['is_buyback']."]]></is_buyback>";
						$inactive_server_xml_str .= "</server>\n";
					}
				}
				
				if ($buyback_list_status == '0') {
					list($min, $max) = explode(',', $buybackSupplierObj->buyback_supplier_settings['ofp_buyback_list_controller']);
					
					$xml_str .= "<game><list_status_msg><![CDATA[".($buyback_list_status=='0' ? sprintf(TEXT_MESSAGE_LIST_CLOSE, ($buybackSupplierObj->pending_orders-$min)) : '')."]]></list_status_msg></game>\n";
				}
            }
            
            $xml_str .= "<servers>".$active_server_xml_str.$inactive_server_xml_str."</servers>\n";
            if (count($buybackSupplierObj->products_arr) > 0) {
                $xml_str .= "<error><![CDATA[0]]></error>";
            } else {
                $xml_str .= "<error><![CDATA[3]]></error>";
            }
            echo $xml_str;
			break;
    	case 'get_buyback_server_list':
            if (!isset($_SESSION['customer_id'])) {
                echo "You are not allowed to request";
                echo '</response>';
                exit;
            }
    		$active_server_xml_str = $inactive_server_xml_str = '';
			
            $selected_product = $buyback_products_id;
            $pre_sales_notice_count = 0; // Prevent email overload
            
            if ($buyback_cat_id > 0) {
				$buybackSupplierObj = new buyback_supplier($buyback_cat_id);
				$currencies->set_decimal_places($buybackSupplierObj->_DECIMAL_PLACES_DISPLAY);
				$buybackSupplierObj->calculate_offer_price();
				$buyback_list_status = $buybackSupplierObj->buyback_supplier_settings['ofp_buyback_list_controller_status'];
				
				$buybackSupplierObj->get_buyback_product_server_full_info();
				
				$buyback_quantity_unit = tep_not_null($buybackSupplierObj->game_buyback_unit) ? '('.$buybackSupplierObj->game_buyback_unit.')' : '';
				
				echo "<game_name><![CDATA[".tep_get_categories_name($buyback_cat_id, $languages_id).']]></game_name>';
				foreach ($buybackSupplierObj->products_arr as $products_id => $products_arr) {
					$product_display_name = $products_arr['categories_name'];
                    
                    if ($buyback_list_status == '0' || (int)$products_arr['is_buyback']) {
//                        if ((int)$selected_product <= 0) {
//                            $selected_product = $products_id;	// Get the first one as default selected product
//                        }
                        $active_server_xml_str .= "<server>";
                        $active_server_xml_str .= "<id><![CDATA[".$products_id."]]></id>";
                        $active_server_xml_str .= "<name><![CDATA[".$product_display_name."]]></name>";
                        $active_server_xml_str .= "<unit_price><![CDATA[".$currencies->format($products_arr['avg_offer_price'], true, BUYBACK_PRICE_DEFAULT_CURRENCY, '', 'buy')."]]></unit_price>";
                        $active_server_xml_str .= "<min_qty>".(int)$products_arr['min_qty']."</min_qty>";
                        $active_server_xml_str .= "<max_qty>".(int)$products_arr['max_qty']."</max_qty>";
                        $active_server_xml_str .= "<qty_unit><![CDATA[".$buyback_quantity_unit."]]></qty_unit>";
                        $active_server_xml_str .= "<is_buyback>".($buyback_list_status == '0' || (float)$products_arr['avg_offer_price'] <= 0 ? '-1' : (int)$products_arr['is_buyback'])."</is_buyback>";
                        $active_server_xml_str .= "</server>\n";
                    } else {
                        $inactive_server_xml_str .= "<server>";
                        $inactive_server_xml_str .= "<id><![CDATA[".$products_id."]]></id>";
                        $inactive_server_xml_str .= "<name><![CDATA[".$product_display_name."]]></name>";
                        $inactive_server_xml_str .= "<unit_price><![CDATA[".$currencies->format($products_arr['avg_offer_price'], true, BUYBACK_PRICE_DEFAULT_CURRENCY, '', 'buy')."]]></unit_price>";
                        $inactive_server_xml_str .= "<min_qty>".(int)$products_arr['min_qty']."</min_qty>";
                        $inactive_server_xml_str .= "<max_qty>".(int)$products_arr['max_qty']."</max_qty>";
                        $inactive_server_xml_str .= "<qty_unit><![CDATA[".$buyback_quantity_unit."]]></qty_unit>";
                        $inactive_server_xml_str .= "<is_buyback>".(int)$products_arr['is_buyback']."</is_buyback>";
                        $inactive_server_xml_str .= "</server>\n";
                    }

                    if ($buyback_list_status == '1') {	// Only send notification if game list is OPEN
                        if ((int)$products_arr['is_buyback']) {
                            if ($pre_sales_notice_count < 5) {
                                if (!isset($products_arr['is_server_full']) || $products_arr['is_server_full']) {
                                    $buybackSupplierObj->set_buyback_product_server_full_info($products_id, 0);
                                    $pre_sales_notice_count++;
                                }
                            }
                        } else {
                            if (!isset($products_arr['is_server_full']) || !$products_arr['is_server_full']) {
                                $buybackSupplierObj->set_buyback_product_server_full_info($products_id, 1);
                            }
                        }
                    }
				}
				
				if ($buyback_list_status == '0') {
					list($min, $max) = explode(',', $buybackSupplierObj->buyback_supplier_settings['ofp_buyback_list_controller']);
					
					$xml_str .= "<game><list_status_msg><![CDATA[".($buyback_list_status=='0' ? sprintf(TEXT_MESSAGE_LIST_CLOSE, ($buybackSupplierObj->pending_orders-$min)) : '')."]]></list_status_msg></game>\n";
				}
            }
			
            $xml_str .= "<servers>".$active_server_xml_str.$inactive_server_xml_str;
            unset($active_server_xml_str, $inactive_server_xml_str);
            
            if (count($buybackSupplierObj->products_arr) > 0) {
                $xml_str .= "<error>0</error>";
            } else {
                $xml_str .= "<error>3</error>";
            }
            $xml_str .=	"</servers>\n";
            echo $xml_str;
            
    	    break;
    	    
    	case 'get_buyback_product_info':
    		tep_xmlhttp_product_buyback_info($buyback_parent_cat_id, $buyback_products_id);
    		
    	    break;
			
    	case 'show_order_report':
    	    $xml_str = '';
    	    
			if ($buyback_req_grp_id) {
				$order_report_select_sql = "SELECT brg.buyback_request_group_id, brg.buyback_request_group_date, brg.buyback_request_group_expiry_date,
												brg.buyback_request_group_comment, brg.buyback_request_contact_name, brg.buyback_request_contact_telephone,
												brg.currency, brg.currency_value, brg.buyback_status_id, 
												br.buyback_request_id, br.products_id, br.buyback_amount, br.buyback_request_quantity, br.buyback_sender_character,
												br.restock_character as saved_restock_character,
												pc.categories_id, pc.products_id 
											FROM ". TABLE_BUYBACK_REQUEST_GROUP . " AS brg
											LEFT JOIN " . TABLE_BUYBACK_REQUEST . " AS br 
												ON (brg.buyback_request_group_id = br.buyback_request_group_id)
											INNER JOIN " . TABLE_PRODUCTS_TO_CATEGORIES . " AS pc 
												ON (br.products_id = pc.products_id) 
											WHERE brg.customers_id = '".tep_db_input($_SESSION['customer_id'])."' 
												AND brg.buyback_request_group_id = '" . tep_db_prepare_input($buyback_req_grp_id) . "'";
				$order_report_result_sql = tep_db_query($order_report_select_sql);
				if ($order_report_row = tep_db_fetch_array($order_report_result_sql)) {
					
					$buyback_main_cat_name = tep_get_buyback_main_cat_info($order_report_row['categories_id']);
					$game_name = $buyback_main_cat_name['text'];
					$server_name = tep_display_category_path(tep_get_product_path_name($order_report_row['products_id']), $order_report_row['categories_id'], 'catalog', false);
					
					$req_qty = $order_report_row['buyback_request_quantity'];
					$product_name = tep_get_products_name($order_report_row['products_id'], $languages_id);
					$total_price = $currencies->format($order_report_row['buyback_amount'], true, $order_report_row['currency'], $order_report_row['currency_value']); //sum up the amounts if each buyback order has more more than one product in the future.
					$sender_character = $order_report_row['buyback_sender_character'];
					$restk_character = ($order_report_row['buyback_status_id'] == 1 ? $order_report_row['saved_restock_character'] : '');
					$delivery_time = $order_report_row['buyback_request_group_expiry_date'];
					$contact_name = $order_report_row['buyback_request_contact_name'];
					$contact_no = $order_report_row['buyback_request_contact_telephone'];
					$show_comments = $order_report_row['buyback_request_group_comment'];
					$order_reference = $buyback_req_grp_id;
					
				}
				
				$cat_cfg_array = tep_get_cfg_setting($buyback_products_id, 'product', 'BUYBACK_QUANTITY_UNIT', 'configuration_key' );				
				
	            //Get Unit of measurement (ie. Million)
				$buyback_qty_unit = $cat_cfg_array['BUYBACK_QUANTITY_UNIT'];
				
        	    $xml_str .= "\n<cid><![CDATA[".trim($_SESSION['customer_id'])."]]></cid>";
        	    $xml_str .= "\n<game_name><![CDATA[".trim($game_name)."]]></game_name>";
        	    $xml_str .= "\n<server_name><![CDATA[".trim($server_name)."]]></server_name>";
        	    $xml_str .= "\n<req_qty><![CDATA[".$req_qty."]]></req_qty>";
        	    $xml_str .= "\n<uom><![CDATA[".$buyback_qty_unit."]]></uom>";
        	    $xml_str .= "\n<product_name><![CDATA[".$product_name."]]></product_name>";
        	    $xml_str .= "\n<total_price><![CDATA[".$total_price."]]></total_price>";
        	    $xml_str .= "\n<sender_character><![CDATA[".$sender_character."]]></sender_character>";
        	    $xml_str .= "\n<restk_character><![CDATA[".$restk_character."]]></restk_character>";
        	    $xml_str .= "\n<delivery_time><![CDATA[".$delivery_time."]]></delivery_time>";
        	    $xml_str .= "\n<contact_name><![CDATA[".$contact_name."]]></contact_name>";
        	    $xml_str .= "\n<contact_no><![CDATA[".$contact_no."]]></contact_no>";
        	    $xml_str .= "\n<show_comments><![CDATA[".$show_comments."]]></show_comments>";
        	    $xml_str .= "\n<order_reference><![CDATA[".$order_reference."]]></order_reference>";
			}
            echo $xml_str;
    		break;
			
    	case 'search_order_history':
    		if (file_exists(DIR_WS_LANGUAGES . $language . '/' . "my_order_history.php")) {
				include_once(DIR_WS_LANGUAGES . $language . '/' . "my_order_history.php");
			}
			
    	    $xml_str = '';
    	    $allow_search_num_days_ago = 60;
			
			$start_date_str = $end_date_str = ' 1 ';
			
			if (tep_not_null($start_date)) {
				if (strpos($start_date, ':') !== false) {
					$start_date_str = " brg.buyback_request_group_date >= '" . $start_date . "'";
				} else {
					$start_date_str = " brg.buyback_request_group_date >= '" . $start_date . " 00:00:00'";
				}
			} else {
//				$sformat = 'Y-m-d H:i:s';
//				$start_date = date($sformat);
//				$start_date_str = " brg.buyback_request_group_date >= '" . $start_date . "'";
			}
			
			if (tep_not_null($end_date)) {
				if (strpos($end_date, ':') !== false) {
					$end_date_str = " brg.buyback_request_group_date <= '" . $end_date . "'";
				} else {
					$end_date_str = " brg.buyback_request_group_date <= '" . $end_date . " 23:59:59'";
				}
			} else {
//				$eformat = 'Y-m-d H:i:s';
//				$end_date = date($eformat);
//				$end_date_str = " brg.buyback_request_group_date <= '" . $end_date . "'";
			}
			
			switch ($product_type) {
				case 'game_currency':
					$custom_product_type_id = '0';
				break;
				case 'hla_product':
					$custom_product_type_id = '4';
				break;
				default:
					$custom_product_type_id = '0';
				break;
			}
			$buyback_request_select_sql = "	SELECT brg.buyback_request_group_id, brg.buyback_request_group_date, brg.buyback_request_group_expiry_date, brg.currency, brg.currency_value, brg.show_restock, brg.buyback_status_id,
												br.orders_products_id, br.buyback_request_id, br.products_id, br.buyback_amount, br.buyback_request_quantity, bs.buyback_status_name, br.buyback_quantity_confirmed,  br.restock_character AS saved_restock_character,
												br.buyback_dealing_type, p.products_cat_id_path, brg.buyback_request_group_served, br.buyback_request_screenshot_before_name, br.buyback_request_screenshot_after_name 
											FROM ". TABLE_BUYBACK_REQUEST_GROUP . " AS brg
											LEFT JOIN " . TABLE_BUYBACK_REQUEST . " AS br 
												ON brg.buyback_request_group_id=br.buyback_request_group_id
											INNER JOIN " . TABLE_PRODUCTS . " AS p 
												ON (br.products_id = p.products_id)
											INNER JOIN " . TABLE_BUYBACK_STATUS . " AS bs 
												ON ( brg.buyback_status_id = bs.buyback_status_id AND bs.language_id = '" . tep_db_input($languages_id) . "')
											WHERE brg.customers_id = '" . tep_db_input($_SESSION['customer_id']) . "' 
												AND brg.buyback_request_order_type = '0' 
												AND p.custom_products_type_id = '".tep_db_input($custom_product_type_id)."'
												AND " . $start_date_str . "
												AND " . $end_date_str;
			if ($order_status_id != 0) {
			    $buyback_request_select_sql .= " AND brg.buyback_status_id = '" . (int)$order_status_id . "' ";
			}
			if ($order_no) {
				$buyback_request_select_sql .= "AND brg.buyback_request_group_id = '" . (int)$order_no . "'";
			}
			
			$cat_parent_path = tep_get_categories_parent_path($game_cat_id);
			if (tep_not_null($cat_parent_path)) {
				$cat_parent_array = explode("_",$cat_parent_path);
				$top_parent_id = $cat_parent_array[1];
				
				$cat_parent_path .= $game_cat_id."_";
			} else {
				$cat_parent_path = "_".$game_cat_id."_";
				$top_parent_id = (int)$game_cat_id;
			}
			
			if ((int)$game_cat_id>0) {
				$cat_parent_path = str_replace('_', '\_', $cat_parent_path);
				$buyback_request_select_sql .= " AND p.products_cat_id_path LIKE '".$cat_parent_path."%'";
			}
			
			$buyback_request_select_sql .= "\n         GROUP BY brg.buyback_request_group_id
											           ORDER BY brg.buyback_status_id asc, brg.buyback_request_group_date desc ";

			$buyback_request_result_sql = tep_db_query($buyback_request_select_sql);
			$buyback_request_num_rows = tep_db_num_rows($buyback_request_result_sql);
			
			$xml_str .= "\n<results>";
			while ($buyback_request_row = tep_db_fetch_array($buyback_request_result_sql)) {
                # remove new order notification
                tep_db_query("DELETE FROM " . TABLE_ORDERS_NOTIFICATION . " WHERE orders_id = '" . (int) $buyback_request_row['buyback_request_group_id'] . "' AND orders_type = 'BO' AND site_id IN (" . SITE_ID . ")");
                
				$product_cat_path_array = explode("_",$buyback_request_row['products_cat_id_path']);
				$buyback_request_row['categories_id'] = $product_cat_path_array[count($product_cat_path_array)-2];	// Last element is '_'
				
				$order_latest_remark = '';
				$allow_to_upload = 'no';
				
				$currencies->set_decimal_places($currencies->currencies[$buyback_request_row['currency']]['decimal_places']);
                
				//counting queuing number
				$total_queue = 0;
    			if ((int)$buyback_request_row['buyback_status_id'] == 1 && $buyback_request_row['buyback_request_group_served'] == '0' && ($buyback_request_row['buyback_dealing_type'] == 'ofp_deal_on_game' || $buyback_request_row['buyback_dealing_type'] == 'ofp_deal_on_open_store')) {
    			    $count_q_select_sql = "SELECT count(*) AS q_no
    		                               FROM ". TABLE_BUYBACK_REQUEST_GROUP ." AS brg
    		                               INNER JOIN ". TABLE_BUYBACK_REQUEST ." AS br
    		                                   ON (brg.buyback_request_group_id=br.buyback_request_group_id)
    		                               WHERE brg.buyback_request_group_id < '". (int) $buyback_request_row['buyback_request_group_id'] ."'
    		                                   AND brg.buyback_status_id = '1'
    		                                   AND brg.buyback_request_group_served = '0'
    		                                   AND (br.buyback_dealing_type = 'ofp_deal_on_game'
    		                                   		OR br.buyback_dealing_type = 'ofp_deal_on_open_store'
    		                                   		OR br.buyback_dealing_type = 'vip_deal_on_game')";
        			$count_q_result_sql = tep_db_query($count_q_select_sql);
        			$count_q_row = tep_db_fetch_array($count_q_result_sql);
        			$total_queue = $count_q_row['q_no'];
    			}
				
				// $allow_to_upload: for the user who havent upload the ss yet.
    			if (!tep_not_null($buyback_request_row['buyback_request_screenshot_after_name']) && !tep_not_null($buyback_request_row['buyback_request_screenshot_before_name']) && BUYBACK_ENABLE_UPLOAD_SCREENSHOT == 'true') {
					$allow_to_upload = 'yes';
				}
				
				//Get restock character.
				$show_restock = 0;
				
				if ($buyback_request_row['buyback_status_id'] != 1) {
			        $receiver_char_name_message = '';
			  	} else {
			  		$show_restock = (int)$buyback_request_row['show_restock'];
			  		
			  		if ($show_restock) {
			  			$receiver_char_name_message = vip_order::tep_get_customer_trading_char($buyback_request_row['orders_products_id']);
			  			if (!tep_not_null($receiver_char_name_message)) {
			  				if (tep_not_null($buyback_request_row['saved_restock_character'])) {
			  					$receiver_char_name_message = $buyback_request_row['saved_restock_character'];
			  				} else {
			  					$receiver_char_name_message = tep_get_buyback_restock_char($buyback_request_row['products_id']);
			  				}
			  			}
			  		} else {
			  			$receiver_char_name_message = TEXT_CHARACTER_NAME_IN_PREPARATION;
			  		}
				}
				
				if ($custom_product_type_id == 0) {
					$buyback_main_cat_name = tep_get_buyback_main_cat_info($buyback_request_row['categories_id']);
					$product_display_name = tep_display_category_path(tep_get_product_path_name($buyback_request_row['products_id']), $buyback_request_row['categories_id'], 'catalog', false);
					// Currency Product
					$xml_str .= "\n<row>";
					$xml_str .= "\n<req_grp_id><![CDATA[".$buyback_request_row['buyback_request_group_id']."]]></req_grp_id>";
					$xml_str .= "\n<req_id><![CDATA[".$buyback_request_row['buyback_request_id']."]]></req_id>";
					$xml_str .= "\n<game_name><![CDATA[".$buyback_main_cat_name['text']."]]></game_name>";
					$xml_str .= "\n<delivery_method><![CDATA[".tep_display_supplier_trade_mode($buyback_request_row['buyback_dealing_type'])."]]></delivery_method>";
					$xml_str .= "\n<server_name><![CDATA[".$product_display_name."]]></server_name>";
					$xml_str .= "\n<req_qty><![CDATA[".$buyback_request_row['buyback_request_quantity']."]]></req_qty>";
					$xml_str .= "\n<confirmed_qty><![CDATA[".$buyback_request_row['buyback_quantity_confirmed']."]]></confirmed_qty>";
					$xml_str .= "\n<amount><![CDATA[".$currencies->format($buyback_request_row['buyback_amount'], true, $buyback_request_row['currency'], $buyback_request_row['currency_value'])."]]></amount>";
					$xml_str .= "\n<show_restock><![CDATA[".$show_restock."]]></show_restock>";
					$xml_str .= "\n<restock_character><![CDATA[".$receiver_char_name_message."]]></restock_character>";
					$xml_str .= "\n<status_name><![CDATA[".$buyback_request_row['buyback_status_name']."]]></status_name>";
					$xml_str .= "\n<current_status_id><![CDATA[".$buyback_request_row['buyback_status_id']."]]></current_status_id>";
					$xml_str .= "\n<total_queue><![CDATA[".$total_queue."]]></total_queue>";
					// allow_to_upload: for the user who havent upload the ss yet.
					$xml_str .= "\n<allow_to_upload><![CDATA[".$allow_to_upload."]]></allow_to_upload>";
					$xml_str .= "\n<custom_product_type_id><![CDATA[".$custom_product_type_id."]]></custom_product_type_id>";
				} else {
					// HLA Product
					$products_cat_id_path = explode('_', $buyback_request_row['products_cat_id_path']);
					$product_game_id = 0;
					
					if (is_array($zone_info_array[1]->zone_categories_id)) {
						$product_game_array = array_intersect($zone_info_array[1]->zone_categories_id, $products_cat_id_path);
						
						if (sizeof($product_game_array) > 0) {
							array_multisort($product_game_array);
							
							$product_game_id = $product_game_array[0];
						}
					}
					$cat_name = tep_get_categories_name($product_game_id, $languages_id);
					
					$ref_no = $lvl = $race_n_class = '';
					$received = 0;
					
					$get_hla_info_sql = "	SELECT orders_products_item_info 
											FROM " . TABLE_ORDERS_PRODUCTS_ITEM . " 
											WHERE orders_products_id = '".tep_db_input($buyback_request_row['orders_products_id'])."'";
					$get_hla_info_result = tep_db_query($get_hla_info_sql);
					while ($get_hla_info_row = tep_db_fetch_array($get_hla_info_result)) {
						$get_hla_info_row = tep_array_unserialize($get_hla_info_row['orders_products_item_info']);
						
						$ref_no .= $get_hla_info_row['products_ref_id'].'<br />';
						$lvl .= $get_hla_info_row['language_id'][1]['level'].'<br />';
						$race_n_class .= $get_hla_info_row['language_id'][1]['race'].' / '.$get_hla_info_row['language_id'][1]['class'].'<br />';;
					}
					
					$check_receive_sql = "	SELECT received 
											FROM " . TABLE_ORDERS_PRODUCTS_HISTORY . " 
											WHERE buyback_request_group_id = '".tep_db_input($buyback_request_row['buyback_request_group_id'])."' 
												AND received = '1'";
					$check_receive_result = tep_db_query($check_receive_sql);
					if (tep_db_num_rows($check_receive_result) > 0) {
						$received = 1;	// Seller can's submit info anymore
					}
					
					$hla_info_stage_1 = tep_draw_products_extra_info($buyback_request_row['orders_products_id'], 'hla_info_stage_1');
					if (count($hla_info_stage_1) > 0) {
						$info_submitted = '1';
					} else {
						$info_submitted = '0';
					}
					
					$xml_str .= "\n<row>";
					$xml_str .= "\n<req_grp_id><![CDATA[".$buyback_request_row['buyback_request_group_id']."]]></req_grp_id>";
					$xml_str .= "\n<req_id><![CDATA[".$buyback_request_row['buyback_request_id']."]]></req_id>";
					$xml_str .= "\n<game_name><![CDATA[".$cat_name."]]></game_name>";
					$xml_str .= "\n<ref_no><![CDATA[".$ref_no."]]></ref_no>";
					$xml_str .= "\n<lvl><![CDATA[".$lvl."]]></lvl>";
					$xml_str .= "\n<race_n_class><![CDATA[".$race_n_class."]]></race_n_class>";
					$xml_str .= "\n<amount><![CDATA[".$currencies->format($buyback_request_row['buyback_amount'], true, $buyback_request_row['currency'], $buyback_request_row['currency_value'])."]]></amount>";
					$xml_str .= "\n<status_name><![CDATA[".$buyback_request_row['buyback_status_name']."]]></status_name>";
					$xml_str .= "\n<current_status_id><![CDATA[".$buyback_request_row['buyback_status_id']."]]></current_status_id>";
					$xml_str .= "\n<custom_product_type_id><![CDATA[".$custom_product_type_id."]]></custom_product_type_id>";
					$xml_str .= "\n<received><![CDATA[".$received."]]></received>";
					$xml_str .= "\n<info_submitted><![CDATA[".$info_submitted."]]></info_submitted>";
					
				}
				
				//Get expirable	
				$show_expiry = 0;
				$expiry_time = '00:00:00';
				if ($custom_product_type_id==0 && in_array((int)$buyback_request_row['buyback_status_id'], array(1,2))) {
					if ($buyback_request_row['buyback_status_id'] == 2) {
						$total_received_select_sql = "SELECT sum(br.buyback_quantity_received) as total_received 
														FROM ".TABLE_BUYBACK_REQUEST." AS br
														WHERE br.buyback_request_group_id = '{$buyback_request_row['buyback_request_group_id']}' 
														GROUP BY buyback_request_group_id";
						$total_received_result_sql = tep_db_query($total_received_select_sql);
						if ($total_received_row = tep_db_fetch_array($total_received_result_sql)) {
							$total_received = (int)$total_received_row['total_received'];
						}
						if ($total_received == 0) {
							$show_expiry = 1;
						}
					} else {
						$show_expiry = 1;
					}
					
					if ($show_expiry) {
						if ($buyback_request_row['buyback_request_group_expiry_date'] != '0000-00-00 00:00:00') {
							$get_time_sec = tep_day_diff(date("Y-m-d H:i:s"), $buyback_request_row['buyback_request_group_expiry_date'], 'sec');
							$expiry_mins = floor($get_time_sec/60);

							$expiry_time = $expiry_mins;
						} else {
							$show_expiry = 0;
						}
					}
					
					if ((int)$buyback_request_row['buyback_status_id'] == 1) {
						if ($total_queue > 0) {
							if ($total_queue < (int)CNBB_REMARK_MAX_QUEUE_NO) {
								$order_latest_remark = TEXT_PROCEED;
							}
						} else if ($show_restock) {
							$buyback_remark_select_sql = "	SELECT comments 
															FROM " . TABLE_BUYBACK_STATUS_HISTORY . "
															WHERE buyback_request_group_id = '".tep_db_input($buyback_request_row['buyback_request_group_id'])."' 
																AND set_as_buyback_remarks = 1 
																AND customer_notified = 1 
																AND (comments IS NOT NULL AND comments <> '')";
							$buyback_remark_result_sql = tep_db_query($buyback_remark_select_sql);
							$buyback_remark_row = tep_db_fetch_array($buyback_remark_result_sql);
							$order_latest_remark = $buyback_remark_row['comments'];
						}
					} else {
						$buyback_remark_select_sql = "	SELECT comments 
														FROM " . TABLE_BUYBACK_STATUS_HISTORY . "
														WHERE buyback_request_group_id = '".tep_db_input($buyback_request_row['buyback_request_group_id'])."' 
															AND set_as_buyback_remarks = 1 
															AND customer_notified = 1 
															AND (comments IS NOT NULL AND comments <> '')";
						$buyback_remark_result_sql = tep_db_query($buyback_remark_select_sql);
						$buyback_remark_row = tep_db_fetch_array($buyback_remark_result_sql);
						$order_latest_remark = $buyback_remark_row['comments'];
					}
				} else {
					$buyback_remark_select_sql = "	SELECT comments 
													FROM " . TABLE_BUYBACK_STATUS_HISTORY . "
													WHERE buyback_request_group_id = '".tep_db_input($buyback_request_row['buyback_request_group_id'])."' 
														AND set_as_buyback_remarks = 1 
														AND customer_notified = 1 
														AND (comments IS NOT NULL AND comments <> '')";
					$buyback_remark_result_sql = tep_db_query($buyback_remark_select_sql);
					$buyback_remark_row = tep_db_fetch_array($buyback_remark_result_sql);
					$order_latest_remark = $buyback_remark_row['comments'];
				}
				
				$xml_str .= "\n<show_expiry><![CDATA[".$show_expiry."]]></show_expiry>";
				$xml_str .= "\n<expiry_time><![CDATA[".$expiry_time."]]></expiry_time>";
				$xml_str .= "\n<buyback_remarks><![CDATA[".$order_latest_remark."]]></buyback_remarks>";
				$xml_str .= "\n</row>";
			}
			$xml_str .= "\n</results>";
			$xml_str .= "\n<num_results><![CDATA[".$buyback_request_num_rows."]]></num_results>";
            echo $xml_str;
    		break;
    	case 'show_payment_report':
    		$result_html = '';
    		
	    	$payment_checking_select_sql = "SELECT store_payments_id 
	    									FROM " . TABLE_STORE_PAYMENTS . " 
			                            	WHERE store_payments_id = '" . $payments_id . "'
			                            		AND user_id = '".(int)$_SESSION['customer_id']."'
			                            		AND user_role = 'customers'";
			$payment_checking_result_sql = tep_db_query($payment_checking_select_sql);
			
			if (tep_db_num_rows($payment_checking_result_sql)) {
				// update report read
	        	$update_payment_read_sql = "UPDATE " . TABLE_STORE_PAYMENTS . " 
				                            SET store_payments_read_mode = 1 
				                            WHERE store_payments_id = '" . $payments_id . "'";
				tep_db_query($update_payment_read_sql);
				
	            $result_html = '<table border="0" width="100%" cellspacing="0" cellpadding="3">
	                                <tr>
	                                    <td align="center">
	                                    	<div style="overflow:auto;height:400px;">
	                                        <table border="1" width="99%" height="300" cellspacing="0" cellpadding="2">
	                                            <tr>
	                                                <td class="main"><b>'. POP_PAYMENT_REPORT_DATETIME .'</b></td>
	                                                <td class="main"><b>'. POP_PAYMENT_REPORT_PAYMENT_MESSAGE .'</b></td>
	                                            </tr>';
				$payment_message_select_sql = " SELECT comments, date_added 
				                                FROM ". TABLE_STORE_PAYMENTS_HISTORY ."
				                                WHERE store_payments_id = '". $payments_id ."'
				                                	AND payee_notified = '1' 
				                                	AND comments <> '' 
				                                ORDER BY store_payments_history_id, date_added";
	            $payment_message_result_sql = tep_db_query($payment_message_select_sql);
	            while ($payment_message_row = tep_db_fetch_array($payment_message_result_sql)) {
	                 $result_html .= '          <tr>
	                                                <td class="main" width="50%" valign="top">'. $payment_message_row['date_added'] .'</td>
									                <td class="main" width="50%" valign="top">'. ($payment_message_row['comments'] ? nl2br($payment_message_row['comments']) : '&nbsp;') .'</td>
	                                            </tr>';
	            }
	            
	            $result_html .= '           </table>
	            							</div>
	                                    </td>
	                                </tr>
	                            </table>';
			}
			
			echo $result_html;
			
    		break;
    	case 'update_buyback_request_group':
    		//valid ids ?
    		$buyback_request_group_id = isset($_POST['req_grp_id']) ? (int)$_POST['req_grp_id'] : 0;
			$buyback_quantity_confirmed = isset($_POST['sent_qty']) ? (int)$_POST['sent_qty'] : 0;
			//$buyback_delivered_id = isset($_POST['delivered_id']) ? $_POST['delivered_id'] : '';
			$buyback_request_id = isset($_POST['req_id']) ? (int)$_POST['req_id'] : 0;
			
			// Temporary Processing to prevent insert unwanted record;
			$matchcase = md5($buyback_request_group_id.':~:'.$buyback_request_id.':~:ConfirmBuybackOrder');
			$concurrentProcessObj = new concurrent_process(FILENAME_BUYBACK, $matchcase, (int)$buyback_quantity_confirmed);
			$duplicate_order = false;
			
			usleep(1100000);
			if($concurrentProcessObj->concurrent_process_matching()) {	// If there is > 1 orders matched
				if (!$concurrentProcessObj->concurrent_process_match_insert_id()) {
					$duplicate_order = true;
					echo '<error_msg>'.ERROR_SUBMITTED_BY_OTHER_USER.'</error_msg>';
				}
			}
			
			if ($duplicate_order == false) {
				$customer_email =  tep_get_customers_email($_SESSION['customer_id']);
				$log_object = new log_files($customer_email); //keep supplier email address
				
				$restock_character = '';
				$buyback_request_select_sql = "	SELECT br.orders_id, br.orders_products_id, br.buyback_dealing_type, br.products_id, br.buyback_unit_price, br.buyback_request_quantity, br.restock_character, l.locking_by 
												FROM " . TABLE_BUYBACK_REQUEST_GROUP . " AS brg 
												INNER JOIN " . TABLE_BUYBACK_REQUEST . " AS br 
													ON (brg.buyback_request_group_id = br.buyback_request_group_id) 
												LEFT JOIN " . TABLE_LOCKING . " AS l 
													ON (l.locking_trans_id = brg.buyback_request_group_id AND l.locking_table_name = '" . tep_db_input(TABLE_BUYBACK_REQUEST_GROUP) . "') 
												WHERE brg.buyback_request_group_id = '" . tep_db_input($buyback_request_group_id) . "' 
													AND brg.buyback_status_id = 1 
													AND brg.customers_id = '".(int)$_SESSION['customer_id']."' 
													AND br.buyback_request_id = '" . tep_db_input($buyback_request_id) . "'";
				$buyback_request_result_sql = tep_db_query($buyback_request_select_sql);
				
				if ($buyback_request_row = tep_db_fetch_array($buyback_request_result_sql)) {
					$buyback_request_quantity = $buyback_request_row['buyback_request_quantity'];
					$vipOrderObj = new vip_order($buyback_request_row['orders_products_id']);
					$vipOrderObj->get_orders_details();
					
					if (tep_not_null($buyback_quantity_confirmed) && ($buyback_quantity_confirmed == $buyback_request_quantity)) {
					//if ($buyback_quantity_confirmed > 0 && ($buyback_quantity_confirmed <= $buyback_request_quantity)) {
						$login_id = '';
						$game_name = '';
						
						$update_confirmed_qty_array = array('buyback_quantity_confirmed' => $buyback_quantity_confirmed,
															'buyback_quantity_received' => $buyback_quantity_confirmed);
						
						if (!tep_not_null($buyback_request_row['restock_character'])) {
							$update_confirmed_qty_array['restock_character'] = $vipOrderObj->tep_get_customer_trading_char($orders_products_id);
							//$update_confirmed_qty_array['restock_character'] = tep_get_buyback_restock_char($buyback_request_row['products_id']);
						}
						tep_db_perform(TABLE_BUYBACK_REQUEST, $update_confirmed_qty_array, 'update', "buyback_request_group_id='$buyback_request_group_id' AND buyback_request_id='$buyback_request_id'");
						
						//Automatically set status to processing & Hide character status
						$update_request_group_status_array = array('buyback_status_id' => '2', 'show_restock' => '0', 'buyback_request_group_last_modified' => 'now()');
						tep_db_perform(TABLE_BUYBACK_REQUEST_GROUP, $update_request_group_status_array, 'update', "buyback_request_group_id='$buyback_request_group_id'");
						
						$buyback_history_data_array = array('buyback_request_group_id' => $buyback_request_group_id,
									                        'buyback_status_id' => '2',
															'date_added' => 'now()',
															'customer_notified' => '0',
															'comments' => ''
															);
						tep_db_perform(TABLE_BUYBACK_STATUS_HISTORY, $buyback_history_data_array);

						//update product qty
						$product_info_select_sql = "SELECT products_skip_inventory, products_main_cat_id 
													FROM " . TABLE_PRODUCTS . " 
													WHERE products_id = '" . (int)$buyback_request_row['products_id'] . "'";
						$product_info_result_sql = tep_db_query($product_info_select_sql);
						
						if($product_info_row = tep_db_fetch_array($product_info_result_sql)) {
							$game_name = tep_get_categories_name($product_info_row['products_main_cat_id']);
							if (!$product_info_row["products_skip_inventory"]) {
								$update_qty_array = array(	array(	'field_name'=> 	'products_quantity',
																					'operator'=> '+', 
																					'value'=> $buyback_quantity_confirmed),
															array(	'field_name'=> 	'products_actual_quantity',
																					'operator'=> '+', 
																					'value'=> $buyback_quantity_confirmed)
														);
							}
							tep_set_product_qty($buyback_request_row['products_id'], $update_qty_array, true, sprintf(LOG_BUYBACK_ORDER, $buyback_request_group_id), '');
						}
						
						if ($buyback_request_row['buyback_dealing_type'] != 'extra_inventory') {
							$char_name = $received = '';
							$confirmation_duration = 0;
							$insert_orders_products_history_1 = $insert_orders_products_history_2 = array();
							
							//Update to TABLE_ORDERS_PRODUCTS_HISTORY
							$get_parent_id_select_sql = "	SELECT parent_orders_products_id 
															FROM " . TABLE_ORDERS_PRODUCTS . " 
															WHERE orders_products_id = '".(int)$buyback_request_row['orders_products_id']."'";
							$get_parent_id_result_sql = tep_db_query($get_parent_id_select_sql);
							if($get_parent_id_row = tep_db_fetch_array($get_parent_id_result_sql)){
								$extra_info_array = tep_draw_products_extra_info($get_parent_id_row['parent_orders_products_id']);
								$char_name = $extra_info_array['char_name'];
								if (!tep_customer_delivery_confirmation($extra_info_array['delivery_mode'])) {
									$insert_orders_products_history_1 = array('received' => '1');
								}
								$buyback_main_cat = tep_get_buyback_main_cat_info($buyback_request_row['products_id'], 'product');
								$confirmation_duration_select_sql = "	SELECT buyback_setting_value 
																		FROM " . TABLE_BUYBACK_SETTING . " 
																		WHERE buyback_setting_table_name = 'buyback_categories' 
																		AND buyback_setting_reference_id = '".(int)$buyback_main_cat['id']."' 
																		AND buyback_setting_key = 'customer_confirmation_duration' LIMIT 1";
								$confirmation_duration_result_sql =	tep_db_query($confirmation_duration_select_sql);
								if ($confirmation_duration_row = tep_db_fetch_array($confirmation_duration_result_sql)) {
									$confirmation_duration = $confirmation_duration_row['buyback_setting_value'];
								}
							}
							
							$insert_orders_products_history_2 = array(	'buyback_request_group_id' => $buyback_request_group_id,
												                        'orders_id' => $buyback_request_row['orders_id'],
																		'orders_products_id' => $buyback_request_row['orders_products_id'],
																		'date_added' => 'now()',
																		'last_updated' => 'now()',
																		'date_confirm_delivered' => date("Y-m-d H:i:s", mktime(date("H"), date("i")+(int)$confirmation_duration, date("s"), date("m"), date("d"), date("Y"))),
																		'delivered_amount' => $buyback_quantity_confirmed,
																		'delivered_character' => $char_name,
																		'changed_by' => $customer_email
																		);
																	
							$insert_orders_products_history = array_merge($insert_orders_products_history_1, $insert_orders_products_history_2);
							tep_db_perform(TABLE_ORDERS_PRODUCTS_HISTORY, $insert_orders_products_history);
							
							if (tep_not_null($buyback_request_row['locking_by'])) {
								tep_order_lock($buyback_request_group_id, ORDERS_LOG_UNLOCK_OWN_ORDER, $login_id);
							}
							
							//update the quantity to order product.
							$edit_order_obj = new edit_order($buyback_request_row['orders_id']);
							$edit_order_obj->deliver_order($buyback_quantity_confirmed, $buyback_request_row['orders_products_id'], $customer_email, true, $messageStack);
							$edit_order_obj->set_customers_comment($buyback_request_row['orders_id'], '0', sprintf(TEXT_BUYBACK_ORDER_REFERENCE_REMARK, $buyback_request_group_id), '0');
						}
						/*
						$rstk_char_date_select_sql = "	SELECT date_added, NOW() as today_date
														FROM " . TABLE_BUYBACK_STATUS_HISTORY . " 
														WHERE buyback_request_group_id = '".(int)$buyback_request_group_id."' 
															AND comments = 'Show Restock Character'";
						$rstk_char_date_result_sql = tep_db_query($rstk_char_date_select_sql);
						
						if ($rstk_char_date_row = tep_db_fetch_array($rstk_char_date_result_sql)) {
							$mins = tep_day_diff($rstk_char_date_row['date_added'], $rstk_char_date_row['today_date'], 'sec') / 60;
							if ($mins < 2) {
								$admin_email = "Vince <<EMAIL>>, KenLee <<EMAIL>>";
								$admin_email_to_array = tep_parse_email_string($admin_email);
								$email_content = 'Buyback Order: '.$buyback_request_group_id."\n";
								$email_content .= 'Open RSTK Time: '.$rstk_char_date_row['date_added']."\n";
								$email_content .= 'Confirm Buyback Order Time: '.$rstk_char_date_row['today_date']."\n\n";
								
								$email_subject = '[OffGamers] Alert - Buyback #'.$buyback_request_group_id.' Completed in short period ('.$game_name.')';
					        	for ($email_cnt=0; $email_cnt < count($admin_email_to_array); $email_cnt++) {
					        		@tep_mail($admin_email_to_array[$email_cnt]['name'], $admin_email_to_array[$email_cnt]['email'], $email_subject, $email_content, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
					        	}
							}
						}
						*/
						
						//Update to db
						echo '<result>updated</result>';
					} else {
						echo '<error_msg>'.TEXT_ERROR_INVALID_QTY.'</error_msg>';
					}
				} else {
					echo '<error_msg>'.TEXT_ERROR_GET_DATA_FAILED.'</error_msg>';
				}
			}
			
			usleep(1100000);
			$concurrentProcessObj->concurrent_process_cleanup();
			
    		break;
    	case 'redirect_upload_to_curl':
    			$customer_id = $_SESSION['customer_id'];
    			$req_grp_id = $_GET['req_grp_id'];
    			$file_name_1 = $_GET['file_name_1'];
    			$file_name_2 = $_GET['file_name_2'];
    			$sup_language = $_GET['sup_language'];
    			
    			$max_file_size = '800'; // KB
				$allow_file_type = array('jpg');
				$result = '';
				$result2 = '';
				$error = "";
				
				if (tep_not_null($file_name_1)) {
					$result = check_uploading_file ($file_name_1, $allow_file_type, $max_file_size);
				}
				
				if (tep_not_null($file_name_2)) {
					$result2 = check_uploading_file ($file_name_2, $allow_file_type, $max_file_size);
				}
				
				if ($result == 'approved' && $result2 == 'approved') {
					$file_1 = $_FILES[$file_name_1]['tmp_name'];
					$file_2 = $_FILES[$file_name_2]['tmp_name'];
	    			
	    			unset($response);
	    			
                    $url = tep_upload_href_link(FILENAME_UPLOAD, "action=ajax_upload&customer_id=".$customer_id."&req_grp_id=".$req_grp_id."&file_name_1=".$file_name_1."&file_name_2=".$file_name_2."&sup_language=".$sup_language, 'SSL');
                    $agent = "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.0)";
                    $ch = curl_init();
                    curl_setopt($ch, CURLOPT_URL,$url);
                    curl_setopt($ch, CURLOPT_VERBOSE, 1);
                    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 2);
                    curl_setopt($ch, CURLOPT_POST, 1);
					curl_setopt($ch, CURLOPT_POSTFIELDS, array($file_name_1 => "@$file_1", $file_name_2 => "@$file_2"));
                    curl_setopt($ch, CURLOPT_TIMEOUT, 120);
                    curl_setopt($ch, CURLOPT_USERAGENT, $agent);
                    curl_setopt($ch, CURLOPT_RETURNTRANSFER,1);
                    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);  //Windows 2003 Compatibility
                    $response = curl_exec($ch);
                    curl_close($ch);

                    echo $response;
					
				} else {
					if ($result != 'approved') {
						echo '<error_msg_1><![CDATA['.$result.']]></error_msg_1>';
					}
					if ($result2 != 'approved') {
						echo '<error_msg_2><![CDATA['.$result2.']]></error_msg_2>';
					}
				}
    		break;
    	case 'pre_selling_info':
    		global $currencies, $sup_languages_id;
            
            if (!isset($_SESSION['customer_id'])) {
                echo "You are not allowed to request";
                echo '</response>';
                exit;
            }
            
            require_once(DIR_WS_CLASSES . 'anti_robot.php');
            $anti_robot_obj = new anti_robot();
            
            if ($anti_robot_obj->traffic_handling() === TRUE) {
                echo '<error>1</error>';
                echo '<notice></notice>';
                echo '<gotoURL>' . tep_href_link(FILENAME_WAITING_ROOM) . '</gotoURL>';
                echo '</response>';
                exit;
            }
            
            unset($anti_robot_obj);
            
			$xml_str = '';
		    $showError = false;
		    $product_id = '&nbsp;';
		    $game_pull_down_menu = '<select><option>'.PULL_DOWN_DEFAULT.'</option></select>';
		    $server_pull_down_menu = '<select><option>'.PULL_DOWN_DEFAULT.'</option></select>';
		    $trading_place = '&nbsp;';
			
		    if ($buyback_parent_cat_id > 0) {
		        $actual_cat_id = tep_get_actual_product_cat_id($buyback_products_id);
				
				$buybackSupplierObj = new buyback_supplier($buyback_parent_cat_id, $buyback_products_id);
				$currencies->set_decimal_places($buybackSupplierObj->_DECIMAL_PLACES_DISPLAY);
				$buybackSupplierObj->calculate_offer_price();
				
				if (!isset($buybackSupplierObj->products_arr[$buyback_products_id])) {
					$showError = true;
				} else {
					$product_info_arr = $buybackSupplierObj->products_arr[$buyback_products_id];
					if ($buybackSupplierObj->buyback_supplier_settings['ofp_buyback_list_controller_status']=='0' || $product_info_arr['is_buyback'] == false) {
						$showError = true;
					}
				}
		    }
		    
		    if ($showError) {
		        $xml_str .= "<error>3</error>";
		        $xml_str .= "<notice>0</notice>";
		    } else {
		       	$unit_price_base = $product_info_arr['avg_offer_price'];	/*USD*/
				$unit_price_localised = $currencies->do_raw_conversion($unit_price_base, true, $_SESSION['currency'], '', 'buy');	/*Localised*/
                $unit_price_localised = number_format($unit_price_localised, $buybackSupplierObj->_DECIMAL_PLACES_DISPLAY, '.', '');
		    	$unit_price_localised_display = $currencies->format($unit_price_base, true, $_SESSION['currency'], '', 'buy');
		    	$currency_symbol = $currencies->currencies[$_SESSION['currency']]['symbol_left'];
		    	
		    	$buybackSupplierObj = new buyback_supplier($buyback_parent_cat_id);
		    	$currencies->set_decimal_places($buybackSupplierObj->_DECIMAL_PLACES_DISPLAY);
				$buybackSupplierObj->calculate_offer_price();
				$trading_place = $buybackSupplierObj->buyback_supplier_settings['ofp_trading_place'];

				$wbb_game_list_arr = tep_get_game_list_arr();
				$game_pull_down_menu = tep_draw_pull_down_menu('wbb_input_game_select', $wbb_game_list_arr, $buyback_parent_cat_id, 'class="gameSelection" id="wbb_input_game_select" onChange="onListingServer(\'0\', this.value);" ');
				
				$game_pulldown_arr[] = array('id'   => 0,
            	                 		 	 'text' => PULL_DOWN_DEFAULT);
               		 		 
				foreach ($buybackSupplierObj->products_arr as $products_id => $products_arr) {
					if ($products_arr['is_buyback'] == 1) {
						$game_pulldown_arr[] = array('id'   => $products_id,
            	                 		 		 	 'text' => $products_arr['categories_name']);
					}
				}
				
				$get_actual_qty_select_sql = "SELECT products_actual_quantity FROM " . TABLE_PRODUCTS . " WHERE products_id = '".(int)$buyback_products_id."'";
				$get_actual_qty_select_result = tep_db_query($get_actual_qty_select_sql);
				$actual_qty = 0;
				if ($get_actual_qty_row = tep_db_fetch_array($get_actual_qty_select_result)) {
					$actual_qty = $get_actual_qty_row['products_actual_quantity'];
				}
				
				if ($buybackSupplierObj->products_arr[$buyback_products_id]['forecast_available_qty'] < 0) {
					$buyback_qty_listing_select_sql =	"	SELECT o.orders_id, o.date_purchased, op.orders_products_id, op.products_id, op.products_quantity, op.products_delivered_quantity, op.orders_products_purchase_eta  
															FROM " . TABLE_ORDERS . " AS o
															INNER JOIN " . TABLE_ORDERS_PRODUCTS . " AS op 
																ON o.orders_id = op.orders_id 
															WHERE op.products_id = '".(int)$buyback_products_id."' 
															AND o.orders_status	IN ( 2, 7 ) 
															AND (op.orders_products_purchase_eta <> -999 OR op.orders_products_purchase_eta IS NULL)
															ORDER BY o.date_purchased";
															
					$buyback_qty_listing_result = tep_db_query($buyback_qty_listing_select_sql);
					$buyback_qty_listing_num = tep_db_num_rows($buyback_qty_listing_result);
					
					if ($buyback_qty_listing_num > 0) {
						$order_id_array = array();
						
						$main_cat_eta_offset = tep_get_game_preset_eta($buyback_products_id);
						$current_time  = time();
						
						while($buyback_qty_listing_row = tep_db_fetch_array($buyback_qty_listing_result)) {
							$vip_allocation_select_sql = "	SELECT orders_products_id 
															FROM ".TABLE_VIP_ORDER_ALLOCATION." 
															WHERE orders_products_id = '".(int)$buyback_qty_listing_row['orders_products_id']."'";
							if (tep_db_num_rows(tep_db_query($vip_allocation_select_sql)) == 0) {
								$first_list_qty = 0;
								$is_buyback_order = false;
								
								//if (!tep_not_null($buyback_qty_listing_row['orders_products_purchase_eta']) || $buyback_qty_listing_row['orders_products_purchase_eta'] == 0) {
								if (($buyback_qty_listing_row['orders_products_purchase_eta'] === '0') || (!tep_not_null($buyback_qty_listing_row['orders_products_purchase_eta']))) {
									$is_buyback_order = true;
								} else if ($buyback_qty_listing_row['orders_products_purchase_eta'] > 0) {
									
									$extra_hour = $main_cat_eta_offset + $buyback_qty_listing_row['orders_products_purchase_eta'];
									$eta = strtotime($buyback_qty_listing_row['date_purchased']." + ".$extra_hour." hours");
									
									if ($current_time > $eta) {
										$is_buyback_order = true;
									}
								}
								
								if ($is_buyback_order) {
									$first_list_qty = tep_get_so_first_list_quantity($buyback_qty_listing_row['orders_id'], $buyback_qty_listing_row['orders_products_id']);
									$product_qty = ($buyback_qty_listing_row['products_quantity'] - $buyback_qty_listing_row['products_delivered_quantity']) - $first_list_qty;
								
									if ($product_qty > 0) {
										$order_id_array[$buyback_qty_listing_row['orders_products_id']] = array("products_id" => $buyback_qty_listing_row['products_id'], 
																												"product_qty" => $product_qty);
									}
								}
							}
						}
					}
				} else {
					$max_inventory_space_overwrite = $buybackSupplierObj->products_arr[$buyback_products_id]['max_inventory_space_overwrite'];
					if (tep_not_null($max_inventory_space_overwrite) && $max_inventory_space_overwrite > 0) {
						$extra_product_qty = $buybackSupplierObj->products_arr[$buyback_products_id]['max_qty'];
						$order_id_array['extra_inventory'] = array("products_id" => $buyback_products_id, 
																   "product_qty" => $extra_product_qty);
					}
				}

				$orders_products_id_cnt = 0;
				$customer_order_id = '';
				$orders_products_id_text = '';
				
				$autoflow_css = count($order_id_array) > 5 ? 'overflow: auto;border:solid 0px #CCCCCC;' : '';
				$customer_order_id .= '<div style="'.$autoflow_css.'height: 147px;width:100%;padding:5px 0px 0px 0px;">';
				$customer_order_id .= '<table border="0" cellpadding="2" cellspacing="0">';
				
				if (count($order_id_array) > 0) {
					asort($order_id_array);
					
					$buybackSupplierObj = new buyback_supplier($buyback_parent_cat_id, $buyback_products_id);
					$buybackSupplierObj->vip = true;
					$buybackSupplierObj->calculate_offer_price();
					
					foreach ($order_id_array as $orders_products_id => $order_info) {
						$min_qty = $product_info_arr['min_qty'];
						$trading_method = '';
						
						$excluded_text = '';
						$min_purchse_qty = (int)$buybackSupplierObj->supplier_price_brackets_sets[$buybackSupplierObj->products_arr[$order_info['products_id']]['bracket_set_id']]['min_purchase_qty'];
						
						if ($orders_products_id > 0) {
							$delivery_info_select_sql = "	SELECT opei.orders_products_extra_info_value 
															FROM " . TABLE_ORDERS_PRODUCTS_EXTRA_INFO . " AS opei 
															INNER JOIN " . TABLE_ORDERS_PRODUCTS . " AS op 
																ON (opei.orders_products_id = op.parent_orders_products_id)
															WHERE op.orders_products_id = '".(int)$orders_products_id."' 
																AND opei.orders_products_extra_info_key = 'delivery_mode'";
							$delivery_info_result_sql = tep_db_query($delivery_info_select_sql);
							if ($delivery_info_row = tep_db_fetch_array($delivery_info_result_sql)) {
								$trade_mode = tep_get_supplier_trade_mode($delivery_info_row['orders_products_extra_info_value']);
								if (tep_not_null($trade_mode)) {
									$delivery_method = tep_display_supplier_trade_mode($trade_mode);
									$trading_method = sprintf(TEXT_TRADE_METHOD, $delivery_method);
								}
							}
						} else {
							$trade_mode = 'extra_inventory';
							$delivery_method = tep_display_supplier_trade_mode('extra_inventory');
							$trading_method = sprintf(TEXT_TRADE_METHOD, $delivery_method);
						}
						
						if ($product_info_arr['min_qty'] > $order_info['product_qty']) {
							$min_qty = $order_info['product_qty'];
						} else {
							if (tep_not_null($min_purchse_qty) || $min_purchse_qty > 0) {
								if ($min_qty < ($order_info['product_qty'] - $min_purchse_qty)) {
									$excluded_qty_from = ($order_info['product_qty'] - $min_purchse_qty) + 1;
									$excluded_qty_to = $order_info['product_qty'] - 1;
									$excluded_text = '  ( '.$excluded_qty_from.' - '.$excluded_qty_to.': '.TEXT_NOT_ACCEPTED . ' ) ';
								}
							}
						}
						
						$orders_products_id_text .= $orders_products_id_cnt == 0 ? $orders_products_id : ','.$orders_products_id;
						
						$customer_order_id .= '<tr>';
						$customer_order_id .= '		<td>'.$order_info['product_qty'].'&nbsp;&nbsp;</td>';
						$customer_order_id .= '		<td>';
						$customer_order_id .= 			tep_draw_input_field('qty_delivered_'.$orders_products_id, TEXT_QTY_DELIVERED, ' id="qty_delivered_'.$orders_products_id.'" size="12" class="qty_delivered" style="height:25px;font-size:10px;color:silver;padding:3.5px;" maxlength="16" onClick="reset_input_field(\''.$orders_products_id.'\', \''.TEXT_QTY_DELIVERED.'\');document.getElementById(\'trade_note_'.$orders_products_id.'\').style.display=\'block\';" onkeydown="if(event.keyCode == 13) {AssignMargin(this.value);Calculate_Selling_Price();}" onKeyUP="if (trim(this.value) != \'\' && !validateInteger(trim(this.value))) { this.value = \'\'; } else {CalculateTotalPrice();}" onBlur="if(this.value == \'\') {this.value=\''.TEXT_QTY_DELIVERED.'\';}"').'<span style="display:none;">'.$unit_price_localised.'</span>&nbsp;&nbsp;';
						$customer_order_id .= 			tep_draw_hidden_field('opid_'.$orders_products_id, $orders_products_id, 'id="opid_'.$orders_products_id.'"');
						$customer_order_id .= 			tep_draw_hidden_field('trade_mode_'.$orders_products_id, $trade_mode, 'id="trade_mode_'.$orders_products_id.'"');
						//$customer_order_id .= 			tep_draw_input_field('char_id_'.$order_id, TEXT_DELIVERED_ID, ' id="char_id_'.$order_id.'" size="12" class="delivered_id" style="height:20px;font-size:10px;color:silver;" maxlength="16" onClick="this.value = \'\';reset_input_field(\'delivered_id\', \'char_id_'.$order_id.'\', \''.TEXT_DELIVERED_ID.'\');" onBlur="if(this.value == \'\') {this.value=\''.TEXT_DELIVERED_ID.'\';}"').'&nbsp;&nbsp;';
						//$customer_order_id .= 			'<a href="javascript:void(0);" onClick="PreSubmit(\''.$order_id.'\');">Trade Now</a><br />';
						$customer_order_id .= '		</td>';
						$customer_order_id .= '		<td style="color:#333333;">
														<div id="trade_note_'.$orders_products_id.'" class="trade_note" style="display:none;">' . TEXT_MIN.'<span id="min_qty_'.$orders_products_id.'">'.$min_qty. '</span> '.TEXT_MAX.'<span id="max_qty_'.$orders_products_id.'">'.$order_info['product_qty'].'</span>'.$excluded_text.' <br /> '.$trading_method.'</div>
														<div id="err_qty_invalid_'.$orders_products_id.'" class="bb_error_msg" style="display:none;font-weight:bold;color:red;">'.ERROR_QUANTITY_INVALID.'</div>
														<div id="err_selling_qty_empty_'.$orders_products_id.'" class="bb_error_msg" style="display:none;font-weight:bold;color:red;">'.WARNING_PLS_FILL_IN_SELLING_QTY.'</div>
													</td>';
						$customer_order_id .= '</tr>';
						
						$orders_products_id_cnt ++;
					}
				}
				
				$customer_order_id .= '</table>';
				$customer_order_id .= '</div>';
				$customer_order_id .= '<span id="orders_products_id_array" style="display:none;">'.$orders_products_id_text.'</span>';
				
				$captcha_img = tep_image('securimage_show.php?sid='.md5(uniqid(time())));
				
				$account_edit_link = tep_href_link(FILENAME_ACCOUNT_EDIT, 'trace=bb_chkout&gcat='.$buyback_parent_cat_id.'&pid='.$buyback_products_id, 'SSL');
				
				$server_pull_down_menu = tep_draw_pull_down_menu('wbb_input_product_select', $game_pulldown_arr, $buyback_products_id, 'id="wbb_input_product_select" onChange="sell_this_form(this.value, \''.$buyback_parent_cat_id.'\')"');
		        $xml_str = "<error>0</error>";
		        $xml_str .= "<notice>1</notice>";
			    $xml_str .= "<product_id><![CDATA[".$buyback_products_id."]]></product_id>";
			    $xml_str .= "<game_pull_down_menu><![CDATA[".$game_pull_down_menu."]]></game_pull_down_menu>";
			    $xml_str .= "<server_pull_down_menu><![CDATA[".$server_pull_down_menu."]]></server_pull_down_menu>";
			    $xml_str .= "<trading_place><![CDATA[".$trading_place."]]></trading_place>";
			    $xml_str .= "<trading_method><![CDATA[".$trading_method."]]></trading_method>";
			    $xml_str .= "<unit_price_base><![CDATA[".$unit_price_base."]]></unit_price_base>";
                $xml_str .= "<unit_price_display><![CDATA[".$unit_price_localised_display."]]></unit_price_display>";
		        //$xml_str .= "<min><![CDATA[".$product_info_arr['min_qty']."]]></min>";
		        //$xml_str .= "<max><![CDATA[".$product_info_arr['max_qty']."]]></max>";
		        $xml_str .= "<buyback_qty_listing><![CDATA[".$customer_order_id."]]></buyback_qty_listing>";
		        $xml_str .= "<captcha_img><![CDATA[".$captcha_img."]]></captcha_img>";
			    $xml_str .= "<product_unit_name><![CDATA[".$product_info_arr['qty_unit']."]]></product_unit_name>";
			    $xml_str .= "<currency_symbol><![CDATA[".$currency_symbol."]]></currency_symbol>";
			    $xml_str .= "<account_edit_link><![CDATA[".$account_edit_link."]]></account_edit_link>";
			    //$xml_str .= "<total_price><![CDATA[".$total_price."]]></total_price>";
		    }
		    
		    echo $xml_str;
    		break;
    	case 'get_server_pull_down':
    		$server_pull_down_menu = '<select><option>'.PULL_DOWN_DEFAULT.'</option></select>';
    		
    		if ($buyback_parent_cat_id > 0) {
		        $actual_cat_id = tep_get_actual_product_cat_id($buyback_products_id);
				
				$buybackSupplierObj = new buyback_supplier($buyback_parent_cat_id, $buyback_products_id);
				$currencies->set_decimal_places($buybackSupplierObj->_DECIMAL_PLACES_DISPLAY);
				$buybackSupplierObj->calculate_offer_price();
				
				if (!isset($buybackSupplierObj->products_arr[$buyback_products_id])) {
					$showError = true;
				} else {
					$product_info_arr = $buybackSupplierObj->products_arr[$buyback_products_id];
					if ($buybackSupplierObj->buyback_supplier_settings['ofp_buyback_list_controller_status']=='0' || $product_info_arr['is_buyback'] == false) {
						$showError = true;
					}
				}
		    }
		    
    		$buybackSupplierObj = new buyback_supplier($buyback_parent_cat_id);
    		$currencies->set_decimal_places($buybackSupplierObj->_DECIMAL_PLACES_DISPLAY);
			$buybackSupplierObj->calculate_offer_price();
			
    		$game_pulldown_arr[] = array('id'   => 0,
            	               		 	 'text' => PULL_DOWN_DEFAULT);
               		 		 
			foreach ($buybackSupplierObj->products_arr as $products_id => $products_arr) {
				if ($products_arr['is_buyback'] == 1) {
					$game_pulldown_arr[] = array('id'   => $products_id,
        	                 		 		 	'text' => $products_arr['categories_name']);
				}
			}
			
			$server_pull_down_menu = tep_draw_pull_down_menu('wbb_input_product_select', $game_pulldown_arr, '0', 'id="wbb_input_product_select" onChange="sell_this_form(this.value, \''.$buyback_parent_cat_id.'\')"');
			echo "<server_pull_down_menu><![CDATA[".$server_pull_down_menu."]]></server_pull_down_menu>";
    		break;	
		default:
			echo "<result>Unknown request!</result>";
			break;
	}
}

echo '</response>';

function check_uploading_file ($fileElementName, $allow_file_type, $max_file_size) {
	$msg = '';
	$pathinfo_arr = pathinfo($_FILES[$fileElementName]['name']);
	$file_type = strtolower($pathinfo_arr['extension']);
	
	if (($max_file_size * 1024) > $_FILES[$fileElementName]['size']) {
		if ($_FILES[$fileElementName]['error'] > 0) {
			$msg = $_FILES[$fileElementName]['error'];
		} elseif (!tep_not_null($_FILES[$fileElementName]['tmp_name']) || $_FILES[$fileElementName]['tmp_name'] == 'none') {
			$msg = '8';
		} else {
			if (in_array($file_type, $allow_file_type)) {
				$msg = 'approved';
			} else {
				$msg = '9';
			}
		}
	} else {
		$msg = '10';
	}
	return $msg;
}

function tep_xmlhttp_product_buyback_info($buyback_parent_cat_id, $buyback_products_id) {
	global $currencies, $sup_languages_id;
	
	$xml_str = '';
    $showError = false;
    $min_value = 0;
    $max_value = 0;
    $product_name = '&nbsp;';
    $product_id = '&nbsp;';
	
    if ($buyback_parent_cat_id > 0) {
        $actual_cat_id = tep_get_actual_product_cat_id($buyback_products_id);
		
		$buybackSupplierObj = new buyback_supplier($buyback_parent_cat_id, $buyback_products_id);
		$currencies->set_decimal_places($buybackSupplierObj->_DECIMAL_PLACES_DISPLAY);
		$buybackSupplierObj->calculate_offer_price();
		
		if (!isset($buybackSupplierObj->products_arr[$buyback_products_id])) {
			$showError = true;
		} else {
			$product_info_arr = $buybackSupplierObj->products_arr[$buyback_products_id];
			if ($buybackSupplierObj->buyback_supplier_settings['ofp_buyback_list_controller_status']=='0' || $product_info_arr['is_buyback'] == false) {
				$showError = true;
			}
		}
    }
	
    if ($showError) {
        $xml_str .= "<error>3</error>";
        $xml_str .= "<notice>0</notice>";
    } else {
       	$unit_price_base = $product_info_arr['avg_offer_price'];	/*USD*/
		$unit_price_localised = $currencies->do_raw_conversion($unit_price_base, true, BUYBACK_PRICE_DEFAULT_CURRENCY, '', 'buy');	/*Localised*/
    	$unit_price_localised_display = $currencies->format($unit_price_base, true, BUYBACK_PRICE_DEFAULT_CURRENCY, '', 'buy');
    	
        $xml_str = "<error>0</error>";
        $xml_str .= "<notice>1</notice>";
	    $xml_str .= "<product_id><![CDATA[".$buyback_products_id."]]></product_id>";
		$xml_str .= "<unit_price><![CDATA[".$unit_price_localised."]]></unit_price>";
	    $xml_str .= "<unit_price_display><![CDATA[".$unit_price_localised_display."]]></unit_price_display>";
        $xml_str .= "<min><![CDATA[".$product_info_arr['min_qty']."]]></min>";
        $xml_str .= "<max><![CDATA[".$product_info_arr['max_qty']."]]></max>";
	    $xml_str .= "<product_unit_name><![CDATA[".$product_info_arr['qty_unit']."]]></product_unit_name>";
	    
	    if (isset($product_info_arr['upper_min_qty']) && $product_info_arr['upper_min_qty'] > 0 && $product_info_arr['upper_min_qty'] < $product_info_arr['max_qty']) {
			$xml_str .= "<bo_exact_qty_msg><![CDATA[".sprintf(TEXT_MATCH_BACKORDER_EXACT_AMOUNT, $product_info_arr['upper_min_qty'], $product_info_arr['max_qty'], $product_info_arr['upper_min_qty']+1, $product_info_arr['max_qty']-1)."]]></bo_exact_qty_msg>";
		}
    }
    
    echo '<selected_product>';
    echo $xml_str;
    echo '</selected_product>';
    
    unset($buybackProductObj);
}
?>