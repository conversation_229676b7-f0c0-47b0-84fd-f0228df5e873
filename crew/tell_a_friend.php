<?php
/*
  $Id: tell_a_friend.php,v 1.3 2007/11/16 02:17:20 chan Exp $

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2003 osCommerce

  Released under the GNU General Public License
*/

require('includes/application_top.php');

if (!tep_session_is_registered('customer_id') && (ALLOW_GUEST_TO_TELL_A_FRIEND == 'false')) {
	$navigation->set_snapshot();
	tep_redirect(tep_href_link(FILENAME_LOGIN, '', 'SSL'));
}

$valid_product = false;

if (isset($HTTP_GET_VARS['products_id'])) {
	$product_info_select_sql = "SELECT pd.products_name, custom_products_type_id 
								FROM " . TABLE_PRODUCTS . " p, " . TABLE_PRODUCTS_DESCRIPTION . " pd 
								WHERE p.products_status = '1' AND p.products_id = '" . (int)$HTTP_GET_VARS['products_id'] . "' 
									AND p.products_id = pd.products_id 
									AND pd.language_id = '" . (int)$languages_id . "'";
	$product_info_query = tep_db_query($product_info_select_sql);
	
	if (tep_db_num_rows($product_info_query)) {
		$valid_product = true;
		$product_info = tep_db_fetch_array($product_info_query);
	}
}

if ($valid_product == false) {
	tep_redirect(tep_href_link(FILENAME_PRODUCT_INFO, 'products_id=' . $HTTP_GET_VARS['products_id'] . '&tpl=' . $product_info['custom_products_type_id']));
}

require(DIR_WS_LANGUAGES . $language . '/' . FILENAME_TELL_A_FRIEND);

if (isset($HTTP_GET_VARS['action']) && ($HTTP_GET_VARS['action'] == 'process')) {
	$error = false;
	
	$to_email_address = tep_db_prepare_input($HTTP_POST_VARS['to_email_address']);
	$to_name = tep_db_prepare_input($HTTP_POST_VARS['to_name']);
	$from_email_address = tep_db_prepare_input($HTTP_POST_VARS['from_email_address']);
	$from_name = tep_db_prepare_input($HTTP_POST_VARS['from_name']);
	$message = tep_db_prepare_input($HTTP_POST_VARS['message']);

	if (empty($from_name)) {
		$error = true;
		$messageStack->add('friend', ERROR_FROM_NAME);
	}

	if (!tep_validate_email($from_email_address)) {
		$error = true;
		$messageStack->add('friend', ERROR_FROM_ADDRESS);
	}

	if (empty($to_name)) {
		$error = true;
		$messageStack->add('friend', ERROR_TO_NAME);
	}

	if (!tep_validate_email($to_email_address)) {
		$error = true;
		$messageStack->add('friend', ERROR_TO_ADDRESS);
	}

	if ($error == false) {
		$email_subject = sprintf(TEXT_EMAIL_SUBJECT, $from_name, STORE_NAME);
		$email_body = sprintf(TEXT_EMAIL_INTRO, $to_name, $from_name, $product_info['products_name'], STORE_NAME) . "\n\n";

		if (tep_not_null($message)) {
			$email_body .= $message . "\n\n";
		}
		
		$email_body .= sprintf(TEXT_EMAIL_LINK, tep_href_link(FILENAME_CUSTOM_PRODUCT_INFO, 'products_id=' . $HTTP_GET_VARS['products_id'] . '&tpl=' . $product_info['custom_products_type_id'])) . "\n\n" .
		sprintf(TEXT_EMAIL_SIGNATURE, STORE_NAME . "\n" . HTTP_SERVER . DIR_WS_CATALOG . "\n");
		
		tep_mail($to_name, $to_email_address, $email_subject, $email_body, $from_name, $from_email_address);

		$messageStack->add_session('header', sprintf(TEXT_EMAIL_SUCCESSFUL_SENT, $product_info['products_name'], tep_output_string_protected($to_name)), 'success');

		tep_redirect(tep_href_link(FILENAME_CUSTOM_PRODUCT_INFO, 'products_id=' . $HTTP_GET_VARS['products_id'] . '&tpl=' . $product_info['custom_products_type_id']));
	}
} elseif (tep_session_is_registered('customer_id')) {
	$account_query = tep_db_query("select customers_firstname, customers_lastname, customers_email_address from " . TABLE_CUSTOMERS . " where customers_id = '" . (int)$customer_id . "'");
	$account = tep_db_fetch_array($account_query);
	
	$from_name = $account['customers_firstname'] . ' ' . $account['customers_lastname'];
	$from_email_address = $account['customers_email_address'];
}

$breadcrumb->add(NAVBAR_TITLE, tep_href_link(FILENAME_TELL_A_FRIEND, 'products_id=' . $HTTP_GET_VARS['products_id'] . '&tpl=' . $product_info['custom_products_type_id']));

$content = CONTENT_TELL_A_FRIEND;

require(DIR_WS_TEMPLATES . TEMPLATENAME_MAIN_PAGE);

require(DIR_WS_INCLUDES . 'application_bottom.php');
?>
