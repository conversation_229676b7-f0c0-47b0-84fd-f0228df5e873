<h1><?=HEADING_TITLE?></h1>
<div class="breakLine"><!-- --></div>

<?php if ($messageStack->size('account') > 0) { ?>
	<div class="loginColumnBorder" style="background-color: #FFFDDB">
		<div>
			<div class="breakLine"><!-- --></div>
			<div style="padding-left:5pt;"><?=$messageStack->output('account'); ?></div>
			<div class="breakLine"><!-- --></div>
		</div>
	</div>
	<div class="breakLine"><!-- --></div>
<?
}

if ($messageStack->size('my_account_home') > 0) {
?>
	<div class="loginColumnBorder" style="background-color: #FFFDDB">
		<div>
			<div class="breakLine"><!-- --></div>
			<div><?=$messageStack->output('my_account_home'); ?></div>
			<div class="breakLine"><!-- --></div>
		</div>
	</div>
	<div class="breakLine"><!-- --></div>
<?php } ?>

<? if (tep_not_null($banner_img_url)) { ?>
		<div style="text-align:center; line-height:100px;">
		<a href=<?=$banner_url?>><span style="font-size:30px;"><?php echo tep_image($banner_img_url, '', 752, 120); ?></span></a>
 		</div> 
 		<br>
<?	
}
ob_start(); 
?> 

	<table width="100%" border="0" cellspacing="0" cellpadding="0"> 
		<tr >
			<td colspan="3" align="center" style="padding:5px 8px 3px 8px;" >
				<table width="100%" align="right" border="0" cellspacing="0" cellpadding="0"> 
					<tr>
						<td style="font-size:16px;font-weight:bold;width:200px;"><?=TITLE_MY_LATEST_ORDERS?></td>
						<td style="text-align:center;width:100px; font-size: 11px;"><img src="<?=DIR_WS_ICONS."icon_cdk.gif" ?>" style="vertical-align:middle;">&nbsp;&nbsp;<?=TEXT_LEGEND_GAME_CARD ?></td>
						<td style="text-align:center;width:100px; font-size: 11px;"><img src="<?=DIR_WS_ICONS."icon_cur.gif" ?>" style="vertical-align:middle;">&nbsp;&nbsp;<?=TEXT_LEGEND_GAME_CURRENCY ?></td>
						<td style="text-align:center;width:100px; font-size: 11px;"><img src="<?=DIR_WS_ICONS."icon_pwl.gif" ?>" style="vertical-align:middle;">&nbsp;&nbsp;<?=TEXT_LEGEND_POWER_LEVELING ?></td>
						<td style="text-align:center;width:100px; font-size: 11px;"><img src="<?=DIR_WS_ICONS."icon_sc.gif" ?>" style="vertical-align:middle;">&nbsp;&nbsp;<?=TEXT_LEGEND_STORE_CREDIT ?></td>
						<td style="text-align:center;width:100px; font-size: 11px;"><img src="<?=DIR_WS_ICONS."icon_hla.gif" ?>" style="vertical-align:middle;">&nbsp;&nbsp;<?=TEXT_LEGEND_HIGH_LEVEL_ACCOUNT ?></td>
					</tr>
				</table>
			</td>
		</tr>
		<tr>
			<td colspan="3">
				<div style="line-height: 5px; background-image: url('<?=DIR_WS_IMAGES."hr.jpg"?>'); background-position:top center;">&nbsp;</div>
			</td>
		</tr>
		<tr>
			<td align="center" valign="top">
				<div class="breakLine" style="height:5px;"><!-- --></div>
					<table id="tbl_buy_order" border="0" cellpadding="0" cellspacing="0" align="center">
						<tr>
							<td id=td_buy_order valign="top">
								<table border="0" cellpadding="0" cellspacing="0">
									<tr>
										<td>
											<div style="float:left">
											<div class="boxHeaderLeft"></div>
											<div class="boxHeaderCenter" style="width: 340px;">
												<font style="color: #ffffff;"><?=TEXT_BUY_ORDER ?></font>
												</div>
											<div class="boxHeaderRight"></div>
											</div>
										</td>
									</tr>
								</table>
								<table width="100%" border="0" cellpadding="0" cellspacing="4">
<?
	$orders_select_sql = "	SELECT o.orders_id, o.date_purchased, o.delivery_name, o.delivery_country, o.billing_name, o.billing_country, ot.text as order_total, s.orders_status_name, s.orders_status_id 
		 					FROM " . TABLE_ORDERS . " AS o 
                                                        LEFT JOIN `" . TABLE_ORDERS_EXTRA_INFO . "` AS `oei`
                                                            ON `oei`.`orders_id` = `o`.`orders_id`
                                                            AND `oei`.`orders_extra_info_key` = 'site_id'
		 					INNER JOIN " . TABLE_ORDERS_TOTAL . " AS ot 
		 						ON (o.orders_id = ot.orders_id AND ot.class = 'ot_total') 
                                                        LEFT JOIN `" . TABLE_ORDERS_STATUS . "` AS `s`
                                                                ON `s`.`orders_status_id` = `o`.`orders_status`
		 					WHERE o.customers_id = '" . (int)$customer_id . "' 
		 						AND (IF (s.language_id = '" . (int)$languages_id . "', 1, IF((select COUNT(s.orders_status_id) > 0
                                                                    from " . TABLE_ORDERS_STATUS . "
                                                                    where s.orders_status_id = o.orders_status
                                                                    AND s.language_id = '" . (int)$languages_id . "'), 0, s.language_id = '" . (int)$default_languages_id . "'))) 
		 						AND o.orders_status IN (1,2,3,7,8)
								AND o.date_purchased >= DATE_SUB(CURDATE(), INTERVAL 14 DAY)
                                                                AND (`oei`.`orders_extra_info_value` IS NULL OR `oei`.`orders_extra_info_value` = '4') 
		 					ORDER BY o.date_purchased DESC 
		 					LIMIT 10";

	$orders_result_sql = tep_db_query($orders_select_sql);
	$count_line = 0;
	$order_nums = tep_db_num_rows($orders_result_sql);
	
	if (!$order_nums) {
			echo TEXT_NO_LAST_BUY_ORDERS;
	} else {
		while ($orders_row = tep_db_fetch_array($orders_result_sql)) {
			$count_line++;

			//$class_dottedLine = ($count_line < 20 && $order_nums >= 1)? "dottedLine" : "";
			$class_dottedLine = ($count_line)? "dottedLine" : "";

			$cur = 0;
			$pwl = 0;
			$cdk = 0;
	      	$scr = 0;
	      	$hla = 0;

	      	$orders_products_select_sql = "SELECT products_id FROM " . TABLE_ORDERS_PRODUCTS . " WHERE orders_id='" . (int)$orders_row['orders_id'] . "' ";
	      	$orders_products_result_sql = tep_db_query($orders_products_select_sql);
	      	while($orders_products_row = tep_db_fetch_array($orders_products_result_sql)) {
	      		$custom_product_type = tep_get_custom_product_type($orders_products_row['products_id']);
	      		$cur = ($custom_product_type == 0 || $cur == 1)? 1: 0;
	      		$pwl = ($custom_product_type == 1 || $pwl == 1)? 1: 0;
	      		$cdk = ($custom_product_type == 2 || $cdk == 1)? 1: 0;
	      		$scr = ($custom_product_type == 3 || $scr == 1)? 1: 0;
	      		$hla = ($custom_product_type == 4 || $hla == 1)? 1: 0;
	      	}

			$icon_cur = ($cur == 1)? '<img src="' . DIR_WS_ICONS . 'icon_cur.gif" style="vertical-align:middle;">': '&nbsp; ';
			$icon_pwl = ($pwl == 1)? '<img src="' . DIR_WS_ICONS . 'icon_pwl.gif" style="vertical-align:middle;">': '&nbsp; ';
			$icon_cdk = ($cdk == 1)? '<img src="' . DIR_WS_ICONS . 'icon_cdk.gif" style="vertical-align:middle;">': '&nbsp; ';
			$icon_scr = ($scr == 1)? '<img src="' . DIR_WS_ICONS . 'icon_sc.gif" style="vertical-align:middle;">': '&nbsp; ';
			$icon_hla = ($hla == 1)? '<img src="' . DIR_WS_ICONS . 'icon_hla.gif" style="vertical-align:middle;">': '&nbsp; ';

	      	$icons_list	= '	<table width="90%" border="0" cellpadding="0" cellspacing="0">
	      						<tr>
	      							<td width="15px" align="center">' . $icon_cdk . '</td>
	      							<td width="15px" align="center">' . $icon_cur . '</td>
	      							<td width="15px" align="center">' . $icon_pwl . '</td>
	      							<td width="15px" align="center">' . $icon_scr . '</td>
	      							<td width="15px" align="center">' . $icon_hla . '</td>
	      						</tr>
	      					</table>';
	?>
									<tr valign="top">
										<td style="width:50px; font-weight:bold;"><a href="<?=tep_href_link(FILENAME_ACCOUNT_HISTORY_INFO, 'orders_type='.$orders_type_status_array[$orders_row['orders_status_id']].'&order_id='.$orders_row['orders_id'], 'SSL')?>"><?=$orders_row['orders_id']?></a>&nbsp;</td>
										<td style="width:150px;"><?=strftime("%d %b %Y, %A", strtotime(tep_date_short($orders_row['date_purchased'])))?>&nbsp;</td>
										<td style="width:60px;"><?=$icons_list?></td>
										<td style="width:50px;"><?=$orders_row['orders_status_name']?>&nbsp;</td>
									</tr>
	<?	if ($class_dottedLine) {	?>
									<tr><td class="<?=$class_dottedLine ?>" colspan="4" style="line-height:1px;">&nbsp;</td></tr>
	<?
			}
		}
	}
?>
								</table>
							<div class="breakLine"><!-- --></div>
						</td>
					</tr>
					</table>
			</td>
			<td width="2px" style="background-image: url('<?=DIR_WS_IMAGES."vr.jpg"?>'); background-repeat: repeat-y; background-position: top center;"></td>
			<td align="center" valign="top">
				<div class="breakLine" style="height:5px;"><!-- --></div>
				<table id="tbl_sell_order" border="0" cellpadding="0" cellspacing="0" height="281px" align="center">
					<tr>
						<td id="td_sell_order" valign="top" > 
							<div style="float:right;">
								<div class="boxHeaderLeft"></div>
								<div class="boxHeaderCenter" style="width: 340px;">
									<font style="color: #ffffff;"><?=TEXT_SELL_ORDER ?></font>
								</div>
								<div class="boxHeaderRight"></div>
							</div>
							<div style="clear:both;">
							<table width="100%" border="0" cellpadding="0" cellspacing="4">
<?php 
	$buyback_request_select_sql = "	SELECT brg.buyback_request_order_type, brg.buyback_request_group_id, brg.buyback_request_group_date, br.products_id, bs.buyback_status_name 
									FROM ". TABLE_BUYBACK_REQUEST_GROUP . " AS brg
									LEFT JOIN " . TABLE_BUYBACK_REQUEST . " AS br 
										ON (brg.buyback_request_group_id = br.buyback_request_group_id)
									INNER JOIN " . TABLE_BUYBACK_STATUS . " AS bs 
										ON ( brg.buyback_status_id = bs.buyback_status_id 
											AND bs.language_id = '" . tep_db_input($languages_id) . "')
									WHERE brg.customers_id = '" . tep_db_input($_SESSION['customer_id']) . "'
										AND buyback_request_group_user_type = '0'
										AND brg.buyback_request_group_date >= DATE_SUB(CURDATE(), INTERVAL 14 DAY) 
									ORDER BY brg.buyback_request_group_date DESC 
									LIMIT 10";
									
	$buyback_request_select_res = tep_db_query($buyback_request_select_sql);

	$buyback_line = 0;
	$buyback_nums = tep_db_num_rows($buyback_request_select_res);

	if (!$buyback_nums) {
		  echo TEXT_NO_LAST_SELL_ORDERS;
	} else {
		while ($buyback = tep_db_fetch_array($buyback_request_select_res)) {
			$buyback_line++;
			$class_dottedLine = ($buyback_line)? "dottedLine" : "";

			$bb_cur = 0;
			$bb_pwl = 0;
			$bb_cdk = 0;
			$bb_scr = 0;
			$bb_hla = 0;

			$bb_custom_product_type = tep_get_custom_product_type($buyback['products_id']);

			$bb_cur = ($bb_custom_product_type == 0 || $bb_cur == 1)? 1: 0;
			$bb_pwl = ($bb_custom_product_type == 1 || $bb_pwl == 1)? 1: 0;
			$bb_cdk = ($bb_custom_product_type == 2 || $bb_cdk == 1)? 1: 0;
	      	$bb_scr = ($bb_custom_product_type == 3 || $bb_scr == 1)? 1: 0;
	      	$bb_hla = ($bb_custom_product_type == 4 || $bb_hla == 1)? 1: 0;

			$bb_icon_cur = ($bb_cur == 1)? '<img src="' . DIR_WS_ICONS . 'icon_cur.gif" style="vertical-align:middle;">': '&nbsp; ';
			$bb_icon_pwl = ($bb_pwl == 1)? '<img src="' . DIR_WS_ICONS . 'icon_pwl.gif" style="vertical-align:middle;">': '&nbsp; ';
			$bb_icon_cdk = ($bb_cdk == 1)? '<img src="' . DIR_WS_ICONS . 'icon_cdk.gif" style="vertical-align:middle;">': '&nbsp; ';
			$bb_icon_scr = ($bb_scr == 1)? '<img src="' . DIR_WS_ICONS . 'icon_scr.gif" style="vertical-align:middle;">': '&nbsp; ';
			$bb_icon_hla = ($bb_hla == 1)? '<img src="' . DIR_WS_ICONS . 'icon_cur.gif" style="vertical-align:middle;">': '&nbsp; ';

			$bb_icons_list	= '	<table width="90%" border="0" cellpadding="0" cellspacing="0">
	      							<tr>
	      								<td width="15px" align="center">' . $bb_icon_cdk . '</td>
	      								<td width="15px" align="center">' . $bb_icon_cur . '</td>
	      								<td width="15px" align="center">' . $bb_icon_pwl . '</td>
	      								<td width="15px" align="center">' . $bb_icon_scr . '</td>
	      								<td width="15px" align="center">' . $bb_icon_hla . '</td>
	      							</tr>
	      						</table>';
?>
								<tr valign="top">
<?									if ($buyback['buyback_request_order_type'] == '0') { ?>
										<?=tep_draw_form('my_order_history_'.$buyback_line, FILENAME_MY_ORDER_HISTORY, 'post','') ?>
										<?=tep_draw_hidden_field('action', 'search') ?>
										<?=tep_draw_hidden_field('odh_input_order_no', $buyback['buyback_request_group_id']) ?>
										<?=tep_draw_hidden_field('odh_input_start_date', $buyback['buyback_request_group_date']) ?>
										<td style="width:50px; font-weight:bold;"><a onclick="document.forms['my_order_history_<?=$buyback_line ?>'].submit()" style="cursor:pointer;"><?=$buyback['buyback_request_group_id']?></a>&nbsp;</td>
										<td style="width:150px;"><?=strftime("%d %b %Y, %A", strtotime(tep_date_short($buyback['buyback_request_group_date'])))?>&nbsp;</td>
										<td style="width:60px;"><?=$bb_icons_list?></td>
										<td style="width:50px;"><?=$buyback['buyback_status_name']?>&nbsp;</td>

<?									} else { ?>
										<?=tep_draw_form('vip_order_history_'.$buyback_line, FILENAME_MY_VIP_ORDERS_HISTORY, 'post','') ?>
										<?=tep_draw_hidden_field('action', 'search') ?>
										<?=tep_draw_hidden_field('vipodh_input_order_no', $buyback['buyback_request_group_id']) ?>
										<?=tep_draw_hidden_field('vipodh_input_start_date', $buyback['buyback_request_group_date']) ?>
										<td style="width:50px; font-weight:bold;"><a onclick="document.forms['vip_order_history_<?=$buyback_line ?>'].submit()" style="cursor:pointer;"><?=$buyback['buyback_request_group_id']?></a>&nbsp;</td>
										<td style="width:150px;"><?=strftime("%d %b %Y, %A", strtotime(tep_date_short($buyback['buyback_request_group_date'])))?>&nbsp;</td>
										<td style="width:60px;"><?=$bb_icons_list?></td>
										<td style="width:50px;"><?=$buyback['buyback_status_name']?>&nbsp;</td>
<?									} ?>
									</form>
								</tr>
<?	 		if ($class_dottedLine) {	?>
								<tr>
									<td class="<?=$class_dottedLine ?>" colspan="4" style="line-height:1px;">&nbsp;</td> 
								</tr>
<?
			}
		} 
	}
?>
							</table>
							</div>
							<div class="breakLine"><!-- --></div>
						</td>
					</tr>
				</table>
			</td>
		</tr>
		<tr>
			<td>
				<div style="line-height: 2px; background-image: url('<?=DIR_WS_IMAGES."gray_hr.jpg"?>'); background-position:center center;">&nbsp;</div>
				<div class="breakLine" style="height:5px;"><!-- --></div>
				<?=tep_div_button(2, '&nbsp; '.BUTTON_VIEW_PAST_BUY_ORDERS.' &nbsp;', tep_href_link(FILENAME_ACCOUNT_HISTORY, '', 'SSL'), ' style="float:right; padding-right:5px;"', 'green_button') ?>
				<div class="breakLine" style="height:5px;"><!-- --></div>
				<div style="line-height: 2px; background-image: url('<?=DIR_WS_IMAGES."gray_hr.jpg"?>'); background-position:center center;">&nbsp;</div>
			</td>
			<td width="2px" style="background-image: url('<?=DIR_WS_IMAGES."vr.jpg"?>'); background-repeat: repeat-y; background-position: top center;"></td>
			<td>
				<div style="line-height: 2px; background-image: url('<?=DIR_WS_IMAGES."gray_hr.jpg"?>'); background-position:center center;">&nbsp;</div>
				<div class="breakLine" style="height:5px;"><!-- --></div>
				<?=tep_div_button(2, '&nbsp; '.BUTTON_VIEW_PAST_SELL_ORDERS.' &nbsp;', tep_href_link(FILENAME_MY_ORDER_HISTORY, 'history_type=buyback', 'SSL'), 'style="float:right; padding-right:5px;"', 'green_button', false) ?>
				<div class="breakLine" style="height:5px;"><!-- --></div>
				<div style="line-height: 2px; background-image: url('<?=DIR_WS_IMAGES."gray_hr.jpg"?>'); background-position:center center;">&nbsp;</div>
			</td>
		</tr>
	</table>
<?
$account_history_content_string = ob_get_contents();
ob_end_clean(); 

echo $page_obj->get_html_simple_rc_box('',$account_history_content_string,13); 
?>
<div class="breakLine"><!-- --></div>
<?php 
	$customer_info_sql = "	SELECT c.customers_firstname, c.customers_lastname, cg.customers_groups_name 
							 FROM " . TABLE_CUSTOMERS . " AS c 
							 INNER JOIN " . TABLE_CUSTOMERS_GROUPS . " AS cg 
							 	ON c.customers_groups_id=cg.customers_groups_id 
							 WHERE c.customers_id = '" . tep_db_input($customer_id) . "'";
	$customer_info_res = tep_db_query($customer_info_sql);
	if ($customer_info_row = tep_db_fetch_array($customer_info_res)) {
		$customer_name = $customer_info_row['customers_firstname'] . ", " . $customer_info_row['customers_lastname'];
		$customer_status = $customer_info_row['customers_groups_name'];
	}
	
	ob_start();
?>
	<table width="100%" border="0" cellpadding="0" cellspacing="8">
		<col style="width: 120px;"></col>
	  	<col style="width: auto;"></col>

		<tr>
			<td style="padding:0px 3px;font-size:16px;font-weight:bold;"><?=TITLE_MY_PROFILE?></td>
		</tr>
		<tr>
			<td colspan="2" style="text-indent: 10px;">
				<div><img src="<?=DIR_WS_ICONS."icon_user_small.gif"?>" style="float:left; padding-left:8px;"></div>
				<div style="padding-top: 6px; display: inline-block;">
					<span style="display: block;"><b><?=$customer_name?></b></span>
					<table width="100%" border="0" cellpadding="0" cellspacing="0">
						<tr style="text-indent: 9px;">
							<td><span style="display: block;"><?=TEXT_MEMBER_ID?></span></td>
							<td><span style="display: block;"><?=SYMBOL_COLON?></span></td>
							<td><span style="display: block;"><b><?=$_SESSION['customer_id']?></b></span></td>
						</tr>
						<tr style="text-indent: 9px;">
							<td><span style="display: block;"><?=TEXT_MEMBER_STATUS?></span></td>
							<td><span style="display: block;"><?=SYMBOL_COLON?></span></td>
							<td><span style="display: block;"><b><?=$customer_status?></b></span></td>
						</tr>
					</table>
				</div>
			</td>
		</tr>
		<tr><td colspan="2" class="dottedLine" style="line-height:2px;">&nbsp;</td></tr>
		<tr style="text-indent: 10px;">
			<td nowrap><?=TEXT_CREDIT_BALANCE . SYMBOL_COLON?></td>
			<td><b><a href="<?=$printStoreCreditLink?>"><?=$printStoreCredit?></a></b></td>
		</tr>
		<tr><td colspan="2" class="dottedLine" style="line-height:2px;">&nbsp;</td></tr>
		<tr style="text-indent: 10px;">
			<td nowrap><?=TEXT_CURRENT_BALANCE . SYMBOL_COLON?></td>
			<td><b><?=$current_balance?></b></td>
		</tr>
		<tr><td colspan="2" class="dottedLine" style="line-height:2px;">&nbsp;</td></tr>
		<tr style="text-indent: 10px;">
			<td nowrap><?=TEXT_OP . SYMBOL_COLON?></td>
			<td><div class="wor-small"></div> <b><?=$printStorePoint?></b><?=(($printStorePoint > 0) ? '&nbsp;&nbsp;&nbsp;<span style="color:#004B91;">&rsaquo;&nbsp;&nbsp;</span><b><a href="'. tep_href_link(FILENAME_REDEEM_POINT) . '">' . TEXT_REDEEM . '</a></b>' : '')?></td>
		</tr>
		<!--
		<tr><td colspan="2" class="dottedLine" style="line-height:2px;">&nbsp;</td></tr>
		<tr style="text-indent: 10px;">
			<td nowrap><?=TEXT_LOYALTY_POINT . SYMBOL_COLON ?></td>
			<td></td>
		</tr>
		-->
		<tr><td colspan="2" class="dottedLine" style="line-height:2px;">&nbsp;</td></tr>
		<tr style="text-indent: 10px;">
			<td nowrap><?=TITLE_MY_PROFILE . SYMBOL_COLON ?></td>
			<td><span style="color:#004B91;">&rsaquo;</span>&nbsp;&nbsp;<b><a href="<?=tep_href_link(FILENAME_ACCOUNT_EDIT)?>"><?=TEXT_EDIT_NOW ?></a></b></td>
		</tr>
		<tr><td colspan="2" class="dottedLine" style="line-height:2px;">&nbsp;</td></tr>
		<tr style="text-indent: 10px;">
			<td nowrap><?=TEXT_PASSWORD . SYMBOL_COLON ?></td>
			<td><span style="color:#004B91;">&rsaquo;</span>&nbsp;&nbsp;<b><a href="<?=tep_href_link(FILENAME_ACCOUNT_EDIT . '#pwd')?>"><?=TEXT_CHANGE_NOW ?></a></b></td>
		</tr>
		<tr><td colspan="2" class="dottedLine" style="line-height:2px;">&nbsp;</td></tr>
		<tr style="text-indent: 10px;">
			<td nowrap><?=TEXT_MY_BILLING_ADDRESS . SYMBOL_COLON ?></td>
			<td><span style="color:#004B91;">&rsaquo;</span>&nbsp;&nbsp;<b><a href="<?=tep_href_link(FILENAME_ACCOUNT_EDIT . '#ba')?>"><?=TEXT_EDIT_NOW ?></a></b></td>
		</tr>
	</table>
<?
$my_profile_content_string = ob_get_contents();
ob_end_clean(); 

echo $page_obj->get_html_simple_rc_box('',$my_profile_content_string,13); 
?>
<div class="breakLine"><!-- --></div>