<h1><?=TITLE_MY_PROFILE ?></h1>
<div class="breakLine"><!-- --></div>
<script language="javascript" src="<?=DIR_WS_JAVASCRIPT?>customer_xmlhttp.js"></script>

<?php if ($messageStack->size('account_activate') > 0) { ?>
	<div class="loginColumnBorder" style="background-color: #FFFDDB">
		<div style="padding:10px;"><?=$messageStack->output('account_activate'); ?></div>
	</div>
	<div class="breakLine"><!-- --></div>
<?php } ?>

<script language="javascript" src="includes/general.js"></script>

<?php 
if ($verify == "email") { 
	ob_start();	
?>
	<table border="0" width="100%" cellspacing="10" cellpadding="0">
		<tr>
	    	<td>
	    		<div style="font-size:16px;font-weight:bold;"><?=HEADING_TITLE_1 ?></div>
	    		<div class="breakLine"><!-- --></div>
	    		<?php 
					if ($action == "resend") { 
						$activation_box = false;
				?>
	
			    		<div><?=sprintf(TEXT_REQUEST_VERIFY, tep_session_is_registered('customer_id') ? TEXT_REQUEST_EMAIL_LOGIN_1 : TEXT_REQUEST_EMAIL_LOGOUT_1, tep_session_is_registered('customer_id') ? TEXT_REQUEST_EMAIL_LOGIN_2 : TEXT_REQUEST_EMAIL_LOGOUT_2)?></div>
			    		<div class="breakLine" style="height:20px;"><!-- --></div>
			    		
						<?=tep_draw_form('resend_request_form', tep_href_link(FILENAME_ACCOUNT_ACTIVATE), 'post', 'onSubmit="return resend_request_checking();"') . tep_draw_hidden_field('action', 'resend')?>
							<table border="0" width="100%" cellspacing="2" cellpadding="2">
								<tr>
									<td><span class="inputLabelBold"><?=ENTRY_EMAIL_ADDRESS?></span>&nbsp;&nbsp;
									<?php 
										if (tep_session_is_registered('customer_id')) {
											echo tep_draw_hidden_field('resending_action', 'verify_email');
											echo tep_draw_hidden_field('resending', $customers_info_row['customers_email_address']) . '<span class="inputField">' . $customers_info_row['customers_email_address'] . '</span>';
										} else {
											echo tep_draw_input_field('resending', '', 'size="31"') . '&nbsp;' . (tep_not_null(ENTRY_EMAIL_ADDRESS_TEXT) ? '<span class="requiredInfo">' . ENTRY_EMAIL_ADDRESS_TEXT . '</span>': '');
										}
									?>
									</td>
				              	</tr>
							</table>
							
							<div class="breakLine" style="height:20px;"><!-- --></div>
							<div class="dottedLine"><!-- --></div>
							<div class="breakLine"><!-- --></div>
							<div style="height:40px; display: block;">
								<div style="float:left; width:200px; display: inline-block; margin: 5px 0px 0px 0px;">
									<div class="triangleRightIconBlue" style="margin: 3px 5px 0px 0px;"><!-- --></div>
									<div class="loginBoxLink"><?=TEXT_RESEND_VERIFICATION_CODE?></div>
								</div>
								<?=tep_div_button(1, '&nbsp; ' . BUTTON_SUBMIT . ' &nbsp; ', 'resend_request_form', 'style="float:right;"', 'green_button', false) ?>
							</div>
						</form>
						
						<script>
							function resend_request_checking() {
								var error = 'Errors have occurs during the process of your form. \n\n Please make the following corrections:\n\n';
								var message = false;
							
								if (document.resend_request.resending.value.length < 6 || trim_str(document.resend_request.resending.value) == "") {
									error += '*Your E-mail must contain a minimum of 6 characters.\n'
									message = true;
								}		
							
								if (message == true) {
									alert(error);
									return false;
								}
								
								return true;
							}
						</script>
	    		<?php
	    			}
	    			
	    			if ($activation_box == true) {
	    		?>
						<div><?=MANUAL_VERIFY_EMAIL_1 ?></div>
						<div class="breakLine" style="height:20px;"><!-- --></div>
						<!--div><?=sprintf(MANUAL_VERIFY_EMAIL_2, tep_session_is_registered('customer_id') ? MANUAL_VERIFY_EMAIL_LOGIN : MANUAL_VERIFY_EMAIL_LOGOUT) . MANUAL_VERIFY_EMAIL_LINK?></div-->
						<!--div class="breakLine" style="height:20px;">&nbsp;</div-->
						
						<?=tep_draw_form('manual_account_activate_form', tep_href_link(FILENAME_ACCOUNT_ACTIVATE), 'post', 'onSubmit="return manual_account_activate_checking();"') . tep_draw_hidden_field('action', 'manual_verified')?>
							<table border="0" width="100%" cellspacing="0" cellpadding="2">
								<tr>
							    	<td class="inputLabelNew" width="20%"><?=ENTRY_EMAIL_ADDRESS?></td>
							    	<td class="inputField">
								<?php 
								if (tep_session_is_registered('customer_id')) {
									echo $customers_info_row['customers_email_address'] . '</span>' . tep_draw_hidden_field('manual_email_address', $customers_info_row['customers_email_address']);
								} else {
									echo tep_draw_input_field('manual_email_address', '', 'size="31"') . '&nbsp;' . (tep_not_null(ENTRY_EMAIL_ADDRESS_TEXT) ? '<span class="requiredInfo">' . ENTRY_EMAIL_ADDRESS_TEXT . '</span>': '');
								}
								?>
									</td>
				    			</tr>
							    <tr>
							    	<td class="inputLabelNew" width="20%"><?=sprintf(ENTRY_VERIFICATION_CODE, $customers_info_row['customers_email_address'])?></td>
									<td class="inputField"><?=tep_draw_input_field('manual_activation_code', '', 'size="31"') . '&nbsp;' . (tep_not_null(ENTRY_EMAIL_ADDRESS_TEXT) ? '<span class="requiredInfo">' . ENTRY_EMAIL_ADDRESS_TEXT . '</span>': '')?></td>
							    </tr>
							</table>
							
							<div class="breakLine" style="height:20px;"><!-- --></div>
							<div class="dottedLine"><!-- --></div>
							<div class="breakLine"><!-- --></div>
							<div style="height:40px; display: block;">
								<div style="float:left; width:200px; display: inline-block; margin: 5px 0px 0px 0px;">
									<div class="triangleRightIconBlue" style="margin: 3px 5px 0px 0px;"><!-- --></div>
									<div class="loginBoxLink"><?=TEXT_RESEND_VERIFICATION_CODE?></div>
								</div>
								<?=tep_div_button(1, '&nbsp; '. BUTTON_SUBMIT .' &nbsp; ', 'manual_account_activate_form', 'style="float:right;"', 'green_button') ?>
							</div>
						</form>
						
						<script>
							function manual_account_activate_checking() {
								var error = 'Errors have occurs during the process of your form. \n\n Please make the following corrections:\n\n';
								var message = false;
								if (document.manual_account_activate.manual_email_address.value.length < 6 || trim_str(document.manual_account_activate.manual_email_address.value) == "") {
									error += '*Your E-mail must contain a minimum of 6 characters.\n'
									message = true;
								}		
								
								if (document.manual_account_activate.manual_activation_code.value.length != 12) {
									error += '*The serial number must contain 12 characters.\n';
									message = true;
								}
								
								if (message == true) {
									alert(error);
									return false;
								}
								return true;
							}
						</script>
				<?php 
					} else if (tep_not_null($message_display)) {
						$last_page = tep_href_link(FILENAME_DEFAULT);
		
						if (sizeof($navigation->path) > 0) {
							for ($path_cnt = (sizeof($navigation->path) - 1); $path_cnt >= 0; $path_cnt--) {
								if ($navigation->path[$path_cnt]['page'] != FILENAME_ACCOUNT_ACTIVATE && substr($navigation->path[$path_cnt]['page'], -12) != '_xmlhttp.php') {
									$last_page = tep_href_link($navigation->path[$path_cnt]['page'], tep_array_to_string($navigation->path[$path_cnt]['get'], array(tep_session_name())), $navigation->path[$path_cnt]['mode']);
									break;
								}
							}
						}
				?>
						<div><?=$message_display?></div>
						<div class="breakLine" style="height:20px;"><!-- --></div>
						<div class="dottedLine"><!-- --></div>
						<div class="breakLine"><!-- --></div>
						<div style="height:40px; display: block;">
							<div style="float:left; width:200px; display: inline-block; margin: 5px 0px 0px 0px;">
							<?php
								if (!tep_info_verified_check($customer_id, $customers_info_row['customers_email_address'], 'email')) {
							?>
								<div class="triangleRightIconBlue" style="margin: 3px 5px 0px 0px;"><!-- --></div>
								<div class="loginBoxLink"><?=TEXT_RESEND_VERIFICATION_CODE;?></div>
							<?php	}	?>
							</div>
							<?=tep_div_button(2, '&nbsp; '. IMAGE_BUTTON_CONTINUE .' &nbsp; ', $last_page, 'style="float:right;"', 'green_button') ?>
						</div>
				<?php }	?>
	    	</td>
		</tr>
	</table>
<?
	$verify_email_content_string = ob_get_contents();
	ob_end_clean(); 
	
	echo $page_obj->get_html_simple_rc_box('',$verify_email_content_string,13); 
} else if ($verify == "phone") {
	if (tep_not_null($order_id)) {  // verify order's telephone
		if ($order->customer['id'] == $customer_id) {
			if ($payment_confirm_complete_days > 0 && $order->info['orders_status_id'] == 7) {	// check order's telephone number if empty, print alert at the top of verifying page
				$order_telephone = tep_parse_telephone($order->customer['telephone'], $order->customer['order_country_code'], 'code');
				$complete_order_telephone = $order->customer['order_country_code'] . $order_telephone;
				
                if (tep_info_verified_check($order->customer['id'], $complete_order_telephone, 'telephone') != 1) {
					$customer_countries_info_array = tep_get_countries_info($order->customer['order_country_code'], 'countries_international_dialing_code');
					ob_start();
?>

		<table border="0" width="100%" cellspacing="10" cellpadding="0">
			<tr>
		    	<td>
		    		<div style="font-size:16px;font-weight:bold;"><?=HEADING_TITLE_2 ?></div>
		    		<div class="breakLine"><!-- --></div>
		    		
					<div id="verify_message"><?=($verify_mode == 3)? TEXT_TITLE_ENTER_PHONE_VERIFICATION_CODE : TEXT_TITLE_ENTER_PHONE_NUMBER;?></div>
					<div class="breakLine" style="height:20px"><!-- --></div>
					
					<?=tep_draw_form('phone_verify_form', tep_href_link(FILENAME_ACCOUNT_ACTIVATE), 'post', 'NONSSL')?>
		   				<div id="js_first_telephone_mode">
			   				<table border="0" cellpadding="0" cellspacing="0" width="100%">
			   					<tr>
			   						<td class="inputLabel" width="20%" align="left"><?=ENTRY_COUNTRY_CODE?></td>
			   						<td align="left" colspan="2" class="inputField">
			   							<div id="dialing_phone_country">
<?
					if (tep_not_null($order->customer['telephone_country'])) {
						echo (tep_country_maxmind_support($order->customer['order_country_code']) == 0 ? '&nbsp;&nbsp;' . TEXT_COUNTRY_NOT_SUPPORT : $order->customer['telephone_country'] . '&nbsp;(+' . $order->customer['order_country_code'] . ')');
					} else {
						$country_select_sql = "	SELECT c.countries_id, c.countries_international_dialing_code, c.countries_name 
												FROM " . TABLE_COUNTRIES . " as c"; 
						$country_result_sql = tep_db_query($country_select_sql);
						$countries_array = array();
						$countries_array[] = array(	'id'=>'','text'=>'['.PULL_DOWN_DEFAULT.']');
						while ($country_row = tep_db_fetch_array($country_result_sql)) {
							$countries_array[] = array(	'id' => $country_row['countries_id'],
														'text' => $country_row['countries_name'] . '&nbsp;+' . $country_row['countries_international_dialing_code']);
						}
						echo tep_draw_pull_down_menu('country', $countries_array, (int)$customer_countries_info_array['id'], ' class="ezInputField" onChange="refreshCountryCode(this, \'country_dial_code_div\');"');
						if (tep_not_null(ENTRY_COUNTRY_TEXT)) {
							echo '<span class="requiredInfo">' . ENTRY_COUNTRY_TEXT . '</span>';
						}
					}
?>
			   							</div>
			   						</td>
								</tr>
			   					<tr>
			   						<td class="inputLabel" width="20%" align="left"><?=ENTRY_TELEPHONE_NUMBER?></td>
			   						<td align="left" class="inputField">
										<!--div id="country_dial_code" style="display:inline; float:left;"><?=(tep_not_null($order->customer['order_country_code'])) ? ("+" . $order->customer['order_country_code']. "&nbsp;") : "+" ?></div-->
										<div id="dialing_phone_number" style="display:inline; float:left;">
<?
					if (tep_not_null($order->customer['telephone'])) {
						echo substr_replace($order->customer['telephone'], '****', -4);
					} else {
						echo tep_draw_input_field('telephone', substr_replace($order->customer['telephone'], '****', -4), 'id="telephone" size="43" onkeydown="if ((event.which && event.which == 13) || (event.keyCode && event.keyCode == 13)){document.button_edit_phone_number.onclick();}"', 'text', false) . '&nbsp;' . (tep_not_null(ENTRY_TELEPHONE_NUMBER_TEXT) ? '<span class="requiredInfo">' . ENTRY_TELEPHONE_NUMBER_TEXT . '</span>': '');
					}
?>
										</div>&nbsp;&nbsp;&nbsp;<a id="link_edit_phone_number" href="<?=tep_href_link(FILENAME_ACCOUNT_EDIT, 'oid=' . $order_id . '&trace=acc_history')?>"><?=BUTTON_EDIT?></a>
									</td>
                                </tr>
								<tr>
									<td><?=tep_draw_separator('pixel_trans.gif', '100%', '20')?></td>
								</tr>
								<tr>
									<td class="inputField" nowrap >
										<?=tep_draw_hidden_field('order_id', $order_id, 'id="order_id"') ?>
										<td class="inputLabel" width="60%" align="left">
											<div id="js_telephone_update_button_div">
<?
					if (tep_not_null($order->customer['telephone']) && tep_not_null($order->customer['telephone_country'])) {
						$phone_verification_obj->get_call_button($order_id);
					}
?>
											</div>
										</td>
									</td>
								</tr>
								<tr>
									<td colspan="3">
										<div id="div_verification_table"></div>
									</td>
								</tr>
		           			</table>
						</div>
					</form>
					
				</td>
			</tr>
			<tr>
				<td><?=tep_draw_separator('pixel_trans.gif', '100%', '20')?></td>
			</tr>
			<tr>
				<td><?=tep_image(DIR_WS_ICONS . 'note.gif').' &nbsp '.TEXT_SMS_NOTES?></td>
			</tr>
		</table>
<?
	$verify_order_phone_content_string = ob_get_contents();
	ob_end_clean(); 
	
	echo $page_obj->get_html_simple_rc_box('',$verify_order_phone_content_string,13); 
?>		
		<script language="javascript">
			function verifyTelephone(code) {
				var server_action = 'myaccount_confirm_telephone';
				var page = '<?=CONTENT_ACCOUNT?>';
				var ref_url = "customer_xmlhttp.php?action="+server_action+"&code="+code+"&order_id=<?=$order_id?>"+"&page="+page;
				
				jQuery.get(ref_url, function(xml){
					jQuery(xml).find('response').each(function(){
						alert(jQuery("result", this).text());
						if (jQuery("goto_url", this).text()!='') {
							window.location = jQuery("goto_url", this).text();
						}
					})
				});
	
			}
            
			function confirm_code(pass_order_id, verify_type){
				pass_order_id = pass_order_id || '';
				var server_action = "myaccount_verify_phone";
				var call_language = jQuery("#call_language").val() || '';
				var submit_url = "customer_xmlhttp.php?action=" + server_action + "&verify_type=" + verify_type +"&call_language=" + call_language;
				if (pass_order_id!='') {
					submit_url += "&order_id=" + pass_order_id;
				}
				jQuery("#button_text_me_now a").attr("href","javascript:void(0);");
			    jQuery.get(submit_url, function(xml){
                    if (jQuery(xml).find("error").length > 0){
                        alert(jQuery(xml).find('error').text());
                    }else{
                       jQuery(xml).find('response').each(function(){
                            jQuery("#button_text_me_now").hide(); 
                            document.getElementById("div_verification_table").innerHTML = jQuery("verify_code_table", this).text();
                        });
                        jQuery("#button_text_me_now").attr("href","javascript:confirm_code('"+pass_order_id+"', '"+verify_type+"');");
                    }
				 });
			}
			
			function check_phone_verify_form(location, telephone) {
				var error = false;
				var message = '';
							               								
				if (location == '') {
					error = true;
					message += '<? echo ENTRY_LOCATION_ERROR?>';
				}
									               								
				if (telephone.length < <?echo MAX_CODE_VERIFICATION;?>) {
					error = true;
					message += '<? echo ENTRY_TELEPHONE_NUMBER_ERROR;?>';
				}
									               								
				if (error == true) {
					alert(message);
					return false;
				} else {
					return true;
				}
			}
		</script>
<?  			}
			}
		}
	} else { // verify profile telephone
		$customer_complete_phone_info_array = tep_format_telephone($customer_id);
		$complete_telephone_number = sizeof($customer_complete_phone_info_array) > 0 ? $customer_complete_phone_info_array['country_international_dialing_code'] . $customer_complete_phone_info_array['telephone_number'] : '';
        
        ob_start();
        
		if (tep_info_verified_check($customer_id, $complete_telephone_number, 'telephone') != 1) {
?>	

		<table border="0" width="100%" cellspacing="10" cellpadding="0">
			<tr>
		    	<td>
		    		<div style="font-size:16px;font-weight:bold;"><?=HEADING_TITLE_2 ?></div>
		    		<div class="breakLine"><!-- --></div>
		    		
					<div id="verify_message"><?=($verify_mode == 3)? TEXT_TITLE_ENTER_PHONE_VERIFICATION_CODE : TEXT_TITLE_ENTER_PHONE_NUMBER;?></div>
					<div class="breakLine" style="height:20px"><!-- --></div>
					
					<?=tep_draw_form('phone_verify_form', tep_href_link(FILENAME_ACCOUNT_ACTIVATE), 'post', 'onsubmit="return false;"')?>
		   				<div id="js_first_telephone_mode">
		   				<table border="0" cellpadding="0" cellspacing="0" width="100%">
		   					<tr>
		   						<td class="inputLabel" width="20%" align="left"><?=ENTRY_LOCATION?></td>
		   						<td class="inputField" align="left" colspan="2"><?=(tep_country_maxmind_support($customer_complete_phone_info_array['country_international_dialing_code']) == 0 ? '&nbsp;&nbsp;' . TEXT_COUNTRY_NOT_SUPPORT : $customer_complete_phone_info_array['country_name'])?></td>
							</tr>
		   					<tr>
		   						<td class="inputLabel" width="20%" align="left"><?=ENTRY_TELEPHONE_NUMBER?></td>
		   						<td align="left" class="inputField">
									<div id="country_dial_code_div">
										<?="+" . $customer_complete_phone_info_array['country_international_dialing_code']. "&nbsp;" . substr_replace($customer_complete_phone_info_array['telephone_number'], '****', -4); ?>
									</div>
								</td>
							</tr>
                            <tr>
                                <td></td>
                                <td align="left" class="inputField"><span class="hds4"><?=TEXT_CHANGE_PHONE_NUMBER?></span></td>
                            </tr>
							<tr>
								<td><?=tep_draw_separator('pixel_trans.gif', '100%', '20')?></td>
							</tr>
							<tr>
								<td class="inputField" nowrap >
									<td class="inputLabel" width="60%" align="left">
										<div id="js_telephone_update_button_div">
<?php 
			if (tep_not_null($customer_id)) {
				$phone_verification_obj->get_call_button();
			}
?>
										</div>
									</td>
								</td>
							</tr>
							<tr>
								<td colspan="3">
									<div id="div_verification_table"></div>
								</td>
							</tr>
	           			</table>
						</div>
					</form>
					
				</td>
			</tr>
			<tr>
				<td><?=tep_draw_separator('pixel_trans.gif', '100%', '20')?></td>
			</tr>
			<tr>
				<td><?=tep_image(DIR_WS_ICONS . 'note.gif') . ' &nbsp ' . TEXT_SMS_NOTES?></td>
			</tr>
		</table>		
		<script language="javascript">
			function verifyTelephone(code) {
				var server_action = 'myaccount_confirm_telephone';
				var page = '<?=CONTENT_ACCOUNT?>';
				var ref_url = "customer_xmlhttp.php?action="+server_action+"&code="+code+"&page="+page;
				
				jQuery.get(ref_url, function(xml){
					jQuery(xml).find('response').each(function(){
						alert(jQuery("result", this).text());
						if (jQuery("goto_url", this).text()!='') {
							window.location = jQuery("goto_url", this).text();
						}
					})
				});
	
			}

			function confirm_code(verify_type) {
				server_action = "myaccount_verify_phone";
				var call_language = jQuery("#call_language").val() || '';
				var submit_url = "customer_xmlhttp.php?action="+server_action+"&call_language="+call_language;
                
				jQuery("#button_text_me_now a").attr("href","javascript:void(0);");
				jQuery.get(submit_url, function(xml){
                    if (jQuery(xml).find("error").length > 0){
                        alert(jQuery(xml).find('error').text());
                    }else{
                        jQuery(xml).find('response').each(function(){
                            jQuery("#button_text_me_now").hide(); 
                            document.getElementById("div_verification_table").innerHTML = jQuery("verify_code_table", this).text();
                        });
                        jQuery("#button_text_me_now").attr("href","javascript:confirm_code('"+verify_type+"');");
                    }
				 });
			}
			
			function check_phone_verify_form(location, telephone) {
				var error = false;
				var message = '';
							               								
				if (location == '') {
					error = true;
					message += '<? echo ENTRY_LOCATION_ERROR?>';
				}
									               								
				if (telephone.length < <?echo MAX_CODE_VERIFICATION;?>) {
					error = true;
					message += '<? echo ENTRY_TELEPHONE_NUMBER_ERROR;?>';
				}
									               								
				if (error == true) {
					alert(message);
					return false;
				} else {
					return true;
				}
			}
		</script>
<?php  
		} else {
?>
        <table border="0" width="100%" cellspacing="10" cellpadding="0">
			<tr>
		    	<td>
		    		<div style="font-size:16px;font-weight:bold;"><?=CODE_MATCH_MESSAGE?></div>
                </td>
            </tr>
        </table>
<?
        }
        
        $verify_profile_phone_content_string = ob_get_contents();
        ob_end_clean(); 

        echo $page_obj->get_html_simple_rc_box('',$verify_profile_phone_content_string,13); 
	}
}
?>
