<table border="0" width="100%" cellspacing="0" cellpadding="0">
	<tr>
		<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
	<tr>
		<td class="pageHeadingTitle" valign="middle" align="center"><?=HEADING_TITLE?></td>
	</tr>
	<tr>
		<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
	<tr>
		<td>
			<div class="row_separator"></div>
		</td>
	</tr>
	<tr>
		<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
<?
if ($messageStack->size('account_buyback') > 0) {
?>
	<tr>
    	<td><?=$messageStack->output('account_buyback')?></td>
	</tr>
	<tr>
    	<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
<?
}

if ($accept_account_buyback) {
?>
	<tr>
		<td>
<?
	switch($do) {
		case "3"
?>
			<table border="0" width="100%" cellspacing="0" cellpadding="0">
				<tr>
					<td colspan="2"><br><div class="latestNewsTitle"></div><br></td>
				</tr>
				<tr>
					<td class="spacingInstruction" align="center" colspan="2"><?=TEXT_BUYBACK_ACCOUNT_THANK_YOU_MESSAGE?></td>
				</tr>
				<tr>
					<td colspan="2"><br><div class="latestNewsTitle"></div><br></td>
				</tr>
				<tr>
					<td align="center" colspan="2"><?=TEXT_BUYBACK_ACCOUNT_CONFIRM_FOOTER?></td>
				</tr>
			</table>
<?
			break;
		case "2":
			$has_alt_char_info = false;
			
			$server_info_select_sql = "	SELECT p.products_cat_path 
		            					FROM " . TABLE_PRODUCTS_TO_CATEGORIES . " AS p2c
		            					INNER JOIN " . TABLE_PRODUCTS . " AS p
		            						ON (p2c.products_id = p.products_id)
		            					WHERE p2c.categories_id = '".tep_db_input($ss_account_buyback['main_char_server'])."' 
		            						AND p2c.products_is_link=0
		            						AND p.custom_products_type_id=0
		            						AND p.products_bundle=''
											AND p.products_bundle_dynamic=''";
		    $server_info_result_sql = tep_db_query($server_info_select_sql);
			$server_info_row = tep_db_fetch_array($server_info_result_sql);
			$cat_display_name = tep_display_category_path($server_info_row['products_cat_path'], $ss_account_buyback['main_char_server'], 'catalog', false);
			
			$back_button_html = tep_draw_form('account_buyback_input_form', tep_href_link(FILENAME_ACCOUNT_BUYBACK, 'do=1&'.$cPath_new, 'SSL'), 'post', '') . tep_image_submit(THEMA.'button_back.gif', IMAGE_BUTTON_BACK) . '</form>';
			$confirm_button_html = tep_draw_form('account_buyback_submit_form', tep_href_link(FILENAME_ACCOUNT_BUYBACK, 'do=3&'.$cPath_new, 'SSL'), 'post', '') . tep_image_submit(BUTTON_CONFIRM, BUTTON_CONFIRM) . '</form>';
?>
			<table border="0" width="100%" cellspacing="0" cellpadding="0">
				<tr>
			    	<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
				</tr>
				<tr>
			    	<td class="infoBoxHeading"><?=BUYBACK_SECTION_MAIN_CHAR?></td>
				</tr>
				<tr>
			    	<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
				</tr>
				<tr>
					<td>
    					<table border="0" width="100%" cellspacing="1" cellpadding="2" class="infoBox">
          					<tr class="infoBoxContents">
			          			<td>
            						<table border="0" cellspacing="2" cellpadding="2" width="100%">
            							<tr>
			                				<td class="inputLabel" width="30%"><?=ENTRY_BUYBACK_ACCOUNT_SERVER?></td>
			                				<td class="inputField"><?=$accepted_games_array[$current_category_id]?></td>
			              				</tr>
			              				<tr>
			                				<td class="inputLabel" width="30%"><?=ENTRY_BUYBACK_ACCOUNT_MAIN_CHAR_SERVER?></td>
			                				<td class="inputField"><?=$cat_display_name?></td>
			              				</tr>
			              				<tr>
			                				<td class="inputLabel" width="30%"><?=ENTRY_BUYBACK_MAIN_CHAR_LVL?></td>
			                				<td class="inputField"><?=$ss_account_buyback['main_char_lvl']?></td>
			              				</tr>
			              				<tr>
			                				<td class="inputLabel" width="30%"><?=ENTRY_BUYBACK_MAIN_CHAR_RACE?></td>
			                				<td class="inputField"><?=$ss_account_buyback['main_char_race']?></td>
			              				</tr>
			              				<tr>
			                				<td class="inputLabel" width="30%"><?=ENTRY_BUYBACK_MAIN_CHAR_CLASS?></td>
			                				<td class="inputField"><?=$ss_account_buyback['main_char_class']?></td>
			              				</tr>
			              				<tr>
						        			<td colspan="2"><br><div class="latestNewsTitle"></div><br></td>
						    			</tr>
						    			<tr>
			                				<td class="inputLabel" width="30%"><?=ENTRY_BUYBACK_MAIN_CHAR_EQUIPMENT?></td>
			                				<td class="inputField"><?=$ss_account_buyback['main_char_equipment']?></td>
			              				</tr>
						    			<tr>
			                				<td class="inputLabel" width="30%"><?=ENTRY_BUYBACK_MAIN_CHAR_MOUNT?></td>
			                				<td class="inputField"><?=$ss_account_buyback['main_char_mount']?></td>
			              				</tr>
			              				<tr>
			                				<td class="inputLabel" width="30%"><?=ENTRY_BUYBACK_MAIN_CHAR_GOLD?></td>
			                				<td class="inputField"><?=$ss_account_buyback['main_char_gold']?></td>
			              				</tr>
			              				<tr>
						        			<td colspan="2"><br><div class="latestNewsTitle"></div><br></td>
						    			</tr>
						    			<tr>
			                				<td class="inputLabel" width="30%"><?=ENTRY_BUYBACK_ACCOUNT_STATUS?></td>
			                				<td class="inputField"><?=($ss_account_buyback['acc_status'] == '1' ? OPTION_ACTIVE : OPTION_INACTIVE)?></td>
			              				</tr>
			              				<tr>
			                				<td class="inputLabel" width="30%"><?=ENTRY_BUYBACK_ACCOUNT_CDKEY?></td>
  											<td class="inputField"><?=($ss_account_buyback['acc_cdkey'] == '1' ? OPTION_AVAILABLE : OPTION_NOT_AVAILABLE)?></td>
			              				</tr>
			              				<tr>
			                				<td class="inputLabel" width="30%"><?=ENTRY_BUYBACK_ACCOUNT_SECRET?></td>
			                				<td class="inputField"><?=($ss_account_buyback['acc_secret'] == '1' ? OPTION_REMEMBER : OPTION_FORGOTTEN)?></td>
			              				</tr>
			            			</table>
			            		</td>
          					</tr>
          				</table>
          			</td>
          		</tr>
          		<tr>
			    	<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
				</tr>
				<tr>
			    	<td class="infoBoxHeading"><?=BUYBACK_SECTION_ALTERNATE_CHAR?></td>
				</tr>
				<tr>
			    	<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
				</tr>
				<tr>
					<td>
    					<table border="0" width="100%" cellspacing="1" cellpadding="2" class="infoBox">
          					<tr class="infoBoxContents">
			          			<td>
            						<table border="0" cellspacing="2" cellpadding="2" width="100%">
<? 			for ($alt_char_cnt=0; $alt_char_cnt < 3; $alt_char_cnt++) {
				if (tep_not_null($ss_account_buyback['alt_char']['server'][$alt_char_cnt])) {
					$alt_realm_info_select_sql = "	SELECT p.products_cat_path 
					            					FROM " . TABLE_PRODUCTS_TO_CATEGORIES . " AS p2c
					            					INNER JOIN " . TABLE_PRODUCTS . " AS p
					            						ON (p2c.products_id = p.products_id)
					            					WHERE p2c.categories_id = '".tep_db_input($ss_account_buyback['alt_char']['server'][$alt_char_cnt])."' 
					            						AND p2c.products_is_link=0
					            						AND p.custom_products_type_id=0
					            						AND p.products_bundle=''
														AND p.products_bundle_dynamic=''";
				    $alt_realm_info_result_sql = tep_db_query($alt_realm_info_select_sql);
					$alt_realm_info_row = tep_db_fetch_array($alt_realm_info_result_sql);
					
					$alt_realm_display_name = tep_display_category_path($alt_realm_info_row['products_cat_path'], $ss_account_buyback['alt_char']['server'][$alt_char_cnt], 'catalog', false);
					
					$has_alt_char_info = true;
				} else {
					continue;
				}
				if ($alt_char_cnt > 0) {
?>
			     						<tr>
						        			<td colspan="2"><br><div class="latestNewsTitle"></div><br></td>
						    			</tr>
<?				} ?>
										<tr>
			                				<td class="inputLabel" width="30%"><?=ENTRY_BUYBACK_ACCOUNT_ALT_CHAR_SERVER?></td>
			                				<td class="inputField"><?=$alt_realm_display_name?></td>
			              				</tr>
										<tr>
			                				<td class="inputLabel" width="30%"><?=ENTRY_BUYBACK_ACCOUNT_ALT_CHAR_LVL?></td>
			                				<td class="inputField"><?=$ss_account_buyback['alt_char']['level'][$alt_char_cnt]?></td>
			              				</tr>
			              				<tr>
			                				<td class="inputLabel" width="30%"><?=ENTRY_BUYBACK_ALT_CHAR_CLASS?></td>
			                				<td class="inputField"><?=$ss_account_buyback['alt_char']['class'][$alt_char_cnt]?></td>
			              				</tr>
<?			} 
			
			if (!$has_alt_char_info) {
				echo '					<tr>
			                				<td class="inputField" colspan="2">'.TEXT_BUYBACK_ACCOUNT_NO_ALT_CHARACTER.'</td>
			              				</tr>';
			}
?>
			              			</table>
			              		</td>
			              	</tr>
			            </table>
			        </td>
			    </tr>
			    <tr>
					<td class="spacingInstruction"><?=sprintf(TEXT_BUYBACK_CANNOT_CONFIRM_MESSAGE, $back_button_html, $confirm_button_html)?></td>
				</tr>
          	</table>
<?
			
			break;
		case "1":
			$categories_array = array (array('id' => '', 'text' => PULL_DOWN_DEFAULT) );
			
			$subcat_array = array($current_category_id);
			tep_get_subcategories($subcat_array, $current_category_id);
			
		    $categories_select_sql = "	SELECT p2c.categories_id, p.products_id, p.products_cat_path 
		            					FROM " . TABLE_PRODUCTS_TO_CATEGORIES . " AS p2c
		            					INNER JOIN " . TABLE_PRODUCTS . " AS p
		            						ON (p2c.products_id = p.products_id)
		            					WHERE p2c.categories_id IN ('" . implode("', '", $subcat_array) . "')
		            						AND p2c.products_is_link=0
		            						AND p.custom_products_type_id=0
		            						AND p.products_bundle=''
											AND p.products_bundle_dynamic=''
		            					ORDER BY p.products_cat_path";
		    $categories_result_sql = tep_db_query($categories_select_sql);
			
			while ($categories_row = tep_db_fetch_array($categories_result_sql)) {
		    	$cat_display_name = tep_display_category_path($categories_row['products_cat_path'], $categories_row['categories_id'], 'catalog', false);
		    	
		    	$categories_array[] = array('id' => $categories_row['categories_id'],
		                                  	'text' => $cat_display_name);
		    }
		    
			echo tep_draw_form('account_buyback_form', tep_href_link(FILENAME_ACCOUNT_BUYBACK, 'do=2&'.$cPath_new, 'SSL'), 'post', 'onSubmit=""');
?>
			<table border="0" width="100%" cellspacing="1" cellpadding="2" class="inputBox">
          		<tr class="inputBoxContents">
            		<td>
        				<table border="0" width="100%" cellspacing="0" cellpadding="2">
          					<tr>
            					<td class="inputBoxHeading">&nbsp;</td>
           						<td class="requiredInfo" align="right"><?=FORM_REQUIRED_INFORMATION?></td>
          					</tr>
        				</table>
        			</td>
				</tr>
				<tr>
    				<td>
    					<table border="0" width="100%" cellspacing="1" cellpadding="2" class="inputNestedBox">
			          		<tr class="inputNestedBoxContents">
			          			<td>
            						<table border="0" cellspacing="2" cellpadding="2" width="100%">
            							<tr>
			                				<td class="inputLabel" width="30%"><?=ENTRY_BUYBACK_CUSTOMER_NAME?></td>
			                				<td class="inputField"><? echo tep_draw_input_field('name', $ss_account_buyback['name'], 'size="49"') . '&nbsp;' . (tep_not_null(ENTRY_FIRST_NAME_TEXT) ? '<span class="requiredInfo">' . ENTRY_FIRST_NAME_TEXT . '</span>': ''); ?></td>
			              				</tr>
			              				<tr>
			            					<td class="inputLabel" width="30%"><?=ENTRY_EMAIL_ADDRESS?></td>
			            					<td class="inputField"><? echo tep_draw_input_field('email_address', $ss_account_buyback['email_address'], 'size="49"') . '&nbsp;' . (tep_not_null(ENTRY_EMAIL_ADDRESS_TEXT) ? '<span class="requiredInfo">' . ENTRY_EMAIL_ADDRESS_TEXT . '</span>': ''); ?></td>
			            				</tr>
			            			</table>
			            		</td>
			            	</tr>
			            </table>
			        </tr>
			    </tr>
				<tr>
    				<td>
    					<table border="0" width="100%" cellspacing="1" cellpadding="2" class="inputNestedBox">
			          		<tr class="inputNestedBoxContents">
			          			<td>
            						<table border="0" cellspacing="2" cellpadding="2" width="100%">
            							<tr>
											<td class="sectionTitle" colspan="2"><b><?=BUYBACK_SECTION_MAIN_CHAR?></b></td>
										</tr>
										<tr>
											<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10'); ?></td>
										</tr>
            							<tr>
			                				<td class="inputLabel" width="30%"><?=ENTRY_BUYBACK_ACCOUNT_MAIN_CHAR_SERVER?></td>
			                				<td class="inputField"><?=tep_draw_pull_down_menu('main_char_server', $categories_array, $ss_account_buyback['main_char_server'], '') . '&nbsp;' . (tep_not_null(ENTRY_FIRST_NAME_TEXT) ? '<span class="requiredInfo">' . ENTRY_FIRST_NAME_TEXT . '</span>': '')?></td>
			              				</tr>
			              				<tr>
						        			<td colspan="2"><br><div class="latestNewsTitle"></div><br></td>
						    			</tr>
						    			<tr>
			                				<td class="inputLabel" width="30%"><?=ENTRY_BUYBACK_MAIN_CHAR_LVL?></td>
			                				<td class="inputField"><?=tep_draw_pull_down_menu('main_char_lvl', $main_char_lvl_array, $ss_account_buyback['main_char_lvl'], '') . '&nbsp;' . (tep_not_null(ENTRY_FIRST_NAME_TEXT) ? '<span class="requiredInfo">' . ENTRY_FIRST_NAME_TEXT . '</span>': '')?></td>
			              				</tr>
			              				<tr>
			                				<td class="inputLabel" width="30%"><?=ENTRY_BUYBACK_MAIN_CHAR_RACE?></td>
			                				<td class="inputField"><?=tep_draw_pull_down_menu('main_char_race', $char_race_array, $ss_account_buyback['main_char_race'], '') . '&nbsp;' . (tep_not_null(ENTRY_FIRST_NAME_TEXT) ? '<span class="requiredInfo">' . ENTRY_FIRST_NAME_TEXT . '</span>': '')?></td>
			              				</tr>
			              				<tr>
			                				<td class="inputLabel" width="30%" valign="top"><?=ENTRY_BUYBACK_MAIN_CHAR_CLASS?></td>
			                				<td class="inputField">
			                					<div style="float: left; height: 35px;"><?=tep_draw_pull_down_menu('main_char_class', $char_class_array, $ss_account_buyback['main_char_class'], '') . '&nbsp;' . (tep_not_null(ENTRY_FIRST_NAME_TEXT) ? '<span class="requiredInfo">' . ENTRY_FIRST_NAME_TEXT . '</span>': '')?></div>
			                					<div><?=TEXT_CHAR_CLASS_NOT_FOUND?></div>
			                				</td>
			              				</tr>
			              				<tr>
			                				<td class="inputLabel" width="30%"><?=ENTRY_BUYBACK_MAIN_CHAR_GENDER?></td>
			                				<td class="inputField"><?=tep_draw_pull_down_menu('main_char_gender', $char_gender_array, $ss_account_buyback['main_char_gender'], '') . '&nbsp;' . (tep_not_null(ENTRY_FIRST_NAME_TEXT) ? '<span class="requiredInfo">' . ENTRY_FIRST_NAME_TEXT . '</span>': '')?></td>
			              				</tr>
			              				<tr>
						        			<td colspan="2"><br><div class="latestNewsTitle"></div><br></td>
						    			</tr>
			              				<tr>
			                				<td class="inputLabel" width="30%"><?=ENTRY_BUYBACK_MAIN_CHAR_EQUIPMENT?></td>
			                				<td class="inputField"><?=tep_draw_pull_down_menu('main_char_equipment', $equipment_array, $ss_account_buyback['main_char_equipment'], '') . '&nbsp;' . (tep_not_null(ENTRY_FIRST_NAME_TEXT) ? '<span class="requiredInfo">' . ENTRY_FIRST_NAME_TEXT . '</span>': '')?></td>
			              				</tr>
						    			<tr>
			                				<td class="inputLabel" width="30%"><?=ENTRY_BUYBACK_MAIN_CHAR_MOUNT?></td>
			                				<td class="inputField"><?=tep_draw_pull_down_menu('main_char_mount', $mount_array, $ss_account_buyback['main_char_mount'], '') . '&nbsp;' . (tep_not_null(ENTRY_FIRST_NAME_TEXT) ? '<span class="requiredInfo">' . ENTRY_FIRST_NAME_TEXT . '</span>': '')?></td>
			              				</tr>
			              				<tr>
			                				<td class="inputLabel" width="30%"><?=ENTRY_BUYBACK_MAIN_CHAR_GOLD?></td>
			                				<td class="inputField"><?=tep_draw_input_field('main_char_gold', $ss_account_buyback['main_char_gold'], 'size="49"') . '&nbsp;' . (tep_not_null(ENTRY_GAME_CURRENCY_TEXT) ? '<span class="requiredInfo">' . ENTRY_GAME_CURRENCY_TEXT . '</span>': '')?></td>
			              				</tr>
			              				<tr>
						        			<td colspan="2"><br><div class="latestNewsTitle"></div><br></td>
						    			</tr>
						    			<tr>
			                				<td class="inputLabel" width="30%"><?=ENTRY_BUYBACK_ACCOUNT_STATUS?></td>
			                				<td>
			                					<table border="0" cellspacing="0" cellpadding="0" width="100%">
            										<tr>
            											<td width="2%" class="inputField"><?=tep_draw_radio_field('acc_status', '1', $ss_account_buyback['acc_status'] == '1' ? true : false)?></td>
            											<td width="25%" class="inputField">&nbsp;<?=OPTION_ACTIVE?></td>
            											<td width="2%" class="inputField"><?=tep_draw_radio_field('acc_status', '-1', $ss_account_buyback['acc_status'] == '-1' ? true : false)?></td>
            											<td class="inputField">&nbsp;<?=OPTION_INACTIVE . (tep_not_null(ENTRY_FIRST_NAME_TEXT) ? '<span class="requiredInfo">' . ENTRY_FIRST_NAME_TEXT . '</span>': '')?></td>
            										</tr>
            									</table>
			                				</td>
			              				</tr>
			              				<tr>
			                				<td class="inputLabel" width="30%"><?=ENTRY_BUYBACK_ACCOUNT_CDKEY?></td>
			                				<td>
			                					<table border="0" cellspacing="0" cellpadding="0" width="100%">
            										<tr>
            											<td width="2%" class="inputField"><?=tep_draw_radio_field('acc_cdkey', '1', $ss_account_buyback['acc_cdkey'] == '1' ? true : false)?></td>
            											<td width="25%" class="inputField">&nbsp;<?=OPTION_AVAILABLE?></td>
            											<td width="2%" class="inputField"><?=tep_draw_radio_field('acc_cdkey', '-1', $ss_account_buyback['acc_cdkey'] == '-1' ? true : false)?></td>
            											<td class="inputField">&nbsp;<?=OPTION_NOT_AVAILABLE . (tep_not_null(ENTRY_FIRST_NAME_TEXT) ? '<span class="requiredInfo">' . ENTRY_FIRST_NAME_TEXT . '</span>': '')?></td>
            										</tr>
            									</table>
			                				</td>
			              				</tr>
			              				<tr>
			                				<td class="inputLabel" width="30%"><?=ENTRY_BUYBACK_ACCOUNT_SECRET?></td>
			                				<td>
			                					<table border="0" cellspacing="0" cellpadding="0" width="100%">
            										<tr>
            											<td width="2%" class="inputField"><?=tep_draw_radio_field('acc_secret', '1', $ss_account_buyback['acc_secret'] == '1' ? true : false)?></td>
            											<td width="25%" class="inputField">&nbsp;<?=OPTION_REMEMBER?></td>
            											<td width="2%" class="inputField"><?=tep_draw_radio_field('acc_secret', '-1', $ss_account_buyback['acc_secret'] == '-1' ? true : false)?></td>
            											<td class="inputField">&nbsp;<?=OPTION_FORGOTTEN . (tep_not_null(ENTRY_FIRST_NAME_TEXT) ? '<span class="requiredInfo">' . ENTRY_FIRST_NAME_TEXT . '</span>': '')?></td>
            										</tr>
            									</table>
			                				</td>
			              				</tr>
			              				<tr>
						        			<td colspan="2"><br><div class="latestNewsTitle"></div><br></td>
						    			</tr>
						    			<tr>
			                				<td class="inputLabel" width="30%"><?=ENTRY_BUYBACK_ASKING_PRICE?></td>
			                				<td class="inputField">US $&nbsp;<?=tep_draw_input_field('asking_price', $ss_account_buyback['asking_price'], 'size="49"') . '&nbsp;' . (tep_not_null(ENTRY_FIRST_NAME_TEXT) ? '<span class="requiredInfo">' . ENTRY_FIRST_NAME_TEXT . '</span>': '')?></td>
			              				</tr>
			              				<tr>
						        			<td colspan="2"><br><div class="latestNewsTitle"></div><br></td>
						    			</tr>
						    			<tr>
			                				<td class="inputLabel" width="30%" valign="top"><?=ENTRY_BUYBACK_OTHER_INFO?></td>
			                				<td class="inputField"><?=tep_draw_textarea_field('acc_buyback_comments', 'virtual', 70, 5, $ss_account_buyback['acc_buyback_comments'], '')?></td>
			              				</tr>
			    					</table>
			            		</td>
			            	</tr>
            			</table>
            		</td>
            	</tr>
            	<tr>
					<td><?=tep_draw_separator('pixel_trans.gif', '100%', '20')?></td>
				</tr>
				<tr>
    				<td>
    					<table border="0" width="100%" cellspacing="1" cellpadding="2" class="inputNestedBox">
			          		<tr class="inputNestedBoxContents">
			          			<td>
            						<table border="0" cellspacing="2" cellpadding="2" width="100%">
            							<tr>
											<td class="sectionTitle" colspan="2"><b><?=BUYBACK_SECTION_ALTERNATE_CHAR?></b></td>
										</tr>
										<tr>
											<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
										</tr>
<? 			for ($alt_char_cnt=0; $alt_char_cnt < 3; $alt_char_cnt++) { ?>
										<tr>
			                				<td class="inputLabel" width="30%"><?=ENTRY_BUYBACK_ACCOUNT_ALT_CHAR_SERVER?></td>
			                				<td class="inputField"><?=tep_draw_pull_down_menu('alt_char[server][]', $categories_array, $ss_account_buyback['alt_char']['server'][$alt_char_cnt], '')?></td>
			              				</tr>
										<tr>
			                				<td class="inputLabel" width="30%"><?=ENTRY_BUYBACK_ACCOUNT_ALT_CHAR_LVL?></td>
			                				<td class="inputField"><?=tep_draw_pull_down_menu('alt_char[level][]', $others_char_lvl_array, $ss_account_buyback['alt_char']['level'][$alt_char_cnt], '')?></td>
			              				</tr>
			              				<tr>
			                				<td class="inputLabel" width="30%"><?=ENTRY_BUYBACK_ALT_CHAR_CLASS?></td>
			                				<td class="inputField"><?=tep_draw_pull_down_menu('alt_char[class][]', $char_class_array, $ss_account_buyback['alt_char']['class'][$alt_char_cnt], '')?></td>
			              				</tr>
<?			   	if ($alt_char_cnt < 2) { ?>
			              				<tr>
						        			<td colspan="2"><br><div class="latestNewsTitle"></div><br></td>
						    			</tr>
<?				}
			} ?>
									</table>
								</td>
							</tr>
							<tr>
						    	<td>
						    		<table border="0" width="100%" cellspacing="1" cellpadding="2" class="buttonBox2">
						          		<tr class="buttonBoxContents2">
						            		<td>
						            			<table border="0" width="100%" cellspacing="0" cellpadding="2">
						              				<tr>
						                				<td width="10"><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
						                				<td align="right"><? echo tep_image_submit(BUTTON_SUBMIT, BUTTON_SUBMIT); ?></td>
						                				<td width="10"><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
						              				</tr>
						            			</table>
						            		</td>
						          		</tr>
						        	</table>
						        </td>
							</tr>
						</table>
					</td>
				</tr>
            </table>
        	</form>
<?
			break;
		default:
?>
			<table border="0" width="100%" cellspacing="0" cellpadding="2">
				<tr>
  					<td class="productBoxHeading" align="center" colspan="2"><?=TEXT_TITLE_CHOOSE_GAME?></td>
  				</td>
  				<tr>
  					<td width="50%" align="center"><?='<a href="'.tep_href_link(FILENAME_ACCOUNT_BUYBACK, "do=1&cPath=16_2299_194").'" class="subCategoryNavigation"><img src="http://image.offgamers.com/entrance/wow-us.jpg" border="0" height="91" width="128"><br>World of Warcraft US</a>'?></td>
  					<td width="50%" align="center"><?='<a href="'.tep_href_link(FILENAME_ACCOUNT_BUYBACK, "do=1&cPath=16_2522_195").'" class="subCategoryNavigation"><img src="http://image.offgamers.com/entrance/wow-eu.jpg" border="0" height="91" width="128"><br>World of Warcraft EU</a>'?></td>
  				</tr>
  			</table>
  			<table border="0" width="100%" cellspacing="0" cellpadding="2">
				<tr>
  					<td class="spacingInstruction" colspan="2"><?=TEXT_ACCOUNT_BUYBACK_DISCLAIMER?></td>
  				</td>
  			</table>
<?
			break;
	}
?>
		</td>
	</tr>
<?
} else {
	echo '<tr><td align="center"><span class="messageStackWarning">We are not accepting any account buybacks for the time being. Please do check back from time to time for updates on our account buyback status.<br><br>Thank you for your patience and we apologize for any inconveniences caused.</span></td></tr>';
}
?>
</table>