<h1><?=TITLE_MY_PROFILE ?></h1>
<div class="breakLine"><!-- --></div>

<?php if ($messageStack->size('account_edit') > 0) { ?>
	<div class="loginColumnBorder" style="background-color: #FFFDDB">
		<div class="breakLine"><!-- --></div>
		<div style="padding-left:5pt;"><?=$messageStack->output('account_edit'); ?></div>
		<div class="breakLine"><!-- --></div>
	</div>
	<div class="breakLine"><!-- --></div>
<?php } ?>

<script language="javascript" src="<?=DIR_WS_JAVASCRIPT?>customer_xmlhttp.js"></script>
<? ob_start(); ?>
	<?=tep_draw_form('account_edit_form', tep_href_link(FILENAME_ACCOUNT_EDIT, tep_get_all_get_params(array('action', 'trace')) . 'action=process', 'SSL'), 'post', 'onSubmit=""') . tep_draw_hidden_field('action', 'process')?>
		<table border="0" width="100%" cellspacing="10" cellpadding="0">
			<tr>
				<td>
		    		<div style="font-size:16px;font-weight:bold;"><?=HEADING_TITLE ?></div>
		    		<div class="breakLine"><!-- --></div>
		    		<div><?=FORM_REQUIRED_MSG?></div>
		    		<div class="breakLine" style="height:20px;"><!-- --></div>
		    		
					<table border="0" width="100%" cellspacing="0" cellpadding="0">
		          		<tr><td colspan="3" style="font-weight:bold"><?=TITLE_LOGIN_EMAIL_PASSWORD?></td></tr>
		          		<tr><td colspan="3"><div class="breakLine"></div></td></tr>
		          		<tr  style="height:45px;" valign="top">
		          			<td style="width:120px;"><?=ENTRY_EMAIL_ADDRESS?></td>
		          			<td style="width:220px;"><?=tep_draw_input_field('edit_profile_email', $account['customers_email_address'], 'id="email" onfocus="getFocus(this.id, \'email_message\', \''.ENTRY_EMAIL_NOTICE.'\')" onblur="input_validation(this.id, \'email_message\', \''. ENTRY_EMAIL_NOTICE.'\', \''.ENTRY_EMAIL_JS_ERROR.'\')"')?>&nbsp;<span class="requiredInfo"><?=ENTRY_EMAIL_ADDRESS_TEXT?></span></td>
		          			<td style="width:380px;"><div id="email_message"></div>&nbsp;</td>
		          		</tr>
		          		<tr  style="height:45px;" valign="top">
		          			<td style="width:120px;"><a name="pwd"></a><?=ENTRY_PASSWORD_CURRENT ?></td>
		          			<td style="width:220px;"><?=tep_draw_password_field('edit_profile_current_password', '', 'autocomplete="off" id="current_password" onfocus="getFocus(this.id, \'current_password_message\', \''.sprintf(ENTRY_PASSWORD_NOTICE, ENTRY_PASSWORD_MIN_LENGTH).'\')" onblur="input_validation(this.id, \'current_password_message\', \''.sprintf(ENTRY_PASSWORD_NOTICE, ENTRY_PASSWORD_MIN_LENGTH).'\', \''.sprintf(ENTRY_PASSWORD_CURRENT_JS_ERROR, ENTRY_PASSWORD_MIN_LENGTH).'\')"')?><!--span class="requiredInfo">&nbsp;<?=ENTRY_PASSWORD_CURRENT_TEXT?></span--></td>
		          			<td style="width:380px;"><div id="current_password_message"></div>&nbsp;</td>
		          		</tr>
		          		<tr  style="height:45px;" valign="top">
		          			<td style="width:120px;"><?=ENTRY_PASSWORD_NEW?></td>
		          			<td style="width:220px;"><?=tep_draw_password_field('edit_profile_password', '', 'autocomplete="off" id="new_password" onfocus="getFocus(this.id, \'new_password_message\', \''.sprintf(ENTRY_PASSWORD_NOTICE, ENTRY_PASSWORD_MIN_LENGTH).'\')" onblur="input_validation(this.id, \'new_password_message\', \''.sprintf(ENTRY_PASSWORD_NOTICE, ENTRY_PASSWORD_MIN_LENGTH).'\', \''.sprintf(ENTRY_PASSWORD_JS_ERROR, ENTRY_PASSWORD_MIN_LENGTH).'\')"')?>&nbsp;<!--span class="requiredInfo"><?=ENTRY_PASSWORD_NEW_TEXT?></span--></td>
		          			<td style="width:380px;"><div id="new_password_message"></div>&nbsp;</td>
		          		</tr>
		          		<tr  style="height:45px;" valign="top">
		          			<td style="width:120px;"><?=ENTRY_PASSWORD_NEW_CONFIRMATION?></td>
		          			<td style="width:220px;"><?=tep_draw_password_field('edit_profile_confirm_password', '', 'autocomplete="off" id="confirm_password" onfocus="getFocus(this.id, \'confirm_password_message\', \''.sprintf(ENTRY_PASSWORD_NOTICE, ENTRY_PASSWORD_MIN_LENGTH).'\')" onblur="input_validation(this.id, \'confirm_password_message\', \''.sprintf(ENTRY_PASSWORD_NOTICE, ENTRY_PASSWORD_MIN_LENGTH).'\', \''.sprintf(ENTRY_PASSWORD_CONFIRMATION_JS_ERROR, ENTRY_PASSWORD_MIN_LENGTH).'\')"')?>&nbsp;<!--span class="requiredInfo"><?=ENTRY_PASSWORD_NEW_CONFIRMATION_TEXT?></span--></td>
		          			<td style="width:380px;"><div id="confirm_password_message"></div>&nbsp;</td>
		          		</tr>
		  				<tr><td colspan="3"><div class="dottedLine" style="lin-height: 2px;">&nbsp;</div></td></tr>
		  				<!--tr><td colspan="3"><div class="breakLine"></div></td></tr-->
		  				
		  				<tr><td colspan="3" style="font-weight:bold" valign="top"><?=TITLE_PERSONAL_DETAILS?></td></tr>
		  				<tr><td colspan="3"><div class="breakLine"></div></td></tr>
		          		<tr  style="height:45px;" valign="top">
		          			<td style="width:120px;"><?=ENTRY_FIRST_NAME?></td>
		          			<td style="width:220px;"><?=tep_draw_input_field('edit_profile_firstname', $account['customers_firstname'], ' id="firstname" onfocus="getFocus(this.id, \''.firstname_message.'\', \''.ENTRY_FIRSTNAME_NOTICE.'\')" onblur="input_validation(this.id, \''.firstname_message.'\', \''.ENTRY_FIRSTNAME_NOTICE.'\', \''.sprintf(ENTRY_FIRSTNAME_JS_ERROR, ENTRY_FIRST_NAME_MIN_LENGTH).'\')"')?>&nbsp;<span class="requiredInfo"><?=ENTRY_FIRST_NAME_TEXT?></span></td>
		          			<td style="width:380px;"><div id="firstname_message"></div>&nbsp;</td>
		          		</tr>
		          		<tr  style="height:45px;" valign="top">
		          			<td style="width:120px;"><?=ENTRY_LAST_NAME?></td>
		          			<td style="width:220px;"><?=tep_draw_input_field('edit_profile_lastname', $account['customers_lastname'], 'id="lastname" onfocus="getFocus(this.id, \''.lastname_message.'\', \''.ENTRY_LASTNAME_NOTICE.'\')" onblur="input_validation(this.id, \''.lastname_message.'\', \''.ENTRY_LASTNAME_NOTICE.'\', \''.sprintf(ENTRY_LASTNAME_JS_ERROR, ENTRY_LAST_NAME_MIN_LENGTH).'\')"')?>&nbsp;<span class="requiredInfo"><?=ENTRY_LAST_NAME_TEXT?></span></td>
		          			<td style="width:380px;"><div id="lastname_message"></div>&nbsp;</td>
		          		</tr>
<?php 
    $disabled_edit_mobile_phone_number = ' disabled';

    if (substr($edit_profile_contact_number, -4) == '****') {
        $edit_profile_contact_number = $edit_profile_contact_number;	
    } else {
        $edit_profile_contact_number = tep_not_null($account['customers_telephone']) ? substr($account['customers_telephone'], 0, -4).'****' : '';
    }
    
    if (!tep_not_empty($account['customers_telephone'])) {
        $disabled_edit_mobile_phone_number = '';
    }
?>
                        <tr style="height:45px;" valign="top">
		          			<td style="width:120px;"><?=ENTRY_COUNTRY_CODE?></td>
		          			<td style="width:220px;"><?=tep_get_ezsignup_country_list('edit_profile_country_code', ((tep_not_null($account['customers_country_dialing_code_id']) && $account['customers_country_dialing_code_id'] != '0')? $account['customers_country_dialing_code_id'] : $country), 'style="width:150px;" id="country" onblur="input_validation(this.id, \''.country_code_message.'\', \''.ENTRY_COUNTRY_ERROR.'\', \''.ENTRY_COUNTRY_ERROR.'\')"' . $disabled_edit_mobile_phone_number)?>&nbsp;<span class="requiredInfo"><?=ENTRY_COUNTRY_CODE_TEXT?></span></td>
		          			<td style="width:380px;"><div id="country_code_message"></div>&nbsp;</td>
		          		</tr>
		          		<tr style="height:60px;" valign="top">
		          			<td style="width:120px;"><?=ENTRY_CONTACT_NUMBER?></td>
                            <td style="width:220px;"><?=tep_draw_input_field('edit_profile_contact_number', $edit_profile_contact_number, 'autocomplete="off" id="contact_number" onfocus="getFocus(this.id, \''.contact_number_message.'\', \''.ENTRY_CONTACTNUMBER_NOTICE.'\')" onblur="input_validation(this.id, \''.contact_number_message.'\', \''.ENTRY_CONTACTNUMBER_NOTICE.'\', \''.ENTRY_CONTACT_NUMBER_JS_ERROR.'\')"' . $disabled_edit_mobile_phone_number)?>&nbsp;<?php echo ($disabled_edit_mobile_phone_number!='' ? ' <a href="javascript:void(0);" onClick="myacc_edit_phone();">Edit</a>' : '<span class="requiredInfo">' . ENTRY_CONTACT_NUMBER_TEXT . '</span>')?></td>
		          			<td style="width:380px;"><div id="contact_number_message"></div>&nbsp;</td>
		          		</tr>
						<tr  style="height:45px;" valign="top">
							<td style="width:120px;"><?=ENTRY_GENDER?></td>
							<td style="width:220px;">
								<?=tep_draw_radio_field('edit_profile_gender', 'm', ($account['customers_gender']=='m'? true:false)). ' '. MALE ?> &nbsp; &nbsp;
								<?=tep_draw_radio_field('edit_profile_gender', 'f', ($account['customers_gender']=='f'? true:false)). ' '. FEMALE ?>
							</td>
							<td style="width:380px;">&nbsp;</td>
						</tr>
						<tr  style="height:45px;" valign="top">
							<td style="width:120px;"><?=ENTRY_DATE_OF_BIRTH ?></td>
							<td style="width:220px;"><?=tep_draw_date_selection_boxes($dob_month, $dob_day, $dob_year, true, ' onfocus="getFocus(this.id, \'dob_message\', \''.ENTRY_DOB_NOTICE.'\')" onblur="input_validation(this.id, \'dob_message\', \''.ENTRY_DOB_NOTICE.'\', \''.ENTRY_DOB_JS_ERROR.'\')"')?></td>
							<td style="width:380px;"><div id="dob_message"></div>&nbsp;</td>
						</tr>
						<tr  style="height:45px;" valign="top">
							<td style="width:120px;"><?=ENTRY_INSTANT_MESSENGER?></td>
							<td style="width:220px;"><?=tep_get_ezsignup_instant_message_type_list('edit_profile_instant_messenger', '', ' id="instantmessage" onfocus="getFocus(this.id,\'im_message\',\''.ENTRY_IM_NOTICE.'\')" onblur="getBlur(this.id, \'im_message\',\''.ENTRY_IM_NOTICE.'\')" onchange="createIM('.ENTRY_IM_ACCOUNT_MAX_ENTRIES.', \'\', \'\', \'\', \'\', \'\')"') ?></td>
							<td style="width:380px;"><div id="im_message"></div>&nbsp;</td>
						</tr>
						<tr valign="top">
							<td colspan="3">
								<!-- Start Instant Message -->
								<table width="100%" border="0" cellpadding="0" cellspacing="0">
									<tbody id="InstantMessageTBody">
									</tbody>
								</table>
								<!-- end Instant Message -->
							</td>
						</tr>
		  				<tr><td colspan="3"><div class="dottedLine">&nbsp;</div></td></tr>
		  				<tr><td colspan="3"><a name="ba"></a><div class="breakLine"></div></td></tr>
		  				
						<tr><td colspan="3" style="font-weight:bold"><?=TITLE_BILLING_ADDRESS_FOR_CREDIT_CARD?></td></tr>
		          		<tr><td colspan="3"><div class="breakLine"></div></td></tr>
						<tr  style="height:45px;" valign="top">
							<td style="width:120px;"><?=ENTRY_BILLING_ADDRESS1 ?></td>
							<td style="width:220px;">
								<?=tep_draw_input_field('edit_profile_billing_address1', $address_row['entry_street_address'], 'id="billing_address1" onfocus="getFocus(this.id, \'\', \'\')" onblur="input_validation(this.id, \'billing_address1_message\', \'\', \''.ENTRY_STREET_ADDRESS_ERROR.'\')"') ?>
							</td>
							<td style="width:380px;"><div id="billing_address1_message"></div>&nbsp;</td>
						</tr>
						<tr  style="height:45px;" valign="top">
							<td style="width:120px;"><?=ENTRY_BILLING_ADDRESS2 ?></td>
							<td style="width:220px;">
								<?=tep_draw_input_field('edit_profile_billing_address2', $address_row['entry_suburb'], 'id="billing_address2" onfocus="getFocus(this.id, \'\', \'\')" onblur="input_validation(this.id, \'billing_address2_message\', \'\', \''.ENTRY_STREET_ADDRESS_ERROR.'\')"') ?>
							</td>
							<td style="width:380px;"><div id="billing_address2_message"></div>&nbsp;</td>
						</tr>
						<tr  style="height:45px;" valign="top">
							<td style="width:120px;"><?=ENTRY_CITY ?></td>
							<td style="width:220px;">
								<?=tep_draw_input_field('edit_profile_billing_city', $address_row['entry_city'], 'id="billing_city" onfocus="getFocus(this.id, \'\', \'\')" onblur="input_validation(this.id, \'billing_city_message\', \'\', \''.ENTRY_CITY_ERROR.'\')"') ?>
							</td>
							<td style="width:380px;"><div id="billing_city_message"></div>&nbsp;</td>
						</tr>
						<tr  style="height:45px;" valign="top">
							<td style="width:120px;"><?=ENTRY_POST_CODE ?></td>
							<td style="width:220px;">
								<?=tep_draw_input_field('edit_profile_billing_postcode', $address_row['entry_postcode'], 'id="billing_postcode" onfocus="getFocus(this.id, \'\', \'\')" onblur="input_validation(this.id, \'billing_postcode_message\', \'\', \''.ENTRY_POST_CODE_ERROR.'\')"') ?>
							</td>
							<td style="width:380px;"><div id="billing_postcode_message"></div>&nbsp;</td>
						</tr>
						<tr  style="height:45px;" valign="top">
							<td style="width:120px;"><?=ENTRY_COUNTRY ?></td>
							<td style="width:220px;">
								<?=tep_get_country_list('edit_profile_billing_country', (tep_not_null($address_row['entry_country_id'])? $address_row['entry_country_id']: $country), 'style="width:150px;" id="billing_country" onChange="refreshStateList(this, \'state_div\', \'state\', \''.(int)$languages_id.'\', true, \'billing_country_message\', \''.ENTRY_COUNTRY_ERROR.'\', \'billing_state_message\', \'\', \'\')"  onblur="input_validation(this.id, \'billing_country_message\', \'\', \''.ENTRY_COUNTRY_ERROR.'\')"') ?>
							</td>
							<td style="width:380px;"><div id="billing_country_message"></div>&nbsp;</td>
						</tr>
						<tr  style="height:45px;" valign="top">
							<td style="width:120px;"><?=ENTRY_STATE ?></td>
							<td style="width:220px;">
								<span id="state_div" style="float:left;">
								<?php
									$zones_array = array( array('id' => '', 'text' => PULL_DOWN_DEFAULT) );
									$zones_select_sql = "SELECT zone_id, zone_name FROM " . TABLE_ZONES . " WHERE zone_country_id = '" . (tep_not_null($address_row['entry_country_id'])? $address_row['entry_country_id']: $country) . "' ORDER BY zone_name";
									$zones_result_sql = tep_db_query($zones_select_sql);
                                    
									if (tep_db_num_rows($zones_result_sql) > 0) {
										while ($zones_row = tep_db_fetch_array($zones_result_sql)) {
											$zones_array[] = array('id' => $zones_row['zone_id'], 'text' => $zones_row['zone_name']);
										}
										
										echo tep_draw_pull_down_menu('state', $zones_array, $address_row['entry_zone_id'], 'id="state" onfocus="getFocus(this.id, \'billing_state_message\', \'\')" onblur="input_validation(this.id, \'billing_state_message\', \'\', \''.ENTRY_STATE_ERROR_SELECT.'\')"');
									} 
									else {
										echo tep_draw_input_field('state', $address_row['entry_state'], 'id="state" onfocus="getFocus(this.id, \'billing_state_message\', \'\')" onblur="input_validation(this.id, \'billing_state_message\', \'\', \''.ENTRY_STATE_ERROR.'\')"');
									}
								?>  
								</span>
							</td>
							<td style="width:380px;"><div id="billing_state_message"></div>&nbsp;</td>
						</tr>
		  						  				
						<tr valign="top">
							<td colspan="3" style="width:100%;">
								<?php 
									// Customer's Profile Verification (Validate customer's answer)
									//print $customers_security_obj->show_customer_security_question_html("inputLabelNew","inputBoxContentsNew");
								?>
							</td>
						</tr>
		  				<tr><td colspan="3"><div class="dottedLine">&nbsp;</div></td></tr>
		  				<!--tr><td colspan="3"><div class="breakLine"></div></td></tr-->
		  				
		  				<tr>
		  					<td colspan="3">
								<div style="height:50px; display: block;">
									<div style="float:left; width:50px; display: inline-block;">
										<div class="triangleRightIconBlue" style="margin: 3px 5px 0px 0px;"><!-- --></div>
										<div class="loginBoxLink"><a href="<?=tep_href_link(FILENAME_ACCOUNT, '', 'SSL')?>"><?=IMAGE_BUTTON_BACK?></a></div>
									</div>
									<?=tep_div_button(1, '&nbsp; '. IMAGE_BUTTON_SAVE_PROFILE .' &nbsp; ', 'account_edit_form', 'style="float:right;"', 'green_button', false, 'if(check_form(\'account_edit_form\')){document.account_edit_form.submit()}') ?>
								</div>
							</td>
						</tr>
		  			</table>
		  		</td>
		  	</tr>
		</table>
	</form>
<?
$account_edit_content_string = ob_get_contents();
ob_end_clean();

echo $page_obj->get_html_simple_rc_box('',$account_edit_content_string,13);
?>

<script language="javascript">
window.onload = function ()
{
	var objIM = document.getElementById("instantmessage");
	objIM.options[0].selected = true;
	
	<?php 

	if ($im_existed_number_of_accounts > 0) {
		for($n=0; $n < $im_existed_number_of_accounts; $n++) {
			
			echo "createIM(".ENTRY_IM_ACCOUNT_MAX_ENTRIES.", '".$im_account['type'][$n]."', '".$im_account['userid'][$n]."', '".$im_account['remarks'][$n]."', '".$im_type[$im_account['type'][$n]]."', '".$im_account['id'][$n]."');";
		}
	}

	?>
}


var form = "";
var submitted = false;
var error = false;
var error_message = "";
var check_isNaN = false;

function myacc_edit_phone() {
    pop_out_loading = '<table id="popup_loading" cellspacing="0" cellpadding="0" width="100%"><tr><td>';
    pop_out_loading += '<table id="regional_table" width="100%" bgcolor="#ffffff" cellspacing="2" cellpadding="10" style="border: 5px solid white">';
    pop_out_loading += '<tr><td align="center" style="text-align:center;">'+'<?php echo tep_image(DIR_WS_IMAGES . "loading.gif", "", 20, 20); ?>'+'</td></tr>';
    pop_out_loading += '</table>';
    pop_out_loading += '</td></tr></table>';
    
    jQuery('#general_content').html(pop_out_loading);
    show_fancybox('general_popup_box');
    var ref_url = "customer_xmlhttp.php?action=edit_phone";
    
    jQuery.ajax({
        url: ref_url,
        type: 'GET',
        dataType: 'xml',
        timeout: 30000,
        success: function(xml) {
            var country_dialing_code_id = jQuery(xml).find('country_dialing_code_id').text();
            var telephone = jQuery(xml).find('telephone').text();
            var security_token = jQuery(xml).find('security').text();
            
            pop_out = '<?=tep_draw_form("myacc_edit_phone_form", '', '', 'id="myacc_edit_phone_form"')?>';
            pop_out += '<table id="popup_add_to_cart" width="100%" cellspacing="0" cellpadding="0" style="background-color:#ffffff">';
            pop_out += '<tr><td class="smallText" colspan="2" style="padding:5px 5px 0"><div class="error_msg" style="background-color: #FFFDDB;line-height: 25px;"></div></td></tr>';
            pop_out += '<tr><td class="smallText" style="padding: 10px 20px 10px 20px"><?=ENTRY_CURRENT_CONTACT_NUMBER?></td><td>'+jQuery(xml).find('current_telephone').text()+'</td></tr>';
            pop_out += '<tr><td class="smallText" style="padding: 10px 20px 10px 20px;"><?=ENTRY_NEW_COUNTRY_CODE?></td><td>'+country_dialing_code_id+'</td></tr>';
            pop_out += '<tr><td class="smallText" style="padding: 10px 20px 10px 20px;"><?=ENTRY_NEW_CONTACT_NUMBER?></td><td>'+telephone+'</td></tr>';
            pop_out += '<tr><td class="smallText" colspan="2"><div class="dottedLine"><!-- --></div></td></tr>';
            pop_out += '<tr><td class="smallText" colspan="2" style="padding: 10px 20px 10px 20px;">'+security_token+'</td></tr>';
            pop_out += '<tr><td colspan="2" style="padding: 10px 30px 10px 20px; text-align: right;">'+'<?php echo tep_image_button2('gray_short', "javascript:void(0);", BUTTON_CANCEL, '', ' onClick="hide_fancybox(\\\'general_popup_box\\\');"') . '&nbsp;&nbsp;' . tep_image_button2('gray_short', "javascript:void(0);", BUTTON_SAVE_CHANGES, '', ' onClick="save_myacc_phone(\\\'myacc_edit_phone_form\\\');"'); ?>'+'</td></tr>';
            pop_out += '</table></form>';
            
            custom_general_popup_box(pop_out, '<b class="largeFont"><?=HEADER_CHANGE_MOBILE ?></b>', '530px', {padding:'0px'});
        }
    });
}

function save_myacc_phone(form_id) {
	jQuery.ajax({
        type:'POST',
        url:'customer_xmlhttp.php?action=save_phone&order_id=<?=$order_id?>',
        data:$('#'+form_id).serialize(),
        dataType: 'xml',
        beforeSend: function(){
            jQuery('#'+form_id+' div.error_msg, #token_request_icon, #token_request_msg, #verify_mobile_number').html('');
        },
        success: function(xml) {
            var status = jQuery(xml).find('error').text(),
                msg = jQuery(xml).find('message').text();
            
            if(status=='2'){
                jQuery("#token_request_msg").html(msg);
                jQuery("#token_request_icon").html('<?=tep_image(DIR_WS_ICONS . 'error.gif')?>');
            }else if(status=='1'){
                jQuery('#'+form_id+' div.error_msg').html(msg);
                jQuery("#token_request_msg").html('<?=TEXT_REQUEST_TOKEN_MSG?>');
                jQuery("#token_request_icon").html('<?=tep_image(DIR_WS_ICONS . 'success.gif')?>');
            }else{
                var pop_out = '<table id="popup_loading" cellspacing="0" cellpadding="0" width="100%"><tr><td>';
                pop_out += '<table id="regional_table" width="100%" bgcolor="#ffffff" cellspacing="2" cellpadding="10" style="border: 5px solid white">';
                pop_out += '<tr><td style="padding: 10px 20px 10px 20px;">'+msg+'</td></tr>';
                pop_out += '</table>';
                pop_out += '</td></tr>';
                pop_out += '<tr><td ><div class="dottedLine"><!-- --></div></td></tr>';
                pop_out += '<tr><td style="padding: 10px 30px 10px 20px; text-align: center;">'+'<?php echo tep_image_button2('gray_short', "javascript:void(0);", BUTTON_OK, '', ' onClick="jQuery(\\\'div.popup_close_button\\\').click();"')?>'+'</td></tr></table>';
                
                custom_general_popup_box(pop_out, '<b class="largeFont"><?=HEADER_CHANGE_MOBILE ?></b>', '530px', {padding:'0px'});
            }
        }
	});
	return false;
}

function check_input(field_name, field_size, message, check_isNaN) {
	if (form.elements[field_name] && (form.elements[field_name].type != "hidden")) {
		var field_value = trim_str(form.elements[field_name].value);
	
		if ((field_value == '' || field_value.length < field_size) || (check_isNaN == true && validateInteger(trim_str(field_value)) == true)) {
			error_message = error_message + "* " + message + "\n";
			error = true;
		}
	}
}

function check_select(field_name, field_default, message) {
	if (form.elements[field_name] && (form.elements[field_name].type != "hidden")) {
		var field_value = form.elements[field_name].value;

		if (field_value == field_default) {
			error_message = error_message + "* " + message + "\n";
			error = true;
		}
	}
}


function check_form(form_name) {
	if (submitted == true) {
		alert("<?php echo JS_ERROR_SUBMITTED; ?>");
		return false;
	}
	
	error = false;
	form = eval('document.'+form_name);
	
	error_message = "<?php echo JS_ERROR; ?>";
	var emailaddr = form.elements["email"].value;
	var contact_number = form.elements["contact_number"].value;
	
	if (validateEmail(emailaddr) != true) {
		error_message = error_message + "* " + "<?php echo ENTRY_EMAIL_JS_ERROR; ?>" + "\n";
		error = true;		
	}
	
	if (trim_str(form.elements["current_password"].value).length > 0) {
		check_input("current_password", <?php echo ENTRY_PASSWORD_MIN_LENGTH; ?>, "<?php echo sprintf(ENTRY_PASSWORD_CURRENT_JS_ERROR, ENTRY_PASSWORD_MIN_LENGTH); ?>");
	}
	if (trim_str(form.elements["current_password"].value).length > 0) {
		check_input("new_password", <?php echo ENTRY_PASSWORD_MIN_LENGTH; ?>, "<?php echo sprintf(ENTRY_PASSWORD_JS_ERROR, ENTRY_PASSWORD_MIN_LENGTH); ?>");
	}
	if (trim_str(form.elements["current_password"].value).length > 0) {
		check_input("confirm_password", <?php echo ENTRY_PASSWORD_MIN_LENGTH; ?>, "<?php echo ENTRY_PASSWORD_NEW_NOT_MATCH_ERROR; ?>");
	}
	
	check_input("firstname", <?php echo ENTRY_FIRST_NAME_MIN_LENGTH; ?>, "<?php echo ENTRY_FIRST_NAME_ERROR; ?>", true);
	check_input("lastname", <?php echo ENTRY_LAST_NAME_MIN_LENGTH; ?>, "<?php echo ENTRY_LAST_NAME_ERROR; ?>", true);
	check_select("country", "", "<?php echo ENTRY_COUNTRY_ERROR; ?>");
	
	if (trim_str(contact_number).length > 0 && isNaN(trim_str(contact_number)) && contact_number.substring((contact_number.length - 4), contact_number.length) != "****") {
		error_message = error_message + "* " + "<?php echo ENTRY_CONTACT_NUMBER_JS_ERROR; ?>" + "\n";
		error = true;
	}
	else if (isNaN(trim_str(contact_number)) == false) {
		check_input("contact_number", <?php echo ENTRY_TELEPHONE_MIN_LENGTH; ?>, "<?php echo sprintf(ENTRY_CONTACT_NUMBER_ERROR, ENTRY_TELEPHONE_MIN_LENGTH); ?>");
	}
	
	check_input("answer", <?php echo ENTRY_SECRET_ANSWER_MIN_LENGTH; ?> , "<?php echo sprintf(ENTRY_ANSWER_ERROR, ENTRY_SECRET_ANSWER_MIN_LENGTH); ?>");
	
	// Optional Information:
	var day_value = form.elements["dob_day"].value;
	var mth_value = form.elements["dob_month"].value;
	var year_value = form.elements["dob_year"].value;
	var baddr1_value = trim_str(form.elements["billing_address1"].value);
	var baddr2_value = trim_str(form.elements["billing_address2"].value);
	var bcity_value = trim_str(form.elements["billing_city"].value);
	var bpostcode_value = trim_str(form.elements["billing_postcode"].value);
	var bcountry_value = form.elements["billing_country"].value;
	var bstate = form.elements["state"];
	
	if (day_value == '' && (mth_value != '' || year_value != '')) {
		check_select("dob_day","","<?php echo ENTRY_DATE_OF_BIRTH_ERROR_1." ".DAY.ENTRY_DATE_OF_BIRTH_ERROR_2." ".DAY.ENTRY_DATE_OF_BIRTH_ERROR_3; ?>");
	}
	if (mth_value == '' && (day_value != '' || year_value != '')) {
		check_select("dob_month","","<?php echo ENTRY_DATE_OF_BIRTH_ERROR_1." ".MONTH.ENTRY_DATE_OF_BIRTH_ERROR_2." ".MONTH.ENTRY_DATE_OF_BIRTH_ERROR_3; ?>");
	}
	if (year_value == '' && (mth_value != '' || day_value != '')) {
		check_select("dob_year","","<?php echo ENTRY_DATE_OF_BIRTH_ERROR_1." ".YEAR.ENTRY_DATE_OF_BIRTH_ERROR_2." ".YEAR.ENTRY_DATE_OF_BIRTH_ERROR_3; ?>");
	}


	if (baddr1_value.toLowerCase() != 'null' && baddr1_value.length > 0 && baddr1_value.length < <?php echo ENTRY_STREET_ADDRESS_MIN_LENGTH; ?> ) {
		error_message = error_message + "* " + "<?php echo ENTRY_STREET_ADDRESS_ERROR; ?>" + "\n";
		error = true;
		var erraddr1 = true;
	}
	
	if (erraddr1 == true) {
		if (baddr2_value.length > 0 && baddr2_value.toLowerCase() != 'null') {
			check_input("billingaddress2", <?php echo ENTRY_STREET_ADDRESS_MIN_LENGTH; ?>, "<?php echo ENTRY_STREET_ADDRESS_ERROR; ?>");
		}
		check_input("billing_city", <?php echo ENTRY_CITY_MIN_LENGTH; ?>, "<?php echo ENTRY_CITY_ERROR; ?>");
		check_input("billing_postcode", <?php echo ENTRY_POSTCODE_MIN_LENGTH; ?>, "<?php echo ENTRY_POST_CODE_ERROR; ?>");
		check_select("billing_country", "", "<?php echo ENTRY_COUNTRY_ERROR; ?>");
		
		if (bcountry_value != '') {
			if (bstate.type == 'text') {
				check_input("state", <?php echo ENTRY_STATE_MIN_LENGTH; ?>, "<?php echo ENTRY_STATE_ERROR; ?>", true);
			} else {
				check_select("state", "", "<?php echo ENTRY_STATE_ERROR_SELECT; ?>");	
			}
		}
	}
	
	
	if (error == true) {
		alert(error_message);
		return false;
	} else {
		submitted = true;
		return true;
	}
}

/* add new instant message row */
var hasLoaded = false;
var ROW_BASE = 1;
var tr = document.getElementsByTagName("tr");
var im_account_counter = 1;

function createIM(im_account_limit, im_account_type, im_account_userid, im_account_remarks, im_account_name, im_account_id)
{
	if (im_account_type != '' && im_account_type != null) {
		//alert(im_account_name);
		addExistingIMRow(im_account_type, im_account_userid, im_account_remarks, im_account_name, im_account_id);
	}
	else {
		if (im_account_counter <= im_account_limit) {
			addNewIMRow();
		}
		else {
			alert("You only can create " + im_account_limit + " instant message accounts.");
		}	
	}
}

function addExistingIMRow(im_type, im_userid, im_remarks, im_name, im_id) {
	var tbl = document.getElementById("InstantMessageTBody");
	var objIM = document.getElementById("instantmessage");
	
	var newTR = document.createElement("tr");
	var newTD1 = document.createElement("td");
	var newTD2 = document.createElement("td");
	var newTD3 = document.createElement("td");
	var newTD4 = document.createElement("td");

	if (im_type != '0')
	{
		newTD1.innerHTML = im_name + ":<input type='hidden' name='edit_instantmessageaccountid[]' value='" + im_id + "'><input type='hidden' name='edit_instantmessageaccounttype[]' value='"+ im_type +"'>";
		newTD2.innerHTML = "<input type='text' name='edit_instantmessageaccount[]' value='" + im_userid + "' style='width:100px;height:21px;' onfocus='this.className=\"focus\"' onblur='this.className=\"\"'>" + " &nbsp;<image src='/images/icon-collapse-small.gif' id='rmInstantMessageAccount' onclick='deleteIMRow(this)'>";
		newTD3.innerHTML = "&nbsp;";
		newTD4.innerHTML = "&nbsp;";
	}
	
	else if (im_type == '0')
	{
		newTD1.innerHTML = "Others&nbsp;IM:<input type='hidden' name='edit_othersinstantmessageaccountid[]' value='" + im_id + "'><input type='hidden' name='edit_othersinstantmessageaccounttype[]' value='" + im_type + "'>";
		newTD2.innerHTML = "<input type='text' name='edit_othersinstantmessageaccountname[]' value='" + im_remarks + "' style='width:100px;height:21px;' onfocus='this.className=\"focus\"' onblur='this.className=\"\"'>";
		newTD3.innerHTML = "ID:";
		newTD4.innerHTML = "<input type='text' name='edit_othersinstantmessageaccount[]' value='" + im_userid + "' style='width:100px;height:21px;' onfocus='this.className=\"focus\"' onblur='this.className=\"\"'>" + " &nbsp;<image src='/images/icon-collapse-small.gif' id='rmInstantMessageAccount' onclick='deleteIMRow(this)'>";
	}
	
	newTD1.setAttribute('width', '70px');
	newTD2.setAttribute('width', '150px');
	newTD3.setAttribute('width', '30px');
	newTD4.setAttribute('width', '150px');
	
	newTD1.setAttribute('height', '25');
	newTD2.setAttribute('height', '25');
	newTD3.setAttribute('height', '25');
	newTD4.setAttribute('height', '25');
	
	newTR.appendChild(newTD1);
	newTR.appendChild(newTD2);
	newTR.appendChild(newTD3);
	newTR.appendChild(newTD4);
		
	tbl.appendChild(newTR);

	hasLoaded = true;
	im_account_counter = im_account_counter + 1;
}

function addNewIMRow()
{
	var tbl = document.getElementById("InstantMessageTBody");
	var objIM = document.getElementById("instantmessage");
	
	var newTR = document.createElement("tr");
	var newTD1 = document.createElement("td");
	var newTD2 = document.createElement("td");
	var newTD3 = document.createElement("td");
	var newTD4 = document.createElement("td");

	if (objIM.options[objIM.selectedIndex].value != '')
	{
		newTD1.innerHTML = objIM.options[objIM.selectedIndex].text + ":<input type='hidden' name='instantmessageaccountid[]' value=''><input type='hidden' name='instantmessageaccounttype[]' value='"+ objIM.options[objIM.selectedIndex].value +"'>";
		newTD2.innerHTML = "<input type='text' name='instantmessageaccount[]' style='width:100px;height:21px;' onfocus='this.className=\"focus\"' onblur='this.className=\"\"'>" + " &nbsp;<image src='/images/icon-collapse-small.gif' id='rmInstantMessageAccount' onclick='deleteIMRow(this)'>";
		newTD3.innerHTML = "&nbsp;";
		newTD4.innerHTML = "&nbsp;";
				
		if (objIM.options[objIM.selectedIndex].value == '0') 
		{
			newTD1.innerHTML = objIM.options[objIM.selectedIndex].text + "&nbsp;IM:<input type='hidden' name='othersinstantmessageaccountid[]' value=''><input type='hidden' name='othersinstantmessageaccounttype[]' value='" + objIM.options[objIM.selectedIndex].value + "'>";
			newTD2.innerHTML = "<input type='text' name='othersinstantmessageaccountname[]' style='width:100px;height:21px;' onfocus='this.className=\"focus\"' onblur='this.className=\"\"'>";
			newTD3.innerHTML = "ID:";
			newTD4.innerHTML = "<input type='text' name='othersinstantmessageaccount[]' style='width:100px;height:21px;' onfocus='this.className=\"focus\"' onblur='this.className=\"\"'>" + " &nbsp;<image src='/images/icon-collapse-small.gif' id='rmInstantMessageAccount' onclick='deleteIMRow(this)'>";
		}

		newTD1.setAttribute('width', '70px');
		newTD2.setAttribute('width', '150px');
		newTD3.setAttribute('width', '30px');
		newTD4.setAttribute('width', '150px');
		
		newTD1.setAttribute('height', '25');
		newTD2.setAttribute('height', '25');
		newTD3.setAttribute('height', '25');
		newTD4.setAttribute('height', '25');
		
		newTR.appendChild(newTD1);
		newTR.appendChild(newTD2);
		newTR.appendChild(newTD3);
		newTR.appendChild(newTD4);
			
		tbl.appendChild(newTR);

		hasLoaded = true;
		im_account_counter = im_account_counter + 1;
	}
}

function deleteIMRow(obj)
{
	var rowArray = new Array(obj.parentNode.parentNode);
	if (hasLoaded) {
		if (tr.length == 1)	{
			hasLoaded = false;
		}
		else {
			for (var i=0; i < rowArray.length; i++) 
			{
				var delIndex = rowArray[i].sectionRowIndex;
				rowArray[i].parentNode.deleteRow(delIndex);
			}
			
			if (im_account_counter > 1)	{
				im_account_counter = im_account_counter - 1;
			}
		}
	}
}

function getFocus (elObjID, msgObjID, msg)
{
	var elObj = document.getElementById(elObjID);
	if (elObj) {
		if (elObjID && msgObjID) {
 			displayMessage(elObjID, msgObjID, msg);
 		}
 	}
}

function getBlur (elObjID, msgObjID, msg)
{
	var elObj = document.getElementById(elObjID);
	if (elObj) {
		if (elObjID && msgObjID) {
			hideMessage(elObjID, msgObjID);
		}
	}
}

function displayMessage(elID, msgID, msg)
{
	if (document.getElementById(elID) && document.getElementById(msgID)) {
		var elObj = document.getElementById(elID);
		var msgObj= document.getElementById(msgID);

		if ((elObj.value.length == 0 || trim_str(elObj.value) == '' || elObj.value == null) && msgID && msg) {
			document.getElementById(msgID).innerHTML = msg;
			document.getElementById(msgID).style.color = "#666666";
			document.getElementById(msgID).style.width = "100%";
			document.getElementById(msgID).style.backgroundColor = '';
		}
		else if (msg == msgObj.innerHTML) {
			hideMessage(elID, msgID);
		}
	}
}

function hideMessage(elID, msgID)
{
	if (document.getElementById(msgID) != null) {
		document.getElementById(msgID).innerHTML = "";
		document.getElementById(msgID).style.border = "";
	}
}

function input_validation(elObjID, msgObjID, noticemsg, errormsg)
{
	var elObj = document.getElementById(elObjID);
	var msgObj = document.getElementById(msgObjID);

	var img_cross = '<img src="images/icons/error.gif" width="13px" height="12px" style="margin: 2px 5px 2px 2px; float: left;">';
	var js_error = false;
	
	getBlur(elObjID, msgObjID, noticemsg);
	
	switch (elObjID) {
		case 'email':
			if (trim_str(elObj.value).length > 0 && validateEmail(elObj.value) != true) {js_error = true;}
			break;
			
		case 'current_password':
			if (elObj.value.toLowerCase() == 'null' || (trim_str(elObj.value).length > 0 && trim_str(elObj.value).length < <?php echo ENTRY_PASSWORD_MIN_LENGTH; ?> )) {js_error = true;} 
			break;
			
		case 'new_password':
			if (elObj.value.toLowerCase() == 'null' || (trim_str(elObj.value).length > 0 && trim_str(elObj.value).length < <?php echo ENTRY_PASSWORD_MIN_LENGTH; ?> )) {js_error = true;} 
			break;

		case 'confirm_password':
			if (elObj.value.toLowerCase() == 'null' || trim_str(elObj.value) != trim_str(document.getElementById('new_password').value) || trim_str(elObj.value).length != trim_str(document.getElementById('new_password').value).length) {js_error = true;} 
			else if (elObj.value.toLowerCase() == 'null' || (trim_str(elObj.value).length > 0 && trim_str(elObj.value).length < <?php echo ENTRY_PASSWORD_MIN_LENGTH; ?>)) {js_error = true; errormsg = "<?php echo sprintf(ENTRY_PASSWORD_CONFIRMATION_JS_ERROR, ENTRY_PASSWORD_MIN_LENGTH); ?>";} 
			break;
		
		case 'firstname':
			if (elObj.value.toLowerCase() == 'null' ||
				(trim_str(elObj.value).length > 0 && (isNaN(trim_str(elObj.value)) == false || trim_str(elObj.value).length < <?php echo ENTRY_FIRST_NAME_MIN_LENGTH; ?>) )
				) {js_error = true;} 
			break;
					
		case 'lastname':
			if (elObj.value.toLowerCase() == 'null' || 
				(trim_str(elObj.value).length > 0 && (isNaN(trim_str(elObj.value)) == false || trim_str(elObj.value).length < <?php echo ENTRY_LAST_NAME_MIN_LENGTH; ?>) ) 
				) {js_error = true;} 
			break;

		case 'country':
			if (elObj.value == null || trim_str(elObj.value) == '') {js_error = true;} 
			break;
			
		case 'contact_number':
			var value_length = elObj.value.length;
			if (elObj.value.toLowerCase() == 'null' || (trim_str(elObj.value).length > 0 && isNaN(elObj.value) && elObj.value.substring(value_length - 4, value_length) != '****' )) {js_error = true;} 
			else if (trim_str(elObj.value).length > 0 && trim_str(elObj.value).length < <?php echo ENTRY_TELEPHONE_MIN_LENGTH; ?>) {js_error = true; errormsg = "<?php echo sprintf(ENTRY_CONTACT_NUMBER_ERROR, ENTRY_TELEPHONE_MIN_LENGTH); ?>";}
			break;
			
		case 'answer':
			if ((elObj.value.toLowerCase() == 'null' || (trim_str(elObj.value).length > 0 && trim_str(elObj.value).length < <?php echo ENTRY_SECRET_ANSWER_MIN_LENGTH; ?>)) && trim_str(document.getElementById('secretquestion').value) != 0) {js_error = true;} 
			break;
			
		case 'day':
			if ((trim_str(elObj.value).length > 0 && (trim_str(document.getElementById('month').value).length == 0 || trim_str(document.getElementById('year').value).length == 0 )) ||
				((trim_str(elObj.value).length == 0 || trim_str(document.getElementById('year').value).length == 0 ) && trim_str(document.getElementById('month').value).length > 0 ) ||
				((trim_str(elObj.value).length == 0 || trim_str(document.getElementById('month').value).length == 0) && trim_str(document.getElementById('year').value).length > 0 ))
			{js_error = true;}
			break;
			
		case 'month':
			if ((trim_str(elObj.value).length > 0 && (trim_str(document.getElementById('day').value).length == 0 || trim_str(document.getElementById('year').value).length == 0 )) ||
				((trim_str(elObj.value).length == 0 || trim_str(document.getElementById('day').value).length == 0) && trim_str(document.getElementById('year').value).length > 0 ) ||
				((trim_str(elObj.value).length == 0 || trim_str(document.getElementById('year').value).length == 0) && trim_str(document.getElementById('day').value).length > 0))
			{js_error = true;}
			break;

		case 'year':
			if ((trim_str(elObj.value).length > 0 && (trim_str(document.getElementById('day').value).length == 0 || trim_str(document.getElementById('month').value).length == 0 )) ||
				((trim_str(elObj.value).length == 0 || trim_str(document.getElementById('day').value).length == 0) && trim_str(document.getElementById('month').value).length > 0 ) ||
				((trim_str(elObj.value).length == 0 || trim_str(document.getElementById('month').value).length == 0) && trim_str(document.getElementById('day').value).length > 0))
			{js_error = true;}
			break;
			
		case 'billing_address1':
			if ((elObj.value.toLowerCase() == 'null' || (trim_str(elObj.value).length > 0 && trim_str(elObj.value).length < <?php echo ENTRY_STREET_ADDRESS_MIN_LENGTH; ?>)) &&
				(trim_str(document.getElementById('billing_address2').value).length > 0  || trim_str(document.getElementById('billing_postcode').value).length > 0  || 
				 trim_str(document.getElementById('billing_country').value).length > 0 || trim_str(document.getElementById('state').value).length > 0 ))  {js_error = true;} 
			break;
			
		case 'billing_address2':
			if (trim_str(document.getElementById('billing_address2').value).length != 0 && trim_str(document.getElementById('billing_address2').value).length < <?php echo ENTRY_STREET_ADDRESS_MIN_LENGTH; ?> ) {js_error = true;} 
			break;

		case 'billing_city':
			if (trim_str(document.getElementById('billing_city').value).length != 0 && (elObj.value.toLowerCase() == 'null' || (trim_str(elObj.value).length > 0 && trim_str(elObj.value).length < <?php echo ENTRY_CITY_MIN_LENGTH; ?>) )) 
			{js_error = true;} 
			break;
									
		case 'billing_postcode':
			if (trim_str(document.getElementById('billing_address1').value).length != 0 && (elObj.value.toLowerCase() == 'null' || (trim_str(elObj.value).length > 0 && trim_str(elObj.value).length < <?php echo ENTRY_POSTCODE_MIN_LENGTH; ?>) )) 
			{js_error = true;} 
			break;
					
		case 'billing_country':
			if (trim_str(document.getElementById('billing_address1').value).length != 0 && (elObj.value.toLowerCase() == 'null' || trim_str(elObj.value) == '')) {js_error = true;} 
			break;

		case 'state':
			if ((trim_str(document.getElementById('billing_address1').value).length > 0 && trim_str(document.getElementById('billing_country').value).length > 0) && 
				(elObj.type == 'text' && (trim_str(elObj.value).toLowerCase() == 'null' || 
				(trim_str(elObj.value).length > 0 && (isNaN(elObj.value) == false || trim_str(elObj.value).length < <?php echo ENTRY_STATE_MIN_LENGTH; ?>))))) {js_error = true;}
			break;
			
		default:
			break;
	}
	
	if (js_error == true) {
		signup_form_error = true;
		msgObj.style.color="#000000";
		msgObj.style.backgroundColor = "#FFD4D4";
		msgObj.style.width = "100%";
		msgObj.style.padding = "0px 0px 2px 0px";
		msgObj.innerHTML = '<div>' + img_cross + '</div><div style="display: inline;"><span style="display: block;">' + errormsg + '</span></div>';
	}
	else {
		msgObj.style.backgroundColor = '';
		msgObj.innerHTML = '';
	}
}

function refreshStateList (sel1, sel2, sel3, _lang, show_title, mesgid_self, errmesg_self, mesgid, errmesg_sel, errmesg_text) 
{
	var span_objRef = DOMCall(sel2);
	var objRef = DOMCall(sel3);
    var server_action = 'state_list';
    var selection_available = false;
    var span_mesg = document.getElementById(mesgid);
   

    if (objRef.type == 'text') 
    {
    	objRef = document.createElement("SELECT");
    	objRef.name = sel3;
    	objRef.id = sel3;
    	objRef.className = "ezInputField";
    	objRef.onchange = function() { input_validation(objRef.id, mesgid, '', errmesg_sel); }
    	objRef.onblur = function() { input_validation(objRef.id, mesgid, '', errmesg_sel); }
    } 
    else 
    {
    	clearOptionList(objRef);
    }
    
    span_objRef.innerHTML = 'Loading ...';
    span_mesg.innerHTML = '';
    sel1.disabled = true;

	var ref_url = "customer_xmlhttp.php?action="+server_action+"&country_id="+sel1.value+"&lang="+_lang;

	jQuery.ajax({
		url:ref_url,
		dataType: 'xml',
		timeout: 60000,
		error: function(){
			
		},
		success: function(xml) {
			clearOptionList(objRef);
      		var selection = jQuery(xml).find("selection");
			
      		if (jQuery(xml).find("selection").length > 0) 
      		{
      			if (show_title == true) 
      			{
      				appendToSelect(objRef, stateSelectionTitle['id'], stateSelectionTitle['text']);
      			}
      			
      			jQuery(xml).find("selection > option").each(function(){
      				appendToSelect(objRef, jQuery(this).attr("index"), jQuery(this).text());
      			});
      		}
      		
      		if ((show_title == true && objRef.options.length > 1) || (show_title == false && objRef.options.length > 0)) {
      			selection_available = true;
      		} else {
      			selection_available = false;
      		}
      		
			if (!selection_available) 
			{
				objRef = document.createElement("INPUT");
		    	objRef.name = sel3;
		    	objRef.id = sel3;
		    	objRef.width = '120';
		    	objRef.className = 'ezInputField';
				objRef.onfocus = function() { getFocus(this.id, '', ''); }
				objRef.onblur = function() { input_validation(this.id, mesgid, '', errmesg_text);}
			}
			
			span_objRef.innerHTML = '';
			span_objRef.appendChild(objRef);
			sel1.disabled = false;

			var msgObj = document.getElementById(mesgid_self);
			var msgObj_state = document.getElementById(mesgid);
			var img_cross = '<img src="images/icons/error.gif" width="13px" height="12px" style="margin: 2px 5px 2px 2px; float: left;">';
			
			if (trim_str(document.getElementById('billing_address1').value).length > 0 && (trim_str(sel1.value) == '' || trim_str(sel1.value).length == 0))
			{
				msgObj.style.color="#000000";
 				msgObj.style.backgroundColor = "#FFD4D4";
				msgObj.style.width = "100%";
				msgObj.style.padding = "0px 0px 2px 0px";
				msgObj.innerHTML = '<div>' + img_cross + '</div><div style="display: inline;"><span style="display: block;">' + errmesg_self + '</span></div>';
				msgObj_state.innerHTML = '';
			}
			else {
				msgObj.innerHTML = '';
				msgObj_state.innerHTML = '';
				msgObj_state.style.backgroundColor = '';
			}
		}
	});
}
</script>