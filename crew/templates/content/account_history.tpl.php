<h1><?=HEADING_TITLE ?></h1>
<div class="breakLine"><!-- --></div>

<script language="javascript" src="includes/general.js"></script>

<?php 	if ($messageStack->size('account_history') > 0) { 	?>
	<div class="loginColumnBorder" style="background-color: #FFFDDB; padding:2px;">
		<div>
			<div class="breakLine"><!-- --></div>
			<div><?=$messageStack->output('account_history'); ?></div>
			<div class="breakLine"><!-- --></div>
		</div>
	</div>
	<div class="breakLine"><!-- --></div>
<?php 	
}  
ob_start();
?>


	<table border="0" width="100%" cellspacing="5" cellpadding="0">
		<tr>
			<td>

<?php
//if ($history_type=='buyback') {
//	$orders_total = tep_count_customer_buyback_requests();
//} else {
//	$orders_total = tep_count_customer_orders();
//}

//if ($orders_total > 0) {
	if ($history_type=='buyback') {
		$history_filter_status_query = "AND brg.buyback_status_id IN ('".implode("','", $orders_type_array[$orders_type])."') ";
		
		if (isset($col)) {
			$order_by = "order by ".$buyback_order_by_array[$col]." $asc_desc";
		}
		else {
			$order_by = "order by ".$buyback_order_by_array['default']." $asc_desc";
		}
		
		//$history_query_raw = "select * from buyback_request_group where customers_id='".(int)$customer_id."' order by buyback_status_id ";
		$history_query_raw = " select brg.*, bs.buyback_status_name, ".
							 " (SELECT DISTINCT op0.custom_products_type_id FROM ". TABLE_ORDERS_PRODUCTS ." AS op0 WHERE op0.orders_id = brg.buyback_request_group_id AND op0.custom_products_type_id = 0) as cur, ".
							 " (SELECT DISTINCT op1.custom_products_type_id FROM ". TABLE_ORDERS_PRODUCTS ." AS op1 WHERE op1.orders_id = brg.buyback_request_group_id AND op1.custom_products_type_id = 1) as pwl, ".
							 " (SELECT DISTINCT op2.custom_products_type_id FROM ". TABLE_ORDERS_PRODUCTS ." AS op2 WHERE op2.orders_id = brg.buyback_request_group_id AND op2.custom_products_type_id = 2) as cdkeys ".
							 " from buyback_request_group brg left join buyback_status bs on brg.buyback_status_id = bs.buyback_status_id where customers_id='".(int)$customer_id."' $history_filter_status_query ".
							 " AND (IF (language_id = '" . (int)$languages_id . "', 1, IF((select COUNT(orders_status_id) > 0 ".
							 "                                                            from " . TABLE_ORDERS_STATUS .
							 "                                                                 where orders_status_id = bs.buyback_status_id ".
							 "                                                                 AND language_id = '" . (int)$languages_id . "'), 0, language_id = '" . (int)$default_languages_id . "'))) ".
							 " $order_by ";
            $history_split = new splitPageResults($history_select_sql, MAX_DISPLAY_ORDER_HISTORY);
	} else {
		$history_filter_status_query = " AND o.orders_status IN ('".implode("','", $orders_type_array[$orders_type])."') ";
		
		if (isset($col)) {
			$order_by = "order by ".$order_by_array[$col]." $asc_desc";
		} else {
			$order_by = "order by ".$order_by_array['default']." $asc_desc";
		}
		
		$history_select_sql = "	select o.orders_id, o.date_purchased, o.delivery_name, o.billing_name, ot.text as order_total, s.orders_status_id, s.orders_status_name 
		 					FROM " . TABLE_ORDERS . " AS o 
                                                        LEFT JOIN `" . TABLE_ORDERS_EXTRA_INFO . "` AS `oei`
                                                            ON `oei`.`orders_id` = `o`.`orders_id`
                                                            AND `oei`.`orders_extra_info_key` = 'site_id'
		 					INNER JOIN " . TABLE_ORDERS_TOTAL . " AS ot 
		 						ON (o.orders_id = ot.orders_id AND ot.class = 'ot_total') 
                                                        LEFT JOIN `" . TABLE_ORDERS_STATUS . "` AS `s`
                                                                ON `s`.`orders_status_id` = `o`.`orders_status`
		 					WHERE o.customers_id = '" . (int)$customer_id . "' 
		 						AND (IF (s.language_id = '" . (int)$languages_id . "', 1, IF((select COUNT(s.orders_status_id) > 0
                                                                    from " . TABLE_ORDERS_STATUS . "
                                                                    where s.orders_status_id = o.orders_status
                                                                    AND s.language_id = '" . (int)$languages_id . "'), 0, s.language_id = '" . (int)$default_languages_id . "')))" 
                                                                . $history_filter_status_query .
                                                                " AND (`oei`.`orders_extra_info_value` IS NULL OR `oei`.`orders_extra_info_value` = '4' ) " . $order_by;
            $history_split = new splitPageResults($history_select_sql, MAX_DISPLAY_ORDER_HISTORY, '*', 'page','db_link','full');
	}
	
	//End Paypal IPN Mod
	$history_query = tep_db_query($history_split->sql_query);

	while ($history = tep_db_fetch_array($history_query)) {
		$product_type_image_html_array = $product_type_default_html_array;
		
		// @20090219 - Select all the product types
      	$orders_products_select_sql = "	SELECT DISTINCT (custom_products_type_id) as product_type 
      									FROM " . TABLE_ORDERS_PRODUCTS . " 
      									WHERE orders_id = '" . (int)$history['orders_id'] . "' 
      										AND parent_orders_products_id = '0'";
      	$orders_products_select_res = tep_db_query($orders_products_select_sql);
		
      	while($orders_products_select_row = tep_db_fetch_array($orders_products_select_res)) {
			$product_type_image_html_array[$orders_products_select_row['product_type']] = '<td width="'.(100/count($all_product_type_array)).'%" align="center">'.tep_image($product_type_icon_array[$orders_products_select_row['product_type']]).'</td>';
		}

      	$icons_list	= '<table width="90%" border="0" cellpadding="0" cellspacing="0" align="center"><tr>';
      	for ($i=0; $i < count($product_type_image_html_array); $i++) {
      		$icons_list .= $product_type_image_html_array[$i];
      	}
      	$icons_list .= '</tr></table>';


		if ($history_type=='buyback') {
			$products_query = tep_db_query("select count(*) as count, sum(buyback_amount) as sum from buyback_request where buyback_request_group_id = '" . (int)$history['orders_id'] . "'");
			$products = tep_db_fetch_array($products_query);

			$list[$history['buyback_status_id']][$history['buyback_request_group_id']]['orders_status'] = tep_not_null($history['orders_status_name']) ? $history['orders_status_name'] : TEXT_UNKNOWN;
			$list[$history['buyback_status_id']][$history['buyback_request_group_id']]['date_purchased']  = $history['buyback_request_group_date'];
			$list[$history['buyback_status_id']][$history['buyback_request_group_id']]['order_total'] = strip_tags($currencies->format($products['sum'], true, $history['currency'], $history['currency_value']));
			$list[$history['buyback_status_id']][$history['buyback_request_group_id']]['product_type'] = $icons_list;

		} else {
			$list[$history['orders_status_id']][$history['orders_id']]['orders_status'] = tep_not_null($history['orders_status_name']) ? $history['orders_status_name'] : TEXT_UNKNOWN;
			$list[$history['orders_status_id']][$history['orders_id']]['date_purchased']  = $history['date_purchased'];
			$list[$history['orders_status_id']][$history['orders_id']]['order_total'] = strip_tags($history['order_total']);
			$list[$history['orders_status_id']][$history['orders_id']]['product_type'] = $icons_list;
  		}
	}
//}

$date_arw_img	= ($direction==1 && $col=="img_date") ? DIR_WS_ICONS."icon_arw_up.gif" : DIR_WS_ICONS."icon_arw_down.gif";
$status_arw_img	= ($direction==1 && $col=="img_status") ? DIR_WS_ICONS."icon_arw_up.gif" : DIR_WS_ICONS."icon_arw_down.gif";
$item_arw_img	= ($direction==1 && $col=="img_item") ? DIR_WS_ICONS."icon_arw_up.gif" : DIR_WS_ICONS."icon_arw_down.gif";

$division = '15%';

?>

			<div>
				<div style="display:block; padding-right:3px;">
					<div style="display:inline-block; width:100%;">
						<table width="100%" align="right" border="0" cellspacing="0" cellpadding="0">
							<tr>
								<td style="font-size:18px;font-weight:bold;width:300px;"><?=$orders_type_title_array[$orders_type]?></td>
								<?php if ($orders_type != "cancelled") {	?>
										<td style="text-align:center;width:100px; font-size: 11px;"><img src="<?=DIR_WS_ICONS."icon_cdk.gif" ?>" style="vertical-align:middle;">&nbsp;&nbsp;<?=TEXT_LEGEND_GAME_CARD ?></td>
										<td style="text-align:center;width:100px; font-size: 11px;"><img src="<?=DIR_WS_ICONS."icon_cur.gif" ?>" style="vertical-align:middle;">&nbsp;&nbsp;<?=TEXT_LEGEND_GAME_CURRENCY ?></td>
										<td style="text-align:center;width:100px; font-size: 11px;"><img src="<?=DIR_WS_ICONS."icon_pwl.gif" ?>" style="vertical-align:middle;">&nbsp;&nbsp;<?=TEXT_LEGEND_POWER_LEVELING ?></td>
										<td style="text-align:center;width:100px; font-size: 11px;"><img src="<?=DIR_WS_ICONS."icon_sc.gif" ?>" style="vertical-align:middle;">&nbsp;&nbsp;<?=TEXT_LEGEND_STORE_CREDIT ?></td>
										<td style="text-align:center;width:100px; font-size: 11px;"><img src="<?=DIR_WS_ICONS."icon_hla.gif" ?>" style="vertical-align:middle;">&nbsp;&nbsp;<?=TEXT_LEGEND_HIGH_LEVEL_ACCOUNT ?></td>
								<?php	}	?>
							</tr>
						</table>
					</div>
				</div>
				<div class="breakLine"><!-- --></div>
				
				<table id="myTable" border="0" width="100%" cellspacing="0" cellpadding="0">
					<thead>
						<div class="boxHeader" style="width:100%;margin: 0px">
							<div class="boxHeaderLeft"><!-- --></div>
							<div class="boxHeaderCenter" style="width:<?=$division?>;padding-left:8px;"><font style="text-align:left;"><?=ORDER_NUMBER_TITLE?></font></div>
							<div class="boxHeaderDivider"><!-- --></div>
							<div class="boxHeaderCenter" style="width:<?=$division?>;text-align:right;padding-left:8px;">
								<font style="text-align:left; cursor:pointer; cursor:hand;" onclick="chgImage('img_date')"><?=ORDER_DATE_TITLE?>&nbsp;<?=tep_image($date_arw_img,'','','',' style="vertical-align:middle; cursor:pointer; cursor:hand;" id="img_date" onclick="chgImage(this.id)"')?></font>
							</div>
							<div class="boxHeaderDivider"><!-- --></div>
							<div class="boxHeaderCenter" style="width:<?=$division?>;text-align:right;padding-left:8px;">
								<font style="cursor:pointer; cursor:hand;text-align:center;" onclick="chgImage('img_status')"><?=ORDER_STATUS_TITLE?>&nbsp;<?=tep_image($status_arw_img,'','','',' style="vertical-align:middle; cursor:pointer; cursor:hand;" id="img_status" onclick="chgImage(this.id)"')?></font>
							</div>
							<div class="boxHeaderDivider"><!-- --></div>
							<?php if ($orders_type != "cancelled") { ?>
								<!--th width="<?=$division?>"><div class="boxHeaderCenter" style="width:100%; text-align:center; vertical-align:middle;"><font style="cursor:pointer; cursor:hand;" onclick="chgImage('img_item')" ><?=ORDER_ITEM_TITLE ?>&nbsp;<?=tep_image($item_arw_img,'','','','id="img_item" onclick="chgImage(this.id)"') ?></font></div></th-->
								<div class="boxHeaderCenter" style="width:<?=$division?>;padding-left:8px;vertical-align:middle;"><font style="text-align:center;"><?=ORDER_ITEM_TITLE ?></font></div>
								<div class="boxHeaderDivider"><!-- --></div>
							<?php }	?>
							<div class="boxHeaderCenter" style="width:<?=$division?>;padding-right:5px;"><font style="text-align:right;"><?=ORDER_AMOUNT_TITLE?></font></div>
							<div class="boxHeaderRight"><!-- --></div>
						</div>
					</thead>
					
					<tbody>
					<?php
						if (sizeof($list) && is_array($list)) {
							$order_records_row_html = tep_draw_order_record_list($list, $orders_type);
							echo implode("\n", $order_records_row_html['on_hold']) . implode("\n", $order_records_row_html['others']);
						}
					?>
					</tbody>
				</table>
			<div class="breakLine"><!-- --></div>
			<?php
			if (is_array($list) && count($list)) {
			?>
			<div id="pager" class="boxHeader" style="width:99%; text-align:right;">
				<input type="hidden" name="direction" id="direction" value="<?=$direction?>">
				<span style="display:inline-block;"><?=$history_split->display_links(MAX_DISPLAY_PAGE_LINKS, tep_get_all_get_params(array('page', 'info', 'x', 'y'))); ?></span>
			</div>
			<? } ?>
			</td>
		</tr>
	</table>
<?
$current_order_content_string = ob_get_contents();
ob_end_clean(); 

echo $page_obj->get_html_simple_rc_box('',$current_order_content_string,13); 
?>
<div class="breakLine" style="height:10px;"><!-- --></div>

<? ob_start(); ?>
	<div class="breakLine"></div>
	<div class="boxHeader" style="width:100%;">
		<div class="boxHeaderLeft"><!-- --></div>
		<div class="boxHeaderCenter" style="width:720px;display:inline;">
			<font style="display:inline-block; color:#fff; padding-top:5px;"><?=QUESTION_AND_ANSWER_HEADER ?></font>
		</div>
		<div class="boxHeaderRight"><!-- --></div>
	</div>
	<div class="breakLine"><!-- --></div>
	
	<div style="padding:5px;"><?=$order_status_desc_array[$orders_type]?></div>
<?
$current_order_qna_content_string = ob_get_contents();
ob_end_clean(); 

echo $page_obj->get_html_simple_rc_box('',$current_order_qna_content_string,13); 
?>

<script language="javascript">
	function chgImage(img_src) {
		var objImg = document.getElementById(img_src);

		if (objImg.src.match('<?=DIR_WS_ICONS."icon_arw_down.gif" ?>')) {
			document.location = '<?=FILENAME_ACCOUNT_HISTORY."?orders_type=$orders_type&page=".$page."&col=' + img_src + '&direction=1"?>';
			objImg.src = '<?=DIR_WS_ICONS."icon_arw_up.gif" ?>';
		} 
		else {
			document.location = '<?=FILENAME_ACCOUNT_HISTORY."?orders_type=$orders_type&page=".$page."&col=' + img_src + '" ?>';
			objImg.src = '<?=DIR_WS_ICONS."icon_arw_down.gif"?>';
		}
	}
</script>