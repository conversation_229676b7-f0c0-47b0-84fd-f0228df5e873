<h1><?=HEADING_TITLE ?></h1>
<div class="breakLine"><!-- --></div>
<table border="0" width="100%" cellspacing="0" cellpadding="0">
	<tr>
		<td>
			<script language="javascript">
			<!--
				if (navigator.appName.toUpperCase().match(/MICROSOFT INTERNET EXPLORER/) != null) {
					document.write('<iframe id="theLayerIframe" src="javascript:;" marginwidth="0" marginheight="0" align="bottom" scrolling="no" frameborder="0" style="position:absolute; width:450px; left: 439; top: 351; visibility: hidden; display: block; filter: alpha(opacity=0);"></iframe>');
				}
			//-->
			</script>
			<div class="theLayer" id="theLayer"></div>
			<div class="theLayerBg" id="theLayerBg">&nbsp;</div>
			<script language="javascript" src="<?=DIR_WS_JAVASCRIPT?>countdown/jquery.countdown.pack.js"></script>
			<link rel="stylesheet" type="text/css" href="<?=DIR_WS_JAVASCRIPT?>countdown/jquery.countdown.css">
			<script language="javascript">
			<!--
				var winW = document.body.scrollWidth;

				jQuery("#theLayerBg").css('width',winW);
				jQuery("#theLayerBg").css('position','absolute');
				jQuery("#theLayerBg").css('height',100);

				jQuery(document).ready(function(){
					var winH = jQuery("#footerBottomDiv").offset().top + 70;

					jQuery("#theLayerBg").css('height',winH);
				});
			//-->
			</script>
		</td>
	</tr>
</table>
<?php
if ($history_type == 'buyback') {
	$buyback_order_result = tep_db_query("SELECT * FROM buyback_request_group AS brg, buyback_request AS br WHERE brg.buyback_request_group_id = '" . (int)$order_id . "' AND brg.buyback_request_group_id = br.buyback_request_group_id;");
	$buyback_order_array = tep_db_fetch_array($buyback_order_result);

	// get status name
	$status_result = tep_db_query("SELECT buyback_status_name FROM buyback_status WHERE buyback_status_id = '".$buyback_order_array['buyback_status_id']."';");

	if ($status_row = tep_db_fetch_array($status_result)) {
		$buyback_order_array['buyback_status_name'] = $status_row['buyback_status_name'];
	} else {
		$buyback_order_array['buyback_status_name'] = "Unknown";
	}
	$products_query = tep_db_query("select count(*) as count, sum(buyback_amount) as sum from buyback_request where buyback_request_group_id = '" . $order_id . "'");
	$products = tep_db_fetch_array($products_query);

	$buyback_order_result_2 = tep_db_query("select * from products_description as pd, buyback_request_group as brg, buyback_request as br, products as p where br.products_id = p.products_id  and p.products_id = pd.products_id and brg.buyback_request_group_id='$order_id' and brg.buyback_request_group_id=br.buyback_request_group_id and pd.language_id='$languages_id'");
?>

<table border="0" width="100%" cellspacing="0" cellpadding="0">
	<tr>
        <td>
        	<table border="0" width="100%" cellspacing="0" cellpadding="2">
          		<tr>
            		<td class="main" colspan="2"><b><? echo sprintf(HEADING_ORDER_NUMBER, $HTTP_GET_VARS['order_id']) . ' <small>(' . $buyback_order_array['buyback_status_name'] . ')</small>'; ?></b></td>
          		</tr>
          		<tr>
            		<td class="smallText"><?=HEADING_ORDER_DATE . ' ' . tep_date_long($buyback_order_array['buyback_request_group_date'])?></td>
            		<td class="smallText" align="right"><?=HEADING_ORDER_TOTAL . ' ' . $currencies->format($products['sum'], true, $buyback_order_array['currency'], $buyback_order_array['currency_value'])?></td>
          		</tr>
        	</table>
        </td>
	</tr>
    <tr>
        <td>
        	<table border="0" width="100%" cellspacing="1" cellpadding="2" class="infoBox">
          		<tr class="infoBoxContents">
            		<td width="100%" valign="top">
            			<table border="0" width="100%" cellspacing="0" cellpadding="0">
              				<tr>
                				<td>
                					<table border="0" width="100%" cellspacing="0" cellpadding="2">
                						<tr>
											<td class="main" colspan="3"><b><?=HEADING_PRODUCTS?></b></td>
                						</tr>
									<?php	while ($row = tep_db_fetch_array($buyback_order_result_2)) {?>
                						<tr>
                							<td class="main" colspan="2"><?=$row['buyback_request_quantity'] . ' x ' . $row['products_cat_path']." > ".$row['products_name']?></td>
                							<td class="main" align="right"><?=$currencies->format($row['buyback_amount'], true, $buyback_order_array['currency'], $buyback_order_array['currency_value'])?></td>
                						</tr>
                					<?php 	} ?>
                					</table>
								</td>
							</tr>
                		</table>
                	</td>
				</tr>
			</table>
		</td>
	</tr>
	<tr>
		<td colspan="3">
			<?=tep_div_button(2, IMAGE_BUTTON_BACK,tep_href_link(FILENAME_ACCOUNT_HISTORY, tep_get_all_get_params(array('order_id')), 'SSL'), '', 'gray_button') ?>
		</td>
	</tr>
</table>

<?php
} else {
	for ($ord=0, $pen_ord=count($order_array); $ord < $pen_ord; $ord++) {
		$order_id = $order_array[$ord];
		$order = new order($order_id);

		if ($order->customer['id'] != $_SESSION['customer_id']) {
			continue;
		}
		
        # remove new order notification
        tep_db_query("DELETE FROM " . TABLE_ORDERS_NOTIFICATION . " WHERE orders_id = '" . (int) $order_id . "' AND orders_type = 'CO' AND site_id IN (0, 4, 5)");

		$pm_obj = new payment_methods($order->info['payment_methods_id']);

		$payment_info_array['auto_cancel_period']	= $pm_obj->payment_method_array->auto_cancel_period;
		$payment_info_array['confirm_complete_days'] = $pm_obj->payment_method_array->confirm_complete_days;

		$status_id = tep_not_null($order_id) ? $order->info['orders_status_id'] : $alert_status;
		$orders_type = tep_get_orders_type($status_id);

		$breadcrumb->add(NAVBAR_TITLE_1, tep_href_link(FILENAME_ACCOUNT, '', 'SSL'));
		$breadcrumb->add(NAVBAR_TITLE_2, tep_href_link(FILENAME_ACCOUNT_HISTORY, '', 'SSL'));

		if ($history_type != 'buyback') {
			$breadcrumb->add(NAVBAR_TITLE_3, tep_href_link(FILENAME_ACCOUNT_HISTORY, 'orders_type='.$orders_type, 'SSL'));
		}
		$breadcrumb->add(NAVBAR_TITLE_4, tep_href_link(FILENAME_ACCOUNT_HISTORY_INFO, ($history_type != 'buyback' ? 'orders_type='.$orders_type.'&order_id='.$order_id : ''), 'SSL'));

		$order_telephone = tep_parse_telephone($order->customer['telephone'], $order->customer['order_country_code'], 'code');
		$complete_order_telephone = $order->customer['order_country_code'] . $order_telephone;

		if (tep_info_verified_check($order->customer['id'], $complete_order_telephone, 'telephone')!=1 && $payment_info_array['confirm_complete_days'] > 0 && $order->info['orders_status_id'] == 7) { //Only orders with "Verifying" status can view the message
			$messageStack->myaccount_add('mya_order_details['.$ord.']', sprintf(ALERT_ORDER_TELEPHONE_VERIFICATION, "<a href=\"".tep_href_link(FILENAME_ACCOUNT_ACTIVATE, 'order_id='.$order_id.'&verify=phone', 'SSL')."\">".TEXT_CLICK_HERE."</a>"));
		}

		// verify order status and if it is pending, it will prompt a message to the user
		if ($order->info['orders_status_id'] == 1) {
			 $messageStack->myaccount_add('mya_order_details['.$ord.']', sprintf(ALERT_ORDER_STATUS_PENDING, "<a href=\"".tep_href_link(FILENAME_ACCOUNT_ACTIVATE, 'order_status_id='.$order_status_id.'&verify=phone', 'SSL')."\">".TEXT_CLICK_HERE."</a>"));
		}

		$display_currency_order_msg = false;
		if ($order->info['orders_status_id'] != 3 && $order->info['orders_status_id'] != 5 ) {	// No need to show Currency message when Order Status is Completed or Canceled
			foreach($order->products as $prd_num => $products) {
				if ($products['custom_products_type_id'] == 0) {
					if (isset($products['package']) && is_array($products['package'])) {
						foreach($products['package'] as $sub_prd_cnt => $sub_products) {
							if ($sub_products['qty'] > $sub_products['delivered_qty']) {
								$display_currency_order_msg = true;
								break 2;
							}
						}
					} else {
						if ($products['qty'] > $products['delivered_qty']) {
							$display_currency_order_msg = true;
							break;
						}
					}
				}
			}
		}

		if ($display_currency_order_msg) {
		 	 $messageStack->myaccount_add('mya_order_details['.$ord.']', sprintf(ALERT_ORDER_STATUS_PENDING_CURRENCY, "<a href=\"".tep_href_link(FILENAME_ACCOUNT_ACTIVATE, 'order_status_id='.$order_status_id.'&verify=phone', 'SSL')."\">".TEXT_CLICK_HERE."</a>"));
		}

		if ($messageStack->size('mya_order_details['.$ord.']') > 0) {
?>
			<div class="loginColumnBorder" style="background-color: #FFFDDB">
				<div>
					<div class="breakLine"><!-- --></div>
					<div><?=$messageStack->output('mya_order_details['.$ord.']'); ?></div>
					<div class="breakLine"><!-- --></div>
				</div>
			</div>
			<div class="breakLine"><!-- --></div>
<?
		}
		ob_start();
?>

	<table border="0" width="100%" cellspacing="5" cellpadding="0">
		<tr>
			<td>
				<div>
					<div style="display:block; padding-right:3px;">
						<div style="display:inline-block; width:100%;">
							<table width="100%" align="right" border="0" cellspacing="0" cellpadding="0">
								<tr>
									<td style="font-size:18px;width:70%;"><?=sprintf(HEADING_ORDER_NUMBER, "<b># ".$order->order_id."</b>")?></td>
									<td style="text-align:right;width:30%; font-size: 11px;" nowrap><?=sprintf(HEADING_ORDER_STATUS, "<b>".$order->info['orders_status']."</b>")?>&nbsp;&nbsp; | &nbsp;&nbsp;<?=sprintf(HEADING_ORDER_DATE, "<b>".tep_date_long($order->info['date_purchased'], DATE_FORMAT_ORDER_DATE)."</b>")?></td>
								</tr>
							</table>
						</div>
					</div>
					<div class="breakLine"><!-- --></div>

					<style type="text/css">
						#myTable td {
							padding-top: 15px;
							padding-bottom: 15px;
							/*overflow: visible;
							position: absolute;*/
						}
						#orderHistoryTable td {
							padding-top: 0px;
							padding-bottom: 10px;
							/*overflow: visible;
							position: absolute;*/
						}
					</style>
					<table id="myTable" border="0" width="100%" cellspacing="0" cellpadding="0">
						<thead>
							<tr class="boxHeader">
							<p></p>
								<th><div class="boxHeaderLeft"></div></th>
								<th width="60%"><div class="boxHeaderCenter" style="width:100%;padding-left:5px;"><font style="text-align:left;"><?=TITLE_PRODUCT_LIST?></font></div></th>
								<th><div class="boxHeaderDivider"></div></th>
								<th width="10%"><div class="boxHeaderCenter" style="width:100%;"><font style="text-align:center;"><?=TITLE_QUANTITY?>&nbsp;</font></div></th>
								<th><div class="boxHeaderDivider"></div></th>
								<th width="20%"><div class="boxHeaderCenter" style="width:100%;"><font style="text-align:right;"><?=TITLE_AMOUNT?>&nbsp;</font></div></th>
								<th><div class="boxHeaderRight"></div></th>
							</tr>
						</thead>
						<tbody>
<?php
	if (sizeof($order->products) && is_array($order->products)) {
		$prdcnt = 0;
		$show_complain_btn = false;

		include_once(DIR_WS_CLASSES . 'direct_topup.php');
		$direct_topup_obj = new direct_topup();
		foreach($order->products as $prd_num => $products) {
			$fully_delivered = false;
			$trade_mode = '';
			$opid = $products['orders_products_id'];
?>
							<tr>
								<td>&nbsp;</td>
								<td style="width:60%;padding-left:7px;vertical-align:top;">
									<b><?=$products['name']?></b>&nbsp;
<?php
			if ($products['custom_products_type_id'] == 0) {
				$extra_info_array = tep_draw_products_extra_info($products['orders_products_id']);

				echo '<div style="display:block;">'.sprintf(TEXT_CHARACTER_NAME, $extra_info_array['char_name']).'</div>';

				if(tep_not_null($extra_info_array['delivery_mode'])) {
					switch ($extra_info_array['delivery_mode']) {
						case '1':
							$trade_mode = OPTION_FACE_TO_FACE;
							break;
						case '2':
							$trade_mode = OPTION_PUT_IN_MY_ACCOUNT;
							break;
						case '3':
							$trade_mode = OPTION_BY_MAIL;
							break;
						case '4':
							$trade_mode = OPTION_OPEN_STORE;
							break;
					}
				}
				echo '<div style="display:block;">'.sprintf(TEXT_TRADE_MODE, $trade_mode).'</div>';

				$allow_order_status = array(1,2,7); // Only allow pending, verifying and processing to edit delivery info
				if (in_array($order->info['orders_status_id'], $allow_order_status)) {
					echo '<div style="display:block;"><a href="javascript:void(0);" onClick="showMe(\''.$products['orders_products_id'].'\')">'.LINK_CHANGE_DELIVERY_INFO.'</a></div>';
				}
			} else if ($products['custom_products_type_id'] == 2 && !isset($products['package'])) {
			 	$customers_top_up_info_select_sql = "	SELECT top_up_info_id, top_up_value
														FROM " . TABLE_CUSTOMERS_TOP_UP_INFO . "
														WHERE orders_products_id = '".$products['orders_products_id']."'";
				$customers_top_up_info_result_sql = tep_db_query($customers_top_up_info_select_sql);
				while ($customers_top_up_info_row = tep_db_fetch_array($customers_top_up_info_result_sql)) {
					$customers_top_up_info_array[$customers_top_up_info_row['top_up_info_id']]['value'] = $customers_top_up_info_row['top_up_value'];
				}

				if (count($customers_top_up_info_array)) {
					echo '<div style="display:block;">';

					$top_up_info_lang_select_sql = "	SELECT tuil.top_up_info_display, tuil.top_up_info_id, tui.top_up_info_key
														FROM " . TABLE_TOP_UP_INFO_LANG . " AS tuil
														INNER JOIN " . TABLE_TOP_UP_INFO . " AS tui
															ON tui.top_up_info_id = tuil.top_up_info_id
														WHERE tuil.top_up_info_id IN ('".implode("','", array_keys($customers_top_up_info_array))."')
															AND tuil.languages_id = '".$languages_id."'";
					$top_up_info_lang_result_sql = tep_db_query($top_up_info_lang_select_sql);
					while ($top_up_info_lang_row = tep_db_fetch_array($top_up_info_lang_result_sql)) {
						$customers_top_up_info_array[$top_up_info_lang_row['top_up_info_id']]['label'] = $top_up_info_lang_row['top_up_info_display'];
						$customers_top_up_info_array[$top_up_info_lang_row['top_up_info_id']]['key'] = $top_up_info_lang_row['top_up_info_key'];
					}

					foreach ($customers_top_up_info_array as $customers_top_up_info_id_loop => $customers_top_up_info_data_loop) {
						if ($customers_top_up_info_data_loop['key']=='character') {
							if (stristr($customers_top_up_info_data_loop['value'], '##')) {
								$character_tmp = explode("##", $customers_top_up_info_data_loop['value']);
								$customers_top_up_info_data_loop['value'] = $character_tmp[1];
							}
							echo $customers_top_up_info_data_loop['label'] . ': ' . $customers_top_up_info_data_loop['value'] . "<BR>";
						} else {
							echo $customers_top_up_info_data_loop['label'] . ': ' . $customers_top_up_info_data_loop['value'] . "<BR>";
						}
					}

					$top_up_status_select_sql = "	SELECT top_up_status
													FROM " . TABLE_ORDERS_TOP_UP . "
													WHERE orders_products_id = '".$products['orders_products_id']."'";
					$top_up_status_result_sql = tep_db_query($top_up_status_select_sql);
					$top_up_status_row = tep_db_fetch_array($top_up_status_result_sql);

					echo TEXT_TOP_UP_STATUS . ": " . (tep_not_null($top_up_status_row['top_up_status']) && $top_up_status_row['top_up_status'] > 0 ? $direct_topup_obj->top_up_status($top_up_status_row['top_up_status']) : '-') . "<br>";

					if (in_array($top_up_status_row['top_up_status'], array(1,3,10))) {
						$publisher_games_message_sql = "SELECT pg.publishers_games_pending_message, pg.publishers_games_reloaded_message, pg.publishers_games_failed_message
														FROM " . TABLE_PUBLISHERS_GAMES . " AS pg
														INNER JOIN " . TABLE_PUBLISHERS_PRODUCTS . " AS pp
															ON pp.publishers_games_id = pg.publishers_games_id
														WHERE pp.products_id = '".$products['id']."' ";
						$publisher_games_message_result = tep_db_query($publisher_games_message_sql);
						$publisher_games_message_row = tep_db_fetch_array($publisher_games_message_result);

						echo TEXT_TOP_UP_MESSAGE . ": ";

						switch ($top_up_status_row['top_up_status']) {
							case '1':
								echo $publisher_games_message_row['publishers_games_pending_message'];
								break;
							case '3':
								echo $publisher_games_message_row['publishers_games_reloaded_message'];
								break;
							case '10':
								echo $publisher_games_message_row['publishers_games_failed_message'];
								break;
						}
					}
					unset($customers_top_up_info_array);
					echo '</div>';
				}
			}

			// cd key button
			$cdk_num = 0;
			if ( isset($products['cdkey_info']) && sizeof($products['cdkey_info']) && in_array($order->info['orders_status_id'], array(2,3)) ) {
				if (is_array($products['package']) && sizeof($products['package'])) {
					list($package_key, $package_values) = each(array_values($products['package']));
					$total_sub_cdkey = sizeof($package_values['cdkey_info']);
				}

				$total_set_cdkey = sizeof($products['cdkey_info']);
				$total_cdk_num = $total_set_cdkey + $total_sub_cdkey;

				if ($total_cdk_num > 0) {
?>
											<div style="font-weight:bold; display:block;">
												<a onclick="getCDKeyImg_list('show', '<?=$products['orders_products_id']."_".count($products['cdkey_info']) ?>')" id="show_all_<?=$products['orders_products_id']?>" href="javascript:;"><?=TEXT_SHOW_ALL ?></a><a onclick="getCDKeyImg_list('hide', '<?=$products['orders_products_id']."_".count($products['cdkey_info']) ?>')" id="hide_all_<?=$products['orders_products_id']?>" href="javascript:;" style="display:none;"><?=TEXT_HIDE_ALL ?></a>&nbsp;&nbsp; | &nbsp;&nbsp;
												<a onclick="document.download_cdkey_<?=$products['orders_products_id']?>.submit();return false;" href="javascript:;"><?=TEXT_DOWNLOAD_ALL ?></a>
											</div>
											<div class="breakLine"></div>
<?php
				}

				if (is_array($products['cdkey_info']) && sizeof($products['cdkey_info'])) {
					echo tep_draw_form("download_cdkey_".$products['orders_products_id'], tep_href_link(FILENAME_ACCOUNT_HISTORY_INFO, tep_get_all_get_params(array())), 'post');
				    echo tep_draw_hidden_field("idents_arr", tep_array_serialize(array('cdkey_info' => $products['cdkey_info'], 'check_sum' => md5($order_id.'#'.$products['id'].'#'.$order->customer['id']))) ) . "\n";
				    echo tep_draw_hidden_field("product_id", $products['id']) . "\n";
				    echo tep_draw_hidden_field("order_id", $order_id) . "\n";
			    	echo "</form>";

					foreach ($products['cdkey_info'] as $cp_code_id => $cdkey_array) {
						$cdk_num++;
?>

											<div style="margin:5px;>
												<?=tep_draw_form('cdkeys_form_'.$cp_code_id, tep_href_link(FILENAME_ACCOUNT_HISTORY_INFO), 'post', 'SSL') ?>
													<?=tep_draw_hidden_field("cdkey_rows_".$products['orders_products_id']."_".$cdk_num, $cp_code_id, 'id="cdkey_rows_'.$products['orders_products_id'].'_'.$cdk_num.'"') ?>
													<div style="float:left; line-height:29px; vertical-align:middle; margin-right:5px;"><?=$cdk_num?>)</div>
													<?=tep_div_button(1, '&nbsp; ' . IMAGE_BUTTON_SHOW . ' &nbsp; ', 'cdkeys_form_'.$cp_code_id, 'id="show_cdkey_code_'.$cp_code_id.'" name="show_cdkey_code_'.$cp_code_id.'" style="float:left;"', 'green_button', false, 'getCDKeyImg(\'show\','.$cp_code_id.',\''.SID.'\',\''.$products['orders_products_id'].'_'.$cdk_num.'\')') ?>
													<div class="breakLine" style="line-height:1px;"> </div>
													<span style="margin-left:15px;" id="nav_sub_<?=$cp_code_id ?>"></span>
													<span style="margin-left:15px;" id="nav_<?=$products['orders_products_id']?>_<?=$cdk_num?>"></span>
													</form>
											</div>
											<div class="breakLine" style="height:10px;"></div>
<?php
					}
				}
			} else if ($products['custom_products_type_id'] == 2 && in_array($order->info['orders_status_id'], array(2,3))) {	// if is cd key bundle
				if (is_array($products['package']) && sizeof($products['package'])) {
					foreach($products['package'] as $pkgno => $sub_products) {
						$total_sub_cdkey = sizeof($sub_products['cdkey_info']);

						if ($total_sub_cdkey > 0) {
							$cdk_num = 0;

							echo tep_draw_form('download_cdkey_'.$sub_products['orders_products_id'], tep_href_link(FILENAME_ACCOUNT_HISTORY_INFO, tep_get_all_get_params(array())), 'post');
						    echo tep_draw_hidden_field("idents_arr", tep_array_serialize(array('cdkey_info' => $sub_products['cdkey_info'], 'check_sum' => md5($order_id.'#'.$sub_products['id'].'#'.$order->customer['id']))) ) . "\n";
						    echo tep_draw_hidden_field("product_id", $sub_products['id']) . "\n";
						    echo tep_draw_hidden_field("order_id", $order_id) . "\n";
					    	echo "</form>";
?>
							<div style="font-weight:bold; display:block;">
								<?=$sub_products['name']?><br>
								<a onclick="getCDKeyImg_list('show', '<?=$sub_products['orders_products_id'].'_'.$total_sub_cdkey ?>')" id="show_all_<?=$sub_products['orders_products_id']?>" href="javascript:;"><?=TEXT_SHOW_ALL ?></a><a onclick="getCDKeyImg_list('hide', '<?=$sub_products['orders_products_id']."_".$total_sub_cdkey ?>')" id="hide_all_<?=$sub_products['orders_products_id']?>" href="javascript:;" style="display:none;"><?=TEXT_HIDE_ALL ?></a>&nbsp;&nbsp; | &nbsp;&nbsp;
								<a onclick="document.download_cdkey_<?=$sub_products['orders_products_id']?>.submit();" href="javascript:;"><?=TEXT_DOWNLOAD_ALL ?></a>
							</div>
							<div class="breakLine"></div>
<?
							foreach($sub_products['cdkey_info'] as $sub_cp_code_id => $sub_cdkey_array) {
								$cdk_num++;
?>
											<div style="margin:5px;">
												<?=tep_draw_form('cdkeys_form_'.$sub_cp_code_id, tep_href_link(FILENAME_ACCOUNT_HISTORY_INFO), 'post', 'SSL') ?>
													<?=tep_draw_hidden_field("cdkey_rows_".$sub_products['orders_products_id']."_".$cdk_num, $sub_cp_code_id, 'id="cdkey_rows_'.$sub_products['orders_products_id'].'_'.$cdk_num.'"') ?>
													<div style="float:left; line-height:29px; vertical-align:middle; margin-right:5px;"><?=$cdk_num?>)</div>
													<?=tep_div_button(1, '&nbsp; ' . IMAGE_BUTTON_SHOW . ' &nbsp; ', 'cdkeys_form_'.$sub_cp_code_id, 'id="show_cdkey_code_'.$sub_cp_code_id.'" name="show_cdkey_code_'.$sub_cp_code_id.'" style="float:left;"', 'green_button', false, 'getCDKeyImg(\'show\','.$sub_cp_code_id.',\''.SID.'\',\''.$sub_products['orders_products_id'].'_'.$cdk_num.'\')') ?>
													<div class="breakLine" style="line-height:1px;"> </div>
													<span style="margin-left:15px;" id="nav_sub_<?=$sub_cp_code_id ?>"></span>
													<span style="margin-left:15px;" id="nav_<?=$sub_products['orders_products_id']?>_<?=$cdk_num?>"></span>
												</form>
											</div>
											<div class="breakLine" style="height:10px;"></div>
<?php
							}
						} else if ($sub_products['custom_content']['delivery_mode'] && $sub_products['custom_content']['delivery_mode'] == '6') {
?>
							<div style="font-weight:bold; display:block;">
								<?=$sub_products['qty']?> x <?=$sub_products['name']?><br>
							</div>
<?
						 	$customers_top_up_info_select_sql = "	SELECT top_up_info_id, top_up_value
																	FROM " . TABLE_CUSTOMERS_TOP_UP_INFO . "
																	WHERE orders_products_id = '".$sub_products['orders_products_id']."'";
							$customers_top_up_info_result_sql = tep_db_query($customers_top_up_info_select_sql);
							while ($customers_top_up_info_row = tep_db_fetch_array($customers_top_up_info_result_sql)) {
								$customers_top_up_info_array[$customers_top_up_info_row['top_up_info_id']]['value'] = $customers_top_up_info_row['top_up_value'];
							}

							if (count($customers_top_up_info_array)) {
								echo '<div style="display:block;">';

								$top_up_info_lang_select_sql = "	SELECT tuil.top_up_info_display, tuil.top_up_info_id, tui.top_up_info_key
																	FROM " . TABLE_TOP_UP_INFO_LANG . " AS tuil
																	INNER JOIN " . TABLE_TOP_UP_INFO . " AS tui
																		ON tui.top_up_info_id = tuil.top_up_info_id
																	WHERE tuil.top_up_info_id IN ('".implode("','", array_keys($customers_top_up_info_array))."')
																		AND tuil.languages_id = '".$languages_id."'";
								$top_up_info_lang_result_sql = tep_db_query($top_up_info_lang_select_sql);
								while ($top_up_info_lang_row = tep_db_fetch_array($top_up_info_lang_result_sql)) {
									$customers_top_up_info_array[$top_up_info_lang_row['top_up_info_id']]['label'] = $top_up_info_lang_row['top_up_info_display'];
									$customers_top_up_info_array[$top_up_info_lang_row['top_up_info_id']]['key'] = $top_up_info_lang_row['top_up_info_key'];
								}

								foreach ($customers_top_up_info_array as $customers_top_up_info_id_loop => $customers_top_up_info_data_loop) {
									if ($customers_top_up_info_data_loop['key']=='character') {
										if (stristr($customers_top_up_info_data_loop['value'], '##')) {
											$character_tmp = explode("##", $customers_top_up_info_data_loop['value']);
											$customers_top_up_info_data_loop['value'] = $character_tmp[1];
										}
										echo $customers_top_up_info_data_loop['label'] . ': ' . $customers_top_up_info_data_loop['value'] . "<BR>";
									} else {
										echo $customers_top_up_info_data_loop['label'] . ': ' . $customers_top_up_info_data_loop['value'] . "<BR>";
									}
								}

								$top_up_status_select_sql = "	SELECT top_up_status
																FROM " . TABLE_ORDERS_TOP_UP . "
																WHERE orders_products_id = '".$sub_products['orders_products_id']."'";
								$top_up_status_result_sql = tep_db_query($top_up_status_select_sql);
								$top_up_status_row = tep_db_fetch_array($top_up_status_result_sql);

								echo TEXT_TOP_UP_STATUS . ": " . $direct_topup_obj->top_up_status($top_up_status_row['top_up_status']) . "<br>";

								if (in_array($top_up_status_row['top_up_status'], array(1,3,10))) {
									$publisher_games_message_sql = "SELECT pg.publishers_games_pending_message, pg.publishers_games_reloaded_message, pg.publishers_games_failed_message
																	FROM " . TABLE_PUBLISHERS_GAMES . " AS pg
																	INNER JOIN " . TABLE_PUBLISHERS_PRODUCTS . " AS pp
																		ON pp.publishers_games_id = pg.publishers_games_id
																	WHERE pp.products_id = '".$sub_products['id']."' ";
									$publisher_games_message_result = tep_db_query($publisher_games_message_sql);
									$publisher_games_message_row = tep_db_fetch_array($publisher_games_message_result);

									echo TEXT_TOP_UP_MESSAGE . ": ";

									switch ($top_up_status_row['top_up_status']) {
										case '1':
											echo $publisher_games_message_row['publishers_games_pending_message'];
											break;
										case '3':
											echo $publisher_games_message_row['publishers_games_reloaded_message'];
											break;
										case '10':
											echo $publisher_games_message_row['publishers_games_failed_message'];
											break;
									}
								}
								unset($customers_top_up_info_array);
								echo '</div><div class="breakLine"></div>';
							}
						}
					}
				}
			} else if ($products['custom_products_type_id'] == 4) { // HLA
				$hla_char_select_sql = "SELECT products_hla_characters_name
										FROM " . TABLE_ORDERS_PRODUCTS_ITEM . "
										WHERE orders_products_id = '" . tep_db_input($products['orders_products_id']) . "'";
				$hla_char_result_sql = tep_db_query($hla_char_select_sql);
				if (tep_db_num_rows($hla_char_result_sql)) {
					echo '	<div style="display:block;">';

					while ($hla_char_row = tep_db_fetch_array($hla_char_result_sql)) {
						echo '&raquo;&nbsp;' . $hla_char_row['products_hla_characters_name'] . '<br />';
					}

					echo '	</div>
							<div class="breakLine"></div>';
				}

				if (in_array($order->info['orders_status_id'], array(2,3))) {
					$hla_info_stage_1 = tep_draw_products_extra_info($products['orders_products_id'], 'hla_info_stage_1');

					if (tep_not_null($hla_info_stage_1)) {
?>
				<div style="display:block;">
					<?=tep_div_button(1, '&nbsp; ' . IMAGE_BUTTON_SHOW . ' &nbsp; ', '', 'id="show_hla_info_' . $products['orders_products_id'] . '" name="show_hla_info_' . $products['orders_products_id'] . '" style="float:left;"', 'green_button', false, 'getHLAInfo(\'show\',' . $products['orders_products_id'] . ')') ?>
				</div>
<?php
					}
				}
			} else {
				$show_complain_btn = true;
			}

			// progress bar
			$cat_cfg_array = tep_get_cfg_setting($products['id'], 'product');

										if ($cat_cfg_array['SHOW_CUSTOMER_CP_PROGRESS_STATUS'] == 'true' && in_array($order->info['orders_status_id'], array(2,3)) && $products['custom_products_type_id'] == 1) {
											if (tep_get_profiler($products['orders_products_id'])) {
												$orders_custom_products_number_select_query = "SELECT orders_custom_products_number FROM " . TABLE_ORDERS_CUSTOM_PRODUCTS . " WHERE orders_products_id ='" . (int)$products['orders_products_id'] . "' ";
												$orders_custom_products_number_result 		= tep_db_query($orders_custom_products_number_select_query);
												$orders_custom_products_number_array 		= tep_db_fetch_array($orders_custom_products_number_result);
												$orders_custom_products_number 				= $orders_custom_products_number_array['orders_custom_products_number'];

												echo tep_div_button(2, '&nbsp; ' . IMAGE_BUTTON_CHARACTERS_PROFILER . ' &nbsp; ', tep_href_link(FILENAME_CHARACTER_PROFILE, 'action=char_info&id=' . $order_id . '_' . $orders_custom_products_number), '', 'green_button', false);
											}

											$custom_product = new custom_product($products['orders_products_id']);

											if ($custom_product->info['bar_available']) {
												// **** power leveling ***
												if (tep_not_null($custom_product->info['desired_level'])) {

													$each_lvl_unit	= 20 / $custom_product->info['desired_level'];
													$total_lvl_unit	= ($custom_product->info['current_level'] * $each_lvl_unit);

													$inner_level_bar= '';
													for ($lvl=0; $lvl < $total_lvl_unit; $lvl++) {
														$w = (($total_lvl_unit - $lvl) < 1) ? intval(((205 - (20*2))/20) * ($total_lvl_unit - $lvl)) : ((205 - (20*2))/20);
														$inner_level_bar .= '<div style="display:block; float:left; margin:1px; background-color:#004B91; width:'.$w.'px; height:18px;"> </div>';
													}
													if ($total_lvl_unit==0) {
														$inner_level_bar .= '<div style="display:block; float:left; margin:1px; width:'.((205 - (20*2))/20).'px; height:18px;"> </div>';
													}
												?>
												<div style="height:40px; display:block; padding:15px 5px 5px 15px;">
													<div style="display:block; float:left; vertical-align:middle;">
														<div style="display:block; float:left; border:solid 1px #E1E1E1; padding-left:1px; background-color:#FFFFFF; width:205px; line-height:20px; vertical-align:middle;">
															<?php print $inner_level_bar;	?>
														</div>
														<div style="display:block; float:left; padding-left:6px; vertical-align:middle; line-height:20px;"><?=sprintf(TEXT_CURRENT_LEVEL, "<b>".$custom_product->info['current_level']."</b>")?></div>
													</div>

													<div style="display:block; float:left; width:205px;">
														<div style="display:inline; padding-top:3px; float:left;"><?=$custom_product->info['start_level']?></div>
														<div style="display:inline; padding-top:3px; float:right;"><?=TEXT_LEVEL."&nbsp;".$custom_product->info['desired_level']?></div>
													</div>
												</div>
												<?php
												}

												// *** time spent ***
												if ($custom_product->info['total_time'] > 0) {
													$each_time_unit	= 20 / $custom_product->info['total_time'];
													$total_time_unit= ($custom_product->info['time_pass'] * $each_time_unit);

													$inner_time_bar = '';
													for ($time=0; $time < $total_time_unit; $time++) {
														$w = (($total_time_unit - $time) < 1) ? intval(((205 - (20*2))/20) * ($total_time_unit - $time)) : ((205 - (20*2))/20);
														$inner_time_bar .= '<div style="display:block; float:left; margin:1px; background-color:#004B91; width:'.$w.'px; height:18px;"> </div>';
													}
													if ($total_time_unit==0) {
														$inner_time_bar .= '<div style="display:block; float:left; margin:1px; width:'.((205 - (20*2))/20).'px; height:18px;"> </div>';
													}
												?>
												<div style="height:40px; display:block; padding:15px 5px 5px 15px;">
													<div style="display:block; float:left; vertical-align:middle;">
														<div style="display:block; float:left; border:solid 1px #E1E1E1; padding-left:1px; background-color:#FFFFFF; width:205px; line-height:20px; vertical-align:middle;">
															<?php print $inner_time_bar;	?>
														</div>
														<div style="display:block; float:left; padding-left:6px; vertical-align:middle; line-height:20px;"><?=sprintf(TEXT_HOURS_PASSED, "<b>".$custom_product->info['time_pass']."</b>")?></div>
													</div>

													<div style="display:block; float:left; width:205px;">
														<div style="display:inline; padding-top:3px; float:left;"><?="0"?></div>
														<div style="display:inline; padding-top:3px; float:right;"><?=$custom_product->info['total_time']."&nbsp;".TEXT_HOURS?></div>
													</div>
												</div>
												<?php
												}
											}
										}

										if ($cat_cfg_array['SHOW_CUSTOMER_CP_PROGRESS_STATUS'] == 'true' && in_array($order->info['orders_status_id'], array(2,3)) && $products['custom_products_type_id'] == 0) {
											// *** gold delivered ***
											if (sizeof($products['package'])) {
												foreach($products['package'] as $sub_prd_cnt => $sub_products) {
													$each_product_unit	= 20 / $sub_products['qty'];
													$total_product_unit= ($sub_products['qty_info']['delivered_quantity'] * $each_product_unit);

													$inner_product_bar = '';
													if ($total_product_unit > 0) {
														for ($bar_cnt=0; $bar_cnt < $total_product_unit; $bar_cnt++) {
															$w = (($total_product_unit - $bar_cnt) < 1) ? intval(((205 - (20*2))/20) * ($total_product_unit - $bar_cnt)) : ((205 - (20*2))/20);
															$inner_product_bar .= '<div style="display:block; float:left; margin:1px; background-color:#004B91; width:'.$w.'px; height:18px;"> </div>';
														}
													} else {
														$inner_product_bar .= '<div style="display:block; float:left; margin:1px; width:'.((205 - (20*2))/20).'px; height:18px;"> </div>';
													}

													if($sub_products['qty_info']['delivered_quantity'] == $sub_products['qty']) {
														$fully_delivered =  true;
													}
													?>
													<div style="height:40px; display:block; padding:15px 5px 5px 15px;">
														<div style="display:block; float:left; vertical-align:middle;">
															<div style="display:block; float:left; border:solid 1px #E3E3E3; padding-left:1px; background-color:#FFFFFF; width:205px; line-height:20px; vertical-align:middle;">
																<?php print $inner_product_bar;	?>
															</div>
															<div style="display:block; float:left; padding-left:6px; vertical-align:middle; line-height:20px;"><?=sprintf(TEXT_TOTAL_DELIVERED_GOLD, "<b>".intval($sub_products['qty_info']['delivered_quantity'])."&nbsp;".TEXT_GOLD."</b>")?></div>
														</div>

														<div style="display:block; float:left; width:205px;">
															<div style="display:inline; padding-top:3px; float:left;"><?="0"?></div>
															<div style="display:inline; padding-top:3px; float:right;"><?=$sub_products['qty']."&nbsp;".TEXT_GOLD?></div>
														</div>
													</div>
<?												}
											} else if($products['is_compensate'] == 1) {
												$each_product_unit	= 20 / $products['qty'];
												$total_product_unit= ($products['delivered_qty'] * $each_product_unit);

												$inner_product_bar = '';
												if ($total_product_unit > 0) {
													for ($bar_cnt=0; $bar_cnt < $total_product_unit; $bar_cnt++) {
														$w = (($total_product_unit - $bar_cnt) < 1) ? intval(((205 - (20*2))/20) * ($total_product_unit - $bar_cnt)) : ((205 - (20*2))/20);
														$inner_product_bar .= '<div style="display:block; float:left; margin:1px; background-color:#004B91; width:'.$w.'px; height:18px;"> </div>';
													}
												} else {
													$inner_product_bar .= '<div style="display:block; float:left; margin:1px; width:'.((205 - (20*2))/20).'px; height:18px;"> </div>';
												}

												if($products['delivered_qty'] == $products['qty']) {
													$fully_delivered =  true;
												}
?>
													<div style="height:40px; display:block; padding:15px 5px 5px 15px;">
														<div style="display:block; float:left; vertical-align:middle;">
															<div style="display:block; float:left; border:solid 1px #E3E3E3; padding-left:1px; background-color:#FFFFFF; width:205px; line-height:20px; vertical-align:middle;">
																<?php print $inner_product_bar;	?>
															</div>
															<div style="display:block; float:left; padding-left:6px; vertical-align:middle; line-height:20px;"><?=sprintf(TEXT_TOTAL_DELIVERED_GOLD, "<b>".intval($products['delivered_qty'])."&nbsp;".TEXT_GOLD."</b>")?></div>
														</div>

														<div style="display:block; float:left; width:205px;">
															<div style="display:inline; padding-top:3px; float:left;"><?="0"?></div>
															<div style="display:inline; padding-top:3px; float:right;"><?=$products['qty']."&nbsp;".TEXT_GOLD?></div>
														</div>
													</div>
<?
											}
										}
?>
								</td>

								<td>&nbsp;</td>
								<td style="text-align:center; vertical-align:top;"><?=$products['qty']?>&nbsp;</td>
								<td>&nbsp;</td>
								<td style="text-align:right; vertical-align:top;">
									<?=$currencies->format($products['price'], true, $order->info['currency'], $order->info['currency_value'])?>&nbsp;
									<?php $refunded_orders_products_id_array = tep_get_products_id_refunded(); ?>
									<?=(is_array($refunded_orders_products_id_array) && sizeof($refunded_orders_products_id_array) ? (in_array($products['orders_products_id'], $refunded_orders_products_id_array) ? '<div class="breakLine" style="height:20px;"></div><div style="line-height:35px; width:100px; background-color:#FF0000; float:right; color:#ffffff; font-weight:bold; vertical-align:middle; text-align:center;">'.TEXT_REFUNDED.'</div>' : '') : '') ?>
								</td>
								<td>&nbsp;</td>
							</tr>
<?php
if ($products['custom_products_type_id'] == 0) {
	/*
		status 1 = Pending
		status 7 = Verifying
		status 2 = Processing
		status 3 = Completed
		status 5 = Canceled
		status 8 = On Hold
	*/
	switch($status_id) {
		case '2':
		case '3':
		case '5':
		case '8':
			$status_checking_access = true;
		break;
		default:
			$status_checking_access = false;
		break;
	}

	$pwl_not_in_process = tep_order_product_pending_delivery(2, 1, $_SESSION['customer_id']) > 0 ? false : true;

	if ($status_checking_access) {
		$eta_button = '';
		$now_time = strtotime(date("Y-m-d H:i:s"));
		$display_accumulate = 'none';
		$customer_email_address = tep_get_customers_email($_SESSION['customer_id']);

		if (!tep_direct_open_buyback($extra_info_array['delivery_mode']) && $fully_delivered == false) {
			$accumulated_buyback_time = '';

			if ($products['is_compensate'] != 1) {
				$countdown_select_sql = "	SELECT op_eta.expiry_hour, op_eta.start_time, op.orders_products_id, op_eta.total_buyback_time
											FROM " . TABLE_ORDERS_PRODUCTS_ETA . " AS op_eta
											INNER JOIN " . TABLE_ORDERS_PRODUCTS . " AS op
												ON (op_eta.orders_products_id = op.orders_products_id)
											WHERE op.parent_orders_products_id  = '".(int)$products['orders_products_id']."'";
			} else {
				$countdown_select_sql = "	SELECT NOW() as nowtime, expiry_hour, start_time, orders_products_id, total_buyback_time
											FROM " . TABLE_ORDERS_PRODUCTS_ETA . "
											WHERE orders_products_id = '".(int)$products['orders_products_id']."'";
			}

			$countdown_result_sql =	tep_db_query($countdown_select_sql);
			if ($countdown_row = tep_db_fetch_array($countdown_result_sql)) {
				if ($countdown_row['expiry_hour'] != 0) {
					$expiry_hour = tep_not_null($countdown_row['expiry_hour']) ? $countdown_row['expiry_hour'] : 0;
					$start_time = tep_not_null($countdown_row['start_time']) ? $countdown_row['start_time'] : date("Y-m-d H:i:s");

					$eta_time = strtotime($start_time." +".$expiry_hour." hour");

					$start_time_sec = tep_day_diff($countdown_row['start_time'], date("Y-m-d H:i:s"), 'sec');
					$start_time_sec = tep_not_null($start_time_sec) ? $start_time_sec : 0;

					if ($eta_time > $now_time) {
						// Buyback opening
						$total_buyback_time = 0;

						$countdown_js = '<script>';
						$countdown_js .= 'jQuery(function () {
												var currentTime_'.$opid.' = new Date();

												var eta_hour_'.$opid.' = currentTime_'.$opid.'.getHours();
												var eta_min_'.$opid.' = currentTime_'.$opid.'.getMinutes();
												var eta_sec_'.$opid.' = currentTime_'.$opid.'.getSeconds();
												var eta_mon_'.$opid.' = currentTime_'.$opid.'.getMonth() + 1;
												var eta_date_'.$opid.' = currentTime_'.$opid.'.getDate();
												var eta_year_'.$opid.' = currentTime_'.$opid.'.getFullYear();

												var s_yrs_'.$opid.' = 0;
												var s_mths_'.$opid.' = 0;
												var s_days_'.$opid.' = 0;
												var s_hrs_'.$opid.' = 0;
												var s_mins_'.$opid.' = 0;
												var s_secs_'.$opid.' = 0;

												var expiry_hour_'.$opid.' = '.$expiry_hour.';
												var start_time_'.$opid.' = '.$start_time_sec.';

												s_yrs_'.$opid.'  = date("Y", mktime(eval(eta_hour_'.$opid.' + "+" + expiry_hour_'.$opid.'), eta_min_'.$opid.', eval(eta_sec_'.$opid.' + "-" + start_time_'.$opid.'), eta_mon_'.$opid.', eta_date_'.$opid.', eta_year_'.$opid.'));
												s_mths_'.$opid.' = date("m", mktime(eval(eta_hour_'.$opid.' + "+" + expiry_hour_'.$opid.'), eta_min_'.$opid.', eval(eta_sec_'.$opid.' + "-" + start_time_'.$opid.'), eta_mon_'.$opid.', eta_date_'.$opid.', eta_year_'.$opid.'));
												s_days_'.$opid.' = date("d", mktime(eval(eta_hour_'.$opid.' + "+" + expiry_hour_'.$opid.'), eta_min_'.$opid.', eval(eta_sec_'.$opid.' + "-" + start_time_'.$opid.'), eta_mon_'.$opid.', eta_date_'.$opid.', eta_year_'.$opid.'));
												s_hrs_'.$opid.'  = date("H", mktime(eval(eta_hour_'.$opid.' + "+" + expiry_hour_'.$opid.'), eta_min_'.$opid.', eval(eta_sec_'.$opid.' + "-" + start_time_'.$opid.'), eta_mon_'.$opid.', eta_date_'.$opid.', eta_year_'.$opid.'));
												s_mins_'.$opid.' = date("i", mktime(eval(eta_hour_'.$opid.' + "+" + expiry_hour_'.$opid.'), eta_min_'.$opid.', eval(eta_sec_'.$opid.' + "-" + start_time_'.$opid.'), eta_mon_'.$opid.', eta_date_'.$opid.', eta_year_'.$opid.'));
												s_secs_'.$opid.' = date("s", mktime(eval(eta_hour_'.$opid.' + "+" + expiry_hour_'.$opid.'), eta_min_'.$opid.', eval(eta_sec_'.$opid.' + "-" + start_time_'.$opid.'), eta_mon_'.$opid.', eta_date_'.$opid.', eta_year_'.$opid.'));
												countdown_timer("Countdown_eta_'.$products['orders_products_id'].'", "counter_box_'.$products['orders_products_id'].'", on_hold_buyback_order, s_yrs_'.$opid.', s_mths_'.$opid.', s_days_'.$opid.', s_hrs_'.$opid.', s_mins_'.$opid.', s_secs_'.$opid.');';
						// Count Up with the accumulated open buyback time
						$total_buyback_time = tep_day_diff($start_time, date("Y-m-d H:i:s"), 'sec');
						if (($countdown_row['total_buyback_time'] > 0) || ($total_buyback_time > 0)) {
							$total_buyback_time += $countdown_row['total_buyback_time'];

							$countdown_js .= '
										        var currentTime_'.$opid.' = new Date();

												var cu_hour_'.$opid.' = currentTime_'.$opid.'.getHours();
												var cu_min_'.$opid.' = currentTime_'.$opid.'.getMinutes();
												var cu_sec_'.$opid.' = currentTime_'.$opid.'.getSeconds();
												var cu_mon_'.$opid.' = currentTime_'.$opid.'.getMonth() + 1;
												var cu_date_'.$opid.' = currentTime_'.$opid.'.getDate();
												var cu_year_'.$opid.' = currentTime_'.$opid.'.getFullYear();

												var cu_yrs_'.$opid.' = 0;
												var cu_mths_'.$opid.' = 0;
												var cu_days_'.$opid.' = 0;
												var cu_hrs_'.$opid.' = 0;
												var cu_mins_'.$opid.' = 0;
												var cu_secs_'.$opid.' = 0;

												var total_buyback_time_'.$opid.' = '.$total_buyback_time.';

												total_buyback_time_'.$opid.' = total_buyback_time_'.$opid.' == 0 ? 1 : total_buyback_time_'.$opid.';

												cu_yrs_'.$opid.'  = date("Y", mktime(cu_hour_'.$opid.', cu_min_'.$opid.', eval(cu_sec_'.$opid.' + "-" + total_buyback_time_'.$opid.'), cu_mon_'.$opid.', cu_date_'.$opid.', cu_year_'.$opid.'));
												cu_mths_'.$opid.' = date("m", mktime(cu_hour_'.$opid.', cu_min_'.$opid.', eval(cu_sec_'.$opid.' + "-" + total_buyback_time_'.$opid.'), cu_mon_'.$opid.', cu_date_'.$opid.', cu_year_'.$opid.'));
												cu_days_'.$opid.' = date("d", mktime(cu_hour_'.$opid.', cu_min_'.$opid.', eval(cu_sec_'.$opid.' + "-" + total_buyback_time_'.$opid.'), cu_mon_'.$opid.', cu_date_'.$opid.', cu_year_'.$opid.'));
												cu_hrs_'.$opid.'  = date("H", mktime(cu_hour_'.$opid.', cu_min_'.$opid.', eval(cu_sec_'.$opid.' + "-" + total_buyback_time_'.$opid.'), cu_mon_'.$opid.', cu_date_'.$opid.', cu_year_'.$opid.'));
												cu_mins_'.$opid.' = date("i", mktime(cu_hour_'.$opid.', cu_min_'.$opid.', eval(cu_sec_'.$opid.' + "-" + total_buyback_time_'.$opid.'), cu_mon_'.$opid.', cu_date_'.$opid.', cu_year_'.$opid.'));
												cu_secs_'.$opid.' = date("s", mktime(cu_hour_'.$opid.', cu_min_'.$opid.', eval(cu_sec_'.$opid.' + "-" + total_buyback_time_'.$opid.'), cu_mon_'.$opid.', cu_date_'.$opid.', cu_year_'.$opid.'));

										        var start_time_'.$opid.' = new Date();

												start_time_'.$opid.' = new Date(cu_yrs_'.$opid.', cu_mths_'.$opid.' - 1, cu_days_'.$opid.', cu_hrs_'.$opid.', cu_mins_'.$opid.', cu_secs_'.$opid.');
												jQuery("#open_time_'.$products['orders_products_id'].'").countdown({since: start_time_'.$opid.', compact: true, format: "HMS", description: ""});
												jQuery("#open_time_msg_'.$products['orders_products_id'].'").css("display", "block");
											';
						}

						$countdown_js .= '  });';
						$countdown_js .= '</script>';
						$eta_button = '<span id="reset_button_'.$products['orders_products_id'].'" style="display:none;"><a href="javascript:void(0);" onClick="update_eta(\''.$products['orders_products_id'].'\', \'reset\');"><span><font>'.BUTTON_IM_ONLINE_NOW.'</font></span></a></span>';
						$eta_button .= '<span id="extend_button_'.$products['orders_products_id'].'"><a href="javascript:void(0);" onClick="update_eta(\''.$products['orders_products_id'].'\', \'extend\');"><span><font>'.BUTTON_EXTEND_TIME.'</font></span></a></span>';
						echo $countdown_js;
					} else {
						// Buyback closing

						// Add record to TABLE_ORDERS_PRODUCTS_HISTORY
						$insert_record_data_sql['changed_by'] = $customer_email_address;
						$insert_record_data_sql['date_added'] = 'now()';
						$insert_record_data_sql['last_updated'] = 'now()';
						$insert_record_data_sql['orders_id'] = $order_id;
						$insert_record_data_sql['orders_products_id'] = $countdown_row['orders_products_id'];
						$insert_record_data_sql['orders_products_history_record'] = 'TEXT_BUYBACK_CLOSED:~:TEXT_BUYBACK_CLOSED_DURATION_TIME_OUT';

						tep_db_perform(TABLE_ORDERS_PRODUCTS_HISTORY, $insert_record_data_sql, 'insert');

						$update_purchase_eta_data_sql = array('orders_products_purchase_eta' => '-999');

						if ($products['is_compensate'] != 1) {
							tep_db_perform(TABLE_ORDERS_PRODUCTS, $update_purchase_eta_data_sql, 'update', ' orders_products_id = "' . (int)$products['orders_products_id'] . '"');
							tep_db_perform(TABLE_ORDERS_PRODUCTS, $update_purchase_eta_data_sql, 'update', ' orders_products_id = "' . (int)$countdown_row['orders_products_id'] . '"');
						} else {
							tep_db_perform(TABLE_ORDERS_PRODUCTS, $update_purchase_eta_data_sql, 'update', ' orders_products_id = "' . (int)$products['orders_products_id'] . '"');
						}

						$closed_buyback_data_sql = array('expiry_hour' => '0');
						tep_db_perform(TABLE_ORDERS_PRODUCTS_ETA, $closed_buyback_data_sql, 'update', ' orders_products_id = "' . (int)$countdown_row['orders_products_id'] . '"');

						if ($pwl_not_in_process && tep_not_null($trade_mode)) {
							$eta_button = '<span id="reset_button_'.$products['orders_products_id'].'"><a href="javascript:void(0);" onClick="update_eta(\''.$products['orders_products_id'].'\', \'reset\');"><span><font>'.BUTTON_IM_ONLINE_NOW.'</font></span></a></span>';
							$eta_button .= '<span id="extend_button_'.$products['orders_products_id'].'" style="display:none;"><a href="javascript:void(0);" onClick="update_eta(\''.$products['orders_products_id'].'\', \'extend\');"><span><font>'.BUTTON_EXTEND_TIME.'</font></span></a></span>';
						}
					}

				} else {
					if ($pwl_not_in_process && tep_not_null($trade_mode)) {
						$display_accumulate = 'block';
						$total_buyback_time = $countdown_row['total_buyback_time'];

						$accumulated_buyback_time = tep_sec_to_daytime($total_buyback_time);

						$eta_button = '<span id="reset_button_'.$products['orders_products_id'].'"><a href="javascript:void(0);" onClick="update_eta(\''.$products['orders_products_id'].'\', \'reset\');"><span><font>'.BUTTON_IM_ONLINE_NOW.'</font></span></a></span>';
						$eta_button .= '<span id="extend_button_'.$products['orders_products_id'].'" style="display:none;"><a href="javascript:void(0);" onClick="update_eta(\''.$products['orders_products_id'].'\', \'extend\');"><span><font>'.BUTTON_EXTEND_TIME.'</font></span></a></span>';
					}
				}
			} else {
				if ($pwl_not_in_process && tep_not_null($trade_mode)) {
					$eta_button = '<span id="reset_button_'.$products['orders_products_id'].'"><a href="javascript:void(0);" onClick="update_eta(\''.$products['orders_products_id'].'\', \'reset\');"><span><font>'.BUTTON_IM_ONLINE_NOW.'</font></span></a></span>';
					$eta_button .= '<span id="extend_button_'.$products['orders_products_id'].'" style="display:none;"><a href="javascript:void(0);" onClick="update_eta(\''.$products['orders_products_id'].'\', \'extend\');"><span><font>'.BUTTON_EXTEND_TIME.'</font></span></a></span>';
				}
			}
		}

		$eta_array = array();
		$how_many_eta_days = 10;
		for($eta_days = 1; $eta_days <= $how_many_eta_days; $eta_days++) {
			$eta_array[] = array('id' => $eta_days,'text' => $eta_days);
		}

		$history_html = $history_year = $history_month = $history_day = $history_hour = $history_min = $history_sec = '';

		if ($products['is_compensate'] != 1) {
			$get_history_select_sql = " SELECT NOW() as nowtime, oph.orders_products_history_id, oph.buyback_request_group_id, oph.delivered_amount, oph.delivered_character, oph.date_added, oph.date_confirm_delivered, oph.dispute_comment, oph.received, oph.rolled_back, oph.orders_products_history_record
										FROM " . TABLE_ORDERS_PRODUCTS_HISTORY . " AS oph
										INNER JOIN " . TABLE_ORDERS_PRODUCTS . " AS op
											ON (oph.orders_products_id = op.orders_products_id)
										WHERE op.parent_orders_products_id = '".(int)$products['orders_products_id']."'
										ORDER BY oph.date_added DESC";
		} else {
			$get_history_select_sql = " SELECT NOW() as nowtime, orders_products_history_id, buyback_request_group_id, delivered_amount, delivered_character, date_added, date_confirm_delivered, dispute_comment, received, rolled_back, orders_products_history_record
										FROM " . TABLE_ORDERS_PRODUCTS_HISTORY . "
										WHERE orders_products_id = '".(int)$products['orders_products_id']."'
										ORDER BY date_added DESC";
		}

		$get_history_result_sql =	tep_db_query($get_history_select_sql);
		while ($get_history_row = tep_db_fetch_array($get_history_result_sql)) {
			$rolled_back = $get_history_row['rolled_back'];
			$date_added = $get_history_row['date_added'];
			$date_confirm_delivered = $get_history_row['date_confirm_delivered'];
			$buyback_request_group_id = $get_history_row['buyback_request_group_id'];
			$op_history_id = $get_history_row['orders_products_history_id'];
			$record = $get_history_row['orders_products_history_record'];
			$record_text = '';

			// For showing the data and time
			$date_and_time = explode(" ", $date_added);
			$history_date = isset($date_and_time[0]) ? $date_and_time[0] : '';
			$history_time = isset($date_and_time[1]) ? $date_and_time[1] : '';

			$history_date_arr = explode("-", $history_date);
			$history_time_arr = explode(":", $history_time);

			$history_year = substr($history_date_arr[0], -2);
			$history_month = $history_date_arr[1];
			$history_day = $history_date_arr[2];

			$dates = $history_day.'-'.$history_month.'-'.$history_year;

			$history_hour = $history_time_arr[0];
			$history_min = $history_time_arr[1];
			$history_sec = $history_time_arr[2];

			$times = date('g:i A', mktime($history_hour, $history_min, $history_sec));

			$row_style = 'padding: 0px 10px;';
			if (tep_not_null($record)) {
				$received = false;
			} else if (!tep_not_null($get_history_row['received'])) {
				if ($rolled_back) {
					$received = false;
					$row_style = 'padding: 0px 10px;';
				} else {
					$confirmation_eta = strtotime($date_confirm_delivered);
					$current_datetime = strtotime($get_history_row['nowtime']);

					if ($confirmation_eta > $current_datetime) {
						$received = false;
					} else {
						$received = true;
						$dispute = false;

						$row_style = 'padding: 0px 10px;color:#999999;';

						$update_history_data_sql['changed_by'] = $customer_email_address;
						$update_history_data_sql['received'] = '1';
						$update_history_data_sql['last_updated'] = 'now()';
						tep_db_perform(TABLE_ORDERS_PRODUCTS_HISTORY, $update_history_data_sql, 'update', ' orders_products_history_id = "'.(int)$op_history_id.'" ');

						// if not extra inventory
						if (tep_not_null($buyback_request_group_id)) {
							$buyback_status_history_update_arr = array('set_as_buyback_remarks' => '0');
							tep_db_perform(TABLE_BUYBACK_STATUS_HISTORY, $buyback_status_history_update_arr, 'update', "buyback_request_group_id= '" . (int)$buyback_request_group_id . "'");

							$buyback_request_group_update_arr = array('buyback_status_id' => '3');
							tep_db_perform(TABLE_BUYBACK_REQUEST_GROUP, $buyback_request_group_update_arr, 'update', "buyback_request_group_id= '" . (int)$buyback_request_group_id . "'");

							$buyback_order_select_sql = "	SELECT brg.customers_id, brg.buyback_request_group_site_id, ci.customer_info_selected_language_id, br.products_id, br.buyback_quantity_received, br.buyback_request_quantity, br.buyback_unit_price, br.buyback_quantity_confirmed, brg.buyback_request_group_date
															FROM " . TABLE_BUYBACK_REQUEST_GROUP . " AS brg
															INNER JOIN ".TABLE_BUYBACK_REQUEST." AS br
																ON (brg.buyback_request_group_id = br.buyback_request_group_id)
															LEFT JOIN " . TABLE_CUSTOMERS_INFO . " AS ci
																ON (brg.customers_id = ci.customers_info_id)
															WHERE brg.buyback_request_group_id = '" . (int)$buyback_request_group_id . "'";
							$buyback_order_result_sql = tep_db_query($buyback_order_select_sql);
							if ($buyback_order_row = tep_db_fetch_array($buyback_order_result_sql)) {
								$buyback_request_group_site_id = $buyback_order_row['buyback_request_group_site_id'];
								$customer_info_selected_language_id = $buyback_order_row['customer_info_selected_language_id'];

								$buyback_history_data_array = array('buyback_request_group_id' => $buyback_request_group_id,
																   	'buyback_status_id' => 3,
																   	'date_added' => 'now()',
																   	'customer_notified' => 0,
																   	'comments' => '',
																   	'set_as_buyback_remarks' => 0,
																   	'changed_by' => $customer_email_address
																	);
								tep_db_perform(TABLE_BUYBACK_STATUS_HISTORY, $buyback_history_data_array);

								$buyback_history_data_array = array('buyback_request_group_id' => $buyback_request_group_id,
																   	'buyback_status_id' => 0,
																   	'date_added' => 'now()',
																   	'customer_notified' => 1,
																   	'comments' => COMMENTS_PROCESSING_TO_COMPLETED,
																   	'set_as_buyback_remarks' => 1,
																   	'changed_by' => 'system'
																	);
								tep_db_perform(TABLE_BUYBACK_STATUS_HISTORY, $buyback_history_data_array);

								// Updating the product average buyback price
								$cron_pending_credit_mature_period = 0;

								$current_buyback_info_select_sql = "SELECT products_buyback_quantity, products_buyback_price FROM " . TABLE_PRODUCTS . " WHERE products_id = '" . tep_db_input($buyback_order_row['products_id']) . "'";
								$current_buyback_info_result_sql = tep_db_query($current_buyback_info_select_sql);
								if ($current_buyback_info_row = tep_db_fetch_array($current_buyback_info_result_sql)) {
									$prev_buyback_qty = (int)$current_buyback_info_row["products_buyback_quantity"];
									$prev_buyback_price = (double)$current_buyback_info_row["products_buyback_price"];

									$order_buyback_qty = (int)$buyback_order_row['buyback_quantity_received'];
									// Already take into account the $order_buyback_qty

									$unit_price = $buyback_order_row['buyback_unit_price'];

									$received_quantity = (int)$buyback_order_row['buyback_quantity_received'];
									$total_buyback_price = $unit_price * ($received_quantity > $buyback_order_row['buyback_quantity_confirmed'] ? $buyback_order_row['buyback_quantity_confirmed'] : $received_quantity);

									$new_buyback_qty = $prev_buyback_qty + $order_buyback_qty;

									if ($new_buyback_qty > 0) {
										$new_buyback_price = ( ($prev_buyback_qty * $prev_buyback_price) + ($total_buyback_price) ) / $new_buyback_qty;
									} else {
										$new_buyback_price = 0;
									}

									$buyback_info_update_sql = "UPDATE " . TABLE_PRODUCTS . "
																SET products_buyback_quantity = '" . tep_db_input($new_buyback_qty) . "', products_buyback_price = '" . tep_db_input(round($new_buyback_price, 4)) . "'
																WHERE products_id = '" . tep_db_input($buyback_order_row['products_id']) . "'";
									tep_db_query($buyback_info_update_sql);

									//Always get the max mature period among all the products
									$this_prod_mature_period = tep_get_products_payment_mature_period($buyback_order_row['products_id'], $buyback_order_row['customers_id']);
									$cron_pending_credit_mature_period = max($this_prod_mature_period, $cron_pending_credit_mature_period);
								}

								/*************************************************************************************
									Preparing data for scheduled cron job
									This step applies to supplier buyback (user type=1) AND customer buyback(user type=0)
								*************************************************************************************/

								$cron_job_verify_select_sql = "	SELECT cron_pending_credit_trans_completed_date
																FROM " . TABLE_CRON_PENDING_CREDIT . "
																WHERE cron_pending_credit_trans_type = 'B'
																	AND cron_pending_credit_trans_id = '".tep_db_input($buyback_request_group_id)."'";
								$cron_job_verify_result_sql = tep_db_query($cron_job_verify_select_sql);

								if (!tep_db_num_rows($cron_job_verify_result_sql)) {
									$cron_pending_credit_data_array = array('cron_pending_credit_trans_type' => 'B',
																			'cron_pending_credit_trans_id' => tep_db_input($buyback_request_group_id),
																			'cron_pending_credit_trans_created_date' => $buyback_order_row['buyback_request_group_date'],
																			'cron_pending_credit_trans_completed_date' => 'now()',
																			'cron_pending_credit_mature_period' => $cron_pending_credit_mature_period,
																			'cron_pending_credit_trans_status' => 3
																		   );
									tep_db_perform(TABLE_CRON_PENDING_CREDIT, $cron_pending_credit_data_array);
								}
								// End of preparing data for scheduled cron job
							}
						}
					}
				}
			} else {
				$received = true;
				if ($get_history_row['received'] == 0) {
					$dispute = true;
				} else if ($get_history_row['received'] == 1) {
					$dispute = false;
					$row_style = 'padding: 0px 10px;color:#999999;';
				}
			}

			$history_html .= '	<tr><td colspan="6"><div style="padding-top: 1px;border-bottom:1px solid #CFCFCF;width:100%;"><!-- --></div></td></tr>
								<tr style="'.$row_style.'" valign="top" id="row_'.$op_history_id.'">
									<td width="13%" style="text-align:center;">'.$dates.'</td>
									<td width="14%" style="text-align:center;">'.$times.'</td>
									<td width="15%" style="text-align:center;">'.($get_history_row['delivered_amount'] != 0 ? $get_history_row['delivered_amount'] : '').'</td>
									<td width="15%" style="text-align:center;">'.$get_history_row['delivered_character'].'</td>
									';

			if (tep_not_null($record)) {
				// Get all constant
				$defined_constants = get_defined_constants();
				$record_arr = explode(":~:", $record);

				$record_text = $defined_constants[$record_arr[0]];

				$history_html .= '<td width="24%" nowrap style="text-align:left;color:black;" id="notes_'.$op_history_id.'">
										&nbsp;
									</td>
									<td width="19%" id="receive_'.$op_history_id.'" style="text-align:center;">
										'.$record_text.'
									</td>';
			} else if ($received) {
				if ($dispute) {
					$history_html .= '<td width="24%" style="text-align:left;">&nbsp;</td>
									  <td width="19%" style="text-align:center;">
										<div style="color:red;padding: 0px 10px;text-align:center;">
											<font color="red" style="text-align:center;">'.TEXT_DID_NOT_RECEIVE.'</font>
										</div>
									  </td>';
				} else {
					$history_html .= '<td width="24%" style="text-align:left;">&nbsp;</td>
									  <td width="19%" style="text-align:center;">
										<div style="padding: 0px 10px;text-align:center;">'.TEXT_DELIVERY_CONFIRMED.'</div>
									  </td>';
				}
			} else {
				if ($rolled_back) {
					$history_html .= '<td width="43%" nowrap style="color:black;text-align:center;" colspan="2">
											'.TEXT_ROLLED_BACK_DELIVERED_AMT.'
										</td>';
				} else {
					if (tep_customer_delivery_confirmation($extra_info_array['delivery_mode'])) {
						$history_html .= '<td width="24%" align="left" style="color:black;text-align:left;" id="notes_'.$op_history_id.'" nowrap>
											'.TEXT_AUTO_CONFIRM_DELIVERY_NOTE.'<br/>
											<div style="float: left;">'.TEXT_REMAINING_TIME.': </div><div style="float: left;" id="Countdown_'.$op_history_id.'" value="'.$op_history_id.'"></div>
										</td>
										<td width="19%" id="receive_'.$op_history_id.'" style="text-align:center;">
											'.TEXT_DID_YOU_RECEIVE.'<br/>
											'.tep_button(BUTTON_YES, BUTTON_YES, '', 'style="padding: 0px 2px;" id="receive_yes" onClick="pre_confirm_delivered(\''.$op_history_id.'\', \''.$extra_info_array['delivery_mode'].'\');"', '').'
											&nbsp;
											'.tep_button(BUTTON_NO, BUTTON_NO, '', 'style="padding: 0px 2px;" id="receive_no" onClick="delivery_dispute(\''.$op_history_id.'\');"', '').'
										</td>';
					} else {
						$history_html .= '<td width="24%" nowrap style="color:black;text-align:left;" id="notes_'.$op_history_id.'">
											&nbsp;
										</td>
										<td width="19%" id="receive_'.$op_history_id.'" style="text-align:center;">
											&nbsp;
										</td>';
					}
				}
			}

			$history_html .= '	</tr>';

			if ($received && tep_not_null($get_history_row['dispute_comment'])) {
				$history_html .= '	<tr id="details_'.$op_history_id.'">
										<td colspan="6" style="padding:0px 20px 10px 20px;">
										<span style="padding-right:5px">'.tep_image(DIR_WS_ICONS . "icon_details.gif", "", "9", "9").'</span>
										'.TEXT_DETAILS.' <span id="details_msg_'.$op_history_id.'">'.$get_history_row['dispute_comment'].'</span>
										</td>
									</tr>';
			} else if (tep_not_null($record)) {
				if (count($record_arr) > 1) {
					if (count($record_arr) > 2) {
						$record_cnt = 0;
						$record_arr_sliced = array_slice($record_arr, 2);

						// Add quotes
						$record_arr_sliced_cnt = 0;
						foreach ($record_arr_sliced as $record_arr_sliced_val) {
							$record_arr_sliced[$record_arr_sliced_cnt] = '"'.$record_arr_sliced_val.'"';
							$record_arr_sliced_cnt ++;
						}
						$record_arr_sliced_implode = implode(",", $record_arr_sliced);
						eval('$record_text = sprintf($defined_constants[$record_arr[1]], '.$record_arr_sliced_implode.');');
					} else {
						$record_text = $defined_constants[$record_arr[1]];
					}
					$history_html .= '	<tr id="details_'.$op_history_id.'">
											<td colspan="6" style="padding:0px 20px 10px 20px;">
												<div style="float:left;padding-right:5px">&nbsp;</div>
												<div style="float:left;" class="noteIcon">&nbsp;</div>
												<div style="float:left;">&nbsp;'.TEXT_REASON.' '.$record_text.'</div>
											</td>
										</tr>';
				} else {
					$history_html .= '	<tr id="details_'.$op_history_id.'" style="display:none;">
											<td colspan="6" style="padding:0px 20px 10px 20px;">
											<span style="padding-right:5px">'.tep_image(DIR_WS_ICONS . "icon_details.gif", "", "9", "9").'</span>
											'.TEXT_DETAILS.' <span id="details_msg_'.$op_history_id.'"></span>
											</td>
										</tr>';
				}
			} else {
				$history_html .= '	<tr id="details_'.$op_history_id.'" style="display:none;">
										<td colspan="6" style="padding:0px 20px 10px 20px;">
										<span style="padding-right:5px">'.tep_image(DIR_WS_ICONS . "icon_details.gif", "", "9", "9").'</span>
										'.TEXT_DETAILS.' <span id="details_msg_'.$op_history_id.'"></span>
										</td>
									</tr>';
			}

			if (tep_customer_delivery_confirmation($extra_info_array['delivery_mode'])) {
				if ($date_confirm_delivered != '0000-00-00 00:00:00') {
					$date_confirm_delivered_sec = tep_day_diff(date("Y-m-d H:i:s"), $date_confirm_delivered, 'sec');

					// For the Confirm delivered Count Down
					$history_html .= '<script>';
					$history_html .= 'jQuery(function () {
											var currentTime_'.$op_history_id.' = new Date();

											var cfm_hour_'.$op_history_id.' = currentTime_'.$op_history_id.'.getHours();
											var cfm_min_'.$op_history_id.' = currentTime_'.$op_history_id.'.getMinutes();
											var cfm_sec_'.$op_history_id.' = currentTime_'.$op_history_id.'.getSeconds();
											var cfm_mon_'.$op_history_id.' = currentTime_'.$op_history_id.'.getMonth() + 1;
											var cfm_date_'.$op_history_id.' = currentTime_'.$op_history_id.'.getDate();
											var cfm_year_'.$op_history_id.' = currentTime_'.$op_history_id.'.getFullYear();

											var cfm_s_yrs_'.$op_history_id.' = 0;
											var cfm_s_mths_'.$op_history_id.' = 0;
											var cfm_s_days_'.$op_history_id.' = 0;
											var cfm_s_hrs_'.$op_history_id.' = 0;
											var cfm_s_mins_'.$op_history_id.' = 0;
											var cfm_s_secs_'.$op_history_id.' = 0;

											var cfm_date_confirm_delivered_'.$op_history_id.' = '.$date_confirm_delivered_sec.';

											cfm_s_yrs_'.$op_history_id.'  = date("Y", mktime(cfm_hour_'.$op_history_id.', cfm_min_'.$op_history_id.', eval(cfm_sec_'.$op_history_id.' + "+" + cfm_date_confirm_delivered_'.$op_history_id.'), cfm_mon_'.$op_history_id.', cfm_date_'.$op_history_id.', cfm_year_'.$op_history_id.'));
											cfm_s_mths_'.$op_history_id.' = date("m", mktime(cfm_hour_'.$op_history_id.', cfm_min_'.$op_history_id.', eval(cfm_sec_'.$op_history_id.' + "+" + cfm_date_confirm_delivered_'.$op_history_id.'), cfm_mon_'.$op_history_id.', cfm_date_'.$op_history_id.', cfm_year_'.$op_history_id.'));
											cfm_s_days_'.$op_history_id.' = date("d", mktime(cfm_hour_'.$op_history_id.', cfm_min_'.$op_history_id.', eval(cfm_sec_'.$op_history_id.' + "+" + cfm_date_confirm_delivered_'.$op_history_id.'), cfm_mon_'.$op_history_id.', cfm_date_'.$op_history_id.', cfm_year_'.$op_history_id.'));
											cfm_s_hrs_'.$op_history_id.'  = date("H", mktime(cfm_hour_'.$op_history_id.', cfm_min_'.$op_history_id.', eval(cfm_sec_'.$op_history_id.' + "+" + cfm_date_confirm_delivered_'.$op_history_id.'), cfm_mon_'.$op_history_id.', cfm_date_'.$op_history_id.', cfm_year_'.$op_history_id.'));
											cfm_s_mins_'.$op_history_id.' = date("i", mktime(cfm_hour_'.$op_history_id.', cfm_min_'.$op_history_id.', eval(cfm_sec_'.$op_history_id.' + "+" + cfm_date_confirm_delivered_'.$op_history_id.'), cfm_mon_'.$op_history_id.', cfm_date_'.$op_history_id.', cfm_year_'.$op_history_id.'));
											cfm_s_secs_'.$op_history_id.' = date("s", mktime(cfm_hour_'.$op_history_id.', cfm_min_'.$op_history_id.', eval(cfm_sec_'.$op_history_id.' + "+" + cfm_date_confirm_delivered_'.$op_history_id.'), cfm_mon_'.$op_history_id.', cfm_date_'.$op_history_id.', cfm_year_'.$op_history_id.'));

									       countdown_timer("Countdown_'.$op_history_id.'", "", auto_delivery_confirmation, cfm_s_yrs_'.$op_history_id.', cfm_s_mths_'.$op_history_id.', cfm_s_days_'.$op_history_id.', cfm_s_hrs_'.$op_history_id.', cfm_s_mins_'.$op_history_id.', cfm_s_secs_'.$op_history_id.');
									  });';
					$history_html .= '</script>';
				}

			}
		}
?>
							<tr>
								<td colspan="7" style="padding: 0px 10px 15px 10px">
								<div style="text-align:right;margin-top:-20;display:<?=$display_accumulate?>;" id="open_time_msg_<?=$products['orders_products_id']?>"><?=sprintf(TEXT_TOTAL_ACCUMULATED_OPEN_TIME, '<span id="open_time_'.$products['orders_products_id'].'" style="font-weight:bold;letter-spacing:1px">'.$accumulated_buyback_time.'</span>');?></div>

								<div style="background-color: rgb(255, 253, 219);width:100%;height:100%;" class="loginColumnBorder">
									<?php
									if (!tep_direct_open_buyback($extra_info_array['delivery_mode'])) {
									?>
									<div>
										<div class="breakLine"><!-- --></div>
										<div>
											<table width="100%" cellspacing="0" cellpadding="2" border="0" style="padding:0px;" class="boxText">
											<tbody>
											<tr>
												<td style="padding: 5px 20px;">
													<?php
													if (!tep_not_null($trade_mode)) {
														// The priority is must have delivery method by customer.
														echo tep_image(DIR_WS_ICONS . "warning.png").'&nbsp;';
														echo sprintf(TEXT_PROVIDE_DELIVERY_INFO, $products['orders_products_id']);
													} else if (!$pwl_not_in_process) {
														echo HEADER_PWL_DELIVERY_INFO_TITLE;
													} else if ($pwl_not_in_process || tep_not_null($eta_button)) {
														echo HEADER_DELIVERY_INFO_TITLE;
													}
													?>
												</td>
											</tr>
											<?
											if (tep_not_null($eta_button)) {
											?>
											<tr>
												<td style="padding: 5px 20px;">
													<div style="float: left;"><?=ENTRY_IN_GAME_DURATION?><?=tep_draw_pull_down_menu('in_game_duration['.$products['orders_products_id'].']', $eta_array, '', ' id="in_game_duration_'.$products['orders_products_id'].'"');?> <?=ENTRY_HOURS;?> <span class="redIndicator">*</span>&nbsp;&nbsp;&nbsp;</div>
													<div style="float: left;" class="green_button">
														<?=$eta_button?>
													</div>
												</td>
											</tr>
											<tr>
												<td style="padding: 0px 20px;">
													<div id="counter_box_<?=$products['orders_products_id']?>"  style="display:none;">
														<div style="float: left;padding-top: 3px;"><?=TEXT_REMAINING_TIME?>: </div>
														<div style="float: left;padding-top: 3px;" id="Countdown_eta_<?=$products['orders_products_id']?>" value="<?=$products['orders_products_id']?>"></div>
														<div style="float: left;padding-top: 5px;">
															<a href="javascript:void(0);" onClick="stop_eta('<?=$products['orders_products_id']?>');"><?=tep_image(DIR_WS_ICONS . "icon_error.gif", "", "12", "12")?></a>
														</div>
													</div>
												</td>
											</tr>
											<?
											}
											?>
											<tr>
												<td style="padding: 0px;">
													&nbsp;
												</td>
											</tr>
											</tbody>
											</table>
										</div>
									</div>
									<?php
									}
									?>
									<div>
										<table id="orderHistoryTable" width="100%" cellspacing="0" cellpadding="0" border="0" class="boxText">
										<tbody>
											<tr><td colspan="6"><div style="padding-top: 1px;<?=tep_direct_open_buyback($extra_info_array['delivery_mode']) == false ? 'border-bottom:1px solid #CFCFCF;': '';?>width:100%;"><!-- --></div></td></tr>
											<tr valign="top" align="center" style="font-weight:bold;">
												<td width="13%" style="text-align:center;"><?=HEADER_DATE?></td>
												<td width="14%" style="text-align:center;"><?=HEADER_TIME?></td>
												<td width="15%" style="text-align:center;"><?=HEADER_DELIVERED_AMOUNT?></td>
												<td width="15%" style="text-align:center;"><?=HEADER_DELIVERED_CHARACTER?></td>
												<td width="24%" style="text-align:left;">&nbsp;</td>
												<td width="19%" style="text-align:center;"><?=HEADER_ACTION?></td>
											</tr>
											<?=$history_html?>
										</tbody>
										</table>
									</div>
								</div>
								</td>
							</tr>
<?php
	} // END IF Game Currency Product
								if($prd_num != count($order->products) - 1) {
									echo '<tr>
											<td colspan="7" class="dottedLine" style="padding:0px;">&nbsp;</td>
										</tr>';
								}
} else if ($products['custom_products_type_id'] == 4) { // HLA
?>
							<tr id="nav_hla_<?=$products['orders_products_id'];?>" style="display: none;">
								<td style="padding: 0px;">&nbsp;</td>
								<td colspan="5" style="padding-top: 0px; padding-left: 7px; vertical-align: top;">
									<div id="nav_sub_hla_<?=$products['orders_products_id'] ?>"><!-- --></div>
								</td>
								<td style="padding: 0px;">&nbsp;</td>
							</tr>
<?php
								if($prd_num != count($order->products) - 1) {
									echo '<tr>
											<td colspan="7" class="dottedLine" style="padding:0px;">&nbsp;</td>
										</tr>';
								}
} else if ($products['custom_products_type_id'] == 2) {
								if($prd_num != count($order->products) - 1) {
									echo '<tr>
											<td colspan="7" class="dottedLine" style="padding:0px;">&nbsp;</td>
										</tr>';
								}
}
									$prdcnt ++;
									}
								}

								// get Orders Totals record
                                unset($totals_array);
								if (sizeof($order->totals) && is_array($order->totals)) {
									for ($r=0, $t=count($order->totals); $r < $t; $r++) {
										$totals_array[$order->totals[$r]['class']] = array('title'	=> $order->totals[$r]['title'],
																						   'text'	=> $order->totals[$r]['text'],
																						   'value'	=> $order->totals[$r]['value']
																					 );
									}
								}
							?>
							<tr style="background-color:#E3E3E3;">
								<td colspan="6" style="text-align:right; font-weight:bold;"><?=sprintf(SUB_TITLE_SUB_TOTAL, $currencies->format($totals_array['ot_subtotal']['value'], true, $order->info['currency'], $order->info['currency_value']))?>&nbsp;</td>
								<td>&nbsp;</td>
							</tr>

							<?php
								// Gift Card
								if (sizeof($totals_array['ot_ogc'])) {
                                    $search = array('Gift Card', ':');
									$totals_array['ot_ogc']['title'] = str_replace($search, '', $totals_array['ot_ogc']['title']);
							?>
							<tr>
								<td colspan="5" class="dottedLine" style="padding-left:12px;"><?=TEXT_GIFT_CARD . "<b>" . $totals_array['ot_ogc']['title'] . "</b>"?>&nbsp;</td>
								<td class="dottedLine" style="text-align:right;"><?=sprintf(TEXT_AMOUNT_IN_RED, $currencies->format($totals_array['ot_ogc']['value'], true, $order->info['currency'], $order->info['currency_value']))?>&nbsp;</td>
								<td class="dottedLine">&nbsp;</td>
							</tr>
							<?php
                                }

								// Store Credit
								if (sizeof($totals_array['ot_gv'])) {
							?>
							<tr>
								<td colspan="5" class="dottedLine" style="padding-left:12px;"><?=TEXT_STORE_CREDIT?>&nbsp;</td>
								<td class="dottedLine" style="text-align:right;"><?=sprintf(TEXT_STORE_CREDIT_AMOUNT, $currencies->format($totals_array['ot_gv']['value'], true, $order->info['currency'], $order->info['currency_value']))?>&nbsp;</td>
								<td class="dottedLine">&nbsp;</td>
							</tr>
							<?php
								}

								// Discount Coupon
								if (sizeof($totals_array['ot_coupon'])) {
									$search = array('Discount Coupons', '(if there is any)', ':');
									$totals_array['ot_coupon']['title'] = str_replace($search, '', $totals_array['ot_coupon']['title']);
							?>
							<tr>
								<td colspan="5" class="dottedLine" style="padding-left:12px;"><?=sprintf(TEXT_DISCOUNT_COUPON_NUMBER, "<b>".$totals_array['ot_coupon']['title']."</b>")?>&nbsp;</td>
								<td class="dottedLine" style="text-align:right;"><?=sprintf(TEXT_DISCOUNT_AMOUNT, $currencies->format($totals_array['ot_coupon']['value'], true, $order->info['currency'], $order->info['currency_value']))?>&nbsp;</td>
								<td class="dottedLine">&nbsp;</td>
							</tr>
							<?php }	?>
							<tr>
								<td colspan="5" class="dottedLine" style="padding-left:12px;"><?=sprintf(TEXT_PAYMENT_METHOD, "<b>".$order->info['payment_method']."</b>")?>&nbsp;</td>
								<td class="dottedLine" style="text-align:right;" nowrap><?=sprintf("<b>".SUB_TITLE_ESTIMATED_TOTAL."</b>", $currencies->format($totals_array['ot_total']['value'], true, $order->info['currency'], $order->info['currency_value']))?>&nbsp;</td>
								<td class="dottedLine">&nbsp;</td>
							</tr>

						</tbody>
					</table>

					<div class="breakLine" style="height:20px;"><!-- --></div>
					<?php
						// verify Order Cancellation Request
						$order_cancelled = false;
						$order_cancelled_select_sql = "SELECT orders_id FROM " . TABLE_ORDERS_CANCEL_REQUEST . " WHERE orders_id = '" . (int)$order_id . "'";
						$order_cancelled_result_sql = tep_db_query($order_cancelled_select_sql);
						if (tep_db_num_rows($order_cancelled_result_sql) > 0) {
							$order_cancelled = true;
						}

						// show expiry count down time
						if (in_array($order->info['orders_status_id'], array(1)) && ($order_cancelled == false)) {
							$pm_expiry_time_array = tep_get_order_expiry_time($order, $pm_obj);
					?>
						<!-- change the height px back to 30px if want to show the 2 msg below -->
					    <div style="height:1px; padding:0px 7px;">
							<div style="float:left;">
								<?=sprintf(MESSAGE_ORDER_CANCELLED, $pm_expiry_time_array['hrs'], $pm_expiry_time_array['mins'])?>
								<?=($order->info['orders_status_id']==1 ? '<br />'.MESSAGE_PAYMENT_INFORMATION : '')?>
							</div>
						</div>
						<div class="breakLine" style="height:30px;"><!-- --></div>
					<?php }	?>
					<div style="height:40px; display:block; padding:0px 7px;">

<?php
	// show cancel this order link
	if (in_array($order->info['orders_status_id'], array(1,2,7))) {
?>
						<div style="float:left; width:150px; display: inline-block; margin: 5px 0px 0px 0px;">
						<?php
							if ($order_cancelled == false) {
						?>
							<div class="red_submit_button">
								<a href="javascript:void(0);" onClick="cancel_order('<?=$order_id;?>', '<?=$order->info['orders_status_id'];?>');" style="cursor:pointer;">
									<span><font><?=LINK_CANCEL_THIS_ORDER?></font></span>
								</a>
							</div>
						<?php
							} else {
						?>
							<div class="lightgray_button">
								<a href="javascript: void(0);">
									<span>
										<font><?=LINK_CANCEL_THIS_ORDER;?></font>
									</span>
								</a>
							</div>
						<?php
							}
						?>
						</div>
<?php
	} else if (in_array($order->info['orders_status_id'], array(3))) {
?>
						<div style="float:left; width:150px; display: inline-block; margin: 5px 0px 0px 0px;">
							<a href="<?=tep_href_link(FILENAME_CANCEL_ORDER, 'order_id='.$order_id, 'SSL')?>" style="cursor:pointer; cursor:hand;">
<?	if ($show_complain_btn == true) { ?>
							<div class="triangleRightIconBlue" style="margin: 3px 5px 0px 0px;"><!-- --></div>
							<div class="loginBoxLink"><?=LINK_COMPLAINT_THIS_ORDER?></div>
<?  } ?>
							</a>
						</div>
<?php
	}

	if ($order_cancelled == false) {
		switch ($order->info['orders_status_id']) {
			case 1:
				//if ($payment_info_array['confirm_complete_days'] > 0) {
					echo tep_div_button(2, '&nbsp; ' . IMAGE_BUTTON_SUBMIT_PAYMENT_INFO . ' &nbsp; ', tep_href_link(FILENAME_RECEIVE_PAYMENT_INFO, 'order_id='.$order->order_id, 'SSL'), 'style="float:right;"', 'green_button', false);
				//}
				break;

			default:
				$amount_delivered	= $currencies->format(tep_get_products_amount_delivered(), true, $order->info['currency'], $order->info['currency_value']);
				$amount_refunded	= $currencies->format(tep_get_products_amount_refunded(), true, $order->info['currency'], $order->info['currency_value']);
				echo '<div style="display:block; float:right; width:250px;">
						<div style="font-weight:bold;text-align:right;">'.sprintf(SUB_TITLE_AMOUNT_DELIVERED, $amount_delivered).'</div>
						<div style="text-align:right;">'.sprintf(SUB_TITLE_AMOUNT_REFUNDED, $amount_refunded).'</div>
					  </div>';
				break;
		}
	} else {
		echo '<div><span class="redIndicator">' . MESSAGE_CANCEL_ORDER_CANCELLED_3 . '</span></div>';
	}
?>
					</div>
				</div>
			</td>
		</tr>
	</table>
<?
	$account_order_info_content_string = ob_get_contents();
	ob_end_clean();

	echo $page_obj->get_html_simple_rc_box('',$account_order_info_content_string,13);
?>
<div class="breakLine" style="height:10px;"><!-- --></div>

<? ob_start(); ?>
	<div class="breakLine"></div>
	<div class="boxHeader" style="width:100%;">
		<div class="boxHeaderLeft"><!-- --></div>
		<div class="boxHeaderCenter" style="width:720px; display:inline;">
			<font style="display:inline-block; color:#fff; padding-top:5px;"><?=HEADER_ORDER_MESSAGE ?></font>
		</div>
		<div class="boxHeaderRight"><!-- --></div>
	</div>
	<div class="breakLine"><!-- --></div>
	<div style="padding:5px;">
		<?php
			$orders_status_array = tep_get_order_status_history_details($order_id);
			if (sizeof($orders_status_array) && is_array($orders_status_array)) {
				echo '<table width="90%" cellpadding="2" cellspacing="0" border="0">';
				foreach($orders_status_array[$order->order_id] as $num => $orders_status_array2) {
					if (tep_not_null($orders_status_array2['status_name']) || (($orders_status_array2['status_read'] == 1) && tep_not_null($orders_status_array2['status_comment']))) {
						$order_status_history_details_content = tep_date_long($orders_status_array2['status_date'], DATE_FORMAT_ORDER_MESSAGE);

						if (tep_not_null($orders_status_array2['status_name'])) {
							$order_status_history_details_content .= '&nbsp;&nbsp; | &nbsp;&nbsp;' . $orders_status_array2['status_name'];
						}
		?>
						<tr>
							<td>
								<font style="font-weight:bold;"><?=$order_status_history_details_content?></font>
							</td>
						</tr>
						<tr>
							<td><?=($orders_status_array2['status_read'] == 1) ? nl2br($orders_status_array2['status_comment']) : '' ?></td>
						</tr>
						<tr><td style="height:15px;"></td></tr>
		<?php
					}
				}
				echo '</table>';
			}
		?>
		<div class="dottedLine"><!-- --></div>
		<div style="height:30px; display: block; padding: 0px 7px;">
			<div style="float:left; width:150px; display: inline-block; margin: 5px 0px 0px 0px;">
				<a href="<?=tep_href_link(FILENAME_ACCOUNT_HISTORY, 'orders_type='.$orders_type, 'SSL')?>">
					<div class="triangleRightIconBlue" style="margin: 3px 5px 0px 0px;"><!-- --></div>
					<div class="loginBoxLink" style="cursor:hand;"><?=IMAGE_BUTTON_BACK?></div>
				</a>
			</div>
		</div>
	</div>
<?
	$account_order_msg_content_string = ob_get_contents();
	ob_end_clean();

	echo $page_obj->get_html_simple_rc_box('',$account_order_msg_content_string,13);
?>
<div class="breakLine" style="height:20px;"><!-- --></div>

<div id="confirm_delivered_box" class="fancy_box_login" style="width:350px;display:none;">
	<div class="fancy_inner_login" style="display: block;">
		<div class="fancy_frame_bg_login_login">
			<div class="fancy_bg fancy_bg_n_login"></div>
			<div class="fancy_bg fancy_bg_ne_login"></div>
			<div class="fancy_bg fancy_bg_e_login"></div>
			<div class="fancy_bg fancy_bg_se_login"></div>
			<div class="fancy_bg fancy_bg_s_login"></div>
			<div class="fancy_bg fancy_bg_sw_login"></div>
			<div class="fancy_bg fancy_bg_w_login"></div>
			<div class="fancy_bg fancy_bg_nw_login"></div>
		</div>
		<div id="confirm_delivered_content" class="fancy_content_login" style="position:static;">
			<form name="loginForm" method="post" action="<?=tep_href_link(FILENAME_LOGIN)?>">
				<?=tep_draw_hidden_field('op_history_id', '', 'id="op_history_id"')?>
				<table id="confirm_delivered_table" cellspacing="0" cellpadding="3" width="100%" style="background-color:#ffffff;padding:5px 12px;">
					<tr>
						<td colspan="3" style="padding:20px 20px 10px 20px;font-size:12px;">
							<?=TITLE_WHAT_TRADING_METHOD?>
						</td>
					</tr>
					<tr>
						<td colspan="3" style="padding:0 80px;font-size:12px;witdh:1px;">
							<table>
								<tr>
									<td width="10"><?=tep_draw_radio_field('trade_method', 'f2f', '', 'id="trade_method_f2f" onClick="select_trading(this.value);"');?></td>
									<td><?=OPTION_FACE_TO_FACE?></td>
								</tr>
							</table>
						</td>
					</tr>
					<tr>
						<td colspan="3" style="padding:0 80px;font-size:12px;witdh:1px;">
							<table>
								<tr>
									<td width="10"><?=tep_draw_radio_field('trade_method', 'mail', '', 'id="trade_method_mail" onClick="select_trading(this.value);"');?></td>
									<td><?=OPTION_BY_MAIL?></td>
								</tr>
							</table>
						</td>
					</tr>
					<tr>
						<td colspan="3" style="padding:0 80px;font-size:12px;witdh:1px;">
							<table>
								<tr>
									<td width="10"><?=tep_draw_radio_field('trade_method', 'open_store', '', 'id="trade_method_open_store" onClick="select_trading(this.value);"');?></td>
									<td><?=OPTION_OPEN_STORE?></td>
								</tr>
							</table>
						</td>
					</tr>
					<tr>
						<td colspan="3" style="padding:0 80px;font-size:12px;witdh:1px;">
							<table>
								<tr>
									<td width="10"><?=tep_draw_radio_field('trade_method', 'others', '', 'id="trade_method_others" onClick="select_trading(this.value);"');?></td>
									<td><?=OPTION_OTHERS?></td>
								</tr>
							</table>
						</td>
					</tr>
					<tr>
						<td colspan="3" style="padding:0 80px;font-size:12px;witdh:1px;">
							<table>
								<tr>
									<td width="10">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td>
									<td>
										<?=tep_draw_input_field('others_field', '', 'size="15" id="others_field" style="display:none;"')?>
									</td>
								</tr>
							</table>
						</td>
					</tr>
					<tr><td class="smallText" colspan="3"><div class="dottedLine" style="padding-top:5px;margin-top:0px;"><!-- --></div></td></tr>
					<tr>
						<td align="right" colspan="3">
							<div class="green_button" style="float:right;">
								<a href="javascript:void(0);" onClick="confirm_delivered();"><span><font><?=BUTTON_OK?></font></span></a>
							</div>
						</td>
					</tr>
				</table>
			</form>
		</div>
	</div>
</div>

<div id="confirm_delivered_box_bg" class="theLayerBg" style="display:none;"></div>
<?php
	}

	unset($order_array);
}
?>

<script language="javascript" src="includes/javascript/custom_product_xmlhttp.js"></script>
<script language="javascript">
	function DOMCall(name) {
		if (document.getElementById)
	    	return document.getElementById(name);
		else if (document.all)
			return document.all[name];
		else if (document.layers)
			return document.layers[name];
	}


	//Get List of cdkeys for this product
	function getCDKeys(product_no) {
		var item_no = product_no.split('_');
		var cdkey_arr = new Array();

		for (var i=1; i <= item_no[1]; i++) {
			var j = i - 1;
			cdkey_arr[j] = document.getElementById('cdkey_rows_'+ item_no[0] + '_' + i).value;
		}

		return cdkey_arr;
	}


	//Get batch CDKEY
	function getCDKeyImg_list(styleClass, product_no) {
		var item_no = product_no.split('_');
		var cdkey_id_arr = new Array();
			cdkey_id_arr = getCDKeys(product_no);

		if (styleClass == 'hide') {
			jQuery("#show_all_"+item_no[0]).show();
			jQuery("#hide_all_"+item_no[0]).hide();
			hideShow_list('unloading', product_no);

			for (i=0; i < cdkey_id_arr.length; i++) {
				//Update the individual key's show/hide link
				hideShow(styleClass, cdkey_id_arr[i], product_no);
	  		}
	  		hideShow_list(styleClass, product_no);
		} else {
			jQuery("#show_all_"+item_no[0]).hide();
			jQuery("#hide_all_"+item_no[0]).show();
			hideShow_list('loading', product_no);
			getCDKeyImg_multiple(cdkey_id_arr, product_no);
		}
	}

	function hideShow_list(styleClass, product_no) {
		//Switch show to hide and vice versa. Also display 'Loading ...' and 'Unloading ...'
		if (product_no.match('_') != null) {
			var item_no	= product_no.split('_');
			var loop_no = item_no[1];
			var oprd_no = item_no[0];
		}

		for(var n=1; n <= loop_no; n++) {
			var cdkey_value	= DOMCall('cdkey_rows_' + oprd_no + '_' + n).value;
			//alert(cdkey_value);
			var cdkey_div 	= DOMCall('show_cdkey_code_' + cdkey_value);
			var sub_div 	= DOMCall('nav_sub_' + cdkey_value);
			var nav_div		= DOMCall('nav_' + oprd_no + '_' + n);
			var prd_no		= oprd_no + '_' + n;

			if (styleClass == "show") {
				hideShow(styleClass, cdkey_value, prd_no);
			} else if (styleClass == "hide") {
				hideShow(styleClass, cdkey_value, prd_no);
			} else if (styleClass == "loading") {
				sub_div.innerHTML = "<?=TEXT_IS_LOADING; ?>";
			} else if (styleClass == "unloading") {
				sub_div.innerHTML = "<?=TEXT_IS_UNLOADING; ?>";
			} else if (styleClass == "show_n_hide") {
				cdkey_div.innerHTML = "<a href=\"javascript:;\" onClick=\"getCDKeyImg_list('hide', '" + product_no + "')\"><? echo LINK_HIDE_CDKEYIMAGES; ?></a>" + "&nbsp;&nbsp;&nbsp;" + "<a href=\"javascript:;\" onClick=\"getCDKeyImg_list('show', '" + product_no + "')\"><? echo LINK_SHOW_CDKEYIMAGES; ?></a>";
			}
		} // end for
	}

	function hideShow(styleClass, cdkey_id, product_no) {
		//Switch show to hide and vice versa. Also display 'Loading ...'
		var cdkey_div	= DOMCall('show_cdkey_code_' + cdkey_id);
		var sub_div		= DOMCall('nav_sub_' + cdkey_id);
		var nav_div 	= DOMCall('nav_' + product_no);

		if (styleClass == "loading") {
			sub_div.innerHTML = "<? echo TEXT_IS_LOADING; ?>";
		} else if (styleClass == "unloading") {
			sub_div.innerHTML = "<? echo TEXT_IS_UNLOADING; ?>";
		} else {
			if (styleClass == "show") {
				cdkey_div.innerHTML = "<a href=\"javascript:;\" onclick=\"getCDKeyImg('hide', "+cdkey_id+", '<?=SID?>', '"+product_no+"')\"><span><font><?='&nbsp;&nbsp;'.IMAGE_BUTTON_HIDE.'&nbsp;&nbsp;'?></span></font></a>";
				sub_div.innerHTML = "";
			}
			else if (styleClass == "hide") {
				cdkey_div.innerHTML = "<a href=\"javascript:;\" onclick=\"getCDKeyImg('show', "+cdkey_id+", '<?=SID?>', '"+product_no+"')\"><span><font><?='&nbsp;&nbsp;'.IMAGE_BUTTON_SHOW.'&nbsp;&nbsp;'?></span></font></a>";
				nav_div.innerHTML = "";
				//nav_div.style.Height = "8px";
				sub_div.innerHTML = "";
			}
		}
	}

	function showDeliveryInfo(mode) {
		var delivery_message_array = new Array('<?=TEXT_ORDER_FACE_TO_FACE_NOTE?>', '<?=TEXT_ORDER_GUYA_NOTE?>', '<?=TEXT_ORDER_MAIL_NOTE?>', '<?=TEXT_ORDER_OPEN_STORE_NOTE?>');
		var delivery_mode_note_obj = DOMCall('delivery_mode_note');

		if (delivery_mode_note_obj != null) {
			DOMCall('delivery_mode_note').innerHTML = '';
		}

		for (var opt_cnt=0; opt_cnt < delivery_message_array.length; opt_cnt++) {
			var opt_pos = opt_cnt + 1;

			if (mode == opt_pos) {
				DOMCall('delivery_sec_'+opt_pos).className = 'show';

				if (delivery_mode_note_obj != null) {
					DOMCall('delivery_mode_note').innerHTML = delivery_message_array[opt_cnt];
				}
			} else {
				DOMCall('delivery_sec_'+opt_pos).className = 'hide';
			}
		}

		jQuery("#delivery_mode_"+mode).attr("checked","checked");

		var action_msn_obj = DOMCall('div_action_msg');
		if (action_msn_obj != null)	action_msn_obj.innerHTML = '';

		realign_fancybox("change_delivery_table");
	}

	function hideMe() {
		if (typeof(document.getElementById('theLayer')) != 'undefined' && document.getElementById('theLayer') != null) {
			document.getElementById('theLayer').style.visibility = "hidden";
			document.getElementById('theLayerBg').style.visibility = "hidden";
			jQuery('body').css('overflow','');
			jQuery('.fancy_box').css('visibility','hidden');
		}

		if (typeof(document.getElementById('theLayerIframe')) != 'undefined' && document.getElementById('theLayerIframe') != null) {
			document.getElementById('theLayerIframe').style.visibility = "hidden";
		}
	}

	function showMe(opid) {
		var server_action = 'get_product_info';

		if (opid != '') {
		    pop_out_loading = '<table cellspacing="0" cellpadding="0" width="100%"><tr><td>';
			pop_out_loading += '<table id="regional_table" width="100%" bgcolor="#ffffff" cellspacing="2" cellpadding="10" style="border: 5px solid white">';
			pop_out_loading += '<tr><td align="center" style="text-align:center;"><?=tep_image(DIR_WS_IMAGES . "loading.gif", "", "20", "20")?></td></tr>';
			pop_out_loading += '</table>';
			pop_out_loading += '</td></tr></table>';

			jQuery('#general_content').html(pop_out_loading);
			show_fancybox('general_popup_box');

			var ref_url = "customer_xmlhttp.php?action="+server_action+"&opid="+opid;

			jQuery.get(ref_url, function(xml) {
				jQuery(xml).find('response').each(function(){
					var pop_out = '';
					var products_id = jQuery("products_id", this).text();
					var custom_product = jQuery("custom_product", this).text();
					var products_quantity = jQuery("products_quantity", this).text();
					var products_cat = jQuery("products_cat", this).text();
					var products_name = jQuery("products_name", this).text();
					var char_name = jQuery("char_name", this).text();
					var account_name = jQuery("account_name", this).text();
					var account_pwd = jQuery("account_pwd", this).text();
					var char_wow_account = jQuery("char_wow_account", this).text();
					var delivery_mode = jQuery("delivery_mode", this).text();
					var delivery_html = jQuery("delivery_html", this).text();
					var delivery_note = jQuery("delivery_note", this).text();

					pop_out += '<?=tep_draw_form("buy_now", tep_href_link(basename($PHP_SELF), tep_get_all_get_params(array("action"))."action=buy_now&products_id='+products_id+'&custom_product='+custom_product+'"), "post", "").tep_draw_hidden_field("buyqty", 1).tep_draw_hidden_field("hidden_password", "'+account_pwd+'", "id=\'hidden_password\'").tep_draw_hidden_field("buy_now_qty", "'+products_quantity+'")?>';
		      		pop_out += '<table id="change_delivery_table" width="100%" cellspacing="0" cellpadding="0">';
					pop_out += '<tr><td colspan="2"><div style="padding:10px 20px;float:left"><b class="largeFont"><?=HEADER_DELIVERY_INFORMATION ?></b></div><div style="float:right;padding:10px 15px"></div></td></tr>';
					pop_out += '<tr><td class="smallText" colspan="2"><div class="dottedLine"><!-- --></div></td></tr>';
					pop_out += '<tr><td class="smallText" colspan="2"><div style="padding:10px 20px;float:left"><font class="largeText"><b>'+products_name+'</b></font><br>'+products_cat+'</div></td></tr>';
					pop_out += '<tr><td align="right" colspan="2"><div style="padding:0 20px;float:left">'+delivery_html+'</div></td></tr>';
					pop_out += '<tr height="40"><td class="smallText" colspan="2"><div style="padding:10px 20px;float:left"><div id="delivery_mode_note" style="border: 1px solid #FF0000; background-color: #FFFFED; padding: 1px;">'+delivery_note+'</div></div></td></tr>';
					pop_out += '<tr><td align="left" class="smallText" width="45%" style="padding:5px 10px 5px 20px;" nowrap><?=ENTRY_CHARACTER_NAME?>:</td><td class="smallText"><?=tep_draw_input_field("extra_info[char_name]", "'+char_name+'", "size=\"33\" id=\"extra_info_char_name\" onclick=\"jQuery(\'#extra_info_char_name\').focus();\" ")?> <span class="redIndicator">*</span></td></tr>';
					pop_out += '<tbody id="delivery_sec_1" class="hide"></tbody>';
					pop_out += '<tbody id="delivery_sec_2" class="hide"><tr><?=sprintf(ENTRY_ORDER_GUYA_ACCOUNT, tep_draw_input_field('extra_info[char_account_name]', "'+account_name+'", "size=\"33\" id=\"extra_info_char_account_name\" onclick=\"jQuery(\'#extra_info_char_account_name\').focus();\" "))?></tr>';
					pop_out += '<tr><?=ENTRY_ORDER_GUYA_WOW_ACCOUNT_INFO?></tr>';
					pop_out += '<tr><?=sprintf(ENTRY_ORDER_GUYA_PWD, tep_draw_input_field('extra_info[char_account_pwd]', "'+account_pwd+'", "size=\"33\" id=\"extra_info_char_account_pwd\" onClick=\"\" onBlur=\"\" "))?></tr>';
					pop_out += '<tr><?=sprintf(ENTRY_ORDER_GUYA_WOW_ACCOUNT, tep_draw_input_field('extra_info[char_wow_account]', "'+char_wow_account+'", 'size="22" id="extra_info_char_wow_account"'))?></tr>';
					pop_out += '<tr><?=ENTRY_ORDER_GUYA_WOW_ACCOUNT_INFO2?></tr></tbody>';
					pop_out += '<tr><td colspan="2">&nbsp;</td></tr>';
					pop_out += '<tbody id="delivery_sec_3" class="hide"><tr><td></td></tr></tbody>';
					pop_out += '<tbody id="delivery_sec_4" class="hide"><tr><td></td></tr></tbody>';
					pop_out += '<tbody id="qna_error_msg" class="hide"></tbody>';
					pop_out += '<tr><td></td><td class="smallText"><div id="div_action_msg"></div><td></tr>';
					pop_out += '<tr><td class="smallText" colspan="2"><div class="dottedLine" style="padding: 10px 0 0"><!-- --></div></td></tr>';
					pop_out += '<tr><td colspan="2" align="center"><table border="0" cellspacing="0" cellpadding="10" align="center"><tr><td nowrap><?=tep_image_button2("green", "javascript: edit_delivery(\''+opid+'\');", BUTTON_SAVE_CHANGES);?></td></tr></table></td></tr>';
					pop_out += '</table></form>';

		      		pop_out += '<div id="loading_box" style="display:none;">'+pop_out_loading+'</div>';

		      		jQuery('#general_content').html(pop_out);
		      		jQuery('#general_content').css('padding', '0px');
					show_fancybox('general_popup_box');

					showDeliveryInfo(delivery_mode);
				});
			});
		}
	}

	function pre_confirm_delivered(op_history_id, delivery_id) {
		if (op_history_id != '') {
			jQuery("#op_history_id").val(op_history_id);

			if(jQuery("#confirm_delivered_box").css('display') == 'block') {
				jQuery("#confirm_delivered_box").css('height',jQuery("#confirm_delivered_table").height()+client_height_adjust());

				var boxTopValue = 0, boxLeftValue = 0;
				var docH = jQuery(document).height();
				var winW = jQuery(window).width();
				var winH = jQuery(window).height();

				boxTopValue = winH/2 - jQuery('#confirm_delivered_content').height()/2 - 130;
				boxLeftValue = winW/2 - 200;

				jQuery("#confirm_delivered_box").css({'left':boxLeftValue, 'top':boxTopValue});
				jQuery("#confirm_delivered_box_bg").css({'width':winW, 'height':docH});
			} else {
				var boxTopValue = 0, boxLeftValue = 0;
				//Get the screen height and width
				var docH = jQuery(document).height();
				var winW = jQuery(window).width();
				var winH = jQuery(window).height();

				boxTopValue = winH/2 - jQuery('#confirm_delivered_content').height()/2 - 130;
				boxLeftValue = winW/2 - 200;

				jQuery("#confirm_delivered_box").css({'display':'block', 'left':boxLeftValue, 'visibility':'visible', 'top':boxTopValue});
				jQuery("#confirm_delivered_box_bg").css({'display':'block', 'position':'absolute', 'width':winW, 'height':docH, 'visibility':'visible'});

				if (typeof(window.innerWidth) == 'number') {
					jQuery("#confirm_delivered_box").css({'position':'fixed'});
				}
			}

			switch (delivery_id) {
				case '1':
					jQuery("#trade_method_f2f").attr("checked", true);
					break;
				case '3':
					jQuery("#trade_method_mail").attr("checked", true);
					break;
				case '4':
					jQuery("#trade_method_open_store").attr("checked", true);
					break;
				default:
					jQuery("#trade_method_f2f").attr("checked", true);
					break;
			}
		}
	}

	function confirm_delivered(history_id) {
		var selected_trade_method = jQuery("input[@name='trade_method']:checked").val();

		if (history_id == undefined || history_id == '') {
			op_history_id = jQuery("#op_history_id").val();
		} else {
			op_history_id = history_id;
		}

		if (selected_trade_method != '' && selected_trade_method == 'others') {
			selected_trade_method = jQuery("#others_field").val();
		}

		var server_action = 'delivery_confirmed';
		var ref_url = "customer_xmlhttp.php?action="+server_action+"&op_history_id="+op_history_id+"&selected_trade_method="+selected_trade_method;

		jQuery.get(ref_url, function(xml){
			jQuery(xml).find('response').each(function(){
				var error = jQuery("error", this).text();
				var history_html = '';

				if (error == '') {
					var receive_msg = '<div style="padding: 0px 10px;"><?=TEXT_DELIVERY_CONFIRMED?></div>';

					jQuery("#row_"+op_history_id).css("color","#999999");

					jQuery("#notes_"+op_history_id).html('');
					jQuery("#receive_"+op_history_id).html(receive_msg);
				}
				// Hide PopUP
				jQuery("#confirm_delivered_box").css({'display':'none','visibility':'hidden'});
				jQuery("#confirm_delivered_box_bg").css({'display':'none','visibility':'hidden'});
				jQuery("body").css('overflow','');
			});
		});
//		jQuery("#row_"+op_history_id).html();
//		jQuery("#comments_"+op_history_id).html();
//		jQuery("#notes_"+op_history_id).html('');
//		jQuery("#receive_"+op_history_id).html();
		//jQuery("#row_"+op_history_id).css("color","#999999");

		//
	}

	function delivery_dispute(op_history_id) {
		if (op_history_id != '') {
			jQuery('#footerBar').css('display', 'none');
			set_fancybox_position();

			pop_out = '<?=tep_draw_form("buy_now", tep_href_link(basename($PHP_SELF), tep_get_all_get_params(array("action"))."action=buy_now&products_id="), "post", "")?>';
      		pop_out += '<table id="popup_dispute_box" width="100%" cellspacing="0" cellpadding="0" style="background-color:#ffffff">';
			pop_out += '<tr><td colspan="2"><div style="padding:10px 20px;float:left"><b class="largeFont"><?=HEADER_DELIVERY_DISPUTE?></b></div></td></tr>';
			pop_out += '<tr><td class="smallText" colspan="2"><div class="dottedLine"><!-- --></div></td></tr>';
			pop_out += '<tr><td class="smallText" colspan="2">';
			pop_out += '<div id="popup_box_loading" style="text-align:center;display:none;">';
      		pop_out += '<?=tep_image(DIR_WS_IMAGES . "lightbox-ico-loading.gif", "", "32", "32", "style=\"padding:50px;\"")?>';
      		pop_out += '</div>';
      		pop_out += '<div id="popup_box_desc">';
			pop_out += '<div style="padding:20px;"><?=TEXT_DISPUTE_DESC?></div>';
			pop_out += '<div style="padding:0px 20px 5px 20px;font-weight:bold;"><?=TEXT_DISPUTE_DETAILS?></div><div style="padding:0 20px;"><?=tep_draw_textarea_field("delivery_dispute", "", "53", "4", "", 'id="delivery_dispute"')?></div>';
			pop_out += '<div class="dottedLine" style="padding: 20px 0 0"><!-- --></div>';
			pop_out += '<table border="0" cellspacing="0" cellpadding="10" align="center"><tr><td nowrap><div class="green_button_fix_width"><a href="javascript:delivery_dispute_submit(\''+op_history_id+'\');"><span><font><?=BUTTON_SUBMIT ?></font></span></a></div></td></tr></table>';
			pop_out += '</table>';
			pop_out += '</div>';
			pop_out += '</td></tr>';
			pop_out += '</form>';

			jQuery("#fancy_content").html(pop_out);
			jQuery(".fancy_close_footer").css('display', 'block');

			realign_fancybox("popup_dispute_box");
		}
	}

	function delivery_dispute_submit(op_history_id) {
		// HLA auto confirm delivery note
		jQuery("#hlaAutoConfirm").css({'display':'none','visibility':'hidden'});

		var server_action = 'delivery_dispute';
		var dispute_content = jQuery("#delivery_dispute").val();
		var ref_url = "customer_xmlhttp.php?action="+server_action+"&op_history_id="+op_history_id;

		jQuery("#popup_box_desc").hide();
		jQuery("#popup_box_loading").show();

		jQuery.post(ref_url,{ dispute_content: dispute_content },function(xml){
			jQuery(xml).find('response').each(function(){
				var error = jQuery("error", this).text();
				var history_html = '';

				if (error == '') {
					var receive_msg = '<div style="color:red;padding: 0px 10px;"><font color="red"><?=TEXT_DID_NOT_RECEIVE?></font></div>';

					if (jQuery.trim(dispute_content) != '') {
						jQuery("#details_msg_"+op_history_id).html(dispute_content);
						jQuery("#details_"+op_history_id).show();
					}

					jQuery("#notes_"+op_history_id).html('');
					jQuery("#receive_"+op_history_id).html(receive_msg);
				} else if(error == 'counting_expired') {
					var receive_msg = '<div style="padding: 0px 10px;"><?=TEXT_DELIVERY_CONFIRMED?></div>';

					jQuery("#row_"+op_history_id).css("color","#999999");

					jQuery("#notes_"+op_history_id).html('');
					jQuery("#receive_"+op_history_id).html(receive_msg);
				}
			});
		});

		jQuery().ajaxStop(function(){ hideFancyBox(); })
	}
	function changeAttrType (id, newtype, getfocus) {
		var getfocus = getfocus ? getfocus : 1;
		var old_field = document.getElementById(id); // So we can use .attributes
		var attrs = old_field.attributes;
		var new_field = "<input "; // Need to create the input by hand since jQuery won't allow .attr('type',xxx);

		// Take care of attributes
		for(var i = 0; i < attrs.length; i++) {
			if(attrs[i].nodeName == "value") {
			  new_field += "value=\""+$(old_field).val()+"\" "; // Don't trust value attribute from the DOM
			} else if(attrs[i].nodeName != "type") {
			  new_field += attrs[i].nodeName+"=\""+attrs[i].nodeValue+"\" ";
			}
		}

		// Swap type field
		new_field += "type=\""+newtype+"\" />";

		// Modify DOM
		jQuery(old_field).before(jQuery(new_field));
		jQuery(old_field).remove();
		if (getfocus == 1 && jQuery(new_field)) {
			jQuery("#"+id).val('');
			jQuery("#"+id).focus();
			return true;
		} else {
			return false;
		}
	}

	function edit_delivery(opid) {
		jQuery("#change_delivery_table").css('display', 'none');
		realign_fancybox("change_delivery_table");
		jQuery("#loading_box").show();
		var answer = jQuery("#answer").val();
		jQuery("#div_action_msg").html('');

        var error_message = '';
        var mode = 1;
        var extra_info_char_name = jQuery("#extra_info_char_name").val();
        var extra_info_char_account_name = jQuery("#extra_info_char_account_name").val();
        var extra_info_char_account_pwd = jQuery("#extra_info_char_account_pwd").val();
        var extra_info_char_wow_account = jQuery("#extra_info_char_wow_account").val();

        jQuery("#qna_error_msg").hide().html('');

        jQuery(".deliveryInfo").each (function() {
            if (this.checked) {
                mode = this.value;
            }
        });

        if (trim_str(extra_info_char_name) == '') {
            error_message = error_message + "* " + '<?=ERROR_PLS_ENTER_CHAR_NAME?>' + "<br>";
        }

        if (mode == 1) {

        } else if (mode == 2) {
            if (trim_str( extra_info_char_account_name) == '') {
                error_message = error_message + "* " + '<?=ERROR_PLS_ENTER_ACCOUNT_NAME?>' + "<br>";
            }

            if (trim_str(extra_info_char_account_pwd) == '') {
                error_message = error_message + "* " + '<?=ERROR_PLS_ENTER_PASSWORD?>' + "<br>";
            } else {
                if (trim_str(extra_info_char_account_pwd) == jQuery("#hidden_password").val()) {
                    extra_info_char_account_pwd = '';
                }
            }
        }

        if (trim_str(error_message) != '') {
            jQuery("#div_action_msg").html('<table border="0" width="100%" bordercolor="#A7A7A7" bgcolor="#ffffff" cellspacing="2" cellpadding="2"><tr><td class="main"><font color="red">'+error_message+'</font></td></tr></table>');
            jQuery("#loading_box").hide(function () {
                realign_fancybox("change_delivery_table");
                jQuery("#change_delivery_table").css('display', 'block');
            });
        } else {
            var oid = '<?=$order_id?>';
            var delivery_mode = '';

            jQuery(".deliveryInfo").each (function() {
                if (this.checked) {
                    delivery_mode = this.value;
                }
            });

            var ref_url = "customer_xmlhttp.php?action=edit_delivery_info&order_id="+oid;

            jQuery.ajax({
            type: "POST",
            url: ref_url,
            data: {order_products_id : opid, delivery_mode : delivery_mode, char_name : extra_info_char_name, account_name : extra_info_char_account_name, account_pwd: extra_info_char_account_pwd, char_wow_account: extra_info_char_wow_account},
            async: false,
            dataType: "xml",
            success: function(data){
                    jQuery(data).find('response').each(function(){
                        var error_msg = jQuery("error_msg", this).text();
                        var url = jQuery("url", this).text();

                        if (error_msg == '') {
                            location.href = url;
                            hideMe();
                        } else {
                            jQuery("#div_action_msg").html('<table border="0" width="100%" bordercolor="#A7A7A7" bgcolor="#ffffff" cellspacing="2" cellpadding="2"><tr><td class="main"><font color="red">'+error_msg+'<br/><br/><?=ACCOUNT_HISTORY_DELIVERY_MODE_NOTES?></font></td></tr></table>');
                            jQuery("#loading_box").hide(function () {
                                realign_fancybox("change_delivery_table");
                                jQuery("#change_delivery_table").css('display', 'block');
                            });
                        }
                    });
                }
            });
        }
	}

	function update_eta(opid, type) {
		var in_game_duration_eta = 0;
		if (type == 'reset') {
			jQuery("#reset_button_"+opid).hide();
			jQuery('#open_time_'+opid).countdown('destroy');
		} else if (type == 'extend') {
			jQuery("#extend_button_"+opid).hide();
			//jQuery('#open_time_'+opid).countdown('pause');
		}

		in_game_duration_eta = jQuery("#in_game_duration_"+opid).val();
		jQuery('#Countdown_eta_'+opid).countdown('destroy');

		jQuery.get("customer_xmlhttp.php?action=eta_counter&opid="+opid+"&eta="+in_game_duration_eta+"&type="+type, function(xml) {
			jQuery(xml).find('response').each(function(){
				var currentTime = new Date()

				var c_hour = currentTime.getHours();
				var c_min = currentTime.getMinutes();
				var c_sec = currentTime.getSeconds();
				var c_mon = currentTime.getMonth() + 1;
				var c_date = currentTime.getDate();
				var c_year = currentTime.getFullYear();

				if (type == 'reset') {
					var yrs = 0;
					var mths = 0;
					var days = 0;
					var hrs = 0;
					var mins = 0;
					var secs = 0;

					var total_buyback_time = jQuery("total_buyback_time", this).text();

					// At least 1 secs in order to execute the function
					total_buyback_time = total_buyback_time == 0 ? 1 : total_buyback_time;


					yrs  = date("Y", mktime(c_hour, c_min, eval(c_sec + '-' + total_buyback_time), c_mon, c_date, c_year));
					mths = date("m", mktime(c_hour, c_min, eval(c_sec + '-' + total_buyback_time), c_mon, c_date, c_year));
					days = date("d", mktime(c_hour, c_min, eval(c_sec + '-' + total_buyback_time), c_mon, c_date, c_year));
					hrs  = date("H", mktime(c_hour, c_min, eval(c_sec + '-' + total_buyback_time), c_mon, c_date, c_year));
					mins = date("i", mktime(c_hour, c_min, eval(c_sec + '-' + total_buyback_time), c_mon, c_date, c_year));
					secs = date("s", mktime(c_hour, c_min, eval(c_sec + '-' + total_buyback_time), c_mon, c_date, c_year));

					var start_time = new Date();
					start_time = new Date(yrs, mths - 1, days, hrs, mins, secs);
					jQuery("#open_time_"+opid).countdown({since: start_time, compact: true, format: "HMS", description: ""});
					jQuery("#open_time_msg_"+opid).css("display", "block");
				}

				var s_yrs = 0;
				var s_mths = 0;
				var s_days = 0;
				var s_hrs = 0;
				var s_mins = 0;
				var s_secs = 0;

				var expiry_hour = jQuery("expiry_hour", this).text();
				var start_time = jQuery("start_time", this).text();

				// Add 1 secs in order to same with the accumulate time
				start_time = eval(start_time + '+' + 1);
				expiry_hour = eval(expiry_hour + '+' + in_game_duration_eta);

				s_yrs  = date("Y", mktime(eval(c_hour + '+' + expiry_hour), c_min, eval(c_sec + '-' + start_time), c_mon, c_date, c_year));
				s_mths = date("m", mktime(eval(c_hour + '+' + expiry_hour), c_min, eval(c_sec + '-' + start_time), c_mon, c_date, c_year));
				s_days = date("d", mktime(eval(c_hour + '+' + expiry_hour), c_min, eval(c_sec + '-' + start_time), c_mon, c_date, c_year));
				s_hrs  = date("H", mktime(eval(c_hour + '+' + expiry_hour), c_min, eval(c_sec + '-' + start_time), c_mon, c_date, c_year));
				s_mins = date("i", mktime(eval(c_hour + '+' + expiry_hour), c_min, eval(c_sec + '-' + start_time), c_mon, c_date, c_year));
				s_secs = date("s", mktime(eval(c_hour + '+' + expiry_hour), c_min, eval(c_sec + '-' + start_time), c_mon, c_date, c_year));

				countdown_timer("Countdown_eta_"+opid, "counter_box_"+opid, "", s_yrs, s_mths, s_days, s_hrs, s_mins, s_secs);
				//jQuery('#open_time_'+opid).countdown('resume');
				jQuery("#extend_button_"+opid).show();
			});
		});
	}

	function auto_delivery_confirmation() {
		var op_history_id = jQuery("#"+this.id).attr("value");
		confirm_delivered(op_history_id);
	}

	function stop_eta(opid) {
		jQuery.get("customer_xmlhttp.php?action=stop_eta&opid="+opid, function(xml) {
			jQuery("#extend_button_"+opid).hide();
			jQuery("#counter_box_"+opid).hide();
			jQuery("#reset_button_"+opid).show();

			jQuery('#Countdown_eta_'+opid).countdown('destroy');
			jQuery('#open_time_'+opid).countdown('destroy');

			jQuery(xml).find('response').each(function(){
				var accumulated_buyback_time = jQuery("accumulated_buyback_time", this).text();
				jQuery('#open_time_'+opid).html(accumulated_buyback_time);
			});
		});
	}

	function on_hold_buyback_order() {
		var opid = jQuery("#"+this.id).attr("value");

		jQuery.get("customer_xmlhttp.php?action=stop_eta&opid="+opid, function(xml) {
			jQuery("#extend_button_"+opid).hide();
			jQuery("#counter_box_"+opid).hide();
			jQuery("#reset_button_"+opid).show();

			jQuery('#Countdown_eta_'+opid).countdown('destroy');
			jQuery('#open_time_'+opid).countdown('destroy');

			jQuery(xml).find('response').each(function(){
				var accumulated_buyback_time = jQuery("accumulated_buyback_time", this).text();
				jQuery('#open_time_'+opid).html(accumulated_buyback_time);
			});
		});
	}

	function select_trading(trading_method) {
		if (trading_method == 'others') {
			jQuery("#others_field").show();
		} else {
			jQuery("#others_field").hide();
		}
	}


	// HLA : retrieve account info
	function getHLAInfo(styleClass, product_no) {
		if (styleClass == 'hide') {
			hideShowHLA('unloading', product_no);
			hideShowHLA(styleClass, product_no);
		} else {
			hideShowHLA('loading', product_no);

			ref_url = "custom_product_xmlhttp.php?action=get_hla_detail&lang="+'<?=$languages_id?>'+'&product_no='+product_no;

			jQuery.get(ref_url, function(xml) {
				jQuery(xml).find('response').each(function(){
					var detail = jQuery("detail", this).text();
					jQuery('#nav_sub_hla_' + product_no).html(detail);
					hideShowHLA(styleClass, product_no);
				});
			});
		}
	}

	function hideShowHLA(styleClass, product_no) {
		// Switch show to hide and vice versa. Also display 'Loading ...'
		var hla_div = DOMCall('show_hla_info_' + product_no);
		var $nav_div = jQuery('#nav_hla_' + product_no);
		var $sub_div = jQuery('#nav_sub_hla_' + product_no);

		if (styleClass == "loading") {
			$nav_div.removeAttr('style');
			$sub_div.html("<? echo TEXT_IS_LOADING; ?>");
		} else if (styleClass == "unloading") {
			$nav_div.removeAttr('style');
			$sub_div.html("<? echo TEXT_IS_UNLOADING; ?>");
		} else {
			if (styleClass == "show") {
				$nav_div.removeAttr('style');
				hla_div.innerHTML = "<a href=\"javascript:;\" onclick=\"getHLAInfo('hide', "+product_no+")\"><span><font><?='&nbsp;&nbsp;'.IMAGE_BUTTON_HIDE.'&nbsp;&nbsp;'?></span></font></a>";
			}
			else if (styleClass == "hide") {
				$nav_div.attr('style', 'display: none;');
				hla_div.innerHTML = "<a href=\"javascript:;\" onclick=\"getHLAInfo('show', "+product_no+")\"><span><font><?='&nbsp;&nbsp;'.IMAGE_BUTTON_SHOW.'&nbsp;&nbsp;'?></span></font></a>";
			}
		}
	}

	function hla_auto_delivery_confirmation() {
		var arr = explode('_', jQuery("#"+this.id).attr("value"));
		var op_history_id = arr[0];
		var product_no = arr[1];
		hla_confirm_delivered(product_no, op_history_id);
	}

	function hla_confirm_delivered(product_no, history_id) {
		if (history_id != undefined && history_id != '') {
			op_history_id = history_id;

			var server_action = 'delivery_confirmed';
			var ref_url = "customer_xmlhttp.php?action="+server_action+"&op_history_id="+op_history_id;

			jQuery.get(ref_url, function(xml){
				jQuery(xml).find('response').each(function(){
					var error = jQuery("error", this).text();
					var receive_msg = jQuery("result", this).text();

					jQuery("#orderHistory").css({'display':'none','visibility':'hidden'});
					jQuery("#hlaAutoConfirm").css({'display':'none','visibility':'hidden'});

					jQuery("#secret_qna_"+product_no).html(receive_msg);
					jQuery("#secret_qna_"+product_no).css({'display':'block'});

					// Hide PopUP
					jQuery("#confirm_delivered_box").css({'display':'none','visibility':'hidden'});
					jQuery("#confirm_delivered_box_bg").css({'display':'none','visibility':'hidden'});
					jQuery("body").css('overflow','');
				});
			});
		}
	}


	// Cancel Customer Order
	function pop_out_loading() {
		content = '<table id="popup_loading" cellspacing="0" cellpadding="0" width="100%"><tr><td>';
		content += '<table id="regional_table" width="100%" bgcolor="#ffffff" cellspacing="2" cellpadding="10" style="border: 5px solid white">';
		content += '<tr><td align="center" style="text-align:center;"><?=tep_image(DIR_WS_IMAGES . "loading.gif", "", 20, 20)?></td></tr>';
		content += '</table>';
		content += '</td></tr></table>';

		jQuery("#fancy_content").html(content);
		jQuery(".fancy_close_footer").css('display', 'block');
		realign_fancybox("popup_loading");
	}

	function cancel_order(order_id, orders_status_id) {
		if (order_id != undefined && order_id != '' && order_id == "<?php echo (int)$_GET['order_id']; ?>") {
			if (orders_status_id != undefined && orders_status_id != '' && orders_status_id == '1') {
				pop_out_loading();
				ref_url = "customer_xmlhttp.php?action=cancel_order&order_id="+order_id;

				jQuery.get(ref_url, function(xml) {
					jQuery(xml).find('response').each(function(){
						var detail = jQuery("detail", this).text();
						var redirect = jQuery("redirect", this).text();

						if ((typeof detail != 'undefined') && (detail != '')) {
							jQuery('#footerBar').css('display', 'none');
							set_fancybox_position();
							jQuery("#fancy_content").html(detail);
							jQuery(".fancy_close_footer").css('display', 'block');
							realign_fancybox('cancel_order_confirmation');
						} else if ((typeof redirect != 'undefined') && (redirect != '')) {
							document.location.href = redirect;
						} else {
							document.location.href = "<?=tep_href_link(FILENAME_ACCOUNT_HISTORY, '', 'SSL');?>";
						}
					});
				});
			} else {
				document.location.href = "<?=tep_href_link(FILENAME_CANCEL_ORDER, 'order_id=' . $order_id, 'SSL')?>";
			}
		}
	}

	function cancel_order_confirm(order_id) {
		if (order_id != undefined && order_id != '') {
			pop_out_loading();
			ref_url = "customer_xmlhttp.php?action=cancel_order_confirm&order_id="+order_id;

			jQuery.get(ref_url, function(xml) {
				jQuery(xml).find('response').each(function(){
					var detail = jQuery("detail", this).text();
					var redirect = jQuery("redirect", this).text();

					if ((typeof detail != 'undefined') && (detail != '')) {
						jQuery('#footerBar').css('display', 'none');
						set_fancybox_position();
						jQuery("#fancy_content").html(detail);
						jQuery(".fancy_close_footer").css('display', 'block');
						realign_fancybox('cancel_order_confirm');
						jQuery(".fancy_close_footer").click(function(){
							document.location.href = "<?=tep_href_link(FILENAME_ACCOUNT_HISTORY, '', 'SSL');?>";
						});
					} else if ((typeof redirect != 'undefined') && (redirect != '')) {
						document.location.href = redirect;
					} else {
						document.location.href = "<?=tep_href_link(FILENAME_ACCOUNT_HISTORY, '', 'SSL');?>";
					}
				});
			});
		}
	}
</script>