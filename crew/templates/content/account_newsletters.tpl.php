<h1><?=TITLE_MY_PROFILE ?></h1>
<div class="breakLine"><!-- --></div>

<?php if ($messageStack->size('account_newslettters') > 0) { ?>
	<div class="loginColumnBorder" style="background-color: #FFFDDB">
		<div style="padding:10px;"><?=$messageStack->output('account_newslettters'); ?></div>
		<div class="breakLine"><!-- --></div>
	</div>
<?
}
ob_start();
?>
	<table align="center" border="0" cellpadding="0" cellspacing="10" width="100%">
 		<tr>
			<td>
				<div style="font-size:16px; font-weight:bold;"><?=MY_NEWSLETTERS_TITLE?></div>
				<div class="breakLine"><!-- --></div>
				<div><?=MY_NEWSLETTERS_DESCRIPTION ?></div>
				<div class="breakLine"><!-- --></div>

				<!-- Start Newsletters content-->
				<?=tep_draw_form('account_newsletters_form', tep_href_link(FILENAME_ACCOUNT_NEWSLETTERS, '', 'SSL'), 'post', ' autocomplete="off"') ?>
				<?=tep_draw_hidden_field('action', 'process') ?>
					<table border="0" cellpadding="0" cellspacing="0" width="100%">
						<tr><td colspan="6" class="breakLine" style="height:20px;"></td></tr>
						<tr>
<?php 

	// tk testing, 2008-05-19
	$show_newsletter_array = array(17);
    $ng_cnt = 0;
    
	foreach ($newsletter_group_array as $news_grp_id => $news_grp_title) {
	    if ($ng_cnt % 2 == 0) {
			if ($ng_cnt > 0) {
				echo '</tr><tr>';
			}
		}
		
		//skip the main one
	    if ( in_array($news_grp_id, $show_newsletter_array) ) {
	        echo '<td class="inputField" style="width:20px;">'. tep_draw_checkbox_field('newsletter[]', $news_grp_id, (in_array($news_grp_id, $customer_subscribed_newsletter) ? true : false), 'id="ng_'.$news_grp_id.'"') . '</td>
	        	  <td class="inputField"><label for="ng_'.$news_grp_id.'">' . $news_grp_title . '</label></td>
	        	  <td width="5px"></td>';
	        $ng_cnt++;
	        continue;
	    }
        
		if ( in_array($news_grp_id, $customer_subscribed_newsletter) ) {
		    $newsletter_hidden_html .= tep_draw_hidden_field('newsletter[]', $news_grp_id, 'id="ng_'.$news_grp_id.'"') ."\n";
        }
	} //end foreach
	
	echo $newsletter_hidden_html;
?>
						</tr>
						<tr><td colspan="6" class="breakLine" style="height:20px;"></td></tr>
					</table>
					<div class="dottedLine">&nbsp;</div>
					
						
					<div style="height:40px; display: block;">
						<div style="float:left; width:50px; display: inline-block;">
							<div class="triangleRightIconBlue" style="margin: 3px 5px 0px 0px;"><!-- --></div>
							<div class="loginBoxLink"><a href="<?=tep_href_link(FILENAME_ACCOUNT, '', 'SSL')?>"><?=IMAGE_BUTTON_BACK?></a></div>
						</div>
						<?=tep_div_button(1, '&nbsp; '. IMAGE_BUTTON_SAVE_PROFILE .' &nbsp; ', 'account_newsletters_form', 'style="float:right;"', 'green_button', false, 'document.account_newsletters_form.submit()') ?>
					</div>
				</form>
				<!-- End Tab content-->
				
			</td>
		</tr>
	</table>
<?
	$acc_newsletters_content_string = ob_get_contents();
	ob_end_clean(); 
	
	echo $page_obj->get_html_simple_rc_box('',$acc_newsletters_content_string,13);
?>


<?php /*echo tep_draw_form('account_newsletter', tep_href_link(FILENAME_ACCOUNT_NEWSLETTERS, '', 'SSL')) . tep_draw_hidden_field('action', 'process');*/ ?>
			<!--h1><?=HEADING_TITLE?></h1>
			<div class="solidThickLine"></div>
<table border="0" width="100%" cellspacing="0" cellpadding="0">
	<tr>
		<td>
			<div style="border: 1px solid rgb(204, 204, 204); padding: 4px; background: rgb(255, 255, 237) none repeat scroll 0%; -moz-background-clip: -moz-initial; -moz-background-origin: -moz-initial; -moz-background-inline-policy: -moz-initial;"><?=TEXT_NEWSLETTER_MSG?></div>
		</td>
	</tr>
	<tr>
		<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
	<tr>
		<td class="infoBoxHeading"><b><?=MY_NEWSLETTERS_TITLE?></b></td>
	</tr>
	<tr>
		<td>
			<table border="0" width="100%" cellspacing="1" cellpadding="2" class="infoBox">
          		<tr class="infoBoxContents">
            		<td>
            			<table border="0" width="100%" cellspacing="0" cellpadding="2">
              				<tr>
                				<td><?php echo tep_draw_separator('pixel_trans.gif', '10', '1'); ?></td>
                				<td>
                					<table border="0" cellspacing="0" cellpadding="2">
                						<tr>
<?

	// tk testing, 2008-05-19
	$show_newsletter_array = array(17);
    $ng_cnt = 0;
    
	foreach ($newsletter_group_array as $news_grp_id => $news_grp_title) {
	    if ($ng_cnt % 3 == 0) {
			if ($ng_cnt > 0)    echo '</tr><tr>';
		}
		
	    //skip the main one
	    if ( in_array($news_grp_id, $show_newsletter_array) ) {
	        echo '<td class="inputField">'. tep_draw_checkbox_field('newsletter[]', $news_grp_id, (in_array($news_grp_id, $customer_subscribed_newsletter) ? true : false), 'id="ng_'.$news_grp_id.'"') . '</td><td class="inputField"><label for="ng_'.$news_grp_id.'">' . $news_grp_title . '</label></td><td width="5px"></td>';
	        $ng_cnt++;
	        continue;
	    }
        
		if ( in_array($news_grp_id, $customer_subscribed_newsletter) ) {
		    $newsletter_hidden_html .= tep_draw_hidden_field('newsletter[]', $news_grp_id, 'id="ng_'.$news_grp_id.'"') ."\n";
        }
	} //end foreach
	echo $newsletter_hidden_html;
?>
										</tr>
									</table>
                				</td>
                				<td><?php echo tep_draw_separator('pixel_trans.gif', '10', '1'); ?></td>
              				</tr>
            			</table>
            		</td>
          		</tr>
        	</table>
        </td>
	</tr>
	<tr>
		<td><?php echo tep_draw_separator('pixel_trans.gif', '100%', '10'); ?></td>
	</tr>
	<tr>
		<td>
        	<table border="0" width="100%" cellspacing="1" cellpadding="2" class="buttonBox">
          		<tr class="buttonBoxContents">
            		<td>
            			<table border="0" width="100%" cellspacing="0" cellpadding="2">
              				<tr>
                				<td width="10"><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
                				<td>
									<?=tep_div_button(2, IMAGE_BUTTON_BACK,tep_href_link(FILENAME_ACCOUNT, '', 'SSL'), '', 'gray_button') ?>
								</td>
                				<td align="right">
									<?=tep_div_button(1, IMAGE_BUTTON_CONTINUE,'account_newsletter', 'style="float:right"', 'gray_button') ?>
								</td>
                				<td width="10"><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
              				</tr>
            			</table>
            		</td>
          		</tr>
        	</table>
        </td>
	</tr>
</table>
</form>
<div class="break_line"></div-->