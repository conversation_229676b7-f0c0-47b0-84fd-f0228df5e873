<?=tep_draw_form('account_notifications', tep_href_link(FILENAME_ACCOUNT_NOTIFICATIONS, '', 'SSL')) . tep_draw_hidden_field('action', 'process')?>
			<h1><?=HEADING_TITLE?></h1>
			<div class="solidThickLine"></div>
<table border="0" width="100%" cellspacing="0" cellpadding="0">
	<tr>
		<td class="infoBoxHeading"><b><?php echo MY_NOTIFICATIONS_TITLE; ?></b></td>
	</tr>
	<tr>
		<td>
			<table border="0" width="100%" cellspacing="1" cellpadding="2" class="infoBox">
          		<tr class="infoBoxContents">
            		<td>
            			<table border="0" width="100%" cellspacing="0" cellpadding="2">
              				<tr>
                				<td><?php echo tep_draw_separator('pixel_trans.gif', '10', '1'); ?></td>
                				<td class="main"><?php echo MY_NOTIFICATIONS_DESCRIPTION; ?></td>
                				<td><?php echo tep_draw_separator('pixel_trans.gif', '10', '1'); ?></td>
              				</tr>
            			</table>
            		</td>
				</tr>
        	</table>
        </td>
	</tr>
	<tr>
        <td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
	<tr>
        <td class="infoBoxHeading"><b><?php echo GLOBAL_NOTIFICATIONS_TITLE; ?></b></td>
	</tr>
	<tr>
		<td>
			<table border="0" width="100%" cellspacing="1" cellpadding="2" class="infoBox">
          		<tr class="infoBoxContents">
            		<td>
            			<table border="0" width="100%" cellspacing="0" cellpadding="2">
              				<tr>
                				<td width="10"><?php echo tep_draw_separator('pixel_trans.gif', '10', '1'); ?></td>
                				<td>
                					<table border="0" width="100%" cellspacing="0" cellpadding="2">
                  						<tr class="moduleRow" onmouseover="rowOverEffect(this)" onmouseout="rowOutEffect(this)" onclick="checkBox('product_global')">
                    						<td class="main" width="30"><?=tep_draw_checkbox_field('product_global', '1', (($global['global_product_notifications'] == '1') ? true : false), 'onclick="checkBox(\'product_global\')"')?></td>
                    						<td class="main"><b><?=GLOBAL_NOTIFICATIONS_TITLE?></b></td>
                  						</tr>
                  						<tr>
                    						<td width="30">&nbsp;</td>
                    						<td class="main"><?=GLOBAL_NOTIFICATIONS_DESCRIPTION?></td>
                  						</tr>
                					</table>
                				</td>
                				<td width="10"><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
              				</tr>
            			</table>
            		</td>
          		</tr>
        	</table>
        </td>
	</tr>
	<tr>
		<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
<?
if ($global['global_product_notifications'] != '1') {
?>
	<tr>
		<td class="infoBoxHeading"><b><?=NOTIFICATIONS_TITLE?></b></td>
	</tr>
	<tr>
		<td>
			<table border="0" width="100%" cellspacing="1" cellpadding="2" class="infoBox">
          		<tr class="infoBoxContents">
            		<td>
            			<table border="0" width="100%" cellspacing="0" cellpadding="2">
              				<tr>
                				<td width="10"><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
                				<td>
                					<table border="0" width="100%" cellspacing="0" cellpadding="2">
<?
	$products_check_query = tep_db_query("select count(*) as total from " . TABLE_PRODUCTS_NOTIFICATIONS . " where customers_id = '" . (int)$customer_id . "'");
    $products_check = tep_db_fetch_array($products_check_query);
    if ($products_check['total'] > 0) {
?>
                  						<tr>
                    						<td class="main" colspan="2"><?=NOTIFICATIONS_DESCRIPTION?></td>
                  						</tr>
<?
		$counter = 0;
      	$products_query = tep_db_query("select pd.products_id, pd.products_name from " . TABLE_PRODUCTS_DESCRIPTION . " pd, " . TABLE_PRODUCTS_NOTIFICATIONS . " pn where pn.customers_id = '" . (int)$customer_id . "' and pn.products_id = pd.products_id and pd.language_id = '" . (int)$languages_id . "' order by pd.products_name");
      	while ($products = tep_db_fetch_array($products_query)) {
?>
                  						<tr class="moduleRow" onmouseover="rowOverEffect(this)" onmouseout="rowOutEffect(this)" onclick="checkBox('products[<?php echo $counter; ?>]')">
                    						<td class="main" width="30"><?=tep_draw_checkbox_field('products[' . $counter . ']', $products['products_id'], true, 'onclick="checkBox(\'products[' . $counter . ']\')"')?></td>
                    						<td class="main"><b><?=$products['products_name']?></b></td>
                  						</tr>
<?
			$counter++;
      	}
	} else {
?>
                  						<tr>
                    						<td class="main"><?=NOTIFICATIONS_NON_EXISTING?></td>
                  						</tr>
<?	} ?>
                					</table>
                				</td>
                				<td width="10"><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
              				</tr>
            			</table>
            		</td>
          		</tr>
        	</table>
        </td>
	</tr>
	<tr>
		<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
<?
}
?>
	<tr>
		<td>
			<table border="0" width="100%" cellspacing="1" cellpadding="2" class="buttonBox">
          		<tr class="buttonBoxContents">
            		<td>
            			<table border="0" width="100%" cellspacing="0" cellpadding="2">
              				<tr>
				                <td width="10"><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
				                <td>
				                	<?=tep_div_button(2, IMAGE_BUTTON_BACK,tep_href_link(FILENAME_ACCOUNT, '', 'SSL'), '', 'gray_button') ?>
								</td>
				                <td align="right">
				                	<?=tep_div_button(1, IMAGE_BUTTON_CONTINUE,'account_notifications', 'style="float:right"', 'gray_button') ?>
								</td>
				                <td width="10"><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
              				</tr>
            			</table>
            		</td>
          		</tr>
        	</table>
        </td>
	</tr>
</table>
</form>
		<div class="break_line"></div>