<?=tep_draw_form('account_password', tep_href_link(FILENAME_ACCOUNT_PASSWORD, '', 'SSL'), 'post', 'onSubmit="return check_form(account_password);"') . tep_draw_hidden_field('action', 'process')?>
<table border="0" width="100%" cellspacing="0" cellpadding="0">
	<tr>
		<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
	<tr>
		<td class="pageHeadingTitle" valign="middle" align="center"><?=HEADING_TITLE?></td>
	</tr>
	<tr>
		<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
	<tr>
		<td>
			<div class="row_separator"></div>
		</td>
	</tr>
	<tr>
		<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
<?
if ($messageStack->size('account_password') > 0) {
?>
	<tr>
		<td><?=$messageStack->output('account_password')?></td>
	</tr>
	<tr>
		<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
<?
}
?>
	<tr>
		<td>
			<table border="0" width="100%" cellspacing="0" cellpadding="2" class="inputBox">
          		<tr class="inputBoxContents">
            		<td>
            			<table border="0" width="100%" cellspacing="0" cellpadding="2">
              				<tr>
                				<td class="inputBoxHeading"><b><?=MY_PASSWORD_TITLE?></b></td>
                				<td class="requiredInfo" align="right"><?=FORM_REQUIRED_INFORMATION?></td>
              				</tr>
            			</table>
            		</td>
          		</tr>
          		<tr>
            		<td>
            			<table border="0" width="100%" cellspacing="1" cellpadding="2" class="inputNestedBox">
              				<tr class="inputNestedBoxContents">
                				<td>
                					<table border="0" cellspacing="2" cellpadding="2">
                  						<tr>
						                    <td class="inputLabel"><?=ENTRY_PASSWORD_CURRENT?></td>
						                    <td class="inputField"><?=tep_draw_password_field('password_current') . '&nbsp;' . (tep_not_null(ENTRY_PASSWORD_CURRENT_TEXT) ? '<span class="requiredInfo">' . ENTRY_PASSWORD_CURRENT_TEXT . '</span>': '')?></td>
                  						</tr>
                  						<tr>
                    						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
                  						</tr>
                  						<tr>
						                    <td class="inputLabel"><?=ENTRY_PASSWORD_NEW?></td>
						                    <td class="inputField"><?=tep_draw_password_field('password_new') . '&nbsp;' . (tep_not_null(ENTRY_PASSWORD_NEW_TEXT) ? '<span class="requiredInfo">' . ENTRY_PASSWORD_NEW_TEXT . '</span>': '')?></td>
                  						</tr>
                  						<tr>
						                    <td class="inputLabel"><?=ENTRY_PASSWORD_CONFIRMATION?></td>
						                    <td class="inputField"><?=tep_draw_password_field('password_confirmation') . '&nbsp;' . (tep_not_null(ENTRY_PASSWORD_CONFIRMATION_TEXT) ? '<span class="requiredInfo">' . ENTRY_PASSWORD_CONFIRMATION_TEXT . '</span>': '')?></td>
                  						</tr>
                					</table>
                				</td>
              				</tr>
              				<tr>
								<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
							</tr>
							<tr>
						    	<td>
						    		<table border="0" width="100%" cellspacing="1" cellpadding="2" class="buttonBox2">
						          		<tr class="buttonBoxContents2">
						            		<td>
						            			<table border="0" width="100%" cellspacing="0" cellpadding="2">
						              				<tr>
						                				<td width="10"><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
						                				<td>
															<?=tep_div_button(2, IMAGE_BUTTON_BACK,tep_href_link(FILENAME_ACCOUNT, '', 'SSL'), '', 'gray_button') ?>
														</td>
						                				<td align="right">
						                					<?=tep_div_button(1, IMAGE_BUTTON_CONTINUE,'account_password', 'style="float:right"', 'green_button') ?>
														</td>
						                				<td width="10"><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
						              				</tr>
						            			</table>
						            		</td>
						          		</tr>
						        	</table>
						        </td>
							</tr>
            			</table>
            		</td>
          		</tr>
        	</table>
        </td>
	</tr>
</table>
</form>