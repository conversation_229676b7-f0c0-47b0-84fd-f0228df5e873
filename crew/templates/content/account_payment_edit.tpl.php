<?php 
	if (file_exists(DIR_FS_JAVASCRIPT . 'payment_xmlhttp.js.php')) {
		include_once (DIR_FS_JAVASCRIPT . 'payment_xmlhttp.js.php'); 
	} 
	
	$notice_message_obj = new messageStack;
	$notice_message_obj->add('notice_message', TEXT_ANNOUNCEMENT, 'notice_box');
?>

<script language="javascript" src="<?=DIR_WS_JAVASCRIPT?>customer_xmlhttp.js"></script>
	<h1><?=HEADING_TITLE?></h1>
	<div class="breakLine"><!-- --></div>
<?
if ($notice_message_obj->size('notice_message') > 0) {
?>
	<div class="loginColumnBorder" style="background-color: rgb(255, 253, 219); float: left;">
		<div>
			<div class="breakLine"><!-- --></div>
			<div style="display: block; color: black;"><b><?=$notice_message_obj->output('notice_message')?></b></div>
			<div class="breakLine"><!-- --></div>
		</div>
	</div>
	<div class="breakLine" style="height:10px;"><!-- --></div>
<?
}
?>
	<table border="0" width="100%" cellspacing="0" cellpadding="0">

<?php	if ($messageStack->size('account_payment_edit') > 0) {	?>
		<tr><td><?=$messageStack->output('account_payment_edit')?></td></tr>
		<tr><td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td></tr>
<?php	}	?>
	
		<tr><td><?=$page_obj->get_html_simple_rc_box('',$form_content,13)?></td></tr>
		<tr>
			<td><b><a href="<?=tep_href_link(FILENAME_BUYBACK)?>"><?=TEXT_BUYBACK_LINK?></a></b></td>
		</tr>
	</table>
</form>

<div class="break_line"></div>