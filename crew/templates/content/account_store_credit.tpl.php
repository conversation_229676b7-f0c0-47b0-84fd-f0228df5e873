<script language="javascript" src="<?=DIR_WS_JAVASCRIPT?>customer_xmlhttp.js"></script>
<h1><?=HEADING_TITLE?></h1>
<div class="breakLine"><!-- --></div>
<?
if ($messageStack->size('account_store_credit') > 0) {
?>
<div class="loginColumnBorder" style="background-color: #FFFDDB; padding:2px;">
	<div>
		<div class="breakLine"><!-- --></div>
		<div><?=$messageStack->output('account_store_credit')?></div>
		<div class="breakLine"><!-- --></div>
	</div>
</div>
<div class="breakLine"><!-- --></div>
<?
}
ob_start();
echo tep_draw_form('account_store_credit', tep_href_link(FILENAME_ACCOUNT_STORE_CREDIT, '', 'SSL'), 'post', '') . tep_draw_hidden_field('action', 'process');
?>
	<table border="0" width="100%" cellspacing="5" cellpadding="0">
	<tr>
		<td>
			<div>
				<table border="0" width="100%" cellspacing="1" cellpadding="2">
					<tr>
						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
					</tr>
					<tr class="inputNestedBoxContents">
						<td width="3%">&nbsp;</td>
						<td><h2><?=TABLE_HEADING_STORE_CREDIT_CONVERSION?></h2></td>
					</tr>
					<tr class="inputNestedBoxContents">
						<td width="3%">&nbsp;</td>
						<td><?=sprintf(TABLE_HEADING_CONVERSION_TEXT, $currencies->currencies[$sc_currency_code]['symbol_left'].$currencies->currencies[$sc_currency_code]['symbol_right'])?></td>
					</tr>
					<tr>
						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
					</tr>
					<tr class="inputNestedBoxContents">
						<td width="3%">&nbsp;</td>
						<td>
							<table border="0" cellspacing="2" cellpadding="2" width="100%">
								<tr>
									<td class="inputLabelNew" align="right" valign="top" width="30%"><?=ENTRY_FORM_MY_STORE_CREDIT?></td>
									<td class="inputFieldBold"><?=$currencies->currencies[$sc_currency_code]['symbol_left'].number_format($store_credit_total_amount, $currencies->currencies[$sc_currency_code]['decimal_places'], $currencies->currencies[$sc_currency_code]['decimal_point'], $currencies->currencies[$sc_currency_code]['thousands_point']).$currencies->currencies[$sc_currency_code]['symbol_right']?></td>
								</tr>
<? 	if ($customer_currency_turned_off === false) { ?>
<? 		if ($sc_pending_payments_found == true) { ?>
								<tr>
									<td class="inputLabelNew" align="right" valign="top"></td>
									<td class="inputField" valign="top"><?php echo tep_image(DIR_WS_ICONS . "warning.png");?>&nbsp;<span class="messageStackError"><?=$warning_message?></span></td>
								</tr>
<? 		} else { ?>
								<tr>
									<td class="inputLabelNew" align="right" valign="top"></td>
									<td class="inputFieldBold"><div id="exchange_rate_div"></div></td>
								</tr>
<?      } ?>
								<tr>
									<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
								</tr>
								<tr>
									<td class="inputLabelNew" align="right" valign="top"><?=ENTRY_FORM_CONVERT_TO?></td>
									<td class="inputField">
										<?php
											if (count($currency_drop_down_array) > 1) {
												echo tep_draw_pull_down_menu('currency_selection', $currency_drop_down_array, $to_currency, 'id="currency_selection" onchange="updateExchangeRate(\'currency_selection\', \''.$customer_id.'\', \'refresh_exchange_rate\');"');
											} else {
												echo '<span class="messageStackError">'.tep_image(DIR_WS_ICONS . "warning.png").'&nbsp;&nbsp;' . ERROR_NO_CONVERSION_ALLOW_GST . '</span>';
											}
										?>
									</td>
								</tr>
								<tr>
									<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
								</tr>
								<!--tr>
									<td class="inputLabelNew" align="right" valign="top"><?=ENTRY_FORM_NEW_STORE_CREDIT?></td>
									<td class="inputFieldBold"><div id="exchange_rate_div"></div></td>
								</tr>
								<tr>
									<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
								</tr-->
<? 		if ($sc_pending_payments_found == false && (count($currency_drop_down_array) > 1)) { ?>
								<tr>
									<td><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
									<td><?	echo tep_div_button(1, IMAGE_BUTTON_CONVERT_NOW, 'account_store_credit', '', 'gray_button'); ?></td>
								</tr>
<? 		}
	} else { ?>
								<tr>
									<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
								</tr>
								<tr>
									<td class="inputLabelNew" align="right" valign="top"><span class="errorText"><?php echo tep_image(DIR_WS_ICONS . "warning.png");?>&nbsp;&nbsp;<?=ENTRY_FORM_ATTENTION?></span></td>
									<td class="inputField" valign="top"><span class="messageStackError"><?=ERROR_INVALID_STORE_CREDIT_CURRENCY?></span></td>
								</tr>
<? 	} ?>
							</table>
						</td>
					</tr>
					<tr>
						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
					</tr>
            	</table>
			</div>
		</td>
	</tr>
	</table>
	</form>
<?
$sc_conversion_content_string = ob_get_contents();
ob_end_clean(); 

echo $page_obj->get_html_simple_rc_box('',$sc_conversion_content_string,13); 
?>
<div class="breakLine" style="height:10px;"><!-- --></div>
	
<? ob_start(); ?>
	<div class="breakLine"></div>
	<div class="boxHeader" style="width:100%;">
		<div class="boxHeaderLeft"><!-- --></div>
		<div class="boxHeaderCenter" style="width:720px;display:inline;">
			<font style="display:inline-block; color:#fff; padding-top:5px;"><?=QUESTION_AND_ANSWER_HEADER ?></font>
		</div>
		<div class="boxHeaderRight"><!-- --></div>
	</div>
	<div class="breakLine"><!-- --></div>
	
	<div style="padding:5px;"><?=QUESTION_AND_ANSWER_CONTENTS?></div>
<?
$sc_qna_content_string = ob_get_contents();
ob_end_clean(); 

echo $page_obj->get_html_simple_rc_box('',$sc_qna_content_string,13); 
?>
	
<? if (isset($_REQUEST['to_curr']) && tep_not_null($_REQUEST['to_curr'])) { ?>
<script language="JavaScript">
updateExchangeRate('currency_selection', '<?=$customer_id?>', 'refresh_exchange_rate');
</script>
<? } ?>
<div class="break_line"></div>
