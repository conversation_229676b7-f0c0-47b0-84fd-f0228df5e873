<h1><?=HEADING_TITLE ?></h1> 
<div class="breakLine"></div>

<?=tep_draw_form('account_store_credit_history', tep_href_link(FILENAME_ACCOUNT_STORE_CREDIT_HISTORY, '', 'SSL'), 'post', '') . tep_draw_hidden_field('action', 'process');?>
<table border="0" width="100%" cellspacing="0" cellpadding="0">
<?
if ($messageStack->size('account_store_credit_history') > 0) {
?>
	<tr>
		<td><?=$messageStack->output('account_store_credit_history')?></td>
	</tr>
	<tr>
		<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
<?
}
?>
	<tr>
		<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
	<tr>
		<td>
<? ob_start(); ?>
				<table border="0" width="100%" cellspacing="1" cellpadding="2">
					<tr>
						<td colspan="3"><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
					</tr>
					<tr class="inputNestedBoxContents">
						<td width="3%">&nbsp;</td>
						<td><h2><?=TABLE_HEADING_STORE_CREDIT_HISTORY?></h2></td>
						<td width="3%">&nbsp;</td>
					</tr>
					<tr>
						<td colspan="3"><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
					</tr>
					<tr class="inputNestedBoxContents">
						<td width="3%">&nbsp;</td>
						<td>
							<div>					
								<div class="tab2Header">
									<ul class="tab2">
										<li class="<?=($tab==1||$tab=='')?'tab2Selected':'tab2NotSelected'?>" id="tab2contentitem1"><a href="javascript:selectTab('tab2content','1','3')"><span><font><?=TAB_HEADING_HISTORY ?></font></span></a></li>
										<li class="<?=($tab==2)?'tab2Selected':'tab2NotSelected'?>" id="tab2contentitem2"><a href="javascript:selectTab('tab2content','2','3')"><span><font><?=TAB_HEADING_STORE_CREDIT_PURCHASED ?></font></span></a></li>
										<li class="<?=($tab==3)?'tab2Selected':'tab2NotSelected'?>" id="tab2contentitem3"><a href="javascript:selectTab('tab2content','3','3')"><span><font><?=TAB_HEADING_STORE_CREDIT_USED ?></font></span></a></li>
									</ul>
								</div>
								<!-- BEGIN HISTORY //-->
								<div class="tab2Content" id="tab2content1">
									<table border="0" width="100%" cellspacing="1" cellpadding="2">
										<tr>
											<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
										</tr>
										<tr>
											<td colspan="2">
												<div class="bodyContentLeft" style="width: 640px;">
													<div class="boxHeader" style="width: 640px;">
														<div class="boxHeaderLeft"></div>
														<div class="boxHeaderCenter" style="width: 205px;"><font><?=TABLE_HEADING_HISTORY_DATE?></font></div>
														<div class="boxHeaderCenter" style="width: 205px;"><font><?=TABLE_HEADING_HISTORY_TYPE?></font></div>
														<div class="boxHeaderCenter" style="width: 210px;"><font><?=TABLE_HEADING_HISTORY_AMOUNT?></font></div>
														<!-- div class="boxHeaderCenter" style="width: 155px;"><font><?=TABLE_HEADING_HISTORY_BALANCE?></font></div //-->
														<div class="boxHeaderRight"></div>
													</div>
													<div class="boxHeader" style="width: 640px;">
														<div><table><tr><td></td></tr></table></div>
													</div>
					<?
						$sc_history_select_sql = "select store_credit_history_date, store_credit_history_currency_id, " .
												" sum(store_credit_history_debit_amount) as store_credit_history_debit_amount, " .
												" sum(store_credit_history_credit_amount) as store_credit_history_credit_amount, " .
												" sum(IF(store_credit_account_type='R', store_credit_history_r_after_balance, 0)) as store_credit_history_r_after_balance, " .
												" sum(IF(store_credit_account_type='NR', store_credit_history_nr_after_balance, 0)) as store_credit_history_nr_after_balance, " .
												" store_credit_account_type, store_credit_history_trans_type, store_credit_activity_type, store_credit_history_activity_desc " .
												" from " . TABLE_STORE_CREDIT_HISTORY . 
												" where customer_id = '" . tep_db_input($_SESSION['customer_id']) . "'" .
												" AND (store_credit_activity_type != 'X' " .
												" AND store_credit_activity_type != 'RP') " .
												" AND (store_credit_history_date >= DATE_SUB(now(), INTERVAL 3 MONTH)) " .
												" group by store_credit_history_date, store_credit_activity_type, store_credit_history_currency_id " .
												" order by store_credit_history_date DESC, store_credit_history_id DESC";
						$history_split = new splitPageResults($sc_history_select_sql, MAX_DISPLAY_ORDER_HISTORY, 'store_credit_history_id', 'tab1_page');
						$history_query = tep_db_query($history_split->sql_query);
						while ($sc_history_row = tep_db_fetch_array($history_query)) {
							$sc_hist_date_str = date("d M Y,l", strtotime($sc_history_row['store_credit_history_date']));
							$sc_hist_curr_id = $sc_history_row['store_credit_history_currency_id'];
							$sc_hist_curr_str = $currencies->get_code_by_id($sc_hist_curr_id);
							
							$sc_hist_debit_amt = $sc_history_row['store_credit_history_debit_amount'];
							$sc_hist_credit_amt = $sc_history_row['store_credit_history_credit_amount'];
							$sc_hist_amt_str = $currencies->format($sc_hist_credit_amt-$sc_hist_debit_amt, false, $sc_hist_curr_str);
							$sc_hist_r_amt = $sc_history_row['store_credit_history_r_after_balance'];
							$sc_hist_nr_amt = $sc_history_row['store_credit_history_nr_after_balance'];
							$sc_hist_bal_amt_str = $currencies->format($sc_hist_r_amt+$sc_hist_nr_amt, false, $sc_hist_curr_str);
							
							switch ($sc_history_row['store_credit_activity_type']) {
								case 'MI':
									$sc_hist_activitiy_str = TEXT_SC_HISTORY_STATUS_MI;
									break;
								case 'MR':
									$sc_hist_activitiy_str = TEXT_SC_HISTORY_STATUS_MR;
									break;
								case 'P':
									$sc_hist_activitiy_str = TEXT_SC_HISTORY_STATUS_P;
									break;
								case 'PW':
									$sc_hist_activitiy_str = TEXT_SC_HISTORY_STATUS_PW;
									break;
								case 'R':
									$sc_hist_activitiy_str = TEXT_SC_HISTORY_STATUS_R;
									break;
								case 'V':
									$sc_hist_activitiy_str = TEXT_SC_HISTORY_STATUS_V;
									break;
								case 'C':
									$sc_hist_activitiy_str = TEXT_SC_HISTORY_STATUS_C;
									break;
								case 'D':
									$sc_hist_activitiy_str = TEXT_SC_HISTORY_STATUS_D;
									break;
								case 'S':
									if (($sc_hist_credit_amt-$sc_hist_debit_amt) > 0) {
										$sc_hist_activitiy_str = TEXT_SC_HISTORY_STATUS_S;
									} else {
										$sc_hist_activitiy_str = TEXT_SC_HISTORY_STATUS_S_DEDUCT;
									}
									break;
								case 'XS':
									$sc_hist_comment = explode('% extra', $sc_history_row['store_credit_history_activity_desc']);
									$extra_percent = $sc_hist_comment[0];
									if (($sc_hist_credit_amt-$sc_hist_debit_amt) > 0) {
										$sc_hist_activitiy_str = sprintf(TEXT_SC_HISTORY_STATUS_XS, $extra_percent);
									} else {
										$sc_hist_activitiy_str = sprintf(TEXT_SC_HISTORY_STATUS_XS_DEDUCT, $extra_percent);
									}
									break;
                                case 'GC':
                                    $sc_hist_activitiy_str = TEXT_SC_HISTORY_STATUS_GC;
                                    break;
								default :
									$sc_hist_activitiy_str = $sc_history_row['store_credit_activity_type'];
									break;
							}
					
					?>
													<div class="boxContent" style="width: 640px;">
														<div style="width: 198px; min-height: 25px; margin: 0px 5px; float:left;"><?=$sc_hist_date_str?></div>
														<div style="width: 194px; min-height: 25px; margin: 0px 5px; float:left;"><?=$sc_hist_activitiy_str?></div>
														<div style="width: 200px; min-height: 25px; margin: 0px 5px; float:left;"><?=$sc_hist_amt_str?></div>
														<!-- div style="width: 150px; min-height: 25px; margin: 0px 5px; float:left;"><?=$sc_hist_bal_amt_str?></div //-->
													</div>
													<div class="boxHeader" style="width: 640px;">
														<div class="row_separator"><table><tr><td></td></tr></table></div>
													</div>
					<?
						}
					?>
												</div>
												<div class="breakLine"><!-- --></div>
												<div id="pager" class="boxHeader" style="width:99%; text-align:right;">
													<input type="hidden" name="direction" id="direction" value="<?=$direction?>">
													<span style="display:inline-block;"><?=$history_split->display_links(MAX_DISPLAY_PAGE_LINKS, tep_get_all_get_params(array('tab1_page', 'info', 'x', 'y', 'tab'))."&tab=1"); ?></span>
												</div>
											</td>
										</tr>
									</table>
								</div>
								<!-- END HISTORY //-->
								<!-- BEGIN SC PURCHASED //-->
								<div class="tab2Content" id="tab2content2" style="display:none">
									<table border="0" width="100%" cellspacing="1" cellpadding="2">
										<tr>
											<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
										</tr>
										<tr>
											<td colspan="2">
												<div class="bodyContentLeft" style="width: 640px;">
													<div class="boxHeader" style="width: 640px;">
														<div class="boxHeaderLeft"></div>
														<div class="boxHeaderCenter" style="width: 205px;"><font><?=TABLE_HEADING_SC_PURCHASED_DATE?></font></div>
														<div class="boxHeaderCenter" style="width: 205px;"><font><?=TABLE_HEADING_SC_PURCHASED_ORDER?></font></div>
														<div class="boxHeaderCenter" style="width: 210px;"><font><?=TABLE_HEADING_SC_PURCHASED_AMOUNT?></font></div>
														<div class="boxHeaderRight"></div>
													</div>
													<div class="boxHeader" style="width: 640px;">
														<div><table><tr><td></td></tr></table></div>
													</div>
					<?
						$sc_purchased_select_sql = 	"select o.orders_id, o.date_purchased, op.products_quantity, ocp.orders_custom_products_value " .
													" from " . TABLE_ORDERS . " as o" .
													" inner join " . TABLE_ORDERS_PRODUCTS . " as op " .
													" 	on (o.orders_id = op.orders_id " . 
													" 		and (" .
													"			op.custom_products_type_id = '3' " . 
													"			or op.parent_orders_products_id in (select distinct op2.orders_products_id from " . TABLE_ORDERS_PRODUCTS . " as op2 where op2.orders_id = o.orders_id and op2.custom_products_type_id = '3')" . 
													" 			) " .
													"	) " .
													" inner join " . TABLE_ORDERS_CUSTOM_PRODUCTS . " as ocp " .
													" 	on (op.orders_products_id = ocp.orders_products_id and ocp.orders_custom_products_key = 'store_credit_currency') " . 
													" where o.customers_id = '" . tep_db_input($_SESSION['customer_id']) . "'" .
													" and op.orders_products_is_compensate = 0 " .
													" and (date_purchased >= DATE_SUB(now(), INTERVAL 3 MONTH)) " .
													" order by o.orders_id DESC";
						$purchase_split = new splitPageResults($sc_purchased_select_sql, MAX_DISPLAY_ORDER_HISTORY, 'o.orders_id', 'tab2_page');
						$purchase_query = tep_db_query($purchase_split->sql_query);
						while ($sc_purchase_row = tep_db_fetch_array($purchase_query)) {
							$sc_buy_date_str = date("d M Y,l", strtotime($sc_purchase_row['date_purchased']));
							$sc_buy_order_id = $sc_purchase_row['orders_id'];
							$sc_buy_curr_id = $sc_purchase_row['orders_custom_products_value'];
							$sc_buy_curr_str = $currencies->get_code_by_id($sc_buy_curr_id);
							$sc_buy_debit_amt = $sc_purchase_row['products_quantity'];
							$sc_buy_amt_str = $currencies->format($sc_buy_debit_amt, false, $sc_buy_curr_str);
					?>
													<div class="boxContent" style="width: 640px;">
														<div style="width: 198px; min-height: 25px; margin: 0px 5px; float:left;"><?=$sc_buy_date_str?></div>
														<div style="width: 194px; min-height: 25px; margin: 0px 5px; float:left;"><?=$sc_buy_order_id?></div>
														<div style="width: 200px; min-height: 25px; margin: 0px 5px; float:left;"><?=$sc_buy_amt_str?></div>
													</div>
													<div class="boxHeader" style="width: 640px;">
														<div class="row_separator"><table><tr><td></td></tr></table></div>
													</div>
					<?
						}
					?>
												</div>
												<div class="breakLine"><!-- --></div>
												<div id="pager" class="boxHeader" style="width:99%; text-align:right;">
													<input type="hidden" name="direction" id="direction" value="<?=$direction?>">
													<span style="display:inline-block;"><?=$purchase_split->display_links(MAX_DISPLAY_PAGE_LINKS, tep_get_all_get_params(array('tab2_page', 'info', 'x', 'y', 'tab'))."&tab=2"); ?></span>
												</div>
											</td>
										</tr>
									</table>
								</div>
								<!-- END SC PURCHASED //-->
								<!-- BEGIN SC USED //-->
								<div class="tab2Content" id="tab2content3" style="display:none">
									<table border="0" width="100%" cellspacing="1" cellpadding="2">
										<tr>
											<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
										</tr>
										<tr>
											<td colspan="2">
												<div class="bodyContentLeft" style="width: 640px;">
													<div class="boxHeader" style="width: 640px;">
														<div class="boxHeaderLeft"></div>
														<div class="boxHeaderCenter" style="width: 205px;"><font><?=TABLE_HEADING_SC_PURCHASED_DATE?></font></div>
														<div class="boxHeaderCenter" style="width: 205px;"><font><?=TABLE_HEADING_SC_PURCHASED_ORDER?></font></div>
														<div class="boxHeaderCenter" style="width: 210px;"><font><?=TABLE_HEADING_SC_PURCHASED_AMOUNT?></font></div>
														<div class="boxHeaderRight"></div>
													</div>
													<div class="boxHeader" style="width: 640px;">
														<div><table><tr><td></td></tr></table></div>
													</div>
					<?
						$sc_used_select_sql = 	"select store_credit_history_date, store_credit_history_currency_id, " .
												" sum(store_credit_history_debit_amount) as store_credit_history_debit_amount, store_credit_history_trans_id, " .
												" store_credit_history_trans_type, store_credit_activity_type " .
												" from " . TABLE_STORE_CREDIT_HISTORY . 
												" where customer_id = '" . tep_db_input($_SESSION['customer_id']) . "'" .
												" AND store_credit_activity_type = 'P' " .
												" AND store_credit_history_trans_type = 'C' " .
												" AND store_credit_history_added_by_role = 'customers' " . 
												" AND (store_credit_history_date >= DATE_SUB(now(), INTERVAL 3 MONTH)) " .
												" group by store_credit_history_trans_id, store_credit_history_currency_id " .
												" order by store_credit_history_date DESC, store_credit_history_id DESC";
						$used_split = new splitPageResults($sc_used_select_sql, MAX_DISPLAY_ORDER_HISTORY, 'store_credit_history_trans_id', 'tab3_page');
						$used_query = tep_db_query($used_split->sql_query);
						while ($sc_used_row = tep_db_fetch_array($used_query)) {
							$sc_used_date_str = date("d M Y,l", strtotime($sc_used_row['store_credit_history_date']));
							$sc_used_order_id = $sc_used_row['store_credit_history_trans_id'];
							$sc_used_curr_id = $sc_used_row['store_credit_history_currency_id'];
							$sc_used_curr_str = $currencies->get_code_by_id($sc_used_curr_id);
							$sc_used_debit_amt = $sc_used_row['store_credit_history_debit_amount'];
							$sc_used_amt_str = $currencies->format($sc_used_debit_amt, false, $sc_used_curr_str);
					?>
													<div class="boxContent" style="width: 640px;">
														<div style="width: 198px; min-height: 25px; margin: 0px 5px; float:left;"><?=$sc_used_date_str?></div>
														<div style="width: 194px; min-height: 25px; margin: 0px 5px; float:left;"><?=$sc_used_order_id?></div>
														<div style="width: 200px; min-height: 25px; margin: 0px 5px; float:left;"><?=$sc_used_amt_str?></div>
													</div>
													<div class="boxHeader" style="width: 640px;">
														<div class="row_separator"><table><tr><td></td></tr></table></div>
													</div>
					<?
						}
					?>
												</div>
												<div class="breakLine"><!-- --></div>
												<div id="pager" class="boxHeader" style="width:99%; text-align:right;">
													<input type="hidden" name="direction" id="direction" value="<?=$direction?>">
													<span style="display:inline-block;"><?=$used_split->display_links(MAX_DISPLAY_PAGE_LINKS, tep_get_all_get_params(array('tab3_page', 'info', 'x', 'y', 'tab'))."&tab=3"); ?></span>
												</div>
											</td>
										</tr>
									</table>
								</div>
								<!-- END SC USED //-->
							</div>
						</td>
						<td width="3%">&nbsp;</td>
					</tr>
					<tr>
						<td colspan="3"><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
					</tr>
					<tr>
						<td width="3%">&nbsp;</td>
						<td align="right">
							<table border="0" cellspacing="1" cellpadding="2">
								<tr>
									<td>
										<?=sprintf(TEXT_MY_STORE_CREDIT_BALANCE, $sc_total_amt_str)?>&nbsp;
									</td>
									<td>
										<?	echo tep_div_button(2, IMAGE_BUTTON_TOP_UP_STORE_CREDITS, tep_href_link(FILENAME_ACCOUNT_STORE_CREDIT_TOPUP, '', 'SSL'), '', 'gray_button'); ?>
									</td>
								</tr>
								<tr>
									<td></td>
									<td>
										<?='&nbsp;&nbsp;<a href="' . $printStoreCreditLink . '">'.TEXT_OR_CONVERT_NOW.'</a>'?>
									</td>
								</tr>
							</table>
						</td>
						<td width="3%">&nbsp;</td>
					</tr>
            	</table>
<?
$sc_history_content_string = ob_get_contents();
ob_end_clean(); 

echo $page_obj->get_html_simple_rc_box('',$sc_history_content_string,13); 
?>
		</td>
	</tr>
</table>
</form>
<script language="javascript">
function selectTab(tabname,tabid,totaltab) {
	for (var i = 1 ; i <= totaltab ; i++) {
		jQuery('#' + tabname + i).hide();
		jQuery('#'+ tabname + 'item' + i).attr('class','tab2NotSelected');
	}
	jQuery('#' + tabname + tabid).show();
	jQuery('#'+ tabname + 'item' + tabid).attr('class','tab2Selected');
}
<?
if (tep_not_null($tab)) {
	echo "selectTab('tab2content','".$tab."','3')";
}
?>
</script>
<div class="break_line"></div>
