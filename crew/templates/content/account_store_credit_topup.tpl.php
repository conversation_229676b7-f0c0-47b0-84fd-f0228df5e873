<h1><?php echo HEADING_TITLE; ?></h1>
<div class="breakLine"></div>
<?php
$promo_message_obj = new messageStack;
$sc_promotion_percentage = store_credit::get_sc_promotion_percentage();
if ($sc_promotion_percentage > 0) {
    $promo_message_obj->add('promo_message', sprintf(TEXT_INFO_EXTRA_SC_PROMO, ($sc_promotion_percentage == (int) $sc_promotion_percentage ? (int) $sc_promotion_percentage : $sc_promotion_percentage)), '');
}

if ($promo_message_obj->size('promo_message') > 0) {
    ?>
    <div class="loginColumnBorder" style="background-color: rgb(255, 253, 219); float: left;">
        <div>
            <div class="breakLine"><!-- --></div>
            <div style="display: block; color: black;"><?= $promo_message_obj->output('promo_message') ?></div>
            <?php
            if (in_array($_SESSION['customers_groups_id'], array('2', '3', '12'))) {
                echo '<div style="padding-left:5px; color: red;">' . TEXT_LAST_DAY_ENJOY_EXTRA_SC . '</div>';
            }
            ?>
            <div class="breakLine"><!-- --></div>
        </div>
    </div>
    <div class="breakLine" style="height:10px;"><!-- --></div>
    <?
}
ob_start();
?>
<table border="0" width="100%" cellspacing="5" cellpadding="0">
    <tr>
        <td valign="top">
            <div id="list_product"><?= $page_info->get_store_credits_list($cPath_array) ?></div>
        </td>
    </tr>
</table>
<div class="breakLine" style="height:10px;"><!-- --></div>
<?
$sc_list_content_string = ob_get_contents();
ob_end_clean();

echo $page_obj->get_html_simple_rc_box('', $sc_list_content_string, 13);
?>
<div class="breakLine"></div>
<? ob_start(); ?>
<div class="boxHeader" style="width:100%;">
    <div class="boxHeaderLeft"><!-- --></div>
    <div class="boxHeaderCenter" style="width:720px;display:inline;">
        <font style="display:inline-block; color:#fff; padding-top:5px;"><?= QUESTION_AND_ANSWER_HEADER ?></font>
    </div>
    <div class="boxHeaderRight"><!-- --></div>
</div>
<div class="breakLine"><!-- --></div>

<div style="padding:5px;"><?= QUESTION_AND_ANSWER_CONTENTS ?></div>
<div class="break_line"></div>
<?
$sc_qna_content_string = ob_get_contents();
ob_end_clean();

echo $page_obj->get_html_simple_rc_box('', $sc_qna_content_string, 13);
?>
<script language="JavaScript" type="text/javascript">
    function validate_minimum_sc(minimum_sc, entered_sc) {
        entered_sc = entered_sc || 0;
        if (parseInt(entered_sc) < parseInt(minimum_sc)) {
            alert('<?= TEXT_INFO_MINIMUM_ORDERS_SC ?>');
            return false;
        } else {
            return true;
        }
    }

    function update_promo_amount(pass_id, pass_amount, pass_rate, pass_left_symbol, pass_right_symbol) {
        var pass_img = '<?= tep_image(DIR_WS_ICONS . "promo.jpg", ICON_PROMO, "", "", "style=\"vertical-align:middle;\"") ?>';
        jQuery("#update_promo_amount_"+pass_id).html(pass_img + "&nbsp;&nbsp;<?= TEXT_INFO_ENTERED_SC_PROMO_AMOUNT1 ?><b>"+ pass_left_symbol + (pass_amount*1).toFixed(2) + pass_right_symbol + "</b><?= TEXT_INFO_ENTERED_SC_PROMO_AMOUNT2 ?><b>"+ pass_left_symbol + (pass_amount*pass_rate).toFixed(2) + pass_right_symbol + "</b><?= TEXT_INFO_ENTERED_SC_PROMO_AMOUNT3 ?>");
    }

</script>