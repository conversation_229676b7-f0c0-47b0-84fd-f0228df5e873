			<h1><?=HEADING_TITLE?></h1>
			<div class="solidThickLine"></div>

<table border="0" width="100%" cellspacing="0" cellpadding="0">
<?
if ($messageStack->size('addressbook') > 0) {
?>
	<tr>
		<td><?=$messageStack->output('addressbook')?></td>
	</tr>
	<tr>
		<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
<?
}
?>
	<tr>
		<td class="infoBoxHeading"><b><?=PRIMARY_ADDRESS_TITLE?></b></td>
	</tr>
	<tr>
		<td>
			<table border="0" width="100%" cellspacing="1" cellpadding="2" class="infoBox">
          		<tr class="infoBoxContents">
            		<td>
            			<table border="0" width="100%" cellspacing="0" cellpadding="2">
              				<tr>
              					<td class="main" width="100%" valign="top"><?=PRIMARY_ADDRESS_DESCRIPTION?></td>
              				</tr>
              				<tr>
								<td align="left" width="100%" valign="top">
                					<table border="0" cellspacing="0" cellpadding="2">
                  						<tr>
                    						<td class="main" align="left" valign="top">
                    							<b><?=PRIMARY_ADDRESS_TITLE?></b>
                    						</td>
                  						</tr>
                  						<tr>
                  							<td class="main" valign="middle" align="left">
                  								<?=tep_address_label($customer_id, $_SESSION['customer_default_address_id'], true, ' ', '<br>')?>
                  							</td>
                  						</tr>
                					</table>
                				</td>
              				</tr>
            			</table>
            		</td>
          		</tr>
        	</table>
        </td>
	</tr>
	<tr>
		<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
	<tr>
		<td class="infoBoxHeading"><b><?=ADDRESS_BOOK_TITLE?></b></td>
	</tr>
	<tr>
		<td>
			<table border="0" width="100%" cellspacing="1" cellpadding="2" class="infoBox">
          		<tr class="infoBoxContents">
            		<td>
            			<table border="0" width="100%" cellspacing="0" cellpadding="2">
<?
$addresses_query = tep_db_query("SELECT address_book_id, entry_firstname as firstname, entry_lastname as lastname, entry_company as company, entry_street_address as street_address, entry_suburb as suburb, entry_city as city, entry_postcode as postcode, entry_state as state, entry_zone_id as zone_id, entry_country_id as country_id FROM " . TABLE_ADDRESS_BOOK . " WHERE customers_id = '" . (int)$customer_id . "' ORDER BY firstname, lastname");
while ($addresses = tep_db_fetch_array($addresses_query)) {
	$format_id = tep_get_address_format_id($addresses['country_id']);
?>
							<tr>
                				<td><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
                				<td>
                					<table border="0" width="100%" cellspacing="0" cellpadding="2">
                  						<!--tr class="moduleRow" onmouseover="rowOverEffect(this)" onmouseout="rowOutEffect(this)" onClick="document.location.href='<?php echo tep_href_link(FILENAME_ADDRESS_BOOK_PROCESS, 'edit=' . $addresses['address_book_id'], 'SSL'); ?>'"-->
                  						<tr class="moduleRow" onmouseover="rowOverEffect(this)" onmouseout="rowOutEffect(this)" >
                    						<td class="main"><b><?=tep_output_string_protected($addresses['firstname'] . ' ' . $addresses['lastname']); ?></b><? if ($addresses['address_book_id'] == $_SESSION['customer_default_address_id']) echo '&nbsp;<small><i>' . PRIMARY_ADDRESS . '</i></small>'?></td>
                    						<td class="main" align="right">
                    							<table cellpadding=0 cellspacing=0 border=0><tr><td>
                    							<?=tep_div_button(2, SMALL_IMAGE_BUTTON_EDIT,tep_href_link(FILENAME_ADDRESS_BOOK_PROCESS, 'edit=' . $addresses['address_book_id'], 'SSL'), '', 'gray_button') ?>
                    							<?=tep_div_button(2, SMALL_IMAGE_BUTTON_DELETE,tep_href_link(FILENAME_ADDRESS_BOOK_PROCESS, 'delete=' . $addresses['address_book_id'], 'SSL'), '', 'gray_button') ?>
                    							</td></tr></table>
											</td>
                  						</tr>
                  						<tr>
                    						<td colspan="2">
                    							<table border="0" cellspacing="0" cellpadding="2">
                      								<tr>
                        								<td width="10"><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
								                        <td class="main"><?=tep_address_format($format_id, $addresses, true, ' ', '<br>')?></td>
								                        <td width="10"><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
                      								</tr>
                    							</table>
                    						</td>
                  						</tr>
                					</table>
                				</td>
                				<td><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
              				</tr>
<?
}
?>
            			</table>
            		</td>
          		</tr>
        	</table>
        </td>
	</tr>
	<tr>
		<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
	<tr>
		<td>
			<table border="0" width="100%" cellspacing="1" cellpadding="2" class="buttonBox">
          		<tr class="buttonBoxContents">
            		<td>
            			<table border="0" width="100%" cellspacing="0" cellpadding="2">
              				<tr>
                				<td width="10"><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
                				<td class="main">
                					<?=tep_div_button(2, IMAGE_BUTTON_BACK,tep_href_link(FILENAME_ACCOUNT, '', 'SSL'), '', 'gray_button') ?>
								</td>
<?
if (tep_count_customer_address_book_entries() < MAX_ADDRESS_BOOK_ENTRIES) {
?>
                				<td class="main" align="right">
                					<?=tep_div_button(2, IMAGE_BUTTON_ADD_ADDRESS,tep_href_link(FILENAME_ADDRESS_BOOK_PROCESS, '', 'SSL'), 'style="float: right"', 'gray_button') ?>
								</td>
<?
}
?>
                				<td width="10"><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
              				</tr>
            			</table>
            		</td>
          		</tr>
        	</table>
        </td>
	</tr>
	<tr>
		<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
	<tr>
		<td class="smallText"><?=sprintf(TEXT_MAXIMUM_ENTRIES, MAX_ADDRESS_BOOK_ENTRIES)?></td>
	</tr>
</table>
		<div class="break_line"></div>