			<h1><?=HEADING_TITLE?></h1>
			<div class="solidThickLine"></div>

<?
if (!isset($HTTP_GET_VARS['delete'])) 
	echo tep_draw_form('addressbook', tep_href_link(FILENAME_ADDRESS_BOOK_PROCESS, tep_get_all_get_params(), 'SSL'), 'post', 'onSubmit="return check_form(addressbook);"');
?>
	<table border="0" width="100%" cellspacing="0" cellpadding="0">
<?
if ($messageStack->size('addressbook') > 0) {
?>
      	<tr>
        	<td><?=$messageStack->output('addressbook')?></td>
      	</tr>
      	<tr>
        	<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
      	</tr>
<?
}

if (isset($HTTP_GET_VARS['delete'])) {
?>
		<tr>
        	<td class="addressBoxHeading"><?=DELETE_ADDRESS_TITLE?></td>
      	</tr>
      	<tr>
        	<td>
        		<table border="0" width="100%" cellspacing="1" cellpadding="2" class="infoBox">
          			<tr class="infoBoxContents">
            			<td>
            				<table border="0" width="100%" cellspacing="0" cellpadding="2">
              					<tr>
              						<td class="actionMsg" width="100%" valign="top"><?=DELETE_ADDRESS_DESCRIPTION?></td>
              					</tr>
              					<tr>
                					<td align="left" width="100%" valign="top">
                						<table border="0" cellspacing="0" cellpadding="2">
                  							<tr>
                    							<td class="addressTitle" align="left" valign="top"><?=SELECTED_ADDRESS?></td>
                  							</tr>
                  							<tr>
                  								<td class="address" valign="middle" align="left">
                  									<?=tep_address_label($customer_id, $HTTP_GET_VARS['delete'], true, ' ', '<br>')?>
                  								</td>
                  							</tr>
                						</table>
                					</td>
              					</tr>
            				</table>
            			</td>
          			</tr>
        		</table>
        	</td>
      	</tr>
      	<tr>
        	<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
      	</tr>
      	<tr>
        	<td>
        		<table border="0" width="100%" cellspacing="1" cellpadding="2" class="buttonBox">
          			<tr class="buttonBoxContents">
            			<td>
            				<table border="0" width="100%" cellspacing="0" cellpadding="2">
              					<tr>
                					<td width="10"><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
                					<td>
                						<?=tep_div_button(2, IMAGE_BUTTON_BACK,tep_href_link(FILENAME_ADDRESS_BOOK, '', 'SSL'), '', 'gray_button') ?>
									</td>
                					<td align="right">
                						<?=tep_div_button(2, IMAGE_BUTTON_DELETE,tep_href_link(FILENAME_ADDRESS_BOOK_PROCESS, 'delete=' . $HTTP_GET_VARS['delete'] . '&action=deleteconfirm', 'SSL'), 'style="float:right"', 'gray_button') ?>
									</td>
                					<td width="10"><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
              					</tr>
            				</table>
            			</td>
          			</tr>
        		</table>
        	</td>
      	</tr>
<?
} else {
?>
      	<tr>
        	<td><? include(DIR_WS_MODULES . 'address_book_details.php'); ?></td>
      	</tr>
      	<tr>
        	<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
      	</tr>
<?
	if (isset($HTTP_GET_VARS['edit']) && is_numeric($HTTP_GET_VARS['edit'])) {
?>
      	<tr>
        	<td>
        		<table border="0" width="100%" cellspacing="1" cellpadding="2" class="buttonBox">
          			<tr class="buttonBoxContents">
            			<td>
            				<table border="0" width="100%" cellspacing="0" cellpadding="2">
              					<tr>
                					<td width="10"><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
                					<td>
									<? 	
										if (isset($_REQUEST['back_url']) ) {
	                						echo tep_div_button(2, IMAGE_BUTTON_BACK,tep_href_link($_REQUEST['back_url'], tep_get_all_get_params(array('edit', 'back_url')), 'SSL'), '', 'gray_button');
										} else {
	                						echo tep_div_button(2, IMAGE_BUTTON_BACK,tep_href_link(FILENAME_ADDRESS_BOOK, '', 'SSL'), '', 'gray_button');
										}
									?>
									</td>
                					<td align="right"><?=tep_draw_hidden_field('action', 'update') . tep_draw_hidden_field('edit', $HTTP_GET_VARS['edit']) ?>
										<?=tep_div_button(1, IMAGE_BUTTON_UPDATE,'addressbook', 'style="float:right"', 'gray_button') ?>                						
									</td>
                					<td width="10"><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
              					</tr>
            				</table>
            			</td>
          			</tr>
        		</table>
        	</td>
      	</tr>
<?
	} else {
      	if (sizeof($navigation->snapshot) > 0) {
        	$back_link = tep_href_link($navigation->snapshot['page'], tep_array_to_string($navigation->snapshot['get'], array(tep_session_name())), $navigation->snapshot['mode']);
      	} else {
        	$back_link = tep_href_link(FILENAME_ADDRESS_BOOK, '', 'SSL');
      	}
?>
      	<tr>
        	<td>
        		<table border="0" width="100%" cellspacing="1" cellpadding="2" class="buttonBox">
          			<tr class="buttonBoxContents">
            			<td>
            				<table border="0" width="100%" cellspacing="0" cellpadding="2">
              					<tr>
                					<td width="10"><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
                					<td>
                						<?=tep_div_button(2, IMAGE_BUTTON_BACK,$back_link, '', 'gray_button') ?>
									</td>
                					<td align="right"><?=tep_draw_hidden_field('action', 'process')?>
                						<?=tep_div_button(1, IMAGE_BUTTON_CONTINUE,'addressbook', 'style="float:right"', 'gray_button') ?>
									</td>
                					<td width="10"><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
              					</tr>
            				</table>
            			</td>
          			</tr>
        		</table>
        	</td>
      	</tr>
<?
	}
}
?>
    </table>
<? if (!isset($HTTP_GET_VARS['delete'])) echo '</form>'; ?>

		<div class="break_line"></div>