<h1><?=HEADING_TITLE?></h1>
<div class="solidThickLine"></div>
<?=tep_draw_form('advanced_search', tep_href_link(FILENAME_ADVANCED_SEARCH_RESULT, '', 'NONSSL', false), 'get', 'onSubmit="return check_form(this);"') . tep_draw_hidden_field('action', 'search_result') . tep_hide_session_id()?>
<table border="0" width="100%" cellspacing="0" cellpadding="0">
<?
if ($messageStack->size('search') > 0) {
?>
	<tr>
    	<td><?=$messageStack->output('search')?></td>
    </tr>
    <tr>
        <td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
<?
}
?>
	<tr>
    	<td>
    		<table border="0" width="100%" cellspacing="0" cellpadding="0">
          		<tr>
            		<td width="100%" height="14" class="inputBoxHeading"></td>
            		<td height="14" class="inputBoxHeading" nowrap><img src="images/infobox/corner_right.gif" border="0" alt="" width="11" height="14"></td>
          		</tr>
        	</table>
        	<table border="0" width="100%" cellspacing="0" cellpadding="1" class="inputBox">
          		<tr>
            		<td>
            			<table border="0" width="100%" cellspacing="0" cellpadding="3" class="inputBoxContents">
              				<tr>
						      	<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '100%', '10'); ?></td>
							</tr>
              				<tr>
				 				<td width=20% class="inputLabelBold"><?=HEADING_SEARCH_CRITERIA?>:</td> 
	             				<td class="inputField"><? echo tep_draw_input_field('keywords', '', 'style="width: 50%"'); ?> <? echo '<a href="javascript:popupWindow_adv_ser(\'' . tep_href_link(FILENAME_POPUP_SEARCH_HELP) . '\')">' . TEXT_SEARCH_HELP_LINK . '</a>'; ?></td>
              				</tr>
              				<tr>
								<td>&nbsp;</td>			  
                				<td class="inputField"><? echo tep_draw_checkbox_field('search_in_description', '1', 'checked') . ' <span class="inputField">' . TEXT_SEARCH_IN_DESCRIPTION .'</span>' ?></td>
              				</tr>
			  				<tr>
						      	<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '100%', '10'); ?></td>
							</tr>
			  				<tr>
			  					<td class="inputLabelBold"><?=ENTRY_CATEGORIES?></td>
               					<td class="inputField">
                				<?
	                				//echo tep_draw_pull_down_menu('categories_id', tep_get_categories(array(array('id' => '', 'text' => TEXT_ALL_CATEGORIES)))); 
	                				echo tep_draw_pull_down_menu('categories_id', tep_custom_get_categories(array(array('id' => '', 'text' => TEXT_ALL_CATEGORIES)),'0','__',true)); 
                				?>
								</td>
							<tr>
						      	<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '100%', '10'); ?></td>
							</tr>
							<tr>
							<?
								$sortByContent=array();
								$sortByContent[0]['id']='2';
								$sortByContent[0]['text']='Product Name';
								$sortByContent[1]['id']='3';
								$sortByContent[1]['text']='Price';
								/*
								$sortByContent[2]['id']='4';
								$sortByContent[2]['text']='Date';				
								*/
								$itemPerPgContent=array();
								$itemPerPgContent[0]['id']='5';
								$itemPerPgContent[0]['text']='5';
								$itemPerPgContent[1]['id']='10';
								$itemPerPgContent[1]['text']='10';
								$itemPerPgContent[2]['id']='20';
								$itemPerPgContent[2]['text']='20';
								$itemPerPgContent[3]['id']='50';
								$itemPerPgContent[3]['text']='50';
							?>
								<td class="inputLabelBold"><?=ENTRY_SORT_BY?></td>
								<td class="inputField"><?=tep_draw_pull_down_menu('sortBy', $sortByContent,'3')?><input type="hidden" name="inc_subcat" value="1"><?=tep_draw_radio_field('sortOrder', 'a', true) . ENTRY_ASCENDING ?><?=tep_draw_radio_field('sortOrder', 'd') . ENTRY_DESCENDING?></td>				
							</tr>
							<tr>
						      	<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '100%', '10'); ?></td>
							</tr>
							<tr>
								<td class="inputLabelBold"><?=ENTRY_ITEM_PER_PAGE?></td>
								<td class="inputField"><?=tep_draw_pull_down_menu('item_per_page', $itemPerPgContent,'20')?></td>				
							</tr>
							<tr>
						      	<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '100%', '10'); ?></td>
							</tr>
							<tr>
								<td>&nbsp;</td>	
								<td>
									<?=tep_div_button(1, IMAGE_BUTTON_SEARCH,'advanced_search', '', 'gray_button') ?>
								</td>
							</tr>
			  				<tr>
						      	<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '100%', '10'); ?></td>
							</tr>
            			</table>
            		</td>
          		</tr>
        	</table>
        </td>
	</tr>
</table>
</form>
<div class="break_line"></div>
