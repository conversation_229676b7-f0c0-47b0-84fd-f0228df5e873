<h1><?=$categories_name_row['categories_name']?></h1>
<div class="solidThickLine"></div>
<table border="0" width="100%" cellspacing="0" cellpadding="0">
<?
	if ($success == 'CA') {
		$cart_link = tep_href_link(FILENAME_SHOPPING_CART);
		if ($bdn == 'y') {
			$messageStack->add('add_to_cart', sprintf(SUCCESS_DYNAMIC_CART_UPDATED, $cart_link), 'success');
		} else {
			$messageStack->add('add_to_cart', sprintf(SUCCESS_DYNAMIC_CART_ADDED, $cart_link), 'success');
		}
	} else if ($err == 'SL') {
		$messageStack->add('add_to_cart', stripslashes(WARNING_PRODUCTS_LOW_QUANTITY) . ((int)$_REQUEST['sq'] > 0 ? sprintf(WARNING_PRODUCTS_SUGGESTED_QUANTITY, $_REQUEST['sq']) : ''), 'error');
		unset($err);
	}
	
	if ($messageStack->size('add_to_cart') > 0) {
?>
	<tr>
    	<td><?=$messageStack->output('add_to_cart')?></td>
	</tr>
	<tr>
		<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
<?	} ?>
	<tr>
		<td>
			<div class="theLayer" id="theLayer"></div>
			<div class="theLayerBg" id="theLayerBg">&nbsp;</div>
			<script language="javascript">
			<!--
				var winW = document.body.scrollWidth;

				jQuery("#theLayerBg").css('width',winW);
				jQuery("#theLayerBg").css('position','absolute');
				jQuery("#theLayerBg").css('height',100);

				jQuery(document).ready(function(){
					var winH = jQuery("#footerBottomDiv").offset().top + 70;

					jQuery("#theLayerBg").css('height',winH);
				});
			//-->
			</script>
		</td>
	</tr>
<?
if (isset($HTTP_GET_VARS['buy_now'])) {?>
	<tr>
		<td class="messageStackError"><img src="images/icons/error.gif" border="0" alt="Error" title=" Error " width="10" height="10">&nbsp;<?=TEXT_ITEM_ADDED_TO_CART?><br><br></td>
	</tr>
<?
}
?>
    <tr>
		<td>
<?
// create column list
$define_list = array(//'PRODUCT_LIST_MODEL' => PRODUCT_LIST_MODEL,
                     'PRODUCT_LIST_NAME' => PRODUCT_LIST_NAME,
                     //'PRODUCT_LIST_MANUFACTURER' => PRODUCT_LIST_MANUFACTURER,
                     'PRODUCT_LIST_PRICE' => PRODUCT_LIST_PRICE,
                     //'PRODUCT_LIST_QUANTITY' => PRODUCT_LIST_QUANTITY,
                     'PRODUCT_LIST_WEIGHT' => PRODUCT_LIST_WEIGHT,
                     'PRODUCT_LIST_IMAGE' => PRODUCT_LIST_IMAGE,
                     'PRODUCT_LIST_BUY_NOW' => PRODUCT_LIST_BUY_NOW);
asort($define_list);

$column_list = array();
reset($define_list);
while (list($key, $value) = each($define_list)) {
	if ($value > 0) $column_list[] = $key;
}

if (empty($HTTP_GET_VARS['sort']) && trim($HTTP_GET_VARS['sortBy']) != '' && trim($HTTP_GET_VARS['sortOrder']) != '') {
	$HTTP_GET_VARS['sort'] = $HTTP_GET_VARS['sortBy'].$HTTP_GET_VARS['sortOrder'];		
}

$select_column_list = ' p.products_quantity, p.products_bundle, p.products_bundle_dynamic, p.custom_products_type_id, ';

for ($i=0, $n=sizeof($column_list); $i<$n; $i++) {
	switch ($column_list[$i]) {
    	/*case 'PRODUCT_LIST_MODEL':
        	$select_column_list .= 'p.products_model, ';
        	break;
      	case 'PRODUCT_LIST_MANUFACTURER':
        	$select_column_list .= 'm.manufacturers_name, ';
        	break;
      	case 'PRODUCT_LIST_QUANTITY':
        	$select_column_list .= 'p.products_quantity, ';
        	break;*/
      	case 'PRODUCT_LIST_IMAGE':
        	$select_column_list .= 'pd.products_image, ';
        	break;
      	case 'PRODUCT_LIST_WEIGHT':
        	$select_column_list .= " replace(rtrim(replace(replace(rtrim(replace(p.products_weight,'0',' ')),' ','0'),'.',' ')),' ','.') as products_weight, ";
        	break;
		}
	}
	
	$categories_id_array = array();
	
	$categories_id_select_sql = "	SELECT c.categories_id 
									FROM " . TABLE_CATEGORIES . " AS c 
									INNER JOIN " . TABLE_CATEGORIES_GROUPS . " AS cg 
										ON (c.categories_id = cg.categories_id AND ((groups_id = '".$customers_groups_id."') OR (groups_id = 0))) 
									WHERE c.categories_status = 1";
	$categories_id_result_sql = tep_db_query($categories_id_select_sql);
	while ($categories_id_row = tep_db_fetch_array($categories_id_result_sql)) {
		if (tep_check_product_region_permission($categories_id_row['categories_id'], true)) {
			$categories_id_array[] = $categories_id_row['categories_id'];
		}
	}
	
	$select_str = "select distinct " . $select_column_list . " p.products_id, pd.products_name, p.products_price ";
	$from_str = "from " . TABLE_PRODUCTS . " AS p, " . TABLE_PRODUCTS_DESCRIPTION . " AS pd, " . TABLE_PRODUCTS_TO_CATEGORIES . " AS p2c ";
	$where_str = " where p.products_status = 1 and ( p.products_bundle = 'yes' OR p.products_bundle_dynamic = 'yes' OR (p.products_bundle <> 'yes' AND p.products_bundle_dynamic <> 'yes' AND p.products_display = 1)) and p.products_id = pd.products_id and pd.language_id = '" . (int)$languages_id . "' and p.products_id = p2c.products_id and p2c.categories_id IN (" . implode(',', $categories_id_array) . ")";
	
	unset($categories_id_array);
	
  	if (isset($HTTP_GET_VARS['categories_id']) && tep_not_null($HTTP_GET_VARS['categories_id'])) {
    	if (isset($HTTP_GET_VARS['inc_subcat']) && ($HTTP_GET_VARS['inc_subcat'] == '1')) {
      		$subcategories_array = array();
      		tep_get_subcategories($subcategories_array, $HTTP_GET_VARS['categories_id']);
			
			$where_str .= " and p2c.products_id = p.products_id and p2c.products_id = pd.products_id and (p2c.categories_id = '" . (int)$HTTP_GET_VARS['categories_id'] . "'";
			
      		for ($i=0, $n=sizeof($subcategories_array); $i<$n; $i++ ) {
        		$where_str .= " or p2c.categories_id = '" . (int)$subcategories_array[$i] . "'";
      		}
      		$where_str .= ")";
    	} else {
      		$where_str .= " and p2c.products_id = p.products_id and p2c.products_id = pd.products_id and (pd.language_id = '" . (int)$languages_id . "' and p2c.categories_id = '" . (int)$HTTP_GET_VARS['categories_id'] . "'";
      		$subcategories_array = array();
      		tep_get_subcategories($subcategories_array, $HTTP_GET_VARS['categories_id']);
      		for ($i=0, $n=sizeof($subcategories_array); $i<$n; $i++ ) {
        		$where_str .= " or p2c.categories_id = '" . (int)$subcategories_array[$i] . "'";
      		}
      		$where_str .= ")";
    	}
  	}
	
  	if (isset($search_keywords) && (sizeof($search_keywords) > 0)) {
    	$where_str .= " and (";
    	for ($i=0, $n=sizeof($search_keywords); $i<$n; $i++ ) {
      		switch ($search_keywords[$i]) {
        		case '(':
        		case ')':
        		case 'and':
        		case 'or':
          			$where_str .= " " . $search_keywords[$i] . " ";
          			break;
	        	default:
	          		$keyword = tep_db_prepare_input($search_keywords[$i]);
	          		$where_str .= "(pd.products_name like '%" . tep_db_input($keyword) . "%'";
	          		if (isset($HTTP_GET_VARS['search_in_description']) && ($HTTP_GET_VARS['search_in_description'] == '1')) $where_str .= " or pd.products_description like '%" . tep_db_input($keyword) . "%'";
	       			$where_str .= ')';
	          		break;
      		}
    	}
    	$where_str .= " )";
  	}
  	
  	if ( (!isset($HTTP_GET_VARS['sort'])) /*|| (!ereg('[1-8][ad]', $HTTP_GET_VARS['sort'])) || (substr($HTTP_GET_VARS['sort'], 0, 1) > sizeof($column_list))*/ ) {
    	for ($i=0, $n=sizeof($column_list); $i<$n; $i++) {
      		if ($column_list[$i] == 'PRODUCT_LIST_NAME') {
        		$HTTP_GET_VARS['sort'] = $i+1 . 'a';
        		$order_str = ' order by pd.products_name';
        		break;
			}
		}
	} else {
		if (trim($HTTP_GET_VARS['sort']))
    		$sort_col = substr($HTTP_GET_VARS['sort'], 0 , 1);
    	$sort_order = substr($HTTP_GET_VARS['sort'], 1);
    	$order_str = ' order by ';
		
    	switch ($column_list[$sort_col-1]/*$HTTP_GET_VARS['sort_by']*/) {
      		/*case 'PRODUCT_LIST_MODEL':
        		$order_str .= "p.products_model " . ($sort_order == 'd' ? "desc" : "") . ", pd.products_name";
        		break;*/
      		case 'PRODUCT_LIST_NAME':
	  		case '1':
        		$order_str .= "pd.products_name " . ($sort_order == 'd' ? "desc" : "");
        		break;
      		/*case 'PRODUCT_LIST_MANUFACTURER':
        		$order_str .= "m.manufacturers_name " . ($sort_order == 'd' ? "desc" : "") . ", pd.products_name";
        		break;
      		case 'PRODUCT_LIST_QUANTITY':
        		$order_str .= "p.products_quantity " . ($sort_order == 'd' ? "desc" : "") . ", pd.products_name";
        		break;*/
      		case 'PRODUCT_LIST_IMAGE':
        		$order_str .= "pd.products_name";
        		break;
      		case 'PRODUCT_LIST_WEIGHT':
        		$order_str .= "p.products_weight " . ($sort_order == 'd' ? "desc" : "") . ", pd.products_name";
        		break;
      		case 'PRODUCT_LIST_PRICE':
	  		case '2':
        		$order_str .= "p.products_price " . ($sort_order == 'd' ? "desc" : "") . ", pd.products_name";
        		break;
	  		case '3':
				$order_str .= "p.products_date_added " . ($sort_order == 'd' ? "desc" : "") . ", pd.products_name";
				break;
			default:
				$order_str .= "p.products_date_added " . ($sort_order == 'd' ? "desc" : "") . ", pd.products_name";
				break;
		}
	}
	
  	$listing_sql = $select_str . $from_str . $where_str . $order_str;
	
	$report_db_link = tep_db_connect(DB_REPORT_SERVER, DB_REPORT_SERVER_USERNAME, DB_REPORT_SERVER_PASSWORD, DB_REPORT_DATABASE, 'report_db_link') or die('Unable to connect to database server!');
	$listing_db_link = 'report_db_link';
	
  	require(DIR_WS_MODULES . FILENAME_PRODUCT_LISTING);
?>
		</td>
	</tr>
    <tr>
    	<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
    <tr>
    	<td><?=tep_div_button(2, IMAGE_BUTTON_BACK,'javascript:history.back(-1);', '', 'gray_button')?></td>
	</tr>
</table>
<div class="break_line"></div>