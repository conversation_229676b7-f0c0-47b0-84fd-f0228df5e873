<?=tep_draw_form('login', tep_href_link(FILENAME_AFFILIATE, 'action=process', 'SSL'))?>
<table border="0" width="100%" cellspacing="0" cellpadding="0"> 
	<tr>
		<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
	<tr>
		<td class="pageHeadingTitle" valign="middle" align="center"><?=HEADING_TITLE?></td>
	</tr>
	<tr>
		<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
	<tr>
		<td>
			<div class="row_separator"></div>
		</td>
	</tr>
	<tr>
		<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
<?
if ($messageStack->size('affiliate_login') > 0) {
?>
	<tr>
    	<td><?=$messageStack->output('affiliate_login')?></td>
	</tr>
    <tr>
    	<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
<?
} 
?> 
	<tr> 
    	<td>
    		<table border="0" width="100%" cellspacing="0" cellpadding="2"> 
          		<tr> 
            		<td class="instructionBoxHeading" width="50%" valign="top"><?=HEADING_NEW_AFFILIATE?></td>
          		</tr>
          		<tr>
            		<td width="100%" height="100%" valign="top">
            			<table border="0" width="100%" height="100%" cellspacing="0" cellpadding="2" class="instructionBox">
              				<tr class="instructionBoxContents">
                				<td>
                					<table border="0" width="100%" height="100%" cellspacing="0" cellpadding="2"> 
                  						<tr class="instructionBoxContents">
                    						<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
                  						</tr>
                  						<tr>
                    						<td class="instructionBoxContents" valign="top"><? echo TEXT_NEW_AFFILIATE . '<br><br>' . TEXT_NEW_AFFILIATE_INTRODUCTION; ?></td> 
                  						</tr>
                  						<tr>
                    						<td colspan="2"><? echo '<a  href="' . tep_href_link(FILENAME_AFFILIATE_TERMS, '', 'SSL') . '">' . TEXT_NEW_AFFILIATE_TERMS . '</a>'; ?></td> 
                  						</tr>
                  						<tr>
                    						<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
                  						</tr>
                  						<tr>
                    						<td>
                    							<table border="0" width="100%" cellspacing="0" cellpadding="2" class="buttonBox">
                      								<tr class="buttonBoxContents">
                        								<td width="10"><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
								                        <td align="right"><? echo tep_image_button(THEMA.'button_continue.gif', IMAGE_BUTTON_CONTINUE, tep_href_link(FILENAME_AFFILIATE_SIGNUP, '', 'SSL')); ?></td>
								                        <td width="10"><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
                      								</tr>
                    							</table>
                    						</td>
                  						</tr>
                					</table>
                				</td>
              				</tr>
            			</table>
            		</td>
            	</tr>
            	<tr> 
					<td class="inputBoxHeading" width="50%" valign="top"><?=HEADING_RETURNING_AFFILIATE?></td> 
				</tr>
            	<tr>
            		<td width="50%" height="100%" valign="top">
            			<table border="0" width="100%" height="100%" cellspacing="0" cellpadding="2" class="inputBox">
              				<tr class="inputBoxContents">
                				<td>
                					<table border="0" width="100%" height="100%" cellspacing="0" cellpadding="2"> 
                  						<tr class="inputBoxContents">
                    						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
                  						</tr>
                  						<tr>
                    						<td class="inputBoxContents" colspan="2"><?=TEXT_RETURNING_AFFILIATE?></td>
                  						</tr>
                  						<tr>
                    						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td> 
                  						</tr>
                  						<tr>
                    						<td class="inputLabel"><?=TEXT_AFFILIATE_ID?></td>
                    						<td class="inputField"><?=tep_draw_input_field('affiliate_username')?></td>
                  						</tr>
                  						<tr>
                    						<td class="inputLabel"><?=TEXT_AFFILIATE_PASSWORD; ?></td>
                    						<td class="inputField"><?=tep_draw_password_field('affiliate_password')?></td>
                  						</tr>
                  						<tr>
                    						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
                  						</tr>
                  						<tr>
                    						<td colspan="2"><? echo '<a href="' . tep_href_link(FILENAME_AFFILIATE_PASSWORD_FORGOTTEN, '', 'SSL') . '">' . TEXT_AFFILIATE_PASSWORD_FORGOTTEN . '</a>'; ?></td> 
                  						</tr>
                  						<tr>
                    						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
                  						</tr>
                  						<tr>
        									<td colspan="2">
        										<table border="0" width="100%" cellspacing="0" cellpadding="2" class="buttonBox">
          											<tr class="buttonBoxContents">
								                        <td width="10"><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
								                        <td align="right"><?=tep_image_submit(THEMA.'button_login.gif', IMAGE_BUTTON_LOGIN)?></td>
								                        <td width="10"><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
          											</tr>
        										</table>
        									</td>
      									</tr>
                					</table>
                				</td>
              				</tr>
            			</table>
            		</td>
          		</tr>
        	</table>
        </td> 
	</tr> 
</table>
</form>