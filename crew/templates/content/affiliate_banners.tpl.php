<script>
	function openpopup(){
		var popurl="affiliate_products.php"
		winpops=window.open(popurl,"","width=400,height=338,scrollbars,")
	}
</script> 
<table border="0" width="100%" cellspacing="0" cellpadding="<?php echo CELLPADDING_SUB; ?>"> 
	<tr>
		<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
	<tr>
		<td class="pageHeadingTitle" valign="middle" align="center"><?=HEADING_TITLE?></td>
	</tr>
	<tr>
		<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
	<tr>
		<td>
			<div class="row_separator"></div>
		</td>
	</tr>
	<tr>
		<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
	<tr>
    	<td class="instruction" colspan=2><?=TEXT_INFORMATION?></td>
	</tr>
    <!--tr>
        <td>
        	<table width="100%" border="0" cellspacing="0" cellpadding="2">
          		<tr>
    				<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
				</tr>
          		<tr>
            		<td class="affiliateBannerTitle" align="center"><? echo TEXT_AFFILIATE_INDIVIDUAL_BANNER . ' ' . $affiliate_banners['affiliate_banners_title']; ?></td>
          		</tr>
          		<tr>
    				<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
				</tr>
          		<tr>
            		<td class="inputLabel" align="center"><? echo tep_draw_form('individual_banner', tep_href_link(FILENAME_AFFILIATE_BANNERS) ) . "\n" . TEXT_AFFILIATE_INDIVIDUAL_BANNER_INFO . tep_draw_input_field('individual_banner_id', '', 'size="5"') . "&nbsp;&nbsp;" . tep_image_submit(THEMA.'button_affiliate_build_a_link.gif', IMAGE_BUTTON_BUILD_A_LINK); ?></form></td>
          		</tr>
          		<tr>
            		<td class="spacingInstruction" align="center"><? echo "click <a href='javascript:openpopup(); windowHandle.focus()'>here</a> to view list of product id"; ?></form></td>
          		</tr>
<?
/*
if (tep_not_null($HTTP_POST_VARS['individual_banner_id']) || tep_not_null($HTTP_GET_VARS['individual_banner_id'])) {
	if (tep_not_null($HTTP_POST_VARS['individual_banner_id'])) $individual_banner_id = $HTTP_POST_VARS['individual_banner_id'];
    if ($HTTP_GET_VARS['individual_banner_id']) $individual_banner_id = $HTTP_GET_VARS['individual_banner_id'];
    
    $affiliate_pbanners_values = tep_db_query("select p.products_image, pd.products_name from " . TABLE_PRODUCTS . " p, " . TABLE_PRODUCTS_DESCRIPTION . " pd where p.products_id = '" . $individual_banner_id . "' and pd.products_id = '" . $individual_banner_id . "' and p.products_status = '1' and pd.language_id = '" . $languages_id . "'");
    if ($affiliate_pbanners = tep_db_fetch_array($affiliate_pbanners_values)) {
    	switch (AFFILIATE_KIND_OF_BANNERS) {
        	case 1:
         		$link = '<a href="' . HTTP_SERVER . DIR_WS_CATALOG . FILENAME_PRODUCT_INFO . '?ref=' . $affiliate_id . '&products_id=' . $individual_banner_id . '&affiliate_banner_id=1" target="_blank"><img src="' . DIR_WS_IMAGES . $affiliate_pbanners['affiliate_banners_image'] . '" border="0" alt="' . $affiliate_pbanners['products_name'] . '"></a>';
          		break;
        	case 2: // Link to Products
          		$link = '<a href="' . HTTP_SERVER . DIR_WS_CATALOG . FILENAME_PRODUCT_INFO . '?ref=' . $affiliate_id . '&'.theme_path . '&products_id=' . $individual_banner_id . '&affiliate_banner_id=1" target="_blank"><img src="' . HTTP_SERVER . DIR_WS_CATALOG . FILENAME_AFFILIATE_SHOW_BANNER1 . '?ref=' . $affiliate_id . '&affiliate_pbanner_id=' . $individual_banner_id . '" border="0" alt="' . $affiliate_pbanners['products_name'] . '"></a>';
          		//$link = '';  // link 1
          		break;
      	}
	}
*/
?>
          		<tr>
            		<td align="center"><br><?=$link // link 1 ?></td>
          		</tr>
          		<tr>
            		<td class="info" align="center"><?=TEXT_AFFILIATE_INFO; // changes 23th sept ?></td>
          		</tr>
          		<tr>
            		<td align="center"><?=tep_draw_textarea_field('affiliate_banner', 'soft', '60', '6', $link); ?></td>
          		</tr>
<?
//}
?>
			</table>
		</td>
	</tr-->
    <tr>
    	<td><?=tep_draw_separator('pixel_trans.gif', '100%', '20')?></td>
	</tr>
<?
if (tep_db_num_rows($affiliate_banners_values)) {
	while ($affiliate_banners = tep_db_fetch_array($affiliate_banners_values)) {
    	$affiliate_products_query = tep_db_query("select products_name from " . TABLE_PRODUCTS_DESCRIPTION . " where products_id = '" . $affiliate_banners['affiliate_products_id'] . "' and language_id = '" . $languages_id . "'");
      	$affiliate_products = tep_db_fetch_array($affiliate_products_query);
      	$prod_id = $affiliate_banners['affiliate_products_id'];
      	$ban_id = $affiliate_banners['affiliate_banners_id'];
      	
      	switch (AFFILIATE_KIND_OF_BANNERS) {
        	case 1: // Link to Products
          		if ($prod_id > 0) {
            		$link = '<a href="' . HTTP_SERVER . DIR_WS_CATALOG . FILENAME_PRODUCT_INFO . '?ref=' . $affiliate_id . '&products_id=' . $prod_id . '&affiliate_banner_id=' . $ban_id . '" target="_blank"><img src="' . DIR_WS_IMAGES . $affiliate_banners['affiliate_banners_image'] . '" border="0" alt="' . $affiliate_products['products_name'] . '"></a>';
          		} else { // generic_link
            		$link = '<a href="' . HTTP_SERVER . DIR_WS_CATALOG . FILENAME_DEFAULT . '?ref=' . $affiliate_id . '&affiliate_banner_id=' . $ban_id . '" target="_blank"><img src="' . DIR_WS_IMAGES . $affiliate_banners['affiliate_banners_image'] . '" border="0" alt="' . $affiliate_banners['affiliate_banners_title'] . '"></a>';
          		}
          		break;
			case 2: // Link to Products
          		if ($prod_id > 0) {
            		$link = '<a href="' . HTTP_SERVER . DIR_WS_CATALOG . FILENAME_PRODUCT_INFO . '?ref=' . $affiliate_id . '&products_id=' . $prod_id . '&affiliate_banner_id=' . $ban_id . '" target="_blank"><img src="' . HTTP_SERVER . DIR_WS_CATALOG . FILENAME_AFFILIATE_SHOW_BANNER . '?ref=' . $affiliate_id . '&affiliate_banner_id=' . $ban_id . '" border="0" alt="' . $affiliate_products['products_name'] . '"></a>';
          		} else { // generic_link
            		$link = '<a href="' . HTTP_SERVER . DIR_WS_CATALOG . FILENAME_DEFAULT . '?ref=' . $affiliate_id . '&affiliate_banner_id=' . $ban_id . '" target="_blank"><img src="' . HTTP_SERVER . DIR_WS_CATALOG . FILENAME_AFFILIATE_SHOW_BANNER . '?ref=' . $affiliate_id . '&affiliate_banner_id=' . $ban_id . '" border="0" alt="' . $affiliate_banners['affiliate_banners_title'] . '"></a>';
          		}
          		break;
      	}
?>
	<tr>
    	<td>
    		<table width="100%" border="0" cellspacing="0" cellpadding="2">
          		<tr>
            		<td class="affiliateBannerTitle" align="center"><?=TEXT_AFFILIATE_NAME . ' ' . $affiliate_banners['affiliate_banners_title']?></td>
          		</tr>
          		<tr>
            		<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
          		</tr>
          		<tr>
            		<td class="info" align="center"><?=$link?></td>
          		</tr>
          		<tr>
            		<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
          		</tr>
          		<tr>
            		<td class="instruction" align="center"><?=TEXT_AFFILIATE_INFO?></td>
          		</tr>
          		<tr>
            		<td class="info" align="center"><?=tep_draw_textarea_field('affiliate_banner', 'soft', '60', '6', $link, ' READONLY ')?></td>
          		</tr>
        	</table>
		</td>
	</tr>	
    <tr>
    	<td><?=tep_draw_separator('pixel_trans.gif', '100%', '20')?></td>
	</tr>
<?
	}
}
?>
</table>