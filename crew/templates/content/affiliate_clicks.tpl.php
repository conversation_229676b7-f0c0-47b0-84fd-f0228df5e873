<table border="0" width="100%" cellspacing="0" cellpadding="0"> 
	<tr>
		<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
	<tr>
		<td class="pageHeadingTitle" valign="middle" align="center"><?=HEADING_TITLE?></td>
	</tr>
	<tr>
		<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
	<tr>
		<td>
			<div class="row_separator"></div>
		</td>
	</tr>
	<tr>
		<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
    <tr>
		<td class="affiliateReportTitle" colspan="4"><?=TEXT_AFFILIATE_HEADER . ' ' . $affiliate_clickthroughs_split->number_of_rows?></td>
	</tr>
    <tr>
    	<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
    <tr>
        <td>
        	<table border="0" width="100%" cellspacing="0" cellpadding="2" class="affiliateReportBox">
          		<tr>
		            <td class="affiliateReportBoxHeading"><?=TABLE_HEADING_DATE?></td>
		 	        <td class="affiliateReportBoxHeading"><?=TABLE_HEADING_CLICKED_PRODUCT?></td>
			        <td class="affiliateReportBoxHeading"><?=TABLE_HEADING_REFFERED?></td>
          		</tr>
<?
	if ($affiliate_clickthroughs_split->number_of_rows > 0) {
    	$affiliate_clickthroughs_values = tep_db_query($affiliate_clickthroughs_split->sql_query);
    	$number_of_clickthroughs = '0';
    	while ($affiliate_clickthroughs = tep_db_fetch_array($affiliate_clickthroughs_values)) {
    		$number_of_clickthroughs++;
      		$row_style = ($number_of_clickthroughs%2 == "0") ? 'affiliateListingEven' : 'affiliateListingOdd' ;
?>
				<tr class="<?=$row_style?>">
            		<td class="affiliateReportInfo"><?=tep_date_short($affiliate_clickthroughs['affiliate_clientdate'])?></td>
<?
      		if ($affiliate_clickthroughs['affiliate_products_id'] > 0) {
        		$link_to = '<a href="' . tep_href_link (FILENAME_PRODUCT_INFO, 'products_id=' . $affiliate_clickthroughs['affiliate_products_id']) . '" target="_blank">' . $affiliate_clickthroughs['products_name'] . '</a>';
      		} else {
        		$link_to = TEXT_START_PAGE;
      		}
?>
		            <td class="affiliateReportInfo"><?=$link_to?></td>
		            <td class="affiliateReportInfo"><?=$affiliate_clickthroughs['affiliate_clientreferer']?></td>
          		</tr>
<?
    	}
  	} else {
?>
          		<tr class="messageRow">
            		<td colspan="3" class="messageData"><?=TEXT_NO_CLICKS?></td>
          		</tr>
<?
  	}
?>
			</table>
		</td>
	</tr>
 	<tr>
		<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
<?
  	if ($affiliate_clickthroughs_split->number_of_rows > 0) {
?>
  	<tr>
		<td colspan="3">
			<table border="0" width="100%" cellspacing="0" cellpadding="0">
  				<tr>
    				<td class="pageResultsText"><?=$affiliate_clickthroughs_split->display_count(TEXT_DISPLAY_NUMBER_OF_CLICKS)?></td>
    				<td align="right" class="pageResultsText"><?=TEXT_RESULT_PAGE?> <?=$affiliate_clickthroughs_split->display_links(MAX_DISPLAY_PAGE_LINKS, tep_get_all_get_params(array('page', 'info', 'x', 'y')))?></td>
  				</tr>
			</table>
		</td>
	</tr>
<?
  	}
?>    
</table>