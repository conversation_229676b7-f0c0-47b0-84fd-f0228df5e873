<table border="0" width="100%" cellspacing="0" cellpadding="0">
	<tr>
		<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
	<tr>
		<td class="pageHeadingTitle" valign="middle" align="center"><?=HEADING_TITLE?></td>
	</tr>
	<tr>
		<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
	<tr>
		<td>
			<div class="row_separator"></div>
		</td>
	</tr>
	<tr>
		<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
<?
if (isset($HTTP_GET_VARS['action']) && ($HTTP_GET_VARS['action'] == 'success')) {
?>
	<tr>
    	<td>
    		<table border="0" width="100%" cellspacing="0" cellpadding="2">
          		<tr>
            		<!--td class="spacingInfo"><?=tep_image(DIR_WS_IMAGES . 'table_background_man_on_board.gif', HEADING_TITLE, '0', '0', 'align="left"') . TEXT_SUCCESS?></td-->
            		<td class="spacingInfo"><?=TEXT_SUCCESS?></td>
          		</tr>
          		<tr>
            		<td align="right">
            			<br><?=tep_image_button(THEMA.'button_continue.gif', IMAGE_BUTTON_CONTINUE, tep_href_link(FILENAME_AFFILIATE_SUMMARY))?>
            		</td>
          		</tr>
        	</table>
        </td>
	</tr>
<?
} else {

	if ($messageStack->size('affiliate_contact') > 0) {
?>
	<tr>
    	<td><?=$messageStack->output('affiliate_contact')?></td>
	</tr>
	<tr>
    	<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
<?	} ?>
	<tr>
    	<td>
    		<table border="0" width="100%" cellspacing="0" cellpadding="2">
    			<?=tep_draw_form('contact_us', tep_href_link(FILENAME_AFFILIATE_CONTACT, 'action=send'))?>
          		<tr>
            		<td class="inputLabel"><?=ENTRY_NAME?><br><?=tep_draw_input_field('name', $affiliate['affiliate_firstname'] . ' ' . $affiliate['affiliate_lastname'], 'size=40')?></td>
          		</tr>
          		<tr>
            		<td class="inputLabel"><?=ENTRY_EMAIL?><br><?=tep_draw_input_field('email', $affiliate['affiliate_email_address'], 'size=40');?></td>
          		</tr>
          		<tr>
            		<td class="inputLabel"><?=ENTRY_ENQUIRY?></td>
          		</tr>
          		<tr>
            		<td><?=tep_draw_textarea_field('enquiry', 'soft', 50, 15, $HTTP_POST_VARS['enquiry'])?></td>
          		</tr>
          		<tr>
    				<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
				</tr>
          		<tr>
            		<td align="right"><?=tep_image_submit(THEMA.'button_continue.gif', IMAGE_BUTTON_CONTINUE)?></td>
          		</tr>
          		</form>
        	</table>
        </td>
	</tr>
<?
}
?>
</table>