<?
$affiliate_query = tep_db_query("select * from " . TABLE_AFFILIATE . " where affiliate_id = '" . $affiliate_id . "'");
$affiliate = tep_db_fetch_array($affiliate_query);

echo tep_draw_form('affiliate_details', tep_href_link(FILENAME_AFFILIATE_DETAILS, '', 'SSL'), 'post') . tep_draw_hidden_field('action', 'process');
?>
<table border="0" width="100%" cellspacing="0" cellpadding="0">
   	<tr>
		<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
	<tr>
		<td class="pageHeadingTitle" valign="middle" align="center"><?=HEADING_TITLE?></td>
	</tr>
	<tr>
		<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
	<tr>
		<td>
			<div class="row_separator"></div>
		</td>
	</tr>
	<tr>
		<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
	<tr>
		<td class="spacingInstruction"><br><?=TEXT_MAIN?></td>
	</tr>
	<tr>
		<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
<?
if ($messageStack->size('edit_affiliate_account') > 0) {
?>
		<tr>
	    	<td><? echo $messageStack->output('edit_affiliate_account'); ?></td>
		</tr>
		<tr>
	    	<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
		</tr>
<?
}
?>
	<tr>
    	<td>
    		<table border="0" width="100%" cellspacing="1" cellpadding="2" class="inputBox">
      			<tr class="inputBoxContents">
        			<td><?require(DIR_WS_MODULES . 'affiliate_account_details.php');?></td>
				</tr>
				<tr>
					<td>
						<table border="0" width="100%" cellspacing="1" cellpadding="2" class="buttonBox2">
			          		<tr class="buttonBoxContents2">
            					<td width="10"><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
                				<td align="right"><?=tep_image_submit(THEMA.'button_continue.gif', IMAGE_BUTTON_CONTINUE)?></td>
                				<td width="10"><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
			          		</tr>
			        	</table>
			        </td>
		        </tr>
			</table>
		</td>
  	</tr>
</table></form>