<table border="0" width="100%" cellspacing="0" cellpadding="0">
	<tr>
		<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
	<tr>
		<td class="pageHeadingTitle" valign="middle" align="center"><?=HEADING_TITLE?></td>
	</tr>
	<tr>
		<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
	<tr>
		<td>
			<div class="row_separator"></div>
		</td>
	</tr>
	<tr>
		<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
  	<tr>
    	<td>
    		<table border="0" width="100%" cellspacing="0" cellpadding="2">
      			<tr>
        			<td class="spacingInfo"><?=TEXT_INFORMATION?></td>
      			</tr>
    		</table>
    	</td>
  	</tr>
  	<tr>
		<td align="right">
			<table border="0" width="100%" cellspacing="1" cellpadding="2" class="buttonBox">
	    		<tr class="buttonBoxContents">
	        		<td>
	        			<table border="0" width="100%" cellspacing="0" cellpadding="2">
	          				<tr>
				                <td width="10"><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
				                <td align="right"><? echo tep_image_button(THEMA.'button_continue.gif', IMAGE_BUTTON_CONTINUE, tep_href_link(FILENAME_AFFILIATE_SUMMARY)) ; ?></td>
				                <td width="10"><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
	          				</tr>
	        			</table>
	        		</td>
	      		</tr>
	    	</table>
		</td>
	</tr>
</table>