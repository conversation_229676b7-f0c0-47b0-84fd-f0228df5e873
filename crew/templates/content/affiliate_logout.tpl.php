<table border="0" width="100%" cellspacing="0" cellpadding="0">
	<tr>
		<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
	<tr>
		<td class="pageHeadingTitle" valign="middle" align="center"><?=HEADING_TITLE?></td>
	</tr>
	<tr>
		<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
	<tr>
		<td>
			<div class="row_separator"></div>
		</td>
	</tr>
	<tr>
		<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
	<tr>
<?
	session_start();
  	$old_user = $affiliate_id;  // store  to test if they *were* logged in
  	$result = session_unregister("affiliate_id");
	
	//session_destroy();
  	if (!empty($old_user)) {
    	if ($result) { // if they were logged in and are not logged out 
      		echo '            <td class="spacingInfo">' . TEXT_INFORMATION . '</td>';
    	} else { // they were logged in and could not be logged out
      		echo '            <td class="errorText">' . TEXT_INFORMATION_ERROR_1 . '</td>';
    	} 
  	} else { // if they weren't logged in but came to this page somehow
    	echo '            <td class="errorText">' . TEXT_INFORMATION_ERROR_2 . '</td>';
  	}
?>
	</tr>
	<tr>
		<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
	<tr>
		<td align="right">
			<table border="0" width="100%" cellspacing="1" cellpadding="2" class="buttonBox">
	    		<tr class="buttonBoxContents">
	        		<td>
	        			<table border="0" width="100%" cellspacing="0" cellpadding="2">
	          				<tr>
				                <td width="10"><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
				                <td align="right"><? echo tep_image_button(THEMA.'button_continue.gif', IMAGE_BUTTON_CONTINUE, tep_href_link(FILENAME_DEFAULT)) ; ?></td>
				                <td width="10"><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
	          				</tr>
	        			</table>
	        		</td>
	      		</tr>
	    	</table>
		</td>
	</tr>
</table>