<?=tep_draw_form('password_forgotten', tep_href_link(FILENAME_AFFILIATE_PASSWORD_FORGOTTEN, 'action=process', 'SSL'))?>
<table border="0" width="100%" cellspacing="0" cellpadding="0">
	<tr>
		<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
	<tr>
		<td class="pageHeadingTitle" valign="middle" align="center"><?=HEADING_TITLE?></td>
	</tr>
	<tr>
		<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
	<tr>
		<td>
			<div class="row_separator"></div>
		</td>
	</tr>
	<tr>
		<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
	<tr>
    	<td class="instruction"><br><?=TEXT_MAIN?></td>
   	</tr>
   	<tr>
    	<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
<?
if ($messageStack->size('password_forgotten') > 0) {
?>
	<tr>
		<td><?=$messageStack->output('password_forgotten')?></td>
	</tr>
	<tr>
		<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
<?
}
?>
	<tr>
    	<td>
    		<table border="0" width="100%" height="100%" cellspacing="1" cellpadding="2" class="inputBox">
          		<tr class="inputBoxContents">
            		<td>
            			<table border="0" width="100%" height="100%" cellspacing="0" cellpadding="2">
              				<tr>
                				<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
              				</tr>
              				<tr>
                				<td>
                					<span class="inputLabelBold"><?=ENTRY_EMAIL_ADDRESS?></span>&nbsp;
                					<span class="inputField"><?=tep_draw_input_field('email_address')?></span>
                				</td>
              				</tr>
              				<tr>
                				<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
              				</tr>
            			</table>
            		</td>
          		</tr>
          		<tr>
			    	<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
				</tr>
			    <tr>
			    	<td>
			    		<table border="0" width="100%" cellspacing="1" cellpadding="2" class="buttonBox">
			          		<tr class="buttonBoxContents">
			            		<td>
			            			<table border="0" width="100%" cellspacing="0" cellpadding="2">
			              				<tr>
			                				<td width="10"><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
			                				<td><? echo tep_image_button(THEMA.'button_back.gif', IMAGE_BUTTON_BACK, tep_href_link(FILENAME_AFFILIATE, '', 'SSL')) ; ?></td>
			                				<td align="right"><? echo tep_image_submit(THEMA.'button_continue.gif', IMAGE_BUTTON_CONTINUE); ?></td>
			                				<td width="10"><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
			              				</tr>
			            			</table>
			            		</td>
			          		</tr>
			        	</table>
			        </td>
				</tr>
        	</table>
        </td>
	</tr>
	
	
</table>
</form>