<table border="0" width="100%" cellspacing="0" cellpadding="0">
	<tr>
		<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
	<tr>
		<td class="pageHeadingTitle" valign="middle" align="center"><?=HEADING_TITLE?></td>
	</tr>
	<tr>
		<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
	<tr>
		<td>
			<div class="row_separator"></div>
		</td>
	</tr>
	<tr>
		<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
	<tr>
		<td class="affiliateReportTitle"><?=TEXT_AFFILIATE_HEADER . ' ' . $affiliate_payment_split->number_of_rows?></td>
	</tr>
	<tr>
		<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
    <tr>
    	<td>
    		<table border="0" width="100%" cellspacing="0" cellpadding="2" class="affiliateReportBox">
          		<tr>
		            <td class="affiliateReportBoxHeading"><?=TABLE_HEADING_PAYMENT_ID?></td>
		            <td class="affiliateReportBoxHeading"><?=TABLE_HEADING_DATE?></td>
		            <td class="affiliateReportBoxHeading" align="right"><?=TABLE_HEADING_PREVIOUS_BALANCE?></td>
		            <td class="affiliateReportBoxHeading" align="right"><?=TABLE_HEADING_PAYMENT_INCL?></td>
		            <td class="affiliateReportBoxHeading" align="right"><?=TABLE_HEADING_RECLAIM_AMOUNT?></td>
					<td class="affiliateReportBoxHeading" align="right"><?=TABLE_HEADING_PAID_AMOUNT?></td>
					<td class="affiliateReportBoxHeading" align="right"><?=TABLE_HEADING_HOLD_AMOUNT?></td>
					<td class="affiliateReportBoxHeading">&nbsp;</td>
		            <td class="affiliateReportBoxHeading"><?=TABLE_HEADING_STATUS?></td>
          		</tr>
<?
if ($affiliate_payment_split->number_of_rows > 0) {
	$affiliate_payment_values = tep_db_query($affiliate_payment_split->sql_query);
	$number_of_payment = 0;
	while ($affiliate_payment = tep_db_fetch_array($affiliate_payment_values)) {
		$number_of_payment++;
		$row_style = ($number_of_payment%2 == "0") ? 'affiliateListingEven' : 'affiliateListingOdd' ;
		
		if ($affiliate_payment["affiliate_payment_status"] > 0) {
			$previous_balance_select_sql = "SELECT SUM(affiliate_total_amount) AS total_earning, SUM(affiliate_reclaim_amount) AS total_reclaim, SUM(affiliate_paid_amount) AS total_paid
											FROM " . TABLE_AFFILIATE_ACCOUNT . " WHERE affiliate_id='" . $affiliate_id . "' AND affiliate_transaction_date < '" . $affiliate_payment["affiliate_transaction_date"] . "'";
			$previous_balance_result_sql = tep_db_query($previous_balance_select_sql);
			if ($previous_balance_row = tep_db_fetch_array($previous_balance_result_sql)) {
				$previous_balance = $previous_balance_row["total_earning"] - $previous_balance_row["total_reclaim"] - $previous_balance_row["total_paid"];
				$withhold_balance = $previous_balance + $affiliate_payment["affiliate_payment_total"] - $affiliate_payment['affiliate_payment_reclaimed_total'] - $affiliate_payment['affiliate_paid_amount'];
			} else {
				$previous_balance = 0;
				$withhold_balance = 0;
			}
		} else {
			$previous_balance = "n/a";
			$withhold_balance = "n/a";
		}
?>
				<tr class="<?=$row_style?>">
		            <td class="affiliateReportInfo"><?=$affiliate_payment['affiliate_payment_id']?></td>
		            <td class="affiliateReportInfo"><?=tep_date_short($affiliate_payment['affiliate_payment_date'])?></td>
		            <td class="affiliateReportInfo" align="right"><?=($affiliate_payment["affiliate_payment_status"] > 0) ? $currencies->format($previous_balance) : $previous_balance?></td>
		            <td class="affiliateReportInfo" align="right"><?=$currencies->format($affiliate_payment['affiliate_payment'] + $affiliate_payment['affiliate_payment_tax'])?></td>
		            <td class="affiliateReportInfo" align="right"><?=$currencies->format($affiliate_payment['affiliate_payment_reclaimed_total'])?></td>
		            <td class="affiliateReportInfo" align="right"><?=($affiliate_payment["affiliate_payment_status"] > 0) ? $currencies->format($affiliate_payment['affiliate_paid_amount']) : 'n/a'?></td>
		            <td class="affiliateReportInfo" align="right"><?=($affiliate_payment["affiliate_payment_status"] > 0) ? $currencies->format($withhold_balance) : $withhold_balance?></td>
		            <td class="affiliateReportInfo">&nbsp;</td>
		            <td class="affiliateReportInfo"><?=$affiliate_payment['affiliate_payment_status_name']?></td>
          		</tr>
<?
	}
} else {
?>
          		<tr class="messageRow">
            		<td colspan="9" class="messageData"><?=TEXT_NO_PAYMENTS?></td>
          		</tr>
<?
}
?>
			</table>
		</td>
	</tr>
	<tr>
		<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
<?
if ($affiliate_payment_split->number_of_rows > 0) {
?>    
  	<tr>
		<td>
			<table border="0" width="100%" cellspacing="0" cellpadding="2">
  				<tr>
    				<td class="pageResultsText"><?=$affiliate_payment_split->display_count(TEXT_DISPLAY_NUMBER_OF_PAYMENTS)?></td>
    				<td align="right" class="pageResultsText"><?=TEXT_RESULT_PAGE?> <?=$affiliate_payment_split->display_links(MAX_DISPLAY_PAGE_LINKS, tep_get_all_get_params(array('page', 'info', 'x', 'y')))?></td>
  				</tr>
			</table>
		</td>
	</tr>
<?
}
?>
</table>