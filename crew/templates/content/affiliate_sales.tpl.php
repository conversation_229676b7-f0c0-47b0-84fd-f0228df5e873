<table border="0" width="100%" cellspacing="0" cellpadding="0">
	<tr>
		<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
	<tr>
		<td class="pageHeadingTitle" valign="middle" align="center"><?=HEADING_TITLE?></td>
	</tr>
	<tr>
		<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
	<tr>
		<td>
			<div class="row_separator"></div>
		</td>
	</tr>
	<tr>
		<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
	<tr>
		<td class="affiliateReportTitle"><?=TEXT_AFFILIATE_HEADER . ' ' . $affiliate_sales_split->number_of_rows?></td>
	</tr>
	<tr>
		<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
    <tr>
    	<td>
    		<table border="0" width="100%" cellspacing="0" cellpadding="2" class="affiliateReportBox">
          		<tr>
		            <td class="affiliateReportBoxHeading"><?=TABLE_HEADING_DATE?></td>
		            <td class="affiliateReportBoxHeading" align="right"><?=TABLE_HEADING_VALUE?></td>
		            <td class="affiliateReportBoxHeading" align="right"><?=TABLE_HEADING_PERCENTAGE?></td>
		            <td class="affiliateReportBoxHeading" align="right"><?=TABLE_HEADING_SALES?></td>
		            <td class="affiliateReportBoxHeading">&nbsp;</td>
		            <td class="affiliateReportBoxHeading"><?=TABLE_HEADING_STATUS?></td>
          		</tr>
<?
if ($affiliate_sales_split->number_of_rows > 0) {
	$affiliate_sales_values = tep_db_query($affiliate_sales_split->sql_query);
	$number_of_sales = 0;
	//$sum_of_earnings = 0;
	//$eligible_order_status = explode(',', AFFILIATE_COMMISSSION_ORDER_STATUS);
	while ($affiliate_sales = tep_db_fetch_array($affiliate_sales_values)) {
  		$number_of_sales++;
  		$row_style = ($number_of_sales%2 == "0") ? 'affiliateListingEven' : 'affiliateListingOdd' ;
  		//if (in_array($affiliate_sales['orders_status_id'], $eligible_order_status)) $sum_of_earnings += $affiliate_sales['affiliate_payment'];
?>
				<tr class="<?=$row_style?>">
		            <td class="affiliateReportInfo"><?=tep_date_short($affiliate_sales['affiliate_date'])?></td>
		            <td class="affiliateReportInfo" align="right"><?=$currencies->format($affiliate_sales['affiliate_value'])?></td>
		            <td class="affiliateReportInfo" align="right"><?=$affiliate_sales['affiliate_percent'] . " %"?></td>
		            <td class="affiliateReportInfo" align="right"><?=$currencies->format($affiliate_sales['affiliate_payment'])?></td>
		            <td class="affiliateReportInfo">&nbsp;</td>
		            <td class="affiliateReportInfo"><? if ($affiliate_sales['orders_status']) echo $affiliate_sales['orders_status']; else echo TEXT_DELETED_ORDER_BY_ADMIN; ?></td>
          		</tr>
<?
	}
} else {
?>
          		<tr class="messageRow">
            		<td class="messageData" colspan="5"><?=TEXT_NO_SALES?></td>
          		</tr>
<?
}
?>
			</table>
		</td>
	</tr>
	<tr>
		<td colspan="5"><?=tep_draw_separator()?></td>
	</tr>
<?
if ($affiliate_sales_split->number_of_rows > 0) {
?>
  	<tr>
		<td colspan="5">
			<table border="0" width="100%" cellspacing="0" cellpadding="2">
  				<tr>
    				<td class="pageResultsText"><?=$affiliate_sales_split->display_count(TEXT_DISPLAY_NUMBER_OF_SALES)?></td>
    				<td class="pageResultsText" align="right"><?=TEXT_RESULT_PAGE?> <?=$affiliate_sales_split->display_links(MAX_DISPLAY_PAGE_LINKS, tep_get_all_get_params(array('page', 'info', 'x', 'y')))?></td>
  				</tr>
			</table>
		</td>
	</tr>
<?
}
?>
</table>