<? echo tep_draw_form('affiliate_signup',  tep_href_link(FILENAME_AFFILIATE_SIGNUP, '', 'SSL'), 'post') . tep_draw_hidden_field('action', 'process'); ?>
	<table border="0" width="100%" cellspacing="0" cellpadding="0">
	    <tr>
			<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
		</tr>
		<tr>
			<td class="pageHeadingTitle" valign="middle" align="center"><?=HEADING_TITLE?></td>
		</tr>
		<tr>
			<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
		</tr>
		<tr>
			<td>
				<div class="row_separator"></div>
			</td>
		</tr>
		<tr>
			<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
		</tr>
	  	<tr>
			<td class="instruction"><br><? echo sprintf(TEXT_ORIGIN_AFFILIATE_LOGIN, tep_href_link(FILENAME_AFFILIATE, tep_get_all_get_params(), 'SSL')); ?></td>
		</tr>
		<tr>
			<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
		</tr>
<?
if ($messageStack->size('affiliate_signup') > 0) {
?>
		<tr>
	    	<td><? echo $messageStack->output('affiliate_signup'); ?></td>
		</tr>
		<tr>
	    	<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
		</tr>
<?
}
?>
	  	<tr>
	    	<td>
	    		<table border="0" width="100%" cellspacing="1" cellpadding="2" class="inputBox">
	      			<tr class="inputBoxContents">
	        			<td>
<?
	if (isset($HTTP_GET_VARS['affiliate_email_address'])) $a_email_address = tep_db_prepare_input($HTTP_GET_VARS['affiliate_email_address']);
	$affiliate['affiliate_country_id'] = STORE_COUNTRY;
  	
  	require(DIR_WS_MODULES . 'affiliate_account_details.php');
?>
						</td>
					</tr>
					<tr>
						<td>
							<table border="0" width="100%" cellspacing="1" cellpadding="2" class="buttonBox2">
				          		<tr class="buttonBoxContents2">
	            					<td width="10"><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
	                				<td align="right"><?=tep_image_submit(THEMA.'button_continue.gif', IMAGE_BUTTON_CONTINUE)?></td>
	                				<td width="10"><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
				          		</tr>
				        	</table>
				        </td>
			        </tr>
				</table>
			</td>
	  	</tr>
	</table>
</form>