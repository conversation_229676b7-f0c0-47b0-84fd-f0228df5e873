<table border="0" width="100%" cellspacing="0" cellpadding="0">
	<tr>
		<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
	<tr>
		<td class="pageHeadingTitle" valign="middle" align="center"><?=HEADING_TITLE?></td>
	</tr>
	<tr>
		<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
	<tr>
		<td>
			<div class="row_separator"></div>
		</td>
	</tr>
	<tr>
		<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
    <tr>
        <td>
        	<table border="0" width="100%" cellspacing="0" cellpadding="2">
				<tr>
            		<td class="spacingInfo"><?= TEXT_GREETING . $affiliate['affiliate_firstname'] . ' ' . $affiliate['affiliate_lastname'] . '<br>' . TEXT_AFFILIATE_ID . $affiliate_id?></td>
          		</tr>
        	</table>
        </td>
	</tr>
    <tr>
        <td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
    <tr>
    	<td>
    		<table border="0" width="100%" cellspacing="0" cellpadding="0" class="affiliateReportBox">
          		<tr>
            		<td valign="top">
            			<table border="0" width="100%" cellspacing="0" cellpadding="2">
              				<tr>
                				<td class="affiliateReportTitle"><?=TEXT_SUMMARY_TITLE?></td>
              				</tr>
              				<tr>
                  				<td class="affiliateReportInfo"><?=sprintf(TEXT_INFORMATION_LAST_UPDATE, $affiliate_updated_payment_row["affiliate_payment_last_modified"])?></td>
                			</tr>
            			</table>
            		</td>
          		</tr>
          		<tr>
            		<td>
            			<table width="100%" border="0" align="center" cellpadding="4" cellspacing="2">
                			<tr>
                  				<td width="50%" align="right" class="affiliateReportInfo"><?=TEXT_IMPRESSIONS?><? echo '<a href="javascript:popupWindow(\'' . tep_href_link(FILENAME_AFFILIATE_HELP_1) . '\', \'AffiliateSummary\',450,140);">' . TEXT_SUMMARY_HELP . '</a>&nbsp;:'; ?></td>
				                <td width="50%" class="affiliateReportInfo"><?=$affiliate_impressions?></td>
							</tr>
							<tr>
				                <td align="right" class="affiliateReportInfo"><?=TEXT_VISITS?><? echo '<a href="javascript:popupWindow(\'' . tep_href_link(FILENAME_AFFILIATE_HELP_2) . '\', \'AffiliateSummary\',450,140)">' . TEXT_SUMMARY_HELP . '</a>&nbsp;:'; ?></td>
				                <td class="affiliateReportInfo"><?=$affiliate_clickthroughs?></td>
                			</tr>
                			<tr>
				                <td align="right" class="affiliateReportInfo"><?=TEXT_VISITS_TRANSACTIONS . '&nbsp;:'?></td>
				                <td class="affiliateReportInfo"><?=$affiliate_clickthroughs_sales?></td>
							</tr>
							<tr>
				                <td align="right" class="affiliateReportInfo"><?=TEXT_CONVERSION?><? echo '<a href="javascript:popupWindow(\'' . tep_href_link(FILENAME_AFFILIATE_HELP_4) . '\', \'AffiliateSummary\',450,140)">' . TEXT_SUMMARY_HELP . '</a>&nbsp;:'; ?></td>
				                <td class="affiliateReportInfo"><?=$affiliate_conversions?></td>
                			</tr>
                			<tr>
				                <td align="right" class="affiliateReportInfo"><?=TEXT_TRANSACTIONS?><? echo '<a href="javascript:popupWindow(\'' . tep_href_link(FILENAME_AFFILIATE_HELP_3) . '\', \'AffiliateSummary\',450,140)">' . TEXT_SUMMARY_HELP . '</a>&nbsp;:'; ?></td>
				                <td class="affiliateReportInfo"><?=$affiliate_transactions?></td>
							</tr>
                			<tr>
			                  	<td align="right" class="affiliateReportInfo"><?=TEXT_AMOUNT?><? echo '<a href="javascript:popupWindow(\'' . tep_href_link(FILENAME_AFFILIATE_HELP_5) . '\', \'AffiliateSummary\',450,140)">' . TEXT_SUMMARY_HELP . '</a>&nbsp;:'; ?></td>
			                  	<td class="affiliateReportInfo"><?=$currencies->format($affiliate_amount)?></td>
							</tr>
							<tr>
			                  	<td align="right" class="affiliateReportInfo"><?=TEXT_AVERAGE?><? echo '<a href="javascript:popupWindow(\'' . tep_href_link(FILENAME_AFFILIATE_HELP_6) . '\', \'AffiliateSummary\',450,140)">' . TEXT_SUMMARY_HELP . '</a>&nbsp;:'; ?></td>
			                  	<td class="affiliateReportInfo"><? echo $currencies->format($affiliate_average); ?></td>
                			</tr>
                			<tr>
                  				<td align="right" class="affiliateReportInfo"><?=TEXT_COMMISSION_RATE?><? echo '<a href="javascript:popupWindow(\'' . tep_href_link(FILENAME_AFFILIATE_HELP_7) . '\', \'AffiliateSummary\',450,140)">' . TEXT_SUMMARY_HELP . '</a>&nbsp;:'; ?></td>
                  				<td class="affiliateReportInfo"><?=tep_round($affiliate_percent, 2). '%'?></td>
                  			</tr>
                  			<tr>
                  				<td align="right" class="affiliateReportInfo"><?=TEXT_COMMISSION?><? echo '<a href="javascript:popupWindow(\'' . tep_href_link(FILENAME_AFFILIATE_HELP_8) . '\', \'AffiliateSummary\',450,140)">' . TEXT_SUMMARY_HELP . '</a>&nbsp;:'; ?></td>
                  				<td class="affiliateReportInfo"><?=$currencies->format($affiliate_commission)?></td>
                			</tr>
                			<tr>
                  				<td align="right" class="affiliateReportInfo"><?=TEXT_INFORMATION_PAID_AMOUNT . '&nbsp;:'?></td>
                  				<td class="affiliateReportInfo"><?=$currencies->format($affiliate_paid_amount)?></td>
                			</tr>
                			<tr>
                  				<td align="right" class="affiliateReportInfo"><?=TEXT_INFORMATION_WITHHOLD_AMOUNT . '&nbsp;:'?></td>
                  				<td class="affiliateReportInfo"><?=$currencies->format($actual_payment_amount-$affiliate_paid_amount)?></td>
                			</tr>
                			<tr>
                  				<td align="right" class="affiliateReportInfo"><?=TEXT_INFORMATION_WITHIN_PERIOD_AMOUNT . '&nbsp;:'?></td>
                  				<td class="affiliateReportInfo"><?=$currencies->format($affiliate_within_period_payment)?></td>
                			</tr>
                			<tr>
                  				<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
                			</tr>
                 			<tr>
                  				<td align="center" class="spacingInstruction" colspan="2"><?=TEXT_SUMMARY?></td>
                			</tr>
                			<tr>
                  				<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
                			</tr>
            			</table>
            		</td>
          		</tr>
        	</table>
        </td>
	</tr>
	<tr>
		<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
	<tr>
		<td align="right">
			<table border="0" width="100%" cellspacing="1" cellpadding="2" class="buttonBox">
	    		<tr class="buttonBoxContents">
	        		<td>
	        			<table border="0" width="100%" cellspacing="0" cellpadding="2">
	          				<tr>
				                <td width="10"><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
				                <td align="right"><? echo tep_image_button(THEMA.'button_affiliate_banners.gif', IMAGE_BANNERS, tep_href_link(FILENAME_AFFILIATE_BANNERS, '')) . '&nbsp;' . tep_image_button(THEMA.'button_affiliate_clickthroughs.gif', IMAGE_CLICKTHROUGHS, tep_href_link(FILENAME_AFFILIATE_CLICKS, '')) . '&nbsp;' . tep_image_button(THEMA.'button_affiliate_sales.gif', IMAGE_SALES, tep_href_link(FILENAME_AFFILIATE_SALES, '','SSL')) ; ?></td>
				                <td width="10"><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
	          				</tr>
	        			</table>
	        		</td>
	      		</tr>
	    	</table>
		</td>
	</tr>
</table>