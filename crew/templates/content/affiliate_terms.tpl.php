<table border="0" width="100%" cellspacing="0" cellpadding="0">
	<tr>
		<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
	<tr>
		<td class="pageHeadingTitle" valign="middle" align="center"><?=HEADING_TITLE?></td>
	</tr>
	<tr>
		<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
	<tr>
		<td>
			<div class="row_separator"></div>
		</td>
	</tr>
	<tr>
		<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
    <tr>
    	<td>
    		<table width="100%" border="0" cellspacing="0" cellpadding="1" >
          		<tr>
            		<td>
            			<table width="100%" border="0" cellspacing="0" cellpadding="4" class="instructionBox">
              				<tr>
                				<td class="instructionBoxHeading"><?=HEADING_AFFILIATE_PROGRAM_TITLE?></td>
              				</tr>
              				<tr class="instructionBoxContents">
                    			<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
                  			</tr>
              				<tr class="instructionBoxContents">
                				<td><?=TEXT_INFORMATION?></td>
              				</tr>
              				<tr>
    							<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
							</tr>
    						<tr>
    							<td>
    								<table border="0" width="100%" cellspacing="0" cellpadding="2" class="buttonBox">
          								<tr class="buttonBoxContents">
            								<td width="50%" align="right" valign="top">
            									<? echo tep_image_button(THEMA.'button_continue.gif', IMAGE_BUTTON_CONTINUE, tep_href_link(FILENAME_AFFILIATE_SIGNUP, '', 'SSL')) ; ?>&nbsp;
            									<? echo tep_image_button(THEMA.'button_login.gif', IMAGE_BUTTON_LOGIN, tep_href_link(FILENAME_AFFILIATE, '', 'SSL')) ; ?>
            								</td>
          								</tr>
        							</table>
        						</td>
							</tr>
            			</table>
            		</td>
          		</tr>
        	</table>
        </td>
	</tr>
</table>