<table width="100%" border="0" cellspacing="2" cellpadding="1">
	<tr>
		<td>
			<table border="0" width="100%" cellspacing="0" cellpadding="0">
				<tr> 
					<td width="100%">
                        <td class="pageHeadingTitle" valign="middle" align="center"><?=HEADING_TITLE?></td>
					</td>
				</tr>
				<tr> 
					<td><?php echo tep_draw_separator('pixel_trans.gif', '100%', '10'); ?></td>
				</tr>
				<tr> 
					<td>
						<table border="0" width="100%" cellspacing="0" cellpadding="2">
<?	
							$languages_query = tep_db_query("select languages_id, name, code, image, directory from " . TABLE_LANGUAGES . " order by sort_order");
							while ($languages = tep_db_fetch_array($languages_query)) {
								$languages_array[] = array('id'			=> $languages['languages_id'],
												   'name'		=> $languages['name'],
								         		   'code'		=> $languages['code'],
								         		   'image'		=> $languages['image'],
								                   'directory'	=> $languages['directory']);
							}
							
							for ($i=0; $i<sizeof($languages_array); $i++) {		 
								$this_language_id = $languages_array[$i]['id'];
								$this_language_name = $languages_array[$i]['name'];
								$this_language_code = $languages_array[$i]['code'];
								$this_language_image = $languages_array[$i]['image'];
								$this_language_directory = $languages_array[$i]['directory'];
								echo " <tr>\n";
								
								$products_query = tep_db_query("select p.products_id, pd.products_name, p.products_model ,pd.products_name, p.products_price, p.products_tax_class_id, p.products_date_added, m.manufacturers_name from " . TABLE_PRODUCTS . " p , " . TABLE_CATEGORIES . " c, " . TABLE_PRODUCTS_TO_CATEGORIES . " p2c left join " . TABLE_MANUFACTURERS . " m on p.manufacturers_id = m.manufacturers_id left join " . TABLE_PRODUCTS_DESCRIPTION . " pd on p.products_id = pd.products_id and pd.language_id = '" . (int)$languages_id . "' left join " . TABLE_SPECIALS . " s on p.products_id = s.products_id where p.products_id = p2c.products_id and c.categories_id = p2c.categories_id and products_status = '1' order by p.products_sort_order ");
								$products_array = array();
								
								while($products = tep_db_fetch_array($products_query)) {
									$products_array[] = array(	'id'   => $products['products_id'],
														 		'name' => $products['products_name']);
								}
								
								for ($j=0; $j<NR_COLUMNS; $j++) {
									echo "   <td class=main valign=\"top\">\n";
									for ($k=$j; $k<sizeof($products_array); $k+=NR_COLUMNS) {
										$this_products_id   = $products_array[$k]['id'];
										$this_products_name = $products_array[$k]['name'];
										echo "     <a href=\"" . tep_href_link(FILENAME_PRODUCT_INFO, 'name=' .str_replace("/", "&#47;", rawurlencode($this_products_name)). '&products_id=' . $this_products_id . (($this_language_code == DEFAULT_LANGUAGE) ? '' : ('&language=' . $this_language_code)), 'NONSSL', false) . "\">" . $this_products_name . "</a><br>\n";
									}
									echo "   </td>\n";
								}
								echo " </tr>\n";
							}
?>
						</table>
					</td>
				</tr>
			</table>
		</td>
	</tr>
</table>