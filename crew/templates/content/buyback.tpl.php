<script type="text/javascript" src="<?=DIR_WS_JAVASCRIPT . 'xmlhttp.js'?>"></script>
<script>
	function tongle_accordion(menu_id) {
		if (jQuery("#"+menu_id).css('display') == 'none') {
			jQuery(".arrow").each(function (i) {
				jQuery("#"+this.id).attr({src: "<?=DIR_WS_ICONS?>arw_open.gif"});
			});
			jQuery("#arrow_"+menu_id).attr({src: "<?=DIR_WS_ICONS?>arw_close.gif"});
		}
	}
</script>
<script type="text/javascript">
<!--
	function submitForm(formName) {
		jQuery("#"+formName).submit();
	}

	function reset_input_field(opid, text_value) {
		var orders_products_id_array = new Array();

		orders_products_id_array = jQuery("#orders_products_id_array").text();
		opid_arr = orders_products_id_array.split(',');

		for (var opid_cnt = 0; opid_cnt < opid_arr.length; opid_cnt++) {
			jQuery("#trade_note_"+opid_arr[opid_cnt]).hide();
			jQuery("#err_selling_qty_empty_"+opid_arr[opid_cnt]).hide();
			jQuery("#err_qty_invalid_"+opid_arr[opid_cnt]).hide();

			if (opid != opid_arr[opid_cnt]) {
				if (jQuery("#qty_delivered_"+opid_arr[opid_cnt]).val() == '') {
					jQuery("#qty_delivered_"+opid_arr[opid_cnt]).val(text_value);
				}
			} else {
				if (jQuery("#qty_delivered_"+opid).val() == text_value) {
					jQuery("#qty_delivered_"+opid).val('');
				}
			}
		}
	}

	function CalculateTotalPrice() {
		var total_amount = 0;
		var orders_products_id_array = new Array();
		var qty_delivered = 0;
		var unit_price = 0;
		var unit_price_usd = 0;
		var total_price = 0;
		var total_price_usd = 0;
		var total_amount = 0;
		var total_amount_usd = 0;
		var cnt = 0;
		var store_order_id = '';


		orders_products_id_array = jQuery("#orders_products_id_array").text();
		opid_arr = orders_products_id_array.split(',');

		for (var opid_cnt = 0; opid_cnt < opid_arr.length; opid_cnt++) {
			qty_delivered = jQuery("#qty_delivered_"+opid_arr[opid_cnt]).val();
			unit_price = jQuery("#qty_delivered_"+opid_arr[opid_cnt]).next().text();
			unit_price_usd = jQuery("#unit_price_usd").val();
			if(validateInteger(qty_delivered) && validateMayHvPtSignDecimal(unit_price)) {
				if (cnt == 0) {
					store_order_id = opid_arr[opid_cnt];
				} else {
					store_order_id = store_order_id + ',' + opid_arr[opid_cnt];
				}
				total_price = qty_delivered * unit_price;
				total_amount = total_amount + total_price;

				total_price_usd = qty_delivered * unit_price_usd;
				total_amount_usd = total_amount_usd + total_price_usd;
				cnt ++;
			}
		}

		<?php
			if ($_SESSION['currency'] != DEFAULT_CURRENCY) {
		?>
				jQuery("#after_convert").html(total_amount_usd.toFixed(6));
				jQuery("#convertion_note_1").css("display", "block");
				jQuery("#convertion_note_2").css("display", "block");
		<?
			}
		?>

		jQuery("#opid_arr").val(store_order_id);
		jQuery("#total_price_hidden").val(total_amount.toFixed(6));
		jQuery("#total_selling_price").html(total_amount.toFixed(6));

	}

	function PreSubmit() {
		var error = 0;
		var orders_products_id_array = new Array();
		var cnt = 0;
		var store_order_id = '';
		var input_qty = '';

		orders_products_id_array = jQuery("#orders_products_id_array").text();
		opid_arr = orders_products_id_array.split(',');

		for (var opid_cnt = 0; opid_cnt < opid_arr.length; opid_cnt++) {
			input_qty = jQuery("#qty_delivered_"+opid_arr[opid_cnt]).val();

			unit_price = jQuery("#qty_delivered_"+opid_arr[opid_cnt]).next().text();
			if(validateInteger(input_qty) && validateMayHvPtSignDecimal(unit_price)) {
				if (cnt == 0) {
					store_order_id = opid_arr[opid_cnt];
				} else {
					store_order_id = store_order_id + ',' + opid_arr[opid_cnt];
				}

				var min_qty = jQuery.trim(jQuery("#min_qty_"+opid_arr[opid_cnt]).text());
				var max_qty = jQuery.trim(jQuery("#max_qty_"+opid_arr[opid_cnt]).text());
				jQuery("#trade_note_"+opid_arr[opid_cnt]).hide();
				jQuery("#err_qty_invalid_"+opid_arr[opid_cnt]).hide();

				if(input_qty == '') {
					error++;
					jQuery("#err_selling_qty_empty_"+opid_arr[opid_cnt]).show();
				} else {
					if (!validateInteger(input_qty) || eval(min_qty + ">" + input_qty) || eval(input_qty + ">" + max_qty)) {
						error++;
						jQuery("#err_qty_invalid_"+opid_arr[opid_cnt]).show();
					}
				}

				cnt ++;
			}
		}

		if (store_order_id == '') {
			orders_products_id_array = jQuery("#orders_products_id_array").text();
			opid_arr = orders_products_id_array.split(',');

			for (var opid_cnt = 0; opid_cnt < opid_arr.length; opid_cnt++) {
				jQuery("#trade_note_"+opid_arr[opid_cnt]).hide();
				jQuery("#err_selling_qty_empty_"+opid_arr[opid_cnt]).show();
			}
			jQuery.unblockUI();
		} else {
			if(jQuery.trim(jQuery("#game_selection").html()) == '' || jQuery.trim(jQuery("#game_selection").html()) == '&nbsp;') {
				error++;
				jQuery("#err_pls_select_game").show();
			}

			if(jQuery.trim(jQuery("#server_selection").html()) == '' || jQuery.trim(jQuery("#server_selection").html()) == '&nbsp;') {
				error++;
				jQuery("#err_pls_select_server").show();
			}

			if (error == 0) {
				jQuery("#opid_arr").val(store_order_id);
				jQuery('#pre_confirm_btn').attr('disabled', true);
				jQuery("#buyback_form").submit();
			} else {
				jQuery.unblockUI();
			}
		}
	}

	function onListingServer(products_id, cat_id) {
	    jQuery("#meeting_point").html('');
        //jQuery("#trade_mode").html('');
        //jQuery("#min_qty").html('');
        //jQuery("#max_qty").html('');
        jQuery("#game_currency").html('');
        //jQuery("#unit_price").html('');

		var languages_id = '<?=$languages_id?>';
		var server_action = 'get_server_pull_down';
		jQuery.get("supplier_xmlhttp.php?action="+server_action+"&buyback_products_id="+products_id+"&buyback_parent_cat_id="+cat_id+"&slang="+languages_id, function(xml) {
			jQuery(xml).find('response').each(function(){
				var server_pull_down_menu = jQuery("server_pull_down_menu", this).text();
				jQuery("#server_selection").html(server_pull_down_menu);
			});
		});
	}

	function set_selected_server(products_id, cat_id) {
		if (typeof (products_id) != 'undefined' && products_id != null && products_id > 0) {
			jQuery("#latest_news").hide();
			jQuery("#buyback_listing").hide();
			jQuery("#selling_info").hide();
			jQuery("#pre_confirm").show();
			jQuery("#wbb_input_selling_qty").val('');
			jQuery("#convertion_note_1").css("display", "none");
			jQuery("#convertion_note_2").css("display", "none");
			jQuery("#total_selling_price").html('0');

			jQuery("#pre_confirm_table").ajaxStart(function(){
			  	jQuery(this).hide();
			   	jQuery("#pre_confirm_progress_img").show();
			});
			jQuery("#pre_confirm_table").ajaxComplete(function(request, settings){
				jQuery("#pre_confirm_progress_img").hide();
			  	jQuery(this).show();
			});

			var languages_id = '<?=$languages_id?>';
			var server_action = 'pre_selling_info';
			jQuery.get("supplier_xmlhttp.php?action="+server_action+"&buyback_products_id="+products_id+"&buyback_parent_cat_id="+cat_id+"&slang="+languages_id, function(xml) {
				jQuery(xml).find('response').each(function(){
			        var error = jQuery("error", this).text();
			        var notice = jQuery("notice", this).text();

                    if(jQuery("gotoURL", this).length){
                        document.location.href=jQuery("gotoURL", this).text();
                        return false;
                    }

			        var game_pull_down_menu = jQuery("game_pull_down_menu", this).text();
			        var server_pull_down_menu = jQuery("server_pull_down_menu", this).text();
			        var trading_place = jQuery("trading_place", this).text();
			        var trading_method = jQuery("trading_method", this).text();
			        var unit_price_usd = jQuery("unit_price_base", this).text();
			        var unit_price_display = jQuery("unit_price_display", this).text();
			        var product_unit_name = jQuery("product_unit_name", this).text();
			        var currency_symbol = jQuery("currency_symbol", this).text();
			        var buyback_qty_listing = jQuery("buyback_qty_listing", this).text();
			        var captcha_img = jQuery("captcha_img", this).text();
			        var account_edit_link = jQuery("account_edit_link", this).text();

			        jQuery("#selling_qty").html(buyback_qty_listing);
			        jQuery("#game_selection").html(game_pull_down_menu);
			        jQuery("#server_selection").html(server_pull_down_menu);
			        jQuery("#meeting_point").html(trading_place);
			        //jQuery("#trade_mode").html(trading_method);
			        jQuery("#price_per_unit").html(unit_price_display);
			        jQuery("#game_currency").html(product_unit_name);
			        jQuery("#unit_price_usd").val(unit_price_usd);
	                jQuery("#unit_price_display").html(unit_price_display);
	                jQuery(".currency_symbol").html(currency_symbol);
	                jQuery("#captcha_img").html(captcha_img);
	                jQuery("#account_edit_link").attr("href", account_edit_link);

	                jQuery(".bb_error_msg").each (function() {
						jQuery("#"+this.id).hide();
					});
			    });
			});
		}
	}

	function set_selected_game(game_cat_id, server_cat_id) {
		//Simulate changing game list dropdown.
		if (server_cat_id == 'undefined') {
			server_cat_id = '0';
		}

		var languages_id = '<?=$languages_id?>';
		callEvent_onBuybackGameSelection(game_cat_id, server_cat_id);
	}

	function callEvent_onBuybackGameSelection(game_cat_id, server_cat_id) {
		jQuery("#wbb_div_server_listing").html('');
		jQuery("#buyback_listing").show();

		jQuery("#server_listing_loading").show(0,function () {
			jQuery("#wbb_input_game_select").val(game_cat_id);
			var languages_id = '<?=$languages_id?>';
			divhtml = '';
			onBuybackGameSelection(game_cat_id, languages_id, server_cat_id);
		});
	}

	var divhtml = '';
	var div_row_count = 0;

	function add_server_listing_row(products_name, unit_price, buy_qty, cat_id, products_id, is_buyback) {
		if (is_buyback == true) {
			var make_order_link = '<a href="<?=tep_href_link(FILENAME_BUYBACK."#buyback_server_listing");?>" ><?=TEXT_SELL_THIS_LINK?></a>';
			var status_text = '<?=tep_image(DIR_WS_ICONS . 'green.gif', '', 13, 13);?>';
		} else {
			var make_order_link = '<span style="color:#CCCCCC"><?=TEXT_SELL_THIS_LINK?></span>';
			var status_text = '<?=tep_image(DIR_WS_ICONS . 'red.gif', '', 13, 13);?>';
		}

		if (is_buyback == '1') {
			var make_order_link = '<a href="javascript:void(0);" onClick="sell_this_form(\''+products_id+'\', \''+cat_id+'\'); return false;"><?=TEXT_SELL_THIS_LINK?></a>';
			var status_text = '<?=tep_image(DIR_WS_ICONS . 'green.gif', '', 13, 13);?>';
		} else if (is_buyback == '-1') {	// Game buyback list is closed
			var make_order_link = '<span style="color:#CCCCCC"><small><?=TEXT_SELL_THIS_LINK?></small><span class="menu_link">';
			var status_text = '<?=tep_image(DIR_WS_ICONS . 'grey.gif', '', 13, 13);?>';
		} else {
			var make_order_link = '<span style="color:#CCCCCC"><small><?=TEXT_SELL_THIS_LINK?></small><span class="menu_link">';
			var status_text = '<?=tep_image(DIR_WS_ICONS . 'red.gif', '', 13, 13);?>';
		}

		var row_style = 'ordersListing';

		if (div_row_count > 0) {
			divhtml += '<tr><td colspan="5"><div class="row_separator"></div></td></tr>';
		}

		divhtml += '<tr height="25" class="'+row_style+'" onMouseOver="this.className =\'ordersListingOver\'" onMouseOut="this.className =\'ordersListingOut\'">'
				+ '<td align="left" width="45%" class="infoListing" style="padding-left:5px;">' + products_name + '<\/td>'
				+ '<td width="16%" class="infoListing" style="text-align:right;">' +  unit_price + '<\/td>'
				+ '<td width="16%" class="infoListing"  style="text-align:right;">' +  buy_qty + '<\/td>'
				+ '<td width="10%" class="infoListing" style="text-align:center;">' + status_text + '<\/td>'
				+ '<td width="13%" class="menu_link" style="text-align:center;">' + make_order_link + '<\/td>'
				+ '<\/tr>';
		div_row_count++;
	}

	function sell_this_form(product_id, cat_id) {
		jQuery('#pre_submit').val('1');
		jQuery('#pid').val(product_id);
		jQuery('#gcat').val(cat_id);
		jQuery('#select_server_form').submit();
	}

	function show_server_listing_div() {
		if (div_row_count < 20) {
			divhtml = divhtml.replace(/##DYNAMIC_WIDTH##/ig, "20%");
		} else {
			divhtml = divhtml.replace(/##DYNAMIC_WIDTH##/ig, "18%");
		}

		div_row_count = 0;

		document.getElementById('wbb_div_server_listing').innerHTML += divhtml;
	}

 	function do_add_to_favourites() {
 		var product_id = jQuery("#wbb_input_product_select").val();
 		var game_cat_id = jQuery("#wbb_input_game_select").val();
		if (game_cat_id > 0 && product_id > 0) {
			jQuery("#fvl_input_game_select").val(game_cat_id);
			jQuery("#fvl_input_product_select").val(product_id);
			jQuery("#add_to_favourites").submit();
		}
	}

	function deal_type_select(id) {
	    document.getElementById(id).checked = true;
	}

	function refresh_server_list () {
		jQuery("#wbb_div_server_listing").html('');

		jQuery("#server_listing_loading").show(0, function () {
			var game_cat_id = jQuery("#wbb_input_game_select").val();
			var languages_id = '<?=$languages_id?>';
			var server_cat_id = 0;

			onBuybackGameSelection(game_cat_id, languages_id, server_cat_id);
		});
	}
// -->
</script>
<?
if (BUYBACK_ENABLED != 'true') {
?>
			<h1><?=TEXT_BUYBACK_IS_OFF?></h1>
			<div class="solidThickLine"></div>
			<div class="break_line"></div>
<?
} else {
?>

	<table border="0" cellpadding="0" cellspacing="0" width="100%">
<?	if ($messageStack->size($content) > 0) { ?>
		<tr>
	    	<td><?=$messageStack->output($content)?></td>
		</tr>
		<tr>
			<td><?=tep_draw_separator('pixel_trans.gif', '100%', '5')?></td>
		</tr>
<?	} ?>
		<tr>
			<td>
<?
$display_style = $action == 'update' ? 'show' : 'hide';

$listing_HTML = '';
$preSubmitHTML = '';

$listing_HTML .='   <table border="0" cellpadding="0" cellspacing="0" width="100%" id="buyback_server_listing"><tr><td><div id="div_game_reopen_count_down" class="messageStackWarning"></div></td></tr></table>'
					.tep_draw_hidden_field('wbb_input_game_select', $_REQUEST['gcat'], 'id="wbb_input_game_select"').
					'<table border="0" cellpadding="0" cellspacing="0" width="100%">
						<tbody class="'.$display_style.'" id="buyback_listing" name="buyback_listing">
							<tr>
						        <td align="center">
						        	<table border="0" cellpadding="0" cellspacing="0" width="598">
						          		<tr>
						            		<td align="center">
						            			<table cellpadding="1" border="0" cellspacing="0" width="100%" border="0" class="infoBox" style="padding-top:0px;padding-left:0px;padding-right:0px;">
						            				<tr><td>'.tep_image(DIR_WS_IMAGES . 'blank.gif', '', 1, 4).'</td></tr>
						                			<tr>
						                  				<td>
						                    				<table border="0" cellpadding="0" cellspacing="0" width="100%">
						                    					<tr>
																	<td align="right" width="1">'.tep_image(DIR_WS_IMAGES . 'blank.gif', '', 1, 1).'</td>
																	<td class="boxHeaderLeftTable">'.tep_image(DIR_WS_IMAGES . 'blank.gif', '', 1, 1).'</td>
																	<td class="boxHeaderCenterTable">
																	<table border="0" cellpadding="0" cellspacing="0" width="100%">
																		<tr valign="center">
																			<th align="left" width="45%" class="product_heading"><span id="game_name"></span> '.TABLE_HEADING_SERVER.'</th>
													                      	<th align="right" width="16%" class="product_heading">'.TABLE_HEADING_PER_UNIT.' <span style="color: white;font-size:17px;font-weight:bold;">*</span></th>
													                      	<th align="right" width="16%" class="product_heading" align="center">'.TABLE_HEADING_QTY.'<span id="table_heading_product_unit_name" name="table_heading_product_unit_name" class="title-text"></span></th>
													                      	<th align="center" width="10%" class="product_heading">'.TABLE_HEADING_STATUS.'</th>
													                      	<th align="left" width="13%" class="product_heading">&nbsp;</th>
																		</tr>
																	</table>
														    		</td>
														    		<td class="boxHeaderRightTable">'.tep_image(DIR_WS_IMAGES . 'blank.gif', '', 1, 1).'</td>
														    		<td align="right" width="1">'.tep_image(DIR_WS_IMAGES . 'blank.gif', '', 1, 1).'</td>
																</tr>
							                    			</table>
						                 	 			</td>
						                			</tr>
						                			<tr>
						                  				<td valign="top">
							                  				<table border="0" cellpadding="0" cellspacing="0" width="100%" valign="top">
							                    				<tr>
							                      					<td valign="top">
							                      						'.tep_draw_form('select_server_form', tep_href_link(FILENAME_BUYBACK, tep_get_all_get_params()), 'POST', 'id="select_server_form"').'
							                      						'.tep_draw_hidden_field('pre_submit', '', 'id="pre_submit"').'
							                      						'.tep_draw_hidden_field('pid', '', 'id="pid"').'
							                      						'.tep_draw_hidden_field('gcat', '', 'id="gcat"').'
							                      						</form>
							                      						<div style="position:relative; height:300px; width:100%; overflow:auto;">
																			<div id="wbb_div_server_listing" name="wbb_div_server_listing" style="position:absolute;width:100%;height:100%;">
																				<div>'.TEXT_MAIN_PLEASE_SELECT_GAME.'</div>
																			</div>
																			<div style="position:absolute;width:100%;text-align:center;">
																				<table border="0" cellpadding="0" cellspacing="0" valign="top" width="100%">
											                    				<tr>
											                      					<td valign="top"><div id="server_listing_loading" style="display:none;text-align:center;margin:0px 45%;">'.tep_image(DIR_WS_IMAGES . 'lightbox-ico-loading.gif', '', '32', '32').'</div></td>
											                      				</tr>
											                      				</table>
																			</div>
																		</div>
							                      					</td>
							                    				</tr>
							                  				</table>
							                  			</td>
							                		</tr>
							                		<tr><td height="25"><div class="breakLineImg"></div></td></tr>
							                		<tr>
							                			<td height="40">
							                				<table border="0" cellpadding="15" cellspacing="0" width="100%">
							                    				<tr>
							                      					<td width="80">
							                      						<div style="padding-right:5px;float:left;">'.tep_image(DIR_WS_ICONS . 'green.gif', '', 13, 13).'</div>
							                      						<div style="float:left;">'.TEXT_NORMAL.'</div>
							                      					</td>
							                      					<td width="80">
							                      						<div style="padding-right:5px;float:left;">'.tep_image(DIR_WS_ICONS . 'red.gif', '', 13, 13).'</div>
							                      						<div style="float:left;">'.TEXT_FULL.'</div>
							                      					</td>
							                      					<td width="80">
							                      						<div style="padding-right:5px;float:left;">'.tep_image(DIR_WS_ICONS . 'grey.gif', '', 13, 13).'</div>
							                      						<div style="float:left;">'.TEXT_LIST_CLOSE.'</div>
							                      					</td>
							                      					<td align="right">
							                      						<div style="float:right;font-size:'.$LARGEST_FONT.'px;"><a href="javascript:void(0);" onclick="return refresh_server_list(); false" >'.TEXT_REFRESH.'</a></div>
							                      						<div style="padding-right:5px;float:right;">
							                      							'.tep_image(DIR_WS_ICONS . 'refresh.gif', '',11, 13, 'id="refresh"').'
							                      						</div>
							                      					</td>
							                      				</tr>
							                      				<tr>
							                      					<td colspan="4">
							                      					<div class="langInfoSizeMedium langInfoLineHeight1_5"><span style="color: black;font-size:17px;font-weight:bold;">*</span>&nbsp;'.TEXT_PRICE_NOTES.'</div>
							                      					</td>
							                      				</tr>
							                      			</table>
							                			</td>
							                		</tr>
								              	</table>
											</td>
										</tr>
									</table>
								</td>
							</tr>
						</tbody>
					</table>';

$preSubmitHTML .=	tep_draw_form('buyback_form', tep_href_link(FILENAME_BUYBACK, tep_get_all_get_params(array('action')) . 'action=confirm'), 'POST', 'id="buyback_form"', 'onSubmit="document.buyback_form.wbb_button_confirm.disabled=true;"')
				   .tep_draw_hidden_field('opid_arr', '', 'id="opid_arr"')
				   .tep_draw_hidden_field('unit_price_usd', '0', 'id="unit_price_usd"')
				   .tep_draw_hidden_field('total_price_hidden', '0', 'id="total_price_hidden"')
				   .'<div id="pre_confirm" style="display:none;">
					<table border="0" cellpadding="0" cellspacing="0" width="100%" border="0" class="subCategoryBox" style="padding:7px 0px 7px 0px;">
    				<tr>
      				<td>
      					<table border="0" cellpadding="0" cellspacing="0" width="100%">
        				<tr>
        					<td>
	        				<table border="0" cellpadding="0" cellspacing="0" width="100%">
	        					<tr>
									<td align="right" width="1">'.tep_image(DIR_WS_IMAGES . "blank.gif", "", 1, 1).'</td>
									<td class="boxHeaderLeftTable">'.tep_image(DIR_WS_IMAGES . "blank.gif", "", 1, 1).'</td>
									<td class="boxHeaderCenterTable">
									<table border="0" cellpadding="0" cellspacing="0" width="100%">
										<tr valign="center">
											<th align="left" class="product_heading">'.HEADING_TITLE.'</th>
											<!--start vip orders -->
										</tr>
									</table>
						    		</td>
						    		<td class="boxHeaderRightTable">'.tep_image(DIR_WS_IMAGES . "blank.gif", "", 1, 1).'</td>
						    		<td align="right" width="1">'.tep_image(DIR_WS_IMAGES . "blank.gif", "", 1, 1).'</td>
								</tr>
							</table>
						</td>
						</tr>
						<tr>
							<td colspan="5" style="padding:20px;text-align:center;" align="center" id="pre_confirm_progress_img">
							'.tep_image(DIR_WS_IMAGES . "lightbox-ico-loading.gif", "", 32, 32).'
							</td>
						</tr>
						<tr>
							<td colspan="5">
							<table border="0" cellpadding="10" cellspacing="0" width="100%" id="pre_confirm_table" style="display:none;">
								<tr>
									<td width="100" valign="top">'.ENTRY_GAME_SELECTION.':</td>
									<td valign="top">
										<table border="0" width="100%" cellspacing="0" cellpadding="0">
										<tr>
											<td valign="top" width="60%">
												<div id="game_selection" onclick="document.getElementById(\'err_pls_select_game\').style.display=\'none\';">&nbsp;</div>
											</td>
											<td valign="top" align="left" width="40%">
												<div id="err_pls_select_game" style="display:none;font-weight:bold;color:red;">'.WARNING_PLS_SELECT_GAME.'</div>
											</td>
										</tr>
										</table>
									</td>
								</tr>
								<tr>
									<td width="100" valign="top">'.ENTRY_SERVER_SELECTION.':</td>
									<td valign="top">
										<table border="0" width="100%" cellspacing="0" cellpadding="0">
										<tr>
											<td valign="top" width="60%">
												<span id="server_selection" onclick="document.getElementById(\'err_pls_select_server\').style.display=\'none\';">&nbsp;</span>
											</td>
											<td valign="top" align="left" width="40%">
												<div id="err_pls_select_server" style="display:none;font-weight:bold;color:red;">'.WARNING_PLS_SELECT_SERVER.'</div>
											</td>
										</tr>
										</table>
									</td>
								</tr>
								<tr>
									<td width="100" valign="top">'.ENTRY_MEETING_POINT.':</td>
									<td valign="top"><div id="meeting_point">&nbsp;</td>
								</tr>
								<tr>
									<td width="100" valign="top">'.TEXT_PRICE_PER_UNIT.':</td>
									<td valign="top"><div id="price_per_unit" style="float:left;">&nbsp;</div></td>
								</tr>
								<tr>
									<td colspan="2"><div class="row_separator"></div></td>
								</tr>
								'.$split_order_listing.'
								<tr>
									<td colspan="2"><div class="row_separator"></div></td>
								</tr>
								<tr>
									<td width="100" valign="top">&nbsp;</td>
									<td valign="top">
										<div style="float:left;" id="captcha_img"></div>
										<div style="float:left;padding-top: 6px;padding-left:5px;"><div style="padding-bottom: 5px;">'.sprintf(TEXT_REFRESH_CAPTCHA, '#captcha_img').'</div>'.tep_draw_input_field("captcha_code", TEXT_CAPTCHA_ANSWER, 'style="height:25px;font-size:10px;color:silver;padding:3.5px;" onClick="clear_input_field(\'captcha_code\')" size="2" maxlength="2" id="captcha_code"').' '.TEXT_CAPTCHA_INSTRUCTION.'</div>
									</td>
								</tr>
								<tr>
									<td width="100" valign="top">'.ENTRY_TOTAL_SELLING_PRICE.'</td>
									<td valign="top">
										<table border="0" width="100%" cellpadding="0" cellspacing="0">
											<tr>
												<td valign="top">
													<span class="currency_symbol" style="float:left;"></span><span id="total_selling_price" style="float:left;">0</span> <div id="convertion_note_1" style="float:left;color:red;display:none;">&nbsp;&nbsp;( US $ <span id="after_convert"></span> )</div>
													<div id="convertion_note_2" style="clear:both;color:red;display:none;padding-top:3px;">
														'.TEXT_PRICE_NOTES_2.'
													</div>
												</td>
												<td align="right">
													'.tep_div_button(2, BUTTON_TRADE_NOW,'javascript:blockUI_disable(\'<h1>'.TEXT_IS_LOADING.'</h1>\');PreSubmit();', 'style="float:right;" id="pre_confirm_btn"', 'gray_button').'
												</td>
											</tr>
										</table>
									</td>
								</tr>
							</table>
							</td>
						</tr>
            			</table>
     	 			</td>
    			</tr>
    		</table>
    		</form>
			<div class="hide" height="1" width="1">

				'. tep_draw_form('add_to_favourites', tep_href_link(FILENAME_MY_FAVOURITE_LINKS, tep_get_all_get_params(array('action')) . 'action=add', 'SSL'), 'POST', ' id="add_to_favourites"').'
				'. tep_draw_hidden_field('fvl_input_game_select', '0', 'id="fvl_input_game_select"').'
				'. tep_draw_hidden_field('fvl_input_product_select', '0', 'id="fvl_input_product_select"').'
				'. '</form>

			</div>
			<table><tr><td><div class="breakLine" style="height:10px;"></div></td></tr></table>
			</div>';

	switch ($action) {
		case 'confirm_msg':
			$boxContent = '
							<table border="0" cellpadding="0" cellspacing="0" width="598">
								<tr height="5px" style="background-image: url(images/announcement_top.gif);">
									<td></td>
								</tr>
								<tr style="background-image: url(' . DIR_WS_IMAGES . 'announcement_center.gif); background-repeat: repeat-y;">
									<td align="center">
										<table border="0" cellpadding="0" cellspacing="0" width="62%">
											<tr>
							            		<td colspan="2">' . tep_draw_separator('pixel_trans.gif', '1', '10') . '</td>
							            	</tr>
							            	<tr>
							            		<td align="center" colspan="3"><small><b>Thank you</b></small></td>
							            	</tr>
											<tr>
												<td>&nbsp;</td>
												<td class="main" align="center">' . TEXT_ORDER_POSTED_INSTRUCTIONS . '</td>
												<td>&nbsp;</td>
											</tr>
											<tr>
							            		<td colspan="2">' . tep_draw_separator('pixel_trans.gif', '1', '10') . '</td>
							            	</tr>
											<tr>
												<td colspan="2" align="center">
													<table cellpadding=0 border=0 cellspacing=0><tr><td>
														'.tep_div_button(2, IMAGE_BUTTON_SELL_MORE,tep_href_link(FILENAME_BUYBACK), '', 'gray_button').
														tep_div_button(2, IMAGE_BUTTON_TRANSFER_NOW,tep_href_link(FILENAME_MY_ORDER_HISTORY), '', 'green_long_button').'
													</td></tr></table>
												</td>
											</tr>
											<tr>
							            		<td colspan="2">' . tep_draw_separator('pixel_trans.gif', '1', '10') . '</td>
							            	</tr>
										</table>
									</td>
								</tr>
								<tr height="5px" style="background-image: url(' . DIR_WS_IMAGES . 'announcement_bottom.gif);">
									<td></td>
								</tr>
							</table>';

			echo $boxContent;
			$boxContent = '';
			break;

		case 'update':

			echo $preSubmitHTML;
?>


<?
if ($get_news_select_num > 0) {
?>
			<table id="latest_news" cellpadding="0" border="0" cellspacing="0" width="100%" border="0" class="subCategoryBox" style="padding:7px 0px 7px 0px;">
    			<tr>
      				<td>
      					<table border="0" cellpadding="0" cellspacing="0" width="100%">
        				<tr>
        					<td>
	        				<table border="0" cellpadding="0" cellspacing="0" width="100%">
	        					<tr>
									<td align="right" width="1"><?=tep_image(DIR_WS_IMAGES . 'blank.gif', '', 1, 1)?></td>
									<td class="boxHeaderLeftTable"><?=tep_image(DIR_WS_IMAGES . 'blank.gif', '', 1, 1)?></td>
									<td class="boxHeaderCenterTable">
									<table border="0" cellpadding="0" cellspacing="0" width="100%">
										<tr valign="center">
											<th align="left" class="product_heading"><?=TABLE_HEADING_LATEST_NEWS?></th>
											<!--start vip orders -->
										</tr>
									</table>
						    		</td>
						    		<td class="boxHeaderRightTable"><?=tep_image(DIR_WS_IMAGES . 'blank.gif', '', 1, 1)?></td>
						    		<td align="right" width="1"><?=tep_image(DIR_WS_IMAGES . 'blank.gif', '', 1, 1)?></td>
								</tr>
							</table>
						</td>
						</tr>
						<tr>
							<td colspan="5">
							<table border="0" cellpadding="10" cellspacing="0" width="100%">
<?php

	$news_count = 0;
	$arrow_img = '';
	while ($get_news_select_row = tep_db_fetch_array($get_news_select_result_sql)) {
		$arrow_img = ($news_count == 0) ? 'arw_close.gif' : 'arw_open.gif';
		echo '<tr style="cursor:pointer;" class="buybackBoxHeader" onclick="tongle_description(\'arrow_'.$get_news_select_row['news_id'].'\', \'buybackBoxHeader\');">
				<td>
				<table border="0" cellpadding="0" cellspacing="0" width="100%">
				<tr>
					<td class="menuTitle">
					'.tep_date_short($get_news_select_row["date_added"]).' - '.$get_news_select_row['headline'].'
					</td>
					<td width="20" align="center"><div class="buybackBoxHeaderExpand" id="arrow_'.$get_news_select_row['news_id'].'_icon"><!-- --></div></td>
				</tr>
				</table>

				<table id="arrow_'.$get_news_select_row['news_id'].'_long" style="display:none;">
					<tr>
						<td>
						<div style="overflow:auto;height:100%;" class="langInfoSizeMedium langInfoLineHeight1_5">
						'.$get_news_select_row['content'].'
						</div>
						</td>
					</tr>
				</table>

				</td>
			</tr>';
		echo '<tr>
				<td style="padding:0px 10px 0px 10px;">
				<div class="row_separator">' . tep_draw_separator('pixel_trans.gif', '100%', '1') . '</div>
				</td>
			</tr>';

		$news_count ++;
	}
	echo '<tr>
				<td style="padding:10px;" align="right">
					<div>
						<div style="float: right;"><b><a href="'.tep_href_link(FILENAME_BUYBACK, tep_get_all_get_params(array('action')) . 'action=latest_news').'">'.LINK_READ_MORE.'</a></b></div>
						<div style="margin: 2px 6px 0pt 0pt; float: right;" class="triangleRightIconBlue"><!-- --></div>
					</div>
				</td>
			</tr>';
?>
							</table>
							</td>
						</tr>
            			</table>
     	 			</td>
    			</tr>
    		</table>
    		<table><tr><td><div class="breakLine" style="height:10px;"></div></td></tr></table>
<?
	}
?>
			<?=$listing_HTML?>
			<table id="selling_info" cellpadding="0" border="0" cellspacing="0" width="100%" border="0" class="subCategoryBox" style="padding:7px 0px 7px 0px;">
    			<tr>
      				<td>
      					<table border="0" cellpadding="0" cellspacing="0" width="100%">
        				<tr>
        					<td>
	        				<table border="0" cellpadding="0" cellspacing="0" width="100%">
	        					<tr>
									<td align="right" width="1"><?=tep_image(DIR_WS_IMAGES . 'blank.gif', '', 1, 1)?></td>
									<td class="boxHeaderLeftTable"><?=tep_image(DIR_WS_IMAGES . 'blank.gif', '', 1, 1)?></td>
									<td class="boxHeaderCenterTable">
									<table border="0" cellpadding="0" cellspacing="0" width="100%">
										<tr valign="center">
											<th align="left" width="45%" class="product_heading"><?=TABLE_HEADING_SELLER_INFO?></th>
											<!--start vip orders -->
										</tr>
									</table>
						    		</td>
						    		<td class="boxHeaderRightTable"><?=tep_image(DIR_WS_IMAGES . 'blank.gif', '', 1, 1)?></td>
						    		<td align="right" width="1"><?=tep_image(DIR_WS_IMAGES . 'blank.gif', '', 1, 1)?></td>
								</tr>
							</table>
							</td>
						</tr>
						<tr>
							<td>
							<table border="0" cellpadding="10" cellspacing="0" width="100%">
								<tr>
									<td>
										<div class="langInfoSizeMedium langInfoLineHeight1_5">
										<?=TEXT_SELLER_INFO_DESC?>
										</div>
									</td>
								</tr>
							</table>
							</td>
						</tr>
            			</table>
     	 			</td>
    			</tr>
    		</table>

<?
			break;
		case 'latest_news':
			$news_count = 0;
			$arrow_img = '';
			$listing_split = new splitPageResults($get_news_select_sql, MAX_DISPLAY_LATEST_NEWS_PAGE);
?>
			<table cellpadding="0" border="0" cellspacing="0" width="100%" border="0" class="subCategoryBox" style="padding:7px 0px 7px 0px;">
    			<tr>
      				<td>
      					<table border="0" cellpadding="0" cellspacing="0" width="100%">
        				<tr>
        					<td>
	        				<table border="0" cellpadding="0" cellspacing="0" width="100%">
	        					<tr>
									<td align="right" width="1"><?=tep_image(DIR_WS_IMAGES . 'blank.gif', '', 1, 1)?></td>
									<td class="boxHeaderLeftTable"><?=tep_image(DIR_WS_IMAGES . 'blank.gif', '', 1, 1)?></td>
									<td class="boxHeaderCenterTable">
									<table border="0" cellpadding="0" cellspacing="0" width="100%">
										<tr valign="center">
											<th align="left" class="product_heading"><?=TABLE_HEADING_LATEST_NEWS?></th>
											<!--start vip orders -->
										</tr>
									</table>
						    		</td>
						    		<td class="boxHeaderRightTable"><?=tep_image(DIR_WS_IMAGES . 'blank.gif', '', 1, 1)?></td>
						    		<td align="right" width="1"><?=tep_image(DIR_WS_IMAGES . 'blank.gif', '', 1, 1)?></td>
								</tr>
							</table>
						</td>
						</tr>
						<tr>
							<td colspan="5">
							<table border="0" cellpadding="10" cellspacing="0" width="100%">
<?php
if ($listing_split->number_of_rows > 0) {
	$get_news_select_result_sql = tep_db_query($listing_split->sql_query);
	while ($get_news_select_row = tep_db_fetch_array($get_news_select_result_sql)) {
		$arrow_img = ($news_count == 0) ? 'arw_close.gif' : 'arw_open.gif';
		echo '<tr style="cursor:pointer;" class="buybackBoxHeader" onclick="tongle_description(\'arrow_'.$get_news_select_row['news_id'].'\', \'buybackBoxHeader\');">
				<td>
				<table border="0" cellpadding="0" cellspacing="0" width="100%">
				<tr>
					<td class="menuTitle">
					'.tep_date_short($get_news_select_row["date_added"]).' - '.$get_news_select_row['headline'].'
					</td>
					<td width="20" align="center"><div class="buybackBoxHeaderExpand" id="arrow_'.$get_news_select_row['news_id'].'_icon"><!-- --></div></td>
				</tr>
				</table>
				<table id="arrow_'.$get_news_select_row['news_id'].'_long" style="display:none;">
					<tr>
						<td>
						<div style="overflow:auto;width:568px;height:100%;" class="langInfoSizeMedium langInfoLineHeight1_5">
						'.$get_news_select_row['content'].'
						</div>
						</td>
					</tr>
				</table>
				</td>
			</tr>';
		echo '<tr>
				<td style="padding:0px 10px 0px 10px;">
				<div class="row_separator"></div>
				</td>
			</tr>';

		$news_count ++;
	}

	if ( ($listing_split->number_of_rows > 0) && ( (PREV_NEXT_BAR_LOCATION == '2') || (PREV_NEXT_BAR_LOCATION == '3') )) {
		echo '<tr><td align="center">'.$listing_split->display_links(MAX_DISPLAY_LATEST_NEWS_PAGE, tep_get_all_get_params(array('page'))).'</td></tr>';
	}
}
?>
							</table>
							</td>
						</tr>
            			</table>
     	 			</td>
    			</tr>
    		</table>
    		<?=$listing_HTML?>
<?

			break;
		case 're-submit':
			echo $preSubmitHTML;

			echo '<script>';
			echo 'set_selected_server("'.tep_db_prepare_input($_SESSION[$form_session_name]['wbb_input_product_select']).'", "'.tep_db_prepare_input($_SESSION[$form_session_name]['wbb_input_game_select']).'");';
			echo '</script>';
			break;
		case 'buyback_checkout':
			echo $preSubmitHTML;

			echo '<script>';
			echo 'set_selected_server("'.tep_db_prepare_input($_GET['pid']).'", "'.tep_db_prepare_input($_GET['gcat']).'");';
			echo '</script>';
			break;
	}//end first form
?>
		</td>
	</tr>
	</table>
<?

	if ($action == 'favourites_link') {
		echo $preSubmitHTML;

		echo '<script>';
		echo 'set_selected_server("'.tep_db_prepare_input($_GET['pid']).'", "'.tep_db_prepare_input($_GET['gcat']).'");';
		echo '</script>';
	}

	if (isset($_REQUEST['pre_submit']) && $_REQUEST['pre_submit'] == '1') {
		echo '<script>';
		echo 'jQuery(document).ready(function() {';
		echo 'set_selected_server("'.tep_db_prepare_input($_REQUEST['pid']).'", "'.tep_db_prepare_input($_REQUEST['gcat']).'");';
		echo '});';
		echo '</script>';
	}
}
?>