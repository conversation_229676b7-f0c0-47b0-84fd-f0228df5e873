<h1><?=HEADER_TITLE_MY_ORDER_HISTORY?></h1>
<div class="solidThickLine"></div>
<?=tep_draw_form('buyback_order_form', tep_href_link(FILENAME_BUYBACK_ORDER_FORM).'?action=submit_form&bo_id='.$buyback_request_group_id, 'post')?>
<table border="0" width="100%" cellspacing="0" cellpadding="3" class="subCategoryBox">
	<tr>
		<td>
			<table border="0" width="100%" cellspacing="0" cellpadding="0" style="padding-left:0px;padding-right:0px;">
				<tr>
					<td class="infoBoxTitle">Order No.: #<?=$orders_id?></td>
					<td align="right" style="text-align:right;">Status: <span style="font-weight:bold;"><?=$orders_status?></span> | Order Date: <span style="font-weight:bold;"><?=$date_ordered?></span></td>
				</tr>
				<tr>
					<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
				</tr>
			</table>
		</td>
	</tr>
	<tr>
		<td>
			<table border="0" width="100%" cellspacing="0" cellpadding="0" style="background-color:#EFEFEF;padding-left:12px;padding-right:0px;">
				<tr><td class="infoBoxTitle" style="padding-top:18px;padding-left:10px;">REFERENCE NO. <?=implode(',', $ref_id_arr)?></td>
				<tr>
					<td class="mediumFont" style="color:#333333;padding-top:20px;padding-left:10px;font-weight:bold;">MAIN CHARACTER INFORMATION (REF. #<?=isset($main_char_info['products_ref_id']) ? $main_char_info['products_ref_id'] : '';?>)</td>
				</tr>
				<tr>
					<td>
						<table border="0" width="100%" cellspacing="0" cellpadding="0">
<?

foreach ($main_char_info['language_id'][1] as $attribute => $attr_value) {
	echo '<tr>
			<td width="150" nowrap style="padding:5px 0px;padding-left:10px;">'.$attribute.':</td>
			<td style="padding:5px 0px;font-weight:bold;">'.$attr_value.'</td>
		</tr>';
}

?>						<tr>
							<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '100%', '20')?></td>
						</tr>	
						</table>
					</td>
				</tr>
			</table>
		</td>
	</tr>
	<tr><td><div class="dottedLine"></div></td></tr>
<?
$error_msg = $messageStack->output($content);
if (tep_not_null($error_msg)) {
	echo '<tr><td>'.$error_msg.'</td></tr>';
}
?>	
	<tr><td><?=$template_html?></td></tr>
	<tr><td><div class="dottedLine"></div></td></tr>
	<tr>
		<td>
			<table border="0" align="right">
				<tr><td>
					<div class="green_button_fix_width">
					<a href="javascript:void(0);" onClick="pre_submit_hla_info(<?=$buyback_request_group_id?>);">
						<span>
							<font><?=BUTTON_SUBMIT?></font>
						</span>
					</a>
					</div>
					</td>
				</tr>
			</table>
		</td>
	</tr>
</table>
<script>
	function pre_submit_hla_info (bo_id) {
		jQuery.get('supplier_xmlhttp.php?action=verify_submit_hla_info&buyback_req_grp_id='+bo_id, function(xml) {
			jQuery(xml).find('response').each(function(){
				var result = jQuery("result", this).text();
				if (result == '1') {
					document.buyback_order_form.submit();
				} else {
					alert('Buyback Order has been canceled.');
					location.href = "<?=tep_href_link(FILENAME_MY_ORDER_HISTORY, tep_get_all_get_params(array('action', 'buyback_req_grp_id')) . 'odh_input_order_no='.$buyback_request_group_id.'&odh_input_product_type_select=hla_product&action=search')?>";
				}
			});
		});
	}
</script>
<style>
	.hla_form_entry {
		width:150px;		
	}
</style>