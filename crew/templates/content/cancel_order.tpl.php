<h1><?=TITLE_MY_PROFILE ?></h1>
<div class="solidThickLine"></div>
<div class="breakLine"><!-- --></div>

<?php if ($messageStack->size('order_cancel_request') > 0) { ?>
	<div class="loginColumnBorder" style="background-color: #FFFDDB">
		<div>
			<div class="breakLine"><!-- --></div>
			<div><?=$messageStack->output('order_cancel_request'); ?></div>
			<div class="breakLine"><!-- --></div>
		</div>
	</div>
	<div class="breakLine" style="line-height:10px;"><!-- --></div>
<?php } ?>

<div class="loginColumnBorder" style="width:100%;">
	<table border="0" width="100%" cellspacing="10" cellpadding="4">
  		<tr>
  			<td>
			<?=tep_draw_form('cancel_order_request_form', tep_href_link(FILENAME_CANCEL_ORDER, 'order_id='.$order_id, 'SSL'), 'post', '') ?>
			<?=tep_draw_hidden_field('action','process') ?>
	    		<div style="font-size:16px;font-weight:bold;"><?=HEADING_TITLE ?></div>
	    		<div class="breakLine" style="height:20px;"><!-- --></div>
	    		
				<table border="0" width="100%" cellspacing="0" cellpadding="2">
	          		<tr>
	          			<td style="width:28%;"><?=TITLE_ORDER_NUMBER ?></td>
	          			<td><?='<a href="'.tep_href_link(FILENAME_ACCOUNT_HISTORY_INFO, 'order_id='.$order_id, 'SSL').'">'.$order_id.'</a>' ?></td>
	          		</tr>
	          		<tr>
	          			<td style="width:28%;"><?=TITLE_PAYMENT_METHOD ?></td>
	          			<td><?=(sizeof($payment_methods_array) == 1 ? $payment_methods_array[0] : (sizeof($payment_methods_array) > 1 ? implode('</td></tr><tr><td>&nbsp;</td><td>', $payment_methods_array) : '')) ?></td>
	          		</tr>
	          		<tr>
	          			<td style="width:28%;"><?=TITLE_AMOUNT ?></td>
	          			<td><?=$order_total_value?></td>
	          		</tr>
	          		
	          		<tr><td colspan="2" style="height:20px;">&nbsp;</td></tr>
	          		<tr><td colspan="2"><?=MESSAGE_1 ?></td></tr>
	          		<tr><td colspan="2" style="height:20px;">&nbsp;</td></tr>
	          		<tr><td colspan="2"><?=MESSAGE_2 ?>&nbsp;</td></tr>
	          		<tr><td colspan="2" style="height:10px;">&nbsp;</td></tr>
	          		<tr>
	          			<td style="width:28%;"><?=TITLE_REASON_FOR_CANCELATION ?>&nbsp;</td>
						<td><?=tep_draw_pull_down_menu('cancel_reason', $cancel_reason_array, '', 'id="cancel_reason"') ?>&nbsp;</td>		          			
	          		</tr>
	          		<tr>
	          			<td style="width:28%;vertical-align:top;"><?=TITLE_ADDITIONAL_COMMENTS ?>&nbsp;</td>
						<td><?=tep_draw_textarea_field('cancel_feedback', 'soft', '70', '8', '', 'id="cancel_feedback"') ?></td>		          			
	          		</tr>
	  				<tr>
	  					<td colspan="2" style="height:50px;">
							<?=tep_div_button(1, '&nbsp; &nbsp; &nbsp; '. BUTTON_SUBMIT .' &nbsp; &nbsp; &nbsp;', 'cancel_order_request_form', 'style="float:right;"', 'green_button', false, 'if (check_form(\'cancel_order_request_form\')) {document.cancel_order_request_form.submit();}') ?>
						</td>
					</tr>
					<tr><td colspan="2" class="dottedLine" style="line-height:1px; padding:0px;">&nbsp;</td></tr>
					<tr><td colspan="2" style="height:20px;">&nbsp;</td></tr>
					<tr><td colspan="2"><?=MESSAGE_3 ?>&nbsp;</td></tr>
					<tr><td colspan="2"><?=sprintf(MESSAGE_4, $live_support_link) ?>&nbsp;</td></tr>
					<tr><td colspan="2" style="height:20px;">&nbsp;</td></tr>
					<tr>
						<td colspan="2">
							<div style="display: block;">
								<div style="float:left; width:50px; display: inline-block;">
									<div class="triangleRightIconBlue" style="margin: 3px 5px 0px 0px;"><!-- --></div>
									<div class="loginBoxLink"><a href="<?=tep_href_link(FILENAME_ACCOUNT_HISTORY_INFO, 'order_id='.$order_id, 'SSL')?>"><?=IMAGE_BUTTON_BACK?></a></div>
								</div>
							</div>
						</td>
					</tr>		
	  			</table>
			</form>
			</td>
		</tr>
	</table>
</div>


<script language="javascript">
function check_form(frm_obj) {
	var reason = document.getElementById('cancel_reason').options[document.getElementById('cancel_reason').selectedIndex];
	var check = false;
	
	if (reason.value.length > 0 && reason.value != '' && reason.value != 0) {
		check = true;
	}
	else {
		alert('<?=JS_ERROR_MESSAGE_1 ?>');
		document.getElementById('cancel_reason').focus();
	}
	
	return check;
}
</script>