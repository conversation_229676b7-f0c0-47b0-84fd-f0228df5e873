<link rel="stylesheet" href="<?=DIR_WS_IMAGES?>css/wow_stylesheet.php?path=<?=$wow_addon_path?>" type="text/css" media="Screen" />
<script language="javascript"><!--
function DOMCall(name) {
	if (document.getElementById)
    	return document.getElementById(name);
	else if (document.all)
		return document.all[name];
	else if (document.layers)
		return document.layers[name];
}	

function hidebag(id) {
	if (DOMCall(id+"_div_id") != null) {
		if (document.getElementById(id+"_div_id").className == "hide") {
			document.getElementById(id+"_div_id").className = "show";
		} else if (document.getElementById(id+"_div_id").className == "show") {
			document.getElementById(id+"_div_id").className = "hide";
		}
	}
}
//--></script>
<table border="0" width="100%" cellspacing="0" cellpadding="0">
	<tr>
		<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
	<tr>
		<td class="pageHeadingTitle" valign="middle" align="center"><?=HEADING_TITLE?></td>
	</tr>
	<tr>
		<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
	<tr>
		<td>
			<div class="row_separator"></div>
		</td>
	</tr>
	<tr>
		<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
	<tr>
		<td>
			<table border="0" width="100%" cellspacing="0" cellpadding="2" class="inputBox">
				<tr>
					<td align="center">
						<table border="0"  cellspacing="0" cellpadding="0" width="50%">
							<tr>
								<td align="center"><?='<a href="' . tep_href_link(FILENAME_CHARACTER_PROFILE, tep_get_all_get_params(array('action')) . 'action=char_info') . '">' . LINK_GEAR . '</a>'?></td>
								<td align="center"><?='<a href="' . tep_href_link(FILENAME_CHARACTER_PROFILE, tep_get_all_get_params(array('action')) . 'action=bag_bank') . '">' . LINK_BANK . '</a>'?></td>
								<td align="center"><?='<a href="' . tep_href_link(FILENAME_CHARACTER_PROFILE, tep_get_all_get_params(array('action')) . 'action=bag') . '">' . LINK_BAGS . '</a>'?></td>
							</tr>
						</table>
					</td>
				</tr>
				<tr>
					<td><?=tep_draw_separator('pixel_trans.gif', '100%', '20')?></td>
				</tr>
<?
$game_char_id = tep_db_prepare_input($_REQUEST['game_char_id']);

$char_info_select_sql = "	SELECT gc.*, gch.* 
							FROM " . TABLE_GAME_CHAR . " AS gc 
							INNER JOIN " . TABLE_GAME_CHAR_HISTORY . " AS gch 
								ON (gc.game_char_id = gch.game_char_id) 
							WHERE gc.orders_products_id = '" . (int)$orders_products_id_row['orders_products_id'] . "' 
							ORDER BY gch.game_char_history_date DESC LIMIT 1";
$char_info_result_sql = tep_db_query($char_info_select_sql);
$char_info_row = tep_db_fetch_array($char_info_result_sql);

$char_info = new char($char_info_row);
?>
				<tr>
					<td class="main" align="center"><?=TEXT_LAST_UPDATED . $char_info_row['game_char_history_date']?></td>
				</tr>
          		<tr class="inputBoxContents">
					<td align="center">
<?
if ($_REQUEST['action'] == 'bag_bank') {
?>
						<table border="0" cellspacing="2" cellpadding="0">
							<tr>
								<td valign="top" align="center">
<?
									$bank = bank_get($char_info_row['game_char_id'], "", $char_info_row["game_char_history_id"], "bank");
									
									if (isset($bank)) {
										$bank->out();
									}
?>
								</td>
							</tr>
							<tr>
								<td valign="top">
<?
									$default_bank_bag_slot = 6;
									$bank_bag_slot = bank_bag_num($char_info_row['game_char_id'], $char_info_row["game_char_history_id"]);
									
									if ($bank_bag_slot > $default_bank_bag_slot) {
										$default_bank_bag_slot = $bank_bag_slot;
									}
									
									for ($bank_bag_count = 1; $bank_bag_count <= $default_bank_bag_slot; $bank_bag_count++) {
										
										$bankbag = bag_get($char_info_row['game_char_id'], "", $char_info_row["game_char_history_id"], "bankBag".$bank_bag_count);
										
										if (isset($bankbag)) {
											$bankbag_div = "bankBag" . $bank_bag_count . "_div_id";
?>
											<div id="<?=$bankbag_div?>" class="hide"><?=$bankbag->out()?></div>
<?
										}
									}
?>
									</div>
								</td>
							</tr>
						</table>
<?
} else if ($_REQUEST['action'] == 'bag') {
?>
						<table border="0" cellspacing="2" cellpadding="0">
							<tr>
								<td valign="top">
<?
									$bag0 = bag_get($char_info_row['game_char_id'], "", $char_info_row["game_char_history_id"], "Bag0");
									if (isset($bag0)) {
										$bag0->out();
									}
?>
								</td>
								<td valign="top">
<?
									$bag1 = bag_get($char_info_row['game_char_id'], "", $char_info_row["game_char_history_id"], "Bag1");
									if (isset($bag1)) {
										$bag1->out();
									}
?>
								</td>
							</tr>
							<tr>
								<td valign="top">
<?
									$bag2 = bag_get($char_info_row['game_char_id'], "", $char_info_row["game_char_history_id"], "Bag2");
									if (isset($bag2)) {
										$bag2->out();
									}
?>
								</td>
								<td valign="top">
<?
									$bag3 = bag_get($char_info_row['game_char_id'], "", $char_info_row["game_char_history_id"], "Bag3");
									if (isset($bag3)) {
										$bag3->out();
									}
?>
								</td>
							</tr>
								<td valign="top">
<?
									$bag4 = bag_get($char_info_row['game_char_id'], "", $char_info_row["game_char_history_id"], "Bag4");
									if (isset($bag4)) {
										$bag4->out();
									}
?>
								</td>
							</tr>
						</table>
<?
} else {
?>
						<table border="0" cellspacing="2" cellpadding="0">
							<tr>
								<td valign="top">
<?
									if (isset($char_info)) {
										$char_info->out();
									}
?>
								</td>
							</tr>
						</table>
<?
}
?>
					</td>
          		</tr>
          		<tr>
					<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
				</tr>
				<tr>
					<td>
						<table border="0" width="100%" cellspacing="0" cellpadding="2">
              				<tr>
                				<td width="10"><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
                				<td><?=tep_image_button('back.gif', IMAGE_BUTTON_BACK, tep_href_link(FILENAME_ACCOUNT_HISTORY_INFO, 'order_id=' . $order_id, 'SSL'))?></td>
              				</tr>
            			</table>
					</td>
				</tr>
          	</table>
        </td>
    </tr>
</table>