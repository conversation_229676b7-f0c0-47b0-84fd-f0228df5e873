			<h1><?=HEADING_TITLE?></h1>
			<div class="solidThickLine"></div>
<table border="0" width="100%" cellspacing="0" cellpadding="0">
    <tr>
    	<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
	<tr>
		<td>
			 <table class="subCategoryBox" height="28px" border="0" width="100%" cellspacing="0" cellpadding="0">
				<tr>
					<td align="center" background="images/step_bg.gif" width="33%"><a href="<?=tep_href_link(FILENAME_CHECKOUT_PAYMENT, '', 'SSL')?>"><div class="systemCheckoutHeading">STEP 1</div></a></td>
					<td align="center" background="images/step_bg.gif" width="33%" class="systemCheckoutHeading"><font color="black">STEP 2</font></td>
					<td align="center" bgcolor="white" width="33%" class="systemCheckoutHeading"><font color="silver">STEP 3</font></td>
				</tr>
			</table>
		</td>
	</tr>
    <tr>
    	<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
    <tr>
    	<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
    <tr>
    	<td><?=tep_draw_separator('pixel_trans.gif', '100%', '5')?></td>
	</tr>
	<tr>
    	<td>
    		<table border="0" width="98%" height="30px" align="center" cellspacing="1" cellpadding="0">
          		<tr class="infoBoxContents">
<?
  	if ($sendto != false) {
?>
    	     		<td width="30%" valign="top">
    	     			<table border="0" width="100%" cellspacing="0" cellpadding="2">
        	      			<tr>
                				<td class="main"><? echo '<b>' . HEADING_DELIVERY_ADDRESS . '</b> <a href="' . tep_href_link(FILENAME_CHECKOUT_SHIPPING_ADDRESS, '', 'SSL') . '">(' . TEXT_EDIT . ')</a>'; ?></td>
              				</tr>
              				<tr>
                				<td class="main"><? echo tep_address_format($order->delivery['format_id'], $order->delivery, 1, ' ', '<br>'); ?></td>
              				</tr>
<?
    	if ($order->info['shipping_method']) {
?>
							<tr>
            			    	<td class="main"><? echo '<b>' . HEADING_SHIPPING_METHOD . '</b> <a href="' . tep_href_link(FILENAME_CHECKOUT_SHIPPING, '', 'SSL') . '">(' . TEXT_EDIT . ')</a>'; ?></td>
              				</tr>
              				<tr>
                				<td class="main"><?=$order->info['shipping_method']?></td>
              				</tr>
<?  	} ?>
						</table>
					</td>
<?	} 
?>
					<td width="<?php echo (($sendto != false) ? '70%' : '100%'); ?>" valign="top">
						<table border="0" width="100%" cellspacing="0" cellpadding="0">							
              				<tr>
                				<td>
                					<table border="0" width="100%" bgcolor="#f6f6f6" cellspacing="0" cellpadding="2">
<?
  	if (sizeof($order->info['tax_groups']) > 1) {
?>

										<tr>
                    						<td class="main" colspan="2"><? echo '<b>' . HEADING_PRODUCTS . '</b> <a href="' . tep_href_link(FILENAME_SHOPPING_CART) . '">(' . TEXT_EDIT . ')</a>'; ?></td>
                    						<td class="smallText" align="right"><b><?=HEADING_TAX?></b></td>
                    						<td class="smallText" align="right"><b><?=HEADING_TOTAL?></b></td>
                  						</tr>
<?  } else { ?>
										<tr height="28px"bgcolor="#88a5b4">
                    						<td class="main" colspan="2"><font color="white">&nbsp;&nbsp;&nbsp;<b><?=HEADING_PRODUCTS?></b></font></td>
											<td class="main" align="right"><font color="white"><b><?=HEADING_AMOUNT?></b>&nbsp;&nbsp;&nbsp;&nbsp;</font></td>
                  						</tr>
										<!--<tr>
                    						<td class="productBoxHeading" colspan="3"><? //echo '<b>' . HEADING_PRODUCTS . '</b> <a href="' . tep_href_link(FILENAME_SHOPPING_CART) . '">(' . TEXT_EDIT . ')</a>'; ?></td>
                  						</tr>-->
<?  }
	$cpt_id_array = array(	0 => '',
							1 => '',
							2 => '');
  	
  	for ($i=0, $n=sizeof($order->products); $i<$n; $i++) {
  		$product_instance_id = tep_not_null($order->products[$i]['custom_content']['hla_account_id']) ? $order->products[$i]['custom_content']['hla_account_id'] : '';
    	$currencies->product_instance_id = $product_instance_id;
    	
  		$cat_name_query = tep_db_query("select categories_id from " . TABLE_PRODUCTS_TO_CATEGORIES . " where products_id = " . $order->products[$i]['id'] . " and products_is_link=0 order by products_id");
	  	$cat_name = tep_db_fetch_array($cat_name_query);
      	$cat_path = tep_output_generated_category_path_sq($cat_name['categories_id'], 'category', false);
      	
		$cpt_id = $order->products[$i]['custom_products_type_id'];
		
		$cpt_id_array[$cpt_id] .= '          <tr>' . "\n" .
		     '            <td class="productOrderInfo" align="right" valign="top" width="20"><b>' . $order->products[$i]['qty'] . '&nbsp;x</b></td>' . "\n" .
		     '            <td class="productOrderInfo" valign="top"><b>' . $order->products[$i]['name']. '</b><br>['.$cat_path.']<br>';
		
		if ( isset($order->products[$i]['static']) && count($order->products[$i]['static']) ) {
			foreach($order->products[$i]['static'] as $get_char_array) {
				if ($get_char_array["qty"]) {
					$bundle_qty_select_sql = "	SELECT subproduct_qty
												FROM " . TABLE_PRODUCTS_BUNDLES . " 
												WHERE bundle_id='".$order->products[$i]["id"]."' AND subproduct_id='".$get_char_array["id"]."'";
					$bundle_qty_result_sql = tep_db_query($bundle_qty_select_sql);
					if ($bundle_qty = tep_db_fetch_array($bundle_qty_result_sql)) {
						$cpt_id_array[$cpt_id] .= "&raquo;&nbsp;<b>" . $order->products[$i]['qty'] .'&nbsp;x&nbsp;'. $bundle_qty["subproduct_qty"] . "&nbsp;" . $get_char_array["name"] ."</b><br>";
					}
				}
				
			}
		}
		
		if ( (isset($order->products[$i]['custom_content']['db_info']) && count($order->products[$i]['custom_content']['db_info'])) ) {
			foreach($order->products[$i]['custom_content']['db_info'] as $get_char_array) {
				if ($get_char_array["class"] == 'CHARACTER NAME') {
					$cpt_id_array[$cpt_id] .= TEXT_CHAR_NAME . $get_char_array["value"];
					//$cpt_id_array[$cpt_id] .= "&raquo;&nbsp;" . $get_char_array["qty"].'&nbsp;x&nbsp;' . $get_char_array["name"] ."<br>";
				}
			}
		} else {
	  		if (STOCK_ALLOW_CHECKOUT == true) { // item is barred from checkout when stock is too low
				if (isset($order->products[$i]["custom_content"][0]) && count($order->products[$i]["custom_content"][0])) {
					if ($order->products[$i]["custom_content"][0]["char_name"]) {
						$cpt_id_array[$cpt_id] .= TEXT_CHAR_NAME . $order->products[$i]["custom_content"][0]["char_name"];
					}
				}
	  		} else {
	  			if (isset($order->products[$i]['custom_content']['delivery_mode']) && $order->products[$i]['custom_content']['delivery_mode'] == '6') {
	  				
	  			} else {
					$stock_status = tep_check_stock_status($order->products[$i]['id'], $order->products[$i]['qty'], $product_instance_id);
	        		if (tep_not_null($stock_status) && $stock_status != "pre-order") {
			  			$any_out_of_stock = true;
					}
				}
				$cpt_id_array[$cpt_id] .= $stock;
			}
   		}
		
    	if ( (isset($order->products[$i]['attributes'])) && (sizeof($order->products[$i]['attributes']) > 0) ) {
      		for ($j=0, $n2=sizeof($order->products[$i]['attributes']); $j<$n2; $j++) {
        		$cpt_id_array[$cpt_id] .= '<br><nobr><small>&nbsp;<i> - ' . $order->products[$i]['attributes'][$j]['option'] . ': ' . $order->products[$i]['attributes'][$j]['value'] . '</i></small></nobr>';
      		}
    	}

	    $cpt_id_array[$cpt_id] .= '</td>' . "\n";

	    if (sizeof($order->info['tax_groups']) > 1) $cpt_id_array[$cpt_id] .= '            <td class="main" valign="top" align="right">' . tep_display_tax_value($order->products[$i]['tax']) . '%</td>' . "\n";
		
		if ($order->products[$i]['pm_price']=="FREE"){
		        	$cpt_id_array[$cpt_id] .= '            <td class="productOrderInfo" align="right" valign="top" nowrap><font color=red><s>' . $currencies->display_price_nodiscount($order->products[$i]['id'], $order->products[$i]['price'], $order->products[$i]['tax'], $order->products[$i]['qty']) . '</s></font>   '.$currencies->display_price_nodiscount($order->products[$i]['price'], $order->products[$i]['tax'], ($order->products[$i]['qty']-1)).'&nbsp;&nbsp;</td>' . "\n" .
		         		 '          </tr>' . "\n";
		    	} else {
		    		//CGDiscountSpecials start
		    		$cpt_id_array[$cpt_id] .= '            <td class="productOrderInfo" align="right" valign="top" nowrap>' . $currencies->display_price_nodiscount($order->products[$i]['id'], $order->products[$i]['final_price'], $order->products[$i]['tax'], $order->products[$i]['qty'], $order->products[$i]['discounts']) . '&nbsp;&nbsp;</td>' . "\n" .
		         		 '          </tr>' . "\n";
		    	}
  	}
	
	foreach ($cpt_id_array as $key => $value) {
		if ($key == 0 && $value != NULL) {
			echo "	<tr>
						<td></td>
					</tr>
					<tr>
						<td colspan='3' class='main'>&nbsp;&nbsp;&nbsp;<font color='#88a5b4'>" . tep_image('images/blue-arrow.gif', '', 3, 5) . ' ' . TEXT_CURRENCY . ' </font> ' . $value . "</td>
					</tr>";
			}
		if ($key == 1 && $value != NULL) {
			echo "	<tr>
						<td></td>
					</tr>
					<tr>
						<td colspan='3'>
							<div class='solidblue_row_separator'></div>
							<div class='solidgrey_row_separator'></div>
						</td>
					</tr>
					<tr>
						<td></td>
					</tr>
					<tr>
						<td colspan='3' class='main'>&nbsp;&nbsp;&nbsp;<font color='#88a5b4'>" . tep_image('images/blue-arrow.gif', '', 3, 5) . ' ' . TEXT_PWL . ' </font> ' . $value . "</td>
					</tr>";
			}
		if ($key == 2 && $value != NULL) {
			echo "	<tr>
						<td></td>
					</tr>
					<tr>
						<td colspan='3'>
							<div class='solidblue_row_separator'></div>
							<div class='solidgrey_row_separator'></div>
						</td>
					</tr>
					<tr>
						<td></td>
					</tr>
					<tr>
						<td colspan='3' class='main'>&nbsp;&nbsp;&nbsp;<font color='#88a5b4'>" . tep_image('images/blue-arrow.gif', '', 3, 5) . ' ' . TEXT_CDK . ' </font> ' . $value . "</td>
					</tr>";
		}
	}
?>
									</table>
                				</td>
              				</tr>
<?	
	if (MODULE_ORDER_TOTAL_INSTALLED) {
		$order_total_modules->process();
		$order_amount_breakdown = $order_total_modules->output(array('pm_name' => true));
		
		if ($full_store_credit) {
			$payment_method_str = '<br>' . $payment_method_str;
		}
		
		$order_amount_breakdown = str_replace('##CUSTOM_OT_PM_NAME##' , $payment_method_str, $order_amount_breakdown);
		
		echo '	<tr>
					<td>
						<table border="0" width="100%" align="center" cellspacing="4" cellpadding="2" bgcolor="#e9eff0">
							<tr>
								<td>' . $order_amount_breakdown . '</td>
							</tr>
						</table>
					</td>
				</tr>';
	}
?>
							<tr>
						    	<td><?=tep_draw_separator('pixel_trans.gif', '100%', '5')?></td>
						    </tr>
            			</table>
            		</td>
          		</tr>
			</table>
        </td>
	</tr>
    <tr>
    	<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
	<tr>
    	<td>
    		<table border="0" width="100%" cellspacing="0" cellpadding="0">
          		<tr>
					<td>&nbsp;</td>
					<td align="left" class="main">
						<a href="<?=tep_href_link(FILENAME_CHECKOUT_PAYMENT, '', 'SSL') . '" class="checkoutBarFrom">' . TEXT_BACK?></a> | <a href="<?=tep_href_link(FILENAME_SHOPPING_CART, '', 'SSL') . '" class="checkoutBarFrom">' . TEXT_EDIT_CART?></a>
					</td>
            		<td align="right" class="main">
					<?
					  	if (isset($$payment->form_action_url) && !$$payment->force_to_checkoutprocess) {
					    	$form_action_url = $$payment->form_action_url;
					  	} else {
					    	$form_action_url = tep_href_link(FILENAME_CHECKOUT_PROCESS, '', 'SSL');
					  	}
						
					  	echo tep_draw_form('checkout_confirmation', $form_action_url, 'post');
						
					  	if (is_array($payment_modules->modules) && !$full_store_credit) {
					    	echo $payment_modules->process_button();
					    	echo $payment_modules->draw_confirm_button();
					  	} else {
					  		echo tep_div_button(1, IMAGE_BUTTON_CONFIRM_ORDER,'checkout_confirmation', 'style="float:right"', 'red_button');
					  	}
					  	
						echo '</form>' . "\n";
					?>
					</td>
					<td>&nbsp;</td>
          		</tr>
        	</table>
        </td>
	</tr>
    <tr>
    	<td>
<?	if (isset($$payment->confirm_complete_days) && $$payment->confirm_complete_days > 0) echo TEXT_FRAUD_DISCLAIMER; ?>
		</td>
	</tr>
	<tr><td>&nbsp;</td></tr>
</table>
		<div class="break_line"></div>