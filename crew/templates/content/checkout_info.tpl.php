<?=tep_draw_form('checkout_info', tep_href_link(FILENAME_CHECKOUT_PAYMENT, 'action=info', 'SSL'), 'post', 'onsubmit="return info_check_form();"')?>
	<table border="0" width="100%" cellspacing="0" cellpadding="0">
    	<tr>
			<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
		</tr>
		<tr>
			<td class="pageHeadingTitle" valign="middle" align="center"><?=HEADING_TITLE?></td>
		</tr>
		<tr>
			<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
		</tr>
		<tr>
			<td>
				<div class="row_separator"></div>
			</td>
		</tr>
		<tr>
			<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
		</tr>
      	<tr>
        	<td>
        		<table border="0" width="100%" cellspacing="0" cellpadding="0">
          			<tr>
<?	if ($shipping != false) { ?>
          				<td width="25%">
            				<table border="0" width="100%" cellspacing="0" cellpadding="0">
              					<tr>
                					<td width="50%" align="right"><?=tep_draw_separator('pixel_silver.gif', '1', '5')?></td>
                					<td width="50%"><?=tep_draw_separator('pixel_silver.gif', '100%', '1')?></td>
              					</tr>
            				</table>
            			</td>
<?	} ?>
            			<td width="25%">
            				<table border="0" width="100%" cellspacing="0" cellpadding="0">
              					<tr>
<?	if ($shipping != false) {
		echo '						<td width="50%">' . tep_draw_separator('pixel_silver.gif', '100%', '1') . '</td>';
	} else {
		echo '						<td width="50%" align="right">' . tep_draw_separator('pixel_silver.gif', '1', '5') . '</td>';
	}
?>
                					<td><?=tep_image(THEMA_IMAGES . 'checkout_bullet.gif')?></td>
                					<td width="50%"><?=tep_draw_separator('pixel_silver.gif', '100%', '1')?></td>
              					</tr>
            				</table>
            			</td>
            			<td width="25%"><?=tep_draw_separator('pixel_silver.gif', '100%', '1')?></td>
            			<td width="25%"><?=tep_draw_separator('pixel_silver.gif', '100%', '1')?></td>
            			<td width="25%">
            				<table border="0" width="100%" cellspacing="0" cellpadding="0">
              					<tr>
                					<td width="50%"><?=tep_draw_separator('pixel_silver.gif', '100%', '1')?></td>
                					<td width="50%"><?=tep_draw_separator('pixel_silver.gif', '1', '5')?></td>
              					</tr>
            				</table>
            			</td>
          			</tr>
          			<tr>
<?	if ($shipping != false) { ?>
            			<td align="center" width="25%" class="checkoutBarFrom"><?='<a href="' . tep_href_link(FILENAME_CHECKOUT_SHIPPING, '', 'SSL') . '" class="checkoutBarFrom">' . CHECKOUT_BAR_DELIVERY . '</a>'?></td>
<?	} ?>
						<td align="center" width="25%" class="checkoutBarCurrent"><?=CHECKOUT_BAR_INFO?></td>
            			<td align="center" width="25%" class="checkoutBarTo"><?=CHECKOUT_BAR_PAYMENT?></td>
            			<td align="center" width="25%" class="checkoutBarTo"><?=CHECKOUT_BAR_CONFIRMATION?></td>
            			<td align="center" width="25%" class="checkoutBarTo"><?=CHECKOUT_BAR_FINISHED?></td>
          			</tr>
        		</table>
        	</td>
      	</tr>
      	<tr>
        	<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
      	</tr>
<?
if ($cart_comments_obj->count_active_comments(1)) {
?>
    <tr>
        <td>
        	<table border="0" width="100%" cellspacing="0" cellpadding="2">
          		<tr>
            		<td class="commentBoxHeading"><a name='c'></a><?=TABLE_HEADING_COMMENTS?></td>
          		</tr>
        	</table>
        </td>
	</tr>
    <tr>
    	<td>
    		<table border="0" width="100%" cellspacing="1" cellpadding="2" class="infoBox">
          		<tr class="infoBoxContents">
            		<td>
            			<?=$cart_comments_obj->display_comments_fields($custom_comments, 1)?>
            			<!--table border="0" width="100%" cellspacing="0" cellpadding="2">
              				<tr>
                				<td><?=tep_draw_textarea_field('comments', 'soft', '60', '5')?></td>
              				</tr>
            			</table-->
            		</td>
          		</tr>
        	</table>
        </td>
	</tr>
    <tr>
    	<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
    </tr>
<?
}
?>
	<tr>
    	<td><?=TEXT_GOLD_DELIVERY_NOTES?></td>
	</tr>
    <tr>
    	<td>
    		<table border="0" width="100%" cellspacing="1" cellpadding="2" class="buttonBox">
        		<tr class="buttonBoxContents">
	            	<td>
	            		<table border="0" width="100%" cellspacing="0" cellpadding="2">
	              			<tr>
	                			<td width="10"><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
	                			<td class="main"><b><?//echo TITLE_CONTINUE_CHECKOUT_PROCEDURE . '</b><br>' . TEXT_CONTINUE_CHECKOUT_PROCEDURE; ?></td>
	                			<td class="main" align="right">
									<?=tep_div_button(1, IMAGE_BUTTON_CONTINUE,'checkout_info', 'style="float:right"', 'gray_button') ?>
								</td>
	                			<td width="10"><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
	              			</tr>
	            		</table>
	            	</td>
	          	</tr>
        	</table>
        </td>
	</tr>
    <tr>
    	<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
</table>
<script language="javascript"><!--
	function Trim(strValue) {
		return LTrim(RTrim(strValue));
	}
	
	function LTrim(strValue) {
		var LTRIMrgExp = /^\s */;
		return strValue.replace(LTRIMrgExp, '');
	}
	
	function RTrim(strValue) {
		var RTRIMrgExp = /\s *$/;
		return strValue.replace(RTRIMrgExp, '');
	}
	
	function info_check_form() {
		if (typeof(comments_validation) == 'function') {
	  		var comment_check_result = comments_validation();
	  		
			if (comment_check_result != '') {
				alert(comment_check_result);
				return false;
			}
		}
		return true;
	}
//--></script>
</form>