<?

if (file_exists('checkout_xmlhttp.js.php')) {
    include_once ('checkout_xmlhttp.js.php');
}
?>
<link rel="stylesheet" href="images/css/cssmenu.css">
<style type="text/css">
    #cartbox_div {
        position: relative;
        width: 470px;
        float: left;
        z-index: 99;
    }
</style>
<script type="text/javascript" src="<?= DIR_WS_JAVASCRIPT ?>jquery-latest.pack.js"></script>
<script language="javascript" src="<?= DIR_WS_JAVASCRIPT ?>ogm_tooltips/ogm_tooltips.js"></script>
<link rel="stylesheet" href="<?= DIR_WS_JAVASCRIPT ?>ogm_tooltips/ogm_tooltips.css" type="text/css">
<script language="javascript">
    function payment_methods_display(pmt_id) {
        if (jQuery('#pmt_' + pmt_id).css('display') == 'none') {
            jQuery('#pmt_' + pmt_id).fadeIn('fast');
            jQuery('#sh_' + pmt_id).attr('class', 'boxHeaderCollapse');
        } else {
            jQuery('#pmt_' + pmt_id).fadeOut('fast');
            jQuery('#sh_' + pmt_id).attr('class', 'boxHeaderExpand');
        }
    }
</script>
<script language="javascript">
<!--
    if (navigator.appName.toUpperCase().match(/MICROSOFT INTERNET EXPLORER/) != null) {
        document.write('<iframe id="theLayerIframe" src="javascript:;" marginwidth="0" marginheight="0" align="bottom" scrolling="no" frameborder="0" style="position:absolute; width:450px; left: 439; top: 351; visibility: hidden; display: block; filter: alpha(opacity=0);"></iframe>');
    }
//-->
</script>
<table cellpadding="7" cellspacing="0" border="0" width="100%" height="618px">
    <?
    if (isset($_REQUEST['error_message']) && tep_not_null($_REQUEST['error_message'])) {
        $messageStack->add('checkout_payment', htmlspecialchars($_REQUEST['error_message']));
    }
	
    if ($messageStack->size('checkout_payment') > 0) {
        ?>
        <tr>
            <td><?= $messageStack->output('checkout_payment') ?></td>
        </tr>
        <?
    }
    ?>
    <tr>
        <td valign="top">
            <div style="float:left; width:473px">
                <div class="loginColumnBorder" id="body_content_div">
                    <?
                    echo tep_draw_form('payment_methods_form', tep_href_link(FILENAME_CHECKOUT_PAYMENT, '', 'SSL'), 'post');
                    $_SESSION['order_total_amt_wo_surcharge'] = '';

                    if ($payment_modules->check_credit_covers()) { // Fully covered by Credits
                        $check_credit_covers_html = '	<table border="0" cellspacing="0" cellpadding="0" width="100%">
										<tr height="500px">
											<td valign="top" style="padding:5px;">' . TEXT_ORDER_FULLY_COVERED_BY_CREDITS . '</td>
										</tr>
									</table>';
                        echo $page_obj->get_html_simple_rc_box('', $check_credit_covers_html . '<div style="clear:both"></div>', 13);
                    } else if ($order->info['subtotal'] <= 0) {
                        $order_zero_amount_checkout_html = '<table border="0" cellspacing="0" cellpadding="0" width="100%">
											<tr height="500px">
												<td valign="top" style="padding:5px;">' . TEXT_ORDER_ZERO_AMOUNT_CHECKOUT . '</td>
											</tr>
										</table>';
                        echo $page_obj->get_html_simple_rc_box('', $order_zero_amount_checkout_html . '<div style="clear:both"></div>', 13);
                    } else {
                        $selection = $payment_modules->selection($currency, $payment);
                        $sorted_selection = $payment_modules->sort_selection($selection);

                        $default_selected = '';
                        $radio_buttons = 0;
                        $payment_gateway_category_show = false;
                        $payment_list_arr = array();
                        $order_total_reprocess = false;

                        // manually simulate the order total calculation to get the total amount for surcharge calculation
                        // this is to fix the surcharge amount calculation for all payment methods in checkout page,
                        // since payments methods is loaded and listed before displayaing the shopping cart.

                        if (isset($order_total_modules) && is_object($order_total_modules)) {
                            $_SESSION['order_total_amt_wo_surcharge'] = $order_total_modules->get_order_total_pre_surcharge();
                        }

                        if (class_exists('ot_surcharge')) {
                            $ot_surcharge = new ot_surcharge();
                        }

                        // use payment_methods_types_sites to hide offline for SC checkout
                        $payment_methods_types_select_sql = "	SELECT pmt.payment_methods_types_id, pmt.payment_methods_types_name, pmtd.payment_methods_types_description
  											FROM " . TABLE_PAYMENT_METHODS_TYPES . " AS pmt
  											INNER JOIN " . TABLE_PAYMENT_METHODS_TYPES_DESCRIPTION . " AS pmtd
  												ON (pmt.payment_methods_types_id = pmtd.payment_methods_types_id AND pmtd.languages_id = '" . tep_db_input($languages_id) . "')
  											WHERE payment_methods_types_mode = 'RECEIVE'
  												AND FIND_IN_SET('MAIN', pmt.payment_methods_types_sites)";
                        $payment_methods_types_result_sql = tep_db_query($payment_methods_types_select_sql);
                        if (tep_db_num_rows($payment_methods_types_result_sql) < 1) {
                            $payment_methods_types_select_sql = "	SELECT pmt.payment_methods_types_id, pmt.payment_methods_types_name, pmtd.payment_methods_types_description
	  											FROM " . TABLE_PAYMENT_METHODS_TYPES . " AS pmt
	  											INNER JOIN " . TABLE_PAYMENT_METHODS_TYPES_DESCRIPTION . " AS pmtd
	  												ON (pmt.payment_methods_types_id = pmtd.payment_methods_types_id AND pmtd.languages_id = '" . tep_db_input($default_languages_id) . "')
	  											WHERE payment_methods_types_mode = 'RECEIVE'
  												AND FIND_IN_SET('MAIN', pmt.payment_methods_types_sites)";
                            $payment_methods_types_result_sql = tep_db_query($payment_methods_types_select_sql);
                        }
                        
                        if(isset($sorted_selection[0])){
                            $payment_list_arr[0]['show'] = 0;
                            $payment_list_arr[0]['description'] = TEXT_FEATURED_PAYMENT_METHODS;
                            $payment_list_arr[0]['type'] = 'payment'; 
                            if(tep_not_null($payment)){
                                $payment_array = explode(':~:', $payment);
                                if (sizeof($payment_array) > 1) {
                                    if($payment_array[0]=='pm_0'){
                                        $payment_list_arr[0]['show'] = 1;
                                        $payment_gateway_category_show = true;
                                    }
                                } 
                            }
                        }
                        while ($payment_methods_types_row = tep_db_fetch_array($payment_methods_types_result_sql)) {
                            if (isset($selection[$payment_methods_types_row['payment_methods_types_id']])) {
                                $payment_list_arr[$payment_methods_types_row['payment_methods_types_id']]['show'] = 0;
                                $payment_list_arr[$payment_methods_types_row['payment_methods_types_id']]['description'] = $payment_methods_types_row['payment_methods_types_description'];
                                $payment_list_arr[$payment_methods_types_row['payment_methods_types_id']]['type'] = 'payment';

                                if (tep_not_null($payment) && $payment_gateway_category_show!=true) {
                                    if (isset($selection[$payment_methods_types_row['payment_methods_types_id']]['show'])) {
                                        $payment_list_arr[$payment_methods_types_row['payment_methods_types_id']]['show'] = 1;
                                        $payment_gateway_category_show = true;
                                    }
                                }
                            }
                        }
                        if(!$payment_gateway_category_show) {
                            if(isset($payment_list_arr[0])){
                                $payment_list_arr[0]['show'] = 1;
                                $payment_gateway_category_show = true;
                            }
                        }
                        
//	// GST : disable currency drop down when GST condition valid
//	if (!tep_not_null($_SESSION['RegionGST']['tax_title'])) {
//		$payment_list_arr[] = array('show' => 1,
//									'description' => 'MORE PAYMENT METHODS',
//									'type' => 'currency'
//									);
//	}

                        $payment_gateway_array = array();
                        $displayed_payment_methods_array = array();
                        $show_paypal_flag = true;
                        $count_payment_methds = 0;
                        $selected_payment_flag = 0; // 0 = none, 1 = default currency, 2 = USD (others currency)
                        foreach ($payment_list_arr as $gw_id => $payment_cat_display) {
                            $pg_class = '';
                            $pgt_class_name = 'boxHeaderCollapse';

                            if ($payment_gateway_category_show) {
                                if (tep_not_null($payment)) {
                                    $pg_class = 'class="hide"';
                                    $pgt_class_name = 'boxHeaderExpand';

                                    if ($payment_cat_display['show'] == 1) {
                                        $pg_class = '';
                                        $pgt_class_name = 'boxHeaderCollapse';
                                    }
                                }
                            }

                            if (isset($payment_cat_display['type']) && $payment_cat_display['type'] == 'payment') {
                                $payment_methods_array = array();
                                foreach ($sorted_selection[$gw_id] as $pm_id => $pm_info_array) {
                                    $pg_key = $pm_info_array['pg_key'];

                                    $pm_html = '';
                                    if ($pm_info_array['payment_gateway_code'] == 'paypal' || $pm_info_array['payment_gateway_code'] == 'paypalEC') {
                                        $show_paypal_flag = false;
                                    }
                                    $supported_currencies_array = array();
                                    $unique_name = $gw_id . ':~:' . $pm_info_array['payment_gateway_code'] . ':~:' . $pm_id;
                                    $payment_title = $pm_info_array['payment_methods_description_title'];
                                    $pro_img = $pm_info_array['payment_methods_logo'];

                                    $pro_img_w = 0;
                                    $pro_img_h = 0;

                                    for ($curr_cnt = 0; $curr_cnt < sizeof($pm_info_array['currency']); $curr_cnt++) {
                                        $supported_currencies_array[] = array('id' => $pm_info_array['currency'][$curr_cnt], 'text' => $pm_info_array['currency'][$curr_cnt]);
                                    }

                                    if (is_object($ot_surcharge)) {
                                        $surcharge_amt = $ot_surcharge->calculate_surcharge_by($unique_name, $currency, 4);
                                        $surcharge_html = " <div id='surcharge_" . $radio_buttons . "'><table><tr><td>" . ($surcharge_amt > 0 ? "(+ " . $currencies->format($surcharge_amt, false, $currency_code) . " " . ENTRY_PAYMENT_SURCHARGE . ")" : "") . "</td></tr></table></div>";
                                    } else {
                                        $surcharge_html = "	<div id='surcharge_" . $radio_buttons . "'><table><tr><td></td></tr></table></div>";
                                    }

                                    $payment_title = $pm_info_array['payment_methods_description_title'] . $surcharge_html;

                                    if ($pm_info_array['payment_methods_receive_status_mode'] == 1) {

                                        $count_payment_methds++;

                                        $radio_para = 'onclick="updateCurrency(' . $radio_buttons . ', \'change\');"';

                                        if (tep_not_null($payment) && $payment == 'pm_' . $unique_name) {
                                            $pm_html .= '<div class="moduleRowSelected" id="defaultSelected" onmouseover="rowOverEffect(this);" onmouseout="rowOutEffect(this);" onclick="updateCurrency(' . $radio_buttons . ', \'\'); selectRowEffect(this, ' . $radio_buttons . ');" valign="top">';
                                            $default_selected = $radio_buttons;
                                            $selected_payment_flag = 1;
                                        } else {
                                            $pm_html .= '<div class="moduleRow" id="row_pm_' . $radio_buttons . '" onmouseover="rowOverEffect(this);" onmouseout="rowOutEffect(this);" onclick="updateCurrency(' . $radio_buttons . ', \'\'); selectRowEffect(this, ' . $radio_buttons . ');" valign="top">';
                                        }
                                    } else {
                                        $radio_para = 'disabled';
                                        $payment_title .= '<br>' . $pm_info_array['payment_methods_status_message'];

                                        $pm_html .= '	<div class="moduleRow" id="row_pm_' . $radio_buttons . '" valign="top">';
                                    }

                                    $pm_html .= '<table border="0" width="98%" cellspacing="0" cellpadding="0">
												<tr>
							    					<td colspan="5">' . tep_draw_separator('pixel_trans.gif', '100%', '8') . '</td>
												</tr>
												<tr>
													<td width="3%"></td>
													<td width="5%">' . ($pg_key != '99999' ? tep_draw_radio_field('payment', 'pm_' . $unique_name, false, ' id="pm_' . $radio_buttons . '" ' . $radio_para) : '') . '</td>';

                                    if ($pg_key != '99999') {
                                        $aws_obj = new ogm_amazon_ws();
                                        $aws_obj->set_bucket_key('BUCKET_STATIC');
                                        $aws_obj->set_filepath('images/payment/');
                                        $pm_logo_path = '';

                                        $image_info_array = $aws_obj->get_image_info($pro_img);

                                        if (tep_not_null($image_info_array)) {
                                            $pm_logo_path = $image_info_array['src'];
                                        } else if (tep_not_null($pro_img) && file_exists(DIR_FS_IMAGES . 'payment/' . $pro_img)) {
                                            $pm_logo_path = DIR_WS_IMAGES . 'payment/' . $pro_img;
                                        }

                                        if (tep_not_null($pm_logo_path))
                                            list($pro_img_w, $pro_img_h) = getimagesize($pm_logo_path);

                                        $pm_html .= "<td width='12%'>" . ($pro_img_w > 0 ? tep_image($pm_logo_path, '', $pro_img_w, $pro_img_h) : '&nbsp;') . "</td>";

                                        unset($aws_obj);
                                    }

                                    $pm_html .= '		<td class="main" style="padding-left:12px;">' . $payment_title . '</td>
												<td width="10%" align="right">' .
                                            //tep_draw_pull_down_menu('cr_' . $radio_buttons, $supported_currencies_array, $_SESSION['currency'], ' disabled onchange="updateCurrency(' . $radio_buttons . ', \'change\');" id="cr_' . $radio_buttons . '"') .
                                            '</td>
											</tr>';

                                    //if ($pm_info_array['show_billing_address'] == 'True' || $pm_info_array['show_contact_number'] == 'True' || $pm_info_array['show_ic'] == 'True') {  // currenct for mobile money only
                                    $pm_html .= '<tr>
											<td width="8%" colspan="2"></td>
											<td colspan="3">
												<div class="' . ($payment == 'pm_' . $unique_name ? '' : 'hide') . ' system_cust_pm_info" id="pm_cust_info_' . $radio_buttons . '">
													<table border="0" width="100%" cellspacing="0" cellpadding="0">';

                                    $display_confirm_link = false;
                                    if ($pm_info_array['show_billing_address'] == 'True') {
                                        $display_confirm_link = true;
                                        $pm_html .= '		<tr>
															<td colspan="2">' . tep_draw_separator('pixel_trans.gif', '100%', '5') . '</td>
														</tr>
														<tr>
															<td colspan="2">' .
                                                tep_address_label($customer_id, $billto, true, ' ', '<br>') . '<br>' .
                                                '<a href="' . tep_href_link(FILENAME_ACCOUNT_EDIT, 'trace=chkout', 'SSL') . '" id="edit_address"><b>' . TEXT_EDIT_ADDRESS . '</b></a>
															</td>
														</tr>';
                                    }

                                    if ($pm_info_array['show_contact_number'] == 'True' || $pm_info_array['show_surname'] == 'True' || $pm_info_array['show_city'] == 'True') {
                                        $account_sql = "SELECT customers_telephone, customers_default_address_id, customers_lastname
													FROM " . TABLE_CUSTOMERS . "
													WHERE customers_id ='" . (int) $customer_id . "'";
                                        $account_result_sql = tep_db_query($account_sql);
                                        $account = tep_db_fetch_array($account_result_sql);
                                    }

                                    if ($pm_info_array['show_city'] == 'True' || $pm_info_array['show_street'] == 'True' || $pm_info_array['show_zip'] == 'True') {
                                        $customers_info_select_sql = "	SELECT entry_street_address, entry_postcode, entry_city
																	FROM " . TABLE_ADDRESS_BOOK . "
																	WHERE address_book_id = '" . (int) $account['customers_default_address_id'] . "'
																		AND customers_id ='" . (int) $customer_id . "'";
                                        $customers_info_result_sql = tep_db_query($customers_info_select_sql);
                                        $customers_info_row = tep_db_fetch_array($customers_info_result_sql);
                                    }

                                    if ($pm_info_array['show_contact_number'] == 'True') {
                                        $display_confirm_link = true;
                                        $customer_complete_phone_info_array = tep_format_telephone($customer_id);
                                        if (!isset($_SESSION['customers_telephone'])) {
                                            $_SESSION['customers_telephone'] = $account['customers_telephone'];
                                        }
                                        $pm_html .= '		<tr>
															<td colspan="2">' . tep_draw_separator('pixel_trans.gif', '100%', '5') . '</td>
														</tr>
														<tr valign="top">
															<td width="15%">' . ENTRY_PAYMENT_CONTACT_NUMBER . '</td>
															<td>+' . $customer_complete_phone_info_array['country_international_dialing_code'] . ' ' . tep_draw_input_field('telephone_number', tep_mask_telephone($_SESSION['customers_telephone']), ' id="telephone_number" ') . '</td>
														</tr>';
                                    }
                                    if ($pm_info_array['show_ic'] == 'True') {
                                        $display_confirm_link = true;
                                        $pm_html .= '		<tr>
															<td colspan="2">' . tep_draw_separator('pixel_trans.gif', '100%', '5') . '</td>
														</tr>
														<tr valign="top">
															<td nowrap width="15%">' . ENTRY_PAYMENT_CUSTOMER_IDENTIFY_NUMBER . '</td>
															<td>' . tep_draw_input_field('identify_number', (isset($_SESSION['identify_number']) ? $_SESSION['identify_number'] : ''), ' id="identify_number" ') . '<br>' . TEXT_INFO_IDENTIFY_NUMBER_EXAMPLE . '</td>
														</tr>';
                                    }

                                    if ($pm_info_array['show_surname'] == 'True') {
                                        $display_confirm_link = true;
                                        if (!isset($_SESSION['checkout_surname'])) {
                                            $_SESSION['checkout_surname'] = $account['customers_lastname'];
                                        }

                                        $pm_html .= '		<tr>
															<td colspan="2">' . tep_draw_separator('pixel_trans.gif', '100%', '5') . '</td>
														</tr>
														<tr valign="top">
															<td nowrap width="15%">' . ENTRY_PAYMENT_CUSTOMER_SURNAME . '</td>
															<td>' . tep_draw_input_field('checkout_surname', $_SESSION['checkout_surname'], ' id="checkout_surname" ') . '</td>
														</tr>';
                                    }

                                    if ($pm_info_array['show_housenumber'] == 'True') {
                                        $display_confirm_link = true;

                                        if (!isset($_SESSION['checkout_housenumber'])) {
                                            $_SESSION['checkout_housenumber'] = '';
                                        }

                                        $pm_html .= '		<tr>
															<td colspan="2">' . tep_draw_separator('pixel_trans.gif', '100%', '5') . '</td>
														</tr>
														<tr valign="top">
															<td nowrap width="15%">' . ENTRY_PAYMENT_CUSTOMER_HOUSENUMBER . '</td>
															<td>' . tep_draw_input_field('checkout_housenumber', $_SESSION['checkout_housenumber'], ' id="checkout_housenumber" ') . '</td>
														</tr>';
                                    }

                                    if ($pm_info_array['show_street'] == 'True') {
                                        $display_confirm_link = true;

                                        if (!isset($_SESSION['checkout_street'])) {
                                            $_SESSION['checkout_street'] = $customers_info_row['entry_street_address'];
                                        }

                                        $pm_html .= '		<tr>
															<td colspan="2">' . tep_draw_separator('pixel_trans.gif', '100%', '5') . '</td>
														</tr>
														<tr valign="top">
															<td nowrap width="15%">' . ENTRY_PAYMENT_CUSTOMER_STREET . '</td>
															<td>' . tep_draw_input_field('checkout_street', $_SESSION['checkout_street'], ' id="checkout_street" ') . '</td>
														</tr>';
                                    }

                                    if ($pm_info_array['show_city'] == 'True') {
                                        $display_confirm_link = true;

                                        if (!isset($_SESSION['checkout_city'])) {
                                            $_SESSION['checkout_city'] = $customers_info_row['entry_city'];
                                        }

                                        $pm_html .= '		<tr>
															<td colspan="2">' . tep_draw_separator('pixel_trans.gif', '100%', '5') . '</td>
														</tr>
														<tr valign="top">
															<td nowrap width="15%">' . ENTRY_PAYMENT_CUSTOMER_CITY . '</td>
															<td>' . tep_draw_input_field('checkout_city', $_SESSION['checkout_city'], ' id="checkout_city" ') . '</td>
														</tr>';
                                    }

                                    if ($pm_info_array['show_zip'] == 'True') {
                                        $display_confirm_link = true;

                                        if (!isset($_SESSION['checkout_zip'])) {
                                            $_SESSION['checkout_zip'] = $customers_info_row['entry_postcode'];
                                        }

                                        $pm_html .= '		<tr>
															<td colspan="2">' . tep_draw_separator('pixel_trans.gif', '100%', '5') . '</td>
														</tr>
														<tr valign="top" width="15%">
															<td nowrap>' . ENTRY_PAYMENT_CUSTOMER_ZIP . '</td>
															<td>' . tep_draw_input_field('checkout_zip', $_SESSION['checkout_zip'], ' id="checkout_zip" ') . '</td>
														</tr>';
                                    }

                                    if ($pm_info_array['show_accountname'] == 'True') {
                                        $display_confirm_link = true;

                                        if (!isset($_SESSION['checkout_accountname'])) {
                                            $_SESSION['checkout_accountname'] = '';
                                        }

                                        $pm_html .= '		<tr>
															<td colspan="2">' . tep_draw_separator('pixel_trans.gif', '100%', '5') . '</td>
														</tr>
														<tr valign="top" width="15%">
															<td nowrap>' . ENTRY_PAYMENT_CUSTOMER_ACCOUNTNAME . '</td>
															<td>' . tep_draw_input_field('checkout_accountname', $_SESSION['checkout_accountname'], ' id="checkout_accountname" ') . '</td>
														</tr>';
                                    }

                                    if ($pm_info_array['show_accountnumber'] == 'True') {
                                        $display_confirm_link = true;

                                        if (!isset($_SESSION['checkout_accountnumber'])) {
                                            $_SESSION['checkout_accountnumber'] = '';
                                        }

                                        $pm_html .= '		<tr>
															<td colspan="2">' . tep_draw_separator('pixel_trans.gif', '100%', '5') . '</td>
														</tr>
														<tr valign="top" width="15%">
															<td nowrap>' . ENTRY_PAYMENT_CUSTOMER_ACCOUNTNUMBER . '</td>
															<td>' . tep_draw_input_field('checkout_accountnumber', $_SESSION['checkout_accountnumber'], ' id="checkout_accountnumber" ') . '</td>
														</tr>';
                                    }

                                    if ($pm_info_array['show_account_number'] == 'True') {
                                        $display_confirm_link = true;

                                        if (!isset($_SESSION['checkout_account_number'])) {
                                            $_SESSION['checkout_account_number'] = '';
                                        }

                                        $pm_html .= '		<tr>
															<td colspan="2">' . tep_draw_separator('pixel_trans.gif', '100%', '5') . '</td>
														</tr>
														<tr valign="top" width="15%">
															<td nowrap>' . ENTRY_PAYMENT_CUSTOMER_ACCOUNT_NUMBER . '</td>
															<td>' . tep_draw_input_field('checkout_zip', $_SESSION['checkout_account_number'], ' id="checkout_account_number" ') . '</td>
														</tr>';
                                    }

                                    if ($pm_info_array['show_bankcode'] == 'True') {
                                        $display_confirm_link = true;

                                        if (!isset($_SESSION['checkout_bankcode'])) {
                                            $_SESSION['checkout_bankcode'] = '';
                                        }

                                        $pm_html .= '		<tr>
															<td colspan="2">' . tep_draw_separator('pixel_trans.gif', '100%', '5') . '</td>
														</tr>
														<tr valign="top" width="15%">
															<td nowrap>' . ENTRY_PAYMENT_CUSTOMER_BANKCODE . '</td>
															<td>' . tep_draw_input_field('checkout_bankcode', $_SESSION['checkout_bankcode'], ' id="checkout_bankcode" ') . '</td>
														</tr>';
                                    }

                                    if ($pm_info_array['show_branchcode'] == 'True') {
                                        $display_confirm_link = true;

                                        if (!isset($_SESSION['checkout_branchcode'])) {
                                            $_SESSION['checkout_branchcode'] = '';
                                        }

                                        $pm_html .= '		<tr>
															<td colspan="2">' . tep_draw_separator('pixel_trans.gif', '100%', '5') . '</td>
														</tr>
														<tr valign="top" width="15%">
															<td nowrap>' . ENTRY_PAYMENT_CUSTOMER_BRANCHCODE . '</td>
															<td>' . tep_draw_input_field('checkout_branchcode', $_SESSION['checkout_branchcode'], ' id="checkout_branchcode" ') . '</td>
														</tr>';
                                    }

                                    if ($pm_info_array['show_directdebittext'] == 'True') {
                                        $display_confirm_link = true;

                                        if (!isset($_SESSION['checkout_directdebittext'])) {
                                            $_SESSION['checkout_directdebittext'] = '';
                                        }

                                        $pm_html .= '		<tr>
															<td colspan="2">' . tep_draw_separator('pixel_trans.gif', '100%', '5') . '</td>
														</tr>
														<tr valign="top" width="15%">
															<td nowrap>' . ENTRY_PAYMENT_CUSTOMER_DIRECTDEBITTEXT . '</td>
															<td>' . tep_draw_input_field('checkout_directdebittext', $_SESSION['checkout_directdebittext'], ' id="checkout_directdebittext" ') . '</td>
														</tr>';
                                    }

                                    if ($pm_info_array['show_vouchernumber'] == 'True') {
                                        $display_confirm_link = true;

                                        if (!isset($_SESSION['show_voucher_number'])) {
                                            $_SESSION['show_voucher_number'] = '';
                                        }

                                        $pm_html .= '		<tr>
															<td colspan="2">' . tep_draw_separator('pixel_trans.gif', '100%', '5') . '</td>
														</tr>
														<tr valign="top" width="15%">
															<td nowrap>' . ENTRY_PAYMENT_CUSTOMER_VOUCHER_NUMBER . '</td>
															<td>' . tep_draw_input_field('checkout_voucher_number', $_SESSION['show_voucher_number'], ' id="checkout_voucher_number" ') . '</td>
														</tr>';
                                    }

                                    if ($pm_info_array['show_vouchervalue'] == 'True') {
                                        $display_confirm_link = true;

                                        if (!isset($_SESSION['checkout_voucher_value'])) {
                                            $_SESSION['checkout_voucher_value'] = '';
                                        }

                                        $pm_html .= '		<tr>
															<td colspan="2">' . tep_draw_separator('pixel_trans.gif', '100%', '5') . '</td>
														</tr>
														<tr valign="top" width="15%">
															<td nowrap>' . ENTRY_PAYMENT_CUSTOMER_VOUCHER_VALUE . '</td>
															<td>' . tep_draw_input_field('checkout_voucher_value', $_SESSION['checkout_voucher_value'], ' id="checkout_voucher_value" ') . '</td>
														</tr>';
                                    }

                                    if ($display_confirm_link) {
                                        $pm_html .= '		<tr>
															<td colspan="2"><div><a href="javascript:update_pm_cust_info(\'pm_cust_info_' . $radio_buttons . '\')"><b>' . LINK_UPDATE_CUSTOMER_PAYMENT_INFO . '</b></a></div></td>
														</tr>';
                                    }
                                    $pm_html .= '		</table>
													</div>
												</td>
											</tr>';
                                    //}
                                    $pm_html .= '	<tr>
						    					<td colspan="5">' . tep_draw_separator('pixel_trans.gif', '100%', '8') . '</td>
											</tr>
										</table>
									</div>';

                                    $displayed_payment_methods_array[] = $pm_id;
                                    $payment_methods_array[] = array('id' => 'vmenu_pm_' . $pm_id, 'desc' => $pm_html, 'href' => '');
                                    $radio_buttons++;
                                }
                            }

                            $payment_gateway_array[] = array('id' => 'vmenu_pg_' . $gw_id,
                                'desc' => $payment_cat_display['description'],
                                'href' => 'javascript:void(0);',
                                'slist' => $payment_methods_array,
                                'default_open' => $payment_cat_display['show'],
                            );
                        }

                        echo $page_obj->get_html_expandable_vmenu_box(sprintf(HEADING_SUBTITLE, $currencies->currencies[$currency]['title'] . ' (' . $currencies->currencies[$currency]['symbol_left'] . $currencies->currencies[$currency]['symbol_right'] . ')'), $payment_gateway_array, 12, '', 12);

                        if ((!isset($_SESSION['RegionGST']['tax_title']) || !tep_not_null($_SESSION['RegionGST']['tax_title'])) && ($count_payment_methds < 5 || $show_paypal_flag === true || $currency == 'BRL' || $currency == 'JPY' || $currency == 'HKD' || $currency == 'MXN') && $currency != DEFAULT_CURRENCY) {
                            $selection = array();
                            $selection = $payment_modules->selection(DEFAULT_CURRENCY, $payment);
                            $sorted_selection = $payment_modules->sort_selection($selection);
                            $payment_list_arr = array();
                            $payment_gateway_category_show = false;


                            $temp_currency = $currency;
                            $currency = $_SESSION['currency'] = DEFAULT_CURRENCY;

                            // temp store
                            $temp_order_obj = $order;
                            $order = new order;

                            if (class_exists('ot_surcharge')) {
                                $ot_usd_surcharge = new ot_surcharge(/* DEFAULT_CURRENCY */);
                            }

                            $payment_methods_types_result_sql = tep_db_query($payment_methods_types_select_sql);
                            while ($payment_methods_types_row = tep_db_fetch_array($payment_methods_types_result_sql)) {
                                $show_after_filter_flag = false;

                                if (count($selection[$payment_methods_types_row['payment_methods_types_id']])) {
                                    foreach ($selection[$payment_methods_types_row['payment_methods_types_id']] as $pg_id_loop => $pm_data_loop) {
                                        if (is_int($pg_id_loop)) {
                                            foreach ($pm_data_loop as $pm_id_loop => $pm_data_loop) {
                                                if (!in_array($pm_id_loop, $displayed_payment_methods_array)) {
                                                    $show_after_filter_flag = true;
                                                    break 2;
                                                }
                                            }
                                        }
                                    }
                                }

                                if ($show_after_filter_flag && isset($selection[$payment_methods_types_row['payment_methods_types_id']]) || $payment_methods_types_row['payment_methods_types_id'] == 7) {
                                    $payment_list_arr[$payment_methods_types_row['payment_methods_types_id']]['show'] = 0;
                                    $payment_list_arr[$payment_methods_types_row['payment_methods_types_id']]['description'] = $payment_methods_types_row['payment_methods_types_description'];
                                    $payment_list_arr[$payment_methods_types_row['payment_methods_types_id']]['type'] = 'payment';

                                    /* 				if ($payment_methods_types_row['payment_methods_types_id'] == 7) {
                                      $selection[$payment_methods_types_row['payment_methods_types_id']][99999][]['payment_methods_description_title'] = TEXT_INFO_OFFLINE_REPLACEMENT_BY_SC;
                                      $payment_list_arr[$payment_methods_types_row['payment_methods_types_id']]['show'] = 1;
                                      $payment_gateway_category_show = true;
                                      } */

                                    if (tep_not_null($payment) && $selected_payment_flag == 0 && $show_after_filter_flag) {
                                        if (isset($selection[$payment_methods_types_row['payment_methods_types_id']]['show'])) {
                                            $payment_list_arr[$payment_methods_types_row['payment_methods_types_id']]['show'] = 1;
                                            $payment_gateway_category_show = true;
                                        }
                                    }
                                }
                            }

                            echo '<br>';
                            echo '<div style="width:45%;float:left;margin-top:5px;"><div class="row_separator" style="border-bottom-color: #999999"><table border="0" width="100%" cellspacing="0" cellpadding="0"><tr><td></td></tr></table></div></div>';
                            echo '<div style="width:10%;text-align:center;float:left;"><b>' . TEXT_INFO_PAYMENT_IN_USD_OR . '</b></div>';
                            echo '<div style="width:45%;float:left;margin-top:5px;"><div class="row_separator" style="border-bottom-color: #999999"><table border="0" width="100%" cellspacing="0" cellpadding="0"><tr><td></td></tr></table></div></div>';
                            echo '<br><br>';

                            $payment_gateway_array = array();
                            $display_flag = false;

                            foreach ($payment_list_arr as $gw_id => $payment_cat_display) {
                                $pg_class = '';
                                $pgt_class_name = 'boxHeaderCollapse';
                                $actual_pg_show = 0;

                                if ($payment_gateway_category_show) {
                                    if (tep_not_null($payment)) {
                                        $pg_class = 'class="hide"';
                                        $pgt_class_name = 'boxHeaderExpand';

                                        if ($payment_cat_display['show'] == 1) {
                                            $pg_class = '';
                                            $pgt_class_name = 'boxHeaderCollapse';
                                        }
                                    }
                                }

                                if (isset($payment_cat_display['type']) && $payment_cat_display['type'] == 'payment') {
                                    $payment_methods_array = array();
                                    if (count($sorted_selection[$gw_id])) {

                                        foreach ($sorted_selection[$gw_id] as $pm_id => $pm_info_array) {
                                            $pg_key = $pm_info_array['pg_key'];

                                            if ($show_paypal_flag === true && $count_payment_methds >= 5 && ($pm_info_array['payment_gateway_code'] != 'paypal' || $pm_info_array['payment_gateway_code'] != 'paypalEC') && $temp_currency != 'BRL' && $temp_currency != 'JPY' && $temp_currency != 'HKD' && $temp_currency != 'MXN') {
                                                continue;
                                            }
                                            if (in_array($pm_id, $displayed_payment_methods_array)) {
                                                continue;
                                            }
                                            $actual_pg_show++;
                                            $display_flag = true;
                                            $pm_html = '';

                                            $supported_currencies_array = array();
                                            $unique_name = $gw_id . ':~:' . $pm_info_array['payment_gateway_code'] . ':~:' . $pm_id;
                                            $payment_title = $pm_info_array['payment_methods_description_title'];
                                            $pro_img = $pm_info_array['payment_methods_logo'];

                                            $pro_img_w = 0;
                                            $pro_img_h = 0;

                                            for ($curr_cnt = 0; $curr_cnt < sizeof($pm_info_array['currency']); $curr_cnt++) {
                                                $supported_currencies_array[] = array('id' => $pm_info_array['currency'][$curr_cnt], 'text' => $pm_info_array['currency'][$curr_cnt]);
                                            }

                                            if (is_object($ot_surcharge)) {
                                                $surcharge_amt = $ot_usd_surcharge->calculate_surcharge_by($unique_name, DEFAULT_CURRENCY, 4, $order->info['total']);
                                                $surcharge_html = " <div id='surcharge_" . $radio_buttons . "'><table><tr><td>" . ($surcharge_amt > 0 ? "(+ " . $currencies->format($surcharge_amt, false, DEFAULT_CURRENCY) . " " . ENTRY_PAYMENT_SURCHARGE . ")" : "") . "</td></tr></table></div>";
                                            } else {
                                                $surcharge_html = "	<div id='surcharge_" . $radio_buttons . "'><table><tr><td></td></tr></table></div>";
                                            }

                                            $payment_title = $pm_info_array['payment_methods_description_title'] . $surcharge_html;

                                            if ($pm_info_array['payment_methods_receive_status_mode'] == 1) {
                                                $radio_para = 'onclick="updateCurrency(\'' . $radio_buttons . '\', \'change\', \'' . DEFAULT_CURRENCY . '\');"';

                                                if (tep_not_null($payment) && $payment == 'pm_' . $unique_name) {
                                                    $pm_html .= '<div class="moduleRowSelected" id="defaultSelected" onmouseover="rowOverEffect(this);" onmouseout="rowOutEffect(this);" onclick="updateCurrency(\'' . $radio_buttons . '\', \'\', \'' . DEFAULT_CURRENCY . '\'); selectRowEffect(this, ' . $radio_buttons . ');" valign="top">';
                                                    $default_selected = $radio_buttons;
                                                    $selected_payment_flag = 2;
                                                } else {
                                                    $pm_html .= '<div class="moduleRow" id="row_pm_' . $radio_buttons . '" onmouseover="rowOverEffect(this);" onmouseout="rowOutEffect(this);" onclick="updateCurrency(\'' . $radio_buttons . '\', \'\', \'' . DEFAULT_CURRENCY . '\'); selectRowEffect(this, ' . $radio_buttons . ');" valign="top">';
                                                }
                                            } else {
                                                $radio_para = 'disabled';
                                                $payment_title .= '<br>' . $pm_info_array['payment_methods_status_message'];

                                                $pm_html .= '	<div class="moduleRow" id="row_pm_' . $radio_buttons . '" valign="top">';
                                            }

                                            $pm_html .= '<table border="0" width="98%" cellspacing="0" cellpadding="0">
                                                        <tr>
                                                            <td colspan="5">' . tep_draw_separator('pixel_trans.gif', '100%', '8') . '</td>
                                                        </tr>
                                                        <tr>
                                                            <td width="3%"></td>
                                                            <td width="5%">' . ($pg_key != '99999' ? tep_draw_radio_field('payment', 'pm_' . $unique_name, false, ' id="pm_' . $radio_buttons . '" ' . $radio_para) : '') . '</td>';

                                            if ($pg_key != '99999') {
                                                $aws_obj = new ogm_amazon_ws();
                                                $aws_obj->set_bucket_key('BUCKET_STATIC');
                                                $aws_obj->set_filepath('images/payment/');
                                                $pm_logo_path = '';

                                                $image_info_array = $aws_obj->get_image_info($pro_img);

                                                if (tep_not_null($image_info_array)) {
                                                    $pm_logo_path = $image_info_array['src'];
                                                } else if (tep_not_null($pro_img) && file_exists(DIR_FS_IMAGES . 'payment/' . $pro_img)) {
                                                    $pm_logo_path = DIR_WS_IMAGES . 'payment/' . $pro_img;
                                                }

                                                if (tep_not_null($pm_logo_path))
                                                    list($pro_img_w, $pro_img_h) = getimagesize($pm_logo_path);

                                                $pm_html .= "<td width='12%'>" . ($pro_img_w > 0 ? tep_image($pm_logo_path, '', $pro_img_w, $pro_img_h) : '&nbsp;') . "</td>";

                                                unset($aws_obj);
                                            }

                                            $pm_html .= '		<td class="main" style="padding-left:12px;">' . $payment_title . '</td>
                                                        <td width="10%" align="right">' .
                                                    //tep_draw_pull_down_menu('cr_' . $radio_buttons, $supported_currencies_array, $_SESSION['currency'], ' disabled onchange="updateCurrency(' . $radio_buttons . ', \'change\');" id="cr_' . $radio_buttons . '"') .
                                                    '</td>
                                                    </tr>';

                                            //if ($pm_info_array['show_billing_address'] == 'True' || $pm_info_array['show_contact_number'] == 'True' || $pm_info_array['show_ic'] == 'True') {  // currenct for mobile money only
                                            $pm_html .= '<tr>
                                                    <td width="8%" colspan="2"></td>
                                                    <td colspan="3">
                                                        <div class="' . ($payment == 'pm_' . $unique_name ? '' : 'hide') . ' system_cust_pm_info" id="pm_cust_info_' . $radio_buttons . '">
                                                            <table border="0" width="100%" cellspacing="0" cellpadding="0">';

                                            $display_confirm_link = false;
                                            if ($pm_info_array['show_billing_address'] == 'True') {
                                                $display_confirm_link = true;
                                                $pm_html .= '		<tr>
                                                                    <td colspan="2">' . tep_draw_separator('pixel_trans.gif', '100%', '5') . '</td>
                                                                </tr>
                                                                <tr>
                                                                    <td colspan="2">' .
                                                        tep_address_label($customer_id, $billto, true, ' ', '<br>') . '<br>' .
                                                        '<a href="' . tep_href_link(FILENAME_ACCOUNT_EDIT, 'trace=chkout', 'SSL') . '" id="edit_address"><b>' . TEXT_EDIT_ADDRESS . '</b></a>
                                                                    </td>
                                                                </tr>';
                                            }

                                            if ($pm_info_array['show_contact_number'] == 'True' || $pm_info_array['show_surname'] == 'True' || $pm_info_array['show_city'] == 'True') {
                                                $account_sql = "SELECT customers_telephone, customers_default_address_id, customers_lastname
                                                            FROM " . TABLE_CUSTOMERS . "
                                                            WHERE customers_id ='" . (int) $customer_id . "'";
                                                $account_result_sql = tep_db_query($account_sql);
                                                $account = tep_db_fetch_array($account_result_sql);
                                            }

                                            if ($pm_info_array['show_city'] == 'True' || $pm_info_array['show_street'] == 'True' || $pm_info_array['show_zip'] == 'True') {
                                                $customers_info_select_sql = "	SELECT entry_street_address, entry_postcode, entry_city
                                                                            FROM " . TABLE_ADDRESS_BOOK . "
                                                                            WHERE address_book_id = '" . (int) $account['customers_default_address_id'] . "'
                                                                                AND customers_id ='" . (int) $customer_id . "'";
                                                $customers_info_result_sql = tep_db_query($customers_info_select_sql);
                                                $customers_info_row = tep_db_fetch_array($customers_info_result_sql);
                                            }

                                            if ($pm_info_array['show_contact_number'] == 'True') {
                                                $display_confirm_link = true;
                                                $customer_complete_phone_info_array = tep_format_telephone($customer_id);
                                                if (!isset($_SESSION['customers_telephone'])) {
                                                    $_SESSION['customers_telephone'] = $account['customers_telephone'];
                                                }
                                                $pm_html .= '		<tr>
                                                                    <td colspan="2">' . tep_draw_separator('pixel_trans.gif', '100%', '5') . '</td>
                                                                </tr>
                                                                <tr valign="top">
                                                                    <td width="15%">' . ENTRY_PAYMENT_CONTACT_NUMBER . '</td>
                                                                    <td>+' . $customer_complete_phone_info_array['country_international_dialing_code'] . ' ' . tep_draw_input_field('telephone_number', tep_mask_telephone($_SESSION['customers_telephone']), ' id="telephone_number" ') . '</td>
                                                                </tr>';
                                            }
                                            if ($pm_info_array['show_ic'] == 'True') {
                                                $display_confirm_link = true;
                                                $pm_html .= '		<tr>
                                                                    <td colspan="2">' . tep_draw_separator('pixel_trans.gif', '100%', '5') . '</td>
                                                                </tr>
                                                                <tr valign="top">
                                                                    <td nowrap width="15%">' . ENTRY_PAYMENT_CUSTOMER_IDENTIFY_NUMBER . '</td>
                                                                    <td>' . tep_draw_input_field('identify_number', (isset($_SESSION['identify_number']) ? $_SESSION['identify_number'] : ''), ' id="identify_number" ') . '<br>' . TEXT_INFO_IDENTIFY_NUMBER_EXAMPLE . '</td>
                                                                </tr>';
                                            }

                                            if ($pm_info_array['show_surname'] == 'True') {
                                                $display_confirm_link = true;
                                                if (!isset($_SESSION['checkout_surname'])) {
                                                    $_SESSION['checkout_surname'] = $account['customers_lastname'];
                                                }

                                                $pm_html .= '		<tr>
                                                                    <td colspan="2">' . tep_draw_separator('pixel_trans.gif', '100%', '5') . '</td>
                                                                </tr>
                                                                <tr valign="top">
                                                                    <td nowrap width="15%">' . ENTRY_PAYMENT_CUSTOMER_SURNAME . '</td>
                                                                    <td>' . tep_draw_input_field('checkout_surname', $_SESSION['checkout_surname'], ' id="checkout_surname" ') . '</td>
                                                                </tr>';
                                            }

                                            if ($pm_info_array['show_housenumber'] == 'True') {
                                                $display_confirm_link = true;

                                                if (!isset($_SESSION['checkout_housenumber'])) {
                                                    $_SESSION['checkout_housenumber'] = '';
                                                }

                                                $pm_html .= '		<tr>
                                                                    <td colspan="2">' . tep_draw_separator('pixel_trans.gif', '100%', '5') . '</td>
                                                                </tr>
                                                                <tr valign="top">
                                                                    <td nowrap width="15%">' . ENTRY_PAYMENT_CUSTOMER_HOUSENUMBER . '</td>
                                                                    <td>' . tep_draw_input_field('checkout_housenumber', $_SESSION['checkout_housenumber'], ' id="checkout_housenumber" ') . '</td>
                                                                </tr>';
                                            }

                                            if ($pm_info_array['show_street'] == 'True') {
                                                $display_confirm_link = true;

                                                if (!isset($_SESSION['checkout_street'])) {
                                                    $_SESSION['checkout_street'] = $customers_info_row['entry_street_address'];
                                                }

                                                $pm_html .= '		<tr>
                                                                    <td colspan="2">' . tep_draw_separator('pixel_trans.gif', '100%', '5') . '</td>
                                                                </tr>
                                                                <tr valign="top">
                                                                    <td nowrap width="15%">' . ENTRY_PAYMENT_CUSTOMER_STREET . '</td>
                                                                    <td>' . tep_draw_input_field('checkout_street', $_SESSION['checkout_street'], ' id="checkout_street" ') . '</td>
                                                                </tr>';
                                            }

                                            if ($pm_info_array['show_city'] == 'True') {
                                                $display_confirm_link = true;

                                                if (!isset($_SESSION['checkout_city'])) {
                                                    $_SESSION['checkout_city'] = $customers_info_row['entry_city'];
                                                }

                                                $pm_html .= '		<tr>
                                                                    <td colspan="2">' . tep_draw_separator('pixel_trans.gif', '100%', '5') . '</td>
                                                                </tr>
                                                                <tr valign="top">
                                                                    <td nowrap width="15%">' . ENTRY_PAYMENT_CUSTOMER_CITY . '</td>
                                                                    <td>' . tep_draw_input_field('checkout_city', $_SESSION['checkout_city'], ' id="checkout_city" ') . '</td>
                                                                </tr>';
                                            }

                                            if ($pm_info_array['show_zip'] == 'True') {
                                                $display_confirm_link = true;

                                                if (!isset($_SESSION['checkout_zip'])) {
                                                    $_SESSION['checkout_zip'] = $customers_info_row['entry_postcode'];
                                                }

                                                $pm_html .= '		<tr>
                                                                    <td colspan="2">' . tep_draw_separator('pixel_trans.gif', '100%', '5') . '</td>
                                                                </tr>
                                                                <tr valign="top" width="15%">
                                                                    <td nowrap>' . ENTRY_PAYMENT_CUSTOMER_ZIP . '</td>
                                                                    <td>' . tep_draw_input_field('checkout_zip', $_SESSION['checkout_zip'], ' id="checkout_zip" ') . '</td>
                                                                </tr>';
                                            }

                                            if ($pm_info_array['show_accountname'] == 'True') {
                                                $display_confirm_link = true;

                                                if (!isset($_SESSION['checkout_accountname'])) {
                                                    $_SESSION['checkout_accountname'] = '';
                                                }

                                                $pm_html .= '		<tr>
                                                                    <td colspan="2">' . tep_draw_separator('pixel_trans.gif', '100%', '5') . '</td>
                                                                </tr>
                                                                <tr valign="top" width="15%">
                                                                    <td nowrap>' . ENTRY_PAYMENT_CUSTOMER_ACCOUNTNAME . '</td>
                                                                    <td>' . tep_draw_input_field('checkout_accountname', $_SESSION['checkout_accountname'], ' id="checkout_accountname" ') . '</td>
                                                                </tr>';
                                            }

                                            if ($pm_info_array['show_accountnumber'] == 'True') {
                                                $display_confirm_link = true;

                                                if (!isset($_SESSION['checkout_accountnumber'])) {
                                                    $_SESSION['checkout_accountnumber'] = '';
                                                }

                                                $pm_html .= '		<tr>
                                                                    <td colspan="2">' . tep_draw_separator('pixel_trans.gif', '100%', '5') . '</td>
                                                                </tr>
                                                                <tr valign="top" width="15%">
                                                                    <td nowrap>' . ENTRY_PAYMENT_CUSTOMER_ACCOUNTNUMBER . '</td>
                                                                    <td>' . tep_draw_input_field('checkout_accountnumber', $_SESSION['checkout_accountnumber'], ' id="checkout_accountnumber" ') . '</td>
                                                                </tr>';
                                            }

                                            if ($pm_info_array['show_account_number'] == 'True') {
                                                $display_confirm_link = true;

                                                if (!isset($_SESSION['checkout_account_number'])) {
                                                    $_SESSION['checkout_account_number'] = '';
                                                }

                                                $pm_html .= '		<tr>
                                                                    <td colspan="2">' . tep_draw_separator('pixel_trans.gif', '100%', '5') . '</td>
                                                                </tr>
                                                                <tr valign="top" width="15%">
                                                                    <td nowrap>' . ENTRY_PAYMENT_CUSTOMER_ACCOUNT_NUMBER . '</td>
                                                                    <td>' . tep_draw_input_field('checkout_zip', $_SESSION['checkout_account_number'], ' id="checkout_account_number" ') . '</td>
                                                                </tr>';
                                            }

                                            if ($pm_info_array['show_bankcode'] == 'True') {
                                                $display_confirm_link = true;

                                                if (!isset($_SESSION['checkout_bankcode'])) {
                                                    $_SESSION['checkout_bankcode'] = '';
                                                }

                                                $pm_html .= '		<tr>
                                                                    <td colspan="2">' . tep_draw_separator('pixel_trans.gif', '100%', '5') . '</td>
                                                                </tr>
                                                                <tr valign="top" width="15%">
                                                                    <td nowrap>' . ENTRY_PAYMENT_CUSTOMER_BANKCODE . '</td>
                                                                    <td>' . tep_draw_input_field('checkout_bankcode', $_SESSION['checkout_bankcode'], ' id="checkout_bankcode" ') . '</td>
                                                                </tr>';
                                            }

                                            if ($pm_info_array['show_branchcode'] == 'True') {
                                                $display_confirm_link = true;

                                                if (!isset($_SESSION['checkout_branchcode'])) {
                                                    $_SESSION['checkout_branchcode'] = '';
                                                }

                                                $pm_html .= '		<tr>
                                                                    <td colspan="2">' . tep_draw_separator('pixel_trans.gif', '100%', '5') . '</td>
                                                                </tr>
                                                                <tr valign="top" width="15%">
                                                                    <td nowrap>' . ENTRY_PAYMENT_CUSTOMER_BRANCHCODE . '</td>
                                                                    <td>' . tep_draw_input_field('checkout_branchcode', $_SESSION['checkout_branchcode'], ' id="checkout_branchcode" ') . '</td>
                                                                </tr>';
                                            }

                                            if ($pm_info_array['show_directdebittext'] == 'True') {
                                                $display_confirm_link = true;

                                                if (!isset($_SESSION['checkout_directdebittext'])) {
                                                    $_SESSION['checkout_directdebittext'] = '';
                                                }

                                                $pm_html .= '		<tr>
                                                                    <td colspan="2">' . tep_draw_separator('pixel_trans.gif', '100%', '5') . '</td>
                                                                </tr>
                                                                <tr valign="top" width="15%">
                                                                    <td nowrap>' . ENTRY_PAYMENT_CUSTOMER_DIRECTDEBITTEXT . '</td>
                                                                    <td>' . tep_draw_input_field('checkout_directdebittext', $_SESSION['checkout_directdebittext'], ' id="checkout_directdebittext" ') . '</td>
                                                                </tr>';
                                            }

                                            if ($pm_info_array['show_vouchernumber'] == 'True') {
                                                $display_confirm_link = true;

                                                if (!isset($_SESSION['show_voucher_number'])) {
                                                    $_SESSION['show_voucher_number'] = '';
                                                }

                                                $pm_html .= '		<tr>
                                                                    <td colspan="2">' . tep_draw_separator('pixel_trans.gif', '100%', '5') . '</td>
                                                                </tr>
                                                                <tr valign="top" width="15%">
                                                                    <td nowrap>' . ENTRY_PAYMENT_CUSTOMER_VOUCHER_NUMBER . '</td>
                                                                    <td>' . tep_draw_input_field('checkout_voucher_number', $_SESSION['show_voucher_number'], ' id="checkout_voucher_number" ') . '</td>
                                                                </tr>';
                                            }

                                            if ($pm_info_array['show_vouchervalue'] == 'True') {
                                                $display_confirm_link = true;

                                                if (!isset($_SESSION['checkout_voucher_value'])) {
                                                    $_SESSION['checkout_voucher_value'] = '';
                                                }

                                                $pm_html .= '		<tr>
                                                                    <td colspan="2">' . tep_draw_separator('pixel_trans.gif', '100%', '5') . '</td>
                                                                </tr>
                                                                <tr valign="top" width="15%">
                                                                    <td nowrap>' . ENTRY_PAYMENT_CUSTOMER_VOUCHER_VALUE . '</td>
                                                                    <td>' . tep_draw_input_field('checkout_voucher_value', $_SESSION['checkout_voucher_value'], ' id="checkout_voucher_value" ') . '</td>
                                                                </tr>';
                                            }

                                            if ($display_confirm_link) {
                                                $pm_html .= '		<tr>
                                                                    <td colspan="2"><div><a href="javascript:update_pm_cust_info(\'pm_cust_info_' . $radio_buttons . '\')"><b>' . LINK_UPDATE_CUSTOMER_PAYMENT_INFO . '</b></a></div></td>
                                                                </tr>';
                                            }
                                            $pm_html .= '		</table>
                                                            </div>
                                                        </td>
                                                    </tr>';
                                            //}
                                            $pm_html .= '	<tr>
                                                        <td colspan="5">' . tep_draw_separator('pixel_trans.gif', '100%', '8') . '</td>
                                                    </tr>
                                                </table>
                                            </div>';

                                            $payment_methods_array[] = array('id' => 'vmenu_pm_' . $pm_id, 'desc' => $pm_html, 'href' => '');
                                            $radio_buttons++;
                                        }
                                    }
                                }

                                if ($actual_pg_show > 0) {
                                    $payment_gateway_array[] = array('id' => 'vmenu_pg_' . $gw_id,
                                        'desc' => $payment_cat_display['description'],
                                        'href' => 'javascript:void(0);',
                                        'slist' => $payment_methods_array,
                                        'default_open' => $payment_cat_display['show'],
                                    );
                                }
                            }

                            if ($display_flag) {
                                echo $page_obj->get_html_expandable_vmenu_box(sprintf(HEADING_SUBTITLE, $currencies->currencies[DEFAULT_CURRENCY]['title'] . ' (' . $currencies->currencies[DEFAULT_CURRENCY]['symbol_left'] . $currencies->currencies[DEFAULT_CURRENCY]['symbol_right'] . ')'), $payment_gateway_array, 12, '', 12);
                            }

                            // restore temp store
                            $currency = $_SESSION['currency'] = $temp_currency;
                            $order = $temp_order_obj;
                            // end temp store
                        }

                        echo '<br><br>';
                        echo '<div class="row_separator" style="border-bottom-color: #999999"><table border="0" width="100%" cellspacing="0" cellpadding="0"><tr><td></td></tr></table></div>';
                        echo '<br>';

                        $payment_gateway_footer_html = '<table cellspacing="0" cellpadding="0" border="0" width="98%">
										<tr>
											<td colspan="5">' . tep_draw_separator('pixel_trans.gif', '100%', '8') . '</td>
										</tr>
										<tr>
											<td colspan="5"><span class="hd5">' . TEXT_INFO_CHANGE_CHECKOUT_CURRENCY . '</span></td>
										</tr>
										<tr>
											<td colspan="5">' . tep_draw_separator('pixel_trans.gif', '100%', '8') . '</td>
										</tr>
										<tr>
											<td width="*%" colspan="4"><b>' . TEXT_INFO_CURRENT_CHECKOUT_CURRENCY . '</b></td>
											<td width="30%" align="right">' . tep_draw_pull_down_menu('sel_checkout_currency', $currencies_array, $currency, ' id="sel_checkout_currency" onchange="set_localization_value(\'currency\' , this.value)" ') . '</td>
										</tr>
									</table>';

                        echo $payment_gateway_footer_html;

                        if (isset($_SESSION['need_sc_usage_qna']) && $_SESSION['need_sc_usage_qna'] == 'true') {
							if (isset($_SESSION['rp_qna_flag']) && $_SESSION['rp_qna_flag'] == false) {
								//do nth
							} else {
                            ?>
                            <script language="JavaScript">
                                jQuery(document).ready(function() {
                                    checkoutQnA();
        <?
        if ($messageStack->size('qna') > 0) {
            echo 'jQuery("#div_action_msg").html("<font color=red>' . ENTRY_QNA_MISMATCH_ANSWER_ERROR . '<font>");';
        }
        ?>
                                });
                            </script>
                            <?
							}
							$_SESSION['rp_qna_flag'] = false;
                        }
                    }
                    ?>
                    </form>
                </div>
            </div>
            <div style="width: 473px; float: right;"><?= $page_obj->get_html_simple_rc_box(HEADING_TITLE, '<div id="cartbox_div">' . $cart->get_cart() . '</div><div style="clear:both"></div>', 12) ?></div>
            <div style="padding-top: 25px; clear: right;">
                <table cellspacing="0" cellpadding="0" border="0" width="50%">
                    <tr>
                        <td width="15px"></td>
                        <td><?php echo TEXT_INFO_PAYMENT_SURCHARGE_DESC; ?></td>
                    </tr>
                </table>
            </div>
        </td>
    </tr>
</table>
<div id="ogm_tooltips"><div class="ogm_tooltips_title"></div><div class="ogm_tooltips_content"></div><div class="ogm_tooltips_triangle"></div></div>
<script language="JavaScript">
<? if (tep_not_null($payment)) { ?>
        jQuery(document).ready(function() {
    <? if (isset($selected_payment_flag) && $selected_payment_flag == 2) { ?>
                updateCurrency('<?= $default_selected ?>', 'change', '<?= DEFAULT_CURRENCY ?>');
    <? } else { ?>
                updateCurrency('<?= $default_selected ?>', 'change');
    <? } ?>
        });
<? } ?>
    jQuery(document).ready(function() {
        if (jQuery('.ogm_tooltips').length > 0) {
            jQuery('.ogm_tooltips').ogm_tooltips({
                image_up: '<?= DIR_WS_IMAGES ?>triangle_face_up.gif',
                image_close: '<?= DIR_WS_ICONS ?>icon_close.gif'
            });
        }
    });

    function popup_cfm_billing_info(act) {
        var action = act != undefined ? act : 'get_billing_content',
                submit_data = '';

        jQuery.ajax({
            url: 'customer_xmlhttp.php?action=' + action,
            data: submit_data,
            type: 'POST',
            dataType: 'xml',
            timeout: 60000,
            beforeSend: function() {
                blockUI_disable();
            },
            error: function() {
                jQuery.unblockUI();
                alert('Please try again.');
            },
            success: function(xml) {
                jQuery.unblockUI();
                if (jQuery(xml).find('error_message').length > 0 && jQuery(xml).find('error_message').text() != '') {
                    alert(jQuery(xml).find('error_message').text());
                } else if (jQuery(xml).find('reload').length > 0) {
                    location.reload();
                } else if (jQuery(xml).find('redirect').length > 0) {
                    document.location.href = jQuery(xml).find('redirect').text();
                } else {
                    if (jQuery(xml).find('billing_info_status').text() == 0) {
                        custom_general_popup_box(jQuery(xml).find('content').text(), jQuery(xml).find('header').text(), '550px', {padding: '0px'});
                    } else {
                        document.checkout_confirmation.submit();
                    }

                }
            }
        });
    }

    function cfm_billing() {
        jQuery.ajax({
            type: "POST",
            url: 'customer_xmlhttp.php?action=cfm_billing',
            data: jQuery("#billing_form").serialize(),
            async: false,
            dataType: "xml",
            beforeSend: function() {
                jQuery('#billing_form div.error_msg').html('');
            },
            error: function() {
                alert('Please try again.');
            },
            success: function(xml) {
                if (jQuery(xml).find('error_message').length > 0 && jQuery(xml).find('error_message').text() != '') {
                    jQuery('#billing_form div.error_msg').html(jQuery(xml).find('error_message').text());
                } else if (jQuery(xml).find('form_submit').length > 0) {
                    document.checkout_confirmation.submit();
                } else if (jQuery(xml).find('redirect').length > 0) {
                    document.location.href = jQuery(xml).find('redirect').text();
                } else {
                    if (jQuery(xml).find('content').length > 0) {
                        custom_general_popup_box(jQuery(xml).find('content').text(), jQuery(xml).find('header').text(), '530px', {padding: '0px'});
                    }
                }
            }
        });
    }

    function cfm_security_token() {
        jQuery.ajax({
            type: 'GET',
            url: 'customer_xmlhttp.php?action=match_security_tac',
            data: {'answer': jQuery('#enter_token_div input[name=answer]').val()},
            dataType: 'xml',
            beforeSend: function() {
                jQuery('#token_request_icon, #token_request_msg').html('');
            },
            success: function(xml) {
                if (jQuery(xml).find('error').length > 0 && jQuery(xml).find('error').text() != '-') {
                    jQuery("#token_request_icon").html(jQuery(xml).find('error_icon').text());
                    jQuery("#token_request_msg").html(jQuery(xml).find('error').text());
                } else {
                    blockUI_disable();
                    jQuery('div.popup_close_button').click();

                    document.checkout_confirmation.submit();
                }
            }
        });
    }
</script>