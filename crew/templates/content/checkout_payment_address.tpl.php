<? echo tep_draw_form('checkout_address', tep_href_link(FILENAME_CHECKOUT_PAYMENT_ADDRESS, tep_get_all_get_params(), 'SSL'), 'post', 'onSubmit="return check_form_optional(checkout_address);"'); ?>
<table border="0" width="100%" cellspacing="0" cellpadding="0">
	<tr>
		<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
	<tr>
		<td class="pageHeadingTitle" valign="middle" align="center"><?=HEADING_TITLE?></td>
	</tr>
	<tr>
		<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
	<tr>
		<td>
			<div class="row_separator"></div>
		</td>
	</tr>
	<tr>
		<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
<?
if ($messageStack->size('checkout_address') > 0) {
?>
	<tr>
    	<td><?=$messageStack->output('checkout_address')?></td>
	</tr>
    <tr>
    	<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
<?
}
?>
  	<tr>
    	<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
  	</tr>
<?
if ($process == false) {
?>
	<tr>
    	<td>
    		<table border="0" width="100%" cellspacing="0" cellpadding="2">
          		<tr>
            		<td class="paymentBoxHeading"><?=TABLE_HEADING_PAYMENT_ADDRESS?></b></td>
          		</tr>
        	</table>
        </td>
	</tr>
    <tr>
    	<td>
    		<table border="0" width="100%" cellspacing="1" cellpadding="2" class="infoBox">
          		<tr class="infoBoxContents">
            		<td>
            			<table border="0" width="100%" cellspacing="0" cellpadding="2">
              				<tr>
                				<td><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
                				<td class="spacingInstruction" width="100%" valign="top"><?=TEXT_SELECTED_PAYMENT_DESTINATION?></td>
                			</tr>
                			<tr>
                				<td><?=tep_draw_separator('pixel_trans.gif', '10', '1'); ?></td>
                				<td align="left" width="100%" valign="top">
                					<table border="0" width="100%" cellspacing="0" cellpadding="2">
      									<tr>
        									<td class="paymentAddressTitle" align="left" valign="top"><?=TITLE_PAYMENT_ADDRESS?></td>
        								</tr>
        								<tr>
        									<td class="paymentAddress" valign="top" align="left"><?=tep_address_label($customer_id, $billto, true, ' ', '<br>')?></td>
      									</tr>
    								</table>
                				</td>
              				</tr>
            			</table>
            		</td>
          		</tr>
        	</table>
        </td>
	</tr>
    <tr>
    	<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
<?
	if ($addresses_count > 1) {
?>
    <tr>
        <td>
        	<table border="0" width="100%" cellspacing="0" cellpadding="2">
          		<tr>
            		<td class="paymentBoxHeading"><?=TABLE_HEADING_ADDRESS_BOOK_ENTRIES?></td>
          		</tr>
        	</table>
        </td>
	</tr>
    <tr>
    	<td>
    		<table border="0" width="100%" cellspacing="1" cellpadding="2" class="infoBox">
          		<tr class="infoBoxContents">
            		<td>
            			<table border="0" width="100%" cellspacing="0" cellpadding="2">
              				<tr>
                				<td><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
                				<td class="spacingInstruction" valign="top"><?=TEXT_SELECT_OTHER_PAYMENT_DESTINATION?></td>
                				<td><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
              				</tr>
<?
		$radio_buttons = 0;

      	$addresses_query = tep_db_query("select address_book_id, entry_firstname as firstname, entry_lastname as lastname, entry_company as company, entry_street_address as street_address, entry_suburb as suburb, entry_city as city, entry_postcode as postcode, entry_state as state, entry_zone_id as zone_id, entry_country_id as country_id from " . TABLE_ADDRESS_BOOK . " where customers_id = '" . $customer_id . "'");
      	while ($addresses = tep_db_fetch_array($addresses_query)) {
        	$format_id = tep_get_address_format_id($addresses['country_id']);
?>
            				<tr>
                				<td><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
                				<td colspan="2">
                					<table border="0" width="100%" cellspacing="0" cellpadding="2">
<?
       		if ($addresses['address_book_id'] == $billto) {
          		echo '                  <tr id="defaultSelected" class="moduleRowSelected" onmouseover="rowOverEffect(this)" onmouseout="rowOutEffect(this)" onclick="selectRowEffect(this, ' . $radio_buttons . ')">' . "\n";
        	} else {
          		echo '                  <tr class="moduleRow" onmouseover="rowOverEffect(this)" onmouseout="rowOutEffect(this)" onclick="selectRowEffect(this, ' . $radio_buttons . ')">' . "\n";
        	}
?>
                    						<td width="10"><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
                    						<td class="paymentAddressTitle" colspan="2"><?=$addresses['firstname'] . ' ' . $addresses['lastname']?></td>
                    						<td class="inputField" align="right"><?=tep_draw_radio_field('address', $addresses['address_book_id'], ($addresses['address_book_id'] == $billto))?></td>
                    						<td width="10"><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
                  						</tr>
                  						<tr>
                    						<td width="10"><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
                    						<td colspan="3">
                    							<table border="0" cellspacing="0" cellpadding="2">
                      								<tr>
                        								<td width="10"><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
                        								<td class="paymentAddress"><?=tep_address_format($format_id, $addresses, true, ' ', ', ')?></td>
                        								<td width="10"><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
                      								</tr>
                    							</table>
                    						</td>
                    						<td width="10"><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
                  						</tr>
                					</table>
                				</td>
                				<td><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
              				</tr>
<?
        	$radio_buttons++;
		}
?>
        				</table>
        			</td>
          		</tr>
        	</table>
        </td>
	</tr>
    <tr>
    	<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
<?
    }
}

if ($addresses_count < MAX_ADDRESS_BOOK_ENTRIES) {
?>
	<tr>
    	<td>
    		<table border="0" width="100%" cellspacing="0" cellpadding="2">
          		<tr>
            		<td class="paymentBoxHeading"><?=TABLE_HEADING_NEW_PAYMENT_ADDRESS?></td>
          		</tr>
        	</table>
        </td>
	</tr>
    <tr>
    	<td>
    		<table border="0" width="100%" cellspacing="1" cellpadding="2" class="infoBox">
          		<tr class="infoBoxContents">
            		<td>
            			<table border="0" width="100%" cellspacing="0" cellpadding="2">
              				<tr>
                				<td><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
                				<td class="spacingInstruction" width="100%" valign="top"><?=TEXT_CREATE_NEW_PAYMENT_ADDRESS?></td>
                				<td><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
              				</tr>
              				<tr>
                				<td><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
                				<td>
                					<table border="0" width="100%" cellspacing="0" cellpadding="2">
                  						<tr>
                    						<td width="10"><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
                    						<td><? require(DIR_WS_MODULES . 'checkout_new_address.php'); ?></td>
                    						<td width="10"><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
                  						</tr>
                					</table>
                				</td>
                				<td><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
              				</tr>
            			</table>
            		</td>
          		</tr>
        	</table>
        </td>
	</tr>
<?
}
?>
	<tr>
    	<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
    <tr>
    	<td>
    		<table border="0" width="100%" cellspacing="1" cellpadding="2" class="buttonBox">
          		<tr class="buttonBoxContents">
            		<td>
            			<table border="0" width="100%" cellspacing="0" cellpadding="2">
              				<tr>
              					<td width="10"><? echo tep_draw_separator('pixel_trans.gif', '10', '1'); ?></td>
<?
if ($process == true) {
	echo '						<td>'.tep_div_button(2, IMAGE_BUTTON_BACK,tep_href_link(FILENAME_CHECKOUT_PAYMENT_ADDRESS, '', 'SSL'), '', 'gray_button').'</td>';
}
?>
                				<td class="main"><? //echo '<b>' . TITLE_CONTINUE_CHECKOUT_PROCEDURE . '</b><br>' . TEXT_CONTINUE_CHECKOUT_PROCEDURE; ?></td>
                				<td class="main" align="right">
									<? echo tep_draw_hidden_field('action', 'submit') ?>
									<?=tep_submit_button(IMAGE_BUTTON_CONTINUE, IMAGE_BUTTON_CONTINUE, '', 'gray_submit_button', false)?>
								</td>
                				<td width="10"><? echo tep_draw_separator('pixel_trans.gif', '10', '1'); ?></td>
              				</tr>
            			</table>
            		</td>
          		</tr>
        	</table>
        </td>
	</tr>
</table>
</form>