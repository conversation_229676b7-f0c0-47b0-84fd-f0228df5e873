<? echo tep_draw_form('checkout_address', tep_href_link(FILENAME_CHECKOUT_SHIPPING, '', 'SSL'), 'post', 'onsubmit="return shipping_check_form();"') . tep_draw_hidden_field('action', 'process'); ?>
<table border="0" width="100%" cellspacing="0" cellpadding="0">
	<tr>
		<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
	<tr>
		<td class="pageHeadingTitle" valign="middle" align="center"><?=HEADING_TITLE?></td>
	</tr>
	<tr>
		<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
	<tr>
		<td>
			<div class="row_separator"></div>
		</td>
	</tr>
	<tr>
		<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
	<tr>
		<td>
			<table border="0" width="100%" cellspacing="0" cellpadding="0">
	        	<tr>
	            	<td width="25%">
	            		<table border="0" width="100%" cellspacing="0" cellpadding="0">
	              			<tr>
	                			<td width="50%" align="right"><?=tep_image(THEMA_IMAGES . 'checkout_bullet.gif')?></td>
	                			<td width="50%"><?=tep_draw_separator('pixel_silver.gif', '100%', '1')?></td>
	              			</tr>
	            		</table>
	            	</td>
	            	<td width="25%"><?=tep_draw_separator('pixel_silver.gif', '100%', '1')?></td>
	            	<td width="25%"><?=tep_draw_separator('pixel_silver.gif', '100%', '1')?></td>
	            	<td width="25%">
	            		<table border="0" width="100%" cellspacing="0" cellpadding="0">
	              			<tr>
	                			<td width="50%"><?=tep_draw_separator('pixel_silver.gif', '100%', '1')?></td>
	                			<td width="50%"><?=tep_draw_separator('pixel_silver.gif', '1', '5')?></td>
	              			</tr>
	            		</table>
	            	</td>
	          	</tr>
	          	<tr>
	            	<td align="center" width="25%" class="checkoutBarCurrent"><?=CHECKOUT_BAR_DELIVERY?></td>
	            	<td align="center" width="25%" class="checkoutBarTo"><?=CHECKOUT_BAR_PAYMENT?></td>
	            	<td align="center" width="25%" class="checkoutBarTo"><?=CHECKOUT_BAR_CONFIRMATION?></td>
	            	<td align="center" width="25%" class="checkoutBarTo"><?=CHECKOUT_BAR_FINISHED?></td>
	          	</tr>
			</table>
		</td>
	</tr>
    <tr>
    	<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
    <tr>
    	<td>
    		<table border="0" width="100%" cellspacing="0" cellpadding="2">
				<tr>
    				<td class="shippingBoxHeading"><?=TABLE_HEADING_SHIPPING_ADDRESS?></td>
				</tr>
			</table>
		</td>
	</tr>
	<tr>
		<td>
	    	<table border="0" width="100%" cellspacing="1" cellpadding="2" class="infoBox">
	        	<tr class="infoBoxContents">
	          		<td>
	          			<table border="0" width="100%" cellspacing="0" cellpadding="2">
	          				<tr>
	          					<td><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td> 
	                			<td width="100%" class="spacingInstruction" align="left" valign="top"><?=TEXT_CHOOSE_SHIPPING_DESTINATION?></td>
	                			<td><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td> 
	                		</tr>
	                		<tr>
	                			<td><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td> 
	            				<td width="100%" class="main" align="left" valign="top">
				           			<table border="0" width="100%" cellspacing="0" cellpadding="2">
				           				<tr>
				           					<td class="shippingAddressTitle" align="left" valign="top"><? echo '<b>' . TITLE_SHIPPING_ADDRESS . '</b>' ; ?></td>
				     					</tr>
				               			<tr>
				                  			<td class="shippingAddress" align="left" valign="top"><? echo tep_address_label($customer_id, $sendto, true, ' ', '<br>'); ?></td>
				               			</tr>
				           			</table>
				           		</td>
				           		<td><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td> 
				       		</tr>
				       		<tr>
				       			<td><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
				       		</tr>
				       		<tr>
				       			<td><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
				             	<td width="100%" align="left" valign="top"><? echo tep_image_button(THEMA.'button_change_address.gif', IMAGE_BUTTON_CHANGE_ADDRESS, tep_href_link(FILENAME_CHECKOUT_SHIPPING_ADDRESS, '', 'SSL')) ; ?></td>
				               	<td><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td> 
				             </tr>
						</table>
					</td>
				</tr>  
	        </table>
		</td>
	</tr>
	<tr>
		<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
<?
if (tep_count_shipping_modules() > 0)
{
?>
	<tr>
		<td>
			<table border="0" width="100%" cellspacing="0" cellpadding="2">
	        	<tr>
	            	<td class="shippingBoxHeading"><?=TABLE_HEADING_SHIPPING_METHOD?></td>
	          	</tr>
	        </table>
		</td>
	</tr>
	<tr>
		<td>
			<table border="0" width="100%" cellspacing="1" cellpadding="2" class="infoBox">
	        	<tr class="infoBoxContents">
	            	<td>
	            		<table border="0" width="100%" cellspacing="0" cellpadding="2">
<?
	if (sizeof($quotes) > 1 && sizeof($quotes[0]) > 1) {
?>
	              			<tr>
	                			<td><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
	                			<td class="spacingInstruction" width="100%" valign="top"><?=TEXT_CHOOSE_SHIPPING_METHOD?></td>
	                			<td><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
	              			</tr>
<?	} else if ($free_shipping == false) { ?>
	              			<tr>
	                			<td><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
	                			<td class="spacingInstruction" width="100%" colspan="2"><?=TEXT_ENTER_SHIPPING_INFORMATION?></td>
	                			<td><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
	              			</tr>
<? 	}
	
	if ($free_shipping == true) {
?>
	              			<tr>
	                			<td><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
	                			<td colspan="2" width="100%">
	                				<table border="0" width="100%" cellspacing="0" cellpadding="2">
	                  					<tr>
				                    		<td width="10"><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
				                    		<td class="main" colspan="3"><b><?=FREE_SHIPPING_TITLE?></b>&nbsp;<?=$quotes[$i]['icon']?></td>
				                    		<td width="10"><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
				                  		</tr>
				                  		<tr id="defaultSelected" class="moduleRowSelected" onmouseover="rowOverEffect(this)" onmouseout="rowOutEffect(this)" onclick="selectRowEffect(this, 0)">
				                    		<td width="10"><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
				                    		<td class="main" width="100%"><?=sprintf(FREE_SHIPPING_DESCRIPTION, $currencies->format(MODULE_ORDER_TOTAL_SHIPPING_FREE_SHIPPING_OVER)) . tep_draw_hidden_field('shipping', 'free_free')?></td>
				                    		<td width="10"><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
				                  		</tr>
	                				</table>
	                			</td>
	                			<td><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td> 
							</tr>
<?  } else {
		$radio_buttons = 0;
		for ($i=0, $n=sizeof($quotes); $i<$n; $i++) {
?>
	              			<tr>
	                			<td><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
	                			<td colspan="2">
	                				<table border="0" width="100%" cellspacing="0" cellpadding="2">
	                  					<tr>
	                    					<td width="10"><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
	                    					<td class="shippingModuleTitle" colspan="3"><?=$quotes[$i]['module']?>&nbsp;<? if (isset($quotes[$i]['icon']) && tep_not_null($quotes[$i]['icon'])) { echo $quotes[$i]['icon']; } ?></td>
	                    					<td width="10"><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
	                  					</tr>
<?			if (isset($quotes[$i]['error'])) { ?>
	                  					<tr>
	                    					<td width="10"><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
	                    					<td class="main" colspan="3"><?=$quotes[$i]['error']?></td>
	                    					<td width="10"><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
	                  					</tr>
<?        	} else {
	          	for ($j=0, $n2=sizeof($quotes[$i]['methods']); $j<$n2; $j++) {
					// set the radio button to be checked if it is the method chosen
					
	            	$checked = (($quotes[$i]['id'] . '_' . $quotes[$i]['methods'][$j]['id'] == $shipping['id']) ? true : false);
					
	            	if ( ($checked == true) || ($n == 1 && $n2 == 1) ) {
	              		echo '<tr id="defaultSelected" class="moduleRowSelected" onmouseover="rowOverEffect(this)" onmouseout="rowOutEffect(this)" onclick="selectRowEffect(this, ' . $radio_buttons . ')">' . "\n";
	            	} else {
	              		echo '<tr class="moduleRow" onmouseover="rowOverEffect(this)" onmouseout="rowOutEffect(this)" onclick="selectRowEffect(this, ' . $radio_buttons . ')">' . "\n";
	            	}
?>
	                    					<td width="10"><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
	                    					<td class="shippingModuleInfo" width="75%"><?=$quotes[$i]['methods'][$j]['title']?></td>
<?					if ( ($n > 1) || ($n2 > 1) ) { ?>
	                    					<td class="price"><?=$currencies->format(tep_add_tax($quotes[$i]['methods'][$j]['cost'], (isset($quotes[$i]['tax']) ? $quotes[$i]['tax'] : 0)))?></td>
	                    					<td class="main" align="right"><?=tep_draw_radio_field('shipping', $quotes[$i]['id'] . '_' . $quotes[$i]['methods'][$j]['id'], $checked)?></td>
<?					} else { ?>
	                    					<td class="price" align="right" colspan="2"><?=$currencies->format(tep_add_tax($quotes[$i]['methods'][$j]['cost'], $quotes[$i]['tax'])) . tep_draw_hidden_field('shipping', $quotes[$i]['id'] . '_' . $quotes[$i]['methods'][$j]['id'])?></td>
<?           		} ?>
	                    					<td width="10"><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
	                  					</tr>
<?            		$radio_buttons++;
				}
			}
?>
	                				</table>
	                			</td>
	                			<td><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
	              			</tr>
<?     	}
	}
?>
	            		</table>
	            	</td>
	          	</tr>
			</table>
		</td>
	</tr>
	<tr>
		<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
<?
}

if ($cart_comments_obj->count_active_comments(1)) {
?>
    <tr>
        <td>
        	<table border="0" width="100%" cellspacing="0" cellpadding="2">
          		<tr>
            		<td class="commentBoxHeading"><a name='c'></a><?=TABLE_HEADING_COMMENTS?></td>
          		</tr>
        	</table>
        </td>
	</tr>
    <tr>
    	<td>
    		<table border="0" width="100%" cellspacing="1" cellpadding="2" class="infoBox">
          		<tr class="infoBoxContents">
            		<td>
            			<?=$cart_comments_obj->display_comments_fields($custom_comments, 1)?>
            			<!--table border="0" width="100%" cellspacing="0" cellpadding="2">
              				<tr>
                				<td><?=tep_draw_textarea_field('comments', 'soft', '60', '5')?></td>
              				</tr>
            			</table-->
            		</td>
          		</tr>
        	</table>
        </td>
	</tr>
    <tr>
    	<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
    </tr>
<?
}
?>
	<tr>
		<td>
			<table border="0" width="100%" cellspacing="1" cellpadding="2" class="buttonBox">
	        	<tr class="buttonBoxContents">
	            	<td>
	            		<table border="0" width="100%" cellspacing="0" cellpadding="2">
	              			<tr>
	                			<td width="10"><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
	                			<td class="main"><?//'<b>' . TITLE_CONTINUE_CHECKOUT_PROCEDURE . '</b><br>' . TEXT_CONTINUE_CHECKOUT_PROCEDURE?></td>
	                			<td class="main" align="right"><?=tep_image_submit(THEMA.'button_continue.gif', IMAGE_BUTTON_CONTINUE)?></td>
	                			<td width="10"><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
	              			</tr>
	            		</table>
	            	</td>
	          	</tr>
	          	<?=$shipping_modules->javascript_validation()?>
	        </table>
		</td>
	</tr>
</table>
</form>