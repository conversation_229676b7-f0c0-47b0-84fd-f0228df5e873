<? echo tep_draw_form('checkout_address', tep_href_link(FILENAME_CHECKOUT_SHIPPING_ADDRESS, '', 'SSL'), 'post', 'onSubmit="return check_form_optional(checkout_address);"'); ?>
<table border="0" width="100%" cellspacing="0" cellpadding="0">
	<tr>
		<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
	<tr>
		<td class="pageHeadingTitle" valign="middle" align="center"><?=HEADING_TITLE?></td>
	</tr>
	<tr>
		<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
	<tr>
		<td>
			<div class="row_separator"></div>
		</td>
	</tr>
	<tr>
		<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
<?
if ($messageStack->size('checkout_address') > 0) {
?>
	<tr>
    	<td><?=$messageStack->output('checkout_address')?></td>
	</tr>
    <tr>
        <td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
    </tr>
<?
}
?>
    <tr>
    	<td>
    		<table border="0" width="100%" cellspacing="0" cellpadding="0">
          		<tr>
            		<td width="25%">
            			<table border="0" width="100%" cellspacing="0" cellpadding="0">
              				<tr>
                				<td width="50%" align="right"><?=tep_image(THEMA_IMAGES . 'checkout_bullet.gif')?></td>
                				<td width="50%"><?=tep_draw_separator('pixel_silver.gif', '100%', '1')?></td>
              				</tr>
            			</table>
            		</td>
            		<td width="25%"><?=tep_draw_separator('pixel_silver.gif', '100%', '1')?></td>
            		<td width="25%"><?=tep_draw_separator('pixel_silver.gif', '100%', '1')?></td>
            		<td width="25%">
            			<table border="0" width="100%" cellspacing="0" cellpadding="0">
              				<tr>
                				<td width="50%"><?=tep_draw_separator('pixel_silver.gif', '100%', '1')?></td>
                				<td width="50%"><?=tep_draw_separator('pixel_silver.gif', '1', '5')?></td>
              				</tr>
            			</table>
            		</td>
          		</tr>
          		<tr>
            		<td align="center" width="25%" class="checkoutBarCurrent"><?=CHECKOUT_BAR_DELIVERY?></td>
            		<td align="center" width="25%" class="checkoutBarTo"><?=CHECKOUT_BAR_PAYMENT?></td>
            		<td align="center" width="25%" class="checkoutBarTo"><?=CHECKOUT_BAR_CONFIRMATION?></td>
            		<td align="center" width="25%" class="checkoutBarTo"><?=CHECKOUT_BAR_FINISHED?></td>
          		</tr>
        	</table>
        </td>
	</tr>
	<tr>
    	<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
  	</tr>
<?
if ($process == false) {
?>
	<tr>
    	<td>
    		<table border="0" width="100%" cellspacing="0" cellpadding="2">
          		<tr>
            		<td class="shippingBoxHeading"><?=TABLE_HEADING_SHIPPING_ADDRESS?></td>
          		</tr>
        	</table>
        </td>
	</tr>
    <tr>
    	<td>
    		<table border="0" width="100%" cellspacing="1" cellpadding="2" class="infoBox">
          		<tr class="infoBoxContents">
            		<td>
            			<table border="0" width="100%" cellspacing="0" cellpadding="2">
	          				<tr>
	          					<td><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td> 
	                			<td width="100%" class="spacingInstruction" align="left" valign="top"><?=TEXT_SELECTED_SHIPPING_DESTINATION?></td>
	                			<td><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td> 
	                		</tr>
	                		<tr>
	                			<td><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td> 
	            				<td width="100%" class="main" align="left" valign="top">
				           			<table border="0" width="100%" cellspacing="0" cellpadding="2">
				           				<tr>
				           					<td class="shippingAddressTitle" align="left" valign="top"><?='<b>' . TITLE_SHIPPING_ADDRESS . '</b>'?></td>
				     					</tr>
				               			<tr>
				                  			<td class="shippingAddress" align="left" valign="top"><?=tep_address_label($customer_id, $sendto, true, ' ', '<br>')?></td>
				               			</tr>
				           			</table>
				           		</td>
				           		<td><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td> 
				       		</tr>
						</table>
            		</td>
          		</tr>
        	</table>
        </td>
	</tr>
    <tr>
    	<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
<?
	if ($addresses_count > 1) {
?>
	<tr>
    	<td>
    		<table border="0" width="100%" cellspacing="0" cellpadding="2">
          		<tr>
            		<td class="shippingBoxHeading"><?=TABLE_HEADING_ADDRESS_BOOK_ENTRIES?></td>
          		</tr>
        	</table>
        </td>
	</tr>
    <tr>
    	<td>
    		<table border="0" width="100%" cellspacing="1" cellpadding="2" class="infoBox">
          		<tr class="infoBoxContents">
            		<td>
            			<table border="0" width="100%" cellspacing="0" cellpadding="2">
              				<tr>
                				<td><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
                				<td class="spacingInstruction" valign="top"><?=TEXT_SELECT_OTHER_SHIPPING_DESTINATION?></td>
                				<td><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
              				</tr>
<?
    	$radio_buttons = 0;
		
    	$addresses_query = tep_db_query("select address_book_id, entry_firstname as firstname, entry_lastname as lastname, entry_company as company, entry_street_address as street_address, entry_suburb as suburb, entry_city as city, entry_postcode as postcode, entry_state as state, entry_zone_id as zone_id, entry_country_id as country_id from " . TABLE_ADDRESS_BOOK . " where customers_id = '" . (int)$customer_id . "'");
		while ($addresses = tep_db_fetch_array($addresses_query)) {
			$format_id = tep_get_address_format_id($addresses['country_id']);
?>
              				<tr>
                				<td><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
                				<td colspan="2">
                					<table border="0" width="100%" cellspacing="0" cellpadding="2">
<?
			if ($addresses['address_book_id'] == $sendto) {
          		echo '            		<tr id="defaultSelected" class="moduleRowSelected" onmouseover="rowOverEffect(this)" onmouseout="rowOutEffect(this)" onclick="selectRowEffect(this, ' . $radio_buttons . ')">' . "\n";
        	} else {
          		echo '             		<tr class="moduleRow" onmouseover="rowOverEffect(this)" onmouseout="rowOutEffect(this)" onclick="selectRowEffect(this, ' . $radio_buttons . ')">' . "\n";
        	}
?>
                    						<td width="10"><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
                    						<td class="shippingAddressTitle" colspan="2"><b><?=tep_output_string_protected($addresses['firstname'] . ' ' . $addresses['lastname'])?></b></td>
                    						<td class="inputField" align="right"><?=tep_draw_radio_field('address', $addresses['address_book_id'], ($addresses['address_book_id'] == $sendto))?></td>
                    						<td width="10"><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
                  						</tr>
                  						<tr>
                    						<td width="10"><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
                   	 						<td colspan="3">
                   	 							<table border="0" cellspacing="0" cellpadding="2">
                      								<tr>
                        								<td width="10"><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
                        								<td class="shippingAddress"><?=tep_address_format($format_id, $addresses, true, ' ', ', ')?></td>
                        								<td width="10"><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
                      								</tr>
                    							</table>
                    						</td>
                    						<td width="10"><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
                  						</tr>
                					</table>
                				</td>
                				<td><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
              				</tr>
<?        	$radio_buttons++;
		}
?>
            			</table>
            		</td>
          		</tr>
        	</table>
        </td>
	</tr>
    <tr>
    	<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
<?
	}
}

if ($addresses_count < MAX_ADDRESS_BOOK_ENTRIES) {
?>
	<tr>
    	<td>
    		<table border="0" width="100%" cellspacing="0" cellpadding="2">
          		<tr>
            		<td class="shippingBoxHeading"><?=TABLE_HEADING_NEW_SHIPPING_ADDRESS?></td>
          		</tr>
        	</table>
        </td>
	</tr>
    <tr>
    	<td>
    		<table border="0" width="100%" cellspacing="1" cellpadding="2" class="infoBox">
          		<tr class="infoBoxContents">
            		<td>
            			<table border="0" width="100%" cellspacing="0" cellpadding="2">
              				<tr>
                				<td><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
                				<td class="spacingInstruction" width="100%" valign="top"><?=TEXT_CREATE_NEW_SHIPPING_ADDRESS?></td>
                				<td><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
              				</tr>
              				<tr>
                				<td><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
                				<td>
                					<table border="0" width="100%" cellspacing="0" cellpadding="2">
                  						<tr>
                    						<td width="10"><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
                    						<td><? require(DIR_WS_MODULES . 'checkout_new_address.php'); ?></td>
                    						<td width="10"><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
                  						</tr>
                					</table>
                				</td>
                				<td><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
              				</tr>
            			</table>
            		</td>
          		</tr>
        	</table>
        </td>
	</tr>
<?
}
?>
	<tr>
    	<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
    <tr>
    	<td>
    		<table border="0" width="100%" cellspacing="1" cellpadding="2" class="buttonBox">
          		<tr class="buttonBoxContents">
            		<td>
            			<table border="0" width="100%" cellspacing="0" cellpadding="2">
              				<tr>
              					<td width="10"><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
<?
if ($process == true) {
	echo '						<td>'.tep_div_button(2, IMAGE_BUTTON_BACK,tep_href_link(FILENAME_CHECKOUT_SHIPPING_ADDRESS, '', 'SSL'), '', 'gray_button').'</td>';
}
?>
                				<td><? //echo '<b>' . TITLE_CONTINUE_CHECKOUT_PROCEDURE . '</b><br>' . TEXT_CONTINUE_CHECKOUT_PROCEDURE; ?></td>
                				<td align="right">
									<?=tep_draw_hidden_field('action', 'submit') ?>
									<?=tep_div_button(1, IMAGE_BUTTON_CONTINUE,'checkout_address', 'style="float:right"', 'gray_button') ?>
								</td>
                				<td width="10"><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
              				</tr>
            			</table>
            		</td>
          		</tr>
        	</table>
        </td>
	</tr>
</table>
</form>