<?
$counter_down_start_frm = 200;
echo $sales_tracking_js;

?>
<style>
    #chk_o_status {padding-left:80px}
    #chk_o_status .icon, #chk_o_status .txt, #chk_o_status .txt2 {float: left;}
    #chk_o_status .txt {padding-top: 5px;padding-left: 5px;}
    #chk_o_status .txt2 {padding-left: 5px;}
    #chk_o_status div.txt2>span>span.num {color:red;font-size:18px;font-size:22px;font-weight:bold;}
    #view_o .txt {float: left;padding-top: 10px;padding-right: 15px;text-align: right;width: 470px;}
    #view_o .cs_btn {float:left;}
    .lyr1_ctn {padding: 0 20px;}
</style>
<script language="JavaScript" type="text/javascript">
    function chk_status(s){
        var i=s%20;
        if (i==0){
            jQuery.ajax({
                type: "POST",
                url: 'checkout_jsonhttp.php?action=chk_order_status',
                data: 'oid=<?=$orders['orders_id']?>',
                async: true,
                dataType: "json",
                beforeSend:  function() {},
                error: function(){
                    return_obj.err = true;
                    alert('Please try again.');
                },
                success: function(data){
                    if(data.status!=undefined){
                        if(data.status){jQuery('#chk_o_status').hide();jQuery('#view_o').show();}
                    }
                }
            });
        }
    }
    
    function cdown(){
        if(jQuery('#chk_o_status').is(":visible")){
            var s_obj=jQuery("#chk_o_status>div.txt2>span>span.num"),
                secs = parseInt(s_obj.html(),10)-1;
            chk_status(secs);
            if (secs <= 0){
                s_obj.html('<?=$counter_down_start_frm?>');
            } else {
                s_obj.html(secs);
            }
        }
    }
    
    jQuery(document).ready(function(){
        if (jQuery('#chk_o_status').is(":visible")){
            setInterval("cdown()", 1000);
        }
    });
</script>
<div class="vspacing"></div>
<?php
	/*-- Bizrate Survey [start] --*/
	if ((int)$orders['payment_methods_parent_id'] != 12) {
		if (defined('BIZRATE_SURVEY_ID') && tep_not_null(BIZRATE_SURVEY_ID) && file_exists(DIR_WS_INCLUDES . 'javascript/bizrate.js.php')) { 
			$customers_groups_id = tep_get_customers_groups_id($customer_id);
			if ($customers_groups_id != 1 && $customers_groups_id != 6 && $customers_groups_id != 8) {
				foreach($order->totals as $order_key => $order_value) {
					if ($order_value['class'] == 'ot_subtotal') { // subtotal
						if ($order_value['value'] > 0) {
							include_once(DIR_WS_INCLUDES . 'javascript/bizrate.js.php'); 
						}
						break;
					}
				}
			}
		}
	}
	/*-- Bizrate Survey [end] --*/
?>
<script language="javascript" src="<?=DIR_WS_JAVASCRIPT?>customer_xmlhttp.js"></script>
<?
if ($messageStack->size('checkout_success') > 0) {
?>
	<div class="loginColumnBorder" style="background-color: #FFFDDB; width: 98%;">
		<div style="padding:10px;"><?=$messageStack->output('checkout_success'); ?></div>
	</div>
	<div class="breakLine"><!-- --></div>
<?
}

ob_start();
?>
    <table border="0" width="100%" cellspacing="0" cellpadding="0" align="center">
        <tr>
            <td class="pageSectionHeadingTitle">
                <table border="0" cellpadding="0" cellspacing="0" width="100%" style="padding: 30px 30px 10px">
                    <tr>
                        <td style="width: 695px;">
                            <div id="chk_o_status" style="display:none;">
                                <div class="icon"><?=tep_image(DIR_WS_IMAGES . 'successful.gif', '', 54, 56)?></div>
                                <div class="txt"><span class="hd1" style="font-size:18px"><?=sprintf(TEXT_CHECKOUT_SUCCESS_HEADING_MSG, $orders['orders_id'])?></span></div>
                                <div class="txt2"><span class="hd5" style="font-size:14px"><?=sprintf(TEXT_CHECKOUT_SUCCESS_DESC_MSG, $counter_down_start_frm)?></div>
                                <div class="clrFx"></div>
                            </div>
                            <div id="view_o">
                                <div class="txt">
                                    <span class="hd1" style="font-size:18px"><?=TEXT_CHECKOUT_SUCCESS_HEADING_MSG2?></span>
                                    <br>
                                    <span class="hd5" style="font-size:14px"><?=sprintf(TEXT_CHECKOUT_SUCCESS_DESC_MSG2, $orders['orders_id'])?></span>
                                </div>
                                <div class="cs_btn"><?=tep_image_button2('gray_big_tall',tep_href_link(FILENAME_ACCOUNT_HISTORY_INFO, 'order_id='.$orders['orders_id'], 'SSL'), BUTTON_VIEW_ORDER, 180) ?></div>
                                <div class="clrFx"></div>
                            </div>
                        </td>
                        <td style="border-left: 1px dotted #CCCCCC;padding: 15px 0 15px 25px;">
                                <table border="0" cellpadding="1" cellspacing="0" width="90%">
                                    <tr>
                                        <td colspan="2"><?=TEXT_ORDER_SUMMARY;?></td>
                                    </tr>
<?
    foreach($order->totals as $order_key => $order_value) {
?>
                                    <tr>
                                        <td align="center">
<?
        //to support multiple language 
        if ($order_value['class'] == 'ot_subtotal' ){//display subtotal
            echo TEXT_SUB_TOTAL;
        } else if ($order_value['class'] == 'ot_coupon' ){//display discount coupon
            echo TEXT_DISCOUNT_COUPONS;
        } else if ($order_value['class'] == 'ot_gv' ){//display store credit
            echo TEXT_STORE_CREDIT;
        } else if ($order_value['class'] == 'ot_surcharge' ){//display surcharge
            echo TEXT_SURCHARGE;
        } else if ($order_value['class'] == 'ot_total' ){//display total
            echo TEXT_TOTAL;
        } else if ($order_value['class'] == 'ot_gst' ) { // display GST
            echo $order_value['title'];
        }
?>
                                        </td>
                                        <td align="center">
<?
        if ($order_value['class'] != 'ot_total'){ //display figure
            echo strip_tags($order_value['text']);
        } else {
            echo $order_value['text'];
        }
?>
                                        </td>
                                    </tr>
<?
    }
    
    $product_id_array = array();
    $categories_name = array();//prepare category name for facebook + twitter.

    foreach($order->products as $products_key => $products_value){//get all purchases made by this customers
        $product_id_array[] = $products_value['id'];
        $categories_name[] = $products_value['name'] . ' (' . tep_get_categories_name($products_value['products_categories_id']) . ')';
    }

    $category_product_name = implode(" , ",$categories_name);
?>
                                </table>
                        </td>
                    </tr>
                    <tr style="height: 100px; vertical-align: bottom;">
                        <td style="text-align: center;">
                            <table border="0" cellpadding="1" cellspacing="0" width="100%" style="display: inline;">
                                <tr>
                                    <td nowrap style="padding:0px 10px"><a href="<?=tep_href_link(FILENAME_ACCOUNT_STORE_CREDIT_HISTORY)?>"><?=TEXT_CHECK_STORE_CREDIT_LINK?></a></td>
                                    <td width="1px">|</td>
                                    <td nowrap style="padding:0px 10px"><a href="<?=tep_href_link(FILENAME_REDEEM_POINT)?>"><?=TEXT_CHECK_OFFGAMERS_POINT_LINK?></a></td>
                                    <td width="1px">|</td>
                                    <td nowrap style="padding:0px 10px"><a href="<?=tep_href_link(FILENAME_CUSTOMER_SUPPORT)?>"><?=TEXT_LIVE_CHAT_LINK?></a></td>
                                    <td width="1px">|</td>
                                    <td nowrap style="padding:0px 10px">
                                        <?=TEXT_CHECK_SHARE_WITH_FRIENDS_LINK?>
                                        <a href="http://facebook.com/sharer.php?u=http://offgamers.com&amp;t=I%20just%20bought%20<?=stripcslashes(strip_tags($category_product_name))?>%20from%20www.OffGamers.com%21" target="_blank"><span style="font-size:30px;"><?=tep_image(DIR_WS_ICONS.'facebook_16.png', '', '12', '12', ''); ?></span></a>
                                        <a href="http://twitter.com/home?status=I%20just%20bought%20<?=stripcslashes(strip_tags($category_product_name))?>%20from%20www.OffGamers.com%21" target="_blank"><span style="font-size:30px;"><?=tep_image(DIR_WS_ICONS.'twitter_16.png', '', '12', '12', '');?></span></a>
                                    </td>
                                </tr>
                            </table>
                        </td>
                        <td></td>
                    </tr>
                </table>
            </td>
        </tr>
        <tr>
            <td><div class="row_separator"></div><br></td>
        </tr>
<?php
    if (is_object($module)) {
?>
        <tr>
            <td class="instruction"><?php echo nl2br($module->message); ?></td>
        </tr>
<?  } ?>
        <tr>
            <td class="instruction" style="padding-left:30px">
<?
                $product_delivery_msg_array = array();
                //mesages will be displayed based on user's purchase.
                foreach ($order->products as $order_product_info) {
                    $product_delivery_msg_array[$order_product_info['custom_products_type_id']] = $custom_prd_type_msg_array[$order_product_info['custom_products_type_id']];
                }
                echo implode('<br>', $product_delivery_msg_array);
?>
            </td>
        </tr>
<?
    if ($do_phone_verification || $do_email_verification) {
        if ($do_email_verification) {
?>
        <tr>
            <td><br /><div class="row_separator"></div><br /></td>
        </tr>
        <tr>
            <td>
                <table border="0" cellpadding="0" cellspacing="0" width="100%" class="lyr1_ctn">
                    <tr>
                        <td rowspan="5" valign="top"><?=tep_image(DIR_WS_ICONS . "warning.png")?></td>
                        <td><b><?=HEADING_EMAIL_VERIFICATION_TITLE?></b></td>
                    </tr>
                    <tr><td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td></tr>
                    <tr class="inputBoxContents">
                        <td class="instruction">
                            <span id="verify_email">
                                <?=TEXT_TITLE_CONFIRM_EMAIL?>
                                <a href="http://kb.offgamers.com/<?=strtolower(str_replace('-', '', $language_code))?>/?p=<?php echo ($language_code == 'en' ? 93 : 131);?>"><span style="font-size:30px;"><?=tep_image(DIR_WS_ICONS.'help_info.gif', '', '12', '12', ''); ?></span></a>
                            </span>
                        </td>
                    </tr>
                    <tr><td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td></tr>
                    <tr>
                        <td colspan="2">
                            <?=tep_draw_form('email_verify_form', tep_href_link(FILENAME_CHECKOUT_SUCCESS), 'post', 'NONSSL')?>
                                <div id="js_email_first_location_mode">
                                    <table border="0" cellpadding="0" cellspacing="0" width="100%">
                                        <tr>
                                            <td class="inputLabel" width="17%" nowrap><?=ENTRY_EMAIL_ADDRESS?></td>
                                            <td class="inputField" colspan="3"><b><?=$info_verified_row['customers_email_address']?></b></td>
                                        </tr>
                                        <tr>
                                            <td class="inputLabel" nowrap><?=ENTRY_CHECKOUT_SUCCESS_VERIFICATION_CODE?></td>
                                            <td class="inputField" width="20%"><?=tep_draw_input_field('verification_code', '', 'size="43" onkeydown="if ((event.which && event.which == 13) || (event.keyCode && event.keyCode == 13)) {document.email_verify_form.NonJSEConfirmEmail.click();return false;}else return true;"', 'text', false)?></td>
                                            <td align="left" width="15%" style="padding-left:10px"><?=tep_image_button2('gray_short', 'javascript:document.email_verify_form.submit();', IMAGE_BUTTON_CONFIRM_CODE, ' name="NonJSEConfirmEmail" onclick="verifyEmail(document.email_verify_form.verification_code.value, \''.ERROR_EMAIL_VERIFICATION_CODE.'\'); return false"')?></td>
                                            <td align="left"><div id="email_verify_div">&nbsp;</div></td>
                                        </tr>
                                        <tr>
                                            <td colspan="4"><?=sprintf(TEXT_RESEND_VERIFICATION_CODE, tep_href_link(FILENAME_CHECKOUT_SUCCESS, 'action=resend'))?></td>
                                        </tr>
                                    </table>
                                </div>
                            </form>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
<?
        }
        
        if ($do_phone_verification) {
?>
        <tr>
            <td><?=tep_draw_separator('pixel_trans.gif', '100%', '20')?></td>
        </tr>
        <tr>
            <td><div class="row_separator"></div><br></td>
        </tr>
        <tr>
            <td>
                <table border="0" width="100%" cellspacing="0" cellpadding="0" class="lyr1_ctn">
                    <tr>
                        <td rowspan="8" valign="top"><?=tep_image(DIR_WS_ICONS . "warning.png")?></td>
                        <td><b><?=HEADING_PHONE_VERIFICATION_TITLE?></b></td>
                    </tr>
                    <tr><td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td></tr>
                    <tr>
                        <td class="instruction"><span id="verify_message"><span></td>
                    </tr>
                    <? if ($verify_mode == 0 || $verify_mode == 1 || $verify_mode == 2) {?>
                            <tr>
                                <td><?=TEXT_TITLE_ENTER_PHONE_NUMBER?><a href="http://kb.offgamers.com/<?=strtolower(str_replace('-', '', $language_code))?>/?p=<?php echo ($language_code == 'en' ? 1982 : 135);?>"><span style="font-size:30px;"><?=tep_image(DIR_WS_ICONS.'help_info.gif', '', '12', '12', ''); ?></span></a></td>
                            </tr>
                    <? } else if ($verify_mode == 3) {?>
                            <tr>
                                <td><?=TEXT_TITLE_ENTER_PHONE_VERIFICATION_CODE?></td>
                            </tr>
                    <?	} ?>
                    <tr><td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td></tr>
                    <tr>        
                        <td> 
                            <?=tep_draw_form('phone_verify_form', tep_href_link(FILENAME_CHECKOUT_SUCCESS), 'post', 'NONSSL')?>
                            <div id="js_first_telephone_mode">
                                <table border="0" cellpadding="0" cellspacing="0" width="100%">
                                    <tr>
                                        <td class="inputLabel" width="20%" align="left"><?=ENTRY_COUNTRY_CODE?></td>
                                        <td align="left" colspan="2" class="inputField">
                                        <div id="dialing_phone_country">
<?												
            if ($verify_mode == 0) {
                if ($phone_verify_mode == 'xmlhttp_mode') {
                    if (tep_not_null($order->customer['telephone_country'])) {
                        echo (tep_country_maxmind_support($order->customer['order_country_code']) == 0 ? '&nbsp;&nbsp;' . TEXT_COUNTRY_NOT_SUPPORT : $order->customer['telephone_country'] . '&nbsp;(+' . $order->customer['order_country_code'] . ')');
                    }
                }
            } else if ($verify_mode == 1 || $verify_mode == 2 || $verify_mode == 3) {
                if(tep_country_maxmind_support($customer_complete_phone_info_array['country_international_dialing_code']) == 0) {
                    echo TEXT_COUNTRY_NOT_SUPPORT;
                }
            }
?>
                                        </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="inputLabel" width="20%" align="left"><?=ENTRY_TELEPHONE_NUMBER?></td>
                                        <td align="left" class="inputField">
                                            <div id="dialing_phone_number" style="display:inline; float:left;">
<?					
		if (tep_not_null($order->customer['telephone'])) {
			echo substr_replace($order->customer['telephone'], '****', -4);
		} else {
			echo tep_draw_input_field('telephone', substr_replace($order->customer['telephone'], '****', -4), 'id="telephone" size="15" onkeydown="if ((event.which && event.which == 13) || (event.keyCode && event.keyCode == 13)){document.link_edit_phone_number.onclick();}"', 'text', false) . '&nbsp;' . (tep_not_null(ENTRY_TELEPHONE_NUMBER_TEXT) ? '<span class="requiredInfo">' . ENTRY_TELEPHONE_NUMBER_TEXT . '</span>': '');
		}
?>					
                                            </div>&nbsp;&nbsp;&nbsp;<a id="link_edit_phone_number" href="<?=tep_href_link(FILENAME_ACCOUNT_EDIT, 'oid=' . $orders['orders_id'] . '&trace=chkout_succ')?>"><?=BUTTON_EDIT?></a>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '100%', '20')?></td>
                                    </tr>
                                    <tr>
                                        <td class="inputField" nowrap ><?=tep_draw_hidden_field('order_id', $orders['orders_id'], 'id="order_id"');?></td>
                                        <td class="inputLabel" width="60%" align="left">
                                            <div id="js_telephone_update_button_div">
<?
		if (tep_not_null($order->customer['telephone']) && tep_not_null($order->customer['telephone_country'])) {
			$phone_verification_obj->get_call_button($orders['orders_id'], 'gray');
		}
?>
                                            </div>
                                        </td>
                                    </tr>		
                                </table>
                                <table border="0" cellpadding="0" cellspacing="0" width="100%">
                                    <tr>
                                        <td>
                                            <div id="div_verification_table"></div><!-- display confirm code -->
                                        </td>
                                    </tr>
                                </table>
                            </div>
                            </form>
                        </td>
                    </tr>
                    <tr>
                        <td><?=tep_draw_separator('pixel_trans.gif', '100%', '20')?></td>
                    </tr>
                    <tr>
                        <td><?=tep_image(DIR_WS_ICONS . 'note.gif') . ' &nbsp ' . TEXT_SMS_NOTES?></td>
                    </tr>
                </table>
		<script language="javascript">
			var ord_id = "<?=$order->order_id?>";
			
			function verifyTelephone(code) {
				if (code == ''){//check if its empty
					alert('Please enter your telephone verification code.');
					return false;
				}
				var server_action = 'myaccount_confirm_telephone';
				var page = '<?=CONTENT_CHECKOUT_SUCCESS?>';
				var ref_url = "customer_xmlhttp.php?action="+server_action+"&code="+code+"&order_id="+ord_id+"&page="+page;
				
				jQuery.get(ref_url, function(xml){
					jQuery(xml).find('response').each(function(){
						alert(jQuery("result", this).text());
						if (jQuery("goto_url", this).text()!='') {
							window.location = jQuery("goto_url", this).text();
						}
					})
				});
			}
			
			function confirm_code(pass_order_id, verify_type){
				pass_order_id = pass_order_id || '';
				var server_action = "myaccount_verify_phone";
				var call_language = jQuery("#call_language").val() || '';
				var submit_url = "customer_xmlhttp.php?action=" + server_action + "&call_language=" + call_language;
				
                if (pass_order_id!='') {
					submit_url += "&order_id=" + pass_order_id;
				}
				jQuery("#button_text_me_now a").attr("href","javascript:void(0);");
			    jQuery.get(submit_url, function(xml){
                    if (jQuery(xml).find("error").length > 0){
                        alert(jQuery(xml).find('error').text());
                    }else{
                       jQuery(xml).find('response').each(function(){ 
                            jQuery("#button_text_me_now").hide(); 
                            if (jQuery("verify_code_table", this).text() != ''){ //remove edit button while call me
                                jQuery("#js_telephone_update_button_div").hide();
                            }
                            document.getElementById("div_verification_table").innerHTML = jQuery("verify_code_table", this).text();
                        });
                        jQuery("#button_text_me_now").attr("href","javascript:confirm_code('"+pass_order_id+"');");
                    }
                });
			}
		</script>
                        </td> 
                    </tr>
<?		
	}
}   // END verification
?>
				
				<tr>
					<td><?=tep_draw_separator('pixel_trans.gif', '100%', '20')?></td>
				</tr>
				<tr>
					<td>
<?
if ($verify_mode == 3) {
	echo '<a href="'.tep_href_link(FILENAME_CHECKOUT_SUCCESS, 'action=report').'">' . ERROR_REPORT .'</a>';
}
?>
					</td>
				</tr>
				<tr>
					<td><?=tep_draw_separator('pixel_trans.gif', '100%', '1')?></td>
				</tr>
<?
if (tep_not_null(GOOGLE_ADWORDS_ACCOUNT_ID)) {
?>				
				<tr>
					<td align="right">
						<script language="JavaScript" type="text/javascript">
						<!--
						var Total_Cost = '<?=$orders["value"]?>'
						//-->
						</script>
						<!-- Google Code for Purchase Conversion Page -->
						<script language="JavaScript" type="text/javascript">
						<!--
						var google_conversion_id = <?=GOOGLE_ADWORDS_ACCOUNT_ID?>;
						var google_conversion_language = "en_US";
						var google_conversion_format = "2";
						var google_conversion_color = "333366";
						if (Total_Cost) {
						  var google_conversion_value = Total_Cost;
						}
						var google_conversion_label = "Purchase";
						//-->
						</script>
						<script language="JavaScript" src="http://www.googleadservices.com/pagead/conversion.js">
						</script>
						<noscript>
						<img height=1 width=1 border=0 src="http://www.googleadservices.com/pagead/conversion/**********/?value=Total_Cost&label=Purchase&script=0">
						</noscript>
						
						<!-- Google Code for Sales Tracking Conversion Page -->
						<script type="text/javascript">
						<!--
						var google_conversion_id = **********;
						var google_conversion_language = "en";
						var google_conversion_format = "2";
						var google_conversion_color = "ffffff";
						var google_conversion_label = "-kuPCPXuqQEQsfCg7AM";
						if (Total_Cost) {
						  var google_conversion_value = Total_Cost;
						}
						//-->
						</script>
						<script type="text/javascript" src="http://www.googleadservices.com/pagead/conversion.js">
						</script>
						<noscript>
						<div style="display:inline;">
						<img height="1" width="1" style="border-style:none;" alt="" src="http://www.googleadservices.com/pagead/conversion/**********/?label=-kuPCPXuqQEQsfCg7AM&amp;guid=ON&amp;script=0"/>
						</div>
						</noscript>
						
						<!-- Google Code for sales Conversion Page -->
						<script type="text/javascript">
						<!--
						var google_conversion_id = 1023875421;
						var google_conversion_language = "en";
						var google_conversion_format = "3";
						var google_conversion_color = "ffffff";
						var google_conversion_label = "SzeJCOutrQEQ3bKc6AM";
						if (Total_Cost) {
						  var google_conversion_value = Total_Cost;
						}
						//-->
						</script>
						<script type="text/javascript" src="http://www.googleadservices.com/pagead/conversion.js"></script>
						<noscript><div style="display:inline;"><img height="1" width="1" style="border-style:none;" alt="" src="http://www.googleadservices.com/pagead/conversion/1023875421/?label=SzeJCOutrQEQ3bKc6AM&amp;guid=ON&amp;script=0"/></div></noscript>
						
						<!-- Google Code for Sales Conversion Page -->
						<script type="text/javascript">
						<!--
						var google_conversion_id = 1018437078;
						var google_conversion_language = "en";
						var google_conversion_format = "2";
						var google_conversion_color = "ffffff";
						var google_conversion_label = "FhclCJqSywEQ1rvQ5QM";
						if (Total_Cost) {
						  var google_conversion_value = Total_Cost;
						}
						//-->
						</script>
						<script type="text/javascript" src="http://www.googleadservices.com/pagead/conversion.js">
						</script>
						<noscript><div style="display:inline;"><img height="1" width="1" style="border-style:none;" alt="" src="http://www.googleadservices.com/pagead/conversion/1018437078/?label=FhclCJqSywEQ1rvQ5QM&amp;guid=ON&amp;script=0"/></div></noscript>
						
						<!-- Google Code for Sales Conversion Page -->
						<script type="text/javascript">
						<!--
						var google_conversion_id = **********;
						var google_conversion_language = "en";
						var google_conversion_format = "2";
						var google_conversion_color = "ffffff";
						var google_conversion_label = "gq0fCNHY4AEQr-nk5gM";
						if (Total_Cost) {
						  var google_conversion_value = Total_Cost;
						}
						-->
						</script>
						<script type="text/javascript" src="http://www.googleadservices.com/pagead/conversion.js">
						</script>
						<noscript><div style="display:inline;"><img height="1" width="1" style="border-style:none;" alt="" src="http://www.googleadservices.com/pagead/conversion/**********/?label=gq0fCNHY4AEQr-nk5gM&amp;guid=ON&amp;script=0"/></div></noscript>
					</td>
				</tr>
<?
}

if (tep_not_null(ADBRITE_ACCOUNT_ID)) {
?>
				<tr>
					<td align="right" >
						<!-- begin AdBrite, Purchases/sales tracking --><img border="0" hspace="0" vspace="0" width="1" height="1" src="http://stats.adbrite.com/stats/stats.gif?_uid=<?=ADBRITE_ACCOUNT_ID?>&_pid=0" /><!-- end AdBrite, Purchases/sales tracking -->
					</td>
				</tr>
<?
}

if (DOWNLOAD_ENABLED == 'true') {
    include(DIR_WS_MODULES . 'downloads.php'); 
}
?>
			</table>
<?
$chkout_success_content_string = ob_get_contents();
ob_end_clean(); 

echo $page_obj->get_html_simple_rc_box('',$chkout_success_content_string,13); 

//----------------------------------------------------------------------------------------------------------------------------- START GET TOP 3 DIFFERENT PRODUCTS
$other_products_id_array = $order->get_recommend_product($product_id_array);
$top_3_diff_pro_type_array = array();
$top_product_array = array();
$product_1_type = array();//%%(STEP 1)%%

if (count($other_products_id_array) > 0 ) {// check if it is not null 
    foreach ($other_products_id_array as $other_products_id_key => $other_products_id_value) {
        $top_product_array[] = array('products_id' => $other_products_id_key, 'customers_count' => $other_products_id_value, 'products_type' => tep_get_custom_product_type($other_products_id_key));
    }
}

if (count($top_product_array) > 0) { //determine the product type for each product id
    foreach($top_product_array as $top_product_val) {
        if ($top_product_val['products_type'] == '0'){//gold
            $product_1_type[$top_product_val['products_id']] = $top_product_val['products_type'];
        }
        if ($top_product_val['products_type'] == '1'){//pwl
            $product_1_type[$top_product_val['products_id']] = $top_product_val['products_type'];
        }
        if ($top_product_val['products_type'] == '2'){//cd key
            $product_1_type[$top_product_val['products_id']] = $top_product_val['products_type'];
        }
    }
}

if(count($product_1_type) > 0) {
    $prod_type_array = array('0','1','2');//%%(STEP 2)%%
    $temp_prod_type_exist_array = array();
    $product_type_not_found = 0;

    foreach($prod_type_array as $type){//check the product type in the array
        $top_product_id_by_type = array_search($type, $product_1_type);

        if ($top_product_id_by_type !== FALSE) {//the product type exist
            $temp_prod_type_exist_array[] = $top_product_id_by_type; 
        } else {//the product type do not exits
            $product_type_not_found++;
        }
    }

    $replacement_product_type = array();//%%(STEP 3)%%
    foreach ($product_1_type as $product_1_type_key => $product_1_type_value){
        if(!in_array($product_1_type_key, $temp_prod_type_exist_array)) { //do not take those that hav been taken in top 3 list
            if (count($replacement_product_type) < $product_type_not_found) { //get the # of record to be replace 
                $replacement_product_type[] = $product_1_type_key;
            } else {
                break;
            }
        }
    }

    $top_3_diff_pro_type_array	= array_merge($temp_prod_type_exist_array, $replacement_product_type);
    unset($temp_prod_type_exist_array, $replacement_product_type);
}

$track_count = 0; //for seperator
if (count($top_3_diff_pro_type_array) > 0) {
?>
    <div class="vspacing"></div>
<?
    ob_start();
?>
<table class="sub_Category_Top3Box" border="0" cellpadding="0" cellspacing="0" width="98%">
	<tr>
		<td>
			<div class="loginColumnBorder" style="width:100%"><!-- outer top 3 box -->
			<table border="0" cellpadding="24" cellspacing="0" width="100%">
				<tr>
					<td>
						<table border="0" cellpadding="0" cellspacing="0" width="100%">
							<tr>
								<td><?=TEXT_SELECTION_BASED_RECOMENDATION?></td>
								<td><?=tep_draw_separator('pixel_trans.gif', '100%', '16')?></td>
							</tr>
						</table>
						<table border="0" cellpadding="0" cellspacing="0" width="100%">
							<tr>
<?						
    $count = 0;
    foreach ($top_3_diff_pro_type_array as $idx => $top_3_diff_pro_type_value) {
        $products_id = $top_3_diff_pro_type_value;
        $products_type = $product_1_type[$products_id];
        
        if ($products_type == '2') {
            $class_display = 'cdkeyImageDisplay';
        } else if ($products_type == '0') {
            $class_display = 'goldImageDisplay';
        } else if ($products_type == '1') {
            $class_display = 'pwlImageDisplay';
        }
        
        $products_cat_id_path_select = "SELECT products_cat_id_path, products_bundle, products_cat_path
                                        FROM ".TABLE_PRODUCTS."
                                        WHERE products_id = '". tep_db_input($products_id) ."'";
        $products_cat_id_path_result = tep_db_query($products_cat_id_path_select);

        if (tep_db_num_rows($products_cat_id_path_result) > 0) {
            $count_index = 'cs_' . $idx;
            $product_name = tep_get_products_name($products_id, $languages_id);
            
            while($products_cat_id_path_row = tep_db_fetch_array($products_cat_id_path_result)) {
                $cat_path_id = $products_cat_id_path_row['products_cat_id_path'];
                $products_bundle = $products_cat_id_path_row['products_bundle'];
                $products_cat_path = $products_cat_id_path_row['products_cat_path'];
            }
?>
                                <td width="32%" class="whiteBgBox" style="border:solid 1px #cfcfcf">
                                    <table border="0" cellpadding="0" cellspacing="10" width="100%">
                                        <tr>
                                            <td width="32%">
                                                <a href="<?=tep_href_link(FILENAME_DEFAULT, 'cPath='.substr($cat_path_id, 1, -1))?>"><div style="margin-left: auto; margin-right: auto;" class="<?=$class_display?>"></div></a>
                                            </td>
                                            <td>	
                                                <table border="0" cellpadding="0" cellspacing="0" width="100%" height="130px">
                                                    <tr>
                                                        <td width="50%" align="right" style="padding:6px;"><!-- display link -->
                                                            <a href="<?=tep_href_link(FILENAME_DEFAULT, 'cPath='.substr($cat_path_id, 1, -1))?>"><?=strip_tags($product_name)?></a><br>
                                                            <div style="font-size:11px;"><a href="<?=tep_href_link(FILENAME_DEFAULT, 'cPath='.substr($cat_path_id, 1, -1))?>"><?=$products_cat_path?></a></div>
                                                        </td>
                                                    </tr>
                                                    <tr id="<?='data_'.$count_index?>"
                                                        data-pid="<?=$products_id?>" 
                                                        data-products-bundle="<?=$products_bundle?>" 
                                                        data-dm=""
                                                        data-cpt-id="<?=$products_type?>"
                                                        data-name="<?=$product_name?>">
                                                        <td width="50%" align="left" valign="top" style="padding:0px;" height="50px"><!-- display button -->
<?
            if ($products_type == '2'){//%%cdkey%%
                echo tep_image_button2('green', 'javascript:void(0);', IMAGE_BUTTON_BUY_CODE, 120, ' id="'.$count_index.'" onclick="pfv(this.id)"');
            } else {
                if ($products_type == '0'){//%%game currency%%
                    echo tep_image_button2('green', tep_href_link(FILENAME_DEFAULT, 'cPath='.substr($cat_path_id, 1, -1)), IMAGE_BUTTON_BUY_NOW, 120);
                } else if ($products_type == '1'){//%%power leveling%%
                    echo tep_image_button2('green', tep_href_link(FILENAME_DEFAULT, 'cPath='.substr($cat_path_id, 1, -1)), IMAGE_BUTTON_BUY_NOW, 120);
                }
            }
?>
                                                        </td>
                                                    </tr>
                                                </table>
                                            </td>
                                        </tr>
                                    </table>
                                </td>
<?
            if ($track_count < (count($top_3_diff_pro_type_array)- 1)) {## NEED USE THIS TO COMMIT
                $track_count++;
                echo '<td width="2%">&nbsp;</td>';
            }
        }
    }
?>
                                <td>&nbsp;</td><!-- to absorb all the empty space -->
<!--%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%% ## above DISPLAY TOP 3 PRODUCTS ## %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%-->	
							</tr>
						</table>
					</td>
				</tr>
			</table>
			</div>
		</td>
	</tr>
</table>
<? 
    $top3_diff_product_content_string = ob_get_contents();
    ob_end_clean(); 

    echo $page_obj->get_html_simple_rc_box('',$top3_diff_product_content_string,13); 
}
//----------------------------------------------------------------------------------------------------------------------------- below GET TOP 3 DIFFERENT PRODUCTS

//----------------------------------------------------------------------------------------------------------------------------- START banner
if (tep_not_null($banner_img_url)) {
    ob_start();
?>
<table class="bottom_page_banner " border="0" cellpadding="0" cellspacing="0" width="98%">
	<tr>
		<td>
			<table border="0" cellpadding="20" cellspacing="0" width="100%">
				<tr>
					<td>
					<? if (tep_not_null($banner_img_url)) { ?>
						<div style="text-align:center; line-height:100px;">
						<a href=<?=$banner_url?>><span style="font-size:30px;"><?php echo tep_image($banner_img_url, '', 752, 120); ?></span></a>
						</div>
					<?	} ?>
					</td>
				</tr>
			</table>
			<tr>
				<td><br><br></td>
			</tr>
		</td>
	</tr>
</table>
<?
    $bottom_banner_content_string = ob_get_contents();
    ob_end_clean();
    
	echo $page_obj->get_html_simple_rc_box('',$bottom_banner_content_string,13); 
}
//----------------------------------------------------------------------------------------------------------------------------- END banner

if ($phone_verification_obj->get_service_provider_status()) {
	if ($module->confirm_complete_days > 0) {
?>
		<script language="javascript">
			if (DOMCall('phone_field_div') != null)	document.getElementById('phone_field_div').className = 'show';
			if (DOMCall('js_telephone_update_button_div') != null)	document.getElementById('js_telephone_update_button_div').className = 'show';
		</script>
<?
	}
	unset($phone_verification_obj);
} 

if (tep_not_null(MSN_ACCOUNT_ID)) {
?>
	<SCRIPT>
	 microsoft_adcenterconversion_domainid = <?=MSN_ACCOUNT_ID?>;
	 microsoft_adcenterconversion_cp = 5050;
	</SCRIPT>
	<SCRIPT SRC="http://0.r.msn.com/scripts/microsoft_adcenterconversion.js"></SCRIPT>
	<NOSCRIPT><IMG width=1 height=1 SRC="http://550517.r.msn.com/?type=1&cp=1"/></NOSCRIPT>
<?
}
?>