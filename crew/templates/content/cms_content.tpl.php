<?
if(!function_exists('eval_buffer')) {
	function eval_buffer($string) {
		ob_start();
		eval("$string[2];");
		$return = ob_get_contents();
		ob_end_clean();
		return $return;
	}
}

if(!function_exists('eval_print_buffer')) {
	function eval_print_buffer($string) {
		ob_start();
		eval("print $string[2];");
		$return = ob_get_contents();
		ob_end_clean();
		return $return;
	}
}

if(!function_exists('eval_html')) {
	function eval_html($string) {
		$string = preg_replace_callback("/(<\?=)(.*?)\?>/si", "eval_print_buffer",$string);
		return preg_replace_callback("/(<\?php|<\?)(.*?)\?>/si", "eval_buffer",$string);
	}
}
?>
			<h1><?=HEADING_TITLE?></h1>
			<div class="solidThickLine"></div>
<table border="0" width="100%" cellspacing="0" cellpadding="0">
<?
if ($messageStack->size('index') > 0) {
?>
	<tr>
    	<td><?=$messageStack->output('index')?></td>
	</tr>
	<tr>
    	<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
<?
}
?>
	<tr>
		<td>
			<table border="0" width="100%" cellspacing="0" cellpadding="0">
				<tr>
					<td class="main"><?=tep_draw_separator('pixel_trans.gif', '100%', 3)?></td>
				</tr>
				<tr>
					<td><?=$cms_content_array['menu_content']?></td>
				</tr>
        	</table>
		</td>
	</tr>
</table>
		<div class="break_line"></div>