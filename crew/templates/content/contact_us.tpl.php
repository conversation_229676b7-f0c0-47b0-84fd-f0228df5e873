<?=tep_draw_form('contact_us', tep_href_link(FILENAME_CONTACT_US, 'action=send'))?>
<table border="0" width="100%" cellspacing="0" cellpadding="0">
	<tr>
		<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
	<tr>
		<td class="pageHeadingTitle" valign="middle" align="center"><?=HEADING_TITLE?></td>
	</tr>
	<tr>
		<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
	<tr>
		<td>
			<div class="row_separator"></div>
		</td>
	</tr>
	<tr>
		<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
<?
  if ($messageStack->size('contact') > 0) {
?>
	<tr>
		<td><?php echo $messageStack->output('contact'); ?></td>
	</tr>
	<tr>
		<td><?php echo tep_draw_separator('pixel_trans.gif', '100%', '10'); ?></td>
	</tr>
<?
  }

  if (isset($HTTP_GET_VARS['action']) && ($HTTP_GET_VARS['action'] == 'success')) {
?>
      <tr>
        <td class="main" align="center"><?php echo tep_image(DIR_WS_IMAGES . 'table_background_man_on_board.gif', HEADING_TITLE, '0', '0', 'align="left"') . TEXT_SUCCESS; ?></td>
      </tr>
      <tr>
        <td><?php echo tep_draw_separator('pixel_trans.gif', '100%', '10'); ?></td>
      </tr>
      <tr>
        <td><table border="0" width="100%" cellspacing="1" cellpadding="2" class="buttonBox">
          <tr class="buttonBoxContents">
            <td><table border="0" width="100%" cellspacing="0" cellpadding="2">
              <tr>
                <td width="10"><?php echo tep_draw_separator('pixel_trans.gif', '10', '1'); ?></td>
                <td align="right">
					<?=tep_div_button(2, IMAGE_BUTTON_CONTINUE,tep_href_link(FILENAME_DEFAULT), 'style="float:right"', 'gray_button') ?>
				</td>
                <td width="10"><?php echo tep_draw_separator('pixel_trans.gif', '10', '1'); ?></td>
              </tr>
            </table></td>
          </tr>
        </table></td>
      </tr>
<?php
  } else {
?>
      <tr>
        <td><table border="0" width="100%" cellspacing="1" cellpadding="2" class="infoBox">
          <tr class="infoBoxContents">
            <td><table border="0" width="100%" cellspacing="0" cellpadding="2">
              <tr>
                <td class="main"><?php echo ENTRY_NAME; ?></td>
              </tr>
              <tr>
                <td class="main"><?php echo tep_draw_input_field('name'); ?></td>
              </tr>
              <tr>
                <td class="main"><?php echo ENTRY_EMAIL; ?></td>
              </tr>
              <tr>
                <td class="main"><?php echo tep_draw_input_field('email'); ?></td>
              </tr>
              <tr>
                <td class="main"><?php echo ENTRY_ENQUIRY; ?></td>
              </tr>
              <tr>
                <td><?php echo tep_draw_textarea_field('enquiry', 'soft', 50, 15); ?></td>
              </tr>
            </table></td>
          </tr>
        </table></td>
      </tr>
      <tr>
        <td><?php echo tep_draw_separator('pixel_trans.gif', '100%', '10'); ?></td>
      </tr>
      <tr>
        <td><table border="0" width="100%" cellspacing="1" cellpadding="2" class="buttonBox">
          <tr class="buttonBoxContents">
            <td><table border="0" width="100%" cellspacing="0" cellpadding="2">
              <tr>
                <td width="10"><?php echo tep_draw_separator('pixel_trans.gif', '10', '1'); ?></td>
                <td align="right">
                	<?=tep_div_button(1, IMAGE_BUTTON_CONTINUE,'contact_us', 'style="float:right"', 'gray_button') ?>
				</td>
                <td width="10"><?php echo tep_draw_separator('pixel_trans.gif', '10', '1'); ?></td>
              </tr>
            </table></td>
          </tr>
        </table></td>
      </tr>
<?php
  }
?>
    </table></form>

