<?
if(!function_exists('eval_buffer')) {
	function eval_buffer($string) {
		ob_start();
		eval("$string[2];");
		$return = ob_get_contents();
		ob_end_clean();
		return $return;
	}
}

if(!function_exists('eval_print_buffer')) {
	function eval_print_buffer($string) {
		ob_start();
		eval("print $string[2];");
		$return = ob_get_contents();
		ob_end_clean();
		return $return;
	}
}

if(!function_exists('eval_html')) {
	function eval_html($string) {
		$string = preg_replace_callback("/(<\?=)(.*?)\?>/si", "eval_print_buffer",$string);
		return preg_replace_callback("/(<\?php|<\?)(.*?)\?>/si", "eval_buffer",$string);
	}
}

if (!$hide_infolinks_title) {
	echo '<h1>'.NAVBAR_TITLE.'</h1>';
	echo '<div class="solidThickLine"></div>';
}
?>
<table border="0" width="100%" cellspacing="0" cellpadding="0">
<?
if (tep_not_null(PAGES_LINK)) {
?>
	<tr>
		<td class="infoLinkContent" align="center" valign="top"><?=eval_html(PAGES_LINK.'<br><br>')?></td>
	</tr>
<?
}
?>
	<tr>
		<td class="infoLinkContent" align="left" valign="top"><?=eval_html(TEXT_INFORMATION)?></td>
	</tr>
<?
if (tep_not_null(PAGES_LINK)) {
?>
	<tr>
		<td class="infoLinkContent" align="center" valign="top"><?=eval_html(PAGES_LINK.'<br><br>')?></td>
	</tr>
<?
}

if ($rowMain['infolinks_back_button'] == 1) {
?>
	<tr>
		<td>
			<table border="0" width="100%" cellspacing="1" cellpadding="2" class="buttonBox">
          		<tr class="buttonBoxContents">
            		<td>
            			<table border="0" width="100%" cellspacing="0" cellpadding="2">
              				<tr>
                				<td align="left">
									<?=tep_div_button(2, IMAGE_BUTTON_BACK,'javascript:history.back(1)', '', 'gray_button') ?>
								</td>
                				<td width="10"><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
              				</tr>
            			</table>
            		</td>
          		</tr>
        	</table>
        </td>
	</tr>
<?
}
?>
</table>
<div class="break_line"></div>