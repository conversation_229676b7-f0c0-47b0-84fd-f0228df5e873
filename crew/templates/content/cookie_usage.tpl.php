  <table border="0" width="100%" cellspacing="0" cellpadding="0">
    <tr>
		<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
	<tr>
		<td class="pageHeadingTitle" valign="middle" align="center"><?=HEADING_TITLE?></td>
	</tr>
	<tr>
		<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
	<tr>
		<td>
			<div class="row_separator"></div>
		</td>
	</tr>
	<tr>
		<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
      <tr>
        <td class="main"><table border="0" width="40%" cellspacing="0" cellpadding="0" align="right">
          <tr>
            <td><?php new infoBoxHeading(array(array('text' => BOX_INFORMATION_HEADING))); ?></td>
          </tr>
          <tr>
            <td><?php new infoBox(array(array('text' => BOX_INFORMATION))); ?></td>
          </tr>
        </table><?php echo TEXT_INFORMATION; ?></td>
      </tr>
      <tr>
        <td><?php echo tep_draw_separator('pixel_trans.gif', '100%', '10'); ?></td>
      </tr>
      <tr>
        <td><table border="0" width="100%" cellspacing="1" cellpadding="2" class="buttonBox">
          <tr class="buttonBoxContents">
            <td><table border="0" width="100%" cellspacing="0" cellpadding="2">
              <tr>
                <td width="10"><?php echo tep_draw_separator('pixel_trans.gif', '10', '1'); ?></td>
                <td align="right">
                	<?=tep_div_button(2, IMAGE_BUTTON_CONTINUE,tep_href_link(FILENAME_DEFAULT), 'style="float:right"', 'gray_button') ?>
				</td>
                <td width="10"><?php echo tep_draw_separator('pixel_trans.gif', '10', '1'); ?></td>
              </tr>
            </table></td>
          </tr>
        </table></td>
      </tr>
    </table>

