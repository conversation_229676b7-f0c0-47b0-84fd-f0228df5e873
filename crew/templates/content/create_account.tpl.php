<script language="javascript" src="<?=DIR_WS_JAVASCRIPT?>xmlhttp.js"></script>
<?

if ($action == 'create_account' && $error == false) { ?>
<table class="subCategoryBox" border="0" cellpadding="0" cellspacing="0" width="98%">
	<tr>
    	<td><?=tep_draw_separator('pixel_trans.gif', '100%', '15')?></td>
	</tr>
	<tr>
		<td align="center">
			<table border="0" width="96%" cellspacing="0" cellpadding="0">
				<tr>
					<td align="right" width="9%"><?=tep_image(DIR_WS_IMAGES . 'successful.gif', '', 54, 56)?>&nbsp;</td>
					<td style="font-family: Arial,Tahoma,Verdana,Helvetica,sans-serif;font-size: 20px;"><?=sprintf(TEXT_SIGNUP_SUCCESS, tep_output_string_protected($customer_first_name), ((isset($_SESSION['trace'])) ? TEXT_CHECKOUT_REDIRECT_MSG : ''))?></td>
				</tr>
<?		if (isset($_SESSION['trace'])) { ?>
				<tr>
			    	<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '100%', '15')?></td>
				</tr>
				<tr>
					<td></td>
					<td class="main">
						<a href="<?=$redirect_path?>"><?=LINK_REDIRECT_MSG?></a>
					</td>
				</tr>
<?
		} else {
			unset($_SESSION['trace']);
?>
				<tr>
			    	<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '100%', '15')?></td>
				</tr>
				<tr><td colspan="2"><div class="row_separator"><!-- --></div></td></tr>
				<tr>
			    	<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '100%', '15')?></td>
				</tr>
				<tr>
					<td></td>
					<td style="font-family: Arial,Tahoma,Verdana,Helvetica,sans-serif; font-weight: bold; font-size: 16px;"><?=TEXT_WHAT_TODO?></td>
				</tr>
				<tr>
					<td></td>
					<td class="main"><a href="<?=$redirect_path?>"><?=LINK_BACK_TO_WHERE_I_COME?></a></td>
				</tr>
				<tr>
					<td></td>
					<td class="main"><a href="<?=tep_href_link(FILENAME_DEFAULT, '', 'SSL')?>"><?=LINK_GOTO_HOMEPAGE?></a></td>
				</tr>
				<tr>
					<td></td>
					<td class="main"><a href="<?=tep_href_link(FILENAME_ACCOUNT, '', 'SSL')?>"><?=LINK_GOTO_MY_ACCOUNT?></a></td>
				</tr>
				<tr>
					<td></td>
					<td class="main"><a href="<?=tep_href_link(FILENAME_SEARCH_ALL_GAMES, '', 'SSL')?>"><?=BREADCRUMB_BUY?></a></td>
				</tr>
				<tr>
					<td></td>
					<td class="main"><a href="<?=tep_href_link(FILENAME_BUYBACK, '', 'SSL')?>"><?=BREADCRUMB_SELL?></a></td>
				</tr>
				<tr>
			    	<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '100%', '15')?></td>
				</tr>
				<tr>
					<td></td>
					<td class="main"><?=TEXT_SIGNUP_SUCCESS_END?></td>
				</tr>
<?		} ?>
				<tr>
			    	<td><?=tep_draw_separator('pixel_trans.gif', '100%', '15')?></td>
				</tr>
			</table>
		</td>
	</tr>
	<tr>
    	<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
<?		if (tep_not_null(ADBRITE_ACCOUNT_ID)) { ?>
	<tr>
		<td align="right" >
			<!-- begin AdBrite, Sign-ups tracking --><img border="0" hspace="0" vspace="0" width="1" height="1" src="http://stats.adbrite.com/stats/stats.gif?_uid=<?=ADBRITE_ACCOUNT_ID?>&_pid=1" /><!-- end AdBrite, Sign-ups tracking -->
		</td>
	</tr>
<?
		}
		
		if (tep_not_null(AD4GAME_ACCOUNT_ID)) {
?>
	<tr>
		<td align="right" >
			<img width="1" height="1" border="0" vspace="0" hspace="0" src="http://ads.ad4game.com/servlet/ajrotator/track?page=<?=AD4GAME_ACCOUNT_ID?>&zone=ad4game&type=simple">
		</td>
	</tr>
<?		} ?>
</table>
<?	}
	
	ob_start();
	echo tep_draw_form('create_account_form', tep_href_link(FILENAME_CREATE_ACCOUNT, 'action=create_account'.$baseURL, 'SSL'), 'post', 'onSubmit=""') . tep_draw_hidden_field('action', 'create_account');	
	if ($messageStack->size('create_account') > 0) { 
?>	
		<tr>
			<td>
				<div><?=$messageStack->output('create_account')?></div>
				<div><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></div>
			</td>
		</tr>
<?
	}
?>
	
		<div class="vspacing"></div>
			<div class="formRow">
				<div class="lbl"><span class="hdsC5"><?=ENTRY_EMAIL_ADDRESS?><font color="red">*</font></span></div>
				<div class="lf ihd1">
					<?=tep_draw_input_field('account_email_address', '', 'style="width: 300px;" id="email" onfocus="getFocus(this.id, \'messageEmail\', \''.ENTRY_EMAIL_NOTICE.'\')" onblur="ez_validation(this.id, \'messageEmail\', \''. ENTRY_EMAIL_NOTICE.'\', \''.ENTRY_EMAIL_JS_ERROR.'\')"');
					(tep_not_null(ENTRY_EMAIL_ADDRESS_TEXT) ? '<span class="requiredInfo">' . ENTRY_EMAIL_ADDRESS_TEXT . '</span>': '');?>
					<span id="img_messageEmail" style="display: inline-block;"></span>
				</div>
				<div class="clrFx"></div>
			</div>
			<div style="float:left;width:135px;">&nbsp</div>
			&nbsp<span id="messageEmail" style="display: inline-block; width: 300px;"></span>
			<div class="formRow">
				<div class="lbl"><span class="hdsC5"><?=ENTRY_PASSWORD?><font color="red">*</font></span></div>
				<div class="lf ihd1">
					<?=tep_draw_password_field('password', '', 'style="width: 300px" id="passwd" onfocus="getFocus(this.id, \'messagePassword\', \''.sprintf(ENTRY_PASSWORD_NOTICE, ENTRY_PASSWORD_MIN_LENGTH).'\')" onblur="ez_validation(this.id, \'messagePassword\', \''.sprintf(ENTRY_PASSWORD_NOTICE, ENTRY_PASSWORD_MIN_LENGTH).'\', \''.sprintf(ENTRY_PASSWORD_JS_ERROR, ENTRY_PASSWORD_MIN_LENGTH).'\')"');
					(tep_not_null(ENTRY_PASSWORD_TEXT) ? '<span class="requiredInfo">' . ENTRY_PASSWORD_TEXT . '</span>': '');?>
					<span id="img_messagePassword" style="display: inline-block;"></span>
				</div>
				<div class="clrFx"></div>
			</div>
			<div style="float:left;width:135px;">&nbsp</div>
			&nbsp<span id="messagePassword" style="display: inline-block; width: 300px;"></span>
			<div class="formRow">
				<div class="lbl"><span class="hdsC5"><?=ENTRY_PASSWORD_CONFIRMATION?><font color="red">*</font></span></div>
				<div class="lf ihd1">
					<?=tep_draw_password_field('password_confirmation', '', 'style="width: 300px" id="password_confirmation" onfocus="getFocus(this.id, \'messageCPassword\', \''.ENTRY_CONFIRM_PASSWORD_NOTICE.'\')" onblur="ez_validation(this.id, \'messageCPassword\', \''.ENTRY_CONFIRM_PASSWORD_NOTICE.'\', \''.ENTRY_PASSWORD_CONFIRMATION_JS_ERROR.'\')"');
					(tep_not_null(ENTRY_PASSWORD_TEXT) ? '<span class="requiredInfo">' . ENTRY_PASSWORD_CONFIRMATION_TEXT . '</span>': '');?>
					<span id="img_messageCPassword" style="display: inline-block;"></span>
				</div>
				<div class="clrFx"></div>
			</div>
			<div style="float:left;width:135px;">&nbsp</div>
			&nbsp<span id="messageCPassword" style="display: inline-block; width: 300px;"></span>
			<div class="vspacing"></div>
			<div class="formRow">
				<div class="lbl"><span class="hdsC5"><?=ENTRY_FIRST_NAME?><font color="red">*</font></span></div>
				<div class="lf ihd1">
					<?=tep_draw_input_field('firstname', '', 'style="width: 300px" id="firstname" onfocus="getFocus(this.id, \''.messageFName.'\', \''.ENTRY_FIRSTNAME_NOTICE.'\')" onblur="ez_validation(this.id, \''.messageFName.'\', \''.ENTRY_FIRSTNAME_NOTICE.'\', \''.sprintf(ENTRY_FIRSTNAME_JS_ERROR, ENTRY_FIRST_NAME_MIN_LENGTH).'\')"');
					(tep_not_null(ENTRY_FIRST_NAME_TEXT) ? '<span class="requiredInfo">' . ENTRY_FIRST_NAME_TEXT . '</span>': '');?>
					<span id="img_messageFName" style="display: inline-block;"></span>
				</div>
				<div class="clrFx"></div>
			</div>
			<div style="float:left;width:135px;">&nbsp</div>
			&nbsp<span id="messageFName" style="display: inline-block; width: 300px;"></span>
			<div class="formRow">
				<div class="lbl"><span class="hdsC5"><?=ENTRY_LAST_NAME?><font color="red">*</font></span></div>
				<div class="lf ihd1">
					<?=tep_draw_input_field('lastname', '', 'style="width: 300px" id="lastname" onfocus="getFocus(this.id, \''.messageLName.'\', \''.ENTRY_LASTNAME_NOTICE.'\')" onblur="ez_validation(this.id, \''.messageLName.'\', \''.ENTRY_LASTNAME_NOTICE.'\', \''.sprintf(ENTRY_LASTNAME_JS_ERROR, ENTRY_LAST_NAME_MIN_LENGTH).'\')"');
					(tep_not_null(ENTRY_LAST_NAME_TEXT) ? '<span class="requiredInfo">' . ENTRY_LAST_NAME_TEXT . '</span>': '');?>
					<span id="img_messageLName" style="display: inline-block;"></span>
				</div>
				<div class="clrFx"></div>
			</div>
			<div style="float:left;width:135px;">&nbsp</div>
			&nbsp<span id="messageLName" style="display: inline-block; width: 300px;"></span>
			<div class="vspacing"></div>

	<div id="captcha" style="padding-left: 135px;display: none">
                        	<div class="formRow">
<?

if (SIGN_UP_CAPTCHA_FIELD == 1) {
    echo '<script type="text/javascript">
                jQuery(document).ready(function() {
                jQuery("#captcha").show();
                });
            </script>';
	recaptcha :: login_captcha_html();
}
?>
	</div>
            <div class="clrFx"></div>
            <div class="vspacing"></div>
	</div>
                        <?php
	if (count($newsletter_group_array)) {
		foreach ($newsletter_group_array as $news_grp_id => $news_grp_title) { 
			echo '
			<div class="formRow">
				<div class="lbl" style="padding-left: 9px;">&nbsp;</div>
				<div class="lf">&nbsp;</div>
					<span>'. tep_draw_checkbox_field('newsletter[]', $news_grp_id, false, 'id="ng_'.$news_grp_id.'" style="boder: #2B5C88"') . '</span>
					<span class="ezNewsletter" style="color: #303030;"><label for="ng_ ' . $news_grp_id . '">' . $news_grp_title . '</label></span>
				<div class="clrFx"></div>										
			</div>';
		}
	}
?>
			<div class="formRow">
				<div class="lbl" style="padding-left: 9px;">&nbsp;</div>
				<div class="lf">&nbsp;</div>
					<?=tep_draw_checkbox_field('agreed','1',false,' id="agreed" onblur="ez_validation(this.id, \'messageAgreed\', \'\', \''.ENTRY_SIGNUP_TERMSANDCONDITION_JS_ERROR.'\')" ') .'&nbsp; '. TEXT_I_HAVE_READ_AND_AGREE . TEXT_TERMS_AND_POLICY;?>
					<span id="img_messageAgreed" style="display: inline-block;"></span>
				<div class="clrFx"></div>
			</div>
			<div style="float:left;width:135px;">&nbsp</div>
			&nbsp<span id="messageAgreed" style="display: inline-block; width: 300px;"></span>
<?	
	if ($ask_for_ads_source) {
		echo '<div style="padding: 0px 134px">' . tep_image_button2('gray_short','javascript:void(0);',BUTTON_JOIN_US_NOW,'','onClick="if(check_form(\'create_account_form\')){js_survey_form()}"') . '<input type="hidden" id="hidden_survey" name="hidden_survey" value=""></div>';
	} else {
		echo '<div style="padding: 0px 134px">' . tep_image_button2('gray_short','javascript:void(0);',BUTTON_JOIN_US_NOW,'','onClick="if(check_form(\'create_account_form\')){document.create_account_form.submit()}"') . '</div>';
	}
	echo '</form>';	

	$fblogin = $ogm_fb_obj->get_FB_button('connect_with_facebook'); 
	if (tep_not_null($fblogin)) {
?>
			<div class="vspacing"></div>
			<div class="dotborder"><!-- --></div>
	        <div class="vspacing"></div>
	        <div class="formRow">
		        <div class="lbl"><span class="hdsC5"><?=REGISTER_USING_FB?></span></div>
				<div style="padding: 3px"><?=$fblogin?></div>
			</div>
			<div class="clrFx"></div>
<? } ?>
			<div class="vspacing" style="height:20px"></div>
<?
	$signup_content = ob_get_contents();
	ob_end_clean();
	$return_customer_content = '<div style="padding: 10px 20px">
									<div class="hd5">' . RETURNING_CUSTOMER_HEADER . '</div>
									<div style="padding:5px 0px">
										' . tep_image_button2('gray_short',tep_href_link(FILENAME_LOGIN),BUTTON_LOGIN_HERE,100) . '
									</div>
								</div>';
	$why_ogm_content = '<div style="padding:10px 15px">
							<div class="hd5">' . WHY_OGM_HEADER . '</div> 
							<div class="vspacing"></div>
							' . BUY_AND_DOWNLOAD_GAMES . '
							<div class="vspacing"></div>
							' . JOIN_CONVERSATION . '
							<div class="vspacing"></div>
							' . GET_SUPPORT . '
							<div class="vspacing"></div>
							' . CHEAP_GAMES_NOT_ENOUGH . '
						</div>';
?>
<div class="vspacing"></div>
<div style="padding-bottom: 50px;">		
	<div style="float: left; width: 722px;">
		<?=$page_obj->get_html_simple_rc_box(REGISTER_ACCOUNT_HEADER, $signup_content,11);?>
	</div>	
			
	<div style="float: right; width: 230px;">
		<div style="">
			<?=$page_obj->get_html_simple_rc_box('', $return_customer_content,23);?>
			<div class="vspacing"></div>
		</div>
		<!--
		<div style="">
			<?$page_obj->get_html_simple_rc_box('', $why_ogm_content,23);?>
			<div class="vspacing"></div>
		</div>
		!-->
	</div>
</div>
<div class="clrFx"></div>

<script language="javascript" src="<?=DIR_WS_JAVASCRIPT?>easy_signup.js"></script>
<script language="javascript">
<!--
	
<?
	if ($action == 'create_account') {
		if ($error == false) {
			if (isset($_SESSION['trace'])) {
				unset($_SESSION['trace']);
?>
				var gotoPath = '<?=$redirect_path?>';
				setTimeout('gotoPage(gotoPath)', 3000);
<?
			}
		}
	}
?>

	var form = '';
	var error_message = '';
	var submitted = false;
	var error = false;
	var check_isNaN = false;
	
	function check_input(field_name, field_size, message, check_isNaN) {
		if (form.elements[field_name] && (form.elements[field_name].type != "hidden")) {
			var field_value = trim_str(form.elements[field_name].value);
		
			if ((field_value == '' || field_value.length < field_size) || (check_isNaN == true && validateInteger(trim_str(field_value)) == true)) {
				error_message = error_message + "* " + message + "\n";
				error = true;
			}
		}
	}
	
	function check_checkbox(field_name, message) {
		var isChecked = false;
	
		if (form.elements[field_name] && (form.elements[field_name].type != "hidden")) {
			var chkbox = form.elements[field_name];
		
			if (chkbox.checked == true) {
				isChecked = true;
			}
		}
		
		if (isChecked == false) {
			error_message = error_message + "* " + message + "\n";
			error = true;
		}
	}
	
	function check_radio(field_name, message) {
		var isChecked = false;
	
		if (form.elements[field_name] && (form.elements[field_name].type != "hidden")) {
			var radio = form.elements[field_name];
		
			for (var i=0; i<radio.length; i++) {
				if (radio[i].checked == true) {
					isChecked = true;
					break;
				}
			}
		
			if (isChecked == false) {
				error_message = error_message + "* " + message + "\n";
				error = true;
			}
		}
	}
	
	function check_select(field_name, field_default, message) {
		if (form.elements[field_name] && (form.elements[field_name].type != "hidden")) {
			var field_value = form.elements[field_name].value;
	
			if (field_value == field_default) {
				error_message = error_message + "* " + message + "\n";
				error = true;
			}
		}
	}
	
	function check_password(field_name_1, field_name_2, field_size, message_1, message_2) {
		if (form.elements[field_name_1] && (form.elements[field_name_1].type != "hidden")) {
			var password = form.elements[field_name_1].value;
			var password_confirmation = form.elements[field_name_2].value;
	
			if (password == '' || password.length < field_size) {
				error_message = error_message + "* " + message_1 + "\n";
				error = true;
			} else if (password != password_confirmation) {
				error_message = error_message + "* " + message_2 + "\n";
				error = true;
			}
		}
	}
	
	function check_password_new(field_name_1, field_name_2, field_name_3, field_size, message_1, message_2, message_3) {
		if (form.elements[field_name_1] && (form.elements[field_name_1].type != "hidden")) {
			var password_current = form.elements[field_name_1].value;
			var password_new = form.elements[field_name_2].value;
			var password_confirmation = form.elements[field_name_3].value;
			
			if (password_current == '' || password_current.length < field_size) {
				error_message = error_message + "* " + message_1 + "\n";
				error = true;
			} else if (password_new == '' || password_new.length < field_size) {
				error_message = error_message + "* " + message_2 + "\n";
				error = true;
			} else if (password_new != password_confirmation) {
				error_message = error_message + "* " + message_3 + "\n";
				error = true;
			}
		}
	}
	
	function check_form(form_name) {
		if (submitted == true) {
			alert("<?php echo JS_ERROR_SUBMITTED; ?>");
			return false;
		}
		
		error = false;
		form = eval('document.'+form_name);
		
		error_message = "<?php echo JS_ERROR; ?>";
		var emailaddr = form.elements["account_email_address"].value;
//		var contactno = form.elements["contactnumber"].value;
		
		if (validateEmail(emailaddr) != true) {
			error_message = error_message + "* " + "<?php echo ENTRY_EMAIL_JS_ERROR; ?>" + "\n";
			error = true;		
		}
		
		check_input("account_email_address", <?php echo ENTRY_EMAIL_ADDRESS_MIN_LENGTH; ?>, "<?php echo ENTRY_EMAIL_ADDRESS_ERROR; ?>");
		check_input("password", <?php echo ENTRY_PASSWORD_MIN_LENGTH; ?>, "<?php echo ENTRY_PASSWORD_ERROR; ?>");
		check_input("password_confirmation", <?php echo ENTRY_PASSWORD_MIN_LENGTH; ?>, "<?php echo ENTRY_PASSWORD_NEW_ERROR_NOT_MATCHING; ?>");
		check_input("firstname", <?php echo ENTRY_FIRST_NAME_MIN_LENGTH; ?>, "<?php echo ENTRY_FIRST_NAME_ERROR; ?>", true);
		check_input("lastname", <?php echo ENTRY_LAST_NAME_MIN_LENGTH; ?>, "<?php echo ENTRY_LAST_NAME_ERROR; ?>", true);
//		check_select("country", "", "<?php echo ENTRY_COUNTRY_ERROR; ?>");
		
//		if (trim_str(contactno).length > 0 && isNaN(trim_str(contactno))) {
//			error_message = error_message + "* " + "<?php echo ENTRY_CONTACT_NUMBER_JS_ERROR; ?>" + "\n";
//			error = true;
//		} else if (isNaN(trim_str(contactno)) == false) {
//			check_input("contactnumber", <?php echo ENTRY_TELEPHONE_MIN_LENGTH; ?>, "<?php echo sprintf(ENTRY_CONTACT_NUMBER_ERROR, ENTRY_TELEPHONE_MIN_LENGTH); ?>");
//		}
		
		check_checkbox("agreed", "<?php echo ENTRY_SIGNUP_TERMSANDCONDITION_JS_ERROR; ?>");
		
		if (error == true) {
			alert(error_message);
			return false;
		} else {
			submitted = true;
			return true;
		}
	}
	
<?	if ($ask_for_ads_source) { ?>
	function js_survey_form() {
		var display_html = '';
		display_html += '<table width="100%" class="ezInputLabel">';
		display_html += '	<tr>';
		display_html += '		<td><b><?=JS_SURVEY_QUESTION?>?</b></td>';
		display_html += '	</tr>';
		display_html += '	<tr>';
		display_html += '		<td><input type="radio" name="rd_survey" class="rd_survey" value="<?=JS_SURVEY_ANSWER_OTHERS?>" checked> <?=JS_SURVEY_ANSWER_OTHERS?></td>';
		display_html += '	</tr>';
		display_html += '	<tr>';
		display_html += '		<td><input type="radio" name="rd_survey" class="rd_survey" value="<?=JS_SURVEY_ANSWER_GAMEAXIS?>"> <?=JS_SURVEY_ANSWER_GAMEAXIS?></td>';
		display_html += '	</tr>';
		display_html += '	<tr>';
		display_html += '		<td><input type="radio" name="rd_survey" class="rd_survey" value="<?=JS_SURVEY_ANSWER_GAMES?>"> <?=JS_SURVEY_ANSWER_GAMES?></td>';
		display_html += '	</tr>';
		display_html += '	<tr>';
		display_html += '		<td><input type="radio" name="rd_survey" class="rd_survey" value="<?=JS_SURVEY_ANSWER_PCGAMERS?>"> <?=JS_SURVEY_ANSWER_PCGAMERS?></td>';
		display_html += '	</tr>';
		display_html += '	<tr>';
		display_html += '		<td><input type="radio" name="rd_survey" class="rd_survey" value="<?=JS_SURVEY_ANSWER_PCCOM?>"> <?=JS_SURVEY_ANSWER_PCCOM?></td>';
		display_html += '	</tr>';
		display_html += '</table>';
		
		jquery_confirm_box(display_html, 1, 0, '<?=JS_SURVEY_TITLE?>');
		jQuery("#jconfirm_submit").click(function(){
			jQuery("#hidden_survey").val(jQuery('input[@name=rd_survey]:checked').val());
			document.create_account_form.submit()
		});
	}
<?	} ?>

function ez_validation(elObjID, msgObjID, noticemsg, errormsg) {
	var elObj = document.getElementById(elObjID);
	var msgObj = document.getElementById(msgObjID);

	var img_cross = '<img src="images/icons/error.gif" width="12px" height="12px" style="padding-left:1px;"> ';
	var js_error = false;
	
	getBlur(elObjID, msgObjID, noticemsg);
	
	switch (elObjID) {
		case 'email':
			if (trim_str(elObj.value).length > 0 && validateEmail(elObj.value) != true) {js_error = true;}
			break;
			
		case 'passwd':
			if (elObj.value.toLowerCase() == 'null' || (trim_str(elObj.value).length > 0 && trim_str(elObj.value).length < <?php echo ENTRY_PASSWORD_MIN_LENGTH; ?> )) {js_error = true;} 
			break;

		case 'password_confirmation':
			if (elObj.value.toLowerCase() == 'null' || trim_str(elObj.value) != trim_str(document.getElementById('passwd').value) || trim_str(elObj.value).length != trim_str(document.getElementById('passwd').value).length) {js_error = true;} 
			else if (elObj.value.toLowerCase() == 'null' || (trim_str(elObj.value).length > 0 && trim_str(elObj.value).length < <?php echo ENTRY_PASSWORD_MIN_LENGTH; ?>)) {js_error = true; errormsg = "<?php echo sprintf(ENTRY_PASSWORD_JS_ERROR, ENTRY_PASSWORD_MIN_LENGTH); ?>";} 
			break;
		
		case 'firstname':
			if (elObj.value.toLowerCase() == 'null' || 
				(trim_str(elObj.value).length > 0 && (isNaN(trim_str(elObj.value)) == false || trim_str(elObj.value).length < <?php echo ENTRY_FIRST_NAME_MIN_LENGTH; ?>) )
				) {js_error = true;} 
			break;
					
		case 'lastname':
			if (elObj.value.toLowerCase() == 'null' || 
				(trim_str(elObj.value).length > 0 && (isNaN(trim_str(elObj.value)) == false || trim_str(elObj.value).length < <?php echo ENTRY_LAST_NAME_MIN_LENGTH; ?>) ) 
				) {js_error = true;} 
			break;

		case 'country':
			if (elObj.value == null || trim_str(elObj.value) == '') {js_error = true;} 
			break;
			
		case 'contactnumber':
			if (elObj.value.toLowerCase() == 'null' || (trim_str(elObj.value).length > 0 && isNaN(elObj.value))) {js_error = true;} 
			else if (trim_str(elObj.value).length > 0 && trim_str(elObj.value).length < <?php echo ENTRY_TELEPHONE_MIN_LENGTH; ?>) {js_error = true; errormsg = "<?php echo sprintf(ENTRY_CONTACT_NUMBER_ERROR, ENTRY_TELEPHONE_MIN_LENGTH); ?>";}
			break;
			
		case 'day':
			if ((trim_str(elObj.value).length > 0 && (trim_str(document.getElementById('month').value).length == 0 || trim_str(document.getElementById('year').value).length == 0 )) ||
				((trim_str(elObj.value).length == 0 || trim_str(document.getElementById('year').value).length == 0 ) && trim_str(document.getElementById('month').value).length > 0 ) ||
				((trim_str(elObj.value).length == 0 || trim_str(document.getElementById('month').value).length == 0) && trim_str(document.getElementById('year').value).length > 0 ))
			{js_error = true;}
			break;
			
		case 'month':
			if ((trim_str(elObj.value).length > 0 && (trim_str(document.getElementById('day').value).length == 0 || trim_str(document.getElementById('year').value).length == 0 )) ||
				((trim_str(elObj.value).length == 0 || trim_str(document.getElementById('day').value).length == 0) && trim_str(document.getElementById('year').value).length > 0 ) ||
				((trim_str(elObj.value).length == 0 || trim_str(document.getElementById('year').value).length == 0) && trim_str(document.getElementById('day').value).length > 0))
			{js_error = true;}
			break;

		case 'year':
			if ((trim_str(elObj.value).length > 0 && (trim_str(document.getElementById('day').value).length == 0 || trim_str(document.getElementById('month').value).length == 0 )) ||
				((trim_str(elObj.value).length == 0 || trim_str(document.getElementById('day').value).length == 0) && trim_str(document.getElementById('month').value).length > 0 ) ||
				((trim_str(elObj.value).length == 0 || trim_str(document.getElementById('month').value).length == 0) && trim_str(document.getElementById('day').value).length > 0))
			{js_error = true;}
			break;
			
		case 'billingaddress1':
			if ((elObj.value.toLowerCase() == 'null' || (trim_str(elObj.value).length > 0 && trim_str(elObj.value).length < <?php echo ENTRY_STREET_ADDRESS_MIN_LENGTH; ?>)) &&
				(trim_str(document.getElementById('billingaddress2').value).length > 0  || trim_str(document.getElementById('billingpostcode').value).length > 0  || 
				 trim_str(document.getElementById('billingcountry').value).length > 0 || trim_str(document.getElementById('state').value).length > 0 ))  {js_error = true;} 
			break;
			
		case 'billingaddress2':
			if (trim_str(document.getElementById('billingaddress2').value).length != 0 && trim_str(document.getElementById('billingaddress2').value).length < <?php echo ENTRY_STREET_ADDRESS_MIN_LENGTH; ?> ) {js_error = true;} 
			break;

		case 'billingcity':
			if (trim_str(document.getElementById('billingcity').value).length != 0 && (elObj.value.toLowerCase() == 'null' || (trim_str(elObj.value).length > 0 && trim_str(elObj.value).length < <?php echo ENTRY_CITY_MIN_LENGTH; ?>) )) 
			{js_error = true;} 
			break;
						
		case 'billingpostcode':
			if (trim_str(document.getElementById('billingaddress1').value).length != 0 && (elObj.value.toLowerCase() == 'null' || (trim_str(elObj.value).length > 0 && trim_str(elObj.value).length < <?php echo ENTRY_POSTCODE_MIN_LENGTH; ?>) )) 
			{js_error = true;} 
			break;
					
		case 'billingcountry':
			if (trim_str(document.getElementById('billingaddress1').value).length != 0 && (elObj.value.toLowerCase() == 'null' || trim_str(elObj.value) == '')) {js_error = true;} 
			break;

		case 'state':
			if ((trim_str(document.getElementById('billingaddress1').value).length > 0 && trim_str(document.getElementById('billingcountry').value).length > 0) && 
				(elObj.type == 'text' && (trim_str(elObj.value).toLowerCase() == 'null' || 
				(trim_str(elObj.value).length > 0 && (isNaN(elObj.value) == false || trim_str(elObj.value).length < <?php echo ENTRY_STATE_MIN_LENGTH; ?>))))) {js_error = true;}
			break;
		
		case 'agreed':
			if (elObj.checked == '') {js_error = true;}
			elObj.className = "";
			break;
			
		default:
			break;
	}
	
	if (js_error == true) {
		signup_form_error = true;
		msgObj.style.color="red";
		msgObj.innerHTML = img_cross + errormsg;
	} else {
		msgObj.innerHTML = '';
	}
}

<?	if (!isset($_REQUEST['action'])) { ?>
	window.onload = function() {
		jQuery('#email').val('');
		jQuery('#passwd').val('');
	}
<?	} ?>
//-->
</script>