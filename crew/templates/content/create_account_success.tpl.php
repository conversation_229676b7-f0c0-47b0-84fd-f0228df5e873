<?
$unserialize_var = tep_array_unserialize($_REQUEST['param']);

if (tep_not_null($last_page)) {
	$_SESSION['trace'] = $last_page;
}


if (isset($_SESSION['trace'])) {
	if ($_SESSION['trace'] == 'chkout') {
		if ($cart->count_contents() > 0) {
			$path = tep_href_link(FILENAME_CHECKOUT_SHIPPING);
		} else {
			$path = tep_href_link(FILENAME_SHOPPING_CART);
		}
	} else if ($_SESSION['trace'] == 'bb_chkout') {
		$path = tep_href_link(FILENAME_BUYBACK);
	} else if ($_SESSION['trace'] == 'scchkout') {
		$path = tep_href_link(FILENAME_SC_CHECKOUT_PAYMENT);
	}
} else {
	$path = $unserialize_var['path'];
}

$redirect_path = $path;
?>

<table class="subCategoryBox" border="0" cellpadding="0" cellspacing="0" width="98%">
	<tr>
    	<td><?=tep_draw_separator('pixel_trans.gif', '100%', '15')?></td>
	</tr>
	<tr>
		<td align="center">
			<table border="0" width="96%" cellspacing="0" cellpadding="0">
				<tr>
					<td align="right" width="9%"><?=tep_image(DIR_WS_IMAGES . 'successful.gif', '', 54, 56)?>&nbsp;</td>
					<td style="font-family: Arial,Tahoma,Verdana,Helvetica,sans-serif;font-size: 20px;"><?=sprintf(TEXT_SIGNUP_SUCCESS, tep_output_string_protected($customer_first_name), ((isset($_SESSION['trace'])) ? TEXT_CHECKOUT_REDIRECT_MSG : ''))?></td>
				</tr>

				<tr>
			    	<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '100%', '15')?></td>
				</tr>
<?
if (isset($_SESSION['trace'])) {
?>
				<tr>
					<td></td>
					<td class="main">
						<a href="<?=$redirect_path?>"><?=LINK_REDIRECT_MSG?></a>
					</td>
				</tr>			
				<tr>
			    	<td><?=tep_draw_separator('pixel_trans.gif', '100%', '15')?></td>
				</tr>
<?
} else {
?>
				<tr>
			    	<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '100%', '15')?></td>
				</tr>
				<tr><td colspan="2"><div class="row_separator"><!-- --></div></td></tr>
				<tr>
			    	<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '100%', '15')?></td>
				</tr>
				<tr>
					<td></td>
					<td style="font-family: Arial,Tahoma,Verdana,Helvetica,sans-serif; font-weight: bold; font-size: 16px;"><?=TEXT_WHAT_TODO?></td>
				</tr>
				<tr>
					<td></td>
					<td class="main"><a href="<?=$redirect_path?>"><?=LINK_BACK_TO_WHERE_I_COME?></a></td>
				</tr>
				<tr>
					<td></td>
					<td class="main"><a href="<?=tep_href_link(FILENAME_DEFAULT, '', 'SSL')?>"><?=LINK_GOTO_HOMEPAGE?></a></td>
				</tr>
				<tr>
					<td></td>
					<td class="main"><a href="<?=tep_href_link(FILENAME_ACCOUNT, '', 'SSL')?>"><?=LINK_GOTO_MY_ACCOUNT?></a></td>
				</tr>
				<tr>
					<td></td>
					<td class="main"><a href="<?=tep_href_link(FILENAME_SEARCH_ALL_GAMES, '', 'SSL')?>"><?=BREADCRUMB_BUY?></a></td>
				</tr>
				<tr>
					<td></td>
					<td class="main"><a href="<?=tep_href_link(FILENAME_BUYBACK, '', 'SSL')?>"><?=BREADCRUMB_SELL?></a></td>
				</tr>
				<tr>
			    	<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '100%', '15')?></td>
				</tr>
				<tr>
					<td></td>
					<td class="main"><?=TEXT_SIGNUP_SUCCESS_END?></td>
				</tr>
			</table>
		</td>
	</tr>
	<tr>
    	<td><?=tep_draw_separator('pixel_trans.gif', '100%', '10')?></td>
	</tr>
<? 
}
?>
</table>

<?
if (tep_not_null(GOOGLE_ADWORDS_ACCOUNT_ID)) {
?>
	<!-- Google Code for Signup Conversion Page -->
	<script type="text/javascript">
	<!--
	var google_conversion_id = **********;
	
	var google_conversion_language = "en";
	var google_conversion_format = "1";
	var google_conversion_color = "666666";
	var google_conversion_label = "TSsFCM_vqQEQsfCg7AM";
	
	var google_conversion_value = 0;
	//-->
	</script>
	<script type="text/javascript" src="http://www.googleadservices.com/pagead/conversion.js"></script>
	<noscript>
	<div style="display:inline;">
	<img height="1" width="1" style="border-style:none;" alt="" src="http://www.googleadservices.com/pagead/conversion/**********/?label=TSsFCM_vqQEQsfCg7AM&amp;guid=ON&amp;script=0"/>
	</div>
	</noscript>
	
	<!-- Google Code for registration Conversion Page -->
	<script type="text/javascript">
	<!--
	var google_conversion_id = **********;
	var google_conversion_language = "en";
	var google_conversion_format = "3";
	var google_conversion_color = "ffffff";
	var google_conversion_label = "JScuCMWurQEQ3bKc6AM";
	var google_conversion_value = 0;
	//-->
	</script>
	<script type="text/javascript" src="http://www.googleadservices.com/pagead/conversion.js"></script>
	<noscript><div style="display:inline;"><img height="1" width="1" style="border-style:none;" alt="" src="http://www.googleadservices.com/pagead/conversion/**********/?label=JScuCMWurQEQ3bKc6AM&amp;guid=ON&amp;script=0"/></div></noscript>
<?
}
?>

<script language="javascript">
<!--
<?
if (isset($_SESSION['trace'])) {
	unset($_SESSION['trace']);
?>
	var gotoPath = '<?=$redirect_path?>';
	setTimeout(gotoPage(gotoPath), 3000);
<?
}
?>
//-->
</script>